package com.tyt.enums;

/**
 * @Description (0.购买 1.新用户注册 2.老用户回归 3.信用获取 4.履约获取 )
 * <AUTHOR>
 * @Date 2023/2/20 13:40
 * @Version 1.0
 **/
@Deprecated
public enum PermissionGainTypeEnum {
    购买(0),
    新用户注册(1),
    沉默用户登录(2),
    流失用户登录(3),
    信用获取(4),
    履约获取(5),
    发布保障货源(7),
    赠送(100);

    private int id;
    PermissionGainTypeEnum(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

}
