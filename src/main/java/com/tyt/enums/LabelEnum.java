package com.tyt.enums;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public enum LabelEnum {

    /**
     * 构造器格式：标签名称,标签ID（唯一，目前无用）,标签分组名称,标签分组ID,父标签ID
     */
    货方("货方", 1, "货方", 1, "注册身份"),
    车方("车方", 2, "车方", 2, "注册身份"),
    注册身份不限("身份不限", 3, "不限", 3, "注册身份"),
    车体验("免费车体验", 10001, "体验用户", 1010, "车标签"),
    车体验使用完("车体验使用完", 10002, "权益用完或过期", 1040, "车标签"),
    车体验过期("车体验过期", 10003, "权益用完或过期", 1040, "车标签"),
    车试用("免费车试用", 10004, "试用用户", 1020, "车标签"),
    车试用使用完("车试用使用完", 10005, "权益用完或过期", 1040, "车标签"),
    车试用过期("车试用过期", 10006, "权益用完或过期", 1040, "车标签"),
    车会员("车会员", 10007, "会员", 1030, "车标签"),
    车会员过期("车会员过期", 10008, "权益用完或过期", 1040, "车标签"),
    车方无标签("车方无标签", 10009, "体验用户", 1010, "车标签"),
    车方不限("车方不限", 10010, "不限", 1000, "车标签"),
    货体验("免费货体验", 20001, "体验用户", 2010, "货标签"),
    货体验使用完("货体验使用完", 20002, "权益用完或过期", 2040, "货标签"),
    货体验过期("货体验过期", 20003, "权益用完或过期", 2040, "货标签"),
    货试用("免费货试用", 20004, "体验用户", 2010, "货标签"),  // 货试用系列，按照体验处理
    货试用使用完("货试用使用完", 20005, "权益用完或过期", 2040, "货标签"),
    货试用过期("货试用过期", 20006, "权益用完或过期", 2040, "货标签"),
    货会员("货会员", 20007, "会员", 2030, "货标签"),
    货会员过期("货会员过期", 20008, "权益用完或过期", 2040, "货标签"),
    货方无标签("货方无标签", 20009, "体验用户", 2010, "货标签"),
    货方不限("货方不限", 20010, "不限", 2000, "货标签"),
    货次卡有效("货次卡有效", 30001, "有效", 3020, "货次卡标签"),
    货次卡使用完("货次卡使用完", 30002, "权益用完或过期", 3030, "货次卡标签"),
    货次卡过期("货次卡过期", 30003, "权益用完或过期", 3030, "货次卡标签"),
    货次卡无标签("货次卡无标签", 30004, "未购买", 3010, "货次卡标签"),
    货次卡不限("货次卡不限", 30004, "不限", 3000, "货次卡标签"),
    已登录("已登录", 40001, "已登录", 4010, "登录状态"),
    未登录("未登录", 40002, "未登录", 4020, "登录状态");

    /**
     * 标签名称
     */
    private String label;
    /**
     * 标签ID
     */
    private Integer labelId;
    /**
     * 标签分组名称
     */
    private String labelGroup;
    /**
     * 标签分组ID
     */
    private Integer labelGroupId;
    /**
     * 标签父分组名称
     */
    private String labelParent;


    LabelEnum(String label, Integer labelId, String labelGroup, Integer labelGroupId, String labelParent) {
        this.label = label;
        this.labelId = labelId;
        this.labelGroup = labelGroup;
        this.labelGroupId = labelGroupId;
        this.labelParent = labelParent;
    }

    /**
     * 按用户标签转换成，分组的ID
     *
     * @param label
     * @return 没有返回-1
     */
    public static Integer getLabelGroupIdByLabel(String label) {
        for (LabelEnum tEnum : values()) {
            if (tEnum.getLabel().equals(label)) {
                return tEnum.getLabelGroupId();
            }
        }
        return -1;
    }

    /**
     * 通过labelGroupId转换为分组的名称
     *
     * @param labelGroupId
     * @return
     */
    public static String getLabelGroupByGroupId(Integer labelGroupId) {
        for (LabelEnum tEnum : values()) {
            if (tEnum.getLabelGroupId().equals(labelGroupId)) {
                return tEnum.getLabelGroup();
            }
        }
        return null;
    }

    /**
     * 以逗号分隔的ID序列，转换成为汉字表示
     *
     * @param labelGroupIds 逗号分隔的数字字符串，例如："2,1,1010,3020,2010,1020,1030,2020"
     * @return style
     * 注册身份:车方/货方
     * 车标签:体验用户/试用用户
     * 货标签:体验用户
     * 货次卡标签:未购买
     */
    public static String getLabelGroupStyle(String labelGroupIds) {
        int[] labelGroupId = stringIdsConvertInts(labelGroupIds);
        return getLabelGroupStyle(labelGroupId);
    }

    /**
     * 以逗号分隔的ID序列，转换成为汉字表示
     *
     * @param labelGroupId 可变参数Integer参数
     * @return style
     * 注册身份:车方/货方
     * 车标签:体验用户/试用用户
     * 货标签:体验用户
     * 货次卡标签:未购买
     */
    public static String getLabelGroupStyle(int... labelGroupId) {
        if (labelGroupId == null) {
            return null;
        }
        Arrays.sort(labelGroupId); // 按定义ID排序，保证页面显示顺序
        String result = null;
        String labelGroup = "";
        String labelParent = "";
        HashSet<String> set = new HashSet<>();
        StringBuilder builder = new StringBuilder();
        for (Integer groupId : labelGroupId) {
            for (LabelEnum tEnum : values()) {
                if (tEnum.getLabelGroupId().equals(groupId)) {
                    labelGroup = tEnum.getLabelGroup();
                    labelParent = tEnum.getLabelParent();
                    boolean b = set.add(labelParent);
                    if (b) {
                        // 格式：+=  "\n" + labelParent + ":" + labelGroup+"/"
                        builder.append("<br/>").append(labelParent).append(":").append(labelGroup).append("/");
                    } else {
                        // 格式：+= labelGroup + "/"
                        builder.append(labelGroup).append("/");
                    }
                    break;
                }
            }
        }
        if (builder.length() > 0) {
            builder.deleteCharAt(builder.length() - 1);
        }
        result = StringUtils.replace(builder.toString(), "/<br/>", "<br/>").replaceFirst("<br/>", "").trim();
        return result;
    }

    /**
     * 逗号分隔的数字字符串 转化为 Integer数组
     *
     * @param value 逗号分隔的数字字符串，例如："2,1,1010,3020,2010,1020,1030,2020"
     * @return
     */
    public static int[] stringIdsConvertInts(String value) {
        int[] intArr = null;
        if (StringUtils.isBlank(value)) {
            intArr = new int[0];
        } else {
            String[] valueArr = value.split(",");
            intArr = new int[valueArr.length];
            for (int i = 0; i < valueArr.length; i++) {
                if(!NumberUtils.isDigits(valueArr[i])) {
                    return new int[0];
                }
                intArr[i] = Integer.parseInt(valueArr[i]);
            }
        }
        return intArr;
    }

    public static String getAllLabelGroupId(){
        Set<Integer> set = new HashSet<>();
        for (LabelEnum tEnum : values()) {
            set.add(tEnum.getLabelGroupId());
        }
        String result = StringUtils.removePattern(set.toString(),"\\[|\\]| ");
        return result;
    }

    public static String getUnlimitedGroupId(){
        Set<Integer> set = new HashSet<>();
        for (LabelEnum tEnum : values()) {
            if(tEnum.getLabelGroup().equals("不限") || tEnum.getLabelGroup().equals("已登录") || tEnum.getLabelGroup().equals("未登录")) {
                set.add(tEnum.getLabelGroupId());
            }
        }
        String result = StringUtils.removePattern(set.toString(),"\\[|\\]| ");
        return result;
    }


    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getLabelId() {
        return labelId;
    }

    public void setLabelId(Integer labelId) {
        this.labelId = labelId;
    }

    public String getLabelGroup() {
        return labelGroup;
    }

    public void setLabelGroup(String labelGroup) {
        this.labelGroup = labelGroup;
    }

    public Integer getLabelGroupId() {
        return labelGroupId;
    }

    public void setLabelGroupId(Integer labelGroupId) {
        this.labelGroupId = labelGroupId;
    }

    public String getLabelParent() {
        return labelParent;
    }

    public void setLabelParent(String labelParent) {
        this.labelParent = labelParent;
    }

    public static void main(String[] args) {
        String result = getLabelGroupStyle("2,1,1010,1020,2010,1020,1030,2020,3010,4010");

//        String result = getAllLabelGroupId();
//        String result = getUnlimitedGroupId();
        System.out.println(result);
    }
}
