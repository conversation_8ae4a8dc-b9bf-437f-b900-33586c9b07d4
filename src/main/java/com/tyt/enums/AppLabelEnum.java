package com.tyt.enums;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public enum AppLabelEnum {

    /**
     * 构造器格式：标签名称,标签ID（唯一，目前无用）,标签分组名称,标签分组ID,父标签ID
     */
    货方APP("货方APP", 1, "货方", 1, "登录客户端"),
    车方APP("车方APP", 2, "车方", 2, "登录客户端"),
    未登录("未登录", 3, "未登录", 3, "登录状态");

    /**
     * 标签名称
     */
    private String label;
    /**
     * 标签ID
     */
    private Integer labelId;
    /**
     * 标签分组名称
     */
    private String labelGroup;
    /**
     * 标签分组ID
     */
    private Integer labelGroupId;
    /**
     * 标签父分组名称
     */
    private String labelParent;


    AppLabelEnum(String label, Integer labelId, String labelGroup, Integer labelGroupId, String labelParent) {
        this.label = label;
        this.labelId = labelId;
        this.labelGroup = labelGroup;
        this.labelGroupId = labelGroupId;
        this.labelParent = labelParent;
    }

    /**
     * 按用户标签转换成，分组的ID
     *
     * @param label
     * @return 没有返回-1
     */
    public static Integer getLabelGroupIdByLabel(String label) {
        for (AppLabelEnum tEnum : values()) {
            if (tEnum.getLabel().equals(label)) {
                return tEnum.getLabelGroupId();
            }
        }
        return -1;
    }

    /**
     * 通过labelGroupId转换为分组的名称
     *
     * @param labelGroupId
     * @return
     */
    public static String getLabelGroupByGroupId(Integer labelGroupId) {
        for (AppLabelEnum tEnum : values()) {
            if (tEnum.getLabelGroupId().equals(labelGroupId)) {
                return tEnum.getLabelGroup();
            }
        }
        return null;
    }

    /**
     * 以逗号分隔的ID序列，转换成为汉字表示
     *
     * @param labelGroupIds 逗号分隔的数字字符串，例如："2,1,1010,3020,2010,1020,1030,2020"
     * @return style
     * 注册身份:车方/货方
     * 车标签:体验用户/试用用户
     * 货标签:体验用户
     * 货次卡标签:未购买
     */
    public static String getLabelGroupStyle(String labelGroupIds) {
        int[] labelGroupId = stringIdsConvertInts(labelGroupIds);
        return getLabelGroupStyle(labelGroupId);
    }

    /**
     * 以逗号分隔的ID序列，转换成为汉字表示
     *
     * @param labelGroupId 可变参数Integer参数
     * @return style
     * 注册身份:车方/货方
     * 车标签:体验用户/试用用户
     * 货标签:体验用户
     * 货次卡标签:未购买
     */
    public static String getLabelGroupStyle(int... labelGroupId) {
        if (labelGroupId == null) {
            return null;
        }
        Arrays.sort(labelGroupId); // 按定义ID排序，保证页面显示顺序
        String result = null;
        String labelGroup = "";
        String labelParent = "";
        HashSet<String> set = new HashSet<>();
        StringBuilder builder = new StringBuilder();
        for (Integer groupId : labelGroupId) {
            for (AppLabelEnum tEnum : values()) {
                if (tEnum.getLabelGroupId().equals(groupId)) {
                    labelGroup = tEnum.getLabelGroup();
                    labelParent = tEnum.getLabelParent();
                    boolean b = set.add(labelParent);
                    if (b) {
                        // 格式：+=  "\n" + labelParent + ":" + labelGroup+"/"
                        builder.append("<br/>").append(labelParent).append(":").append(labelGroup).append("/");
                    } else {
                        // 格式：+= labelGroup + "/"
                        builder.append(labelGroup).append("/");
                    }
                    break;
                }
            }
        }
        if (builder.length() > 0) {
            builder.deleteCharAt(builder.length() - 1);
        }
        result = StringUtils.replace(builder.toString(), "/<br/>", "<br/>").replaceFirst("<br/>", "").trim();
        return result;
    }

    /**
     * 逗号分隔的数字字符串 转化为 Integer数组
     *
     * @param value 逗号分隔的数字字符串，例如："2,1,1010,3020,2010,1020,1030,2020"
     * @return
     */
    public static int[] stringIdsConvertInts(String value) {
        int[] intArr = null;
        if (StringUtils.isBlank(value)) {
            intArr = new int[0];
        } else {
            String[] valueArr = value.split(",");
            intArr = new int[valueArr.length];
            for (int i = 0; i < valueArr.length; i++) {
                if(!NumberUtils.isDigits(valueArr[i])) {
                    return new int[0];
                }
                intArr[i] = Integer.parseInt(valueArr[i]);
            }
        }
        return intArr;
    }

    public static String getAllLabelGroupId(){
        Set<Integer> set = new HashSet<>();
        for (AppLabelEnum tEnum : values()) {
            set.add(tEnum.getLabelGroupId());
        }
        String result = StringUtils.removePattern(set.toString(),"\\[|\\]| ");
        return result;
    }

    public static String getUnlimitedGroupId(){
        Set<Integer> set = new HashSet<>();
        for (AppLabelEnum tEnum : values()) {
            if(tEnum.getLabelGroup().equals("不限") || tEnum.getLabelGroup().equals("已登录") || tEnum.getLabelGroup().equals("未登录")) {
                set.add(tEnum.getLabelGroupId());
            }
        }
        String result = StringUtils.removePattern(set.toString(),"\\[|\\]| ");
        return result;
    }


    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getLabelId() {
        return labelId;
    }

    public void setLabelId(Integer labelId) {
        this.labelId = labelId;
    }

    public String getLabelGroup() {
        return labelGroup;
    }

    public void setLabelGroup(String labelGroup) {
        this.labelGroup = labelGroup;
    }

    public Integer getLabelGroupId() {
        return labelGroupId;
    }

    public void setLabelGroupId(Integer labelGroupId) {
        this.labelGroupId = labelGroupId;
    }

    public String getLabelParent() {
        return labelParent;
    }

    public void setLabelParent(String labelParent) {
        this.labelParent = labelParent;
    }

    public static void main(String[] args) {
        String result = getLabelGroupStyle("2,1,1010,1020,2010,1020,1030,2020,3010,4010");

//        String result = getAllLabelGroupId();
//        String result = getUnlimitedGroupId();
        System.out.println(result);
    }
}
