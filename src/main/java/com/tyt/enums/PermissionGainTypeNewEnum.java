package com.tyt.enums;

/**
 * @Description (0.购买 1.新用户注册 2.老用户回归 3.信用获取 4.履约获取 )
 * <AUTHOR>
 * @Date 2023/2/20 13:40
 * @Version 1.0
 **/
public enum PermissionGainTypeNewEnum {
    购买(0),
    新用户注册(1),
    沉默用户登录(2),
    流失用户登录(3),
    信用登录获取(4),
    信用完成履约任务发放(5),
    购买会员赠送(6),
    发布保障货源(7),
    活动获取(8),
    发布有价货源(9),
    评论赠送(11),
    赠送(100);

    private int id;
    PermissionGainTypeNewEnum(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

}
