package com.tyt.shielding.config;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date ：2022:01:25 16:41
 * @description：货车轴数校验
 * @ClassName : com.tyt.shielding.config.AxesNumberValue
 */
public class AxesNumberValidation implements ConstraintValidator<AxesNumber, String> {
    Set<String> set =  new HashSet<>();
    @Override
    public void initialize(AxesNumber constraintAnnotation) {
        String[] value = constraintAnnotation.value();
        if(value.length <= 0){
            set.add("1轴");
            set.add("2轴");
            set.add("3轴");
            set.add("4轴");
            set.add("5轴");
            set.add("6轴以上");
        }else{
            for (String s : value) {
                set.add(s);
            }
        }
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        return set.contains(s);
    }
}
