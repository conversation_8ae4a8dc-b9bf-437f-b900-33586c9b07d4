package com.tyt.shielding.config;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @date ：2022:01:25 16:37
 * @description：货车轴数校验注解
 * @ClassName : com.tyt.shielding.config.AxesNumberValue
 */
@Documented
@Retention(RUNTIME)
@Constraint(validatedBy = {AxesNumberValidation.class})
@Target({FIELD})
public @interface AxesNumber {
    String message() default "货车轴数格式不正确";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

    String[] value() default {};
}
