package com.tyt.shielding.config;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date ：2022:01:25 16:41
 * @description：货车轴数校验
 * @ClassName : com.tyt.shielding.config.AxesNumberValue
 */
public class EmissionStandardsValidation implements ConstraintValidator<EmissionStandards, String> {
    Set<String> set =  new HashSet<>();
    @Override
    public void initialize(EmissionStandards constraintAnnotation) {
        String[] value = constraintAnnotation.value();
        if(value.length <= 0){
            set.add("国一");
            set.add("国二");
            set.add("国三");
            set.add("国四");
            set.add("国五");
            set.add("国六");
        }else{
            for (String s : value) {
                set.add(s);
            }
        }
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        return set.contains(s);
    }
}
