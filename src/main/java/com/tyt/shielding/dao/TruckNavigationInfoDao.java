package com.tyt.shielding.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.Activity;
import com.tyt.model.TruckNavigationInfo;

import java.util.List;

public interface TruckNavigationInfoDao extends BaseDao<TruckNavigationInfo, Long>{

    /**
     * 逻辑删除
     * @param userId
     */
    void updateIsDelete(Long userId,Integer navigateType);

    /**
     * @description 根据车辆导航信息id和用户id删除数据
     * <AUTHOR>
     * @date 2022/6/29 11:07
     * @param id
     * @param userId
     * @return void
     */
    void deleteruckNavigationInfo(Long id, Long userId);

    /**
     * 通过 userId 获取用户的车辆导航信息
     * @param userId
     * @return
     */
    List<TruckNavigationInfo> selectTruckNavigationInfoByUserId(Long userId,Integer navigateType);
}
