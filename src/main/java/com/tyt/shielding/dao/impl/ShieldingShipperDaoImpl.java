package com.tyt.shielding.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.ShieldingShipper;
import com.tyt.shielding.dao.ShieldingShipperDao;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Repository("shieldingShipperDao")
public class ShieldingShipperDaoImpl extends BaseDaoImpl<ShieldingShipper, Long> implements ShieldingShipperDao {

    public ShieldingShipperDaoImpl() {
        super.setEntityClass(ShieldingShipper.class);
    }

    @Override
    public void updateIsDelete(ShieldingShipper shieldingShipper) {
        String sql = "UPDATE tyt_shielding_shipper SET is_delete = 1, update_id = ?, update_time = now() WHERE user_id = ? and is_delete = 0 and shielding_user_id = ?";
        this.executeUpdateSql(sql, new Object[] {shieldingShipper.getUserId(), shieldingShipper.getUserId(), shieldingShipper.getShieldingUserId()});
    }

    @Override
    public List<Long> selectShieldingShipperByUserId(Long userId) {
        String sql = "select shielding_user_id shieldingUserId from  tyt_shielding_shipper where user_id = :userId and is_delete = 0";
        Map<String, Type> typeMap = new HashMap<String, Type>();
        typeMap.put("shieldingUserId", Hibernate.LONG);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        return this.search(sql, typeMap, null, paramMap);

    }

    @Override
    public List<ShieldingShipper> checkRepeatInsert(Long userId, Long shieldingUserId) {
        List<ShieldingShipper> shieldingShippers = this.find("from  ShieldingShipper where userId = ? and isDelete = 0 and shieldingUserId = ?", userId, shieldingUserId);
        return shieldingShippers;
    }
}
