package com.tyt.shielding.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TruckNavigationInfo;
import com.tyt.shielding.dao.TruckNavigationInfoDao;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository("truckNavigationInfoDao")
public class TruckNavigationInfoDaoImpl extends BaseDaoImpl<TruckNavigationInfo, Long> implements TruckNavigationInfoDao {

    public TruckNavigationInfoDaoImpl() {
        super.setEntityClass(TruckNavigationInfo.class);
    }

    @Override
    public void updateIsDelete(Long userId,Integer navigateType) {
        String sql = "UPDATE tyt_truck_navigation_info SET is_delete = 1, update_id = ?, update_time = now() WHERE user_id = ? and navigate_type=?  and is_delete = 0";
        this.executeUpdateSql(sql, new Object[] {userId, userId,navigateType});
    }

    /**
     * @description 根据车辆导航信息id和用户id删除数据
     * <AUTHOR>
     * @date 2022/6/29 11:07
     * @param id
     * @param userId
     * @return void
     */
    @Override
    public void deleteruckNavigationInfo(Long id, Long userId) {
        String sql = "UPDATE tyt_truck_navigation_info SET is_delete = 1, update_id = ?, update_time = now() WHERE id = ? and user_id = ? and is_delete = 0";
        this.executeUpdateSql(sql, new Object[] {userId, id, userId});
    }

    @Override
    public List<TruckNavigationInfo> selectTruckNavigationInfoByUserId(Long userId,Integer navigateType) {
        List<TruckNavigationInfo> truckNavigationInfos = this.find("from TruckNavigationInfo where userId = ? and navigate_type=? and is_delete = 0", userId,navigateType);
        return truckNavigationInfos;
    }
}
