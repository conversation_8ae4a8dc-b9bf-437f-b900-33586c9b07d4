package com.tyt.shielding.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.ShieldingShipper;

import java.util.List;

public interface ShieldingShipperDao extends BaseDao<ShieldingShipper, Long>{

    /**
     * 取消屏蔽
     * @param shieldingShipper
     */
    void updateIsDelete(ShieldingShipper shieldingShipper);

    /**
     * 查询当前用户屏蔽的用户列表
     * @param userId
     * @return
     */
    List<Long> selectShieldingShipperByUserId(Long userId);

    /**
     * 判断是否已经屏蔽过了
     * @param userId
     * @param shieldingUserId
     * @return
     */
    List<ShieldingShipper> checkRepeatInsert(Long userId, Long shieldingUserId);

}
