package com.tyt.shielding.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TruckNavigationInfo;
import com.tyt.shielding.bean.DTO.TruckNavigationInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-01-25 11:16:52
 */
public interface TruckNavigationInfoService extends BaseService<TruckNavigationInfo, Long> {

    /**
     * 车辆导航信息新增或修改
     * @param truckNavigationInfoDTO
     */
    void insertOrUpdate(TruckNavigationInfoDTO truckNavigationInfoDTO);

    /**
     * 查询用户的车辆导航信息
     * @param userId
     * @return
     */
    List<TruckNavigationInfo> selectTruckNavigationInfo(Long userId,Integer navigateType);

    /**
     * @description 删除货车导航信息
     * <AUTHOR>
     * @date 2022/6/28 11:18
     * @param userId
     * @return void
     */
    void deleteTruckNavigationInfo(Long userId,Integer navigateType);

}

