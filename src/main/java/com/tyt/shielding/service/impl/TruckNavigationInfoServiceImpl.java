package com.tyt.shielding.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TruckNavigationInfo;
import com.tyt.shielding.bean.DTO.TruckNavigationInfoDTO;
import com.tyt.shielding.dao.TruckNavigationInfoDao;
import com.tyt.shielding.service.TruckNavigationInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-01-25 11:16:52
 */
@Service("truckNavigationInfoService")
public class TruckNavigationInfoServiceImpl extends BaseServiceImpl<TruckNavigationInfo, Long> implements TruckNavigationInfoService {

    @Resource
    private TruckNavigationInfoDao truckNavigationInfoDao;

    @Override
    @Resource(name = "truckNavigationInfoDao")
    public void setBaseDao(BaseDao<TruckNavigationInfo, Long> dao) {
        super.setBaseDao(dao);
    }

    @Override
    public void insertOrUpdate(TruckNavigationInfoDTO truckNavigationInfoDTO) {
        truckNavigationInfoDao.updateIsDelete(truckNavigationInfoDTO.getUserId(),truckNavigationInfoDTO.getNavigateType());
        TruckNavigationInfo truckNavigationInfo = new TruckNavigationInfo(truckNavigationInfoDTO);
        truckNavigationInfoDao.insert(truckNavigationInfo);
    }

    @Override
    public List<TruckNavigationInfo> selectTruckNavigationInfo(Long userId,Integer navigateType) {
        List<TruckNavigationInfo> truckNavigationInfoList = truckNavigationInfoDao.selectTruckNavigationInfoByUserId(userId,navigateType);
        return truckNavigationInfoList;
    }

    /**
     * @description 删除货车导航信息
     * <AUTHOR>
     * @date 2022/6/28 11:18
     * @param userId
     * @return void
     */
    @Override
    public void deleteTruckNavigationInfo(Long userId,Integer navigateType) {
        truckNavigationInfoDao.updateIsDelete(userId,navigateType);
    }
}