package com.tyt.shielding.service.impl;

import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.TransportUserBean;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.model.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.shielding.dao.ShieldingShipperDao;
import com.tyt.shielding.service.ShieldingShipperService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.StringUtil;
import com.tyt.util.XXTea;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022-01-25 11:16:52
 */
@Slf4j
@Service("shieldingShipperService")
public class ShieldingShipperServiceImpl extends BaseServiceImpl<ShieldingShipper, Long> implements ShieldingShipperService {

    @Resource
    private ShieldingShipperDao shieldingShipperDao;
    @Resource
    private InfoFeeBusinessService infoFeeBusinessService;
    @Resource
    private UserService userService;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;
    @Resource(name = "tytConfigService")
    private TytConfigService configService;
    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;


    private final static String SHIELDING_LIST_KEY_PREFIX = "SHIELDING:USERID:";

    @Override
    @Resource(name = "shieldingShipperDao")
    public void setBaseDao(BaseDao<ShieldingShipper, Long> dao) {
        super.setBaseDao(dao);
    }

    /**
     * 屏蔽
     */
    private static final Integer SHIELDING = 1;
    /**
     * 解除屏蔽
     */
    private static final Integer UNBLOCK = 2;

    @Override
    public ResultMsgBean shieldingShipper(Long userId, Long shieldingUserId, Integer type, Long orderId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        ShieldingShipper shieldingShipper = new ShieldingShipper(userId, shieldingUserId);
        if(SHIELDING.equals(type)){
            // 通过货源 id 获取货源信息
            TransportMain transportMain = transportMainService.getTransportMainForId(orderId);
            if(null == transportMain || null == transportMain.getUserId()){
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("参数异常!");
                return resultMsgBean;
            }
            shieldingUserId = transportMain.getUserId();
            // 判断是否是重复屏蔽
            List<ShieldingShipper> shieldingShipperList = shieldingShipperDao.checkRepeatInsert(userId, shieldingUserId);
            if(null != shieldingShipperList && !shieldingShipperList.isEmpty()){
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("您已屏蔽过该发货人，请勿重复屏蔽！");
                return resultMsgBean;
            }
            // 判断当前用户屏蔽的发货人的数量
            List<Long> list = shieldingShipperDao.selectShieldingShipperByUserId(userId);
            if(list.size() >= 50){
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("屏蔽发货人数量已达上限");
                return resultMsgBean;
            }
            shieldingShipper.setShieldingUserId(shieldingUserId);
            shieldingShipperDao.insert(shieldingShipper);
            Map<String, String> map = new HashMap<>(1);
            map.put(String.valueOf(shieldingUserId), String.valueOf(shieldingUserId));
            RedisUtil.mapPut(SHIELDING_LIST_KEY_PREFIX + userId, map);
        }else if(UNBLOCK.equals(type)){
            shieldingShipperDao.updateIsDelete(shieldingShipper);
            RedisUtil.mapRemove(SHIELDING_LIST_KEY_PREFIX + userId, String.valueOf(shieldingUserId));
        }else{
            throw new RuntimeException("参数异常");
        }
        return resultMsgBean;
    }

    @Override
    public ResultMsgBean selectShieldingShipper(Long userId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        List<Long> list = shieldingShipperDao.selectShieldingShipperByUserId(userId);
        if(null == list || list.isEmpty()){
            return resultMsgBean;
        }
        ArrayList<TransportUserBean> transportUserBeans = new ArrayList<>();
        list.stream().forEach(e ->{
            try {
                TransportUserBean userBeanDetail = infoFeeBusinessService.getUserBeanDetail(userId, e, null);
                User user = userService.getByUserId(e);
                if(null != user || StringUtils.isNotBlank(user.getCellPhone())){
                    userBeanDetail.setCallPhone(user.getCellPhone());
                    userBeanDetail.setRegTime(user.getCtime());
                    userBeanDetail.setIsNeedDecrypt(configService.getIntValue(Constant.IS_NEED_ENCYPT_KEY, 1));
                    userBeanDetail.setNickName(XXTea.Encrypt(StringUtil.formatUserName(StringUtil.hidePhoneInStr(user.getUserName()), String.valueOf(user.getId())), AppConfig.getProperty("tyt.xxtea.key")));
                    //v6210新增信用分/信用分等级字段
                    ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(user.getId());
                    if (null != userCreditInfo) {
                        userBeanDetail.setTotalScore(userCreditInfo.getTotalScore() == null ? new BigDecimal(0) : userCreditInfo.getTotalScore());
                        userBeanDetail.setRankLevel(userCreditInfo.getRankLevel() == null ? 1 : userCreditInfo.getRankLevel());
                    }
                }
                transportUserBeans.add(userBeanDetail);
            } catch (Exception ex) {
                log.error("获取屏蔽发货人信息异常", e);
            }
        });
        resultMsgBean.setData(transportUserBeans);
        return resultMsgBean;
    }

    /**
     * 从缓存中获取当前用户屏蔽的发货人id
     *
     * @param userId 车主id
     * @return 屏蔽的发货人userIds
     */
    @Override
    public List<Long> getShieldingShipperFromCache(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        // 获取当前用户屏蔽的发货人列表
        Map<String, String> shieldingShipperMap = RedisUtil.getMap(SHIELDING_LIST_KEY_PREFIX + userId);
        if (MapUtils.isNotEmpty(shieldingShipperMap)) {
            return shieldingShipperMap.keySet().stream().map(Long::valueOf).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}