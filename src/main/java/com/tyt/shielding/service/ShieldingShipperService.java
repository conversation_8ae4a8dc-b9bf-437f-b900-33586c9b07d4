package com.tyt.shielding.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ShieldingShipper;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-01-25 11:16:52
 */
public interface ShieldingShipperService extends BaseService<ShieldingShipper, Long> {

    /**
     * 屏蔽发货人、取消屏蔽
     * @param userId
     * @param shieldingUserId
     * @param type
     * @return
     */
    ResultMsgBean shieldingShipper(Long userId, Long shieldingUserId, Integer type, Long orderId);

    /**
     * 获取当前用户屏蔽的发货人
     * @param userId
     * @return
     */
    ResultMsgBean selectShieldingShipper(Long userId);

    /**
     * 从缓存中获取当前用户屏蔽的发货人id
     *
     * @param userId 车主id
     * @return 屏蔽的发货人userIds
     */
    List<Long> getShieldingShipperFromCache(Long userId);
}

