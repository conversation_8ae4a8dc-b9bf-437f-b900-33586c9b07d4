package com.tyt.shielding.bean.DTO;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @date ：2022:01:25 15:47
 * @description：货车导航信息 DTO
 * @ClassName : com.tyt.shielding.bean.DTO.TruckNavigationInfoDTO
 */
@Data
public class TruckNavigationInfoDTO {
    /**
     * 用户 id
     */
    @NotEmpty(message = "用户 id 不能为空！")
    private Long userId;
    /**
     * 车牌号
     */
    @NotBlank(message = "请填写车头牌号")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5]{1}[A-Z]{1}[A-Z_0-9]{5}$", message = "车牌号格式不正确！")
    private String carNumber;
    /**
     * 货车长度
     */
    @NotEmpty(message = "请填写车货总长")
    @Min(value = 1, message = "车货总车不符合标准")
    @Max(value = 30, message = "车货总车不符合标准")
    private Float truckLong;
    /**
     * 货车宽
     */
    @NotEmpty(message = "请填写车货总宽")
    @Min(value = 1, message = "车货总宽不符合标准")
    @Max(value = 5, message = "车货总宽不符合标准")
    private Float truckWidth;
    /**
     * 货车高
     */
    @NotEmpty(message = "请填写车货总高")
    @Min(value = 1, message = "车货总高不符合标准")
    @Max(value = 10, message = "车货总高不符合标准")
    private Float truckHigh;
    /**
     * 货车重
     */
    @NotEmpty(message = "请填写车货总重量")
    @Min(value = 1, message = "车货总重不符合标准")
    @Max(value = 500, message = "车货总重不符合标准")
    private Float truckWeight;
    /**
     * 货车核定载重
     */
    @NotEmpty(message = "请填写核定载重")
    @Min(value = 1, message = "核定载重不符合标准")
    @Max(value = 500, message = "核定载重不符合标准")
    private Float truckApprovedLoad;
    /**
     * 轴数
     */
    @NotEmpty(message = "请输入轴数")
    @Pattern(regexp = "^(([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5]))$", message = "请填写合理车辆轴数")
    private String axesNumber;
    /**
     * 排放标准(高德必填 腾讯不需要)
     */
    private String emissionStandards;

    /**
     * 导航类型 导航类型 （0 高德 1 腾讯）
     */
    private Integer navigateType;

    /**
     * 车牌颜色。1.蓝牌；2.黄牌；3.黑牌; 4.白牌; 5.绿牌(新能源、农用车)；6.黄绿
     */
    private Integer plateColor;

    /**
     * 货车类型 1 微型车 2 轻型车 3 中型车 4 重型车
     * 高德不需要  腾讯必填
     */
    private Integer truckType;
}