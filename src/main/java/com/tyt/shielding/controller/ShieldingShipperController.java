package com.tyt.shielding.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.ShieldingShipper;
import com.tyt.shielding.service.ShieldingShipperService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @date ：2022:01:26 14:34
 * @description：屏蔽发货人下所有货源 Controller
 * @ClassName : com.tyt.shielding.controller.ShieldingShipperController
 */
@Controller
@RequestMapping("/plat/shieldingShipper")
public class ShieldingShipperController {

    /**
     * 屏蔽
     */
    private static final Integer SHIELDING = 1;

    @Resource
    private ShieldingShipperService shieldingShipperService;

    /**
     * 屏蔽发货人、取消屏蔽
     * @param userId
     * @param shieldingUserId
     * @param type
     * @return
     */
    @ResponseBody
    @RequestMapping("shieldingShipper")
    public ResultMsgBean shieldingShipper(Long userId, Long shieldingUserId, Integer type, Long orderId){
        if(null == userId || null == shieldingUserId || null == type || (type.equals(SHIELDING) && null == orderId)){
            ResultMsgBean resultMsgBean = new ResultMsgBean();
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("参数异常");
            return resultMsgBean;
        }
        return shieldingShipperService.shieldingShipper(userId, shieldingUserId, type, orderId);
    }

    /**
     * 获取当前用户屏蔽的发货人
     * @param userId
     * @return
     */
    @ResponseBody
    @RequestMapping("selectShieldingShipper")
    public ResultMsgBean selectShieldingShipper(Long userId){
        if(null == userId){
            ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.ERROR, "参数异常");
            return resultMsgBean;
        }
        ResultMsgBean resultMsgBean = shieldingShipperService.selectShieldingShipper(userId);
        return resultMsgBean;
    }

}
