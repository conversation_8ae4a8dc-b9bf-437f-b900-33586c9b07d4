package com.tyt.shielding.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.TruckNavigationInfo;
import com.tyt.shielding.bean.DTO.TruckNavigationInfoDTO;
import com.tyt.shielding.constant.TruckConstant;
import com.tyt.shielding.service.TruckNavigationInfoService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022:01:25 15:42
 * @description：车辆导航信息controller
 * @ClassName : com.tyt.shielding.controller.TruckNavigationInfoController
 */
@Controller
@RequestMapping("/plat/truckNavigationInfo")
public class TruckNavigationInfoController {

    public static org.slf4j.Logger logger = LoggerFactory.getLogger(TruckNavigationInfoController.class);

    @Resource
    private TruckNavigationInfoService truckNavigationInfoService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;



    /**
     * 车辆导航信息新增或修改(高德)
     * @param truckNavigationInfoDTO
     * @param bindingResult
     * @return
     */
    @ResponseBody
    @RequestMapping("insertTruckNavigationInfo")
    public ResultMsgBean insertOrUpdateTruckNavigationInfo(@Valid TruckNavigationInfoDTO truckNavigationInfoDTO, BindingResult bindingResult){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        if(bindingResult.hasErrors()){
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg(bindingResult.getFieldErrors().get(0).getDefaultMessage());
            return resultMsgBean;
        }
        if(StringUtils.isBlank(truckNavigationInfoDTO.getEmissionStandards())){
            return new ResultMsgBean(ResultMsgBean.ERROR,"排放标准不符合标准");
        }
        Integer onOff = tytConfigService.getIntValue(Constant.CAR_INFO_ONOFF, 1);
        if(onOff.intValue() == NumberUtils.INTEGER_ZERO.intValue()){
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("当前货车导航功能升级中，请点击返回，继续使用常规导航服务");
            return resultMsgBean;
        }

        int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
        //用户id
        Long userId = truckNavigationInfoDTO.getUserId();
        //车牌号
        String carNumber = truckNavigationInfoDTO.getCarNumber();
        try {
            if (LockUtil.lockObject("3", userId + carNumber, redisLockTimeout)) {
                truckNavigationInfoDTO.setNavigateType(TruckConstant.NAVIGATE_TYPE_AMAP);
                truckNavigationInfoService.insertOrUpdate(truckNavigationInfoDTO);
            }
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("参数不能为空");
            logger.error("TruckNavigationInfoController insertTruckNavigationInfo error",e);
        } finally {
            LockUtil.unLockObject("3", userId + carNumber);
        }
        return resultMsgBean;
    }

    /**
     * 查询用户的导航信息(高德)
     * @param userId
     * @return
     */
    @ResponseBody
    @RequestMapping("selectTruckNavigationInfo")
    public ResultMsgBean selectTruckNavigationInfo(Long userId){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        if(null == userId){
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("参数不能为空");
            return resultMsgBean;
        }
        List<TruckNavigationInfo> truckNavigationInfos =  new ArrayList<>();
        Integer onOff = tytConfigService.getIntValue(Constant.CAR_INFO_ONOFF, 1);

        if(onOff.intValue() != NumberUtils.INTEGER_ZERO.intValue()){
            truckNavigationInfos = truckNavigationInfoService.selectTruckNavigationInfo(userId, TruckConstant.NAVIGATE_TYPE_AMAP);
        }
        resultMsgBean.setData(truckNavigationInfos);
        return resultMsgBean;
    }

    /**
     * @description 删除货车导航信息接口(高德)
     * <AUTHOR>
     * @date 2022/6/28 11:40
     * @param userId
     * @return com.tyt.model.ResultMsgBean
     */
    @ResponseBody
    @RequestMapping("deleteTruckNavigationInfo")
    public ResultMsgBean deleteTruckNavigationInfo(Long userId){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        try {
            if(null == userId){
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("用户ID不能为空");
                return resultMsgBean;
            }
            truckNavigationInfoService.deleteTruckNavigationInfo(userId, TruckConstant.NAVIGATE_TYPE_AMAP);
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("删除货车导航信息失败");
            logger.error("TruckNavigationInfoController deleteTruckNavigationInfo error",e);
        }
        return resultMsgBean;
    }

    /**
     * 车辆导航信息新增或修改(腾讯)
     * @param truckNavigationInfoDTO
     * @param bindingResult
     * @return ResultMsgBean
     */
    @ResponseBody
    @RequestMapping("insertTencentTruckNavigationInfo")
    public ResultMsgBean insertTencentTruckNavigationInfo(@Valid TruckNavigationInfoDTO truckNavigationInfoDTO, BindingResult bindingResult) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        if (bindingResult.hasErrors()) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg(bindingResult.getFieldErrors().get(0).getDefaultMessage());
            return resultMsgBean;
        }
        if(null==truckNavigationInfoDTO.getPlateColor()){
            return new ResultMsgBean(ResultMsgBean.ERROR,"车牌颜色不能为空");
        }
        int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
        //用户id
        Long userId = truckNavigationInfoDTO.getUserId();
        //车牌号
        String carNumber = truckNavigationInfoDTO.getCarNumber();
        try {
            if (LockUtil.lockObject("3", userId + carNumber, redisLockTimeout)) {
                truckNavigationInfoDTO.setNavigateType(TruckConstant.NAVIGATE_TYPE_TENCENT);
                truckNavigationInfoService.insertOrUpdate(truckNavigationInfoDTO);
            }
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("参数不能为空");
            logger.error("TruckNavigationInfoController insertTencentTruckNavigationInfo error",e);
        } finally {
            LockUtil.unLockObject("3", userId + carNumber);
        }
        return resultMsgBean;
    }


    /**
     * 删除货车导航信息接口(腾讯)
     * @param userId
     * @return ResultMsgBean
     */
    @ResponseBody
    @RequestMapping("deleteTencentTruckNavigationInfo")
    public ResultMsgBean deleteTencentTruckNavigationInfo(Long userId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        try {
            if (null == userId) {
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("用户ID不能为空");
                return resultMsgBean;
            }
            truckNavigationInfoService.deleteTruckNavigationInfo(userId, TruckConstant.NAVIGATE_TYPE_TENCENT);
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("删除货车导航信息失败");
            logger.error("TruckNavigationInfoController deleteTencentTruckNavigationInfo error",e);
        }
        return resultMsgBean;
    }

    /**
     * 查询用户的导航信息(腾讯)
     * @param userId
     * @return ResultMsgBean
     */
    @ResponseBody
    @RequestMapping("selectTencentTruckNavigationInfo")
    public ResultMsgBean selectTencentTruckNavigationInfo(Long userId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        if (null == userId) {
            return new ResultMsgBean(ResultMsgBean.ERROR, "参数不能为空");
        }
        List<TruckNavigationInfo> truckNavigationInfos = new ArrayList<>();
        Integer onOff = tytConfigService.getIntValue(Constant.CAR_INFO_ONOFF, 1);

        if (onOff.intValue() != NumberUtils.INTEGER_ZERO.intValue()) {
            truckNavigationInfos = truckNavigationInfoService.selectTruckNavigationInfo(userId, TruckConstant.NAVIGATE_TYPE_TENCENT);
        }
        resultMsgBean.setData(truckNavigationInfos);
        return resultMsgBean;
    }


}
