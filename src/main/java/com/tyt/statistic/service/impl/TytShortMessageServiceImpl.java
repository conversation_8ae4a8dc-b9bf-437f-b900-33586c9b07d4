package com.tyt.statistic.service.impl;

import java.util.Date;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytShortMessage;
import com.tyt.statistic.service.TytShortMessageService;
import com.tyt.util.TimeUtil;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("tytShortMessageService")
public class TytShortMessageServiceImpl extends
		BaseServiceImpl<TytShortMessage, Long> implements
		TytShortMessageService {

	@Resource(name = "tytShortMessageDao")
	public void setBaseDao(BaseDao<TytShortMessage, Long> tytShortMessageDao) {
		super.setBaseDao(tytShortMessageDao);
	}

	@Override
	public TytShortMessage addMessage(TytShortMessage message)
			throws Exception {
		message.setCreateTime(new Date());
		this.add(message);
		return message;
	}

	@Override
	public Integer getMaxCount(Long userId, String msgType) throws Exception {
		String countSQL = "SELECT m.`count` FROM tyt_user_message m "
				+ "WHERE m.`user_id`=? AND m.`msg_type`=? AND m.`create_time`>=?";
		Integer count = this.getBaseDao().query(countSQL,
				new Object[] { userId, msgType, TimeUtil.today()});
		if (count == null) {
			return 0;
		}
		return count;
	}

}
