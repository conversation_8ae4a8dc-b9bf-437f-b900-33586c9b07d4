package com.tyt.statistic.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUserMessage;
import com.tyt.statistic.service.TytUserMessageService;
import com.tyt.util.TimeUtil;

@Service("tytUserMessageService")
public class TytUserMessageServiceImpl extends
		BaseServiceImpl<TytUserMessage, Long> implements TytUserMessageService {

	@Resource(name = "tytUserMessageDao")
	public void setBaseDao(BaseDao<TytUserMessage, Long> tytUserMessageSDao) {
		super.setBaseDao(tytUserMessageSDao);
	}

	@Override
	public void updateMessageCount(Long userId, String msgType)
			throws Exception {
		// 修改发短信次数
		String updateSQL = "UPDATE tyt_user_message m SET m.`count`=m.`count`+1 "
				+ "WHERE m.`user_id`=? AND m.`msg_type`=? AND m.`create_time`>=?";
		int i = this.getBaseDao().executeUpdateSql(updateSQL,
				new Object[] { userId, msgType,TimeUtil.today()});
		// 没有记录，则添加一条
		if (i == 0) {
			TytUserMessage userMessage = new TytUserMessage();
			userMessage.setUserId(userId);
			userMessage.setMsgType(msgType);
			userMessage.setCreateTime(new Date());
			userMessage.setCount(1);
			this.add(userMessage);
		}
	}

}
