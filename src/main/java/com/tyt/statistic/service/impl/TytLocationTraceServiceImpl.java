package com.tyt.statistic.service.impl;

import java.util.Date;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytLocationTrace;
import com.tyt.statistic.service.TytLocationTraceService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("tytLocationTraceService")
public class TytLocationTraceServiceImpl extends
		BaseServiceImpl<TytLocationTrace, Long> implements
		TytLocationTraceService {

	@Resource(name = "tytLocationTraceDao")
	public void setBaseDao(BaseDao<TytLocationTrace, Long> tytLocationTraceDao) {
		super.setBaseDao(tytLocationTraceDao);
	}

	@Override
	public TytLocationTrace addTraceLocation(TytLocationTrace trace)
			throws Exception {
		trace.setCreateTime(new Date());
		this.add(trace);
		return trace;
	}

}
