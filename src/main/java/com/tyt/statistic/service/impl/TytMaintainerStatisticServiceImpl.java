package com.tyt.statistic.service.impl;

import java.util.Date;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytShortMessage;
import com.tyt.statistic.service.TytMaintainerStatisticService;

@Service("tytMaintainerStatisticServiceImpl")
public class TytMaintainerStatisticServiceImpl extends
		BaseServiceImpl<TytShortMessage, Long> implements
		TytMaintainerStatisticService {

	// 只是需要一个注入dao,选择哪个没关系，只用其基本dao层方法
	@Resource(name = "tytShortMessageDao")
	public void setBaseDao(BaseDao<TytShortMessage, Long> dao) {
		super.setBaseDao(dao);
	}

	@Override
	public void addActivityChange(String serviceId, String userId, String status) {
		
		String saveSQL="INSERT INTO `tyt_merchant_service_status_record` (`service_id`, `user_id`, `status`, `create_time`) VALUES(?,?,?,?);";
		this.getBaseDao().executeUpdateSql(saveSQL,
				new Object[] { serviceId, userId, status, new Date() });
	}

	@Override
	public void addQueryCondition(Long userId,int type, String content, String resultSize) {

		String saveSearchSQL = "INSERT INTO `tyt_search_condition_record` (`user_id`,`type`, `content`, `result_size`, `create_time`) VALUES(?,?,?,?,?);";
		this.getBaseDao().executeUpdateSql(saveSearchSQL,
				new Object[] {userId, type, content, resultSize, new Date() });
	}

	@Override
	public void addScanData(String userId, long msgId, int type) {

		String saveScanSQL = "INSERT INTO `tyt_read_record` (`msg_type`, `msg_id`, `user_id`, `create_time`) VALUES(?,?,?,?);";
		this.getBaseDao().executeUpdateSql(saveScanSQL,
				new Object[] { type, msgId, userId, new Date() });

	}
}
