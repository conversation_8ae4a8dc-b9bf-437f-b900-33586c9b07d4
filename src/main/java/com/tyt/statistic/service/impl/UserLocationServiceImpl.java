package com.tyt.statistic.service.impl;

import java.util.Date;
import java.util.Map;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.User;
import com.tyt.model.UserLocation;
import com.tyt.statistic.service.UserLocationService;
import com.tyt.user.service.UserService;
import com.tyt.util.StringUtil;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 
 * <AUTHOR>
 * @date   2015年12月27日上午10:07:21
 *
 */
@Service("userLocationService")
public class UserLocationServiceImpl extends BaseServiceImpl<UserLocation,Long> implements UserLocationService {

	@Resource(name = "userService")
	private UserService userService;
	
    @Resource(name="userLocationDao")
    public void setBaseDao(BaseDao<UserLocation, Long> userLocationDao) {
        super.setBaseDao(userLocationDao);
    }

	
	@Override
	public UserLocation addLocation(Map<String, String> params) {
		// TODO Auto-generated method stub
		try {
			//生成对象
			UserLocation location=createLocation(params);
			//保存到数据库
			this.add(location);
			return location;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}


	/**
	 * @param params
	 * @return UserLocation
	 */
	private UserLocation createLocation(Map<String, String> params) {
		try {
			UserLocation location=new UserLocation();
			//基础参数
			location.setClientSign(Integer.parseInt(params.get("clientSign"))); 
			location.setClientVersion(params.get("clientVersion"));
			location.setOpType(params.get("opType"));
			location.setCountry(params.get("country"));
			location.setProvince(params.get("province"));
			location.setCity(params.get("city"));
			location.setDistrict(params.get("district"));
			location.setRoad(params.get("road"));
			location.setLongitude(params.get("longitude"));
			location.setLatitude(params.get("latitude"));
            location.setCreateTime(new Date());
            if (StringUtils.hasLength(params.get("aoiName"))){
                location.setAoiName(params.get("aoiName"));
            }
            if (StringUtils.hasLength(params.get("poiName"))){
                location.setPoiName(params.get("poiName"));
            }
            if (StringUtils.hasLength(params.get("address"))){
                location.setAddress(params.get("address"));
            }
            if (StringUtils.hasLength(params.get("number"))){
                location.setNumber(params.get("number"));
            }
			//用户ID
			String userId=params.get("userId");
			if(StringUtils.hasLength(userId)){
				Long userIdLong=Long.parseLong(params.get("userId"));
				User user=userService.getByUserId(userIdLong);
				if(user==null){
					throw new RuntimeException("用户不存在");
				}
				location.setUserId(userIdLong);
				location.setCellPhone(user.getCellPhone());
				location.setUserSign(user.getUserSign());
				location.setDeliverType(user.getDeliverType());
			}
			return location;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

 
}
