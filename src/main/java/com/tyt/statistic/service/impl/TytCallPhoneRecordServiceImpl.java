package com.tyt.statistic.service.impl;

import java.util.Date;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCallPhoneRecord;
import com.tyt.statistic.service.TytCallPhoneRecordService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("tytCallPhoneRecordService")
public class TytCallPhoneRecordServiceImpl extends
		BaseServiceImpl<TytCallPhoneRecord, Long> implements
		TytCallPhoneRecordService {

	@Resource(name = "tytCallPhoneRecordDao")
	public void setBaseDao(
			BaseDao<TytCallPhoneRecord, Long> tytCallPhoneRecordDao) {
		super.setBaseDao(tytCallPhoneRecordDao);
	}

	@Override
	public TytCallPhoneRecord addPhone(TytCallPhoneRecord phone) {
		phone.setCreateTime(new Date());
		this.add(phone);
		return phone;
	}

}
