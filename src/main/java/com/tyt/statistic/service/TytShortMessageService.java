package com.tyt.statistic.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytShortMessage;

public interface TytShortMessageService extends
		BaseService<TytShortMessage, Long> {
	/**
	 * 保存短信内容
	 * 
	 * @param message
	 * @return
	 * @throws Exception
	 */
	public TytShortMessage addMessage(TytShortMessage message)
			throws Exception;
	/**
	 * 查询我的最大发布次数
	 * @param userId 
	 * @param msgType
	 * @return
	 * @throws Exception
	 */
	public Integer getMaxCount(Long userId, String msgType)throws Exception;

}
