package com.tyt.statistic.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytLocationTrace;
import com.tyt.statistic.dao.TytLocationTraceDao;
import org.springframework.stereotype.Repository;

@Repository("tytLocationTraceDao")
public class TytLocationTraceDaoImpl extends
		BaseDaoImpl<TytLocationTrace, Long> implements TytLocationTraceDao {
	public TytLocationTraceDaoImpl() {
		this.setEntityClass(TytLocationTrace.class);
	}
}
