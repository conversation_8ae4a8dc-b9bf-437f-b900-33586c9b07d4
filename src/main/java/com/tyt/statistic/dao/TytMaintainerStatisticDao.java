package com.tyt.statistic.dao;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytShortMessage;

/**
 * 维修师活动切换记录
 * 
 * <AUTHOR>
 * @date 2016-6-3下午2:33:47
 * @description
 */
public interface TytMaintainerStatisticDao extends BaseService<TytShortMessage, Long> {

	/**
	 * 记录维修师接活状态改变
	 * 
	 * @param serviceId
	 * @param userId
	 * @param status
	 */
	void addActivityChange(String serviceId, String userId, String status);

	/**
	 * 
	 * @param type
	 *            1 搜索商户 2 搜索维修师
	 * @param content
	 *            搜索内容
	 */
	void addQueryCondition(int type, String content,String resultSize);

	/**
	 * 记录维修师信息浏览记录
	 * 
	 * @param userId
	 *            浏览用户id
	 * @param msgId
	 *            被浏览器维修师id
	 * @param type
	 *            类型 1 维修师 2 商户
	 */
	void addScanData(String userId, long msgId, int type);
}
