package com.tyt.statistic.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.UserLocation;
import com.tyt.statistic.service.UserLocationService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;

/**
 * 采集类接口
 * 
 * <AUTHOR>
 * @date 2015年12月27日上午10:10:17
 * 
 */
@Controller
@RequestMapping("/plat/collect")
public class CollectionController extends BaseController {

	@Resource(name = "userLocationService")
	UserLocationService userLocationService;
	@Resource(name = "userService")
	private UserService userService;

	@RequestMapping("/user/location/save")
	public void userLocation(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("opType");
					add("longitude");
					add("latitude");
				}
			};
			if (!validateArguments("用户登录", request, response, params, names))
				return;
			/* 保存对象 */
			UserLocation location = userLocationService.addLocation(params);
			backResponse(request, response, ReturnCodeConstant.OK, "采集定位信息成功", null, 0);
			logger.info("采集定位信息成功");
		} catch (Exception e) {
			backResponse(request, response, ReturnCodeConstant.ERROR, "采集定位信息异常", null, 0);
			logger.info("采集定位信息异常");
			e.printStackTrace();
			return;
		}

	}

	/**
	 * 
	 * @param userId
	 * @param name
	 *            功能模块名称
	 * @param clientId
	 *            终端唯一标识
	 * @param osVersion
	 *            操作系统版本号
	 * @param clientSign
	 *            终端标识(1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB)
	 * @return
	 */
	@RequestMapping("/user/click")
	@ResponseBody
	public ResultMsgBean moduleUseStatistic(String userId, String name, String clientId, String osVersion, String clientSign, 
			@RequestParam(value="type",defaultValue="1")String type, String clientVersion) {
			
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ResultMsgBean.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(name)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("name不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(clientId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("clientId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(osVersion)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("osVersion不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(clientSign)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("clientSign不能为空");
				return rm;
			}
			// 添加用户点击使用模块记录
			userService.addModuleUse(userId, name, clientId, osVersion, clientSign,type, clientVersion);
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ResultMsgBean.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}
}
