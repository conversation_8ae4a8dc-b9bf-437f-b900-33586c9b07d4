package com.tyt.statistic.controller;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.ShortMsgBean;
import com.tyt.maintainer.service.MaintainerService;
import com.tyt.merchant.service.TytMerchantService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCallPhoneRecord;
import com.tyt.model.TytLocationTrace;
import com.tyt.model.TytShortMessage;
import com.tyt.statistic.service.TytCallPhoneRecordService;
import com.tyt.statistic.service.TytLocationTraceService;
import com.tyt.statistic.service.TytShortMessageService;
import com.tyt.statistic.service.TytUserMessageService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.StringUtil;

@Controller
@RequestMapping("/plat/statistic")
public class StatisticController {

	@Resource(name = "tytLocationTraceService")
	TytLocationTraceService tytLocationTraceService;

	@Resource(name = "tytShortMessageService")
	TytShortMessageService tytShortMessageService;

	@Resource(name = "tytUserMessageService")
	TytUserMessageService tytUserMessageService;

	@Resource(name = "tytConfigService")
	TytConfigService tytConfigService;

	@Resource(name = "tytCallPhoneRecordService")
	TytCallPhoneRecordService tytCallPhoneRecordService;

	@Resource(name = "maintainerService")
	private MaintainerService maintainerService;

	@Resource(name = "tytMerchantService")
	TytMerchantService tytMerchantService;
	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;

	/**
	 * 用户实时位置采集
	 * 
	 * @param trace
	 * @return
	 */
	@RequestMapping("/currentLocation")
	@ResponseBody
	public ResultMsgBean saveCurrentLocation(TytLocationTrace trace) {
		ResultMsgBean rm = new ResultMsgBean();

		try {
			if (trace.getLongitude() == null || "".equals(trace.getLongitude().trim()) || !StringUtil.isDouble(trace.getLongitude().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("longitude不能为空");
				return rm;
			}
			if (trace.getLatitude() == null || "".equals(trace.getLatitude().trim()) || !StringUtil.isDouble(trace.getLatitude().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("latitude不能为空");
				return rm;
			}
			// 保存实时位置信息
			tytLocationTraceService.addTraceLocation(trace);

			// 修改维修师的经纬度
			maintainerService.updateRecentInfo(trace.getUserId(), trace.getLongitude(), trace.getLatitude(), trace.getProvince(), trace.getCity(), trace.getCounty(), trace.getCreateTime());

			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("保存成功");
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * 发送短信
	 * 
	 * @param message
	 * @param userId
	 * @return
	 */
	@RequestMapping("/shortMessage")
	@ResponseBody
	public ResultMsgBean saveShortMessage(TytShortMessage message) {

		ResultMsgBean rm = new ResultMsgBean();

		try {
			// 参数验证
			if (message.getMsgType() == null || "".equals(message.getMsgType().trim()) || !StringUtil.isNumeric(message.getMsgType().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("msgType不能为空");
				return rm;
			}
			if (message.getMsgId() == null || message.getMsgId().longValue() <= 0) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("msgId不能为空");
				return rm;
			}
			if (message.getMsgType() == null || "".equals(message.getMsgType().trim()) || !StringUtil.isNumeric(message.getMsgType().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("msgType不能为空");
				return rm;
			}
			if (message.getFromPhone() == null || "".equals(message.getFromPhone().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("fromPhone不能为空");
				return rm;
			}
			if (message.getToPhone() == null || "".equals(message.getToPhone().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("toPhone不能为空");
				return rm;
			}
			if (message.getContent() == null || "".equals(message.getContent().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("content不能为空");
				return rm;
			}
			// 获取最大条数
			// 最大条数保存到tyt_config表，以shortMessageCountLimit_类型 命名
			String msgType = message.getMsgType();
			Integer limitCount = tytConfigService.getIntValue("shortMessageCountLimit_" + msgType);
			// 检查是否超过最大条数
			Integer maxCount = tytShortMessageService.getMaxCount(message.getUserId(), msgType);
			if (limitCount != null && maxCount.intValue() >= limitCount.intValue()) {
				rm.setCode(ReturnCodeConstant.MORE_THAN_LIMIT);
				rm.setMsg("每天短信咨询最多" + limitCount + "次");
				return rm;
			}
			/*
			 * 发送短信
			 * SendMessage.sendMessageWithNewPlat(message.getToPhone().trim(),
			 * message.getContent().trim());
			 */
			ShortMsgBean shortMsgBean = new ShortMsgBean();
			// 根据短信key获取短信模板
			shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			String messageSerailNum = SerialNumUtil.generateSeriaNum();
			shortMsgBean.setMessageSerailNum(messageSerailNum);
			shortMsgBean.setContent(message.getContent().trim());
			shortMsgBean.setCell_phone(message.getToPhone().trim());
			shortMsgBean.setRemark("");
			tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			tytMqMessageService.sendMqMessage(messageSerailNum, JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			// 保存短信内容
			TytShortMessage msg = tytShortMessageService.addMessage(message);
			// 修改短信次数
			tytUserMessageService.updateMessageCount(msg.getUserId(), msg.getMsgType());

			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("保存成功");
			return rm;
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	@RequestMapping("/callPhone")
	@ResponseBody
	public ResultMsgBean saveCallPhone(TytCallPhoneRecord phone) {
		ResultMsgBean rm = new ResultMsgBean();

		try {
			// 参数验证
			if (phone.getMsgType() == null || "".equals(phone.getMsgType().trim()) || !StringUtil.isNumeric(phone.getMsgType().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("msgType不能为空");
				return rm;
			}
			// if (phone.getMsgId() == null || phone.getMsgId().longValue() <=
			// 0) {
			// rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			// rm.setMsg("msgId不能为空");
			// return rm;
			// }
			if (phone.getMsgType() == null || "".equals(phone.getMsgType().trim()) || !StringUtil.isNumeric(phone.getMsgType().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("msgType不能为空");
				return rm;
			}
			if (phone.getFromPhone() == null || "".equals(phone.getFromPhone().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("fromPhone不能为空");
				return rm;
			}
			if (phone.getToPhone() == null || "".equals(phone.getToPhone().trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("toPhone不能为空");
				return rm;
			}
			// 添加到电话记录表
			phone = tytCallPhoneRecordService.addPhone(phone);

			// 修改相关表的被拨打次数
			// 1维修师表
			if (phone.getMsgType().equals("1")) {
				maintainerService.updateCalledCount(phone.getMsgId());
			} else if (phone.getMsgType().equals("2")) {
				// 2商户
				tytMerchantService.updateCalledCount(phone.getMsgId());
			}

			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("保存成功");
			return rm;
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

}
