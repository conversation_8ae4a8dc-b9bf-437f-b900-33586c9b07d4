package com.tyt.maintainer.bean;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaintainerQueryResultBean implements java.io.Serializable {
	private static final long serialVersionUID = -2602069019730372351L;

	private int currentPage;
	private int totalRecord;
	private int pageSize;
	private int maxPage;
	List<TytMechantServiceBean> data;

	public int getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}

	public int getTotalRecord() {
		return totalRecord;
	}

	public void setTotalRecord(int totalRecord) {
		this.totalRecord = totalRecord;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public int getMaxPage() {
		return maxPage;
	}

	public void setMaxPage(int maxPage) {
		this.maxPage = maxPage;
	}

	public List<TytMechantServiceBean> getData() {
		return data;
	}

	public void setData(List<TytMechantServiceBean> data) {
		this.data = data;
	}
}
