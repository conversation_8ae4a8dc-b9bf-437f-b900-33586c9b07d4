package com.tyt.maintainer.bean;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;


import com.tyt.util.TimeUtil;

/**
 * <AUTHOR>
 * @date 2016-6-1下午2:41:39
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = { "lastOnlineDate" })
public class TytMechantServiceBean {
	private int id;
	/* 头像地址 */
	private String headImageUrl;
	/* 维修师的名称 */
	private String name;
	/* 维修师的电话列表 */
	private String telePhones;
	/* 维修师的接活状态 */
	private Integer status;
	/* 距离该维修师的距离 */
	private String distance;
	/* 维修师的技能 */
	private List<DeviceLabels> deviceLables;
	/* 被浏览次数 */
	private Integer readCount;
	/* 被呼叫次数 */
	private Integer callCount;
	/* 最后在线多长时间以前 */
	private Integer lastDay;
	/* 最后在线日期 */
	private String lastOnlineDate;
	/* 最近在现单位 */
	private String lastDayUnit = "天";
	/* 最后一次所在的经度（数据库乘以一万存储） */
	private String longitude;
	/* 最后一次所在的纬度（数据库乘以一万存储） */
	private String latitude;
	private Integer homePageDisplay;
	private String remark;
	/* 0禁用 1正常*/
	private String enabled;

	public Integer getHomePageDisplay() {
		return homePageDisplay;
	}

	public void setHomePageDisplay(Integer homePageDisplay) {
		this.homePageDisplay = homePageDisplay;
	}

	public String getLastOnlineDate() {
		return lastOnlineDate;
	}

	public void setLastOnlineDate(String lastOnlineDate) {
		this.lastOnlineDate = lastOnlineDate;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public Integer getReadCount() {
		return readCount;
	}

	public void setReadCount(Integer readCount) {
		this.readCount = readCount;
	}

	public Integer getCallCount() {
		return callCount;
	}

	public void setCallCount(Integer callCount) {
		this.callCount = callCount;
	}

	public Integer getLastDay() {
		try {
		if (lastOnlineDate != null && !"".equals(lastOnlineDate.trim())) {
			lastOnlineDate = lastOnlineDate.substring(0, 19);
			SimpleDateFormat SDF_DATE = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat SDFHMS_DATE = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			
				/*
				 * 上次提交位置时间<10分钟=1分钟前 10分钟<上次提交位置时间<30分钟=10分钟前
				 * 30分钟<上次提交位置时间<1小时=30分钟前 1小时<上次提交位置时间<2小时=1小时前
				 * 2小时<上次提交位置时间<3小时=2小时前 。。。。。。。。。依次类推。。。。。。。。。
				 * 23小时<上次提交位置时间<24小时=23小时前 24小时<上次提交位置时间<48小时=1天前
				 * 48小时<上次提交位置时间<72小时=2天前 。。。。。。。。。依次类推。。。。。。。。。
				 * 720小时<上次提交位置时间=30天前
				 */
				int minutesDiff = TimeUtil.minutesDiff(lastOnlineDate, SDFHMS_DATE.format(new Date(System.currentTimeMillis())));
				if (minutesDiff < 10) {
					this.setLastDayUnit("分钟前");
					return 1;
				} else if (minutesDiff > 10 && minutesDiff < 30) {
					this.setLastDayUnit("分钟前");
					return 10;
				} else if (minutesDiff > 30 && minutesDiff < 60) {
					this.setLastDayUnit("分钟前");
					return 30;
				} else {
					int hourDiff = minutesDiff / 60;
					if (hourDiff <= 23) {
						this.setLastDayUnit("小时前");
						return hourDiff;
					} else if (hourDiff > 23 && hourDiff < 24) {
						this.setLastDayUnit("小时前");
						return 23;
					} else {
						int dayDiff = TimeUtil.getDays(lastOnlineDate, SDF_DATE.format(new Date(System.currentTimeMillis())));
						if (dayDiff < 30) {
							this.setLastDayUnit("天前");
							return dayDiff;
						} else {
							this.setLastDayUnit("天前");
							return 30;
						}
					}
				}
			
		}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return lastDay;
	}

	public void setLastDay(int lastDay) {
		this.lastDay = lastDay;
	}

	public String getLastDayUnit() {
		return lastDayUnit;
	}

	public void setLastDayUnit(String lastDayUnit) {
		this.lastDayUnit = lastDayUnit;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getHeadImageUrl() {
		return headImageUrl;
	}

	public void setHeadImageUrl(String headImageUrl) {
		this.headImageUrl = headImageUrl;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTelePhones() {
		return telePhones;
	}

	public void setTelePhones(String telePhones) {
		this.telePhones = telePhones;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getDistance() {
		return distance;
	}

	public void setDistance(String distance) {
		this.distance = distance;
	}

	public List<DeviceLabels> getDeviceLables() {
		return deviceLables;
	}

	public void setDeviceLables(List<DeviceLabels> deviceLables) {
		this.deviceLables = deviceLables;
	}

	public void setLastDay(Integer lastDay) {
		this.lastDay = lastDay;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getEnabled() {
		return enabled;
	}

	public void setEnabled(String enabled) {
		this.enabled = enabled;
	}
}
