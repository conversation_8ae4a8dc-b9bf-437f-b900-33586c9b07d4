package com.tyt.maintainer.bean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = { "serviceId" })
public class DeviceLabels {
	private Integer id;
	private Integer deviceType;
	private Integer serviceId;
	private String lable;
	private String deviceTypeName;
	private String lableNames;

	public Integer getServiceId() {
		return serviceId;
	}

	public void setServiceId(Integer serviceId) {
		this.serviceId = serviceId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(Integer deviceType) {
		this.deviceType = deviceType;
	}

	public String getLable() {
		return lable;
	}

	public void setLable(String lable) {
		this.lable = lable;
	}

	public String getDeviceTypeName() {
		return deviceTypeName;
	}

	public void setDeviceTypeName(String deviceTypeName) {
		this.deviceTypeName = deviceTypeName;
	}

	public String getLableNames() {
		return lableNames;
	}

	public void setLableNames(String lableNames) {
		this.lableNames = lableNames;
	}

	@Override
	public String toString() {
		return "DeviceLabels [id=" + id + ", deviceType=" + deviceType
				+ ", serviceId=" + serviceId + ", deviceTypeName="
				+ deviceTypeName + ", lableNames=" + lableNames + ", lable="
				+ lable + "]";
	}

}
