package com.tyt.maintainer.controller;

import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.maintainer.bean.MaintainerQueryResultBean;
import com.tyt.maintainer.bean.TytMechantServiceBean;
import com.tyt.maintainer.bean.TytMechantServiceSaveBean;
import com.tyt.maintainer.bean.TytMechantServiceUpdateBean;
import com.tyt.maintainer.service.MaintainerService;
import com.tyt.model.ResultMsgBean;
import com.tyt.statistic.service.TytMaintainerStatisticService;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/maintainer/")
public class MaintainerController extends BaseController {
	@Resource(name = "maintainerService")
	private MaintainerService maintainerService;

	@Resource(name = "tytMaintainerStatisticServiceImpl")
	private TytMaintainerStatisticService tytMaintainerStatisticService;
	
	/**
	 * 请求维修师列表
	 * 
	 * @param baseParameter
	 *            基础参数
	 * @param deviceType
	 * @param lable
	 * @param status
	 * @param latitude
	 * @param longitude
	 * @param currentPage
	 * @return
	 */
	@RequestMapping(value = "/query")
	@ResponseBody
	public ResultMsgBean list(String type, String deviceType, String lable,Long userId,
			String status, String latitude, String longitude, String currentPage) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			MaintainerQueryResultBean resultBean = maintainerService.query(
					type, deviceType, lable, status, latitude, longitude,
					currentPage);
			// 保存查询记录到统计表
			tytMaintainerStatisticService.addQueryCondition(userId,2, "技能: "
					+ deviceType + " 机种: " + lable + " 接活状态: " + status
					+ " 经度: " + longitude + " 纬度: " + latitude,
					resultBean.getData() == null ? "0" : resultBean.getData()
							.size() + "");
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			rm.setData(resultBean);
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 查询维修师的详细信息
	 * 
	 * @param baseParameter
	 * @param id
	 *            维修师主键
	 * @param userId
	 *            当前登录用户id
	 * @return
	 */
	@RequestMapping(value = "detail")
	@ResponseBody
	public ResultMsgBean detail(Long id, Long userId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (id == null || id.longValue()<=0) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("维修商ID不能为空");
			} /*
			 * else if (userId == null || "".equals(userId.trim())) {
			 * rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
			 * rm.setMsg("用户ID不能为空"); }
			 */else {
				TytMechantServiceBean serviceBean = maintainerService
						.getServiceById(userId==null?null:userId+"", id);
				// 保存浏览记录
				tytMaintainerStatisticService.addScanData(userId==null?null:userId+"",
						id, 1);
				// 维修师主表浏览次数加1
				maintainerService.updateReadedCount(Long.valueOf(id));
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(serviceBean);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 查询我的维修师信息
	 * 
	 * @param baseParameter
	 * @param userId
	 * @return
	 */
	@RequestMapping(value = "myInfo")
	@ResponseBody
	public ResultMsgBean myInfo(BaseParameter baseParameter, String userId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (userId == null || "".equals(userId.trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("参数错误");
			} else {
				TytMechantServiceBean serviceBean = maintainerService
						.queryMyInfo(userId);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(serviceBean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 保存我的维修师信息
	 * 
	 * @param baseParameter
	 * @param userId
	 * @return
	 */
	@RequestMapping(value = "save", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean save(TytMechantServiceSaveBean serviceSaveBean,
			@RequestParam(required=false) MultipartFile headImage) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (serviceSaveBean.getUserId() == null
					|| "".equals(serviceSaveBean.getUserId().trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("用户ID不能为空");
			} else if (serviceSaveBean.getCellPhone() == null
					|| "".equals(serviceSaveBean.getCellPhone().trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("用户账号不能为空");
			} else if (serviceSaveBean.getName() == null
					|| "".equals(serviceSaveBean.getName().trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("维修师姓名不能为空");
			} else if (serviceSaveBean.getDeviceLables() == null
					|| "".equals(serviceSaveBean.getDeviceLables().trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("标签信息不能为空");
			} else if (serviceSaveBean.getLongitude() == null
					|| "".equals(serviceSaveBean.getLongitude().trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("经度不能为空");
			} else if (serviceSaveBean.getLatitude() == null
					|| "".equals(serviceSaveBean.getLatitude().trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("纬度不能为空");
			} else if (serviceSaveBean.getTelePhones() == null
					|| "".equals(serviceSaveBean.getTelePhones().trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("联系电话不能为空");
			} else {
				/*
				 * 判断当前用户是否已经添加了维修商，如果已经添加则返回已添加提示
				 */
				if (maintainerService.isUserHasAddMaintainer(serviceSaveBean
						.getUserId())) {
					rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
					rm.setMsg("您已经发布过了，可以去修改维修师信息");
				} else {

					if(!super.isAllImg(headImage)){
						rm.setCode(ResultMsgBean.ERROR);
						rm.setMsg("图片格式有误！");
						return rm;
					}

					//保存图片，返回图片地址
					String imageName = maintainerService.saveHeadImage(headImage, serviceSaveBean.getUserId());
					// 保存信息
					maintainerService.saveData(serviceSaveBean, imageName);
					rm.setCode(ReturnCodeConstant.OK);
					rm.setMsg("保存成功");
				}
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 更新维修师的信息
	 * 
	 * @param serviceUpdateBean
	 * @param headImage
	 * @return
	 */
	@RequestMapping(value = "update", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean update(TytMechantServiceUpdateBean serviceUpdateBean,
			@RequestParam(required=false) MultipartFile headImage) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (serviceUpdateBean.getId() == null
					|| "".equals(serviceUpdateBean.getId())
					|| serviceUpdateBean.getUserId() == null
					|| "".equals(serviceUpdateBean.getUserId().trim())
					|| serviceUpdateBean.getName() == null
					|| "".equals(serviceUpdateBean.getName().trim())
					|| serviceUpdateBean.getDeviceLables() == null
					|| "".equals(serviceUpdateBean.getDeviceLables().trim())
					|| serviceUpdateBean.getUserId() == null
					|| "".equals(serviceUpdateBean.getUserId().trim())
					|| serviceUpdateBean.getName() == null
					|| "".equals(serviceUpdateBean.getName().trim())
					|| serviceUpdateBean.getDeviceLables() == null
					|| "".equals(serviceUpdateBean.getDeviceLables().trim())
					|| serviceUpdateBean.getLongitude() == null
					|| "".equals(serviceUpdateBean.getLongitude().trim())
					|| serviceUpdateBean.getLatitude() == null
					|| "".equals(serviceUpdateBean.getLatitude().trim())
					|| serviceUpdateBean.getTelePhones() == null
					|| "".equals(serviceUpdateBean.getTelePhones().trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("参数错误");
			} else {

				if(!super.isAllImg(headImage)){
					rm.setCode(ResultMsgBean.ERROR);
					rm.setMsg("图片格式有误！");
					return rm;
				}

				//保存图片，返回图片地址
				String imageName = maintainerService.saveHeadImage(headImage, serviceUpdateBean.getUserId());
				// 保存信息
				maintainerService.updateData(serviceUpdateBean, imageName);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("更新成功");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 更新维修师接活状态和首页 显示状态 id 必填 数字 维修师ID type 必填 数字 类型 1接活状态标识 2首页显示标识 status 必填
	 * 数字 接活：0休息中 1接活中 2忙碌中 首页显示： 0否 1是
	 * 
	 * @param serviceUpdateBean
	 * @param headImage
	 * @return
	 */
	@RequestMapping(value = "updateStatus", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean updateStatus(String userId, String id, String type,
			String status) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (userId == null || "".equals(userId.trim()) || id == null
					|| "".equals(id.trim()) || type == null
					|| "".equals(type.trim()) || status == null
					|| "".equals(status.trim())
					|| (!"1".equals(type) && !"2".equals(type))) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("参数错误");
			} else {
				// 保存信息
				maintainerService.updateStatus(userId, id, type, status);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("更新成功");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
