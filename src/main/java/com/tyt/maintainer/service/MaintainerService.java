package com.tyt.maintainer.service;

import java.util.Date;

import org.springframework.web.multipart.MultipartFile;

import com.tyt.base.service.BaseService;
import com.tyt.maintainer.bean.MaintainerQueryResultBean;
import com.tyt.maintainer.bean.TytMechantServiceBean;
import com.tyt.maintainer.bean.TytMechantServiceSaveBean;
import com.tyt.maintainer.bean.TytMechantServiceUpdateBean;
import com.tyt.model.TytMechantService;

/**
 * 维修师服务层
 * 
 * <AUTHOR>
 * @date 2016-6-1上午10:42:40
 * @description
 */
public interface MaintainerService extends BaseService<TytMechantService, Long> {

	/**
	 * 查询维修师列表信息
	 * 
	 * @param type
	 *            0分页 1全部
	 * @param deviceType
	 * @param lable
	 * @param status
	 * @param latitude
	 * @param longitude
	 * @param currentPage
	 * @return
	 */
	MaintainerQueryResultBean query(String type, String deviceType, String lable, String status, String latitude, String longitude, String currentPage);

	/**
	 * 获取维修师详情信息
	 * 
	 * @param userId
	 *            用户ID
	 * @param id
	 *            当前查询的维修师ID
	 * @return
	 */
	TytMechantServiceBean getServiceById(String userId, long serviceId);

	/**
	 * 查询我的维修师信息
	 * 
	 * @param userId
	 * @return
	 */
	TytMechantServiceBean queryMyInfo(String userId);

	/**
	 * 判断某个用户是否已经添加了商户
	 * 
	 * @param userId
	 * @return
	 */
	boolean isUserHasAddMaintainer(String userId);

	/**
	 * 保存商户信息
	 * 
	 * @param serviceSaveBean
	 * @return
	 */
	TytMechantService saveData(TytMechantServiceSaveBean serviceSaveBean, String imageName);

	/**
	 * 更新商户信息
	 * 
	 * @param serviceSaveBean
	 */
	void updateData(TytMechantServiceUpdateBean serviceSaveBean, String imageName);

	/**
	 * 更新维修师首页显示和接活状态
	 * 
	 * @param id
	 * @param type
	 * @param status
	 */
	void updateStatus(String userId, String id, String type, String status);

	/**
	 * @description 更新维修师显示状态
	 * <AUTHOR>
	 * @date 2021/12/25 19:01
	 * @param userId
	 * @param status
	 * @return int
	 */
	int updateStatusByUserId(Long userId, Integer status) throws Exception;

	/**
	 * 修改维修师的实时位置
	 * 
	 * @param id维修师唯一主键
	 * @param longitude经度
	 * @param latitude纬度
	 * @param provice省
	 * @param city市
	 * @param county县
	 * @param lastOnlineTime最后一次在线时间
	 * @return
	 */
	public void updateRecentInfo(Long userId, String longitude, String latitude, String provice, String city, String county, Date lastOnlineTime);

	/**
	 * 修改被拨打电话次数
	 * 
	 * @param id
	 */
	public void updateCalledCount(Long id);

	/**
	 * 修改被拨打浏览次数
	 * 
	 * @param id
	 */
	public void updateReadedCount(Long id);
    /**
     * 根据联系电话查询
     * @param tell
     * @return
     */
	Integer getByCellPhone(String telePhone);
    /**
     * 保存维修师图片
     * @param userId
     * @param headImageURL
     */
	public void saveHeadImageUrl(String userId, String headImageURL);
	/**
	 * 爆粗图片，为空的话默认个人中心的值，否则同时修改维修师跟个人中心的值
	 * @param headImage
	 * @param userId
	 * @return
	 * @throws Exception 
	 */
	public String saveHeadImage(MultipartFile headImage,String userId) throws Exception;

}
