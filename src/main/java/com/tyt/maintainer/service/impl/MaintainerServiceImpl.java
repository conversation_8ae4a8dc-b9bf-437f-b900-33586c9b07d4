package com.tyt.maintainer.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.maintainer.bean.DeviceLabels;
import com.tyt.maintainer.bean.MaintainerQueryResultBean;
import com.tyt.maintainer.bean.TytMechantServiceBean;
import com.tyt.maintainer.bean.TytMechantServiceSaveBean;
import com.tyt.maintainer.bean.TytMechantServiceUpdateBean;
import com.tyt.maintainer.service.MaintainerService;
import com.tyt.model.TytMechantService;
import com.tyt.statistic.service.TytMaintainerStatisticService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.DistanceUtil;
import com.tyt.util.ImageUtil;
import com.tyt.util.TimeUtil;

@Service("maintainerService")
public class MaintainerServiceImpl extends BaseServiceImpl<TytMechantService, Long> implements MaintainerService {

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "tytMaintainerStatisticServiceImpl")
	private TytMaintainerStatisticService tytMaintainerStatisticService;

	@Resource(name = "userService")
	UserService userService;

	private DecimalFormat df = new DecimalFormat("0.0000");

	@Resource(name = "maintainerDao")
	public void setBaseDao(BaseDao<TytMechantService, Long> maintainerDao) {
		super.setBaseDao(maintainerDao);
	}

	@SuppressWarnings("deprecation")
	@Override
	public MaintainerQueryResultBean query(String type, String deviceType, String lable, String status, String latitude, String longitude, String currentPage) {
		MaintainerQueryResultBean resultBean = new MaintainerQueryResultBean();
		/*
		 * 计算从第几条开始查询到第几条
		 */
		Integer searchSize = tytConfigService.getIntValue("merchantServicePageSize");
		if (searchSize == null || searchSize.intValue() <= 0) {
			searchSize = 15;
		}
		int startPos = (Integer.valueOf(currentPage) - 1) * searchSize + 1;
		int endPos = Integer.valueOf(currentPage) * searchSize;
		List<TytMechantServiceBean> returnList = new ArrayList<TytMechantServiceBean>();
		StringBuffer count = new StringBuffer("SELECT count(*)  FROM tyt_mechant_service tms ");
		StringBuffer sb = new StringBuffer(" WHERE tms.enabled>=1 ");
		List<Object> list = new ArrayList<Object>();
		// 维修师最近在线小时数
		Integer maintainerLastOnlineHour = tytConfigService.getIntValue("maintainerLastOnlineHour");
		if (maintainerLastOnlineHour == null) {
			maintainerLastOnlineHour = -1;
		}
		if (maintainerLastOnlineHour > 0) {
			sb.append(" AND tms.last_online_time>=?");
			list.add(TimeUtil.formatDateTime(TimeUtil.dateDiffHour(0 - maintainerLastOnlineHour)));
		}
		// 获取商户附近的查询距离
		Integer distance = tytConfigService.getIntValue("merchantServiceNeighborRange");
		if (distance == null) {
			distance = 100;
		}
		/*
		 * 根据经纬度和要查询的范围获取矩形顶点的经纬度坐标
		 */
		double[] rectanglePoints = DistanceUtil.getRectanglePointsByPos(Double.valueOf(longitude), Double.valueOf(latitude), distance);
		int leftLatitude = (int) (rectanglePoints[0] * 10000);
		int leftLongitude = (int) (rectanglePoints[1] * 10000);
		int rightLatitude = (int) (rectanglePoints[2] * 10000);
		int rightLongitude = (int) (rectanglePoints[3] * 10000);
		sb.append(" AND tms.`longitude` >= ?");
		list.add(leftLongitude);
		sb.append(" AND tms.`longitude` <= ?");
		list.add(rightLongitude);
		sb.append(" AND tms.`latitude` >= ?");
		list.add(rightLatitude);
		sb.append(" AND tms.`latitude` <= ?");
		list.add(leftLatitude);
		/*
		 * 由于客户端过滤是先选择机种后选择技能，deviceType代表机种，lable代表技能，lable不为空则deviceType
		 * 一定不为空，但是需要注意，实际技能为父，机种为子
		 */
		if (lable != null && !"".equals(lable.trim())) {
			sb.append(" AND EXISTS (SELECT * FROM tyt_merchant_service_technical tmst WHERE tmst.`service_id`=tms.`id` AND tmst.`lable` LIKE ?) AND EXISTS (SELECT * FROM tyt_merchant_service_technical tmst WHERE tmst.`service_id`=tms.`id` AND tmst.`device_type`=?)");
			list.add("%#" + deviceType + "#%");
			list.add(lable);
		} else {
			if (deviceType != null && !"".equals(deviceType.trim())) {
				sb.append(" AND EXISTS (SELECT * FROM tyt_merchant_service_technical tmst WHERE tmst.`service_id`=tms.`id` AND tmst.`lable` LIKE ?)");
				list.add("%#" + deviceType + "#%");
			}
		}
		if (status != null && !"".equals(status.trim())) {
			sb.append(" AND tms.`status`=?");
			list.add(status);
		}
		count.append(sb);
		BigInteger total = this.getBaseDao().query(count.toString(), list.toArray());
		/*
		 * 总数大于0才去查询数据
		 */
		if (total == null || total.intValue() < 1) {
			resultBean.setMaxPage(0);
			resultBean.setPageSize(searchSize);
			resultBean.setTotalRecord(0);
		} else {
			/*
			 * 查询数据
			 */
			int totalRecord = total.intValue();
			int pageNumber = (totalRecord + searchSize - 1) / searchSize;
			resultBean.setMaxPage(pageNumber);
			resultBean.setPageSize(searchSize);
			resultBean.setTotalRecord(totalRecord);
			resultBean.setCurrentPage(Integer.valueOf(currentPage));
			if (Integer.valueOf(currentPage).intValue() <= pageNumber) {
				StringBuffer select = new StringBuffer("SELECT tms.`id`, tms.`head_image_url` AS headImageUrl, tms.`name`, tms.`tele_phones` AS telePhones, tms.`status`, tms.longitude, tms.`latitude`, tms.`enabled` FROM tyt_mechant_service tms ");
				select.append(sb);
				Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
				scalarMap.put("id", Hibernate.INTEGER);
				scalarMap.put("headImageUrl", Hibernate.STRING);
				scalarMap.put("name", Hibernate.STRING);
				scalarMap.put("telePhones", Hibernate.STRING);
				scalarMap.put("status", Hibernate.INTEGER);
				scalarMap.put("longitude", Hibernate.STRING);
				scalarMap.put("latitude", Hibernate.STRING);
				scalarMap.put("enabled", Hibernate.STRING);
				// 查询所有在矩形区域内的数据
				returnList = this.getBaseDao().search(select.toString(), scalarMap, TytMechantServiceBean.class, list.toArray());
				if (returnList.size() > 0) {
					Iterator<TytMechantServiceBean> iterator = returnList.iterator();
					TytMechantServiceBean serviceBean = null;
					double dis;
					scalarMap.clear();
					scalarMap.put("id", Hibernate.INTEGER);
					scalarMap.put("deviceType", Hibernate.INTEGER);
					scalarMap.put("lable", Hibernate.STRING);
					scalarMap.put("deviceTypeName", Hibernate.STRING);
					scalarMap.put("lableNames", Hibernate.STRING);
					scalarMap.put("serviceId", Hibernate.INTEGER);
					// 保存有效距离内的维修师id，用于查询维修师的标签信息
					List<Integer> serviceIds = new ArrayList<Integer>();
					while (iterator.hasNext()) {
						serviceBean = iterator.next();
						/*
						 * 过滤并设置距离
						 */
						String lati = df.format(Float.valueOf(serviceBean.getLatitude()) / 10000);
						String longit = df.format(Float.valueOf(serviceBean.getLongitude()) / 10000);
						dis = DistanceUtil.distance(Double.valueOf(longitude), Double.valueOf(latitude), Double.valueOf(longit), Double.valueOf(lati));
						if (dis > distance * 1000) {
							iterator.remove();
						} else {
							// 设置距离
							serviceBean.setDistance(dis + "");
							serviceIds.add(serviceBean.getId());
						}
					}
					/*
					 * 所有的数据按照距离排序从近到远排序
					 */
					Collections.sort(returnList, new Comparator<TytMechantServiceBean>() {
						@Override
						public int compare(TytMechantServiceBean o1, TytMechantServiceBean o2) {
							return (int) (Double.valueOf(o1.getDistance()) - Double.valueOf(o2.getDistance()));
						}
					});
					/*
					 * type 0 分页 1 所有
					 */
					if ("0".equals(type)) {
						// 根据当前查询的数据位置过滤数据
						returnList = filterDataByPos(returnList, startPos, endPos);
					}
					// 获取维修师的技能标签信息
					returnList = fetchDeviceLables(returnList, scalarMap, df, serviceIds);
					Iterator<TytMechantServiceBean> itera = returnList.iterator();
					while (itera.hasNext()) {
						itera.next().setLastDayUnit(null);
					}
				}
			}
			resultBean.setData(returnList);
		}
		return resultBean;
	}

	/**
	 * 获取维修师的技能标签信息
	 * 
	 * @param returnList
	 * @param scalarMap
	 * @param df
	 * @param serviceIds
	 * @return
	 */
	private List<TytMechantServiceBean> fetchDeviceLables(List<TytMechantServiceBean> returnList, Map<String, org.hibernate.type.Type> scalarMap, DecimalFormat df, List<Integer> serviceIds) {
		if (returnList.isEmpty()) {
			return returnList;
		}
		Iterator<TytMechantServiceBean> iterator;
		TytMechantServiceBean serviceBean;
		String sql = "SELECT tmst.`id`, tmst.`device_type` AS 'deviceType', " + "tmst.`service_id` AS 'serviceId', tmst.`lable`," + "tmst.`device_type_name` AS `deviceTypeName`,tmst.`lable_names` AS `lableNames` " + "FROM tyt_merchant_service_technical tmst WHERE tmst.`service_id` in(:serviceIds)";
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("serviceIds", serviceIds);
		/*
		 * 根据维修师id集合查询所有的标签信息
		 */
		List<DeviceLabels> lablesList = this.getBaseDao().search(sql, scalarMap, DeviceLabels.class, paramsMap);
		// 保存维修师id和技能标签，便于设置每个维修师的技能标签
		Map<String, List<DeviceLabels>> serviceLableMap = new HashMap<String, List<DeviceLabels>>();
		if (lablesList.size() > 0) {
			Iterator<DeviceLabels> iter = lablesList.iterator();
			DeviceLabels devLables;
			while (iter.hasNext()) {
				devLables = iter.next();
				String serviceId = devLables.getServiceId() + "";
				if (serviceLableMap.containsKey(serviceId)) {
					serviceLableMap.get(serviceId).add(devLables);
				} else {
					lablesList = new ArrayList<DeviceLabels>();
					lablesList.add(devLables);
					serviceLableMap.put(serviceId, lablesList);
				}
			}
		}
		iterator = returnList.iterator();
		while (iterator.hasNext()) {
			serviceBean = iterator.next();
			serviceBean.setDeviceLables(serviceLableMap.get(serviceBean.getId() + ""));
			serviceBean.setLongitude(df.format(Float.valueOf(serviceBean.getLongitude()) / 10000));
			serviceBean.setLatitude(df.format(Float.valueOf(serviceBean.getLatitude()) / 10000));
		}
		return returnList;
	}

	private List<TytMechantServiceBean> filterDataByPos(List<TytMechantServiceBean> returnList, int startPos, int endPos) {
		if (returnList.size() > 0) {
			returnList = returnList.subList(--startPos, (endPos > returnList.size()) ? returnList.size() : endPos);
		}
		return returnList;
	}

	@SuppressWarnings("deprecation")
	@Override
	public TytMechantServiceBean getServiceById(String userId, long serviceId) {
		String sql = "SELECT tms.`id`, tms.`call_count` AS 'callCount', tms.`read_count` AS 'readCount', tms.`last_online_time` AS 'lastOnlineDate', tms.`status`, tms.`head_image_url` AS 'headImageUrl', tms.`name`, tms.`tele_phones` AS 'telePhones', tms.`longitude`, tms.`latitude`,tms.`remark`,tms.`enabled` FROM tyt_mechant_service tms WHERE tms.`id`=:id";
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("id", serviceId);
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.INTEGER);
		scalarMap.put("callCount", Hibernate.INTEGER);
		scalarMap.put("readCount", Hibernate.INTEGER);
		scalarMap.put("lastOnlineDate", Hibernate.STRING);
		scalarMap.put("status", Hibernate.INTEGER);
		scalarMap.put("headImageUrl", Hibernate.STRING);
		scalarMap.put("name", Hibernate.STRING);
		scalarMap.put("telePhones", Hibernate.STRING);
		scalarMap.put("longitude", Hibernate.STRING);
		scalarMap.put("latitude", Hibernate.STRING);
		scalarMap.put("remark", Hibernate.STRING);
		scalarMap.put("enabled", Hibernate.STRING);
		List<TytMechantServiceBean> serviceBeans = this.getBaseDao().search(sql, scalarMap, TytMechantServiceBean.class, paramsMap);
		TytMechantServiceBean serviceBean = serviceBeans.size() == 1 ? serviceBeans.get(0) : null;
		if (serviceBean != null) {
			serviceBean.setLongitude(df.format(Float.valueOf(serviceBean.getLongitude()) / 10000));
			serviceBean.setLatitude(df.format(Float.valueOf(serviceBean.getLatitude()) / 10000));
		}
		/*
		 * 获取标签信息
		 */
		sql = "SELECT tmst.`id`, tmst.`device_type` AS 'deviceType', " + "tmst.`service_id` AS 'serviceId', tmst.`lable`," + "tmst.`device_type_name` AS `deviceTypeName`,tmst.`lable_names` AS `lableNames` " + "FROM tyt_merchant_service_technical tmst WHERE tmst.`service_id`=:id";
		scalarMap.clear();
		scalarMap.put("id", Hibernate.INTEGER);
		scalarMap.put("deviceType", Hibernate.INTEGER);
		scalarMap.put("lable", Hibernate.STRING);
		scalarMap.put("serviceId", Hibernate.INTEGER);
		scalarMap.put("lableNames", Hibernate.STRING);
		scalarMap.put("deviceTypeName", Hibernate.STRING);
		List<DeviceLabels> lablesList = this.getBaseDao().search(sql, scalarMap, DeviceLabels.class, paramsMap);
		serviceBean.setDeviceLables(lablesList);
		return serviceBean;
	}

	@SuppressWarnings("deprecation")
	@Override
	public TytMechantServiceBean queryMyInfo(String userId) {
		String sql = "SELECT tms.`id`, tms.`call_count` AS 'callCount', tms.`read_count` AS 'readCount', tms.`last_online_time` AS 'lastOnlineDate', tms.`status`, tms.`head_image_url` AS 'headImageUrl', tms.`name`, tms.`tele_phones` AS 'telePhones', tms.`home_page_display` AS 'homePageDisplay',tms.`remark`,tms.`enabled` FROM tyt_mechant_service tms WHERE tms.`user_id`=:userId";
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("userId", userId);
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.INTEGER);
		scalarMap.put("callCount", Hibernate.INTEGER);
		scalarMap.put("readCount", Hibernate.INTEGER);
		scalarMap.put("lastOnlineDate", Hibernate.STRING);
		scalarMap.put("status", Hibernate.INTEGER);
		scalarMap.put("headImageUrl", Hibernate.STRING);
		scalarMap.put("name", Hibernate.STRING);
		scalarMap.put("telePhones", Hibernate.STRING);
		scalarMap.put("homePageDisplay", Hibernate.INTEGER);
		scalarMap.put("remark", Hibernate.STRING);
		scalarMap.put("enabled", Hibernate.STRING);
		List<TytMechantServiceBean> serviceBeans = this.getBaseDao().search(sql, scalarMap, TytMechantServiceBean.class, paramsMap);
		TytMechantServiceBean serviceBean = serviceBeans.size() == 1 ? serviceBeans.get(0) : null;
		if (serviceBean != null) {
			paramsMap.clear();
			scalarMap.clear();
			paramsMap.put("serviceId", serviceBean.getId());
			scalarMap.put("id", Hibernate.INTEGER);
			scalarMap.put("deviceType", Hibernate.INTEGER);
			scalarMap.put("lable", Hibernate.STRING);
			/*
			 * 获取标签数据
			 */
			sql = "SELECT tmst.`id`, tmst.`device_type` AS 'deviceType', tmst.`lable` FROM tyt_merchant_service_technical tmst WHERE tmst.`service_id` = :serviceId";
			List<DeviceLabels> lablesList = this.getBaseDao().search(sql, scalarMap, DeviceLabels.class, paramsMap);
			serviceBean.setDeviceLables(lablesList);
		}
		return serviceBean;
	}

	@Override
	public boolean isUserHasAddMaintainer(String userId) {
		String sql = "SELECT COUNT(*) FROM tyt_mechant_service tms WHERE tms.`user_id`=?";
		BigInteger count = this.getBaseDao().query(sql, new Object[] { userId });
		return count.intValue() == 1;
	}

	@Override
	public TytMechantService saveData(TytMechantServiceSaveBean serviceSaveBean, String imageName) {
		TytMechantService mechantService = new TytMechantService();
		mechantService.setHeadImageUrl(imageName);
		mechantService.setLongitude(new BigDecimal(serviceSaveBean.getLongitude()).movePointRight(4).intValue());
		mechantService.setLatitude((new BigDecimal(serviceSaveBean.getLatitude()).movePointRight(4).intValue()));
		mechantService.setName(serviceSaveBean.getName());
		mechantService.setTelePhones(serviceSaveBean.getTelePhones());
		mechantService.setCellPhone(serviceSaveBean.getCellPhone());
		mechantService.setUserId(Long.valueOf(serviceSaveBean.getUserId()));
		// 默认被拨打次数,被浏览次数为0,状态1 接活中,首页显示1
		mechantService.setCallCount(0);
		mechantService.setReadCount(0);
		mechantService.setStatus(1);
		mechantService.setHomePageDisplay(1);
		mechantService.setCreateTime(new Date());
		mechantService.setUpdateTime(new Date());
		mechantService.setLastOnlineTime(new Date());
		mechantService.setEnabled("1");// 默认正常
		mechantService.setClientSign(serviceSaveBean.getClientSign());
		mechantService.setClientVersion(serviceSaveBean.getClientVersion());
		mechantService.setRemark(serviceSaveBean.getRemark());
		Long serviceId = (Long) this.getBaseDao().insertWithReturnId(mechantService);
		// 便签处理
		String[] devicesLables = serviceSaveBean.getDeviceLables().split(";");
		String[] lables;
		String[] devicesLableName;
		String[] lableNames;
		String insertSql = "INSERT INTO tyt_merchant_service_technical (service_id, device_type,device_type_name, lable,lable_names) VALUE(?, ?, ?, ?, ?)";
		String[] params = new String[5];
		if (devicesLables.length > 0) {
			for (String deviceLable : devicesLables) {
				lables = deviceLable.split("/");
				params[0] = serviceId + "";

				devicesLableName = lables[0].split(":");
				params[1] = devicesLableName[0];
				params[2] = devicesLableName[1];

				lableNames = lables[1].split(":");
				params[3] = lableNames[0];
				params[4] = lableNames[1];

				this.getBaseDao().executeUpdateSql(insertSql, params);
			}
		}
		return mechantService;
	}

	@Override
	public void updateData(TytMechantServiceUpdateBean serviceUpdateBean, String imageName) {
		List<Object> paramsList = new ArrayList<Object>();
		paramsList.add(imageName);
		paramsList.add(serviceUpdateBean.getName());
		paramsList.add(serviceUpdateBean.getTelePhones());
		paramsList.add(new BigDecimal(serviceUpdateBean.getLongitude()).movePointRight(4).intValue());
		paramsList.add((new BigDecimal(serviceUpdateBean.getLatitude()).movePointRight(4).intValue()));
		paramsList.add(Long.valueOf(serviceUpdateBean.getUserId()));
		paramsList.add(serviceUpdateBean.getRemark());
		paramsList.add(serviceUpdateBean.getId());
		String updateSql = "UPDATE tyt_mechant_service tms SET head_image_url=?, `name`=?, tele_phones=?, longitude=?, latitude=?, user_id=?,remark=? WHERE id=?";
		// 更新
		this.getBaseDao().executeUpdateSql(updateSql, paramsList.toArray());
		/*
		 * 便签处理
		 */
		paramsList.clear();
		String serviceId = serviceUpdateBean.getId();
		paramsList.add(serviceUpdateBean.getId());
		updateSql = "DELETE FROM tyt_merchant_service_technical WHERE service_id=?";
		// 删除标签
		this.getBaseDao().executeUpdateSql(updateSql, paramsList.toArray());
		// 便签处理
		String[] devicesLables = serviceUpdateBean.getDeviceLables().split(";");
		String[] lables;
		String[] devicesLableName;
		String[] lableNames;
		String insertSql = "INSERT INTO tyt_merchant_service_technical (service_id, device_type,device_type_name, lable,lable_names) VALUE(?, ?, ?, ?, ?)";
		String[] params = new String[5];
		if (devicesLables.length > 0) {
			for (String deviceLable : devicesLables) {
				lables = deviceLable.split("/");
				params[0] = serviceId + "";

				devicesLableName = lables[0].split(":");
				params[1] = devicesLableName[0];
				params[2] = devicesLableName[1];

				lableNames = lables[1].split(":");
				params[3] = lableNames[0];
				params[4] = lableNames[1];

				this.getBaseDao().executeUpdateSql(insertSql, params);
			}
		}

	}

	@Override
	public void updateStatus(String userId, String id, String type, String status) {
		String[] params = new String[] { status, id };
		params[0] = status;
		params[1] = id;
		String sql = null;
		// 类型 1接活状态标识 2首页显示标识
		switch (Integer.valueOf(type)) {
		case 1:
			sql = "UPDATE tyt_mechant_service tms SET tms.`status` = ? WHERE tms.`id` = ?";
			// 保存维修师接活状态改变信息到统计表
			tytMaintainerStatisticService.addActivityChange(id, userId, status);
			break;
		case 2:
			sql = "UPDATE tyt_mechant_service tms SET tms.`home_page_display` = ? WHERE tms.`id` = ?";
			break;
		}
		this.getBaseDao().executeUpdateSql(sql, params);
	}

	/**
	 * @description 更新维修师显示状态
	 * <AUTHOR>
	 * @date 2021/12/25 19:01
	 * @param userId
	 * @param status
	 * @return int
	 */
	@Override
	public int updateStatusByUserId(Long userId, Integer status) throws Exception{
		String sql = "UPDATE tyt_mechant_service tms SET tms.`home_page_display` = ? WHERE tms.`user_id` = ?";
		int result = this.getBaseDao().executeUpdateSql(sql, new Object[]{status, userId});
		return result;
	}

	@Override
	public void updateRecentInfo(Long userId, String longitude, String latitude, String provice, String city, String county, Date lastOnlineTime) {
		String updateSQL = "UPDATE tyt_mechant_service m " + "SET m.`longitude`=?,m.`latitude`=?,m.`province`=?,m.`city`=?,m.`county`=?," + "m.`last_online_time`=? WHERE m.`user_id`=?";

		int lati = new BigDecimal(latitude).movePointRight(4).intValue();
		int longi = new BigDecimal(longitude).movePointRight(4).intValue();

		this.getBaseDao().executeUpdateSql(updateSQL, new Object[] { longi, lati, provice, city, county, lastOnlineTime, userId });
	}

	@Override
	public void updateCalledCount(Long id) {
		String updateSQL = "UPDATE tyt_mechant_service m SET m.`call_count`=m.`call_count`+1 WHERE m.`id`=?";
		this.getBaseDao().executeUpdateSql(updateSQL, new Object[] { id });
	}

	@Override
	public void updateReadedCount(Long id) {
		String updateSQL = "UPDATE tyt_mechant_service m SET m.`read_count`=m.`read_count`+1 WHERE m.`id`=?";
		this.getBaseDao().executeUpdateSql(updateSQL, new Object[] { id });

	}

	@Override
	public Integer getByCellPhone(String telePhone) {
		String countSQL = "SELECT COUNT(*) FROM tyt_mechant_service m WHERE m.`tele_phones` LIKE ?";
		BigInteger c = this.getBaseDao().query(countSQL, new Object[] { "%"+telePhone+"%" });
		if (c != null) {
			return c.intValue();
		}
		return null;
	}

	@Override
	public void saveHeadImageUrl(String userId, String headImageURL) {
		String updateSQL = "UPDATE tyt_mechant_service tms SET tms.head_image_url=? WHERE tms.user_id=?";
		this.getBaseDao().executeUpdateSql(updateSQL, new Object[] { headImageURL, userId });
	}

	@Override
	public String saveHeadImage(MultipartFile headImage, String userId) throws Exception {
		String imageName = null;
		// 保存头像文件
		if (headImage != null && !headImage.isEmpty()) {
			// 修改维修师表的头像
			imageName = ImageUtil.renamePic(headImage, "head");
			headImage.transferTo(new File(AppConfig.getProperty("picture.path.domain") + imageName));
			// 修改个人中心的头像地址
			userService.saveHead(imageName, Long.valueOf(userId));
		} else {
			// 查询个人中心的头像，取出其地址赋给维修师
			String headURL = userService.getHeadURL(userId);
			if (headURL != null && !headURL.trim().equals("")) {
				imageName = headURL;
			}
		}
		return imageName;
	}
}
