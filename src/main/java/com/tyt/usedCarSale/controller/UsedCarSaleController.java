package com.tyt.usedCarSale.controller;

import cn.hutool.core.bean.BeanUtil;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.file.bean.FileInfo;
import com.tyt.file.service.FileInfoService;
import com.tyt.model.*;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.paramvalidator.BeanValidator;
import com.tyt.usedCarBrand.service.TytUsedCarBrandService;
import com.tyt.usedCarSale.bean.*;
import com.tyt.usedCarSale.service.TytUsedCarSaleService;
import com.tyt.usedCarWhiteList.service.TytUsedCarWhitelistService;
import com.tyt.user.querybean.SourceBean;
import com.tyt.user.service.TytSourceService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/used/car/sale/")
public class UsedCarSaleController extends BaseController {

    @Autowired
    private TytUsedCarSaleService usedCarSaleService;
    @Autowired
    private TytUserIdentityAuthService userIdentityAuthService;
    @Autowired
    private TytNoticePopupTemplService noticePopupTemplService;
    @Autowired
    private TytSourceService tytSourceService;
    @Autowired
    private TytUsedCarBrandService usedCarBrandService;
    @Autowired
    private TytUsedCarWhitelistService usedCarWhitelistService;
    @Autowired
    private UserService userService;
    @Autowired
    private FileInfoService fileInfoService;

    /**
     * 二手车销售列表
     * @param infoType 信息类型 1.车头 2.挂车 3.一主一挂 0.全部
     * @param searchBean 查询项
     * @param queryTime 查询最小时间
     * @param queryActionType 查询类型
     * @return rm
     */
    @RequestMapping(value = {"getList.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getUsedCarList(UsedCarSearchBean searchBean,
                                        @RequestParam(name="infoType",defaultValue = "0") Integer infoType,
                                        String queryTime,
                                        @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功!");
        try {
            Long userId = searchBean.getUserId();
            if(userId == null) {
                resultMsgBean.setMsg("登录用户Id不能为空！");
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                return resultMsgBean;
            }
            if (checkQueryParameter(queryActionType,queryTime,resultMsgBean)){
                List<TytUsedCarSale> usedCarList = usedCarSaleService.getUsedCarList(infoType, searchBean,queryTime,queryActionType);
                resultMsgBean.setData(usedCarList);
            }
        }catch (Exception e){
            e.printStackTrace();
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("getUsedCarList 获取二手车销售列表发生错误！"+e.getMessage());
        }
        return resultMsgBean;
    }

    /**
     * 获取二手车详情
     * @param id //id
     * @return rm
     */
    @RequestMapping(value = {"getDetail.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getUsedCarDetail(BaseParameter baseParameter,@RequestParam(value = "id", required = true) Long id){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功!");
        try {
            /*Long userId = baseParameter.getUserId();
            if(userId == null) {
                resultMsgBean.setMsg("登录用户Id不能为空！");
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                return resultMsgBean;
            }*/
            UsedCarDetailBean usedCarSale = usedCarSaleService.getByUsedCarById(id);
            resultMsgBean.setData(usedCarSale);
        }catch (Exception e){
            e.printStackTrace();
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("getUsedCarDetail 获取二手车销售详情发生错误！"+e.getMessage());
        }
        return resultMsgBean;
    }

    /**
     * 获取车辆类型
     * @return rm
     */
    @RequestMapping(value = {"getCarSource.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getCarSource(){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功!");
        try {
            List<String> groupCodes = new ArrayList<>();
            groupCodes.add("head_drive_style"); //驱动形式
            groupCodes.add("head_speed_ratio"); //速比
            groupCodes.add("discharge_standard");//排放标准
            groupCodes.add("tail_length"); //板车长度
            groupCodes.add("tail_used_car_type");//板车类型
            groupCodes.add("tail_kind");//板车种类
            groupCodes.add("tail_ladder_style");//爬梯类型
            groupCodes.add("tail_axle_brand");//车轴品牌
            List<TytSource> carTypeList = tytSourceService.getByGroupCodeNotContainChild(groupCodes);
            resultMsgBean.setData(carTypeList);
        }catch (Exception e){
            e.printStackTrace();
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }

    /**
     * 参数检验
     * @param queryActionType 查询类型
     * @param queryTime 查询时间
     * @param rm 返回
     * @return boolean
     */
    private boolean checkQueryParameter(Integer queryActionType, String queryTime, ResultMsgBean rm) {
        if (queryActionType < 1 || queryActionType > 2) {
            rm.setMsg("查询类型不正确！");
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            return false;
        }
        if (queryActionType == 2 && StringUtils.isBlank(queryTime)){
            rm.setMsg("查询时间不能为空！");
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            return false;
        }
        return true;
    }

    /**
     * @Description  1.发布、编辑车辆信息接口
     * <AUTHOR>
     * @Date  2020/1/8 19:56
     * @Param [carSaleBean]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"publishCarInfo","publishCarInfo.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean publishCarInfo(CarSaleBean carSaleBean) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            //1.根据ID是否存在,判断发布还是编辑,做初步的参数和逻辑校验
            //二手车车辆Id
            Long id = carSaleBean.getId();
            //发布状态 1.上架 2.下架 3.草稿
            Short postStatus = carSaleBean.getPostStatus();
            //如果发布状态为1.上架,则进行参数校验
            if(postStatus != null && postStatus.intValue() == 1){
                if(id == null){ //如果车辆Id为空，表明是发布车辆信息
                    String[] carImages = carSaleBean.getCarImages();
                    if(carImages == null){ //如果车辆照片信息为空
                        rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                        rm.setMsg("车辆图片不能为空");
                        return rm;
                    }
                    //车辆照片不能少于1张,不能超过11张
                    if(carImages.length < 1  || carImages.length > 11){
                        rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                        rm.setMsg("车辆图片不能少于1张,不能超过11张");
                        return rm;
                    }
                }else{  //如果车辆Id不为空，则表明是编辑车辆信息
                    TytUsedCarSale tytUsedCarSale = usedCarSaleService.getById(id);
                    if(tytUsedCarSale == null){ //如果车辆信息为空
                        rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                        rm.setMsg("编辑的车辆信息不存在");
                        return rm;
                    }
                    //发布状态 1.上架 2.下架 3.草稿
                    Short oldPostStatus = tytUsedCarSale.getPostStatus();
                    //审核状态 1.审核中 2.审核通过 3.审核失败
                    Short auditStatus = tytUsedCarSale.getAuditStatus();
                    if(oldPostStatus == 1 ){ //如果发布状态为发布中(上架)
                        if(auditStatus == 1){ //1.审核中
                            rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                            rm.setMsg("不允许编辑审核中的车辆信息");
                            return rm;
                        }else if(auditStatus == 2){ //2.审核通过
                            rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                            rm.setMsg("请先将该车辆下架后再进行编辑");
                            return rm;
                        }
                    }
                    //编辑时候新增的图片数组
                    String[] carImages = carSaleBean.getCarImages();
                    //编辑时候删除的图片ID数组
                    Long[] deleteImageIds = carSaleBean.getDeleteImageIds();
                    //原图片列表
                    List<FileInfo> fileInfoList = fileInfoService.getFileInfoList(id, 2);
                    //车辆图片的总数量
                    int carImagesCount = 0;
                    if(fileInfoList != null && fileInfoList.size() > 0){
                        carImagesCount += fileInfoList.size();
                    }
                    if(deleteImageIds != null && deleteImageIds.length > 0){
                        carImagesCount -=  deleteImageIds.length;
                    }
                    if(carImages != null &&  carImages.length > 0){
                        carImagesCount += carImages.length;
                    }
                    //车辆照片不能少于1张,不能超过11张
                    if(carImagesCount < 1  || carImagesCount > 11){
                        rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                        rm.setMsg("车辆图片不能少于1张,不能超过11张");
                        return rm;
                    }
                }
                //2.校验传入的参数值
                try {
                    //2.1先对所有的必填参数做一次参数校验，不区分车辆类型
                    BeanValidator.validate(carSaleBean);
                    //2.2再根据具体的车辆类型分别做不同的参数校验
                    //信息类型 1.仅车头 2.仅挂车 3.一主一挂
                    Short infoType = carSaleBean.getInfoType();
                    switch (infoType) {
                        case 1: //1.仅车头
                            CarHeadBean carHeadBean = new CarHeadBean();
                            //父类对象属性值拷贝给子类对象
                            BeanUtils.copyProperties(carSaleBean,carHeadBean);
                            //调用对象验证器对所有参数进行校验
                            BeanValidator.validate(carHeadBean);
                            break;
                        case 2: //2.仅挂车
                            CarTailBean carTailBean = new CarTailBean();
                            //父类对象属性值拷贝给子类对象
                            BeanUtils.copyProperties(carSaleBean,carTailBean);
                            //调用对象验证器对所有参数进行校验
                            BeanValidator.validate(carTailBean);
                            break;
                        case 3: //3.一主一挂
                            CarHeadAndTailBean carHeadAndTailBean = new CarHeadAndTailBean();
                            //父类对象属性值拷贝给子类对象
                            BeanUtil.copyProperties(carSaleBean,carHeadAndTailBean);
                            //调用对象验证器对所有参数进行校验
                            BeanValidator.validate(carHeadAndTailBean);
                            break;
                        default:
                            rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                            rm.setMsg("发布信息类型错误");
                            return rm;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.info("参数校验发生错误，错误信息为："+e.getMessage());
                    rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                    rm.setMsg(e.getMessage());
                    return rm;
                }
            }
            //3.发布、编辑车辆主体信息和图片信息
            Map <String, Object> map = usedCarSaleService.savePublishCarInfo(carSaleBean);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("发布或编辑车辆信息成功！");
            rm.setData(map);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("发布或编辑车辆信息失败！", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @Description  2.批量修改车辆发布状态接口 发布状态 1.上架 2.下架 3.草稿
     * <AUTHOR>
     * @Date  2020/1/9 11:01
     * @Param [baseParameter, ids, postStatus]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"updateCarStatus","updateCarStatus.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean updateCarStatus(BaseParameter baseParameter,Long[] ids,Integer postStatus,String dismountReason) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户Id不能为空！");
                return rm;
            }
            if(ids == null || ids.length == 0){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("车辆ID不能为空！");
                return rm;
            }
            if(postStatus == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("车辆状态不能为空！");
                return rm;
            }
            usedCarSaleService.updateCarStatus(userId,ids,postStatus,dismountReason);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("批量修改车辆发布状态成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("批量修改车辆发布状态失败！", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @Description  3.发布车辆权益验证接口
     *               是否是白名单用户、是否身份认证（弹权益弹框）
     * <AUTHOR>
     * @Date  2020/1/9 11:32
     * @Param [baseParameter]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"checkPublish","checkPublish.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean checkPublish(BaseParameter baseParameter) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户Id不能为空！");
                return rm;
            }
//            //判断用户是否在白名单 TODO:暂时删除白名单判断的逻辑，不限制用户发布车辆信息
//            TytUsedCarWhitelist tytUsedCarWhitelist = new TytUsedCarWhitelist();
//            tytUsedCarWhitelist.setUserId(userId);
//            tytUsedCarWhitelist.setStatus(1); //是否有效：1.是 2.否
//            TytUsedCarWhitelist usedCarWhitelist = usedCarWhitelistService.find(tytUsedCarWhitelist);
//            if(usedCarWhitelist == null){
//                rm.setCode(ReturnCodeConstant.NO_PERMISSION);
//                rm.setMsg("无法发布车辆信息，请联系客服！");
//                return rm;
//            }
            //判断用户是否身份认证
            TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(userId));
            if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
                //提醒认证弹框
                TytNoticePopupTempl noticePopupTempl;
                if (Constant.ClientSignNewEnum.isClientSignEnumcode(Integer.parseInt(baseParameter.getClientSign()))){
                    noticePopupTempl = noticePopupTemplService.getTemplByType(PopupTypeEnum.未实名认证, null);
                }else{
                    noticePopupTempl = noticePopupTemplService.getTemplByType(PopupTypeEnum.未认证身份, null);
                }
                rm.setNoticeData(noticePopupTempl);
                rm.setCode(ReturnCodeConstant.NO_PERMISSION);
                rm.setMsg("用户身份认证未通过！");
                return rm;
            }
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("发布车辆权益验证成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("发布车辆权益验证失败！", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @Description  4.模糊搜索车辆品牌列表接口
     *               可根据名称或拼音进行列表的搜索
     * <AUTHOR>
     * @Date  2020/1/21 12:27
     * @Param [brandName, brandType]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"getUsedCarBrandList","getUsedCarBrandList.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getUsedCarBrandList(String brandName, Integer brandType) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            List<TytUsedCarBrand> usedCarBrandList = usedCarBrandService.getUsedCarBrandList(brandName, brandType);
            rm.setData(usedCarBrandList);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("获取二手车车辆品牌列表成功！");
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("获取二手车车辆品牌列表失败！", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 我的发布
     * @param listType 1.发布中 2.已下架 3.已保存
     * @param userId 用户id
     * @return rm
     */
    @RequestMapping(value = {"getMyList.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getMyUsedCarList(
                                        @RequestParam(name="listType",defaultValue = "1") Integer listType,
                                        @RequestParam(name="userId",required = true) Long userId){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功!");
        try {
            List<TytUsedCarSale> myUsedCarList = usedCarSaleService.getMyUsedCarList(listType,userId);
            resultMsgBean.setData(myUsedCarList);
        }catch (Exception e){
            e.printStackTrace();
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("getUsedCarList 获取我的发布列表发生错误！"+e.getMessage());
        }
        return resultMsgBean;
    }

    /**
     * 获取发布人信息
     * @param userId
     * @return
     */
    @RequestMapping(value = {"getUserInfo.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getUserInfo(
            @RequestParam(name="userId",required = true) Long userId){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功!");
        try {
            User user = userService.getByUserId(userId);
            Map<String,Object> map = new HashMap<>();
            map.put("userName",user.getTrueName());
            map.put("cellPhone", user.getCellPhone());
            resultMsgBean.setData(map);
        }catch (Exception e){
            e.printStackTrace();
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("getUsedCarList 获取我的发布列表发生错误！"+e.getMessage());
        }
        return resultMsgBean;
    }
}
