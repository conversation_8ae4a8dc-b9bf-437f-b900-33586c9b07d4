package com.tyt.usedCarSale.bean;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CarHeadBean
 * @Description 二手车车头实体类
 * <AUTHOR>
 * @Date 2020-01-19 11:00
 * @Version 1.0
 */
@Data
public class CarHeadBean extends CarSaleBean {

    //出厂时间
    @NotNull(message = "请填写出厂时间后再做发布")
    private Date headFactoryTime;
    //上牌时间
    @NotNull(message = "请填写上牌时间后再做发布")
    private Date headCheckInTime;
    //车头品牌
    @NotBlank(message = "请填写车头品牌后再做发布")
    private String headBrand;
    //车头型号
    @NotBlank(message = "请填写车头型号后再做发布")
    private String headModel;
    //马力
    @NotNull(message = "请填写马力后再做发布")
    private BigDecimal headHorsepower;
    //驱动形式
    @NotBlank(message = "请填写驱动形式后再做发布")
    private String headDriveStyle;
    //排放标准
    @NotBlank(message = "请填写排放标准后再做发布")
    private String headDischargeStandard;
    //驾驶室
    @NotBlank(message = "请选择驾驶室后再做发布")
    private String headCab;
    //行车公里数(公里)
    @NotNull(message = "请填写行车公里数后再做发布")
    private BigDecimal headDriveKilometers;
    //是否有保险 1.是 2.否
    @NotNull(message = "请选择是否有保险后再做发布")
    private Short headInsurance;
    //车头车源所在地省
    @NotBlank(message = "请选择车头车源所在地省后再做发布")
    private String headSourceProvince;
    //车头车源所在地市
    @NotBlank(message = "请选择车头车源所在地市后再做发布")
    private String headSourceCity;
    //车头车源所在地区
    @NotBlank(message = "请选择车头车源所在地区后再做发布")
    private String headSourceArea;

}
