package com.tyt.usedCarSale.bean;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CarSaleBean
 * @Description 二手车信息实体类
 * <AUTHOR>
 * @Date 2020-01-08 19:38
 * @Version 1.0
 */
@Data
public class CarSaleBean {

    //信息类型 1.仅车头 2.仅挂车 3.一主一挂
    @NotNull(message = "信息类型不能为空")
    private Short infoType;
    //id
    private Long id;
    //发布人id
    @NotNull(message = "请填写'发布人id'后再做发布")
    private Long userId;
    //发布人姓名
    private String userName;
    //售车人姓名
    @NotBlank(message = "请填写'售车人姓名'后再做发布")
    @Size(min = 1,max = 40,message = "售车人姓名不能超出40个汉字")
    private String saleName;
    //售车人手机号
    @NotBlank(message = "请填写'售车人手机号'后再做发布")
    @Pattern(regexp = "[1]{1}[0-9]{10}$",message = "发布人手机号格式不合法")
    private String saleCellPhone;

    //车头标题
    private String headTitle;
    //出厂时间
    private Date headFactoryTime;
    //上牌时间
    private Date headCheckInTime;
    //车头品牌
    private String headBrand;
    //车头型号
    private String headModel;
    //马力
    private BigDecimal headHorsepower;
    //驱动形式
    private String headDriveStyle;
    //速比
    private String headSpeedRatio;
    //排放标准
    private String headDischargeStandard;
    //驾驶室
    private String headCab;
    //行车公里数(公里)
    private BigDecimal headDriveKilometers;
    //是否有保险 1.是 2.否
    private Short headInsurance;
    //车头车源所在地省
    private String headSourceProvince;
    //车头车源所在地市
    private String headSourceCity;
    //车头车源所在地区
    private String headSourceArea;
    //车头车源所在地详细地址
    private String headSourceAddress;
    //车头车源所在地全地址
    private String headSourceFullAddress;

    //板车标题
    private String tailTitle;
    //板车品牌
    private String tailBrand;
    //板车上牌日期
    private Date tailCheckInTime;
    //板车上牌时间-板车车龄（年）
    private BigDecimal tailAge;
    //板车长度（米）
    private BigDecimal tailLength;
    //板车类型 （tyt_source表tail_car_type）
    private String tailType;
    //板车种类
    private String tailKind;
    //平台长度(米)
    private BigDecimal tailPlatformLength;
    //货台面离地高度(厘米)
    private BigDecimal tailMesaToGround;
    //板车自重(吨)
    private BigDecimal tailDeadWeight;
    //保货吨位(吨)
    private BigDecimal tailLoad;
    //爬梯样式
    private String tailLadderStyle;
    //车轴品牌
    private String tailAxleBrand;
    //是否带抽拉 1.是 2.否
    private Short tailHaveDrawing;
    //板车车源所在地省
    private String tailSourceProvince;
    //板车车源所在地市
    private String tailSourceCity;
    //板车车源所在地区
    private String tailSourceArea;
    //板车车头车源所在地详细地址
    private String tailSourceAddress;
    //板车车头车源所在地全地址
    private String tailSourceFullAddress;

    //是否可拆开售卖 1.是 2.否
    private Short isBreakSale;
    //车头是否已售卖 1.是 2.否
    private Short headIsSale;
    //挂车是否已售卖 1.是 2.否
    private Short tailIsSale;
    //车头售卖时间
    private Date headSaleTime;
    //挂车售卖时间
    private Date tailSaleTime;

    //列表展示图缩略图地址
    private String firstPicUrl;
    //车辆图片集合,Base64编码的数组
    private String[] carImages;
    //要删除的图片的Id集合
    private Long[] deleteImageIds;

    //发布状态 1.上架 2.下架 3.草稿
    @NotNull(message = "发布状态不能为空")
    private Short postStatus;
    //审核状态 1.审核中 2.审核通过 3.审核失败
    private Short auditStatus;
    //信息来源 1.app提交 2.后台提交
    @NotNull(message = "信息来源不能为空")
    private Short infoSource;

    //后台发布人id
    private Long createId;
    //后台发布人姓名
    private String createName;
    //审核人id
    private Long auditId;
    //审核人姓名
    private String auditName;
    //审核时间
    private Date auditTime;
    //审核失败原因
    private String auditFail;

    //创建时间（提交时间）
    private Date ctime;
    //修改时间
    private Date utime;
    //备注
    private String remark;
    //发布时间
    private Date publishTime;
}
