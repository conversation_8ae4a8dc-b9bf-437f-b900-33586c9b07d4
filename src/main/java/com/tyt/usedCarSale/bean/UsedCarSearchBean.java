package com.tyt.usedCarSale.bean;

import com.tyt.base.bean.BaseParameter;

public class UsedCarSearchBean extends BaseParameter {

    private static final long serialVersionUID = 2833651191646664406L;
    private String headBrand; //车头品牌
    private Integer headHorsepowerMin; //马力最小
    private Integer headHorsepowerMax; //马力最大
    private Integer timeMin; //年限（最小）
    private Integer timeMax; //年限 （最大）
    private String headProvince; //省
    private String headCity;  //市
    private String headArea;  //县

    private String tailType;  //车型
    private String tailProvince; //省
    private String tailCity;  //市
    private String tailArea;  //县
    private String tailLength;//板车长度
    private String tailBrand; //板车品牌

    public String getHeadBrand() {
        return headBrand;
    }

    public void setHeadBrand(String headBrand) {
        this.headBrand = headBrand;
    }

    public Integer getHeadHorsepowerMin() {
        return headHorsepowerMin;
    }

    public void setHeadHorsepowerMin(Integer headHorsepowerMin) {
        this.headHorsepowerMin = headHorsepowerMin;
    }

    public Integer getHeadHorsepowerMax() {
        return headHorsepowerMax;
    }

    public void setHeadHorsepowerMax(Integer headHorsepowerMax) {
        this.headHorsepowerMax = headHorsepowerMax;
    }

    public Integer getTimeMin() {
        return timeMin;
    }

    public void setTimeMin(Integer timeMin) {
        this.timeMin = timeMin;
    }

    public Integer getTimeMax() {
        return timeMax;
    }

    public void setTimeMax(Integer timeMax) {
        this.timeMax = timeMax;
    }

    public String getHeadProvince() {
        return headProvince;
    }

    public void setHeadProvince(String headProvince) {
        this.headProvince = headProvince;
    }

    public String getHeadCity() {
        return headCity;
    }

    public void setHeadCity(String headCity) {
        this.headCity = headCity;
    }

    public String getHeadArea() {
        return headArea;
    }

    public void setHeadArea(String headArea) {
        this.headArea = headArea;
    }

    public String getTailType() {
        return tailType;
    }

    public void setTailType(String tailType) {
        this.tailType = tailType;
    }

    public String getTailProvince() {
        return tailProvince;
    }

    public void setTailProvince(String tailProvince) {
        this.tailProvince = tailProvince;
    }

    public String getTailCity() {
        return tailCity;
    }

    public void setTailCity(String tailCity) {
        this.tailCity = tailCity;
    }

    public String getTailArea() {
        return tailArea;
    }

    public void setTailArea(String tailArea) {
        this.tailArea = tailArea;
    }

    public String getTailLength() {
        return tailLength;
    }

    public void setTailLength(String tailLength) {
        this.tailLength = tailLength;
    }

    public String getTailBrand() {
        return tailBrand;
    }

    public void setTailBrand(String tailBrand) {
        this.tailBrand = tailBrand;
    }
}
