package com.tyt.usedCarSale.bean;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CarTailBean
 * @Description 二手车挂车实体类
 * <AUTHOR>
 * @Date 2020-01-19 11:01
 * @Version 1.0
 */
@Data
public class CarTailBean extends CarSaleBean{

    //板车品牌
    @NotBlank(message = "请填写板车品牌后再做发布")
    private String tailBrand;
    //板车上牌时间-板车车龄（年）
    @NotNull(message = "请填写板车上牌时间后再做发布")
    private BigDecimal tailAge;
    //板车长度（米）
    @NotNull(message = "请填写板车长度后再做发布")
    private BigDecimal tailLength;
    //板车类型 （tyt_source表tail_car_type）
    @NotBlank(message = "请填写板车类型后再做发布")
    private String tailType;
    //板车种类
    @NotBlank(message = "请填写板车种类后再做发布")
    private String tailKind;
    //平台长度(米)
    @NotNull(message = "请填写平台长度后再做发布")
    private BigDecimal tailPlatformLength;
    //货台面离地高度(厘米)
    @NotNull(message = "请填写货台面离地高度后再做发布")
    private BigDecimal tailMesaToGround;
    //板车自重(吨)
    @NotNull(message = "请填写板车自重后再做发布")
    private BigDecimal tailDeadWeight;
    //保货吨位(吨)
    @NotNull(message = "请填写保货吨位后再做发布")
    private BigDecimal tailLoad;
    //爬梯样式
    @NotBlank(message = "请填写爬梯样式后再做发布")
    private String tailLadderStyle;
    //是否带抽拉 1.是 2.否
    @NotNull(message = "请选择是否带抽拉后再做发布")
    private Short tailHaveDrawing;
    //板车车源所在地省
    @NotBlank(message = "请选择板车车源所在地省后再做发布")
    private String tailSourceProvince;
    //板车车源所在地市
    @NotBlank(message = "请选择板车车源所在地市后再做发布")
    private String tailSourceCity;
    //板车车源所在地区
    @NotBlank(message = "请选择板车车源所在地区后再做发布")
    private String tailSourceArea;

}
