package com.tyt.usedCarSale.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.file.bean.FileInfo;
import com.tyt.file.service.FileInfoService;
import com.tyt.model.TytUsedCarSale;
import com.tyt.model.UsedCarAuditRecord;
import com.tyt.model.User;
import com.tyt.mybatis.mapper.UsedCarAuditRecordMapper;
import com.tyt.usedCarSale.bean.CarSaleBean;
import com.tyt.usedCarSale.bean.UsedCarDetailBean;
import com.tyt.usedCarSale.bean.UsedCarSearchBean;
import com.tyt.usedCarSale.dao.TytUsedCarSaleDao;
import com.tyt.usedCarSale.service.TytUsedCarSaleService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Base64Util;
import com.tyt.util.TimeUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service("tytUsedCarSaleService")
public class TytUsedCarSaleServiceImpl extends BaseServiceImpl<TytUsedCarSale,Long> implements TytUsedCarSaleService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserService userService;
    @Autowired
    private FileInfoService fileInfoService;
    @Autowired
    private TytConfigService tytConfigService;
    @Autowired
    private TytUsedCarSaleDao tytUsedCarSaleDao;
    @Autowired
    private UsedCarAuditRecordMapper usedCarAuditRecordMapper;
    @Resource(name = "tytUsedCarSaleDao")
    public void setBaseDao(BaseDao<TytUsedCarSale, Long> tytUsedCarSaleDao) {
        super.setBaseDao(tytUsedCarSaleDao);
    }

    //上传文件类型名称，用于在相对路径中创建文件夹，与其他文件区分
    //支持多级文件目录，文件目录格式 x/y/z
    private static String TYPENAME = "usedCarSale/image/";

    /**
     * @Description  发布、编辑车辆信息的方法
     * <AUTHOR>
     * @Date  2020/1/8 20:21
     * @Param [carSaleBean]
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    @Override
    public Map<String, Object> savePublishCarInfo(CarSaleBean carSaleBean) throws Exception {

        Map<String,Object> map = new HashMap <String,Object>();
        //获取用户信息
        Long userId = carSaleBean.getUserId();
        User user = userService.getByUserId(userId);
        //1.保存车辆主体信息
        //补充、拼装carSaleBean的参数
        addCarSaleBeanParams(carSaleBean, userId, user);
        //二手车信息实体
        TytUsedCarSale tytUsedCarSale = null;
        //车辆信息ID
        Long carSaleId = carSaleBean.getId();
        if(carSaleId == null){ //如果车辆信息ID为空,则表明是发布
            tytUsedCarSale = new TytUsedCarSale();
            //拷贝二手车实体对象
            BeanUtils.copyProperties(carSaleBean, tytUsedCarSale);
            carSaleId = (Long)this.getBaseDao().insertSave(tytUsedCarSale);
        }else{ //否则为编辑
            tytUsedCarSale = this.getById(carSaleId);
            //拷贝二手车实体对象
            BeanUtils.copyProperties(carSaleBean, tytUsedCarSale);
            this.getBaseDao().update(tytUsedCarSale);
        }

        //2.保存车辆图片信息(由于上传图片比较耗时，采用新建线程池异步上传的方式进行优化)
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Long finalCarSaleId = carSaleId;
        TytUsedCarSale finalTytUsedCarSale = tytUsedCarSale;
        executorService.execute(new Runnable() {
            @SneakyThrows
            @Override
            public void run() {
                long startTime = System.currentTimeMillis();
                logger.info("=========发布车辆信息，上传图片开始：========="+startTime);
                String[] carImages = carSaleBean.getCarImages();
                //文件存储的相对路径
                String typeName = TYPENAME + finalCarSaleId;
                if(carImages != null && carImages.length > 0){
                    for (int i = 0; i < carImages.length; i++) {
                        String carImage = carImages[i];
                        MultipartFile carImageFile = Base64Util.base64ToMultipart(carImage);
                        //上传新文件并补充文件信息 type=2:二手板车图片
                        Map fileInfoMap = fileInfoService.uploadFile(user, finalCarSaleId, 2, carImageFile, typeName);
                    }
                }
                long endTime = System.currentTimeMillis();
                logger.info("=========发布车辆信息，上传图片结束：========="+endTime);
                logger.info("=========发布车辆信息，上传图片共花费的时间为：=========" + (endTime - startTime) + "ms");
                //3.将删除的图片信息状态置为无效
                Long[] deleteImageIds = carSaleBean.getDeleteImageIds();
                if(deleteImageIds != null && deleteImageIds.length > 0){
                    for (Long deleteImageId : deleteImageIds) {
                        //文件状态: 1-正常,2-删除
                        fileInfoService.updateFileInfoStatus(user,deleteImageId,2);
                    }
                }

                //4.由于删除的如果是车辆首张图片，会导致列表图和详情第一张图不一致
                //取出车辆图片列表，用列表的第一张图覆盖更新车辆信息首张图片
                List<FileInfo> fileInfoList = fileInfoService.getFileInfoList(finalCarSaleId, 2);
                if(fileInfoList != null && fileInfoList.size() > 0){
                    FileInfo fileInfo = fileInfoList.get(0);
                    if(fileInfo != null){
                        //压缩图图片地址
                        String smallFilePath = fileInfo.getSmallFilePath();
                        finalTytUsedCarSale.setFirstPicUrl(smallFilePath);
                        tytUsedCarSaleDao.update(finalTytUsedCarSale);
                    }
                }else{ //如果车辆图片列表为空，则将车辆信息中的缩略图置为空
                        finalTytUsedCarSale.setFirstPicUrl(null);
                        tytUsedCarSaleDao.update(finalTytUsedCarSale);
                }
            }
        });
        // 关闭线程
        executorService.shutdown();

        //发布的车辆信息ID
        map.put("carSaleId",carSaleId);
        return map;
    }

    /**
     * @Description  补充、拼装carSaleBean的参数
     * <AUTHOR>
     * @Date  2020/1/20 18:36
     * @Param [carSaleBean, userId, user]
     * @return void
     **/
    private void addCarSaleBeanParams(CarSaleBean carSaleBean, Long userId, User user) {
        //信息类型 1.仅车头 2.仅挂车 3.一主一挂
        int infoType = carSaleBean.getInfoType().intValue();
        //发布人id
        carSaleBean.setUserId(userId);
        //发布人姓名
        carSaleBean.setUserName(user.getUserName());
        //如果是车头或者一主一挂
        if(infoType == 1 || infoType == 3){
            //处理车头车源所在地全地址
            String headSourceProvince = carSaleBean.getHeadSourceProvince();
            String headSourceCity = carSaleBean.getHeadSourceCity();
            String headSourceArea = carSaleBean.getHeadSourceArea();
            //车头车源所在地全地址
            String headSourceFullAddress = this.handleRegion(headSourceProvince, headSourceCity, headSourceArea);
            carSaleBean.setHeadSourceFullAddress(headSourceFullAddress);
            //车头标题(规则：年份+品牌+型号+马力+驱动)
            String headTitle = "";
            //品牌
            String headBrand = carSaleBean.getHeadBrand();
            if(StringUtils.isNotBlank(headBrand)){
                headTitle += headBrand;
            }
            //马力
            BigDecimal headHorsepower = carSaleBean.getHeadHorsepower();
            if(headHorsepower != null){
                headTitle += " "+headHorsepower;
            }
            //排放标准
            String headDischargeStandard = carSaleBean.getHeadDischargeStandard();
            if(StringUtils.isNotBlank(headDischargeStandard)){
                headTitle += " "+headDischargeStandard;
            }

            headTitle += " 牵引头";

            carSaleBean.setHeadTitle(headTitle);
        }
        //如果是挂车或者一主一挂
        if(infoType == 2 || infoType == 3) {
            //处理板车车源所在地全地址
            String tailSourceProvince = carSaleBean.getTailSourceProvince();
            String tailSourceCity = carSaleBean.getTailSourceCity();
            String tailSourceArea = carSaleBean.getTailSourceArea();
            //板车车源所在地全地址
            String tailSourceFullAddress = this.handleRegion(tailSourceProvince, tailSourceCity, tailSourceArea);
            carSaleBean.setTailSourceFullAddress(tailSourceFullAddress);
            //板车标题(规则：长度+品牌+类型+种类)
            String tailTitle = "";
            //品牌
            String tailBrand = carSaleBean.getTailBrand();
            if(StringUtils.isNotBlank(tailBrand)){
                tailTitle += tailBrand;
            }
            //长度
            BigDecimal tailLength = carSaleBean.getTailLength();
            if(tailLength != null){
                tailTitle += " "+tailLength.toString() + "米";
            }
            //类型
            String tailType = carSaleBean.getTailType();
            if(StringUtils.isNotBlank(tailType)){
                tailTitle += " "+tailType;
            }
            tailTitle += " 板车";
            carSaleBean.setTailTitle(tailTitle);
        }
        //如果是一主一挂
        if(infoType == 3){
            //车头是否已售卖 1.是 2.否
            Short headIsSale = carSaleBean.getHeadIsSale();
            if(headIsSale != null && headIsSale.intValue() == 1){
                //车头售卖时间
                carSaleBean.setHeadSaleTime(new Date());
                //信息类型 1.仅车头 2.仅挂车 3.一主一挂
                //信息类型改为2.仅挂车
                carSaleBean.setInfoType((short)2);
            }
            //挂车是否已售卖 1.是 2.否
            Short tailIsSale = carSaleBean.getTailIsSale();
            if(tailIsSale != null && tailIsSale.intValue() == 1){
                //挂车售卖时间
                carSaleBean.setTailSaleTime(new Date());
                //信息类型 1.仅车头 2.仅挂车 3.一主一挂
                //信息类型改为1.仅车头
                carSaleBean.setInfoType((short)1);
            }
        }
        //审核状态 1.审核中 2.审核通过 3.审核失败
        carSaleBean.setAuditStatus((short)1);
        //发布状态 1.上架 2.下架 3.草稿
        Short postStatus = carSaleBean.getPostStatus();
        if(postStatus != null ){
            //如果发布状态为1.上架,则增加发布时间
            if(postStatus.intValue() == 1){
                //发布时间
                carSaleBean.setPublishTime(new Date());
                //如果发布状态为3.草稿,则将发布时间置为空
            }else if(postStatus.intValue() == 3){
                carSaleBean.setPublishTime(null);
            }
        }

        //车辆信息ID
        Long carSaleId = carSaleBean.getId();
        //如果车辆ID为空，则证明是新增
        if(carSaleId == null){
            //创建时间（提交时间）
            carSaleBean.setCtime(new Date());
        }else{ //如果车辆信息ID不为空，则证明是编辑，补充对象的相关信息
            //根据车辆信息ID，查询原数据对象
            TytUsedCarSale tytUsedCarSale = this.getById(carSaleId);
            //列表展示图缩略图地址
            carSaleBean.setFirstPicUrl(tytUsedCarSale.getFirstPicUrl());
            //后台发布人id
            carSaleBean.setCreateId(tytUsedCarSale.getCreateId());
            //后台发布人姓名
            carSaleBean.setCreateName(tytUsedCarSale.getCreateName());
            //审核人id
            carSaleBean.setAuditId(tytUsedCarSale.getAuditId());
            //审核人姓名
            carSaleBean.setAuditName(tytUsedCarSale.getAuditName());
            //审核时间
            carSaleBean.setAuditTime(tytUsedCarSale.getAuditTime());
            //审核失败原因
            carSaleBean.setAuditFail(tytUsedCarSale.getAuditFail());
            //创建时间
            carSaleBean.setCtime(tytUsedCarSale.getCtime());
        }
        //修改时间
        carSaleBean.setUtime(new Date());
    }

    /**
     * 处理省市区县的连接
     *
     * @param provinc 省
     * @param city    市
     * @param area    区县
     * @return 完整的地址
     */
    private String handleRegion(String provinc, String city, String area) {
        String point = "";
        if(StringUtils.isNotBlank(provinc)){
            switch (provinc) {
                case "北京":
                case "天津":
                case "上海":
                case "重庆": {
                    point = city + org.apache.commons.lang3.StringUtils.defaultString(area);
                    break;
                }
                default: {
                    point = provinc + city + org.apache.commons.lang3.StringUtils.defaultString(area);
                    break;
                }
            }
        }
        return point;
    }


    /**
     * @Description  批量修改车辆发布状态，发布状态 1.上架 2.下架 3.草稿
     * <AUTHOR>
     * @Date  2020/1/9 10:17
     * @Param [ids, postStatus]
     * @return void
     **/
    @Override
    public void updateCarStatus(Long userId,Long[] ids, Integer postStatus,String dismountReason) throws Exception {
        //查询用户信息
        User user = userService.getByUserId(userId);
        //1.修改车辆主体信息的状态
        String updateSql = "update tyt_used_car_sale set post_status=:postStatus,utime=:utime where id in (:ids)";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("ids",ids);
        params.put("postStatus",postStatus);
        params.put("utime",new Date());
        int result = this.getBaseDao().executeUpdateSql(updateSql, params);
        UsedCarAuditRecord record=new UsedCarAuditRecord();
        record.setCtime(new Date());
        record.setReason(dismountReason);
        record.setStatus("1");
        record.setUsedCarSaleId(ids[0]);
        record.setUserId(user.getId());
        record.setUserName(user.getUserName());
        usedCarAuditRecordMapper.insertUsedCarAuditRecord(record);
    }

    //服务器图片路径(旧规则)
    public static final String TYT_SERVER_PICTURE_URL_OLD = "tyt_server_picture_url_old";


    @Override
    public List<TytUsedCarSale> getUsedCarList(Integer infoType,UsedCarSearchBean searchBean,String queryTime,Integer queryActionType) throws Exception{
        int pageSize = 20;
        String tytServerPictureUrlOld = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_OLD);
        List<Object> params = new ArrayList<Object>();
        StringBuffer sql = new StringBuffer("SELECT s.id id,s.`head_title` headTitle,s.`head_source_province` headSourceProvince," +
                "s.`head_source_city` headSourceCity,s.`head_check_in_time` headCheckInTime,s.`head_source_area` headSourceArea,s.`head_source_full_address` headSourceFullAddress,s.`head_drive_kilometers` headDriveKilometers," +
                "s.`tail_title` tailTitle,s.`tail_age` tailAge,s.`tail_check_in_time` tailCheckInTime,s.`tail_source_province` tailSourceProvince,s.`tail_source_city` tailSourceCity," +
                "s.`tail_source_area` tailSourceArea,s.`tail_source_full_address` tailSourceFullAddress,s.`ctime` ctime,s.`info_type` infoType, s.`sale_cell_phone` saleCellPhone, " +
                "CONCAT('"+tytServerPictureUrlOld+"',s.`first_pic_url`) firstPicUrl,s.`is_break_sale` isBreakSale,s.`publish_time` publishTime FROM `tyt_used_car_sale` s WHERE 1=1");
        sql.append(" AND s.`post_status`=1 ");//有效
        sql.append(" AND s.`audit_status`=2 "); //审核通过
        if (StringUtils.isNotBlank(searchBean.getHeadProvince())){
            sql.append(" AND s.`head_source_province`=? ");
            params.add(searchBean.getHeadProvince());
        }
        if (StringUtils.isNotBlank(searchBean.getHeadCity())){
            sql.append(" AND s.`head_source_city`=? ");
            params.add(searchBean.getHeadCity());
        }
        if (StringUtils.isNotBlank(searchBean.getHeadArea())){
            sql.append(" AND s.`head_source_area`=? ");
            params.add(searchBean.getHeadArea());
        }
        if (StringUtils.isNotBlank(searchBean.getHeadBrand())){
            sql.append(" AND s.`head_brand`=?");
            params.add(searchBean.getHeadBrand());
        }
        if (null != searchBean.getHeadHorsepowerMin()){
            sql.append(" AND s.`head_horsepower`>=?");
            params.add(searchBean.getHeadHorsepowerMin());
        }
        if (null != searchBean.getHeadHorsepowerMax()){
            sql.append(" AND s.`head_horsepower`<=?");
            params.add(searchBean.getHeadHorsepowerMax());
        }
        if (null != searchBean.getTimeMax()){
            String minTime = TimeUtil.formatDate(TimeUtil.addYearDays(-searchBean.getTimeMax(),0));
            sql.append(" AND s.`head_check_in_time`>?");
            params.add(minTime+" 00:00:00");
        }
        if (null != searchBean.getTimeMin()){
            String maxTime = TimeUtil.formatDate(TimeUtil.addYearDays(-searchBean.getTimeMin(),0));
            sql.append(" AND s.`head_check_in_time`<?");
            params.add(maxTime+" 23:59:59");
        }
        if (StringUtils.isNotBlank(searchBean.getTailProvince())){
            sql.append(" AND s.`tail_source_province`=? ");
            params.add(searchBean.getTailProvince());
        }
        if (StringUtils.isNotBlank(searchBean.getTailCity())){
            sql.append(" AND s.`tail_source_city`=? ");
            params.add(searchBean.getTailCity());
        }
        if (StringUtils.isNotBlank(searchBean.getTailArea())){
            sql.append(" AND s.`tail_source_area`=? ");
            params.add(searchBean.getTailArea());
        }
        if (StringUtils.isNotBlank(searchBean.getTailType())){
            sql.append(" AND s.`tail_type`=? ");
            params.add(searchBean.getTailType());
        }
        if (StringUtils.isNotBlank(searchBean.getTailLength())){
            sql.append(" AND s.`tail_length`=? ");
            params.add(searchBean.getTailLength());
        }
        if (StringUtils.isNotBlank(searchBean.getTailBrand())){
            sql.append(" AND s.`tail_brand`=? ");
            params.add(searchBean.getTailBrand());
        }
        if (infoType == 1){
            sql.append(" AND (s.`info_type`=1 OR (s.`info_type`=3 AND s.`is_break_sale`=1))");
        }else if (infoType == 2){
            sql.append(" AND (s.`info_type`=2 OR (s.`info_type`=3 AND s.`is_break_sale`=1))");
        }else if (infoType == 3){
            sql.append(" AND s.`info_type`=3");
        }
        if (queryActionType == 2) {
            sql.append(" and s.`publish_time`< ?");
            params.add(queryTime);
        }
        sql.append(" order by s.`publish_time` desc ");

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("headTitle", Hibernate.STRING);
        scalarMap.put("headSourceProvince", Hibernate.STRING);
        scalarMap.put("headSourceCity", Hibernate.STRING);
        scalarMap.put("tailAge", Hibernate.BIG_DECIMAL);
        scalarMap.put("headCheckInTime", Hibernate.TIMESTAMP);
        scalarMap.put("tailCheckInTime", Hibernate.TIMESTAMP);
        scalarMap.put("headSourceArea",Hibernate.STRING);
        scalarMap.put("headSourceFullAddress",Hibernate.STRING);
        scalarMap.put("headDriveKilometers",Hibernate.BIG_DECIMAL);
        scalarMap.put("tailTitle", Hibernate.STRING);
        scalarMap.put("tailSourceProvince", Hibernate.STRING);
        scalarMap.put("tailSourceCity", Hibernate.STRING);
        scalarMap.put("tailSourceArea",Hibernate.STRING);
        scalarMap.put("tailSourceFullAddress",Hibernate.STRING);
        scalarMap.put("ctime",Hibernate.TIMESTAMP);
        scalarMap.put("infoType",Hibernate.SHORT);
        scalarMap.put("saleCellPhone",Hibernate.STRING);
        scalarMap.put("firstPicUrl",Hibernate.STRING);
        scalarMap.put("isBreakSale",Hibernate.SHORT);
        scalarMap.put("publishTime",Hibernate.TIMESTAMP);
        return this.getBaseDao().search(sql.toString(), scalarMap, TytUsedCarSale.class, params.toArray(), 1, pageSize);
    }

    @Override
    public UsedCarDetailBean getByUsedCarById(Long id) {
        UsedCarDetailBean detailBean = new UsedCarDetailBean();
        String tytServerPictureUrlOld = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_OLD);
        TytUsedCarSale carSale = this.getById(id);
        detailBean.setCarSale(carSale);
        List<FileInfo> fileInfoList = fileInfoService.getFileInfoList(id,2);
        if (fileInfoList!=null && fileInfoList.size()>0){
            for (FileInfo fileInfo : fileInfoList) {
                fileInfo.setFilePath(tytServerPictureUrlOld+fileInfo.getFilePath());
                fileInfo.setSmallFilePath(tytServerPictureUrlOld+fileInfo.getSmallFilePath());
            }
        }
        detailBean.setFileInfoList(fileInfoList);
        return detailBean;
    }

    @Override
    public List<TytUsedCarSale> getMyUsedCarList(Integer listType,Long userId) throws Exception{
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");
        String tytServerPictureUrlOld = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_OLD);
        List<Object> params = new ArrayList<Object>();
        StringBuffer sql = new StringBuffer("SELECT s.id id,s.user_id userId,s.`head_title` headTitle,s.`head_source_province` headSourceProvince," +
                "s.`head_source_city` headSourceCity,s.`head_source_area` headSourceArea,s.`head_source_full_address` headSourceFullAddress,s.`head_drive_kilometers` headDriveKilometers," +
                "s.`tail_title` tailTitle,s.`tail_age` tailAge,s.`head_check_in_time` headCheckInTime,s.`tail_check_in_time` tailCheckInTime,s.`tail_source_province` tailSourceProvince,s.`tail_source_city` tailSourceCity," +
                "s.`tail_source_area` tailSourceArea,s.`tail_source_full_address` tailSourceFullAddress,s.`ctime` ctime,s.`info_type` infoType, s.`sale_cell_phone` saleCellPhone, s.`audit_status` auditStatus, " +
                "CONCAT('"+tytServerPictureUrlOld+"',s.`first_pic_url`) firstPicUrl,s.`is_break_sale` isBreakSale, s.`publish_time` publishTime FROM `tyt_used_car_sale` s WHERE 1=1");
        if (listType == 1){
            sql.append(" AND s.`post_status`=1 ");//发布中
        }else if (listType ==2) {
            sql.append(" AND s.`post_status`=2 ");//已下架
        }else if (listType == 3){
            sql.append(" AND s.`post_status`=3 ");//已保存
        }
        sql.append(" AND s.user_id=?");
        params.add(userId);
        if(listType == 3){
            sql.append(" order by s.`id` desc ");
        }else{
            sql.append(" order by s.`publish_time` desc ");
        }

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("headTitle", Hibernate.STRING);
        scalarMap.put("headSourceProvince", Hibernate.STRING);
        scalarMap.put("headSourceCity", Hibernate.STRING);
        scalarMap.put("headSourceArea",Hibernate.STRING);
        scalarMap.put("headCheckInTime", Hibernate.TIMESTAMP);
        scalarMap.put("tailCheckInTime", Hibernate.TIMESTAMP);
        scalarMap.put("headSourceFullAddress",Hibernate.STRING);
        scalarMap.put("headDriveKilometers",Hibernate.BIG_DECIMAL);
        scalarMap.put("tailTitle", Hibernate.STRING);
        scalarMap.put("tailAge", Hibernate.BIG_DECIMAL);
        scalarMap.put("tailSourceProvince", Hibernate.STRING);
        scalarMap.put("tailSourceCity", Hibernate.STRING);
        scalarMap.put("tailSourceArea",Hibernate.STRING);
        scalarMap.put("tailSourceFullAddress",Hibernate.STRING);
        scalarMap.put("ctime",Hibernate.TIMESTAMP);
        scalarMap.put("infoType",Hibernate.SHORT);
        scalarMap.put("saleCellPhone",Hibernate.STRING);
        scalarMap.put("auditStatus",Hibernate.SHORT);
        scalarMap.put("firstPicUrl",Hibernate.STRING);
        scalarMap.put("isBreakSale",Hibernate.SHORT);
        scalarMap.put("publishTime",Hibernate.TIMESTAMP);
        return this.getBaseDao().search(sql.toString(), scalarMap, TytUsedCarSale.class, params.toArray(), 1, 1000);
    }

    /**
     * @description 根据userId更新二手车状态
     * <AUTHOR>
     * @date 2021/12/25 17:46
     * @param userId
     * @return int
     */
    @Override
    public int updateCarStatusByUserId(Long userId, Integer postStatus) throws Exception {
        //修改车辆主体信息的状态
        String updateSql = "update tyt_used_car_sale set post_status=:postStatus,utime=:utime where user_id =:userId ";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("postStatus",postStatus);
        params.put("utime",new Date());
        params.put("userId",userId);
        int result = this.getBaseDao().executeUpdateSql(updateSql, params);
        return result;
    }
}
