package com.tyt.usedCarSale.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUsedCarSale;
import com.tyt.usedCarSale.bean.CarSaleBean;

import java.util.Map;
import com.tyt.usedCarSale.bean.UsedCarDetailBean;
import com.tyt.usedCarSale.bean.UsedCarSearchBean;

import java.util.List;

public interface TytUsedCarSaleService extends BaseService<TytUsedCarSale,Long> {

    /**
     * @Description  发布、编辑车辆信息的方法
     * <AUTHOR>
     * @Date  2020/1/8 20:14
     * @Param [carSaleBean]
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    public Map <String,Object> savePublishCarInfo(CarSaleBean carSaleBean) throws Exception;

    /**
    * @Description 批量修改车辆发布状态，发布状态 1.上架 2.下架 3.草稿
    * <AUTHOR>
    * @Date  2020/1/9 10:15
    * @Param [ids, postStatus]
    * @return void
    **/
    public void updateCarStatus(Long userId, Long[] ids, Integer postStatus,String dismountReason) throws Exception;
    /**
     * 获取二手车销售列表
     * @param searchBean
     * @param queryTime
     * @param queryActionType
     * @return
     */
    List<TytUsedCarSale> getUsedCarList(Integer infoType,UsedCarSearchBean searchBean,String queryTime,Integer queryActionType) throws Exception;

    /**
     * 二手车详情页
     * @param id
     * @return
     */
    UsedCarDetailBean getByUsedCarById(Long id);

    /**
     * 我的发布列表
     * @param listType 1.发布中 2.已下架 3.已保存
     * @param userId 用户id
     * @return list
     * @throws Exception
     */
    List<TytUsedCarSale> getMyUsedCarList(Integer listType, Long userId) throws Exception;

    /**
     * @description 根据userId更新二手车状态
     * <AUTHOR>
     * @date 2021/12/25 17:46
     * @param userId
     * @return int
     */
    int updateCarStatusByUserId(Long userId,Integer postStatus) throws Exception;
}
