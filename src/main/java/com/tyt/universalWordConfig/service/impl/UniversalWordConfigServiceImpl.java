package com.tyt.universalWordConfig.service.impl;

import com.tyt.mybatis.mapper.UniversalWordConfigMapper;
import com.tyt.universalWordConfig.bean.UniversalWordConfigInfo;
import com.tyt.universalWordConfig.service.UniversalWordConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class UniversalWordConfigServiceImpl implements UniversalWordConfigService {

    @Autowired
    private UniversalWordConfigMapper universalWordConfigMapper;

    @Override
    public List<UniversalWordConfigInfo> getAllUniversalWordConfigInfoListByType(Integer type) {
        List<UniversalWordConfigInfo> universalWordConfigInfoList = universalWordConfigMapper.getAllUniversalWordConfigInfoListByType(type);
        if (CollectionUtils.isEmpty(universalWordConfigInfoList)) {
            return new ArrayList<>();
        }
        return universalWordConfigInfoList;
    }

    @Override
    public List<UniversalWordConfigInfo> getAllUniversalWordConfigInfoListByCodeList(List<String> codes) {
        List<UniversalWordConfigInfo> universalWordConfigInfoList = universalWordConfigMapper.getAllUniversalWordConfigInfoListByCodeList(codes);
        if (CollectionUtils.isEmpty(universalWordConfigInfoList)) {
            return new ArrayList<>();
        }
        return universalWordConfigInfoList;
    }
}
