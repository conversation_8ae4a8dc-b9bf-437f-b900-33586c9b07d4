package com.tyt.universalWordConfig.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.universalWordConfig.service.UniversalWordConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 * 通用文案配置
 * 通用文案配置
 */
@RestController
@RequestMapping("/plat/universalWordConfig")
public class UniversalWordConfigController extends BaseController {

    @Autowired
    private UniversalWordConfigService universalWordConfigService;

    /**
     * 查询车或货的所有通用文案配置
     *
     * @param type 1:车，2:货
     * @return ResultMsgBean List UniversalWordConfigInfo
     */
    @GetMapping({"/getAllUniversalWordConfigInfoListByType", "/getAllUniversalWordConfigInfoListByType.action"})
    public ResultMsgBean getUniversalWordConfigInfoList(@RequestParam("type") Integer type) {
        if (type == null || (type != 1 && type != 2 && type != 3)) {
            return ResultMsgBean.failResponse(10001, "请求参数错误");
        }
        return ResultMsgBean.successResponse(universalWordConfigService.getAllUniversalWordConfigInfoListByType(type));
    }

    /**
     * 通过code集合查询通用文案配置
     *
     * @param codes 多个code间用逗号分隔拼接而成的字符串
     * @return ResultMsgBean List UniversalWordConfigInfo
     */
    @PostMapping({"/getAllUniversalWordConfigInfoListByCodeList", "/getAllUniversalWordConfigInfoListByCodeList.action"})
    public ResultMsgBean getAllUniversalWordConfigInfoListByCodeList(String codes) {
        if (StringUtils.isBlank(codes)) {
            return ResultMsgBean.failResponse(10001, "请求参数错误");
        }
        List<String> codeList = Arrays.stream(codes.trim().split(COMMA)).collect(Collectors.toList());
        codeList = codeList.stream().map(String::trim).collect(Collectors.toList());
        return ResultMsgBean.successResponse(universalWordConfigService.getAllUniversalWordConfigInfoListByCodeList(codeList));
    }

}
