package com.tyt.upgrade.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUpgradeTemplateTask;
import com.tyt.upgrade.bean.NoticePopupBean;
import com.tyt.upgrade.service.TytUpgradeTemplateTaskService;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/7/30 15:55
 * @Version 1.0
 **/
@Service("tytUpgradeTemplateTaskService")
public class TytUpgradeTemplateTaskServiceImpl extends BaseServiceImpl<TytUpgradeTemplateTask,Long> implements TytUpgradeTemplateTaskService {
    @Resource(name = "tytUpgradeTemplateTaskDao")
    public void setBaseDao(BaseDao<TytUpgradeTemplateTask, Long> tytUpgradeTemplateTaskLongBaseDao) {
        super.setBaseDao(tytUpgradeTemplateTaskLongBaseDao);
    }

    @Override
    public NoticePopupBean queryPicList(Long userId) {
        String sql="SELECT tum.id id,tum.template_pic templatePic,tum.cargo_station_android_url cargoStationAndroidUrl,tum.cargo_station_ios_url cargoStationIosUrl,"+
                "tum.car_owner_android_url carOwnerAndroidUrl, tum.car_owner_ios_url carOwnerIosUrl,IFNULL(tus.close_flag,0) closeFlag," +
                "CASE WHEN tum.template_type = 'CARGO_STATION' THEN '1' WHEN tum.template_type = 'CAR_OWNER' THEN '2' ELSE '0' END buttonStatus," +
                "tum.cargo_station_logo cargoStationLogo,tum.car_owner_logo cargoOwnerLogo FROM rel_task_user rtu "+
                "JOIN tyt_upgrade_templatetask tus ON rtu.task_id = tus.id JOIN tyt_upgrade_template tum ON tus.template_id = tum.id"+
                " WHERE tus.task_status = 1 AND rtu.user_status = 1 AND tus.task_start_time <= NOW() AND tus.task_end_time >= NOW() AND rtu.user_id = ? order by tus.ctime DESC LIMIT 1";

        Object[] params=new Object[] {userId};

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("templatePic", Hibernate.STRING);
        scalarMap.put("cargoStationAndroidUrl", Hibernate.STRING);
        scalarMap.put("cargoStationIosUrl", Hibernate.STRING);
        scalarMap.put("carOwnerAndroidUrl", Hibernate.STRING);
        scalarMap.put("carOwnerIosUrl", Hibernate.STRING);
        scalarMap.put("closeFlag", Hibernate.INTEGER);
        scalarMap.put("buttonStatus", Hibernate.INTEGER);
        scalarMap.put("cargoStationLogo", Hibernate.STRING);
        scalarMap.put("cargoOwnerLogo", Hibernate.STRING);
        List<NoticePopupBean> noticePopupBeans = this.getBaseDao().search(sql, scalarMap, NoticePopupBean.class, params);
        if(noticePopupBeans!=null&& !noticePopupBeans.isEmpty()){
            return noticePopupBeans.get(0);
        }
        //如果当前用户不存在任务，查询是否有全量的任务
        String allSql="SELECT tum.id id,tum.template_pic templatePic,tum.cargo_station_android_url cargoStationAndroidUrl,tum.cargo_station_ios_url cargoStationIosUrl,"+
                "tum.car_owner_android_url carOwnerAndroidUrl, tum.car_owner_ios_url carOwnerIosUrl,IFNULL(tus.close_flag,0) closeFlag," +
                "CASE WHEN tum.template_type = 'CARGO_STATION' THEN '1' WHEN tum.template_type = 'CAR_OWNER' THEN '2' ELSE '0' END buttonStatus," +
                "tum.cargo_station_logo cargoStationLogo,tum.car_owner_logo cargoOwnerLogo "+
                "FROM tyt_upgrade_templatetask tus JOIN tyt_upgrade_template tum ON tus.template_id = tum.id "+
                "WHERE tus.task_status = 1 AND tus.all_user_flag = 1 AND tus.task_start_time <= NOW() AND tus.task_end_time >= NOW() order by tus.ctime DESC LIMIT 1";

        Map<String, Type> allScalarMap = new HashMap<String, Type>();
        allScalarMap.put("id", Hibernate.LONG);
        allScalarMap.put("templatePic", Hibernate.STRING);
        allScalarMap.put("cargoStationAndroidUrl", Hibernate.STRING);
        allScalarMap.put("cargoStationIosUrl", Hibernate.STRING);
        allScalarMap.put("carOwnerAndroidUrl", Hibernate.STRING);
        allScalarMap.put("carOwnerIosUrl", Hibernate.STRING);
        allScalarMap.put("closeFlag", Hibernate.INTEGER);
        allScalarMap.put("buttonStatus", Hibernate.INTEGER);
        allScalarMap.put("cargoStationLogo", Hibernate.STRING);
        allScalarMap.put("cargoOwnerLogo", Hibernate.STRING);
        List<NoticePopupBean> allNoticePopupBeans = this.getBaseDao().search(allSql, allScalarMap, NoticePopupBean.class,new Object[]{});
        if(allNoticePopupBeans!=null&& !allNoticePopupBeans.isEmpty()){
            return allNoticePopupBeans.get(0);
        }
        return new NoticePopupBean();
    }
}
