package com.tyt.upgrade.service.impl;

import com.tyt.upgrade.service.UpgradeCheckService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/12/18 16:52
 * @Version 1.0
 **/

@Service("upgradeCheckService")
public class UpgradeCheckServiceImpl implements UpgradeCheckService {


    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Override
    public boolean checkClientInfo(String clientVersion, Integer clientSign){

        String upgradeClientVersion = configService.getStringValue(Constant.UPGRADE_CLIENT_VERSION_CHECK, "6000");
        String upgradeclientSign = configService.getStringValue(Constant.UPGRADE_CLIENT_SIGN_CHECK, "2,3");

        String[] clientVersions = upgradeClientVersion.split(",");
        String[] clientSigns = upgradeclientSign.split(",");
        for(String version : clientVersions){
            if(Integer.parseInt(clientVersion)<Integer.parseInt(version)){
                for(String sign : clientSigns){
                    if(clientSign==Integer.parseInt(sign)){
                        return true;
                    }
                }
            }
        }
        return false;
    }
}
