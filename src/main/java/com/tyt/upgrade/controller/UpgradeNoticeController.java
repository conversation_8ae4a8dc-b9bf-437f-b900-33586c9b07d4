package com.tyt.upgrade.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.transport.querybean.TransportBean;
import com.tyt.upgrade.bean.NoticePopupBean;
import com.tyt.upgrade.bean.UpgradeNoticeQueryBean;
import com.tyt.upgrade.service.TytUpgradeTemplateTaskService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/7/30 15:50
 * @Version 1.0
 **/
@Controller
@RequestMapping("/plat/upgrade/notice")
public class UpgradeNoticeController extends BaseController {

    @Resource(name = "tytUpgradeTemplateTaskService")
    TytUpgradeTemplateTaskService tytUpgradeTemplateTaskService;

    /**
     * 获取提示框
     * @param bean 查询条件
     * @return rm
     */
    @RequestMapping(value = "/picList")
    @ResponseBody
    public ResultMsgBean picList(HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            Long userId = Long.valueOf(request.getParameter("userId"));
            if (userId==null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID不能为空");
                return rm;
            }
            NoticePopupBean noticePopupBean = tytUpgradeTemplateTaskService.queryPicList(userId);
            rm.setData(noticePopupBean);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }
}
