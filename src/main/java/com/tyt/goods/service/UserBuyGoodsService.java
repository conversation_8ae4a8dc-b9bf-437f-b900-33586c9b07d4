package com.tyt.goods.service;


import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserBuyGoods;

import java.util.Date;
import java.util.List;


/**
 * @Description  用户购买商品服务层
 * <AUTHOR>
 * @Date  2019/6/25 16:13 
 * @Param 
 * @return 
 **/
public interface UserBuyGoodsService extends BaseService<TytUserBuyGoods, Long> {

    /**
     * @Description  保存用户购买商品信息记录
     * <AUTHOR>
     * @Date  2019/7/10 16:43
     * @Param [userId, goodsId, buyPath, ordersStatus]
     *         buyPath：购买商品路径，无值传入 ""
     *         ordersStatus: 订单状态(0.待支付 1.支付失败 2.支付成功)
     * @return com.tyt.model.TytUserBuyGoods
     **/
    public TytUserBuyGoods saveUserBuyGoodsInfo(Long userId, Long goodsId,String buyPath,Integer ordersStatus) throws Exception;

    /**
     * @Description  根据订单号更新用户购买商品的状态
     * <AUTHOR>
     * @Date  2019/7/10 16:44
     * @Param [userBuyGoods, ordersStatus]
     * @return void
     **/
    public void updateUserBuyGoodsStatus(TytUserBuyGoods userBuyGoods,Integer ordersStatus);

    /**
     * @Description  根据用户Id和订单状态获取用户权益记录
     *               (实际上就是购买商品+附赠商品)
     * <AUTHOR>
     * @Date  2019/7/11 11:32
     * @Param [userId, ordersStatus]
     * @return java.util.List<com.tyt.model.TytUserBuyGoods>
     **/
    public List<TytUserBuyGoods> getUserBuyGoods(Long userId,Integer ordersStatus);

    boolean isGivingRight(Long userId, Long goodsId);

    /**
     * @description 查询是否给用户赠送过会员商品
     * <AUTHOR>
     * @date 2022/9/2 10:29
     * @param userId
     * @param goodsId
     * @param startTime
     * @param endTime
     * @return boolean
     */
    boolean isGivingRight(Long userId, Long goodsId, Date startTime, Date endTime);

    boolean isGivingRightForGoods(Long userId);

    /**
     * 查询用户是否有购买记录
     * @param userId
     * @param type  1:会员  2：次卡
     * @return
     */
    Integer getUserBuyGoodsRecord(Long userId,Integer type);
    
    /**
     * 判断用户是否购买过会员
     * @param userId 用户Id
     * @return boolean
     */
    boolean judgeUserBuyGoods(Long userId);

    /**
     * 查询待生效的商品
     **/
    TytUserBuyGoods getDelayedGoods(Long userId, Integer goodsType);

    /**
     * 更新延迟生效商品状态
     */
    void updateDelayedGoodsStatus(Long buyGoodsId, Long userId);
}
