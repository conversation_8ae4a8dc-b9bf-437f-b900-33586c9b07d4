package com.tyt.goods.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.goods.service.GoodsService;
import com.tyt.model.TytGoods;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description  商品服务层实现类
 * <AUTHOR>
 * @Date  2019/6/25 16:17
 * @Param
 * @return
 **/
@Service(value = "goodsService")
public class GoodsServiceImpl extends BaseServiceImpl<TytGoods, Long> implements GoodsService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "goodsDao")
	public void setBaseDao(BaseDao<TytGoods, Long> goodsDao) {
		super.setBaseDao(goodsDao);
	}


}
