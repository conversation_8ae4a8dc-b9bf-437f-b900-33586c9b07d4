package com.tyt.goods.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytSequenceService;
import com.tyt.goods.service.GoodsService;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.model.TytGoods;
import com.tyt.model.TytUserBuyGoods;
import com.tyt.model.User;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;

/**
 * @Description  用户购买商品服务层实现类
 * <AUTHOR>
 * @Date  2019/6/25 16:17
 * @Param
 * @return
 **/
@Service(value = "userBuyGoodsService")
public class UserBuyGoodsServiceImpl extends BaseServiceImpl<TytUserBuyGoods, Long> implements UserBuyGoodsService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "userBuyGoodsDao")
	public void setBaseDao(BaseDao<TytUserBuyGoods, Long> userBuyGoodsDao) {
		super.setBaseDao(userBuyGoodsDao);
	}

	@Autowired
	private UserService userService;

	@Autowired
	private GoodsService goodsService;

	@Autowired
	private TytSequenceService tytSequenceService;

	/**
	 * @Description  保存用户购买商品信息记录
	 * <AUTHOR>
	 * @Date  2019/7/10 16:44
	 * @Param [userId, goodsId, buyPath, ordersStatus]
	 * @return com.tyt.model.TytUserBuyGoods
	 **/
	@Override
	public TytUserBuyGoods saveUserBuyGoodsInfo(Long userId,Long goodsId,String buyPath,Integer ordersStatus) throws Exception{

		// 获取用户信息
		User user = userService.getById(userId);
		// 商品信息
		TytGoods goods = goodsService.getById(goodsId);
		//用户购买商品记录订单号
		String orderId = tytSequenceService.updateGetOrderNumberForDateTime("tyt_user_buy_goods_order_num");

		//用户购买商品信息对象
		TytUserBuyGoods userBuyGoods = new TytUserBuyGoods();
		userBuyGoods.setOrderId(orderId);
		userBuyGoods.setUserId(user.getId());
		userBuyGoods.setUserName(user.getUserName());
		userBuyGoods.setCellPhone(user.getCellPhone());
		userBuyGoods.setGoodsId(goods.getId());
		userBuyGoods.setGoodsName(goods.getName());
		userBuyGoods.setGoodsType(goods.getType());
		userBuyGoods.setGoodsStatus(goods.getStatus());
		userBuyGoods.setOriginalPrice(goods.getOriginalPrice());
		userBuyGoods.setPrice(goods.getPrice());
		userBuyGoods.setTotalFee(goods.getPrice());
		//订单状态(0.待支付 1.支付失败 2.支付成功)
		userBuyGoods.setOrdersStatus(ordersStatus);
		userBuyGoods.setCtime(TimeUtil.getTimeStamp());
		userBuyGoods.setUtime(TimeUtil.getTimeStamp());
		userBuyGoods.setBuyPath(buyPath);
		//有效期--开始时间
		Timestamp startTime = TimeUtil.getTimeStamp();
		userBuyGoods.setBeginTime(startTime);
		Timestamp endTime = getEndTime(goods, startTime);
		//有效期--结束时间
		userBuyGoods.setEndTime(endTime);

		this.getBaseDao().insert(userBuyGoods);
		return userBuyGoods;
	}



	/**
	 * @Description  根据订单号更新用户购买商品的状态
	 * <AUTHOR>
	 * @Date  2019/7/10 16:44
	 * @Param [userBuyGoods, ordersStatus]
	 * @return void
	 **/
	@Override
	public void updateUserBuyGoodsStatus(TytUserBuyGoods userBuyGoods, Integer ordersStatus) {

		// 商品信息
		TytGoods goods = goodsService.getById(userBuyGoods.getGoodsId());
		//订单状态(0.待支付 1.支付失败 2.支付成功)
		userBuyGoods.setOrdersStatus(ordersStatus);
		userBuyGoods.setUtime(TimeUtil.getTimeStamp());
		//有效期--开始时间
		Timestamp startTime = TimeUtil.getTimeStamp();
		userBuyGoods.setBeginTime(startTime);
		//商品有效期
		Timestamp endTime = getEndTime(goods, startTime);
		//有效期--结束时间
		userBuyGoods.setEndTime(endTime);
		this.getBaseDao().update(userBuyGoods);
	}

	/**
	 * @Description  根据有效期开始时间获取结束时间的方法
	 * <AUTHOR>
	 * @Date  2019/7/2 10:32
	 * @Param [goods, startTime]
	 * @return java.sql.Timestamp
	 **/
	private Timestamp getEndTime(TytGoods goods, Timestamp startTime) {
		//商品有效期
		Integer effectiveTime = goods.getEffectiveTime();
		//有效期单位 （day:天; month:月;year:年;boolean:布尔）
		String unit = goods.getUnit();
		//有效期--结束时间
		Timestamp endTime = null;
		if ("year".equals(unit)) {
			endTime = TimeUtil.stampAddYear(startTime, effectiveTime);
		} else if ("month".equals(unit)) {
			endTime = TimeUtil.stampAddMonth(startTime, effectiveTime);
		} else if ("day".equals(unit)) {
			endTime = TimeUtil.stampAdd(startTime, effectiveTime);
		}
		return endTime;
	}
     
	/**
	 * @Description 根据用户Id和订单状态获取用户权益记录
     *              (实际上就是购买商品+附赠商品)
	 * <AUTHOR>
	 * @Date  2019/7/11 11:33
	 * @Param [userId, ordersStatus]
	 * @return java.util.List<com.tyt.model.TytUserBuyGoods>
	 **/
    @Override
    public List<TytUserBuyGoods> getUserBuyGoods(Long userId, Integer ordersStatus) {
        String sql = "SELECT s.remark as goodsName, g.utime as utime " +
                    "FROM  tyt_user_buy_goods g " +
                    " LEFT JOIN tyt_goods_permission p ON g.goods_id = p.goods_id " +
                    " LEFT JOIN permission_sort s ON p.permission_sort_id = s.id  " +
                    " where g.user_id = ? and g.orders_status = ? " +
                    "ORDER BY " +
                    " g.utime desc";
        //传入的参数
        Object[] params = new Object[] {userId,ordersStatus};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("goodsName", Hibernate.STRING);
        scalarMap.put("utime", Hibernate.TIMESTAMP);

        return this.getBaseDao().search(sql, scalarMap, TytUserBuyGoods.class, params);
    }

    @Override
    public boolean isGivingRight(Long userId, Long goodsId){
        String sql = "SELECT * FROM tyt_user_buy_goods WHERE user_id=? AND goods_id=?";
        final Object[] params = {
                userId,goodsId
        };
        List<TytUserBuyGoods> userBuyGoods = this.getBaseDao().queryForList(sql, params);
        if (userBuyGoods.size()>0){
            return true;
        }
        return false;
    }
	/**
	 * @description 查询是否给用户赠送过会员商品
	 * <AUTHOR>
	 * @date 2022/9/2 10:29
	 * @param userId
	 * @param goodsId
	 * @param startTime
	 * @param endTime
	 * @return boolean
	 */
	@Override
	public boolean isGivingRight(Long userId, Long goodsId, Date startTime, Date endTime) {
		String sql = "SELECT * FROM tyt_user_buy_goods WHERE user_id=? AND goods_id=? AND ctime >= ? AND ctime <= ?";
		final Object[] params = {
				userId,goodsId,startTime,endTime
		};
		List<TytUserBuyGoods> userBuyGoods = this.getBaseDao().queryForList(sql, params);
		if (userBuyGoods.size()>0){
			return true;
		}
		return false;
	}

	@Override
    public boolean isGivingRightForGoods(Long userId){
        String sql = "SELECT * FROM tyt_user_buy_goods WHERE user_id=? AND goods_id in (12,100,164,165,166)";
        final Object[] params = {
                userId
        };
        List<TytUserBuyGoods> userBuyGoods = this.getBaseDao().queryForList(sql, params);
        if (userBuyGoods.size()>0){
            return true;
        }
        return false;
    }

	@Override
	public Integer getUserBuyGoodsRecord(Long userId,Integer type) {
		// 1:货会员   3：组合会员4:发货次卡 8:货体验 9：n次X年发货卡  10：加购会员  12：拼团货会员
		StringBuilder sqlBuilder = new StringBuilder().append("SELECT id FROM tyt_user_buy_goods WHERE user_id=? AND orders_status =2 and total_fee>1");
		if(type == 1){
			sqlBuilder.append( " AND goods_type in (1,3,8,10,12)");
		}else {
			sqlBuilder.append( " AND goods_type in (4,9)");
		}
		sqlBuilder.append(" limit 1");
		 Object[] params = {
				userId
		};
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("id", Hibernate.LONG);

		List<TytUserBuyGoods> userBuyGoods = this.getBaseDao().search(sqlBuilder.toString(), scalarMap, TytUserBuyGoods.class, params);

		if(CollectionUtils.isEmpty(userBuyGoods)){
			return 0;
		}
		return userBuyGoods.size();
	}

	@Override
	public boolean judgeUserBuyGoods(Long userId) {
		// 1:货会员   3：组合会员4:发货次卡 8:货体验 9：n次X年发货卡  10：加购会员  12：拼团货会员
		StringBuilder sqlBuilder = new StringBuilder().append("SELECT id FROM tyt_user_buy_goods WHERE user_id=? AND orders_status =2 and total_fee>0  limit 1");
		Object[] params = {userId};
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("id", Hibernate.LONG);
		List<TytUserBuyGoods> userBuyGoods = this.getBaseDao().search(sqlBuilder.toString(), scalarMap, TytUserBuyGoods.class, params);
		if(CollectionUtils.isEmpty(userBuyGoods)||userBuyGoods.size()==0){
			return false;
		}
		return true;
	}

	@Override
	public TytUserBuyGoods getDelayedGoods(Long userId, Integer goodsType) {
		String sql = "SELECT * FROM tyt_user_buy_goods WHERE user_id = ? AND goods_type = ? and orders_status = 2 and delayed_grant = 1 and delayed_status = 2 order by ctime";
		final Object[] params = {
				userId,goodsType
		};
		List<TytUserBuyGoods> userBuyGoods = this.getBaseDao().queryForList(sql, params);
		if(CollectionUtils.isNotEmpty(userBuyGoods)){
			return userBuyGoods.get(0);
		}
		return null;
	}

	@Override
	public void updateDelayedGoodsStatus(Long buyGoodsId, Long userId) {
		String sql = "UPDATE tyt_user_buy_goods SET delayed_status = 3, utime = now() WHERE user_id = ? and delayed_grant = 1 and delayed_status = 1";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { userId });
		if (ObjectUtil.isNotNull(buyGoodsId)) {
			sql = "UPDATE tyt_user_buy_goods SET delayed_status = 1, utime = now() WHERE id = ?";
			this.getBaseDao().executeUpdateSql(sql, new Object[] { buyGoodsId });
		}
	}



}
