package com.tyt.goods.dao.impl;


import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.goods.dao.UserBuyGoodsDao;
import com.tyt.model.TytUserBuyGoods;
import org.springframework.stereotype.Repository;

/**
 * @Description  用户购买商品数据层实现类
 * <AUTHOR>
 * @Date  2019/6/27 17:55
 * @Param
 * @return
 **/
@Repository("userBuyGoodsDao")
public class UserBuyGoodsDaoImpl extends BaseDaoImpl<TytUserBuyGoods, Long> implements UserBuyGoodsDao {

	public UserBuyGoodsDaoImpl() {
		this.setEntityClass(TytUserBuyGoods.class);
	}
}
