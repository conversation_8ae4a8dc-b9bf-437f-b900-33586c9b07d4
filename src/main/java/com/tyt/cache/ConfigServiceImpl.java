package com.tyt.cache;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Config;
import com.tyt.util.Constant;
@Service("configService")
public class ConfigServiceImpl extends BaseServiceImpl<Config,Long> implements ConfigService {
	
	@Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;
	
	@Resource(name="configDao")
	public void setBaseDao(BaseDao<Config, Long> configDao) {
	        super.setBaseDao(configDao);
	}
	
	@Override
	public Config get() {
    	Config config = (Config) cacheService.getObject(Constant.CACHE_CONFIG_KEY);
    	if(null == config) {
    	  config = this.getById(1l);
    	  cacheService.setObject(Constant.CACHE_CONFIG_KEY, config,Constant.CACHE_EXPIRE_LIMIT);
    	}
		return config;
	}

	
	@Override
	public boolean updateConfig(Config config) {
		this.update(config);
		return cacheService.setObject(Constant.CACHE_CONFIG_KEY, config,Constant.CACHE_EXPIRE_LIMIT);
	}

}
