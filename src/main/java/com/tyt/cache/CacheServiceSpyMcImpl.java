package com.tyt.cache;


import com.tyt.service.common.redis.RedisUtil;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;


@Service("cacheServiceMcImpl")
public class CacheServiceSpyMcImpl implements CacheService {

	protected Log logger = LogFactory.getLog(this.getClass());

	public static final int RETRY_COUNT = 3;

	//添加key前缀
	public static String newKeyIndex = "mem_";




	@Override
	public boolean setObject(String key, Object value, long expiry) {
		if (RedisUtil.setObject(newKeyIndex+key,value,(int) expiry)!=null){
			return true;
		}
		return false;
	}

	@Override
	public boolean setObject(String key, Object value) {
		if (RedisUtil.setObject(newKeyIndex+key,value,0)!=null){
			return true;
		}
		return false;
	}

	@Override
	public boolean setString(String key, String value, long expiry) {
		if (RedisUtil.set(newKeyIndex+key,value,(int) expiry)!=null){
			return true;
		}
		return false;
	}

	@Override
	public boolean setString(String key, String value) {
		if (RedisUtil.set(newKeyIndex+key,value,0)!=null){
			return true;
		}
		return false;
	}

	@Override
	public Object getObject(String key) {
		return RedisUtil.getObject(newKeyIndex+key);
	}

	@Override
	public <T> T getJavaObject(String key) {
		Object object = null;
		T result = null;
		int count = 0;
		do {
			object = getObject(key);
			if (null != object) {
				try {
					result = (T) object;
				} catch (Exception e) {
					logger.error("get object type error:" + newKeyIndex+key + " " + object.toString() + " " + e.toString());
				}
			}
			count++;
		} while (null != object && null == result && count < RETRY_COUNT);

		return result;

	}

	@Override
	public String getString(String key) {
		return RedisUtil.get(newKeyIndex+key);
	}

	@Override
	public boolean del(String key) {
		if (RedisUtil.del(newKeyIndex+key)>0){
			return true;
		}
		return false;
	}
}