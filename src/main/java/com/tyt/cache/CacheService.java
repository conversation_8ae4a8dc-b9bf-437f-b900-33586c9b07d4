package com.tyt.cache;

public interface CacheService {
	public boolean setObject(String key, Object value, long expiry);

	public boolean setObject(String key, Object value);

	public boolean setString(String key, String value, long expiry);

	public boolean setString(String key, String value);

	public Object getObject(String key);

	public String getString(String key);

	public <T> T getJavaObject(String key);

	public boolean del(String key);
}
