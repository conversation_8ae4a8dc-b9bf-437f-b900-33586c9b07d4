package com.tyt.job;

import com.tyt.cache.ConfigService;
import com.tyt.model.Config;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * User: Administrator
 * Date: 13-11-17
 * Time: 下午2:02
 */
@Service("dailyJob")
public class DailyJob {
    protected Log logger = LogFactory.getLog(this.getClass());

	@Resource(name = "configService")
    private ConfigService configService;
	

//    @Scheduled(cron = "0 */5 0 * * ?")
//    public void execute() {
//        try {
//         /* Constant.TODAY = TimeUtil.today();
//          Constant.SDF_TODAY = TimeUtil.formatDate(Constant.TODAY);
//          logger.info("SDF_TODAY:"+Constant.SDF_TODAY);*/
//          logger.info("TIMING LOAD LOCAL CACHE CONFIG FROM MC CACHE.");
//          Config config = configService.get();
//          if(null != config)
//             Global.config = config;	
//        } catch (Exception e) {
//          logger.error("dailyJob:"+e.toString());
//        }
//    }
}
