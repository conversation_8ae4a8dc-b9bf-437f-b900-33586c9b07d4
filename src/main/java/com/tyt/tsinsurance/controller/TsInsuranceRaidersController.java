package com.tyt.tsinsurance.controller;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportInsuranceRaiders;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.tsinsurance.service.TsInsuranceRaidersService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;

@Controller
@RequestMapping("/plat/ts/insureRaiders")
public class TsInsuranceRaidersController extends BaseController{
	
	@Resource(name="tsInsuranceRaidersService")
	private TsInsuranceRaidersService tsInsuranceRaidersService;
	
	/**
	 * 获取攻略列表
	 * @return
	 */
	@RequestMapping(value = "/getRaidersList.action")
	@ResponseBody
	public ResultMsgBean getRaidersList(){
		ResultMsgBean result = new ResultMsgBean();
		try {
			List<TransportInsuranceRaiders> list = tsInsuranceRaidersService.getList();
			if(list!=null && list.size()>0){
				result.setCode(200);
				result.setData(list);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
		
	}
	/**
	 * 点赞记录接口
	 * @param raidersId 攻略ID
	 * @param userId  用户ID
	 * @param usefulOrUseless 1：useful  2:useless
	 * @return
	 */
	@RequestMapping(value = "/saveInsureRaiders.action")
	@ResponseBody
	public ResultMsgBean saveInsureRaiders (Long raidersId, Long userId ,Integer usefulOrUseless){
		ResultMsgBean result = new ResultMsgBean();
		try {
			if (raidersId == null || raidersId.intValue() <=0) {
				result.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				result.setMsg("insuranceId参数缺失");
				return result;
			}
			if (userId == null || userId.longValue() <=0) {
				result.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				result.setMsg("userId参数缺失");
				return result;
			}
			if (usefulOrUseless == null || usefulOrUseless.intValue() <= 0) {
				result.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				result.setMsg("usefulOrUseless参数缺失");
				return result;
			}
			String todayDate = TimeUtil.formatDate(new Date());
			String val = raidersId.toString()+userId.toString()+todayDate;
			//先去缓存里查用户是否点赞过这条信息 
			String mapValue = RedisUtil.getMapValue(Constant.INSURE_RAIDERS_LIKE_USER, val);
			//如果没有点过，数据库+1，增加到缓存
			if(StringUtils.isBlank(mapValue)){
				String flg="add";
				tsInsuranceRaidersService.updateInsureRaiders(raidersId,usefulOrUseless,flg);
				RedisUtil.mapPut(Constant.INSURE_RAIDERS_LIKE_USER, val, val);
				result.setCode(200);
				result.setMsg("add");
			}else{
				//如果有点过。数据库-1，并且删除这条缓存
				String flg="subtract";
				tsInsuranceRaidersService.updateInsureRaiders(raidersId,usefulOrUseless,flg);
				RedisUtil.mapRemove(Constant.INSURE_RAIDERS_LIKE_USER, val);
				result.setCode(200);
				result.setMsg("subtract");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
		
	}
	
}
