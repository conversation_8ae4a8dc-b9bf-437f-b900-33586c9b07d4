package com.tyt.tsinsurance.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytSequenceService;
import com.tyt.model.HtPayCallBack;
import com.tyt.model.HtPayRecord;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportInsurance;
import com.tyt.tsinsurance.bean.PayRequestBean;
import com.tyt.tsinsurance.bean.PayStatusBean;
import com.tyt.tsinsurance.service.HtPayCallBackService;
import com.tyt.tsinsurance.service.HtPayRecordService;
import com.tyt.tsinsurance.service.TsInsuranceRaidersService;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.MD5Util;
import com.tyt.util.PropertiesUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.Date;

@Controller
@RequestMapping("/plat/ht/pay")
public class HtPayController extends BaseController{
	Logger logger = LoggerFactory.getLogger("htbxpay");
	
	@Resource(name="tsinsuranceService")
	private TsInsuranceService tsinsuranceService;
	
	@Resource(name="htPayCallBackService")
	private HtPayCallBackService htPayCallBackService;
	
	@Resource(name="htPayRecordService")
	private HtPayRecordService htPayRecordService;
	
	@Resource(name="tsInsuranceRaidersService")
	private TsInsuranceRaidersService tsInsuranceRaidersService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;
	
	@Resource(name = "tytSequenceService")
	private TytSequenceService tytSequenceService;
	
	PropertiesUtil propertiesUtil = PropertiesUtil.init("server_url");
	
	String channelCode=propertiesUtil.getString("htbx.pay.channelCode");
	String key=propertiesUtil.getString("htbx.pay.key");
	
	/**
	 * 支付异步回调 不需要保护
	 * @param status
	 * @param orderNo
	 * @param payFinishTime
	 * @param channelCode
	 * @param transactionNo
	 * @param sign
	 * @param payMode
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/asyncCallback.action")
	public void asyncCallback(String status,
			String  orderNo,
			String payFinishTime,
			String channelCode,
			String transactionNo,
			String sign,
			String payMode,HttpServletRequest request, HttpServletResponse response){
		
		logger.info("htbx_request_data_status={},orderNo={},payFinishTime={},channelCode={},transactionNo={},sign={},payMode={}",status,orderNo,payFinishTime,channelCode,
			transactionNo,sign,payMode);
    	//验签名
		String newSign=MD5Util.GetMD5Code(channelCode+orderNo+payMode+transactionNo+key).toUpperCase();
		if(!newSign.equals(sign)){
			logger.info("htbx_request_data_sign_error_newSign={},oldSign={}",newSign,sign);
			printJSON(request,response, "sign_error");
			
			return;
		}
	
		try {
			
			//插入回调记录表
			HtPayCallBack htPayCallBack=new HtPayCallBack();
			htPayCallBack.setChannelCode(channelCode);
			htPayCallBack.setCtime(new Date());
			htPayCallBack.setOrderNo(orderNo);
			htPayCallBack.setPayFinishTime(TimeUtil.parseDateString(payFinishTime));
			htPayCallBack.setPayMode(payMode);
			htPayCallBack.setSign(sign);
			htPayCallBack.setTransactionNo(transactionNo);
			htPayCallBack.setUtime(new Date());
			htPayCallBack.setStatus(Integer.parseInt(status));
			htPayCallBackService.add(htPayCallBack);
			logger.info(htPayCallBack.getOrderNo()+"支付回调表保存成功！");
			
			htPayCallBackService.saveAsyncCallback(status,orderNo,payFinishTime,
					channelCode,transactionNo,sign,payMode);
			
			printJSON(request,response, "success");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("htbx_request_callback_error",e);
			printJSON(request,response, "error");
		}
		return;
	}
	
	
	
	
	/**
	 * 获得支付时需要的信息与签名 需要保护
	 * @param id 
	 * @return
	 */
	@RequestMapping(value = "/getPayInfo.action")
	public void getPayInfo(
			@RequestParam(value="id", required=true) Long id
			,HttpServletRequest request, HttpServletResponse response
			){
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.ERROR);
		rm.setMsg("服务器错误");
		try {
			String alipay_showUrl=tytConfigService.getStringValue("tyt_insurance_alipay_showUrl", "");
			String actionUrl=tytConfigService.getStringValue("tyt_insurance_pay_request_url", "http://wxsx.pc.ehuatai.com/WPP_Interface_Test/payChoose");
			String environmental=tytConfigService.getStringValue("tyt_insurance_environmental", "2");
			
			TransportInsurance transportInsurance=tsinsuranceService.getById(id);
			PayRequestBean payRequestBean=new PayRequestBean();
			payRequestBean.setChannelCode(channelCode);
			payRequestBean.setTradeName("华泰保险");
			payRequestBean.setTradeType("02");
			payRequestBean.setOrderNo(transportInsurance.getOrderNo());
			//测试环境支付1分钱
			if("2".equals(environmental)){
				payRequestBean.setMoney("0.01");
			}else
			payRequestBean.setMoney(new BigDecimal(transportInsurance.getPremiumCurrency()).movePointLeft(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
			
			payRequestBean.setTradeTime(TimeUtil.formatDateTime(new Date()));
			payRequestBean.setShowUrl(alipay_showUrl);
			
			payRequestBean.setActionUrl(actionUrl);
			
			payRequestBean.setSign(MD5Util.GetMD5Code(payRequestBean.getChannelCode()+payRequestBean.getMoney()+payRequestBean.getOrderNo()+payRequestBean.getTradeTime()+key).toUpperCase());
/*			payRequestBean.setIsAliPay();
			payRequestBean.setIsChinaPay();
			payRequestBean.setIsWXClinet();
			payRequestBean.setIsWXH5();
			payRequestBean.setSpbillCreateIp();
			payRequestBean.setSceneInfo();*/
			payRequestBean.setSpbillCreateIp(getRealIp(request));
			
			logger.info("htbx_pay_request_Date="+JSON.toJSONString(payRequestBean));
			//保存支付表
			HtPayRecord htPayRecord =new HtPayRecord();
			htPayRecord.setCtime(new Date());
			
			htPayRecord.setMoney(transportInsurance.getPremiumCurrency());
			
			htPayRecord.setOrderNo(transportInsurance.getOrderNo());
			//htPayRecord.setPayNo(tytSequenceService.updateGetNumberForDateTime(channelCode, TimeUtil.formatDateTimeForyyyyMMddHHmmss(new Date()), "tyt_insurance_pay_orders", 8));
			//htPayRecord.setPayTime();
			htPayRecord.setStatus(0);
			htPayRecord.setTiId(transportInsurance.getId());
			htPayRecord.setUtime(new Date());
			htPayRecordService.add(htPayRecord);
			
			rm.setCode(ResultMsgBean.OK);
			rm.setMsg("查询成功");
			rm.setData(payRequestBean);
			
			printJSON(request,response, rm);

			return;
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			printJSON(request,response, rm);
		}
		

		return;
	}

	/** 
	 * 通过订单号获得保单信息  需要保护
	 * @param orderNo  订单号
	 * @return
	 */
	@RequestMapping(value = "/getInsuranceInfo.action")
	public void getInsuranceInfo(
			@RequestParam(value="orderNo", required=true) String orderNo
			,HttpServletRequest request, HttpServletResponse response
			){
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.ERROR);
		rm.setMsg("服务器错误");
		try {
			String filePathName=tytConfigService.getStringValue("tyt_out_net_file_path_name_url");
			TransportInsurance insurance = tsinsuranceService.getTransportInsuranceForOrderNo(orderNo);
			insurance.setImgUrl(filePathName+insurance.getImgUrl());
			rm.setData(insurance);
			rm.setCode(ResultMsgBean.OK);
			rm.setMsg("查询成功");
			rm.setTime(String.valueOf(System.currentTimeMillis()));
			printJSON(request,response, rm);

			return;
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");

			printJSON(request,response, rm);
		}

		return;
	
	}
	
	
	protected void printJSON(HttpServletRequest servletRequest,
            HttpServletResponse servletResponse, Object obj) {
			PrintWriter writer = null;
			try {
					servletResponse.setContentType("text/html;charset=UTF-8");   
					servletResponse.setCharacterEncoding("UTF-8");
					writer = servletResponse.getWriter();
					String jsoncallback = servletRequest.getParameter("jsoncallback");
					if (null == jsoncallback || jsoncallback.isEmpty()) {
					writer.print(JSON.toJSONString(obj));
					} else {
					writer.print(jsoncallback + "(" + JSON.toJSONString(obj) + ")");
					}
				} catch (Exception e) {
						e.printStackTrace();
						logger.error("输出信息失败！" + e.getMessage());
					} finally {
						if (null != writer) {
							writer.close();
						}
				}
	}
	
	
	   public static String getRealIp(HttpServletRequest request) {
	        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址
	        String ip = request.getHeader("X-Forwarded-For");
	        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	                ip = request.getHeader("Proxy-Client-IP");

	            }
	            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	                ip = request.getHeader("WL-Proxy-Client-IP");

	            }
	            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	                ip = request.getHeader("HTTP_CLIENT_IP");

	            }
	            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	                ip = request.getHeader("HTTP_X_FORWARDED_FOR");

	            }
	            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	                ip = request.getRemoteAddr();
	            }
	        } else if (ip.length() > 15) {
	            String[] ips = ip.split(",");
	            for (int index = 0; index < ips.length; index++) {
	                String strIp = (String) ips[index];
	                if (!("unknown".equalsIgnoreCase(strIp))) {
	                    ip = strIp;
	                    break;
	                }
	            }
	        }
	        if("0:0:0:0:0:0:0:1".equals(ip)){
	            ip = "127.0.0.1";
	        }
	        return ip;

	    }
	   /**
	     * 获得保单支付状态
	     * @param userId
	     * @param ticket
	     * @param insurance
	     * @return
	     */
		@RequestMapping(value = "/getPayStatus")
		@ResponseBody
		public ResultMsgBean getPayStatus(Long id ){
			ResultMsgBean rm = new ResultMsgBean();
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("保存成功");
			try {
				if (id==null) {
					rm.setCode(201);
	                rm.setMsg("保险ID不能为空");
	                return rm;
				}
				PayStatusBean payStatusBean = htPayCallBackService.getPayStatusBean(id);
				if(null == payStatusBean){
					rm.setCode(201);
					rm.setMsg("保险ID不存在");
					return rm;
				}
				rm.setData(payStatusBean);
			} catch (Exception e) {
				e.printStackTrace();
				rm.setCode(ReturnCodeConstant.ERROR);
				rm.setMsg("保单保存失败");
			}
			return rm;
		}
}
