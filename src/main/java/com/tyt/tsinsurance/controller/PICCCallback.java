package com.tyt.tsinsurance.controller;

import com.tyt.model.HtPayCallBack;
import com.tyt.tsinsurance.bean.picc.CallbackPolicyInfo;
import com.tyt.tsinsurance.bean.picc.CallbackResponseBean;
import com.tyt.tsinsurance.bean.picc.CallbackWrapper;
import com.tyt.tsinsurance.bean.picc.PICCContants;
import com.tyt.tsinsurance.service.HtPayCallBackService;
import com.tyt.util.AESUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.codehaus.jackson.map.DeserializationConfig;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("/insurance/picc")
@Controller
public class PICCCallback {
    private static final Logger logger = LoggerFactory.getLogger(PICCCallback.class);

    private static final String DATE_FORMAT_SECOND = "yyyy-MM-dd HH:mm:ss"; //2018-11-27 15:38:23

    @Autowired
    private HtPayCallBackService callBackService;

    @Autowired
    private HtPayCallBackService htPayCallBackService;



    @RequestMapping(value = "/callback", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, CallbackResponseBean> callback(@RequestBody String data){
        Map<String, CallbackResponseBean> responseBeanMap = new HashMap<>();
        CallbackResponseBean resultData = new CallbackResponseBean();
        responseBeanMap.put("head", resultData);

        /**
         * {
         *   "head": {
         *     "transactionNo": "",
         *     "operator": "S10000026",
         *     "aiBaoTransactionNo": "",
         *     "timeStamp": "",
         *     "errorCode": "0000",
         *     "errorMsg": "成功",
         *     "system ": " S10000026"
         *   },
         *   "body": {
         *     "policyInfo": [
         *       {
         *         "orderNo": "1543309664",
         *         "policyNo": "PYDG201835020097E71501",
         *         "insurerKey": "1543309664#tyt",
         *         "errorCode": "0000",
         *         "errorMsg": "成功",
         *         "proposalNo": "",
         *         "proposalTime": "",
         *         "policyTime": "2018-11-27 17:08:56",
         *         "productId": "ABX10000084"
         *       }
         *     ]
         *   }
         * }
         */
        try {
            logger.info("encrypted picc callback is {}", data);
            data = AESUtil.decrypt(data, AESUtil.KEY);
            logger.info("decrypted picc callback is {}", data);
            if(StringUtils.isNotEmpty(data)) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                CallbackWrapper callbackWrapper = objectMapper.readValue(data, CallbackWrapper.class);
                if(callbackWrapper.getHead() != null
                        && callbackWrapper.getHead().getErrorCode() != null
                        && PICCContants.SUCCESS_CODE.equals(callbackWrapper.getHead().getErrorCode())
                        && callbackWrapper.getBody() != null
                        && callbackWrapper.getBody().getPolicyInfo() != null){
                    List<CallbackPolicyInfo> policyInfoLst = callbackWrapper.getBody().getPolicyInfo();
                    CallbackPolicyInfo policyInfo = policyInfoLst.get(0);
                    String policyNo = policyInfo.getPolicyNo();
                    String orderNo = policyInfo.getOrderNo();
                    String policyTime = policyInfo.getPolicyTime();
                    String status = "1"; //投保成功肯定支付成功
                    String payType = policyInfo.getPayType(); //1微信，2支付宝，3银联
                    String payMode = "22"; //支付方式支付宝23，微信22，银联25
                    if(StringUtils.isNotEmpty(payType)){
                        if("1".equals(payType)){
                            payMode = "22";
                        }else if("2".equals(payType)){
                            payMode = "23";
                        }else if("3".equals(payType)){
                            payMode = "25";
                        }
                    }
                    try {
                        //插入回调记录表
                        HtPayCallBack htPayCallBack=new HtPayCallBack();
                        htPayCallBack.setChannelCode(PICCContants.CHANNEL_ID);
                        htPayCallBack.setCtime(new Date());
                        htPayCallBack.setOrderNo(orderNo);
                        htPayCallBack.setPayFinishTime(TimeUtil.parseDateString(policyTime));
                        htPayCallBack.setPayMode(payMode);
//                        htPayCallBack.setSign(sign);
                        htPayCallBack.setTransactionNo(policyNo);
                        htPayCallBack.setUtime(new Date());
                        htPayCallBack.setStatus(Integer.parseInt(status));
                        htPayCallBackService.add(htPayCallBack);
                        logger.info(htPayCallBack.getOrderNo()+"支付回调表保存成功！");
                        callBackService.doPICCCallback(status, orderNo, policyTime, policyNo, payMode);
                    } catch (Exception e) {
                        logger.error("更新数据库错误！{}", ExceptionUtils.getStackTrace(e));
                    }

                }else {
                    resultData.setErrorCode("0001");
                    resultData.setErrorMsg("回调数据有误! callback data is "  + data);
                }
            }
        } catch (IOException e) {
            logger.error("{}", ExceptionUtils.getStackTrace(e));
            resultData.setErrorCode("0001");
            resultData.setErrorMsg("反序列化回调数据出错! callback data is "  + data);
        }

        return responseBeanMap;
    }


}
