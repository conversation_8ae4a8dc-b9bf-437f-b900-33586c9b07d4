package com.tyt.tsinsurance.controller;

import javax.annotation.Resource;

import com.tyt.config.util.AppConfig;
import com.tyt.tsinsurance.SaleReturnActivityBean;
import com.tyt.tsinsurance.bean.*;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportInsurance;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/plat/ts/insurance")
public class TsInsuranceController extends BaseController{

    public static Logger logger = LoggerFactory.getLogger(TsInsuranceController.class);

	@Resource(name="tsinsuranceService")
	private TsInsuranceService tsinsuranceService;
	@Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
	
	/**
	 * 保险查看详情接口
	 * @param insuranceId
	 * @return
	 */
	@RequestMapping(value = "/getDetail")
	@ResponseBody
	public ResultMsgBean getTsInsuranceDetail(
			@RequestParam(value="insuranceId", required=true) Long insuranceId){
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("查询成功");
		try {
			String filePathName=tytConfigService.getStringValue("tyt_out_net_file_path_name_url");
			TransportInsurance insuranceNew= new TransportInsurance();
			TransportInsurance insurance = tsinsuranceService.getById(insuranceId);
			//电子保单图片文件路径
			String imgUrl = insurance.getImgUrl();
            //电子保单pdf文件是否存在
            if(StringUtils.isNotBlank(imgUrl))
            {
                insurance.setImgUrl(filePathName+insurance.getImgUrl());
            }else{
                insurance.setImgUrl("");
            }
			BeanUtils.copyProperties(insurance, insuranceNew);
			rm.setData(insuranceNew);
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	/**
	 * 保险查看详情接口(h5)
	 * @param insuranceId
	 * @return
	 */
	@RequestMapping(value = "/getDetail.action")
	@ResponseBody
	public ResultMsgBean getInsuranceDetail(
			@RequestParam(value="insuranceId", required=true) Long insuranceId){
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("查询成功");
		try {
			String filePathName=tytConfigService.getStringValue("tyt_out_net_file_path_name_url");
			TransportInsurance insuranceNew= new TransportInsurance();
			TransportInsurance insurance = tsinsuranceService.getById(insuranceId);
			//电子保单图片文件路径
            String imgUrl = insurance.getImgUrl();
            //电子保单pdf文件是否存在
            if(StringUtils.isNotBlank(imgUrl))
            {
                insurance.setImgUrl(filePathName+insurance.getImgUrl());
            }else{
                insurance.setImgUrl("");
            }
			BeanUtils.copyProperties(insurance, insuranceNew);
			rm.setData(insuranceNew);
			rm.setTime(String.valueOf(System.currentTimeMillis()));
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

    /**
     * 保存保单
     * @param userId
     * @param ticket
     * @param insurance
     * @return
     */
	@RequestMapping(value = "/saveInsurance")
	@ResponseBody
	public ResultMsgBean saveInsurance(@RequestParam(value="userId", required=true) Long userId,
			@RequestParam(value="ticket", required=true) String ticket,InsuranceSaveBean insurance,Integer clientVersion){
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("保存成功");
		try {
            //保险大类 type  1：华泰货运险 2：人保货运险  3：平安保险
            Integer type = insurance.getType();
            if (clientVersion<6010){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("请下载最新版本到保险大厅中购买货运险");
                return rm;
            }
            if (type!=3){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("请下载最新版本到保险大厅中购买货运险");
                return rm;
            }
			ResultMsgBean checkBlank = tsinsuranceService.checkBlank(insurance);
			if (checkBlank.getCode()!=ReturnCodeConstant.OK) {
				return checkBlank;
			}
			//保费计算公式需要根据是华泰货运险还是人保货运险进行区分
			CalcCostBean calcCost = new CalcCostBean();
			//如果为人保货运险
			if(type != null && type.intValue() == 2)
			{
				//2019.3.7改为和华泰相同的保费计算规则
				calcCost = tsinsuranceService.calcCost(insurance.getAmtCurrency(), insurance.getStartProvinc(), insurance.getStartCity(), insurance.getStartArea(),
						                                   insurance.getDestProvinc(), insurance.getDestCity(), insurance.getDestArea());
			}else{ //平安
				calcCost = tsinsuranceService.pingAnPremiumCalculation(insurance.getAmtCurrency(), insurance.getStartProvinc(), insurance.getStartCity(), insurance.getStartArea(),
						                                   insurance.getDestProvinc(), insurance.getDestCity(), insurance.getDestArea());
			}
			if(calcCost == null) { // 如果距离为空，则返回失败
                logger.info("未查询到{}{}{}-{}{}{}距离",insurance.getStartProvinc(), insurance.getStartCity(), insurance.getStartArea(), insurance.getDestProvinc(), insurance.getDestCity(), insurance.getDestArea());
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("网络繁忙，请稍后重试");
                return rm;
            }
			BigDecimal premiumCurrencyParam = new BigDecimal(insurance.getPremiumCurrency());
			BigDecimal calcPremiumCurrency = calcCost.getPremiumCurrency();
			logger.info("传入的保费金额premiumCurrencyParam为："+premiumCurrencyParam);
			logger.info("计算出的保费金额calcPremiumCurrency为："+calcPremiumCurrency);
			if (premiumCurrencyParam.compareTo(calcPremiumCurrency) != 0) {
				rm.setCode(201);
                rm.setMsg("保费计算错误，请重新计算");
                return rm;
			}
			Map<String,Object> insuranceMap = tsinsuranceService.saveInsurance(insurance);
			//保单Id
			Long insuranceId = Long.valueOf(String.valueOf(insuranceMap.get("insuranceId")));
			//旧版接口：返回数据保单Id
			rm.setData(insuranceId);
			//新版接口：增加人保货运险，增加返回数据
			rm.setExtraData(insuranceMap);
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			//返回投保失败的原因
			rm.setMsg("保单保存失败:"+e.getMessage());
		}
		return rm;
	}
	/**
	 * 修改保单
	 * @param userId
	 * @param ticket
	 * @param insurance
	 * @return
	 */
	@RequestMapping(value = "/updateInsurance")
	@ResponseBody
	public ResultMsgBean updateInsurance(@RequestParam(value="userId", required=true) Long userId,
			@RequestParam(value="ticket", required=true) String ticket,InsuranceSaveBean insurance){
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("保存成功");
		try {
			ResultMsgBean checkBlank = tsinsuranceService.checkBlank(insurance);
			if (checkBlank.getCode()!=ReturnCodeConstant.OK) {
				return checkBlank;
			}
			if (insurance.getId()!=null) {
				TransportInsurance ins = tsinsuranceService.getById(insurance.getId());
				if (ins.getStatus()==1) {
					rm.setCode(202);
					rm.setMsg("此订单已支付成功");
					return rm;
				}
				Map<String,Object> insuranceMap = tsinsuranceService.updateInsurance(insurance);
				//保单Id
				Long insuranceId = Long.valueOf(String.valueOf(insuranceMap.get("insuranceId")));
				//旧版接口：返回数据保单Id
				rm.setData(insuranceId);
				//新版接口：增加人保货运险，增加返回数据
				rm.setExtraData(insuranceMap);
			}else{
				rm.setCode(201);
				rm.setMsg("保单id获取不到");
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("保单保存失败");
		}
		return rm;
	}
	/**
	 * 列表删除
	 * @param insuranceId  保单ID
	 * @return
	 */
	@RequestMapping(value = "/delInsurance.action")
	@ResponseBody
	public ResultMsgBean delInsurance(Long insuranceId) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			if(insuranceId!=null && insuranceId.longValue()>0){
				tsinsuranceService.delInsurance(insuranceId);
				result.setCode(200);
				result.setMsg("删除成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("异常错误信息");
		}
		return result;
		
	}

	/**
	 * 查询保单列表
	 * @param userId 用户ID
	 * @param ticket 仅校验
	 * @param opt 操作类型  0.全部  1.待支付  2.生效中  3.已失效
	 * @param lastId 最后一个保单ID
	 * @return
	 */
	@RequestMapping(value = "/mylist.action")
	@ResponseBody
	public Object myList(@RequestParam(value = "userId", required = true) Long userId,
							  @RequestParam(value = "ticket", required = true) String ticket,
							  @RequestParam(value = "opt", required = true) Integer opt,
							  @RequestParam(value = "lastId", required = true) Long lastId
							  ) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("查询成功");
		rm.setTime(String.valueOf(System.currentTimeMillis()));
		try {
			TsInsuranceListBean bean = tsinsuranceService.getMyList(userId, opt, lastId);
			rm.setData(bean);
		} catch (Exception e) {
		    logger.error("查询保单列表异常，userId:"+userId, e);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("网络繁忙，请稍后重试");
		}
		return rm;
	}

    /**
     * 查询保单列表
     * @param userId 用户ID
     * @param ticket 仅校验
     * @param opt 操作类型  1.我是车方  2.我是货方
     * @return
     */
    @RequestMapping(value = {"/import/waybill", "/import/waybill.action"})
    @ResponseBody
    public Object importWaybill(@RequestParam(value = "userId", required = true) Long userId,
                                @RequestParam(value = "ticket", required = true) String ticket,
                                @RequestParam(value = "opt", required = true) Integer opt
    ) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
			WaybillBean waybillBean = new WaybillBean();
			int pageSize=0;
            if (opt == 1) { // 车方
                pageSize = tytConfigService.getIntValue("car_insur_import_page_size",30);
            } else if (opt == 2) { // 货方
                pageSize = tytConfigService.getIntValue("goods_insur_import_page_size",30);
            }
			waybillBean.setMaximum(pageSize);

            List<ImportWaybillBean> beans = tsinsuranceService.importWaybill( userId, opt);
			waybillBean.setList(beans);
            if(beans == null) { // 如没有数据返回空数组
				waybillBean.setList(new ArrayList<ImportWaybillBean>());
            }
			rm.setData(waybillBean);
        } catch (Exception e) {
            logger.error("导入运单列表异常，userId:"+userId, e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络繁忙，请稍后重试");
        }
        return rm;
    }

    /**
     * 计算保费
     * @param userId
     * @param ticket
     * @param amtCurrency 保额
     * @param startProvinc 出发地 省
     * @param startCity 出发地 市
     * @param startArea 出发地 区
     * @param destProvinc 目的地 省
     * @param destCity 目的地 市
     * @param destArea 目的地 区
     * @return
     */
    @RequestMapping(value = "/calc/cost")
    @ResponseBody
    public Object calcCost(@RequestParam(value = "userId", required = true) Long userId,
                                @RequestParam(value = "ticket", required = true) String ticket,
                                @RequestParam(value = "amtCurrency", required = true) String amtCurrency,
                                @RequestParam(value = "startProvinc", required = true) String startProvinc,
                                @RequestParam(value = "startCity", required = true) String startCity,
                                @RequestParam(value = "startArea", required = true) String startArea,
                                @RequestParam(value = "destProvinc", required = true) String destProvinc,
                                @RequestParam(value = "destCity", required = true) String destCity,
                                @RequestParam(value = "destArea", required = true) String destArea,
						        @RequestParam(value = "type", required = false) Integer type,
						        @RequestParam(value = "category", required = false) Integer category
    ) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            CalcCostBean bean=tsinsuranceService.pingAnPremiumCalculation(amtCurrency, startProvinc, startCity, startArea, destProvinc, destCity, destArea);
			//判断保险大类 type  1：华泰货运险 2：人保货运险
			//如果为人保货运险
			/*if(type != null && type.intValue() == 2)
			{
				//2019.3.7改为和华泰相同的保费计算规则
				bean = tsinsuranceService.calcCost(amtCurrency,startProvinc,startCity,startArea,
						                                        destProvinc,destCity,destArea);
			}else{
				bean = tsinsuranceService.calcCost(amtCurrency,startProvinc,startCity,startArea,
						                                        destProvinc,destCity,destArea);
			}*/
            if(bean == null) { // 如果距离为空，则返回失败
                logger.info("未查询到{}{}{}-{}{}{}距离",startProvinc,startCity,startArea,destProvinc,destCity,destArea);
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("网络繁忙，请稍后重试");
                return rm;
            }
            rm.setData(bean);
        } catch (Exception e) {
            logger.error("计算保费异常，userId:"+userId, e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络繁忙，请稍后重试");
        }
        return rm;
    }
    
    /**
     * 立即支付
     * @param insuranceId
     * @return
     */
    @RequestMapping(value="/immediatePay.action")
    @ResponseBody
    public ResultMsgBean immediatePay(@RequestParam(value="insuranceId", required=true) Long insuranceId) {
    	 ResultMsgBean rm = new ResultMsgBean();
         
         try {
        	 TransportInsurance insurance = tsinsuranceService.getById(insuranceId);
        	 if (insurance.getDelStatus()>0) {
        		 rm.setCode(202);
        		 rm.setMsg("该订单已删除");
        		 return rm;
			}
        	 if (insurance.getStatus()==1) {
        		 rm.setCode(203);
        		 rm.setMsg("该订单已支付");
        		 return rm;
			}
             Date now=new Date();
        	 Date addDay = TimeUtil.addDay(insurance.getStartTime(), 1);
        	 Date startTime = TimeUtil.weeHours(addDay, 0);
             if (insurance.getType() ==3 ){
                 long expireDate = insurance.getCtime().getTime()+30*60*1000;
                 if (expireDate>now.getTime()){
                     rm.setCode(ReturnCodeConstant.OK);
                     rm.setMsg("跳转到支付页面");
                     return rm;
                 }else{
                     rm.setCode(204);
                     rm.setMsg("支付链接已失效");
                     return rm;
                 }
             }else{
                 if (now.getTime()<startTime.getTime()) {
                     rm.setCode(ReturnCodeConstant.OK);
                     rm.setMsg("跳转到支付页面");
                     return rm;
                 }
             }
             rm.setCode(201);
             rm.setMsg("跳转到编辑页面，修改起运时间");
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(500);
			rm.setMsg("异常错误信息");
		}
         return rm;
    }

    /**
     * 保险返利活动  保单数量&投保钱数
     * @param userId
     * @return
     */
    @RequestMapping(value="/activity.action")
    @ResponseBody
    public ResultMsgBean saleReturnAcivity(@RequestParam(value="userId", required=true) Long userId) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            SaleReturnActivityBean bean = tsinsuranceService.getCountAndCurrency(userId);
            rm.setData(bean);
        } catch (Exception e) {
            e.printStackTrace();
            rm.setCode(500);
            rm.setMsg("异常错误信息");
        }
        return rm;
    }
}
