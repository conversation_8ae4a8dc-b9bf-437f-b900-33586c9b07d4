package com.tyt.tsinsurance.controller;

import com.tyt.model.HtPayCallBack;
import com.tyt.model.TransportInsurance;
import com.tyt.tsinsurance.bean.pingan.PinganAcceptBean;
import com.tyt.tsinsurance.bean.pingan.PinganPayResponseBean;
import com.tyt.tsinsurance.service.HtPayCallBackService;
import com.tyt.tsinsurance.service.PinganInsuranceService;
import com.tyt.tsinsurance.service.TsInsuranceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;

@RequestMapping("/insurance/pingan")
@Controller
public class PinganPayController {
    private static final Logger logger = LoggerFactory.getLogger(PinganPayController.class);

    @Autowired
    private PinganInsuranceService pinganInsuranceService;
    @Autowired
    private HtPayCallBackService callBackService;
    @Autowired
    private TsInsuranceService tsInsuranceService;

    @RequestMapping(value = "/callback.action")
    @ResponseBody
    public PinganPayResponseBean pinganCallBack(@RequestBody PinganAcceptBean callBackBean){
        logger.info("pingan_callback_request_param:"+callBackBean.toString());
        PinganPayResponseBean responseBean = new PinganPayResponseBean();
        try {
            TransportInsurance tsi = tsInsuranceService.getByInsuranceNo(callBackBean.getApplyPolicyNo());
            //插入回调记录表
            HtPayCallBack htPayCallBack=new HtPayCallBack();
            htPayCallBack.setChannelCode("MP09110010");
            htPayCallBack.setCtime(new Date());
            htPayCallBack.setOrderNo(tsi.getOrderNo());
            htPayCallBack.setPayFinishTime(new Date());
            htPayCallBack.setTransactionNo(callBackBean.getApplyPolicyNo());
            htPayCallBack.setUtime(new Date());
            htPayCallBack.setStatus(1);
            callBackService.add(htPayCallBack);
            logger.info(htPayCallBack.getOrderNo()+"支付回调表保存成功！");
            pinganInsuranceService.savePinganCallBack(tsi,callBackBean);
        }catch (Exception e){
            responseBean.setResponseCode("0");
            responseBean.setResponseMessage("支付回调失败");
            e.printStackTrace();
        }
        return responseBean;
    }
}
