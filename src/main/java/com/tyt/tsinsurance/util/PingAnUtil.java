package com.tyt.tsinsurance.util;

import com.alibaba.fastjson.JSONObject;
import com.tyt.htbx.APPPolicy;
import com.tyt.model.TransportInsurance;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.service.common.snowflake.SnowflakeId;
import com.tyt.tsinsurance.bean.CalcCostBean;
import com.tyt.tsinsurance.bean.InsuranceSaveBean;
import com.tyt.tsinsurance.bean.pingan.*;
import com.tyt.util.Constant;
import com.tyt.util.IdCardUtil;
import com.tyt.util.PropertiesUtil;
import com.tyt.util.TimeUtil;
import com.tyt.util.httputil.HttpUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/18 16:22
 */
public class PingAnUtil {
    static PropertiesUtil server = PropertiesUtil.init("server");
    public static InsureReturn pingAnInsure(InsuranceSaveBean insurance, TransportInsurance tsInsurance, StringBuffer goodsName, StringBuffer viaLoc, CalcCostBean calcCost) {
        Map<String,Object> map=new HashMap<>();
        map.put("partnerName","P_BJBLD_GP");
        map.put("departmentCode","2241599");
        map.put("transferCode","APPLY");
//        map.put("tradeSubdivision","224");
        map.put("schemeName","");
        map.put("transSerialNo", SnowflakeId.generateId());
        Contract contract=new Contract();
        //投保人信息
        List<ApplicantInfoList> applicantInfoList =new ArrayList<>();
        ApplicantInfoList applicantInfo=new ApplicantInfoList();
        if (insurance.getApplicantType() == 1){
            applicantInfo.setPersonnelType("1");
            applicantInfo.setBirthday(IdCardUtil.getBirthday(tsInsurance.getApplicantId()));
            applicantInfo.setSexCode(IdCardUtil.getGender(tsInsurance.getApplicantId()).equals(1) ? "M" : "F");
            applicantInfo.setAge(Short.parseShort(IdCardUtil.evaluate(tsInsurance.getApplicantId())));
            applicantInfo.setCertificateType("01");
        }else{
            applicantInfo.setPersonnelType("0");
            applicantInfo.setCertificateType("03");
        }
        applicantInfo.setName(tsInsurance.getApplicantName());
        applicantInfo.setCertificateNo(tsInsurance.getApplicantId());
        applicantInfo.setLinkManName(tsInsurance.getApplicantName());
        applicantInfo.setNationality("中国");
        applicantInfo.setAddress("暂无");
        applicantInfo.setMobileTelephone(tsInsurance.getApplicantPhone());
        applicantInfoList.add(applicantInfo);
        contract.setApplicantInfoList(applicantInfoList);
        //被保人信息
        List<InsurantInfoList> insurantInfoList=new ArrayList<>();
        InsurantInfoList insurantInfo=new InsurantInfoList();
        if (insurance.getInsuredType() == 1){
            insurantInfo.setPersonnelType("1");
            insurantInfo.setCertificateType("01");
            insurantInfo.setBirthday(IdCardUtil.getBirthday(tsInsurance.getInsuredId()));
            insurantInfo.setSexCode(IdCardUtil.getGender(tsInsurance.getInsuredId()).equals(1) ? "M" : "F");
            insurantInfo.setAge(Short.parseShort(IdCardUtil.evaluate(tsInsurance.getInsuredId())));
        }else{
            insurantInfo.setPersonnelType("0");
            insurantInfo.setCertificateType("03");
        }
        insurantInfo.setName(tsInsurance.getInsuredName());
        insurantInfo.setCertificateNo(insurance.getInsuredId());
        insurantInfo.setLinkManName(tsInsurance.getInsuredName());
        insurantInfo.setAddress("暂无");
        insurantInfo.setMobileTelephone(tsInsurance.getInsuredPhone());
        insurantInfoList.add(insurantInfo);
        contract.setInsurantInfoList(insurantInfoList);
        //baseInfo
        Map<String,Object> baseInfo=new HashMap<>();
        baseInfo.put("productCode","MP09110010");
        baseInfo.put("inputNetworkFlag","internet");
        baseInfo.put("totalInsuredAmount",Double.valueOf(calcCost.getAmtCurrency().movePointRight(4)+""));
        baseInfo.put("expectRate",calcCost.getPremium()+"");
        baseInfo.put("dataSource","openApi");
        baseInfo.put("inputBy",insurance.getTrueName());
        contract.setBaseInfo(baseInfo);
        //extendInfo
        Map<String,Object> extendInfo=new HashMap<>();
        extendInfo.put("isPolicyBeforePayfee","1");
        contract.setExtendInfo(extendInfo);
        //riskGroupInfoList 标的组信息
        List<Map<String,Object>> riskGroupInfoList=new ArrayList<>();
        Map<String,Object> iskGroupInfoMap=new HashMap<>();
        List<Map<String,Object>> riskPropertyInfoList=new ArrayList<>();
        Map<String,Object> riskPropertyMap=new HashMap<>();
        riskPropertyMap.put("cargoBigType","16");
        riskPropertyMap.put("cargoDetailType","8429");
        riskPropertyMap.put("conveyanceType","01");
        riskPropertyMap.put("cargoRiskDepict",goodsName.toString());
        Map<String,Object> cargoPort=new HashMap<>();
        cargoPort.put("startPortProvince",insurance.getStartPortProvinceCode());
        cargoPort.put("startPortCity",insurance.getStartPortCityCode());
        cargoPort.put("startPortDepict",tsInsurance.getStartPoint()+tsInsurance.getStartCity()+tsInsurance.getStartArea());
        cargoPort.put("terminiProvince",insurance.getTerminiProvinceCode());
        cargoPort.put("terminiCity",insurance.getTerminiCityCode());
        cargoPort.put("terminiDepict",tsInsurance.getDestPoint()+tsInsurance.getDestCity()+tsInsurance.getDestArea());
        cargoPort.put("approachPortDepict",viaLoc.toString());
        //途经港可为空
        //cargoPort.put("approachPortDepict","openApi");
        riskPropertyMap.put("cargoPort",cargoPort);
        Map<String,Object> riskProperty=new HashMap<>();
        riskProperty.put("riskPropertyMap",riskPropertyMap);
        riskPropertyInfoList.add(riskProperty);
        //planInfoList
        List<Map<String,Object>> planInfoList=new ArrayList<>();
        Map<String,Object> isMain=new HashMap<>();
        isMain.put("isMain","1");
        isMain.put("planCode","PL0900025");
        planInfoList.add(isMain);
        iskGroupInfoMap.put("riskPropertyInfoList",riskPropertyInfoList);
        iskGroupInfoMap.put("planInfoList",planInfoList);
        riskGroupInfoList.add(iskGroupInfoMap);
        contract.setRiskGroupInfoList(riskGroupInfoList);
        //extendGroupInfo
        Map<String,Object> extendGroupInfo=new HashMap<>();
        ExtendDetailMap extendDetailMap=new ExtendDetailMap();
        extendDetailMap.setCargoInfo(tsInsurance.getTaskContent1());
        extendDetailMap.setCasingFashion("f7");
//        if (tsInsurance.getTsId() !=null ){
//            extendDetailMap.setConveyanceNo(tsInsurance.getTsId()+"");
//        }
        extendDetailMap.setLicensePlate(tsInsurance.getHeadNo());
        extendDetailMap.setConsignorDate(TimeUtil.formatDate(tsInsurance.getStartTime()));
        extendDetailMap.setInvoiceMoney(Double.valueOf(new BigDecimal(tsInsurance.getAmtCurrency()).movePointRight(4)+""));
        extendDetailMap.setRatePrintFormat("01");
        extendDetailMap.setInsurancePrintFormat("01");
        extendGroupInfo.put("extendDetailMap",extendDetailMap);
        contract.setExtendGroupInfo(extendGroupInfo);
        map.put("contract",contract);

        String url = server.getString("pingan.insurance.insure");
        String token =RedisUtil.get(Constant.PING_AN_TOKEN_KEY);
        if (StringUtils.isEmpty(token)){
            token = PingAnUtil.getPingAnToken();
        }
        System.out.println("投保参数insurance："+JSONObject.toJSONString(insurance));
        System.out.println("投保参数tsInsurance："+JSONObject.toJSONString(tsInsurance));
        System.out.println("投保参数calcCost："+JSONObject.toJSONString(calcCost));
        String newUrl=url+"?access_token="+token+"&request_id=applyForFee"+System.currentTimeMillis();
        System.out.println("平安投保参数："+JSONObject.toJSONString(map));
        String s = HttpUtil.doPost(newUrl, JSONObject.toJSONString(map));
        System.out.println("----------------------返回结果："+s);
        InsureReturn insureReturn = JSONObject.parseObject(s, InsureReturn.class);
        if ("12306".equals(insureReturn.getRet())){
            String pingAnToken = PingAnUtil.getPingAnToken();
            String s1 = HttpUtil.doPost(url + "?access_token=" + pingAnToken + "&request_id=applyForFee" + System.currentTimeMillis(), JSONObject.toJSONString(map));
            insureReturn=JSONObject.parseObject(s1, InsureReturn.class);
        }else if ("13012".equals(insureReturn.getRet())){
            RedisUtil.del(Constant.PING_AN_TOKEN_KEY);
        }
        System.out.println(JSONObject.toJSONString("投保返回参数insureReturn："+ JSONObject.toJSONString(insureReturn)));
        return insureReturn;
    }

    public static String getPingAnToken() {
        Map<String,String> result=new HashMap<>();
        result.put("client_id", server.getString("pingan.insurance.client_id"));
        result.put("client_secret", server.getString("pingan.insurance.client_secret"));
        result.put("grant_type", server.getString("pingan.insurance.grant_type"));
        try {
            String s = HttpUtil.httpsPost(server.getString("pingan.insurance.getToken"), result);
            JSONObject jsonObject = JSONObject.parseObject(s);
            Map<String,String> data = (Map<String,String>)jsonObject.get("data");
            System.out.println(JSONObject.toJSONString(s));
            if (data!=null){
                System.out.println(data.get("access_token"));
                RedisUtil.set(Constant.PING_AN_TOKEN_KEY,data.get("access_token"),0);
                return data.get("access_token");
            }else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
