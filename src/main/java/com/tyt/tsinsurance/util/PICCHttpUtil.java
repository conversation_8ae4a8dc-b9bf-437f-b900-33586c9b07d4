package com.tyt.tsinsurance.util;

import com.tyt.util.AESUtil;
import com.tyt.util.MD5Util;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;

@Component
public class PICCHttpUtil {
    private static final Logger logger = LoggerFactory.getLogger(PICCHttpUtil.class);

    private static final String EPOLICY_PASSWORD = "abx$7RFFFFV%TGB%9^&";
    private static final String SLASH = "/";

    private static String PICC_INSURANCE_URL;

    private static String PICC_EPOLICY_HOST;

    @Value("${picc.insurance.url}")
    private String piccInsuranceUrlInjector;

    @Value("${picc.epolicy.host}")
    private String piccEpolicyHost;



    @PostConstruct
    public void init(){
        PICC_INSURANCE_URL = piccInsuranceUrlInjector;
        PICC_EPOLICY_HOST = piccEpolicyHost;
    }

    public static String doPICCPost(String rawParam, String rawData) throws IOException {
        String encodedParam = AESUtil.doB64encode(rawParam);
        String encryptedData = AESUtil.encrypt(rawData, AESUtil.KEY);

         try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(PICC_INSURANCE_URL+"?param="+encodedParam);
            httpPost.setEntity(EntityBuilder.create()
                    .setContentType(ContentType.create("text/plain", Consts.UTF_8))
                    .setText(encryptedData).build());

            logger.info("Executing request {}", httpPost.getRequestLine());

            ResponseHandler<String> responseHandler = response -> {
                int status = response.getStatusLine().getStatusCode();
                if (status >= 200 && status < 300) {
                    HttpEntity entity = response.getEntity();
                    return entity != null ? EntityUtils.toString(entity) : null;
                } else {
                    throw new ClientProtocolException("Unexpected response status: " + status);
                }
            };
            String responseBody = httpclient.execute(httpPost, responseHandler);
            logger.info("response is {}", responseBody);

            return AESUtil.decrypt(responseBody, AESUtil.KEY);
        }

    }


    /**
     * http://**************:8940/getPolicyDownUrl+"?pNo="+pNo+"&sign="+utils.md5Encypt(global.config.json_password+pNo)
     * @param transactionNo
     * @return
     */
    public static String composeEPolictyUrl(String transactionNo) {
        String sign = MD5Util.GetMD5Code(EPOLICY_PASSWORD + transactionNo);
        String epolicyUrl = new StringBuffer(PICC_EPOLICY_HOST)
                .append(SLASH)
                .append("getPolicyDownUrl")
                .append("?pNo=")
                .append(transactionNo)
                .append("&sign=")
                .append(sign).toString();
        return epolicyUrl;


    }


}
