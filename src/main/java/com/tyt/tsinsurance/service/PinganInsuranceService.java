package com.tyt.tsinsurance.service;

import com.tyt.model.TransportInsurance;
import com.tyt.tsinsurance.bean.pingan.PinganAcceptBean;

public interface PinganInsuranceService {
    /**
     * 获取平安支付url
     * @param amount  保费
     * @param productName  产品名称
     * @param orderNo  支付订单号
     * @param applyPolicyNo  投保单号
     * @param noticeNo  通知单号
     * @param customName  客户名
     * @return 字符串
     * @throws Exception e
     */
    String getPinganPayUrl(String amount, String productName,String orderNo, String applyPolicyNo, String noticeNo, String customName) throws Exception;

    /**
     * 平安支付回调
     * @param callBackBean  支付回调bean
     * @throws Exception e
     */
    void savePinganCallBack(TransportInsurance tsi, PinganAcceptBean callBackBean) throws Exception;

}
