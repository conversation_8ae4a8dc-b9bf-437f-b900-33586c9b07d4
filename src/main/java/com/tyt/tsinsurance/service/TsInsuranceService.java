package com.tyt.tsinsurance.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportInsurance;
import com.tyt.tsinsurance.SaleReturnActivityBean;
import com.tyt.tsinsurance.bean.CalcCostBean;
import com.tyt.tsinsurance.bean.ImportWaybillBean;
import com.tyt.tsinsurance.bean.InsuranceSaveBean;
import com.tyt.tsinsurance.bean.TsInsuranceListBean;

import java.util.List;
import java.util.Map;

public interface TsInsuranceService extends BaseService<TransportInsurance, Long> {

    ResultMsgBean checkBlank(InsuranceSaveBean insurance) throws Exception;

    void delInsurance(Long insuranceId);

    Map<String,Object> saveInsurance(InsuranceSaveBean insurance) throws Exception;

    /**
     * 获取我的保单列表
     *
     * @param userId      用户ID
     * @param opt         0.全部  1.待支付  2.生效中  3.已失效
     * @param lastInsurId 最后的保单ID
     * @return TsInsuranceListBean
     */
    TsInsuranceListBean getMyList(Long userId, Integer opt, Long lastInsurId);

    /**
     * 通过状态查询统计数量
     *
     * @param userId 用户ID
     * @param status 保险状态
     * @return 统计后的数字
     */
    Integer getCountByStatus(Long userId, Integer status);

    /**
     * 导入运单列表
     *
     * @param userId 用户userId
     * @param opt    操作类型  1.我是车方  2.我是货方
     */
    List<ImportWaybillBean> importWaybill(Long userId, Integer opt) throws Exception;

    /**
     * 获取我是货方的导入运单列表
     *
     * @param userId 用户ID
     * @return List<ImportWaybillBean>
     */
    List<ImportWaybillBean> isShipper(Long userId) throws Exception;

    /**
     * 获取我是车方的导入运单列表
     *
     * @param userId 用户ID
     * @return List<ImportWaybillBean>
     */
    List<ImportWaybillBean> isCarry(Long userId);

    /**
     * 通过orderNo获得 保单对像
     *
     * @param orderNo
     * @return
     */
    public TransportInsurance getTransportInsuranceForOrderNo(String orderNo);

    /**
     * 计算保费(华泰货运险)
     * @param amtCurrency 保额
     * @param startProvinc 出发地 省
     * @param startCity 出发地 市
     * @param startArea 出发地 区
     * @param destProvinc 目的地 省
     * @param destCity 目的地 市
     * @param destArea 目的地 区
     * @return CalcCostBean
     */
    CalcCostBean calcCost(String amtCurrency,
                          String startProvinc,
                          String startCity,
                          String startArea,
                          String destProvinc,
                          String destCity,
                          String destArea);


    /**
     * 计算保费(人保货运险)
     * @param amtCurrency 保额
     * @param startProvinc 出发地 省
     * @param startCity 出发地 市
     * @param startArea 出发地 区
     * @param destProvinc 目的地 省
     * @param destCity 目的地 市
     * @param destArea 目的地 区
     * @return CalcCostBean
     */
    CalcCostBean calcPICCCost(String amtCurrency,
                          String startProvinc,
                          String startCity,
                          String startArea,
                          String destProvinc,
                          String destCity,
                          String destArea);

    Map<String,Object> updateInsurance(InsuranceSaveBean insurance) throws Exception;


	void delAutInsurance(Long insuranceId);

    /**
     * @Description  根据货源Id查询是否有关联的保单信息
     * <AUTHOR>
     * @Date  2018/11/28 15:48
     * @Param [tsId]
     * @return java.lang.Long  最后一次购买的保单Id
     **/
    TransportInsurance  queryInsuranceByTsId(Long userId,Long tsId) throws Exception;


    /**
     * 计算保费(平安)
     * @param amtCurrency 保额
     * @param startProvinc 出发地 省
     * @param startCity 出发地 市
     * @param startArea 出发地 区
     * @param destProvinc 目的地 省
     * @param destCity 目的地 市
     * @param destArea 目的地 区
     * @return CalcCostBean
     */
    CalcCostBean pingAnPremiumCalculation(String amtCurrency,
                                          String startProvinc,
                                          String startCity,
                                          String startArea,
                                          String destProvinc,
                                          String destCity,
                                          String destArea);

    TransportInsurance getByInsuranceNo(String insuranceNo);

    /**
     * 获取活动期间的保单总数和投保金额
     * @param userId
     * @return
     */
    SaleReturnActivityBean getCountAndCurrency(Long userId);
}
