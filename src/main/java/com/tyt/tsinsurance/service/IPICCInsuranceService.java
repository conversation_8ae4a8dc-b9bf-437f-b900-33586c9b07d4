package com.tyt.tsinsurance.service;

import com.tyt.tsinsurance.bean.picc.PICCBusinessException;
import com.tyt.tsinsurance.bean.picc.PolicyInfo;
import com.tyt.tsinsurance.bean.picc.SubmitInsuranceBean;

public interface IPICCInsuranceService {

    /**
     *
     * @param insuranceBean
     * @return payUrl
     * @throws PICCBusinessException
     */
    String getPICCPayUrl(SubmitInsuranceBean insuranceBean) throws PICCBusinessException;


    String getEPolicyUrl(String policyNo) throws PICCBusinessException;

    PolicyInfo queryPaymentStatus(String insuranceNo) throws PICCBusinessException;
}
