package com.tyt.tsinsurance.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.HtPayCallBack;
import com.tyt.tsinsurance.bean.PayStatusBean;

public interface HtPayCallBackService extends BaseService<HtPayCallBack, Long> {
	/**
	 * 保存异步回调数据
	 * @param status
	 * @param orderNo
	 * @param payFinishTime
	 * @param channelCode
	 * @param transactionNo
	 * @param sign
	 * @param payMode
	 */
	public void saveAsyncCallback(String status,
			String  orderNo,
			String payFinishTime,
			String channelCode,
			String transactionNo,
			String sign,
			String payMode)throws Exception ;

	void doPICCCallback(String status, String orderNo,
						String payFinishTime, String transactionNo,
						String payMode) throws Exception;

	/**
	 * 获得保单支付状态
	 * @param id
	 * @return
	 */
	public PayStatusBean getPayStatusBean(Long id);
}
