package com.tyt.tsinsurance.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TransportInsuranceSub;
import com.tyt.tsinsurance.service.TsInsuranceSubService;

@Service("tsinsuranceSubService")
public class TsInsuranceSubServiceImpl extends
		BaseServiceImpl<TransportInsuranceSub, Long> implements TsInsuranceSubService {

	@Resource(name = "tsinsuranceSubDao")
	public void setBaseDao(BaseDao<TransportInsuranceSub, Long> tsinsuranceSubDao) {
		super.setBaseDao(tsinsuranceSubDao);
	}
}
