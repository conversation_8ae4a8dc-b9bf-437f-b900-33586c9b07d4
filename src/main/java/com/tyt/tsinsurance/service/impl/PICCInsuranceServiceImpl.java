package com.tyt.tsinsurance.service.impl;

import com.tyt.tsinsurance.bean.picc.*;
import com.tyt.tsinsurance.service.IPICCInsuranceService;
import com.tyt.tsinsurance.util.PICCHttpUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Service
public class PICCInsuranceServiceImpl implements IPICCInsuranceService {
    private static final Logger logger = LoggerFactory.getLogger(PICCInsuranceServiceImpl.class);

    private static final String SUBMIT_PARAM = "{\"system\":\"S10000038\",\"interface\":\"100052\",\"mode\":\"\"}";
    private static final String E_POLICY = "{\"system\":\"S10000038\",\"interface\":\"100044\",\"mode\":\"\"}";
    private static final String PAYMENT_QUERY = "{\"system\":\"S10000038\",\"interface\":\"100049\",\"mode\":\"\"}";

    private static final String DATE_FORMAT_SECOND = "yyyy-MM-dd HH:mm:ss"; //2018-11-27 15:38:23
    private static final String DATE_FORMAT_HOUR = "yyyy-MM-dd:HH"; //2018-11-27:15
    public static final String INSURER_KEY_SUFFIX = "#tyt";
    public static final String SUCCESS_CODE = "0000";

    @Value("${picc.payment.callback.url}")
    private String paymentCallbackUrl;


    @Override
    public String getPICCPayUrl(SubmitInsuranceBean insuranceBean) throws PICCBusinessException {
        for (Field f : insuranceBean.getClass().getFields()) {
            f.setAccessible(true);
            try {
                if (f.get(insuranceBean) == null) {
                    throw new PICCBusinessException("参数非法! {} 不能为空!" + f.getName());
                }
            } catch (IllegalAccessException e) {
                logger.error("error occur while access field by reflect, {}", ExceptionUtils.getStackTrace(e));
            }
        }

        SimpleDateFormat dateFormatSecond = new SimpleDateFormat(DATE_FORMAT_SECOND);
        SimpleDateFormat dateFormatHour = new SimpleDateFormat(DATE_FORMAT_HOUR);
        String insurerKey = insuranceBean.getInsuranceNo() + INSURER_KEY_SUFFIX;
        Date now = new Date();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        Date nextDay = calendar.getTime();

        try {
            Date startTime = dateFormatSecond.parse(insuranceBean.getStartTime());
            Date departTime = dateFormatHour.parse(insuranceBean.getDepartureDate());
            if(startTime.before(now)){
                throw new PICCBusinessException("起保时间不能早于下单时间！");
            }
            if(departTime.before(now)){
                throw new PICCBusinessException("起运时间不能早于下单时间！");
            }
        } catch (ParseException e) {
            throw new PICCBusinessException("起保时间/起运时间反序列化错误！");
        }
        SubmitOrderWrapper submitOrderWrapper = SubmitOrderWrapper.builder()
                .setHead(Head.builder()
                        .setTransactionNo(insuranceBean.getInsuranceNo())
                        .setTimeStamp(dateFormatSecond.format(now)).build())
                .setBody(Body.builder()
                        .setApplicant(Applicant.builder()
                                .setCardNo(insuranceBean.getApplicantCardNo())
                                .setCardType(insuranceBean.getApplicantCardType())
                                .setName(insuranceBean.getApplicantName())
                                .setMobile(insuranceBean.getApplicantMobile()).build())
                        .setInsurant(Insurant.builder()
                                .setCardType(insuranceBean.getInsurantCardType())
                                .setCardNo(insuranceBean.getInsurantCardNo())
                                .setName(insuranceBean.getInsurantName())
                                .setAmount(insuranceBean.getAmount())
                                .setPremium(insuranceBean.getPremium())
                                .setInsurerKey(insurerKey)
                                .setMobile(insuranceBean.getInsurantMobile())
                                .setRelation(insuranceBean.getRelation()).build())
                        .setOrderInfo(OrderInfo.builder()
                                .setOrderNo(insuranceBean.getInsuranceNo())
                                .setStartTime(insuranceBean.getStartTime())
                                .setSumAmount(insuranceBean.getAmount())
                                .setSumPremium(insuranceBean.getPremium()).build())
                        .setTargets(Targets.builder()
                                .setInvRefNo(insuranceBean.getInsuranceNo())
                                .setDepartureDate(insuranceBean.getDepartureDate())
                                .setTransport(insuranceBean.getTransport())
                                .setFromLoc(insuranceBean.getFromLoc())
                                .setToLoc(insuranceBean.getToLoc())
                                .setViaLoc(insuranceBean.getViaLoc())
                                .setGoodsName(insuranceBean.getGoodsName())
                                .setWeight(insuranceBean.getWeight())
                                .setPackQty(insuranceBean.getPackQty())
                                .setRatio(insuranceBean.getRatio())
                                .setRecognizeeOrg(insuranceBean.getRecognizeeOrg())
                                .setGoodsTypeNo(insuranceBean.getGoodsTypeNo()).build())
                        .setExtendInfo(ExtendInfo.builder()
                                .setCallbackURL(paymentCallbackUrl).build()).build()).build();

        String submitResult = "";
        try {
            submitResult = PICCHttpUtil.doPICCPost(SUBMIT_PARAM, new ObjectMapper().writeValueAsString(submitOrderWrapper));
            logger.info("picc reply is {}", submitResult);
        } catch (IOException e) {
            logger.error("序列化请求参数错误! {}", ExceptionUtils.getStackTrace(e));
            throw new PICCBusinessException("序列化请求参数错误! ");
        }

        // {"head":{"transactionNo":"1543217903","operator":"S10000038","timeStamp":"2018-11-26 15:53:56","errorCode":"0000","errorMsg":"成功"},"body":{"payUrl":"https://ceshit.zhongyuanib.com:6443/t/5injrL"}}
        if(StringUtils.isNotEmpty(submitResult)){
            SubmitOrderReplyWrapper replyWrapper = null;
            try {
                replyWrapper = new ObjectMapper().readValue(submitResult, SubmitOrderReplyWrapper.class);
            } catch (IOException e) {
                logger.error("反序列化接口返回错误! {}", ExceptionUtils.getStackTrace(e));
                throw new PICCBusinessException("反序列化接口返回错误! ");
            }
            if(replyWrapper.getHead() != null
                    && StringUtils.isNotEmpty(replyWrapper.getHead().getErrorCode())
                    && SUCCESS_CODE.equals(replyWrapper.getHead().getErrorCode())) {
                if (replyWrapper.getBody() != null && StringUtils.isNotEmpty(replyWrapper.getBody().getPayUrl())) {
                    return replyWrapper.getBody().getPayUrl();
                } else {
                    throw new PICCBusinessException(replyWrapper.getHead().getErrorMsg());
                }
            }else {
                throw new PICCBusinessException(replyWrapper.getHead().getErrorMsg());
            }
        }

        logger.error("return null");
        return null;
    }

    @Override
    public String getEPolicyUrl(String policyNo) throws PICCBusinessException {
        EPolicyData ePolicyRequst = EPolicyData.builder()
                .setHead(EPolicyHead.builder()
                        .setTimeStamp(new SimpleDateFormat(DATE_FORMAT_SECOND).format(new Date())).build())
                .setBody(EPolicyBody.builder()
                        .setInsResults(InsResults.builder().setPolicyNo(policyNo).build()).build()).build();
        String ePolicyResult = "";
        try {
            //{"head":{"transactionNo":null,"operator":"S10000038","aiBaoTransactionNo":"ABXQUNAR171543291135373378326222","timeStamp":"2018-11-27 11:58:55","errorCode":"0000","errorMsg":"成功"},"body":{"insResults":{"policyNo":"611009906000501180001007","policyDownUrl":"http://58.49.112.218:8081/webins/"}}}
            ePolicyResult = PICCHttpUtil.doPICCPost(E_POLICY, new ObjectMapper().writeValueAsString(ePolicyRequst));
        } catch (IOException e) {
            logger.error("序列化请求参数错误! {}", ExceptionUtils.getStackTrace(e));
            throw new PICCBusinessException("序列化请求参数错误! ");
        }

        if(StringUtils.isNotEmpty(ePolicyResult)){
            EPolicyData ePolicyData = null;
            try {
                ePolicyData = new ObjectMapper().readValue(ePolicyResult, EPolicyData.class);
                if(ePolicyData.getHead() != null
                        && ePolicyData.getHead().getErrorCode() != null
                        && SUCCESS_CODE.equals(ePolicyData.getHead().getErrorCode())){
                    if(ePolicyData.getBody() != null
                            && ePolicyData.getBody().getInsResults() != null
                            && StringUtils.isNotEmpty(ePolicyData.getBody().getInsResults().getPolicyDownUrl())){
                        return ePolicyData.getBody().getInsResults().getPolicyDownUrl();
                    }else {
                        throw new PICCBusinessException(ePolicyData.getHead().getErrorMsg());
                    }
                }else {
                    throw new PICCBusinessException(ePolicyData.getHead().getErrorMsg());
                }
            } catch (IOException e) {
                logger.error("反序列化接口返回错误! {}", ExceptionUtils.getStackTrace(e));
                throw new PICCBusinessException("反序列化接口返回错误! ");
            }
        }
        logger.error("return null");
        return null;

    }

    @Override
    public PolicyInfo queryPaymentStatus(String insuranceNo) throws PICCBusinessException {
        PaymentQueryWrapper paymentQueryWrapper = PaymentQueryWrapper.builder()
                .setHead(EPolicyHead.builder().build())
                .setBody(PaymentQueryRequestBody.builder()
                        .setLocalOrderNo(insuranceNo).build()).build();
        String paymentResult = "";
        try {
            paymentResult =  PICCHttpUtil.doPICCPost(PAYMENT_QUERY, new ObjectMapper().writeValueAsString(paymentQueryWrapper));
        } catch (IOException e) {
            logger.error("序列化请求参数错误! {}", ExceptionUtils.getStackTrace(e));
            throw new PICCBusinessException("序列化请求参数错误! ");
        }

        if(StringUtils.isNotEmpty(paymentResult)){
            try {
                PaymentQueryResponseWrapper paymentQueryResult = new ObjectMapper().readValue(paymentResult, PaymentQueryResponseWrapper.class);
                if(paymentQueryResult != null && paymentQueryResult.getBody() != null){
                    return paymentQueryResult.getBody().getPolicyInfo();
                }
            } catch (IOException e) {
                logger.error("反序列化接口返回错误! {}", ExceptionUtils.getStackTrace(e));
                throw new PICCBusinessException("反序列化接口返回错误! ");
            }
        }

        return null;

    }
}
