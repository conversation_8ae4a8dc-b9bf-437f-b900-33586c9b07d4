package com.tyt.tsinsurance.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.HtPayRecord;
import com.tyt.tsinsurance.service.HtPayRecordService;

@Service("htPayRecordService")
public class HtPayRecordServiceImpl extends
		BaseServiceImpl<HtPayRecord, Long> implements HtPayRecordService {

	@Resource(name = "htPayRecordDao")
	public void setBaseDao(BaseDao<HtPayRecord, Long> htPayRecordDao) {
		super.setBaseDao(htPayRecordDao);
	}
	public HtPayRecord getHtPayRecord(String orderNo){
		String hql="from HtPayRecord where orderNo=? order by id desc ";
		List<HtPayRecord> list=this.getBaseDao().find(hql, orderNo);
		if(list!=null &&list.size()>0){
			return list.get(0);	
		}
		return null;
	}

	@Override
    public HtPayRecord getPinganPayRecord(String insuranceNo){
        String hql="from HtPayRecord where insuranceNo=? order by id desc ";
        List<HtPayRecord> list=this.getBaseDao().find(hql, insuranceNo);
        if(list!=null &&list.size()>0){
            return list.get(0);
        }
        return null;
    }

}
