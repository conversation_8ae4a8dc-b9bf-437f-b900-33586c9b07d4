package com.tyt.tsinsurance.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.common.service.TytMapDictService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.CarOwnerIntentionBean;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportMtService;
import com.tyt.transport.service.TytCarOwnerIntentionService;
import com.tyt.tsinsurance.SaleReturnActivityBean;
import com.tyt.tsinsurance.bean.CalcCostBean;
import com.tyt.tsinsurance.bean.ImportWaybillBean;
import com.tyt.tsinsurance.bean.TsInsuranceListBean;

import com.tyt.tsinsurance.bean.picc.SubmitInsuranceBean;
import com.tyt.tsinsurance.bean.pingan.InsureReturn;
import com.tyt.tsinsurance.service.HtPayRecordService;
import com.tyt.tsinsurance.service.IPICCInsuranceService;
import com.tyt.tsinsurance.service.PinganInsuranceService;
import com.tyt.tsinsurance.util.PingAnUtil;
import com.tyt.util.*;
import com.tyt.util.httputil.HttpUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytSequenceService;
import com.tyt.htbx.HtbxUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportInsurance;
import com.tyt.tsinsurance.bean.InsuranceSaveBean;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.user.service.UserTelService;

@Service("tsinsuranceService")
public class TsInsuranceServiceImpl extends
        BaseServiceImpl<TransportInsurance, Long> implements TsInsuranceService {

	static PropertiesUtil propertiesUtil = PropertiesUtil.init("server_url");
	static PropertiesUtil server = PropertiesUtil.init("server");
	static String channelCode=propertiesUtil.getString("htbx.pay.channelCode");

    public static Logger logger = LoggerFactory.getLogger(TsInsuranceServiceImpl.class);

    @Resource(name = "tsinsuranceDao")
    public void setBaseDao(BaseDao<TransportInsurance, Long> tsinsuranceDao) {
        super.setBaseDao(tsinsuranceDao);
    }

    @Resource(name = "userService")
    private UserService userService;

	@Resource(name = "userTelService")
	private UserTelService userTelService;

	@Resource(name = "tytSequenceService")
	private TytSequenceService tytSequenceService;

	@Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "transportOrdersService")
    private TransportOrdersService transportOrdersService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "transportMtService")
    private TransportMtService transportMtService;

    @Resource(name = "tytCarOwnerIntentionService")
    private TytCarOwnerIntentionService carOwnerService;

    @Resource(name = "tytMapDictService")
    private TytMapDictService mapDictService;

	@Resource(name="htPayRecordService")
	private HtPayRecordService htPayRecordService;

	@Resource(name="pinganInsuranceService")
	private PinganInsuranceService pinganInsuranceService;

	private static final String INSURANCE_MAX_AMT_CURRENCY = "tyt_insurance_max_amt_currency";
	private static final String INSURANCE_MAX_WEIGHT = "tyt_insurance_max_weight";
	private static final String INSURANCE_RATE = "tyt_insurance_rate";
	//华泰公司全名称
	private static final String INSURANCE_COMPANY_HT = "tyt_insurance_company";
	//人保公司全名称
	private static final String INSURANCE_COMPANY_RB = "tyt_insurance_company_rb";
	//平安公司全名称
	private static final String INSURANCE_COMPANY_PA = "tyt_insurance_company_pa";

	private static final String DATE_FORMAT_SECOND = "yyyy-MM-dd HH:mm:ss"; //2018-11-27 15:38:23
	private static final String DATE_FORMAT_HOUR = "yyyy-MM-dd:HH"; //2018-11-27:15

	@Autowired
	private IPICCInsuranceService insuranceService;

//	@Override
//	public ResultMsgBean checkBlank(InsuranceSaveBean insurance) throws Exception{
//		ResultMsgBean rm=new ResultMsgBean(200,"验证通过");
//		if (insurance.getCellPhone()==null || "".equals(insurance.getCellPhone().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("cellPhone不能为空");
//			return rm;
//		}
//		if (insurance.getUserName()==null || "".equals(insurance.getUserName().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("userName不能为空");
//			return rm;
//		}
//		if (insurance.getApplicantName()==null || "".equals(insurance.getApplicantName().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("applicantName不能为空");
//			return rm;
//		}
//		if (insurance.getApplicantId()==null || "".equals(insurance.getApplicantId().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("applicantId不能为空");
//			return rm;
//		}
//		if (insurance.getApplicantPhone()==null || "".equals(insurance.getApplicantPhone().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("applicantPhone不能为空");
//			return rm;
//		}
//		if (insurance.getInsuredName()==null || "".equals(insurance.getInsuredName().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("insuredName不能为空");
//			return rm;
//		}
//		if (insurance.getInsuredId()==null || "".equals(insurance.getInsuredId().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("insuredId不能为空");
//			return rm;
//		}
//		if (insurance.getInsuredPhone()==null || "".equals(insurance.getInsuredPhone())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("insuredPhone不能为空");
//			return rm;
//		}
//		if (insurance.getHeadNo()==null || "".equals(insurance.getHeadNo().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("headNo不能为空");
//			return rm;
//		}
//		if (insurance.getTailNo()==null || "".equals(insurance.getTailNo().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("tailNo不能为空");
//			return rm;
//		}
//		if (insurance.getTaskContent1()==null || "".equals(insurance.getTaskContent1().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("taskContent1不能为空");
//			return rm;
//		}
//		Integer maxWeight = tytConfigService.getIntValue(INSURANCE_MAX_WEIGHT,100);
//		if (insurance.getWeight()==null || "".equals(insurance.getWeight().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("weight不能为空");
//			return rm;
//		}else if (new BigDecimal(insurance.getWeight()).movePointRight(2).intValue()>new BigDecimal(maxWeight).movePointRight(2).intValue()) {
//			rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
//			rm.setMsg("重量不能超过100");
//			return rm;
//		}
//		if (insurance.getStartTime()==null) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("startTime不能为空");
//			return rm;
//		}else{
//			Date date=TimeUtil.parseDateString(TimeUtil.formatDate(new Date())+" 00:00:00");
//			Date startTime = TimeUtil.parseDateString(insurance.getStartTime()+" 00:00:00");
//			if (startTime.getTime()<date.getTime()) {
//				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
//				rm.setMsg("起运日期不能早于当前日期");
//				return rm;
//			}
//		}
//		if (insurance.getStartPoint()==null || "".equals(insurance.getStartPoint().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("startPoint不能为空");
//			return rm;
//		}
//		if (insurance.getDestPoint()==null || "".equals(insurance.getDestPoint().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("destPoint不能为空");
//			return rm;
//		}
//		if (insurance.getStartProvinc()==null || "".equals(insurance.getStartProvinc().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("satrtProvinc不能为空");
//			return rm;
//		}
//		if (insurance.getStartCity()==null || "".equals(insurance.getStartCity().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("satrtCity不能为空");
//			return rm;
//		}
//		if (insurance.getStartArea()==null || "".equals(insurance.getStartArea().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("satrtArea不能为空");
//			return rm;
//		}
//		if (insurance.getDestProvinc()==null || "".equals(insurance.getDestProvinc().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("destProvinc不能为空");
//			return rm;
//		}
//		if (insurance.getDestCity()==null || "".equals(insurance.getDestCity().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("destCity不能为空");
//			return rm;
//		}
//		if (insurance.getDestArea()==null || "".equals(insurance.getDestArea().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("destArea不能为空");
//			return rm;
//		}
//		if (insurance.getDistance()==null || "".equals(insurance.getDistance().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("distance不能为空");
//			return rm;
//		}
//		Integer maxAmtCurrency = tytConfigService.getIntValue(INSURANCE_MAX_AMT_CURRENCY,500);
//		if (insurance.getAmtCurrency()==null) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("amtCurrency不能为空");
//			return rm;
//		}else if (new BigDecimal(insurance.getAmtCurrency()).movePointRight(2).intValue()>new BigDecimal(maxAmtCurrency).movePointRight(2).intValue()) {
//			rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
//			rm.setMsg("保额不能大于500");
//			return rm;
//		}
//		if (insurance.getPremiumCurrency()==null || "".equals(insurance.getPremiumCurrency().trim())) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("premiumCurrency不能为空");
//			return rm;
//		}else{
//			Integer premiumCurrency = tytConfigService.getIntValue("tyt_insurance_min_premiumCurrency",30);
//			if (new BigDecimal(insurance.getPremiumCurrency()).intValue()<premiumCurrency) {
//				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
//				rm.setMsg("保费过低，请修改保额");
//				return rm;
//			}
//		}
//		if (insurance.getCategory()==null) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("category不能为空");
//			return rm;
//		}
//		//校验投保人证件号码格式是否正确  投保人类型 1个人 2公司
//		Integer applicantType = insurance.getApplicantType();
//		if (applicantType == null) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("applicantType不能为空");
//			return rm;
//		}
//        if(applicantType != null && applicantType.intValue() > 0)
//		{
//			//投保人的证件号码
//			String applicantId = insurance.getApplicantId();
//			if(applicantType.intValue() == 1)//个人
//			{
//				boolean flag = IdCardUtil.checkIdCard(applicantId);
//				if(flag == false)
//				{
//					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//					rm.setMsg("投保人-身份证号码格式不正确");
//					return rm;
//				}
//			}else if(applicantType.intValue() == 2){//公司
//				boolean flag = BusinessCodeUtil.isValid(applicantId);
//				if(flag == false)
//				{
//					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//					rm.setMsg("投保人-证件号码格式不正确");
//					return rm;
//				}
//			}
//		}
//        //校验被保人证件号码格式是否正确 被保人类型  1个人  2公司
//		Integer insuredType = insurance.getInsuredType();
//		if (insuredType ==null) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("insuredType不能为空");
//			return rm;
//		}
//		if(insuredType != null && insuredType.intValue() > 0)
//		{
//			//被保人的证件号码
//			String insuredId = insurance.getInsuredId();
//			if(insuredType.intValue() == 1)//个人
//			{
//				boolean flag = IdCardUtil.checkIdCard(insuredId);
//				if(flag == false)
//				{
//					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//					rm.setMsg("被保人-身份证号码格式不正确");
//					return rm;
//				}
//			}else if(insuredType.intValue() == 2)//公司
//			{
//				boolean flag = BusinessCodeUtil.isValid(insuredId);
//				if(flag == false)
//				{
//					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//					rm.setMsg("被保人-证件号码格式不正确");
//					return rm;
//				}
//			}
//		}
//		return rm;
//	}

	@Override
	public ResultMsgBean checkBlank(InsuranceSaveBean insurance) throws Exception{
		ResultMsgBean rm=new ResultMsgBean(200,"验证通过");

		//保险大类 type  1：华泰货运险 2：人保货运险
		Integer type = insurance.getType();
		/*//增加货运险正在维护中的提示信息
		if(type != null && (type.intValue() == 1 || type.intValue() == 2))
		{
			rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
			rm.setMsg("货运险正在维护中");
			return rm;
		}*/

		//操作人账号
		if (insurance.getCellPhone()==null || "".equals(insurance.getCellPhone().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("cellPhone不能为空");
			return rm;
		}
		//操作人账号姓名
		if (insurance.getUserName()==null || "".equals(insurance.getUserName().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("userName不能为空");
			return rm;
		}
		//校验投保人证件号码格式是否正确  投保人类型 1个人 2公司
		Integer applicantType = insurance.getApplicantType();
		if (applicantType == null) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("投保人类型不能为空");
			return rm;
		}
		if(applicantType != null && applicantType.intValue() > 0)
		{
			//投保人姓名/公司名称
			String applicantName = insurance.getApplicantName();
			//投保人的证件号码
			String applicantId = insurance.getApplicantId();
			//投保人手机号码
			String applicantPhone = insurance.getApplicantPhone();
			if(applicantType.intValue() == 1)//个人
			{
				if (applicantName ==null || "".equals(applicantName.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入投保人姓名");
					return rm;
				}
				//判断投保人姓名长度
				if(applicantName.length()>5){
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("投保人姓名过长");
					return rm;
				}
				if (applicantId==null || "".equals(applicantId.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入投保人身份证号");
					return rm;
				}
				boolean flag = IdCardUtil.checkIdCard(applicantId);
				if(flag == false)
				{
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("投保人身份证号格式错误");
					return rm;
				}
				if (applicantPhone ==null || "".equals(applicantPhone.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入投保人手机号码");
					return rm;
				}
			}else if(applicantType.intValue() == 2){//公司
				if (applicantName ==null || "".equals(applicantName.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入投保公司名称");
					return rm;
				}
				//判断投保公司名称长度
				if(applicantName.length()>50){
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("投保公司名称过长");
					return rm;
				}
				if (applicantId==null || "".equals(applicantId.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入投保公司统一社会信用代码");
					return rm;
				}
				boolean flag = BusinessCodeUtil.isValidNew(applicantId);
				if(flag == false)
				{
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("投保公司统一社会信用代码错误");
					return rm;
				}
				if (applicantPhone ==null || "".equals(applicantPhone.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入投保公司手机号码");
					return rm;
				}
			}
		}
		//校验被保人证件号码格式是否正确 被保人类型  1个人  2公司
		Integer insuredType = insurance.getInsuredType();
		if (insuredType ==null) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("被保人类型不能为空");
			return rm;
		}
		if(insuredType != null && insuredType.intValue() > 0)
		{
			//被保人姓名/公司名称
			String insuredName = insurance.getInsuredName();
			//被保人的证件号码
			String insuredId = insurance.getInsuredId();
			//被保人手机号码
			String insuredPhone = insurance.getInsuredPhone();
			if(insuredType.intValue() == 1)//个人
			{
				if (insuredName ==null || "".equals(insuredName.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入被保人姓名");
					return rm;
				}
				//判断被保人姓名长度
				if(insuredName.length()>5){
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("被保人姓名过长");
					return rm;
				}
				if (insuredId==null || "".equals(insuredId.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入被保人身份证号");
					return rm;
				}
				boolean flag = IdCardUtil.checkIdCard(insuredId);
				if(flag == false)
				{
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("被保人身份证号格式错误");
					return rm;
				}
				if (insuredPhone ==null || "".equals(insuredPhone)) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入被保人手机号码");
					return rm;
				}
			}else if(insuredType.intValue() == 2)//公司
			{
				if (insuredName ==null || "".equals(insuredName.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入被保公司名称");
					return rm;
				}
				//判断被保公司名称长度
				if(insuredName.length()>50){
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("被保公司名称过长");
					return rm;
				}
				if (insuredId==null || "".equals(insuredId.trim())) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入被保公司统一社会信用代码");
					return rm;
				}
				boolean flag = BusinessCodeUtil.isValidNew(insuredId);
				if(flag == false)
				{
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("被保公司统一社会信用代码错误");
					return rm;
				}
				if (insuredPhone ==null || "".equals(insuredPhone)) {
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("未输入被保公司手机号码");
					return rm;
				}
			}
		}
		if (insurance.getHeadNo()==null || "".equals(insurance.getHeadNo().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("车头车牌未填写");
			return rm;
		}
		if (insurance.getTailNo()==null || "".equals(insurance.getTailNo().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("挂车车牌未填写");
			return rm;
		}
		if (insurance.getTaskContent1()==null || "".equals(insurance.getTaskContent1().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未输入货物名称");
			return rm;
		}
		Integer maxWeight = tytConfigService.getIntValue(INSURANCE_MAX_WEIGHT,100);
		if (insurance.getWeight()==null || "".equals(insurance.getWeight().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未输入货物重量");
			return rm;
		}else if (new BigDecimal(insurance.getWeight()).movePointRight(2).intValue()>new BigDecimal(maxWeight).movePointRight(2).intValue()) {
			rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
			rm.setMsg("重量不能超过100");
			return rm;
		}

//		if (StringUtils.isBlank(insurance.getContentNewOld())){
//            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//            rm.setMsg("请选择货物新旧");
//            return rm;
//        }
//        if (StringUtils.isBlank(insurance.getPacking())){
//            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//            rm.setMsg("请选择包装方式");
//            return rm;
//        }
		if (insurance.getStartTime()==null) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未选择起运时间");
			return rm;
		}else{
			Date date=TimeUtil.parseDateString(TimeUtil.formatDate(new Date())+" 00:00:00");
			Date startTime = TimeUtil.parseDateString(insurance.getStartTime()+" 00:00:00");
			if (startTime.getTime()<date.getTime()) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("起运日期不能早于当前日期");
				return rm;
			}
		}
		if (insurance.getStartPoint()==null || "".equals(insurance.getStartPoint().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未选择出发地");
			return rm;
		}
		if (insurance.getDestPoint()==null || "".equals(insurance.getDestPoint().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未选择目的地");
			return rm;
		}
		if (insurance.getStartProvinc()==null || "".equals(insurance.getStartProvinc().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未选择出发地省");
			return rm;
		}
		if (insurance.getStartCity()==null || "".equals(insurance.getStartCity().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未选择出发地市");
			return rm;
		}
		if (insurance.getDestProvinc()==null || "".equals(insurance.getDestProvinc().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未选择目的地省");
			return rm;
		}
		if (insurance.getDestCity()==null || "".equals(insurance.getDestCity().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("未选择目的地市");
			return rm;
		}
		if (insurance.getDistance()==null || "".equals(insurance.getDistance().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("distance不能为空");
			return rm;
		}
		Integer maxAmtCurrency = tytConfigService.getIntValue(INSURANCE_MAX_AMT_CURRENCY,400);
		if ("".equals(insurance.getAmtCurrency()) || insurance.getAmtCurrency()==null) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("请输入保额");
			return rm;
		}else if (new BigDecimal(insurance.getAmtCurrency()).movePointRight(2).intValue()>new BigDecimal(maxAmtCurrency).movePointRight(2).intValue()) {
			rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
			rm.setMsg("货物价值超过"+maxAmtCurrency+"万的运输不予承保");
			return rm;
		}
		if (insurance.getPremiumCurrency()==null || "".equals(insurance.getPremiumCurrency().trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("保费不能为空");
			return rm;
		}else{
			Integer premiumCurrency = tytConfigService.getIntValue("tyt_insurance_min_premiumCurrency",30);
			if (new BigDecimal(insurance.getPremiumCurrency()).intValue()<premiumCurrency) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("保费过低，请修改保额");
				return rm;
			}
		}
		if (insurance.getCategory()==null) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("保险类型不能为空");
			return rm;
		}else{
			//保项：1:基本险 2:综合险 3:货运险(人保)
			int category = insurance.getCategory().intValue();
			if(category == 2){
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("综合险维护中");
				return rm;
			}
		}

		return rm;
	}

	@Override
	public Map<String,Object> saveInsurance(InsuranceSaveBean insurance) throws Exception{
		TransportInsurance tsInsurance = new TransportInsurance();
		tsInsurance.setUserId(insurance.getUserId());
		tsInsurance.setUserName(insurance.getUserName());
		tsInsurance.setCellPhone(insurance.getCellPhone());
		tsInsurance.setApplicantName(insurance.getApplicantName());
		tsInsurance.setApplicantId(insurance.getApplicantId());
		tsInsurance.setApplicantPhone(insurance.getApplicantPhone());
		tsInsurance.setInsuredName(insurance.getInsuredName());
		tsInsurance.setInsuredId(insurance.getInsuredId());
		tsInsurance.setInsuredPhone(insurance.getInsuredPhone());
		//根据保险大类判断保险公司名称 type 1：华泰货运险 2：人保货运险
		Integer type = insurance.getType();
		//保险公司名称
		String company = "";
        if(type != null && type.intValue() == 2)
		{
			company = tytConfigService.getStringValue(INSURANCE_COMPANY_RB);
			tsInsurance.setCompany(company);
			tsInsurance.setType(2);
		}else{//为了兼容之前的接口,保险公司类型如果为空，公司名称则默认为平安，保险类型type默认为3
			company = tytConfigService.getStringValue(INSURANCE_COMPANY_PA);
			tsInsurance.setCompany(company);
			tsInsurance.setType(3);
		}
		tsInsurance.setHeadNo(insurance.getHeadNo());
		tsInsurance.setTailNo(insurance.getTailNo());
		//货物名称，人保货运险的下单接口会用到
        StringBuffer goodsName = new StringBuffer();
		String taskContent1 = insurance.getTaskContent1();
		String taskContent2 = insurance.getTaskContent2();
		String taskContent3 = insurance.getTaskContent3();
		String taskContent4 = insurance.getTaskContent4();
		tsInsurance.setTaskContent1(taskContent1);
		goodsName.append(taskContent1);
		if (StringUtils.isNotBlank(taskContent2)) {
			tsInsurance.setTaskContent2(taskContent2);
			goodsName.append(",").append(taskContent2);
		}
		if (StringUtils.isNotBlank(taskContent3)) {
			tsInsurance.setTaskContent3(taskContent3);
			goodsName.append(",").append(taskContent3);
		}
		if (StringUtils.isNotBlank(taskContent4)) {
			tsInsurance.setTaskContent4(taskContent4);
			goodsName.append(",").append(taskContent4);
		}
		tsInsurance.setWeight(new BigDecimal(insurance.getWeight()).movePointRight(2).intValue());
		tsInsurance.setContentNewOld(insurance.getContentNewOld());
		tsInsurance.setPacking(insurance.getPacking());
		String now = TimeUtil.formatDate(new Date());
		if (now.equals(insurance.getStartTime())) {
			tsInsurance.setStartTime(new Date());
			tsInsurance.setEffectiveTime(TimeUtil.addDay(new Date(), 30));
		}else{
			tsInsurance.setStartTime(TimeUtil.parseDateString(insurance.getStartTime()+" 00:00:00"));
			tsInsurance.setEffectiveTime(TimeUtil.addDay(insurance.getStartTime(), 30));
		}
		tsInsurance.setStartPoint(insurance.getStartPoint());
		tsInsurance.setDestPoint(insurance.getDestPoint());
		tsInsurance.setStartCity(insurance.getStartCity());
		tsInsurance.setStartProvinc(insurance.getStartProvinc());
		if (StringUtils.isNotBlank(insurance.getStartArea())) {
			tsInsurance.setStartArea(insurance.getStartArea());
		}
		tsInsurance.setDestProvinc(insurance.getDestProvinc());
		tsInsurance.setDestCity(insurance.getDestCity());
		if (StringUtils.isNotBlank(insurance.getDestArea())) {
			tsInsurance.setDestArea(insurance.getDestArea());
		}
		//出发地目的地编码
		PingAnPortCode startPingAnPortCode = mapDictService.fingPingAnPortCode(insurance.getStartCity(), insurance.getStartProvinc());
		if (startPingAnPortCode==null){
			throw new Exception("出发地参数有误");
		}else {
			insurance.setStartPortProvinceCode(startPingAnPortCode.getProvinceCode());
			if (StringUtils.isNotBlank(startPingAnPortCode.getAreaCode())){
                insurance.setStartPortCityCode(startPingAnPortCode.getAreaCode());
            }else{
                if (StringUtils.isNotBlank(startPingAnPortCode.getCityCode())){
                    insurance.setStartPortCityCode(startPingAnPortCode.getCityCode());
                }else{
                    throw new Exception("出发地参数有误");
                }
            }
		}
		PingAnPortCode destPingAnPortCode = mapDictService.fingPingAnPortCode(insurance.getDestCity(), insurance.getDestProvinc());
		if(destPingAnPortCode==null){
			throw new Exception("目的地参数有误");
		}else {
            if (StringUtils.isNotBlank(destPingAnPortCode.getAreaCode())){
                insurance.setTerminiCityCode(destPingAnPortCode.getAreaCode());
            }else{
                if (StringUtils.isNotBlank(destPingAnPortCode.getCityCode())){
                    insurance.setTerminiCityCode(destPingAnPortCode.getCityCode());
                }else{
                    throw new Exception("目的地参数有误");
                }
            }
			insurance.setTerminiProvinceCode(destPingAnPortCode.getProvinceCode());
		}

		//中转地
		StringBuffer viaLoc = new StringBuffer();
		String transferStation1 = insurance.getTransferStation1();
		String transferStation2 = insurance.getTransferStation2();
		String transferStation3 = insurance.getTransferStation3();
		String transferStation4 = insurance.getTransferStation4();
		if (StringUtils.isNotBlank(transferStation1)) {
			tsInsurance.setTransferStation1(transferStation1);
			viaLoc.append(transferStation1).append(",");
		}
		if (StringUtils.isNotBlank(transferStation2)) {
			tsInsurance.setTransferStation2(transferStation2);
			viaLoc.append(transferStation2).append(",");
		}
		if (StringUtils.isNotBlank(transferStation3)) {
			tsInsurance.setTransferStation3(transferStation3);
			viaLoc.append(transferStation3).append(",");
		}
		if (StringUtils.isNotBlank(transferStation4)) {
			tsInsurance.setTransferStation4(transferStation4);
			viaLoc.append(transferStation4).append(",");
		}
		if (insurance.getTsId()!=null) {
			tsInsurance.setTsId(insurance.getTsId());
		}
		//保额
		tsInsurance.setAmtCurrency(new BigDecimal(insurance.getAmtCurrency()).movePointRight(2).intValue());
        //保费计算公式需要根据是华泰货运险还是人保货运险进行区分
		CalcCostBean calcCost = new CalcCostBean();
		//如果为人保货运险
		/*if(type != null && type.intValue() == 2) {
			//2019.3.7改为和华泰相同的保费计算规则
			calcCost = calcCost(insurance.getAmtCurrency(), insurance.getStartProvinc(), insurance.getStartCity(), insurance.getStartArea(),
					insurance.getDestProvinc(), insurance.getDestCity(), insurance.getDestArea());
		}else{ //华泰货运险
			calcCost = calcCost(insurance.getAmtCurrency(), insurance.getStartProvinc(), insurance.getStartCity(), insurance.getStartArea(),
					insurance.getDestProvinc(), insurance.getDestCity(), insurance.getDestArea());
		}*/
		calcCost=pingAnPremiumCalculation(insurance.getAmtCurrency(), insurance.getStartProvinc(), insurance.getStartCity(), insurance.getStartArea(),
				insurance.getDestProvinc(), insurance.getDestCity(), insurance.getDestArea());
		//距离
		BigDecimal distince = calcCost.getDistance().movePointRight(2);
		tsInsurance.setDistance(distince.intValue());
		//保费系数 小数点右移五位
        BigDecimal premium = calcCost.getPremium().movePointRight(5);
		tsInsurance.setPremium(premium.intValue());
		//保费
		tsInsurance.setPremiumCurrency(new BigDecimal(insurance.getPremiumCurrency()).movePointRight(2).intValue());
		tsInsurance.setCategory(insurance.getCategory());
		tsInsurance.setStatus(0);
		tsInsurance.setDelStatus(0);
		tsInsurance.setCtime(new Date());
		tsInsurance.setUtime(new Date());
		tsInsurance.setApplicantType(insurance.getApplicantType());
		tsInsurance.setInsuredType(insurance.getInsuredType());
		String orderNo = tytSequenceService.updateGetNumberForDateTime(channelCode,TimeUtil.formatDateTimeForyyyyMMddHHmmss(new Date()),"tyt_insurance_orders",8d);
		tsInsurance.setOrderNo(orderNo);
		this.add(tsInsurance);

		User user = userService.getByUserId(insurance.getUserId());
		String inputName = user.getTrueName();
		if(StringUtils.isBlank(inputName)) { // trueName存在NULL的情况
			inputName = user.getUserName();
		}
		if(inputName.length() > 5) { // trueName存在大于5个字和胡乱输入的情况
			inputName = inputName.substring(0, 5);
		}
		insurance.setTrueName(inputName);
		InsureReturn insureReturn = PingAnUtil.pingAnInsure(insurance, tsInsurance, goodsName, viaLoc, calcCost);
        if (!"999999".equals(insureReturn.getData().getResponseCode())){
            throw new Exception("投保失败！");
        }
        /*//根据不同的保险类型判断调用哪个公司的投保申请接口
		//人保支付页面的Url
		String piccPayUrl = "";
		//人保货运险
		if(type != null && type.intValue() == 2)
		{
			//人保货运险支付下单,获取支付页面Url
			piccPayUrl = savePICCPayInfo(insurance, tsInsurance, goodsName, viaLoc, calcCost);
			if(StringUtils.isBlank(piccPayUrl)) {
				throw new Exception("获取人保支付页面Url失败");
			}
		}else{ //华泰货运险
			//保单号
			String number = HtbxUtil.insuranceApply(tsInsurance);
			if (StringUtils.isBlank(number)) {
				throw new Exception("获取保单号失败");
			}else{
				tsInsurance.setInsuranceNo(number);
				this.update(tsInsurance);
			}
		}*/
		String pinganPayUrl = "";
        if ("0".equals(insureReturn.getRet())){
            logger.info("投保通讯成功！");
            String amount = insurance.getPremiumCurrency();
            //String productName = "国内运输(公路、水路、铁路、航空)险";
            String productName = insureReturn.getData().getResult().getProductName();
            String applyPolicyNo = insureReturn.getData().getResult().getApplyPolicyNo();
            String noticeNo = insureReturn.getData().getResult().getNoticeNo();
            pinganPayUrl = pinganInsuranceService.getPinganPayUrl(amount,productName,orderNo,applyPolicyNo,noticeNo,insurance.getInsuredName());
            if(StringUtils.isBlank(pinganPayUrl)) {
                throw new Exception("获取平安支付页面Url失败");
            }
            tsInsurance.setPiccPayUrl(pinganPayUrl);
            tsInsurance.setInsuranceNo(insureReturn.getData().getResult().getApplyPolicyNo());
            this.update(tsInsurance);

            //保存支付记录表
            HtPayRecord htPayRecord =new HtPayRecord();
            htPayRecord.setCtime(new Date());
            htPayRecord.setMoney(tsInsurance.getPremiumCurrency());
            htPayRecord.setOrderNo(tsInsurance.getOrderNo());
            htPayRecord.setStatus(0);
            htPayRecord.setTiId(tsInsurance.getId());
            htPayRecord.setUtime(new Date());
            htPayRecordService.add(htPayRecord);
        }
		Map<String,Object> map = new HashMap<String,Object>();
		//保单Id
		map.put("insuranceId",tsInsurance.getId());
		//保险大类  1：华泰货运险 2：人保货运险
		map.put("type",tsInsurance.getType());
		/*//人保支付页面的Url
		map.put("piccPayUrl",piccPayUrl);*/
		// 平安保险支付页面url
        map.put("piccPayUrl",pinganPayUrl);
		return map;
	}

	/**
	 * @Description  人保货运险支付下单
	 * <AUTHOR>
	 * @Date  2019/1/11 11:35
	 * @Param [insurance, tsInsurance, goodsName, viaLoc, calcCost]
	 * @return java.lang.String
	 **/
	private String savePICCPayInfo(InsuranceSaveBean insurance, TransportInsurance tsInsurance, StringBuffer goodsName, StringBuffer viaLoc, CalcCostBean calcCost) throws Exception {

		SubmitInsuranceBean submitInsuranceBean = new SubmitInsuranceBean();
		submitInsuranceBean.setInsuranceNo(tsInsurance.getOrderNo());
		//投保人类型  1个人  2公司 --> 01 身份证 37 统一社会信用代码
		String applicantCardType = "";
		if(insurance.getApplicantType() != null)
		{
			if(insurance.getApplicantType().intValue() == 1) //个人
			{
				applicantCardType = "01";
			}else if(insurance.getApplicantType().intValue() == 2){//公司
				applicantCardType = "37";
			}
			submitInsuranceBean.setApplicantCardType(applicantCardType);
		}
		submitInsuranceBean.setApplicantCardNo(insurance.getApplicantId());
		submitInsuranceBean.setApplicantName(insurance.getApplicantName());
		submitInsuranceBean.setApplicantMobile(insurance.getApplicantPhone());
		//被保人类型 1个人 2公司
		String insurantCardType = "";
		if(insurance.getInsuredType() != null)
		{
			if(insurance.getInsuredType().intValue() == 1) //个人
			{
				insurantCardType = "01";
			}else if(insurance.getInsuredType().intValue() == 2){//公司
				insurantCardType = "37";
				submitInsuranceBean.setRecognizeeOrg(insurance.getInsuredId());
			}
			submitInsuranceBean.setInsurantCardType(insurantCardType);
		}
		submitInsuranceBean.setInsurantCardNo(insurance.getInsuredId());
		submitInsuranceBean.setInsurantName(insurance.getInsuredName());
		submitInsuranceBean.setInsurantMobile(insurance.getInsuredPhone());
		//投保人与被保人关系 9-其他
		submitInsuranceBean.setRelation("9");
		//保额
		submitInsuranceBean.setAmount(new BigDecimal(insurance.getAmtCurrency()).movePointRight(4).toString());
		//保费
		submitInsuranceBean.setPremium(new BigDecimal(insurance.getPremiumCurrency()).toString());
		//保险费率 -- 去除末尾多余的0,例如:0.30改为0.3
		submitInsuranceBean.setRatio(calcCost.getPremium().movePointRight(3).stripTrailingZeros().toPlainString());
		//起运地
		submitInsuranceBean.setFromLoc(insurance.getStartPoint());
		//目的地
		submitInsuranceBean.setToLoc(insurance.getDestPoint());
		//货物名称
		submitInsuranceBean.setGoodsName(goodsName.toString());
		//货物类型代码 632100-其他机械、仪器、仪表及其零件
		submitInsuranceBean.setGoodsTypeNo("632100");
		String[] goodsNames = goodsName.toString().split(",");
		//包装 X台
		submitInsuranceBean.setPackQty(goodsNames.length+"台");

		//传入的的起运时间  yyyy-MM-dd
		String startTime = insurance.getStartTime();
		String nowDateStr = TimeUtil.formatDate(new Date());

		//最终起运时间(拼接上小时)
		String startTimeStr = "";
		//起保时间转换后的日期
		Date startDate = null;
		//如果是当天，时间+1小时  2018-11-28 18:00:00
		if (nowDateStr.equals(startTime)) {
			//获取当前服务器的小时数 +1小时
			Calendar c = Calendar.getInstance();
			c.setTime(new Date());
			c.add(Calendar.HOUR_OF_DAY, 1);
			Date nextHourDate = c.getTime();

			startTimeStr = new SimpleDateFormat(DATE_FORMAT_HOUR).format(nextHourDate);
			startDate = nextHourDate;
		}else{ //如果大于当天 2018-11-29 00:00:00
			startTimeStr = startTime+":00";
			startDate = TimeUtil.parseDateString(startTime+" 00:00:00");
		}
		//起运日期 yyyy-MM-dd:HH
		submitInsuranceBean.setDepartureDate(startTimeStr);
		//起保时间 yyyy-MM-dd HH:mm:ss
		submitInsuranceBean.setStartTime(new SimpleDateFormat(DATE_FORMAT_SECOND).format(startDate));


		//货物重量
		submitInsuranceBean.setWeight(insurance.getWeight()+"吨");
		//中转地
		String viaLocStr = viaLoc.toString();
		if(StringUtils.isNotBlank(viaLocStr))
		{
			viaLocStr = viaLocStr.substring(0, viaLocStr.lastIndexOf(","));
			submitInsuranceBean.setViaLoc(viaLocStr);
		}
		//运输工具信息 格式：(车头车牌信息+挂车车牌信息)
		submitInsuranceBean.setTransport(insurance.getHeadNo()+","+insurance.getTailNo());
		//人保支付页面的Url
		String piccPayUrl = "";
		piccPayUrl = insuranceService.getPICCPayUrl(submitInsuranceBean);
		//人保支付页面的Url
		if(StringUtils.isNotBlank(piccPayUrl))
		{
			tsInsurance.setPiccPayUrl(piccPayUrl);
		}
		//设置保单号insuranceNo为保单Id
		tsInsurance.setInsuranceNo(tsInsurance.getId().toString());
		this.update(tsInsurance);


		//保存支付记录表
		HtPayRecord htPayRecord =new HtPayRecord();
		htPayRecord.setCtime(new Date());
		htPayRecord.setMoney(tsInsurance.getPremiumCurrency());
		htPayRecord.setOrderNo(tsInsurance.getOrderNo());
		htPayRecord.setStatus(0);
		htPayRecord.setTiId(tsInsurance.getId());
		htPayRecord.setUtime(new Date());
		htPayRecordService.add(htPayRecord);
		return piccPayUrl;
	}

	@Override
	public Map<String,Object> updateInsurance(InsuranceSaveBean insurance) throws Exception{
		delAutInsurance(insurance.getId());
		Map<String,Object> map = saveInsurance(insurance);
		return map;
	}

	@Override
	public void delInsurance(Long insuranceId) {
		String sql="UPDATE transport_insurance SET del_status=1,utime=NOW()  WHERE id=?";
		this.executeUpdateSql(sql, new Object[]{insuranceId});

	}

    @Override
    public TsInsuranceListBean getMyList(Long userId, Integer opt, Long lastId) {
		Integer pageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.page.size");
//        Integer pageSize = 4;
        List<Object> paramList = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder("select * from transport_insurance where del_status = 0 and user_id = ? ");
        paramList.add(userId);
        switch (opt) {
            case 1: { // 待支付
                sql.append(" and status = 0");
                break;
            }
            case 2: { // 已生效
                sql.append(" and status = 1 and  effective_time > now() ");
                break;
            }
            case 3: { // 已失效
                sql.append(" and ( (status = 1 and effective_time < now())  or status = 2 )");
                break;
            }
            default: { // 默认或opt=1，均为全部
                break;
            }
        }
        if(lastId.longValue() == 0) {
			sql.append(" and id > ? ");
		} else {
			sql.append(" and id < ? ");
		}
		sql.append(" order by id desc");
        paramList.add(lastId);
        List<TransportInsurance> insurances = this.getBaseDao().search(sql.toString(), paramList.toArray(), 0, pageSize);
        // 组装页面需要数据
        Integer waitPay = this.getCountByStatus(userId, 0);
        Integer effective = this.getCountByStatus(userId, 1);
        if (CollectionUtils.isNotEmpty(insurances)) {
            lastId = insurances.get(insurances.size() - 1).getId();
        }
        TsInsuranceListBean bean = new TsInsuranceListBean();
        bean.setInsurances(insurances);
        bean.setWaitPay(waitPay);
        bean.setEffective(effective);
        bean.setLastId(lastId);
        return bean;
    }

    @Override
    public Integer getCountByStatus(Long userId, Integer status) {
        String sql = "select count(id) from transport_insurance where del_status = 0 and user_id = ? and status = ?";
       if(status.intValue()==1){
    	   sql=sql+" and  effective_time > now() ";
       }
        final Object[] params = {
                userId,
                status
        };
        BigInteger count = this.getBaseDao().query(sql, params);
        return count.intValue();
    }

    @Override
    public List<ImportWaybillBean> importWaybill(Long userId, Integer opt) throws Exception{
        // 查询数据
        if (opt.intValue() == 1) { // 1.我是车方
			return this.isCarry(userId);
        } else if (opt.intValue() == 2) { // 2.我是货方
			return this.isShipper(userId);
        }
        return null;
    }

//    @Override
//    public List<ImportWaybillBean> isShipper(Long userId) {
//        // MT表获取人工派单的数据
//        List<TransportMt> mtList = transportMtService.getAgreeList(userId);
//        List<Long> ids = new ArrayList<>();
//        Map<Long, CarOwnerIntentionBean> carMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(mtList)) {
//            for (TransportMt mt : mtList) {
//                ids.add(mt.getTsId());
//            }
//            // 通过人工派单的数据查询相关车辆
//            List<CarOwnerIntentionBean> carList = carOwnerService.getCarOwnerListByTsIds(ids);
//            if (carList != null) {
//                for (CarOwnerIntentionBean bean : carList) {
//                    // 存储至MAP，便于后续数据整合
//                    carMap.put(bean.getSrcMsgId(), bean);
//                }
//            }
//        }
//        // 查询主表内的发货数据，通过用户ID，和上面的tsid集合及3天时间内的
//        List<TransportMain> mainList = transportMainService.getTransportListByIds(userId, ids, 3);
//        // 组装接口对象返回数据
//        List<ImportWaybillBean> importWaybillBeans = new ArrayList<>();
//        ImportWaybillBean importBean = null;
//        for (TransportMain main : mainList) {
//            importBean = new ImportWaybillBean();
//            importBean.setTsId(main.getId());
//            importBean.setTsOrderNo(main.getTsOrderNo());
//            importBean.setStartPoint(main.getStartPoint());
//            importBean.setStartProvinc(main.getStartProvinc());
//            importBean.setStartCity(main.getStartCity());
//            importBean.setStartArea(main.getStartArea());
//            importBean.setDestPoint(main.getDestPoint());
//            importBean.setDestProvinc(main.getDestProvinc());
//            importBean.setDestCity(main.getDestCity());
//            importBean.setDestArea(main.getDestArea());
//            importBean.setTaskContent(main.getTaskContent());
//            importBean.setWeight(main.getWeight());
//            importBean.setCtime(main.getCtime());
//            CarOwnerIntentionBean carBean = carMap.get(main.getSrcMsgId());
//            if (carBean != null) { // 设置有车的对象
//                importBean.setHeadNo(carBean.getHeadCity() + carBean.getHeadNo());
//                importBean.setTailNo(carBean.getTailCity() + carBean.getTailNo());
//            }
//            importWaybillBeans.add(importBean);
//        }
//        return importWaybillBeans;
//    }

    @Override
    public List<ImportWaybillBean> isShipper(Long userId) throws Exception {
        // 查询主表内的发货数据，通过用户ID，和上面的tsid集合及3天时间内的
        List<TransportMain> mainList = transportMainService.getImportTransportByUserId(userId, 3);
        // 组装接口对象返回数据
        if (mainList != null && mainList.size()>0){
            List<ImportWaybillBean> importWaybillBeans = new ArrayList<>();
            ImportWaybillBean importBean = null;
            for (TransportMain main : mainList) {
                importBean = new ImportWaybillBean();
                importBean.setTsId(main.getId());
                importBean.setTsOrderNo(main.getTsOrderNo());
                importBean.setStartPoint(main.getStartPoint());
                importBean.setStartProvinc(main.getStartProvinc());
                importBean.setStartCity(main.getStartCity());
                importBean.setStartArea(main.getStartArea());
                importBean.setDestPoint(main.getDestPoint());
                importBean.setDestProvinc(main.getDestProvinc());
                importBean.setDestCity(main.getDestCity());
                importBean.setDestArea(main.getDestArea());
                importBean.setTaskContent(main.getTaskContent());
                importBean.setWeight(main.getWeight());
                importBean.setCtime(main.getCtime());
                importWaybillBeans.add(importBean);
            }
            return importWaybillBeans;
        }
        return null;
    }

    @Override
    public List<ImportWaybillBean> isCarry(Long userId) {
        List<Long> ids = transportOrdersService.getTsIdsByRobStatus(userId, 3, "4", "5", "6");
        if (ids == null){
        	return null;
		}
        // 查询主表内的发货数据，通过用户ID，和上面的tsid集合及3天时间内的
        List<TransportMain> mainList = transportMainService.getTransportListByIds(ids);
        // 组装接口对象返回数据
        List<ImportWaybillBean> importWaybillBeans = new ArrayList<>();
        ImportWaybillBean importBean = null;
        for (TransportMain main : mainList) {
            importBean = new ImportWaybillBean();
            importBean.setTsId(main.getId());
            importBean.setTsOrderNo(main.getTsOrderNo());
            importBean.setStartPoint(main.getStartPoint());
            importBean.setStartProvinc(main.getStartProvinc());
            importBean.setStartCity(main.getStartCity());
            importBean.setStartArea(main.getStartArea());
            importBean.setDestPoint(main.getDestPoint());
            importBean.setDestProvinc(main.getDestProvinc());
            importBean.setDestCity(main.getDestCity());
            importBean.setDestArea(main.getDestArea());
            importBean.setTaskContent(main.getTaskContent());
            importBean.setWeight(main.getWeight());
            importBean.setCtime(main.getCtime());
            importWaybillBeans.add(importBean);
        }
        return importWaybillBeans;
    }

    public TransportInsurance getTransportInsuranceForOrderNo(String orderNo) {
        String hql = "from TransportInsurance where orderNo=?";
        List<TransportInsurance> list = this.getBaseDao().find(hql, orderNo);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    @Override
    public CalcCostBean calcCost(String amtCurrency, String startProvinc, String startCity, String startArea,
                                 String destProvinc, String destCity, String destArea) {
        TytMapDict mapDict =mapDictService.getDistance(null,startProvinc,startCity,startArea,
                destProvinc,destCity,destArea);
//        if (mapDict == null || mapDict.getDistance() == null || mapDict.getDistance().equals(0)) { // 如果未查询到距离，返回null
//            return null;
//        }
        String insuranceRate = tytConfigService.getStringValue("tyt_insurance_rate", "0-300#0.00020|300.01-800#0.00025|800.01-10000000#0.0003");
        String insurancediscount = tytConfigService.getStringValue("tyt_insurance_discount", "0.8"); // 折扣费率
        String[] rates = insuranceRate.split("\\|"); // 分割档位
		String distanceRange = null; // 保费距离范围
		BigDecimal ficDistance=null;
		BigDecimal distance = null;
		if (mapDict == null || mapDict.getDistance() == null) { // 如果未查询到距离，返回null
			ficDistance=new BigDecimal("550");
        }else if(mapDict != null && mapDict.getDistance() == 0){
        	ficDistance=new BigDecimal("0");
        }else{
        	ficDistance = new BigDecimal(mapDict.getDistance()).movePointLeft(2);
        }
		distance=ficDistance;
         // 距离
		BigDecimal amtCurrencyDecimal = new BigDecimal(amtCurrency).movePointRight(4);
		BigDecimal premium = new BigDecimal("0"); // 保费系数
		for (String str : rates) {
			String[] details = str.split("#"); // 分割距离范围和系数
			distanceRange = details[0];
			String[] drs = distanceRange.split("-"); // 分割范围起始
			Double start = new BigDecimal(drs[0]).doubleValue();
			Double end = new BigDecimal(drs[1]).doubleValue();
			if (ficDistance.doubleValue() >= start && ficDistance.doubleValue() <= end) {
				premium = new BigDecimal(details[1]);
				break;
			}
		}
		BigDecimal premiumCurrency = amtCurrencyDecimal.multiply(premium).setScale(2, BigDecimal.ROUND_HALF_UP); // 保费金额-折扣后金额, 打8折
		BigDecimal originalPrice = premiumCurrency.divide(new BigDecimal(insurancediscount)).setScale(2, BigDecimal.ROUND_HALF_UP); // 保费原价格
		// 回填数据
		CalcCostBean bean = new CalcCostBean();
		bean.setAmtCurrency(amtCurrencyDecimal.movePointLeft(4));
		bean.setDistance(distance);
		bean.setDistanceRange(distanceRange);
		bean.setPremium(premium);
		bean.setPremiumCurrency(premiumCurrency);
		bean.setOriginalPrice(originalPrice);
        return bean;
    }

    /**
     * @Description  计算保费(人保货运险)
     * <AUTHOR>
     * @Date  2018/11/27 19:09
     * @Param [amtCurrency, startProvinc, startCity, startArea, destProvinc, destCity, destArea]
     * @return com.tyt.tsinsurance.bean.CalcCostBean
     **/
	@Override
	public CalcCostBean calcPICCCost(String amtCurrency, String startProvinc, String startCity, String startArea, String destProvinc, String destCity, String destArea) {

		/**
		 1.投保费率：
		 ①、起止地点涉及：广西、甘肃、新疆、西藏、云南、贵州、四川的，按照0.35‰费率。
		 ②、起止地点未涉及①所含地点的，按照0.3‰费率。
		 2.保费计算策略：保费=保险金额*费率
		 **/
		//人保-特殊地区的保险费率
		String piccRateSpecial = tytConfigService.getStringValue("picc_insurance_rate_special", "广西|甘肃|新疆|西藏|云南|贵州|四川#0.00035");
		//人保-普通地区的保险费率
		String piccRateNormal = tytConfigService.getStringValue("picc_insurance_rate_normal", "0.00030");
        //折扣费率
		String insurancediscount = tytConfigService.getStringValue("tyt_insurance_discount", "0.8");

		//分割特殊地区保险费率的档位
		String[] specialRates = piccRateSpecial.split("#");
		//特殊地区的数组集合
		String[] specilaAreas = specialRates[0].split("\\|");
        //特殊地区的保险费率
		String specialRate = specialRates[1];

		//最终使用的保险费率
		BigDecimal premium = new BigDecimal("0"); // 保费系数
		//1.判断起止地点是否在特殊地区范围内，如果在则使用特殊保险费率，否则使用普通保险费率
		if(specilaAreas != null)
		{
			for (String specilaArea : specilaAreas) {
				if(startProvinc.contains(specilaArea) || destProvinc.contains(specilaArea))
				{
					premium = new BigDecimal(specialRate);
					break;
				}else{
					premium = new BigDecimal(piccRateNormal);
				}
			}
		}else{
			premium = new BigDecimal(piccRateNormal);
		}
        //2. 计算保费 保费=保险金额*费率
		//保险金额 单位：万元，向右移动四位
		BigDecimal amtCurrencyDecimal = new BigDecimal(amtCurrency).movePointRight(4);
		//保费金额-折扣后金额, 打8折
		BigDecimal premiumCurrency = amtCurrencyDecimal.multiply(premium).setScale(2, BigDecimal.ROUND_HALF_UP);
		//保费原价格
		BigDecimal originalPrice = premiumCurrency.divide(new BigDecimal(insurancediscount)).setScale(2, BigDecimal.ROUND_HALF_UP);

		//3.查询起止地点之间的距离
		TytMapDict mapDict =mapDictService.getDistance(null,startProvinc,startCity,startArea,destProvinc,destCity,destArea);
		BigDecimal ficDistance=null;
		BigDecimal distance = null;
		if (mapDict == null || mapDict.getDistance() == null) { // 如果未查询到距离，返回null
			ficDistance=new BigDecimal("550");
		}else if(mapDict != null && mapDict.getDistance() == 0){
			ficDistance=new BigDecimal("0");
		}else{
			ficDistance = new BigDecimal(mapDict.getDistance()).movePointLeft(2);
		}
		distance=ficDistance;

		// 回填数据
		CalcCostBean bean = new CalcCostBean();
		bean.setAmtCurrency(amtCurrencyDecimal.movePointLeft(4));
		bean.setDistance(distance);
//		bean.setDistanceRange(distanceRange);
		bean.setPremium(premium);
		bean.setPremiumCurrency(premiumCurrency);
		bean.setOriginalPrice(originalPrice);
		return bean;
	}

	@Override
	public void delAutInsurance(Long insuranceId) {
		String sql="UPDATE transport_insurance SET del_status=2,utime=NOW()  WHERE id=?";
		this.executeUpdateSql(sql, new Object[]{insuranceId});
	}

    /**
     * @Description  根据货源Id查询是否有关联的保单信息
     * <AUTHOR>
     * @Date  2018/11/28 15:49
     * @Param [tsId]
     * @return java.lang.Long 最后一次购买的保单Id(支付成功)
     **/
	@Override
	public TransportInsurance queryInsuranceByTsId(Long userId,Long tsId) throws Exception {

        //查询保单的sql语句
		String sql = "select * from transport_insurance where del_status = 0 and status = 1 and user_id = ? and ts_id = ? "
				     + " order by utime desc";
		Object[] params = {userId,tsId};
		List<TransportInsurance> insurances = this.getBaseDao().queryForList(sql, params);
		if(insurances != null && insurances.size() > 0){
			//最新的保单对象
			TransportInsurance transportInsurance = insurances.get(0);
			return transportInsurance;
		}
		return null;
	}


	@Override
	public CalcCostBean pingAnPremiumCalculation(String amtCurrency, String startProvinc, String startCity, String startArea, String destProvinc, String destCity, String destArea) {
		logger.info("开始保费计算--------------------------------");
		logger.info("保额--------------------------------"+amtCurrency);
		logger.info("开始省--------------------------------"+startProvinc);
		logger.info("开始市区--------------------------------"+startCity);
		logger.info("开始区县--------------------------------"+startArea);
		logger.info("目的地省--------------------------------"+destProvinc);
		logger.info("目的地市区--------------------------------"+destCity);
		logger.info("目的地区县--------------------------------"+destArea);
		//折扣费率
		String insurancediscount = tytConfigService.getStringValue("tyt_insurance_discount", "0.8");
		//判断是否是特殊路线
		BigDecimal distance = distanceCalculation(startProvinc, startCity, startArea, destProvinc, destCity, destArea);
		BigDecimal rate=null;
		String specialRoute = tytConfigService.getStringValue("pingan_special_route", "新疆,西藏,青海");
		String[] specialRoutes = specialRoute.split(",");
		for (String route : specialRoutes) {
			if(startProvinc.contains(route)||destProvinc.contains(route)){
				rate=new BigDecimal(server.getString("pingan.special.rate"));
			}
		}
		if (rate==null){
			//判断是否是山区路线
			String mountainArea = tytConfigService.getStringValue("pingan_mountain_route", "云南,贵州,四川,甘肃,宁夏,蒙古");
			String[] mountainAreas = mountainArea.split(",");
			for (String area : mountainAreas) {
				if(startProvinc.contains(area)||destProvinc.contains(area)){
					rate=new BigDecimal(server.getString("pingan.mountain.rate"));
				}
			}
			//无特殊情况
			if (rate==null){
				if (distance.compareTo(new BigDecimal(300))==1){
					rate=new BigDecimal(server.getString("pingan.routine.max.rate"));
				}else {
					rate=new BigDecimal(server.getString("pingan.routine.min.rate"));
				}
			}
		}
		//保险金额 单位：万元，向右移动四位
		BigDecimal amtCurrencyDecimal = new BigDecimal(amtCurrency).movePointRight(4);
		//保费金额-折扣后金额, 打8折
		BigDecimal premiumCurrency = amtCurrencyDecimal.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
		//保费原价格
		BigDecimal originalPrice = premiumCurrency.divide(new BigDecimal(insurancediscount)).setScale(2, BigDecimal.ROUND_HALF_UP);
		CalcCostBean bean = new CalcCostBean();
		bean.setAmtCurrency(amtCurrencyDecimal.movePointLeft(4));
		bean.setDistance(distance);
		bean.setPremium(rate);
		bean.setPremiumCurrency(premiumCurrency);
		bean.setOriginalPrice(originalPrice);
		return bean;
	}

	public BigDecimal distanceCalculation(String startProvinc, String startCity, String startArea, String destProvinc, String destCity, String destArea){
		TytMapDict mapDict =mapDictService.getDistance(null,startProvinc,startCity,startArea,destProvinc,destCity,destArea);
		BigDecimal ficDistance=null;
		// 如果未查询到距离，返回null
		if (mapDict == null || mapDict.getDistance() == null) {
			ficDistance=new BigDecimal("550");
		}else if(mapDict != null && mapDict.getDistance() == 0){
			ficDistance=new BigDecimal("0");
		}else{
			ficDistance = new BigDecimal(mapDict.getDistance()).movePointLeft(2);
		}
		return ficDistance;
	}

	@Override
	public TransportInsurance getByInsuranceNo(String insuranceNo){
	    String sql ="from TransportInsurance where insuranceNo=?";
        List<TransportInsurance> list=this.getBaseDao().find(sql, insuranceNo);
        if(list!=null &&list.size()>0){
            return list.get(0);
        }
        return null;
    }

    @Override
    public SaleReturnActivityBean getCountAndCurrency(Long userId) {
	    String atime = tytConfigService.getStringValue("insurance_activity_time","2021-11-11#2021-12-12");
        String[] times = atime.split("#");
	    String startTime =times[0]+" 00:00:00";
	    String endTime = times[1]+" 23:59:59";
        String sql = "SELECT COUNT(*) num,SUM(premium_currency)/100 currency FROM `transport_insurance` WHERE user_id=? AND status=1 AND pay_time>? AND pay_time<?";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("num", Hibernate.INTEGER);
        scalarMap.put("currency", Hibernate.DOUBLE);
        List<SaleReturnActivityBean> bean = this.getBaseDao().search(sql, scalarMap, SaleReturnActivityBean.class, new Object[]{userId,startTime,endTime});
        if (bean.size()>0){
            return bean.get(0);
        }
        return new SaleReturnActivityBean();
    }

}
