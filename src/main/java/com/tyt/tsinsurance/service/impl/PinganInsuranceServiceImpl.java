package com.tyt.tsinsurance.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyt.common.service.TytSequenceService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.HtPayRecord;
import com.tyt.model.TransportInsurance;
import com.tyt.model.TransportInsuranceSub;
import com.tyt.service.common.common.HttpClientFactory;
import com.tyt.service.common.json.CreateObjectMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.tsinsurance.bean.pingan.PinganAcceptBean;
import com.tyt.tsinsurance.bean.pingan.PinganPayBean;
import com.tyt.tsinsurance.bean.pingan.PinganPdfGetBean;
import com.tyt.tsinsurance.service.HtPayRecordService;
import com.tyt.tsinsurance.service.PinganInsuranceService;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.tsinsurance.service.TsInsuranceSubService;
import com.tyt.tsinsurance.util.PingAnUtil;
import com.tyt.util.*;
import com.tyt.util.httputil.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Service("pinganInsuranceService")
public class PinganInsuranceServiceImpl implements PinganInsuranceService {
    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytSequenceService")
    private TytSequenceService tytSequenceService;
    @Resource(name="htPayRecordService")
    private HtPayRecordService htPayRecordService;
    @Resource(name="tsinsuranceService")
    private TsInsuranceService tsinsuranceService;
    @Resource(name="tsinsuranceSubService")
    private TsInsuranceSubService tsinsuranceSubService;

    @Value("${picc.payment.callback.url}")
    private String paymentCallbackUrl;

    static PropertiesUtil server = PropertiesUtil.init("server");
    private static ObjectMapper mapper = CreateObjectMapper.instance();
    private static CloseableHttpClient httpClient = HttpClientFactory.getHttpClientWithRetry();

    @Override
    public String getPinganPayUrl(String amount, String productName,String orderNo, String applyPolicyNo, String noticeNo, String customName) throws Exception{

        PinganPayBean bean = new PinganPayBean();
        bean.setTradeNo(orderNo);
        bean.setAmount(amount);
        bean.setCurrency_no("CNY");
        bean.setProductName(productName);
        bean.setCustomerName(customName);
        bean.setProductType("02");
        bean.setProductCode("**********");
        bean.setPartnerName("P_BJBLD_GP");
        bean.setApplyPolicyNo(applyPolicyNo);
        bean.setNoticeNo(noticeNo);
        bean.setReturnUrl("");
//        bean.setFrontNotifyUrl(paymentCallbackUrl+"?status=1&payMethodlock=1");
        bean.setFrontNotifyUrl("");
        //signMsg加密规则：合作伙伴用户名+产品代码+投保人(客户)+金额+通知单号
        String msg = bean.getPartnerName()+bean.getProductCode()+bean.getCustomerName()+String.valueOf(amount)+noticeNo;
        String signMsg = SHA256Util.sha256LowerCase(msg);
        bean.setSignMsg(signMsg);
        //接口名+时间戳
        String request_id="prePayFinance"+System.currentTimeMillis();
        String access_token=RedisUtil.get(Constant.PING_AN_TOKEN_KEY);
        if (StringUtils.isBlank(access_token)){
            access_token = PingAnUtil.getPingAnToken();
        }
        String url = server.getString("pingan.insurance.payUrl")+"?access_token="+access_token+"&request_id="+request_id;
        HttpPost httpPost = new HttpPost(url);
        String map = mapper.writeValueAsString(bean);
        StringEntity body = new StringEntity(map, "UTF-8");
        logger.info("pinganpay requestParam:"+mapper.writeValueAsString(bean));
        System.out.println(mapper.writeValueAsString(bean));
        body.setContentType("application/json;charset=UTF-8");
        httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
        httpPost.addHeader("Accept", "application/json;charset=UTF-8");
        httpPost.setEntity(body);
        CloseableHttpResponse response = httpClient.execute(httpPost);
        logger.info(response.toString());
        try {
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
                return null;
            }
            String strContent = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            JSONObject jsonObject = JSON.parseObject(strContent);
            if ("0".equals(jsonObject.getString("ret"))) {
                JSONObject data = jsonObject.getJSONObject("data");
                String resultCode = data.getString("resultCode");
                if ("SUCCESS".equals(resultCode)){
                    String returnTradeNo = data.getString("tradeNo");
                    return data.getString("payUrl");
                }else{
                    String resultMsg = data.getString("resultMsg");
                    logger.info("支付链接获取失败，失败原因为："+resultMsg);
                }
            }else if ("13012".equals(jsonObject.getString("ret"))){
                RedisUtil.del(Constant.PING_AN_TOKEN_KEY);
                logger.info("access_token已失效");
            }else{
                logger.info("通讯失败，错误信息为："+jsonObject.getString("msg")+",requestId:"+jsonObject.getString("requestId"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("pinganPay平安保险支付链接获取异常：", e);
            throw new Exception();
        } finally {
            HttpClientUtils.closeQuietly(response);
        }
        return null;
    }

    @Override
    public void savePinganCallBack(TransportInsurance transportInsurance, PinganAcceptBean callBackBean) throws Exception{
        //修改 支付记录表
        HtPayRecord htPayRecord =htPayRecordService.getHtPayRecord(transportInsurance.getOrderNo());
        if("SUCCESS".equals(callBackBean.getResultCode())){
            htPayRecord.setStatus(1);
        }else{
            htPayRecord.setStatus(2);
        }
        htPayRecord.setPayTime(new Date());
        htPayRecord.setUtime(new Date());
        htPayRecordService.update(htPayRecord);
        logger.info(htPayRecord.getOrderNo()+"支付记录表修改成功！");

        //支付成功 取得保单信息，投保确认
        if("SUCCESS".equals(callBackBean.getResultCode())){
            if(transportInsurance.getStatus().intValue()==0){
                //修改 保单表
                transportInsurance.setNumber(callBackBean.getPolicyNo());
                transportInsurance.setStatus(1);
                transportInsurance.setPolicyCtime(new Date());
                transportInsurance.setPayTime(new Date());
                String now = TimeUtil.formatDate(new Date());
                if (now.equals(TimeUtil.formatDate(transportInsurance.getStartTime()))) {
                    transportInsurance.setEffectiveTime(TimeUtil.addDay(new Date(), 30));
                }
                transportInsurance.setUtime(new Date());
                tsinsuranceService.update(transportInsurance);
                logger.info(transportInsurance.getOrderNo()+"保险主表修改成功！");
                String fileName = savePdf(callBackBean.getPolicyNo());
                if (fileName!=null){
                    transportInsurance.setImgUrl(fileName);
                }
                tsinsuranceService.update(transportInsurance);
                //插入 保险流水表
                TransportInsuranceSub tisub =new TransportInsuranceSub();
                tisub.setApplicantName(transportInsurance.getApplicantName());
                tisub.setApplicantPhone(transportInsurance.getApplicantPhone());
                tisub.setCellPhone(transportInsurance.getCellPhone());
                tisub.setCompany(transportInsurance.getCompany());
                tisub.setCtime(new Date());
                tisub.setCurrency(transportInsurance.getPremiumCurrency());
                tisub.setInsuredName(transportInsurance.getInsuredName());
                tisub.setInsuredPhone(transportInsurance.getInsuredPhone());
                tisub.setNumber(transportInsurance.getNumber());
                tisub.setPid(transportInsurance.getId());
                tisub.setRetreatType(1);
                tisub.setType(transportInsurance.getType());
                tisub.setUserId(transportInsurance.getUserId());
                tisub.setUtime(new Date());
                tsinsuranceSubService.add(tisub);
                logger.info(transportInsurance.getOrderNo()+"保险流水表保存成功！");
            }else{
                if (StringUtils.isBlank(transportInsurance.getImgUrl())){
                    String fileName = savePdf(callBackBean.getPolicyNo());
                    if (fileName!=null){
                        transportInsurance.setImgUrl(fileName);
                        tsinsuranceService.update(transportInsurance);
                    }
                }
                logger.info(transportInsurance.getOrderNo()+"保单已进行过回调");
            }
        }else{
            logger.info("pingan 支付失败，code:"+callBackBean.getResultCode()+"，msg:"+callBackBean.getResultMsg());
        }
    }

    private String savePdf(String number) throws Exception{
        PinganPdfGetBean pdfBean = new PinganPdfGetBean();
        pdfBean.setPartnerCode("P_BJBLD_GP");
        pdfBean.setPolicyNo(number);
        //电子保单接口名+时间戳
        String request_id="printGP"+System.currentTimeMillis();
        String access_token=RedisUtil.get(Constant.PING_AN_TOKEN_KEY);
        if (StringUtils.isBlank(access_token)){
            access_token = PingAnUtil.getPingAnToken();
        }
        String url = server.getString("pingan.insurance.getPDF")+"?access_token="+access_token+"&request_id="+request_id;
        String response = HttpUtil.doPost(url,JSONObject.toJSONString(pdfBean));
        System.out.println(JSONObject.toJSONString(pdfBean));
        System.out.println(response);
        JSONObject jsonObject = JSON.parseObject(response);
        if ("0".equals(jsonObject.getString("ret"))) {
            JSONObject data = jsonObject.getJSONObject("data");
            String resultCode = data.getString("responseCode");
            if ("999999".equals(resultCode)) {
                String pdf = data.getString("returnPdfValue");
                if (StringUtils.isNotBlank(pdf)) {
                    String fileName = DownFileUtil.getSaveFilePathName("htbx", number + ".pdf");
                    logger.info("pabx_pdf_save_path_name=" + fileName);
                    Base64Util.base64StringToPdf(pdf,AppConfig.getProperty("picture.path.domain")+fileName);
                    return fileName;
                }else{
                    logger.info("获取pinganPdf失败,pdf文件为null");
                }
            } else {
                String responseMsg = data.getString("responseMsg");
                logger.info("获取pinganPdf失败，失败原因为：" + responseMsg);
            }
        }else{
            logger.info("pinganPdf通讯失败，失败原因为：" + jsonObject.getString("msg"));
        }
        return null;
    }

}
