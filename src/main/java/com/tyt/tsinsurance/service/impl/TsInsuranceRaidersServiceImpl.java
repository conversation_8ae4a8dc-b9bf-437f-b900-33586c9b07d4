package com.tyt.tsinsurance.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TransportInsuranceRaiders;
import com.tyt.tsinsurance.service.TsInsuranceRaidersService;

@Service("tsInsuranceRaidersService")
public class TsInsuranceRaidersServiceImpl extends
		BaseServiceImpl<TransportInsuranceRaiders, Long> implements TsInsuranceRaidersService{

	@Resource(name = "tsInsuranceRaidersDao")
	public void setBaseDao(BaseDao<TransportInsuranceRaiders, Long> tsInsuranceRaidersDao) {
		super.setBaseDao(tsInsuranceRaidersDao);
	}

	@Override
	public List<TransportInsuranceRaiders> getList() {
		String sql="SELECT r.id id,r.source source,r.news_title newsTitle,r.picture_url pictureUrl,"
				+ "r.open_url openUrl,r.publish_time publishTime,r.useful useful,r.useless useless FROM transport_insurance_raiders r ORDER BY id ";
		 Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
		 map.put("id", Hibernate.LONG);
		 map.put("source", Hibernate.STRING);
		 map.put("newsTitle", Hibernate.STRING);
		 map.put("pictureUrl", Hibernate.STRING);
		 map.put("openUrl", Hibernate.STRING);
		 map.put("publishTime", Hibernate.TIMESTAMP);
		 map.put("useful", Hibernate.INTEGER);
		 map.put("useless", Hibernate.INTEGER);
		 return this.getBaseDao().search(sql, map,TransportInsuranceRaiders.class, new Object[]{});
	}

	@Override
	public void updateInsureRaiders(Long id, Integer usefulOrUseless,String flg) {
		// usefulOrUseless  1: useful    2 :useless
		String sql="";
		if(flg.equals("add")){
			if(usefulOrUseless == 1){
				sql="UPDATE transport_insurance_raiders SET useful=useful+1 WHERE id=? ";
			}else if(usefulOrUseless == 2){
				sql="UPDATE transport_insurance_raiders SET useless=useless+1 WHERE id=?";
			}
		}else if(flg.equals("subtract")){
			if(usefulOrUseless == 1){
				sql="UPDATE transport_insurance_raiders SET useful=useful-1 WHERE id=? ";
			}else if(usefulOrUseless == 2){
				sql="UPDATE transport_insurance_raiders SET useless=useless-1 WHERE id=?";
			}
		}
		this.executeUpdateSql(sql, new Object[]{id});
	}
}
