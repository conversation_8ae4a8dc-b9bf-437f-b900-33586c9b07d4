package com.tyt.tsinsurance.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.htbx.HtbxUtil;
import com.tyt.htbx.IssuePolicyResult;
import com.tyt.htbx.PolicyIssueBean;
import com.tyt.model.HtPayCallBack;
import com.tyt.model.HtPayRecord;
import com.tyt.model.TransportInsurance;
import com.tyt.model.TransportInsuranceSub;
import com.tyt.tsinsurance.bean.PayStatusBean;
import com.tyt.tsinsurance.service.*;
import com.tyt.tsinsurance.util.PICCHttpUtil;
import com.tyt.util.DownFileUtil;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service("htPayCallBackService")
public class HtPayCallBackServiceImpl extends
		BaseServiceImpl<HtPayCallBack, Long> implements HtPayCallBackService {
	Logger logger = LoggerFactory.getLogger("htbxpay");
	@Resource(name = "htPayCallBackDao")
	public void setBaseDao(BaseDao<HtPayCallBack, Long> htPayCallBackDao) {
		super.setBaseDao(htPayCallBackDao);
	}

	@Autowired
	private IPICCInsuranceService piccService;

	@Resource(name="tsinsuranceService")
	private TsInsuranceService tsinsuranceService;
	
	@Resource(name="tsinsuranceSubService")
	private TsInsuranceSubService tsinsuranceSubService;
	
	@Resource(name="htPayRecordService")
	private HtPayRecordService htPayRecordService;

/*	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;
*/
	@Override
	public void saveAsyncCallback(String status, String orderNo,
			String payFinishTime, String channelCode, String transactionNo,
			String sign, String payMode) throws Exception {
		
		//修改 支付记录表
		HtPayRecord htPayRecord =htPayRecordService.getHtPayRecord(orderNo);
		if("1".equals(status)){
			htPayRecord.setStatus(1);
		}else
			htPayRecord.setStatus(2);
		htPayRecord.setPayTime(TimeUtil.parseDateString(payFinishTime));
		htPayRecord.setUtime(new Date());		
		htPayRecordService.update(htPayRecord);
		logger.info(htPayRecord.getOrderNo()+"支付记录表修改成功！");
		
		//支付成功 取得保单信息，投保确认
		if("1".equals(status)){

			TransportInsurance transportInsurance=tsinsuranceService.getById(htPayRecord.getTiId());
			if(transportInsurance.getStatus().intValue()==0){
				//支付成功 取得保单信息，投保确认
				PolicyIssueBean policyIssueBean=new PolicyIssueBean();			
				//支付宝/微信，支付流水号
				policyIssueBean.setInsrpakNo(transactionNo);
				//投保单号
				policyIssueBean.setInsurancePolicy(transportInsurance.getInsuranceNo());
				//合计保费
				policyIssueBean.setPaymentPrm(new BigDecimal(transportInsurance.getPremiumCurrency()).movePointLeft(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
				//支付模式 22: 微信，23：支付宝
				policyIssueBean.setPayMode(payMode);
				///请求时间
				policyIssueBean.setRequestTime(transportInsurance.getCtime());
				//流水号
				policyIssueBean.setSerialNumber(orderNo);
				//银行交易时间 
				policyIssueBean.setTransExeTime(TimeUtil.parseDateString(payFinishTime));
				//支付成功 取得保单信息，投保确认
				IssuePolicyResult issuePolicyResult=HtbxUtil.insuranceConfrim(policyIssueBean);
				//投保确认结果
				if(issuePolicyResult!=null){
					//修改 保单表
					transportInsurance.setPdfUrl(issuePolicyResult.getPdfUrl());
					transportInsurance.setNumber(issuePolicyResult.getNumber());
					transportInsurance.setStatus(1);
					transportInsurance.setPayType(Integer.parseInt(payMode));
					transportInsurance.setPolicyCtime(new Date());
					transportInsurance.setPayTime(TimeUtil.parseDateString(payFinishTime));
					String now = TimeUtil.formatDate(new Date());
					if (now.equals(TimeUtil.formatDate(transportInsurance.getStartTime()))) {
						transportInsurance.setEffectiveTime(TimeUtil.addDay(TimeUtil.parseDateString(payFinishTime), 30));
					}
					transportInsurance.setUtime(new Date());
					tsinsuranceService.update(transportInsurance);
					logger.info(transportInsurance.getOrderNo()+"保险主表修改成功！");
					String fileName=DownFileUtil.getSaveFilePathName("htbx",issuePolicyResult.getNumber()+".pdf");
					String filepath = AppConfig.getProperty("picture.path.domain")+fileName;
					logger.info("htbx_pdf_save_path_name="+fileName);
					DownFileUtil.download(issuePolicyResult.getPdfUrl(), filepath);
					transportInsurance.setImgUrl(fileName);
					tsinsuranceService.update(transportInsurance);
					
					//插入 保险流水表
					TransportInsuranceSub  tisub =new TransportInsuranceSub();
					tisub.setApplicantName(transportInsurance.getApplicantName());
					tisub.setApplicantPhone(transportInsurance.getApplicantPhone());
					tisub.setCellPhone(transportInsurance.getCellPhone());
					tisub.setCompany(transportInsurance.getCompany());
					tisub.setCtime(new Date());
					tisub.setCurrency(transportInsurance.getPremiumCurrency());
					tisub.setInsuredName(transportInsurance.getInsuredName());
					tisub.setInsuredPhone(transportInsurance.getInsuredPhone());
					tisub.setNumber(transportInsurance.getNumber());
					tisub.setPayType(transportInsurance.getPayType());
					tisub.setPid(transportInsurance.getId());
					tisub.setRetreatType(1);
					tisub.setType(transportInsurance.getType());
					tisub.setUserId(transportInsurance.getUserId());
					tisub.setUtime(new Date());
					tsinsuranceSubService.add(tisub);
					logger.info(transportInsurance.getOrderNo()+"保险流水表保存成功！");
				}else{
					logger.error(transportInsurance.getOrderNo()+"投保确认失败,结果为null");
				}
			}else{
				logger.info(transportInsurance.getOrderNo()+"保单已进行过回调");
				
			}
			
		}else{
			logger.info("htbx_支付失败_request_data_status={},orderNo={},payFinishTime={},channelCode={},transactionNo={},sign={},payMode={}",status,orderNo,payFinishTime,channelCode,
				transactionNo,sign,payMode);
		}
	}


	@Override
	public void doPICCCallback(String status, String orderNo,
							   String payFinishTime, String transactionNo,
							   String payMode) throws Exception {

		//修改 支付记录表
		HtPayRecord htPayRecord =htPayRecordService.getHtPayRecord(orderNo);
		if("1".equals(status)){
			htPayRecord.setStatus(1);
		}else
			htPayRecord.setStatus(2);
		htPayRecord.setPayTime(TimeUtil.parseDateString(payFinishTime));
		htPayRecord.setUtime(new Date());
		htPayRecordService.update(htPayRecord);
		logger.info(htPayRecord.getOrderNo()+"支付记录表修改成功！");

		//支付成功 取得保单信息，投保确认
		if("1".equals(status)){

			TransportInsurance transportInsurance=tsinsuranceService.getById(htPayRecord.getTiId());
			if(transportInsurance.getStatus().intValue()==0){
				//支付成功 取得保单信息，投保确认
				PolicyIssueBean policyIssueBean=new PolicyIssueBean();
				//支付宝/微信，支付流水号
				policyIssueBean.setInsrpakNo(transactionNo);
				//投保单号
				policyIssueBean.setInsurancePolicy(transportInsurance.getInsuranceNo());
				//合计保费
				policyIssueBean.setPaymentPrm(new BigDecimal(transportInsurance.getPremiumCurrency()).movePointLeft(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
				//支付模式 22: 微信，23：支付宝
				policyIssueBean.setPayMode(payMode);
				///请求时间
				policyIssueBean.setRequestTime(transportInsurance.getCtime());
				//流水号
				policyIssueBean.setSerialNumber(orderNo);
				//银行交易时间
				policyIssueBean.setTransExeTime(TimeUtil.parseDateString(payFinishTime));
				//支付成功 取得保单信息，投保确认
				IssuePolicyResult issuePolicyResult = new IssuePolicyResult();
//				String ePolicyUrl = piccService.getEPolicyUrl(transactionNo);
				String ePolicyUrl = PICCHttpUtil.composeEPolictyUrl(transactionNo);
				issuePolicyResult.setPdfUrl(ePolicyUrl);
				issuePolicyResult.setNumber(transactionNo);
				//投保确认结果
				if(issuePolicyResult!=null){
					//修改 保单表
					transportInsurance.setPdfUrl(issuePolicyResult.getPdfUrl());
					transportInsurance.setNumber(issuePolicyResult.getNumber());
					transportInsurance.setStatus(1);
					transportInsurance.setPayType(Integer.parseInt(payMode));
					transportInsurance.setPolicyCtime(new Date());
					transportInsurance.setPayTime(TimeUtil.parseDateString(payFinishTime));
					String now = TimeUtil.formatDate(new Date());
					if (now.equals(TimeUtil.formatDate(transportInsurance.getStartTime()))) {
						transportInsurance.setEffectiveTime(TimeUtil.addDay(TimeUtil.parseDateString(payFinishTime), 30));
					}
					transportInsurance.setUtime(new Date());
					tsinsuranceService.update(transportInsurance);
					logger.info(transportInsurance.getOrderNo()+"保险主表修改成功！");
					String fileName=DownFileUtil.getSaveFilePathName("htbx",issuePolicyResult.getNumber()+".pdf");
					String filepath = AppConfig.getProperty("picture.path.domain")+fileName;
					logger.info("htbx_pdf_save_path_name="+fileName);
					DownFileUtil.download(issuePolicyResult.getPdfUrl(), filepath);
					transportInsurance.setImgUrl(fileName);
					tsinsuranceService.update(transportInsurance);

					//插入 保险流水表
					TransportInsuranceSub  tisub =new TransportInsuranceSub();
					tisub.setApplicantName(transportInsurance.getApplicantName());
					tisub.setApplicantPhone(transportInsurance.getApplicantPhone());
					tisub.setCellPhone(transportInsurance.getCellPhone());
					tisub.setCompany(transportInsurance.getCompany());
					tisub.setCtime(new Date());
					tisub.setCurrency(transportInsurance.getPremiumCurrency());
					tisub.setInsuredName(transportInsurance.getInsuredName());
					tisub.setInsuredPhone(transportInsurance.getInsuredPhone());
					tisub.setNumber(transportInsurance.getNumber());
					tisub.setPayType(transportInsurance.getPayType());
					tisub.setPid(transportInsurance.getId());
					tisub.setRetreatType(1);
					tisub.setType(transportInsurance.getType());
					tisub.setUserId(transportInsurance.getUserId());
					tisub.setUtime(new Date());
					tsinsuranceSubService.add(tisub);
					logger.info(transportInsurance.getOrderNo()+"保险流水表保存成功！");
				}else{
					logger.error(transportInsurance.getOrderNo()+"投保确认失败,结果为null");
				}
			}else{
				logger.info(transportInsurance.getOrderNo()+"保单已进行过回调");

			}

		}else{
			logger.info("htbx_支付失败_request_data_status={},orderNo={},payFinishTime={},transactionNo={},payMode={}",status,orderNo,payFinishTime,
					transactionNo,payMode);
		}
	}

	@Override
	public PayStatusBean getPayStatusBean(Long id){
		PayStatusBean payStatusBean=new PayStatusBean();
		TransportInsurance transportInsurance=tsinsuranceService.getById(id);
		if(null == transportInsurance){
			return null;
		}
		String hql="from HtPayCallBack where orderNo=? order by id desc";
		List <HtPayCallBack> list=this.getBaseDao().find(hql, transportInsurance.getOrderNo());
		if(list!=null &&list.size()>0){
			HtPayCallBack htPayCallBack=list.get(0);
			payStatusBean.setId(id);
			payStatusBean.setOrderNo(transportInsurance.getOrderNo());
			payStatusBean.setStatus(htPayCallBack.getStatus());
			payStatusBean.setPayFinishTime(htPayCallBack.getPayFinishTime());
//			payStatusBean.setPayMode(Integer.parseInt(htPayCallBack.getPayMode()));
		}else{
			payStatusBean.setId(id);
			payStatusBean.setOrderNo(transportInsurance.getOrderNo());
			payStatusBean.setStatus(2);
		}
		return payStatusBean;	
	}
}
