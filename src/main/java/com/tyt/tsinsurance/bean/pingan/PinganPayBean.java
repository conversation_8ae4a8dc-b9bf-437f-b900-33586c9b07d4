package com.tyt.tsinsurance.bean.pingan;

public class PinganPayBean {
    /**
     * 支付订单号：渠道支付订单号,在同一支付来源码下必须唯一，由大小写字母、数字组成,用于防止重复提交或重复缴费
     */
    private String tradeNo;
    /**
     * 订单支付总金额：本次付款金额，currency_no为CNY时，最多2位小数，整数位最多12位
     */
    private String amount;
    /**
     * 货币码：当前仅支持固定值CNY：人民币
     */
    private String currency_no;
    /**
     * 产品名称：用户可见的付款商品的描述,该值将展示在支付页面的”商品名称”栏位
     */
    private String productName;
    /**
     * 客户名称：
     */
    private String customerName;
    /**
     * 商品类别：财产险02, 意健险0A,车险01
     */
    private String productType;
    /**
     * 返回地址：支付页面返回业务系统地址
     */
    private String returnUrl;
    /**
     * 产品编码：产品编码【固定传：MP09110010】
     */
    private String productCode;
    /**
     * 支付结果前端通知地址：支付完成后前端回跳通知地址
     */
    private String frontNotifyUrl;
    /**
     * 投保单号：投保单号，投保唯一标识
     */
    private String applyPolicyNo;
    /**
     * 通知单号：通知单号，财务系统唯一标识
     */
    private String noticeNo;
    /**
     * 合作伙伴用户名：投保的合作伙伴用户名【固定传：P_BJBLD】
     */
    private String partnerName;
    /**
     * 加密串：根据指定的加签规则生成的签名字符串
     */
    private String signMsg;

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getCurrency_no() {
        return currency_no;
    }

    public void setCurrency_no(String currency_no) {
        this.currency_no = currency_no;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getFrontNotifyUrl() {
        return frontNotifyUrl;
    }

    public void setFrontNotifyUrl(String frontNotifyUrl) {
        this.frontNotifyUrl = frontNotifyUrl;
    }

    public String getApplyPolicyNo() {
        return applyPolicyNo;
    }

    public void setApplyPolicyNo(String applyPolicyNo) {
        this.applyPolicyNo = applyPolicyNo;
    }

    public String getNoticeNo() {
        return noticeNo;
    }

    public void setNoticeNo(String noticeNo) {
        this.noticeNo = noticeNo;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getSignMsg() {
        return signMsg;
    }

    public void setSignMsg(String signMsg) {
        this.signMsg = signMsg;
    }

    @Override
    public String toString() {
        return "PinganPayBean{" +
                "tradeNo='" + tradeNo + '\'' +
                ", amount=" + amount +
                ", currency_no='" + currency_no + '\'' +
                ", productName='" + productName + '\'' +
                ", customerName='" + customerName + '\'' +
                ", productType='" + productType + '\'' +
                ", returnUrl='" + returnUrl + '\'' +
                ", productCode='" + productCode + '\'' +
                ", frontNotifyUrl='" + frontNotifyUrl + '\'' +
                ", applyPolicyNo='" + applyPolicyNo + '\'' +
                ", noticeNo='" + noticeNo + '\'' +
                ", partnerName='" + partnerName + '\'' +
                ", signMsg='" + signMsg + '\'' +
                '}';
    }
}
