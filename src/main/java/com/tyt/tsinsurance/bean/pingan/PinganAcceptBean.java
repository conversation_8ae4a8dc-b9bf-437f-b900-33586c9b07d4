package com.tyt.tsinsurance.bean.pingan;

import java.io.Serializable;

public class PinganAcceptBean implements Serializable {
    private static final long serialVersionUID = -4563833939765369646L;
    private String resultCode;
    private String resultMsg;
    private String tradeNo;
    private String acc_code;
    private String partnerCode;
    private String applyPolicyNo;
    private String policyNo;
    private String noticeNo;
    private String dataSource;
    private String insuranceBeginDate;
    private String insuranceEndDate;
    private String documentUrl;
    private String paymentState;
    private String paymentSum;


    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getAcc_code() {
        return acc_code;
    }

    public void setAcc_code(String acc_code) {
        this.acc_code = acc_code;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getApplyPolicyNo() {
        return applyPolicyNo;
    }

    public void setApplyPolicyNo(String applyPolicyNo) {
        this.applyPolicyNo = applyPolicyNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getNoticeNo() {
        return noticeNo;
    }

    public void setNoticeNo(String noticeNo) {
        this.noticeNo = noticeNo;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getInsuranceBeginDate() {
        return insuranceBeginDate;
    }

    public void setInsuranceBeginDate(String insuranceBeginDate) {
        this.insuranceBeginDate = insuranceBeginDate;
    }

    public String getInsuranceEndDate() {
        return insuranceEndDate;
    }

    public void setInsuranceEndDate(String insuranceEndDate) {
        this.insuranceEndDate = insuranceEndDate;
    }

    public String getDocumentUrl() {
        return documentUrl;
    }

    public void setDocumentUrl(String documentUrl) {
        this.documentUrl = documentUrl;
    }

    public String getPaymentState() {
        return paymentState;
    }

    public void setPaymentState(String paymentState) {
        this.paymentState = paymentState;
    }

    public String getPaymentSum() {
        return paymentSum;
    }

    public void setPaymentSum(String paymentSum) {
        this.paymentSum = paymentSum;
    }

    @Override
    public String toString() {
        return "PinganAcceptBean{" +
                "resultCode='" + resultCode + '\'' +
                ", resultMsg='" + resultMsg + '\'' +
                ", tradeNo='" + tradeNo + '\'' +
                ", acc_code='" + acc_code + '\'' +
                ", partnerCode='" + partnerCode + '\'' +
                ", applyPolicyNo='" + applyPolicyNo + '\'' +
                ", policyNo='" + policyNo + '\'' +
                ", noticeNo='" + noticeNo + '\'' +
                ", dataSource='" + dataSource + '\'' +
                ", insuranceBeginDate='" + insuranceBeginDate + '\'' +
                ", insuranceEndDate='" + insuranceEndDate + '\'' +
                ", documentUrl='" + documentUrl + '\'' +
                ", paymentState='" + paymentState + '\'' +
                ", paymentSum='" + paymentSum + '\'' +
                '}';
    }
}
