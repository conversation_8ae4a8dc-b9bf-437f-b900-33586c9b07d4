package com.tyt.tsinsurance.bean;

import java.io.Serializable;
import java.util.Date;

/**
 * 导入运单bean
 */
public class ImportWaybillBean implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 6775799585840668467L;

    /**
     * 货源唯一ID
     */
    private Long tsId;
    /**
     * 货源运单号
     */
    private String tsOrderNo;
    /**
     * 出发地-省市区
     */
    private String startPoint;
    /**
     * 出发地-省
     */
    private String startProvinc;
    /**
     * 出发地-市
     */
    private String startCity;
    /**
     * 出发地-区
     */
    private String startArea;
    /**
     * 目的地-省市区
     */
    private String destPoint;
    /**
     * 目的地-省
     */
    private String destProvinc;
    /**
     * 目的地-市
     */
    private String destCity;
    /**
     * 目的地-区
     */
    private String destArea;
    /**
     * 货物内容
     */
    private String taskContent;
    /**
     * 重量
     */
    private String weight;
    /**
     * 车牌
     */
    private String headNo;
    /**
     * 挂牌
     */
    private String tailNo;
    /**
     * 创建时间
     */
    private Date ctime;

    public Long getTsId() {
        return tsId;
    }

    public void setTsId(Long tsId) {
        this.tsId = tsId;
    }

    public String getTsOrderNo() {
        return tsOrderNo;
    }

    public void setTsOrderNo(String tsOrderNo) {
        this.tsOrderNo = tsOrderNo;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getStartProvinc() {
        return startProvinc;
    }

    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }

    public String getStartCity() {
        return startCity;
    }

    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }

    public String getStartArea() {
        return startArea;
    }

    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }

    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    public String getDestProvinc() {
        return destProvinc;
    }

    public void setDestProvinc(String destProvinc) {
        this.destProvinc = destProvinc;
    }

    public String getDestCity() {
        return destCity;
    }

    public void setDestCity(String destCity) {
        this.destCity = destCity;
    }

    public String getDestArea() {
        return destArea;
    }

    public void setDestArea(String destArea) {
        this.destArea = destArea;
    }

    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getHeadNo() {
        return headNo;
    }

    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }

    public String getTailNo() {
        return tailNo;
    }

    public void setTailNo(String tailNo) {
        this.tailNo = tailNo;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "ImportWaybillBean{" +
                "tsId=" + tsId +
                ", tsOrderNo='" + tsOrderNo + '\'' +
                ", startPoint='" + startPoint + '\'' +
                ", startProvinc='" + startProvinc + '\'' +
                ", startCity='" + startCity + '\'' +
                ", startArea='" + startArea + '\'' +
                ", destPoint='" + destPoint + '\'' +
                ", destProvinc='" + destProvinc + '\'' +
                ", destCity='" + destCity + '\'' +
                ", destArea='" + destArea + '\'' +
                ", taskContent='" + taskContent + '\'' +
                ", weight='" + weight + '\'' +
                ", headNo='" + headNo + '\'' +
                ", tailNo='" + tailNo + '\'' +
                ", ctime=" + ctime +
                '}';
    }
}
