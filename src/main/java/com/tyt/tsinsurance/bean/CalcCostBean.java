package com.tyt.tsinsurance.bean;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计算保费
 * Created by duanwc on 18/1/29.
 */
public class CalcCostBean implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 6875799585840668467L;

    /**
     * 保额
     */
    private BigDecimal amtCurrency;

    /**
     * 保费距离范围
     */
    private String distanceRange;

    /**
     * 保费系数
     */
    private BigDecimal premium;

    /**
     * 保费金额-折扣后金额
     */
    private BigDecimal premiumCurrency;

    /**
     * 保费原价格
     */
    private BigDecimal originalPrice;

    /**
     * 距离
     */
    private BigDecimal distance;

    public BigDecimal getAmtCurrency() {
        return amtCurrency;
    }

    public void setAmtCurrency(BigDecimal amtCurrency) {
        this.amtCurrency = amtCurrency;
    }

    public String getDistanceRange() {
        return distanceRange;
    }

    public void setDistanceRange(String distanceRange) {
        this.distanceRange = distanceRange;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getPremiumCurrency() {
        return premiumCurrency;
    }

    public void setPremiumCurrency(BigDecimal premiumCurrency) {
        this.premiumCurrency = premiumCurrency;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getDistance() {
        return distance;
    }

    public void setDistance(BigDecimal distance) {
        this.distance = distance;
    }

    @Override
    public String toString() {
        return "CalcCostBean{" +
                "amtCurrency=" + amtCurrency +
                ", distanceRange='" + distanceRange + '\'' +
                ", premium=" + premium +
                ", premiumCurrency=" + premiumCurrency +
                ", originalPrice=" + originalPrice +
                ", distance=" + distance +
                '}';
    }
}
