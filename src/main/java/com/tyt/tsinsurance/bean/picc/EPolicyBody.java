package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EPolicyBody {
    private InsResults insResults;

    public EPolicyBody() {
    }

    public EPolicyBody(InsResults insResults) {
        this.insResults = insResults;
    }

    public static EPolicyBodyBuilder builder(){
        return new EPolicyBodyBuilder();
    }

    public static class EPolicyBodyBuilder {
        private InsResults insResults;

        public EPolicyBodyBuilder setInsResults(InsResults insResults) {
            this.insResults = insResults;
            return this;
        }
        public EPolicyBody build(){
            return new EPolicyBody(this.insResults);
        }
    }

    public InsResults getInsResults() {
        return insResults;
    }

    public void setInsResults(InsResults insResults) {
        this.insResults = insResults;
    }
}
