package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SubmitOrderReplyWrapper {
    private Head head;
    private SubmitOrderReplyBody body;

    public Head getHead() {
        return head;
    }

    public void setHead(Head head) {
        this.head = head;
    }

    public SubmitOrderReplyBody getBody() {
        return body;
    }

    public void setBody(SubmitOrderReplyBody body) {
        this.body = body;
    }
}
