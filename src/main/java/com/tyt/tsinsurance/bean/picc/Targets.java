package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 *      	"packQty": "1台",
 *  *             	"goodsName":"挖机",
 *  * 	            "goodsTypeNo":"630602",
 *  *             	"transportType": "公路",
 *  *             	"transport": "汽车",
 *  *             	"fromLoc": "北京",
 *  *             	"toLoc": "天津",
 *  *             	"departureDate": "2018-11-24:14",
 *  *             	"ratio": "30"
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Targets {
    private String invRefNo; //	N	标记\运单号
    private String recognizeeOrg; //	N	被保险人机构代码
    private String packQty;
    private String goodsName;
    private String weight; //	N	重量 例如100吨
    private String goodsTypeNo = "632100"; //632100 电器、机械、运输工具、设备类〉其他机械、仪器、仪表及其零件
    private String transportType = "公路";
    private String transport = "汽车";
    private String transportNo; //	N	航班次
    private String fromLoc;
    private String viaLoc; //	N	中转地
    private String toLoc; //	Y	目的地
    private String departureDate; // yyyy-MM-dd:HH
    private String ratio = "0.3";
    private String currencyID; //	N	投保币种 默认人民币
    private String endCurrencyID; //	N	结算币种 默认人民币
    private String hasAdditive; //	N	是否投保附加险 0-不投 1-投 默认不投







    public Targets(String invRefNo,
                   String packQty,
                   String goodsName,
                   String fromLoc,
                   String toLoc,
                   String departureDate,
                   String goodsTypeNo,
                   String viaLoc,
                   String weight,
                   String ratio,
                   String recognizeeOrg,
                   String transport) {
        this.invRefNo = invRefNo;
        this.packQty = packQty;
        this.goodsName = goodsName;
        this.fromLoc = fromLoc;
        this.toLoc = toLoc;
        this.departureDate = departureDate;
        this.goodsTypeNo = goodsTypeNo;
        this.viaLoc = viaLoc;
        this.weight = weight;
        this.ratio = ratio;
        this.recognizeeOrg = recognizeeOrg;
        this.transport = transport;
    }

    public static TargetsBuilder builder(){
        return new TargetsBuilder();
    }

    public static class TargetsBuilder {
        private String invRefNo;
        private String packQty;
        private String goodsName;
        private String goodsTypeNo = "632100"; //632100 电器、机械、运输工具、设备类〉其他机械、仪器、仪表及其零件
        private String transport;
        private String fromLoc;
        private String toLoc;
        private String departureDate; // yyyy-MM-dd:HH
        private String viaLoc;
        private String weight;
        private String ratio;
        private String recognizeeOrg; //	N	被保险人机构代码



        public TargetsBuilder setInvRefNo(String invRefNo) {
            this.invRefNo = invRefNo;
            return this;
        }

        public TargetsBuilder setPackQty(String packQty) {
            this.packQty = packQty;
            return this;
        }

        public TargetsBuilder setGoodsName(String goodsName) {
            this.goodsName = goodsName;
            return this;
        }

        public TargetsBuilder setTransport(String transport) {
            this.transport = transport;
            return this;
        }

        public TargetsBuilder setFromLoc(String fromLoc) {
            this.fromLoc = fromLoc;
            return this;
        }

        public TargetsBuilder setToLoc(String toLoc) {
            this.toLoc = toLoc;
            return this;
        }

        public TargetsBuilder setDepartureDate(String departureDate) {
            this.departureDate = departureDate;
            return this;
        }

        public TargetsBuilder setGoodsTypeNo(String goodsTypeNo){
            this.goodsTypeNo = goodsTypeNo;
            return this;
        }

        public TargetsBuilder setViaLoc(String viaLoc) {
            this.viaLoc = viaLoc;
            return this;
        }

        public TargetsBuilder setWeight(String weight) {
            this.weight = weight;
            return this;
        }

        public TargetsBuilder setRatio(String ratio) {
            this.ratio = ratio;
            return this;
        }

        public TargetsBuilder setRecognizeeOrg(String recognizeeOrg) {
            this.recognizeeOrg = recognizeeOrg;
            return this;
        }

        public Targets build(){
            return new Targets(this.invRefNo, this.packQty, this.goodsName, this.fromLoc, this.toLoc, this.departureDate, this.goodsTypeNo, this.viaLoc, this.weight, this.ratio, this.recognizeeOrg, this.transport);
        }
    }

    public String getPackQty() {
        return packQty;
    }

    public void setPackQty(String packQty) {
        this.packQty = packQty;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsTypeNo() {
        return goodsTypeNo;
    }

    public void setGoodsTypeNo(String goodsTypeNo) {
        this.goodsTypeNo = goodsTypeNo;
    }

    public String getTransportType() {
        return transportType;
    }

    public void setTransportType(String transportType) {
        this.transportType = transportType;
    }

    public String getTransport() {
        return transport;
    }

    public void setTransport(String transport) {
        this.transport = transport;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getToLoc() {
        return toLoc;
    }

    public void setToLoc(String toLoc) {
        this.toLoc = toLoc;
    }

    public String getDepartureDate() {
        return departureDate;
    }

    public void setDepartureDate(String departureDate) {
        this.departureDate = departureDate;
    }

    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }


    public String getInvRefNo() {
        return invRefNo;
    }

    public void setInvRefNo(String invRefNo) {
        this.invRefNo = invRefNo;
    }

    public String getRecognizeeOrg() {
        return recognizeeOrg;
    }

    public void setRecognizeeOrg(String recognizeeOrg) {
        this.recognizeeOrg = recognizeeOrg;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getTransportNo() {
        return transportNo;
    }

    public void setTransportNo(String transportNo) {
        this.transportNo = transportNo;
    }

    public String getViaLoc() {
        return viaLoc;
    }

    public void setViaLoc(String viaLoc) {
        this.viaLoc = viaLoc;
    }

    public String getCurrencyID() {
        return currencyID;
    }

    public void setCurrencyID(String currencyID) {
        this.currencyID = currencyID;
    }

    public String getEndCurrencyID() {
        return endCurrencyID;
    }

    public void setEndCurrencyID(String endCurrencyID) {
        this.endCurrencyID = endCurrencyID;
    }

    public String getHasAdditive() {
        return hasAdditive;
    }

    public void setHasAdditive(String hasAdditive) {
        this.hasAdditive = hasAdditive;
    }
}
