package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentQueryResponseWrapper {
    private EPolicyHead head;
    private PolicyInfoBody body;

    public EPolicyHead getHead() {
        return head;
    }

    public void setHead(EPolicyHead head) {
        this.head = head;
    }

    public PolicyInfoBody getBody() {
        return body;
    }

    public void setBody(PolicyInfoBody body) {
        this.body = body;
    }
}
