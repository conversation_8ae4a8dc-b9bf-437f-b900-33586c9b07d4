package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CallbackPolicyInfo {
    /**
     *   "policyInfo": [
     *          *       {
     *          *         "orderNo": "1543309664",
     *          *         "policyNo": "PYDG201835020097E71501",
     *          *         "insurerKey": "1543309664#tyt",
     *          *         "errorCode": "0000",
     *          *         "errorMsg": "成功",
     *          *         "proposalNo": "",
     *          *         "proposalTime": "",
     *          *         "policyTime": "2018-11-27 17:08:56",
     *          *         "productId": "ABX10000084"
     *          *       }
     *          *     ]
     */

    private String orderNo;
    private String policyNo;
    private String insurerKey;
    private String errorCode;
    private String errorMsg;
    private String proposalNo;
    private String proposalTime;
    private String policyTime;
    private String payType; //payType	Y	支付方式 (1微信，2支付宝，3银联)
    private String productId;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getInsurerKey() {
        return insurerKey;
    }

    public void setInsurerKey(String insurerKey) {
        this.insurerKey = insurerKey;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getProposalNo() {
        return proposalNo;
    }

    public void setProposalNo(String proposalNo) {
        this.proposalNo = proposalNo;
    }

    public String getProposalTime() {
        return proposalTime;
    }

    public void setProposalTime(String proposalTime) {
        this.proposalTime = proposalTime;
    }

    public String getPolicyTime() {
        return policyTime;
    }

    public void setPolicyTime(String policyTime) {
        this.policyTime = policyTime;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }
}
