package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 *
 *        {
 * 		"head": {
 * 			"transactionNo": "",
 * 			"operator": "S10000038",
 * 			"timeStamp": "2018-11-23 06:01:12"
 *
 *        },
 * 		"body": {
 * 			"applicant": {
 * 				"cardNo": "110101199102062324",
 *             	"cardType": "01",
 *             	"name": "满十八",
 *             	"birthday": "",
 *             	"sex": "",
 *             	"mobile": ""
 *            },
 * 			"insurants": [
 *                {
 * 					"cardNo": "110101199102062324",
 * 					"cardType": "01",
 * 	                "insurerKey": "1542952872#tyt",
 * 	                "name": "满十八",
 * 	                "relation": "0",
 * 	                "birthday": "",
 * 	                "sex": "",
 * 	                "amount": 10000,
 * 					"premium": 100
 *
 *                }
 * 			],
 * 			"orderInfo": {
 * 				"startTime": "2018-11-24 14:01:12",
 *             	"orderNo": "1542952872",
 *             	"productNo": "ABX10000084",
 *             	"channelId": "CH10000342",
 *             	"sumAmount": 10000,
 * 				"sumPremium": 100
 *
 *            },
 * 			"targets": {
 *             	"packQty": "1台",
 *             	"goodsName":"挖机",
 * 	            "goodsTypeNo":"630602",
 *             	"transportType": "公路",
 *             	"transport": "汽车",
 *             	"fromLoc": "北京",
 *             	"toLoc": "天津",
 *             	"departureDate": "2018-11-24:14",
 *             	"ratio": "30"
 *            },
 * 			"extendInfo": {
 *  				"callbackURL": "http://www.teyuntong.com"
 *            }
 *        }
 *
 *    }
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SubmitOrderWrapper {
    private Head head;
    private Body body;

    public SubmitOrderWrapper(Head head, Body body) {
        this.head = head;
        this.body = body;
    }

    public Head getHead() {
        return head;
    }

    public void setHead(Head head) {
        this.head = head;
    }

    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }

    public  static SubmitOrderWrapperBuilder builder(){
        return new SubmitOrderWrapperBuilder();
    }

    public static class SubmitOrderWrapperBuilder{
        private Head head;
        private Body body;

        public SubmitOrderWrapperBuilder setHead(Head head){
            this.head = head;
            return this;
        }

        public SubmitOrderWrapperBuilder setBody(Body body){
            this.body = body;
            return this;
        }

        public SubmitOrderWrapper build(){
            return new SubmitOrderWrapper(this.head, this.body);
        }

    }
}
