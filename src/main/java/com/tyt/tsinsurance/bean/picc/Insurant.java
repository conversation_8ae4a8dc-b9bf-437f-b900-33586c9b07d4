package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 	"cardNo": "110101199102062324",
 *  * 					"cardType": "01",
 *  * 	                "insurerKey": "1542952872#tyt",
 *  * 	                "name": "满十八",
 *  * 	                "relation": "0",
 *  * 	                "birthday": "",
 *  * 	                "sex": "",
 *  * 	                "amount": "10000",
 *  * 					"premium": "100"
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Insurant {
    private String cardNo;
    private String cardType = "01"; //01身份证
    private String insurerKey;
    private String name;
    private String relation = PICCContants.RELATION; //9其他
    private String birthday = "";
    private String sex = "";
    private String amount; //保额
    private String premium; //保费
    private String mobile = "";
    private String policyCount = "1";
    private String phone = "";
    private String email = "";
    private String address = "";
    private String insurerExt = "";



    public Insurant(String cardNo, String cardType, String insurerKey, String name, String amount, String premium, String mobile, String relation) {
        this.cardNo = cardNo;
        this.cardType = cardType;
        this.insurerKey = insurerKey;
        this.name = name;
        this.amount = amount;
        this.premium = premium;
        this.mobile = mobile;
        this.relation = relation;
    }

    public static InsurantBuilder builder(){
        return new InsurantBuilder();
    }

    public static class InsurantBuilder {
        private String cardNo;
        private String cardType;
        private String insurerKey;
        private String name;
        private String amount; //保额
        private String premium; //保费
        private String mobile = "";
        private String relation = PICCContants.RELATION;

        public InsurantBuilder setCardNo(String cardNo) {
            this.cardNo = cardNo;
            return this;
        }

        public InsurantBuilder setCardType(String cardType) {
            this.cardType = cardType;
            return this;
        }

        public InsurantBuilder setInsurerKey(String insurerKey) {
            this.insurerKey = insurerKey;
            return this;
        }

        public InsurantBuilder setName(String name) {
            this.name = name;
            return this;

        }

        public InsurantBuilder setAmount(String amount) {
            this.amount = amount;
            return this;

        }

        public InsurantBuilder setPremium(String premium) {
            this.premium = premium;
            return this;

        }

        public InsurantBuilder setMobile(String mobile){
            this.mobile = mobile;
            return this;
        }

        public InsurantBuilder setRelation(String relation){
            this.relation = relation;
            return this;
        }

        public Insurant build(){
            return new Insurant(this.cardNo, this.cardType, this.insurerKey, this.name, this.amount, this.premium, this.mobile, this.relation);
        }
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getInsurerKey() {
        return insurerKey;
    }

    public void setInsurerKey(String insurerKey) {
        this.insurerKey = insurerKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getPremium() {
        return premium;
    }

    public void setPremium(String premium) {
        this.premium = premium;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPolicyCount() {
        return policyCount;
    }

    public void setPolicyCount(String policyCount) {
        this.policyCount = policyCount;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getInsurerExt() {
        return insurerExt;
    }

    public void setInsurerExt(String insurerExt) {
        this.insurerExt = insurerExt;
    }
}
