package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PolicyInfo {
    /**
     *                 "policyNo":"611009904001701180001011",
     *                 "insurerKey":"5df8f62419284eabad5c55e13af7778#3408",
     *                 "status":"2", 订单状态 0无效，1待支付，2已支付
     *                 "errorCode":"0000",
     *                 "errorMsg":"成功"
     */
    private String policyNo;
    private String insurerKey;
    private String status;
    private String errorCode;
    private String errorMsg;

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getInsurerKey() {
        return insurerKey;
    }

    public void setInsurerKey(String insurerKey) {
        this.insurerKey = insurerKey;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
