package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CallbackResponseBean {
    /**
     * {
     *     "head":{
     *         "transactionNo":"",
     *         "operator":"S10000006",
     *         "aiBaoTransactionNo":"",
     *         "timeStamp":"",
     *         "errorCode":"0000",
     *         "errorMsg":"成功"
     *     }
     * }
     */

    private String transactionNo;
    private String operator = "S10000038";
    private String aiBaoTransactionNo;
    private String timeStamp;
    private String errorCode = "0000";
    private String errorMsg = "成功";

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getAiBaoTransactionNo() {
        return aiBaoTransactionNo;
    }

    public void setAiBaoTransactionNo(String aiBaoTransactionNo) {
        this.aiBaoTransactionNo = aiBaoTransactionNo;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
