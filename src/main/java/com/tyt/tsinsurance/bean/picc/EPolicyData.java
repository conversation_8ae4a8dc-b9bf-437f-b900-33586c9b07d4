package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EPolicyData {
    private EPolicyHead head;
    private EPolicyBody body;

    public EPolicyData(){}

    public EPolicyData(EPolicyHead head, EPolicyBody body) {
        this.head = head;
        this.body = body;
    }

    public static EPolicyDataBuilder builder(){
        return new EPolicyDataBuilder();
    }

    public static class EPolicyDataBuilder {
        private EPolicyHead head;
        private EPolicyBody body;

        public EPolicyDataBuilder setHead(EPolicyHead head) {
            this.head = head;
            return this;
        }

        public EPolicyDataBuilder setBody(EPolicyBody body) {
            this.body = body;
            return this;
        }

        public EPolicyData build(){
            return new EPolicyData(head, body);
        }
    }

    public EPolicyHead getHead() {
        return head;
    }

    public void setHead(EPolicyHead head) {
        this.head = head;
    }

    public EPolicyBody getBody() {
        return body;
    }

    public void setBody(EPolicyBody body) {
        this.body = body;
    }
}
