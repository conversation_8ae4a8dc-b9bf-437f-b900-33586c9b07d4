package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentQueryWrapper {
    private EPolicyHead head;
    private PaymentQueryRequestBody body;

    public PaymentQueryWrapper() {
    }

    public PaymentQueryWrapper(EPolicyHead head, PaymentQueryRequestBody body) {
        this.head = head;
        this.body = body;
    }

    public static PaymentQueryWrapperBuilder builder(){
        return new PaymentQueryWrapperBuilder();
    }

    public static class PaymentQueryWrapperBuilder {
        private EPolicyHead head;
        private PaymentQueryRequestBody body;

        public PaymentQueryWrapperBuilder setHead(EPolicyHead head) {
            this.head = head;
            return this;
        }

        public PaymentQueryWrapperBuilder setBody(PaymentQueryRequestBody body) {
            this.body = body;
            return this;
        }

        public PaymentQueryWrapper build(){
            return new PaymentQueryWrapper(this.head, this.body);
        }
    }

    public EPolicyHead getHead() {
        return head;
    }

    public void setHead(EPolicyHead head) {
        this.head = head;
    }

    public PaymentQueryRequestBody getBody() {
        return body;
    }

    public void setBody(PaymentQueryRequestBody body) {
        this.body = body;
    }
}
