package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InsResults {
//    "policyNo":"611009906000501180001007",
//            "policyDownUrl":""
    private String policyNo;
    private String policyDownUrl = "";

    public InsResults() {
    }

    public InsResults(String policyNo, String policyDownUrl) {
        this.policyNo = policyNo;
        this.policyDownUrl = policyDownUrl;
    }

    public static InsResultsBuilder builder(){
        return new InsResultsBuilder();
    }


    public static class InsResultsBuilder {
        private String policyNo;
        private String policyDownUrl;

        public InsResultsBuilder setPolicyNo(String policyNo) {
            this.policyNo = policyNo;
            return this;
        }

        public InsResultsBuilder setPolicyDownUrl(String policyDownUrl) {
            this.policyDownUrl = policyDownUrl;
            return this;
        }

        public InsResults build() {
            return new InsResults(this.policyNo, this.policyDownUrl);
        }
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getPolicyDownUrl() {
        return policyDownUrl;
    }

    public void setPolicyDownUrl(String policyDownUrl) {
        this.policyDownUrl = policyDownUrl;
    }
}
