package com.tyt.tsinsurance.bean.picc;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 		"cardNo": "110101199102062324",
 *  *             	"cardType": "01",
 *  *             	"name": "满十八",
 *  *             	"birthday": "",
 *  *             	"sex": "",
 *  *             	"mobile": ""
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Applicant {
    private String applicantExt = "";
    private String cardNo;
    private String cardType = "01"; //01 身份证 37 统一社会信用代码
    private String name;
    private String birthday = "";
    private String sex = "";
    private String mobile = "";
    private String phone = "";
    private String email = "";
    private String address = "";

    public Applicant(String cardNo, String cardType, String name, String mobile) {
        this.cardNo = cardNo;
        this.cardType = cardType;
        this.name = name;
        this.mobile = mobile;
    }

    public static ApplicantBuilder builder(){
        return new ApplicantBuilder();
    }
    public static class ApplicantBuilder {
        private String cardNo;
        private String cardType;
        private String name;
        private String mobile = "";

        public ApplicantBuilder setCardNo(String cardNo) {
            this.cardNo = cardNo;
            return this;
        }

        public ApplicantBuilder setCardType(String cardType) {
            this.cardType = cardType;
            return this;
        }

        public ApplicantBuilder setName(String name) {
            this.name = name;
            return this;
        }

        public ApplicantBuilder setMobile(String mobile) {
            this.mobile = mobile;
            return this;
        }

        public Applicant build(){
            return new Applicant(this.cardNo, this.cardType, this.name, this.mobile);
        }
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Applicant(String applicantExt) {
        this.applicantExt = applicantExt;
    }

    public String getApplicantExt() {
        return applicantExt;
    }

    public void setApplicantExt(String applicantExt) {
        this.applicantExt = applicantExt;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
