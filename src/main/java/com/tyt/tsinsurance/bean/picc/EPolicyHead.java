package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

//    	"transactionNo": "",
//                * 			"operator": "",
//                * 			"timeStamp": "2018-11-23 06:01:12"
//             * 			"timeStamp": "2018-11-23 06:01:12"
@JsonIgnoreProperties(ignoreUnknown = true)
public class EPolicyHead {
    private String transactionNo = ""; //交易流水号（订单号）
    private String operator = "S10000038";
    private String aiBaoTransactionNo;
    private String timeStamp; // yyyy-MM-dd HH:mm:ss
    private String errorCode = "0000";
    private String errorMsg = "";

    public EPolicyHead() {

    }

    public EPolicyHead(String transactionNo, String timeStamp) {
        this.transactionNo = transactionNo;
        this.timeStamp = timeStamp;
    }

    public static HeadBuilder builder(){
        return new HeadBuilder();
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getAiBaoTransactionNo() {
        return aiBaoTransactionNo;
    }

    public void setAiBaoTransactionNo(String aiBaoTransactionNo) {
        this.aiBaoTransactionNo = aiBaoTransactionNo;
    }

    public static class HeadBuilder {
        private String transactionNo;
        private String timeStamp;

        public HeadBuilder setTransactionNo(String transactionNo){
            this.transactionNo = transactionNo;
            return this;
        }
        public HeadBuilder setTimeStamp(String timeStamp){
            this.timeStamp = timeStamp;
            return this;
        }

        public EPolicyHead build(){
            return new EPolicyHead(this.transactionNo, this.timeStamp);
        }
    }
}
