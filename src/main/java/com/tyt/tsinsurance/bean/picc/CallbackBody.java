package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CallbackBody {
    private List<CallbackPolicyInfo> policyInfo;

    public List<CallbackPolicyInfo> getPolicyInfo() {
        return policyInfo;
    }

    public void setPolicyInfo(List<CallbackPolicyInfo> policyInfo) {
        this.policyInfo = policyInfo;
    }
}
