package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CallbackWrapper {
    private CallbackHead head;
    private CallbackBody body;

    public CallbackHead getHead() {
        return head;
    }

    public void setHead(CallbackHead head) {
        this.head = head;
    }

    public CallbackBody getBody() {
        return body;
    }

    public void setBody(CallbackBody body) {
        this.body = body;
    }
}
