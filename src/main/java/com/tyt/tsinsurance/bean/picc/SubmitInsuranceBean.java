package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SubmitInsuranceBean {
    private String insuranceNo; //保单ID
    private String applicantCardNo; //投保人身份证号码
    private String applicantCardType; //投保人证件类型 01 身份证 37 统一社会信用代码
    private String applicantName; //投保人名称
    private String applicantMobile = ""; //投保人手机号码
    private String insurantCardNo; //被保人身份证号码
    private String insurantCardType; //被保人证件类型 01 身份证 37 统一社会信用代码
    private String insurantName; //被保人名称
    private String insurantMobile = ""; //被保人手机号码
    private String relation = "0";//投保人与被保人关系
    private String recognizeeOrg = ""; //	N	被保险人机构代码
    private String amount; //保额
    private String premium; //保费
    private String ratio; //费率
    private String fromLoc; //起运地
    private String toLoc; //目的地
    private String viaLoc; //中转地
    private String goodsName; //货物名称
    private String weight; //重量
    private String goodsTypeNo = "630602"; //货物类型代码 630602 电器、机械、运输工具、设备类〉起重、运送、装卸机械设备
    private String packQty; //包装
    private String startTime; //起保时间 //yyyy-MM-dd HH:mm:ss
    private String departureDate; //起运日期yyyy-MM-dd:HH
    private String transport;  //运输工具



    public String getInsuranceNo() {
        return insuranceNo;
    }

    public void setInsuranceNo(String insuranceNo) {
        this.insuranceNo = insuranceNo;
    }

    public String getApplicantCardNo() {
        return applicantCardNo;
    }

    public void setApplicantCardNo(String applicantCardNo) {
        this.applicantCardNo = applicantCardNo;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getApplicantMobile() {
        return applicantMobile;
    }

    public void setApplicantMobile(String applicantMobile) {
        this.applicantMobile = applicantMobile;
    }

    public String getInsurantCardNo() {
        return insurantCardNo;
    }

    public void setInsurantCardNo(String insurantCardNo) {
        this.insurantCardNo = insurantCardNo;
    }

    public String getInsurantName() {
        return insurantName;
    }

    public void setInsurantName(String insurantName) {
        this.insurantName = insurantName;
    }

    public String getInsurantMobile() {
        return insurantMobile;
    }

    public void setInsurantMobile(String insurantMobile) {
        this.insurantMobile = insurantMobile;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getPremium() {
        return premium;
    }

    public void setPremium(String premium) {
        this.premium = premium;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getToLoc() {
        return toLoc;
    }

    public void setToLoc(String toLoc) {
        this.toLoc = toLoc;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsTypeNo() {
        return goodsTypeNo;
    }

    public void setGoodsTypeNo(String goodsTypeNo) {
        this.goodsTypeNo = goodsTypeNo;
    }

    public String getPackQty() {
        return packQty;
    }

    public void setPackQty(String packQty) {
        this.packQty = packQty;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getDepartureDate() {
        return departureDate;
    }

    public void setDepartureDate(String departureDate) {
        this.departureDate = departureDate;
    }

    public String getApplicantCardType() {
        return applicantCardType;
    }

    public void setApplicantCardType(String applicantCardType) {
        this.applicantCardType = applicantCardType;
    }

    public String getInsurantCardType() {
        return insurantCardType;
    }

    public void setInsurantCardType(String insurantCardType) {
        this.insurantCardType = insurantCardType;
    }

    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }

    public String getViaLoc() {
        return viaLoc;
    }

    public void setViaLoc(String viaLoc) {
        this.viaLoc = viaLoc;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getRecognizeeOrg() {
        return recognizeeOrg;
    }

    public void setRecognizeeOrg(String recognizeeOrg) {
        this.recognizeeOrg = recognizeeOrg;
    }

    public String getTransport() {
        return transport;
    }

    public void setTransport(String transport) {
        this.transport = transport;
    }
}
