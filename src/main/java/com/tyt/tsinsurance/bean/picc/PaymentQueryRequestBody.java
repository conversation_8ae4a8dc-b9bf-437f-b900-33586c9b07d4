package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentQueryRequestBody {
    /**
     * "payNo":"ABXUP20180524163517266",
     *         "localOrderNo":"",
     *         "extOrderNo":""
     */

    private String payNo = "";
    private String localOrderNo;
    private String extOrderNo = "";

    public PaymentQueryRequestBody(String localOrderNo) {
        this.localOrderNo = localOrderNo;
    }

    public static PaymentQueryRequestBodyBuilder builder() {
        return new PaymentQueryRequestBodyBuilder();
    }

    public static class PaymentQueryRequestBodyBuilder {
        private String localOrderNo;

        public PaymentQueryRequestBodyBuilder setLocalOrderNo(String localOrderNo) {
            this.localOrderNo = localOrderNo;
            return this;
        }

        public PaymentQueryRequestBody build(){
            return new PaymentQueryRequestBody(this.localOrderNo);
        }
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public String getLocalOrderNo() {
        return localOrderNo;
    }

    public void setLocalOrderNo(String localOrderNo) {
        this.localOrderNo = localOrderNo;
    }

    public String getExtOrderNo() {
        return extOrderNo;
    }

    public void setExtOrderNo(String extOrderNo) {
        this.extOrderNo = extOrderNo;
    }
}
