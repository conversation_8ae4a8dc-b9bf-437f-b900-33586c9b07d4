package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtendInfo {
    private String callbackURL;


    public ExtendInfo(String callbackURL) {
        this.callbackURL = callbackURL;
    }


    public static ExtendInfoBuilder builder(){
        return new ExtendInfoBuilder();
    }
    public String getCallbackURL() {
        return callbackURL;
    }

    public void setCallbackURL(String callbackURL) {
        this.callbackURL = callbackURL;
    }

    public static class ExtendInfoBuilder {
        private String callbackURL;

        public ExtendInfoBuilder setCallbackURL(String callbackURL) {
            this.callbackURL = callbackURL;
            return this;
        }

        public ExtendInfo build(){
            return new ExtendInfo(this.callbackURL);
        }


    }
}
