package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Body {
    private Applicant applicant;
    private List<Insurant> insurants = new ArrayList<>();
    private OrderInfo orderInfo;
    private Targets targets;
    private ExtendInfo extendInfo;

    public Body(Applicant applicant, Insurant insurant, OrderInfo orderInfo, Targets targets, ExtendInfo extendInfo) {
        this.applicant = applicant;
        this.insurants.add(insurant);
        this.orderInfo = orderInfo;
        this.targets = targets;
        this.extendInfo = extendInfo;
    }

    public static BodyBuilder builder(){
        return new BodyBuilder();
    }

    public Applicant getApplicant() {
        return applicant;
    }

    public void setApplicant(Applicant applicant) {
        this.applicant = applicant;
    }

    public List<Insurant> getInsurants() {
        return insurants;
    }

    public void setInsurants(List<Insurant> insurants) {
        this.insurants = insurants;
    }

    public OrderInfo getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(OrderInfo orderInfo) {
        this.orderInfo = orderInfo;
    }

    public Targets getTargets() {
        return targets;
    }

    public void setTargets(Targets targets) {
        this.targets = targets;
    }

    public ExtendInfo getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(ExtendInfo extendInfo) {
        this.extendInfo = extendInfo;
    }

    public static class BodyBuilder {
        private Applicant applicant;
        private Insurant insurant;
        private OrderInfo orderInfo;
        private Targets targets;
        private ExtendInfo extendInfo;

        public BodyBuilder setApplicant(Applicant applicant){
            this.applicant = applicant;
            return this;
        }
        public BodyBuilder setInsurant(Insurant insurant){
            this.insurant = insurant;
            return this;
        }
        public BodyBuilder setOrderInfo(OrderInfo orderInfo){
            this.orderInfo = orderInfo;
            return this;
        }
        public BodyBuilder setTargets(Targets targets){
            this.targets = targets;
            return this;
        }
        public BodyBuilder setExtendInfo(ExtendInfo extendInfo){
            this.extendInfo = extendInfo;
            return this;
        }

        public Body build(){
            return new Body(this.applicant, this.insurant, this.orderInfo, this.targets, this.extendInfo);
        }

    }
}
