package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 		"startTime": "2018-11-24 14:01:12",
 *  *             	"orderNo": "1542952872",
 *  *             	"productNo": "ABX10000084",
 *  *             	"channelId": "CH10000342",
 *  *             	"sumAmount": "10000",
 *  * 				"sumPremium": "100"
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderInfo {
    private String startTime; //yyyy-MM-dd HH:mm:ss
    private String orderNo;
    private String productNo = PICCContants.PRODUCT_ID; //ABX10000084 货运险
    private String channelId = PICCContants.CHANNEL_ID;
    private String sumAmount;
    private String sumPremium;
    private String quantity = "";
    private String endTime = "";
    private String userId = "";


    public OrderInfo(String startTime, String orderNo, String sumAmount, String sumPremium) {
        this.startTime = startTime;
        this.orderNo = orderNo;
        this.sumAmount = sumAmount;
        this.sumPremium = sumPremium;
    }

    public static OrderInfoBuilder builder(){
        return new OrderInfoBuilder();
    }

    public static class OrderInfoBuilder {
        private String startTime; //yyyy-MM-dd HH:mm:ss
        private String orderNo;
        private String sumAmount;
        private String sumPremium;

        public OrderInfoBuilder setStartTime(String startTime) {
            this.startTime = startTime;
            return this;
        }

        public OrderInfoBuilder setOrderNo(String orderNo) {
            this.orderNo = orderNo;
            return this;
        }

        public OrderInfoBuilder setSumAmount(String sumAmount) {
            this.sumAmount = sumAmount;
            return this;

        }

        public OrderInfoBuilder setSumPremium(String sumPremium) {
            this.sumPremium = sumPremium;
            return this;

        }

        public OrderInfo build(){
            return  new OrderInfo(this.startTime, this.orderNo, this.sumAmount, this.sumPremium);
        }
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(String sumAmount) {
        this.sumAmount = sumAmount;
    }

    public String getSumPremium() {
        return sumPremium;
    }

    public void setSumPremium(String sumPremium) {
        this.sumPremium = sumPremium;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
