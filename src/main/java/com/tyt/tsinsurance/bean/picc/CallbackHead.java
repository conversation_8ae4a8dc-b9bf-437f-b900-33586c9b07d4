package com.tyt.tsinsurance.bean.picc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CallbackHead {
    /**
     *   "head": {
     *     "transactionNo": "",
     *     "operator": "S10000026",
     *     "aiBaoTransactionNo": "",
     *     "timeStamp": "",
     *     "errorCode": "0000",
     *     "errorMsg": "成功",
     *     "system ": " S10000026"
     *   }
     */

    private String transactionNo;
    private String operator;
    private String aiBaoTransactionNo;
    private String timeStamp;
    private String errorCode;
    private String errorMsg;
    private String system;

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getAiBaoTransactionNo() {
        return aiBaoTransactionNo;
    }

    public void setAiBaoTransactionNo(String aiBaoTransactionNo) {
        this.aiBaoTransactionNo = aiBaoTransactionNo;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
