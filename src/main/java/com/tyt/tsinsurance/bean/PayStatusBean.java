package com.tyt.tsinsurance.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PayStatusBean {
	private Long id;
	private String orderNo;
	private Integer status;
	private Integer payMode;
	private Date payFinishTime;
	public Long getId() {
		return id;
	}
	public String getOrderNo() {
		return orderNo;
	}
	public Integer getStatus() {
		return status;
	}
	public Integer getPayMode() {
		return payMode;
	}
	public Date getPayFinishTime() {
		return payFinishTime;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public void setPayMode(Integer payMode) {
		this.payMode = payMode;
	}
	public void setPayFinishTime(Date payFinishTime) {
		this.payFinishTime = payFinishTime;
	}
	
}
