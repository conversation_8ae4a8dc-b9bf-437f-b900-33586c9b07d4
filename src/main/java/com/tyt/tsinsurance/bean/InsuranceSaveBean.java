package com.tyt.tsinsurance.bean;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InsuranceSaveBean implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 6775799585840668467L;
	private Long id;
	private Long userId;
    private String cellPhone;
    private String userName;
	/**
	 *
	 */
	private String trueName;
    private String applicantName;
    private String applicantId;
    private String applicantPhone;
    private String insuredName;
    private String insuredId;
    private String insuredPhone;
    private String company;
    private Integer type;
    private String number;
    private String headNo;
    private String tailNo;
    private String taskContent1;
    private String taskContent2;
    private String taskContent3;
    private String taskContent4;
    private String weight;
    private String startTime;
    private String startPoint;
    private String destPoint;
    private String startCity;
    private String startProvinc;
    private String startArea;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private String transferStation1;
    private String transferStation2;
    private String transferStation3;
    private String transferStation4;
    private Long tsId;
    private String distance;
    private String amtCurrency;
    private String premium;
    private String premiumCurrency;
    private Integer category;
    private Integer applicantType;
    private Integer insuredType;

    private String contentNewOld;
    private String packing;

	/***
	 * 出发地省编码
	 */
	private String startPortProvinceCode;
	/**
	 * 出发地市编码
	 */
    private String startPortCityCode;
	/**
	 * 目的地省编码
	 */
	private String terminiProvinceCode;
	/**
	 * 目的地市编码
	 */
    private String terminiCityCode;


	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getApplicantName() {
		return applicantName;
	}
	public void setApplicantName(String applicantName) {
		this.applicantName = applicantName;
	}
	public String getApplicantId() {
		return applicantId;
	}
	public void setApplicantId(String applicantId) {
		this.applicantId = applicantId;
	}
	public String getApplicantPhone() {
		return applicantPhone;
	}
	public void setApplicantPhone(String applicantPhone) {
		this.applicantPhone = applicantPhone;
	}
	public String getInsuredName() {
		return insuredName;
	}
	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}
	public String getInsuredId() {
		return insuredId;
	}
	public void setInsuredId(String insuredId) {
		this.insuredId = insuredId;
	}
	public String getInsuredPhone() {
		return insuredPhone;
	}
	public void setInsuredPhone(String insuredPhone) {
		this.insuredPhone = insuredPhone;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getNumber() {
		return number;
	}
	public void setNumber(String number) {
		this.number = number;
	}
	public String getHeadNo() {
		return headNo;
	}
	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}
	public String getTailNo() {
		return tailNo;
	}
	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}
	public String getTaskContent1() {
		return taskContent1;
	}
	public void setTaskContent1(String taskContent1) {
		this.taskContent1 = taskContent1;
	}
	public String getTaskContent2() {
		return taskContent2;
	}
	public void setTaskContent2(String taskContent2) {
		this.taskContent2 = taskContent2;
	}
	public String getTaskContent3() {
		return taskContent3;
	}
	public void setTaskContent3(String taskContent3) {
		this.taskContent3 = taskContent3;
	}
	public String getTaskContent4() {
		return taskContent4;
	}
	public void setTaskContent4(String taskContent4) {
		this.taskContent4 = taskContent4;
	}
	public String getWeight() {
		return weight;
	}
	public void setWeight(String weight) {
		this.weight = weight;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getStartPoint() {
		return startPoint;
	}
	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}
	public String getDestPoint() {
		return destPoint;
	}
	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}
	public String getStartCity() {
		return startCity;
	}
	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}
	public String getStartProvinc() {
		return startProvinc;
	}
	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}
	public String getStartArea() {
		return startArea;
	}
	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}
	public String getDestProvinc() {
		return destProvinc;
	}
	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}
	public String getDestCity() {
		return destCity;
	}
	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}
	public String getDestArea() {
		return destArea;
	}
	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}
	public String getTransferStation1() {
		return transferStation1;
	}
	public void setTransferStation1(String transferStation1) {
		this.transferStation1 = transferStation1;
	}
	public String getTransferStation2() {
		return transferStation2;
	}
	public void setTransferStation2(String transferStation2) {
		this.transferStation2 = transferStation2;
	}
	public String getTransferStation3() {
		return transferStation3;
	}
	public void setTransferStation3(String transferStation3) {
		this.transferStation3 = transferStation3;
	}
	public String getTransferStation4() {
		return transferStation4;
	}
	public void setTransferStation4(String transferStation4) {
		this.transferStation4 = transferStation4;
	}
	public Long getTsId() {
		return tsId;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	public String getDistance() {
		return distance;
	}
	public void setDistance(String distance) {
		this.distance = distance;
	}
	public String getAmtCurrency() {
		return amtCurrency;
	}
	public void setAmtCurrency(String amtCurrency) {
		this.amtCurrency = amtCurrency;
	}
	public String getPremium() {
		return premium;
	}
	public void setPremium(String premium) {
		this.premium = premium;
	}
	public String getPremiumCurrency() {
		return premiumCurrency;
	}
	public void setPremiumCurrency(String premiumCurrency) {
		this.premiumCurrency = premiumCurrency;
	}
	public Integer getCategory() {
		return category;
	}
	public void setCategory(Integer category) {
		this.category = category;
	}
	public Integer getApplicantType() {
		return applicantType;
	}
	public void setApplicantType(Integer applicantType) {
		this.applicantType = applicantType;
	}
	public Integer getInsuredType() {
		return insuredType;
	}
	public void setInsuredType(Integer insuredType) {
		this.insuredType = insuredType;
	}

	public String getStartPortProvinceCode() {
		return startPortProvinceCode;
	}

	public void setStartPortProvinceCode(String startPortProvinceCode) {
		this.startPortProvinceCode = startPortProvinceCode;
	}

    public String getContentNewOld() {
        return contentNewOld;
    }

    public void setContentNewOld(String contentNewOld) {
        this.contentNewOld = contentNewOld;
    }

    public String getPacking() {
        return packing;
    }

    public void setPacking(String packing) {
        this.packing = packing;
    }

    public String getStartPortCityCode() {
		return startPortCityCode;
	}

	public void setStartPortCityCode(String startPortCityCode) {
		this.startPortCityCode = startPortCityCode;
	}

	public String getTerminiProvinceCode() {
		return terminiProvinceCode;
	}

	public void setTerminiProvinceCode(String terminiProvinceCode) {
		this.terminiProvinceCode = terminiProvinceCode;
	}

	public String getTerminiCityCode() {
		return terminiCityCode;
	}

	public void setTerminiCityCode(String terminiCityCode) {
		this.terminiCityCode = terminiCityCode;
	}

	public String getTrueName() {
		return trueName;
	}

	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}
}
