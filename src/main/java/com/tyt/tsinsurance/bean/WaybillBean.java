package com.tyt.tsinsurance.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 运单bean
 */
public class WaybillBean implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 6775799585840668467L;

    /**
     * 导入运单列表
     */
    private List<ImportWaybillBean> list;
    /**
     * 最大返回条数
     */
    private Integer maximum;

    public List<ImportWaybillBean> getList() {
        return list;
    }

    public void setList(List<ImportWaybillBean> list) {
        this.list = list;
    }

    public Integer getMaximum() {
        return maximum;
    }

    public void setMaximum(Integer maximum) {
        this.maximum = maximum;
    }

    @Override
    public String toString() {
        return "WaybillBean{" +
                "list=" + list +
                ", maximum=" + maximum +
                '}';
    }
}
