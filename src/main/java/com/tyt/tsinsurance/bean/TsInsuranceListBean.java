package com.tyt.tsinsurance.bean;

import com.tyt.model.TransportInsurance;

import java.io.Serializable;
import java.util.List;

public class TsInsuranceListBean implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 6775799585840668467L;

	/**
	 * 保单列表
	 */
	private List<TransportInsurance> insurances;
	/**
	 * 最后的保单ID
	 */
	private Long lastId;
	/**
	 * 待支付气泡数
	 */
	private Integer waitPay;
	/**
	 * 生效中气泡数
	 */
	private Integer effective;

	public List<TransportInsurance> getInsurances() {
		return insurances;
	}

	public void setInsurances(List<TransportInsurance> insurances) {
		this.insurances = insurances;
	}

	public Long getLastId() {
		return lastId;
	}

	public void setLastId(Long lastId) {
		this.lastId = lastId;
	}

	public Integer getWaitPay() {
		return waitPay;
	}

	public void setWaitPay(Integer waitPay) {
		this.waitPay = waitPay;
	}

	public Integer getEffective() {
		return effective;
	}

	public void setEffective(Integer effective) {
		this.effective = effective;
	}

	@Override
	public String toString() {
		return "TsInsuranceListBean{" +
				"insurances=" + insurances +
				", lastId=" + lastId +
				", waitPay=" + waitPay +
				", effective=" + effective +
				'}';
	}
}
