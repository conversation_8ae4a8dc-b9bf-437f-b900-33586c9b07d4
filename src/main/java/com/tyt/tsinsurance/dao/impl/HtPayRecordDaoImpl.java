package com.tyt.tsinsurance.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.HtPayRecord;
import com.tyt.tsinsurance.dao.HtPayRecordDao;

@Repository("htPayRecordDao")
public class HtPayRecordDaoImpl extends BaseDaoImpl<HtPayRecord, Long> implements HtPayRecordDao {

	public HtPayRecordDaoImpl() {
		this.setEntityClass(HtPayRecord.class);
	}

}
