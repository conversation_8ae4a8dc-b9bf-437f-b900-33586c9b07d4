package com.tyt.user.controller;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.tyt.base.controller.BaseController;
import com.tyt.model.Apply;
import com.tyt.model.User;
import com.tyt.user.service.ApplyService;
import com.tyt.user.service.UserService;
import com.tyt.util.RandomUtil;
import com.tyt.util.SendMessage;

/**
 * User: Administrator
 * Date: 14-3-15
 * Time: 下午7:40
 */
@Controller
@RequestMapping("/front")
public class FrontController extends BaseController {
	@Resource(name = "applyService")
	private ApplyService applyService;
	
	@Resource(name = "userService")
	private UserService userService;
	
	@RequestMapping(value = "/checkCellPhone")
    public void checkCellPhone(@RequestParam(value = "cellPhone", defaultValue="") String cellPhone,
    	HttpServletRequest request,
    	HttpServletResponse response
    		){
		PrintWriter out=null;
    	Map<String,String> map=new HashMap<String,String>();
    	try{
    		request.setCharacterEncoding("utf-8");  //这里不设置编码会有乱码
            response.setContentType("text/html;charset=utf-8");
            response.setHeader("Cache-Control", "no-cache"); 
    		out=response.getWriter();
        	List<Apply> applyList = applyService.getList(" entity.cellPhone='"+cellPhone+"'", null);
        	if(applyList!= null && applyList.size()>0) {
    			map.put("msg","此号码已有申请记录，请重新填写！");
        		out.print(JSON.toJSON(map));
        		applyList=null;
        		return;
        	}else{
        		List<User> users=userService.getList(" entity.cellPhone='"+cellPhone+"'", null);
        		if(users!=null&&users.size()>0){
        			map.put("msg","此号码已经注册，请重新填写！");
            		out.print(JSON.toJSON(map));
            		users=null;
            		return;
        		}else{
        			map.put("msg","");
            		out.print(JSON.toJSON(map));
            		return;
        		}
        	}
    	}catch(Exception e){
    		logger.info("apply Exception."+e.toString());
    		map.put("msg","失败，请稍后重试！");
    		out.print(JSON.toJSON(map));
    	}finally{
    		if(out!=null)out.close();
    		map=null;
    	}
    }
	
    @RequestMapping(value = "/apply")
    public void apply(@RequestParam(value = "cellPhone",defaultValue="") String cellPhone,
            @RequestParam(value = "trueName", defaultValue= "") String trueName,
            @RequestParam(value = "qq", defaultValue= "") String qq,
    		HttpServletRequest request,HttpServletResponse response) {
    	 
    	PrintWriter out=null;
    	Map<String,String> map=new HashMap<String,String>();
    	
    	try{
    		request.setCharacterEncoding("utf-8");  //这里不设置编码会有乱码
            response.setContentType("text/html;charset=utf-8");
            response.setHeader("Cache-Control", "no-cache"); 
    		out=response.getWriter();
    		if(cellPhone==null||cellPhone.trim().equals("")||
    		   trueName==null||trueName.trim().equals("")||
    		   qq==null||qq.trim().equals("")
    				){
    			map.put("msg","不能有空值！");
    			out.print(JSON.toJSON(map));
    			return;
    		}
        	Apply apply = new Apply(trueName.trim(),cellPhone.trim(),qq.trim());
    		applyService.add(apply);
    		map.put("msg","申请成功，请耐心等待！");
    		out.print(JSON.toJSON(map));
    	}catch(Exception e){
    		logger.info("apply Exception."+e.toString());
    		map.put("msg","申请失败，请稍后重试！");
    		out.print(JSON.toJSON(map));
    	}finally{
    		if(out!=null)out.close();
    		map=null;
    	}
       
    }
    @RequestMapping(value = "/suiji")
    public void mathRandom(@RequestParam(value = "cellPhone", defaultValue="") String cellPhone,
    	HttpServletRequest request,
    	HttpServletResponse response
    		){
    	String vcode=RandomUtil.getSixRandom();
     	SendMessage.sendMessage(cellPhone.trim(),"验证码:"+vcode);
     	PrintWriter out=null;
     	Map<String,String> map=new HashMap<String,String>();
		try {
			map.put("cellPhone",cellPhone.trim());
	    	map.put("vcode",vcode);
	    	out = response.getWriter();
	    	out.print(JSON.toJSON(map));
		} catch (IOException e) {
			logger.info("apply Exception. "+e.toString());
		}finally{
			if(out!=null)out.close();
			map=null;
		}
    	
    }
    
    @RequestMapping(value = "/toApply")
    public String toApply(HttpServletRequest request) {
    	return "back/jsp/apply";
    }
}
