package com.tyt.user.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.bean.CarLocation;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;
import com.tyt.plat.entity.base.CurrentLocationVO;
import com.tyt.user.service.CarCurrentLocationService;
import com.tyt.util.CarLocationUtils;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/08 10:28
 */
@Slf4j
@RequestMapping("/location")
@RestController
public class CarCurrentLocationController {

    @Autowired
    private CarCurrentLocationService carCurrentLocationService;

    @Resource
    private TransportOrdersService transportOrdersService;


    @GetMapping("/getCurrentLocation")
    public ResultMsgBean getCurrentLocation(@RequestParam("carHeadNo") String carHeadNo){
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            Object currentLocation = carCurrentLocationService.getCurrentLocation(carHeadNo);
            log.info("获取车辆位置成功:{}", JSON.toJSONString(currentLocation));
            resultMsgBean.setData(currentLocation);
        } catch (Exception e) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取车辆位置失败");
            log.info("获取车辆位置失败",e);
        }
        return resultMsgBean;
    }


    @GetMapping("/getCurrentLocations")
    public ResultMsgBean getCurrentLocations(@RequestParam("heads") String heads){
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            List<CurrentLocationVO> currentLocationVOList = carCurrentLocationService.getCurrentLocations(heads);
            resultMsgBean.setData(currentLocationVOList);
        } catch (Exception e) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取车辆位置失败");
            log.info("获取车辆位置失败",e);
        }
        return resultMsgBean;
    }


    @GetMapping("/getCarLocation")
    public ResultMsgBean getCarLocation(@RequestParam("headCity")  String headCity, @RequestParam("headNo") String headNo,@RequestParam("tsId") Long tsId){
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
//            Date beginTime = TimeUtil.parseString("2024-11-04 00:00:00");
//            Date endTime = TimeUtil.parseString("2024-11-08 00:00:00");
            TytTransportOrders transportOrders = transportOrdersService.getByTsId(tsId); //33833506L
            Date startOfDay = DateUtil.startOfDay(DateUtil.addTime(transportOrders.getPayEndTime(), Calendar.DAY_OF_MONTH, -1));
            Date endOfDay = DateUtil.endOfDay(DateUtil.addTime(transportOrders.getPayEndTime(), Calendar.DAY_OF_MONTH, 3));
            List<CarLocation> carLocation = CarLocationUtils.getCarLocation(headCity, headNo, startOfDay, endOfDay);
            resultMsgBean.setData(carLocation);
        } catch (Exception e) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取车辆轨迹失败");
            log.info("获取车辆轨迹失败",e);
        }
        return resultMsgBean;
    }

}
