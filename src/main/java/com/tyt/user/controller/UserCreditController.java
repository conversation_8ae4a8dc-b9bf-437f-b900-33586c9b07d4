package com.tyt.user.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.infofee.bean.CreditUserInfo;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.transport.service.TransportMainService;
import com.tyt.util.ReturnCodeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/plat/user/credit")
public class UserCreditController extends BaseController {

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@Autowired
	private IInfofeeDetailService infofeeDetailService;

	@RequestMapping(value = "/info/forcar", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean getUserCreditInfoForCar(@RequestParam(value = "goodsId", required = true) String goodsId,
										   String userId) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
		try {
			TransportMain transportMain = transportMainService.getTransportMainForId(Long.valueOf(goodsId));

			CreditUserInfo creditUserInfo = infofeeDetailService.getCreditUserInfoForCar(transportMain.getUserId().toString(), userId);
			resultMsgBean.setData(creditUserInfo);
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("失败");
		}
		return resultMsgBean;
	}

}
