package com.tyt.user.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.promo.service.PromoUserCouponService;
import com.tyt.tsinsurance.bean.ImportWaybillBean;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.user.bean.UserBaseInfo;
import com.tyt.user.service.InternalUserService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping(value = "internal/user")
@Slf4j
public class InternalUserController {
    @Autowired
    private InternalUserService internalUserService;
    @Resource(name="tsinsuranceService")
    private TsInsuranceService tsinsuranceService;
    @Autowired
    private PromoUserCouponService promoUserCouponService;
    /**
     * 获取用户信息及该用户近三天的运单信息
     * @param userId
     * @param opt 操作类型  1.我是车方  2.我是货方 默认车方
     * @return
     */
    @GetMapping(value = {"getbyid", "getbyid.action"})
    public ResultMsgBean getById(Long userId,@RequestParam(defaultValue = "1") Integer opt) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        UserBaseInfo userBaseInfo = internalUserService.getByUserId(userId);
        if (Objects.nonNull(userBaseInfo) && userBaseInfo.getCheckResult()) {
            List<ImportWaybillBean> beans = null;
            try {
                beans = tsinsuranceService.importWaybill(userId, opt);
            } catch (Exception e) {
                log.error("获取用户信息及该用户近三天的运单信息异常:{}", e.getMessage());
                e.printStackTrace();
            }
            userBaseInfo.setWaybills(beans);
        }
        return ResultMsgBean.successResponse(userBaseInfo);
    }

    /**
     * 获取最近3天运单信息
     * @param userId
     * @param opt 操作类型  1.我是车方  2.我是货方
     * @return
     */
    @GetMapping("get/recent/order")
    public ResultMsgBean getRecentOrder(Long userId,Integer opt) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        if (Objects.isNull(opt)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "opt不能为空");
        }
        List<ImportWaybillBean> beans = null;
        try {
            beans = tsinsuranceService.importWaybill(userId, opt);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResultMsgBean.successResponse(beans);
    }


    /**
     * 修改赠险卡卷状态
     * @param id
     * @param coupomStatus 状态
     * @return
     */
    @GetMapping(value = {"updateinsurancecoupom", "updateinsurancecoupom.action"})
    @ResponseBody
    public ResultMsgBean updateInsuranceCoupom(Integer id, Integer coupomStatus) {
        if (Objects.isNull(id)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "id不能为空");
        }
        if (Objects.isNull(coupomStatus)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "coupomStatus不能为空");
        }
            try {
                promoUserCouponService.updateCoupom(id, coupomStatus);
            } catch (Exception e) {
                log.error("修改赠险卡卷状态异常:{}", e.getMessage());
                e.printStackTrace();
            }
        return ResultMsgBean.successResponse();
    }
}
