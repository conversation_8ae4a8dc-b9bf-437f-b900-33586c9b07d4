package com.tyt.user.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.tyt.acvitity.service.CsComplaintRecordResultServcie;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.common.service.*;
import com.tyt.config.util.AppConfig;
import com.tyt.corp.CorpRestClient;
import com.tyt.driver.service.TytCarDriverArchivesService;
import com.tyt.excellentgoodshomepage.service.ExcellentGoodsService;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqUserMsg;
import com.tyt.infofee.bean.UserPermissionResponseBean;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.infofee.service.UserCancelService;
import com.tyt.maintainer.service.MaintainerService;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.model.*;
import com.tyt.noticePopup.bean.PopupSaveBean;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupService;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.bean.GoodsType;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.enterprise.db.EnterpriseDBService;
import com.tyt.plat.entity.base.TytAbtestConfig;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.enums.AbTestRuleTypeEnum;
import com.tyt.plat.enums.ValidDateFlagEnum;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.service.user.UserVerifyService;
import com.tyt.promo.model.VipNoticeForGoodsBean;
import com.tyt.promo.service.ICouponService;
import com.tyt.receive.service.TransportBackendAxbBinderService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.enums.DepositTypeEnum;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportService;
import com.tyt.user.bean.*;
import com.tyt.user.bean.AdviceTypeBean.AdviceTypeVO;
import com.tyt.user.enums.UserPermissionTypeEnum;
import com.tyt.user.querybean.*;
import com.tyt.user.service.*;
import com.tyt.user.service.impl.VersionBusinessService;
import com.tyt.util.*;
import com.tyt.wxuser.bean.BindPhoneBean;
import com.tyt.wxuser.bean.BindPhoneResBean;
import com.tyt.wxuser.service.TytCarWxUserInfoService;
import com.tyt.wxuser.service.TytWxUserInfoService;
import com.tytrecommend.recommend.service.PreferService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.tyt.user.enums.UserPermissionTypeEnum.*;
import static com.tyt.user.enums.UserPermissionTypeEnum.GOODS_NUM_NEW;

/**
 * 用户相关接口
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/plat/user")
public class UserController extends BaseController {
    public static final int IS_MOCK_USER = 1;
    private final String tpayAccountUrl = AppConfig.getProperty("tpay.account.api.url");

    @Resource(name = "maintainerService")
    private MaintainerService maintainerService;
    @Resource(name = "tytChannelLogService")
    private TytChannelLogService tytChannelLogService;
    @Resource(name = "userCallPhoneService")
    private UserCallPhoneService userCallPhoneService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "userTelService")
    private UserTelService userTelService;
    @Resource(name = "adviceService")
    private AdviceService adviceService;
    @Resource(name = "inviteFriendsService")
    InviteFriendsService inviteFriendsService;
    @Resource(name = "linkManService")
    private LinkManService linkManService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "versionBusiness")
    private VersionBusinessService versionBusiness;
    @Resource(name = "tytUserSubService")
    private TytUserSubService tytUserSubService;
    @Resource(name = "userIdentityMainService")
    private UserIdentityMainService userIdentityMainService;
    @Resource(name = "maintainerService")
    private MaintainerService matainerService;
    @Resource(name = "tytForceUpgradeUserService")
    private TytForceUpgradeUserService tytForceUpgradeUserService;
    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityAuthService;
    @Resource(name = "tytUserMacService")
    private TytUserMacService userMacService;
    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;
    @Resource(name = "transportService")
    private TransportService transportService;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;
    @Resource(name = "tytSourceService")
    private TytSourceService sourceService;
    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;
    @Resource(name = "preferService")
    private PreferService preferService;

    @Resource(name = "loginActivityService")
    private LoginActivityService loginActivityService;
    @Resource
    private CarService carService;

    @Resource(name = "tytBubbleService")
    private TytBubbleService bubbleService;

    @Resource(name = "couponService")
    private ICouponService couponService;

    @Resource(name = "tytNoticeRemindTmplService")
    private TytNoticeRemindTmplService tytNoticeRemindTmplService;

    @Resource(name = "tytNoticeRemindService")
    private TytNoticeRemindService noticeRemindService;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService noticePopupTemplService;

    @Resource(name = "tytNoticePopupService")
    private TytNoticePopupService noticePopupService;

    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Resource(name = "userBuyGoodsService")
    private UserBuyGoodsService userBuyGoodsService;

    @Resource(name = "blacklistUserService")
    private BlacklistUserService blacklistUserService;

    @Resource(name = "tytCarDriverArchivesService")
    private TytCarDriverArchivesService tytCarDriverArchivesService;
    @Resource(name = "tytWxUserInfoService")
    private TytWxUserInfoService tytWxUserInfoService;
    @Resource(name = "tytCarWxUserInfoService")
    private TytCarWxUserInfoService tytCarWxUserInfoService;


    @Autowired
    private IInfofeeDetailService infofeeDetailService;

    @Resource(name = "tytUserStaffService")
    private TytUserStaffService tytUserStaffService;
    @Resource(name = "transportBackendAxbBinderService")
    private TransportBackendAxbBinderService transportBackendAxbBinderService;

    @Resource(name = "userCancelService")
    private UserCancelService userCancelService;

    @Resource(name = "userCreditInfoPopupService")
    private UserCreditInfoPopupService userCreditInfoPopupService;

    @Autowired
    private EnterpriseDBService enterpriseDBService;

    @Autowired
    private CsComplaintRecordResultServcie csComplaintRecordResultServcie;

    @Autowired
    private CsMaintainedCustomService csMaintainedCustomService;

    @Autowired
    private InternalEmployeeService internalEmployeeService;

    @Autowired
    private IFeedbackUserService feedbackUserService;

    @Autowired
    private UserVerifyService userVerifyService;

    @Autowired
    private ExcellentGoodsService excellentGoodsService;

    @Autowired
    private AbtestService abtestService;

    private static final PropertiesFileUtil propertiesFileUtil = PropertiesFileUtil.init("message");

    public static final String userWeb =AppConfig.getProperty("user.service.api.url");


    private static final String USER_VIP_COUNT_KEY = "VIP_COUNT";

    private static final String PHONE_COUNT_AB_CODE = "phone_count_abtest";


    /**
     * 手机号正则
     */
    private static final String MOBILE_REGEX = "^1\\d{10}$";

    /**
     * 客户端区分新老app code值，小于等于6的为老版本客户端，大于6的为版本拆分后的新版本
     */
    private static final int CLIENT_SIGN_SEPERATOR = 6;

    private static final String LOW_VERSION_NOTICE = "当前版本过低，请到应用市场下载新版本";

    private static final Map<String, Boolean> banPasswords = new HashMap<>();

    static {
        banPasswords.put("e10adc3949ba59abbe56e057f20f883e", true); // 123456
        banPasswords.put("fcea920f7412b5da7be0cf42b8c93759", true); // 1234567
        banPasswords.put("25d55ad283aa400af464c76d713c07ad", true); // 12345678
        banPasswords.put("25f9e794323b453885f5181f1b624d0b", true); // 123456789
        banPasswords.put("c33367701511b4f6020ec61ded352059", true); // 654321
        banPasswords.put("4297f44b13955235245b2497399d7a93", true); // 123123

        banPasswords.put("c5db244dbfc91ca5dc46626cc21d5594", true); // 12301230
        banPasswords.put("dc483e80a7a0bd9ef71d8cf973673924", true); // a123456
        banPasswords.put("efe6398127928f1b2e9ef3207fb82663", true); // qweqwe
        banPasswords.put("f379eaf3c831b04de153469d1bec345e", true); // 666666
        banPasswords.put("3d186804534370c3c817db0563f0e461", true); // 321321
        banPasswords.put("7c497868c9e6d3e4cf2e87396372cd3b", true); // 66666666
        banPasswords.put("8ddcff3a80f4189ca1c9d4d902c3c909", true); // 88888888
        banPasswords.put("75e266f182b4fa3625d4a4f4f779af54", true); // 666888
        banPasswords.put("670b14728ad9902aecba32e22fa4f6bd", true); // 000000
        banPasswords.put("21218cca77804d2ba1922c33e0151105", true); // 888888
        banPasswords.put("af8f9dffa5d420fbc249141645b962ee", true); // a12345
        banPasswords.put("d5ee2eedfcf7adc285db4967bd86910d", true); // 6666666
        banPasswords.put("388ec3e3fa4983032b4f3e7d8fcb65ad", true); // 8888888
        banPasswords.put("002772406c000396fd4704af816b075f", true); // 618618
        banPasswords.put("fea73474cda848c7233e377158dbef60", true); // 871871
    }

    /**
     * 用户正常登录接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/oldLogin", "/oldLogin.action"})
    public void login(HttpServletRequest request, HttpServletResponse response) {
        long startMillis = System.currentTimeMillis();
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            int clientVersion = Integer.parseInt(params.get("clientVersion"));
            if (clientVersion < 6000) {
                backResponse(request, response, ReturnCodeConstant.ERROR, LOW_VERSION_NOTICE, null, 0);
                return;
            }
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("cellPhone");
                    add("password");
                }
            };
            if (!validateArguments("用户登录", request, response, params, names)) {
                return;
            }
            /* 取相关参数 */
            String cellPhone = params.get("cellPhone");
            String password = params.get("password");
            String clientSign = params.get("clientSign");
            String clientId = params.get("clientId");
            String userVersion = params.get("clientVersion");
            String condition = cellPhone;
            String channelCode = params.get("channelCode");// 区分IOS是企业版还是个人版

            //获取用户请求的真实的IP地址
            String realIp = StringUtil.getRealIp(request);
            //判断限制特定IP登录的开关是否开启
            if (TytSwitchUtil.isLimitIpLogin()) {
                logger.info("用户请求的真实的IP地址为【realIp：{}】", realIp);

                //获取限制特定IP登录的地址列表
                String limitIps = tytConfigService.getStringValue("limit_ip_login_list", "***************");
                logger.info("限制特定IP登录的地址列表为【limitIps：{}】", limitIps);
                //限制特定IP登录的提示语
                String limitTips = tytConfigService.getStringValue("limit_ip_login_tips", "系统升级中");
                //如果IP地址列表不为空，允许特定IP地址登录
                if (StringUtils.isNotBlank(limitIps)) {
                    List<String> ipList = Arrays.asList(limitIps.split("#"));
                    //如果限制特定IP登录的地址列表不包含用户请求IP地址，则限制登录
                    if (!ipList.contains(realIp)) {
                        backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, limitTips, null, 0);
                        return;
                    }
                } else {//如果IP地址为空，则不允许任何IP地址登录
                    backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, limitTips, null, 0);
                    return;
                }
            }

            // 校验手机号格式
            if (!this.validateMobileNo(cellPhone, request, response)) {
                return;
            }

            Integer versionControl = tytConfigService.getIntValue(Constant.LOGIN_VERSION_CONTROL, 1000);
            if (Integer.parseInt(userVersion) < versionControl) {
                backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, "系统升级中", null, 0);
                return;
            }
            /* 判断用户是否属于强制升级的用户 */
            Map<String, Object> upgradeStrategyMap = tytForceUpgradeUserService.getForceUpgradeVersionInfo(2, cellPhone, null, clientId, channelCode, clientSign, userVersion);
            if (upgradeStrategyMap != null && "1".equals(upgradeStrategyMap.get("type"))) {
                logger.info(condition + "软件版本低,属于强制升级的用户");
                backResponse(request, response, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", upgradeStrategyMap, 0);
                return;
            }

            /* 判断版本号 */
            Map<String, Object> versionResult = versionBusiness.checkVersion(true, params);
            if (versionResult != null) {
                String type = (String) versionResult.get("type");
                if (type.equals("1")) {
                    logger.info(condition + "软件版本低");
                    backResponse(request, response, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", versionResult, 0);
                    return;
                }

            }
            /* 根据手机号查询用户 */
            User user = userService.getUserByCellphone(cellPhone);
            //马甲用户登陆提示同未注册
            if (user != null && user.getIsMock() == 1) {
                user = null;
            }
            //校验是否初始化密码
            String appletsLoginPassword = tytConfigService
                    .getStringValue(Constant.APPLETS_LOGIN_PASSWORD, "42f749c840acea865fbe927947eea7ae");
            if (user != null && appletsLoginPassword.equals(user.getPassword())) {
                backResponse(request, response, ReturnCodeConstant.APPLETS_LOGIN_PASSWORD, "请设置登录密码",
                        null, 0);
                return;
            }

            // 是否需要验证登录失败次数的标记
            boolean isNeedRestricLoginFail = false;
            String todayDate = TimeUtil.formatDate(new Date());
            try {
                // 获取需要进行登录错误次数控制的最小版本（不包括）
                String restriceLoginFailMinVersion = tytConfigService
                        .getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5301");
                int restriceLoginFailMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
                int clientVersionInt = Integer.valueOf(userVersion).intValue();
                isNeedRestricLoginFail = (clientVersionInt > restriceLoginFailMinVersionInt);
            } catch (Exception e) {
            }
            // 判断手机号存在否？
            if (user == null) {
                logger.error(condition + "手机号尚未注册");
                // 设置手机号未注册状态码供客户端使用
                if (isNeedRestricLoginFail) {
                    backResponse(request, response, 10000, "手机号尚未注册",
                            null, 0);
                } else {
                    backResponse(request, response, ReturnCodeConstant.LOGIN_ERROR_CODE, "手机号尚未注册",
                            null, 0);
                }
                return;
            }
            // 判断用户激活否？
//			if (user.getUserType() == User.USER_TYPE_TRIAL_NOT_VERIFY) {
//				logger.error(condition + "账号未激活");
//				backResponse(request, response, ReturnCodeConstant.LOGIN_ERROR_CODE, "账号未激活，请联系客服", null, 0);
//				return;
//			}
            // 判断用户黑名单否？
            if (user.getBlackStatus() == User.BLACKLIST_STATUS_Y) {
                BlacklistUser blacklistUser = blacklistUserService.getBlackByUserId(user.getId());
                int cause = 5; // 默认其它原因
                if (blacklistUser != null) {
                    cause = blacklistUser.getCause();
                }
                TytSource tytSource = TytSourceUtil.getSourceName("monitor_user_add_reason", String.valueOf(cause));
                String causeName = "账号未激活，请联系客服";
                if (tytSource != null) {
                    causeName = tytSource.getName() + "，请联系客服";
                }
                // 设置账号未激活状态码供客户端使用
                if (isNeedRestricLoginFail) {
                    backResponse(request, response, 10001, causeName, null, 0);
                } else {
                    logger.error(condition + "账号在黑名单");
                    backResponse(request, response, ReturnCodeConstant.LOGIN_ERROR_CODE, causeName, null, 0);
                }
                return;
            }
            String redisKey = Constant.LOGIN_FAIL_TIME_REDIS_KEY + cellPhone + todayDate;
            int cacheSeconds = (int) TimeUtil.getTomorrowZeroSeconds();
            // 如果是需要限制登录次数的版本则判断用户当天的登录次数
            if (isNeedRestricLoginFail) {
                String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                String verifyOk = RedisUtil.get(verifyOkKey);
                if (StringUtils.isEmpty(verifyOk) || !"1".equals(verifyOk)) {
                    // 获取当前登录失败的次数，如果达到十次则返回控制客户端弹出验证码
                    String loginFailTime = RedisUtil.get(redisKey);
                    if (StringUtils.isNotEmpty(loginFailTime)) {
                        if (Integer.valueOf(loginFailTime)
                                .intValue() >= Constant.PASSWORD_ERROR_SHOW_CODE_TIME) {
                            backResponse(request, response, 10004, "密码错误次数超过10次", null, 0);
                            return;
                        }
                    }
                }
            }
            // 判断密码正确否？
            if (!password(user, password)) {
                // 设置密码错误状态码供客户端使用
                if (isNeedRestricLoginFail) {
                    String loginFailTime = RedisUtil.get(redisKey);
                    // 如果当前没有失败次数则记录失败次数为1次,
                    // 否则判断登录失败的次数是否到达5次，如果到达5次，则返回引导客户端弹出重置密码框状态码
                    if (StringUtils.isEmpty(loginFailTime)) {
                        RedisUtil.set(redisKey, "1", cacheSeconds);
                    } else {
                        // 增加登录失败次数
                        RedisUtil.incr(redisKey);
                        int loginFailTimeInt = Integer.valueOf(loginFailTime).intValue();
                        loginFailTimeInt = loginFailTimeInt + 1;
                        // 登录失败次数到达5次引导客户端弹出重置密码框状态码
                        if (loginFailTimeInt >= Constant.PASSWORD_ERROR_SHOW_MODIFY_TIME) {
                            // 如果大于10次则说明是用户成功输入了验证码，再次密码错误需要清空验证码通过信息
                            if (loginFailTimeInt > Constant.PASSWORD_ERROR_SHOW_CODE_TIME) {
                                // 密码错误一次删除验证码通过信息
                                String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                                RedisUtil.del(verifyOkKey);
                            }
                            backResponse(request, response, 10003, "密码错误次数超过5次", null, 0);
                            return;
                        }
                    }
                    backResponse(request, response, 10002, "密码错误,请重新输入", null, 0);
                } else {
                    logger.error(condition + "密码错误");
                    backResponse(request, response, ReturnCodeConstant.LOGIN_ERROR_CODE, "密码错误,请重新输入", null, 0);
                }
                return;
            }
            // 简单密码校验，禁止登陆
            boolean isBanPassword = banPasswords.containsKey(user.getPassword());
            if (isBanPassword) {
                backResponse(request, response, 10002, "登录密码过于简单，点击忘记密码立即修改", null, 0);
                return;

                // 分批次按取膜3日计算 --------过渡时期结束，全面放开--------
//				long phoneModVal = Long.parseLong(cellPhone) % 3;
//				long dateModVal = Long.parseLong(TimeUtil.formatDate_(new Date())) % 3;
//				if(phoneModVal == dateModVal) {
//					backResponse(request, response, 10002, "登录密码过于简单，点击忘记密码立即修改", null, 0);
//					return;
//				}
            }

            if (isNeedRestricLoginFail) {
                // 清空错误次数信息
                RedisUtil.del(redisKey);
                // 清除验证码验证信息
                String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                RedisUtil.del(verifyOkKey);
            }
            //add by tianjw on 20180723  增加登录后的活动操作
            try {
                loginActivityService.excuteActivity(user);
                //登录活动发mq
                //20190428增加5月优惠券活动 此次活动时间20190506-20190531 仅限大于5910版本
                loginActivityService.sendActivityMq(user.getId(), userVersion);
            } catch (Exception e) {
                logger.error("活动发送首次登录推送消息失败");
                e.printStackTrace();
            }
            //钱包待激活开户-已认证用户待激活开户
            TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(user.getId()));
            if (identityAuth != null && identityAuth.getIdentityStatus() == 1) {
                userService.openacctInactiveApply(user.getId());
            }
            // 记录日志,响应客户端
            channelLog(request, user, ChannelLogConstant.Login, params);
            UserBean userBean = commonLogin(request, user, params, upgradeStrategyMap, clientId, "0");
            logger.info(condition + "old用户登录成功...... {}  use {}", user.getId(), System.currentTimeMillis() - startMillis);
            backResponse(request, response, ReturnCodeConstant.OK, "登陆成功", userBean, 0);
            logger.info(condition + "用户登录成功......");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }

    }

    /**
     * 用户正常登录接口  新登录 优化后
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/login", "/login.action"})
    public void newLogin(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);

            if (!userService.versionLoginAllow(params)) {
                this.backResponse(request, response, ReturnCodeConstant.ERROR, LOW_VERSION_NOTICE, null, 0);
                return;
            }

//            TytNoticePopupTempl noticePopupTempl = userService.checkVersionLogin(request, params);
//            if (noticePopupTempl != null) {
//                this.backResponseAddNotice(request, response, ReturnCodeConstant.NO_PERMISSION, "当前版本过低，请升级", null, 0, noticePopupTempl);
//                return;
//            }

            boolean passed = this.loginCheck(params, request, response);
            if (!passed) {
                return;
            }
            /* 取相关参数 */
            String cellPhone = params.get("cellPhone");
            ResultMsgBean resultMsgBean = userService.newLogin(request, params);
            backResponseAddNotice(request, response, resultMsgBean.getCode(), resultMsgBean.getMsg(), resultMsgBean.getData(), 0, resultMsgBean.getNoticeData());
            logger.info(cellPhone + "用户登录完成......,结果：{}", resultMsgBean.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }

    }

    private boolean loginCheck(Map<String, String> params, HttpServletRequest request, HttpServletResponse response) throws Exception {
        /* 业务参数验证 */
        @SuppressWarnings("serial")
        List<String> names = new ArrayList<String>() {
            {
                add("cellPhone");
                add("password");
            }
        };
        if (!validateArguments("用户登录", request, response, params, names)) {
            return false;
        }
        /* 取相关参数 */
        String cellPhone = params.get("cellPhone");
        // 校验手机号格式
        if (!this.validateMobileNo(cellPhone, request, response)) {
            return false;
        }
        return loginCheckForUpgrade(params, request, response);
    }

    private boolean loginCheckForUpgrade(Map<String, String> params, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取用户请求的真实的IP地址
        String realIp = StringUtil.getRealIp(request);
        //判断限制特定IP登录的开关是否开启
        if (TytSwitchUtil.isLimitIpLogin()) {
            logger.info("用户请求的真实的IP地址为【realIp：{}】", realIp);

            //获取限制特定IP登录的地址列表
            String limitIps = tytConfigService.getStringValue("limit_ip_login_list", "***************");
            logger.info("限制特定IP登录的地址列表为【limitIps：{}】", limitIps);
            //限制特定IP登录的提示语
            String limitTips = tytConfigService.getStringValue("limit_ip_login_tips", "系统升级中");
            //如果IP地址列表不为空，允许特定IP地址登录
            if (StringUtils.isNotBlank(limitIps)) {
                List<String> ipList = Arrays.asList(limitIps.split("#"));
                //如果限制特定IP登录的地址列表不包含用户请求IP地址，则限制登录
                if (!ipList.contains(realIp)) {
                    backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, limitTips, null, 0);
                    return false;
                }
            } else {//如果IP地址为空，则不允许任何IP地址登录
                backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, limitTips, null, 0);
                return false;
            }
        }
        /* 取相关参数 */
//        String userVersion = params.get("clientVersion");
//        Integer versionControl = tytConfigService.getIntValue(Constant.LOGIN_VERSION_CONTROL, 1000);
//        if (Integer.parseInt(userVersion) < versionControl) {
//            backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, "系统升级中", null, 0);
//            return false;
//        }

        /* 判断版本号 */
        Map<String, Object> versionResult = versionBusiness.checkVersion(true, params);
        if (versionResult != null) {
            String type = (String) versionResult.get("type");
            if (type.equals("1")) {
                backResponse(request, response, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", versionResult, 0);
                return false;
            }
        }
        return true;
    }

    /**
     * 短信登录接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/oldSmsLogin"})
    public void smsLogin(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("cellPhone");
                    add("verificationCode");
                }
            };
            if (!smsLoginValidateArguments("短信登录", request, response, params, names)) {
                return;
            }
            /* 取相关参数 */
            String cellPhone = params.get("cellPhone");
            String verificationCode = params.get("verificationCode");
            String clientSign = params.get("clientSign");
            String clientId = params.get("clientId");
            String userVersion = params.get("clientVersion");
            String condition = cellPhone;
            String channelCode = params.get("channelCode");// 区分IOS是企业版还是个人版

            //获取用户请求的真实的IP地址
            String realIp = StringUtil.getRealIp(request);
            //判断限制特定IP登录的开关是否开启
            if (TytSwitchUtil.isLimitIpLogin()) {
                logger.info("用户请求的真实的IP地址为【realIp：{}】", realIp);

                //获取限制特定IP登录的地址列表
                String limitIps = tytConfigService.getStringValue("limit_ip_login_list", "***************");
                logger.info("限制特定IP登录的地址列表为【limitIps：{}】", limitIps);
                //限制特定IP登录的提示语
                String limitTips = tytConfigService.getStringValue("limit_ip_login_tips", "系统升级中");
                //如果IP地址列表不为空，允许特定IP地址登录
                if (StringUtils.isNotBlank(limitIps)) {
                    List<String> ipList = Arrays.asList(limitIps.split("#"));
                    //如果限制特定IP登录的地址列表不包含用户请求IP地址，则限制登录
                    if (!ipList.contains(realIp)) {
                        backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, limitTips, null, 0);
                        return;
                    }
                } else {//如果IP地址为空，则不允许任何IP地址登录
                    backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, limitTips, null, 0);
                    return;
                }
            }

            // 校验手机号格式
            if (!this.validateMobileNo(cellPhone, request, response)) {
                return;
            }

            Integer versionControl = tytConfigService.getIntValue(Constant.LOGIN_VERSION_CONTROL, 1000);
            if (Integer.parseInt(userVersion) < versionControl) {
                backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, "系统升级中", null, 0);
                return;
            }
            /* 判断用户是否属于强制升级的用户 */
            Map<String, Object> upgradeStrategyMap = tytForceUpgradeUserService.getForceUpgradeVersionInfo(2, cellPhone, null, clientId, channelCode, clientSign, userVersion);
            if (upgradeStrategyMap != null && "1".equals(upgradeStrategyMap.get("type"))) {
                logger.info(condition + "软件版本低,属于强制升级的用户");
                backResponse(request, response, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", upgradeStrategyMap, 0);
                return;
            }

            /* 判断版本号 */
            Map<String, Object> versionResult = versionBusiness.checkVersion(true, params);
            if (versionResult != null) {
                String type = (String) versionResult.get("type");
                if (type.equals("1")) {
                    logger.info(condition + "软件版本低");
                    backResponse(request, response, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", versionResult, 0);
                    return;
                }

            }
            /* 根据手机号查询用户 */
            User user = userService.getUserByCellphone(cellPhone);
            //马甲用户登陆提示同未注册
            if (user != null && user.getIsMock() == 1) {
                user = null;
            }
            // 是否需要验证登录失败次数的标记
            boolean isNeedRestricLoginFail = false;
            String todayDate = TimeUtil.formatDate(new Date());
            try {
                // 获取需要进行登录错误次数控制的最小版本（不包括）
                String restriceLoginFailMinVersion = tytConfigService
                        .getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5301");
                int restriceLoginFailMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
                int clientVersionInt = Integer.valueOf(userVersion).intValue();
                isNeedRestricLoginFail = (clientVersionInt > restriceLoginFailMinVersionInt);
            } catch (Exception e) {
            }
            // 判断手机号存在否？
            Integer newUserFalg = 0;
            if (user == null) {
                newUserFalg = 1;
                User oldUser = userService.getUserByCellphone(cellPhone);
                User newUser = regUser(params);
                String appletsLoginPassword = tytConfigService
                        .getStringValue(Constant.APPLETS_LOGIN_PASSWORD, "42f749c840acea865fbe927947eea7ae");
                newUser.setPassword(appletsLoginPassword);
                UserBean userBean = createNewUser(request, params, oldUser, newUser);
                user = userService.getByUserId(userBean.getId());
            }
            // 判断用户激活否？
//			if (user.getUserType() == User.USER_TYPE_TRIAL_NOT_VERIFY) {
//				logger.error(condition + "账号未激活");
//				backResponse(request, response, ReturnCodeConstant.LOGIN_ERROR_CODE, "账号未激活，请联系客服", null, 0);
//				return;
//			}
            // 判断用户黑名单否？
            if (user.getBlackStatus() == User.BLACKLIST_STATUS_Y) {
                BlacklistUser blacklistUser = blacklistUserService.getBlackByUserId(user.getId());
                int cause = 5; // 默认其它原因
                if (blacklistUser != null) {
                    cause = blacklistUser.getCause();
                }
                TytSource tytSource = TytSourceUtil.getSourceName("monitor_user_add_reason", String.valueOf(cause));
                String causeName = "账号未激活，请联系客服";
                if (tytSource != null) {
                    causeName = tytSource.getName() + "，请联系客服";
                }
                // 设置账号未激活状态码供客户端使用
                if (isNeedRestricLoginFail) {
                    backResponse(request, response, 10001, causeName, null, 0);
                } else {
                    logger.error(condition + "账号在黑名单");
                    backResponse(request, response, ReturnCodeConstant.LOGIN_ERROR_CODE, causeName, null, 0);
                }
                return;
            }
            String redisKey = Constant.LOGIN_SMS_FAIL_TIME_REDIS_KEY + cellPhone + todayDate;
            int cacheSeconds = (int) TimeUtil.getTomorrowZeroSeconds();
            // 如果是需要限制登录次数的版本则判断用户当天的登录次数
            if (isNeedRestricLoginFail) {
                String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                String verifyOk = RedisUtil.get(verifyOkKey);
                if (StringUtils.isEmpty(verifyOk) || !"1".equals(verifyOk)) {
                    // 获取当前登录失败的次数，如果达到十次则返回控制客户端弹出验证码
                    String loginFailTime = RedisUtil.get(redisKey);
                    if (StringUtils.isNotEmpty(loginFailTime)) {
                        if (Integer.valueOf(loginFailTime)
                                .intValue() >= Constant.PASSWORD_ERROR_SHOW_CODE_TIME) {
                            backResponse(request, response, 10004, "验证码错误次数超过10次", null, 0);
                            return;
                        }
                    }
                }
            }


            // 判断验证码正确否？
            if (!validateLoginVerifyCode(verificationCode, cellPhone, request, response)) {
                // 设置验证码错误状态码供客户端使用
                if (isNeedRestricLoginFail) {
                    String loginFailTime = RedisUtil.get(redisKey);
                    // 如果当前没有失败次数则记录失败次数为1次,
                    // 否则判断登录失败的次数是否到达5次，如果到达5次，则返回引导客户端弹出重置密码框状态码
                    if (StringUtils.isEmpty(loginFailTime)) {
                        RedisUtil.set(redisKey, "1", cacheSeconds);
                    } else {
                        // 增加登录失败次数
                        RedisUtil.incr(redisKey);
                        int loginFailTimeInt = Integer.valueOf(loginFailTime).intValue();
                        loginFailTimeInt = loginFailTimeInt + 1;
                        // 登录失败次数到达5次引导客户端弹出重置密码框状态码
                        if (loginFailTimeInt >= Constant.PASSWORD_ERROR_SHOW_MODIFY_TIME) {
                            // 如果大于10次则说明是用户成功输入了验证码，再次密码错误需要清空验证码通过信息
                            if (loginFailTimeInt > Constant.PASSWORD_ERROR_SHOW_CODE_TIME) {
                                // 密码错误一次删除验证码通过信息
                                String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                                RedisUtil.del(verifyOkKey);
                            }
                            backResponse(request, response, 10002, "验证码错误,请重新输入", null, 0);
                            return;
                        }
                    }
                    backResponse(request, response, 10002, "验证码错误,请重新输入", null, 0);
                } else {
                    logger.error(condition + "验证码错误");
                    backResponse(request, response, ReturnCodeConstant.LOGIN_ERROR_CODE, "验证码错误,请重新输入", null, 0);
                }
                return;
            }
            // 简单密码校验，禁止登陆
            boolean isBanPassword = banPasswords.containsKey(user.getPassword());
            if (isBanPassword) {
                backResponse(request, response, 10002, "登录密码过于简单，点击忘记密码立即修改", null, 0);
                return;

                // 分批次按取膜3日计算 --------过渡时期结束，全面放开--------
//				long phoneModVal = Long.parseLong(cellPhone) % 3;
//				long dateModVal = Long.parseLong(TimeUtil.formatDate_(new Date())) % 3;
//				if(phoneModVal == dateModVal) {
//					backResponse(request, response, 10002, "登录密码过于简单，点击忘记密码立即修改", null, 0);
//					return;
//				}
            }

            if (isNeedRestricLoginFail) {
                // 清空错误次数信息
                RedisUtil.del(redisKey);
                // 清除验证码验证信息
                String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                RedisUtil.del(verifyOkKey);
            }
            //add by tianjw on 20180723  增加登录后的活动操作
            try {
                loginActivityService.excuteActivity(user);
                //登录活动发mq
                //20190428增加5月优惠券活动 此次活动时间20190506-20190531 仅限大于5910版本
                loginActivityService.sendActivityMq(user.getId(), userVersion);
            } catch (Exception e) {
                logger.error("活动发送首次登录推送消息失败");
                e.printStackTrace();
            }
            //钱包待激活开户-已认证用户待激活开户
            TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(user.getId()));
            if (identityAuth != null && identityAuth.getIdentityStatus() == 1) {
                userService.openacctInactiveApply(user.getId());
            }
            // 记录日志,响应客户端
            channelLog(request, user, ChannelLogConstant.Login, params);
            UserBean result = commonLogin(request, user, params, upgradeStrategyMap, clientId, "0");
            result.setNewUserFlag(newUserFalg);
            backResponse(request, response, ReturnCodeConstant.OK, "登陆成功", result, 0);
            logger.info(condition + "用户登录成功......");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }

    }

    /**
     * 短信登录接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/smsLogin"})
    public void newSmsLogin(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("cellPhone");
                    add("verificationCode");
                }
            };
            if (!smsLoginValidateArguments("短信登录", request, response, params, names)) {
                return;
            }

            if (!userService.versionLoginAllow(params)) {
                this.backResponse(request, response, ReturnCodeConstant.ERROR, LOW_VERSION_NOTICE, null, 0);
                return;
            }

            TytNoticePopupTempl noticePopupTempl = userService.checkVersionLogin(request, params);
            if (noticePopupTempl != null) {
                this.backResponseAddNotice(request, response, ReturnCodeConstant.NO_PERMISSION, "当前版本过低，请升级", null, 0, noticePopupTempl);
                return;
            }
            boolean passed = this.loginCheckForUpgrade(params, request, response);
            if (!passed) {
                return;
            }
            /* 取相关参数 */
            String cellPhone = params.get("cellPhone");
            String condition = cellPhone;

            //获取用户请求的真实的IP地址
            String realIp = StringUtil.getRealIp(request);
            //判断限制特定IP登录的开关是否开启
            if (TytSwitchUtil.isLimitIpLogin()) {
                logger.info("用户请求的真实的IP地址为【realIp：{}】", realIp);

                //获取限制特定IP登录的地址列表
                String limitIps = tytConfigService.getStringValue("limit_ip_login_list", "***************");
                logger.info("限制特定IP登录的地址列表为【limitIps：{}】", limitIps);
                //限制特定IP登录的提示语
                String limitTips = tytConfigService.getStringValue("limit_ip_login_tips", "系统升级中");
                //如果IP地址列表不为空，允许特定IP地址登录
                if (StringUtils.isNotBlank(limitIps)) {
                    List<String> ipList = Arrays.asList(limitIps.split("#"));
                    //如果限制特定IP登录的地址列表不包含用户请求IP地址，则限制登录
                    if (!ipList.contains(realIp)) {
                        backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, limitTips, null, 0);
                        return;
                    }
                } else {//如果IP地址为空，则不允许任何IP地址登录
                    backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, limitTips, null, 0);
                    return;
                }
            }

            // 校验手机号格式
            if (!this.validateMobileNo(cellPhone, request, response)) {
                return;
            }

            ResultMsgBean resultMsgBean = userService.newSmsLogin(request, params);
            backResponseAddNotice(request, response, resultMsgBean.getCode(), resultMsgBean.getMsg(), resultMsgBean.getData(), 0, resultMsgBean.getNoticeData());
            logger.info(condition + "用户短信登录完成, 结果：{}......", resultMsgBean.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }

    }

    private boolean validateLoginVerifyCode(String code, String cellPhone, HttpServletRequest request, HttpServletResponse response) {
        String realVerifyCode = RedisUtil.get(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
        if (!code.equals(realVerifyCode)) {
            return false;
        } else {
            RedisUtil.del(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
            return true;
        }
    }

    private void rebindDevice(String clientId, User user, Integer clientSign) {
        Long userId = user.getId();

        // 用户手机号
        String cellPhone = user.getCellPhone();
        // 查看该用户是否已经绑定设备，未绑定则尝试绑定
        TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(user.getId());
        logger.info("======================tytUserSub:" + tytUserSub);
        if (tytUserSub != null) {
            Integer userGroup = tytUserSub.getUserGroup();
            //设备是否被绑定 true：是 false：否
            boolean hasBinded = tytUserSubService.hasBinded(clientId, userId);
            //绑定状态
            Integer bindStatus = tytUserSub.getBindStatus();
            int clientPort = Constant.isCarOrGoodsOrOrigin(clientSign);
            logger.info("rebindDevice userId: " + userId + ", tytUserSub.getBindStatus()："
                    + bindStatus + ", clientId: " + clientId + " userGroup:" + userGroup + " hasBinded:" + hasBinded);
            TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(userId));
            // 如果是未绑定状态则尝试绑定
            boolean activityOnOff = TytSwitchUtil.isGoodsGivewayPermissionOn();
            Integer num = 0;
            //判断设备号不为“”，null而且不为未获取状态下查询注册数量--即设备号为真实的代码数值
            if (org.apache.commons.lang3.StringUtils.isNotBlank(clientId) && !("未获取").equals(clientId)) {
                //获取同一台设备下注册的车/货的数量
                num = tytUserSubService.getCountNum(clientPort, clientId);
            }
            //限制同一个设备新注册用户权益数量
            Integer edNum = tytConfigService.getIntValue("equityDistribution");
            Integer count = userCancelService.getCellPhone(user.getCellPhone());
            if (bindStatus == null || (bindStatus != null && bindStatus.intValue() == 1)) {
                //if (!hasBinded) {
                tytUserSubService.updateBindStatus(clientId, user.getId(), 2);
                if (count == 0) {
                    //如果手机号未注销过，判断同一个设备号注册数量是否小于规定数量或者获取设备号为“”，null或者设备号为未获取情况下进行下发
                    if (num < edNum || org.apache.commons.lang3.StringUtils.isBlank(clientId) || ("未获取").equals(clientId)) {
                        if (userGroup != null) {
                            // 绑定成功之后设置对应的需要提示的消息
                            if (userGroup == Constant.RIGHTS_EXPERIENCE) {
                                if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
                                    // 插入身份认证未成功的提醒
                                    logger.info("rebindDevice userId: " + userId + ", saveRightsInfo");
                                    //弹窗
//									if (clientPort == 1) {
//										// 新注册用户注册完成后，客户端会自动调用login方法再进行登录，车方赠送权益时机进行调整后，会造成两次弹窗
//										// 解决方案，移除注册时插入弹窗的数据，仅车版app
////										noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app未实名认证, userId, clientPort);
//									} else
                                    if (clientPort == 2) {
                                        if (activityOnOff) {
                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app未实名认证New, userId, clientPort);
                                        } else {
                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app未实名认证, userId, clientPort);
                                        }
                                    }
//									else {
//										noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆app未身份认证, userId, clientPort);
//									}
                                } else {
                                    // 5930 弹窗
//									if (clientPort == 1) {
////										noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app实名认证通过, userId, clientPort);
////										userPermissionService.giveExperienceMember(user, clientPort); // 发权益
//									} else
                                    if (clientPort == 2) {
                                        if (activityOnOff) {
                                            noticePopupService.savePopup(PopupTypeEnum.每月赠送发货权益, userId, clientPort);
                                        } else {
                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app实名认证通过, userId, clientPort);
                                        }
                                    }
//									else {
//										noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆app身份认证通过, userId, clientPort);
//									}
                                }
                                //未获取到设备号的账号，直接发送权益，不受数量限制
                                if (clientPort != 1) {
                                    // 5930 新权益， 发放体验权益
                                    userPermissionService.giveExperienceMember(user, clientPort); // 发权益
                                }

                            }
                            if (userGroup == Constant.RIGHTS_EXPERIENCE || userGroup == Constant.RIGHTS_EXPERIENCE_TIMEOUT) {
                                //给用户发放车试用的权益开关
                                int onoff = tytConfigService.getIntValue("car_auth_permission_onoff", 1);
                                if (onoff == 1) {
                                    //业务调整需求为符合条件用户每日发放3 + 2权益，不赠送试用会员，通过开关控制
                                    if ("1".equals(user.getIsCar())) {
                                        // 试⽤会员，车辆认证审核通过后的提醒
                                        noticeRemindService.saveRightsInfo(Constant.INDEX_CAR_AUTH_TYPE1,
                                                Constant.INDEX_CAR_AUTH_TYPE2,
                                                Constant.INDEX_CAR_AUTH_CONTENT,
                                                tytUserSub.getUserId());
                                        // 5930版本弹窗
                                        PopupSaveBean saveBean = new PopupSaveBean();
                                        saveBean.setPopupTypeEnum(PopupTypeEnum.身份为试用后首次登录app);
                                        saveBean.setReceiveId(userId);
                                        saveBean.setOriginPopup(1);
                                        saveBean.setCarPopup(1);
                                        noticePopupService.savePopup(saveBean); // 弹窗
                                        // 发放试用会员权益
                                        userPermissionService.giveTrialMember(user);
                                        //修改用户状态为试用
                                        tytUserSubService.updateUserGroup(userId, Constant.RIGHTS_NORMAL);
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                //如果已绑定，判断身份是否为体验
                if (userGroup != null && userGroup == Constant.RIGHTS_EXPERIENCE) {
                    if (org.apache.commons.lang3.StringUtils.isBlank(tytUserSub.getBindCliendid()) || ("未获取").equals(tytUserSub.getBindCliendid())) {
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(clientId) && !("未获取").equals(clientId)) {
                            tytUserSubService.updateBindStatus(clientId, user.getId(), 2);
                        }
                    }
                    if (count == 0) {
                        //如果手机号未注销过，判断同一个设备号注册数量是否小于规定数量或者获取设备号为“”，null或者设备号为未获取情况下进行下发
                        if (num < edNum || org.apache.commons.lang3.StringUtils.isBlank(clientId) || ("未获取").equals(clientId)) {
                            //判断用户是否赠送过老权益
                            boolean isGivingRight = userBuyGoodsService.isGivingRight(userId, GoodsType.体验会员.getId());
                            if (!isGivingRight && (clientPort == 1 || clientPort == 2)) {
                                boolean isGivingRightGoods = userBuyGoodsService.isGivingRightForGoods(userId);
                                boolean isGivingRightCar = userBuyGoodsService.isGivingRight(userId, GoodsType.车体验.getId());
                                if (clientPort == 1) {
                                    if (!isGivingRightCar) {
                                        if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app未实名认证, userId, clientPort);
                                        } else {
                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app实名认证通过, userId, clientPort);
                                            userPermissionService.giveExperienceMember(user, clientPort); // 发权益
                                        }
                                    }
                                } else {
                                    if (isGivingRightCar && !isGivingRightGoods) {
                                        if (activityOnOff) {
                                            if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
                                                noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app未实名认证New, userId, clientPort);
                                            } else {
                                                noticePopupService.savePopup(PopupTypeEnum.每月赠送发货权益, userId, clientPort);
                                            }
                                        } else {
                                            if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
                                                noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app未实名认证, userId, clientPort);
                                            } else {
                                                noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app实名认证通过, userId, clientPort);
                                            }
                                        }
                                        userPermissionService.giveExperienceMember(user, clientPort); // 发权益

                                    }
                                }
                            }
                        }
                    }
                }
                // 如果已经绑定绑定身份是试用会员，但是还没有开始使用，则设置开始时间
                if (userGroup != null && userGroup == Constant.RIGHTS_NORMAL
                        && tytUserSub.getLevel2BigingTime() == null) {
                    tytUserSubService.updateLevelBeginTime(userId, new Date());
                }
            }
        }
    }

    /**
     * 用户模拟登陆接口
     *
     * @param request
     * @param response
     */
    @Deprecated
    @RequestMapping(value = "/oldSimulatedLogin")
    public void simulatedLogin(HttpServletRequest request, HttpServletResponse response) {
        long startMillis = System.currentTimeMillis();
        try {
            /* 解析参数 */
            Map<String, String> params = parseRequestParams(request);

            Long userId = Long.parseLong(params.get("userId"));
            String clientSign = params.get("clientSign");
            String userVersion = params.get("clientVersion");
            String clientId = params.get("clientId");
            String condition = "userId_" + userId + "用户模拟登陆 ";
            String channelCode = params.get("channelCode");// 区分IOS是企业版还是个人版
            if (Integer.parseInt(userVersion) < 6000) {
                backResponse(request, response, ReturnCodeConstant.ERROR, LOW_VERSION_NOTICE, null, 0);
                return;
            }
            Integer versionControl = tytConfigService.getIntValue(Constant.LOGIN_VERSION_CONTROL, 1000);
            if (Integer.parseInt(userVersion) < versionControl) {
                backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, "系统升级中", null, 0);
                return;
            }
            /* 判断用户是否属于强制升级的用户 */
            Map<String, Object> upgradeStrategyMap = tytForceUpgradeUserService.getForceUpgradeVersionInfo(1, null, userId, clientId, channelCode, clientSign, userVersion);
            if (upgradeStrategyMap != null && "1".equals(upgradeStrategyMap.get("type"))) {
                logger.info(condition + "软件版本低,属于强制升级的用户");
                backResponse(request, response, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", upgradeStrategyMap, 0);
                return;
            }
            /* 判断版本号 */
            Map<String, Object> versionResult = versionBusiness.checkVersion(true, params);
            if (versionResult != null) {
                String type = (String) versionResult.get("type");
                if (type.equals("1")) {
                    // 20161114模拟登陆接口由于《版本低》提示参数，客户端没有做处理，故改成踢出用户，让其重新登录，因为登陆接口有相应的提示
                    logger.info(condition + "软件版本低,暂时把用户踢出去");
                    String objTicketKey = UserTicketUtil.getObjTicketKey(userId.toString());
                    cacheService.setObject(objTicketKey, "0000");
                    backResponse(request, response, ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG, versionResult, 0);
                    return;
                }
            }
            /* 根据id查询用户 */
            User user = userService.getByUserId(userId);
            //登录活动发mq
            //20190428增加5月优惠券活动 此次活动时间20190506-20190531 仅限大于5910版本
            loginActivityService.sendActivityMq(user.getId(), userVersion);
            /* 记录日志，响应客户端 */
            channelLog(request, user, ChannelLogConstant.simulatedLogin, params);
            UserBean userBean = commonLogin(request, user, params, upgradeStrategyMap, clientId, "1");
            logger.info(condition + "old用户登录成功...... {}  use {}", user.getId(), System.currentTimeMillis() - startMillis);
            backResponse(request, response, ReturnCodeConstant.OK, "登陆成功", userBean, 0);
            logger.info(condition + "成功......");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 用户模拟登陆接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/simulatedLogin")
    public void newSimulatedLogin(HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, String> params = parseRequestParams(request);

            if (!userService.versionLoginAllow(params)) {
                this.backResponse(request, response, ReturnCodeConstant.ERROR, LOW_VERSION_NOTICE, null, 0);
                return;
            }

            TytNoticePopupTempl noticePopupTempl = userService.checkVersionLogin(request, params);
            if (noticePopupTempl != null) {
                this.backResponseAddNotice(request, response, ReturnCodeConstant.NO_PERMISSION, "当前版本过低，请升级", null, 0, noticePopupTempl);
                return;
            }
            String clientVersion = params.get("clientVersion");
            String clientSign = params.get("clientSign");
            if ("21".equals(clientSign) || "22".equals(clientSign) || Integer.parseInt(clientVersion) > 6349) {
                boolean passed = this.loginCheckForUpgrade(params, request, response);
                if (!passed) {
                    return;
                }
            }
            ResultMsgBean resultMsgBean = userService.newSimulatedLogin(request, params);
            backResponse(request, response, resultMsgBean.getCode(), resultMsgBean.getMsg(), resultMsgBean.getData(), 0);
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }


    /**
     * 根据登录的clientSign判断是否刷新ticket
     *
     * @param userId
     * @param clientSign
     * @param isSimulatedLogin 判断是否模拟登陆0不是1是
     * @return
     */
    private String getTicket(long userId, int clientSign, String isSimulatedLogin) {
        String ticket = Encoder.md5("" + userId + System.currentTimeMillis());

        // 针对clientSign缓存的ticket
        String carTicketKey = UserTicketUtil.getCarTicketKey(String.valueOf(userId));
        String goodsTicketKey = UserTicketUtil.getGoodsTicketKey(String.valueOf(userId));
        String objTicketKey = UserTicketUtil.getObjTicketKey(String.valueOf(userId));
        String miniProgramTicketKey = UserTicketUtil.getMiniProgramGoodsKey(String.valueOf(userId));
        String miniProgramTicketTruckerKey = UserTicketUtil.getMiniProgramTruckerKey(String.valueOf(userId));

        // 过期时间
        long expire = Long.parseLong(AppConfig.getProperty("tyt.ticket.out.time.hour")) * 60 * 60;

        if (clientSign <= CLIENT_SIGN_SEPERATOR) {
			/*
			if ("1".equals(isSimulatedLogin)){
				setOldTicket(objTicketKey,"1");
			}
			*/
            cacheService.setString(carTicketKey, System.currentTimeMillis() + "");
            cacheService.setString(goodsTicketKey, System.currentTimeMillis() + "");
            cacheService.setObject(objTicketKey, ticket, expire);
        }

        if (Constant.ClientSignEnum.ANDROID_CAR.code == clientSign || Constant.ClientSignEnum.IOS_CAR.code == clientSign) {
			/*
			if ("1".equals(isSimulatedLogin)){
				setOldTicket(carTicketKey,"0");
			}
			*/
            cacheService.setString(carTicketKey, ticket, expire);
            cacheService.setObject(objTicketKey, System.currentTimeMillis() + "");
        }

        if (Constant.ClientSignEnum.ANDROID_GOODS.code == clientSign || Constant.ClientSignEnum.IOS_GOODS.code == clientSign
                || Constant.ClientSignEnum.WEB_GOODS.code == clientSign) {
			/*
			if ("1".equals(isSimulatedLogin)){
				setOldTicket(goodsTicketKey,"0");
			}
			*/
            cacheService.setString(goodsTicketKey, ticket, expire);
            cacheService.setObject(objTicketKey, System.currentTimeMillis() + "");
        }

        if (Constant.ClientSignEnum.MINI_PROGRAM_GOODS.code == clientSign) {
			/*
			if ("1".equals(isSimulatedLogin)){
				setOldTicket(miniProgramTicketKey,"0");
			}
			*/
            cacheService.setString(miniProgramTicketKey, ticket, expire);
        }

        if (Constant.ClientSignEnum.MINI_PROGRAM_TRUCKER.code == clientSign) {
            if ("1".equals(isSimulatedLogin)) {
                setOldTicket(miniProgramTicketTruckerKey, "0");
            }
            cacheService.setString(miniProgramTicketTruckerKey, ticket, expire);
        }

        return ticket;
    }

    /**
     * 保存旧的ticket
     *
     * @param userKey
     * @param isOldKey 是否为老版本key0不是1是
     */
    private void setOldTicket(String userKey, String isOldKey) {
        // 过期时间
        long expire = Long.parseLong(Constant.TYT_OLD_TICKET_OUT_TIME_MIN) * 60;
        if ("0".equals(isOldKey)) {
            String oldTicket = cacheService.getString(userKey);
            logger.info("old ticket :{}", oldTicket);
            if (StringUtils.isNotEmpty(oldTicket)) {
                cacheService.setString(Constant.CACHE_OLD_TICKET_KEY + userKey, oldTicket, expire);
                logger.info("保存老的ticket成功");
                logger.info("保存老的ticket成功 key:{}", Constant.CACHE_OLD_TICKET_KEY + userKey);
            }
        } else {
            Object oldTicket = cacheService.getObject(userKey);
            if (oldTicket != null) {
                cacheService.setObject(Constant.CACHE_OLD_TICKET_KEY + userKey, oldTicket, expire);
                logger.info("保存老的ticket成功");
                logger.info("保存老的ticket成功 key:{}", Constant.CACHE_OLD_TICKET_KEY + userKey);
            }
        }
    }

    /**
     * 公共登陆方法
     *
     * @param user
     * @param clientId
     * @return
     * @throws Exception
     */
    private UserBean commonLogin(HttpServletRequest request, User user, Map<String, String> params, Map<String, Object> upgradeStrategyMap, String clientId, String isSimulatedLogin) throws Exception {

        /* 删除缓存 */
        String userSubKey = Constant.CACHE_USERSUB_KEY + user.getId() + "_" + TimeUtil.formatDateMonthTime(new Date());
        cacheService.del(userSubKey);
        // btt5910修改 登录和模拟登录
        /* 生成ticket */
        int clientSign = Integer.parseInt(params.get("clientSign"));
        logger.info("-----------------登录clientSign:" + clientSign + "----------------");
        rebindDevice(clientId, user, clientSign);
        String ticket = this.getTicket(user.getId(), clientSign, isSimulatedLogin);
        /* 更新用户信息 */
        userService.updateLastInfo(user.getId(), ticket, Integer.parseInt(params.get("clientSign")), params.get("clientVersion"), params.get("osVersion"));
        tytUserSubService.savePushCid(user.getId(), params.get("cid"), Integer.parseInt(params.get("clientSign")), params.get("clientVersion"), params.get("osVersion"), params.get("deviceId"), params.get("carDeviceId"), params.get("goodsDeviceId"));
        /*更新升级用户的状态是否完成*/
        if (upgradeStrategyMap != null && upgradeStrategyMap.get("isUpdate") != null && "1".equals(upgradeStrategyMap.get("isUpdate").toString()) &&
                upgradeStrategyMap.get("updateVersion") != null && !"".equals(upgradeStrategyMap.get("updateVersion").toString()) &&
                upgradeStrategyMap.get("taskId") != null && !"".equals(upgradeStrategyMap.get("taskId").toString())) {

            tytForceUpgradeUserService.updateInfo(user.getId(), upgradeStrategyMap.get("updateVersion"), upgradeStrategyMap.get("taskId"));
        }
        /* 获取最新的用户信息 */
        user = userService.getById(user.getId());
        User users = new User();
        BeanUtils.copyProperties(user, users);
        users.setTicket(ticket);
        /* user信息缓存 */
        cacheService.setObject(Constant.CACHE_USER_KEY + users.getId(), users, AppConfig.getIntProperty("tyt.cache.user.time"));

        /* 生成响应数据userBean */
        UserBean userBean = createUserResponse(users);

        // 根据clientSign返回对应客户端的昵称（用户名 - 车/货）
        if (clientSign == Constant.ClientSignEnum.ANDROID_CAR.code || clientSign == Constant.ClientSignEnum.IOS_CAR.code) {
            if (StringUtils.isNotBlank(user.getCarUserName())) {
                userBean.setUserName(users.getCarUserName());
            }
        }

        //用户定向升级，如果为提示升级则返回以下几个参数
        if (upgradeStrategyMap != null && upgradeStrategyMap.get("type") != null) {
            userBean.setIsUpdate(2); //是否需要升级 1:不需要升级   2：需要升级
            userBean.setGoalVersion(String.valueOf(upgradeStrategyMap.get("version")));
            userBean.setContent(String.valueOf(upgradeStrategyMap.get("contentNew")));
            userBean.setUrl(String.valueOf(upgradeStrategyMap.get("url")));
            userBean.setUpgradeMode(String.valueOf(upgradeStrategyMap.get("type"))); // 0：提示升级   1：强制升级
        } else {
            userBean.setIsUpdate(1); //是否需要升级 1或者其他:不需要升级   2：需要升级
        }

        /*
         * 获取用户的认证信息
         */
        fetchUserIdentity(users, userBean);
        //serveDays不准确，根据endTime计算后返回，兼容老版本，精准货源推荐新版本忽略本逻辑
        if (userBean != null && userBean.getEndTime() != null) {
            int days = TimeUtil.getDays(TimeUtil.formatDate(new Date()), TimeUtil.formatDate(userBean.getEndTime()));
            userBean.setServeDays(days);
            if (days < 0) {
                userBean.setServeDays(0);
            }
        }
        //精准货源推荐  更新用户的在线心跳时间
        if (user != null && user.getId() != null && user.getId().longValue() > 0) {
            preferService.updateUserPreferOnlineTime(user.getId());
        }

        /* 记录日志 */
        createLog(request, users, OpLog.OP_CLIENT_LOGIN, params);
        //app我的发货菜单状态
        TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(user.getId());
        if (tytUserSub != null) {
            userBean.setMyGoodsMenu(tytUserSub.getMyGoodsMenu() == null ? 2 : tytUserSub.getMyGoodsMenu());
            // 用户时候设置了钱包密码信息
            userBean.setPwdStatus(tytUserSub.getPocketPwdStatus());
            if (tytUserSub.getUserGroup() != null) {
                userBean.setUserLevelStatus(tytUserSub.getUserGroup());
            }
            userBean.setNonVipContent(Constant.NON_VIP_CONTENT);
            userBean.setCarVerifyFlag("1".equals(user.getIsCar()) ? 1 : 0);
            if (tytUserSub.getUserGroup() != null && tytUserSub.getUserGroup() == Constant.RIGHTS_NORMAL) { // 试用会员
                if (tytUserSub.getLevel2BigingTime() != null) {
                    int passedDays = TimeUtil.daysBetween(tytUserSub.getLevel2BigingTime(), new Date());
                    int allDays = tytConfigService.getIntValue("rights_normal_call_day", 20);
                    int remainDays = allDays - passedDays;
                    userBean.setLevel2RemainDays(remainDays > 0 ? remainDays : 1);
                }
            }
            int couponNum = couponService.getValidRemainQtyByUserId(user.getId(), clientSign);
            userBean.setCouponNum(couponNum);
            userBean.setBindStatus(tytUserSub.getBindStatus());
        } else {
            userBean.setMyGoodsMenu(2);
        }
        //登录更新用户权益缓存
        String permissionKey = Constant.USER_PERMISSION_REDIS_KEY + user.getId();
        RedisUtil.del(permissionKey);
        UserPermissionResult result = new UserPermissionResult();
        PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.用户昵称显示, user.getId());
        if (permissionResult.getUse()) {
            result.setIsUserNamePower(1);
        }
        PermissionResult contentPermissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货物内容显示, user.getId());
        if (contentPermissionResult.getUse()) {
            result.setIsContentPower(1);
        }
        RedisUtil.setObject(permissionKey, result, 0);

        // 同步corp企业服务token
        CorpRestClient.getInstance().setCorpHeads(user.getId());

        //新版货主身份标签弹窗
        userService.checkGoodsTypePopup(clientSign, userBean);

        return userBean;
    }

    private void fetchUserIdentity(User user, UserBean userBean) {
        userBean.setUserIdentityStatus(user.getVerifyPhotoSign());
        userBean.setIsVerifySales(2);
        TytUserIdentityAuth userIdentityAuth = userIdentityAuthService.getByUserId(user.getId() + "");
        if (userIdentityAuth != null) {
            if (userIdentityAuth.getExamineStatus() == null || userIdentityAuth.getExamineStatus().intValue() == 0 || userIdentityAuth.getExamineStatus().intValue() == 1) {
                userBean.setIsVerifySales(0);
            }
            userBean.setUserIdentityStatus(userIdentityAuth.getIdentityStatus());

            userBean.setIdCardValidDate(userIdentityAuth.getIdCardValidDate());
            userBean.setIdCardLongTerm(userIdentityAuth.getIdCardLongTerm());

            ValidDateFlagEnum validDateFlag = userVerifyService.getValidDateFlag(userIdentityAuth.getIdCardLongTerm(), userIdentityAuth.getIdCardValidDate());
            userBean.setValidDateFlag(validDateFlag.getCode());

        } else if (userIdentityAuth == null && user.getVerifyPhotoSign() == 3) {// 为了兼容老版本中认证失败而进行的处理，即老版本中的认证失败在新版本中体现为未认证
            userBean.setUserIdentityStatus(0);
        }

        TytInvoiceEnterprise verifyInfo = enterpriseDBService.getVerifyInfoByUserId(user.getId());
        if (verifyInfo == null) {
            userBean.setEnterpriseAuthStatus(0);
        } else {
            Integer infoVerifyStatus = verifyInfo.getInfoVerifyStatus();
            userBean.setEnterpriseAuthStatus((infoVerifyStatus == 3 || infoVerifyStatus == 4 || infoVerifyStatus == 5) ? 3 : infoVerifyStatus == 2 ? 1 : infoVerifyStatus == 1 ? 2 : 0);
        }

        //TODO 获取校验用户的销售审核状态 modify by tianjw on 2017-12-26
        if (user != null) {
            if (user.getDeliver_type_one() == null || user.getDeliver_type_one().equals("10") || user.getDeliver_type_one().equals("12") || user.getDeliver_type_one().equals("13")) {
                userBean.setIsVerifySales(1);
            }
            // 获取用户的销售审核一级身份名称开始（APP5300以上版本使用）
            String deliverTypeOne = user.getDeliver_type_one();
            fetchDeliverTypeOneName(userBean, deliverTypeOne);
        }
    }

    private void fetchDeliverTypeOneName(UserBean userBean, String deliverTypeOne) {
        if (StringUtils.isEmpty(deliverTypeOne) || Integer.valueOf(deliverTypeOne).intValue() == 13) {
            userBean.setDeliverTypeOneName("未确认");
            logger.info("用户一级审核身份(身份方法),DeliverTypeOneCode = {} ,ID = {}", deliverTypeOne, userBean.getUserName());
            userBean.setDeliverTypeOneCode("13");
        } else {
            TytSource source = null;
            List<TytSource> sourceList = sourceService.getList(" groupCode='user_deliver_type_one' AND value= " + deliverTypeOne + " AND status=0", new PageBean(1, 1));
            if (sourceList != null && sourceList.size() == 1) {
                source = sourceList.get(0);
            }
            if (source != null) {
                String sourceName = source.getName();
                if (StringUtils.isNotEmpty(sourceName)) {
                    sourceName = sourceName.startsWith("运输公司") ? "运输公司或车队" : sourceName;
                }
                userBean.setDeliverTypeOneCode(deliverTypeOne);
                userBean.setDeliverTypeOneName(sourceName);
            }
        }
    }

    @PostMapping("/bindPhone")
    @ResponseBody
    public ResultMsgBean bindPhone(HttpServletRequest request, BindPhoneBean bindPhoneBean) throws Exception {
        if (StringUtils.isBlank(bindPhoneBean.getVerificationCode())) {
            return new ResultMsgBean(ResultMsgBean.ERROR, "请输入验证码");
        }
        String redisKey = Constant.SMS_VERIFYCODE_PREFFIX + bindPhoneBean.getPhone();
        String realVerifyCode = RedisUtil.get(redisKey);
        if (!Objects.equals(realVerifyCode, bindPhoneBean.getVerificationCode())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "短信验证码错误");
        }
        User user = userService.getUserByCellphone(bindPhoneBean.getPhone());
        BindPhoneResBean bindPhoneResBean = new BindPhoneResBean();
        BaseWxUserInfo baseWxUserInfo = new BaseWxUserInfo();
        // 车主小程序
        if (bindPhoneBean.getType() != null && bindPhoneBean.getType() == 2) {
            TytCarWxUserInfo tytCarWxUserInfo = tytCarWxUserInfoService.getTytCarWxUserInfoByOpenId(bindPhoneBean.getOpenId());
            if (tytCarWxUserInfo == null || tytCarWxUserInfo.getUserId() != null) {
                return new ResultMsgBean(ReturnCodeConstant.BOUND_PHONE_CANT_CHANGE, "绑定手机号不可更改");
            }
            if (user != null) {
                TytCarWxUserInfo tytCarWxUserInfoByUserId = tytCarWxUserInfoService.getTytCarWxUserInfoByUserId(user.getId());
                if (tytCarWxUserInfoByUserId != null) {
                    return new ResultMsgBean(ReturnCodeConstant.PHONE_HAS_BEEN_BOUND, "该手机号已被绑定");
                }
            }
            BeanUtils.copyProperties(tytCarWxUserInfo, baseWxUserInfo);
        } else {
            //货主小程序
            TytWxUserInfo tytWxUserInfo = tytWxUserInfoService.getTytWxUserInfoByOpenId(bindPhoneBean.getOpenId());
            if (tytWxUserInfo == null || tytWxUserInfo.getUserId() != null) {
                return new ResultMsgBean(ReturnCodeConstant.BOUND_PHONE_CANT_CHANGE, "绑定手机号不可更改");
            }
            if (user != null) {
                TytWxUserInfo tytWxUserInfoByUserId = tytWxUserInfoService.getTytWxUserInfoByUserId(user.getId());
                if (tytWxUserInfoByUserId != null) {
                    return new ResultMsgBean(ReturnCodeConstant.PHONE_HAS_BEEN_BOUND, "该手机号已被绑定");
                }
            }
            BeanUtils.copyProperties(tytWxUserInfo, baseWxUserInfo);
        }

        Map<String, String> params = parseRequestParams(request);
        if (user != null) {
            baseWxUserInfo.setUserId(user.getId());
            baseWxUserInfo.setCellPhone(user.getCellPhone());
            if (bindPhoneBean.getType() != null && bindPhoneBean.getType() == 2) {
                baseWxUserInfo.setUserName(user.getCarUserName());
            } else {
                baseWxUserInfo.setUserName(user.getUserName());
            }
            if (StringUtils.isBlank(user.getUserName())) {
                String clientSign = params.get("clientSign");
                userService.saveUserName(baseWxUserInfo.getWxNickName(), user.getId(), Integer.valueOf(clientSign));
            }
            if (StringUtils.isBlank(user.getHeadUrl())) {
                userService.saveHead(baseWxUserInfo.getAvatarUrl(), user.getId());
            }
            bindPhoneResBean.setIdentityStatus(user.getVerifyPhotoSign());
        } else {
//                //校验手机号是否注销过
//                ResultMsgBean result = userService.getUserIsCancel(bindPhoneBean.getPhone());
//                if (result.getCode()!=  ReturnCodeConstant.OK){
//                    return result;
//                }
            params.put("cellPhone", bindPhoneBean.getPhone());
            String appletsLoginPassword = tytConfigService
                    .getStringValue(Constant.APPLETS_LOGIN_PASSWORD, "42f749c840acea865fbe927947eea7ae");
            params.put("password", appletsLoginPassword);
            user = regUser(params);
            if (StringUtils.isNotBlank(baseWxUserInfo.getWxNickName()) && !isHasEmoji(baseWxUserInfo.getWxNickName())) {
                user.setUserName(baseWxUserInfo.getWxNickName());
                user.setCarUserName(baseWxUserInfo.getWxNickName());
            }
            user.setHeadUrl(baseWxUserInfo.getAvatarUrl());
            //register
            try {
                // 往tyt_cellphone添加手机号
                userService.addCellPhoneToTemp(bindPhoneBean.getPhone());
            } catch (Exception e) {
                return new ResultMsgBean(ReturnCodeConstant.BOUND_PHONE_CANT_CHANGE, "手机号已绑定");
            }
            UserBean newUser = createNewUser(request, params, null, user);
            baseWxUserInfo.setUserId(newUser.getId());
            baseWxUserInfo.setCellPhone(newUser.getCellPhone());
            baseWxUserInfo.setUserName(newUser.getUserName());
            bindPhoneResBean.setIdentityStatus(newUser.getVerifyPhotoSign());
        }
        if (bindPhoneBean.getType() != null && bindPhoneBean.getType() == 2) {
            tytCarWxUserInfoService.updateBindUser(baseWxUserInfo);
        } else {

            tytWxUserInfoService.updateBindUser(baseWxUserInfo);
        }
        RedisUtil.del(redisKey);
        ResultMsgBean result = new ResultMsgBean();
        bindPhoneResBean.setUserId(baseWxUserInfo.getUserId());
        bindPhoneResBean.setCellPhone(bindPhoneBean.getPhone());
        result.setData(bindPhoneResBean);
        return result;
    }


    /**
     * 判断字符串是否含有Emoji表情
     **/
    private boolean isHasEmoji(String reviewerName) {
        Pattern pattern = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]");
        Matcher matcher = pattern.matcher(reviewerName);
        return matcher.find();
    }


    /**
     * 用户注册接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/save", "/save.action"})
    public void save(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);

            //版本校验
            if (!userService.versionLoginAllow(params)) {
                this.backResponse(request, response, ReturnCodeConstant.ERROR, LOW_VERSION_NOTICE, null, 0);
                return;
            }

            /* 业务参数验证 */
            List<String> names = new ArrayList<>();
            names.add("cellPhone");
            names.add("password");
            names.add("userSign");

            /* 业务参数验证 */
            if (!validateArguments("用户注册", request, response, params, names)) {
                return;
            }

            /*
            TytNoticePopupTempl noticePopupTempl = userService.checkVersionLogin(request, params);
            if (noticePopupTempl != null) {
                this.backResponseAddNotice(request, response, ReturnCodeConstant.NO_PERMISSION, "当前版本过低，请升级", null, 0, noticePopupTempl);
                return;
            }
            boolean passed = this.loginCheckForUpgrade(params, request, response);
            if (!passed) {
                return;
            }
            */

            /* 取相关参数 */
            String cellPhone = params.get("cellPhone");
            Integer userSign = Integer.parseInt(params.get("userSign"));
            String condition = cellPhone;

            // 校验手机号格式
            if (!this.validateMobileNo(cellPhone, request, response)) {
                return;
            }

//			//校验手机号是否注销过
//            ResultMsgBean resultMsgBean = userService.getUserIsCancel(cellPhone);
//			if (resultMsgBean.getCode()!= ReturnCodeConstant.OK){
//                backResponse(request, response, resultMsgBean.getCode(), resultMsgBean.getMsg(), null, 0);
//                return;
//            }

            // 校验Web端验证码、以及两次输入密码
            if (!this.validateGoodsWebInput(params, request, response)) {
                return;
            }
            // 校验密码是否过于简单
            if (!this.validatePasswordSimply(params.get("password"), request, response)) {
                return;
            }
            // 校验短信验证码
            if (!this.validateVerifyCode(params, cellPhone, request, response)) {
                return;
            }

            /* 判断身份是否合法？ */
            if (!User.UserSignEnum.isUserSignEnumcode(userSign)) {
                logger.error(condition + "非法身份注册");
                backResponse(request, response, ReturnCodeConstant.REGISTER_ERROR_CODE, "非法身份注册", null, 0);
                return;
            }
            // 是否需要验证登录失败次数的标记
            boolean isNeedRestricLoginFail = false;
            String userVersion = params.get("clientVersion");
            try {
                // 获取需要进行登录错误次数控制的最小版本（不包括）
                String restriceLoginFailMinVersion = tytConfigService
                        .getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5300");
                int restriceLoginFailMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
                int clientVersionInt = Integer.valueOf(userVersion).intValue();
                isNeedRestricLoginFail = (clientVersionInt > restriceLoginFailMinVersionInt);
            } catch (Exception e) {
            }

            String clientSign = params.get("clientSign");
            String clientId = params.get("clientId");
            String channelCode = params.get("channelCode");// 区分IOS是企业版还是个人版

            /* 判断用户是否属于强制升级的用户 */
            Map<String, Object> upgradeStrategyMap = tytForceUpgradeUserService.getForceUpgradeVersionInfo(2, cellPhone, null, clientId, channelCode, clientSign, userVersion);
            if (upgradeStrategyMap != null && "1".equals(upgradeStrategyMap.get("type"))) {
                logger.info(condition + "软件版本低,属于强制升级的用户");
                this.backResponse(request, response, ReturnCodeConstant.VERSION_UPGADE_CODE, LOW_VERSION_NOTICE, upgradeStrategyMap, 0);
                return;
            }

            try {
                // 往tyt_cellphone添加手机号
                userService.addCellPhoneToTemp(cellPhone);
            } catch (Exception e) {
                // APP5300以上版本返回不同的验证码
                if (isNeedRestricLoginFail) {
                    backResponse(request, response, 10000, "手机号已经存在,您可以直接登录", null, 0);
                    return;
                } else {
                    logger.error(condition + "该身份的手机号已经存在");
                    backResponse(request, response, ReturnCodeConstant.REGISTER_ERROR_CODE, "手机号已经存在,您可以直接登录", null, 0);
                    return;
                }
            }

            // 手机号唯一性检验
            User oldUser = userService.getUserByCellphone(cellPhone);
            if (oldUser != null && oldUser.getIsMock() != IS_MOCK_USER) {
                // APP5300以上版本返回不同的验证码
                if (isNeedRestricLoginFail) {
                    backResponse(request, response, 10000, "手机号已经存在,您可以直接登录", null, 0);
                    return;
                } else {
                    logger.error(condition + "该身份的手机号已经存在");
                    backResponse(request, response, ReturnCodeConstant.REGISTER_ERROR_CODE,
                            "手机号已经存在,您可以直接登录", null, 0);
                    return;
                }
            }

            /* 生成用户实体 */
            User user = regUser(params);
            UserBean userBean = this.smsRegNewUser(request, params, upgradeStrategyMap, oldUser, user);
            userService.registerGetCallCount(userBean.getId());

            backResponse(request, response, ReturnCodeConstant.OK, "注册成功", userBean, 0);
            logger.info(condition + "用户注册成功......");
        } catch (Exception e) {
            logger.error("", e);
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }

    }

    /**
     * 重构用户注册登录.
     * @param request request
     * @param params params
     * @param upgradeStrategyMap upgradeStrategyMap
     * @param oldUser oldUser
     * @param user user
     * @return UserBean
     * @throws Exception e
     */
    private UserBean smsRegNewUser(HttpServletRequest request, Map<String, String> params, Map<String, Object> upgradeStrategyMap, User oldUser, User user) throws Exception {

        String clientId = params.get("clientId");

        userService.createNewUser(request, params, oldUser, user);

        //开通tpay_account账户
        createUserAccount(user);

        UserBean userBean = userService.newCommonLogin(request, user, params, upgradeStrategyMap, clientId, "0", 1);
        Integer isShowIdentityPop = userService.getIsShowIdentityPop(user);
        userBean.setNewUserFlag(1);
        userBean.setIsShowIdentityPop(isShowIdentityPop);

        return userBean;
    }

    @Deprecated
    private UserBean createNewUser(HttpServletRequest request, Map<String, String> params, User oldUser, User user) throws Exception {

        /*********************** abcedfg ************************/

        userService.createNewUser(request, params, oldUser, user);
        //开通tpay_account账户
        createUserAccount(user);
        /* 按正常登陆流程走，生成客户端数据 */
        return commonLogin(request, user, params, null, params.get("clientId"), "0");

    }

    private void createUserAccount(User user) {
        try {
            logger.info("用户注册开通tpay_account账户，userId:{},userPhone:{}", user.getId(), user.getCellPhone());
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("userId", user.getId());
            paramMap.put("userName", user.getUserName());
            paramMap.put("realName", user.getTrueName());
            paramMap.put("userPhone", user.getCellPhone());
            Integer interfaceMoveSwitch = tytConfigService.getIntValue("interface_move_switch", 0);
            logger.info("【接口迁移】开关:{}",interfaceMoveSwitch);
            String baseUrl=interfaceMoveSwitch == 1?tpayAccountUrl:userWeb;
            String result = HttpUtil.post(baseUrl + "/account/info/openAccount", JSON.toJSONString(paramMap));
            logger.info("用户注册开通tpay_account结果，baseUrl:{} result:{}",baseUrl, result);
            ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
            if (!resultMsgBean.getCode().equals(ResultMsgBean.OK)) {
                logger.error("用户注册开通tpay_account账户失败,原因:{},userId:{},userPhone:{}", resultMsgBean.getMsg(), user.getId(), user.getCellPhone());
            } else {
                logger.info("用户注册开通tpay_account账户成功，userId:{},userPhone:{}", user.getId(), user.getCellPhone());
            }
        } catch (Exception e) {
            logger.error("用户注册开通tpay_account账户异常,userId:{},userPhone:{},baseUrl:{}", user.getId(), user.getCellPhone(), tpayAccountUrl);
            e.printStackTrace();
        }
    }

    /**
     * 用户忘记密码接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/password/forget", "/password/forget.action"})
    public void passwordForget(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);


            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("cellPhone");
                    add("newPassword");
                }
            };
            String condition = params.get("cellPhone") + "密码修改 ";
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            /* 取相关参数 */
            String cellPhone = params.get("cellPhone");
            String newPassword = params.get("newPassword");

            // 校验手机号格式
            if (!this.validateMobileNo(cellPhone, request, response)) {
                return;
            }
            // 校验Web端验证码、以及两次输入密码
            if (!this.validateGoodsWebInput(params, request, response)) {
                return;
            }
            // 校验密码是否过于简单
            if (!this.validatePasswordSimply(newPassword, request, response)) {
                return;
            }
            // 校验短信验证码
            if (!this.validateVerifyCode(params, cellPhone, request, response)) {
                return;
            }

            /* 根据Id查询用户 */
            User oldUser = userService.getUserByCellphone(cellPhone);
            // 判断手机号存在否？
            if (oldUser == null || oldUser.getIsMock() == IS_MOCK_USER) {
                logger.error(condition + "手机号尚未注册");
                backResponse(request, response, ReturnCodeConstant.LOGIN_ERROR_CODE, "手机号尚未注册", null, 0);
                return;
            }
            /* 删除缓存信息 */
            cacheService.del(Constant.CACHE_USER_KEY + oldUser.getId());
            /* 修改用户信息 */
            userService.updatePassword(oldUser.getId(), newPassword);
            /* 缓存user */
            User user = userService.getById(oldUser.getId());
            cacheService.setObject(Constant.CACHE_USER_KEY + oldUser.getId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));
            // 日志记录
            createLog(request, oldUser, OpLog.OP_CLIENT_USER_UPDATE_PASSWORD, params);
            // 验证成功删除登录失败次数信息
            String todayDate = TimeUtil.formatDate(new Date());
            String redisKey = Constant.LOGIN_FAIL_TIME_REDIS_KEY + cellPhone + todayDate;
            RedisUtil.del(redisKey);
            // 验证失败清除验证码信息
            RedisUtil.del(Constant.LOGIN_VERIFY_CODE_REDIS_KEY + cellPhone);
            backResponse(request, response, ReturnCodeConstant.OK, "密码修改成功", null, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 单个用户信息查询接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/get", "/get.action"})
    public void get(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "单个信息查询 ";
            /* 根据Id查询用户 */
            User user = userService.getByUserId(userId);

            UserBean userBean = createUserResponse(user);

            Integer staffCount = tytUserStaffService.getStaffCountByUserId(userId);
            userBean.setStaffCount(staffCount);
            //serveDays不准确，根据endTime计算后返回，兼容老版本，精准货源推荐新版本忽略本逻辑
            if (userBean != null && userBean.getEndTime() != null) {
                int days = TimeUtil.getDays(TimeUtil.formatDate(new Date()), TimeUtil.formatDate(userBean.getEndTime()));
                userBean.setServeDays(days);
                if (days < 0) {
                    userBean.setServeDays(0);
                }
            }
            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
            if (userCreditInfo != null) {
                if (StringUtils.isNotBlank(userCreditInfo.getTransportScore())) {
                    userBean.setTransportScore(userCreditInfo.getTransportScore());
                }
                if (StringUtils.isNotBlank(userCreditInfo.getTsRank())) {
                    userBean.setTsRank(userCreditInfo.getTsRank());
                }
                if (null != userCreditInfo.getTotalScore()) {
                    userBean.setTotalScore(userCreditInfo.getTotalScore().setScale(0, RoundingMode.DOWN));
                }
                if (null != userCreditInfo.getRankLevel() && userCreditInfo.getRankLevel() > 0) {
                    userBean.setRankLevel(userCreditInfo.getRankLevel());
                } else {
                    userBean.setRankLevel(0);
                }
                if (null != userCreditInfo.getLastLevelGap() && userCreditInfo.getLastLevelGap() > 0) {
                    userBean.setLastLevelGap(userCreditInfo.getLastLevelGap());
                } else {
                    userBean.setLastLevelGap(0);
                }
                if (null != userCreditInfo.getCarTotalServerScore()) {
                    userBean.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
                }
                if (null != userCreditInfo.getCarServerRankScore()) {
                    userBean.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
                }
                userBean.setCarCreditRankLevel(StringUtils.isNotBlank(userCreditInfo.getCarCreditRankLevel()) ? userCreditInfo.getCarCreditRankLevel() : "1");
                userBean.setCarCreditRankLeveType(userCreditInfo.getCarCreditRankLeveType());
                userBean.setCarCreditScore(userCreditInfo.getCarCreditScore());
                userBean.setCarRankNum(userCreditInfo.getCarRankNum());
                userBean.setCarLastLevelGap(userCreditInfo.getCarLastLevelGap());

            } else {
                userBean.setRankLevel(0);
                userBean.setLastLevelGap(0);
                // 当数据部门没有同步信用数据时，默认给前端返回成长等级1
                userBean.setCarCreditRankLevel("1");
            }
            // 判断30天内是否有严重客诉
            int wrongCount = csComplaintRecordResultServcie.getCountByWrongReason(user);
            if (wrongCount > 0) {
                userBean.setCarCreditRankLevel("0");
            }
            //当用户参与货方信用配置 则返回相应信息
            CreditConfigInfo creditConfigUser = marketingActivityService.getUserExistCreditConfig(user.getId());
            if (creditConfigUser != null) {
                if (userCreditInfo != null && userCreditInfo.getGoodsCurOrderNum() != null) {
                    creditConfigUser.setGoodsCurOrderNum(userCreditInfo.getGoodsCurOrderNum());
                } else {
                    creditConfigUser.setGoodsCurOrderNum(0);
                }
                userBean.setCreditConfigInfo(creditConfigUser);
            }
            TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(user.getId());
            if (tytUserSub != null) {
                userBean.setMyGoodsMenu(tytUserSub.getMyGoodsMenu() == null ? 2 : tytUserSub.getMyGoodsMenu());
                //v6000新增用户是否设置了钱包密码信息
                userBean.setPwdStatus(tytUserSub.getPocketPwdStatus());
                if (tytUserSub.getUserGroup() != null) {
                    userBean.setUserLevelStatus(tytUserSub.getUserGroup());
                }
                userBean.setBindStatus(tytUserSub.getBindStatus());
            }
            userBean.setNonVipContent(Constant.NON_VIP_CONTENT);
            userBean.setCarVerifyFlag("1".equals(user.getIsCar()) ? 1 : 0);
            if (tytUserSub.getUserGroup() != null && tytUserSub.getUserGroup() == Constant.RIGHTS_NORMAL) { // 试用会员
                if (tytUserSub.getLevel2BigingTime() != null) {
                    int passedDays = TimeUtil.daysBetween(tytUserSub.getLevel2BigingTime(), new Date());
                    int allDays = tytConfigService.getIntValue("rights_normal_call_day", 20);
                    int remainDays = allDays - passedDays;
                    userBean.setLevel2RemainDays(remainDays > 0 ? remainDays : 1);
                }
            }
//            String clientSign = request.getParameter("clientSign");
            String clientSign = params.get("clientSign");

            int clientSignInt = Integer.parseInt(clientSign);

            int couponNum = couponService.getValidRemainQtyByUserId(user.getId(), Integer.parseInt(clientSign));
            userBean.setCouponNum(couponNum);
            // 根据不同的clientSign
            String ticket = null;
            if (Integer.parseInt(clientSign) <= CLIENT_SIGN_SEPERATOR || Constant.ClientSignEnum.PC.code == Integer.parseInt(clientSign)) {
                String objTicketKey = UserTicketUtil.getObjTicketKey(userId.toString());
                ticket = (String) cacheService.getObject(objTicketKey);
            }
            if (Constant.ClientSignEnum.ANDROID_CAR.code == Integer.parseInt(clientSign) || Constant.ClientSignEnum.IOS_CAR.code == Integer.parseInt(clientSign)) {
                String carTicketKey = UserTicketUtil.getCarTicketKey(userId.toString());
                ticket = cacheService.getString(carTicketKey);
                if (StringUtils.isNotBlank(user.getCarUserName())) {
                    userBean.setUserName(user.getCarUserName());
                }
            }
            if (Constant.ClientSignEnum.ANDROID_GOODS.code == Integer.parseInt(clientSign) || Constant.ClientSignEnum.IOS_GOODS.code == Integer.parseInt(clientSign)
                    || Constant.ClientSignEnum.WEB_GOODS.code == Integer.parseInt(clientSign)) {
                String goodsTicketKey = UserTicketUtil.getGoodsTicketKey(userId.toString());
                ticket = cacheService.getString(goodsTicketKey);
            }

            if (Constant.ClientSignEnum.MINI_PROGRAM_GOODS.code == Integer.parseInt(clientSign)) {
                String miniProgramGoodsKey = UserTicketUtil.getMiniProgramGoodsKey(userId.toString());
                ticket = cacheService.getString(miniProgramGoodsKey);
            }
            if (Constant.ClientSignEnum.MINI_PROGRAM_TRUCKER.code == Integer.parseInt(clientSign)) {
                String miniProgramTruckerKey = UserTicketUtil.getMiniProgramTruckerKey(userId.toString());
                ticket = cacheService.getString(miniProgramTruckerKey);
            }

            int number = tytCarDriverArchivesService.countCarDriverNumber(userId);
            userBean.setCarDriverNumber(number);

            userBean.setTicket(ticket);
            userBean.setDomain(tytConfigService.getStringValue("tyt_server_picture_url_old"));

            //新版货主身份标签弹窗
            userService.checkGoodsTypePopup(clientSignInt, userBean);

            //待评价订单数量
            int userType = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
            Integer feedBackTodoCount = feedbackUserService.getUserFeedbackTodoCount(userId, userType);
            userBean.setFeedBackTodoCount(feedBackTodoCount);

            backResponse(request, response, ReturnCodeConstant.OK, "查询信息成功", userBean, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 获取货主权益相关信息
     *
     * @param userId 用户Id
     */
    @RequestMapping(value = "/getCargoOwnerPermission")
    @ResponseBody
    public ResultMsgBean getCargoOwnerPermission(@RequestParam(name = "userId") Long userId) {
        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse();
        try {
            //参数解析
            if (userId == null || userId.intValue() <= 0) {
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }
            // 根据Id查询用户 货会员 货发货次数 相关权益
            List<UserPermissionResponseBean> userPermissions = userPermissionService.getGoodsPermission(userId);
            makeUserPermissionsExcellentGoodsCardNum(userPermissions, userId);
            Map<String, UserPermissionResponseBean> userPermissionMap = userPermissions.stream().collect(Collectors.toMap(UserPermissionResponseBean::getServicePermissionTypeId, a -> a, (k1, k2) -> k1));
            //新货会员兼容发货权益
            if (userPermissionMap.containsKey(UserPermissionTypeEnum.GOODS_NUM_NEW.getTypeId())
                    && userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM_NEW.getTypeId()).getStatus() == 1
                    && userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM_NEW.getTypeId()).getRemainNum() > 0) {

                if (userPermissionMap.containsKey(UserPermissionTypeEnum.GOODS_NUM.getTypeId())
                        && userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM.getTypeId()).getStatus() == 1
                        && userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM.getTypeId()).getRemainNum() > 0) {
                    Integer numRemainNum = userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM.getTypeId()).getRemainNum();
                    Integer numNewRemainNum = userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM_NEW.getTypeId()).getRemainNum();
                    Date numEndTime = userPermissionMap.get(GOODS_NUM.getTypeId()).getEndTime();
                    Date numNewEndTime = userPermissionMap.get(GOODS_NUM_NEW.getTypeId()).getEndTime();
                    userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM.getTypeId()).setRemainNum(numRemainNum + numNewRemainNum);
                    userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM.getTypeId()).setEndTime(numNewEndTime.after(numEndTime) ? numNewEndTime : numEndTime);
                } else {
                    userPermissionMap.get(UserPermissionTypeEnum.GOODS_NUM_NEW.getTypeId()).setServicePermissionTypeId(UserPermissionTypeEnum.GOODS_NUM.getTypeId());
                }
            }
            userPermissions = new ArrayList<>(userPermissionMap.values());
            //特殊处理权益到期时间为23:59:59
            for (UserPermissionResponseBean userPermission : userPermissions) {
                userPermission.setEndTime(TimeUtil.weeHours(userPermission.getEndTime(), 1));
            }
            resultMsgBean.setData(userPermissions);
            logger.info("货主:【{}】相关权益查询成功,权益信息:【{}】", userId, JSON.toJSONString(userPermissions));
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取货主权益相关信息异常:", e);
            return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
        }
    }

    /**
     * 获取车主权益相关信息
     *
     * @param userId 用户Id
     */
    @RequestMapping(value = "/getCarOwnerPermission")
    @ResponseBody
    public ResultMsgBean getCarOwnerPermission(@RequestParam(name = "userId") Long userId) {
        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse();
        try {
            //参数解析
            if (userId == null || userId.intValue() <= 0) {
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }
            // 根据Id查询用户 车会员 车拨打次数 相关权益
            List<UserPermissionResponseBean> userPermissions = userPermissionService.getUserOwnerPermission(userId, UserPermissionTypeEnum.CAR_NUM.getTypeId(), UserPermissionTypeEnum.CAR_VIP.getTypeId());
            //特殊处理权益到期时间为23:59:59
            for (UserPermissionResponseBean userPermission : userPermissions) {
                userPermission.setEndTime(TimeUtil.weeHours(userPermission.getEndTime(), 1));
            }
            resultMsgBean.setData(userPermissions);
            logger.info("车主:【{}】相关权益查询成功,权益信息:【{}】", userId, JSON.toJSONString(userPermissions));
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取车主权益相关信息异常:", e);
            return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
        }
    }

    private void makeUserPermissionsExcellentGoodsCardNum(List<UserPermissionResponseBean> userPermissions, Long userId) {
        if (userPermissions == null) {
            userPermissions = new ArrayList<>();
        }
        int canUseExcellentGoodsCardNumTotla = excellentGoodsService.getAllCanUseCardCountNumByUserId(userId);
        if (canUseExcellentGoodsCardNumTotla <= 0) {
            return;
        }
        boolean isHaveGoodsPermissions = userPermissions.stream().anyMatch(userPermission -> userPermission.getServicePermissionTypeId() != null && userPermission.getServicePermissionTypeId().equals(UserPermissionTypeEnum.GOODS_NUM.getTypeId()));
        if (isHaveGoodsPermissions) {
            for (UserPermissionResponseBean userPermission : userPermissions) {
                if (userPermission.getServicePermissionTypeId() != null && userPermission.getServicePermissionTypeId().equals(UserPermissionTypeEnum.GOODS_NUM.getTypeId())) {
                    userPermission.setExcellentCardNum(canUseExcellentGoodsCardNumTotla);
                    break;
                }
            }
        }
        UserPermissionResponseBean userPermissionResponseBean = new UserPermissionResponseBean();
        userPermissionResponseBean.setUserId(userId);
        userPermissionResponseBean.setRemainNum(0);
        userPermissionResponseBean.setSystemTime(new Date());
        userPermissionResponseBean.setEndTime(new Date());
        userPermissionResponseBean.setServicePermissionTypeId(UserPermissionTypeEnum.EXCELLENT_GOODS_NUM.getTypeId());
        userPermissionResponseBean.setStatus(1);
        userPermissionResponseBean.setExcellentCardNum(canUseExcellentGoodsCardNumTotla);
        userPermissions.add(userPermissionResponseBean);
    }


    @RequestMapping(value = {"updateUserJoinStatus", "updateUserJoinStatus.action"})
    @ResponseBody
    public ResultMsgBean updateUserJoinStatus(Long configId, Long subId, Long userId) {
        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse();
        try {
            if (configId == null || subId == null || userId == null) {
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }
            //当用户参与货方信用配置 则返回相应信息
            marketingActivityService.updateUserConfigStatus(configId, subId, userId);
        } catch (Exception e) {
            logger.error(e.toString());
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }


    /**
     * 用户反馈接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/advice/save", "/advice/save.action"})
    public void saveAdvice(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "用户反馈 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("title");
                    add("content");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            /* 验证反馈类型码 */
            if (!Advice.AdviceTitleEnum.isAdviceTitleEnumcode(Integer.parseInt(params.get("title")))) {
                backResponse(request, response, ReturnCodeConstant.TYPE_ERROR_CODE, "反馈类型错误", null, 0);
                return;
            }
            adviceService.add(createAdvice(params));
            backResponse(request, response, ReturnCodeConstant.OK, "用户反馈成功", null, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            logger.error(e.toString());
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);

        }

    }

    /**
     * 意见建议提交
     */
    @PostMapping(value = "/v6280/advice/save")
    @ResponseBody
    public ResultMsgBean saveAdvice6280(BaseParameter baseParameter, String content, String picUrl, String typeStr) {
        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse();
        try {
            // 反馈类型
            if (StringUtils.isBlank(typeStr)) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("反馈类型参数错误");
                return resultMsgBean;
            }
            // 反馈类型
            int length = StringUtils.length(content);
            if (length < 5 || length > 500) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                resultMsgBean.setMsg("意见反馈内容5-200字");
                return resultMsgBean;
            }

            Advice advice = new Advice();
            // 根据userId提取用户手机号
            User user = userService.getByUserId(baseParameter.getUserId());
            advice.setCellPhone(user.getCellPhone());
            advice.setPlatId(Integer.parseInt(baseParameter.getClientSign()));
            advice.setVersion(baseParameter.getClientVersion());
            // 功能建议
            advice.setTitle(5);
            advice.setContent(content);
            advice.setPicUrl(picUrl);
            advice.setTypeStr(typeStr);
            advice.setStatus(Constant.ADVICE_STATUS_NO);
            advice.setCtime(new Timestamp(System.currentTimeMillis()));
            advice.setSource(6);
            advice.setcName(user.getId().toString());
            adviceService.add(advice);
        } catch (Exception e) {
            logger.error("", e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }

    /**
     * 6280获取意见建议类型接口
     */
    @GetMapping(value = "/v6280/advice/type")
    @ResponseBody
    public ResultMsgBean getAdviceTypes(BaseParameter baseParameter) {
        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse();
        try {
            String clientSign = baseParameter.getClientSign();
            if (StringUtils.isBlank(clientSign)) {
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("未知客户端");
                return resultMsgBean;
            }

            List<SourceBean> adviceTypes;
            int flag = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
            if (flag == 1) {
                // 车
                adviceTypes = sourceService.getByGroupCodeWithNoLimit("advice_type_car");
            } else if (flag == 2) {
                // 货
                adviceTypes = sourceService.getByGroupCodeWithNoLimit("advice_type_transport");
            } else {
                adviceTypes = Collections.emptyList();
            }

            adviceTypes = adviceTypes == null ? Collections.emptyList() : adviceTypes;
            List<AdviceTypeVO> types = adviceTypes.stream().map(sb -> {
                AdviceTypeVO adviceType = new AdviceTypeVO();
                adviceType.setTypeDesc(sb.getValue());
                return adviceType;
            }).collect(Collectors.toList());

            AdviceTypeBean adviceTypeBean = new AdviceTypeBean();
            adviceTypeBean.setTypes(types);

            resultMsgBean.setData(adviceTypeBean);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }

    /**
     * 推荐好友接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/recommend/save")
    public void recommendFriendSave(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "推荐好友 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("name");
                    add("tel");
                    add("userSign");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            /* 添加数据 */
            inviteFriendsService.add(createRecommendFriends(params));
            backResponse(request, response, ReturnCodeConstant.OK, "推荐好友成功", null, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 生成推荐人对象
     *
     * @param params
     * @return
     */
    private InviteFriends createRecommendFriends(Map<String, String> params) {
        InviteFriends friends = new InviteFriends();
        friends.setUserId(Long.parseLong(params.get("userId")));
        friends.setFriendName(params.get("name"));
        friends.setFriendCell(params.get("tel"));
        friends.setFriendType(Integer.parseInt(params.get("userSign")));
        friends.setRemark(params.get("remark"));
        friends.setPlatId(Integer.parseInt(params.get("clientSign")));
        friends.setCtime(new Timestamp(System.currentTimeMillis()));
        friends.setCounts(1);
        return friends;
    }

    /**
     * 联系人信息采集接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/linkman/save")
    public void linakmanSave(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "保存联系人 ";
            /* 取相关参数 */
            linkManService.add(createLinkman(params));
            backResponse(request, response, ReturnCodeConstant.OK, "1", null, 0);
            logger.info(condition + "联系人保存成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 用户实名认证采集接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/identity/save")
    @ResponseBody
    public void identitySave(HttpServletRequest request, HttpServletResponse response, @RequestParam MultipartFile mainPic) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "实名认证 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("identity");
                    add("realName");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            /* 判断照片是否上传？ */
            if (mainPic.isEmpty()) {
                logger.error(condition + "图片为空");
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "图片不能为空", null, 0);
                return;
            }
            if (userIdentityMainService.isExitForPhoto(userId)) {
                logger.error(condition + "重复录入");
                backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "您已经录入认证信息", null, 0);
                return;
            }

            if (!super.isAllImg(mainPic)) {
                logger.error(condition + ":图片格式错误！");
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "图片格式错误！", null, 0);
                return;
            }

            userIdentityMainService.saveUserIdentityMain(userId, params.get("identity"), params.get("realName"), mainPic);
            backResponse(request, response, ReturnCodeConstant.OK, "实名认证采集成功", null, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 用户实名认证信息查询
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/identity/get")
    public void identityGet(HttpServletRequest request, HttpServletResponse response) {

        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "实名认证查询 ";
            /* 查询信息 */
            UserIdentity identity = null;
            TytUserIdentityMain tytUserIdentityMain = userIdentityMainService.getTytUserIdentityMainForUserId(userId);
            if (tytUserIdentityMain != null) {
                identity = new UserIdentity();
                BeanUtils.copyProperties(tytUserIdentityMain, identity);
                identity.setMainurl(tytUserIdentityMain.getMainUrl());
                User user = userService.getByUserId(userId);
                identity.setStatus(String.valueOf(user.getVerifyFlag()));
                if (null == identity.getMainurl() || "".equals(identity.getMainurl())) {
                    identity.setMainurl("   ");
                }
            }
            backResponse(request, response, ReturnCodeConstant.OK, "实名认证信息查询成功", identity, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 保存头像
     *
     * @param request
     * @param response
     * @param headPic
     */
    @RequestMapping(value = {"/head/save", "/head/save.action"})
    @ResponseBody
    public void headSave(HttpServletRequest request, HttpServletResponse response, @RequestParam MultipartFile headPic) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "保存头像 ";
            /* 判断照片是否上传？ */
            if (headPic.isEmpty()) {
                logger.error(condition + "头像为空");
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "头像不能为空", null, 0);
                return;
            }

            if (!super.isAllImg(headPic)) {
                logger.error(condition + "头像格式错误");
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "图片格式错误！", null, 0);
                return;
            }

            /* 重命名图片 */
            String headurl = renamePic(headPic, "head");
            /* 上传头像 */
            headPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + headurl));
            /* 保存个人中心头像 */
            userService.saveHead(headurl, userId);
            /* 保存维修师头像 */
            matainerService.saveHeadImageUrl(userId + "", headurl);
            backResponse(request, response, ReturnCodeConstant.OK, "保存头像成功", null, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 用户昵称修改
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/username/save", "/username/save.action"})
    public void userNameSave(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "修改昵称 ";
            String userName = params.get("userName");
            /* 业务参数验证 */
            if (userName == null || userName.trim().equals("")) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "昵称不能为空", null, 0);
                return;
            }
            if (!StringUtil.checkUserName(userName)) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "昵称包含不规范文字，请修改后重新发布", null, 0);
                return;
            }

            String clientSign = params.get("clientSign");
            if (StringUtils.isBlank(clientSign)) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "clientSign不能为空", null, 0);
                return;
            }

            userService.saveUserName(userName, userId, Integer.parseInt(clientSign));
            backResponse(request, response, ReturnCodeConstant.OK, "保存昵称成功", null, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
            return;
        }
    }

    /**
     * 用户联系人电话本添加接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/telbook/save", "/telbook/save.action"})
    public void saveUserTel(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "电话本添加 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("tel");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            String tel = params.get("tel");
            if (!this.validateVerifyCode(params, tel, request, response)) {
                return;
            }

            User user = userService.getByUserId(userId);
            /*
             * if(tel.equals(user.getCellPhone())){
             * logger.info(condition+"添加自己"); backResponse(request, response,
             * ReturnCodeConstant.DATA_HAS_EXIT, "您不能添加自己",null,0); return; }
             */
            /* 检查是否已添加？ */
            if (tel.equals(user.getCellPhone()) || userTelService.get(userId, tel)) {
                logger.info(condition + "联系人已经存在");
                backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "联系人已经存在", null, 0);
                return;
            }
            // ab测 默认false走老逻辑
            boolean phoneCountAbTest = false;
            TytAbtestConfig abTestConfig = abtestService.getAbTestConfig(PHONE_COUNT_AB_CODE);
            if (Objects.nonNull(abTestConfig)) {
                if (Objects.equals(abTestConfig.getRuleType(), AbTestRuleTypeEnum.USER.getCode())) {
                    // ab测试类型为用户，判断当前用户是否在ab测内
                    Integer userType = abtestService.getUserTypeByAbTestIdAndUserId(abTestConfig.getId(), userId);
                    if (Objects.nonNull(userType)) {
                        phoneCountAbTest = userType == 1;
                    }
                } else {
                    // ab测试类型为全部，直接取ab测配置
                    phoneCountAbTest = abTestConfig.getDefaultType() == 1;
                }
            }

            // 走新逻辑
            if(phoneCountAbTest){
                logger.info("执行手机号限制逻辑，userId:{}",userId);
                Date monthFirstDay = TimeUtil.getMonthFirstDay(new Date());
                Date monthLastDay = TimeUtil.getMonthLastDay(new Date());
                // 新增
                // 1.校验生效手机号数量<=2
                List<UserTelModel> latestTels = userTelService.getLatestTelsByUserId(userId, null);
                if(CollUtil.isNotEmpty(latestTels) && latestTels.size()>=2){
                    String menu = params.get("menu");
                    if("1".equals(menu)){
                        backResponse(request, response, ReturnCodeConstant.ERROR, "联系方式添加已达到上限，如需新增，可到个人中心-联系电话内操作", null, 0);
                    }else{
                        backResponse(request, response, ReturnCodeConstant.ERROR, "联系方式添加已达到上限", null, 0);
                    }
                    return;
                }
                // 2.最近一个月新增次数
                List<UserTelModel> latestTelsByUserId = userTelService.getLatestTelsByUserId(userId, 1);
                if(CollUtil.isNotEmpty(latestTelsByUserId)){
                    UserTelModel userTel = latestTelsByUserId.get(0);
                    if(Objects.nonNull(userTel.getCreateTime()) && userTel.getCreateTime().compareTo(monthFirstDay) >0 && userTel.getCreateTime().compareTo(monthLastDay)<0){
                        backResponse(request, response, ReturnCodeConstant.ERROR, "每月只能添加修改一次", null, 0);
                        return;
                    }
                }
                /* 添加到数据库 */
                UserTel usertel = new UserTel(userId, tel, "1", tel.startsWith("1") ? "0" : "1");
                usertel.setIsVerified(1);
                userTelService.add(usertel);
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("id", usertel.getId());
                map.put("tel", tel);
                map.put("isVerified", 1);
                backResponse(request, response, ReturnCodeConstant.OK, "保存联系人成功", map, 0);
            }else {

                /* 添加到数据库 */
                UserTel usertel = new UserTel(userId, tel, "1", tel.startsWith("1") ? "0" : "1");
                usertel.setIsVerified(2);
                userTelService.add(usertel);
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("id", usertel.getId());
                map.put("tel", tel);
                backResponse(request, response, ReturnCodeConstant.OK, "保存联系人成功", map, 0);
                logger.info(condition + "成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }


    @ResponseBody
    @RequestMapping("/telbook/updateTel")
    public ResultMsgBean updateTel(UserTelBean userTelBean) {
        logger.info("userTelBean:{}",JSON.toJSONString(userTelBean));

        ResultMsgBean resultMsgBean = new ResultMsgBean();

        Long id = userTelBean.getId();
        Long userId = userTelBean.getUserId();
        String tel = userTelBean.getTel();
        String verifyCode = userTelBean.getVerifyCode();
        String menu = userTelBean.getMenu();

        if (Objects.isNull(id)) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("id不可为空");
            return resultMsgBean;
        }
        if (Objects.isNull(userId)) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("userId不可为空");
            return resultMsgBean;
        }
        if (StringUtils.isBlank(tel)) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("手机号不可为空");
            return resultMsgBean;
        } else {
            String pattern = "^[1][1-9][\\d]{9}$";
            if (!Pattern.matches(pattern, tel)) {
                resultMsgBean.setCode(500);
                resultMsgBean.setMsg("手机号格式异常");
                return resultMsgBean;
            }
        }
        if (StringUtils.isBlank(verifyCode)) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("验证码不可为空");
            return resultMsgBean;
        } else {
            String realVerifyCode = RedisUtil.get(Constant.SMS_VERIFYCODE_PREFFIX + tel);
            if (!verifyCode.equals(realVerifyCode)) {
                resultMsgBean.setCode(500);
                resultMsgBean.setMsg("短信验证码错误");
                return resultMsgBean;
            } else {
                RedisUtil.del(Constant.SMS_VERIFYCODE_PREFFIX + tel);
            }
        }

        // 根据id查询数据，判断前端传入手机号和数据手机号是否一致
        UserTel userTel = userTelService.getById(id);
        logger.info("userTel:{}",JSON.toJSONString(userTel));
        if (Objects.isNull(userTel)) {
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("数据不存在不可修改");
            return resultMsgBean;
        }

        try {
            User user = userService.getByUserId(userId);


            Integer isVerified = userTel.getIsVerified();
            // 当前数据是已验证状态
            if (isVerified == 1) {

                /* 检查是否已添加？ */
                if (tel.equals(user.getCellPhone()) || userTelService.get(userId, tel)) {
                    resultMsgBean.setCode(500);
                    resultMsgBean.setMsg("手机号已添加，无需修改");
                    return resultMsgBean;
                }

                Date monthFirstDay = TimeUtil.getMonthFirstDay(new Date());
                Date monthLastDay = TimeUtil.getMonthLastDay(new Date());
                if (Objects.nonNull(userTel.getUpdateTime()) && userTel.getUpdateTime().compareTo(monthFirstDay) > 0 && userTel.getUpdateTime().compareTo(monthLastDay) < 0) {
                    resultMsgBean.setCode(500);
                    resultMsgBean.setMsg("每月只能添加修改一次");
                    return resultMsgBean;
                } else {
                    UserTel usertel = new UserTel();
                    usertel.setId(id);
                    usertel.setTell(tel);
                    userTelService.updateUserTel(usertel);
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", usertel.getId());
                    map.put("tel", tel);
                    map.put("isVerified", 1);
                    resultMsgBean.setData(map);
                    return resultMsgBean;
                }

            } else {
                // 1.校验生效手机号数量<=2
                List<UserTelModel> latestTels = userTelService.getLatestTelsByUserId(userId, null);
                if (CollUtil.isNotEmpty(latestTels) && latestTels.size() >= 2) {
                    resultMsgBean.setCode(500);
                    if("1".equals(menu)){
                        resultMsgBean.setMsg("联系方式添加已达到上限，如需新增，可到个人中心-联系电话内操作");
                    }else{
                        resultMsgBean.setMsg("联系方式添加已达到上限");
                    }
                    return resultMsgBean;
                }

                if (!tel.equals(userTel.getTell())) {
                    if (tel.equals(user.getCellPhone()) || userTelService.get(userId, tel)) {
                        resultMsgBean.setCode(500);
                        resultMsgBean.setMsg("手机号已添加，无需修改");
                        return resultMsgBean;
                    }
                }

                UserTel usertel = new UserTel();
                usertel.setId(id);
                usertel.setTell(tel);
                userTelService.updateUserTel(usertel);
                Map<String, Object> map = new HashMap<>();
                map.put("id", usertel.getId());
                map.put("tel", tel);
                map.put("isVerified", 1);
                resultMsgBean.setData(map);
                return resultMsgBean;
            }

        } catch (Exception e) {
            logger.error("修改/验证联系人异常：", e);
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("服务器异常");
            return resultMsgBean;
        }
    }

    /**
     * 用户电话本查询接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/telbook/get", "/telbook/get.action"})
    public void userTelGetList(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "电话本获取 ";

            // 电话类型
            String type = params.get("type");
            /* 根据Id查询电话本 */
            List<QueryUserTel> tels = userTelService.getTelsById(userId, type);
            backResponse(request, response, ReturnCodeConstant.OK, "查询联系人成功", tels, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 设置虚拟号拨号电话
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2021-10-19 15:25:47
     */
    @RequestMapping(value = {"/telbook/setDialPhone", "/telbook/setDialPhone.action"})
    @ResponseBody
    public ResultMsgBean setDialPhone(HttpServletRequest request, HttpServletResponse response) {
        /* 参数解析 */
        try {
            Map<String, String> params = parseRequestParams(request);

            long userId = Long.parseLong(params.get("userId"));
            String phone = params.get("phone");
            if (StringUtils.isBlank(phone)) {
                ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "请选择联系方式");
                return msgBean;
            }

            ResultMsgBean resultMsgBean = transportBackendAxbBinderService.setDialPhone(userId, phone);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            ResultMsgBean msgBean = new ResultMsgBean(500, "服务器错误");
            return msgBean;
        }
    }

    /**
     * 获取虚拟号拨号电话
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2021-10-19 15:25:47
     */
    @RequestMapping(value = {"/telbook/getDialPhone", "/telbook/getDialPhone.action"})
    public void getDialPhone(HttpServletRequest request, HttpServletResponse response) {
        /* 参数解析 */
        try {
            Map<String, String> params = parseRequestParams(request);
            List<String> names = new ArrayList<String>() {
                {
                    add("userId");
                }
            };
            long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "获取虚拟号拨打电话 ";
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            String dialPhone = transportBackendAxbBinderService.getDialPhoneByUserId(userId);
            backResponse(request, response, ReturnCodeConstant.OK, "查询拨打电话成功", dialPhone, 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除电话本联系人
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/telbook/delete", "/telbook/delete.action"})
    public void userTelDelete(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            String condition = "userId_" + Long.parseLong(params.get("userId")) + "电话本删除 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("id");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            Long id = Long.parseLong(params.get("id"));

            UserTel tel = userTelService.getById(id);
            if (tel != null && tel.getStatus().equals("1")) {
                // 查看维修师是否绑定该手机号
                Integer c = maintainerService.getByCellPhone(tel.getTell());
                if (c == null || c.intValue() <= 0) {
                    /* 置联系人无效 */
                    userTelService.disabledId(id);
                } else {
                    backResponse(request, response, ReturnCodeConstant.NOT_ALLOWED_DELETE, "该号码被维修师业务绑定,不能删除", null, 0);
                    return;
                }

            }
            backResponse(request, response, ReturnCodeConstant.OK, "删除联系人成功", null, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 判断密码是否正确
     *
     * @param password
     * @return
     */
    private boolean password(User user, String password) {
        return Encoder.md5(user.getPassword() + user.getCellPhone()).equals(password);
    }

    /**
     * 保存User到数据库
     *
     * @param params
     * @return
     * @throws Exception
     */
    private User regUser(Map<String, String> params) throws Exception {
        User user = new User();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        user.setCtime(timestamp);
        user.setMtime(timestamp);
        user.setCellPhone(params.get("cellPhone"));
        user.setPassword(params.get("password"));

        // userSign 字段应该已经废弃 这个判断只是为了让代码不报错
        String userSign = params.get("userSign");
        if (StringUtils.isNotBlank(userSign)) {
            user.setUserSign(Integer.parseInt(userSign));
        } else {
            user.setUserSign(User.UserSignEnum.货主.code);
        }

        user.setUserType(User.USER_TYPE_TRIAL);
        if (AppConfig.getIntProperty("tyt.user.need.verify") == 1) { // 如果需要验证的用户是未激活状态
            user.setUserType(User.USER_TYPE_TRIAL_NOT_VERIFY);
        }
        user.setInfoUploadFlag(User.INFO_UPLOAD_DISABLE);
        user.setPlatId(Integer.parseInt(params.get("clientSign")));
        user.setServeDays(AppConfig.getIntProperty("tyt.trial.days"));
        user.setPhoneServeDays(AppConfig.getIntProperty("tyt.phone.trial.day"));
        user.setQqModTimes(0);
        user.setEndTime(TimeUtil.stampAdd(TimeUtil.getTimeStamp(), user.getServeDays()));
        /* 省市县 */
        String[] resultArray = MobileUtil.getMobileAddressArr(user.getCellPhone());
        if (resultArray != null && resultArray.length == 2) {
            user.setProvince(resultArray[0]);
            user.setCity(resultArray[1]);
        }
        // 系统实名认证判读
        if (!isSystemIdentity(user.getCellPhone(), user.getProvince() + user.getCity())) {
            user.setVerifyFlag(1);
            user.setTrueName(" ");
            user.setUserName(" ");
        } else {
            user.setTrueName(params.get("trueName"));
            user.setUserName(params.get("userName"));
            user.setVerifyFlag(0);
        }
        user.setInfoPublishFlag(User.INFO_PUBLISH_ENABLE);
        user.setIsCar("0");
        user.setIsBank("0");
        /* 关闭QQ消息盒子 */
        user.setQqBoxFlag(User.QQ_BOX_FLAG_CLOSE);
        user.setIsDispatch(0);
        user.setVerifyPhotoSign(0);
        user.setUserPart(100);
        // 司机限制
        TytConfig tytConfig = tytConfigService.getValue("driverLimit");
        if (tytConfig != null && "1".equals(tytConfig.getValue())) {
            if (user.getUserSign().intValue() == 7) {
                user.setVerifyFlag(0);
                user.setServeDays(0);
                user.setPhoneServeDays(0);
                user.setEndTime(new Timestamp(System.currentTimeMillis()));
                // user.setInfoPublishFlag(User.INFO_PUBLISH_DISABLE);
            }
        }

        if (params.get("channelCode") != null && !"".equals(params.get("channelCode"))) {
            TytSource tytSource = TytSourceUtil.getSourceName("app_channel", String.valueOf(params.get("channelCode")));
            if (tytSource != null) {
                user.setChannel(Integer.parseInt(params.get("channelCode")));
            }
        }

        user.setSource("待定");
        user.setSourceRemark("");
        return user;
    }

    /**
     * 生成返回给客户端的用户信息
     *
     * @param user
     * @return
     * @throws Exception
     */
    private UserBean createUserResponse(User user) throws Exception {
        /* 创建UserBean */
        UserBean userBean = new UserBean();
        BeanUtils.copyProperties(user, userBean);
		/*// 获取身份标签
		Object[] identityLables = tytUserSubService.getIdentityLables(user.getId());
		if (identityLables != null) {
			userBean.setBcarIdentityLables((String) identityLables[0]);
			userBean.setScarIdentityLables((String) identityLables[1]);
		}*/
        /*
         * 获取用户的认证信息
         */
        fetchUserIdentity(user, userBean);
        // 获取用户权益信息
        List<UserPermission> userPermissions = userPermissionService.getPermissionListByUserId(user.getId());
        userPermissions = BeanUtil.copyToList(userPermissions, UserPermission.class);
        //兼容新会员权益
        Map<String, UserPermission> userPermissionMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(userPermissions)) {
            userPermissionMap = userPermissions.stream().collect(Collectors.toMap(UserPermission::getServicePermissionTypeId, a -> a, (k1, k2) -> k1));
        }
        userPermissionService.compatibilityNewPermission(user.getId(), userPermissionMap, CAR_NUM.getTypeId(), CAR_NUM_MEAL.getTypeId());
        userPermissionService.compatibilityNewPermission(user.getId(), userPermissionMap, GOODS_NUM.getTypeId(), GOODS_NUM_NEW.getTypeId());
        userPermissions = new ArrayList<>(userPermissionMap.values());
        userBean.setUserPermissions(userPermissions);
        //获取平台交易量
        Integer tradeNums = infofeeDetailService.getTradeNums(user.getId());
        userBean.setTradeNums(tradeNums == null ? "0" : String.valueOf(tradeNums));
        //判断是否含有专车
        boolean isSpecialCar = carService.selectCarIsSpecialCarCount(user.getId());
        userBean.setIsSpecialCar(isSpecialCar ? 1 : 0);
        return userBean;
    }

    /**
     * 创建Advice对象
     *
     * @param params
     * @return
     * @throws Exception
     */
    private Advice createAdvice(Map<String, String> params) throws Exception {
        Advice advice = new Advice();
        // 根据userId提取用户手机号
        User user = userService.getByUserId(Long.parseLong(params.get("userId")));
        advice.setCellPhone(user.getCellPhone());
        advice.setPlatId(Integer.parseInt(params.get("clientSign")));
        advice.setVersion(params.get("clientVersion"));
        advice.setTitle(Integer.parseInt(params.get("title")));
        advice.setContent(params.get("content"));
        advice.setTelPhone(params.get("telPhone"));
        advice.setStatus(Constant.ADVICE_STATUS_NO);
        advice.setSource(6);
        advice.setcName(user.getId().toString());
        advice.setCtime(new Timestamp(System.currentTimeMillis()));
        return advice;
    }

    /**
     * 创建联系人实体类
     *
     * @param params
     * @return
     * @throws Exception
     */
    private LinkMan createLinkman(Map<String, String> params) throws Exception {
        LinkMan man = new LinkMan();
        Long userId = Long.parseLong(params.get("userId"));
        User user = userService.getByUserId(userId);
        man.setCellPhone(user.getCellPhone());
        man.setPlatId(Integer.parseInt(params.get("clientSign")));
        man.setCtime(new Timestamp(System.currentTimeMillis()));
        // 去除特殊字符
        man.setLinkMan(params.get("linkMan").trim().replaceAll("[^0-9a-zA-Z\u4e00-\u9fa5，,+:<：]+", ""));
        return man;
    }

    @RequestMapping(value = "/call/phone")
    @ResponseBody
    public ResultMsgBean callPhone(BaseParameter baseParameter, String tsId, String moduleType) {
        logger.info("user call phone, muduleType is: " + moduleType);
        ResultMsgBean rm = new ResultMsgBean();
//		try {
//			TytConfig tytConfig = tytConfigService.getValue("callPhoneLog");
//			if ("1".equals(tytConfig == null ? null : tytConfig.getValue())) {
//				if (StringUtils.isBlank(tsId)) {
//					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//					rm.setMsg("运输信息ID不能为空！");
//				} else {
//
//					TransportMain transportMain = transportMainService.getTransportMainForId(Long.valueOf(tsId));
//					TytUserCallPhone tucp = new TytUserCallPhone();
//					tucp.setTsId(transportMain.getId());
//					tucp.setUserId(baseParameter.getUserId());
//					tucp.setCreateTime(new Date());
//					tucp.setClientVersion(baseParameter.getClientVersion());
//					tucp.setMuduleType(moduleType);
//					tucp.setActionType(4);
//					int clientSign = 0;
//					if (StringUtil.isNumeric(baseParameter.getClientSign()))
//						clientSign = Integer.parseInt(baseParameter.getClientSign());
//					tucp.setClientSign(clientSign);
//					userCallPhoneService.add(tucp);
//					/*
//					 * 如果是通过精准货源模块拨打的电话则需要记录货源拨打电话的信息，按照用户去重
//					 */
//					if (StringUtils.isNotEmpty(moduleType) && "1".equals(moduleType)) {
//						// 获取货物信息
//						Transport transport = transportService.getById(Long.valueOf(tsId));
//						if (transport != null) {
//							String srcMsgId = transport.getSrcMsgId() + "";
//							if (StringUtils.isEmpty(srcMsgId)) {
//								rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//								rm.setMsg("src msg ID不能为空！");
//							} else {
//								String today = TimeUtil.formatDate_(new Date());
//								String goodCallUserIdsKey = tytConfigService.getStringValue(Constant.RECOMMEND_USER_CALL_KEY, "recommend_user_call_${srcMsgId}_${date}");
//								goodCallUserIdsKey = StringUtils.replaceEach(goodCallUserIdsKey, new String[] { "${srcMsgId}", "${date}" }, new String[] { srcMsgId, today });
//								String goodCallTimesKeys = tytConfigService.getStringValue(Constant.RECOMMEND_USER_CALL_TIMES_KEY, "recommend_user_call_times_${goodId}_${date}");
//								goodCallTimesKeys = StringUtils.replaceEach(goodCallTimesKeys, new String[] { "${goodId}", "${date}" }, new String[] { srcMsgId, today });
//								// 获取当前都有哪些用户当天在精准推荐模块拨打了该货源电话
//								String userIds = cacheService.getString(goodCallUserIdsKey);
//								logger.info("query good call userIds by key: " + goodCallUserIdsKey + ", result is: " + userIds);
//								/*
//								 * 如果是获取结果为空，说明当天还没有人打过电话则记录该用户的拨打信息以及货源被拨打次数信息
//								 */
//								String callTimes = "";
//								String callUserIds = "";
//								boolean isNeedChange = false;
//								if (StringUtils.isEmpty(userIds)) {
//									isNeedChange = true;
//									// 设置货源拨打电话次数
//									callTimes = "1";
//									// 设置拨打货源的用户信息
//									callUserIds = "#" + baseParameter.getUserId() + "#";
//								} else {
//									/*
//									 * 只有在该用户没有拨打过电话才处理
//									 */
//									if (userIds.indexOf("#" + baseParameter.getUserId() + "#") == -1) {
//										isNeedChange = true;
//										// 获取货源拨打电话次数
//										String goodCallTimes = cacheService.getString(goodCallTimesKeys);
//										logger.info("query good call times by key: " + goodCallTimesKeys + ", result is: " + goodCallTimes);
//										// 设置次数
//										callTimes = String.valueOf(Integer.valueOf(goodCallTimes) + 1);
//										// 设置拨打电话的用户信息
//										callUserIds = userIds + baseParameter.getUserId() + "#";
//									}
//								}
//								if (isNeedChange) {
//									cacheService.setString(goodCallTimesKeys, callTimes, Constant.ONE_DAY);
//									cacheService.setString(goodCallUserIdsKey, callUserIds, Constant.ONE_DAY);
//								}
//							}
//						}
//					}
//					rm.setCode(ReturnCodeConstant.OK);
//					rm.setMsg("操作成功");
//				}
//			}
//		} catch (Exception ex) {
//			logger.error("服务器异常", ex);
//			rm.setCode(ReturnCodeConstant.ERROR);
//			rm.setMsg("服务器错误");
//		}
        return rm;
    }

    /**
     * 判断是否开启系统实名认证
     *
     * @param phone
     * @return false 需要系统实名认证
     */
    public boolean isSystemIdentity(String phone, String place) {

        TytConfig systemIdentityIsOpendTytConfig = tytConfigService.getValue("systemIdentityIsOpend");
        String systemIdentityIsOpend = systemIdentityIsOpendTytConfig.getValue();
        // String systemIdentityIsOpend="1";
        // 开关是否开了
        if ("1".equals(systemIdentityIsOpend)) {
            TytConfig systemIdeatityAreaTytConfig = tytConfigService.getValue("systemIdeatityArea");
            String systemIdeatityArea = systemIdeatityAreaTytConfig.getValue();
            // String systemIdeatityArea="北京,贵州,天津,郑州";
//			String place = MobileUtil.getMobileAddress(phone);
            String[] systemIdeatityAreas = systemIdeatityArea == null ? null : systemIdeatityArea.split(",");

            // 判断是否在排除范围内
            if (systemIdeatityAreas != null && null != place) {
                for (String area : systemIdeatityAreas) {
                    if (place.indexOf(area) != -1) {
                        return true;
                    }
                }
            }
        } else {
            return true;
        }

        return false;
    }

    // private static boolean isFixedPhone(String cellPhone){
    // return
    // Pattern.compile("^(010|02\\d|0[3-9]\\d{2})?-\\d{6,8}$").matcher(cellPhone).matches();
    // }
    // public static void main(String[] args) {
    // System.out.println(isFixedPhone("0357-6566812"));
    // }
    @RequestMapping(value = "/sendGoodsCheck")
    @ResponseBody
    public ResultMsgBean sendGoodsCheck(BaseParameter baseParameter) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            String msg = propertiesFileUtil.getString("tyt.user.send.goods.check.msg");
            GoodsCheckBean goodsCheckBean = new GoodsCheckBean();
            if (tytUserSubService.isSendGoods(baseParameter.getUserId())) {
                goodsCheckBean.setIsSendGoods(0);
                goodsCheckBean.setMsg(msg);
                goodsCheckBean.setRemainNumber(tytUserSubService.getUserRemainNumber(baseParameter.getUserId()));
            } else {
                goodsCheckBean.setIsSendGoods(1);
                goodsCheckBean.setMsg(msg);
                goodsCheckBean.setRemainNumber(0);
            }
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("查询成功");
            rm.setData(goodsCheckBean);

        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping(value = "/identity/info")
    @ResponseBody
    public ResultMsgBean identityInfo(BaseParameter baseParameter) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            UserIdentityBean userIdentityBean = new UserIdentityBean();

            TytUserIdentityMain tytUserIdentityMain = userIdentityMainService.getTytUserIdentityMainForUserId(baseParameter.getUserId());

            if (tytUserIdentityMain != null) {
                BeanUtils.copyProperties(tytUserIdentityMain, userIdentityBean);
                if (tytUserIdentityMain.getVerifyPhotoSign() == 3) {
                    userIdentityBean.setFailureReason("认证失败！" + tytUserIdentityMain.getFailureReason());
                }
            }
            User user = userService.getByUserId(baseParameter.getUserId());
            userIdentityBean.setUserId(baseParameter.getUserId());
            userIdentityBean.setVerifyFlag(user.getVerifyFlag());
            userIdentityBean.setVerifyPhotoSign(user.getVerifyPhotoSign() == null ? 0 : user.getVerifyPhotoSign());
            if (userIdentityBean.getVerifyFlag().intValue() == 0 || userIdentityBean.getVerifyFlag().intValue() == 3) {
                userIdentityBean.setIdentity(null);
                userIdentityBean.setRealName(null);
            }

            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg(propertiesFileUtil.getString("tyt.user.identity.info.success"));
            rm.setData(userIdentityBean);

        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 照片认证采集接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/identity/photo/save")
    @ResponseBody
    public void identityPhotoSave(HttpServletRequest request, HttpServletResponse response, @RequestParam MultipartFile mainPic) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "实名认证 ";
            // /*业务参数验证*/
            // @SuppressWarnings("serial")
            // List<String> names=new
            // ArrayList<String>(){{add("identity");add("realName");}};
            // if(!validateArguments(condition,request,response,params,names))return;
            /* 判断照片是否上传？ */
            if (mainPic.isEmpty()) {
                logger.error(condition + "图片为空");
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "图片不能为空", null, 0);
                return;
            }
            if (userIdentityMainService.isExitForPhoto(userId)) {
                logger.error(condition + "重复录入");
                backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, propertiesFileUtil.getString("tyt.user.identity.photo.save.error1"), null, 0);
                return;
            }

            if (!super.isAllImg(mainPic)) {
                logger.error(condition + ":图片格式错误！");
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "图片格式错误！", null, 0);
                return;
            }

            /* 先把之前提交的置为无效 */
            userIdentityMainService.saveUserIdentityMain(userId, mainPic);
            backResponse(request, response, ReturnCodeConstant.OK, propertiesFileUtil.getString("tyt.user.identity.photo.save.success"), null, 0);
            logger.info(condition + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 信息认证采集接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/identity/info/save")
    @ResponseBody
    public void identityInfoSave(String identity, String realName, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            /*
             * Map<String, String> params = parseRequestParams(request); Long
             * userId = Long.parseLong(params.get("userId")); String condition =
             * "userId_" + userId + "实名认证 ";
             */
            // /*业务参数验证*/
            /*
             * if (realName == null || "".equals(realName)) {
             * logger.error(condition + "姓名不能为空"); backResponse(request,
             * response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "姓名不能为空",
             * null, 0); return; } if (identity == null || "".equals(identity))
             * { logger.error(condition + "身份证号不能为空"); backResponse(request,
             * response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "身份证号不能为空",
             * null, 0); return; } User user = userService.getByUserId(userId);
             * if (user.getVerifyFlag().intValue() == 1 ||
             * user.getVerifyFlag().intValue() == 2) { logger.error(condition +
             * "身份证号已认证过"); backResponse(request, response,
             * ReturnCodeConstant.DATA_HAS_EXIT,
             * propertiesFileUtil.getString("tyt.user.identity.info.save.error.2007"
             * ), null, 0); return; }
             *
             * if (userIdentityMainService.isExitForInfo(identity, realName)) {
             * logger.error(condition + "该身份证号已被认证占用，如有疑问请联系客服");
             * backResponse(request, response, 2008,
             * propertiesFileUtil.getString
             * ("tyt.user.identity.info.save.error.2008"), null, 0); return; }
             *
             * // 保存数据库 中 int state =
             * userIdentityMainService.saveUserIdentityMain(userId, identity,
             * realName); if (state == 0) { Map<String, Integer> map = new
             * HashMap<String, Integer>(); map.put("remainNumber",
             * tytUserSubService.getUserRemainNumber(userId));
             */
            Map<String, Integer> map = new HashMap<String, Integer>();
            map.put("remainNumber", 0);
            backResponse(request, response, ReturnCodeConstant.OK, propertiesFileUtil.getString("tyt.user.identity.info.save.success"), map, 0);

            /*
             * backResponse(request, response, ReturnCodeConstant.OK,
             * propertiesFileUtil
             * .getString("tyt.user.identity.info.save.success"), map, 0);
             * logger.info(condition + "成功"); } else if (state == 101) {
             * backResponse(request, response, 2005,
             * propertiesFileUtil.getString
             * ("tyt.user.identity.info.save.error.2005"), null, 0); } else if
             * (state == 102) { backResponse(request, response, 2010,
             * propertiesFileUtil
             * .getString("tyt.user.identity.info.save.error.2010"), null, 0); }
             * else if (state == 400) { backResponse(request, response, 2009,
             * propertiesFileUtil
             * .getString("tyt.user.identity.info.save.error.2009"), null, 0); }
             * else { backResponse(request, response, ReturnCodeConstant.ERROR,
             * "服务器错误", null, 0); }
             */
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    protected void channelLog(HttpServletRequest request, User user, Integer record_type, Map<String, String> params) throws Exception {
        TytChannelLog tytChannelLog = new TytChannelLog();

        tytChannelLog.setPhoneType(params.get("phoneType"));
        tytChannelLog.setVersionType(params.get("versionType") == null ? null : Integer.parseInt(params.get("versionType")));
        tytChannelLog.setClientId(params.get("clientId"));
        tytChannelLog.setOsVersion(params.get("osVersion"));
        tytChannelLog.setClientVersion(params.get("clientVersion"));
        tytChannelLog.setClientSign(Integer.parseInt(params.get("clientSign")));
        tytChannelLog.setUserId(user.getId());
        tytChannelLog.setRecordType(record_type);
        tytChannelLog.setCtime(new Date());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(params.get("channelCode"))) {
            TytSource tytSource = TytSourceUtil.getSourceName("app_channel", String.valueOf(params.get("channelCode")));
            if (tytSource != null) {
                tytChannelLog.setChannelName(tytSource.getName());
                tytChannelLog.setChannelCode(Integer.parseInt(params.get("channelCode")));
            }
        }
        if (params.get("certificateType") != null) {
            tytChannelLog.setCertificateType(Integer.parseInt(params.get("certificateType")));
        }
        tytChannelLogService.add(tytChannelLog);
        //userId与mac(clientId)关系唯一记录添加至tyt_user_mac
        userMacService.saveUserMac(user, tytChannelLog);

        //缓存记录用户登录的手机基本信息，每次登录时更新存储  这部分用户企业版登录，已无企业版项目
        Map<String, Object> map = new HashMap<>();
        map.put("userId", user.getId());
        map.put("phoneType", tytChannelLog.getPhoneType());
        map.put("clientId", tytChannelLog.getClientId());
        map.put("osVersion", tytChannelLog.getOsVersion());
        map.put("clientVersion", tytChannelLog.getClientVersion());
        map.put("clientSign", tytChannelLog.getClientSign());
        map.put("cid", params.get("cid"));
        map.put("deviceId", params.get("deviceId"));
        String userHeader = JSON.toJSONString(map);
        RedisUtil.set(Constant.PLAT_USER_HEADER + user.getId(), userHeader, 3 * 24 * 60 * 60);

    }

    /**
     * 标签获取，包括身份标签，商户标签，维修师技能标签(父子，子父)
     *
     * @return
     */
    @RequestMapping(value = "/identityLabels/list")
    @ResponseBody
    public ResultMsgBean getIdentityLabelsList() {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            Map<String, List<TytSource>> map = userService.getIdentityLables();
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("获得成功");
            rm.setData(map);
            return rm;
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }

    @RequestMapping(value = "/identityLabels/update")
    @ResponseBody
    public ResultMsgBean updateIdentityLabels(Long userId, String bcarIdentityLables, String scarIdentityLables) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if ((bcarIdentityLables == null || bcarIdentityLables.trim().equals("")) && (scarIdentityLables == null || scarIdentityLables.trim().equals(""))) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("标签不能为空");
                return rm;
            }
            tytUserSubService.updateIdentityLables(userId, bcarIdentityLables, scarIdentityLables);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("保存标签成功");
            return rm;
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }

    @RequestMapping(value = "/collectCount")
    @ResponseBody
    public ResultMsgBean collectCount(String userId) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("获取成功");
        try {
            if (StringUtils.isEmpty(userId)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("userId不能为空");
                return rm;
            }
            UserCollectCountBean collectCountBean = tytUserSubService.getUserCollectCount(userId);
            rm.setData(collectCountBean);
            return rm;
        } catch (Exception e) {
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }

    /**
     * 我的收藏列表
     *
     * @param userId ：板车招聘收藏 3：板车求职收藏 4：设备招聘收藏 5：设备招聘收藏
     *               <1时，currentPage=1;当currentPage>maxPage时不再请求服务器。每次请求数据去重
     * @return
     */
    @RequestMapping("/collectList")
    @ResponseBody
    public ResultMsgBean getMyCollects(Long userId, @RequestParam(value = "type", defaultValue = "2") String type, @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage, @RequestParam(value = "status", defaultValue = "0") String status) {
        ResultMsgBean msgBean = new ResultMsgBean();
        try {
            msgBean.setCode(ReturnCodeConstant.OK);
            msgBean.setMsg("查询成功");
            Map<String, Object> result = userService.getCollectList(userId, type, currentPage, status);
            msgBean.setData(result);
            return msgBean;
        } catch (Exception e) {
            // TODO Auto-generated catch block
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
            e.printStackTrace();
            return msgBean;
        }
    }

    /**
     * @param userId
     * @return
     */
    @RequestMapping("/goodsNotify")
    @ResponseBody
    public ResultMsgBean goodsNotify(Long userId) {
        GoodsNotifyResBean msgBean = new GoodsNotifyResBean();
        msgBean.setCode(ReturnCodeConstant.OK);
        try {
            if (userId == null) {
                msgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                msgBean.setMsg("userId 不能为空");
            } else {
                // 查询是是否需要弹框提醒设置获取成交
                boolean isNeedNotify = userService.isNeedNotifyGoods(userId);
                msgBean.setNeedNotify(isNeedNotify ? 1 : 2);
                System.out.println("msgBean-----" + msgBean.getNeedNotify());
            }
        } catch (Exception e) {
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
            e.printStackTrace();
            return msgBean;
        }
        return msgBean;
    }

    /**
     * 保存用户的一级身份
     *
     * @param userId                用户id
     * @param sourceUserClassValue  用户分类1、发货方2、车辆方 见 source user_class
     * @param userIdentityTypeValue 用户身份见source表 user_identity_type
     * @return
     */
    @RequestMapping("/identityAuthentication/saveOneLevelIdentity")
    @ResponseBody
    public ResultMsgBean saveOneLevelIdentity(String userId, Integer sourceUserClassValue, Integer userIdentityTypeValue) {
        GoodsNotifyResBean msgBean = new GoodsNotifyResBean();
        msgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
        try {
            if (userId == null) {
                msgBean.setMsg("userId不能为空");
            } else if (sourceUserClassValue == null || sourceUserClassValue < 1 || sourceUserClassValue > 2) {
                msgBean.setMsg("sourceUserClassValue值不能为空，且只能是1或2");
            } else if (userIdentityTypeValue == null || userIdentityTypeValue < 1 || userIdentityTypeValue > 9) {
                msgBean.setMsg("userIdentityTypeValue值不能为空，且只能是1到9之间的整数");
            } else {
                msgBean.setCode(ReturnCodeConstant.OK);
                msgBean.setMsg("操作成功");
                userService.saveOneLevelIdentity(userId, userIdentityTypeValue > 5 ? 2 : 1, userIdentityTypeValue);
            }
        } catch (Exception e) {
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
            e.printStackTrace();
            return msgBean;
        }
        return msgBean;
    }

    @RequestMapping(value = "/vip/count")
    @ResponseBody
    public ResultMsgBean VIPCount(String userId) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询开通会员总人数成功");
        try {
            VIPUserCount userCount = new VIPUserCount();
            String vipCount = cacheService.getString(USER_VIP_COUNT_KEY);
            if (StringUtils.isNotBlank(vipCount)) {
                userCount.setVIPCount(vipCount);
                rm.setData(userCount);
                return rm;
            }
            BigInteger count = userService.getVIPCount();
            BigInteger countNew = count.multiply(new BigInteger("3"));
//			Integer initCount = tytConfigService.getIntValue("initVIPCount");
            cacheService.setString(USER_VIP_COUNT_KEY, countNew.toString(), Constant.CACHE_EXPIRE_TIME_24H);
            userCount.setVIPCount(countNew.toString());
            rm.setData(userCount);
            return rm;
        } catch (Exception e) {
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }

    /**
     * 获取验证码图片
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = {"/validateImage", "/validateImage.action"})
    public void validateImage(HttpServletRequest request, HttpServletResponse response, String cellPhone) throws IOException {
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        // 表明生成的响应是图片
        response.setContentType("image/jpeg");
        CreateImageCode vCode = new CreateImageCode(110, 40, 4, 0);
        // 保存验证码到redis用于验证
        RedisUtil.set(Constant.LOGIN_VERIFY_CODE_REDIS_KEY + cellPhone, vCode.getCode(), Constant.CACHE_EXPIRE_TIME_12H_INT);
        ImageIO.write(vCode.getBuffImg(), "png", response.getOutputStream());
    }

    /**
     * 验证验证码接口
     *
     * @return
     */
    @RequestMapping(value = "/validateCode")
    @ResponseBody
    public ResultMsgBean validateCode(String cellPhone, String code) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("验证成功");
        try {
            String realCode = RedisUtil.get(Constant.LOGIN_VERIFY_CODE_REDIS_KEY + cellPhone);
            if (StringUtils.isEmpty(realCode) || !realCode.equals(code)) {
                rm.setCode(300);
                rm.setMsg("图形验证码不正确");
                // 验证失败清除验证码信息
                RedisUtil.del(Constant.LOGIN_VERIFY_CODE_REDIS_KEY + cellPhone);
            } else {
                // 验证成功保存验证成功标记
                String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                RedisUtil.set(verifyOkKey, "1", (int) TimeUtil.getTomorrowZeroSeconds());
            }
            return rm;
        } catch (Exception e) {
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }

    @RequestMapping(value = "/queryByPhone")
    @ResponseBody
    public ResultMsgBean queryByPhone(String cellPhone) {
        ResultMsgBean rm = new ResultMsgBean();
        logger.info("queryByPhone: " + cellPhone);
        try {
            userService.queryByCellphone(cellPhone, rm);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping(value = "/rightsInfo")
    @ResponseBody
    public ResultMsgBean rightsInfo(Long userId) {
        ResultMsgBean rm = new ResultMsgBean();
        logger.info("9100 rightsInfo: " + userId);
        try {
            RightsInfoBean infoBean = userService.getRightsInfo(userId);
            rm.setData(infoBean);
            bubbleService.updateRightsInfoBubble(userId);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping(value = "/getCellPhone")
    @ResponseBody
    public ResultMsgBean getCellPhone(
            @RequestParam(value = "userId", required = true) Long userId,
            @RequestParam(value = "linkUserId", required = true) Long linkUserId,
            String clientVersion) {
        ResultMsgBean rm = new ResultMsgBean();
        logger.info("查询注册手机号: " + linkUserId);
        try {
            PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货源回拨联系人, userId);
            if (permissionResult.getUse()) {
                logger.info("查询用户{}-存在回拨联系人权益", userId);
                String cellPhone = userService.getCellPhoneById(linkUserId);
                rm.setData(cellPhone);
            } else {
                logger.info("查询用户{}-无回拨联系人权益", userId);
                Integer userType = abtestService.getUserType(Constant.TRANSMISSION_AB_TEST_KEY, userId);
                if (userType == 1 && StringUtils.isNotBlank(clientVersion) && Integer.parseInt(clientVersion) >= 6640){
                    VipNoticeForGoodsBean notice = couponService.getNoticeContent(userId);
                    rm.setCode(ReturnCodeConstant.NO_PERMISSION_PERSONAL);
                    rm.setData(JSON.toJSONString(notice));
                }else{
                    TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(), null);
                    rm.setNoticeData(noticePopupTempl);
                    rm.setCode(ReturnCodeConstant.NO_PERMISSION);
                }
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    private boolean validateGoodsWebInput(Map<String, String> params, HttpServletRequest request, HttpServletResponse response) {

        int clientSign = Integer.parseInt(params.get("clientSign"));

        if (Constant.ClientSignEnum.WEB_GOODS.code == clientSign) {

            if (StringUtils.isBlank(params.get("password"))) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "密码不能为空", null, 0);
                return false;
            }
            if (StringUtils.isBlank(params.get("newPassword"))) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "再次输入密码不能为空", null, 0);
                return false;
            }

            // 校验两次密码输入是否一致
            if (!params.get("password").equals(params.get("newPassword"))) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "两次密码输入不一致", null, 0);
                return false;
            }
        }

        return true;
    }

    private boolean validateMobileNo(String mobileNo, HttpServletRequest request, HttpServletResponse response) {
        Pattern pattern = Pattern.compile(MOBILE_REGEX);
        if (!pattern.matcher(mobileNo).matches()) {
            backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "手机号格式错误", null, 0);
            return false;
        }
        return true;
    }

    private boolean validatePasswordSimply(String password, HttpServletRequest request, HttpServletResponse response) {
        boolean isBanPassword = banPasswords.containsKey(password);
        if (isBanPassword) {
            backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "您的密码过于简单，请重新设置", null, 0);
            return false;
        }
        return true;
    }

    private boolean validateVerifyCode(Map<String, String> params, String cellPhone, HttpServletRequest request, HttpServletResponse response) {

        int clientSign = Integer.parseInt(params.get("clientSign"));

        // 老版本特运通无需校验
        if (clientSign <= CLIENT_SIGN_SEPERATOR && clientSign != 1) {
            return true;
        }
        String pattern = "^[1][1-9][\\d]{9}$";
        if (!Pattern.matches(pattern, cellPhone)) {
            backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "手机号格式异常", null, 0);
            return false;
        }

        // 由于客户端和货主web端定义的字段不同，区分下
        String verifyCode = (clientSign == Constant.ClientSignEnum.WEB_GOODS.code) ? params.get("verifyCode") : params.get("verificationCode");

        if (StringUtils.isBlank(verifyCode)) {
            backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "短信验证码不能为空", null, 0);
            return false;
        }

        String realVerifyCode = RedisUtil.get(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
        if (!verifyCode.equals(realVerifyCode)) {
            backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "短信验证码错误", null, 0);
            return false;
        } else {
            RedisUtil.del(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
            return true;
        }
    }


    @RequestMapping(value = {"/getCredit", "/getCredit.action"})
    public void getCredit(HttpServletRequest request, HttpServletResponse response) {

        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            if (StringUtils.isBlank(params.get("userId"))) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "参数缺失", null, 0);
                return;
            }
            Long userId = Long.parseLong(params.get("userId"));

            UserCreditBean bean = new UserCreditBean();
            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
            if (userCreditInfo != null) {
                if (null != userCreditInfo.getTotalScore()) {
                    bean.setTotalScore(userCreditInfo.getTotalScore().setScale(0, RoundingMode.DOWN));
                }
                if (null != userCreditInfo.getRankLevel() && userCreditInfo.getRankLevel() != 0) {
                    bean.setRankLevel(userCreditInfo.getRankLevel());
                } else {
                    bean.setRankLevel(1);
                }
                if (null != userCreditInfo.getCarTotalServerScore()) {
                    bean.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
                }
                if (null != userCreditInfo.getCarServerRankScore()) {
                    bean.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
                }
            } else {
                bean.setRankLevel(1);
            }
            backResponse(request, response, ReturnCodeConstant.OK, "查询信用成功", bean, 0);

        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    @ResponseBody
    @RequestMapping(value = {"/credit/monthlyReport", "/credit/monthlyReport.action"})
    public ResultMsgBean getCreditMonthlyReport(HttpServletRequest request) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            if (StringUtils.isBlank(params.get("userId"))) {
                return new ResultMsgBean(ResultMsgBean.ERROR, "参数缺失");
            }
            Long userId = Long.parseLong(params.get("userId"));

            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
            UserCreditMonthlyReportVO result = UserCreditMonthlyReportVO.fromApiUserCreditInfo(
                    userCreditInfo);
            return new ResultMsgBean(ResultMsgBean.OK, "成功", result);
        } catch (Exception e) {
            logger.error("查询信用月报异常", e);
            return new ResultMsgBean(ResultMsgBean.ERROR, "服务器错误");
        }
    }


    /**
     * 单个用户信息查询接口
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = {"/getByCellPhone"})
    public void get(@RequestParam(value = "cellPhone", required = true) String cellPhone,
                    HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);

            /* 根据Id查询用户 */
            User user = userService.getUserByCellphone(cellPhone);
            if (user == null) {
                backResponse(request, response, ReturnCodeConstant.ERROR, "未查询到用户信息", null, 0);
                return;
            }
            UserBean userBean = new UserBean();
            BeanUtils.copyProperties(user, userBean);
            userBean.setDomain(tytConfigService.getStringValue("tyt_server_picture_url_old"));
            // 去掉部分敏感和无效信息
            userBean.setIdCard(null);
            userBean.setTicket(null);
            userBean.setPhoneServeDays(null);
            userBean.setQqBoxFlag(null);
            userBean.setInfoPublishFlag(null);
            userBean.setPhoneOpenFlag(null);
            userBean.setUserIdentityStatus(null);
            userBean.setRankLevel(null);
            userBean.setKillBill(null);
            userBean.setRenewalDate(null);
            userBean.setRenewalYears(null);
            userBean.setIsUpdate(null);
            userBean.setIsBank(null);
            userBean.setIdentityType(null);


            backResponse(request, response, ReturnCodeConstant.OK, "查询信息成功", userBean, 0);
            logger.info("手机号:{}, 查询完成");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }
    }

    /**
     * 修改身份 用于 登录后弹出身份认证选择界面后 设置用户身份
     *
     * @param userId
     * @param identityType 车版状态 20 个人车主 21 运输公司 22 司机 23 其他 货版状态 30 个人货主 31 货站 32 物流公司 33 制造商 34 其他
     * @return
     */
    @PostMapping("/updateIdentityType")
    @ResponseBody
    public ResultMsgBean updateIdentityType(Long userId, Integer identityType) {
        if (Objects.isNull(userId) || Objects.isNull(identityType)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少必要参数");
        }
        try {
            userService.updateIdentityType(identityType, userId);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
    }

    /**
     * 修改注册身份 用于 登录后弹出身份认证选择界面后 设置用户身份
     *
     * @param userId
     * @param
     * @param
     * @return
     */
    @PostMapping("/updateSelectionIdentity")
    @ResponseBody
    public ResultMsgBean updateSelectionIdentity(Long userId, Integer selectionIdentity, Integer initialNum, Integer initialCarNum) {
        if (Objects.isNull(userId) || Objects.isNull(selectionIdentity) || Objects.isNull(initialNum) || Objects.isNull(initialCarNum)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少必要参数");
        }
        try {
            userService.updateSelectionIdentity(selectionIdentity, initialNum, initialCarNum, userId);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
    }

    /**
     * @param userId
     * @return com.tyt.model.ResultMsgBean
     * @description 查询用户信用弹窗信息接口
     * <AUTHOR>
     * @date 2022/9/21 17:10
     */
    @RequestMapping(value = {"getUserCreditInfoPopup", "getUserCreditInfoPopup.action"})
    @ResponseBody
    public ResultMsgBean getUserCreditInfoPopup(Long userId) {
        try {
            if (Objects.isNull(userId)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
            }
            //1.判断用户信用弹窗信息是否为空，如果为空则进行初始化
            UserCreditInfoPopup userCreditInfoPopup = userCreditInfoPopupService.getPopupByUserId(userId);
            if (userCreditInfoPopup == null) {
                userCreditInfoPopup = new UserCreditInfoPopup();
                userCreditInfoPopup.setUserId(userId);
                userCreditInfoPopup.setPopupNum(0);
                userCreditInfoPopup.setCheckStatus(0);
                userCreditInfoPopup.setCtime(new Date());
                userCreditInfoPopup.setMtime(new Date());
                userCreditInfoPopupService.addSave(userCreditInfoPopup);
            }
            //2.查询用户信用分和等级信息
            UserCreditBean userCreditBean = new UserCreditBean();
            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
            if (userCreditInfo != null) {
                if (null != userCreditInfo.getTotalScore()) {
                    userCreditBean.setTotalScore(userCreditInfo.getTotalScore().setScale(0, RoundingMode.DOWN));
                } else {
                    userCreditBean.setTotalScore(BigDecimal.ZERO);
                }
                if (null != userCreditInfo.getRankLevel() && userCreditInfo.getRankLevel() != 0) {
                    userCreditBean.setRankLevel(userCreditInfo.getRankLevel());
                } else {
                    userCreditBean.setRankLevel(1);
                }
                if (null != userCreditInfo.getLastWeekRankLevel() && userCreditInfo.getLastWeekRankLevel() != 0) {
                    userCreditBean.setLastWeekRankLevel(userCreditInfo.getLastWeekRankLevel());
                }
            } else {
                userCreditBean.setTotalScore(BigDecimal.ZERO);
                userCreditBean.setRankLevel(1);
                userCreditBean.setLastWeekRankLevel(1);
            }
            //3.判断是否弹窗和弹窗类型
            //用户弹窗实体对象
            PopupBean popupBean = new PopupBean();
            //总得分
            BigDecimal totalScore = userCreditBean.getTotalScore();
            //信用分等级
            Integer rankLevel = userCreditBean.getRankLevel();
            //历史信用分等级
            Integer lastWeekRankLevel = userCreditBean.getLastWeekRankLevel();
            //弹窗次数
            Integer popupNum = userCreditInfoPopup.getPopupNum();
            //最近一次弹窗时间
            Date popupTime = userCreditInfoPopup.getPopupTime();
            //最近一次弹窗时间格式化
            String popupTimeStr = (popupTime != null) ? (TimeUtil.formatDate(popupTime)) : "";
            //当天日期
            String today = TimeUtil.formatDate(new Date());
            //查看信用分规则状态：0.未查看 1.已查看
            Integer checkStatus = userCreditInfoPopup.getCheckStatus();
            //用户信用弹窗开关(1.开 2.关)
            Integer isPopupFlag = tytConfigService.getIntValue("user:credit:info:is:popup", 1);
            if (isPopupFlag == 2 || null == totalScore || totalScore.equals(BigDecimal.ZERO) || popupNum >= 2
                    || checkStatus == 1 || today.equals(popupTimeStr)) {
                popupBean.setIsPopup(false);
                //不弹窗,直接返回
                return ResultMsgBean.successResponse(popupBean);
            } else {
                popupBean.setIsPopup(true);
            }
            //判断用户信用等级是否提升
            if (rankLevel != null && lastWeekRankLevel != null
                    && (rankLevel.intValue() > lastWeekRankLevel.intValue())) {
                popupBean.setPopupType(1); //1.等级更新通知
            } else {
                popupBean.setPopupType(2); //2.信用分更新通知
            }
            //信用分信息
            popupBean.setUserCreditBean(userCreditBean);

            //4.更新弹窗次数、最近一次弹窗时间
            userCreditInfoPopup.setPopupNum(popupNum + 1);
            userCreditInfoPopup.setPopupTime(new Date());
            userCreditInfoPopup.setMtime(new Date());
            userCreditInfoPopupService.update(userCreditInfoPopup);

            logger.info("根据userId查询信用弹窗信息成功,用户userId:{},信用弹窗信息为:{}", userId, JSON.toJSONString(popupBean));
            return ResultMsgBean.successResponse(popupBean);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("根据userId查询信用弹窗信息失败,用户userId:{},错误信息为：{}", userId, e.getMessage());
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "查询信用弹窗信息失败！");
        }
    }

    /**
     * @param userId      用户ID
     * @param checkStatus 查看信用分规则状态：1.已查看 2.未查看
     * @return com.tyt.model.ResultMsgBean
     * @description 更新信用分规则查看状态接口
     * <AUTHOR>
     * @date 2022/9/21 17:29
     */
    @RequestMapping(value = "updatePopupCheckStatus")
    @ResponseBody
    public ResultMsgBean updatePopupCheckStatus(Long userId, Integer checkStatus) {
        try {
            if (Objects.isNull(userId)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
            }
            if (Objects.isNull(checkStatus)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "查看信用分规则状态不能为空");
            }
            //1.判断用户信用弹窗信息是否为空
            UserCreditInfoPopup userCreditInfoPopup = userCreditInfoPopupService.getPopupByUserId(userId);
            if (userCreditInfoPopup == null) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "用户信用弹窗记录为空");
            }
            //2.更新查看信用分规则状态
            userCreditInfoPopup.setCheckStatus(checkStatus);
            userCreditInfoPopup.setMtime(new Date());
            userCreditInfoPopupService.update(userCreditInfoPopup);

            logger.info("更新查看信用分规则成功,用户userId:{},查看信用分规则状态:{}", userId, checkStatus);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("更新查看信用分规则失败,用户userId:{},查看信用分规则状态：{}", userId, checkStatus);
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "更新查看信用分规则失败");
        }

    }

    /**
     * @param userId userId
     * @return com.tyt.model.ResultMsgBean
     * @description 查询用户标签接口
     * <AUTHOR>
     */
    @RequestMapping(value = {"getUserLabel", "getUserLabel.action"})
    @ResponseBody
    public ResultMsgBean getUserLabel(Long userId) {
        if (userId == null) {
            return ResultMsgBean.failResponse(500, "userId不能为空");
        }
        UserLabel userLabel = userService.getUserLabel(userId);
        return ResultMsgBean.successResponse(userLabel);
    }

    /**
     * 校验手机号是否可注册（是否注销过）
     *
     * @param cellPhone 手机号
     * @return rm
     */
    @RequestMapping(value = "validCellphone", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean validCellphone(@RequestParam(value = "cellPhone") String cellPhone) {
        try {
            return userService.getUserIsCancel(cellPhone);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
        }
    }

    /**
     * 检查小程序是否限制登录的接口
     *
     * @param userId 用户ID
     * @return
     */
    @RequestMapping(value = "wxMiniLoginLimit.action", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean wxMiniLoginLimit(@RequestParam(value = "userId") Long userId) {
        try {
            return userService.getWxMiniLoginLimit(userId);
        } catch (Exception e) {
            logger.info("检查小程序是否限制登录发生错误：", e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
        }
    }


    /**
     * 检查小程序是否限制发货的接口
     *
     * @param userId 用户ID
     * @return
     */
    @RequestMapping(value = "checkLimitPublish.action", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean checkLimitPublish(@RequestParam(value = "userId") Long userId) {
        try {
            return blacklistUserService.checkLimitPublish(userId);
        } catch (Exception e) {
            logger.info("检查小程序是否限制发货发生错误：", e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
        }
    }


    /**
     * 查询代调货主是否授权绑定接口
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "isBindDispatch")
    @ResponseBody
    public ResultMsgBean isBindDispatch(Long userId) {
        try {
            if (Objects.isNull(userId)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
            }
            //代调货主授权绑定结果对象
            BindDispatchResultBean bindDispatchResultBean = new BindDispatchResultBean();

            CsMaintainedCustom csMaintainedCustom = csMaintainedCustomService.getCsMaintainedCustomByUserId(userId);
            if (csMaintainedCustom != null) {
                Long dispatcherId = csMaintainedCustom.getDispatcherId();
                if (dispatcherId != null) {
                    String dispatcherName = csMaintainedCustom.getDispatcherName();
                    bindDispatchResultBean.setDispatcherId(dispatcherId);
                    bindDispatchResultBean.setDispatcherName(dispatcherName);
                    TytInternalEmployee internalEmployee = internalEmployeeService.getById(dispatcherId);
                    if (internalEmployee != null) {
                        bindDispatchResultBean.setDispatcherPhoneNo(internalEmployee.getLoginPhoneNo());
                    }
                }
            }
            logger.info("查询代调货主是否授权绑定-成功,userId:{},bindDispatchResultBean:{}", userId, JSON.toJSONString(bindDispatchResultBean));
            return ResultMsgBean.successResponse(bindDispatchResultBean);
        } catch (Exception e) {
            logger.error("查询代调货主是否授权绑定-失败:", e);
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "查询代调货主是否授权绑定-失败");
        }
    }

    /**
     * 代调货主授权绑定接口
     *
     * @param userId            用户ID
     * @param dispatcherPhoneNo 调度人手机号
     * @return
     */
    @RequestMapping(value = "bindDispatch")
    @ResponseBody
    public ResultMsgBean bindDispatch(Long userId, String dispatcherPhoneNo) {
        try {
            if (Objects.isNull(userId)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
            }
            User user = userService.getByUserId(userId);
            if (user == null) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "用户信息不存在");
            }
            TytInternalEmployee internalEmployee = internalEmployeeService.getByPhone(dispatcherPhoneNo);
            if (internalEmployee == null) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "调度人信息不存在");
            }
            CsMaintainedCustom csMaintainedCustom = csMaintainedCustomService.getCsMaintainedCustomByUserId(userId);
            if (csMaintainedCustom != null && csMaintainedCustom.getDispatcherId() != null) {
                return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "代调货主已授权绑定,请勿重复操作");
            }
            //判断客户维护信息是否存在
            if (csMaintainedCustom != null) {
                csMaintainedCustom = csMaintainedCustomService.updateCustom(internalEmployee, csMaintainedCustom);
            } else {
                csMaintainedCustom = csMaintainedCustomService.saveCustom(user, internalEmployee);
            }
            logger.info("代调货主授权绑定成功,userId:{},csMaintainedCustom:{}", userId, csMaintainedCustom);
            return ResultMsgBean.successResponse(csMaintainedCustom);
        } catch (Exception e) {
            logger.error("代调货主授权绑定失败:", e);
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "查询代调货主是否授权绑定-失败");
        }
    }

}
