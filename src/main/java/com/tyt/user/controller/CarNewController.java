package com.tyt.user.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.infofee.enums.InvoiceServiceProviderEnum;
import com.tyt.infofee.service.TransportEnterpriseLogService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.WalletService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.*;
import com.tyt.plat.entity.base.TytBlockConfig;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.plat.entity.base.TytTransportEnterpriseLog;
import com.tyt.plat.entity.base.TytUserRecord;
import com.tyt.plat.mapper.base.TytTransportOrdersMapper;
import com.tyt.plat.service.base.TytUserRecordService;
import com.tyt.plat.service.block.TytBlockConfigService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportService;
import com.tyt.user.bean.CarLocationRecordBean;
import com.tyt.user.bean.CarMergeBean;
import com.tyt.user.bean.MyFullCarListBean;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.querybean.UserCarBean;
import com.tyt.user.service.*;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSwitchUtil;
import com.tytrecommend.model.TytPreferenceNew;
import com.tytrecommend.recommend.service.PreferNewService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;


@Controller
@RequestMapping("/plat/carNew")
public class CarNewController extends BaseController {

	@Resource(name = "carService")
	private CarService carService;
	
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "transportService")
	TransportService transportService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "walletService")
	private WalletService walletService;
	
	@Resource(name = "tytCarLocationLogService")
	private TytCarLocationLogService tytCarLocationLogService;
	

	@Resource(name = "preferNewService")
	private PreferNewService preferNewService;
	@Resource(name = "carDriverPhoneInfoService")
	private CarDriverPhoneInfoService carDriverPhoneInfoService;

	@Resource(name = "carDetailTailService")
	private CarDetailTailService carDetailTailService;

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@Autowired
	private TytTransportOrdersMapper tytTransportOrdersMapper;

	@Autowired
	private TytBlockConfigService tytBlockConfigService;

	@Autowired
	private TytUserRecordService tytUserRecordService;

	private static final String BLOCK_CONFIG = "block_config";

	private static final Integer ONE = 1;


	@Autowired
	private TransportEnterpriseLogService transportEnterpriseLogService;
	/**
	 * 查询车辆信息接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/get")
	public void get(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);

			if(StringUtils.isBlank(params.get("userId"))||StringUtils.isBlank(params.get("id"))){
				backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "参数缺失", null, 0);
				return;
			}
			Long userId = Long.parseLong(params.get("userId"));
			String condition = "userId_" + userId + "单个车辆信息查询 ";
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("id");
				}
			};
			if (!validateArguments(condition, request, response, params, names)){
				return;
			}

			/* 取相关参数 */
			Long carId = Long.parseLong(params.get("id"));
			/* 根据Id查询用户 车辆信息包含偏好信息 */
			// Car car = carService.getById(carId);
			QueryCar queryCar = carService.getQueryCarNew(carId);

			logger.info(condition + "成功");
			backResponse(request, response, ReturnCodeConstant.OK, "查询信息成功", queryCar, 0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}

	/**
	 * 所有车辆
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = {"/identity/get", "/identity/get.action"})
	public void identityGetList(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			String userIdStr = params.get("userId");
			String clientVersionStr = params.get("clientVersion");
			if(StringUtils.isBlank(userIdStr) || StringUtils.isBlank(clientVersionStr)){
				backResponse(request, response, ReturnCodeConstant.ERROR, "缺少参数", null, 0);
				return;
			}
			Long userId = Long.parseLong(userIdStr);
			String condition = "userId_" + userId + "所有车辆获取 版本号为" + clientVersionStr;
			String auth = params.get("auth");
			/* 业务参数获取 */
			/* 查询信息 */
			// 判断版本号是否为精准货源推荐以前的版本
			int clientVersion = tytConfigService.getIntValue("clientVersion");
			List<QueryCar> cars = new ArrayList<QueryCar>();
			if (Integer.parseInt(clientVersionStr) <= clientVersion)
				cars = carService.getQueryAllOld(userId, auth);
			else
				cars = carService.getQueryAllNew(userId, auth);
			backResponse(request, response, ReturnCodeConstant.OK, "车辆信息查询成功", cars, 0);
			logger.info(condition + "成功");
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}
	@RequestMapping(value = "/getCarListForPayPage")
	@ResponseBody
	public ResultMsgBean getCarListForPayPage(Long userId,String auth) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
		try {
			logger.info("identityGetListForPc userId is {},auth is {}",userId,auth );
			List<QueryCar> cars = carService.getQueryAllNew(userId, auth);
			cars.sort((a,b)->{
			if(a.getAuth().equals("1")){
				return -1;
			}else if(b.getAuth().equals("1")){
				return 1;
			}else{
				return 0;
			}
		});
			String remark = carIntercept(userId,cars);
			if(StringUtils.isNotEmpty(remark)){
				resultMsgBean.setHatch(remark);
			}
			logger.info("getCarListForPayPage carService getQueryAllNew return is {}",cars );
			resultMsgBean.setData(cars);
			return resultMsgBean;
		} catch (Exception e) {
			logger.info("plat identityGetListForPc carService getQueryAllNew Error msg is {}",e);
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("失败");
			return resultMsgBean;
		}
	}

	public String carIntercept(Long userId,List<QueryCar> cars) {
		String remark = "";
		try {

			//查询身份判断
			User user = userService.getByUserId(userId);

			//查询是否有符合的配置，没有的话直接跳过
			TytBlockConfig config = tytBlockConfigService.getByUserConfig(user);
			if (null == config) {
				return null;
			}
			//透传文案
			TytUserRecord recordCode = tytUserRecordService.getByUserIdAndCode(userId, BLOCK_CONFIG);
			remark = getRemark(recordCode, config,userId);
			//当单车认证接单上限为空时,只返回透传文案
			if(null == config.getReceivingOrders()){
				return remark;
			}
			//车辆认证失败的提醒
			for(QueryCar car : cars){
				if(!"2".equals(car.getAuth())){
					continue;
				}
				Date date = new Date();
				//审核失败的车辆查询历史单量提醒

				Integer count = tytTransportOrdersMapper.getPayUserIdCarIdCTime(userId,car.getId().longValue(),config.getOpenTime(),date);
				logger.info("userId【{}】，车辆id【{}】，开始时间【{}】，当前时间【{}】,count【{}】",userId,car.getId().longValue(),config.getOpenTime(),date,count);
				int num = config.getReceivingOrders() - count;
				if( 0 > num){
					num = 0;
				}
				car.setRemark("为提高车货匹配且控制平台倒货等行为,接单需填写证件齐全的真实承运车辆,证件不全车辆最多可接单"+config.getReceivingOrders()+"次,超出后须完成车辆认证方可接单。\n" +
						"车辆"+car.getHeadCity()+car.getHeadNo()+"当前可接单次数剩余"+num+"次,请尽快完成车辆认证.");
			}

		}catch (Exception e) {
			logger.info("查询阻断信息失败，用户id【{}】", userId);
		}
		return remark;
	}

	/**
	 * 根据记录返回透传文案
	 * @param code
	 * @param config
	 * @return String
	 */
	public String getRemark(TytUserRecord code,TytBlockConfig config,Long userId){
		//为空则提示
		String title = "为提高车货匹配且控制平台倒货等行为，平台对接单车辆做出如下规定：\n";
		String content = "超出后将限制接单，请填写真实承运车辆或完成相关车辆认证。";
		String remark = "";
		if(null == code || !config.getId().toString().equals(code.getValue())){
			if(null != config.getBlock() && null != config.getReceivingOrders()){
				remark ="同一个车，每日最多可接"+config.getBlock()+"单\n" +
						"同一个车（未认证），累计可接"+config.getReceivingOrders()+"单\n" ;
			} else if (null != config.getBlock()) {
				remark ="同一个车，每日最多可接"+config.getBlock()+"单\n" ;
			}else{
				remark ="同一个车（未认证），累计可接"+config.getReceivingOrders()+"单\n" ;
			}
			if(StringUtils.isNotEmpty(remark)){
				remark = title + remark + content;
			}
			tytUserRecordService.saveRecord(userId,BLOCK_CONFIG,1,config.getId().toString());
		}
		return remark;
	}

	/**
	 * 根据用户信息获取用户车辆列表
	 * @param userId
	 * @param auth
	 * @return
	 */
	@RequestMapping(value = "/getCarListForPc")
	@ResponseBody
	public ResultMsgBean getCarListForPc(Long userId,String auth) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
		try {
			logger.info("identityGetListForPc userId is {},auth is {}",userId,auth );
			List<QueryCar> cars = carService.getQueryAllNew(userId, auth);
			logger.info("identityGetListForPc carService getQueryAllNew return is {}",cars );
			resultMsgBean.setData(cars);
			return resultMsgBean;
		} catch (Exception e) {
			logger.info("plat identityGetListForPc carService getQueryAllNew Error msg is {}",e);
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("失败");
			return resultMsgBean;
		}
	}

	/**
	 * 车辆找货开关
	 * 
	 * 
	 */
	@RequestMapping(value = "/identity/updateOnOff")
	public void updateOnOff(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			Long userId = Long.parseLong(params.get("userId"));
			String condition = "userId_" + userId + "车辆找货开关更新 ";
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("id");
					add("findGoodOnOff");
				}
			};
			if (!validateArguments(condition, request, response, params, names))
				return;
			/* 取相关参数 */
			String findGoodOnOff = params.get("findGoodOnOff");
			Long id = Long.parseLong(params.get("id"));
			//5301版本之后不需要判断直接就可以更改
//			int version = tytConfigService.getIntValue("car_login_youhua_version");
//			int currentVersion =  Integer.parseInt(params.get("clientVersion"));
//			if(currentVersion<=version) {
				// 判断是否是开启，如果是开启请求 会对此用户进行是否认证通过交过会员费,是否到期，是否设置偏好等验证
			if ("1".equals(findGoodOnOff)) {
				TytPreferenceNew tytPre = preferNewService.newFindPreferenceByCarId(id);
				if (tytPre == null) {
					logger.info(condition + "失败");
					backResponse(request, response, ReturnCodeConstant.NO_SET_PREFER, "请设置车辆找货偏好", null, 0);
					return;
				}
				Car car = carService.getById(id);
				if (car.getAuth().equals("0")) {
					logger.info(condition + "失败");
					backResponse(request, response, ReturnCodeConstant.AUTH_ISGOING, "客服加紧审核中，请耐心等待", null, 0);
					return;
				}
				if (car.getAuth().equals("2")) {
					logger.info(condition + "失败");
					backResponse(request, response, ReturnCodeConstant.AUTH_FAIL, "请重新提交车辆认证", null, 0);
					return;
				}
				// 判断是否交费 并且会员费没到期
				User user = userService.getById(userId);
				if (TimeUtil.addDay(TimeUtil.formatDate(new Date(user.getEndTime().getTime())), 1).getTime() < System.currentTimeMillis()) {
					logger.info(condition + "失败");
					backResponse(request, response, ReturnCodeConstant.NOT_IS_VIP, "开通会员尊享好货推荐", null, 0);
					return;
				}

			}
//			}
			carService.updateOnOffNew(id, findGoodOnOff);
			logger.info(condition + "成功");
			backResponse(request, response, ReturnCodeConstant.OK, "车辆找货开关设置成功", null, 0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}
	@RequestMapping(value = "getAddrList")
	public void getAddrList(HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> params;
		try {
			params = parseRequestParams(request);
			Long carId = Long.parseLong(params.get("carId"));
			//配置 获取最近{pageSize}条数据
			int pageSize = tytConfigService.getIntValue("car_location_least_pageSize");
			List<CarLocationRecordBean> records = tytCarLocationLogService.getList(carId,pageSize);
			backResponse(request, response, ReturnCodeConstant.OK, "车辆位置信息获取成功", records, 0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
		
	}
	
	/**
	 * 获取司机电话信息
	 * @param id 车辆id
	 * @return
	 */
	@RequestMapping(value="/getDriverPhoneInfo")
	@ResponseBody
	public ResultMsgBean getDriverPhoneInfo(
			@RequestParam(value="id", required=true) Long id) {
		ResultMsgBean result =new ResultMsgBean();
		try {
			TytCarDriverPhoneInfo driverPhone=carDriverPhoneInfoService.getByCarId(id);
			result.setCode(ReturnCodeConstant.OK);
			result.setData(driverPhone);
			result.setMsg("查询信息成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器错误");
		}
		return result;
	}
	/**
	 * 司机电话保存
	 * @param driverPhone
	 * @return
	 */
	@RequestMapping(value="/driverPhone/save")
	@ResponseBody
	public ResultMsgBean updateDriverPhoneInfo(TytCarDriverPhoneInfo driverPhone) {
		ResultMsgBean result =new ResultMsgBean();
		try {
			carDriverPhoneInfoService.updateDriverPhoneInfo(driverPhone);
			result.setCode(ReturnCodeConstant.OK);
			result.setMsg("车辆信息添加成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器错误");
		}
		return result;
		
	}

	/**
	 * 我的车辆列表
	 *
	 * @param baseParameter
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/myCarList", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean myCarList(BaseParameter baseParameter,String isDispatch,
								   HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, null);
		try {
			//获取用户Id
			Long userId = baseParameter.getUserId();
			if (userId == null||userId<=0) {
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg("用户Id不能为空！");
				return resultMsgBean;
			}
			//根据传入的参数，查询我的车辆列表
			String clientVersion = baseParameter.getClientVersion();
			Integer version=6050;
			if (StringUtils.isNotBlank(clientVersion)){
				version=Integer.valueOf(clientVersion);
			}
			MyFullCarListBean myCarList = carService.getMergeMyCarList(userId,isDispatch,version);

			//列表排序，待完善的排在最前面
			sortCarListByNeedImproven(myCarList);

			resultMsgBean.setCode(ReturnCodeConstant.OK);
			resultMsgBean.setData(myCarList);
		} catch (Exception e) {
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			resultMsgBean.setMsg("服务器错误");
			logger.error("查询车辆列表发生错误", e);
			e.printStackTrace();
		}
		return resultMsgBean;
	}

	/**
	 * 车辆列表排序（待完善的排在最前面）
	 * @param myCarList 车辆列表返回总信息
	 */
	private void sortCarListByNeedImproven(MyFullCarListBean myCarList) {
		if (myCarList != null && CollectionUtils.isNotEmpty(myCarList.getCarList())) {
			List<CarMergeBean> needImprovenCarList = new ArrayList<>();
			List<CarMergeBean> noNeedImprovenCarList = new ArrayList<>();
			for (CarMergeBean carMergeBean : myCarList.getCarList()) {
				if (carMergeBean.getCarIsNeedImprovenData()) {
					needImprovenCarList.add(carMergeBean);
				} else {
					noNeedImprovenCarList.add(carMergeBean);
				}
			}
			needImprovenCarList.addAll(noNeedImprovenCarList);
			myCarList.setCarList(needImprovenCarList);
		}
	}

	/**
	 * 我的车辆数量及当前钱包金额
	 *
	 * @param baseParameter
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/myCarNumberAndBalance", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean myCarNumberAndBalance(BaseParameter baseParameter,
											   HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, null);
		try {
			//获取用户Id
			Long userId = baseParameter.getUserId();
			String ticket = baseParameter.getTicket();
			Map<String, Object> userInfoMap = new HashMap<String, Object>();
			Boolean isLogin = false;
			if (userId != null && userId != 0&!Strings.isNullOrEmpty(ticket)) {
				isLogin = true;
			}
			if (!isLogin) {
				userInfoMap.put("carNumber", 0);
				userInfoMap.put("totalRemaining", 0.00);
			} else {
				//根据传入的参数userId，查询我的车辆数量
				int carNum = carService.getCarNum(userId);

				boolean carIsNeedImprovenData = makeCarIsNeedImprovenData(userId);

				BigDecimal totalRemaining = new BigDecimal(BigInteger.ZERO);
				//请求新的账户余额信息
				if(TytSwitchUtil.isManBangVersion()){
					totalRemaining = walletService.getManBangUserAccountBalance(Long.toString(userId),ticket);
				}else
				if(TytSwitchUtil.isTpayVersion()){
					totalRemaining = walletService.getUserAccountBalance(Long.toString(userId));
				}else {
					totalRemaining = walletService.getUserSumWalletAccount(Long.toString(userId));
					//totalRemaining = totalRemaining.divide(new BigDecimal("100.00"), 2, RoundingMode.HALF_UP);
					//旧版本展示余额为0
					totalRemaining = new BigDecimal(0);
				}
				//获取货源发布中的发布条数
				Long myTodayGoodCount=transportService.getMyPublishNew(userId, 1,null);
				userInfoMap.put("carReminder",carNum ==0?1:0);
				userInfoMap.put("carNumber",carNum);
				userInfoMap.put("carIsNeedImprovenData",carIsNeedImprovenData);
				userInfoMap.put("totalRemaining", totalRemaining.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
				userInfoMap.put("myTodayGoodCount", myTodayGoodCount);
			}
			resultMsgBean.setCode(ReturnCodeConstant.OK);
			resultMsgBean.setData(userInfoMap);
		} catch (Exception e) {
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			resultMsgBean.setMsg("服务器错误");
			logger.error("用户:"+baseParameter.getUserId()+",版本号:"+baseParameter.getClientVersion()+"查询车辆数量及钱包金额发生错误", e);
			e.printStackTrace();
		}
		return resultMsgBean;
	}

	/**
	 * 查询该用户下是否有待完善信息的车辆
	 * @param userId
	 * @return true：属于待完善车辆；false：不属于
	 */
	private boolean makeCarIsNeedImprovenData(Long userId) {
		List<Car> localCars = carService.getListByUserId(userId);
		if (CollectionUtils.isNotEmpty(localCars)) {
			List<Long> carIdList = localCars.stream().map(Car::getId).filter(Objects::nonNull).collect(Collectors.toList());
			if (carIdList.isEmpty()) {
				return false;
			}
			List<CarDetailTail> carDetailTails = carDetailTailService.selectCarDetailTailByCarIdList(carIdList);
			Map<Long, Integer> carDetailTailCarIdIsPureFlatMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(carDetailTails)) {
				for (CarDetailTail carDetailTail : carDetailTails) {
					carDetailTailCarIdIsPureFlatMap.put(carDetailTail.getCarId(), carDetailTail.getIsPureFlat());
				}
			}
			//判断该用户是否有车辆需要完善信息，只要有一辆车需要完善信息，carIsNeedImprovenData字段就返回true
			if (CollectionUtils.isNotEmpty(localCars)) {
				for (Car localCar : localCars) {
					localCar.setIsPureFlat(carDetailTailCarIdIsPureFlatMap.get(localCar.getId()));
					if (carService.carIsNeedImprovenData(localCar)) {
						return true;
					}
				}
			}
		}
		return false;
	}

	/**
	 * 获取调度车辆详情
	 * @param id
	 * @return
	 */
	@RequestMapping("/getDispatchCar")
	@ResponseBody
	public ResultMsgBean getDispatchCar(@RequestParam("id") Long id){
		ResultMsgBean result =new ResultMsgBean();
		if (id==null){
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("参数有误");
		}
		try {
			Car dispatchCar = carService.updateDispatchCar(id);
			if (dispatchCar==null){
				result.setCode(ReturnCodeConstant.ERROR);
				result.setMsg("车辆不存在");
			}
			result.setCode(ReturnCodeConstant.OK);
			result.setData(dispatchCar);
			result.setMsg("查询成功");
		}catch (Exception e){
			e.printStackTrace();
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器错误");
		}
		return result;
	}

	/**
	 *车辆和司机绑定接口
	 * @param carId
	 * @param driverId
	 * @return
	 */
	@RequestMapping("/carDriverBind")
	@ResponseBody
	public ResultMsgBean carDriverBind(Long carId,Long driverId,Long userId){
		ResultMsgBean result =new ResultMsgBean();
		try {
			if (carId==null||driverId==null){
				result.setCode(ReturnCodeConstant.ERROR);
				result.setMsg("参数有误！");
			}else {
				carService.updateCarDriverBind(carId, driverId,userId);

				int dispatchEnable = tytConfigService.getIntValue("dispatch_service_enable",1);
				if(dispatchEnable == 0){
					result.setCode(ReturnCodeConstant.OK);
					result.setMsg("操作成功");
					return result;
				}

				// 3/26 要下线特运通大件运输智能调度系统，先注释掉下面代码，过三个月可以删除
				/*String controlDomin = AppConfig.getProperty("control.server.domin");
				String secret = AppConfig.getProperty("tyt.private.key");
				try {

					String cellPhone = userService.getByUserId(userId).getCellPhone();
					Map<String, Object> params=new HashMap<>();
					params.put("ownerPhone", cellPhone);
					String sign = SignUtil.sign(params, secret);
					params.put("sign",sign);
					String response = HttpUtil.post(controlDomin + "/app/vehicle/informSyncCar", JSON.toJSONString(params));
					logger.info("通知调度车辆审核通过接口，返回信息{}",response);
				}catch (Exception e){
					e.printStackTrace();
					logger.info("通知调度车辆审核通过接口失败，异常{}",e.getMessage());
				}*/

				result.setCode(ReturnCodeConstant.OK);
				result.setMsg("操作成功");
			}
		}catch (Exception e){
			e.printStackTrace();
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器错误");
		}
		return result;
	}

	/**
	 * 修改车辆电话
	 * @param tailPhone
	 * @param headPhone
	 * @param id
	 * @return
	 */
	@RequestMapping("/updateCarPhone")
	@ResponseBody
	public ResultMsgBean updateCarPhone(String tailPhone,String headPhone,Long id){
		if (id==null){
			return new ResultMsgBean(ReturnCodeConstant.ERROR,"参数有误");
		}
		try {
			Integer integer = carService.updateCarPhone(tailPhone, headPhone, id);
			if (integer>0){
				return new ResultMsgBean(ReturnCodeConstant.OK,"修改成功");
			}else {
				return new ResultMsgBean(ReturnCodeConstant.ERROR,"修改失败");
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("调度车辆详情修改接口异常：{}",e.getMessage());
			return new ResultMsgBean(ReturnCodeConstant.ERROR,"修改失败");
		}
	}


	/**
	 * 获取已认证的挂车列表接口
	 * @param userId
	 * @return
	 */
	@RequestMapping("/getAuthTailList")
	@ResponseBody
	public ResultMsgBean getAuthTailList(Long userId){
		ResultMsgBean result =new ResultMsgBean();
		try {
			if (userId == null||userId<=0) {
				result.setCode(ReturnCodeConstant.ERROR);
				result.setMsg("用户Id不能为空！");
				return result;
			}

			List<QueryCar> authTailList = carService.getAuthTailList(userId, "1");
			authTailList = authTailList.stream().collect(
					Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(QueryCar::getTailCity).thenComparing(QueryCar::getTailNo))), ArrayList::new));
			int size = authTailList.size();
			for (int i = size - 1; i >= 0; i--) {
				QueryCar queryCar = authTailList.get(i);
				if(StringUtils.isNotEmpty(queryCar.getTailNo())){
					String tailNo = queryCar.getTailNo();
					if(!Objects.equals(tailNo.substring(tailNo.length()-1),"超") && !Objects.equals(tailNo.substring(tailNo.length()-1),"挂")){
						authTailList.remove(i);
					}
				}
			}
			result.setData(authTailList);
			result.setCode(ReturnCodeConstant.OK);
			result.setMsg("操作成功");
		}catch (Exception e){
			e.printStackTrace();
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器错误");
		}
		return result;
	}


	/**
	 * 符合开票货源的车辆
	 * @param userId 用户id
	 * @param id 货源id
	 * @return ResultMsgBean
	 */
	@GetMapping("/goods/getCarList")
	@ResponseBody
	public ResultMsgBean getCarList(Long userId, Long id) {
		if(CommonUtil.hasNull(userId) || CommonUtil.hasNull(id)){
			return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
		}
		String dateContent = getDateContent(id);
		if(StringUtils.isNotEmpty(dateContent)){
			return ResultMsgBean.failResponse(ResponseEnum.request_error.info(dateContent));
		}

		try {
			//三方货源
			TytTransportEnterpriseLog log = transportEnterpriseLogService.getBySrcMsgId(id);
			Boolean isSelfInvoice = checkSubict(id, log);
			//本平台开票货源
			Map<String,List<UserCarBean>> map = new HashMap<>();
			logger.info("开票货源srcMsgId: {} isSelfInvoice: {} ", id, isSelfInvoice);
			if(isSelfInvoice){
				String weight = getWeight(id);
				map = carService.getCarList(userId, id,weight);
			}else{
				TransportMain main = transportMainService.getById(id);
				map = carService.getCarInvoiceList(userId, id, main,log);
			}
			return ResultMsgBean.successResponse(map);
		} catch (Exception e) {
			logger.error("查询车辆错误",e);
			return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
		}
	}



	public Boolean checkSubict(Long id, TytTransportEnterpriseLog log){
		//三方货源
		Long logId = 0L;
		if(null != log){
			logId = log.getInvoiceSubjectId();
		}
		String data = tytConfigService.getStringValue("invoice_subject_data");
		String subiectData = data.split(",")[0];
		return subiectData.equals(logId.toString());
	}


	/**
	 * 符合开票货源车辆的司机
	 * @param transportId 货源id
	 * @param id 车辆id
	 * @param userId 用户id
	 * @return ResultMsgBean
	 */
	@GetMapping("/goods/carUserList")
	@ResponseBody
	public ResultMsgBean getCarList(Long transportId, Long id, Long userId, Integer type) {
		//参数判断
		if (CommonUtil.hasNull(userId) || CommonUtil.hasNull(transportId)) {
			return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
		}
		TransportMain main = transportMainService.getById(transportId);
		if (null == main) {
			return ResultMsgBean.failResponse(ResponseEnum.request_error.info("货源信息为空"));
		}
		if (null == main.getInvoiceTransport() || 0 == main.getInvoiceTransport()) {
			return ResultMsgBean.failResponse(ResponseEnum.request_error.info("非开票货源"));
		}
		try {
			Map<String, List<TytInvoiceDriver>> map = carService.getCarUserList(id, userId, type, main);
			return ResultMsgBean.successResponse(map);
		} catch (Exception e) {
			logger.error("查询车辆错误", e);
			return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
		}
	}


	/**
	 * 参数判断
	 * @param id 货源id
	 * @return String
	 */
	public String getDateContent(Long id){
		String content = "";
		if (null == id) {
			content = "基础参数错误";
			return content;
		}

		TransportMain main = transportMainService.getById(id);
		if (null == main) {
			content = "货源信息为空";
			return content;
		}
		if (null == main.getInvoiceTransport() || 0 == main.getInvoiceTransport()) {
			content = "非开票货源";
			return content;
		}

		return content;
	}

	/**
	 * 获取货源重量
	 * @param id 货源id
	 * @return String
	 */
	public String getWeight(Long id){
		TransportMain main = transportMainService.getById(id);

		return main.getWeight();
	}


	/**
	 * 
	 * @param id 货源id
	 * @param code 车辆id
	 * @param invoiceCode 司机id
	 * @return ResultMsgBean
	 */
	@RequestMapping("/goods/carInvoiceType")
	@ResponseBody
	public ResultMsgBean getCarList(Long id,Long code, Long invoiceCode) {
		//参数判断
		if (CommonUtil.hasNull(id)) {
			return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
		}
		String dateContent = getDateContent(id);
		if(StringUtils.isNotEmpty(dateContent)){
			return ResultMsgBean.failResponse(ResponseEnum.request_error.info(dateContent));
		}
		try {
			//三方货源
			TytTransportEnterpriseLog log = transportEnterpriseLogService.getBySrcMsgId(id);
			Boolean isSelfInvoice = checkSubict(id, log);
			logger.info("开票货源srcMsgId: {} isSelfInvoice: {} ", id, isSelfInvoice);
			Boolean carType = false;
			//本平台开票货源
			if(isSelfInvoice){
				 carType = carService.getCarinvoiceType(code,invoiceCode);
			}else{
				//默认返回true
				if(InvoiceServiceProviderEnum.XHL.getCode().equals(log.getServiceProviderCode())){
					return ResultMsgBean.successResponse(true);
				}
				 TransportMain main = transportMainService.getById(id);
				 carType = carService.getSubjectCarinvoiceType(code,invoiceCode,main.getUserId());
			}
			return ResultMsgBean.successResponse(carType);
		} catch (Exception e) {
			logger.error("准驾车型匹配失败", e);
			return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
		}
	}

	/**
	 * 根据所选的车辆id校验阻断阈值
	 * @param carId 车辆id
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = {"/getCheckCar", "/getCheckCar.action"})
	@ResponseBody
	public ResultMsgBean getCheckCar(@RequestParam Long carId) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, null);
		try {
			//查询身份判断

			Car car = carService.getById(carId);
			//查不到车辆信息，直接返回
			if (null == car) {
				return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
			}
			User user = userService.getByUserId(car.getUserId());
			if (null == user) {
				return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
			}
			resultMsgBean = carService.getCheckCar(car, user);
			return resultMsgBean;
		} catch (Exception e) {
			logger.error("车辆选择校验失败", e);
			//return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
		}
		return resultMsgBean;
	}


	/**
	 * 校验车牌号是否必填
	 * @param type 车牌号填写状态
	 * @param userId 用户id
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = {"/getBlockCar", "/getBlockCar.action"})
	@ResponseBody
	public ResultMsgBean getBlockCar(String type, @RequestParam Long userId) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, null);
		try {
			//查询身份判断
			User user = userService.getByUserId(userId);
			if (null == user) {
				return new ResultMsgBean(ReturnCodeConstant.ERROR, "无用户信息");
			}
			//查询是否有符合的配置，没有的话直接跳过
			TytBlockConfig config = tytBlockConfigService.getByUserConfig(user);
			if (null == config) {
				return resultMsgBean;
			}
			//必填车牌配置而且未填，返回提醒
			if (ONE.equals(config.getLicensePlate()) && StringUtils.isEmpty(type)) {
				resultMsgBean.setCode(ReturnCodeConstant.BLOCK_ERROR);
				resultMsgBean.setMsg("请填写承运车牌号");
			}
			return resultMsgBean;
		} catch (Exception e) {
			logger.error("车辆必填校验失败", e);
			//return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
		}
		return resultMsgBean;
	}
}
