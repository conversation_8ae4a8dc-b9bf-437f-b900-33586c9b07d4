package com.tyt.user.controller;

import cn.hutool.core.util.ObjectUtil;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.*;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.service.ServicePermissionService;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.enterprise.db.EnterpriseDBService;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.enums.ValidDateFlagEnum;
import com.tyt.plat.service.user.UserVerifyService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.bean.UserEnterpriseAuthBeanInfo;
import com.tyt.user.bean.UserIdentityAuthBean;
import com.tyt.user.bean.UserIdentityAuthBeanNew;
import com.tyt.user.service.*;
import com.tyt.util.Constant;
import com.tyt.util.CreateFileUtil;
import com.tyt.util.ImageUtil;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/plat/user/identity/auth")
public class TytUserIdentityAuthController extends BaseController {
    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService tytUserIdentityAuthService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "tytSourceService")
    private TytSourceService sourceService;

    @Resource(name = "tytUserSubService")
    private TytUserSubService subService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Autowired
    private EnterpriseDBService enterpriseDBService;

    @Autowired
    private UserVerifyService userVerifyService;

    @Autowired
    private BlacklistIdentityCardService blacklistIdentityCardService;



    @Resource(name = "servicePermissionService")
    private ServicePermissionService servicePermissionService;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @RequestMapping(value = {"/save", "/save.action"})
    @ResponseBody
    public ResultMsgBean save(BaseParameter baseParameter, UserIdentityAuthBean userIdentityAuthBean,
                              @RequestParam(required = false) MultipartFile mainUrlPic,
                              @RequestParam(required = false) MultipartFile backUrlPic,
                              @RequestParam(required = false) MultipartFile qualificationsUrlPic,
                              @RequestParam(required = false) MultipartFile licenseUrlPic,
                              @RequestParam(required = false) MultipartFile iPhotoPic) {

        Long userId = baseParameter.getUserId();

        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (StringUtils.isNotBlank(userIdentityAuthBean.getIdCard())) {
                //校验身份是否被拉黑
                boolean checkResult = blacklistIdentityCardService.checkIdCardBlackList(userIdentityAuthBean.getIdCard());
                if (checkResult) {
                    blacklistIdentityCardService.blackListUser(userId, userIdentityAuthBean.getIdCard());
                    return ResultMsgBean.failResponse(ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_CODE, ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_MSG);
                }
            }

            TytUserIdentityAuth tytUserIdentityAuth = tytUserIdentityAuthService.createTytUserIdentityAuth(baseParameter.getUserId(), userIdentityAuthBean,
                    mainUrlPic, backUrlPic, qualificationsUrlPic, licenseUrlPic, iPhotoPic, baseParameter.getClientSign());
//			if(StringUtils.isBlank(tytUserIdentityAuth.getMainUrl())) {
//				rm.setCode(ResultMsgBean.ERROR);
//				rm.setMsg("请重新上传身份证正面照");
//				return rm;
//			}
//			if(StringUtils.isBlank(tytUserIdentityAuth.getiPhotoUrl())) {
//				rm.setCode(ResultMsgBean.ERROR);
//				rm.setMsg("请上传身份证国徽页");
//				return rm;
//			}
            if (!super.isAllImg(mainUrlPic, backUrlPic, qualificationsUrlPic, licenseUrlPic, iPhotoPic)) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("图片格式有误！");
                return rm;
            }

            rm = tytUserIdentityAuthService.save(tytUserIdentityAuth, baseParameter.getClientSign(), false);
        } catch (Exception ex) {

            PlatCommonUtil.printErrorInfo("user_save_error : userId : " + userId, ex);
            rm = ResultMsgBean.failResponse(ex);

        }
        return rm;
    }

    @RequestMapping(value = {"/saveByPath", "/save.action"})
    @ResponseBody
    public ResultMsgBean save(BaseParameter baseParameter, UserIdentityAuthBean userIdentityAuthBean,
                              @RequestParam(required = false) String mainUrlPic,
                              @RequestParam(required = false) String iPhotoPic) {

        Long userId = userIdentityAuthBean.getUserId();
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (StringUtils.isNotBlank(userIdentityAuthBean.getIdCard())) {
                //校验身份是否被拉黑
                boolean checkResult = blacklistIdentityCardService.checkIdCardBlackList(userIdentityAuthBean.getIdCard());
                if (checkResult) {
                    blacklistIdentityCardService.blackListUser(userId, userIdentityAuthBean.getIdCard());
                    return ResultMsgBean.failResponse(ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_CODE, ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_MSG);
                }
            }
            TytUserIdentityAuth tytUserIdentityAuth = createTytUserIdentityAuthByPath(baseParameter.getUserId(), userIdentityAuthBean, mainUrlPic, iPhotoPic, baseParameter.getClientSign());
            if (StringUtils.isBlank(tytUserIdentityAuth.getMainUrl())) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("请重新上传身份证正面照");
                return rm;
            }
            if (StringUtils.isBlank(tytUserIdentityAuth.getiPhotoUrl())) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("请上传身份证国徽页");
                return rm;
            }
            rm = tytUserIdentityAuthService.save(tytUserIdentityAuth, baseParameter.getClientSign(), false);
        } catch (Exception ex) {

            PlatCommonUtil.printErrorInfo("user_save_error : userId : " + userId, ex);
            rm = ResultMsgBean.failResponse(ex);

        }
        return rm;
    }

    /**
     * 上传身份认证图片
     *
     * @param file
     * @return
     * <AUTHOR>
     * @date 2021-05-22 15:07:24
     */
    @RequestMapping(value = {"/saveAuthPic"})
    @ResponseBody
    public ResultMsgBean saveFile(MultipartFile file) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (!super.isAllImg(file)) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("图片格式有误！");
                return rm;
            }

            String path = renamePic(file, "user");
            file.transferTo(new File(AppConfig.getProperty("picture.path.domain") + path));
            ResultMsgBean resultMsgBean = new ResultMsgBean();
            resultMsgBean.setData(path);
            return resultMsgBean;
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * 提交企业认证
     *
     * @return
     * <AUTHOR>
     * @date 2021-05-22 15:07:24
     */
    @RequestMapping(value = {"/saveEnterprise"})
    @ResponseBody
    public ResultMsgBean saveEnterprise(BaseParameter baseParameter, MultipartFile enterprisePic, String enterpriseAuthPath) {
        ResultMsgBean rm = new ResultMsgBean();
        try {

            if (!super.isAllImg(enterprisePic)) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("图片格式有误！");
                return rm;
            }

            String clientSignStr = baseParameter.getClientSign();

            Integer clientSign = null;

            if(StringUtils.isNotBlank(clientSignStr)){
                clientSign = Integer.parseInt(clientSignStr);
            }

            String path = renamePic(enterprisePic, "user");
            enterprisePic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + path));
            tytUserIdentityAuthService.saveEnterprise(baseParameter.getUserId(), path, enterpriseAuthPath, clientSign);
            ResultMsgBean resultMsgBean = new ResultMsgBean();
            String prefix_picture = tytConfigService.getStringValue("prefix_picture", "http://newtest.teyuntong.net/rootdata");
            resultMsgBean.setData(prefix_picture + path);
            return resultMsgBean;
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 接口本来是给5600使用，但是后来由于产品设计原因又改回原有规则，该接口目前处于无用状态
     *
     * @param baseParameter
     * @param trueName      真实姓名
     * @param idCard        身份证号
     * @param mainUrlPic    身份证正面照
     * @param iPhotoPic     本人照
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/saveNew")
    @ResponseBody
    public ResultMsgBean saveNew(BaseParameter baseParameter, String trueName, String idCard,
                                 @RequestParam(required = false) MultipartFile mainUrlPic,
                                 @RequestParam(required = false) MultipartFile iPhotoPic) {
        logger.info("user identity auth 5300 trueName: " + trueName + ", idCard: " + idCard
                + ", mainUrlPic: " + mainUrlPic + ", iPhotoPic: " + iPhotoPic);
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (StringUtils.isEmpty(trueName)) {
                rm.setCode(2002);
                rm.setMsg("trueName不能为空");
            } else if (StringUtils.isEmpty(idCard)) {
                rm.setCode(2002);
                rm.setMsg("idCard不能为空");
            } else {
                if (!super.isAllImg(mainUrlPic, iPhotoPic)) {
                    rm.setCode(ResultMsgBean.ERROR);
                    rm.setMsg("图片格式有误！");
                    return rm;
                }

                // 初始化默认的用户认证信息
                initIdentityAuth(baseParameter.getUserId());
                TytUserIdentityAuth tytUserIdentityAuth = createTytUserIdentityAuthAfter5300(baseParameter.getUserId(), trueName, idCard, mainUrlPic, iPhotoPic);
                rm = tytUserIdentityAuthService.saveAfter5300(tytUserIdentityAuth);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 初始化默认的用户认证信息
     *
     * @param userId
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    private void initIdentityAuth(Long userId) throws IllegalAccessException, InvocationTargetException {
        // 默认为货站
        userService.saveOneLevelIdentityNew(String.valueOf(userId), 1, 3);
    }

    private TytUserIdentityAuth createTytUserIdentityAuthAfter5300(Long userId, String trueName,
                                                                   String idCard, MultipartFile mainUrlPic, MultipartFile iPhotoPic) throws Exception {
        TytUserIdentityAuth tytUserIdentityAuth = new TytUserIdentityAuth();
        tytUserIdentityAuth.setTrueName(trueName);
        tytUserIdentityAuth.setIdCard(idCard);
        User user = userService.getById(userId);
        tytUserIdentityAuth.setUserId(userId);
        tytUserIdentityAuth.setUserClass(user.getUserClass());
        tytUserIdentityAuth.setMobile(user.getCellPhone());
        tytUserIdentityAuth.setIdentityType(user.getIdentityType());
        // 因为存在审核失败重新上传的情况，所以身份证正面照和本人照都是有可能为空的
        if (mainUrlPic != null && !mainUrlPic.isEmpty()) {
            tytUserIdentityAuth.setMainUrl(renamePic(mainUrlPic, "user"));
            mainUrlPic.transferTo(new File(AppConfig.getProperty("picture.path.domain")
                    + tytUserIdentityAuth.getMainUrl()));
        } else {
            tytUserIdentityAuth.setMainUrl(null);
        }
        if (iPhotoPic != null && !iPhotoPic.isEmpty()) {
            tytUserIdentityAuth.setiPhotoUrl(renamePic(iPhotoPic, "user"));
            iPhotoPic.transferTo(new File(AppConfig.getProperty("picture.path.domain")
                    + tytUserIdentityAuth.getiPhotoUrl()));
        } else {
            tytUserIdentityAuth.setiPhotoUrl(null);
        }
        logger.info("user identity auth 5300 tytUserIdentityAuth: " + tytUserIdentityAuth);
        return tytUserIdentityAuth;
    }


    TytUserIdentityAuth createTytUserIdentityAuthByPath(Long userId, UserIdentityAuthBean userIdentityAuthBean, String mainUrlPicPath, String iPhotoPicPath, String clientSign) throws Exception {

        TytUserIdentityAuth tytUserIdentityAuth = new TytUserIdentityAuth();
        BeanUtils.copyProperties(userIdentityAuthBean, tytUserIdentityAuth);
        User user = userService.getById(userId);
        tytUserIdentityAuth.setUserId(userId);
        tytUserIdentityAuth.setMobile(user.getCellPhone());
        if (user.getUserClass() != null && user.getIdentityType() != null) {
            tytUserIdentityAuth.setUserClass(user.getUserClass());
            tytUserIdentityAuth.setIdentityType(user.getIdentityType());
        } else {
            int clientPort = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
            if (clientPort == 1) {
                tytUserIdentityAuth.setUserClass(2);
                tytUserIdentityAuth.setIdentityType(6);
            } else if (clientPort == 2) {
                tytUserIdentityAuth.setUserClass(1);
                tytUserIdentityAuth.setIdentityType(3);
            } else if (clientPort == 3) {
                tytUserIdentityAuth.setUserClass(3);
                tytUserIdentityAuth.setIdentityType(10);
            } else if (clientPort == 4) {
                tytUserIdentityAuth.setUserClass(4);
                tytUserIdentityAuth.setIdentityType(11);
            } else {
                tytUserIdentityAuth.setUserClass(2);
                tytUserIdentityAuth.setIdentityType(6);
            }
        }
        tytUserIdentityAuth.setMainUrl(mainUrlPicPath);
        tytUserIdentityAuth.setiPhotoUrl(iPhotoPicPath);
        return tytUserIdentityAuth;
    }

    /**
     * 重命名图片
     *
     * @param typeName
     * @return
     */
    @Override
    protected String renamePic(MultipartFile pic, String typeName) {
        // String fileSeparator=System.getProperty("file.separator");//获取系统文件分隔符
        String domainurl = "/data/pictures/" + typeName + "/";// 获取文件路径
        CreateFileUtil.createDir(AppConfig.getProperty("picture.path.domain") + domainurl);
        return domainurl + ImageUtil.renameFile(pic.getOriginalFilename());
    }

    /**
     * 查询用户的认证信息
     *
     * @param userId 用户id
     * @return
     */
    @RequestMapping({"/queryIdentity", "/queryIdentity.action", "queryIdentityApplets"})
    @ResponseBody
    public ResultMsgBean queryIdentity(BaseParameter baseParameter, String userId) {
        ResultMsgBean msgBean = new ResultMsgBean();
        msgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
        try {
            if (userId == null) {
                msgBean.setMsg("userId不能为空");
            } else {
                TytUserIdentityAuth identityAuth = tytUserIdentityAuthService.getByUserId(userId);
                if (identityAuth != null) {
                    msgBean.setCode(ReturnCodeConstant.OK);
                    msgBean.setMsg("查询成功");
                    UserIdentityAuthBean userIdentityAuthBean = filter(identityAuth, baseParameter.getClientVersion());
                    msgBean.setData(userIdentityAuthBean);
                } else {
//					msgBean.setMsg("用户id为" + userId + "的用户未进行用户一级身份认证");
                    msgBean.setCode(ReturnCodeConstant.OK);
                    msgBean.setMsg("查询成功");
                    UserIdentityAuthBean userIdentityAuthBean = new UserIdentityAuthBean();
                    userIdentityAuthBean.setIdentityStatus(0);
                    msgBean.setData(userIdentityAuthBean);
                }
            }
        } catch (Exception e) {
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
            e.printStackTrace();
            return msgBean;
        }
        return msgBean;
    }

    /**
     * 查询用户企业认证信息
     *
     * @param userId 用户id
     * @return
     */
    @RequestMapping({"/queryEnterpriseAuthInfo", "/queryEnterpriseAuthInfo.action"})
    @ResponseBody
    public ResultMsgBean queryEnterpriseAuthInfo(BaseParameter baseParameter, String userId) {
        ResultMsgBean msgBean = new ResultMsgBean();
        msgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
        try {
            if (userId == null) {
                msgBean.setMsg("userId不能为空");
            } else {
                TytUserIdentityAuth identityAuth = tytUserIdentityAuthService.getByUserId(userId);
                if (identityAuth == null) {
                    UserEnterpriseAuthBeanInfo userEnterpriseAuthBeanInfo = new UserEnterpriseAuthBeanInfo();
                    userEnterpriseAuthBeanInfo.setUserId(Long.valueOf(userId));
                    userEnterpriseAuthBeanInfo.setEnterpriseAuthStatus(0);
                    msgBean.setData(userEnterpriseAuthBeanInfo);
                    msgBean.setMsg("用户id为" + userId + "的用户未进行用户一级身份认证");
                } else {
                    TytInvoiceEnterprise verifyInfo = enterpriseDBService.getVerifyInfoByUserId(Long.valueOf(userId));
                    if (ObjectUtil.isNotEmpty(verifyInfo)) {
                        UserEnterpriseAuthBeanInfo userEnterpriseAuthBeanInfo = new UserEnterpriseAuthBeanInfo();
                        userEnterpriseAuthBeanInfo.setId(verifyInfo.getId());
                        userEnterpriseAuthBeanInfo.setUserId(verifyInfo.getCertigierUserId());
                        userEnterpriseAuthBeanInfo.setEnterpriseAuthStatus(adjustStatus(verifyInfo.getInfoVerifyStatus()));

                        //适配老版本的路径前缀
                        String licenseUrl = verifyInfo.getLicenseUrl();
                        if (StringUtils.isNotBlank(licenseUrl) && !licenseUrl.startsWith("http")) {
                            String prefix_picture = tytConfigService.getStringValue("prefix_picture", "http://newtest.teyuntong.net/rootdata");
                            userEnterpriseAuthBeanInfo.setEnterpriseAuthLicenseUrl(prefix_picture + licenseUrl);
                        } else {
                            userEnterpriseAuthBeanInfo.setEnterpriseAuthLicenseUrl(licenseUrl);
                        }

                        userEnterpriseAuthBeanInfo.setEnterpriseAuthFailureReason(verifyInfo.getInfoVerifyReason());
                        msgBean.setCode(ReturnCodeConstant.OK);
                        msgBean.setMsg("查询成功");
                        msgBean.setData(userEnterpriseAuthBeanInfo);
                    } else {
                        UserEnterpriseAuthBeanInfo userEnterpriseAuthBeanInfo = new UserEnterpriseAuthBeanInfo();
                        userEnterpriseAuthBeanInfo.setUserId(Long.valueOf(userId));
                        userEnterpriseAuthBeanInfo.setEnterpriseAuthStatus(0);
                        msgBean.setData(userEnterpriseAuthBeanInfo);
                        msgBean.setMsg("用户id为" + userId + "的用户未进行企业认证");
                    }
                }
            }
        } catch (Exception e) {
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
            e.printStackTrace();
            return msgBean;
        }
        return msgBean;
    }

    private Integer adjustStatus(Integer currentStatus) {
        int status = 0;
        if (currentStatus == 1) {
            status = 2;
        }
        if (currentStatus == 2) {
            status = 1;
        }
        if (currentStatus == 3) {
            status = 3;
        }
        if (currentStatus == 4) {
            status = 3;
        }
        return status;
    }

    /**
     * 查询用户的认证信息
     *
     * @param userId 用户id
     * @return
     */
    @RequestMapping("/queryIdentityNew")
    @ResponseBody
    public ResultMsgBean queryIdentityNew(BaseParameter baseParameter, String userId) {
        ResultMsgBean msgBean = new ResultMsgBean();
        msgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
        try {
            if (userId == null) {
                msgBean.setMsg("userId不能为空");
            } else {
                TytUserIdentityAuth identityAuth = tytUserIdentityAuthService.getByUserId(userId);
                TytUserIdentityAuth identityAutyClone = new TytUserIdentityAuth();
                if (identityAuth != null) {
                    BeanUtils.copyProperties(identityAuth, identityAutyClone);
                    msgBean.setCode(ReturnCodeConstant.OK);
                    msgBean.setMsg("查询成功");
                    UserIdentityAuthBeanNew userIdentityAuthBeanNew = filterNew(identityAutyClone);
                    User user = userService.getById(Long.valueOf(userId));
                    // 设置是否为企业货主
                    if (user.getUserClass().intValue() == 1 && user.getIdentityType().intValue() == 2) {
                        userIdentityAuthBeanNew.setIdentityType(2);
                    } else {
                        userIdentityAuthBeanNew.setIdentityType(1);
                    }
                    // 设置销售审核一级身份
                    String deliverTypeOne = user.getDeliver_type_one();
                    // 处理待定、内部员工、无效、未跟踪均显示为：身份待定
                    if (StringUtils.isEmpty(deliverTypeOne) || Integer.valueOf(deliverTypeOne).intValue() >= 10) {
                        userIdentityAuthBeanNew.setDeliverTypeOne(12);
                        userIdentityAuthBeanNew.setDeliverTypeOneName("身份待定");
                    } else {
                        userIdentityAuthBeanNew.setDeliverTypeOne(Integer.valueOf(deliverTypeOne));
                        TytSource source = null;
                        List<TytSource> sourceList = sourceService.getList(" groupCode='user_deliver_type_one' AND value= " + deliverTypeOne + " AND status=0", new PageBean(1, 1));
                        if (sourceList != null && sourceList.size() == 1) {
                            source = sourceList.get(0);
                        }
                        if (source != null) {
                            String sourceName = source.getName();
                            if (StringUtils.isNotEmpty(sourceName)) {
                                sourceName = sourceName.startsWith("运输公司") ? "运输公司或车队" : sourceName;
                            }
                            userIdentityAuthBeanNew.setDeliverTypeOneName(sourceName);
                        }
                    }
                    msgBean.setData(userIdentityAuthBeanNew);
                } else {
                    msgBean.setMsg("用户id为" + userId + "的用户未进行用户一级身份认证");
                }
            }
        } catch (Exception e) {
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
            e.printStackTrace();
            return msgBean;
        }
        return msgBean;
    }

    /**
     * APP5300以上版本调用该方法
     *
     * @param identityAuth
     * @return
     */
    private UserIdentityAuthBeanNew filterNew(TytUserIdentityAuth identityAuth) {
        identityAuth.setExamineUserId(null);
        identityAuth.setId(null);
        identityAuth.setExamineStatus(null);
        identityAuth.setExamineUserName(null);
        identityAuth.setExamineTime(null);
        identityAuth.setSex(null);
        identityAuth.setMainUrl(null);
        identityAuth.setiPhotoUrl(null);
        identityAuth.setLicenseUrl(null);
        identityAuth.setBackUrl(null);
        UserIdentityAuthBeanNew userIdentityAuthBean = new UserIdentityAuthBeanNew();
        BeanUtils.copyProperties(identityAuth, userIdentityAuthBean);
        userIdentityAuthBean.setCurrentTime(new Date());
        if (userIdentityAuthBean.getCtime() == null) {
            userIdentityAuthBean.setCtime(identityAuth.getUtime());
        }
        // 兼容5300之前的版本
        // 如果是板车司机则使用驾驶证正面照的认证结果替换身份证正面照的认证结果(不需要兼容，和身份证正面照用的是同一个字段)
        return userIdentityAuthBean;
    }

    private UserIdentityAuthBean filter(TytUserIdentityAuth identityAuth, String version) {

        ValidDateFlagEnum validDateEnum = userVerifyService.getValidDateFlag(identityAuth.getIdCardLongTerm(), identityAuth.getIdCardValidDate());
        Integer validDateFlag = validDateEnum.getCode();

        //identityAuth.setCtime(null);
        identityAuth.setExamineUserId(null);
        identityAuth.setId(null);
        identityAuth.setExamineStatus(null);
        identityAuth.setExamineUserName(null);
        identityAuth.setExamineTime(null);
        //identityAuth.setUtime(null);
        identityAuth.setSex(null);
        //identityAuth.setUserId(null);

        UserIdentityAuthBean userIdentityAuthBean = new UserIdentityAuthBean();

        BeanUtils.copyProperties(identityAuth, userIdentityAuthBean);
        userIdentityAuthBean.setValidDateFlag(validDateFlag);

        userIdentityAuthBean.setCurrentTime(new Date());
        if (userIdentityAuthBean.getCtime() == null) {
            userIdentityAuthBean.setCtime(identityAuth.getUtime());
        }
        userIdentityAuthBean.setWorkTypeNew(identityAuth.getWorkType());
        if (identityAuth.getWorkType() != null && !"".equals(identityAuth.getWorkType()) && identityAuth.getWorkType().length() > 1) {
            userIdentityAuthBean.setWorkType(identityAuth.getWorkType().substring(0, 1));
        }
        int versionCode = Integer.parseInt(version);
        //如果是企业用户 并且是3310之前版本则把身份证正面照与本人照互换
        if (identityAuth.getIdentityType().intValue() == 2 && identityAuth.getIdentityStatus().intValue() == 3) {

            if (versionCode < 3310) {
                userIdentityAuthBean.setBackUrl(identityAuth.getMainUrl());
                userIdentityAuthBean.setBackStatus(identityAuth.getMainStatus());
                userIdentityAuthBean.setBackFailueReason(identityAuth.getMainFailureReason());

                userIdentityAuthBean.setMainFailureReason(identityAuth.getiPhotoFailureReason());
                userIdentityAuthBean.setMainStatus(identityAuth.getiPhotoStatus());
                userIdentityAuthBean.setMainUrl(identityAuth.getiPhotoUrl());
            }
        }
        //为了兼容android bug
        if (versionCode < 3310 && identityAuth.getIdentityType().intValue() != 2 && identityAuth.getIdentityStatus().intValue() == 3) {
            if (identityAuth.getiPhotoStatus() != null && identityAuth.getiPhotoStatus().intValue() == 2) {
                if (identityAuth.getiPhotoFailureReason() != null && !"".equals(identityAuth.getiPhotoFailureReason())) {
                    /*			userIdentityAuthBean.setBackUrl(identityAuth.getMainUrl());*/
                    userIdentityAuthBean.setBackStatus(identityAuth.getiPhotoStatus());
                    userIdentityAuthBean.setBackFailueReason(identityAuth.getiPhotoFailureReason());
                }
            }
        }
        return userIdentityAuthBean;
    }

    /**
     * 获取用户今日二要素校验次数
     *
     * @param userId userId
     * @return ResultMsgBean
     */
    @GetMapping("/getRealVerifyCount")
    @ResponseBody
    public ResultMsgBean getRealVerifyCount(Long userId) {

        if (CommonUtil.hasNull(userId)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            int realVerifyCount = tytUserIdentityAuthService.getRealVerifyCount(userId);
            return ResultMsgBean.successResponse(realVerifyCount);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getRealVerifyCount_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 更新用户缓存信息
     */
    @RequestMapping("/updateCacheUserInfo")
    @ResponseBody
    public ResultMsgBean updateCacheUserInfo(@RequestParam("userId") Long userId) {
        try {
            User user = userService.getByUserId(userId);
            //修改用户缓存
            cacheService.del(Constant.CACHE_USER_KEY + user.getId());
            /* user信息缓存 */
            cacheService.setObject(Constant.CACHE_USER_KEY + user.getId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("updateCacheUserInfo error :", e);
        }
        return ResultMsgBean.successResponse();
    }


    /**
     * 更新用户缓存信息
     */
    @RequestMapping("/updateUserPermissionResult")
    @ResponseBody
    public ResultMsgBean updateUserPermissionResult(@RequestParam("userId") Long userId) {
        try {
            String permissionKey =  Constant.USER_PERMISSION_REDIS_KEY + userId;
            RedisUtil.del(permissionKey);
            UserPermissionResult result = new UserPermissionResult();
            boolean isUserName = this.authPermissionReturn(Permission.用户昵称显示,userId);
            if (isUserName){
                result.setIsUserNamePower(1);
            }
            boolean isContent = this.authPermissionReturn(Permission.货物内容显示,userId);
            if (isContent){
                result.setIsContentPower(1);
            }
            RedisUtil.setObject(permissionKey,result,0);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("updateCacheUserInfo error :", e);
        }
        return ResultMsgBean.successResponse();
    }


    //查询用户是否有查看货源信息和昵称的权利
    private boolean authPermissionReturn(Permission permission, Long userId) {
        // 1. 获取权益类型的对象
        ServicePermission servicePermission = servicePermissionService.getServicePermission(permission.getId());
        // 1.1 如果bindDep属性为null值，则表示用户拥有此项权益
        if(StringUtils.isBlank(servicePermission.getBindDep())) {
            return true;
        }
        // 2. 查询当前用户指定类型的权益列表
        List<UserPermission> userPermissions = userPermissionService.getListByUserIdAndType(userId, servicePermission.getBindDep());
        //无权益 处理
        if (userPermissions.isEmpty()) { // 无权益
            return false;
        }
        //如果是寄生依赖关系，存在依赖则表示有权益
        if (servicePermission.getParasitic()) {
            if (CollectionUtils.isNotEmpty(userPermissions)) {
                return true;
            }
        }
        return false;
    }
}
