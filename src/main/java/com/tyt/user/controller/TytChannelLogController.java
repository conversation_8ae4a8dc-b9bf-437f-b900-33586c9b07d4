package com.tyt.user.controller;

import java.util.Date;

import javax.annotation.Resource;

import com.tyt.asoIntegralWall.service.AsoIntegralWallService;
import com.tyt.model.AsoIntegralWallLocal;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytChannelLog;
import com.tyt.model.TytSource;
import com.tyt.user.service.TytChannelLogService;
import com.tyt.user.service.TytSourceService;
import com.tyt.util.ChannelLogConstant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;

@Controller
@RequestMapping("/plat/channel")
public class TytChannelLogController extends BaseController {
	
	@Resource(name = "tytChannelLogService")
	private TytChannelLogService tytChannelLogService;
	
	@Resource(name = "tytSourceService")
	private TytSourceService tytSourceService;

    @Resource(name = "asoIntegralWallService")
    private AsoIntegralWallService asoIntegralWallService;
	/**
	 * 渠道采集认证
	 */
	@RequestMapping(value = "/collection")
	@ResponseBody
	public ResultMsgBean collection(BaseParameter baseParameter, TytChannelLog tytChannelLog){
		ResultMsgBean rm = new ResultMsgBean();
		try{
			if(tytChannelLog!=null){
				tytChannelLog.setRecordType(ChannelLogConstant.install);
				tytChannelLog.setCtime(new Date());
                if (tytChannelLog.getChannelCode()!=null){
                    TytSource tytSource = TytSourceUtil.getSourceName("app_channel", String.valueOf(tytChannelLog.getChannelCode()));
                    if (tytSource != null) {
                        tytChannelLog.setChannelName(tytSource.getName());
                    }
                }
				tytChannelLogService.add(tytChannelLog);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("发送成功");
			}else{
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("保存数据不能为空");
			}
		}catch(Exception e){
			logger.error("服务器异常", e);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

    /**
     * IOS采集idfa
     * @param asoIntegralWallLocal 参数
     * @return rm
     */
    @RequestMapping(value = "/idfa/collection")
    @ResponseBody
    public ResultMsgBean idfaCollection(AsoIntegralWallLocal asoIntegralWallLocal){
        ResultMsgBean rm = new ResultMsgBean(200,"保存成功");
        try{
            if (StringUtils.isBlank(asoIntegralWallLocal.getIdfa())){
                rm.setCode(500);
                rm.setMsg("idfa不能为空");
                return rm;
            }
            asoIntegralWallService.saveIdfa(asoIntegralWallLocal);
        }catch(Exception e){
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }




	
	
}
