package com.tyt.user.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.model.PublicResource;
import com.tyt.model.ResultMsgBean;
import com.tyt.payment.service.PayMethodService;
import com.tyt.payment.service.PriceService;
import com.tyt.user.bean.IpInfoBean;
import com.tyt.user.service.PublicResourceService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/resource/")
public class PublicResourceController extends BaseController{
	
	@Resource(name = "payMethodService")
	private PayMethodService payMethodService;
	
	@Resource(name = "priceService")
	private PriceService priceService;
	
	@Resource(name = "publicResourceService")
	private PublicResourceService publicResourceService;
	
	@RequestMapping(value = "pay/price/get")
	public void get(Integer type,String cellPhone,String token,
			@RequestParam(value = "platId", defaultValue = Constant.PLAT_PC+"") Integer platId,
			HttpServletRequest request,HttpServletResponse response){
			
		try{
			if(!StringUtils.hasLength(cellPhone)){
	        	logger.info("resource:get. cellPhone is null.");
				ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"cellPhone is null");
				printJSON(request, response, msgBean);
				return;
	        }
			
			if(type==null){
	        	logger.info("resource:get. type is null.");
				ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
						"type is null");
				printJSON(request, response, msgBean);
				return;
	        }
			
			if (!validateToken(cellPhone, token, request, response)) {
				logger.info("resource:get.token error");
				return;
			}
			
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK,"1");
			switch(type){
			case 1:
				msgBean.setData(payMethodService.getEnabledList(platId));
				break;
			case 2:
				msgBean.setData(priceService.getEnabledList());
				break;
			default:
			}
			printJSON(request, response, msgBean);
			}catch(Exception e){
				logger.info("resource:get.Exception"+cellPhone);
				e.printStackTrace();
			}
		
	}
	/**
	 * 公共资源接口，客户端登陆访问
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = {"global/get", "global/get.action"})
	public void publicGet(HttpServletRequest request,HttpServletResponse response){
		try {
			//根据IP判断是否保定用户
			List<PublicResource> list=publicResourceService.getPublicResourceByIp(request.getHeader("X-Real-IP"));
//			List<PublicResource> list=publicResourceService.getPublicResourceByIp("***************");
			backResponse(request, response, ReturnCodeConstant.OK, "公共资源获取成功", list,0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null,0);
		}
	}
	
	/**
	 * @Description  APP中jsp、H5页面公共资源接口
	 * <AUTHOR>
	 * @Date  2019/5/14 11:06
	 * @Param [request, response]
	 * @return void
	 **/
	@RequestMapping(value = "global/pageGet.action")
	public void pageGet(HttpServletRequest request,HttpServletResponse response){

		try {
			//获取页面所需要的公共资源
			List<PublicResource> list=publicResourceService.getPagePublicResource();
			backResponse(request, response, ReturnCodeConstant.OK, "公共资源获取成功", list,0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null,0);
		}
	}


	@RequestMapping(value = "global/getByParam.action")
	public void getGlobalByParam(String paramName,
					HttpServletRequest request,HttpServletResponse response){
		try {
			//获取页面所需要的公共资源
			PublicResource pagePublicResource=publicResourceService.getGlobalByParam(paramName);
			backResponse(request, response, ReturnCodeConstant.OK, "公共资源获取成功", pagePublicResource,0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null,0);
		}

	}


	@RequestMapping("global/getByParamList.action")
	public void getGlobalByParamList(@RequestParam("paramNameList") List paramNameList,
									 HttpServletRequest request,HttpServletResponse response){
		try {
			//获取页面所需要的公共资源集合
			List<PublicResource> paramList = publicResourceService.getGlobalByParamList(paramNameList);
			backResponse(request, response, ReturnCodeConstant.OK, "公共资源获取成功", paramList,0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null,0);
		}


	}

	/**
	 * 根据ip获取特运通对应的定位信息
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("getIpInfo")
	public ResultMsgBean getIpInfo(HttpServletRequest request){
		ResultMsgBean msgBean = new ResultMsgBean();
		try{
			IpInfoBean ipInfo=publicResourceService.getInfo(request.getHeader("X-Real-IP"));
			msgBean.setCode(ResultMsgBean.OK);
			msgBean.setData(ipInfo);
			logger.info("detailLocationByMapFromDB:{}", JSON.toJSONString(ipInfo));
		}catch(Exception e){
			e.printStackTrace();
			msgBean.setCode(ResultMsgBean.ERROR);
			msgBean.setMsg("服务器错误");
		}
		return msgBean;
	}
}
