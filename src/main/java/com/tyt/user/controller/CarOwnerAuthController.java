package com.tyt.user.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytCarOwnerAuth;
import com.tyt.user.bean.CarOwnerAuthReq;
import com.tyt.user.bean.CarOwnerAuthVO;
import com.tyt.user.bean.UserCarInfoVO;
import com.tyt.user.service.CarOwnerAuthService;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 车主认证
 * <AUTHOR>
 * @since 2024/01/31 17:34
 */
@RestController
@RequestMapping(value = "/plat/carOwnerAuth")
@Slf4j
public class CarOwnerAuthController {
    @Autowired
    private CarOwnerAuthService carOwnerAuthService;
    @Autowired
    private CarService carService;
    @Autowired
    private TytConfigService tytConfigService;

    /**
     * 获取用户车主认证信息
     * @param userId
     * @return
     */
    @RequestMapping(value = "/getCarOwnerInfo")
    public ResultMsgBean getCarOwnerInfo(Long userId) {
        if (Objects.isNull(userId)){
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
        }
        CarOwnerAuthVO carOwnerInfo = carOwnerAuthService.getCarOwnerInfo(userId);
        return ResultMsgBean.successResponse(carOwnerInfo);
    }

    /**
     * 判断是否符合一键提交规则
     * @param userId 用户id
     * @param carId 车辆id
     * @return
     */
    @RequestMapping(value = "/isQuickCommit")
    public ResultMsgBean isQuickCommit(Long userId,Long carId) {
        log.info("车主认证-判断是否符合一键提交,userId={},carId={}", userId, carId);
        if (Objects.isNull(userId) || Objects.isNull(carId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
        }
        try {
            Integer quickCommit = carOwnerAuthService.isQuickCommit(userId, carId);
            return ResultMsgBean.successResponse(new CarOwnerAuthVO(null, quickCommit));
        } catch (Exception e) {
            log.error("车主认证-判断是否符合一键提交规则异常:", e);
        }
        return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
    }

    /**
     * 根据车辆id获取车辆信息
     * @param carId 车辆id
     * @return
     */
    @RequestMapping(value = "/getCarInfo")
    public ResultMsgBean getCarInfo(Long carId) {
        if (Objects.isNull(carId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
        }
        UserCarInfoVO userCarInfoVO = carService.getUserCarInfoVO(carId);
        if (Objects.nonNull(userCarInfoVO) && StringUtils.isNotBlank(userCarInfoVO.getHeadDrivingUrl())) {
            String prefixPicture = tytConfigService.getStringValue("prefix_picture", "http://newtest.teyuntong.net/rootdata");
            String headDrivingUrl = userCarInfoVO.getHeadDrivingUrl();
            userCarInfoVO.setHeadDrivingUrl(prefixPicture + headDrivingUrl);
        }
        return ResultMsgBean.successResponse(userCarInfoVO);
    }

    /**
     * 根据文本信息判断是否为企业
     * @param content 文本信息
     * @return 0 = 是企业 1=个人
     */
    @RequestMapping(value = "/isEnterprise")
    public ResultMsgBean isEnterprise(String content) {
        if (StringUtils.isBlank(content)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
        }
        Integer is = content.length() > 5 && content.contains("公司") ? 0 : 1;
        return ResultMsgBean.successResponse(is);
    }

    /**
     * 一键提交
     * @param userId 用户id
     * @param carId 车辆id
     * @return car_owner_auth_give_permission
     */
    @RequestMapping(value = "/quickCommit")
    public ResultMsgBean quickCommit(Long userId,Long carId) {
        if (Objects.isNull(userId) || Objects.isNull(carId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
        }

        TytCarOwnerAuth ownerAuth = carOwnerAuthService.selectByCarIdAndAuth(carId);
        if (Objects.nonNull(ownerAuth)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.DATA_HAS_EXIT, "请勿重复认证");
        }

        try {
            log.info("车主认证-一键提交参数信息userId={},carId={}", userId, carId);
            return carOwnerAuthService.quickCommit(userId, carId);
        } catch (Exception e) {
            log.error("车主认证-一键提交异常:", e);
        }
        return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
    }

    /**
     * 车主认证提交
     * @param carOwnerAuthReq
     * @return
     */
    @RequestMapping(value = "/carOwnerAuthSave")
    public ResultMsgBean carOwnerAuthSave(@Validated CarOwnerAuthReq carOwnerAuthReq) {
        log.info("车主认证-提交认证信息参数为:{}", JSON.toJSONString(carOwnerAuthReq));
        TytCarOwnerAuth ownerAuth = carOwnerAuthService.selectByCarIdAndAuth(carOwnerAuthReq.getCarId());
        if (Objects.nonNull(ownerAuth)){
            return ResultMsgBean.failResponse(ReturnCodeConstant.DATA_HAS_EXIT, "请勿重复认证");
        }
        carOwnerAuthService.carOwnerAuthSave(carOwnerAuthReq);
        return ResultMsgBean.successResponse();
    }
}
