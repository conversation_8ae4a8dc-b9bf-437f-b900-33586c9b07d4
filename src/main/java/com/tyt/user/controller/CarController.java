package com.tyt.user.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.car.service.CarDetailHeadService;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.carinsurance.bean.ParamCheckUtil;
import com.tyt.common.service.TytGeoDictService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqSyncAuthCarMsg;
import com.tyt.model.*;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.ocr.OcrService;
import com.tyt.plat.vo.ocr.RoadTransportBackOcrRpcVo;
import com.tyt.plat.vo.ocr.VehicleLicenseFrontVo;
import com.tyt.user.bean.*;
import com.tyt.user.enums.CarInvoiceStatusEnum;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.service.*;
import com.tyt.util.*;
import com.tytrecommend.model.TytPreference;
import com.tytrecommend.recommend.service.CarLicenseDictService;
import com.tytrecommend.recommend.service.PreferNewService;
import com.tytrecommend.recommend.service.PreferService;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

@Controller
@RequestMapping("/plat/car")
public class CarController extends BaseController {

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "carService")
	private CarService carService;

	@Resource(name = "carLinkManService")
	private CarLinkManService carLinkManService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "userService")
	private UserService userService;
	@Resource(name = "carLogService")
	private CarLogService carLogService;

	@Resource(name = "preferNewService")
	private PreferNewService preferNewService;

	@Resource(name = "preferService")
	private PreferService preferService;
	@Resource(name = "tytGeoDictService")
	private TytGeoDictService tytGeoDictService;

	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;

	@Resource(name = "carCurrentLocationService")
	private CarCurrentLocationService carCurrentLocationService;

	@Resource(name = "carLicenseDictService")
	private CarLicenseDictService carLicenseDictService;

	@Resource(name = "carDetailHeadService")
	private CarDetailHeadService carDetailHeadService;


	@Resource(name = "carDetailTailService")
	private CarDetailTailService carDetailTailService;

	@Autowired
	private CommonApiService commonApiService;

	@Resource(name = "threadPoolExecutor")
	private ThreadPoolExecutor threadPoolExecutor;

	@Autowired
	private OcrService ocrService;

	public static void main(String[] args) {
		long l = Long.parseLong(null);
		System.out.println(l);
	}

	/**
	 * 车辆认证
	 *
	 * @param request
	 * @param response
	 * @param headDrivingPic 车头行驶证正/副页							【tyt_car 表】
	 * @param tailDrivingPic 挂车行驶证正/副页							【tyt_car 表】
	 * @param tailDrivingOtherSidePic 临时行驶车牌号反面				【tyt_car 表】
	 * @param roadCardPositivePic 车头道路运输证主页 貌似跟调度有关 已启用	【tyt_car_detail_head 表】
	 * @param roadLicenseNoPic 车头道路运输证副页 貌似跟调度有关 已启用	【tyt_car_detail_head 表】
	 * @param headDrivingSubpagePic 车头行驶证副页反面					【tyt_car 表】
	 * @param headTransportHomepagePic 车头道路运输证主页				【tyt_car 表】
	 * @param headTransportSubpagePic 车头道路运输证副页				【tyt_car 表】
	 * @param tailDrivingSubpagePic 挂车行驶证副页反面					【tyt_car 表】
	 * @param tailTransportHomepagePic 挂车道路运输证主页				【tyt_car 表】
	 * @param tailTransportSubpagePic 挂车道路运输证副页				【tyt_car 表】
	 * @param tailPhotoPic 挂车照片									【tyt_car 表】
	 * @param headCity 车头车牌号 省份简称 如【冀】						【tyt_car 表】
	 * @param headNo 车头车牌号 具体的值 如【A33R8S】					【tyt_car 表】
	 * @param tailCity 挂车车牌号 省份简称 如【冀】						【tyt_car 表】
	 * @param tailNo 车头车牌号 具体的值 如【A挂NSNS】					【tyt_car 表】
	 * @param userId 用户id
	 * @param isPureFlat 挂车样式									【tyt_car_detail_tail 表】
	 * @param otherPureFlat 挂车样式 -其他							【tyt_car_detail_tail 表】
	 * @param carType 挂车型号 值在字典表【tyt_source】key=tail_car_type【tyt_car 表】
	 * @param otherCarType 挂车型号选其他时用户输入的 					【tyt_car_detail_tail 表】
	 * @param isDispatch 是否为调度 【貌似已弃用】 						【tyt_car 表】
	 * @param isTailTemporary 挂车拍照是否为临时牌照 					【非数据库字段】
	 * @param hasLadder 是否带爬梯  									【tyt_car 表】
	 * @param loadSurfaceLength 工作面长度 							【tyt_car_detail_tail 表】
	 * @param loadSurfaceHeight 工作面高度 							【tyt_car_detail_tail 表】
	 * @param maxPayload 最大载重 									【tyt_car_detail_tail 表】
	 * @param roadTransportType 道路运输证类型 貌似跟调度有关			【tyt_car_detail_head 表】
	 * @param authPath 车辆认证来源									【tyt_car 表】
	 * @param tailLength 挂车长										【tyt_car_detail_head 表】
	 * @param tailWidth 挂车宽										【tyt_car_detail_head 表】
	 * @param tailHeight 挂车高										【tyt_car_detail_head 表】
	 */
	@RequestMapping(value = "/identity/save")
	@ResponseBody
	public void identitySave(HttpServletRequest request, HttpServletResponse response,
							 @RequestParam MultipartFile headDrivingPic,
							 @RequestParam(required = false) MultipartFile tailDrivingPic,
							 @RequestParam(required = false) MultipartFile tailDrivingOtherSidePic,
							 @RequestParam(required = false) MultipartFile roadCardPositivePic,
							 @RequestParam(required = false) MultipartFile roadCardOtherSidePic,
							 @RequestParam(required = false) MultipartFile roadLicenseNoPic,
							 @RequestParam(required = false) MultipartFile headDrivingSubpagePic,
							 @RequestParam(required = false) MultipartFile headTransportHomepagePic,
							 @RequestParam(required = false) MultipartFile headTransportSubpagePic,
							 @RequestParam(required = false) MultipartFile tailDrivingSubpagePic,
							 @RequestParam(required = false) MultipartFile tailTransportHomepagePic,
							 @RequestParam(required = false) MultipartFile tailTransportSubpagePic,
							 @RequestParam(required = false) MultipartFile tailPhotoPic,
							 @RequestParam(required = false) Long tailId) {
		try {
			if(!super.isAllImg(headDrivingPic, tailDrivingPic, tailDrivingOtherSidePic, roadCardPositivePic,
					roadCardOtherSidePic, roadLicenseNoPic,
					headDrivingSubpagePic, headTransportHomepagePic, headTransportSubpagePic,
					tailDrivingSubpagePic, tailTransportHomepagePic, tailTransportSubpagePic)){
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "图片格式有误", null, 0);
				return;
			}

			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			String userId = params.get("userId");
			if(StringUtils.isBlank(userId)){
				backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "参数缺失", null, 0);
				return;
			}
			User user = userService.getById(Long.parseLong(userId));
			if (user == null) {
				backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "该用户不存在", null, 0);
				return;
			}
			if (user.getIsDispatch()!=null){
				params.put("isDispatch",user.getIsDispatch().toString());
			}
			String condition = "userId_" + userId + "车辆认证 ";
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("headCity");
					add("headNo");
					add("tailCity");
					add("tailNo");
					add("clientSign");
				}
			};
			if (!validateArguments(condition, request, response, params, names)){
				return;
			}
			//开票参数验证
			if(!carParametersVerification(params,roadCardPositivePic)){
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "参数有误", null, 0);
				return;
			}
			/* 判断照片是否上传？ */
			if (headDrivingPic == null || headDrivingPic.isEmpty()) {
				logger.error(condition + "图片为空");
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "车头证件照不能为空", null, 0);
				return;
			}
			//6370需求 若用户选择车型为【单机板=carType=14】,不显示挂车样式、挂车行驶证正/副页、挂车行驶证副页反面、挂车道路运输证主页、挂车道路运输证副页等字段
			if (tailId == null && !"14".equals(params.get("carType")) && !"15".equals(params.get("carType")) && (tailDrivingPic == null || tailDrivingPic.isEmpty())) {
				logger.error(condition + "图片为空");
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "挂车证件照不能为空", null, 0);
				return;
			}
			/* 验证重复信息？ */
			if (carService.get(params)) {
				logger.error(condition + "车辆已存在，请勿重复添加");
				backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "车辆已存在，请勿重复添加", null, 0);
				return;
			}

			//牌照号校验
			String carHeadNo = params.get("headNo");
			if (carHeadNo.endsWith("挂") || carHeadNo.endsWith("超")){
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "请填写正确的车头车牌号", null, 0);
				return;
			}
			if (Objects.isNull(tailId)) {
				String carTailNo = params.get("tailNo");
				String carType = params.get("carType");
				if (!"14".equals(carType) && !"15".equals(params.get("carType")) && !(carTailNo.endsWith("挂") || carTailNo.endsWith("超"))) {
					backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "请填写正确的车挂车牌号", null, 0);
					return;
				}
			}
			//检验有效期
			//车头驾驶证有效期
			String headDrivingExpiredTime = params.get("headDrivingExpiredTime");
			//挂车驾驶证有效期
			String tailDrivingExpiredTime = params.get("tailDrivingExpiredTime");
			if (StringUtils.isNotBlank(headDrivingExpiredTime)) {
				long expiredTime = Long.parseLong(headDrivingExpiredTime);
				Date date = TimeUtil.dateAddMonth(TimeUtil.getMonthLastDay(new Date()),15);
				if (expiredTime > date.getTime()){
					backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "车头行驶证有效期不能超过当前月份+15个月", null, 0);
					return;
				}
				if (expiredTime < new Date().getTime()){
					backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "车头行驶证有效期不能小于当前时间", null, 0);
					return;
				}
			}
			if (StringUtils.isNotBlank(tailDrivingExpiredTime)) {
				long expiredTime = Long.parseLong(tailDrivingExpiredTime);
				Date date = TimeUtil.dateAddMonth(TimeUtil.getMonthLastDay(new Date()),15);
				if (expiredTime > date.getTime()){
					backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "挂车行驶证有效期不能超过当前月份+15个月", null, 0);
					return;
				}
				if (expiredTime < new Date().getTime()){
					backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "挂车行驶证有效期不能小于当前时间", null, 0);
					return;
				}
			}
			/* 创建Car */
            String headDrivingUrl = renamePic(headDrivingPic, "car");
            String tailDrivingUrl = null;
            if (tailDrivingPic != null && !tailDrivingPic.isEmpty()){
				tailDrivingUrl = renamePic(tailDrivingPic, "car");
			}
            CarSaveBean carSaveBean = createCarSaveBean(params,tailId);
			headDrivingPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + headDrivingUrl));
			if (tailDrivingPic != null && tailDrivingUrl!=null){
				tailDrivingPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + tailDrivingUrl));
			}
			if(tailId != null && tailDrivingUrl == null){
				//查询换绑的新的挂车信息
				CarDetailTail carDetailTail = carDetailTailService.getById(tailId);
				//获取换绑挂车的主信息
				Car newCar = carService.getById(carDetailTail.getCarId());
				tailDrivingUrl = newCar.getTailDrivingUrl();
			}
			//开票新增参数

			//临时形式车牌号反面
			String tailDrivingOtherSideUrl=getPicUrlByPic(tailDrivingOtherSidePic);
			//道路运输证正面
			String roadCardPositiveUrl=getPicUrlByPic(roadCardPositivePic);
			//道路运输证反面
			String roadCardOtherSideUrl=getPicUrlByPic(roadCardOtherSidePic);
			//道路运输经营许可证
			String roadLicenseNoUrl=getPicUrlByPic(roadLicenseNoPic);
			//车头行驶证副页反面
			String headDrivingSubpageUrl = getPicUrlByPic(headDrivingSubpagePic);
			//车头道路运输证主页
			String headTransportHomepageUrl = getPicUrlByPic(headTransportHomepagePic);
			//车头道路运输证副页
			String headTransportSubpageUrl = getPicUrlByPic(headTransportSubpagePic);
			//挂车行驶证副页反面
			String tailDrivingSubpageUrl = getPicUrlByPic(tailDrivingSubpagePic);
			//挂车道路运输证主页
			String tailTransportHomepageUrl = getPicUrlByPic(tailTransportHomepagePic);
			//挂车道路运输证副页
			String tailTransportSubpageUrl = getPicUrlByPic(tailTransportSubpagePic);
			//挂车照片
			String tailPhotoUrl = getPicUrlByPic(tailPhotoPic);

			Long id = carService.saveCar(carSaveBean,headDrivingUrl,tailDrivingUrl,tailDrivingOtherSideUrl,roadCardPositiveUrl,roadCardOtherSideUrl,roadLicenseNoUrl,
					headDrivingSubpageUrl,headTransportHomepageUrl,headTransportSubpageUrl,
					tailDrivingSubpageUrl,tailTransportHomepageUrl,tailTransportSubpageUrl,tailPhotoUrl,tailId);
			//初始化偏好 初始化出发地目的地载重为2-50吨
			//暂时不上线
//			initPreferenceNew(params,id);
			CarIdBean carId = new CarIdBean();
			carId.setId(id);
			backResponse(request, response, ReturnCodeConstant.OK, "车辆信息添加成功", carId, 0);
			logger.info(condition + "添加成功");
			//线程池处理道运证OCR
			doOCR(headTransportHomepageUrl, tailTransportHomepageUrl, id);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}

	private void doOCR(String headTransportHomepageUrl, String tailTransportHomepageUrl, Long carId){
		try {
			if (StringUtils.isNotBlank(headTransportHomepageUrl)) {
				final String headUrl = AppConfig.getProperty("picture.path.domain") + headTransportHomepageUrl;
				threadPoolExecutor.execute(() -> {
					RoadTransportBackOcrRpcVo ocrRpcVo = ocrService.roadTransportBackOcr(headUrl);
					if (ObjectUtil.isNotNull(ocrRpcVo)) {
						carService.updateCarForTransportOcr(carId, ocrRpcVo, 1);
					}
				});
			}
			if (StringUtils.isNotBlank(tailTransportHomepageUrl)) {
				final String tailUrl = AppConfig.getProperty("picture.path.domain") + tailTransportHomepageUrl;
				threadPoolExecutor.execute(() -> {
					RoadTransportBackOcrRpcVo ocrRpcVo = ocrService.roadTransportBackOcr(tailUrl);
					if (ObjectUtil.isNotNull(ocrRpcVo)) {
						carService.updateCarForTransportOcr(carId, ocrRpcVo, 2);
					}
				});
			}
		} catch (Exception e){
			logger.error("do transport ocr error:", e);
		}
	}

	/**
	 * 查询车辆信息接口
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/get")
	public void get(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			Long userId = Long.parseLong(params.get("userId"));
			String condition = "userId_" + userId + "单个车辆信息查询 ";
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("id");
				}
			};
			if (!validateArguments(condition, request, response, params, names)){
				return;
			}
			/* 取相关参数 */
			Long carId = Long.parseLong(params.get("id"));
			/* 根据Id查询用户 车辆信息包含偏好信息 */
			// Car car = carService.getById(carId);
			QueryCar queryCar = carService.getQueryCar(carId);

			logger.info(condition + "成功");
			backResponse(request, response, ReturnCodeConstant.OK, "查询信息成功", queryCar, 0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}

	/**
	 * 所有车辆
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/identity/get")
	public void identityGetList(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			Long userId = Long.parseLong(params.get("userId"));
			String condition = "userId_" + userId + "所有车辆获取 版本号为" + params.get("clientVersion");
			String auth = params.get("auth");
			/* 业务参数获取 */
			/* 查询信息 */
			// 判断版本号是否为精准货源推荐以前的版本
			int clientVersion = tytConfigService.getIntValue("clientVersion");
			List<QueryCar> cars = new ArrayList<QueryCar>();
			if (Integer.parseInt(params.get("clientVersion")) <= clientVersion){
				cars = carService.getQueryAllOld(userId, auth);
			} else{
				cars = carService.getQueryAll(userId, auth);
			}
			backResponse(request, response, ReturnCodeConstant.OK, "车辆信息查询成功", cars, 0);
			logger.info(condition + "成功");
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}

	/**
	 *
	 * 重新审核车辆
	 *
	 */
	@RequestMapping(value = "/identity/updatesave")
	public void update(HttpServletRequest request, HttpServletResponse response,
					   @RequestParam(required = false) MultipartFile headDrivingPic,
					   @RequestParam(required = false) MultipartFile tailDrivingPic,
					   @RequestParam(required = false) MultipartFile tailDrivingOtherSidePic,
					   @RequestParam(required = false) MultipartFile roadCardPositivePic,
					   @RequestParam(required = false) MultipartFile roadCardOtherSidePic,
					   @RequestParam(required = false) MultipartFile roadLicenseNoPic,
					   @RequestParam(required = false) MultipartFile headDrivingSubpagePic,
					   @RequestParam(required = false) MultipartFile headTransportHomepagePic,
					   @RequestParam(required = false) MultipartFile headTransportSubpagePic,
					   @RequestParam(required = false) MultipartFile tailDrivingSubpagePic,
					   @RequestParam(required = false) MultipartFile tailTransportHomepagePic,
					   @RequestParam(required = false) MultipartFile tailTransportSubpagePic,
					   @RequestParam(required = false) MultipartFile tailPhotoPic,
					   @RequestParam(required = false) Long tailId) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			Long userId = Long.parseLong(params.get("userId"));
			String condition = "userId_" + userId + "车辆认证更新 ";
			User user = userService.getById(userId);
			if (user == null) {
				backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "该用户不存在", null, 0);
				return;
			}
			if (user.getIsDispatch()!=null){
				params.put("isDispatch",user.getIsDispatch().toString());
			}
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("id");
					add("headCity");
					add("headNo");
					add("tailCity");
					add("tailNo");
					//2019-10-23 xyy 认证车辆新增挂车类型和挂车样式
//					add("carType");  //挂车类型
//					add("isPureFlat");  //挂车样式
				}
			};
			if (!validateArguments(condition, request, response, params, names)){
				return;
			}
			if(!validatePicType( headDrivingPic,  tailDrivingPic,  tailDrivingOtherSidePic,
					roadCardPositivePic,  roadCardOtherSidePic,  roadLicenseNoPic,
					headDrivingSubpagePic, headTransportHomepagePic, headTransportSubpagePic,
					tailDrivingSubpagePic, tailTransportHomepagePic, tailTransportSubpagePic)){
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "图片格式有误", null, 0);
				return;

			}
			if(!carParametersVerification(params,roadCardPositivePic,roadLicenseNoPic,roadCardOtherSidePic)){
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "参数有误", null, 0);
				return;
			}
			/* 判断照片是否上传？ *//*
			if ((headDrivingPic == null || headDrivingPic.isEmpty()) && (tailDrivingPic == null || tailDrivingPic.isEmpty())) {
				logger.info(condition + "更新失败");
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "没有上传车头或挂车图片", null, 0);
				return;
			}*/
			//牌照号校验
			String carHeadNo = params.get("headNo");
			if (carHeadNo.endsWith("挂") || carHeadNo.endsWith("超")){
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "请填写正确的车头车牌号", null, 0);
				return;
			}

			if (Objects.isNull(tailId)) {
				String carTailNo = params.get("tailNo");
				String carType = params.get("carType");
				if (!"14".equals(carType) && !"15".equals(carType) && !(carTailNo.endsWith("挂") || carTailNo.endsWith("超"))) {
					backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "请填写正确的车挂车牌号", null, 0);
					return;
				}
			}
			//检验有效期
			//车头驾驶证有效期
			String headDrivingExpiredTime = params.get("headDrivingExpiredTime");
			//挂车驾驶证有效期
			String tailDrivingExpiredTime = params.get("tailDrivingExpiredTime");
			if (StringUtils.isNotBlank(headDrivingExpiredTime)) {
				long expiredTime = Long.parseLong(headDrivingExpiredTime);
				Date date = TimeUtil.dateAddMonth(TimeUtil.getMonthLastDay(new Date()),15);
				if (expiredTime > date.getTime()){
					backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "车头行驶证有效期不能超过当前月份+15个月", null, 0);
					return;
				}
			}
			if (StringUtils.isNotBlank(tailDrivingExpiredTime)) {
				long expiredTime = Long.parseLong(tailDrivingExpiredTime);
				Date date = TimeUtil.dateAddMonth(TimeUtil.getMonthLastDay(new Date()),15);
				if (expiredTime > date.getTime()){
					backResponse(request, response, ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "挂车行驶证有效期不能超过当前月份+15个月", null, 0);
					return;
				}
			}

			/* 验证重复信息？ */
			if (carService.get(params)) {
				logger.error(condition + "车辆已存在，请勿重复添加");
				backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "车辆已存在，请勿重复添加", null, 0);
				return;
			}
			logger.info("传入的挂车ID为：{}", tailId);
			//获取换绑挂车的主信息
			Car newCar = null;
			if(tailId != null){
				//查询换绑的新的挂车信息
				CarDetailTail carDetailTail = carDetailTailService.getById(tailId);
				logger.info("换绑挂车信息:{}", JSON.toJSONString(carDetailTail));
				//获取换绑挂车的主信息
				newCar = carService.getById(carDetailTail.getCarId());
				logger.info("换绑挂车的主信息:{}", JSON.toJSONString(newCar));
			}
			// 更新
			Car car = updateCar(params,headDrivingPic,tailDrivingPic,tailDrivingOtherSidePic,
					headDrivingSubpagePic,headTransportHomepagePic,headTransportSubpagePic,
					tailDrivingSubpagePic,tailTransportHomepagePic,tailTransportSubpagePic,tailPhotoPic,newCar);
			if (car == null) {
				logger.info(condition + "更新失败");
				backResponse(request, response, ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "没有该车", null, 0);
				return;
			}
			CarSaveBean carSaveBean = createCarSaveBean(params, tailId);
			if("1".equals(params.get("isDispatch"))){
				DispatchCarBean dispatchCarBean = createDispatchCarBean(params, roadCardPositivePic, roadCardOtherSidePic, roadLicenseNoPic,car);
				carService.updateCarHead(dispatchCarBean,car);
			}
			if (headDrivingPic != null && !headDrivingPic.isEmpty()) {
				headDrivingPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getHeadDrivingUrl()));
			}
			if (tailDrivingPic != null && !tailDrivingPic.isEmpty()){
				tailDrivingPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getTailDrivingUrl()));
			}

			if (headDrivingSubpagePic != null && !headDrivingSubpagePic.isEmpty()){
				headDrivingSubpagePic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getHeadDrivingSubpageUrl()));
			}
			if (headTransportHomepagePic != null && !headTransportHomepagePic.isEmpty()){
				headTransportHomepagePic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getHeadTransportHomepageUrl()));
			}
			if (headTransportSubpagePic != null && !headTransportSubpagePic.isEmpty()){
				headTransportSubpagePic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getHeadTransportSubpageUrl()));
			}
			if (tailDrivingSubpagePic != null && !tailDrivingSubpagePic.isEmpty()){
				tailDrivingSubpagePic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getTailDrivingSubpageUrl()));
			}
			if (tailTransportHomepagePic != null && !tailTransportHomepagePic.isEmpty()){
				tailTransportHomepagePic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getTailTransportHomepageUrl()));
			}
			if (tailTransportSubpagePic != null && !tailTransportSubpagePic.isEmpty()){
				tailTransportSubpagePic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getTailTransportSubpageUrl()));
			}
			if (tailPhotoPic !=null && !tailPhotoPic.isEmpty()) {
				tailPhotoPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getTailPhotoUrl()));
			}
			carService.update(car);
			syncAuthCarMsg(car.getId());

            //2019-10-23 xyy 更改挂车样式
            if (StringUtils.isNotBlank(params.get("isPureFlat"))){
                carService.saveTailDetail(car.getId(),car.getUserId(),car.getTailCity(),car.getTailNo(),Integer.parseInt(params.get("isPureFlat")),carSaveBean);
            }
			//初始化偏好 初始化出发地目的地载重为2-50吨  暂时不上线
//			initPreferenceNew(params,car.getId());

			//更改user表中is_car状态
			int carAuth = carService.isCarAuth(user.getId(), "1");
			if (carAuth>0 && !"1".equals(user.getIsCar())) {
				user.setIsCar("1");
				user.setMtime(new Timestamp(System.currentTimeMillis()));
				userService.update(user);
				cacheService.del(Constant.CACHE_USER_KEY + user.getId());
				cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
			}
			if (carAuth<=0 && "1".equals(user.getIsCar())) {
				user.setIsCar("0");
				user.setMtime(new Timestamp(System.currentTimeMillis()));
				userService.update(user);
				cacheService.del(Constant.CACHE_USER_KEY + user.getId());
				cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
			}
			backResponse(request, response, ReturnCodeConstant.OK, "车辆信息更新成功", null, 0);
			logger.info(condition + "更新成功");
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}

	}

	private boolean validatePicType(MultipartFile headDrivingPic, MultipartFile tailDrivingPic, MultipartFile tailDrivingOtherSidePic,
									MultipartFile roadCardPositivePic, MultipartFile roadCardOtherSidePic, MultipartFile roadLicenseNoPic,
									MultipartFile headDrivingSubpagePic, MultipartFile headTransportHomepagePic, MultipartFile headTransportSubpagePic,
									MultipartFile tailDrivingSubpagePic, MultipartFile tailTransportHomepagePic, MultipartFile tailTransportSubpagePic) {
		//允许的图片格式
		String imgType = "jpg,png,jpeg,bmp,image/png,image/jpeg,image/jpg,image/bmp";
		if(headDrivingPic!=null&&
				(StringUtils.isEmpty(headDrivingPic.getContentType())||!imgType.contains(headDrivingPic.getContentType()))){
			logger.info("车辆审核,车头行驶本图片类型：{}",headDrivingPic.getContentType());
			return false;
		}
		if(tailDrivingPic!=null&&
				(StringUtils.isEmpty(tailDrivingPic.getContentType())||!imgType.contains(tailDrivingPic.getContentType()))){
			logger.info("车辆审核,挂车行驶本图片类型：{}",tailDrivingPic.getContentType());
			return false;
		}
		if(tailDrivingOtherSidePic!=null&&
				(StringUtils.isEmpty(tailDrivingOtherSidePic.getContentType())||!imgType.contains(tailDrivingOtherSidePic.getContentType()))){
			logger.info("车辆审核,挂车行驶本反面图片类型：{}",tailDrivingOtherSidePic.getContentType());
			return false;
		}
		if(roadCardPositivePic!=null&&
				(StringUtils.isEmpty(roadCardPositivePic.getContentType())||!imgType.contains(roadCardPositivePic.getContentType()))){
			logger.info("车辆审核,道路运输证正面图片类型：{}",roadCardPositivePic.getContentType());
			return false;
		}
		if(roadCardOtherSidePic!=null&&
				(StringUtils.isEmpty(roadCardOtherSidePic.getContentType())||!imgType.contains(roadCardOtherSidePic.getContentType()))){
			logger.info("车辆审核,道路运输证反面图片类型：{}",roadCardOtherSidePic.getContentType());
			return false;
		}
		if(roadLicenseNoPic!=null&&
				(StringUtils.isEmpty(roadLicenseNoPic.getContentType())||!imgType.contains(roadLicenseNoPic.getContentType()))){
			logger.info("车辆审核,道路运输经营许可证图片类型：{}",roadLicenseNoPic.getContentType());
			return false;
		}

		if(headDrivingSubpagePic!=null&&
				(StringUtils.isEmpty(headDrivingSubpagePic.getContentType())||!imgType.contains(headDrivingSubpagePic.getContentType()))){
			logger.info("车辆审核,车头行驶证副页反面图片类型：{}",headDrivingSubpagePic.getContentType());
			return false;
		}
		if(headTransportHomepagePic!=null&&
				(StringUtils.isEmpty(headTransportHomepagePic.getContentType())||!imgType.contains(headTransportHomepagePic.getContentType()))){
			logger.info("车辆审核,车头道路运输证主页图片类型：{}",headTransportHomepagePic.getContentType());
			return false;
		}
		if(headTransportSubpagePic!=null&&
				(StringUtils.isEmpty(headTransportSubpagePic.getContentType())||!imgType.contains(headTransportSubpagePic.getContentType()))){
			logger.info("车辆审核,车头道路运输证副页图片类型：{}",headTransportSubpagePic.getContentType());
			return false;
		}
		if(tailDrivingSubpagePic!=null&&
				(StringUtils.isEmpty(tailDrivingSubpagePic.getContentType())||!imgType.contains(tailDrivingSubpagePic.getContentType()))){
			logger.info("车辆审核,挂车行驶证副页反面图片类型：{}",tailDrivingSubpagePic.getContentType());
			return false;
		}
		if(tailTransportHomepagePic!=null&&
				(StringUtils.isEmpty(tailTransportHomepagePic.getContentType())||!imgType.contains(tailTransportHomepagePic.getContentType()))){
			logger.info("车辆审核,挂车道路运输证主页图片类型：{}",tailTransportHomepagePic.getContentType());
			return false;
		}
		if(tailTransportSubpagePic!=null&&
				(StringUtils.isEmpty(tailTransportSubpagePic.getContentType())||!imgType.contains(tailTransportSubpagePic.getContentType()))){
			logger.info("车辆审核,挂车道路运输证副页图片类型：{}",tailTransportSubpagePic.getContentType());
			return false;
		}

		return true;
	}


	private void syncAuthCarMsg(Long carId) {
		MqSyncAuthCarMsg syncAuthCarMsg = new MqSyncAuthCarMsg();
		String messageSerailNum = SerialNumUtil.generateSeriaNum();
		syncAuthCarMsg.setMessageSerailNum(messageSerailNum);
		syncAuthCarMsg.setCarId(carId.intValue());
		syncAuthCarMsg.setType("2");
		syncAuthCarMsg.setMessageType(MqBaseMessageBean.SYNC_AUTH_CAR_STATUS_MESSAGE);
		logger.info("car controller syncAuthCarMsg: " + syncAuthCarMsg);
		tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(syncAuthCarMsg), MqBaseMessageBean.SYNC_AUTH_CAR_STATUS_MESSAGE);
		tytMqMessageService.sendMqMessage(syncAuthCarMsg.getMessageSerailNum(), JSON.toJSONString(syncAuthCarMsg), MqBaseMessageBean.SYNC_AUTH_CAR_STATUS_MESSAGE);
	}

	/**
	 * 车辆列表排序
	 *
	 *
	 */
	@RequestMapping(value = "/identity/sort")
	public void sort(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			Long userId = Long.parseLong(params.get("userId"));
			String condition = "userId_" + userId + "车辆列表排序 ";
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("sortList");
				}
			};
			if (!validateArguments(condition, request, response, params, names))
				return;
			/* 取相关参数 */
			String carIds = params.get("sortList");
			// 判断传入的id 数量和数据库中的是否一致 如果一致就修改
			if (!carService.validateNumEqual(params)) {
				logger.info(condition + "失败");
				backResponse(request, response, ReturnCodeConstant.BASIC_PARAMETER_ERROR, "传入ID数量和数据库中不一致", null, 0);
				return;
			}
			/* 更改排序 */
			boolean isUpdate = carService.updateSort(carIds);
			if (isUpdate) {
				logger.info(condition + "成功");
				backResponse(request, response, ReturnCodeConstant.OK, "保存排序成功", null, 0);
			}
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}

	/**
	 * 车辆找货开关
	 *
	 *
	 */
	@RequestMapping(value = "/identity/updateOnOff")
	public void updateOnOff(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			Long userId = Long.parseLong(params.get("userId"));
			String condition = "userId_" + userId + "车辆找货开关更新 ";
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("id");
					add("findGoodOnOff");
				}
			};
			if (!validateArguments(condition, request, response, params, names))
				return;
			/* 取相关参数 */
			String findGoodOnOff = params.get("findGoodOnOff");
			Long id = Long.parseLong(params.get("id"));
			// 判断是否是开启，如果是开启请求 会对此用户进行是否认证通过交过会员费,是否到期，是否设置偏好等验证
			if ("1".equals(findGoodOnOff)) {
				TytPreference tytPre = preferService.newFindPreferenceByCarId(id);
				if (tytPre == null) {
					logger.info(condition + "失败");
					backResponse(request, response, ReturnCodeConstant.NO_SET_PREFER, "请设置车辆找货偏好", null, 0);
					return;
				}
				Car car = carService.getById(id);
				if (car.getAuth().equals("0")) {
					logger.info(condition + "失败");
					backResponse(request, response, ReturnCodeConstant.AUTH_ISGOING, "客服加紧审核中，请耐心等待", null, 0);
					return;
				}
				if (car.getAuth().equals("2")) {
					logger.info(condition + "失败");
					backResponse(request, response, ReturnCodeConstant.AUTH_FAIL, "请重新提交车辆认证", null, 0);
					return;
				}
				// 判断是否交费 并且会员费没到期
				User user = userService.getById(userId);
				if (TimeUtil.addDay(TimeUtil.formatDate(new Date(user.getEndTime().getTime())), 1).getTime() < System.currentTimeMillis()) {
					logger.info(condition + "失败");
					backResponse(request, response, ReturnCodeConstant.NOT_IS_VIP, "开通会员尊享好货推荐", null, 0);
					return;
				}

			}
			carService.updateOnOff(id, findGoodOnOff);
			logger.info(condition + "成功");
			backResponse(request, response, ReturnCodeConstant.OK, "车辆找货开关设置成功", null, 0);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}


	@RequestMapping(value = "/delete")
	public void delete(HttpServletRequest request, HttpServletResponse response) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			/* 业务参数验证 */
			if (StringUtils.isEmpty(params.get("userId"))) {
				backResponse(request, response, ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空", null, 0);
				return;
			}
			if (StringUtils.isEmpty(params.get("id"))) {
				backResponse(request, response, ReturnCodeConstant.BASIC_PARAMETER_ERROR, "车辆id不能为空", null, 0);
				return;
			}
			Long userId = Long.parseLong(params.get("userId"));
			String condition = "userId_" + userId + "删除车辆信息";
			/* 取相关参数 */
			Long carId = Long.parseLong(params.get("id"));
			/*
			 * 查询车辆是否属于该用户
			 */
			boolean isBlong = carService.isCarBlongToUser(carId, userId);
			Car byId = carService.getById(carId);
			logger.info("**********isBlong " + isBlong + "**********");
			if (!isBlong) {
				backResponse(request, response, ReturnCodeConstant.BASIC_PARAMETER_ERROR, "删除的车辆不属于该用户", null, 0);
				return;
			} else {
				/* 根据车辆Id删除车辆 */
				int clientVersion = tytConfigService.getIntValue("clientVersion");
				if (Integer.parseInt(params.get("clientVersion")) > clientVersion) {
					carService.deleteById(carId, params.get("deleteReason"));
					User user = userService.getByUserId(userId);
				}
				logger.info(condition + "成功");
				backResponse(request, response, ReturnCodeConstant.OK, "删除成功", null, 0);

			}
			syncAuthCarMsg(carId);
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}

	@RequestMapping(value = "/location/getCarLocation")
	@ResponseBody
	public ResultMsgBean getCarLocation(Long carId, String licensePlate) {
		logger.info("car controller getCarLocation carId is: " + carId + " , licensePlate is: " + licensePlate);
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
		try {
			if (StringUtils.isEmpty(licensePlate)) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				resultMsgBean.setMsg("licensePlate can not be null");
			} else {
				TytCarCurrentLocation currentLocation = carCurrentLocationService.getByCarHeadLicence(licensePlate);
				CarCurrentLocationBean carCurrentLocationBean = new CarCurrentLocationBean();
				if (currentLocation != null) {
					BeanUtils.copyProperties(carCurrentLocationBean, currentLocation);
					carCurrentLocationBean.setNewLocationTime(currentLocation.getNewLocationTime());
					resultMsgBean.setData(carCurrentLocationBean);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("失败");
		}
		logger.info("car controller getCarLocation result is: " + resultMsgBean);
		return resultMsgBean;
	}

	@RequestMapping(value="/identity/getCarDriverPhoneInfo")
	@ResponseBody
	public ResultMsgBean getCarDriverPhoneInfo(Long id) {
		ResultMsgBean result =new ResultMsgBean();
//		carDriverPhoneInfoService

		return null;
	}

	/**
	 * 所有车辆
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/identity/get.action")
	public void identityGetListAction(HttpServletRequest request, HttpServletResponse response) {
		this.identityGetList(request, response);
	}

	/**
	 * @Description  获取车辆类型列表
	 * <AUTHOR>
	 * @Date  2019/6/12 18:43
	 * @Param [request, response]
	 * @return com.tyt.model.ResultMsgBean
	 **/
	@RequestMapping(value = "/identity/carTypeList", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean carTypeList(HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
		try {
			//获取车辆二级分类  carClassify=2
			List<CarType> carTypeList = carService.getCarTypeListByClassify(2);
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("carTypeList", carTypeList);
			resultMsgBean.setCode(ReturnCodeConstant.OK);
			resultMsgBean.setMsg("查询车辆类型列表成功！");
			resultMsgBean.setData(map);
		} catch (Exception e) {
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			resultMsgBean.setMsg("服务器错误");
			logger.info("查询车辆类型列表发生错误！"+e.getMessage());
			e.printStackTrace();
		}
		return resultMsgBean;
	}

	/**
	 * @Description 我的车辆列表
	 * <AUTHOR>
	 * @Date  2019/5/31 17:01
	 * @Param [carBean, request, response]
	 * @return com.tyt.model.ResultMsgBean
	 **/
	@RequestMapping(value = "/identity/myCarList", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean myCarList(BaseParameter baseParameter,
								   HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
		try {
			Map<String,Object> map = new HashMap<String,Object>();
			//获取用户Id
			Long userId = baseParameter.getUserId();
			if(userId == null) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				resultMsgBean.setMsg("用户Id不能为空！");
				return resultMsgBean;
			}
			//根据传入的参数，查询我的车辆列表
			List<Car> myCarList = carService.getMyCarList(userId);
			map.put("myCarList", myCarList);
			resultMsgBean.setCode(ReturnCodeConstant.OK);
			resultMsgBean.setMsg("查询车辆列表成功！");
			resultMsgBean.setData(map);
		} catch (Exception e) {
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			resultMsgBean.setMsg("服务器错误");
			logger.info("查询车辆列表发生错误！"+e.getMessage());
			e.printStackTrace();
		}
		return resultMsgBean;
	}

	/**
	 * @Description  完善车辆信息
	 * <AUTHOR>
	 * @Date  2019/7/3 16:49
	 * @Param [updateCarBean, request, response]
	 * @return com.tyt.model.ResultMsgBean
	 **/
	@RequestMapping(value = "/identity/updateCarInfo", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean updateCar(UpdateCarBean updateCarBean,
								   HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
		try {
			//调用参数校验工具类，校验参数
			String errorMsg = ParamCheckUtil.checkParam(updateCarBean);
			if(StringUtils.isNotBlank(errorMsg)) {
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg(errorMsg);
				return resultMsgBean;
			}
			//查询车辆信息
			int result = carService.updateCarInfo(updateCarBean);
			if(result > 0){
				resultMsgBean.setCode(ReturnCodeConstant.OK);
				resultMsgBean.setMsg("车辆信息修改成功！");
			}else{
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg("车辆信息修改失败！");
				return resultMsgBean;
			}
		} catch (Exception e) {
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			resultMsgBean.setMsg("服务器错误");
			logger.info("车辆信息修改发生错误！"+e.getMessage());
			e.printStackTrace();
		}
		return resultMsgBean;
	}

	/**
	 * @Description  完善车辆信息 -- 调度中心版本
	 * <AUTHOR>
	 * @Date  2019/7/3 16:49
	 * @Param [updateCarBean, request, response]
	 * @return com.tyt.model.ResultMsgBean
	 **/
	@RequestMapping(value = "/identity/updateCarInfoDiaodu", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean updateCar(UpdateCarDiaoduBean updateCarDiaoduBean,
								   HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
		try {
			//调用参数校验工具类，校验参数
			String errorMsg = ParamCheckUtil.checkParam(updateCarDiaoduBean);
			if(StringUtils.isNotBlank(errorMsg)) {
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg(errorMsg);
				return resultMsgBean;
			}
			Long carId = updateCarDiaoduBean.getCarId();
			Long driverUserId = updateCarDiaoduBean.getDriverUserId();
			Long secondaryDriverUserId = updateCarDiaoduBean.getSecondaryDriverUserId();
			//查询车辆信息
			Car car = carService.getById(carId);
			if(car != null){
				Long carDriverUserId = car.getDriverUserId();
				Long carSecondaryDriverUserId = car.getSecondaryDriverUserId();
				if(driverUserId != null && carSecondaryDriverUserId != null
				&& driverUserId.longValue() == carSecondaryDriverUserId.longValue()){
					resultMsgBean.setCode(ReturnCodeConstant.ERROR);
					resultMsgBean.setMsg("该司机已绑定副司机");
					return resultMsgBean;
				}
				if(secondaryDriverUserId != null && carDriverUserId != null
						&& secondaryDriverUserId.longValue() == carDriverUserId.longValue()){
					resultMsgBean.setCode(ReturnCodeConstant.ERROR);
					resultMsgBean.setMsg("该司机已绑定主司机");
					return resultMsgBean;
				}
			}

			//修改车辆绑定司机信息
			int result = carService.updateCarInfoDiaodu(updateCarDiaoduBean);
			if(result > 0){
				resultMsgBean.setCode(ReturnCodeConstant.OK);
				resultMsgBean.setMsg("车辆信息修改成功！");
			}else{
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg("车辆信息修改失败！");
				return resultMsgBean;
			}
		} catch (Exception e) {
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			resultMsgBean.setMsg("服务器错误");
			logger.info("车辆信息修改发生错误！"+e.getMessage());
			e.printStackTrace();
		}
		return resultMsgBean;
	}

	/**
	 * 删除绑定司机信息接口
	 * @param updateCarDiaoduBean
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/identity/deleteDriverInfo", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean deleteDriverInfo(UpdateCarDiaoduBean updateCarDiaoduBean,
								          HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
		try {
			//调用参数校验工具类，校验参数
			String errorMsg = ParamCheckUtil.checkParam(updateCarDiaoduBean);
			if(StringUtils.isNotBlank(errorMsg)) {
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg(errorMsg);
				return resultMsgBean;
			}
			Integer deleteDriverType = updateCarDiaoduBean.getDeleteDriverType();
			if(deleteDriverType == null){
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg("删除司机类型不能为空");
				return resultMsgBean;
			}
			//删除绑定司机信息
			int result = carService.deleteDriverInfo(updateCarDiaoduBean);
			if(result > 0){
				resultMsgBean.setCode(ReturnCodeConstant.OK);
				resultMsgBean.setMsg("删除绑定司机信息成功！");
			}else{
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg("删除绑定司机信息失败！");
				return resultMsgBean;
			}
		} catch (Exception e) {
			resultMsgBean.setCode(ResultMsgBean.ERROR);
			resultMsgBean.setMsg("服务器错误");
			logger.info("删除绑定司机信息发生错误！"+e.getMessage());
			e.printStackTrace();
		}
		return resultMsgBean;
	}

	/**
	 * 获取字典中的车辆类型和车辆样式
	 * @return
	 */
	@RequestMapping(value = "/identity/getTrailerStyleType", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean getTrailerStyleType(){
		try {
			return new ResultMsgBean(ResultMsgBean.OK, "操作成功!",carService.getTrailerStyleType());
		}catch (Exception e){
			return new ResultMsgBean(ResultMsgBean.ERROR, "服务器错误!");
		}
	}

	/**
	 * 开票相关新增车辆参数校验
	 * @param params
	 * @param roadCardPositivePic
	 * @return
	 */
	private boolean carParametersVerification(Map<String, String> params,MultipartFile roadCardPositivePic){
		if (StringUtils.isNotEmpty(params.get("isDispatch"))&&"1".equals(params.get("isDispatch"))){
			if (roadCardPositivePic == null || roadCardPositivePic.isEmpty()) {
				return false;
			}

			List<String> names = new ArrayList<String>() {
				{
					//工作面长
					add("loadSurfaceLength");
					//工作面高
					add("loadSurfaceHeight");
					//最大载重
					add("maxPayload");
				}
			};
			for (String name : names) {
				if(StringUtils.isEmpty(params.get(name))){
					logger.info("----------------车辆新增时有误参数："+name);
					return false;
				}
			}

		}
		return true;
	}

	private String getPicUrlByPic(MultipartFile pic) throws IOException {
		String url=null;
		if (pic!=null&&!pic.isEmpty()){
			url=renamePic(pic, "car");
			pic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + url));
		}
		return url;
	}

	/**
	 * 开票相关修改车辆参数校验
	 * @param params
	 * @param roadCardPositivePic
	 * @param roadLicenseNoPic
	 * @return
	 */
	private boolean carParametersVerification(Map<String, String> params,MultipartFile roadCardPositivePic,MultipartFile roadLicenseNoPic,MultipartFile roadCardOtherSidePic){
		if (StringUtils.isNotEmpty(params.get("isDispatch"))&&"1".equals(params.get("isDispatch"))){
			if ((roadCardPositivePic==null||roadCardPositivePic.isEmpty())&&(roadCardOtherSidePic==null||roadCardOtherSidePic.isEmpty())){
				if ("1".equals(params.get("roadUpdateType"))||"3".equals(params.get("roadUpdateType"))){
					return false;
				}
			}
			if (roadLicenseNoPic==null||roadLicenseNoPic.isEmpty()){
				if ("2".equals(params.get("roadUpdateType"))||"3".equals(params.get("roadUpdateType"))){
					return false;
				}
			}
			if (StringUtils.isBlank(params.get("roadTransportType"))){
				logger.info("roadTransportType:{}-------------",params.get("roadTransportType"));
				return false;
			}
			return true;
		}else {
			return true;
		}

	}

	/**
	 * 更新car
	 *
	 * @param params
	 * @param headDrivingPic
	 * @param tailDrivingPic
	 * @return
	 * @throws Exception
	 */
	private Car updateCar(Map<String, String> params, MultipartFile headDrivingPic, MultipartFile tailDrivingPic,
						  MultipartFile tailDrivingOtherSidePic,
						  MultipartFile headDrivingSubpagePic,
						  MultipartFile headTransportHomepagePic,
						  MultipartFile headTransportSubpagePic,
						  MultipartFile tailDrivingSubpagePic,
						  MultipartFile tailTransportHomepagePic,
						  MultipartFile tailTransportSubpagePic,
						  MultipartFile tailPhotoPic ,
						  Car newCar) throws Exception {
		Car car = carService.getById(Long.parseLong(params.get("id")));
		if (car == null) {
			return null;
		}
		String oldHeadCity = car.getHeadCity();
		String oldHeadNo = car.getHeadNo();
		String newHeadCity = params.get("headCity");
		String newHeadNo = params.get("headNo");
		//6420 如果车牌号有变化则同步删除车主认证信息
		if (StringUtils.isNotBlank(oldHeadCity) && StringUtils.isNotBlank(oldHeadNo) && StringUtils.isNotBlank(newHeadCity) && StringUtils.isNotBlank(newHeadNo)){
			if (!oldHeadCity.equals(newHeadCity) || !oldHeadNo.equals(newHeadNo)){
				carService.deleteByCarOwnerAuth(car.getId());
			}
		}
		// 认证成功的车辆重新审核时保存修改記錄
		if ("1".equals(car.getAuth())) {
			CarLog carLog = new CarLog();
			Car carNew = new Car();
			BeanUtils.copyProperties(carNew, car);
			carNew.setId(null);
			BeanUtils.copyProperties(carLog, carNew);
			carLog.setRecordTime(new Date());
			carLog.setCarId(car.getId());
			carLog.setUpdateType(params.get("updateType"));
			carLog.setUpdateReason(params.get("updateReason"));
			carLog.setIsDisplay(1);
			carLogService.add(carLog);
		}
		car.setUserId(Long.parseLong(params.get("userId")));
		car.setHeadCity(params.get("headCity"));
		car.setHeadNo(params.get("headNo"));
		car.setTailCity(params.get("tailCity"));
		car.setTailNo(params.get("tailNo"));
		//是否带爬梯
		if (StringUtils.isNotBlank(params.get("hasLadder"))) {
			car.setHasLadder(Integer.parseInt(params.get("hasLadder")));
		}
		if(StringUtils.isNotBlank(params.get("carType"))){
			car.setCarType(Integer.parseInt(params.get("carType")));
		}

		//6430新增车头\挂车行驶证有效期
		if (StringUtils.isNotBlank(params.get("headDrivingExpiredTime"))) {
			Long headDrivingExpiredTimeL = Long.valueOf(params.get("headDrivingExpiredTime"));
			if (headDrivingExpiredTimeL > 0) {
				Date headDrivingExpiredTime = TimeUtil.timeStampToDate(headDrivingExpiredTimeL);
				car.setHeadDrivingExpiredTime(headDrivingExpiredTime);
			}
		}

		if (StringUtils.isNotBlank(params.get("tailDrivingExpiredTime"))) {
			Long tailDrivingExpiredTimeL = Long.valueOf(params.get("tailDrivingExpiredTime"));
			if (tailDrivingExpiredTimeL > 0) {
				Date tailDrivingExpiredTime = TimeUtil.timeStampToDate(tailDrivingExpiredTimeL);
				if (Objects.nonNull(tailDrivingExpiredTime)) {
					car.setTailDrivingExpiredTime(tailDrivingExpiredTime);
				}
			}
		}

		// 如果认证成功的重新上传则设置AUth 为3 为后台 提示标识
		if ("1".equals(car.getAuth())) {
			car.setAuth("3");
		} else{
			car.setAuth("0");
		}
		// 车头信息修改
		if ("1".equals(params.get("updateType"))) {
			car.setHeadAuthStatus("0");
			car.setHeadFailReason("");
		}
		// 车挂信息
		else if ("2".equals(params.get("updateType"))) {
			car.setTailAuthStatus("0");
			car.setTailFailReason("");
		}
		// 车头及车挂信息
		else if ("3".equals(params.get("updateType"))) {
			car.setTailAuthStatus("0");
			car.setTailFailReason("");
			car.setHeadAuthStatus("0");
			car.setHeadFailReason("");
		}
		car.setFindGoodOnOff("0");
		car.setUpdateTime(new Date());
		car.setUpdateType(params.get("updateType"));
		car.setUpdateReason(params.get("updateReason"));

		//开票新增参数
		if ("1".equals(params.get("isDispatch"))){
			car.setIsDispatch("1");
			if ("1".equals(params.get("roadUpdateType"))){
				car.setRoadCardStatus("0");
				car.setRoadCardFailReason("");
			}else if ("2".equals(params.get("roadUpdateType"))){
				car.setRoadLicenseStatus("0");
				car.setRoadLicenseReason("");
			}else if ("3".equals(params.get("roadUpdateType"))){
				car.setRoadCardStatus("0");
				car.setRoadCardFailReason("");
				car.setRoadLicenseStatus("0");
				car.setRoadLicenseReason("");
			}
		}else {
			car.setIsDispatch("0");
		}
		if (headDrivingPic != null && !headDrivingPic.isEmpty()) {
			car.setHeadDrivingUrl(renamePic(headDrivingPic, "car"));
		}
		if (tailDrivingPic != null && !tailDrivingPic.isEmpty()){
			car.setTailDrivingUrl(renamePic(tailDrivingPic, "car"));
		}
		//车头行驶证副页反面url
		if (headDrivingSubpagePic != null && !headDrivingSubpagePic.isEmpty()){
			car.setHeadDrivingSubpageUrl(renamePic(headDrivingSubpagePic, "car"));
		}
		//车头道路运输证主页url
		if (headTransportHomepagePic != null && !headTransportHomepagePic.isEmpty()){
			car.setHeadTransportHomepageUrl(renamePic(headTransportHomepagePic, "car"));
		}
		//车头道路运输证副页url
		if (headTransportSubpagePic != null && !headTransportSubpagePic.isEmpty()){
			car.setHeadTransportSubpageUrl(renamePic(headTransportSubpagePic, "car"));
		}
		//挂车行驶证副页反面url
		if (tailDrivingSubpagePic != null && !tailDrivingSubpagePic.isEmpty()){
			car.setTailDrivingSubpageUrl(renamePic(tailDrivingSubpagePic, "car"));
		}
		//挂车道路运输证主页url
		if (tailTransportHomepagePic != null && !tailTransportHomepagePic.isEmpty()){
			car.setTailTransportHomepageUrl(renamePic(tailTransportHomepagePic, "car"));
		}
		//挂车道路运输证副页url
		if (tailTransportSubpagePic != null && !tailTransportSubpagePic.isEmpty()){
			car.setTailTransportSubpageUrl(renamePic(tailTransportSubpagePic, "car"));
		}
		//挂车照片
		if (tailPhotoPic !=null && !tailPhotoPic.isEmpty()){
			car.setTailPhotoUrl(renamePic(tailPhotoPic,"car"));
		}

		if (!Objects.isNull(tailDrivingOtherSidePic)){
			car.setTailDrivingOtherSideUrl(getPicUrlByPic(tailDrivingOtherSidePic));
		}

		if(headTransportHomepagePic != null || headTransportSubpagePic != null){
			// 车头道路运输证审核状态 0 认证中 1 认证通过 2 认证失败
			car.setHeadTransportAuthStatus(0);
		}
		if(tailTransportHomepagePic != null || tailTransportSubpagePic != null){
			// 挂车道路运输证审核状态 0 认证中 1 认证通过 2 认证失败
			car.setTailTransportAuthStatus(0);
		}
		//车牌号包含 超 为临时牌照 否则没有临牌
		String tailNo = car.getTailNo();
		if(StringUtils.isNotBlank(tailNo) && Objects.equals(tailNo.substring(tailNo.length()-1),"超")){
			if (StringUtils.isNotBlank(car.getHeadDrivingUrl()) && StringUtils.isNotBlank(car.getHeadTransportHomepageUrl()) && StringUtils.isNotBlank(car.getTailDrivingUrl()) && StringUtils.isNotBlank(car.getTailDrivingOtherSideUrl())) {
				car.setIsInvoice(CarInvoiceStatusEnum.WAIT_AUDIT.getCode());
				car.setThirdPartyRequire(CarInvoiceStatusEnum.WAIT_AUDIT.getCode());
			}
		}else {
			car.setTailDrivingOtherSideUrl(null);
			//非临牌开票判断
			if (StringUtils.isNotBlank(car.getHeadDrivingUrl()) && StringUtils.isNotBlank(car.getHeadTransportHomepageUrl()) && StringUtils.isNotBlank(car.getTailDrivingUrl())) {
				if (StringUtils.isNotBlank(car.getTailTransportHomepageUrl())){
					car.setIsInvoice(CarInvoiceStatusEnum.WAIT_AUDIT.getCode());
				}
				car.setThirdPartyRequire(CarInvoiceStatusEnum.WAIT_AUDIT.getCode());
			}
		}
		Integer carType = car.getCarType();
		if (null != carType && car.getIsInvoice() == 3){
			if (14 == carType && (StringUtils.isNotBlank(car.getHeadDrivingUrl()) && StringUtils.isNotBlank(car.getHeadTransportHomepageUrl()))){ //单机板
				car.setIsInvoice(CarInvoiceStatusEnum.WAIT_AUDIT.getCode());
				car.setThirdPartyRequire(CarInvoiceStatusEnum.WAIT_AUDIT.getCode());
			}
			if (15 == carType && StringUtils.isNotBlank(car.getHeadDrivingUrl())){ //清障车
				car.setIsInvoice(CarInvoiceStatusEnum.WAIT_AUDIT.getCode());
				car.setThirdPartyRequire(CarInvoiceStatusEnum.WAIT_AUDIT.getCode());
			}
		}
		//如果挂车ID不为空，则证明换绑了挂车，更新挂车相关的信息
		if(newCar != null){
			car.setTailCity(newCar.getTailCity());
			car.setTailNo(newCar.getTailNo());
			car.setTailDrivingUrl(newCar.getTailDrivingUrl());
			car.setTailDrivingSubpageUrl(newCar.getTailDrivingSubpageUrl());
			car.setTailDrivingOtherSideUrl(newCar.getTailDrivingOtherSideUrl());
			car.setTailTransportHomepageUrl(newCar.getTailTransportHomepageUrl());
			car.setTailTransportSubpageUrl(newCar.getTailTransportSubpageUrl());
			car.setTailName(newCar.getTailName());
			car.setTailPhone(newCar.getTailPhone());
			car.setTailBrand(newCar.getTailBrand());
			car.setTailAuthStatus(newCar.getTailAuthStatus());
			car.setTailFailReason(newCar.getTailFailReason());
			car.setTailTransportNo(newCar.getTailTransportNo());
			car.setTailDrivingExpiredTime(newCar.getTailDrivingExpiredTime());
			car.setTailTransportExpiredTime(newCar.getTailTransportExpiredTime());
			car.setTailTransportAuthStatus(newCar.getTailTransportAuthStatus());
			car.setTailTransportFailReason(newCar.getTailTransportFailReason());
		}
		//更改偏好
		carService.updatePreferNew(car.getId());
		return car;

	}

	private DispatchCarBean createDispatchCarBean(Map<String, String> params,MultipartFile roadCardPositivePic, MultipartFile roadCardOtherSidePic ,MultipartFile roadLicenseNoPic,Car car) throws IOException {
		DispatchCarBean dispatchCarBean=new DispatchCarBean();
		dispatchCarBean.setId(Long.valueOf(params.get("id")));
		dispatchCarBean.setRoadTransportType(params.get("roadTransportType"));
		dispatchCarBean.setRoadCardPositiveUrl(getPicUrlByPic(roadCardPositivePic));
		dispatchCarBean.setRoadCardOtherSideUrl("2".equals(params.get("roadTransportType")) ? "":getPicUrlByPic(roadCardOtherSidePic));
        String picUrlByPic = getPicUrlByPic(roadLicenseNoPic);
        if ("2".equals(car.getRoadLicenseStatus())&&picUrlByPic==null){
			car.setRoadLicenseStatus(null);
			car.setRoadLicenseReason("");
			dispatchCarBean.setRoadLicenseNoUrl("");
		}else {
			dispatchCarBean.setRoadLicenseNoUrl(picUrlByPic);
		}
		return dispatchCarBean;
	}

	/**
	 * 创建Car
	 *
	 * @param params·
	 * @return
	 * @throws Exception
	 */
	private CarSaveBean createCarSaveBean(Map<String, String> params, Long tailId) throws Exception {
		CarSaveBean car = new CarSaveBean();
		car.setUserId(Long.parseLong(params.get("userId")));
		//车头城市简称
		car.setHeadCity(params.get("headCity"));
		//车头车牌号
		car.setHeadNo(params.get("headNo"));
		//挂车城市简称
		car.setTailCity(params.get("tailCity"));
		//挂车车牌号
		car.setTailNo(params.get("tailNo"));
		car.setClientSign(params.get("clientSign"));
		//挂车拍照是否为临牌
		car.setIsTailTemporary(params.get("isTailTemporary"));
		//车辆类型
		String carType = params.get("carType");
		//是否纯平
		String isPureFlat = params.get("isPureFlat");
		String otherCarType = params.get("otherCarType");
		String otherPureFlat = params.get("otherPureFlat");
		String hasLadder = params.get("hasLadder");
		String isDispatch = params.get("isDispatch");
		String loadSurfaceLength = params.get("loadSurfaceLength");
		String loadSurfaceHeight = params.get("loadSurfaceHeight");
		String maxPayload = params.get("maxPayload");
		String roadTransportType = params.get("roadTransportType");
		String authPath = params.get("authPath");
		//6370 新增 挂车车牌号为临牌时 挂车长度
		String tailLengthStr = params.get("tailLength");
		//6370 新增 挂车车牌号为临牌时 挂车宽度
		String tailWidthStr = params.get("tailWidth");
		//6370 新增 挂车车牌号为临牌时 挂车高度
		String tailHeightStr = params.get("tailHeight");
		//6430 新增 车头驾驶证有效期
		String headDrivingExpiredTime = params.get("headDrivingExpiredTime");
		//6430 新增 挂车驾驶证有效期
		String tailDrivingExpiredTime = params.get("tailDrivingExpiredTime");
		//如果挂车ID不为空，则证明换绑了挂车，更新挂车相关的信息
		if(tailId != null){
			logger.info("传入的挂车ID为：{}", tailId);
			//查询换绑的新的挂车信息
			CarDetailTail carDetailTail = carDetailTailService.getById(tailId);
			logger.info("换绑挂车信息:{}", JSON.toJSONString(carDetailTail));
			//获取换绑挂车的主信息
			Car newCar = carService.getById(carDetailTail.getCarId());
			if (ObjectUtil.isNotNull(newCar.getCarType())) {
				carType = String.valueOf(newCar.getCarType());
			}
			if (ObjectUtil.isNotNull(carDetailTail.getIsPureFlat())) {
				isPureFlat = String.valueOf(carDetailTail.getIsPureFlat());
			}
			logger.info("换绑挂车的主信息:{}, 车辆类型：{}，是否纯平：{}", JSON.toJSONString(newCar), carType, isPureFlat);
		}

		//2019-10-23 xyy 增加挂车类型
		if (StringUtils.isNotEmpty(carType)){
			car.setCarType(Integer.parseInt(carType));
			if (StringUtils.isNotBlank(isPureFlat)){
				car.setIsPureFlat(Integer.parseInt(isPureFlat));
			}
			if ("0".equals(carType)&& otherCarType ==null){
				car.setOtherCarType("其他");
			}else {
				car.setOtherCarType(otherCarType);
			}
			if ("0".equals(isPureFlat)&& otherPureFlat ==null){
				car.setOtherPureFlat("其他");
			}
		}
		if (StringUtils.isNotBlank(hasLadder)){
			car.setHasLadder(Integer.parseInt(hasLadder));
		}else {
			car.setHasLadder(1);
		}
		if (StringUtils.isNotBlank(tailLengthStr)){
			//由m转换为mm
			String tailLength = new BigDecimal(tailLengthStr).movePointRight(3).toPlainString();
			car.setTailLength(tailLength);
		}
		if (StringUtils.isNotBlank(tailWidthStr)){
			//由m转换为mm
			String tailWidth = new BigDecimal(tailWidthStr).movePointRight(3).toPlainString();
			car.setTailWidth(tailWidth);
		}
		if (StringUtils.isNotBlank(tailHeightStr)){
			//由m转换为mm
			String tailHeight = new BigDecimal(tailHeightStr).movePointRight(3).toPlainString();
			car.setTailHeight(tailHeight);
		}
		//开票新增
		if ("1".equals(isDispatch)){
			car.setIsDispatch("1");
            car.setLoadSurfaceLength(loadSurfaceLength);
            car.setMaxPayload(maxPayload);
            car.setRoadTransportType(roadTransportType);
		}else {
			car.setIsDispatch("0");
		}
		if (StringUtils.isNotBlank(loadSurfaceHeight)){
			car.setLoadSurfaceHeight(loadSurfaceHeight);
		}else{
			car.setLoadSurfaceHeight("0.8");
		}
		// 设置最大载重
		car.setMaxPayload(CarTypeMaxPayload.getMaxPayload(carType, maxPayload));
		car.setAuth("0");
		// 设置车辆认证来源
		car.setAuthPath(authPath);
		//6430新增车头挂车行驶证过期时间
		if (StringUtils.isNotBlank(headDrivingExpiredTime)) {
			car.setHeadDrivingExpiredTime(Long.valueOf(headDrivingExpiredTime));
		}
		if (StringUtils.isNotBlank(tailDrivingExpiredTime)) {
			car.setTailDrivingExpiredTime(Long.valueOf(tailDrivingExpiredTime));
		}
		return car;
	}

	/**
	 * 车辆认证
	 *
	 * @param request
	 * @param response
	 * @param headDrivingPic
	 * @param tailDrivingPic
	 */
	@RequestMapping(value = "/identity/saveNew")
	@ResponseBody
	public void identitySaveNew(HttpServletRequest request, HttpServletResponse response, @RequestParam String headDrivingPic, @RequestParam(required = false) String tailDrivingPic,
								@RequestParam(required = false) String tailDrivingOtherSidePic, @RequestParam(required = false) String roadCardPositivePic,
								@RequestParam(required = false) String roadCardOtherSidePic ,@RequestParam(required = false) String roadLicenseNoPic) {
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			Long userId = Long.parseLong(params.get("userId"));
			User user = userService.getById(userId);
			if (user.getIsDispatch()!=null){
				params.put("isDispatch",user.getIsDispatch().toString());
			}
			if (user == null) {
				backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "该用户不存在", null, 0);
				return;
			}
			String condition = "userId_" + userId + "车辆认证 ";
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("headCity");
					add("headNo");
					add("tailCity");
					add("tailNo");
					add("clientSign");
				}
			};
			if (!validateArguments(condition, request, response, params, names)){
				return;
			}
			/* 验证重复信息？ */
			if (carService.get(params)) {
				logger.error(condition + "车辆已存在，请勿重复添加");
				backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "车辆已存在，请勿重复添加", null, 0);
				return;
			}
			//开票参数验证
			if(!carParametersVerificationNew(params,roadCardPositivePic)){
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "参数有误", null, 0);
				return;
			}
			/* 判断照片是否上传？ */
			if (StringUtils.isBlank(headDrivingPic)) {
				logger.error(condition + "图片为空");
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "车头证件照不能为空", null, 0);
				return;
			}
			if (StringUtils.isBlank(tailDrivingPic)) {
				logger.error(condition + "图片为空");
				backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "挂车证件照不能为空", null, 0);
				return;
			}

			/* 创建Car */
			CarSaveBean car = createCarSaveBean(params,null);
			//开票新增参数
			Long id = carService.saveCar(car,headDrivingPic,tailDrivingPic,tailDrivingOtherSidePic,roadCardPositivePic,roadCardOtherSidePic,roadLicenseNoPic,
					null,null,null,null,null,null,null,null);
			CarIdBean carId = new CarIdBean();
			carId.setId(id);
			backResponse(request, response, ReturnCodeConstant.OK, "车辆信息添加成功", carId, 0);
			logger.info(condition + "添加成功");
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}

	/**
     *
     * 重新审核车辆
     *
     */
    @RequestMapping(value = "/identity/updatesaveNew")
    public void updateNew(HttpServletRequest request, HttpServletResponse response, @RequestParam(required = false) String headDrivingPic, @RequestParam(required = false) String tailDrivingPic,
                       @RequestParam(required = false) String tailDrivingOtherSidePic, @RequestParam(required = false) String roadCardPositivePic,
                       @RequestParam(required = false) String roadCardOtherSidePic ,@RequestParam(required = false) String roadLicenseNoPic) {
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "车辆认证更新 ";
            User user = userService.getById(userId);
            if (user.getIsDispatch()!=null){
                params.put("isDispatch",user.getIsDispatch().toString());
            }
            if (user == null) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "该用户不存在", null, 0);
                return;
            }
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("id");
                    add("headCity");
                    add("headNo");
                    add("tailCity");
                    add("tailNo");
                }
            };
            if (!validateArguments(condition, request, response, params, names)){
                return;
            }
			/* 验证重复信息？ */
			if (carService.get(params)) {
				logger.error(condition + "车辆已存在，请勿重复添加");
				backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "车辆已存在，请勿重复添加", null, 0);
				return;
			}
            if(!carParametersVerificationNew(params,roadCardPositivePic,roadLicenseNoPic)){
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "参数有误", null, 0);
                return;
            }

            // 更新
            Car car = updateCarNew(params, headDrivingPic, tailDrivingPic,tailDrivingOtherSidePic);
            CarSaveBean carSaveBean = createCarSaveBean(params,null);
            if("1".equals(params.get("isDispatch"))){
                DispatchCarBean dispatchCarBean = createDispatchCarBeanNew(params, roadCardPositivePic, roadCardOtherSidePic, roadLicenseNoPic,car);
                carService.updateCarHead(dispatchCarBean,car);
            }
            if (car == null) {
                logger.info(condition + "更新失败");
                backResponse(request, response, ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "没有该车", null, 0);
                return;
            }
            carService.update(car);
            syncAuthCarMsg(car.getId());

            //2019-10-23 xyy 更改挂车样式
            if (StringUtils.isNotBlank(params.get("isPureFlat"))){
                carService.saveTailDetail(car.getId(),car.getUserId(),car.getTailCity(),car.getTailNo(),Integer.parseInt(params.get("isPureFlat")),carSaveBean);
            }
            //初始化偏好 初始化出发地目的地载重为2-50吨  暂时不上线
//			initPreferenceNew(params,car.getId());

            //更改user表中is_car状态
            int carAuth = carService.isCarAuth(user.getId(), "1");
            if (carAuth>0 && !"1".equals(user.getIsCar())) {
                user.setIsCar("1");
                user.setMtime(new Timestamp(System.currentTimeMillis()));
                userService.update(user);
                cacheService.del(Constant.CACHE_USER_KEY + user.getId());
                cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
            }
            if (carAuth<=0 && "1".equals(user.getIsCar())) {
                user.setIsCar("0");
                user.setMtime(new Timestamp(System.currentTimeMillis()));
                userService.update(user);
                cacheService.del(Constant.CACHE_USER_KEY + user.getId());
                cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
            }
            backResponse(request, response, ReturnCodeConstant.OK, "车辆信息更新成功", null, 0);
            logger.info(condition + "更新成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }

    }

    /**
	 * 开票相关新增车辆参数校验
	 * @param params
	 * @param roadCardPositivePic
	 * @return
	 */
	private boolean carParametersVerificationNew(Map<String, String> params,String roadCardPositivePic){
		if (StringUtils.isNotEmpty(params.get("isDispatch"))&&"1".equals(params.get("isDispatch"))){
			if (StringUtils.isBlank(roadCardPositivePic)) {
				return false;
			}

			List<String> names = new ArrayList<String>() {
				{
					//工作面长
					add("loadSurfaceLength");
					//工作面高
					add("loadSurfaceHeight");
					//最大载重
					add("maxPayload");
				}
			};
			for (String name : names) {
				if(StringUtils.isEmpty(params.get(name))){
					logger.info("----------------车辆新增时有误参数："+name);
					return false;
				}
			}

		}
		return true;
	}

	/**
	 * 开票相关修改车辆参数校验
	 * @param params
	 * @param roadCardPositivePic
	 * @param roadLicenseNoPic
	 * @return
	 */
	private boolean carParametersVerificationNew(Map<String, String> params,String roadCardPositivePic,String roadLicenseNoPic){
		if (StringUtils.isNotEmpty(params.get("isDispatch"))&&"1".equals(params.get("isDispatch"))){
			if (StringUtils.isBlank(roadCardPositivePic)){
				if ("1".equals(params.get("roadUpdateType"))||"3".equals(params.get("roadUpdateType"))){
					return false;
				}
			}
			if (StringUtils.isBlank(roadLicenseNoPic)){
				if ("2".equals(params.get("roadUpdateType"))||"3".equals(params.get("roadUpdateType"))){
					return false;
				}
			}
			if (StringUtils.isBlank(params.get("roadTransportType"))){
				logger.info("roadTransportType:{}-------------",params.get("roadTransportType"));
				return false;
			}
			return true;
		}else {
			return true;
		}

	}

	/**
	 * 更新car
	 *
	 * @param params
	 * @param headDrivingPic
	 * @param tailDrivingPic
	 * @return
	 * @throws Exception
	 */
	private Car updateCarNew(Map<String, String> params, String headDrivingPic, String tailDrivingPic,
							 String tailDrivingOtherSidePic) throws Exception {
		Car car = carService.getById(Long.parseLong(params.get("id")));
		if (car == null) {
			return null;
		}
		// 认证成功的车辆重新审核时保存修改記錄
		if ("1".equals(car.getAuth())) {
			CarLog carLog = new CarLog();
			Car carNew = new Car();
			BeanUtils.copyProperties(carNew, car);
			carNew.setId(null);
			BeanUtils.copyProperties(carLog, carNew);
			carLog.setRecordTime(new Date());
			carLog.setCarId(car.getId());
			carLog.setUpdateType(params.get("updateType"));
			carLog.setUpdateReason(params.get("updateReason"));
			carLog.setIsDisplay(1);
			carLogService.add(carLog);
		}
		car.setUserId(Long.parseLong(params.get("userId")));
		car.setHeadCity(params.get("headCity"));
		car.setHeadNo(params.get("headNo"));
		car.setTailCity(params.get("tailCity"));
		car.setTailNo(params.get("tailNo"));
		if(StringUtils.isNotBlank(params.get("carType"))){
			car.setCarType(Integer.parseInt(params.get("carType")));
		}
		// 如果认证成功的重新上传则设置AUth 为3 为后台 提示标识
		if ("1".equals(car.getAuth())) {
			car.setAuth("3");
		} else{
			car.setAuth("0");
		}
		// 车头信息修改
		if ("1".equals(params.get("updateType"))) {
			car.setHeadAuthStatus("0");
			car.setHeadFailReason("");
		}
		// 车挂信息
		else if ("2".equals(params.get("updateType"))) {
			car.setTailAuthStatus("0");
			car.setTailFailReason("");
		}
		// 车头及车挂信息
		else if ("3".equals(params.get("updateType"))) {
			car.setTailAuthStatus("0");
			car.setTailFailReason("");
			car.setHeadAuthStatus("0");
			car.setHeadFailReason("");
		}
		car.setFindGoodOnOff("0");
		car.setUpdateTime(new Date());
		car.setUpdateType(params.get("updateType"));
		car.setUpdateReason(params.get("updateReason"));

		//开票新增参数
		if ("1".equals(params.get("isDispatch"))){
			car.setIsDispatch("1");
			if ("1".equals(params.get("roadUpdateType"))){
				car.setRoadCardStatus("0");
				car.setRoadCardFailReason("");
			}else if ("1".equals(params.get("roadUpdateType"))){
				car.setRoadLicenseStatus("0");
				car.setRoadLicenseReason("");
			}else if ("3".equals(params.get("roadUpdateType"))){
				car.setRoadCardStatus("0");
				car.setRoadCardFailReason("");
				car.setRoadLicenseStatus("0");
				car.setRoadLicenseReason("");
			}
			if (tailDrivingOtherSidePic!=null){
				car.setTailDrivingOtherSideUrl(tailDrivingOtherSidePic);
			}
		}else {
			car.setIsDispatch("0");
		}
		if (headDrivingPic != null) {
			car.setHeadDrivingUrl(headDrivingPic);
		}
		if (tailDrivingPic != null){
			car.setTailDrivingUrl(tailDrivingPic);
		}
		//更改偏好
		carService.updatePreferNew(car.getId());
		return car;

	}

	private DispatchCarBean createDispatchCarBeanNew(Map<String, String> params,String roadCardPositivePic, String roadCardOtherSidePic ,String roadLicenseNoPic,Car car) throws IOException {
		DispatchCarBean dispatchCarBean=new DispatchCarBean();
		dispatchCarBean.setId(Long.valueOf(params.get("id")));
		dispatchCarBean.setRoadTransportType(params.get("roadTransportType"));
		dispatchCarBean.setRoadCardPositiveUrl(roadCardPositivePic);
		dispatchCarBean.setRoadCardOtherSideUrl(roadCardOtherSidePic);
		if (roadLicenseNoPic==null){
			car.setRoadLicenseStatus(null);
			car.setRoadLicenseReason("");
		}else {
			dispatchCarBean.setRoadLicenseNoUrl(roadLicenseNoPic);
		}
		return dispatchCarBean;
	}

	/**
	 * OCR 识别车辆行驶证信息
	 * @param url 行驶证照片url
	 * @return com.tyt.model.ResultMsgBean
	 */
	@RequestMapping(value = "/ocrDrivingLicenseInfo")
	@ResponseBody
	public ResultMsgBean ocrDrivingLicenseInfo(String url) {
		if (StringUtils.isBlank(url)) {
			return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
		}
		logger.info("OCR识别车辆行驶证信息,参数url={}", url);
		return carService.ocrDrivingLicenseInfo(url);
	}


	/**
	 * OCR 识别车辆行驶证主页信息(6420新增 目前车主认证在用)
	 * @param url 行驶证照片url
	 * @return com.tyt.model.ResultMsgBean
	 */
	@RequestMapping(value = "/ocrVehicleLicenseFront")
	@ResponseBody
	public ResultMsgBean ocrVehicleLicenseFront(String url){
		if (StringUtils.isBlank(url)) {
			return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
		}
		VehicleLicenseFrontVo vehicleLicenseFrontVo = commonApiService.vehicleLicenseMainOcr(url);
		vehicleLicenseFrontVo.setOcrJsonText(null);
		return ResultMsgBean.successResponse(vehicleLicenseFrontVo);
	}

	/**
	 * OCR识别行驶证正副页 并返回相关提示信息（6430新增 目前车辆认证在用）
	 * @param url
	 * @return
	 */
	@RequestMapping(value = "/getCarOcrDrivingLicenseInfo")
	@ResponseBody
	public ResultMsgBean getCarOcrDrivingLicenseInfo(String url){
		if (StringUtils.isBlank(url)) {
			return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
		}

		return carService.getCarOcrDrivingLicenseInfo(url);
	}

	/**
	 * 刷新用户车辆是否可以开票信息
	 * @param userId
	 * @return
	 */
	@RequestMapping(value = "refreshIsInvoice")
	@ResponseBody
	public ResultMsgBean refreshIsInvoice(Long userId){
		if (Objects.isNull(userId)){
			return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少参数");
		}
		carService.refreshIsInvoice(userId);
		return ResultMsgBean.successResponse();
	}
}
