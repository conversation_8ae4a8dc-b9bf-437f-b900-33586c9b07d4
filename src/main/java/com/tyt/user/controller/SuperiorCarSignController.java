package com.tyt.user.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.enterprise.pojo.vo.LicenseInfoWithOCRResp;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.bean.SuperiorCarSignStatusResBean;
import com.tyt.user.service.*;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@Slf4j
@Validated
@RestController
@RequestMapping("/plat/superiorCar")
public class SuperiorCarSignController extends BaseController {

	@Resource(name = "superiorCarSignService")
	private SuperiorCarSignService superiorCarSignService;

	/**
	 * 查询优车签约状态
	 * <a href="http://192.168.2.20:3300/project/37/interface/api/11833">...</a>
	 */
	@GetMapping(value = {"/getSignStatus","/getSignStatus.action"})
	public ResultMsgBean getSignStatus(@NotNull(message = "用户ID不能为空")Long userId) {
		try {
			SuperiorCarSignStatusResBean data = superiorCarSignService.getSignStatus(userId);
			return ResultMsgBean.successResponse(data);
		} catch (TytException tytException) {
			return ResultMsgBean.failResponse(tytException.getErrorCode(),tytException.getErrorMsg());
		} catch (Exception e) {
			log.error("SuperiorCar getSignStatus error,param:", e);
			return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "查询优车签约状态异常");
		}
	}

	/**
	 * 优车签约接口
	 * <a href="http://192.168.2.20:3300/project/37/interface/api/11826">...</a>
	 */
	@PostMapping(value = {"/sign","/sign.action"})
	public ResultMsgBean sign(@NotNull(message = "用户ID不能为空")Long userId) {
		try {
			return superiorCarSignService.sign(userId);
		} catch (TytException tytException) {
			return ResultMsgBean.failResponse(tytException.getErrorCode(),tytException.getErrorMsg());
		} catch (Exception e) {
			log.error("SuperiorCar sign error,param:", e);
			return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "优车签约接口异常");
		}
	}
}
