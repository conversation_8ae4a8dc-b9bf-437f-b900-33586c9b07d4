package com.tyt.user.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.model.User;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.plat.entity.base.TytUserWifiLog;
import com.tyt.plat.mapper.base.TytUserWifiLogMapper;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 保存用户wifi记录
 * @date 2024/03/05 11:40
 */
@RestController
@RequestMapping("/plat/user")
@Slf4j
public class UserWifiLogController extends BaseController {


    @Autowired
    private TytUserWifiLogMapper tytUserWifiLogMapper;

    @Autowired
    private UserService userService;

    @PostMapping("/saveWifi")
    @ResponseBody
    public ResultMsgBean getCarList(HttpServletRequest request,TytUserWifiLog tytUserWifiLog) {
        try {
            Map<String, String> params = parseRequestParams(request);
            String userId = params.get("userId");
            String clientSign = params.get("clientSign");
            log.info("用户信息userId：{}，clientSign{},wifi名称{}",userId,clientSign,tytUserWifiLog.getWifiName());

            //参数判断
            if(StringUtils.isEmpty(userId) || StringUtils.isEmpty(clientSign) || StringUtils.isEmpty(tytUserWifiLog.getWifiName())){
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }

            User user = userService.getById(Long.valueOf(userId));
            if(null == user){
                return ResultMsgBean.failResponse(ResponseEnum.sys_error.info("无用户信息"));
            }
            tytUserWifiLog.setUserId(Long.valueOf(userId));
            tytUserWifiLog.setType(clientSign);
            tytUserWifiLog.setPhone(user.getCellPhone());
            tytUserWifiLog.setCreateTime(new Date());
            tytUserWifiLog.setEscalationTime(new Date());
            tytUserWifiLog.setUpdateTime(new Date());
            tytUserWifiLogMapper.insert(tytUserWifiLog);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            log.error("用户wifi保存失败", e);
            return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
        }
    }



}
