package com.tyt.user.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;

@Controller
@RequestMapping("/plat/userAPP")
public class UserAPPController extends BaseController{

	public static Logger userApp = LoggerFactory.getLogger("userApp");
	
	@RequestMapping(value = "/outputUserAPP")
	@ResponseBody
	public ResultMsgBean outputUserAPP(Long userId,String ticket, String deviceType,String appNames){
		ResultMsgBean result = new ResultMsgBean();
		try {
			String[] split = appNames.split(";");
			for (int i = 0; i < split.length; i++) {
				userApp.info("|"+userId+"|"+ticket+"|"+deviceType+"|"+split[i]);
			}
			result.setCode(200);
			result.setMsg("日志打印完成");
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("服务器错误");
		}
		return result;
	}
}
