package com.tyt.user.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.BlockInfo;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.User;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.bean.ComplaintReasonBean;
import com.tyt.user.bean.ComplaintReasonBean.ComplaintReasonVO;
import com.tyt.user.querybean.SourceBean;
import com.tyt.user.service.BlockInfoService;
import com.tyt.user.service.TytSourceService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.MobileUtil;
import com.tyt.util.ReturnCodeConstant;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/plat/complaint")
public class BlockInfoController extends BaseController {

	@Resource(name = "blockInfoService")
	private BlockInfoService blockInfoService;
	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;
	@Resource(name = "tytSourceService")
	private TytSourceService sourceService;
	/**
	 * 采集
	 * @return
	 */
	@RequestMapping(value = {"/save","/save.action"})
	@ResponseBody
	public ResultMsgBean save(BaseParameter baseParameter,
			String blockerTel, String blockerName,String reason,Long tsId, String beFrom){
		ResultMsgBean rm = new ResultMsgBean();
		try {
			String clientSign = baseParameter.getClientSign();
			if (!Strings.isNullOrEmpty(clientSign) && String.valueOf(Constant.ClientSignEnum.WEB_GOODS.code).equals(clientSign)) {
				if (Strings.isNullOrEmpty(blockerTel) || Strings.isNullOrEmpty(blockerName)) {
					rm.setCode(3001);
					rm.setMsg("被投诉人手机号或姓名不能为空");
				}
			}
//			if(null==blockerTel||"".equals(blockerTel)){
//				rm.setCode(3001);
//				rm.setMsg("被投诉人手机号不能为空");
//			}else
			if(null==reason||"".equals(reason.trim())){
				rm.setCode(3002);
				rm.setMsg("投诉内容不能为空");
			}else{
				BlockInfo blockInfo=new BlockInfo();
				blockInfo.setCtime(new Timestamp(System.currentTimeMillis()));
				if (StringUtils.isNotBlank(blockerTel)){
                    blockInfo.setBlockerTel(blockerTel);
                }
				if(tsId!=null&& tsId.longValue()!=0l){
					TransportMain  transportMain=transportMainService.getTransportMainForId(tsId);
					if(transportMain!=null){
						Long userId=transportMain.getUserId();
						User complaintUser=userService.getById(userId);
						blockInfo.setBlockerName(complaintUser.getTrueName());
						blockInfo.setCellPhone(complaintUser.getCellPhone());
						blockInfo.setQq(complaintUser.getQq());
						//20180115.如果骗子手机号为空赋值帐号
						if(StringUtils.isBlank(blockerTel)){
							blockInfo.setBlockerTel(complaintUser.getCellPhone());
						}

					}
					blockInfo.setInfoId(tsId==null?null:tsId.longValue()==0?null:tsId.longValue());

				}
				if(baseParameter.getUserId()!=null && baseParameter.getUserId().longValue()!=0l){
					User user=userService.getById(baseParameter.getUserId());
					blockInfo.setInformerTel(user.getCellPhone());
				}
				//blockInfo.setNote();
				blockInfo.setPlatId(baseParameter.getClientSign()==null?2:Integer.parseInt(baseParameter.getClientSign()));
				blockInfo.setReason(reason);
				//20180115.修改
				blockInfo.setAddress(MobileUtil.getMobileAddress(blockInfo.getBlockerTel()));
				blockInfo.setStatus(1);
				blockInfo.setBeFrom(beFrom);
				//blockInfo.setVerifyCause();

				if (String.valueOf(Constant.ClientSignEnum.WEB_GOODS.code).equals(clientSign)) {
					blockInfo.setBlockerName(blockerName);
					blockInfo.setCellPhone(blockerTel);
				}

				blockInfoService.add(blockInfo);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("保存成功");
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

    /**
     * 获取货源删除原因
     */
    @GetMapping(value = "/reason")
    @ResponseBody
    public ResultMsgBean getReasons(BaseParameter baseParameter) {
        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse();
        try {
            List<SourceBean> reasons = sourceService.getByGroupCodeWithNoLimit("transport_complaint_reason");
            reasons = reasons == null ? Collections.emptyList() : reasons;

			List<ComplaintReasonVO> complaintReasonVOS = reasons.stream().map(reason -> {
				ComplaintReasonVO complaintReason = new ComplaintReasonVO();
				complaintReason.setReasonDesc(reason.getValue());
				return complaintReason;
			}).collect(Collectors.toList());

			ComplaintReasonBean complaintReasonBean = new ComplaintReasonBean();
			complaintReasonBean.setReasaons(complaintReasonVOS);
			resultMsgBean.setData(complaintReasonBean);
		} catch (Exception e) {
            logger.error("服务器异常", e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }
}
