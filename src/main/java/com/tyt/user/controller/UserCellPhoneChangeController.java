package com.tyt.user.controller;

import com.alibaba.fastjson.JSONObject;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.plat.entity.base.TytCellPhoneChangeAudit;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.bean.CellPhoneChangeAuditInfo;
import com.tyt.user.enums.CellPhoneChangeAuditStatusEnum;
import com.tyt.user.enums.VerifyPhotoSignEnum;
import com.tyt.user.service.UserCellPhoneChangeService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.IdUtils;
import com.tyt.util.MobileUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.verificationcode.service.VerificationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户变更手机号
 * @date 2023/09/05 09:46
 */
@RestController
@RequestMapping(value = "/plat/userCellPhoneChange")
@Slf4j
public class UserCellPhoneChangeController {
    @Autowired
    private UserCellPhoneChangeService userCellPhoneChangeService;
    @Autowired
    private UserService userService;
    @Autowired
    private VerificationCodeService verificationCodeService;

    private static final Integer CHECK_TYPE = 2;

    /**
     *  判断用户是否可以变更手机号
     * <AUTHOR>
     * @param userId
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = "getIsChangeInfo")
    public ResultMsgBean getIsChangeInfo(Long userId) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "参数错误");
        }
        return userCellPhoneChangeService.getIsChangeInfo(userId);
    }

    /**
     *  用户变更手机号 校验验证码
     * <AUTHOR>
     * @param phone
     * @param captcha
     * @param checkType 1 原手机号验证 2 新手机号验证
     * @return com.tyt.model.ResultMsgBean
     */
    @PostMapping(value = "captchaCheck")
    public ResultMsgBean captchaCheck(String phone, String captcha, Integer checkType) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(captcha) || Objects.isNull(checkType)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "参数错误");
        }

        if (BooleanUtils.isFalse(MobileUtil.isMobile(phone))) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.PHONE_FORMAT_ERROR, "手机号码格式错误");
        }

        if (CHECK_TYPE.equals(checkType)) {
            //新手机号已在平台注册不允许更换
            User user = userService.getUserByCellphone(phone);
            if (Objects.nonNull(user)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BOUND_PHONE_CANT_CHANGE_PHONE_REPEAT, "手机号已被注册，无法更换");
            }
        }
        ResultMsgBean verify = verificationCodeService.verify(phone, captcha);
        String randomIdByUUID = IdUtils.getRandomIdByUUID();
        //该缓存为了防止用户变更手机号 跳过第一步原手机号验证直接进行第二步变更手机号
        RedisUtil.set(Constant.CELLPHONE_CHANGE_STEP + phone, randomIdByUUID, Constant.CACHE_EXPIRE_TIME_10MIN_INT);
        verify.setData(randomIdByUUID);
        return verify;
    }

    /**
     *  用户自主变更手机号
     * <AUTHOR>
     * @param userId
     * @param newPhone
     * @param oldPhone
     * @return com.tyt.model.ResultMsgBean
     */
    @PostMapping(value = "updateCellPhone")
    public ResultMsgBean updateCellPhone(Long userId, String newPhone, String oldPhone, String captcha,String stepTicket) {
        log.info("用户变更手机号,自主变更参数信息userId={},newPhone={},oldPhone={},captcha={},stepTicket{}", userId, newPhone, oldPhone, captcha, stepTicket);
        if (StringUtils.isBlank(newPhone) || StringUtils.isBlank(oldPhone) || StringUtils.isBlank(captcha) || StringUtils.isBlank(stepTicket)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "参数错误");
        }

        if (BooleanUtils.isFalse(MobileUtil.isMobile(newPhone))) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.PHONE_FORMAT_ERROR, "手机号码格式错误");
        }
        //新手机号已在平台注册不允许更换
        User user = userService.getUserByCellphone(newPhone);
        if (Objects.nonNull(user)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BOUND_PHONE_CANT_CHANGE_PHONE_REPEAT, "手机号已被注册，无法更换");
        }

        //该校验为了限制用户跳过第一步原手机号验证直接进行手机号变更
        String stepCacheTicket = RedisUtil.get(Constant.CELLPHONE_CHANGE_STEP + oldPhone);
        if (!stepTicket.equals(stepCacheTicket)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "请先验证原手机号");
        }

        //验证码校验
        ResultMsgBean captchaCheck = verificationCodeService.verify(newPhone, captcha);
        if (!captchaCheck.isSuccess()) {
            return captchaCheck;
        }

        try {
            User byUserId = userService.getByUserId(userId);
            if (Objects.isNull(byUserId)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.USER_NON, "用户不存在");
            }

            //传过来的原手机号与数据库不匹配
            if (!oldPhone.trim().equals(byUserId.getCellPhone().trim())) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "原手机号校验失败");
            }


            //变更规则校验 是否符合更换条件
            ResultMsgBean isChangeInfo = userCellPhoneChangeService.getIsChangeInfo(userId);
            if (!isChangeInfo.isSuccess()) {
                return isChangeInfo;
            }

            userCellPhoneChangeService.updateCellPhone(userId, oldPhone, newPhone.trim());
            log.info("用户变更手机号,自主变更成功userId={},newPhone={},oldPhone={},captcha={}", userId, newPhone, oldPhone, captcha);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            log.error("用户变更手机号,自主变更手机号异常:", e);
        }
        return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "系统异常");
    }

    /**
     * 用户变更手机号-校验是否可以提交审核变更
     * <AUTHOR>
     * @param userId
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = "getIsAuditChangeInfo")
    public ResultMsgBean getIsAuditChangeInfo(Long userId) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "参数错误");
        }

        User nonCacheUser = userService.getByUserIdNonCache(userId);
        if (Objects.isNull(nonCacheUser)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.USER_NON, "用户不存在");
        }

        if (Objects.isNull(nonCacheUser.getVerifyPhotoSign()) || !Objects.equals(nonCacheUser.getVerifyPhotoSign(), VerifyPhotoSignEnum.CODE_1.getCode())) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.USER_UNVERIFIED, "请先完成实名认证");
        }

        return ResultMsgBean.successResponse();
    }

    /**
     *  用户变更手机号-审核变更保存变更信息
     * <AUTHOR>
     * @param auditInfo
     * @return com.tyt.model.ResultMsgBean
     */
    @PostMapping(value = "saveChangeAuditInfo")
    public ResultMsgBean saveChangeAuditInfo(CellPhoneChangeAuditInfo auditInfo) {
        log.info("用户变更手机号,审核变更提交的审核信息为:{}", JSONObject.toJSONString(auditInfo));
        if (Objects.isNull(auditInfo) || StringUtils.isBlank(auditInfo.getNewCellPhone()) || StringUtils.isBlank(auditInfo.getOldCellPhone())
                || StringUtils.isBlank(auditInfo.getHandIdPhoto()) || StringUtils.isBlank(auditInfo.getHandApplyForPhoto())) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "参数错误");
        }

        if (BooleanUtils.isFalse(MobileUtil.isMobile(auditInfo.getNewCellPhone()))){
            return ResultMsgBean.failResponse(ReturnCodeConstant.PHONE_FORMAT_ERROR, "手机号码格式错误");
        }

        User nonCacheUser = userService.getByUserIdNonCache(auditInfo.getUserId());
        if (Objects.isNull(nonCacheUser)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.USER_NON, "用户不存在");
        }

        //新手机号已在平台注册不允许更换
        User user = userService.getUserByCellphone(auditInfo.getNewCellPhone());
        if (Objects.nonNull(user)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BOUND_PHONE_CANT_CHANGE_PHONE_REPEAT, "手机号已被注册，无法更换");
        }


        if (Objects.isNull(nonCacheUser.getVerifyPhotoSign()) || !Objects.equals(nonCacheUser.getVerifyPhotoSign(), VerifyPhotoSignEnum.CODE_1.getCode())) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.USER_UNVERIFIED, "请先完成实名认证");
        }

        //传过来的原手机号与数据库不匹配
        if (!auditInfo.getOldCellPhone().trim().equals(nonCacheUser.getCellPhone().trim())) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "原手机号校验失败");
        }

        //变更规则校验 是否符合更换条件
        ResultMsgBean isChangeInfo = userCellPhoneChangeService.getIsChangeInfo(auditInfo.getUserId());
        if (!isChangeInfo.isSuccess()) {
            return isChangeInfo;
        }

        TytCellPhoneChangeAudit tytCellPhoneChangeAudit = new TytCellPhoneChangeAudit();
        BeanUtils.copyProperties(auditInfo, tytCellPhoneChangeAudit);
        tytCellPhoneChangeAudit.setTrueName(nonCacheUser.getTrueName());
        tytCellPhoneChangeAudit.setAuditStatus(CellPhoneChangeAuditStatusEnum.CHECK_PENDING.getAuditStatus());
        userCellPhoneChangeService.saveChangeAuditInfo(tytCellPhoneChangeAudit);
        return ResultMsgBean.successResponse();
    }
}
