package com.tyt.user.controller;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.user.service.VerifyLogService;
import com.tyt.util.ReturnCodeConstant;
/**
 * 验证码接口
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/plat/verify")
public class VerifyCodeController  extends BaseController{
	@Resource(name = "verifyLogService")
	private VerifyLogService verifyLogService;

	/**
	 * 发送语音验证码
	 * @param baseParameter
	 * @param phone
	 * @param verifyCode
	 * @return
	 */
	@RequestMapping(value = "/sendVoiceVerify")
	@ResponseBody
	public ResultMsgBean sendVoiceVerify(BaseParameter baseParameter, String phone,String verifyCode) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (StringUtils.isBlank(phone)||StringUtils.isBlank(verifyCode)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("电话号或验证码不能为空！");
			} else {
				boolean isSuccess=verifyLogService.saveSendVerifyCode(baseParameter.getUserId()!=null?baseParameter.getUserId().toString():null, phone, verifyCode, "1");
				if(isSuccess){
					rm.setCode(ReturnCodeConstant.OK);
					rm.setMsg("发送成功");
				}else{
					rm.setCode(ReturnCodeConstant.ERROR);
					rm.setMsg("发送失败");
				}
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	
	/**
	 * 验证码验证
	 * @param baseParameter
	 * @param phone
	 * @param verifyCode
	 * @return
	 */
	@RequestMapping(value = "/verifyCode")
	@ResponseBody
	public ResultMsgBean verifyCode(BaseParameter baseParameter, String phone,String verifyCode) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (StringUtils.isBlank(phone)||StringUtils.isBlank(verifyCode)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("电话号或验证码不能为空！");
			} else {
				boolean isSuccess=verifyLogService.verify(phone, verifyCode);
				if(isSuccess){
					rm.setCode(ReturnCodeConstant.OK);
					rm.setMsg("验证成功");
				}else{
					rm.setCode(ReturnCodeConstant.ERROR);
					rm.setMsg("验证失败");
				}
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
