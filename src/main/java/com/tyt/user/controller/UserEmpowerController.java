package com.tyt.user.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytUserempowerSyncgoods;
import com.tyt.user.service.CsMaintainedCustomService;
import com.tyt.user.service.TytUserempowerSyncgoodsService;
import com.tyt.util.ReturnCodeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * @ClassName UserEmpowerController
 * @Description 
 * <AUTHOR> Lion
 * @Date 2022/10/18 11:39
 * @Verdion 1.0
 **/
@Controller
@RequestMapping("/plat/user/syncgoods")
public class UserEmpowerController extends BaseController {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytUserempowerSyncgoodsService")
    private TytUserempowerSyncgoodsService tytUserempowerSyncgoodsService;

    @Resource(name = "csMaintainedCustomService")
    private CsMaintainedCustomService csMaintainedCustomService;
    
    /***
      * <AUTHOR> Lion
      * @Description 查询货源同步用户接口
      * @Param [userId]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/10/18 13:32
      */
    @RequestMapping(value = {"/getEmpowerUser", "/getEmpowerUser.action"})
    @ResponseBody
    public ResultMsgBean getEmpowerUser(@RequestParam(value = "userId", required = true) Long userId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            TytUserempowerSyncgoods userempowerSyncgoods = tytUserempowerSyncgoodsService.getEmpowerUser(userId);
            resultMsgBean.setData(userempowerSyncgoods);
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
        }
        return resultMsgBean;
    }


    /***
      * <AUTHOR> Lion
      * @Description 弹窗权益授权接口
      * @Param [userId]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/10/18 13:32
      */
    @RequestMapping(value = {"/empower", "/empower.action"})
    @ResponseBody
    public ResultMsgBean updateEmpowerUser(@RequestParam(value = "userId", required = true) Long userId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "授权成功");
        try {
            resultMsgBean = tytUserempowerSyncgoodsService.updateEmpowerUser(userId, resultMsgBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
        }
        return resultMsgBean;
    }


    @RequestMapping({"/getEmpowerStatus","/getEmpowerStatus.action"})
    @ResponseBody
    public ResultMsgBean getEmpowerStatus(@RequestParam(value = "userId", required = true) Long userId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            HashMap map = csMaintainedCustomService.getEmpowerStatus(userId);
            resultMsgBean.setData(map);
        } catch (Exception e) {
            logger.error("个人货主用户授权状态查询", e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
        }
        return resultMsgBean;
    }



    @RequestMapping({"/saveEmpowerStatus","/saveEmpowerStatus.action"})
    @ResponseBody
    public ResultMsgBean saveEmpowerStatus(@RequestParam(value = "userId", required = true) Long userId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "授权成功");
        try {
            resultMsgBean = csMaintainedCustomService.saveEmpowerStatus(userId, resultMsgBean);
        } catch (Exception e) {
            logger.error("个人货主用户授权", e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
        }
        return resultMsgBean;
    }


}
