package com.tyt.user.controller;

import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tyt.base.controller.BaseController;
import com.tyt.user.service.TytForceUpgradeUserService;
import com.tyt.user.service.impl.VersionBusinessService;
import com.tyt.util.ReturnCodeConstant;
/**
 * 手机版本检测接口
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("plat/version")
public class UpgradeController extends BaseController {

	@Resource(name="versionBusiness")
	VersionBusinessService versionBusiness;

	@Resource(name="tytForceUpgradeUserService")
	TytForceUpgradeUserService tytForceUpgradeUserService;

	    @RequestMapping(value = "/check")
	    public void check(HttpServletRequest request,HttpServletResponse response) {

	    	try{
	    		/*参数解析*/
				Map<String, String> params=parseRequestParams(request);

				String condition="版本检测接口：参数"+params.toString();
				/*判断版本号*/
				Map<String,Object> versionResult=versionBusiness.checkVersion(false, params);
				if(versionResult!=null){
					logger.info(condition+"软件版本低");
					backResponse(request, response, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", versionResult,0);
					return;
				}
		    	/*返回版本信息*/
	      	    backResponse(request, response, ReturnCodeConstant.OK, "您当前版本是最新版本，不用升级的哦！",null,0);
				logger.info(condition+"版本检测成功,不用升级");
	    	}catch(Exception e){
	    		e.printStackTrace();
				backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null,0);
	    	}

	 }

}
