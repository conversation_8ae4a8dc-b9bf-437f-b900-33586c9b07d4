package com.tyt.user.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.infofee.bean.CreditUserInfo;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.TytUserStaff;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.querybean.UserStaffBean;
import com.tyt.user.service.TytUserStaffService;
import com.tyt.util.ReturnCodeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/plat/user/staff")
public class UserStaffController extends BaseController {

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "tytUserStaffService")
	private TytUserStaffService tytUserStaffService;

	@RequestMapping(value = "/queryList")
	@ResponseBody
	public ResultMsgBean getUserCreditInfoForCar(@RequestParam(value = "userId", required = true) Long userId) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
		try {
			List<TytUserStaff> staffList = tytUserStaffService.getTytUserStaffByUserId(userId);
			resultMsgBean.setData(staffList);
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("失败");
		}
		return resultMsgBean;
	}

	@RequestMapping(value = "save", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean save(TytUserStaff tytUserStaff){
		ResultMsgBean result = tytUserStaffService.addStaff(tytUserStaff);
		return result;
	}

	@RequestMapping(value = "update", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsgBean update(UserStaffBean tytUserStaff){
		ResultMsgBean result = tytUserStaffService.updateStaff(tytUserStaff);
		return result;
	}

	@RequestMapping(value = "remove")
	@ResponseBody
	public ResultMsgBean remove(@RequestParam(value = "id", required = true) Long id){
		ResultMsgBean result = new ResultMsgBean();
		tytUserStaffService.delete(id);
		return result;
	}

}
