package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.TytUserIdentityAuthLog;

public interface TytUserIdentityAuthLogService extends BaseService<TytUserIdentityAuthLog,Long>  {
    /**
     * 添加数据（暂时未使用）
     * @param userIdentityAuth
     * @param nowDate
     * @return
     * @throws Exception
     */
	public TytUserIdentityAuthLog saveIdentityAuthLog(TytUserIdentityAuth userIdentityAuth) throws Exception;
	
}
