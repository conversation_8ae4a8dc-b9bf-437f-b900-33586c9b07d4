package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytCarDriverPhoneInfo;

public interface CarDriverPhoneInfoService extends BaseService<TytCarDriverPhoneInfo, Long>{

	/**
	 * 根据车辆ID获取联系司机电话
	 * @param id
	 * @return
	 */
	TytCarDriverPhoneInfo getByCarId(Long id);

	/**
	 * 保存或修改联系司机电话
	 * @param driverPhone
	 */
	void updateDriverPhoneInfo(TytCarDriverPhoneInfo driverPhone);

}
