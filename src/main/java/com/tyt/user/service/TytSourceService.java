package com.tyt.user.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytSource;
import com.tyt.plat.enums.SourceGroupCodeEnum;
import com.tyt.user.querybean.SourceBean;
/**
 * 
 * <AUTHOR>
 *
 */
public interface TytSourceService extends BaseService<TytSource,Long> {
	/**
	 * 根据groupCode获得键-值对
	 * @param groupCode
	 * @return
	 */
	public List<SourceBean> getByGroupCode(String groupCode);
	/**
	 * 获得所有的键-值对
	 * @return
	 */
	public List<SourceBean> getAllFromDB();
	/**
	 * 获得所有 有效 配置
	 * @return
	 */
	public List<TytSource>  getAllTytSourceList();
	
	/**
	 * 获得所有子集
	 * @param parent
	 * @return
	 */
	public List<TytSource> getSubTytSourceList(String parent);
	/**
	 * 根据parent模糊查询获取子级
	 * 配件维修版本一个子级对应多个父级
	 * @param parent
	 * @return
	 */
	List<TytSource> getSubByLikeParent(String parent);
	/**
	 * 根据父级groupCode获取父级集合,包含子级
	 * @param groupCode
	 * @return
	 */
	public List<TytSource> getByGroupCode(List<String> groupCode);
	/**
	 * 根据groupCode获取结果集,不取相应的子级
	 * @param groupCode
	 * @return
	 */
	public List<TytSource> getByGroupCodeNotContainChild(List<String> groupCode);
	/**
	 * 根据每条结果的ID,为结果集查询相应的子级,这种方法适用于通过父级找子级
	 * @param list
	 * @return
	 */
	public List<TytSource> getChildById(List<TytSource> list);
	/**
	 * 根据每条结果的parent,为结果集查询相应的子级,这种方法适用于通过子级找父级
	 * @param list
	 * @return
	 */
	public List<TytSource> getChildByParent(List<TytSource> list);
	/**
	 * 根据子级groupCode获取子集 合    关系:子{父}
	 * @param parentGroupCode
	 * @return
	 */
	public List<TytSource> getSubParentList(List<String> subGroupCode);
	/**
	 * 根据ID集合批量获取,结果集不包含子级
	 * @param idArray
	 * @return
	 */
	public List<TytSource> getByIdNotContainChild(List<String> idList);
	/**
	 * 根据groupCode获得所有键-值对
	 * @param groupCode
	 * @return
	 */
	List<SourceBean> getByGroupCodeWithNoLimit(String groupCode);

	List<SourceBean> getByGroupCodeWithNoLimit(SourceGroupCodeEnum ... codeEnumArray);

}
