package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserSub;
import com.tyt.user.bean.UserCollectCountBean;

import java.util.Date;

public interface TytUserSubService extends BaseService<TytUserSub, Long> {
	/**
	 * 通过userID查询
	 *
	 * @param userId
	 * @return TytUserSub
	 */
    TytUserSub getTytUserSubByUserId(Long userId);

	TytUserSub saveTytUserByUserId(Long userID, Integer verifyFlag, String clientId) throws Exception;
	/**
	 * 修改
	 * @param userId
	 * @return
	 * @throws Exception
	 */
    TytUserSub saveChangeTytUserSub(Long userId)throws Exception;
	/**
	 * 判断用户是否可以发货
	 *
	 * @param userId
	 * @return true 可以 false不可以
	 * @throws Exception
	 */
    boolean isSendGoods(Long userId) throws Exception;

	/**
	 * 取得用户剩余发送条数
	 *
	 * @param userId
	 * @return
	 * @throws Exception
	 */
    int getUserRemainNumber(Long userId) throws Exception;

	/**
	 * 重置用户发送条数限制
	 *
	 * @param userId
	 * @return
	 * @throws Exception
	 */
    void setUserSendGoodsLimitNumber(Long userId) throws Exception;

	/**
	 * 清角标
	 *
	 * @param userId
	 */
    void delNotifyBadge(Long userId);

	/**
	 * 设置最后登录CID和版本信息
	 *
	 * @param userId
	 * @param cid
	 *            　　
	 * @param clientSign
	 *            　客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
	 * @param clientVersion
	 *            　　客户端版本号
	 * @param osVersion
	 *            　系统版本号
	 */
    void savePushCid(Long userId, String cid, Integer clientSign, String clientVersion, String osVersion);

	/**
	 * 获取用户的身份标签
	 *
	 * @param id
	 * @return
	 */
    Object[] getIdentityLables(Long userId);

	/**
	 * 修改用户身份标签
	 *
	 * @param userId
	 * @param bcarIdentityLables
	 * @param scarIdentityLables
	 */
    void updateIdentityLables(Long userId, String bcarIdentityLables, String scarIdentityLables);

	/**
	 * 获取用户的各种收藏的数量
	 *
	 * @param userId
	 * @return
	 */
    UserCollectCountBean getUserCollectCount(String userId);

	void savePushCid(Long userId, String cid, Integer clientSign, String clientVersion, String osVersion, String deviceId, String carDeviceId, String goodsDeviceId);

	void updateUserGroup(Long userId, int userGroup);

//	void rightCheck(Long userId, Integer type, ResultMsgBean result);

	/**
	 * 5930 新身份权益认证
	 * @param userId
	 * @param type
	 * @param result
	 */
    void rightCheckV5930(Long userId, Integer type, ResultMsgBean result);

	int getCountByClientId(String clientId,Long userId);

	void updateBindStatus(String clientId, Long userID, int bindStatus);

	boolean hasBinded(String clientId,Long userId);
	TytUserSub updateCache(Long userId);

//	/**
//	 * 测试无问题后删除
//	 */
//	boolean canContinue(TytUserSub tytUserSubByUserId);

    void updateLevelBeginTime(Long userId, Date date);


	Integer getCountNum(int port, String clientId);

//	/**
//	 * 测试无问题后删除
//	 * 根据用户ID返回是否有拨打电话权限；
//	 * @param userId
//	 * @return  boolean 可拨打true
//	 */
//    boolean isCallPhone(Long userId);

	/**
	 * 更新货源发布数量
	 * @param userId  用户id
	 * @param publishNum
	 */
	void updatePublishNum(Long userId,int publishNum);

}
