package com.tyt.user.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytInternalEmployee;
import com.tyt.model.User;
import com.tyt.model.CsMaintainedCustom;

import java.util.HashMap;

public interface CsMaintainedCustomService {


    ResultMsgBean saveEmpowerStatus(Long userId, ResultMsgBean resultMsgBean);

    HashMap getEmpowerStatus(Long userId);

    CsMaintainedCustom getCsMaintainedCustomByUserId(Long userId);

    /**
     * 保存用户维护数据
     * @param user
     * @param internalEmployee
     * @return
     */
    CsMaintainedCustom saveCustom(User user, TytInternalEmployee internalEmployee);

    CsMaintainedCustom updateCustom(TytInternalEmployee internalEmployee, CsMaintainedCustom csMaintainedCustom);

}
