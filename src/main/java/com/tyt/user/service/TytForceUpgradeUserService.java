package com.tyt.user.service;

import java.util.List;
import java.util.Map;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytForceUpgradeUser;

public interface TytForceUpgradeUserService extends BaseService<TytForceUpgradeUser, Long> {

	/**
	 * 从数据库获取列表
	 * @param upgradeType 强制升级标识:1强制升级;2不升级
	 * @return
	 * @throws Exception
	 */
//	public List<TytForceUpgradeUser> getForceUpgradeUserListFromDB(String upgradeType)throws Exception;
//	/**
//	 * 键值为手机号_type
//	 * @param upgradeFlag
//	 * @return
//	 * @throws Exception
//	 */
//	public Map<String,TytForceUpgradeUser> getForceUpgradePhoneMap()throws Exception;
//	/**
//	 * 键值为用户userId_type
//	 * @param upgradeFlag
//	 * @return
//	 * @throws Exception
//	 */
//	public Map<String,TytForceUpgradeUser> getForceUpgradeUserMap()throws Exception;
//	/**
//	 * 键值为用户clientId_type
//	 * @param upgradeFlag
//	 * @return
//	 * @throws Exception
//	 */
//	public Map<String,TytForceUpgradeUser> getForceUpgradeClientIdMap()throws Exception;
//
//	/**
//	 * 通过用户注册账号获取该用户的强制升级信息
//	 * @param registerPhone 用户账号
//	 * @return
//	 * @throws Exception
//	 */
//	public TytForceUpgradeUser getForceUpgradeByPhone(String phone_type)throws Exception;
//	/**
//	 * 通过用户注册ID获取该用户的强制升级信息
//	 * @param userId
//	 * @param upgradeFlag
//	 * @return
//	 * @throws Exception
//	 */
//	public TytForceUpgradeUser getForceUpgradeByUserId(String userId_type)throws Exception;
//	/**
//	 * 通过用户clientId获取该用户的强制升级信息
//	 * @param clientId_type
//	 * @return
//	 * @throws Exception
//	 */
//	public TytForceUpgradeUser getForceUpgradeByClientId(String clientId_type)throws Exception;
	/**
	 * 获取升级的相关信息
	 * @param queryFlag:1通过用户ID;2通过手机号;3clientId
	 * @param registerPhone手机号
	 * @param userId用户ID
	 * @param clientId终端唯一标识
	 * @param upgradeFlag
	 * @param userVersion当前版本号
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> getForceUpgradeVersionInfo(Integer queryFlag,String registerPhone,Long userId,String clientId,String channelCode,String clientSign,String userVersion);
	/**
	 * 更改强制升级用户状态
	 * @param id
	 * @param object
	 * @param taskId
	 */
	public void updateInfo(Long id, Object object, Object taskId);

}
