package com.tyt.user.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.PublicResource;
import com.tyt.user.bean.IpInfoBean;

public interface PublicResourceService extends BaseService<PublicResource, Long> {
	/**
	 * 获取公共资源
	 * @return
	 * @throws Exception
	 */
	public List<PublicResource> getPublicResourceByIp(String ip) throws Exception;
	/**
	 * APP中jsp、H5页面公共资源
	 * @return
	 * @throws Exception
	 */
	public List<PublicResource> getPagePublicResource() throws Exception;
	/**
	 * 获取所有公共资源
	 *
	 * @return
	 * @throws Exception
	 */
	public List<PublicResource> getAll() throws Exception;
	/**
	 * 获取显示的公共资源列表
	 *
	 * @return
	 * @throws Exception
	 */
	public List<PublicResource> getShowList(String hideTurnPictureName,String hideTurnPictureTitleLinkName) throws Exception;


	/**
	 * 根据key获取公共资源
	 *
	 * @param string
	 * @return
	 */
	public PublicResource getByKey(String key);
	/**
	 * 根据ip值获取tyt_geo_dict表对应的详细信息
	 * @param ip
	 * @return
	 * @throws Exception
	 */
	public IpInfoBean getInfo(String ip)throws Exception;

    PublicResource getGlobalByParam(String paranName) throws Exception;

	public List<PublicResource> getGlobalByParamList(List<String> paramNameList) throws Exception;
}
