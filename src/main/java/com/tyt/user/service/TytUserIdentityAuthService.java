package com.tyt.user.service;

import java.util.Date;

import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.entity.base.TytInvoiceTaxRateRule;
import com.tyt.plat.entity.base.TytSelfCompanySign;
import com.tyt.plat.vo.user.FaceUserIdentityAuthReq;
import com.tyt.plat.vo.user.UserFaceVerifyVo;
import com.tyt.user.bean.UserBaseInfo;
import com.tyt.user.bean.UserIdentityAuthBean;
import org.springframework.web.multipart.MultipartFile;

public interface TytUserIdentityAuthService extends BaseService<TytUserIdentityAuth,Long>  {

	TytUserIdentityAuth createTytUserIdentityAuth(Long userId, UserIdentityAuthBean userIdentityAuthBean, MultipartFile mainUrlPic,
												  MultipartFile backUrlPic, MultipartFile qualificationsUrlPic,
												  MultipartFile licenseUrlPic, MultipartFile iPhotoPic, String clientSign) throws Exception;

	TytUserIdentityAuth getByUserId(String userId);

	/**
	 * 保存或者修改身份认证信息,tyt_user_identity_auth表与用户是一对一的关系(把第一版实名认证迁移到第二版实名认证中去)
	 * @param userId 用户ID
	 * @param mainUrl 身份证正面照路径
	 * @return
	 * @throws Exception
	 */
	TytUserIdentityAuth saveIdentityAuthMove(Long userId,String mobile,Integer identityType,Integer userClass,String trueName,String mainUrl,Date nowDate)throws Exception;

	/**
	 * 保存身份认证信息
	 * @param tytUserIdentityAuth tytUserIdentityAuth
	 * @param faceVerifyResult 人脸识别认证状态
	 * @return ResultMsgBean
	 */
    ResultMsgBean save(TytUserIdentityAuth tytUserIdentityAuth, String clientSign, boolean faceVerifyResult);

	/**
	 * 人脸识别实名认证保存.
	 * @param userIdentityAuthReq userIdentityAuthReq
	 * @return Integer
	 */
	UserFaceVerifyVo saveFaceVerifyV5(FaceUserIdentityAuthReq userIdentityAuthReq) throws Exception;

	/**
	 * 人脸识别实名认证保存.
	 * @param userIdentityAuthReq userIdentityAuthReq
	 * @return Integer
	 */
	UserFaceVerifyVo saveFaceVerifyV3(FaceUserIdentityAuthReq userIdentityAuthReq) throws Exception;

	/**
	 * 用户自动审核成功mq
	 * @param userId
	 */
	void sendUserAutoVerifyMq(Long userId);

	ResultMsgBean saveEnterprise(Long userId,String enterpriseAuthLicenseUrl,String enterpriseAuthPath, Integer clientSign);

	/**
	 * 获取rate
	 * @param enterpriseId enterpriseId
	 * @return TytInvoiceTaxRateRule
	 */
	TytInvoiceTaxRateRule getUserRateRule(Long enterpriseId);

	/**
	 * 初始化新加字段.
	 * @param invoiceEnterprise invoiceEnterprise
	 */
	TytSelfCompanySign initCustomerManager(TytInvoiceEnterprise invoiceEnterprise);

	/**
	 * initBaseEnterprise
	 * @param userInfo
	 * @param invoiceEnterprise
	 */
    void initBaseEnterprise(UserBaseInfo userInfo, TytInvoiceEnterprise invoiceEnterprise);

    /**
	 * 保存身份认证信息,适用于APP5300以后版本
	 *
	 * @param tytUserIdentityAuth
	 * @return
	 */
    ResultMsgBean saveAfter5300(TytUserIdentityAuth tytUserIdentityAuth);

	/**
	 * 通过userId 获得用户身份信息
	 * @param userId
	 * @return TytUserIdentityAuth
	 */
    TytUserIdentityAuth getTytUserIdentityAuth(Long userId);

	/**
	 * 通过 userName,idCard 获得用户身份信息
	 * @param userName 用户真实姓名
	 * @param idCard 用户身份证号
	 * @return TytUserIdentityAuth
	 */
    TytUserIdentityAuth getTytUserIdentityAuth(String userName, String idCard);

	/**
	 * 获取二要素验证次数
	 * @param userId userId
	 * @return int
	 */
	int getRealVerifyCount(Long userId);
}
