package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.InviteFriends;

public interface InviteFriendsService extends BaseService<InviteFriends, Long>{

//	public void saveInviteFriends(Long userId,String friends,Integer platId) throws Exception;
	
	public boolean getByUserIdAndFriendCell(Long userId,String friendCell) throws Exception;
	
	public void addCounts(Long userId,String friendCell) throws Exception;
}
