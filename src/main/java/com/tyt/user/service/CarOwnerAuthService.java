package com.tyt.user.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytCarOwnerAuth;
import com.tyt.user.bean.CarOwnerAuthReq;
import com.tyt.user.bean.CarOwnerAuthVO;

/**
 * 车主认证
 * <AUTHOR>
 * @since 2024/02/01 13:54
 */
public interface CarOwnerAuthService {
    CarOwnerAuthVO getCarOwnerInfo(Long userId);

    /**
     * 是否符合一键提交规则
     * @param userId
     * @param carId
     * @return 0 不符合 1 符合 实名认证判断符合 2 符合 企业认证判断符合
     */
    Integer isQuickCommit(Long userId,Long carId) throws Exception;

    /**
     * 保存车主认证信息
     * @param carOwnerAuthReq
     */
    void carOwnerAuthSave(CarOwnerAuthReq carOwnerAuthReq);

    /**
     * 一键提交
     * @param userId
     * @param carId
     */
    ResultMsgBean quickCommit(Long userId, Long carId) throws Exception;

    TytCarOwnerAuth getByCarId(Long carId);
    TytCarOwnerAuth selectByCarIdAndAuth(Long carId);

    Integer isEnterprise(String content);
}
