package com.tyt.user.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.User;

public interface CollectService extends BaseService<User, Long>{
	
	/**
	 * 查询信息id的集合
	 * @param userId用户ID
	 * @param type 1是新车咨询 2 板车求职3 板车招聘4 设备求职5设备招聘
	 * @param status 0收藏1取消收藏
	 * @param currentPage当前页数
	 * @param pageSize每页条数
	 * @return
	 */
	public List<Long> getInfoIdsByType(String userId,String type,String status,Integer currentPage,Integer pageSize);
	/**
	 * 获取我的收藏列表的总条数
	 * @param userId 用户ID
	 * @param type 1是新车咨询 2 板车求职3 板车招聘4 设备求职5设备招聘
	 * @param status 0收藏1取消收藏
	 * @return
	 */
	public int getInfoIdsCountsByType(String userId, String type, String status);

}
