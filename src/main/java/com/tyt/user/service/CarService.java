package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.Car;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.User;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.plat.entity.base.TytTransportEnterpriseLog;
import com.tyt.plat.vo.ocr.RoadTransportBackOcrRpcVo;
import com.tyt.user.bean.*;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.querybean.UserCarBean;

import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

public interface CarService extends BaseService<Car, Long> {
	/**
	 * 得到用户简化的所有查询信息
	 * 
	 * @param userId
	 * @param auth
	 * @return
	 */
	List<QueryCar> getQueryAll(Long userId, String auth);
	
	/**
	 * 得到用户简化的所有查询信息
	 * 
	 * @param userId
	 * @param auth
	 * @return
	 */
	List<QueryCar> getQueryAllOld(Long userId, String auth);

	/**
	 * 验证重复信息
	 * 
	 * @param params
	 * @return
	 * @throws Exception
	 */
	boolean get(Map <String, String> params) throws Exception;

	/**
	 *
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public Car getCarByParams(Map<String, String> params) throws Exception;

	/**
	 * 天假车辆信息并改变相应的用户信息
	 * 
	 * @param car
	 * @return
	 */
	Long addCar(Car car) throws Exception;

	/**
	 * 获得实名认证验证总结果集
	 * 
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	boolean isExitEnabled(Long userId) throws Exception;

	boolean isCarBlongToUser(Long carId, Long userId);

	/**
	 * 根据车辆id删除车辆
	 * 
	 * @param carId
	 * @throws InvocationTargetException 
	 * @throws IllegalAccessException 
	 */
	void deleteById(Long carId, String deleteReason) throws IllegalAccessException, InvocationTargetException;

	void deleteCar(Long carId, String deleteReason);

	void deleteByCarOwnerAuth(Long carId);
	/**
	 * 获取最大的排序序列号
	 * @return
	 */
	BigInteger getMaxSort(Long userId);

	QueryCar getQueryCar(Long carId);

	boolean  updateSort(String carIds);

	/**
	 * 修改是否满足开票字段的值
	 * @param carId 车辆id
	 * @param isInvoice 是否满足开票 1满足 2不满足
	 * @return
	 */
	boolean updateIsInvoice(Long carId,Integer isInvoice,Integer thirdPartyRequire,Integer xhlPartyRequire);

	boolean validateNumEqual(Map <String, String> params);

	boolean updateOnOff(Long id, String findGoodOnOff);
	
	Car newGetbyId(Long id);
	
	BigInteger getCountByUserId(Long userId);

	QueryCar getQueryCarNew(Long carId) throws Exception;

	List<QueryCar> getQueryAllNew(Long userId, String auth);

	boolean updateOnOffNew(Long id, String findGoodOnOff);

	void updatePreferNew(Long carId);

	int isCarAuth(Long userId, String auth);

    /**
     * @Description  根据用户id和认证状态查询用户所有的车辆
     * <AUTHOR>
     * @Date  2019/3/13 10:11
     * @Param [userId]
     * @return java.util.List<com.tyt.user.querybean.QueryCar>
     **/
	List<QueryCar> getAllCarByUserId(Long userId);
    
	/**
	 * @Description  根据车头车牌号查询认证车辆的信息
	 * <AUTHOR>
	 * @Date  2019/3/14 11:27
	 * @Param [userId, carHeadCity, carHeadNo]
	 * @return com.tyt.model.CarInsuranceInquiry
	 **/
	List<QueryCar> getAuthCarInfoByHeadNo(Long userId, String carHeadCity, String carHeadNo);

	/**
	 * 根据车辆类型 获取车辆类型列表
	 *
	 * @return
	 */
	List<CarType> getCarTypeListByClassify(Integer carClassify);

	/**
	 * @Description  我的车辆列表
	 * <AUTHOR>
	 * @Date  2019/7/3 11:56
	 * @Param [userId]
	 * @return java.util.List<com.tyt.model.Car>
	 **/
	List<Car> getMyCarList(Long userId);

	List<Car> getListByUserId(Long userId);

	/**
	 * @Description  完善车辆信息
	 * <AUTHOR>
	 * @Date  2019/7/3 15:57
	 * @Param [updateCarBean]
	 * @return void
	 **/
	int updateCarInfo(UpdateCarBean updateCarBean);

    /**
     * @description 完善车辆信息 -- 调度中心版本
     * <AUTHOR>
     * @date 2020/5/11 11:18
     * @param updateCarDiaoduBean
     * @return int
     */
	int updateCarInfoDiaodu(UpdateCarDiaoduBean updateCarDiaoduBean);

	/**
	 * 删除绑定司机信息方法
	 * @param updateCarDiaoduBean
	 * @return
	 */
	int deleteDriverInfo(UpdateCarDiaoduBean updateCarDiaoduBean);

    Long saveCar(CarSaveBean carSaveBean, String headDrivingUrl, String tailDrivingUrl,
				 String tailDrivingOtherSideUrl, String roadCardPositiveUrl,
				 String roadCardOtherSideUrl, String roadLicenseNoUrl,
				 String headDrivingSubpageUrl,
				 String headTransportHomepageUrl,
				 String headTransportSubpageUrl,
				 String tailDrivingSubpageUrl,
				 String tailTransportHomepageUrl,
				 String tailTransportSubpageUrl,
				 String tailPhotoUrl,
				 Long tailId) throws Exception;

    void saveTailDetail(Long carId, Long userId, String tailCity, String tailNo, Integer isPureFlat,CarSaveBean carSaveBean);

    Long saveCar(CarSaveBean carSaveBean) throws Exception;

	/**
	 * 获取我的车辆列表
	 *
	 * @param userId
	 * @return
	 */
	MyFullCarListBean getMergeMyCarList(Long userId,String isDispatch,Integer clientVersion);

	/**
	 * 获取字典总的挂车的车辆类型和车辆
	 * @return
	 */
	Map<String,Object> getTrailerStyleType();

	/**
	 * 获取调度车辆详情
	 * @param id
	 * @return
	 */
	Car updateDispatchCar(Long id);

	/**
	 * 修改车头信息
	 * @param dispatchCarBean
	 */
	void updateCarHead(DispatchCarBean dispatchCarBean,Car car);

	/**
	 * 车辆司机绑定
	 * @param carId
	 * @param driverId
	 */
	void updateCarDriverBind(Long carId,Long driverId,Long userId) throws Exception;

	/**
	 * 更新用户名下的所有车辆为调度车
	 *
	 * @param userId 用户ID
	 * @param dispatchStatus 是否为调度车，0-不是，1-是
	 */
	void updateDispatchStatus(long userId, int dispatchStatus);

	/**
	 * 修改挂车车头所有人电话
	 * @param tailPhone
	 * @param headPhone
	 * @param id
	 */
	Integer updateCarPhone(String tailPhone,String headPhone,Long id);

	/**
	 * 判断用户车辆中是否含有专车
	 * @param userId
	 * @return
	 */
	boolean selectCarIsSpecialCarCount(Long userId	);

    int getCarNum(Long userId);

	/**
	 * 获取已认证的挂车列表
	 * @param userId
	 * @param auth
	 * @return
	 */
	List<QueryCar> getAuthTailList(Long userId, String auth);

	/**
	 * 判断该车辆是否是待完善信息车辆
	 * @param car 车辆信息
	 * @return 1:是待完善信息车辆；2:否
	 */
	boolean carIsNeedImprovenData(Car car);

	/**
	 * OCR 识别行驶证正副页信息
	 * @param url 行驶证正副页图片url
	 * @return com.tyt.model.ResultMsgBean
	 */
	ResultMsgBean ocrDrivingLicenseInfo(String url);

	/**
	 * 根据车辆id获取车辆车头信息
	 * @param carId
	 * @return
	 */
	UserCarInfoVO getUserCarInfoVO(Long carId);

	List<Long> selectIds(Long userId, String auth);

	Map<String,List<UserCarBean>> getCarList(Long userId, Long id,String weight);

	Map<String, List<TytInvoiceDriver>> getCarUserList(Long id, Long userId,Integer type, TransportMain main);

	boolean checkSigningDriver(TransportMain main, Long userId, TytInvoiceDriver driver);

	Boolean invoiceType(String code, String invoiceCode);


	Boolean getCarinvoiceType(Long carId, Long invoiceId);

	/**
	 *OCR识别行驶证主页\副页 当识别出不符合项时给出提示
	 * @param url 需识别的图片地址
	 * @return
	 */
	ResultMsgBean getCarOcrDrivingLicenseInfo(String url);

	/**
	 * 刷新用户车辆是否可以开票信息
	 * @param userId
	 */
	void refreshIsInvoice(Long userId);

    Integer getByUserIdAuth(Long userId);

	ResultMsgBean getCheckCar(Car car, User user);

	/**
	 * 道运证OCR结果更新车辆信息
	 *
	 * @param carId 车辆ID
	 * @param ocrRpcVo OCR结果
	 * @param carType 1-车头；2-车挂；
	 */
	void updateCarForTransportOcr(Long carId, RoadTransportBackOcrRpcVo ocrRpcVo, Integer carType);

	List<UserCarBean> getUserCar(Long userId);

	Map<String, List<UserCarBean>> getCarInvoiceList(Long userId, Long id, TransportMain main, TytTransportEnterpriseLog log) throws Exception;

	Boolean getSubjectCarinvoiceType(Long code, Long invoiceCode, Long userId);
}
