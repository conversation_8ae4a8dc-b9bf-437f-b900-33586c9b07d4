package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytCallPhoneLimit;

/**
 * 用户一级身份拨打电话限制服务层
 * 
 * <AUTHOR>
 * @date 2017-3-9下午2:45:18
 * @description
 */
public interface TytCallPhoneLimitService extends BaseService<TytCallPhoneLimit, Long> {

	/**
	 * 根据一级身份类型获取
	 * 
	 * @param userClass
	 * @param identityType
	 * @return
	 */
	TytCallPhoneLimit getByUserIdentity(Integer userClass, Integer identityType);
}
