package com.tyt.user.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytUserempowerSyncgoods;

import java.util.List;

public interface TytUserempowerSyncgoodsService {

    /**
     *
     * @param userId
     * @return
     */
    TytUserempowerSyncgoods getEmpowerUser(Long userId);

    /**
     *
     * @param userId
     * @param resultMsgBean
     * @return
     */
    ResultMsgBean updateEmpowerUser(Long userId, ResultMsgBean resultMsgBean);
}
