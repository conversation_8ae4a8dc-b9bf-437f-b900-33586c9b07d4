package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytCarCurrentLocation;
import com.tyt.plat.entity.base.CurrentLocationVO;

import java.util.List;

/**
 * 车辆实时位置服务层
 * 
 * <AUTHOR>
 * @date 2018年1月8日下午3:38:32
 * @description
 */
public interface CarCurrentLocationService extends BaseService<TytCarCurrentLocation, Long> {

	/**
	 * 根据车头牌照获取车辆位置信息
	 * 
	 * @param licensePlate
	 * @return
	 */
	TytCarCurrentLocation getByCarHeadLicence(String licensePlate);

    TytCarCurrentLocation getByCarHead(String city, String no);

	/**
	 * 获取当前车头所在的位置
	 *
	 * @param carHeadNo 完整的车头牌照号，eg：京CF8392
	 * @throws Exception
	 */
	public Object getCurrentLocation(String carHeadNo) throws Exception;

	List<CurrentLocationVO> getCurrentLocations(String heads)throws Exception;

	List<CurrentLocationVO> getZJCurrentLocations(String heads);
}
