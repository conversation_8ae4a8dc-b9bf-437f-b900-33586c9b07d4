package com.tyt.user.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytCellPhoneChangeAudit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户变更手机号相关逻辑
 * @date 2023/09/05 10:46
 */
public interface UserCellPhoneChangeService {

    /**
     * @description 根据用户id及状态获取手机号变更审核信息
     * <AUTHOR>
     * @date 2023/9/5 11:00
     * @version 1.0
     * @param userId
     * @param auditStatus 审核状态 1：待审核 2：审核通过 3：驳回'
     * @return com.tyt.plat.entity.base.TytCellPhoneChangeAudit
     */
    TytCellPhoneChangeAudit getByUserIdAndAuditStatus(Long userId,Integer auditStatus);

    /**
     * @description 获取是否可以变更手机号 如果可以变更返回当前手机号
     * <AUTHOR>
     * @date 2023/9/5 15:18
     * @version 1.0
     * @param userId
     * @return com.tyt.model.ResultMsgBean
     */
    ResultMsgBean getIsChangeInfo(Long userId);

    /**
     * @description 用户变更手机号-自主变更
     * <AUTHOR>
     * @date 2023/9/5 20:40
     * @version 1.0
     * @param userId 用户iD
     * @param oldPhone 原手机号
     * @param newPhone 变更后手机号
     * @return void
     */
    void updateCellPhone(Long userId,String oldPhone,String newPhone);

    /**
     * @description 用户变更手机号-申请变更
     * <AUTHOR>
     * @date 2023/9/5 20:42
     * @version 1.0
     * @param audit
     * @return void
     */
    void saveChangeAuditInfo(TytCellPhoneChangeAudit audit);
}
