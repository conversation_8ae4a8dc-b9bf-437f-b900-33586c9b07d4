package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.UserCancelBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytNoticePopupTempl;
import com.tyt.model.TytSource;
import com.tyt.model.User;
import com.tyt.smallTools.controller.bean.UserDetailByCellphone;
import com.tyt.user.bean.RightsInfoBean;
import com.tyt.user.bean.UserLabel;
import com.tyt.user.querybean.UserBean;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * User: Administrator Date: 13-11-10 Time: 下午4:24
 */
public interface UserService extends BaseService<User, Long> {

	/**
	 * 异常登录短信模板key
	 */
	String EXCEPTION_LOGIN_MSG_TEMPLATE_KEY = "exception_login_msg";

	void updateServeDays();

	void clearQqModTimes();

	List<String> getSales(String condition);

	/**
	 * 根据手机号查询用户
	 *
	 * @param cellphone
	 * @return
	 */
    User getUserByCellphone(String cellphone);

	/**
	 * 根据手机号和身份查询用户
	 *
	 * @param cellPhone
	 * @param userSign
	 * @return
	 */
    User getUserByCellAndSign(String cellPhone, Integer userSign);

	/**
	 * 修改密码
	 *
	 * @param id
	 * @param password
	 * @return
	 * @throws Exception
	 */
    boolean updatePassword(Long id, String password) throws Exception;

	/**
	 * 从缓存数据库取值
	 *
	 * @param userId
	 * @return
	 * @throws Exception
	 */
    User getByUserId(Long userId) throws Exception;

	/**
	 * 数据库取值
	 * @param userId
	 * @return
	 */
	User getByUserIdNonCache(Long userId);
	/**
	 * 更改用户最后登录的相关信息
	 *
	 * @param id
	 * @param clientSign
	 * @param clientVersion
	 * @param osVersion
	 * @return
	 * @throws Exception
	 */
    boolean updateLastInfo(Long id, String ticket, Integer clientSign, String clientVersion, String osVersion) throws Exception;

	/**
	 * 添加头像
	 *
	 * @param headurl
	 * @throws Exception
	 */
    void saveHead(String headurl, Long userId) throws Exception;

	/**
	 * 注册完成以后再初始化qq，昵称信息
	 */
    User addUser(User user) throws Exception;

	/**
	 * 修改用户实名认证标识
	 *
	 * @param userId
	 * @param verifyFlag
	 * @throws Exception
	 */
    void updateVeryCode(Long userId, Integer verifyFlag) throws Exception;
	/**
	 * 修改用户的身份照片认证状态
	 * @param userId
	 * @param photoVerifyFlag
	 * @param identityType
	 * @param userClass
	 * @throws Exception
	 */
    void updatePhotoVerifyFlag(Long userId, Date mtime, Integer photoVerifyFlag, Integer userClass, Integer identityType) throws Exception;
	/**
	 * 修改昵称
	 *
	 * @param userName
	 * @param userId
	 * @param clientSign
	 */
    void saveUserName(String userName, Long userId, Integer clientSign) throws Exception;

	void updateQQ(Long id) throws Exception;

	void updateInitUserName(User user) throws Exception;

	void updateMbUserId(String MbUserId, Long uid) throws Exception;

	/**
	 * 获取标签集合 父子 子父(配件维修师APP查询使用)
	 *
	 * @return
	 */
    Map<String, List<TytSource>> getIdentityLables();

	/**
	 * 获取我的头像
	 *
	 * @param userId
	 * @return
	 */
    String getHeadURL(String userId);

	/**
	 * 为了解决用户表重复数据问题，加了中间表，该表主键为手机号，重复添加抛出异常
	 *
	 * @param cellPhone
	 * @throws Exception
	 */
    void addCellPhoneToTemp(String cellPhone) throws Exception;

	/**
	 * 更新手机号
	 *
	 * @param changeCellPhone
	 * @param cellPhone
	 * @throws Exception
	 */
	public void updateCellPhone(String changeCellPhone, String cellPhone) throws Exception;

	/**
	 * 获取我的收藏列表
	 *
	 * @param userId用户ID
	 * @param type1是新车咨询
	 *            2 板车求职3 板车招聘4 设备求职5设备招聘
	 * @param currentPage当前页数
	 * @param status
	 *            0收藏1取消收藏
	 * @return
	 */
    Map<String, Object> getCollectList(Long userId, String type, Integer currentPage, String status);

	/**
	 * 添加用户点击使用模块记录
	 *
	 * @param userId
	 *            用户ID，非登陆状态传-1
	 * @param name
	 *            功能模块名称
	 * @param clientId
	 *            手机设备号（唯一标示）
	 * @param osVersion
	 *            操作系统版本号
	 * @param clientSign
	 *            终端标识(1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB)
	 * @param clientVersion
	 *            当前版本号
	 */
    void addModuleUse(String userId, String name, String clientId, String osVersion, String clientSign, String type, String clientVersion);

	/**
	 * 查询用户是否有需要提示货物成交信息
	 *
	 * @param userId
	 * @return
	 */
    boolean isNeedNotifyGoods(Long userId) throws Exception;

	/**
	 * 增加用户的账户信息
	 *
	 * @param id
	 * @param accountType
	 */
    void addUserAccount(Long userId, int accountType);

	/**
	 * 获取用户照片认证状态
	 *
	 * @param userId
	 * @return
	 * @throws Exception
	 */
    Integer getPhotoVerifyFlag(Long userId) throws Exception;

	/**
	 * 获取用户照片认证状态
	 * @param user
	 * @return
	 */
	Integer getPhotoVerifyFlag(User user);

	/**
	 * 保存用户的一级身份
	 *
	 * @param userId
	 *            用户id
	 * @param sourceUserClassValue
	 *            用户分类1、发货方2、车辆方 见 source user_class
	 * @param userIdentityTypeValue
	 *            用户身份见source表 user_identity_type
	 * @return
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 */
    void saveOneLevelIdentity(String userId, Integer sourceUserClassValue, Integer userIdentityTypeValue) throws IllegalAccessException, InvocationTargetException;

	/**
	 * 本接口用于APP5300以上版本
	 *
	 * @param userId
	 * @param sourceUserClassValue
	 * @param userIdentityTypeValue
	 * @throws IllegalAccessException
	 * @throws InvocationTargetException
	 */
    void saveOneLevelIdentityNew(String userId, Integer sourceUserClassValue, Integer userIdentityTypeValue) throws IllegalAccessException, InvocationTargetException;

	/**
	 * 检测用户是否可以正常发货
	 * @param userId
	 * @param clientVersion
	 * @param clientSign
	 * @return
	 * @throws Exception
	 */
    ResultMsgBean saveGoodsCheck(Long userId, String clientVersion, Integer clientSign)throws Exception;

	BigInteger getVIPCount();

	/**
	 * 改变用户的服务期限，选择是添加还是修改记录
	 * @param endTime
	 * @param userId
	 * @param cellPhone
	 * @param serverDays
	 */
    void updateUserServicePeriod(Date oldEndTime, String userId, String cellPhone, Integer serverDays);

	/**
	 * 给用户加赠送天数
	 * @param user
	 * @param extralDays
	 * @param note
	 */
    void updateExtralDays(User user, Integer extralDays, String note);

	void queryByCellphone(String cellPhone, ResultMsgBean rm);

	User getUserByCell(String cellPhone);

	RightsInfoBean getRightsInfo(Long userId) throws IllegalAccessException, InvocationTargetException;

	/**
	 * 发送异常登录的短信提醒
	 * @param userId 用户ID
	 */
    void sendExceptionLoginSms(Long userId);

	/**
	 * 通过userId获取注册手机号
	 *
	 * @param userId
	 * @return
	 */
	String getCellPhoneById(Long userId) throws Exception;


    /**
     * 检测用户发布货物身份验证
     * @param userId
     * @return
     * @throws Exception
     */
    ResultMsgBean checkUserPhotoVerifyFlag(Long userId, String clientSign)throws Exception;

	/**
	 * 检测用户发布货物身份验证
	 * @param user
	 * @return
	 * @throws Exception
	 */
	ResultMsgBean checkUserPhotoVerifyFlag(User user, String clientSign)throws Exception;

    /**
     * 检测用户是否有发货权益
     * @param userId
     * @return
     * @throws Exception
     */
    ResultMsgBean checkUserPublishTransportPermission(Long userId,String price) throws Exception;

	/**
	 * 查询用户的信用信息
	 * @param userId
	 * @param queryPhone
	 * @param queryUser
	 * @return
	 */
	UserDetailByCellphone getUserCredit(Long userId, String queryPhone, User queryUser);

	/**
	 * 查询用户列表
	 * @param userIds
	 * @return
	 */
    List<User> getUserListByIds(String userIds);

    void openacctInactiveApply(Long userId);

	/**
	 * 接收并更新用户成为调度车的邀请同意状态
	 *
	 * @param user 用户完整的对象
	 * @param dispatchStatus 是否为调度车，0-不是，1-是
	 */
	void updateDispatchStatus(User user, int dispatchStatus);

	/**
	 * 是否允许版本登录
	 * @param params
	 * @return
	 */
	boolean versionLoginAllow(Map<String, String> params);

	/**
	 * 登录时校验版本强升
	 * @param request
	 * @param params
	 * @return
	 */
	TytNoticePopupTempl checkVersionLogin(HttpServletRequest request, Map<String, String> params);

	ResultMsgBean newLogin(HttpServletRequest request,Map<String, String> params) throws Exception;

	ResultMsgBean newSmsLogin(HttpServletRequest request,Map<String,String> paras) throws Exception;

	Long createNewUser(HttpServletRequest request, Map<String, String> params, User oldUser, User user) throws Exception;

	ResultMsgBean newSimulatedLogin(HttpServletRequest request,Map<String, String> params) throws Exception;

	/**
	 * 公共登陆方法
	 *
	 * @param request
	 * @param user
	 * @param params
	 * @param upgradeStrategyMap
	 * @param clientId
	 * @param isSimulatedLogin
	 * @return
	 * @throws Exception
	 */
	UserBean newCommonLogin(HttpServletRequest request, User user, Map<String, String> params,
								   Map<String, Object> upgradeStrategyMap, String clientId, String isSimulatedLogin, int isRegister) throws Exception;

	/**
	 * 校验是否需要新版车主标签弹窗
	 * @param clientSign 客户端版本
	 * @param userBean userBean
	 */
	void checkGoodsTypePopup(int clientSign, UserBean userBean);

	void sendLoginMq(HttpServletRequest request, User user, Map<String, String> loginParams, Map<String, Object> upgradeStrategyMap, Integer recordType);

	ResultMsgBean buildLoginResult(ResultMsgBean result, int code, String msg, Object data);

	/**
	 * @description 关闭代扣协议
	 * <AUTHOR>
	 * @date 2021/12/23 16:10
	 * @param userCancelBean
	 * @return com.tyt.model.ResultMsgBean
	 */
	ResultMsgBean cancelAgree(UserCancelBean userCancelBean) throws Exception;

	/**
	 * 删除tyt_cellphone表中的号码
	 *
	 * @param cellPhone
	 * @return
	 */
	public boolean deleteFromTytCellphone(String cellPhone) throws Exception;

	Integer getIsShowIdentityPop(User user);

	void  updateIdentityType(Integer identityType,Long userId) throws Exception;

	void  updateSelectionIdentity(Integer selectionIdentity,Integer initialNum,Integer initialCarNum,Long userId) throws Exception;

	/**
	 * @description 获取用户标签
	 * @param userId userId
	 * @return UserLabel
	 */
	UserLabel getUserLabel(Long userId);

    ResultMsgBean getUserIsCancel(String cellPhone) throws Exception;

	/**
	 * 查询微信小程序限制登录信息
	 * @param userId 用户ID
	 * @return
	 * @throws Exception
	 */
	ResultMsgBean getWxMiniLoginLimit(Long userId) throws Exception;

	void updateBlackStatus(Long userId, int blackStatus);

	void registerGetCallCount(Long userId);
}
