package com.tyt.user.service;

import org.springframework.web.multipart.MultipartFile;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserIdentityMain;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:32
 */
public interface UserIdentityMainService extends BaseService<TytUserIdentityMain,Long>  {

	public TytUserIdentityMain getTytUserIdentityMainForUserId(Long userId);
	
	/**
	 * 判断是否存在上传的 图片认证信息
	 * @param userId
	 * @return
	 */
	public boolean isExitForPhoto(Long userId)throws Exception;
	
	/**
	 * 判断 身份证号与姓名是否有认证过的
	 * @param userId
	 * @param identity
	 * @param trueName
	 * @return
	 */
	public boolean isExitForInfo(String identity ,String trueName) ;
	
	/**
	 * 保存图片认证信息
	 * @param mainPic
	 * @return
	 */
	public boolean saveUserIdentityMain(Long userId ,MultipartFile mainPic)throws Exception ;
	
	/**
	 * 用于老plat接口 上传认证信息
	 * @param userId
	 * @param identity
	 * @param trueName
	 * @param mainPic
	 * @return
	 * @throws Exception
	 */
	public boolean saveUserIdentityMain(Long userId,String identity ,String trueName,MultipartFile mainPic) throws Exception ;


	/**
	 * 保存信息认证信息
	 * @param mainPic
	 * @return 状态码0      查询成功，姓名和身份证号一致
				101  查询成功，身份证号不存在
				102  查询成功，姓名和身份证号不一致
				300 系统错误
				400 发送条数超了
				（103  查询失败，URL参数错误
				104  查询失败，系统正在维护中
				105  查询失败，系统错误
				106  查询失败，请联系APIX客服
				200  查询失败，余额不足，请充值
				201  系统异常，请联系APIX客服）
	 */
	public int saveUserIdentityMain(Long userId,String identity ,String trueName) throws Exception ;
	
	/**
	 * 新版本身份认证同步数据用
	 * @param userId
	 * @param mainUrl
	 * @return
	 * @throws Exception
	 */
	public boolean saveUserIdentityMain(Long userId,String trueName,String idCard,String mainUrl) ;
}
