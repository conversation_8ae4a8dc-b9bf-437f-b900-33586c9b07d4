package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytChannelLog;
import com.tyt.model.TytUserMac;
import com.tyt.model.User;

public interface TytUserMacService extends BaseService<TytUserMac, Long>{

    /**
     * 保存用户与mac地址的关系，userId与mac形成唯一索引，随登录而更新
     * @param user User
     * @param channelLog TytChannelLog
     */
    TytUserMac saveUserMac(User user, TytChannelLog channelLog);

}
