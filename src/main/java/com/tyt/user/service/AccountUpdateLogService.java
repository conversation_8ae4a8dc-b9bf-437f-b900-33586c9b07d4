package com.tyt.user.service;

import com.tyt.plat.entity.base.AccountUpdateLog;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户变更手机号记录
 * @date 2023/09/05 11:47
 */
public interface AccountUpdateLogService {

    Integer countByTimePeriod(Long userId,Integer day);

    /**
     * @description 获取某个用户最近的一条修改记录
     * <AUTHOR>
     * @date 2023/9/5 13:08
     * @version 1.0
     * @param userId
     * @return com.tyt.plat.entity.base.AccountUpdateLog
     */
    AccountUpdateLog getRecentAccountUpdateLog(Long userId);

}
