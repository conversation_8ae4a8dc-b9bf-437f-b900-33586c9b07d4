package com.tyt.user.service;

import java.lang.reflect.InvocationTargetException;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserCallPhone;

public interface UserCallPhoneService extends BaseService<TytUserCallPhone, Long> {

	void addGetPhoneLog(String goodId, String userId, String clientSign, String clientVersion, String moduleType, Integer isCanCall) throws IllegalAccessException, InvocationTargetException;

    void addGetPhone(String goodId, Long userId, String clientSign, String clientVersion, String moduleType, Integer isCanCall,String path);
}
