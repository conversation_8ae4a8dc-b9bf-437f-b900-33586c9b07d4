package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserStaff;
import com.tyt.model.TytUserSub;
import com.tyt.user.bean.UserCollectCountBean;
import com.tyt.user.querybean.UserStaffBean;

import java.util.Date;
import java.util.List;

public interface TytUserStaffService extends BaseService<TytUserStaff, Long> {
	/**
	 * 通过userID查询
	 *
	 * @param userId
	 * @return staff list
	 */
    List<TytUserStaff> getTytUserStaffByUserId(Long userId);

    ResultMsgBean addStaff(TytUserStaff tytUserStaff);

	ResultMsgBean updateStaff(UserStaffBean tytUserStaff);

	Integer getStaffCountByUserId(Long userId);
}
