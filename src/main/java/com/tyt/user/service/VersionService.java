package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.Version;
/**
 * 
 * <AUTHOR>
 *
 */
public interface VersionService extends BaseService<Version,Long> {
	/**
	 * 版本记录置为无效
	 * @param id
	 * @return
	 * @throws Exception
	 */
	public boolean setVersionDisabled(Long id) throws Exception;
	/**
	 * 获得有效版本记录
	 * @return
	 * @throws Exception
	 */
	public Version getEnabledVersion() throws Exception;
	/**
	 * 新增版本记录
	 * @param version
	 * @throws Exception
	 */
	public void addVersion(Version version) throws Exception;
}
