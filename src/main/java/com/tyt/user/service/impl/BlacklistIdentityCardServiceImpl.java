package com.tyt.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.tyt.cache.CacheService;
import com.tyt.model.User;
import com.tyt.plat.entity.base.BlacklistIdentityCard;
import com.tyt.plat.entity.base.BlacklistUser;
import com.tyt.plat.entity.base.BlacklistUserLog;
import com.tyt.plat.entity.base.BlacklistUserOrders;
import com.tyt.plat.mapper.base.*;
import com.tyt.user.service.BlacklistIdentityCardService;
import com.tyt.user.service.BlacklistUserOrdersService;
import com.tyt.user.service.BlacklistUserService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import com.tyt.util.UserTicketUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service("blacklistIdentityCardService")
@RequiredArgsConstructor
public class BlacklistIdentityCardServiceImpl implements BlacklistIdentityCardService {

	private final BlackListIdentityCardMapper blackListIdentityCardMapper;

	private final BlackListUserMapper blackListUserMapper;

	private final BlackListUserLogMapper blackListUserLogMapper;

	private final UserService userService;

	private final BlacklistUserService blacklistUserService;

	private final CacheService cacheService;

	private static final String FOREVERTIME = "2999-01-01 00:00:00";

	@Override
	public boolean checkIdCardBlackList(String idCard) {
		BlacklistIdentityCard identityCard = blackListIdentityCardMapper.getByIdCard(idCard);
		if (identityCard != null && identityCard.getStatus() == 1) {
			return true;
		}
		return false;
	}

	@Override
	public void blackListUser(Long userId, String idCard) {
		try {
			User user = userService.getByUserId(userId);
			if (user != null) {
				com.tyt.model.BlacklistUser blackListForPhone = blacklistUserService.getBlackByCellPhone(user.getCellPhone());
				com.tyt.model.BlacklistUser blackListForUserId = blacklistUserService.getBlackByUserId(userId);
				if (blackListForPhone != null) {
					updateBlackListForEver(blackListForPhone, user, idCard);
				} else if (blackListForUserId != null) {
					updateBlackListForEver(blackListForUserId, user, idCard);
				}

				if (blackListForPhone == null && blackListForUserId == null) {
					//重点监控用户新增
					BlacklistUser insertUser = buildBlackListUser(user, idCard);
					blackListUserMapper.insert(insertUser);
					//新增记录
					BlacklistUserLog blacklistUserLog = buildBlackListUserLog(insertUser);
					blackListUserLogMapper.insert(blacklistUserLog);
				}

				//用户拉黑
				userService.updateBlackStatus(userId, 1);

				//踢登
				UserTicketUtil.kickOutAllClient(userId + "");
				cacheService.del(Constant.CACHE_USER_KEY + userId);
				}
		} catch (Exception e) {
			log.error("black list user error:", e);
		}
	}

	@SneakyThrows
	private void updateBlackListForEver(com.tyt.model.BlacklistUser blackListForUserId, User user, String idCard) {
		BlacklistUser updateUser = new BlacklistUser();
		updateUser.setId(blackListForUserId.getId());
		updateUser.setIdcard(idCard);
		updateUser.setStatus(1);
		updateUser.setPerpetual(1);
		updateUser.setRestrictStartTime(new Date());
		updateUser.setRestrictEndTime(TimeUtil.parseDateString(FOREVERTIME));
		updateUser.setRestrictNum(-1);
		updateUser.setOperaUserId(null);
		updateUser.setOperaUserName("身份认证校验触发拉黑");
		updateUser.setMtime(new Date());
		//新增记录
		BlacklistUserLog blacklistUserLog = buildBlackListUserLog(updateUser);

		if (blackListForUserId.getUserId() != null
				&& blackListForUserId.getUserId() == 0
				&& StringUtils.isNotBlank(blackListForUserId.getCellPhone())) {
			updateUser.setUserName(user.getTrueName());
			updateUser.setUserId(user.getId());
			updateUser.setIdcard(idCard);
			updateUser.setRegisterFlag(0);

			blacklistUserLog.setUserName(user.getTrueName());
			blacklistUserLog.setUserId(user.getId());
			blacklistUserLog.setIdcard(user.getIdCard());
			blacklistUserLog.setCellPhone(user.getCellPhone());
		} else {
			blacklistUserLog.setUserName(blackListForUserId.getUserName());
			blacklistUserLog.setUserId(blackListForUserId.getUserId());
			blacklistUserLog.setIdcard(blackListForUserId.getIdcard());
			blacklistUserLog.setCellPhone(blackListForUserId.getCellPhone());
		}

		blackListUserMapper.updateByPrimaryKeySelective(updateUser);

		blacklistUserLog.setId(null);
		blacklistUserLog.setRestrictNum(-1);
		blacklistUserLog.setCtime(new Date());
		blacklistUserLog.setCarLimitMinutes(0);
		blacklistUserLog.setLimitMinutes(0);
		blackListUserLogMapper.insert(blacklistUserLog);
	}

	private BlacklistUser buildBlackListUser(User user, String idCard) throws Exception{
		BlacklistUser blacklistUser = new BlacklistUser();
		blacklistUser.setUserId(user.getId());
		blacklistUser.setUserName(user.getTrueName());
		blacklistUser.setIdcard(idCard);
		blacklistUser.setStatus(1);
		blacklistUser.setPerpetual(1);
		blacklistUser.setRestrictStartTime(new Date());
		blacklistUser.setRestrictEndTime(TimeUtil.parseDateString(FOREVERTIME));
		blacklistUser.setRestrictNum(-1);
		blacklistUser.setCarLimitMinutes(0);
		blacklistUser.setLimitMinutes(0);
		blacklistUser.setCellPhone(user.getCellPhone());
		blacklistUser.setRegisterFlag(0);
		blacklistUser.setOperaUserId(null);
		blacklistUser.setOperaUserName("身份认证校验触发拉黑");
		blacklistUser.setCtime(new Date());
		blacklistUser.setMtime(new Date());
		return blacklistUser;
	}

	private BlacklistUserLog buildBlackListUserLog(BlacklistUser blacklistUser) {
		BlacklistUserLog blacklistUserLog = new BlacklistUserLog();
		BeanUtil.copyProperties(blacklistUser, blacklistUserLog);
		return blacklistUserLog;
	}
}
