package com.tyt.user.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.looppicture.bean.HpLoopPictureBean;
import com.tyt.looppicture.service.HpLoopPictureService;
import com.tyt.model.PublicResource;
import com.tyt.service.common.amap.api.AMapServiceAPI;
import com.tyt.service.common.amap.service.IPInfo;
import com.tyt.user.bean.IpInfoBean;
import com.tyt.user.service.PublicResourceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.AddressUtil;
import com.tyt.util.Constant;

@Service("publicResourceService")
public class PublicResourceServiceImpl extends
		BaseServiceImpl<PublicResource, Long> implements PublicResourceService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "hpLoopPictureService")
	private HpLoopPictureService hpLoopPictureService;

	@Resource(name = "publicResourceDao")
	public void setBaseDao(BaseDao<PublicResource, Long> publicResourceDao) {
		super.setBaseDao(publicResourceDao);
	}

	/*@Override
	public List<PublicResource> getPublicResourceByIp(String ip)
			throws Exception {
		// 根据IP判断是否保定用户
		String address = AddressUtil.getAddresses("ip=" + ip, "utf-8");
		List<PublicResource> list = null;
		// tyt_config表的开关
		Integer turnPicBaoDingOnOff = tytConfigService
				.getIntValue("publicResourceTurnPicBaoXianBaoDingOnOff");
		Integer turnPicFeiBaoDingOnOff = tytConfigService
				.getIntValue("publicResourceTurnPicBaoXianFeiBaoDingOnOff");
		if (address != null) {
			if (address.startsWith("河北省保定市")) {
				if (turnPicBaoDingOnOff != null && turnPicBaoDingOnOff == 1) {
					list = this.getInsuranceList();
				} else {
					list = this.getAll();
				}
			} else {
				if (turnPicFeiBaoDingOnOff != null
						&& turnPicFeiBaoDingOnOff == 1) {
					list = this.getInsuranceList();
				} else {
					list = this.getAll();
				}
			}
		} else {
			list = this.getAll();
		}
		return list;
	}*/

	@Override
	public List<PublicResource> getPublicResourceByIp(String ip)
			throws Exception {
		// 根据IP判断是否保定用户
//		String address = AddressUtil.getAddresses("ip=" + ip, "utf-8");
		//	String address = AddressUtil.getAddresses(ip);

		IPInfo  iPInfo =AMapServiceAPI.getIPInfo(ip);
		String address=iPInfo!=null?iPInfo.getCity():null;
		//由于高德服务器停止响应5分钟，导致服务请求超时，此处
		//String address=null;
		List<PublicResource> list = null;
		String hideTurnPictureName=null;
		String hideTurnPictureTitleLinkName=null;
		if (address != null) {
			if (address.indexOf("保定")!=-1) {
				hideTurnPictureName=tytConfigService.getStringValue("publicResourceBaoDingTurnPictureHide");
				hideTurnPictureTitleLinkName=tytConfigService.getStringValue("publicResourceBaoDingTurnTitleLinkPictureHide");
			} else {
				hideTurnPictureName=tytConfigService.getStringValue("publicResourceNoBaoDingTurnPictureHide");
				hideTurnPictureTitleLinkName=tytConfigService.getStringValue("publicResourceNoBaoDingTurnTitleLinkPictureHide");
			}
			if (hideTurnPictureName!=null&&hideTurnPictureName.trim().length()>0
					&&hideTurnPictureTitleLinkName!=null||hideTurnPictureTitleLinkName.trim().length()>0) {
				list = this.getShowList(hideTurnPictureName,hideTurnPictureTitleLinkName);
			} else {
				list = this.getAll();
			}
		} else {
			list = this.getAll();
		}


		//到得app首页轮播图
		Map<String,List<HpLoopPictureBean>>  map=hpLoopPictureService.getHpLoopPictureBean(address);
		if(map!=null&&map.size()>0){
			List<HpLoopPictureBean> brandLoopPictureList=map.get("brandLoopPictureList");
			PublicResource publicResource=null;
			if(brandLoopPictureList!=null&&brandLoopPictureList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("brandLoopPictureList");
				publicResource.setValue(JSON.toJSONString(brandLoopPictureList));
				publicResource.setId(-999l);
				list.add(publicResource);
			}
			List<HpLoopPictureBean> goodsLoopPictureList=map.get("goodsLoopPictureList");
			if(goodsLoopPictureList!=null&&goodsLoopPictureList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("goodsLoopPictureList");
				publicResource.setValue(JSON.toJSONString(goodsLoopPictureList));
				publicResource.setId(-888l);
				list.add(publicResource);
			}
			List<HpLoopPictureBean> carLoopPictureList=map.get("carLoopPictureList");
			if(carLoopPictureList!=null&&carLoopPictureList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("carLoopPictureList");
				publicResource.setValue(JSON.toJSONString(carLoopPictureList));
				publicResource.setId(-777l);
				list.add(publicResource);
			}
			List<HpLoopPictureBean> startPictureList=map.get("startPictureList");
			if(startPictureList!=null&&startPictureList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("startPictureList");
				publicResource.setValue(JSON.toJSONString(startPictureList));
				publicResource.setId(-1000l);
				list.add(publicResource);
			}
			List<HpLoopPictureBean> infoFeeCarOwnerList=map.get("infoFeeCarOwnerList");
			if(infoFeeCarOwnerList!=null&&infoFeeCarOwnerList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("infoFeeCarOwnerList");
				publicResource.setValue(JSON.toJSONString(infoFeeCarOwnerList));
				publicResource.setId(-1001l);
				list.add(publicResource);
			}
			List<HpLoopPictureBean> infoFeeShipperList=map.get("infoFeeShipperList");
			if(infoFeeShipperList!=null&&infoFeeShipperList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("infoFeeShipperList");
				publicResource.setValue(JSON.toJSONString(infoFeeShipperList));
				publicResource.setId(-1002l);
				list.add(publicResource);
			}
			List<HpLoopPictureBean> goodsDetailPictureList=map.get("goodsDetailPictureList");
			if(goodsDetailPictureList!=null&&goodsDetailPictureList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("goodsDetailPictureList");
				publicResource.setValue(JSON.toJSONString(goodsDetailPictureList));
				publicResource.setId(-1003l);
				list.add(publicResource);
			}
			List<HpLoopPictureBean> completePaymentPictureList=map.get("completePaymentPictureList");
			if(completePaymentPictureList!=null&&completePaymentPictureList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("completePaymentPictureList");
				publicResource.setValue(JSON.toJSONString(completePaymentPictureList));
				publicResource.setId(-1004l);
				list.add(publicResource);
			}
			List<HpLoopPictureBean> pubGoodsPictureList=map.get("pubGoodsPictureList");
			if(pubGoodsPictureList!=null&&pubGoodsPictureList.size()>0){
				publicResource=new PublicResource();
				publicResource.setName("pubGoodsPictureList");
				publicResource.setValue(JSON.toJSONString(pubGoodsPictureList));
				publicResource.setId(-1005l);
				list.add(publicResource);
			}
		}
		return list;
	}
	/**
	 * @Description  APP中jsp、H5页面公共资源
	 * <AUTHOR>
	 * @Date  2019/5/14 11:13
	 * @Param []
	 * @return java.util.List<com.tyt.model.PublicResource>
	 **/
	@Override
	public List<PublicResource> getPagePublicResource() throws Exception {

		//公共资源接口集合
		List<PublicResource> list = new ArrayList<PublicResource>();
		//获取购买过货运险的人数
		Integer buyerNum = (Integer) cacheService.getObject(Constant.CACHE_BUY_TRANSPORT_INSURANCE_NUM);
		//购买货运险人数提示语模板
		String buyerNumTips = tytConfigService.getStringValue("buyer_insurance_num_tips");
		if(buyerNum == null || buyerNum.intValue() < 0){
			buyerNum = 0;
		}
		buyerNumTips = StringUtils.replaceEach(buyerNumTips, new String[]{"${buyerNum}"}, new String[]{buyerNum.toString()});
		PublicResource publicResource = new PublicResource();
		publicResource.setName("buy_transport_insurance_num");
		publicResource.setValue(buyerNumTips);
		publicResource.setId(-2000l);
		list.add(publicResource);

		//App下载版本链接(android)
		PublicResource publicResource2 = this.getByKey("download_android_url");
		list.add(publicResource2);

		//PC客户端最新版本下载链接
		PublicResource publicResource3 = this.getByKey("download_pc_url");
		list.add(publicResource3);

		//用户注销原因
		PublicResource userCancelReason = this.getByKey("user_cancel_reason");
		list.add(userCancelReason);


		//用户退款原因
		PublicResource refund_reason = this.getByKey("refund_reason");
		list.add(refund_reason);
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PublicResource> getAll() {
		List<PublicResource> list = (List<PublicResource>) cacheService
				.getObject(Constant.CACHE_PUBLIC_RESOURCE_KEY);
		if (list == null) {
			list = this.getList(null, null);
			cacheService.setObject(Constant.CACHE_PUBLIC_RESOURCE_KEY, list,
					Constant.CACHE_EXPIRE_TIME_24H);
		}
		return list;
	}

	@Override
	public List<PublicResource> getShowList(String hideTurnPictureName,String hideTurnPictureTitleLinkName) throws Exception {
		List<PublicResource> list = this.getAll();// 先获取所有的公共资源数据
		if (hideTurnPictureName==null||hideTurnPictureName.trim().length()<=0
				||hideTurnPictureTitleLinkName==null||hideTurnPictureTitleLinkName.trim().length()<=0){
			return list;
		}
		Iterator<PublicResource> iterotor = list.iterator();
		PublicResource resource = null;
		boolean count1 = false;//标记1
		boolean count2 = false;//标记2
		String isReplaceSting=null;
		while (iterotor.hasNext()) {
			resource = iterotor.next();
			if (resource.getName().equals("turnPictures")) {
				isReplaceSting=isHideContain(resource.getValue(),hideTurnPictureName);
				if(isReplaceSting!=null){
					resource.setValue(isReplaceSting);
				}
				count1 = true;
			}
			if (resource.getName().equals("turnPicturesTitleLinks")) {
				isReplaceSting=isHideContain(resource.getValue(),hideTurnPictureTitleLinkName);
				if(isReplaceSting!=null){
					resource.setValue(isReplaceSting);
				}
				count2 = true;
			}
			if (count1&&count2)
				break;// 两个值都已经修改过了，则跳出循环
		}
		return list;
	}
	/**
	 * 如果包含的话，返回被替换的字符串
	 * @param curValue
	 * @param hideArray
	 * @return
	 */
	private String isHideContain(String curValue,String hideArray) {
		if(hideArray==null||curValue.trim().length()<=0)return curValue;
		String[] array=curValue.split(";");
		String specialSignal="$$";
		StringBuffer newString=new StringBuffer(specialSignal);
		for(int i=0;i<array.length;i++){
			if(!hideArray.contains("#"+i+"#"))newString.append(";").append(array[i]);
		}
		if(newString.equals(specialSignal)){
			return "";
		}
		return newString.toString().replace(specialSignal+";", "");

	}

	@Override
	public PublicResource getByKey(String key) {
		/*List<PublicResource> publicResources = getAll();
		for (int i = 0; i < publicResources.size(); i++) {
			if (key.equals(publicResources.get(i).getName())) {
				return publicResources.get(i);
			}
		}
		return null;*/
		PublicResource publicResource = (PublicResource) cacheService.getObject(Constant.CACHE_PUBLIC_RESOURCE_PREFIX + key);
		if (publicResource == null) {
			PublicResource query = new PublicResource();
			query.setName(key);
			publicResource = this.find(query);
			if (publicResource != null) {
				cacheService.setObject(Constant.CACHE_PUBLIC_RESOURCE_PREFIX + key, publicResource, Constant.CACHE_EXPIRE_TIME_24H);
			}
		}
		return publicResource;
	}

	@Override
	public IpInfoBean getInfo(String ip) throws Exception {
		logger.info("ip:{}",ip);
		Map<String, String> detailArea = new HashMap<String, String>();
		// 自动定位开关，配置在tyt_config表，0关、1开
		Integer appSearchTransportAddressAutoLocationSwitch = tytConfigService
				.getIntValue("appSearchTransportAddressAutoLocationSwitch");
		if (appSearchTransportAddressAutoLocationSwitch != null
				&& appSearchTransportAddressAutoLocationSwitch == 1) {
			// 根据ip查询出省市县
			detailArea = AddressUtil.getAddressesMap(ip);
		}
		if (detailArea == null || detailArea.size() <= 0) {
			// 定位无结果或者自动定位关闭则获取默认省市县
			detailArea = getTransportDefaultArea();
		}
		logger.info("detailArea:{}",JSON.toJSONString(detailArea));
		IpInfoBean detailLocationByMapFromDB = getDetailLocationByMapFromDB(detailArea);
		logger.info("detailLocationByMapFromDB:{}",JSON.toJSONString(detailLocationByMapFromDB));
		if (detailLocationByMapFromDB==null||StringUtils.isEmpty(detailLocationByMapFromDB.getCity())
				||StringUtils.isEmpty(detailLocationByMapFromDB.getCounty())
				||StringUtils.isEmpty(detailLocationByMapFromDB.getProvince())){
			detailArea.put("province", "北京");
			detailArea.put("city", "北京市");
			detailArea.put("county", "东城区");
			logger.info("detailArea:{}",JSON.toJSONString(detailArea));
			return getDetailLocationByMapFromDB(detailArea);
		}else {
			// 返回结果集
			return detailLocationByMapFromDB;
		}
	}

	@Override
	public PublicResource getGlobalByParam(String paranName) throws Exception {
		return  this.getByKey(paranName);
	}

	@Override
	public List<PublicResource> getGlobalByParamList(List<String> paramNameList) throws Exception {
		ArrayList<PublicResource> list = new ArrayList<>();
//		int size = paramNameList.size();
//		for (int i = 0; i < size; i++) {
//			String paranName = paramNameList.get(i);
//			list.add(this.getByKey(paranName));
//		}
		for (String paranName : paramNameList) {
			list.add(this.getByKey(paranName));
		}
		return list;
	}

	/**
	 * 获取找货默认的省市县 采用tyt_config表的默认值，键appSearchTransportDefaultAddress
	 * tyt_config表的默认值为空，定位到北京北京市东城区
	 *
	 * @return
	 */
	private Map<String, String> getTransportDefaultArea() {
		Map<String, String> detailArea = new HashMap<String, String>();
		String defaultArea = tytConfigService
				.getStringValue("appSearchTransportDefaultAddress");// 默认值
		if (defaultArea != null && !defaultArea.trim().equals("")) {
			String[] arr = defaultArea.split("_");
			if (arr.length == 3) {
				detailArea.put("province", arr[0]);
				detailArea.put("city", arr[1]);
				detailArea.put("county", arr[2]);
			} else if (arr.length == 2) {
				detailArea.put("province", arr[0]);
				detailArea.put("city", arr[1]);
				detailArea.put("county", "");
			} else {
				detailArea.put("province", arr[0]);
				detailArea.put("city", "");
				detailArea.put("county", "");
			}

		} else {
			detailArea.put("province", "北京");
			detailArea.put("city", "北京市");
			detailArea.put("county", "东城区");
		}
		return detailArea;
	}

	/**
	 * 根据省市县查找tyt_geo_dict表对应的信息：省市县都不为空，type=3；县为空，其余不为空type=2；省不为空，其余为空，type=1
	 * 。
	 *
	 * @param detailArea
	 * @return IpInfoBean
	 */
	private IpInfoBean getDetailLocationByMapFromDB(
			Map<String, String> detailArea) {
		StringBuffer sqlBuffer = new StringBuffer(
				"SELECT px,py,longitude,latitude,province_name province,city_name city,area_name county "
						+ " FROM tyt_city WHERE level=?");
		List<Object> paramList = new ArrayList<Object>();
		String province = detailArea.get("province");
		String city = detailArea.get("city");
		String county = detailArea.get("county");
		if (city != null && !"".equals(city.trim())) {
			sqlBuffer.append(" AND  city_name=? AND area_name=? AND LOCATE(province_name,?)>0");
			paramList.add(3);
			paramList.add(city);
			if (county != null && !"".equals(county.trim())){
				paramList.add(county);
			}else {
				paramList.add(city);
			}
			paramList.add(province);
		}
		else{
			return null;
		}
		/* else if (province != null && !"".equals(province.trim())) {
			sqlBuffer.append(" AND  LOCATE(provinc,?)>0");
			paramList.add(1);
			paramList.add(province);
		}*/
		Map<String, org.hibernate.type.Type> paramMap = new HashMap<String, org.hibernate.type.Type>();
		paramMap.put("province", Hibernate.STRING);
		paramMap.put("city", Hibernate.STRING);
		paramMap.put("county", Hibernate.STRING);
		paramMap.put("px", Hibernate.STRING);
		paramMap.put("py", Hibernate.STRING);
		paramMap.put("longitude", Hibernate.STRING);
		paramMap.put("latitude", Hibernate.STRING);
		List<IpInfoBean> ipInfoBeanList = this.getBaseDao().search(
				sqlBuffer.toString(), paramMap, IpInfoBean.class,
				paramList.toArray());
		if (ipInfoBeanList != null && ipInfoBeanList.size() > 0) {
			IpInfoBean ipInfoBean = ipInfoBeanList.get(0);
			// 新的地址库经纬度和坐标不需要数值调整了
			/*ipInfoBean.setPx(new BigDecimal(ipInfoBean.getPx())
					.movePointLeft(2).toString());
			ipInfoBean.setPy(new BigDecimal(ipInfoBean.getPy())
					.movePointLeft(2).toString());
			ipInfoBean.setLongitude(new BigDecimal(ipInfoBean.getLongitude())
					.movePointLeft(2).toString());
			ipInfoBean.setLatitude(new BigDecimal(ipInfoBean.getLatitude())
					.movePointLeft(2).toString());*/
			return ipInfoBean;
		}
		return null;
	}

}
