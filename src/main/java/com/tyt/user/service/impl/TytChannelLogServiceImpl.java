package com.tyt.user.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytChannelLog;
import com.tyt.user.service.TytChannelLogService;
@Service("tytChannelLogService")
public class TytChannelLogServiceImpl extends BaseServiceImpl<TytChannelLog,Long> implements TytChannelLogService {
	@Resource(name="tytChannelLogDao")
	public void setBaseDao(BaseDao<TytChannelLog, Long> tytChannelLogDao) {
	        super.setBaseDao(tytChannelLogDao);
	}
	
	
}
