package com.tyt.user.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.dao.TytForceUpgradeUserDao;
import com.tyt.user.querybean.SpecifiedUserUpgradeBean;
import com.tyt.util.StringUtil;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.TytForceUpgradeUser;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytForceUpgradeUserService;
import com.tyt.util.Constant;

@Service("tytForceUpgradeUserService")
public class TytForceUpgradeUserServiceImpl extends BaseServiceImpl<TytForceUpgradeUser,Long> implements TytForceUpgradeUserService{

	public Logger logger = LoggerFactory
			.getLogger(this.getClass());

	@Resource(name="tytForceUpgradeUserDao")
	public void setBaseDao(BaseDao<TytForceUpgradeUser, Long> tytForceUpgradeUserDao) {
	        super.setBaseDao(tytForceUpgradeUserDao);
	}

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

//	@Override
//	public List<TytForceUpgradeUser> getForceUpgradeUserListFromDB(String upgradeType)
//			throws Exception {
//		StringBuffer conditionSQL=new StringBuffer(" 1=1");
//		if(upgradeType!=null&&!upgradeType.trim().equals("")){
//			conditionSQL.append(" and upgradeType=").append(upgradeType);
//		}
//		return this.getBaseDao().search(conditionSQL.toString(), null);
//	}

//	@SuppressWarnings("unchecked")
//	@Override
//	public Map<String,TytForceUpgradeUser> getForceUpgradePhoneMap()
//			throws Exception {
//		Object obj=cacheService.getObject(Constant.CACHE_FORCE_UPGRADE_USER_CELLPHONE_MAP_KEY);
//		logger.info("强制升级配置信息obj:size：{}",obj);
//		if(obj==null){
//			logger.info("用户强制升级:memcache phone无");
//			List<TytForceUpgradeUser> forceUserList=getForceUpgradeUserListFromDB("1");
//			logger.info("强制升级配置信息查询数据库：forceUserList.size:{}",forceUserList!=null ? forceUserList.size() : 0);
//			if(forceUserList!=null&&forceUserList.size()>0){
//				Map<String,TytForceUpgradeUser> userIdMap=new HashMap<String,TytForceUpgradeUser>();
//				for(TytForceUpgradeUser fuser:forceUserList){
//					userIdMap.put(fuser.getUserPhone()+"_"+fuser.getType(), fuser);
//				}
//				cacheService.setObject(Constant.CACHE_FORCE_UPGRADE_USER_CELLPHONE_MAP_KEY,userIdMap,Constant.CACHE_EXPIRE_TIME_24H);
//				return userIdMap;
//			}
//
//		}
//		logger.info("用户强制升级:memcache phone有");
//		return (Map<String, TytForceUpgradeUser>) obj;
//	}

//	@SuppressWarnings("unchecked")
//	@Override
//	public Map<String, TytForceUpgradeUser> getForceUpgradeUserMap() throws Exception {
//
//		Object obj=cacheService.getObject(Constant.CACHE_FORCE_UPGRADE_USER_ID_MAP_KEY);
//		if(obj==null){
//			logger.info("用户强制升级:memcache userId无");
//			List<TytForceUpgradeUser> forceUserList=getForceUpgradeUserListFromDB("1");
//			if(forceUserList!=null&&forceUserList.size()>0){
//				Map<String,TytForceUpgradeUser> userIdMap=new HashMap<String,TytForceUpgradeUser>();
//				for(TytForceUpgradeUser fuser:forceUserList){
//					userIdMap.put(fuser.getUserId()+"_"+fuser.getType(), fuser);
//				}
//				cacheService.setObject(Constant.CACHE_FORCE_UPGRADE_USER_ID_MAP_KEY,userIdMap,Constant.CACHE_EXPIRE_TIME_24H);
//				return userIdMap;
//			}
//
//		}
//		logger.info("用户强制升级:memcache userId有");
//		return (Map<String, TytForceUpgradeUser>) obj;
//	}

//	@SuppressWarnings("unchecked")
//	@Override
//	public Map<String, TytForceUpgradeUser> getForceUpgradeClientIdMap() throws Exception {
//
//		Object obj=cacheService.getObject(Constant.CACHE_FORCE_UPGRADE_CLIENTID_MAP_KEY);
//		if(obj==null){
//			logger.info("用户强制升级:memcache clientId无");
//			List<TytForceUpgradeUser> forceUserList=getForceUpgradeUserListFromDB("1");
//			if(forceUserList!=null&&forceUserList.size()>0){
//				Map<String,TytForceUpgradeUser> clientIdMap=new HashMap<String,TytForceUpgradeUser>();
//				for(TytForceUpgradeUser fuser:forceUserList){
//					clientIdMap.put(fuser.getClientId()+"_"+fuser.getType(), fuser);
//				}
//				cacheService.setObject(Constant.CACHE_FORCE_UPGRADE_CLIENTID_MAP_KEY,clientIdMap,Constant.CACHE_EXPIRE_TIME_24H);
//				return clientIdMap;
//			}
//
//		}
//		logger.info("用户强制升级:memcache clientId有");
//		return (Map<String, TytForceUpgradeUser>) obj;
//	}

//	//根据手机号查询是否需要强制升级
//	@Override
//	public TytForceUpgradeUser getForceUpgradeByPhone(String phone_type) throws Exception {
//		Map<String,TytForceUpgradeUser> map=this.getForceUpgradePhoneMap();
//		if(map==null||map.size()<=0){
//			return null;
//		}
//
//
//
//		return map.get(phone_type);
//	}
//
//	//根据user_id查询是否需要强制升级
//	@Override
//	public TytForceUpgradeUser getForceUpgradeByUserId(String userId_type)
//			throws Exception {
//		Map<String,TytForceUpgradeUser> map=this.getForceUpgradeUserMap();
//		if(map==null||map.size()<=0){
//			return null;
//		}
//		return map.get(userId_type);
//	}
//
//	//根据Client_id查询是否需要强制升级
//	@Override
//	public TytForceUpgradeUser getForceUpgradeByClientId(String clientId_type)
//			throws Exception {
//		Map<String,TytForceUpgradeUser> map=this.getForceUpgradeClientIdMap();
//		if(map==null||map.size()<=0){
//			return null;
//		}
//		return map.get(clientId_type);
//	}

	//根据userId查询是否需要强制升级
	private List<SpecifiedUserUpgradeBean> getUpgradeTaskInfoByUserId(String userIdType){
		String cacheKey = tytConfigService.getStringValue(Constant.SPECIFIED_USER_UPGRADE_PREFIX,"common_specified_user_upgrade_");
		logger.info("cacheKey+userIdType:{}",cacheKey+userIdType);
		List<SpecifiedUserUpgradeBean> suubs = RedisUtil.getObjectList(cacheKey+userIdType);
		return suubs;
	}

	//根据手机号查询是否需要强制升级
	private List<SpecifiedUserUpgradeBean> getUpgradeTaskInfoByPhone(String phoneType){
		String cacheKey = tytConfigService.getStringValue(Constant.SPECIFIED_USER_UPGRADE_PREFIX,"common_specified_user_upgrade_");
		List<SpecifiedUserUpgradeBean> suubs = RedisUtil.getObjectList(cacheKey+phoneType);
		return suubs;
	}

	//根据Client_id查询是否需要强制升级
	private List<SpecifiedUserUpgradeBean> getUpgradeTaskInfoByClientId(String clientIdType){
		String cacheKey = tytConfigService.getStringValue(Constant.SPECIFIED_USER_UPGRADE_PREFIX,"common_specified_user_upgrade_");
		List<SpecifiedUserUpgradeBean> suubs = RedisUtil.getObjectList(cacheKey+clientIdType);
		return suubs;
	}


	@Override
	public Map<String, Object> getForceUpgradeVersionInfo(Integer queryFlag,String registerPhone,Long userId,String clientId,
			String channelCode,String clientSign,String userVersion) {
		logger.info("用户强制升级参数:queryFlag【{}】,registerPhone【{}】,userId【{}】,channelCode【{}】,clientSign【{}】,userVersion【{}】,clientId【{}】"
				,queryFlag,registerPhone,userId,channelCode,clientSign,userVersion,clientId);
		//数据校验
		if(!StringUtils.isNumeric(userVersion)){
			logger.info("用户版本号必须是数字");
			return null;
		}
		//强制升级开关是否打开
		Integer forceUserUpgradeKey=tytConfigService.getIntValue(Constant.TYT_CONFIG_FORCE_USER_UPGRADE_KEY);
		if(forceUserUpgradeKey==null||forceUserUpgradeKey.intValue()!=1){
			logger.info("用户强制升级开关:关");
			return null;
		}

		String type="1";//1安卓2ios企业3ios个人
		if(clientSign.equals("3")){
			String downloadType=getDownloadTypeByChannelCode(channelCode);
			if(downloadType.equals("1")){
				type="3";
			}else if(downloadType.equals("2")){
				type="2";
			}
		}
		//用户版本号
		Integer userVersionInt=Integer.parseInt(userVersion);

		Integer oldVersion = tytConfigService.getIntValue("plat_ios_26_simulated_login_version",5009);
		//增加IOS企业版旧版本模拟登陆强制升级弹窗兼容处理   5009版本以前不支持模拟登陆弹窗
		//queryFlag==1 && userId != null 则判断为模拟登录
		if("2".equals(type) && queryFlag==1 && userId != null &&  userVersionInt.intValue() <= oldVersion.intValue()){
			logger.info("IOS企业版旧版本模拟登陆强制升级弹窗兼容处理,直接返回");
			return null;
		}

		TytForceUpgradeUser forceUpgradeUser=null;
		List<SpecifiedUserUpgradeBean> suubs = null;
//		if(queryFlag==1){
//			logger.info("用户强制升级:根据用户id判断");
//			forceUpgradeUser=this.getForceUpgradeByUserId(userId+"_"+type);
//		}else if(queryFlag==2){
//			logger.info("用户强制升级:根据用户账号判断");
//			forceUpgradeUser=this.getForceUpgradeByPhone(registerPhone+"_"+type);
//		}else if(queryFlag==3){
//			logger.info("用户强制升级:根据用户clientId判断");
//			forceUpgradeUser=this.getForceUpgradeByClientId(clientId+"_"+type);
//		}
		//根据用户userId或者手机号判断
		if(queryFlag==1 && userId != null){
			logger.info("用户强制升级:根据用户id判断");
			suubs = getUpgradeTaskInfoByUserId(userId+"_"+type);
			logger.info("suubs:{}",suubs);
		}else if(queryFlag==2 && StringUtils.isNotEmpty(registerPhone)){
			logger.info("用户强制升级:根据用户账号判断");
			suubs = getUpgradeTaskInfoByPhone(registerPhone+"_"+type);
		}
//		else if(queryFlag==3){
//			logger.info("用户强制升级:根据用户clientId判断");
//			suubs = getUpgradeTaskInfoByClientId(clientId+"_"+type);
//		}
		//根据用户clientId判断
		if(StringUtils.isNotEmpty(clientId)){
			logger.info("用户强制升级:根据用户clientId判断");
			if(suubs == null){
				suubs = getUpgradeTaskInfoByClientId(clientId+"_"+type);
			}else{
				List<SpecifiedUserUpgradeBean> suubsTemp = getUpgradeTaskInfoByClientId(clientId+"_"+type);
				if(suubsTemp != null && suubsTemp.size() > 0){
					suubs.addAll(suubsTemp);
				}
			}

		}

		if(suubs == null || suubs.size() <= 0){
			logger.info("用户强制升级:无需升级，缓存不存在该用户升级数据");
			return null;
		}

		Long taskId=null;
		Date nowDate = new Date();
		boolean isUpdateFinish = false;
		//TODO 校验升级规则
		for(SpecifiedUserUpgradeBean suub : suubs){
			//版本号已经是目标版本号 并且在当前时间范围内
			if(userVersionInt.intValue() == Integer.valueOf(suub.getGoalVersion()).intValue() &&
					nowDate.getTime() > suub.getUpgradeBegintime().getTime() &&  nowDate.getTime() < suub.getUpgradeEndtime().getTime()){
					isUpdateFinish=true;
					taskId=suub.getTaskId();
					break;
			}
			//时间是否符合
			if(nowDate.getTime() < suub.getUpgradeBegintime().getTime() ||   nowDate.getTime() > suub.getUpgradeEndtime().getTime()){
				continue;
			}
			//版本号是否再区间内
			if(userVersionInt.intValue() < Integer.valueOf(suub.getBeginVersion()).intValue() || userVersionInt.intValue() > Integer.valueOf(suub.getEndVersion()).intValue()){
				continue;
			}
			//都符合则停止循环，执行以下操作
			forceUpgradeUser = new TytForceUpgradeUser();
			forceUpgradeUser.setVersion(suub.getGoalVersion());
			forceUpgradeUser.setUrl(suub.getUrl());
			forceUpgradeUser.setUpgradeType(suub.getUpgradeMode() != null ? (suub.getUpgradeMode().intValue() - 1)+"" : "0"); //转换 1提示 2强制   原有：// 0 选择// 1强升
			forceUpgradeUser.setContent(suub.getCoutent());
			break;
		}
		Map<String, Object> upgradeStrategyMap = new HashMap<String, Object>();
		if(isUpdateFinish){
			upgradeStrategyMap.put("isUpdate",1);
			upgradeStrategyMap.put("updateVersion",userVersionInt);
			upgradeStrategyMap.put("taskId",taskId);
		}
		if(forceUpgradeUser == null){
			logger.info("用户强制升级:无需升级，不存在符合该用户的升级数据");
			return upgradeStrategyMap;
		}
		upgradeStrategyMap.put("version",forceUpgradeUser.getVersion());
		upgradeStrategyMap.put("url", forceUpgradeUser.getUrl());
		upgradeStrategyMap.put("type", forceUpgradeUser.getUpgradeType()); // 0 选择// 1强升
		upgradeStrategyMap.put("contentNew", forceUpgradeUser.getContent());
		return upgradeStrategyMap;


	}


//		Integer versionInt=Integer.parseInt(forceUpgradeUser.getVersion());
//		if(userVersionInt>=versionInt){
//			logger.info("用户强制升级:此用户已经是最新版本");
//			return null;
//		}
//


	/**
	 * 根据渠道号获取下载类型
	 * @param channelCode26:企业渠道号,其他:默认为个人渠道号
	 * @return
	 */
	private String getDownloadTypeByChannelCode(String channelCode) {
		String downloadType = "1";
		if (channelCode != null && channelCode.trim().length() > 0
				&& channelCode.trim().equals("26")) {
			downloadType = "2";
		}
		return downloadType;
	}

	@Override
	public void updateInfo(Long userId, Object object,Object taskId) {
		((TytForceUpgradeUserDao) this.getBaseDao()).updateInfo(userId,object,taskId);

	}
}
