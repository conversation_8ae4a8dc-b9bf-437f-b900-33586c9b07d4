package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.UserCreditInfoPopup;
import com.tyt.model.UserIdentity;
import com.tyt.user.service.UserCreditInfoPopupService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 用户信用分弹窗服务层实现类
 * <AUTHOR>
 * @date 2022/9/20 18:30
 */
@Service("userCreditInfoPopupService")
public class UserCreditInfoPopupServiceImpl extends BaseServiceImpl<UserCreditInfoPopup,Long> implements UserCreditInfoPopupService {

    @Override
    @Resource(name="userCreditInfoPopupDao")
    public void setBaseDao(BaseDao <UserCreditInfoPopup, Long> userCreditInfoPopupDao) {
        super.setBaseDao(userCreditInfoPopupDao);
    }

    /**
     * @description 根据用户ID获取信用弹框记录
     * <AUTHOR>
     * @date 2022/9/21 10:08
     * @param userId
     * @return com.tyt.model.UserCreditInfoPopup
     */
    @Override
    public UserCreditInfoPopup getPopupByUserId(Long userId) throws Exception {
        String sql = "select * from user_credit_info_popup where user_id = ? order by id desc";
        List<UserCreditInfoPopup> list = this.getBaseDao().queryForList(sql,new Object[]{userId});
        if(list != null && list.size() > 0){
            return list.get(0);
        }
        return null;
    }
}
