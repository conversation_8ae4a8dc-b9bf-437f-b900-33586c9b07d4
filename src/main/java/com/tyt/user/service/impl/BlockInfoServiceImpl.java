package com.tyt.user.service.impl;
import java.math.BigInteger;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.BlockInfo;
import com.tyt.user.service.BlockInfoService;
@Service("blockInfoService")
public class BlockInfoServiceImpl extends BaseServiceImpl<BlockInfo,Long> implements BlockInfoService{

	    @Resource(name="blockInfoDao")
		public void setBaseDao(BaseDao<BlockInfo, Long> blockInfoDao) {
		        super.setBaseDao(blockInfoDao);
		}
	    
	    @Override
	    public BigInteger getBlockInfoCount(String phone){
	    	//1,2,8
	    	String sql="select count(1) from tyt_info_blocker where (cell_phone=? or blocker_tel=?) and STATUS=2 and verify_cause IN(1,2,8)";
	    	BigInteger count=this.getBaseDao().query(sql, new Object[]{phone,phone});
	    	return count;
	    }
		
    
}
