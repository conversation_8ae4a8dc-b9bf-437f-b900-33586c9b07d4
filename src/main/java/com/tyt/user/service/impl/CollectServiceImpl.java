package com.tyt.user.service.impl;

import java.math.BigInteger;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.User;
import com.tyt.user.service.CollectService;
@Service("collectService")
public class CollectServiceImpl extends BaseServiceImpl<User, Long> implements CollectService{

	@Resource(name="userDao")
	public void setBaseDao(BaseDao<User, Long> baseDao) {
		// TODO Auto-generated method stub
		super.setBaseDao(baseDao);
	}
	
	@Override
	public List<Long> getInfoIdsByType(String userId, String type, String status,Integer currentPage,Integer pageSize) {
		String selSQL="SELECT ms_id FROM tyt_collect_info WHERE user_id=? AND type=? AND status=?";
		return this.getBaseDao().search(selSQL, null, null, new Object[]{userId,type,status}, currentPage, pageSize);
	}
	
	@Override
	public int getInfoIdsCountsByType(String userId, String type, String status) {
		String selSQL="SELECT COUNT(*) FROM tyt_collect_info WHERE user_id=? AND type=? AND status=?";
		BigInteger count=this.getBaseDao().query(selSQL, new Object[]{userId,type,status});
		if(count==null)return 0;
		return count.intValue();
	}

}
