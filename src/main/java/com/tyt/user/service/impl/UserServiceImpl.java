package com.tyt.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.tyt.acvitity.service.ActivityService;
import com.tyt.acvitity.service.ConventionActivityService;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.bcar.service.BcarJobService;
import com.tyt.bcar.service.BcarRecruitService;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.config.util.AppConfig;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.infofee.service.UserCancelService;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.model.*;
import com.tyt.noticePopup.bean.PopupSaveBean;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupService;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.bean.GoodsType;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.BlacklistUserOrders;
import com.tyt.plat.entity.base.TytUserIdentityLabel;
import com.tyt.plat.enums.EnterpriseAuthStatusEnum;
import com.tyt.plat.enums.GlobalStatusEnum;
import com.tyt.plat.enums.GoodsTypeFirstEnum;
import com.tyt.plat.mapper.base.TytOwnerAuthMapper;
import com.tyt.plat.mapper.base.TytUserIdentityLabelMapper;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.promo.service.ICouponService;
import com.tyt.scar.service.ScarJobService;
import com.tyt.scar.service.ScarRecruitService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.smallTools.controller.bean.UserDetailByCellphone;
import com.tyt.user.bean.*;
import com.tyt.user.dao.UserDao;
import com.tyt.user.querybean.UserBean;
import com.tyt.user.service.*;
import com.tyt.util.*;
import com.tyt.wxuser.service.TytCarWxUserInfoService;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.tyt.user.enums.UserPermissionTypeEnum.*;
import static com.tyt.user.enums.UserPermissionTypeEnum.GOODS_NUM_NEW;

@Service("userService")
public class UserServiceImpl extends BaseServiceImpl<User, Long> implements UserService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final PropertiesFileUtil propertiesFileUtil = PropertiesFileUtil.init("message");

    private static final String userCenterUrl = AppConfig.getProperty("user.center.api.url");
    public static final int IS_MOCK_USER = 1;
    @Resource(name = "opLogService")
    private OpLogService opLogService;

    @Resource(name = "cacheServiceMcImpl")
    protected CacheService cacheService;

    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;
    @Resource
    private TytOwnerAuthMapper tytOwnerAuthMapper;

    @Autowired
    private TytConfigService tytconfigService;

    @Autowired
    private ConventionActivityService conventionActivityService;

    @Autowired
    private TytUserIdentityLabelMapper tytUserIdentityLabelMapper;

    @Autowired
    private TransportWayBillExService transportWayBillExService;

    @Autowired
    private TytCarWxUserInfoService carWxUserInfoService;

    /**
     * 客户端区分新老app code值，小于等于6的为老版本客户端，大于6的为版本拆分后的新版本
     */
    private static final int CLIENT_SIGN_SEPERATOR = 6;

    private static final String UNAUTH_USER_CALL_EXEMPT_SWITCH = "unauth_user_call_exempt_switch";
    private static final Map<String, Boolean> banPasswords = new HashMap<>();

    static {
        banPasswords.put("e10adc3949ba59abbe56e057f20f883e", true); // 123456
        banPasswords.put("fcea920f7412b5da7be0cf42b8c93759", true); // 1234567
        banPasswords.put("25d55ad283aa400af464c76d713c07ad", true); // 12345678
        banPasswords.put("25f9e794323b453885f5181f1b624d0b", true); // 123456789
        banPasswords.put("c33367701511b4f6020ec61ded352059", true); // 654321
        banPasswords.put("4297f44b13955235245b2497399d7a93", true); // 123123

        banPasswords.put("c5db244dbfc91ca5dc46626cc21d5594", true); // 12301230
        banPasswords.put("dc483e80a7a0bd9ef71d8cf973673924", true); // a123456
        banPasswords.put("efe6398127928f1b2e9ef3207fb82663", true); // qweqwe
        banPasswords.put("f379eaf3c831b04de153469d1bec345e", true); // 666666
        banPasswords.put("3d186804534370c3c817db0563f0e461", true); // 321321
        banPasswords.put("7c497868c9e6d3e4cf2e87396372cd3b", true); // 66666666
        banPasswords.put("8ddcff3a80f4189ca1c9d4d902c3c909", true); // 88888888
        banPasswords.put("75e266f182b4fa3625d4a4f4f779af54", true); // 666888
        banPasswords.put("670b14728ad9902aecba32e22fa4f6bd", true); // 000000
        banPasswords.put("21218cca77804d2ba1922c33e0151105", true); // 888888
        banPasswords.put("af8f9dffa5d420fbc249141645b962ee", true); // a12345
        banPasswords.put("d5ee2eedfcf7adc285db4967bd86910d", true); // 6666666
        banPasswords.put("388ec3e3fa4983032b4f3e7d8fcb65ad", true); // 8888888
        banPasswords.put("002772406c000396fd4704af816b075f", true); // 618618
        banPasswords.put("fea73474cda848c7233e377158dbef60", true); // 871871
    }

    @Resource(name = "tytChannelLogService")
    private TytChannelLogService tytChannelLogService;
    @Resource(name = "tytUserMacService")
    private TytUserMacService userMacService;
    @Resource(name = "tytSourceService")
    private TytSourceService tytSourceService;

    @Resource(name = "collectService")
    private CollectService collectService;

    @Resource(name = "bCarJobService")
    private BcarJobService bCarJobService;

    @Resource(name = "bCarRecruitService")
    private BcarRecruitService bCarRecruitService;

    @Resource(name = "sCarJobService")
    private ScarJobService sCarJobService;

    @Resource(name = "sCarRecruitService")
    private ScarRecruitService sCarRecruitService;

    @Resource(name = "tytMqMessageService")
    TytMqMessageService tytMqMessageService;

    @Resource(name = "tytMessageTmplService")
    private TytMessageTmplService messageTmplService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "tytUserSubService")
    TytUserSubService tytUserSubService;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService identityAuthService;

    @Resource(name = "carService")
    private CarService carService;

    @Resource(name = "tytBubbleService")
    private TytBubbleService bubbleService;

    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityService;

    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;
    @Resource(name = "blockInfoService")
    private BlockInfoService blockInfoService;
    @Resource(name = "tytForceUpgradeUserService")
    private TytForceUpgradeUserService tytForceUpgradeUserService;
    @Resource(name = "blacklistUserService")
    private BlacklistUserService blacklistUserService;
    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityAuthService;
    @Resource(name = "tytNoticePopupService")
    private TytNoticePopupService noticePopupService;
    @Resource(name = "tytNoticeRemindService")
    private TytNoticeRemindService noticeRemindService;
    @Resource(name = "userBuyGoodsService")
    private UserBuyGoodsService userBuyGoodsService;
    @Resource(name = "couponService")
    private ICouponService couponService;
    @Resource(name = "versionBusiness")
    private VersionBusinessService versionBusiness;
    @Resource(name = "activityService")
    private ActivityService activityService;

    @Resource(name = "userCancelService")
    private UserCancelService userCancelService;

    @Resource(name = "tytOwnerAuthService")
    private TytOwnerAuthService tytOwnerAuthService;

    @Resource(name = "blacklistUserOrdersService")
    private BlacklistUserOrdersService blacklistUserOrdersService;

    @Resource(name = "userDao")
    public void setBaseDao(BaseDao<User, Long> userDao) {
        super.setBaseDao(userDao);
    }

    @Autowired
    private AbtestService abtestService;

    @Override
    public void updateServeDays() {
        ((UserDao) this.getBaseDao()).decreServeDays();
    }

    @Override
    public void clearQqModTimes() {
        ((UserDao) this.getBaseDao()).clearQqModTimes();
    }

    @Override
    public User getUserByCellphone(String cellphone) {

        StringBuffer sql = new StringBuffer();
        sql.append(" entity.cellPhone ='").append(cellphone.trim()).append("'");

        List<User> userList = getList(sql.toString(), null);

        User user = null;
        if (userList != null && userList.size() > 0) {
            user = userList.get(0);
        }
        return user;
    }

    @Override
    public List<String> getSales(String condition) {
        return ((UserDao) this.getBaseDao()).getSales(condition);
    }

    @Override
    public User getUserByCellAndSign(String cellPhone, Integer userSign) {
        StringBuffer sql = new StringBuffer();
        sql.append(" entity.cellPhone ='").append(cellPhone.trim()).append("'").append(" and entity.userSign=").append(userSign);
        List<User> userList = getList(sql.toString(), null);
        User user = null;
        if (userList != null && userList.size() > 0) {
            user = userList.get(0);
        }
        return user;
    }

    @Override
    public User getUserByCell(String cellPhone) {
        StringBuffer sql = new StringBuffer();
        sql.append(" entity.cellPhone ='").append(cellPhone.trim()).append("'");
        List<User> userList = getList(sql.toString(), null);
        User user = null;
        if (userList != null && userList.size() > 0) {
            user = userList.get(0);
        }
        return user;
    }

    @Override
    public boolean updatePassword(Long id, String password) throws Exception {
        return ((UserDao) this.getBaseDao()).updatePassword(id, password);
    }

    @Override
    public User getByUserId(Long userId) throws Exception {
        User user = (User) cacheService.getObject(Constant.CACHE_USER_KEY + userId);
        if (user == null) {
            user = this.getById(userId);
            if (user != null) {
                this.getBaseDao().refresh(user);
                cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
            }
        }
        return user;
    }

    @Override
    public User getByUserIdNonCache(Long userId) {
        try {
            return this.getByUserId(userId);
        } catch (Exception e) {
            logger.error("根据用户id获取用户信息异常:", e);
        }
        return null;
    }

    @Override
    public boolean updateLastInfo(Long id, String ticket, Integer clientSign, String clientVersion, String osVersion) throws Exception {
        return ((UserDao) this.getBaseDao()).updateLastInfo(id, ticket, clientSign, clientVersion, osVersion);
    }

    @Override
    public void saveHead(String headurl, Long userId) throws Exception {
        // 删除缓存
        cacheService.del(Constant.CACHE_USER_KEY + userId);
        // 保存数据
        ((UserDao) this.getBaseDao()).saveHead(headurl, userId);
        // 查询用户信息
        User user = this.getById(userId);
        // 添加缓存
        cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
    }

    @Override
    public void saveUserName(String userName, Long userId, Integer clientSign) throws Exception {
        // 删除缓存
        cacheService.del(Constant.CACHE_USER_KEY + userId);
        // 保存数据
        ((UserDao) this.getBaseDao()).saveUserName(userName, userId, clientSign);
        // 查询用户信息
        User user = this.getById(userId);
        // 添加缓存
        cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
    }

    @Override
    public User addUser(User user) throws Exception {
        // 保存用户
        this.add(user);
        Long id = user.getId();
        // 修改qq=用户ID,userName=会员+用户ID，
        if (user.getQq() == null) {
            updateQQ(id);
        }

        if (StringUtils.isBlank(user.getUserName()) || StringUtils.isBlank(user.getCarUserName())) {
            //初始化用户名
            this.updateInitUserName(user);
        }

//		//根据生成的user表的id 生成mbUserId
//		user.setMbUserId(Constant.MANBANG_DOMAIN_CONST + user.getId());

        this.updateMbUserId(Constant.MANBANG_DOMAIN_CONST + id, id);

        // 发送转帐MQ
        /*
         * MqUserMsg mqUserMsg=new MqUserMsg();
         *
         * mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
         * mqUserMsg
         * .setMessageType(MqBaseMessageBean.MESSAGETYPE_USER_INIT_MESSAGE);
         * mqUserMsg.setUserId(id); //发送初始化用户信息
         * tytMqMessageService.addSaveMqMessage
         * (mqUserMsg.getMessageSerailNum(),JSON.toJSONString(mqUserMsg),
         * mqUserMsg.getMessageType());
         */
        return getById(id);
    }

    @Override
    public void updateVeryCode(Long userId, Integer verifyFlag) throws Exception {
        ((UserDao) this.getBaseDao()).updateVeryCode(userId, verifyFlag);
    }

    @Override
    public void updatePhotoVerifyFlag(Long userId, Date mtime, Integer photoVerifyFlag, Integer userClass, Integer identityType) throws Exception {
        ((UserDao) this.getBaseDao()).updatePhotoVerifyFlag(userId, mtime, photoVerifyFlag, userClass, identityType);

    }

    @Override
    public void updateQQ(Long id) throws Exception {
        ((UserDao) this.getBaseDao()).updateQQ(id);
    }

    @Override
    public void updateInitUserName(User user) throws Exception {
        ((UserDao) this.getBaseDao()).saveInitUserName(user);
    }

    @Override
    public void updateMbUserId(String mbUserId, Long uid) throws Exception {
        ((UserDao) this.getBaseDao()).updateMbUserId(mbUserId, uid);
    }

    @PostConstruct
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, List<TytSource>> getIdentityLables() {
        Map<String, List<TytSource>> map = new HashMap<String, List<TytSource>>();// 返回结果集
        /* 先查询缓存，缓存有直接使用缓存数据，否则查询数据库 */
        Object obj = cacheService.getObject(Constant.CACHE_TYT_SOURCE_PARENT_SUB_KEY_);// 查询缓存
        if (obj != null) {// 缓存有值
            System.out.println("-------------缓存有tyt_source父子关系-----------");
            map = (Map<String, List<TytSource>>) obj;
        } else {// 缓存没值
            long startTime = System.currentTimeMillis();
            /**
             * 1获取父子关系集合
             */
            List<String> parentGroupCode = new ArrayList<String>();// 父级标签集合
            parentGroupCode.add("identity_labels");
            parentGroupCode.add("merchant_type");
            parentGroupCode.add("merchant_service_kind");
            parentGroupCode.add("server_menu");
            List<TytSource> parentList = tytSourceService.getByGroupCode(parentGroupCode);// 获取父级集合，同时包含其子级
            // 初始化Map结果集
            for (String code : parentGroupCode) {
                map.put(code, new ArrayList<TytSource>());

            }
            // 给map结果集赋值
            for (TytSource s : parentList) {
                map.get(s.getGroupCode()).add(s);
            }
            /**
             * 2获取子父关系集合,因为维修时APP端查询需要用子集关联父级
             */
            parentGroupCode.clear();
            parentGroupCode.add("merchant_service_kind_lable");// 子级标签
            List<TytSource> subParentList = tytSourceService.getSubParentList(parentGroupCode);// 子级集合包含其父级
            // 初始化Map结果集
            for (String code : parentGroupCode) {
                map.put(code, new ArrayList<TytSource>());

            }
            // 给map结果集赋值
            for (TytSource s : subParentList) {
                map.get(s.getGroupCode()).add(s);
            }
            //v6000版本 新增 merchant_type_app 车货分离版本 只要展示以下两项 为了兼容老版本 采用以下方法 begin-------
            List<TytSource> tytSources = map.get("merchant_type");
            List<TytSource> merchantTypAppSource = new ArrayList<>();
            for (TytSource tytSource : tytSources) {
                if ("板车维修商".equals(tytSource.getName()) || "板车配件商".equals(tytSource.getName())) {
                    merchantTypAppSource.add(tytSource);
                }
            }
            map.put("merchant_type_app", merchantTypAppSource);
            //v6000版本 新增 merchant_type_app 车货分离版本 只要展示以下两项 为了兼容老版本 采用以下方法 end-------
            /**
             * 3查询结果放缓存24小时
             */
            boolean b = cacheService.setObject(Constant.CACHE_TYT_SOURCE_PARENT_SUB_KEY_, map, Constant.CACHE_EXPIRE_TIME_24H);
            System.out.println("getIdentityLables=" + (System.currentTimeMillis() - startTime) + "--------------缓存tyt_source父子关系" + b + "------------------");
        }
        return map;

    }

    @Override
    public String getHeadURL(String userId) {
        String selSQL = "SELECT u.head_url FROM tyt_user u WHERE u.id=?";
        return this.getBaseDao().query(selSQL, new Object[]{userId});
    }

    @Override
    public void addCellPhoneToTemp(String cellPhone) throws Exception {
        String sql = "INSERT INTO `tyt_cellphone` (`cell_phone`) VALUES(?);";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{cellPhone});
    }

    /**
     * 更新手机号
     *
     * @param changeCellPhone
     * @param cellPhone
     * @throws Exception
     */
    @Override
    public void updateCellPhone(String changeCellPhone, String cellPhone) throws Exception {
        String sql = "update `tyt_cellphone` set `cell_phone` =?  where cell_phone=? ";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{changeCellPhone, cellPhone});
    }

    @Override
    public Map<String, Object> getCollectList(Long userId, String type, Integer currentPage, String status) {
        Map<String, Object> result = new HashMap<String, Object>();
        // 查询总条数
        int totalRecord = collectService.getInfoIdsCountsByType(userId + "", type, status);
        // 当前页面
        if (currentPage <= 0)
            currentPage = 1;
        // 每页条数
        Integer pageSize = AppConfig.getIntProperty("tyt.collect.query.page.size");
        if (pageSize == null)
            pageSize = 15;
        // 总页数
        int pageNumber = (totalRecord + pageSize - 1) / pageSize;
        result.put("pageSize", pageSize);
        result.put("maxPage", pageNumber);
        result.put("currentPage", currentPage);
        if (currentPage <= pageNumber) {
            // 查询满足条件的id集合
            List<Long> ids = collectService.getInfoIdsByType(userId + "", type, status, currentPage, pageSize);
            if (ids != null && ids.size() > 0) {
                switch (type) {
                    case "2":// 大板车求职收藏
                        result.put("data", bCarJobService.getBcarJobListByIds(ids, currentPage));
                        break;
                    case "3":// 大板车招聘收藏
                        result.put("data", bCarRecruitService.getBcarJobListByIds(ids, currentPage));
                        break;
                    case "4":// 设备求职
                        result.put("data", sCarJobService.getScarJobListByIds(ids, currentPage));
                        break;
                    case "5":// 设备招聘
                        result.put("data", sCarRecruitService.getScarRecruitListByIds(ids, currentPage));
                        break;
                }

            }
        }
        return result;
    }

    @Override
    public void addModuleUse(String userId, String name, String clientId, String osVersion, String clientSign, String type, String clientVersion) {
        String sql = "INSERT INTO `tyt`.`tyt_app_module_use` (`module_name`, `client_id`, `os_version`, `terminal`, `operate_time`, `userId` ,`type` ,`client_version`) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        String terminal = "未知";
        switch (Integer.valueOf(clientSign)) {
            case 1:
                terminal = "PC";
                break;
            case 2:
                terminal = "ANDROID";
                break;
            case 3:
                terminal = "IOS";
                break;
            case 4:
                terminal = "APAD";
                break;
            case 5:
                terminal = "IPAD";
                break;
            case 6:
                terminal = "WEB";
                break;
        }
        this.getBaseDao().executeUpdateSql(sql, new Object[]{name, clientId, osVersion, terminal, new Date(), userId, type, clientVersion});
    }

    @Override
    public boolean isNeedNotifyGoods(Long userId) throws Exception {
        return false;
    }

    @Override
    public void addUserAccount(Long userId, int accountType) {
        String sql = "INSERT INTO `tyt`.`tyt_user_account` ( `user_id`, `current_balance`, `affiliated_type`, `create_time`, `update_time`) values ( ?,?,?,?,? )";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{userId, 0, accountType, new Date(), new Date()});
    }

    @Override
    public Integer getPhotoVerifyFlag(Long userId) throws Exception {
        User user = getByUserId(userId);
        Integer verifyPhotoSign = user.getVerifyPhotoSign();
        if (verifyPhotoSign == null) {
            verifyPhotoSign = 0;
        }
        return verifyPhotoSign;
    }

    @Override
    public Integer getPhotoVerifyFlag(User user) {
        Integer verifyPhotoSign = user.getVerifyPhotoSign();
        if (verifyPhotoSign == null) {
            verifyPhotoSign = 0;
        }
        return verifyPhotoSign;
    }

    @Override
    public ResultMsgBean saveGoodsCheck(Long userId, String clientVersion, Integer clientSign) throws Exception {
        /**
         * 1身份认证检查 2发布状态检查 3剩余发布条数检查
         */
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        logger.info("》》》》》plat  method saveGoodsCheck:{}【开始】", userId);
        /** 身份认证检测，只是从某个版本（tyt_config表配置）开始的才有，其他的没有这项检测 */
        // 从tyt_config表获取最高版本控制线
        Integer clientVersionInt = Integer.parseInt(clientVersion);
        Integer notCheckMaxVersionInt = null;
        if (clientSign == 2) {
            notCheckMaxVersionInt = tytConfigService.getIntValue("notCheckAndroidMaxVersionLinePhotoVerify");
        } else if (clientSign == 3) {
            notCheckMaxVersionInt = tytConfigService.getIntValue("notCheckIosMaxVersionLinePhotoVerify");
        }
        if (notCheckMaxVersionInt == null) {
            notCheckMaxVersionInt = 3100;
        }
        if (clientVersionInt <= notCheckMaxVersionInt) {
            logger.info("》》》》》plat  method saveGoodsCheck:{}此版本的客户端不用检测身份,$$$$$$$$$$【clientVersionInt:{}】", userId, clientVersionInt);
        } else {
            Integer verifyPhotoSign = this.getPhotoVerifyFlag(userId);
            // 0未认证1通过2认证中3认证失败
            logger.info("》》》》》plat  method saveGoodsCheck:{}$$$$$$$$$$【verifyPhotoSign:{}】", userId, verifyPhotoSign);
            if (verifyPhotoSign < 0 || verifyPhotoSign > 3)
                verifyPhotoSign = 0;// 非法值
            if (verifyPhotoSign == 0) {
                resultMsgBean.setCode(11001);
                resultMsgBean.setMsg(propertiesFileUtil.getString("tyt.user.identity.auth.no.tip.11001"));
                return resultMsgBean;
            } else if (verifyPhotoSign == 2) {
                resultMsgBean.setCode(11002);
                resultMsgBean.setMsg(propertiesFileUtil.getString("tyt.user.identity.auth.wait.tip.11002"));
                return resultMsgBean;
            } else if (verifyPhotoSign == 3) {
                resultMsgBean.setCode(11003);
                resultMsgBean.setMsg(propertiesFileUtil.getString("tyt.user.identity.auth.failure.tip.11003"));
                return resultMsgBean;
            }
        }

        User user = this.getByUserId(userId);
        /** 有没有发布权限检测 */
        if (user.getInfoPublishFlag() == User.INFO_PUBLISH_DISABLE) {
            resultMsgBean.setCode(ReturnCodeConstant.INFO_REFUSE_UPLOAD_CODE);
            resultMsgBean.setMsg("您的上传权限未开通，请联系客服。");
            return resultMsgBean;
        }
        if (clientVersionInt <= notCheckMaxVersionInt) {
            /** 判断用户是否可以发货，发布条数 */
            if (!tytUserSubService.isSendGoods(userId)) {
                resultMsgBean.setCode(ReturnCodeConstant.MORE_THAN_LIMIT);
                resultMsgBean.setMsg(AppConfig.getProperty("goods.limit.prompt"));
                return resultMsgBean;
            }
        }
        logger.info("》》》》》plat  method saveGoodsCheck:{}【结束】", userId);
        resultMsgBean.setCode(ReturnCodeConstant.OK);
        resultMsgBean.setMsg("可以正常发货");
        return resultMsgBean;
    }

    @Override
    public void saveOneLevelIdentity(String userId, Integer sourceUserClassValue, Integer userIdentityTypeValue) throws IllegalAccessException, InvocationTargetException {
//        String sql = "UPDATE tyt_user SET user_class=?, identity_type=?, verify_photo_sign=? WHERE id=?";
//        this.getBaseDao().executeUpdateSql(sql, new Object[]{sourceUserClassValue, userIdentityTypeValue, 0, userId});
        // 获取缓存中的用户信息
        User user = (User) cacheService.getObject(Constant.CACHE_USER_KEY + userId);
        /*
         * 查询tyt_user_identity_auth表时候已经有认证记录，如果没有则直接添加，
         * 如果有则将当前信息记录到tyt_user_identity_auth_log 表记录一次认证日志，然后更新当前的认证记录为初始化的状态
         */
        String sql = "SELECT COUNT(*) FROM tyt_user_identity_auth WHERE user_id=?";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{userId});
        if (count.intValue() == 0) {
            TytUserIdentityAuth userIdentityAuth = generateUserIdentity(userId, sourceUserClassValue, userIdentityTypeValue, user);
            identityAuthService.add(userIdentityAuth);
        } else {
            // 查询用户的认证信息
            TytUserIdentityAuth userIdentityAuth = identityAuthService.getByUserId(userId);
            if (userIdentityAuth != null) {
                // 更新用户身份认证为初始化状态
                userIdentityAuth.init(sourceUserClassValue, userIdentityTypeValue);
                identityAuthService.update(userIdentityAuth);
            }
        }
        if (user != null) {
            user.setIdentityType(userIdentityTypeValue);
            user.setUserClass(sourceUserClassValue);
            cacheService.del(Constant.CACHE_USER_KEY + userId);
            cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
        }
    }

    @Override
    public void saveOneLevelIdentityNew(String userId, Integer sourceUserClassValue, Integer userIdentityTypeValue) throws IllegalAccessException, InvocationTargetException {
        String sql = "UPDATE tyt_user SET user_class=?, identity_type=?, verify_photo_sign=? WHERE id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{sourceUserClassValue, userIdentityTypeValue, 0, userId});
        // 获取缓存中的用户信息
        User user = (User) cacheService.getObject(Constant.CACHE_USER_KEY + userId);
        /*
         * 查询tyt_user_identity_auth表时候已经有认证记录，如果没有则直接添加，
         * 如果有则将当前信息记录到tyt_user_identity_auth_log 表记录一次认证日志，然后更新当前的认证记录为初始化的状态
         */
        sql = "SELECT COUNT(*) FROM tyt_user_identity_auth WHERE user_id=?";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{userId});
        if (count.intValue() == 0) {
            TytUserIdentityAuth userIdentityAuth = generateUserIdentity(userId, sourceUserClassValue, userIdentityTypeValue, user);
            identityAuthService.add(userIdentityAuth);
        } else {
            // 查询用户的认证信息
            TytUserIdentityAuth userIdentityAuth = identityAuthService.getByUserId(userId);
            if (userIdentityAuth != null) {
                // 更新用户身份认证为初始化状态
                // userIdentityAuth.init(sourceUserClassValue, userIdentityTypeValue);
                identityAuthService.update(userIdentityAuth);
            }
        }
        if (user != null) {
            user.setIdentityType(userIdentityTypeValue);
            user.setUserClass(sourceUserClassValue);
            cacheService.del(Constant.CACHE_USER_KEY + userId);
            cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
        }
    }

    private TytUserIdentityAuth generateUserIdentity(String userId, Integer sourceUserClassValue, Integer userIdentityTypeValue, User user) {
        TytUserIdentityAuth userIdentityAuth = new TytUserIdentityAuth();
        userIdentityAuth.setUserId(Long.valueOf(userId));
        userIdentityAuth.setMobile(user == null ? "" : user.getCellPhone());
        userIdentityAuth.setUserClass(sourceUserClassValue);
        userIdentityAuth.setIdentityType(userIdentityTypeValue);
        return userIdentityAuth;
    }

    @Override
    public BigInteger getVIPCount() {
        String sql = "SELECT COUNT(*) FROM tyt_user where user_type =1";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{});
        return count;
    }

    @Override
    public void updateUserServicePeriod(Date endTime, String userId, String cellPhone, Integer serverDays) {
        Date today = new Date();
        if (endTime == null)
            endTime = new Date();
        String newEndTime = TimeUtil.formatDateTime(TimeUtil.stampAdd(TimeUtil.getTimeStamp(), serverDays));
        String todayTime = TimeUtil.formatDateTime(new Date());
        if (today.after(endTime)) {// 添加记录
            this.addUserServicePeriod(userId, todayTime, newEndTime, cellPhone);
        } else {// 修改记录
            // 查找tyt_user_service_period表该用户的最近一条记录
            Integer id = this.getLatestUserServicePeriod(userId);
            if (id != null) {
                this.updateServiceEndTime(id + "", newEndTime);
            }
        }

    }

    public void addUserServicePeriod(String userId, String serviceStartTime, String serviceEndTime, String cellPhone) {
        String conditionSQL = "INSERT INTO tyt_user_service_period(`user_id`, `service_end_time`, `service_start_time`, `cell_phone`) VALUE (:userId, :serviceEndTime, :serviceStartTime, :cellPhone)";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("userId", userId);
        paramMap.put("serviceStartTime", serviceStartTime);
        paramMap.put("serviceEndTime", serviceEndTime);
        paramMap.put("cellPhone", cellPhone);
        this.getBaseDao().executeUpdateSql(conditionSQL, paramMap);
    }

    public Integer getLatestUserServicePeriod(String userId) {
        String conditionSQL = "SELECT id FROM tyt_user_service_period  WHERE user_id=? ORDER BY update_time DESC LIMIT 1";
        Integer id = this.getBaseDao().query(conditionSQL, new Object[]{userId});
        return id;
    }

    public void updateServiceEndTime(String id, String endDate) {
        String conditionSQL = "UPDATE tyt_user_service_period SET service_end_time=:endDate WHERE id=:id";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("id", id);
        paramMap.put("endDate", endDate);
        this.getBaseDao().executeUpdateSql(conditionSQL, paramMap);
    }

    @Override
    public void updateExtralDays(User user, Integer extralDays, String note) {
        try {
            // 剩余天数
            String sql = "update tyt_user set serve_days=?,phone_serve_days=?," + "end_time=?,mtime=?,extra_days=extra_days+?,home_phone=? where id=?";
            List<Object> params = new ArrayList<Object>();
            params.add(user.getServeDays() + extralDays);
            params.add(user.getServeDays() + extralDays);
            params.add(TimeUtil.formatDateTime(TimeUtil.stampAdd(TimeUtil.stampAdd(TimeUtil.getTimeStamp(), user.getServeDays()), extralDays)));
            // 今天+剩余天数换算成年+年限
            params.add(TimeUtil.formatDateTime(new Date()));
            params.add(extralDays);
            params.add(note);
            params.add(user.getId());
            // 执行数据库更新
            this.executeUpdateSql(sql, params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

    }

    @Override
    public void queryByCellphone(String cellPhone, ResultMsgBean rm) {
        Map<String, QueryByPhoneResponse> resultMap = new HashMap<String, QueryByPhoneResponse>();
        User user;
        String[] cellPhoneArr = cellPhone.split("\\|");
        String curCellPhone;
        QueryByPhoneResponse curRes;
        for (int i = 0; i < cellPhoneArr.length; i++) {
            user = new User();
            curCellPhone = cellPhoneArr[i];
            user = getUserByCell(curCellPhone);
            curRes = new QueryByPhoneResponse();
            if (user == null) {
                curRes.setIsRegister("2");
            } else {
                curRes.setIsRegister("1");
                curRes.setUserId(user.getId());
                curRes.setUserName(user.getTrueName());
                curRes.setUserAvatar(user.getHeadUrl());
                // 查询认证车辆数
                curRes.setAuthCarNum(carService.getCountByUserId(user.getId()).intValue());
            }
            resultMap.put(curCellPhone, curRes);
        }
        logger.info("queryByPhone queryByPhoneRes: " + resultMap);
        rm.setData(resultMap);
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
    }

    @Override
    public RightsInfoBean getRightsInfo(Long userId) throws IllegalAccessException, InvocationTargetException {
        RightsInfoBean infoBean = null;
        List<TytBubble> bubbles = bubbleService.getNewBubbleForUserId(userId);
        if (bubbles.size() > 0) {
            infoBean = new RightsInfoBean();
            BeanUtils.copyProperties(infoBean, bubbles.get(0));
        }
        return infoBean;
    }

    /**
     * 发送异常登录的短信提醒
     *
     * @param userId
     */
    public void sendExceptionLoginSms(Long userId) {
        User user = null;
        try {
            user = this.getByUserId(userId);
        } catch (Exception e) {
            logger.error("userId:" + userId + "用户查询异常", e);
            return;
        }
        if (user == null) {
            logger.info("userId:" + userId + "用户不存在");
            return;
        }
        ShortMsgBean shortMsgBean = new ShortMsgBean();
        // 根据短信key获取短信模板
        String content = messageTmplService.getSmsTmpl(EXCEPTION_LOGIN_MSG_TEMPLATE_KEY, "您的账号${cellPhone},已经在其他设备上登录。如非您本人授权操作，请及时更改登录密码，或联系客服400112443");
        shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
        String messageSerailNum = SerialNumUtil.generateSeriaNum();
        shortMsgBean.setMessageSerailNum(messageSerailNum);
        shortMsgBean.setCell_phone(user.getCellPhone());
        shortMsgBean.setRemark("");
        String cellPhone = StringUtil.replaceMobile(user.getCellPhone()); // 隐藏后四位手机号
        String applySucesscontent = StringUtils.replaceEach(content, new String[]{"${cellPhone}"}, new String[]{cellPhone});
        shortMsgBean.setContent(applySucesscontent);
        String messageContent = JSON.toJSONString(shortMsgBean);
        tytMqMessageService.addSaveMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
        tytMqMessageService.sendMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
    }

    @Override
    public String getCellPhoneById(Long userId) throws Exception {
        User user = this.getByUserId(userId);
        return user.getCellPhone();
    }


    @Override
    public ResultMsgBean checkUserPhotoVerifyFlag(Long userId, String clientSign) throws Exception {
        // 照片认证标志0未认证1通过2认证中3认证失败
        Integer verifyPhotoSign = this.getPhotoVerifyFlag(userId);
        if (verifyPhotoSign != null && verifyPhotoSign == 1) {

            return new ResultMsgBean(ReturnCodeConstant.OK, "");
        }

        TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(userId);
        Integer maxPublishNum = tytConfigService.getIntValue("user_max_publish_num", 3);
        if (Objects.isNull(tytUserSub.getPublishNum()) || tytUserSub.getPublishNum() < maxPublishNum) {
            return new ResultMsgBean(ReturnCodeConstant.OK, "");
        }
//        //提示认证中提示语
        if (verifyPhotoSign == 2) {
            ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NO_PERMISSION, "认证中请等待");
            msgBean.setNoticeData(tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.身份认证中提示, null));

            return msgBean;
        }
        // 提示认证弹窗
        ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NO_PERMISSION, "请进行身份认证");
        TytNoticePopupTempl noticePopupTempl;
        if (Constant.ClientSignNewEnum.isClientSignEnumcode(Integer.parseInt(clientSign))) {
            noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.未实名认证, null);
        } else {
            noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.未认证身份, null);
        }
        msgBean.setNoticeData(noticePopupTempl);
        return msgBean;


    }


    @Override
    public ResultMsgBean checkUserPhotoVerifyFlag(User user, String clientSign) throws Exception {
        // 照片认证标志0未认证1通过2认证中3认证失败
        Integer verifyPhotoSign = this.getPhotoVerifyFlag(user);
        if (verifyPhotoSign != null && verifyPhotoSign == 1) {

            return new ResultMsgBean(ReturnCodeConstant.OK, "");
        }
        //TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(user.getId());
        Integer userPublishNum = RedisUtil.getObject(Constant.MONTH_PUBLISH_NUM + user.getId());
        Integer maxPublishNum = tytConfigService.getIntValue("user_max_publish_num", 3);
        int userSubPublishNum = Objects.isNull(userPublishNum) ? 0 : userPublishNum;
        if (userSubPublishNum < maxPublishNum) {
            return new ResultMsgBean(ReturnCodeConstant.OK, "");
        }
//		//提示认证中提示语
//		if (verifyPhotoSign == 2) {
//			ResultMsgBean msgBean=new ResultMsgBean(ReturnCodeConstant.NO_PERMISSION,"认证中请等待");
//			msgBean.setNoticeData(tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.身份认证中提示,null));
//
//			return msgBean;
//		}
//		// 提示认证弹窗
//		ResultMsgBean msgBean=new ResultMsgBean(ReturnCodeConstant.NO_PERMISSION,"请进行身份认证");
//		TytNoticePopupTempl noticePopupTempl;
//		if (Constant.ClientSignNewEnum.isClientSignEnumcode(Integer.parseInt(clientSign))){
//			noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.未实名认证, null);
//		}else{
//			noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.未认证身份, null);
//		}
//		msgBean.setNoticeData(noticePopupTempl);
        return new ResultMsgBean(ReturnCodeConstant.USER_UNVERIFIED, "请认证后重试。");


    }


    @Override
    public ResultMsgBean checkUserPublishTransportPermission(Long userId, String price) {
        PermissionResult permissionResult = null;
        if (StringUtils.isNotBlank(price)) {
            permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.有价货源发布, userId);
        } else {
            permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货源发布, userId);
        }
        if (permissionResult.getUse()) {
            ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "");
            resultMsgBean.setData(permissionResult.getUsedRecordId());
            return resultMsgBean;
        }
        ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NO_PERMISSION, "");
        msgBean.setNoticeData(tytNoticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(), null));
        return msgBean;
    }

    @Override
    public UserDetailByCellphone getUserCredit(Long userId, String queryPhone, User queryUser) {

        UserDetailByCellphone userDetail = null;
        //根据手机号查询对象
        if (queryUser != null) {
            userDetail = new UserDetailByCellphone();

            userDetail.setUserId(queryUser.getId());
            userDetail.setCellPhone(queryPhone);
            userDetail.setHeadUrl(queryUser.getHeadUrl());
            userDetail.setCarUserName(queryUser.getCarUserName());

            List<String> abTestCode = new ArrayList<>();
            abTestCode.add("not_show_transport_user_data");
            TytAbtestConfigVo userType = abtestService.getUserTypeList(abTestCode, userId).get(0);
            if (userType.getType() == 1) {
                userDetail.setUserName("特运通老板");
            } else {
                userDetail.setUserName(queryUser.getUserName());
            }

            userDetail.setProvince(queryUser.getProvince());
            userDetail.setCity(queryUser.getCity());
            userDetail.setUserType(queryUser.getUserType());
            userDetail.setUserClass(queryUser.getUserClass());
            userDetail.setCtime(queryUser.getCtime());
            userDetail.setRegisterDay(TimeUtil.daysBetween(queryUser.getCtime(), new Date()));
            userDetail.setIdentityType(queryUser.getIdentityType());
            userDetail.setDeliverTypeOne(queryUser.getDeliver_type_one());
            userDetail.setBlackStatus(queryUser.getBlackStatus());
            // 查询官方授权昵称
            String authNameTea = tytOwnerAuthService.getAuthNameTea(queryUser.getId());
            if (StringUtils.isNotBlank(authNameTea)) {
                userDetail.setAuthNameTea(authNameTea);
            }
            if (queryUser.getBlackStatus() != null && queryUser.getBlackStatus() == 1) {
                userDetail.setRiskPrompt(2);
            } else {
                userDetail.setRiskPrompt(1);
            }
            //根据用户id和用户分类查询用户会员信息
            if (queryUser.getUserClass() != null) {
                //由于用户信息userclass字段有的为空，所以先定义一个空的字符串
                String typeId = "";
                //根据userclass值设置字符串
                if (queryUser.getUserClass() == 1) {
                    typeId = "100201";
                }
                if (queryUser.getUserClass() == 2) {
                    //如果用户信息为车辆方，更改为车会员id
                    typeId = "100101";
                }
                if (StringUtils.isNotBlank(typeId)) {
                    UserPermission userPermission = userPermissionService.getUserPermission(queryUser.getId(), typeId);
                    if (null != userPermission) {
                        userDetail.setUserType(userPermission.getStatus());

                    }
                }
            }


            TytUserIdentityAuth queryUserIdentity = userIdentityService.getTytUserIdentityAuth(queryUser.getId());
            if (queryUserIdentity != null) {
                if (queryUserIdentity.getEnterpriseAuthStatus() != null && queryUserIdentity.getEnterpriseAuthStatus() == 1) {
                    userDetail.setLicenseState(1);
                } else {
                    userDetail.setLicenseState(0);
                }
                if (queryUserIdentity.getInfoStatus() != null && queryUserIdentity.getInfoStatus() == 1) {
                    userDetail.setIdentityState(1);
                } else {
                    userDetail.setIdentityState(0);
                }
                userDetail.setExamineStatus(queryUserIdentity.getExamineStatus());
            } else {
                userDetail.setLicenseState(0);
                userDetail.setIdentityState(0);
            }
            userDetail.setCarNum(carService.getCountByUserId(queryUser.getId()).longValue());
            BigInteger count = blockInfoService.getBlockInfoCount(queryPhone);
            if (count.intValue() > 0) {
                userDetail.setFraudRecord(1);
            } else {
                userDetail.setFraudRecord(0);
            }
            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(queryUser.getId());
            if (userCreditInfo != null) {
                //信用等级
                if (null != userCreditInfo.getRankLevel()) {
                    userDetail.setTsRank(userCreditInfo.getRankLevel());
                }
                //信用分
                if (userCreditInfo.getTotalScore() != null) {
                    userDetail.setTotalScore(userCreditInfo.getTotalScore());
                }
                //是否黑名单
                if (userCreditInfo.getIsBlacklistUser() != null) {
                    userDetail.setBlackStatus(userCreditInfo.getIsBlacklistUser());
                }
                if (userCreditInfo.getFraudCount() == null || userCreditInfo.getFraudCount() == 0) {
                    userDetail.setFraudRecord(0);
                } else {
                    userDetail.setFraudRecord(1);
                }
                userDetail.setDeliverAmount(userCreditInfo.getTransport145Count());
                userDetail.setVolume(userCreditInfo.getTransportSuccCount());
                userDetail.setInfofeeNum(userCreditInfo.getTransportInformationPaymentCount());
                userDetail.setVipNum(userCreditInfo.getVipGrowthDays());
                //V6220 车方服务分
                if (null != userCreditInfo.getCarTotalServerScore()) {
                    userDetail.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
                }
                if (null != userCreditInfo.getCarServerRankScore()) {
                    userDetail.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
                }
                if (null != userCreditInfo.getCarVipGrowthDays()) {
                    userDetail.setCarVipGrowthDays(userCreditInfo.getCarVipGrowthDays());
                }
                if (null != userCreditInfo.getCarTransportInformationPaymentCount()) {
                    userDetail.setCarTransportInformationPaymentCount(userCreditInfo.getCarTransportInformationPaymentCount());
                }


            } else {
                userDetail.setInfofeeNum(0L);
                userDetail.setVipNum(0L);
                userDetail.setDeliverAmount(0L);
                userDetail.setVolume(0L);
                userDetail.setTsRank(1);
                userDetail.setTotalScore(new BigDecimal(0));
                userDetail.setCarTotalServerScore(0);
                userDetail.setCarServerRankScore(1);
                userDetail.setCarVipGrowthDays(0L);
                userDetail.setCarTransportInformationPaymentCount(0L);
            }
            //用户违约次数
            TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(queryUser.getId());
            if (tytUserSub != null && tytUserSub.getCarBreakNum() != null && tytUserSub.getCarBreakNum() > 0) {
                userDetail.setCarBreakNum(tytUserSub.getCarBreakNum());
            } else {
                userDetail.setCarBreakNum(0);
            }

            if (tytUserSub != null && tytUserSub.getGoodsBreakNum() != null && tytUserSub.getGoodsBreakNum() > 0) {
                userDetail.setGoodsBreakNum(tytUserSub.getGoodsBreakNum());
            } else {
                userDetail.setGoodsBreakNum(0);
            }
        }
        return userDetail;
    }

    /**
     * 查询用户列表
     *
     * @param userIds
     * @return
     */
    @Override
    public List<User> getUserListByIds(String userIds) {
        String sql = "select * from tyt_user where id in (" + userIds + ") ";
        List<User> list = this.getBaseDao().queryForList(sql, new Object[]{});
        return list;
    }

    @Override
    public void openacctInactiveApply(Long userId) {

        UserAcctStatusBean userAcctStatusBean = this.getAcctStatus(userId);
        if (userAcctStatusBean == null || !("NORMAL").equals(userAcctStatusBean.getUserStatus())) {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("userId", userId);
            String result = HttpUtil.get(userCenterUrl + "/openacct/inactive/apply", paramMap);
            logger.info("登录待激活开户信息结果:【{}】", result);
        }
    }

    /**
     * 获取用户开户状态
     *
     * @param userId
     * @return
     */
    private UserAcctStatusBean getAcctStatus(long userId) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("userId", userId);
        String result = HttpUtil.get(userCenterUrl + "/openacct/status/get", paramMap);
        logger.info("获取开户信息字符串结果:【{}】", result);
        ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
        logger.info("获取开户信息结果:【{}】", resultMsgBean);
        if (ResultMsgBean.OK != resultMsgBean.getCode()) {
            return null;
        }

        UserAcctStatusBean userAcctStatusBean = new UserAcctStatusBean();
        if (resultMsgBean.getData() != null) {
            userAcctStatusBean = JSON.parseObject(resultMsgBean.getData().toString(), UserAcctStatusBean.class);
        }
        return userAcctStatusBean;
    }


    @Override
    public void updateDispatchStatus(User user, int dispatchStatus) {
        try {
            Long userId = user.getId();
            String sql = "update tyt_user set is_dispatch=?,mtime=? where id=?";
            final Object[] params = {
                    dispatchStatus, // 设置为同意成为专车用户
                    new Date(),
                    userId
            };
            // 执行数据库更新
            this.executeUpdateSql(sql, params);
            logger.info("接收调度系统请求，用户{}成为调度用户状态更新为同意", userId);
            // 更新用户下所有车辆成为调度车
            carService.updateDispatchStatus(userId, dispatchStatus);
            logger.info("接收调度系统请求，用户{}名下所有车辆更新为调度车", userId);
        } catch (Exception e) {
            logger.error("更新用户调度状态异常", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean versionLoginAllow(Map<String, String> params) {
        String clientVersionStr = params.get("clientVersion");

        int clientVersion = 0;
        if (StringUtils.isBlank(clientVersionStr)) {
            logger.warn("app_login_version_is_blank!");
        } else {
            clientVersion = Integer.parseInt(clientVersionStr);
        }

        //老的短信登录校验逻辑
        //Integer versionControl = tytConfigService.getIntValue(Constant.LOGIN_VERSION_CONTROL,1000);
        if (clientVersion < 6000) {
            return false;
        }
        return true;
    }

    @Override
    public TytNoticePopupTempl checkVersionLogin(HttpServletRequest request, Map<String, String> params) {
        TytNoticePopupTempl noticePopupTempl = null;

        String clientVersionStr = params.get("clientVersion");
        //int clientVersion = Integer.parseInt(clientVersionStr);

        String clientSign = params.get("clientSign");

        //校验车版强升
        if (TytSwitchUtil.isCar6140UpgradeOn()) {
            if ("21".equals(clientSign)) {
                //安卓车版强制升级校验
                String carVersions = tytConfigService.getStringValue(Constant.force_car_upgrade_version, "");

                if (StringUtils.isNotBlank(carVersions)) {
                    String[] versionArray = carVersions.split(",");
                    Set<String> versionSet = Arrays.stream(versionArray).collect(Collectors.toSet());
                    if (versionSet.contains(clientVersionStr)) {
                        noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.车主版6140_6141_6142升级弹窗, null);
                        return noticePopupTempl;
                    }
                }
            }
        }

        //校验货版强升
        if (tytConfigService.isEqualsOne(Constant.force_goods_upgrade)) {
            if ("22".equals(clientSign)) {
                //安卓货版强制升级校验
                String goodsVersions = tytConfigService.getStringValue(Constant.force_goods_upgrade_version, "");

                if (StringUtils.isNotBlank(goodsVersions)) {
                    String[] versionArray = goodsVersions.split(",");

                    Set<String> versionSet = Arrays.stream(versionArray).collect(Collectors.toSet());
                    if (versionSet.contains(clientVersionStr)) {
                        noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.货站版网页强制升级弹窗, null);
                        return noticePopupTempl;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public ResultMsgBean newLogin(HttpServletRequest request, Map<String, String> params) throws Exception {
        /* 取相关参数 */
        String cellPhone = params.get("cellPhone");
        String password = params.get("password");
        String clientSign = params.get("clientSign");
        String clientId = params.get("clientId");
        String userVersion = params.get("clientVersion");
        String condition = cellPhone;
        String channelCode = params.get("channelCode");// 区分IOS是企业版还是个人版

        ResultMsgBean result = new ResultMsgBean();
        /* 判断用户是否属于强制升级的用户 */
        Map<String, Object> upgradeStrategyMap = tytForceUpgradeUserService.getForceUpgradeVersionInfo(2, cellPhone, null, clientId, channelCode, clientSign, userVersion);
        if (upgradeStrategyMap != null && "1".equals(upgradeStrategyMap.get("type"))) {
            logger.info(condition + "软件版本低,属于强制升级的用户");
            return buildLoginResult(result, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", upgradeStrategyMap);
        }

        /* 根据手机号查询用户 */
        User user = getUserByCellphone(cellPhone);
        //马甲用户登陆提示同未注册
        if (user != null && user.getIsMock() == 1) {
            user = null;
        }
        // 是否需要验证登录失败次数的标记
        boolean isNeedRestricLoginFail = false;
        String todayDate = TimeUtil.formatDate(new Date());
        try {
            // 获取需要进行登录错误次数控制的最小版本（不包括）
            String restriceLoginFailMinVersion = tytConfigService
                    .getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5301");
            int restriceLoginFailMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
            int clientVersionInt = Integer.valueOf(userVersion).intValue();
            isNeedRestricLoginFail = (clientVersionInt > restriceLoginFailMinVersionInt);
        } catch (Exception e) {
        }

        // 判断手机号存在否？
        if (user == null) {
            logger.error(condition + "手机号尚未注册");
            // 设置手机号未注册状态码供客户端使用
            if (isNeedRestricLoginFail) {
                result.setCode(10000);
                result.setMsg("手机号尚未注册");
            } else {
                result.setCode(ReturnCodeConstant.LOGIN_ERROR_CODE);
                result.setMsg("手机号尚未注册");
            }
            return result;
        }
        //查询用户是否存在处罚订单
        BlacklistUserOrders blacklistUserOrders = blacklistUserOrdersService.getOrdersByUserIdAndRestrictTime(user.getId(), new Date());
        if (blacklistUserOrders != null) {
            Long waybillExId = blacklistUserOrders.getWaybillExId();
            //查询异常上报订单信息
            TytTransportWaybillEx transportWaybillEx = transportWayBillExService.getExById(waybillExId);
            if (transportWaybillEx != null) {
                //运单号
                String tsOrderNo = transportWaybillEx.getTsOrderNo();
                //出发地(省市区以减号-分割开)
                String startPoint = transportWaybillEx.getStartPoint();
                //目的地(省市区以减号-分割开)
                String destPoint = transportWaybillEx.getDestPoint();
                //货物内容
                String taskContent = transportWaybillEx.getTaskContent();
                if (StringUtils.isNotBlank(taskContent) && taskContent.length() > 6) {
                    taskContent = taskContent.substring(0, 6) + "···";
                }
                //替换参数,逗号分隔
                String masterParams = tsOrderNo + "," + startPoint + "," + destPoint + "," + taskContent;

                TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号管制通知_订单, masterParams);
                result.setNoticeData(noticePopupTempl);
                return buildLoginResult(result, ReturnCodeConstant.NO_PERMISSION, null, user);
            }
        }

        // 判断用户黑名单否？
        // if (user.getBlackStatus() == User.BLACKLIST_STATUS_Y) {
        // 	BlacklistUser blacklistUser = blacklistUserService.getBlackByUserId(user.getId());
        //     String causeName = "账号未激活，请联系客服";
        // 	if(blacklistUser != null) {
        //         logger.error("userId为"+ user.getId() +"的账号在黑名单");
        //         causeName = "您的帐号存在行为异常，请联系客服";
        // 	}
        // 	// 设置账号未激活状态码供客户端使用
        // 	if (isNeedRestricLoginFail) {
        // 		result.setCode(10001);
        // 		result.setMsg(causeName);
        // 		return buildLoginResult(result,10001,causeName,null);
        // 	} else {
        // 		logger.error(condition + "账号在黑名单");
        // 		return buildLoginResult(result,ReturnCodeConstant.LOGIN_ERROR_CODE,causeName,null);
        // 	}
        // }
        BlacklistUser blacklistUser = blacklistUserService.getBlackByUserIdAndStatus(user.getId(), 1);
        if (user.getBlackStatus() == User.BLACKLIST_STATUS_Y && blacklistUser != null) {
            TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号管制通知_拉黑, null);
            result.setNoticeData(noticePopupTempl);
            return buildLoginResult(result, ReturnCodeConstant.NO_PERMISSION, null, user);
        }

        //mac地址是否拉黑
        if (blacklistUserService.isBlacklistMac(clientId)) {
            logger.info("mac black : {}", clientId);
            return buildLoginResult(result, ReturnCodeConstant.LOGIN_ERROR_CODE, "设备限制登录", user);
        }

        String redisKey = Constant.LOGIN_FAIL_TIME_REDIS_KEY + cellPhone + todayDate;
        int cacheSeconds = (int) TimeUtil.getTomorrowZeroSeconds();
        // 如果是需要限制登录次数的版本则判断用户当天的登录次数
        if (isNeedRestricLoginFail) {
            String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
            String verifyOk = RedisUtil.get(verifyOkKey);
            if (org.apache.commons.lang.StringUtils.isEmpty(verifyOk) || !"1".equals(verifyOk)) {
                // 获取当前登录失败的次数，如果达到十次则返回控制客户端弹出验证码
                String loginFailTime = RedisUtil.get(redisKey);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(loginFailTime)) {
                    if (Integer.valueOf(loginFailTime)
                            .intValue() >= Constant.PASSWORD_ERROR_SHOW_CODE_TIME) {
                        return buildLoginResult(result, 10004, "密码错误次数超过10次", null);
                    }
                }
            }
        }

        // 判断密码正确否？
        if (!password(user, password)) {
            // 设置密码错误状态码供客户端使用
            if (isNeedRestricLoginFail) {
                String loginFailTime = RedisUtil.get(redisKey);
                // 如果当前没有失败次数则记录失败次数为1次,
                // 否则判断登录失败的次数是否到达5次，如果到达5次，则返回引导客户端弹出重置密码框状态码
                if (org.apache.commons.lang.StringUtils.isEmpty(loginFailTime)) {
                    RedisUtil.set(redisKey, "1", cacheSeconds);
                } else {
                    // 增加登录失败次数
                    RedisUtil.incr(redisKey);
                    int loginFailTimeInt = Integer.valueOf(loginFailTime).intValue();
                    loginFailTimeInt = loginFailTimeInt + 1;
                    // 登录失败次数到达5次引导客户端弹出重置密码框状态码
                    if (loginFailTimeInt >= Constant.PASSWORD_ERROR_SHOW_MODIFY_TIME) {
                        // 如果大于10次则说明是用户成功输入了验证码，再次密码错误需要清空验证码通过信息
                        if (loginFailTimeInt > Constant.PASSWORD_ERROR_SHOW_CODE_TIME) {
                            // 密码错误一次删除验证码通过信息
                            String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                            RedisUtil.del(verifyOkKey);
                        }
                        return buildLoginResult(result, 10003, "密码错误次数超过5次", null);

                    }
                }
                return buildLoginResult(result, 10002, "密码错误,请重新输入", null);
            } else {
                logger.error(condition + "密码错误");
                return buildLoginResult(result, ReturnCodeConstant.LOGIN_ERROR_CODE, "密码错误,请重新输入", null);
            }
        }

        // 简单密码校验，禁止登陆
        boolean isBanPassword = banPasswords.containsKey(user.getPassword());
        if (isBanPassword) {
            return buildLoginResult(result, 10002, "登录密码过于简单，点击忘记密码立即修改", null);
        }

        if (isNeedRestricLoginFail) {
            // 清空错误次数信息
            RedisUtil.del(redisKey);
            // 清除验证码验证信息
            String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
            RedisUtil.del(verifyOkKey);
        }

        UserBean userBean = newCommonLogin(request, user, params, upgradeStrategyMap, clientId, "0", 0);
        userBean.setIsShowIdentityPop(getIsShowIdentityPop(user));
//		logger.info(condition + "用户登录成功......userId:{}, use: {}",userBean.getId(),System.currentTimeMillis() - millis);
        return buildLoginResult(result, ReturnCodeConstant.OK, "登陆成功", userBean);
    }

    public Integer getIsShowIdentityPop(User user) {
        try {
            String minRegisterTime = tytConfigService.getStringValue("identity_onoff_min_register_time");
            logger.info("getIsShowIdentityPop是否启用新认证界面,minRegisterTime = {}", minRegisterTime);
            Date date = TimeUtil.parseDateString(minRegisterTime);
            if (user.getCtime().getTime() >= date.getTime()) {
                logger.info("getIsShowIdentityPop根据注册时间判断该用户为启用新认证界面内注册的用户,userId={}......", user.getId());
                //如果 registerIdentity 为空则需要弹窗
                if (Objects.isNull(user.getRegisterIdentity())) {
                    logger.info("getIsShowIdentityPop该用户identityType==null,返回0启用新认证界面,userId={}", user.getId());
                    return 0;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("getIsShowIdentityPop判定userId={}的用户不需要启用新认证界面,返回 1", user.getId());
        return 1;
    }

    @Override
    public ResultMsgBean newSmsLogin(HttpServletRequest request, Map<String, String> params) throws Exception {
        ResultMsgBean result = new ResultMsgBean();
        String cellPhone = params.get("cellPhone");
        String clientSign = params.get("clientSign");
        String clientId = params.get("clientId");
        String userVersion = params.get("clientVersion");
        String condition = cellPhone;
        String channelCode = params.get("channelCode");// 区分IOS是企业版还是个人版
        String verificationCode = params.get("verificationCode");

        /* 判断用户是否属于强制升级的用户 */
        Map<String, Object> upgradeStrategyMap = tytForceUpgradeUserService.getForceUpgradeVersionInfo(2, cellPhone, null, clientId, channelCode, clientSign, userVersion);
        if (upgradeStrategyMap != null && "1".equals(upgradeStrategyMap.get("type"))) {
            logger.info(condition + "软件版本低,属于强制升级的用户");
            return buildLoginResult(result, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", upgradeStrategyMap);
        }

        /* 判断版本号 */
        Map<String, Object> versionResult = versionBusiness.checkVersion(true, params);
        if (versionResult != null) {
            String type = (String) versionResult.get("type");
            if (type.equals("1")) {
                logger.info(condition + "软件版本低");
                return buildLoginResult(result, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", versionResult);
            }

        }
        /* 根据手机号查询用户 */
        User user = getUserByCellphone(cellPhone);
        //马甲用户登陆提示同未注册
        if (user != null && user.getIsMock() == 1) {
            user = null;
        }
        // 是否需要验证登录失败次数的标记
        boolean isNeedRestricLoginFail = false;
        String todayDate = TimeUtil.formatDate(new Date());
        try {
            // 获取需要进行登录错误次数控制的最小版本（不包括）
            String restriceLoginFailMinVersion = tytConfigService
                    .getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5301");
            int restriceLoginFailMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
            int clientVersionInt = Integer.valueOf(userVersion).intValue();
            isNeedRestricLoginFail = (clientVersionInt > restriceLoginFailMinVersionInt);
        } catch (Exception e) {
        }
        // 判断手机号存在否？
        Integer newUserFalg = 0;
        if (user == null) {

//		    //校验手机号是否注销过
//            ResultMsgBean resultMsgBean = this.getUserIsCancel(cellPhone);
//            if (resultMsgBean.getCode()!= ReturnCodeConstant.OK){
//                return resultMsgBean;
//            }
            newUserFalg = 1;
            User oldUser = getUserByCellphone(cellPhone);
            User newUser = regUser(params);
            String appletsLoginPassword = tytConfigService
                    .getStringValue(Constant.APPLETS_LOGIN_PASSWORD, "42f749c840acea865fbe927947eea7ae");
            newUser.setPassword(appletsLoginPassword);
            try {
                // 往tyt_cellphone添加手机号
                this.addCellPhoneToTemp(cellPhone);
            } catch (Exception e) {
                // APP5300以上版本返回不同的验证码
                if (isNeedRestricLoginFail) {
                    return buildLoginResult(result, 10000, "手机号已经存在,您可以直接登录", null);
                } else {
                    logger.error(condition + "该身份的手机号已经存在");
                    return buildLoginResult(result, ReturnCodeConstant.REGISTER_ERROR_CODE, "手机号已经存在,您可以直接登录", null);
                }
            }
            Long newUserId = createNewUser(request, params, oldUser, newUser);
            logger.info("用户短信登录,自动创建用户信息userId={}", newUserId);
            user = this.getByUserId(newUserId);
            registerGetCallCount(newUserId);
        }
        //查询用户是否存在处罚订单
        BlacklistUserOrders blacklistUserOrders = blacklistUserOrdersService.getOrdersByUserIdAndRestrictTime(user.getId(), new Date());
        if (blacklistUserOrders != null) {
            Long waybillExId = blacklistUserOrders.getWaybillExId();
            //查询异常上报订单信息
            TytTransportWaybillEx transportWaybillEx = transportWayBillExService.getExById(waybillExId);
            if (transportWaybillEx != null) {
                //运单号
                String tsOrderNo = transportWaybillEx.getTsOrderNo();
                //出发地(省市区以减号-分割开)
                String startPoint = transportWaybillEx.getStartPoint();
                //目的地(省市区以减号-分割开)
                String destPoint = transportWaybillEx.getDestPoint();
                //货物内容
                String taskContent = transportWaybillEx.getTaskContent();
                if (StringUtils.isNotBlank(taskContent) && taskContent.length() > 6) {
                    taskContent = taskContent.substring(0, 6) + "···";
                }
                //替换参数,逗号分隔
                String masterParams = tsOrderNo + "," + startPoint + "," + destPoint + "," + taskContent;

                TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号管制通知_订单, masterParams);
                result.setNoticeData(noticePopupTempl);
                return buildLoginResult(result, ReturnCodeConstant.NO_PERMISSION, null, user);
            }
        }
        // 判断用户黑名单否？
        // if (user.getBlackStatus() == User.BLACKLIST_STATUS_Y) {
        // 	BlacklistUser blacklistUser = blacklistUserService.getBlackByUserId(user.getId());
        // 	int cause = 5; // 默认其它原因
        //     String causeName = "账号未激活，请联系客服";
        // 	if(blacklistUser != null && blacklistUser.getStatus() == 1) {
        //         logger.error("userId为"+ user.getId() +"的账号在黑名单");
        //         causeName = "您的帐号存在行为异常，请联系客服";
        // 	}
        // 	// 设置账号未激活状态码供客户端使用
        // 	if (isNeedRestricLoginFail) {
        // 		return buildLoginResult(result, 10001, causeName, null);
        // 	} else {
        // 		logger.error(condition + "账号在黑名单");
        // 		return buildLoginResult(result, ReturnCodeConstant.LOGIN_ERROR_CODE, causeName, null);
        // 	}
        // }
        BlacklistUser blacklistUser = blacklistUserService.getBlackByUserIdAndStatus(user.getId(), 1);
        if (user.getBlackStatus() == User.BLACKLIST_STATUS_Y && blacklistUser != null) {
            TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号管制通知_拉黑, null);
            result.setNoticeData(noticePopupTempl);
            return buildLoginResult(result, ReturnCodeConstant.NO_PERMISSION, null, user);
        }

        //mac地址是否拉黑
        if (blacklistUserService.isBlacklistMac(clientId)) {
            logger.info("mac black : {}", clientId);
            return buildLoginResult(result, ReturnCodeConstant.LOGIN_ERROR_CODE, "设备限制登录", user);
        }

        String redisKey = Constant.LOGIN_SMS_FAIL_TIME_REDIS_KEY + cellPhone + todayDate;
        int cacheSeconds = (int) TimeUtil.getTomorrowZeroSeconds();
        // 如果是需要限制登录次数的版本则判断用户当天的登录次数
        if (isNeedRestricLoginFail) {
            String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
            String verifyOk = RedisUtil.get(verifyOkKey);
            boolean tag = org.apache.commons.lang.StringUtils.isEmpty(verifyOk) || !"1".equals(verifyOk);
            logger.info("verifyOk :{},tag:{}", verifyOk, tag);
            if (tag) {
                // 获取当前登录失败的次数，如果达到十次则返回控制客户端弹出验证码
                String loginFailTime = RedisUtil.get(redisKey);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(loginFailTime)) {
                    if (Integer.valueOf(loginFailTime)
                            .intValue() >= Constant.PASSWORD_ERROR_SHOW_CODE_TIME) {
                        return buildLoginResult(result, 10004, "验证码错误次数超过10次", null);
                    }
                }
            }
        }


        // 判断验证码正确否？
        if (!validateLoginVerifyCode(verificationCode, cellPhone)) {
            // 设置验证码错误状态码供客户端使用
            if (isNeedRestricLoginFail) {
                String loginFailTime = RedisUtil.get(redisKey);
                // 如果当前没有失败次数则记录失败次数为1次,
                // 否则判断登录失败的次数是否到达5次，如果到达5次，则返回引导客户端弹出重置密码框状态码
                if (org.apache.commons.lang.StringUtils.isEmpty(loginFailTime)) {
                    RedisUtil.set(redisKey, "1", cacheSeconds);
                } else {
                    // 增加登录失败次数
                    RedisUtil.incr(redisKey);
                    int loginFailTimeInt = Integer.valueOf(loginFailTime).intValue();
                    loginFailTimeInt = loginFailTimeInt + 1;
                    // 登录失败次数到达5次引导客户端弹出重置密码框状态码
                    if (loginFailTimeInt >= Constant.PASSWORD_ERROR_SHOW_MODIFY_TIME) {
                        // 如果大于10次则说明是用户成功输入了验证码，再次密码错误需要清空验证码通过信息
                        if (loginFailTimeInt > Constant.PASSWORD_ERROR_SHOW_CODE_TIME) {
                            // 密码错误一次删除验证码通过信息
                            String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
                            RedisUtil.del(verifyOkKey);
                        }
                        return buildLoginResult(result, 10002, "验证码错误,请重新输入", null);
                    }
                }
                return buildLoginResult(result, 10002, "验证码错误,请重新输入", null);
            } else {
                logger.error(condition + "验证码错误");
                return buildLoginResult(result, ReturnCodeConstant.LOGIN_ERROR_CODE, "验证码错误,请重新输入", null);
            }
        }
        // 简单密码校验，禁止登陆
//        boolean isBanPassword = banPasswords.containsKey(user.getPassword());
//        if (isBanPassword) {
//            return buildLoginResult(result, 10002, "登录密码过于简单，点击忘记密码立即修改", null);
            // 分批次按取膜3日计算 --------过渡时期结束，全面放开--------
//				long phoneModVal = Long.parseLong(cellPhone) % 3;
//				long dateModVal = Long.parseLong(TimeUtil.formatDate_(new Date())) % 3;
//				if(phoneModVal == dateModVal) {
//					backResponse(request, response, 10002, "登录密码过于简单，点击忘记密码立即修改", null, 0);
//					return;
//				}
//        }

        if (isNeedRestricLoginFail) {
            // 清空错误次数信息
            RedisUtil.del(redisKey);
            // 清除验证码验证信息
            String verifyOkKey = Constant.VERY_OK_REDIS_KEY + cellPhone;
            RedisUtil.del(verifyOkKey);
        }
//		//add by tianjw on 20180723  增加登录后的活动操作
//		try{
//			loginActivityService.excuteActivity(user);
////			登录活动发mq
////			20190428增加5月优惠券活动 此次活动时间20190506-20190531 仅限大于5910版本
//			loginActivityService.sendActivityMq(user.getId(),userVersion);
//		}catch (Exception e){
//			logger.error("活动发送首次登录推送消息失败");
//			e.printStackTrace();
//		}
        UserBean userBean = newCommonLogin(request, user, params, upgradeStrategyMap, clientId, "0", 0);
        userBean.setNewUserFlag(newUserFalg);
        userBean.setIsShowIdentityPop(getIsShowIdentityPop(user));


        return buildLoginResult(result, ReturnCodeConstant.OK, "登陆成功", userBean);
    }

    /**
     * 新注册账号拨打豁免弹窗
     * 0关闭，1-奇数20次；偶数5次，2-20次
     *
     */
    @Override
    public void registerGetCallCount(Long userId) {


        try {
            Integer registerGetCallCountFlag = tytConfigService.getIntValue(UNAUTH_USER_CALL_EXEMPT_SWITCH, 0);
            logger.info("registerGetCallCount userId:{} registerGetCallCountFlag:{}", userId,registerGetCallCountFlag);
            PopupSaveBean saveBean = new PopupSaveBean();
            saveBean.setReceiveId(userId);
            saveBean.setOriginPopup(1);
            saveBean.setCarPopup(1);

            if(registerGetCallCountFlag == 1){
                // 判断用户id 的奇偶性,奇数实验组
                if(userId % 2 != 0){
                    saveBean.setPopupTypeEnum(PopupTypeEnum.新注册账号拨打豁免弹窗2);
                    noticePopupService.savePopup(saveBean);
                }else {
                    saveBean.setPopupTypeEnum(PopupTypeEnum.新注册账号拨打豁免弹窗);
                    noticePopupService.savePopup(saveBean);
                }
            }else if(registerGetCallCountFlag == 2){
                // 全部用户
                saveBean.setPopupTypeEnum(PopupTypeEnum.新注册账号拨打豁免弹窗2);
                noticePopupService.savePopup(saveBean);
            }
        }catch (Exception e){
            logger.error("registerGetCallCount error:", e);
        }
    }

    private boolean validateLoginVerifyCode(String code, String cellPhone) {
        String realVerifyCode = RedisUtil.get(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
        if (!code.equals(realVerifyCode)) {
            return false;
        } else {
            RedisUtil.del(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
            return true;
        }
    }

    /**
     * 保存User到数据库
     *
     * @param params
     * @return
     * @throws Exception
     */
    private User regUser(Map<String, String> params) {
        User user = new User();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        user.setCtime(timestamp);
        user.setMtime(timestamp);
        user.setCellPhone(params.get("cellPhone"));
        user.setPassword(params.get("password"));

        // userSign 字段应该已经废弃 这个判断只是为了让代码不报错
        String userSign = params.get("userSign");
        if (org.apache.commons.lang.StringUtils.isNotBlank(userSign)) {
            user.setUserSign(Integer.parseInt(userSign));
        } else {
            user.setUserSign(User.UserSignEnum.货主.code);
        }

        user.setUserType(User.USER_TYPE_TRIAL);
        if (AppConfig.getIntProperty("tyt.user.need.verify") == 1) { // 如果需要验证的用户是未激活状态
            user.setUserType(User.USER_TYPE_TRIAL_NOT_VERIFY);
        }
        Integer clientSign = Integer.parseInt(params.get("clientSign"));

        user.setInfoUploadFlag(User.INFO_UPLOAD_DISABLE);
        user.setPlatId(clientSign);
        user.setClientSign(clientSign);
        user.setServeDays(AppConfig.getIntProperty("tyt.trial.days"));
        user.setPhoneServeDays(AppConfig.getIntProperty("tyt.phone.trial.day"));
        user.setQqModTimes(0);
        user.setEndTime(TimeUtil.stampAdd(TimeUtil.getTimeStamp(), user.getServeDays()));
        /* 省市县 */
        String[] resultArray = MobileUtil.getMobileAddressArr(user.getCellPhone());
        if (resultArray != null && resultArray.length == 2) {
            user.setProvince(resultArray[0]);
            user.setCity(resultArray[1]);
        }
        // 系统实名认证判读
        if (!isSystemIdentity(user.getCellPhone(), user.getProvince() + user.getCity())) {
            user.setVerifyFlag(1);
            user.setTrueName(" ");
            user.setUserName(" ");
        } else {
            user.setTrueName(params.get("trueName"));
            user.setUserName(params.get("userName"));
            user.setVerifyFlag(0);
        }
        user.setInfoPublishFlag(User.INFO_PUBLISH_ENABLE);
        user.setIsCar("0");
        user.setIsBank("0");
        /* 关闭QQ消息盒子 */
        user.setQqBoxFlag(User.QQ_BOX_FLAG_CLOSE);
        user.setIsDispatch(0);
        user.setVerifyPhotoSign(0);
        user.setUserPart(100);
        // 司机限制
        TytConfig tytConfig = tytConfigService.getValue("driverLimit");
        if (tytConfig != null && "1".equals(tytConfig.getValue())) {
            if (user.getUserSign().intValue() == 7) {
                user.setVerifyFlag(0);
                user.setServeDays(0);
                user.setPhoneServeDays(0);
                user.setEndTime(new Timestamp(System.currentTimeMillis()));
                // user.setInfoPublishFlag(User.INFO_PUBLISH_DISABLE);
            }
        }

        if (params.get("channelCode") != null && !"".equals(params.get("channelCode"))) {
            TytSource tytSource = TytSourceUtil.getSourceName("app_channel", String.valueOf(params.get("channelCode")));
            if (tytSource != null) {
                user.setChannel(Integer.parseInt(params.get("channelCode")));
            }
        }

        user.setSource("待定");
        user.setSourceRemark("");
        return user;
    }

    @Override
    public Long createNewUser(HttpServletRequest request, Map<String, String> params, User oldUser, User user) throws Exception {

        //设置用户身份
        int clientSign = Integer.parseInt(params.get("clientSign"));
        user.setVerifyPhotoSign(0);
        if (clientSign == Constant.ClientSignEnum.ANDROID_CAR.code || clientSign == Constant.ClientSignEnum.IOS_CAR.code || clientSign == Constant.ClientSignEnum.H5_CAR.code) {
            user.setUserClass(2);
            user.setIdentityType(6);
        } else if (clientSign == Constant.ClientSignEnum.ANDROID_GOODS.code || clientSign == Constant.ClientSignEnum.IOS_GOODS.code
                || clientSign == Constant.ClientSignEnum.WEB_GOODS.code || clientSign == Constant.ClientSignEnum.H5_GOODS.code) {
            user.setUserClass(1);
            user.setIdentityType(3);
        } else if (clientSign == Constant.ClientSignEnum.MINI_PROGRAM_GOODS.code) {
            user.setUserClass(3);
            user.setIdentityType(10);
        } else if (clientSign == Constant.ClientSignEnum.MINI_PROGRAM_TRUCKER.code) {
            user.setUserClass(4);
            user.setIdentityType(11);
        }

        if (oldUser != null && oldUser.getIsMock() == IS_MOCK_USER) {
            //更新mock用户为正式用户
            user.setId(oldUser.getId());
            user.setUserName(oldUser.getUserName());
            user.setCarUserName(oldUser.getUserName());
            user.setIsMock(0);
            this.getBaseDao().evict(oldUser);
            this.getBaseDao().update(user);
            this.getBaseDao().flush();
        } else {
            /* 保存用户信息 */
            user = this.addUser(user);
        }
        String clientSignText = params.get("clientSign");
        // 发送MQ
        MqUserMsg mqUserMsg = new MqUserMsg();

        mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqUserMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_USER_INIT_MESSAGE);
        mqUserMsg.setUserId(user.getId());
        mqUserMsg.setClientSign(clientSignText);
        tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

        // 发送初始化用户信息
        tytMqMessageService.sendMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

        if (!isSystemIdentity(user.getCellPhone(), user.getProvince() + user.getCity())) {
            tytUserSubService.saveTytUserByUserId(user.getId(), 1, params.get("clientId"));
        } else {
            tytUserSubService.saveTytUserByUserId(user.getId(), 0, params.get("clientId"));
        }

        // 如果是新版车、货APP、货WEB注册的用户，注册时保存默认一级身份
        if (clientSign == Constant.ClientSignEnum.ANDROID_CAR.code || clientSign == Constant.ClientSignEnum.IOS_CAR.code || clientSign == Constant.ClientSignEnum.H5_CAR.code) {
            this.saveOneLevelIdentity(String.valueOf(user.getId()), 2, 6);
        } else if (clientSign == Constant.ClientSignEnum.ANDROID_GOODS.code || clientSign == Constant.ClientSignEnum.IOS_GOODS.code
                || clientSign == Constant.ClientSignEnum.WEB_GOODS.code || clientSign == Constant.ClientSignEnum.H5_GOODS.code) {
            this.saveOneLevelIdentity(String.valueOf(user.getId()), 1, 3);
        } else if (clientSign == Constant.ClientSignEnum.MINI_PROGRAM_GOODS.code) {
            this.saveOneLevelIdentity(String.valueOf(user.getId()), 3, 10);
        } else if (clientSign == Constant.ClientSignEnum.MINI_PROGRAM_TRUCKER.code) {
            this.saveOneLevelIdentity(String.valueOf(user.getId()), 4, 11);
        }

        /* 用户注册日志 */
        createLog(request, user, OpLog.OP_CLIENT_REG, params);
        channelLog(request, user, ChannelLogConstant.register, params);
        /* 按正常登陆流程走，生成客户端数据 */
//		return this.newCommonLogin(request, user, params,null, params.get("clientId"),"0");
        return user.getId();
    }

    /**
     * 添加日志到数据库
     *
     * @return
     */
    protected void createLog(HttpServletRequest request, User user, Integer opType,
                             Map<String, String> params) throws Exception {
        OpLog log = new OpLog();
        log.setUserId(user.getId());
        log.setOpType(opType);
        log.setIp(StringUtil.getRealIp(request));
        log.setPlatId(Integer.parseInt(params.get("clientSign")));
        log.setCellPhone(user.getCellPhone());
        log.setTicket(user.getTicket());
        log.setVersion(params.get("clientVersion"));
        opLogService.add(log);
    }


    protected void channelLog(HttpServletRequest request, User user, Integer record_type, Map<String, String> params) throws Exception {
        TytChannelLog tytChannelLog = new TytChannelLog();

        tytChannelLog.setPhoneType(params.get("phoneType"));
        tytChannelLog.setVersionType(params.get("versionType") == null ? null : Integer.parseInt(params.get("versionType")));
        tytChannelLog.setClientId(params.get("clientId"));
        tytChannelLog.setOsVersion(params.get("osVersion"));
        tytChannelLog.setClientVersion(params.get("clientVersion"));
        tytChannelLog.setClientSign(Integer.parseInt(params.get("clientSign")));
        tytChannelLog.setUserId(user.getId());
        tytChannelLog.setRecordType(record_type);

        tytChannelLog.setCtime(new Date());
        if (StringUtils.isNotBlank(params.get("channelCode"))) {
            TytSource tytSource = TytSourceUtil.getSourceName("app_channel", String.valueOf(params.get("channelCode")));
            if (tytSource != null) {
                tytChannelLog.setChannelName(tytSource.getName());
                tytChannelLog.setChannelCode(Integer.parseInt(params.get("channelCode")));
            }
        }
        if (params.get("certificateType") != null) {
            tytChannelLog.setCertificateType(Integer.parseInt(params.get("certificateType")));
        }
        tytChannelLogService.add(tytChannelLog);
        //userId与mac(clientId)关系唯一记录添加至tyt_user_mac
        userMacService.saveUserMac(user, tytChannelLog);
        //缓存记录用户登录的手机基本信息，每次登录时更新存储  这部分用户企业版登录，已无企业版项目
//		Map<String,Object> map = new HashMap<>();
//		map.put("userId",user.getId());
//		map.put("phoneType",tytChannelLog.getPhoneType());
//		map.put("clientId",tytChannelLog.getClientId());
//		map.put("osVersion",tytChannelLog.getOsVersion());
//		map.put("clientVersion",tytChannelLog.getClientVersion());
//		map.put("clientSign",tytChannelLog.getClientSign());
//		map.put("cid",params.get("cid"));
//		map.put("deviceId",params.get("deviceId"));
//		String userHeader = JSON.toJSONString(map);
//		RedisUtil.set(Constant.PLAT_USER_HEADER + user.getId(), userHeader, 3 * 24 * 60 * 60);

    }

    public boolean isSystemIdentity(String phone, String place) {

        TytConfig systemIdentityIsOpendTytConfig = tytConfigService.getValue("systemIdentityIsOpend");
        String systemIdentityIsOpend = systemIdentityIsOpendTytConfig.getValue();
        // String systemIdentityIsOpend="1";
        // 开关是否开了
        if ("1".equals(systemIdentityIsOpend)) {
            TytConfig systemIdeatityAreaTytConfig = tytConfigService.getValue("systemIdeatityArea");
            String systemIdeatityArea = systemIdeatityAreaTytConfig.getValue();
            // String systemIdeatityArea="北京,贵州,天津,郑州";
//			String place = MobileUtil.getMobileAddress(phone);
            String[] systemIdeatityAreas = systemIdeatityArea == null ? null : systemIdeatityArea.split(",");

            // 判断是否在排除范围内
            if (systemIdeatityAreas != null && null != place) {
                for (String area : systemIdeatityAreas) {
                    if (place.indexOf(area) != -1) {
                        return true;
                    }
                }
            }
        } else {
            return true;
        }

        return false;
    }

    @Override
    public ResultMsgBean newSimulatedLogin(HttpServletRequest request, Map<String, String> params) throws Exception {
        long millis = System.currentTimeMillis();
        ResultMsgBean result = new ResultMsgBean();
        /* 解析参数 */
        Long userId = Long.parseLong(params.get("userId"));
        String clientSign = params.get("clientSign");
        String userVersion = params.get("clientVersion");
        String clientId = params.get("clientId");
        String condition = "userId_" + userId + "用户模拟登陆 ";
        logger.info(condition + "-------------------- start ");
        String channelCode = params.get("channelCode");// 区分IOS是企业版还是个人版
        Integer versionControl = tytConfigService.getIntValue(Constant.LOGIN_VERSION_CONTROL, 1000);

        if (Integer.parseInt(userVersion) < versionControl) {
            return buildLoginResult(result, ReturnCodeConstant.OTHER_ERROR, "系统升级中", null);
        }
        /* 判断用户是否属于强制升级的用户 */
        Map<String, Object> upgradeStrategyMap = tytForceUpgradeUserService.getForceUpgradeVersionInfo(1, null, userId, clientId, channelCode, clientSign, userVersion);
        if (upgradeStrategyMap != null && "1".equals(upgradeStrategyMap.get("type"))) {
            logger.info(condition + "软件版本低,属于强制升级的用户");
            return buildLoginResult(result, ReturnCodeConstant.VERSION_UPGADE_CODE, "您当前版本低,请升级", upgradeStrategyMap);
        }


        /* 判断版本号 */
        Map<String, Object> versionResult = versionBusiness.checkVersion(true, params);
        if (versionResult != null) {
            String type = (String) versionResult.get("type");
            if (type.equals("1")) {
                if ("21".equals(clientSign) || "22".equals(clientSign) || Integer.parseInt(userVersion) > 6349) {
                    // 20161114模拟登陆接口由于《版本低》提示参数，客户端没有做处理，故改成踢出用户，让其重新登录，因为登陆接口有相应的提示
                    logger.info(condition + "软件版本低,暂时把用户踢出去");
                    String objTicketKey = UserTicketUtil.getObjTicketKey(userId.toString());
                    cacheService.setObject(objTicketKey, "0000");
                    return buildLoginResult(result, ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG, versionResult);
                }
            }
        }
        /* 根据id查询用户 */
        User user = getByUserId(userId);
        //登录活动发mq
        //20190428增加5月优惠券活动 此次活动时间20190506-20190531 仅限大于5910版本
//		loginActivityService.sendActivityMq(user.getId(),userVersion);
        /* 记录日志，响应客户端 */
//			channelLog(request, user, ChannelLogConstant.simulatedLogin, params);
        UserBean userBean = newCommonLogin(request, user, params, upgradeStrategyMap, clientId, "1", 0);
        userBean.setIsShowIdentityPop(getIsShowIdentityPop(user));
        logger.info(condition + "登录成功......userId:{}, use: {}", userBean.getId(), System.currentTimeMillis() - millis);
        return buildLoginResult(result, ReturnCodeConstant.OK, "登陆成功", userBean);
    }

    /**
     * 公共登陆方法
     *
     * @param request
     * @param user
     * @param params
     * @param upgradeStrategyMap
     * @param clientId
     * @param isSimulatedLogin
     * @return
     * @throws Exception
     */
    @Override
    public UserBean newCommonLogin(HttpServletRequest request, User user, Map<String, String> params,
                                    Map<String, Object> upgradeStrategyMap, String clientId, String isSimulatedLogin, int isRegister) throws Exception {

        String userSubKey = Constant.CACHE_USERSUB_KEY + user.getId() + "_" + TimeUtil.formatDateMonthTime(new Date());
        cacheService.del(userSubKey);
        // btt5910修改 登录和模拟登录
        /* 生成ticket */
        String reqTicket = params.get("ticket");
        int clientSign = Integer.parseInt(params.get("clientSign"));
        logger.info("-----------------登录clientSign:" + clientSign + "----------------");
        Integer recordType = null;
        if (Objects.equals("1", isSimulatedLogin)) {
            recordType = ChannelLogConstant.simulatedLogin;
        } else {
            recordType = ChannelLogConstant.Login;
            // 因为用户密码注册之后会再走一遍登录接口，导致用户权益弹窗弹两遍，所以添加此逻辑
            if (isRegister == 0){
                rebindDevice(clientId, user, clientSign);
            }
        }
        String ticket = this.getTicket(user.getId(), clientSign, isSimulatedLogin, reqTicket);
        user.setTicket(ticket);
        //loginMq
        this.sendLoginMq(request, user, params, upgradeStrategyMap, recordType);
        //赠送1+9发货次卡活动
        if (TytSwitchUtil.isGoodsNonMemberActivityOn()) {
            this.sendGoodsCardActivity(user);
        }
        /* 更新用户信息 */
        //mq 处理
//		userService.updateLastInfo(user.getId(), ticket, Integer.parseInt(params.get("clientSign")), params.get("clientVersion"), params.get("osVersion"));
        tytUserSubService.savePushCid(user.getId(), params.get("cid"), Integer.parseInt(params.get("clientSign")), params.get("clientVersion"), params.get("osVersion"), params.get("deviceId"), params.get("carDeviceId"), params.get("goodsDeviceId"));
        /*更新升级用户的状态是否完成*/
        //mq 处理
//		if(upgradeStrategyMap != null && upgradeStrategyMap.get("isUpdate")!=null && "1".equals(upgradeStrategyMap.get("isUpdate").toString()) &&
//				upgradeStrategyMap.get("updateVersion") != null  &&  !"".equals(upgradeStrategyMap.get("updateVersion").toString()) &&
//				upgradeStrategyMap.get("taskId") != null  &&  !"".equals(upgradeStrategyMap.get("taskId").toString())){
//
//			tytForceUpgradeUserService.updateInfo(user.getId(),upgradeStrategyMap.get("updateVersion"),upgradeStrategyMap.get("taskId"));
//		}
        /* 获取最新的用户信息 */
//		user = userService.getById(user.getId());
        User users = new User();
        users.setClientSign(Integer.parseInt(params.get("clientSign")));
        users.setOsVersion(params.get("osVersion"));
        users.setClientVersion(params.get("clientVersion"));
        users.setMtime(new Timestamp(System.currentTimeMillis()));
        users.setLastTime(new Date());
        org.springframework.beans.BeanUtils.copyProperties(user, users);
        logger.info("-----------------登录user:" + JSON.toJSONString(user) + "----------------" + JSON.toJSONString(users));
        /* user信息缓存 */
        cacheService.setObject(Constant.CACHE_USER_KEY + users.getId(), users, AppConfig.getIntProperty("tyt.cache.user.time"));

        /* 生成响应数据userBean */
        UserBean userBean = createUserResponse(users);
        TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(user.getId());
        userBean.setTradeNums(Optional.ofNullable(tytUserSub.getDealNum()).orElse(0).toString());

        // 根据clientSign返回对应客户端的昵称（用户名 - 车/货）
        if (clientSign == Constant.ClientSignEnum.ANDROID_CAR.code || clientSign == Constant.ClientSignEnum.IOS_CAR.code) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(user.getCarUserName())) {
                userBean.setUserName(users.getCarUserName());
            }
        }

        //用户定向升级，如果为提示升级则返回以下几个参数
        if (upgradeStrategyMap != null && upgradeStrategyMap.get("type") != null) {
            userBean.setIsUpdate(2); //是否需要升级 1:不需要升级   2：需要升级
            userBean.setGoalVersion(String.valueOf(upgradeStrategyMap.get("version")));
            userBean.setContent(String.valueOf(upgradeStrategyMap.get("contentNew")));
            userBean.setUrl(String.valueOf(upgradeStrategyMap.get("url")));
            userBean.setUpgradeMode(String.valueOf(upgradeStrategyMap.get("type"))); // 0：提示升级   1：强制升级
        } else {
            userBean.setIsUpdate(1); //是否需要升级 1或者其他:不需要升级   2：需要升级
        }

        /*
         * 获取用户的认证信息
         */
        //登录优化 重复操作 createUserResponse 已处理实名认证信息
//		fetchUserIdentity(users, userBean);
        //serveDays不准确，根据endTime计算后返回，兼容老版本，精准货源推荐新版本忽略本逻辑
        if (userBean != null && userBean.getEndTime() != null) {
            int days = TimeUtil.getDays(TimeUtil.formatDate(new Date()), TimeUtil.formatDate(userBean.getEndTime()));
            userBean.setServeDays(days);
            if (days < 0) {
                userBean.setServeDays(0);
            }
        }
        //精准货源推荐  更新用户的在线心跳时间
        //登录优化 删除不用
//		if(user != null && user.getId() != null && user.getId().longValue() > 0){
//			preferService.updateUserPreferOnlineTime(user.getId());
//		}

        /* 记录日志 */
        //mq 处理
//		createLog(request, users, OpLog.OP_CLIENT_LOGIN, params);
        //app我的发货菜单状态
        if (tytUserSub != null) {
            userBean.setMyGoodsMenu(tytUserSub.getMyGoodsMenu() == null ? 2 : tytUserSub.getMyGoodsMenu());
            // 用户时候设置了钱包密码信息
            userBean.setPwdStatus(tytUserSub.getPocketPwdStatus());
            if (tytUserSub.getUserGroup() != null) {
                userBean.setUserLevelStatus(tytUserSub.getUserGroup());
            }
            userBean.setNonVipContent(Constant.NON_VIP_CONTENT);
            userBean.setCarVerifyFlag("1".equals(user.getIsCar()) ? 1 : 0);
            if (tytUserSub.getUserGroup() != null && tytUserSub.getUserGroup() == Constant.RIGHTS_NORMAL) { // 试用会员
                if (tytUserSub.getLevel2BigingTime() != null) {
                    int passedDays = TimeUtil.daysBetween(tytUserSub.getLevel2BigingTime(), new Date());
                    int allDays = tytConfigService.getIntValue("rights_normal_call_day", 20);
                    int remainDays = allDays - passedDays;
                    userBean.setLevel2RemainDays(remainDays > 0 ? remainDays : 1);
                }
            }
            int couponNum = couponService.getValidRemainQtyByUserId(user.getId(), clientSign);
            userBean.setCouponNum(couponNum);
            userBean.setBindStatus(tytUserSub.getBindStatus());
        } else {
            userBean.setMyGoodsMenu(2);
        }
        //登录更新用户权益缓存
        String permissionKey = Constant.USER_PERMISSION_REDIS_KEY + user.getId();
        RedisUtil.del(permissionKey);
        UserPermissionResult result = new UserPermissionResult();
        PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.用户昵称显示, user.getId());
        if (permissionResult.getUse()) {
            result.setIsUserNamePower(1);
        }
        PermissionResult contentPermissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货物内容显示, user.getId());
        if (contentPermissionResult.getUse()) {
            result.setIsContentPower(1);
        }
        RedisUtil.setObject(permissionKey, result, 0);
        //设置是否需要弹框
        userBean.setIsPopUp(0);
        //是否弹广告位
        checkUserPopup(userBean);
        //新版货主身份标签弹窗
        this.checkGoodsTypePopup(clientSign, userBean);

        return userBean;
    }

    @Override
    public void checkGoodsTypePopup(int clientSign, UserBean userBean) {
        //只有货版才校验
        if (Constant.ClientSignEnum.ANDROID_GOODS.codeEqualse(clientSign)
                || Constant.ClientSignEnum.IOS_GOODS.codeEqualse(clientSign)) {

            Long userId = userBean.getId();

            if (userId == null) {
                return;
            }

            Example exa = new Example(TytUserIdentityLabel.class);
            exa.and().andEqualTo("userId", userId);

            TytUserIdentityLabel tytUserIdentityLabel = tytUserIdentityLabelMapper.selectOneByExample(exa);

            Integer goodsTypeFirst = null;
            Integer goodsTypeSecond = null;

            if (tytUserIdentityLabel != null) {
                Integer goodsTypeFirstTmp = tytUserIdentityLabel.getGoodsTypeFirst();
                Integer goodsTypeSecondTmp = tytUserIdentityLabel.getGoodsTypeSecond();

                if (goodsTypeFirstTmp != null && goodsTypeFirstTmp > 0) {
                    goodsTypeFirst = goodsTypeFirstTmp;
                }

                if (goodsTypeSecondTmp != null && goodsTypeSecondTmp > 0) {
                    goodsTypeSecond = goodsTypeSecondTmp;
                }
            }

            userBean.setGoodsTypeFirst(goodsTypeFirst);
            userBean.setGoodsTypeSecond(goodsTypeSecond);

            //一级标签只能为 1,2
            if (goodsTypeFirst != null && goodsTypeFirst > 0 && goodsTypeFirst < 3) {
                //已设置身份标签
                userBean.setShowGoodsTypePop(GlobalStatusEnum.no.getCode());
                return;
            }

            userBean.setShowGoodsTypePop(GlobalStatusEnum.yes.getCode());

            //查询用户企业认证状态
            TytUserIdentityAuth tytUserIdentityAuth = userIdentityAuthService.getTytUserIdentityAuth(userId);

            if (tytUserIdentityAuth != null) {
                Integer enterpriseAuthStatus = tytUserIdentityAuth.getEnterpriseAuthStatus();

                if (EnterpriseAuthStatusEnum.success.equalsCode(enterpriseAuthStatus)) {
                    userBean.setGoodsTypeFirst(GoodsTypeFirstEnum.enterprise.getCode());
                }
            }
        }
    }

    /**
     * @param user
     * @return void
     * @description 赠送1+9发货次卡活动
     * <AUTHOR>
     * @date 2022/8/1 10:54
     */
    private void sendGoodsCardActivity(User user) {
        Long userId = user.getId();
        logger.info("赠送1+9发货次卡活动开始，用户ID为：{}", userId);
        //1.查询用户是否在有效活动名单内
        Integer activityId = tytConfigService.getIntValue("goods:nonmember:activity:id", 0);
        boolean isExist = marketingActivityService.getUserIsExistInActivity(userId, activityId);
        logger.info("赠送1+9发货次卡活动，用户ID{} 是否在有效活动名单内：{}", userId, isExist);
        //2.查询是否给用户赠送过1+9发货次卡
        Integer goodsId = tytConfigService.getIntValue("goods:giveway:goods:id", 0);
        MarketingActivity marketingActivity = marketingActivityService.getById(activityId.longValue());
        //活动开始时间
        Date startTime = marketingActivity.getStartTime();
        //活动结束时间
        Date endTime = marketingActivity.getEndTime();
        boolean isGivingFlag = userBuyGoodsService.isGivingRight(userId, goodsId.longValue(), startTime, endTime);
        logger.info("赠送1+9发货次卡活动，查询是否给用户ID{} 赠送过1+9发货次卡：{}", userId, isGivingFlag);
        //3.赠送用户1+9发货次卡
        if (isExist && !isGivingFlag) {
            logger.info("赠送1+9发货次卡活动满足条件，用户ID为：{}", userId);
            userPermissionService.giveGoodsCard(user);
        }
        logger.info("赠送1+9发货次卡活动结束，用户ID为：{}", userId);
    }

    private void checkUserPopup(UserBean userBean) {
        //判断用户是否参加过活动
        if (activityService.getPopUp(userBean.getId()) == 0) {
            return;
        }
        //查询用户是否有过车会员记录
        UserPermission carPermission = userPermissionService.getUserPermission(userBean.getId(), "100101");
        if (carPermission != null) {
            if (carPermission.getStatus() == 3 && TimeUtil.daysBetween(carPermission.getEndTime(), new Date()) >= 30) {
                userBean.setIsPopUp(1);
            }
        } else {
            if (TimeUtil.daysBetween(userBean.getCtime(), new Date()) >= 30) {
                userBean.setIsPopUp(1);
            }
        }
    }


    private void rebindDevice(String clientId, User user, Integer clientSign) {
        Long userId = user.getId();
        // 查看该用户是否已经绑定设备，未绑定则尝试绑定
        TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(user.getId());
        logger.info("======================tytUserSub:" + tytUserSub);
        if (tytUserSub != null) {
            //
            Integer userGroup = tytUserSub.getUserGroup();
            //设备是否被绑定 true：是 false：否
            boolean hasBinded = tytUserSubService.hasBinded(clientId, userId);
            //绑定状态
            Integer bindStatus = tytUserSub.getBindStatus();
            int clientPort = Constant.isCarOrGoodsOrOrigin(clientSign);
            logger.info("rebindDevice userId: " + userId + ", tytUserSub.getBindStatus()："
                    + bindStatus + ", clientId: " + clientId + " userGroup:" + userGroup + " hasBinded:" + hasBinded);
            TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(userId));
            boolean activityOnOff = TytSwitchUtil.isGoodsGivewayPermissionOn();
            Integer num = 0;
            Integer count = userCancelService.getCellPhone(user.getCellPhone());
            //判断设备号不为“”，null而且不为未获取状态下查询注册数量--即设备号为真实的代码数值
            if (StringUtils.isNotBlank(clientId) && !("未获取").equals(clientId)) {
                //获取同一台设备下注册的车/货的数量
                num = tytUserSubService.getCountNum(clientPort, clientId);
            }
            //限制同一个设备新注册用户权益数量
            Integer edNum = tytConfigService.getIntValue("equityDistribution");
            // 如果是未绑定状态则尝试绑定
            if (bindStatus == null || (bindStatus != null && bindStatus.intValue() == 1)) {
                //if (!hasBinded) {

                tytUserSubService.updateBindStatus(clientId, user.getId(), 2);

                if (count == 0) {
                    //如果手机号未注销过，判断同一个设备号注册数量是否小于规定数量或者获取设备号为“”，null或者设备号为未获取情况下进行下发
                    if (num < edNum || StringUtils.isBlank(clientId) || ("未获取").equals(clientId)) {
                        if (userGroup != null) {
                            // 绑定成功之后设置对应的需要提示的消息
                            if (userGroup == Constant.RIGHTS_EXPERIENCE) {
                                if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
                                    // 插入身份认证未成功的提醒
                                    logger.info("rebindDevice userId: " + userId + ", saveRightsInfo");
                                    //弹窗
                                    if (clientPort == 1) {
                                        noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app未实名认证, userId, clientPort);
                                    } else if (clientPort == 2) {
                                        // todo  2024-04-30 根据需求CXFS-5665要求注销登录注册发放货权益
//                                        if (activityOnOff) {
//                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app未实名认证New, userId, clientPort);
//                                        } else {
//                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app未实名认证, userId, clientPort);
//                                        }
                                    }
//									else {
//										noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆app未身份认证, userId, clientPort);
//									}
                                } else {
                                    // 5930 弹窗
//									if (clientPort == 1) {
////										noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app实名认证通过, userId, clientPort);
////										userPermissionService.giveExperienceMember(user, clientPort); // 发权益
//									} else
                                    if (clientPort == 2) {
                                        // todo  2024-04-30 根据需求CXFS-5665要求注销登录注册发放货权益
//                                        if (activityOnOff) {
//                                            noticePopupService.savePopup(PopupTypeEnum.每月赠送发货权益, userId, clientPort);
//                                        } else {
//                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app实名认证通过, userId, clientPort);
//                                        }
                                    }
//									else {
//										noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆app身份认证通过, userId, clientPort);
//									}
                                }
                                if (clientPort != 1) {
                                    // todo  2024-04-30 根据需求CXFS-5665要求注销登录注册发放货权益
                                    // 5930 新权益， 发放体验权益
//                                    userPermissionService.giveExperienceMember(user, clientPort); // 发权益
                                }
                            }
                            if (userGroup == Constant.RIGHTS_EXPERIENCE || userGroup == Constant.RIGHTS_EXPERIENCE_TIMEOUT) {
                                //给用户发放车试用的权益开关
                                int onoff = tytConfigService.getIntValue("car_auth_permission_onoff", 1);
                                if (onoff == 1) {
                                    if ("1".equals(user.getIsCar())) {
                                        // 试⽤会员，车辆认证审核通过后的提醒
                                        noticeRemindService.saveRightsInfo(Constant.INDEX_CAR_AUTH_TYPE1,
                                                Constant.INDEX_CAR_AUTH_TYPE2,
                                                Constant.INDEX_CAR_AUTH_CONTENT,
                                                tytUserSub.getUserId());
                                        // 5930版本弹窗
                                        PopupSaveBean saveBean = new PopupSaveBean();
                                        saveBean.setPopupTypeEnum(PopupTypeEnum.身份为试用后首次登录app);
                                        saveBean.setReceiveId(userId);
                                        saveBean.setOriginPopup(1);
                                        saveBean.setCarPopup(1);
                                        noticePopupService.savePopup(saveBean); // 弹窗
                                        // 发放试用会员权益
                                        userPermissionService.giveTrialMember(user);
                                        //修改用户状态为试用
                                        tytUserSubService.updateUserGroup(userId, Constant.RIGHTS_NORMAL);
                                    }
                                }
                            }
                        }
                    }
                }
                //}
            } else {
                //如果已绑定，判断身份是否为体验
                if (userGroup != null && (userGroup == Constant.RIGHTS_EXPERIENCE || userGroup == Constant.RIGHTS_EXPERIENCE_TIMEOUT || userGroup == Constant.RIGHTS_NORMAL)) {
                    if (StringUtils.isBlank(tytUserSub.getBindCliendid()) || ("未获取").equals(tytUserSub.getBindCliendid())) {
                        if (StringUtils.isNotBlank(clientId) && !("未获取").equals(clientId)) {
                            tytUserSubService.updateBindStatus(clientId, user.getId(), 2);
                        }
                    }
                    if (count == 0) {
                        //如果手机号未注销过，判断同一个设备号注册数量是否小于规定数量或者获取设备号为“”，null或者设备号为未获取情况下进行下发
                        if (num < edNum || StringUtils.isBlank(clientId) || ("未获取").equals(clientId)) {
                            //判断用户是否赠送过老权益
                            boolean isGivingRight = userBuyGoodsService.isGivingRight(userId, GoodsType.体验会员.getId());
                            if (!isGivingRight && (clientPort == 1 || clientPort == 2)) {
                                boolean isGivingRightGoods = userBuyGoodsService.isGivingRightForGoods(userId);
                                boolean isGivingRightCar = userBuyGoodsService.isGivingRight(userId, GoodsType.车体验.getId());
                                if (clientPort == 1) {
                                    //2023.3.13-取消是否有送货会员判断，只留车会员，没送过并且实名认证就发权益
//									if (isGivingRightGoods && !isGivingRightCar) {
//									if (!isGivingRightCar) {
//										if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
//											noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app未实名认证, userId, clientPort);
//										} else {
//											noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app实名认证通过, userId, clientPort);
//											userPermissionService.giveExperienceMember(user, clientPort); // 发权益
//										}
//									}

                                    //查询存量历史用户赠送找货权益活动id
                                    String equitiesActivityId = tytconfigService.getStringValue("equities_activity_id", "0");
                                    //活动id
                                    Long equitiesId = Long.valueOf(equitiesActivityId);
                                    //查询该用户是否在本次活动中
                                    List<MarketingActivityUser> marketingActivityUserList = conventionActivityService.getEquitiesByUserId(equitiesId, userId, 1);
                                    //在活动中，发放权益并修改状态
                                    if (marketingActivityUserList != null && marketingActivityUserList.size() > 0) {
                                        //实名认证完成
                                        if (identityAuth != null && identityAuth.getIdentityStatus() == 1) {
                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app实名认证通过, userId, clientPort);
                                            userPermissionService.giveExperienceMember(user, clientPort); // 发权益
                                            conventionActivityService.updateUserStatus(equitiesId, userId, 2);//修改状态
                                        }
                                    } else if (!isGivingRightCar) {
                                        if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app未实名认证, userId, clientPort);
                                        } else {
                                            noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app实名认证通过, userId, clientPort);
                                            userPermissionService.giveExperienceMember(user, clientPort); // 发权益
                                        }
                                    }

//									else { // 历史存量未身份认证用户，重新赠送一次找货权益，由于需求调整，代码暂注释
//										try {
//											//2023-03-13新需求，3.14之前的用户，未认证的历史用户，需要重新赠送一次3天1000次找货权益
//											Date startGivePermissionTime = TimeUtil.parseDateString("2023-03-14 00:00:00");
//											boolean isFutureGivingRightCar = userBuyGoodsService.isGivingRight(userId, GoodsType.车体验.getId(), startGivePermissionTime, new Date());
//											//认证时间在指定时间分界线之后，同时赠送权益记录在指定时间分界线之后
//											if (identityAuth != null && identityAuth.getExamineTime() != null) {
//												if (TimeUtil.isBeforeWhenDate(startGivePermissionTime, identityAuth.getExamineTime())
//														&& identityAuth.getIdentityStatus() == 1
//														&& !isFutureGivingRightCar
//												) {
//													noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆车app实名认证通过, userId, clientPort);
//													userPermissionService.giveExperienceMember(user, clientPort); // 发权益
//												}
//											}
//										} catch (Exception e) {
//											e.printStackTrace();
//										}
//									}
                                } else {
                                    if (isGivingRightCar && !isGivingRightGoods) {
                                        // todo  2024-04-30 根据需求CXFS-5665要求注销登录注册发放货权益
//                                        if (activityOnOff) {
//                                            if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
//                                                noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app未实名认证New, userId, clientPort);
//                                            } else {
//                                                noticePopupService.savePopup(PopupTypeEnum.每月赠送发货权益, userId, clientPort);
//                                            }
//                                        } else {
//                                            if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
//                                                noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app未实名认证, userId, clientPort);
//                                            } else {
//                                                noticePopupService.savePopup(PopupTypeEnum.注册后首次登陆货app实名认证通过, userId, clientPort);
//                                            }
//                                        }
//                                        userPermissionService.giveExperienceMember(user, clientPort); // 发权益
                                    }
                                }
                            }
                        }
                    }
                }
                // 如果已经绑定绑定身份是试用会员，但是还没有开始使用，则设置开始时间
                if (userGroup != null && userGroup == Constant.RIGHTS_NORMAL
                        && tytUserSub.getLevel2BigingTime() == null) {
                    //// TODO: 2021/4/27 可以删除
                    tytUserSubService.updateLevelBeginTime(userId, new Date());
                }
            }
        }
    }


    /**
     * 生成返回给客户端的用户信息
     *
     * @param user
     * @return
     * @throws Exception
     */
    private UserBean createUserResponse(User user) {
        /* 创建UserBean */
        UserBean userBean = new UserBean();
        org.springframework.beans.BeanUtils.copyProperties(user, userBean);
		/*// 获取身份标签
		Object[] identityLables = tytUserSubService.getIdentityLables(user.getId());
		if (identityLables != null) {
			userBean.setBcarIdentityLables((String) identityLables[0]);
			userBean.setScarIdentityLables((String) identityLables[1]);
		}*/
        /*
         * 获取用户的认证信息
         */
        fetchUserIdentity(user, userBean);
        // 获取用户权益信息
        List<UserPermission> userPermissions = userPermissionService.getPermissionListByUserId(user.getId());
        userPermissions = BeanUtil.copyToList(userPermissions, UserPermission.class);
        //兼容新会员权益
        Map<String, UserPermission> userPermissionMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(userPermissions)) {
            userPermissionMap = userPermissions.stream().collect(Collectors.toMap(UserPermission::getServicePermissionTypeId, a -> a, (k1, k2) -> k1));
        }
        userPermissionService.compatibilityNewPermission(user.getId(), userPermissionMap, CAR_NUM.getTypeId(), CAR_NUM_MEAL.getTypeId());
        userPermissionService.compatibilityNewPermission(user.getId(), userPermissionMap, GOODS_NUM.getTypeId(), GOODS_NUM_NEW.getTypeId());
        userPermissions = new ArrayList<>(userPermissionMap.values());
        userBean.setUserPermissions(userPermissions);
        //获取平台交易量
        //登录优化直接取userSub
//		Integer tradeNums = infofeeDetailService.getTradeNums(user.getId());
//		userBean.setTradeNums(tradeNums==null?"0":String.valueOf(tradeNums));
        boolean isSpecialCar = carService.selectCarIsSpecialCarCount(user.getId());
        userBean.setIsSpecialCar(isSpecialCar ? 1 : 0);
        return userBean;
    }

    private void fetchUserIdentity(User user, UserBean userBean) {
        userBean.setUserIdentityStatus(user.getVerifyPhotoSign());
        userBean.setIsVerifySales(2);
        TytUserIdentityAuth userIdentityAuth = userIdentityAuthService.getByUserId(user.getId() + "");
        if (userIdentityAuth != null) {
            if (userIdentityAuth.getExamineStatus() == null || userIdentityAuth.getExamineStatus().intValue() == 0 || userIdentityAuth.getExamineStatus().intValue() == 1) {
                userBean.setIsVerifySales(0);
            }
            userBean.setUserIdentityStatus(userIdentityAuth.getIdentityStatus());
            userBean.setEnterpriseAuthStatus(userIdentityAuth.getEnterpriseAuthStatus());
        } else if (userIdentityAuth == null && user.getVerifyPhotoSign() == 3) {// 为了兼容老版本中认证失败而进行的处理，即老版本中的认证失败在新版本中体现为未认证
            userBean.setUserIdentityStatus(0);
            userBean.setEnterpriseAuthStatus(0);
        }

        //TODO 获取校验用户的销售审核状态 modify by tianjw on 2017-12-26
        if (user != null) {
            if (user.getDeliver_type_one() == null || user.getDeliver_type_one().equals("10") || user.getDeliver_type_one().equals("12") || user.getDeliver_type_one().equals("13")) {
                userBean.setIsVerifySales(1);
            }
            // 获取用户的销售审核一级身份名称开始（APP5300以上版本使用）
            String deliverTypeOne = user.getDeliver_type_one();
            fetchDeliverTypeOneName(userBean, deliverTypeOne);
        }
    }

    private void fetchDeliverTypeOneName(UserBean userBean, String deliverTypeOne) {
        if (org.apache.commons.lang.StringUtils.isEmpty(deliverTypeOne) || Integer.valueOf(deliverTypeOne).intValue() == 13) {
            userBean.setDeliverTypeOneName("未确认");
            logger.info("用户一级审核身份(身份方法),DeliverTypeOneCode = {} ,ID = {}", deliverTypeOne, userBean.getUserName());
            userBean.setDeliverTypeOneCode("13");
        } else {
//			TytSource source = null;
//			List<TytSource> sourceList = sourceService.getList(" groupCode='user_deliver_type_one' AND value= " + deliverTypeOne + " AND status=0", new PageBean(1,1));
//			if (sourceList != null && sourceList.size() == 1) {
//				source = sourceList.get(0);
//			}
            TytSource source = TytSourceUtil.getSourceName("user_deliver_type_one", deliverTypeOne);
            if (source != null) {
                String sourceName = source.getName();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(sourceName)) {
                    sourceName = sourceName.startsWith("运输公司") ? "运输公司或车队" : sourceName;
                }
                userBean.setDeliverTypeOneName(sourceName);
                userBean.setDeliverTypeOneCode(deliverTypeOne);
            }
        }
    }

    @Override
    public void sendLoginMq(HttpServletRequest request, User user, Map<String, String> loginParams, Map<String, Object> upgradeStrategyMap, Integer recordType) {
        LoginRelateOperationBean message = new LoginRelateOperationBean();
        int clientSign = Integer.parseInt(loginParams.get("clientSign"));
        Long userId = user.getId();
        message.setUserId(0L);
        message.setType("APP");
        message.setOpType(OpLog.OP_CLIENT_LOGIN);
        message.setOpContent("");
        message.setIp(StringUtil.getRealIp(request));
        message.setPlatId(clientSign);
        message.setVersion(loginParams.get("clientVersion"));
        message.setCellPhone(user.getCellPhone());
        message.setTicket(user.getTicket());
        message.setOpTime(new Timestamp(System.currentTimeMillis()));
        message.setLoginParams(loginParams);
        message.setUpgradeStrategyMap(upgradeStrategyMap);
        message.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        message.setMessageType(MqBaseMessageBean.TYTPC_LOGIN_RELATE_OPERATION);
        message.setUserId(userId);
        message.setRecordType(recordType);
        message.setNeedGiveCallPhonePermission(0);
        message.setCarLastLoginTime(user.getCarLastLoginTime());

        if ((Constant.ClientSignEnum.ANDROID_CAR.code == clientSign || Constant.ClientSignEnum.IOS_CAR.code == clientSign) && (user.getCarLastLoginTime() != null && user.getCarLastLoginTime().before(TimeUtil.addDay(-365)))) {
            message.setNeedGiveCallPhonePermission(1);
            //获取车微信小程序登录时间
            TytCarWxUserInfo carWxUserInfo = carWxUserInfoService.updateGetTytCarWxUserInfoByUserId(userId);
            if (null != carWxUserInfo && carWxUserInfo.getLastLoginTime().after(TimeUtil.addDay(-365))) {
                message.setNeedGiveCallPhonePermission(0);
            }
        }
        // 保存mq信息到数据库
        tytMqMessageService.addSaveMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message), message.getMessageType());
        tytMqMessageService.sendMqMessageDirect(message.getMessageSerailNum(), JSON.toJSONString(message), 1000L);
    }

    /**
     * 根据登录的clientSign判断是否刷新ticket
     *
     * @param userId
     * @param clientSign
     * @param isSimulatedLogin 判断是否模拟登陆0不是1是
     * @return
     */
    private String getTicket(long userId, int clientSign, String isSimulatedLogin, String reqTicket) {

        String ticket = null;
        //为了兼容安卓模拟登录问题，模拟登录时，ticket 不变，去掉 old ticket 保存.
        if ("1".equals(isSimulatedLogin) && StringUtils.isNotBlank(reqTicket)) {
            ticket = reqTicket;
        } else {
            ticket = Encoder.md5("" + userId + System.currentTimeMillis());
        }

        // 针对clientSign缓存的ticket
        String carTicketKey = UserTicketUtil.getCarTicketKey(String.valueOf(userId));
        String goodsTicketKey = UserTicketUtil.getGoodsTicketKey(String.valueOf(userId));
        String objTicketKey = UserTicketUtil.getObjTicketKey(String.valueOf(userId));

        // 过期时间
        long expire = Long.parseLong(AppConfig.getProperty("tyt.ticket.out.time.hour")) * 60 * 60;

        if (clientSign <= CLIENT_SIGN_SEPERATOR) {
			/*
			if ("1".equals(isSimulatedLogin)){
				setOldTicket(objTicketKey,"1");
			}
			*/
            cacheService.setString(carTicketKey, System.currentTimeMillis() + "");
            cacheService.setString(goodsTicketKey, System.currentTimeMillis() + "");
            cacheService.setObject(objTicketKey, ticket, expire);
        }

        if (Constant.ClientSignEnum.ANDROID_CAR.code == clientSign || Constant.ClientSignEnum.IOS_CAR.code == clientSign) {
			/*
			if ("1".equals(isSimulatedLogin)){
				setOldTicket(carTicketKey,"0");
			}
			*/
            cacheService.setString(carTicketKey, ticket, expire);
            cacheService.setObject(objTicketKey, System.currentTimeMillis() + "");
        }

        if (Constant.ClientSignEnum.ANDROID_GOODS.code == clientSign || Constant.ClientSignEnum.IOS_GOODS.code == clientSign
                || Constant.ClientSignEnum.WEB_GOODS.code == clientSign) {
			/*
			if ("1".equals(isSimulatedLogin)){
				setOldTicket(goodsTicketKey,"0");
			}
			*/
            cacheService.setString(goodsTicketKey, ticket, expire);
            cacheService.setObject(objTicketKey, System.currentTimeMillis() + "");
        }

        return ticket;
    }

    /**
     * 保存旧的ticket
     *
     * @param userKey
     * @param isOldKey 是否为老版本key0不是1是
     */
    private void setOldTicket(String userKey, String isOldKey) {
        // 过期时间
        long expire = Long.parseLong(Constant.TYT_OLD_TICKET_OUT_TIME_MIN) * 60;
        if ("0".equals(isOldKey)) {
            String oldTicket = cacheService.getString(userKey);
            logger.info("old ticket :{}", oldTicket);
            if (org.apache.commons.lang.StringUtils.isNotEmpty(oldTicket)) {
                cacheService.setString(Constant.CACHE_OLD_TICKET_KEY + userKey, oldTicket, expire);
                logger.info("保存老的ticket成功");
                logger.info("保存老的ticket成功 key:{}", Constant.CACHE_OLD_TICKET_KEY + userKey);
            }
        } else {
            Object oldTicket = cacheService.getObject(userKey);
            if (oldTicket != null) {
                cacheService.setObject(Constant.CACHE_OLD_TICKET_KEY + userKey, oldTicket, expire);
                logger.info("保存老的ticket成功");
                logger.info("保存老的ticket成功 key:{}", Constant.CACHE_OLD_TICKET_KEY + userKey);
            }
        }
    }

    @Override
    public ResultMsgBean buildLoginResult(ResultMsgBean result, int code, String msg, Object data) {
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    /**
     * 判断密码是否正确
     *
     * @param password
     * @return
     */
    private boolean password(User user, String password) {
        return Encoder.md5(user.getPassword() + user.getCellPhone()).equals(password);
    }

    /**
     * @param userCancelBean
     * @return com.tyt.model.ResultMsgBean
     * @description 关闭代扣协议
     * <AUTHOR>
     * @date 2021/12/23 16:10
     */
    @Override
    public ResultMsgBean cancelAgree(UserCancelBean userCancelBean) throws Exception {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        CancelAgreeBean cancelAgreeBean = new CancelAgreeBean();
        cancelAgreeBean.setClientSign(userCancelBean.getClientSign());
        cancelAgreeBean.setOsVersion(userCancelBean.getOsVersion());
        cancelAgreeBean.setClientVersion(userCancelBean.getClientVersion());
        cancelAgreeBean.setClientId(userCancelBean.getClientId());
        cancelAgreeBean.setTicket(userCancelBean.getTicket());
        cancelAgreeBean.setUserId(userCancelBean.getUserId());

        String result = HttpRequest.post(userCenterUrl + "/agree/cancel")
                .body(JSON.toJSONString(cancelAgreeBean))
                .execute()
                .body();
        logger.info("获取关闭代扣协议结果:【{}】", result);
        Resp resp = JSON.parseObject(result, Resp.class);
        resultMsgBean.setCode(resp.getCode());
        resultMsgBean.setMsg(resp.getMsg());
        resultMsgBean.setData(resp.getData());
        return resultMsgBean;
    }

    @Override
    public boolean deleteFromTytCellphone(String cellPhone) throws Exception {
        String delSql = "delete from tyt_cellphone where cell_phone=?";
        int c = this.executeUpdateSql(delSql, new Object[]{cellPhone});
        return c > 0;
    }

    @Override
    public void updateIdentityType(Integer identityType, Long userId) throws Exception {
        String sql = "UPDATE tyt_user SET register_identity=? WHERE id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{identityType, userId});
        // 删除并更新缓存信息
        cacheService.del(Constant.CACHE_USER_KEY + userId);
        this.getByUserId(userId);
    }

    @Override
    public void updateSelectionIdentity(Integer selectionIdentity, Integer initialNum, Integer initialCarNum, Long userId) throws Exception {
        User user = this.getByUserId(userId);
        if (user != null) {
            if (StringUtils.isBlank(user.getDeliver_type_one()) || "13".equals(user.getDeliver_type_one())) {
                String deliverTypeOne = selectionIdentity.toString();
                String sql = "UPDATE tyt_user SET deliver_type_one=? ,selection_identity=? ,initial_num=?,initial_car_num=? WHERE id=?";
                this.getBaseDao().executeUpdateSql(sql, new Object[]{deliverTypeOne, selectionIdentity, initialNum, initialCarNum, userId});
                // 删除并更新缓存信息
                cacheService.del(Constant.CACHE_USER_KEY + userId);
            } else {
                logger.info("updateSelectionIdentity，用户id为：{},用户一级审核身份为：{}", userId, StringUtils.isBlank(user.getDeliver_type_one()) ? "空" : user.getDeliver_type_one());
            }
        }
    }

    @Override
    public UserLabel getUserLabel(Long userId) {
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
        return new UserLabel(userCreditInfo != null && userCreditInfo.getUserLabelIcon() != null ? userCreditInfo.getUserLabelIcon() : 0);
    }

    @Override
    public ResultMsgBean getUserIsCancel(String cellPhone) throws Exception {
        ResultMsgBean bean = new ResultMsgBean(ResultMsgBean.OK, "查询成功");
        //查询用户是否用货app注销过
        UserCancelNoticeBean noticeBean = userCancelService.getCountByCellPhoneAndGoodsPort(cellPhone);
        if (noticeBean == null || noticeBean.getCount() < 2) { //如果用户没有在货app注销过，或者注销过一次，可正常注册
            return bean;
        }
        String allowDays = tytconfigService.getStringValue("user_cancel_allow_days", "7,45");
        String[] allowDay = allowDays.split(",");
        int day = 0;
        if (noticeBean.getCount() == 2) {
            day = Integer.parseInt(allowDay[0]);
        } else {
            day = Integer.parseInt(allowDay[1]);
        }
        Date allowDate = TimeUtil.addDay(noticeBean.getMaxDate(), day);
        if (new Date().after(allowDate)) {
            return bean;
        }
        bean.setCode(ReturnCodeConstant.REGISTER_ERROR_CODE);
        String returnMsg = StringUtils.replaceEach(ReturnCodeConstant.REGISTER_DATE_NOT_COME,
                new String[]{"${date}"},
                new String[]{TimeUtil.formatDateMonthDay(allowDate)});
        bean.setMsg(returnMsg);
        return bean;
    }

    /**
     * 查询微信小程序限制登录信息
     *
     * @param userId 用户ID
     * @return
     * @throws Exception
     */
    @Override
    public ResultMsgBean getWxMiniLoginLimit(Long userId) throws Exception {
        ResultMsgBean result = new ResultMsgBean();
        /* 根据手机号查询用户 */
        User user = this.getByUserId(userId);
        //查询用户是否存在处罚订单
        BlacklistUserOrders blacklistUserOrders = blacklistUserOrdersService.getOrdersByUserIdAndRestrictTime(user.getId(), new Date());
        if (blacklistUserOrders != null) {
            Long waybillExId = blacklistUserOrders.getWaybillExId();
            //查询异常上报订单信息
            TytTransportWaybillEx transportWaybillEx = transportWayBillExService.getExById(waybillExId);
            if (transportWaybillEx != null) {
                //运单号
                String tsOrderNo = transportWaybillEx.getTsOrderNo();
                //出发地(省市区以减号-分割开)
                String startPoint = transportWaybillEx.getStartPoint();
                //目的地(省市区以减号-分割开)
                String destPoint = transportWaybillEx.getDestPoint();
                //货物内容
                String taskContent = transportWaybillEx.getTaskContent();
                if (StringUtils.isNotBlank(taskContent) && taskContent.length() > 6) {
                    taskContent = taskContent.substring(0, 6) + "···";
                }
                //替换参数,逗号分隔
                String masterParams = tsOrderNo + "," + startPoint + "," + destPoint + "," + taskContent;

                TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号管制通知_订单, masterParams);
                result.setNoticeData(noticePopupTempl);
                result.setData(user); //为了小程序埋点使用
                return buildLoginResult(result, ReturnCodeConstant.NO_PERMISSION, null, null);
            }

        }
        // 判断用户黑名单否？
        BlacklistUser blacklistUser = blacklistUserService.getBlackByUserIdAndStatus(user.getId(), 1);
        if (user.getBlackStatus() == User.BLACKLIST_STATUS_Y && blacklistUser != null) {
            TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号管制通知_拉黑, null);
            result.setNoticeData(noticePopupTempl);
            result.setData(user); //为了小程序埋点使用
            return buildLoginResult(result, ReturnCodeConstant.NO_PERMISSION, null, null);
        }
        return buildLoginResult(result, ReturnCodeConstant.OK, "小程序登录成功", null);
    }

	/**
	 * 更新拉黑状态
	 *
	 * @param userId      用户ID
	 * @param blackStatus 拉黑状态：0-否；1-是；
	 */
	@Override
	public void updateBlackStatus(Long userId, int blackStatus) {
		String sql = "UPDATE tyt_user SET black_status = ?, mtime = ? WHERE id = ?";
		this.getBaseDao().executeUpdateSql(sql, new Object[]{blackStatus, new Date(), userId});
	}
}


