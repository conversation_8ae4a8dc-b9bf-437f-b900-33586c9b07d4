package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytChannelLog;
import com.tyt.model.TytUserMac;
import com.tyt.model.User;
import com.tyt.user.dao.TytUserMacDao;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserMacService;
import com.tyt.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service("tytUserMacService")
public class TytUserMacServiceImpl extends BaseServiceImpl<TytUserMac, Long> implements TytUserMacService {
    @Resource(name = "tytUserMacDao")
    public void setBaseDao(BaseDao<TytUserMac, Long> tytUserMacDao) {
        super.setBaseDao(tytUserMacDao);
    }

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Override
    public TytUserMac saveUserMac(User user, TytChannelLog channelLog) {
        Date nowTime = new Date();
        TytUserMac userMac = new TytUserMac();
        userMac.setMac(channelLog.getClientId());
        userMac.setUserId(channelLog.getUserId());
        userMac.setUserName(user.getTrueName());
        userMac.setCellPhone(user.getCellPhone());
        userMac.setIdcard(user.getIdCard());
        userMac.setClientType(channelLog.getPhoneType());
        userMac.setClientSign(channelLog.getClientSign());
        userMac.setCtime(nowTime);
        userMac.setMtime(nowTime);
        int num = 0; // 更新条数，0-未有任何更新，1-新增记录， 2-更新记录
        if (StringUtils.isNotBlank(userMac.getMac())) {
            num = this.getBaseDao(TytUserMacDao.class).saveUserMac(userMac);
        }
        // 发送异常登录短信
        this.sendExceptionLoginSMS(channelLog.getUserId(), num);
        return userMac;
    }

    /**
     * 发送异常登录短信，当为新增记录时，且条数大于1条，发送短信
     *
     * @param userId 用户ID
     * @param num    0-未有任何更新，1-新增记录， 2-更新记录
     */
    private void sendExceptionLoginSMS(Long userId, int num) {
        if (num == 1) { // 为新增记录时发送异常登录短信
            Integer count = this.getBaseDao(TytUserMacDao.class).getCountByUserId(userId);
            if (count > 1) { // 0-未有任何更新，1-新增记录， 2-更新记录
                // 后台配置异常登录是否发送短信，为true时发送，默认为false
                boolean isSend = Boolean.parseBoolean(tytConfigService.getStringValue("exception_login_is_send_sms", "false"));
                if (isSend) { // 为true时，发送异常登录提醒短信
                    userService.sendExceptionLoginSms(userId);
                }
            }
        }
    }
}
