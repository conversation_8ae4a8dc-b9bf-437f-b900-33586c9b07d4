package com.tyt.user.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUserCallPhone;
import com.tyt.user.service.UserCallPhoneService;
import com.tyt.util.StringUtil;

@Service("userCallPhoneService")
public class UserCallPhoneServiceImpl extends BaseServiceImpl<TytUserCallPhone, Long> implements UserCallPhoneService {
	@Resource(name = "userCallPhoneDao")
	public void setBaseDao(BaseDao<TytUserCallPhone, Long> userCallPhoneDao) {
		super.setBaseDao(userCallPhoneDao);
	}

	@Override
	public void addGetPhoneLog(String goodId, String userId, String clientSign, String clientVersion, String moduleType, Integer isCanCall) throws IllegalAccessException, InvocationTargetException {
		TytUserCallPhone userCallPhone = new TytUserCallPhone();
		userCallPhone.setTsId(Long.valueOf(goodId));//此ID为srcId，非tsId
		userCallPhone.setUserId(Long.valueOf(userId));
		userCallPhone.setCreateTime(new Date());
		userCallPhone.setClientVersion(clientVersion);
		userCallPhone.setMuduleType(moduleType);
		userCallPhone.setActionType(4);
		if (StringUtil.isNumeric(clientSign)) {
			userCallPhone.setClientSign(Integer.valueOf(clientSign));
		}
		userCallPhone.setActionType(1);
		// 保存获取电话的日志
		this.getBaseDao().insert(userCallPhone);
		TytUserCallPhone callPhoneResult = new TytUserCallPhone();
		BeanUtils.copyProperties(callPhoneResult, userCallPhone);
		/*
		 * 是否可以打电话，0可以，1不可以
		 */
		if (isCanCall.intValue() == 0) {
			callPhoneResult.setActionType(2);
		} else {
			callPhoneResult.setActionType(3);
		}
		// 保存获取电话结果的日志
		this.getBaseDao().insert(callPhoneResult);
	}

	@Override
	public void addGetPhone(String goodId, Long userId, String clientSign, String clientVersion, String moduleType, Integer isCanCall,String path){
        TytUserCallPhone userCallPhone = new TytUserCallPhone();
        userCallPhone.setTsId(Long.valueOf(goodId));//此ID为srcId，非tsId
        userCallPhone.setUserId(userId);
        userCallPhone.setCreateTime(new Date());
        userCallPhone.setClientVersion(clientVersion);
        userCallPhone.setMuduleType(moduleType);
        userCallPhone.setActionType(4);
        if (StringUtil.isNumeric(clientSign)) {
            userCallPhone.setClientSign(Integer.valueOf(clientSign));
        }
        userCallPhone.setActionType(1);
        /*
         * 是否可以打电话，0可以，1不可以
         */
        if (isCanCall.intValue() == 0) {
            userCallPhone.setActionType(2);
        } else {
            userCallPhone.setActionType(3);
        }
        if (StringUtils.isNotBlank(path)){
            userCallPhone.setPath(path);
        }
        // 保存获取电话的日志
        this.getBaseDao().insert(userCallPhone);
    }

}
