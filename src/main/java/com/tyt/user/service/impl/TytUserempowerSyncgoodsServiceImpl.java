package com.tyt.user.service.impl;

import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytUserempowerSyncgoods;
import com.tyt.plat.mapper.base.TytUserempowerSyncgoodsMapper;
import com.tyt.user.service.TytUserempowerSyncgoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @ClassName TytUserempowerSyncgoodsServiceImpl
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/10/18 11:41
 * @Verdion 1.0
 **/
@Service("tytUserempowerSyncgoodsService")
public class TytUserempowerSyncgoodsServiceImpl extends BaseServiceImpl<TytUserempowerSyncgoods,Long> implements TytUserempowerSyncgoodsService {

    @Autowired
    private TytUserempowerSyncgoodsMapper tytUserempowerSyncgoodsMapper;


    @Override
    public TytUserempowerSyncgoods getEmpowerUser(Long userId) {

        TytUserempowerSyncgoods userempowerSyncgoods = tytUserempowerSyncgoodsMapper.selectUserEmpower(userId);
        return userempowerSyncgoods;
    }

    @Override
    public ResultMsgBean updateEmpowerUser(Long userId, ResultMsgBean resultMsgBean) {
        TytUserempowerSyncgoods userempowerSyncgoods = tytUserempowerSyncgoodsMapper.selectUserEmpower(userId);
        userempowerSyncgoods.setStatus(0);
        userempowerSyncgoods.setMtime(new Date());
        userempowerSyncgoods.setSyncTime(new Date());
        int i = tytUserempowerSyncgoodsMapper.updateByPrimaryKeySelective(userempowerSyncgoods);
        if(i == 0){
            resultMsgBean.setCode(410);
            resultMsgBean.setMsg("修改失败");
        }
        return resultMsgBean;
    }
}
