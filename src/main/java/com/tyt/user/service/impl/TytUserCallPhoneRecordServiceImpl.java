package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUserCallPhoneRecord;
import com.tyt.model.TytUserSub;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserCallPhoneRecordService;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

/**
 * 
 * <AUTHOR>
 * @date 2017-3-9下午12:56:56
 * @description
 */
@SuppressWarnings("deprecation")
@Service("tytUserCallPhoneRecordService")
public class TytUserCallPhoneRecordServiceImpl extends BaseServiceImpl<TytUserCallPhoneRecord, Long> implements TytUserCallPhoneRecordService {
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "tytUserCallPhoneRecordDao")
	public void setBaseDao(BaseDao<TytUserCallPhoneRecord, Long> tytUserCallPhoneRecordDao) {
		super.setBaseDao(tytUserCallPhoneRecordDao);
	}

	@Override
	public TytUserCallPhoneRecord getBySrcMsgIdAndUserId(Long srcMsgId, String userId) {
		String sql = "select id from tyt_user_call_phone_record tucpr where tucpr.`ts_id`=? and tucpr.`user_id`=? and LEFT(tucpr.`ctime`, 10)=?";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		String todayInDate = TimeUtil.formatDate(new Date());
		List<TytUserCallPhoneRecord> userCallPhoneRecords = this.getBaseDao().search(sql, scalarMap, TytUserCallPhoneRecord.class, new Object[] { srcMsgId, userId, todayInDate });
		return userCallPhoneRecords.size() == 1 ? userCallPhoneRecords.get(0) : null;
	}
	
	@Override
	public List<TytUserCallPhoneRecord> getByUserId(String userId) {
		String sql = "select id from tyt_user_call_phone_record tucpr where tucpr.`user_id`=? ";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		List<TytUserCallPhoneRecord> userCallPhoneRecords = this.getBaseDao().search(sql, scalarMap, TytUserCallPhoneRecord.class, new Object[]{userId});
		return userCallPhoneRecords == null ? new ArrayList<TytUserCallPhoneRecord>() : userCallPhoneRecords;
	}

	@Override
	public TytUserCallPhoneRecord getBySrcMsgIdAndUserIdWithRights(Long srcMsgId, TytUserSub userSub) {
		String sql = "select id from tyt_user_call_phone_record tucpr where tucpr.`ts_id`=? and tucpr.`user_id`=? and tucpr.`level`=? and tucpr.`ctime` > ?";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		String todayInDate = TimeUtil.formatDate(new Date());
		List<TytUserCallPhoneRecord> userCallPhoneRecords = this.getBaseDao().search(sql, scalarMap, TytUserCallPhoneRecord.class,
				new Object[] { srcMsgId, userSub.getUserId(), userSub.getUserGroup(), todayInDate });
		return userCallPhoneRecords.size() == 1 ? userCallPhoneRecords.get(0) : null;
	}

	@Override
	public int getCountByUserGroup(Integer userGroup, Long userId) throws Exception {
		// 查询21天前的
		int allowCallNum = tytConfigService.getIntValue("rights_normal_call_num", 20);
		Date earliestDate = TimeUtil.addDay(new Date(), -(allowCallNum + 1));
		BigInteger count = this.getBaseDao().query(
				"SELECT COUNT(*) FROM tyt.`tyt_user_call_phone_record` tuc WHERE tuc.`user_id`=? AND tuc.`level`=? AND tuc.`ctime`>? ",
				new Object[]{userId, userGroup, earliestDate});
		return count.intValue();
	}

	@Override
	public int getTodayCountByUserGroup(Integer userGroup, Long userId) {
		BigInteger count = this.getBaseDao().query(
					"SELECT COUNT(*) FROM tyt.`tyt_user_call_phone_record` tuc WHERE tuc.`user_id`=? AND tuc.`level`=? AND tuc.`ctime`>? ",
					new Object[] { userId, userGroup, TimeUtil.formatDate(new Date()) });
		return count.intValue();
	}

	@Override
    public void addPhoneRecord(Long srcMsgId, Integer level, Long userId,String path,String platId) {
        TytUserCallPhoneRecord record = new TytUserCallPhoneRecord();
        record.setCtime(new Date());
        record.setLevel(Long.valueOf(level));
        record.setTsId(srcMsgId);
        record.setUserId(userId);
        record.setPlatId(platId);
        if (StringUtils.isNotBlank(path)){
            record.setPath(path);
        }
        this.add(record);
    }

    @Override
    public TytUserCallPhoneRecord getPhoneBySrcMsgIdAndUserId(Long srcMsgId, String userId) {
        String sql = "select id from tyt_user_call_phone_record tucpr where tucpr.`ts_id`=? and tucpr.`user_id`=?";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        List<TytUserCallPhoneRecord> userCallPhoneRecords = this.getBaseDao().search(sql, scalarMap, TytUserCallPhoneRecord.class, new Object[] { srcMsgId, userId});
        return userCallPhoneRecords.size() > 0 ? userCallPhoneRecords.get(0) : null;
    }

	@Override
	public Integer getByUserIdAndDate(Long userId, Date date){
		BigInteger count = this.getBaseDao().query(
				"SELECT COUNT(*) FROM tyt.`tyt_user_call_phone_record` tuc WHERE tuc.`user_id`=?  AND tuc.`ctime`>= ?  AND tuc.`ctime`<= ?",
				new Object[] { userId,  date,  new Date()});
		return count.intValue();
	}

	@Override
	public int getCallStatusByUserIdAndTsId(Long srcMsgId, Long userId) {
		String sql = "select COUNT(*) from tyt_user_call_phone_record tucpr where tucpr.`ts_id`=? and tucpr.`user_id`=? and tucpr.ctime>?";
		BigInteger count = this.getBaseDao().query(sql,new Object[] { srcMsgId, userId, TimeUtil.weeHours(new Date(),0) });
		return count.intValue() == 0 ? 0 : 1;
	}

	@Override
	public int getDistinctTsCountByUserId(Long userId) {
		String sql = "select count(distinct ts_id) from tyt_user_call_phone_record tucpr where tucpr.`user_id`=?";
		BigInteger count = this.getBaseDao().query(sql, new Object[]{userId});
		return count.intValue();
	}

}
