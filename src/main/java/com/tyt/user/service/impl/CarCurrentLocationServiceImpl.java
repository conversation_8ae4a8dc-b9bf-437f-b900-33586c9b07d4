package com.tyt.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCarCurrentLocation;
import com.tyt.plat.client.user.ApiTraceLocationClient;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.entity.base.CurrentLocationVO;
import com.tyt.plat.vo.user.EnterpriseModifyCheckVo;
import com.tyt.service.common.common.HttpClientFactory;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.CarCurrentLocationService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.CircuitBreakerService;
import com.tyt.util.Constant;
import com.tyt.util.MD5Util;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service(value = "carCurrentLocationService")
public class CarCurrentLocationServiceImpl extends BaseServiceImpl<TytCarCurrentLocation, Long> implements CarCurrentLocationService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Autowired
    private CircuitBreakerService circuitBreakerService;

    @Autowired
    private ApiTraceLocationClient apiTraceLocationClient;

    private static CloseableHttpClient httpClient = getHttpclientInstance();

    private static CloseableHttpClient getHttpclientInstance() {
        if (httpClient == null) {
            httpClient = HttpClientFactory.getHttpClientWithRetry();
        }
        return httpClient;
    }

    @Resource(name = "carCurrentLocationDao")
    public void setBaseDao(BaseDao<TytCarCurrentLocation, Long> carCurrentLocationDao) {
        super.setBaseDao(carCurrentLocationDao);
    }

    @Override
    public TytCarCurrentLocation getByCarHeadLicence(String licensePlate) {
        char licenseCity = licensePlate.charAt(0);
        String licenseNo = licensePlate.substring(1, licensePlate.length());
        List<TytCarCurrentLocation> carCurrentLocationList = this.getBaseDao().find("from TytCarCurrentLocation where headCity=? and headNo=? and status=?  ", new Object[]{licenseCity, licenseNo, (byte) 1});
        return (carCurrentLocationList != null && carCurrentLocationList.size() >= 1) ? carCurrentLocationList.get(0) : null;
    }

    @Override
    public TytCarCurrentLocation getByCarHead(String city, String no) {
        List<TytCarCurrentLocation> carCurrentLocationList = this.getBaseDao().find("from TytCarCurrentLocation where headCity=? and headNo=? and status=?  ", new Object[]{city.charAt(0), no, (byte) 1});
        return (carCurrentLocationList != null && carCurrentLocationList.size() >= 1) ? carCurrentLocationList.get(0) : null;
    }

    @Override
    public Object getCurrentLocation(String carHeadNo) throws Exception {
        try{
            JSONObject locationData = RedisUtil.getObject(Constant.CACHE_CAR_CURRENT_LOCATION + carHeadNo);
            logger.info("getCurrentLocation from cache, carHeadNo: " + carHeadNo + ", locationData:{}",JSON.toJSONString(locationData));
            if (locationData != null) {
                logger.info("2getCurrentLocation from cache, carHeadNo: " + carHeadNo + ", locationData:{}",JSON.toJSONString(locationData));
                return locationData;
            }
            Response<InternalWebResult<List<CurrentLocationVO>>> execute = apiTraceLocationClient.getCarRealTimeLocation(carHeadNo, null).execute();
            List<CurrentLocationVO> currentLocationVOList = InternalClientUtil.getDataDetail(execute);
            logger.info("getCurrentLocation from client, carHeadNo: " + carHeadNo + ", currentLocationVOList:{} " ,JSON.toJSONString(currentLocationVOList));
            if(CollectionUtils.isNotEmpty(currentLocationVOList)){
                CurrentLocationVO currentLocationVO = currentLocationVOList.get(0);
                locationData = (JSONObject) JSON.toJSON(currentLocationVO);
                RedisUtil.setObject(Constant.CACHE_CAR_CURRENT_LOCATION + carHeadNo, locationData, Constant.CACHE_EXPIRE_TIME_5MIN);

            }
            return locationData;
        }catch (Exception e){
            logger.error("getCurrentLocation error", e);
            return null;
        }

        // 缓存不存在去BI服务器请求数据
//        String biBase = tytConfigService.getStringValue(Constant.SYNC_CAR_BI_HTTP_BASE_KEY, "http://*************");
//        String biPath = "/idc/index.php/apibi/ZhiyunRT/getCarRealTimeLocation";
//        String biPrivateKey = tytConfigService.getStringValue(Constant.SYNC_CAR_BI_PRIVATEKEY_KEY, "9d13dcadb5dbff5d0a93b6389ac5c89a");
//        String biApiTime = TimeUtil.formatDateTime(new Date());
//        StringBuffer sb = new StringBuffer();
//        String signOriginal = sb.append(biPrivateKey).append(biPath).append("?api_time=").append(biApiTime)
//                .append("&carHeadNo=").append(carHeadNo).toString();
//        logger.info("signOriginal--" + signOriginal);
//        String sign = MD5Util.GetMD5Code(signOriginal);
//        logger.info("sign--" + sign);
//
//        sb.setLength(0);
//        sb.append(biBase).append(biPath).append("?api_time=").append(URLEncoder.encode(biApiTime, "utf-8"));
//        sb.append("&carHeadNo=").append(carHeadNo);
//        sb.append("&api_sign=").append(sign);
//        String url = sb.toString();
//        logger.info("bi service request getCurrentLocation url is: " + url);
//
//        HttpGet httpGet = new HttpGet(url);
//
//        //熔断
//        String strContent = circuitBreakerService.executeWithCircuitBreaker(
//                "ZhiyunRT_getCarRealTimeLocation", () -> {
//                    try {
//                        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
//                            if (response.getStatusLine().getStatusCode() != 200) {
//                                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
//                                return null;
//                            }
//                            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
//                        }
//                    } catch (IOException e) {
//                        throw new RuntimeException(e);
//                    }
//                });
//
//        try {
//            if (StringUtils.isNotBlank(strContent)) {
//                JSONObject jsonObject = JSON.parseObject(strContent);
//                if (jsonObject.getInteger("code") == 200) {
//                    JSONArray arrayData = jsonObject.getJSONArray("data");
//                    if (CollectionUtils.isNotEmpty(arrayData)) {
//                        locationData = arrayData.getJSONObject(0);
//                        RedisUtil.setObject(Constant.CACHE_CAR_CURRENT_LOCATION + carHeadNo, locationData, Constant.CACHE_EXPIRE_TIME_5MIN);
//                    }
//                }
//            }
//        } catch (Exception e) {
//            logger.error("BI服务器-请求车辆位置接口异常：", e);
//            throw new Exception();
//        }
//        return locationData;
    }


    @Override
    public List<CurrentLocationVO> getCurrentLocations(String heads)throws Exception{
        try {
            List<CurrentLocationVO> locationData = RedisUtil.getObject(Constant.CACHE_CAR_CURRENT_LOCATION + heads);
            if (locationData != null) {
                return locationData;
            }
            Response<InternalWebResult<List<CurrentLocationVO>>> execute = apiTraceLocationClient.getCarRealTimeLocation(heads, null).execute();
            locationData = InternalClientUtil.getDataDetail(execute);
            if (CollectionUtils.isNotEmpty(locationData)) {
                RedisUtil.setObject(Constant.CACHE_CAR_CURRENT_LOCATION + heads, locationData, Constant.CACHE_EXPIRE_TIME_5MIN);
            }
            return locationData;
        }catch (Exception e){
            logger.error("getCurrentLocations error", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<CurrentLocationVO> getZJCurrentLocations(String heads){
        try {
            List<CurrentLocationVO> locationData = RedisUtil.getObject(Constant.CACHE_CAR_CURRENT_LOCATION + heads);
            if (locationData != null) {
                return locationData;
            }
            Response<InternalWebResult<List<CurrentLocationVO>>> execute = apiTraceLocationClient.getCarZJRealTimeLocation(heads, null).execute();
            locationData = InternalClientUtil.getDataDetail(execute);
            if (CollectionUtils.isNotEmpty(locationData)) {
                RedisUtil.setObject(Constant.CACHE_CAR_CURRENT_LOCATION + heads, locationData, Constant.CACHE_EXPIRE_TIME_5MIN);
            }
            return locationData;
        }catch (Exception e){
            logger.error("getCurrentLocations error", e);
            return new ArrayList<>();
        }
    }
}
