package com.tyt.user.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.car.service.CarDetailHeadService;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.common.service.TytMqRecommMessageService;
import com.tyt.config.util.AppConfig;
import com.tyt.dispatch.owner.service.DispatchOwnerService;
import com.tyt.driver.service.TytCarDriverArchivesService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqGuessLikeMsg;
import com.tyt.infofee.enums.InvoiceServiceProviderEnum;
import com.tyt.invoicetransportconfiglog.enums.InvoiceServiceTypeEnum;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.CarDetailHeadMapper;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.openapi.service.ScheduleCarService;
import com.tyt.plat.biz.invoice.pojo.DriverDetailVO;
import com.tyt.plat.biz.invoice.serivce.IInvoiceDriverService;
import com.tyt.plat.client.user.ApiInvoiceCapacityClient;
import com.tyt.plat.client.user.dto.CarCheckDTO;
import com.tyt.plat.client.user.vo.CarCheckRpcVO;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.BlockCarStatusEnum;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.block.TytBlockConfigService;
import com.tyt.plat.vo.ocr.RoadTransportBackOcrRpcVo;
import com.tyt.plat.vo.ocr.VehicleLicenseBackVo;
import com.tyt.plat.vo.ocr.VehicleLicenseFrontVo;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.bean.*;
import com.tyt.user.dao.CarDao;
import com.tyt.user.enums.CarInvoiceStatusEnum;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.querybean.UserCarBean;
import com.tyt.user.service.*;
import com.tyt.util.*;
import com.tytrecommend.model.TytPreference;
import com.tytrecommend.model.TytPreferenceCar;
import com.tytrecommend.recommend.bean.MqMsgCacheInitBean;
import com.tytrecommend.recommend.service.PreferCarService;
import com.tytrecommend.recommend.service.PreferService;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service("carService")
public class CarServiceImpl extends BaseServiceImpl<Car, Long> implements CarService {

	public  final static Logger logger = LoggerFactory.getLogger(CarServiceImpl.class);

	@Resource(name = "carDao")
	public void setBaseDao(BaseDao<Car, Long> CarDao) {
		super.setBaseDao(CarDao);
	}

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "tytMqRecommMessService")
	private TytMqRecommMessageService tytMqRecommMessService;

	@Resource(name = "preferCarService")
	private PreferCarService preferCarService;

	@Resource(name = "preferService")
	private PreferService preferService;

	@Resource(name = "carLogService")
	private CarLogService carLogService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityService;

	@Resource(name = "scheduleCarService")
	private ScheduleCarService scheduleCarService;

	@Resource(name = "carDetailTailService")
	private CarDetailTailService carDetailTailService;

	@Resource(name = "carDetailHeadService")
	private CarDetailHeadService carDetailHeadService;

	@Resource(name = "tytCarDriverArchivesService")
	private TytCarDriverArchivesService tytCarDriverArchivesService;
	@Resource(name = "carCurrentLocationService")
	private CarCurrentLocationService carCurrentLocationService;
	@Autowired
	private CarDetailHeadMapper carDetailHeadMapper;

	@Autowired
	private IInvoiceDriverService invoiceDriverService;

	@Autowired
	private SuperiorCarSignService superiorCarSignService;
	@Autowired
	private CommonApiService commonApiService;
	@Autowired
	private TytCarOwnerAuthMapper tytCarOwnerAuthMapper;

	@Autowired
	private TytInvoiceDriverMapper tytInvoiceDriverMapper;

	@Autowired
	private TytCarTypeLincenseMapper tytCarTypeLincenseMapper;

	@Autowired
	private TytCarTypeMapper tytCarTypeMapper;

	@Autowired
	private TytBlockConfigService tytBlockConfigService;

	@Autowired
	private TytTransportOrdersMapper transportOrdersMapper;

	@Autowired
	private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;

	@Autowired
	private DispatchOwnerService dispatchOwnerService;

	@Resource(name = "tytNoticePopupTemplService")
	private TytNoticePopupTemplService tytNoticePopupTemplService;


	@Autowired
	private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

	@Autowired
	private TytCarMapper tytCarMapper;

	@Resource(name = "tytUserIdentityAuthService")
	private TytUserIdentityAuthService identityAuthService;

	@Autowired
	private TytSigningCarMapper tytSigningCarMapper;

	@Autowired
	private TytSigningCarInfoMapper tytSigningCarInfoMapper;

	@Autowired
	private TytSigningDriverBlackMapper tytSigningDriverBlackMapper;

	@Autowired
	private TytInvoiceTransportConfigLogMapper tytInvoiceTransportConfigLogMapper;

	@Autowired
	private TytDispatchCooperativeMapper tytDispatchCooperativeMapper;

	private static String  globalCarLocationRefreshOnoff ="global_car_location_refresh_onoff";

	public static final String CAR_LIST_LOCATION = "carListLocation";

	private static final int TRACTOR = 1; //牵引车
	private static final int SINGLE_CAR = 2; //单机板

	@SuppressWarnings("null")
	@Override
	public List<QueryCar> getQueryAll(Long userId, String auth) {
		List<QueryCar> queryCars= ((CarDao) (this.getBaseDao())).getQueryAll(userId, auth);
		if(queryCars==null&&queryCars.size()<1){
			return null;
		}
		for(QueryCar car : queryCars){
			car.setCurrentTime(new Date());
			//如果是认证成功的重新上传的更改
			if("3".equals(car.getAuth())){
				car.setAuth("0");
			}
			if(StringUtils.isNotEmpty(car.getLength())){
				if(car.getLength().lastIndexOf("00")>0){
					car.setLength(String.valueOf(Integer.valueOf(car.getLength())/100));
				}else
					car.setLength(String.valueOf(Double.valueOf(car.getLength())/100d));
			}
			if(StringUtils.isNotEmpty(car.getWide())){
				if(car.getWide().lastIndexOf("00")>0){
					car.setWide(String.valueOf(Integer.valueOf(car.getWide())/100));
				}else
					car.setWide(String.valueOf(Double.valueOf(car.getWide())/100d));
			}

			if(StringUtils.isNotEmpty(car.getHigh())){
				if(car.getHigh().lastIndexOf("00")>0){
					car.setHigh(String.valueOf(Integer.valueOf(car.getHigh())/100));
				}else
					car.setHigh(String.valueOf(Double.valueOf(car.getHigh())/100d));
			}
			if(StringUtils.isNotEmpty(car.getEndWeight())){
				if(car.getEndWeight().lastIndexOf("00")>0){
					car.setEndWeight(String.valueOf(Integer.valueOf(car.getEndWeight())/100));
				}else
					car.setEndWeight(String.valueOf(Double.valueOf(car.getEndWeight())/100d));
			}
			if(StringUtils.isNotEmpty(car.getBeginWeight())){
				if(car.getBeginWeight().lastIndexOf("00")>0){
					car.setBeginWeight(String.valueOf(Integer.valueOf(car.getBeginWeight())/100));
				}else
					car.setBeginWeight(String.valueOf(Double.valueOf(car.getBeginWeight())/100d));
			}
			//加载偏好车型
			if(car.getPreId()!=null){
				StringBuffer goodType = new StringBuffer();
				BigInteger preId=car.getPreId();
				//获得偏好车型
				List<TytPreferenceCar> cars = preferCarService.newgetPreferCar(preId.longValue());
				if(cars!=null){
					for(TytPreferenceCar preferCar :cars){
						goodType.append(preferCar.getCarName()+"、");
					}
				}
				if(StringUtils.isNotBlank(goodType.toString()))
					car.setGoodType(goodType.toString().substring(0, goodType.toString().length()-1));
			}
		}
		return queryCars;

	}

	@Override
	public boolean get(Map<String, String> params) throws Exception {
		Long userId = Long.parseLong(params.get("userId"));
		String headCity = params.get("headCity");
		String headNo = params.get("headNo");
		String tailCity = params.get("tailCity");
		String tailNo = params.get("tailNo");
		StringBuffer sql = new StringBuffer();
		sql.append(" entity.userId=" + userId).append(" and entity.headCity='").append(headCity + "'").append(" and entity.headNo='").append(headNo + "'").append(" and entity.tailCity='").append(tailCity + "'").append(" and entity.tailNo='").append(tailNo + "'")
				.append(" and entity.isDelete='").append(1 + "'");
		//modify by tianjw on 2017-12-08 车辆信息是否重复验证：之前只验证认证成功的，现改为验证所有认证状态，去除车辆验证状态条件过滤
//		sql.append(" and entity.auth='").append(1 + "'");
		List<Car> tels = this.getList(sql.toString(), null);
		if(tels==null || !(tels.size()>0)){
			return false;
		}
		//排除 当前修改的 id
		if(params.get("id")!=null){
			for(Car car :tels){
				if(car.getId()!=Long.parseLong(params.get("id"))){
					return true;
				}
			}
			return false;
		}
		return true;
	}


	@Override
	public Car getCarByParams(Map<String, String> params) throws Exception {
		Long userId = Long.parseLong(params.get("userId"));
		String headCity = params.get("headCity");
		String headNo = params.get("headNo");
		String tailCity = params.get("tailCity");
		String tailNo = params.get("tailNo");
		StringBuffer sql = new StringBuffer();
		sql.append(" entity.userId=" + userId).append(" and entity.headCity='").append(headCity + "'").append(" and entity.headNo='").append(headNo + "'").append(" and entity.tailCity='").append(tailCity + "'").append(" and entity.tailNo='").append(tailNo + "'")
				.append(" and entity.isDelete='").append(1 + "'");
		//modify by tianjw on 2017-12-08 车辆信息是否重复验证：之前只验证认证成功的，现改为验证所有认证状态，去除车辆验证状态条件过滤
//		sql.append(" and entity.auth='").append(1 + "'");
		List<Car> tels = this.getList(sql.toString(), null);
		if(tels==null || !(tels.size()>0)){
			return null;
		}
		return tels.get(0);
	}

	@Override
	public Long addCar(Car car) throws Exception {
		return (Long) this.getBaseDao().insertSave(car);

	}

	@Override
	public boolean isExitEnabled(Long userId) throws Exception {
		return ((CarDao) (this.getBaseDao())).isExitEnabled(userId);
	}

	@SuppressWarnings("deprecation")
	@Override
	public boolean isCarBlongToUser(Long carId, Long userId) {
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("carId", carId);
		Map<String, org.hibernate.type.Type> scalrMap = new HashMap<String, org.hibernate.type.Type>();
		scalrMap.put("id", Hibernate.LONG);
		String sql = "select tc.`user_id` AS 'id' from tyt_car tc where tc.`id`=:carId";
		User user = this.getBaseDao().queryByMap(sql, paramsMap, User.class, scalrMap);
		System.out.println("user.getId():" + user.getId() + "userId:" + userId);
		return user != null && user.getId().equals(userId);
	}

	@Override
	public void updatePreferNew(Long carId){
		String updateOnoffSql = "update tyt_recommend.tyt_preference set find_good_onoff = 0 where car_id=?";
		this.getBaseDao().executeUpdateSql(updateOnoffSql, new Object[] { carId });
		//同时更改new偏好表的找货开关状态
		String updateOnoffNewSql = "update tyt_recommend.tyt_preference_new set is_auth_update=2,update_onoff_time=now() where car_id=?";
		((CarDao) (this.getBaseDao())).executeUpdateSql(updateOnoffNewSql, new Object[] { carId });
	}

	@Override
	public void deleteById(Long carId,String deleteReason) throws IllegalAccessException, InvocationTargetException {
		Car car = this.getBaseDao().findById(carId);
		this.getBaseDao().executeUpdateSql("update tyt_car tc set tc.`is_delete`=2 , tc.delete_reason=? where tc.`id`=?", new Object[] { deleteReason,carId });
		String updateOnoffSql = "update tyt_recommend.tyt_preference set find_good_onoff = 0 where car_id=?";
		this.getBaseDao().executeUpdateSql(updateOnoffSql, new Object[] { car.getId() });
		//同时更改new偏好表的找货开关状态
		String updateOnoffNewSql = "update tyt_recommend.tyt_preference_new set find_good_onoff = 0 ,is_auth_update=2,update_onoff_time=now() where car_id=?";
		((CarDao) (this.getBaseDao())).executeUpdateSql(updateOnoffNewSql, new Object[] { car.getId() });
		//同步删除车主认证信息
		this.deleteByCarOwnerAuth(carId);
		//保存删除记录

		if(car!=null&&"1".equals((car.getAuth()))){
			CarLog carLog = new CarLog();
			Car carNew = new Car();
			BeanUtils.copyProperties(carNew, car);
			carNew.setId(null);
			BeanUtils.copyProperties(carLog, carNew);
			carLog.setRecordTime(new Date());
			carLog.setCarId(carId);
			carLog.setIsDelete(2);
			carLog.setDeleteReason(deleteReason);
			carLog.setIsDisplay(1);
			carLogService.add(carLog);
		}
		//判断user表中iscar状态是否需要改变
		try {
			Long userId = car.getUserId();
			User user = userService.getByUserId(userId);
			if (user!=null) {
				int carAuth = this.isCarAuth(user.getId(), "1");
				if (carAuth>0 && !"1".equals(user.getIsCar())) {
					user.setIsCar("1");
					user.setMtime(new Timestamp(System.currentTimeMillis()));
					userService.update(user);
					cacheService.del(Constant.CACHE_USER_KEY + user.getId());
					cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
				}
				if (carAuth<=0 && "1".equals(user.getIsCar())) {
					user.setIsCar("0");
					user.setMtime(new Timestamp(System.currentTimeMillis()));
					userService.update(user);
					cacheService.del(Constant.CACHE_USER_KEY + user.getId());
					cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//删除车辆更新优车签约状态
		updateSuperiorCarSignStatus(car);
	}

	@Override
	public void deleteCar(Long carId, String deleteReason) {
		this.getBaseDao().executeUpdateSql("update tyt_car tc set tc.`is_delete`=2 , tc.delete_reason=? where tc.`id`=?", new Object[] { deleteReason,carId });
	}

	@Override
	public void deleteByCarOwnerAuth(Long carId) {
		TytCarOwnerAuth tytCarOwnerAuth = tytCarOwnerAuthMapper.selectByCarId(carId);
		if (Objects.nonNull(tytCarOwnerAuth)){
			this.getBaseDao().executeUpdateSql("update tyt_car_owner_auth set is_delete = 0 where car_id = ?",new Object[]{carId});
			this.getBaseDao().executeUpdateSql("update tyt_car_owner_auth_proof set is_delete = 0 where car_owner_auth_id = ?",new Object[]{tytCarOwnerAuth.getId()});
		}
	}


	private void updateSuperiorCarSignStatus(Car car) {
		if (car!=null) {
			TytSuperiorCarSign signByUserId = superiorCarSignService.getSuperiorCarSign(car.getUserId());
			if (signByUserId!=null&&signByUserId.getSignStatus()!=null&&signByUserId.getSignStatus()==1) {
				String sql = "SELECT * FROM `tyt_car` WHERE user_id = ?";
				final Object[] params = {
						car.getUserId()
				};
				List<Car> cars = this.getBaseDao().queryForList(sql, params);
				if (cars!=null&&cars.size()>0) {
					List<Car> collect = cars.stream().filter(c -> c.getId()!=car.getId()&&c.getIsDelete()==1&&"1".equals(c.getAuth())).collect(Collectors.toList());
					if (collect.size()==0) {
						//正常状态删除车辆则更新为签约未审核
						signByUserId.setSignStatus(0);
						signByUserId.setUpdateTime(new Date());
						superiorCarSignService.update(signByUserId);
					}
				}
			}

		}
	}

	@Override
	public int isCarAuth(Long userId, String auth) {
		List<Object> listObject = new ArrayList<Object>();
		StringBuffer sbCount = new StringBuffer("SELECT count(*) FROM tyt_car c WHERE  c.user_id=? and c.auth=?  and is_delete=?");
		listObject.add(userId);
		listObject.add(auth);
		listObject.add(1);
		BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), listObject.toArray());
		return rowCount.intValue();
	}

	/**
	 * 获得当前用户最大的排序新增的时候追加
	 */

	@Override
	public BigInteger getMaxSort(Long userId) {
		String sql = "select max(sort) from tyt_car where user_id=?";
		BigInteger count = this.getBaseDao().query(sql, new Object[] { userId});
		return count;
	}
/**
 * 获得单个车辆信息(包括车辆偏好信息)
 */
@SuppressWarnings("deprecation")
@Override
public QueryCar getQueryCar(Long carId) {
	String sql = "select car.id id,car.user_id userId,head_city headCity ,head_no headNo, tail_city tailCity,tail_no tailNo, head_driving_url headDrivingUrl,tail_driving_url tailDrivingUrl,auth, failure_reason failureReason, head_auth_status headAuthStatus, head_failure_reason "
			+ "headFailureReason, tail_auth_status tailAuthStatus,tail_failure_reason tailFailureReason, sort ,car.find_good_onoff findGoodOnOff ,pre.id preId,start_provinc startProvinc ,start_city startCity, start_area startArea,dest_provinc destProvinc,dest_city destCity,dest_area destArea, begin_weight beginWeight ,end_weight endWeight, "
			+ "pre.length length,pre.wide wide,pre.high high ,car.update_time updateTime from tyt_car "
			+ " car LEFT JOIN tyt_recommend.tyt_preference pre on car.id=pre.car_id where car.id=?";
	Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
	scalarMap.put("id", Hibernate.BIG_INTEGER);
	scalarMap.put("headCity", Hibernate.STRING);
	scalarMap.put("headNo", Hibernate.STRING);
	scalarMap.put("tailCity", Hibernate.STRING);
	scalarMap.put("tailNo", Hibernate.STRING);
	scalarMap.put("auth", Hibernate.STRING);
	scalarMap.put("userId", Hibernate.BIG_INTEGER);
	scalarMap.put("headDrivingUrl", Hibernate.STRING);
	scalarMap.put("tailDrivingUrl", Hibernate.STRING);
	scalarMap.put("failureReason", Hibernate.STRING);
	scalarMap.put("headAuthStatus", Hibernate.SHORT);
	scalarMap.put("headFailureReason", Hibernate.STRING);
	scalarMap.put("tailAuthStatus", Hibernate.SHORT);
	scalarMap.put("tailFailureReason", Hibernate.STRING);
	scalarMap.put("findGoodOnOff", Hibernate.STRING);
	scalarMap.put("sort", Hibernate.BIG_INTEGER);
	scalarMap.put("startProvinc", Hibernate.STRING);
	scalarMap.put("startCity", Hibernate.STRING);
	scalarMap.put("startArea", Hibernate.STRING);
	scalarMap.put("destProvinc", Hibernate.STRING);
	scalarMap.put("destCity", Hibernate.STRING);
	scalarMap.put("destArea", Hibernate.STRING);
	scalarMap.put("beginWeight", Hibernate.STRING);
	scalarMap.put("endWeight", Hibernate.STRING);
	scalarMap.put("length", Hibernate.STRING);
	scalarMap.put("wide", Hibernate.STRING);
	scalarMap.put("high", Hibernate.STRING);
	scalarMap.put("preId", Hibernate.BIG_INTEGER);
	scalarMap.put("updateTime", Hibernate.TIMESTAMP);

	QueryCar car =  this.getBaseDao().search(sql, scalarMap,
			QueryCar.class, new Object[]{carId}).get(0);
	car.setCurrentTime(new Date());
	BigInteger preId;
	StringBuffer goodType = new StringBuffer();
	if(car!=null && car.getPreId()!=null){
		preId=car.getPreId();
		//获得偏好车型
		List<TytPreferenceCar> cars = preferCarService.newgetPreferCar(preId.longValue());
		if(cars!=null){
			for(TytPreferenceCar preferCar :cars){
				goodType.append(preferCar.getCarName()+"、");
			}
		}
		if(StringUtils.isNotEmpty(goodType.toString()))
			car.setGoodType(goodType.toString().substring(0, goodType.toString().length()-1));

	}
	if(car!=null){
		//如果是认证成功的重新上传的更改
		if("3".equals(car.getAuth())){
			car.setAuth("0");
		}
		if(StringUtils.isNotEmpty(car.getLength())){
			if(car.getLength().lastIndexOf("00")>0){
				car.setLength(String.valueOf(Integer.valueOf(car.getLength())/100));
			}else
				car.setLength(String.valueOf(Double.valueOf(car.getLength())/100d));
		}
		if(StringUtils.isNotEmpty(car.getWide())){
			if(car.getWide().lastIndexOf("00")>0){
				car.setWide(String.valueOf(Integer.valueOf(car.getWide())/100));
			}else
				car.setWide(String.valueOf(Double.valueOf(car.getWide())/100d));
		}

		if(StringUtils.isNotEmpty(car.getHigh())){
			if(car.getHigh().lastIndexOf("00")>0){
				car.setHigh(String.valueOf(Integer.valueOf(car.getHigh())/100));
			}else
				car.setHigh(String.valueOf(Double.valueOf(car.getHigh())/100d));
		}
		if(StringUtils.isNotEmpty(car.getEndWeight())){
			if(car.getEndWeight().lastIndexOf("00")>0){
				car.setEndWeight(String.valueOf(Integer.valueOf(car.getEndWeight())/100));
			}else
				car.setEndWeight(String.valueOf(Double.valueOf(car.getEndWeight())/100d));
		}
		if(StringUtils.isNotEmpty(car.getBeginWeight())){
			if(car.getBeginWeight().lastIndexOf("00")>0){
				car.setBeginWeight(String.valueOf(Integer.valueOf(car.getBeginWeight())/100));
			}else
				car.setBeginWeight(String.valueOf(Double.valueOf(car.getBeginWeight())/100d));
		}
	}
	return car;
}

@Override
public boolean updateSort(String carIds) {
	try {
		String[] carIdArr = carIds.split(",");
		String sql = "update tyt_car set sort =? where id=?";
		if(carIdArr.length>0){
			for(int i=0;i<carIdArr.length;i++){
				this.getBaseDao().executeUpdateSql(sql, new Object[]{carIdArr.length-i,carIdArr[i]});
			}
			return true;
		}
		return false;
	} catch (Exception e) {
		e.printStackTrace();
		return false;
	}

}

	@Override
	public boolean updateIsInvoice(Long carId, Integer isInvoice, Integer thirdPartyRequire, Integer xhlPartyRequire) {
		String sql = "update tyt_car set is_invoice =?,third_party_require=?,xhl_party_require=? where id=?";
		try {
			this.getBaseDao().executeUpdateSql(sql, new Object[]{isInvoice,thirdPartyRequire,xhlPartyRequire, carId});
			return true;
		} catch (Exception e) {
			logger.error("update is_invoice field error:", e);
		}
		return false;
	}

	@Override
public boolean validateNumEqual(Map<String, String> params) {
		String userId = params.get("userId");
		String carIds = params.get("sortList");
		BigInteger count = this.getBaseDao().query("select count(*) from tyt_car where user_id=? and is_delete=1", new Object[]{userId});
		if(count.intValue()==carIds.split(",").length){
			return true;
		}
		return false;
}

@Override
public boolean updateOnOff(Long id, String findGoodOnOff) {
	Car car = this.getBaseDao().findById(id);
	String sql = "update tyt_car set find_good_onoff =?,update_time=now() where id=?";
	String sqlUpdatePre = "update tyt_recommend.tyt_preference set find_good_onoff =?,utime=now() where car_id=?";
	if(car!=null&& !findGoodOnOff.equals(car.getFindGoodOnOff())){
		this.getBaseDao().executeUpdateSql(sql, new Object[]{findGoodOnOff,id});
		this.getBaseDao().executeUpdateSql(sqlUpdatePre, new Object[]{findGoodOnOff,id});
		//判断是否需要发Mq
		TytPreference t=preferService.newFindPreferenceByCarId(id);
		if(t!=null&&t.getIsUpdate()!=null){
			//如果有更新 则需要发mQ 只有是开启开关 和偏好设置更新的时候才会发Mq
			if("1".equals(findGoodOnOff)&&"1".equals(t.getIsUpdate().toString())){
				// 清除缓存偏好设置  mq   发送清空猜你喜欢列表Mq
				MqMsgCacheInitBean mqMsgCacheInitBean = new MqMsgCacheInitBean();
				mqMsgCacheInitBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
				mqMsgCacheInitBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_CAR_CACHE_MESSAGE);
				mqMsgCacheInitBean.setCarId(t.getCarId());
				mqMsgCacheInitBean.setUserId(t.getUserId());
				tytMqRecommMessService.addSaveMqMessage(mqMsgCacheInitBean.getMessageSerailNum(), JSON.toJSONString(mqMsgCacheInitBean), MqBaseMessageBean.MESSAGETYPE_CAR_CACHE_MESSAGE);
				tytMqRecommMessService.sendMqMessageWithResult(mqMsgCacheInitBean.getMessageSerailNum(), JSON.toJSONString(mqMsgCacheInitBean), MqBaseMessageBean.MESSAGETYPE_CAR_CACHE_MESSAGE);
				//发送mq	清除猜你喜欢列表
				String guessLikeRedisKey = tytConfigService.getStringValue(Constant.RECOMMEND_GUESS_LIKE_PREFIX,"recommend_car_");
				guessLikeRedisKey = guessLikeRedisKey.concat(t.getCarId()+"").concat("_").concat(TimeUtil.formatDate_(new Date()));
				RedisUtil.del(guessLikeRedisKey);
				RedisUtil.del("recommend_guess_like_maxid_"+t.getCarId());
				MqGuessLikeMsg mqFindGoodOnOffMessageBean=new MqGuessLikeMsg();
				mqFindGoodOnOffMessageBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
				mqFindGoodOnOffMessageBean.setMethodType(0);
				mqFindGoodOnOffMessageBean.setMd5(t.getMd5());
				mqFindGoodOnOffMessageBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_RECOMMEND_TRANSPORT_FINDGOOD_MESSAGE);
				mqFindGoodOnOffMessageBean.setCarId(id);
				Integer delayedTime = tytConfigService.getIntValue(Constant.CACHE_RECOMMEND_MQDELAYEDSENDTIME_KEY);
				long delayedTimes = System.currentTimeMillis()+delayedTime;
				tytMqRecommMessService.addSaveMqMessage(mqFindGoodOnOffMessageBean.getMessageSerailNum(), JSON.toJSONString(mqFindGoodOnOffMessageBean), MqBaseMessageBean.MESSAGETYPE_RECOMMEND_TRANSPORT_FINDGOOD_MESSAGE,1,delayedTimes);
				boolean isSuccess = tytMqRecommMessService.sendTimerMqMessageWithResult(mqFindGoodOnOffMessageBean.getMessageSerailNum(), JSON.toJSONString(mqFindGoodOnOffMessageBean),
						MqBaseMessageBean.MESSAGETYPE_RECOMMEND_TRANSPORT_FINDGOOD_MESSAGE,delayedTimes);
				if(isSuccess){
					this.getBaseDao().executeUpdateSql("update tyt_recommend.tyt_preference set is_update = 0 where car_id =? ", new Object[]{car.getId()});
				}
			}
		}
		CarLog carLog = new CarLog();
		try {
			BeanUtils.copyProperties(carLog, car);
			carLog.setId(null);
			carLog.setRecordTime(new Date());
			carLog.setCarId(car.getId());
			carLog.setFindGoodOnOff(findGoodOnOff);
			carLog.setIsDisplay(0);
			carLog.setUpdateType("4");
			carLogService.add(carLog);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return true;
	}
	return false;
}

@Override
public Car newGetbyId(Long id) {

	return this.getBaseDao().findById(id);
}

@Override
public List<QueryCar> getQueryAllOld(Long userId, String auth) {
	return ((CarDao) (this.getBaseDao())).getQueryAllOld(userId, auth);
}

@Override
public BigInteger getCountByUserId(Long userId) {
	String sql="select count(1) from tyt_car where user_id=? and is_delete=1 and auth=1";
	BigInteger count=this.getBaseDao().query(sql, new Object[]{userId});
	return count;
}

@Override
public QueryCar getQueryCarNew(Long carId) throws Exception{
	String sql = "select car.id id,car.user_id userId,head_city headCity ,head_no headNo, tail_city tailCity,tail_no tailNo, head_driving_url headDrivingUrl,tail_driving_url tailDrivingUrl,auth, failure_reason failureReason, head_auth_status headAuthStatus,car.has_ladder hasLadder, car.car_head_type carHeadType,car.horse_power horsePower,car.tail_driving_other_side_url tailDrivingOtherSideUrl,"
			+ "tail.other_pure_flat otherPureFlat,tail.other_car_type otherCarType,tail.max_payload maxPayload,tail.is_joint_pull isJointPull,tail.joint_pull_length jointPullLength,tail.temp_licence_expires tempLicenceExpires,tail.load_surface_length loadSurfaceLength,tail.load_surface_height loadSurfaceHeight,car.road_card_status roadCardStatus,car.road_license_status roadLicenseStatus,car.road_card_fail_reason roadCardFailReason,car.road_license_fail_reason roadLicenseReason, head_failure_reason "
			+ "headFailureReason, tail_auth_status tailAuthStatus,tail_failure_reason tailFailureReason, sort ,pre.find_good_onoff findGoodOnOff ,pre.id preId,start_provinc startProvinc ,start_city startCity, start_area startArea,dest_provinc destProvinc,dest_city destCity,dest_area destArea, begin_weight beginWeight ,end_weight endWeight,head.road_transport_type roadTransportType, "
			+ "pre.length length,pre.wide wide,pre.high high ,car.update_time updateTime,car.car_type carType,pre.is_auth_update isAuthUpdate ,pre.preference_car goodType, tail.is_pure_flat isPureFlat,head.road_card_positive_url roadCardPositiveUrl,head.road_card_other_side_url roadCardOtherSideUrl,head.road_license_no_url roadLicenseNoUrl,"
			+ " car.head_driving_subpage_url headDrivingSubpageUrl,"
			+ "car.head_transport_homepage_url headTransportHomepageUrl,"
			+ "car.head_transport_subpage_url headTransportSubpageUrl,"
			+ "car.tail_driving_subpage_url tailDrivingSubpageUrl,"
			+ "car.tail_transport_homepage_url tailTransportHomepageUrl,"
			+ "car.tail_transport_subpage_url tailTransportSubpageUrl,"
			+ "car.head_transport_auth_status headTransportAuthStatus,"
			+ "car.head_transport_fail_reason headTransportFailReason,"
			+ "car.tail_transport_auth_status tailTransportAuthStatus,"
			+ "car.tail_transport_fail_reason tailTransportFailReason,"
			+ "car.head_driving_expired_time headDrivingExpiredTime, "
			+ "car.head_transport_expired_time headTransportExpiredTime, "
			+ "car.tail_driving_expired_time tailDrivingExpiredTime, "
			+ "car.tail_transport_expired_time tailTransportExpiredTime, "
			+ "car.tail_photo_url AS tailPhotoUrl, "
			+ "tail.length AS tailLength, "
			+ "tail.width AS tailWidth, "
			+ "tail.height AS tailHeight, car.is_invoice isInvoice "
			+ " from tyt_car  car "
			+ " LEFT JOIN tyt_recommend.tyt_preference_new pre on car.id=pre.car_id LEFT JOIN tyt_car_detail_tail tail ON tail.car_id = car.id LEFT JOIN tyt_car_detail_head head ON head.car_id = car.id  where car.id=?";
	Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
	scalarMap.put("id", Hibernate.BIG_INTEGER);
	scalarMap.put("headCity", Hibernate.STRING);
	scalarMap.put("headNo", Hibernate.STRING);
	scalarMap.put("tailCity", Hibernate.STRING);
	scalarMap.put("tailNo", Hibernate.STRING);
	scalarMap.put("auth", Hibernate.STRING);
	scalarMap.put("userId", Hibernate.BIG_INTEGER);
	scalarMap.put("headDrivingUrl", Hibernate.STRING);
	scalarMap.put("tailDrivingUrl", Hibernate.STRING);
	scalarMap.put("failureReason", Hibernate.STRING);
	scalarMap.put("headAuthStatus", Hibernate.SHORT);
	scalarMap.put("headFailureReason", Hibernate.STRING);
	scalarMap.put("tailAuthStatus", Hibernate.SHORT);
	scalarMap.put("tailFailureReason", Hibernate.STRING);
	scalarMap.put("findGoodOnOff", Hibernate.STRING);
	scalarMap.put("sort", Hibernate.BIG_INTEGER);
	scalarMap.put("startProvinc", Hibernate.STRING);
	scalarMap.put("startCity", Hibernate.STRING);
	scalarMap.put("startArea", Hibernate.STRING);
	scalarMap.put("destProvinc", Hibernate.STRING);
	scalarMap.put("destCity", Hibernate.STRING);
	scalarMap.put("destArea", Hibernate.STRING);
	scalarMap.put("beginWeight", Hibernate.STRING);
	scalarMap.put("endWeight", Hibernate.STRING);
	scalarMap.put("length", Hibernate.STRING);
	scalarMap.put("wide", Hibernate.STRING);
	scalarMap.put("high", Hibernate.STRING);
	scalarMap.put("carType", Hibernate.STRING);
	scalarMap.put("isPureFlat", Hibernate.STRING);
	scalarMap.put("preId", Hibernate.BIG_INTEGER);
	scalarMap.put("updateTime", Hibernate.TIMESTAMP);
	scalarMap.put("isAuthUpdate", Hibernate.STRING);
	scalarMap.put("goodType", Hibernate.STRING);
	//车货拆分新增返回参数
	scalarMap.put("hasLadder", Hibernate.STRING);
	scalarMap.put("carHeadType", Hibernate.STRING);
	scalarMap.put("horsePower", Hibernate.INTEGER);
	scalarMap.put("otherPureFlat", Hibernate.STRING);
	scalarMap.put("otherCarType", Hibernate.STRING);
	scalarMap.put("isJointPull", Hibernate.INTEGER);
	scalarMap.put("jointPullLength", Hibernate.STRING);
	scalarMap.put("loadSurfaceLength", Hibernate.STRING);
	scalarMap.put("maxPayload", Hibernate.STRING);
	scalarMap.put("loadSurfaceHeight", Hibernate.STRING);
	scalarMap.put("tailDrivingOtherSideUrl", Hibernate.STRING);
	scalarMap.put("roadCardPositiveUrl", Hibernate.STRING);
	scalarMap.put("roadCardOtherSideUrl", Hibernate.STRING);
	scalarMap.put("roadLicenseNoUrl", Hibernate.STRING);
	scalarMap.put("roadCardStatus", Hibernate.STRING);
	scalarMap.put("roadLicenseStatus", Hibernate.STRING);
	scalarMap.put("roadCardFailReason", Hibernate.STRING);
	scalarMap.put("roadLicenseReason", Hibernate.STRING);
	scalarMap.put("roadTransportType", Hibernate.STRING);

	scalarMap.put("headDrivingSubpageUrl", Hibernate.STRING);
	scalarMap.put("headTransportHomepageUrl", Hibernate.STRING);
	scalarMap.put("headTransportSubpageUrl", Hibernate.STRING);
	scalarMap.put("tailDrivingSubpageUrl", Hibernate.STRING);
	scalarMap.put("tailTransportHomepageUrl", Hibernate.STRING);
	scalarMap.put("tailTransportSubpageUrl", Hibernate.STRING);
	scalarMap.put("headTransportAuthStatus", Hibernate.INTEGER);
	scalarMap.put("headTransportFailReason", Hibernate.STRING);
	scalarMap.put("tailTransportAuthStatus", Hibernate.INTEGER);
	scalarMap.put("tailTransportFailReason", Hibernate.STRING);
	scalarMap.put("headDrivingExpiredTime", Hibernate.TIMESTAMP);
	scalarMap.put("headTransportExpiredTime", Hibernate.TIMESTAMP);
	scalarMap.put("tailDrivingExpiredTime", Hibernate.TIMESTAMP);
	scalarMap.put("tailTransportExpiredTime", Hibernate.TIMESTAMP);
	scalarMap.put("tailPhotoUrl", Hibernate.STRING);
	scalarMap.put("tailLength", Hibernate.STRING);
	scalarMap.put("tailWidth", Hibernate.STRING);
	scalarMap.put("tailHeight", Hibernate.STRING);
	scalarMap.put("isInvoice", Hibernate.INTEGER);
	QueryCar car =  this.getBaseDao().search(sql, scalarMap,
			QueryCar.class, new Object[]{carId}).get(0);
	Date now = new Date();
	car.setCurrentTime(now);
	if(car!=null){
		//如果是认证成功的重新上传的更改
		if("3".equals(car.getAuth())){
			car.setAuth("0");
		}
		Integer globalCarLocationRefreshOnoffValue = tytConfigService.getIntValue(globalCarLocationRefreshOnoff, 1);
		if(globalCarLocationRefreshOnoffValue==1||car.getIsAuthUpdate()==null){
			car.setIsAuthUpdate("2");
		}
		if(StringUtils.isNotEmpty(car.getLength())){
			if(car.getLength().lastIndexOf("00")>0){
				car.setLength(String.valueOf(Integer.valueOf(car.getLength())/100));
			}else
				car.setLength(String.valueOf(Double.valueOf(car.getLength())/100d));
		}
		if(StringUtils.isNotEmpty(car.getWide())){
			if(car.getWide().lastIndexOf("00")>0){
				car.setWide(String.valueOf(Integer.valueOf(car.getWide())/100));
			}else
				car.setWide(String.valueOf(Double.valueOf(car.getWide())/100d));
		}

		if(StringUtils.isNotEmpty(car.getHigh())){
			if(car.getHigh().lastIndexOf("00")>0){
				car.setHigh(String.valueOf(Integer.valueOf(car.getHigh())/100));
			}else
				car.setHigh(String.valueOf(Double.valueOf(car.getHigh())/100d));
		}
		if(StringUtils.isNotEmpty(car.getEndWeight())){
			if(car.getEndWeight().lastIndexOf("00")>0){
				car.setEndWeight(String.valueOf(Integer.valueOf(car.getEndWeight())/100));
			}else
				car.setEndWeight(String.valueOf(Double.valueOf(car.getEndWeight())/100d));
		}
		if(StringUtils.isNotEmpty(car.getBeginWeight())){
			if(car.getBeginWeight().lastIndexOf("00")>0){
				car.setBeginWeight(String.valueOf(Integer.valueOf(car.getBeginWeight())/100));
			}else
				car.setBeginWeight(String.valueOf(Double.valueOf(car.getBeginWeight())/100d));
		}
		if (StringUtils.isNumeric(car.getTailLength()) && Integer.parseInt(car.getTailLength())>=100){
			car.setTailLength(new BigDecimal(car.getTailLength()).movePointLeft(3).setScale(2, RoundingMode.HALF_UP).toPlainString());
		}
		if (StringUtils.isNumeric(car.getTailWidth()) && Integer.parseInt(car.getTailWidth())>=100){
			car.setTailWidth(new BigDecimal(car.getTailWidth()).movePointLeft(3).setScale(2, RoundingMode.HALF_UP).toPlainString());
		}
		if (StringUtils.isNumeric(car.getTailHeight()) && Integer.parseInt(car.getTailHeight()) >=100){
			car.setTailHeight(new BigDecimal(car.getTailHeight()).movePointLeft(3).setScale(2, RoundingMode.HALF_UP).toPlainString());
		}
		//车头行驶证有效期
		Date headDrivingExpiredTime = car.getHeadDrivingExpiredTime();
        //车头道路运输证有效期
		Date headTransportExpiredTime = car.getHeadTransportExpiredTime();
        //挂车行驶证有效期
		Date tailDrivingExpiredTime = car.getTailDrivingExpiredTime();
        //挂车道路运输证有效期
		Date tailTransportExpiredTime = car.getTailTransportExpiredTime();
		//逾期提醒信息
        String expiredTips = "";
		if(headDrivingExpiredTime != null && now.after(TimeUtil.addDay(headDrivingExpiredTime,1) )){
			expiredTips = StringUtils.replaceEach("逾期提醒：您的行驶证于${expiredTime}到期，请及时更换",
					new String[]{"${expiredTime}"},
					new String[]{TimeUtil.formatDateYear(headDrivingExpiredTime)});
			car.setHeadDrivingExpiredTips(expiredTips);
		}
		if(headTransportExpiredTime != null && now.after(TimeUtil.addDay(headTransportExpiredTime,1))){
			expiredTips = StringUtils.replaceEach("逾期提醒：您的道路运输证于${expiredTime}到期，请及时更换",
					new String[]{"${expiredTime}"},
					new String[]{TimeUtil.formatDateYear(headTransportExpiredTime)});
			car.setHeadTransportExpiredTips(expiredTips);
		}
		if(tailDrivingExpiredTime != null && now.after(TimeUtil.addDay(tailDrivingExpiredTime,1))){
			expiredTips = StringUtils.replaceEach("逾期提醒：您的行驶证于${expiredTime}到期，请及时更换",
					new String[]{"${expiredTime}"},
					new String[]{TimeUtil.formatDateYear(tailDrivingExpiredTime)});
			car.setTailDrivingExpiredTips(expiredTips);
		}
		if(tailTransportExpiredTime != null && now.after(TimeUtil.addDay(tailTransportExpiredTime,1))){
			expiredTips = StringUtils.replaceEach("逾期提醒：您的道路运输证于${expiredTime}到期，请及时更换",
					new String[]{"${expiredTime}"},
					new String[]{TimeUtil.formatDateYear(tailTransportExpiredTime)});
			car.setTailTransportExpiredTips(expiredTips);
		}

		try {
			//逾期提示语
			StringBuilder expiredTipsBuilder = new StringBuilder();
			if (ObjectUtil.isNotNull(headDrivingExpiredTime) && now.after(headDrivingExpiredTime)) {
				expiredTipsBuilder.append("车头行驶证过期；");
			}
			if (ObjectUtil.isNotNull(tailDrivingExpiredTime) && now.after(tailDrivingExpiredTime)) {
				expiredTipsBuilder.append("车挂行驶证/临牌过期；");
			}
			String headTransportHomepageUrl = car.getHeadTransportHomepageUrl();
			if (StringUtils.isBlank(headTransportHomepageUrl)) {
				expiredTipsBuilder.append("车头道运证缺失；");
			}
			//查询换绑的新的挂车信息
			CarDetailTail carDetailTail = carDetailTailService.getByCarId(car.getId().longValue());
			if (ObjectUtil.isNotNull(carDetailTail) && StringUtils.isBlank(carDetailTail.getCarType())) {
				expiredTipsBuilder.append("挂车型号缺失；");
			}
			if (ObjectUtil.isNotNull(carDetailTail) && ObjectUtil.isNull(carDetailTail.getIsPureFlat())) {
				expiredTipsBuilder.append("挂车样式缺失；");
			}
			String hasLadder = car.getHasLadder();
			if (StringUtils.isBlank(hasLadder) || "2".equals(hasLadder)) {
				expiredTipsBuilder.append("是否带爬梯缺失；");
			}
			car.setExpiredTips(expiredTipsBuilder.toString());
		} catch (Exception e) {
			logger.error("get car detail expired tips error:", e);
		}
	}

		Car paramCar = new Car();
		paramCar.setAuth(car.getAuth());
		paramCar.setCarType(car.getCarType() == null ? null : Integer.valueOf(car.getCarType()));
		paramCar.setIsPureFlat(car.getIsPureFlat() == null ? null : Integer.valueOf(car.getIsPureFlat()));
		paramCar.setTailNo(car.getTailNo());
		paramCar.setTailCity(car.getTailCity());
		paramCar.setHeadTransportHomepageUrl(car.getHeadTransportHomepageUrl());
		paramCar.setHeadDrivingExpiredTime(car.getHeadDrivingExpiredTime());
		paramCar.setTailDrivingExpiredTime(car.getTailDrivingExpiredTime());
		car.setCarIsNeedImprovenData(this.carIsNeedImprovenData(paramCar));

		return car;
}

@SuppressWarnings("null")
@Override
public List<QueryCar> getQueryAllNew(Long userId, String auth) {
		List<QueryCar> queryCars= ((CarDao) (this.getBaseDao())).getQueryAllNew(userId, auth);
		if(queryCars==null&&queryCars.size()<1){
			return null;
		}
		Integer globalCarLocationRefreshOnoffValue = tytConfigService.getIntValue(globalCarLocationRefreshOnoff, 1);
		for(QueryCar car : queryCars){
			car.setCurrentTime(new Date());
			if(globalCarLocationRefreshOnoffValue==1||car.getIsAuthUpdate()==null){
				car.setIsAuthUpdate("2");
			}

			//如果是认证成功的重新上传的更改
			if("3".equals(car.getAuth())){
				car.setAuth("0");
			}
			if(StringUtils.isNotEmpty(car.getLength())){
				if(car.getLength().lastIndexOf("00")>0){
					car.setLength(String.valueOf(Integer.valueOf(car.getLength())/100));
				}else
					car.setLength(String.valueOf(Double.valueOf(car.getLength())/100d));
			}
			if(StringUtils.isNotEmpty(car.getWide())){
				if(car.getWide().lastIndexOf("00")>0){
					car.setWide(String.valueOf(Integer.valueOf(car.getWide())/100));
				}else
					car.setWide(String.valueOf(Double.valueOf(car.getWide())/100d));
			}

			if(StringUtils.isNotEmpty(car.getHigh())){
				if(car.getHigh().lastIndexOf("00")>0){
					car.setHigh(String.valueOf(Integer.valueOf(car.getHigh())/100));
				}else
					car.setHigh(String.valueOf(Double.valueOf(car.getHigh())/100d));
			}
			if(StringUtils.isNotEmpty(car.getEndWeight())){
				if(car.getEndWeight().lastIndexOf("00")>0){
					car.setEndWeight(String.valueOf(Integer.valueOf(car.getEndWeight())/100));
				}else
					car.setEndWeight(String.valueOf(Double.valueOf(car.getEndWeight())/100d));
			}
			if(StringUtils.isNotEmpty(car.getBeginWeight())){
				if(car.getBeginWeight().lastIndexOf("00")>0){
					car.setBeginWeight(String.valueOf(Integer.valueOf(car.getBeginWeight())/100));
				}else
					car.setBeginWeight(String.valueOf(Double.valueOf(car.getBeginWeight())/100d));
			}

		}

		return queryCars;

}

@Override
public boolean updateOnOffNew(Long id, String findGoodOnOff) {
		Car car = this.getBaseDao().findById(id);
		String sqlUpdatePre = "update tyt_recommend.tyt_preference_new set find_good_onoff =?,update_onoff_time=now(),utime=now() where car_id=?";
		if(car!=null){
			this.getBaseDao().executeUpdateSql(sqlUpdatePre, new Object[]{findGoodOnOff,id});
			CarLog carLog = new CarLog();
			try {
				BeanUtils.copyProperties(carLog, car);
				carLog.setId(null);
				carLog.setRecordTime(new Date());
				carLog.setCarId(car.getId());
				carLog.setFindGoodOnOff(findGoodOnOff);
				carLog.setIsDisplay(0);
				carLog.setUpdateType("4");
				carLogService.add(carLog);
			} catch (Exception e) {
				e.printStackTrace();
			}
			return true;
		}
		return false;
}

	/**
	 * @Description  根据用户id和认证状态查询用户所有的车辆
	 * <AUTHOR>
	 * @Date  2019/3/13 10:13
	 * @Param [userId]
	 * @return java.util.List<com.tyt.user.querybean.QueryCar>
	 **/
	@Override
	public List<QueryCar> getAllCarByUserId(Long userId) {
		return ((CarDao) (this.getBaseDao())).getAllCarByUserId(userId);
	}

	/**
	 * @Description  根据车头车牌号查询认证车辆的信息
	 * <AUTHOR>
	 * @Date  2019/3/14 11:30
	 * @Param [userId, carHeadCity, carHeadNo]
	 * @return com.tyt.model.CarInsuranceInquiry
	 **/
	@Override
	public List<QueryCar> getAuthCarInfoByHeadNo(Long userId, String carHeadCity, String carHeadNo) {
		//获取已认证成功的车辆信息
		List<QueryCar> authCarList = ((CarDao) (this.getBaseDao())).getAuthCarInfoByHeadNo(userId, carHeadCity, carHeadNo);
		return authCarList;
	}

	/**
	 * 根据车辆类型 获取车辆类型列表
	 *
	 * @return
	 */
	public List<CarType> getCarTypeListByClassify(Integer carClassify) {
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("id", Hibernate.INTEGER);
		scalarMap.put("carClassify", Hibernate.INTEGER);
		scalarMap.put("carType", Hibernate.STRING);
		scalarMap.put("updateTime", Hibernate.STRING);
		String sql = "SELECT tcd.`id`,tcd.`car_classify` AS carClassify, tcd.`car_type` AS 'carType', tcd.`update_time` AS 'updateTime' FROM tyt_car_type tcd where tcd.car_classify = ? ORDER BY tcd.car_classify DESC,tcd.id DESC";
		return this.getBaseDao().search(sql, scalarMap, CarType.class, new Object[] {carClassify});
	}

	/**
	 * @Description  我的车辆列表
	 * <AUTHOR>
	 * @Date  2019/7/3 11:57
	 * @Param [userId]
	 * @return java.util.List<com.tyt.model.Car>
	 **/
	@Override
	public List<Car> getMyCarList(Long userId) {
		String sql = "SELECT " +
				" c.id id, " +
				" c.head_city headCity, " +
				" c.head_no headNo, " +
				" c.tail_city tailCity, " +
				" c.tail_no tailNo, " +
				" c.auth auth, " +
				" c.driver_name driverName, " +
				" c.driver_phone driverPhone, " +
				" c.secondary_driver_name secondaryDriverName, " +
				" c.secondary_driver_phone secondaryDriverPhone, " +
				" c.follow_driver_phone followDriverPhone, " +
				" c.horse_power horsePower, " +
				" t.check_weight checkWeight, " +
				" t.max_payload maxPayload, " +
				" t.length length, " +
				" t.width width, " +
				" t.height height, " +
				" c.car_type carType,  " +
                " ct.car_type carTypeName  " +
				"FROM " +
				" tyt_car c " +
				" LEFT JOIN tyt_car_detail_tail t ON c.id = t.car_id  " +
                " LEFT JOIN tyt_car_type ct ON c.car_type = ct.id " +
				"WHERE " +
				" c.is_delete = 1  " +
				" AND c.user_id = ?  " +
				"ORDER BY " +
				" c.update_time DESC ";

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id",Hibernate.LONG);
		scalarMap.put("headCity",Hibernate.STRING);
		scalarMap.put("headNo",Hibernate.STRING);
		scalarMap.put("tailCity",Hibernate.STRING);
		scalarMap.put("tailNo",Hibernate.STRING);
		scalarMap.put("auth",Hibernate.STRING);
		scalarMap.put("driverName",Hibernate.STRING);
		scalarMap.put("driverPhone",Hibernate.STRING);
		scalarMap.put("secondaryDriverName",Hibernate.STRING);
		scalarMap.put("secondaryDriverPhone",Hibernate.STRING);
		scalarMap.put("followDriverPhone",Hibernate.STRING);
		scalarMap.put("horsePower",Hibernate.INTEGER);
		scalarMap.put("checkWeight",Hibernate.STRING);
		scalarMap.put("maxPayload",Hibernate.STRING);
		scalarMap.put("length",Hibernate.STRING);
		scalarMap.put("width",Hibernate.STRING);
		scalarMap.put("height",Hibernate.STRING);
		scalarMap.put("carType",Hibernate.INTEGER);
        scalarMap.put("carTypeName",Hibernate.STRING);

		//我的车辆列表
		List<Car> myCarList = this.getBaseDao().search(sql, scalarMap, Car.class, new Object[]{userId});
		return myCarList;
	}

	@Override
	public List<Car> getListByUserId(Long userId) {
		String sql = "SELECT * FROM tyt_car " +
				" WHERE" +
				" is_delete = 1 AND user_id = ? " +
				" ORDER BY create_time DESC";
		//我的车辆列表
		List<Car> myCarList = this.getBaseDao().queryForList(sql, new Object[]{userId});
		return myCarList;
	}

	/**
	 * @Description  完善车辆信息
	 * <AUTHOR>
	 * @Date  2019/7/3 15:58
	 * @Param [updateCarBean]
	 * @return void
	 **/
	@Override
	public int updateCarInfo(UpdateCarBean updateCarBean) {

		//接收到的车辆参数
		Long userId = updateCarBean.getUserId();
		Long carId = updateCarBean.getCarId();
		String maxPayload = updateCarBean.getMaxPayload();
		Integer carType = updateCarBean.getCarType();
		String driverName = updateCarBean.getDriverName();
		String driverPhone = updateCarBean.getDriverPhone();
		String secondaryDriverName = updateCarBean.getSecondaryDriverName();
		String secondaryDriverPhone = updateCarBean.getSecondaryDriverPhone();
        // 车辆信息
		Car car = this.getBaseDao().findById(carId);
		int results = 0;
		//如果超限载重不为空,则修改挂车详情信息表

		String sql  = "update tyt_car_detail_tail set max_payload=:maxPayload "
				+" where car_id=:carId and user_id=:userId";
		Map<String, Object> map = new HashMap<String,Object>();
		map.put("maxPayload", maxPayload);
		map.put("carId", carId);
		map.put("userId", userId);
		int result = this.getBaseDao().executeUpdateSql(sql, map);
		if(result <= 0){
			//插入一条记录
			sql = "insert into tyt_car_detail_tail (`car_id`,`user_id`,`city`,`car_no`,`max_payload`) "
					+ "values (:carId, :userId, :city, :carNo, :maxPayload)";
			map = new HashMap<String,Object>();
			map.put("carId", carId);
			map.put("userId", userId);
			map.put("city", car.getTailCity());
			map.put("carNo", car.getTailNo());
			map.put("maxPayload", maxPayload);
			result = this.getBaseDao().executeUpdateSql(sql, map);
		}
		results += result;

		//如果是其它字段，则修改车辆信息表
		sql = "update tyt_car set car_type=:carType,"
				+"driver_name=:driverName,driver_phone=:driverPhone,"
				+"secondary_driver_name=:secondaryDriverName,"
				+"secondary_driver_phone=:secondaryDriverPhone "
				+" where id=:carId and user_id=:userId ";
		map = new HashMap<String,Object>();
		map.put("carType", carType);
		map.put("driverName", driverName);
		map.put("driverPhone", driverPhone);
		map.put("secondaryDriverName", secondaryDriverName);
		map.put("secondaryDriverPhone", secondaryDriverPhone);
		map.put("carId", carId);
		map.put("userId", userId);
		result = this.getBaseDao().executeUpdateSql(sql, map);
		results += result;
		return results;
	}

	/**
	 * @description 完善车辆信息 -- 调度中心版本
	 * <AUTHOR>
	 * @date 2020/5/11 11:18
	 * @param updateCarDiaoduBean
	 * @return int
	 */
	@Override
	public int updateCarInfoDiaodu(UpdateCarDiaoduBean updateCarDiaoduBean) {
		//接收到的车辆参数
		Long userId = updateCarDiaoduBean.getUserId();
		Long carId = updateCarDiaoduBean.getCarId();
		String driverName = updateCarDiaoduBean.getDriverName();
		String driverPhone = updateCarDiaoduBean.getDriverPhone();
		String secondaryDriverName = updateCarDiaoduBean.getSecondaryDriverName();
		String secondaryDriverPhone = updateCarDiaoduBean.getSecondaryDriverPhone();
		Long driverUserId = updateCarDiaoduBean.getDriverUserId();
		Long secondaryDriverUserId = updateCarDiaoduBean.getSecondaryDriverUserId();

		String sql = "";
		int result = 0;
		Map<String, Object> map = new HashMap<String,Object>();
		if(driverUserId != null){ //APP开票版本，绑定主司机
			DriverDetailVO driverDetail = invoiceDriverService.getDriverDetail(driverUserId);
			if(driverDetail != null){
				sql = "update tyt_car set "
						+"driver_user_id=:driverUserId, "
						+"driver_name=:driverName,"
						+"driver_phone=:driverPhone "
						+" where id=:carId and user_id=:userId";
				map.put("driverUserId", driverUserId);
				map.put("driverName", driverDetail.getName());
				map.put("driverPhone", driverDetail.getPhone());
				map.put("carId", carId);
				map.put("userId", userId);
				result = this.getBaseDao().executeUpdateSql(sql, map);
			}
		}
		if(secondaryDriverUserId != null){ //APP开票版本，绑定副司机
			DriverDetailVO secondDriverDetail = invoiceDriverService.getDriverDetail(secondaryDriverUserId);
			if(secondDriverDetail != null){
				sql = "update tyt_car set "
						+"secondary_driver_user_id=:secondaryDriverUserId,"
						+"secondary_driver_name=:secondaryDriverName,"
						+"secondary_driver_phone=:secondaryDriverPhone "
						+" where id=:carId and user_id=:userId";
				map = new HashMap<String,Object>();
				map.put("secondaryDriverUserId", secondaryDriverUserId);
				map.put("secondaryDriverName", secondDriverDetail.getName());
				map.put("secondaryDriverPhone", secondDriverDetail.getPhone());
				map.put("carId", carId);
				map.put("userId", userId);
				result = this.getBaseDao().executeUpdateSql(sql, map);
			}
		}
		if(driverUserId == null && secondaryDriverUserId == null){ //APP老版本，没有主副司机ID
			//查询车辆信息
			Car car = this.getById(carId);
			//主司机ID
			Long oldDriverUserId = car.getDriverUserId();
			//副司机ID
			Long oldSecondaryDriverUserId = car.getSecondaryDriverUserId();
			if(oldDriverUserId != null){ //同步更新开票司机表信息
				driverUserId = oldDriverUserId;
                //更新司机表信息
				invoiceDriverService.updateDriverInner4Car(driverUserId, driverName, driverPhone);
			}else{
				//同步插入开票司机表信息
				driverUserId = invoiceDriverService.syncOldDriver(userId,driverName,driverPhone);
			}
			if(oldSecondaryDriverUserId != null){ //更新开票司机表信息
				secondaryDriverUserId = oldSecondaryDriverUserId;
				//更新司机表信息
				invoiceDriverService.updateDriverInner4Car(secondaryDriverUserId,secondaryDriverName,secondaryDriverPhone);
			}else{
				//同步开票司机表信息
				secondaryDriverUserId = invoiceDriverService.syncOldDriver(userId,secondaryDriverName,secondaryDriverPhone);
			}

			//如果是其它字段，则修改车辆信息表
			sql = "update tyt_car set "
					+"driver_user_id=:driverUserId, "
					+"driver_name=:driverName,"
					+"driver_phone=:driverPhone,"
					+"secondary_driver_user_id=:secondaryDriverUserId,"
					+"secondary_driver_name=:secondaryDriverName,"
					+"secondary_driver_phone=:secondaryDriverPhone "
					+" where id=:carId and user_id=:userId";
			map = new HashMap<String,Object>();
			map.put("driverUserId", driverUserId);
			map.put("driverName", driverName);
			map.put("driverPhone", driverPhone);
			map.put("secondaryDriverUserId", secondaryDriverUserId);
			map.put("secondaryDriverName", secondaryDriverName);
			map.put("secondaryDriverPhone", secondaryDriverPhone);
			map.put("carId", carId);
			map.put("userId", userId);
			result = this.getBaseDao().executeUpdateSql(sql, map);
		}
		return result;
	}

	/**
	 * 删除绑定司机信息方法
	 * @param updateCarDiaoduBean
	 * @return
	 */
	@Override
	public int deleteDriverInfo(UpdateCarDiaoduBean updateCarDiaoduBean) {
		//接收到的车辆参数
		Long userId = updateCarDiaoduBean.getUserId();
		Long carId = updateCarDiaoduBean.getCarId();
		//删除司机类型：1.删除主司机 2.删除副司机
		Integer deleteDriverType = updateCarDiaoduBean.getDeleteDriverType();

		String sql = "";
		//删除车辆主司机信息
		if(1 == deleteDriverType){
			sql = "update tyt_car set "
					+"driver_user_id=null, "
					+"driver_name=null,"
					+"driver_phone=null"
					+" where id=:carId and user_id=:userId";
		}else if(2 == deleteDriverType){
			sql = "update tyt_car set "
					+"secondary_driver_user_id=null,"
					+"secondary_driver_name=null,"
					+"secondary_driver_phone=null "
					+" where id=:carId and user_id=:userId";
		}
		Map<String, Object> map = new HashMap<String,Object>();
		map.put("carId", carId);
		map.put("userId", userId);
		int result = this.getBaseDao().executeUpdateSql(sql, map);
		return result;
	}

	@Override
	public Long saveCar(CarSaveBean carSaveBean, String headDrivingUrl, String tailDrivingUrl,
			String tailDrivingOtherSideUrl, String roadCardPositiveUrl,
			String roadCardOtherSideUrl, String roadLicenseNoUrl,
			String headDrivingSubpageUrl,
			String headTransportHomepageUrl,
			String headTransportSubpageUrl,
			String tailDrivingSubpageUrl,
			String tailTransportHomepageUrl,
			String tailTransportSubpageUrl,
			String tailPhotoUrl,
			Long tailId) throws Exception{
		//车牌号包含 超 为临时牌照 否则没有临牌
		String tailNo = carSaveBean.getTailNo();
		int invoice = 3;
		int thirdPartyRequire = 3,xhlPartyRequire = 3;

		if(StringUtils.isNotBlank(headDrivingUrl) && StringUtils.isNotBlank(headTransportHomepageUrl) && StringUtils.isNotBlank(headDrivingSubpageUrl)) {
			xhlPartyRequire = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
		}
			//临牌开票判断
		if(StringUtils.isNotBlank(tailNo) && !Objects.equals(tailNo.substring(tailNo.length()-1),"超")){
			//非临牌开票判断
			if(StringUtils.isNotEmpty(headDrivingUrl) && StringUtils.isNotEmpty(headTransportHomepageUrl)  && StringUtils.isNotEmpty(tailDrivingUrl)){
				thirdPartyRequire = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
				if (StringUtils.isNotEmpty(tailTransportHomepageUrl)){
					invoice = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
				}
			}
			tailDrivingOtherSideUrl = null;
		}else{
			if(StringUtils.isNotEmpty(headDrivingUrl) && StringUtils.isNotEmpty(headTransportHomepageUrl) && StringUtils.isNotEmpty(tailDrivingUrl)
					&& StringUtils.isNotEmpty(tailDrivingOtherSideUrl)){
				invoice = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
				thirdPartyRequire = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
				xhlPartyRequire = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();

			}
		}
		Integer carType = carSaveBean.getCarType();
		if (null != carType && thirdPartyRequire == 3){
			if (14 == carType && (StringUtils.isNotEmpty(headDrivingUrl) && StringUtils.isNotEmpty(headTransportHomepageUrl))){ //单机板
				invoice = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
				thirdPartyRequire = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
			}
			if (15 == carType && StringUtils.isNotEmpty(headDrivingUrl)){ //清障车
				invoice = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
				thirdPartyRequire = CarInvoiceStatusEnum.WAIT_AUDIT.getCode();
			}
		}
		Car car = createCar(carSaveBean,headDrivingUrl,tailDrivingUrl,tailDrivingOtherSideUrl,
				headDrivingSubpageUrl, headTransportHomepageUrl, headTransportSubpageUrl,
				tailDrivingSubpageUrl, tailTransportHomepageUrl, tailTransportSubpageUrl, tailPhotoUrl,tailId);
		car.setIsInvoice(invoice);
		car.setThirdPartyRequire(thirdPartyRequire);
		car.setXhlPartyRequire(xhlPartyRequire);
        Long carId = addCar(car);
        //增加挂车样式
        if (carSaveBean.getIsPureFlat()!=null){
            saveTailDetail(carId,carSaveBean.getUserId(),carSaveBean.getTailCity(), tailNo,carSaveBean.getIsPureFlat(),carSaveBean);
        }
		if ("1".equals(carSaveBean.getIsDispatch())){
			CarDetailHead carDetailHead=new CarDetailHead();
			carDetailHead.setCarId(carId);
			carDetailHead.setLength(carSaveBean.getTailLength());
			carDetailHead.setWidth(carSaveBean.getTailWidth());
			carDetailHead.setHeight(carSaveBean.getTailHeight());
			carDetailHead.setState("1");
			carDetailHead.setUseNature("货运");
			carDetailHeadService.add(carDetailHead);
		}
        return carId;
    }

    private Car createCar(CarSaveBean carSaveBean, String headDrivingUrl, String tailDrivingUrl,
			String tailDrivingOtherSideUrl,
			String headDrivingSubpageUrl,
			String headTransportHomepageUrl,
			String headTransportSubpageUrl,
			String tailDrivingSubpageUrl,
			String tailTransportHomepageUrl,
			String tailTransportSubpageUrl,
			String tailPhotoUrl,
			Long tailId){
        Car car = new Car();
        car.setUserId(carSaveBean.getUserId());
        car.setHeadCity(carSaveBean.getHeadCity());
        car.setHeadNo(carSaveBean.getHeadNo());
        car.setTailCity(carSaveBean.getTailCity());
        car.setTailNo(carSaveBean.getTailNo());
		car.setHeadLicensePlateColor("黄色");
        // 2017-07-19 车头车挂审核排序等默认信息设置
        car.setHeadAuthStatus("0");
        car.setTailAuthStatus("0");
        car.setFindGoodOnOff("0");
        car.setAuth(carSaveBean.getAuth());
        BigInteger maxSort = getMaxSort(carSaveBean.getUserId());
        if (maxSort == null) {
            maxSort = new BigInteger("0");
        }
        car.setSort(maxSort.longValue() + 1);
        car.setCreateTime(new Date());
        car.setUpdateTime(new Date());
		car.setSource(2);
        if (carSaveBean.getCarType() != null){
            car.setCarType(carSaveBean.getCarType());
        }
        /* 图片处理 */
        if (StringUtils.isNotBlank(headDrivingUrl)){
            car.setHeadDrivingUrl(headDrivingUrl);
        }else{
            car.setHeadDrivingUrl("");
        }
        if (StringUtils.isNotBlank(tailDrivingUrl)){
            car.setTailDrivingUrl(tailDrivingUrl);
        }
        //车头行驶证副页反面url
		if (StringUtils.isNotBlank(headDrivingSubpageUrl)){
			car.setHeadDrivingSubpageUrl(headDrivingSubpageUrl);
		}
		//车头道路运输证主页url
		if (StringUtils.isNotBlank(headTransportHomepageUrl)){
			car.setHeadTransportHomepageUrl(headTransportHomepageUrl);
		}
		//车头道路运输证副页url
		if (StringUtils.isNotBlank(headTransportSubpageUrl)){
			car.setHeadTransportSubpageUrl(headTransportSubpageUrl);
		}
		//挂车行驶证副页反面url
		if (StringUtils.isNotBlank(tailDrivingSubpageUrl)){
			car.setTailDrivingSubpageUrl(tailDrivingSubpageUrl);
		}
		//挂车道路运输证主页url
		if (StringUtils.isNotBlank(tailTransportHomepageUrl)){
			car.setTailTransportHomepageUrl(tailTransportHomepageUrl);
		}
		//挂车道路运输证副页url
		if (StringUtils.isNotBlank(tailTransportSubpageUrl)){
			car.setTailTransportSubpageUrl(tailTransportSubpageUrl);
		}
		//车头道路运输证审核状态 0 认证中 1 认证通过 2 认证失败
		car.setHeadTransportAuthStatus(0);
		//挂车道路运输证审核状态 0 认证中 1 认证通过 2 认证失败
		car.setTailTransportAuthStatus(0);

		car.setHasLadder(carSaveBean.getHasLadder());
        car.setClientSign(carSaveBean.getClientSign());
        //车货拆分新增参数
		if(carSaveBean.getHorsePower()!=null){
			car.setCarHeadType(carSaveBean.getCarHeadType());
			car.setHasLadder(carSaveBean.getHasLadder());
			car.setHorsePower(carSaveBean.getHorsePower());
		}
		//开票新增参数
		if (StringUtils.isNotEmpty(tailDrivingOtherSideUrl)){
			car.setTailDrivingOtherSideUrl(tailDrivingOtherSideUrl);
		}
		//6370新增 挂车照片 【挂车型号选择【其他】，需要增加上传【挂车照片】字段（必填）】
		if (StringUtils.isNotBlank(tailPhotoUrl)){
			car.setTailPhotoUrl(tailPhotoUrl);
		}
		if ("1".equals(carSaveBean.getIsDispatch())){
			car.setIsDispatch(carSaveBean.getIsDispatch());
			car.setRoadCardStatus("0");
			car.setRoadLicenseStatus("0");
		}
		//车辆认证来源
		car.setAuthPath(carSaveBean.getAuthPath());

		//6430新增车头\挂车行驶证过期时间
		if (Objects.nonNull(carSaveBean.getHeadDrivingExpiredTime()) && carSaveBean.getHeadDrivingExpiredTime()>0) {
			car.setHeadDrivingExpiredTime(TimeUtil.timeStampToDate(carSaveBean.getHeadDrivingExpiredTime()));
		}

		if (Objects.nonNull(carSaveBean.getTailDrivingExpiredTime()) && carSaveBean.getTailDrivingExpiredTime() >0){
			car.setTailDrivingExpiredTime(TimeUtil.timeStampToDate(carSaveBean.getTailDrivingExpiredTime()));
		}

		//如果挂车ID不为空，则证明换绑了挂车，更新挂车相关的信息
		if(tailId != null){
			//查询换绑的新的挂车信息
			CarDetailTail carDetailTail = carDetailTailService.getById(tailId);
			//获取换绑挂车的主信息
			Car newCar = this.getById(carDetailTail.getCarId());

			car.setTailCity(newCar.getTailCity());
			car.setTailNo(newCar.getTailNo());
			car.setTailDrivingUrl(newCar.getTailDrivingUrl());
			car.setTailDrivingSubpageUrl(newCar.getTailDrivingSubpageUrl());
			car.setTailDrivingOtherSideUrl(newCar.getTailDrivingOtherSideUrl());
			car.setTailTransportHomepageUrl(newCar.getTailTransportHomepageUrl());
			car.setTailTransportSubpageUrl(newCar.getTailTransportSubpageUrl());
			car.setTailName(newCar.getTailName());
			car.setTailPhone(newCar.getTailPhone());
			car.setTailBrand(newCar.getTailBrand());
			car.setTailAuthStatus(newCar.getTailAuthStatus());
			car.setTailFailReason(newCar.getTailFailReason());
			car.setTailTransportNo(newCar.getTailTransportNo());
			car.setTailDrivingExpiredTime(newCar.getTailDrivingExpiredTime());
			car.setTailTransportExpiredTime(newCar.getTailTransportExpiredTime());
			car.setTailTransportAuthStatus(newCar.getTailTransportAuthStatus());
			car.setTailTransportFailReason(newCar.getTailTransportFailReason());
		}
        return car;
    }

    @Override
    public void saveTailDetail(Long carId, Long userId, String tailCity, String tailNo, Integer isPureFlat,CarSaveBean carSaveBean){
		CarDetailTail carDetailTail = carDetailTailService.getByCarId(carId);
		String tCurbWeight = "";
        if (Objects.nonNull(carDetailTail)){
			tCurbWeight = carDetailTail.getCurbWeight();
			if (StringUtils.isBlank(tCurbWeight) && tailNo.endsWith("超")){
				tCurbWeight = "10000";
			}
            String updateSql = "UPDATE tyt_car_detail_tail SET city =?, car_no =?, is_pure_flat =?,other_pure_flat=?,other_car_type=?," +
					"is_joint_pull=?,joint_pull_length=?,temp_licence_expires=?,load_surface_length=?,load_surface_height=?,max_payload=?,length=?," +
					"width=?,height=?, curb_weight=?" +
					" WHERE car_id=?";
            this.getBaseDao().executeUpdateSql(updateSql,new Object[]{tailCity,tailNo,isPureFlat,carSaveBean.getOtherPureFlat(),
					carSaveBean.getOtherCarType(),carSaveBean.getIsJointPull(),carSaveBean.getJointPullLength(),carSaveBean.getTempLicenceExpires()
					,carSaveBean.getLoadSurfaceLength(),carSaveBean.getLoadSurfaceHeight(),carSaveBean.getMaxPayload(),
					carSaveBean.getTailLength(),carSaveBean.getTailWidth(),carSaveBean.getTailHeight(),tCurbWeight,carId});
        }else{
			if (tailNo.endsWith("超")){
				tCurbWeight = "10000";
			}
            String saveSql = "INSERT INTO `tyt_car_detail_tail` (`car_id`,`user_id`,`city`,`car_no`,`is_pure_flat`,`other_pure_flat`," +
					"`other_car_type`,`is_joint_pull`,`joint_pull_length`,`temp_licence_expires`,`load_surface_length`,`load_surface_height`," +
					"`max_payload`,length,width,height,curb_weight) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            this.getBaseDao().executeUpdateSql(saveSql,new Object[]{carId,userId,tailCity,tailNo,isPureFlat,carSaveBean.getOtherPureFlat(),
					carSaveBean.getOtherCarType(),carSaveBean.getIsJointPull(),carSaveBean.getJointPullLength(),carSaveBean.getTempLicenceExpires()
					,carSaveBean.getLoadSurfaceLength(),carSaveBean.getLoadSurfaceHeight(),carSaveBean.getMaxPayload(),carSaveBean.getTailLength(),
					carSaveBean.getTailWidth(),carSaveBean.getTailHeight(),tCurbWeight});
        }
    }

    @Override
    public Long saveCar(CarSaveBean carSaveBean) throws Exception{
		Long carId = checkCar(carSaveBean);
		if (carId==null){
			User byUserId = userService.getByUserId(carSaveBean.getUserId());
			//carId=saveCar(carSaveBean,null,null,null,null,null,null);
			Car car = new Car();
			car.setUserId(carSaveBean.getUserId());
			car.setHeadCity(carSaveBean.getHeadCity());
			car.setHeadNo(carSaveBean.getHeadNo());
			car.setTailCity(carSaveBean.getTailCity());
			car.setTailNo(carSaveBean.getTailNo());
			car.setHeadFailReason(Constant.CAR_FAILURE_REASON);
			car.setAuth(carSaveBean.getAuth());
			BigInteger maxSort = getMaxSort(carSaveBean.getUserId());
			if (maxSort == null) {
				maxSort = new BigInteger("0");
			}
			car.setSort(maxSort.longValue() + 1);
			car.setCreateTime(new Date());
			car.setUpdateTime(new Date());
			car.setHeadDrivingUrl("");
			car.setHasLadder(carSaveBean.getHasLadder());
			car.setClientSign(carSaveBean.getClientSign());
			car.setIsDispatch(byUserId.getIsDispatch() ==null ?"":byUserId.getIsDispatch()+"");
			//6440增加开票状态
			car.setIsInvoice(carSaveBean.getIsInvoice());
			this.getBaseDao().insert(car);
			return car.getId();
        }
        return carId;
    }

    private Long checkCar(CarSaveBean carSaveBean){
		String sql = "SELECT id FROM tyt_car WHERE user_id=? AND head_city=? AND head_no =? AND tail_city=? AND tail_no=? AND is_delete=1 limit 1";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        String todayInDate = TimeUtil.formatDate(new Date());
        List<Car> cars = this.getBaseDao().search(sql, scalarMap, Car.class, new Object[] { carSaveBean.getUserId(),carSaveBean.getHeadCity(),carSaveBean.getHeadNo(),carSaveBean.getTailCity(),carSaveBean.getTailNo() });
        if (cars!=null && cars.size()>0){
            return cars.get(0).getId();
        }else{
            return null;
        }
    }

	@Override
	public MyFullCarListBean getMergeMyCarList(Long userId,String isDispatch,Integer clientVersion) {
		User user = null;
		try {
			logger.info("我的车辆列表传入的用户ID：" + userId);
			user = userService.getByUserId(userId);
		} catch (Exception e) {
			logger.error("查询用户信息失败:{},用户ID:{}", e, userId);
			return null;
		}

		// 1. 获取车辆库数据
		List<CarMergeBean> loacalCarList = new ArrayList<>();
		List<Car> localCars = this.getListByUserId(userId);

		//再查询挂车信息，获取到挂车样式数据
		Map<Long, Integer> carDetailTailCarIdIsPureFlatMap = makeCarDetailTailCarIdIsPureFlatMap(localCars);

		//查询车主认证信息
		Map<Long, TytCarOwnerAuth> tytCarOwnerAuthMap = tytCarOwnerAuthMapper.selectConvertMap(userId);

		CarMergeBean carBean = null;
		for(Car car : localCars) {
			carBean = new CarMergeBean();
			org.springframework.beans.BeanUtils.copyProperties(car, carBean);
			if(StringUtils.equals(car.getAuth(), "1")) {
				carBean.setCarAuth(true);
			}
			//是否车主认证
			carBean.setCarOwnerAuth(Objects.nonNull(tytCarOwnerAuthMap.get(car.getId())));
			//填充挂车样式数据（供后续判断该车是否属于待完善信息车辆）
			car.setIsPureFlat(carDetailTailCarIdIsPureFlatMap.get(car.getId()));
			//判断该车辆是否是待完善车辆
			carBean.setCarIsNeedImprovenData(this.carIsNeedImprovenData(car));

			carBean.setHeadCarNum(car.getHeadCity() + car.getHeadNo());
			carBean.setTailCarNum(car.getTailCity() + car.getTailNo());
			carBean.setState(1);
			carBean.setIsDispatch("1".equals(car.getIsDispatch()) ? 1:0);
			if ("3".equals(car.getCarDegree()+"")){
				carBean.setIsTailored(1);
			}else {
				carBean.setIsTailored(0);
			}
			// 电话列表
			TreeMap<String, String> phoneList = new TreeMap<>();
			if(StringUtils.isNotBlank(carBean.getDriverPhone())) {
				phoneList.put("主司机" + carBean.getDriverName(), carBean.getDriverPhone());
			}
			if(StringUtils.isNotBlank(carBean.getSecondaryDriverPhone())) {
				phoneList.put("副司机" + carBean.getSecondaryDriverName(), carBean.getSecondaryDriverPhone());
			}
			carBean.setOriginalId(car.getId());
			carBean.setPhoneList(phoneList);
			//查询司机新表
			Long driverUserId = car.getDriverUserId();
			Long secondaryDriverUserId = car.getSecondaryDriverUserId();
			if(driverUserId != null){
				DriverDetailVO driverDetail = invoiceDriverService.getDriverDetail(driverUserId);
				if(driverDetail != null){
					carBean.setDriverName(driverDetail.getName());
					carBean.setDriverPhone(driverDetail.getPhone());
				}
			}
			if(secondaryDriverUserId != null){
				DriverDetailVO secondDriverDetail = invoiceDriverService.getDriverDetail(secondaryDriverUserId);
				if(secondDriverDetail != null){
					carBean.setSecondaryDriverName(secondDriverDetail.getName());
					carBean.setSecondaryDriverPhone(secondDriverDetail.getPhone());
				}

			}
			loacalCarList.add(carBean);
		}

		// 2. 获取远程专车数据
		List<CarMergeBean> specialCarList = new ArrayList<>();
		//List<SpecialCar> specialCars = new ArrayList<>();

		int sum = 0, count = 0;

		// 3. 是否存在专车
		//boolean existSpecialCar = !specialCarList.isEmpty();

		// 4. 整合数据并排序
		// 移除本地列表重复数据
		//loacalCarList.removeAll(specialCarList);
		// 以专车列表为主，添加处理后的本地列表数据
		specialCarList.addAll(loacalCarList);
		// 按时间进行倒序，CarMergeBean实现了Comparable接口，null为默认实现
		specialCarList.sort(null);

		List<Long> carIds=new ArrayList<>();
		//获取车辆运输状态
		List<Long> carIdStates=new ArrayList<>();
		for (CarMergeBean bean : specialCarList) {
			carIds.add(bean.getOriginalId());
//			if (!ObjectUtil.equal(bean.getIsDispatch(),1)){
//				bean.setState(1);
//				carIdStates.add(bean.getOriginalId());
//			}
			if (ObjectUtil.equal(bean.getState(),1)){
				carIdStates.add(bean.getOriginalId());
			}
		}

		if(carIds.size()>0){
			List<TytCarDriverArchives> tytCarDriverArchives = carDetailHeadMapper.selectTytCarDriverArchivesById(carIds);
			for (CarMergeBean carMergeBean : specialCarList) {
				for (TytCarDriverArchives tytCarDriverArchive : tytCarDriverArchives) {
					if (tytCarDriverArchive.getCarId().equals(carMergeBean.getOriginalId())){
						carMergeBean.setDriverData(tytCarDriverArchive);
					}
				}
			}
		}
		if (carIdStates.size()>0){
			List<Long> longs = carDetailHeadMapper.selectCarIdsByOrderStatus(carIdStates);
			for (CarMergeBean carMergeBean : specialCarList) {
				carMergeBean.setCarIsCanGetLocation(false);
				for (Long aLong : longs) {
					if (carMergeBean.getOriginalId().equals(aLong)){
						carMergeBean.setState(2);
						String carListLocation = tytConfigService.getStringValue(CAR_LIST_LOCATION,"1");
						boolean authFlag= ("1".equals(carMergeBean.getAuth())&&"1".equals(carListLocation));
						logger.info("车辆列表认证的车辆位置开关【{}】",authFlag);
						if (authFlag && !carMergeBean.getCarIsNeedImprovenData()){
							try {
								carMergeBean.setCarIsCanGetLocation(true);
								Object currentLocation = carCurrentLocationService.getCurrentLocation(carMergeBean.getHeadCarNum());
								if (currentLocation!=null){
									NewCarCurrentLocationBean newCarCurrentLocationBean = JSON.parseObject(JSON.toJSONString(currentLocation), NewCarCurrentLocationBean.class);
									carMergeBean.setCurrentSpeed(newCarCurrentLocationBean.getSpd());
									carMergeBean.setVehiclePositionLat(newCarCurrentLocationBean.getLocationLatitude()!=null ? new BigDecimal(newCarCurrentLocationBean.getLocationLatitude()):null);
									carMergeBean.setVehiclePositionLon(newCarCurrentLocationBean.getLocationLongitude()!=null ? new BigDecimal(newCarCurrentLocationBean.getLocationLongitude()):null);
									carMergeBean.setVehiclePosition(newCarCurrentLocationBean.getNewLocation());
									carMergeBean.setVehiclePositionTime(newCarCurrentLocationBean.getNewLocationTime());
									//6400新增两个有关车辆定位的返回字段（离线状态、离线时长）
									carMergeBean.setOfflineState(newCarCurrentLocationBean.getOfflineState());
									carMergeBean.setOfflineTime(newCarCurrentLocationBean.getOfflineTime());
								}
							} catch (Exception e) {
								logger.info("获取车辆列表车辆位置异常",e);
							}
						}
					}
				}
			}
		}

		// 5. 用户通过身份认证，则填充用户数据
		MyCarUserBean userBean = new MyCarUserBean();
		userBean.setIsDispatch(user.getIsDispatch()==null ? 0:user.getIsDispatch());
		TytUserIdentityAuth identityAuth = userIdentityService.getTytUserIdentityAuth(userId);
		if(identityAuth !=null && identityAuth.getIdentityStatus().equals(1)) {
			// 身份证号和姓名均不为空
			String showName = null;
			if(StringUtils.isNoneBlank(identityAuth.getTrueName(), identityAuth.getIdCard())) {
				userBean.setLastName(identityAuth.getTrueName().substring(0, 1));
				userBean.setCall(IdCardUtil.getCallGender(identityAuth.getIdCard()));
				if(identityAuth.getIdentityType().equals(2) || identityAuth.getIdentityType().equals(8)) {
					showName = identityAuth.getEnterpriseName();
				} else {
					showName = Optional.ofNullable(user.getCarUserName()).orElse(user.getUserName());
				}
			} else {
				showName = identityAuth.getEnterpriseName();
			}
			userBean.setShowName(showName);
			userBean.setNumber(specialCarList.size()+"");
			userBean.setEnterpriseAuthStatus(identityAuth.getEnterpriseAuthStatus());
			// 计算整体好评度平均值
			if(sum != 0 && count !=0) {
				BigDecimal comprehensiveEvaluation = new BigDecimal(sum);
				BigDecimal decimalCount = new BigDecimal(count);
				Integer rating = comprehensiveEvaluation.divide(decimalCount,0,BigDecimal.ROUND_HALF_UP).intValue();
				userBean.setRating(rating);
			}
		}
		MyFullCarListBean carListBean = new MyFullCarListBean();
		carListBean.setUser(userBean);
		carListBean.setCarList(specialCarList);
		carListBean.setExistSpecialCar(false);
		carListBean.setCurrentTime(new Date());
		return carListBean;
	}

	/**
	 * 构造carId-挂车样式map数据，方面后面直接获取
	 * @param localCars
	 * @return true：属于待完善车辆；false：不属于
	 */
	private Map<Long, Integer> makeCarDetailTailCarIdIsPureFlatMap(List<Car> localCars) {
		Map<Long, Integer> carDetailTailCarIdIsPureFlatMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(localCars)) {
			List<Long> carIdList = localCars.stream().map(Car::getId).filter(Objects::nonNull).collect(Collectors.toList());
			if (carIdList.isEmpty()) {
				return carDetailTailCarIdIsPureFlatMap;
			}
			List<CarDetailTail> carDetailTails = carDetailTailService.selectCarDetailTailByCarIdList(carIdList);
			if (CollectionUtils.isNotEmpty(carDetailTails)) {
				for (CarDetailTail carDetailTail : carDetailTails) {
					carDetailTailCarIdIsPureFlatMap.put(carDetailTail.getCarId(), carDetailTail.getIsPureFlat());
				}
			}
		}
		return carDetailTailCarIdIsPureFlatMap;
	}

	@Override
	public Map<String, Object> getTrailerStyleType() {
		Map<String, Object> map=new HashMap<>();
		//挂车样式
		map.put("trailerStyle",TytSourceUtil.getSourceList("tail_car_style"));
		//挂车类型
		map.put("trailerType",TytSourceUtil.getSourceList("tail_car_type"));
		return map;
	}

	@Override
	public Car updateDispatchCar(Long id) {
		Car car = this.getBaseDao().findById(id);
		if(car==null){
			return null;
		}
		CarDetailHead carDetailHead = new CarDetailHead();
		carDetailHead.setCarId(id);
		//查询对应的车头信息
		carDetailHead = carDetailHeadService.find(carDetailHead);
		CarDetailTail carDetailTail = new CarDetailTail();
		carDetailTail.setCarId(id);
		//查询对应的挂车信息
		carDetailTail=carDetailTailService.find(carDetailTail);
		if (carDetailHead==null){
			carDetailHead=new CarDetailHead();
		}
		if (carDetailTail==null){
			carDetailTail=new CarDetailTail();
		}
		//数据的相关封装
		carDetailHead.setDrivingForm("0".equals(car.getDrivingForm()) ? "":car.getDrivingForm());
		carDetailHead.setHorsePower(car.getHorsePower());
		carDetailHead.setEmissionStandard("0".equals(car.getEmissionStandard()) ? "":car.getEmissionStandard());
		carDetailHead.setHeadBelongType(carDetailHead.getBlongType());
		carDetailHead.setHeadPhone(car.getHeadPhone());
		String carHeadType=null;
		if ("1".equals(car.getCarHeadType()+"")){
			carHeadType="高顶";
		}
		if ("2".equals(car.getCarHeadType()+"")){
			carHeadType="低顶";
		}
		carDetailHead.setCarTypeName(carHeadType);
		car.setCarHeadData(carDetailHead);
		carDetailTail.setTailPhone(car.getTailPhone());
		carDetailTail.setHasLadder(car.getHasLadder());
		if (car.getCarType()!=null){
			carDetailTail.setCarTypeName(TytSourceUtil.getSourceName("tail_car_type",car.getCarType()+"").getName());
			carDetailTail.setCarType(car.getCarType()+"");
		}
		if (carDetailTail.getIsPureFlat()!=null){
			carDetailTail.setIsPureFlatName(TytSourceUtil.getSourceName("tail_car_style", carDetailTail.getIsPureFlat() + "").getName());
		}
		car.setCarTailData(carDetailTail);
		if (car.getDriverId()!=null){
			TytCarDriverArchives archives = tytCarDriverArchivesService.getById(car.getDriverId());
			car.setDriverData(archives);
		}
		return car;
	}

	@Override
	public void updateCarHead(DispatchCarBean dispatchCarBean,Car car) {
		int i = carDetailHeadMapper.selectCarDetailHeadNumberByCarId(car.getId());
		if (i>0){
			carDetailHeadMapper.updateCarDetailHead(dispatchCarBean);
		}else {
			CarDetailHead carDetailHead=new CarDetailHead();
			carDetailHead.setCarId(car.getId());
			carDetailHead.setCity(car.getHeadCity());
			carDetailHead.setCarNo(car.getHeadNo());
			carDetailHead.setRoadCardOtherSideUrl(dispatchCarBean.getRoadCardOtherSideUrl());
			carDetailHead.setRoadCardPositiveUrl(dispatchCarBean.getRoadCardPositiveUrl());
			carDetailHead.setRoadLicenseNoUrl(dispatchCarBean.getRoadLicenseNoUrl());
			carDetailHead.setState("1");
			carDetailHead.setUseNature("货运");
			carDetailHead.setUserId(car.getUserId());
			carDetailHead.setRoadTransportType(dispatchCarBean.getRoadTransportType());
			carDetailHeadService.add(carDetailHead);
		}
	}

	@Override
	public void updateCarDriverBind(Long carId, Long driverId,Long userId) throws Exception {
		carDetailHeadMapper.updateCarDriverId(carId, driverId);
	}


	@Override
	public void updateDispatchStatus(long userId, int dispatchStatus) {
		String sql = "update tyt_car set is_dispatch=?,update_time=? where user_id=?";
		final Object[] params = {
				dispatchStatus, // 设置为调度车
				new Date(),
				userId
		};
		// 执行数据库更新
		this.executeUpdateSql(sql, params);
	}

	@Override
	public Integer updateCarPhone(String tailPhone, String headPhone, Long id) {
		return carDetailHeadMapper.updateCarPhone(tailPhone, headPhone, id);
	}

	@Override
	public boolean selectCarIsSpecialCarCount(Long userId) {
		Integer integer = carDetailHeadMapper.selectCarIsSpecialCarCount(userId);
		if (integer!=null&&integer>0){
			return true;
		}
		return false;
	}

	@Override
	public int getCarNum(Long userId) {

		User user = null;
		try {
			user = userService.getByUserId(userId);
		} catch (Exception e) {
			logger.error("查询用户信息失败", e);
			return 0;
		}

		// 1. 获取车辆库数据
		List<CarMergeBean> loacalCarList = new ArrayList<>();
		List<Car> localCars = this.getListByUserId(userId);
		Set carSet = new HashSet();
		if(null != localCars){
			for(Car car : localCars) {
				carSet.add(car.getHeadCity() + car.getHeadNo()+car.getTailCity() + car.getTailNo());
			}
		}
		return carSet.size();
	}

	/**
	 * 获取已认证的挂车列表
	 * @param userId
	 * @param auth
	 * @return
	 */
	@Override
	public List<QueryCar> getAuthTailList(Long userId, String auth) {
		return ((CarDao) (this.getBaseDao())).getAuthTailList(userId, auth);
	}

	/**
	 * 判断该车辆是否是待完善信息车辆
	 * @param car 车辆信息
	 * @return 1:是待完善信息车辆；2:否
	 */
	@Override
	public boolean carIsNeedImprovenData(Car car) {
		//	判断车辆认证状态是否为认证通过（auth == 1）
		//		是：
		// 			判断挂车型号是否为空
		//				是：
		//					算待完善 needImprovenData 1：待完善；2:非待完善
		//				否：
		//					判断挂车车牌号是否为空
		//						是：算待完善
		//					判断挂车型号是否为单机板
		//						不是单机板：
		//							判断挂车样式是否为空
		//								是：算待完善
		//								否：不算待完善
		//		否：不算
		if (car == null || StringUtils.isEmpty(car.getAuth()) || !"1".equals(car.getAuth())) {
			return false;
		}
		//车头道运证为空 待完善
		if (car.getCarType() != null && car.getCarType() != 15 && StringUtils.isBlank(car.getHeadTransportHomepageUrl())){
			return true;
		}
		long now = TimeUtil.weeHours(new Date(),0).getTime();
		//车头、车挂行驶证有效期
		if (Objects.nonNull(car.getHeadDrivingExpiredTime()) && car.getHeadDrivingExpiredTime().getTime()< now){
			return true;
		}
		if (car.getCarType() == null) {
			return true;
		} else {
			if (StringUtils.isEmpty(car.getTailNo()) || StringUtils.isEmpty(car.getTailCity())) {
				return true;
			}
			if (car.getCarType() != 14 && car.getCarType() != 15 && (Objects.nonNull(car.getTailDrivingExpiredTime()) && car.getTailDrivingExpiredTime().getTime() < now)){
				return true;
			}
            return car.getCarType() != 14 && car.getCarType() != 15 && car.getIsPureFlat() == null;
		}
	}

	@Override
	public ResultMsgBean ocrDrivingLicenseInfo(String url) {
		try {
			//识别行驶证副页
			VehicleLicenseBackVo vehicleLicenseBackVo = commonApiService.vehicleLicenseBackOcr(url);
			logger.info("OCR识别车辆行驶证信息,行驶证副页识别结果={}", JSONObject.toJSONString(vehicleLicenseBackVo));
			if (Objects.nonNull(vehicleLicenseBackVo)) {
				//判断行驶证是否过期逻辑
				if (StringUtils.isNotBlank(vehicleLicenseBackVo.getInspectionRecord())) {
					LocalDate now = LocalDate.now();
					Integer year = getYearBySubString(vehicleLicenseBackVo.getInspectionRecord());
					if (Objects.nonNull(year) && now.getYear() > year) {
						return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "行驶证副页上的有效期已过期，请上传行驶证副页反面或更新证件");
					}
					Integer month = getMonthBySubString(vehicleLicenseBackVo.getInspectionRecord());
					if (Objects.nonNull(month) && now.getMonth().getValue() > month) {
						return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "行驶证副页上的有效期已过期，请上传行驶证副页反面或更新证件");
					}
				}
				//判断车辆是否报废逻辑
				if (StringUtils.isNotBlank(vehicleLicenseBackVo.getRemark())) {
					LocalDate now = LocalDate.now();
					LocalDate remarkDate = parseScrapTime(vehicleLicenseBackVo.getRemark());
					if (Objects.nonNull(remarkDate) && now.isAfter(remarkDate)) {
						return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "车辆已报废");
					}
				}
			}

			VehicleLicenseFrontVo vehicleLicenseFrontVo = commonApiService.vehicleLicenseMainOcr(url);
			logger.info("OCR识别车辆行驶证信息,行驶证正页识别结果={}",JSONObject.toJSONString(vehicleLicenseFrontVo));
			if (vehicleLicenseBackVo.allFieldsIsNull() && vehicleLicenseFrontVo.allFieldsIsNull()){
				return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "识别失败");
			}
			//判断行驶证 正页 和 副页的车牌号是否一致
			if (Objects.nonNull(vehicleLicenseBackVo) && Objects.nonNull(vehicleLicenseFrontVo) &&
					StringUtils.isNotBlank(vehicleLicenseBackVo.getCarNumber()) && StringUtils.isNotBlank(vehicleLicenseFrontVo.getCarNumber()) &&
					!vehicleLicenseBackVo.getCarNumber().equals(vehicleLicenseFrontVo.getCarNumber())) {
				return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "行驶证正副页车牌号不一致");
			}

			if (Objects.nonNull(vehicleLicenseFrontVo)) {
//				//判断行驶证车辆是否为货车
//				if (StringUtils.isNotBlank(vehicleLicenseFrontVo.getUseNature()) && !vehicleLicenseFrontVo.getUseNature().equals("货运")) {
//					return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "请确认车辆，当前行驶证车辆不是货车");
//				}
				//判断行驶证车辆使用性质是否为货运
				if (StringUtils.isNotBlank(vehicleLicenseFrontVo.getUseNature()) && !vehicleLicenseFrontVo.getUseNature().equals("货运")) {
					return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "请确认行驶证车辆，当前行驶证车辆使用性质不为货运");
				}
			}
			return ResultMsgBean.successResponse();
		} catch (Exception e) {
			logger.error("添加车辆OCR识别异常:", e);
		}
		return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "识别异常");
	}

	/**
	 * 获取OCR识别检验记录 年
	 * @param input
	 * @return java.lang.Integer
	 */
	public static Integer getYearBySubString(String input) {
		if (StringUtils.isBlank(input)) {
			return null;
		}
		try {
			int yearIndex = input.indexOf("年");
			String year = input.substring(yearIndex - 4, yearIndex);
			return Integer.parseInt(year);
		} catch (Exception e) {
			logger.error("获取OCR检验记录年份异常:", e);
		}
		return null;
	}

	/**
	 * 获取OCR识别检验记录 月
	 * @param input
	 * @return java.lang.Integer
	 */
	public static Integer getMonthBySubString(String input) {
		if (StringUtils.isBlank(input)) {
			return null;
		}
		try {
			int yearIndex = input.indexOf("月");
			String month = input.substring(yearIndex - 2, yearIndex);
			return Integer.parseInt(month);
		} catch (Exception e) {
			logger.error("获取OCR检验记录月份异常:", e);
		}
		return null;
	}

	private  LocalDate parseScrapTime(String originalInformation) {
		// 正则表达式匹配日期格式
		String regex = "\\d{4}-\\d{1,2}-\\d{1,2}";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(originalInformation);
		if (matcher.find()) {
			String dateStr = matcher.group(); // 提取匹配的日期字符串
			try {
				LocalDate parse = LocalDate.parse(dateStr);
				return parse;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	@Override
	public UserCarInfoVO getUserCarInfoVO(Long carId) {
		String sql = "SELECT id AS carId,head_driving_url AS headDrivingUrl,head_city AS headCity,head_no AS headNo,head_name AS headName from tyt_car where id = ?";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("carId", Hibernate.LONG);
		scalarMap.put("headDrivingUrl", Hibernate.STRING);
		scalarMap.put("headNo", Hibernate.STRING);
		scalarMap.put("headCity", Hibernate.STRING);
		scalarMap.put("headName", Hibernate.STRING);
		List<UserCarInfoVO> search = this.getBaseDao().search(sql, scalarMap, UserCarInfoVO.class, new Object[]{carId});
		if (CollectionUtils.isNotEmpty(search)) {
			return this.getBaseDao().search(sql, scalarMap, UserCarInfoVO.class, new Object[]{carId}).get(0);
		}
		return null;
	}

	@Override
	public List<Long> selectIds(Long userId, String auth) {
		String sql = "SELECT id FROM tyt_car c WHERE  c.user_id=? and c.auth=?  and is_delete=?";
		return this.getBaseDao().search(sql, null, null, new Object[]{userId,auth,1});
	}

	@Override
	public ResultMsgBean getCarOcrDrivingLicenseInfo(String url) {
		try {
			//识别行驶证副页
			VehicleLicenseBackVo vehicleLicenseBackVo = commonApiService.vehicleLicenseBackOcr(url);
			vehicleLicenseBackVo.setOcrJsonText(null);
			logger.info("OCR识别车辆行驶证信息,行驶证副页识别结果={}", JSON.toJSONString(vehicleLicenseBackVo));

			VehicleLicenseFrontVo vehicleLicenseFrontVo = commonApiService.vehicleLicenseMainOcr(url);
			vehicleLicenseFrontVo.setOcrJsonText(null);
			logger.info("OCR识别车辆行驶证信息,行驶证正页识别结果={}", JSON.toJSONString(vehicleLicenseFrontVo));
			if (vehicleLicenseBackVo.allFieldsIsNull() && vehicleLicenseFrontVo.allFieldsIsNull()) {
				return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "识别失败");
			}
			//聚合信息
			CarOcrDrivingLicenseInfo carOcrDrivingLicenseInfo = new CarOcrDrivingLicenseInfo();
			carOcrDrivingLicenseInfo.setVehicleLicenseBackVo(vehicleLicenseBackVo);
			carOcrDrivingLicenseInfo.setVehicleLicenseFrontVo(vehicleLicenseFrontVo);

			//处理提示信息
			String warnMessage = getWarnMessage(carOcrDrivingLicenseInfo);
			if (StringUtils.isNotBlank(warnMessage)) {
				carOcrDrivingLicenseInfo.setWarnMessage(warnMessage);
			}

			//处理过期时间
			if (StringUtils.isNotBlank(vehicleLicenseBackVo.getInspectionRecord())) {
				Integer year = getYearBySubString(vehicleLicenseBackVo.getInspectionRecord());
				Integer month = getMonthBySubString(vehicleLicenseBackVo.getInspectionRecord());
				if (Objects.nonNull(year) && Objects.nonNull(month)) {
					String lastDay = TimeUtil.getLastDay(year, month);
					vehicleLicenseBackVo.setInspectionRecord(lastDay);
				}else {
					vehicleLicenseBackVo.setInspectionRecord(null);
				}
			}
			return ResultMsgBean.successResponse(carOcrDrivingLicenseInfo);
		} catch (Exception e) {
			logger.error("添加车辆OCR识别异常getCarOcrDrivingLicenseInfo:", e);
		}
		return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "识别异常");
	}


	private String getWarnMessage(CarOcrDrivingLicenseInfo carOcrDrivingLicenseInfo) {
		if (Objects.isNull(carOcrDrivingLicenseInfo)) {
			return null;
		}
		VehicleLicenseBackVo vehicleLicenseBackVo = carOcrDrivingLicenseInfo.getVehicleLicenseBackVo();
		if (Objects.nonNull(carOcrDrivingLicenseInfo.getVehicleLicenseBackVo())) {
			//判断行驶证是否过期逻辑
			if (StringUtils.isNotBlank(vehicleLicenseBackVo.getInspectionRecord())) {
				LocalDate now = LocalDate.now();
				Integer year = getYearBySubString(vehicleLicenseBackVo.getInspectionRecord());
				if (Objects.nonNull(year) && now.getYear() > year) {
					return "行驶证副页上的有效期已过期，请上传行驶证副页反面或更新证件";
				}
				Integer month = getMonthBySubString(vehicleLicenseBackVo.getInspectionRecord());
				if (Objects.nonNull(month) && now.getMonth().getValue() > month) {
					return "行驶证副页上的有效期已过期，请上传行驶证副页反面或更新证件";
				}
			}
			//判断车辆是否报废逻辑
			if (StringUtils.isNotBlank(vehicleLicenseBackVo.getRemark())) {
				LocalDate now = LocalDate.now();
				LocalDate remarkDate = parseScrapTime(vehicleLicenseBackVo.getRemark());
				if (Objects.nonNull(remarkDate) && now.isAfter(remarkDate)) {
					return "车辆已报废";
				}
			}
		}

		VehicleLicenseFrontVo vehicleLicenseFrontVo = carOcrDrivingLicenseInfo.getVehicleLicenseFrontVo();

		//判断行驶证 正页 和 副页的车牌号是否一致
		if (Objects.nonNull(vehicleLicenseBackVo) && Objects.nonNull(vehicleLicenseFrontVo) &&
				StringUtils.isNotBlank(vehicleLicenseBackVo.getCarNumber()) && StringUtils.isNotBlank(vehicleLicenseFrontVo.getCarNumber()) &&
				!vehicleLicenseBackVo.getCarNumber().equals(vehicleLicenseFrontVo.getCarNumber())) {
			return "行驶证正副页车牌号不一致";
		}

		if (Objects.nonNull(vehicleLicenseFrontVo)) {
			//判断行驶证车辆使用性质是否为货运
			if (StringUtils.isNotBlank(vehicleLicenseFrontVo.getUseNature()) && !vehicleLicenseFrontVo.getUseNature().equals("货运")) {
				return "请确认行驶证车辆，当前行驶证车辆使用性质不为货运";
			}
		}
		return null;
	}

	@Override
	public void refreshIsInvoice(Long userId) {
		if (Objects.isNull(userId)) {
			return;
		}
		List<Car> carList = getListByUserId(userId);
		if (CollectionUtils.isEmpty(carList)){
			return;
		}

		for (Car car : carList) {
			if (car.getIsInvoice() == CarInvoiceStatusEnum.WAIT_AUDIT.getCode()){
				continue;
			}
			if ("0".equals(car.getAuth())){
				continue;
			}
			int headCarType = TRACTOR; //1牵引车 2 单机板
			//校验tyt_car_detail_head 表数据是否符合开票
			CarDetailHead carDetailHead = carDetailHeadService.getByCarId(car.getId());
			//校验tyt_car_detail_tail 表数据是否符合开票
			CarDetailTail carDetailTail = carDetailTailService.getByCarId(car.getId());
			//判断车辆是否为单机板
			if (null != carDetailHead && StringUtils.isNotBlank(carDetailHead.getCarType())){
				String carType = scheduleCarService.getCarTypeByClassify(carDetailHead.getCarType());
				if (StringUtils.isNotBlank(carType) && !carType.contains("牵引")) {
					headCarType = SINGLE_CAR;
				}
			}

			int selfStatus = checkSelfStatus(car,carDetailHead,carDetailTail, headCarType);
			int thirdPartyStatus = checkThirdPartyStatus(car, carDetailHead,carDetailTail,headCarType);
			CarCheckRpcVO carCheckRpcVO = checkXhlThirdPartyStatus(car, carDetailHead);
			Integer xhlThirdPartyStatus = car.getXhlPartyRequire();
			if(Objects.nonNull(carCheckRpcVO)){
				xhlThirdPartyStatus = carCheckRpcVO.getCarCheckStatus();
			}

			updateIsInvoice(car.getId(),selfStatus,thirdPartyStatus,xhlThirdPartyStatus);
		}
	}

	private int checkSelfStatus(Car car, CarDetailHead carDetailHead, CarDetailTail carDetailTail, int headCarType){
		int checkUrl = checkUrl(car, headCarType, 1);
		if (checkUrl != CarInvoiceStatusEnum.INVOICE.getCode()){
			return checkUrl;
		}
		if (StringUtils.isBlank(car.getAuth()) || "2".equals(car.getAuth())){
			return CarInvoiceStatusEnum.NO_INVOICE.getCode();
		}
		boolean checkCarMainIsInvoice = checkCarMainIsInvoice(car,headCarType,1);
		//校验tyt_car 表数据是否开票
		if (BooleanUtils.isFalse(checkCarMainIsInvoice)) {
			return CarInvoiceStatusEnum.NO_INVOICE.getCode();
		}
		boolean checkCarDetailHead = checkCarDetailHead(carDetailHead,headCarType);
		if (BooleanUtils.isFalse(checkCarDetailHead)) {
			return CarInvoiceStatusEnum.NO_INVOICE.getCode();
		}
		boolean checkCarDetailTail = checkCarDetailTail(carDetailTail,headCarType);
		if (BooleanUtils.isFalse(checkCarDetailTail)) {
			return CarInvoiceStatusEnum.NO_INVOICE.getCode();
		}
		//如果都符合开票 则将该车辆改为符合开票
		return CarInvoiceStatusEnum.INVOICE.getCode();
	}

	private int checkThirdPartyStatus(Car car, CarDetailHead carDetailHead, CarDetailTail carDetailTail, int headCarType){
		int checkUrl = checkUrl(car, headCarType, 2);
		if (checkUrl != CarInvoiceStatusEnum.INVOICE.getCode()){
			return checkUrl;
		}
		if (StringUtils.isBlank(car.getAuth()) || "2".equals(car.getAuth())){
			return CarInvoiceStatusEnum.NO_INVOICE.getCode();
		}
		boolean checkCarMainIsInvoice = checkCarMainIsInvoice(car,headCarType,2);
		//校验tyt_car 表数据是否开票
		if (BooleanUtils.isFalse(checkCarMainIsInvoice)) {
			return CarInvoiceStatusEnum.NO_INVOICE.getCode();
		}
		boolean checkCarDetailHead = checkCarDetailHeadForThird(carDetailHead,headCarType);
		if (BooleanUtils.isFalse(checkCarDetailHead)) {
			return CarInvoiceStatusEnum.NO_INVOICE.getCode();
		}
		boolean checkCarDetailTail = checkCarDetailTailForThird(carDetailTail,headCarType);
		if (BooleanUtils.isFalse(checkCarDetailTail)) {
			return CarInvoiceStatusEnum.NO_INVOICE.getCode();
		}
		//如果都符合开票 则将该车辆改为符合开票
		return CarInvoiceStatusEnum.INVOICE.getCode();
	}

	@Autowired
	private ApiInvoiceCapacityClient apiInvoiceCapacityClient;

	private CarCheckRpcVO checkXhlThirdPartyStatus(Car car, CarDetailHead carDetailHead){

		CarCheckDTO carCheckDTO = new CarCheckDTO();
		carCheckDTO.setServiceProviderCode("XHL");
		carCheckDTO.setHeadNo(car.getHeadNo());
		carCheckDTO.setHeadCity(car.getHeadCity());
		carCheckDTO.setHeadTransportNo(car.getHeadTransportNo());
		carCheckDTO.setHeadOwner(car.getHeadName());
		carCheckDTO.setHeadVehicleEnergyType(car.getHeadVehicleEnergyType());
		carCheckDTO.setHeadLicensePlateColor(car.getHeadLicensePlateColor());
		carCheckDTO.setHeadBusinessLicenseNo(car.getHeadBusinessLicenseNo());
		carCheckDTO.setHeadDrivingUrl(car.getHeadDrivingUrl());
		carCheckDTO.setHeadDrivingSubpagePicUrl(car.getHeadDrivingSubpageUrl());
		carCheckDTO.setHeadTransportHomepageUrl(car.getHeadTransportHomepageUrl());
		carCheckDTO.setHeadDrivingExpiredTime(car.getHeadDrivingExpiredTime());
		carCheckDTO.setHeadTransportExpiredTime(car.getHeadTransportExpiredTime());
		if(Objects.nonNull(carDetailHead)){
			carCheckDTO.setHeadCarType(carDetailHead.getCarType());
			carCheckDTO.setHeadLength(carDetailHead.getLength());
			carCheckDTO.setHeadWidth(carDetailHead.getWidth());
			carCheckDTO.setHeadHeight(carDetailHead.getHeight());
			carCheckDTO.setHeadCarRegister(carDetailHead.getCarRegister());
			carCheckDTO.setHeadIssueDate(carDetailHead.getIssueDate());
			carCheckDTO.setHeadScrapDate(carDetailHead.getScrapDate());
			carCheckDTO.setHeadTotalWeight(carDetailHead.getTotalWeight());
			carCheckDTO.setHeadCurbWeight(carDetailHead.getCurbWeight());
			carCheckDTO.setHeadCheckWeight(carDetailHead.getCheckWeight());
			carCheckDTO.setHeadTowWeight(carDetailHead.getTowWeight());
			carCheckDTO.setHeadCarIdCode(carDetailHead.getCarIdcode());
			carCheckDTO.setHeadCarEngineNo(carDetailHead.getCarEngineNo());
			carCheckDTO.setHeadIssueAuthority(carDetailHead.getHeadIssueAuthority());
		}
		try {

			return apiInvoiceCapacityClient.checkCar(carCheckDTO).execute().body();
		}catch (Exception e){
			logger.info("调用xhl车检接口异常,", e);
			return null;
		}
	}

	private int checkUrl(Car car, int headCarType, int flag){
		if (StringUtils.isBlank(car.getHeadDrivingUrl())){
			return CarInvoiceStatusEnum.DEFICIENCY.getCode();
		}
		Integer carType = car.getCarType();
		if ((null == carType || 15 != carType) && (StringUtils.isBlank(car.getHeadTransportHomepageUrl()))){
			return CarInvoiceStatusEnum.DEFICIENCY.getCode();
		}
		if (null != car.getHeadDrivingExpiredTime() && TimeUtil.weeHours(car.getHeadDrivingExpiredTime(),1).before(new Date())
				&& StringUtils.isBlank(car.getHeadDrivingSubpageUrl())){
			return CarInvoiceStatusEnum.DEFICIENCY.getCode();
		}
		// 挂车
		if (headCarType == TRACTOR){
			if (car.getTailNo().contains("超")){
				if (StringUtils.isBlank(car.getTailDrivingUrl()) || StringUtils.isBlank(car.getTailDrivingOtherSideUrl())){
					return CarInvoiceStatusEnum.DEFICIENCY.getCode();
				}
				if(null == car.getTailDrivingExpiredTime() || TimeUtil.weeHours(car.getTailDrivingExpiredTime(), 1).before(new Date())){
					return CarInvoiceStatusEnum.DEFICIENCY.getCode();
				}
			}else {
				if (StringUtils.isBlank(car.getTailDrivingUrl())){
					return CarInvoiceStatusEnum.DEFICIENCY.getCode();
				}
				if (flag == 1 && StringUtils.isBlank(car.getTailTransportHomepageUrl())){
					return CarInvoiceStatusEnum.DEFICIENCY.getCode();
				}
			}
			if (null != car.getTailDrivingExpiredTime() && TimeUtil.weeHours(car.getTailDrivingExpiredTime(),1).before(new Date())
					&& StringUtils.isBlank(car.getTailDrivingSubpageUrl())){
				return CarInvoiceStatusEnum.DEFICIENCY.getCode();
			}
		}
		return CarInvoiceStatusEnum.INVOICE.getCode();
	}

	/**
	 * 校验tyt_car 表字段是否满足开票
	 * @param car
	 * @return
	 */
	public boolean checkCarMainIsInvoice(Car car, Integer headCarType, int flag) {
		if (Objects.isNull(car)) {
			return false;
		}
		//校验必填字段
		if (StringUtils.isBlank(car.getHeadCity())
				|| StringUtils.isBlank(car.getHeadNo())
				|| StringUtils.isBlank(car.getHeadName())
				|| StringUtils.isBlank(car.getHeadVehicleEnergyType())
				|| Objects.isNull(car.getHeadDrivingExpiredTime())
				|| StringUtils.isBlank(car.getTailCity())
				|| StringUtils.isBlank(car.getTailNo())) {
			return false;
		}

		Integer carType = car.getCarType();
		if (null == carType || 15 != carType){
			if (StringUtils.isBlank(car.getHeadTransportNo())
					|| StringUtils.isBlank(car.getHeadBusinessLicenseNo())
					|| StringUtils.isBlank(car.getHeadLicensePlateColor())
					|| StringUtils.isBlank(car.getHeadCarNo())
					|| StringUtils.isBlank(car.getHeadVehicleType())
					|| Objects.isNull(car.getHeadIssueDate())){
				return false;
			}
			if ("1".equals(car.getHeadAuthStatus()) && car.getHeadTransportAuthStatus() == 1 && (StringUtils.isNotBlank(car.getHeadCarNo()))) {
				String headNo = car.getHeadCity() + car.getHeadNo();
				if (!car.getHeadCarNo().equals(headNo)) {
					return false;
				}
			}
		}

		if (flag == 1 && "1".equals(car.getTailAuthStatus()) && car.getTailTransportAuthStatus() == 1 && (StringUtils.isNotBlank(car.getTailCarNo()))) {
			String tailNo = car.getTailCity() + car.getTailNo();
			if (!car.getTailCarNo().equals(tailNo)) {
				return false;
			}
		}

		//校验 车头\挂车\车头道路运输证\挂车道路运输证 有效期
		if (TimeUtil.weeHours(car.getHeadDrivingExpiredTime(),1).before(new Date())) {
			return false;
		}
		if (headCarType == TRACTOR){
			if (flag == 1 && !car.getTailNo().contains("超") && (StringUtils.isBlank(car.getTailCarNo())
					|| StringUtils.isBlank(car.getTailVehicleType())
					|| Objects.isNull(car.getTailIssueDate())
					|| StringUtils.isBlank(car.getTailTransportNo())
					|| StringUtils.isBlank(car.getTailBusinessLicenseNo()))){
				return false;
			}
			if(null == car.getTailDrivingExpiredTime()){
				return false;
			}
            return !TimeUtil.weeHours(car.getTailDrivingExpiredTime(), 1).before(new Date());
		}
		return true;
	}

	/**
	 * 校验 tyt_car_detail_head 表字段是否满足开票
	 * @param carDetailHead
	 * @return
	 */
	public boolean checkCarDetailHead(CarDetailHead carDetailHead, int headCarType) {
		if (Objects.isNull(carDetailHead)) {
			return false;
		}
		if (StringUtils.isBlank(carDetailHead.getCurbWeight())
				|| Objects.isNull(carDetailHead.getMaximumAxleLoad())
				|| StringUtils.isBlank(carDetailHead.getHeadIssueAuthority())) {
			return false;
		}
		if (headCarType == TRACTOR){
            return !StringUtils.isBlank(carDetailHead.getTowWeight());
		}else {
            return !StringUtils.isBlank(carDetailHead.getCheckWeight());
		}
    }

	private boolean checkCarDetailHeadForThird(CarDetailHead carDetailHead, int headCarType) {
		if (Objects.isNull(carDetailHead)) {
			return false;
		}
		if (StringUtils.isBlank(carDetailHead.getCarType())
				|| StringUtils.isBlank(carDetailHead.getCurbWeight())
				|| Objects.isNull(carDetailHead.getMaximumAxleLoad())
				|| StringUtils.isBlank(carDetailHead.getCarIdcode())
				|| StringUtils.isBlank(carDetailHead.getHeadIssueAuthority())
				|| Objects.isNull(carDetailHead.getIssueDate())) {
			return false;
		}
		if (headCarType == TRACTOR){
			return !StringUtils.isBlank(carDetailHead.getTowWeight());
		}else {
			return !StringUtils.isBlank(carDetailHead.getCheckWeight());
		}
	}

	/**
	 * 校验 tyt_car_detail_tail 表字段是否满足开票
	 * @param carDetailTail
	 * @return
	 */
	public boolean checkCarDetailTail(CarDetailTail carDetailTail, int headCarType) {
		if (Objects.isNull(carDetailTail)) {
			return false;
		}
		if (headCarType == TRACTOR){
			if (StringUtils.isBlank(carDetailTail.getCarType())
					|| StringUtils.isBlank(carDetailTail.getCurbWeight())
					|| StringUtils.isBlank(carDetailTail.getCheckWeight())
					|| StringUtils.isBlank(carDetailTail.getLength())
					|| StringUtils.isBlank(carDetailTail.getWidth())
//					|| StringUtils.isBlank(carDetailTail.getHeight())
					|| StringUtils.isBlank(carDetailTail.getLoadSurfaceHeight())
					|| Objects.isNull(carDetailTail.getMaximumAxleLoad())
					|| StringUtils.isBlank(carDetailTail.getTailIssueAuthority())) {
				return false;
			}
		}

		return true;
	}

	private boolean checkCarDetailTailForThird(CarDetailTail carDetailTail, int headCarType) {
		if (Objects.isNull(carDetailTail)) {
			return false;
		}
		if (headCarType == TRACTOR){
			if (StringUtils.isBlank(carDetailTail.getCurbWeight())
					|| StringUtils.isBlank(carDetailTail.getLength())
					|| StringUtils.isBlank(carDetailTail.getWidth())
					|| StringUtils.isBlank(carDetailTail.getLoadSurfaceHeight())
					|| Objects.isNull(carDetailTail.getMaximumAxleLoad())) {
				return false;
			}
		}
		return true;
	}

	public static void main(String[] args) {
		String str = "2018-01-10仅可用于运送不可拆解物体";


//		LocalDate now = LocalDate.now();
//		LocalDate remarkDate = parseScrapTime(str);
//		if (now.isAfter(remarkDate)) {
//			System.out.println("车辆已报废");
//		}

		//String str = "今天是2022年12月20日";

		// 正则表达式匹配日期格式
		String regex = "\\d{4}-\\d{1,2}-\\d{1,2}";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(str);

		if (matcher.find()) {
			String dateStr = matcher.group(); // 提取匹配的日期字符串
		}
	}

	@Override
	public Map<String,List<UserCarBean>> getCarList(Long userId, Long id,String goodsWeight){

		Map<String, List<UserCarBean>> stringListMap = new HashMap<>();
		//查询用户的车辆信息
		List<UserCarBean> userCars = getUserCar(userId);
		if(CollectionUtils.isEmpty(userCars)){
			return stringListMap;
		}

		//货物重量
		BigDecimal weight = (StringUtils.isNotEmpty(goodsWeight)) ? new BigDecimal(goodsWeight).multiply(new BigDecimal("1000")) : new BigDecimal(BigInteger.ZERO);
		//获取车辆信息id
		List<Long> idList = userCars.stream().map(UserCarBean::getId).collect(Collectors.toList());
		//挂车信息
		Map<Long,CarDetailTail> tailMap = carDetailTailService.getByCarIds(idList);
		//车头信息
		Map<Long,CarDetailHead> headMap = carDetailHeadService.getByCarIds(idList);

		stringListMap = convertCarList(userCars, tailMap, headMap, weight);
		return stringListMap;

	}


	@Override
	public Map<String, List<UserCarBean>> getCarInvoiceList(Long userId, Long id, TransportMain main, TytTransportEnterpriseLog transportEnterpriseLog) throws Exception{
		Map<String, List<UserCarBean>> stringListMap = new HashMap<>();
		//查询用户的车辆信息
		List<UserCarBean> userCars = getUserCar(userId);
		if(CollectionUtils.isEmpty(userCars)){
			return stringListMap;
		}
		//符合开票要求的
		List<UserCarBean> accordCars = new ArrayList<>();

		//不符合开票要求的
		List<UserCarBean> notAccordCars = new ArrayList<>();
		TytInvoiceTransportEnterpriseConfigLog log = getLastTytInvoiceTransportEnterpriseConfig(main.getUserId(), transportEnterpriseLog.getServiceProviderCode());
		for(UserCarBean userCarBean : userCars){
			userCarBean.setIsInvoice(userCarBean.getThirdPartyRequire());
			if(InvoiceServiceProviderEnum.XHL.getCode().equals(transportEnterpriseLog.getServiceProviderCode())){
				userCarBean.setIsInvoice(userCarBean.getXhlPartyRequire());
			}
			Boolean aBoolean = checkUserCar(userCarBean, log);
			logger.info("车辆是否匹配车辆id【{}】,【{}】,",userCarBean.getId(),aBoolean);
			Boolean checkCar = checkCar(userCarBean, main);
			if(aBoolean && checkCar){
				accordCars.add(userCarBean);
				continue;
			}
			notAccordCars.add(userCarBean);
		}
		stringListMap.put("accordCars", accordCars);
		stringListMap.put("notAccordCars", notAccordCars);
		return stringListMap;
	}

	/**
	 * 校验发货人与接单人是否一致
	 * @param carId 车辆id
	 * @param userId 发货人id
	 * @return Boolean
	 */
	public Boolean checkCar(UserCarBean bean, TransportMain main) {

//		if(main.getExcellentGoods() == 2 && !"1".equals(bean.getAuth())){
//			return false;
//		}
		String name = tytCarMapper.getByCarId(bean.getId());
		if (org.apache.commons.lang.StringUtils.isEmpty(name)) {
			return true;
		}
		TytUserIdentityAuth auth = identityAuthService.getTytUserIdentityAuth(main.getUserId());
		//如果车头所有人与货源发布人昵称不为空，判断是否相同
		if (null != auth && org.apache.commons.lang.StringUtils.isNotEmpty(auth.getTrueName())) {
			if (name.equals(auth.getTrueName())) {
				return false;
			}
		}
		TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(main.getUserId());
		//判断发布人发货企业是否为空，不为空判断是否相同
		if (null != tytInvoiceEnterprise && org.apache.commons.lang.StringUtils.isNotEmpty(tytInvoiceEnterprise.getEnterpriseName())) {
			if (name.equals(tytInvoiceEnterprise.getEnterpriseName())) {
				return false;
			}
		}

		return true;
	}


	/**
	 * 校验三方状态
	 * @param bean 车辆信息
	 * @param log 后台配置的三方状态
	 * @return Boolean
	 */
	public Boolean checkUserCar(UserCarBean bean, TytInvoiceTransportEnterpriseConfigLog log){
		if(null == log){
			return true;
		}
		if(1 == log.getCarRequireAuth()){

			if(Objects.equals(3, bean.getIsInvoice()) && "1".equals(bean.getAuth()) ){
				return true;
			}
		}
		//证件待审
		if(1 == log.getCarRequireWaitAuth() && Objects.equals(4, bean.getIsInvoice())){
			return true;
		}
		//符合要求
		if(1 == log.getCarRequireConform() && Objects.equals(1, bean.getIsInvoice())){
			return true;
		}
		//不符合要求
		if(1 == log.getCarRequireNoConform()  && Objects.equals(2, bean.getIsInvoice())){
			return true;
		}
		return false;
	}

	public TytInvoiceTransportEnterpriseConfigLog getLastTytInvoiceTransportEnterpriseConfig(Long goodsUserId, String serviceProviderCode) {
		TytInvoiceTransportEnterpriseConfigLog tytInvoiceTransportEnterpriseConfigLog = new TytInvoiceTransportEnterpriseConfigLog();
		//翔和翎,走全局配置
        if(InvoiceServiceProviderEnum.XHL.getCode().equals(serviceProviderCode)){
			TytInvoiceTransportConfigLog tytInvoiceTransportConfigLogCacheData = tytInvoiceTransportConfigLogMapper.getLastInvoiceConfigByServiceType(InvoiceServiceTypeEnum.XHL.getCode());
			org.springframework.beans.BeanUtils.copyProperties(tytInvoiceTransportConfigLogCacheData, tytInvoiceTransportEnterpriseConfigLog);
			return tytInvoiceTransportEnterpriseConfigLog;
		}
		Long enterpriseId = tytInvoiceEnterpriseMapper.getByTransportUserId(goodsUserId);
		if (enterpriseId == null) {
			enterpriseId = -1L;
		}
		Integer count = tytInvoiceTransportConfigLogMapper.isHaveLastTytInvoiceTransportEnterpriseConfig(enterpriseId);
		if (count == null || count == 0) {
			TytInvoiceTransportConfigLog lastTytInvoiceTransportConfig = getLastTytInvoiceTransportConfig();
			org.springframework.beans.BeanUtils.copyProperties(lastTytInvoiceTransportConfig, tytInvoiceTransportEnterpriseConfigLog);
			return tytInvoiceTransportEnterpriseConfigLog;
		}

		//该企业存在自己配置
		TytInvoiceTransportEnterpriseConfigLog tytInvoiceTransportEnterpriseConfigLogCacheData = tytInvoiceTransportConfigLogMapper.getLastTytInvoiceTransportEnterpriseConfig(enterpriseId);
		tytInvoiceTransportEnterpriseConfigLog = new TytInvoiceTransportEnterpriseConfigLog();
		org.springframework.beans.BeanUtils.copyProperties(tytInvoiceTransportEnterpriseConfigLogCacheData, tytInvoiceTransportEnterpriseConfigLog);

		return tytInvoiceTransportEnterpriseConfigLog;
	}


	public TytInvoiceTransportConfigLog getLastTytInvoiceTransportConfig() {
		TytInvoiceTransportConfigLog tytInvoiceTransportConfigLog = new TytInvoiceTransportConfigLog();
		TytInvoiceTransportConfigLog tytInvoiceTransportConfigLogCacheData = tytInvoiceTransportConfigLogMapper.getLastTytInvoiceTransportConfig();
		if (tytInvoiceTransportConfigLogCacheData != null) {
			org.springframework.beans.BeanUtils.copyProperties(tytInvoiceTransportConfigLogCacheData, tytInvoiceTransportConfigLog);
		} else {
			tytInvoiceTransportConfigLog = new TytInvoiceTransportConfigLog();
		}
		return tytInvoiceTransportConfigLog;
	}


	@Override
	public List<UserCarBean> getUserCar(Long userId) {
		String sql = "SELECT id ,head_city AS headCity,head_no AS headNo,tail_city AS tailCity,tail_no as tailNo,auth,user_id as userId,is_invoice as isInvoice," +
				"third_party_require as thirdPartyRequire,xhl_party_require as xhlPartyRequire " +
				"from tyt_car where user_id = ? " +
				"and is_delete = 1 ORDER BY (case when auth=1 then 1 else 2 end), id desc limit 100";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("headCity", Hibernate.STRING);
		scalarMap.put("headNo", Hibernate.STRING);

		scalarMap.put("tailCity", Hibernate.STRING);
		scalarMap.put("tailNo", Hibernate.STRING);
		scalarMap.put("auth", Hibernate.STRING);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("isInvoice", Hibernate.INTEGER);
		scalarMap.put("thirdPartyRequire", Hibernate.INTEGER);
		scalarMap.put("xhlPartyRequire", Hibernate.INTEGER);
		List<UserCarBean> search = this.getBaseDao().search(sql, scalarMap, UserCarBean.class, new Object[]{userId});
		if (CollectionUtils.isNotEmpty(search)) {
			return search;
		}
		return null;
	}


	/**
	 * 重量转换
	 * @param weight 重量
	 * @return Integer
	 */
	public BigDecimal convertWeight(String weight){
		BigDecimal maxWeight = (StringUtils.isNotEmpty(weight)) ? new BigDecimal(weight) : new BigDecimal(BigInteger.ZERO);
		return maxWeight;
	}


	/**
	 * 车辆数据判断
	 * @param userCars 车辆
	 * @param tailMap 车头信息

	 * @param headMap 挂车信息
	 * @param weight 货物重量
	 * @return Map<String,List<UserCarBean>>
	 */
	public Map<String, List<UserCarBean>> convertCarList(List<UserCarBean> userCars, Map<Long, CarDetailTail> tailMap, Map<Long, CarDetailHead> headMap, BigDecimal weight) {
		//符合开票要求的
		List<UserCarBean> accordCars = new ArrayList<>();

		//不符合开票要求的
		List<UserCarBean> notAccordCars = new ArrayList<>();

		//不满足载重要求的
		List<UserCarBean> noCarryCars = new ArrayList<>();
		Map<String, List<UserCarBean>> map = new HashMap<>();
		for (UserCarBean bean : userCars) {
			CarDetailTail carDetailTail = null;
			if (null != tailMap) {
				carDetailTail = tailMap.get(bean.getId());
			}
			CarDetailHead carDetailHead = null;
			if (null != headMap) {
				carDetailHead = headMap.get(bean.getId());
			}

			//车头/挂车/轴荷为空---按不满足开票要求处理
			if (null == carDetailTail || null == carDetailHead || null == carDetailTail.getMaximumAxleLoad() || null == carDetailHead.getMaximumAxleLoad()) {
				notAccordCars.add(bean);
			} else if (null != carDetailTail && null != carDetailHead && null != carDetailTail.getMaximumAxleLoad() && null != carDetailHead.getMaximumAxleLoad()) {
				//车头+车挂最大轴荷
				BigDecimal max = new BigDecimal(carDetailTail.getMaximumAxleLoad() + carDetailHead.getMaximumAxleLoad());
				//车头整备质量
				BigDecimal tailWeight = convertWeight(carDetailTail.getCurbWeight());
				//挂车整备质量
				BigDecimal headWeight = convertWeight(carDetailHead.getCurbWeight());

				//总重量
				BigDecimal goodsWeight = weight.add(tailWeight).add(headWeight);
				//满足开票要求的车
				if (1 == bean.getIsInvoice()) {
					if (max.compareTo(goodsWeight) != -1) {
						//满足重量要求
						accordCars.add(bean);
					} else {
						//不满足载重
						noCarryCars.add(bean);
					}
				} else if (2 == bean.getIsInvoice()) {
					//不是开票车票，但是满足重量为不满足开票车辆
					if (max.compareTo(goodsWeight) != -1) {
						//满足重量要求
						notAccordCars.add(bean);
					} else {
						//不满足载重
						noCarryCars.add(bean);
					}
				}else{
					notAccordCars.add(bean);
				}
			}
		}
		map.put("accordCars", accordCars);
		map.put("notAccordCars", notAccordCars);
		map.put("noCarryCars", noCarryCars);
		return map;
	}


	/**
	 * 查询符合车辆车型的司机
	 * @param id 车辆id
	 * @param userId 用户id
	 * @param type 选择类型 1：直接选择  2：单独选择
	 * @return Map<String,List<TytInvoiceDriver>>
	 */
	public Map<String, List<TytInvoiceDriver>> getCarUserList(Long id, Long userId, Integer type, TransportMain main) {
		Map<String, List<TytInvoiceDriver>> invoiceMap = new HashMap<>();
		//满足条件的司机
		List<TytInvoiceDriver> accordUserS = new ArrayList<>();
		//不满足条件的司机
		List<TytInvoiceDriver> nAccordUserS = new ArrayList<>();

		if(null == id){
			List<TytInvoiceDriver> drivers = tytInvoiceDriverMapper.getByCarUserId(userId);
			if(null != drivers){
				invoiceMap = getUserInvoiceDriver(drivers, userId, accordUserS, nAccordUserS, main);
			}
		}else{
			Car car = this.getById(id);
			if (null == car) {
				return invoiceMap;
			}
			//驾照类型
			String carType = getCarLinces(car.getId());


			if (1 == type) {
				//主司机用户id
				Long driverUserId = car.getDriverUserId();

				//如果是本人
				if (null != driverUserId && userId.equals(driverUserId)) {
					List<TytInvoiceDriver> drivers = tytInvoiceDriverMapper.getByUserIdInvoice(userId);
					if (CollectionUtils.isNotEmpty(drivers)) {
						invoiceMap = returnDriver(drivers, carType, userId, accordUserS, nAccordUserS, main);
					}
				}
                /*else if(StringUtils.isNotEmpty(car.getDriverPhone())){
					//司机不是本人
					String driverPhone = car.getDriverPhone();
					if (StringUtils.isNotBlank(driverPhone)) {
						List<TytInvoiceDriver> drivers = tytInvoiceDriverMapper.getDriverPhone(driverPhone,car.getUserId());
						if (CollectionUtils.isNotEmpty(drivers)) {
							invoiceMap = returnDriver(drivers, carType, car.getDriverUserId(), accordUserS);
						}
					}

				}*/
			} else {
				//查询用户的车辆信息
				List<TytInvoiceDriver> drivers = tytInvoiceDriverMapper.getByCarUserId(userId);
				if (CollectionUtils.isNotEmpty(drivers)) {
					invoiceMap = getInvoiceDriver(drivers, carType, userId, accordUserS, nAccordUserS, main);
				}
			}
		}

		return invoiceMap;
	}


	/**
	 * 查询车辆车型对应驾照
	 * @param id 车辆id
	 * @return String
	 */
	public String getCarLinces(Long id) {
		String linces = "";
		//根据挂车信息查询车辆类型
		CarDetailHead head = carDetailHeadService.getByCarId(id);

		if (null == head) {
			return linces;
		}
		if (StringUtils.isEmpty(head.getCarType())) {
			return linces;
		}
		TytCarType tytCarType = tytCarTypeMapper.selectByPrimaryKey(head.getCarType());
		if (null == tytCarType) {
			return linces;
		}
		if (StringUtils.isEmpty(tytCarType.getCarType())) {
			return linces;
		}

		linces = tytCarTypeLincenseMapper.getByName(tytCarType.getCarType());
		return linces;
	}

	@Override
	public boolean checkSigningDriver(TransportMain main, Long userId, TytInvoiceDriver driver){
		if (main.getExcellentGoods() != 2){
			return true;
		}
		TytSpecialCarDispatchFailure tytSpecialCarDispatchFailure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(main.getId());
		if (tytSpecialCarDispatchFailure != null && tytSpecialCarDispatchFailure.getDeclareInPublic() != null && tytSpecialCarDispatchFailure.getDeclareInPublic() == 1) {
			return true;
		}
		TytSigningCar tytSigningCar = tytSigningCarMapper.getByUserId(userId);
		if(null == tytSigningCar){
			return false;
		}
		Integer blackNum = tytSigningDriverBlackMapper.getByDriverUserId(driver.getDriverUserId());
		if (0 < blackNum){
			return false;
		}
		Integer num = tytSigningCarInfoMapper.getByIdName(tytSigningCar.getId(),driver.getPhone());
		if (0 >= num){
			return false;
		}
		Long cargoOwnerId = main.getCargoOwnerId();
		if (!Objects.isNull(cargoOwnerId) && cargoOwnerId != 0){
			TytDispatchCooperative cooperative = tytDispatchCooperativeMapper.selectByPrimaryKey(cargoOwnerId);
			if (Objects.isNull(cooperative)){
				return false;
			}
			TytDispatchCargoOwner owner = dispatchOwnerService.getByCooperativeId(cooperative.getId());
			String[] signing = tytSigningCar.getSigning().split(",");
			if (Objects.isNull(owner) || (owner.getAssignCar() != 0 && !Arrays.asList(signing).contains(cooperative.getCooperativeName()))){
				return false;
			}
		}
		return true;
	}

	/**
	 * 查询车辆自己的主司机
	 * @param drivers 主司机的车辆
	 * @param carType 车型

	 * @param userId 用户id
	 * @param accordUserS 符合的司机
	 * @return Map<String,List<TytInvoiceDriver>>
	 */
	public Map<String, List<TytInvoiceDriver>> returnDriver(List<TytInvoiceDriver> drivers, String carType, Long userId, List<TytInvoiceDriver> accordUserS, List<TytInvoiceDriver> nAccordUserS, TransportMain main) {
		Map<String, List<TytInvoiceDriver>> map = new HashMap<>();
		for (TytInvoiceDriver driver : drivers) {
			String phone = getPhone(driver.getPhone());
			boolean checkSignDriver = checkSigningDriver(main, userId,driver);
			if (checkSignDriver){
				//有匹配的车型,按车型对应的驾驶证型号匹配
				if (StringUtils.isNotBlank(carType)) {
					if (StringUtils.isNotEmpty(driver.getDlCardVehicleType()) && invoiceType(carType,driver.getDlCardVehicleType())) {
						driver.setUserId(userId);
						driver.setPhone(phone);
						accordUserS.add(driver);
					}
				} else {
					//否则不需要匹配
					driver.setUserId(userId);
					driver.setPhone(phone);
					accordUserS.add(driver);
				}
			}else {
				driver.setUserId(userId);
				driver.setPhone(phone);
				nAccordUserS.add(driver);
			}
		}
		map.put("accordUserS", accordUserS);
		map.put("nAccordUserS", nAccordUserS);
		return map;
	}

	/**
	 * 全部承运车辆
	 * @param drivers 用户司机列表
	 * @param carType 车型

	 * @param userId 用户id

	 * @param accordUserS 符合的司机
	 * @param nAccordUserS 不符合的司机
	 * @return Map<String,List<TytInvoiceDriver>>
	 */
	public Map<String, List<TytInvoiceDriver>> getInvoiceDriver(List<TytInvoiceDriver> drivers, String carType, Long userId,
			List<TytInvoiceDriver> accordUserS, List<TytInvoiceDriver> nAccordUserS, TransportMain main) {
		Map<String, List<TytInvoiceDriver>> map = new HashMap<>();
		for (TytInvoiceDriver driver : drivers) {
			String phone = getPhone(driver.getPhone());
			boolean checkSignDriver = checkSigningDriver(main, userId,driver);
			boolean isCarTypeMatch = StringUtils.isNotBlank(carType) && invoiceType(carType, driver.getDlCardVehicleType());

			//认证通过而且车型匹配
			if ((1 == driver.getVerifyStatus() || 0 == driver.getVerifyStatus()) && (isCarTypeMatch || StringUtils.isBlank(carType)) && checkSignDriver) {
				driver.setUserId(userId);
				driver.setPhone(phone);
				accordUserS.add(driver);
			} else {
				driver.setUserId(userId);
				driver.setPhone(phone);
				nAccordUserS.add(driver);
			}
		}
		map.put("accordUserS", accordUserS);
		map.put("nAccordUserS", nAccordUserS);
		return map;
	}

	/**
	 * 手机号加密
	 * @param phone 手机号
	 * @return String
	 */
	public String getPhone(String phone){
		String cellPhone = "";
		if(StringUtils.isNotEmpty(phone)){
			cellPhone = phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
		}
		return cellPhone;
	}

	public Map<String, List<TytInvoiceDriver>> getUserInvoiceDriver(List<TytInvoiceDriver> drivers,  Long userId,
			List<TytInvoiceDriver> accordUserS, List<TytInvoiceDriver> nAccordUserS, TransportMain main) {
		Map<String, List<TytInvoiceDriver>> map = new HashMap<>();
		for (TytInvoiceDriver driver : drivers) {
			String phone = getPhone(driver.getPhone());
			boolean checkSignDrive = checkSigningDriver(main, userId,driver);
			//认证通过而且车型匹配
			if ((1 == driver.getVerifyStatus() || 0 == driver.getVerifyStatus()) && checkSignDrive){
				driver.setUserId(userId);
				driver.setPhone(phone);
				accordUserS.add(driver);
			} else {
				driver.setUserId(userId);
				driver.setPhone(phone);
				nAccordUserS.add(driver);
			}
		}
		map.put("accordUserS", accordUserS);
		map.put("nAccordUserS", nAccordUserS);
		return map;
	}


	/**
	 * 车型匹配
	 * @param codes 车辆code
	 * @param carType 司机code
	 * @return Boolean
	 */
	public Boolean invoiceType(String codes, String carType) {
		if (StringUtils.isBlank(codes)) {
			return true;
		} else if (StringUtils.isNotEmpty(codes) && StringUtils.isNotEmpty(carType)) {
			if (codes.contains(",")) {
				List<String> strings = Arrays.asList(codes.split(","));
				for (String code : strings) {
					if (carType.contains(code)) {
						return true;
					}
				}
			} else {
				return carType.contains(codes);
			}
		}
		return false;
	}


	@Override
	public Boolean getCarinvoiceType(Long carId, Long invoiceId){
		//车辆类型
		String carTypeInfo = getCarLinces(carId);
		if(StringUtils.isEmpty(carTypeInfo)){
			return true;
		}

		TytInvoiceDriver driver = tytInvoiceDriverMapper.selectByPrimaryKey(invoiceId);
		if(null == driver){
			return false;
		}
		if(StringUtils.isEmpty(driver.getDlCardVehicleType())){
			return false;
		}

		if (carTypeInfo.contains(",")) {
			List<String> strings = Arrays.asList(carTypeInfo.split(","));
			for (String code : strings) {
				if (driver.getDlCardVehicleType().contains(code)) {
					return true;
				}
			}
		} else {
			return driver.getDlCardVehicleType().contains(carTypeInfo);
		}
		return false;

	}


	/**
	 * 三方货源校验车辆
	 * @param code 车辆id
	 * @param invoiceCode 司机id
	 * @param userId 发货用户
	 * @return Boolean
	 */
	@Override
	public Boolean getSubjectCarinvoiceType(Long code, Long invoiceCode, Long userId){
		//先判断司机准驾车型是否匹配，否的话直接返回
		Boolean carinvoiceType = getCarinvoiceType(code, invoiceCode);
		if(!carinvoiceType){
			return false;
		}
		//通过的话，再校验车辆三方要求是否匹配
		TytInvoiceTransportEnterpriseConfigLog log = getLastTytInvoiceTransportEnterpriseConfig(userId,"");
		UserCarBean bean = getByCarId(code);
		if(null == bean){
			return false;
		}
		Boolean aBoolean = checkUserCar(bean, log);
		return aBoolean;
	}


	public UserCarBean getByCarId(Long id) {
		String sql = "SELECT id ,head_city AS headCity,head_no AS headNo,tail_city AS tailCity,tail_no as tailNo,auth,user_id as userId,third_party_require as isInvoice from tyt_car where id = ?";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("headCity", Hibernate.STRING);
		scalarMap.put("headNo", Hibernate.STRING);

		scalarMap.put("tailCity", Hibernate.STRING);
		scalarMap.put("tailNo", Hibernate.STRING);
		scalarMap.put("auth", Hibernate.STRING);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("isInvoice", Hibernate.INTEGER);
		List<UserCarBean> search = this.getBaseDao().search(sql, scalarMap, UserCarBean.class, new Object[]{id});
		if (CollectionUtils.isNotEmpty(search)) {
			return search.get(0);
		}
		return null;
	}

	/**
	 * 查询用户名下是否有认证中/认证成功的车辆
	 * @param userId 用户id
	 * @return Integer
	 */
	@Override
	public Integer getByUserIdAuth(Long userId){
		String sql = "SELECT count(*) FROM tyt_car " +
				" WHERE" +
				" is_delete = 1 and (auth = 1 or auth = 0) AND user_id = ? " +
				" ORDER BY create_time DESC limit 1";
		BigInteger c = this.getBaseDao().query(sql, new Object[] { userId });
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}

	@Override
	public ResultMsgBean getCheckCar(Car car, User user){
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
		String headName = car.getHeadCity()+car.getHeadNo();
		//查询是否有符合的配置，没有的话直接跳过
		TytBlockConfig config = tytBlockConfigService.getByUserConfig(user);
		if (null == config) {
			return resultMsgBean;
		}
		Date today = TimeUtil.today();
		Date date = new Date();
		//查询当天的单量

		int count = transportOrdersMapper.getPayUserIdCarIdCTime(user.getId(),car.getId(),today,date);
		logger.info("当天单量，userId【{}】，车辆id【{}】，开始时间【{}】，当前时间【{}】,count[{}]",user.getId(),car.getId(),config.getOpenTime(),date,count);
		String masterParams = headName + "," + count;


		//第一步当天的接单数大于等于单车阻断数
		if(null != config.getBlock() && count >= config.getBlock()){
			resultMsgBean = getPopupTempl(PopupTypeEnum.BLOCK, masterParams, 50006, resultMsgBean);
			if (resultMsgBean.getCode() != ResultMsgBean.OK) {
				return resultMsgBean;
			}
		}

		//第二步认证失败的判断历史数据单量
		if(BlockCarStatusEnum.CONFIG_TYPE_TWO.getStatus().equals(car.getAuth())){
			//认证失败

			count = transportOrdersMapper.getPayUserIdCarIdCTime(user.getId(),car.getId(),config.getOpenTime(),date);
			logger.info("认证失败，userId【{}】，车辆id【{}】，开始时间【{}】，当前时间【{}】,count【{}】",user.getId(),car.getId(),config.getOpenTime(),date,count);
			masterParams = headName + "," + count;
			//认证阻断不为空而且历史单量大于认证阻断次数
			if(null != config.getReceivingOrders() && count >= config.getReceivingOrders()){
				resultMsgBean = getPopupTempl(PopupTypeEnum.BLOCK_AUTH, masterParams, 50007, resultMsgBean);
				if (resultMsgBean.getCode() != ResultMsgBean.OK) {
					return resultMsgBean;
				}
			}
		}

		//第三步认证成功或认证失败的走判断提醒阈值不为空而且当天单量大于都等于阈值，返回提醒文案
		if(null != config.getRemind() && count >= config.getRemind()){
			resultMsgBean = getPopupTempl(PopupTypeEnum.BLOCK_REMIND, masterParams, 50005, resultMsgBean);
			if (resultMsgBean.getCode() != ResultMsgBean.OK) {
				return resultMsgBean;
			}
		}

		return resultMsgBean;
	}

	@Override
	public void updateCarForTransportOcr(Long carId, RoadTransportBackOcrRpcVo ocrRpcVo, Integer carType) {
		String sql;
		if (carType == 1) {
			sql = "update tyt_car set head_car_no = ?, head_transport_no = ?, head_business_license_no = ?, head_license_plate_color = ?, " +
					"head_vehicle_type = ?, head_business_scope = ?, head_issue_date = ?, update_time = ? where id = ?";
		} else {
			sql = "update tyt_car set tail_car_no = ?, tail_transport_no = ?, tail_business_license_no = ?, head_license_plate_color = ?, " +
					"tail_vehicle_type = ?, tail_business_scope = ?, tail_issue_date = ?, update_time = ? where id = ?";
		}
		Object[] params = {
				ocrRpcVo.getPlateNum(),
				ocrRpcVo.getRoadTransportCard(),
				ocrRpcVo.getBusinessLicenseNo(),
				"黄色",
				ocrRpcVo.getVehicleType(),
				ocrRpcVo.getBusinessScope(),
				ocrRpcVo.getIssueTime(),
				new Date(),
				carId
		};
		// 执行数据库更新
		this.executeUpdateSql(sql, params);
	}


	public ResultMsgBean getPopupTempl(PopupTypeEnum popup,String masterParams,Integer type,ResultMsgBean resultMsgBean){
		TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(popup, masterParams);
		if(null != noticePopupTempl){
			resultMsgBean.setCode(type);
			resultMsgBean.setMsg(noticePopupTempl.getMasterContent());
		}
		return resultMsgBean;
	}
}
