package com.tyt.user.service.impl;

import com.gexin.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.TytConfig;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.dao.TytConfigDao;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Service("tytConfigService")
public class TytConfigServiceImpl extends BaseServiceImpl<TytConfig, Long> implements TytConfigService {

	private final static Logger logger = LoggerFactory.getLogger(TytConfigServiceImpl.class);

	@Resource(name = "tytConfigDao")
	public void setBaseDao(BaseDao<TytConfig, Long> tytConfigDao) {
		super.setBaseDao(tytConfigDao);
	}

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	public static final String CACHE_TYT_CONFIG_MAP_FLAG_KEY = "TYT_CONFIG_MAP_FLAG";

	private static volatile String CURRENT_CACHE_TYT_CONFIG_MAP_FLAG_KEY = "NO_FLAG";
	/**
	 * 本地缓存
	 */
	public static Map<String, TytConfig> cacheMap = new HashMap<>();
	{
		this.cacheCommend();
	}

	/**
	 * 更新本地缓存数据，tyt_config表
	 */
	private void cacheCommend(){

		ScheduledThreadPoolExecutor exec = new ScheduledThreadPoolExecutor(1);
		/**
		 * 创建并执行一个在给定初始延迟后首次启用的定期操作，后续操作具有给定的周期；<br/>
		 * 也就是将在 initialDelay 后开始执行，然后在initialDelay+period 后执行，<br/>
		 * 接着在 initialDelay + 2 * period 后执行，依此类推。
		 * 暂定10秒执行一次
		 */
		exec.scheduleAtFixedRate(new Runnable() {
			public void run() {
				try {
					String redisCurrentKey = Optional.ofNullable(RedisUtil.get(CACHE_TYT_CONFIG_MAP_FLAG_KEY)).orElse("");
					if(CURRENT_CACHE_TYT_CONFIG_MAP_FLAG_KEY.equals(redisCurrentKey)){
						return;
					}
					logger.info("config 缓存更新 当前 flag :【{}】 , 更新后 flag :【{}】", CURRENT_CACHE_TYT_CONFIG_MAP_FLAG_KEY, redisCurrentKey);
					List<TytConfig> list = null;
					// 如果membercache缓存不存在，则数据库查询，同时存入本地缓存和membercache缓存
					Object object = cacheService.getObject(Constant.CACHE_TYT_CONFIG_MAP_KEY);
					try {
						System.out.println("数据库config111长度：" + JSON.toJSONString(object).getBytes().length);
					} catch (Exception e) {
					}
					if (object == null) {
						list = getList(null, null);
						if(CollectionUtils.isNotEmpty(list)) {
							// 存入本地缓存
							for (TytConfig config : list) {
								cacheMap.put(config.getName(), config);
							}
							// 存入网络缓存bnm,
							cacheService.setObject(Constant.CACHE_TYT_CONFIG_MAP_KEY, cacheMap, Constant.CACHE_EXPIRE_TIME_24H);
						} else {
							// 清除本地缓存
							cacheMap.clear();
						}
					} else {
					    // 存入本地缓存
                        cacheMap = (Map<String, TytConfig>) object;
					}
					CURRENT_CACHE_TYT_CONFIG_MAP_FLAG_KEY = redisCurrentKey;
				} catch (Exception e) {
				}
			}   //暂定10秒请求一次数据
		}, 10, TimeUnit.SECONDS.toMillis(10), TimeUnit.MILLISECONDS);
	}

    @Override
	public Map<String, TytConfig> getConfigMap() {
		// 之前版本，由网络获取缓存
//		Map<String, TytConfig> configMap = new HashMap<String, TytConfig>();
//		Object object = cacheService.getObject(Constant.CACHE_TYT_CONFIG_MAP_KEY);
//		if (object == null) {
//			List<TytConfig> list = this.getList(null, null);
//			for (TytConfig config : list) {
//				configMap.put(config.getName(), config);
//			}
//			cacheService.setObject(Constant.CACHE_TYT_CONFIG_MAP_KEY, configMap, Constant.CACHE_EXPIRE_TIME_24H);
//		} else {
//			configMap = (Map<String, TytConfig>) object;
//		}

		// 由本地获取缓存
		Map<String, TytConfig> configMap = cacheMap;
		return configMap;
	}

	@Override
	public TytConfig getValue(String key) {
		return getConfigMap().get(key);
	}

	@Override
	public Integer getIntValue(String key) {
		TytConfig config = getConfigMap().get(key);
		if (config == null)
			return null;
		if (!StringUtil.isNumeric(config.getValue()))
			return null;
		return Integer.parseInt(config.getValue());
	}

	@Override
	public Integer getIntValue(String key, int defaultValue) {
		TytConfig config=getConfigMap().get(key);
		if(config==null)return defaultValue;
		if(!StringUtil.isNumeric(config.getValue()))return defaultValue;
		return Integer.parseInt(config.getValue());
	}

	@Override
	public boolean isEqualsValue (String key,int compareValue) {
		if(StringUtils.isBlank(key)){
			return false;
		}
		TytConfig config=getConfigMap().get(key);
		if(config==null){
			return false;
		}
		if(!StringUtil.isNumeric(config.getValue())){
			return false;
		}
		return Integer.parseInt(config.getValue())==compareValue;
	}

	@Override
	public boolean isEqualsOne(String key) {

		return isEqualsValue(key,1);
	}

	@Override
	public Integer fetchRecommendIntValue(String key) {
		TytConfig config = getConfigMap().get(key);
		if (config == null)
			return null;
		if (!StringUtil.isNumeric(config.getValue()))
			return null;
		return Integer.parseInt(config.getValue());
	}
	
	@Override
	public Integer fetchRecommendIntValue(String key, int defaultValue) {
		TytConfig config = getConfigMap().get(key);
		if (config == null)
			return defaultValue;
		if (!StringUtil.isNumeric(config.getValue()))
			return defaultValue;
		return Integer.parseInt(config.getValue());
	}


	@Override
	public String getStringValue(String key) {
		TytConfig config = getConfigMap().get(key);
		if (config == null)
			return null;
		return config.getValue();
	}

	@Override
	public String getStringValue(String key, String defaultValue) {
		TytConfig config = getConfigMap().get(key);
		if (config == null)
			return defaultValue;
		return config.getValue();
	}
	
	@Override
	public String fetchRecommendStringValue(String key) {
		TytConfig config = getConfigMap().get(key);
		if (config == null)
			return null;
		return config.getValue();
	}
	
	@Override
	public String fetchRecommStrValue(String key, String defaultValue) {
		TytConfig config = getConfigMap().get(key);
		if (config == null)
			return defaultValue;
		return config.getValue();
	}

	@Override
	public void update(String counts) {
		// TODO Auto-generated method stub
		boolean flag = false;
		while (!flag) {
			flag = cacheService.del(Constant.CACHE_TYT_CONFIG_MAP_KEY);
		}
		((TytConfigDao) (this.getBaseDao())).update(counts);
	}

}
