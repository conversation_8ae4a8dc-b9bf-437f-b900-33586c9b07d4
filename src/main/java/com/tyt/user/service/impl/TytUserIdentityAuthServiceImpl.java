package com.tyt.user.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.cargo.dataqueue.client.topic.ts.UserBusinessTopic;
import com.tyt.cargo.dataqueue.client.vo.user.UserIdVo;
import com.tyt.config.util.AppConfig;
import com.tyt.messagecenter.core.utils.ConvertUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.User;
import com.tyt.plat.client.user.ApiUserInvoiceClient;
import com.tyt.plat.client.user.dto.FaceAuthUploadImageRpcDTO;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.constant.TytEnterpriseConstant;
import com.tyt.plat.enterprise.db.EnterpriseDBService;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.entity.base.TytInvoiceTaxRateRule;
import com.tyt.plat.entity.base.TytSelfCompanySign;
import com.tyt.plat.enums.*;
import com.tyt.plat.enums.enterprise.EnterpriseSourceTypeEnum;
import com.tyt.plat.mapper.base.TytInvoiceTaxRateRuleMapper;
import com.tyt.plat.mapper.base.TytSelfCompanySignMapper;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.producer.BusinessMqProducerService;
import com.tyt.plat.service.user.ApiInvoiceEnterpriseService;
import com.tyt.plat.service.user.UserVerifyService;
import com.tyt.plat.vo.face.FaceAuthUploadImageRpcVO;
import com.tyt.plat.vo.face.FaceV3VerifyResp;
import com.tyt.plat.vo.face.FaceV5VerifyResp;
import com.tyt.plat.vo.invoice.ThirdEnterpriseUserCodeVo;
import com.tyt.plat.vo.ocr.OcrBusinessLicenseVo;
import com.tyt.plat.vo.user.EnterpriseModifyCheckVo;
import com.tyt.plat.vo.user.FaceUserIdentityAuthReq;
import com.tyt.plat.vo.user.UserFaceVerifyVo;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.bean.UserBaseInfo;
import com.tyt.user.bean.UserIdentityAuthBean;
import com.tyt.user.dao.TytUserIdentityAuthDao;
import com.tyt.user.service.*;
import com.tyt.util.*;
import kafka.utils.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Slf4j
@Service("tytUserIdentityAuthService")
public class TytUserIdentityAuthServiceImpl extends BaseServiceImpl<TytUserIdentityAuth, Long> implements TytUserIdentityAuthService {

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "tytUserIdentityAuthLogService")
    TytUserIdentityAuthLogService userIdentityAuthLogService;

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "userIdentityMainService")
    private UserIdentityMainService userIdentityMainService;

    @Autowired
    private EnterpriseDBService enterpriseDBService;

    @Autowired
    private InternalUserService internalUserService;

    @Autowired
    private CommonApiService commonApiService;

    @Autowired
    private UserVerifyService userVerifyService;

    @Autowired
    private BusinessMqProducerService businessMqProducerService;

    @Autowired
    private ApiInvoiceEnterpriseService apiInvoiceEnterpriseService;

    @Autowired
    private TytSelfCompanySignMapper tytSelfCompanySignMapper;

    @Autowired
    private TytInvoiceTaxRateRuleMapper tytInvoiceTaxRateRuleMapper;

    @Autowired
    private ApiUserInvoiceClient apiUserInvoiceClient;

    @Resource(name = "tytUserIdentityAuthDao")
    @Override
    public void setBaseDao(BaseDao<TytUserIdentityAuth, Long> tytUserIdentityAuthDao) {
        super.setBaseDao(tytUserIdentityAuthDao);
    }

    @Override
    public TytUserIdentityAuth createTytUserIdentityAuth(Long userId, UserIdentityAuthBean userIdentityAuthBean,
                                                         MultipartFile mainUrlPic, MultipartFile backUrlPic,
                                                         MultipartFile qualificationsUrlPic, MultipartFile licenseUrlPic,
                                                         MultipartFile iPhotoPic, String clientSign) throws Exception {

        TytUserIdentityAuth tytUserIdentityAuth = new TytUserIdentityAuth();
        BeanUtils.copyProperties(userIdentityAuthBean, tytUserIdentityAuth);
        log.info("身份实名认证传入的userId为：" + userId);
        User user = userService.getById(userId);
        tytUserIdentityAuth.setUserId(userId);
        tytUserIdentityAuth.setMobile(user.getCellPhone());
        if (user.getUserClass() != null && user.getIdentityType() != null) {
            tytUserIdentityAuth.setUserClass(user.getUserClass());
            tytUserIdentityAuth.setIdentityType(user.getIdentityType());
        } else {
            int clientPort = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
            if (clientPort == 1) {
                tytUserIdentityAuth.setUserClass(2);
                tytUserIdentityAuth.setIdentityType(6);
            } else if (clientPort == 2) {
                tytUserIdentityAuth.setUserClass(1);
                tytUserIdentityAuth.setIdentityType(3);
            } else if (clientPort == 4) {
                tytUserIdentityAuth.setUserClass(4);
                tytUserIdentityAuth.setIdentityType(11);
            }
        }
        if (mainUrlPic != null && !mainUrlPic.isEmpty()) {
            tytUserIdentityAuth.setMainUrl(CreateFileUtil.renamePic(mainUrlPic, "user"));
            mainUrlPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + tytUserIdentityAuth.getMainUrl()));
        } else {
            tytUserIdentityAuth.setMainUrl(null);
        }
        if (backUrlPic != null && !backUrlPic.isEmpty()) {
            tytUserIdentityAuth.setBackUrl(CreateFileUtil.renamePic(backUrlPic, "user"));
            backUrlPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + tytUserIdentityAuth.getBackUrl()));
        } else {
            tytUserIdentityAuth.setBackUrl(null);
        }
        if (qualificationsUrlPic != null && !qualificationsUrlPic.isEmpty()) {
            tytUserIdentityAuth.setQualificationsUrl(CreateFileUtil.renamePic(qualificationsUrlPic, "user"));
            qualificationsUrlPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + tytUserIdentityAuth.getQualificationsUrl()));

        } else {
            tytUserIdentityAuth.setQualificationsUrl(null);
        }
        if (licenseUrlPic != null && !licenseUrlPic.isEmpty()) {
            tytUserIdentityAuth.setLicenseUrl(CreateFileUtil.renamePic(licenseUrlPic, "user"));
            licenseUrlPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + tytUserIdentityAuth.getLicenseUrl()));

        } else {
            tytUserIdentityAuth.setLicenseUrl(null);
        }
        if (iPhotoPic != null && !iPhotoPic.isEmpty()) {
            tytUserIdentityAuth.setiPhotoUrl(CreateFileUtil.renamePic(iPhotoPic, "user"));
            iPhotoPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + tytUserIdentityAuth.getiPhotoUrl()));
            //6270将手持照片改为身份证反面照，为兼容老版本同时将backUrl也保存一份
            tytUserIdentityAuth.setBackUrl(tytUserIdentityAuth.getiPhotoUrl());
        } else {
            tytUserIdentityAuth.setiPhotoUrl(null);
        }
        //企业互换
//		if(user.getIdentityType()!=null && user.getIdentityType().intValue()==2){
//			tytUserIdentityAuth.setiPhotoUrl(tytUserIdentityAuth.getMainUrl());
//			tytUserIdentityAuth.setMainUrl(tytUserIdentityAuth.getBackUrl());
//		}

        tytUserIdentityAuth.setRealVerify(VerifyStatusEnum.UNAUTH.getCode());
        tytUserIdentityAuth.setFaceVerify(VerifyStatusEnum.UNAUTH.getCode());

        return tytUserIdentityAuth;
    }

    /**
     * 校验是否允许修改实名信息.
     * @param userIdentityAuthReq userIdentityAuthReq
     * @param dbUser dbUser
     */
    private void checkUserVerifyCount(TytUserIdentityAuth userIdentityAuthReq, User dbUser){

        Long userId = userIdentityAuthReq.getUserId();
        String idCard = userIdentityAuthReq.getIdCard();
        String trueName = userIdentityAuthReq.getTrueName();

        //校验每日次数
        int verifyDayCount = userVerifyService.getVerifyDayCount(userId, true);

        userVerifyService.checkChangeRealAllow(idCard, trueName, dbUser);

    }

    /**
     * 设置实名认证相关字段.
     * @param tytUserIdentityAuth tytUserIdentityAuth
     */
    private void setRealVerifyInfo(TytUserIdentityAuth tytUserIdentityAuth,
                                   boolean realVerifySuccess, boolean faceVerifySuccess){
        if(realVerifySuccess) {
            tytUserIdentityAuth.setRealVerify(GlobalStatusEnum.yes.getCode());
        }
        if(faceVerifySuccess){
            tytUserIdentityAuth.setFaceVerify(GlobalStatusEnum.yes.getCode());
        }

    }

    @Override
    public ResultMsgBean save(TytUserIdentityAuth tytUserIdentityAuth, String clientSign, boolean faceVerifyResult) {

        Long userId = tytUserIdentityAuth.getUserId();

        String idCard = tytUserIdentityAuth.getIdCard();
        String trueName = tytUserIdentityAuth.getTrueName();

        ResultMsgBean resultMsgBean = new ResultMsgBean();
        User dbUser = userService.getById(userId);

        //判断数据库中有没有，有修改，没有保存
        TytUserIdentityAuth tuia = getTytUserIdentityAuth(tytUserIdentityAuth.getUserId());

        if(tuia != null){
            Integer idCardLongTerm = tytUserIdentityAuth.getIdCardLongTerm();

            if(idCardLongTerm == null){
                tytUserIdentityAuth.setIdCardLongTerm(tuia.getIdCardLongTerm());
                tytUserIdentityAuth.setIdCardValidDate(tuia.getIdCardValidDate());
            }
        }

        //验证 参数
        if (!checkTytUserIdentityAuth(dbUser, tuia, resultMsgBean, tytUserIdentityAuth, clientSign)) {
            log.info("---------------提交不成功------------------");
            return resultMsgBean;
        }

        this.checkUserVerifyCount(tytUserIdentityAuth, dbUser);

        //如果未识别人脸，则进行二要素
        if(!faceVerifyResult){
            //二要素
            Integer verifyResult = userVerifyService.realVerify(idCard, trueName);

            if(!GlobalStatusEnum.yes.equalsCode(verifyResult)){
                throw TytException.createException(ResponseEnum.rest_error.info("姓名与身份证信息不匹配"));
            }
        }

        if (tuia == null) {
            //初始化数据，不是这个身份的数据清空
            initTytUserIdentityAuth(tytUserIdentityAuth);
            tytUserIdentityAuth.setId(null);
            tytUserIdentityAuth.setPlatId(clientSign);

            this.setRealVerifyInfo(tytUserIdentityAuth, true, faceVerifyResult);

            this.getBaseDao().insert(tytUserIdentityAuth);
        } else {

            copyProperty(tytUserIdentityAuth, tuia);
            tuia.setPlatId(clientSign);

            this.setRealVerifyInfo(tuia, true, faceVerifyResult);
            log.info("update identity 【{}】", JSON.toJSONString(tuia));
            this.getBaseDao().update(tuia);
        }
        //修改用户表
        dbUser.setIdentityType(tytUserIdentityAuth.getIdentityType());
        dbUser.setUserClass(tytUserIdentityAuth.getUserClass());
        dbUser.setVerifyPhotoSign(2);
        userService.update(dbUser);
        //修改用户缓存
        cacheService.del(Constant.CACHE_USER_KEY + dbUser.getId());
        /* user信息缓存 */
        cacheService.setObject(Constant.CACHE_USER_KEY + dbUser.getId(), dbUser, AppConfig.getIntProperty("tyt.cache.user.time"));

        userIdentityMainService.saveUserIdentityMain(dbUser.getId(), tytUserIdentityAuth.getTrueName(), tytUserIdentityAuth.getIdCard(), tytUserIdentityAuth.getMainUrl());

        resultMsgBean.setCode(ResultMsgBean.OK);
        resultMsgBean.setMsg("提交成功");
        return resultMsgBean;
    }

    @Override
    public UserFaceVerifyVo saveFaceVerifyV5(FaceUserIdentityAuthReq userIdentityAuthReq) throws Exception{

        String clientSign = userIdentityAuthReq.getClientSign();

        Long userId = userIdentityAuthReq.getUserId();
        String idCard = userIdentityAuthReq.getIdCard();
        String trueName = userIdentityAuthReq.getTrueName();

        String faceToken = userIdentityAuthReq.getFaceToken();

        if(StringUtils.isBlank(faceToken)){
            throw TytException.createException(ResponseEnum.rest_error.info());
        }

        UserFaceVerifyVo faceVerifyVo = new UserFaceVerifyVo();
        faceVerifyVo.setVerifyStatus(GlobalStatusEnum.yes.getCode());

        //人脸认证
        FaceV5VerifyResp faceVerifyResp = userVerifyService.faceVerifyV5(idCard, trueName, faceToken);

        //TODO: face check info
        boolean faceVerifyResult = false;
        if(faceVerifyResp != null){
            faceVerifyResult = true;
        }
        if(!faceVerifyResult){
            //人脸认证失败，直接返回
            faceVerifyVo.setVerifyStatus(GlobalStatusEnum.no.getCode());
            return faceVerifyVo;
        }

        MultipartFile mainUrlPic = userIdentityAuthReq.getMainUrlPic();
        MultipartFile iPhotoPic = userIdentityAuthReq.getIPhotoPic();

        UserIdentityAuthBean userIdentityAuthBean = ConvertUtil.beanConvert(userIdentityAuthReq, new UserIdentityAuthBean());

        TytUserIdentityAuth tytUserIdentityAuth = this.createTytUserIdentityAuth(userIdentityAuthReq.getUserId(), userIdentityAuthBean,
                mainUrlPic, null, null, null, iPhotoPic, userIdentityAuthReq.getClientSign());

        //人脸识别活体照片上传
        uploadFaceVerifyImage(tytUserIdentityAuth, ObjectUtil.isNotNull(faceVerifyResp.getImages()) ? faceVerifyResp.getImages().getImageBest() : null);

        ResultMsgBean resultMsgBean = this.save(tytUserIdentityAuth, clientSign, true);

        if(resultMsgBean.isSuccess()){
            //认证成功
            return faceVerifyVo;
        } else {
            ResponseCode responseCode = new ResponseCode(resultMsgBean.getCode(), resultMsgBean.getMsg());
            throw TytException.createException(responseCode);
        }
    }

    private void uploadFaceVerifyImage(TytUserIdentityAuth tytUserIdentityAuth, String imageBest) {
        if (StringUtils.isBlank(imageBest)) {
            return;
        }
        try {
            FaceAuthUploadImageRpcDTO faceAuthUploadImageRpcDTO = new FaceAuthUploadImageRpcDTO();
            faceAuthUploadImageRpcDTO.setUserId(tytUserIdentityAuth.getUserId());
            faceAuthUploadImageRpcDTO.setIdCard(tytUserIdentityAuth.getIdCard());
            faceAuthUploadImageRpcDTO.setImageBest(imageBest);
            Response<FaceAuthUploadImageRpcVO> internalWebResultCall = apiUserInvoiceClient.faceAuthUploadImageForBase64(faceAuthUploadImageRpcDTO).execute();
            log.info("face auth internalWebResultCall {}", JSON.toJSONString(internalWebResultCall));
            if (internalWebResultCall.isSuccessful() && ObjectUtil.isNotNull(internalWebResultCall.body())) {
                FaceAuthUploadImageRpcVO faceAuthUploadImageRpcVO = internalWebResultCall.body();
                if (StringUtils.isNotBlank(faceAuthUploadImageRpcVO.getUrl())) {
                    tytUserIdentityAuth.setFaceVerifyImageUrl(faceAuthUploadImageRpcVO.getUrl());
                }
            }
        } catch (Exception e) {
            log.error("face auth upload error", e);
        }
    }

    @Override
    public UserFaceVerifyVo saveFaceVerifyV3(FaceUserIdentityAuthReq userIdentityAuthReq) throws Exception{

        String clientSign = userIdentityAuthReq.getClientSign();

        Long userId = userIdentityAuthReq.getUserId();
        String idCard = userIdentityAuthReq.getIdCard();
        String trueName = userIdentityAuthReq.getTrueName();

        String faceToken = userIdentityAuthReq.getFaceToken();
        MultipartFile megliveData = userIdentityAuthReq.getMegliveData();

        if(StringUtils.isBlank(faceToken) || megliveData == null || megliveData.isEmpty()){
            throw TytException.createException(ResponseEnum.request_error.info());
        }

        //校验人脸识别次数
        int userFaceIdCardCount = userVerifyService.getUserFaceIdCardCount(userId, idCard, true);

        UserFaceVerifyVo faceVerifyVo = new UserFaceVerifyVo();
        //人脸识别时，自动验真
        faceVerifyVo.setVerifyStatus(GlobalStatusEnum.yes.getCode());
        faceVerifyVo.setVerifyFailCount(userFaceIdCardCount);

        log.info("faceVerifyV3_time_test_004");
        //人脸认证
        FaceV3VerifyResp faceVerifyResp = userVerifyService.faceVerifyV3(faceToken, megliveData);
        boolean faceVerifyResult = userVerifyService.isVaceVerifyPass(faceVerifyResp);

        log.info("faceVerifyV3_time_test_005");
        if(!faceVerifyResult){
            log.info("face_verify_fail : " + userId);
            //人脸认证失败，直接返回
            userFaceIdCardCount = userVerifyService.incrUserFaceIdCardCount(userId, idCard);

            faceVerifyVo.setVerifyStatus(GlobalStatusEnum.no.getCode());
            faceVerifyVo.setMsg("人脸认证失败");
            faceVerifyVo.setVerifyFailCount(userFaceIdCardCount);

            return faceVerifyVo;
        }

        log.info("face_verify_pass : " + userId);

        MultipartFile mainUrlPic = userIdentityAuthReq.getMainUrlPic();
        MultipartFile iPhotoPic = userIdentityAuthReq.getIPhotoPic();
        MultipartFile qualificationsUrlPic = userIdentityAuthReq.getQualificationsUrlPic();

        UserIdentityAuthBean userIdentityAuthBean = ConvertUtil.beanConvert(userIdentityAuthReq, new UserIdentityAuthBean());

        TytUserIdentityAuth tytUserIdentityAuth = this.createTytUserIdentityAuth(userIdentityAuthReq.getUserId(), userIdentityAuthBean,
                mainUrlPic, null, qualificationsUrlPic, null, iPhotoPic, userIdentityAuthReq.getClientSign());

        //人脸识别活体照片上传
        uploadFaceVerifyImage(tytUserIdentityAuth, ObjectUtil.isNotNull(faceVerifyResp.getImages()) ? faceVerifyResp.getImages().getImageBest() : null);

        log.info("faceVerifyV3_time_test_006");
        ResultMsgBean resultMsgBean = this.save(tytUserIdentityAuth, clientSign, true);

        log.info("faceVerifyV3_time_test_007");
        if(resultMsgBean.isSuccess()){
            //认证成功
            return faceVerifyVo;
        } else {
            ResponseCode responseCode = new ResponseCode(resultMsgBean.getCode(), resultMsgBean.getMsg());
            throw TytException.createException(responseCode);
        }
    }

    @Override
    public void sendUserAutoVerifyMq(Long userId) {

        UserIdVo userIdVo = new UserIdVo();
        userIdVo.setUserId(userId);

        businessMqProducerService.saveAndSendMessage(userIdVo, UserBusinessTopic.TOPIC_NAME, UserBusinessTopic.Tag.USER_VERIFY_PASS, "用户实名认证", 500L);

    }

    private void setSourceType(TytInvoiceEnterprise dbEnterprise, Integer clientSign){
        if(clientSign == null){
            return;
        }
        Integer sourceType = null;

        if(Constant.ClientSignEnum.isCar(clientSign)){
            sourceType = EnterpriseSourceTypeEnum.CAR.getCode();
        } else if(Constant.ClientSignEnum.isGoods(clientSign)){
            sourceType = EnterpriseSourceTypeEnum.GOODS.getCode();
        }
        dbEnterprise.setSourceType(sourceType);
    }

    private void checkEnterpriseModifyAllow(Long userId){
        EnterpriseModifyCheckVo modifyCheckVo = apiInvoiceEnterpriseService.checkModifyAllow(userId);
        if(modifyCheckVo != null){
            Boolean orderExist = modifyCheckVo.getOrderExist();
            Boolean transportExist = modifyCheckVo.getTransportExist();

            if(BooleanUtils.isTrue(orderExist)){
                throw TytException.createException(PlatResponseEnum.ORDER_EXIST.info());
            }

            if(BooleanUtils.isTrue(transportExist)){
                throw TytException.createException(PlatResponseEnum.TRANDPORT_EXIST.info());
            }
        }
    }

    @Override
    public ResultMsgBean saveEnterprise(Long userId, String enterpriseAuthLicenseUrl, String enterpriseAuthPath, Integer clientSign) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        TytUserIdentityAuth authInfo = getByUserId(userId.toString());
        if (!Constant.AGE1.equals(authInfo.getIdentityStatus())) {
            resultMsgBean.setCode(ReturnCodeConstant.FILE_ENTERPRISE_AUTH);
            resultMsgBean.setMsg("实名认证成功后才可进行企业认证");
            return resultMsgBean;
        } else {
            //认证信息入新表
            TytInvoiceEnterprise verifyInfo = enterpriseDBService.getVerifyInfoByUserId(userId);
            if (ObjectUtil.isNotEmpty(verifyInfo)) {
                if (EnterpriseVerifyOverallStatusEnum.VERIFIED.getStatus().equals(verifyInfo.getEnterpriseVerifyStatus())) {
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "the enterprise has verified"));
                }
                if (EnterpriseVerifyNodeStatusEnum.VERIFIED.getStatus().equals(verifyInfo.getInfoVerifyStatus())) {
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "enterprise info verified"));
                }
                TytInvoiceEnterprise tytInvoiceEnterprise = buildDto(verifyInfo.getId(), enterpriseAuthLicenseUrl);

                if(tytInvoiceEnterprise.getEnterpriseName() != null) {
                    if(this.checkMainInfoChange(tytInvoiceEnterprise, verifyInfo)){
                        this.checkEnterpriseModifyAllow(userId);
                    }
                }

                this.setSourceType(tytInvoiceEnterprise, clientSign);
                enterpriseDBService.update(tytInvoiceEnterprise);
            } else {
                UserBaseInfo userInfo = internalUserService.getByUserId(userId);
                if (ObjectUtil.isNull(userInfo.getUserId())) {
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "user info not queried"));
                }
                TytInvoiceEnterprise tytInvoiceEnterprise = this.buildInsertDto(userInfo, enterpriseAuthLicenseUrl);
                this.setSourceType(tytInvoiceEnterprise, clientSign);
                enterpriseDBService.insert(tytInvoiceEnterprise);
            }

            //认证信息入老表
            authInfo.setEnterpriseAuthFailureReason(null);
            authInfo.setEnterpriseAuthCreditCode(null);
            authInfo.setEnterpriseAuthCompanyName(null);
            authInfo.setEnterpriseAuthFailureReason(null);
            authInfo.setEnterpriseAuthLicenseUrl(enterpriseAuthLicenseUrl);
            authInfo.setEnterpriseAuthStatus(2);
            authInfo.setExamineStatus(0);
            Date utime = new Date();
            if (authInfo.getEnterpriseAuthCtime() == null) {
                authInfo.setEnterpriseAuthCtime(utime);
            }
            authInfo.setEnterpriseAuthUtime(utime);
            authInfo.setUtime(utime);
            //企业认证来源
            authInfo.setEnterpriseAuthPath(enterpriseAuthPath);
            this.getBaseDao().update(authInfo);

            //修改用户缓存
            cacheService.del(Constant.CACHE_USER_KEY + userId);
        }
        return resultMsgBean;
    }

    private TytInvoiceEnterprise buildDto(Long id, String enterpriseAuthLicenseUrl) {
        TytInvoiceEnterprise tytInvoiceEnterprise = new TytInvoiceEnterprise();
        tytInvoiceEnterprise.setId(id);
        tytInvoiceEnterprise.setLicenseUrl(enterpriseAuthLicenseUrl);
        tytInvoiceEnterprise.setEnterpriseVerifyStatus(EnterpriseVerifyOverallStatusEnum.VERIFING.getStatus());
        tytInvoiceEnterprise.setInfoVerifyStatus(EnterpriseVerifyNodeStatusEnum.VERIFING.getStatus());
        tytInvoiceEnterprise.setMtime(new Date());

        doOCR(tytInvoiceEnterprise, enterpriseAuthLicenseUrl);

        return tytInvoiceEnterprise;
    }

    private boolean checkMainInfoChange(TytInvoiceEnterprise newInvoiceEnterprise,
                                        TytInvoiceEnterprise dbEnterprise){

        String enterpriseName = newInvoiceEnterprise.getEnterpriseName();
        String enterpriseCreditCode = newInvoiceEnterprise.getEnterpriseCreditCode();
        String legalPersonName = newInvoiceEnterprise.getLegalPersonName();

        String dbEnterpriseName = dbEnterprise.getEnterpriseName();
        String dbEnterpriseCreditCode = dbEnterprise.getEnterpriseCreditCode();
        String dbLegalPersonName = dbEnterprise.getLegalPersonName();

        if(!StringUtils.equals(enterpriseName, dbEnterpriseName)
                || !StringUtils.equals(enterpriseCreditCode, dbEnterpriseCreditCode)
                || !StringUtils.equals(legalPersonName, dbLegalPersonName)
        ){
            return true;
        }
        return false;
    }

    @Override
    public TytInvoiceTaxRateRule getUserRateRule(Long enterpriseId){
        TytInvoiceTaxRateRule invoiceTaxRateRule = null;

        if(enterpriseId != null){
            invoiceTaxRateRule = tytInvoiceTaxRateRuleMapper.getInvoiceTaxRateRule(2, enterpriseId);
        }
        if(invoiceTaxRateRule == null){
            invoiceTaxRateRule = tytInvoiceTaxRateRuleMapper.getInvoiceTaxRateRule(1, 0L);
        }
        return invoiceTaxRateRule;
    }

    /**
     * 初始化新加字段.
     * @param invoiceEnterprise invoiceEnterprise
     */
    @Override
    public TytSelfCompanySign initCustomerManager(TytInvoiceEnterprise invoiceEnterprise){
        TytSelfCompanySign selfCompanySign = tytSelfCompanySignMapper.getSelfCompanySign();

        Integer taxRateFloat = invoiceEnterprise.getTaxRateFloat();
        String customerManagerName = invoiceEnterprise.getCustomerManagerName();
        String customerManagerPhone = invoiceEnterprise.getCustomerManagerPhone();
        String customerManagerEmail = invoiceEnterprise.getCustomerManagerEmail();

        if(taxRateFloat == null){
            invoiceEnterprise.setTaxRateFloat(GlobalStatusEnum.no.getCode());
        }
        if(StringUtils.isBlank(customerManagerName)) {
            invoiceEnterprise.setCustomerManagerName(selfCompanySign.getLinkUserName());
        }
        if(StringUtils.isBlank(customerManagerPhone)){
            invoiceEnterprise.setCustomerManagerPhone(selfCompanySign.getLinkPhone());
        }
        if(StringUtils.isBlank(customerManagerEmail)) {
            invoiceEnterprise.setCustomerManagerEmail(selfCompanySign.getEmail());
        }
        return selfCompanySign;
    }

    @Override
    public void initBaseEnterprise(UserBaseInfo userInfo, TytInvoiceEnterprise invoiceEnterprise){
        invoiceEnterprise.setMtime(new Date());

        invoiceEnterprise.setCertigierUserId(userInfo.getUserId());
        invoiceEnterprise.setCertigierUserName(userInfo.getTrueName());
        invoiceEnterprise.setCertigierUserPhone(userInfo.getCellPhone());

        invoiceEnterprise.setManagerFlag(GlobalStatusEnum.no.getCode());
        //V6490 设置过费率的同一统一信用代码的企业直接取以前设置的。未设置过的走默认
        TytInvoiceEnterprise enterpriseByCode = enterpriseDBService.getEnterpriseByCode(invoiceEnterprise.getEnterpriseCreditCode());
        if (ObjectUtil.isNotNull(enterpriseByCode) && ObjectUtil.isNotNull(enterpriseByCode.getEnterpriseTaxRate())) {
            invoiceEnterprise.setEnterpriseTaxRate(enterpriseByCode.getEnterpriseTaxRate());
        } else {
            //默认值
            TytInvoiceTaxRateRule invoiceTaxRateRule = this.getUserRateRule(null);

            invoiceEnterprise.setEnterpriseTaxRate(invoiceTaxRateRule.getEarlierTaxRate());
        }

        //新增字段
        this.initCustomerManager(invoiceEnterprise);
    }

    private TytInvoiceEnterprise buildInsertDto(UserBaseInfo userInfo, String enterpriseAuthLicenseUrl) {
        TytInvoiceEnterprise dto = new TytInvoiceEnterprise();
        this.initBaseEnterprise(userInfo, dto);

        dto.setCtime(new Date());
        dto.setLicenseUrl(enterpriseAuthLicenseUrl);
        dto.setEnterpriseVerifyStatus(EnterpriseVerifyOverallStatusEnum.VERIFING.getStatus());
        dto.setInfoVerifyStatus(EnterpriseVerifyNodeStatusEnum.VERIFING.getStatus());
        dto.setContractVerifyStatus(EnterpriseVerifyNodeStatusEnum.UNVERIFIED.getStatus());
        dto.setCertigierVerifyStatus(EnterpriseVerifyNodeStatusEnum.UNVERIFIED.getStatus());
        dto.setAccountStatus(null);

        doOCR(dto, enterpriseAuthLicenseUrl);

        return dto;
    }

    private void doOCR(TytInvoiceEnterprise tytInvoiceEnterprise, String enterpriseAuthLicenseUrl) {
        String prefix_picture = tytConfigService.getStringValue("prefix_picture", "http://newtest.teyuntong.net/rootdata");
        OcrBusinessLicenseVo ocrBusinessLicenseVo = commonApiService.doOcrByLicenseUrl(prefix_picture + enterpriseAuthLicenseUrl);
        if (ocrBusinessLicenseVo != null && StringUtils.isNotBlank(ocrBusinessLicenseVo.getCompanyName())) {
            tytInvoiceEnterprise.setEnterpriseName(ocrBusinessLicenseVo.getCompanyName());
            tytInvoiceEnterprise.setEnterpriseCreditCode(ocrBusinessLicenseVo.getCreditCode());
            tytInvoiceEnterprise.setLegalPersonName(ocrBusinessLicenseVo.getLegalPersonName());
            tytInvoiceEnterprise.setEnterpriseDetailAddress(ocrBusinessLicenseVo.getRegisterAddress());
        }
    }

    @Override
    public ResultMsgBean saveAfter5300(TytUserIdentityAuth tytUserIdentityAuth) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        User user = userService.getById(tytUserIdentityAuth.getUserId());
        // 查询该用户当前的认证信息，判断数据库中有没有，有修改，没有保存
        TytUserIdentityAuth oldUserIdentityAuth = getTytUserIdentityAuth(
                tytUserIdentityAuth.getUserId());
        // 验证 参数
        if (!checkTytUserIdentityAuthNew(user, oldUserIdentityAuth, resultMsgBean,
                tytUserIdentityAuth)) {
            return resultMsgBean;
        }
        // 设置数据类型为5300以上版本数据
        tytUserIdentityAuth.setDataType(4);
        // 如果不存在则直接保存认证信息
        if (oldUserIdentityAuth == null) {
            // 初始化数据，不是这个身份的数据清空
            initTytUserIdentityAuth(tytUserIdentityAuth);
            tytUserIdentityAuth.setId(null);
            this.getBaseDao().insert(tytUserIdentityAuth);
        } else {
            // 保存日志
            copyPropertyNew(tytUserIdentityAuth, oldUserIdentityAuth);
            // 设置性别和数据来源
            oldUserIdentityAuth.setSex(IdCardUtil.getGender(tytUserIdentityAuth.getIdCard()));
            oldUserIdentityAuth.setDataType(4);
            this.getBaseDao().update(oldUserIdentityAuth);
        }
        // 修改用户表
        user.setIdentityType(tytUserIdentityAuth.getIdentityType());
        user.setUserClass(tytUserIdentityAuth.getUserClass());
        user.setVerifyPhotoSign(2);
        userService.update(user);
        // 修改用户缓存
        cacheService.del(Constant.CACHE_USER_KEY + user.getId());
        /* user信息缓存 */
        cacheService.setObject(Constant.CACHE_USER_KEY + user.getId(), user,
                AppConfig.getIntProperty("tyt.cache.user.time"));
        // 同步到老系统
        userIdentityMainService.saveUserIdentityMain(user.getId(),
                tytUserIdentityAuth.getTrueName(), tytUserIdentityAuth.getIdCard(),
                tytUserIdentityAuth.getMainUrl());
        resultMsgBean.setCode(ResultMsgBean.OK);
        resultMsgBean.setMsg("提交成功");
        return resultMsgBean;
    }

    @Override
    public TytUserIdentityAuth getTytUserIdentityAuth(Long userId) {
        String hql = "from TytUserIdentityAuth where userId=? ";
        List<TytUserIdentityAuth> list = this.getBaseDao().find(hql, userId);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public TytUserIdentityAuth getTytUserIdentityAuth(String userName, String idCard) {
        String hql = "from TytUserIdentityAuth where trueName=? and idCard = ? and infoStatus = 1";
        List<TytUserIdentityAuth> list = this.getBaseDao().find(hql, userName, idCard);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    protected void copyPropertyNew(TytUserIdentityAuth tytUserIdentityAuth,
                                   TytUserIdentityAuth oldUserIdentityAuth) {
        oldUserIdentityAuth.setMobile(tytUserIdentityAuth.getMobile());
        oldUserIdentityAuth.setIdentityStatus(2);
        oldUserIdentityAuth.setUserClass(tytUserIdentityAuth.getUserClass());
        oldUserIdentityAuth.setIdentityType(tytUserIdentityAuth.getIdentityType());
        oldUserIdentityAuth.setTrueName(tytUserIdentityAuth.getTrueName());
        oldUserIdentityAuth.setIdCard(tytUserIdentityAuth.getIdCard());
        oldUserIdentityAuth.setInfoStatus(0);
        oldUserIdentityAuth.setInfoFailureReason(null);
        // 如果客户端上传了身份证正面照则使用客户端上传的
        if (StringUtils.isNotBlank(tytUserIdentityAuth.getMainUrl())) {
            oldUserIdentityAuth.setMainUrl(tytUserIdentityAuth.getMainUrl());
            oldUserIdentityAuth.setMainStatus(0);
            oldUserIdentityAuth.setMainFailureReason(null);
        }
        // 如果客户端上传了本人照则使用客户端上传的
        if (StringUtils.isNotBlank(tytUserIdentityAuth.getiPhotoUrl())) {
            oldUserIdentityAuth.setiPhotoUrl(tytUserIdentityAuth.getiPhotoUrl());
            oldUserIdentityAuth.setiPhotoStatus(0);
            oldUserIdentityAuth.setiPhotoFailureReason(null);
        }
        // 重置其他字段
        oldUserIdentityAuth.setCtime(new Date());
        oldUserIdentityAuth.setExamineStatus(0);
        oldUserIdentityAuth.setExamineTime(null);
        oldUserIdentityAuth.setExamineUserId(null);
        oldUserIdentityAuth.setExamineUserName(null);
        oldUserIdentityAuth.setUtime(new Date());
    }

    protected void copyProperty(TytUserIdentityAuth tytUserIdentityAuth, TytUserIdentityAuth tuia) {
        tuia.setFaceVerifyImageUrl(tytUserIdentityAuth.getFaceVerifyImageUrl());
        tuia.setMobile(tytUserIdentityAuth.getMobile());
        tuia.setIdentityStatus(2);
        tuia.setUserClass(tytUserIdentityAuth.getUserClass());
        tuia.setIdentityType(tytUserIdentityAuth.getIdentityType());
        //个人认证来源
        tuia.setUserAuthPath(tytUserIdentityAuth.getUserAuthPath());
        /*if(tuia.getInfoStatus()==null||tuia.getInfoStatus().intValue()!=1){
         */

        tuia.setTrueName(tytUserIdentityAuth.getTrueName());

        //性别
        if (tytUserIdentityAuth.getIdentityType().intValue() != 2) {
            tytUserIdentityAuth.setSex(IdCardUtil.getGender(tytUserIdentityAuth.getIdCard()));
        }
        tuia.setIdCard(tytUserIdentityAuth.getIdCard());
        tuia.setInfoStatus(0);
        tuia.setInfoFailureReason(null);

        /*}*/
        //if (tuia.getMainStatus() == null || tuia.getMainStatus().intValue() != 1) {
        if (StringUtils.isNotBlank(tytUserIdentityAuth.getMainUrl())) {
            tuia.setMainUrl(tytUserIdentityAuth.getMainUrl());
            tuia.setMainStatus(0);
            tuia.setMainFailureReason(null);
        }

        //}
//		else{
//			if(StringUtils.isNotBlank(tytUserIdentityAuth.getMainUrl())){
//				tuia.setMainUrl(tytUserIdentityAuth.getMainUrl());
//			}
//			tuia.setMainStatus(0);
//			tuia.setMainFailureReason(null);
//		}

        //if (tuia.getBackStatus() == null || tuia.getBackStatus().intValue() != 1) {
        if (StringUtils.isNotBlank(tytUserIdentityAuth.getBackUrl())) {
            tuia.setBackUrl(tytUserIdentityAuth.getBackUrl());
            tuia.setBackStatus(0);
            tuia.setBackFailueReason(null);
        }

        //}

//		if(StringUtils.isNotBlank(tytUserIdentityAuth.getBackUrl())){
//			tuia.setBackUrl(tytUserIdentityAuth.getBackUrl());
//		}
//		tuia.setBackStatus(0);
//		tuia.setBackFailueReason(null);
        if (StringUtils.isNotBlank(tytUserIdentityAuth.getQualificationsUrl())) {
            tuia.setQualificationsUrl(tytUserIdentityAuth.getQualificationsUrl());
        }
        tuia.setEnterpriseName(tytUserIdentityAuth.getEnterpriseName());
        tuia.setEnterpriseType(tytUserIdentityAuth.getEnterpriseType());
        tuia.setEnterpriseAmount(tytUserIdentityAuth.getEnterpriseAmount());
        tuia.setEnterprisePhone(tytUserIdentityAuth.getEnterprisePhone());

        if (tuia.getLicenseStatus() == null || tuia.getLicenseStatus().intValue() != 1) {
            if (StringUtils.isNotBlank(tytUserIdentityAuth.getLicenseUrl())) {
                tuia.setLicenseUrl(tytUserIdentityAuth.getLicenseUrl());
            }
            tuia.setLicenseStatus(0);
            tuia.setLicenseFailureReason(null);
        }
//		else{
//			if(StringUtils.isNotBlank(tytUserIdentityAuth.getLicenseUrl())){
//				tuia.setLicenseUrl(tytUserIdentityAuth.getLicenseUrl());
//			}
//			tuia.setLicenseStatus(0);
//			tuia.setLicenseFailureReason(null);
//		}
        tuia.setWorkType(tytUserIdentityAuth.getWorkType());
        tuia.setCtime(new Date());
        tuia.setExamineStatus(0);
        tuia.setExamineTime(null);
        tuia.setExamineUserId(null);
        tuia.setExamineUserName(null);
        tuia.setUtime(new Date());
        tuia.setDataType(1);

        //if (tuia.getiPhotoStatus() == null || tuia.getiPhotoStatus().intValue() != 1) {
        if (StringUtils.isNotBlank(tytUserIdentityAuth.getiPhotoUrl())) {
            tuia.setiPhotoUrl(tytUserIdentityAuth.getiPhotoUrl());
            tuia.setiPhotoStatus(0);
            tuia.setiPhotoFailureReason(null);
        }
        //}

        //证件有效期
        tuia.setIdCardLongTerm(tytUserIdentityAuth.getIdCardLongTerm());
        tuia.setIdCardValidDate(tytUserIdentityAuth.getIdCardValidDate());

//		else{
//			if(StringUtils.isNotBlank(tytUserIdentityAuth.getiPhotoUrl())){
//				tuia.setiPhotoUrl(tytUserIdentityAuth.getiPhotoUrl());
//			}
//			tuia.setiPhotoStatus(0);
//			tuia.setiPhotoFailureReason(null);
//		}

        switch (tuia.getIdentityType().intValue()) {
            case 1:
                tuia.setEnterpriseAmount(null);
                tuia.setEnterpriseName(null);
                tuia.setEnterprisePhone(null);
                tuia.setEnterpriseType(null);
                tuia.setBackUrl(null);
                tuia.setQualificationsUrl(null);
                tuia.setLicenseUrl(null);
                break;
            case 2:
                tuia.setTrueName(null);
                tuia.setIdCard(null);
                tuia.setWorkType(null);
                tuia.setQualificationsUrl(null);
                break;
            case 3:
                tuia.setEnterpriseAmount(null);
                tuia.setEnterpriseName(null);
                tuia.setEnterprisePhone(null);
                tuia.setEnterpriseType(null);
                tuia.setWorkType(null);
                tuia.setBackUrl(null);
                tuia.setLicenseUrl(null);
                break;
            case 4:
                tuia.setEnterpriseAmount(null);
                tuia.setEnterpriseName(null);
                tuia.setEnterprisePhone(null);
                tuia.setEnterpriseType(null);
                tuia.setWorkType(null);
                tuia.setBackUrl(null);
                tuia.setLicenseUrl(null);
                tuia.setQualificationsUrl(null);
                break;
            case 5:
                tuia.setEnterpriseAmount(null);
                tuia.setEnterpriseName(null);
                tuia.setEnterprisePhone(null);
                tuia.setEnterpriseType(null);
                tuia.setWorkType(null);
                tuia.setBackUrl(null);
                tuia.setLicenseUrl(null);
                break;
            case 6:
                tuia.setEnterpriseAmount(null);
                tuia.setEnterpriseName(null);
                tuia.setEnterprisePhone(null);
                tuia.setEnterpriseType(null);
                tuia.setWorkType(null);
                tuia.setBackUrl(null);
                tuia.setLicenseUrl(null);
                break;
            case 7:
                tuia.setEnterpriseAmount(null);
                tuia.setEnterpriseName(null);
                tuia.setEnterprisePhone(null);
                tuia.setEnterpriseType(null);
                tuia.setWorkType(null);
                tuia.setBackUrl(null);
                tuia.setLicenseUrl(null);
                tuia.setQualificationsUrl(null);
                break;
            case 8:
                tuia.setEnterpriseAmount(null);
                tuia.setEnterpriseType(null);
                tuia.setWorkType(null);
                tuia.setBackUrl(null);
                tuia.setQualificationsUrl(null);
                break;
            case 9:
                tuia.setEnterpriseAmount(null);
                tuia.setEnterpriseName(null);
                tuia.setEnterprisePhone(null);
                tuia.setEnterpriseType(null);
                tuia.setWorkType(null);
                tuia.setBackUrl(null);
                tuia.setLicenseUrl(null);
                break;
        }
    }

    /**
     * 初始化属性
     *
     * @param tytUserIdentityAuth
     */
    protected void initTytUserIdentityAuth(TytUserIdentityAuth tytUserIdentityAuth) {
        tytUserIdentityAuth.setCtime(new Date());
        if (tytUserIdentityAuth.getIdentityType().intValue() != 2) {
            tytUserIdentityAuth.setSex(IdCardUtil.getGender(tytUserIdentityAuth.getIdCard()));
        }
        tytUserIdentityAuth.setIdentityStatus(2);
        tytUserIdentityAuth.setInfoStatus(0);
        tytUserIdentityAuth.setInfoFailureReason(null);
        tytUserIdentityAuth.setMainStatus(0);
        tytUserIdentityAuth.setMainFailureReason(null);
        tytUserIdentityAuth.setBackStatus(0);
        tytUserIdentityAuth.setBackFailueReason(null);
        tytUserIdentityAuth.setLicenseStatus(0);
        tytUserIdentityAuth.setLicenseFailureReason(null);
        tytUserIdentityAuth.setExamineStatus(0);
        tytUserIdentityAuth.setExamineTime(null);
        tytUserIdentityAuth.setExamineUserId(null);
        tytUserIdentityAuth.setExamineUserName(null);
        tytUserIdentityAuth.setUtime(new Date());
        tytUserIdentityAuth.setDataType(1);
        tytUserIdentityAuth.setiPhotoStatus(0);
        tytUserIdentityAuth.setiPhotoFailureReason(null);

        //赋值为空导致用户上传的本人照失效，注释掉代码
        //tytUserIdentityAuth.setiPhotoUrl(null);

        switch (tytUserIdentityAuth.getIdentityType().intValue()) {
            case 1:
                tytUserIdentityAuth.setEnterpriseAmount(null);
                tytUserIdentityAuth.setEnterpriseName(null);
                tytUserIdentityAuth.setEnterprisePhone(null);
                tytUserIdentityAuth.setEnterpriseType(null);
                tytUserIdentityAuth.setBackUrl(null);
                tytUserIdentityAuth.setQualificationsUrl(null);
                tytUserIdentityAuth.setLicenseUrl(null);
                break;
            case 2:
                tytUserIdentityAuth.setTrueName(null);
                tytUserIdentityAuth.setIdCard(null);
                tytUserIdentityAuth.setWorkType(null);
                tytUserIdentityAuth.setQualificationsUrl(null);
                break;
            case 3:
                tytUserIdentityAuth.setEnterpriseAmount(null);
                tytUserIdentityAuth.setEnterpriseName(null);
                tytUserIdentityAuth.setEnterprisePhone(null);
                tytUserIdentityAuth.setEnterpriseType(null);
                tytUserIdentityAuth.setWorkType(null);
                tytUserIdentityAuth.setBackUrl(null);
                tytUserIdentityAuth.setLicenseUrl(null);
                break;
            case 4:
                tytUserIdentityAuth.setEnterpriseAmount(null);
                tytUserIdentityAuth.setEnterpriseName(null);
                tytUserIdentityAuth.setEnterprisePhone(null);
                tytUserIdentityAuth.setEnterpriseType(null);
                tytUserIdentityAuth.setWorkType(null);
                tytUserIdentityAuth.setBackUrl(null);
                tytUserIdentityAuth.setLicenseUrl(null);
                tytUserIdentityAuth.setQualificationsUrl(null);
                break;
            case 5:
                tytUserIdentityAuth.setEnterpriseAmount(null);
                tytUserIdentityAuth.setEnterpriseName(null);
                tytUserIdentityAuth.setEnterprisePhone(null);
                tytUserIdentityAuth.setEnterpriseType(null);
                tytUserIdentityAuth.setWorkType(null);
                tytUserIdentityAuth.setBackUrl(null);
                tytUserIdentityAuth.setLicenseUrl(null);
                break;
            case 6:
                tytUserIdentityAuth.setEnterpriseAmount(null);
                tytUserIdentityAuth.setEnterpriseName(null);
                tytUserIdentityAuth.setEnterprisePhone(null);
                tytUserIdentityAuth.setEnterpriseType(null);
                tytUserIdentityAuth.setWorkType(null);
                tytUserIdentityAuth.setBackUrl(null);
                tytUserIdentityAuth.setLicenseUrl(null);
                break;
            case 7:
                tytUserIdentityAuth.setEnterpriseAmount(null);
                tytUserIdentityAuth.setEnterpriseName(null);
                tytUserIdentityAuth.setEnterprisePhone(null);
                tytUserIdentityAuth.setEnterpriseType(null);
                tytUserIdentityAuth.setWorkType(null);
                tytUserIdentityAuth.setBackUrl(null);
                tytUserIdentityAuth.setLicenseUrl(null);
                tytUserIdentityAuth.setQualificationsUrl(null);
                break;
            case 8:
                tytUserIdentityAuth.setEnterpriseAmount(null);
                tytUserIdentityAuth.setEnterpriseType(null);
                tytUserIdentityAuth.setWorkType(null);
                tytUserIdentityAuth.setBackUrl(null);
                tytUserIdentityAuth.setQualificationsUrl(null);
                break;
            case 9:
                tytUserIdentityAuth.setEnterpriseAmount(null);
                tytUserIdentityAuth.setEnterpriseName(null);
                tytUserIdentityAuth.setEnterprisePhone(null);
                tytUserIdentityAuth.setEnterpriseType(null);
                tytUserIdentityAuth.setWorkType(null);
                tytUserIdentityAuth.setBackUrl(null);
                tytUserIdentityAuth.setLicenseUrl(null);
                break;
            default:
                break;
        }
    }

    /**
     * true 通过，false没通过检查
     *
     * @param resultMsgBean
     * @param tytUserIdentityAuth
     * @return boolean
     */
    private boolean checkTytUserIdentityAuth(User user, TytUserIdentityAuth tuia, ResultMsgBean resultMsgBean,
                                               TytUserIdentityAuth tytUserIdentityAuth, String clientSign) {

        boolean status = false;
        //新逻辑，只有认证中时，才不允许重复认证.
        //tuia.getIdentityStatus() == 1 ||
        if (tuia != null && (tuia.getIdentityStatus() == 2)) {
            if (String.valueOf(Constant.ClientSignEnum.ANDROID_CAR.code).equals(clientSign)
                    || (String.valueOf(Constant.ClientSignEnum.IOS_CAR.code).equals(clientSign))
                    || String.valueOf(Constant.ClientSignEnum.ANDROID_GOODS.code).equals(clientSign)
                    || (String.valueOf(Constant.ClientSignEnum.IOS_GOODS.code).equals(clientSign))
                    || (String.valueOf(Constant.ClientSignEnum.WEB_GOODS.code).equals(clientSign))) {
                resultMsgBean.setCode(2004);
                resultMsgBean.setMsg("实名认证中，不能上传新的认证信息");
            } else {
                resultMsgBean.setCode(2003);
                resultMsgBean.setMsg("身份认证为认证中，不能上传新的身份信息");
            }
            return status;
        }
        Long userId = user.getId();
        TytInvoiceEnterprise invoiceEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);
        if(invoiceEnterprise != null){
            Integer infoVerifyStatus = invoiceEnterprise.getInfoVerifyStatus();

            if(EnterpriseVerifyNodeStatusEnum.VERIFING.equalsCode(infoVerifyStatus)
                    || EnterpriseVerifyNodeStatusEnum.VERIFIED.equalsCode(infoVerifyStatus)){
                throw TytException.createException(ResponseEnum.request_error.info("请先联系客服驳回企业认证状态，再修改实名"));
            }
        }

        //校验有效期
        Date idCardValidDate = tytUserIdentityAuth.getIdCardValidDate();
        Integer idCardLongTerm = tytUserIdentityAuth.getIdCardLongTerm();
        if(GlobalStatusEnum.yes.equalsCode(idCardLongTerm)){
            idCardValidDate = null;
        } else {
            idCardLongTerm = GlobalStatusEnum.no.getCode();

            if(idCardValidDate != null){
                Date nowTime = new Date();
                Date todayTime = DateUtil.startOfDay(nowTime);

                if(todayTime.after(idCardValidDate)){
                    //证件已过期
                    throw TytException.createException(ResponseEnum.request_error.info("身份证已过期，无法提交"));
                }
            }
        }
        tytUserIdentityAuth.setIdCardValidDate(idCardValidDate);
        tytUserIdentityAuth.setIdCardLongTerm(idCardLongTerm);

        //是否换了一级身份 true是换了
        boolean isReplaceIdentity = user.getIdentityType() != null && !(user.getIdentityType().intValue() == tytUserIdentityAuth.getIdentityType());

        if (tytUserIdentityAuth.getIdentityType().intValue() != 2 && StringUtils.isBlank(tytUserIdentityAuth.getTrueName())) {
            resultMsgBean.setCode(2003);
            resultMsgBean.setMsg("真实姓名不能为空");
        } else if (tytUserIdentityAuth.getIdentityType().intValue() != 2 && StringUtils.isBlank(tytUserIdentityAuth.getIdCard())) {
            resultMsgBean.setCode(2003);
            resultMsgBean.setMsg("身份证号不能为空");
        } else if (!Constant.ClientSignNewEnum.isClientSignEnumcode(Integer.parseInt(clientSign)) &&
                ((isReplaceIdentity || (tuia == null || tuia.getMainStatus().intValue() != 1)) && StringUtils.isBlank(tytUserIdentityAuth.getMainUrl()))) {
            resultMsgBean.setCode(2003);
            resultMsgBean.setMsg("手持身份证照不能为空");
        } else if (tuia == null) {
            if (StringUtils.isBlank(tytUserIdentityAuth.getMainUrl())) {
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("请重新上传身份证正面照");
                return status;
            }
            if (StringUtils.isBlank(tytUserIdentityAuth.getiPhotoUrl())) {
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("请上传身份证国徽页");
                return status;
            }
            status = true;
        } else if (tuia != null && (tuia.getMainStatus() == null || tuia.getMainStatus() != 1)) {
            if (StringUtils.isBlank(tytUserIdentityAuth.getMainUrl())) {
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("请重新上传身份证正面照");
                return status;
            }
            status = true;
        } else if (tuia != null && (tuia.getiPhotoStatus() == null || tuia.getiPhotoStatus() != 1)) {
            if (StringUtils.isBlank(tytUserIdentityAuth.getiPhotoUrl())) {
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("请上传身份证国徽页");
                return status;
            }
            status = true;
        } else {
            switch (tytUserIdentityAuth.getIdentityType().intValue()) {
                case 1:
					/*if(tytUserIdentityAuth.getWorkType()==null||tytUserIdentityAuth.getWorkType().intValue()==0){
						resultMsgBean.setCode(2003);
						resultMsgBean.setMsg("从事工作不能为空");
					}else*/
                    status = true;
                    break;
                case 2:
				/*	if((tuia==null|| tuia.getBackStatus().intValue()!=1) && StringUtils.isBlank(tytUserIdentityAuth.getBackUrl())){
						resultMsgBean.setCode(2003);
						resultMsgBean.setMsg("身份证正面照不能为空");
					}else*/
                    if (!Constant.ClientSignNewEnum.isClientSignEnumcode(Integer.parseInt(clientSign))) {
                        if (StringUtils.isBlank(tytUserIdentityAuth.getEnterpriseName())) {
                            resultMsgBean.setCode(2003);
                            resultMsgBean.setMsg("企业名称不能为空");
                        } else if (tytUserIdentityAuth.getEnterpriseType() == null || tytUserIdentityAuth.getEnterpriseType().intValue() == 0) {
                            resultMsgBean.setCode(2003);
                            resultMsgBean.setMsg("企业类型不能为空");
                        } else if (tytUserIdentityAuth.getEnterpriseAmount() == null || tytUserIdentityAuth.getEnterpriseAmount().intValue() == 0) {
                            resultMsgBean.setCode(2003);
                            resultMsgBean.setMsg("企业年发货量不能为空");
                        } else if (StringUtils.isBlank(tytUserIdentityAuth.getEnterprisePhone())) {
                            resultMsgBean.setCode(2003);
                            resultMsgBean.setMsg("企业电话不能为空");
                        } else if ((tuia == null || tuia.getLicenseStatus().intValue() != 1) && StringUtils.isBlank(tytUserIdentityAuth.getLicenseUrl())) {
                            resultMsgBean.setCode(2003);
                            resultMsgBean.setMsg("营业执照不能为空");
                        } else {
                            status = true;
                        }
                    } else {
                        status = true;
                    }
                    break;
                case 3:
                    //				if((tuia==null|| StringUtils.isBlank(tuia.getQualificationsUrl()))&&StringUtils.isBlank(tytUserIdentityAuth.getQualificationsUrl())){
                    //					resultMsgBean.setCode(2003);
                    //					resultMsgBean.setMsg("名片照不能为空");
                    //				}else
                    status = true;
                    break;
                case 4:
                    status = true;
                    break;
                case 5:
                    //				if((tuia==null|| StringUtils.isBlank(tuia.getQualificationsUrl()))&&StringUtils.isBlank(tytUserIdentityAuth.getQualificationsUrl())){
                    //					resultMsgBean.setCode(2003);
                    //					resultMsgBean.setMsg("名片照不能为空");
                    //				}else
                    status = true;
                    break;
                case 6:
                    //				if((tuia==null|| StringUtils.isBlank(tuia.getQualificationsUrl()))&&StringUtils.isBlank(tytUserIdentityAuth.getQualificationsUrl())){
                    //					resultMsgBean.setCode(2003);
                    //					resultMsgBean.setMsg("道路运输经营许可证照不能为空");
                    //				}else
                    status = true;
                    break;
                case 7:
                    status = true;
                    break;
                case 8:
                    if (!Constant.ClientSignNewEnum.isClientSignEnumcode(Integer.parseInt(clientSign))) {
                        if (StringUtils.isBlank(tytUserIdentityAuth.getEnterpriseName())) {
                            resultMsgBean.setCode(2003);
                            resultMsgBean.setMsg("企业名称不能为空");
                        } else if (StringUtils.isBlank(tytUserIdentityAuth.getEnterprisePhone())) {
                            resultMsgBean.setCode(2003);
                            resultMsgBean.setMsg("企业电话不能为空");
                        } else if ((tuia == null || tuia.getLicenseStatus().intValue() != 1) && StringUtils.isBlank(tytUserIdentityAuth.getLicenseUrl())) {
                            resultMsgBean.setCode(2003);
                            resultMsgBean.setMsg("营业执照不能为空");
                        } else {
                            status = true;
                        }
                    } else {
                        status = true;
                    }
                    break;
                case 9:
                    //				if((tuia==null|| StringUtils.isBlank(tuia.getQualificationsUrl()))&&StringUtils.isBlank(tytUserIdentityAuth.getQualificationsUrl())){
                    //					resultMsgBean.setCode(2003);
                    //					resultMsgBean.setMsg("名片照不能为空");
                    //				}else
                    status = true;
                    break;
                case 10:
					/*if(tytUserIdentityAuth.getWorkType()==null||tytUserIdentityAuth.getWorkType().intValue()==0){
						resultMsgBean.setCode(2003);
						resultMsgBean.setMsg("从事工作不能为空");
					}else*/
                    status = true;
                    break;
                case 11:
					/*if(tytUserIdentityAuth.getWorkType()==null||tytUserIdentityAuth.getWorkType().intValue()==0){
						resultMsgBean.setCode(2003);
						resultMsgBean.setMsg("从事工作不能为空");
					}else*/
                    status = true;
                    break;
                default:
                    log.error("identityType_not_support ... ");
            }
        }
        return status;
    }

    /**
     * 本方法适用于APP5300以上版本
     *
     * @param user
     * @param oldUserIndentityAuth
     * @param resultMsgBean
     * @param tytUserIdentityAuth
     * @return
     */
    private boolean checkTytUserIdentityAuthNew(User user, TytUserIdentityAuth oldUserIndentityAuth,
                                                ResultMsgBean resultMsgBean, TytUserIdentityAuth tytUserIdentityAuth) {
        boolean status = false;
        // 如果是认证中状态则不允许重新上传
        if (oldUserIndentityAuth != null && (oldUserIndentityAuth.getIdentityStatus() == 1 || oldUserIndentityAuth.getIdentityStatus() == 2)) {
            resultMsgBean.setCode(10000);
            resultMsgBean.setMsg("身份认证为认证中或认证成功，不能上传新的身份信息");
        } else {
            status = true;
        }
        return status;
    }

    @Override
    public TytUserIdentityAuth getByUserId(String userId) {
        List<TytUserIdentityAuth> userIdentityAuths = this.getBaseDao().find("from TytUserIdentityAuth where userId=?", Long.valueOf(userId));
        return userIdentityAuths.size() == 1 ? userIdentityAuths.get(0) : null;
    }

    @Override
    public TytUserIdentityAuth saveIdentityAuthMove(Long userId, String mobile, Integer identityType, Integer userClass, String trueName, String mainUrl, Date nowDate) throws Exception {
        /**获取手机号，默认一级身份，默认身份类型*/
        //if(identityType==null){
        String configIdentityTypeNameTable = ArgumentsNamePropertiesUtil.getStringValue("user.identity.auth.identity_type.dafault.name");
        identityType = tytConfigService.getIntValue(configIdentityTypeNameTable);
        //}
        //if(userClass==null){
        String configIdentityUserClassNameTable = ArgumentsNamePropertiesUtil.getStringValue("user.identity.auth.user_class.dafault.name");
        userClass = tytConfigService.getIntValue(configIdentityUserClassNameTable);
        //}
        /**添加或者修改到数据库(因为tyt_user_identity_auth每个用户仅有一条数据)*/
        TytUserIdentityAuth tytUserIdentityAuth = this.getByUserId(userId + "");
        User user = userService.getByUserId(userId);
        if (tytUserIdentityAuth == null) {
            tytUserIdentityAuth = ((TytUserIdentityAuthDao) this.getBaseDao()).saveIdentityAuth(user, userId, mobile, userClass, identityType, mainUrl, trueName, nowDate);
        } else {
            String idCard = null;
            String sex = null;
            if (user.getVerifyFlag() != null && user.getVerifyFlag().intValue() == 1) {
                trueName = user.getTrueName();
                idCard = user.getIdCard();
                sex = user.getSex();
                tytUserIdentityAuth.setTrueName(trueName);
                tytUserIdentityAuth.setIdCard(idCard);
                tytUserIdentityAuth.setSex(sex);
            }

            tytUserIdentityAuth.setIdentityStatus(2);
            tytUserIdentityAuth.setUserClass(userClass);
            tytUserIdentityAuth.setIdentityType(identityType);
            tytUserIdentityAuth.setInfoStatus(0);
            tytUserIdentityAuth.setMainStatus(0);
            tytUserIdentityAuth.setBackStatus(0);
            tytUserIdentityAuth.setLicenseStatus(0);
            tytUserIdentityAuth.setMobile(user.getCellPhone());
            tytUserIdentityAuth.setMainUrl(mainUrl);
            tytUserIdentityAuth.setCtime(new Date());
            tytUserIdentityAuth.setExamineStatus(0);
            tytUserIdentityAuth.setUtime(new Date());
            tytUserIdentityAuth.setDataType(0);

            this.getBaseDao().update(tytUserIdentityAuth);
            //tytUserIdentityAuth= ((TytUserIdentityAuthDao) this.getBaseDao()).updateIdentityAuth(user,tytUserIdentityAuth.getId(), userId, mobile, userClass, identityType, mainUrl, trueName,  nowDate);
        }
        tytUserIdentityAuth.setUserClass(userClass);
        tytUserIdentityAuth.setIdentityType(identityType);
        return tytUserIdentityAuth;

    }

    @Override
    public int getRealVerifyCount(Long userId) {

        String countKey = RedisKeyConstant.getUserRealVerifyKey(userId);

        String countValue = RedisUtil.get(countKey);

        int verifyCount = 0;
        if (StringUtils.isNotBlank(countValue)) {
            verifyCount = Integer.parseInt(countValue);
        }

        return verifyCount;
    }
}
