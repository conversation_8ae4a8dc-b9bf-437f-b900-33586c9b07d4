package com.tyt.user.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCreditPart;
import com.tyt.user.service.CreditPartService;


@Service("creditPartService")
public class CreditPartServiceImpl extends BaseServiceImpl<TytCreditPart,Long> implements CreditPartService {

    @Resource(name="creditPartDao")
    public void setBaseDao(BaseDao<TytCreditPart, Long> creditPartDao) {
        super.setBaseDao(creditPartDao);
    }
 
}
