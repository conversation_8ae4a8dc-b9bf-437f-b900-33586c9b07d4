package com.tyt.user.service.impl;

import java.io.File;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.TytConfig;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.TytUserIdentityInfo;
import com.tyt.model.TytUserIdentityMain;
import com.tyt.model.User;
import com.tyt.model.UserIdentity;
import com.tyt.user.dao.UserIdentityDao;
import com.tyt.user.querybean.IDCardAuthResult;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthBusiness;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserIdentityInfoService;
import com.tyt.user.service.UserIdentityMainService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.CreateFileUtil;
import com.tyt.util.IDCardAuth;
import com.tyt.util.IdCardUtil;
import com.tyt.util.ImageUtil;
import com.tyt.util.TimeUtil;


@Service("userIdentityMainService")
public class UserIdentityMainServiceImpl extends BaseServiceImpl<TytUserIdentityMain,Long> implements UserIdentityMainService {
	@Resource(name="userIdentityDao")
	UserIdentityDao userIdentityDao;
	
	@Resource(name = "userService")
	private UserService userService;
	
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
	@Resource(name = "userIdentityInfoService")
	private UserIdentityInfoService userIdentityInfoService;
	   
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;
	
	@Resource(name = "tytUserSubService")
	private TytUserSubService tytUserSubService;
	
	@Resource(name = "tytUserIdentityAuthBusiness")
	TytUserIdentityAuthBusiness tytUserIdentityAuthBusiness;
	
	@Resource(name = "tytUserIdentityAuthService")
	TytUserIdentityAuthService userIdentityAuthService;
	
	
    @Resource(name="userIdentityMainDao")
    public void setBaseDao(BaseDao<TytUserIdentityMain, Long> userIdentityMainDao) {
        super.setBaseDao(userIdentityMainDao);
    }
 
	

	@Override
	public TytUserIdentityMain getTytUserIdentityMainForUserId(Long userId) {
		String hql="from TytUserIdentityMain where userId=?";
		List<TytUserIdentityMain> list= this.getBaseDao().find(hql, userId);
		if(list!=null&& list.size()>0){
			return list.get(0);
		}
		return null;
	}

	public boolean isExitForPhoto(Long userId) throws Exception{
		
//		String hql="from TytUserIdentityMain where userId=? and verifyPhotoSign in(1,2)";
//		List<TytUserIdentityMain> list= this.getBaseDao().find(hql, userId);
//		if(list!=null&& list.size()>0){
//			return true;
//		}
//		return false;
		//to query from tyt_user table
		User user=userService.getByUserId(userId);
		if(user.getVerifyPhotoSign()==1||user.getVerifyPhotoSign()==2){
			return true;
		}
		return false;
	}
	
	public boolean isExitForInfo(String identity ,String trueName) {
		
		String hql="from TytUserIdentityMain where identity=?  and  verifyInfoSign=?";
		List<TytUserIdentityMain> list= this.getBaseDao().find(hql,  identity,new Integer(1));
		if(list!=null&& list.size()>0){
			return true;
		}
		return false;
	}
	
	
	public boolean saveUserIdentityMain(Long userId,String identity ,String trueName,MultipartFile mainPic) throws Exception {
		
		TytUserIdentityMain tytUserIdentityMain=this.getTytUserIdentityMainForUserId(userId);
		String mainUrl=renamePic(mainPic, "user");
		mainPic.transferTo(new File(AppConfig.getProperty("picture.path.domain")+mainUrl));
		Date nowDate=new Date();
		//保存或修改tytUserIdentityMain
		if(tytUserIdentityMain!=null){
			tytUserIdentityMain.setUpdateTime(nowDate);
			tytUserIdentityMain.setIdentity(identity);
			tytUserIdentityMain.setRealName(trueName);
			tytUserIdentityMain.setInfoTime(nowDate);
			if(tytUserIdentityMain.getVerifyInfoSign().intValue()!=1){
			tytUserIdentityMain.setVerifyInfoSign(2);
			}
			tytUserIdentityMain.setVerifyPhotoSign(2);
			/*数据库back_url保存用户>=2次上传的照片，每次覆盖；main_url只保存首次的照片，且不变*/
			tytUserIdentityMain.setBackUrl(mainUrl);
			tytUserIdentityMain.setPhotoTime(nowDate);
			this.getBaseDao().update(tytUserIdentityMain);
		}else{
			tytUserIdentityMain=new TytUserIdentityMain();
			tytUserIdentityMain.setIdentity(identity);
			tytUserIdentityMain.setRealName(trueName);
			tytUserIdentityMain.setInfoTime(nowDate);
			tytUserIdentityMain.setUserId(userId);
			tytUserIdentityMain.setCreateTime(nowDate);
			tytUserIdentityMain.setUpdateTime(nowDate);
			tytUserIdentityMain.setVerifyInfoSign(2);
			tytUserIdentityMain.setVerifyPhotoSign(2);
			tytUserIdentityMain.setMainUrl(mainUrl);
			tytUserIdentityMain.setPhotoTime(nowDate);
			this.getBaseDao().insert(tytUserIdentityMain);
		}
		
		//修改user		
		/*提取用户信息*/
		User user=userService.getByUserId(userId);
		//信息已经认证通过，或自动实名，不修改用户认证状态
		if(user.getVerifyFlag().longValue()!=1){
			user.setVerifyFlag(2);
		}
		user.setVerifyPhotoSign(2);
		user.setMtime(new Timestamp(System.currentTimeMillis()));
		/*更新用户验证信息到数据库*/
		userService.update(user);
		
		
		/*保存流水*/
		/*先把之前提交的置为无效*/
		userIdentityDao.updateDisabled(userId,0);		
		/*创建UserIdentity*/
		UserIdentity userIdentity;		
		userIdentity = createUserIdentity( userId ,identity , trueName, mainUrl);		
		userIdentityDao.insert(userIdentity);
		
		/*删除用户缓存信息*/
		cacheService.del(Constant.CACHE_USER_KEY+userIdentity.getUserId());		
		/*用户信息放缓存*/
		cacheService.setObject(Constant.CACHE_USER_KEY+user.getId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));
				
		return true;
	}
	
	@Override
	public boolean saveUserIdentityMain(Long userId,MultipartFile mainPic) throws Exception {
		TytUserIdentityMain tytUserIdentityMain=this.getTytUserIdentityMainForUserId(userId);
		//上传图片
		String mainUrl=renamePic(mainPic, "user");
		mainPic.transferTo(new File(AppConfig.getProperty("picture.path.domain")+mainUrl));
		Date nowDate=new Date();
		//保存或修改tytUserIdentityMain
		if(tytUserIdentityMain!=null){
			tytUserIdentityMain.setUpdateTime(nowDate);
			tytUserIdentityMain.setVerifyPhotoSign(2);
			/*数据库back_url保存用户>=2次上传的照片，每次覆盖；main_url只保存首次的照片，且不变*/
			if(tytUserIdentityMain.getMainUrl()==null||"".equals(tytUserIdentityMain.getMainUrl().trim())){
				tytUserIdentityMain.setMainUrl(mainUrl);
			}else{
				tytUserIdentityMain.setBackUrl(mainUrl);
			}
			tytUserIdentityMain.setPhotoTime(nowDate);
			this.getBaseDao().update(tytUserIdentityMain);
		}else{
			tytUserIdentityMain=new TytUserIdentityMain();
			tytUserIdentityMain.setUserId(userId);
			tytUserIdentityMain.setCreateTime(nowDate);
			tytUserIdentityMain.setUpdateTime(nowDate);
			tytUserIdentityMain.setVerifyInfoSign(0);
			tytUserIdentityMain.setVerifyPhotoSign(2);
			tytUserIdentityMain.setMainUrl(mainUrl);
			tytUserIdentityMain.setPhotoTime(nowDate);
			this.getBaseDao().insert(tytUserIdentityMain);
		}
		User user=userService.getByUserId(userId);
		/**往第二版身份认证表tyt_user_identity_auth，tyt_user_identity_auth_log添加数据*/
		TytUserIdentityAuth userIdentityAuth=userIdentityAuthService.saveIdentityAuthMove(userId,user.getCellPhone(), user.getIdentityType(), user.getUserClass(), user.getTrueName(), mainUrl, nowDate);
		//tytUserIdentityAuthBusiness.saveIdentityAuthMoveBusiness(userId,mainUrl, nowDate);
		/**修改user用户照片认证状态到数据库*/
		userService.updatePhotoVerifyFlag(userId, nowDate, 2,userIdentityAuth.getUserClass(),userIdentityAuth.getIdentityType());
		/*保存流水*/
		/*先把之前提交的置为无效*/
		userIdentityDao.updateDisabled(userId,0);		
		/*创建UserIdentity*/
		UserIdentity identity;		
		identity = createUserIdentity( userId ,null,null, mainUrl);		
		userIdentityDao.insert(identity);
		/*删除用户缓存信息*/
		cacheService.del(Constant.CACHE_USER_KEY+userId);		
		return true;
	}
	
	private UserIdentity createUserIdentity(Long userId ,String identity ,String trueName,String mainUrl){
		UserIdentity userIdentity=new UserIdentity();
		userIdentity.setIdentity((identity==null||"".equals(identity))?null:identity);
		userIdentity.setRealName((trueName==null||"".equals(trueName))?null:trueName);		
		userIdentity.setUserId(userId);
		userIdentity.setMainurl(mainUrl);
		userIdentity.setStatus("2");
		userIdentity.setCreateTime(new Date());
		userIdentity.setUpdateTime(new Date());
		userIdentity.setEnabled(1);
		return userIdentity;
	}
	
	/**
	 * 重命名图片
	 * @param pic待上传的图片
	 * @param typeName文件保存分目录名称
	 * @return
	 */
	protected String renamePic(MultipartFile pic,String typeName) {
//		String fileSeparator=System.getProperty("file.separator");//获取系统文件分隔符
		String domainurl="/data/pictures/"+typeName+"/";//获取文件路径
		CreateFileUtil.createDir(AppConfig.getProperty("picture.path.domain")+domainurl);
		return domainurl+ImageUtil.renameFile(pic.getOriginalFilename());
	}

	@Override
	public int saveUserIdentityMain(Long userId, String identity,
			String trueName) throws Exception {
		
		TytConfig tytConfig=tytConfigService.getValue("verifyNumber");
		int verifyNumber=Integer.parseInt(tytConfig.getValue());
		Object obj=(Object)cacheService.getObject(Constant.CACHE_VERIFY_NUMBER+userId+"_"+TimeUtil.formatDate(new Date()));
		int vn=obj==null?0:Integer.parseInt(String.valueOf(obj));
		//判断第三方认证发送条数
			if(vn<verifyNumber){
				//调用第三方认证
			IDCardAuthResult iDCardAuthResult=IDCardAuth.authIdentity(trueName, identity);
			//保存发送第三方认证条数
			cacheService.setObject(Constant.CACHE_VERIFY_NUMBER+userId+"_"+TimeUtil.formatDate(new Date()),(vn+1),Constant.CACHE_EXPIRE_TIME_24H);
			TytUserIdentityInfo userIdentityInfo=new TytUserIdentityInfo();
			userIdentityInfo.setCtime(new Date());
			userIdentityInfo.setIdCard(identity);
			userIdentityInfo.setResult(iDCardAuthResult.getResult());	
			userIdentityInfo.setTrueName(trueName);
			userIdentityInfo.setUserId(userId);
			userIdentityInfo.setUtime(new Date());
			if(iDCardAuthResult.getCode()==0)
			userIdentityInfo.setStatus(1);
			else
			userIdentityInfo.setStatus(3);			
			//保存流水
			userIdentityInfoService.add(userIdentityInfo);
			

			TytUserIdentityMain tytUserIdentityMain=this.getTytUserIdentityMainForUserId(userId);
			//保存或修改tytUserIdentityMain
			if(tytUserIdentityMain!=null){
				tytUserIdentityMain.setUpdateTime(new Date());
				tytUserIdentityMain.setIdentity(identity);
				tytUserIdentityMain.setRealName(trueName);
				tytUserIdentityMain.setInfoTime(new Date());
				if(iDCardAuthResult.getCode()==0)
					tytUserIdentityMain.setVerifyInfoSign(1);
				else
					tytUserIdentityMain.setVerifyInfoSign(3);
				if(tytUserIdentityMain.getVerifyPhotoSign()==null){
					tytUserIdentityMain.setVerifyPhotoSign(0);
				}
				
				tytUserIdentityMain.setInfoResult(iDCardAuthResult.getMsg());
				this.getBaseDao().update(tytUserIdentityMain);
				
			}else{
				tytUserIdentityMain=new TytUserIdentityMain();
				tytUserIdentityMain.setIdentity(identity);
				tytUserIdentityMain.setRealName(trueName);
				tytUserIdentityMain.setInfoTime(new Date());
				tytUserIdentityMain.setUserId(userId);
				tytUserIdentityMain.setCreateTime(new Date());
				tytUserIdentityMain.setUpdateTime(new Date());
				if(iDCardAuthResult.getCode()==0)
					tytUserIdentityMain.setVerifyInfoSign(1);
				else
					tytUserIdentityMain.setVerifyInfoSign(3);
				tytUserIdentityMain.setVerifyPhotoSign(0);
				tytUserIdentityMain.setInfoResult(iDCardAuthResult.getMsg());
				this.getBaseDao().insert(tytUserIdentityMain);
			}
			if(iDCardAuthResult.getCode()==0){
				//修改user		
				/*提取用户信息*/
//				User user=userService.getByUserId(userId);
				User user=userService.getById(userId);
				//信息已经认证通过，或自动实名，不修改用户认证状态
				if(user.getVerifyFlag().longValue()!=1){
					user.setVerifyFlag(1);
				}	
				//刷新缓存
				tytUserSubService.setUserSendGoodsLimitNumber(userId);
				//删除 子表缓存
				cacheService.del(Constant.CACHE_USERSUB_KEY + userId.longValue()
						+ "_" + TimeUtil.formatDateMonthTime(new Date()));
				user.setTrueName(trueName);
				user.setIdCard(identity);
				//user.setInfoPublishFlag(User.INFO_PUBLISH_ENABLE);
				
				///取得姓名和性别
				String userName=user.getUserName();
				String sex=IdCardUtil.getGender(tytUserIdentityMain.getIdentity());
				if(!StringUtils.hasLength(userName)){
					userName=tytUserIdentityMain.getRealName().substring(0, 1)+(sex.equals("0")?"女士":"先生");
					user.setUserName(userName);
				}		
				user.setSex(sex);
				
				user.setMtime(new Timestamp(System.currentTimeMillis()));
				/*更新用户验证信息到数据库*/
				userService.update(user);				
				/*删除用户缓存信息*/
				cacheService.del(Constant.CACHE_USER_KEY+userId);		
				/*用户信息放缓存*/
				cacheService.setObject(Constant.CACHE_USER_KEY+user.getId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));
				return 0;
			}else{			
				if(iDCardAuthResult.getCode()==101){
					return 101;
				}else if(iDCardAuthResult.getCode()==102){
					return 102;
					}else return 300;						
			}
		
		}else 
			return 400;
	}

	@Override
	public boolean saveUserIdentityMain(Long userId,String trueName,String idCard,String mainUrl) {
		TytUserIdentityMain tytUserIdentityMain=this.getTytUserIdentityMainForUserId(userId);
		
		//String mainUrl=renamePic(mainPic, "user");
		//mainPic.transferTo(new File(AppConfig.getProperty("picture.path.domain")+mainUrl));
		//保存或修改tytUserIdentityMain
		if(tytUserIdentityMain!=null){
			tytUserIdentityMain.setIdentity(idCard);
			tytUserIdentityMain.setRealName(trueName);
			tytUserIdentityMain.setUpdateTime(new Date());
			tytUserIdentityMain.setVerifyPhotoSign(2);
			/*数据库back_url保存用户>=2次上传的照片，每次覆盖；main_url只保存首次的照片，且不变*/
			if(tytUserIdentityMain.getMainUrl()==null||"".equals(tytUserIdentityMain.getMainUrl().trim())){
				tytUserIdentityMain.setMainUrl(mainUrl);
			}else{
				tytUserIdentityMain.setBackUrl(mainUrl);
			}
			tytUserIdentityMain.setPhotoTime(new Date());
			this.getBaseDao().update(tytUserIdentityMain);
		}else{
			tytUserIdentityMain=new TytUserIdentityMain();
			tytUserIdentityMain.setIdentity(idCard);
			tytUserIdentityMain.setRealName(trueName);
			tytUserIdentityMain.setUserId(userId);
			tytUserIdentityMain.setCreateTime(new Date());
			tytUserIdentityMain.setUpdateTime(new Date());
			tytUserIdentityMain.setVerifyInfoSign(0);
			tytUserIdentityMain.setVerifyPhotoSign(2);
			tytUserIdentityMain.setMainUrl(mainUrl);
			tytUserIdentityMain.setPhotoTime(new Date());
			this.getBaseDao().insert(tytUserIdentityMain);
		}
		
		//修改user		
		/*提取用户信息*/
		//User user=userService.getByUserId(userId);
		//信息已经认证通过，或自动实名，不修改用户认证状态
		//if(user.getVerifyFlag().longValue()!=1){
			//user.setVerifyFlag(2);
		//}
		//user.setVerifyPhotoSign(2);
		//user.setMtime(new Timestamp(System.currentTimeMillis()));
		/*更新用户验证信息到数据库*/
		//userService.update(user);
		
		
		/*保存流水*/
		/*先把之前提交的置为无效*/
		userIdentityDao.updateDisabled(userId,0);		
		/*创建UserIdentity*/
		UserIdentity identity;		
		identity = createUserIdentity( userId ,idCard,trueName, mainUrl);		
		userIdentityDao.insert(identity);
		
		/*删除用户缓存信息*/
		//cacheService.del(Constant.CACHE_USER_KEY+identity.getUserId());		
		/*用户信息放缓存*/
		//cacheService.setObject(Constant.CACHE_USER_KEY+user.getId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));
				
		return true;
	}
	
}
