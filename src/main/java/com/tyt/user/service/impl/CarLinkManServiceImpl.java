package com.tyt.user.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.CarLinkMan;
import com.tyt.user.service.CarLinkManService;
@Service("carLinkManService")
public class CarLinkManServiceImpl extends BaseServiceImpl<CarLinkMan,Long> implements CarLinkManService{

	@Resource(name="carLinkManDao")
	public void setBaseDao(BaseDao<CarLinkMan, Long> CarLinkManDao) {
	        super.setBaseDao(CarLinkManDao);
	}

	
}
