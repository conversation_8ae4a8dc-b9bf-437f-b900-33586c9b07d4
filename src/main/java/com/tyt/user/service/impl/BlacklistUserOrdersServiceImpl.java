package com.tyt.user.service.impl;

import com.tyt.plat.entity.base.BlacklistUserOrders;
import com.tyt.plat.mapper.base.BlackListUserOrdersLogMapper;
import com.tyt.plat.mapper.base.BlackListUserOrdersMapper;
import com.tyt.user.service.BlacklistUserOrdersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service("blacklistUserOrdersService")
public class BlacklistUserOrdersServiceImpl implements BlacklistUserOrdersService {

	@Autowired
	private BlackListUserOrdersMapper blackListUserOrdersMapper;
	@Autowired
	private BlackListUserOrdersLogMapper blackListUserOrdersLogMapper;

	@Override
	public List<BlacklistUserOrders> getBlackByUserId(Long userId) {
		return blackListUserOrdersMapper.getBlackByUserId(userId);
	}


	@Override
	public BlacklistUserOrders getOrdersByUserIdAndRestrictTime(Long userId, Date time) {
        return blackListUserOrdersMapper.getOrdersByUserIdAndRestrictTime(userId, time);
	}

	/**
	 * 查询限制发货的处罚订单
	 * @param userId
	 * @param time
	 * @return
	 */
	@Override
	public BlacklistUserOrders getGoodsLimitByUserIdAndTime(Long userId, Date time) {
		return blackListUserOrdersMapper.getGoodsLimitByUserIdAndTime(userId, time);
	}
}
