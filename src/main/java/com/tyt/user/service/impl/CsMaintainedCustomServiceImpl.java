package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.*;
import com.tyt.model.CsMaintainedCustom;
import com.tyt.plat.mapper.base.CsMaintainedCustomMapper;
import com.tyt.user.service.CsMaintainedCustomService;
import com.tyt.user.service.CsPointCityService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

@Service("csMaintainedCustomService")
public class CsMaintainedCustomServiceImpl extends BaseServiceImpl<CsMaintainedCustom, Long> implements CsMaintainedCustomService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CsMaintainedCustomMapper csMaintainedCustomMapper;

    @Resource(name = "userService")
    private UserService userService;

    @Autowired
    private CsPointCityService csPointCityService;

    @Resource(name = "csMaintainedCustomDao")
    public void setBaseDao(BaseDao<CsMaintainedCustom, Long> csMaintainedCustomDao) {
        super.setBaseDao(csMaintainedCustomDao);
    }


    @Override
    public HashMap<String, Object> getEmpowerStatus(Long userId) {
        CsMaintainedCustom csMaintainedCustom = csMaintainedCustomMapper.getCsMaintainedCustomByUserid(userId);
        if (null != csMaintainedCustom) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("empowerStatus", csMaintainedCustom.getEmpowerStatus());
            return map;
        } else {
            HashMap<String, Object> map = new HashMap<>();
            map.put("empowerStatus", 1);
            return map;
        }
    }

    @Override
    public ResultMsgBean saveEmpowerStatus(Long userId, ResultMsgBean resultMsgBean) {
        try {
            User byUserId = userService.getByUserId(userId);
            Integer verifyPhotoSign = byUserId.getVerifyPhotoSign();
            if (verifyPhotoSign != 1) {
                resultMsgBean.setCode(411);
                resultMsgBean.setMsg("用户未认证");
                return resultMsgBean;
            }

            CsMaintainedCustom csMaintainedCustom = csMaintainedCustomMapper.getCsMaintainedCustomByUserid(userId);
            if (null == csMaintainedCustom) {
                resultMsgBean.setCode(412);
                resultMsgBean.setMsg("查询失败");
                return resultMsgBean;
            }
            csMaintainedCustom.setEmpowerStatus((short) 3);
            csMaintainedCustom.setUtime(new Date());
            int i = csMaintainedCustomMapper.updateByPrimaryKeySelective(csMaintainedCustom);


            if (i == 0) {
                resultMsgBean.setCode(410);
                resultMsgBean.setMsg("修改失败");
                return resultMsgBean;
            }
            return resultMsgBean;

        } catch (Exception e) {
            logger.error("个人货主用户授权失败，失败原因:" + e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "授权失败");
        }
    }

    @Override
    public CsMaintainedCustom getCsMaintainedCustomByUserId(Long userId) {
        return csMaintainedCustomMapper.getCsMaintainedCustomByUserid(userId);

    }

    /**
     * 保存用户维护数据
     *
     * @param user
     * @param internalEmployee
     * @return
     */
    @Override
    public CsMaintainedCustom saveCustom(User user, TytInternalEmployee internalEmployee) {
        int flag = 1;
        CsMaintainedCustom custom = new CsMaintainedCustom();
        if (!checkCustomByCustomId(user.getId())) {
            custom.setIsNeedShow((short) 2);
            if (user.getUserClass() != null && user.getUserClass() == 1) {
                custom.setBelongTo((short) 2);
            } else if (user.getUserClass() != null && user.getUserClass() == 2) {
                custom.setBelongTo((short) 1);
            } else if (user.getUserClass() != null && user.getUserClass() == 3) {
                custom.setBelongTo((short) 3);
            } else {
                if (user.getPlatId() != null && (user.getPlatId() == 21 || user.getPlatId() == 31)) {
                    custom.setBelongTo((short) 1);
                } else if (user.getPlatId() != null && (user.getPlatId() == 22 || user.getPlatId() == 32)) {
                    custom.setBelongTo((short) 2);
                } else {
                    if (flag % 2 > 0) {
                        custom.setBelongTo((short) 1);
                    } else {
                        custom.setBelongTo((short) 2);
                    }
                    flag++;
                }
            }
            custom.setCustomId(user.getId());
            //根据手机号查找下发数据
            CsMaintainedCustom getByPhone = getCustomByPhone(user.getCellPhone());
            CsNewCustom newCustom = newCustomIsExit(user.getCellPhone());
            if (getByPhone != null) {
                if (getByPhone.getMaintainerId() != null) {
                    custom.setMaintainerId(getByPhone.getMaintainerId());
                    custom.setMaintainerName(getByPhone.getMaintainerName());
                    custom.setIsNeedShow((short) 1);
                }
                if (getByPhone.getGoodsMaintainerId() != null) {
                    custom.setGoodsMaintainerId(getByPhone.getGoodsMaintainerId());
                    custom.setGoodsMaintainerName(getByPhone.getGoodsMaintainerName());
                    custom.setIsNeedShow((short) 1);
                }
            } else {
                if (newCustom != null) {
                    CsBusunessUserBind userBind = getUserBind(newCustom.getModifyId());
                    if (userBind != null) {
                        if (userBind.getRoleId() == 18 || userBind.getRoleId() == 21) {
                            custom.setMaintainerId(newCustom.getModifyId());
                            custom.setMaintainerName(newCustom.getModifyName());
                            custom.setIsNeedShow((short) 1);
                        }
                        if (userBind.getRoleId() == 19 || userBind.getRoleId() == 23) {
                            custom.setGoodsMaintainerId(newCustom.getModifyId());
                            custom.setGoodsMaintainerName(newCustom.getModifyName());
                            custom.setIsNeedShow((short) 1);
                        }
                    }
                }
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(user.getCity())) {
                CsPointCity pointCity = csPointCityService.getPointCityByCity(user.getCity());
                if (pointCity != null) {
                    custom.setAreaType(pointCity.getType().shortValue());
                } else {
                    custom.setAreaType((short) 3);
                }
            } else {
                custom.setAreaType((short) 3);
            }
            custom.setIsNeedDefender((short) 2);
            custom.setIsNeedMove((short) 2);
            custom.setIsNeedForceShow((short) 2);
            custom.setStatus((short) 1);
            custom.setModifyName("系统");
            custom.setCustomPhone(user.getCellPhone());
            custom.setCtime(new Date());
            custom.setDispatcherId(internalEmployee.getId());
            custom.setDispatcherName(internalEmployee.getName());
            custom.setBindingDispatchTime(new Date());
            custom.setEmpowerStatus((short) 3);
            custom.setGoodsSync(0);
            this.getBaseDao().insert(custom);
            //更新新的客户列表已注册状态
            if (newCustom != null) {
                String updateSql = "UPDATE `cs_new_custom` SET `register_status`=1,utime=? WHERE `custom_phone`=?";
                this.getBaseDao().executeUpdateSql(updateSql, new Object[]{new Date(), user.getCellPhone()});
            }
        }
        return custom;
    }

    @Override
    public CsMaintainedCustom updateCustom(TytInternalEmployee internalEmployee, CsMaintainedCustom csMaintainedCustom) {
        if (Objects.isNull(csMaintainedCustom.getDispatcherId())){
            csMaintainedCustom.setDispatcherId(internalEmployee.getId());
            csMaintainedCustom.setDispatcherName(internalEmployee.getName());
            csMaintainedCustom.setBindingDispatchTime(new Date());
        }
        csMaintainedCustom.setEmpowerStatus((short) 3);
        if (csMaintainedCustom.getGoodsSync() == null) {
            csMaintainedCustom.setGoodsSync(0);
        }
        this.getBaseDao().update(csMaintainedCustom);
        return csMaintainedCustom;
    }

    private boolean checkCustomByCustomId(Long customId) {
        String sql = "SELECT COUNT(*) FROM `cs_maintained_custom` WHERE custom_id =?";
        BigInteger c = this.getBaseDao().query(sql, new Object[]{customId});
        if (c != null && c.intValue() > 0) {
            return true;
        }
        return false;
    }


    private CsMaintainedCustom getCustomByPhone(String customPhone) {
        if (!org.apache.commons.lang.StringUtils.contains(customPhone, "(已注销)")) {
            customPhone += "(已注销)";
        }
        String sql = "SELECT * FROM cs_maintained_custom WHERE custom_phone=? ORDER BY id DESC";
        List<CsMaintainedCustom> list = this.getBaseDao().search(sql, new Object[]{customPhone}, 0, 1);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    private CsNewCustom newCustomIsExit(String cellPhone) {
        String sql = "SELECT `id` id,`modify_id` modifyId,`modify_name` modifyName,belong_to belongTo FROM `cs_new_custom` WHERE custom_phone=:customPhone AND register_status=2";
        Map<String, Type> map = new HashMap<String, Type>();
        map.put("id", Hibernate.LONG);
        map.put("modifyId", Hibernate.LONG);
        map.put("modifyName", Hibernate.STRING);
        map.put("belongTo", Hibernate.INTEGER);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("customPhone", cellPhone);
        List<CsNewCustom> list = this.getBaseDao().search(sql, map, CsNewCustom.class, paramMap);
        if (list != null && list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    private CsBusunessUserBind getUserBind(Long id) {
        String sql = "SELECT `id` id,`name` name,`login_phone_no` loginPhoneNo,`real_name` realName,role_id roleId FROM `cs_business_user_bind` WHERE id=:id AND is_valid=1";
        Map<String, Type> map = new HashMap<String, Type>();
        map.put("id", Hibernate.LONG);
        map.put("name", Hibernate.STRING);
        map.put("loginPhoneNo", Hibernate.STRING);
        map.put("realName", Hibernate.STRING);
        map.put("roleId", Hibernate.LONG);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("id", id);
        List<CsBusunessUserBind> list = this.getBaseDao().search(sql, map, CsBusunessUserBind.class, paramMap);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


}
