package com.tyt.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.service.PlatUserInviteAwardInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.message.bean.MqPushAcvitityMessage;
import com.tyt.model.Advice;
import com.tyt.model.PlatUserInviteAwardInfo;
import com.tyt.model.User;
import com.tyt.user.bean.MqUserAcvitityMessage;
import com.tyt.user.dao.AdviceDao;
import com.tyt.user.service.AdviceService;
import com.tyt.user.service.LoginActivityService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service("loginActivityService")
public class LoginActivityServiceImpl implements LoginActivityService {

	@Resource(name = "platUserInviteAwardInfoService")
	PlatUserInviteAwardInfoService platUserInviteAwardInfoService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;
	@Resource(name = "userService")
	private UserService userService;
	@Override
	public void excuteActivity(User user) {
		//目前只有拉新活动  此次活动时间 20180801--20180821
		if(user.getChannel() != null && user.getChannel().intValue() == 5209){
			PlatUserInviteAwardInfo platUserInviteAwardInfo = new PlatUserInviteAwardInfo();
			platUserInviteAwardInfo.setInviteUserId(user.getId());
			platUserInviteAwardInfo.setFirstMsgState(1);
			platUserInviteAwardInfo = this.platUserInviteAwardInfoService.find(platUserInviteAwardInfo);
			if(platUserInviteAwardInfo == null){
				//不存在该用户或者已经发送过
				return;
			}
			platUserInviteAwardInfo.setMtime(new Date());
			platUserInviteAwardInfo.setFirstMsgState(2);
			this.platUserInviteAwardInfoService.update(platUserInviteAwardInfo);
			//发送用户引导推送MQ消息
			sendPushMessageMQ(user,platUserInviteAwardInfo.getUserId());
		}
	}

	@Override
	public void sendActivityMq(Long userId,String userVersion) {
		try {
			Integer version = tytConfigService.getIntValue("user_coupon_version", 5910);
			if(Integer.valueOf(userVersion)>=version){
				//开关配置。是否发放优惠券
				boolean value = this.tytConfigService.isEqualsOne("login_coupon_switch");
				if(value){
					MqUserAcvitityMessage message=new MqUserAcvitityMessage();
					message.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
					message.setMessageType(MqBaseMessageBean.USER_ACVITITY_MESSAGE);
					message.setUserId(userId);
					message.setClientVersion(userVersion);
					// 保存mq信息到数据库
					tytMqMessageService.addSaveMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message), message.getMessageType());
					tytMqMessageService.sendMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message), message.getMessageType());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	//TODO 发送用户引导推送MQ消息
	private void sendPushMessageMQ(User user,Long curUserId){
		String time = tytConfigService.getStringValue("acvitity_end_time","--");
		if(!time.equals("--")){
			time = "（"+ TimeUtil.parseStringToDateTime(time,"yyyy-MM-dd HH:mm:ss","M月d日")+"）前";
		}else{
			time = "";
		}
		try {
			User curUser = userService.getByUserId(curUserId);
		String msg = "特运通和您的朋友"+curUser.getUserName()+"欢迎您的到来！活动期间"+time+"付费可免费延长会员期哟！";
		MqPushAcvitityMessage mqUserMsg = new MqPushAcvitityMessage();

		mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
		mqUserMsg.setMessageType(MqBaseMessageBean.PUSH_ACVITITY_MESSAGE);
		mqUserMsg.setUserId(user.getId());
		mqUserMsg.setCellPhone(user.getCellPhone());
		mqUserMsg.setTrueName(user.getTrueName());
		mqUserMsg.setRemarks("三周年活动通知");//说明
		mqUserMsg.setContent(msg);//内容
		mqUserMsg.setSummary("特运通三周年，活动好礼免费送");//摘要
		mqUserMsg.setTitle("欢迎登录特运通");//标题
		// 保存mq信息到数据库
		tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

		// 发送3周年庆典活动push
		tytMqMessageService.sendMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
