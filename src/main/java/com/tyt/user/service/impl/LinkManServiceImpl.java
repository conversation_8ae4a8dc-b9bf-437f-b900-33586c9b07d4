package com.tyt.user.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.LinkMan;
import com.tyt.user.service.LinkManService;
@Service("linkManService")
public class LinkManServiceImpl extends BaseServiceImpl<LinkMan,Long> implements LinkManService{

	@Resource(name="linkManDao")
	public void setBaseDao(BaseDao<LinkMan, Long> linkManDao) {
	        super.setBaseDao(linkManDao);
	}
}
