package com.tyt.user.service.impl;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCallPhoneLimit;
import com.tyt.user.service.TytCallPhoneLimitService;
import com.tyt.util.Constant;

@Service("tytCallPhoneLimitService")
public class TytCallPhoneLimitServiceImpl extends BaseServiceImpl<TytCallPhoneLimit, Long> implements TytCallPhoneLimitService {

	@Resource(name = "tytCallPhoneLimitDao")
	public void setBaseDao(BaseDao<TytCallPhoneLimit, Long> tytCallPhoneLimitDao) {
		super.setBaseDao(tytCallPhoneLimitDao);
	}

	@Override
	public TytCallPhoneLimit getByUserIdentity(Integer userClass, Integer identityType) {
		TytCallPhoneLimit callPhoneLimitResult = null;
		String hql = "from TytCallPhoneLimit where userClass=? and identityType=?";
		List<TytCallPhoneLimit> callPhoneLimits = this.getBaseDao().find(hql, userClass.shortValue(), identityType.shortValue());
		if (callPhoneLimits.size() == 1) {
			callPhoneLimitResult = callPhoneLimits.get(0);
		} else {
			callPhoneLimits = this.getBaseDao().find(hql, Constant.USER_CLASS_DEFUALT_VALUE, Constant.IDENTITY_TYPE_DEFAULT_VALUE);
			callPhoneLimitResult = (callPhoneLimits.size() == 1 ? callPhoneLimits.get(0) : null);
		}
		return callPhoneLimitResult;
	}
}
