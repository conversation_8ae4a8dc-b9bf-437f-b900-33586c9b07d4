package com.tyt.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.message.bean.SendDialTimesMsgBean;
import com.tyt.model.*;
import com.tyt.permission.bean.PermissionChangeType;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.TytCarOwnerAuth;
import com.tyt.plat.entity.base.TytCarOwnerAuthProof;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.mapper.base.TytCarOwnerAuthMapper;
import com.tyt.plat.mapper.base.TytCarOwnerAuthProofMapper;
import com.tyt.plat.mapper.base.TytInvoiceEnterpriseMapper;
import com.tyt.user.bean.CarOwnerAuthReq;
import com.tyt.user.bean.CarOwnerAuthVO;
import com.tyt.user.service.*;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/02/01 13:54
 */
@Service("carOwnerAuthService")
@Slf4j
public class CarOwnerAuthServiceImpl implements CarOwnerAuthService {
    @Autowired
    private TytCarOwnerAuthMapper tytCarOwnerAuthMapper;
    @Autowired
    private TytCarOwnerAuthProofMapper tytCarOwnerAuthProofMapper;
    @Autowired
    private CarService carService;
    @Autowired
    private UserService userService;
    @Autowired
    private TytConfigService tytConfigService;
    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityService;

    @Autowired
    private UserBuyGoodsService userBuyGoodsService;
    @Autowired
    private UserPermissionService userPermissionService;
    @Autowired
    private TytMqMessageService tytMqMessageService;

    @Override
    public CarOwnerAuthVO getCarOwnerInfo(Long userId) {
        List<TytCarOwnerAuth> tytCarOwnerAuths = tytCarOwnerAuthMapper.selectByUserIdAuthStatus(userId, 1);
        //首次认证成功是否放发权益开关 0 关 1 开
        Integer carOwnerAuthGivePermission = tytConfigService.getIntValue("car_owner_auth_give_permission", 0);
        if (CollectionUtils.isEmpty(tytCarOwnerAuths)) {
            return new CarOwnerAuthVO(carOwnerAuthGivePermission == 1 ? 0 : 1, null);
        }
        List<Long> carIds = tytCarOwnerAuthMapper.selectTytCarIds(userId, 1);
        List<Long> carOwnerCarIds = tytCarOwnerAuthMapper.selectCarIds(userId);
        if(CollectionUtils.isEmpty(carOwnerCarIds)){
            return new CarOwnerAuthVO(carOwnerAuthGivePermission == 1 ? 0 : 1, null);
        }
        if (carOwnerCarIds.containsAll(carIds)) {
            return new CarOwnerAuthVO(2, null);
        }
        return new CarOwnerAuthVO(1, null);
    }

    @Override
    public Integer isQuickCommit(Long userId, Long carId) throws Exception {
        Car car = carService.newGetbyId(carId);
        if (Objects.isNull(car) || StringUtils.isBlank(car.getHeadDrivingUrl()) || StringUtils.isBlank(car.getHeadNo()) && StringUtils.isBlank(car.getHeadName())) {
            log.info("车主认证-判断是否符合一键提交,车辆信息不全,参数为:userId={},carId={}", userId, carId);
            return 0;
        }

        TytUserIdentityAuth identityAuth = userIdentityService.getTytUserIdentityAuth(userId);
        //如果实名认证成功判断真实姓名与车辆认证姓名是否相同
        if (Objects.nonNull(identityAuth) && identityAuth.getIdentityStatus().equals(1)) {
            User user = userService.getByUserId(userId);
            if (Objects.nonNull(user) && StringUtils.isNotBlank(user.getTrueName()) && user.getTrueName().equals(car.getHeadName())) {
                log.info("车主认证-判断是否符合一键提交,实名认证校验通过,参数为:userId={},carId={}", userId, carId);
                return 1;
            }
        }

        TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserId(userId, 2);
        if (Objects.nonNull(tytInvoiceEnterprise) && StringUtils.isNotBlank(tytInvoiceEnterprise.getEnterpriseName()) && car.getHeadName().equals(tytInvoiceEnterprise.getEnterpriseName())) {
            log.info("车主认证-判断是否符合一键提交,企业认证校验通过,参数为:userId={},carId={}", userId, carId);
            return 2;
        }

        return 0;
    }

    @Override
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public void carOwnerAuthSave(CarOwnerAuthReq carOwnerAuthReq) {
        TytCarOwnerAuth tytCarOwnerAuth = new TytCarOwnerAuth();
        BeanUtils.copyProperties(carOwnerAuthReq, tytCarOwnerAuth);
        tytCarOwnerAuth.setCreateTime(new Date());
        tytCarOwnerAuth.setAuthStatus(0);
        tytCarOwnerAuth.setCarIdentity(this.isEnterprise(carOwnerAuthReq.getHeadName()));
        tytCarOwnerAuthMapper.insertSelective(tytCarOwnerAuth);
        String certificateMaterial = carOwnerAuthReq.getCertificateMaterial();
        if (StringUtils.isNotBlank(certificateMaterial)) {
            String[] split = certificateMaterial.split(",");
            for (String s : split) {
                TytCarOwnerAuthProof tytCarOwnerAuthProof = new TytCarOwnerAuthProof();
                tytCarOwnerAuthProof.setCarOwnerAuthId(tytCarOwnerAuth.getId());
                tytCarOwnerAuthProof.setCertificateMaterialUrl(s);
                tytCarOwnerAuthProof.setCreateTime(new Date());
                tytCarOwnerAuthProofMapper.insertSelective(tytCarOwnerAuthProof);
            }
        }
    }

    @Override
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public ResultMsgBean quickCommit(Long userId, Long carId) throws Exception {
        Integer quickCommit = isQuickCommit(userId, carId);
        if (quickCommit == 0) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.OTHER_ERROR, "不符合一键提交条件");
        }
        Car car = carService.newGetbyId(carId);

        TytCarOwnerAuth tytCarOwnerAuth = new TytCarOwnerAuth();
        tytCarOwnerAuth.setCarId(carId);
        tytCarOwnerAuth.setUserId(userId);
        tytCarOwnerAuth.setHeadNo(car.getHeadNo());
        tytCarOwnerAuth.setHeadName(car.getHeadName());
        tytCarOwnerAuth.setCarRelation(0);
        tytCarOwnerAuth.setAuthStatus(1);
        tytCarOwnerAuth.setCreateTime(new Date());
        //实名认证与车主姓名相同
        if (quickCommit == 1) {
            User user = userService.getByUserId(userId);
            tytCarOwnerAuth.setCarIdentity(1);
            tytCarOwnerAuth.setCarOwnerIdNumber(user.getIdCard());
        }

        //企业认证与车主姓名相同
        if (quickCommit == 2) {
            TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserId(userId, 2);
            tytCarOwnerAuth.setCarIdentity(0);
            tytCarOwnerAuth.setCarOwnerIdNumber(tytInvoiceEnterprise.getEnterpriseCreditCode());
        }

        List<TytCarOwnerAuth> tytCarOwnerAuths = tytCarOwnerAuthMapper.selectByUserIdAuthStatus(userId, 1);

        tytCarOwnerAuthMapper.insertSelective(tytCarOwnerAuth);

        //首次认证成功是否放发权益开关 0 关 1 开
        Integer carOwnerAuthGivePermission = tytConfigService.getIntValue("car_owner_auth_give_permission", 0);
        if (CollectionUtils.isEmpty(tytCarOwnerAuths) && carOwnerAuthGivePermission == 1) {
            int carOwnerAuthGiveGoodsId = tytConfigService.getIntValue("car_owner_auth_give_goods_id");
            TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, (long) carOwnerAuthGiveGoodsId, "carOwnerGive", 2);
            userPermissionService.sendUserPermission2MQ(userId, (long) carOwnerAuthGiveGoodsId, PermissionChangeType.赠送.getId(), userBuyGoods, null);
        }
        //发送抢单豆
        SendDialTimesMsgBean sendDialTimesMsgBean = new SendDialTimesMsgBean();
        sendDialTimesMsgBean.setUserId(userId);
        sendDialTimesMsgBean.setSendType(SendDialTimesMsgBean.DialTimesSendType.CAR_USER_LEVEL);
        sendDialTimesMsgBean.setMessageType(MqBaseMessageBean.SEND_DIAL_TIMES);
        sendDialTimesMsgBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        // 保存mq信息到数据库
//        tytMqMessageService.addSaveMqMessage(sendDialTimesMsgBean.getMessageSerailNum(), JSON.toJSONString(sendDialTimesMsgBean), sendDialTimesMsgBean.getMessageType());
//        tytMqMessageService.sendMqMessageDirect(sendDialTimesMsgBean.getMessageSerailNum(), JSON.toJSONString(sendDialTimesMsgBean), 1000L);
        tytMqMessageService.sendMsgCustom(JSON.toJSONString(sendDialTimesMsgBean), "MARKET_CENTER_TOPIC", sendDialTimesMsgBean.getMessageSerailNum(), "DIAL", 0L);
        return ResultMsgBean.successResponse();
    }

    @Override
    public TytCarOwnerAuth getByCarId(Long carId) {
        return tytCarOwnerAuthMapper.selectByCarId(carId);
    }

    @Override
    public TytCarOwnerAuth selectByCarIdAndAuth(Long carId) {
        return tytCarOwnerAuthMapper.selectByCarIdAndAuth(carId);
    }

    @Override
    public Integer isEnterprise(String content) {
        return content.length() > 5 && content.contains("公司") ? 0 : 1;
    }
}
