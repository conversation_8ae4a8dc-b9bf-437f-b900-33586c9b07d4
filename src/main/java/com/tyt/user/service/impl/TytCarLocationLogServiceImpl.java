package com.tyt.user.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCarLocationLog;
import com.tyt.user.bean.CarLocationRecordBean;
import com.tyt.user.dao.TytCarLocationLogDao;
import com.tyt.user.service.TytCarLocationLogService;

@Service("tytCarLocationLogService")
public class TytCarLocationLogServiceImpl extends BaseServiceImpl<TytCarLocationLog, Long> implements TytCarLocationLogService {
	@Resource(name="tytCarLocationLogDao")
	public void setBaseDao(BaseDao<TytCarLocationLog, Long> tytCarLocationLogDao) {
	        super.setBaseDao(tytCarLocationLogDao);
	}

	@Override
	public List<CarLocationRecordBean> getList(Long carId ,int pageSize) {
		List<CarLocationRecordBean> list = ((TytCarLocationLogDao)this.getBaseDao()).getListByCarId(carId,pageSize);
		return list;
	}
}
