package com.tyt.user.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.model.*;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.plat.entity.base.BlacklistUserOrders;
import com.tyt.transport.service.TransportService;
import com.tyt.user.service.BlacklistUserOrdersService;
import com.tyt.user.service.BlacklistUserService;
import com.tyt.util.Jdk8DateTimeUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service("blacklistUserService")
@Slf4j
public class BlacklistUserServiceImpl extends BaseServiceImpl<BlacklistUser, Long> implements BlacklistUserService {
    @Autowired
    private TytNoticePopupTemplService tytNoticePopupTemplService;

    @Autowired
    private BlacklistUserOrdersService blacklistUserOrdersService;

    @Autowired
    private TransportWayBillExService transportWayBillExService;

    @Autowired
    private TransportService transportService;

    @Resource(name = "blacklistUserDao")
    public void setBaseDao(BaseDao<BlacklistUser, Long> blacklistUserDao) {
        super.setBaseDao(blacklistUserDao);
    }


    @Override
    public BlacklistUser getBlackByUserId(Long userId) {
        String sql = "SELECT * FROM blacklist_user WHERE user_id=?";
        List<BlacklistUser> list = this.getBaseDao().queryForList(sql, new Object[]{userId});
        if (list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public BlacklistUser getBlackByUserIdAndStatus(Long userId, Integer status) {
        String sql = "SELECT * FROM blacklist_user WHERE user_id=? and status=? order by id desc";
        List<BlacklistUser> list = this.getBaseDao().queryForList(sql, new Object[]{userId, status});
        if (list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public ResultMsgBean checkLimitPublish(Long userId) {
        ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");
        BlacklistUser blacklistUser = this.getBlackByUserId(userId);
        if (Objects.isNull(blacklistUser)) {
            return msgBean;
        }
        //用户手机号
        String cellPhone = blacklistUser.getCellPhone();
        //发货长期被限制
        if (Objects.nonNull(blacklistUser.getLimitMinutes()) && blacklistUser.getLimitMinutes() == -1) {
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setMsg(null);
            msgBean.setData("发货受限");

            // TytNoticePopupTempl popupTempl = tytNoticePopupTemplService.getByType(800, 6);
            // //之所以从新copy出一个新对象是因为在向数据库对象set值时 hibernate 会自动执行update语句
            // TytNoticePopupTempl popupTemplNew = new TytNoticePopupTempl();
            // BeanUtils.copyProperties(popupTempl,popupTemplNew);
            // String format = String.format(popupTemplNew.getMasterContent(), blacklistUser.getCellPhone());
            // popupTemplNew.setMasterContent(format);
            // msgBean.setNoticeData(popupTemplNew);
            // return msgBean;

            //查询用户是否存在处罚订单
            BlacklistUserOrders blacklistUserOrders = blacklistUserOrdersService.getGoodsLimitByUserIdAndTime(userId, new Date());
            if(blacklistUserOrders != null){
                Long waybillExId = blacklistUserOrders.getWaybillExId();
                //查询异常上报订单信息
                TytTransportWaybillEx transportWaybillEx = transportWayBillExService.getExById(waybillExId);
                if(transportWaybillEx != null){
                    //运单号
                    String tsOrderNo = transportWaybillEx.getTsOrderNo();
                    //出发地(省市区以减号-分割开)
                    String startPoint = transportWaybillEx.getStartPoint();
                    //目的地(省市区以减号-分割开)
                    String destPoint = transportWaybillEx.getDestPoint();
                    //货物内容
                    String taskContent = transportWaybillEx.getTaskContent();
                    if(StringUtils.isNotBlank(taskContent) && taskContent.length() > 6){
                        taskContent = taskContent.substring(0,6) + "···";
                    }
                    //替换参数,逗号分隔
                    String masterParams = cellPhone + "," + tsOrderNo + "," + startPoint + "," + destPoint + "," + taskContent;

                    TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号权益限制提醒_长期_有订单, masterParams);
                    msgBean.setNoticeData(noticePopupTempl);
                    return msgBean;
                }
            }else{
                //替换参数,逗号分隔
                String masterParams = cellPhone;

                TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号权益限制提醒_长期_无订单, masterParams);
                msgBean.setNoticeData(noticePopupTempl);
                return msgBean;
            }
        }
        // 发货某时间段被限制
        if (Objects.nonNull(blacklistUser.getLimitEndTime()) && blacklistUser.getLimitEndTime().after(new Date())) {
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setMsg(null);
            msgBean.setData("发货受限");

            // TytNoticePopupTempl popupTempl = tytNoticePopupTemplService.getByType(800, 5);
            // //之所以从新copy出一个新对象是因为在向数据库对象set值时 hibernate 会自动执行update语句
            // TytNoticePopupTempl popupTemplNew = new TytNoticePopupTempl();
            // BeanUtils.copyProperties(popupTempl,popupTemplNew);
            // String format = String.format(popupTemplNew.getMasterContent(), TimeUtil.formatDateTime(blacklistUser.getLimitStartTime()), TimeUtil.formatDateTime(blacklistUser.getLimitEndTime()));
            // popupTemplNew.setMasterContent(format);
            // msgBean.setNoticeData(popupTemplNew);
            // return msgBean;

            //查询用户是否存在处罚订单
            BlacklistUserOrders blacklistUserOrders = blacklistUserOrdersService.getGoodsLimitByUserIdAndTime(userId, new Date());
            if(blacklistUserOrders != null){
                Long waybillExId = blacklistUserOrders.getWaybillExId();
                //查询异常上报订单信息
                TytTransportWaybillEx transportWaybillEx = transportWayBillExService.getExById(waybillExId);
                if(transportWaybillEx != null){
                    //运单号
                    String tsOrderNo = transportWaybillEx.getTsOrderNo();
                    //出发地(省市区以减号-分割开)
                    String startPoint = transportWaybillEx.getStartPoint();
                    //目的地(省市区以减号-分割开)
                    String destPoint = transportWaybillEx.getDestPoint();
                    //货物内容
                    String taskContent = transportWaybillEx.getTaskContent();
                    if(StringUtils.isNotBlank(taskContent) && taskContent.length() > 6){
                        taskContent = taskContent.substring(0,6) + "···";
                    }

                    Date goodsLimitEndTime = blacklistUserOrders.getGoodsLimitEndTime();
                    LocalDateTime localDateTime = Jdk8DateTimeUtil.dateToLocalDateTime(goodsLimitEndTime);
                    String limitEndTime = Jdk8DateTimeUtil.formatLocalDateTimeToString(localDateTime, Jdk8DateTimeUtil.DATE_FMT_13);

                    //替换参数,逗号分隔
                    String masterParams = cellPhone + "," + tsOrderNo + "," + startPoint + ","
                            + destPoint + "," + taskContent + "," + limitEndTime;

                    TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号权益限制提醒_短期_有订单, masterParams);
                    msgBean.setNoticeData(noticePopupTempl);
                    return msgBean;
                }
            }else{
                Date goodsLimitEndTime = blacklistUser.getLimitEndTime();
                LocalDateTime localDateTime = Jdk8DateTimeUtil.dateToLocalDateTime(goodsLimitEndTime);
                String limitEndTime = Jdk8DateTimeUtil.formatLocalDateTimeToString(localDateTime, Jdk8DateTimeUtil.DATE_FMT_13);

                //替换参数,逗号分隔
                String masterParams = cellPhone + "," + limitEndTime;

                TytNoticePopupTempl noticePopupTempl = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.账号权益限制提醒_短期_无订单, masterParams);
                msgBean.setNoticeData(noticePopupTempl);
                return msgBean;
            }
        }
        return msgBean;
    }
    @Override
    public ResultMsgBean checkLimitFindGoods(Long userId) throws Exception{
        ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");
        BlacklistUser blacklistUser = this.getBlackByUserId(userId);

        if (Objects.isNull(blacklistUser)) {
            return msgBean;
        }
        //发货长期被限制
        if (Objects.nonNull(blacklistUser.getCarLimitMinutes()) && blacklistUser.getCarLimitMinutes() == -1) {
            TytTransportWaybillEx orderInfo = getCurrentRestrictOrderInfo(userId, true);
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            if (orderInfo != null) {
                //todo 6370 魔法值
                TytNoticePopupTempl noticePopup = tytNoticePopupTemplService.getByType(800, 10);
                //之所以从新copy出一个新对象是因为在向数据库对象set值时 hibernate 会自动执行update语句
                TytNoticePopupTempl noticePopupNew = new TytNoticePopupTempl();
                BeanUtils.copyProperties(noticePopup,noticePopupNew);
                String taskContent = orderInfo.getTaskContent();
                if (taskContent.length()>5) {
                    taskContent = taskContent.substring(0, 5);
                    taskContent = taskContent + "...";
                }
                String formatMsg = String.format(noticePopupNew.getMasterContent(),
                        blacklistUser.getCellPhone(),
                        orderInfo.getTsOrderNo(),
                        orderInfo.getStartPoint(),
                        orderInfo.getDestPoint(),
                        taskContent);
                noticePopupNew.setMasterContent(formatMsg);
                msgBean.setNoticeData(noticePopupNew);
                msgBean.setMsg(formatMsg);
            } else {
                TytNoticePopupTempl noticePopup = tytNoticePopupTemplService.getByType(800, 8);
                msgBean.setNoticeData(noticePopup);
                //之所以从新copy出一个新对象是因为在向数据库对象set值时 hibernate 会自动执行update语句
                TytNoticePopupTempl noticePopupNew = new TytNoticePopupTempl();
                BeanUtils.copyProperties(noticePopup,noticePopupNew);
                String formatMsg = String.format(noticePopupNew.getMasterContent(), blacklistUser.getCellPhone());
                noticePopupNew.setMasterContent(formatMsg);
                msgBean.setNoticeData(noticePopupNew);
                msgBean.setMsg(formatMsg);
            }
            return msgBean;
        }
        // 发货某时间段被限制
        if (Objects.nonNull(blacklistUser.getCarLimitEndTime()) && blacklistUser.getCarLimitEndTime().after(new Date())) {
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            TytTransportWaybillEx orderInfo = getCurrentRestrictOrderInfo(userId, false);
            if (orderInfo != null) {
                //todo 6370 魔法值
                TytNoticePopupTempl noticePopup = tytNoticePopupTemplService.getByType(800, 9);
                //之所以从新copy出一个新对象是因为在向数据库对象set值时 hibernate 会自动执行update语句
                TytNoticePopupTempl noticePopupNew = new TytNoticePopupTempl();
                BeanUtils.copyProperties(noticePopup,noticePopupNew);
                String taskContent = orderInfo.getTaskContent();
                if (taskContent.length()>5) {
                    taskContent = taskContent.substring(0, 5);
                    taskContent = taskContent + "...";
                }
                String formatMsg = String.format(noticePopupNew.getMasterContent(),
                        blacklistUser.getCellPhone(),
                        orderInfo.getTsOrderNo(),
                        orderInfo.getStartPoint(),
                        orderInfo.getDestPoint(),
                        taskContent,
                        TimeUtil.formatDateTime(blacklistUser.getCarLimitEndTime()));
                noticePopupNew.setMasterContent(formatMsg);
                msgBean.setNoticeData(noticePopupNew);
                msgBean.setMsg(formatMsg);
            } else {
                TytNoticePopupTempl popupTempl = tytNoticePopupTemplService.getByType(800, 7);

                TytNoticePopupTempl popupTemplNew = new TytNoticePopupTempl();
                BeanUtils.copyProperties(popupTempl,popupTemplNew);
                String formatMsg = String.format(popupTemplNew.getMasterContent(),
                        blacklistUser.getCellPhone(),
                        TimeUtil.formatDateTime(blacklistUser.getCarLimitEndTime()));
                popupTemplNew.setMasterContent(formatMsg);
                msgBean.setNoticeData(popupTemplNew);
                msgBean.setMsg(formatMsg);
            }
            return msgBean;
        }
        return msgBean;
    }

    @Override
    public BlacklistUser getBlackByCellPhone(String cellPhone) {
        String sql = "SELECT * FROM blacklist_user WHERE cell_phone = ?";
        List<BlacklistUser> list = this.getBaseDao().queryForList(sql, new Object[]{cellPhone});
        if (list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public void updateCellPhoneCancel(String cellPhone) {
        String cancelPhone = cellPhone + "已注销";
        String sql = "UPDATE blacklist_user SET cell_phone = ? WHERE cell_phone = ?";
        this.getBaseDao().executeUpdateSql(sql, new Object[] { cancelPhone, cellPhone });
    }

    @Override
    public boolean isBlacklistMac(String clientId) {
        String selectSQL = "SELECT id from blacklist_mac where mac = ? and status = 1";
        Object[] params = new Object[]{clientId};
        return ObjectUtil.isNotNull(this.getBaseDao().query(selectSQL, params));
    }

    /**
     * 获取当前生效的限制找货货源信息
     *
     * @param userId 用户ID
     * @param isPerpetual 是否长期
     * @return Transport 货源信息
     */
    private TytTransportWaybillEx getCurrentRestrictOrderInfo(Long userId, boolean isPerpetual) throws Exception{
        List<BlacklistUserOrders> blacklistUserOrders = blacklistUserOrdersService.getBlackByUserId(userId);
        if (blacklistUserOrders!=null&&blacklistUserOrders.size()>0) {
            Date currentTime = new Date();
            if (isPerpetual) {
                for (BlacklistUserOrders orders : blacklistUserOrders) {
                    if (orders.getCarLimitStartTime()!=null&&orders.getCarLimitStartTime().before(currentTime)
                            &&orders.getCarLimitEndTime()!=null&&orders.getCarLimitEndTime().after(TimeUtil.parseDateString("2998-12-31 00:00:00"))) {
                        TytTransportWaybillEx transportWaybillEx = transportWayBillExService.getExById(orders.getWaybillExId());
                        log.info("限制找货查询货物信息 userId {} isPerpetual {}  结果：{}",userId,isPerpetual,transportWaybillEx);
                        return transportWaybillEx;
                    }
                }
            } else {
                for (BlacklistUserOrders orders : blacklistUserOrders) {
                    if (orders.getCarLimitEndTime()!=null&&orders.getCarLimitEndTime().after(currentTime)
                            &&orders.getCarLimitStartTime()!=null&&orders.getCarLimitStartTime().before(currentTime)) {
                        TytTransportWaybillEx transportWaybillEx = transportWayBillExService.getExById(orders.getWaybillExId());
                        log.info("限制找货查询货物信息 userId {} isPerpetual {}  结果：{}",userId,isPerpetual,transportWaybillEx);
                        return transportWaybillEx;
                    }
                }
            }
        }
        return null;
    }
}
