package com.tyt.user.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.User;
import com.tyt.user.service.TytUserIdentityAuthBusiness;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;

@Service("tytUserIdentityAuthBusiness")
public class TytUserIdentityAuthBusinessImpl implements TytUserIdentityAuthBusiness {

	
	@Resource(name = "tytUserIdentityAuthService")
	TytUserIdentityAuthService userIdentityAuthService;
	
	@Resource(name = "userService")
	private UserService userService;
	

	@Override
	public TytUserIdentityAuth saveIdentityAuthMoveBusiness(Long userId,String mainUrl,
			Date nowDate) throws Exception {
		User user=userService.getByUserId(userId);
		return userIdentityAuthService.saveIdentityAuthMove(userId,user.getCellPhone(), user.getIdentityType(), user.getUserClass(), user.getTrueName(), mainUrl, nowDate);
	}

	
}
