package com.tyt.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.ShortMsgBean;
import com.tyt.user.service.VerifyLogService;
import com.tyt.util.Constant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.VoiceUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("verifyLogService")
public class VerifyLogServiceImpl implements VerifyLogService {
	private static final String TYT_VOICE_VERIFY_SMS_CONTENT_KEY = "tyt.voice.verify.sms.content";
	private Logger logger = LoggerFactory.getLogger(VerifyLogServiceImpl.class);

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;
	@Resource(name = "tytMessageTmplService")
	private TytMessageTmplService messageTmplService;
	
	@Override
	public boolean saveSendVerifyCode(String userId, String phone, String verifyCode, String type) {
		boolean isSuccess = false;
		// 1语音
		if ("1".equals(type)) {
			isSuccess = VoiceUtil.sendVoiceVerify(verifyCode, phone);
		} else {
			String[] keyArray = new String[] { "${verifyCode}" };
			String[] valueArray = new String[] { verifyCode };
			//String msg = FormatString.formatFailMessage("tyt.voice.verify.sms.content", keyArray, valueArray);
			String verifyContent = messageTmplService.getSmsTmpl(TYT_VOICE_VERIFY_SMS_CONTENT_KEY);
			logger.info("get smt template by key: " + TYT_VOICE_VERIFY_SMS_CONTENT_KEY + " , result is: " + verifyCode);
			if (StringUtils.isEmpty(verifyContent)) {
				verifyContent = "验证码:${verifyCode}";
			}
			verifyContent = StringUtils.replaceEach(verifyContent, keyArray, valueArray);
			ShortMsgBean shortMsgBean = new ShortMsgBean();
			// 根据短信key获取短信模板
			shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			String messageSerailNum = SerialNumUtil.generateSeriaNum();
			shortMsgBean.setMessageSerailNum(messageSerailNum);
			shortMsgBean.setContent(verifyContent);
			shortMsgBean.setCell_phone(phone);
			shortMsgBean.setRemark("");
			tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			isSuccess = tytMqMessageService.sendMqMessageWithResult(messageSerailNum, JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
		}

		if (isSuccess) {
			cacheService.setString(Constant.CACHE_VERIFY_CODE_KEY + phone, verifyCode, Long.parseLong(com.tyt.config.util.AppConfig.getProperty("tyt.voice.verify.cache.time.second")));
		}

		if (isSuccess) {
			logger.info("手机号={}验证码={},{}发送成功", phone, verifyCode, "1".equals(type) ? "语音" : "短信");
			return true;
		} else {
			logger.error("手机号={}验证码={},{}发送失败", phone, verifyCode, "1".equals(type) ? "语音" : "短信");
			return false;
		}
	}

	@Override
	public boolean verify(String phone, String verifyCode) {
		String vCode = cacheService.getString(Constant.CACHE_VERIFY_CODE_KEY + phone);
		if (verifyCode.equals(vCode)) {
			logger.info("手机号={}验证码={}验证成功", phone, verifyCode);
			cacheService.del(Constant.CACHE_VERIFY_CODE_KEY + phone);
			return true;
		} else {
			logger.error("手机号={}验证码={}正确的=｛｝", phone, verifyCode, vCode);
			return false;
		}
	}

}
