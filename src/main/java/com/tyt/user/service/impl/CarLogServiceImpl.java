package com.tyt.user.service.impl;



import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.CarLog;
import com.tyt.user.service.CarLogService;



@Service("carLogService")
public  class CarLogServiceImpl extends BaseServiceImpl<CarLog, Long> implements CarLogService  {

	@Resource(name = "carLogDao")
	public void setBaseDao(BaseDao<CarLog, Long> carLogDao) {
		super.setBaseDao(carLogDao);
	}

	

	
	



	
}
