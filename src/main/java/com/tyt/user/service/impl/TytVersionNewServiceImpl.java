package com.tyt.user.service.impl;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytVersionNew;
import com.tyt.user.service.TytVersionNewService;
import com.tyt.util.Constant;

@Service("tytVersionNewService")
public class TytVersionNewServiceImpl extends BaseServiceImpl<TytVersionNew,Long> implements TytVersionNewService{
    
	
    @Resource(name="tytVersionNewDao")
    public void setBaseDao(BaseDao<TytVersionNew, Long> versionDao) {
        super.setBaseDao(versionDao);
    }

	@Override
	public TytVersionNew getByClient(Integer clientType, String userVersion,
			Integer status,String downloadType) {
		TytVersionNew version=null;
		/*查询条件*/
		String querySql="select * from tyt_version_new where  "
				+ " client_type=? and status=? and ?>=start_version and ?<=end_version and download_type=?";
		/*配置参数*/
		List<Object> params=new ArrayList<Object>();
		params.add(clientType);
		params.add(status);
		params.add(userVersion);
		params.add(userVersion);
		params.add(downloadType);
		/*查询结果*/
		List<TytVersionNew>  list=this.getBaseDao().search(querySql, params.toArray(), 1, 1);
		if(list.size()>0){
			version=list.get(0);
		}
		params=null;
		list=null;//使用完毕提醒垃圾回收
		
		/*特殊情况，未知版本处理*/
		if(version==null){
			String speName="pc";
			if(clientType==Constant.PLAT_ANDROID)speName="android";
			if(clientType==Constant.PLAT_IOS)speName="ios";
			
			querySql="select * from tyt_version_new where"
					+ " client_type=? and start_version=? and status=? and download_type=?";
			
			List<Object> speParams=new ArrayList<Object>();
			speParams.add(clientType);
			speParams.add(speName);
			speParams.add(status);
			speParams.add(downloadType);
			List<TytVersionNew>  lis=this.getBaseDao().search(querySql, speParams.toArray(),1,1);
			if(lis.size()>0){
				version=lis.get(0);
			}
			speParams=null;
			lis=null;//使用完毕提醒垃圾回收
		}
		
		return version;
	}

}
