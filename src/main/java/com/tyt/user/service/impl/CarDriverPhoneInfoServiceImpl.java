package com.tyt.user.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCarDriverPhoneInfo;
import com.tyt.user.service.CarDriverPhoneInfoService;

@Service(value = "carDriverPhoneInfoService")
public class CarDriverPhoneInfoServiceImpl extends BaseServiceImpl<TytCarDriverPhoneInfo, Long> implements CarDriverPhoneInfoService{

	@Resource(name = "carDriverPhoneInfoDao")
	public void setBaseDao(BaseDao<TytCarDriverPhoneInfo, Long> carDriverPhoneInfoDao) {
		super.setBaseDao(carDriverPhoneInfoDao);
	}

	@Override
	public TytCarDriverPhoneInfo getByCarId(Long id) {
		String hql="FROM TytCarDriverPhoneInfo WHERE carId=? ";
		List<TytCarDriverPhoneInfo> list=this.getBaseDao().find(hql, id);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}

	@Override
	public void updateDriverPhoneInfo(TytCarDriverPhoneInfo driverPhone) {
		TytCarDriverPhoneInfo info = this.getByCarId(driverPhone.getCarId());
		if(info==null) {
			driverPhone.setCreateTime(new Date());
			driverPhone.setUpdateTime(new Date());
			this.getBaseDao().insert(driverPhone);
		}else {
			info.setUpdateTime(new Date());
			info.setDriverName(driverPhone.getDriverName());
			info.setDriverPhone(driverPhone.getDriverPhone());
			info.setSecondDriverName(driverPhone.getSecondDriverName());
			info.setSecondDriverPhone(driverPhone.getSecondDriverPhone());
			info.setFollowerPhone(driverPhone.getFollowerPhone());
			this.getBaseDao().update(info);
		}
		
	}
}
