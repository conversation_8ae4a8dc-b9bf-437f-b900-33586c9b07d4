package com.tyt.user.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUserIdentityInfo;
import com.tyt.user.service.UserIdentityInfoService;


@Service("userIdentityInfoService")
public class UserIdentityInfoServiceImpl extends BaseServiceImpl<TytUserIdentityInfo,Long> implements UserIdentityInfoService {

    @Resource(name="userIdentityInfoDao")
    public void setBaseDao(BaseDao<TytUserIdentityInfo, Long> userIdentityInfoDao) {
        super.setBaseDao(userIdentityInfoDao);
    }

 
}
