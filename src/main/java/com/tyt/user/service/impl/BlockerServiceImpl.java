package com.tyt.user.service.impl;


import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Blocker;
import com.tyt.user.service.BlockerService;
@Service("blockerService")
public class BlockerServiceImpl extends BaseServiceImpl<Blocker,Long>  implements BlockerService {

	@Resource(name="blockerDao")
    public void setBaseDao(BaseDao<Blocker, Long> blockerDao) {
        super.setBaseDao(blockerDao);
    }

	
}
