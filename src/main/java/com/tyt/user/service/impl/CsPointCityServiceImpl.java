package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.CsPointCity;
import com.tyt.user.service.CsPointCityService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("csPointCityService")
public class CsPointCityServiceImpl extends BaseServiceImpl<CsPointCity, Long> implements CsPointCityService {

    @Resource(name = "csPointCityDao")
    public void setBaseDao(BaseDao<CsPointCity, Long> csPointCityDao) {
        super.setBaseDao(csPointCityDao);
    }

    @Override
    public CsPointCity getPointCityByCity(String city){
        String sql = "from CsPointCity where city=?";
        List<CsPointCity> pointCityList= this.getBaseDao().find(sql,city);
        if(pointCityList!=null&& pointCityList.size()>0){
            return pointCityList.get(0);
        }
        return null;
    }
}
