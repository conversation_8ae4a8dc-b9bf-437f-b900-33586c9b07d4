package com.tyt.user.service.impl;

import java.util.List;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.UserTel;
import com.tyt.user.dao.UserTelDao;
import com.tyt.user.querybean.QueryUserTel;
import com.tyt.user.querybean.UserTelModel;
import com.tyt.user.service.UserTelService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * 
 * <AUTHOR>
 *
 */
@Service("userTelService")
public class UserTelServiceImpl extends BaseServiceImpl<UserTel,Long> implements UserTelService {

    @Resource(name="userTelDao")
    public void setBaseDao(BaseDao<UserTel, Long> userTelDao) {
        super.setBaseDao(userTelDao);
    }

	@Override
	public List<QueryUserTel> getTelsById(Long userId,String type) throws Exception {
		return ((UserTelDao) this.getBaseDao()).getTelsById(userId,type);
	}

	@Override
	public boolean get(Long userId, String tel) {
		StringBuffer sql=new StringBuffer();
		sql.append(" entity.userId="+userId).append(" and entity.tell='").append(tel+"'").append(" and entity.status=1");
		List<UserTel> tels=this.getList(sql.toString(), null);
		return tels.size()>0;
	}

	@Override
	public List<UserTel> getUserTel(Long userId, String tel) {
		StringBuffer sql=new StringBuffer();
		sql.append(" entity.userId="+userId).append(" and entity.tell='").append(tel+"'").append(" and entity.status=1");
		return this.getList(sql.toString(), null);
	}

	@Override
	public void disabledId(Long id) throws Exception {
		((UserTelDao) this.getBaseDao()).disabledId(id);
	}

	@Override
	public void updateUserTel(UserTel userTel) {
		((UserTelDao) this.getBaseDao()).updateUserTel(userTel);
	}

	@Override
	public List<String> getLatestTels(Long userId, Integer count) {
		if(count!=null&&count.intValue()>0){
			String sql="SELECT tell FROM tyt_user_tel WHERE user_id=? AND status=? ORDER BY  id DESC LIMIT ?";
			List<String> tels=this.getBaseDao().queryByCondition(sql,new Object[]{userId,1,count});
			return tels;
		}else{
			String sql="SELECT tell FROM tyt_user_tel WHERE user_id=? AND status=?";
			List<String> tels=this.getBaseDao().queryByCondition(sql,new Object[]{userId,1});
			return tels;
		}
	}


	@Override
	public List<UserTelModel> getLatestTelsByUserId(Long userId, Integer count) {
		return ((UserTelDao) this.getBaseDao()).getLatestTelsByUserId(userId,count);


//		if(count!=null&&count.intValue()>0){
//			String sql="SELECT * FROM tyt_user_tel WHERE user_id=? AND status=? and is_verified=1  ORDER BY id DESC LIMIT ?";
//			List<UserTel> tels=this.getBaseDao().queryByCondition(sql,new Object[]{userId,1,count});
//			return tels;
//		}else{
//			String sql="SELECT * FROM tyt_user_tel WHERE user_id=? AND status=? and is_verified=1 ";
//			List<UserTel> tels=this.getBaseDao().queryByCondition(sql,new Object[]{userId,1});
//			return tels;
//		}
	}
}
