package com.tyt.user.service.impl;

import java.math.BigInteger;
import java.util.*;

import javax.annotation.Resource;

import com.tyt.plat.enums.SourceGroupCodeEnum;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytSource;
import com.tyt.user.querybean.SourceBean;
import com.tyt.user.service.TytSourceService;

/**
 * 
 * <AUTHOR>
 * 
 */
@Service("tytSourceService")
public class TytSourceServiceImpl extends BaseServiceImpl<TytSource, Long>
		implements TytSourceService {

	@Resource(name = "tytSourceDao")
	public void setBaseDao(BaseDao<TytSource, Long> sourceDao) {
		super.setBaseDao(sourceDao);
	}

	@Override
	public List<SourceBean> getByGroupCode(String groupCode) {
		/* 查询条件 */
		List<Object> listObject = new ArrayList<Object>();
		listObject.add(groupCode);
		/* 求条数 */
		StringBuffer sbCount = new StringBuffer(
				"select count(*) from tyt_source where group_code=?");
		BigInteger rowCount = this.getBaseDao().query(sbCount.toString(),
				listObject.toArray());
		if (rowCount != null && rowCount.longValue() > 0) {
			/* 数据集合 */
			StringBuffer sbSql = new StringBuffer(
					"select name,value from tyt_source where group_code=? order by sort desc");
			/* 查询值类型定义 */
			Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
			map.put("name", Hibernate.STRING);
			map.put("value", Hibernate.STRING);
			/* 查结果 */
			List<SourceBean> list = this.getBaseDao().search(sbSql.toString(),
					map, SourceBean.class, listObject.toArray(),
					rowCount.intValue(), 1);
			return list;
		}
		return null;

	}

	@Override
	public List<SourceBean> getAllFromDB() {
		/* 求条数 */
		StringBuffer sbCount = new StringBuffer(
				"select count(*) from tyt_source");
		BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), null);

		if (rowCount != null && rowCount.longValue() > 0) {
			/* 数据集合 */
			StringBuffer sbSql = new StringBuffer(
					"SELECT group_code groupCode,NAME,VALUE FROM tyt_source "
							+ "ORDER BY group_code,sort");
			/* 查询值类型定义 */
			Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
			map.put("groupCode", Hibernate.STRING);
			map.put("name", Hibernate.STRING);
			map.put("value", Hibernate.STRING);
			/* 查结果 */
			List<SourceBean> list = this.getBaseDao().search(sbSql.toString(),
					map, SourceBean.class, null, 1, rowCount.intValue());
			return list;
		}
		return null;
	}

	@Override
	public List<TytSource> getAllTytSourceList() {
		String hql = "from TytSource where status=? ";
		return this.getBaseDao().find(hql, 0);

	}

	@Override
	public List<TytSource> getSubTytSourceList(String parent) {
		String hql = "from TytSource where status=? and parent=? order by sort asc";
		return this.getBaseDao().find(hql, 0, parent);
	}

	@Override
	public List<TytSource> getSubByLikeParent(String parent) {
		String hql = "FROM TytSource WHERE status=? AND parent LIKE ? ORDER BY sort ASC";
		return this.getBaseDao().find(hql, 0, "%" + parent + "%");
	}
	
	@Override
	public List<TytSource> getByGroupCodeNotContainChild(List<String> groupCode) {
		String sql = "SELECT id,group_code groupCode,group_name groupName,"
				+ "value,name,short_name shortName,"
				+ "remark,sort,parent,status FROM  tyt_source "
				+ "WHERE status=:status AND group_code IN(:list) "
				+ "ORDER BY sort ASC";
		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
        
		mapType.put("id",Hibernate.LONG);
		mapType.put("groupCode",Hibernate.STRING);
		mapType.put("groupName",Hibernate.STRING);
		mapType.put("value",Hibernate.STRING);
		mapType.put("name",Hibernate.STRING);
		mapType.put("shortName",Hibernate.STRING);
		mapType.put("remark",Hibernate.STRING);
		mapType.put("sort",Hibernate.INTEGER);
		mapType.put("parent",Hibernate.STRING);
		mapType.put("status",Hibernate.INTEGER);
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("status", 0);
		map.put("list", groupCode);
		return this.getBaseDao().search(sql, mapType, TytSource.class, map);

	}
	
	@Override
	public List<TytSource> getChildById(List<TytSource> list){
		// 根据id找子级
		List<TytSource> subList = null;
		Iterator<TytSource> iterator = list.iterator();
		TytSource source = new TytSource();
		while (iterator.hasNext()) {
			source = iterator.next();
			subList = this.getSubByLikeParent(source.getId() + "");//获取子级
			if (subList != null && subList.size() > 0) {
				source.setSubset(subList);
			}
		}
		return list;
	}
	
	@Override
	public List<TytSource> getChildByParent(List<TytSource> list){
	    //根据parent找父级
		Iterator<TytSource> iterator=list.iterator();
		TytSource tytSource=null;
		String parent=null;
		List<String> parentIdList=null;
		while(iterator.hasNext()){
			tytSource=iterator.next();
			parent=tytSource.getParent();//父级，格式 :#父级ID#父级ID#
			if(parent!=null&&!"".equals(parent.trim())&&parent.length()>2){//父级ID存在
				parentIdList=Arrays.asList(parent.substring(1).split("#"));//父级ID转化成数组
				tytSource.setSubset(this.getByIdNotContainChild(parentIdList));//根据父级ID获取结果集
			}
		}
		return list;
	}
	@Override
	public List<TytSource> getByGroupCode(List<String> groupCode) {
		//根据groupCode查询出结果集，但不包含子级
		List<TytSource> parentList=this.getByGroupCodeNotContainChild(groupCode);	
		//根据每条结果的id查找其子级
		parentList=this.getChildById(parentList);
		return parentList;
	}

	@Override
	public List<TytSource> getSubParentList(List<String> groupCode) {
		//根据标签查询结果集，但不包含父级
		List<TytSource> result =this.getByGroupCodeNotContainChild(groupCode);
		//根据每条结果的parent查找其父级
		result=this.getChildByParent(result);
		return result;
	}
	
	@Override
	public List<TytSource> getByIdNotContainChild(List<String> idList) {
		if(idList==null){
			return null;
		}
		String sql = "SELECT id,group_code groupCode,group_name groupName,"
				+ "value,name,short_name shortName,"
				+ "remark,sort,parent,status FROM  tyt_source "
				+ "WHERE status=:status AND id IN(:list) "
				+ "ORDER BY sort ASC";
		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
        
		mapType.put("id",Hibernate.LONG);
		mapType.put("groupCode",Hibernate.STRING);
		mapType.put("groupName",Hibernate.STRING);
		mapType.put("value",Hibernate.STRING);
		mapType.put("name",Hibernate.STRING);
		mapType.put("shortName",Hibernate.STRING);
		mapType.put("remark",Hibernate.STRING);
		mapType.put("sort",Hibernate.INTEGER);
		mapType.put("parent",Hibernate.STRING);
		mapType.put("status",Hibernate.INTEGER);
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("status", 0);
		map.put("list", idList);
		List<TytSource> result =this.getBaseDao().search(sql, mapType, TytSource.class, map);
		return result;
	}
	@SuppressWarnings("deprecation")
	@Override
	public List<SourceBean> getByGroupCodeWithNoLimit(String groupCode) {
		/* 查询条件 */
		List<Object> listObject = new ArrayList<Object>();
		listObject.add(groupCode);
		/* 数据集合 */
		StringBuffer sbSql = new StringBuffer("select name,value from tyt_source where group_code=? order by sort asc");
		/* 查询值类型定义 */
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("name", Hibernate.STRING);
		map.put("value", Hibernate.STRING);
		/* 查结果 */
		List<SourceBean> list = this.getBaseDao().search(sbSql.toString(), map, SourceBean.class, listObject.toArray());
		return list;
	}

	@Override
	public List<SourceBean> getByGroupCodeWithNoLimit(SourceGroupCodeEnum ... codeEnumArray) {

		if (ArrayUtils.isEmpty(codeEnumArray)) {
			return null;
		}

		Set<String> codeSet = new HashSet<>();

		for(SourceGroupCodeEnum oneEnum: codeEnumArray ){
			String groupCode = oneEnum.getGroupCode();
			codeSet.add("'" + groupCode + "'");
		}

		String groupCodeStr = StringUtils.join(codeSet, ",");

		String sql = "select name, value from tyt_source where group_code in (" + groupCodeStr + ") order by sort asc";

		/* 查询值类型定义 */
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("name", Hibernate.STRING);
		map.put("value", Hibernate.STRING);

		Object[] params = {};

		/* 查结果 */
		List<SourceBean> list = this.getBaseDao().search(sql, map, SourceBean.class, params);
		return list;
	}


}
