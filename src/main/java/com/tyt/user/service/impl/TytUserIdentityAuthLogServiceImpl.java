package com.tyt.user.service.impl;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.TytUserIdentityAuthLog;
import com.tyt.user.service.TytUserIdentityAuthLogService;

@Service("tytUserIdentityAuthLogService")
public class TytUserIdentityAuthLogServiceImpl extends
		BaseServiceImpl<TytUserIdentityAuthLog, Long> implements
		TytUserIdentityAuthLogService {

	@Resource(name = "tytUserIdentityAuthLogDao")
	public void setBaseDao(
			BaseDao<TytUserIdentityAuthLog, Long> tytUserIdentityAuthLogDao) {
		super.setBaseDao(tytUserIdentityAuthLogDao);
	}

	@Override
	public TytUserIdentityAuthLog saveIdentityAuthLog(TytUserIdentityAuth userIdentityAuth)
			throws Exception {
		TytUserIdentityAuthLog userIdentityAuthLog=new TytUserIdentityAuthLog();
		BeanUtils.copyProperties(userIdentityAuth, userIdentityAuthLog);
		userIdentityAuthLog.setUiaId(userIdentityAuth.getId());
		this.add(userIdentityAuthLog);
		return userIdentityAuthLog;
	}
}
