package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.PageBean;
import com.tyt.model.VerifyCode;
import com.tyt.user.service.VerifyCodeService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:35
 */
@Service("verifyCodeService")
public class VerifyCodeServiceImpl extends BaseServiceImpl<VerifyCode,Long> implements VerifyCodeService {

    @Resource(name="verifyCodeDao")
    public void setBaseDao(BaseDao<VerifyCode, Long> verifyCodeDao) {
        super.setBaseDao(verifyCodeDao);
    }

    @Override
    public VerifyCode getVerifyCode(String verifyCode) {
        VerifyCode vcQuery = new VerifyCode();
        vcQuery.setVerifyCode(verifyCode);

        List<VerifyCode> vcList = getBaseDao().search(vcQuery);

        return (vcList.size()>0) ? vcList.get(0) : null;

    }

    @Override
    public List<VerifyCode> getUsefulVerifyCodeList(int size) {
        VerifyCode vcQuery = new VerifyCode();
        vcQuery.setUsedCount(0);

        PageBean pageBean = new PageBean();
        pageBean.setPageSize(size);
        pageBean.setCurrentPage(1);
        List<VerifyCode> result = getBaseDao().search(" entity.usedCount = 0",pageBean);
        return result;
    }
}
