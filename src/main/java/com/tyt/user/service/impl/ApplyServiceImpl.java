package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Apply;
import com.tyt.user.service.ApplyService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("applyService")
public class ApplyServiceImpl extends BaseServiceImpl<Apply,Long> implements ApplyService {

    @Resource(name="applyDao")
    public void setBaseDao(BaseDao<Apply, Long> applyDao) {
        super.setBaseDao(applyDao);
    }

 
}
