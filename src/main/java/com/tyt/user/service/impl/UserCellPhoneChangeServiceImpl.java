package com.tyt.user.service.impl;

import com.tyt.cache.CacheService;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.AccountUpdateLog;
import com.tyt.plat.entity.base.TytCellPhoneChangeAudit;
import com.tyt.plat.entity.base.TytCellphone;
import com.tyt.plat.mapper.base.AccountUpdateLogMapper;
import com.tyt.plat.mapper.base.TytCellPhoneChangeAuditMapper;
import com.tyt.plat.mapper.base.TytCellphoneMapper;
import com.tyt.plat.mapper.base.TytInvoiceDriverMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.bean.CellPhoneIsChangeInfo;
import com.tyt.user.enums.CellPhoneChangeAuditStatusEnum;
import com.tyt.user.service.AccountUpdateLogService;
import com.tyt.user.service.UserCellPhoneChangeService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户变更手机号相关逻辑
 * @date 2023/09/05 10:46
 */
@Service
@Slf4j
public class UserCellPhoneChangeServiceImpl implements UserCellPhoneChangeService {
    @Autowired
    private TytCellPhoneChangeAuditMapper tytCellPhoneChangeAuditMapper;
    @Autowired
    private AccountUpdateLogService accountUpdateLogService;
    @Autowired
    private AccountUpdateLogMapper accountUpdateLogMapper;
    @Autowired
    private TytCellphoneMapper tytCellphoneMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private TytInvoiceDriverMapper tytInvoiceDriverMapper;

    @Override
    public TytCellPhoneChangeAudit getByUserIdAndAuditStatus(Long userId, Integer auditStatus) {
        return tytCellPhoneChangeAuditMapper.selectByUserIdAndAuditStatus(userId, auditStatus);
    }

    @Override
    public ResultMsgBean getIsChangeInfo(Long userId) {
        try {
            //审核中的不允许二次提交
            TytCellPhoneChangeAudit cellPhoneChangeAudit = getByUserIdAndAuditStatus(userId, CellPhoneChangeAuditStatusEnum.CHECK_PENDING.getAuditStatus());
            if (Objects.nonNull(cellPhoneChangeAudit)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BOUND_PHONE_CANT_CHANGE_AUDIT, "资料审核中,请耐心等待");
            }

            AccountUpdateLog accountUpdateLog = accountUpdateLogService.getRecentAccountUpdateLog(userId);
            //每周仅限更换一次校验
            if (Objects.nonNull(accountUpdateLog)) {
                String nowDateStr = TimeUtil.formatDateYYYYMMDD(new Date());
                Date beginTime = TimeUtil.getWeekFirstDay(nowDateStr);
                Date endTime = TimeUtil.getWeekLastDay(nowDateStr);
                boolean belongCalendar = TimeUtil.belongCalendar(accountUpdateLog.getCtime(), beginTime, endTime);
                if (accountUpdateLog.getCtime().compareTo(beginTime) == 0 || accountUpdateLog.getCtime().compareTo(endTime) == 0 || belongCalendar) {
                    return ResultMsgBean.failResponse(ReturnCodeConstant.BOUND_PHONE_CANT_CHANGE_TIME, "更换手机号每周仅限1次,请下周再试");
                }
            }
            //校验通过返回当前手机号
            String cellPhoneById = userService.getCellPhoneById(userId);
            return ResultMsgBean.successResponse(new CellPhoneIsChangeInfo(cellPhoneById));
        } catch (Exception e) {
            log.error("用户变更手机号,校验是否可以变更异常:", e);
        }
        return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "系统异常");
    }

    @Override
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public void updateCellPhone(Long userId, String oldPhone, String newPhone) {
        tytCellphoneMapper.deleteFromTytCellphone(oldPhone);
        if (PhoneFormatCheckUtils.isMobile(newPhone)) {
            TytCellphone tytCellphone = tytCellphoneMapper.selectByCellPhone(newPhone);
            if (Objects.isNull(tytCellphone)) {
                tytCellphoneMapper.insertCellPhoneToTemp(newPhone);
            }
        }
        accountUpdateLogMapper.updateIdentityAuthMobile(userId, newPhone);
        accountUpdateLogMapper.updateBlacklistUser(userId, newPhone, oldPhone);
        accountUpdateLogMapper.updateUserCellphoneInfo(userId, newPhone, "0000");
        //变更手机号更新司机手机号信息
        tytInvoiceDriverMapper.updatePhoneByPhone(newPhone, oldPhone);
        AccountUpdateLog accountUpdateLog = new AccountUpdateLog();
        accountUpdateLog.setCellPhone(oldPhone);
        accountUpdateLog.setNewPhone(newPhone);
//        accountUpdateLog.setReason("用户自主变更");
        accountUpdateLog.setUserId(userId);
        accountUpdateLog.setUpdateType(0);
        accountUpdateLog.setCtime(new Date());
        accountUpdateLogMapper.insertSelective(accountUpdateLog);
        // 删除缓存 提出登录状态
        UserTicketUtil.kickOutAllClient(userId.toString());
        cacheService.del(Constant.CACHE_USER_KEY + userId);
        //删除步骤校验缓存
        RedisUtil.del(Constant.CELLPHONE_CHANGE_STEP + oldPhone);
    }

    @Override
    public void saveChangeAuditInfo(TytCellPhoneChangeAudit audit) {
        audit.setCreateTime(new Date());
        tytCellPhoneChangeAuditMapper.insertSelective(audit);
    }
}
