package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.config.util.AppConfig;
import com.tyt.message.service.TytPushUserService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.TytUserSub;
import com.tyt.transport.querybean.SttLimitBean;
import com.tyt.transport.service.SttLimitService;
import com.tyt.user.bean.RightsCheckBean;
import com.tyt.user.bean.UserCollectCountBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.classic.Session;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service("tytUserSubService")
public class TytUserSubServiceImpl extends BaseServiceImpl<TytUserSub, Long> implements TytUserSubService {
    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;
    @Resource(name = "sttLimitService")
    SttLimitService sttLimitService;
    @Resource(name = "tytPushUserService")
    TytPushUserService tytPushUserService;
    @Resource(name = "tytUserSubService")
    private TytUserSubService userSubService;

    @Resource(name = "tytBubbleService")
    private TytBubbleService bubbleService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService authService;

    @Resource(name = "tytNoticeRemindService")
    private TytNoticeRemindService noticeRemindService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    public org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytUserSubDao")
    public void setBaseDao(BaseDao<TytUserSub, Long> tytUserSub) {
        super.setBaseDao(tytUserSub);
    }

    @Override
    public TytUserSub getTytUserSubByUserId(Long userId) {
        // 增加字段时，数据库和缓存不同步问题； 2019-05-18
        if(userId == null || userId.longValue() <= 0){
            return null;
        }
        String key=Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date());
        TytUserSub tytUserSub = (TytUserSub) cacheService.getObject(key);
        String befDateStr=tytConfigService.getStringValue(Constant.USER_SUB_CACHE_VALID_TIME,"");
        boolean isRefresh= (null ==tytUserSub ) || TimeUtil.isBeforeWhenDate(tytUserSub.getUtime(),befDateStr) ;

        if(isRefresh){
            String hql = "from TytUserSub where userId=?";
            List<TytUserSub> tytUserSubList = this.getBaseDao().find(hql, userId);

            if (null != tytUserSubList && tytUserSubList.size() > 0) {
                tytUserSub = tytUserSubList.get(0);
                this.getBaseDao().refresh(tytUserSub);
                cacheService.setObject(key, tytUserSub, Constant.CACHE_EXPIRE_TIME_24H);
                logger.info(" userSubisRefresh="+isRefresh+"userId="+userId );
                return tytUserSub;
            }
        }
        return  tytUserSub;

    }

    @Override
    public TytUserSub updateCache(Long userId) {
        String key = Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date());
        cacheService.del(key);
        String hql = "from TytUserSub where userId=?";
        List<TytUserSub> tytUserSubList = this.getBaseDao().find(hql, userId);
        TytUserSub tytUserSub = tytUserSubList.get(0);
        cacheService.setObject(key, tytUserSub, Constant.CACHE_EXPIRE_TIME_24H);
        return tytUserSub;
    }

    @Override
    public TytUserSub saveTytUserByUserId(Long userID, Integer verifyFlag, String clientId) throws Exception {

        // 插入数据库一条
        TytUserSub tytUserSub = getTytUserSubByUserId(userID);
        if (tytUserSub == null) {
            tytUserSub = new TytUserSub();
            tytUserSub.setUserId(userID);
            tytUserSub.setVerifyFlag(verifyFlag);
            SttLimitBean sttLimitBean = sttLimitService.getUserSttLimit(userID);
            tytUserSub.setSendTptType(sttLimitBean.getType());
            tytUserSub.setSendTptNumber(0);
            tytUserSub.setNewMsgNbr(0);
            tytUserSub.setCarNewMsgNbr(0);
            tytUserSub.setGoodsNewMsgNbr(0);
            tytUserSub.setNotifyBadge(0);
            tytUserSub.setCarNotifyBadge(0);
            tytUserSub.setGoodsNewMsgNbr(0);
            // 信息费优化增加发布货源信息费成交数
            tytUserSub.setDealNum(0);
            // 钱包密码默认为未设置
            tytUserSub.setPocketPwdStatus(1);
            // 设置为体验用户
            tytUserSub.setUserGroup(Constant.RIGHTS_EXPERIENCE);
            // 插入身份认证未成功提醒

            //5930版本去掉，因为注册完成后会直走登录。 都在登录处进行判断和发放 2019-7-10；
            tytUserSub.setBindStatus(1);
            /*
             // btt5910修改 判断当前设备是否已经被绑定
            if (hasBinded(clientId)) {
                tytUserSub.setBindStatus(1);
            } else {
                tytUserSub.setBindStatus(2);
                tytUserSub.setBindCliendid(clientId);
                // 插入身份认证未成功的提醒
				// btt5910修改
                noticeRemindService.saveRightsInfo(Constant.INDEX_EXPERIENCE_NO_IDENTITY_TYPE1,
                        Constant.INDEX_EXPERIENCE_NO_IDENTITY_TYPE2,
                        Constant.INDEX_EXPERIENCE_NO_IDENTITY_CONTENT,
                        userID);
            }*/
            add(tytUserSub);
        } else {
            // 如果是未绑定状态则尝试绑定
            if (tytUserSub.getBindStatus() == null || tytUserSub.getBindStatus() == 1) {
                if (!hasBinded(clientId,userID)) {
                    updateBindStatus(clientId, userID, 2);
                }
            }
        }
        return tytUserSub;
    }

    @Override
    public void updateBindStatus(String clientId, Long userID, int bindStatus) {
        this.getBaseDao().executeUpdateSql(
                "UPDATE tyt.`tyt_user_sub` tus SET tus.`bind_status`=?, tus.`bind_cliendid`=? WHERE tus.`user_id`=?",
                new Object[]{bindStatus, clientId, userID});
        String key=Constant.CACHE_USERSUB_KEY + userID.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date());
        cacheService.del(key);
    }

    @Override
    public boolean hasBinded(String clientId,Long userId) {
        return getCountByClientId(clientId,userId) > 0;
    }

    @Override
    public int getCountByClientId(String clientId,Long userId) {
        BigInteger count = this.getBaseDao().query(
                "SELECT COUNT(*) FROM tyt.`tyt_user_sub` tus WHERE tus.`bind_cliendid`=? and tus.`user_id`=?",
                new Object[]{clientId,userId});
        return count.intValue();
    }

    public boolean isSendGoods(Long userId) throws Exception {
        TytUserSub tytUserSub = getTytUserSubByUserId(userId);
        // 未认证用户
        if (tytUserSub == null) {
            return false;
        } else {
            SttLimitBean sttLimitBean = sttLimitService.getUserSttLimit(userId);
            if (sttLimitBean.getValue() != -1) {
                if ((tytUserSub.getSendTptNumber() == null ? 0 : tytUserSub.getSendTptNumber().intValue()) >= sttLimitBean.getValue().intValue()) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        }
    }

    public int getUserRemainNumber(Long userId) throws Exception {

        int remainNumber = 0;

        SttLimitBean sttLimitBean = sttLimitService.getUserSttLimit(userId);
        int limitNumber = Integer.MAX_VALUE;

        if (sttLimitBean.getValue().intValue() != -1)
            limitNumber = sttLimitBean.getValue();

        TytUserSub tytUserSub = getTytUserSubByUserId(userId);
        if (tytUserSub == null) {
            remainNumber = limitNumber;
        } else {
            remainNumber = limitNumber - (tytUserSub.getSendTptNumber() == null ? 0 : tytUserSub.getSendTptNumber().intValue());
        }
        return remainNumber < 0 ? 0 : remainNumber;
    }

    @Override
    public void setUserSendGoodsLimitNumber(Long userId) throws Exception {
        TytUserSub tytUserSub = getTytUserSubByUserId(userId);
        SttLimitBean sttLimitBean = sttLimitService.getUserSttLimit(userId);
        if (tytUserSub == null) {
            tytUserSub = new TytUserSub();
            tytUserSub.setSendTptNumber(0);
            tytUserSub.setSendTptType(sttLimitBean.getType());
            tytUserSub.setUserId(userId);
            tytUserSub.setVerifyFlag(0);
            tytUserSub.setNewMsgNbr(0);
            tytUserSub.setCarNewMsgNbr(0);
            tytUserSub.setGoodsNewMsgNbr(0);
            tytUserSub.setNotifyBadge(0);
            tytUserSub.setCarNotifyBadge(0);
            tytUserSub.setGoodsNotifyBadge(0);
            this.getBaseDao().insert(tytUserSub);

        } else {
            tytUserSub.setSendTptNumber(0);
            tytUserSub.setSendTptType(sttLimitBean.getType());
            tytUserSub.setUtime(new Date());
            this.getBaseDao().update(tytUserSub);
            cacheService.del(Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date()));
        }

    }

    @Override
    public void delNotifyBadge(Long userId) {
        String sql = "update tyt_user_sub set notify_badge=0,utime=now() where user_id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{userId});
        cacheService.del(Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date()));

    }

    @Override
    public void savePushCid(Long userId, String cid, Integer clientSign, String clientVersion, String osVersion) {
        if (cid != null && !"".equals(cid)) {
            if (2 == clientSign.intValue() || 3 == clientSign.intValue()) {
                String sql = "update tyt_user_sub set client_sign=?,cid=?,utime=now()  where user_id=?";

                TytUserSub tytUserSub = this.getTytUserSubByUserId(userId);
                if (null != tytUserSub) {
                    if (tytUserSub.getClientSign() != null) {
                        int sign = tytUserSub.getClientSign();
                        if (sign == 2 && 3 == clientSign.intValue()) {
                            sql = "update tyt_user_sub set client_sign=?,cid=?,notify_badge=0,utime=now()  where user_id=?";
                        }
                    }
                }
                this.getBaseDao().executeUpdateSql(sql, new Object[]{clientSign, cid, userId});

                tytPushUserService.savePushCid(userId, cid, clientSign, clientVersion, osVersion);
                cacheService.del(Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date()));
            }
        }
    }

    @Override
    public Object[] getIdentityLables(Long userId) {
        // 先从缓存取身份标签，没有则从数据库取，缓存有效期同用户信息
        Object[] labels = new Object[2];
        Object bcarLables = cacheService.getObject(Constant.CACHE_USER_IDENTITY_LABLES_BCAR + userId);
        Object scarLables = cacheService.getObject(Constant.CACHE_USER_IDENTITY_LABLES_SCAR + userId);

        if (bcarLables == null || scarLables == null) {
            String SQL = "SELECT bcar_identity_lables,scar_identity_lables FROM tyt_user_sub where user_id=?";
            labels = this.getBaseDao().query(SQL, new Object[]{userId});
            if (labels != null) {
                if (labels[0] != null) {
                    cacheService.setObject(Constant.CACHE_USER_IDENTITY_LABLES_BCAR + userId, labels[0], AppConfig.getIntProperty("tyt.cache.user.time"));
                }
                if (labels[1] != null) {
                    cacheService.setObject(Constant.CACHE_USER_IDENTITY_LABLES_SCAR + userId, labels[1], AppConfig.getIntProperty("tyt.cache.user.time"));
                }
            }

        } else {
            labels[0] = bcarLables;
            labels[1] = scarLables;
        }
        return labels;
    }

    @Override
    public void updateIdentityLables(Long userId, String bcarIdentityLables, String scarIdentityLables) {
        String updateSQL = "UPDATE tyt_user_sub set bcar_identity_lables=?,scar_identity_lables=? where user_id=?";
        this.executeUpdateSql(updateSQL, new Object[]{bcarIdentityLables, scarIdentityLables, userId});
        cacheService.del(Constant.CACHE_USER_IDENTITY_LABLES_BCAR + userId);
        cacheService.del(Constant.CACHE_USER_IDENTITY_LABLES_SCAR + userId);
    }

    @SuppressWarnings("deprecation")
    @Override
    public UserCollectCountBean getUserCollectCount(String userId) {
        String sql = "SELECT bcarJobCollecetCount, bcarRecruitCollecetCount, scarJobCollecetCount, scarRecruitCollecetCount, goodsCollecetCount  FROM (SELECT COUNT(*) AS 'bcarJobCollecetCount', 1 AS 'temp' FROM tyt_collect_info tci, tyt_bcar_job tbj WHERE tci.`user_id`=:userId AND tci.`status`=0 AND tci.`type`=2 AND tci.`ms_id`=tbj.`id` AND tbj.`del_status`=0) t1 LEFT OUTER JOIN  ( (SELECT COUNT(*) AS 'bcarRecruitCollecetCount', 1 AS 'temp' FROM tyt_collect_info tci, tyt_bcar_recruit tbr  WHERE tci.`user_id`=:userId AND tci.`status`=0 AND tci.`type`=3 AND tci.`ms_id`=tbr.`id` AND tbr.`del_status`=0)) t2 ON t1.temp = t2.temp LEFT OUTER JOIN ( (SELECT COUNT(*) AS 'scarJobCollecetCount', 1 AS 'temp' FROM tyt_collect_info tci, tyt_scar_job tsj WHERE tci.`user_id`=:userId  AND tci.`status`=0 AND tci.`type`=4 AND tci.`ms_id`=tsj.`id` AND tsj.`del_status`=0)) t3 ON t1.temp = t3.temp LEFT OUTER JOIN ( (SELECT COUNT(*) AS 'scarRecruitCollecetCount', 1 AS 'temp'  FROM tyt_collect_info tci, tyt_scar_recruit tsr  WHERE tci.`user_id`=:userId  AND tci.`status`=0 AND tci.`type`=5 AND tci.`ms_id`=tsr.`id` AND tsr.`del_status`=0)) t4 ON t1.temp = t4.temp LEFT OUTER JOIN ( (SELECT COUNT(*) AS 'goodsCollecetCount', 1 AS 'temp' FROM tyt_transport_collect ttc WHERE ttc.`user_id`=:userId AND ttc.`status`=1)) t5 ON t1.temp = t5.temp";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("goodsCollecetCount", Hibernate.INTEGER);
        scalarMap.put("bcarJobCollecetCount", Hibernate.INTEGER);
        scalarMap.put("scarJobCollecetCount", Hibernate.INTEGER);
        scalarMap.put("bcarRecruitCollecetCount", Hibernate.INTEGER);
        scalarMap.put("scarRecruitCollecetCount", Hibernate.INTEGER);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("userId", userId);
        List<UserCollectCountBean> userCollectCountBeans = this.getBaseDao().search(sql, scalarMap, UserCollectCountBean.class, paramMap);
        return userCollectCountBeans.get(0);
    }

    /**
     * 用户发货数加1
     * @param userId
     */
    private void incrSendTptNumber(Long userId) {

        String sql = "update tyt_user_sub set send_tpt_number = ifnull(send_tpt_number, 0) + 1 "
                + " where user_id = ? ";

        Object[] params = {userId};
        this.getBaseDao().executeUpdateSql(sql, params);

    }

    @Override
    public TytUserSub saveChangeTytUserSub(Long userId) throws Exception {

        TytUserSub tytUserSub = this.getTytUserSubByUserId(userId);
        if (tytUserSub == null) {
            tytUserSub = new TytUserSub();
            tytUserSub.setSendTptNumber(1);
            tytUserSub.setSendTptType(sttLimitService.getUserSttLimit(userId).getType());
            tytUserSub.setUserId(userId);
            tytUserSub.setVerifyFlag(0);
            this.add(tytUserSub);
        } else {
            this.incrSendTptNumber(userId);
        }
        return tytUserSub;
    }

    /**
     * user_sub 表更新后清除缓存
     * @param tytUserSubList
     */
    private void clearUserSubCache(List<TytUserSub> tytUserSubList){
        for(TytUserSub oneUserSub : tytUserSubList) {
            Long userId = oneUserSub.getUserId();
            cacheService.del(Constant.CACHE_USERSUB_KEY + userId + "_" + TimeUtil.formatDateMonthTime(new Date()));
        }
    }

    /**
     * 校验设备id是否合法，某些情况会出现极光id无法获取情况
     * @param deviceIds
     * @return
     */
    private boolean checkDeviceId(String ... deviceIds){

        boolean checkResult = true;
        if(deviceIds != null) {
            String reg = "[\u4e00-\u9fa5]+";//中文正则
            Pattern pattern = Pattern.compile(reg);

            for (String oneDevice: deviceIds) {
                if(StringUtils.isNotBlank(oneDevice)){
                    Matcher matcher = pattern.matcher(oneDevice);
                    boolean result = matcher.find();

                    if(result){
                        //包含中文
                        checkResult = false;
                        break;
                    }
                }
            }
        }
        return checkResult;
    }

    /**
     * 登录时，修改之前登录人的deviceId
     * car 和 goods 只能有一个
     */
    private void kickOutOldDevice(Long userId, String carDeviceId, String goodsDeviceId) {

        if(!this.checkDeviceId(carDeviceId, goodsDeviceId)){
            logger.error("jg_device_id_null : userId : {} | carDeviceId : {} | goodsDeviceId : {}", userId, carDeviceId, goodsDeviceId);
            return;
        }

        if(StringUtils.isNotBlank(carDeviceId)){
            String hql = "from TytUserSub where userId != ? and carDeviceId = ?";
            List<TytUserSub> tytUserSubList = this.getBaseDao().find(hql, userId, carDeviceId);

            if(CollectionUtils.isNotEmpty(tytUserSubList)){
                List<Long> userIdList = tytUserSubList.stream().map(TytUserSub::getUserId).collect(Collectors.toList());

                String userIdStr = StringUtils.join(userIdList, ",");

                String sql = "update tyt_user_sub set car_device_id = '', utime=now() where user_id in (" + userIdStr + ") and car_device_id = ? " ;

                this.getBaseDao().executeUpdateSql(sql, new Object[]{carDeviceId});
                this.clearUserSubCache(tytUserSubList);
            }
        }else if(StringUtils.isNotBlank(goodsDeviceId)){
            String hql = "from TytUserSub where userId != ? and goodsDeviceId = ?";
            List<TytUserSub> tytUserSubList = this.getBaseDao().find(hql, userId, goodsDeviceId);

            if(CollectionUtils.isNotEmpty(tytUserSubList)){
                List<Long> userIdList = tytUserSubList.stream().map(TytUserSub::getUserId).collect(Collectors.toList());

                String userIdStr = StringUtils.join(userIdList, ",");

                String sql = "update tyt_user_sub set goods_device_id = '', utime=now() where user_id in (" + userIdStr + ") and goods_device_id = ? " ;

                this.getBaseDao().executeUpdateSql(sql, new Object[]{goodsDeviceId});
                this.clearUserSubCache(tytUserSubList);
            }

        }
    }

    @Override
    public void savePushCid(Long userId, String cid, Integer clientSign, String clientVersion, String osVersion, String deviceId,String carDeviceId,String goodsDeviceId) {

        TytUserSub tytUserSub = this.getTytUserSubByUserId(userId);
        if (null != tytUserSub) {
            String sql = "update tyt_user_sub set client_sign=?,cid=?,utime=now(),device_id=?,car_device_id=?,goods_device_id=? where user_id=?";
            if (tytUserSub.getClientSign()!=null){
                int sign = tytUserSub.getClientSign();
                if (2 == clientSign.intValue() || 3 == clientSign.intValue()) {
                    if ((sign == 2 || sign == 21 ||sign == 22) && 3 == clientSign.intValue()) {
                        sql = "update tyt_user_sub set client_sign=?,cid=?,notify_badge=0,utime=now(),device_id=? where user_id=?";
                    }else {
                        sql = "update tyt_user_sub set client_sign=?,cid=?,utime=now(),device_id=? where user_id=?";
                    }
                    this.getBaseDao().executeUpdateSql(sql, new Object[]{clientSign, cid, deviceId,userId});
                }else if (21 == clientSign.intValue() || 31 == clientSign.intValue()) {
                    if ((sign == 2 || sign == 21 ||sign == 22) && 31 == clientSign.intValue()) {
                        sql = "update tyt_user_sub set client_sign=?,car_notify_badge=0,utime=now(),car_device_id=? where user_id=?";
                    }else {
                        sql = "update tyt_user_sub set client_sign=?,utime=now(),car_device_id=? where user_id=?";
                    }
                    this.getBaseDao().executeUpdateSql(sql, new Object[]{clientSign, carDeviceId,userId});
                }else if (22 == clientSign.intValue() || 32 == clientSign.intValue()) {
                    if ((sign == 2 || sign == 21 ||sign == 22) && 32 == clientSign.intValue()) {
                        sql = "update tyt_user_sub set client_sign=?,goods_notify_badge=0,utime=now(),goods_device_id=? where user_id=?";
                    }else {
                        sql = "update tyt_user_sub set client_sign=?,utime=now(),goods_device_id=? where user_id=?";
                    }
                    this.getBaseDao().executeUpdateSql(sql, new Object[]{clientSign, goodsDeviceId,userId});
                }
            }else {
                this.getBaseDao().executeUpdateSql(sql, new Object[]{clientSign,cid,deviceId,carDeviceId,goodsDeviceId,userId});
            }
            tytPushUserService.savePushCid(userId, cid, clientSign, clientVersion, osVersion, deviceId,carDeviceId,goodsDeviceId);
            cacheService.del(Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date()));
            this.kickOutOldDevice(userId, carDeviceId, goodsDeviceId);

        }
    }

    @Override
    public void updateUserGroup(Long userId, int userGroup) {
        this.getBaseDao().executeUpdateSql(
                "UPDATE tyt.`tyt_user_sub` tus SET tus.`user_group`=? WHERE tus.`user_id`=?",
                new Object[]{userGroup, userId});
        String key=Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date());
        cacheService.del(key);
        this.insertUserGroupLog(userId,userGroup);


    }
//
//    @Override
//    public void rightCheck(Long userId, Integer type, ResultMsgBean result) {
//        RightsCheckBean checkBean = new RightsCheckBean(1);
//        TytUserSub userSub = userSubService.getTytUserSubByUserId(userId);
//        // 1：信用 2：找货历史 3：货源提醒
//        switch (type) {
//            case 1: // 1：信用
//                Integer userGroup = userSub.getUserGroup();
//                if (userGroup == null) {
//                    userGroup = 2;
//                }
//                // 非付费有效期内用户判断设备是否绑定
//                if (userGroup != Constant.RIGHTS_VIP
//                        && userSub.getBindStatus() != null
//                        && userSub.getBindStatus() == 1) {
//                    checkBean.setCanContinue(2);
//                    checkBean.setPromptContent("开通会员后可继续查看");
//                    checkBean.setPromptType(9);
//                } else if (userGroup == Constant.RIGHTS_EXPERIENCE_TIMEOUT) {
//                    checkBean.setCanContinue(2);
//                    checkBean.setPromptContent("开通会员后可继续查看");
//                    checkBean.setPromptType(6);
//                } else if (userGroup == Constant.RIGHTS_NORMAL_TIMEOUT) {
//                    checkBean.setCanContinue(2);
//                    checkBean.setPromptContent("开通会员后可继续查看");
//                    checkBean.setPromptType(7);
//                } else if (userGroup == Constant.RIGHTS_VIP_TIMEOUT) {
//                    checkBean.setCanContinue(2);
//                    checkBean.setPromptContent("开通会员后可继续查看");
//                    checkBean.setPromptType(8);
//                } else {
//                    TytUserIdentityAuth auth = authService.getByUserId(String.valueOf(userId));
//                    boolean identitySuc = auth.getIdentityStatus() == 1;
//                    // 身份认证，车辆认证
//                    if (!identitySuc) {
//                        if (userGroup == Constant.RIGHTS_EXPERIENCE) {
//                            checkBean.setCanContinue(2);
//                            checkBean.setPromptContent("为保障您和对方的财产安全，请先进行身份认证");
//                            checkBean.setPromptType(2);
//                        } else if (userGroup == Constant.RIGHTS_NORMAL) {
//                            checkBean.setCanContinue(2);
//                            checkBean.setPromptContent("为保障您和对方的财产安全，请先进行身份认证");
//                            checkBean.setPromptType(3);
//                        } else if (userGroup == Constant.RIGHTS_VIP) {
//                            checkBean.setCanContinue(2);
//                            checkBean.setPromptContent("为保障您和对方的财产安全，请先进行身份认证");
//                            checkBean.setPromptType(10);
//                        }
//                    } else if (userGroup == Constant.RIGHTS_NORMAL) {// 试用会员判断车辆认证
//                        User user = userService.getById(userId);
//                        if (!"1".equals(user.getIsCar())) {
//                            checkBean.setCanContinue(2);
//                            checkBean.setPromptContent("您未认证车辆，立即认证可继续使用试用会员");
//                            checkBean.setPromptType(4);
//                        }
//                    }
//                }
//                break;
//            case 2: // 2：找货历史
//                if (!canContinue(userSub)) {
//                    checkBean.setCanContinue(2);
//                    checkBean.setPromptContent("开通会员后可继续查看");
//                    checkBean.setPromptType(5);
//                }
//                break;
//            case 3: // 3：货源提醒
//                if (!canContinue(userSub)) {
//                    checkBean.setCanContinue(2);
//                    checkBean.setPromptContent("开通会员后可继续查看");
//                    checkBean.setPromptType(1);
//                }
//                break;
//        }
//        result.setData(checkBean);
//    }

    @Override
    public void rightCheckV5930(Long userId, Integer type, ResultMsgBean result) {
        RightsCheckBean checkBean = new RightsCheckBean(1);
        TytUserSub userSub = userSubService.getTytUserSubByUserId(userId);
        // 1：信用 2：找货历史 3：货源提醒
        switch (type) {
            case 1: // 1：信用
                logger.info("查询信用传入的用户ID为:" + userId);
                TytUserIdentityAuth auth = authService.getByUserId(String.valueOf(userId));
                boolean identitySuc = auth.getIdentityStatus() == 1;
                // 身份认证，车辆认证
                if (!identitySuc) {
                    checkBean.setCanContinue(2);
                    checkBean.setPromptContent("为保障您和对方的财产安全，请先进行身份认证");
                }
                break;
        }
        result.setData(checkBean);
    }

//    测试无问题后删除
//    @Override
//    public boolean canContinue(TytUserSub userSub) {
//        Integer userGroup;
//        Integer bindStatus;
//        // 会员直接过
//        if ((userGroup = userSub.getUserGroup()) != null
//                && userGroup == Constant.RIGHTS_VIP) {
//            return true;
//        }
//        return userSub != null
//                && (bindStatus = userSub.getBindStatus()) != null
//                && bindStatus == 2
//                && userGroup != null
//                && (userGroup == Constant.RIGHTS_EXPERIENCE
//                || userGroup == Constant.RIGHTS_NORMAL);
//    }


//    测试无问题后删除
//    @Override
//    public boolean isCallPhone(Long userId) {
//        if(userId==null || userId==0){
//            return false;
//        }
//        TytUserSub tytUserSub= getTytUserSubByUserId(userId);
//        if(tytUserSub==null){
//            return false;
//        }
//
//        int  userGroup=tytUserSub.getUserGroup()==null? -1 : tytUserSub.getUserGroup();
//        int  bindStatus=tytUserSub.getBindStatus()==null? -1 : tytUserSub.getBindStatus();
//
//        if(userGroup == Constant.RIGHTS_VIP){
//             return  true;
//        }
//
//        if(bindStatus != 2){
//            return  false;
//        }
//        boolean isCall=  (userGroup == Constant.RIGHTS_EXPERIENCE ||userGroup == Constant.RIGHTS_NORMAL);
//
//
//        return isCall;
//
//
//    }

    @Override
    public void updateLevelBeginTime(Long userId, Date date) {
        this.getBaseDao().executeUpdateSql("update tyt.tyt_user_sub tus set tus.level2_biging_time=? where tus.user_id=?",
                new Object[]{date, userId});
    }

    private void insertUserGroupLog(Long userId,int userGrop){
        this.getBaseDao().executeUpdateSql("insert into tyt_user_group_log(user_id,user_group,c_time) values(?,?,NOW())",
                new Object[]{userId, userGrop});
    }

    @Override
    public Integer getCountNum(int port, String clientId) {

        if(port == 1){
            BigInteger count = this.getBaseDao().query(
                    "select count(*) from tyt_user user left join tyt_user_sub sub on user.id = sub.user_id where user.car_user_sign = 1 and sub.bind_cliendid = ?",
                    new Object[]{clientId});
            return count.intValue();
        }else if (port == 2){
            BigInteger count = this.getBaseDao().query(
                    "select count(*) from tyt_user user left join tyt_user_sub sub on user.id = sub.user_id where user.goods_user_sign = 1 and sub.bind_cliendid = ?",
                    new Object[]{clientId});
            return count.intValue();
        }else{
            return 0;
        }
    }

    @Override
    public void updatePublishNum(Long userId, int publishNum) {
        this.getBaseDao().executeUpdateSql(
                "UPDATE tyt.`tyt_user_sub` tus SET tus.`publish_num`=? WHERE tus.`user_id`=?",
                new Object[]{publishNum, userId});
        String key=Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date());
        cacheService.del(key);
    }
}
