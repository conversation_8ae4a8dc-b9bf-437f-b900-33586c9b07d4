package com.tyt.user.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.User;
import com.tyt.model.UserIdentity;
import com.tyt.user.dao.UserIdentityDao;
import com.tyt.user.service.UserIdentityService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
@Service("userIdentityService")
public class UserIdentityServiceImpl extends BaseServiceImpl<UserIdentity,Long> implements UserIdentityService{

	@Resource(name = "userService")
	private UserService userService;
	
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
	@Resource(name="userIdentityDao")
	public void setBaseDao(BaseDao<UserIdentity, Long> userIdentityDao) {
	        super.setBaseDao(userIdentityDao);
	}

	@Override
	public UserIdentity getEnabled(Long userId,Integer enabled) {
		StringBuffer sql=new StringBuffer("select * from tyt_user_identity where user_id=? and enabled=? order by id desc");
		List<UserIdentity> userIdentityList=((UserIdentityDao)(this.getBaseDao())).search(sql.toString(), new Object[]{userId,enabled}, 1, 1);
		if (userIdentityList!=null&& userIdentityList.size()>0){
			return userIdentityList.get(0);
		}
		return null;
	}

	@Override
	public void addIdentity(UserIdentity identity) throws Exception {
		/*添加未认证中的实名认证信息*/
		this.add(identity);
		/*删除用户缓存信息*/
		cacheService.del(Constant.CACHE_USER_KEY+identity.getUserId());
		/*更新用户验证信息到数据库*/
		userService.updateVeryCode(identity.getUserId(), 2);
		/*提取用户信息*/
		User user=userService.getById(identity.getUserId());
		/*用户信息放缓存*/
		cacheService.setObject(Constant.CACHE_USER_KEY+user.getId(), user, AppConfig.getIntProperty("tyt.cache.user.time"));
	}

	@Override
	public boolean isExit(Long userId) {
		return ((UserIdentityDao)(this.getBaseDao())).isExit(userId);
	}
	@Override
	public void updateDisabled(Long userId, Integer enabled) {
		((UserIdentityDao)(this.getBaseDao())).updateDisabled(userId,enabled);
		
	}
}
