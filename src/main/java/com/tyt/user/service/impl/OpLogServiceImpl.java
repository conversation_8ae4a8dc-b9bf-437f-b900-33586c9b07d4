package com.tyt.user.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.OpLog;
import com.tyt.model.OpLogStat;
import com.tyt.model.UserLogTime;
import com.tyt.user.dao.OpLogDao;
import com.tyt.user.service.OpLogService;

import java.util.List;

@Service("opLogService")
public class OpLogServiceImpl extends BaseServiceImpl<OpLog,Long> implements OpLogService {
	 @Resource(name="opLogDao")
	 public void setBaseDao(BaseDao<OpLog, Long> opLogDao) {
	     super.setBaseDao(opLogDao);
	 }

    @Override
    public List<OpLogStat> getOpLogStatList(String beginTime, String endTime) {
        return ((OpLogDao)getBaseDao()).getOpLogStatList(beginTime,endTime);
    }

	@Override
	public List<UserLogTime> getUserIdTimeList(String sql) {
		// TODO Auto-generated method stub
		return ((OpLogDao)getBaseDao()).getUserIdTimeList(sql);
	}
}
