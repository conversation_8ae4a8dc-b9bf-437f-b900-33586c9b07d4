package com.tyt.user.service.impl;

import java.util.Date;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.Version;
import com.tyt.user.dao.VersionDao;
import com.tyt.user.service.VersionService;
import com.tyt.util.Constant;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:10
 */
@Service("versionService")
public class VersionServiceImpl extends BaseServiceImpl<Version,Long> implements VersionService {
    
	
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
    @Resource(name="versionDao")
    public void setBaseDao(BaseDao<Version, Long> versionDao) {
        super.setBaseDao(versionDao);
    }

	@Override
	public Version getEnabledVersion() throws Exception {
		Version version=(Version) cacheService.getObject(Constant.CACHE_VERSION_KEY);
		if(version==null){
			version=((VersionDao)(this.getBaseDao())).getEnabledVersion();
			cacheService.setObject(Constant.CACHE_VERSION_KEY, version, Constant.CACHE_EXPIRE_TIME_24H);
		}
		return version;
	}

	@Override
	public boolean setVersionDisabled(Long id) throws Exception{
		return ((VersionDao)(this.getBaseDao())).setVersionDisabled(id);
	}

	@Override
	public void addVersion(Version version) throws Exception {
		version.setCreateTime(new Date());
		version.setUpdateTime(new Date());
		version.setStatus(1);
		version.setPcIsAutoUpgrade("0");
		((VersionDao)(this.getBaseDao())).insert(version);
	}


}
