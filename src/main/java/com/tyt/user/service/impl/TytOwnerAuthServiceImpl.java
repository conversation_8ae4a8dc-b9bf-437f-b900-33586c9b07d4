package com.tyt.user.service.impl;

import com.tyt.config.util.AppConfig;
import com.tyt.plat.entity.base.TytOwnerAuth;
import com.tyt.plat.mapper.base.TytOwnerAuthMapper;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytOwnerAuthService;
import com.tyt.util.XXTea;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2022/12/27
 */
@Service("tytOwnerAuthService")
public class TytOwnerAuthServiceImpl implements TytOwnerAuthService {
    @Resource
    private TytConfigService tytConfigService;
    @Resource
    private TytOwnerAuthMapper tytOwnerAuthMapper;

    /**
     * 获取官方授权昵称
     *
     * @param userId
     * @return
     */
    @Override
    public String getAuthNameTea(Long userId) {
        if (userId != null) {
            String checkAuthName = tytConfigService.getStringValue("tyt:plat:config:checkAuthName");
            if (StringUtils.isNotBlank(checkAuthName) && "1".equals(checkAuthName)) {
                Example example = new Example(TytOwnerAuth.class);
                example.createCriteria().andEqualTo("userId", userId).andEqualTo("status", 2);
                TytOwnerAuth tytOwnerAuth = tytOwnerAuthMapper.selectOneByExample(example);
                if (tytOwnerAuth != null) {
                    return XXTea.Encrypt(tytOwnerAuth.getAuthName(), AppConfig.getProperty("tyt.xxtea.key"));
                }
            }
        }
        return null;
    }

    /**
     * 官方授权昵称加密
     *
     * @param authName
     * @return
     */
    @Override
    public String getAuthNameTea(String authName) {
        if (StringUtils.isNotBlank(authName)) {
            String checkAuthName = tytConfigService.getStringValue("tyt:plat:config:checkAuthName");
            if (StringUtils.isNotBlank(checkAuthName) && "1".equals(checkAuthName)) {
                return XXTea.Encrypt(authName, AppConfig.getProperty("tyt.xxtea.key"));
            }
        }
        return null;
    }
}
