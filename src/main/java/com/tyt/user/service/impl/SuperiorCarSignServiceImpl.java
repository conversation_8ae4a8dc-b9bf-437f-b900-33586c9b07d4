package com.tyt.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.messagecenter.core.enums.BusinessTagEnum;
import com.tyt.messagecenter.core.enums.NativePageEnum;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.*;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.bean.SuperiorCarSignStatusResBean;
import com.tyt.user.service.CarService;
import com.tyt.user.service.SuperiorCarSignService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service("superiorCarSignService")
public class SuperiorCarSignServiceImpl extends BaseServiceImpl<TytSuperiorCarSign,Long>  implements SuperiorCarSignService {

	@Resource(name="superiorCarSignDao")
    public void setBaseDao(BaseDao<TytSuperiorCarSign, Long> blockerDao) {
        super.setBaseDao(blockerDao);
    }

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityAuthService;

    @Resource(name = "carService")
    private CarService carService;

    @Resource(name =  "userPermissionService")
    private UserPermissionService userPermissionService;

    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Override
    public SuperiorCarSignStatusResBean getSignStatus(Long userId) {
        SuperiorCarSignStatusResBean resBean = SuperiorCarSignStatusResBean.builder().status(2).isAuth(1).isCarAuth(1).isVip(1).build();
        List<TytSuperiorCarSign> superiorCarSigns = this.getBaseDao().search(TytSuperiorCarSign.builder().userId(userId).build());
        if (superiorCarSigns!=null&&superiorCarSigns.size()>0) resBean.setStatus(1);
        //检查用户实名状态
        TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(userId));
        if (identityAuth==null||identityAuth.getIdentityStatus()==1||identityAuth.getIdentityStatus()==2) resBean.setIsAuth(0);
        //检查用户车辆状态
        List<Car> listByUserId = carService.getListByUserId(userId);
        if (listByUserId!=null) {
            List<Car> newCars = listByUserId.stream().filter(car -> ("0".equals(car.getAuth())||"1".equals(car.getAuth()))).collect(Collectors.toList());
            if (newCars.size()>0) {
                resBean.setIsCarAuth(0);
            }
        }
        //检查付费会员
        List<UserPermission> userPermission = userPermissionService.getListByUserIdAndType(userId,"100101");
        if (userPermission!=null&&userPermission.size()>0) resBean.setIsVip(0);
        return resBean;
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public ResultMsgBean sign(Long userId) throws Exception{
        //检查是否签约
        List<TytSuperiorCarSign> superiorCarSigns = this.getBaseDao().search(TytSuperiorCarSign.builder().userId(userId).build());
        if (superiorCarSigns!=null&&superiorCarSigns.size()>0)
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_SIGNED_ERROR, "用户已签约"));
        //检查用户实名状态
        TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(userId));
        if (identityAuth==null||identityAuth.getIdentityStatus()==0||identityAuth.getIdentityStatus()==3)
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_IDENTITY_ERROR, "用户未实名认证/实名未通过"));
        //检查用户车辆状态
        List<Car> listByUserId = carService.getListByUserId(userId);
        if (listByUserId==null)
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_CAR_ERROR, "用户未车辆认证/车辆认证未通过"));
        List<Car> newCars = listByUserId.stream().filter(car -> ("0".equals(car.getAuth())||"1".equals(car.getAuth()))).collect(Collectors.toList());
        if (newCars.size()<=0)
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_CAR_ERROR, "用户未车辆认证/车辆认证未通过"));

        //检查付费会员
        List<UserPermission> userPermission = userPermissionService.getListByUserIdAndType(userId,"100101");
        if (userPermission==null||userPermission.size()<=0)
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_VIP_ERROR, "用户非付费会员"));

        int signStatus = 1;
        if (identityAuth.getIdentityStatus()==2) signStatus = 0;
        List<Car> cars = listByUserId.stream().filter(car -> "0".equals(car.getAuth())).collect(Collectors.toList());
        if (newCars.size()==cars.size()) signStatus = 0;

        TytSuperiorCarSign dao = TytSuperiorCarSign.builder()
                .userId(userId)
                .signStatus(signStatus)
                .signTime(new Date())
                .updateTime(new Date())
                .build();
        this.getBaseDao().insert(dao);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK,"签约成功");
        //如果签约成功但是未审核 接口返回30008
        if (signStatus==0) {
            resultMsgBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGNED_NO_CHECK_ERROR);
            resultMsgBean.setMsg("签约成功，等待审核");
        }
        return resultMsgBean;
        //签约完成发短信和PUSH
//        if (signStauts==1) sendMsg(identityAuth);
    }

    private void sendMsg(TytUserIdentityAuth identityAuth) {
        MessagePushBase messagePushBase = new MessagePushBase();
        messagePushBase.addUserId(identityAuth.getUserId());
        messagePushBase.setTitle("证件已审核通过");
        messagePushBase.setContent("您的证件已审核通过，更多优质货源等您来选，快来接单吧！");
        messagePushBase.setRemarks("优车签约成功通知");
        messagePushBase.setPushType(1, 0);

        //PUSH
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
        notifyMessage.openWithNativePage(NativePageEnum.transport_search);

        //短信
        ShortMessageBean shortMessage = new ShortMessageBean();
        shortMessage.setCellPhone(identityAuth.getMobile());
        shortMessage.setContent("您的证件已审核通过，更多优质货源等您来选，快来接单吧！");
        shortMessage.setRemark("优车签约成功短信");

        messageCenterPushService.sendMultiMessage(shortMessage, null, notifyMessage);
    }

    @Override
    public TytSuperiorCarSign getSuperiorCarSign(Long userId) {
        List<TytSuperiorCarSign> carSignList = this.getBaseDao().search(TytSuperiorCarSign.builder().userId(userId).build());
        return CollectionUtil.isNotEmpty(carSignList) ? carSignList.get(0) : null;
    }


}
