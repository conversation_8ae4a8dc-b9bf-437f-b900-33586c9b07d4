package com.tyt.user.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.InviteFriends;
import com.tyt.model.User;
import com.tyt.user.dao.InviteFriendsDao;
import com.tyt.user.querybean.Friends;
import com.tyt.user.service.InviteFriendsService;
import com.tyt.user.service.UserService;
import com.tyt.util.SendMessage;
import com.tyt.util.TimeUtil;

@Service("inviteFriendsService")
public class InviteFriendsServiceImpl extends BaseServiceImpl<InviteFriends, Long> implements InviteFriendsService{

	@Resource(name="inviteFriendsDao")
	public void setBaseDao(BaseDao<InviteFriends, Long> inviteFriendsDao) {
	        super.setBaseDao(inviteFriendsDao);
	}
	
	@Resource(name = "userService")
	private UserService userService;
	

//	@Override
//	public void saveInviteFriends(Long userId,String friends,Integer platId) throws Exception{
//		InviteFriends friend=new InviteFriends();
//		User user=null;
//		for(Friends frd:StringToList(friends)){
//			if(getByUserIdAndFriendCell(userId,frd.getTel())){
//			  addCounts(userId, frd.getTel());	
//			}else{
//				friend.setUserId(userId);
//				friend.setFriendCell(frd.getTel());
//				friend.setFriendName(frd.getName());
//				friend.setCtime(TimeUtil.getTimeStamp());
//				friend.setPlatId(platId);
//				add(friend);
//				user=userService.getById(userId);
//				SendMessage.sendMessage(frd.getTel(), "您的好友"
//				     +user!=null?(user.getTrueName()==null?"":user.getTrueName()):""
//				    		 +"邀请您成为特运通VIP用户,详情请见www.teyuntong.com");
//			}
//		}
//		
//	}
   /**
    * 字符串到对象集合
    * @param friends
    * @return
    */
   private List<Friends> StringToList(String friends){
	   String[] friendsArr=friends.split(",");
	   List<Friends> friendsList=new ArrayList<Friends>();
	   for(String friend:friendsArr){
		   String[] friendArr=friend.split(":");
		   String[] telsArr=friendArr[1].split("<");
		   for(String tel:telsArr){
			   Friends frd=new Friends(filterString(friendArr[0]),filterString(tel));
			   friendsList.add(frd);
			   frd=null; 
		   }
	   }
	   return friendsList;
   }
   /**
    * 过滤字符串，只保留某些值
    * @param str
    * @return
    */
   private String filterString(String str){
	   str=str.replaceAll("[^0-9a-zA-Z\u4e00-\u9fa5，,+:：]+","");
	   if(str.startsWith("+86")){
		   str=str.substring(3);
	   }
	   return str;
   }

	@Override
	public boolean getByUserIdAndFriendCell(Long userId,String friendCell) throws Exception{
			
		  StringBuffer sql = new StringBuffer();
	      sql.append(" entity.userId =").append(userId);
	      sql.append(" and entity.friendCell ='").append(friendCell.trim()).append("'");
//	      sql.append(" and date_format(entity.ctime,'%Y-%m-%d')='").append(TimeUtil.formatDate(TimeUtil.today())).append("'");
	      List<InviteFriends> friendList=this.getList(sql.toString(), null);
		  return friendList.size()>0;
	}



	@Override
	public void addCounts(Long userId, String friendCell) throws Exception{
		((InviteFriendsDao) this.getBaseDao()).addCounts(userId, friendCell);
	}
	


}
