package com.tyt.user.service.impl;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.tyt.cache.CacheService;
import com.tyt.model.User;
import com.tyt.plat.entity.base.TytUserUpgradeInfo;
import com.tyt.plat.mapper.base.TytUserUpgradeInfoMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tyt.model.TytVersionNew;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytVersionNewService;

@Service("versionBusiness")
@Slf4j
public class VersionBusinessService {

	/* 开关Service */
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	/* 新版本Version service */
	@Resource(name = "tytVersionNewService")
	TytVersionNewService tytVersionNewService;
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Autowired
	private UserService userService;

	@Autowired
	private TytUserUpgradeInfoMapper tytUserUpgradeInfoMapper;

	/**
	 * 版本检测
	 * @param isLogin是否登录类检测(包括正常登陆、模拟登陆)
	 * @param params
	 * @param request
	 * @param response
	 * @return
	 */
	public Map<String, Object> checkVersion(boolean isLogin,
			Map<String, String> params) {

		try{
			/* 解析客户端传过来的原始值 */
			String userVersion = params.get("clientVersion");
			Integer clientType = Integer.parseInt(params.get("clientSign"));
			String channelCode = params.get("channelCode");
			String downloadType = getDownloadTypeByChannelCode(channelCode);
			/*根据开关判断是否提示升级,false表示不提示,true表示根据数据库的参数配置决定*/
//		if (!getUpgradeDirection(isLogin, clientType, downloadType))
//			return null;
			Long userId = null;
			String userIdStr = params.get("userId");
			String cellPhone = params.get("cellPhone");
			if (Integer.parseInt(userVersion) >= 6670 || StringUtils.isNotBlank(userIdStr) || StringUtils.isNotBlank(cellPhone)){
				if (StringUtils.isBlank(userIdStr)){
					if (StringUtils.isBlank(cellPhone) ){
						return null;
					}
					User user = userService.getUserByCellphone(cellPhone);
					if (user == null){
						return null;
					}
					userId = user.getId();
				}else {
					userId = Long.parseLong(userIdStr);
				}
			}

			/*根据数据库配置判断是否升级*/
			return checkVersionNew(userVersion, clientType, downloadType, userId, channelCode);
		}catch (Exception e){
			log.error("版本检测异常", e);
			return null;
		}
	}

	private Map<String, Object> checkVersionNew(
			String userVersion, Integer clientType, String downloadType, Long userId, String channelCodeStr) {
		String itemKey = userVersion + "_" + clientType + "_" + downloadType;
		TytVersionNew versionNew = (TytVersionNew) RedisUtil.getObjectMapValue(Constant.VERSION_NEW_BY_CLIENT, itemKey);
		Map<String, Object> upgradeStrategyMap = new ConcurrentHashMap<String, Object>();
		if(versionNew == null){
			/* 获取相应的版本信息 */
			versionNew = tytVersionNewService.getByClient(clientType,
				userVersion, 1, downloadType);
			RedisUtil.mapObjectPut(Constant.VERSION_NEW_BY_CLIENT,itemKey,versionNew);
			RedisUtil.expire(Constant.VERSION_NEW_BY_CLIENT,(int)Constant.CACHE_EXPIRE_TIME_24H);
		}

		if (versionNew == null) {
			updateUpgradeStatus(userId, userVersion, clientType);
			return null;
		}
		TytUserUpgradeInfo upgradeInfo = null;
		if (null != userId){
			upgradeInfo = tytUserUpgradeInfoMapper.getByUserIdAndUpgradeId(userId, versionNew.getId());
			if (upgradeInfo != null && upgradeInfo.getUpgradeType() == 2){ //不升
				updateUpgradeStatus(userId, userVersion, clientType);
				return null;
			}
		}
		if (upgradeInfo == null && versionNew.getUpgradeType().equals("2")){//不升
			updateUpgradeStatus(userId, userVersion, clientType);
			return null;
		}
		if (clientType != 31 && clientType != 32 && versionNew.getGrayPeriod() != null && upgradeInfo == null && versionNew.getGrayPeriod() > 0) {
			if (StringUtils.isNotBlank(channelCodeStr)) {
				log.info("灰度已开启，channelCode : {}", channelCodeStr);
				int channelCode = Integer.parseInt(channelCodeStr);
				if (channelCode != 1 && channelCode != 15) {
					return null;
				}
			}
		}

		upgradeStrategyMap.put("version", versionNew.getVersion());
		if (upgradeInfo != null){
			upgradeStrategyMap.put("type", upgradeInfo.getUpgradeType().toString());
		}else {
			upgradeStrategyMap.put("type", versionNew.getUpgradeType());
		}
		upgradeStrategyMap.put("url", versionNew.getDownloadUrl());
		upgradeStrategyMap.put("contentNew", versionNew.getShowContentNew());
		upgradeStrategyMap.put("appMarket", versionNew.getAppMarket());
		return upgradeStrategyMap;
	}

	private void updateUpgradeStatus(Long userId, String userVersion, Integer clientType){
		if (null == userId){
			return;
		}
		List<TytUserUpgradeInfo> userList = tytUserUpgradeInfoMapper.getByUser(userId);
		if (CollectionUtils.isEmpty(userList)){
			return;
		}
		for (TytUserUpgradeInfo userUpgradeInfo : userList) {
			TytVersionNew versionNew = tytVersionNewService.getById(userUpgradeInfo.getUpgradeTableId());
			if (versionNew == null || versionNew.getStatus().equals("0") || !versionNew.getClientType().equals(clientType.toString())){
				continue;
			}
			if(Integer.parseInt(versionNew.getVersion()) <= Integer.parseInt(userVersion)){
				tytUserUpgradeInfoMapper.updateStatusById(userVersion, userUpgradeInfo.getId());
			}
		}
	}

	/**
	 * 根据渠道号获取下载类型
	 *
	 * @param channelCode
	 *            26:企业渠道号,其他:默认为个人渠道号
	 * @return
	 */
	private String getDownloadTypeByChannelCode(String channelCode) {
		String downloadType = "1";
		if (channelCode != null && channelCode.trim().length() > 0
				&& channelCode.trim().equals("26")) {
			downloadType = "2";
		}
		return downloadType;
	}

	/**
	 * 后台升级开关控制:IOS个人版、企业版分别加检测类开关,登陆类开关,Android加检测类开关、登陆类开关;0关 1开
	 * 登陆类包括正常登陆、模拟登陆;检测类包括APP启动自检测跟用户手动检测
	 * androidLoginUpgradeOnOff:安卓登陆类开关、androidCheckUpgradeOnOff:安卓检测类开关、
	 * iosLoginPersonalUpgradeOnOff:ios个人登陆类开关、iosLoginEnterpriseUpgradeOnOff:ios企业版登陆类开关、
	 * iosCheckPersonalUpgradeOnOff:ios企业版登陆类开关、iosCheckEnterpriseUpgradeOnOff:ios企业版检测类开关
	 * pcLoginUpgradeOnOff:pc登陆开关、pcCheckUpgradeOnOff:pc检测类开关
	 * @param isLogin是否登录类检测
	 * @param clientType客户端类型1：PC;2：android; 3:ios
	 * @param downloadType下载类型1个人版2企业版
	 * @return true:走正常升级流程;false:本次不用升级
	 */
	private boolean getUpgradeDirection(boolean isLogin, Integer clientType,
			String downloadType) {

		Integer switchOnOff = 1;
		if (isLogin) {
			if(clientType == 1){
				switchOnOff = tytConfigService
						.getIntValue("pcLoginUpgradeOnOff");
			}else if (clientType == 2) {
				switchOnOff = tytConfigService
						.getIntValue("androidLoginUpgradeOnOff");
			} else if (clientType == 3) {
				if (downloadType.equals("1")) {
					switchOnOff = tytConfigService
							.getIntValue("iosLoginPersonalUpgradeOnOff");
				} else if (downloadType.equals("2")) {
					switchOnOff = tytConfigService
							.getIntValue("iosLoginEnterpriseUpgradeOnOff");
				}
			}
		} else {
			if(clientType == 1){
				switchOnOff = tytConfigService
						.getIntValue("pcCheckUpgradeOnOff");
			}else if (clientType == 2) {
				switchOnOff = tytConfigService
						.getIntValue("androidCheckUpgradeOnOff");
			} else if (clientType == 3) {
				if (downloadType.equals("1")) {
					switchOnOff = tytConfigService
							.getIntValue("iosCheckPersonalUpgradeOnOff");
				} else if (downloadType.equals("2")) {
					switchOnOff = tytConfigService
							.getIntValue("iosCheckEnterpriseUpgradeOnOff");
				}
			}
		}
		return switchOnOff!=null&&switchOnOff == 1;
	}

}
