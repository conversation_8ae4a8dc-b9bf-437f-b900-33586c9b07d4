package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.User;
import com.tyt.user.bean.UserBaseInfo;
import com.tyt.user.service.InternalUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class InternalUserServiceImpl extends BaseServiceImpl<User, Long> implements InternalUserService {
    @Resource(name = "userDao")
    public void setBaseDao(BaseDao<User, Long> userDao) {
        super.setBaseDao(userDao);
    }

    @Override
    public UserBaseInfo getByUserId(Long userId) {
        User user = this.getBaseDao().findById(userId);
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        if (Objects.isNull(user)) {
            userBaseInfo.setCheckResult(false);
            return userBaseInfo;
        }
        userBaseInfo.setCheckResult(true);
        userBaseInfo.setCellPhone(user.getCellPhone());
        userBaseInfo.setIdCard(user.getIdCard());
        userBaseInfo.setTrueName(user.getTrueName());
        userBaseInfo.setUserId(user.getId());
        return userBaseInfo;
    }
}
