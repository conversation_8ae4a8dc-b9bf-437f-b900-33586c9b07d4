package com.tyt.user.service.impl;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Advice;
import com.tyt.user.dao.AdviceDao;
import com.tyt.user.service.AdviceService;
@Service("adviceService")
public class AdviceServiceImpl extends BaseServiceImpl<Advice,Long> implements AdviceService{
	@Autowired
	private AdviceDao adviceDao;
	@Resource(name="adviceDao")
	public void setBaseDao(BaseDao<Advice, Long> adviceDao) {
	        super.setBaseDao(adviceDao);
	}
}
