package com.tyt.user.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserStaff;
import com.tyt.user.querybean.UserStaffBean;
import com.tyt.user.service.TytUserStaffService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

/**
 * @description:
 * @author: lizhao
 */
@Service("tytUserStaffService")
public class TytUserStaffServiceImpl extends BaseServiceImpl<TytUserStaff, Long> implements TytUserStaffService {

    @Resource(name = "tytUserStaffDao")
    public void setBaseDao(BaseDao<TytUserStaff, Long> tytUserStaff) {
        super.setBaseDao(tytUserStaff);
    }

    /**
     * 通过userID查询
     *
     * @param userId
     * @return staff list
     */
    @Override
    public List<TytUserStaff> getTytUserStaffByUserId(Long userId) {
        TytUserStaff search = new TytUserStaff();
        search.setUserId(userId);
        List<TytUserStaff> list = this.getBaseDao().search(search);
        return list;
    }

    @Override
    public ResultMsgBean addStaff(TytUserStaff tytUserStaff){
        ResultMsgBean result = new ResultMsgBean();
        if(StringUtils.isBlank(tytUserStaff.getStaffName())){
            result.setCode(ReturnCodeConstant.STAFF_PHONE_ERROR);
            result.setMsg("姓名未填写");
            return result;
        }
        if(StringUtils.isNotBlank(tytUserStaff.getStaffPhone())){
            String[] split = tytUserStaff.getStaffPhone().split(",");

            if(split.length >3){
                result.setCode(ReturnCodeConstant.STAFF_PHONE_ERROR);
                result.setMsg("手机号数量过多");
                return result;
            }
            for (int i = 0; i < split.length; i++) {
                //验证手机号不为空 1开头的长度为11位的数字
                if(StringUtils.isEmpty(split[i]) || split[i].length() != 11
                    || !NumberUtils.isNumber(split[i]) || !split[i].startsWith("1")){
                    return new ResultMsgBean(ReturnCodeConstant.STAFF_PHONE_ERROR,"手机号格式错误");
                }
            }
            Set<String> set = new TreeSet<>();
            for (String i : split) {
                set.add(i);
            }
            if(set.size() != split.length){
                result.setMsg("当前号码您已添加过啦～");
                result.setCode(ReturnCodeConstant.STAFF_PHONE_ERROR);
                return result;
            }
        }else{
            result.setCode(ReturnCodeConstant.STAFF_PHONE_ERROR);
            result.setMsg("手机号未填写");
            return result;
        }
        tytUserStaff.setCreateTime(new Date());
        this.add(tytUserStaff);
        return result;
    }

    @Override
    public ResultMsgBean updateStaff(UserStaffBean tytUserStaff) {
        ResultMsgBean result = new ResultMsgBean();
        if(StringUtils.isBlank(tytUserStaff.getStaffName())){
            result.setCode(ReturnCodeConstant.STAFF_PHONE_ERROR);
            result.setMsg("姓名未填写");
            return result;
        }
        if(StringUtils.isNotBlank(tytUserStaff.getStaffPhone())){
            String[] split = tytUserStaff.getStaffPhone().split(",");
            if(split.length >3){
                result.setCode(ReturnCodeConstant.STAFF_PHONE_ERROR);
                result.setMsg("手机号数量过多");
                return result;
            }
            for (int i = 0; i < split.length; i++) {
                //验证手机号不为空 1开头的长度为11位的数字
                if(StringUtils.isEmpty(split[i]) || split[i].length() != 11
                    || !NumberUtils.isNumber(split[i]) || !split[i].startsWith("1")){
                    return new ResultMsgBean(ReturnCodeConstant.STAFF_PHONE_ERROR,"手机号格式错误");
                }
            }
            Set<String> set = new TreeSet<>();
            for (String i : split) {
                set.add(i);
            }
            if(set.size() != split.length){
                result.setMsg("当前号码您已添加过啦～");
                result.setCode(ReturnCodeConstant.STAFF_PHONE_ERROR);
                return result;
            }
        }else{
            result.setCode(ReturnCodeConstant.STAFF_PHONE_ERROR);
            result.setMsg("手机号未填写");
            return result;
        }
        TytUserStaff staff = this.getBaseDao().findById(tytUserStaff.getId());
        staff.setStaffName(tytUserStaff.getStaffName());
        staff.setStaffPhone(tytUserStaff.getStaffPhone());
        staff.setUpdateTime(new Date());
        this.getBaseDao().update(staff);
        return result;
    }

    @Override
    public Integer getStaffCountByUserId(Long userId) {
        String sql = "select count(*) from tyt_user_staff where user_id=? ";
        BigInteger c = this.getBaseDao().query(sql, new Object[] { userId});
        if (c != null) {
            return c.intValue();
        }
        return 0;
    }
}
