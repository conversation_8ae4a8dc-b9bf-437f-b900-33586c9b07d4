package com.tyt.user.service.impl;

import com.tyt.plat.entity.base.AccountUpdateLog;
import com.tyt.plat.mapper.base.AccountUpdateLogMapper;
import com.tyt.user.service.AccountUpdateLogService;
import com.tyt.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户手机号变更记录
 * @date 2023/09/05 11:47
 */
@Service
public class AccountUpdateLogServiceImpl implements AccountUpdateLogService {
    @Autowired
    private AccountUpdateLogMapper accountUpdateLogMapper;
    @Override
    public Integer countByTimePeriod(Long userId, Integer day) {
        String startTime = TimeUtil.minusDays(day);
        return accountUpdateLogMapper.countByTimePeriod(userId, startTime);
    }

    @Override
    public AccountUpdateLog getRecentAccountUpdateLog(Long userId) {
        return accountUpdateLogMapper.selectRecentAccountUpdateLog(userId);
    }
}
