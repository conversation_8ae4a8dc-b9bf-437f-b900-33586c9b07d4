package com.tyt.user.service;

import java.util.Map;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytConfig;

public interface TytConfigService extends BaseService<TytConfig, Long>{
  
  public TytConfig getValue(String key);
  
  public Integer getIntValue(String key);
  public Integer getIntValue(String key, int defaultValue);
  public Integer fetchRecommendIntValue(String key);
  public Integer fetchRecommendIntValue(String key, int defaultValue);
  public String getStringValue(String key);
  public String getStringValue(String key, String defaultValue);
  public String fetchRecommendStringValue(String key);
  public String fetchRecommStrValue(String key, String defaultValue);

    /**
     *
     * @param counts 限制数量
     */
  public void update(String counts);
  
  public  Map<String,TytConfig> getConfigMap();

    /**
     * 判断key的对应的value值是否和传入的比较值相等
     * @param key 参数key 值
     * @param compareValue  比较值
     * @return  boolean
     */
  public boolean isEqualsValue (String key,int compareValue);

    /**
     * 判断key对应的值是否等于1
     * @param key 参数key值
     * @return boolean
     */
  public boolean isEqualsOne(String key);
}
