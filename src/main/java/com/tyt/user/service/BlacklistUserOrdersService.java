package com.tyt.user.service;

import com.tyt.plat.entity.base.BlacklistUserOrders;

import java.util.Date;
import java.util.List;

public interface BlacklistUserOrdersService {

    List<BlacklistUserOrders> getBlackByUserId(Long userId);


    BlacklistUserOrders getOrdersByUserIdAndRestrictTime(Long userId, Date time);

    /**
     * 查询限制发货的处罚订单
     * @param userId
     * @param time
     * @return
     */
    BlacklistUserOrders getGoodsLimitByUserIdAndTime(Long userId, Date time);
}