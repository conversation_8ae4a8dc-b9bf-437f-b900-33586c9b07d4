package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytVersionNew;
/**
 * 
 * <AUTHOR>
 *
 */
public interface TytVersionNewService extends BaseService<TytVersionNew,Long> {
	/**
	 * 由客户端类型获得版本信息
	 * @param clientType客户端类型
	 * @param userVersion版本号
	 * @param status状态 0无效 1有效
	 * @param downloadType 1个人版2企业版
	 * @return
	 */
	public TytVersionNew getByClient(Integer clientType,String userVersion,Integer status,String downloadType);

}
