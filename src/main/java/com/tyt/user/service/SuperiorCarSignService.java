package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytSuperiorCarSign;
import com.tyt.user.bean.SuperiorCarSignStatusResBean;

public interface SuperiorCarSignService extends BaseService<TytSuperiorCarSign, Long> {

    SuperiorCarSignStatusResBean getSignStatus(Long userId);

    ResultMsgBean sign(Long userId) throws Exception;

    TytSuperiorCarSign getSuperiorCarSign(Long userId);
}
