package com.tyt.user.service;

import java.util.Date;
import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserCallPhoneRecord;
import com.tyt.model.TytUserSub;

/**
 * 
 * <AUTHOR>
 * @date 2017-3-9下午12:53:47
 * @description
 */
public interface TytUserCallPhoneRecordService extends BaseService<TytUserCallPhoneRecord, Long> {

	/**
	 * 根据货源原始id和用户id获取用户拨打电话信息
	 * 
	 * @param srcMsgId
	 *            根据货源原始id
	 * @param userId
	 *            用户id
	 * @return
	 */
	TytUserCallPhoneRecord getBySrcMsgIdAndUserId(Long srcMsgId, String userId);
	TytUserCallPhoneRecord getBySrcMsgIdAndUserIdWithRights(Long srcMsgId, TytUserSub userSub);
	List<TytUserCallPhoneRecord> getByUserId(String userId);
	int getCountByUserGroup(Integer userGroup, Long userId) throws Exception;
	int getTodayCountByUserGroup(Integer userGroup, Long userId);

    void addPhoneRecord(Long srcMsgId, Integer level, Long userId,String path,String platId);

    TytUserCallPhoneRecord getPhoneBySrcMsgIdAndUserId(Long srcMsgId, String userId);

	Integer getByUserIdAndDate(Long userId, Date date);

    int getCallStatusByUserIdAndTsId(Long srcMsgId, Long userId);
	// 获取用户拨打电话记录总数，货源去重
	int getDistinctTsCountByUserId(Long userId);
}
