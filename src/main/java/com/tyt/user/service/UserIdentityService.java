package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.UserIdentity;

public interface UserIdentityService extends BaseService<UserIdentity, Long> {
	/**
	 * 获取用户最近一次提交的验证信息
	 * @param userId
	 * @return
	 */
     public UserIdentity getEnabled(Long userId,Integer enabled);
     
     /**
 	 * 获取用户最近一次提交的验证信息
 	 * @param userId
 	 * @return
 	 */
      public boolean isExit(Long userId);
     
     /**
      * 添加用户实名认证信息，同时修改相应用户表
      * @param identity
      * @throws Exception
      */
     public void addIdentity(UserIdentity identity)throws Exception;
     /**
 	 * 之前的所有实名认证记录置无效
 	 * @param userId
 	 * @param enabled
 	 */
 	public void updateDisabled(Long userId, Integer enabled);

}
