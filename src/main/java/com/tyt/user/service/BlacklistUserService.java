package com.tyt.user.service;

import com.tyt.model.BlacklistUser;
import com.tyt.model.ResultMsgBean;

public interface BlacklistUserService {
    /**
     * 按用户ID获取用户黑名单
     *
     * @param userId
     * @return
     */
    BlacklistUser getBlackByUserId(Long userId);

    /**
     * 根据用户ID和状态查询黑名单用户信息
     * @param userId 用户ID
     * @param status 状态：0
     * @return
     */
    BlacklistUser getBlackByUserIdAndStatus(Long userId, Integer status);

    /**
     * 校验发货是否被限制
     * 操作入口：后台管理 ->用户资料管理 > 重点监控用户管理
     * @param userId
     * @return
     */
    ResultMsgBean checkLimitPublish(Long userId);

    /**
     * 校验找货是否被限制
     * 操作入口：后台管理 ->用户资料管理 > 重点监控用户管理
     * @param userId
     * @return
     */
    ResultMsgBean checkLimitFindGoods(Long userId) throws Exception;

    /**
     * 按用户手机号获取用户黑名单
     *
     * @param cellPhone 手机号
     */
    BlacklistUser getBlackByCellPhone(String cellPhone);

    /**
     * 拉黑用户手机号追加已注销
     */
    void updateCellPhoneCancel(String cellPhone);

    /**
     * 是否拉黑mac地址
     */
    boolean isBlacklistMac(String clientId);
}
