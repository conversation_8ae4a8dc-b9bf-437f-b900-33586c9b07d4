package com.tyt.user.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.UserTel;
import com.tyt.user.querybean.QueryUserTel;
import com.tyt.user.querybean.UserTelModel;

/**
 * 
 * <AUTHOR>
 *
 */
public interface UserTelService extends BaseService<UserTel,Long> {
	/**
	 * 根据id提取电话本
	 * @param userId
	 * @param type 0手机 1固话
	 * @throws Exception
	 */
	public List<QueryUserTel> getTelsById(Long userId, String type) throws Exception;
	/**
	 * 根据用户id，和联系人查询重复信息
	 * @param userId
	 * @param tel
	 * @return
	 */
	public boolean get(Long userId, String tel);

	List<UserTel> getUserTel(Long userId, String tel);
	/**
	 * 置联系人无效
	 * @param id
	 * @throws Exception
	 */
	public void disabledId(Long id) throws Exception;


	void updateUserTel(UserTel userTel);
	/**
	 * 获取最新count条电话号码
	 * @param userId用户ID
	 * @param count电话条数
	 * @return
	 */
	public List<String> getLatestTels(Long userId, Integer count);

	List<UserTelModel> getLatestTelsByUserId(Long userId, Integer count);

}
