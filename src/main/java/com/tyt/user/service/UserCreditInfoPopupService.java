package com.tyt.user.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.UserCreditInfoPopup;

/**
 * @description 用户信用分弹窗服务层
 * <AUTHOR>
 * @date 2022/9/20 18:26
 */
public interface UserCreditInfoPopupService extends BaseService<UserCreditInfoPopup, Long> {

    /**
     * @description 根据用户ID获取信用弹框记录
     * <AUTHOR>
     * @date 2022/9/21 10:08
     * @param userId
     * @return com.tyt.model.UserCreditInfoPopup
     */
    UserCreditInfoPopup getPopupByUserId(Long userId) throws Exception;

}
