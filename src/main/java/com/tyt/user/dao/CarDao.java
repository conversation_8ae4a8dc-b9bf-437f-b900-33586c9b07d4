package com.tyt.user.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.Car;
import com.tyt.user.querybean.QueryCar;

import java.util.List;
/**
 * 
 * <AUTHOR>
 *
 */
public interface CarDao extends BaseDao<Car,Long> {
	/**
	 * 得到用户简化的所有查询信息
	 * @param userId
	 * @param auth 
	 * @return
	 */
	List<QueryCar> getQueryAll(Long userId, String auth);
	/**
	 * 得到用户简化的所有查询信息
	 * @param userId
	 * @param auth 
	 * @return
	 */
	List<QueryCar> getQueryAllOld(Long userId, String auth);
	/**
     * 获得实名认证验证总结果集
     * @param userId
     * @return
     * @throws Exception
     */
	boolean isExitEnabled(Long userId)throws Exception;
	List<QueryCar> getQueryAllNew(Long userId, String auth);


    /**
     * @Description  根据用户id和认证状态查询用户所有的车辆
     * <AUTHOR>
     * @Date  2019/3/13 10:11
     * @Param [userId, auth]
     * @return java.util.List<com.tyt.user.querybean.QueryCar>
     **/
	List<QueryCar> getAllCarByUserId(Long userId);

	/**
	 * @Description  根据车头车牌号查询认证车辆的信息
	 * <AUTHOR>
	 * @Date  2019/3/14 11:27
	 * @Param [userId, carHeadCity, carHeadNo]
	 * @return com.tyt.model.CarInsuranceInquiry
	 **/
	List<QueryCar> getAuthCarInfoByHeadNo(Long userId, String carHeadCity, String carHeadNo);

	/**
	 * 获取已认证的挂车列表
	 * @param userId
	 * @param auth
	 * @return
	 */
	List<QueryCar> getAuthTailList(Long userId, String auth);
}
