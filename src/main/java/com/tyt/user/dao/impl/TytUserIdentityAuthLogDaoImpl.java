package com.tyt.user.dao.impl;

import java.util.Date;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserIdentityAuthLog;
import com.tyt.user.dao.TytUserIdentityAuthLogDao;

@Repository("tytUserIdentityAuthLogDao")
public class TytUserIdentityAuthLogDaoImpl   extends BaseDaoImpl<TytUserIdentityAuthLog, Long>  implements  TytUserIdentityAuthLogDao   {
    
	public TytUserIdentityAuthLogDaoImpl() {
        this.setEntityClass(TytUserIdentityAuthLog.class);
    }

	@Override
	public TytUserIdentityAuthLog saveIdentityAuth(Long userId,
			Long userIdentityAuthId, String mobile, Integer userClass,
			Integer identityType, String mainUrl, Date nowDate)
			throws Exception {
		TytUserIdentityAuthLog userIdentityAuthLog = new TytUserIdentityAuthLog();
		userIdentityAuthLog.setUiaId(userIdentityAuthId);
		userIdentityAuthLog.setUserId(userId);
		userIdentityAuthLog.setMobile(mobile);
		userIdentityAuthLog.setIdentityStatus(0);
		userIdentityAuthLog.setUserClass(userClass);
		userIdentityAuthLog.setIdentityType(identityType);
		userIdentityAuthLog.setInfoStatus(0);
		userIdentityAuthLog.setMainUrl(mainUrl);
		userIdentityAuthLog.setMainStatus(0);
		userIdentityAuthLog.setCtime(nowDate);
		userIdentityAuthLog.setExamineStatus(0);
		this.insert(userIdentityAuthLog);
		return userIdentityAuthLog;
	}
}
