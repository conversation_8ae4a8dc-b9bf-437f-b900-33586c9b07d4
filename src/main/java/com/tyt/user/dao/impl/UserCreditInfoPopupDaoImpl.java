package com.tyt.user.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.UserCreditInfoPopup;
import com.tyt.user.dao.UserCreditInfoPopupDao;
import org.springframework.stereotype.Repository;

/**
 * @description 用户信用分弹窗数据层实现类
 * <AUTHOR>
 * @date 2022/9/20 18:12
 */
@Repository("userCreditInfoPopupDao")
public class UserCreditInfoPopupDaoImpl extends BaseDaoImpl<UserCreditInfoPopup,Long> implements UserCreditInfoPopupDao {

    public UserCreditInfoPopupDaoImpl(){
       this.setEntityClass(UserCreditInfoPopup.class);
    }

}
