package com.tyt.user.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.User;
import com.tyt.user.dao.UserDao;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午4:22
 */
@Slf4j
@Repository("userDao")
public class UserDaoImpl  extends BaseDaoImpl<User, Long>  implements  UserDao   {

      public UserDaoImpl() {
          this.setEntityClass(User.class);
      }

    @Override
    public void decreServeDays() {
        HibernateTemplate tmpl = getHibernateTemplate();
        tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
            	SQLQuery phoneQuery = session.createSQLQuery("update tyt_user set phone_serve_days = phone_serve_days-1  where phone_serve_days>=1 and DATEDIFF( CURDATE() , ctime ) >=1");
                SQLQuery query = session.createSQLQuery("update tyt_user set serve_days = serve_days-1,mtime= now() where serve_days>=1 and DATEDIFF( CURDATE() , ctime ) >=1");
                System.out.println("user serve days change:"+query.executeUpdate());
                System.out.println("user phone serve days change:"+phoneQuery.executeUpdate());
                return null;
            }
        });
    }

    @Override
    public void clearQqModTimes() {
        HibernateTemplate tmpl = getHibernateTemplate();
        tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("update tyt_user set qq_mod_times =0,qq_mod_time = null,mtime= now() ");
                System.out.println("clear qq_mod_times:"+query.executeUpdate());
                return null;
            }
        });
    }

    /**
      * 取得销售的集合
    */
	@SuppressWarnings("unchecked")
	@Override
	public List<String> getSales(final  String condition) {
		HibernateTemplate tmpl = getHibernateTemplate();
        return (List<String>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("select distinct(sales) from tyt_user "+condition);
                
                return query.list();
            }
        });
		
	}

	@Override
	public boolean updatePassword(final Long id, final String newPassword)  throws Exception{
		HibernateTemplate tmpl = getHibernateTemplate();
	     return   (boolean) tmpl.execute(new HibernateCallback<Object>() {
	            @Override
	            public Object doInHibernate(Session session)
	                    throws HibernateException, SQLException {
	                SQLQuery query = session.createSQLQuery("update tyt_user set password='"+newPassword+"',mtime='"+TimeUtil.formatDateTime(new Date())+"' where id="+id);
	                return query.executeUpdate()>=1;
	            }
	        });
	}

	@Override
	public boolean updateLastInfo(final Long id,final String ticket, final Integer clientSign,
			final String clientVersion, final String osVersion) throws Exception {

		String currentDate = TimeUtil.formatDateTime(new Date());

		StringBuilder sql = new StringBuilder("update tyt_user set client_sign = :clientSign, os_version = :osVersion, client_version = :clientVersion, ticket = :ticket, mtime = :mtime");
		Map<String, Object> params = new HashMap<>();
		params.put("clientSign", clientSign);
		params.put("osVersion", osVersion);
		params.put("clientVersion", clientVersion);
		params.put("ticket", ticket);
		params.put("mtime", currentDate);

		if (clientSign == Constant.ClientSignEnum.ANDROID_CAR.code || clientSign == Constant.ClientSignEnum.IOS_CAR.code) {
			sql.append(", car_user_sign = :carUserSign, car_last_login_time = :carLastLoginTime");
			params.put("carUserSign", 1);
			params.put("carLastLoginTime", currentDate);
		} else if (clientSign == Constant.ClientSignEnum.ANDROID_GOODS.code || clientSign == Constant.ClientSignEnum.IOS_GOODS.code || clientSign == Constant.ClientSignEnum.WEB_GOODS.code) {
			sql.append(", goods_user_sign = :goodsUserSign, goods_last_login_time = :goodsLastLoginTime");
			params.put("goodsUserSign", 1);
			params.put("goodsLastLoginTime", currentDate);
		} else {
			sql.append(", last_time = :lastTime");
			params.put("lastTime", currentDate);
		}

		sql.append(" where id = :id");
		params.put("id", id);

		return executeUpdateSql(sql.toString(), params) >= 1;
	}

	@Override
	public void saveHead(final String headurl,final Long userId) throws Exception {
		HibernateTemplate tmpl = getHibernateTemplate();
	    tmpl.execute(new HibernateCallback<Object>() {
	            @Override
	            public Object doInHibernate(Session session)
	                    throws HibernateException, SQLException {
	                SQLQuery query = session.createSQLQuery("update tyt_user set head_url=?,mtime=? where id=?");
	                return query.setParameter(0, headurl)
	                		.setParameter(1, TimeUtil.formatDateTime(new Date()))
	                		.setParameter(2, userId).executeUpdate();
	            }
	        });
		
	}

	@Override
	public void saveUserName(final String userName, final Long userId, Integer clientSign) throws Exception {
		StringBuilder sql = new StringBuilder("update tyt_user set ");
		if (clientSign == Constant.ClientSignEnum.ANDROID_CAR.code || clientSign == Constant.ClientSignEnum.IOS_CAR.code) {
			sql.append("car_user_name=? ");
		} else {
			sql.append("user_name=? ");
		}
		sql.append(", mtime=? where id=?");
		executeUpdateSql(sql.toString(), new Object[]{userName, TimeUtil.formatDateTime(new Date()), userId});
	}

	

	
	@Override
	public void updateVeryCode(final Long userId, final Integer verifyFlag)
			throws Exception {
		HibernateTemplate tmpl = getHibernateTemplate();
       tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("update tyt_user set verify_flag=?,mtime=? where id=?");
                return query.setParameter(0, verifyFlag)
                		.setParameter(1, TimeUtil.formatDateTime(new Date()))
                		.setParameter(2, userId).executeUpdate();
            }
        });
		
	}
	
	@Override
	public void updatePhotoVerifyFlag(Long userId, Date mtime,
			Integer photoVerifyFlag, Integer userClass, Integer identityType) {
		String updateSQL="UPDATE tyt_user set verify_photo_sign=?,user_class=?,identity_type=?,mtime=? where id=?";
		this.executeUpdateSql(updateSQL, new Object[]{photoVerifyFlag,userClass,identityType,mtime,userId});
	}

	@Override
	public void updateQQ(final Long id) throws Exception {
		getHibernateTemplate().execute(new HibernateCallback<Object>() {
	            @Override
	            public Object doInHibernate(Session session)
	                    throws HibernateException, SQLException {
	                SQLQuery query = session.createSQLQuery("update tyt_user set qq=? where id=?");
	                return query.setParameter(0, id).setParameter(1, id).executeUpdate();
	            }
	        });
	}

	@Override
	public void saveInitUserName(User user) {

		Long id = user.getId();

		if(id == null){
			log.error("saveInitUserName : userId must not be null!");
			return;
		}

		String userName = user.getUserName();
		String carUserName = user.getCarUserName();

		if(StringUtils.isBlank(userName)){
			userName = "用户" + id;
		}

		if(StringUtils.isBlank(carUserName)){
			carUserName = "用户" + id;
		}

		String updateSql = "update tyt_user set user_name=?, car_user_name=? where id=?";

		Object[] params = {userName, carUserName, id};

		this.executeUpdateSql(updateSql, params);

		log.info("init_save_user_names : " + id);
	}

	@Override
	public void updateMbUserId(String mbUserId, Long uid) {
		getHibernateTemplate().execute(new HibernateCallback<Object>() {
			@Override
			public Object doInHibernate(Session session)
					throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery("update tyt_user set mb_user_id=? where id=?");
				return query.setParameter(0, mbUserId).setParameter(1, uid).executeUpdate();
			}
		});
	}


}
