package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserIdentityMain;
import com.tyt.user.dao.UserIdentityMainDao;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("userIdentityMainDao")
public class UserIdentityMainDaoImpl   extends BaseDaoImpl<TytUserIdentityMain, Long>  implements  UserIdentityMainDao   {
    public UserIdentityMainDaoImpl() {
        this.setEntityClass(TytUserIdentityMain.class);
    }
}
