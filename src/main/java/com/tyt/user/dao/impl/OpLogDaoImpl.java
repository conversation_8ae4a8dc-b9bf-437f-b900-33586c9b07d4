package com.tyt.user.dao.impl;


import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.OpLog;
import com.tyt.model.OpLogStat;
import com.tyt.model.UserLogTime;
import com.tyt.user.dao.OpLogDao;

import org.springframework.util.StringUtils;

import java.math.BigInteger;
import java.sql.Date;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
@SuppressWarnings("unchecked")
@Repository("opLogDao")
public class OpLogDaoImpl extends BaseDaoImpl<OpLog, Long> implements OpLogDao {

	public OpLogDaoImpl() {
	        this.setEntityClass(OpLog.class);
	}

    @Override
    public List<OpLogStat> getOpLogStatList(String beginTime,String endTime) {
        StringBuffer sql = new StringBuffer();
        sql.append("select entity.opType, count(entity.id) from OpLog entity ");
        boolean hasFirst = false;
        if(StringUtils.hasLength(beginTime)) {
        	sql.append(" where ");
        	sql.append(" date_format(entity.opTime,'%Y%m%d')>='").append(beginTime).append("'");
        	hasFirst = true;
        }	
        if(StringUtils.hasLength(endTime)) {
        	if(hasFirst) sql.append(" and ");
        	else sql.append(" where ");
        	sql.append(" date_format(entity.opTime,'%Y%m%d')<='").append(endTime).append("'");
        }	
        sql.append(" group by opType");
        List objectList =  this.getHibernateTemplate().find(sql.toString());
        List<OpLogStat> opLogStatList = new ArrayList<OpLogStat>();
        for(Object object : objectList) {
            Object element[] = (Object []) object;
            OpLogStat  logStat = new OpLogStat();
            logStat.setOpType((Integer)element[0]);
            logStat.setOpCount(((Long)element[1]).intValue());
            opLogStatList.add(logStat);
        }
        return opLogStatList;
    }

	
	@Override
	public List<UserLogTime> getUserIdTimeList(final String sql) {
		List<UserLogTime> logs = new ArrayList<UserLogTime>();
		 List<Object> objectList =(List<Object>) 
			 this.getHibernateTemplate().execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
            	SQLQuery query = session.createSQLQuery(
            			"SELECT user_id,DATE(op_time) FROM tyt_log where op_type=101 and "+sql
    		    		+ " GROUP BY user_id,DATE(op_time) ORDER BY user_id DESC");
//            	SQLQuery query = session.createSQLQuery(
//    			"SELECT * FROM temp_log where "+sql+ " ORDER BY user_id DESC");
                return query.list();
            }
        });
		 for(Object object : objectList) {
			    Object element[] = (Object []) object;
	            UserLogTime  log = new UserLogTime();
	            log.setUserId(((BigInteger)element[0]).longValue());
	            log.setOpTime((Date)element[1]);
	            logs.add(log);
	            element=null;
	            log=null;
		 }
		 return logs;
	}
}

