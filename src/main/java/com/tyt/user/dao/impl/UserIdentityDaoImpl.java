package com.tyt.user.dao.impl;

import java.math.BigInteger;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.UserIdentity;
import com.tyt.user.dao.UserIdentityDao;
import com.tyt.util.TimeUtil;
@Repository("userIdentityDao")
public class UserIdentityDaoImpl extends BaseDaoImpl<UserIdentity,Long> implements UserIdentityDao  {
	public UserIdentityDaoImpl(){
		   this.setEntityClass(UserIdentity.class);
	   }

	@SuppressWarnings("unchecked")
	@Override
	public boolean isExit(final Long userId) {
		List<BigInteger> ids=(List<BigInteger>) getHibernateTemplate().execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("select id from tyt_user_identity where user_id=? and status in(1,2)");
                return query.setParameter(0,userId).list();
            }
        });
		return ids.size()>0;
	}
	@Override
	public void updateDisabled(final Long userId, final Integer enabled) {
		getHibernateTemplate().execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("update tyt_user_identity set enabled=?,update_time=? where user_id=?");
                query.setParameter(0, enabled).
                setParameter(1, TimeUtil.formatDateTime(new Date()))
                .setParameter(2, userId).executeUpdate();
                return null;
            }
        });
		
	}
	
}
