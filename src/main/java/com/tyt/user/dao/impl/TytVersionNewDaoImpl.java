package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytVersionNew;
import com.tyt.user.dao.TytVersionNewDao;

@Repository("tytVersionNewDao")
public class TytVersionNewDaoImpl extends BaseDaoImpl<TytVersionNew, Long>  implements  TytVersionNewDao   {

    public TytVersionNewDaoImpl() {
        this.setEntityClass(TytVersionNew.class);
    }

}
