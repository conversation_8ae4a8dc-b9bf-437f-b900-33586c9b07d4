package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserSub;
import com.tyt.user.dao.TytUserSubDao;
/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午4:22
 */
@Repository("tytUserSubDao")
public class TytUserSubDaoImpl  extends BaseDaoImpl<TytUserSub, Long>  implements  TytUserSubDao   {

      public TytUserSubDaoImpl() {
          this.setEntityClass(TytUserSub.class);
      }

    
}
