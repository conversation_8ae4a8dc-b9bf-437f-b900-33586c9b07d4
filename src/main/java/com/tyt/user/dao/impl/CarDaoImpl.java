package com.tyt.user.dao.impl;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import com.tyt.model.CarInsuranceInquiry;
import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.Car;
import com.tyt.user.dao.CarDao;
import com.tyt.user.querybean.QueryCar;
@Repository("carDao")
public class CarDaoImpl extends BaseDaoImpl<Car,Long> implements CarDao  {
    public CarDaoImpl(){
        this.setEntityClass(Car.class);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<QueryCar> getQueryAll(final Long userId,final String auth) {

        HibernateTemplate tmpl = getHibernateTemplate();
        return (List<QueryCar>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                if(StringUtils.hasLength(auth)){
                    SQLQuery query = session.createSQLQuery("select car.id id,car.user_id userId,head_city headCity ,head_no headNo, tail_city tailCity,tail_no tailNo, head_driving_url headDrivingUrl,tail_driving_url tailDrivingUrl,auth, failure_reason failureReason, head_auth_status headAuthStatus, head_failure_reason "
                            + "headFailureReason, tail_auth_status tailAuthStatus,tail_failure_reason tailFailureReason, sort ,car.find_good_onoff findGoodOnOff ,pre.id preId,start_provinc startProvinc ,start_city startCity, start_area startArea,dest_provinc destProvinc,dest_city destCity,dest_area destArea, begin_weight beginWeight ,end_weight endWeight, "
                            + "pre.length length,pre.wide wide,pre.high high , car.update_time updateTime  from tyt_car "
                            + " car LEFT JOIN tyt_recommend.tyt_preference pre on car.id=pre.car_id where car.user_id=? and auth=? and is_delete=1 order by sort desc,id desc");
                    return query.addScalar("id", Hibernate.BIG_INTEGER)
                            .addScalar("headCity", Hibernate.STRING)
                            .addScalar("headNo", Hibernate.STRING)
                            .addScalar("tailCity", Hibernate.STRING)
                            .addScalar("tailNo", Hibernate.STRING)
                            .addScalar("auth", Hibernate.STRING)
                            .addScalar("userId", Hibernate.BIG_INTEGER)
                            .addScalar("headDrivingUrl", Hibernate.STRING)
                            .addScalar("tailDrivingUrl", Hibernate.STRING)
                            .addScalar("failureReason", Hibernate.STRING)
                            .addScalar("headAuthStatus", Hibernate.SHORT)
                            .addScalar("headFailureReason", Hibernate.STRING)
                            .addScalar("tailAuthStatus", Hibernate.SHORT)
                            .addScalar("tailFailureReason", Hibernate.STRING)
                            .addScalar("findGoodOnOff", Hibernate.STRING)
                            .addScalar("sort", Hibernate.BIG_INTEGER)
                            .addScalar("startProvinc", Hibernate.STRING)
                            .addScalar("startCity", Hibernate.STRING)
                            .addScalar("startArea", Hibernate.STRING)
                            .addScalar("destProvinc", Hibernate.STRING)
                            .addScalar("destCity", Hibernate.STRING)
                            .addScalar("destArea", Hibernate.STRING)
                            .addScalar("beginWeight", Hibernate.STRING)
                            .addScalar("endWeight", Hibernate.STRING)
                            .addScalar("length", Hibernate.STRING)
                            .addScalar("wide", Hibernate.STRING)
                            .addScalar("high", Hibernate.STRING)
                            .addScalar("preId", Hibernate.BIG_INTEGER)
                            .addScalar("updateTime", Hibernate.TIMESTAMP)
                            .setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).setParameter(1, auth).list();
                }else{
                    SQLQuery query = session.createSQLQuery("select car.id id,car.user_id userId,head_city headCity ,head_no headNo, tail_city tailCity,tail_no tailNo, head_driving_url headDrivingUrl,tail_driving_url tailDrivingUrl,auth, failure_reason failureReason, head_auth_status headAuthStatus, head_failure_reason "
                            + "headFailureReason, tail_auth_status tailAuthStatus,tail_failure_reason tailFailureReason, sort ,car.find_good_onoff findGoodOnOff ,pre.id preId,start_provinc startProvinc ,start_city startCity, start_area startArea,dest_provinc destProvinc,dest_city destCity,dest_area destArea, begin_weight beginWeight ,end_weight endWeight, "
                            + "pre.length length,pre.wide wide,pre.high high , car.update_time updateTime  from tyt_car "
                            + " car LEFT JOIN tyt_recommend.tyt_preference pre on car.id=pre.car_id where  car.user_id=?  and is_delete=1 order by sort desc,id desc");
                    return query.addScalar("id", Hibernate.BIG_INTEGER)
                            .addScalar("headCity", Hibernate.STRING)
                            .addScalar("headNo", Hibernate.STRING)
                            .addScalar("tailCity", Hibernate.STRING)
                            .addScalar("tailNo", Hibernate.STRING)
                            .addScalar("auth", Hibernate.STRING)
                            .addScalar("userId", Hibernate.BIG_INTEGER)
                            .addScalar("headDrivingUrl", Hibernate.STRING)
                            .addScalar("tailDrivingUrl", Hibernate.STRING)
                            .addScalar("failureReason", Hibernate.STRING)
                            .addScalar("headAuthStatus", Hibernate.SHORT)
                            .addScalar("headFailureReason", Hibernate.STRING)
                            .addScalar("tailAuthStatus", Hibernate.SHORT)
                            .addScalar("tailFailureReason", Hibernate.STRING)
                            .addScalar("findGoodOnOff", Hibernate.STRING)
                            .addScalar("sort", Hibernate.BIG_INTEGER)
                            .addScalar("startProvinc", Hibernate.STRING)
                            .addScalar("startCity", Hibernate.STRING)
                            .addScalar("startArea", Hibernate.STRING)
                            .addScalar("destProvinc", Hibernate.STRING)
                            .addScalar("destCity", Hibernate.STRING)
                            .addScalar("destArea", Hibernate.STRING)
                            .addScalar("beginWeight", Hibernate.STRING)
                            .addScalar("endWeight", Hibernate.STRING)
                            .addScalar("length", Hibernate.STRING)
                            .addScalar("wide", Hibernate.STRING)
                            .addScalar("high", Hibernate.STRING)
                            .addScalar("preId", Hibernate.BIG_INTEGER)
                            .addScalar("updateTime", Hibernate.TIMESTAMP)
                            .setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).list();
                }

            }
        });
    }
    @SuppressWarnings("unchecked")
    @Override
    public boolean isExitEnabled(final Long userId) throws Exception {
        List<String> list=(List<String>) getHibernateTemplate().execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("select * from tyt_car where user_id=? and auth=1");
                return query.setParameter(0, userId).list();
            }
        });
        return list.size()>0;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<QueryCar> getQueryAllOld(final Long userId, final String auth) {
        HibernateTemplate tmpl = getHibernateTemplate();
        return (List<QueryCar>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                if(StringUtils.hasLength(auth)){
                    SQLQuery query = session.createSQLQuery("select id,head_city as headCity,head_no as headNo,auth as auth,tail_no as tailNo,tail_city as tailCity from tyt_car where user_id=? and auth=? and is_delete=1 order by id desc");
                    return query.setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).setParameter(1, auth).list();
                }else{
                    SQLQuery query = session.createSQLQuery("select id,head_city as headCity,head_no as headNo,auth as auth,tail_no as tailNo,tail_city as tailCity from tyt_car where user_id=? and auth!=2 and is_delete=1 order by id desc");
                    return query.setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).list();
                }

            }
        });
    }

    @Override
    public List<QueryCar> getQueryAllNew(final Long userId, final String auth) {
        HibernateTemplate tmpl = getHibernateTemplate();
        return (List<QueryCar>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                if(StringUtils.hasLength(auth)){
                    SQLQuery query = session.createSQLQuery("select car.id id,car.user_id userId,head_city headCity ,head_no headNo, tail_city tailCity,tail_no tailNo, head_driving_url headDrivingUrl,tail_driving_url tailDrivingUrl,auth, failure_reason failureReason, head_auth_status headAuthStatus, head_failure_reason "
                            + "headFailureReason, tail_auth_status tailAuthStatus,tail_failure_reason tailFailureReason, car.sort ,pre.find_good_onoff findGoodOnOff ,pre.id preId,start_provinc startProvinc ,start_city startCity, start_area startArea,dest_provinc destProvinc,dest_city destCity,dest_area destArea, begin_weight beginWeight ,end_weight endWeight, "
                            + "pre.length length,pre.wide wide,pre.high high , car.update_time updateTime ,pre.is_auth_update isAuthUpdate ,pre.preference_car goodType, car.car_type carType, ts.name carTypeName from tyt_car "
                            + " car LEFT JOIN tyt_source ts on ts.value = car.car_type AND ts.group_code = 'tail_car_type' LEFT JOIN tyt_recommend.tyt_preference_new pre on car.id=pre.car_id where car.user_id=? and auth=? and is_delete=1 order by sort desc,id desc");
                    return query.addScalar("id", Hibernate.BIG_INTEGER)
                            .addScalar("headCity", Hibernate.STRING)
                            .addScalar("headNo", Hibernate.STRING)
                            .addScalar("tailCity", Hibernate.STRING)
                            .addScalar("tailNo", Hibernate.STRING)
                            .addScalar("auth", Hibernate.STRING)
                            .addScalar("userId", Hibernate.BIG_INTEGER)
                            .addScalar("headDrivingUrl", Hibernate.STRING)
                            .addScalar("tailDrivingUrl", Hibernate.STRING)
                            .addScalar("failureReason", Hibernate.STRING)
                            .addScalar("headAuthStatus", Hibernate.SHORT)
                            .addScalar("headFailureReason", Hibernate.STRING)
                            .addScalar("tailAuthStatus", Hibernate.SHORT)
                            .addScalar("tailFailureReason", Hibernate.STRING)
                            .addScalar("findGoodOnOff", Hibernate.STRING)
                            .addScalar("sort", Hibernate.BIG_INTEGER)
                            .addScalar("startProvinc", Hibernate.STRING)
                            .addScalar("startCity", Hibernate.STRING)
                            .addScalar("startArea", Hibernate.STRING)
                            .addScalar("destProvinc", Hibernate.STRING)
                            .addScalar("destCity", Hibernate.STRING)
                            .addScalar("destArea", Hibernate.STRING)
                            .addScalar("beginWeight", Hibernate.STRING)
                            .addScalar("endWeight", Hibernate.STRING)
                            .addScalar("length", Hibernate.STRING)
                            .addScalar("wide", Hibernate.STRING)
                            .addScalar("high", Hibernate.STRING)
                            .addScalar("preId", Hibernate.BIG_INTEGER)
                            .addScalar("updateTime", Hibernate.TIMESTAMP)
                            .addScalar("isAuthUpdate", Hibernate.STRING)
                            .addScalar("goodType", Hibernate.STRING)
                            .addScalar("carType", Hibernate.STRING)
                            .addScalar("carTypeName", Hibernate.STRING)
//                  .addScalar("currentSpeed", Hibernate.STRING)
//            		.addScalar("currentDetailAddr", Hibernate.STRING)
//            		.addScalar("currentPosition", Hibernate.STRING)
//            		.addScalar("currentStatus", Hibernate.INTEGER)
//            		.addScalar("startPointLongitude",Hibernate.DOUBLE)
//              	.addScalar("startPointLatitude",Hibernate.DOUBLE)
                            .setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).setParameter(1, auth).list();
                }else{
                    SQLQuery query = session.createSQLQuery("select car.id id,car.user_id userId,head_city headCity ,head_no headNo, tail_city tailCity,tail_no tailNo, head_driving_url headDrivingUrl,tail_driving_url tailDrivingUrl,auth, failure_reason failureReason, head_auth_status headAuthStatus, head_failure_reason "
                            + "headFailureReason, tail_auth_status tailAuthStatus,tail_failure_reason tailFailureReason, car.sort ,pre.find_good_onoff findGoodOnOff ,pre.id preId,start_provinc startProvinc ,start_city startCity, start_area startArea,dest_provinc destProvinc,dest_city destCity,dest_area destArea, begin_weight beginWeight ,end_weight endWeight, "
                            + "pre.length length,pre.wide wide,pre.high high , car.update_time updateTime,pre.is_auth_update isAuthUpdate ,pre.preference_car goodType, car.car_type carType, ts.name carTypeName from tyt_car "
                            + " car LEFT JOIN tyt_source ts on ts.value = car.car_type AND ts.group_code = 'tail_car_type' LEFT JOIN tyt_recommend.tyt_preference_new pre on car.id=pre.car_id where  car.user_id=?  and is_delete=1 order by sort desc,id desc");
                    return query.addScalar("id", Hibernate.BIG_INTEGER)
                            .addScalar("headCity", Hibernate.STRING)
                            .addScalar("headNo", Hibernate.STRING)
                            .addScalar("tailCity", Hibernate.STRING)
                            .addScalar("tailNo", Hibernate.STRING)
                            .addScalar("auth", Hibernate.STRING)
                            .addScalar("userId", Hibernate.BIG_INTEGER)
                            .addScalar("headDrivingUrl", Hibernate.STRING)
                            .addScalar("tailDrivingUrl", Hibernate.STRING)
                            .addScalar("failureReason", Hibernate.STRING)
                            .addScalar("headAuthStatus", Hibernate.SHORT)
                            .addScalar("headFailureReason", Hibernate.STRING)
                            .addScalar("tailAuthStatus", Hibernate.SHORT)
                            .addScalar("tailFailureReason", Hibernate.STRING)
                            .addScalar("findGoodOnOff", Hibernate.STRING)
                            .addScalar("sort", Hibernate.BIG_INTEGER)
                            .addScalar("startProvinc", Hibernate.STRING)
                            .addScalar("startCity", Hibernate.STRING)
                            .addScalar("startArea", Hibernate.STRING)
                            .addScalar("destProvinc", Hibernate.STRING)
                            .addScalar("destCity", Hibernate.STRING)
                            .addScalar("destArea", Hibernate.STRING)
                            .addScalar("beginWeight", Hibernate.STRING)
                            .addScalar("endWeight", Hibernate.STRING)
                            .addScalar("length", Hibernate.STRING)
                            .addScalar("wide", Hibernate.STRING)
                            .addScalar("high", Hibernate.STRING)
                            .addScalar("preId", Hibernate.BIG_INTEGER)
                            .addScalar("updateTime", Hibernate.TIMESTAMP)
                            .addScalar("isAuthUpdate", Hibernate.STRING)
                            .addScalar("goodType", Hibernate.STRING)
                            .addScalar("carType", Hibernate.STRING)
                            .addScalar("carTypeName", Hibernate.STRING)
//                    		.addScalar("currentSpeed", Hibernate.STRING)
//                    		.addScalar("currentDetailAddr", Hibernate.STRING)
//                    		.addScalar("currentPosition", Hibernate.STRING)
//                    		.addScalar("currentStatus", Hibernate.INTEGER)
//                    		.addScalar("startPointLongitude",Hibernate.DOUBLE)
//                      		.addScalar("startPointLatitude",Hibernate.DOUBLE)
                            .setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).list();
                }

            }
        });
    }

    /**
     * @Description  根据用户id和认证状态查询用户所有的车辆
     * <AUTHOR>
     * @Date  2019/3/13 10:16
     * @Param [userId, auth]
     * @return java.util.List<com.tyt.user.querybean.QueryCar>
     **/
    @Override
    public List<QueryCar> getAllCarByUserId(Long userId) {
        HibernateTemplate tmpl = getHibernateTemplate();
        return (List<QueryCar>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session) throws HibernateException, SQLException {

                SQLQuery query = session.createSQLQuery("select id,head_city as headCity,head_no as headNo from tyt_car where user_id=?  and is_delete=1 order by create_time desc");
                return query.setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).list();
            }
        });
    }

    /**
     * @Description  根据车头车牌号查询认证车辆的信息
     * <AUTHOR>
     * @Date  2019/3/14 11:32
     * @Param [userId, carHeadCity, carHeadNo]
     * @return com.tyt.model.CarInsuranceInquiry
     **/
    @Override
    public List<QueryCar> getAuthCarInfoByHeadNo(Long userId, String carHeadCity, String carHeadNo) {
        HibernateTemplate tmpl = getHibernateTemplate();
        return (List<QueryCar>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session) throws HibernateException, SQLException {

                SQLQuery query = session.createSQLQuery("select id, head_city as headCity,head_no as headNo," +
                        "head_driving_url as headDrivingUrl from tyt_car where user_id=? and head_city=? and head_no=? and is_delete=1 order by id desc");
                return query.setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).setParameter(1, carHeadCity).setParameter(2, carHeadNo).list();
            }
        });
    }

    /**
     * 获取已认证的挂车列表
     * @param userId
     * @param auth
     * @return
     */
    @Override
    public List<QueryCar> getAuthTailList(Long userId, String auth) {
        HibernateTemplate tmpl = getHibernateTemplate();
        return (List<QueryCar>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("SELECT " +
                        " t.id id, " +
                        " c.head_city AS headCity, " +
                        " c.head_no AS headNo, " +
                        " c.auth AS auth, " +
                        " c.tail_no AS tailNo, " +
                        " c.tail_city AS tailCity  " +
                        "FROM " +
                        " tyt_car c left join tyt_car_detail_tail t on c.id = t.car_id " +
                        "WHERE " +
                        " c.user_id =?  " +
                        " AND c.auth =?  " +
                        " AND c.is_delete = 1  " +
                        "ORDER BY " +
                        " c.id DESC;");
                return query.setResultTransformer(Transformers.aliasToBean(QueryCar.class)).setParameter(0, userId).setParameter(1, auth).list();
            }
        });
    }
}
