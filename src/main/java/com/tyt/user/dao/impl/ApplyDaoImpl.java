package com.tyt.user.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.Apply;
import com.tyt.user.dao.ApplyDao;

import org.springframework.stereotype.Repository;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("applyDao")
public class ApplyDaoImpl   extends BaseDaoImpl<Apply, Long>  implements  ApplyDao   {
    public ApplyDaoImpl() {
        this.setEntityClass(Apply.class);
    }
}
