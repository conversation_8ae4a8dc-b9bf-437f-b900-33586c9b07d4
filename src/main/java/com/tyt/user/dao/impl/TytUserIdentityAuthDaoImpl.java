package com.tyt.user.dao.impl;

import java.util.Date;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.User;
import com.tyt.user.dao.TytUserIdentityAuthDao;

@Repository("tytUserIdentityAuthDao")
public class TytUserIdentityAuthDaoImpl extends
		BaseDaoImpl<TytUserIdentityAuth, Long> implements
		TytUserIdentityAuthDao {
	public TytUserIdentityAuthDaoImpl() {
		this.setEntityClass(TytUserIdentityAuth.class);
	}

	@Override
	public TytUserIdentityAuth saveIdentityAuth(User user,Long userId, String mobile,
			Integer userClass, Integer identityType, String mainUrl,String trueName,
			Date nowDate) throws Exception {
		TytUserIdentityAuth userIdentityAuth = new TytUserIdentityAuth();
		userIdentityAuth.setUserId(userId);
		userIdentityAuth.setMobile(mobile);
		if(user.getVerifyFlag()!=null&&user.getVerifyFlag().intValue()==1){
			userIdentityAuth.setTrueName(user.getTrueName());
			userIdentityAuth.setIdCard(user.getIdCard());
			userIdentityAuth.setSex(user.getSex());
		}else
		userIdentityAuth.setTrueName(trueName);
		
		userIdentityAuth.setIdentityStatus(2);
		userIdentityAuth.setUserClass(userClass);
		userIdentityAuth.setIdentityType(identityType);
		userIdentityAuth.setInfoStatus(null);
		userIdentityAuth.setMainUrl(mainUrl);
		userIdentityAuth.setMainStatus(0);
		userIdentityAuth.setCtime(nowDate);
		userIdentityAuth.setExamineStatus(0);
		userIdentityAuth.setUtime(nowDate);
		userIdentityAuth.setDataType(0);
		this.insert(userIdentityAuth);
		return userIdentityAuth;
	}

	@Override
	public TytUserIdentityAuth updateIdentityAuth(User user,Long userIdentityAuthId,
			Long userId, String mobile, Integer userClass,
			Integer identityType, String mainUrl,String trueName, Date nowDate)
			throws Exception {
		String idCard=null;
		String sex=null;
		if(user.getVerifyFlag()!=null&&user.getVerifyFlag().intValue()==1){
			trueName=user.getTrueName();
			idCard=user.getIdCard();
			sex=user.getSex();
		}
		
		String updateSQL="UPDATE tyt_user_identity_auth "
				+ "set mobile=?,identity_status=?,"
				+ "user_class=?,identity_type=?,info_status=?,"
				+ "main_url=?,main_status=?,ctime=?,examine_status=?,utime=?,data_type=?,true_name=?,sex=?,id_card=? where id=?";
		this.executeUpdateSql(updateSQL, new Object[]{mobile,2,
				userClass,identityType,0,mainUrl,0,nowDate,0,nowDate,0,trueName,sex,idCard,userIdentityAuthId});
		return this.findById(userIdentityAuthId);
	}

}
