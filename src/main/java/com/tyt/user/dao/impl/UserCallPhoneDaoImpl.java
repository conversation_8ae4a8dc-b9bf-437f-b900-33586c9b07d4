package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserCallPhone;
import com.tyt.user.dao.UserCallPhoneDao;

@Repository("userCallPhoneDao")
public class UserCallPhoneDaoImpl extends BaseDaoImpl<TytUserCallPhone, Long> implements
		UserCallPhoneDao {

	public UserCallPhoneDaoImpl(){
		   this.setEntityClass(TytUserCallPhone.class);
	   }


}
