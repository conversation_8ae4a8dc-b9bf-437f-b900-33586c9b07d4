package com.tyt.user.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.CsMaintainedCustom;
import com.tyt.user.dao.CsMaintainedCustomDao;
import org.springframework.stereotype.Repository;

@Repository("csMaintainedCustomDao")
public class CsMaintainedCustomDaoImpl extends BaseDaoImpl<CsMaintainedCustom, Long> implements CsMaintainedCustomDao {


    public CsMaintainedCustomDaoImpl() {
        this.setEntityClass(CsMaintainedCustom.class);
    }
}
