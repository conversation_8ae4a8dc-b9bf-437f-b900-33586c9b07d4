package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserIdentityInfo;
import com.tyt.user.dao.UserIdentityInfoDao;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("userIdentityInfoDao")
public class UserIdentityInfoDaoImpl   extends BaseDaoImpl<TytUserIdentityInfo, Long>  implements  UserIdentityInfoDao   {
    public UserIdentityInfoDaoImpl() {
        this.setEntityClass(TytUserIdentityInfo.class);
    }
}
