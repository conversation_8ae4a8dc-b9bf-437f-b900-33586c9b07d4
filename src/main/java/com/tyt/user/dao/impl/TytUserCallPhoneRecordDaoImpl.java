package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserCallPhoneRecord;
import com.tyt.user.dao.TytUserCallPhoneRecordDao;

@Repository("tytUserCallPhoneRecordDao")
public class TytUserCallPhoneRecordDaoImpl extends BaseDaoImpl<TytUserCallPhoneRecord, Long> implements TytUserCallPhoneRecordDao {
	public TytUserCallPhoneRecordDaoImpl() {
		this.setEntityClass(TytUserCallPhoneRecord.class);
	}
}
