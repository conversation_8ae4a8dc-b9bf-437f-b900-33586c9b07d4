package com.tyt.user.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserStaff;
import com.tyt.model.TytUserSub;
import com.tyt.user.dao.TytUserStaffDao;
import org.springframework.stereotype.Repository;

/**
 * @description:
 * @author: liz<PERSON>
 */
@Repository("tytUserStaffDao")
public class TytUserStaffDaoImpl extends BaseDaoImpl<TytUserStaff, Long> implements TytUserStaffDao {
    public TytUserStaffDaoImpl() {
        this.setEntityClass(TytUserStaff.class);
    }
}
