package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCallPhoneLimit;
import com.tyt.user.dao.TytCallPhoneLimitDao;

/**
 * 
 * <AUTHOR>
 * @date 2017-3-9下午2:48:14
 * @description
 */
@Repository("tytCallPhoneLimitDao")
public class TytCallPhoneLimitDaoImpl extends BaseDaoImpl<TytCallPhoneLimit, Long> implements TytCallPhoneLimitDao {
	public TytCallPhoneLimitDaoImpl() {
		this.setEntityClass(TytCallPhoneLimit.class);
	}
}
