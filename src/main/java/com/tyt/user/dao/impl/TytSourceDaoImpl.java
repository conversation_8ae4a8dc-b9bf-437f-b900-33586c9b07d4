package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytSource;
import com.tyt.user.dao.TytSourceDao;
/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午4:22
 */
@Repository("tytSourceDao")
public class TytSourceDaoImpl  extends BaseDaoImpl<TytSource, Long>  implements  TytSourceDao   {

      public TytSourceDaoImpl() {
          this.setEntityClass(TytSource.class);
      }

    
}
