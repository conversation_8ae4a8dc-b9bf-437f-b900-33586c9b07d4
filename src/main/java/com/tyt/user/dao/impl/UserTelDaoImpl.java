package com.tyt.user.dao.impl;

import java.sql.SQLException;
import java.util.List;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.UserTel;
import com.tyt.user.dao.UserTelDao;
import com.tyt.user.querybean.QueryUserTel;
import com.tyt.user.querybean.UserTelModel;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;
/**
 * 
 * <AUTHOR>
 *
 */
@Repository("userTelDao")
public class UserTelDaoImpl  extends BaseDaoImpl<UserTel, Long>  implements  UserTelDao   {
    public UserTelDaoImpl() {
        this.setEntityClass(UserTel.class);
    }

	@SuppressWarnings("unchecked")
	@Override
	public List<QueryUserTel> getTelsById(final Long userId,final String type) throws Exception {
		return (List<QueryUserTel>) getHibernateTemplate().execute(new HibernateCallback<Object>() {
	            @Override
	            public Object doInHibernate(Session session)
	                    throws HibernateException, SQLException {
	            	StringBuffer sb=new StringBuffer("");
	            	sb.append("select id as id,tell as tel,is_verified as isVerified from tyt_user_tel where user_id=? and status=?");
	            	
	            	if(type!=null&&!"".equals(type.toString())){
	            		sb.append(" and type=?");
	            	}
	                SQLQuery query = session.createSQLQuery(sb.toString());
	                query.setResultTransformer(Transformers.aliasToBean(QueryUserTel.class)).setParameter(0,userId).setParameter(1,1);
	                if(type!=null&&!"".equals(type.toString())){
	            		query.setParameter(2,type);
	            	}
	                
	                return query.list();
	            }
	        });
		
	}

	@Override
	public List<UserTelModel> getLatestTelsByUserId(Long userId, Integer count) {
		return (List<UserTelModel>) getHibernateTemplate().execute(new HibernateCallback<Object>() {
			@Override
			public Object doInHibernate(Session session)
					throws HibernateException, SQLException {
				StringBuffer sb=new StringBuffer("");
				sb.append("SELECT id as id,tell as tel,is_verified as isVerified,user_id as userId,status as status,type as type,create_time as createTime,update_time as updateTime FROM tyt_user_tel WHERE user_id=? AND status=? and is_verified=1");
				if(count!=null){
					sb.append(" ORDER BY id DESC LIMIT ?");
				}
				SQLQuery query = session.createSQLQuery(sb.toString());
				query.setResultTransformer(Transformers.aliasToBean(UserTelModel.class)).setParameter(0,userId).setParameter(1,1);
				if(count!=null){
					query.setParameter(2,count);
				}
				return query.list();
			}
		});
	}

	@Override
	public void disabledId(final Long id) throws Exception {
		getHibernateTemplate().execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
                SQLQuery query = session.createSQLQuery("update tyt_user_tel set status=2 where id=?");
                return query.setParameter(0,id).executeUpdate();
            }
        });
		
	}

	@Override
	public void updateUserTel(UserTel userTel) {

		getHibernateTemplate().execute(new HibernateCallback<Object>() {
			@Override
			public Object doInHibernate(Session session)
					throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery("update tyt_user_tel set tell=?,is_verified=1,update_time = now() where id=?");
				return query.setParameter(0,userTel.getTell()).setParameter(1,userTel.getId()).executeUpdate();
			}
		});
	}
}
