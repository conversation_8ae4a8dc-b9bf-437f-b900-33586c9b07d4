package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCarDriverPhoneInfo;
import com.tyt.user.dao.CarDriverPhoneInfoDao;
@Repository("carDriverPhoneInfoDao")
public class CarDriverPhoneInfoDaoImpl extends BaseDaoImpl<TytCarDriverPhoneInfo, Long> implements CarDriverPhoneInfoDao {

	public CarDriverPhoneInfoDaoImpl() {
		this.setEntityClass(TytCarDriverPhoneInfo.class);
	}
}
