package com.tyt.user.dao.impl;

import java.sql.SQLException;
import java.util.List;

import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCarLocationLog;
import com.tyt.user.bean.CarLocationRecordBean;
import com.tyt.user.dao.TytCarLocationLogDao;

@Repository("tytCarLocationLogDao")
public class TytCarLocationLogDaoImpl extends BaseDaoImpl<TytCarLocationLog,Long> implements TytCarLocationLogDao {
	public TytCarLocationLogDaoImpl(){
		   this.setEntityClass(TytCarLocationLog.class);
	   }

	@Override
	public List<CarLocationRecordBean> getListByCarId(final Long carId, final int pageSize) {
		HibernateTemplate tmpl = getHibernateTemplate();
		return (List<CarLocationRecordBean>) tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
            		SQLQuery query = session.createSQLQuery("select car_id carId, detail_addr detailAddr,position,speed,record_time recordTime ,now() currentTime from tyt.tyt_car_location_log where car_id=? ORDER BY record_time desc LIMIT ? ");
                    return query
                    .addScalar("carId", Hibernate.LONG)
                    .addScalar("detailAddr", Hibernate.STRING)
                    .addScalar("position", Hibernate.STRING)
            		.addScalar("speed", Hibernate.STRING)
            		.addScalar("recordTime", Hibernate.TIMESTAMP)
            		.addScalar("currentTime", Hibernate.TIMESTAMP)
                    .setResultTransformer(Transformers.aliasToBean(CarLocationRecordBean.class)).setParameter(0, carId).setParameter(1, pageSize).list();
            }
		 });
	}
}