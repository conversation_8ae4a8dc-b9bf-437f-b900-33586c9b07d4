package com.tyt.user.dao.impl;

import java.sql.SQLException;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.InviteFriends;
import com.tyt.user.dao.InviteFriendsDao;
@Repository("inviteFriendsDao")
public class InviteFriendsDaoImpl extends BaseDaoImpl<InviteFriends, Long> implements InviteFriendsDao{

	public InviteFriendsDaoImpl() {
		this.setEntityClass(InviteFriends.class);
	}

	@Override
	public void addCounts(final Long userId, final String friendCell) throws Exception{
		HibernateTemplate tmpl = getHibernateTemplate();
	    tmpl.execute(new HibernateCallback<Object>() {
	        @Override
	        public Object doInHibernate(Session session)
	                throws HibernateException, SQLException {
	            SQLQuery query = session.createSQLQuery("update tyt_invite_friends set counts=counts+1 where user_id=? and friend_cell=?");
	            query.setParameter(0, userId).setParameter(1, friendCell).executeUpdate();
	            return null;
	        }
	    });
		
	}

}
