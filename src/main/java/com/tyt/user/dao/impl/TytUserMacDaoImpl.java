package com.tyt.user.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserMac;
import com.tyt.user.dao.TytUserMacDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * User: duanwc
 * Date: 17-08-31
 * Time: 11:38
 */
@Repository("tytUserMacDao")
public class TytUserMacDaoImpl extends BaseDaoImpl<TytUserMac, Long>  implements TytUserMacDao {
    public TytUserMacDaoImpl() {
        this.setEntityClass(TytUserMac.class);
    }

    @Override
    public Integer saveUserMac(TytUserMac userMac) {
        final String sql = "INSERT INTO tyt_user_mac ( mac, user_id, user_name, cell_phone, idcard, client_type, client_sign, times, bind_status, ctime, mtime ) " +
                " VALUE(?, ?, ?, ?, ?, ?, ?, 1, 1, ?, ?) " +
                "  ON DUPLICATE KEY UPDATE mtime = ?, user_name = ?, cell_phone = ?, idcard = ?, times = times + 1 ";
        final Object[] params = {
                userMac.getMac(),
                userMac.getUserId(),
                userMac.getUserName(),
                userMac.getCellPhone(),
                userMac.getIdcard(),
                userMac.getClientType(),
                userMac.getClientSign(),
                userMac.getCtime(),
                userMac.getMtime(),
                userMac.getMtime(),
                userMac.getUserName(),
                userMac.getCellPhone(),
                userMac.getIdcard()
        };
        int i = this.executeUpdateSql(sql,params);
        return i;
    }

    @Override
    public Integer getCountByUserId(Long userId) {
        String sql = "SELECT COUNT(user_id) userCount FROM `tyt_user_mac` WHERE user_id = ?";
        BigInteger c = this.query(sql, new Object[] { userId });
        if (c != null) {
            return c.intValue();
        }
        return 0;
    }
}
