package com.tyt.user.dao.impl;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.Version;
import com.tyt.user.dao.VersionDao;
import com.tyt.util.TimeUtil;

@Repository("versionDao")
public class VersionDaoImpl extends BaseDaoImpl<Version, Long>  implements  VersionDao   {

    public VersionDaoImpl() {
        this.setEntityClass(Version.class);
    }

	@Override
	public Version getEnabledVersion() throws Exception{
		Version version=null;
		List<Version> versions=this.search(" entity.status=1 order by id desc", null);
		if(versions!=null){
			version=versions.get(0);
		}
		return version;
	}

	@Override
	public boolean setVersionDisabled(final Long id) throws Exception {
		 HibernateTemplate tmpl = getHibernateTemplate();
	     return   (boolean) tmpl.execute(new HibernateCallback<Object>() {
	            @Override
	            public Object doInHibernate(Session session)
	                    throws HibernateException, SQLException {
	                SQLQuery query = session.createSQLQuery("update tyt_version set status=0,update_time='"+TimeUtil.formatDateTime(new Date())+"' where id<="+id);
	                return query.executeUpdate()>=1;
	            }
	        });
		
	}

}
