package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytChannelLog;
import com.tyt.user.dao.TytChannelLogDao;
/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("tytChannelLogDao")
public class TytChannelLogDaoImpl   extends BaseDaoImpl<TytChannelLog, Long>  implements  TytChannelLogDao   {
    public TytChannelLogDaoImpl() {
        this.setEntityClass(TytChannelLog.class);
    }
}
