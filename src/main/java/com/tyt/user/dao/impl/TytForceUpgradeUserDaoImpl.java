package com.tyt.user.dao.impl;

import java.sql.SQLException;
import java.util.Date;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytForceUpgradeUser;
import com.tyt.user.dao.TytForceUpgradeUserDao;
import com.tyt.util.TimeUtil;
@Repository("tytForceUpgradeUserDao")
public class TytForceUpgradeUserDaoImpl extends BaseDaoImpl<TytForceUpgradeUser,Long> implements TytForceUpgradeUserDao  {
	
	public TytForceUpgradeUserDaoImpl(){
		   this.setEntityClass(TytForceUpgradeUser.class);
	   }

	@Override
	public boolean updateInfo(final Long userId, final Object version ,final Object taskId) {
		HibernateTemplate tmpl = getHibernateTemplate();
	     return   (boolean) tmpl.execute(new HibernateCallback<Object>() {
	            @Override
	            public Object doInHibernate(Session session)
	                    throws HibernateException, SQLException {
	                SQLQuery query = session.createSQLQuery("update tyt.tyt_force_upgrade_user set is_finished=2,update_time='"+TimeUtil.formatDateTime(new Date())
	                		+"' where user_id="+userId+" and task_id="+Long.parseLong(taskId.toString()));
	                return query.executeUpdate()>=1;
	            }
	        });
		
	}

}
