package com.tyt.user.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCarCurrentLocation;
import com.tyt.user.dao.CurrentCarLocationDao;

@Repository("carCurrentLocationDao")
public class CurrentCarLocationDaoImpl extends BaseDaoImpl<TytCarCurrentLocation, Long> implements CurrentCarLocationDao {

	public CurrentCarLocationDaoImpl() {
		this.setEntityClass(TytCarCurrentLocation.class);
	}
}
