package com.tyt.user.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.VerifyCode;
import com.tyt.user.dao.VerifyCodeDao;

import org.springframework.stereotype.Repository;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:38
 */
@Repository("verifyCodeDao")
public class VerifyCodeDaoImpl   extends BaseDaoImpl<VerifyCode, Long>  implements  VerifyCodeDao   {
    public VerifyCodeDaoImpl() {
        this.setEntityClass(VerifyCode.class);
    }
}
