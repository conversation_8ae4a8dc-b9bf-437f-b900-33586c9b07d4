package com.tyt.user.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.BlacklistUser;
import com.tyt.user.dao.BlacklistUserDao;
import org.springframework.stereotype.Repository;

@Repository("blacklistUserDao")
public class BlacklistUserDaoImpl extends BaseDaoImpl<BlacklistUser, Long> implements BlacklistUserDao {
	public BlacklistUserDaoImpl() {
		this.setEntityClass(BlacklistUser.class);
	}

}
