package com.tyt.user.dao;

import java.util.List;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.UserTel;
import com.tyt.user.querybean.QueryUserTel;
import com.tyt.user.querybean.UserTelModel;

/**
 * 
 * <AUTHOR>
 *
 */
public interface UserTelDao extends BaseDao<UserTel,Long>  {

	/**
	 * 根据id提取电话本
	 * @param userId
	 * @param type 0手机 1固话
	 * @throws Exception
	 */
	public List<QueryUserTel> getTelsById(Long userId, String type) throws Exception;
	/**
	 * 置联系人无效
	 * @param id
	 * @throws Exception
	 */
	public void disabledId(Long id) throws Exception;

	void updateUserTel(UserTel userTel);


	List<UserTelModel> getLatestTelsByUserId(Long userId, Integer count);
}
