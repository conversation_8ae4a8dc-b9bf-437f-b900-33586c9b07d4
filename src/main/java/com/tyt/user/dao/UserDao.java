package com.tyt.user.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.User;

import java.util.Date;
import java.util.List;
/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午3:24
 */
public interface User<PERSON>ao extends BaseDao<User,Long> {
    void decreServeDays();

    void clearQqModTimes();
    
    List<String> getSales(String condition);
    /**
     * 用户修改密码
     * @param id
     * @param password
     * @return
     */
    boolean updatePassword(Long id, String password) throws Exception;
    /**
     * 更改用户最后登录的相关信息
     * @param id
     * @param clientSign
     * @param clientVersion
     * @param osVersion
     * @return
     * @throws Exception
     */
    boolean updateLastInfo(Long id, String ticket, Integer clientSign, String clientVersion, String osVersion)throws Exception;
    /**
     * 添加头像
     * @param headurl
     * @throws Exception
     */
    void saveHead(String headurl, Long userId)throws Exception;
    
    /**
     * 修改用户实名认证标识
     * @param userId
     * @param verifyFlag
     * @throws Exception
     */
    void updateVeryCode(Long userId, Integer verifyFlag)throws Exception;
    /**
     * 修改身份照片认证标识
     * @param userId
     * @param mtime
     * @param photoVerifyFlag
     * @param identityType 
     * @param userClass 
     */
    void updatePhotoVerifyFlag(Long userId, Date mtime,
                               Integer photoVerifyFlag, Integer userClass, Integer identityType);
	/**
	 * 修改用户昵称
	 * @param userName
	 * @param userId
	 * @param clientSign
	 * @throws Exception
	 */
    void saveUserName(String userName, Long userId, Integer clientSign)throws Exception;
	void updateQQ(Long id)throws Exception;

    /**
     * 更新用户userName
     * @param user
     * @throws Exception
     */
    void saveInitUserName(User user);

    void updateMbUserId(String mbUserId, Long uid);
}
