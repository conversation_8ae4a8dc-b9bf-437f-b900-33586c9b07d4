package com.tyt.user.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.TytUserMac;

/**
 * User: duanwc
 * Date: 17-08-31
 * Time: 11:32
 */
public interface TytUserMacDao extends BaseDao<TytUserMac,Long>  {
    /**
     * 保存用户与mac地址的关系，userId与mac形成唯一索引，随登录而更新
     * @param userMac TytUserMac
     */
    Integer saveUserMac(TytUserMac userMac);

    /**
     * 通过userId查询记录数
     * @param userId
     * @return
     */
    Integer getCountByUserId(Long userId);
}
