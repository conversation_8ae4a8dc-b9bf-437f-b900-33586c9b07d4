package com.tyt.user.dao;

import java.util.Date;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.TytUserIdentityAuthLog;

public interface TytUserIdentityAuthLogDao extends BaseDao<TytUserIdentityAuthLog,Long> {
	
	/**
	 * 保存身份认证信息
	 * @param userId 用户ID
	 * @param userIdentityAuthId tyt_user_identity_auth表的ID
	 * @param mobile 用户手机号码
	 * @param userClass 用户分类1、发货方2、车辆方 见 source  user_class
	 * @param identityType 用户身份见source表
	 * @param mainUrl 身份证正面照路径
	 * @return
	 * @throws Exception
	 */
	TytUserIdentityAuthLog saveIdentityAuth(Long userId,Long userIdentityAuthId,String mobile, Integer userClass,Integer identityType,String mainUrl,Date nowDate)throws Exception;


}
