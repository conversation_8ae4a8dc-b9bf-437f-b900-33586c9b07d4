package com.tyt.user.querybean;

import java.io.Serializable;


import com.fasterxml.jackson.annotation.JsonInclude;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsCheckBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4596628414834819109L;
	private Integer isSendGoods=1;
	private String msg="";
	private Integer remainNumber;
	public Integer getIsSendGoods() {
		return isSendGoods;
	}
	public void setIsSendGoods(Integer isSendGoods) {
		this.isSendGoods = isSendGoods;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public Integer getRemainNumber() {
		return remainNumber;
	}
	public void setRemainNumber(Integer remainNumber) {
		this.remainNumber = remainNumber;
	}
}
