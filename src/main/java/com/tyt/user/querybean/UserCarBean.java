package com.tyt.user.querybean;

import lombok.Data;

@Data
public class UserCarBean {

	/**
	 *车辆id
	 */
	Long id;
	/**
	 *车头牌照头字母
	 */
	String headCity;
	/**
	 *车头牌照号码
	 */
	String headNo;
	/**
	 *挂车牌照头字母
	 */
	String tailCity;
	/**
	 *挂车牌照号码
	 */
	String tailNo;
	/**
	 *车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
	 */
	String auth;
	/**
	 *用户id
	 */
	Long userId;
	/**
	 *是否满足开票 1满足 2不满足
	 */
	Integer isInvoice;

	Integer thirdPartyRequire;

	Integer xhlPartyRequire;
}
