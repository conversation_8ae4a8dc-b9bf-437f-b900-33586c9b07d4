package com.tyt.user.querybean;

import java.math.BigInteger;
import java.util.Date;

import com.tyt.util.PictureUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.persistence.Column;

public class QueryCar{

	BigInteger id;// 车头认证状态
	String headCity;// 车头牌照头字母
	String headNo;// 车头牌照号码
	String tailCity;// 挂车牌照头字母
	String tailNo;// 挂车牌照号码
	String auth;// 车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
	BigInteger userId;
	String headDrivingUrl;// 车头行驶本url
	String tailDrivingUrl;// 挂车行驶本URL
	String failureReason;// 失败原因
	Short headAuthStatus; //车头审核状态
	String headFailureReason;//车头审核失败原因
	Short tailAuthStatus; //车挂审核状态
	String tailFailureReason; //车挂审核失败原因
	String findGoodOnOff; //找货开关
	BigInteger   sort; //排序
	String startProvinc; //出发地省份
	String startCity; // 出发地城市
	String startArea; //  出发地地区
	String destProvinc; // 目的地省份
	String destCity; //  目的地城市
	String destArea; //目的地地区
	String beginWeight; // 最少货重
	String endWeight; //最大货重
	String length; // 货物长度
	String wide; // 货物宽度
	String high; // 货物高度
	String goodType; // 货物类型
	BigInteger   preId;
	Date updateTime;
	Date currentTime;
	String isAuthUpdate;
	String carType;
	String carTypeName;
	String isPureFlat;
	/**
	 * 是否带爬梯
	 */
	String hasLadder;
	/**
	 * 车头类型
	 */
	String carHeadType;

	/**
	 * 牵引马力
	 * */
	Integer horsePower;

	/**
	 * 其他挂车样式
	 */
	String otherPureFlat;

	/**
	 * 其他挂车类型
	 */
	String otherCarType;

	/**
	 * 是否抽拉 1 是 2 否
	 */
	Integer isJointPull;

	/**
	 * 抽拉长度
	 */
	String jointPullLength;

	/**
	 * 工作面长
	 */
	String loadSurfaceLength;

	/**
	 * 工作面高
	 */
	String loadSurfaceHeight;

	/**
	 * 最大载重
	 */
	String maxPayload;

	/**
	 * 临牌到期时间
	 */
	Date tempLicenceExpires;

	/**
	 * 临牌反面url
	 */
	String tailDrivingOtherSideUrl;
	/**
	 * 道路运输证正面
	 */
	String roadCardPositiveUrl;
	/**
	 * 道路运输证反面
	 */
	String roadCardOtherSideUrl;
	/**
	 * 道路运输经营许可证
	 */
	String roadLicenseNoUrl;
	/**
	 * 道路运输证审核结果 0审核中1审核成功2审核失败
	 */
	String roadCardStatus;
	/**
	 * 道路运输经营许可证审核结果 0审核中1审核成功2审核失败
	 */
	String roadLicenseStatus;
	/**
	 * 道路运输证审核失败原因
	 */
	String roadCardFailReason;
	/**
	 * 道路运输经营许可证审核失败原因
	 */
	String roadLicenseReason;
	/**
	 *道路运输证类型
	 */
	String roadTransportType;

	/**
	 * 车头行驶证副页反面url
	 */
	private String headDrivingSubpageUrl;
	/**
	 * 车头道路运输证主页url
	 */
	private String headTransportHomepageUrl;
	/**
	 * 车头道路运输证副页url
	 */
	private String headTransportSubpageUrl;
	/**
	 * 挂车行驶证副页反面url
	 */
	private String tailDrivingSubpageUrl;
	/**
	 * 挂车道路运输证主页url
	 */
	private String tailTransportHomepageUrl;
	/**
	 * 挂车道路运输证副页url
	 */
	private String tailTransportSubpageUrl;

	/**
	 * 车头道路运输证审核状态
	 * 0 认证中
	 * 1 认证通过
	 * 2 认证失败
	 */
	private Integer headTransportAuthStatus;

	/**
	 * 车头道路运输证审核失败原因
	 */
	private String headTransportFailReason;

	/**
	 * 挂车道路运输证审核状态
	 * 0 认证中
	 * 1 认证通过
	 * 2 认证失败
	 */
	private Integer tailTransportAuthStatus;

	/**
	 * 挂车道路运输证审核失败原因
	 */
	private String tailTransportFailReason;

	/**
	 * 车头行驶证有效期
	 */
	private Date headDrivingExpiredTime;

	/**
	 * 车头道路运输证有效期
	 */
	private Date headTransportExpiredTime;

	/**
	 * 挂车行驶证有效期
	 */
	private Date tailDrivingExpiredTime;

	/**
	 * 挂车道路运输证有效期
	 */
	private Date tailTransportExpiredTime;

	/**
	 * 车头行驶证逾期提醒
	 */
	private String headDrivingExpiredTips;

	/**
	 * 车头道路运输证逾期提醒
	 */
	private String headTransportExpiredTips;

	/**
	 * 挂车行驶证逾期提醒
	 */
	private String tailDrivingExpiredTips;

	/**
	 * 挂车道路运输证逾期提醒
	 */
	private String tailTransportExpiredTips;
	/**
	 * 挂车照片
	 */
	private String tailPhotoUrl;
	/**
	 *挂车长度
	 */
	private String tailLength;
	/**
	 *挂车宽度
	 */
	private String tailWidth;
	/**
	 *挂车高
	 */
	private String tailHeight;

	/**
	 * 车辆是否为待完善车辆
	 */
	private Boolean carIsNeedImprovenData;

	private String remark;

//	private String currentSpeed;
//	private String currentDetailAddr;
//	private String currentPosition;
//	private Integer currentStatus;
//	private Double startPointLongitude;//车辆当前位置经度
//	private Double startPointLatitude;//车辆当前位置纬度

	@Getter
	@Setter
	private Integer isInvoice;

	private String expiredTips;

	public String getRoadTransportType() {
		return roadTransportType;
	}

	public void setRoadTransportType(String roadTransportType) {
		this.roadTransportType = roadTransportType;
	}

	public String getHasLadder() {
		return hasLadder;
	}

	public void setHasLadder(String hasLadder) {
		this.hasLadder = hasLadder;
	}

	public String getCarHeadType() {
		return carHeadType;
	}

	public void setCarHeadType(String carHeadType) {
		this.carHeadType = carHeadType;
	}

	public Integer getHorsePower() {
		return horsePower;
	}

	public void setHorsePower(Integer horsePower) {
		this.horsePower = horsePower;
	}

	public String getOtherPureFlat() {
		return otherPureFlat;
	}

	public void setOtherPureFlat(String otherPureFlat) {
		this.otherPureFlat = otherPureFlat;
	}

	public String getOtherCarType() {
		return otherCarType;
	}

	public void setOtherCarType(String otherCarType) {
		this.otherCarType = otherCarType;
	}

	public Integer getIsJointPull() {
		return isJointPull;
	}

	public void setIsJointPull(Integer isJointPull) {
		this.isJointPull = isJointPull;
	}

	public String getJointPullLength() {
		return jointPullLength;
	}

	public void setJointPullLength(String jointPullLength) {
		this.jointPullLength = jointPullLength;
	}

	public String getLoadSurfaceLength() {
		return loadSurfaceLength;
	}

	public void setLoadSurfaceLength(String loadSurfaceLength) {
		this.loadSurfaceLength = loadSurfaceLength;
	}

	public String getLoadSurfaceHeight() {
		return loadSurfaceHeight;
	}

	public void setLoadSurfaceHeight(String loadSurfaceHeight) {
		this.loadSurfaceHeight = loadSurfaceHeight;
	}

	public String getTailDrivingOtherSideUrl() {
		return PictureUtil.getPictureUrl(tailDrivingOtherSideUrl);
	}

	public void setTailDrivingOtherSideUrl(String tailDrivingOtherSideUrl) {
		this.tailDrivingOtherSideUrl = tailDrivingOtherSideUrl;
	}

	public String getRoadCardPositiveUrl() {
		return PictureUtil.getPictureUrl(roadCardPositiveUrl);
	}

	public void setRoadCardPositiveUrl(String roadCardPositiveUrl) {
		this.roadCardPositiveUrl = roadCardPositiveUrl;
	}

	public String getRoadCardOtherSideUrl() {
		return PictureUtil.getPictureUrl(roadCardOtherSideUrl);
	}

	public void setRoadCardOtherSideUrl(String roadCardOtherSideUrl) {
		this.roadCardOtherSideUrl = roadCardOtherSideUrl;
	}

	public String getRoadLicenseNoUrl() {
		return PictureUtil.getPictureUrl(roadLicenseNoUrl);
	}

	public void setRoadLicenseNoUrl(String roadLicenseNoUrl) {
		this.roadLicenseNoUrl = roadLicenseNoUrl;
	}

	public String getRoadCardStatus() {
		return roadCardStatus;
	}

	public void setRoadCardStatus(String roadCardStatus) {
		this.roadCardStatus = roadCardStatus;
	}

	public String getRoadLicenseStatus() {
		return roadLicenseStatus;
	}

	public void setRoadLicenseStatus(String roadLicenseStatus) {
		this.roadLicenseStatus = roadLicenseStatus;
	}

	public String getRoadCardFailReason() {
		return roadCardFailReason;
	}

	public void setRoadCardFailReason(String roadCardFailReason) {
		this.roadCardFailReason = roadCardFailReason;
	}

	public String getRoadLicenseReason() {
		return roadLicenseReason;
	}

	public void setRoadLicenseReason(String roadLicenseReason) {
		this.roadLicenseReason = roadLicenseReason;
	}

	public Date getTempLicenceExpires() {
		return tempLicenceExpires;
	}

	public void setTempLicenceExpires(Date tempLicenceExpires) {
		this.tempLicenceExpires = tempLicenceExpires;
	}

	public BigInteger getId() {
		return id;
	}
	public void setId(BigInteger id) {
		this.id = id;
	}
	
	public String getHeadCity() {
		return headCity;
	}
	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}
	public String getHeadNo() {
		return headNo;
	}
	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}
	
	public String getAuth() {
		return auth;
	}
	public void setAuth(String auth) {
		this.auth = auth;
	}
	
	public String getTailCity() {
		return tailCity;
	}
	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}
	public String getTailNo() {
		return tailNo;
	}
	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}
	
	public BigInteger getUserId() {
		return userId;
	}
	public void setUserId(BigInteger userId) {
		this.userId = userId;
	}
	public String getHeadDrivingUrl() {
		return headDrivingUrl;
	}
	public void setHeadDrivingUrl(String headDrivingUrl) {
		this.headDrivingUrl = headDrivingUrl;
	}
	public String getTailDrivingUrl() {
		return PictureUtil.getPictureUrl(tailDrivingUrl);
	}
	public void setTailDrivingUrl(String tailDrivingUrl) {
		this.tailDrivingUrl = tailDrivingUrl;
	}
	public String getFailureReason() {
		return failureReason;
	}
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	public Short getHeadAuthStatus() {
		return headAuthStatus;
	}
	public void setHeadAuthStatus(Short headAuthStatus) {
		this.headAuthStatus = headAuthStatus;
	}
	public String getHeadFailureReason() {
		return headFailureReason;
	}
	public void setHeadFailureReason(String headFailureReason) {
		this.headFailureReason = headFailureReason;
	}
	public Short getTailAuthStatus() {
		return tailAuthStatus;
	}
	public void setTailAuthStatus(Short tailAuthStatus) {
		this.tailAuthStatus = tailAuthStatus;
	}
	public String getTailFailureReason() {
		return tailFailureReason;
	}
	public void setTailFailureReason(String tailFailureReason) {
		this.tailFailureReason = tailFailureReason;
	}
	public String getFindGoodOnOff() {
		return findGoodOnOff;
	}
	public void setFindGoodOnOff(String findGoodOnOff) {
		this.findGoodOnOff = findGoodOnOff;
	}
	public BigInteger getSort() {
		return sort;
	}
	public void setSort(BigInteger sort) {
		this.sort = sort;
	}
	public String getStartProvinc() {
		return startProvinc;
	}
	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}
	public String getStartCity() {
		return startCity;
	}
	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}
	public String getStartArea() {
		return startArea;
	}
	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}
	public String getDestProvinc() {
		return destProvinc;
	}
	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}
	public String getDestCity() {
		return destCity;
	}
	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}
	public String getDestArea() {
		return destArea;
	}
	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}
	public String getBeginWeight() {
		return beginWeight;
	}
	public void setBeginWeight(String beginWeight) {
		this.beginWeight = beginWeight;
	}
	public String getEndWeight() {
		return endWeight;
	}
	public void setEndWeight(String endWeight) {
		this.endWeight = endWeight;
	}
	public String getLength() {
		return length;
	}
	public void setLength(String length) {
		this.length = length;
	}
	public String getWide() {
		return wide;
	}
	public void setWide(String wide) {
		this.wide = wide;
	}
	public String getHigh() {
		return high;
	}
	public void setHigh(String high) {
		this.high = high;
	}
	public String getGoodType() {
		return goodType;
	}
	public void setGoodType(String goodType) {
		this.goodType = goodType;
	}
	
	public BigInteger getPreId() {
		return preId;
	}
	public void setPreId(BigInteger preId) {
		this.preId = preId;
	}
	
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public Date getCurrentTime() {
		return currentTime;
	}
	public void setCurrentTime(Date currentTime) {
		this.currentTime = currentTime;
	}

	public String getIsAuthUpdate() {
		return isAuthUpdate;
	}
	public void setIsAuthUpdate(String isAuthUpdate) {
		this.isAuthUpdate = isAuthUpdate;
	}

	public String getCarType() {
		return carType;
	}

	public void setCarType(String carType) {
		this.carType = carType;
	}

	public String getIsPureFlat() {
		return isPureFlat;
	}

	public void setIsPureFlat(String isPureFlat) {
		this.isPureFlat = isPureFlat;
	}

	public String getMaxPayload() {
		return maxPayload;
	}

	public void setMaxPayload(String maxPayload) {
		this.maxPayload = maxPayload;
	}

	//	public String getCurrentSpeed() {
//		return currentSpeed;
//	}
//	public void setCurrentSpeed(String currentSpeed) {
//		this.currentSpeed = currentSpeed;
//	}
//	public String getCurrentDetailAddr() {
//		return currentDetailAddr;
//	}
//	public void setCurrentDetailAddr(String currentDetailAddr) {
//		this.currentDetailAddr = currentDetailAddr;
//	}
//	public String getCurrentPosition() {
//		return currentPosition;
//	}
//	public void setCurrentPosition(String currentPosition) {
//		this.currentPosition = currentPosition;
//	}
//	public Integer getCurrentStatus() {
//		return currentStatus;
//	}
//	public void setCurrentStatus(Integer currentStatus) {
//		this.currentStatus = currentStatus;
//	}
//	
//	
//	public Double getStartPointLongitude() {
//		return startPointLongitude;
//	}
//	public void setStartPointLongitude(Double startPointLongitude) {
//		this.startPointLongitude = startPointLongitude;
//	}
//	public Double getStartPointLatitude() {
//		return startPointLatitude;
//	}
//	public void setStartPointLatitude(Double startPointLatitude) {
//		this.startPointLatitude = startPointLatitude;
//	}
//	public String getDirvePositionName() {
//		String positionName = "";
//		try {
//			/*
//			 * 字符串类型（ 0 或 360：正北，大于 0 且小于 90：东北，等于 90：正东，大于 90 且小 于 180：东南，等于
//			 * 180：正南，大于 180 且小于 270：西南，等于 270：正西，大 于 270 且小于等于 359：西北，其他：未知）
//			 */
//			int positions = Integer.valueOf(currentPosition);
//			if (positions == 0 || positions == 360) {
//				positionName = "正北";
//			} else if (positions > 0 && positions < 90) {
//				positionName = "东北";
//			} else if (positions == 90) {
//				positionName = "正东";
//			} else if (positions > 90 && positions < 180) {
//				positionName = "东南";
//			} else if (positions == 180) {
//				positionName = "正南";
//			} else if (positions > 180 && positions < 270) {
//				positionName = "西南";
//			} else if (positions == 270) {
//				positionName = "正西";
//			} else if (positions > 270 && positions < 360) {
//				positionName = "西北";
//			} else {
//				positionName = "未知";
//			}
//		} catch (NumberFormatException e) {
//			positionName = "未知";
//		}
//		return positionName;
//	}
//	

	public String getHeadDrivingSubpageUrl() {
		return PictureUtil.getPictureUrl(headDrivingSubpageUrl);
	}

	public void setHeadDrivingSubpageUrl(String headDrivingSubpageUrl) {
		this.headDrivingSubpageUrl = headDrivingSubpageUrl;
	}

	public String getHeadTransportHomepageUrl() {
		return PictureUtil.getPictureUrl(headTransportHomepageUrl);
	}

	public void setHeadTransportHomepageUrl(String headTransportHomepageUrl) {
		this.headTransportHomepageUrl = headTransportHomepageUrl;
	}

	public String getHeadTransportSubpageUrl() {
		return PictureUtil.getPictureUrl(headTransportSubpageUrl);
	}

	public void setHeadTransportSubpageUrl(String headTransportSubpageUrl) {
		this.headTransportSubpageUrl = headTransportSubpageUrl;
	}

	public String getTailDrivingSubpageUrl() {
		return PictureUtil.getPictureUrl(tailDrivingSubpageUrl);
	}

	public void setTailDrivingSubpageUrl(String tailDrivingSubpageUrl) {
		this.tailDrivingSubpageUrl = tailDrivingSubpageUrl;
	}

	public String getTailTransportHomepageUrl() {
		return PictureUtil.getPictureUrl(tailTransportHomepageUrl);
	}

	public void setTailTransportHomepageUrl(String tailTransportHomepageUrl) {
		this.tailTransportHomepageUrl = tailTransportHomepageUrl;
	}

	public String getTailTransportSubpageUrl() {
		return PictureUtil.getPictureUrl(tailTransportSubpageUrl);
	}

	public void setTailTransportSubpageUrl(String tailTransportSubpageUrl) {
		this.tailTransportSubpageUrl = tailTransportSubpageUrl;
	}

	public Integer getHeadTransportAuthStatus() {
		return headTransportAuthStatus;
	}

	public void setHeadTransportAuthStatus(Integer headTransportAuthStatus) {
		this.headTransportAuthStatus = headTransportAuthStatus;
	}

	public Integer getTailTransportAuthStatus() {
		return tailTransportAuthStatus;
	}

	public void setTailTransportAuthStatus(Integer tailTransportAuthStatus) {
		this.tailTransportAuthStatus = tailTransportAuthStatus;
	}

	public String getHeadDrivingExpiredTips() {
		return headDrivingExpiredTips;
	}

	public void setHeadDrivingExpiredTips(String headDrivingExpiredTips) {
		this.headDrivingExpiredTips = headDrivingExpiredTips;
	}

	public String getHeadTransportExpiredTips() {
		return headTransportExpiredTips;
	}

	public void setHeadTransportExpiredTips(String headTransportExpiredTips) {
		this.headTransportExpiredTips = headTransportExpiredTips;
	}

	public String getTailDrivingExpiredTips() {
		return tailDrivingExpiredTips;
	}

	public void setTailDrivingExpiredTips(String tailDrivingExpiredTips) {
		this.tailDrivingExpiredTips = tailDrivingExpiredTips;
	}

	public String getTailTransportExpiredTips() {
		return tailTransportExpiredTips;
	}

	public void setTailTransportExpiredTips(String tailTransportExpiredTips) {
		this.tailTransportExpiredTips = tailTransportExpiredTips;
	}

	public Date getHeadDrivingExpiredTime() {
		return headDrivingExpiredTime;
	}

	public void setHeadDrivingExpiredTime(Date headDrivingExpiredTime) {
		this.headDrivingExpiredTime = headDrivingExpiredTime;
	}

	public Date getHeadTransportExpiredTime() {
		return headTransportExpiredTime;
	}

	public void setHeadTransportExpiredTime(Date headTransportExpiredTime) {
		this.headTransportExpiredTime = headTransportExpiredTime;
	}

	public Date getTailDrivingExpiredTime() {
		return tailDrivingExpiredTime;
	}

	public void setTailDrivingExpiredTime(Date tailDrivingExpiredTime) {
		this.tailDrivingExpiredTime = tailDrivingExpiredTime;
	}

	public Date getTailTransportExpiredTime() {
		return tailTransportExpiredTime;
	}

	public void setTailTransportExpiredTime(Date tailTransportExpiredTime) {
		this.tailTransportExpiredTime = tailTransportExpiredTime;
	}

	public String getHeadTransportFailReason() {
		return headTransportFailReason;
	}

	public void setHeadTransportFailReason(String headTransportFailReason) {
		this.headTransportFailReason = headTransportFailReason;
	}

	public String getTailTransportFailReason() {
		return tailTransportFailReason;
	}

	public void setTailTransportFailReason(String tailTransportFailReason) {
		this.tailTransportFailReason = tailTransportFailReason;
	}

	public String getTailPhotoUrl() {
		return PictureUtil.getPictureUrl(tailPhotoUrl);
	}

	public void setTailPhotoUrl(String tailPhotoUrl) {
		this.tailPhotoUrl = tailPhotoUrl;
	}

	public String getTailLength() {
		return tailLength;
	}

	public void setTailLength(String tailLength) {
		this.tailLength = tailLength;
	}

	public String getTailWidth() {
		return tailWidth;
	}

	public void setTailWidth(String tailWidth) {
		this.tailWidth = tailWidth;
	}

	public String getTailHeight() {
		return tailHeight;
	}

	public void setTailHeight(String tailHeight) {
		this.tailHeight = tailHeight;
	}

	public Boolean isCarIsNeedImprovenData() {
		return carIsNeedImprovenData;
	}

	public void setCarIsNeedImprovenData(Boolean carIsNeedImprovenData) {
		this.carIsNeedImprovenData = carIsNeedImprovenData;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	public String getExpiredTips() {
		return expiredTips;
	}

	public void setExpiredTips(String expiredTips) {
		this.expiredTips = expiredTips;
	}

	public String getCarTypeName() {
		return carTypeName;
	}

	public void setCarTypeName(String carTypeName) {
		this.carTypeName = carTypeName;
	}
}
