package com.tyt.user.querybean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.model.UserPermission;
import com.tyt.plat.utils.BigDecimalSerialize;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * User: Administrator Date: 13-12-8 Time: 下午7:25
 */
public class UserBean {
	private Long id;
	private String cellPhone;
	private Integer serveDays;
	private String trueName;
	private Integer userType;
	private String version;
	private Long systemTime;
	private String ticket;
	private Integer infoUploadFlag;
	private Integer infoPublishFlag;
	private Integer killBill;
	private Integer phoneOpenFlag;
	private Integer qqBoxFlag;
	private Integer phoneServeDays;
	private String userName;
	private Integer verifyFlag;
	private Integer userSign;
	String isCar;// '是否完善了车的信息',
	String isBank;// '是否绑定银行卡信息',
	String headUrl;// '头像url',
	String domain;// '域名',
	private Timestamp payDate;
	private Timestamp renewalDate;
	private Integer renewalYears;
	private Integer payStatus;// 付费状态

	private Integer verifyPhotoSign;
	// 用户分数
	private Integer userPart;
	// 注册时间
	private Timestamp ctime;
	private Timestamp endTime;// 用户到期时间
	private String bcarIdentityLables;// 大板车身份标签
	private String scarIdentityLables;// 设备身份标签
	private Integer userClass;// 用户分类1、发货方2、车辆方 见 source user_class
	private Integer identityType;// 用户身份见source表 user_identity_type
	/*
	 * 用户身份认证标志0未认证1通过2认证中3认证失败
	 */
	private Integer userIdentityStatus = 0;

	//用户定向升级
	private Integer isUpdate = 1;//是否需要升级  1:不需要升级   2：需要升级
	private String content;     //提示内容
	private String url;         //新版本下载地址
	private String upgradeMode;//升级模式 0：提示升级   1：强制升级
	private String goalVersion;//升级到的版本

	private Integer isVerifySales; //是否销售审核通过   0未审核通过   1：审核通过但异常  2：正常

	private Integer myGoodsMenu;
	//2018-1-24 增加 身份证号
	private String idCard;
	// 销售审核一级身份明长城
	private String deliverTypeOneName;
	private String deliverTypeOneCode;
	private Integer pwdStatus;
	private int userLevelStatus;
	private int level2RemainDays;
	private int couponNum;
	private String nonVipContent;
	// 是否车辆认证  是否车辆认证 0未认证或认证中或认证失败 1认证成功
	private int carVerifyFlag;
	// 5930 增加权益相关
	private List<UserPermission> userPermissions;
	// 用户是否绑定设备
	private Integer bindStatus;

	//5950 增加信任分
    private String transportScore;  //分数
    private String tsRank;  //评级
	//6000 增加当前用户在平台的交易量
	private String tradeNums;
	//司机数
	private Integer carDriverNumber;

	// 2021-01-23 开票、成为调度车
	/**
	 * 是否调度车 1是 0不是
	 */
	private Integer isDispatch;
	/**
	 * 是否专车 1 是 2不是
	 */
	private Integer isSpecialCar;

	/**
	 * 企业认证状态 0未认证1通过2认证中3认证失败
	 */
	private Integer enterpriseAuthStatus;

	private Integer staffCount;

	private CreditConfigInfo creditConfigInfo;

	public Integer getIsSpecialCar() {
		return isSpecialCar;
	}

	public void setIsSpecialCar(Integer isSpecialCar) {
		this.isSpecialCar = isSpecialCar;
	}
	/**
	 * 短信验证码登录 如果为注册会先注册，登录后返回新用户标识，app用来判断是否跳转实名认证页面
	 * newUserFlag 1 是 0 否
	 */
	private Integer newUserFlag;

	/**
	 * 是否有弹框 0-否 1-是
	 */
	private Integer isPopUp;

	/**
	 * 信用分
	 */
	@JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
	@JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
	private BigDecimal totalScore;

	/**
	 * 信用分等级 "1 2 3 4 5"
	 */
	private Integer rankLevel;

	/**
	 * 距下一等级相差人数（货）
	 */
	private Integer lastLevelGap;

	private Integer carTotalServerScore;//'车方服务分_new'

	private Integer carServerRankScore;//'车方等级_new'

	private Integer isShowIdentityPop; //是否显示新版身份认证弹窗 0 显示 1不显示

	/**
	 * 车方信用等级
	 */
	@Getter
	@Setter
	private String carCreditRankLevel;

	/**
	 * 车方信用等级类型 1:运点值计算  2：购买会员  3：会员首次登录
	 */
	@Getter
	@Setter
	private Integer carCreditRankLeveType;

	/**
	 * 车方运点值
	 */
	@Getter
	@Setter
	private BigDecimal carCreditScore;
	/**
	 * 车主排名
	 */
	@Getter
	@Setter
	private Integer carRankNum;
	/**
	 * 距下一级相差的人数
	 */
	@Getter
	@Setter
	private Integer carLastLevelGap;

	/**
	 * 是否弹窗货主身份标签 0 不显示 1显示
	 */
	@Getter
	@Setter
	private Integer showGoodsTypePop;

	/**
	 * 一级货主标签值
	 */
	@Getter
	@Setter
	private Integer goodsTypeFirst;

	/**
	 * 二级货主标签值
	 */
	@Getter
	@Setter
	private Integer goodsTypeSecond;

	/**
	 * 待评价订单数量
	 */
	@Getter
	@Setter
	private Integer feedBackTodoCount;

	/**
	 * 有效期.
	 */
	@Getter
	@Setter
	private Date idCardValidDate;

	/**
	 * 是否长期.
	 */
	@Getter
	@Setter
	private Integer idCardLongTerm;

	/**
	 * 过期类型.
	 */
	@Getter
	@Setter
	private Integer validDateFlag;

	public Integer getCarTotalServerScore() {
		return carTotalServerScore;
	}

	public void setCarTotalServerScore(Integer carTotalServerScore) {
		this.carTotalServerScore = carTotalServerScore;
	}

	public Integer getCarServerRankScore() {
		return carServerRankScore;
	}

	public void setCarServerRankScore(Integer carServerRankScore) {
		this.carServerRankScore = carServerRankScore;
	}

	public BigDecimal getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(BigDecimal totalScore) {
		this.totalScore = totalScore;
	}

	public Integer getRankLevel() {
		return rankLevel;
	}

	public void setRankLevel(Integer rankLevel) {
		this.rankLevel = rankLevel;
	}

	public Integer getLastLevelGap() {
		return lastLevelGap;
	}

	public void setLastLevelGap(Integer lastLevelGap) {
		this.lastLevelGap = lastLevelGap;
	}

	public Integer getCarDriverNumber() {
		return carDriverNumber;
	}

	public void setCarDriverNumber(Integer carDriverNumber) {
		this.carDriverNumber = carDriverNumber;
	}

	public int getCouponNum() {
		return couponNum;
	}

	public void setCouponNum(int couponNum) {
		this.couponNum = couponNum;
	}

	public int getUserLevelStatus() {
		return userLevelStatus;
	}

	public void setUserLevelStatus(int userLevelStatus) {
		this.userLevelStatus = userLevelStatus;
	}

	public int getLevel2RemainDays() {
		return level2RemainDays;
	}

	public void setLevel2RemainDays(int level2RemainDays) {
		this.level2RemainDays = level2RemainDays;
	}

	public String getNonVipContent() {
		return nonVipContent;
	}

	public void setNonVipContent(String nonVipContent) {
		this.nonVipContent = nonVipContent;
	}

	public int getCarVerifyFlag() {
		return carVerifyFlag;
	}

	public void setCarVerifyFlag(int carVerifyFlag) {
		this.carVerifyFlag = carVerifyFlag;
	}

	public Integer getPwdStatus() {
		return pwdStatus;
	}

	public void setPwdStatus(Integer pwdStatus) {
		this.pwdStatus = pwdStatus;
	}

	public String getDeliverTypeOneName() {
		return deliverTypeOneName;
	}

	public void setDeliverTypeOneName(String deliverTypeOneName) {
		this.deliverTypeOneName = deliverTypeOneName;
	}

	public Integer getUserIdentityStatus() {
		return userIdentityStatus;
	}

	public void setUserIdentityStatus(Integer userIdentityStatus) {
		this.userIdentityStatus = userIdentityStatus;
	}

	public Integer getUserClass() {
		return userClass;
	}

	public void setUserClass(Integer userClass) {
		this.userClass = userClass;
	}

	public Integer getIdentityType() {
		return identityType;
	}

	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

	public String getUserName() {
		return StringUtils.isEmpty(userName) ? "用户" + id : userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Integer getVerifyFlag() {
		return verifyFlag;
	}

	public void setVerifyFlag(Integer verifyFlag) {
		this.verifyFlag = verifyFlag;
	}

	public Integer getPhoneServeDays() {
		return phoneServeDays;
	}

	public void setPhoneServeDays(Integer phoneServeDays) {
		this.phoneServeDays = phoneServeDays;
	}

	public UserBean() {
		this.systemTime = new java.util.Date().getTime();
	}

	public Integer getServeDays() {
		return serveDays;
	}

	public void setServeDays(Integer serveDays) {
		this.serveDays = serveDays;
	}

	public String getTrueName() {
		return trueName;
	}

	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Long getSystemTime() {
		return systemTime;
	}

	public void setSystemTime(Long systemTime) {
		this.systemTime = systemTime;
	}

	public String getTicket() {
		return ticket;
	}

	public void setTicket(String ticket) {
		this.ticket = ticket;
	}

	public Integer getInfoUploadFlag() {
		return infoUploadFlag;
	}

	public void setInfoUploadFlag(Integer infoUploadFlag) {
		this.infoUploadFlag = infoUploadFlag;
	}

	public Integer getInfoPublishFlag() {
		return infoPublishFlag;
	}

	public void setInfoPublishFlag(Integer infoPublishFlag) {
		this.infoPublishFlag = infoPublishFlag;
	}

	public Integer getKillBill() {
		return killBill;
	}

	public void setKillBill(Integer killBill) {
		this.killBill = killBill;
	}

	public Integer getPhoneOpenFlag() {
		return phoneOpenFlag;
	}

	public void setPhoneOpenFlag(Integer phoneOpenFlag) {
		this.phoneOpenFlag = phoneOpenFlag;
	}

	public Integer getQqBoxFlag() {
		return qqBoxFlag;
	}

	public void setQqBoxFlag(Integer qqBoxFlag) {
		this.qqBoxFlag = qqBoxFlag;
	}

	public Integer getUserSign() {
		return userSign;
	}

	public void setUserSign(Integer userSign) {
		this.userSign = userSign;
	}

	public String getIsCar() {
		return isCar;
	}

	public void setIsCar(String isCar) {
		this.isCar = isCar;
	}

	public String getIsBank() {
		return isBank;
	}

	public void setIsBank(String isBank) {
		this.isBank = isBank;
	}

	public String getHeadUrl() {
		return headUrl;
	}

	public void setHeadUrl(String headUrl) {
		this.headUrl = headUrl;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public Timestamp getPayDate() {
		return payDate;
	}

	public void setPayDate(Timestamp payDate) {
		this.payDate = payDate;
	}

	public Timestamp getRenewalDate() {
		return renewalDate;
	}

	public void setRenewalDate(Timestamp renewalDate) {
		this.renewalDate = renewalDate;
	}

	public Integer getRenewalYears() {
		return renewalYears;
	}

	public void setRenewalYears(Integer renewalYears) {
		this.renewalYears = renewalYears;
	}

	public Integer getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(Integer payStatus) {
		this.payStatus = payStatus;
	}

	public Integer getVerifyPhotoSign() {
		return verifyPhotoSign;
	}

	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
		this.verifyPhotoSign = verifyPhotoSign;
	}

	public Integer getUserPart() {
		return userPart;
	}

	public void setUserPart(Integer userPart) {
		this.userPart = userPart;
	}

	public Timestamp getCtime() {
		return ctime;
	}

	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}

	public Timestamp getEndTime() {
		return endTime;
	}

	public void setEndTime(Timestamp endTime) {
		this.endTime = endTime;
	}

	public String getBcarIdentityLables() {
		return bcarIdentityLables;
	}

	public void setBcarIdentityLables(String bcarIdentityLables) {
		this.bcarIdentityLables = bcarIdentityLables;
	}

	public String getScarIdentityLables() {
		return scarIdentityLables;
	}

	public void setScarIdentityLables(String scarIdentityLables) {
		this.scarIdentityLables = scarIdentityLables;
	}

	public Integer getIsUpdate() {
		return isUpdate;
	}

	public void setIsUpdate(Integer isUpdate) {
		this.isUpdate = isUpdate;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getUpgradeMode() {
		return upgradeMode;
	}

	public void setUpgradeMode(String upgradeMode) {
		this.upgradeMode = upgradeMode;
	}

	public String getGoalVersion() {
		return goalVersion;
	}

	public void setGoalVersion(String goalVersion) {
		this.goalVersion = goalVersion;
	}

	public Integer getIsVerifySales() {
		return isVerifySales;
	}

	public void setIsVerifySales(Integer isVerifySales) {
		this.isVerifySales = isVerifySales;
	}

	public Integer getMyGoodsMenu() {
		return myGoodsMenu;
	}

	public void setMyGoodsMenu(Integer myGoodsMenu) {
		this.myGoodsMenu = myGoodsMenu;
	}
	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public List<UserPermission> getUserPermissions() {
		return userPermissions;
	}

	public void setUserPermissions(List<UserPermission> userPermissions) {
		this.userPermissions = userPermissions;
	}

	public Integer getBindStatus() {
		return bindStatus;
	}

	public void setBindStatus(Integer bindStatus) {
		this.bindStatus = bindStatus;
	}

    public String getTransportScore() {
        return transportScore;
    }

    public void setTransportScore(String transportScore) {
        this.transportScore = transportScore;
    }

    public String getTsRank() {
        return tsRank;
    }

    public void setTsRank(String tsRank) {
        this.tsRank = tsRank;
    }

	public String getTradeNums() {
		return tradeNums;
	}

	public void setTradeNums(String tradeNums) {
		this.tradeNums = tradeNums;
	}

	public Integer getIsDispatch() {
		return isDispatch;
	}

	public void setIsDispatch(Integer isDispatch) {
		this.isDispatch = isDispatch;
	}

	public Integer getNewUserFlag() {
		return newUserFlag;
	}

	public void setNewUserFlag(Integer newUserFlag) {
		this.newUserFlag = newUserFlag;
	}

	public Integer getEnterpriseAuthStatus() {
		return enterpriseAuthStatus;
	}

	public void setEnterpriseAuthStatus(Integer enterpriseAuthStatus) {
		this.enterpriseAuthStatus = enterpriseAuthStatus;
	}

	public Integer getStaffCount() {
		return staffCount;
	}

	public void setStaffCount(Integer staffCount) {
		this.staffCount = staffCount;
	}

	public Integer getIsPopUp() {
		return isPopUp;
	}

	public void setIsPopUp(Integer isPopUp) {
		this.isPopUp = isPopUp;
	}

	public Integer getIsShowIdentityPop() {
		return isShowIdentityPop;
	}

	public void setIsShowIdentityPop(Integer isShowIdentityPop) {
		this.isShowIdentityPop = isShowIdentityPop;
	}

	public String getDeliverTypeOneCode() {
		return deliverTypeOneCode;
	}

	public void setDeliverTypeOneCode(String deliverTypeOneCode) {
		this.deliverTypeOneCode = deliverTypeOneCode;
	}

	public CreditConfigInfo getCreditConfigInfo() {
		return creditConfigInfo;
	}

	public void setCreditConfigInfo(CreditConfigInfo creditConfigInfo) {
		this.creditConfigInfo = creditConfigInfo;
	}


}
