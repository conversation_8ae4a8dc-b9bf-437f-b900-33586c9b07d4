package com.tyt.user.querybean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.plat.utils.BigDecimalSerialize;
import lombok.Data;

import javax.persistence.Entity;
import java.math.BigDecimal;

/**
 * @ClassName UserCreditBean
 * @Description TODO
 * <AUTHOR> Lion
 * @Date 2022/5/30 13:16
 * @Verdion 1.0
 **/
@Data
public class UserCreditBean {

    /**
     *'总得分'
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal totalScore;
    /**
     *'信用分等级'
     */
    private Integer rankLevel;

    /**
     *'上周信用分等级'
     */
    private Integer lastWeekRankLevel;

    private Integer carTotalServerScore;//'车方服务分_new'

    private Integer carServerRankScore;//'车方等级_new'

    /**
     * 头像url
     */
    private String headUrl;
}
