package com.tyt.user.querybean;

import java.io.Serializable;
import java.util.Date;

/**
 * SELECT
 t.`user_id`,
 t.`user_phone`,
 t.`client_id`,
 t.`task_id`,
 ta.`begin_version`,
 ta.`end_version`,
 ta.`upgrade_begintime`,
 ta.`upgrade_endtime`,
 ta.`client_type`,
 ta.`goal_version`,
 ta.`upgrade_mode`,
 ta.`coutent`,
 ta.`url`
 FROM
 tyt_force_upgrade_user t
 LEFT JOIN tyt_upgrade_task ta
 ON t.`task_id` = ta.`id`
 WHERE ta.`status` = 1
 */
/**
 * 用户定向升级Bean
 *
 */
public class SpecifiedUserUpgradeBean implements Serializable{

    private static final long serialVersionUID = 1746224544604357481L;
    private Long userId;
    private String userPhone;
    private String clientId;
    private Long taskId;
    private String beginVersion;
    private String endVersion;
    private Date upgradeBegintime;
    private Date upgradeEndtime;
    private Integer clientType;
    private String goalVersion;
    private Integer upgradeMode;
    private String coutent;
    private String url;
    private String appType;

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getBeginVersion() {
        return beginVersion;
    }

    public void setBeginVersion(String beginVersion) {
        this.beginVersion = beginVersion;
    }

    public String getEndVersion() {
        return endVersion;
    }

    public void setEndVersion(String endVersion) {
        this.endVersion = endVersion;
    }

    public Date getUpgradeBegintime() {
        return upgradeBegintime;
    }

    public void setUpgradeBegintime(Date upgradeBegintime) {
        this.upgradeBegintime = upgradeBegintime;
    }

    public Date getUpgradeEndtime() {
        return upgradeEndtime;
    }

    public void setUpgradeEndtime(Date upgradeEndtime) {
        this.upgradeEndtime = upgradeEndtime;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getGoalVersion() {
        return goalVersion;
    }

    public void setGoalVersion(String goalVersion) {
        this.goalVersion = goalVersion;
    }

    public Integer getUpgradeMode() {
        return upgradeMode;
    }

    public void setUpgradeMode(Integer upgradeMode) {
        this.upgradeMode = upgradeMode;
    }

    public String getCoutent() {
        return coutent;
    }

    public void setCoutent(String coutent) {
        this.coutent = coutent;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
