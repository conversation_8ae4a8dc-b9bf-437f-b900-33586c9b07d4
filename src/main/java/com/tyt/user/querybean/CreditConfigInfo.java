package com.tyt.user.querybean;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName：CreditConfigInfo
 * @Author: TYT
 * @Date: 2023/4/28 16:15
 * @Description:
 */
@Data
public class CreditConfigInfo {
    //用户Id
    private Long userId;
    //配置开始时间
    private Date startTime;
    //配置结束时间
    private Date endTime;
    //目标单数量
    private Integer targetNumber;
    //级别
    private String level;
    //货方完单进度数量
    private Integer  goodsCurOrderNum;
    //是否已加入
    private Integer isJoin;
    //通道Id
    private Long subId;
    //配置Id
    private Long configId;

}
