package com.tyt.user.enums;

import com.tyt.messagecenter.core.utils.CommonUtil;
import lombok.Getter;

/**
 * 货主身份认证
 * <AUTHOR>
 * @version 1.0
 * @date 2023/09/18 11:35
 */
public enum UserLabelTypeEnum {
    /**
     * firstCode	货主一级身份  个人1    企业2
     * secondCode	货主二级身份 个人一手货主1  个人货站2  企业一手货主1  企业物流公司2  企业货站3
     * alias	转换成别名 1个人货主  2企业货主  3货站 4物流公司
     */
    CODE_1_1(1, 1, "个人一手货主", 1),
    CODE_1_2(1, 2, "个人货站", 3),
    CODE_2_1(2, 1, "企业一手货主", 2),
    CODE_2_2(2, 2, "企业物流公司", 4),
    CODE_2_3(2, 3, "企业货站", 3),
    ;

    @Getter
    private Integer firstCode;

    @Getter
    private Integer secondCode;

    @Getter
    private String message;

    @Getter
    private Integer alias;

    UserLabelTypeEnum(Integer firstCode, Integer secondCode, String message, Integer alias) {
        this.firstCode = firstCode;
        this.secondCode = secondCode;
        this.message = message;
        this.alias = alias;
    }


    public static UserLabelTypeEnum getEnum(Integer firstCode, Integer secondCode) {
        if (CommonUtil.hasNull(firstCode, secondCode)) {
            return null;
        }
        for (UserLabelTypeEnum typeEnum : UserLabelTypeEnum.values()) {
            if (typeEnum.getFirstCode().equals(firstCode) && typeEnum.getSecondCode().equals(secondCode)) {
                return typeEnum;
            }
        }
        return null;
    }

}
