package com.tyt.user.enums;

import lombok.Getter;

/**
 * 企业认证状态枚举
 * 基本信息审核状态:0-未提交;1-审核中;2-已通过;3-审核驳回;4-审核失败;5-信息已过期;
 *
 * <AUTHOR>
 * @since 2024-09-29 10:46
 */
@Getter
public enum InfoVerifyStatusEnum {
    NOT_SUBMIT(0, "未提交"),
    AUDITING(1, "审核中"),
    AUDIT_PASSED(2, "已通过"),
    AUDIT_REJECTED(3, "审核驳回"),
    AUDIT_FAILED(4, "审核失败"),
    EXPIRED(5, "信息已过期")
    ;
    private Integer code;
    private String name;
    InfoVerifyStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
