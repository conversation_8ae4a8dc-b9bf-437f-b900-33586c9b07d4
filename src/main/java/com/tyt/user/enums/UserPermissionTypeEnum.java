package com.tyt.user.enums;

import lombok.Getter;

/**
* 用户权益类型
* <AUTHOR>
* @since 2023/11/7 14:54
*/
@Getter
public enum UserPermissionTypeEnum {
    CAR_VIP("100101","车会员"),
    CAR_NUM("100102","拨打次数"),
    CAR_NUM_MEAL("100103","拨打次数(套餐)"),
    GOODS_VIP("100201","货会员"),
    GOODS_NUM("100202","发货次数"),
    GOODS_NUM_NEW("100203","新货会员发货次数"),
    EXCELLENT_GOODS_NUM("100502","优车发货次数");

    private final String typeId;
    private final String typeName;

    UserPermissionTypeEnum(String typeId, String typeName) {
        this.typeId = typeId;
        this.typeName = typeName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(String reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getTypeId().equals(reqCode);
        return result;
    }

}
