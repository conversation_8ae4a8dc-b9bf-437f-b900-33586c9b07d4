package com.tyt.user.enums;

/**
 * 变更状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/09/18 11:24
 */
public enum CellPhoneChangeAuditStatusEnum {
    CHECK_PENDING(1,"待审核"),
    PASSED(2,"审核通过"),
    REJECT(3,"驳回");
    private Integer auditStatus;

    private String auditMessage;

    CellPhoneChangeAuditStatusEnum(Integer auditStatus, String auditMessage) {
        this.auditStatus = auditStatus;
        this.auditMessage = auditMessage;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public String getAuditMessage() {
        return auditMessage;
    }
}
