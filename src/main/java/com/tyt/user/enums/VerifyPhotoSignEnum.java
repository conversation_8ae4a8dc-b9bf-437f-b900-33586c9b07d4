package com.tyt.user.enums;

/**
 * 身份认证状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/09/18 11:35
 */
public enum VerifyPhotoSignEnum {
    CODE_0(0,"未认证"),
    CODE_1(1,"认证通过"),
    CODE_2(2,"认证中"),
    CODE_3(3,"认证失败"),;

    private Integer code;

    private String message;

    VerifyPhotoSignEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
