package com.tyt.user.bean;

import com.tyt.model.CarDetailHead;
import com.tyt.model.CarDetailTail;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/1/18 10:40
 */
@Data
public class DispatchCarBean implements Serializable {

    private static final long serialVersionUID = 4686463551474987418L;
    /**
     * 道路运输证正面
     */
    private String roadCardPositiveUrl;
    private String roadCardOtherSideUrl;
    private String roadLicenseNoUrl;
    private String roadTransportType;
    /**
     * 挂车长度
     */
    private String tailLength;
    /**
     * 挂车宽度
     */
    private String tailWidth;
    /**
     * 挂车高度
     */
    private String tailHeight;
    private Long id;


}
