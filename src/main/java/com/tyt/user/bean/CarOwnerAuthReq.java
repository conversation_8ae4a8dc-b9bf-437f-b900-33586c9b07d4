package com.tyt.user.bean;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/01 09:47
 */
@Data
public class CarOwnerAuthReq {
    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 车辆id
     */
    @NotNull(message = "车辆id不能为空")
    private Long carId;

    /**
     * 车辆车牌号
     */
    @NotBlank(message = "车辆车牌号不能为空")
    private String headNo;

    /**
     *车牌所有人
     */
    @NotBlank(message = "车牌所有人不能为空")
    private String headName;

    /**
     * 车主手机号
     */
//    @NotBlank(message = "车主手机号不能为空")
    private String carUserPhone;

    /**
     * 车主身份  0=企业 1=个人
     */
//    @NotNull(message = "车主身份不能为空")
    private Integer carIdentity;

    /**
     * 车主关系  0 非挂靠 1 挂靠
     */
    @NotNull(message = "车主关系不能为空")
    private Integer carRelation;

    /**
     * 车主证件号码 个人就是身份证号 企业就是企业纳税人识别码
     */
//    @NotNull(message = "车主证件号码不能为空")
    private Integer carOwnerIdNumber;

    /**
     * 证件照片
     */
    @NotNull(message = "证件照片不能为空")
    private String idPhoto;

    /**
     *认证材料类型 0 挂靠协议 1承诺书
     */
    @NotNull(message = "认证材料类型不能为空")
    private Integer authMaterialsType;

    /**
     * 证件材料-承诺书 （最多四张）\\ 挂靠协议 多张以逗号分割
     */
//    @NotNull(message = "证件材料不能为空")
    private String certificateMaterial;
}
