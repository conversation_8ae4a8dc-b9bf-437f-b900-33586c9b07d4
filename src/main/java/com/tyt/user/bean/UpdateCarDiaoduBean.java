package com.tyt.user.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description 更新车辆信息实体类 -- 调度版本
 * <AUTHOR>
 * @date 2020/5/11 11:19
 * @param
 * @return
 */
@Data
public class UpdateCarDiaoduBean {

    @NotNull(message = "用户Id不能为空！")
    private Long userId;
    @NotNull(message = "车辆Id不能为空！")
    private Long carId;

    private String driverName;
    private String driverPhone;

    private String secondaryDriverName;
    private String secondaryDriverPhone;

    /**
     * 主司机用户ID
     */
    private Long driverUserId;

    /**
     * 副司机用户ID
     */
    private Long secondaryDriverUserId;

    /**
     * 删除司机类型：1.删除主司机 2.删除副司机
     */
    private Integer deleteDriverType;
}
