package com.tyt.user.bean;

import com.tyt.model.ApiDataUserCreditInfoTwo;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/02/20
 */
@Data
public class UserCreditMonthlyReportVO implements Serializable {

    /**
     * 总得分
     */
    private Integer totalScore;
    /**
     * '信用分等级'
     */
    private Integer rankLevel;

    /**
     * 履约量得分
     */
    private Integer performanceNumScore;

    /**
     * 成交率得分
     */
    private Integer closingRatioScore;

    /**
     * 取消率得分
     */
    private Integer cancelRatioScore;

    /**
     * 有责客诉量得分
     */
    private Integer dutyComplaintNumScore;

    /**
     * 任务分无投诉
     */
    private Integer taskNoComplaintScore;

    /**
     * 任务分无取消
     */
    private Integer taskNoCancelScore;

    /**
     * 重货率得分
     */
    private Integer duplicateGoodsRatioScore;

    /**
     * 上期履约量得分
     */
    private Integer previousIssuePerformanceNumScore;

    /**
     * 上期成交率得分
     */
    private Integer previousIssueClosingRatioScore;

    /**
     * 上期取消率得分
     */
    private Integer previousIssueCancelRatioScore;

    /**
     * 上期有责客诉量得分
     */
    private Integer previousIssueDutyComplaintNumScore;

    /**
     * 上期任务分无投诉
     */
    private Integer previousIssueTaskNoComplaintScore;

    /**
     * 上期任务分无取消
     */
    private Integer previousIssueTaskNoCancelScore;

    /**
     * 上期重货率得分
     */
    private Integer previousIssueDuplicateGoodsRatioScore;

    /**
     * 上期信用得分
     */
    private Integer previousIssueCreditScore;

    /**
     * 上期信用等级，0或空:无信用等级，1:D,2:C,3:B,4:A,5:S
     */
    private Integer previousIssueRankLevel;


    public static UserCreditMonthlyReportVO fromApiUserCreditInfo(ApiDataUserCreditInfoTwo creditInfo) {
        UserCreditMonthlyReportVO result = new UserCreditMonthlyReportVO();
        if (creditInfo == null) {
            return result;
        }

        result.setTotalScore(result.covertBigDecimal(creditInfo.getTotalScore()));
        result.setRankLevel(creditInfo.getRankLevel());
        result.setPerformanceNumScore(result.covertBigDecimal(creditInfo.getPerformanceNumScore()));
        result.setClosingRatioScore(result.covertBigDecimal(creditInfo.getClosingRatioScore()));
        result.setCancelRatioScore(result.covertBigDecimal(creditInfo.getCancelRatioScore()));
        result.setDutyComplaintNumScore(result.covertBigDecimal(creditInfo.getDutyComplaintNumScore()));
        result.setTaskNoComplaintScore(result.covertBigDecimal(creditInfo.getTaskNoComplaintScore()));
        result.setTaskNoCancelScore(result.covertBigDecimal(creditInfo.getTaskNoCancelScore()));
        result.setDuplicateGoodsRatioScore(result.covertBigDecimal(creditInfo.getDuplicateGoodsRatioScore()));

        result.setPreviousIssueCreditScore(result.covertBigDecimal(creditInfo.getPreviousIssueCreditScore()));
        result.setPreviousIssueRankLevel(creditInfo.getPreviousIssueRankLevel());
        result.setPreviousIssuePerformanceNumScore(
                result.covertBigDecimal(creditInfo.getPreviousIssuePerformanceNumScore()));
        result.setPreviousIssueClosingRatioScore(
                result.covertBigDecimal(creditInfo.getPreviousIssueClosingRatioScore()));
        result.setPreviousIssueCancelRatioScore(
                result.covertBigDecimal(creditInfo.getPreviousIssueCancelRatioScore()));
        result.setPreviousIssueDutyComplaintNumScore(
                result.covertBigDecimal(creditInfo.getPreviousIssueDutyComplaintNumScore()));
        result.setPreviousIssueTaskNoComplaintScore(
                result.covertBigDecimal(creditInfo.getPreviousIssueTaskNoComplaintScore()));
        result.setPreviousIssueTaskNoCancelScore(
                result.covertBigDecimal(creditInfo.getPreviousIssueTaskNoCancelScore()));
        result.setPreviousIssueDuplicateGoodsRatioScore(
                result.covertBigDecimal(creditInfo.getPreviousIssueDuplicateGoodsRatioScore()));

        return result;
    }

    private Integer covertBigDecimal(BigDecimal origin) {
        return origin == null ? null : origin.intValue();
    }
}
