package com.tyt.user.bean;

import com.alibaba.fastjson.JSON;

public class RightsCheckBean {
	private int canContinue;
	private String promptContent;
	private int promptType;

	public RightsCheckBean(int canContinue) {
		this.canContinue = canContinue;
	}
	
	public int getCanContinue() {
		return canContinue;
	}

	public void setCanContinue(int canContinue) {
		this.canContinue = canContinue;
	}

	public String getPromptContent() {
		return promptContent;
	}

	public void setPromptContent(String promptContent) {
		this.promptContent = promptContent;
	}

	public int getPromptType() {
		return promptType;
	}

	public void setPromptType(int promptType) {
		this.promptType = promptType;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
