package com.tyt.user.bean;

import lombok.Data;
import javax.validation.constraints.NotNull;

/**
 * @ClassName UpdateCarBean
 * @Description 更新车辆信息实体类
 * <AUTHOR>
 * @Date 2019-07-03 15:53
 * @Version 1.0
 */
@Data
public class UpdateCarBean {

    @NotNull(message = "用户Id不能为空！")
    private Long userId;
    @NotNull(message = "车辆Id不能为空！")
    private Long carId;
    private String maxPayload;
    private Integer carType;

    private String driverName;
    private String driverPhone;

    private String secondaryDriverName;
    private String secondaryDriverPhone;
}
