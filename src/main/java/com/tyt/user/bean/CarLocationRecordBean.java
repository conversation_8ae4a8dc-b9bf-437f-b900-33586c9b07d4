package com.tyt.user.bean;

import java.util.Date;

public class CarLocationRecordBean {
	private Long carId;//车辆ID
	private String detailAddr;//详细位置
	private String position; //方位
	private String speed; //速度
	private Date recordTime; //记录时间
	private Date currentTime; //当前时间
	public Long getCarId() {
		return carId;
	}
	public void setCarId(Long carId) {
		this.carId = carId;
	}
	public String getDetailAddr() {
		return detailAddr;
	}
	public void setDetailAddr(String detailAddr) {
		this.detailAddr = detailAddr;
	}
	public String getPosition() {
		return position;
	}
	public void setPosition(String position) {
		this.position = position;
	}
	public String getSpeed() {
		return speed;
	}
	public void setSpeed(String speed) {
		this.speed = speed;
	}
	public Date getRecordTime() {
		return recordTime;
	}
	public void setRecordTime(Date recordTime) {
		this.recordTime = recordTime;
	}
	public String getDirvePositionName() {
		String positionName = "";
		try {
			/*
			 * 字符串类型（ 0 或 360：正北，大于 0 且小于 90：东北，等于 90：正东，大于 90 且小 于 180：东南，等于
			 * 180：正南，大于 180 且小于 270：西南，等于 270：正西，大 于 270 且小于等于 359：西北，其他：未知）
			 */
			int positions = Integer.valueOf(position);
			if (positions == 0 || positions == 360) {
				positionName = "正北";
			} else if (positions > 0 && positions < 90) {
				positionName = "东北";
			} else if (positions == 90) {
				positionName = "正东";
			} else if (positions > 90 && positions < 180) {
				positionName = "东南";
			} else if (positions == 180) {
				positionName = "正南";
			} else if (positions > 180 && positions < 270) {
				positionName = "西南";
			} else if (positions == 270) {
				positionName = "正西";
			} else if (positions > 270 && positions < 360) {
				positionName = "西北";
			} else {
				positionName = "未知";
			}
		} catch (NumberFormatException e) {
			positionName = "未知";
		}
		return positionName;
	}
	public Date getCurrentTime() {
		return currentTime;
	}
	public void setCurrentTime(Date currentTime) {
		this.currentTime = currentTime;
	}
	
	
}
