package com.tyt.user.bean;

import com.alibaba.fastjson.JSON;

/**
 * 用户收藏的信息的数量的实体
 * 
 * <AUTHOR>
 * @date 2016-7-7下午12:02:35
 * @description
 */
public class UserCollectCountBean {
	/* 货物收藏的数量 */
	private Integer goodsCollecetCount;
	/* 板车求职收藏的数量 */
	private Integer bcarJobCollecetCount;
	/* 设备求职收藏的数量 */
	private Integer scarJobCollecetCount;
	/* 板车招聘收藏的数量 */
	private Integer bcarRecruitCollecetCount;
	/* 设备收藏的数量 */
	private Integer scarRecruitCollecetCount;

	public Integer getGoodsCollecetCount() {
		return goodsCollecetCount;
	}

	public void setGoodsCollecetCount(Integer goodsCollecetCount) {
		this.goodsCollecetCount = goodsCollecetCount;
	}

	public Integer getBcarJobCollecetCount() {
		return bcarJobCollecetCount;
	}

	public void setBcarJobCollecetCount(Integer bcarJobCollecetCount) {
		this.bcarJobCollecetCount = bcarJobCollecetCount;
	}

	public Integer getScarJobCollecetCount() {
		return scarJobCollecetCount;
	}

	public void setScarJobCollecetCount(Integer scarJobCollecetCount) {
		this.scarJobCollecetCount = scarJobCollecetCount;
	}

	public Integer getBcarRecruitCollecetCount() {
		return bcarRecruitCollecetCount;
	}

	public void setBcarRecruitCollecetCount(Integer bcarRecruitCollecetCount) {
		this.bcarRecruitCollecetCount = bcarRecruitCollecetCount;
	}

	public Integer getScarRecruitCollecetCount() {
		return scarRecruitCollecetCount;
	}

	public void setScarRecruitCollecetCount(Integer scarRecruitCollecetCount) {
		this.scarRecruitCollecetCount = scarRecruitCollecetCount;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
