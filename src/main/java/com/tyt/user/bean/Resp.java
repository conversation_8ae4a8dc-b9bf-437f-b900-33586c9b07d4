package com.tyt.user.bean;

import java.io.Serializable;

/**
 * 统一响应结果封装，方便api doc
 *
 * @param <T>
 * <AUTHOR>
 */
public class Resp<T> implements Serializable {

    /**
     * The Code.
     */
    private Integer code;
    /**
     * The Message.
     */
    private String msg;
    /**
     * The Data.
     */
    private T data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
