package com.tyt.user.bean;

import java.io.Serializable;
import java.util.Date;

/**
 * 2019-10-23 xyy
 */
public class CarSaveBean implements Serializable {
    private static final long serialVersionUID = -6504466062362814050L;
    private Long carId;
    private Long userId;
    private String headCity;
    private String headNo;
    private String tailCity;
    private String tailNo;
    private Integer carType;
    private Integer isPureFlat;

    /**
     * 认证状态0:认证中；1:认证成功；2：认证失败；3：系统删除；4：待认证
     */
    private String auth;
    /**
     *  是否抽拉 1 是 2 否
     */
    private Integer isJointPull;

    /**
     *  抽拉长度
     */
    private String jointPullLength;

    /**
     *  工作面长
     */
    private String loadSurfaceLength;

    /**
     *  工作面高
     */
    private String loadSurfaceHeight;
    /**
     * 是否调度车0不是1是
     */
    private String isDispatch;

    /**
     *  是否爬梯 0 不带 1 带
     */
    private Integer hasLadder;

    /**
     *  牵引马力
     */
    private Integer horsePower;

    /**
     * 当挂车样式为其他时必填
     */
    private String otherPureFlat;

    /**
     * 当挂车类型为其他时必填
     */
    private String otherCarType;
    /**
     * 最大载重
     */
    private String maxPayload;
    /**
     * 客户端标识
     */
    private String clientSign;
    /**
     * 挂车所有人地址
     */
    private String tailAddress;
    /**
     * 临牌到期时间
     */
    private Date tempLicenceExpires;
    /**
     * 车头类型 1.高顶 2.低顶
     */
    private Integer carHeadType;
    /**
     * 道路运输证类型
     */
    private String roadTransportType;
    /**
     * 道路运输证审核结果 0审核中1审核成功2审核失败
     */
    private String roadCardStatus;
    /**
     * 道路运输经营许可证审核结果 0审核中1审核成功2审核失败
     */
    private String roadLicenseStatus;
    /**
     * 车头审核状态
     */
    String headAuthStatus;
    /**
     * 车头审核失败原因
     */
    String headFailReason;
    /**
     * 车挂审核状态
     */
    String tailAuthStatus;
    /**
     * 车挂审核失败原因
     */
    String tailFailReason;

    /**
     * 车辆认证来源
     */
    private String authPath;
    /**
     *  挂车长度
     */
    private String tailLength;
    /**
     *  挂车宽度
     */
    private String tailWidth;
    /**
     * 挂车高度
     */
    private String tailHeight;
    /**
     * 挂车车牌号是否为临牌 1=是 0=否
     */
    private String isTailTemporary;
    /**
     * 车头行驶证有效期
     */
    private Long headDrivingExpiredTime;
    /**
     * 挂车行驶证有效期
     */
    private Long tailDrivingExpiredTime;

    /**
     * 是否满足开票 1满足 2不满足 3证件缺失  4证件待审
     */
    private Integer isInvoice;

    @Override
    public String toString() {
        return "CarSaveBean{" +
                "carId=" + carId +
                ", userId=" + userId +
                ", headCity='" + headCity + '\'' +
                ", headNo='" + headNo + '\'' +
                ", tailCity='" + tailCity + '\'' +
                ", tailNo='" + tailNo + '\'' +
                ", carType=" + carType +
                ", isPureFlat=" + isPureFlat +
                ", auth='" + auth + '\'' +
                ", isJointPull=" + isJointPull +
                ", jointPullLength='" + jointPullLength + '\'' +
                ", loadSurfaceLength='" + loadSurfaceLength + '\'' +
                ", loadSurfaceHeight='" + loadSurfaceHeight + '\'' +
                ", isDispatch='" + isDispatch + '\'' +
                ", hasLadder=" + hasLadder +
                ", horsePower=" + horsePower +
                ", otherPureFlat='" + otherPureFlat + '\'' +
                ", otherCarType='" + otherCarType + '\'' +
                ", maxPayload='" + maxPayload + '\'' +
                ", clientSign='" + clientSign + '\'' +
                ", tailAddress='" + tailAddress + '\'' +
                ", tempLicenceExpires=" + tempLicenceExpires +
                ", carHeadType=" + carHeadType +
                ", roadTransportType='" + roadTransportType + '\'' +
                '}';
    }

    public String getRoadTransportType() {
        return roadTransportType;
    }

    public void setRoadTransportType(String roadTransportType) {
        this.roadTransportType = roadTransportType;
    }

    public String getTailAddress() {
        return tailAddress;
    }

    public void setTailAddress(String tailAddress) {
        this.tailAddress = tailAddress;
    }

    public String getClientSign() {
        return clientSign;
    }

    public void setClientSign(String clientSign) {
        this.clientSign = clientSign;
    }

    public String getMaxPayload() {
        return maxPayload;
    }

    public void setMaxPayload(String maxPayload) {
        this.maxPayload = maxPayload;
    }

    public Date getTempLicenceExpires() {
        return tempLicenceExpires;
    }

    public void setTempLicenceExpires(Date tempLicenceExpires) {
        this.tempLicenceExpires = tempLicenceExpires;
    }


    public Integer getCarHeadType() {
        return carHeadType;
    }

    public void setCarHeadType(Integer carHeadType) {
        this.carHeadType = carHeadType;
    }

    public Integer getIsJointPull() {
        return isJointPull;
    }

    public void setIsJointPull(Integer isJointPull) {
        this.isJointPull = isJointPull;
    }

    public String getJointPullLength() {
        return jointPullLength;
    }

    public void setJointPullLength(String jointPullLength) {
        this.jointPullLength = jointPullLength;
    }

    public String getLoadSurfaceLength() {
        return loadSurfaceLength;
    }

    public void setLoadSurfaceLength(String loadSurfaceLength) {
        this.loadSurfaceLength = loadSurfaceLength;
    }

    public String getLoadSurfaceHeight() {
        return loadSurfaceHeight;
    }

    public void setLoadSurfaceHeight(String loadSurfaceHeight) {
        this.loadSurfaceHeight = loadSurfaceHeight;
    }

    public String getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(String isDispatch) {
        this.isDispatch = isDispatch;
    }

    public Integer getHasLadder() {
        return hasLadder;
    }

    public void setHasLadder(Integer hasLadder) {
        this.hasLadder = hasLadder;
    }

    public Integer getHorsePower() {
        return horsePower;
    }

    public void setHorsePower(Integer horsePower) {
        this.horsePower = horsePower;
    }

    public String getOtherPureFlat() {
        return otherPureFlat;
    }

    public void setOtherPureFlat(String otherPureFlat) {
        this.otherPureFlat = otherPureFlat;
    }

    public String getOtherCarType() {
        return otherCarType;
    }

    public void setOtherCarType(String otherCarType) {
        this.otherCarType = otherCarType;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getHeadCity() {
        return headCity;
    }

    public void setHeadCity(String headCity) {
        this.headCity = headCity;
    }

    public String getHeadNo() {
        return headNo;
    }

    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }

    public String getTailCity() {
        return tailCity;
    }

    public void setTailCity(String tailCity) {
        this.tailCity = tailCity;
    }

    public String getTailNo() {
        return tailNo;
    }

    public void setTailNo(String tailNo) {
        this.tailNo = tailNo;
    }

    public Integer getCarType() {
        return carType;
    }

    public void setCarType(Integer carType) {
        this.carType = carType;
    }

    public Integer getIsPureFlat() {
        return isPureFlat;
    }

    public void setIsPureFlat(Integer isPureFlat) {
        this.isPureFlat = isPureFlat;
    }

    public String getAuth() {
        return auth;
    }

    public void setAuth(String auth) {
        this.auth = auth;
    }

    public String getRoadCardStatus() {
        return roadCardStatus;
    }

    public void setRoadCardStatus(String roadCardStatus) {
        this.roadCardStatus = roadCardStatus;
    }

    public String getRoadLicenseStatus() {
        return roadLicenseStatus;
    }

    public void setRoadLicenseStatus(String roadLicenseStatus) {
        this.roadLicenseStatus = roadLicenseStatus;
    }

    public String getHeadAuthStatus() {
        return headAuthStatus;
    }

    public void setHeadAuthStatus(String headAuthStatus) {
        this.headAuthStatus = headAuthStatus;
    }

    public String getHeadFailReason() {
        return headFailReason;
    }

    public void setHeadFailReason(String headFailReason) {
        this.headFailReason = headFailReason;
    }

    public String getTailAuthStatus() {
        return tailAuthStatus;
    }

    public void setTailAuthStatus(String tailAuthStatus) {
        this.tailAuthStatus = tailAuthStatus;
    }

    public String getTailFailReason() {
        return tailFailReason;
    }

    public void setTailFailReason(String tailFailReason) {
        this.tailFailReason = tailFailReason;
    }

    public String getAuthPath() {
        return authPath;
    }

    public void setAuthPath(String authPath) {
        this.authPath = authPath;
    }

    public String getTailLength() {
        return tailLength;
    }

    public void setTailLength(String tailLength) {
        this.tailLength = tailLength;
    }

    public String getTailWidth() {
        return tailWidth;
    }

    public void setTailWidth(String tailWidth) {
        this.tailWidth = tailWidth;
    }

    public String getTailHeight() {
        return tailHeight;
    }

    public void setTailHeight(String tailHeight) {
        this.tailHeight = tailHeight;
    }

    public String getIsTailTemporary() {
        return isTailTemporary;
    }

    public void setIsTailTemporary(String isTailTemporary) {
        this.isTailTemporary = isTailTemporary;
    }

    public Long getHeadDrivingExpiredTime() {
        return headDrivingExpiredTime;
    }

    public void setHeadDrivingExpiredTime(Long headDrivingExpiredTime) {
        this.headDrivingExpiredTime = headDrivingExpiredTime;
    }

    public Long getTailDrivingExpiredTime() {
        return tailDrivingExpiredTime;
    }

    public void setTailDrivingExpiredTime(Long tailDrivingExpiredTime) {
        this.tailDrivingExpiredTime = tailDrivingExpiredTime;
    }

    public Integer getIsInvoice() {
        return isInvoice;
    }

    public void setIsInvoice(Integer isInvoice) {
        this.isInvoice = isInvoice;
    }
}
