package com.tyt.user.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SuperiorCarSignStatusResBean implements Serializable {

	//0-签约（资料未审核）;1-正常；2-黑名单；
	private Integer status;
	//是否需要实名(0-否；1-是；)
	private Integer isAuth;
	//是否需要车辆认(0-否；1-是；)
	private Integer isCarAuth;
	//是否需要付费会员(0-否；1-是；)
	private Integer isVip;
}
