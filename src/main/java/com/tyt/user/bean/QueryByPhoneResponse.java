package com.tyt.user.bean;

import com.alibaba.fastjson.JSON;

/**
 * 根据手机号查询是否为注册用户的响应实体
 * 
 * <AUTHOR>
 * @date 2018年8月22日下午8:38:16
 * @description
 */
public class QueryByPhoneResponse {
	// 是否为特运通注册用户 1 是 2 不是
	private String isRegister;
	// 认证车辆数
	private int authCarNum;
	// 用户id
	private Long userId;
	// 用户姓名
	private String userName;
    /**
     * 特运通用户头像
     */
    private String userAvatar;

	public String getUserAvatar() {
		return userAvatar;
	}

	public void setUserAvatar(String userAvatar) {
		this.userAvatar = userAvatar;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getIsRegister() {
		return isRegister;
	}

	public void setIsRegister(String isRegister) {
		this.isRegister = isRegister;
	}

	public int getAuthCarNum() {
		return authCarNum;
	}

	public void setAuthCarNum(int authCarNum) {
		this.authCarNum = authCarNum;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
