package com.tyt.user.bean;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/4/15 11:15
 */
@Data
public class NewCarCurrentLocationBean {
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date newLocationTime;
    private String headNo;
    private String spd;
    private String locationLongitude;
    private String headCity;
    private String locationLatitude;
    private String newLocation;

    /**
     * Represents the offline state of a car's current location.
     */
    private String offlineState;

    /**
     * Represents the offline time of a car's current location.
     */
    private String offlineTime;

}
