package com.tyt.user.bean;

import com.tyt.infofee.bean.MqBaseMessageBean;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/7 13:08
 */
@Data
public class LoginRelateOperationBean extends MqBaseMessageBean {
    private Long userId;
    /**
     * app 或者pc
     */
    private String type;

    private Integer opType;
    private String opContent;
    private String ip;
    private Integer platId;
    private String version;
    private String cellPhone;
    private String ticket;
    private String pcSign;
    private Timestamp opTime;
    private Integer recordType;
    private Map<String, String> loginParams;
    private Map<String, Object> upgradeStrategyMap;
    /**
     * 是否是车方沉默用户 0不是 1是
     */
    private Integer needGiveCallPhonePermission;

    /**
     * 车方上次登录时间
     */
    private Date carLastLoginTime;

}
