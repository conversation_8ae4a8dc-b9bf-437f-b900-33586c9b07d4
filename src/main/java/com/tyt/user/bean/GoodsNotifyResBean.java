package com.tyt.user.bean;

import com.tyt.model.ResultMsgBean;

/**
 * 封装客户端请求是否需要提示货物成交提示弹框返回信息的而实体
 * 
 * <AUTHOR>
 * @date 2016-7-29上午9:02:49
 * @description
 */
@SuppressWarnings("serial")
public class GoodsNotifyResBean extends ResultMsgBean {
	/*
	 * 是否需要提示弹框，1：需要，2：不需要
	 */
	private Integer needNotify;

	public Integer getNeedNotify() {
		return needNotify;
	}

	public void setNeedNotify(Integer needNotify) {
		this.needNotify = needNotify;
	}
}
