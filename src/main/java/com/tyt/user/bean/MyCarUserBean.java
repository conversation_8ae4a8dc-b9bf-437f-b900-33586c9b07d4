package com.tyt.user.bean;


public class MyCarUserBean {

    /**
     * 姓
     */
    private String lastName;
    /**
     * 先生/女士
     */
    private String call;
    /**
     * 车辆数
     */
    private String number;
    /**
     * 好评度
     */
    private Integer rating;
    /**
     * 显示名称[企业/公司名称/用户昵称]
     */
    private String showName;
    /**
     * 是否调度车 1是 0不是
     */
    private Integer isDispatch;

    /**
     * 企业认证状态 0未认证1通过2认证中3认证失败
     */
    private Integer enterpriseAuthStatus;

    public Integer getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(Integer isDispatch) {
        this.isDispatch = isDispatch;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getCall() {
        return call;
    }

    public void setCall(String call) {
        this.call = call;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number + "辆";
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public Integer getEnterpriseAuthStatus() {
        return enterpriseAuthStatus;
    }

    public void setEnterpriseAuthStatus(Integer enterpriseAuthStatus) {
        this.enterpriseAuthStatus = enterpriseAuthStatus;
    }

    @Override
    public String toString() {
        return "MyCarUserBean{" +
                "lastName='" + lastName + '\'' +
                ", call='" + call + '\'' +
                ", number='" + number + '\'' +
                ", rating=" + rating +
                ", showName='" + showName + '\'' +
                ", enterpriseAuthStatus='" + enterpriseAuthStatus + '\'' +
                '}';
    }
}
