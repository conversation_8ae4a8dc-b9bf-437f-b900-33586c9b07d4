package com.tyt.user.bean;

import com.tyt.model.TytCarDriverArchives;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.TreeMap;

/**
 * 对专车数据和老库数据进行整合，用于我的车辆列表
 */
@Data
public class CarMergeBean implements Comparable{

    /**
     * 车辆ID
     */
    private Long id;
    /**
     * 对应plant项目的车辆id
     */
    private Long originalId;
    /**
     * 挂车id
     */
    private Long trailerId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 车头牌照
     * headCity + headNo
     */
    private String headCarNum;
    /**
     * 挂车牌照
     * tailCity + tailNo
     */
    private String tailCarNum;
    /**
     * 创建日期
     */
    private Date createTime;
    /**
     * 认证状态
     * 0：认证中
     * 1：认证成功
     * 2：认证失败
     * 3：系统删除
     * 4：待认证
     */
    private String auth;

    /**
     * 主司机姓名
     */
    private String driverName;
    /**
     * 主司机电话
     */
    private String driverPhone;
    /**
     * 副司机姓名
     */
    private String secondaryDriverName;
    /**
     * 副司机电话
     */
    private String secondaryDriverPhone;
    /**
     * 随车电话
     */
    private String followDriverPhone;
    /**
     * 默认调度员
     */
    private String defaultSchedulingName;
    /**
     * 调度员电话
     */
    private String defaultSchedulingPhone;
    /**
     * 好评度
     */
    private Integer comprehensiveEvaluation;
    /**
     * 车辆位置
     */
    private String vehiclePosition;
    /**
     * 车辆位置更新时间
     */
    private Date vehiclePositionTime;
    /**
     * 车速
     */
    private String currentSpeed;
    /**
     * 状态(1是空车，2是运输中)
     */
    private Integer state;
    /**
     * 电话列表
     */
    private TreeMap<String, String> phoneList;
    /**
     * 经度
     */
    private BigDecimal vehiclePositionLon;
    /**
     * 纬度
     */
    private BigDecimal vehiclePositionLat;
    /**
     * 是否专车
     */
    private boolean specialCar = false;
    /**
     * 是否认证
     */
    private boolean carAuth = false;
    /**
     * 是否是调度车辆默认是
     */
    private Integer isDispatch=0;
    /**
     * 是否是专车1：是  0否
     */
    private Integer isTailored;
    /**
     * 所属司机相关信息
     */
    private TytCarDriverArchives driverData;

    /**
     * 主司机用户ID
     */
    private Long driverUserId;

    /**
     * 副司机用户ID
     */
    private Long secondaryDriverUserId;

    /**
     * 车辆是否为待完善车辆
     */
    private Boolean carIsNeedImprovenData;

    /**
     * 当前车辆是否被允许获取位置信息
     *
     */
    private Boolean carIsCanGetLocation;

    /**
     * Represents the offline state of a car.
     *
     * @see CarMergeBean
     * @see CarMergeBean#offlineState
     */
    private String offlineState;


    /**
     * The offline time of the car.
     */
    private String offlineTime;
    /**
     * 是否车主认证
     */
    private boolean carOwnerAuth = false;

    @Getter
    @Setter
    private Integer isInvoice;

    @Override
    public int compareTo(Object o) {
        CarMergeBean that = (CarMergeBean) o;
        return that.createTime.compareTo(this.createTime);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CarMergeBean that = (CarMergeBean) o;
        return Objects.equals(headCarNum, that.headCarNum) &&
                Objects.equals(tailCarNum, that.tailCarNum);
    }

    @Override
    public int hashCode() {
        return Objects.hash(headCarNum, tailCarNum);
    }

}
