package com.tyt.user.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 接收专车远程对象
 */
@Data
public class SpecialCar {

    /**
     * 车id
     */
    private Long carId;
    /**
     * 对应plant项目的车辆id
     */
    private Long originalId;
    /**
     * 挂车id
     */
    private Long trailerId;
    /**
     * 好评度
     */
    private Integer comprehensiveEvaluation;
    /**
     * 车头牌号
     */
    private String carNum;
    /**
     * 挂车牌号
     */
    private String licensePlateNum;
    /**
     * 车样式
     */
    private String carStyle;
    /**
     * 主司机姓名
     */
    private String mainDriverName;
    /**
     * 主司机电话
     */
    private String mainDriverPhone;
    /**
     * 主司机身份证号
     */
    private String mainDriverIdcard;
    /**
     * 主司机地址
     */
    private String mainDriverAddress;
    /**
     * 车辆位置
     */
    private String vehiclePosition;
    /**
     * 车辆位置更新时间
     */
    private Date vehiclePositionTime;
    /**
     * 车速
     */
    private String currentSpeed;
    /**
     * 默认调度员
     */
    private String defaultSchedulingName;
    /**
     * 调度员电话
     */
    private String defaultSchedulingPhone;
    /**
     * 调度员身份证号
     */
    private String defaultSchedulingIdcard;
    /**
     * 副司机姓名
     */
    private String viceDriverName;
    /**
     * 随车电话
     */
    private String accessoryPhone;
    /**
     * 所有人
     */
    private String carOwner;
    /**
     * 所有人电话
     */
    private String ownerPhone;
    /**
     * 真实所有人地址
     */
    private String ownerAddress;
    /**
     * 副司机电话
     */
    private String viceDriverPhone;
    /**
     * 经度
     */
    private BigDecimal vehiclePositionLon;
    /**
     * 纬度
     */
    private BigDecimal vehiclePositionLat;
    /**
     * 状态(1是空车，2是运输中)
     */
    private Integer state;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 是否是专车1是其他不是
     */
    private String carOwnership;
    /**
     * 是否是调度车辆默认是1是0不是
     */
    private Integer isDispatch;
    /**
     * 是否是专车1：是  0否
     */
    private Integer isTailored;
}
