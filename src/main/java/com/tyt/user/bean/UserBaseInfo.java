package com.tyt.user.bean;

import com.tyt.tsinsurance.bean.ImportWaybillBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class UserBaseInfo {
    private Long userId;
    /**
     * true：已注册 false：未注册
     */
    private Boolean checkResult;
    /**
     * 姓名
     */
    private String trueName;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 手机号
     */
    private String cellPhone;
    /**
     * 该用户近三天的运单信息
     */
    private List<ImportWaybillBean> waybills;
}
