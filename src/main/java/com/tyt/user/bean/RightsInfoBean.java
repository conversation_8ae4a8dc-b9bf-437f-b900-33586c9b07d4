package com.tyt.user.bean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;

public class RightsInfoBean {
	private String type1;
	private String type2;
	@JsonProperty(value = "content")
	private String promptContent;

	public String getType1() {
		return type1;
	}

	public void setType1(String type1) {
		this.type1 = type1;
	}

	public String getType2() {
		return type2;
	}

	public void setType2(String type2) {
		this.type2 = type2;
	}

	public String getPromptContent() {
		return promptContent;
	}

	public void setPromptContent(String promptContent) {
		this.promptContent = promptContent;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}
