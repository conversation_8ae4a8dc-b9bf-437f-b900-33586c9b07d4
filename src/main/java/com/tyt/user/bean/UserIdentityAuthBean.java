package com.tyt.user.bean;

import java.util.Date;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserIdentityAuthBean implements java.io.Serializable {







	/**
	 *
	 */
	private static final long serialVersionUID = 893551609418164800L;
	private Long id;
	private Long userId;
	private String mobile;
	private Integer identityStatus;
	private Integer userClass;
	private Integer identityType;
	private String trueName;
	private String sex;
	private String idCard;
	private String nation;
	private String address;
	private Integer infoStatus;
	private String infoFailureReason;
	private String mainUrl;
	private Integer mainStatus;
	private String mainFailureReason;
	private String backUrl;
	private Integer backStatus;
	private String backFailueReason;
	private String qualificationsUrl;
	private String enterpriseName;
	private Integer enterpriseType;
	private Integer enterpriseAmount;
	private String enterprisePhone;
	private String licenseUrl;
	private Integer licenseStatus;
	private String licenseFailureReason;
	private String workType;
	private Date ctime;
	private Integer dataType=0;

	private String workTypeNew;

	private String iPhotoUrl;
	private Integer iPhotoStatus=0;
	private String iPhotoFailureReason;
	private Date currentTime;
	private Integer enterpriseAuthStatus;
	private String enterpriseAuthFailureReason;

	private String userAuthPath;
	private String enterpriseAuthPath;

	/**
	 * 有效期.
	 */
	@Getter
	@Setter
	private Date idCardValidDate;

	/**
	 * 是否长期.
	 */
	@Getter
	@Setter
	private Integer idCardLongTerm;

	/**
	 * 过期类型.
	 */
	@Getter
	@Setter
	private Integer validDateFlag;

	public Long getId() {
		return id;
	}
	public Long getUserId() {
		return userId;
	}
	public String getMobile() {
		return mobile;
	}
	public Integer getIdentityStatus() {
		return identityStatus;
	}
	public Integer getUserClass() {
		return userClass;
	}
	public Integer getIdentityType() {
		return identityType;
	}
	public String getTrueName() {
		return trueName;
	}
	public String getSex() {
		return sex;
	}
	public String getIdCard() {
		return idCard;
	}
	public Integer getInfoStatus() {
		return infoStatus;
	}
	public String getInfoFailureReason() {
		return infoFailureReason;
	}
	public String getMainUrl() {
		return mainUrl;
	}
	public Integer getMainStatus() {
		return mainStatus;
	}
	public String getMainFailureReason() {
		return mainFailureReason;
	}
	public String getBackUrl() {
		return backUrl;
	}
	public Integer getBackStatus() {
		return backStatus;
	}
	public String getBackFailueReason() {
		return backFailueReason;
	}
	public String getQualificationsUrl() {
		return qualificationsUrl;
	}
	public String getEnterpriseName() {
		return enterpriseName;
	}
	public Integer getEnterpriseType() {
		return enterpriseType;
	}
	public Integer getEnterpriseAmount() {
		return enterpriseAmount;
	}
	public String getEnterprisePhone() {
		return enterprisePhone;
	}
	public String getLicenseUrl() {
		return licenseUrl;
	}
	public Integer getLicenseStatus() {
		return licenseStatus;
	}
	public String getLicenseFailureReason() {
		return licenseFailureReason;
	}
	public String getWorkType() {
		return workType;
	}
	public Date getCtime() {
		return ctime;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public void setIdentityStatus(Integer identityStatus) {
		this.identityStatus = identityStatus;
	}
	public void setUserClass(Integer userClass) {
		this.userClass = userClass;
	}
	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}
	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	public void setInfoStatus(Integer infoStatus) {
		this.infoStatus = infoStatus;
	}
	public void setInfoFailureReason(String infoFailureReason) {
		this.infoFailureReason = infoFailureReason;
	}
	public void setMainUrl(String mainUrl) {
		this.mainUrl = mainUrl;
	}
	public void setMainStatus(Integer mainStatus) {
		this.mainStatus = mainStatus;
	}
	public void setMainFailureReason(String mainFailureReason) {
		this.mainFailureReason = mainFailureReason;
	}
	public void setBackUrl(String backUrl) {
		this.backUrl = backUrl;
	}
	public void setBackStatus(Integer backStatus) {
		this.backStatus = backStatus;
	}
	public void setBackFailueReason(String backFailueReason) {
		this.backFailueReason = backFailueReason;
	}
	public void setQualificationsUrl(String qualificationsUrl) {
		this.qualificationsUrl = qualificationsUrl;
	}
	public void setEnterpriseName(String enterpriseName) {
		this.enterpriseName = enterpriseName;
	}
	public void setEnterpriseType(Integer enterpriseType) {
		this.enterpriseType = enterpriseType;
	}
	public void setEnterpriseAmount(Integer enterpriseAmount) {
		this.enterpriseAmount = enterpriseAmount;
	}
	public void setEnterprisePhone(String enterprisePhone) {
		this.enterprisePhone = enterprisePhone;
	}
	public void setLicenseUrl(String licenseUrl) {
		this.licenseUrl = licenseUrl;
	}
	public void setLicenseStatus(Integer licenseStatus) {
		this.licenseStatus = licenseStatus;
	}
	public void setLicenseFailureReason(String licenseFailureReason) {
		this.licenseFailureReason = licenseFailureReason;
	}
	public void setWorkType(String workType) {
		this.workType = workType;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	public String getNation() {
		return nation;
	}
	public String getAddress() {
		return address;
	}
	public Integer getDataType() {
		return dataType;
	}
	public String getiPhotoUrl() {
		return iPhotoUrl;
	}
	public Integer getiPhotoStatus() {
		return iPhotoStatus;
	}
	public String getiPhotoFailureReason() {
		return iPhotoFailureReason;
	}
	public void setNation(String nation) {
		this.nation = nation;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}
	public void setiPhotoUrl(String iPhotoUrl) {
		this.iPhotoUrl = iPhotoUrl;
	}
	public void setiPhotoStatus(Integer iPhotoStatus) {
		this.iPhotoStatus = iPhotoStatus;
	}
	public void setiPhotoFailureReason(String iPhotoFailureReason) {
		this.iPhotoFailureReason = iPhotoFailureReason;
	}
	public Date getCurrentTime() {
		return currentTime;
	}
	public void setCurrentTime(Date currentTime) {
		this.currentTime = currentTime;
	}
	public String getWorkTypeNew() {
		return workTypeNew;
	}
	public void setWorkTypeNew(String workTypeNew) {
		this.workTypeNew = workTypeNew;
	}

	public Integer getEnterpriseAuthStatus() {
		return enterpriseAuthStatus;
	}

	public void setEnterpriseAuthStatus(Integer enterpriseAuthStatus) {
		this.enterpriseAuthStatus = enterpriseAuthStatus;
	}

	public String getEnterpriseAuthFailureReason() {
		return enterpriseAuthFailureReason;
	}

	public void setEnterpriseAuthFailureReason(String enterpriseAuthFailureReason) {
		this.enterpriseAuthFailureReason = enterpriseAuthFailureReason;
	}

	public String getUserAuthPath() {
		return userAuthPath;
	}

	public void setUserAuthPath(String userAuthPath) {
		this.userAuthPath = userAuthPath;
	}

	public String getEnterpriseAuthPath() {
		return enterpriseAuthPath;
	}

	public void setEnterpriseAuthPath(String enterpriseAuthPath) {
		this.enterpriseAuthPath = enterpriseAuthPath;
	}
}
