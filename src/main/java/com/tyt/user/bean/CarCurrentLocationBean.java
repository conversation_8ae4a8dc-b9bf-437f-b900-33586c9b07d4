package com.tyt.user.bean;

import java.util.Date;
import org.apache.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 
 * <AUTHOR>
 * @date 2018年1月8日下午5:56:36
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarCurrentLocationBean {
	private Long id;
	/*
	 * 认证通过车辆的id
	 */
	private int carId;
	/*
	 * 是否使用北斗定位，1 使用 2待定 3 不使用
	 */
	private Byte useBeidou = 2;
	/*
	 * 北斗定位状态，1 正常 2 不正常 3 待定
	 */
	private Byte beidouStatus = 3;
	/*
	 * 最新定位时间
	 */
	private Date newLocationTime;
	/*
	 * 最新定位地址
	 */
	private String newLocation;
	/*
	 * 车辆当前位置经度
	 */
	private String locationLongitude;
	/*
	 * 车辆当前位置经度
	 */
	private String locationLatitude;
	/*
	 * 定位方式 1 北斗定位 2 待定
	 */
	private Byte locaitonType = 1;
	/*
	 * 车辆当前行驶速度
	 */
	private String speed;
	/*
	 * 字符串类型（ 0 或 360：正北，大于 0 且小于 90：东北，等于 90：正东，大于 90 且小 于 180：东南，等于 180：正南，大于
	 * 180 且小于 270：西南，等于 270：正西，大 于 270 且小于等于 359：西北，其他：未知）
	 */
	private String dirvePosition;
	private String carStatus = "0";
	/*
	 * 车主id
	 */
	private Integer carOwnerId;

	public CarCurrentLocationBean() {
	}

	public Integer getCarOwnerId() {
		return carOwnerId;
	}

	public void setCarOwnerId(Integer carOwnerId) {
		this.carOwnerId = carOwnerId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getCarId() {
		return carId;
	}

	public void setCarId(int carId) {
		this.carId = carId;
	}

	public Byte getUseBeidou() {
		return useBeidou;
	}

	public void setUseBeidou(Byte useBeidou) {
		this.useBeidou = useBeidou;
	}

	public Byte getBeidouStatus() {
		return beidouStatus;
	}

	public void setBeidouStatus(Byte beidouStatus) {
		this.beidouStatus = beidouStatus;
	}

	public String getNewLocationTime() {
		return newLocationTime == null ? null : newLocationTime.getTime() + "";
	}

	public void setNewLocationTime(Date newLocationTime) {
		this.newLocationTime = newLocationTime;
	}

	public String getNewLocation() {
		return newLocation;
	}

	public void setNewLocation(String newLocation) {
		this.newLocation = newLocation;
	}

	public String getLocationLongitude() {
		return StringUtils.isEmpty(locationLongitude) ? null : String.valueOf(locationLongitude);
	}

	public void setLocationLongitude(String locationLongitude) {
		this.locationLongitude = locationLongitude;
	}

	public String getLocationLatitude() {
		return StringUtils.isEmpty(locationLatitude) ? null : String.valueOf(locationLatitude);
	}

	public void setLocationLatitude(String locationLatitude) {
		this.locationLatitude = locationLatitude;
	}

	public Byte getLocaitonType() {
		return locaitonType;
	}

	public void setLocaitonType(Byte locaitonType) {
		this.locaitonType = locaitonType;
	}

	public String getSpeed() {
		return speed;
	}

	public void setSpeed(String speed) {
		this.speed = speed;
	}

	public String getDirvePosition() {
		return dirvePosition;
	}

	public String getDirvePositionName() {
		String positionName = "";
		try {
			/*
			 * 字符串类型（ 0 或 360：正北，大于 0 且小于 90：东北，等于 90：正东，大于 90 且小 于 180：东南，等于
			 * 180：正南，大于 180 且小于 270：西南，等于 270：正西，大 于 270 且小于等于 359：西北，其他：未知）
			 */
			int position = Integer.valueOf(dirvePosition);
			if (position == 0 || position == 360) {
				positionName = "正北";
			} else if (position > 0 && position < 90) {
				positionName = "东北";
			} else if (position == 90) {
				positionName = "正东";
			} else if (position > 90 && position < 180) {
				positionName = "东南";
			} else if (position == 180) {
				positionName = "正南";
			} else if (position > 180 && position < 270) {
				positionName = "西南";
			} else if (position == 270) {
				positionName = "正西";
			} else if (position > 270 && position < 360) {
				positionName = "西北";
			} else {
				positionName = "未知";
			}
		} catch (NumberFormatException e) {
			positionName = "未知";
		}
		return positionName + "方向";
	}

	public void setDirvePosition(String dirvePosition) {
		this.dirvePosition = dirvePosition;
	}

	public String getCarStatus() {
		return carStatus;
	}

	public void setCarStatus(String carStatus) {
		this.carStatus = carStatus;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
