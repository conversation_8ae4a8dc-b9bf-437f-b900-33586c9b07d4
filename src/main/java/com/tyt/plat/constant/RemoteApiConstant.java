package com.tyt.plat.constant;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 10:54
 */
public class RemoteApiConstant {

    public final static String idc_host = "http://bidata.teyuntong.net";

    public final static String api_sign = "8d60c1fdf478100169faf26408504b4b";

    public static class IdcApi{

        /** 计算运费 **/
        public final static String price_uri = "/idc/index.php/apibi/PriceCompute/getPrice";

        /**
         * -货源推荐
         */
        public static final String GOODS_DETAIL_RECOMMEND_PATH = "/idc/index.php/apibi/goodsDetailRecommended/push";
        public static final String INSTANT_GRAB_PATH = "/api/goods-model/list";

        /**
         * 获取抽佣分数BI接口
         */
        public static final String COMMISSION_SCORE_PATH = "/api/goods-model-price/list";

    }

    /**
     * commonApi http api
     */
    public static class CommonApi {

        /** common api secret key  **/
        public final static String secret_key = "ecb3039f24189857271ebb4a1da4ab0b";

        /** 货物分词 **/
        public final static String split_content_word = "/common-api/wordSplit/splitContent";

        /** orc证件识别 营业执照接口 **/
        public final static String DOOCR_BY_LICENSE_URL = "/common-api/cardOcr/businessLicenseOcr";

    }

    /**
     * ManBangApi
     */
    public static class ManBangApi {

        /** 获取用户钱包账号信息 **/
        public final static String GET_USER_REAL_INFO = "/mbOpenAcct/getUserRealInfo.action";

    }

    /**
     * e签宝回调
     */
    public static class EsignCallBack {

        /** 签署人验证码 **/
        public final static String SIGN_VALID = "/plat/plat/enterprise/signValidCallBack.action";

    }
}
