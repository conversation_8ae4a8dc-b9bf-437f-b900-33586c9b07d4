package com.tyt.plat.constant;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * property.
 * @date 2021/12/29 17:03
 */
@Slf4j
public class PropertyKeyConstant {

    /**
     * 当前环境.
     */
    public static final String ACTIVE_PROFILE = "active_profile";

    /**
     * mq topic 后缀
     */
    public static final String TOPIC_SUFFIX = "topic_suffix";

    /**
     * rocket mq 配置.
     */
    public static class MqConfigKey {

        public static final String ACCESS_KEY = "ACCESS_KEY";
        public static final String SECRET_KEY = "SECRET_KEY";
        public static final String NAMESRV_ADDR = "NAMESRV_ADDR";

    }

    /**
     * oss 配置.
     */
    public static class OssConfigKey {

        public static final String ENDPOINT = "oss.endpoint";
        public static final String ACCESS_KEY = "oss.accessKeyId";
        public static final String ACCESS_SECRET = "oss.accessKeySecret";
        public static final String BUCKET_NAME = "oss.bucketName";
        public static final String DOMAIN = "oss.domain";

    }

    /**
     * oss 配置.
     */
    public static class EsignConfigKey {

        public static final String APP_ID = "esign.appId";
        public static final String SECRET = "esign.secret";

    }
}
