package com.tyt.plat.constant;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.service.common.redis.RedisUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/9/21 14:42
 */
public class RedisKeyConstant {
    //change lock
    public static final String exposure_change_lock = "tyt:data:lock:exposure_change_lock";
    public static final String intention_change_lock = "tyt:data:lock:intention_change_lock";

    public static class IncrKey {

        //曝光修改id
        public static final String exposure_change = "tyt:data:incr:exposure_change";
        public static final String intention_change = "tyt:data:incr:intention_change";

    }

    /**
     * 货源操作锁
     */
    public static final String transport_opt_lock = "tyt:plat:lock:transport_opt";

    /**
     * 后台货源锁定
     */
    public static final String backend_transport_lock = "tyt:plat:lock:backend_transport_opt";

    /**
     * plat cache 前缀
     */
    public static final String plat_cache_prefix = "tyt:plat:cache";

    /**
     * redis 拼接日期，用于日内计次
     * @param prefix prefix
     * @param suffix suffix
     * @return String
     */
    public static String joinDayKey(String prefix, String suffix){
        Date nowTime = new Date();
        String dayShort = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        String verifyKey = CommonUtil.joinRedisKey(prefix, dayShort, suffix);
        return verifyKey;
    }

    /**
     * transport 天最小id，用于沉底，可能不是准确的
     * @return
     */
    public static final String getBottomIdSubtract(){
        String minTsIdKey = "bottomIdSubtract";
        String todayDate = DateUtil.dateToString(new Date(), DateUtil.day_format_short);
        String resultKey = CommonUtil.joinRedisKey(plat_cache_prefix, todayDate, minTsIdKey);
        return resultKey;
    }

    /**
     * 货源加价时间间隔
     * @param srcMsgId
     * @return
     */
    public static String getAddPriceTimeInterval(Long srcMsgId){
        String key = "tyt:plat:cache:addPriceTimeInterval";
        String addKey = CommonUtil.joinRedisKey(key, srcMsgId + "");
        return addKey;
    }

    public static final String COURSE_PREVIEW_KEY = "tyt:course:preview:";

    /**
     * 获取用户二要素验证次数 key.
     * @param userId userId
     * @return String
     */
    public static String getUserRealVerifyKey(Long userId){
        Date nowTime = new Date();
        String dayShort = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        String key = "tyt:plat:cache:userRealVerify";
        String verifyKey = CommonUtil.joinRedisKey(key, dayShort, userId + "");
        return verifyKey;
    }

    /**
     * 获取人脸识别天总次数 key.
     * @param userId userId
     * @return String
     */
    public static String getUserFaceTotalKey(Long userId){
        Date nowTime = new Date();
        String dayShort = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        String key = "tyt:plat:cache:UserFace:Total";
        String verifyKey = CommonUtil.joinRedisKey(key, dayShort, userId + "");
        return verifyKey;
    }

    /**
     * 获取人脸识别天总次数 key.
     * @param userId userId
     * @return String
     */
    public static String getUserFaceIdCardKey(Long userId, String idCard){
        Date nowTime = new Date();
        String dayShort = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        String key = "tyt:plat:cache:UserFace:IdCard";
        String verifyKey = CommonUtil.joinRedisKey(key, dayShort, userId + "", idCard);
        return verifyKey;
    }

    /**
     * 合同天次数 key.
     * @param userId userId
     * @return String
     */
    public static String getDayContractKey(Long userId){
        Date nowTime = new Date();
        String dayShort = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        String key = "tyt:plat:cache:EnterpriseDayContract";
        String verifyKey = CommonUtil.joinRedisKey(key, dayShort, userId + "");
        return verifyKey;
    }

    /**
     * 获取合同编码计数
     * @return String
     */
    public static String getContractNumberKey(){

        Date nowTime = new Date();
        String dayShort = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        String key = "tyt:plat:cache:ContractNumber";
        String verifyKey = CommonUtil.joinRedisKey(key, dayShort);
        return verifyKey;

    }

    /**
     * 获取redis 计数
     * @param redisKey redisKey
     * @return int
     */
    public static int getCount(String redisKey){
        String dayCountText = RedisUtil.get(redisKey);

        int count = 0;
        if (StringUtils.isNotBlank(dayCountText)) {
            count = Integer.parseInt(dayCountText);
        }
        return count;
    }

    /**
     * 校验redis count
     * @param redisKey redisKey
     * @param maxCount maxCount
     * @return boolean
     */
    public static boolean countCheck(String redisKey, int maxCount){

        int dayCount = getCount(redisKey);

        boolean result = (maxCount > dayCount);

        return result;
    }

    /**
     * 自增并设置超时时间.
     * @param redisKey redisKey
     * @param second second
     * @return Long
     */
    public static Long incrAndExpire(String redisKey, int second){
        Long incr = RedisUtil.incr(redisKey);
        RedisUtil.expire(redisKey, second);
        return incr;
    }

    /**
     * 授权激活链接点击间隔
     * @param userId
     * @return
     */
    public static String getActiveAuthIntervalKey(Long userId){
        String key = "tyt:plat:cache:ActiveAuthInterval";
        String verifyKey = CommonUtil.joinRedisKey(key, userId + "");
        return verifyKey;
    }

    /**
     * 授权激活链接点击每日次数
     */
    public static final String ACTIVE_AUTH_DAY = "tyt:plat:cache:ActiveAuthDay";

    /**
     * 企业每日人脸识别次数key
     */
    public static final String ENTERPRISE_FACE_COUNT = "tyt:plat:cache:ENTERPRISE_FACE_COUNT";

}
