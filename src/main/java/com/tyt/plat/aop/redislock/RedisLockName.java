package com.tyt.plat.aop.redislock;

public enum RedisLockName {
    FEEDBACK("redisLock:feedback:id:"),
    /**
     * 评价申诉保存锁
     */
    FEEDBACK_APPEAL("redisLock:feedback:appeal:"),
    POST_FEEDBACK("redisLock:postFeedback:transportId:");


    private final String name;

    RedisLockName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
