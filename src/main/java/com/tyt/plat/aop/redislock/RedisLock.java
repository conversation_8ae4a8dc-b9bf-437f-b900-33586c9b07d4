package com.tyt.plat.aop.redislock;


import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface RedisLock {

    /**
     * 锁分类
     */
    RedisLockName name();

    /**
     * SpEL格式的key
     */
    String key()  ;

    /**
     * 等待锁时间的单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 等待锁的时间
     */
    long waitTime() default 5L;

    /**
     * 应用lock的SpEL表达式，默认为 ""，意味着总是应用abtest
     */
    String condition() default "";
}
