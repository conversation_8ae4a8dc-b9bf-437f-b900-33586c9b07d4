package com.tyt.plat.aop.redislock;

import com.alibaba.fastjson.JSON;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

@Component
@Aspect
@Slf4j
@EnableAspectJAutoProxy(proxyTargetClass = true)
@Order(value = Ordered.HIGHEST_PRECEDENCE + 10)
public class RedisLockAspect {

    @Pointcut("@annotation(com.tyt.plat.aop.redislock.RedisLock)")
    public void lockPoint() {
    }

    @Around("lockPoint()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        RedisLock redisLock = method.getAnnotation(RedisLock.class);
        String key = redisLock.key();
        RedisLockName name = redisLock.name();

        String condition = getBySpringElKey(args, method, redisLock.condition());

        boolean enable = StringUtils.isBlank(condition) || Boolean.parseBoolean(condition);

        // 条件不满足，放行
        if (!enable) {
            return joinPoint.proceed();
        }

        //拼装key
        String lockKey = name.getName() + getBySpringElKey(args, method, key);

        long waitTime = redisLock.waitTime();
        TimeUnit timeUnit = redisLock.timeUnit();

        RedissonClient redissonClient = RedissonUtils.getClient();

        RLock rLock = redissonClient.getLock(lockKey);
        try {
            //获取分布式锁
            boolean tryLock = rLock.tryLock(waitTime, timeUnit);
            if (!tryLock) {
                log.error("redis锁获取失败class:{},method:{},args:{}", joinPoint.getTarget().getClass().getName(),
                        method.getName(), JSON.toJSONString(args));
                throw TytException.createException(new ResponseCode(500, "系统繁忙"));
            }

            //执行方法
            return joinPoint.proceed();
        } finally {
            rLock.unlock();
        }
    }

    private String getBySpringElKey(Object[] args, Method method, String key) {
        if (StringUtils.isBlank(key)) {
            return "";
        }

        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(key);
        //设置解析上下文(有哪些占位荷,以及每种占位符的值-根据方法的参数名和参数值
        EvaluationContext context = new StandardEvaluationContext();

        String[] parameterNames = new DefaultParameterNameDiscoverer().getParameterNames(method);
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }
        return expression.getValue(context).toString();
    }
}
