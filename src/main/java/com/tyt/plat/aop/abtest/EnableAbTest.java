package com.tyt.plat.aop.abtest;

import java.lang.annotation.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface EnableAbTest {

    /**
     * abtest 对应的code
     */
    String code();

    /**
     * 获取userId 的 SpEL格式的key ，默认为 "" ,会取默认的type
     */
    String userId() default "";

    /**
     * 符合abtest开关的type
     */
    int[] types();

    /**
     * 应用abtest的SpEL表达式，默认为 ""，意味着总是应用abtest
     */
    String condition() default "";
}
