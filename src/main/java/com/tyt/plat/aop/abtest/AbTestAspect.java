package com.tyt.plat.aop.abtest;

import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@Aspect
@Slf4j
@EnableAspectJAutoProxy(proxyTargetClass = true)
@Order(value = Ordered.HIGHEST_PRECEDENCE + 9)
public class AbTestAspect {

    private final AbtestService abtestService;

    public AbTestAspect(AbtestService abtestService) {
        this.abtestService = abtestService;
    }

    @Pointcut("@annotation(com.tyt.plat.aop.abtest.EnableAbTest)")
    public void lockPoint() {
    }

    @Around("lockPoint()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        EnableAbTest enableAbTests = method.getAnnotation(EnableAbTest.class);

        String condition = getBySpringElKey(args, method, enableAbTests.condition());

        boolean enable = StringUtils.isBlank(condition) || Boolean.parseBoolean(condition);

        // 条件不满足，放行
        if (!enable) {
            return joinPoint.proceed();
        }

        boolean pass = true;

        try {
            //获取userId
            String userIdStr = getBySpringElKey(args, method, enableAbTests.userId());
            Long userId;

            if (StringUtils.isBlank(userIdStr)) {
                userId = null;
            } else {
                userId = Long.valueOf(userIdStr);
            }

            String code = enableAbTests.code();
            // ab测试开启才查code
            List<TytAbtestConfigVo> userTypeList =
                    abtestService.getUserTypeList(Collections.singletonList(code), userId);

            Integer userType = userTypeList.stream().filter(it -> Objects.equals(it.getCode(), code))
                    .findFirst().map(TytAbtestConfigVo::getType).orElse(null);

            int[] types = enableAbTests.types();

            pass = Arrays.stream(types)
                    .anyMatch(it -> userType != null && userType == it);
        } catch (Exception e) {
            log.error("abTest 信息获取异常", e);
            throw TytException.createException();
        }

        if (pass) {
            return joinPoint.proceed();
        } else {
            throw TytException.createException(new ResponseCode(500, "功能尚未开启"));
        }
    }

    private String getBySpringElKey(Object[] args, Method method, String key) {
        if (StringUtils.isBlank(key)) {
            return "";
        }

        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(key);
        //设置解析上下文(有哪些占位荷,以及每种占位符的值-根据方法的参数名和参数值
        EvaluationContext context = new StandardEvaluationContext();

        String[] parameterNames = new DefaultParameterNameDiscoverer().getParameterNames(method);
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }
        return expression.getValue(context).toString();
    }
}
