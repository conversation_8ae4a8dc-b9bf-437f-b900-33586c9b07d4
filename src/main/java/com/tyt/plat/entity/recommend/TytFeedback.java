package com.tyt.plat.entity.recommend;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_feedback")
public class TytFeedback {
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货物ID
     */
    @Column(name = "ts_id")
    private Long tsId;

    /**
     * 车辆ID
     */
    @Column(name = "car_id")
    private Long carId;

    /**
     * 发货人userId
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 反馈人userid
     */
    @Column(name = "fb_user_id")
    private Long fbUserId;

    /**
     * 反馈时间
     */
    private Date ctime;

    /**
     * 反馈状态0正常1是删除
     */
    private Short status;
}