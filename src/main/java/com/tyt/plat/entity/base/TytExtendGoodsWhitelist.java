package com.tyt.plat.entity.base;

import java.util.Date;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 扩展货源 白名单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_extend_goods_whitelist")
public class TytExtendGoodsWhitelist implements Serializable {

    private static final long serialVersionUID = 1L;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    private Long goodsStationId;


    private String goodsStationName;


    private Date ctime;


    private Date mtime;


    private String updateUserName;


    private Long updateUserId;


    private Integer type;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGoodsStationId() {
        return goodsStationId;
    }

    public void setGoodsStationId(Long goodsStationId) {
        this.goodsStationId = goodsStationId;
    }

    public String getGoodsStationName() {
        return goodsStationName;
    }

    public void setGoodsStationName(String goodsStationName) {
        this.goodsStationName = goodsStationName;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "TytExtendGoodsWhitelist{" +
        "id=" + id +
        ", goodsStationId=" + goodsStationId +
        ", goodsStationName=" + goodsStationName +
        ", ctime=" + ctime +
        ", mtime=" + mtime +
        ", updateUserName=" + updateUserName +
        ", updateUserId=" + updateUserId +
        ", type=" + type +
        "}";
    }
}
