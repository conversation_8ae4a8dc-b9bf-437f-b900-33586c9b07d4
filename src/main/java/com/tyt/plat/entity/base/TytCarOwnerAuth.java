package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_car_owner_auth")
public class TytCarOwnerAuth {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    /**
     * 车辆id
     */
    @Column(name = "car_id")
    private Long carId;

    /**
     * 车头牌照
     */
    @Column(name = "head_no")
    private String headNo;

    /**
     * APP 的 车牌所有人 对应 后台 车主姓名
     */
    @Column(name = "head_name")
    private String headName;

    /**
     * 车主手机号
     */
    @Column(name = "car_user_phone")
    private String carUserPhone;

    /**
     * 车主身份  0企业 1个人 对应后台 行驶证所有人类别
     */
    @Column(name = "car_identity")
    private Integer carIdentity;

    /**
     * 车主关系  0 非挂靠 1 挂靠
     */
    @Column(name = "car_relation")
    private Integer carRelation;

    /**
     * 车主证件号码 个人就是身份证号 企业就是企业纳税人识别码
     */
    @Column(name = "car_owner_id_number")
    private String carOwnerIdNumber;

    /**
     * 证件照片
     */
    @Column(name = "id_photo")
    private String idPhoto;

    /**
     * 认证材料类型 0 挂靠协议 1承诺书
     */
    @Column(name = "auth_materials_type")
    private Integer authMaterialsType;

    /**
     * 证件材料-承诺书 （最多四张）\ 挂靠协议 多张以逗号分割
     */
//    @Column(name = "certificate_material")
//    private String certificateMaterial;

    /**
     * 认证状态 0 认证中、1 认证成功、2 认证失败
     */
    @Column(name = "auth_status")
    private Integer authStatus;

    /**
     * 审核人id
     */
    @Column(name = "auth_user_id")
    private Integer authUserId;

    /**
     * 审核人名称
     */
    @Column(name = "auth_user_name")
    private String authUserName;

    /**
     * 审核时间
     */
    @Column(name = "auth_time")
    private Date authTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 0 删除 1未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;
}