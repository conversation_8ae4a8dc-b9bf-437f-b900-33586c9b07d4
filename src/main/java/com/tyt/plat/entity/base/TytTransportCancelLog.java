package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_cancel_log")
public class TytTransportCancelLog {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 撤销原因key
     */
    @Column(name = "backout_reason_key")
    private String backoutReasonKey;

    /**
     * 撤销原因value
     */
    @Column(name = "backout_reason_value")
    private Integer backoutReasonValue;

    /**
     * 撤销时间
     */
    @Column(name = "cancel_time")
    private Date cancelTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 货源信息json
     */
    @Column(name = "transport_json")
    private String transportJson;
}