package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_waybill_ex_repay")
public class TransportOrdersExRepay implements Serializable {

    public static final int REPAY_STATUS_WAIT = 0;

    public static final int REPAY_STATUS_DOING = 1;

    public static final int REPAY_STATUS_ALREADY = 2;

    public static final int DELETE_NO = 0;

    public static final int DELETE_YES = 1;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 运单号
     */
    @Column(name = "ts_order_no")
    private String tsOrderNo;

    /**
     * 运单ID
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 异常上报方：1-车主上报；2-货主上报；
     */
    @Column(name = "ex_party")
    private Integer exParty;

    /**
     * 货主用户ID
     */
    @Column(name = "goods_user_id")
    private Long goodsUserId;

    /**
     * 车主用户ID
     */
    @Column(name = "car_user_id")
    private Long carUserId;

    /**
     * 补偿金额（元）
     */
    @Column(name = "compensate_amount")
    private BigDecimal compensateAmount;

    /**
     * 补偿类型：1.订金补偿 2.放空补偿 3.压车补偿 4.距离加价补偿 5.吨位加价补偿 6.拉跑货补偿 7.倒卖货补偿 8.倒送货补偿 9.防溢出补偿 10.其他
     */
    @Column(name = "compensate_type")
    private Integer compensateType;

    /**
     * 补偿原因
     */
    @Column(name = "compensate_reason")
    private String compensateReason;

    /**
     * 补偿时间
     */
    @Column(name = "compensate_time")
    private Date compensateTime;

    /**
     * 追偿对象：1-车方；2-货方；
     */
    @Column(name = "repay_target")
    private Integer repayTarget;

    /**
     * 追偿对象用户ID
     */
    @Column(name = "repay_user_id")
    private Long repayUserId;

    /**
     * 追偿金额（元）
     */
    @Column(name = "repay_amount")
    private BigDecimal repayAmount;

    /**
     * 追偿状态：0-待追偿；1-追偿中；2-已还款；
     */
    @Column(name = "repay_status")
    private Integer repayStatus;

    /**
     * 追偿支付单号
     */
    @Column(name = "repay_trade_no")
    private Integer repayTradeNo;

    /**
     * 追偿支付状态：0-未支付；1-支付中；2-支付成功；3-支付失败；
     */
    @Column(name = "pay_status")
    private Integer payStatus;

    /**
     * 是否删除：0-否；1-是；
     */
    @Column(name = "delete")
    private Integer delete;

    /**
     * 到账时间
     */
    @Column(name = "pay_time")
    private Date payTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}