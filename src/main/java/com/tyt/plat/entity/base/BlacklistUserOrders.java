package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 黑名单用户订单拉黑表实体类
 * blacklist_user_orders
 *
 * <AUTHOR>
 * @date 2023-10-9 09:52:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlacklistUserOrders implements Serializable{

	/**
	 * id
	 */
	private Long id;
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 异常上报ID
	 */
	private Long waybillExId;
	/**
	 * 运单号
	 */
	private String tsOrderNo;
	/**
	 * 拉黑状态：（0-否；1-是；）
	 */
	private Integer status;
	/**
	 * 是否永久（0-否；1-是；）
	 */
	private Integer perpetual;
	/**
	 * 拉黑天数
	 */
	private Integer restrictNum;
	/**
	 * 拉黑开始时间
	 */
	private Date restrictStartTime;
	/**
	 * 拉黑结束时间
	 */
	private Date restrictEndTime;
	/**
	 * 找货限制开始时间
	 */
	private Date carLimitStartTime;
	/**
	 * 找货限制结束时间
	 */
	private Date carLimitEndTime;
	/**
	 * 找货限制天数（-1表示永久限制）
	 */
	private Integer carLimitNum;
	/**
	 * 发货限制开始时间
	 */
	private Date goodsLimitStartTime;
	/**
	 * 发货限制结束时间
	 */
	private Date goodsLimitEndTime;
	/**
	 * 发货限制天数（-1表示永久限制）
	 */
	private Integer goodsLimitNum;
	/**
	 * 发货限制原因
	 */
	private String goodsCause;
	/**
	 * 找货限制原因
	 */
	private String carCause;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 操作人ID
	 */
	private Long operaUserId;
	/**
	 * 操作人名称
	 */
	private String operaUserName;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date modifyTime;

}
