package com.tyt.plat.entity.base;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 好中差货模型因子
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@Table(name = "tyt_transport_good_model_factor")
public class TransportGoodModelFactorDO {

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 模型等级： 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2
     */
    @Column(name = "model_level")
    private Integer modelLevel;

    /**
     * 吨重区间（左闭右开）起始值
     */
    @Column(name = "weight_start")
    private Integer weightStart;

    /**
     * 吨重区间（左闭右开）终止值
     */
    @Column(name = "weight_end")
    private Integer weightEnd;

    /**
     * 距离（公里）起始值
     */
    @Column(name = "distance_start")
    private Integer distanceStart;

    /**
     * 距离（公里）起始值
     */
    @Column(name = "distance_end")
    private Integer distanceEnd;

    /**
     * 固定价下限
     */
    @Column(name = "price_lower")
    private BigDecimal priceLower;

    /**
     * 固定价上限
     */
    @Column(name = "price_upper")
    private BigDecimal priceUpper;

    /**
     * 吨公里价下限
     */
    @Column(name = "unit_price_lower")
    private BigDecimal unitPriceLower;

    /**
     * 吨公里价上限
     */
    @Column(name = "unit_price_upper")
    private BigDecimal unitPriceUpper;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}
