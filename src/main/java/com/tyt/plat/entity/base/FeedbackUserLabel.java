package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_user_label")
public class FeedbackUserLabel implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 标签id
     */
    @Column(name = "label_id")
    private Long labelId;

    /**
     * 标签名
     */
    @Column(name = "label_name")
    private String labelName;

    /**
     * 评价id
     */
    @Column(name = "feedback_id")
    private Long feedbackId;

    /**
     * 评价用户id
     */
    @Column(name = "post_user_id")
    private Long postUserId;

    /**
     * 评论用户类型 1车 2货
     */
    @Column(name = "post_user_type")
    private Integer postUserType;

    /**
     * 被评价用户id
     */
    @Column(name = "receive_user_id")
    private Long receiveUserId;

    /**
     * 被评论用户类型  1车 2货
     */
    @Column(name = "receive_user_type")
    private Integer receiveUserType;

    /**
     * 是否删除 0 未删除 1已删除
     */
    @Column(name = "del_flag")
    private Boolean delFlag;

    /**
     * 标签类别 1:好评，2:中评，3:差评
     */
    @Column(name = "label_feedback_type")
    private Integer labelFeedbackType;

    /**
     * 对应的运单id
     */
    @Column(name = "ts_order_id")
    private Long tsOrderId;

    /**
     * 对应的货源id
     */
    @Column(name = "ts_id")
    private Long tsId;

    /**
     * 是否对被评价人隐藏  0不隐藏，1隐藏
     */
    private Boolean receiverHideFlag;
}