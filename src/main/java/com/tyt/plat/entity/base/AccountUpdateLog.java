package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "account_update_log")
public class AccountUpdateLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户原手机号
     */
    @Column(name = "cell_phone")
    private String cellPhone;

    /**
     * 更换的新手机号
     */
    @Column(name = "new_phone")
    private String newPhone;

    /**
     * 更改原因
     */
    private String reason;

    /**
     * 更改时间
     */
    private Date ctime;

    /**
     * 操作员ID
     */
    @Column(name = "opt_id")
    private Long optId;

    /**
     * 操作员姓名
     */
    @Column(name = "opt_name")
    private String optName;

    /**
     * 变更类型 0:用户变更 1:客服变更
     */
    @Column(name = "update_type")
    private Integer updateType;

    /**
     * 证件url
     */
    @Column(name = "certificate_url")
    private String certificateUrl;

    /**
     * 变更手机号审核表id（如果为审核变更才有值）
     */
    @Column(name = "cell_phone_change_edit_id")
    private Long cellPhoneChangeEditId;
}