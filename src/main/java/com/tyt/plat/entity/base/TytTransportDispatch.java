package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Table(name = "tyt_transport_dispatch")
public class TytTransportDispatch {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 货主id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 发货人id
     */
    @Column(name = "publish_user_id")
    private Long publishUserId;

    /**
     * 发货人姓名
     */
    @Column(name = "publish_user_name")
    private String publishUserName;

    /**
     * 调度负责人id
     */
    @Column(name = "dispatcher_id")
    private Long dispatcherId;

    /**
     * 调度人姓名
     */
    @Column(name = "dispatcher_name")
    private String dispatcherName;

    /**
     * 货主出价
     */
    @Column(name = "owner_freight")
    private BigDecimal ownerFreight;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 给货货主手机号
     */
    @Column(name = "give_goods_phone")
    private String giveGoodsPhone;

    /**
     * 给货货主手名称
     */
    @Column(name = "give_goods_name")
    private String giveGoodsName;

    /**
     * 发布终端（1后台；2app；3PC）
     */
    @Column(name = "publish_platform")
    private Integer publishPlatform;

    /**
     * 信息费差值
     */
    @Column(name = "info_fee_diff")
    private BigDecimal infoFeeDiff;
}