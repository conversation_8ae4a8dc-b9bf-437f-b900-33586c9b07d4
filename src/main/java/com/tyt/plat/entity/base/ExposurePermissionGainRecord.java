package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "exposure_permission_gain_record")
public class ExposurePermissionGainRecord {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 获取类型(0.购买 1.新用户注册 2.沉默用户登录 3.流失用户登录 4.信用获取 5.履约获取 )
     */
    @Column(name = "gain_type")
    private Integer gainType;

    /**
     * 订单号
     */
    @Column(name = "ord_num")
    private String ordNum;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private Long goodsId;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 过期时间
     */
    @Column(name = "expired_time")
    private Date expiredTime;

    /**
     * 权益总次数
     */
    @Column(name = "total_num")
    private Integer totalNum;

    /**
     * 获取时间
     */
    private Date ctime;
}