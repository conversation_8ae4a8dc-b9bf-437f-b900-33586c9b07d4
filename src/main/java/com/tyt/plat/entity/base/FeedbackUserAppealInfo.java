package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_user_appeal_info")
public class FeedbackUserAppealInfo implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 申诉表主键ID
     */
    @Column(name = "appeal_id")
    private Long appealId;

    /**
     * 申诉原因
     */
    @Column(name = "appeal_reason")
    private String appealReason;

    /**
     * 申诉图片地址（多张用英文逗号隔开）
     */
    @Column(name = "appeal_picture")
    private String appealPicture;

    /**
     * 提交申诉时间
     */
    @Column(name = "appeal_time")
    private Date appealTime;
}