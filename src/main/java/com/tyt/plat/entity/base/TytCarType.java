package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_car_type")
public class TytCarType {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 车辆类型： 1.车辆一级类型 2.车辆二级类型
     */
    @Column(name = "car_classify")
    private Byte carClassify;

    /**
     * 车辆类型名称
     */
    @Column(name = "car_type")
    private String carType;

    @Column(name = "update_time")
    private Date updateTime;
}