package com.tyt.plat.entity.base;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "tyt_transport_tec_service_fee")
public class TytTransportTecServiceFee {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源主表ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 会员折前技术服务费
     */
    @Column(name = "member_before_fee")
    private BigDecimal memberBeforeFee;

    /**
     * 会员折后技术服务费
     */
    @Column(name = "member_after_fee")
    private BigDecimal memberAfterFee;

    /**
     * 非会员折后技术服务费
     */
    @Column(name = "no_member_after_fee")
    private BigDecimal noMemberAfterFee;

    /**
     * 非会员折前技术服务费
     */
    @Column(name = "no_member_before_fee")
    private BigDecimal noMemberBeforeFee;

    /**
     * 会员抽佣货源权益
     */
    @Column(name = "member_interests_word")
    private String memberInterestsWord;

    /**
     * 会员抽佣货源权益链接
     */
    @Column(name = "member_interests_url")
    private String memberInterestsUrl;

    /**
     * 非会员抽佣货源权益
     */
    @Column(name = "no_member_interests_word")
    private String noMemberInterestsWord;

    /**
     * 非会员抽佣货源权益链接
     */
    @Column(name = "no_member_interests_url")
    private String noMemberInterestsUrl;

    /**
     * 车方会员联系该货源是否开启虚拟号
     */
    @Column(name = "member_show_privacy_phone_tab")
    private Integer memberShowPrivacyPhoneTab;

    /**
     * 非车方会员联系该货源是否开启虚拟号
     */
    @Column(name = "no_member_show_privacy_phone_tab")
    private Integer noMemberShowPrivacyPhoneTab;

    /**
     * 车方会员超时免佣 0:关闭；1:开启
     */
    @Column(name = "member_free_type")
    private Integer memberFreeTecServiceFeeType;

    /**
     * 车方会员超时免佣满足X人查看
     */
    @Column(name = "member_free_view_count")
    private Integer memberFreeViewCount;

    /**
     * 车方会员超时免佣满足X人拨打
     */
    @Column(name = "member_free_call_count")
    private Integer memberFreeCallCount;

    /**
     * 车方会员超时免佣准备状态 0:未准备就绪；1:准备就绪
     */
    @Column(name = "member_free_ready_type")
    private Integer memberFreeReadyType;

    /**
     * 车方会员超时免佣准备就绪时间
     */
    @Column(name = "member_free_ready_time")
    private Date memberFreeReadyTime;

    /**
     * 车方会员X分钟后免佣
     */
    @Column(name = "member_free_time")
    private Integer memberFreeTecServiceFeeTime;

    /**
     * 非车方会员超时免佣 0:关闭；1:开启
     */
    @Column(name = "no_member_free_type")
    private Integer noMemberFreeTecServiceFeeType;

    /**
     * 非车方会员超时免佣满足X人查看
     */
    @Column(name = "no_member_free_view_count")
    private Integer noMemberFreeViewCount;

    /**
     * 非车方会员超时免佣满足X人拨打
     */
    @Column(name = "no_member_free_call_count")
    private Integer noMemberFreeCallCount;

    /**
     * 非车方会员超时免佣准备状态 0:未准备就绪；1:准备就绪
     */
    @Column(name = "no_member_free_ready_type")
    private Integer noMemberFreeReadyType;

    /**
     * 非车方会员超时免佣准备就绪时间
     */
    @Column(name = "no_member_free_ready_time")
    private Date noMemberFreeReadyTime;

    /**
     * 非车方会员X分钟后免佣
     */
    @Column(name = "no_member_free_time")
    private Integer noMemberFreeTecServiceFeeTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 会员技术服务费计算使用的分段类型 1：运费分段；2：抽佣分数分段
     */
    @Column(name = "member_use_commission_stage_type")
    private Integer memberUseCommissionStageType;

    /**
     * 非会员技术服务费计算使用的分段类型 1：运费分段；2：抽佣分数分段
     */
    @Column(name = "no_member_use_commission_stage_type")
    private Integer noMemberUseCommissionStageType;

    /**
     * 抽佣分数
     */
    @Column(name = "commission_score")
    private BigDecimal commissionScore;

    /**
     * 无价货源优车指导价
     */
    @Column(name = "good_car_price_transport_carry_price")
    private String goodCarPriceTransportCarryPrice;

    /**
     * 适用货源（单选）1:专车；2:普货；3:优车1.0；4:优车2.0；5:运满满
     */
    @Column(name = "apply_transport_type")
    private Integer applyTransportType;

    /**
     * 订金模式 0:不限；1:退还；2:不退还
     */
    @Column(name = "refund_flag_type")
    private Integer refundFlagType;

    /**
     * 价格模式 0:不限；1:有价电议；2:无价电议；3:一口价
     */
    @Column(name = "price_publish_type")
    private Integer pricePublishType;

    /**
     * 该货源%取值随机数
     */
    private Integer transportProportionNum;

    /**
     * 运费低值
     */
    private BigDecimal priceMin;

    /**
     * 运费高值
     */
    private BigDecimal priceMax;

    /**
     * 技术服务费抽取运费百分比
     */
    private BigDecimal tecServiceFeeRate;

    /**
     * 技术服务费低值
     */
    private BigDecimal tecServiceFeeMin;

    /**
     * 技术服务费高值
     */
    private BigDecimal tecServiceFeeMax;

    /**
     * 折扣时间（距首发X分钟内）
     */
    private Integer discountTime;

    /**
     * 限时折扣（最终折扣），0-10
     */
    private BigDecimal discount;

    private String discountConfig; //折扣配置（用于定时任务打折）

    /**
     * 好货标签类型：1-全部，2-部分
     */
    private Integer goodTransportLabelType;

    /**
     * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:其他
     */
    private Integer goodTransportLabel;

    /**
     * 路线类型：0-通用，1-指定路线
     */
    private Integer routeType;

    /**
     * 出发地
     */
    private String startCity;

    /**
     * 目的地
     */
    private String destCity;

    /**
     * 里程数低值
     */
    private BigDecimal distanceMin;

    /**
     * 里程数高值
     */
    private BigDecimal distanceMax;

    private String allDiscount; //所有折扣配置

}