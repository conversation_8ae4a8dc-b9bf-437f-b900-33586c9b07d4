package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_invoice_driver")
public class TytInvoiceDriver {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 进行认证的用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 认证状态(0 认证中，1 认证成功，2 认证失败，3 未认证)
     */
    @Column(name = "verify_status")
    private Integer verifyStatus;

    /**
     * 审核用户id
     */
    @Column(name = "verify_user_id")
    private Integer verifyUserId;

    /**
     * 审核用户姓名
     */
    @Column(name = "verify_user_name")
    private String verifyUserName;


    @Column(name = "driver_user_id")
    private Long driverUserId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    @Column(name = "id_card")
    private String idCard;

    /**
     * 性别(0未知 1男 2女)
     */
    @Column(name = "id_card_gender")
    private Integer idCardGender;

    /**
     * 身份证有效期开始日期
     */
    @Column(name = "id_card_effective_begin_time")
    private Date idCardEffectiveBeginTime;

    /**
     * 身份证有效期结束日期
     */
    @Column(name = "id_card_expiration_time")
    private Date idCardExpirationTime;

    /**
     * 民族
     */
    @Column(name = "id_card_nation")
    private String idCardNation;

    /**
     * 生日
     */
    @Column(name = "id_card_birthday")
    private Date idCardBirthday;

    /**
     * 身份证住址
     */
    @Column(name = "id_card_address")
    private String idCardAddress;

    /**
     * 是否本人，1：不是本人 2：是本人
     */
    private Integer self;

    /**
     * 身份证验真(0：未验真  1：验真成功  2：验真失败)
     */
    @Column(name = "id_card_truth")
    private Integer idCardTruth;

    /**
     * 从业资格证号
     */
    @Column(name = "qc_card")
    private String qcCard;

    /**
     * 从业资格证有效期
     */
    @Column(name = "qc_card_expiration_time")
    private Date qcCardExpirationTime;

    /**
     * 从业资格证验真(0：未验真  1：验真成功  2：验真失败)
     */
    @Column(name = "qc_card_truth")
    private Integer qcCardTruth;

    /**
     * 从业资格证姓名
     */
    @Column(name = "qc_name")
    private String qcName;

    /**
     * 从业资格证准驾车型
     */
    @Column(name = "qc_card_vehicle_type")
    private String qcCardVehicleType;

    /**
     * 从业资格证类别
     */
    @Column(name = "qc_card_type")
    private String qcCardType;

    /**
     * 从业资格证发证日期
     */
    @Column(name = "qc_card_send_time")
    private Date qcCardSendTime;

    /**
     * 驾驶证号
     */
    @Column(name = "dl_card")
    private String dlCard;

    /**
     * 驾驶证准驾车辆
     */
    @Column(name = "dl_card_vehicle_type")
    private String dlCardVehicleType;

    /**
     * 驾驶证发证机关
     */
    @Column(name = "dl_card_auth_gov")
    private String dlCardAuthGov;

    /**
     * 驾驶证初次领证日期
     */
    @Column(name = "dl_card_issue_time")
    private Date dlCardIssueTime;

    /**
     * 驾驶证有效期开始日期
     */
    @Column(name = "dl_card_effective_begin_time")
    private Date dlCardEffectiveBeginTime;

    /**
     * 驾驶证有效期结束日期
     */
    @Column(name = "dl_card_expiration_time")
    private Date dlCardExpirationTime;

    /**
     * 驾驶证验真(0：未验真  1：验真成功  2：验真失败)
     */
    @Column(name = "dl_card_truth")
    private Integer dlCardTruth;

    /**
     * 授权（0：未授权 1：授权中 2：已授权 3：拒绝授权）
     */
    private Integer empower;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 身份证是否是长期
     */
    @Column(name = "id_card_permanent")
    private Boolean idCardPermanent;

    /**
     * 驾驶证姓名
     */
    @Column(name = "dl_name")
    private String dlName;

    /**
     * 身份证发证机关
     */
    @Column(name = "id_card_sign_gov")
    private String idCardSignGov;

    /**
     * 身份证发证机关
     */
    @Column(name = "delete_flag")
    private Integer deleteFlag;

    /**
     * 驾驶证是否是长期
     */
    @Column(name = "dl_card_permanent")
    private Boolean dlCardPermanent;
}