package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_ad_position_goods_config")
public class TytAdPositionGoodsConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * tyt_ad_position表主键Id
     */
    @Column(name = "ad_position_id")
    private Long adPositionId;

    /**
     * 广告位位置:36货源详情页顶部   37订金支付页顶部  38电议弹窗  55货源发布弹窗   此处枚举同广告位位置
     */
    @Column(name = "ad_position")
    private Integer adPosition;

    /**
     * 货源价格类型 0全部  1有价  2无价  3一口价
     */
    @Column(name = "price_type")
    private Integer priceType;

    /**
     * 货源类型 0全部   1普通货源   2优车货源 3优车定价货源
     */
    @Column(name = "goods_type")
    private Integer goodsType;

    /**
     * 货主身份 0全部   1个人货主    2企业货主   3货站   4物流公司
     */
    @Column(name = "owner_identity")
    private Integer ownerIdentity;

    /**
     * 开票状态 0不限   1开票    2非开票
     */
    @Column(name = "invoice_transport")
    private Integer invoiceTransport;

    /**
     * 订金模式 0不限   1可退    2不可退
     */
    @Column(name = "refund_flag")
    private Integer refundFlag;

    /**
     * 发货方式 0不限   1优车发货  2普通发货
     */
    @Column(name = "delivery_flag")
    private Integer deliveryFlag;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;
}