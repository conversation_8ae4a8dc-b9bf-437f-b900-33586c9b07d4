package com.tyt.plat.entity.base;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抽佣分段三级配置（超时折扣）
 */
@Data
public class TytTecServiceFeeDiscountConfig {

    private Long id; // 主键ID

    private Long configId; // 抽佣货源技术服务费配置主表ID

    private Long stageId; // 抽佣货源技术服务费配置阶段表ID

    private Long proportionId; // 抽佣货源技术服务费配置阶段-抽佣百分比表ID

    private Integer discountTime; // 折扣时间（距首发X分钟内）

    private BigDecimal discount; // 限时折扣（最终折扣），0-10

    private Date createTime; // 创建时间

    private Date modifyTime; // 修改时间

}