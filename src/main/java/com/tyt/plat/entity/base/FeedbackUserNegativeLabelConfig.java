package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_user_negative_label_config")
public class FeedbackUserNegativeLabelConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户手机号
     */
    @Column(name = "user_phone")
    private String userPhone;

    /**
     * 展示的标签ID
     */
    @Column(name = "show_feedback_label_id")
    private Long showFeedbackLabelId;

    /**
     * 是否展示 0:不展示，1:展示
     */
    @Column(name = "is_show")
    private Integer isShow;

    /**
     * 操作人用户ID
     */
    @Column(name = "modify_user_id")
    private Long modifyUserId;

    /**
     * 操作人用户名称
     */
    @Column(name = "modify_user_name")
    private String modifyUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "user_type")
    private Integer userType;
}