package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "draw_activity_info")
public class DrawActivityInfoEntity {
    /**
     * 活动id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 活动名称
     */
    @Column(name = "activity_name")
    private String activityName;

    /**
     * 活动开始时间
     */
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 创建人id
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 活动状态(1.有效 2.无效)
     */
    private Integer status;

    /**
     * 限制每人抽奖次数类型(1.总共 2.每天)
     */
    @Column(name = "limit_times_type")
    private Integer limitTimesType;

    /**
     * 限制每人抽奖次数
     */
    @Column(name = "limit_times")
    private Integer limitTimes;

    /**
     * 修改人id
     */
    @Column(name = "update_user_id")
    private Long updateUserId;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * app类型(1.车主版 2.货主版)
     */
    @Column(name = "app_type")
    private Integer appType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除(1.未删除 2.已删除)
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 用于标识进行哪种业务校验，为空只进行基础校验，不进行业务校验
     */
    @Column(name = "biz_check_sign")
    private String bizCheckSign;
}