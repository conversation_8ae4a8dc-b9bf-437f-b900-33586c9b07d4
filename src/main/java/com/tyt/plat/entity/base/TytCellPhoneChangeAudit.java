package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_cell_phone_change_audit")
public class TytCellPhoneChangeAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户真实姓名
     */
    @Column(name = "true_name")
    private String trueName;

    /**
     * 变更前手机号
     */
    @Column(name = "old_cell_phone")
    private String oldCellPhone;

    /**
     * 变更后手机号
     */
    @Column(name = "new_cell_phone")
    private String newCellPhone;

    /**
     * 手持身份证照片url
     */
    @Column(name = "hand_id_photo")
    private String handIdPhoto;

    /**
     * 手持申请照片url
     */
    @Column(name = "hand_apply_for_photo")
    private String handApplyForPhoto;

    /**
     * 审核状态 1：待审核 2：审核通过 3：驳回
     */
    @Column(name = "audit_status")
    private Integer auditStatus;

    /**
     * 1:照片模糊 2：照片非本人 3：身份证非本人 4：申请书不规范 5：其他原因
     */
    @Column(name = "reject_reason_code")
    private Integer rejectReasonCode;

    /**
     * 驳回原因
     */
    @Column(name = "reject_reason_msg")
    private String rejectReasonMsg;

    /**
     * 审核人id
     */
    @Column(name = "opt_id")
    private Long optId;

    /**
     * 审核人姓名
     */
    @Column(name = "opt_name")
    private String optName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间（审核时间）
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}