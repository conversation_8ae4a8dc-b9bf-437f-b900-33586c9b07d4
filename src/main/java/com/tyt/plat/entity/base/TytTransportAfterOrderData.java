package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_after_order_data")
public class TytTransportAfterOrderData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 货物内容
     */
    @Column(name = "task_content")
    private String taskContent;

    /**
     * 货物类型名称,如“装载机”，“挖掘机”
     */
    @Column(name = "good_type_name")
    private String goodTypeName;

    /**
     * 出发地城市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 目的地市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 出发地目的地之间距离
     */
    private String distance;

    /**
     * 重量单位吨
     */
    private String weight;

    /**
     * 运费
     */
    private String price;

    /**
     * 本条创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}