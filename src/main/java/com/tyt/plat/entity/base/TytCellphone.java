package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_cellphone")
public class TytCellphone {
    /**
     * 手机号
     */
    @Id
    @Column(name = "cell_phone")
    private String cellPhone;

    /**
     * 采集日期
     */
    @Column(name = "create_time")
    private Date createTime;
}