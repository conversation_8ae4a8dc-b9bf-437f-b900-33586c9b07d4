package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_custom_first_order_record")
public class TytCustomFirstOrderRecord {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 客户手机号
     */
    @Column(name = "custom_phone")
    private String customPhone;

    /**
     * 首次发货时间
     */
    @Column(name = "first_publish_time")
    private Date firstPublishTime;

    /**
     * 首次履约完单时间
     */
    @Column(name = "first_finish_order_time")
    private Date firstFinishOrderTime;

    /**
     * 状态字段
     */
    private Byte status;
}