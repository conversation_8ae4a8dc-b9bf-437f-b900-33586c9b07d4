package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "dw_market_v4_agent_info")
public class DwMarketV4AgentInfo {
    /**
     * 主键,自增id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 提交经销商用户id
     */
    @Column(name = "user_id")
    private Integer userId;

    /**
     * 来源类型 1:后台 2:app/发布现车 3:app/商家入驻
     */
    private Integer source;

    /**
     * 经销商名称
     */
    private String name;

    /**
     * 经销商所在省
     */
    private String province;

    /**
     * 经销商所在市
     */
    private String city;

    /**
     * 经销商所在区域
     */
    private String area;

    /**
     * 经销商地址
     */
    private String address;

    /**
     * 经销商联系人
     */
    private String contact;

    /**
     * 对接人
     */
    private String meet;

    /**
     * 经销商电话(可用作登录账号发布现车)
     */
    @Column(name = "cell_phone")
    private String cellPhone;

    /**
     * 经销商电话_1
     */
    @Column(name = "cell_phone_f")
    private String cellPhoneF;

    /**
     * 经销商电话_2
     */
    @Column(name = "cell_phone_s")
    private String cellPhoneS;

    /**
     * 真实身份
     */
    private Byte role;

    /**
     * 创建人id
     */
    @Column(name = "creator_id")
    private Long creatorId;

    /**
     * 修改人id
     */
    @Column(name = "changer_id")
    private Long changerId;

    /**
     * 职位
     */
    private String position;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 入驻时间
     */
    private Date etime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 状态 0:禁用 1:启用
     */
    private Integer status;

    /**
     * 置顶状态 0:不置顶 1:置顶
     */
    @Column(name = "top_status")
    private Byte topStatus;

    /**
     * 合作状态 0:否 1:是
     */
    @Column(name = "cooperate_status")
    private Integer cooperateStatus;

    /**
     * 现车状态 1：保存（经销商列表不展示） 2：提交（经销商列表展示）
     */
    @Column(name = "show_status")
    private Byte showStatus;

    /**
     * 处理状态 -1:驳回 0:未处理 1:已处理
     */
    @Column(name = "treat_status")
    private Integer treatStatus;

    /**
     * 描述
     */
    private String description;

    /**
     * 驳回原因
     */
    @Column(name = "reject_reason")
    private String rejectReason;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;
}