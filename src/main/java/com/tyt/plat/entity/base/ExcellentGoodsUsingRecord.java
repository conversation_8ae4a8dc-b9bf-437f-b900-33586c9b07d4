package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "excellent_goods_using_record")
public class ExcellentGoodsUsingRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 使用次数
     */
    @Column(name = "use_count")
    private Integer useCount;

    /**
     * 使用时间
     */
    @Column(name = "use_date")
    private Date useDate;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 电议无价使用次数
     */
    @Column(name = "call_no_price_use_count")
    private Integer callNoPriceUseCount;

    /**
     * 电议有价使用次数
     */
    @Column(name = "call_price_use_count")
    private Integer callPriceUseCount;

    /**
     * 一口价使用次数
     */
    @Column(name = "fixed_price_use_count")
    private Integer fixedPriceUseCount;
}