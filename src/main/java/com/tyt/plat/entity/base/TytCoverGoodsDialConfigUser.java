package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_cover_goods_dial_config_user")
public class TytCoverGoodsDialConfigUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 操作人ID
     */
    @Column(name = "operate_user_id")
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    @Column(name = "operate_user_name")
    private String operateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 用户名
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 捂货规则配置表id
     */
    @Column(name = "dial_config_id")
    private Long dialConfigId;
}