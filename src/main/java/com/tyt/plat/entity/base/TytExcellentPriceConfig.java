package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_excellent_price_config")
public class TytExcellentPriceConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 出发地省
     */
    @Column(name = "start_provinc")
    private String startProvinc;

    /**
     * 出发地市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 目的地省
     */
    @Column(name = "dest_provinc")
    private String destProvinc;

    /**
     * 目的地市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 最小重量（单位吨）
     */
    @Column(name = "min_weight")
    private BigDecimal minWeight;

    /**
     * 最大重量（单位吨）
     */
    @Column(name = "max_weight")
    private BigDecimal maxWeight;

    /**
     * 最大里程（单位公里）
     */
    @Column(name = "max_distance")
    private BigDecimal maxDistance;

    /**
     * 最小里程（单位公里）
     */
    @Column(name = "min_distance")
    private BigDecimal minDistance;

    /**
     * 起步里程（单位公里）
     */
    @Column(name = "start_mileage")
    private BigDecimal startMileage;

    /**
     * 起步价（单位元）
     */
    @Column(name = "start_price")
    private BigDecimal startPrice;

    /**
     * 超里程单价（单位元）
     */
    @Column(name = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 调整系数
     */
    @Column(name = "adjust_coefficient")
    private BigDecimal adjustCoefficient;

    /**
     * 价格最大值系数
     */
    @Column(name = "max_price_coefficient")
    private BigDecimal maxPriceCoefficient;

    /**
     * 更快应答系数
     */
    @Column(name = "faster_coefficient")
    private BigDecimal fasterCoefficient;

    /**
     * 备注
     */
    private String remark;

    /**
     * 1:启用 0:禁用
     */
    private Integer status;

    /**
     * 创建人id
     */
    @Column(name = "create_id")
    private Long createId;

    /**
     * 创建人名称
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "modify_id")
    private Long modifyId;

    /**
     * 修改人
     */
    @Column(name = "modify_name")
    private String modifyName;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 价格模式：1一口价 2有价电议，多选用逗号隔开
     */
    @Column(name = "price_model")
    private String priceModel;

    /**
     * 货类
     */
    @Column(name = "good_type_name")
    private String goodTypeName;
}