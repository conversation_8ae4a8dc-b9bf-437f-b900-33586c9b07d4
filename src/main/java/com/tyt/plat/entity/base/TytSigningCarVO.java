package com.tyt.plat.entity.base;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TytSigningCarVO {

    private Long id;

    /**
     * 接单率
     */
    private BigDecimal receivingOrders;

    /**
     * 好评率
     */
    private BigDecimal favorableComment;

    private Long driverId;

    private String headCityNo;

    private String tailCityNo;

    private BigDecimal range;

    private Long userId;

    private String phone;

    private Integer status;

    /**
     * 车型
     */
    private String carType;

    /**
     * 是否车型匹配：0-否，1-是
     */
    private Integer carTypeMatch;

    /**
     * 司机标签：1-兼职运力，2-全职运力
     */
    private Integer driverTag;

    /**
     * 位置
     */
    private String location;

    /**
     * 距离
     */
    private BigDecimal distance;

    private Long driverUserId;
}