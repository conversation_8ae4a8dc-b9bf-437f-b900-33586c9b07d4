package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_special_car_dispatch_detail")
public class TytSpecialCarDispatchDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 专车派单id(tyt_special_car_dispatch表id)
     */
    @Column(name = "dispatch_id")
    private Long dispatchId;

    /**
     * 货源ID
     */
    @Column(name = "ts_id")
    private Long tsId;

    /**
     * 车主ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 车主联系电话
     */
    @Column(name = "pay_link_phone")
    private String payLinkPhone;

    /**
     * 司机id
     */
    @Column(name = "driver_id")
    private Long driverId;

    /**
     * 位置
     */
    private String location;

    /**
     * 距离
     */
    private BigDecimal distance;

    /**
     * tyt_signing_car_info表ID
     */
    @Column(name = "car_info_id")
    private Long carInfoId;

    /**
     * 派单优先级
     */
    private Integer priority;

    /**
     * 派单状态：0.未派单  1.派单成功 2.派单失败
     */
    @Column(name = "dispatch_status")
    private Integer dispatchStatus;

    /**
     * 派单失败原因
     */
    @Column(name = "dispatch_error_msg")
    private String dispatchErrorMsg;

    /**
     * 派单时间
     */
    @Column(name = "dispatch_time")
    private Date dispatchTime;

    /**
     * 接单状态：0.未接单 1.已接单
     */
    @Column(name = "accept_status")
    private Integer acceptStatus;

    /**
     * 接单时间
     */
    @Column(name = "accept_time")
    private Date acceptTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 是否人工指派：0-否，1-是
     */
    @Column(name = "manual_assign")
    private Byte manualAssign;

    /**
     * 车辆id
     */
    @Column(name = "car_id")
    private Long carId;

    /**
     * 车型是否匹配：0-否，1-是
     */
    @Column(name = "car_type_match")
    private Integer carTypeMatch;
}