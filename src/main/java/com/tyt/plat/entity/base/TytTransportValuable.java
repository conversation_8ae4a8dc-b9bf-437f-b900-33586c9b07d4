package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_valuable")
public class TytTransportValuable {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 发货人ID
     */
    @Column(name = "publish_user_id")
    private Long publishUserId;

    /**
     * 发货人姓名
     */
    @Column(name = "publish_user_name")
    private String publishUserName;

    /**
     * 给货货主手机号
     */
    @Column(name = "give_goods_phone")
    private String giveGoodsPhone;

    /**
     * 发货人ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货主出价
     */
    @Column(name = "owner_freight")
    private BigDecimal ownerFreight;

    /**
     * 价值货源类型：1-新客首单货源，2-优车2.0货源
     */
    @Column(name = "value_type")
    private Integer valueType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 是否跟进：0-否，1-是
     */
    @Column(name = "follow_up")
    private Integer followUp;

    /**
     * 是否优车2.0首单：0-否，1-是
     */
    @Column(name = "first_order")
    private Integer firstOrder;

    /**
     * 是否优车2.0前3单：0-否，1-是
     */
    @Column(name = "top_three_order")
    private Integer topThreeOrder;

    /**
     * 跟进人ID
     */
    @Column(name = "follow_up_user_id")
    private Long followUpUserId;

    /**
     * 跟进人姓名
     */
    @Column(name = "follow_up_user_name")
    private String followUpUserName;

    /**
     * 最新跟进时间
     */
    @Column(name = "follow_up_time")
    private Date followUpTime;

    /**
     * 市场跟进人ID
     */
    @Column(name = "market_follow_up_user_id")
    private Long marketFollowUpUserId;

    /**
     * 市场跟进人姓名
     */
    @Column(name = "market_follow_up_user_name")
    private String marketFollowUpUserName;

    /**
     * 司机是否出价：0-否，1-是
     */
    @Column(name = "driver_give_price")
    private Integer driverGivePrice;

    /**
     * 司机最新出价
     */
    @Column(name = "driver_latest_price")
    private BigDecimal driverLatestPrice;

    /**
     * 是否直客：0-否，1-是
     */
    @Column(name = "direct_customer")
    private Integer directCustomer;
}