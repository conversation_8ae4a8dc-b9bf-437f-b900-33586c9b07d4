package com.tyt.plat.entity.base;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>
 * 货源自动重发记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Getter
@Setter
@Table(name = "tyt_transport_auto_resend_record")
public class TransportAutoResendRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 之前的货源id
     */
    @Column(name = "old_src_msg_id")
    private Long oldSrcMsgId;

    /**
     * 新货源id
     */
    @Column(name = "new_src_msg_id")
    private Long newSrcMsgId;

    /**
     * 发布时间
     */
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 状态：0待重发；1重发成功；2重发失败
     */
    private Integer status;

    /**
     * 重发失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}
