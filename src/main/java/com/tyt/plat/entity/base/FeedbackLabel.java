package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_label")
public class FeedbackLabel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 标签名称
     */
    @Column(name = "label_name")
    private String labelName;

    /**
     * 标签所属类型 1 车方，2 货方
     */
    @Column(name = "label_type")
    private Integer labelType;

    /**
     * 类别 1:好评，2:中评，3:差评
     */
    @Column(name = "feedback_type")
    private Integer feedbackType;

    /**
     * 启用禁用状态 0:禁用，1:启用
     */
    @Column(name = "enable_status")
    private Boolean enableStatus;

    /**
     * 操作人用户ID
     */
    @Column(name = "modify_user_id")
    private Long modifyUserId;

    /**
     * 操作人用户名
     */
    @Column(name = "modify_user_name")
    private String modifyUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 标签显示优先级
     */
    @Column(name = "show_priority")
    private Integer showPriority;
}