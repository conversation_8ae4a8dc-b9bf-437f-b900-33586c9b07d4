package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_self_company_sign")
public class TytSelfCompanySign {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 平台签章主体企业名称
     */
    @Column(name = "company_name")
    private String companyName;

    /**
     * 联系人姓名
     */
    @Column(name = "link_user_name")
    private String linkUserName;

    /**
     * 联系人手机号
     */
    @Column(name = "link_phone")
    private String linkPhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * e签宝企业账号id
     */
    @Column(name = "sign_account_id")
    private String signAccountId;

    /**
     * e签宝企业印章地址
     */
    @Column(name = "sign_seal_url")
    private String signSealUrl;

    /**
     * 是否有效（0无效;1有效）
     */
    private Integer enable;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}