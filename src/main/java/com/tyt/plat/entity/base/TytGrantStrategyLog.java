package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_grant_strategy_log")
public class TytGrantStrategyLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户Id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * tyt_grant_strategy_config表主键配置Id
     */
    @Column(name = "grant_strategy_id")
    private Long grantStrategyId;

    /**
     * 发放条件
     */
    @Column(name = "grant_type")
    private String grantType;

    /**
     * 权益类型
     */
    @Column(name = "gain_type")
    private String gainType;

    /**
     * 发放数量
     */
    @Column(name = "gain_num")
    private Integer gainNum;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}