package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_block_config")
public class TytBlockConfig {
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配置类型 1：全部  2：手机号归属地 3：名单上传
     */
    @Column(name = "config_type")
    private Integer configType;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 车牌是否必填 1：是 2：否
     */
    @Column(name = "license_plate")
    private Integer licensePlate;

    /**
     * 新用户会员/有拨打次数
     */
    @Column(name = "new_user_permission")
    private Integer newUserPermission;

    /**
     * 老用户会员
     */
    @Column(name = "old_user_permission")
    private Integer oldUserPermission;

    /**
     * 老用户拨打次数
     */
    @Column(name = "old_user_phone")
    private Integer oldUserPhone;

    /**
     * 单车提醒阈值
     */
    private Integer remind;

    /**
     * 单车阻断阈值
     */
    private Integer block;

    /**
     * 单车未认证接单上限
     */
    @Column(name = "receiving_orders")
    private Integer receivingOrders;

    /**
     * 状态 1：开启 2：关闭
     */
    private Integer status;

    /**
     * 开启时间
     */
    @Column(name = "open_time")
    private Date openTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 更新人
     */
    @Column(name = "modify_name")
    private String modifyName;
}