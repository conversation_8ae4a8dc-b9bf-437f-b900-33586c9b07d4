package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_main_extend")
public class TytTransportMainExtend {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 用车类型：1-整车，2-零担
     */
    @Column(name = "use_car_type")
    private Integer useCarType;

    /**
     * 运价模式：1-固定运价，2-灵活运价
     */
    @Column(name = "price_type")
    private Integer priceType;

    /**
     * 最低建议价，优车定价最低值，特惠优车价，=fixPriceMin
     */
    private Integer suggestMinPrice;

    /**
     * 最高建议价，优车定价最高值，极速优车价，=fixPriceMax
     */
    private Integer suggestMaxPrice;

    /**
     * 优车定价最快成交价格，快速优车价，=fixPriceFast
     */
    private Integer fixPriceFast;

    /**
     * 成本价，=thMinPrice
     */
    private Integer costPrice;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 好货模型分数
     */
    @Column(name = "good_model_score")
    private BigDecimal goodModelScore;

    /**
     * 好货模型等级
     */
    @Column(name = "good_model_level")
    private Integer goodModelLevel;

    /**
     * 回价助手运费上限
     */
    @Column(name = "price_cap")
    private Integer priceCap;

    /**
     * 好差货模型分数
     */
    @Column(name = "lim_good_model_score")
    private BigDecimal limGoodModelScore;

    /**
     * 好差货模型等级
     */
    @Column(name = "lim_good_model_level")
    private Integer limGoodModelLevel;

    /**
     * 好货运价模型分数（抽佣）
     */
    @Column(name = "commission_score")
    private BigDecimal commissionScore;

    /**
     * 秒杀货源：1是0否
     */
    @Column(name = "seckill_goods")
    private Integer seckillGoods;

    /**
     * 优惠金额
     */
    @Column(name = "perk_price")
    private Integer perkPrice;

    /**
     * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
     */
    @Column(name = "good_transport_label")
    private Integer goodTransportLabel;

    /**
     * 货参不完整的好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0-其他
     */
    @Column(name = "good_transport_label_part")
    private Integer goodTransportLabelPart;

    /**
     * 是否是融合发货 1是0否
     */
    private Integer clientFusion;
}