package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_special_car_price_config")
public class TytSpecialCarPriceConfig {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 出发地城市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 目的地城市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 起始吨位
     */
    @Column(name = "start_tonnage")
    private Integer startTonnage;

    /**
     * 终止吨位
     */
    @Column(name = "end_tonnage")
    private Integer endTonnage;

    /**
     * 专车货主表tyt_dispatch_cargo_owner主键ID
     */
    @Column(name = "cargo_owner_id")
    private Long cargoOwnerId;

    /**
     * 货主名称
     */
    @Column(name = "cargo_owner_name")
    private String cargoOwnerName;

    /**
     * 记录状态：0-未生效，1-已生效
     */
    private Integer status;

    /**
     * 运费规则，json格式存储
     */
    @Column(name = "price_rule")
    private String priceRule;

    /**
     * 零担运费规则，json格式存储
     */
    @Column(name = "less_price_rule")
    private String lessPriceRule;

    /**
     * 驾驶货物费
     */
    @Column(name = "driving_fee")
    private BigDecimal drivingFee;

    /**
     * 运价模式：1-固定运价，2-灵活运价
     */
    @Column(name = "price_type")
    private Integer priceType;

    /**
     * 整车运价下限
     */
    @Column(name = "price_lower_limit")
    private BigDecimal priceLowerLimit;

    /**
     * 零担运价下限
     */
    @Column(name = "less_price_lower_limit")
    private BigDecimal lessPriceLowerLimit;

    /**
     * 创建人ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 修改人ID
     */
    @Column(name = "modify_user_id")
    private Long modifyUserId;

    /**
     * 修改人姓名
     */
    @Column(name = "modify_user_name")
    private String modifyUserName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 记录状态：0-正常，1-删除
     */
    @Column(name = "del_status")
    private Integer delStatus;
}