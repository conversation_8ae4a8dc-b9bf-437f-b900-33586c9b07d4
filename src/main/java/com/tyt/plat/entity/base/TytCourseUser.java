package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_course_user")
public class TytCourseUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 课程id
     */
    @Column(name = "course_id")
    private Long courseId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 学习时间
     */
    @Column(name = "study_time")
    private Date studyTime;

    /**
     * 学习状态（0未学习;1学习中;2学习完成）
     */
    @Column(name = "study_status")
    private Integer studyStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建管理员id
     */
    @Column(name = "create_man_id")
    private Long createManId;

    /**
     * 创建管理员名称
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 修改管理员id
     */
    @Column(name = "modify_man_id")
    private Long modifyManId;

    /**
     * 修改管理员名称
     */
    @Column(name = "modify_name")
    private String modifyName;
}