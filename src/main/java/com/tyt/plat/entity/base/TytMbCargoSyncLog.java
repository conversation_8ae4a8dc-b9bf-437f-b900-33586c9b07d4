package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_mb_cargo_sync_log")
public class TytMbCargoSyncLog implements Serializable {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * tyt_mb_cargo_sync_info 表的 id
     */
    private Long cargoInfoId;

    /**
     * 货源id
     */
    private Long cargoId;

    /**
     * 同步类型 0 新增 1修改 2删除
     */
    private Integer syncType;

    /**
     * tyt_mb_cargo_sync_info 被操作以后 对应数据的json
     */
    private String infoJson;

    /**
     * 货源版本控制
     */
    private Integer cargoVersion;
}
