package com.tyt.plat.entity.base;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 抽佣分段二级配置（抽佣百分比）
 */
@Data
public class TytTecServiceFeeProportionConfig {

    private Long id; // 主键ID

    private Long configId; // 抽佣货源技术服务费配置主表ID

    private Long stageId; // 抽佣货源技术服务费配置阶段表ID

    private BigDecimal tecServiceFeeRate; // 技术服务费抽取运费百分比

    private Date createTime; // 创建时间

    private Date modifyTime; // 修改时间

    /**
     * 三级配置（超时折扣）
     */
    private List<TytTecServiceFeeDiscountConfig> tytTecServiceFeeDiscountConfigList;

}