package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "dw_market_v4_brand_info")
public class DwMarketV4BrandInfo {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    private String companyId;

    /**
     * 联系人姓名
     */
    private String contact;

    /**
     * 联系电话
     */
    @Column(name = "cell_phone")
    private String cellPhone;

    /**
     * 职位
     */
    private String position;

    /**
     * 创建人id
     */
    @Column(name = "creator_id")
    private Long creatorId;

    /**
     * 修改者id
     */
    @Column(name = "changer_id")
    private Long changerId;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 入驻时间
     */
    private Date etime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 标识位 0:无效 1:有效
     */
    private Byte status;

    /**
     * 现车保存状态 1：保存（品牌列表不展示） 2：发布（品牌列表展示）
     */
    @Column(name = "show_status")
    private Byte showStatus;

    /**
     * 处理状态 0:未处理 1:处理
     */
    @Column(name = "handle_status")
    private Integer handleStatus;

    /**
     * 合作状态 0:未合作 1:合作
     */
    @Column(name = "cooperate_status")
    private Byte cooperateStatus;
}