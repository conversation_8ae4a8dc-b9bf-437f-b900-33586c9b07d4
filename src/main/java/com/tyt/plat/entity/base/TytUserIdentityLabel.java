package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_user_identity_label")
public class TytUserIdentityLabel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货主身份一级标签（1个人;2企业）
     */
    @Column(name = "goods_type_first")
    private Integer goodsTypeFirst;

    /**
     * 货主身份二级标签
     */
    @Column(name = "goods_type_second")
    private Integer goodsTypeSecond;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 货主身份一级审核标签（1个人;2企业）
     */
    @Column(name = "audit_goods_type_first")
    private Integer auditGoodsTypeFirst;

    /**
     * 货主身份二级审核标签
     */
    @Column(name = "audit_goods_type_second")
    private Integer auditGoodsTypeSecond;

    /**
     * 货主身份二级审核备注
     */
    @Column(name = "audit_goods_type_remark")
    private String auditGoodsTypeRemark;
}