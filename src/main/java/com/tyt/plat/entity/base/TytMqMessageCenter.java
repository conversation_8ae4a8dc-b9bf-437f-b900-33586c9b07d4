package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_mq_message_center")
public class TytMqMessageCenter {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 消息序列号，每个消息有一个唯一的序列号，用于唯一标示一条消息
     */
    @Column(name = "message_serial_num")
    private String messageSerialNum;

    /**
     * 消息的处理状态(1：未处理 2：已处理 3: 处理失败;4多次失败死信)
     */
    @Column(name = "deal_status")
    private Byte dealStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 发送类型（短信，消息，通知，按顺序位数是否是1确定是否发送，该字段只用来显示）
     */
    @Column(name = "message_type")
    private String messageType;

    /**
     * 失败次数
     */
    @Column(name = "fail_count")
    private Integer failCount;

    /**
     * 消息的原始完整内容
     */
    @Column(name = "message_content")
    private String messageContent;
}