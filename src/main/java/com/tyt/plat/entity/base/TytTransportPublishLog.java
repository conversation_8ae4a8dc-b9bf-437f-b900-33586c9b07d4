package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_publish_log")
public class TytTransportPublishLog {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 发布方式：1-确认发布，2-直接发布，3-加价，4-转一口价，5-填价
     */
    @Column(name = "pub_type")
    private Integer pubType;

    /**
     * 发布时间
     */
    @Column(name = "pub_time")
    private Date pubTime;

    /**
     * 请求来源：1-撤销弹窗
     */
    @Column(name = "request_source")
    private Integer requestSource;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}