package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_user_permission_gain")
public class TytUserPermissionGain {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 权益类型对应的唯一标识
     */
    @Column(name = "service_permission_type_id")
    private String servicePermissionTypeId;

    /**
     * 权益类型对应名称
     */
    @Column(name = "service_permission_type_name")
    private String servicePermissionTypeName;

    /**
     * 发放方式(0系统自动方法;1后台手动发放)
     */
    @Column(name = "send_type")
    private Integer sendType;

    /**
     * 获取类型(0默认)
     */
    @Column(name = "gain_type")
    private Integer gainType;

    /**
     * 订单id
     */
    @Column(name = "buy_goods_id")
    private Long buyGoodsId;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private Long goodsId;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 过期时间
     */
    @Column(name = "expired_time")
    private Date expiredTime;

    /**
     * 获得次数
     */
    @Column(name = "gain_count")
    private Integer gainCount;

    /**
     * 使用次数
     */
    @Column(name = "use_count")
    private Integer useCount;

    /**
     * 活动id
     */
    @Column(name = "activity_id")
    private Long activityId;

    /**
     * 活动名称
     */
    @Column(name = "activity_name")
    private String activityName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}