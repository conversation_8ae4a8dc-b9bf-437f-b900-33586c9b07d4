package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_dispatch_cooperative")
public class TytDispatchCooperative {
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 合作商名称
     */
    @Column(name = "cooperative_name")
    private String cooperativeName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态：1-有效；2-无效；
     */
    private Integer status;

    /**
     * 操作人用户ID
     */
    @Column(name = "action_user_id")
    private Long actionUserId;

    /**
     * 操作人姓名
     */
    @Column(name = "action_user_name")
    private String actionUserName;

    /**
     * 删除状态：0-未删除；1-已删除；
     */
    @Column(name = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}