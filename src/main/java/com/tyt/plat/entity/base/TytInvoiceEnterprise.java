package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_invoice_enterprise")
public class TytInvoiceEnterprise {
    /**
     * 企业ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 企业名称
     */
    @Column(name = "enterprise_name")
    private String enterpriseName;

    /**
     * 企业法人姓名
     */
    @Column(name = "legal_person_name")
    private String legalPersonName;

    /**
     * 企业法人手机号
     */
    @Column(name = "legal_person_phone")
    private String legalPersonPhone;

    /**
     * 企业法人身份证号码
     */
    @Column(name = "legal_person_card")
    private String legalPersonCard;

    /**
     * 法人身份证URL国徽面
     */
    @Column(name = "legal_person_card_url_g")
    private String legalPersonCardUrlG;

    /**
     * 法人身份证URL头像面
     */
    @Column(name = "legal_person_card_url_t")
    private String legalPersonCardUrlT;

    /**
     * 企业认证统一社会信用代码
     */
    @Column(name = "enterprise_credit_code")
    private String enterpriseCreditCode;

    /**
     * 企业类型
     */
    @Column(name = "enterprise_type")
    private String enterpriseType;

    /**
     * 企业经营范围
     */
    @Column(name = "enterprise_business_scope")
    private String enterpriseBusinessScope;

    /**
     * 企业归属地
     */
    @Column(name = "enterprise_home_address")
    private String enterpriseHomeAddress;

    /**
     * 企业注册地址
     */
    @Column(name = "enterprise_detail_address")
    private String enterpriseDetailAddress;

    /**
     * 营业执照URL
     */
    @Column(name = "license_url")
    private String licenseUrl;

    /**
     * 营业执照开始时间
     */
    @Column(name = "license_start_time")
    private Date licenseStartTime;

    /**
     * 营业执照到期时间
     */
    @Column(name = "license_end_time")
    private Date licenseEndTime;

    /**
     * 道路经营许可证照片URL
     */
    @Column(name = "transport_license_url")
    private String transportLicenseUrl;

    /**
     * 授权激活类型（1法人授权;2委托书授权） 
     */
    @Column(name = "sign_type")
    private Integer signType;

    /**
     * 协议编号(同contract_number)
     */
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 协议开始时间
     */
    @Column(name = "contract_start_time")
    private Date contractStartTime;

    /**
     * 协议结束时间
     */
    @Column(name = "contract_end_time")
    private Date contractEndTime;

    /**
     * 协议续约时间
     */
    @Column(name = "contract_continue_time")
    private Date contractContinueTime;

    /**
     * （废弃）协议文件地址URL
     */
    @Column(name = "contract_url")
    private String contractUrl;

    /**
     * 授权人用户ID
     */
    @Column(name = "certigier_user_id")
    private Long certigierUserId;

    /**
     * 授权人姓名
     */
    @Column(name = "certigier_user_name")
    private String certigierUserName;

    /**
     * 授权人手机号（平台账号）
     */
    @Column(name = "certigier_user_phone")
    private String certigierUserPhone;

    /**
     * （废弃）授权委托书URL
     */
    @Column(name = "authorization_url")
    private String authorizationUrl;

    /**
     * 企业发票税率
     */
    @Column(name = "enterprise_tax_rate")
    private BigDecimal enterpriseTaxRate;

    /**
     * 发票税率是否浮动：0-否；1-是；
     */
    @Column(name = "tax_rate_float")
    private Integer taxRateFloat;

    /**
     * (废弃)企业认证状态:0-未认证;1-认证通过;2-认证中;3-认证失败;4-认证过期;
     */
    @Column(name = "enterprise_verify_status")
    private Integer enterpriseVerifyStatus;

    /**
     * 基本信息审核状态:0-未提交;1-审核中;2-已通过;3-审核驳回;4-审核失败;5-信息已过期;
     */
    @Column(name = "info_verify_status")
    private Integer infoVerifyStatus;

    /**
     * 基本信息审核失败原因
     */
    @Column(name = "info_verify_reason")
    private String infoVerifyReason;

    /**
     * 基本信息提交时间
     */
    @Column(name = "info_verify_commit_time")
    private Date infoVerifyCommitTime;

    /**
     * 框架协议状态，同contract_status(0未签署;1签署中;2已签署;3签署失败;4已失效) 
     */
    @Column(name = "contract_verify_status")
    private Integer contractVerifyStatus;

    /**
     * (废弃)合同信息审核失败原因
     */
    @Column(name = "contract_verify_reason")
    private String contractVerifyReason;

    /**
     * (废弃)合同信息提交时间
     */
    @Column(name = "contract_verify_commit_time")
    private Date contractVerifyCommitTime;

    /**
     * 框架合同协议首次准入时间 
     */
    @Column(name = "contract_audit_time")
    private Date contractAuditTime;

    /**
     * (废弃)合同信息审核人ID
     */
    @Column(name = "contract_audit_user_id")
    private Long contractAuditUserId;

    /**
     * (废弃)合同信息审核人name
     */
    @Column(name = "contract_audit_user_name")
    private String contractAuditUserName;

    /**
     * (废弃)授权人信息审核状态:0-未提交;1-审核中;2-已通过;3-审核驳回;4-审核失败;5-信息已过期;
     */
    @Column(name = "certigier_verify_status")
    private Integer certigierVerifyStatus;

    /**
     * (废弃)授权人信息审核失败原因
     */
    @Column(name = "certigier_verify_reason")
    private String certigierVerifyReason;

    /**
     * (废弃)授权人信息提交时间
     */
    @Column(name = "certigier_verify_commit_time")
    private Date certigierVerifyCommitTime;

    /**
     * (废弃)授权人信息审核时间
     */
    @Column(name = "certigier_audit_time")
    private Date certigierAuditTime;

    /**
     * (废弃)授权人信息审核人ID
     */
    @Column(name = "certigier_audit_user_id")
    private Long certigierAuditUserId;

    /**
     * (废弃)授权人信息审核人name
     */
    @Column(name = "certigier_audit_user_name")
    private String certigierAuditUserName;

    /**
     * 在线开票状态(0关闭;1开启) 
     */
    @Column(name = "account_status")
    private Integer accountStatus;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 企业基础信息审核时间
     */
    @Column(name = "info_audit_time")
    private Date infoAuditTime;

    /**
     * 基本信息审核人ID
     */
    @Column(name = "info_audit_user_id")
    private Long infoAuditUserId;

    /**
     * 基本信息审核人name
     */
    @Column(name = "info_audit_user_name")
    private String infoAuditUserName;

    /**
     * 道路运输许可证驳回原因
     */
    @Column(name = "transport_reject_reason")
    private String transportRejectReason;

    /**
     * 道路运输许可证认证状态(0-未提交;1-审核中;2-已通过;3-审核驳回)
     */
    @Column(name = "transport_license_status")
    private Integer transportLicenseStatus;

    /**
     * 道路运输证提交时间
     */
    @Column(name = "transport_commit_time")
    private Date transportCommitTime;

    /**
     * 道路运输证审核时间
     */
    @Column(name = "transport_audit_time")
    private Date transportAuditTime;

    /**
     * 道路运输证审核人ID
     */
    @Column(name = "transport_audit_user_id")
    private Long transportAuditUserId;

    /**
     * 道路运输证审核人name
     */
    @Column(name = "transport_audit_user_name")
    private String transportAuditUserName;

    /**
     * 是否进行过天眼查企业核验 0：未进行；1：核验成功；2：核验失败
     */
    @Column(name = "real_verify")
    private Integer realVerify;

    /**
     * 营业执照是否长期 0：非长期；1：长期
     */
    @Column(name = "license_permanent")
    private Integer licensePermanent;

    /**
     * 企业最终激活状态(0未激活;1激活中;2激活成功;3激活失败)
     */
    @Column(name = "contract_final_status")
    private Integer contractFinalStatus;

    /**
     * 企业最终激活失败原因
     */
    @Column(name = "contract_final_reason")
    private String contractFinalReason;

    /**
     * 是否是企业管理员（0否;1是） 
     */
    @Column(name = "manager_flag")
    private Integer managerFlag;

    /**
     * 是否是手动关闭开票状态(account_status)（0否；1是）
     */
    @Column(name = "invoice_close_flag")
    private Integer invoiceCloseFlag;

    /**
     * 认证提交来源(1车,2货)
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 客户经理姓名
     */
    @Column(name = "customer_manager_name")
    private String customerManagerName;

    /**
     * 客户经理手机号
     */
    @Column(name = "customer_manager_phone")
    private String customerManagerPhone;

    /**
     * 客户经理邮箱
     */
    @Column(name = "customer_manager_email")
    private String customerManagerEmail;
}