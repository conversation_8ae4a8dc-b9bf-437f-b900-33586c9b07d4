package com.tyt.plat.entity.base;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>
 * 货源重发日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Getter
@Setter
@Table(name = "tyt_transport_resend_log")
public class TransportResendLogDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 之前的货源id
     */
    @Column(name = "old_src_msg_id")
    private Long oldSrcMsgId;

    /**
     * 新货源id
     */
    @Column(name = "new_src_msg_id")
    private Long newSrcMsgId;

    /**
     * 发布时间
     */
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 重发类型：1自动重发；2手动重发
     */
    @Column(name = "resend_type")
    private Integer resendType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}
