package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "excellent_goods_group")
public class ExcellentGoodsGroup {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 分组名称
     */
    @Column(name = "group_name")
    private String groupName;

    /**
     * 发货次数
     */
    @Column(name = "goods_count")
    private Integer goodsCount;

    /**
     * 1:日次数 2：月次数
     */
    private Integer type;

    /**
     * 0:停用 1：启用
     */
    private Integer enabled;

    /**
     * 0:删除 1：正常
     */
    @Column(name = "is_del")
    private Integer isDel;

    /**
     * 操作人id
     */
    @Column(name = "operate_id")
    private Long operateId;

    /**
     * 操作人名称
     */
    @Column(name = "operate_name")
    private String operateName;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 电议无价发货次数
     */
    @Column(name = "call_no_price_count")
    private Integer callNoPriceCount;

    /**
     * 电议有价发货次数
     */
    @Column(name = "call_price_count")
    private Integer callPriceCount;

    /**
     * 一口价发货次数
     */
    @Column(name = "fixed_price_count")
    private Integer fixedPriceCount;

}