package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_online_freight_enterprise")
public class TytOnlineFreightEnterprise {
    /**
     * 网络货运企业ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 企业名称
     */
    @Column(name = "enterprise_name")
    private String enterpriseName;

    /**
     * 企业法人姓名
     */
    @Column(name = "legal_person_name")
    private String legalPersonName;

    /**
     * 企业认证统一社会信用代码
     */
    @Column(name = "enterprise_credit_code")
    private String enterpriseCreditCode;

    /**
     * 企业注册地址
     */
    @Column(name = "enterprise_detail_address")
    private String enterpriseDetailAddress;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}