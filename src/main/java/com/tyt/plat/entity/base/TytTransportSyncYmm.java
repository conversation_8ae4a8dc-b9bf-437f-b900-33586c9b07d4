package com.tyt.plat.entity.base;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_sync_ymm")
public class TytTransportSyncYmm implements Serializable {

    private static final long serialVersionUID = -3675329980168556928L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * tyt货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 集团货源id 集团返回
     */
    @Column(name = "cargo_id")
    private Long cargoId;

    /**
     * tyt发货人id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 集团返回的合作商流水号,货源编辑、下架接口要用
     */
    @Column(name = "partner_serial_no")
    private String partnerSerialNo;

    /**
     * 合作商请求流水号 tyt生成
     */
    @Column(name = "transport_number")
    private String transportNumber;

    /**
     * tyt货源状态 0 发布中 1已下架
     */
    @Column(name = "transport_status")
    private Integer transportStatus;

    /**
     * 同步状态 0同步成功 1同步失败
     */
    @Column(name = "sync_status")
    private Integer syncStatus;

    /**
     * 异常code码
     */
    @Column(name = "sub_code")
    private String subCode;

    /**
     * 异常code码描述
     */
    @Column(name = "sub_code_msg")
    private String subCodeMsg;

    /**
     * 合作商追踪信息 货源编辑接口要用非必填字段 集团返回
     */
    @Column(name = "tracking_msg")
    private String trackingMsg;

    private Date ctime;

    private Date mtime;

    /**
     * 0未删除 1已删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;
}