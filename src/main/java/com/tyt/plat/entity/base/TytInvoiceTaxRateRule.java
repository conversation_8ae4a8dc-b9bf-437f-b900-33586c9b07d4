package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_invoice_tax_rate_rule")
public class TytInvoiceTaxRateRule {
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配置类型：1-通用；2-企业个性化；
     */
    @Column(name = "config_type")
    private Integer configType;

    /**
     * 企业认证表主键ID
     */
    @Column(name = "enterprise_id")
    private Long enterpriseId;

    /**
     * 签约前期费率
     */
    @Column(name = "earlier_tax_rate")
    private BigDecimal earlierTaxRate;

    /**
     * 阶梯费率配置集
     */
    @Column(name = "tiered_tax_rate")
    private String tieredTaxRate;

    /**
     * 状态：1-生效；2-失效；
     */
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}