package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_dispatch_view_detail")
public class TytTransportDispatchViewDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 车主id
     */
    @Column(name = "car_user_id")
    private Long carUserId;

    /**
     * 车主姓名
     */
    @Column(name = "car_user_name")
    private String carUserName;

    /**
     * 车主昵称
     */
    @Column(name = "car_nick_name")
    private String carNickName;

    /**
     * 车主手机号
     */
    @Column(name = "car_phone")
    private String carPhone;

    /**
     * 类型（1查看；2联系）
     */
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}