package com.tyt.plat.entity.base;

import com.tyt.transport.annotation.PropertyName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_mb_cargo_sync_info")
public class TytMbCargoSyncInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 满帮货源id
     */
    @Column(name = "cargo_id")
    private Long cargoId;

    /**
     * 货源名
     */
    @Column(name = "cargo_name")
    @PropertyName(value = "货源名")
    private String cargoName;

    /**
     * 发货地-省名
     */
    @Column(name = "load_province_name")
    private String loadProvinceName;

    /**
     * 发货地-市名
     */
    @Column(name = "load_city_name")
    private String loadCityName;

    /**
     * 发货地-区名
     */
    @Column(name = "load_district_name")
    private String loadDistrictName;

    /**
     * 发货地-地址详情（长度<=200）
     */
    @Column(name = "load_detail_address")
    @PropertyName(value = "发货地址详情")
    private String loadDetailAddress;

    /**
     * 发货地-纬度 （小数点后6位）
     */
    @Column(name = "load_lat")
    private Double loadLat;

    /**
     * 发货地-经度 （小数点后6位）
     */
    @Column(name = "load_lon")
    private Double loadLon;

    /**
     * 卸货地-省名
     */
    @Column(name = "unload_province_name")
    private String unloadProvinceName;

    /**
     * 卸货地-市名
     */
    @Column(name = "unload_city_name")
    private String unloadCityName;

    /**
     * 卸货地-区名
     */
    @Column(name = "unload_district_name")
    private String unloadDistrictName;

    /**
     * 卸货地-地址详情（长度<=200）
     */
    @Column(name = "unload_detail_address")
    @PropertyName(value = "卸货地址详情")
    private String unloadDetailAddress;

    /**
     * 卸货地-纬度（小数点后6位）
     */
    @Column(name = "unload_lat")
    private Double unloadLat;

    /**
     * 卸货地-经度（小数点后6位）
     */
    @Column(name = "unload_lon")
    private Double unloadLon;

    /**
     * 打包方式 原数据是List<Long>类型，这里直接存json
     */
    @Column(name = "packing_types")
    private String packingTypes;

    /**
     * 打包方式描述
     */
    @Column(name = "packing_type_desc")
    private String packingTypeDesc;

    /**
     * 最小重量
     */
    @Column(name = "min_weight")
    @PropertyName(value = "最小重量")
    private Double minWeight;

    /**
     * 最大重量
     */
    @Column(name = "max_weight")
    @PropertyName(value = "最大重量")
    private Double maxWeight;

    /**
     * 用车数量
     */
    @Column(name = "truck_count")
    @PropertyName(value = "用车数量")
    private Integer truckCount;

    /**
     * 车长，原数据是List<Double>类型，这里直接存json
     */
    @Column(name = "truck_lengths")
    private String truckLengths;

    /**
     * 车型，原数据是List<TruckType>类型，这里直接存json  UNLIMITED(- )1, "" ,
( )ORDINARY 0, "" ,
( )FLAT 1, "" ,
( )GAOLAN 2, "" ,
( )VAN 3, "" ,
( )CONTAINER 4, "" ,
( )FULLYCLOSED 5, "" ,
( )SPECIALTYPE 6, "" ,
( )DANGER 7, "" ,
( )DUMP 8, "" ,
( )COLDSTORAGE 9, "" ,
( )HEATPRESERVATION 10, "" ,
( )SAND 11, "" ,
( )HIGHANDLOWPLATE 12, "" ,
( )MICROBUS 13, "" ,
( )QUILT 14, "" ,
( )LADDER 15, "" ,
( )WING 16, "" ,
( )IVECO 17, "" ,
( )VANBUS 18, "" ,
( )MODERATE_FLATCAR 19, "" ,
( )MODERATE_VANBUS 20, "" ,
( )MINIBUS 21, "" ,
( )MODERATEBUS 22, "" ,
( )MINIFLATCAR 23, "" ,
( )TRICYCLE 24, "" ,
( )TRICYCLE_TRUCK 26, "" ,
( )LOW_FLATCAR 27, "" ,
( )OTHER 99, "" 
     */
    @Column(name = "truck_types")
    private String truckTypes;

    /**
     * 用车类型，1 零担 2整车
     */
    @Column(name = "truck_user_types")
    private Integer truckUserTypes;

    /**
     * 装车时间
     */
    @Column(name = "load_time")
    @PropertyName(value = "装车时间")
    private Date loadTime;

    /**
     * 卸货时间
     */
    @Column(name = "unload_time")
    @PropertyName(value = "卸货时间")
    private Date unloadTime;

    /**
     * 定金金额，单位：分
     */
    @Column(name = "deposit_amt")
    @PropertyName(value = "定金金额")
    private Long depositAmt;

    /**
     * 订金是否退还   0：不可退1：可退
     */
    @Column(name = "deposit_return")
    @PropertyName(value = "订金是否退还")
    private Integer depositReturn;

    /**
     * 运费，单位：分
     */
    @Column(name = "expect_freight")
    @PropertyName(value = "运费")
    private Long expectFreight;

    /**
     * 成交模式
BUYOUT("buyout", "",1),
TEL("tel", "",2),
ENTRUST("entrust", "",3),
OPERATOR_TRUCK_BOOK("operatorTruckBook", "", 4)
     */
    @Column(name = "deal_mode")
    private Integer dealMode;

    /**
     * 最小长度
     */
    @Column(name = "min_length")
    private Double minLength;

    /**
     * 最大长度
     */
    @Column(name = "max_length")
    private Double maxLength;

    /**
     * 最小宽度
     */
    @Column(name = "min_width")
    private Double minWidth;

    /**
     * 最大宽度
     */
    @Column(name = "max_width")
    private Double maxWidth;

    /**
     * 最小高度
     */
    @Column(name = "min_height")
    private Double minHeight;

    /**
     * 最大高度
     */
    @Column(name = "max_height")
    private Double maxHeight;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name")
    @PropertyName(value = "联系人姓名")
    private String contactName;

    /**
     * 联系人手机号
     */
    @Column(name = "contact_telephone")
    @PropertyName(value = "contact_telephone")
    private String contactTelephone;

    /**
     * 货主id
     */
    @Column(name = "shipper_id")
    private Long shipperId;

    /**
     * 删除标识 0 未删除 1已删除
     */
    @Column(name = "del_flag")
    private Integer delFlag;

    /**
     * 最后操作类型 0 新增 1修改 2删除
     */
    @Column(name = "last_operate_action")
    private Integer lastOperateAction;

    /**
     * 发货地 省+市+区 拼接以后的字符串
     */
    @Column(name = "start_point")
    private String startPoint;

    /**
     * 卸货地 省+市+区 拼接以后的字符串
     */
    @Column(name = "dest_point")
    private String destPoint;

    /**
     * 货源版本控制
     */
    @Column(name = "cargo_version")
    private Integer cargoVersion;

    /**
     * 删除原因
     */
    @Column(name = "del_reason")
    private String delReason;
}