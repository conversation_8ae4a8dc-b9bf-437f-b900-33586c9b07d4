package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_activate_config")
public class TytActivateConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 登录礼包id
     */
    @Column(name = "login_give_goods")
    private Long loginGiveGoods;

    /**
     * 登录赠送商品id
     */
    @Column(name = "login_give_goods_id")
    private Long loginGiveGoodsId;

    /**
     * 登录赠送次数
     */
    @Column(name = "login_give_goods_times")
    private Integer loginGiveGoodsTimes;

    /**
     * 分享礼包id
     */
    @Column(name = "share_give_goods")
    private Long shareGiveGoods;

    /**
     * 分享赠送商品id
     */
    @Column(name = "share_give_goods_id")
    private Long shareGiveGoodsId;

    /**
     * 分享赠送次数
     */
    @Column(name = "share_give_goods_times")
    private Integer shareGiveGoodsTimes;

    /**
     * 近期登录天数
     */
    @Column(name = "limit_login_days")
    private Integer limitLoginDays;

    /**
     * 近期拨打天数
     */
    @Column(name = "limit_call_days")
    private Integer limitCallDays;

    /**
     * 近期拨打次数
     */
    @Column(name = "limit_call_num")
    private Integer limitCallNum;

    /**
     * 不赠送天数
     */
    @Column(name = "no_give_days")
    private Integer noGiveDays;

    /**
     * 会员状态  1过期30天内 2过期3个月内 3过期6个月内 4过期12个月内
     */
    @Column(name = "vip_status")
    private Integer vipStatus;

    /**
     * 新注册状态 1注册30天内 2注册3个月内
     */
    @Column(name = "register_status")
    private Integer registerStatus;

    /**
     * 配置状态 0无效 1有效
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operator;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "modify_time")
    private Date modifyTime;
}