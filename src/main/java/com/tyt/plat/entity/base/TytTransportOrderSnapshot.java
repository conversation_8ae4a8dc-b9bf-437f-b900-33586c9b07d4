package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_order_snapshot")
public class TytTransportOrderSnapshot {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 原始货源id(tyt_transport_main主键)
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 订单id(tyt_transport_orders主键)
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 快照创建时间
     */
    @Column(name = "snapshot_time")
    private Date snapshotTime;

    /**
     * 出发地(省市区以减号-分割开)
     */
    @Column(name = "start_point")
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    @Column(name = "dest_point")
    private String destPoint;

    /**
     * 货物内容
     */
    @Column(name = "task_content")
    private String taskContent;

    /**
     * 联系人
     */
    private String tel;

    /**
     * 发布时间
     */
    @Column(name = "pub_time")
    private String pubTime;

    /**
     * qq
     */
    @Column(name = "pub_qq")
    private Long pubQq;

    /**
     * 昵称
     */
    @Column(name = "nick_name")
    private String nickName;

    /**
     * 用户认证昵称
     */
    @Column(name = "user_show_name")
    private String userShowName;

    /**
     * 状态 1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     */
    private Short status;

    /**
     * 人工/自动
     */
    private Short source;

    /**
     * 采集时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 上传电话
     */
    @Column(name = "upload_cellphone")
    private String uploadCellphone;

    /**
     * 重发时间
     */
    private Short resend;

    /**
     * 出发地坐标
     */
    @Column(name = "start_coord")
    private String startCoord;

    /**
     * 目的地坐标
     */
    @Column(name = "dest_coord")
    private String destCoord;

    /**
     * 终端标识
     */
    @Column(name = "plat_id")
    private Short platId;

    /**
     * 验证标识
     */
    @Column(name = "verify_flag")
    private Short verifyFlag;

    /**
     * 运费，尚未使用
     */
    private String price;

    /**
     * 发布人usreID 关联tyt_user表id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 运费代码
     */
    @Column(name = "price_code")
    private String priceCode;

    /**
     * 出发地坐标x
     */
    @Column(name = "start_coord_x")
    private Integer startCoordX;

    /**
     * 出发地坐标y
     */
    @Column(name = "start_coord_y")
    private Integer startCoordY;

    /**
     * 目的地坐标x
     */
    @Column(name = "dest_coord_x")
    private Integer destCoordX;

    /**
     * 目的地坐标y
     */
    @Column(name = "dest_coord_y")
    private Integer destCoordY;

    /**
     * 出发地详细地址
     */
    @Column(name = "start_detail_add")
    private String startDetailAdd;

    /**
     * 出发地经度
     */
    @Column(name = "start_longitude")
    private Integer startLongitude;

    /**
     * 出发地纬度
     */
    @Column(name = "start_latitude")
    private Integer startLatitude;

    /**
     * 目的地详细地址
     */
    @Column(name = "dest_detail_add")
    private String destDetailAdd;

    /**
     * 目的地经度
     */
    @Column(name = "dest_longitude")
    private Integer destLongitude;

    /**
     * 目的地纬度
     */
    @Column(name = "dest_latitude")
    private Integer destLatitude;

    /**
     * 发布日期
     */
    @Column(name = "pub_date")
    private Date pubDate;

    /**
     * 货物代码 与 货物对应 task_content 字段是一对
     */
    @Column(name = "goods_code")
    private String goodsCode;

    /**
     * 重量代码
     */
    @Column(name = "weight_code")
    private String weightCode;

    /**
     * 重量单位吨
     */
    private String weight;

    /**
     * 货物长单位米
     */
    private String length;

    /**
     * 货物宽单位米
     */
    private String wide;

    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 是否三超 0未超1超
     */
    @Column(name = "is_superelevation")
    private String isSuperelevation;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 备注
     */
    private String remark;

    /**
     * 出发地目的地之间距离
     */
    private Integer distance;

    /**
     * 发货日期
     */
    @Column(name = "pub_goods_time")
    private Date pubGoodsTime;

    /**
     * 联系人电话3
     */
    private String tel3;

    /**
     * 联系人电话4
     */
    private String tel4;

    /**
     * 显示类型 0不显示  1显示
     */
    @Column(name = "display_type")
    private String displayType;

    /**
     * hash_code
     */
    @Column(name = "hash_code")
    private String hashCode;

    /**
     * 是否完善了车的信息 车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
     */
    @Column(name = "is_car")
    private String isCar;

    /**
     * 用户类型0试用 1付费 2未激活
     */
    @Column(name = "user_type")
    private Short userType;

    @Column(name = "pc_old_content")
    private String pcOldContent;

    /**
     * 重发次数
     */
    @Column(name = "resend_counts")
    private Integer resendCounts;

    /**
     * 照片认证标志0未认证1通过2认证中3认证失败
     */
    @Column(name = "verify_photo_sign")
    private Short verifyPhotoSign;

    /**
     * 用户分数
     */
    @Column(name = "user_part")
    private Integer userPart;

    /**
     * 出发地城市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 出发地省
     */
    @Column(name = "start_provinc")
    private String startProvinc;

    /**
     * 出发地区
     */
    @Column(name = "start_area")
    private String startArea;

    /**
     * 目的地省
     */
    @Column(name = "dest_provinc")
    private String destProvinc;

    /**
     * 目的地市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 目的地区
     */
    @Column(name = "dest_area")
    private String destArea;

    /**
     * 客户端版本号
     */
    @Column(name = "client_version")
    private String clientVersion;

    /**
     * 是否收信息费货源   0是不需要1是需要
     */
    @Column(name = "is_info_fee")
    private String isInfoFee;

    /**
     * 信息费运单状态：0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
     */
    @Column(name = "info_status")
    private String infoStatus;

    /**
     * 运单号
     */
    @Column(name = "ts_order_no")
    private String tsOrderNo;

    /**
     * 第一次发布时间
     */
    @Column(name = "release_time")
    private Date releaseTime;

    /**
     * 发货人注册时间
     */
    @Column(name = "reg_time")
    private Date regTime;

    /**
     * 货物型号
     */
    private String type;

    /**
     * 货物品牌
     */
    private String brand;

    /**
     * 货物类型名称,如“装载机”，“挖掘机”
     */
    @Column(name = "good_type_name")
    private String goodTypeName;

    /**
     * 货物的台数，针对标准化的数据
     */
    @Column(name = "good_number")
    private Integer goodNumber;

    /**
     * 是否是标准化数据：0是，1不是
     */
    @Column(name = "is_standard")
    private Byte isStandard;

    /**
     * 匹配项的ID，针对标准化的数据
     */
    @Column(name = "match_item_id")
    private Integer matchItemId;

    /**
     * android两点距离
     */
    @Column(name = "android_distance")
    private Integer androidDistance;

    /**
     * IOS两点距离
     */
    @Column(name = "ios_distance")
    private Integer iosDistance;

    /**
     * 是否展示在找货列表 0不显示，1是显示
     */
    @Column(name = "is_display")
    private Short isDisplay;

    /**
     * 参考长度
     */
    @Column(name = "refer_length")
    private Integer referLength;

    /**
     * 参考宽度
     */
    @Column(name = "refer_width")
    private Integer referWidth;

    /**
     * 参考高度
     */
    @Column(name = "refer_height")
    private Integer referHeight;

    /**
     * 参考重量
     */
    @Column(name = "refer_weight")
    private Integer referWeight;

    /**
     * 车辆长度
     */
    @Column(name = "car_length")
    private String carLength;

    /**
     * 装车时间
     */
    @Column(name = "loading_time")
    private Date loadingTime;

    /**
     * 卸货起始时间
     */
    @Column(name = "begin_unload_time")
    private Date beginUnloadTime;

    /**
     * 卸车时间
     */
    @Column(name = "unload_time")
    private Date unloadTime;

    /**
     * 车辆最低长度，单位米
     */
    @Column(name = "car_min_length")
    private BigDecimal carMinLength;

    /**
     * 车辆最大长度，单位米
     */
    @Column(name = "car_max_length")
    private BigDecimal carMaxLength;

    /**
     * 车辆类型
     */
    @Column(name = "car_type")
    private String carType;

    /**
     * 装货起始时间
     */
    @Column(name = "begin_loading_time")
    private Date beginLoadingTime;

    /**
     * 挂车样式
     */
    @Column(name = "car_style")
    private String carStyle;

    /**
     * 工作面高最小值，单位米
     */
    @Column(name = "work_plane_min_high")
    private BigDecimal workPlaneMinHigh;

    /**
     * 工作面高最大值，单位米
     */
    @Column(name = "work_plane_max_high")
    private BigDecimal workPlaneMaxHigh;

    /**
     * 工作面长最小值，单位米
     */
    @Column(name = "work_plane_min_length")
    private BigDecimal workPlaneMinLength;

    /**
     * 工作面长最大值，单位米
     */
    @Column(name = "work_plane_max_length")
    private BigDecimal workPlaneMaxLength;

    /**
     * 是否需要爬梯
     */
    private String climb;

    /**
     * 订单量
     */
    @Column(name = "order_number")
    private Integer orderNumber;

    /**
     * 好评度
     */
    private Integer evaluate;

    /**
     * 特殊要求
     */
    @Column(name = "special_required")
    private String specialRequired;

    /**
     * 相似编码
     */
    @Column(name = "similarity_code")
    private String similarityCode;

    /**
     * 相似货源首发ID
     */
    @Column(name = "similarity_first_id")
    private Long similarityFirstId;

    /**
     * 相似货源首发信息
     */
    @Column(name = "similarity_first_info")
    private String similarityFirstInfo;

    /**
     * 轮胎是否外露
     */
    @Column(name = "tyre_exposed_flag")
    private String tyreExposedFlag;

    /**
     * 车辆长度标签
     */
    @Column(name = "car_length_labels")
    private String carLengthLabels;

    /**
     * 调车数量
     */
    @Column(name = "shunting_quantity")
    private Integer shuntingQuantity;

    /**
     * 首发货源类型（电议1，一口价2）
     */
    @Column(name = "first_publish_type")
    private Short firstPublishType;

    /**
     * 货源类型（电议1，一口价2）
     */
    @Column(name = "publish_type")
    private Short publishType;

    /**
     * 是否是优车货源（0:否 1：是）
     */
    @Column(name = "excellent_goods")
    private Integer excellentGoods;

    /**
     * 信息费（元）
     */
    @Column(name = "info_fee")
    private BigDecimal infoFee;

    /**
     * 技术服务费（元）
     */
    @Column(name = "tec_service_fee")
    private BigDecimal tecServiceFee;

    /**
     * 是否删除 0：未删除 1已删除
     */
    @Column(name = "is_delete")
    private Short isDelete;

    /**
     * 是否是 17.5 米专享 0：否 1：是
     */
    @Column(name = "exclusive_type")
    private Integer exclusiveType;

    /**
     * 信用分
     */
    @Column(name = "total_score")
    private BigDecimal totalScore;

    /**
     * 信用分等级_new
     */
    @Column(name = "rank_level")
    private Integer rankLevel;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    @Column(name = "refund_flag")
    private Integer refundFlag;

    /**
     * 货源来源（1货主；2调度客服；）
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 官方授权昵称
     */
    @Column(name = "auth_name")
    private String authName;

    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    @Column(name = "label_json")
    private String labelJson;

    /**
     * 保障货源（1是；0否；）
     */
    @Column(name = "guarantee_goods")
    private Integer guaranteeGoods;

    /**
     * 标准货名备注
     */
    @Column(name = "machine_remark")
    private String machineRemark;


    /**
     * 优惠价
     */
    @Column(name = "promotion_price")
    private BigDecimal promotionPrice;

    /**
     * 划线价
     */
    @Column(name = "strike_through_price")
    private BigDecimal strikeThroughPrice;

    /**
     * 货源客服经理手机号
     */
    @Column(name = "ts_customer_manage_phone")
    private String tsCustomerManagePhone;

    /**
     * 货源客服经理名称
     */
    @Column(name = "ts_customer_manage_name")
    private String tsCustomerManageName;

    /**
     * 是否是S城直客（0:否 1:是）
     */
    @Column(name = "is_scity_direct_customer")
    private Integer isScityDirectCustomer;

    @Column(name = "invoice_goods_source")
    private String invoiceGoodsSource;

    @Column(name = "project_name")
    private String projectName;

    /**
     * 秒抢货源：1是0否
     */
    @Column(name = "seckill_goods")
    private Integer seckillGoods;

    /**
     * 订单分配状态 0初始化 1分配成功 2分配失败
     */
    @Column(name = "order_allocate_state")
    private Integer orderAllocateState;

}