package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_app_call_log")
public class TytAppCallLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 电话日志所在的模块，暂定 1:货物（如果有其他由此需求需要在这里进行添加说明）
     */
    @Column(name = "call_module")
    private Byte callModule;

    /**
     * 所属车辆
     */
    @Column(name = "from_car")
    private String fromCar;

    /**
     * 所属车辆
     */
    @Column(name = "car_id")
    private String carId;

    /**
     * 拨打电话的时间
     */
    @Column(name = "call_time")
    private Date callTime;

    /**
     * 拨打电话用户的ID
     */
    @Column(name = "caller_id")
    private Long callerId;

    /**
     * 用户拨打电话咨询的信息的id(如果是货物模块，则就是货物id)
     */
    @Column(name = "called_info_id")
    private Long calledInfoId;

    /**
     * 电话咨询信息的结果，货物模块定义如下值：
            1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易 6：已经拉走 (具体值通过tyt_source查询，group_code值为app_call_result_code)
     */
    @Column(name = "call_result_code")
    private Byte callResultCode;

    /**
     * 电话标注
     */
    @Column(name = "call_result_name")
    private String callResultName;

    /**
     * 发布用户ID
     */
    @Column(name = "pub_user_id")
    private Long pubUserId;

    /**
     * 原货物信息ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 拨打备注
     */
    private String reference;

    /**
     * 客户端标识
     */
    @Column(name = "plat_id")
    private String platId;

    /**
     * 标记货主code，多选以逗号分割(具体值通过tyt_source查询，group_code值为app_call_result_code)
     */
    @Column(name = "marker_owner_codes")
    private String markerOwnerCodes;
    /**
     * 标记货主具体信息
     */
    @Column(name = "marker_owner_names")
    private String markerOwnerNames;
}