package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_dispatch_car_type")
public class TytTransportDispatchCarType {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 起始吨位
     */
    @Column(name = "start_tonnage")
    private Integer startTonnage;

    /**
     * 终止吨位
     */
    @Column(name = "end_tonnage")
    private Integer endTonnage;

    /**
     * 车型
     */
    @Column(name = "car_type")
    private String carType;

    /**
     * 状态：1-启用，2-禁用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}