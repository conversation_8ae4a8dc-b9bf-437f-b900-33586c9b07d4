package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_special_car_dispatch_failure")
public class TytSpecialCarDispatchFailure {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 货主ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 工单状态：0-待处理，1-处理中，2-已结单
     */
    @Column(name = "work_order_status")
    private Integer workOrderStatus;

    /**
     * 首次响应时间
     */
    @Column(name = "response_time")
    private Date responseTime;

    /**
     * 结单时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 发货用户类型：1-调度发货，2-普通用户发货
     */
    @Column(name = "publish_user_type")
    private Byte publishUserType;

    /**
     * 派单类型：0-系统自动派单，1-人工指派，2-人工改派
     */
    @Column(name = "dispatch_type")
    private Byte dispatchType;

    /**
     * 发货人ID(tyt_user表ID)
     */
    @Column(name = "publish_user_id")
    private Long publishUserId;

    /**
     * 发货人姓名
     */
    @Column(name = "publish_user_name")
    private String publishUserName;

    /**
     * 调度负责人ID
     */
    @Column(name = "dispatcher_id")
    private Long dispatcherId;

    /**
     * 调度负责人姓名
     */
    @Column(name = "dispatcher_name")
    private String dispatcherName;

    /**
     * 货主出价
     */
    @Column(name = "owner_freight")
    private BigDecimal ownerFreight;

    /**
     * 给货货主手机号
     */
    @Column(name = "give_goods_phone")
    private String giveGoodsPhone;

    /**
     * 给货货主姓名
     */
    @Column(name = "give_goods_name")
    private String giveGoodsName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 删除状态：0-正常，1-已删除
     */
    @Column(name = "del_status")
    private Byte delStatus;

    /**
     * 是否可大厅抢单 0：否；1：是
     */
    @Column(name = "declare_in_public")
    private Integer declareInPublic;
}