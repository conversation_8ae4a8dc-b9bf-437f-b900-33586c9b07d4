package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_mb_merge")
public class TytTransportMbMerge {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * YMM 货源id
     */
    @Column(name = "cargo_id")
    private Long cargoId;

    /**
     * 特运通货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 0：有效 1：无效
     */
    private Byte status;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 版本 发货、编辑发布维护
     */
    @Column(name = "cargo_version")
    private Integer cargoVersion;
}