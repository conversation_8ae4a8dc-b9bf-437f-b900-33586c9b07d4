package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "goods_address_level_record")
public class GoodsAddressLevelRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 出发地省
     */
    @Column(name = "start_province")
    private String startProvince;

    /**
     * 出发地市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 出发地区
     */
    @Column(name = "start_district")
    private String startDistrict;

    /**
     * 出发地乡镇街道
     */
    @Column(name = "start_township")
    private String startTownship;

    /**
     * 出发地路、门牌号
     */
    @Column(name = "start_street")
    private String startStreet;

    /**
     * 目的地省
     */
    @Column(name = "dest_province")
    private String destProvince;

    /**
     * 目的地市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 目的地区
     */
    @Column(name = "dest_district")
    private String destDistrict;

    /**
     * 目的地乡镇街道
     */
    @Column(name = "dest_township")
    private String destTownship;

    /**
     * 目的地路、门牌号
     */
    @Column(name = "dest_street")
    private String destStreet;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 修改人
     */
    @Column(name = "modify_name")
    private String modifyName;

    /**
     * 发货时调用BI优车好货接口BI返回结果
     */
    @Column(name = "i_g_b_i_result_data")
    private Integer iGBIResultData;

    /**
     * 编辑发货时是否实际展示过【优推好车主】
     */
    @Column(name = "publish_transport_is_show_good_car")
    private Integer publishTransportIsShowGoodCar;

    /**
     * 出发地地址来源,1:地图选点，2：手动搜索
     */
    @Column(name = "start_addr_source")
    private Integer startAddrSource;

    /**
     * 目的地地址来源,1:地图选点，2：手动搜索
     */
    @Column(name = "dest_addr_source")
    private Integer destAddrSource;

    /**
     * 建议价最小价格
     */
    @Column(name = "th_min_price")
    private Integer thMinPrice;

    /**
     * 建议价最大价格建议价格
     */
    @Column(name = "th_max_price")
    private Integer thMaxPrice;

    /**
     * 建议价建议价格
     */
    @Column(name = "suggest_price")
    private Integer suggestPrice;

    /**
     * 1:app;2:pc
     */
    @Column(name = "client_type")
    private Integer clientType;

    /**
     * 建议价最小价格
     */
    @Column(name = "suggest_min_price")
    private BigDecimal suggestMinPrice;

    /**
     * 建议价最大价格建议价格
     */
    @Column(name = "suggest_max_price")
    private BigDecimal suggestMaxPrice;

    /**
     * 优车定价低值
     */
    @Column(name = "fix_price_min")
    private Integer fixPriceMin;

    /**
     * 优车定价高值
     */
    @Column(name = "fix_price_max")
    private Integer fixPriceMax;

    /**
     * 优车定价最快成交价格
     */
    @Column(name = "fix_price_fast")
    private Integer fixPriceFast;

    /**
     * 是否弹出了优车定价货源卡片
     */
    @Column(name = "show_good_car_price_transport_tab")
    private Integer showGoodCarPriceTransportTab;

    /**
     * 自动转优车定价类型 1:编辑发布自动转；2:直接发布自动转；3：填价、加价自动转；4转一口价自动转；5YMM货源自动转
     */
    @Column(name = "automatic_good_car_price_transport_type")
    private Integer automaticGoodCarPriceTransportType;

    /**
     * 是否优车定价货源
     */
    @Column(name = "good_car_price_transport")
    private Integer goodCarPriceTransport;

    /**
     * 配置的捂货时间(单位秒)
     */
    @Column(name = "x_time_in_config")
    private Integer xTimeInConfig;

    /**
     * 实际在线捂货时间(单位秒)
     */
    @Column(name = "x_time_in_actual")
    private Integer xTimeInActual;

    /**
     * 发货类型，1:首发 2:编辑重发 3:直接发布
     */
    @Column(name = "publish_type")
    private Byte publishType;

    /**
     * 公里数，一位小数(专车发货新增字段)
     */
    @Column(name = "distance_kilometer")
    private BigDecimal distanceKilometer;

    /**
     * 其他费用
     */
    @Column(name = "other_fee")
    private BigDecimal otherFee;

    /**
     * 是否抽佣货源 1:是；0:否
     */
    @Column(name = "commission_transport")
    private Integer commissionTransport;

    /**
     * 是否满足抽佣条件打标 0：不抽佣 1：抽佣 2：首单免佣
     */
    @Column(name = "meet_commission_rules")
    private Integer meetCommissionRules;

    /**
     * 1:由专车变为普通好货货源
     */
    @Column(name = "change_goods_type")
    private Integer changeGoodsType;
}