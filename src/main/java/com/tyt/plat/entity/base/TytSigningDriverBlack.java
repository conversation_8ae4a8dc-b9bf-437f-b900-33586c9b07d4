package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_signing_driver_black")
public class TytSigningDriverBlack {
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 司机手机号
     */
    @Column(name = "cell_phone")
    private String cellPhone;

    /**
     * 司机用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 状态：1-正常；2-黑名单；
     */
    @Column(name = "sign_status")
    private Integer signStatus;

    /**
     * 黑名单类型（1.时间限制；2长期）
     */
    @Column(name = "black_type")
    private Integer blackType;

    /**
     * 限制天数
     */
    @Column(name = "restrict_num")
    private Integer restrictNum;

    /**
     * 限制开始时间（处理时间）
     */
    @Column(name = "restrict_start_time")
    private Date restrictStartTime;

    /**
     * 限制结束时间
     */
    @Column(name = "restrict_end_time")
    private Date restrictEndTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}