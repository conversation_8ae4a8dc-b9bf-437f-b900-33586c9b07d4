package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_agreement")
public class TytAgreement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 协议名称
     */
    private String name;

    /**
     * 协议链接
     */
    @Column(name = "link_url")
    private String linkUrl;

    /**
     * 展示类型 1.长期 2.周期
     */
    @Column(name = "show_type")
    private Short showType;

    /**
     * 周期展示开始时间
     */
    @Column(name = "show_start_time")
    private Date showStartTime;

    /**
     * 周期展示结束时间
     */
    @Column(name = "show_end_time")
    private Date showEndTime;

    /**
     * 展示端 0全部 1.车 2.货
     */
    @Column(name = "show_port")
    private Short showPort;

    /**
     * 优先级（数字越大越靠前）
     */
    private Integer sort;

    /**
     * 状态 1.停用 2.启用
     */
    private Short status;

    /**
     * 是否删除 0.已删除 1.有效
     */
    @Column(name = "is_valid")
    private Short isValid;

    private Date ctime;

    private Date mtime;

    /**
     * 操作人
     */
    private String operater;

    /**
     * 标签类型 1：规则中心 2：公告通知
     */
    @Column(name = "tab_type")
    private Integer tabType;
}