package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "lottery_record")
public class LotteryRecord {
    /**
     * 定向用户抽奖记录表id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户表id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户手机号（账号）
     */
    @Column(name = "user_call_phone")
    private String userCallPhone;

    /**
     * 活动表id（由奖项创建的活动）
     */
    @Column(name = "promo_activity_id")
    private Long promoActivityId;

    /**
     * 活动名称
     */
    @Column(name = "activity_name")
    private String activityName;

    /**
     * 活动表id（活动的类型）
     */
    @Column(name = "draw_activity_info_id")
    private Long drawActivityInfoId;

    /**
     * 活动与奖项关系表id
     */
    @Column(name = "prob_coupon_id")
    private Long probCouponId;

    /**
     * 奖项名字
     */
    @Column(name = "prob_coupon_name")
    private String probCouponName;

    /**
     * 奖品类型 1 实物   2 代金券
     */
    @Column(name = "award_type")
    private Byte awardType;

    /**
     * 奖券面值
     */
    @Column(name = "coupon_amount")
    private BigDecimal couponAmount;

    /**
     * 奖券过期时间（天）
     */
    @Column(name = "expiration_time")
    private Long expirationTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private Long createBy;

    /**
     * 修改人
     */
    @Column(name = "update_by")
    private Long updateBy;

    /**
     * 是否删除（0：删除，1：不删除）
     */
    @Column(name = "is_delete")
    private Byte isDelete;
}