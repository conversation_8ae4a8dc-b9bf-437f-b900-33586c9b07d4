package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_car_type_lincense")
public class TytCarTypeLincense {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 车型名称
     */
    @Column(name = "car_type")
    private String carType;

    /**
     * 车型名称2
     */
    @Column(name = "car_type_two")
    private String carTypeTwo;

    /**
     * 驾照类型
     */
    @Column(name = "license_type")
    private String licenseType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}