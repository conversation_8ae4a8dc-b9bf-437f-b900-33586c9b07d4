package com.tyt.plat.entity.base;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 货名管理白名单表
 *
 * <AUTHOR>
 * @since 2024/04/23 10:16
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "tyt_machine_type_whitelist")
public class TytMachineTypeWhitelist {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货物名称
     */
    @Column(name = "show_name")
    private String showName;

    /**
     * 货物类目
     */
    @Column(name = "top_class")
    private String topClass;

    /**
     * 货类
     */
    @Column(name = "second_class")
    private String secondClass;

    /**
     * 品牌
     */
    @Column(name = "brand_type")
    private String brandType;

    /**
     * 类型
     */
    @Column(name = "top_type")
    private String topType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 修改人
     */
    @Column(name = "modify_name")
    private String modifyName;
}
