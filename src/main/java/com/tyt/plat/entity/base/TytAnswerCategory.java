package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_answer_category")
public class TytAnswerCategory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 类型（1车，2货）
     */
    @Column(name = "belong_type")
    private Integer belongType;

    /**
     * 类目名称
     */
    @Column(name = "category_name")
    private String categoryName;

    /**
     * 图标地址
     */
    @Column(name = "icon_url")
    private String iconUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 排序
     */
    @Column(name = "sort_number")
    private Integer sortNumber;

    /**
     * 备注
     */
    private String remark;
}