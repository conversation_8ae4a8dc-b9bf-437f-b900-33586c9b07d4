package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "cs_complaint_record_result")
public class CsComplaintRecordResult {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 投诉记录id
     */
    @Column(name = "record_id")
    private Long recordId;

    /**
     * 是否是有效投诉 1不是 2是
     */
    @Column(name = "is_valid_complaint")
    private Integer isValidComplaint;

    /**
     * 责任方 1.货方责任 2.车方责任 3.责任不清 4.双方无责
     */
    @Column(name = "responsible_party")
    private Integer responsibleParty;

    /**
     * 判责原因
     */
    @Column(name = "wrong_reason")
    private Integer wrongReason;

    /**
     * 判责原因内容
     */
    @Column(name = "wrong_remark")
    private String wrongRemark;

    /**
     * 处理标准
     */
    @Column(name = "handle_standard")
    private Integer handleStandard;

    /**
     * 订金是否分配 1.否 2.是
     */
    @Column(name = "is_distribution")
    private Integer isDistribution;

    /**
     * 是否放空赔付 1.否 2.是
     */
    @Column(name = "is_compensation")
    private Integer isCompensation;

    /**
     * 是否补偿 1.否 2.是
     */
    @Column(name = "is_compensate")
    private Integer isCompensate;

    /**
     * 补偿分类 1.现金红包 2.减免券 3.无需分配
     */
    @Column(name = "compensate_type")
    private Integer compensateType;

    /**
     * 投诉分类一级
     */
    @Column(name = "complaint_qus_one")
    private Long complaintQusOne;

    /**
     * 投诉分类二级
     */
    @Column(name = "complaint_qus_two")
    private Long complaintQusTwo;

    /**
     * 投诉分类三级
     */
    @Column(name = "complaint_qus_three")
    private Long complaintQusThree;

    /**
     * 投诉分类四级
     */
    @Column(name = "complaint_qus_four")
    private Long complaintQusFour;

    private Date ctime;

    /**
     * 操作人
     */
    @Column(name = "op_name")
    private String opName;

    /**
     * 责任方违规： 1：否  2：违规   3：严重违规
     */
    @Column(name = "responsible_party_violation")
    private Integer responsiblePartyViolation;

    /**
     * 是否进行定金分配二级 1.平台分配  2.协商一致
     */
    @Column(name = "is_distribution_two")
    private Integer isDistributionTwo;

    /**
     * 责任方二级 1.货站责任  2.货主责任
     */
    @Column(name = "responsible_party_two")
    private Integer responsiblePartyTwo;
}