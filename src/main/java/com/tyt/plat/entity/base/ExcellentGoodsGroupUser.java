package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "excellent_goods_group_user")
public class ExcellentGoodsGroupUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    private Long groupId;

    /**
     * 0:删除 1：正常
     */
    @Column(name = "is_del")
    private Integer isDel;

    /**
     * 操作人id
     */
    @Column(name = "operate_id")
    private Long operateId;

    /**
     * 操作人名称
     */
    @Column(name = "operate_name")
    private String operateName;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;
}