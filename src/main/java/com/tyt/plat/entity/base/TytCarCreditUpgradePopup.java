package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_car_credit_upgrade_popup")
public class TytCarCreditUpgradePopup {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    /**
     * 是否已弹窗 0未弹窗 1已弹窗
     */
    @Column(name = "is_popup")
    private Integer isPopup;

    /**
     * 1 等级展示弹窗 2 成长等级升级弹窗（v1\v2 升V3）
     */
    @Column(name = "popup_type")
    private Integer popupType;

    /**
     * 弹窗时间
     */
    @Column(name = "popup_time")
    private Date popupTime;

    private Date ctime;
}