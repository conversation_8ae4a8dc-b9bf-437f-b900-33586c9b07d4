package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_signing_car_info")
public class TytSigningCarInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 签约车辆id
     */
    @Column(name = "signing_id")
    private Long signingId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 司机表id
     */
    @Column(name = "driver_id")
    private Long driverId;

    /**
     * 司机用户id
     */
    @Column(name = "driver_user_id")
    private Long driverUserId;

    /**
     * 司机手机号
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 好评率
     */
    @Column(name = "favorable_comment")
    private BigDecimal favorableComment;

    /**
     * 综合分值
     */
    @Column(name = "compre_fraction")
    private BigDecimal compreFraction;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 市区
     */
    private String city;

    /**
     * 归属调度
     */
    private String dispatch;

    /**
     * 仓库
     */
    private String county;

    /**
     * 车型
     */
    @Column(name = "car_type")
    private String carType;

    /**
     * 车头信息
     */
    @Column(name = "head_city_no")
    private String headCityNo;

    /**
     * 挂车信息
     */
    @Column(name = "tail_city_no")
    private String tailCityNo;

    /**
     * 挂车长度(米)
     */
    private String length;

    /**
     * 货台面高度（厘米）
     */
    @Column(name = "table_height")
    private String tableHeight;

    /**
     * 平台样式
     */
    @Column(name = "other_pure_flat")
    private String otherPureFlat;

    /**
     * 爬梯类型
     */
    @Column(name = "ladder_type")
    private String ladderType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 更新人
     */
    @Column(name = "update_name")
    private String updateName;
}