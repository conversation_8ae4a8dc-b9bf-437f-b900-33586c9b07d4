package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_user_upgrade_info")
public class TytUserUpgradeInfo {
    /**
     * 自增ID
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 升级表ID
     */
    @Column(name = "upgrade_table_id")
    private Long upgradeTableId;

    /**
     * 定向升级来源（1:客户端、2:PC 默认客户端）
     */
    @Column(name = "upgrade_source")
    private Byte upgradeSource;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 最近一次登陆版本
     */
    @Column(name = "last_login_version")
    private String lastLoginVersion;

    /**
     * 升级方式（0：提示升级；1：强制升级；2：不升级 默认不升级）
     */
    @Column(name = "upgrade_type")
    private Byte upgradeType;

    /**
     * 升级状态（0:未升级；1:已升级 默认0）
     */
    @Column(name = "upgrade_status")
    private Byte upgradeStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人userID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 创建人username
     */
    @Column(name = "create_username")
    private String createUsername;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}