package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_course_category")
public class TytCourseCategory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 类型（1车，2货）
     */
    private Integer type;

    /**
     * 类目名称
     */
    @Column(name = "category_name")
    private String categoryName;

    /**
     * 图标地址
     */
    @Column(name = "icon_url")
    private String iconUrl;

    /**
     * 有效状态(1有效;0已删除)
     */
    private Integer enable;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 排序
     */
    @Column(name = "sort_number")
    private Integer sortNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建管理员id
     */
    @Column(name = "create_man_id")
    private Long createManId;

    /**
     * 创建管理员名称
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 修改管理员id
     */
    @Column(name = "modify_man_id")
    private Long modifyManId;

    /**
     * 修改管理员名称
     */
    @Column(name = "modify_name")
    private String modifyName;
}