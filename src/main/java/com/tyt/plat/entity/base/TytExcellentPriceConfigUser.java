package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_excellent_price_config_user")
public class TytExcellentPriceConfigUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 优车2.0定价模型配置表id
     */
    @Column(name = "config_id")
    private Long configId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户系数
     */
    @Column(name = "user_coefficient")
    private BigDecimal userCoefficient;

    /**
     * 创建人id
     */
    @Column(name = "create_id")
    private Long createId;

    /**
     * 创建人名称
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "update_id")
    private Long updateId;

    /**
     * 修改人名称
     */
    @Column(name = "update_name")
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}