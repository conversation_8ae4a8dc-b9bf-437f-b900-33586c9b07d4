package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_price_up")
public class TytTransportPriceUp {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 原货源id(tyt_transport_main主键)
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 创建人id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 加价次数
     */
    @Column(name = "up_number")
    private Integer upNumber;

    /**
     * 运费加价金额（元）
     */
    @Column(name = "up_price")
    private BigDecimal upPrice;

    /**
     * 原价格（元）
     */
    @Column(name = "before_price")
    private BigDecimal beforePrice;

    /**
     * 加价后价格（元）
     */
    @Column(name = "after_price")
    private BigDecimal afterPrice;

    /**
     * 采集时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}