package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_fee_config")
public class TytFeeConfig {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 适应类型：0专车货源
     */
    @Column(name = "type")
    private String type;

    /**
     * 最小值 单位:元
     */
    @Column(name = "min_val")
    private BigDecimal minVal;

    /**
     * 单位 默认0金额 以元为单位
     */
    private Integer unit;

    /**
     * 最大值 单位:元
     */
    @Column(name = "max_val")
    private BigDecimal maxVal;

    /**
     * 比例 默认0-100中间代表0%到100%
     */
    @Column(name = "ratio")
    private BigDecimal ratio;

    /**
     * 排序顺序(按照值从小到大排序)
     */
    @Column(name = "sort_id")
    private Integer sortId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人用户ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 更新人用户ID
     */
    @Column(name = "update_user_id")
    private Long updateUserId;

    /**
     * 删除状态 0 未删除 1已删除
     */
    @Column(name = "del_status")
    private Integer delStatus;
}