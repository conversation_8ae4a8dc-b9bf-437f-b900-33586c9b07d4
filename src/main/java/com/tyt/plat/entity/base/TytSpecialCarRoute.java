package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_special_car_route")
public class TytSpecialCarRoute {
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * tyt_special_car表的id
     */
    @Column(name = "special_id")
    private Long specialId;

    /**
     * 开始城市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 目的地城市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}