package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_user_appeal_examine")
public class FeedbackUserAppealExamine implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 申诉表主键ID
     */
    @Column(name = "appeal_id")
    private Long appealId;

    /**
     * 处理意见
     */
    @Column(name = "action_opinion")
    private String actionOpinion;

    /**
     * 处理人ID
     */
    @Column(name = "action_user_id")
    private Long actionUserId;

    /**
     * 处理人姓名
     */
    @Column(name = "action_user_name")
    private String actionUserName;

    /**
     * 处理时间
     */
    @Column(name = "action_time")
    private Date actionTime;
}