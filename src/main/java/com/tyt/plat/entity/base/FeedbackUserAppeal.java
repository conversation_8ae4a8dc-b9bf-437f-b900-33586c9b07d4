package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_user_appeal")
public class FeedbackUserAppeal implements Serializable {

    public static final int APPEAL_STATUS_DOING = 1;
    public static final int APPEAL_STATUS_REJECT = 2;

    public static final int APPEAL_STATUS_SUCCESS = 3;

    public static final int APPEAL_STATUS_FAIL = 4;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 评价表主键ID
     */
    @Column(name = "feedback_id")
    private Long feedbackId;

    /**
     * 申诉状态：1-申诉中；2-已驳回；3-申诉成功；4-申诉失败；
     */
    @Column(name = "appeal_status")
    private Integer appealStatus;

    /**
     * 驳回原因（见字典表 feedback_appeal_reject_reason）
     */
    @Column(name = "reject_reason")
    private Integer rejectReason;

    /**
     * 申诉时间
     */
    @Column(name = "appeal_time")
    private Date appealTime;

    /**
     * 最新操作人用户ID
     */
    @Column(name = "action_user_id")
    private Long actionUserId;

    /**
     * 最新操作人用户姓名
     */
    @Column(name = "action_user_name")
    private String actionUserName;

    /**
     * 最新操作时间
     */
    @Column(name = "action_time")
    private Date actionTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}