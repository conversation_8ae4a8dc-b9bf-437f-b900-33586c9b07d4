package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_transport_price_perk_config")
public class TytTransportPricePerkConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 路线 出发地城市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 路线 目的地城市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 生效开始时间
     */
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 补贴比例
     */
    @Column(name = "perk_ratio")
    private Integer perkRatio;

    /**
     * 运距范围 最小值
     */
    @Column(name = "distance_min")
    private Integer distanceMin;

    /**
     * 运距范围 最大值
     */
    @Column(name = "distance_max")
    private Integer distanceMax;

    /**
     * 0禁用 1启用
     */
    private Integer status;

    /**
     * 删除状态 0否 1是
     */
    @Column(name = "delete_status")
    private Integer deleteStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;
}