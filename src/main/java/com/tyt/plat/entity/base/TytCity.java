package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_city")
public class TytCity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "area_code")
    private String areaCode;

    @Column(name = "parent_area_code")
    private String parentAreaCode;

    private String level;

    @Column(name = "map_code")
    private String mapCode;

    @Column(name = "area_name")
    private String areaName;

    @Column(name = "map_area_name")
    private String mapAreaName;

    @Column(name = "city_name")
    private String cityName;

    @Column(name = "map_city_name")
    private String mapCityName;

    @Column(name = "province_name")
    private String provinceName;

    @Column(name = "map_province_name")
    private String mapProvinceName;

    private String rf;

    private String px;

    private String py;

    private String longitude;

    private String latitude;

    @Column(name = "short_name")
    private String shortName;

    @Column(name = "standard_name")
    private String standardName;

    @Column(name = "map_longitude")
    private String mapLongitude;

    @Column(name = "map_latitude")
    private String mapLatitude;

    private String type;

    @Column(name = "create_time")
    private Date createTime;
}