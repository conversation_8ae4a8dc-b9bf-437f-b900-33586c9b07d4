package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_cover_goods_dial_config")
public class TytCoverGoodsDialConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 是否删除(0未删除，1已删除)
     */
    @Column(name = "del_flag")
    private Short delFlag;

    /**
     * 操作人ID
     */
    @Column(name = "operate_user_id")
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    @Column(name = "operate_user_name")
    private String operateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 配置名
     */
    private String name;

    /**
     * 发货倒计时
     */
    @Column(name = "x_time_in_seconds")
    private Integer xTimeInSeconds;

    /**
     * 详情倒计时
     */
    @Column(name = "y_time_in_seconds")
    private Integer yTimeInSeconds;

    /**
     * 免责次数
     */
    @Column(name = "n_times")
    private Integer nTimes;

    /**
     * 启用标识 0未启用，1启用
     */
    private Boolean enable;

    /**
     * 免责卡发放次数
     */
    @Column(name = "grant_n_times")
    private Integer grantNTimes;
}