package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_cover_goods_beans_config_user")
public class TytCoverGoodsBeansConfigUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户名
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 抢单豆总个数
     */
    @Column(name = "total_num")
    private Integer totalNum;

    /**
     * 抢单豆使用个数
     */
    @Column(name = "used_num")
    private Integer usedNum;

    /**
     * 抢单豆剩余个数
     */
    @Column(name = "left_num")
    private Integer leftNum;

    /**
     * 抢单豆发放名称
     */
    @Column(name = "beans_name")
    private String beansName;

    /**
     * 抢单豆有效期
     */
    @Column(name = "validate_time")
    private Date validateTime;

    /**
     * 操作人ID
     */
    @Column(name = "operate_user_id")
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    @Column(name = "operate_user_name")
    private String operateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 抢单豆状态，1-有效，2-无效-用完，3-无效-到期
     */
    @Column(name = "beans_status")
    private Integer beansStatus;

    /**
     * 捂货规则配置表id
     */
    @Column(name = "dial_config_id")
    private Long dialConfigId;
}