package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_user_wifi_log")
public class TytUserWifiLog {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 终端分类 31：车App  32：货App
     */
    private String type;

    /**
     * WiFi名称
     */
    @Column(name = "wifi_name")
    private String wifiName;

    /**
     * 上报时间
     */
    @Column(name = "escalation_time")
    private Date escalationTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}