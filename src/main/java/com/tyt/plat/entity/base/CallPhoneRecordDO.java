package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "call_phone_record")
public class CallPhoneRecordDO {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 拨打人id
     */
    @Column(name = "car_user_id")
    private Long carUserId;

    /**
     * 拨打人姓名
     */
    @Column(name = "car_user_name")
    private String carUserName;

    /**
     * 是否是车方vip 1.是2.否
     */
    @Column(name = "car_is_vip")
    private Short carIsVip;

    /**
     * 路径
     */
    private String path;

    /**
     * 模块
     */
    private String module;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 客户端标识
     */
    @Column(name = "plat_id")
    private String platId;
}