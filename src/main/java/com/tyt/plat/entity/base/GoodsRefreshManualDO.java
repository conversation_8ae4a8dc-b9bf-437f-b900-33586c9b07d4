package com.tyt.plat.entity.base;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <p>
 * 货源刷新手动曝光表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Getter
@Setter
@Table(name = "tyt_goods_refresh_manual")
public class GoodsRefreshManualDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 发货人id
     */
    private Long userId;

    /**
     * 刷新间隔
     */
    private Integer refreshInterval;

    /**
     * 刷新总次数
     */
    private Integer totalTimes;

    /**
     * 已刷新次数
     */
    private Integer refreshTimes;

    /**
     * 剩余刷新次数，=刷新总次数-已刷新次数
     */
    private Integer leftTimes;

    /**
     * 刷新条目值，填运费1，转一口价2，补充目的地4，多个符合就相加
     */
    private Integer itemValue;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date modifyTime;
}
