package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "exposure_permission_expired_record")
public class ExposurePermissionExpiredRecord {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 过期前剩余次数
     */
    @Column(name = "expired_before_num")
    private Integer expiredBeforeNum;

    /**
     * 过期次数
     */
    @Column(name = "expired_num")
    private Integer expiredNum;

    /**
     * 过期后剩余次数
     */
    @Column(name = "expired_after_num")
    private Integer expiredAfterNum;

    /**
     * 过期时间
     */
    @Column(name = "expired_time")
    private Date expiredTime;
}