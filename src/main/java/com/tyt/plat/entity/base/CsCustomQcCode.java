package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "cs_custom_qc_code")
public class CsCustomQcCode {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 客户id
     */
    @Column(name = "custom_id")
    private Long customId;

    /**
     * 维护人姓名
     */
    @Column(name = "maintainer_name")
    private String maintainerName;

    /**
     * 类型： 1：车  2：货
     */
    private Integer type;

    /**
     * 城市
     */
    @Column(name = "city")
    private String city;

    /**
     * 礼品
     */
    @Column(name = "gift")
    private String gift;

    /**
     * 自定义
     */
    @Column(name = "self_define")
    private String selfDefine;


    /**
     * 客户宣传页下载路径
     */
    @Column(name = "qc_code_path")
    private String qcCodePath;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}