package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "owner_company_log")
public class OwnerCompanyLog {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 企业id
     */
    @Column(name = "enterprise_id")
    private Long enterpriseId;

    /**
     * 货站id
     */
    @Column(name = "company_id")
    private Long companyId;

    /**
     * 企业货源id
     */
    @Column(name = "backend_id")
    private Long backendId;

    /**
     * 平台货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 企业货源订单号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 原状态
     */
    private Integer status;

    /**
     * 原订单流转状态
     */
    @Column(name = "order_status")
    private Integer orderStatus;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}