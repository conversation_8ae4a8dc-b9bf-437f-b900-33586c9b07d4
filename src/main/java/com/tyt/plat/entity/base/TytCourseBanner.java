package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_course_banner")
public class TytCourseBanner {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 类型（1车，2货）
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 图片地址
     */
    @Column(name = "img_url")
    private String imgUrl;

    /**
     * 链接地址
     */
    @Column(name = "link_url")
    private String linkUrl;

    /**
     * 有效状态(1有效;0已删除)
     */
    private Integer enable;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 排序
     */
    @Column(name = "sort_number")
    private Integer sortNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建管理员id
     */
    @Column(name = "create_man_id")
    private Long createManId;

    /**
     * 创建管理员名称
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 修改管理员id
     */
    @Column(name = "modify_man_id")
    private Long modifyManId;

    /**
     * 修改管理员名称
     */
    @Column(name = "modify_name")
    private String modifyName;
}