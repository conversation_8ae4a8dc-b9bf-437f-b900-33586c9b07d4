package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_cover_goods_config")
public class TytCoverGoodsConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 配置明细  configCode为1时：  1：每次发货默认勾选，2：每次发货默认不勾选 3：默认勾选，记录用户操作状态;configCode为2时 0：关  1：开
     */
    @Column(name = "config_item")
    private Integer configItem;

    /**
     * 配置类型标识;1：勾选配置  2：引导配置 3：优货捂货配置
     */
    @Column(name = "config_code")
    private Integer configCode;

    /**
     * 启用状态 0：禁用 1：启用
     */
    private Integer enable;

    /**
     * 配置额外信息，configCode不同，信息不同
     * 如configCode=4是引导文案
     */
    @Column(name = "config_ext")
    private String configExt;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 修改人
     */
    @Column(name = "modify_name")
    private String modifyName;
}