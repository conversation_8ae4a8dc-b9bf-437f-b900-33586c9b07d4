package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_intelligent_deposit_user_record")
public class TytIntelligentDepositUserRecord {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 默认填充订金
     */
    @Column(name = "default_deposit")
    private BigDecimal defaultDeposit;

    /**
     * 默认退还方式
     */
    @Column(name = "default_refund_flag")
    private Integer defaultRefundFlag;

    /**
     * 用户提交的订金
     */
    @Column(name = "user_deposit")
    private BigDecimal userDeposit;

    /**
     * 用户提交的退还方式
     */
    @Column(name = "user_refund_flag")
    private Integer userRefundFlag;

    /**
     * 创建时间
     */
    @Column(name = "creat_time")
    private Date creatTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}