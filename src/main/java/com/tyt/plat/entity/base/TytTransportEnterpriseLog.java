package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_enterprise_log")
public class TytTransportEnterpriseLog {
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    private Long enterpriseId;

    /**
     * 企业名称
     */
    @Column(name = "enterprise_name")
    private String enterpriseName;

    /**
     * 企业法人姓名
     */
    @Column(name = "legal_person_name")
    private String legalPersonName;

    /**
     * 企业法人手机号
     */
    @Column(name = "legal_person_phone")
    private String legalPersonPhone;

    /**
     * 企业法人身份证号码
     */
    @Column(name = "legal_person_card")
    private String legalPersonCard;

    /**
     * 法人身份证URL国徽面
     */
    @Column(name = "legal_person_card_url_g")
    private String legalPersonCardUrlG;

    /**
     * 法人身份证URL头像面
     */
    @Column(name = "legal_person_card_url_t")
    private String legalPersonCardUrlT;

    /**
     * 企业认证统一社会信用代码
     */
    @Column(name = "enterprise_credit_code")
    private String enterpriseCreditCode;

    /**
     * 企业类型
     */
    @Column(name = "enterprise_type")
    private String enterpriseType;

    /**
     * 企业经营范围
     */
    @Column(name = "enterprise_business_scope")
    private String enterpriseBusinessScope;

    /**
     * 企业归属地
     */
    @Column(name = "enterprise_home_address")
    private String enterpriseHomeAddress;

    /**
     * 企业注册地址
     */
    @Column(name = "enterprise_detail_address")
    private String enterpriseDetailAddress;

    /**
     * 营业执照URL
     */
    @Column(name = "license_url")
    private String licenseUrl;

    /**
     * 营业执照开始时间
     */
    @Column(name = "license_start_time")
    private Date licenseStartTime;

    /**
     * 营业执照到期时间
     */
    @Column(name = "license_end_time")
    private Date licenseEndTime;

    /**
     * 道路经营许可证照片URL
     */
    @Column(name = "transport_license_url")
    private String transportLicenseUrl;

    /**
     * 授权激活类型（1法人授权;2委托书授权）
     */
    @Column(name = "sign_type")
    private Integer signType;

    /**
     * 协议编号(同contract_number)
     */
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 协议开始时间
     */
    @Column(name = "contract_start_time")
    private Date contractStartTime;

    /**
     * 协议结束时间
     */
    @Column(name = "contract_end_time")
    private Date contractEndTime;

    /**
     * 授权人用户ID
     */
    @Column(name = "certigier_user_id")
    private Long certigierUserId;

    /**
     * 授权人姓名
     */
    @Column(name = "certigier_user_name")
    private String certigierUserName;

    /**
     * 授权人手机号（平台账号）
     */
    @Column(name = "certigier_user_phone")
    private String certigierUserPhone;

    /**
     * 企业发票税率
     */
    @Column(name = "enterprise_tax_rate")
    private BigDecimal enterpriseTaxRate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 开票主体ID，0表示本公司开票主体，其他的表示三方开票主体
     */
    @Column(name = "invoice_subject_id")
    private Long invoiceSubjectId;

    /**
     * 服务商code
     */
    private String serviceProviderCode;

    /**
     * 指派车方手机号
     */
    private String assignCarTel;

    /**
     * XHL开票-收货人姓名
     */
    @Column(name = "consignee_name")
    private String consigneeName;

    /**
     * XHL开票-收货人联系方式
     */
    @Column(name = "consignee_tel")
    private String consigneeTel;

    /**
     * XHL开票-收货人企业名称
     */
    @Column(name = "consignee_enterprise_name")
    private String consigneeEnterpriseName;
    /**
     * 预付金额
     */
    @Column(name = "prepaid_price")
    private BigDecimal prepaidPrice;
    /**
     * 到付金额
     */
    @Column(name = "collected_price")
    private BigDecimal collectedPrice;
    /**
     * 回单付金额
     */
    @Column(name = "receipt_price")
    private BigDecimal receiptPrice;
    /**
     *0：全额支付  1：分段支付
     */
    @Column(name = "payments_type")
    private Integer paymentsType;

}