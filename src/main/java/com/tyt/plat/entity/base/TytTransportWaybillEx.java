package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_waybill_ex")
public class TytTransportWaybillEx {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 异常上报类型 参见 source 表 exec_party_car exec_party_goods 组
     */
    @Id
    @Column(name = "ex_type")
    private String exType;

    /**
     * 出发地(省市区以减号-分割开)
     */
    @Column(name = "start_point")
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    @Column(name = "dest_point")
    private String destPoint;

    /**
     * 货物内容
     */
    @Column(name = "task_content")
    private String taskContent;

    /**
     * 联系人
     */
    private String tel;

    /**
     * 发布时间
     */
    @Column(name = "pub_time")
    private String pubTime;

    /**
     * 采集时间
     */
    private Date ctime;

    /**
     * 上传电话
     */
    @Column(name = "upload_cellphone")
    private String uploadCellphone;

    /**
     * 发布人usreID 关联tyt_user表id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系人电话3
     */
    private String tel3;

    /**
     * 联系人电话4
     */
    private String tel4;

    /**
     * 运单号
     */
    @Column(name = "ts_order_no")
    private String tsOrderNo;

    /**
     * 车主user_id
     */
    @Column(name = "pay_user_id")
    private Long payUserId;

    /**
     * 车主注册账号
     */
    @Column(name = "pay_cell_phone")
    private String payCellPhone;

    /**
     * 车主联系电话
     */
    @Column(name = "pay_link_phone")
    private String payLinkPhone;

    /**
     * 支付金额
     */
    @Column(name = "pay_amount")
    private Long payAmount;

    /**
     * 异常上报时间
     */
    @Column(name = "ex_time")
    private Date exTime;

    /**
     * 异常上报方1车主上报，2货主上报
     */
    @Column(name = "ex_party")
    private String exParty;

    /**
     * 异常上报类型(编辑) 参见source表 exec_party_car exec_party_goods 组
     */
    @Column(name = "ex_type_other")
    private String exTypeOther;

    /**
     * 责任方 参见source表 ex_fault_party组
     */
    @Column(name = "ex_fault_party")
    private String exFaultParty;

    /**
     * 上报类型其他原因
     */
    @Column(name = "ex_other")
    private String exOther;

    /**
     * 异常上报处理状态0初始化1处理中2处理完成3自行处理
     */
    @Column(name = "ex_status")
    private String exStatus;

    /**
     * 处理完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * 最终意见
     */
    @Column(name = "result_opinion")
    private String resultOpinion;

    /**
     * 处理结果 车主金额单位分
     */
    @Column(name = "car_amount")
    private Long carAmount;

    /**
     * 处理结果 货主金额单位分
     */
    @Column(name = "goods_amount")
    private Long goodsAmount;

    /**
     * 操作人
     */
    @Column(name = "action_user_id")
    private Long actionUserId;

    /**
     * 操作人账号
     */
    @Column(name = "action_cell_phone")
    private String actionCellPhone;

    /**
     * 操作人姓名
     */
    @Column(name = "action_name")
    private String actionName;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 货源信息ID
     */
    @Column(name = "ts_id")
    private Long tsId;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 最新处理意见
     */
    @Column(name = "latest_opinion")
    private String latestOpinion;

    /**
     * 状态0有效1是无效
     */
    private Integer status;

    /**
     * 订单类型：0.异常上报订单  1.投诉订单
     */
    @Column(name = "order_type")
    private Integer orderType;
}
