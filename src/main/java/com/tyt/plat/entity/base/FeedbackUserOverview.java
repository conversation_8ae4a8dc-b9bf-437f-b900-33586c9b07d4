package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_user_overview")
public class FeedbackUserOverview {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 用户类型  1车 2货
     */
    @Column(name = "user_type")
    private Short userType;

    /**
     * 发表的好评数
     */
    @Column(name = "posted_positive_feedback_num")
    private Integer postedPositiveFeedbackNum;

    /**
     * 发表的中评数
     */
    @Column(name = "posted_neutral_feedback_num")
    private Integer postedNeutralFeedbackNum;

    /**
     * 发表的差评数
     */
    @Column(name = "posted_negative_feedback_num")
    private Integer postedNegativeFeedbackNum;

    /**
     * 发表的评论数
     */
    @Column(name = "posted_feedback_num")
    private Integer postedFeedbackNum;

    /**
     * 收到的好评数
     */
    @Column(name = "received_positive_feedback_num")
    private Integer receivedPositiveFeedbackNum;

    /**
     * 收到的中评数
     */
    @Column(name = "received_neutral_feedback_num")
    private Integer receivedNeutralFeedbackNum;

    /**
     * 收到的差评数
     */
    @Column(name = "received_negative_feedback_num")
    private Integer receivedNegativeFeedbackNum;

    /**
     * 收到的评论数
     */
    @Column(name = "received_feedback_num")
    private Integer receivedFeedbackNum;

    /**
     * 收到评价的好评率
     */
    @Column(name = "received_positive_feedback_rating")
    private Integer receivedPositiveFeedbackRating;
}