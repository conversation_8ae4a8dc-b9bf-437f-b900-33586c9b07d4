package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_car_owner_auth_proof")
public class TytCarOwnerAuthProof {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 车主认证表id(tyt_car_owner_auth)
     */
    @Column(name = "car_owner_auth_id")
    private Long carOwnerAuthId;

    /**
     * 认证材料url
     */
    @Column(name = "certificate_material_url")
    private String certificateMaterialUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 0 删除 1未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;
}