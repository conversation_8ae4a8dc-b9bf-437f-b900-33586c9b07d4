package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_accident_record")
public class TytAccidentRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 主类型：1.出发地和到达地的x和y轴匹配有误
     */
    @Column(name = "primitive_type")
    private String primitiveType;

    /**
     * 子类型
     */
    private String subtype;

    /**
     * 问题描述
     */
    private String remake;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 数据样本json
     */
    @Column(name = "data_sample_json")
    private String dataSampleJson;
}