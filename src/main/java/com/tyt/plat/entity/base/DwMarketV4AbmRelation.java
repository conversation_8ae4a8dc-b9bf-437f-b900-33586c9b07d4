package com.tyt.plat.entity.base;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "dw_market_v4_abm_relation")
public class DwMarketV4AbmRelation {
    /**
     * 经销商id
     */
    @Column(name = "a_id")
    private Integer aId;

    /**
     * 品牌id
     */
    @Column(name = "b_id")
    private Integer bId;

    /**
     * 代理车型id
     */
    @Column(name = "m_id")
    private Integer mId;
}