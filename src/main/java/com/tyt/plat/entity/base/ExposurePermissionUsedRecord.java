package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "exposure_permission_used_record")
public class ExposurePermissionUsedRecord implements Serializable {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 出发地(省市区以减号-分割开)
     */
    @Column(name = "start_point")
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    @Column(name = "dest_point")
    private String destPoint;

    /**
     * 货物内容
     */
    @Column(name = "task_content")
    private String taskContent;

    /**
     * 使用时间
     */
    private Date ctime;
}