package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import javax.persistence.*;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_draw_commission_rule")
public class TytDrawCommissionRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 抽佣渠道  -1:不限，1：普货 + 优车,3优车定价,4运满满,5专车
     */
    @Column(name = "commission_source")
    private Integer commissionSource;

    /**
     * 货源类型（-1:不限，电议1，一口价2）
     */
    @Column(name = "publish_type")
    private Integer publishType;

    /**
     * 有无价格（-1:不限，0无价，1有价）
     */
    @Column(name = "have_price")
    private Integer havePrice;

    /**
     * 订金是否退还（-1:不限，0不退还；1退还）
     */
    @Column(name = "refund_flag")
    private Integer refundFlag;

    /**
     * 抽佣开始日期
     */
    @Column(name = "start_date")
    private Date startDate;

    /**
     * 抽佣结束日期
     */
    @Column(name = "end_date")
    private Date endDate;

    /**
     * 每天开始时间点
     */
    @Column(name = "daily_start")
    private Time dailyStart;

    /**
     * 每天结束时间点
     */
    @Column(name = "daily_end")
    private Time dailyEnd;

    /**
     * 抽佣货源上限
     */
    @Column(name = "commission_max_count")
    private Integer commissionMaxCount;

    /**
     * 抽取规则 0:全量抽取 1：隔条抽取
     */
    private Integer rule;

    /**
     * 0：禁用 1：启用
     */
    private Integer enabled;

    /**
     * 是否接入好货模型分数 0：否 1：是
     */
    @Column(name = "good_transport")
    private Integer goodTransport;

    /**
     * 好货分数线最小值
     */
    @Column(name = "min_fractional_line")
    private BigDecimal minFractionalLine;

    /**
     * 好货分数线最大值
     */
    @Column(name = "max_fractional_line")
    private BigDecimal maxFractionalLine;

    /**
     * 创建人
     */
    @Column(name = "create_id")
    private Long createId;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "modify_id")
    private Long modifyId;

    /**
     * 修改人
     */
    @Column(name = "modify_name")
    private String modifyName;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}