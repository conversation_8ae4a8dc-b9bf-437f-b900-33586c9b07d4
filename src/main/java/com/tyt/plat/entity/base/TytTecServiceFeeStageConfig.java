package com.tyt.plat.entity.base;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "tyt_tec_service_fee_stage_config")
public class TytTecServiceFeeStageConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 抽佣货源技术服务费配置主表ID
     */
    @Column(name = "config_id")
    private Long configId;

    /**
     * 运费低值
     */
    @Column(name = "price_min")
    private BigDecimal priceMin;

    /**
     * 运费高值
     */
    @Column(name = "price_max")
    private BigDecimal priceMax;

    /**
     * 技术服务费抽取运费百分比
     */
    @Column(name = "tec_service_fee_rate")
    private BigDecimal tecServiceFeeRate;

    /**
     * 技术服务费低值
     */
    @Column(name = "tec_service_fee_min")
    private BigDecimal tecServiceFeeMin;

    /**
     * 技术服务费高值
     */
    @Column(name = "tec_service_fee_max")
    private BigDecimal tecServiceFeeMax;

    /**
     * 限时折扣（最终折扣），0-10
     */
    private BigDecimal discount;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 类型，1:运费阶梯；2：抽佣分数阶梯
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 二级配置（抽佣百分比）
     */
    List<TytTecServiceFeeProportionConfig> tytTecServiceFeeProportionConfigList;

}