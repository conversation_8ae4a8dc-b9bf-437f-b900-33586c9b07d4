package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_special_car_contact_record")
public class TytSpecialCarContactRecord {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 员工ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 员工姓名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 沟通状态：1-首次响应，2-再次跟进，3-工单结单
     */
    @Column(name = "contact_status")
    private Short contactStatus;

    /**
     * 工单状态：0-待处理，1-处理中，2-已结单
     */
    @Column(name = "work_order_status")
    private Short workOrderStatus;

    /**
     * 货源状态：0-无效（已过期），1-有效（发布中），4-成交，5-取消状态
     */
    @Column(name = "goods_status")
    private Short goodsStatus;

    /**
     * 是否变更发货方式：0-否，1-是
     */
    @Column(name = "is_change")
    private Short isChange;

    /**
     * 联系人角色
     */
    @Column(name = "contact_role")
    private String contactRole;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 沟通内容
     */
    @Column(name = "contact_content")
    private String contactContent;

    /**
     * 自动派单失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 变更发货方式原因
     */
    @Column(name = "change_reason")
    private String changeReason;

    /**
     * 货源成交原因
     */
    @Column(name = "deal_reason")
    private String dealReason;

    /**
     * 货源未成交原因
     */
    @Column(name = "not_deal_reason")
    private String notDealReason;

    /**
     * 结单备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}