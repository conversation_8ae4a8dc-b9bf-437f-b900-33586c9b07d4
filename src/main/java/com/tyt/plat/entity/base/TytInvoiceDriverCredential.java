package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_invoice_driver_credential")
public class TytInvoiceDriverCredential {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 司机id
     */
    @Column(name = "invoice_driver_id")
    private Long invoiceDriverId;

    /**
     * 图片类型(1 身份证正面、2 身份证反面、3 驾驶证正面、4 驾驶证反面、 5 从业资格证)
     */
    @Column(name = "pic_type")
    private Integer picType;

    /**
     * 图片url
     */
    @Column(name = "pic_url")
    private String picUrl;

    /**
     * 认证状态(0 认证中、1 认证成功、2 认证失败)
     */
    @Column(name = "verify_status")
    private Integer verifyStatus;

    /**
     * 认证结果描述
     */
    @Column(name = "verify_desc")
    private String verifyDesc;

    /**
     * orc结果 json
     */
    @Column(name = "orc_json")
    private String orcJson;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}