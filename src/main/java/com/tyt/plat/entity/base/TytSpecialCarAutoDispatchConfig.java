package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_special_car_auto_dispatch_config")
public class TytSpecialCarAutoDispatchConfig {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 出发地城市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 目的地城市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 车辆距离出发地小于等于X公里进行派单
     */
    @Column(name = "distance_limit")
    private Integer distanceLimit;

    /**
     * 司机好评率
     */
    @Column(name = "favor_rate")
    private BigDecimal favorRate;

    /**
     * 司机接单率
     */
    @Column(name = "receive_rate")
    private BigDecimal receiveRate;

    /**
     * 派单方式：1-同时指派，2-先后指派
     */
    @Column(name = "dispatch_type")
    private Integer dispatchType;

    /**
     * 最多指派司机数量
     */
    @Column(name = "max_dispatch_num")
    private Integer maxDispatchNum;

    /**
     * 先后指派司机x分钟未接单指派下一位
     */
    @Column(name = "after_minutes")
    private Integer afterMinutes;

    /**
     * 状态：1-启用，2-禁用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 操作人ID
     */
    @Column(name = "op_user_id")
    private Long opUserId;

    /**
     * 操作人姓名
     */
    @Column(name = "op_user_name")
    private String opUserName;

    /**
     * 删除状态：0-正常，1-已删除
     */
    @Column(name = "del_status")
    private Integer delStatus;
}