package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 黑名单用户订单拉黑日志表实体类
 * blacklist_user_orders_log
 *
 * <AUTHOR>
 * @date 2023-10-9 10:07:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlacklistUserOrdersLog implements Serializable{

	/**
	 * id
	 */
	private Long id;
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 异常上报ID
	 */
	private Long waybillExId;
	/**
	 * 运单号
	 */
	private String tsOrderNo;
	/**
	 * 操作角色（1-货方；2-车方；）
	 */
	private Integer actionRole;
	/**
	 * 拉黑操作类型（1-新增；2-增加；3-减少；4-解除；）
	 */
	private Integer restrictActionType;
	/**
	 * 拉黑天数（-1为永久拉黑）
	 */
	private Integer restrictNum;
	/**
	 * 限制找货操作类型（1-新增；2-增加；3-减少；4-解除；）
	 */
	private Integer carRestrictActionType;
	/**
	 * 限制找货天数（-1为永久限制）
	 */
	private Integer carRestrictNum;
	/**
	 * 限制发货操作类型（1-新增；2-增加；3-减少；4-解除；）
	 */
	private Integer goodsRestrictActionType;
	/**
	 * 限制找货天数（-1为永久限制）
	 */
	private Integer goodsRestrictNum;
	/**
	 * 限制找货/发货原因
	 */
	private String restrictCause;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 操作人ID
	 */
	private Long operaUserId;
	/**
	 * 操作人名称
	 */
	private String operaUserName;
	/**
	 * 创建时间
	 */
	private Date createTime;
}
