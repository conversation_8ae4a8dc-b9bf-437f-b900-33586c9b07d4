package com.tyt.plat.entity.base;

import java.math.BigDecimal;

public class TytInvoiceEnterpriseLog extends TytInvoiceEnterprise{

    private Long invoiceSubjectId;

    private String serviceProviderCode;

    private String assignCarTel;

    /**
     * XHL开票-收货人姓名
     */
    private String consigneeName;

    /**
     * XHL开票-收货人联系方式
     */
    private String consigneeTel;

    /**
     * XHL开票-收货人企业名称
     */
    private String consigneeEnterpriseName;

    /**
     * 预付金额
     */
    private BigDecimal prepaidPrice;
    /**
     * 到付金额
     */
    private BigDecimal collectedPrice;
    /**
     * 回单付金额
     */
    private BigDecimal receiptPrice;
    /**
     * 回单付金额
     */
    private Integer paymentsType;


    public Long getInvoiceSubjectId() {
        return invoiceSubjectId;
    }

    public void setInvoiceSubjectId(Long invoiceSubjectId) {
        this.invoiceSubjectId = invoiceSubjectId;
    }

    public String getServiceProviderCode() {
        return serviceProviderCode;
    }

    public void setServiceProviderCode(String serviceProviderCode) {
        this.serviceProviderCode = serviceProviderCode;
    }

    public String getAssignCarTel() {
        return assignCarTel;
    }

    public void setAssignCarTel(String assignCarTel) {
        this.assignCarTel = assignCarTel;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getConsigneeTel() {
        return consigneeTel;
    }

    public void setConsigneeTel(String consigneeTel) {
        this.consigneeTel = consigneeTel;
    }

    public String getConsigneeEnterpriseName() {
        return consigneeEnterpriseName;
    }

    public void setConsigneeEnterpriseName(String consigneeEnterpriseName) {
        this.consigneeEnterpriseName = consigneeEnterpriseName;
    }

    public BigDecimal getPrepaidPrice() {
        return prepaidPrice;
    }

    public BigDecimal getCollectedPrice() {
        return collectedPrice;
    }

    public void setCollectedPrice(BigDecimal collectedPrice) {
        this.collectedPrice = collectedPrice;
    }

    public void setPrepaidPrice(BigDecimal prepaidPrice) {
        this.prepaidPrice = prepaidPrice;
    }

    public BigDecimal getReceiptPrice() {
        return receiptPrice;
    }

    public void setReceiptPrice(BigDecimal receiptPrice) {
        this.receiptPrice = receiptPrice;
    }

    public Integer getPaymentsType() {
        return paymentsType;
    }

    public void setPaymentsType(Integer paymentsType) {
        this.paymentsType = paymentsType;
    }
}