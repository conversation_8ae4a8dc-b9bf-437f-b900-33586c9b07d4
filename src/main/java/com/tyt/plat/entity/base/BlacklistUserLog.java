package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 黑名单拉黑记录表实体类
 * blacklist_user_log
 *
 * <AUTHOR>
 * @date 2024-4-19 14:42:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "blacklist_user_log")
public class BlacklistUserLog implements Serializable{
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(name = "user_id")
	private Long userId; //用户id
	@Column(name = "user_name")
	private String userName; //用户名称
	@Column(name = "idcard")
	private String idcard; //身份证号
	@Column(name = "cell_phone")
	private String cellPhone; //手机号
	@Column(name = "status")
	private Integer status; //状态，0-不是黑名单，1-是黑名单
	@Column(name = "cause")
	private Integer cause; //发货限制原因,参考tyt_source 配置中
	/**
	 *
	 * 是否永久（0-否；1-是；）
	 */
	@Column(name = "perpetual")
	private Integer perpetual;
	/**
	 * 拉黑天数
	 */
	@Column(name = "restrict_num")
	private Integer restrictNum;
	/**
	 * 拉黑开始时间
	 */
	@Column(name = "restrict_start_time")
	private Date restrictStartTime;
	/**
	 * 拉黑结束时间
	 */
	@Column(name = "restrict_end_time")
	private Date restrictEndTime;
	@Column(name = "ext_cause")
	private String extCause; //统一备注
	@Column(name = "rm_cause")
	private String rmCause; //移除黑名单理由
	@Column(name = "limit_start_time")
	private Date limitStartTime; // 发货限制开始时间
	@Column(name = "limit_end_time")
	private Date limitEndTime; // 发货限制结束时间
	@Column(name = "opera_user_id")
	private Long operaUserId; //操作人Id
	@Column(name = "opera_user_name")
	private String operaUserName; //操作人用户名
	@Column(name = "ctime")
	private Date ctime; //创建时间
	@Column(name = "mtime")
	private Date mtime; //更新时间
	@Column(name = "car_cause")
	private Integer carCause; //找货限制原因,参考tyt_source 配置中
	@Column(name = "car_limit_start_time")
	private Date carLimitStartTime; // 找货限制开始时间
	@Column(name = "car_limit_end_time")
	private Date carLimitEndTime; // 找货限制结束时间
	@Column(name = "car_limit_minutes")
	private Integer carLimitMinutes; //找货限制分钟数
	@Column(name = "limit_minutes")
	private Integer limitMinutes; //发货限制分钟数

	/**
	 * 找货限制原因(多级原因使用,拼接)
	 */
	@Column(name = "car_limit_reason")
	private String carLimitReason;

	/**
	 * 找货限制原因名称(多级原因使用,拼接)
	 */
	@Column(name = "car_limit_reason_name")
	private String carLimitReasonName;

	/**
	 * 发货限制原因(多级原因使用,拼接)
	 */
	@Column(name = "limit_reason")
	private String limitReason;

	/**
	 * 发货限制原因名称(多级原因使用,拼接)
	 */
	@Column(name = "limit_reason_name")
	private String limitReasonName;
}
