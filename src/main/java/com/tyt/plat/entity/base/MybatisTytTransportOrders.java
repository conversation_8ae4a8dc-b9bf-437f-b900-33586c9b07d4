package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_orders")
public class MybatisTytTransportOrders {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 出发地(省市区以减号-分割开)
     */
    @Column(name = "start_point")
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    @Column(name = "dest_point")
    private String destPoint;

    /**
     * 货物内容
     */
    @Column(name = "task_content")
    private String taskContent;

    /**
     * 联系人
     */
    private String tel;

    /**
     * 发布时间
     */
    @Column(name = "pub_time")
    private String pubTime;

    /**
     * 采集时间
     */
    private Date ctime;

    /**
     * 上传电话
     */
    @Column(name = "upload_cellphone")
    private String uploadCellphone;

    /**
     * 发布人usreID 关联tyt_user表id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 发布人昵称
     */
    @Column(name = "pub_user_name")
    private String pubUserName;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系人电话3
     */
    private String tel3;

    /**
     * 联系人电话4
     */
    private String tel4;

    /**
     * sort_id 用于增量同步数据
     */
    @Column(name = "sort_id")
    private Long sortId;

    /**
     * 运单号
     */
    @Column(name = "ts_order_no")
    private String tsOrderNo;

    /**
     * 三方平台类型 0 特运通  1:满帮
     */
    @Column(name = "thirdparty_platform_type")
    private Integer thirdpartyPlatformType;

    /**
     * 三方平台单号
     */
    @Column(name = "thirdparty_platform_order_no")
    private String thirdpartyPlatformOrderNo;

    /**
     * 货源信息ID
     */
    @Column(name = "ts_id")
    private Long tsId;

    /**
     * 关联订单号(用于与tyt_old_order表关联,而非ts_order_no)
     */
    @Column(name = "pay_order_no")
    private String payOrderNo;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    @Column(name = "refund_flag")
    private Integer refundFlag;

    /**
     *  预计退款到账日期
     */
    @Column(name = "de_refund_dueDate")
    private Date deRefundDuedate;

    /**
     * 0 未延迟退款 1 延迟退款 
     */
    @Column(name = "delay_refund_status")
    private Integer delayRefundStatus;

    /**
     * 信息费类型 0 线上 1线下 2不需要信息费
     */
    @Column(name = "info_fee_type")
    private Byte infoFeeType;

    /**
     * 车主user_id
     */
    @Column(name = "pay_user_id")
    private Long payUserId;

    /**
     * 车主昵称
     */
    @Column(name = "pay_user_name")
    private String payUserName;

    /**
     * 车主注册账号
     */
    @Column(name = "pay_cell_phone")
    private String payCellPhone;

    /**
     * 车主联系电话
     */
    @Column(name = "pay_link_phone")
    private String payLinkPhone;

    /**
     * 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）12车方取消订单    13异常处理完成 
     */
    @Column(name = "rob_status")
    private String robStatus;

    /**
     * 支付状态0待支付1支付失败2支付成功 3取消支付
     */
    @Column(name = "pay_status")
    private String payStatus;

    /**
     * 接单时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 线下订单创建人
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 线下订单备注
     */
    private String remark;

    /**
     * 内部支付流水号
     */
    @Column(name = "pay_no")
    private String payNo;

    /**
     * 支付方式 1支付宝 2易宝银行卡 3微信 4线下支付 5tpay支付
     */
    @Column(name = "pay_type")
    private String payType;

    /**
     * 支付子渠道
     */
    @Column(name = "pay_sub_channel")
    private String paySubChannel;

    /**
     * 支付金额单位分
     */
    @Column(name = "pay_amount")
    private Long payAmount;

    /**
     * 支付手续费金额单位分
     */
    @Column(name = "pay_fee_amount")
    private Long payFeeAmount;

    /**
     * 订单总金额(单位分)
     */
    @Column(name = "total_order_amount")
    private Long totalOrderAmount;

    /**
     * 处理结果 车主金额单位分
     */
    @Column(name = "car_amount")
    private Long carAmount;

    /**
     * 处理结果 货主金额单位分
     */
    @Column(name = "goods_amount")
    private Long goodsAmount;

    /**
     * 处理异常上报时间
     */
    @Column(name = "handle_ex_time")
    private Date handleExTime;

    /**
     * 优惠券金额(单位分)
     */
    @Column(name = "coupon_amount")
    private Long couponAmount;

    /**
     * 平台服务费 单位分
     */
    @Column(name = "pay_service_charge")
    private Integer payServiceCharge;

    /**
     * 运费金额 单位元
     */
    @Column(name = "carriage_fee")
    private Integer carriageFee;

    /**
     * 限时货源标识0和null不是1是
     */
    @Column(name = "time_limit_identification")
    private Integer timeLimitIdentification;

    /**
     * 支付完成时间
     */
    @Column(name = "pay_end_time")
    private Date payEndTime;

    /**
     * 退款金额 单位分
     */
    @Column(name = "refund_amount")
    private Long refundAmount;

    /**
     * 退款时间
     */
    @Column(name = "refund_time")
    private Date refundTime;

    /**
     * 退款到账时间
     */
    @Column(name = "refund_arrival_time")
    private Date refundArrivalTime;

    /**
     * 退款状态 1.退款中 2.退款成功 3.退款失败
     */
    @Column(name = "refund_status")
    private String refundStatus;

    /**
     * 退款原因
     */
    @Column(name = "refund_reason")
    private String refundReason;

    /**
     * 退款异常信息
     */
    @Column(name = "refund_err_msg")
    private String refundErrMsg;

    /**
     * 同意装货时间（成交时间）
     */
    @Column(name = "agree_time")
    private Date agreeTime;

    /**
     * 装货完成时间
     */
    @Column(name = "load_time")
    private Date loadTime;

    /**
     * 拒绝装货时间
     */
    @Column(name = "refuse_time")
    private Date refuseTime;

    /**
     * 车头牌照头字母
     */
    @Column(name = "head_city")
    private String headCity;

    /**
     * 车头牌照号码
     */
    @Column(name = "head_no")
    private String headNo;

    /**
     * 挂车牌照头字母
     */
    @Column(name = "tail_city")
    private String tailCity;

    /**
     * 挂车牌照号码
     */
    @Column(name = "tail_no")
    private String tailNo;

    /**
     * 车辆ID
     */
    @Column(name = "car_id")
    private Long carId;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 异常上报取消状态:0初始化 1车方撤销 2 货方撤销
     */
    @Column(name = "ex_cancel_status")
    private Integer exCancelStatus;

    /**
     * 信息费状态：5货源撤销退款 6车主取消支付 10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成
     */
    @Column(name = "cost_status")
    private Short costStatus;

    /**
     * 操作人Id
     */
    @Column(name = "op_id")
    private Long opId;

    /**
     * 操作人姓名
     */
    @Column(name = "op_name")
    private String opName;

    /**
     * 显示货源是否成交车标识 0不是 1是
     */
    @Column(name = "is_deal_car")
    private Integer isDealCar;

    /**
     * 派车或者取消派车时间
     */
    @Column(name = "deal_car_time")
    private Date dealCarTime;

    /**
     * 0 未延迟 1 延迟付款  2 拒绝退款延迟
     */
    @Column(name = "delay_status")
    private Integer delayStatus;

    /**
     * 货方详情是否展示 0展示 1不展示
     */
    @Column(name = "goods_show")
    private Integer goodsShow;

    /**
     * 车方详情是否展示 0展示 1不展示
     */
    @Column(name = "car_show")
    private Integer carShow;

    /**
     * 延迟付款到期时间
     */
    @Column(name = "de_payment_dueDate")
    private Date dePaymentDuedate;

    /**
     * 装车状态 0 未装车 1 已装车
     */
    @Column(name = "loading_status")
    private Integer loadingStatus;

    /**
     * 装车状态二级选项类型 参见 source 表 load_y_child_type和load_n_child_type 组
     */
    @Column(name = "loading_child_status")
    private Integer loadingChildStatus;

    /**
     * 是否更换过车辆 0未更换 1已更换
     */
    @Column(name = "car_replace")
    private Integer carReplace;

    /**
     * 货源来源（1货主；2调度客服；）
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 是否投诉: 0.未投诉 1.已投诉
     */
    @Column(name = "is_complaint")
    private Integer isComplaint;

    /**
     * 订单状态：5.待接单 10.待签署 15.待装货 20.待卸货/待收货 25.待收运费/待付运费 30.已完成 35.已取消
     */
    @Column(name = "order_new_status")
    private Integer orderNewStatus;

    /**
     * 司机id
     */
    @Column(name = "driver_id")
    private Long driverId;
}