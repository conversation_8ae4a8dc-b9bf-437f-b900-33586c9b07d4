package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_custom_service_rule")
public class TytCustomServiceRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 业务端 1.车主版 2.货站版 3.全部
     */
    private Short port;

    /**
     * 规则名称
     */
    @Column(name = "rule_title")
    private String ruleTitle;

    /**
     * tyt_custom_service_type 表id
     */
    @Column(name = "type_id")
    private Integer typeId;

    /**
     * 规则类型名称
     */
    @Column(name = "rule_type_name")
    private String ruleTypeName;

    /**
     * 规则状态 1.启用 2.禁用
     */
    private Short status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operater;

    private Date ctime;

    private Date mtime;

    /**
     * 规则内容
     */
    @Column(name = "rule_content")
    private String ruleContent;
}