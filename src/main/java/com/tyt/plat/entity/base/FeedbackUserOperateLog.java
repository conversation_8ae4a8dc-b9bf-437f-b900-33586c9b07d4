package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_user_operate_log")
public class FeedbackUserOperateLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 评价id
     */
    @Column(name = "feedback_id")
    private Long feedbackId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 修改前标签 json
     */
    @Column(name = "pre_operate_label")
    private String preOperateLabel;

    /**
     * 修改后标签 json
     */
    @Column(name = "post_operate_label")
    private String postOperateLabel;

    /**
     * 修改前备注
     */
    @Column(name = "pre_operate_comment")
    private String preOperateComment;

    /**
     * 修改后备注
     */
    @Column(name = "post_operate_comment")
    private String postOperateComment;

    /**
     * 修改前 评价类别 1好评 2中评 3差评
     */
    @Column(name = "pre_operate_type")
    private Integer preOperateType;

    /**
     * 修改后 评价类别 1好评 2中评 3差评
     */
    @Column(name = "post_operate_type")
    private Integer postOperateType;

    /**
     * 操作类型 1 修改/2 删除
     */
    @Column(name = "operate_type")
    private Integer operateType;
}