package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_front_page")
public class TytFrontPage {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 分类id
     */
    @Column(name = "category_id")
    private Long categoryId;

    /**
     * 客户端类型（1车，2货,3车和货）
     */
    @Column(name = "client_type")
    private Integer clientType;

    /**
     * 标题
     */
    private String title;

    /**
     * h5地址
     */
    @Column(name = "link_url")
    private String linkUrl;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建管理员id
     */
    @Column(name = "create_man_id")
    private Long createManId;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 修改管理员id
     */
    @Column(name = "modify_man_id")
    private Long modifyManId;

    /**
     * 是否发布（1是，0否）
     */
    @Column(name = "publish_status")
    private Integer publishStatus;

    /**
     * 发布时间
     */
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 发布管理员id
     */
    @Column(name = "publish_man_id")
    private Long publishManId;
}