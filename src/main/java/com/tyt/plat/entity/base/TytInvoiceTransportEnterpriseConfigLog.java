package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_invoice_transport_enterprise_config_log")
public class TytInvoiceTransportEnterpriseConfigLog implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    /**
     * 最大吨位
     */
    @Column(name = "max_tonnage")
    private Double maxTonnage;

    /**
     * 最大吨位是否修改 0：未修改；1：修改了
     */
    @Column(name = "max_tonnage_is_change")
    private Integer maxTonnageIsChange;

    /**
     * 最大长度
     */
    @Column(name = "max_length")
    private Double maxLength;

    /**
     * 最大长度是否修改 0：未修改；1：修改了
     */
    @Column(name = "max_length_is_change")
    private Integer maxLengthIsChange;

    /**
     * 最大宽度
     */
    @Column(name = "max_width")
    private Double maxWidth;

    /**
     * 最大宽度 0：未修改；1：修改了
     */
    @Column(name = "max_width_is_change")
    private Integer maxWidthIsChange;

    /**
     * 最大高度
     */
    @Column(name = "max_height")
    private Double maxHeight;

    /**
     * 最大高度是否修改 0：未修改；1：修改了
     */
    @Column(name = "max_height_is_change")
    private Integer maxHeightIsChange;

    /**
     * 最大在途运费金额总数
     */
    @Column(name = "max_total_transport_price")
    private Double maxTotalTransportPrice;

    /**
     * 最大在途运费金额总数是否修改 0：未修改；1：修改了
     */
    @Column(name = "max_total_transport_price_is_change")
    private Integer maxTotalTransportPriceIsChange;

    //最大单次运费
    @Column(name = "max_price")
    private String maxPrice;

    // 超限证 装货阶段是否必传 0否 1是
    @Column(name = "load_need_load_stage")
    private Integer loadNeedLoadStage;

    // 超限证 卸货阶段是否必传 0否 1是
    @Column(name = "unload_need_load_stage")
    private Integer unloadNeedLoadStage;

    // 超限证 收货阶段是否必传 0否 1是
    @Column(name = "get_need_load_stage")
    private Integer getNeedLoadStage;

    // 车头整备质量+挂车整备质量+货物重量>【】吨
    @Column(name = "car_weight")
    private BigDecimal carWeight;

    // 车货宽>【】米
    @Column(name = "car_width")
    private BigDecimal carWidth;

    // 车货高>【】米
    @Column(name = "car_height")
    private BigDecimal carHeight;

    // 车货长>【】米
    @Column(name = "car_length")
    private BigDecimal carLength;

    // 装货图片 装货阶段是否必传 0否 1是
    @Column(name = "load_need_load_photo")
    private Integer loadNeedLoadPhoto;

    // 装货图片 收货阶段是否必传 0否 1是
    @Column(name = "get_need_load_photo")
    private Integer getNeedLoadPhoto;

    // 卸货图片 卸货阶段是否必传 0否 1是
    @Column(name = "unload_need_unload_photo")
    private Integer unloadNeedUnloadPhoto;

    // 卸货图片 收货阶段是否必传 0否 1是
    @Column(name = "get_need_unload_photo")
    private Integer getNeedUnloadPhoto;

    // 回单图片 卸货阶段是否必传 0否 1是
    @Column(name = "unlod_need_receipt_photo")
    private Integer unlodNeedReceiptPhoto;

    // 回单图片 收货阶段是否必传 0否 1是
    @Column(name = "get_need_receipt_photo")
    private Integer getNeedReceiptPhoto;

    //是否修改了公里价格限制
    @Column(name = "update_price_config")
    private Integer updatePriceConfig;

    /**
     * 配置修改时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 配置修改人ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 配置修改人用户名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    //接单车辆要求配置 适用类型 1三方开票
    @Column(name = "take_order_car_require_type")
    private Integer takeOrderCarRequireType;

    //接单车辆要求配置 三方要求-证件缺失 且认证状态-认证通过 0否 1是
    @Column(name = "car_require_auth")
    private Integer carRequireAuth;

    //接单车辆要求配置 三方要求-证件待审 0否 1是
    @Column(name = "car_require_wait_auth")
    private Integer carRequireWaitAuth;

    //接单车辆要求配置 三方要求-符合要求 0否 1是
    @Column(name = "car_require_conform")
    private Integer carRequireConform;

    //接单车辆要求配置 三方要求-不符合要求 0否 1是
    @Column(name = "car_require_no_conform")
    private Integer carRequireNoConform;

    //是否可指派车方 0：否；1：是
    @Column(name = "can_assign_car")
    private Integer canAssignCar;

    //是否分段付款 0：否；1：是
    @Column(name = "segmented_payments")
    private Integer segmentedPayments;

}