package com.tyt.plat.entity.base;

import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_special_car")
public class TytSpecialCar {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 账号用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 特运通账号
     */
    @Column(name = "tyt_cell_phone")
    private String tytCellPhone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 司机表id
     */
    @Column(name = "driver_id")
    private Long driverId;

    /**
     * 司机用户id
     */
    @Column(name = "driver_user_id")
    private Long driverUserId;

    /**
     * 司机手机号
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 驾驶能力 逗号分隔
     */
    @Column(name = "driving_ability")
    private String drivingAbility;

    /**
     * 城市
     */
    private String city;

    /**
     * 车型
     */
    @Column(name = "car_type")
    private String carType;

    /**
     * 车头信息
     */
    @Column(name = "head_city_no")
    private String headCityNo;

    /**
     * 挂车信息
     */
    @Column(name = "tail_city_no")
    private String tailCityNo;

    /**
     * 挂车长度(米)
     */
    private String length;

    /**
     * 货台面高度（厘米）
     */
    @Column(name = "table_height")
    private String tableHeight;

    /**
     * 平台样式
     */
    @Column(name = "other_pure_flat")
    private String otherPureFlat;

    /**
     * 爬梯类型
     */
    @Column(name = "ladder_type")
    private String ladderType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分数
     */
    private String fraction;

    /**
     * 审核状态 0：审核中  1：成功   2：失败
     */
    private Integer status;

    /**
     * 审核失败原因
     */
    private String reason;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 更新人
     */
    @Column(name = "update_name")
    private String updateName;

    /**
     * 车辆id
     */
    @Column(name = "car_id")
    private Long carId;

    @Column(name = "bi_distance")
    private String biDistance;


    @Transient
    private List<TytSpecialCarRoute> routes;
}