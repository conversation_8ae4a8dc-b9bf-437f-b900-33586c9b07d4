package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_owner_auth")
public class TytOwnerAuth {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货主id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货主手机号
     */
    @Column(name = "cell_phone")
    private String cellPhone;

    /**
     * 官方授权名称
     */
    @Column(name = "auth_name")
    private String authName;

    /**
     * 状态 1停用 2启用
     */
    private Integer status;

    /**
     * 操作人
     */
    @Column(name = "op_name")
    private String opName;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}