package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "lottery_user")
public class LotteryUser {
    /**
     * 定向抽奖用户表主键（自增）
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户表id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 总抽奖次数
     */
    @Column(name = "total_draws_num")
    private Long totalDrawsNum;

    /**
     * 活动表id
     */
    @Column(name = "draw_activity_info_id")
    private Long drawActivityInfoId;

    /**
     * 用户账号（手机号）
     */
    @Column(name = "user_call_phone")
    private String userCallPhone;

    /**
     * 是否参与抽奖 0未参与 1已参与
     */
    @Column(name = "take_in")
    private Byte takeIn;

    /**
     * 是否发送 0否 1是
     */
    @Column(name = "whether_to_send")
    private Byte whetherToSend;

    /**
     * 创建人姓名
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private Long createBy;

    /**
     * 修改人
     */
    @Column(name = "update_by")
    private Long updateBy;

    /**
     * 是否删除（0：删除，1：不删除）
     */
    @Column(name = "is_delete")
    private Byte isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}