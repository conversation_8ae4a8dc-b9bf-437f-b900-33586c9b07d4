package com.tyt.plat.entity.base;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <p>
 * 捂货记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Getter
@Setter
@Table(name = "tyt_cover_goods_log")
public class CoverGoodsLogDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 车主id
     */
    private Long userId;

    /**
     * 货源id
     */
    private Long tsId;

    /**
     * 货源类型：1好货 2中货 3差货
     */
    private Integer goodsLevel;

    /**
     * 当天浏览的第几个货源，按货源类型分别统计
     */
    private Integer viewIndex;

    /**
     * 命中规则
     */
    private String hitRule;

    /**
     * 优推好车主过期时间
     */
    private Date priorityRecommendExpireTime;

    /**
     * 发货倒计时
     */
    private Integer xTimeInSeconds;

    /**
     * 详情倒计时
     */
    private Integer yTimeInSeconds;

    /**
     * 捂货间隔
     */
    private Integer coverInterval;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
