package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table(name = "tyt_user_permission_used")
public class TytUserPermissionUsed {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 权益类型对应的唯一标识
     */
    @Column(name = "service_permission_type_id")
    private String servicePermissionTypeId;

    /**
     * 权益类型对应名称
     */
    @Column(name = "service_permission_type_name")
    private String servicePermissionTypeName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 货源ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 获取记录id
     */
    @Column(name = "gain_id")
    private Long gainId;
}