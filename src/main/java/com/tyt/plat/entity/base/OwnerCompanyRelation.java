package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "owner_company_relation")
public class OwnerCompanyRelation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    private Long enterpriseId;

    /**
     * 运输公司ID
     */
    @Column(name = "company_id")
    private Long companyId;

    /**
     * 状态，0-停用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}