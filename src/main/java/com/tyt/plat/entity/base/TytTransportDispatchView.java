package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_dispatch_view")
public class TytTransportDispatchView {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 车主id
     */
    @Column(name = "car_user_id")
    private Long carUserId;

    /**
     * 车主姓名
     */
    @Column(name = "car_user_name")
    private String carUserName;

    /**
     * 车主昵称
     */
    @Column(name = "car_nick_name")
    private String carNickName;

    /**
     * 车主手机号
     */
    @Column(name = "car_phone")
    private String carPhone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备注人id
     */
    @Column(name = "remark_user_id")
    private Long remarkUserId;

    /**
     * 备注人姓名
     */
    @Column(name = "remark_user_name")
    private String remarkUserName;

    /**
     * 备注时间
     */
    @Column(name = "remark_time")
    private Date remarkTime;

    /**
     * 用户查看次数
     */
    @Column(name = "view_count")
    private Integer viewCount;

    /**
     * 用户联系次数
     */
    @Column(name = "contact_count")
    private Integer contactCount;

    /**
     * 查看时间
     */
    @Column(name = "view_time")
    private Date viewTime;

    /**
     * 联系时间
     */
    @Column(name = "contact_time")
    private Date contactTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}