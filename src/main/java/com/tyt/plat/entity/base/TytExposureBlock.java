package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_exposure_block")
public class TytExposureBlock {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 曝光卡权益次数
     */
    @Column(name = "exposure_permission_num")
    private Integer exposurePermissionNum;

    /**
     * 黑名单状态(0 未封禁 1 封禁 2 延迟封禁 )
     */
    @Column(name = "block_status")
    private Integer blockStatus;

    /**
     * 是否永久封禁(0 不是 1 是)
     */
    @Column(name = "permanent_block")
    private Boolean permanentBlock;

    /**
     * 是否删除(0未删除，1已删除)
     */
    @Column(name = "delete_flag")
    private Boolean deleteFlag;

    /**
     * 操作人ID
     */
    @Column(name = "operate_user_id")
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    @Column(name = "operate_user_name")
    private String operateUserName;

    /**
     * 封禁原因
     */
    @Column(name = "reason")
    private String reason;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 版本号
     */
    @Column(name = "version")
    private Integer version;

    /**
     * 封禁开始时间
     */
    @Column(name = "block_begin_time")
    private Date blockBeginTime;

    /**
     * 封禁结束时间
     */
    @Column(name = "block_end_time")
    private Date blockEndTime;

    /**
     * 是否短信通知(0 不通知 1通知)
     */
    @Column(name = "sms_notify")
    private Boolean smsNotify;
}