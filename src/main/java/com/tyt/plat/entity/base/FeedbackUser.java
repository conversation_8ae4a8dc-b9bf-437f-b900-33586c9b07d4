package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feedback_user")
public class FeedbackUser implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 评价用户id
     */
    @Column(name = "post_user_id")
    private Long postUserId;

    /**
     * 评论用户手机号
     */
    @Column(name = "post_user_phone")
    private String postUserPhone;

    /**
     * 评价用户头像
     */
    @Column(name = "post_user_avatar")
    private String postUserAvatar;

    /**
     * 评论用户类型 1车 2货
     */
    @Column(name = "post_user_type")
    private Integer postUserType;

    /**
     * 被评价用户id
     */
    @Column(name = "receive_user_id")
    private Long receiveUserId;

    /**
     * 被评论用户手机号
     */
    @Column(name = "receive_user_phone")
    private String receiveUserPhone;

    /**
     * 被评价用户头像
     */
    @Column(name = "receive_user_avatar")
    private String receiveUserAvatar;

    /**
     * 被评论用户类型  1车 2货
     */
    @Column(name = "receive_user_type")
    private Integer receiveUserType;

    /**
     * 是否删除 0 未删除 1已删除
     */
    @Column(name = "del_flag")
    private Boolean delFlag;

    /**
     * 对应的运单id
     */
    @Column(name = "ts_order_id")
    private Long tsOrderId;

    /**
     * 对应的货源id
     */
    @Column(name = "ts_id")
    private Long tsId;

    /**
     * 对应的订单主键id
     */
    private Long orderId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 对应运单货方名
     */
    @Column(name = "transport_order_pub_user_name")
    private String transportOrderPubUserName;

    /**
     * 对应运单车方名
     */
    @Column(name = "transport_order_pay_user_name")
    private String transportOrderPayUserName;

    /**
     * 对应运单出发地
     */
    @Column(name = "transport_order_start_point")
    private String transportOrderStartPoint;

    /**
     * 对应运单目的地
     */
    @Column(name = "transport_order_dest_point")
    private String transportOrderDestPoint;

    /**
     * 评价类别 1好评 2中评 3差评
     */
    @Column(name = "feedback_type")
    private Integer feedbackType;

    /**
     * 评价操作截止日期
     */
    @Column(name = "feedback_deadline")
    private Date feedbackDeadline;

    /**
     * 评价期结束是否需要处理 0不需要 1需要
     */
    @Column(name = "need_handle_on_deadline")
    private Boolean needHandleOnDeadline;

    /**
     * 评价备注
     */
    private String comment;

    /**
     * 评价修改次数
     */
    @Column(name = "update_times")
    private Integer updateTimes;

    /**
     * 数据状态：0-有效；1-失效；
     */
    private Boolean receiverHideFlag;

    /**
     * 失效原因：1-责任方评价失效；2-申诉成功失效；
     */
    @Column(name = "lose_efficacy_reason")
    private Integer loseEfficacyReason;
}