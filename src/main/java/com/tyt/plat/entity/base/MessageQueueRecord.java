package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "message_queue_record")
public class MessageQueueRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * topic
     */
    private String topic;

    /**
     * tag
     */
    private String tag;

    /**
     * 阿里mq id
     */
    @Column(name = "ali_mq_id")
    private String aliMqId;

    /**
     * 消息唯一标识
     */
    @Column(name = "msg_key")
    private String msgKey;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处理状态(0:未消费；1:消费中;2:成功;3:失败;4死信)
     */
    private Integer status;

    /**
     * 消息消费次数
     */
    @Column(name = "consume_count")
    private Integer consumeCount;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 消息内容
     */
    @Column(name = "msg_content")
    private String msgContent;
}