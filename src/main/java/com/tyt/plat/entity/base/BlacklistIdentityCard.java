package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 黑名单用户身份证拉黑表实体类
 * blacklist_identity_card
 *
 * <AUTHOR>
 * @date 2024-4-19 14:32:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "blacklist_identity_card")
public class BlacklistIdentityCard implements Serializable{

	/**
	 * id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	/**
	 * 身份证号
	 */
	@Column(name = "identity_card")
	private String identityCard;
	/**
	 * 拉黑状态：1-生效；2-失效；
	 */
	@Column(name = "status")
	private Integer status;
	/**
	 * 原因
	 */
	@Column(name = "reason")
	private String reason;
	/**
	 * 操作人ID
	 */
	@Column(name = "action_id")
	private Long actionId;
	/**
	 * 操作人姓名
	 */
	@Column(name = "action_name")
	private String actionName;
	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@Column(name = "modify_time")
	private Date modifyTime;
}
