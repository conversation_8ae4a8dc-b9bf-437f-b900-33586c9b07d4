package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_machine_type_audit")
public class TytMachineTypeAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 显示名称
     */
    @Column(name = "show_name")
    private String showName;

    /**
     * 一级分类
     */
    @Column(name = "top_class")
    private String topClass;

    /**
     * 二级分类，如挖掘机，起重机等
     */
    @Column(name = "second_class")
    private String secondClass;

    /**
     * 品牌型号
     */
    @Column(name = "brand_type")
    private String brandType;

    /**
     * 一级型号
     */
    @Column(name = "top_type")
    private String topType;

    /**
     * 0:忽略，1:待审核 2：已审核（合规）3：已审核（非法）
     */
    private Integer status;

    /**
     * 合规审核次数
     */
    @Column(name = "conformance_counts")
    private Integer conformanceCounts;

    /**
     * 非法审核次数
     */
    @Column(name = "illegal_counts")
    private Integer illegalCounts;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 修改人
     */
    @Column(name = "modify_name")
    private String modifyName;
}