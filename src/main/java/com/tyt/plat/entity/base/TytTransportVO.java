package com.tyt.plat.entity.base;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description main表VO
 * @date 2023-08-16-14-08-23
 */
@Data
public class TytTransportVO implements Serializable {

    private Long userId;
    private Long id;
    private Long srcMsgId;
    private Long dispatchId;
    private String startPoint;
    private String destPoint;
    private String taskContent;
    private String userShowName;

    private Integer status;
    private Integer publishType;
}
