package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "cs_new_custom")
public class CsNewCustom {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 客户姓名
     */
    @Column(name = "custom_name")
    private String customName;

    /**
     * 客户电话
     */
    @Column(name = "custom_phone")
    private String customPhone;

    /**
     * 客户来源
     */
    @Column(name = "custom_source")
    private String customSource;

    /**
     * 注册状态 1：已注册 2：未注册
     */
    @Column(name = "register_status")
    private Short registerStatus;

    /**
     * 备注
     */
    private String remark;

    @Column(name = "city")
    private String city;


    @Column(name = "gift")
    private String gift;

    @Column(name = "self_define")
    private String selfDefine;

    /**
     * 修改人id
     */
    @Column(name = "modify_id")
    private Long modifyId;

    /**
     * 修改人姓名
     */
    @Column(name = "modify_name")
    private String modifyName;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;

    /**
     * 状态 1：有效 2：无效 3：删除
     */
    private Short status;

    /**
     * 所属方  1.车维 2，货维   5.调度
     */
    @Column(name = "belong_to")
    private Integer belongTo;

    /**
     * 是否是推荐用户
     */
    @Column(name = "is_recommend_user")
    private Short isRecommendUser;

    /**
     * 推荐人账号
     */
    @Column(name = "recommend_cell_phone")
    private String recommendCellPhone;

    /**
     * 推荐人用户id
     */
    @Column(name = "recommend_user_id")
    private Long recommendUserId;
}