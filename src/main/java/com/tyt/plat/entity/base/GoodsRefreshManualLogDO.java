package com.tyt.plat.entity.base;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <p>
 * 货源刷新手动曝光用户提交日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@Table(name = "tyt_goods_refresh_manual_log")
public class GoodsRefreshManualLogDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 发货人id
     */
    private Long userId;

    /**
     * 提交任务类型，1填运费，2转一口价，4补充目的地
     */
    private Integer itemValue;

    /**
     * 提交前运费
     */
    private String priceBefore;

    /**
     * 提交后运费
     */
    private String priceAfter;

    /**
     * 提交前货源类型（电议1，一口价2）
     */
    private Integer publishTypeBefore;

    /**
     * 提交后货源类型（电议1，一口价2）
     */
    private Integer publishTypeAfter;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date modifyTime;

    /**
     * 系统推荐金额
     */
    private String priceSuggest;

    /**
     * 来源页面：0 未知；1 APP货源详情页货源诊断入口；2 APP发货成功货源诊断入口；3 APP发货页低速找车弹窗；10 PC发货前填价弹窗；
     */
    private Integer sourcePage;
}
