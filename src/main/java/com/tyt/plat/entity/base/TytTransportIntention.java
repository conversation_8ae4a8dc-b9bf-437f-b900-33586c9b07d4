package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_intention")
public class TytTransportIntention {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 重发后原信息ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 曝光状态（1曝光，0不曝光）
     */
    private Integer status;

    /**
     * 是否有人拨打（0没有；1有）
     */
    @Column(name = "has_call")
    private Integer hasCall;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 修改id(同步需要)
     */
    @Column(name = "change_id")
    private Long changeId;

    /**
     * 相似货源编码
     */
    @Column(name = "similarity_code")
    private String similarityCode;

    /**
     * 好货模型分数
     */
    @Column(name = "goods_model_score")
    private BigDecimal goodsModelScore;
}