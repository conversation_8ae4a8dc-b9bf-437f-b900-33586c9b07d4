package com.tyt.plat.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_signing_car")
public class TytSigningCar {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 特运通账号
     */
    @Column(name = "tyt_cell_phone")
    private String tytCellPhone;

    /**
     * 签约合作商tyt_source表signing_cooperation_agreement
     */
    private String signing;

    /**
     * 归属调度
     */
    private String dispatch;

    /**
     * 与调度账号合作次数
     */
    @Column(name = "cooperate_num")
    private Integer cooperateNum;

    /**
     * 指派次数
     */
    @Column(name = "assign_num")
    private Integer assignNum;

    /**
     * 指派成功次数
     */
    @Column(name = "assign_success_num")
    private Integer assignSuccessNum;

    /**
     * 接单率
     */
    @Column(name = "receiving_orders")
    private BigDecimal receivingOrders;

    /**
     * 好评率
     */
    @Column(name = "favorable_comment")
    private BigDecimal favorableComment;

    /**
     * 综合分值
     */
    @Column(name = "compre_fraction")
    private BigDecimal compreFraction;

    /**
     * 开关，接单状态 0关闭  1开启
     */
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_name")
    private String updateName;
}