package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_cover_goods_dial_user_use_log")
public class TytCoverGoodsDialUserUseLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户名
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 使用原因
     */
    @Column(name = "use_reason")
    private String useReason;

    /**
     * 货源ID
     */
    @Column(name = "ts_id")
    private Long tsId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 变更数量
     */
    @Column(name = "change_num")
    private Integer changeNum;

    /**
     * 变更类型 0扣除 1添加
     */
    @Column(name = "change_type")
    private Integer changeType;

    /**
     * 过期时间，只有变更类型是发放时存在
     */
    @Column(name = "expire_time")
    private Date expireTime;
}