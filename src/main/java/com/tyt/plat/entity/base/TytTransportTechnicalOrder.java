package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_technical_order")
public class TytTransportTechnicalOrder {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 货主id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 车主id
     */
    @Column(name = "pay_user_id")
    private Long payUserId;

    /**
     * 与tyt_old_order表关联
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 技术服务费单号
     */
    @Column(name = "technical_service_no")
    private String technicalServiceNo;

    /**
     * 技术服务费 单位分
     */
    @Column(name = "technical_service_fee")
    private Long technicalServiceFee;

    /**
     * 状态 0待支付 1支付成功 2支付失败
     */
    private Byte status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}