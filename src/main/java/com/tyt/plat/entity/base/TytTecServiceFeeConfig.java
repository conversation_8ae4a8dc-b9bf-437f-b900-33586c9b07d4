package com.tyt.plat.entity.base;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "tyt_tec_service_fee_config")
public class TytTecServiceFeeConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 适用货源（单选）1:专车；2:普货；3:优车1.0；4:优车2.0；5:运满满
     */
    @Column(name = "apply_transport_type")
    private Integer applyTransportType;

    /**
     * 专车签约合作商类型 1:平台；2:非平台
     */
    @Column(name = "special_carcooperative_type")
    private Integer specialCarCooperativeType;

    /**
     * 订金模式 0:不限；1:退还；2:不退还
     */
    @Column(name = "refund_flag_type")
    private Integer refundFlagType;

    /**
     * 车会员状态 0:不限；1:会员用户；2:非会员用户
     */
    @Column(name = "car_member_type")
    private Integer carMemberType;

    /**
     * 价格模式 0:不限；1:有价电议；2:无价电议；3:一口价
     */
    @Column(name = "price_publish_type")
    private Integer pricePublishType;

    /**
     * 超时免佣 0:关闭；1:开启
     */
    @Column(name = "free_tec_service_fee_type")
    private Integer freeTecServiceFeeType;

    /**
     * 超时免佣满足X人查看
     */
    @Column(name = "free_tec_service_fee_view_count")
    private Integer freeTecServiceFeeViewCount;

    /**
     * 超时免佣满足X人拨打
     */
    @Column(name = "free_tec_service_fee_call_count")
    private Integer freeTecServiceFeeCallCount;

    /**
     * X分钟后免佣
     */
    @Column(name = "free_tec_service_fee_time")
    private Integer freeTecServiceFeeTime;

    /**
     * 是否使用虚拟号 0:关闭；1:开启
     */
    @Column(name = "privacy_phone_type")
    private Integer privacyPhoneType;

    /**
     * 抽佣货源权益
     */
    @Column(name = "interests_word")
    private String interestsWord;

    /**
     * 抽佣货源权益链接
     */
    @Column(name = "interests_url")
    private String interestsUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 创建人用户名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 修改人ID
     */
    @Column(name = "modify_user_id")
    private Long modifyUserId;

    /**
     * 修改人用户名
     */
    @Column(name = "modify_user_name")
    private String modifyUserName;
}