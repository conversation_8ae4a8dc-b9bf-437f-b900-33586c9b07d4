package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_invoice_enterprise_sign")
public class TytInvoiceEnterpriseSign {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 企业id
     */
    @Column(name = "enterprise_id")
    private Long enterpriseId;

    /**
     * 企业名称
     */
    @Column(name = "enterprise_name")
    private String enterpriseName;

    /**
     * 企业认证统一社会信用代码
     */
    @Column(name = "enterprise_credit_code")
    private String enterpriseCreditCode;

    /**
     * 企业法人姓名
     */
    @Column(name = "legal_person_name")
    private String legalPersonName;

    /**
     * 企业法人手机号
     */
    @Column(name = "legal_person_phone")
    private String legalPersonPhone;

    /**
     * 企业法人身份证号码
     */
    @Column(name = "legal_person_card")
    private String legalPersonCard;

    /**
     * 是否有效（0无效;1有效）
     */
    private Integer enable;

    /**
     * 签署人用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 签署人实名手机号(不一定是登录账号)
     */
    @Column(name = "user_phone")
    private String userPhone;

    /**
     * 框架协议状态(0未签署;1签署中;2已签署;3签署失败;4已失效)
     */
    @Column(name = "contract_status")
    private Integer contractStatus;

    /**
     * 未签署的协议文件URL
     */
    @Column(name = "contract_blank_url")
    private String contractBlankUrl;

    /**
     * 合同协议名称
     */
    @Column(name = "contract_name")
    private String contractName;

    /**
     * 协议编号
     */
    @Column(name = "contract_number")
    private String contractNumber;

    /**
     * 协议开始时间
     */
    @Column(name = "contract_start_time")
    private Date contractStartTime;

    /**
     * 协议结束时间
     */
    @Column(name = "contract_end_time")
    private Date contractEndTime;

    /**
     * e签宝三方企业账号id
     */
    @Column(name = "sign_account_id")
    private String signAccountId;

    /**
     * e签宝三方企业印章地址
     */
    @Column(name = "sign_seal_url")
    private String signSealUrl;

    /**
     * e签宝 flowId
     */
    @Column(name = "sign_flow_id")
    private String signFlowId;

    /**
     * e签宝签署人三要素实名认证状态（0未认证,1认证中,2认证成功,3认证失败）
     */
    @Column(name = "signer_verify_status")
    private Integer signerVerifyStatus;

    /**
     * 授权激活类型（1法人授权;2委托书授权）
     */
    @Column(name = "sign_type")
    private Integer signType;

    /**
     * 企业激活授权状态(0未授权;1授权中;2授权成功;3授权失败)
     */
    @Column(name = "active_auth_status")
    private Integer activeAuthStatus;

    /**
     * 签署意愿确认状态(0未确认;1确认中;2确认成功;3确认失败)
     */
    @Column(name = "sign_confirm_status")
    private Integer signConfirmStatus;

    /**
     * 签署意愿确认时间
     */
    @Column(name = "sign_confirm_time")
    private Date signConfirmTime;

    /**
     * 授权委托书URL
     */
    @Column(name = "sign_auth_url")
    private String signAuthUrl;

    /**
     * 授权委托书URL生成日期
     */
    @Column(name = "sign_auth_time")
    private Date signAuthTime;

    /**
     * 签署完成的协议文件URL
     */
    @Column(name = "contract_sign_url")
    private String contractSignUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * e签宝签署记录id
     */
    @Column(name = "sign_service_id")
    private String signServiceId;

    /**
     * 企业实名校验flowId
     */
    @Column(name = "enterprise_flow_id")
    private String enterpriseFlowId;
}