package com.tyt.plat.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_dispatch_cargo_owner")
public class DispatchCargoOwner {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 账号
     */
    @Column(name = "cell_phone")
    private String cellPhone;

    /**
     * 货主名称
     */
    @Column(name = "owner_name")
    private String ownerName;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 货主类型 1企业 2个人
     */
    @Column(name = "owner_type")
    private Integer ownerType;

    /**
     * 签约合作商 0否 1是
     */
    @Column(name = "sign_partner")
    private Integer signPartner;

    /**
     * 签约合作商ID
     */
    @Column(name = "cooperative_id")
    private Long cooperativeId;

    /**
     * 指定车方承运 0否 1是
     */
    @Column(name = "assign_car")
    private Integer assignCar;

    /**
     * 授权状态 1未授权 2已授权
     */
    @Column(name = "empower_status")
    private Integer empowerStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 1正常 2删除
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operator;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "modify_time")
    private Date modifyTime;
}