package com.tyt.plat.entity.base;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <p>
 * 秒抢货源命中条件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Getter
@Setter
@Table(name = "tyt_transport_seckill_factor")
public class TransportSeckillFactorDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 目的地城市
     */
    private String destCity;

    /**
     * 创建时间
     */
    private Date createTime;
}
