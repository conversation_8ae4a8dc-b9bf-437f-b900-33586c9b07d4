package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_custom_information")
public class TytCustomInformation {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * user表ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货主手机号
     */
    @Column(name = "goods_phone")
    private String goodsPhone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 来源渠道：1：自拓；2：市场；3：KA；4：其他
     */
    private Byte source;

    /**
     * 身份类型：1：个人货主；2：二手货主；3：经销商；4：租赁商；5：生产商；6：货站；7：物流公司；8：个人车主；9：运输公司；10：司机
     */
    @Column(name = "indentity_type")
    private String indentityType;

    /**
     * 主要发货类型（多个发货类型间用分号分隔）
     */
    @Column(name = "publish_good_type")
    private String publishGoodType;

    /**
     * 客户等级：1：S；2：A；3：B；4：C；5：D
     */
    @Column(name = "custom_levels")
    private String customLevels;

    /**
     * 市场维护人员用户名
     */
    @Column(name = "market_maintenance_personnel")
    private String marketMaintenancePersonnel;

    /**
     * 市场维护人员用户ID
     */
    @Column(name = "market_maintenance_personnel_user_id")
    private Long marketMaintenancePersonnelUserId;

    /**
     * 调度维护人员用户名
     */
    @Column(name = "dispatch_maintenance_personnel")
    private String dispatchMaintenancePersonnel;

    /**
     * 调度维护人员用户ID
     */
    @Column(name = "dispatch_maintenance_personnel_user_id")
    private Long dispatchMaintenancePersonnelUserId;

    /**
     * 代调意向等级：1：S；2：A；3：B；4：C；5：D
     */
    @Column(name = "dispatch_intention_levels")
    private String dispatchIntentionLevels;

    /**
     * 详细地址
     */
    @Column(name = "detail_address")
    private String detailAddress;

    /**
     * 货主城市
     */
    @Column(name = "goods_city")
    private String goodsCity;

    /**
     * 公司名称
     */
    @Column(name = "company_name")
    private String companyName;

    /**
     * 公司凭证url（多个url间用分号分隔）
     */
    @Column(name = "company_voucher")
    private String companyVoucher;

    /**
     * 职位：1：个体；2：公司负责人；3：物流负责人；4：业务员；5：其他
     */
    private String position;

    /**
     * 手机号码归属地
     */
    @Column(name = "mobile_belonging_place")
    private String mobileBelongingPlace;

    /**
     * 状态（预留 默认值：0）
     */
    private Byte status;

    /**
     * 创建人用户名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 创建人用户ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 更新人用户名
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人用户ID
     */
    @Column(name = "update_user_id")
    private Long updateUserId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 最近成单时间
     */
    @Column(name = "performance_time")
    private Date performanceTime;
}