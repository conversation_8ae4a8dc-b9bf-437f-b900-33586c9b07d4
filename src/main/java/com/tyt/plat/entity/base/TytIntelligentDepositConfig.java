package com.tyt.plat.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_intelligent_deposit_config")
public class TytIntelligentDepositConfig {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 起始吨重
     */
    @Column(name = "start_tonnage")
    private Integer startTonnage;

    /**
     * 结束吨重
     */
    @Column(name = "end_tonnage")
    private Integer endTonnage;

    /**
     * 起始距离
     */
    @Column(name = "start_distance")
    private Integer startDistance;

    /**
     * 结束距离
     */
    @Column(name = "end_distance")
    private Integer endDistance;

    /**
     * 订金金额
     */
    private Integer deposit;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}