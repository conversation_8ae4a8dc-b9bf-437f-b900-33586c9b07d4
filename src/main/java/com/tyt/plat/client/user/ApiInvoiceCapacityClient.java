package com.tyt.plat.client.user;


import com.tyt.plat.client.user.dto.CarCheckDTO;
import com.tyt.plat.client.user.vo.CarCheckRpcVO;
import com.tyt.plat.commons.internal.InternalServiceConstant;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;


/**
 * <AUTHOR>
 * @since 2025/02/19 15:17
 */
public interface ApiInvoiceCapacityClient {

    String BASE_URL = InternalServiceConstant.USER_SERVICE_URL;


    @POST(BASE_URL+"/capacity/third/car/check")
    Call<CarCheckRpcVO> checkCar(@Body CarCheckDTO carCheckDTO);
}
