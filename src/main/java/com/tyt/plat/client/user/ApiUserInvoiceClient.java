package com.tyt.plat.client.user;

import com.tyt.plat.client.user.dto.FaceAuthUploadImageRpcDTO;
import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.vo.face.FaceAuthUploadImageRpcVO;
import com.tyt.plat.vo.invoice.CarDriverResponseVO;
import com.tyt.plat.vo.invoice.ThirdDominantInfoVo;
import com.tyt.plat.vo.invoice.ThirdEnterpriseInfoVo;
import com.tyt.plat.vo.invoice.ThirdEnterpriseUserCodeVo;
import org.springframework.web.bind.annotation.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 开票-用户中心接口对接
 *
 * <AUTHOR>
 * @since 2024/7/18 15:45
 */
public interface ApiUserInvoiceClient {

    String BASE_URL = InternalServiceConstant.USER_SERVICE_URL;


    /**
     * 创建/编辑运力
     *
     * <AUTHOR>
     * @param userId 用户id
     * @param carId 车辆id
     * @param driverId 司机表id
     * @param orderId 订单id  根据id查询运力id，如有就是编辑，没有就是新增
     * @return Call<InternalWebResult<Object>>
     */
    @POST(BASE_URL + "/rpc/car/invoice/send")
    Call<InternalWebResult<CarDriverResponseVO>> sendCarInvoice(@Query("userId") Long userId,
                                                                @Query("carId") Long carId,
                                                                @Query("driverId") Long driverId,
                                                                @Query("orderId") Long orderId,
                                                                @Query("userCode") String userCode);

    /**
     * 根据主体ID获取主体信息
     */
    @GET(BASE_URL + "/enterprise/third/getDominantInfoById")
    Call<InternalWebResult<ThirdDominantInfoVo>> getDominantInfoById(@Query("dominantId") Long dominantId);

    /**
     * 根据用户ID和主体ID查询三方企业签约信息
     */
    @GET(BASE_URL + "/enterprise/third/getInfoByUserId")
    Call<InternalWebResult<ThirdEnterpriseInfoVo>> getInfoByUserId(@Query("userId")Long userId, @Query("dominantId")Long dominantId);

    /**
     * 获取三方用户编码
     *
     * @param userId        用户ID
     * @param dominantId    主体ID
     * @return ThirdEnterpriseUserCodeVo
     */
    @GET(BASE_URL + "/enterprise/third/getUserCode")
    Call<InternalWebResult<ThirdEnterpriseUserCodeVo>> getUserCode(@Query("userId")Long userId, @Query("dominantId")Long dominantId);

    /**
     * 人脸识别图片上传
     */
    @POST(BASE_URL + "/rpc/user/identity/faceAuthUploadForBase64")
    Call<FaceAuthUploadImageRpcVO> faceAuthUploadImageForBase64(@Body FaceAuthUploadImageRpcDTO faceAuthUploadImageRpcDTO);
}
