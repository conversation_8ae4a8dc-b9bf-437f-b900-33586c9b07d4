package com.tyt.plat.client.user;

import com.tyt.acvitity.bean.CarLocation;
import com.tyt.acvitity.bean.CarLocationParam;
import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.entity.base.CurrentLocationVO;
import org.springframework.web.bind.annotation.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/07 14:05
 */
public interface ApiTraceLocationClient {


    String BASE_URL = InternalServiceConstant.USER_TRACE_URL;


    /**
     *
     * @param carHeadNo  车牌号
     * @param color 颜色 1 蓝色、2 黄色、3 黄绿  非必填 默认 2
     *
     */
    @GET(BASE_URL + "/rpc/getRealTimeLocation")
    Call<InternalWebResult<List<CurrentLocationVO>>> getCarRealTimeLocation(@Query(value = "carHeadNo") String carHeadNo,
                                                              @Query(value = "color") String color);

    @GET(BASE_URL + "/rpc/getCarZJRealTimeLocation")
    Call<InternalWebResult<List<CurrentLocationVO>>> getCarZJRealTimeLocation(@Query(value = "carHeadNo") String carHeadNo,
                                                                              @Query(value = "color") String color);


//    /**
//     * 查询车辆历史轨迹
//     * @param carHeadNo 车牌号
//     * @param color 车牌颜色 1 蓝色、2 黄色、3 黄绿色  非必填 默认 2
//     * @param beginTime  查询开始时间 格式：yyyy-MM-dd HH:mm:ss   近 6 个月自然月
//     * @param endTime  查询结束时间  格式：yyyy-MM-dd HH:mm:ss   与开始时间相差 72小时之内
//     */
    @POST(BASE_URL + "/rpc/getCarLocus")
    Call<InternalWebResult<List<CarLocation>>> getCarLocus(@Body CarLocationParam carLocationParam);

}
