package com.tyt.plat.client.user.dto;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @since 2025/02/19 10:19
 */
@Data
public class CarCheckDTO {

    private String serviceProviderCode;

    /**
     * 车头牌照头字母
     */
    private String headCity;

    /**
     * 车头牌照号码
     */
    private String headNo;

    /**
     * 车头车辆类型
     */
    private String headCarType;


    /**
     * 车长:单位mm
     */
    private String headLength;
    /**
     * 车宽
     */
    private String headWidth;
    /**
     * 车高
     */
    private String headHeight;

    /**
     * 注册日期
     */
    private String headCarRegister;

    /**
     * 发证日期
     */
    private String headIssueDate;

    /**
     * 强制报废日期
     */
    private String headScrapDate;


    /**
     * 车头道路运输证号
     */
    private String headTransportNo;

    /**
     * 所有人
     */
    private String headOwner;

    /**
     * 车辆能源类型
     */
    private String headVehicleEnergyType;


    /**
     * 车牌颜色
     */
    private String headLicensePlateColor;

    /**
     * 总质量
     */
    private String headTotalWeight;

    /**
     * 整备质量
     */
    private String headCurbWeight;
    /**
     * 核定载质量
     */
    private String headCheckWeight;

    /**
     * 准牵引总质量
     */
    private String headTowWeight;

    /**
     * 车辆识别代号
     */
    private String headCarIdCode;


    /**
     * 发动机号
     */
    private String headCarEngineNo;


    /**
     * 发证机关
     */
    private String headIssueAuthority;

    /**
     * 车头经营许可证号
     */
    private String headBusinessLicenseNo;


    /**
     * 车头行驶证正/副页url
     */
    private String headDrivingUrl;


    /**
     * 车头行驶证副页反面图片url
     */
    private String headDrivingSubpagePicUrl;

    /**
     * 车头道路运输证主页图片url
     */
    private String headTransportHomepageUrl;

    /**
     * 车头行驶证有效期
     */
    private Date headDrivingExpiredTime;

    /**
     * 车头道路运输证有效期
     */
    private Date headTransportExpiredTime;

}
