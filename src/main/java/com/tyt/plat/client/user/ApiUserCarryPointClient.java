package com.tyt.plat.client.user;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.vo.user.UserCarryPointTotalVo;
import retrofit2.Call;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;

/**
 * 用户运点分内部服务调用
 *
 * <AUTHOR>
 * @date 2024/4/22 9:33
 */
public interface ApiUserCarryPointClient {

    String BASE_URL = InternalServiceConstant.USER_SERVICE_URL + "/api/userCarryPoint";

    /**
     * 获取我的运点分总分.
     *
     */
    @POST(BASE_URL + "/getUserCarryPointTotal")
    Call<InternalWebResult<UserCarryPointTotalVo>> getUserCarryPointTotal(@Query("userId") Long userId);

    /**
     * 批量获取用户运点分.
     * @param userIds userIds
     * @return WebResult
     */
    @POST(BASE_URL + "/getUserCarryPointList")
    Call<InternalWebResult<List<UserCarryPointTotalVo>>> getUserCarryPointList(@Query("userIds") String userIds);

}
