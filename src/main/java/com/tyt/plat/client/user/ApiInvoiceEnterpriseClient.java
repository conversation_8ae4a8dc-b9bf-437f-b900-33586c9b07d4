package com.tyt.plat.client.user;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.vo.user.EnterpriseModifyCheckVo;
import com.tyt.plat.vo.user.InvoiceEnterpriseInfoVO;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.math.BigDecimal;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/5/30 17:16
 */
public interface ApiInvoiceEnterpriseClient {

    String BASE_URL = InternalServiceConstant.USER_SERVICE_URL + "/invoiceEnterprise";
    String ENTERPRISE_BASE_URL = InternalServiceConstant.USER_SERVICE_URL + "/enterprise";

    /**
     * 校验企业信息是否可以修改
     */
    @POST(BASE_URL + "/checkModifyAllow")
    Call<InternalWebResult<EnterpriseModifyCheckVo>> checkModifyAllow(@Query("userId") Long userId);

    /**
     * 根据用户ID和开票主体ID获取费率，如果不可开票则不会返回费率
     */
    @GET(ENTERPRISE_BASE_URL + "/third/getTaxRate")
    Call<InternalWebResult<BigDecimal>> invoiceEnterpriseGetTaxRate(@Query("userId") Long userId, @Query("dominantId") Long dominantId);

  
    /**
     * 根据用户Id 查看企业相关信息
     * <AUTHOR>
     * @param userId 用户Id
     * @return Call<InternalWebResult<BigDecimal>>
     */
    @POST(BASE_URL + "/getInfoByUserId")
    Call<InternalWebResult<InvoiceEnterpriseInfoVO>> getInfoByUserId(@Query("userId") Long userId);

}
