package com.tyt.plat.client.user;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.vo.user.UserExposureInfoVO;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * 用户权限接口
 *
 * <AUTHOR>
 * @since 2024/09/23 19:47
 */
public interface ApiUserPermissionClient {

    String BASE_URL = InternalServiceConstant.USER_SERVICE_URL + "/rpc/user/permission";

    /**
     * 用户曝光卡权益相关信息
     */
    @GET(BASE_URL + "/coupon/goods/info")
    Call<UserExposureInfoVO> getGoodsCouponInfo(@Query("userId") Long userId);

}
