package com.tyt.plat.client.autocall;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.vo.esign.AutoCallTaskRequest;
import com.tyt.plat.vo.ocr.BusinessLicenseOcrRpcVO;
import com.tyt.plat.vo.ocr.RoadTransportBackOcrRpcVo;
import com.tyt.plat.vo.ocr.RoadTransportQuaCertOcrRpcVo;
import com.tyt.plat.vo.ocr.VehicleLicenseDeputyPageBackOcrRpcVo;
import org.springframework.web.bind.annotation.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * outer自动外呼
 *
 * <AUTHOR>
 * @since 2025-6-4 14:53:24
 */
public interface OuterAutoCallClient {

    String BASE_URL = InternalServiceConstant.OUTER_SERVICE_URL;

    /**
     * 道运证OCR.
     */
    @POST(BASE_URL + "/cticloud/task/autoCallTask")
    Call<Void> autoCallTask(@Body AutoCallTaskRequest autoCallTaskRequest);

}
