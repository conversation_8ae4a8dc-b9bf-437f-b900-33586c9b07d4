package com.tyt.plat.client.ocr;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.vo.ocr.BusinessLicenseOcrRpcVO;
import com.tyt.plat.vo.ocr.RoadTransportBackOcrRpcVo;
import com.tyt.plat.vo.ocr.RoadTransportQuaCertOcrRpcVo;
import com.tyt.plat.vo.ocr.VehicleLicenseDeputyPageBackOcrRpcVo;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * 集团OCR内部服务调用
 *
 * <AUTHOR>
 * @since 2024-6-4 14:53:24
 */
public interface GroupOcrClient {

    String BASE_URL = InternalServiceConstant.OUTER_SERVICE_URL + "/ocr";

    /**
     * 道运证OCR.
     */
    @GET(BASE_URL + "/roadTransportBackOcr")
    Call<RoadTransportBackOcrRpcVo> roadTransportBackOcr(@Query("url") String url);

    /**
     * 行驶证副页背面OCR.
     */
    @GET(BASE_URL + "/vehicleLicenseDeputyPageBackOcr")
    Call<VehicleLicenseDeputyPageBackOcrRpcVo> vehicleLicenseDeputyPageBackOcr(@Query("url") String url);

    /**
     * 道路运输从业资格证副页OCR.
     */
    @GET(BASE_URL + "/roadTransportQualificationCertificateBackOcr")
    Call<RoadTransportQuaCertOcrRpcVo> roadTransportQualificationCertificateBackOcr(@Query("url") String url);

    /**
     * 营业执照OCR.
     */
    @GET(BASE_URL + "/businessLicenseOcr")
    Call<BusinessLicenseOcrRpcVO> businessLicenseOcr(@Query("url") String url);
}
