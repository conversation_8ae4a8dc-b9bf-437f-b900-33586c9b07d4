package com.tyt.plat.client.distance;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.vo.distance.DistanceRpcReq;
import com.tyt.plat.vo.distance.DistanceRpcVO;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 腾讯位置服务，货车导航计算距离
 *
 * <AUTHOR>
 * @since 2025-02-07 10:37
 */
public interface TencentLocationClient {
    String BASE_URL = InternalServiceConstant.OUTER_SERVICE_URL;

    /**
     * 计算两个经纬度之间的导航距离
     *
     * <a href="https://lbs.qq.com/service/webService/webServiceGuide/route/directionTrucking#10">官方文档链接</a>
     */
    @POST(BASE_URL + "/distance/rpc/calculateDistance")
    Call<DistanceRpcVO> calculateDistance(@Body DistanceRpcReq req);
}
