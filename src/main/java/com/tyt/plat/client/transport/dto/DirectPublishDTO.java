package com.tyt.plat.client.transport.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 直接发布请求参数DTO
 *
 * <AUTHOR>
 * @since 2025/02/11 10:05
 */
@Getter
@Setter
public class DirectPublishDTO {

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 是否是小程序货源
     */
    private Integer isBackendTransport;

    /**
     * 转一口价/电议 货源类型（电议1，一口价2） 可传 null
     */
    private Integer publishType;

    /**
     * 修改后价格，可传null
     */
    private String price;

    // ====================================================

    /**
     * 加价操作是否需要校验时间间隔
     */
    private Boolean isCheckAddPriceInterval;

}
