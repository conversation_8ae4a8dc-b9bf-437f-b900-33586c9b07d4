package com.tyt.plat.client.transport.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 货源扩展表
 *
 * <AUTHOR>
 * @since 2025-07-25 14:22
 */
@Data
public class TransportMainExtendDTO {
    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 运价模式：1-固定运价，2-灵活运价
     */
    private Integer priceType;

    /**
     * 最低建议价，优车定价最低值，特惠优车价，=fixPriceMin
     */
    private Integer suggestMinPrice;

    /**
     * 最高建议价，优车定价最高值，极速优车价，=fixPriceMax
     */
    private Integer suggestMaxPrice;

    /**
     * 优车定价最快成交价格，快速优车价，=fixPriceFast
     */
    private Integer fixPriceFast;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 好货模型分数
     */
    private BigDecimal goodModelScore;

    /**
     * 好货模型等级
     */
    private Integer goodModelLevel;


    /**
     * 好差货模型分数
     */
    private BigDecimal limGoodModelScore;

    /**
     * 好差货模型等级
     */
    private Integer limGoodModelLevel;

    /**
     * 回价助手运费上限
     */
    private Integer priceCap;

    /**
     * 好货运价模型分数（抽佣）
     */
    private BigDecimal commissionScore;

    /**
     * 秒抢货源：1是0否
     */
    private Integer seckillGoods;

    /**
     * 优惠价格
     */
    private Integer perkPrice;

    /**
     * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
     */
    private Integer goodTransportLabel;

    /**
     * 货参不完整的好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0-其他
     */
    private Integer goodTransportLabelPart;

    /**
     * 是否是融合发货 1是0否
     */
    private Integer clientFusion;
}
