package com.tyt.plat.client.transport.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 拼车判断
 *
 * <AUTHOR>
 * @since 2025-03-04 14:14
 */
@Data
public class CarpoolMatchDTO {
    private Long userId;
    /**
     * 出发地城市
     */
    private String startCity;
    /**
     * 目的地城市
     */
    private String destCity;
    /**
     * 货物吨重
     */
    private BigDecimal weight;
    /**
     * 货源ID
     */
    private Long srcMsgId;
    /**
     * 距离
     */
    private BigDecimal distance;
    /**
     * 是否是开票货源:0-否，1-是
     */
    private Integer invoiceTransport;
}
