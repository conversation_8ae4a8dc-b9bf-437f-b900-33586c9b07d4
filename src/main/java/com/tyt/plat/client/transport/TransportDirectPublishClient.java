package com.tyt.plat.client.transport;

import com.tyt.plat.client.transport.dto.*;
import com.tyt.plat.commons.internal.InternalServiceConstant;
import org.springframework.web.bind.annotation.RequestBody;
import retrofit2.http.POST;

public interface TransportDirectPublishClient {

    String BASE_URL = InternalServiceConstant.GOODS_SERVICE_URL;

    /**
     * 直接发布
     */
    @POST(BASE_URL + "/rpc/transport/direct/publish/directPublish")
    DirectPublishResultVO directPublish(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 填价/加价接口
     */
    @POST(BASE_URL + "/rpc/transport/direct/publish/updatePrice")
    DirectPublishResultVO updatePrice(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 转电议/一口价
     */
    @POST(BASE_URL + "/rpc/transport/direct/publish/transfer")
    DirectPublishResultVO transfer(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 更新货源信息
     */
    @POST(BASE_URL + "/rpc/transport/direct/publish/updateGoodsInfo")
    DirectPublishResultVO updateGoodsInfo(@RequestBody UpdateGoodsInfoDTO updateGoodsInfoDTO);

}
