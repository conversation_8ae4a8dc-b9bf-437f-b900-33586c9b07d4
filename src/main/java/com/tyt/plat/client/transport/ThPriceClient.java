package com.tyt.plat.client.transport;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.transport.querybean.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

public interface ThPriceClient {

    String BASE_URL = InternalServiceConstant.GOODS_SERVICE_URL;

    @POST(BASE_URL + "/rpc/thPrice/getThPrice")
    Call<CarryPriceVo> getThPrice(@Body TransportCarryBean transportCarryBean);

    @POST(BASE_URL + "/rpc/price/changePirceLog")
    Call<Void> changePirceLog(@Body ChangePriceLogReq changePriceLogReq);

    @POST(BASE_URL + "/rpc/thPrice/isHavePriceModelTwo")
    Call<Boolean> isHavePriceModelTwo(@Body TransportCarryBean transportCarryBean);

    @POST(BASE_URL + "/rpc/transportMain/getSimilarityTransportHavePriceCount")
    Call<Boolean> getSimilarityTransportHavePriceCount(@Query("similarityCode") String similarityCode);

    @GET(BASE_URL + "/rpc/transportStatus/saveGoodsStatus")
    Call<Void> saveGoodsStatus(@Query("userId") Long userId, @Query("srcMsgId") Long srcMsgId, @Query("operateType") Integer operateType, @Query("backoutReasonKey") String backoutReasonKey, @Query("backoutReasonValue") Integer backoutReasonValue, TransportDoneRequest doneRequest);

    /**
     * 查询是否允许发布优车2.0电议货源
     *
     * @return -1是发布受限，0次数用完，>0是剩余次数
     */
    @GET(BASE_URL + "/rpc/goodCarPrice/getRemainCount")
    Call<Integer> getRemainCount(@Query("userId") Long userId);

    /**
     * 保存用户发布优车2.0电议货源次数
     */
    @GET(BASE_URL + "/rpc/goodCarPrice/saveTransferTeleUseCount")
    Call<Void> saveTransferTeleUseCount(@Query("userId") Long userId, @Query("srcMsgId") Long srcMsgId);

    /**
     * 保存用户发布优车2.0电议货源次数
     */
    @GET(BASE_URL + "/rpc/goodCarPrice/saveTransferTeleDealCount")
    Call<Void> saveTransferTeleDealCount(@Query("userId") Long userId, @Query("srcMsgId") Long srcMsgId);

    /**
     * 查询货车导航距离
     */
    @POST(BASE_URL + "/rpc/navigation/distance")
    Call<NavigationResultVO> navigationDistance(@Body NavigationQueryDTO navQueryDTO);
}
