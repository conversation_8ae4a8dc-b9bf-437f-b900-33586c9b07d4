package com.tyt.plat.client.transport;

import com.tyt.plat.client.transport.dto.BenefitLabelVO;
import com.tyt.plat.client.transport.dto.CheckIsNeedFreeTecServiceFeeVO;
import com.tyt.plat.commons.internal.InternalServiceConstant;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @since 2025/05/19 17:23
 */
public interface TransportSearchClient {

    String BASE_URL = InternalServiceConstant.GOODS_SEARCH_URL;

    @GET(BASE_URL + "/rpc/transport/label/getTransportBenefitLabel")
    Call<BenefitLabelVO> getTransportBenefitLabel(@Query("srcMsgId") Long srcMsgId);


}
