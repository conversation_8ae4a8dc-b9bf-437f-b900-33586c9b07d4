package com.tyt.plat.client.transport.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 货源发布后需要更新的数据
 *
 * <AUTHOR>
 * @since 2024/08/05 17:53
 */
@Getter
@Setter
public class UpdateGoodsInfoDTO {

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 货物长单位米
     */
    private String length;

    /**
     * 货物宽单位米
     */
    private String wide;

    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 货物重量单位吨
     */
    private String weight;

    // ====目的地信息=====

    /**
     * 目的地纬度
     */
    private BigDecimal destLatitude;
    /**
     * 目的地经度
     */
    private BigDecimal destLongitude;
    /**
     * 目的地X坐标
     */
    private BigDecimal destCoordX;
    /**
     * 目的地Y坐标
     */
    private BigDecimal destCoordY;

    // 目的地完整地址/省/市/县/详细地址
    private String destPoint;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private String destDetailAdd;

    /**
     * 距离
     */
    private BigDecimal distance;

}
