package com.tyt.plat.client.transport;

import com.tyt.plat.client.transport.dto.CarpoolMatchDTO;
import com.tyt.plat.client.transport.dto.CheckIsNeedFreeTecServiceFeeVO;
import com.tyt.plat.client.transport.dto.TecServiceFeeComputeDTO;
import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.vo.TecServiceFeeConfigComputeResultVO;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

public interface TransportTecserviceFeeClient {

    String BASE_URL = InternalServiceConstant.GOODS_SERVICE_URL;

    @GET(BASE_URL + "/rpc/checkIsNeedFreeTecSericeFeeByCarUser")
    Call<CheckIsNeedFreeTecServiceFeeVO> checkIsNeedFreeTecSericeFeeByCarUser(@Query("carUserId") Long carUserId, @Query("srcMsgId") Long srcMsgId);

    @GET(BASE_URL + "/rpc/checkIsNeedFreeTecSericeFeeByTransport")
    Call<CheckIsNeedFreeTecServiceFeeVO> checkIsNeedFreeTecSericeFeeByTransport(@Query("transportUserId") Long transportUserId
            , @Query("startCity") String startCity, @Query("isGoodCarPriceTransport") boolean isGoodCarPriceTransport);

    @POST(BASE_URL + "/rpc/commission/computeTecServiceFeeByTransportData")
    Call<TecServiceFeeConfigComputeResultVO> computeTecServiceFeeByTransportData(@Body TecServiceFeeComputeDTO computeDTO);

    @POST(BASE_URL + "/rpc/transportMain/checkMatchCarpool")
    Call<Boolean> checkMatchCarpool(@Body CarpoolMatchDTO dto);


    @GET(BASE_URL + "/rpc/exposureCard/giveaway/checkAndSave")
    Call<Boolean> checkAndSaveExposureCardGiveaway(@Query("srcMsgId") Long srcMsgId);

}
