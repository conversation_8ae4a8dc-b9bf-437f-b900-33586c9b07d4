package com.tyt.plat.client.trade.infofee.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
* 根据条件查看用户履约单量
* <AUTHOR>
* @since 2024/8/12 18:12
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserPerformanceNumDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户类型 null或0(车主和货主) 1车主 2货主
     */
    private Integer userType;

    /**
     * 订单创建时间tyt_transport_orders表create_time
     */
    private Date createTime;

    /**
     * 是否去除风险单 0或null不去除 1去除
     */
    private Integer riskOrderFlag;

}
