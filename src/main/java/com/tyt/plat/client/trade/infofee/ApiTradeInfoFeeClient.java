package com.tyt.plat.client.trade.infofee;

import com.tyt.acvitity.bean.TransportOrders;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;
import com.tyt.plat.client.trade.infofee.dto.AssignOrdersSaveBillDTO;
import com.tyt.plat.client.trade.infofee.dto.UserPerformanceNumDTO;
import com.tyt.plat.commons.internal.InternalServiceConstant;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.List;
import java.util.Map;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 开票-用户中心接口对接
 *
 * <AUTHOR>
 * @since 2024/7/18 15:45
 */
public interface ApiTradeInfoFeeClient {

    String BASE_URL = InternalServiceConstant.TRADE_SERVICE_URL;


    @POST(BASE_URL + "/trade/orders/getTsIdListByUserId")
    @FormUrlEncoded
    Call<Map<Long, List<Long>>> getTsIdListByUserId(@Field("userIds") List<Long> userIds);

    /**
     * 批量查询车主是否存在承运中或待支付的专车订单
     *
     * @param payUserIds            车主id列表
     * @return Map<Long,Boolean>    key=车主id,value=是否存在承运中或待支付的专车订单
     */
    @POST(BASE_URL + "/trade/orders/getOrderListByPayUserIds")
    @FormUrlEncoded
    Call<Map<Long, Boolean>> getOrderListByPayUserIds(@Field("payUserIds") List<Long> payUserIds);

    /**
     * 根据suerId和用户类型 查看用户履约量
     *
     * @param userPerformanceNumDTO 入参
     * @return
     */

    @POST(BASE_URL + "/trade/infoFee/getUserPerformanceNum")
    Call<Integer> getUserPerformanceNum(@Body UserPerformanceNumDTO userPerformanceNumDTO);

    @POST(BASE_URL + "/trade/assign/orders/saveWayBill")
    Call<ResultMsgBean> invoiceTransportAssignCar(@Body AssignOrdersSaveBillDTO assignOrdersSaveBillDTO);

    @GET(BASE_URL + "/trade/orders/getByTsId")
    Call<List<TytTransportOrders>> getByTsId(@Query("srcMsgId") Long srcMsgId);


    /**
     * 订单取消
     *
     * @param TransportOrders
     * @return
     */
    @POST(BASE_URL + "/trade/cancel/third/order")
    Call<ResultMsgBean> cancelThirdOrder(@Body TytTransportOrders TransportOrders);

}
