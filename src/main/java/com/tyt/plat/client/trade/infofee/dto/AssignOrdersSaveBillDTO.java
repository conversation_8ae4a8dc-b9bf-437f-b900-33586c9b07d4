package com.tyt.plat.client.trade.infofee.dto;

import lombok.Data;

import java.io.Serializable;


/**
* 指派单0元支付保存订单请求入参
* <AUTHOR>
* @since 2024/10/29 14:06
*/
@Data
public class AssignOrdersSaveBillDTO implements Serializable {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * tyt_transport_main表主键Id
     */
    private Long tsId;
    /**
     * 指派手机号
     */
    private String cellPhone;

    /**
     *  运前信息费（单位元）
     */
    private Long agencyMoney;

    /**
     *入口 先默认99
     */
    private Integer carOwnerPayType;

    /**
     * 运费（单位元）
     */
    private Integer  carriageFee;

    /**
     * 技术服务费（单位元）
     */
    private Long tecServiceFee;

}
