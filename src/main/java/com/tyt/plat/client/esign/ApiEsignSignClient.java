package com.tyt.plat.client.esign;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.vo.esign.LocalSafeSignPDF3rdVO;
import com.tyt.plat.vo.esign.SilentUserSealSignReq;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 易签宝-电子签名
 * <p>
 * 每个接口的说明都是超链接，点击可以跳转官方文档
 *
 * <AUTHOR>
 * @since 2024-5-21 11:30:04
 */
public interface ApiEsignSignClient {

    String BASE_URL = InternalServiceConstant.OUTER_SERVICE_URL + "/esignSign";

    /**
     * 平台用户PDF文件签署(无意愿)
     *
     * <a href="https://qianxiaoxia.yuque.com/opendoc/pv66r3/wq86pt">官方文档链接</a>
     */
    @POST(BASE_URL + "/silentUserSealSign")
    Call<InternalWebResult<LocalSafeSignPDF3rdVO>> silentUserSealSign(@Body SilentUserSealSignReq req);

}
