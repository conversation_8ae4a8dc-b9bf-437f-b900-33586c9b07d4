package com.tyt.plat.client.push;

import com.tyt.plat.client.push.dto.GoodsPushDto;
import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.commons.internal.InternalWebResult;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;


public interface ApiInnerPushClient {

    String BASE_URL = InternalServiceConstant.INNER_SERVICE_URL;

    @POST(BASE_URL + "/rpc/goods/push/sendPush")
    Call<InternalWebResult<Void>> sendPush(@Body GoodsPushDto goodsPushDto);

}
