package com.tyt.plat.client.push.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 推荐货源推送类型枚举
 *
 * <AUTHOR>
 * @since 2024-11-04 09:48
 */
@Getter
@AllArgsConstructor
public enum PushCodeEnum {
    RECOMMEND_GOODS_PUSH("recommend_goods_push", "优推好货推送"),
    COMMISSION_FREE_PUSH("commission_free_push", "免佣货源推送"),
    SUBSCRIBE_GOODS_PUSH("subscribe_goods_push", "好货订阅推送"),
    RECOMMEND_ORDER_PUSH("recommend_order_push", "订单相关推送");

    private final String code;
    private final String name;
}
