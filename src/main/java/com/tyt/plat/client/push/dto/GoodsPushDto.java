package com.tyt.plat.client.push.dto;

import lombok.Data;

import java.util.List;

/**
 * 货源推送实体
 *
 * <AUTHOR>
 * @since 2024-10-31 11:05
 */
@Data
public class GoodsPushDto {
    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 用户ID
     */
    private List<Long> userIdList;

    /**
     * 推送标题（第一行）
     */
    private String title;

    /**
     * 推送内容（第二行）
     */
    private String content;

    /**
     * 推送信息（第三行）
     */
    private String infoText;

    /**
     * 推送来源(eg: 高质量货源推送)
     */
    private String pushSource;

    /**
     * 推送类型：1-站内，2-站外，3-站内+站外
     */
    private PushTypeEnum pushType;

    /**
     * 推送code，站内push客户端根据code区分
     */
    private PushCodeEnum pushCode;

    /**
     * 站内push客户端自定义信息
     */
    private String extraJson;
}
