package com.tyt.plat.client.invoice;

import com.tyt.plat.commons.internal.InternalServiceConstant;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.vo.invoice.CreateWaybillThreeRequest;
import com.tyt.plat.vo.invoice.CreateWaybillThreeResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 开票-对接三方服务客户端
 *
 * <AUTHOR>
 * @since 2024/7/18 15:48
 */
public interface InvoiceOpenApiClient {

    String BASE_URL = InternalServiceConstant.OUTER_SERVICE_URL;

    /**
     * 创建运单接口
     *
     * <AUTHOR>
     * @param createWaybillThreeRequest 创建运单请求对象
     * @return Call<CreateWaybillThreeResponse>
     */
    @POST(BASE_URL + "/invoice/wj/addWaybillThree")
    Call<InternalWebResult<CreateWaybillThreeResponse>> addWaybillThree(@Body CreateWaybillThreeRequest createWaybillThreeRequest);

}