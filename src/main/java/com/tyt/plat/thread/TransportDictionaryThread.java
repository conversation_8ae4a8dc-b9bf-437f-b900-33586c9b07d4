package com.tyt.plat.thread;

import com.tyt.plat.service.dic.TransportDictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/5/10 16:31
 */
@Slf4j
@Component
public class TransportDictionaryThread extends Thread{

    /**
     * 最后一次词典刷新时间
     */
    private static volatile long lastTimeStamp = 0;

    @Autowired
    private TransportDictionaryService transportDictionaryService;

//    public TransportDictionaryThread(TransportDictionaryService transportDictionaryService) {
//        this.transportDictionaryService = transportDictionaryService;
//    }

    @Override
    public void run() {

        long nowTimeStamp = System.currentTimeMillis();

        try {
            boolean dicChange = transportDictionaryService.checkDictionaryChange(lastTimeStamp);

            if(dicChange){

                log.info("########## transportDictionary_newChange_start ==================== ");

                transportDictionaryService.refreshDictionary();

                log.info("########## transportDictionary_newChange_end ==================== ");
            }

            lastTimeStamp = nowTimeStamp;
        } catch (Exception e) {
            log.error("TransportDictionaryThread_run_error : ", e);
        }

    }


}
