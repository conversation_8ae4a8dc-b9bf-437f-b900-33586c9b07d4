package com.tyt.plat.service.block;

import com.tyt.model.User;
import com.tyt.plat.entity.base.CurrentLocationVO;
import com.tyt.plat.entity.base.TytBlockConfig;
import com.tyt.plat.entity.base.TytSigningCarVO;
import com.tyt.plat.entity.base.TytSpecialCarAutoDispatchConfig;
import com.tyt.transport.bean.AssignableCarFilterBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**阻断配置
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/05/10 13:08
 */
public interface TytBlockConfigService {
    TytBlockConfig getByUserConfig(User user);

    List<TytSigningCarVO> getCarUser(String ids, String city, String area);

    List<TytSigningCarVO> getAssignableCars(AssignableCarFilterBean filterBean, TytSpecialCarAutoDispatchConfig dispatchConfig);

    List<TytSigningCarVO> getSigningCarList(List<TytSigningCarVO> signingCarList);

    Boolean getSigningCarBlack(Long userId);

    List<CurrentLocationVO> getBICarLocal(String carNos);

    void updateDriverAssignNum(Long carInfoId);
}
