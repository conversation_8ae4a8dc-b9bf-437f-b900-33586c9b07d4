package com.tyt.plat.service.block.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.common.service.TytMapDictService;
import com.tyt.model.User;
import com.tyt.plat.client.distance.TencentLocationClient;
import com.tyt.plat.client.trade.infofee.ApiTradeInfoFeeClient;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.BlockConfigEnum;
import com.tyt.plat.enums.DriverVerifyEnum;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.block.TytBlockConfigService;
import com.tyt.plat.vo.distance.DistanceRpcReq;
import com.tyt.plat.vo.distance.DistanceRpcVO;
import com.tyt.plat.vo.other.SigningCarInfoVo;
import com.tyt.transport.bean.AssignableCarFilterBean;
import com.tyt.transport.enums.AssignCarEnum;
import com.tyt.transport.enums.DistancePreferenceEnum;
import com.tyt.transport.enums.DriverDrivingEnum;
import com.tyt.transport.enums.ExcellentGoodsEnums;
import com.tyt.user.service.CarCurrentLocationService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.AddressCuttingUtil;
import com.tyt.util.ListUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**阻断配置
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/05/10 13:08
 */
@Service
@Slf4j
public class TytBlockConfigServiceImpl implements TytBlockConfigService {

    @Autowired
    private TytBlockConfigMapper tytBlockConfigMapper;

    @Autowired
    private TytBlockConfigUserMapper tytBlockConfigUserMapper;

    @Autowired
    private TytSigningCarMapper tytSigningCarMapper;

    @Autowired
    private DispatchCargoOwnerMapper dispatchCargoOwnerMapper;
    @Autowired
    private TytDispatchCooperativeMapper tytDispatchCooperativeMapper;
    @Autowired
    private TytTransportMainMapper tytTransportMainMapper;
    @Autowired
    private ApiTradeInfoFeeClient apiTradeInfoFeeClient;
    @Resource
    private GoodsAddressLevelRecordMapper goodsAddressLevelRecordMapper;
    @Autowired
    private TencentLocationClient tencentLocationClient;
    @Resource(name = "carCurrentLocationService")
    private CarCurrentLocationService carCurrentLocationService;

    @Autowired
    private TytMapDictService tytMapDictService;
    @Autowired
    private TytConfigService tytConfigService;
    /**
     * 货主测试账号
     */
    private static final String CARGO_OWNER_TEST_ACCOUNT = "cargo_owner_test_user_ids";
    /**
     * 距离查询开关：0-本地距离表，1-腾讯货车导航
     */
    private static final String SPECIAL_CAR_DISTANCE_SWITCH = "special_car_distance_switch";

    private static final String SPECIAL_CAR_LAST_LOCATION_SWITCH = "special_car_last_location_switch";
    /**
     * 距离阈值
     */
    private static final String DISTANCE_THRESHOLD = "70";
    /**
     * 司机匹配数量阈值
     */
    private static final int DRIVER_MATCH_THRESHOLD = 20;
    private static final Pattern ADDRESS_PATTERN = Pattern.compile("(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)(?<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)?(?<town>[^区]+区|.+镇)?(?<village>.*)");

    @Override
    public TytBlockConfig getByUserConfig(User user) {
        //先查询开启中的配置
        List<TytBlockConfig> list = tytBlockConfigMapper.getConfig();
        //无配置，直接放行
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        //有配置，开始循环判断符合条件的配置
        for (TytBlockConfig config : list) {
            //判断是否名单上传的配置
            if (BlockConfigEnum.CONFIG_TYPE_THREE.getStatus().equals(config.getConfigType())) {
                //如果有名单上传的配置，查询用户id是否在名单中
                TytBlockConfigUser byIdAndUserId = tytBlockConfigUserMapper.getByIdAndUserId(user.getId(), config.getId());
                if (null != byIdAndUserId) {
                    return config;
                }
            }
            //判断是否手机号归属地
            if (BlockConfigEnum.CONFIG_TYPE_TWO.getStatus().equals(config.getConfigType())) {
                //配置的市
                String configCity = config.getCity();
                //配置的省
                String configProvince = config.getProvince();
                //市不为空的时候，只以市为准
                if (StringUtils.isNotEmpty(configCity) && configCity.contains(user.getCity())) {
                    return config;
                }
                //市为空的时候，以省为准
                if (StringUtils.isEmpty(configCity) &&
                        StringUtils.isNotEmpty(configProvince) && configProvince.equals(user.getProvince())) {
                    return config;
                }
            }
            //如果类型是全部则直接返回
            if (BlockConfigEnum.CONFIG_TYPE_ONE.getStatus().equals(config.getConfigType())){
                return config;
            }
        }
        return null;
    }

    /**
     * 获取满足指派条件的司机ID集合
     *
     * @param filterBean
     * @return
     */
    @Override
    public List<TytSigningCarVO> getAssignableCars(AssignableCarFilterBean filterBean,
                                                   TytSpecialCarAutoDispatchConfig dispatchConfig) {
        log.info("专车发货自动派单，获取满足条件的司机列表参数:{}", JSON.toJSONString(filterBean));
        if (StringUtils.isEmpty(filterBean.getStartCity()) && StringUtils.isEmpty(filterBean.getDestCity())) {
            return Collections.emptyList();
        }
        // 在配置中的测试账号，不走司机圈选逻辑
        String cargoOwnerTestAccount = tytConfigService.getStringValue(CARGO_OWNER_TEST_ACCOUNT, "0");
        if (StringUtils.isNotBlank(cargoOwnerTestAccount)) {
            List<String> cargoOwnerTestIds = Arrays.asList(cargoOwnerTestAccount.split(","));
            if (CollectionUtils.isNotEmpty(cargoOwnerTestIds) && cargoOwnerTestIds.contains(filterBean.getUserId().toString())) {
                log.info("专车发货自动派单，在配置中的测试货主账号不走司机圈选，货主ID:{}", filterBean.getUserId());
                return Collections.emptyList();
            }
        }

        List<TytSigningCarVO> list = new ArrayList<>();
        // 筛选满足派单条件的司机：路线 > 城市 > 省份 > 全国
        List<SigningCarInfoVo> assignableCars = filterCarsByCondition(filterBean, dispatchConfig);
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            assignableCars.forEach(e -> {
                TytSigningCarVO vo = new TytSigningCarVO();
                vo.setId(e.getCarInfoId());
                vo.setLocation(e.getLocation());
                vo.setDistance(e.getDistance());
                vo.setUserId(e.getUserId());
                vo.setPhone(e.getPhone());
                vo.setDriverId(e.getDriverId());
                vo.setDriverUserId(e.getDriverUserId());
                vo.setFavorableComment(e.getFavorableComment());
                vo.setReceivingOrders(e.getReceivingOrders());
                vo.setHeadCityNo(e.getHeadCityNo());
                vo.setTailCityNo(e.getTailCityNo());
                vo.setCarType(e.getCarType());
                vo.setDriverTag(e.getDriverTag());
                list.add(vo);
            });
        }
        return list;
    }

    /**
     * 根据条件筛选满足派单条件的车辆
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoVo>  filterCarsByCondition(AssignableCarFilterBean filterBean,
                                                          TytSpecialCarAutoDispatchConfig dispatchConfig) {
        String distanceThreshold = DISTANCE_THRESHOLD;
        if (!Objects.equals(filterBean.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) &&
                Objects.nonNull(filterBean.getDistanceLimit())) {
            distanceThreshold = String.valueOf(filterBean.getDistanceLimit());
        }

        int maxDispatchNum = DRIVER_MATCH_THRESHOLD;
        if (Objects.nonNull(dispatchConfig)) {
            distanceThreshold = String.valueOf(dispatchConfig.getDistanceLimit());
            maxDispatchNum = dispatchConfig.getMaxDispatchNum();
        }

        Integer distanceSwitch = tytConfigService.getIntValue(SPECIAL_CAR_DISTANCE_SWITCH, 1);

        // 根据路线筛选司机，获取距离
        List<SigningCarInfoVo> assignableCars = filterByRoute(filterBean, distanceThreshold, dispatchConfig, distanceSwitch);

        if (assignableCars.size() < maxDispatchNum) {
            // 路线匹配距离小于70公里的车辆小于20个，根据城市再筛选司机
            filterByCity(filterBean, distanceThreshold, assignableCars, dispatchConfig, distanceSwitch);
        }

        if (assignableCars.size() < maxDispatchNum) {
            // 路线和城市匹配距离小于70公里的车辆小于20个，根据省份再筛选司机
            filterByProvince(filterBean, distanceThreshold, assignableCars, dispatchConfig, distanceSwitch);
        }

        // if (assignableCars.size() < maxDispatchNum) {
        //     // 路线和城市和省份筛选后的司机数量还是小于20，需要筛选城市配置为全国的司机
        //     filterByCountry(filterBean, distanceThreshold, assignableCars, dispatchConfig, distanceSwitch);
        // }
        return assignableCars;
    }

    /**
     * 根据路线筛选司机
     *
     * @param filterBean
     * @param distanceThreshold
     * @param dispatchConfig
     * @return
     */
    private List<SigningCarInfoVo> filterByRoute(AssignableCarFilterBean filterBean,
                                                 String distanceThreshold,
                                                 TytSpecialCarAutoDispatchConfig dispatchConfig,
                                                 Integer distanceSwitch) {
        List<SigningCarInfoVo> assignableCars = getAssignableCarsByRoute(filterBean);

        assignableCars = filterByDispatchConfig(dispatchConfig, assignableCars, filterBean);

        getLocationAndDistance(filterBean, assignableCars, distanceSwitch);
        assignableCars = assignableCars.stream()
                .filter(e -> Objects.nonNull(e.getDistance()) && e.getDistance().compareTo(new BigDecimal(distanceThreshold)) <= 0)
                .collect(Collectors.toList());
        log.info("专车发货自动派单，根据路线筛选司机，货源ID：{}, 出发地:{}，目的地:{}，司机距离限制:{}, 数量：{}, 结果:{}", filterBean.getSrcMsgId(),
                filterBean.getStartCity(), filterBean.getDestCity(), distanceThreshold, assignableCars.size(), JSON.toJSONString(assignableCars));
        return assignableCars;
    }

    /**
     * 根据城市再筛选司机
     *
     * @param filterBean
     * @param distanceThreshold
     * @param assignableCars
     * @param dispatchConfig
     */
    private void filterByCity(AssignableCarFilterBean filterBean,
                              String distanceThreshold,
                              List<SigningCarInfoVo> assignableCars,
                              TytSpecialCarAutoDispatchConfig dispatchConfig,
                              Integer distanceSwitch) {
        List<SigningCarInfoVo> assignableCarsByCity = getAssignableCarsByCity(filterBean);
        // 去除路线筛选已经筛选出来的司机
        assignableCarsByCity.removeAll(assignableCars);

        assignableCarsByCity = filterByDispatchConfig(dispatchConfig, assignableCarsByCity, filterBean);

        getLocationAndDistance(filterBean, assignableCarsByCity, distanceSwitch);
        assignableCarsByCity = assignableCarsByCity.stream()
                .filter(e -> Objects.nonNull(e.getDistance()) && e.getDistance().compareTo(new BigDecimal(distanceThreshold)) <= 0)
                .collect(Collectors.toList());
        log.info("专车发货自动派单，根据城市筛选司机，货源ID：{}, 出发地市:{}，目的地市:{}，司机距离限制:{}, 数量：{}, 结果:{}", filterBean.getSrcMsgId(),
                filterBean.getStartCity(), filterBean.getDestCity(), distanceThreshold, assignableCarsByCity.size(),
                JSON.toJSONString(assignableCarsByCity));
        assignableCars.addAll(assignableCarsByCity);
    }

    /**
     * 根据省份再筛选司机
     *
     * @param filterBean
     * @param distanceThreshold
     * @param assignableCars
     * @param dispatchConfig
     */
    private void filterByProvince(AssignableCarFilterBean filterBean,
                                  String distanceThreshold,
                                  List<SigningCarInfoVo> assignableCars,
                                  TytSpecialCarAutoDispatchConfig dispatchConfig,
                                  Integer distanceSwitch) {
        List<SigningCarInfoVo> assignableCarsByProvince = getAssignableCarsByProvince(filterBean);
        // 去除路线筛选已经筛选出来的司机
        assignableCarsByProvince.removeAll(assignableCars);

        assignableCarsByProvince = filterByDispatchConfig(dispatchConfig, assignableCarsByProvince, filterBean);

        getLocationAndDistance(filterBean, assignableCarsByProvince, distanceSwitch);
        assignableCarsByProvince = assignableCarsByProvince.stream()
                .filter(e -> Objects.nonNull(e.getDistance()) && e.getDistance().compareTo(new BigDecimal(distanceThreshold)) <= 0)
                .collect(Collectors.toList());
        log.info("专车发货自动派单，根据省份筛选司机，货源ID：{}, 出发地省:{}，目的地省:{}，司机距离限制:{}, 数量：{}, 结果:{}", filterBean.getSrcMsgId(),
                filterBean.getStartProvince(), filterBean.getDestProvince(), distanceThreshold, assignableCarsByProvince.size(),
                JSON.toJSONString(assignableCarsByProvince));
        assignableCars.addAll(assignableCarsByProvince);
    }

    /**
     * 筛选城市配置为全国的司机
     *
     * @param filterBean
     * @param distanceThreshold
     * @param assignableCars
     * @param dispatchConfig
     */
    private void filterByCountry(AssignableCarFilterBean filterBean,
                                 String distanceThreshold,
                                 List<SigningCarInfoVo> assignableCars,
                                 TytSpecialCarAutoDispatchConfig dispatchConfig,
                                 Integer distanceSwitch) {
        List<SigningCarInfoVo> assignableCarsByCountry = getAssignableCarsByCountry(filterBean);
        // 去除路线和城市已经筛选出来的司机
        assignableCarsByCountry.removeAll(assignableCars);

        assignableCarsByCountry = filterByDispatchConfig(dispatchConfig, assignableCarsByCountry, filterBean);

        getLocationAndDistance(filterBean, assignableCarsByCountry, distanceSwitch);
        assignableCarsByCountry = assignableCarsByCountry.stream()
                .filter(e -> Objects.nonNull(e.getDistance()) && e.getDistance().compareTo(new BigDecimal(distanceThreshold)) <= 0)
                .collect(Collectors.toList());
        log.info("专车发货自动派单，筛选城市配置有全国的司机，货源ID：{}, 出发地:{}，目的地:{}，司机距离限制:{}, 数量：{}, 结果:{}", filterBean.getSrcMsgId(),
                filterBean.getStartCity(), filterBean.getDestCity(), distanceThreshold, assignableCarsByCountry.size(),
                JSON.toJSONString(assignableCarsByCountry));
        assignableCars.addAll(assignableCarsByCountry);
    }

    /**
     * 根据专车派单配置过滤
     *
     * @param dispatchConfig
     * @param assignableCars
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoVo> filterByDispatchConfig(TytSpecialCarAutoDispatchConfig dispatchConfig,
                                                          List<SigningCarInfoVo> assignableCars,
                                                          AssignableCarFilterBean filterBean) {
        if (!Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), filterBean.getExcellentGoods())) {
            return assignableCars;
        }

        if (Objects.isNull(dispatchConfig) || Objects.isNull(dispatchConfig.getFavorRate()) || Objects.isNull(dispatchConfig.getReceiveRate())) {
            return assignableCars;
        }
        assignableCars = assignableCars.stream()
                .filter(v -> {
                    if (Objects.nonNull(v.getFavorableComment()) && Objects.nonNull(v.getReceivingOrders())) {
                        return v.getFavorableComment().compareTo(dispatchConfig.getFavorRate()) >= 0 &&
                                v.getReceivingOrders().compareTo(dispatchConfig.getReceiveRate()) >= 0;
                    }
                    return false;
                }).collect(Collectors.toList());
        return assignableCars;
    }

    /**
     * 根据省份筛选司机
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoVo> getAssignableCarsByProvince(AssignableCarFilterBean filterBean) {
        List<String> provinces = new ArrayList<>();
        provinces.add(filterBean.getStartProvince());
        provinces.add(filterBean.getDestProvince());

        List<SigningCarInfoVo> assignableCars = tytSigningCarMapper.getAssignableCarsByProvince(provinces);
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            assignableCars = filterAssignableCars(filterBean, assignableCars);
        } else {
            assignableCars = new ArrayList<>();
        }
        return assignableCars;
    }

    /**
     * 获取车辆位置和距离
     *
     * @param filterBean
     * @param assignableCars
     */
    private void getLocationAndDistance(AssignableCarFilterBean filterBean, List<SigningCarInfoVo> assignableCars, Integer distanceSwitch) {
        Map<String,CurrentLocationVO> map = new HashMap<>();
        // 调用bi获取车头位置信息
        getHeadLocation(filterBean, assignableCars, map);

        for (SigningCarInfoVo vo : assignableCars) {
            if (Objects.equals(distanceSwitch, 1)) {
                getDistanceByLocationV2(filterBean, map, vo);
            } else {
                getDistanceByLocation(filterBean, map, vo);
            }
        }
    }

    /**
     * 根据位置获取距离，调用腾讯地图距离矩阵接口
     * https://lbs.qq.com/service/webService/webServiceGuide/route/directionTrucking#10.1
     *
     * @param filterBean
     * @param map
     * @param vo
     */
    private void getDistanceByLocationV2(AssignableCarFilterBean filterBean, Map<String, CurrentLocationVO> map, SigningCarInfoVo vo) {
        CurrentLocationVO locationVO = map.get(vo.getHeadCityNo());
        if (Objects.isNull(locationVO)) {
            log.info("专车发货自动派单，未获取到车头位置，货源ID:{}, 车头信息:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                    vo.getHeadCityNo(), filterBean.getStartCity(), filterBean.getDestCity());
            return;
        }
        vo.setLocation(locationVO.getNewLocation());

        if (StringUtils.isBlank(locationVO.getLocationLatitude()) || StringUtils.isBlank(locationVO.getLocationLongitude()) ||
                Objects.equals(locationVO.getLocationLatitude(), "0.000000") ||
                Objects.equals(locationVO.getLocationLongitude(), "0.000000")) {
            log.info("专车发货自动派单，未获取到车头经纬度，货源ID:{}, 车头信息:{}, 出发地:{}, 目的地:{}，位置信息:{}", filterBean.getSrcMsgId(),
                    vo.getHeadCityNo(), filterBean.getStartCity(), filterBean.getDestCity(), locationVO);
            return;
        }

        Integer distance = 0;
        try {
            DistanceRpcReq req = new DistanceRpcReq();
            req.setSrcMsgId(filterBean.getSrcMsgId());
            req.setFromLatitude(locationVO.getLocationLatitude());
            req.setFromLongitude(locationVO.getLocationLongitude());
            req.setToLatitude(filterBean.getStartLatitude());
            req.setToLongitude(filterBean.getStartLongitude());
            Response<DistanceRpcVO> execute = tencentLocationClient.calculateDistance(req).execute();
            if (execute.isSuccessful()) {
                DistanceRpcVO rpcVO = execute.body();
                if (Objects.nonNull(rpcVO) && rpcVO.isSuccess()) {
                    distance = rpcVO.getDistance();
                }
            }
        } catch (Exception e) {
            log.error("专车发货自动派单，根据车头位置查询距离异常，货源ID:{}, 位置信息:{}, 出发地城市:{}, 出发地区:{}",
                    filterBean.getSrcMsgId(), locationVO, filterBean.getStartCity(), filterBean.getStartArea(), e);
        }
        if (Objects.isNull(distance) || distance == 0) {
            log.info("专车发货自动派单，根据车头位置查询距离失败，货源ID:{}, 位置信息:{}, 出发地城市:{}, 出发地区:{}",
                    filterBean.getSrcMsgId(), locationVO, filterBean.getStartCity(), filterBean.getStartArea());
            return;
        }
        log.info("专车发货自动派单，根据车头位置获取距离，货源ID：{}, 车头信息:{}, 出发地城市:{}, 出发地区:{}, 距离:{}",
                filterBean.getSrcMsgId(), vo.getHeadCityNo(), filterBean.getStartCity(), filterBean.getStartArea(), distance);
        //换算公里
        BigDecimal d = new BigDecimal(distance).divide(new BigDecimal("1000"), 1, RoundingMode.HALF_UP);
        vo.setDistance(d);
    }

    /**
     * 根据位置信息获取车辆距离货物的距离
     *
     * @param filterBean
     * @param map
     * @param vo
     */
    private void getDistanceByLocation(AssignableCarFilterBean filterBean, Map<String, CurrentLocationVO> map, SigningCarInfoVo vo) {
        CurrentLocationVO locationVO = map.get(vo.getHeadCityNo());
        if (Objects.isNull(locationVO) || StringUtils.isEmpty(locationVO.getNewLocation())) {
            log.info("专车发货自动派单，未获取到车头位置，货源ID:{}, 车头信息:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                    vo.getHeadCityNo(), filterBean.getStartCity(), filterBean.getDestCity());
            return;
        }
        String location = locationVO.getNewLocation();
        vo.setLocation(location);

        String address = AddressCuttingUtil.addressCutting(location);
        String city = "";
        String district = "";
        if (StringUtils.isEmpty(address)) {
            log.info("专车发货自动派单，车头位置截取失败，货源ID:{}, 车头信息:{}, 车头位置:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                    vo.getHeadCityNo(), location, filterBean.getStartCity(), filterBean.getDestCity());
            return;
        }
        String[] parts = address.split(",");
        city = parts[0];
        if (parts.length > 1) {
            district = parts[1];
        }
        if(StringUtils.isEmpty(city) || StringUtils.isEmpty(district)){
            log.info("专车发货自动派单，车头位置截取失败，货源ID:{}, 车头信息:{}, 车头位置:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                    vo.getHeadCityNo(), location, filterBean.getStartCity(), filterBean.getDestCity());
            return;
        }

        int distance = 0;
        String districtNew = districtInfo(district);
        if (city.equals(filterBean.getStartCity()) && (district.equals(filterBean.getStartArea()) || districtNew.equals(filterBean.getStartArea()))) {
            // 车头位置和出发地相同距离赋值1公里
            distance = 100;
        } else {
            //查询距离
            distance = tytMapDictService.getDistanceCity(city, district, filterBean.getStartCity(), filterBean.getStartArea());
            if (0 == distance) {
                distance = tytMapDictService.getDistanceCity(city, districtNew, filterBean.getStartCity(), filterBean.getStartArea());
            }
        }
        if (0 == distance) {
            log.info("专车发货自动派单，根据车头位置查询距离失败，货源ID:{}, 车头信息:{}, 车头城市:{}, 车头区:{}, 出发地城市:{}, 出发地区:{}",
                    filterBean.getSrcMsgId(), vo.getHeadCityNo(), city, district, filterBean.getStartCity(), filterBean.getStartArea());
            return;
        }
        log.info("专车发货自动派单，根据车头位置获取距离，货源ID：{}, 车头信息:{}, 车头城市:{}, 车头区:{}, 出发地城市:{}, 出发地区:{}, 距离:{}",
                filterBean.getSrcMsgId(), vo.getHeadCityNo(), city, district, filterBean.getStartCity(), filterBean.getStartArea(), distance);
        //换算公里
        BigDecimal d = new BigDecimal(distance).divide(new BigDecimal("100"), 1, RoundingMode.HALF_UP);
        vo.setDistance(d);
    }

    /**
     * 调用bi获取车头位置信息
     *
     * @param filterBean
     * @param assignableCars
     * @param map
     */
    private void getHeadLocation(AssignableCarFilterBean filterBean, List<SigningCarInfoVo> assignableCars, Map<String, CurrentLocationVO> map) {
        // 车头去重
        List<String> headList = assignableCars.stream()
                .map(SigningCarInfoVo::getHeadCityNo)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(headList)) {
            List<List<String>> splitHeadList = ListUtil.splitIntoChunks(headList, 10);
            for (List<String> list : splitHeadList) {
                //去重后的车头信息调用bi获取每个车头位置
                String heads = list.stream()
                        .map(String::trim)
                        .collect(Collectors.joining(","));
                try {
                    StopWatch watch = new StopWatch();
                    watch.start();
                    List<CurrentLocationVO> location;
                    Integer specialCarLastLocationSwitch = tytConfigService.getIntValue(SPECIAL_CAR_LAST_LOCATION_SWITCH, 0);
                    log.info("getCarLocus specialCarLastLocationSwitch:{}", specialCarLastLocationSwitch);
                    if(specialCarLastLocationSwitch ==0){
                        location = carCurrentLocationService.getCurrentLocations(heads);
                    }else {
                        location = carCurrentLocationService.getZJCurrentLocations(heads);
                    }
                    watch.stop();
                    log.info("专车发货自动派单，调用bi获取车头位置，货源ID：{}, 请求车头数量:{}，耗时:{} 毫秒, 结果：{}",
                            filterBean.getSrcMsgId(), list.size(), watch.getTotalTimeMillis(), JSON.toJSONString(location));
                    if(CollectionUtils.isNotEmpty(location)){
                        for(CurrentLocationVO vo : location){
                            map.put(vo.getHeadCity() + vo.getHeadNo(), vo);
                        }
                    }
                } catch (Exception e) {
                    log.error("专车发货自动派单，调用bi获取车头位置失败，货源ID:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                            filterBean.getStartCity(), filterBean.getDestCity(), e);
                }
            }
        }
    }

    /**
     * 通过路线筛选获取满足派单条件的车辆
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoVo> getAssignableCarsByRoute(AssignableCarFilterBean filterBean) {
        List<SigningCarInfoVo> assignableCars = tytSigningCarMapper.getAssignableCarsByRoute(filterBean.getStartCity(), filterBean.getDestCity());
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            assignableCars = filterAssignableCars(filterBean, assignableCars);
        } else {
            assignableCars = new ArrayList<>();
        }
        return assignableCars;
    }

    /**
     * 通过城市筛选获取满足派单条件的车辆
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoVo> getAssignableCarsByCity(AssignableCarFilterBean filterBean) {
        String startCity = filterBean.getStartCity();
        String destCity = filterBean.getDestCity();
        List<String> cities = new ArrayList<>();
        cities.add(startCity);
        cities.add(destCity);

        // tyt_signing_car_info_city表中city存值存在：北京市  北京。需要处理一下把这两种都查出来
        if (startCity.endsWith("市")) {
            cities.add(startCity.replace("市", ""));
        }
        if (destCity.endsWith("市")) {
            cities.add(destCity.replace("市", ""));
        }

        List<SigningCarInfoVo> assignableCars = tytSigningCarMapper.getAssignableCarsByCity(cities);
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            assignableCars = filterAssignableCars(filterBean, assignableCars);
        } else {
            assignableCars = new ArrayList<>();
        }
        return assignableCars;
    }

    /**
     * 筛选城市配置为全国的满足派单条件的车辆
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoVo> getAssignableCarsByCountry(AssignableCarFilterBean filterBean) {
        List<SigningCarInfoVo> assignableCars = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 100;
        while (true) {
            int start = (pageNo - 1) * pageSize;
            List<SigningCarInfoVo> subAssignableCars = tytSigningCarMapper.getAssignableCarsByCountry(start, pageSize);
            int size = subAssignableCars.size();
            if (size == 0) {
                break;
            }
            subAssignableCars = filterAssignableCars(filterBean, subAssignableCars);
            assignableCars.addAll(subAssignableCars);
            if (size < pageSize) {
                break;
            }
            pageNo = pageNo + 1;
        }
        return assignableCars;
    }

    /**
     * 根据条件过滤可派单司机
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoVo> filterAssignableCars(AssignableCarFilterBean filterBean,
                                                        List<SigningCarInfoVo> assignableCars) {
        // 需要驾驶此类货物，增加驾驶能力筛选
        assignableCars = filterByDrivingAbility(filterBean, assignableCars);

        // 指定了签约合作商，增加签约合作商筛选
        assignableCars = filterByCargoOwner(filterBean, assignableCars);

        // 运输距离偏好过滤，长途：大于200公里； 短途：小于等于200公里； 长途/短途：不限制距离
        assignableCars = filterByDistancePreference(filterBean, assignableCars);

        // 过滤存在承运中或待支付的专车订单的车主
        // 6600不判断司机是否有待支付订单
        // assignableCars = filterByOrder(filterBean, assignableCars);

        return assignableCars;
    }

    /**
     * 根据驾驶能力筛选
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoVo> filterByDrivingAbility(AssignableCarFilterBean filterBean, List<SigningCarInfoVo> assignableCars) {
        if (DriverDrivingEnum.DRIVING.getCode().equals(filterBean.getDriverDriving()) &&
                StringUtils.isNotEmpty(filterBean.getGoodsTypeName())) {
            assignableCars = assignableCars.stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getDrivingAbility()) &&
                            e.getDrivingAbility().contains(filterBean.getGoodsTypeName()))
                    .collect(Collectors.toList());
        }
        return assignableCars;
    }

    /**
     * 签约合作商条件过滤
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoVo> filterByCargoOwner(AssignableCarFilterBean filterBean, List<SigningCarInfoVo> assignableCars) {
        if (Objects.nonNull(filterBean.getCargoOwnerId())) {
            DispatchCargoOwner cargoOwner = dispatchCargoOwnerMapper.selectByCooperativeId(filterBean.getCargoOwnerId());

            // 该货主指定车方承运，筛选该货主签约司机
            if (Objects.nonNull(cargoOwner) && AssignCarEnum.YES.getCode().equals(cargoOwner.getAssignCar())) {
                TytDispatchCooperative cooperative = tytDispatchCooperativeMapper.selectByPrimaryKey(cargoOwner.getCooperativeId());
                if (Objects.isNull(cooperative)) {
                    return assignableCars;
                }
                assignableCars = assignableCars.stream()
                        .filter(e -> {
                            if (StringUtils.isNotEmpty(e.getSigning())) {
                                List<String> list = Arrays.stream(e.getSigning().split(",")).collect(Collectors.toList());
                                return list.contains(cooperative.getCooperativeName());
                            }
                            return false;
                        })
                        // 筛选完成司机认证的司机
                        .filter(e -> Objects.equals(e.getVerifyStatus(), DriverVerifyEnum.VERIFY_SUCCESS.getCode()))
                        .collect(Collectors.toList());
            }
        }
        return assignableCars;
    }

    /**
     * 运输距离偏好条件过滤
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoVo> filterByDistancePreference(AssignableCarFilterBean filterBean, List<SigningCarInfoVo> assignableCars) {
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            BigDecimal distanceKilometer = new BigDecimal("0");
            GoodsAddressLevelRecord levelRecord = goodsAddressLevelRecordMapper.selectBySrcMsgId(filterBean.getSrcMsgId());
            if (Objects.nonNull(levelRecord) && Objects.nonNull(levelRecord.getDistanceKilometer())) {
                distanceKilometer = levelRecord.getDistanceKilometer();
            } else {
                Integer distance = tytTransportMainMapper.selectDistanceBySrcMsgId(filterBean.getSrcMsgId());
                if (Objects.nonNull(distance)) {
                    distanceKilometer = new BigDecimal(distance / 100);
                }
            }
            String preference;
            if (distanceKilometer.compareTo(new BigDecimal("200")) > 0) {
                preference = DistancePreferenceEnum.LONG_DISTANCE.getValue();
            } else {
                preference = DistancePreferenceEnum.SHORT_DISTANCE.getValue();
            }
            String finalPreference = preference;
            assignableCars = assignableCars.stream()
                    .filter(e -> (Objects.equals(finalPreference, e.getDistancePreference()) ||
                            Objects.equals(DistancePreferenceEnum.NO_LIMIT.getValue(), e.getDistancePreference())))
                    .collect(Collectors.toList());
        }
        return assignableCars;
    }

    /**
     * 若司机存在承运中货待支付专车订单，则不给该司机指派(非专车货源需要过滤)
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoVo> filterByOrder(AssignableCarFilterBean filterBean, List<SigningCarInfoVo> assignableCars) {
        if (Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), filterBean.getExcellentGoods())) {
            return assignableCars;
        }
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            List<Long> carUserIdList = assignableCars.stream().map(SigningCarInfoVo::getUserId).collect(Collectors.toList());
            try {
                Response<Map<Long, Boolean>> response = apiTradeInfoFeeClient.getOrderListByPayUserIds(carUserIdList).execute();
                if (response.isSuccessful()) {
                    Map<Long, Boolean> carOrderMap = response.body();
                    log.info("专车发货自动派单，货源ID:{}, 查询司机订单返回:{}", filterBean.getSrcMsgId(), JSONObject.toJSONString(carOrderMap));
                    if (MapUtils.isNotEmpty(carOrderMap)) {
                        assignableCars = assignableCars.stream()
                                .filter(v -> (Objects.isNull(carOrderMap.get(v.getUserId())) || !carOrderMap.get(v.getUserId())))
                                .collect(Collectors.toList());
                    }
                }
            } catch (Exception e) {
                log.error("专车发货自动派单，查询司机是否存在承运中或待支付专车订单失败，货源id：{}", filterBean.getSrcMsgId(), e);
            }
        }
        return assignableCars;
    }

    @Override
    public void updateDriverAssignNum(Long carInfoId) {
        tytSigningCarMapper.updateDriverAssignNum(carInfoId);
    }

    @Override
    public List<TytSigningCarVO> getCarUser(String ids,String transportCity,String transportArea){
        try{
            //根据私域车辆司机表id查询相关数据
            List<String> listIds = Arrays.asList(ids.split(String.valueOf(",")));
            List<TytSigningCarVO> list = tytSigningCarMapper.getUserList(listIds);
            if(CollectionUtils.isEmpty(list)){
                return null;
            }
            //过滤在黑名单中的数据
            list = list.stream()
                    .filter(obj -> !Objects.equals(2, obj.getStatus()))
                    .collect(Collectors.toList());
            //根据司机车头信息去重生成集合
            List<String> headList = list.stream()
                    .map(TytSigningCarVO::getHeadCityNo)
                    .distinct()
                    .collect(Collectors.toList());
            Map<String,String> map = new HashMap<>();
            if(CollectionUtils.isNotEmpty(headList)){
                //去重后的车头信息调用bi获取每个车头位置
                String heads = headList.stream()
                        .map(String::trim)
                        .collect(Collectors.joining(","));
                List<CurrentLocationVO> location = carCurrentLocationService.getCurrentLocations(heads);
                if(CollectionUtils.isNotEmpty(location)){
                    for(CurrentLocationVO vo : location){
                        map.put(vo.getHeadCity()+vo.getHeadNo(),vo.getNewLocation());
                    }
                }
            }

            //遍历司机信息，获取司机具体位置
            for(TytSigningCarVO vo : list){
                if(null != vo.getStatus() && 2 == vo.getStatus()){
                    continue;
                }
                String local = map.get(vo.getHeadCityNo());
                //未查出距离，直接为0
                if(StringUtils.isEmpty(local)){
                    vo.setRange(new BigDecimal(0));
                    continue;
                }
                log.info("查询司机车头具体位置司机id【{}】，地址【{}】",vo.getId(),local);
                String address = AddressCuttingUtil.addressCutting(local);
                String city = "";
                String district = "";
                if(StringUtils.isNotEmpty(address)){
                    String[] parts = address.split(",");
                    city = parts[0];
                    if (parts.length > 1) {
                         district = parts[1];
                    }

                }else{
                    vo.setRange(new BigDecimal(0));
                    continue;
                }
                if(StringUtils.isEmpty(city) || StringUtils.isEmpty(district)){
                    vo.setRange(new BigDecimal(0));
                    continue;
                }
                //查询距离
                int dict = tytMapDictService.getDistanceCity(city,district,transportCity,transportArea);
                log.info("查询距离，出发市【{}】，出发区【{}】，目的地【{}】，目的地【{}】,距离【{}】",city,district,transportCity,transportArea,dict);
                if (0 == dict){
                    district = districtInfo(district);
                    dict = tytMapDictService.getDistanceCity(city,district,transportCity,transportArea);
                }
                log.info("转换后的查询距离，出发市【{}】，出发区【{}】，目的地【{}】，目的地【{}】,距离【{}】",city,district,transportCity,transportArea,dict);
                if(0 == dict){
                    vo.setRange(new BigDecimal(0));
                }else{
                    //换算公里
                    BigDecimal i = new BigDecimal(dict).divide(new BigDecimal(100));
                    //取倒数保留三位小数
                    BigDecimal range = new BigDecimal(1).divide(i,3, RoundingMode.DOWN);
                    vo.setRange(range);
                }
            }
            for(TytSigningCarVO vo : list){
                //好评率*2
                BigDecimal comment = vo.getFavorableComment().multiply(new BigDecimal(2));
                //接单率*5
                BigDecimal orders = vo.getReceivingOrders().multiply(new BigDecimal(3));
                BigDecimal range = vo.getRange().multiply(new BigDecimal(5));
                vo.setRange(comment.add(orders).add(range));
            }
            Collections.sort(list, Comparator.comparing(TytSigningCarVO::getRange).reversed());
            return list;
        }catch (Exception e){
            log.info("轮询派单司机排序失败",e);
        }
        return null;
    }

    @Override
    public List<TytSigningCarVO> getSigningCarList(List<TytSigningCarVO> signingCarList){
        try{
            if(CollectionUtils.isEmpty(signingCarList)){
                return null;
            }
            //过滤在黑名单中的数据
            signingCarList = signingCarList.stream()
                    .filter(car -> tytSigningCarMapper.checkDriverBlackStatus(car.getDriverUserId()) == 0)
                    .collect(Collectors.toList());

            for(TytSigningCarVO vo : signingCarList){

                BigDecimal rangFactor = new BigDecimal(1).divide(vo.getDistance(),3, RoundingMode.DOWN);
                //好评率*2
                BigDecimal comment = vo.getFavorableComment().multiply(new BigDecimal(2));
                //接单率*5
                BigDecimal orders = vo.getReceivingOrders().multiply(new BigDecimal(3));
                BigDecimal range = rangFactor.multiply(new BigDecimal(5));
                vo.setRange(comment.add(orders).add(range));
            }
            signingCarList.sort(Comparator.comparing(TytSigningCarVO::getRange).reversed());
            return signingCarList;
        }catch (Exception e){
            log.error("专车发货自动派单，轮询派单司机排序失败",e);
        }
        return null;
    }


    public  String districtInfo(String district) {
        if (district.endsWith("区")) {
            return remove(district, "县");
        }
        if( district.endsWith("县")){
            return remove(district, "区");
        }
        return district;
    }

    public String remove(String district, String name){
        String substring = district.substring(0, district.length() - 1);
        district =  substring + name;

        return district;
    }

    @Override
    public  Boolean getSigningCarBlack(Long userId){
       List<Long> ids =  tytSigningCarMapper.getSigningCarBlack(userId);
       if(CollectionUtils.isEmpty(ids)){
           return true;
       }

       Integer count = tytSigningCarMapper.getByUserNoIds(ids);
       if(ids.size() <= count){
           return false;
       }
       return true;
    }


    @Override
    public List<CurrentLocationVO> getBICarLocal(String carNos){
        try{
            long now = System.currentTimeMillis();
            List<CurrentLocationVO> location = carCurrentLocationService.getCurrentLocations(carNos);
            log.info("调用车辆位置耗时：" + (System.currentTimeMillis() - now));
            return location;
        }catch (Exception e){
            log.error("调用车辆位置失败",e);
        }
        return null;
    }
}
