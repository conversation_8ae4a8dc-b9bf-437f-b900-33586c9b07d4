package com.tyt.plat.service.mq;

import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.messagecenter.core.vo.base.MessageCenterBaseVo;
import com.tyt.messagecenter.core.vo.mq.MessageCenterData;

/**
 * 消息中心生产者
 */
public interface IMessageCenterProducer {

	/**
	 * 发送消息中心数据
	 * @param centerData
	 * @param delayTime 可以为空
	 * @return
	 */
	SendResult sendMessageCenter(MessageCenterData centerData, Long delayTime);

	SendResult sendBusinessMessage(MessageCenterBaseVo centerData, Long delayTime);

}
