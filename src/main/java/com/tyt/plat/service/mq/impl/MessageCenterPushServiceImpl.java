package com.tyt.plat.service.mq.impl;

import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.messagecenter.core.vo.mq.*;
import com.tyt.plat.service.mq.IMessageCenterProducer;
import com.tyt.plat.service.mq.MessageCenterPushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 消息中心生产者
 */
@Slf4j
@Service
public class MessageCenterPushServiceImpl implements MessageCenterPushService {

    @Autowired
    private IMessageCenterProducer messageCenterProducer;

    @Override
    public NewsMessagePush createNewsMessage(String remarks, String summary, String title, String content,
                                             Long userId, int carPush, int goodsPush) {
        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.addUserId(userId);

        messagePushBase.setTitle(title);
        messagePushBase.setContent(content);
        messagePushBase.setRemarks(remarks);
        messagePushBase.setPushType(carPush, goodsPush);

        //消息
        NewsMessagePush newsMessage = NewsMessagePush.createByPushBase(messagePushBase);
        newsMessage.setSummary(summary);

        return newsMessage;
    }

    @Override
    public NotifyMessagePush createNotifyMessage(String remarks, String title, String content, Long userId,
                                                 String openType, String linkUrl, int carPush, int goodsPush) {

        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.addUserId(userId);

        messagePushBase.setTitle(title);
        messagePushBase.setContent(content);
        messagePushBase.setRemarks(remarks);
        messagePushBase.setPushType(carPush, goodsPush);

        //通知
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);

        // 设置打开方式为1打开链接 0打开应用
        if(StringUtils.isNotBlank(openType)){
            notifyMessage.setOpenType(Integer.parseInt(openType));
            notifyMessage.setLinkUrl(linkUrl);
        }
        return notifyMessage;
    }

    @Override
    public ShortMessageBean createShortMessage(String cellPhone, String content, String remark) {
        ShortMessageBean shortMessage = new ShortMessageBean();
        shortMessage.setCellPhone(cellPhone);
        shortMessage.setContent(content);
        shortMessage.setRemark(remark);
        return shortMessage;
    }

    @Override
    public ShortMessageBean createVerifyShortMessage(String cellPhone, String content, String templateCode,
                                                     String verifyCode, String remark) {

        ShortMessageBean shortMessage = new ShortMessageBean();
        shortMessage.setCellPhone(cellPhone);
        shortMessage.setContent(content);
        shortMessage.setTemplateCode(templateCode);
        shortMessage.setVerifyCode(verifyCode);
        shortMessage.setRemark(remark);

        return shortMessage;
    }

    @Override
    public SendResult sendShortMsg(String cellPhone, String content, String remark, Long delayTime){
        ShortMessageBean shortMessage = new ShortMessageBean();
        shortMessage.setCellPhone(cellPhone);
        shortMessage.setContent(content);
        shortMessage.setRemark(remark);

        MessageCenterData messageCenterData = new MessageCenterData("_plat", shortMessage, null, null);
        SendResult sendResult = messageCenterProducer.sendMessageCenter(messageCenterData, delayTime);
        return sendResult;
    }

    @Override
    public SendResult sendShortMsg(String cellPhone, String content, String remark) {
        return this.sendShortMsg(cellPhone, content, remark, null);
    }

    @Override
    public SendResult sendMultiMessage(ShortMessageBean shortMessage, NewsMessagePush newsMessage, NotifyMessagePush notifyMessage, Long delayTime) {
        MessageCenterData messageCenterData = new MessageCenterData("_plat", shortMessage, newsMessage, notifyMessage);
        SendResult sendResult = messageCenterProducer.sendMessageCenter(messageCenterData, delayTime);
        return sendResult;
    }

    @Override
    public SendResult sendMultiMessage(ShortMessageBean shortMessage, NewsMessagePush newsMessage, NotifyMessagePush notifyMessage) {
        return this.sendMultiMessage(shortMessage, newsMessage, notifyMessage, null);
    }

    @Override
    public NewsMessagePush changeToNewDesignMessage(NewsMessagePush newsMessagePush, Integer enterDetail, Integer jumpPage, String jumpButtonText, String jumpPageUrl, String jumpPageUrlGoods) {
        // 改版字段
        newsMessagePush.setNewDesign(1);
        newsMessagePush.setEnterDetail(enterDetail);
        newsMessagePush.setJumpPage(jumpPage);
        newsMessagePush.setJumpButtonText(jumpButtonText);
        newsMessagePush.setJumpPageUrl(jumpPageUrl);
        newsMessagePush.setJumpPageUrlGoods(jumpPageUrlGoods);
        return newsMessagePush;
    }

}
