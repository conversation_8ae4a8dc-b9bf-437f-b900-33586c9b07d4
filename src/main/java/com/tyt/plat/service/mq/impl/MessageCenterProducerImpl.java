package com.tyt.plat.service.mq.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.*;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.vo.base.MessageCenterBaseVo;
import com.tyt.messagecenter.core.vo.mq.MessageCenterData;
import com.tyt.plat.entity.base.TytMqMessageCenter;
import com.tyt.plat.service.mq.IMessageCenterProducer;
import com.tyt.plat.service.mq.MqMessageCenterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Properties;
import java.util.ResourceBundle;

/**
 * 消息中心生产者
 */
@Slf4j
@Service
public class MessageCenterProducerImpl implements IMessageCenterProducer {

    @Autowired
    private MqMessageCenterService mqMessageCenterService;

    /** 消息中心专有topic **/
    public final static String MESSAGE_CENTER_TOPIC;
    /** 消息中心业务类topic **/
    public final static String CENTER_BUSINESS_TOPIC;

    /** tag 不变，先写死 **/
    public final static String MESSAGE_CENTER_TAG = "TAG_PUSH_MESSAGE";

    //直接发送的mq
    private static Producer simpleProducer;

    //业务相关的 producer
    private static Producer businessProducer;

    public static final ResourceBundle p=ResourceBundle.getBundle("server_url");

    static{
        MESSAGE_CENTER_TOPIC = getString("MESSAGE_CENTER_TOPIC");

        CENTER_BUSINESS_TOPIC = getString("OFTEN_ROUTE_TOPIC");

        initSimpleProducer();

        initBusinessProducer();

    }

    public static void initSimpleProducer(){

        Properties properties = new Properties();

        properties.setProperty(PropertyKeyConst.GROUP_ID, getString("MESSAGE_CENTER_GROUP"));
        properties.setProperty(PropertyKeyConst.AccessKey, getString("ACCESS_KEY"));
        properties.setProperty(PropertyKeyConst.SecretKey, getString("SECRET_KEY"));
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, getString("NAMESRV_ADDR"));
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, getString("SendMsgTimeoutMillis"));

        //普通producer
        simpleProducer = ONSFactory.createProducer(properties);
        simpleProducer.start();
    }

    public static void initBusinessProducer(){
        Properties properties = new Properties();

        properties.setProperty(PropertyKeyConst.GROUP_ID, getString("OFTEN_ROUTE_GROUP"));
        properties.setProperty(PropertyKeyConst.AccessKey, getString("ACCESS_KEY"));
        properties.setProperty(PropertyKeyConst.SecretKey, getString("SECRET_KEY"));
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, getString("NAMESRV_ADDR"));
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, getString("SendMsgTimeoutMillis"));

        //普通producer
        businessProducer = ONSFactory.createProducer(properties);
        businessProducer.start();
    }

    public static String getString(String key){
        Object obj = p.getObject(key);
        String value = obj!=null?obj.toString():"";
        return value;
    }

    private Message createMessage(String messages, String topic, String key, String tag, Long delayedTime){

        Message message1 = new Message(topic, tag, key, messages.getBytes(StandardCharsets.UTF_8));

        if (delayedTime!=null) {
            message1.setStartDeliverTime(System.currentTimeMillis() + delayedTime);
        }
        return message1;
    }

    /**
     * 发送普通消息，非事务
     * @param centerData
     * @param topic
     * @param tag
     * @param delayTime
     * @return
     */
    private SendResult sendMsgSimple(MessageCenterData centerData, String topic, String tag, Long delayTime){
        if(centerData == null || !centerData.isNotEmpty()){
            return null;
        }
        String messageSerialNum = centerData.getMessageSerialNum();

        if(CommonUtil.hasNull(topic, messageSerialNum, tag)){
            return null;
        }

        String mqDataJson = JSON.toJSONString(centerData);

        SendResult sendResult = null;
        if (simpleProducer != null) {
            try {
                TytMqMessageCenter tytMqMessageCenter = new TytMqMessageCenter();
                tytMqMessageCenter.setMessageSerialNum(messageSerialNum);
                tytMqMessageCenter.setMessageContent(mqDataJson);
                tytMqMessageCenter.setDealStatus((byte) 1);
                tytMqMessageCenter.setCreateTime(new Date());
                tytMqMessageCenter.setUpdateTime(new Date());
                tytMqMessageCenter.setFailCount(0);

                boolean insert = mqMessageCenterService.insertMessageCenter(tytMqMessageCenter);

                Message message = this.createMessage(mqDataJson, topic, messageSerialNum, tag, delayTime);
                sendResult = simpleProducer.send(message);
                log.info("Time=2=of==the==message==has==been==sent " + sendResult.getMessageId() + "=====product message, the content is: " + mqDataJson);
            } catch (Exception e) {
                log.error("Time=3=of==the==message==has==been==sent ", e);
            }
        } else {
            log.info("Time=4=of==the==message==has==been==sent " + (System.currentTimeMillis()) + "=====product message, the content is: " + mqDataJson);
        }
        return sendResult;
    }

    @Override
    public SendResult sendMessageCenter(MessageCenterData centerData, Long delayTime) {
        log.info("sendMessageCenter_start_sn : " + centerData.getMessageSerialNum());
        //推送消息专有topic
        SendResult sendResult = this.sendMsgSimple(centerData, MESSAGE_CENTER_TOPIC, MESSAGE_CENTER_TAG, delayTime);
        return sendResult;
    }

    private SendResult producerBusinessMessage(MessageCenterBaseVo centerData, String topic, Long delayTime){

        String messageSerialNum = centerData.getMessageSerialNum();
        String tag = centerData.getTag();

        if(CommonUtil.hasNull(topic, messageSerialNum, tag)){
            return null;
        }

        String mqDataJson = JSON.toJSONString(centerData);

        SendResult sendResult = null;
        try {
            Message message = this.createMessage(mqDataJson, topic, messageSerialNum, tag, delayTime);
            sendResult = businessProducer.send(message);
            log.info("producerBusinessMessage_done . msgId : {}, msgJson : {}", sendResult.getMessageId(), mqDataJson);
        } catch (Exception e) {
            log.error("producerBusinessMessage_error : ", e);
        }

        return sendResult;
    }

    @Override
    public SendResult sendBusinessMessage(MessageCenterBaseVo centerData, Long delayTime) {
        String messageSerialNum = centerData.getMessageSerialNum();
        String tag = centerData.getTag();

        if(centerData == null || StringUtils.isBlank(messageSerialNum) || StringUtils.isBlank(tag)){
            log.error("sendBusinessMessage_null_values_error");
            return null;
        }

        log.info("sendBusinessMessage_start. sn : {}, tag : {}", messageSerialNum, tag);

        //推送消息专有topic
        SendResult sendResult = this.producerBusinessMessage(centerData, CENTER_BUSINESS_TOPIC, delayTime);
        return sendResult;
    }

}
