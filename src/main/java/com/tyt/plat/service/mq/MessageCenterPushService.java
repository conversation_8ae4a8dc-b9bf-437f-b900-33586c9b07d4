package com.tyt.plat.service.mq;

import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.messagecenter.core.vo.mq.NewsMessagePush;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;

/**
 * 消息中心生产者
 */
public interface MessageCenterPushService {

	/**
	 * 生产消息实体类
	 * @param remarks
	 * @param summary
	 * @param title
	 * @param content
	 * @param userId
	 * @param carPush
	 * @param goodsPush
	 * @return
	 */
	NewsMessagePush createNewsMessage(String remarks, String summary, String title, String content,
									  Long userId, int carPush, int goodsPush);

	/**
	 * 生产通知实体类
	 * @param remarks
	 * @param title
	 * @param content
	 * @param userId
	 * @param openType
	 * @param linkUrl
	 * @param carPush
	 * @param goodsPush
	 * @return
	 */
	NotifyMessagePush createNotifyMessage(String remarks, String title, String content, Long userId,
										  String openType, String linkUrl, int carPush, int goodsPush);

	/**
	 * 创建短信实体类
	 * @param cellPhone
	 * @param content
	 * @param remark
	 * @return
	 */
	ShortMessageBean createShortMessage(String cellPhone, String content, String remark);

	/**
	 * 创建验证码类型短信实体类
	 * @param cellPhone
	 * @param content
	 * @param remark
	 * @return
	 */
	ShortMessageBean createVerifyShortMessage(String cellPhone, String content, String templateCode,
											  String verifyCode, String remark);

	/**
	 * 单独发送短信
	 * @param cellPhone
	 * @param content
	 * @param remark
	 * @param delayTime 可以为空
	 */
	SendResult sendShortMsg(String cellPhone, String content, String remark, Long delayTime);

	/**
	 * 单独发送短信
	 * @param cellPhone
	 * @param content
	 * @param remark
	 */
	SendResult sendShortMsg(String cellPhone, String content, String remark);

	/**
	 * 短信，消息，通知同时发送
	 * 可以部分为空
	 * @param shortMessage
	 * @param newsMessage
	 * @param notifyMessage
	 * @param delayTime
	 */
	SendResult sendMultiMessage(ShortMessageBean shortMessage, NewsMessagePush newsMessage, NotifyMessagePush notifyMessage, Long delayTime);

	/**
	 * 短信，消息，通知同时发送
	 * 可以部分为空
	 * @param shortMessage
	 * @param newsMessage
	 * @param notifyMessage
	 */
	SendResult sendMultiMessage(ShortMessageBean shortMessage, NewsMessagePush newsMessage, NotifyMessagePush notifyMessage);

	/**
	 * 转换为改版后的站内信
	 */
	NewsMessagePush changeToNewDesignMessage(NewsMessagePush newsMessagePush, Integer enterDetail, Integer jumpPage, String jumpButtonText, String jumpPageUrl, String jumpPageUrlGoods);


}
