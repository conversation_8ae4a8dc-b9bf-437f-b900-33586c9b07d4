package com.tyt.plat.service.mq.impl;

import com.tyt.plat.entity.base.TytMqMessageCenter;
import com.tyt.plat.mapper.base.TytMqMessageCenterMapper;
import com.tyt.plat.service.mq.MqMessageCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class MqMessageCenterServiceImpl implements MqMessageCenterService {

    @Autowired
    private TytMqMessageCenterMapper mqMessageCenterMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean insertMessageCenter(TytMqMessageCenter tytMqMessageCenter) {

        mqMessageCenterMapper.insertSelective(tytMqMessageCenter);
        return true;

    }

}
