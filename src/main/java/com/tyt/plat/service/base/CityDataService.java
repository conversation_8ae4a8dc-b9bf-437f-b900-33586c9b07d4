package com.tyt.plat.service.base;

import com.tyt.plat.entity.base.TytCity;
import com.tyt.plat.vo.map.CityInfoVo;

import java.math.BigDecimal;

/**
 * 地图城市坐标
 */
public interface CityDataService {

    /**
     * 根据adcode 获取
     *
     * @param adcode
     * @return
     */
    TytCity getCityByAdcode(String adcode);

    /**
     * 根据名称获取
     *
     * @param province
     * @param cityName
     * @param areaName
     * @return
     */
    TytCity getCityByName(String province, String cityName, String areaName);

    /**
     * 根据大地坐标系获取
     *
     * @param px
     * @param py
     * @return
     */
    TytCity getCityByPxPy(String px, String py);

    /**
     * 保存错误城市记录
     *
     * @param cityInfoVo
     */
    void saveErrorCity(CityInfoVo cityInfoVo, Long userId, String remark);

    /**
     * 根据请求匹配城市
     *
     * @param province
     * @param cityName
     * @param areaName
     * @param px
     * @param py
     * @param userId
     * @return
     */
    TytCity getMatchCity(String adcode, String province, String cityName, String areaName,
                         BigDecimal px, BigDecimal py, Long userId);

    /**
     * 根据标准城市名返回简短的城市名
     *
     * @param city
     * @return
     */
    TytCity getShortCityName(String city);
}
