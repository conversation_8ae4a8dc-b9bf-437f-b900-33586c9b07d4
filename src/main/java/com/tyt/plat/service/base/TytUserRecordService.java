package com.tyt.plat.service.base;

import com.tyt.plat.entity.base.TytUserRecord;
import com.tyt.plat.vo.other.IndividuationSettings;

import java.util.List;

public interface TytUserRecordService {
    /**
     * 根据用户id及业务code码获取信息
     *
     * @param userId
     * @param code
     * @return
     */
    TytUserRecord getByUserIdAndCode(Long userId, String code);

    void saveRecord(Long userId, String code, Integer status, String value);

    TytUserRecord saveRecord(TytUserRecord userRecord, Long userId, String code, Integer status, String value);

    /**
     * @description 根据用户id获取个性化设置
     * @param userId 用户id
     * @param codes code集合
     * @return java.util.List<com.tyt.plat.entity.base.TytUserRecord>
     * <AUTHOR>
     * @date 2023/8/31 15:44
     * @version 1.0
     */
    List<IndividuationSettings> getIndividuationSettings(Long userId, List<String> codes);


    void saveTimesRecord(Long userId, String code);
}
