package com.tyt.plat.service.base.impl;

import com.tyt.plat.entity.base.TytUserRecord;
import com.tyt.plat.enums.UserRecordEnum;
import com.tyt.plat.mapper.base.TytUserRecordMapper;
import com.tyt.plat.service.base.TytUserRecordService;
import com.tyt.plat.vo.other.IndividuationSettings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class TytUserRecordServiceImpl implements TytUserRecordService {

    @Autowired
    private TytUserRecordMapper tytUserRecordMapper;

    @Override
    public TytUserRecord getByUserIdAndCode(Long userId, String code) {
        return tytUserRecordMapper.selectByUserIdAndCode(userId, code);
    }

    @Override
    public void saveRecord(Long userId, String code, Integer status, String value) {

        TytUserRecord userRecord = tytUserRecordMapper.selectByUserIdAndCode(userId, code);
        this.saveRecord(userRecord, userId, code, status, value);
    }

    @Override
    public TytUserRecord saveRecord(TytUserRecord userRecord, Long userId, String code, Integer status, String value) {

        Date nowTime = new Date();

        if (Objects.isNull(userRecord)) {
            userRecord = TytUserRecord.builder()
                    .userId(userId)
                    .code(code)
                    .status(status)
                    .remark(UserRecordEnum.getRemarkByCode(code))
                    .value(value)
                    .createTime(nowTime)
                    .modifyTime(nowTime)
                    .build();
            tytUserRecordMapper.insertSelective(userRecord);

        } else {

            userRecord.setStatus(status);
            userRecord.setValue(value);
            userRecord.setModifyTime(nowTime);
            tytUserRecordMapper.updateByPrimaryKeySelective(userRecord);

        }

        return userRecord;
    }

    @Override
    public List<IndividuationSettings> getIndividuationSettings(Long userId, List<String> codes) {
        List<IndividuationSettings> individuationSettings = tytUserRecordMapper.selectIndividuationSettings(userId, codes);
        if (CollectionUtils.isEmpty(individuationSettings)) {
            //如果没有设置 返回默认值
            for (String code : codes) {
                IndividuationSettings setting = new IndividuationSettings();
                setting.setUserId(userId);
                setting.setStatus(1);
                setting.setCode(code);
                individuationSettings.add(setting);
            }
        }
        if (individuationSettings.size() < codes.size()) {
            //校验 如果其中某个未设置 则返回默认值
            for (String s : codes) {
                boolean b = individuationSettings.stream().anyMatch(se -> se.getCode().equals(s));
                if (BooleanUtils.isFalse(b)) {
                    IndividuationSettings setting = new IndividuationSettings();
                    setting.setUserId(userId);
                    setting.setCode(s);
                    setting.setStatus(1);
                    individuationSettings.add(setting);
                }
            }
        }
        return individuationSettings;
    }

    @Override
    public void saveTimesRecord(Long userId, String code) {
        TytUserRecord userRecord = tytUserRecordMapper.selectByUserIdAndCode(userId, code);
        int value = 1;
        if (!Objects.isNull(userRecord)){
            value = Integer.parseInt(userRecord.getValue()) + 1;
        }
        this.saveRecord(userRecord, userId, code, 1, String.valueOf(value));
    }

}
