package com.tyt.plat.service.base.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.tyt.plat.entity.base.TytAbtestConfig;
import com.tyt.plat.entity.base.TytAbtestConfigUser;
import com.tyt.plat.mapper.base.TytAbtestConfigMapper;
import com.tyt.plat.mapper.base.TytAbtestConfigUserMapper;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 9:53
 */
@Slf4j
@Service
public class AbtestServiceImpl implements AbtestService {

    @Autowired
    private TytAbtestConfigMapper tytAbtestConfigMapper;

    @Autowired
    private TytAbtestConfigUserMapper tytAbtestConfigUserMapper;

    public static final String YOUCHE_IS_SHOW_USERNAMEABTEST_CODE = "is_show_username";

    public static final String SHOW_BOSS_NICK_NAME = "show_boss_nick_name";

    @Override
    public Integer getUserType(String code, Long userId) {

        Integer typeResult = 0;

        Example exa = new Example(TytAbtestConfig.class);
        exa.and().andEqualTo("code", code)
                .andEqualTo("enable", 1);

        TytAbtestConfig tytAbtestConfig = tytAbtestConfigMapper.selectOneByExample(exa);

        if (tytAbtestConfig == null) {
            return 0;
        }

        typeResult = tytAbtestConfig.getDefaultType();
        if (userId == null || tytAbtestConfig.getRuleType() == 0) {
            return typeResult;
        }

        Example acExa = new Example(TytAbtestConfigUser.class);
        acExa.and().andEqualTo("abtestId", tytAbtestConfig.getId())
                .andEqualTo("userId", userId);

        TytAbtestConfigUser tytAbtestConfigUser = tytAbtestConfigUserMapper.selectOneByExample(acExa);

        if (tytAbtestConfigUser != null) {
            typeResult = tytAbtestConfigUser.getType();
        }
        return typeResult;
    }

    /**
     * 根据code获取ab测详细信息
     *
     * @param code
     * @return
     */
    @Override
    public TytAbtestConfig getAbTestConfig(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        Example exa = new Example(TytAbtestConfig.class);
        exa.and().andEqualTo("code", code)
                .andEqualTo("enable", 1);

        return tytAbtestConfigMapper.selectOneByExample(exa);
    }

    /**
     * 根据abtest记录ID和用户ID获取该用户配置类型
     *
     * @param abTestId
     * @param userId
     * @return null-该用户不在ab测配置内
     */
    @Override
    public Integer getUserTypeByAbTestIdAndUserId(Long abTestId, Long userId) {
        if (Objects.isNull(abTestId) || Objects.isNull(userId)) {
            return null;
        }
        Example acExa = new Example(TytAbtestConfigUser.class);
        acExa.and().andEqualTo("abtestId", abTestId)
                .andEqualTo("userId", userId);

        TytAbtestConfigUser tytAbtestConfigUser = tytAbtestConfigUserMapper.selectOneByExample(acExa);
        if (Objects.nonNull(tytAbtestConfigUser)) {
            return tytAbtestConfigUser.getType();
        }
        return null;
    }

    @Override
    public List<TytAbtestConfigVo> getUserTypeList(List<String> codeList, Long userId) {
        log.info("abtest入参codeList:{},userId:{}", JSONUtil.toJsonStr(codeList), userId);
        List<TytAbtestConfigVo> list = new ArrayList<>();
        Example exa = new Example(TytAbtestConfig.class);
        exa.and().andIn("code", codeList)
                .andEqualTo("enable", 1);

        List<TytAbtestConfig> tytAbtestConfigList = tytAbtestConfigMapper.selectByExample(exa);

        for (String code : codeList) {
            Integer typeResult = 0;
            TytAbtestConfigVo tytAbtestConfigVo = new TytAbtestConfigVo();
            if (CollUtil.isEmpty(tytAbtestConfigList)) {
                tytAbtestConfigVo.setCode(code);
                tytAbtestConfigVo.setType(typeResult);
                list.add(tytAbtestConfigVo);
                continue;
            }
            List<String> configCodeList = tytAbtestConfigList.stream().map(TytAbtestConfig::getCode).collect(Collectors.toList());
            if (!configCodeList.contains(code)) {
                tytAbtestConfigVo.setCode(code);
                tytAbtestConfigVo.setType(typeResult);
                list.add(tytAbtestConfigVo);
                continue;
            }

            for (TytAbtestConfig tytAbtestConfig : tytAbtestConfigList) {
                if (code.equals(tytAbtestConfig.getCode())) {
                    typeResult = tytAbtestConfig.getDefaultType();
                    if (userId != null && tytAbtestConfig.getRuleType() != 0) {
                        Example acExa = new Example(TytAbtestConfigUser.class);
                        acExa.and().andEqualTo("abtestId", tytAbtestConfig.getId())
                                .andEqualTo("userId", userId);

                        TytAbtestConfigUser tytAbtestConfigUser = tytAbtestConfigUserMapper.selectOneByExample(acExa);
                        if (tytAbtestConfigUser != null) {
                            typeResult = tytAbtestConfigUser.getType();
                        }
                    }
                    tytAbtestConfigVo.setCode(code);
                    tytAbtestConfigVo.setType(typeResult);
                    list.add(tytAbtestConfigVo);
                }
            }
        }
        log.info("abtest出参code:{},userId:{}", JSONUtil.toJsonStr(list), userId);
        return list;
    }

    @Override
    public boolean isAbTestEnable(String code) {
        Example exa = new Example(TytAbtestConfig.class);
        exa.and().andEqualTo("code", code)
                .andEqualTo("enable", 1);

        return tytAbtestConfigMapper.selectCountByExample(exa) > 0;
    }

    @Override
    public List<TytAbtestConfig> selectAllConfig() {
        return tytAbtestConfigMapper.selectAllConfig();
    }
}
