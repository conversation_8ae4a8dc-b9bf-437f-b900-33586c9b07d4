package com.tyt.plat.service.base;


import com.tyt.plat.entity.base.TytAbtestConfig;
import com.tyt.plat.vo.map.TytAbtestConfigVo;

import java.util.List;

/**
 * 调用运费价格相关
 */
public interface AbtestService {

    Integer getUserType(String code, Long userId);

    /**
     * 根据code获取ab测详细信息
     *
     * @param code
     * @return
     */
    TytAbtestConfig getAbTestConfig(String code);

    /**
     * 根据abtest记录ID和用户ID获取该用户配置类型
     *
     * @param abTestId
     * @param userId
     * @return null-该用户不在ab测配置内
     */
    Integer getUserTypeByAbTestIdAndUserId(Long abTestId, Long userId);

    List<TytAbtestConfigVo> getUserTypeList(List<String> code, Long userId);

    boolean isAbTestEnable(String code);

    /**
     * 查询所有ab配置项
     *
     * @return
     */
    List<TytAbtestConfig> selectAllConfig();

}
