package com.tyt.plat.service.base.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.plat.entity.base.TytAccidentRecord;
import com.tyt.plat.entity.base.TytCity;
import com.tyt.plat.mapper.base.TytAccidentRecordMapper;
import com.tyt.plat.mapper.base.TytCityMapper;
import com.tyt.plat.service.base.CityDataService;
import com.tyt.plat.vo.map.CityInfoVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 地图城市坐标
 * @date 2022/3/21 9:53
 */
@Slf4j
@Service
public class CityDataServiceImpl implements CityDataService {

    @Autowired
    private TytCityMapper tytCityMapper;

    @Autowired
    private TytAccidentRecordMapper tytAccidentRecordMapper;

    @Override
    public TytCity getCityByAdcode(String adcode) {
        //由于侯总暂时没有提供 adcode 字段，暂时先不支持

        return null;
    }

    @Override
    public TytCity getCityByName(String province, String cityName, String areaName) {
        if (StringUtils.isBlank(province) || StringUtils.isBlank(cityName) || StringUtils.isBlank(areaName)) {
            throw TytException.createException(ResponseEnum.request_error.info("地区名称错误"));
        }
        //省字段不需要作为条件（市，区/县 可以唯一确认地区，并且省字段地图与数据库不匹配）
        TytCity city = tytCityMapper.getCityByName(cityName, areaName);
        return city;
    }

    @Override
    public TytCity getCityByPxPy(String px, String py) {
        if (StringUtils.isBlank(px) || StringUtils.isBlank(py)) {
            throw TytException.createException(ResponseEnum.request_error.info("地区坐标错误"));
        }

        TytCity city = tytCityMapper.getCityByPxPy(px, py);
        return city;
    }

    @Override
    public void saveErrorCity(CityInfoVo cityInfoVo, Long userId, String remark) {

        String cityJson = JSON.toJSONString(cityInfoVo);

        TytAccidentRecord accidentRecord = new TytAccidentRecord();

        //城市未匹配
        accidentRecord.setPrimitiveType("1");
        //新模式匹配不到
        accidentRecord.setSubtype("4");
        accidentRecord.setRemake(remark);
        accidentRecord.setCreateTime(new Date());
        accidentRecord.setUserId(userId);
        accidentRecord.setDataSampleJson(cityJson);

        tytAccidentRecordMapper.insert(accidentRecord);
    }

    @Override
    public TytCity getMatchCity(String adcode, String province, String cityName, String areaName, BigDecimal decPx, BigDecimal decPy, Long userId) {

        TytCity matchCity = null;

        if (StringUtils.isNotBlank(adcode)) {
            matchCity = this.getCityByAdcode(adcode);
        }
        if (matchCity == null) {
            if (StringUtils.isNotBlank(province) && StringUtils.isNotBlank(cityName) && StringUtils.isNotBlank(areaName)) {
                matchCity = this.getCityByName(province, cityName, areaName);
            }
        }

        String pxText = null;
        String pyText = null;

        if (decPx != null) {
            pxText = decPx.stripTrailingZeros().toPlainString();
        }
        if (decPy != null) {
            pyText = decPy.stripTrailingZeros().toPlainString();
        }

        if (matchCity == null) {
            if (decPx != null && decPy != null) {
                matchCity = this.getCityByPxPy(pxText, pyText);
            }
        }

        CityInfoVo cityInfoVo = new CityInfoVo();

        cityInfoVo.setAdcode(adcode);
        cityInfoVo.setProvinceName(province);
        cityInfoVo.setCityName(cityName);
        cityInfoVo.setAreaName(areaName);
        cityInfoVo.setPx(pxText);
        cityInfoVo.setPy(pyText);
        cityInfoVo.setLongitude(null);
        cityInfoVo.setLatitude(null);

        if (matchCity == null) {
            if (StringUtils.isNotBlank(province) && StringUtils.isNotBlank(cityName) && StringUtils.isNotBlank(areaName)) {
                areaName = cityName;
                matchCity = this.getCityByName(province, cityName, areaName);
            }
            String remark = "新模式未匹配";
            if (matchCity != null) {
                remark = "新模式提级匹配";
            }
            this.saveErrorCity(cityInfoVo, userId, remark);
        } else {
            log.info("match_city_result : reqCityInfo : {} , matchCityId : {}", JSON.toJSONString(cityInfoVo), matchCity.getId());
        }
        return matchCity;
    }

    @Override
    public TytCity getShortCityName(String city) {
        return tytCityMapper.getShortCityName(city);
    }

}
