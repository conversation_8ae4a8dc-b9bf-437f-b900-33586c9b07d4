package com.tyt.plat.service.ocr;

import com.tyt.plat.vo.ocr.BusinessLicenseOcrRpcVO;
import com.tyt.plat.vo.ocr.RoadTransportBackOcrRpcVo;
import com.tyt.plat.vo.ocr.RoadTransportQuaCertOcrVo;
import com.tyt.plat.vo.ocr.VehicleLicenseDeputyPageBackOcrRpcVo;

/**
 * OCR服务类.
 */
public interface OcrService {

    /**
     * 行驶证副页背面OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    VehicleLicenseDeputyPageBackOcrRpcVo vehicleLicenseDeputyPageBackOcr(String url);

    /**
     * 道路运输从业资格证OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    RoadTransportQuaCertOcrVo roadTransportQualificationCertificateBackOcr(String url);

    /**
     * 道运证OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    RoadTransportBackOcrRpcVo roadTransportBackOcr(String url);

    /**
     * 营业执照OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    BusinessLicenseOcrRpcVO businessLicenseOcr(String url);
}
