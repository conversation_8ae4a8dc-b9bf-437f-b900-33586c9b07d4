package com.tyt.plat.service.ocr.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.plat.client.ocr.GroupOcrClient;
import com.tyt.plat.service.ocr.OcrService;
import com.tyt.plat.vo.ocr.*;
import com.tyt.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * OCR服务实现类.
 *
 * <AUTHOR>
 * @since 2024-6-4 15:04:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OcrServiceImpl implements OcrService {

    private final GroupOcrClient groupOcrClient;

    @Override
    public VehicleLicenseDeputyPageBackOcrRpcVo vehicleLicenseDeputyPageBackOcr(String url) {
        try {
            Response<VehicleLicenseDeputyPageBackOcrRpcVo> internalResponse = groupOcrClient.vehicleLicenseDeputyPageBackOcr(url).execute();
            return getData(internalResponse);
        } catch (Exception e) {
            log.error("vehicle license deputy page back ocr error:", e);
            return null;
        }
    }

    @Override
    public RoadTransportQuaCertOcrVo roadTransportQualificationCertificateBackOcr(String url) {
        try {
            Response<RoadTransportQuaCertOcrRpcVo> internalResponse = groupOcrClient.roadTransportQualificationCertificateBackOcr(url).execute();
            RoadTransportQuaCertOcrRpcVo data = getData(internalResponse);
            if (ObjectUtil.isNotNull(data)) {
                RoadTransportQuaCertOcrVo vo = RoadTransportQuaCertOcrVo.builder().
                        certificateNumber(data.getCertificateNumber()).
                        name(data.getName()).
                        drivingClass(data.getDrivingClass()).build();
                List<QualificationCategoryRpcVo> qualificationCategoryList = data.getQualificationCategoryList();
                if (BeanUtil.isNotEmpty(qualificationCategoryList)) {
                    QualificationCategoryRpcVo qualificationCategoryRpcVo = qualificationCategoryList.get(0);
                    vo.setCategory(qualificationCategoryRpcVo.getCategory());
                    formatTime(vo, qualificationCategoryRpcVo);
                }
                return vo;
            }
            return null;
        } catch (Exception e) {
            log.error("road transport qualification certificate back ocr error:", e);
            return null;
        }
    }

    @Override
    public RoadTransportBackOcrRpcVo roadTransportBackOcr(String url) {
        try {
            Response<RoadTransportBackOcrRpcVo> internalResponse = groupOcrClient.roadTransportBackOcr(url).execute();
            return getData(internalResponse);
        } catch (Exception e) {
            log.error("road transport back ocr error:", e);
            return null;
        }
    }

    @Override
    public BusinessLicenseOcrRpcVO businessLicenseOcr(String url) {
        try {
            Response<BusinessLicenseOcrRpcVO> internalResponse = groupOcrClient.businessLicenseOcr(url).execute();
            return getData(internalResponse);
        } catch (Exception e) {
            log.error("business license ocr error:", e);
            return null;
        }
    }

    private void formatTime(RoadTransportQuaCertOcrVo vo, QualificationCategoryRpcVo qualificationCategoryRpcVo) {
        String regStr1 = "\\d+-\\d+-\\d+";
        Pattern pattern1 = Pattern.compile(regStr1);
        String regStr2 = "\\d+年\\d+月\\d+日";
        Pattern pattern2 = Pattern.compile(regStr2);
        if (StringUtils.isNotBlank(qualificationCategoryRpcVo.getExpiryDate())) {
            Matcher matcher1 = pattern1.matcher(qualificationCategoryRpcVo.getExpiryDate());
            if (matcher1.find()) {
                vo.setExpiryDate(TimeUtil.dayToDate(qualificationCategoryRpcVo.getExpiryDate()));
            }
            Matcher matcher2 = pattern2.matcher(qualificationCategoryRpcVo.getExpiryDate());
            if (matcher2.find()) {
                String expiryDate = qualificationCategoryRpcVo.getExpiryDate();
                if (expiryDate.startsWith("至")) {
                    expiryDate = expiryDate.replace("至", "");
                }
                vo.setExpiryDate(TimeUtil.chinaDayToDate(expiryDate));
            }
            //兼容后台时间控件bug
            if (vo.getExpiryDate() != null) {
                long time = vo.getExpiryDate().getTime();
                if (time <= 0 || !TimeUtil.checkYear(vo.getExpiryDate())) {
                    vo.setExpiryDate(null);
                }
            }
        }
        if (StringUtils.isNotBlank(qualificationCategoryRpcVo.getInitialIssueDate())) {
            Matcher matcher1 = pattern1.matcher(qualificationCategoryRpcVo.getInitialIssueDate());
            if (matcher1.find()) {
                vo.setInitialIssueDate(TimeUtil.dayToDate(qualificationCategoryRpcVo.getInitialIssueDate()));
            }
            Matcher matcher2 = pattern2.matcher(qualificationCategoryRpcVo.getInitialIssueDate());
            if (matcher2.find()) {
                vo.setInitialIssueDate(TimeUtil.chinaDayToDate(qualificationCategoryRpcVo.getInitialIssueDate()));
            }
            //兼容后台时间控件bug
            if (vo.getInitialIssueDate() != null) {
                long time = vo.getInitialIssueDate().getTime();
                if (time <= 0 || !TimeUtil.checkYear(vo.getInitialIssueDate())) {
                    vo.setInitialIssueDate(null);
                }
            }
        }
    }

    private <T> T getData(Response<T> response) {
        log.info("query response:{}", response);
        if (!response.isSuccessful()) {
            return null;
        }
        T body = response.body();
        log.info("query get data:【{}】", JSON.toJSONString(body));
        return body;
    }
}
