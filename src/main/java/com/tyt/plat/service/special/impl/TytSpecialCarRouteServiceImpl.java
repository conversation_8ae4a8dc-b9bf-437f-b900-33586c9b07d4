package com.tyt.plat.service.special.impl;

import com.tyt.plat.entity.base.TytSpecialCarRoute;
import com.tyt.plat.mapper.base.TytSpecialCarRouteMapper;
import com.tyt.plat.service.special.TytSpecialCarRouteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/08/12 13:08
 */
@Slf4j
@Service
public class TytSpecialCarRouteServiceImpl implements TytSpecialCarRouteService {

    @Autowired
    private TytSpecialCarRouteMapper tytSpecialCarRouteMapper;

    @Override
    public List<TytSpecialCarRoute> getBySpecialId(Long id){
        return tytSpecialCarRouteMapper.getBySpecialId(id);
    }

    @Override
    public void deleteBySpecialId(Long id){
        tytSpecialCarRouteMapper.deleteBySpecialId(id);
    }

    @Override
    public void saveBatch(String routes, Long id){
        String[] fruits = routes.split(",");
        List<TytSpecialCarRoute> cars = new ArrayList<>();
        for (String fruit : fruits) {
            String[] parts = fruit.split("-");
            TytSpecialCarRoute route = new TytSpecialCarRoute();
            route.setStartCity(parts[0]);
            route.setDestCity(parts[1]);
            route.setSpecialId(id);
            route.setCreateTime(new Date());
            route.setModifyTime(new Date());
            cars.add(route);
        }
        if(CollectionUtils.isNotEmpty(cars)){
            tytSpecialCarRouteMapper.insertList(cars);
        }
    }
}
