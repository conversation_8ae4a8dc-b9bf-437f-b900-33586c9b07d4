package com.tyt.plat.service.special.impl;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.commons.model.PageParameter;
import com.tyt.plat.commons.tools.CustomPageHelper;
import com.tyt.plat.entity.base.TytSpecialCar;
import com.tyt.plat.entity.base.TytSpecialCarRoute;
import com.tyt.plat.mapper.base.TytSpecialCarMapper;
import com.tyt.plat.service.special.TytSpecialCarRouteService;
import com.tyt.plat.service.special.TytSpecialCarService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/07/16 19:18
 */
@Service
@Slf4j
public class TytSpecialCarServiceImpl implements TytSpecialCarService {

    @Autowired
    private TytSpecialCarMapper tytSpecialCarMapper;

    @Autowired
    private TytSpecialCarRouteService tytSpecialCarRouteService;


    @Override
    public Map<String, Object> getList(Long userId, Integer page, Integer pageSize){
        List<TytSpecialCar> cars = tytSpecialCarMapper.getList(userId,(page - 1) * pageSize,pageSize);
        Integer total = tytSpecialCarMapper.getTotal(userId);
        if(CollectionUtils.isNotEmpty(cars)){
            for(TytSpecialCar car : cars){
                List<TytSpecialCarRoute> routes = tytSpecialCarRouteService.getBySpecialId(car.getId());
                car.setRoutes(routes);
            }
        }
        //数据封装
        Map<String, Object> map = new HashMap<>(8);
        map.put("total", total);
        map.put("list", cars);
        return map;
    }

    @Override
    public TytSpecialCar getById(Long id){
        TytSpecialCar tytSpecialCar = tytSpecialCarMapper.selectByPrimaryKey(id);
        List<TytSpecialCarRoute> routes = tytSpecialCarRouteService.getBySpecialId(tytSpecialCar.getId());
        tytSpecialCar.setRoutes(routes);
        return tytSpecialCar;
    }

    @Override
    public Integer getInfo(Long userId, Long driverId, Long driverUserId){
        Integer num = tytSpecialCarMapper.getInfo(userId,driverId,driverUserId);
        return num;
    }
}
