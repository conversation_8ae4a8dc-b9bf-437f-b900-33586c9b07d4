package com.tyt.plat.service.special.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.plat.entity.base.CsCustomQcCode;
import com.tyt.plat.entity.base.CsNewCustom;
import com.tyt.plat.mapper.base.CsCustomQcCodeMapper;
import com.tyt.plat.mapper.base.CsNewCustomMapper;
import com.tyt.plat.service.special.CsNewCustomService;
import com.tyt.plat.vo.invoice.MaintainedUserInfoDto;
import com.tyt.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/08/09 11:14
 */
@Service
@Slf4j
public class CsNewCustomServiceImpl implements CsNewCustomService {



    @Autowired
    private CsNewCustomMapper csNewCustomMapper;

    @Autowired
    private CsCustomQcCodeMapper csCustomQcCodeMapper;

    @Override
    public void addpcCodeCustom(Long userId, String phone, Integer type,Long qcCodeId){

        MaintainedUserInfoDto user = csNewCustomMapper.checkMaintainedUser(phone);
        if(null != user ){
            log.info("根据二维码添加用户已注册");
            return;
        }
        CsNewCustom custom = csNewCustomMapper.getNewCustomByCellPhone(phone);
        if(custom!=null && !Objects.equals(5, custom.getBelongTo())){
            log.info("该客户已被【{}】添加到新增客户",custom.getModifyName());
            return;
        }
        MaintainedUserInfoDto customVo = csNewCustomMapper.getByBindId(userId);
        log.info("客服人员不为空【{}】",customVo.toString());
        if(null != customVo) {
            CsNewCustom newCustom = new CsNewCustom();
            newCustom.setCustomPhone(phone);
            newCustom.setCustomSource("10");
            newCustom.setRegisterStatus(Short.valueOf("2"));
            newCustom.setModifyId(userId);
            newCustom.setModifyName(customVo.getTrueName());
            newCustom.setBelongTo(4);
            newCustom.setCtime(new Date());
            newCustom.setUtime(new Date());
            newCustom.setStatus(Short.valueOf("1"));
            newCustom.setIsRecommendUser(Short.valueOf("1"));
            newCustom.setRecommendCellPhone(customVo.getCellPhone());
            newCustom.setRecommendUserId(userId);
            newCustom.setRemark("二维码邀请");
            CsCustomQcCode csCustomQcCode = csCustomQcCodeMapper.selectByPrimaryKey(qcCodeId);
            log.info("csCustomQcCode:{}", JSON.toJSONString(csCustomQcCode));
            if(Objects.nonNull(csCustomQcCode)){
                newCustom.setCity(csCustomQcCode.getCity());
                newCustom.setGift(csCustomQcCode.getGift());
                newCustom.setSelfDefine(csCustomQcCode.getSelfDefine());
            }
            csNewCustomMapper.insert(newCustom);
        }
    }
}
