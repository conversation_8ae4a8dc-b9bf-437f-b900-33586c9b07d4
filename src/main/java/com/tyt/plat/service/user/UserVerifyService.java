package com.tyt.plat.service.user;

import com.tyt.model.User;
import com.tyt.plat.enums.ValidDateFlagEnum;
import com.tyt.plat.vo.face.FaceV3VerifyResp;
import com.tyt.plat.vo.face.FaceV5VerifyResp;
import com.tyt.plat.vo.user.EnterpriseFaceVerifyReq;
import com.tyt.plat.vo.user.FaceUserIdentityAuthReq;
import com.tyt.plat.vo.user.UserFaceVerifyVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

/**
 * 人身核验相关.
 */
public interface UserVerifyService {

    ValidDateFlagEnum getValidDateFlag(Integer idCardLongTerm, Date idCardValidDate);

    /**
     * 每天验证5次校验.
     * @param userId userId
     * @return int
     */
    int getVerifyDayCount(Long userId, boolean check);

    /**
     * 每天验证次数增加.
     * @param userId userId
     * @return int
     */
    int incrVerifyDayCount(Long userId);

    int getUserFaceTotalCount(Long userId, boolean check);

    int incrUserFaceTotalCount(Long userId);

    /**
     * 用户当天人脸总次数.
     * @param userId userId
     * @param check check
     * @return int
     */
    int getUserFaceIdCardCount(Long userId, String idCard, boolean check);

    int incrUserFaceIdCardCount(Long userId, String idCard);

    /**
     * 用户二要素认证.
     * @param idCard idCard
     * @param userName userName
     * @return Integer
     */
    Integer realVerify(String idCard, String userName);

    /**
     * 校验是否允许修改实名信息.
     * @param idCard idCard
     * @param trueName trueName
     * @param dbUser dbUser
     */
    void checkChangeRealAllow(String idCard, String trueName, User dbUser);

    /**
     * 用户二要素认证.
     * @param idCard idCard
     * @param userName userName
     * @return Integer
     */
    Integer checkAndRealVerify(Long userId, String idCard, String userName);

    /**
     * 人脸识别获取token.
     * @param userId userId
     * @param idCard idCard
     * @return String
     */
    UserFaceVerifyVo getFaceTokenV5(Long userId, String idCard);

    FaceV5VerifyResp faceVerifyV5(String idCard, String trueName, String faceToken);

    /**
     * 人脸识别获取token.
     * @param userId userId
     * @param idCard idCard
     * @return String
     */
    UserFaceVerifyVo getFaceTokenV3(Long userId, String idCard, String trueName);

    FaceV3VerifyResp faceVerifyV3(String faceToken, MultipartFile megliveData);

    /**
     * 判断是否人脸认证通过.
     * @param verifyResp verifyResp
     * @return boolean
     */
    boolean isVaceVerifyPass(FaceV3VerifyResp verifyResp);

    /**
     * 人脸识别token.
     * @param userId userId
     * @return UserFaceVerifyVo
     */
    UserFaceVerifyVo getEnterpriseFaceToken(Long userId);

    /**
     * 人脸识别实名认证保存.
     * @param enterpriseFaceVerifyReq enterpriseFaceVerifyReq
     * @return Integer
     */
    UserFaceVerifyVo updateEnterpriseFaceVerify(EnterpriseFaceVerifyReq enterpriseFaceVerifyReq);

}
