package com.tyt.plat.service.user;

import com.tyt.plat.vo.user.EnterpriseModifyCheckVo;
import java.math.BigDecimal;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/5/30 17:16
 */
public interface ApiInvoiceEnterpriseService {

    /**
     * 校验企业信息是否可以修改
     */
    EnterpriseModifyCheckVo checkModifyAllow(Long userId) ;

    /**
     * 根据用户ID和开票主体ID获取费率，如果不可开票则不会返回费率
     */
    BigDecimal invoiceEnterpriseGetTaxRate(Long userId, Long dominantId);

}
