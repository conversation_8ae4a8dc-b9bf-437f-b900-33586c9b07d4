package com.tyt.plat.service.user;

import com.tyt.plat.vo.user.UserCarryPointTotalVo;

import java.util.List;

/**
 * <p>
 * 用户车方运点分 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-01-15
 */
public interface ApiUserCarryPointService {

    UserCarryPointTotalVo getUserCarryPointTotal(Long userId) throws Exception;

    List<UserCarryPointTotalVo> getUserCarryPointList(String userIds) throws Exception;
}
