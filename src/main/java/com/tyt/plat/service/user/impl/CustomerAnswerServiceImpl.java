package com.tyt.plat.service.user.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.plat.entity.base.TytAnswerCategory;
import com.tyt.plat.entity.base.TytAnswerPage;
import com.tyt.plat.entity.base.TytAnswerPageContent;
import com.tyt.plat.enums.AnswerShowScopeEnum;
import com.tyt.plat.mapper.base.TytAnswerCategoryMapper;
import com.tyt.plat.mapper.base.TytAnswerPageContentMapper;
import com.tyt.plat.mapper.base.TytAnswerPageMapper;
import com.tyt.plat.service.user.CustomerAnswerService;
import com.tyt.plat.vo.user.AnswerPageDetailVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客服问答.
 * <AUTHOR>
 *
 * @date 2023-9-6 13:37:40
 */
@Slf4j
@Service
public class CustomerAnswerServiceImpl implements CustomerAnswerService {

    @Autowired
    private TytAnswerCategoryMapper tytAnswerCategoryMapper;

    @Autowired
    private TytAnswerPageMapper tytAnswerPageMapper;

    @Autowired
    private TytAnswerPageContentMapper tytAnswerPageContentMapper;

    private static final String CUSTOM_ANSWER_REDIS_KEY = "customanswer:answer:";

    @Override
    public List<TytAnswerCategory> getCategoryList(Integer belongType) {
        List<TytAnswerCategory> categoryList = tytAnswerCategoryMapper.getCategoryList(belongType);
        return categoryList;
    }

    @Override
    public List<TytAnswerPage> getAnswerList(Long categoryId, Integer pageSize, Integer sortNumber) {

        List<TytAnswerPage> answerList = tytAnswerPageMapper.getAnswerList(categoryId, pageSize, sortNumber);

        return answerList;
    }

    @Override
    public AnswerPageDetailVo getPageDetail(Long pageId) {

        TytAnswerPage tytAnswerPage = tytAnswerPageMapper.selectByPrimaryKey(pageId);

        if(tytAnswerPage == null){
            throw TytException.createException(ResponseEnum.request_error.info("问答不存在！"));
        }
        Integer showScope = tytAnswerPage.getShowScope();

        if(!AnswerShowScopeEnum.whole.equalsCode(showScope)){
            throw TytException.createException(ResponseEnum.request_error.info("您无权访问该内容！"));
        }
        AnswerPageDetailVo pageDetailVo = new AnswerPageDetailVo();
        BeanUtils.copyProperties(tytAnswerPage, pageDetailVo);

        Example exa = new Example(TytAnswerPageContent.class);
        exa.and().andEqualTo("pageId", pageId);

        TytAnswerPageContent tytAnswerPageContent = tytAnswerPageContentMapper.selectOneByExample(exa);

        String contentText = null;

        if(tytAnswerPageContent != null){
            contentText = tytAnswerPageContent.getContentText();
        }

        pageDetailVo.setContentText(contentText);
        return pageDetailVo;
    }

    @Override
    public List<TytAnswerPage> queryByTitleFragment(String titleFragment, Integer belongType) {
        List<TytAnswerPage> tytAnswerPages = new ArrayList<>();
        if (StringUtils.isBlank(titleFragment)) {
            return tytAnswerPages;
        }
        String redisKey = CUSTOM_ANSWER_REDIS_KEY + MD5Util.GetMD5Code(titleFragment);
        String tytAnswerPagesJsonString = RedisUtil.get(redisKey);
        if (StringUtils.isNotEmpty(tytAnswerPagesJsonString)) {
            tytAnswerPages = JSON.parseArray(tytAnswerPagesJsonString, TytAnswerPage.class);
            RedisUtil.expire(redisKey, 30);
            return tytAnswerPages;
        } else {
            List<TytAnswerCategory> categoryList = tytAnswerCategoryMapper.getCategoryListByBelongType(belongType);
            if (CollectionUtils.isNotEmpty(categoryList)) {
                List<Long> categoryIdList = categoryList.stream().map(TytAnswerCategory::getId).collect(Collectors.toList());
                tytAnswerPages = tytAnswerPageMapper.queryByTitleFragment(titleFragment, categoryIdList);
                if (CollectionUtils.isEmpty(tytAnswerPages)) {
                    tytAnswerPages = new ArrayList<>();
                }
            } else {
                tytAnswerPages = new ArrayList<>();
            }
            RedisUtil.set(redisKey, JSON.toJSONString(tytAnswerPages), 30);
            return tytAnswerPages;
        }
    }
}
