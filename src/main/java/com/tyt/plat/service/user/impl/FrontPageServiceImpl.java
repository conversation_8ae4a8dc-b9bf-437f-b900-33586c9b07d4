package com.tyt.plat.service.user.impl;

import com.tyt.plat.entity.base.TytFrontCategory;
import com.tyt.plat.entity.base.TytFrontPage;
import com.tyt.plat.entity.base.TytFrontPageContent;
import com.tyt.plat.enums.GlobalStatusEnum;
import com.tyt.plat.enums.front.page.ProtocolPopEnum;
import com.tyt.plat.mapper.base.TytFrontCategoryMapper;
import com.tyt.plat.mapper.base.TytFrontPageContentMapper;
import com.tyt.plat.mapper.base.TytFrontPageMapper;
import com.tyt.plat.service.user.FrontPageService;
import com.tyt.plat.vo.user.FrontPageDetailVo;
import com.tyt.plat.vo.user.ProtocolIsPopUpVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 运营中台.
 * <AUTHOR>
 *
 * @date 2023-9-6 13:37:40
 */
@Slf4j
@Service
public class FrontPageServiceImpl implements FrontPageService {

    @Autowired
    private TytFrontCategoryMapper tytFrontCategoryMapper;

    @Autowired
    private TytFrontPageMapper tytFrontPageMapper;

    @Autowired
    private TytFrontPageContentMapper tytFrontPageContentMapper;

    @Override
    public FrontPageDetailVo getPageDetail(Long pageId) {

        TytFrontPage frontPage = tytFrontPageMapper.selectByPrimaryKey(pageId);

        if(frontPage == null){
            throw TytException.createException(ResponseEnum.request_error.info("该页面不存在！"));
        }

        Integer status = frontPage.getStatus();

        if(!GlobalStatusEnum.yes.equalsCode(status)){
            throw TytException.createException(ResponseEnum.request_error.info("该页面不可访问！"));
        }

        FrontPageDetailVo pageDetailVo = new FrontPageDetailVo();
        BeanUtils.copyProperties(frontPage, pageDetailVo);

        Long categoryId = frontPage.getCategoryId();

        TytFrontCategory tytFrontCategory = tytFrontCategoryMapper.selectByPrimaryKey(categoryId);

        if(tytFrontCategory != null){
            String categoryName = tytFrontCategory.getCategoryName();

            pageDetailVo.setBelongType(tytFrontCategory.getBelongType());
            pageDetailVo.setCategoryName(categoryName);
        }

        TytFrontPageContent frontPageContent = tytFrontPageContentMapper.getPageContentByPageId(pageId);

        if(frontPageContent != null){
            String contentText = frontPageContent.getContentText();
            pageDetailVo.setContentText(contentText);
        }

        return pageDetailVo;
    }

    @Override
    public ProtocolIsPopUpVo getProtocolIsPopUp(Long pageId, Date modifyTime) {
        TytFrontPage frontPage = tytFrontPageMapper.selectByPrimaryKey(pageId);
        if (Objects.isNull(frontPage)) {
            throw TytException.createException(ResponseEnum.request_error.info("该页面不存在！"));
        }
        Integer status = frontPage.getStatus();
        if (!GlobalStatusEnum.yes.equalsCode(status)) {
            throw TytException.createException(ResponseEnum.request_error.info("该页面不可访问！"));
        }
        ProtocolIsPopUpVo protocolIsPopUpVo = getProtocolIsPopUpVo(modifyTime, frontPage);
        TytFrontPageContent frontPageContent = tytFrontPageContentMapper.getPageContentByPageId(pageId);
        if(Objects.nonNull(frontPageContent)){
            protocolIsPopUpVo.setContentText(frontPageContent.getContentText());
            protocolIsPopUpVo.setTitle(frontPageContent.getTitle());
        }
        return protocolIsPopUpVo;

    }

    /**
     * @param modifyTime 修改時間
     * @param frontPage  协议
     * @return
     */
    private static ProtocolIsPopUpVo getProtocolIsPopUpVo(Date modifyTime, TytFrontPage frontPage) {
        ProtocolIsPopUpVo protocolIsPopUpVo = new ProtocolIsPopUpVo();
        // 获取最新修改时间，如果为null则视为第一次访问
        Date latestModifyTime = frontPage.getModifyTime();
        //协议更新时间为空
        if (Objects.isNull(modifyTime)) {
            protocolIsPopUpVo.setPop(ProtocolPopEnum.POP.getCode());
            //后续协议有更新
        } else if (Objects.nonNull(latestModifyTime) && latestModifyTime.getTime() > modifyTime.getTime()) {
            protocolIsPopUpVo.setPop(ProtocolPopEnum.POP.getCode());
        } else {
            protocolIsPopUpVo.setPop(ProtocolPopEnum.NO_POP.getCode());
        }
        protocolIsPopUpVo.setLatestModifyTime(latestModifyTime);
        return protocolIsPopUpVo;
    }
}
