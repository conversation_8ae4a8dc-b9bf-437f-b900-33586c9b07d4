package com.tyt.plat.service.user;

import com.tyt.base.bean.BaseParameter;
import com.tyt.plat.entity.base.TytAnswerCategory;
import com.tyt.plat.entity.base.TytAnswerPage;
import com.tyt.plat.vo.user.AnswerPageDetailVo;

import java.util.List;

/**
 * 客服问答
 */
public interface CustomerAnswerService {

    /**
     * 根据类型获取问答分类.
     * @param belongType 类型
     * @return
     */
    List<TytAnswerCategory> getCategoryList(Integer belongType);

    /**
     * 问答列表.
     * @param categoryId
     * @param pageSize
     * @param sortNumber
     * @return
     */
    List<TytAnswerPage> getAnswerList(Long categoryId, Integer pageSize, Integer sortNumber);

    /**
     * 问答详情.
     * @param pageId
     * @return
     */
    AnswerPageDetailVo getPageDetail(Long pageId);

    /**
     * 根据问答标题模糊查询问答列表
     * @param titleFragment 问答标题片段
     * @param belongType 类型（1车，2货）
     * @return 问答列表
     */
    List<TytAnswerPage> queryByTitleFragment(String titleFragment, Integer belongType);
}
