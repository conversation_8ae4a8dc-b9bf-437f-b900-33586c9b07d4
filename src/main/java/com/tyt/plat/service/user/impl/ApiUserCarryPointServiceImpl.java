package com.tyt.plat.service.user.impl;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.client.user.ApiUserCarryPointClient;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.service.user.ApiUserCarryPointService;
import com.tyt.plat.vo.user.UserCarryPointTotalVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.util.List;

/**
 * <p>
 * 用户附属表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-01-15
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class ApiUserCarryPointServiceImpl implements ApiUserCarryPointService {

    @Autowired
    private ApiUserCarryPointClient apiUserCarryPointClient;

    @Override
    public UserCarryPointTotalVo getUserCarryPointTotal(Long userId) throws Exception{
        long startTime = System.currentTimeMillis();
        Response<InternalWebResult<UserCarryPointTotalVo>> userCarryPointTotal = apiUserCarryPointClient.getUserCarryPointTotal(userId).execute();
        UserCarryPointTotalVo dataDetail = InternalClientUtil.getDataDetail(userCarryPointTotal);
        CommonUtil.printSpendTime(startTime);
        return dataDetail;
    }

    @Override
    public List<UserCarryPointTotalVo> getUserCarryPointList(String userIds) throws Exception{
        long startTime = System.currentTimeMillis();
        Response<InternalWebResult<List<UserCarryPointTotalVo>>> userCarryPointTotal = apiUserCarryPointClient.getUserCarryPointList(userIds).execute();
        List<UserCarryPointTotalVo> pointList = InternalClientUtil.getDataDetail(userCarryPointTotal);
        CommonUtil.printSpendTime(startTime);
        return pointList;
    }

}
