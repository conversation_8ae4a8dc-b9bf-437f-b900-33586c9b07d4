package com.tyt.plat.service.user;

import com.tyt.plat.vo.user.FrontPageDetailVo;
import com.tyt.plat.vo.user.ProtocolIsPopUpVo;

import java.util.Date;

/**
 * 运营中台.
 */
public interface FrontPageService {

    /**
     * 营销页面详情.
     * @param pageId
     * @return
     */
    FrontPageDetailVo getPageDetail(Long pageId);

    /**
     * 获取协是否议弹窗.
     * @param pageId 协议主键id
     * @param modifyTime 修改时间
     * @return
     */
    ProtocolIsPopUpVo getProtocolIsPopUp(Long pageId, Date modifyTime);
}
