package com.tyt.plat.service.user.impl;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.client.user.ApiInvoiceEnterpriseClient;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.service.user.ApiInvoiceEnterpriseService;
import com.tyt.plat.vo.user.EnterpriseModifyCheckVo;
import com.tyt.plat.vo.user.UserCarryPointTotalVo;
import com.tyt.service.common.exception.TytException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * <p>
 * 用户附属表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-01-15
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class ApiInvoiceEnterpriseServiceImpl implements ApiInvoiceEnterpriseService {

    @Autowired
    private ApiInvoiceEnterpriseClient apiInvoiceEnterpriseClient;

    @Override
    public EnterpriseModifyCheckVo checkModifyAllow(Long userId) {
        long startTime = System.currentTimeMillis();
        Response<InternalWebResult<EnterpriseModifyCheckVo>> userCarryPointTotal = null;
        try {
            userCarryPointTotal = apiInvoiceEnterpriseClient.checkModifyAllow(userId).execute();
            EnterpriseModifyCheckVo dataDetail = InternalClientUtil.getDataDetail(userCarryPointTotal);
            CommonUtil.printSpendTime(startTime);
            return dataDetail;
        } catch (IOException e) {
            log.error("", e);
            throw TytException.createException(e);
        }
    }

    @Override
    public BigDecimal invoiceEnterpriseGetTaxRate(Long userId, Long dominantId) {
        long startTime = System.currentTimeMillis();
        Response<InternalWebResult<BigDecimal>> execute;
        try {
            execute = apiInvoiceEnterpriseClient.invoiceEnterpriseGetTaxRate(userId, dominantId).execute();
            BigDecimal rate = InternalClientUtil.getDataDetailNoThrowException(execute);
            CommonUtil.printSpendTime(startTime);
            return rate;
        } catch (IOException e) {
            log.error("", e);
            throw TytException.createException(e);
        }
    }

}
