package com.tyt.plat.service.user.impl;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.User;
import com.tyt.plat.constant.ConfigKeyConstant;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.constant.TimeUnitConstant;
import com.tyt.plat.constant.TransactionConstant;
import com.tyt.plat.enums.*;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.remote.MbAccountService;
import com.tyt.plat.service.user.UserVerifyService;
import com.tyt.plat.vo.face.*;
import com.tyt.plat.vo.mb.UserRealResponse;
import com.tyt.plat.vo.user.EnterpriseFaceVerifyReq;
import com.tyt.plat.vo.user.IdCardTwoElementVerifyDTO;
import com.tyt.plat.vo.user.IdCardTwoElementVerifyVO;
import com.tyt.plat.vo.user.UserFaceVerifyVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;

/**
 * 人身核验相关.
 * <AUTHOR>
 *
 * @date 2023-9-6 13:37:40
 */
@Slf4j
@Service
public class UserVerifyServiceImpl implements UserVerifyService {

    /**
     * 每天最多验证次数5.
     */
    public static final int DAY_VERIFY_MAX = 5;

    /**
     * 每天总共最多提交次数40.
     */
    public static final int FACE_TOTAL_MAX = 40;

    /**
     * 企业人脸识别最大次数
     */
    public static final int ENTERPRISE_FACE_MAX = 10;

    @Autowired
    private UserService userService;

    @Autowired
    private CommonApiService commonApiService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private MbAccountService mbAccountService;

    @Autowired
    private TytUserIdentityAuthService tytUserIdentityAuthService;

    private String getDayErrorMsg(){
        String msg = "今天提交已超过" + DAY_VERIFY_MAX + "次，请明天再试";
        return msg;
    }

    @Override
    public ValidDateFlagEnum getValidDateFlag(Integer idCardLongTerm, Date idCardValidDate) {

        if (GlobalStatusEnum.yes.equalsCode(idCardLongTerm)) {
            //长期
            return ValidDateFlagEnum.VALID;
        }

        if (idCardValidDate == null) {
            return ValidDateFlagEnum.UNKNOWN;
        }

        Date nowTime = new Date();

        if (nowTime.after(idCardValidDate)) {
            //已过期
            return ValidDateFlagEnum.EXPIRE;
        }

        long validTs = idCardValidDate.getTime();

        Date endDate = DateUtil.addTime(nowTime, Calendar.DAY_OF_MONTH, 90);
        long endTs = endDate.getTime();

        if (endTs > validTs) {
            return ValidDateFlagEnum.LOW_VALID;
        }

        return ValidDateFlagEnum.UNKNOWN;
    }

    @Override
    public int getVerifyDayCount(Long userId, boolean check){
        String verifyKey = RedisKeyConstant.getUserRealVerifyKey(userId);

        String countValue = RedisUtil.get(verifyKey);

        int verifyCount = 0;

        if(StringUtils.isNotBlank(countValue)){
            verifyCount = Integer.parseInt(countValue);
        }

        if(check && verifyCount >= DAY_VERIFY_MAX){
            throw TytException.createException(ResponseEnum.request_error.info(this.getDayErrorMsg()));
        }

        return verifyCount;
    }

    @Override
    public int incrVerifyDayCount(Long userId){
        int verifyCount = this.getVerifyDayCount(userId, false);
        verifyCount = verifyCount + 1;

        String verifyKey = RedisKeyConstant.getUserRealVerifyKey(userId);

        RedisUtil.set(verifyKey, verifyCount + "", TimeUnitConstant.DAY_SECOND);
        return verifyCount;
    }

    @Override
    public Integer realVerify(String idCard, String userName) {

        IdCardTwoElementVerifyDTO req = new IdCardTwoElementVerifyDTO();
        req.setIdCardNo(idCard);
        req.setIdCardName(userName);

        IdCardTwoElementVerifyVO idCardTwoElementVerifyVO = commonApiService.twoElementVerify(req);

        Integer result = idCardTwoElementVerifyVO.isSuccess() ? GlobalStatusEnum.yes.getCode() : GlobalStatusEnum.no.getCode();

        return result;
    }

    @Override
    public void checkChangeRealAllow(String idCard, String trueName, User dbUser){

        Long userId = dbUser.getId();

        //校验身份证，姓名是否更改
        String dbIdCard = dbUser.getIdCard();
        String dbTrueName = dbUser.getTrueName();

        if (StringUtils.equals(dbIdCard, idCard) && StringUtils.equals(dbTrueName, trueName)) {
            //证件没有修改
            log.info("user_verify_nochange : " + userId);
            return;
        }

        //校验企业认证状态


        //实名有修改
        //校验是否允许修改
        //1. 判断是否开通钱包
        UserRealResponse mbUserRealInfo = mbAccountService.getMbUserRealInfo(userId);
        String authLevel = null;
        if (mbUserRealInfo != null) {
            authLevel = mbUserRealInfo.getAuthLevel();
        }

        if (StringUtils.isBlank(authLevel) || MbAuthLevelEnum.REAL_NAME_NONE.equalsCode(authLevel)) {
            //未实名，允许修改
            log.info("user_real_info_change_allow ... " + userId);
        } else {
            //钱包未注销，不允许修改实名
            throw TytException.createException(PlatResponseEnum.USER_INFO_CHANGE_BLOCK.info());
        }
    }

    @Override
    public Integer checkAndRealVerify(Long userId, String idCard, String userName) {

        User dbUser = userService.getById(userId);

        if(dbUser == null){
            throw TytException.createException(ResponseEnum.request_error.info("用户不存在"));
        }

        this.checkChangeRealAllow(idCard, userName, dbUser);

        this.getVerifyDayCount(userId, true);

        Integer result = this.realVerify(idCard, userName);
        this.incrVerifyDayCount(userId);

        return result;
    }

    /**
     * 用户当天人脸总次数.
     * @param userId userId
     * @return int
     */
    @Override
    public int getUserFaceTotalCount(Long userId, boolean check){

        String faceUserKey = RedisKeyConstant.getUserFaceTotalKey(userId);

        String countValue = RedisUtil.get(faceUserKey);

        int faceUserCount = 0;

        if(StringUtils.isNotBlank(countValue)){
            faceUserCount = Integer.parseInt(countValue);
        }

        if(check && faceUserCount > FACE_TOTAL_MAX){
            throw TytException.createException(ResponseEnum.rest_error.info("今天提交已超过最大次数，请明天再试"));
        }

        return faceUserCount;
    }

    @Override
    public int incrUserFaceTotalCount(Long userId){
        int verifyCount = this.getUserFaceTotalCount(userId, false);
        verifyCount = verifyCount + 1;

        String faceUserKey = RedisKeyConstant.getUserFaceTotalKey(userId);

        RedisUtil.set(faceUserKey, verifyCount + "", TimeUnitConstant.DAY_SECOND);
        return verifyCount;
    }

    /**
     * 用户当天每组人脸总次数.
     * @param userId userId
     * @return int
     */
    @Override
    public int getUserFaceIdCardCount(Long userId, String idCard, boolean check){

        String countKey = RedisKeyConstant.getUserFaceIdCardKey(userId, idCard);

        String countValue = RedisUtil.get(countKey);

        int verifyCount = 0;

        if(StringUtils.isNotBlank(countValue)){
            verifyCount = Integer.parseInt(countValue);
        }

        if(check && verifyCount >= DAY_VERIFY_MAX){
            throw TytException.createException(ResponseEnum.request_error.info(this.getDayErrorMsg()));
        }

        return verifyCount;
    }

    @Override
    public int incrUserFaceIdCardCount(Long userId, String idCard){
        int verifyCount = this.getUserFaceIdCardCount(userId, idCard, false);
        verifyCount = verifyCount + 1;

        String faceUserKey = RedisKeyConstant.getUserFaceIdCardKey(userId, idCard);

        RedisUtil.set(faceUserKey, verifyCount + "", TimeUnitConstant.DAY_SECOND);
        return verifyCount;
    }

    @Override
    public UserFaceVerifyVo getFaceTokenV5(Long userId, String idCard) {

        this.getUserFaceTotalCount(userId, true);

        int faceIdCardCount = this.getUserFaceIdCardCount(userId, idCard, false);

        UserFaceVerifyVo faceTokenVo = new UserFaceVerifyVo();

        faceTokenVo.setVerifyFailCount(faceIdCardCount);

        if(faceIdCardCount < DAY_VERIFY_MAX){

            String livenessId = tytConfigService.getStringValue(ConfigKeyConstant.FACE_LIVENESS_KEY);

            FaceV5TokenReq req = new FaceV5TokenReq();
            req.setLivenessId(livenessId);

            FaceV5TokenResp bizToken = commonApiService.getBizTokenV5(req);

            String faceToken = bizToken.getBizToken();
            faceTokenVo.setFaceToken(faceToken);
        } else {
            faceTokenVo.setMsg(this.getDayErrorMsg());
        }

        return faceTokenVo;
    }

    @Override
    public FaceV5VerifyResp faceVerifyV5(String idCard, String trueName, String faceToken){

        String verifyId = tytConfigService.getStringValue(ConfigKeyConstant.FACE_VERIFY_ID);

        FaceV5VerifyReq req = new FaceV5VerifyReq();
        req.setComparisonType(ComparisonTypeEnum.FACE_REAL.getCode());
        req.setDataType("0");

        /**
         * 是否开启传输数据加密，详细说明见：加密说明
         * 0：不开启
         * 1：SM2
         * 2：RSA
         */
        req.setEncryptionType("0");

        req.setIdCardName(trueName);
        req.setIdCardNumber(idCard);

        /**
         * verify场景id：本次验证请求相关配置在控台可设置，并将生成的id号在接口中使用
         */
        req.setVerifyId(verifyId);
        req.setBizToken(faceToken);

        FaceV5VerifyResp faceVerifyResp = commonApiService.faceVerifyV5(req);

        return faceVerifyResp;

    }

    @Override
    public UserFaceVerifyVo getFaceTokenV3(Long userId, String idCard, String trueName) {

        int userFaceTotalCount = this.getUserFaceTotalCount(userId, true);

        int faceIdCardCount = this.getUserFaceIdCardCount(userId, idCard, true);

        UserFaceVerifyVo faceTokenVo = new UserFaceVerifyVo();

        faceTokenVo.setVerifyFailCount(faceIdCardCount);

        if(faceIdCardCount < DAY_VERIFY_MAX){

            FaceV3TokenReq req = new FaceV3TokenReq();
            req.setIdCardNumber(idCard);
            req.setIdCardName(trueName);

            FaceV3TokenResp bizToken = commonApiService.getBizTokenV3(req);
            String faceToken = bizToken.getBizToken();

            if(StringUtils.isBlank(faceToken)){
                throw TytException.createException(ResponseEnum.request_error.info("人脸识别组件初始化失败，请重试！"));
            }

            faceTokenVo.setFaceToken(faceToken);
        } else {
            faceTokenVo.setMsg(this.getDayErrorMsg());
        }
        userFaceTotalCount = this.incrUserFaceTotalCount(userId);

        return faceTokenVo;
    }

    @Override
    public FaceV3VerifyResp faceVerifyV3(String faceToken, MultipartFile megliveData){

        byte[] dataBytes = null;
        try {
            dataBytes = megliveData.getBytes();
        } catch (IOException e) {
            throw TytException.createException(e);
        }

        String megliveDataBase64 = CommonUtil.encodeBase64(dataBytes);

        FaceV3VerifyReq req = new FaceV3VerifyReq();

        req.setBizToken(faceToken);
        req.setMegliveDataBase64(megliveDataBase64);

        FaceV3VerifyResp faceVerifyResp = commonApiService.faceVerifyV3(req);

        return faceVerifyResp;

    }

    @Override
    public boolean isVaceVerifyPass(FaceV3VerifyResp verifyResp) {

        if(verifyResp == null){
            return false;
        }

        Integer resultCode = verifyResp.getResultCode();

        if(resultCode != null && 1000 == resultCode){
            //认证通过.
            return true;
        }

        return false;
    }

    private void checkEnterpriseFace(TytUserIdentityAuth userIdentityAuth){

        if(userIdentityAuth == null){
            throw TytException.createException(ResponseEnum.request_error.info("请先提交用户实名认证信息"));
        }

        Integer infoStatus = userIdentityAuth.getInfoStatus();
        if(!UserInfoStatusEnum.SUCCESS.equalsCode(infoStatus)){
            throw TytException.createException(ResponseEnum.request_error.info("当前账号未实名审核"));
        }

        Integer faceVerify = userIdentityAuth.getFaceVerify();

        if(VerifyStatusEnum.SUCCESS.equalsCode(faceVerify)){
            throw TytException.createException(ResponseEnum.request_error.info("当前账号已实名通过"));
        }
    }

    /**
     * 校验企业人脸识别次数
     * @param userId userId
     */
    private int checkEnterpriseFaceTokenCount(Long userId){

        String faceCountKey = RedisKeyConstant.joinDayKey(RedisKeyConstant.ENTERPRISE_FACE_COUNT, userId + "");

        int faceCount = RedisKeyConstant.getCount(faceCountKey);

        if(faceCount >= ENTERPRISE_FACE_MAX){
            throw TytException.createException(ResponseEnum.request_error.info("今日次数已用完，请明天再试！"));
        }
        return faceCount;
    }

    /**
     * 增加企业人脸识别次数
     * @param userId userId
     */
    private int incrEnterpriseFaceTokenCount(Long userId){

        String faceCountKey = RedisKeyConstant.joinDayKey(RedisKeyConstant.ENTERPRISE_FACE_COUNT, userId + "");

        Long incrLong = RedisKeyConstant.incrAndExpire(faceCountKey, TimeUnitConstant.DAY_SECOND);

        return incrLong.intValue();
    }

    @Override
    public UserFaceVerifyVo getEnterpriseFaceToken(Long userId) {

        //校验次数
        int faceCount = this.checkEnterpriseFaceTokenCount(userId);

        TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getByUserId(userId + "");

        this.checkEnterpriseFace(userIdentityAuth);

        User dbUser = null;
        try {
            dbUser = userService.getByUserId(userId);
        } catch (Exception e) {
            throw TytException.createException(e);
        }

        String trueName = dbUser.getTrueName();
        String idCard = dbUser.getIdCard();

        UserFaceVerifyVo userFaceVerifyVo = new UserFaceVerifyVo();
        userFaceVerifyVo.setMaxVerifyCount(ENTERPRISE_FACE_MAX);
        userFaceVerifyVo.setVerifyFailCount(faceCount);

        FaceV3TokenReq req = new FaceV3TokenReq();
        req.setIdCardNumber(idCard);
        req.setIdCardName(trueName);

        FaceV3TokenResp bizToken = commonApiService.getBizTokenV3(req);
        String faceToken = bizToken.getBizToken();

        userFaceVerifyVo.setFaceToken(faceToken);

        if(StringUtils.isBlank(faceToken)){
            throw TytException.createException(ResponseEnum.request_error.info("人脸识别组件初始化失败，请重试！"));
        }

        return userFaceVerifyVo;
    }

    @Override
    public UserFaceVerifyVo updateEnterpriseFaceVerify(EnterpriseFaceVerifyReq enterpriseFaceVerifyReq) {

        Long userId = enterpriseFaceVerifyReq.getUserId();
        int faceFailCount = this.checkEnterpriseFaceTokenCount(userId);

        TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getByUserId(userId + "");
        this.checkEnterpriseFace(userIdentityAuth);

        Date nowTime = new Date();

        String faceToken = enterpriseFaceVerifyReq.getFaceToken();
        MultipartFile megliveData = enterpriseFaceVerifyReq.getMegliveData();

        FaceV3VerifyResp faceV3VerifyResp = this.faceVerifyV3(faceToken, megliveData);

        Integer verifyStatus = null;

        UserFaceVerifyVo faceVerifyVo = new UserFaceVerifyVo();

        if(!this.isVaceVerifyPass(faceV3VerifyResp)){
            faceFailCount = this.incrEnterpriseFaceTokenCount(userId);

            verifyStatus = GlobalStatusEnum.no.getCode();
            faceVerifyVo.setMsg("人脸认证失败");

            userIdentityAuth.setFaceVerify(VerifyStatusEnum.FAIL.getCode());

        } else {
            //认证通过
            verifyStatus = GlobalStatusEnum.yes.getCode();

            userIdentityAuth.setFaceVerify(VerifyStatusEnum.SUCCESS.getCode());
            userIdentityAuth.setRealVerify(VerifyStatusEnum.SUCCESS.getCode());
        }

        userIdentityAuth.setUtime(nowTime);
        tytUserIdentityAuthService.update(userIdentityAuth);

        faceVerifyVo.setVerifyFailCount(faceFailCount);
        faceVerifyVo.setVerifyStatus(verifyStatus);
        faceVerifyVo.setMaxVerifyCount(ENTERPRISE_FACE_MAX);

        return faceVerifyVo;
    }
}
