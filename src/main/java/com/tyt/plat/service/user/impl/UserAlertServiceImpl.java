package com.tyt.plat.service.user.impl;

import com.tyt.model.User;
import com.tyt.plat.constant.AbtestConstant;
import com.tyt.plat.entity.base.TytUserRecord;
import com.tyt.plat.enums.GlobalStatusEnum;
import com.tyt.plat.enums.UserRecordEnum;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.service.base.TytUserRecordService;
import com.tyt.plat.service.user.UserAlertService;
import com.tyt.plat.utils.DateUtil;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 车主身份标签.
 * <AUTHOR>
 *
 * @date 2023-9-6 13:37:40
 */
@Slf4j
@Service
public class UserAlertServiceImpl implements UserAlertService {

    @Autowired
    private TytUserRecordService tytUserRecordService;

    @Autowired
    private UserService userService;

    @Autowired
    private AbtestService abtestService;

    /**
     * 校验该地区是否允许弹窗.
     * @param userInfo userInfo
     * @return GlobalStatusEnum
     */
    private boolean checkPopAllowCity(User userInfo){

        String city = userInfo.getCity();

        if(StringUtils.isNotBlank(city)
                && (city.contains("邢台") || city.contains("保定"))){
            //不弹
            return false;
        }
        return true;
    }

    /**
     * 校验abtest 是否弹红包.
     * @param userId userId
     * @return boolean
     */
    private boolean checkPopAllowAbtest(Long userId){

        //先判断是否在abtest.
        Integer userType = abtestService.getUserType(AbtestConstant.VIP_RED_TIP, userId);

        return GlobalStatusEnum.yes.equalsCode(userType);
    }

    @Override
    public Integer vipRedPacket(Long userId) {

        User userInfo = null;
        try {
            userInfo = userService.getByUserId(userId);
        } catch (Exception e) {
            throw TytException.createException(e);
        }

        if(!this.checkPopAllowCity(userInfo)){
            //不允许弹窗
            return GlobalStatusEnum.no.getCode();
        }

        if(!this.checkPopAllowAbtest(userId)){
            //不允许弹窗
            return GlobalStatusEnum.no.getCode();
        }

        //红包abtest code
        String recordKey = UserRecordEnum.VIP_RED_PACKET_POP.getCode();

        //检查今日是否弹过
        TytUserRecord userRecord = tytUserRecordService.getByUserIdAndCode(userId, recordKey);

        final Date nowTime = new Date();
        //今天的日期标识
        String recordDayValue = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        if(userRecord != null){

            String recordValue = userRecord.getValue();

            if(recordDayValue.equals(recordValue)){
                //已弹，不再弹窗
                return GlobalStatusEnum.no.getCode();
            }
        }

        //需要弹窗，记录今日已弹窗
        try {
            tytUserRecordService.saveRecord(userRecord, userId, recordKey, 1, recordDayValue);
        } catch (Exception e) {
            log.error("", e);
        }

        return GlobalStatusEnum.yes.getCode();
    }

}
