package com.tyt.plat.service.user.impl;

import com.tyt.plat.entity.base.TytUserIdentityLabel;
import com.tyt.plat.mapper.base.TytUserIdentityLabelMapper;
import com.tyt.plat.service.user.UserIdentityLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.persistence.Column;
import java.util.Date;

/**
 * 车主身份标签.
 * <AUTHOR>
 *
 * @date 2023-9-6 13:37:40
 */
@Slf4j
@Service
public class UserIdentityLabelServiceImpl implements UserIdentityLabelService {

    @Autowired
    private TytUserIdentityLabelMapper tytUserIdentityLabelMapper;

    @Override
    public void saveGoosType(Long userId, Integer goodsTypeFirst, Integer goodsTypeSecond) {

        Example exa = new Example(TytUserIdentityLabel.class);
        exa.and().andEqualTo("userId", userId);

        TytUserIdentityLabel tytUserIdentityLabel = tytUserIdentityLabelMapper.selectOneByExample(exa);

        Date nowTime = new Date();

        if(tytUserIdentityLabel == null){

            tytUserIdentityLabel = new TytUserIdentityLabel();

            tytUserIdentityLabel.setUserId(userId);
            tytUserIdentityLabel.setGoodsTypeFirst(goodsTypeFirst);
            tytUserIdentityLabel.setGoodsTypeSecond(goodsTypeSecond);
            tytUserIdentityLabel.setCreateTime(nowTime);
            tytUserIdentityLabel.setModifyTime(nowTime);

            tytUserIdentityLabelMapper.insertSelective(tytUserIdentityLabel);

        } else {
            tytUserIdentityLabel.setUserId(userId);
            tytUserIdentityLabel.setGoodsTypeFirst(goodsTypeFirst);
            tytUserIdentityLabel.setGoodsTypeSecond(goodsTypeSecond);
            tytUserIdentityLabel.setModifyTime(nowTime);

            tytUserIdentityLabelMapper.updateByPrimaryKeySelective(tytUserIdentityLabel);
        }

    }
}
