package com.tyt.plat.service.help;

import com.tyt.base.bean.BaseParameter;
import com.tyt.plat.entity.base.TytCourseBanner;
import com.tyt.plat.entity.base.TytCourseCategory;
import com.tyt.plat.entity.base.TytCourseInfo;
import com.tyt.plat.vo.help.CourseDetailVo;
import com.tyt.plat.vo.help.UserCourseCountVo;

import java.util.List;

/**
 * 用户课程.
 * <AUTHOR>
 */
public interface UserCourseService {

    /**
     * 课程列表
     * @param sortNumber sortNumber
     * @return List
     */
    List<TytCourseInfo> getCourseList(Integer type, Long categoryId, Integer sortNumber);

    /**
     * 轮播图列表
     * @param type type
     * @return List
     */
    List<TytCourseBanner> getBannerList(Integer type);

    /**
     * 获取分类列表.
     * @param type type
     * @return List
     */
    List<TytCourseCategory> getCategoryList(Integer type);

    /**
     * 获取待学习数量
     * @param type type
     * @return int
     */
    UserCourseCountVo getNotLearnCount(Integer type, BaseParameter baseParameter);

    /**
     * 获取学习情况.
     * @param type type
     * @param userId userId
     * @return UserCourseCountVo
     */
    UserCourseCountVo getLearnInfo(Long userId, Integer type);

    /**
     * 查询我的课程列表
     * @param userId userId
     * @param type type
     * @param learnFlag learnFlag
     * @param sortNumber sortNumber
     * @return List
     */
    List<TytCourseInfo> getMyCourseList(Long userId, Integer type, Integer learnFlag, Integer sortNumber);

    /**
     * 校验预览key.
     * @param courseId courseId
     * @param previewKey previewKey
     */
    void checkPreviewKey(Long courseId, String previewKey);

    /**
     * 获取课程内容.
     * @param courseId courseId
     * @return CourseDetailVo
     */
    CourseDetailVo getCourseContent(Long courseId, boolean enableCheck);

    /**
     * 课程详情.
     * @param userId userId
     * @param courseId courseId
     */
    CourseDetailVo getCourseDetail(Long userId, Long courseId);

    /**
     * 保存学习记录.
     * @param userId userId
     * @param courseId userId
     */
    void saveStudy(Long userId, Long courseId);

    Boolean getNewCourse(Integer type,Long userId);

    void saveNewCourse(Integer type, Long userId);
}
