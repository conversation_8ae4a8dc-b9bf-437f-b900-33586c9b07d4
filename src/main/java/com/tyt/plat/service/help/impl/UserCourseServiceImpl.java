package com.tyt.plat.service.help.impl;

import com.tyt.base.bean.BaseParameter;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.ConvertUtil;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.GlobalStatusEnum;
import com.tyt.plat.enums.StudyStatusEnum;
import com.tyt.plat.enums.UserRecordEnum;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.base.TytUserRecordService;
import com.tyt.plat.service.help.UserCourseService;
import com.tyt.plat.utils.DateUtil;
import com.tyt.plat.vo.help.CourseDetailVo;
import com.tyt.plat.vo.help.UserCourseCountVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * 用户课程.
 *
 * <AUTHOR>
 * @date 2023-11-21 13:37:40
 */
@Slf4j
@Service
public class UserCourseServiceImpl implements UserCourseService {

    @Autowired
    private TytUserRecordService tytUserRecordService;

    @Autowired
    private TytCourseInfoMapper tytCourseInfoMapper;

    @Autowired
    private TytCourseBannerMapper tytCourseBannerMapper;

    @Autowired
    private TytCourseUserMapper tytCourseUserMapper;

    @Autowired
    private TytCourseCategoryMapper tytCourseCategoryMapper;

    @Autowired
    private TytCourseContentMapper tytCourseContentMapper;

    private static final String COURSE_MAXID_CAR = "course:maxid:car";

    private static final String COURSE_MAXID_GOOD = "course:maxid:good";




    @Override
    public List<TytCourseInfo> getCourseList(Integer type, Long categoryId, Integer sortNumber) {

        List<TytCourseInfo> hotList = tytCourseInfoMapper.getCourseList(type, categoryId, sortNumber);

        return hotList;
    }

    @Override
    public List<TytCourseBanner> getBannerList(Integer type) {

        List<TytCourseBanner> bannerList = tytCourseBannerMapper.getBannerList(type);

        return bannerList;
    }

    @Override
    public List<TytCourseCategory> getCategoryList(Integer type) {

        List<TytCourseCategory> categoryList = tytCourseCategoryMapper.getCategoryList(type);

        return categoryList;
    }

    @Override
    public UserCourseCountVo getNotLearnCount(Integer type, BaseParameter baseParameter) {

        Date nowTime = new Date();
        Long userId = baseParameter.getUserId();

        UserRecordEnum userRecordEnum = UserRecordEnum.courseNotlearnTip;

        String recordKey = userRecordEnum.getCode() + type;

        UserCourseCountVo courseCountVo = new UserCourseCountVo();

        TytUserRecord userRecord = tytUserRecordService.getByUserIdAndCode(userId, recordKey);

        int showTip = 0;

        if (userRecord == null) {
            showTip = 1;
        } else {
            String recordValue = userRecord.getValue();

            String todayStr = DateUtil.dateToString(nowTime, DateUtil.day_format_short);
            if (StringUtils.isBlank(recordValue) || !todayStr.equals(recordValue)) {
                showTip = 1;
            }
        }

        int notLearnCount = tytCourseUserMapper.getLearnInfoCount(userId, type, GlobalStatusEnum.no.getCode());
        courseCountVo.setNotLearnCount(notLearnCount);
        courseCountVo.setShowTip(showTip);
        return courseCountVo;
    }

    @Override
    public UserCourseCountVo getLearnInfo(Long userId, Integer type) {

        UserCourseCountVo courseCountVo = new UserCourseCountVo();

        int notLearnCount = tytCourseUserMapper.getLearnInfoCount(userId, type, GlobalStatusEnum.no.getCode());
        int hasLearnCount = tytCourseUserMapper.getLearnInfoCount(userId, type, GlobalStatusEnum.yes.getCode());

        courseCountVo.setNotLearnCount(notLearnCount);
        courseCountVo.setHasLearnCount(hasLearnCount);
        return courseCountVo;
    }

    @Override
    public List<TytCourseInfo> getMyCourseList(Long userId, Integer type, Integer learnFlag, Integer sortNumber) {

        List<TytCourseInfo> myCourseList = tytCourseInfoMapper.getMyCourseList(userId, type, learnFlag, sortNumber);

        return myCourseList;
    }

    @Override
    public void checkPreviewKey(Long courseId, String previewKey) {

        if(StringUtils.isBlank(previewKey)){
            throw TytException.createException(ResponseEnum.request_error.info("您无权查看！"));
        }

        String redisKey = CommonUtil.joinRedisKey(RedisKeyConstant.COURSE_PREVIEW_KEY, courseId + "");
        String cachePreviewKey = RedisUtil.getObject(redisKey);

        if(StringUtils.isBlank(cachePreviewKey)){
            throw TytException.createException(ResponseEnum.request_error.info("二维码已过期！"));
        }

        if(!previewKey.equals(cachePreviewKey)){
            throw TytException.createException(ResponseEnum.request_error.info("您无权查看！"));
        }

    }

    /**
     * 查询并校验课程是否有效.
     * @param courseId courseId
     * @return TytCourseInfo
     */
    private TytCourseInfo getEffectiveCourse(Long courseId, boolean enableCheck){

        TytCourseInfo courseInfo = tytCourseInfoMapper.selectByPrimaryKey(courseId);

        if (courseInfo == null) {
            throw TytException.createException(ResponseEnum.request_error.info("该课程不存在。"));
        }

        if(enableCheck) {
            if (!GlobalStatusEnum.yes.equalsCode(courseInfo.getEnable())) {
                throw TytException.createException(ResponseEnum.request_error.info("该课程已禁用!"));
            }
        }

        if (!GlobalStatusEnum.yes.equalsCode(courseInfo.getStatus())) {
            throw TytException.createException(ResponseEnum.request_error.info("该课程不存在."));
        }

        return courseInfo;
    }

    @Override
    public CourseDetailVo getCourseContent(Long courseId, boolean enableCheck){

        TytCourseInfo courseInfo = this.getEffectiveCourse(courseId, enableCheck);

        Example exa = new Example(TytCourseContent.class);
        exa.and().andEqualTo("courseId", courseId);

        //富文本内容
        TytCourseContent tytCourseContent = tytCourseContentMapper.selectOneByExample(exa);

        String contentText = "";
        if(tytCourseContent != null){
            contentText = tytCourseContent.getContentText();
        }
        CourseDetailVo courseDetailVo = ConvertUtil.beanConvert(courseInfo, new CourseDetailVo());
        courseDetailVo.setContentText(contentText);

        return courseDetailVo;
    }

    /**
     * 获取用户选课.
     * @param userId userId
     * @param courseId courseId
     * @return TytCourseUser
     */
    private TytCourseUser getUserCourse(Long userId, Long courseId){

        //学习状态

        Example exa = new Example(TytCourseUser.class);
        exa.and().andEqualTo("courseId", courseId)
                .andEqualTo("userId", userId);

        TytCourseUser tytCourseUser = tytCourseUserMapper.selectOneByExample(exa);

        return tytCourseUser;
    }

    @Override
    public CourseDetailVo getCourseDetail(Long userId, Long courseId) {

        CourseDetailVo courseDetailVo = this.getCourseContent(courseId, true);

        TytCourseUser tytCourseUser = this.getUserCourse(userId, courseId);

        courseDetailVo.setCourseUser(tytCourseUser);

        return courseDetailVo;
    }

    @Override
    public void saveStudy(Long userId, Long courseId) {

        TytCourseInfo courseInfo = this.getEffectiveCourse(courseId, true);

        if (!GlobalStatusEnum.yes.equalsCode(courseInfo.getEnable())) {
            throw TytException.createException(ResponseEnum.request_error.info("该课程已禁用!"));
        }

        TytCourseUser tytCourseUser = this.getUserCourse(userId, courseId);

        if(tytCourseUser == null){
            throw TytException.createException(ResponseEnum.request_error.info("当前课程无需学习。"));
        }
        Date nowTime = new Date();

        tytCourseUser.setStudyTime(nowTime);
        tytCourseUser.setStudyStatus(StudyStatusEnum.HAS_LEARN.getCode());

        tytCourseUserMapper.updateByPrimaryKeySelective(tytCourseUser);

    }

    @Override
    public Boolean getNewCourse(Integer type,Long userId){
        String key = getMaxId( type);
        //查询缓存里最大的课程id
        //Long maxId = RedisUtil.getObject(key);
        String id = RedisUtil.get(key);
        log.info("缓存最大的id【{}】",id);
        if(StringUtils.isEmpty(id)){
            return false;
        }
        Long maxId = Long.valueOf(id);
        TytUserRecord byUserIdAndCode = tytUserRecordService.getByUserIdAndCode(userId, key);
        //缓存不为空而且用户没有记录或者有记录的最大id小于缓存里 的id
        if(null == byUserIdAndCode || (null != byUserIdAndCode && maxId > Long.valueOf(byUserIdAndCode.getValue()))){
            return true;
        }
        return false;
    }

    @Override
    public void saveNewCourse(Integer type, Long userId){
        String key = getMaxId(type);
        //查询缓存里最大的课程id
        String id = RedisUtil.get(key);
        if(StringUtils.isNotEmpty(id)){
            tytUserRecordService.saveRecord(userId,key,1,id);
        }
    }


    public String getMaxId(Integer type){
        String key = "";
        //根据车/货取key值

        if(1 == type){
            key = COURSE_MAXID_CAR;
        } else if (2 == type) {
            key = COURSE_MAXID_GOOD;
        }
        log.info("获取版本类型【{}】，key值【{}】",type,key);
        return key;
    }
}
