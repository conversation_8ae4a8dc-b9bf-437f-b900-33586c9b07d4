package com.tyt.plat.service.dic;


import com.tyt.plat.entity.base.TytTransportDictionary;

import java.util.List;

/**
 * 调用运费价格相关
 */
public interface TransportDictionaryService {

    boolean checkDictionaryChange(long timestamp);

    List<TytTransportDictionary> getDictionaryList();

    void refreshDictionary();

    /**
     * 校验是否匹配分词
     *
     * @param content
     */
    List<String> verifyDictionaryMatches(String content);
}
