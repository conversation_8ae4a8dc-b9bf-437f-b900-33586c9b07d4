package com.tyt.plat.service.dic.impl;

import cn.hutool.core.collection.CollUtil;
import com.tfc.analysis.entity.Keyword;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.plat.commons.properties.SystemProperty;
import com.tyt.plat.entity.base.TytTransportDictionary;
import com.tyt.plat.mapper.base.TytTransportDictionaryMapper;
import com.tyt.plat.service.dic.TransportDictionaryService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TransportNullifyService;
import com.tyt.util.GoodsUnique;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 9:53
 */
@Slf4j
@Service
public class TransportDictionaryServiceImpl implements TransportDictionaryService {

    private static String SEARCH_DICTIONARY_KEY = "search_dictionary_key";

    private static int  SEARCH_DICTIONARY_TIME =10*60;

    @Autowired
    private SystemProperty systemProperty;

    @Autowired
    private TytTransportDictionaryMapper transportDictionaryMapper;

    @Autowired
    private TransportNullifyService transportNullifyService;


    @Override
    public boolean checkDictionaryChange(long timestamp) {

        String modifyTime = null;
        if (timestamp > 0) {
            Date date = new Date(timestamp);
            modifyTime = DateUtil.dateToString(date, DateUtil.date_time_format);
        }

        Long dicId = transportDictionaryMapper.checkDictionaryChange(modifyTime);
        return dicId != null;
    }

    @Override
    public List<TytTransportDictionary> getDictionaryList() {
        return transportDictionaryMapper.selectAll();
    }

    @Override
    public void refreshDictionary() {
        int pageSize = 200;
        long maxId = 0L;

        String tmpFolder = systemProperty.getTmpFolder();

        log.info("refreshDictionary_tmpFolder ： " + tmpFolder);

        String dicPath = CommonUtil.joinPath(tmpFolder, "transport_dictionary.dic");

        CommonUtil.createFileFolder(dicPath);

        OutputStreamWriter outputWriter = null;
        BufferedWriter bufferedWriter = null;

        try {
            outputWriter = new OutputStreamWriter(new FileOutputStream(dicPath, false), "UTF-8");
            bufferedWriter = new BufferedWriter(outputWriter);

            while (true) {

                List<TytTransportDictionary> pageDictionaryList = transportDictionaryMapper.getPageDictionaryList(maxId, pageSize);

                if (CollectionUtils.isEmpty(pageDictionaryList)) {
                    break;
                }

                for (TytTransportDictionary oneDic : pageDictionaryList) {

                    Long dicId = oneDic.getId();

                    String keyword = oneDic.getKeyword();
                    Integer rate = oneDic.getRate();
                    String correctWord = oneDic.getCorrectWord();
                    if (correctWord == null) {
                        correctWord = "";
                    }

                    String dicLine = keyword + "\t" + rate + "\t" + correctWord;
                    dicLine = dicLine.trim();
                    System.out.println("dicLine : " + dicLine);

                    bufferedWriter.write(dicLine);
                    bufferedWriter.newLine();

                    maxId = dicId;

                }
            }

            bufferedWriter.flush();

        } catch (IOException e) {
            log.error("refreshDictionary_error : ", e);
        } finally {
            if (bufferedWriter != null) {
                try {
                    bufferedWriter.close();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
            if (outputWriter != null) {
                try {
                    outputWriter.close();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }

        GoodsUnique.resetDictionary();
        GoodsUnique.loadMyDict(dicPath);

        log.info("refreshDictionary_done ....... ");
    }


    @Override
    public List<String> verifyDictionaryMatches(String content) {
        List<Keyword> keywordList = fetchDictionaryKeywords();
        return transportNullifyService.getKeywordByTaskContentAndRemark(content, keywordList);
    }

    private List<Keyword> fetchDictionaryKeywords() {
        List<String> dictionaryWordsList = RedisUtil.getObjectList(SEARCH_DICTIONARY_KEY);
        if (CollUtil.isEmpty(dictionaryWordsList)) {
            //重新加载
            List<TytTransportDictionary> dictionaryList = this.getDictionaryList();
            dictionaryWordsList = dictionaryList.stream().map(TytTransportDictionary::getKeyword).collect(Collectors.toList());
            RedisUtil.setObjectList(SEARCH_DICTIONARY_KEY, dictionaryWordsList, SEARCH_DICTIONARY_TIME);
        }
        List<Keyword> dictionaryKeywordsList = new ArrayList<Keyword>();
        dictionaryWordsList.forEach(d -> {
            dictionaryKeywordsList.add(new Keyword(d));
        });
        return dictionaryKeywordsList;
    }
}
