package com.tyt.plat.service.dw.impl;

import com.tyt.plat.entity.base.DwMarketV4AbmRelation;
import com.tyt.plat.entity.base.DwMarketV4AgentInfo;
import com.tyt.plat.entity.base.DwMarketV4BrandInfo;
import com.tyt.plat.mapper.base.DwMarketV4AbmRelationMapper;
import com.tyt.plat.mapper.base.DwMarketV4AgentInfoMapper;
import com.tyt.plat.mapper.base.DwMarketV4BrandInfoMapper;
import com.tyt.plat.service.api.BaseApiService;
import com.tyt.plat.service.dw.DwMarketV4AgentInfoService;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/04/18 11:02
 */
@Slf4j
@Service
public class DwMarketV4AgentInfoServiceImpl implements DwMarketV4AgentInfoService {

    @Autowired
    private DwMarketV4AgentInfoMapper dwMarketV4AgentInfoMapper;

    @Autowired
    private DwMarketV4BrandInfoMapper dwMarketV4BrandInfoMapper;

    @Autowired
    private DwMarketV4AbmRelationMapper dwMarketV4AbmRelationMapper;

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager", rollbackFor = Exception.class)
    public void saveInfo(String contact, String phone, String brandName){
        DwMarketV4AgentInfo info = dwMarketV4AgentInfoMapper.getByPhone(phone);
        if(null != info){
            throw TytException.createException(new ResponseCode(500, "已提交过该手机号，不能重复提交"));
        }
        //经销商
        DwMarketV4AgentInfo agent = DwMarketV4AgentInfo.builder()
                .source(3)
                .contact(contact)
                .cellPhone(phone)
                .ctime(new Date())
                .status(0)
                .cooperateStatus(0)
                .treatStatus(0).build();
        dwMarketV4AgentInfoMapper.insertSelective(agent);
        //品牌
        DwMarketV4BrandInfo brand = DwMarketV4BrandInfo.builder()
                .name(brandName)
                .ctime(new Date())
                .handleStatus(0).build();
        dwMarketV4BrandInfoMapper.insertSelective(brand);
        //关联
        DwMarketV4AbmRelation relation = DwMarketV4AbmRelation.builder()
                .aId(agent.getId())
                .bId(brand.getId())
                .mId(0).build();
        dwMarketV4AbmRelationMapper.insertSelective(relation);
    }
}
