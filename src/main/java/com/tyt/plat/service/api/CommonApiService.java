package com.tyt.plat.service.api;


import com.tyt.plat.vo.axb.*;
import com.tyt.plat.vo.esign.*;
import com.tyt.plat.vo.face.*;
import com.tyt.plat.vo.ocr.*;
import com.tyt.plat.vo.user.IdCardTwoElementVerifyDTO;
import com.tyt.plat.vo.user.IdCardTwoElementVerifyVO;

import java.util.List;

/**
 * CommonApi 调用
 */
public interface CommonApiService extends BaseApiService {

    /**
     * 提取货物标准词
     *
     * @param content
     * @return
     */
    List<String> splitContent(String content);

    /**
     * OCR证件识别--营业执照
     */
    OcrBusinessLicenseVo doOcrByLicenseUrl(String licenseUrl);

    /**
     * 身份证正面ocr
     *
     * @param url url
     * @return result
     */
    OcrIdCardFrontVo idCardFrontOcr(String url);

    /**
     * 身份证反面ocr
     *
     * @param url url
     * @return result
     */
    OcrIdCardBackVo idCardBackOcr(String url);

    /**
     * 驾驶证正面ocr
     *
     * @param url url
     * @return result
     */
    DriverLicenseFrontVo driverLicenseFrontOcr(String url);

    /**
     * 驾驶证反面ocr
     *
     * @param url url
     * @return result
     */
    DriverLicenseBackVo driverLicenseBackOcr(String url);

    /**
     * 道路运输证ocr
     *
     * @param url url
     * @return result
     */
    RoadTransportVo roadTransportOcr(String url);

    AxbBindVO axbBind(AxbBindReq req);

    Object axbUpdate(AxbUpdateReq req);

    List<AxbInfoVO> getAxbInfo(Integer bizType, Long bizId, String telA, String telB);

    Object axbDelete(AxbDeleteReq req);

    /**
     * 调用三方接口 根据关键词天眼查获取企业信息
     * @param keyword 关键词
     * @return IcBasicInfoNormalResp
     */
    IcBasicInfoNormalResp baseInfoNormal(String keyword);

    /**
     * 二要素验证.
     * @param req req
     * @return IdCardTwoElementVerifyVO
     */
    IdCardTwoElementVerifyVO twoElementVerify(IdCardTwoElementVerifyDTO req);

    /**
     * 获取人脸识别token.
     * @param req req
     * @return FaceTokenResp
     */
    FaceV5TokenResp getBizTokenV5(FaceV5TokenReq req);

    /**
     * 人脸比对.
     * @param req req
     * @return FaceVerifyResp
     */
    FaceV5VerifyResp faceVerifyV5(FaceV5VerifyReq req);

    /**
     * 获取人脸识别token.
     * @param req req
     * @return FaceTokenResp
     */
    FaceV3TokenResp getBizTokenV3(FaceV3TokenReq req);

    /**
     * 人脸比对.
     * @param req req
     * @return FaceVerifyResp
     */
    FaceV3VerifyResp faceVerifyV3(FaceV3VerifyReq req);

    /**
     * 行驶证主页ocr
     *
     * @param url url
     * @return result
     */
    VehicleLicenseFrontVo vehicleLicenseMainOcr(String url);

    /**
     * 行驶证副页ocr
     *
     * @param url url
     * @return result
     */
    VehicleLicenseBackVo vehicleLicenseBackOcr(String url);

    /** ==================== e签宝 ==================== **/

    /**
     * 本地PDF模板文件流填充.
     * @param req req
     * @return Call
     */
    CreatePdfVO createPdf(CreatePdfReq req);

    /**
     * 创建企业签署账户
     */
    AddAccountVO addEsignAcount(AddAccountReq req);

    /**
     * 创建企业模板印章
     */
    TemplateSealVO addTemplateSeal(TemplateSealReq req);

    /**
     * 【手机号认证】运营商3要素核身
     */
    Telecom3FactorsVO telecom3Factors(Telecom3FactorsDTO req);

    /**
     * 【手机号认证】短信验证码校验
     */
    Object telecom3FactorsVerify(Telecom3FactorsVerifyDTO req);

    /** ========== 签署意愿确认 ========== **/

    /**
     * 指定手机发送签署短信验证码
     *
     */
    Object sendSignMobileCode3rd(SendSignMobileCode3rdReq req);

    /**
     * 平台用户PDF文件流签署（指定手机号短信验证）
     *
     */
    LocalSafeSignPDF3rdVO localSafeSignPDF3rd(LocalSafeSignPDF3rdReq req);

    /**
     * 平台自身PDF文件流签署（印章标识）
     *
     */
    LocalSignPdfVO sealIdSign(LocalSignPdfReq req);

    /**
     * PDF文档验签（文件流）
     *
     */
    LocalVerifyPdfVO localPdfVerify(LocalVerifyPdfReq req);

    /** ========== 授权书签署 ========== **/

    /**
     * 【4要素】企业核身认证
     */
    EnterpriseFourFactorsVO enterpriseFourFactors(EnterpriseFourFactorsReq req);

    /**
     * 【3要素】企业核身认证
     */
    EnterpriseThreeFactorsVO enterpriseThreeFactors(EnterpriseThreeFactorsReq req);

    /**
     * 【授权书认证】发起授权书签署
     */
    Object legalRepSign(LegalRepSignReq req);


    /**
     * 【授权书认证】获取签署链接
     */
    SignUrlResp getAuthSignUrl(String flowId);


    /**
     * 【授权书认证】查询签署状态
     */
    LegalRepSignResultResp legalRepSignResult(String flowId);

    /**
     * 自动外呼
     * @param autoCallTaskRequest
     */
    void autoCallTask(AutoCallTaskRequest autoCallTaskRequest);

}
