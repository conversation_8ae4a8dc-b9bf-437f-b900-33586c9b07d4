package com.tyt.plat.service.api;

import com.tyt.plat.vo.axb.*;
import com.tyt.plat.vo.esign.*;
import com.tyt.plat.vo.face.*;
import com.tyt.plat.vo.ocr.*;
import com.tyt.plat.vo.user.IdCardTwoElementVerifyDTO;
import com.tyt.plat.vo.user.IdCardTwoElementVerifyVO;
import lombok.Data;
import org.springframework.validation.annotation.Validated;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/28 上午11:06
 */
public interface CommonApiRemote {

    @Data
    class CommonApiResponse<T> {
        private int code;
        private String msg;
        private T data;
        private Date timestamp;

        public boolean isSuccess() {
            return code == 200;
        }
    }

    /**
     * 营业执照
     */
    @POST("cardOcr/businessLicenseOcr")
    Call<CommonApiResponse<OcrBusinessLicenseVo>> businessLicenseOcr(@Query("url") String url);

    /**
     * 身份证正面
     */
    @POST("cardOcr/idCardFrontOcr")
    Call<CommonApiResponse<OcrIdCardFrontVo>> idCardFrontOcr(@Query("url") String url);

    /**
     * 身份证反面
     */
    @POST("cardOcr/idCardBackOcr")
    Call<CommonApiResponse<OcrIdCardBackVo>> idCardBackOcr(@Query("url") String url);

    /**
     * 驾驶证正面
     */
    @POST("cardOcr/driverLicenseFrontOcr")
    Call<CommonApiResponse<DriverLicenseFrontVo>> driverLicenseFrontOcr(@Query("url") String url);

    /**
     * 驾驶证反面
     */
    @POST("cardOcr/driverLicenseBackOcr")
    Call<CommonApiResponse<DriverLicenseBackVo>> driverLicenseBackOcr(@Query("url") String url);

    /**
     * 道路运输证
     */
    @POST("cardOcr/roadTransportOcr")
    Call<CommonApiResponse<RoadTransportVo>> roadTransportOcr(@Query("url") String url);


    @POST("axb/bind")
    public Call<CommonApiResponse<AxbBindVO>> axbBind(@Body AxbBindReq req);

    @POST("axb/update")
    public Call<CommonApiResponse<Object>> axbUpdate(@Body AxbUpdateReq req);

    @GET("axb/info")
    public Call<CommonApiResponse<List<AxbInfoVO>>> getAxbInfo(@Query("bizType") Integer bizType, @Query("bizId") Long bizId, @Query("telA") String telA, @Query("telB") String telB);

    @POST("axb/delete")
    public Call<CommonApiResponse<Object>> axbDelete(@Body AxbDeleteReq req);

    /**
     * 根据关键词获取企业基础信息
     * @param keyword 关键词
     * @return
     */
    @GET("tian_yan_cha/ic/baseinfo/normal")
    public Call<CommonApiResponse<IcBasicInfoNormalResp>> baseInfoNormal(@Query("keyword") String keyword);

    /**
     * 火山引擎二要素.
     * @param req req
     * @return Call
     */
    @POST("volcengine/business_security/id_card/two_element_verify")
    Call<CommonApiResponse<IdCardTwoElementVerifyVO>> twoElementVerify(@Body IdCardTwoElementVerifyDTO req);

    /**
     * 人脸token.
     * @param req req
     * @return Call
     */
    @POST("megaii/face_id/get_biz_token")
    Call<CommonApiResponse<FaceV5TokenResp>> getBizTokenV5(@Body FaceV5TokenReq req);

    /**
     * 人脸比对.
     * @param req req
     * @return Call
     */
    @POST("megaii/face_id/verify")
    Call<CommonApiResponse<FaceV5VerifyResp>> faceVerifyV5(@Body FaceV5VerifyReq req);

    /**
     * 人脸token.
     * @param req req
     * @return Call
     */
    @POST("megaii/face_id/v3/get_biz_token")
    Call<CommonApiResponse<FaceV3TokenResp>> getBizTokenV3(@Body FaceV3TokenReq req);

    /**
     * 人脸比对.
     * @param req req
     * @return Call
     */
    @POST("megaii/face_id/v3/verify")
    Call<CommonApiResponse<FaceV3VerifyResp>> faceVerifyV3(@Body FaceV3VerifyReq req);

    /**
     * 行驶证主页
     */
    @POST("cardOcr/vehicleLicenseMainOcr")
    Call<CommonApiResponse<VehicleLicenseFrontVo>> vehicleLicenseMainOcr(@Query("url") String url);

    /**
     * 行驶证副页
     */
    @POST("cardOcr/vehicleLicenseBackOcr")
    Call<CommonApiResponse<VehicleLicenseBackVo>> vehicleLicenseBackOcr(@Query("url") String url);

    /** ==================== e签宝 ==================== **/

    /**
     * 本地PDF模板文件流填充.
     * @param req req
     * @return Call
     */
    @POST("esign/sign/pdf/create")
    Call<CommonApiResponse<CreatePdfVO>> createPdf(@Body CreatePdfReq req);

    /**
     * 创建企业签署账户
     */
    @POST("esign/sign/account/add")
    Call<CommonApiResponse<AddAccountVO>> addEsignAcount(@Body AddAccountReq req);

    /**
     * 创建企业模板印章
     */
    @POST("esign/sign/template/seal/add")
    Call<CommonApiResponse<TemplateSealVO>> addTemplateSeal(@Body TemplateSealReq req);

    /**
     * 【手机号认证】运营商3要素核身
     */
    @POST("esign/identity/auth/individual/telecom3Factors")
    Call<CommonApiResponse<Telecom3FactorsVO>> telecom3Factors(@Body Telecom3FactorsDTO req);

    /**
     * 【手机号认证】短信验证码校验
     */
    @POST("esign/identity/auth/individual/telecom3Factors/verify")
    Call<CommonApiResponse<Object>> telecom3FactorsVerify(@Body Telecom3FactorsVerifyDTO req);


    /** ========== 签署意愿确认 ========== **/

    /**
     * 指定手机发送签署短信验证码
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/uv05t5">官方文档链接</a>
     */
    @POST("esign/sign/mobileCode/send")
    Call<CommonApiResponse<Object>> sendSignMobileCode3rd(@Body @Validated SendSignMobileCode3rdReq req);

    /**
     * 平台用户PDF文件流签署（指定手机号短信验证）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/gg9yea">官方文档链接</a>
     */
    @POST("esign/sign/localPdf/saveSign3rd")
    Call<CommonApiResponse<LocalSafeSignPDF3rdVO>> localSafeSignPDF3rd(@Body @Validated LocalSafeSignPDF3rdReq req);

    /**
     * 平台自身PDF文件流签署（印章图片）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/zk13ay">官方文档链接</a>
     */
    @POST("esign/sign/localPdf/sealIdSign")
    Call<CommonApiResponse<LocalSignPdfVO>> sealIdSign(@Body @Validated LocalSignPdfReq req);

    /**
     * PDF文档验签（文件流）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/ruk0br">官方文档链接</a>
     */
    @POST("esign/sign/localPdf/verify")
    Call<CommonApiResponse<LocalVerifyPdfVO>> localPdfVerify(@Body @Validated LocalVerifyPdfReq req);

    /** ========== 授权书签署 ========== **/

    /**
     * 【4要素】企业核身认证
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/ikp0qg">官方文档链接</a>
     */
    @POST("esign/identity/auth/organization/enterprise/fourFactors")
    Call<CommonApiResponse<EnterpriseFourFactorsVO>> enterpriseFourFactors(@Body EnterpriseFourFactorsReq req);

    /**
     * 【3要素】企业核身认证
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/dzvhtp">官方文档链接</a>
     */
    @POST("esign/identity/auth/organization/enterpriseThreeFactors")
    Call<CommonApiResponse<EnterpriseThreeFactorsVO>> enterpriseThreeFactors(@Body EnterpriseThreeFactorsReq req);

    /**
     * 【授权书认证】发起授权书签署
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/dyyohd">官方文档链接</a>
     */
    @POST("esign/identity/auth/organization/legalRepSign")
    Call<CommonApiResponse<Object>> legalRepSign(@Body LegalRepSignReq req);

    /**
     * 【授权书认证】获取签署链接
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/cgiwtx">官方文档链接</a>
     */
    @GET("esign/identity/auth/organization/signUrl")
    Call<CommonApiResponse<SignUrlResp>> getAuthSignUrl(@Query("flowId")  String flowId);

    /**
     * 【授权书认证】查询签署状态
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/ih16c4">官方文档链接</a>
     */
    @GET("esign/identity/auth/organization/legalRepSignResult")
    Call<CommonApiResponse<LegalRepSignResultResp>> legalRepSignResult(@Query("flowId") String flowId);

    /**
     * 自动外呼
     */
    @POST("cticloud/task/autoCallTask")
    Call<CommonApiResponse<Object>> autoCallTask(@Body AutoCallTaskRequest autoCallTaskRequest);


}
