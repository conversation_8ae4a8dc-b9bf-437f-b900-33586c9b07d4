package com.tyt.plat.service.api.impl;

import com.tyt.plat.constant.TytHostConstant;
import com.tyt.plat.service.api.BaseApiService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 9:53
 */
@Slf4j
public class BaseApiServiceImpl implements BaseApiService {

    @Autowired
    private TytConfigService tytConfigService;

    /**
     * 获取内网host
     * @return String
     */
    @Override
    public String getPrivatePlatHost(){

        String privatePlatHost = tytConfigService.getStringValue(TytHostConstant.PRIVATE_PLAT_HOST);

        if(StringUtils.isBlank(privatePlatHost)){
            throw TytException.createException(ResponseEnum.sys_error.info("privatePlatHost is blank!"));
        }

        return privatePlatHost;
    }

    /**
     * 获取公网host
     * @return String
     */
    @Override
    public String getPublicPlatHost(){

        String privatePlatHost = tytConfigService.getStringValue(TytHostConstant.PUBLIC_PLAT_HOST);

        if(StringUtils.isBlank(privatePlatHost)){
            throw TytException.createException(ResponseEnum.sys_error.info("privatePlatHost is blank!"));
        }

        return privatePlatHost;
    }
}
