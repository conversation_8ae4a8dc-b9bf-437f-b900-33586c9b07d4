package com.tyt.plat.service.api.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Maps;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.constant.RemoteApiConstant;
import com.tyt.plat.enums.PlatResponseEnum;
import com.tyt.plat.enums.esign.EsignResponseEnum;
import com.tyt.plat.service.api.CommonApiRemote;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.utils.http.HttpClientUtil;
import com.tyt.plat.vo.axb.*;
import com.tyt.plat.vo.esign.*;
import com.tyt.plat.vo.face.*;
import com.tyt.plat.vo.ocr.*;
import com.tyt.plat.vo.user.IdCardTwoElementVerifyDTO;
import com.tyt.plat.vo.user.IdCardTwoElementVerifyVO;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.security.SecureLinkUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;
import retrofit2.http.Body;

import java.net.URI;
import java.time.Duration;
import java.util.*;

/**
 * <AUTHOR>
 * common api 调用.
 * @date 2022/3/21 9:53
 */
@Slf4j
@Service
public class CommonApiServiceImpl extends BaseApiServiceImpl implements CommonApiService, InitializingBean {

    private final ObjectMapper mapper;

    private CommonApiRemote commonApiRemote;

    public CommonApiServiceImpl() {
        mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
    }

    @Override
    public List<String> splitContent(String content) {
        List<String> nameList = new ArrayList<>();

        if (StringUtils.isBlank(content)) {
            return nameList;
        }

        String platHost = super.getPrivatePlatHost();
        String apiUrl = CommonUtil.joinPath(platHost, RemoteApiConstant.CommonApi.split_content_word);

        long timestamp = System.currentTimeMillis();

        Map<String, String> paramMap = new TreeMap<>();
        paramMap.put("timestamp", timestamp + "");
        paramMap.put("content", content);

        String linkString = SecureLinkUtil.createLinkString(SecureLinkUtil.paraFilter(paramMap));
        String sign = SecureLinkUtil.sign(linkString, RemoteApiConstant.CommonApi.secret_key, "MD5");

        URI uri = HttpClientUtil.createUri(apiUrl, paramMap);

        HttpGet httpGet = new HttpGet(uri);
        httpGet.addHeader("sign", sign);

        try(CloseableHttpResponse httpResponse = HttpClientUtil.execute(httpGet)) {
            nameList = HttpClientUtil.getResponseData(httpResponse, new TypeReference<List<String>>() {
            });
        } catch (Exception e){
            throw TytException.createException(e);
        }

        return nameList;
    }

    @Override
    public OcrBusinessLicenseVo doOcrByLicenseUrl(String licenseUrl) {
        try {
            Response<CommonApiRemote.CommonApiResponse<OcrBusinessLicenseVo>> response =
                    commonApiRemote.businessLicenseOcr(licenseUrl).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 企业营业执照orc失败", e);
            return null;
        }
    }

    @Override
    public OcrIdCardFrontVo idCardFrontOcr(String url) {
        try {
            Response<CommonApiRemote.CommonApiResponse<OcrIdCardFrontVo>> response =
                    commonApiRemote.idCardFrontOcr(url).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 身份证正面orc失败", e);
            return null;
        }
    }

    @Override
    public OcrIdCardBackVo idCardBackOcr(String url) {
        try {
            Response<CommonApiRemote.CommonApiResponse<OcrIdCardBackVo>> response =
                    commonApiRemote.idCardBackOcr(url).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 身份证反面orc失败", e);
            return null;
        }
    }

    @Override
    public DriverLicenseFrontVo driverLicenseFrontOcr(String url) {
        try {
            Response<CommonApiRemote.CommonApiResponse<DriverLicenseFrontVo>> response =
                    commonApiRemote.driverLicenseFrontOcr(url).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 驾驶证正面orc失败", e);
            return null;
        }
    }

    @Override
    public DriverLicenseBackVo driverLicenseBackOcr(String url) {
        try {
            Response<CommonApiRemote.CommonApiResponse<DriverLicenseBackVo>> response =
                    commonApiRemote.driverLicenseBackOcr(url).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 驾驶证反面orc失败", e);
            return null;
        }
    }

    @Override
    public RoadTransportVo roadTransportOcr(String url) {
        try {
            Response<CommonApiRemote.CommonApiResponse<RoadTransportVo>> response =
                    commonApiRemote.roadTransportOcr(url).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 道路运输证orc失败", e);
            return null;
        }
    }

    private <T> T getData(Response<CommonApiRemote.CommonApiResponse<T>> response) {
        log.info("common-api orc response:{}", response);
        if (!response.isSuccessful()) {
            return null;
        }

        CommonApiRemote.CommonApiResponse<T> body = response.body();

        PlatCommonUtil.printFixLog("commonapi_getData", body);

        return body != null && body.isSuccess() ? body.getData() : null;
    }

    /**
     * 获取数据，如果有错误则抛出异常
     * @param response response
     * @param <T> response
     * @return T
     */
    private <T> T getDataDetail(Response<CommonApiRemote.CommonApiResponse<T>> response) {
        log.info("common-api orc response:{}", response);
        if (!response.isSuccessful()) {
            return null;
        }

        CommonApiRemote.CommonApiResponse<T> body = response.body();

        PlatCommonUtil.printFixLog("getDataDetail", body);

        if(body != null && body.isSuccess() ){
            return body.getData();
        }

        ResponseCode responseCode = ResponseEnum.request_error.info();
        if(body != null){
            int bodyCode = body.getCode();
            String errorMsg = body.getMsg();

            responseCode.setCode(bodyCode);

            if(StringUtils.isNotBlank(errorMsg)){
                responseCode.setMsg(errorMsg);
            }
        }
        throw TytException.createException(responseCode);

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // common-api 拦截器
        Interceptor commApiInterceptor = chain -> {
            Request oldRequest = chain.request();

            HttpUrl httpUrl = oldRequest.url()
                    .newBuilder()
                    .addQueryParameter("timestamp", String.valueOf(System.currentTimeMillis()))
                    // 拦截器动态修改host
                    //.host("dev.teyuntong.net").build();
                    .host(StringUtils.replacePattern(getPrivatePlatHost(), "(http|https)://(.*)", "$2")).build();

            // -----------------  验签 ---------------------
            HashMap<String, String> paramMap = Maps.newHashMap();
            for (String parameterName : httpUrl.queryParameterNames()) {
                paramMap.put(parameterName, httpUrl.queryParameter(parameterName));
            }

            String linkString = SecureLinkUtil.createLinkString(SecureLinkUtil.paraFilter(paramMap));

            final Request copy = oldRequest.newBuilder().build();
            if (copy.body() != null) {
                final Buffer buffer = new Buffer();
                copy.body().writeTo(buffer);

                String body = buffer.readUtf8();

                if (StringUtils.isNotBlank(body)) {

                    linkString += body;

                }
            }

            String sign = SecureLinkUtil.sign(linkString, RemoteApiConstant.CommonApi.secret_key, "MD5");

            // sign header
            Headers headers = oldRequest.headers().newBuilder()
                    .add("sign", sign)
                    .add("tyt-sign-type", "stream")
                    .build();
            // -----------------  验签结束 -------------------

            // 构建新请求
            Request request = oldRequest.newBuilder().url(httpUrl).headers(headers).build();
            return chain.proceed(request);
        };

        OkHttpClient httpClient = new OkHttpClient.Builder()
                .readTimeout(Duration.ofSeconds(15))
                .connectTimeout(Duration.ofSeconds(5))
                .addInterceptor(commApiInterceptor)
                .build();

        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(getPrivatePlatHost() + "/common-api/")
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .client(httpClient).build();

        commonApiRemote = retrofit.create(CommonApiRemote.class);
    }

    @Override
    public AxbBindVO axbBind(AxbBindReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<AxbBindVO>> response = commonApiRemote.axbBind(req).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 绑定隐私号失败", e);
            return null;
        }
    }

    @Override
    public Object axbUpdate(AxbUpdateReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<Object>> response = commonApiRemote.axbUpdate(req).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 更新隐私号失败", e);
            return null;
        }
    }

    @Override
    public List<AxbInfoVO> getAxbInfo(Integer bizType, Long bizId, String telA, String telB) {
        try {
            Response<CommonApiRemote.CommonApiResponse<List<AxbInfoVO>>> response =
                    commonApiRemote.getAxbInfo(bizType, bizId, telA, telB).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 获取隐私号信息集合失败", e);
            return null;
        }
    }

    @Override
    public Object axbDelete(AxbDeleteReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<Object>> response = commonApiRemote.axbDelete(req).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 解绑隐私号失败", e);
            return null;
        }
    }

    @Override
    public IcBasicInfoNormalResp baseInfoNormal(String keyword) {
        try {
            Response<CommonApiRemote.CommonApiResponse<IcBasicInfoNormalResp>> response = commonApiRemote.baseInfoNormal(keyword).execute();
            log.info("common-api 天眼查获取信息结果:{}", response);
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 天眼查获取信息失败", e);
            return null;
        }
    }

    @Override
    public IdCardTwoElementVerifyVO twoElementVerify(IdCardTwoElementVerifyDTO req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<IdCardTwoElementVerifyVO>> response =
                    commonApiRemote.twoElementVerify(req).execute();

            IdCardTwoElementVerifyVO data = getData(response);

            return data;
        } catch (Exception e) {
            log.error("twoElementVerify_error : ", e);
            return null;
        }
    }

    @Override
    public VehicleLicenseFrontVo vehicleLicenseMainOcr(String url) {
        try {
            Response<CommonApiRemote.CommonApiResponse<VehicleLicenseFrontVo>> response =
                    commonApiRemote.vehicleLicenseMainOcr(url).execute();
            log.info("common-api 行驶证主页orc结果:{}", response);
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 行驶证主页orc失败", e);
            return null;
        }
    }

    @Override
    public FaceV5TokenResp getBizTokenV5(FaceV5TokenReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<FaceV5TokenResp>> response =
                    commonApiRemote.getBizTokenV5(req).execute();

            FaceV5TokenResp data = getData(response);

            return data;
        } catch (Exception e) {
            log.error("twoElementVerify_error : ", e);
            return null;
        }
    }

    @Override
    public FaceV5VerifyResp faceVerifyV5(FaceV5VerifyReq req){
        try {
            Response<CommonApiRemote.CommonApiResponse<FaceV5VerifyResp>> response =
                    commonApiRemote.faceVerifyV5(req).execute();

            FaceV5VerifyResp data = getData(response);

            return data;
        } catch (Exception e) {
            log.error("twoElementVerify_error : ", e);
            return null;
        }
    }

    @Override
    public FaceV3TokenResp getBizTokenV3(FaceV3TokenReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<FaceV3TokenResp>> response =
                    commonApiRemote.getBizTokenV3(req).execute();

            FaceV3TokenResp data = getData(response);

            return data;
        } catch (Exception e) {
            log.error("twoElementVerify_error : ", e);
            return null;
        }
    }

    @Override
    public FaceV3VerifyResp faceVerifyV3(FaceV3VerifyReq req){
        try {

            Response<CommonApiRemote.CommonApiResponse<FaceV3VerifyResp>> response =
                    commonApiRemote.faceVerifyV3(req).execute();

            FaceV3VerifyResp data = getData(response);

            return data;
        } catch (Exception e) {
            log.error("faceVerifyV3_error : ", e);
            return null;
        }
    }

    @Override
    public VehicleLicenseBackVo vehicleLicenseBackOcr(String url) {
        try {
            Response<CommonApiRemote.CommonApiResponse<VehicleLicenseBackVo>> response =
                    commonApiRemote.vehicleLicenseBackOcr(url).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 行驶证副页orc失败", e);
            return null;
        }
    }

    /** ==================== e签宝 ==================== **/

    private TytException createEsignException(Exception e){
        TytException te = TytException.createException(e);

        int errorCode = te.getErrorCode();
        String errorMsg = te.getErrorMsg();

        if(PlatResponseEnum.ESIGN_API_ERROR.equalsCode(errorCode)){
            ResponseCode responseCode = JSON.parseObject(errorMsg, ResponseCode.class);

            if(responseCode != null){
                Integer apiCode = responseCode.getCode();
                String apiMessage = responseCode.getMsg();

                EsignResponseEnum responseEnum = EsignResponseEnum.getResponseEnum(apiCode);

                if(responseEnum != null){
                    apiMessage = responseEnum.getMsg();
                }
                if(StringUtils.isNotBlank(apiMessage)){
                    te.setErrorMsg(apiMessage);
                }
            }
        }
        return te;
    }

    /**
     * 本地PDF模板文件流填充.
     * @param req req
     * @return Call
     */
    @Override
    public CreatePdfVO createPdf(CreatePdfReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<CreatePdfVO>> response =
                    commonApiRemote.createPdf(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("commonapi_createPdf_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    /**
     * 创建企业签署账户
     */
    @Override
    public AddAccountVO addEsignAcount(AddAccountReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<AddAccountVO>> response =
                    commonApiRemote.addEsignAcount(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("commonapi_addEsignAcount_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    /**
     * 创建企业模板印章
     */
    @Override
    public TemplateSealVO addTemplateSeal(TemplateSealReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<TemplateSealVO>> response =
                    commonApiRemote.addTemplateSeal(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("commonapi_addTemplateSeal_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    /**
     * 【手机号认证】运营商3要素核身
     */
    @Override
    public Telecom3FactorsVO telecom3Factors(Telecom3FactorsDTO req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<Telecom3FactorsVO>> response =
                    commonApiRemote.telecom3Factors(req).execute();
            return this.getDataDetail(response);
        } catch (Exception e) {
            log.error("commonapi_telecom3Factors_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    /**
     * 【手机号认证】短信验证码校验
     */
    @Override
    public Object telecom3FactorsVerify(Telecom3FactorsVerifyDTO req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<Object>> response =
                    commonApiRemote.telecom3FactorsVerify(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("telecom3FactorsVerify_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    /** ========== 签署意愿确认 ========== **/

    @Override
    public Object sendSignMobileCode3rd(SendSignMobileCode3rdReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<Object>> response =
                    commonApiRemote.sendSignMobileCode3rd(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("sendSignMobileCode3rd_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    @Override
    public LocalSafeSignPDF3rdVO localSafeSignPDF3rd(LocalSafeSignPDF3rdReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<LocalSafeSignPDF3rdVO>> response =
                    commonApiRemote.localSafeSignPDF3rd(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("localSafeSignPDF3rd_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    @Override
    public LocalSignPdfVO sealIdSign(LocalSignPdfReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<LocalSignPdfVO>> response =
                    commonApiRemote.sealIdSign(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("localSignPdf_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    @Override
    public LocalVerifyPdfVO localPdfVerify(LocalVerifyPdfReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<LocalVerifyPdfVO>> response =
                    commonApiRemote.localPdfVerify(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("localPdfVerify_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    /** ========== 授权书签署 ========== **/

    @Override
    public EnterpriseFourFactorsVO enterpriseFourFactors(EnterpriseFourFactorsReq req){
        try {
            Response<CommonApiRemote.CommonApiResponse<EnterpriseFourFactorsVO>> response =
                    commonApiRemote.enterpriseFourFactors(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("enterpriseFourFactors_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    /**
     * 【3要素】企业核身认证
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/dzvhtp">官方文档链接</a>
     */
    @Override
    public EnterpriseThreeFactorsVO enterpriseThreeFactors(@Body EnterpriseThreeFactorsReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<EnterpriseThreeFactorsVO>> response =
                    commonApiRemote.enterpriseThreeFactors(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("enterpriseThreeFactors_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }

    }

    @Override
    public Object legalRepSign(LegalRepSignReq req){
        try {
            Response<CommonApiRemote.CommonApiResponse<Object>> response =
                    commonApiRemote.legalRepSign(req).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("legalRepSign_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    @Override
    public SignUrlResp getAuthSignUrl(String flowId){
        try {
            Response<CommonApiRemote.CommonApiResponse<SignUrlResp>> response =
                    commonApiRemote.getAuthSignUrl(flowId).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("getAuthSignUrl_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    @Override
    public LegalRepSignResultResp legalRepSignResult(String flowId){
        try {
            Response<CommonApiRemote.CommonApiResponse<LegalRepSignResultResp>> response =
                    commonApiRemote.legalRepSignResult(flowId).execute();
            return getDataDetail(response);
        } catch (Exception e) {
            log.error("legalRepSignResult_error", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

    @Override
    public void autoCallTask(AutoCallTaskRequest autoCallTaskRequest) {
        try {
            log.info("autoCallTask 请求参数：{}", JSON.toJSONString(autoCallTaskRequest));
            Response<CommonApiRemote.CommonApiResponse<Object>> response = commonApiRemote.autoCallTask(autoCallTaskRequest).execute();
        } catch (Exception e) {
            log.error("autoCallTask", e);
            TytException te=  this.createEsignException(e);
            throw te;
        }
    }

}
