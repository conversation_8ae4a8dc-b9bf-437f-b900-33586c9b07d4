package com.tyt.plat.service.api;

import com.tyt.plat.constant.TytHostConstant;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 9:53
 */
public interface BaseApiService {

    /**
     * 获取内网host
     * @return String
     */
    String getPrivatePlatHost();

    /**
     * 获取公网host
     * @return String
     */
    String getPublicPlatHost();

}
