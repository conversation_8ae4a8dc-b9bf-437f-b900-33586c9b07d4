package com.tyt.plat.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.plat.constant.RemoteApiConstant;
import com.tyt.plat.service.remote.CarryPriceService;
import com.tyt.plat.vo.remote.CarryPriceReq;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.util.CircuitBreakerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 9:53
 */
@Slf4j
@Service
public class CarryPriceServiceImpl implements CarryPriceService {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public CarryPriceVo getCarryPrice(CarryPriceReq carryPriceReq) {

        //强行兼容出发地目的地省市错误问题
        if (carryPriceReq != null) {
            if (StringUtils.isNotBlank(carryPriceReq.getStartProvince())) {
                carryPriceReq.setStartProvince(carryPriceReq.getStartProvince().replace("省", "").replace("市", ""));
            }
            if (StringUtils.isNotBlank(carryPriceReq.getDestProvince())) {
                carryPriceReq.setDestProvince(carryPriceReq.getDestProvince().replace("省", "").replace("市", ""));
            }
            if (StringUtils.isNotBlank(carryPriceReq.getStartCity())) {
                carryPriceReq.setStartCity(carryPriceReq.getStartCity().replace("市市", "市"));
            }
            if (StringUtils.isNotBlank(carryPriceReq.getDestCity())) {
                carryPriceReq.setDestCity(carryPriceReq.getDestCity().replace("市市", "市"));
            }
        }

        CarryPriceVo priceResult = null;

        String nowTimeStr = DateUtil.dateToString(new Date(), DateUtil.date_time_format);

        carryPriceReq.setApiSign(RemoteApiConstant.api_sign);
        carryPriceReq.setApiTime(nowTimeStr);

        String apiUrl = CommonUtil.joinPath(RemoteApiConstant.idc_host, RemoteApiConstant.IdcApi.price_uri);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, String> multiMap = CommonUtil.objectToMultiMap(carryPriceReq);

        log.info("getCarryPrice_请求参数: {}", JSON.toJSONString(carryPriceReq));

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(multiMap, headers);
        ResponseEntity<JSONObject> response =
                CircuitBreakerUtil.executeWithCircuitBreaker(CircuitBreakerUtil.CircuitBreakerConfiguration.BI_SERVICE,
                () -> restTemplate.postForEntity(apiUrl, request, JSONObject.class), null
        );

        boolean reqSuccess = false;
        JSONObject respBody = response.getBody();

        log.info("getCarryPrice_response: {}", respBody);

        if (response.getStatusCode().is2xxSuccessful()) {
            Integer respCode = respBody.getInteger("code");

            if(respCode != null && respCode.equals(200)){
                reqSuccess = true;
                priceResult = respBody.getObject("data", CarryPriceVo.class);
            }
        }

        if(!reqSuccess){

            String respMsg = null;
            if(respBody != null){
                respMsg = respBody.getString("msg");
            }

            log.error("request_idc_price_error : code : [{}], body : {}", response.getStatusCodeValue(), respBody);
            throw new RuntimeException("请求计费接口失败！" + respMsg);
        }

        if (priceResult != null) {
            if (priceResult.getSuggestMinPrice().compareTo(new BigDecimal(0)) == 0) {
                priceResult.setSuggestMinPrice(null);
            }
            if (priceResult.getSuggestMaxPrice().compareTo(new BigDecimal(0)) == 0) {
                priceResult.setSuggestMaxPrice(null);
            }
            if (priceResult.getSuggestPrice() == 0) {
                priceResult.setSuggestPrice(null);
            }

            if (priceResult.getFixPriceMin() == 0) {
                priceResult.setFixPriceMin(null);
            }
            if (priceResult.getFixPriceMax() == 0) {
                priceResult.setFixPriceMax(null);
            }
            if (priceResult.getFixPriceFast() == 0) {
                priceResult.setFixPriceFast(null);
            }
        }

        return priceResult;
    }
}
