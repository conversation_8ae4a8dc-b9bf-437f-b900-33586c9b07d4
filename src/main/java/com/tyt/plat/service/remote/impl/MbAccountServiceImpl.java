package com.tyt.plat.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.config.util.AppConfig;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.commons.properties.UserCenterConfigProperty;
import com.tyt.plat.constant.RemoteApiConstant;
import com.tyt.plat.service.remote.MbAccountService;
import com.tyt.plat.vo.mb.QueryUserVerifyInfoReVo;
import com.tyt.plat.vo.mb.UserRealResponse;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.tpay.TpayUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 9:53
 */
@Slf4j
@Service
public class MbAccountServiceImpl implements MbAccountService {

    @Autowired
    private UserCenterConfigProperty userCenterConfigProperty;
    public static final String userWeb = AppConfig.getProperty("user.service.api.url");

    @Autowired
    private TytConfigService tytConfigService;
    @Override
    public UserRealResponse getMbUserRealInfo(Long userId) {

        //查询满帮开户状态
        QueryUserVerifyInfoReVo verifyReq = new QueryUserVerifyInfoReVo();
        verifyReq.setUserId(userId);
        verifyReq.setTimestamp(System.currentTimeMillis() + "");
        verifyReq.setMerchantId(userCenterConfigProperty.getTpayMerchantId());
        verifyReq.setVersion(userCenterConfigProperty.getTpayVersion());
        verifyReq.setNeedImage(true);
        Integer interfaceMoveSwitch = tytConfigService.getIntValue("interface_move_switch", 0);
        log.info("【接口迁移】开关:{}",interfaceMoveSwitch);
        String reqUrl = null;
        if(interfaceMoveSwitch == 1) {
            reqUrl = CommonUtil.joinPath(userCenterConfigProperty.getUserCenterDomain(), RemoteApiConstant.ManBangApi.GET_USER_REAL_INFO);
        }else{
            reqUrl = userWeb + "/mbOpenAcct/getUserRealInfo.action";
        }

        Map<String, Object> paramMap = CommonUtil.objectToMap(verifyReq);

        UserRealResponse userRealResponse = null;
        try {
            String bodyText = TpayUtil.sendBodyRequest(reqUrl, paramMap);

            ResultMsgBean resultMsgBean = JSON.parseObject(bodyText, ResultMsgBean.class);

            if(resultMsgBean.isSuccess()){

                Object data = resultMsgBean.getData();

                if(data != null){
                    userRealResponse = JSON.parseObject(data.toString(), UserRealResponse.class);
                }
            } else {
                throw TytException.createException(ResponseEnum.rest_error.info("获取用户信息错误！"));
            }
        } catch (Exception e) {
            throw TytException.createException(e);
        }
        return userRealResponse;
    }

}
