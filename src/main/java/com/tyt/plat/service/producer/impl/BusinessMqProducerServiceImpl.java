package com.tyt.plat.service.producer.impl;

import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.plat.commons.properties.RocketMqConfigProperty;
import com.tyt.plat.entity.base.MessageQueueRecord;
import com.tyt.plat.service.producer.BusinessMqProducerService;
import com.tyt.plat.service.producer.MessageQueueRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 消息中心生产者
 */
@Slf4j
@Service
public class BusinessMqProducerServiceImpl extends MqProducerBaseServiceImpl implements BusinessMqProducerService {

    @Autowired
    private MessageQueueRecordService messageQueueRecordService;

    @Autowired
    private RocketMqConfigProperty rocketMqConfigProperty;

    /**
     * 获取topic.
     *
     * @param topic
     * @return
     */
    public String creatTopicName(String topic) {

        String topicSuffix = rocketMqConfigProperty.getTopicSuffix();

        if (topicSuffix == null) {
            topicSuffix = "";
        }

        String topicName = topic + topicSuffix;

        return topicName;
    }

    @Override
    public MessageQueueRecord saveMqRecord(Object dataObj, String topic, String tag, String remark) {
        super.checkMqMessage(dataObj, topic, tag);
        return messageQueueRecordService.saveMqRecord(dataObj, topic, tag, remark);
    }

    @Override
    public SendResult sendMessage(MessageQueueRecord queueRecord, Long delayTime) {

        String topicName = queueRecord.getTopic();
        String tag = queueRecord.getTag();
        String msgKey = queueRecord.getMsgKey();
        String msgContent = queueRecord.getMsgContent();

        log.info("sendMessage_start. topic : {} , tag : {} , msgKey : {}", topicName, tag, msgKey);

        SendResult sendResult = super.produceMessage(topicName, tag, msgKey, msgContent, delayTime);

        return sendResult;
    }

    @Override
    public SendResult saveAndSendMessage(Object dataObj, String topic, String tag, String remark, Long delayTime) {

        MessageQueueRecord queueRecord = this.saveMqRecord(dataObj, topic, tag, remark);

        SendResult sendResult = this.sendMessage(queueRecord, delayTime);

        return sendResult;
    }

}
