package com.tyt.plat.service.producer.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.cargo.dataqueue.client.enums.MqStatusEnum;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.entity.base.MessageQueueRecord;
import com.tyt.plat.mapper.base.MessageQueueRecordMapper;
import com.tyt.plat.service.producer.MessageQueueRecordService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 消息中心生产者
 */
@Slf4j
@Service
public class MessageQueueRecordServiceImpl implements MessageQueueRecordService {

    @Autowired
    private MessageQueueRecordMapper messageQueueRecordMapper;

    /**
     * 保存mq消息表.
     * 以非事务方式运行，直接提交.
     *
     * @param dataObj
     * @param topicName
     * @param tag
     * @param remark
     * @return
     */
    //@Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Override
    public MessageQueueRecord saveMqRecord(Object dataObj, String topicName, String tag, String remark) {
        if (dataObj == null) {
            throw TytException.createException(ResponseEnum.sys_error.info("Mq body Must not be null!"));
        }

        if (StringUtils.isBlank(topicName) || StringUtils.isBlank(tag)) {
            throw TytException.createException(ResponseEnum.sys_error.info("Mq Topic and Tag Must not be null!"));
        }

        String msgContent = null;

        if (dataObj instanceof String) {
            msgContent = (String) dataObj;
        } else {
            msgContent = JSON.toJSONString(dataObj);
        }

        Date nowTime = new Date();

        String msgKey = CommonUtil.getUUID(false);

        MessageQueueRecord queueRecord = new MessageQueueRecord();
        queueRecord.setTopic(topicName);
        queueRecord.setTag(tag);
        queueRecord.setMsgKey(msgKey);
        queueRecord.setRemark(remark);
        queueRecord.setStatus(MqStatusEnum.init.getCode());
        queueRecord.setConsumeCount(0);
        queueRecord.setCreateTime(nowTime);
        queueRecord.setModifyTime(nowTime);
        queueRecord.setMsgContent(msgContent);

        messageQueueRecordMapper.insertSelective(queueRecord);

        return queueRecord;
    }

}
