package com.tyt.plat.service.producer;

import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.plat.entity.base.MessageQueueRecord;

/**
 * 消息中心生产者
 * <AUTHOR>
 */
public interface BusinessMqProducerService {

    /**
     * 保存 mq 消息记录
     *
     * @param dataObj 消息对象
     * @param topic topic
     * @param tag tag
     * @param remark 备注
     * @return MessageQueueRecord
     */
    MessageQueueRecord saveMqRecord(Object dataObj, String topic, String tag, String remark);

    /**
     * 延迟发送mq消息.
     *
     * @param queueRecord 队列记录
     * @param delayTime 延迟时间
     * @return SendResult
     */
    SendResult sendMessage(MessageQueueRecord queueRecord, Long delayTime);

    /**
     * 直接发送mq消息.
     *
     * @param dataObj 消息体
     * @param topic topic
     * @param tag tag
     * @param remark 备注
     * @param delayTime 延迟时间
     * @return SendResult
     */
    SendResult saveAndSendMessage(Object dataObj, String topic, String tag, String remark, Long delayTime);

}
