package com.tyt.plat.service.producer.impl;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.tyt.plat.constant.BeanConfigConstant;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 消息中心生产者
 */
@Slf4j
public class MqProducerBaseServiceImpl {

    @Resource(name = BeanConfigConstant.BUSINESS_PRODUCER)
    protected ProducerBean producerBean;

    /**
     * 生成mq 消息实体
     *
     * @param topicName
     * @param tag
     * @param key
     * @param messageContent
     * @param delayedTime
     * @return
     */
    public Message createMessage(String topicName, String tag, String key, String messageContent, Long delayedTime) {

        Message mqMessage = new Message(topicName, tag, key, messageContent.getBytes(StandardCharsets.UTF_8));

        if (delayedTime != null && delayedTime > 0) {
            mqMessage.setStartDeliverTime(System.currentTimeMillis() + delayedTime);
        }
        return mqMessage;
    }

    /**
     * 校验mq参数.
     *
     * @param dataObj
     * @param topic
     * @param tag
     */
    public void checkMqMessage(Object dataObj, String topic, String tag) {
        if (dataObj == null) {
            throw TytException.createException(ResponseEnum.sys_error.info("Mq Content Must not be null!"));
        }

        if (StringUtils.isBlank(topic) || StringUtils.isBlank(tag)) {
            throw TytException.createException(ResponseEnum.sys_error.info("Mq Topic and Tag Must not be null!"));
        }
    }

    /**
     * 发送mq消息.
     *
     * @param topicName
     * @param tag
     * @param msgKey
     * @param msgContent
     * @param delayTime
     * @return
     */
    public SendResult produceMessage(String topicName, String tag, String msgKey, String msgContent, Long delayTime) {

        Message message = this.createMessage(topicName, tag, msgKey, msgContent, delayTime);

        SendResult sendResult = producerBean.send(message);

        return sendResult;

    }

}
