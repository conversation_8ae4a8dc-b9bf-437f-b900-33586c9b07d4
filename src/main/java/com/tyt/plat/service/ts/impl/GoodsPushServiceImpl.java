package com.tyt.plat.service.ts.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.messagecenter.core.enums.BusinessTagEnum;
import com.tyt.messagecenter.core.enums.NativePageEnum;
import com.tyt.messagecenter.core.utils.CityUtil;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.ts.GoodsPushDataVo;
import com.tyt.model.TransportMain;
import com.tyt.model.TytUserSub;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.plat.service.ts.GoodsPushService;
import com.tyt.service.common.exception.TytException;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytUserSubService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 货源推送
 * <AUTHOR>
 * @date 2022-12-5 15:19:09
 */
@Slf4j
@Service
public class GoodsPushServiceImpl implements GoodsPushService {

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Autowired
    private TytUserSubService tytUserSubService;



    @Override
    public void pushGoodsTips(Long userId, Long srcMsgId) {

        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);

        if(transportMain == null){
            throw TytException.createException(ResponseCodeEnum.参数错误.info("货源不存在！"));
        }

        TytUserSub userSub = tytUserSubService.getTytUserSubByUserId(userId);

        if(userSub == null){
            throw TytException.createException(ResponseCodeEnum.参数错误.info("用户不存在"));
        }

        Long tsUserId = transportMain.getUserId();

        if(userId.equals(tsUserId)){
            throw TytException.createException(ResponseCodeEnum.参数错误.info("无法给自己推送自己的货源"));
        }

        srcMsgId = transportMain.getSrcMsgId();

        String title = "平台推荐—一手货源";

        String[] infoArray = {transportMain.getTaskContent(), transportMain.getWeight() + "吨"};

        String infoText = StringUtils.join(infoArray, " ");

        String startAddress = CityUtil.createAddressInfo(transportMain.getStartCity(), transportMain.getStartArea());
        String destAddress = CityUtil.createAddressInfo(transportMain.getDestCity(), transportMain.getDestArea());

        String price = transportMain.getPrice();
        String priceText = "";

        BigDecimal priceDec = null;
        if(StringUtils.isNotBlank(price)){
            priceDec = new BigDecimal(price);
            priceText = "运费【" + price + "】，";
        }

        GoodsPushDataVo goodsPushDataVo = new GoodsPushDataVo();
        goodsPushDataVo.setSrcMsgId(srcMsgId);
        goodsPushDataVo.setTitle(title);
        goodsPushDataVo.setStartAddress(startAddress);
        goodsPushDataVo.setDestAddress(destAddress);
        goodsPushDataVo.setInfoText(infoText);
        goodsPushDataVo.setPrice(priceDec);

        String extraData = JSON.toJSONString(goodsPushDataVo);

        String remarks = "pushGoodsTips:货源推送";

        String weight = transportMain.getWeight();
        String weightText = weight + "吨";
        String taskContent = transportMain.getTaskContent();

        String contentTmp = "有一条好货，从【%s】到【%s】，%s【%s】【%s】不容错过，快来看看吧。";

        String notifyContent = String.format(contentTmp, startAddress, destAddress, priceText, taskContent, weightText);

        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.addUserId(userId);

        messagePushBase.setTitle(title);
        messagePushBase.setContent(notifyContent);
        messagePushBase.setRemarks(remarks);
        messagePushBase.setCarPush((short)1);

        //通知
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);

        notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
        notifyMessage.addNativeParameter("id", srcMsgId + "");

        notifyMessage.setPushCode(BusinessTagEnum.goods_push.name());
        notifyMessage.setExtraData(extraData);

        messageCenterPushService.sendMultiMessage(null, null, notifyMessage);

    }

}
