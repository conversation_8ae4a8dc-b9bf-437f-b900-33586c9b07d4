package com.tyt.plat.service.ts.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.messagecenter.core.enums.BusinessTagEnum;
import com.tyt.messagecenter.core.enums.NativePageEnum;
import com.tyt.messagecenter.core.utils.CityUtil;
import com.tyt.messagecenter.core.utils.ConvertUtil;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.ts.GoodsPushDataVo;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.model.TytUserSub;
import com.tyt.plat.entity.base.TytTransportHistory;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.plat.mapper.base.TytTransportHistoryMapper;
import com.tyt.plat.mapper.base.TytTransportMainExtendMapper;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.plat.service.ts.GoodsPushService;
import com.tyt.plat.service.ts.TransportHistoryService;
import com.tyt.service.common.exception.TytException;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytUserSubService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 货源历史记录
 * <AUTHOR>
 * @date 2022-12-5 15:19:09
 */
@Slf4j
@Service
public class TransportHistoryServiceImpl implements TransportHistoryService {

    @Autowired
    private TytTransportHistoryMapper tytTransportHistoryMapper;
    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;

    @Override
    public TytTransportHistory createWithTransport(Transport transport){

        JSONObject jsonObj = (JSONObject)JSON.toJSON(transport);

        jsonObj.remove("startCoordX");
        jsonObj.remove("startCoordY");
        jsonObj.remove("destCoordX");
        jsonObj.remove("destCoordY");
        jsonObj.remove("startLatitude");
        jsonObj.remove("startLongitude");
        jsonObj.remove("destLongitude");
        jsonObj.remove("destLatitude");
        jsonObj.remove("distance");

        TytTransportHistory tytTransportHistory = jsonObj.toJavaObject(TytTransportHistory.class);

        tytTransportHistory.setId(null);

        tytTransportHistory.setTsId(transport.getId());
        tytTransportHistory.setPubQq(transport.getPubQQ());
        tytTransportHistory.setUploadCellphone(transport.getUploadCellPhone());
        tytTransportHistory.setStartCoordX(transport.getStartCoordXValue());
        tytTransportHistory.setStartCoordY(transport.getStartCoordYValue());
        tytTransportHistory.setDestCoordX(transport.getDestCoordXValue());
        tytTransportHistory.setDestCoordY(transport.getDestCoordYValue());
        tytTransportHistory.setStartLongitude(transport.getStartLongitudeValue());
        tytTransportHistory.setStartLatitude(transport.getStartLatitudeValue());
        tytTransportHistory.setDestLongitude(transport.getDestLongitudeValue());
        tytTransportHistory.setDestLatitude(transport.getDestLatitudeValue());
        tytTransportHistory.setDistance(transport.getDistanceValue());
        tytTransportHistory.setMachineRemark(transport.getMachineRemark());
        tytTransportHistory.setInvoiceTransport(transport.getInvoiceTransport());
        tytTransportHistory.setAdditionalPrice(transport.getAdditionalPrice());
        tytTransportHistory.setEnterpriseTaxRate(transport.getEnterpriseTaxRate());
        tytTransportHistory.setDriverDriving(transport.getDriverDriving());
        tytTransportHistory.setLoadCellPhone(transport.getLoadCellPhone());
        tytTransportHistory.setUnloadCellPhone(transport.getUnloadCellPhone());
        tytTransportHistory.setCargoOwnerId(transport.getCargoOwnerId());
        tytTransportHistory.setExcellentGoods(transport.getExcellentGoods());
        tytTransportHistory.setExcellentGoodsTwo(transport.getExcellentGoodsTwo());
        tytTransportHistory.setPublishGoodsType(transport.getPublishGoodsType());

        if (Objects.nonNull(transport.getSrcMsgId())) {
            TytTransportMainExtend mainExtend = tytTransportMainExtendMapper.getBySrcMsgId(transport.getSrcMsgId());
            if (Objects.nonNull(mainExtend)) {
                // tytTransportHistory.setUseCarType(mainExtend.getUseCarType());
                // tytTransportHistory.setPriceType(mainExtend.getPriceType());
                // tytTransportHistory.setSuggestMinPrice(mainExtend.getSuggestMinPrice());
                // tytTransportHistory.setSuggestMaxPrice(mainExtend.getSuggestMaxPrice());
                // tytTransportHistory.setGoodModelLevel(mainExtend.getGoodModelLevel());
                // tytTransportHistory.setGoodModelScore(mainExtend.getGoodModelScore());
                // tytTransportHistory.setLimGoodModelScore(mainExtend.getLimGoodModelScore());
                // tytTransportHistory.setLimGoodModelLevel(mainExtend.getLimGoodModelLevel());
                // tytTransportHistory.setPriceCap(mainExtend.getPriceCap());
                // tytTransportHistory.setCommissionScore(mainExtend.getCommissionScore());
                // tytTransportHistory.setSeckillGoods(mainExtend.getSeckillGoods());
                // tytTransportHistory.setPerkPrice(mainExtend.getPerkPrice());
                // tytTransportHistory.setGoodTransportLabel(mainExtend.getGoodTransportLabel());
                // tytTransportHistory.setGoodTransportLabelPart(mainExtend.getGoodTransportLabelPart());
                BeanUtils.copyProperties(mainExtend, tytTransportHistory, "id", "srcMsgId", "createTime", "modifyTime");
            }
        }
        return tytTransportHistory;
    }

    @Override
    public void saveTransportHistory(Transport transport) {

        if(transport != null && transport.getId() != null){
            TytTransportHistory tytTransportHistory = this.createWithTransport(transport);
            tytTransportHistoryMapper.insert(tytTransportHistory);
        }

    }

}
