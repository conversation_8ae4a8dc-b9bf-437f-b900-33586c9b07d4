package com.tyt.plat.mapper.recommend;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.recommend.GoodTypeCnt;
import org.apache.ibatis.annotations.Param;

/**
 *
 *
 * <AUTHOR>
 * @since 2024-12-02 13:32
 */
public interface GoodTypeCntMapper extends CustomBaseMapper<GoodTypeCnt> {
    Integer getGoodsTypeCnt(@Param("userId") Long userId, @Param("goodTypeName") String goodTypeName);
}
