package com.tyt.plat.mapper.recommend;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tytrecommend.model.NewIdentity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * bi身份信息
 * <AUTHOR>
 * @since 2024-05-31 09:40
 */
@Mapper
public interface NewIdentityMapper extends CustomBaseMapper<NewIdentity> {
    NewIdentity getByUserId(@Param("userId") Long userId);

    List<NewIdentity> batchGetByUserId(@Param("userIds") List<Long> userIds);

}
