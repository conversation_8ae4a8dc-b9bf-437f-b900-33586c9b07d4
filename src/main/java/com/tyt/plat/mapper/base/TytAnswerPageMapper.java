package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytAnswerPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytAnswerPageMapper extends CustomBaseMapper<TytAnswerPage> {

    /**
     * 根据类目id 获取问答列表
     * @param categoryId
     * @return
     */
    List<TytAnswerPage> getAnswerList(@Param("categoryId") Long categoryId, @Param("pageSize") Integer pageSize,
                                      @Param("sortNumber") Integer sortNumber);

    /**
     * 根据问答标题模糊查询问答列表
     * @param titleFragment 问答标题片段
     * @param categoryIdList 分类ID集合
     * @return 问答列表
     */
    List<TytAnswerPage> queryByTitleFragment(@Param("titleFragment") String titleFragment, @Param("categoryIdList") List<Long> categoryIdList);
}