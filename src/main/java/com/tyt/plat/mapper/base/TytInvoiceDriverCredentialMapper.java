package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytInvoiceDriverCredential;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytInvoiceDriverCredentialMapper extends CustomBaseMapper<TytInvoiceDriverCredential> {

    List<TytInvoiceDriverCredential> selectByDriverId(@Param("driverId") Long driverId);

}