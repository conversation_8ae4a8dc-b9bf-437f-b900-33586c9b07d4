package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCoverGoodsDialConfigUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface TytCoverGoodsDialConfigUserMapper extends CustomBaseMapper<TytCoverGoodsDialConfigUser> {
    /**
     * 根据userId查看捂货货源用户导入信息
     *
     * @param userId 用户Id
     * @return TytCoverGoodsDialConfigUser
     */
    TytCoverGoodsDialConfigUser selectEnabledLastImported(@Param("userId") Long userId);


}