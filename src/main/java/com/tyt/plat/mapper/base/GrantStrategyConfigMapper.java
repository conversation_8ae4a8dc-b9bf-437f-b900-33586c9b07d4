package com.tyt.plat.mapper.base;


import com.tyt.model.GrantStrategyConfig;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GrantStrategyConfigMapper extends CustomBaseMapper<GrantStrategyConfig> {

    List<GrantStrategyConfig> selectByGrantType(@Param("grantType") String grantType);

}