package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportExposure;
import com.tyt.transport.querybean.DestTransportSearchBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytTransportExposureMapper extends CustomBaseMapper<TytTransportExposure> {

    Long getMaxChangeId();

    /**
     * 根据路线信息查询急走货源中信用分等级最高的货源id
     * @param destTransportSearchBean
     * @return
     */
    Long selectMaxRankLevelByDestTransportSearchBean(DestTransportSearchBean destTransportSearchBean);

    /**
     * 根据货物相似code查询急走货源中信用分等级最高的货源id
     * @param similarityCode
     * @param goodsId
     * @return
     */
    Long selectMaxRankLeveBySimilarityCode(@Param("similarityCode") String similarityCode, @Param("goodsId") Long goodsId);
}
