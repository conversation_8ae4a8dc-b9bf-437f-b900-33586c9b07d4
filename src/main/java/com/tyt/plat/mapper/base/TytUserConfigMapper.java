package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytUserConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytUserConfigMapper extends CustomBaseMapper<TytUserConfig> {

    List<TytUserConfig> selectByConfigTypesAndUserId(@Param("userId") Long userId,
                                                     @Param("configTypes") List<String> configTypes);

    TytUserConfig selectByUserIdAndType(@Param("userId") Long userId, @Param("type") String type);
}