package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportValuable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytTransportValuableMapper extends CustomBaseMapper<TytTransportValuable> {
    TytTransportValuable selectBySrcMsgId(@Param("srcMsgId") Long srcMsgId);
}