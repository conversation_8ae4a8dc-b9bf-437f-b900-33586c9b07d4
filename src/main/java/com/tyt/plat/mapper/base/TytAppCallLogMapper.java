package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytAppCallLog;
import com.tyt.plat.vo.ts.CallLogQuery;
import com.tyt.transport.querybean.CallLogBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TytAppCallLogMapper extends CustomBaseMapper<TytAppCallLog> {
    TytAppCallLog callDetail(@Param("userId") String userId,@Param("goodId") String goodId,@Param("clientSign") String clientSign);

    /**
     * 查询拨打总数
     * @return
     */
    int getCallLogCount(CallLogQuery callLogQuery);

    /**
     * 通话记录列表
     * @param callLogQuery
     * @return
     */
    List<CallLogBean> getCallLogList(CallLogQuery callLogQuery);

    /**
     * 浏览记录列表
     * @param callLogQuery
     * @return
     */
    List<CallLogBean> getViewLogList(CallLogQuery callLogQuery);

    /**
     * Returns the last backout reason time for a given tsId.
     *
     * @param tsId the tsId of the transport service
     * @return the last backout reason time as a Date object, or null if not found
     */
    Date getLastAddMoneyTime(@Param("tsId") Long tsId);

    /**
     * Returns the count of view logs based on the source message ID.
     *
     * @param tsId the ID of the transport service
     * @return the count of view logs as an integer
     */
    int getViewLogCountBySrcMsgId(@Param("tsId") Long tsId);

    List<Long> getViewLogUserIdListBySrcMsgId(@Param("tsId") Long tsId);

    /**
     * Returns the count of call logs based on the source message ID.
     *
     * @param tsId the ID of the transport service
     * @return the count of call logs as an integer
     */
    int getCallLogCountBySrcMsgId(@Param("tsId") Long tsId);

    List<Long> getSrcMsgIdListByTransportUserIdAndDate(@Param("transportUserId") Long transportUserId, @Param("startDate") Date startDate);

}