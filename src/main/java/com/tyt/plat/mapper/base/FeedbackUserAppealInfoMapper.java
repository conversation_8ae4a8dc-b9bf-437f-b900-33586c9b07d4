package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.FeedbackUserAppealInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FeedbackUserAppealInfoMapper extends CustomBaseMapper<FeedbackUserAppealInfo> {

    FeedbackUserAppealInfo getInfoByAppealId(@Param("appealId")Long appealId);
}