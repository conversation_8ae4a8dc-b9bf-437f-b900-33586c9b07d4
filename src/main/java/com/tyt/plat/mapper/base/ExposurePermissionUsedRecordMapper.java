package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.ExposurePermissionUsedRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExposurePermissionUsedRecordMapper extends CustomBaseMapper<ExposurePermissionUsedRecord> {

    /**
     * 查询曝光权益使用记录
     * @param userId userId
     * @param today 今天日期字符串 （YYYY-MM-DD）
     * @return List ExposurePermissionUsedRecord
     */
    List<ExposurePermissionUsedRecord> getExposurePermissionUsedRecordListByUserId(@Param("userId") Long userId, @Param("today") String today);
}