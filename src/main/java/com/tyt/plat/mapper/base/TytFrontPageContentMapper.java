package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytFrontPageContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytFrontPageContentMapper extends CustomBaseMapper<TytFrontPageContent> {

    /**
     * 根据pageId 获取内容.
     * @param pageId 页面id
     * @return TytFrontPageContent
     */
    TytFrontPageContent getPageContentByPageId(@Param("pageId") Long pageId);

}