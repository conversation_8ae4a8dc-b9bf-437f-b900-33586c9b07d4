package com.tyt.plat.mapper.base;

import com.tyt.plat.biz.covergoodsdial.pojo.CoverGoodsUserBeansVO;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCoverGoodsBeansConfigUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytCoverGoodsBeansConfigUserMapper extends CustomBaseMapper<TytCoverGoodsBeansConfigUser> {


    /**
     * 根据用户Id 查看用户抢单豆获取记录
     * @param userId
     * @return CoverGoodsDialGainInfoVO
     */
    List<TytCoverGoodsBeansConfigUser> selectCoverGoodsDialGainLogByUserId(@Param("userId") Long userId);


    /**
     * 根据userId获取当前用户剩余抢单豆总个数
     * @param userId
     * @return int
     */
    CoverGoodsUserBeansVO selectTotalBeansLeftNumByUserId(@Param("userId") Long userId);


    /**
     * 根据用户Id 查看用户剩余次数大于0的抢单豆获取记录
     * @param userId
     * @return CoverGoodsDialGainInfoVO
     */
    List<TytCoverGoodsBeansConfigUser> selectLeftNumByUserId(@Param("userId") Long userId);

    /**
     * 使用一次抢单豆更新数据
     * @param id
     */
    void useBeansById(@Param("id") Long id,@Param("beansStatus") Integer beansStatus);
}