package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytInvoiceDriverMapper extends CustomBaseMapper<TytInvoiceDriver> {

    List<TytInvoiceDriver> selectByUserIdAndVerifyStatus(@Param("userId") Long userId, @Param("verifyStatus") Integer verifyStatus);

    Long countByUserIdAndVerifyStatus(@Param("userId") Long userId, @Param("verifyStatus") Integer verifyStatus);

    long countByUserIdAndIdCard(@Param("userId") Long userId, @Param("idCard") String idCard);

    List<TytInvoiceDriver> getByPhone(@Param("cellPhone") String cellPhone);

    List<TytInvoiceDriver> getDriverPhone(@Param("cellPhone") String driverPhone,@Param("userId") Long userId);

    List<TytInvoiceDriver> getByUserIdInvoice(@Param("userId") Long userId);

    List<TytInvoiceDriver> getByCarUserId(@Param("userId") Long userId);

    long countByUserIdAndPhone(@Param("userId") Long userId,@Param("cellPhone") String phone);

    long countByUserIdIdCard(@Param("userId") Long userId,@Param("idCard") String idCard,@Param("id") Long id);

    long countByUserIdPhone(@Param("userId") Long userId,@Param("cellPhone") String phone, @Param("id") Long id);

    List<TytInvoiceDriver> getInvoiceDriverListByDriverUserId(@Param("driverUserId") Long driverUserId);

    List<TytInvoiceDriver> getByUserId(@Param("userId") Long userId);

    int updatePhoneByPhone(@Param("newPhone") String newPhone, @Param("oldPhone") String oldPhone);

    void updateDelStatusByCellPhone(@Param("cellPhone") String cellPhone);
}