package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface TytTransportMainExtendMapper extends CustomBaseMapper<TytTransportMainExtend> {

    TytTransportMainExtend getBySrcMsgId(Long srcMsgId);

    /**
     * 返回用车类型
     */
    List<TytTransportMainExtend> getUseCarTypeBySrcMsgIds(List<Long> srcMsgIds);

    Integer getPriceTypeBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    void recordScoreEveryTransportUpdate(@Param("srcMsgId") Long srcMsgId, @Param("instantGrabScore") BigDecimal instantGrabScore, @Param("commissionScore") BigDecimal commissionScore);

    Integer getTransportIsSeckillGoods(@Param("srcMsgId") Long srcMsgId);

    Integer getSeckillGoodsHaveSuccess(@Param("srcMsgId") Long srcMsgId);

    Integer getSeckillGoodsHaveError(@Param("srcMsgId") Long srcMsgId);

}