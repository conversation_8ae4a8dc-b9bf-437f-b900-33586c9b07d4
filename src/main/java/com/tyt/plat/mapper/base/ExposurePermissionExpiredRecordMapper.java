package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.ExposurePermissionExpiredRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExposurePermissionExpiredRecordMapper extends CustomBaseMapper<ExposurePermissionExpiredRecord> {

    /**
     * 查询曝光权益过期记录
     * @param userId userId
     * @param today 今天日期字符串 （YYYY-MM-DD）
     * @return List ExposurePermissionExpiredRecord
     */
    List<ExposurePermissionExpiredRecord> getExposurePermissionExpiredRecordListByUserId(@Param("userId") Long userId, @Param("today") String today);
}