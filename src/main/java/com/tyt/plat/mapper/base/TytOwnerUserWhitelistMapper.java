package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytOwnerUserWhitelist;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytOwnerUserWhitelistMapper extends CustomBaseMapper<TytOwnerUserWhitelist> {
    /**
     * 根据userid查询下单白名单信息
     * @param userId
     * @return
     */
    TytOwnerUserWhitelist selectByUserId(@Param("userId") Long userId);
}