package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytDispatchCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2024-06-03 14:20
 */
@Mapper
public interface TytDispatchCompanyMapper extends CustomBaseMapper<TytDispatchCompany> {
    int countByUserId(@Param("userId") Long userId);

    TytDispatchCompany getByUserId(@Param("userId") Long userId);
}
