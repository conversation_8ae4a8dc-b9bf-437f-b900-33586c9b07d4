package com.tyt.plat.mapper.base;

import com.tyt.acvitity.bean.ActivityInfo;
import com.tyt.plat.entity.base.TytFreeTecServiceFeeLog;
import com.tyt.plat.entity.base.TytTransportTecServiceFee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface TytTransportTecServiceFeeMapper {

    int insert(TytTransportTecServiceFee record);

    TytTransportTecServiceFee getBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    int updateBySrcMsgId(TytTransportTecServiceFee record);

    int delete(@Param("srcMsgId") Long srcMsgId);

    /**
     *
     * @param srcMsgId 货源ID
     * @param optionType 操作类型：1:抽佣；2:不抽佣
     * @param fromType 操作来源：1:APP；2:调度后台；3:定时任务（这里固定传1）
     */
    void saveTransportTecServiceFeeLog(@Param("srcMsgId") Long srcMsgId, @Param("optionType") int optionType, @Param("fromType") int fromType);

    List<ActivityInfo> getActivity(@Param("type") Integer type);

    Integer getActivityRecord(@Param("activityId")Long id, @Param("userId") Long userId);

    Integer getPrize(@Param("activityId")Long id, @Param("userId") Long userId);

    Integer getUserAuth(Long userId);

    Integer getCarUserPhone(@Param("userId") Long userId,@Param("time") Date time);

    Integer getGoodsUserTransport(@Param("userId") Long userId,@Param("time") Date time);

    void deleteFreeTecServiceFeeLogBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    void addFreeTecServiceFeeLog(TytFreeTecServiceFeeLog tytFreeTecServiceFeeLog);

}