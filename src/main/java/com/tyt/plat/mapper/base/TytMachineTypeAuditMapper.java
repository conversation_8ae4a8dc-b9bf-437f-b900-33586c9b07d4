package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytMachineTypeAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytMachineTypeAuditMapper extends CustomBaseMapper<TytMachineTypeAudit> {
    /**
     * @param showName
     * @return
     */
    TytMachineTypeAudit selectByShowName(@Param("showName") String showName);
}