package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.GoodsAddressLevelRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface GoodsAddressLevelRecordMapper extends CustomBaseMapper<GoodsAddressLevelRecord> {
    GoodsAddressLevelRecord selectBySrcMsgId(@Param("srcMsgId") Long srcMsgId);
}