package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.BlacklistUserOrders;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface BlackListUserOrdersMapper  extends CustomBaseMapper<BlacklistUserOrders> {

    @Select("select o.id," +
            "o.user_id userId," +
            "o.waybill_ex_id waybillExId," +
            "o.ts_order_no tsOrderNo," +
            "o.status status," +
            "o.perpetual perpetual," +
            "o.restrict_num restrictNum," +
            "o.restrict_start_time restrictStartTime," +
            "o.restrict_end_time restrictEndTime," +
            "o.car_limit_start_time carLimitStartTime," +
            "o.car_limit_end_time carLimitEndTime," +
            "o.car_limit_num carLimitNum," +
            "o.goods_limit_start_time goodsLimitStartTime," +
            "o.goods_limit_end_time goodsLimitEndTime," +
            "o.goods_limit_num goodsLimitNum," +
            "o.goods_cause goodsCause," +
            "o.car_cause carCause," +
            "o.remark remark," +
            "o.opera_user_id operaUserId," +
            "o.opera_user_name operaUserName," +
            "o.create_time createTime," +
            "o.modify_time modifyTime " +
            " from blacklist_user_orders o " +
            " where o.user_id = #{userId} " +
            " and o.status = 1 " +
            " and o.restrict_start_time <= #{nowDate} " +
            " and o.restrict_end_time   >= #{nowDate} " +
            " order by o.id desc limit 1")
    BlacklistUserOrders getOrdersByUserIdAndRestrictTime(@Param("userId")Long userId, @Param("nowDate") Date nowDate);

    @Select("select o.id," +
            "o.user_id userId," +
            "o.waybill_ex_id waybillExId," +
            "o.ts_order_no tsOrderNo," +
            "o.status status," +
            "o.perpetual perpetual," +
            "o.restrict_num restrictNum," +
            "o.restrict_start_time restrictStartTime," +
            "o.restrict_end_time restrictEndTime," +
            "o.car_limit_start_time carLimitStartTime," +
            "o.car_limit_end_time carLimitEndTime," +
            "o.car_limit_num carLimitNum," +
            "o.goods_limit_start_time goodsLimitStartTime," +
            "o.goods_limit_end_time goodsLimitEndTime," +
            "o.goods_limit_num goodsLimitNum," +
            "o.goods_cause goodsCause," +
            "o.car_cause carCause," +
            "o.remark remark," +
            "o.opera_user_id operaUserId," +
            "o.opera_user_name operaUserName," +
            "o.create_time createTime," +
            "o.modify_time modifyTime " +
            " from blacklist_user_orders o " +
            " where o.user_id = #{userId} " +
            " and o.goods_limit_start_time <= #{nowDate} " +
            " and o.goods_limit_end_time >= #{nowDate} " +
            " order by o.id desc limit 1")
    BlacklistUserOrders getGoodsLimitByUserIdAndTime(@Param("userId")Long userId, @Param("nowDate") Date nowDate);

    @Select("select o.id," +
            "o.user_id userId," +
            "o.waybill_ex_id waybillExId," +
            "o.ts_order_no tsOrderNo," +
            "o.status status," +
            "o.perpetual perpetual," +
            "o.restrict_num restrictNum," +
            "o.restrict_start_time restrictStartTime," +
            "o.restrict_end_time restrictEndTime," +
            "o.car_limit_start_time carLimitStartTime," +
            "o.car_limit_end_time carLimitEndTime," +
            "o.car_limit_num carLimitNum," +
            "o.goods_limit_start_time goodsLimitStartTime," +
            "o.goods_limit_end_time goodsLimitEndTime," +
            "o.goods_limit_num goodsLimitNum," +
            "o.goods_cause goodsCause," +
            "o.car_cause carCause," +
            "o.remark remark," +
            "o.opera_user_id operaUserId," +
            "o.opera_user_name operaUserName," +
            "o.create_time createTime," +
            "o.modify_time modifyTime " +
            " from blacklist_user_orders o " +
            " where o.user_id = #{userId} ")
    List<BlacklistUserOrders> getBlackByUserId(Long userId);
}