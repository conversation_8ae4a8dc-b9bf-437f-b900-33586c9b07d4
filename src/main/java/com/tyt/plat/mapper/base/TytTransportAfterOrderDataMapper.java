package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportAfterOrderData;
import com.tyt.transport.querybean.SameTransportAveragePriceData;
import com.tyt.transport.querybean.SameTransportAveragePriceReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytTransportAfterOrderDataMapper extends CustomBaseMapper<TytTransportAfterOrderData> {

    /**
     * Retrieves the average price of goods with the same transport.
     * The method takes in a SameTransportAveragePriceReq object as input, which contains the necessary parameters for the query.
     *
     * @param sameTransportAveragePriceReq the request object containing the parameters for the query
     * @return a list of SameTransportAveragePriceData objects representing the average price data for the same transport
     */
    List<SameTransportAveragePriceData> getSameTransportAveragePrice(@Param("req") SameTransportAveragePriceReq sameTransportAveragePriceReq);

}