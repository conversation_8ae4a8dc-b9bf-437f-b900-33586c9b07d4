package com.tyt.plat.mapper.base;

import com.tyt.plat.biz.feedback.manager.pojo.FeedbackLabelSumDTO;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.FeedbackUserLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface FeedbackUserLabelMapper extends CustomBaseMapper<FeedbackUserLabel> {

    void deleteByFeedbackId(@Param("feedbackId") Long feedbackId);

    List<FeedbackUserLabel> selectByByFeedbackIdAndDelFlag(@Param("feedbackId") Long feedbackId,
                                                           @Param("delFlag") Boolean delFlag);

    /**
     * 统计数量
     *
     * @param postUserId      发表人id
     * @param postUserType    发表人类型
     * @param receiveUserId   接收人id
     * @param receiveUserType 接收人类型
     * @param feedbackType    评价类型
     * @param delFlag         是否删除
     * @param beginTime       起始时间
     * @return count
     */
    Long count(@Param("postUserId") Long postUserId, @Param("postUserType") Integer postUserType,
               @Param("receiveUserId") Long receiveUserId, @Param("receiveUserType") Integer receiveUserType,
               @Param("feedbackType") int feedbackType, @Param("delFlag") boolean delFlag,
               @Param("beginTime") Date beginTime);

    /**
     * sum
     *
     * @param postUserId      发表人id
     * @param postUserType    发表人类型
     * @param receiveUserId   接收人id
     * @param receiveUserType 接收人类型
     * @param beginTime       起始时间
     * @param delFlag         是否删除
     * @param feedbackType    评价类型
     * @return FeedbackLabelSumDTO
     */
    List<FeedbackLabelSumDTO> sum(@Param("postUserId") Long postUserId,
                                  @Param("postUserType") Integer postUserType,
                                  @Param("receiveUserId") Long receiveUserId,
                                  @Param("receiveUserType") Integer receiveUserType,
                                  @Param("beginTime") Date beginTime,
                                  @Param("delFlag") boolean delFlag,
                                  @Param("feedbackType") Integer feedbackType);

    List<String> selectLabelNames(@Param("feedbackId") Long feedbackId, @Param("delFlag") boolean delFlag);
}