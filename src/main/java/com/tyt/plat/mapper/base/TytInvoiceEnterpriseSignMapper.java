package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytInvoiceEnterpriseSign;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytInvoiceEnterpriseSignMapper extends CustomBaseMapper<TytInvoiceEnterpriseSign> {

    /**
     * 获取企业有效的签约记录.
     * @param enterpriseId enterpriseId
     * @return TytInvoiceEnterpriseSign
     */
    TytInvoiceEnterpriseSign getEnableEnterpriseSign(@Param("enterpriseId") Long enterpriseId);

    /**
     * 获取企业有效的签约记录.
     * @param userId userId
     * @return TytInvoiceEnterpriseSign
     */
    TytInvoiceEnterpriseSign getUserEnterpriseSign(@Param("userId") Long userId);

}