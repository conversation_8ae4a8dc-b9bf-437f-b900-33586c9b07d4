package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytDispatchCargoOwner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytDispatchCargoOwnerMapper extends CustomBaseMapper<TytDispatchCargoOwner> {
    /**
     * 根据用户id查询专车获取
     * @param userId 用户id
     * @return owner
     */
    TytDispatchCargoOwner getByUserId(@Param("userId") Long userId);

    /**
     * 获取签约合作商列表
     * @return list
     */
    List<TytDispatchCargoOwner> getSigningList();

    TytDispatchCargoOwner getByCooperativeId(@Param("cooperativeId") Long cooperativeId);
}