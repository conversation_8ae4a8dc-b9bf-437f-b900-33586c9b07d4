package com.tyt.plat.mapper.base;


import com.tyt.acvitity.bean.UserCenterExposureInfo;
import com.tyt.plat.entity.base.ExposurePermissionGainRecord;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ExposurePermissionGainRecordMapper extends CustomBaseMapper<ExposurePermissionGainRecord> {


    /**
     * 根据类型查询用户获取权益次数
     * @param userId
     * @param gainType
     * @return
     */
    int getPermissionGainRecordNum(@Param("userId") Long userId,@Param("gainType") int gainType);

    /**
     * 查询曝光权益获取记录
     * @param userId userId
     * @param today 今天日期字符串 （YYYY-MM-DD）
     * @return List ExposurePermissionGainRecord
     */
    List<ExposurePermissionGainRecord> getExposurePermissionGainRecordListByUserId(@Param("userId") Long userId, @Param("today") String today);

    Long getMaxGainId(@Param("userId") Long userId);

    /**
     * 获取我的页面曝光卡获取信息【带聚合查询】
     * @param userId
     * @param startTime
     * @return
     */
    List<UserCenterExposureInfo> selectUserCenterExposureInfo(@Param("userId") Long userId,@Param("startTime") String startTime);
    /**
     * 获取我的页面曝光卡获取信息【不带聚合查询】
     * @param userId
     * @param startTime
     * @return
     */
    List<UserCenterExposureInfo> selectLastExposureInfo(@Param("userId") Long userId,@Param("startTime") String startTime);

    int getNewExposureCount(@Param("userId") Long userId, @Param("markId") Long markId, @Param("startTime") Date startTime);

}