package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.FeedbackUserNegativeLabelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FeedbackUserNegativeLabelConfigMapper extends CustomBaseMapper<FeedbackUserNegativeLabelConfig> {

    FeedbackUserNegativeLabelConfig selectByUserId(@Param("userId") Long userId);
}