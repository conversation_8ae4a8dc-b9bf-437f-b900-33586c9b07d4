package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportMbMerge;
import com.tyt.transport.querybean.TransportYmmListBean;
import com.tyt.transport.querybean.TransportYmmSyncLogBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytTransportMbMergeMapper extends CustomBaseMapper<TytTransportMbMerge> {
    TytTransportMbMerge selectByCargoId(@Param("cargoId") Long cargoId);

    void updateCargoMerge(@Param("srcMsgId") Long srcMsgId);

    TytTransportMbMerge selectBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    /**
     * 查询当前版本发布的货源信息
     * @param srcMsgId
     * @return
     */
    TransportYmmListBean selectBeforeVersionJson(@Param("srcMsgId") Long srcMsgId);

    /**
     * 查询最后一次修改信息
     * @param cargoId
     * @return
     */
    TransportYmmSyncLogBean selectLastVersionJson(@Param("cargoId") Long cargoId);


}