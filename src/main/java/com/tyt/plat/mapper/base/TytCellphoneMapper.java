package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCellphone;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytCellphoneMapper extends CustomBaseMapper<TytCellphone> {

    TytCellphone selectByCellPhone(@Param("cellPhone") String cellPhone);

    void insertCellPhoneToTemp(String cellPhone);

    void deleteFromTytCellphone(@Param("cellPhone") String cellPhone);
}