package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCarOwnerAuthProof;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytCarOwnerAuthProofMapper extends CustomBaseMapper<TytCarOwnerAuthProof> {

    void deleteByCarOwnerAuthId(@Param("carOwnerAuthId") Long carOwnerAuthId);
}