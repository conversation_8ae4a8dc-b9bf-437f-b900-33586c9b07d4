package com.tyt.plat.mapper.base;

import com.tyt.plat.entity.base.GoodsRefreshManualDO;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * <p>
 * 货源刷新手动曝光表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Mapper
public interface GoodsRefreshManualMapper extends BaseMapper<GoodsRefreshManualDO> {

    /**
     * 获取最近7天的曝光动态
     */
    List<String> getRecentlyDynamic();

    /**
     * 根据srcMsgId查询
     */
    GoodsRefreshManualDO getBySrcMsgId(Long srcMsgId);
}
