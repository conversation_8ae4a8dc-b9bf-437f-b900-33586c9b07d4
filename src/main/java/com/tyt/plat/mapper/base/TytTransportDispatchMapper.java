package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportDispatch;
import com.tyt.plat.entity.base.TytTransportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface TytTransportDispatchMapper extends CustomBaseMapper<TytTransportDispatch> {
    /**
     * @return TytTransportDispatch
     * @description
     * @date 2023/9/15 18:24
     * @Param srcMsgId:
     */
    TytTransportDispatch getTytTransportDispatchBySrcId(@Param("srcMsgId") Long srcMsgId);

    TytTransportVO getDispatchAndGoodsInfo(Long srcMsgId);

    int countExcellentByPhone(@Param("cellPhone") String cellPhone, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}