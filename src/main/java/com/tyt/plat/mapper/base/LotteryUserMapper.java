package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.LotteryUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface LotteryUserMapper extends CustomBaseMapper<LotteryUser> {

    long countByUserIdAndDrawActivityId(@Param("userId") Long userId,
            @Param("drawActivityId") Long drawActivityId);

}