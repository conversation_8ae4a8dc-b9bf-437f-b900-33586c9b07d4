package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportPricePerkConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytTransportPricePerkConfigMapper extends CustomBaseMapper<TytTransportPricePerkConfig> {
    TytTransportPricePerkConfig getConfigByCity(@Param("startCity")String startCity, @Param("destCity") String destCity);
}