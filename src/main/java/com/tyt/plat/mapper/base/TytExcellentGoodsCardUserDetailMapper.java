package com.tyt.plat.mapper.base;

import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetail;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytExcellentGoodsCardUserDetailMapper extends CustomBaseMapper<TytExcellentGoodsCardUserDetail> {

    List<TytExcellentGoodsCardUserDetail> getAllNoUseCarListByUserId(@Param("userId") Long userId, @Param("startNum") Integer startNum, @Param("pageSize") Integer pageSize);

    TytExcellentGoodsCardUserDetailCanUseCountVO getAllCanUseCarCountNumMax100AndLimitTimeByUserId(@Param("userId") Long userId);

    List<TytExcellentGoodsCardUserDetail> getAllCanUseCarListByUserId(@Param("userId") Long userId);

    List<TytExcellentGoodsCardUserDetail> getAllCanUseCarListByUserIdPage(@Param("userId") Long userId, @Param("startNum") Integer startNum, @Param("pageSize") Integer pageSize);

    /**
     * 更新优车发货卡用户明细表状态
     *
     * @param excellentCardId
     * @param type
     */
    int updateType(@Param("excellentCardId") Long excellentCardId, @Param("type") Integer type);

    int getAllCanUseCardCountNumByUserId(@Param("userId") Long userId);

    Long getPriceCardIdByUserId(Long userId);

    TytExcellentGoodsCardUserDetail getByUserDate(Long userId);
}