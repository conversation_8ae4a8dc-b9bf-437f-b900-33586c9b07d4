package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytInvoiceTaxRateRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytInvoiceTaxRateRuleMapper extends CustomBaseMapper<TytInvoiceTaxRateRule> {

    TytInvoiceTaxRateRule getInvoiceTaxRateRule(@Param("configType") Integer configType, @Param("enterpriseId") Long enterpriseId);

}