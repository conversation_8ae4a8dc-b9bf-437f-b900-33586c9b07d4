package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCarOwnerAuth;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface TytCarOwnerAuthMapper extends CustomBaseMapper<TytCarOwnerAuth> {
    /**
     * 根据用户id及审核状态查询
     * @param userId
     * @param authStatus
     * @return
     */
    List<TytCarOwnerAuth> selectByUserIdAuthStatus(@Param("userId") Long userId, @Param("authStatus") Integer authStatus);

    /**
     * 统计某个用户车主认证成功数量
     * @param userId
     * @return
     */
    int countSucceedNum(Long userId);

    TytCarOwnerAuth selectByCarId(@Param("carId") Long carId);
    TytCarOwnerAuth selectByCarIdAndAuth(@Param("carId") Long carId);

    @MapKey("carId")
    Map<Long,TytCarOwnerAuth> selectConvertMap(@Param("userId") Long userId);

    List<Long> selectCarIds(@Param("userId") Long userId);
    List<Long> selectTytCarIds(@Param("userId") Long userId,@Param("auth") Integer auth);
}