package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytIntelligentDepositConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

@Mapper
public interface TytIntelligentDepositConfigMapper extends CustomBaseMapper<TytIntelligentDepositConfig> {
    TytIntelligentDepositConfig getConfigByWeightAndDistance(@Param("weight") BigDecimal weight,
                                                             @Param("distance") BigDecimal distance);
}