package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytUserPermissionGain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface TytUserPermissionGainMapper extends CustomBaseMapper<TytUserPermissionGain> {

    /**
     * 优先获取过期时间最早的数据.
     * @param userId userId
     * @param servicePermissionTypeId servicePermissionTypeId
     * @return TytUserPermissionGain
     */
    TytUserPermissionGain getFirstPermissionGain(@Param("dayStartTime") Date dayStartTime, @Param("userId") Long userId, @Param("servicePermissionTypeId") String servicePermissionTypeId);

    /**
     * 更新使用次数.
     * @param gainId gainId
     * @return int
     */
    int updatePermissionUseCount(@Param("gainId") Long gainId);

}