package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytUserIdentityLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytUserIdentityLabelMapper extends CustomBaseMapper<TytUserIdentityLabel> {
    TytUserIdentityLabel getUserGoodsType(@Param("userId") Long userId);
}