package com.tyt.plat.mapper.base;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 拨打反馈页面填写记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Mapper
public interface CallFeedbackLogMapper {

    // 根据srcMsgId和carUserId查询反馈记录
    @Select("select feedback_1 from tyt_call_feedback_log where car_user_id = #{carUserId} and src_msg_id = #{srcMsgId} order by id desc limit 1")
    String getLastFeedback(@Param("carUserId") Long carUserId, @Param("srcMsgId") Long srcMsgId);
}
