package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytMbCargoSyncInfo;
import com.tyt.transport.querybean.TransportYmmListBean;
import com.tyt.transport.querybean.TransportYmmListReqBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TytMbCargoSyncInfoMapper extends CustomBaseMapper<TytMbCargoSyncInfo> {

    @Select("SELECT " +
            "i.id as id, " +
            "i.create_time as createTime, " +
            "i.update_time as updateTime, " +
            "i.cargo_id as cargoId, " +
            "i.cargo_name as cargoName, " +
            "i.load_province_name as loadProvinceName, " +
            "i.load_city_name as loadCityName, " +
            "i.load_district_name as loadDistrictName, " +
            "i.load_detail_address as loadDetailAddress, " +
            "i.load_lat as loadLat, " +
            "i.load_lon as loadLon, " +
            "i.unload_province_name as unloadProvinceName, " +
            "i.unload_city_name as unloadCityName, " +
            "i.unload_district_name as unloadDistrictName, " +
            "i.unload_detail_address as unloadDetailAddress, " +
            "i.unload_lat as unloadLat, " +
            "i.unload_lon as unloadLon, " +
            "i.packing_types as packingTypes, " +
            "i.packing_type_desc as packingTypeDesc, " +
            "i.min_weight as minWeight, " +
            "i.max_weight as maxWeight, " +
            "i.truck_count as truckCount, " +
            "i.truck_lengths as truckLengths, " +
            "i.truck_types as truckTypes, " +
            "i.truck_user_types as truckUserTypes, " +
            "i.load_time as loadTime, " +
            "i.unload_time as unloadTime, " +
            "i.deposit_amt as depositAmt, " +
            "i.deposit_return as depositReturn, " +
            "i.expect_freight as expectFreight, " +
            "i.deal_mode as dealMode, " +
            "i.min_length as minLength, " +
            "i.max_length as maxLength, " +
            "i.min_width as minWidth, " +
            "i.max_width as maxWidth, " +
            "i.min_height as minHeight, " +
            "i.max_height as maxHeight, " +
            "i.contact_name as contactName, " +
            "i.contact_telephone as contactTelephone, " +
            "i.shipper_id as shipperId, " +
            "i.del_flag as delFlag, " +
            "i.last_operate_action as lastOperateAction, " +
            "i.start_point as startPoint, " +
            "i.dest_point as destPoint, " +
            "i.cargo_version as cargoVersion, " +
            "i.del_reason as delReason  " +
            "FROM " +
            " tyt_transport_mb_merge m " +
            " LEFT JOIN tyt_mb_cargo_sync_info i ON m.cargo_id = i.cargo_id  " +
            "WHERE " +
            "  m.src_msg_id = #{srcMsgId} " +
            " AND m.STATUS = 0  " +
            " AND i.del_flag = 0  " +
            " AND i.last_operate_action IN ( 0, 1 )  " +
            "ORDER BY " +
            " i.update_time DESC limit 1")
    TytMbCargoSyncInfo getTytMbCargoSyncInfo(@Param("srcMsgId") Long srcMsgId);

    TytMbCargoSyncInfo selectYMMCargoById(@Param("cargoId") Long cargoId);

    /**
     * 获取运满满货源待接单列表
     * @param req
     * @return
     */
    List<TransportYmmListBean> getTransportYmmList(@Param("req") TransportYmmListReqBean req);
}