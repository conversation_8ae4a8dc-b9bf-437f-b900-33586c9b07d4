package com.tyt.plat.mapper.base;


import com.tyt.plat.entity.base.TytExtendGoodsWhitelist;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * <p>
 * 扩展货源 白名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04
 */
public interface TytExtendGoodsWhitelistMapper extends BaseMapper<TytExtendGoodsWhitelist> {

    List<TytExtendGoodsWhitelist> getWhitelist(@Param("goodsStationId")Long receiverUserId);

}
