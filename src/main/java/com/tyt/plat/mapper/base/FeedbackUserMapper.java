package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.FeedbackUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface FeedbackUserMapper extends CustomBaseMapper<FeedbackUser> {

    List<FeedbackUser> selectList(@Param("postUserId") Long postUserId,
                                  @Param("postUserType") Integer postUserType,
                                  @Param("receiveUserId") Long receiveUserId,
                                  @Param("receiveUserType") Integer receiveUserType,
                                  @Param("beginTime") Date beginTime,
                                  @Param("delFlag") boolean delFlag,
                                  @Param("feedbackType") Integer feedbackType,
                                  @Param("labelId") Long labelId);

    /**
     * 统计数量
     *
     * @param postUserId      发表人id
     * @param postUserType    发表人类型
     * @param receiveUserId   接收人id
     * @param receiveUserType 接收人类型
     * @param feedbackType    评价类型
     * @param delFlag         是否删除
     * @param beginTime       起始时间
     * @return count
     */
    Long count(@Param("postUserId") Long postUserId, @Param("postUserType") Integer postUserType,
               @Param("receiveUserId") Long receiveUserId, @Param("receiveUserType") Integer receiveUserType,
               @Param("feedbackType") int feedbackType, @Param("delFlag") boolean delFlag,@Param("isValid") Integer isValid,
               @Param("beginTime") Date beginTime);
}