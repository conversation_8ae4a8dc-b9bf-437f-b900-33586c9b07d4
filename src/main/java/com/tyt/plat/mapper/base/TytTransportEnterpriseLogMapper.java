package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.entity.base.TytTransportEnterpriseLog;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TytTransportEnterpriseLogMapper extends CustomBaseMapper<TytTransportEnterpriseLog> {

    /**
     * 返回专票货源的开票主体ID
     */
    List<TytTransportEnterpriseLog> getInvoiceSubjectIdBySrcMsgIds(List<Long> srcMsgIds);

}