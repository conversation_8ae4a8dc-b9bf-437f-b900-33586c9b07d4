package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytDrawCommissionRule;
import com.tyt.transport.bean.CommissionTypeBean;
import com.tyt.transport.bean.DrawCommissionReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytDrawCommissionRuleMapper extends CustomBaseMapper<TytDrawCommissionRule> {
    /**
     * 查询符合条件的记录
     *
     * @param req
     * @return
     */
    TytDrawCommissionRule selectRule(@Param("req") DrawCommissionReq req);
}