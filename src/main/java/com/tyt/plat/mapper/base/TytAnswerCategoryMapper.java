package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytAnswerCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytAnswerCategoryMapper extends CustomBaseMapper<TytAnswerCategory> {

    /**
     * 获取分类列表.
     * @param belongType
     * @return
     */
    List<TytAnswerCategory> getCategoryList(@Param("belongType") Integer belongType);

    /**
     * 根据车货类型获取该类型下的所有分类id集合
     * @param belongType 所属类型 1:车；2:货
     * @return 分类集合
     */
    List<TytAnswerCategory> getCategoryListByBelongType(@Param("belongType") Integer belongType);

}