package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCoverGoodsDialUserUseLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytCoverGoodsDialUserUseLogMapper extends CustomBaseMapper<TytCoverGoodsDialUserUseLog> {

    /**
     * 根据userId获取用户使用抢单豆记录
     * @param userId
     * @return
     */
    List<TytCoverGoodsDialUserUseLog> selectUserUseLogByUserId(@Param("userId") Long userId);

    /**
     * 根据参数插入用户使用抢单豆记录表
     * @param userId 用户Id
     * @param tsId  货源Id
     * @param useReason 使用原因
     */
    void insertUserUseLogByParams(@Param("userId") Long userId,@Param("useReason") String useReason,@Param("tsId") Long tsId);

    Integer countByUserIdAndChangeType(@Param("userId") Long userId, @Param("changeType") int changeType);
}