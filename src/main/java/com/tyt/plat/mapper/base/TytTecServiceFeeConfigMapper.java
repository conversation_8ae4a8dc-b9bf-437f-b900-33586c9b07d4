package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTecServiceFeeConfig;
import com.tyt.plat.vo.TytTecServiceFeeConfigVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TytTecServiceFeeConfigMapper extends CustomBaseMapper<TytTecServiceFeeConfig> {

    TytTecServiceFeeConfig getByConditionsUseToCompute(TytTecServiceFeeConfig condition);

    Date getMatchConditionLastViewTime(@Param("srcMsgId") Long srcMsgId, @Param("freeTecServiceFeeViewCount") Integer freeTecServiceFeeViewCount, @Param("todayStartDate") Date todayStartDate, @Param("tomorrowStartDate") Date tomorrowStartDate);

    Date getMatchConditionLastCallTime(@Param("srcMsgId") Long srcMsgId, @Param("freeTecServiceFeeCallCount") Integer freeTecServiceFeeCallCount, @Param("todayStartDate") Date todayStartDate, @Param("tomorrowStartDate") Date tomorrowStartDate);


}