package com.tyt.plat.mapper.base;

import com.tyt.customservice.dto.CustomServiceRuleListReq;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCustomServiceRule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TytCustomServiceRuleMapper extends CustomBaseMapper<TytCustomServiceRule> {
    List<TytCustomServiceRule> selectList(CustomServiceRuleListReq customServiceRuleListReq);
}
