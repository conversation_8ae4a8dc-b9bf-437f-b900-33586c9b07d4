package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCustomServiceType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TytCustomServiceTypeMapper extends CustomBaseMapper<TytCustomServiceType> {
    /**
     * 查询所有未删除的客户服务分类信息
     * @return
     */
    List<TytCustomServiceType> selectAll();
}
