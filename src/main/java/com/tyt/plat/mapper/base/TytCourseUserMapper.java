package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCourseUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytCourseUserMapper extends CustomBaseMapper<TytCourseUser> {

    /**
     * 获取待学习数量.
     * @param type type
     * @return int
     */
    int getLearnInfoCount(@Param("userId") Long userId, @Param("type") Integer type, @Param("learnFlag") Integer learnFlag);

}