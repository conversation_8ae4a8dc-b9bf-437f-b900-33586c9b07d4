package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytUserRecord;
import com.tyt.plat.vo.other.GoodsAddressLevelRecordVo;
import com.tyt.plat.vo.other.GoodsSmallDO;
import com.tyt.plat.vo.other.IndividuationSettings;
import com.tyt.plat.vo.ts.TransportCarFindBIDataJson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface TytUserRecordMapper extends CustomBaseMapper<TytUserRecord> {
    TytUserRecord selectByUserIdAndCode(@Param("userId") Long userId, @Param("code") String code);

    /**
     * @description 根据userId及多code获取配置信息
     * @param userId 用户id
     * @param codes 业务code集合
     * @return java.util.List<com.tyt.plat.entity.base.TytUserRecord>
     * <AUTHOR>
     * @date 2023/8/31 15:22
     * @version 1.0
     */
    List<IndividuationSettings> selectIndividuationSettings(@Param("userId") Long userId, @Param("codes") List<String> codes);

    void saveFindTransportLogBIData(TransportCarFindBIDataJson transportCarFindBIDataJson);

    /**
     * 根据货源ID查询地址表信息
     * @param srcMsgId
     * @return
     */
    GoodsAddressLevelRecordVo getGoodsAddressLevelRecordByGoodsId(@Param("srcMsgId") Long srcMsgId);

    void insertIntoGoodsAddressLevelRecordGoodCarPriceTransportIsTrue(@Param("srcMsgId") Long srcMsgId,
                                                                      @Param("distanceKilometer") BigDecimal distanceKilometer,
                                                                      @Param("otherFee") BigDecimal otherFee);

    void updateGoodsAddressLevelRecordGoodCarPriceTransportIsTrueBySrcMsgId(@Param("srcMsgId") Long srcMsgId,
                                                                            @Param("distanceKilometer") BigDecimal distanceKilometer,
                                                                            @Param("otherFee") BigDecimal otherFee);

    Long selectGoodsAddressLevelRecordIdBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    List<Long> getGoodsSmallIdList(@Param("userId") Long userId);

    List<GoodsSmallDO> getByIdList(@Param("idList") List<Long> idList);


}