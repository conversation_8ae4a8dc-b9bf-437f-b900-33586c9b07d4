package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.entity.base.TytInvoiceEnterpriseLog;
import com.tyt.plat.entity.base.TytTransportEnterpriseLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytInvoiceEnterpriseMapper extends CustomBaseMapper<TytInvoiceEnterprise> {

    /**
     * 根据信用代码获取企业管理员
     * @param enterpriseCreditCode enterpriseCreditCode
     * @return TytInvoiceEnterprise
     */
    TytInvoiceEnterprise getManagerEnterprise(@Param("enterpriseCreditCode") String enterpriseCreditCode, @Param("excludeId") Long excludeId);

    TytInvoiceEnterprise selectByUserId(@Param("useId") Long useId,@Param("infoVerifyStatus") Integer infoVerifyStatus);

    TytInvoiceEnterprise selectByUserIdNoStatus(@Param("useId") Long useId);

    void deleteInvoiceTransportEnterpriseDataBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    void saveInvoiceTransportEnterpriseData(@Param("userEnterpriseData") TytInvoiceEnterpriseLog userEnterpriseData, @Param("srcMsgId") Long srcMsgId);

    TytInvoiceEnterprise getEnterpriseByCode(@Param("enterpriseCreditCode") String enterpriseCreditCode);

    Long getByTransportUserId(@Param("goodsUserId") Long goodsUserId);

    TytTransportEnterpriseLog getInvoiceTransportEnterpriseLogBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

}