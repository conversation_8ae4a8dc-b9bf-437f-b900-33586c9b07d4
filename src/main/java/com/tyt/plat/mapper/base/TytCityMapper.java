package com.tyt.plat.mapper.base;

import com.tyt.infofee.bean.TytCityDto;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytCityMapper extends CustomBaseMapper<TytCity> {

    /**
     * 根据名称获取
     * @param cityName
     * @param areaName
     * @return
     */
    TytCity getCityByName(@Param("cityName") String cityName, @Param("areaName") String areaName);

    /**
     * 根据大地坐标系
     * @param px
     * @param py
     * @return
     */
    TytCity getCityByPxPy(@Param("px") String px, @Param("py") String py);

    /**
     * 根据省、市、区 查询
     * @param province 省级名称
     * @param city 市级名称
     * @param area 地区名称
     * @return
     */
    TytCity selectByAddress(@Param("province") String province, @Param("city") String city, @Param("area") String area);

    /**
     * 根据标准城市名返回简短的城市名
     */
    TytCity getShortCityName(String cityName);

    /**
     * 根据条件查询城市
     *
     * @param tytCityDto 省市区实体类
     * @return
     */
    TytCity getCityDataByCondition(@Param("tytCityDto") TytCityDto tytCityDto);

    List<TytCity> getCity(String city);
}
