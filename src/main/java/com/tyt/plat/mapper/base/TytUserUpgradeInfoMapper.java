package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytUserUpgradeInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytUserUpgradeInfoMapper extends CustomBaseMapper<TytUserUpgradeInfo> {
    TytUserUpgradeInfo getByUserIdAndUpgradeId(@Param("userId") Long userId, @Param("upgradeId") Long upgradeId);

    List<TytUserUpgradeInfo> getByUser(@Param("userId") Long userId);

    void updateStatusById(@Param("userVersion") String userVersion, @Param("id") Long id);
}