package com.tyt.plat.mapper.base;

import com.tyt.model.TransportMain;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description main表 mapper
 * @date 2023-08-16-14-39-43
 */
@Mapper
public interface TytTransportMainMapper extends CustomBaseMapper<TytTransportVO> {

    TytTransportVO selectTransportById(Long srcMsgId);

    /**
     * 用户最近30天是否发货（不包含今天）
     *
     * @param userId 发货人
     * @return 返回最后一单的发布时间
     */
    Date hasUserTransportLast30Day(Long userId);

    /**
     * 获取用户某天发货信息
     *
     * @param userId  发货人
     * @param someDay yyyy-MM-dd
     */
    List<TytTransportVO> getUserSomeDayTransportData(@Param("userId") Long userId,
                                                      @Param("someDay") String someDay);

    /**
     * 查询是否拨打过，有一个货源拨打过就返回1
     */
    Integer hasContactInSrcMsgIds(@Param("srcMsgIds") List<Long> srcMsgIds);


    TransportMain getFirstTransport(@Param("userId") Long userId);

    List<Long> getInReleaseTransportIdList(@Param("transportUserId") Long transportUserId, @Param("todayStartDate") Date todayStartDate);

    Integer getSameTransportInPublishCount(@Param("srcMsgId") Long srcMsgId, @Param("similarityCode") String similarityCode, @Param("today") String today);

    Integer selectDistanceBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    Integer getSameStartCityAndDestCityTransportInPublish(@Param("srcMsgId") Long srcMsgId, @Param("startCity") String startCity, @Param("destCity") String destCity, @Param("today") String today);

    Integer countByGoodTypeName(@Param("srcMsgIds") List<Long> srcMsgIds, @Param("goodTypeName") String goodTypeName);

    List<Long> getUserPublishingSrcMsgIds(@Param("userId") Long userId);
}
