package com.tyt.plat.mapper.base;

import com.tyt.agreement.bean.AgreementListBean;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytAgreement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytAgreementMapper extends CustomBaseMapper<TytAgreement> {

    List<AgreementListBean> selectAgreementList(@Param("showPort") Integer showPort,@Param("tabType") Integer tabType);
}