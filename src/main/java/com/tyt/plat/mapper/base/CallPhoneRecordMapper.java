package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.CallPhoneRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CallPhoneRecordMapper extends CustomBaseMapper<CallPhoneRecordDO> {
    CallPhoneRecordDO getLatestCallRecord(@Param("srcMsgIds") List<Long> srcMsgIds);
}