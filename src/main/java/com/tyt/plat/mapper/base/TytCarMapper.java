package com.tyt.plat.mapper.base;

import com.tyt.model.Car;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCarVO;
import com.tyt.plat.entity.base.TytSigningCar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytCarMapper extends CustomBaseMapper<TytCarVO> {

    TytCarVO queryMasterDriver(Long carId);

    String getByCarId(Long carId);

    Long selectCarIdByUserIdAndHeadCityNo(@Param("userId") Long userId, @Param("headCity") String headCity, @Param("headNo") String headNo, @Param("tailCity") String tailCity, @Param("tailNo") String tailNo);

}
