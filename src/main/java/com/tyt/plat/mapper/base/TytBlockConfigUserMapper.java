package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytBlockConfigUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytBlockConfigUserMapper extends CustomBaseMapper<TytBlockConfigUser> {
    TytBlockConfigUser getByIdAndUserId(@Param("userId") Long userId,@Param("id") Long id);
}