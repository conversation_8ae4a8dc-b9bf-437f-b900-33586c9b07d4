package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytExcellentPriceConfigUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytExcellentPriceConfigUserMapper extends CustomBaseMapper<TytExcellentPriceConfigUser> {
    /**
     * 查询该配置下用户信息
     *
     * @param configId
     * @param userId
     * @return
     */
    TytExcellentPriceConfigUser getByConfigIdAndUserId(@Param("configId") Long configId, @Param("userId") Long userId);
}