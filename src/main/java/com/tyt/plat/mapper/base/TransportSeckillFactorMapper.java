package com.tyt.plat.mapper.base;

import com.tyt.plat.entity.base.TransportSeckillFactorDO;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * <p>
 * 秒抢货源命中条件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Mapper
public interface TransportSeckillFactorMapper extends BaseMapper<TransportSeckillFactorDO> {

    // 查询所有目的地城市
    List<String> selectAllDestCity();

    // 检查目的地城市是否存在
    int existDestCity(String destCity);
}
