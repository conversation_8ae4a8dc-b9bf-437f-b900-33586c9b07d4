package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCourseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytCourseInfoMapper extends CustomBaseMapper<TytCourseInfo> {

    /**
     * 获取课程列表
     * @param type
     * @param sortNumber
     * @return
     */
    List<TytCourseInfo> getCourseList(@Param("type") Integer type,
                                      @Param("categoryId") Long categoryId,
                                      @Param("sortNumber") Integer sortNumber);

    /**
     * 查询我的课程列表.
     * @param userId userId
     * @param type type
     * @param learnFlag learnFlag
     * @param sortNumber sortNumber
     * @return List
     */
    List<TytCourseInfo> getMyCourseList(@Param("userId") Long userId, @Param("type") Integer type,
                                        @Param("learnFlag") Integer learnFlag, @Param("sortNumber") Integer sortNumber);

}