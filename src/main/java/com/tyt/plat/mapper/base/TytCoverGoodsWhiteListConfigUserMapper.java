package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCoverGoodsWhiteListConfigUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytCoverGoodsWhiteListConfigUserMapper extends CustomBaseMapper<TytCoverGoodsWhiteListConfigUser> {


    /**
     * 根据用户Id查看该用户目前是否处于捂货白名单
     * @param userId 用户Id
     * @return Long
     */
    Long countByUserId(@Param("userId") Long userId);
}