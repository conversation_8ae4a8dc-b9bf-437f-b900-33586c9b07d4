package com.tyt.plat.mapper.base;

import com.tyt.plat.biz.repay.pojo.RepayListReq;
import com.tyt.plat.biz.repay.pojo.RepayListResp;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TransportOrdersExRepay;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TransportOrdersExRepayMapper extends CustomBaseMapper<TransportOrdersExRepay> {

    List<RepayListResp> myRepayList(@Param("req") RepayListReq req);

}