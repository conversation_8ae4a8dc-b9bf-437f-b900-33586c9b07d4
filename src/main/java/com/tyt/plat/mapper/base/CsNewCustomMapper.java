package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.CsNewCustom;
import com.tyt.plat.vo.invoice.MaintainedUserInfoDto;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CsNewCustomMapper extends CustomBaseMapper<CsNewCustom> {
    MaintainedUserInfoDto checkMaintainedUser(String phone);

    CsNewCustom getNewCustomByCellPhone(String phone);

    MaintainedUserInfoDto getByBindId(Long userId);
}