package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportDictionary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytTransportDictionaryMapper extends CustomBaseMapper<TytTransportDictionary> {

    /**
     * 查询是否有更新数据
     *
     * @param modifyTime
     * @return
     */
    Long checkDictionaryChange(@Param("modifyTime") String modifyTime);

    /**
     * 分页获取字典列表
     *
     * @param maxId
     * @param pageSize
     * @return
     */
    List<TytTransportDictionary> getPageDictionaryList(@Param("maxId") Long maxId, @Param("pageSize") Integer pageSize);


    /**
     * 获取分词列表
     *
     * @return
     */
    List<TytTransportDictionary> selectAll();

}