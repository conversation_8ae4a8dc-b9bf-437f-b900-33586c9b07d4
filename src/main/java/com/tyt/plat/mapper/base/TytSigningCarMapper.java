package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytSigningCar;
import com.tyt.plat.entity.base.TytSigningCarVO;
import com.tyt.plat.vo.other.SigningCarInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytSigningCarMapper extends CustomBaseMapper<TytSigningCar> {
    TytSigningCar getByUserId(Long userId);

    void updateCar(@Param("userId") Long userId,@Param("status") Integer status);

    List<TytSigningCarVO> getUserList(@Param("listIds")  List<String> listIds);

    List<SigningCarInfoVo> getAssignableCars(@Param("cities") List<String> cities);

    List<Long> getSigningCarBlack(Long userId);


    Integer getByUserNoIds(@Param("ids") List<Long> ids);

    void updateDriverAssignNum(@Param("carInfoId") Long carInfoId);

    List<SigningCarInfoVo> getAssignableCarsByRoute(@Param("startCity") String startCity,
                                                    @Param("destCity") String destCity);

    List<SigningCarInfoVo> getAssignableCarsByCity(@Param("cities") List<String> cities);

    List<SigningCarInfoVo> getAssignableCarsByProvince(@Param("provinces") List<String> provinces);

    List<SigningCarInfoVo> getAssignableCarsByCountry(@Param("start") Integer start,
                                                      @Param("pageSize") Integer pageSize);

    Integer checkDriverBlackStatus(Long userId);
}