package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.MybatisTytTransportOrders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Mapper
public interface TytTransportOrdersMapper extends CustomBaseMapper<MybatisTytTransportOrders> {
    @Deprecated
    List<MybatisTytTransportOrders> getUnPostFeedbackOrders(@Param("userId") Long userId, @Param("payUserId") Long payUserId, @Param("beginTime") Date beginTime);

    List<MybatisTytTransportOrders> getUnPostFeedbackOrdersNew(@Param("userId") Long userId, @Param("payUserId") Long payUserId, @Param("beginTime") Date beginTime,@Param("driverIdList") List<Long> driverIdList);

    List<MybatisTytTransportOrders> getOrdersByIdList(@Param("idList")List<Long> idList);

    Integer firstHonourAnAgreement(@Param("userId") Long userId);

    /** 获取该用户近一年来 待装货、待收货、待支付（待付运费）的未冻结的运费总额
     *
     * @param userId userId
     * @param startDate startDate
     * @return 运费总额
     */
    Integer getAllNoFreezeOrdersPriceCount(@Param("userId") Long userId, @Param("startDate") Date startDate);

    int getPayUserIdCarIdCTime(@Param("userId") Long userId,@Param("carId") Long carId,@Param("openTime") Date openTime,@Param("endTime") Date date);
}