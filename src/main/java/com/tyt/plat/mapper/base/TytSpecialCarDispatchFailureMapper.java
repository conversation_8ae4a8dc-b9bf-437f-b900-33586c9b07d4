package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytSpecialCarDispatchFailure;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface TytSpecialCarDispatchFailureMapper extends CustomBaseMapper<TytSpecialCarDispatchFailure> {
    TytSpecialCarDispatchFailure selectBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    void updateWorkOrderStatus(@Param("srcMsgId") Long srcMsgId,
                               @Param("workOrderStatus") Integer workOrderStatus,
                               @Param("endTime") Date endTime);


    void updateDeclareInPublicToNo(@Param("srcMsgId") Long srcMsgId);
}