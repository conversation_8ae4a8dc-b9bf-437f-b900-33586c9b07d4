package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytDispatchCooperative;
import com.tyt.transport.vo.CargoOwnerInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytDispatchCooperativeMapper extends CustomBaseMapper<TytDispatchCooperative> {
    TytDispatchCooperative selectByName(@Param("name") String name);

    List<CargoOwnerInfoVo> selectListByName(@Param("name") String name);

}