package com.tyt.plat.mapper.base;

import com.tyt.infofee.bean.MyComplaintListBean;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportWaybillEx;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytTransportWaybillExMapper extends CustomBaseMapper<TytTransportWaybillEx> {

    List<MyComplaintListBean> getMyComplaintList(@Param("userId") Long userId,
            @Param("payUserId") Long payUserId);
}
