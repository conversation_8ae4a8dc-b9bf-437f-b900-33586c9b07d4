package com.tyt.plat.mapper.base;

import com.tyt.plat.entity.base.CoverGoodsLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 捂货记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Mapper
public interface CoverGoodsLogMapper extends BaseMapper<CoverGoodsLogDO> {

    /**
     * 根据用户id和货源id查询
     */
    CoverGoodsLogDO getByUserAndTsId(@Param("userId") Long userId, @Param("tsId") Long tsId);

    /**
     * 统计今天该用户的被捂货次数，不统计好货
     */
    int countTodayCoverTimesWithoutGoodGoods(@Param("userId") Long userId);

    /**
     * 获取用户被捂货的货源id
     */
    Set<Long> getCoverGoodsIds(@Param("userId") Long userId, @Param("srcMsgIds") List<Long> srcMsgIds);
}
