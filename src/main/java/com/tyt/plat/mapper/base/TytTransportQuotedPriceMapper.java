package com.tyt.plat.mapper.base;

import com.tyt.plat.vo.remote.TytQuotedPriceBiData;
import com.tyt.plat.vo.ts.CallLogQuery;
import com.tyt.transport.querybean.CallLogBean;
import com.tyt.transportquotedprice.bean.TytTransportQuotedPrice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface TytTransportQuotedPriceMapper {

    TytTransportQuotedPrice getQuotedPriceByCarUserIdAndTransportMainId(@Param("carUserId") Long carUserId, @Param("srcMsgId") Long srcMsgId);

    TytTransportQuotedPrice getQuotedPriceById(@Param("transportQuotedPriceId") Long transportQuotedPriceId);

    List<TytTransportQuotedPrice> getQuotedPriceListByTransportMainId(@Param("srcMsgId") Long srcMsgId);

    List<TytTransportQuotedPrice> getQuotedPriceListByTransportMainIdOrderByCarQuotedPriceTime(@Param("srcMsgId") Long srcMsgId);

    Long firstCarQuotedPrice(@Param("carUserId") Long carUserId, @Param("carUserName") String carUserName
            , @Param("transportUserId") Long transportUserId, @Param("transportUserName") String transportUserName
            , @Param("srcMsgId") Long srcMsgId, @Param("price") Integer price, @Param("reason") String reason);

    Long firstCarQuotedPriceV2(TytTransportQuotedPrice quotedPrice);

    void subsequentCarQuotedPrice(@Param("carUserId") Long carUserId, @Param("carUserName") String carUserName
            , @Param("srcMsgId") Long srcMsgId, @Param("price") Integer price, @Param("reason") String reason);

    void transportQuotedPrice(@Param("transportQuotedPriceId") Long transportQuotedPriceId, @Param("price") Integer price);

    void carAgree(@Param("carUserId") Long carUserId, @Param("srcMsgId") Long srcMsgId);

    void transportAgree(@Param("transportQuotedPriceId") Long transportQuotedPriceId, @Param("priceAssistantAutoAgree") Integer priceAssistantAutoAgree);

    List<Long> getTransportHaveAnyQuotedPrice(@Param("transportUserId") Long transportUserId, @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    Integer getCountTransportMainIsValidForIdList(@Param("srcMsgIdList") List<Long> srcMsgIdList
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    List<Long> getTransportMainIsValidSrcMsgIdsForIdList(@Param("srcMsgIdList") List<Long> srcMsgIdList
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    List<Long> getCarHaveNewTransportQuotedPrice(@Param("carUserId") Long carUserId
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    Date getCarHaveNewTransportQuotedPriceLastTime(@Param("carUserId") Long carUserId, @Param("srcMsgIdList") List<Long> srcMsgIdList
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    List<Long> getCarHaveAgreeTransportQuotedPrice(@Param("carUserId") Long carUserId
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    Date getCarHaveAgreeTransportQuotedPriceLastTime(@Param("carUserId") Long carUserId, @Param("srcMsgIdList") List<Long> srcMsgIdList
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);


    Integer getCarAndThisTransportIsHaveNewTransportQuotedPrice(@Param("srcMsgId") Long srcMsgId, @Param("carUserId") Long carUserId
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    Integer getCarAndThisTransportIsHaveAgreeTransportQuotedPrice(@Param("srcMsgId") Long srcMsgId, @Param("carUserId") Long carUserId
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    Integer getTransportQuotedPriceCountBySrcMsgIs(@Param("srcMsgId") Long srcMsgId
            , @Param("todayBeginTime") Date todayBeginTime, @Param("todayEndTime") Date todayEndTime);

    List<CallLogBean> getQuotedPriceListByCarId(CallLogQuery callLogQuery);

    /**
     * 报价保存BI所需数据
     * @param tytQuotedPriceBiData
     */
    void addTransportQuotedPriceBIDataLog(TytQuotedPriceBiData tytQuotedPriceBiData);

    Integer getCarIsHaveQuotedPriceToTransport(@Param("carUserId") Long carUserId, @Param("srcMsgId") Long srcMsgId);

    List<Long> getSrcMsgIdListByTransportUserIdAndDate(@Param("transportUserId") Long transportUserId, @Param("startDate") Date startDate);

    List<TytTransportQuotedPrice> getAllQuotedPriceListBySrcMsgIdList(@Param("srcMsgIds") List<Long> inReleaseTransportIdList);

    int getTransportHaveOptionQuotedPriceCount(@Param("srcMsgId") Long srcMsgId);

    TytTransportQuotedPrice getQuotedPriceLastOneBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    int countSystemQuotedPrice(@Param("srcMsgId") Long srcMsgId);

    TytTransportQuotedPrice getLatestQuotedRecord(@Param("srcMsgIds") List<Long> srcMsgIds);

    void transportQuotedPriceModifyPriceLog(@Param("transportQuotedPriceId") Long transportQuotedPriceId, @Param("srcMsgId") Long srcMsgId
            , @Param("befortPrice") BigDecimal befortPrice, @Param("afterPrice") BigDecimal afterPrice);
}