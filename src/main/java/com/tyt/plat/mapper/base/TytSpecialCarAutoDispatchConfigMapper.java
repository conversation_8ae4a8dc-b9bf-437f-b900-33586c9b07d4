package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytSpecialCarAutoDispatchConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytSpecialCarAutoDispatchConfigMapper extends CustomBaseMapper<TytSpecialCarAutoDispatchConfig> {
    TytSpecialCarAutoDispatchConfig selectDispatchConfigByRoute(@Param("startCity") String startCity, @Param("destCity") String destCity);
}