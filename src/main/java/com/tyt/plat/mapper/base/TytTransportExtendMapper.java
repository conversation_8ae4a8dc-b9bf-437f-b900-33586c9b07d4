package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportExtend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

@Mapper
public interface TytTransportExtendMapper extends CustomBaseMapper<TytTransportExtend> {
    /**
     * 根据货源表id查询扩展信息
     *
     * @param tsId
     * @return
     */
    TytTransportExtend selectByTsId(@Param("tsId") Long tsId);

}