package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportSyncYmm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytTransportSyncYmmMapper extends CustomBaseMapper<TytTransportSyncYmm> {
    TytTransportSyncYmm selectTytTransportSyncYmmBySrcId(@Param("srcMsgId") Long srcMsgId);
}