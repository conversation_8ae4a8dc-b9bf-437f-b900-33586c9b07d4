package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.CsComplaintRecordResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CsComplaintRecordResultMapper extends CustomBaseMapper<CsComplaintRecordResult> {

    int selectCountByReason(@Param("cellPhone") String cellPhone);
}