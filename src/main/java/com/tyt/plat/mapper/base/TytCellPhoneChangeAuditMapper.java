package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCellPhoneChangeAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytCellPhoneChangeAuditMapper extends CustomBaseMapper<TytCellPhoneChangeAudit> {
    /**
     * @description 根据用户id及审核状态查询
     * <AUTHOR>
     * @date 2023/9/5 10:54
     * @version 1.0
     * @param userId 用户id
     * @param auditStatus 审核状态 1：待审核 2：审核通过 3：驳回'
     * @return com.tyt.plat.entity.base.TytCellPhoneChangeAudit
     */
    TytCellPhoneChangeAudit selectByUserIdAndAuditStatus(@Param("userId") Long userId,@Param("auditStatus") Integer auditStatus);
}