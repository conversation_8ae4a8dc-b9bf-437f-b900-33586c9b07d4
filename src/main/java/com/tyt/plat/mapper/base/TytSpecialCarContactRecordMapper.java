package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytSpecialCarContactRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytSpecialCarContactRecordMapper extends CustomBaseMapper<TytSpecialCarContactRecord> {
    int countBySrcMsgId(@Param("srcMsgId") Long srcMsgId);
}