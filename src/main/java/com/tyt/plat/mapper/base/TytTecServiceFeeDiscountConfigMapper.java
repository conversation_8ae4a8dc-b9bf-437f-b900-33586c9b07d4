package com.tyt.plat.mapper.base;

import com.tyt.plat.entity.base.TytTecServiceFeeDiscountConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface TytTecServiceFeeDiscountConfigMapper {

    List<TytTecServiceFeeDiscountConfig> getTytTecServiceFeeDiscountConfigByProportionId(@Param("proportionId") Long ProportionId);

    TytTecServiceFeeDiscountConfig getTytTecServiceFeeDiscountConfigByProportionIdAndMin(@Param("proportionId") Long ProportionId, @Param("matchConditionEarliestTimeBetweenNowMin") Long matchConditionEarliestTimeBetweenNowMin);

}