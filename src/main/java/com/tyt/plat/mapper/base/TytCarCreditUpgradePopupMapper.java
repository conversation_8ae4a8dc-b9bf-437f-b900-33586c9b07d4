package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCarCreditUpgradePopup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytCarCreditUpgradePopupMapper extends CustomBaseMapper<TytCarCreditUpgradePopup> {
    TytCarCreditUpgradePopup selectByUserIdPopupType(@Param("userId") Long userId,@Param("popupType") Integer popupType);
}