package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytActivateConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytActivateConfigMapper extends CustomBaseMapper<TytActivateConfig> {
    TytActivateConfig getActivateConfig();

    Integer getBlackStatus(@Param("userId") Long userId);

}