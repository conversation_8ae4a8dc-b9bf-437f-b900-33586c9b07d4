package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.BlacklistIdentityCard;
import com.tyt.plat.entity.base.BlacklistUserOrders;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface BlackListIdentityCardMapper extends CustomBaseMapper<BlacklistIdentityCard> {

    BlacklistIdentityCard getByIdCard(@Param("idCard")String idCard);

}