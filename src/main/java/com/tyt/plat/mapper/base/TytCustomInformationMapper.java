package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCustomInformation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/8 11:07
 */
@Mapper
public interface TytCustomInformationMapper extends CustomBaseMapper<TytCustomInformation> {

    /**
     * @return TytCustomInformation
     * @description 根据手机号查询客户信息
     * @date 2023/9/8 14:23
     * @Param goodsPhone:
     */
    TytCustomInformation selectByGoodsPhone(@Param("goodsPhone") String goodsPhone);
}