package com.tyt.plat.mapper.base;

import com.tyt.invoicetransport.bean.TytInvoiceTransportPriceConfig;
import com.tyt.plat.entity.base.TytInvoiceTransportConfigLog;
import com.tyt.plat.entity.base.TytInvoiceTransportEnterpriseConfigLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytInvoiceTransportConfigLogMapper {

    TytInvoiceTransportConfigLog getLastTytInvoiceTransportConfig();

    Integer isHaveLastTytInvoiceTransportEnterpriseConfig(@Param("enterpriseId") Long enterpriseId);

    List<TytInvoiceTransportPriceConfig> getTytInvoiceTransportPriceConfig();

    TytInvoiceTransportEnterpriseConfigLog getLastTytInvoiceTransportEnterpriseConfig(@Param("enterpriseId") Long enterpriseId);

    List<TytInvoiceTransportPriceConfig> getTytInvoiceTransportPriceEnterpriseConfig(@Param("enterpriseId") Long enterpriseId);


    TytInvoiceTransportConfigLog getLastInvoiceConfigByServiceType(@Param("invoiceServiceType") Integer invoiceServiceType);
}