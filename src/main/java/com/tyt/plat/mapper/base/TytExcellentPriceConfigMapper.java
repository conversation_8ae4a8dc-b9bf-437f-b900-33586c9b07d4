package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytExcellentPriceConfig;
import com.tyt.transport.querybean.TransportCarryBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytExcellentPriceConfigMapper extends CustomBaseMapper<TytExcellentPriceConfig> {
    /**
     * 根据条件获取优车2.0配置
     *
     * @param transportCarryBean
     * @return
     */

    List<TytExcellentPriceConfig> getConfig(@Param("req") TransportCarryBean transportCarryBean);
}