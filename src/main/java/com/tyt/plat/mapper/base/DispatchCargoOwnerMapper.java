package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.DispatchCargoOwner;
import com.tyt.transport.vo.CargoOwnerInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 专车货主管理
 */
@Mapper
public interface DispatchCargoOwnerMapper extends CustomBaseMapper<DispatchCargoOwner> {
    /**
     * 根据用户ID查询是签约合作商的记录
     * @param ownerUserId
     * @return
     */
    DispatchCargoOwner selectSignedByUserId(@Param("ownerUserId") Long ownerUserId);

    DispatchCargoOwner selectByUserId(@Param("ownerUserId") Long ownerUserId);

    /**
     * 根据货主名称查询货主信息
     * @param platCargoOwnerName
     * @return
     */
    DispatchCargoOwner selectByOwnerName(@Param("platCargoOwnerName") String platCargoOwnerName);

    List<CargoOwnerInfoVo> selectCargoOwnerList(@Param("cargoOwnerName") String cargoOwnerName);

    DispatchCargoOwner selectByCooperativeId(@Param("cooperativeId") Long cooperativeId);

    Integer selectRuleCount(@Param("ownerUserId") Long ownerUserId);

}