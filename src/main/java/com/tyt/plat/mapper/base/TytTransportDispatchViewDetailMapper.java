package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportDispatchViewDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytTransportDispatchViewDetailMapper extends CustomBaseMapper<TytTransportDispatchViewDetail> {
    List<Long> queryViewCarUser(@Param("srcMsgId") Long srcMsgId);
}