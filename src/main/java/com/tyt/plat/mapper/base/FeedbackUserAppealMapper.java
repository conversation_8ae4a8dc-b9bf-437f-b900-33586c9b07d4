package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.FeedbackUserAppeal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FeedbackUserAppealMapper extends CustomBaseMapper<FeedbackUserAppeal> {

    FeedbackUserAppeal getAppealByFeedbackId(@Param("feedBackId") Long feedBackId);

    List<FeedbackUserAppeal> getAppealByFeedBackIdList(@Param("feedBackIdList") List<Long> feedBackIdList);
}