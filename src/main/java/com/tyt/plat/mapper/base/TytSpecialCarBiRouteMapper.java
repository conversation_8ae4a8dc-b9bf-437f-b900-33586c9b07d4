package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytSpecialCarBiRoute;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface TytSpecialCarBiRouteMapper extends CustomBaseMapper<TytSpecialCarBiRoute> {
    int countByRoute(@Param("startCity") String startCity, @Param("destCity") String destCity);
}