package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.AccountUpdateLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AccountUpdateLogMapper extends CustomBaseMapper<AccountUpdateLog> {
    /**
     * @description 统计某个用户某时间段的变更条数
     * @param userId
     * @param startTime
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2023/9/5 11:50
     * @version 1.0
     */
    Integer countByTimePeriod(@Param("userId") Long userId, @Param("startTime") String startTime);

    /**
     * @description 查询某个用户最近的一次修改记录
     * <AUTHOR>
     * @date 2023/9/5 13:09
     * @version 1.0
     * @param userId
     * @return com.tyt.plat.entity.base.AccountUpdateLog
     */
    AccountUpdateLog selectRecentAccountUpdateLog(@Param("userId") Long userId);

    void updateIdentityAuthMobile(@Param("userId") Long userId, @Param("mobile") String mobile);

    void updateBlacklistUser(@Param("userId") Long userId, @Param("newPhone") String newPhone, @Param("oldPhone") String oldPhone);

    void updateUserCellphoneInfo(@Param("userId") Long userId,@Param("cellPhone") String cellPhone,@Param("ticket") String ticket);
}
