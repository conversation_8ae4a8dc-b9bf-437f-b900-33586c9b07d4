package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytKeywordMatchesNewUnstandard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytKeywordMatchesNewUnstandardMapper extends CustomBaseMapper<TytKeywordMatchesNewUnstandard> {

    /**
     * 只返回一个对应的分词
     * @param wordList
     * @return
     */
    TytKeywordMatchesNewUnstandard getOneKeywordMatch(@Param("wordList") List<String> wordList);

}