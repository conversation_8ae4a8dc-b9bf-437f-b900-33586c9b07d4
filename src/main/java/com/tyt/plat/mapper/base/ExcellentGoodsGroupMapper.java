package com.tyt.plat.mapper.base;

import com.tyt.deposit.bean.ExcellentGoodsGroupDetailBean;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.ExcellentGoodsGroup;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ExcellentGoodsGroupMapper extends CustomBaseMapper<ExcellentGoodsGroup> {
    /**
     * 查询优车发货剩余次数
     * @param userId
     * @return
     */
    List<ExcellentGoodsGroupDetailBean> selectUserGroupInfo(Long userId);
}