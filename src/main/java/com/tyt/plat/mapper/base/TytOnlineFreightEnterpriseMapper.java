package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytOnlineFreightEnterprise;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytOnlineFreightEnterpriseMapper extends CustomBaseMapper<TytOnlineFreightEnterprise> {

    /**
     * 根据企业名称获取网络货运企业信息
     * @param enterpriseName
     * @return
     */
    TytOnlineFreightEnterprise getOnlineFreightEnterpriseByName(@Param("enterpriseName") String enterpriseName);

    TytOnlineFreightEnterprise getOnlineFreightEnterpriseByEnterpriseCreditCode(@Param("enterpriseCreditCode") String enterpriseCreditCode);

}