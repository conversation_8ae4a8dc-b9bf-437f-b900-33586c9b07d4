package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.FeedbackLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FeedbackLabelMapper extends CustomBaseMapper<FeedbackLabel> {

    /**
     * 查询数量
     *
     * @param labelType    标签类型
     * @param feedbackType 标签类别
     * @param labelIds     标签id
     * @return 数量
     */
    Long count(@Param("labelType") Integer labelType,
               @Param("feedbackType") Integer feedbackType,
               @Param("labelIds") List<Long> labelIds);

    List<FeedbackLabel> selectList(@Param("labelType") Integer labelType,
                                   @Param("feedbackType") Integer feedbackType,
                                   @Param("enableStatus") boolean enableStatus);
}