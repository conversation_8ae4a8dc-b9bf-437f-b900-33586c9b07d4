package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytAbtestConfig;
import com.tyt.plat.entity.base.TytCustomServiceType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TytAbtestConfigMapper extends CustomBaseMapper<TytAbtestConfig> {
    /**
     * 查询所有可用的配置
     *
     * @return
     */
    List<TytAbtestConfig> selectAllConfig();

}