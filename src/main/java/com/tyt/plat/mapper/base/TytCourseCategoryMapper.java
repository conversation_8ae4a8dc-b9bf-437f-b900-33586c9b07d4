package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCourseCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytCourseCategoryMapper extends CustomBaseMapper<TytCourseCategory> {

    /**
     * 获取分类列表.
     * @param type type
     * @return List
     */
    List<TytCourseCategory> getCategoryList(@Param("type") Integer type);

}