package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytCourseBanner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytCourseBannerMapper extends CustomBaseMapper<TytCourseBanner> {

    /**
     * 轮播图列表
     * @param type
     * @return
     */
    List<TytCourseBanner> getBannerList(@Param("type") Integer type);

}