package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytSpecialCarPriceConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface TytSpecialCarPriceConfigMapper extends CustomBaseMapper<TytSpecialCarPriceConfig> {

    TytSpecialCarPriceConfig selectMatchPriceConfig(@Param("cargoOwnerId") Long id,
                                                    @Param("startCity") String startCity,
                                                    @Param("destCity") String destCity,
                                                    @Param("weight") BigDecimal weight);

    List<TytSpecialCarPriceConfig> getConfigStatus();

    int countByOwnerAndRoute(@Param("cargoOwnerId") Long id,
                             @Param("startCity") String startCity,
                             @Param("destCity") String destCity);


    List<TytSpecialCarPriceConfig> selectByCities(@Param("startCity") String startCity, @Param("destCity") String destCity);

    int countMatchPriceConfigCityAndRule(@Param("startCity") String startCity,
                                         @Param("destCity") String destCity);

}