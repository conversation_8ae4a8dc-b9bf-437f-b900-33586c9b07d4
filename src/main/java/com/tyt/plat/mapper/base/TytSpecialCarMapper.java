package com.tyt.plat.mapper.base;

import com.tyt.plat.biz.invoice.pojo.DriverListVO;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytSpecialCar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytSpecialCarMapper extends CustomBaseMapper<TytSpecialCar> {
    List<TytSpecialCar> getList( @Param("userId") Long userId,@Param("page")Integer page,@Param("pageSize") Integer pageSize);

    Integer getInfo(@Param("userId") Long userId,@Param("driverId") Long driverId,@Param("driverUserId")  Long driverUserId);

    Integer getTotal(@Param("userId") Long userId);
}