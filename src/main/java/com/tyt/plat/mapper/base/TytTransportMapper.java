
package com.tyt.plat.mapper.base;

import com.tyt.model.Transport;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytTransportVO;
import com.tyt.transport.querybean.SimilarTransportSearchBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description main表 mapper
 * @date 2023-08-16-14-39-43
 */
@Mapper
public interface TytTransportMapper extends CustomBaseMapper<TytTransportVO> {

    TytTransportVO selectTransportById(Long srcMsgId);

    List<Transport> getSimilarityList(SimilarTransportSearchBean searchBean);

    Long getTsIdBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

}
