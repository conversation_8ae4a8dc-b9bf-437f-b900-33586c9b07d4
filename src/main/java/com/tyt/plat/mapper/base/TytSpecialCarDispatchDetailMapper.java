package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytSpecialCarDispatchDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytSpecialCarDispatchDetailMapper extends CustomBaseMapper<TytSpecialCarDispatchDetail> {

    Integer selectCountByUserAndGoodsId(@Param("goodsId") Long goodsId,@Param("userId") Long userId);

    Integer selectCountByGoodsId(@Param("goodsId") Long goodsId);

}