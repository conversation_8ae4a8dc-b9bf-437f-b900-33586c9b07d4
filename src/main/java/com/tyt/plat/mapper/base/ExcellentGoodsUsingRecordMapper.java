package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.ExcellentGoodsUsingRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExcellentGoodsUsingRecordMapper extends CustomBaseMapper<ExcellentGoodsUsingRecord> {

    ExcellentGoodsUsingRecord selectUsedCount(@Param("beginDay") String beginDay, @Param("endDay")String endDay, @Param("userId")Long userId);

    ExcellentGoodsUsingRecord selectByUserIdAndDate(@Param("userId")Long userId, @Param("useDate")String useDate);

    List<ExcellentGoodsUsingRecord> selectListByUserIdAndDate(@Param("userId")Long userId, @Param("useDate") String useDate);
}