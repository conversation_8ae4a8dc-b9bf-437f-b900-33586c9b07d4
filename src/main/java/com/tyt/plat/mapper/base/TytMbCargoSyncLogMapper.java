package com.tyt.plat.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.plat.entity.base.TytMbCargoSyncLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytMbCargoSyncLogMapper extends CustomBaseMapper<TytMbCargoSyncLog> {

    /**
     * 获取最后一次同步日志
     * @param cargoId
     * @return
     */
    TytMbCargoSyncLog selectLastByCargoId(@Param("cargoId") Long cargoId);

    /**
     * 获取某版本的同步记录
     * @param cargoId
     * @param cargoVersion
     * @return
     */
    TytMbCargoSyncLog selectByCargoIdAndVersion(@Param("cargoId")Long cargoId,@Param("cargoVersion") Integer cargoVersion);
}
