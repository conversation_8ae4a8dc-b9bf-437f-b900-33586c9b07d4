package com.tyt.plat.enterprise.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class LicenseInfoWithOCRResp implements Serializable {
    private String enterpriseName;
    private String enterpriseCreditCode;
    private String legalPersonName;
    private String enterpriseDetailAddress;
    private String enterpriseBusinessScope;

    //有效期起始日期
    private Date validStartDate;
    //营业执照到期时间(根据天眼查返回信息确认)
    private Date licenseEndTime;
    //营业执照是否长期 0：非长期；1：长期(根据天眼查返回信息确认)
    private Integer licensePermanent;

}
