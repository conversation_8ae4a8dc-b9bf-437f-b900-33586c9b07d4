package com.tyt.plat.enterprise.pojo.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class VerifySaveContractReq implements Serializable {

    @NotNull(message = "签署方式不能为空")
    @Size(message = "签署方式不正确",min = 1,max = 2)
    private Integer signType;
    @NotNull(message = "协议文件URL不能为空")
    private String contractUrl;
}
