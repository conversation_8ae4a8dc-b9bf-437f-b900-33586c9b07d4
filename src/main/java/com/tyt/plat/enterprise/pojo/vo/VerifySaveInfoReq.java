package com.tyt.plat.enterprise.pojo.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

@Data
public class VerifySaveInfoReq implements Serializable {

    @NotNull(message = "企业名称不能为空")
    private String enterpriseName;
    @NotNull(message = "统一信用代码不能为空")
    @Pattern(regexp = "^[A-Za-z0-9(-)]+$",message = "统一信用代码格式不正确，请核验")
    private String enterpriseCreditCode;
    @NotNull(message = "企业法人姓名不能为空")
    private String legalPersonName;
    @NotNull(message = "企业注册地址不能为空")
    private String enterpriseDetailAddress;
    @NotNull(message = "营业执照URL不能为空")
    private String licenseUrl;
    //经营范围
    private String enterpriseBusinessScope;

    //道路经营许可证照片URL
    private String transportLicenseUrl;


    //营业执照开始时间(根据天眼查返回信息确认)
    private Date licenseStartTime;
    //营业执照到期时间(根据天眼查返回信息确认)
    private Date licenseEndTime;
    //营业执照是否长期 0：非长期；1：长期(根据天眼查返回信息确认)
    private int licensePermanent;
    //是否进行过天眼查企业核验 0：未进行；1：核验成功；2：核验失败
    private int realVerify;
    //企业类型
    private String enterpriseType;


}
