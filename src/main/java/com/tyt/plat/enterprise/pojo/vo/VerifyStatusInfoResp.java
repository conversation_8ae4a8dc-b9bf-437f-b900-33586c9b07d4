package com.tyt.plat.enterprise.pojo.vo;

import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
public class VerifyStatusInfoResp implements Serializable {

    private Long id;

    private String enterpriseName;

    private String legalPersonName;

    private String enterpriseCreditCode;

    private String enterpriseDetailAddress;

    private String licenseUrl;

    private String licenseEndTime;

    private String transportLicenseUrl;

    private String contractNo;

    private String contractUrl;

    private String authorizationUrl;

    @Deprecated
    private Integer enterpriseVerifyStatus;

    private Integer infoVerifyStatus;

    /**
     * 基本信息审核失败原因
     */
    private String infoVerifyReason;

    /**
     * 企业基础信息审核时间
     */
    private Date infoAuditTime;

    /**
     * 道路运输许可证驳回原因
     */
    private String transportRejectReason;

    /**
     * 道路运输许可证认证状态(0-未提交;1-审核中;2-已通过;3-审核驳回)
     */
    private Integer transportLicenseStatus;

    /**
     * 道路运输证审核时间
     */
    private Date transportAuditTime;

    /** ========== 税票相关字段 ========== **/
    /**
     * 框架协议状态，同contract_status(0未签署;1签署中;2已签署;3签署失败;4已失效)
     */
    private Integer contractVerifyStatus;

    /**
     * 企业最终激活状态(0未激活;1激活中;2激活成功;3激活失败)
     */
    private Integer contractFinalStatus;

    /**
     * 企业最终激活失败原因
     */
    private String contractFinalReason;

    /**
     * 是否是企业管理员
     */
    private Integer managerFlag;

}
