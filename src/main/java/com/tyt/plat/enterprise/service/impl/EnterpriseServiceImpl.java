package com.tyt.plat.enterprise.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.gexin.fastjson.JSON;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.ConvertUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.User;
import com.tyt.plat.client.esign.ApiEsignSignClient;
import com.tyt.plat.client.user.ApiInvoiceEnterpriseClient;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.commons.tools.AliOssClient;
import com.tyt.plat.constant.*;
import com.tyt.plat.enterprise.db.EnterpriseDBService;
import com.tyt.plat.enterprise.pojo.vo.*;
import com.tyt.plat.enterprise.service.EnterpriseService;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.*;
import com.tyt.plat.enums.enterprise.*;
import com.tyt.plat.enums.esign.*;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.ocr.OcrService;
import com.tyt.plat.service.user.ApiInvoiceEnterpriseService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.utils.TimeUtil;
import com.tyt.plat.utils.http.HttpClientUtil;
import com.tyt.plat.utils.mask.MaskFieldEnum;
import com.tyt.plat.utils.mask.MaskFieldUtil;
import com.tyt.plat.vo.axb.IcBasicInfoNormalResp;
import com.tyt.plat.vo.enterprise.EnterpriseContractVo;
import com.tyt.plat.vo.enterprise.SignValidCallVo;
import com.tyt.plat.vo.enterprise.TieredTaxRate;
import com.tyt.plat.vo.enterprise.TieredTaxRateText;
import com.tyt.plat.vo.esign.*;
import com.tyt.plat.vo.ocr.BusinessLicenseOcrRpcVO;
import com.tyt.plat.vo.ocr.OcrBusinessLicenseVo;
import com.tyt.plat.vo.user.EnterpriseModifyCheckVo;
import com.tyt.plat.vo.user.UserCarryPointTotalVo;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.bean.UserBaseInfo;
import com.tyt.user.service.InternalUserService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class EnterpriseServiceImpl implements EnterpriseService {

    public static final String ZH_DAY_FORMAT = "yyyy年MM月dd日";
    /**
     * 已缓存的合同地址.
     */
    private volatile String contractTmpUrlCache = null;

    /**
     * 合同模板本地地址
     */
    private static final String CONTRACT_LOCAL_NAME = "enterprise/enterprise_contract_template.pdf";

    @Autowired
    private EnterpriseDBService enterpriseDBService;
    @Autowired
    private InternalUserService internalUserService;

    @Autowired
    private CommonApiService commonApiService;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService tytUserIdentityAuthService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

    @Autowired
    private TytInvoiceEnterpriseSignMapper tytInvoiceEnterpriseSignMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private AliOssClient aliOssClient;

    @Autowired
    private TytContractGmvRateConfigMapper tytContractGmvRateConfigMapper;

    @Autowired
    private TytSelfCompanySignMapper tytSelfCompanySignMapper;

    @Autowired
    private ApiEsignSignClient apiEsignSignClient;

    @Autowired
    private ApiInvoiceEnterpriseService apiInvoiceEnterpriseService;

    @Autowired
    private TytInvoiceTaxRateRuleMapper tytInvoiceTaxRateRuleMapper;

    @Autowired
    private OcrService ocrService;

    /**
     * 营业执照是否长期 0：非长期；1：长期
     */
    private static final int INVOICE_ENTERPRISE_LONG_TERM = 1;
    private static final int INVOICE_ENTERPRISE_SHORT_TERM = 0;


    /**
     * 是否进行过天眼查企业核验 0：未进行；1：核验成功；2：核验失败
     */
    private static final int TIAN_YAN_CHA_REALVERIFY_NO = 0;
    private static final int TIAN_YAN_CHA_REALVERIFY_YES = 1;
    private static final int TIAN_YAN_CHA_REALVERIFY_ERROR = 2;


    /**
     * 营业执照状态 开业、在业、存续、迁入、迁出、正常
     */
    private static final String REG_STATUS_NORMAL = "正常";
    private static final String REG_STATUS_OPEN = "开业";
    private static final String REG_STATUS_IN_SERVICE = "在业";
    private static final String REG_STATUS_SUBSIST = "存续";
    private static final String REG_STATUS_MOVE_IN = "迁入";
    private static final String REG_STATUS_MOVE_OUT = "迁出";

    public static final String ENTERPRISE_PERSONAL = "个体工商户";

    @Override
    public LicenseInfoWithOCRResp getLicenseInfoWithOCR(LicenseInfoWithOCRReq licenseInfoWithOCRReq) {
        //调用集团营业执照OCR能力
        BusinessLicenseOcrRpcVO businessLicenseOcrRpcVO = ocrService.businessLicenseOcr(licenseInfoWithOCRReq.getLicenseUrl());
        return buildBusinessLicenseVo(businessLicenseOcrRpcVO, licenseInfoWithOCRReq.getLicenseUrl());
    }

    private boolean checkMainInfoChange(VerifySaveInfoReq verifySaveInfoReq, TytInvoiceEnterprise dbEnterprise){

        String enterpriseName = verifySaveInfoReq.getEnterpriseName();
        String enterpriseCreditCode = verifySaveInfoReq.getEnterpriseCreditCode();
        String legalPersonName = verifySaveInfoReq.getLegalPersonName();

        String dbEnterpriseName = dbEnterprise.getEnterpriseName();
        String dbEnterpriseCreditCode = dbEnterprise.getEnterpriseCreditCode();
        String dbLegalPersonName = dbEnterprise.getLegalPersonName();

        if(!StringUtils.equals(enterpriseName, dbEnterpriseName)
                || !StringUtils.equals(enterpriseCreditCode, dbEnterpriseCreditCode)
                || !StringUtils.equals(legalPersonName, dbLegalPersonName)
        ){
            return true;
        }
        return false;
    }

    private void setSourceType(TytInvoiceEnterprise dbEnterprise, Integer clientSign){
        if(clientSign == null){
            return;
        }
        Integer sourceType = null;

        if(Constant.ClientSignEnum.isCar(clientSign)){
            sourceType = EnterpriseSourceTypeEnum.CAR.getCode();
        } else if(Constant.ClientSignEnum.isGoods(clientSign)){
            sourceType = EnterpriseSourceTypeEnum.GOODS.getCode();
        }
        dbEnterprise.setSourceType(sourceType);
    }

    private void checkEnterpriseModifyAllow(Long userId){
        EnterpriseModifyCheckVo modifyCheckVo = apiInvoiceEnterpriseService.checkModifyAllow(userId);
        if(modifyCheckVo != null){
            Boolean orderExist = modifyCheckVo.getOrderExist();
            Boolean transportExist = modifyCheckVo.getTransportExist();

            if(BooleanUtils.isTrue(orderExist)){
                throw TytException.createException(PlatResponseEnum.ORDER_EXIST.info());
            }

            if(BooleanUtils.isTrue(transportExist)){
                throw TytException.createException(PlatResponseEnum.TRANDPORT_EXIST.info());
            }
        }
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public void saveInfo(Long userId, VerifySaveInfoReq verifySaveInfoReq, Integer clientSign) {
        TytUserIdentityAuth authInfo = tytUserIdentityAuthService.getByUserId(userId.toString());
        if (!Constant.IDENTITY_STATUS_SUCCESS.equals(authInfo.getIdentityStatus())) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.FILE_ENTERPRISE_AUTH, "实名认证成功后才可进行企业认证"));
        }

        UserBaseInfo userInfo = internalUserService.getByUserId(userId);
        if (ObjectUtil.isNull(userInfo.getUserId())) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "未查询到用户信息"));
        }

        //获取天眼查信息 进行信息校验 并返回经营结束时间
        checkTiAnYanChaBaseInfo(verifySaveInfoReq);

        Integer infoVerifyStatus = null;
        Integer transportLicenseStatus = null;

        TytInvoiceEnterprise dbEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);

        boolean exist = true;

        if (dbEnterprise == null) {
            exist = false;
            dbEnterprise = new TytInvoiceEnterprise();

            tytUserIdentityAuthService.initBaseEnterprise(userInfo, dbEnterprise);
            dbEnterprise.setCtime(new Date());

        } else {

            if(this.checkMainInfoChange(verifySaveInfoReq, dbEnterprise)){
                this.checkEnterpriseModifyAllow(userId);
            }

            tytUserIdentityAuthService.initCustomerManager(dbEnterprise);

            infoVerifyStatus = dbEnterprise.getInfoVerifyStatus();
            transportLicenseStatus = dbEnterprise.getTransportLicenseStatus();
        }

        if (infoVerifyStatus == null) {
            infoVerifyStatus = EnterpriseVerifyNodeStatusEnum.UNVERIFIED.getStatus();
            dbEnterprise.setInfoVerifyStatus(infoVerifyStatus);
        }
        if (transportLicenseStatus == null) {
            transportLicenseStatus = TransportLicenseStatusEnum.nulldefault.getCode();
            dbEnterprise.setTransportLicenseStatus(transportLicenseStatus);
        }

        boolean saveInfo = true;
        boolean saveTransportLicense = true;

        //基本信息审核中，或审核通过，不允许编辑
        if (EnterpriseVerifyNodeStatusEnum.VERIFING.equalsCode(infoVerifyStatus)
                || EnterpriseVerifyNodeStatusEnum.VERIFIED.equalsCode(infoVerifyStatus)) {
            saveInfo = false;
        }
        //基本信息审核中，或审核通过，不允许编辑
        if (TransportLicenseStatusEnum.submit.equalsCode(transportLicenseStatus)
                || TransportLicenseStatusEnum.success.equalsCode(transportLicenseStatus)) {
            saveTransportLicense = false;
        }

        if (!saveInfo && !saveTransportLicense) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "企业基本信息已认证"));
        }

        if (saveInfo) {

            TytInvoiceEnterpriseSign userEnterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);
            if (userEnterpriseSign != null) {
                throw TytException.createException(ResponseEnum.request_error.info("请先联系客服驳回企业认证，再提交信息"));
            }

            this.setSourceType(dbEnterprise, clientSign);

            //保存基本信息
            this.buildEnterpriseBase(userInfo, dbEnterprise, verifySaveInfoReq);
        }
        String transportLicenseUrl = verifySaveInfoReq.getTransportLicenseUrl();

        if (saveTransportLicense && StringUtils.isNotBlank(transportLicenseUrl)) {
            //保存运输许可证
            this.buildEnterpriseTransport(dbEnterprise, verifySaveInfoReq);
        }

        if (exist) {
            enterpriseDBService.updateTwo(dbEnterprise);
        } else {
            tytUserIdentityAuthService.initBaseEnterprise(userInfo, dbEnterprise);
            dbEnterprise.setCtime(new Date());

            enterpriseDBService.insert(dbEnterprise);
        }

        if (saveInfo) {
            //认证信息入老表
            authInfo.setEnterpriseAuthFailureReason(null);
            authInfo.setEnterpriseAuthCreditCode(dbEnterprise.getEnterpriseCreditCode());
            authInfo.setEnterpriseAuthCompanyName(dbEnterprise.getEnterpriseName());
            authInfo.setEnterpriseAuthLicenseUrl(dbEnterprise.getLicenseUrl());
            authInfo.setEnterpriseAuthStatus(2);
            authInfo.setExamineStatus(0);
            Date now = new Date();
            authInfo.setEnterpriseAuthCtime(now);
            authInfo.setEnterpriseAuthUtime(now);
            authInfo.setUtime(now);
            //企业认证来源
            authInfo.setEnterpriseAuthPath(null);
            tytUserIdentityAuthService.update(authInfo);
        }
    }


    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public void saveTransportLicenseInfo(Long userId, String transportLicenseUrl) {
        TytUserIdentityAuth authInfo = tytUserIdentityAuthService.getByUserId(userId.toString());
        if (!Constant.IDENTITY_STATUS_SUCCESS.equals(authInfo.getIdentityStatus())) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.FILE_ENTERPRISE_AUTH, "实名认证成功后才可进行运输资质认证"));
        }
        Integer transportLicenseStatus = null;

        TytInvoiceEnterprise dbEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);

        boolean exist = true;

        if (dbEnterprise == null) {
            exist = false;
            dbEnterprise = new TytInvoiceEnterprise();
        } else {
            transportLicenseStatus = dbEnterprise.getTransportLicenseStatus();
        }
        if (transportLicenseStatus == null) {
            dbEnterprise.setTransportLicenseStatus(TransportLicenseStatusEnum.nulldefault.getCode());
        }

        //道路运输证信息审核中，或审核通过，不允许编辑
        if (TransportLicenseStatusEnum.submit.equalsCode(transportLicenseStatus)
                || TransportLicenseStatusEnum.success.equalsCode(transportLicenseStatus)) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "运输资质信息已认证"));
        }

        UserBaseInfo userInfo = internalUserService.getByUserId(userId);
        if (ObjectUtil.isNull(userInfo.getUserId())) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "未查询到用户信息"));
        }

        if (StringUtils.isNotBlank(transportLicenseUrl)) {
            //保存运输许可证
            dbEnterprise.setMtime(new Date());
            dbEnterprise.setTransportLicenseUrl(transportLicenseUrl);
            dbEnterprise.setTransportLicenseStatus(TransportLicenseStatusEnum.submit.getCode());
            dbEnterprise.setTransportRejectReason("");
            dbEnterprise.setTransportCommitTime(new Date());
        }

        if (exist) {
            //每次提交重置上次道路运输证审核信息为null
            dbEnterprise.setTransportAuditTime(null);
            dbEnterprise.setTransportAuditUserId(null);
            dbEnterprise.setTransportAuditUserName(null);
            enterpriseDBService.updateTwo(dbEnterprise);
        } else {
            //默认值
            dbEnterprise.setCertigierUserId(userInfo.getUserId());
            dbEnterprise.setCertigierUserName(userInfo.getTrueName());
            dbEnterprise.setCertigierUserPhone(userInfo.getCellPhone());
            dbEnterprise.setEnterpriseTaxRate(TytEnterpriseConstant.DEFAULT_RATE_DECIMAL);
            dbEnterprise.setRealVerify(TIAN_YAN_CHA_REALVERIFY_NO);
            dbEnterprise.setLicensePermanent(INVOICE_ENTERPRISE_SHORT_TERM);
            dbEnterprise.setCtime(new Date());
            enterpriseDBService.insert(dbEnterprise);
        }
    }


    /**
     * 对比天眼查信息和传递过来的企业信息
     *
     * @param verifySaveInfoReq 用户传递企业信息
     * @return void
     */
    private void checkTiAnYanChaBaseInfo(VerifySaveInfoReq verifySaveInfoReq) {

        Date licenseStartTime = verifySaveInfoReq.getLicenseStartTime();
        Date licenseEndTime = verifySaveInfoReq.getLicenseEndTime();
        int licensePermanent = verifySaveInfoReq.getLicensePermanent();

        if(licensePermanent == INVOICE_ENTERPRISE_LONG_TERM){
            licenseEndTime = null;
        }

        //统一信用代码不能为空
        String enterpriseCreditCode = verifySaveInfoReq.getEnterpriseCreditCode();
        //企业名称
        String enterpriseName = verifySaveInfoReq.getEnterpriseName();
        //法人姓名
        String legalPersonName = verifySaveInfoReq.getLegalPersonName();
        IcBasicInfoNormalResp basicInfoNormal = commonApiService.baseInfoNormal(enterpriseCreditCode);
        log.info("checkTiAnYanChaBaseInfo baseInfoNormal 返回结果:{}", basicInfoNormal == null ? "" : JSON.toJSONString(basicInfoNormal));
        if (basicInfoNormal != null && basicInfoNormal.getResult() != null) {
            IcBasicInfoNormalResp.ResultDTO result = basicInfoNormal.getResult();
            if (!enterpriseName.equals(result.getName())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ENTERPRISE_NAME_ERROR, "企业名称不正确，请修改后再提交"));
            }
            if (!legalPersonName.equals(result.getLegalPersonName())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.LEGAL_PERSON_NAME_ERROR, "法人姓名不正确，请修改后再提交"));
            }
            if (!enterpriseCreditCode.equals(result.getCreditCode())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.CREDIT_CODE_ERROR, "统一信用代码不正确，请修改后再提交"));
            }
            //经营有效期
            Long fromTime = result.getFromTime();
            Object toTime = result.getToTime();

            if(fromTime != null){
                licenseStartTime = new Date(fromTime);
            }
            if(toTime != null){
                long toTimeLong = Long.parseLong(toTime.toString());
                licenseEndTime = new Date(toTimeLong);
                licensePermanent = INVOICE_ENTERPRISE_SHORT_TERM;
            } else {
                licensePermanent = INVOICE_ENTERPRISE_LONG_TERM;
                licenseEndTime = null;
            }

            if (!REG_STATUS_NORMAL.equals(result.getRegStatus()) && !REG_STATUS_OPEN.equals(result.getRegStatus()) && !REG_STATUS_IN_SERVICE.equals(result.getRegStatus())
                    && !REG_STATUS_SUBSIST.equals(result.getRegStatus()) && !REG_STATUS_MOVE_IN.equals(result.getRegStatus()) && !REG_STATUS_MOVE_OUT.equals(result.getRegStatus())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.REG_STATUS_ERROR, "营业执照状态异常！"));
            }
            verifySaveInfoReq.setRealVerify(TIAN_YAN_CHA_REALVERIFY_YES);
            verifySaveInfoReq.setEnterpriseType(result.getCompanyOrgType());
        } else {
            verifySaveInfoReq.setRealVerify(TIAN_YAN_CHA_REALVERIFY_ERROR);
        }

        if(licenseEndTime == null){
            licensePermanent = INVOICE_ENTERPRISE_LONG_TERM;
        } else {
            licensePermanent = INVOICE_ENTERPRISE_SHORT_TERM;

            final Date nowTime = new Date();
            if(licenseEndTime.before(nowTime)) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.REG_TO_TIME_ERROR, "营业执照有效期异常！"));
            }
        }

        verifySaveInfoReq.setLicenseStartTime(licenseStartTime);
        verifySaveInfoReq.setLicenseEndTime(licenseEndTime);
        verifySaveInfoReq.setLicensePermanent(licensePermanent);

    }


    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public void saveContract(Long userId, VerifySaveContractReq verifySaveContractReq) {
        TytInvoiceEnterprise verifyInfo = enterpriseDBService.getVerifyInfoByUserId(userId);
        if (ObjectUtil.isNotNull(verifyInfo)) {
            if (EnterpriseVerifyOverallStatusEnum.VERIFIED.getStatus().equals(verifyInfo.getEnterpriseVerifyStatus())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "企业已认证"));
            }
            if (EnterpriseVerifyNodeStatusEnum.VERIFIED.getStatus().equals(verifyInfo.getContractVerifyStatus())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "企业基本信息已认证"));
            }
            TytInvoiceEnterprise tytInvoiceEnterprise = this.buildDto(verifySaveContractReq);
            tytInvoiceEnterprise.setId(verifyInfo.getId());
            tytInvoiceEnterprise.setMtime(new Date());
            enterpriseDBService.update(tytInvoiceEnterprise);
            return;
        }
        throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "请先进行企业信息认证"));
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public void saveCertigier(Long userId, VerifySaveCertigierReq verifySaveCertigierReq) {
        TytInvoiceEnterprise verifyInfo = enterpriseDBService.getVerifyInfoByUserId(userId);
        if (ObjectUtil.isNotNull(verifyInfo)) {
            if (EnterpriseVerifyOverallStatusEnum.VERIFIED.getStatus().equals(verifyInfo.getEnterpriseVerifyStatus())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "企业已认证"));
            }
            if (EnterpriseVerifyNodeStatusEnum.VERIFIED.getStatus().equals(verifyInfo.getCertigierVerifyStatus())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "企业基本信息已认证"));
            }
            TytInvoiceEnterprise tytInvoiceEnterprise = this.buildDto(verifySaveCertigierReq);
            tytInvoiceEnterprise.setId(verifyInfo.getId());
            tytInvoiceEnterprise.setMtime(new Date());
            enterpriseDBService.update(tytInvoiceEnterprise);
            return;
        }
        throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "请先进行企业信息认证"));
    }

    @Override
    public VerifyStatusInfoResp getVerifyStatus(Long userId) {
        TytInvoiceEnterprise verifyInfo = enterpriseDBService.getVerifyInfoByUserId(userId);
        if (verifyInfo == null || verifyInfo.getId() == null) {
            verifyInfo = TytInvoiceEnterprise.builder().enterpriseVerifyStatus(EnterpriseVerifyOverallStatusEnum.UNVERIFIED.getStatus()).build();
        }
        return BeanUtil.copyProperties(verifyInfo, VerifyStatusInfoResp.class);
    }

    @Override
    public boolean checkEnterpriseInOnlineFreight(String enterpriseName) {
        TytOnlineFreightEnterprise enterprise = enterpriseDBService.getOnlineFreightEnterpriseByName(enterpriseName);
        if (enterprise != null) {
            return true;
        }
        return false;
    }

    private LicenseInfoWithOCRResp buildVo(OcrBusinessLicenseVo ocrBusinessLicenseVo) {
        if (ocrBusinessLicenseVo == null) {
            return null;
        }
        LicenseInfoWithOCRResp resp = new LicenseInfoWithOCRResp();
        resp.setEnterpriseName(ocrBusinessLicenseVo.getCompanyName());
        resp.setEnterpriseCreditCode(ocrBusinessLicenseVo.getCreditCode());
        resp.setLegalPersonName(ocrBusinessLicenseVo.getLegalPersonName());
        resp.setEnterpriseDetailAddress(ocrBusinessLicenseVo.getRegisterAddress());
        resp.setEnterpriseBusinessScope(ocrBusinessLicenseVo.getBusinessScope());
        resp.setValidStartDate(ocrBusinessLicenseVo.getValidStartDate());

        String expiration = ocrBusinessLicenseVo.getExpiration();
        if(StringUtils.isNotBlank(expiration)){

            if("长期".equals(expiration)){
                resp.setLicensePermanent(GlobalStatusEnum.yes.getCode());
            }else {
                Date licenseEndTime = null;
                try {
                    licenseEndTime = DateUtil.parseDate(expiration, ZH_DAY_FORMAT);
                } catch (Exception e) {
                    log.error("", e);
                }
                resp.setLicensePermanent(GlobalStatusEnum.no.getCode());
                resp.setLicenseEndTime(licenseEndTime);
            }
        }

        return resp;
    }

    private LicenseInfoWithOCRResp buildBusinessLicenseVo(BusinessLicenseOcrRpcVO businessLicenseOcrRpcVO, String licenseUrl) {
        if (ObjectUtil.isEmpty(businessLicenseOcrRpcVO)) {
            //重新进行百度OCR
            return buildVo(commonApiService.doOcrByLicenseUrl(licenseUrl));
        }
        LicenseInfoWithOCRResp resp = new LicenseInfoWithOCRResp();
        resp.setEnterpriseName(businessLicenseOcrRpcVO.getName());
        resp.setEnterpriseCreditCode(businessLicenseOcrRpcVO.getRegNum());
        resp.setLegalPersonName(businessLicenseOcrRpcVO.getPerson());
        resp.setEnterpriseDetailAddress(businessLicenseOcrRpcVO.getAddress());
        resp.setEnterpriseBusinessScope(businessLicenseOcrRpcVO.getBusiness());
        try {
            resp.setValidStartDate(TimeUtil.parseyyyymmddCN(businessLicenseOcrRpcVO.getStartTime()));
            String expiration = businessLicenseOcrRpcVO.getEndTime();
            parseLicenseEndTime(resp, businessLicenseOcrRpcVO.getEndTime());
            if(StringUtils.isNotBlank(expiration)){
                if("长期".equals(expiration)){
                    resp.setLicensePermanent(1);
                }else {
                    Date licenseEndTime = TimeUtil.parseyyyymmddCN(expiration);
                    resp.setLicensePermanent(2);
                    resp.setLicenseEndTime(licenseEndTime);
                }
            }
        } catch (Exception e) {
            log.error("build business license parse time error", e);
        }

        //有未识别的内容走百度OCR兜底
        guaranteeBaiDuOcr(resp, licenseUrl);

        return resp;
    }

    private void guaranteeBaiDuOcr(LicenseInfoWithOCRResp resp, String licenseUrl) {
        //补齐OCR缺失的字段
        if (StringUtils.isBlank(resp.getEnterpriseName())
                || StringUtils.isBlank(resp.getEnterpriseCreditCode())
                || resp.getEnterpriseCreditCode().length() != 18
                || StringUtils.isBlank(resp.getLegalPersonName())
                || StringUtils.isBlank(resp.getEnterpriseDetailAddress())
                || StringUtils.isBlank(resp.getEnterpriseBusinessScope())
                || ObjectUtil.isNull(resp.getValidStartDate())
                || ObjectUtil.isNull(resp.getLicensePermanent())
                || (resp.getLicensePermanent() == 2 && ObjectUtil.isNull(resp.getLicenseEndTime()))) {
            log.info("进入百度OCR兜底逻辑 resp【{}】", JSON.toJSONString(resp));
            OcrBusinessLicenseVo ocrBusinessLicenseVo = commonApiService.doOcrByLicenseUrl(licenseUrl);
            if (StringUtils.isBlank(resp.getEnterpriseName()) && StringUtils.isNotBlank(ocrBusinessLicenseVo.getCompanyName())) {
                resp.setEnterpriseName(ocrBusinessLicenseVo.getCompanyName());
            }
            if ((StringUtils.isBlank(resp.getEnterpriseCreditCode()) || resp.getEnterpriseCreditCode().length() != 18)
                    && StringUtils.isNotBlank(ocrBusinessLicenseVo.getCreditCode())) {
                resp.setEnterpriseCreditCode(ocrBusinessLicenseVo.getCreditCode());
            }
            if (StringUtils.isBlank(resp.getLegalPersonName()) && StringUtils.isNotBlank(ocrBusinessLicenseVo.getLegalPersonName())) {
                resp.setLegalPersonName(ocrBusinessLicenseVo.getLegalPersonName());
            }
            if (StringUtils.isBlank(resp.getEnterpriseDetailAddress()) && StringUtils.isNotBlank(ocrBusinessLicenseVo.getRegisterAddress())) {
                resp.setEnterpriseDetailAddress(ocrBusinessLicenseVo.getRegisterAddress());
            }
            if (StringUtils.isBlank(resp.getEnterpriseBusinessScope()) && StringUtils.isNotBlank(ocrBusinessLicenseVo.getBusinessScope())) {
                resp.setEnterpriseBusinessScope(ocrBusinessLicenseVo.getBusinessScope());
            }
            if (ObjectUtil.isNull(resp.getValidStartDate()) && ObjectUtil.isNotNull(ocrBusinessLicenseVo.getValidStartDate())) {
                resp.setValidStartDate(ocrBusinessLicenseVo.getValidStartDate());
            }
            if ((ObjectUtil.isNull(resp.getLicensePermanent())
                    || (resp.getLicensePermanent() == 2 && ObjectUtil.isNull(resp.getLicenseEndTime())))
                    && ObjectUtil.isNotNull(ocrBusinessLicenseVo.getExpiration())) {
                parseLicenseEndTime(resp, ocrBusinessLicenseVo.getExpiration());
            }
        }
    }

    private void parseLicenseEndTime(LicenseInfoWithOCRResp resp, String expiration) {
        if(StringUtils.isNotBlank(expiration)){
            if("长期".equals(expiration)){
                resp.setLicensePermanent(1);
            }else {
                Date licenseEndTime = null;
                try {
                    licenseEndTime = TimeUtil.parseyyyymmddCN(expiration);
                } catch (Exception e) {
                    log.error("", e);
                }
                resp.setLicensePermanent(2);
                resp.setLicenseEndTime(licenseEndTime);
            }
        }
    }

    /**
     * 保存企业基本信息
     *
     * @param dto
     * @param verifySaveInfoReq
     */
    private void buildEnterpriseBase(UserBaseInfo userInfo, TytInvoiceEnterprise dto, VerifySaveInfoReq verifySaveInfoReq) {

        dto.setEnterpriseName(verifySaveInfoReq.getEnterpriseName());
        dto.setEnterpriseDetailAddress(verifySaveInfoReq.getEnterpriseDetailAddress());
        dto.setEnterpriseCreditCode(verifySaveInfoReq.getEnterpriseCreditCode());
        dto.setLegalPersonName(verifySaveInfoReq.getLegalPersonName());
        dto.setLicenseUrl(verifySaveInfoReq.getLicenseUrl());
        dto.setCertigierUserId(userInfo.getUserId());
        dto.setCertigierUserName(userInfo.getTrueName());
        dto.setCertigierUserPhone(userInfo.getCellPhone());
        dto.setEnterpriseVerifyStatus(EnterpriseVerifyOverallStatusEnum.VERIFING.getStatus());
        dto.setInfoVerifyStatus(EnterpriseVerifyNodeStatusEnum.VERIFING.getStatus());
        dto.setContractVerifyStatus(EnterpriseVerifyNodeStatusEnum.UNVERIFIED.getStatus());
        dto.setCertigierVerifyStatus(EnterpriseVerifyNodeStatusEnum.UNVERIFIED.getStatus());
        dto.setAccountStatus(null);
        dto.setMtime(new Date());
        dto.setInfoVerifyReason("");
        dto.setLicenseStartTime(verifySaveInfoReq.getLicenseStartTime());
        dto.setLicenseEndTime(verifySaveInfoReq.getLicenseEndTime());
        dto.setLicensePermanent(verifySaveInfoReq.getLicensePermanent());
        dto.setInfoVerifyCommitTime(new Date());
        dto.setRealVerify(verifySaveInfoReq.getRealVerify());
        dto.setEnterpriseType(verifySaveInfoReq.getEnterpriseType());
        //每次提交重置上次审核信息
        dto.setInfoAuditTime(null);
        dto.setInfoAuditUserId(null);
        dto.setInfoAuditUserName(null);

        dto.setContractFinalStatus(ContractFinalStatusEnum.INIT.getCode());
        dto.setContractFinalReason(null);
        dto.setManagerFlag(GlobalStatusEnum.no.getCode());

    }

    /**
     * 保存企业运输许可证
     *
     * @param dto
     * @param verifySaveInfoReq
     */
    private void buildEnterpriseTransport(TytInvoiceEnterprise dto, VerifySaveInfoReq verifySaveInfoReq) {

        dto.setMtime(new Date());
        dto.setTransportLicenseUrl(verifySaveInfoReq.getTransportLicenseUrl());
        dto.setTransportLicenseStatus(TransportLicenseStatusEnum.submit.getCode());
        dto.setTransportRejectReason("");
        dto.setTransportCommitTime(new Date());
        //每次提交重置上次审核信息
        dto.setTransportAuditTime(null);
        dto.setTransportAuditUserId(null);
        dto.setTransportAuditUserName(null);
    }

    private TytInvoiceEnterprise buildDto(VerifySaveContractReq verifySaveContractReq) {
        TytInvoiceEnterprise dto = new TytInvoiceEnterprise();
        dto.setSignType(verifySaveContractReq.getSignType());
        dto.setContractUrl(verifySaveContractReq.getContractUrl());//@Deprecated
        dto.setContractVerifyStatus(EnterpriseVerifyNodeStatusEnum.VERIFING.getStatus());
        return dto;
    }

    private TytInvoiceEnterprise buildDto(VerifySaveCertigierReq verifySaveCertigierReq) {
        TytInvoiceEnterprise dto = new TytInvoiceEnterprise();
        dto.setAuthorizationUrl(verifySaveCertigierReq.getAuthorizationUrl());//@Deprecated
        dto.setCertigierVerifyStatus(EnterpriseVerifyNodeStatusEnum.VERIFING.getStatus());
        return dto;
    }


    //1. 取消合同
    public void cancleContract(TytInvoiceEnterpriseSign enterpriseSign) {

        Date nowTime = new Date();

        Long signId = enterpriseSign.getId();

        TytInvoiceEnterpriseSign enterpriseSignUpdate = new TytInvoiceEnterpriseSign();
        enterpriseSignUpdate.setId(signId);
        enterpriseSignUpdate.setEnable(GlobalStatusEnum.no.getCode());
        enterpriseSignUpdate.setModifyTime(nowTime);
        tytInvoiceEnterpriseSignMapper.updateByPrimaryKeySelective(enterpriseSignUpdate);

    }

    @Override
    @Transactional(transactionManager = TransactionConstant.MYBATIS, rollbackFor = Exception.class)
    public void terminateContract(Long userId) {

        Date nowTime = new Date();

        TytInvoiceEnterprise dbEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);

        Integer contractFinalStatus = dbEnterprise.getContractFinalStatus();

        if (!ContractFinalStatusEnum.FAIL.equalsCode(contractFinalStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("企业当前激活状态不允许重新上传！"));
        }

        //1. 取消合同
        Long enterpriseId = dbEnterprise.getId();
        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getEnableEnterpriseSign(enterpriseId);
        if(enterpriseSign != null){
            this.cancleContract(enterpriseSign);
        }

        //2. 修改状态
        dbEnterprise.setSignType(null);
        dbEnterprise.setContractNo(null);
        dbEnterprise.setContractStartTime(null);
        dbEnterprise.setContractEndTime(null);
        dbEnterprise.setContractVerifyStatus(ContractStatusEnum.INIT.getCode());
        //dbEnterprise.setContractAuditTime();

        dbEnterprise.setContractFinalStatus(ContractFinalStatusEnum.INIT.getCode());
        dbEnterprise.setContractFinalReason(null);

        dbEnterprise.setAccountStatus(null);
        dbEnterprise.setManagerFlag(GlobalStatusEnum.no.getCode());
        dbEnterprise.setCtime(nowTime);

        tytInvoiceEnterpriseMapper.updateByPrimaryKey(dbEnterprise);

    }

    @Override
    @Transactional(transactionManager = TransactionConstant.MYBATIS, rollbackFor = Exception.class)
    public void rejectEnterpriseVerify(Long userId) {

        Date nowTime = new Date();

        TytInvoiceEnterprise dbEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);

        Integer infoVerifyStatus = dbEnterprise.getInfoVerifyStatus();

        if (EnterpriseVerifyNodeStatusEnum.UNVERIFIED.equalsCode(infoVerifyStatus)
                || EnterpriseVerifyNodeStatusEnum.VERIFY_REJECT.equalsCode(infoVerifyStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("企业当前认证状态不允许进行该操作！"));
        }

        Integer realVerify = dbEnterprise.getRealVerify();

        if (EnterpriseRealVerifyEnum.SUCCESS.equalsCode(realVerify)) {
            throw TytException.createException(ResponseEnum.request_error.info("企业已核验，不允许操作！"));
        }

        Long enterpriseId = dbEnterprise.getId();
        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getEnableEnterpriseSign(enterpriseId);

        if (enterpriseSign != null) {
            Integer contractStatus = enterpriseSign.getContractStatus();

            if (!ContractStatusEnum.INIT.equalsCode(contractStatus)) {
                throw TytException.createException(ResponseEnum.request_error.info("企业协议签署中，不允许操作！"));
            }
        }

        String reasonText = "用户企业激活时发起更新";
        this.saveEnterpriseReject(dbEnterprise, reasonText);
    }

    private void saveEnterpriseReject(TytInvoiceEnterprise dbEnterprise, String infoVerifyReason){
        Date nowTime = new Date();
        Long userId = dbEnterprise.getCertigierUserId();

        Integer contractVerifyStatus = dbEnterprise.getContractVerifyStatus();

        //1. 修改 enterprise
        TytInvoiceEnterprise enterpriseUpdate = new TytInvoiceEnterprise();
        BeanUtils.copyProperties(dbEnterprise, enterpriseUpdate);

        enterpriseUpdate.setLegalPersonPhone(null);
        enterpriseUpdate.setLegalPersonCard(null);
        enterpriseUpdate.setSignType(null);
        enterpriseUpdate.setContractNo(null);
        enterpriseUpdate.setContractStartTime(null);
        enterpriseUpdate.setContractEndTime(null);
        enterpriseUpdate.setContractVerifyStatus(0);
        enterpriseUpdate.setAccountStatus(null);

        enterpriseUpdate.setInfoVerifyStatus(EnterpriseVerifyNodeStatusEnum.VERIFY_REJECT.getStatus());
        enterpriseUpdate.setInfoVerifyReason(infoVerifyReason);
        enterpriseUpdate.setManagerFlag(GlobalStatusEnum.no.getCode());
        enterpriseUpdate.setCtime(nowTime);

        if(ContractStatusEnum.SUCCESS.equalsCode(contractVerifyStatus)){
            enterpriseUpdate.setContractFinalStatus(ContractStatusEnum.FAIL.getCode());
            enterpriseUpdate.setContractFinalReason(infoVerifyReason);
        } else {
            enterpriseUpdate.setContractFinalStatus(ContractStatusEnum.INIT.getCode());
            enterpriseUpdate.setContractFinalReason(null);
        }

        tytInvoiceEnterpriseMapper.updateByPrimaryKey(enterpriseUpdate);

        //2. 修改 identity
        TytUserIdentityAuth authInfo = tytUserIdentityAuthService.getByUserId(userId.toString());

        authInfo.setLicenseStatus(UserInfoStatusEnum.FAIL.getCode());
        authInfo.setLicenseFailureReason(infoVerifyReason);
        authInfo.setEnterpriseAuthStatus(UserInfoStatusEnum.FAIL.getCode());
        authInfo.setEnterpriseAuthFailureReason(infoVerifyReason);
        authInfo.setUtime(nowTime);

        tytUserIdentityAuthService.update(authInfo);
    }

    @Override
    public void destroyEnterpriseVerify(TytInvoiceEnterprise dbEnterprise) {
        Long userId = dbEnterprise.getCertigierUserId();

        log.info("destroyEnterpriseVerify, userId : {}", userId);
        Long enterpriseId = dbEnterprise.getId();
        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getEnableEnterpriseSign(enterpriseId);

        if (enterpriseSign != null) {
            this.cancleContract(enterpriseSign);
        }

        this.saveEnterpriseReject(dbEnterprise, "用户注销");

    }


    /**
     * 非管理员时，抛出异常.
     *
     * @param managerName  managerName
     * @param managerPhone managerPhone
     */
    private void throwManageWarnMsg(String managerName, String managerPhone) {
        // 其中姓名、手机号取签署中协议提交用户的账号实名信息加密展示

        String maskName = MaskFieldUtil.maskText(MaskFieldEnum.NAME, managerName);
        String maskPhone = MaskFieldUtil.maskText(MaskFieldEnum.PHONE, managerPhone);

        String errorMsgTmp = "您所认证企业已激活或在激活中，无法重复提交，请联系企业管理员%s（%s）。如需帮助，请联系平台客服";
        String errorMsg = String.format(errorMsgTmp, maskName, maskPhone);

        throw TytException.createException(PlatResponseEnum.ENTERPRISE_NOT_MANAGER.info(errorMsg));

    }

    private void checkContractPreview(TytUserIdentityAuth userIdentityAuth, TytInvoiceEnterprise dbEnterprise) {

        //1. 实名认证状态, 未提交、认证中、认证驳回，终止(tost 提示)
        if (userIdentityAuth == null
                || !UserInfoStatusEnum.SUCCESS.equalsCode(userIdentityAuth.getInfoStatus())) {
            throw TytException.createException(ResponseEnum.request_error.info("请先完成实名认证"));
        }

        //2. 企业认证状态,未提交、认证驳回时，终止(tost 提示)

        Integer infoVerifyStatus = null;
        if (dbEnterprise != null) {
            infoVerifyStatus = dbEnterprise.getInfoVerifyStatus();
        }

        if (!EnterpriseVerifyNodeStatusEnum.VERIFIED.equalsCode(infoVerifyStatus)
                && !EnterpriseVerifyNodeStatusEnum.VERIFING.equalsCode(infoVerifyStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("请先提交企业认证"));
        }

        /**
        //3. 企业是否在线验真，自定义code (弹窗，按钮)

        Integer realVerify = dbEnterprise.getRealVerify();
        if (!EnterpriseRealVerifyEnum.SUCCESS.equalsCode(realVerify)) {
            throw TytException.createException(PlatResponseEnum.ENTERPRISE_NOT_REAL_VERIFY.info("企业激活前，请先更新企业认证资料"));
        }
        */

        //4. 是否存在其他用户提交的签署中的框架协议(弹窗)
        String enterpriseCreditCode = dbEnterprise.getEnterpriseCreditCode();
        if (StringUtils.isBlank(enterpriseCreditCode)) {
            throw TytException.createException(ResponseEnum.request_error.info("请先提交企业认证"));
        }

        Long id = dbEnterprise.getId();
        this.checkManagerEnterprise(enterpriseCreditCode, id);

        //5. 企业认证提交的企业是否为网络货运企业
        String enterpriseName = dbEnterprise.getEnterpriseName();
        if (this.checkEnterpriseInOnlineFreight(enterpriseName)) {
            throw TytException.createException(PlatResponseEnum.ENTERPRISE_ONLINE_FREIGHT.info("认证企业为网货平台，根据国家网络货运管理条例，网货平台不得相互委托业务"));
        }

        //6. 企业认证提交的企业的企业类型
        String enterpriseType = dbEnterprise.getEnterpriseType();
        if (StringUtils.isBlank(enterpriseType)) {
            throw TytException.createException(PlatResponseEnum.ENTERPRISE_TYPE_ERROR.info("认证企业类型无法识别，请联系客服"));
        }
        if (enterpriseType.contains(ENTERPRISE_PERSONAL)) {
            throw TytException.createException(PlatResponseEnum.ENTERPRISE_TYPE_ERROR.info("认证企业为个体工商户，暂不支持签约"));
        }

        //7. 用户提交的框架服务协议是否状态为签署中
        // （流程跳转）是：进入签署意愿验证码页(页面处理跳转)

        //8. 用户是否已经生成法人授权书链接
        //是：进入链接展示页(页面处理跳转)

    }

    /**
     * 校验企业每日生成协议次数，30次
     *
     * @param userId
     */
    private void checkContractDayCount(Long userId) {
        int contractMax = tytConfigService.getIntValue(ConfigKeyConstant.ENTERPRISE_CONTRACT_MAX, 30);
        String redisKey = RedisKeyConstant.getDayContractKey(userId);
        boolean check = RedisKeyConstant.countCheck(redisKey, contractMax);
        if(!check){
            throw TytException.createException(ResponseEnum.request_error.info("今日操作次数过多，请明天再试"));
        }
    }

    /**
     * 自增每日合同数
     *
     * @param userId userId
     */
    private void incrContractDayCount(Long userId) {
        String redisKey = RedisKeyConstant.getDayContractKey(userId);
        RedisKeyConstant.incrAndExpire(redisKey, TimeUnitConstant.DAY_SECOND);

    }

    /**
     * 下载pdf 文件
     *
     * @param contractFile contractFile
     */
    private void downloadContractPdf(File contractFile) {

        if (contractFile.exists()) {
            boolean delete = contractFile.delete();
            if (!delete) {
                throw TytException.createException(ResponseEnum.request_error.info("生成合同预览文件失败，请重试！"));
            }
        }

        CommonUtil.createFileFolder(contractFile.getAbsolutePath());

        //开始下载
        try (OutputStream output = new FileOutputStream(contractFile)) {
            HttpClientUtil.downloadFile(this.contractTmpUrlCache, output);

            log.info("enterprise_contract_download_success : {}", this.contractTmpUrlCache);
        } catch (Exception e) {
            log.error("contract_download_error : ", e);
            throw TytException.createException(ResponseEnum.request_error.info("生成合同预览文件失败，请重试！"));
        }

    }

    /**
     * 合同本地缓存文件.
     *
     * @return File
     */
    private File getContractLocalFile() {

        //本地路径
        String projectTmpPath = PlatCommonUtil.getProjectTmpPath();
        String contractPath = CommonUtil.joinPath(projectTmpPath, CONTRACT_LOCAL_NAME);
        File contractFile = new File(contractPath);

        String contractTmpUrl = tytConfigService.getStringValue(ConfigKeyConstant.CONTRACT_TEMPLATE_URL);

        if (StringUtils.isBlank(this.contractTmpUrlCache) || !this.contractTmpUrlCache.equals(contractTmpUrl)) {
            this.contractTmpUrlCache = contractTmpUrl;

            log.info("enterprise_contract_change : {}", this.contractTmpUrlCache);

            this.downloadContractPdf(contractFile);
        }
        return contractFile;
    }

    private TytSelfCompanySign getSelfCompanySign(){
        TytSelfCompanySign selfCompanySign = tytSelfCompanySignMapper.getSelfCompanySign();

        if(selfCompanySign == null){
            throw TytException.createException(ResponseEnum.request_error.info("合同生成失败，请联系客服！"));
        }
        return selfCompanySign;
    }

    /**
     * 设置企业签章合同 pdf 相关字段
     * @param dbUser dbUser
     * @param dbEnterprise dbEnterprise
     * @param invoiceEnterpriseSign invoiceEnterpriseSign
     * @return ContractPdfFormVo
     */
    private ContractPdfFormVo getContractPdfForm(User dbUser, TytInvoiceEnterprise dbEnterprise, TytInvoiceEnterpriseSign invoiceEnterpriseSign) {

        TytSelfCompanySign selfCompanySign = tytUserIdentityAuthService.initCustomerManager(dbEnterprise);

        String idCard = dbUser.getIdCard();
        String enterpriseName = dbEnterprise.getEnterpriseName();
        String userName = dbEnterprise.getCertigierUserName();
        String userPhone = dbEnterprise.getCertigierUserPhone();

        Date contractStartTime = invoiceEnterpriseSign.getContractStartTime();
        Date contractEndTime = invoiceEnterpriseSign.getContractEndTime();

        String startTimeStr = DateUtil.dateToString(contractStartTime, DateUtil.day_format);
        String endTimeStr = DateUtil.dateToString(contractEndTime, DateUtil.day_format);

        String zhAutiTime = DateUtil.dateToString(contractStartTime, PlatBaseConstant.DateFormatType.ZH_DAY_FORMAT);

        String[] startTimeArray = startTimeStr.split("-");
        String[] endTimeArray = endTimeStr.split("-");

        ContractPdfFormVo contractPdfFormVo = new ContractPdfFormVo();

        contractPdfFormVo.setContractNumber(invoiceEnterpriseSign.getContractNumber());
        contractPdfFormVo.setEnterpriseDetailAddressOne(dbEnterprise.getEnterpriseDetailAddress());
        contractPdfFormVo.setEnterpriseNameOne(enterpriseName);
        contractPdfFormVo.setUserNameOne(userName);
        contractPdfFormVo.setUserPhoneOne(userPhone);
        contractPdfFormVo.setUserEmailOne("");

        String selfCompanyName = selfCompanySign.getCompanyName();

        contractPdfFormVo.setTytEnterpriseNameOne(selfCompanyName);
        contractPdfFormVo.setTytUserNameOne(dbEnterprise.getCustomerManagerName());
        contractPdfFormVo.setTytUserPhoneOne(dbEnterprise.getCustomerManagerPhone());
        contractPdfFormVo.setTytEmailOne(dbEnterprise.getCustomerManagerEmail());

        contractPdfFormVo.setEndYear(endTimeArray[0]);
        contractPdfFormVo.setEndMonth(endTimeArray[1]);
        contractPdfFormVo.setEndDay(endTimeArray[2]);

        contractPdfFormVo.setUserSignNameOne(enterpriseName);
        contractPdfFormVo.setTytSignNameOne(selfCompanyName);

        contractPdfFormVo.setEnterpriseNameTwo(enterpriseName);
        contractPdfFormVo.setTytEnterpriseNameTwo(selfCompanyName);

        BigDecimal rateDic = dbEnterprise.getEnterpriseTaxRate();
        if(rateDic == null){
            rateDic = new BigDecimal(TytEnterpriseConstant.DEFAULT_RATE);
        }
        String rateText = rateDic.stripTrailingZeros().toPlainString();

        contractPdfFormVo.setDefaultFreightRate(rateText + "%");

        contractPdfFormVo.setUserSignNameTwo(enterpriseName);
        contractPdfFormVo.setTytSignNameTwo(selfCompanyName);

        contractPdfFormVo.setEnterpriseNameThree(enterpriseName);
        contractPdfFormVo.setAccountName(enterpriseName);
        contractPdfFormVo.setUserPhoneThree(userPhone);
        contractPdfFormVo.setAuthUserName(userName);
        contractPdfFormVo.setAuthIdCard(idCard);
        contractPdfFormVo.setAuthDuty(TytEnterpriseConstant.DEFAULT_AUTH_DUTY);

        contractPdfFormVo.setUserSignNameThree(enterpriseName);
        contractPdfFormVo.setSignTime(zhAutiTime);

        return contractPdfFormVo;
    }

    private List<TieredTaxRateText> removeBlank(List<TieredTaxRate> tieredTaxRateList){

        List<TieredTaxRateText> taxRateTextList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(tieredTaxRateList)){
            for(TieredTaxRate oneTaxRate: tieredTaxRateList) {
                BigDecimal taxRate = oneTaxRate.getTaxRate();
                BigDecimal gmv = oneTaxRate.getGmv();

                if(gmv != null && taxRate!= null){
                    String gmvText = gmv.stripTrailingZeros().toPlainString();
                    String taxRateText = taxRate.stripTrailingZeros().toPlainString() + "%";
                    TieredTaxRateText taxRateTextVo = new TieredTaxRateText();
                    taxRateTextVo.setGmvText(gmvText);
                    taxRateTextVo.setTaxRateText(taxRateText);
                    taxRateTextList.add(taxRateTextVo);
                }
            }
        }

        return taxRateTextList;

    }

    private List<TieredTaxRateText> parseTaxRate(String tieredTaxRateJson){

        List<TieredTaxRate> tieredTaxRateList = JSON.parseArray(tieredTaxRateJson, TieredTaxRate.class);
        List<TieredTaxRateText> taxRateTextList = this.removeBlank(tieredTaxRateList);

        List<TieredTaxRateText> resultRateTextList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(taxRateTextList)){

            int i = 0;
            for(TieredTaxRateText oneRateText: taxRateTextList){

                //当前Gmv
                String currentGmv = oneRateText.getGmvText();
                //当前费率
                String currentRate = oneRateText.getTaxRateText();

                //拼接
                String gmvResultText = null;

                //下一个Gmv
                String nextGmv = null;
                //下一个费率
                String nextRate = null;
                if(i < (taxRateTextList.size() - 1)){
                    //下一个
                    TieredTaxRateText nextRateVo = taxRateTextList.get(i + 1);

                    nextGmv = nextRateVo.getGmvText();
                    nextRate = nextRateVo.getTaxRateText();

                }
                if(i == 0){
                    gmvResultText = nextGmv + "以下";
                } else if(i >= (taxRateTextList.size() - 1)){
                    gmvResultText = currentGmv + "（含）以上";
                } else {
                    gmvResultText = currentGmv + "（含） - " + nextGmv;
                }

                TieredTaxRateText finalRateVo = new TieredTaxRateText();

                finalRateVo.setGmvText(gmvResultText);
                finalRateVo.setTaxRateText(currentRate);

                resultRateTextList.add(finalRateVo);

                i++;
            }

        }

        return resultRateTextList;
    }

    @Override
    public void addGmvRate(Map<String, Object> pdfFormMap, Long enterpriseId){
        TytInvoiceTaxRateRule invoiceTaxRateRule = tytUserIdentityAuthService.getUserRateRule(enterpriseId);

        String tieredTaxRate = invoiceTaxRateRule.getTieredTaxRate();

        List<TieredTaxRateText> rateList = this.parseTaxRate(tieredTaxRate);

        if(CollectionUtils.isNotEmpty(rateList)){
            int i = 1;
            for(TieredTaxRateText oneRateConfig: rateList){
                String gmvRange = oneRateConfig.getGmvText();
                String freightRate = oneRateConfig.getTaxRateText();

                String gmvRangeKey = "gmvRange" + i;
                String freightRateKey = "freightRate" + i;

                pdfFormMap.put(gmvRangeKey, gmvRange);
                pdfFormMap.put(freightRateKey, freightRate);

                i++;
            }
        }

    }

    private String getContractUrl(User dbUser, TytInvoiceEnterprise dbEnterprise, TytInvoiceEnterpriseSign invoiceEnterpriseSign) {
        log.info("getContractPreview_time_spend_005");
        File contractLocalFile = this.getContractLocalFile();

        byte[] contractBytes = null;
        try {
            contractBytes = FileUtils.readFileToByteArray(contractLocalFile);
        } catch (IOException e) {
            throw TytException.createException(e);
        }

        String contractBase64 = CommonUtil.encodeBase64(contractBytes);

        CreatePdfReq req = new CreatePdfReq();
        req.setPdfFileBase64(contractBase64);
        req.setPdfFileName(contractLocalFile.getName());

        Long enterpriseId = dbEnterprise.getId();

        ContractPdfFormVo pdfFormVo = this.getContractPdfForm(dbUser, dbEnterprise, invoiceEnterpriseSign);
        Map<String, Object> pdfFormMap = CommonUtil.objectToMap(pdfFormVo);
        this.addGmvRate(pdfFormMap, enterpriseId);
        log.info("getContractPreview_time_spend_006");
        req.setTxtFields(pdfFormMap);

        CreatePdfVO pdf = commonApiService.createPdf(req);
        log.info("getContractPreview_time_spend_007");
        if (pdf == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同生成失败，请重试！"));
        }

        String pdfBase64 = pdf.getResultStreamBase64();

        if (StringUtils.isBlank(pdfBase64)) {
            throw TytException.createException(ResponseEnum.request_error.info("合同生成失败，请重试！"));
        }

        byte[] pdfBytes = CommonUtil.decodeBase64(pdfBase64);

        String fileType = "enterprise/blank";
        String fileName = "contract.pdf";

        String fileKey = aliOssClient.createFullPath(fileType, fileName, true);

        String contractBlankUrl = aliOssClient.upload(pdfBytes, fileKey, null, false);
        log.info("getContractPreview_time_spend_008");
        return contractBlankUrl;
    }

    /**
     * 生成新的合同编码
     *
     * @return String
     */
    private String generatorContractNumber() {
        // EFA20240124000001,20240124为当天日期，最后面6位递增，点击后新建窗口查看签署后的协议内容。
        Date nowTime = new Date();
        String dayShort = DateUtil.dateToString(nowTime, DateUtil.day_format_short);

        String contractNumberKey = RedisKeyConstant.getContractNumberKey();
        Long incr = RedisUtil.incr(contractNumberKey);
        RedisUtil.expire(contractNumberKey, 24 * 60 * 60);

        if (incr > 999999) {
            throw TytException.createException(ResponseEnum.request_error.info("已达今日最大次数，请明日重试"));
        }

        String fitNum = String.format("%03d", incr);
        String contractNumber = "EFA" + dayShort + fitNum;

        log.info("generatorContractNumber : " + contractNumber);

        return contractNumber;
    }

    /**
     * 合同结束日期
     *
     * @param contractStartTime contractStartTime
     * @return Date
     */
    private Date getContractEndTime(Date contractStartTime) {
        Date contractEndTime = DateUtil.addTime(contractStartTime, Calendar.YEAR, 1);

        return contractEndTime;
    }

    /**
     * 生成pdf 预览公共参数
     *
     * @param dbUser                dbUser
     * @param dbEnterprise          dbEnterprise
     * @param invoiceEnterpriseSign invoiceEnterpriseSign
     */
    private void setPublicPreviewSign(User dbUser, TytInvoiceEnterprise dbEnterprise, TytInvoiceEnterpriseSign invoiceEnterpriseSign) {

        final Date nowTime = new Date();
        String contractNumber = this.generatorContractNumber();

        Date contractStartTime = nowTime;
        Date contractEndTime = this.getContractEndTime(contractStartTime);

        invoiceEnterpriseSign.setEnterpriseId(dbEnterprise.getId());
        invoiceEnterpriseSign.setEnterpriseName(dbEnterprise.getEnterpriseName());
        invoiceEnterpriseSign.setEnterpriseCreditCode(dbEnterprise.getEnterpriseCreditCode());
        invoiceEnterpriseSign.setLegalPersonName(dbEnterprise.getLegalPersonName());
        invoiceEnterpriseSign.setLegalPersonPhone(dbEnterprise.getLegalPersonPhone());
        invoiceEnterpriseSign.setLegalPersonCard(dbEnterprise.getLegalPersonCard());
        invoiceEnterpriseSign.setEnable(GlobalStatusEnum.yes.getCode());
        invoiceEnterpriseSign.setUserId(dbEnterprise.getCertigierUserId());
        invoiceEnterpriseSign.setContractName("企业服务框架协议");
        invoiceEnterpriseSign.setContractNumber(contractNumber);

        invoiceEnterpriseSign.setContractStartTime(contractStartTime);
        invoiceEnterpriseSign.setContractEndTime(contractEndTime);

        invoiceEnterpriseSign.setActiveAuthStatus(SignPublicStatusEnum.INIT.getCode());
        invoiceEnterpriseSign.setModifyTime(nowTime);

        String contractBlankUrl = this.getContractUrl(dbUser, dbEnterprise, invoiceEnterpriseSign);
        invoiceEnterpriseSign.setContractBlankUrl(contractBlankUrl);

        //签署人实名手机号(不一定是登录账号)
        invoiceEnterpriseSign.setUserPhone(null);

        //e签宝三方企业账号id
        invoiceEnterpriseSign.setSignAccountId(null);
        //e签宝三方企业印章地址
        invoiceEnterpriseSign.setSignSealUrl(null);
        //e签宝 flowId
        invoiceEnterpriseSign.setSignFlowId(null);
        //授权激活类型（1法人授权;2委托书授权）
        invoiceEnterpriseSign.setSignType(null);
        //签署意愿确认时间
        invoiceEnterpriseSign.setSignConfirmTime(null);
        //授权委托书URL
        invoiceEnterpriseSign.setSignAuthUrl(null);
        //授权委托书URL生成日期
        invoiceEnterpriseSign.setSignAuthTime(null);
        //签署完成的协议文件URL
        invoiceEnterpriseSign.setContractSignUrl(null);

    }

    @Override
    public TytInvoiceEnterpriseSign newPreviewSign(User dbUser, TytInvoiceEnterprise dbEnterprise) {

        final Date nowTime = new Date();

        TytInvoiceEnterpriseSign invoiceEnterpriseSign = new TytInvoiceEnterpriseSign();

        this.setPublicPreviewSign(dbUser, dbEnterprise, invoiceEnterpriseSign);

        invoiceEnterpriseSign.setContractStatus(ContractStatusEnum.INIT.getCode());
        invoiceEnterpriseSign.setSignerVerifyStatus(SignPublicStatusEnum.INIT.getCode());
        invoiceEnterpriseSign.setSignConfirmStatus(SignPublicStatusEnum.INIT.getCode());
        invoiceEnterpriseSign.setCreateTime(nowTime);

        int i = tytInvoiceEnterpriseSignMapper.insertSelective(invoiceEnterpriseSign);

        return invoiceEnterpriseSign;
    }

    @Override
    public TytInvoiceEnterpriseSign updatePreviewSign(User dbUser, TytInvoiceEnterprise dbEnterprise, TytInvoiceEnterpriseSign dbEnterpriseSign) {

        this.setPublicPreviewSign(dbUser, dbEnterprise, dbEnterpriseSign);

        int i = tytInvoiceEnterpriseSignMapper.updateByPrimaryKeySelective(dbEnterpriseSign);

        return dbEnterpriseSign;
    }

    /**
     * 通过签约状态，重置企业认证状态
     *
     * @param dbEnterprise   dbEnterprise
     * @param enterpriseSign enterpriseSign
     */
    private void resetEnterpriseWithSign(User dbUser, TytInvoiceEnterprise dbEnterprise, TytInvoiceEnterpriseSign enterpriseSign) {

        dbEnterprise.setSignType(null);
        dbEnterprise.setContractNo(enterpriseSign.getContractNumber());
        dbEnterprise.setContractStartTime(enterpriseSign.getContractStartTime());
        dbEnterprise.setContractEndTime(enterpriseSign.getContractEndTime());
        dbEnterprise.setCertigierUserId(dbUser.getId());
        dbEnterprise.setCertigierUserName(dbUser.getTrueName());
        dbEnterprise.setCertigierUserPhone(dbUser.getCellPhone());
        dbEnterprise.setContractVerifyStatus(enterpriseSign.getContractStatus());
        //dbEnterprise.setContractAuditTime();

        dbEnterprise.setContractFinalStatus(SignPublicStatusEnum.INIT.getCode());
        dbEnterprise.setContractFinalReason(null);

        dbEnterprise.setAccountStatus(null);
        dbEnterprise.setManagerFlag(GlobalStatusEnum.no.getCode());

        tytInvoiceEnterpriseMapper.updateByPrimaryKey(dbEnterprise);
    }

    private User getByUserId(Long userId) {

        User dbUser = null;
        try {
            dbUser = userService.getByUserId(userId);
        } catch (Exception e) {
            throw TytException.createException(e);
        }

        if (dbUser == null) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }

        return dbUser;
    }

    private void checkContractModify(TytInvoiceEnterpriseSign enterpriseSign){
        Integer contractStatus = enterpriseSign.getContractStatus();
        if(ContractStatusEnum.WAITING.equalsCode(contractStatus)
                || ContractStatusEnum.SUCCESS.equalsCode(contractStatus)){
            throw TytException.createException(ResponseEnum.request_error.info("当前协议已生成，如需重新签署，请联系客服！"));
        }

    }

    @Override
    public EnterpriseContractVo getContractPreview(Long userId) {
        log.info("getContractPreview_time_spend_001");
        this.checkContractDayCount(userId);

        User dbUser = this.getByUserId(userId);
        TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getByUserId(userId + "");
        TytInvoiceEnterprise dbEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);

        this.checkContractPreview(userIdentityAuth, dbEnterprise);

        Long enterpriseId = dbEnterprise.getId();
        log.info("getContractPreview_time_spend_001");
        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getEnableEnterpriseSign(enterpriseId);

        if (enterpriseSign == null) {
            enterpriseSign = this.newPreviewSign(dbUser, dbEnterprise);
            log.info("getContractPreview_time_spend_002");
        } else {

            String signAuthUrl = enterpriseSign.getSignAuthUrl();
            if(StringUtils.isNotBlank(signAuthUrl)){
                EnterpriseContractVo contractVo = ConvertUtil.beanConvert(enterpriseSign, new EnterpriseContractVo());
                return contractVo;
            }

            Integer contractStatus = enterpriseSign.getContractStatus();
            if(ContractStatusEnum.WAITING.equalsCode(contractStatus)
                    || ContractStatusEnum.SUCCESS.equalsCode(contractStatus)){
                EnterpriseContractVo contractVo = ConvertUtil.beanConvert(enterpriseSign, new EnterpriseContractVo());
                return contractVo;
            }

            enterpriseSign = this.updatePreviewSign(dbUser, dbEnterprise, enterpriseSign);
            log.info("getContractPreview_time_spend_003");
        }

        this.resetEnterpriseWithSign(dbUser, dbEnterprise, enterpriseSign);
        this.incrContractDayCount(userId);
        log.info("getContractPreview_time_spend_004");
        EnterpriseContractVo contractVo = ConvertUtil.beanConvert(enterpriseSign, new EnterpriseContractVo());
        return contractVo;
    }

    @Override
    public EnterpriseContractVo agreeSignContract(Long userId) {

        Date nowTime = new Date();
        User dbUser = this.getByUserId(userId);
        TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getByUserId(userId + "");
        TytInvoiceEnterprise dbEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);

        this.checkContractPreview(userIdentityAuth, dbEnterprise);

        Long enterpriseId = dbEnterprise.getId();
        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getEnableEnterpriseSign(enterpriseId);

        if (enterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }

        this.checkContractModify(enterpriseSign);

        String signAccountId = enterpriseSign.getSignAccountId();
        String signSealUrl = enterpriseSign.getSignSealUrl();
        if (StringUtils.isBlank(signAccountId) || StringUtils.isBlank(signSealUrl)) {

            //创建账号或签章
            if (StringUtils.isBlank(signAccountId)) {
                //创建账号
                String enterpriseCreditCode = dbEnterprise.getEnterpriseCreditCode();
                String enterpriseName = dbEnterprise.getEnterpriseName();

                AddAccountReq accountReq = new AddAccountReq();
                accountReq.setName(enterpriseName);
                accountReq.setRegType(OrganRegTypeEnum.MERGE.name());
                accountReq.setOrganCode(enterpriseCreditCode);
                accountReq.setUserType(0);

                AddAccountVO addAccountVO = commonApiService.addEsignAcount(accountReq);
                signAccountId = addAccountVO.getAccountId();

                if (StringUtils.isBlank(signAccountId)) {
                    throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
                }

                enterpriseSign.setSignAccountId(signAccountId);

                signSealUrl = null;

            }

            if (StringUtils.isBlank(signSealUrl)) {
                //创建签章
                TemplateSealReq sealReq = new TemplateSealReq();
                sealReq.setAccountId(signAccountId);
                sealReq.setTemplateType(OrganizeTemplateTypeEnum.STAR.name());
                sealReq.setColor(SealColorEnum.RED.name());

                TemplateSealVO templateSealVO = commonApiService.addTemplateSeal(sealReq);

                String sealDataBase64 = templateSealVO.getSealData();

                byte[] sealBytes = CommonUtil.decodeBase64(sealDataBase64);

                String fileType = "enterprise/seal";
                String fileName = "seal.png";

                String fileKey = aliOssClient.createFullPath(fileType, fileName, true);

                signSealUrl = aliOssClient.upload(sealBytes, fileKey, null, true);

                enterpriseSign.setSignSealUrl(signSealUrl);
            }

            enterpriseSign.setModifyTime(nowTime);
            tytInvoiceEnterpriseSignMapper.updateByPrimaryKey(enterpriseSign);
        }

        EnterpriseContractVo contractVo = ConvertUtil.beanConvert(enterpriseSign, new EnterpriseContractVo());
        return contractVo;
    }

    @Override
    public void signValidSend(Long userId, String userPhone) {
        Date nowTime = new Date();
        User dbUser = this.getByUserId(userId);

        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);
        if (enterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }

        Integer signerVerifyStatus = enterpriseSign.getSignerVerifyStatus();
        if(SignPublicStatusEnum.SUCCESS.equalsCode(signerVerifyStatus)){
            throw TytException.createException(ResponseEnum.request_error.info("签署人已认证!"));
        }

        String idCard = dbUser.getIdCard();
        String trueName = dbUser.getTrueName();

        Telecom3FactorsDTO req = new Telecom3FactorsDTO();
        req.setIdNo(idCard);
        req.setName(trueName);
        req.setMobileNo(userPhone);

        req.setContextId(userId + "");
        //String notifyUrl = CommonUtil.joinPath(commonApiService.getPublicPlatHost(), RemoteApiConstant.EsignCallBack.SIGN_VALID);
        //req.setNotifyUrl(notifyUrl);

        Telecom3FactorsVO factorsVO = commonApiService.telecom3Factors(req);

        String flowId = factorsVO.getFlowId();

        if (StringUtils.isBlank(flowId)) {
            throw TytException.createException(ResponseEnum.request_error.info("请求失败，请稍后重试!"));
        }

        enterpriseSign.setUserPhone(userPhone);
        enterpriseSign.setSignFlowId(flowId);
        enterpriseSign.setSignerVerifyStatus(SignPublicStatusEnum.WAITING.getCode());
        enterpriseSign.setModifyTime(nowTime);
        tytInvoiceEnterpriseSignMapper.updateByPrimaryKey(enterpriseSign);

    }

    @Override
    public void signValidCheck(Long userId, String validCode) {
        Date nowTime = new Date();
        User dbUser = this.getByUserId(userId);

        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);
        if (enterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }
        Integer signerVerifyStatus = enterpriseSign.getSignerVerifyStatus();
        if(SignPublicStatusEnum.SUCCESS.equalsCode(signerVerifyStatus)){
            throw TytException.createException(ResponseEnum.request_error.info("签署人已认证!"));
        }

        String signFlowId = enterpriseSign.getSignFlowId();

        if (StringUtils.isBlank(signFlowId)) {
            throw TytException.createException(ResponseEnum.request_error.info("请先发送验证码！"));
        }

        Telecom3FactorsVerifyDTO req = new Telecom3FactorsVerifyDTO();

        req.setFlowId(signFlowId);
        req.setAuthcode(validCode);

        Object verify = commonApiService.telecom3FactorsVerify(req);
        enterpriseSign.setSignerVerifyStatus(SignPublicStatusEnum.SUCCESS.getCode());
        enterpriseSign.setModifyTime(nowTime);

        tytInvoiceEnterpriseSignMapper.updateByPrimaryKeySelective(enterpriseSign);

        log.info("signValidCheck: {}", JSON.toJSON(verify));
    }

    @Override
    public void signValidCallBack(SignValidCallVo signValidCallVo) {

        final Date nowTime = new Date();
        String flowId = signValidCallVo.getFlowId();
        Boolean success = signValidCallVo.getSuccess();
        String contextId = signValidCallVo.getContextId();

        if (StringUtil.hasBlank(flowId, contextId) || success == null) {
            log.error("signValidCallBack_error : {}", JSON.toJSON(signValidCallVo));
        }
        Long userId = Long.parseLong(contextId);

        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);
        if (enterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }

        this.checkContractModify(enterpriseSign);

        String signFlowId = enterpriseSign.getSignFlowId();

        if (StringUtils.equals(flowId, signFlowId)) {
            //回调数据正确
            int signerVerifyStatus = Boolean.TRUE.equals(success) ? SignPublicStatusEnum.SUCCESS.getCode() : SignPublicStatusEnum.FAIL.getCode();
            enterpriseSign.setSignerVerifyStatus(signerVerifyStatus);
            enterpriseSign.setModifyTime(nowTime);

            tytInvoiceEnterpriseSignMapper.updateByPrimaryKeySelective(enterpriseSign);
        } else {
            throw TytException.createException(ResponseEnum.request_error.info("flowId 不匹配"));
        }
    }

    @Override
    public EnterpriseContractVo getEnterpriseSign(Long userId) {
        TytInvoiceEnterpriseSign enterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);
        if (enterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }

        EnterpriseContractVo contractVo = ConvertUtil.beanConvert(enterpriseSign, new EnterpriseContractVo());
        return contractVo;
    }

    @Override
    @Transactional(transactionManager = TransactionConstant.MYBATIS, rollbackFor = Exception.class)
    public EnterpriseContractVo legalRealValid(Long userId, String legalPersonCard) {

        User dbUser = this.getByUserId(userId);

        TytInvoiceEnterprise invoiceEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);
        if (invoiceEnterprise == null) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }
        TytInvoiceEnterpriseSign dbEnterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);

        if (dbEnterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }
        Integer signerVerifyStatus = dbEnterpriseSign.getSignerVerifyStatus();

        if (!SignPublicStatusEnum.SUCCESS.equalsCode(signerVerifyStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("请先进行签署人验证!"));
        }

        Integer activeAuthStatus = dbEnterpriseSign.getActiveAuthStatus();
        if (SignPublicStatusEnum.SUCCESS.equalsCode(activeAuthStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("已授权成功，请勿重复操作!"));
        }

        String loginIdCard = dbUser.getIdCard();

        if(!StringUtils.equals(legalPersonCard, loginIdCard)){
            throw TytException.createException(ResponseEnum.request_error.info("身份证号填写错误，请核实后填写!"));
        }

        //校验人脸状态
        TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getByUserId(userId.toString());
        Integer faceVerify = userIdentityAuth.getFaceVerify();

        if(!VerifyStatusEnum.SUCCESS.equalsCode(faceVerify)){
            throw TytException.createException(PlatResponseEnum.NOT_FACE_VERIFY.info());
        }

        dbEnterpriseSign.setLegalPersonCard(legalPersonCard);
        invoiceEnterprise.setLegalPersonCard(legalPersonCard);

        EnterpriseFourFactorsReq req = new EnterpriseFourFactorsReq();
        req.setName(dbEnterpriseSign.getEnterpriseName());
        req.setOrgCode(dbEnterpriseSign.getEnterpriseCreditCode());
        //req.setLegalRepCertType();
        req.setLegalRepName(dbEnterpriseSign.getLegalPersonName());
        req.setLegalRepIdNo(legalPersonCard);
        //req.setRepetition();
        req.setContextId(userId + "");
        //req.setNotifyUrl();
        req.setAgentFlowId(dbEnterpriseSign.getSignFlowId());

        EnterpriseFourFactorsVO enterpriseFourFactorsVO = commonApiService.enterpriseFourFactors(req);

        final Date nowTime = new Date();

        String flowId = enterpriseFourFactorsVO.getFlowId();

        dbEnterpriseSign.setEnterpriseFlowId(flowId);

        dbEnterpriseSign.setSignType(SignTypeEnum.LEGAL.getCode());
        dbEnterpriseSign.setActiveAuthStatus(SignPublicStatusEnum.SUCCESS.getCode());
        dbEnterpriseSign.setSignAuthUrl(null);
        dbEnterpriseSign.setSignAuthTime(null);
        dbEnterpriseSign.setModifyTime(nowTime);

        //企业主表
        invoiceEnterprise.setSignType(SignTypeEnum.LEGAL.getCode());
        invoiceEnterprise.setMtime(nowTime);

        //判断是否是当前管理员
        this.manageEnterpriseContract(invoiceEnterprise, dbEnterpriseSign);

        EnterpriseContractVo contractVo = ConvertUtil.beanConvert(dbEnterpriseSign, new EnterpriseContractVo());
        return contractVo;
    }

    @Override
    public void confirmValidSend(Long userId) {
        Date nowTime = new Date();
        User dbUser = this.getByUserId(userId);

        TytInvoiceEnterpriseSign dbEnterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);

        if (dbEnterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }

        Integer signConfirmStatus = dbEnterpriseSign.getSignConfirmStatus();
        if (SignPublicStatusEnum.SUCCESS.equalsCode(signConfirmStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("签署意愿已确认，无需重复操作!"));
        }
        Integer activeAuthStatus = dbEnterpriseSign.getActiveAuthStatus();
        if (!SignPublicStatusEnum.SUCCESS.equalsCode(activeAuthStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("请先进行企业激活授权!"));
        }

        String signAccountId = dbEnterpriseSign.getSignAccountId();
        String cellPhone = dbUser.getCellPhone();

        SendSignMobileCode3rdReq mobileCode3rdReq = new SendSignMobileCode3rdReq();
        mobileCode3rdReq.setAccountId(signAccountId);
        mobileCode3rdReq.setMobile(cellPhone);

        Object o = commonApiService.sendSignMobileCode3rd(mobileCode3rdReq);

        dbEnterpriseSign.setSignConfirmStatus(SignPublicStatusEnum.WAITING.getCode());
        dbEnterpriseSign.setModifyTime(nowTime);

        tytInvoiceEnterpriseSignMapper.updateByPrimaryKeySelective(dbEnterpriseSign);
    }

    private LocalSafeSignPDF3rdVO doUserSignSeal(User dbUser, TytInvoiceEnterpriseSign dbEnterpriseSign, String validCode){

        String cellPhone = dbUser.getCellPhone();
        String signAccountId = dbEnterpriseSign.getSignAccountId();

        String signSealUrl = dbEnterpriseSign.getSignSealUrl();
        log.info("download_seal_img : {}", signSealUrl);
        String sealBase64 = HttpClientUtil.downloadBase64(signSealUrl);

        log.info("doUserSignSeal_time_spend_001");

        String contractBlankUrl = dbEnterpriseSign.getContractBlankUrl();
        String contractBlankBase64 = HttpClientUtil.downloadBase64(contractBlankUrl);

        log.info("doUserSignSeal_time_spend_002");

        //签署PDF文档信息
        LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPdfReq pdfInfo = new LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPdfReq();
        pdfInfo.setFileBase64(contractBlankBase64);

        //签章位置信息
        LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPosReq posInfo = new LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPosReq();
        posInfo.setPosType(EsignPosTypeEnum.KEYWORD.getCode());
        posInfo.setPosPage("11,16,17");
        posInfo.setPosX(20);
        posInfo.setPosY(0);
        posInfo.setKey(EsignConstant.USER_SEAL_KEYWORD);
        posInfo.setWidth(EsignConstant.SEAL_WIDTH);
        //posInfo.setQrCodeSign();
        //posInfo.setCancellingSign();
        posInfo.setAddSignTime(false);

        LocalSafeSignPDF3rdReq userSignPdf = new LocalSafeSignPDF3rdReq();
        userSignPdf.setAccountId(signAccountId);
        userSignPdf.setSignType(SignSealTypeEnum.KEY.getCode());
        userSignPdf.setMobile(cellPhone);
        userSignPdf.setCode(validCode);
        userSignPdf.setSealData(sealBase64);
        userSignPdf.setPdfInfo(pdfInfo);
        userSignPdf.setPosInfo(posInfo);

        LocalSafeSignPDF3rdVO signPDF3rdVO = commonApiService.localSafeSignPDF3rd(userSignPdf);

        log.info("doUserSignSeal_time_spend_003");
        return signPDF3rdVO;
    }

    /**
     * 平台自身签章.
     * @param signPDF3rdVO signPDF3rdVO
     * @return LocalSignPdfVO
     */
    @Override
    public LocalSafeSignPDF3rdVO doSelfSignSeal(LocalSafeSignPDF3rdVO signPDF3rdVO){
        log.info("doSelfSignSeal ... ");

        TytSelfCompanySign selfCompanySign = this.getSelfCompanySign();

        String signAccountId = selfCompanySign.getSignAccountId();
        String signSealUrl = selfCompanySign.getSignSealUrl();
        log.info("download_self_seal_img : {}", signSealUrl);
        String sealBase64 = HttpClientUtil.downloadBase64(signSealUrl);

        log.info("doSelfSignSeal_time_spend_001");

        String contractBlankBase64 = signPDF3rdVO.getPdfBase64();

        //签署PDF文档信息
        SilentUserSealSignReq.LocalSafeSignPDF3rdPdfReq pdfInfo = new SilentUserSealSignReq.LocalSafeSignPDF3rdPdfReq();
        pdfInfo.setFileBase64(contractBlankBase64);

        //签章位置信息
        SilentUserSealSignReq.LocalSafeSignPDF3rdPosReq posInfo = new SilentUserSealSignReq.LocalSafeSignPDF3rdPosReq();
        posInfo.setPosType(EsignPosTypeEnum.KEYWORD.getCode());
        posInfo.setPosPage("11,16");
        posInfo.setPosX(120);
        posInfo.setPosY(-30);
        posInfo.setKey(EsignConstant.TYT_SEAL_KEYWORD);
        posInfo.setWidth(EsignConstant.SEAL_WIDTH);
        //posInfo.setQrCodeSign();
        //posInfo.setCancellingSign();
        posInfo.setAddSignTime(false);

        SilentUserSealSignReq userSignPdf = new SilentUserSealSignReq();
        userSignPdf.setAccountId(signAccountId);
        userSignPdf.setSignType(SignSealTypeEnum.KEY.name());
        userSignPdf.setSealData(sealBase64);
        userSignPdf.setPdfInfo(pdfInfo);
        userSignPdf.setPosInfo(posInfo);

        try {
            Response<InternalWebResult<LocalSafeSignPDF3rdVO>> pdfResp = apiEsignSignClient.silentUserSealSign(userSignPdf).execute();
            log.info("doUserSignSeal_time_spend_003");
            LocalSafeSignPDF3rdVO signPdfVo = InternalClientUtil.getEsignDataDetail(pdfResp);
            return signPdfVo;
        } catch (Exception e) {
            throw TytException.createException(e);
        }

    }

    @Override
    @Transactional(transactionManager = TransactionConstant.MYBATIS, rollbackFor = Exception.class)
    public EnterpriseContractVo confirmValidCheck(Long userId, String validCode) {
        Date nowTime = new Date();
        User dbUser = this.getByUserId(userId);

        TytInvoiceEnterprise dbInvoiceEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);
        if(dbInvoiceEnterprise == null){
            throw TytException.createException(ResponseEnum.request_error.info());
        }

        Integer infoVerifyStatus = dbInvoiceEnterprise.getInfoVerifyStatus();
        if(!EnterpriseVerifyNodeStatusEnum.VERIFING.equalsCode(infoVerifyStatus)
                && !EnterpriseVerifyNodeStatusEnum.VERIFIED.equalsCode(infoVerifyStatus) ){
            throw TytException.createException(ResponseEnum.request_error.info("请先提交企业认证!"));
        }

        TytInvoiceEnterpriseSign dbEnterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);

        if (dbEnterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }
        log.info("confirmValidCheck_time_spend_001");
        Integer signConfirmStatus = dbEnterpriseSign.getSignConfirmStatus();
        if (SignPublicStatusEnum.SUCCESS.equalsCode(signConfirmStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("签署意愿已确认，无需重复操作!"));
        }

        if (!SignPublicStatusEnum.WAITING.equalsCode(signConfirmStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("请先获取签署意愿确认验证码!"));
        }

        LocalSafeSignPDF3rdVO signPDF3rdVo = this.doUserSignSeal(dbUser, dbEnterpriseSign, validCode);

        log.info("confirmValidCheck_time_spend_002");

        LocalSafeSignPDF3rdVO sealIdPdf = this.doSelfSignSeal(signPDF3rdVo);

        log.info("confirmValidCheck_time_spend_003");

        String signServiceId = sealIdPdf.getSignServiceId();
        String pdfBase64 = sealIdPdf.getPdfBase64();

        byte[] pdfBytes = CommonUtil.decodeBase64(pdfBase64);

        String fileType = "enterprise/sign";
        String fileName = "contract.pdf";

        String fileKey = aliOssClient.createFullPath(fileType, fileName, true);
        String contractSignUrl = aliOssClient.upload(pdfBytes, fileKey, null, false);

        log.info("confirmValidCheck_time_spend_004");

        Integer contractFinalStatus = null;
        if(EnterpriseVerifyNodeStatusEnum.VERIFING.equalsCode(infoVerifyStatus)){
            //认证中
            log.info("enterprise_info_status is verifing : {}", userId);

            contractFinalStatus = ContractFinalStatusEnum.WAITING.getCode();

        }else if(EnterpriseVerifyNodeStatusEnum.VERIFIED.equalsCode(infoVerifyStatus)){

            contractFinalStatus = ContractFinalStatusEnum.SUCCESS.getCode();

            Integer invoiceCloseFlag = dbInvoiceEnterprise.getInvoiceCloseFlag();

            if(GlobalStatusEnum.yes.equalsCode(invoiceCloseFlag)){
                //手动关闭时
                dbInvoiceEnterprise.setAccountStatus(GlobalStatusEnum.no.getCode());
            } else {
                dbInvoiceEnterprise.setAccountStatus(GlobalStatusEnum.yes.getCode());
            }

            Date contractAuditTime = dbInvoiceEnterprise.getContractAuditTime();
            if(contractAuditTime == null){
                dbInvoiceEnterprise.setContractAuditTime(nowTime);
            }
        }

        //认证成功
        dbEnterpriseSign.setContractStatus(ContractStatusEnum.SUCCESS.getCode());
        dbEnterpriseSign.setContractSignUrl(contractSignUrl);
        dbEnterpriseSign.setSignServiceId(signServiceId);

        dbEnterpriseSign.setSignConfirmStatus(SignPublicStatusEnum.SUCCESS.getCode());
        dbEnterpriseSign.setSignConfirmTime(nowTime);
        dbEnterpriseSign.setModifyTime(nowTime);
        tytInvoiceEnterpriseSignMapper.updateByPrimaryKeySelective(dbEnterpriseSign);

        dbInvoiceEnterprise.setContractFinalStatus(contractFinalStatus);
        dbInvoiceEnterprise.setContractVerifyStatus(ContractStatusEnum.SUCCESS.getCode());
        dbInvoiceEnterprise.setContractFinalReason("");
        dbInvoiceEnterprise.setMtime(nowTime);
        tytInvoiceEnterpriseMapper.updateByPrimaryKeySelective(dbInvoiceEnterprise);

        EnterpriseContractVo contractVo = ConvertUtil.beanConvert(dbEnterpriseSign, new EnterpriseContractVo());
        contractVo.setContractFinalStatus(contractFinalStatus);

        log.info("confirmValidCheck_time_spend_005");
        return contractVo;
    }

    /**
     * 企业三要素认证
     * @param dbEnterpriseSign
     * @return
     */
    public String enterpriseThreeValid(TytInvoiceEnterpriseSign dbEnterpriseSign){

        Long enterpriseId = dbEnterpriseSign.getEnterpriseId();
        String enterpriseName = dbEnterpriseSign.getEnterpriseName();
        String enterpriseCreditCode = dbEnterpriseSign.getEnterpriseCreditCode();
        String legalPersonName = dbEnterpriseSign.getLegalPersonName();

        EnterpriseThreeFactorsReq req = new EnterpriseThreeFactorsReq();
        req.setName(enterpriseName);
        req.setOrgCode(enterpriseCreditCode);
        req.setLegalRepName(legalPersonName);
        req.setContextId(enterpriseId + "");
        //req.setNotifyUrl();

        EnterpriseThreeFactorsVO enterpriseThreeFactorsVO = commonApiService.enterpriseThreeFactors(req);

        String enterpriseFlowId = enterpriseThreeFactorsVO.getFlowId();

        if(StringUtils.isBlank(enterpriseFlowId)){
            throw TytException.createException(ResponseEnum.request_error.info("校验失败，请重试！"));
        }

        return enterpriseFlowId;

    }

    @Override
    @Transactional(transactionManager = TransactionConstant.MYBATIS, rollbackFor = Exception.class)
    public EnterpriseContractVo createActiveAuth(Long userId, String legalIdCard, String legalPhone) {

        Date nowTime = new Date();
        User dbUser = this.getByUserId(userId);

        TytInvoiceEnterprise invoiceEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);
        if (invoiceEnterprise == null) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }
        TytInvoiceEnterpriseSign dbEnterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);

        if (dbEnterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }

        Integer signerVerifyStatus = dbEnterpriseSign.getSignerVerifyStatus();
        if (!SignPublicStatusEnum.SUCCESS.equalsCode(signerVerifyStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("请先进行签署人验证!"));
        }

        Integer activeAuthStatus = dbEnterpriseSign.getActiveAuthStatus();
        if (SignPublicStatusEnum.SUCCESS.equalsCode(activeAuthStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("已授权成功，请勿重复操作!"));
        }

        String signAuthUrl = dbEnterpriseSign.getSignAuthUrl();

        if (StringUtils.isNotBlank(signAuthUrl)) {
            Date signAuthTime = dbEnterpriseSign.getSignAuthTime();
            Date validAuthTime = DateUtil.addTime(signAuthTime, Calendar.DAY_OF_MONTH, 30);
            if (nowTime.before(validAuthTime)) {
                EnterpriseContractVo contractVo = ConvertUtil.beanConvert(dbEnterpriseSign, new EnterpriseContractVo());
                return contractVo;
            }
        }

        String enterpriseFlowId = this.enterpriseThreeValid(dbEnterpriseSign);

        dbEnterpriseSign.setEnterpriseFlowId(enterpriseFlowId);

        String userName = dbUser.getTrueName();
        String idCard = dbUser.getIdCard();

        LegalRepSignReq req = new LegalRepSignReq();

        req.setFlowId(enterpriseFlowId);
        req.setAgentName(userName);
        req.setAgentIdNo(idCard);
        req.setMobileNo(legalPhone);
        req.setLegalRepIdNo(legalIdCard);
        req.setRedirectUrl(null);

        Object o = commonApiService.legalRepSign(req);

        SignUrlResp authSignUrl = commonApiService.getAuthSignUrl(enterpriseFlowId);
        String signUrl = authSignUrl.getSignUrl();

        log.info("createActiveAuth_signUrl : {}", signUrl);

        dbEnterpriseSign.setLegalPersonCard(legalIdCard);
        dbEnterpriseSign.setLegalPersonPhone(legalPhone);
        dbEnterpriseSign.setSignType(SignTypeEnum.AUTH.getCode());
        dbEnterpriseSign.setActiveAuthStatus(SignPublicStatusEnum.WAITING.getCode());
        dbEnterpriseSign.setSignAuthUrl(signUrl);
        dbEnterpriseSign.setSignAuthTime(nowTime);

        dbEnterpriseSign.setModifyTime(nowTime);
        tytInvoiceEnterpriseSignMapper.updateByPrimaryKeySelective(dbEnterpriseSign);

        invoiceEnterprise.setLegalPersonCard(legalIdCard);
        invoiceEnterprise.setLegalPersonPhone(legalPhone);
        invoiceEnterprise.setSignType(SignTypeEnum.AUTH.getCode());
        invoiceEnterprise.setMtime(nowTime);
        tytInvoiceEnterpriseMapper.updateByPrimaryKeySelective(invoiceEnterprise);

        EnterpriseContractVo contractVo = ConvertUtil.beanConvert(dbEnterpriseSign, new EnterpriseContractVo());
        return contractVo;
    }

    private void checkManagerEnterprise(String enterpriseCreditCode, Long excludeEnterpriseId){

        TytInvoiceEnterprise managerEnterprise = tytInvoiceEnterpriseMapper.getManagerEnterprise(enterpriseCreditCode, excludeEnterpriseId);

        if (managerEnterprise != null) {
            //企业已有其他管理员
            String certigierUserName = managerEnterprise.getCertigierUserName();
            String certigierUserPhone = managerEnterprise.getCertigierUserPhone();
            this.throwManageWarnMsg(certigierUserName, certigierUserPhone);
        }
    }

    /**
     * 设置企业管理员
     * @param invoiceEnterprise invoiceEnterprise
     * @param dbEnterpriseSign dbEnterpriseSign
     */
    private void manageEnterpriseContract(TytInvoiceEnterprise invoiceEnterprise,
                                          TytInvoiceEnterpriseSign dbEnterpriseSign) {

        final Date nowTime = new Date();

        //成功授权
        Long enterpriseId = dbEnterpriseSign.getEnterpriseId();
        String enterpriseCreditCode = dbEnterpriseSign.getEnterpriseCreditCode();
        this.checkManagerEnterprise(enterpriseCreditCode, enterpriseId);

        dbEnterpriseSign.setContractStatus(ContractStatusEnum.WAITING.getCode());
        dbEnterpriseSign.setActiveAuthStatus(SignPublicStatusEnum.SUCCESS.getCode());
        dbEnterpriseSign.setModifyTime(nowTime);
        tytInvoiceEnterpriseSignMapper.updateByPrimaryKey(dbEnterpriseSign);

        invoiceEnterprise.setContractVerifyStatus(ContractStatusEnum.WAITING.getCode());
        invoiceEnterprise.setManagerFlag(GlobalStatusEnum.yes.getCode());
        invoiceEnterprise.setMtime(nowTime);
        tytInvoiceEnterpriseMapper.updateByPrimaryKey(invoiceEnterprise);

    }

    /**
     * 授权点击完成次数校验
     * @param userId userId
     */
    private void checkActiveAuthCount(Long userId){
        //校验时间间隔5分钟
        String activeAuthIntervalKey = RedisKeyConstant.getActiveAuthIntervalKey(userId);
        String cacheText = RedisUtil.get(activeAuthIntervalKey);

        if(StringUtils.isNotBlank(cacheText)){
            throw TytException.createException(ResponseEnum.request_error.info("5分钟内仅可操作一次，请稍后再试!"));
        }

        //日总次数
        String dayKey = RedisKeyConstant.joinDayKey(RedisKeyConstant.ACTIVE_AUTH_DAY, userId + "");
        boolean checkResult = RedisKeyConstant.countCheck(dayKey, 20);

        if(!checkResult){
            throw TytException.createException(ResponseEnum.request_error.info("今日重试次数过多，建议明天重试!"));
        }
    }

    /**
     * 授权点击完成次数计次
     * @param userId userId
     */
    private void incrActiveAuthCount(Long userId){
        //校验时间间隔5分钟
        String activeAuthIntervalKey = RedisKeyConstant.getActiveAuthIntervalKey(userId);
        RedisKeyConstant.incrAndExpire(activeAuthIntervalKey, TimeUnitConstant.FIVE_MINUTE_SECOND);

        //日总次数
        String activeAuthDayKey = RedisKeyConstant.joinDayKey(RedisKeyConstant.ACTIVE_AUTH_DAY, userId + "");
        RedisKeyConstant.incrAndExpire(activeAuthDayKey, TimeUnitConstant.DAY_SECOND);

    }

    @Override
    @Transactional(transactionManager = TransactionConstant.MYBATIS, rollbackFor = Exception.class)
    public EnterpriseContractVo activeAuthCheck(Long userId) {

        Date nowTime = new Date();
        User dbUser = this.getByUserId(userId);

        this.checkActiveAuthCount(userId);

        TytInvoiceEnterprise invoiceEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);
        if (invoiceEnterprise == null) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }
        TytInvoiceEnterpriseSign dbEnterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);
        if (dbEnterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }
        Integer signType = dbEnterpriseSign.getSignType();
        if (!SignTypeEnum.AUTH.equalsCode(signType)) {
            throw TytException.createException(ResponseEnum.request_error.info("非授权委托书激活方式!"));
        }

        Integer activeAuthStatus = dbEnterpriseSign.getActiveAuthStatus();

        if (SignPublicStatusEnum.INIT.equalsCode(activeAuthStatus)) {
            throw TytException.createException(ResponseEnum.request_error.info("请先生成授权书!"));
        }

        if (!SignPublicStatusEnum.WAITING.equalsCode(activeAuthStatus)) {
            EnterpriseContractVo contractVo = ConvertUtil.beanConvert(dbEnterpriseSign, new EnterpriseContractVo());
            return contractVo;
        }

        String enterpriseFlowId = dbEnterpriseSign.getEnterpriseFlowId();
        LegalRepSignResultResp legalRepSignResultResp = commonApiService.legalRepSignResult(enterpriseFlowId);
        String esignStatus = legalRepSignResultResp.getStatus();

        if (AuthSignResultEnum.FINISHED.equalsCode(esignStatus)) {
            //判断是否是当前管理员
            this.manageEnterpriseContract(invoiceEnterprise, dbEnterpriseSign);

        } else if (AuthSignResultEnum.SIGNFAILED.equalsCode(esignStatus)) {

            dbEnterpriseSign.setActiveAuthStatus(SignPublicStatusEnum.FAIL.getCode());
            dbEnterpriseSign.setModifyTime(nowTime);
            tytInvoiceEnterpriseSignMapper.updateByPrimaryKeySelective(dbEnterpriseSign);
        }
        this.incrActiveAuthCount(userId);
        EnterpriseContractVo contractVo = ConvertUtil.beanConvert(dbEnterpriseSign, new EnterpriseContractVo());
        return contractVo;
    }

    @Override
    public EnterpriseContractVo cancleActiveAuth(Long userId) {

        Date nowTime = new Date();
        User dbUser = this.getByUserId(userId);

        TytInvoiceEnterprise invoiceEnterprise = enterpriseDBService.getVerifyInfoByUserId(userId);
        if (invoiceEnterprise == null) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }
        TytInvoiceEnterpriseSign dbEnterpriseSign = tytInvoiceEnterpriseSignMapper.getUserEnterpriseSign(userId);
        if (dbEnterpriseSign == null) {
            throw TytException.createException(ResponseEnum.request_error.info("合同未生成，请退出重试!"));
        }
        Integer signType = dbEnterpriseSign.getSignType();
        if (!SignTypeEnum.AUTH.equalsCode(signType)) {
            throw TytException.createException(ResponseEnum.request_error.info("非授权委托书激活方式!"));
        }

        Integer activeAuthStatus = dbEnterpriseSign.getActiveAuthStatus();

        if (!SignPublicStatusEnum.WAITING.equalsCode(activeAuthStatus)) {
            //只有授权中才可以修改
            throw TytException.createException(ResponseEnum.request_error.info("当前授权书状态不允许修改!"));
        }

        dbEnterpriseSign.setEnterpriseFlowId(null);
        dbEnterpriseSign.setLegalPersonCard(null);
        dbEnterpriseSign.setLegalPersonPhone(null);
        dbEnterpriseSign.setSignType(null);
        dbEnterpriseSign.setActiveAuthStatus(SignPublicStatusEnum.INIT.getCode());
        dbEnterpriseSign.setSignAuthUrl(null);
        dbEnterpriseSign.setSignAuthTime(null);
        dbEnterpriseSign.setModifyTime(nowTime);
        tytInvoiceEnterpriseSignMapper.updateByPrimaryKey(dbEnterpriseSign);

        EnterpriseContractVo contractVo = ConvertUtil.beanConvert(dbEnterpriseSign, new EnterpriseContractVo());
        return contractVo;
    }
}
