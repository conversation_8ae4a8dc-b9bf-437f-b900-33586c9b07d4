package com.tyt.plat.enterprise.service;

import com.tyt.model.User;
import com.tyt.plat.enterprise.pojo.vo.*;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.entity.base.TytInvoiceEnterpriseSign;
import com.tyt.plat.vo.enterprise.EnterpriseContractVo;
import com.tyt.plat.vo.enterprise.SignValidCallVo;
import com.tyt.plat.vo.esign.LocalSafeSignPDF3rdVO;

import java.util.Map;

public interface EnterpriseService {


    LicenseInfoWithOCRResp getLicenseInfoWithOCR(LicenseInfoWithOCRReq licenseInfoWithOCRReq);

    /**
     * 提交企业基本信息
     */
    void saveInfo(Long userId,VerifySaveInfoReq verifySubmitInfoReq, Integer clientSign);

    /**
     * 提交保存道路运输证信息
     */
    void saveTransportLicenseInfo(Long userId,String transportLicenseUrl);

    /**
     * 提交企业营运协议
     */
    void saveContract(Long userId,VerifySaveContractReq verifySaveContractReq);

    /**
     * 提交授权书信息
     */
    void saveCertigier(Long userId,VerifySaveCertigierReq verifySaveCertigierReq);

    /**
     * 查询企业认证状态和认证信息
     */
    VerifyStatusInfoResp getVerifyStatus(Long userId);

    /**
     * 根据企业名称查看是否存在于网络货运企业列表内
     * @param enterpriseName
     * @return
     */
    boolean checkEnterpriseInOnlineFreight(String enterpriseName);

    /**
     * 重新签署，取消之前的协议..
     * @param userId userId
     */
    void terminateContract(Long userId);

    /**
     * 驳回企业认证状态.
     * @param userId userId
     */
    void rejectEnterpriseVerify(Long userId);
    void destroyEnterpriseVerify(TytInvoiceEnterprise dbEnterprise);

    /**
     * add
     * @param pdfFormMap pdfFormMap
     * @param enterpriseId enterpriseId
     */
    void addGmvRate(Map<String, Object> pdfFormMap, Long enterpriseId);

    /**
     * 新增签约框架协议.
     * @param dbEnterprise dbEnterprise
     * @return TytInvoiceEnterpriseSign
     */
    TytInvoiceEnterpriseSign newPreviewSign(User dbUser, TytInvoiceEnterprise dbEnterprise);

    /**
     * 修改签约框架协议
     * @param dbUser dbUser
     * @param dbEnterprise dbEnterprise
     * @param dbEnterpriseSign dbEnterpriseSign
     * @return TytInvoiceEnterpriseSign
     */
    TytInvoiceEnterpriseSign updatePreviewSign(User dbUser, TytInvoiceEnterprise dbEnterprise, TytInvoiceEnterpriseSign dbEnterpriseSign);

    /**
     * 企业协议合同信息.
     * @param userId userId
     * @return EnterpriseContractVo
     */
    EnterpriseContractVo getContractPreview(Long userId) ;

    /**
     * 同意签署
     * @param userId userId
     * @return EnterpriseContractVo
     */
    EnterpriseContractVo agreeSignContract(Long userId);

    /**
     * 签署人验证码发送
     * @param userId userId
     * @param userPhone userPhone
     */
    void signValidSend(Long userId, String userPhone);

    /**
     * 签署人验证-验证码校验
     * @param userId userId
     * @param validCode validCode
     */
    void signValidCheck(Long userId, String validCode);

    /**
     * 认证回调
     * @param signValidCallVo signValidCallVo
     */
    void signValidCallBack(SignValidCallVo signValidCallVo);

    /**
     * 获取企业签约各种状态.
     * @param userId userId
     * @return EnterpriseContractVo
     */
    EnterpriseContractVo getEnterpriseSign(Long userId);

    /**
     * 法人四要素
     * @param userId userId
     * @param legalPersonCard legalPersonCard
     * @return EnterpriseContractVo
     */
    EnterpriseContractVo legalRealValid(Long userId, String legalPersonCard);

    /**
     * 签署意愿验证码发送
     * @param userId userId
     */
    void confirmValidSend(Long userId);

    LocalSafeSignPDF3rdVO doSelfSignSeal(LocalSafeSignPDF3rdVO signPDF3rdVO);

    /**
     *
     * @param userId
     * @param validCode
     * @return
     */
    EnterpriseContractVo confirmValidCheck(Long userId, String validCode);

    /**
     * 生成链接
     * @param userId userId
     * @param legalIdCard legalIdCard
     * @param legalPhone legalPhone
     * @return EnterpriseContractVo
     */
    EnterpriseContractVo createActiveAuth(Long userId, String legalIdCard, String legalPhone);

    /**
     * 校验授权是否完成.
     * @param userId userId
     * @return EnterpriseContractVo
     */
    EnterpriseContractVo activeAuthCheck(Long userId);

    /**
     * 取消授权书.
     * @param userId userId
     * @return EnterpriseContractVo
     */
    EnterpriseContractVo cancleActiveAuth(Long userId);

}
