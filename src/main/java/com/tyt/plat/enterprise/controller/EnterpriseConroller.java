package com.tyt.plat.enterprise.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.controller.BaseController;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.commons.properties.EsignConfigProperty;
import com.tyt.plat.enterprise.pojo.vo.*;
import com.tyt.plat.enterprise.service.EnterpriseService;
import com.tyt.plat.utils.EsignUtil;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.enterprise.EnterpriseContractVo;
import com.tyt.plat.vo.enterprise.SignValidCallVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@Validated
@RestController
@RequestMapping("/plat/enterprise")
public class EnterpriseConroller extends BaseController {

    private static final String CHECK_BUSINESS_SCOPE = "网络货运";

    @Autowired
    private EnterpriseService enterpriseService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Autowired
    private EsignConfigProperty esignConfigProperty;

    /**
     * 缓存中企业认证次数
     */
    public static final String TODAY_ENTERPRISE_NUM = "today_enterprise_num";

    /**
     * OCR提取营业执照信息
     * <a href="http://192.168.2.20:3300/project/37/interface/api/10895">...</a>
     */
    @PostMapping("/verify/getlicenseInfoWithOCR")
    public ResultMsgBean getLicenseInfoWithOCR(@Valid LicenseInfoWithOCRReq licenseInfoWithOCRReq, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,bindingResult.getFieldError().getDefaultMessage());
        }
        try {
            LicenseInfoWithOCRResp data = enterpriseService.getLicenseInfoWithOCR(licenseInfoWithOCRReq);
            return ResultMsgBean.successResponse(data);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR,tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("GetLicenseInfoWithOCR error,param: {} ", licenseInfoWithOCRReq, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "OCR提取营业执照信息异常");
        }
    }

    /**
     * 提交企业基本信息
     * <a href="http://192.168.2.20:3300/project/37/interface/api/10769">...</a>
     */
    @PostMapping("/verify/saveInfo")
    public ResultMsgBean saveInfo(@NotNull(message = "userId is null")Long userId,Integer forceSubmit,
                                  @Valid VerifySaveInfoReq verifySubmitInfoReq, Integer clientSign, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,bindingResult.getFieldError().getDefaultMessage());
        }
        //校验经营范围 (此处为兼容历史版本 新增forceSubmit 当forceSubmit不填或者为0时检测 经营范围是否包含货运许可证信息)
        if ((null==forceSubmit||forceSubmit==0)&&StringUtils.isNotBlank(verifySubmitInfoReq.getEnterpriseBusinessScope())
                && this.checkBusinessScope(verifySubmitInfoReq)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BUSINESS_SCOPE_ERROR,"根据国家网络货运管理条例，网络货运平台之间不得互相委托业务。");
        }
        //从redis获取今日提交次数 如果大于等于最大次数 提示错误信息 小于最大次数时 设置提交次数+1 并设置过期时间为次日凌晨
        Integer maxNum = tytConfigService.getIntValue("invoice_enterprise_submit_max_num", 5);
        int cacheSeconds = (int) TimeUtil.getTomorrowZeroSeconds();
        Integer num = RedisUtil.getObject(TODAY_ENTERPRISE_NUM + userId)==null?0: RedisUtil.getObject(TODAY_ENTERPRISE_NUM + userId);
        log.info("verify saveInfo,maxNum:{} key:{} num:{}", maxNum,TODAY_ENTERPRISE_NUM + userId,num);
        if (num >=maxNum) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.CHECK_TODAY_ENTERPRISE_NUM,"今日提交已超过"+maxNum+"次,请明天再试");
        }else{
            RedisUtil.setObject(TODAY_ENTERPRISE_NUM + userId, num + 1, cacheSeconds);
        }
        // 格式化企业名称中的英文括号
        if (verifySubmitInfoReq.getEnterpriseName().contains("(")
                || verifySubmitInfoReq.getEnterpriseName().contains(")")) {
            verifySubmitInfoReq.setEnterpriseName(verifySubmitInfoReq.getEnterpriseName().replaceAll("\\(", "（"));
            verifySubmitInfoReq.setEnterpriseName(verifySubmitInfoReq.getEnterpriseName().replaceAll("\\)", "）"));
        }
        try {
            enterpriseService.saveInfo(userId,verifySubmitInfoReq, clientSign);
            return ResultMsgBean.successResponse();
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(tytException.getErrorCode(),tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("SaveInfo error,param: {} ", verifySubmitInfoReq, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "提交企业基本信息异常");
        }
    }



    /**
     * 提交保存道路运输证信息
     * @param userId
     * @param transportLicenseUrl
     * @return ResultMsgBean
     */
    @PostMapping("/verify/saveTransportLicenseInfo")
    public ResultMsgBean saveTransportLicenseInfo(@NotNull(message = "userId is null")Long userId,@NotNull(message = "transportLicenseUrl is null")String transportLicenseUrl) {
        try {
            enterpriseService.saveTransportLicenseInfo(userId,transportLicenseUrl);
            return ResultMsgBean.successResponse();
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(tytException.getErrorCode(),tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("saveTransportLicenseInfo error,userId: {} transportLicenseUrl: {} ", userId,transportLicenseUrl, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "提交保存道路运输证信息异常");
        }
    }



    private boolean checkBusinessScope(VerifySaveInfoReq verifySubmitInfoReq) {
        if (verifySubmitInfoReq.getEnterpriseBusinessScope().contains(CHECK_BUSINESS_SCOPE)) {
            return true;
        }
        return false;
    }

    /**
     * 提交企业营运协议
     * <a href="http://192.168.2.20:3300/project/37/interface/api/10790">...</a>
     */
    @Deprecated
    @PostMapping("/verify/saveContract")
    public ResultMsgBean saveContract(@NotNull(message = "userId is null")Long userId,@Valid VerifySaveContractReq verifySaveContractReq,BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,bindingResult.getFieldError().getDefaultMessage());
        }
        try {
            log.error("old_saveContract_req_warn");
            enterpriseService.saveContract(userId,verifySaveContractReq);
            return ResultMsgBean.successResponse();
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR,tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("SaveContract error,param: {} ", verifySaveContractReq, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR,"提交企业营运协议异常");
        }
    }

    /**
     * 提交授权书信息
     * <a href="http://192.168.2.20:3300/project/37/interface/api/10797">...</a>
     */
    @Deprecated
    @PostMapping("/verify/saveCertigier")
    public ResultMsgBean saveCertigier(@NotNull(message = "userId is null")Long userId, @Valid VerifySaveCertigierReq verifySaveCertigierReq, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,bindingResult.getFieldError().getDefaultMessage());
        }
        try {
            enterpriseService.saveCertigier(userId,verifySaveCertigierReq);
            return ResultMsgBean.successResponse();
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR,tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("SaveCertigier error,param: {} ", verifySaveCertigierReq, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR,"提交授权书信息异常");
        }
    }

    /**
     * APP查看企业认证状态前调用
     * 检测当天提交企业认证次数是否符合要求
     * @param userId
     * @return ResultMsgBean
     */
    @GetMapping("/verify/checkTodayEnterpriseNum")
    public ResultMsgBean checkTodayEnterpriseNum(@NotNull(message = "userId is null")Long userId) {
        try {
            Integer maxNum = tytConfigService.getIntValue("invoice_enterprise_submit_max_num", 5);
            Integer num = RedisUtil.getObject(TODAY_ENTERPRISE_NUM + userId);
            if (num != null && num >=maxNum) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.CHECK_TODAY_ENTERPRISE_NUM,"今日提交已超过"+maxNum+"次,请明天再试");
            }
            return ResultMsgBean.successResponse();
        } catch (TytException tytException) {
            log.error("GetVerifyStatus error,param: {} ", userId, tytException);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR,tytException.getErrorMsg());
        }
    }


    /**
     * APP查看企业认证状态前调用
     * 检测经营范围是否包含网络货运许可证
     * @param enterpriseBusinessScope 经营范围
     * @return ResultMsgBean
     */
    @PostMapping("/verify/checkEnterpriseBusinessScope")
    public ResultMsgBean checkEnterpriseBusinessScope(String enterpriseBusinessScope,String enterpriseName) {
        try {
            //为兼容新老版本 如果请求参数存在企业名称 则查看库中网络货运企业集合中是否有该企业
            if(StringUtils.isNotBlank(enterpriseName)){
                if (enterpriseService.checkEnterpriseInOnlineFreight(enterpriseName)){
                    return ResultMsgBean.failResponse(ReturnCodeConstant.BUSINESS_SCOPE_ERROR,"根据国家网络货运管理条例，网络货运平台之间不得互相委托业务。确认继续吗");
                }
            }else{
                //如果不存在企业名称 则查看经营范围 是否包含经营范围
                if(StringUtils.isNotBlank(enterpriseBusinessScope)){
                    //校验经营范围
                    if (enterpriseBusinessScope.contains(CHECK_BUSINESS_SCOPE)){
                        return ResultMsgBean.failResponse(ReturnCodeConstant.BUSINESS_SCOPE_ERROR,"根据国家网络货运管理条例，网络货运平台之间不得互相委托业务。确认继续吗");
                    }
                }
            }
            return ResultMsgBean.successResponse();
        } catch (TytException tytException) {
            log.error("checkEnterpriseBusinessScope error,param: {} ", enterpriseBusinessScope, tytException);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR,tytException.getErrorMsg());
        }
    }

    /**
     * 查询企业认证状态和认证信息
     * <a href="http://192.168.2.20:3300/project/37/interface/api/10776">...</a>
     */
    @GetMapping("/verify/getVerifyStatus")
    public ResultMsgBean getVerifyStatus(@RequestParam("userId") Long userId) {
        try {
            VerifyStatusInfoResp info = enterpriseService.getVerifyStatus(userId);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getVerifyStatus : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 重新上传信息，取消之前的协议.
     */
    @PostMapping("/terminateContract")
    public ResultMsgBean terminateContract(Long userId) {
        try {
            if(CommonUtil.hasNull(userId)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            enterpriseService.terminateContract(userId);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("terminateContract : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 更新企业认证-驳回原来的认证
     */
    @PostMapping("/rejectEnterpriseVerify")
    public ResultMsgBean rejectEnterpriseVerify(Long userId) {
        try {
            if(CommonUtil.hasNull(userId)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            enterpriseService.rejectEnterpriseVerify(userId);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("rejectEnterpriseVerify : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 去激活，并获取合同预览模板
     */
    @PostMapping("/getContractPreview")
    public ResultMsgBean getContractPreview(Long userId) {
        try {
            if(CommonUtil.hasNull(userId)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            EnterpriseContractVo info = enterpriseService.getContractPreview(userId);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getContractPreview : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 同意签署
     */
    @PostMapping("/agreeSignContract")
    public ResultMsgBean agreeSignContract(Long userId) {
        try {
            if(CommonUtil.hasNull(userId)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            EnterpriseContractVo info = enterpriseService.agreeSignContract(userId);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("agreeSignContract : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 签署人验证码发送
     */
    @PostMapping("/signValidSend")
    public ResultMsgBean signValidSend(Long userId, String userPhone) {
        try {
            if(CommonUtil.hasNull(userId, userPhone)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            enterpriseService.signValidSend(userId, userPhone);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("signValidSend : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 签署人验证-验证码校验
     */
    @PostMapping("/signValidCheck")
    public ResultMsgBean signValidCheck(Long userId, String validCode) {
        try {
            if(CommonUtil.hasNull(userId, validCode)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            enterpriseService.signValidCheck(userId, validCode);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("signValidCheck : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 签署人验证码-e签宝回调
     */
    @PostMapping("/signValidCallBack.action")
    public ResponseEntity<ResultMsgBean> signValidCallBack(HttpServletRequest httpRequest) {
        try (ServletInputStream input = httpRequest.getInputStream();
             ByteArrayOutputStream output = new ByteArrayOutputStream();){

            IOUtils.copy(input, output);
            byte[] bodyBytes = output.toByteArray();
            String bodyText = new String(bodyBytes, StandardCharsets.UTF_8);

            log.info("bodyText : {}", bodyText);

            SignValidCallVo signValidCallVo = JSON.parseObject(bodyText, SignValidCallVo.class);

            Map<String, String[]> parameterMap = httpRequest.getParameterMap();
            log.info("call_request_map : {}", JSON.toJSONString(parameterMap));
            log.info("callBackData : {}", JSON.toJSONString(signValidCallVo));

            boolean signResult = EsignUtil.checkSignture(httpRequest, esignConfigProperty, bodyText);

            if(!signResult){
                throw TytException.createException(ResponseEnum.illegal_req.info());
            }

            enterpriseService.signValidCallBack(signValidCallVo);
            ResultMsgBean resultMsgBean = ResultMsgBean.successResponse();
            return ResponseEntity.status(HttpStatus.OK).body(resultMsgBean);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("signValidCallBack : ", e);
            ResultMsgBean resultMsgBean = ResultMsgBean.failResponse(e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(resultMsgBean);
        }
    }

    /**
     * 签署人验证码-结果轮询
     */
    @PostMapping("/getEnterpriseSign")
    public ResultMsgBean getEnterpriseSign(Long userId) {
        try {
            if(CommonUtil.hasNull(userId)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            EnterpriseContractVo info = enterpriseService.getEnterpriseSign(userId);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getEnterpriseSign : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 法人激活授权-法人四要素认证
     */
    @PostMapping("/legalRealValid")
    public ResultMsgBean legalRealValid(Long userId, String legalPersonCard) {
        try {
            if(CommonUtil.hasNull(userId, legalPersonCard)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            EnterpriseContractVo info = enterpriseService.legalRealValid(userId, legalPersonCard);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("legalRealValid : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 签署意愿-验证码发送
     */
    @PostMapping("/confirmValidSend")
    public ResultMsgBean confirmValidSend(Long userId) {
        try {
            if(CommonUtil.hasNull(userId)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            enterpriseService.confirmValidSend(userId);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("confirmValidSend : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 签署意愿确认-验证码校验
     */
    @PostMapping("/confirmValidCheck")
    public ResultMsgBean confirmValidCheck(Long userId, String validCode) {
        try {
            if(CommonUtil.hasNull(userId, validCode)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            EnterpriseContractVo info = enterpriseService.confirmValidCheck(userId, validCode);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("confirmValidCheck : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 授权书-去激活-下一步
     */
    @PostMapping("/createActiveAuth")
    public ResultMsgBean createActiveAuth(Long userId, String legalIdCard, String legalPhone) {
        try {
            if(CommonUtil.hasNull(userId, legalIdCard, legalPhone)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            EnterpriseContractVo info = enterpriseService.createActiveAuth(userId, legalIdCard, legalPhone);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("createActiveAuth : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 授权书-授权完成
     */
    @PostMapping("/activeAuthCheck")
    public ResultMsgBean activeAuthCheck(Long userId) {
        try {
            if(CommonUtil.hasNull(userId)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            EnterpriseContractVo info = enterpriseService.activeAuthCheck(userId);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("activeAuthCheck : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 授权书-取消
     */
    @PostMapping("/cancleActiveAuth")
    public ResultMsgBean cancleActiveAuth(Long userId) {
        try {
            if(CommonUtil.hasNull(userId)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            EnterpriseContractVo info = enterpriseService.cancleActiveAuth(userId);
            return ResultMsgBean.successResponse(info);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("activeAuthCheck : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

}
