package com.tyt.plat.enterprise.db;

import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.entity.base.TytOnlineFreightEnterprise;
import com.tyt.plat.mapper.base.TytInvoiceEnterpriseMapper;
import com.tyt.plat.mapper.base.TytOnlineFreightEnterpriseMapper;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Component
public class EnterpriseDBService {

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

    @Autowired
    private TytOnlineFreightEnterpriseMapper tytOnlineFreightEnterpriseMapper;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    public TytInvoiceEnterprise getVerifyInfoByUserId(Long userId){
        TytInvoiceEnterprise dto = new TytInvoiceEnterprise();
        dto.setCertigierUserId(userId);
        TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectOne(dto);
        //适配老版本的路径前缀
        if (tytInvoiceEnterprise!=null) {
            String licenseUrl = tytInvoiceEnterprise.getLicenseUrl();
            if (StringUtils.isNotBlank(licenseUrl) && !licenseUrl.startsWith("http")) {
                String prefix_picture = tytConfigService.getStringValue("prefix_picture","http://newtest.teyuntong.net/rootdata");
                tytInvoiceEnterprise.setLicenseUrl(prefix_picture + licenseUrl);
            } else {
                tytInvoiceEnterprise.setLicenseUrl(licenseUrl);
            }
        }
        return tytInvoiceEnterprise;
    }

    @Transactional(transactionManager = "mybatisTransactionManager")
    public int update(TytInvoiceEnterprise tytInvoiceEnterprise) {
        return tytInvoiceEnterpriseMapper.updateByPrimaryKeySelective(tytInvoiceEnterprise);
    }

    /**
     * 更新方法(会将为空的字段在数据库中置为NULL)
     * @param tytInvoiceEnterprise
     * @return
     */
    @Transactional(transactionManager = "mybatisTransactionManager")
    public int updateTwo(TytInvoiceEnterprise tytInvoiceEnterprise) {
        return tytInvoiceEnterpriseMapper.updateByPrimaryKey(tytInvoiceEnterprise);
    }

    @Transactional(transactionManager = "mybatisTransactionManager")
    public int insert(TytInvoiceEnterprise tytInvoiceEnterprise) {
        return tytInvoiceEnterpriseMapper.insertSelective(tytInvoiceEnterprise);
    }

    public TytOnlineFreightEnterprise getOnlineFreightEnterpriseByName(String enterpriseName){
     return  tytOnlineFreightEnterpriseMapper.getOnlineFreightEnterpriseByName(enterpriseName);
    }

    public TytInvoiceEnterprise getEnterpriseByCode(String enterpriseCode) {
        return tytInvoiceEnterpriseMapper.getEnterpriseByCode(enterpriseCode);
    }
}
