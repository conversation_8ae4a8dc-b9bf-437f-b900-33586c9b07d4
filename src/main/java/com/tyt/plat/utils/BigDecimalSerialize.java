package com.tyt.plat.utils;

import com.alibaba.fastjson.serializer.*;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: bigdecimal 去掉小数点后无效0
 * @date 2022/3/26 14:42
 */
public class BigDecimalSerialize {

    /**
     * 移除无效小数点
     * @param value
     * @return
     */
    public static BigDecimal removeInvalidPoint(BigDecimal value){

        if(value != null){
            value = new BigDecimal(value.stripTrailingZeros().toPlainString());
        }
        return value;
    }

    /**
     * jackson 序列化
     */
    public static class JacksonSerializer extends JsonSerializer<BigDecimal> {
        @Override
        public void serialize(BigDecimal value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

            value = BigDecimalSerialize.removeInvalidPoint(value);
            jsonGenerator.writeNumber(value);
        }
    }

    /**
     * fastjson 序列化
     */
    public static class FastJsonSerializer extends BigDecimalCodec {

        @Override
        public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {

            if(object != null && object instanceof BigDecimal){
                object = BigDecimalSerialize.removeInvalidPoint((BigDecimal) object);
            }
            super.write(serializer, object, fieldName, fieldType, features);
        }
    }

}
