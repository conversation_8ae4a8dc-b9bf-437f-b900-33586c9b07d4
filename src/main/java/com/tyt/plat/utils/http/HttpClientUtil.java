package com.tyt.plat.utils.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.utils.FileInfo;
import com.tyt.plat.utils.TytFileUtil;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class HttpClientUtil {

    /**
     * userAgent
     */
    public static final String MOCK_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36";

    /** 同域名同时请求线程数 **/
    private static final Integer MAX_CONN_PER_ROUTE = 10;

    /** httpClient **/
    private static final CloseableHttpClient instance = getHttpClientWithRetry();

    public static CloseableHttpClient getInstance(){
        return instance;
    }

    /**
     * 默认配置
     * @return
     */
    private static RequestConfig getHttpClientConfig(){
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(2000)
                .setSocketTimeout(5000)
                .build();
        return requestConfig;
    }

    /**
     * 生成httpclient
     * @return
     */
    private static CloseableHttpClient getHttpClientWithRetry() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100);
        connectionManager.setDefaultMaxPerRoute(20);//例如默认每路由最高50并发，具体依据业务来定

        RequestConfig httpClientConfig = getHttpClientConfig();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(httpClientConfig)
                .setMaxConnPerRoute(MAX_CONN_PER_ROUTE)
                .build();

        return httpClient;
    }

    /**
     * 创建uri 并拼接参数
     * @param apiUrl
     * @param paramMap
     * @return
     */
    public static URI createUri(String apiUrl, Map<String, String> paramMap){
        URI uri = null;
        try {
            URIBuilder builder = new URIBuilder(apiUrl);

            if(MapUtils.isNotEmpty(paramMap)){
                paramMap.forEach(builder::addParameter);
            }
            uri = builder.build();
        } catch (URISyntaxException e) {
            log.error("", e);
        }

        return uri;
    }

    /**
     * 解析json
     * @param httpResponse
     * @return
     */
    public static JSONObject toJsonObject(HttpResponse httpResponse) {

        JSONObject jsonObject = null;

        try {
            HttpEntity entity = httpResponse.getEntity();
            String jsonStr = EntityUtils.toString(entity, StandardCharsets.UTF_8);

            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if(statusCode != 200) {
                log.warn("toJsonObject_error : httpStatus : [{}], respBody : {}", statusCode, jsonStr);
                throw TytException.createException(ResponseEnum.sys_busy.info());
            }

            jsonObject = JSON.parseObject(jsonStr);
        } catch (IOException e) {
            throw TytException.createException(e);
        }

        return jsonObject;
    }

    /**
     * http client 请求
     * @param httpRequest
     * @return
     */
    public static CloseableHttpResponse execute(HttpUriRequest httpRequest) throws IOException{
        CloseableHttpResponse httpResponse = null;

        try {
            httpResponse = instance.execute(httpRequest);
        } catch (Exception e) {
            log.error("http_client_execute_error : ", e);

            TytException te = null;

            if(e instanceof ConnectTimeoutException){
                te = TytException.createException(ResponseEnum.timeout.info());
            } else if(e instanceof SocketTimeoutException){
                te = TytException.createException(ResponseEnum.sys_busy.info());
            } else {
                te = TytException.createException(ResponseEnum.sys_busy.info());
            }

            throw te;
        }

        return httpResponse;
    }

    /**
     * 读取data 数据
     * @param httpResponse
     * @param typeReference
     * @param <T>
     * @return
     */
    public static <T> T getResponseData(HttpResponse httpResponse, TypeReference<T> typeReference) {
        JSONObject jsonObject = HttpClientUtil.toJsonObject(httpResponse);

        if(jsonObject == null){
            return null;
        }
        T data = null;

        Integer code = jsonObject.getInteger("code");
        if(code != null && code.equals(200)){

            data = jsonObject.getObject("data", typeReference);

        } else {
            log.warn("getResponseData_error : body : {}", jsonObject.toJSONString());
            String msg = jsonObject.getString("msg");
            throw TytException.createException(new ResponseCode(code, msg));
        }

        return data;
    }

    /**
     * 读取data 数据
     * @param httpResponse
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T getResponseData(HttpResponse httpResponse, Class<T> clazz) {

        JSONObject jsonObject = HttpClientUtil.toJsonObject(httpResponse);

        if(jsonObject == null){
            return null;
        }

        T data = null;

        Integer code = jsonObject.getInteger("code");
        if(code != null && code.equals(200)) {

            data = jsonObject.getObject("data", clazz);

        } else {
            log.warn("getResponseData_error : body : {}", jsonObject.toJSONString());
            String msg = jsonObject.getString("msg");
            throw TytException.createException(new ResponseCode(code, msg));
        }

        return data;
    }

    /**
     * 下载文件.
     * @param fileUrl fileUrl
     * @param output output
     */
    public static void downloadFile(String fileUrl, OutputStream output){

        HttpGet httpGet = new HttpGet(fileUrl);
        httpGet.addHeader("User-Agent", MOCK_USER_AGENT);

        try(CloseableHttpResponse response = instance.execute(httpGet)) {
            try (InputStream inputStream = response.getEntity().getContent()) {

                IOUtils.copy(inputStream, output);

            } catch (IOException e) {
                throw TytException.createException(e);
            }

        } catch (Exception e) {
            throw TytException.createException(e);
        }
    }

    /**
     * 下载文件，大文件不允许使用.
     * @param fileUrl fileUrl
     */
    public static FileInfo downloadFile(String fileUrl){

        try(ByteArrayOutputStream output = new ByteArrayOutputStream()) {

            downloadFile(fileUrl, output);

            byte[] dataBytes = output.toByteArray();

            FileInfo fileInfo = TytFileUtil.getFileInfo(fileUrl);

            fileInfo.setDataBytes(dataBytes);
            return fileInfo;

        } catch (Exception e) {
            throw TytException.createException(e);
        }
    }

    /**
     * 下载文件base64，大文件不允许使用.
     * @param fileUrl fileUrl
     */
    public static String downloadBase64(String fileUrl){
        if(StringUtils.isBlank(fileUrl)){
            return null;
        }

        FileInfo fileInfo = HttpClientUtil.downloadFile(fileUrl);

        byte[] dataBytes = fileInfo.getDataBytes();

        String fileBase64 = CommonUtil.encodeBase64(dataBytes);
        return fileBase64;
    }
}
