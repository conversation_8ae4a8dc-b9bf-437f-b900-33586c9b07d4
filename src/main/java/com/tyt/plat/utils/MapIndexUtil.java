package com.tyt.plat.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 一个key, 多个值的情况
 * @date 2022/3/16 18:30
 */
public class MapIndexUtil <K, V> {

    private Map<K, List<V>> dataMap = new HashMap<>();

    public void put(K key, V value){

        List<V> vList = dataMap.get(key);

        if(vList == null){
            vList = new ArrayList<>();
            dataMap.put(key, vList);
        }

        vList.add(value);
    }

    public List<V> get(K key, V value){
        List<V> vList = dataMap.get(key);
        return vList;
    }

}
