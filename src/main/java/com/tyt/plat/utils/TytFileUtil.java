package com.tyt.plat.utils;

import com.tyt.messagecenter.core.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/12/15 10:57
 */
public class TytFileUtil {

    /** app name **/
    public static final String APP_PATH_NAME = "plat";

    private TytFileUtil() {
    }

    /**
     * 文件名信息.
     * @param filePath 文件路径或名称
     * @return FileInfo
     */
    public static FileInfo getFileInfo(String filePath) {
        if (filePath == null) {
            return null;
        }

        File tmpFile = new File(filePath);
        String fileName = tmpFile.getName();

        String nameOnly = "";
        String suffix = "";

        int pointIndex = fileName.lastIndexOf(".");

        if (pointIndex >= 0) {
            nameOnly = fileName.substring(0, pointIndex);
            suffix = fileName.substring(pointIndex);
        } else {
            nameOnly = fileName;
        }

        FileInfo fileInfo = new FileInfo(fileName, nameOnly, suffix);

        return fileInfo;
    }

    /**
     * 生成带 uuid 的唯一文件名.
     *
     * @param fileName   文件名称
     * @param keepOrigin 是否保留原始文件名
     * @return String
     */
    public static String uuidFileName(String fileName, boolean keepOrigin) {

        if(fileName == null){
            fileName = "";
        }

        FileInfo fileInfo = getFileInfo(fileName);

        String nameOnly = fileInfo.getNameOnly();
        String suffix = fileInfo.getSuffix();

        String uuid = CommonUtil.getUUID(true);

        String originFileName = nameOnly;
        if (keepOrigin && StringUtils.isNotBlank(originFileName)) {
            originFileName = originFileName + "_";
        } else {
            originFileName = "";
        }

        String fullFileName = originFileName + uuid + suffix;
        return fullFileName;
    }

    /**
     * 生成文件路径，按照日期拆分.
     * path 规则： /${env}/${app_name}/${type_name}/${yyyy}/${MM}/${dd}/${uuid_file_name}
     * 示例： http://peimages.teyuntong.net/online/invoice/import/2023/12/15/cae513bd35d5782dafe98e18ffd3b5e5.xslx
     * @return String
     */
    public static String dateFolder(){

        Date nowTime = new Date();

        String nowTimeText = DateUtil.dateToString(nowTime, DateUtil.day_format);

        String[] timeArray = nowTimeText.split("-");

        String year = timeArray[0];
        String month = timeArray[1];
        String day = timeArray[2];

        String fullPath = CommonUtil.joinPath(year, month, day);

        return fullPath;
    }

}
