package com.tyt.plat.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/17 14:49
 */
public class NumberConvertUtil {

    /**
     * 数值转换
     * @param value
     * @return
     */
    public static Short toShort(Number value){
        if(value == null){
            return null;
        }
        short result = value.shortValue();
        return result;
    }
    public static Byte toByte(Number value){
        if(value == null){
            return null;
        }
        byte result = value.byteValue();
        return result;
    }

    public static Double strToDouble(String value){
        Double result = null;
        if(StringUtils.isNotBlank(value)){
            result = Double.parseDouble(value);
        }
        return result;
    }

    public static void main(String[] args) {

        String value = "2.3346";

        Double aDouble = strToDouble(value);

        System.out.println("finished ... ");

    }

}
