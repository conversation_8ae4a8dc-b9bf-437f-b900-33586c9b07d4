package com.tyt.plat.utils;

import com.alibaba.fastjson.JSON;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/2/26 16:24
 */
@Slf4j
public class PlatCommonUtil {

    /** 项目根目录 **/
    private volatile static String PROJECT_ROOT_PATH;

    /**
     * 初始化项目根目录
     * @return String
     */
    private synchronized static void initProjectRoot(){

        if(StringUtils.isNotBlank(PROJECT_ROOT_PATH)) {
            return;
        }

        URL resourceUrl = PlatCommonUtil.class.getResource("/");
        String resourcePath = resourceUrl.getPath();

        log.info("project_resource_path : {}", resourcePath);

        if(StringUtils.isBlank(resourcePath)){
            throw TytException.createException(ResponseEnum.sys_error.info("Init_Project_Root_Error!"));
        }

        String jarFlag = "file:";
        boolean isJar = false;
        if(resourcePath.startsWith(jarFlag)){
            isJar = true;
            resourcePath = resourcePath.substring(jarFlag.length());
        }

        File dirFileTmp = new File(resourcePath);

        int i = 0;
        while (true){
            if(dirFileTmp.isDirectory()){
                break;
            }
            dirFileTmp = dirFileTmp.getParentFile();
            i++;

            if(i > 10){
                //查找项目目录过深，正常不会超过
                throw TytException.createException(ResponseEnum.sys_error.info("Project_Root_Dir_init_Error!"));
            }
        }

        PROJECT_ROOT_PATH = dirFileTmp.getAbsolutePath();

        if(StringUtils.isBlank(PROJECT_ROOT_PATH)){
            throw TytException.createException(ResponseEnum.sys_error.info("Project_Root_Dir_Blank_Error!"));
        }

        log.info("project_root_path_init : " + PROJECT_ROOT_PATH);
    }

    /**
     * 获取项目根目录.
     * @return
     */
    public static String getProjectRoot(){

        if(StringUtils.isNotBlank(PROJECT_ROOT_PATH)) {
            return PROJECT_ROOT_PATH;
        }

        initProjectRoot();

        return PROJECT_ROOT_PATH;
    }

    /**
     * 获取项目临时目录
     * @return
     */
    public static String getProjectTmpPath(){

        String tmpFolder = CommonUtil.joinPath(getProjectRoot(), "plat_tmp_folder");
        return tmpFolder;
    }

    /**
     * 创建文件包括目录
     *
     * @param filePath
     * @return
     * @throws IOException
     */
    public static void createFileFolder(String filePath) {

        File parentFile = new File(filePath).getParentFile();

        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
    }

    /**
     * 将文本写入文件
     *
     * @param path
     * @param content
     * @param append
     */
    public static void writeFileContent(String path, String content, boolean append) {

        if(content == null){
            return;
        }

        createFileFolder(path);

        FileWriter fw = null;
        BufferedWriter writer = null;
        try {
            fw = new FileWriter(path, append);
            writer = new BufferedWriter(fw);

            writer.write(content);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            log.error("", e);
        } finally {
            try {
                writer.close();
                fw.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                log.error("", e);
            }
        }
    }


    /**
     * 打印消耗时间
     * @param remark
     * @param startTime
     */
    public static void printSpendTime(String remark, long startTime){

        long endTime = System.currentTimeMillis();

        long spend = (endTime - startTime);

        log.info("#### spend_time_{} : spend : [{}] ms!", remark, spend);

    }

    /**
     * 打印消耗时间
     * @param remark
     * @param startTime
     */
    public static void writeSpendTime(String path, String remark, long startTime){

        long endTime = System.currentTimeMillis();

        long spend = (endTime - startTime);

        String textTmp = "spend_time_"+ remark +" : spend : [" + spend + "] ms!\n";

        writeFileContent(path, textTmp, true);

        log.info("#### spend_time_{} : spend : [{}] ms!", remark, spend);

    }

    /**
     * 防止出发报警.
     * @param remark remark
     * @param e e
     */
    public static void printErrorInfo(String remark, Exception e){
        if(remark == null){
            remark = "";
        }

        if(e instanceof TytException){
            TytException tytException = (TytException) e;
            log.warn(remark + " " + tytException.getErrorMsg());
        }else {
            log.error(remark, e);
        }
    }

    /**
     * 打印日志，内容过长时会截取
     * @param label label
     * @param info info
     */
    public static void printFixLog(String label, Object info){
        String logText = "";

        if(info != null){
            if(info instanceof String){
                logText = (String) info;
            }else {
                logText = JSON.toJSONString(info);
            }

        }

        int maxLength = 1000;

        if (logText != null && logText.length() > maxLength) {
            logText = logText.substring(0, maxLength - 1) + "...skipping...";
        }

        log.info(label + " : {}", logText);
    }


    public static void main(String[] args) {

        printFixLog("aaabb", "bbbcc");

        System.out.println("finished ... ");

    }

}
