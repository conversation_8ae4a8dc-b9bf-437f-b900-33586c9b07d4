package com.tyt.plat.utils.mask;

import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import org.apache.commons.lang3.StringUtils;

public class MaskFieldUtil {

    /**
     * 字段脱敏.
     * @param text
     * @return
     */
    public static String maskText(MaskFieldEnum maskFieldEnum, String text){

        if(maskFieldEnum == null){
            throw TytException.createException(ResponseEnum.sys_error.info());
        }
        if(StringUtils.isBlank(text)){
            return text;
        }
        String findReg = maskFieldEnum.getFindReg();
        String replaceExp = maskFieldEnum.getReplaceExp();

        String result = text.replaceAll(findReg, replaceExp);
        return result;
    }

}
