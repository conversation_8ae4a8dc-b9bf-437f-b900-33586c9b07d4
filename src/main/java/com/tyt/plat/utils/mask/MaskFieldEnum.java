package com.tyt.plat.utils.mask;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

public enum MaskFieldEnum {

    //手机号
    PHONE( "^(\\d{3})\\d{4}(\\d{4})$", "$1****$2"),
    //身份证
    ID_NUMBER("^(\\d{4})\\d{8,10}(.{4})$", "$1****$2"),

    //邮箱
    EMAIL("^(.).*(@.*)$", "$1***$2"),
    //替换所有
    ALL("^.*$", "**"),

    //姓名
    NAME("(?<=.{1}).", "**"),

    NORMAL("^(.{2}).*(.{1})$", "$1***$2"),

    ;

    @Getter
    private final String findReg;
    @Getter
    private final String replaceExp;

    MaskFieldEnum(String findReg, String replaceExp) {
        this.findReg = findReg;
        this.replaceExp = replaceExp;
    }

    /**
     * 字段脱敏.
     * @param text
     * @return
     */
    public String maskText(String text){
        if(StringUtils.isBlank(text)){
            return text;
        }

        String result = text.replaceAll(findReg, replaceExp);
        return result;
    }

}
