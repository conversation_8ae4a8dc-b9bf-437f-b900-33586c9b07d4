package com.tyt.plat.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.ResourceBundle;

/**
 * 系统环境配置.
 *
 * <AUTHOR>
 * @since 2023/10/12 16:47
 */
@Slf4j
public class SystemEnvironmentUtil {

    /**
     * 系统 properties.
     */
    public static final ResourceBundle RESOURCE_BUNDLE = ResourceBundle.getBundle("server_url");

    /**
     * 获取项目配置.
     *
     * @param key key
     * @return
     */
    public static String getString(String key, boolean required) {

        String value = null;

        try {

            Object obj = RESOURCE_BUNDLE.getObject(key);

            value = (obj != null ? obj.toString() : "");

        } catch (Exception e) {
            if(required){
                throw e;
            } else {
                log.warn("NULL_RESOURCE_VALUE : {}, msg : {}", key, e.getMessage());
            }
        }

        return value;
    }

    /**
     * 获取项目配置.
     *
     * @param key key
     * @return
     */
    public static String getString(String key) {

        String value = getString(key, true);

        return value;
    }

    public static void main(String[] args) {

        String accessKey = SystemEnvironmentUtil.getString("NAMESRV_ADDR");

        String accessKey1 = SystemEnvironmentUtil.getString("accessKey");

        System.out.println("add code ... ");

    }

}
