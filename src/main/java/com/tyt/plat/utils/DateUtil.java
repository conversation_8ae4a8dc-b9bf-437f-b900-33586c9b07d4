package com.tyt.plat.utils;

import cn.hutool.core.date.DateTime;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtil {
    public static final String date_time_format = "yyyy-MM-dd HH:mm:ss";
    public static final String day_format = "yyyy-MM-dd";
    public static final String MONTH_FORMAT = "yyyy-MM";

    public static final String time_format_short = "yyyyMMddHHmmss";
    public static final String day_format_short = "yyyyMMdd";
    public static final String MONTH_FORMAT_SHORT = "yyyyMM";

    public static final String default_time_zone = "GMT+8";

    /**
     * 转换String 为 date
     *
     * @param dateStr
     * @param formatType
     * @return
     * @throws Exception
     */
    public static Date stringToDate(String dateStr, String formatType) {

        SimpleDateFormat format = new SimpleDateFormat(formatType);

        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            log.error("", e);
        }

        return date;
    }

    /**
     * 日期转字符串
     *
     * @param date
     * @param formatType
     * @return
     */
    public static String dateToString(Date date, String formatType) {
        SimpleDateFormat format = new SimpleDateFormat(formatType);
        String resultDay = format.format(date);
        return resultDay;
    }

    /**
     * 获取天开始时间
     *
     * @param date
     * @param formatType
     * @return
     */
    public static String getDayBegin(Date date, String formatType) {

        String dayStr = dateToString(date, day_format);

        Date dayDate = stringToDate(dayStr, day_format);

        String result = dateToString(dayDate, formatType);

        return result;
    }

    /**
     * 获取当前月的第一天
     *
     * @return
     */
    public static Date getFirstDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取当前月的最后一天
     *
     * @return
     */
    public static Date getEndDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }


    /**
     * 计算两个日期相差的秒数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 相差的秒数
     */
    public static long differenceInSeconds(Date startDate, Date endDate) {
        // 获取两个日期的时间差（毫秒）
        long diffInMilliseconds = Math.abs(endDate.getTime() - startDate.getTime());

        // 将毫秒转换为秒
        return TimeUnit.SECONDS.convert(diffInMilliseconds, TimeUnit.MILLISECONDS);
    }

}
