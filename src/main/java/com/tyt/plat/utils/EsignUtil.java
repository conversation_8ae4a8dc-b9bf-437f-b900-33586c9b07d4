package com.tyt.plat.utils;

import com.alibaba.fastjson.JSON;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.commons.properties.EsignConfigProperty;
import com.tyt.plat.vo.enterprise.EsignCallBackHeaderVo;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/3/1 14:30
 */
@Slf4j
public class EsignUtil {

    private static final String ALGORITHM = "HmacSHA256";

    /**
     * 获取e签宝header.
     * @param httpRequest httpRequest
     * @return EsignCallBackHeaderVo
     */
    public static EsignCallBackHeaderVo getEsignCallBackHeader(HttpServletRequest httpRequest) {

        String appId = httpRequest.getHeader("X-Tsign-Open-App-Id");
        String signature = httpRequest.getHeader("X-Tsign-Open-SIGNATURE");
        String timestamp = httpRequest.getHeader("X-Tsign-Open-TIMESTAMP");
        String algorithm = httpRequest.getHeader("X-Tsign-Open-SIGNATURE-ALGORITHM");

        if(StringUtils.isBlank(signature) || StringUtils.isBlank(timestamp)){
            return null;
        }

        EsignCallBackHeaderVo headerVo = new EsignCallBackHeaderVo();

        headerVo.setAppId(appId);
        headerVo.setSignature(signature);
        headerVo.setTimestamp(timestamp);
        headerVo.setAlgorithm(algorithm);

        log.info("call_back_header : {}", JSON.toJSONString(headerVo));

        return headerVo;
    }

    /**
     * 校验e签宝签名
     * @param httpRequest httpRequest
     * @param esignConfigProperty esignConfigProperty
     * @return boolean
     */
    public static boolean checkSignture(HttpServletRequest httpRequest, EsignConfigProperty esignConfigProperty, String bodyText) {

        EsignCallBackHeaderVo headerVo = getEsignCallBackHeader(httpRequest);

        if(headerVo == null){
            return false;
        }

        String configAppId = esignConfigProperty.getAppId();
        String appId = headerVo.getAppId();

        if(!StringUtils.equals(configAppId, appId)){
            return false;
        }

        String signture = headerVo.getSignature();
        String timestamp = headerVo.getTimestamp();
        String paramText = timestamp;
        String secret = esignConfigProperty.getSecret();

        String fullSignData = paramText + bodyText;
        //计算签名方法
        String localSign = null;
        try {
            localSign = getSignature(fullSignData, secret);
        } catch (Exception e) {
            throw TytException.createException(e);
        }
        localSign = localSign.toLowerCase();

        boolean signResult = localSign.equals(signture);

        return signResult;
    }

    /**
     * 加签文本
     * @param data data
     * @param secret secret
     * @return String
     * @throws Exception e
     */
    public static String getSignature(String data, String secret) throws Exception{
        byte[] secretByte = secret.getBytes(StandardCharsets.UTF_8);
        byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);

        Mac mac = Mac.getInstance(ALGORITHM);

        mac.init(new SecretKeySpec(secretByte, ALGORITHM));

        byte[] signData = mac.doFinal(dataBytes);

        String signature = "";
        for(byte oneByte : signData){
            String hexString = String.format("%02X", oneByte);

            signature = signature + hexString;
        }

        //完成 Mac 操作
        return signature;
    }

}
