package com.tyt.plat.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.service.special.CsNewCustomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/08/09 11:11
 */
@Slf4j
@RestController
@RequestMapping("/plat/custom")
public class CustomQcCodeController {


    @Autowired
    private CsNewCustomService csNewCustomService;


    @RequestMapping(value = "/addCustom.action")
    public ResultMsgBean addCustom(@RequestParam Long userId, @RequestParam String phone, @RequestParam Integer type, @RequestParam Long qcCodeId) {
        log.info("根据二维码扫码添加新用户用户id【{}】，手机号【{}】，分类【{}】",userId,phone,type);
        csNewCustomService.addpcCodeCustom(userId,phone,type,qcCodeId);
        return ResultMsgBean.successResponse();
    }
}
