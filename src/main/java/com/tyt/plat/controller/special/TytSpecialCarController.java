package com.tyt.plat.controller.special;

import com.alibaba.fastjson.JSON;
import com.tyt.car.service.CarDetailHeadService;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.model.*;
import com.tyt.plat.biz.invoice.pojo.DriverListVO;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.CarHasLadder;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.special.TytSpecialCarRouteService;
import com.tyt.plat.service.special.TytSpecialCarService;
import com.tyt.plat.vo.SpecialCarVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.bean.CarType;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytSourceService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;
import kafka.utils.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/07/16 19:17
 */
@Slf4j
@RestController
@RequestMapping("/plat/signing/car")
public class TytSpecialCarController {

    @Autowired
    private TytInvoiceDriverMapper tytInvoiceDriverMapper;


    @Autowired
    private CarService carService;

    @Autowired
    private TytSpecialCarPriceConfigMapper tytSpecialCarPriceConfigMapper;

    @Autowired
    private TytSpecialCarService tytSpecialCarService;

    @Autowired
    private UserService userService;

    @Autowired
    private TytSpecialCarMapper tytSpecialCarMapper;

    @Autowired
    private CarDetailTailService carDetailTailService;

    @Autowired
    private TytCarTypeMapper tytCarTypeMapper;
    @Autowired
    private TytCityMapper tytCityMapper;

    @Autowired
    private CarDetailHeadService carDetailHeadService;

    @Autowired
    private TytSourceService tytSourceService;

    @Autowired
    private TytSpecialCarRouteService tytSpecialCarRouteService;

    /**
     * 查询车辆信息
     * @param userId
     * @return ResultMsgBean
     */
    @RequestMapping ("/getCarDriverCityList.action")
    public ResultMsgBean getInfo(Long userId,@RequestParam(value = "queryCity", required = false) String queryCity,
                                 @RequestParam(value = "queryStartCity",required = false) String queryStartCity,
                                 @RequestParam(value = "queryDestCity",required = false) String queryDestCity) {
        if(null == userId){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        SpecialCarVo vo = new SpecialCarVo();
        List<QueryCar> cars = carService.getQueryAllOld(userId,"1");
        log.info("专车添加车主查询车辆信息：车主id【{}】，车辆信息【{}】",userId,cars.toString());
        vo.setCars(cars);
        List<TytInvoiceDriver> drivers = tytInvoiceDriverMapper.selectByUserIdAndVerifyStatus(userId,1);
        vo.setDrivers(drivers);

        // 城市，路线数据  6630 专车申请页不再展示城市路线
        /*List<TytSpecialCarPriceConfig> configs = tytSpecialCarPriceConfigMapper.selectByCities(queryStartCity,queryDestCity);

        if(CollectionUtils.isNotEmpty(configs)){
            List<String> nameList = configs.stream().map(TytSpecialCarPriceConfig::getStartCity).collect(Collectors.toList());
            List<String> newName = configs.stream().map(TytSpecialCarPriceConfig::getDestCity).collect(Collectors.toList());
            nameList.addAll(newName);
            List<String> collect1 = nameList.stream().distinct().collect(Collectors.toList());
            // 根据传入的城市精确匹配
            if(StringUtils.isNotBlank(queryCity)){
                List<String> collect = collect1.stream().filter(queryCity::equals).collect(Collectors.toList());
                vo.setCitys(collect);
            }else {
                vo.setCitys(collect1);
            }

            List<String> cityList = new ArrayList<>();
            for(TytSpecialCarPriceConfig config : configs){
                cityList.add(config.getStartCity()+"-"+config.getDestCity());
            }
            vo.setStartDests(cityList.stream().distinct().collect(Collectors.toList()));
        }*/
        return ResultMsgBean.successResponse(vo);
    }



    @GetMapping ("/getCityInfo")
    public ResultMsgBean getCityInfo(@RequestParam(value = "queryCity") String queryCity) {

        List<String> cityList = new ArrayList<>();
        if(StringUtils.isNotBlank(queryCity)){
            List<TytCity> city = tytCityMapper.getCity(queryCity);
            if(CollectionUtils.isNotEmpty(city)){
                cityList.addAll(city.stream().map(TytCity::getCityName).collect(Collectors.toList()));
            }
        }
        return ResultMsgBean.successResponse(cityList);
    }


    /**
     * 查询专车列表
     * @return ResultMsgBean
     */
    @RequestMapping ("/list.action")
    public ResultMsgBean list(@RequestParam Long userId, @RequestParam Integer page, @RequestParam Integer pageSize) {
        try {
            Map<String, Object> map = tytSpecialCarService.getList(userId, page, pageSize);
            return ResultMsgBean.successResponse(map);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("查询司机列表失败,userId: {}, page: {}, pageSize:{} ", userId, page, pageSize, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "查询专车列表失败");
        }
    }

    @RequestMapping ("/getInfo.action")
    public ResultMsgBean getCarInfo(@RequestParam Long id) {
        TytSpecialCar car = tytSpecialCarService.getById(id);
        return ResultMsgBean.successResponse(car);
    }

    @RequestMapping ("/save.action")
    public ResultMsgBean save(TytSpecialCar car,String route)throws Exception {
        TytInvoiceDriver driver1 = tytInvoiceDriverMapper.selectByPrimaryKey(car.getDriverId());
        if(null == driver1 || !car.getUserId().equals(driver1.getUserId())){
            return new ResultMsgBean(ReturnCodeConstant.ERROR,"无该司机信息，请重新选择");
        }
        Integer count = tytSpecialCarService.getInfo(car.getUserId(),car.getDriverId(),driver1.getDriverUserId());
        if(count > 0){
            return new ResultMsgBean(ReturnCodeConstant.ERROR,"该司机是专车司机，请勿重复申请");
        }
        try {
            User user = userService.getByUserId(car.getUserId());
            Car byId = carService.getById(car.getCarId());
            if(null == byId){
                return new ResultMsgBean(ReturnCodeConstant.ERROR,"无该车辆信息，请重新选择");
            }

            if(!car.getUserId().equals(byId.getUserId())){
                return new ResultMsgBean(ReturnCodeConstant.ERROR,"该车辆不是当前车主车辆，请重新选择");
            }
            CarDetailTail tail = carDetailTailService.getByCarId(car.getCarId());
            String length = "";
            String height = "";
            String carStyle = "";
            String carType = "";

            if(null != tail){
                 length = getLength(tail.getLength());
                 height =getHeight(tail.getLoadSurfaceHeight());
                //挂车样式
                 carStyle =  getCarStyle(tail.getIsPureFlat());
                //挂车型号---
                 carType = getCarType(byId.getCarType());
            }

            TytSpecialCar newCar =TytSpecialCar.builder()
                    .userId(car.getUserId())
                    .tytCellPhone(user.getCellPhone())
                    .name(driver1.getName())
                    .driverId(driver1.getId())
                    .driverUserId(driver1.getDriverUserId())
                    .driverPhone(driver1.getPhone())
                    .phone(driver1.getPhone())
                    .drivingAbility(car.getDrivingAbility())
                    .city(car.getCity())
                    .carId(car.getCarId())
                    .carType(carType)
                    .length(length)
                    .ladderType(CarHasLadder.getValueByKey(byId.getHasLadder()))
                    .headCityNo(byId.getHeadCity() + byId.getHeadNo())
                    .tailCityNo(byId.getTailCity() + byId.getTailNo())
                    .tableHeight(height)
                    .otherPureFlat(carStyle)
                    .modifyTime(new Date())
                    .build();

            try{
                BasePoiDistince distinct = carDetailTailService.getDistinct(String.valueOf(car.getUserId()));
                log.info("经分返回用户距离标记结果userId:{}，distince:{}",car.getUserId(), JSON.toJSONString(distinct));
                if(null != distinct){
                    newCar.setBiDistance(distinct.getPoiCity()+"_"+distinct.getPoiArea()+"_"+distinct.getWarehouse()+"_"+distinct.getDistinct());
                }
            }catch (Exception e){
                log.error("经分返回用户距离标记结果异常userId:{}，e:",car.getUserId(), e);
            }
            if(null != car.getId()){
                TytSpecialCar specialCar = tytSpecialCarService.getById(car.getId());
                newCar.setId(car.getId());
                newCar.setUpdateName(user.getUserName());
                newCar.setStatus(0);
                newCar.setReason(null);
                newCar.setCreateTime(specialCar.getCreateTime());
                newCar.setCreateName(specialCar.getCreateName());
                tytSpecialCarMapper.updateByPrimaryKey(newCar);
                tytSpecialCarRouteService.deleteBySpecialId(car.getId());
            }else{
                newCar.setCreateName(user.getUserName());
                newCar.setCreateTime(new Date());
                tytSpecialCarMapper.insertSelective(newCar);
            }
            if(StringUtils.isNotEmpty(route)){
                tytSpecialCarRouteService.saveBatch(route,newCar.getId());
            }
            return ResultMsgBean.successResponse();
        } catch (Exception e){
            log.info("准车准入添加失败",e);
            return ResultMsgBean.failResponse(e);
        }


    }

    public String getLength(String tailLength){
        String length = "0";
        try{
            if(StringUtils.isNotEmpty(tailLength)){
                BigDecimal bigDecimal = new BigDecimal(tailLength);
                BigDecimal divide = bigDecimal.divide(new BigDecimal(1000));
                length = divide.toString();
            }
        }catch (Exception e){
            log.error("处理车辆长度失败【{}】",e);
        }

        return length;
    }

    public String getHeight(String tailHeight){
        String height = "0";
        try{
            if(StringUtils.isNotEmpty(tailHeight)){
                BigDecimal bigDecimal = new BigDecimal(tailHeight);
                BigDecimal divide = bigDecimal.multiply(new BigDecimal(100));
                height = divide.toString();
            }
        }catch (Exception e){
            log.error("处理车辆高度失败【{}】",e);
        }

        return height;
    }

    public String getCarStyle(Integer pureFlat){
        String carStyle = "";
        if(null != pureFlat){
            TytSource tytSource = TytSourceUtil.getSourceName("tail_car_style",pureFlat.toString());
            if(null != tytSource){
                carStyle = tytSource.getName();
            }
        }
        return carStyle;
    }

    public String getCarType(Integer carType){
        String type = "";
        if(null != carType){
            TytSource tytSource = TytSourceUtil.getSourceName("tail_car_type",carType.toString());
            if(null != tytSource){
                type = tytSource.getName();
            }
        }
        return type;
    }

}
