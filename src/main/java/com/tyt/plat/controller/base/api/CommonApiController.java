package com.tyt.plat.controller.base.api;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.enums.GlobalStatusEnum;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.ocr.OcrIdCardBackVo;
import com.tyt.plat.vo.ocr.OcrIdCardFrontVo;
import com.tyt.plat.vo.ocr.RoadTransportVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/5 上午10:31
 */
@RestController
@Slf4j
@RequestMapping("/plat/common-api")
public class CommonApiController {

    private final CommonApiService commonApiService;

    public CommonApiController(CommonApiService commonApiService) {
        this.commonApiService = commonApiService;
    }

    private OcrIdCardFrontVo checkCardFront(OcrIdCardFrontVo idCardFrontVo) {
        if (idCardFrontVo == null) {
            idCardFrontVo = new OcrIdCardFrontVo();
        }

        String idNumber = idCardFrontVo.getIdNumber();
        String name = idCardFrontVo.getName();

        if (StringUtils.isBlank(idNumber) || StringUtils.isBlank(name)) {
            throw TytException.createException(ResponseEnum.request_error.info("不是身份证人像页或太模糊，请重新上传"));
        }
        return idCardFrontVo;
    }

    private boolean isCardLongTerm(String expireDateText) {
        boolean result = (StringUtils.isNotBlank(expireDateText) && expireDateText.contains("长期"));
        return result;
    }

    public boolean dateExpire(Integer longTerm, Date expireDate) {

        boolean expireResult = false;

        if (GlobalStatusEnum.yes.equalsCode(longTerm)) {
            //长期的
        } else {
            if (expireDate != null) {
                Date nowTime = new Date();
                if (nowTime.after(expireDate)) {
                    //证件已过期
                    expireResult = true;
                }
            }
        }

        return expireResult;
    }

    private OcrIdCardBackVo checkCardBack(OcrIdCardBackVo cardBackVo) {

        if (cardBackVo == null) {
            cardBackVo = new OcrIdCardBackVo();
        }

        int expireFlag = GlobalStatusEnum.no.getCode();
        int longTerm = GlobalStatusEnum.no.getCode();

        String expireDateText = cardBackVo.getExpireDateText();
        Date expireDate = cardBackVo.getExpireDate();

        if (this.isCardLongTerm(expireDateText)) {
            longTerm = GlobalStatusEnum.yes.getCode();
        }

        if (this.dateExpire(longTerm, expireDate)) {
            expireFlag =(GlobalStatusEnum.yes.getCode());
        }
        cardBackVo.setExpireFlag(expireFlag);
        cardBackVo.setIdCardLongTerm(longTerm);

        if (!GlobalStatusEnum.yes.equalsCode(longTerm) && expireDate == null) {

            throw TytException.createException(ResponseEnum.request_error.info("不是身份证国徽页或太模糊，请重新上传"));

        } else if (GlobalStatusEnum.yes.equalsCode(expireFlag)) {
            cardBackVo.setRiskFlag(GlobalStatusEnum.yes.getCode());
            cardBackVo.setRiskMsg("身份证已过期");
        }

        return cardBackVo;
    }

    @GetMapping("/ocr/idCard/font")
    public ResultMsgBean idCardFontOcr(@RequestParam String url) {
        try {
            OcrIdCardFrontVo idCardFrontVo = commonApiService.idCardFrontOcr(url);

            idCardFrontVo = this.checkCardFront(idCardFrontVo);

            return ResultMsgBean.successResponse(idCardFrontVo);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("idCardFontOcr : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    @GetMapping("/ocr/idCard/opposite")
    public ResultMsgBean idCardOppositeOcr(@RequestParam String url) {
        try {
            OcrIdCardBackVo idCardBackVo = commonApiService.idCardBackOcr(url);
            idCardBackVo = this.checkCardBack(idCardBackVo);

            return ResultMsgBean.successResponse(idCardBackVo);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("idCardOppositeOcr : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    @GetMapping("/ocr/qcCard")
    public ResultMsgBean qcCardOcr(@RequestParam String url) {
        RoadTransportVo roadTransportVo = commonApiService.roadTransportOcr(url);
        return ResultMsgBean.successResponse(roadTransportVo == null ? new RoadTransportVo() : roadTransportVo);
    }
}
