package com.tyt.plat.controller.base;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytUserRecord;
import com.tyt.plat.service.base.TytUserRecordService;
import com.tyt.plat.vo.other.IndividuationSettings;
import com.tyt.service.common.enums.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/plat/userRecord")
public class UserRecordController {
    @Autowired
    private TytUserRecordService tytUserRecordService;

    /**
     * 获取认证信息授权弹窗提示
     *
     * @param userId 大厅
     * @param code   car_user_info_auth = 车方认证，goods_user_info_auth =货方认证
     * @return 0=不弹窗提示（说明已经弹过了） 1=弹窗提示
     */
    @RequestMapping(value = "/getUserAuthorizeInfo")
    public ResultMsgBean getUserAuthorizeInfo(Long userId, String code) {
        log.info("获取认证信息授权弹窗提示:userId={},code={}", userId, code);
        if (Objects.isNull(userId) || StringUtils.isEmpty(code)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        TytUserRecord userRecord = tytUserRecordService.getByUserIdAndCode(userId, code);
        if (Objects.isNull(userRecord)) {
            return ResultMsgBean.successResponse(1);
        }
        //没有状态？ 那就弹吧
        if (Objects.isNull(userRecord.getStatus())) {
            return ResultMsgBean.successResponse(1);
        }
        //status =1 已经同意了，同意了就别弹了
        if (userRecord.getStatus() == 1) {
            return ResultMsgBean.successResponse(0);
        }
        //默认弹窗提示
        return ResultMsgBean.successResponse(1);
    }

    /**
     * 更新认证信息授权弹窗提示
     *
     * @param userId
     * @param code   car_user_info_auth = 车方认证，goods_user_info_auth =货方认证
     * @param status 0 不同意 1 同意
     * @return
     */
    @PostMapping(value = "/updateUserAuthorizeInfo")
    public ResultMsgBean updateUserAuthorizeInfo(Long userId, String code, Integer status) {
        log.info("更新认证信息授权弹窗提示:userId={},code={},status={}", userId, code, status);
        if (Objects.isNull(userId) || StringUtils.isEmpty(code) || Objects.isNull(status)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        tytUserRecordService.saveRecord(userId, code, status, null);
        return ResultMsgBean.successResponse();
    }

    /**
     * @param userId
     * @param codes  hall_custom_setup 找货大厅个性化设置 details_custom_setup 货源详情个性化设置
     * @return com.tyt.model.ResultMsgBean
     * @description 根据用户id及多code获取信息
     * <AUTHOR>
     * @date 2023/8/31 15:45
     * @version 1.
     */
    @RequestMapping(value = "getIndividuationSettings")
    public ResultMsgBean getIndividuationSettings(Long userId, String codes) {
        if (Objects.isNull(userId) || StringUtils.isEmpty(codes)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        List<String> codeList = Arrays.asList(codes.split(","));
        List<IndividuationSettings> userRecords = tytUserRecordService.getIndividuationSettings(userId, codeList);

        return ResultMsgBean.successResponse(userRecords);
    }

    /**
     * 记录次数
     * @param userId 用户id
     * @param code code
     * @return rm
     */
    @PostMapping(value = "/updateTimesRecord")
    public ResultMsgBean updateTimesRecord(Long userId, String code) {

        if (Objects.isNull(userId) || StringUtils.isEmpty(code)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        tytUserRecordService.saveTimesRecord(userId, code);
        return ResultMsgBean.successResponse();
    }

    /**
     * 获取用户record 结果.
     *
     * @param userId userId
     * @param code   code
     * @return ResultMsgBean
     */
    @GetMapping(value = "/getRecord")
    public ResultMsgBean getRecord(Long userId, String code) {

        if (CommonUtil.hasNull(userId, code)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        TytUserRecord userRecord = tytUserRecordService.getByUserIdAndCode(userId, code);

        return ResultMsgBean.successResponse(userRecord);
    }

    /**
     * 保存用户记录.
     *
     * @param userId userId
     * @param code code
     * @param status status
     * @param value value
     * @return
     */
    @PostMapping(value = "/saveRecord")
    public ResultMsgBean saveRecord(Long userId, String code, Integer status, String value) {

        if (Objects.isNull(userId) || StringUtils.isEmpty(code) || Objects.isNull(status)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        tytUserRecordService.saveRecord(userId, code, status, value);
        return ResultMsgBean.successResponse();
    }



}
