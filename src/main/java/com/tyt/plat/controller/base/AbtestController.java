package com.tyt.plat.controller.base;

import cn.hutool.core.collection.CollUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytAbtestConfig;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.service.common.enums.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 * ab测试类
 *
 * <AUTHOR>
 * @date 2022/10/14 16:00
 */
@Slf4j
@RestController
@RequestMapping("/plat/abtest")
public class AbtestController {

    @Autowired
    private AbtestService abtestService;

    @PostMapping(value = {"/getUserType", "/getUserType.action"})
    public ResultMsgBean getUserType(String code, Long userId) {

        if (StringUtils.isBlank(code)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        Integer userType = abtestService.getUserType(code, userId);

        return ResultMsgBean.successResponse(userType);
    }

    @PostMapping(value = {"/getUserTypeForList", "/getUserTypeForList.action"})
    public ResultMsgBean getUserTypeForList(String codes, Long userId) {
        List<String> codeList;
        if (StringUtils.isBlank(codes)) {
            List<TytAbtestConfig> abtestConfigList = abtestService.selectAllConfig();
            if (CollUtil.isEmpty(abtestConfigList)) {
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }
            codeList = abtestConfigList.stream().map(TytAbtestConfig::getCode).collect(Collectors.toList());
        } else {
            codeList = Arrays.stream(codes.split(COMMA)).collect(Collectors.toList());
        }
        List<TytAbtestConfigVo> userTypeList = abtestService.getUserTypeList(codeList, userId);

        return ResultMsgBean.successResponse(userTypeList);
    }


}
