package com.tyt.plat.controller.car;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.plat.entity.base.CurrentLocationVO;
import com.tyt.plat.entity.base.TytSigningCar;
import com.tyt.plat.entity.base.TytSigningCarVO;
import com.tyt.plat.entity.base.TytSpecialCarDispatchFailure;
import com.tyt.plat.mapper.base.TytSigningCarMapper;
import com.tyt.plat.mapper.base.TytSpecialCarDispatchFailureMapper;
import com.tyt.plat.service.block.TytBlockConfigService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.CarCurrentLocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/04/17 14:28
 */
@Slf4j
@RestController
@RequestMapping("/plat/signing")
public class TytSigningCarController {


    @Autowired
    private TytSigningCarMapper tytSigningCarMapper;

    @Autowired
    private TytBlockConfigService tytBlockConfigService;

    @Autowired
    private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;

    @Autowired
    private TransportMainService transportMainService;



    @RequestMapping ("/getInfo")
    public ResultMsgBean getInfo(Long userId) {
        if(null == userId){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        TytSigningCar car = tytSigningCarMapper.getByUserId(userId);

        return ResultMsgBean.successResponse(car);
    }

    /**
     * 司机是否可以支付专车货源
     * @param userId
     * @param srcMsgId
     * @return
     */
    @RequestMapping ("/isShowContractTab")
    public ResultMsgBean isShowContractTab(Long userId, Long srcMsgId) {
        if(null == srcMsgId || null == userId){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        //非专车货源不弹窗
        TransportMain bySrcMsgId = transportMainService.getBySrcMsgId(srcMsgId);
        if (bySrcMsgId == null || bySrcMsgId.getExcellentGoods() == null || bySrcMsgId.getExcellentGoods() != 2) {
            return ResultMsgBean.successResponse("1");
        }

        //给app返回的有值表示不弹窗，返回null表示弹窗
        TytSpecialCarDispatchFailure tytSpecialCarDispatchFailure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(srcMsgId);
        if (tytSpecialCarDispatchFailure != null && tytSpecialCarDispatchFailure.getDeclareInPublic() != null && tytSpecialCarDispatchFailure.getDeclareInPublic() == 1) {
            return ResultMsgBean.successResponse("1");
        } else {
            TytSigningCar car = tytSigningCarMapper.getByUserId(userId);
            return ResultMsgBean.successResponse(car == null ? null : "1");
        }
    }


    @RequestMapping ("/updateCar")
    public ResultMsgBean updateCar(Long userId,Integer status) {
        if(null == userId || null == status ){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        tytSigningCarMapper.updateCar(userId,status);

        return ResultMsgBean.successResponse();
    }


    @RequestMapping ("/getCarUser")
    public ResultMsgBean getCarUser(String ids,String city,String area) {
        if(StringUtils.isEmpty(ids)){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        List<TytSigningCarVO> list =  tytBlockConfigService.getCarUser(ids,city,area);

        return ResultMsgBean.successResponse(list);
    }


    @RequestMapping ("/getSigningCarBlack")
    public ResultMsgBean getSigningCarBlack(@RequestParam  Long userId) {

        Boolean black =  tytBlockConfigService.getSigningCarBlack(userId);

        return ResultMsgBean.successResponse(black);
    }



    /**
     * 调用bi测试
     * @param ids
     * @param city
     * @param area
     * @return ResultMsgBean
     */
    @RequestMapping ("/getBICarLocal")
    public ResultMsgBean getBICarLocal(String carNos) {

        List<CurrentLocationVO> list = tytBlockConfigService.getBICarLocal(carNos);

        return ResultMsgBean.successResponse(list);
    }

    /**
     * 修改指派次数
     * @param carInfoId 签约司机表id
     * @return rm
     */
    @RequestMapping ("/updateDriverAssignNum")
    public ResultMsgBean updateDriverAssignNum(Long carInfoId) {
        if(Objects.isNull(carInfoId)){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        tytBlockConfigService.updateDriverAssignNum(carInfoId);
        return ResultMsgBean.successResponse();
    }
}
