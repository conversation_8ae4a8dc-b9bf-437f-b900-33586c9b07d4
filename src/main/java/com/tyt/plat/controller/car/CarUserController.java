package com.tyt.plat.controller.car;

import com.alibaba.fastjson.JSONObject;
import com.tyt.base.controller.BaseController;
import com.tyt.dispatch.owner.service.DispatchOwnerService;
import com.tyt.model.Car;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.ocr.OcrService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.querybean.UserCarBean;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/05/20 13:19
 */
@Slf4j
@RestController
@RequestMapping("/plat/car")
public class CarUserController extends BaseController {

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;


    @Autowired
    private TytCarMapper tytCarMapper;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService identityAuthService;

    @Resource(name = "carService")
    private CarService carService;

    @Autowired
    private OcrService ocrService;

    @Autowired
    private TytSigningCarMapper tytSigningCarMapper;

    @Autowired
    private TytInvoiceDriverMapper tytInvoiceDriverMapper;

    @Autowired
    private TytSigningCarInfoMapper tytSigningCarInfoMapper;

    @Autowired
    private DispatchOwnerService dispatchOwnerService;

    @Autowired
    private TytSigningDriverBlackMapper tytSigningDriverBlackMapper;

    @Autowired
    private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;

    @Autowired
    private TytDispatchCooperativeMapper tytDispatchCooperativeMapper;

    private static final String ONE = "1";

    private static final int SIGNING_ONE = 1;

    private static final int TRANSPORT_FIVEL = 5;


    @RequestMapping ("/checkUserCar")
    public ResultMsgBean getInfo(@RequestParam Long id, @RequestParam Long userId,@RequestParam Long carId, Long driverId) {
        TransportMain main = transportMainService.getById(id);
        if(null == main){
             return new ResultMsgBean(ReturnCodeConstant.ERROR,"货源为空");
        }
        String dateContent = getDateContent(main);
        if(StringUtils.isNotEmpty(dateContent)){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info(dateContent));
        }

        try {
            Car car = carService.getById(carId);
            if (null == car){
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info("车辆不存在"));
            }
            String name = tytCarMapper.getByCarId(carId);
            if(StringUtils.isNotBlank(name)){
                TytUserIdentityAuth auth = identityAuthService.getTytUserIdentityAuth(main.getUserId());
                //如果车头所有人与货源发布人昵称不为空，判断是否相同
                if(null != auth && StringUtils.isNotEmpty(auth.getTrueName())){
                    if(name.equals(auth.getTrueName())){
                        return new ResultMsgBean(ReturnCodeConstant.ERROR,"不能委托自有车辆，请更换");
                    }
                }
                TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(main.getUserId());
                //判断发布人发货企业是否为空，不为空判断是否相同
                if(null != tytInvoiceEnterprise && StringUtils.isNotEmpty(tytInvoiceEnterprise.getEnterpriseName())){
                    if(name.equals(tytInvoiceEnterprise.getEnterpriseName())){
                        return new ResultMsgBean(ReturnCodeConstant.ERROR,"不能委托自有车辆，请更换");
                    }
                }
            }
            if (main.getExcellentGoods() == 2){
//                if (!"1".equals(car.getAuth())){
//                    return new ResultMsgBean(ReturnCodeConstant.ERROR,"车辆未认证通过");
//                }
                if (null != driverId){
                    TytInvoiceDriver driver = tytInvoiceDriverMapper.selectByPrimaryKey(driverId);
                    boolean flag = carService.checkSigningDriver(main,userId,driver);
                    if (!flag){
                        return ResultMsgBean.failResponse(ResponseEnum.request_error.info("司机不符合专车规则"));
                    }
                }
            }

            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            logger.error("查询车辆错误", e);
        }
        return ResultMsgBean.successResponse();
    }

    public String getDateContent(TransportMain main){
        String content = "";
        if (null == main) {
            content = "货源信息为空";
            return content;
        }
        if (null == main.getInvoiceTransport() || 0 == main.getInvoiceTransport()) {
            content = "非开票货源";
            return content;
        }

        return content;
    }


    @RequestMapping("/getSigningCarList")
    public ResultMsgBean getSigningCarList(@RequestParam Long userId) {
        try {
            List<UserCarBean> cars = carService.getUserCar(userId);
            if (CollectionUtils.isEmpty(cars)) {
                return ResultMsgBean.successResponse();
            }
            Map<String, List<UserCarBean>> carMap = new HashMap<>();
            List<UserCarBean> yCar = new ArrayList<>();
            List<UserCarBean> nCar = new ArrayList<>();
            for (UserCarBean bean : cars) {
                if (ONE.equals(bean.getAuth())) {
                    yCar.add(bean);
                } else {
                    nCar.add(bean);
                }
            }
            carMap.put("yCar", yCar);
            carMap.put("nCar", nCar);
            return ResultMsgBean.successResponse(carMap);
        } catch (Exception e) {
            logger.error("查询车辆列表错误", e);
            return ResultMsgBean.failResponse(e);
        }
    }



    @RequestMapping("/getSigningUserList")
    public ResultMsgBean getSigningUserList(@RequestParam Long userId,@RequestParam Long id) {
        log.info("getSigningUserList请求参数，userID:{}, id:{}", userId, id);
        TransportMain main = transportMainService.getById(id);
        if(null == main){
            return new ResultMsgBean(ReturnCodeConstant.ERROR,"货源为空");
        }
        TytSigningCar tytSigningCar = tytSigningCarMapper.getByUserId(userId);
        try {
            Map<String,List<TytInvoiceDriver>> driverMap = new HashMap<>();
            List<TytInvoiceDriver> drivers = tytInvoiceDriverMapper.getByUserId(userId);
            if(CollectionUtils.isEmpty(drivers)){
                return ResultMsgBean.successResponse(driverMap);
            }

            //只要该专车货源允许大厅抢单，则所有司机均可选
            TytSpecialCarDispatchFailure tytSpecialCarDispatchFailure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(id);
            if (tytSpecialCarDispatchFailure != null && tytSpecialCarDispatchFailure.getDeclareInPublic() != null && tytSpecialCarDispatchFailure.getDeclareInPublic() == 1) {
                driverMap.put("yDriver",drivers);
                return ResultMsgBean.successResponse(driverMap);
            }

            //如果不是签约商，司机都为不符合
            if(null == tytSigningCar){
                driverMap.put("nDriver",drivers);
                return ResultMsgBean.successResponse(driverMap);
            }
            List<TytInvoiceDriver> yInvoices = new ArrayList<>();
            List<TytInvoiceDriver> nInvoices = new ArrayList<>();
            Long cargoOwnerId = main.getCargoOwnerId();
            TytDispatchCargoOwner owner = null;
            TytDispatchCooperative cooperative = null;
            if (!Objects.isNull(cargoOwnerId) && cargoOwnerId != 0){
                cooperative = tytDispatchCooperativeMapper.selectByPrimaryKey(cargoOwnerId);
                if (null != cooperative){
                    owner = dispatchOwnerService.getByCooperativeId(cooperative.getId());
                }
            }
            for(TytInvoiceDriver driver : drivers){
               if (checkDriver(driver,tytSigningCar,cargoOwnerId,owner, cooperative)){
                   yInvoices.add(driver);
               }else{
                   nInvoices.add(driver);
               }
            }
            driverMap.put("yDriver",yInvoices);
            driverMap.put("nDriver",nInvoices);
            log.info("getSigningUserList请求参数，userID:{}, id:{}, 返回结果:{}", userId, id, JSONObject.toJSONString(driverMap));
            return ResultMsgBean.successResponse(driverMap);
        } catch (Exception e) {
            logger.error("查询车辆列表错误", e);
            return ResultMsgBean.failResponse(e);
        }
    }


    @RequestMapping("/checkCarDriver")
    public ResultMsgBean checkCarDriver(@RequestParam Long userId,@RequestParam Long id,@RequestParam Long carId,@RequestParam Long driverId) {
        TransportMain main = transportMainService.getById(id);
        if(null == main){
            return new ResultMsgBean(ReturnCodeConstant.ERROR,"货源为空");
        }

        try {
            boolean carData = false;
            boolean driverData = false;
            Car car = carService.getById(carId);
            if(null == car || !ONE.equals(car.getAuth())){
                carData = true;
            }
            //只要该专车货源允许大厅抢单，则所有司机均可选
            TytSpecialCarDispatchFailure tytSpecialCarDispatchFailure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(id);
            if (tytSpecialCarDispatchFailure != null && tytSpecialCarDispatchFailure.getDeclareInPublic() != null && tytSpecialCarDispatchFailure.getDeclareInPublic() == 1) {
                driverData = false;
            }else {
                TytSigningCar tytSigningCar = tytSigningCarMapper.getByUserId(userId);
                if(null == tytSigningCar){
                    return new ResultMsgBean(ReturnCodeConstant.ERROR,"无签约车辆");
                }
                //司机为空
                TytInvoiceDriver driver = tytInvoiceDriverMapper.selectByPrimaryKey(driverId);
                Integer blackNum = tytSigningDriverBlackMapper.getByDriverUserId(driver.getDriverUserId());
                if(null == driver || 0 < blackNum){
                    driverData = true;
                }
                if (Boolean.FALSE.equals(driverData)){
                    //不是签约车辆
                    Integer num = tytSigningCarInfoMapper.getByIdName(tytSigningCar.getId(),driver.getPhone());
                    if (0 >= num){
                        driverData = true;
                    }else {
                        Long cargoOwnerId = main.getCargoOwnerId();
                        if (!Objects.isNull(cargoOwnerId) && cargoOwnerId != 0){
                            TytDispatchCargoOwner owner = dispatchOwnerService.getById(cargoOwnerId);
                            String[] signing = tytSigningCar.getSigning().split(",");
                            if (Objects.isNull(owner) || (owner.getAssignCar() != 0 && !Arrays.asList(signing).contains(owner.getOwnerName()))){
                                driverData = true;
                            }
                        }
                    }
                }
            }
            Map<String,Boolean> map = new HashMap<>();
            if(carData || driverData){
                map.put("carData",carData);
                map.put("driverData",driverData);
                ResultMsgBean rm = new ResultMsgBean();
                rm.setCode(ReturnCodeConstant.INFO_FEE_OPERATE_DATA_DISABLED);
                rm.setData(map);
                return rm;
            }

            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            logger.error("校验专车司机车辆失败", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    private boolean checkDriver(TytInvoiceDriver driver, TytSigningCar tytSigningCar, Long cargoOwnerId, TytDispatchCargoOwner owner, TytDispatchCooperative cooperative){
        Integer blackNum = tytSigningDriverBlackMapper.getByDriverUserId(driver.getDriverUserId());
        if(0 < blackNum){
            return false;
        }
        //判断是否在签约车辆里面
        Integer num = tytSigningCarInfoMapper.getByIdName(tytSigningCar.getId(),driver.getPhone());
        if (0 >= num) {
            return false;
        }
        if (Objects.isNull(cargoOwnerId) || 0 == cargoOwnerId) {
            return true;
        }
        if (Objects.isNull(cooperative)){
            return false;
        }
        if (Objects.isNull(owner)) {
            return false;
        }
        if (owner.getAssignCar() == 0) {
            return true;
        }

        String[] signing = tytSigningCar.getSigning().split(",");
        return Arrays.asList(signing).contains(cooperative.getCooperativeName());
    }

    /**
     * 行驶证副页背面OCR
     */
    @GetMapping("/ocrDrivingLicenseSecondBackInfo")
    public ResultMsgBean vehicleLicenseDeputyPageBackOcr(@RequestParam("url") String url) {
        return ResultMsgBean.successResponse(ocrService.vehicleLicenseDeputyPageBackOcr(url));
    }

    /**
     * 行驶证副页背面OCR
     */
    @GetMapping("/roadTransportQualificationCertificateOcr")
    public ResultMsgBean roadTransportQualificationCertificateOcr(@RequestParam("url") String url) {
        return ResultMsgBean.successResponse(ocrService.roadTransportQualificationCertificateBackOcr(url));
    }
}
