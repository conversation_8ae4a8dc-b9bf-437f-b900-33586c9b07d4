package com.tyt.plat.controller.car;

import com.tyt.base.bean.BaseParameter;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.service.help.UserCourseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/course/user")
@Slf4j
public class CourseUserController {


    @Autowired
    private UserCourseService userCourseService;

    /**
     * 查询用户是否需要展示角标
     * @param base 参数
     * @return ResultMsgBean
     */
    @RequestMapping("/getNewCourse")
    public ResultMsgBean getInfo(BaseParameter base,@RequestParam Integer type) {
        Boolean show = userCourseService.getNewCourse(type,base.getUserId());
        return ResultMsgBean.successResponse(show);
    }

    /**
     * 上报
     * @param base 参数
     * @return ResultMsgBean
     */
    @RequestMapping("/saveNewCourse.action")
    public ResultMsgBean saveNewCourse(BaseParameter base,@RequestParam Integer type) {
        userCourseService.saveNewCourse(type,base.getUserId());
        return ResultMsgBean.successResponse();
    }

}
