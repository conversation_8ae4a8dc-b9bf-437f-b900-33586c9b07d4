package com.tyt.plat.controller.car;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.service.dw.DwMarketV4AgentInfoService;
import com.tyt.service.common.enums.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/04/18 11:03
 */
@Slf4j
@RestController
@RequestMapping("/plat/dwAgent")
public class DwMarketV4AgentInfoController {



    @Autowired
    private DwMarketV4AgentInfoService dwMarketV4AgentInfoService;

    /**
     * 商家入驻
     * @param contact 联系人
     * @param phone 联系电话
     * @param brandName 品牌
     * @return ResultMsgBean
     */
    @RequestMapping(value = {"/saveInfo", "/saveInfo.action"})
    public ResultMsgBean saveInfo(String contact, String phone, String brandName) {

        if (StringUtils.isBlank(contact) || StringUtils.isBlank(phone) || StringUtils.isBlank(brandName)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            dwMarketV4AgentInfoService.saveInfo(contact,phone,brandName);
            return ResultMsgBean.successResponse();
        }catch (Exception e){
            log.info("商家入驻失败",e);
            return ResultMsgBean.failResponse(e);
        }


    }
}
