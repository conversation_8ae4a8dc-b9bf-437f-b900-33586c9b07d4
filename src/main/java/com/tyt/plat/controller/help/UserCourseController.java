package com.tyt.plat.controller.help;

import com.tyt.base.bean.BaseParameter;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytCourseBanner;
import com.tyt.plat.entity.base.TytCourseCategory;
import com.tyt.plat.entity.base.TytCourseInfo;
import com.tyt.plat.service.help.UserCourseService;
import com.tyt.plat.vo.help.CourseDetailVo;
import com.tyt.plat.vo.help.UserCourseCountVo;
import com.tyt.service.common.enums.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户课程.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/userFrame/userCourse")
public class UserCourseController {

    @Autowired
    private UserCourseService userCourseService;

    /**
     * 所有课程列表（热门推荐，分类下课程）
     *
     * @param type       type
     * @param sortNumber 排序编号
     * @return ResultMsgBean
     */
    @GetMapping("/getCourseList")
    public ResultMsgBean getAllCourseList(Integer type, Long categoryId, Integer sortNumber) {

        if (type == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        List<TytCourseInfo> courseList = userCourseService.getCourseList(type, categoryId, sortNumber);
        return ResultMsgBean.successResponse(courseList);

    }

    /**
     * banner列表
     *
     * @param type type
     * @return ResultMsgBean
     */
    @GetMapping("/getBannerList")
    public ResultMsgBean getBannerList(Integer type) {

        if (type == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        List<TytCourseBanner> bannerList = userCourseService.getBannerList(type);
        return ResultMsgBean.successResponse(bannerList);

    }

    /**
     * 分类列表
     *
     * @param type type
     * @return ResultMsgBean
     */
    @GetMapping("/getCategoryList")
    public ResultMsgBean getCategoryList(Integer type) {

        if (type == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        List<TytCourseCategory> courseCategorieList = userCourseService.getCategoryList(type);
        return ResultMsgBean.successResponse(courseCategorieList);

    }

    /**
     * 获取待学习数量
     *
     * @param type          type
     * @param baseParameter 基础参数
     * @return ResultMsgBean
     */
    @GetMapping("/getNotLearnCount")
    public ResultMsgBean getNotLearnCount(Integer type, BaseParameter baseParameter) {

        if (type == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        UserCourseCountVo courseCountVo = userCourseService.getNotLearnCount(type, baseParameter);
        return ResultMsgBean.successResponse(courseCountVo);
    }


    /**
     * 获取学习进度.
     *
     * @param type          type
     * @param userId userId
     * @return ResultMsgBean
     */
    @GetMapping("/getLearnInfo")
    public ResultMsgBean getLearnInfo(Long userId, Integer type) {

        if (type == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        UserCourseCountVo courseCountVo = userCourseService.getLearnInfo(userId, type);
        return ResultMsgBean.successResponse(courseCountVo);
    }

    /**
     * 所有课程列表（热门推荐，分类下课程）
     *
     * @param type       type
     * @param sortNumber 排序编号
     * @return ResultMsgBean
     */
    @GetMapping("/getMyCourseList")
    public ResultMsgBean getMyCourseList(BaseParameter baseParameter, Integer type, Integer learnFlag, Integer sortNumber) {

        if (CommonUtil.hasNull(type, learnFlag)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        List<TytCourseInfo> myCourseList = userCourseService.getMyCourseList(baseParameter.getUserId(), type, learnFlag, sortNumber);

        return ResultMsgBean.successResponse(myCourseList);
    }

    /**
     * 查询课程详情.
     *
     * @param courseId       courseId
     * @param userId userId
     * @return ResultMsgBean
     */
    @GetMapping("/getCourseDetail")
    public ResultMsgBean getCourseDetail(Long userId, Long courseId) {

        if (CommonUtil.hasNull(courseId, userId)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        CourseDetailVo courseDetail = userCourseService.getCourseDetail(userId, courseId);
        return ResultMsgBean.successResponse(courseDetail);
    }

    /**
     * 预览课程详情.
     *
     * @param courseId       courseId
     * @return ResultMsgBean
     */
    @GetMapping("/previewCourse.action")
    public ResultMsgBean previewCourse(Long courseId, String previewKey) {

        if (CommonUtil.hasNull(courseId, previewKey)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        userCourseService.checkPreviewKey(courseId, previewKey);

        CourseDetailVo courseDetail = userCourseService.getCourseContent(courseId, false);
        return ResultMsgBean.successResponse(courseDetail);
    }

    /**
     * 保存学习进度.
     *
     * @param courseId       courseId
     * @return ResultMsgBean
     */
    @PostMapping("/saveStudy")
    public ResultMsgBean saveStudy(Long userId, Long courseId) {

        if (CommonUtil.hasNull(userId, courseId)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        userCourseService.saveStudy(userId, courseId);
        return ResultMsgBean.successResponse();
    }

}
