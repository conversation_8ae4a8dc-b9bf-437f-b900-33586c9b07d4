package com.tyt.plat.controller.user;

import com.tyt.base.bean.BaseParameter;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.constant.FrontPageConstant;
import com.tyt.plat.enums.front.page.ProtocolPopEnum;
import com.tyt.plat.service.user.FrontPageService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.user.FrontPageDetailVo;
import com.tyt.plat.vo.user.ProtocolIsPopUpVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Objects;

/**
 * 运营中台.
 */
@Slf4j
@RestController
@RequestMapping("/userFrame/frontPage")
public class FrontPageController {

    @Autowired
    private FrontPageService frontPageService;

    /**
     * 获取页面详情
     * @param pageId 页面id
     * @param baseParameter 基础参数
     * @return ResultMsgBean
     */
    @GetMapping(value = {"getPageDetail", "getPageDetail.action"})
    public ResultMsgBean getPageDetail(Long pageId, BaseParameter baseParameter) {

        if (pageId == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            FrontPageDetailVo pageDetail = frontPageService.getPageDetail(pageId);
            return ResultMsgBean.successResponse(pageDetail);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getPageDetail : pageId : " + pageId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 协议是否弹窗
     * <a href="http://www.teyuntong.net/tytModel/middleMarket?noTitle=yes&pageId=50065">.</a> 默认是这个协议
     *
     * @param pageId     协议的主键id
     * @param modifyTime 前端传的修改时间
     * @return
     */
    @GetMapping("plat/protocolIsPopUp")
    public ResultMsgBean protocolIsPopUp(@RequestParam(value = "pageId",required = false) Long pageId,
                                         @RequestParam(value = "modifyTime",required = false)  String modifyTime) {
       //设置协议默认值
        if(Objects.isNull(pageId)){
            pageId = FrontPageConstant.PAGE_ID;
        }
        try {
            log.info("protocolIsPopUp pageId: {} modifyTime: {}" , pageId, modifyTime);
            Date dmodifyTime = null;
            if(!StringUtils.isEmpty(modifyTime)){
                dmodifyTime = TimeUtil.timeStampToDate(new Long(modifyTime));
            }
            ProtocolIsPopUpVo protocolIsPopUpVo = frontPageService.getProtocolIsPopUp(pageId,dmodifyTime);
            return ResultMsgBean.successResponse(protocolIsPopUpVo);
        } catch (Exception e) {
            log.error("protocolIsPopUp pageId: {} modifyTime: {}" , pageId, modifyTime,e);
            ProtocolIsPopUpVo errorProtocolIsPopUpVo = new ProtocolIsPopUpVo();
            errorProtocolIsPopUpVo.setPop(ProtocolPopEnum.NO_POP.getCode());
            return ResultMsgBean.failResponse(ReturnCodeConstant.OK,"网络异常,请稍后重试");
        }
    }

}
