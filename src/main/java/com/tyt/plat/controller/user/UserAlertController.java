package com.tyt.plat.controller.user;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.service.user.UserAlertService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.service.common.enums.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户身份标签.
 */
@Slf4j
@RestController
@RequestMapping("/userFrame/userAlert")
public class UserAlertController {

    @Autowired
    private UserAlertService userAlertService;

    /**
     * 会员购买页红包弹窗
     * @param userId userId
     * @return ResultMsgBean
     */
    @PostMapping("/vipRedPacket")
    public ResultMsgBean vipRedPacket(Long userId) {

        if (CommonUtil.hasNull(userId)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            Integer popStatus = userAlertService.vipRedPacket(userId);
            return ResultMsgBean.successResponse(popStatus);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("vipRedPacket : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

}
