package com.tyt.plat.controller.user;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.service.user.UserIdentityLabelService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.service.common.enums.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户身份标签.
 */
@Slf4j
@RestController
@RequestMapping("/userFrame/identityLabel")
public class UserIdentityLabelController {

    @Autowired
    private UserIdentityLabelService userIdentityLabelService;

    /**
     * 保存车主身份标签
     * @param goodsTypeFirst 一级身份
     * @param goodsTypeSecond 二级身份
     * @return ResultMsgBean
     */
    @PostMapping("/saveGoosType")
    public ResultMsgBean saveGoosType(Long userId, Integer goodsTypeFirst, Integer goodsTypeSecond) {

        if (CommonUtil.hasNull(userId, goodsTypeFirst, goodsTypeSecond)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            userIdentityLabelService.saveGoosType(userId, goodsTypeFirst, goodsTypeSecond);
            return ResultMsgBean.successResponse();

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("saveGoosType_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

}
