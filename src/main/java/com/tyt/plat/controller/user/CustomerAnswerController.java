package com.tyt.plat.controller.user;

import com.tyt.base.bean.BaseParameter;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.constant.PlatBaseConstant;
import com.tyt.plat.entity.base.TytAnswerCategory;
import com.tyt.plat.entity.base.TytAnswerPage;
import com.tyt.plat.service.user.CustomerAnswerService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.user.AnswerPageDetailVo;
import com.tyt.service.common.enums.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客服问答
 */
@Slf4j
@RestController
@RequestMapping("/userFrame/customerAnswer")
public class CustomerAnswerController {

    @Autowired
    private CustomerAnswerService customerAnswerService;

    @GetMapping("/getCategoryList")
    public ResultMsgBean getCategoryList(Integer belongType, BaseParameter baseParameter) {

        if (belongType == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        List<TytAnswerCategory> categoryList = customerAnswerService.getCategoryList(belongType);

        return ResultMsgBean.successResponse(categoryList);
    }

    @GetMapping("/getAnswerList")
    public ResultMsgBean getAnswerList(Long categoryId, Integer pageSize, Integer sortNumber, BaseParameter baseParameter) {

        if (categoryId == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        if(pageSize == null){
            pageSize = PlatBaseConstant.DEFAULT_PAGE_SIZE;
        }
        if(pageSize > 100){
            pageSize = PlatBaseConstant.DEFAULT_PAGE_SIZE;
        }

        List<TytAnswerPage> answerList = customerAnswerService.getAnswerList(categoryId, pageSize, sortNumber);

        return ResultMsgBean.successResponse(answerList);
    }

    @GetMapping("/getPageDetail")
    public ResultMsgBean getPageDetail(Long pageId, BaseParameter baseParameter) {

        if (pageId == null) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {

            AnswerPageDetailVo pageDetail = customerAnswerService.getPageDetail(pageId);
            return ResultMsgBean.successResponse(pageDetail);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getPageDetail : pageId : " + pageId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 根据问答标题模糊查询问答列表
     *
     * @param titleFragment 问答标题片段
     * @param belongType 类型（1车，2货）
     * @return 问答列表
     */
    @GetMapping("/queryAnswerByTitleFragment")
    public ResultMsgBean search(@RequestParam("titleFragment") String titleFragment, @RequestParam("belongType") Integer belongType) {
        titleFragment = StringUtils.replace(titleFragment.trim(), "%", "");
        titleFragment = StringUtils.replace(titleFragment.trim(), "_", "");
        if (StringUtils.isBlank(titleFragment)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        if (belongType == null || (belongType != 1 && belongType != 2)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }
        List<TytAnswerPage> tytAnswerPages;
        try {
            tytAnswerPages = customerAnswerService.queryByTitleFragment(titleFragment, belongType);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("search customer answer : titleFragment : " + titleFragment, e);
            return ResultMsgBean.failResponse(e);
        }
        return ResultMsgBean.successResponse(tytAnswerPages);
    }

}
