package com.tyt.plat.controller.user;

import com.tyt.base.controller.BaseController;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.enums.GlobalStatusEnum;
import com.tyt.plat.service.user.UserVerifyService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.user.EnterpriseFaceVerifyReq;
import com.tyt.plat.vo.user.FaceUserIdentityAuthReq;
import com.tyt.plat.vo.user.UserFaceVerifyVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.service.BlacklistIdentityCardService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户身份标签.
 */
@Slf4j
@RestController
@RequestMapping("/userFrame/userVerify")
public class UserVerifyController extends BaseController {

    @Autowired
    private UserVerifyService userVerifyService;

    @Autowired
    private TytUserIdentityAuthService tytUserIdentityAuthService;

    @Autowired
    private BlacklistIdentityCardService blacklistIdentityCardService;

    /**
     * 用户二要素验证
     * @param userId userId
     * @param userName userName
     * @param idCard idCard
     * @return
     */
    @PostMapping("/realVerify")
    public ResultMsgBean realVerify(Long userId, String idCard, String userName) {

        if (CommonUtil.hasNull(userId, idCard, userName)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        //校验身份是否被拉黑
        boolean checkResult = blacklistIdentityCardService.checkIdCardBlackList(idCard);
        if (checkResult) {
            blacklistIdentityCardService.blackListUser(userId, idCard);
            return ResultMsgBean.failResponse(ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_CODE, ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_MSG);
        }

        try {
            Integer verifyResult = userVerifyService.checkAndRealVerify(userId, idCard, userName);
            return ResultMsgBean.successResponse(verifyResult);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("vipRedPacket : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 人脸识别token.
     * @param userId userId
     * @param idCard idCard
     * @return
     */
    @Deprecated
    @PostMapping("/getFaceTokenV5")
    public ResultMsgBean getFaceTokenV5(Long userId, String idCard) {

        if (CommonUtil.hasNull(userId, idCard)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            UserFaceVerifyVo token = userVerifyService.getFaceTokenV5(userId, idCard);
            return ResultMsgBean.successResponse(token);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("vipRedPacket : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 人脸识别认证，并提交认证结果.
     * @param userIdentityAuthReq
     * @return
     */
    @Deprecated
    @PostMapping("/faceVerifyV5")
    @ResponseBody
    public ResultMsgBean faceVerifyV5(FaceUserIdentityAuthReq userIdentityAuthReq) {

        try {
            Long userId = userIdentityAuthReq.getUserId();

            MultipartFile mainUrlPic = userIdentityAuthReq.getMainUrlPic();
            MultipartFile iPhotoPic = userIdentityAuthReq.getIPhotoPic();
            if (!super.isAllImg(mainUrlPic, iPhotoPic)) {
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info("图片格式有误！"));
            }

            UserFaceVerifyVo facePass = tytUserIdentityAuthService.saveFaceVerifyV5(userIdentityAuthReq);

            if(GlobalStatusEnum.yes.equalsCode(facePass.getVerifyStatus())){
                //认证成功
                tytUserIdentityAuthService.sendUserAutoVerifyMq(userId);
            }

            return ResultMsgBean.successResponse(facePass);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("faceVerify_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 人脸识别token.
     * @param userId userId
     * @param idCard idCard
     * @return
     */
    @PostMapping("/getFaceTokenV3")
    public ResultMsgBean getFaceTokenV3(Long userId, String idCard, String trueName) {

        if (CommonUtil.hasNull(userId, idCard, trueName)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            UserFaceVerifyVo token = userVerifyService.getFaceTokenV3(userId, idCard, trueName);
            return ResultMsgBean.successResponse(token);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getFaceTokenV3 : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 人脸识别认证，并提交认证结果.
     * @param userIdentityAuthReq
     * @return
     */
    @PostMapping("/faceVerifyV3")
    @ResponseBody
    public ResultMsgBean faceVerifyV3(@Validated FaceUserIdentityAuthReq userIdentityAuthReq) {

        try {
            Long userId = userIdentityAuthReq.getUserId();

            if(userId == null){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            MultipartFile mainUrlPic = userIdentityAuthReq.getMainUrlPic();
            MultipartFile iPhotoPic = userIdentityAuthReq.getIPhotoPic();
            if (!super.isAllImg(mainUrlPic, iPhotoPic)) {
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info("图片格式有误！"));
            }
            log.info("faceVerifyV3_time_test_001");
            UserFaceVerifyVo faceVerifyVo = tytUserIdentityAuthService.saveFaceVerifyV3(userIdentityAuthReq);

            log.info("faceVerifyV3_time_test_002");
            if(GlobalStatusEnum.yes.equalsCode(faceVerifyVo.getVerifyStatus())){
                //认证成功
                tytUserIdentityAuthService.sendUserAutoVerifyMq(userId);

                log.info("faceVerifyV3_time_test_003");
            }

            return ResultMsgBean.successResponse(faceVerifyVo);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("faceVerify_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 企业签约时，进行人脸识别token，v3版本.
     * @param userId userId
     * @return
     */
    @PostMapping("/getEnterpriseFaceToken")
    public ResultMsgBean getEnterpriseFaceToken(Long userId) {

        if (CommonUtil.hasNull(userId)) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            UserFaceVerifyVo token = userVerifyService.getEnterpriseFaceToken(userId);
            return ResultMsgBean.successResponse(token);

        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getEnterpriseFaceToken : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 企业认证时，企业认证-单独校验人脸识别v3
     * @param enterpriseFaceVerifyReq enterpriseFaceVerifyReq
     * @return ResultMsgBean
     */
    @PostMapping("/enterpriseFaceVerify")
    public ResultMsgBean enterpriseFaceVerify(@Validated EnterpriseFaceVerifyReq enterpriseFaceVerifyReq) {

        try {
            Long userId = enterpriseFaceVerifyReq.getUserId();

            if(userId == null){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            UserFaceVerifyVo faceVerifyVo = userVerifyService.updateEnterpriseFaceVerify(enterpriseFaceVerifyReq);

            return ResultMsgBean.successResponse(faceVerifyVo);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("enterpriseFaceVerify_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

}
