package com.tyt.plat.commons.internal;

import com.alibaba.fastjson.JSON;
import com.tyt.plat.enums.PlatResponseEnum;
import com.tyt.plat.enums.esign.EsignResponseEnum;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import retrofit2.Response;

/**
 * 内部调用服务util
 *
 * <AUTHOR>
 * @date 2024/4/24 13:40
 */
@Slf4j
public class InternalClientUtil {

    /**
     * 获取数据，如果有错误则抛出异常
     * @param response response
     * @param <T> response
     * @return T
     */
    public static <T> T getDataDetail(Response<InternalWebResult<T>> response) {
        log.info("getDataDetail_response:{}", response);
        if (!response.isSuccessful()) {
            log.info("internal_client_response_error:{}", response);
            throw TytException.createException(ResponseEnum.rest_error.info("请求服务失败！"));
        }

        InternalWebResult<T> bodyResult = response.body();

        PlatCommonUtil.printFixLog("getDataDetail", bodyResult);

        T resultData = InternalWebResult.getResultData(bodyResult);
        return resultData;
    }

    /**
     * 获取数据，如果有错误则抛出异常
     * @param response response
     * @param <T> response
     * @return T
     */
    public static <T> T getDataDetailNoThrowException(Response<InternalWebResult<T>> response) {
        log.info("getDataDetail_response:{}", response);
        if (!response.isSuccessful()) {
            log.info("internal_client_response_error:{}", response);
            return null;
        }

        InternalWebResult<T> bodyResult = response.body();

        PlatCommonUtil.printFixLog("getDataDetail", bodyResult);

        T resultData = InternalWebResult.getResultData(bodyResult);
        return resultData;
    }

    /**
     * 处理esign异常.
     * @param e e
     * @return TytException
     */
    private static TytException handleEsignException(Exception e){
        TytException te = TytException.createException(e);

        int errorCode = te.getErrorCode();
        String errorMsg = te.getErrorMsg();

        if(PlatResponseEnum.ESIGN_API_ERROR.equalsCode(errorCode)){
            ResponseCode responseCode = JSON.parseObject(errorMsg, ResponseCode.class);

            if(responseCode != null){
                Integer apiCode = responseCode.getCode();
                String apiMessage = responseCode.getMsg();

                EsignResponseEnum responseEnum = EsignResponseEnum.getResponseEnum(apiCode);

                if(responseEnum != null){
                    apiMessage = responseEnum.getMsg();
                }
                if(StringUtils.isNotBlank(apiMessage)){
                    te.setErrorMsg(apiMessage);
                }
            }
        }
        return te;
    }

    /**
     * 获取esign数据，如果有错误则抛出异常
     * 特殊错误码时需要解析错误信息
     * @param response response
     * @param <T> response
     * @return T
     */
    public static <T> T getEsignDataDetail(Response<InternalWebResult<T>> response) {

        try {
            T dataDetail = getDataDetail(response);
            return dataDetail;
        } catch (Exception e) {
            TytException te = handleEsignException(e);
            throw te;
        }

    }


}
