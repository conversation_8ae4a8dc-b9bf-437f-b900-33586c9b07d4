package com.tyt.plat.commons.internal;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;

/**
 * retrofit 拦截器.
 *
 * <AUTHOR>
 * @date 2024/4/24 14:01
 */
public class InternalClientInterceptor implements Interceptor {

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request oldRequest = chain.request();

        // 构建新请求
        Request newRequest = oldRequest.newBuilder()
                .addHeader("tyt-internal-client", "plat")
                .build();

        return chain.proceed(newRequest);
    }
}
