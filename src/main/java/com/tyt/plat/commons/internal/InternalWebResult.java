package com.tyt.plat.commons.internal;

import com.tyt.plat.enums.PlatResponseEnum;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 调用内部服务使用的返回对象.
 * <AUTHOR>
 * @since 2023/10/19 10:44
 */
@Slf4j
@Data
public class InternalWebResult<T> {

    /**
     * 返回码
     */
    private String code;

    /**
     * 返回信息
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;


    public InternalWebResult() {
    }

    //操作成功
    public static final String SUCCESS_CODE = "00000000";

    /**
     * 判断返回码是否正确
     * @param resp resp
     * @param respCode respCode
     * @return boolean
     */
    public static boolean codeEquals(InternalWebResult<?> resp, String respCode) {
        boolean result = false;
        if(resp != null && resp.getCode() != null && resp.getCode().equals(respCode)){
            result = true;
        }
        return result;
    }

    /**
     * 校验返回结果
     * @param resp resp
     * @return boolean
     */
    public static boolean check(InternalWebResult<?> resp, boolean throwError) {

        if (resp != null && codeEquals(resp, SUCCESS_CODE)) {
            return true;
        }

        ResponseCode errorCode = PlatResponseEnum.INTERNAL_CALL_ERROR.info();

        if(resp != null){

            String codeResp = resp.getCode();

            int codeInt = errorCode.getCode();

            try {
                codeInt = Integer.parseInt(codeResp);
            } catch (Exception e) {
                log.error("check_code_parse_error : ", codeResp);
            }

            String msgResp = resp.getMsg();

            errorCode.setCode(codeInt);
            errorCode.setMsg(msgResp);
        }

        if(throwError) {
            throw TytException.createException(errorCode);
        }

        return false;
    }

    /**
     * 获取并校验返回结果，失败校验
     * @param resp resp
     * @param <T> T
     * @return T
     */
    public static <T> T getResultData(InternalWebResult<T> resp, boolean throwError) {
        boolean check = check(resp, throwError);
        T result = null;
        if(check) {
            result = resp.getData();
        }
        return result;
    }

    /**
     * 获取并校验返回结果 快速失败模式
     *
     * @param resp resp
     * @param <T> T
     * @return T
     */
    public static <T> T getResultData(InternalWebResult<T> resp) {
        return getResultData(resp, true);
    }

}
