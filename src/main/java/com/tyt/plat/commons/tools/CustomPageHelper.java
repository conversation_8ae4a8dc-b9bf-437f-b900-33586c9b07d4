package com.tyt.plat.commons.tools;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tyt.common.bean.PageData;
import com.tyt.plat.commons.model.PageParameter;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * PageHelper 辅助类
 */
@Data
public class CustomPageHelper implements Serializable {

    public static final int default_page_index = 0;
    public static final int default_page_size = 0;

    private PageParameter pageParameter;

    //当前页码
    private Integer pageNum = 1;
    //每页显示页数
    private Integer pageSize = 10;

    //分页开始的位置（尽量使用 pageNumber）
    private Integer start;

    private String orderBySql;

    /** 总行数 **/
    private Long total = 0L;

    /**
     * 总页数
     */
    private Integer pages = 0;

    /** pagger **/
    @JSONField(serialize=false)
    @JsonIgnore
    private Page pagger;

    /**
     * 适用于 自带的分页方式
     * @param pageParameter    分页参数类
     */
    private CustomPageHelper(PageParameter pageParameter){
        this.initPagger(pageParameter);
    }

    /**
     * 初始化
     * @param pageParameter
     */
    private void initPagger(PageParameter pageParameter){

        if(pageParameter == null){
            pageParameter = new PageParameter();
        }

        Integer pageNumber = pageParameter.getPageNum();
        Integer pageSize = pageParameter.getPageSize();
        Integer start = pageParameter.getStart();

        String orderBySql = pageParameter.createOrderBySql();

        if(pageNumber == null){
            pageNumber = default_page_index;
        }
        if(pageSize == null){
            pageSize = default_page_size;
        }

        this.pageNum = pageNumber;
        this.pageSize = pageSize;
        this.start = start;
        this.orderBySql = orderBySql;

        this.startPagger();
    }

    private void startPagger(){
        if(this.start != null && this.start >= 0) {
            this.pagger = PageHelper.offsetPage(this.start, pageSize);
            this.pagger.setOrderBy(orderBySql);
        }else {
            this.pagger = PageHelper.startPage(pageNum, pageSize, orderBySql);
        }
    }

    /**
     * 查询之前 生成pagger
     * 调用之后必须跟 sql 查询语句、endPage() 或 clearPage() 其中的一种
     * @return
     */
    public static CustomPageHelper startPage(PageParameter pageParameter){

        CustomPageHelper pageHelper = new CustomPageHelper(pageParameter);

        return pageHelper;
    }

    /**
     * 清除 ThreadLocal 分页参数
     */
    public static void clearPage(){
        PageHelper.clearPage();
    }

    /**
     * 查询完毕，设置结果和页数
     */
    private void readPageResult() {

        if(pagger != null){
            this.total = pagger.getTotal();
            this.pages = pagger.getPages();
        } else {
            throw TytException.createException(ResponseEnum.sys_error.info("pagger is null!"));
        }
    }

    /**
     * 生成 page 对象
     * @param dataList
     * @param <E>
     * @return
     */
    public <E> PageData<E> endPage(List<E> dataList){
        clearPage();
        this.readPageResult();

        PageData<E> pageData = new PageData<>();

        pageData.setPageNum(this.pageNum);
        pageData.setPageSize(this.pageSize);
        pageData.setTotal(this.total);
        pageData.setPages(this.pages);
        pageData.setList(dataList);

        return pageData;
    }

}
