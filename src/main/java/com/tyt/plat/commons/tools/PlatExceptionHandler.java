package com.tyt.plat.commons.tools;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.utils.PlatCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestControllerAdvice(basePackages = "com.tyt.plat.controller")
public class PlatExceptionHandler {

    private String getRequestUri(){
        HttpServletRequest request = ((ServletRequestAttributes)(RequestContextHolder.currentRequestAttributes())).getRequest();

        String requestUri = request.getRequestURI();

        return requestUri;
    }

    /**
     * 统一处理所有拦截
     * @param e 异常信息
     * @return ResultBean 统一返回类
     */
    @ExceptionHandler(value = Exception.class)
    public ResultMsgBean handleException(Exception e) {
        String errorMsg = e.getMessage();

        String requestUri = this.getRequestUri();

        PlatCommonUtil.printErrorInfo("handleException_error, uri : " + requestUri + " : ", e);

        ResultMsgBean resp = ResultMsgBean.failResponse(e);
        return resp;
    }

}
