package com.tyt.plat.commons.tools;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.commons.properties.AliOssProperty;
import com.tyt.plat.enums.FileTypeEnum;
import com.tyt.plat.utils.TytFileUtil;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * 阿里云oss文件操作
 * @date 2023/12/15 10:33
 */
@Slf4j
public class AliOssClient {

    private AliOssProperty ossProperty;

    private String profile;

    @Getter
    private OSS ossClient;

    public AliOssClient(AliOssProperty ossProperty, String profile) {
        log.info("initOSSConfig ========== ");
        this.ossProperty = ossProperty;
        this.profile = profile;

    }

    /**
     * 初始化 oss.
     */
    public void initOss() {
        String endpoint = ossProperty.getEndpoint();
        String accessKeyId = ossProperty.getAccessKeyId();
        String accessKeySecret = ossProperty.getAccessKeySecret();

        this.ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

    }

    /**
     * 关闭资源.
     */
    public void close() {
        if (ossClient != null) {
            // 关闭OSSClient。
            ossClient.shutdown();
        }
    }

    /**
     * 文件上传，改方法会覆盖原始文件，注意 finalKey 唯一性.
     *
     * @param input          文件流
     * @param fileKey        文件路径及名称
     * @param objectMetadata 可以为空
     * @param download       是否直接下载
     * @return String
     */
    public String upload(InputStream input, String fileKey, ObjectMetadata objectMetadata, Boolean download) {

        if (StringUtils.isEmpty(fileKey)) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }

        String contentType = FileTypeEnum.getContentType(fileKey);

        if (objectMetadata == null) {
            objectMetadata = new ObjectMetadata();
        }

        File tmpFile = new File(fileKey);
        String fileName = tmpFile.getName();

        objectMetadata.setContentType(contentType);
        try {
            if (download != null && download) {
                //可以直接下载的
                objectMetadata.setContentDisposition("attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            } else {
                //直接浏览器打开的
                objectMetadata.setContentDisposition("inline;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            }
        } catch (UnsupportedEncodingException e) {
            throw TytException.createException(e);
        }

        this.ossClient.putObject(ossProperty.getBucketName(), fileKey, input, objectMetadata);

        String result = CommonUtil.joinPath(ossProperty.getDomain(), fileKey);

        return result;
    }

    public void downloadFile(String url, String localPath){
        // 解析完整URL，提取存储桶名称和对象键
        try {
            File file = new File(localPath);
            URL fullUrl = new URL(url);
            String host = fullUrl.getHost();
            String bucketName = host.split("\\.")[0];
            String objectKey = fullUrl.getPath().substring(1); // 去除路径开头的斜杠
            this.ossClient.getObject(new GetObjectRequest(bucketName, objectKey), file);
        } catch (Exception e) {
            log.error(url+"下载文件失败", e);
        }

    }

    /**
     * 文件上传，改方法会覆盖原始文件，注意 finalKey 唯一性
     *
     * @param dataByte       文件byte
     * @param fileKey        文件路径及名称
     * @param objectMetadata 可以为空
     * @param download       是否直接下载
     * @return String
     */
    public String upload(byte[] dataByte, String fileKey, ObjectMetadata objectMetadata, Boolean download) {
        String result = null;

        try (InputStream input = new ByteArrayInputStream(dataByte)) {

            result = this.upload(input, fileKey, objectMetadata, download);
        } catch (Exception e) {
            throw TytException.createException(e);
        }

        return result;
    }

    /** ========== 下载文件 ========== **/

    /**
     * 下载阿里云文件.
     *
     * @param url
     * @return
     * @throws Exception
     */
    public void downloadOss(String url, OutputStream output) throws IOException {

        URL urlObj = new URL(url);

        OSSObject ossObj = this.ossClient.getObject(urlObj, null);

        try (InputStream input = ossObj.getObjectContent();) {
            IOUtils.copy(input, output);
        }

    }

    /**
     * 下载阿里云文件.
     *
     * @param url
     * @return
     * @throws Exception
     */
    public byte[] downloadOssBytes(String url) throws IOException {
        byte[] imgBytes = null;

        try (ByteArrayOutputStream output = new ByteArrayOutputStream();) {

            this.downloadOss(url, output);
            imgBytes = output.toByteArray();

        }
        return imgBytes;
    }

    /**
     * 生成 oss 存储用的 folder，用来指定具体名称的操作.
     * @param type type
     * @return String
     */
    public String createDateFolder(String type){

        String dateFolder = TytFileUtil.dateFolder();

        String fullPath = CommonUtil.joinPath(this.profile, TytFileUtil.APP_PATH_NAME, type, dateFolder);

        return fullPath;
    }

    /**
     * 生成oss文件路径
     * path 规则： /${env}/${app_name}/${type_name}/${yyyy}/${MM}/${dd}/${uuid_file_name}
     * 示例： http://peimages.teyuntong.net/online/invoice/import/2023/12/15/cae513bd35d5782dafe98e18ffd3b5e5.xslx
     * @param fileType 文件类型
     * @param fileName 文件名称，主要需要文件的后缀.
     * @return String
     */
    public String createFullPath(String fileType, String fileName, boolean keepOrigin){

        String dateFolder = this.createDateFolder(fileType);

        String uuidName = TytFileUtil.uuidFileName(fileName, keepOrigin);

        String fullPath = CommonUtil.joinPath(dateFolder, uuidName);

        return fullPath;
    }

}
