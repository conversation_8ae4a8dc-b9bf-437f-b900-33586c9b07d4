package com.tyt.plat.commons.config;

import com.alibaba.fastjson.support.retrofit.Retrofit2ConverterFactory;
import com.tyt.plat.client.autocall.OuterAutoCallClient;
import com.tyt.plat.client.distance.TencentLocationClient;
import com.tyt.plat.client.esign.ApiEsignSignClient;
import com.tyt.plat.client.invoice.InvoiceOpenApiClient;
import com.tyt.plat.client.ocr.GroupOcrClient;
import com.tyt.plat.client.push.ApiInnerPushClient;
import com.tyt.plat.client.trade.infofee.ApiTradeInfoFeeClient;
import com.tyt.plat.client.transport.ThPriceClient;
import com.tyt.plat.client.transport.TransportDirectPublishClient;
import com.tyt.plat.client.transport.TransportSearchClient;
import com.tyt.plat.client.transport.TransportTecserviceFeeClient;
import com.tyt.plat.client.user.*;
import com.tyt.plat.commons.internal.InternalClientInterceptor;
import com.tyt.plat.constant.PlatBeanConstant;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;

import java.time.Duration;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/4/24 9:38
 */
@Slf4j
@Configuration
public class InternalServiceConfig {

    public static final long READ_TIMEOUT = 15L;
    public static final long CONNECT_TIMEOUT = 3L;

    private final TytConfigService tytConfigService;

    public InternalServiceConfig(TytConfigService tytConfigService) {
        this.tytConfigService = tytConfigService;
    }

    /**
     * 调用内部服务的
     */
    @Bean(PlatBeanConstant.INTERNAL_RETROFIT)
    public Retrofit getInternalRetrofit() {
        String internalHost = tytConfigService.getStringValue("env_switch");
        if (StringUtils.isBlank(internalHost)) {
            throw TytException.createException(ResponseEnum.sys_error.info("env_switch must not be empty!!!"));
        }
        if (!internalHost.endsWith("/")) {
            internalHost = internalHost + "/";
        }

        log.info("internal_host : {}", internalHost);

        Interceptor interceptor = new InternalClientInterceptor();

        OkHttpClient httpClient = new OkHttpClient.Builder().readTimeout(Duration.ofSeconds(READ_TIMEOUT)).connectTimeout(Duration.ofSeconds(CONNECT_TIMEOUT)).addInterceptor(interceptor).build();

        Retrofit2ConverterFactory fastJsonConverter = new Retrofit2ConverterFactory();

        Retrofit retrofit = new Retrofit.Builder().baseUrl(internalHost).addConverterFactory(fastJsonConverter).client(httpClient).build();

        return retrofit;
    }

    @Bean
    public ApiUserCarryPointClient createApiUserCarryPointClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        ApiUserCarryPointClient apiUserCarryPointClient = retrofit.create(ApiUserCarryPointClient.class);
        return apiUserCarryPointClient;
    }

    @Bean
    public ApiEsignSignClient createApiEsignSignClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        ApiEsignSignClient apiEsignSignClient = retrofit.create(ApiEsignSignClient.class);
        return apiEsignSignClient;
    }

    @Bean
    public TencentLocationClient createTencentLocationClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        TencentLocationClient tencentLocationClient = retrofit.create(TencentLocationClient.class);
        return tencentLocationClient;
    }

    @Bean
    public ApiInvoiceEnterpriseClient createApiInvoiceEnterpriseClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        ApiInvoiceEnterpriseClient apiEsignSignClient = retrofit.create(ApiInvoiceEnterpriseClient.class);
        return apiEsignSignClient;
    }

    @Bean
    public GroupOcrClient createGroupOcrClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(GroupOcrClient.class);
    }

    @Bean
    public ApiUserInvoiceClient createApiUserInvoiceClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(ApiUserInvoiceClient.class);
    }

    @Bean
    public InvoiceOpenApiClient createInvoiceOpenApiClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(InvoiceOpenApiClient.class);
    }

    @Bean
    public ApiTradeInfoFeeClient createApiTradeInfoFeeClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(ApiTradeInfoFeeClient.class);
    }

    @Bean
    public TransportTecserviceFeeClient createTransportTecserviceFeeClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(TransportTecserviceFeeClient.class);
    }

    @Bean
    public TransportDirectPublishClient createTransportDirectPublishClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(TransportDirectPublishClient.class);
    }

    @Bean
    public OuterAutoCallClient createOuterAutoCallClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(OuterAutoCallClient.class);
    }

    @Bean
    public ThPriceClient createThPriceClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(ThPriceClient.class);
    }

    @Bean
    public ApiUserPermissionClient createApiUserPermissionClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(ApiUserPermissionClient.class);
    }


    @Bean
    public ApiTraceLocationClient createApiTraceLocationClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(ApiTraceLocationClient.class);
    }

    @Bean
    public ApiInnerPushClient createApiInnerPushClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(ApiInnerPushClient.class);
    }


    @Bean
    public ApiInvoiceCapacityClient createApiInvoiceCapacityClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(ApiInvoiceCapacityClient.class);
    }

    @Bean
    public TransportSearchClient createTransportSearchClient(@Qualifier(PlatBeanConstant.INTERNAL_RETROFIT) Retrofit retrofit) {
        return retrofit.create(TransportSearchClient.class);
    }
}
