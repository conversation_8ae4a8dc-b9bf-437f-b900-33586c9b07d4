package com.tyt.plat.commons.config;

import com.tyt.config.util.AppConfig;
import com.tyt.plat.commons.properties.AliOssProperty;
import com.tyt.plat.commons.properties.EsignConfigProperty;
import com.tyt.plat.commons.properties.SystemProperty;
import com.tyt.plat.commons.properties.UserCenterConfigProperty;
import com.tyt.plat.constant.PropertyKeyConstant;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.utils.SystemEnvironmentUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class WebConfig {

    @Bean
    public SystemProperty createSystemProperty() {

        String activeProfile = SystemEnvironmentUtil.getString(PropertyKeyConstant.ACTIVE_PROFILE);
        String projectRoot = PlatCommonUtil.getProjectRoot();
        String tmpFolder = PlatCommonUtil.getProjectTmpPath();

        SystemProperty systemProperty = new SystemProperty();

        systemProperty.setActiveProfile(activeProfile);
        systemProperty.setProjectRoot(projectRoot);
        systemProperty.setTmpFolder(tmpFolder);

        return systemProperty;
    }

    @Bean
    public EsignConfigProperty createEsignConfigProperty() {

        String endpoint = SystemEnvironmentUtil.getString(PropertyKeyConstant.EsignConfigKey.APP_ID);
        String accessKey = SystemEnvironmentUtil.getString(PropertyKeyConstant.EsignConfigKey.SECRET);

        EsignConfigProperty esignConfigProperty = new EsignConfigProperty();
        esignConfigProperty.setAppId(endpoint);
        esignConfigProperty.setSecret(accessKey);

        return esignConfigProperty;
    }





    /**
     * restTemplate
     *
     * @return
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(2000);// 设置连接超时，单位毫秒
        requestFactory.setReadTimeout(2000);  //设置读取超时
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(requestFactory);
        return restTemplate;
    }

    @Bean("threadPoolExecutor")
    public ThreadPoolExecutor threadPoolExecutor() {
        return new ThreadPoolExecutor(8, 20,
                30L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(1000), Executors.defaultThreadFactory());
    }

    @Bean
    public UserCenterConfigProperty getUserCenterConfigProperty(){

        String userCenterDomain = AppConfig.getProperty("user.center.api.url");
        String tpayMerchantIdText = AppConfig.getProperty("tpay.merchantId");
        String tpayVersionText = AppConfig.getProperty("tpay.version");

        UserCenterConfigProperty configProperty = new UserCenterConfigProperty();

        configProperty.setUserCenterDomain(userCenterDomain);
        configProperty.setTpayMerchantId(Long.parseLong(tpayMerchantIdText));
        configProperty.setTpayVersion(Integer.parseInt(tpayVersionText));

        return configProperty;
    }


}
