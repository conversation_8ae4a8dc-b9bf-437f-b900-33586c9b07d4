package com.tyt.plat.commons.config;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.tyt.plat.commons.properties.RocketMqConfigProperty;
import com.tyt.plat.constant.BeanConfigConstant;
import com.tyt.plat.constant.PropertyKeyConstant;
import com.tyt.plat.utils.SystemEnvironmentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Slf4j
@Configuration
public class RocketMqConfig {

    @Bean
    public RocketMqConfigProperty createRocketMqConfigProperty() {

        String accessKey = SystemEnvironmentUtil.getString(PropertyKeyConstant.MqConfigKey.ACCESS_KEY);
        String secretKey = SystemEnvironmentUtil.getString(PropertyKeyConstant.MqConfigKey.SECRET_KEY);
        String namesrvAddr = SystemEnvironmentUtil.getString(PropertyKeyConstant.MqConfigKey.NAMESRV_ADDR);

        String topicSuffix = SystemEnvironmentUtil.getString(PropertyKeyConstant.TOPIC_SUFFIX, false);

        if(topicSuffix == null) {
            String activeProfile = SystemEnvironmentUtil.getString(PropertyKeyConstant.ACTIVE_PROFILE);

            topicSuffix = "_" + activeProfile;
        }

        RocketMqConfigProperty mqConfigProperty = new RocketMqConfigProperty();

        mqConfigProperty.setAccessKey(accessKey);
        mqConfigProperty.setSecretKey(secretKey);
        mqConfigProperty.setNamesrvAddr(namesrvAddr);
        mqConfigProperty.setTopicSuffix(topicSuffix);

        return mqConfigProperty;
    }

    /**
     * base mq client.
     *
     * @return
     */
    @Bean(name = BeanConfigConstant.BUSINESS_PRODUCER, initMethod = "start", destroyMethod = "shutdown")
    public ProducerBean baseMqProducer() {

        RocketMqConfigProperty mqConfigProperty = this.createRocketMqConfigProperty();

        Properties mqProperties = mqConfigProperty.getMqProperties();

        ProducerBean producer = new ProducerBean();
        producer.setProperties(mqProperties);

        log.info("########## rocket_mq_config ########## : {}", JSON.toJSONString(mqConfigProperty));

        return producer;
    }

}
