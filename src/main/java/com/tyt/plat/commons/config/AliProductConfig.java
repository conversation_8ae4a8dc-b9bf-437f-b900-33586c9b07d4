package com.tyt.plat.commons.config;

import com.tyt.plat.commons.properties.AliOssProperty;
import com.tyt.plat.commons.properties.SystemProperty;
import com.tyt.plat.commons.tools.AliOssClient;
import com.tyt.plat.constant.PropertyKeyConstant;
import com.tyt.plat.utils.SystemEnvironmentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class AliProductConfig {

    @Bean
    public AliOssProperty createAliOssProperty() {

        String endpoint = SystemEnvironmentUtil.getString(PropertyKeyConstant.OssConfigKey.ENDPOINT);
        String accessKey = SystemEnvironmentUtil.getString(PropertyKeyConstant.OssConfigKey.ACCESS_KEY);
        String accessSecret = SystemEnvironmentUtil.getString(PropertyKeyConstant.OssConfigKey.ACCESS_SECRET);
        String bucketName = SystemEnvironmentUtil.getString(PropertyKeyConstant.OssConfigKey.BUCKET_NAME);
        String domain = SystemEnvironmentUtil.getString(PropertyKeyConstant.OssConfigKey.DOMAIN);

        AliOssProperty aliOssProperty = new AliOssProperty();

        aliOssProperty.setEndpoint(endpoint);
        aliOssProperty.setAccessKeyId(accessKey);
        aliOssProperty.setAccessKeySecret(accessSecret);
        aliOssProperty.setBucketName(bucketName);
        aliOssProperty.setDomain(domain);

        return aliOssProperty;
    }

    /**
     * ali oss client.
     *
     * @return AliOssClient
     */
    @Bean
    public AliOssClient getAliOssClient(AliOssProperty aliOssProperty, SystemProperty systemProperty) {

        AliOssClient aliOssClient = new AliOssClient(aliOssProperty, systemProperty.getActiveProfile());
        aliOssClient.initOss();

        return aliOssClient;
    }


}
