package com.tyt.plat.commons.model;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.enums.OrderByEnum;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 分页查询参数
 */
@Data
public class PageParameter {

    /**
     * 表列名正则
     */
    public static final String reg_column_name = "^[a-zA-Z0-9_-]+$";

    //当前页码
    private Integer pageNum = 1;

    //每页显示页数
    private Integer pageSize = 10;

    //分页开始的位置（尽量使用 pageNumber）
    private Integer start;

    //排序
    private String orderColumn;

    //0正序，1倒序
    private Integer sort;

    public PageParameter() {
    }

    public PageParameter(Integer pageNum, Integer pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public void setOrderBy(String orderColumn, OrderByEnum orderByEnum) {
        this.setOrderColumn(orderColumn);
        this.sort = orderByEnum.getCode();
    }

    public String getOrderColumn() {
        if(StringUtils.isNotBlank(orderColumn)) {
            if (!CommonUtil.regMatch(reg_column_name, orderColumn)) {
                throw TytException.createException(ResponseEnum.illegal_req.info());
            }
        }
        return orderColumn;
    }

    public void setOrderColumn(String orderColumn) {
        if(StringUtils.isNotBlank(orderColumn)) {
            if (!CommonUtil.regMatch(reg_column_name, orderColumn)) {
                throw TytException.createException(ResponseEnum.illegal_req.info());
            }
        }
        this.orderColumn = orderColumn;
    }

    /**
     * 拼接排序字段
     * @return
     */
    public String createOrderBySql(){
        String orderBySql = null;
        if(StringUtils.isNotBlank(orderColumn)){

            orderBySql = orderColumn;

            if(sort != null && sort == OrderByEnum.desc.getCode()){
                orderBySql = orderBySql + " desc ";
            }
        }
        return orderBySql;
    }

}



