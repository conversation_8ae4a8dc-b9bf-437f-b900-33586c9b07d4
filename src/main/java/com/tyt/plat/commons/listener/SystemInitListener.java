package com.tyt.plat.commons.listener;

import com.tyt.plat.thread.TransportDictionaryThread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/5/10 11:24
 */
@Slf4j
@Component
public class SystemInitListener implements ApplicationListener<ContextRefreshedEvent> {

    static volatile int count = 0;

    @Autowired
    private TransportDictionaryThread transportDictionaryThread;

    /**
     * 系统初始化完成执行
     */
    private void systemInit(){

        count++;

        if(count > 1){
            log.info("error_twice_init_run_error : " + count);
        }

        ScheduledExecutorService dicExecutor = Executors.newScheduledThreadPool(1);

        dicExecutor.scheduleAtFixedRate(transportDictionaryThread, 1, 30, TimeUnit.SECONDS);

        log.info("---- start_transport_dictionary_thread_done .... ");

    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {

        if(event.getApplicationContext().getParent() == null){

            log.info("==================== SystemInitListener_start ====================");

            this.systemInit();

            log.info("==================== SystemInitListener_done ====================");
        }

    }
}