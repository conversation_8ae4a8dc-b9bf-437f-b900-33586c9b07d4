package com.tyt.plat.commons.properties;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import lombok.Data;

import java.util.Properties;

@Data
public class RocketMqConfigProperty {

    private String accessKey;
    private String secretKey;
    private String namesrvAddr;

    //后缀，区分环境用，空字符串也是有意义的
    private String topicSuffix;

    /**
     * 初始化配置
     *
     * @return
     */
    public Properties getMqProperties() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, this.accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, this.secretKey);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.namesrvAddr);
        return properties;
    }

}
