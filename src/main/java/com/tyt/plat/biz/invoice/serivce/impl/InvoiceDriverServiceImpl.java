package com.tyt.plat.biz.invoice.serivce.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.CarDetailHead;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.User;
import com.tyt.plat.biz.invoice.constant.DriverCredentialVerifyStatus;
import com.tyt.plat.biz.invoice.constant.DriverVerifyFailEnums;
import com.tyt.plat.biz.invoice.constant.DriverVerifyStatus;
import com.tyt.plat.biz.invoice.db.InvoiceDbService;
import com.tyt.plat.biz.invoice.pojo.*;
import com.tyt.plat.biz.invoice.serivce.IInvoiceDriverService;
import com.tyt.plat.commons.model.CheckResult;
import com.tyt.plat.commons.model.PageParameter;
import com.tyt.plat.commons.tools.CustomPageHelper;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.plat.entity.base.TytInvoiceDriverCredential;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.plat.service.ocr.OcrService;
import com.tyt.plat.vo.ocr.*;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import io.vavr.API;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/24 下午2:29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceDriverServiceImpl implements IInvoiceDriverService {

    private final InvoiceDbService invoiceDbService;

    private final CommonApiService commonApiService;

    private final ThreadPoolTaskExecutor executor;

    private final TytUserIdentityAuthService tytUserIdentityAuthService;

    private final MessageCenterPushService messageCenterPushService;

    private final UserService userService;

    private final OcrService ocrService;

    private final static Integer ZERO = 0;
    private final static Integer ONE = 1;
    private final static Integer TWO = 2;
    private final static Integer THREE = 3;

    private final static String PHONE_REMARK = ",手机尾号";

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public Long addDriver(AddDriverDTO addDriverDTO, Long userId) throws Exception {
        log.info("用户:{} 添加司机:{}", userId, addDriverDTO);

        List<AddDriverCredentialDTO> credentialDTOS =
                JSON.parseArray(new String(Base64.getDecoder().decode(addDriverDTO.getCredentials())),
                        AddDriverCredentialDTO.class);

        // 强制5张图
        if (credentialDTOS == null || credentialDTOS.size() != 5) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "司机凭证参数异常"));
        }

        // 1 2 3 4 5 各一张
        Set<Integer> picTypes =
                credentialDTOS.stream().map(AddDriverCredentialDTO::getPicType).collect(Collectors.toSet());
        for (int i = 0; i < 5; i++) {
            if (!picTypes.remove(i + 1)) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "司机凭证参数异常"));
            }
        }

        long saveIdCardCount = invoiceDbService.countByUserIdAndIdCard(userId, addDriverDTO.getIdCard());
        if (saveIdCardCount > 0) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "不能重复添加司机"));
        }

        long phoneCount = invoiceDbService.countByUserIdAndPhone(userId, addDriverDTO.getPhone());
        if (phoneCount > 0) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "不能重复添加司机"));
        }

        //V6520校验手机号规则
        checkDriverPhone(userId, addDriverDTO.getName(), addDriverDTO.getIdCard(), addDriverDTO.getPhone());

        // driver
        Date now = new Date();

        TytInvoiceDriver tytInvoiceDriver = addDriverDTO.toDO();
        try {
            if (StringUtils.isNotBlank(addDriverDTO.getInitialIssueDate())) {
                tytInvoiceDriver.setQcCardSendTime(new Date(Long.parseLong(addDriverDTO.getInitialIssueDate())));
            }
        } catch (Exception e) {
            log.error("initial issue date string to date error:", e);
        }
        tytInvoiceDriver.setVerifyStatus(DriverVerifyStatus.VERIFYING.getState());
        tytInvoiceDriver.setUserId(userId);
        tytInvoiceDriver.setCreateTime(now);
        tytInvoiceDriver.setUpdateTime(now);

        List<TytInvoiceDriverCredential> credentials = credentialDTOS.stream()
                .map(AddDriverCredentialDTO::toDO)
                .collect(Collectors.toList());

        // ocr识别
        CountDownLatch countDownLatch = new CountDownLatch(credentials.size());
        for (TytInvoiceDriverCredential credential : credentials) {
            executor.execute(() -> {
                try {
                    // 不会影响相同的字段，该用多线程
                    fillInvoiceDriverWithOcr(tytInvoiceDriver, credential);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        countDownLatch.await(10, TimeUnit.SECONDS);


        User user = getUserOrCreate(addDriverDTO.getPhone());
        tytInvoiceDriver.setDriverUserId(user.getId());
        tytInvoiceDriver.setDeleteFlag(0);

        //V6520 自动审核校验司机信息
        Map<Integer, DriverVerifyFailEnums> failEnumMap = autoAuditDriver(tytInvoiceDriver);
        log.info("司机认证 自动审核结果 failEnums：【{}】， tytInvoiceDriver：【{}】", JSON.toJSONString(failEnumMap), JSON.toJSONString(tytInvoiceDriver));

        Long driverId = invoiceDbService.saveInvoiceDriver(tytInvoiceDriver);

        credentials = credentials.stream()
                .map(it -> createDriverCredential(it, driverId, now, tytInvoiceDriver.getVerifyStatus(), failEnumMap))
                .collect(Collectors.toList());

        invoiceDbService.saveInvoiceDriverCredentials(credentials);
        return driverId;
    }

    private Map<Integer, DriverVerifyFailEnums> autoAuditDriver(TytInvoiceDriver tytInvoiceDriver) {
        Map<Integer, DriverVerifyFailEnums> failEnumMap = new HashMap<>();
        try {
            log.info("auto audit driver info:【{}】", JSON.toJSONString(tytInvoiceDriver));
            boolean flag = true;
            Date now = new Date();
            if (StringUtils.isBlank(tytInvoiceDriver.getName())
                    || StringUtils.isBlank(tytInvoiceDriver.getQcName())
                    || StringUtils.isBlank(tytInvoiceDriver.getDlName())
                    || !StringUtils.equals(tytInvoiceDriver.getName(), tytInvoiceDriver.getQcName())
                    || !StringUtils.equals(tytInvoiceDriver.getName(), tytInvoiceDriver.getDlName())) {
                //三证姓名不一致
                flag = false;
            } else if (StringUtils.isBlank(tytInvoiceDriver.getIdCard())
                            || StringUtils.isBlank(tytInvoiceDriver.getDlCard())
                            || !StringUtils.equals(tytInvoiceDriver.getIdCard(), tytInvoiceDriver.getDlCard())) {
                //驾驶证号码和身份证号码不一致
                flag = false;
            } else if (StringUtils.isBlank(tytInvoiceDriver.getIdCard())
                            || StringUtils.isBlank(tytInvoiceDriver.getName())
                            || ObjectUtil.isNull(tytInvoiceDriver.getIdCardGender())
                            || StringUtils.isBlank(tytInvoiceDriver.getPhone())
                            || StringUtils.isBlank(tytInvoiceDriver.getIdCardNation())
                            || ObjectUtil.isNull(tytInvoiceDriver.getIdCardBirthday())
                            || ObjectUtil.isNull(tytInvoiceDriver.getIdCardEffectiveBeginTime())
                            || (!tytInvoiceDriver.getIdCardPermanent() && ObjectUtil.isNull(tytInvoiceDriver.getIdCardExpirationTime()))
                            || StringUtils.isBlank(tytInvoiceDriver.getIdCardAddress())) {
                //身份证姓名、身份证号、性别、手机号、身份证有效期起止、民族、生日、住址有为空
                flag = false;
            } else if (StringUtils.isBlank(tytInvoiceDriver.getDlName())
                            || StringUtils.isBlank(tytInvoiceDriver.getDlCard())
                            || StringUtils.isBlank(tytInvoiceDriver.getDlCardVehicleType())
                            || StringUtils.isBlank(tytInvoiceDriver.getDlCardAuthGov())
                            || ObjectUtil.isNull(tytInvoiceDriver.getDlCardIssueTime())
                            || ObjectUtil.isNull(tytInvoiceDriver.getDlCardEffectiveBeginTime())
                            || (!tytInvoiceDriver.getDlCardPermanent() && ObjectUtil.isNull(tytInvoiceDriver.getDlCardExpirationTime()))) {
                //驾驶证姓名、驾驶证号码、准驾车型、驾驶证发证机关、初次领证日期、驾驶证有效期起止时间有为空
                flag = false;
            } else if (StringUtils.isBlank(tytInvoiceDriver.getQcName())
                            || StringUtils.isBlank(tytInvoiceDriver.getQcCard())
                            || ObjectUtil.isNull(tytInvoiceDriver.getQcCardExpirationTime())) {
                //从业资格证姓名、从业资格证号、从业资格证有效期有为空
                flag = false;
            } else if (!checkNameHaveChinese(tytInvoiceDriver)) {
                flag = false;
            } else if (!checkIdCardNumber(tytInvoiceDriver)) {
                flag = false;
            } else if (!checkPhoneNumber(tytInvoiceDriver)) {
                flag = false;
            }

             if (ObjectUtil.isNotNull(tytInvoiceDriver.getIdCardExpirationTime())
                     && now.after(tytInvoiceDriver.getIdCardExpirationTime())) {
                 failEnumMap.put(2, DriverVerifyFailEnums.ID_CARD_EXPIRATION_TIME_PAST);
             }
            if (ObjectUtil.isNotNull(tytInvoiceDriver.getDlCardExpirationTime())
                    && now.after(tytInvoiceDriver.getDlCardExpirationTime())) {
                failEnumMap.put(3, DriverVerifyFailEnums.DL_CARD_EXPIRATION_TIME_PAST);
                failEnumMap.put(4, DriverVerifyFailEnums.DL_CARD_EXPIRATION_TIME_PAST);
            }
            if (ObjectUtil.isNotNull(tytInvoiceDriver.getQcCardExpirationTime())
                    && now.after(tytInvoiceDriver.getQcCardExpirationTime())) {
                failEnumMap.put(5, DriverVerifyFailEnums.QC_CARD_EXPIRATION_TIME_PAST);
            }
            if (ObjectUtil.isNotNull(tytInvoiceDriver.getIdCardBirthday())) {
                Date maxTime = TimeUtil.dateAddYear(tytInvoiceDriver.getIdCardBirthday(), 60);
                Date minTime = TimeUtil.dateAddYear(tytInvoiceDriver.getIdCardBirthday(), 18);
                if (now.before(minTime) || now.after(maxTime)) {
                    failEnumMap.put(1, DriverVerifyFailEnums.ID_CARD_AGE);
                }
            }

            //校验
            if (flag && failEnumMap.isEmpty()) {
                tytInvoiceDriver.setVerifyStatus(DriverVerifyStatus.VERIFY_SUCCESS.getState());
                tytInvoiceDriver.setVerifyUserId(0);
                tytInvoiceDriver.setVerifyUserName("系统");
            } else if (!failEnumMap.isEmpty()) {
                tytInvoiceDriver.setVerifyStatus(DriverVerifyStatus.VERIFY_FAILED.getState());
                tytInvoiceDriver.setVerifyUserId(0);
                tytInvoiceDriver.setVerifyUserName("系统");
            }
            return failEnumMap;
        } catch (Exception e) {
            log.error("auto audit driver info error:", e);
            return failEnumMap;
        }
    }

    /**
     * 校验手机号是否为11位数字
     */
    private boolean checkPhoneNumber(TytInvoiceDriver tytInvoiceDriver) {
        String phone = tytInvoiceDriver.getPhone();
        Pattern pattern = Pattern.compile("^\\d{11}$");
        Matcher matcherName = pattern.matcher(phone);
        return matcherName.find();
    }

    /**
     * 校验身份证号是否为15位或18位数字
     */
    private boolean checkIdCardNumber(TytInvoiceDriver tytInvoiceDriver) {
        String idCard = tytInvoiceDriver.getIdCard();
        Pattern pattern = Pattern.compile("^\\d{15}$|^\\d{18}$");
        Matcher matcherName = pattern.matcher(idCard);
        return matcherName.find();
    }

    /**
     * 校验三证姓名为两个以上汉字
     */
    private boolean checkNameHaveChinese(TytInvoiceDriver tytInvoiceDriver) {
        String driverName = tytInvoiceDriver.getName();
        String qcName = tytInvoiceDriver.getQcName();
        String dlName = tytInvoiceDriver.getDlName();
        Pattern pattern = Pattern.compile("^[\u4e00-\u9fa5]{2}.*");
        Matcher matcherName = pattern.matcher(driverName);
        Matcher matcherQcName = pattern.matcher(qcName);
        Matcher matcherDlName = pattern.matcher(dlName);
        return matcherName.find() && matcherQcName.find() && matcherDlName.find();
    }

    private User getUserOrCreate(String phone)throws Exception {
        User user = userService.getUserByCellphone(phone);
        if (user == null) {
            user = new User();
            user.setCellPhone(phone);
            user.setIsMock(1);
            user.setInfoUploadFlag(1);
            user.setInfoPublishFlag(2);
            //BUG修复，马甲用户默认先初始化必要字段，以解决身份限制发货的缓存更新空指针问题
            user.setUserSign(User.UserSignEnum.司机.code);
            user.setVerifyPhotoSign(0);
            user.setVerifyFlag(0);
            user.setCtime(new Timestamp(System.currentTimeMillis()));
            user.setMtime(new Timestamp(System.currentTimeMillis()));
            user = userService.addUser(user);
        }
        return user;
    }



    /**
     * ocr填充司机信息
     *
     * @param tytInvoiceDriver tytInvoiceDriver
     * @param driverCredential 图片凭证
     */
    private boolean fillInvoiceDriverWithOcr(TytInvoiceDriver tytInvoiceDriver,
                                          TytInvoiceDriverCredential driverCredential) {
        boolean isUpdate = false;
        String picUrl = driverCredential.getPicUrl();
        //图片类型(1 身份证正面、2 身份证反面、3 驾驶证正面、4 驾驶证反面、 5 从业资格证)
        Integer picType = driverCredential.getPicType();

        if (StringUtils.isBlank(picUrl)) {
            return isUpdate;
        }

        switch (picType) {
            case 1:
                OcrIdCardFrontVo ocrIdCardFrontVo = commonApiService.idCardFrontOcr(picUrl);
                if (ocrIdCardFrontVo != null) {

                    String gender = ocrIdCardFrontVo.getGender();
                    tytInvoiceDriver.setIdCardGender(StringUtils.isBlank(gender) ? 0 : "男".equals(gender) ? 1 : 2);
                    if (StringUtils.isNotBlank(ocrIdCardFrontVo.getIdNumber())) {
                        tytInvoiceDriver.setIdCard(ocrIdCardFrontVo.getIdNumber());
                    }
                    if (StringUtils.isNotBlank(ocrIdCardFrontVo.getNation())) {
                        tytInvoiceDriver.setIdCardNation(ocrIdCardFrontVo.getNation());
                    }
                    if (Objects.nonNull(ocrIdCardFrontVo.getBirthday())){
                        if (TimeUtil.checkYear(ocrIdCardFrontVo.getBirthday())){
                            tytInvoiceDriver.setIdCardBirthday(ocrIdCardFrontVo.getBirthday());
                        }
                    }
                    if (StringUtils.isNotBlank(ocrIdCardFrontVo.getLiveAddress())) {
                        tytInvoiceDriver.setIdCardAddress(ocrIdCardFrontVo.getLiveAddress());
                    }

                    driverCredential.setOrcJson(ocrIdCardFrontVo.getOcrJsonText());
                    isUpdate = true;
                }
                break;
            case 2:
                OcrIdCardBackVo idCardBackVo = commonApiService.idCardBackOcr(picUrl);
                if (idCardBackVo != null) {
                    if (Objects.nonNull(idCardBackVo.getSignDate())){
                        if (TimeUtil.checkYear(idCardBackVo.getSignDate())){
                            tytInvoiceDriver.setIdCardEffectiveBeginTime(idCardBackVo.getSignDate());
                        }
                    }
                    if (StringUtils.isNotBlank(idCardBackVo.getSignGov())) {
                        tytInvoiceDriver.setIdCardSignGov(idCardBackVo.getSignGov());
                    }
                    driverCredential.setOrcJson(idCardBackVo.getOcrJsonText());
                    isUpdate = true;
                }

                break;
            case 5:
                RoadTransportQuaCertOcrVo roadTransportQuaCertOcrVo = ocrService.roadTransportQualificationCertificateBackOcr(picUrl);
                if (roadTransportQuaCertOcrVo != null) {
                    if (StringUtils.isNotBlank(roadTransportQuaCertOcrVo.getName())) {
                        tytInvoiceDriver.setQcName(roadTransportQuaCertOcrVo.getName());
                    }
                    if (StringUtils.isNotBlank(roadTransportQuaCertOcrVo.getDrivingClass())) {
                        tytInvoiceDriver.setQcCardVehicleType(roadTransportQuaCertOcrVo.getDrivingClass());
                    }
                    if (StringUtils.isNotBlank(roadTransportQuaCertOcrVo.getCategory())) {
                        tytInvoiceDriver.setQcCardType(roadTransportQuaCertOcrVo.getCategory());
                    }
                    if (ObjectUtil.isNotNull(roadTransportQuaCertOcrVo.getInitialIssueDate())) {
                        tytInvoiceDriver.setQcCardSendTime(roadTransportQuaCertOcrVo.getInitialIssueDate());
                    }
                    driverCredential.setOrcJson(JSON.toJSONString(roadTransportQuaCertOcrVo));
                    isUpdate = true;
                }
                break;
            case 3:
                DriverLicenseFrontVo driverLicenseFrontVo = commonApiService.driverLicenseFrontOcr(picUrl);
                if (driverLicenseFrontVo != null) {
                    if (StringUtils.isNotBlank(driverLicenseFrontVo.getName())) {
                        tytInvoiceDriver.setDlName(driverLicenseFrontVo.getName());
                    }
                    if (StringUtils.isNotBlank(driverLicenseFrontVo.getDriverNumber())) {
                        tytInvoiceDriver.setDlCard(driverLicenseFrontVo.getDriverNumber());
                    }
                    if (StringUtils.isNotBlank(driverLicenseFrontVo.getLicenseType())) {
                        tytInvoiceDriver.setDlCardVehicleType(driverLicenseFrontVo.getLicenseType());
                    }
                    if (StringUtils.isNotBlank(driverLicenseFrontVo.getIssueUnit())) {
                        tytInvoiceDriver.setDlCardAuthGov(driverLicenseFrontVo.getIssueUnit());
                    }

                    driverCredential.setOrcJson(driverLicenseFrontVo.getOcrJsonText());
                    if (Objects.nonNull(driverLicenseFrontVo.getFirstRegisterDate())){
                        if (TimeUtil.checkYear(driverLicenseFrontVo.getFirstRegisterDate())){
                            tytInvoiceDriver.setDlCardIssueTime(driverLicenseFrontVo.getFirstRegisterDate());
                        }
                    }
                    if (Objects.nonNull(driverLicenseFrontVo.getValidStartDate())){
                        if (TimeUtil.checkYear(driverLicenseFrontVo.getValidStartDate())){
                            tytInvoiceDriver.setDlCardEffectiveBeginTime(driverLicenseFrontVo.getValidStartDate());
                        }
                    }
                    tytInvoiceDriver.setDlCardPermanent(false);
                    if (StringUtils.isNotBlank(driverLicenseFrontVo.getValidEndDateText()) && "长期".equals(driverLicenseFrontVo.getValidEndDateText())){
                        tytInvoiceDriver.setDlCardPermanent(true);
                        tytInvoiceDriver.setDlCardExpirationTime(null);
                    }else {
                        if (Objects.nonNull(driverLicenseFrontVo.getValidEndDate())){
                            if (TimeUtil.checkYear(driverLicenseFrontVo.getValidEndDate())){
                                tytInvoiceDriver.setDlCardExpirationTime(driverLicenseFrontVo.getValidEndDate());
                            }
                        }
                    }
                    isUpdate = true;
                }
                break;
            case 4:
                DriverLicenseBackVo driverLicenseBackVo = commonApiService.driverLicenseBackOcr(picUrl);
                if (driverLicenseBackVo != null) {
                    driverCredential.setOrcJson(driverLicenseBackVo.getOcrJsonText());
                    isUpdate = true;
                }
                break;
            default:
                break;
        }
        return isUpdate;
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public Long syncOldDriver(Long userId, String name, String phone) {
        if (StringUtils.isBlank(name) && StringUtils.isBlank(phone)) {
            return null;
        }
        Date now = new Date();

        TytInvoiceDriver invoiceDriver = TytInvoiceDriver.builder()
                .userId(userId)
                .name(name)
                .verifyStatus(3)
                .updateTime(now)
                .phone(phone).build();
        Long invoiceDriverId = invoiceDbService.saveInvoiceDriver(invoiceDriver);

        List<TytInvoiceDriverCredential> credentials = Lists.newArrayList();
        // 图片凭证
        for (int i = 0; i < 5; i++) {
            TytInvoiceDriverCredential driverCredential = new TytInvoiceDriverCredential();
            driverCredential.setPicType(i + 1);
            driverCredential.setPicUrl("");
            credentials.add(createDriverCredential(driverCredential, invoiceDriver.getId(), now, invoiceDriver.getVerifyStatus(), null));
        }
        invoiceDbService.saveInvoiceDriverCredentials(credentials);

        return invoiceDriverId;
    }

    @Override
    public List<DriverListVO> getDriverList(Long userId, Integer page, Integer pageSize, Integer verifyStatus) {
        CustomPageHelper.startPage(new PageParameter(page, pageSize));

        List<TytInvoiceDriver> drivers = invoiceDbService.listByUserIdAndVerifyStatus(userId, verifyStatus);

        PageMethod.clearPage();
        return drivers.stream().map(it -> {
            DriverListVO driverListVO = DriverListVO.fromDO(it);
            CheckResult<String> checkResult = checkCanEdit(it);
            driverListVO.setCanEdit(checkResult.isSuccess());
            driverListVO.setExpireHint(checkResult.getMessage());
            return driverListVO;
        }).collect(Collectors.toList());
    }

    /**
     * 检查是否能进行修改司机操作
     *
     * @param invoiceDriver invoiceDriver
     * @return CheckResult.success == true 可以修改，否则不能修改
     */
    public CheckResult<String> checkCanEdit(TytInvoiceDriver invoiceDriver) {

        CheckResult<String> checkResult = new CheckResult<>();

        if (invoiceDriver == null) {
            checkResult.setSuccess(false);
            return checkResult;
        }

        StringJoiner sj = new StringJoiner(System.lineSeparator());

        Date deadLine = DateUtils.addDays(new Date(), 30);

        if (Boolean.FALSE.equals(invoiceDriver.getIdCardPermanent())
                && invoiceDriver.getIdCardExpirationTime() != null
                && deadLine.after(invoiceDriver.getIdCardExpirationTime())) {
            sj.add(String.format("逾期提醒：您的身份证于%s到期，请及时更换",
                    DateUtil.format(invoiceDriver.getIdCardExpirationTime(), "yyyy年MM月dd日")));
        }

        if (invoiceDriver.getQcCardExpirationTime() != null
                && deadLine.after(invoiceDriver.getQcCardExpirationTime())) {
            sj.add(String.format("逾期提醒：您的从业资格证于%s到期，请及时更换",
                    DateUtil.format(invoiceDriver.getQcCardExpirationTime(), "yyyy年MM月dd日")));
        }

        if (Boolean.FALSE.equals(invoiceDriver.getDlCardPermanent())
                && invoiceDriver.getDlCardExpirationTime() != null
                && deadLine.after(invoiceDriver.getDlCardExpirationTime())) {
            sj.add(String.format("逾期提醒：您的驾驶证于%s到期，请及时更换",
                    DateUtil.format(invoiceDriver.getDlCardExpirationTime(), "yyyy年MM月dd日")));
        }

        //V6520 放开编辑校验
        checkResult.setSuccess(true);
        checkResult.setMessage(sj.toString());
        return checkResult;
    }


    @Override
    public DriverDetailVO getDriverDetail(Long id) {

        TytInvoiceDriver tytInvoiceDriver = invoiceDbService.getById(id);
        if (tytInvoiceDriver == null) {
            return null;
        }

        DriverDetailVO driverDetailVO = DriverDetailVO.fromDO(tytInvoiceDriver);

        List<DriverDetailCredentialDTO> credentials =
                invoiceDbService.getCredentialsByDriverId(id).stream()
                        .map(it -> DriverDetailCredentialDTO.fromDO(it).clearInfoIfVerifySuccess())
                        .collect(Collectors.toList());
        driverDetailVO.setCredentials(credentials);
        return driverDetailVO;
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public Long updateDriver(Long userId, UpdateDriverDTO updateDriverDTO) throws Exception {
        log.info("用户:{} 修改司机:{}", userId, updateDriverDTO);
        TytInvoiceDriver invoiceDriver = invoiceDbService.getById(updateDriverDTO.getId());

        CheckResult<String> checkResult = checkCanEdit(invoiceDriver);
        if (!checkResult.isSuccess()) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "不满足修改条件，无法修改"));
        }

        if (!Objects.equals(invoiceDriver.getUserId(), userId)) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "不满足修改条件，无法修改"));
        }

        //V6520校验手机号规则
        checkDriverPhone(userId, updateDriverDTO.getName(), updateDriverDTO.getIdCard(), updateDriverDTO.getPhone());

        updateDriverDTO.toDO(invoiceDriver);

        try {
            if (StringUtils.isNotBlank(updateDriverDTO.getInitialIssueDate())) {
                invoiceDriver.setQcCardSendTime(new Date(Long.parseLong(updateDriverDTO.getInitialIssueDate())));
            }
        } catch (Exception e) {
            log.error("initial issue date string to date error:", e);
        }

        Date now = new Date();

        invoiceDriver.setVerifyStatus(0);
        invoiceDriver.setUpdateTime(now);

        List<UpdateDriverCredentialDTO> credentials =
                JSON.parseArray(new String(Base64.getDecoder().decode(updateDriverDTO.getCredentials())),
                        UpdateDriverCredentialDTO.class);

        long saveIdCardCount = invoiceDbService.countByUserIdIdCard(userId, invoiceDriver.getIdCard(),invoiceDriver.getId());
        if (saveIdCardCount > 0) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "不能重复添加司机,如有需要请在司机列表进行修改"));
        }

        long phoneCount = invoiceDbService.countByUserIdPhone(userId, invoiceDriver.getPhone(),invoiceDriver.getId());
        if (phoneCount > 0) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "不能重复添加司机,如有需要请在司机列表进行修改"));
        }

        //查询当前司机上传的证件信息
        List<TytInvoiceDriverCredential> driverCredentialList = invoiceDbService.getCredentialsByDriverId(invoiceDriver.getId());

        if (credentials != null && !credentials.isEmpty()) {
            Map<Long, String> updateMap = credentials.stream()
                    .collect(Collectors.toMap(UpdateDriverCredentialDTO::getId, UpdateDriverCredentialDTO::getPicUrl));

            for (TytInvoiceDriverCredential credential : driverCredentialList) {
                String updatePicUrl = updateMap.get(credential.getId());
                if (StringUtils.isNotBlank(updatePicUrl)) {
                    credential.setPicUrl(updatePicUrl);
                    credential.setUpdateTime(new Date());
                    credential.setVerifyStatus(DriverCredentialVerifyStatus.VERIFYING.getState());
                }
            }

            // ocr识别
            CountDownLatch countDownLatch = new CountDownLatch(credentials.size());
            for (TytInvoiceDriverCredential credential : driverCredentialList) {
                executor.execute(() -> {
                    try {
                        // 不会影响相同的字段，该用多线程
                        fillInvoiceDriverWithOcr(invoiceDriver, credential);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }

            countDownLatch.await(5, TimeUnit.SECONDS);
        }

        //V6520 自动审核校验司机信息
        Map<Integer, DriverVerifyFailEnums> failEnumMap = autoAuditDriver(invoiceDriver);
        log.info("司机认证 自动审核结果 failEnums：【{}】， tytInvoiceDriver：【{}】", JSON.toJSONString(failEnumMap), JSON.toJSONString(invoiceDriver));

        User user = getUserOrCreate(invoiceDriver.getPhone());
        invoiceDriver.setDriverUserId(user.getId());

        invoiceDbService.updateInvoiceDriver(invoiceDriver);

        //根据自动审核结果设置对应证件审核状态
        driverCredentialList.stream().forEach(it -> updateDriverCredentialStatus(it, now, invoiceDriver.getVerifyStatus(), failEnumMap));

        return invoiceDriver.getId();
    }

    private void updateDriverCredentialStatus(TytInvoiceDriverCredential driverCredential, Date now,
                                              Integer verifyStatus, Map<Integer, DriverVerifyFailEnums> failEnumMap) {
        if (!failEnumMap.isEmpty()) {
            if (failEnumMap.containsKey(driverCredential.getPicType())) {
                DriverVerifyFailEnums failEnums = failEnumMap.get(driverCredential.getPicType());
                driverCredential.setVerifyStatus(DriverCredentialVerifyStatus.VERIFY_FAILED.getState());
                driverCredential.setVerifyDesc(failEnums.getFailReason());
            } else {
                driverCredential.setVerifyStatus(DriverCredentialVerifyStatus.VERIFY_SUCCESS.getState());
                driverCredential.setVerifyDesc("");
            }
        } else {
            driverCredential.setVerifyStatus(DriverVerifyStatus.VERIFY_SUCCESS.getState() == verifyStatus ?
                    DriverCredentialVerifyStatus.VERIFY_SUCCESS.getState() : DriverCredentialVerifyStatus.VERIFYING.getState());
            driverCredential.setVerifyDesc("");
        }
        driverCredential.setUpdateTime(now);
        invoiceDbService.updateByPrimaryKeySelective(driverCredential);
    }

    private void checkDriverPhone(Long userId, String name, String idCard, String phone) throws Exception{
        idCard = formatIdCard(idCard);
        User user = userService.getByUserId(userId);
        String userIdCard = formatIdCard(user.getIdCard());
        if (((ObjectUtil.isNotNull(user.getTrueName()) && !user.getTrueName().equals(name))
                || (ObjectUtil.isNotNull(userIdCard) && !userIdCard.equals(idCard)))
                && (ObjectUtil.isNotNull(user.getCellPhone()) && user.getCellPhone().equals(phone))) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "因添加不是本人，手机号不能与账号相同，请更换！"));
        }
    }

    /**
     * 兼容身份证末位
     */
    private String formatIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return idCard;
        }
        if (idCard.endsWith("x")) {
            return idCard.replaceAll("x", "X");
        }
        return idCard;
    }

    @Override
    public Long updateDriverInner4Car(Long id, String name, String phone) {
        TytInvoiceDriver driver = invoiceDbService.getById(id);
        if (driver == null) {
            return null;
        }

        int verifyStatus;
        if (Objects.equals(DriverVerifyStatus.UN_VERIFY.getState(), driver.getVerifyStatus())) {
            verifyStatus = DriverVerifyStatus.UN_VERIFY.getState();
        } else {
            verifyStatus = DriverVerifyStatus.VERIFYING.getState();
        }

        TytInvoiceDriver update = TytInvoiceDriver.builder().id(id)
                .name(name)
                .phone(phone)
                .updateTime(new Date())
                .verifyStatus(verifyStatus).build();
        invoiceDbService.updateInvoiceDriver(update);
        return id;
    }

    private static TytInvoiceDriverCredential createDriverCredential(TytInvoiceDriverCredential driverCredential,
                                                                     Long driverId, Date now, Integer verifyStatus,
                                                                     Map<Integer, DriverVerifyFailEnums> failEnumMap) {
        if (!failEnumMap.isEmpty()) {
            if (failEnumMap.containsKey(driverCredential.getPicType())) {
                DriverVerifyFailEnums failEnums = failEnumMap.get(driverCredential.getPicType());
                driverCredential.setVerifyStatus(DriverCredentialVerifyStatus.VERIFY_FAILED.getState());
                driverCredential.setVerifyDesc(failEnums.getFailReason());
            } else {
                driverCredential.setVerifyStatus(DriverCredentialVerifyStatus.VERIFY_SUCCESS.getState());
                driverCredential.setVerifyDesc("");
            }
        } else {
            driverCredential.setVerifyStatus(DriverVerifyStatus.VERIFY_SUCCESS.getState() == verifyStatus ?
                    DriverCredentialVerifyStatus.VERIFY_SUCCESS.getState() : DriverCredentialVerifyStatus.VERIFYING.getState());
            driverCredential.setVerifyDesc("");
        }
        driverCredential.setInvoiceDriverId(driverId);
        driverCredential.setUpdateTime(now);
        if (driverCredential.getCreateTime() == null) {
            driverCredential.setCreateTime(now);
        }

        if (driverCredential.getOrcJson() == null) {
            driverCredential.setOrcJson("");
        }

        return driverCredential;
    }

    @Override
    public DriverCountVO getDriverCount(Long userId) {
        DriverCountVO driverCountVO = new DriverCountVO();
        driverCountVO.setCount(invoiceDbService.countVerifiedDriver(userId));
        return driverCountVO;
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager", rollbackFor = Exception.class)
    public void sendInvoice(Integer type, Long id) {
        //判断司机是否存在
        TytInvoiceDriver driver = invoiceDbService.getById(id);
        if (driver == null) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "司机不存在"));
        }
        if (ONE.equals(type)) {
            //判断是否有实名认证，如有判断是否本人
            TytUserIdentityAuth auth = tytUserIdentityAuthService.getByUserId(driver.getUserId().toString());
            if (null != auth) {
                if (auth.getIdCard().equals(driver.getIdCard())) {
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "本人无需授权"));
                }
            } else if (TWO.equals(driver.getSelf())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "本人无需授权"));
            }
            //司机未认证成功，无法授权
            if (!ONE.equals(driver.getVerifyStatus())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "未认证成功，无法请求授权"));
            }
            if (ONE.equals(driver.getEmpower())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "授权中，无法请求授权"));
            }
            if (TWO.equals(driver.getEmpower())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "已授权成功，无法请求授权"));
            }
            updateSend(driver, auth, type);
        } else if (TWO.equals(type)) {
            deterDriverTwoType(driver.getEmpower());
            updateSend(driver, null, type);
        } else if (THREE.equals(type)) {
            deterDriverThreeType(driver.getEmpower());
            updateSend(driver, null, type);
        }
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager", rollbackFor = Exception.class)
    public void sendInvoiceNew(Integer type, Long id){
        //判断司机是否存在
        TytInvoiceDriver driver = invoiceDbService.getById(id);
        if (driver == null) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "司机不存在"));
        }
        if (TWO.equals(type)) {

            updateSend(driver, null, type);
        } else if (THREE.equals(type)) {

            updateSend(driver, null, type);
        }
    }

    @Override
    public void updateDelStatusByCellPhone(String cellPhone) {
        invoiceDbService.updateDelStatusByCellPhone(cellPhone);
    }

    public void deterDriverTwoType(Integer empower){
        if (!ONE.equals(empower)) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "未提交授权，不允许直接通过"));
        }
    }

    public void deterDriverThreeType(Integer empower){
        if (!ONE.equals(empower)) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "未提交授权，不允许直接拒绝"));
        }
    }


    public void updateSend(TytInvoiceDriver driver, TytUserIdentityAuth auth, Integer type) {
        try {
            //司机的手机号
            String mobile = driver.getPhone().substring(driver.getPhone().length() - 4);
            String remark = "司机授权";
            String content = "";
            User user = userService.getByUserId(driver.getUserId());
            String cellPhone = "";
            if (ONE.equals(type)) {
                driver.setEmpower(1);
                //车队长的手机号
                String phone = user.getCellPhone().substring(user.getCellPhone().length() - 4);
                if (null != auth) {
                    content = "您好,车队长" + auth.getTrueName() + PHONE_REMARK + phone + "账户,请求添加您为其车队的司机,可为您承接运单,可点击以下链接进入app进行授权操作https://www.teyuntong.net/call.html?t=c";
                } else {
                    content = "您好,手机尾号" + phone + "账户,请求添加您为其车队的司机,可为您承接运单,可点击以下链接进入app进行授权操作https://www.teyuntong.net/call.html?t=c";
                }
                cellPhone = driver.getPhone();
            } else if (TWO.equals(type)) {
                driver.setEmpower(2);
                content = "您好,司机" + driver.getName() + PHONE_REMARK + mobile + ",同意授权成为您车队的司机!";
                cellPhone = user.getCellPhone();
            } else if (THREE.equals(type)) {
                driver.setEmpower(3);
                content = "您好,司机" + driver.getName() + PHONE_REMARK + mobile + ",拒绝授权成为您车队的司机!";
                cellPhone = user.getCellPhone();
            }
            invoiceDbService.updateInvoiceDriver(driver);

            //短信
            ShortMessageBean shortMessage = new ShortMessageBean();
            shortMessage.setCellPhone(cellPhone);
            shortMessage.setContent(content);
            shortMessage.setRemark(remark);

            messageCenterPushService.sendMultiMessage(shortMessage, null, null);
        } catch (Exception e) {
            log.error("授权操作失败", e);
        }
    }

    /**
     * 查询需要授权的数据
     *
     * @param userId
     * @return List<DriverInvoiceDto>
     */
    public List<DriverInvoiceDto> empowerList(Long userId) throws Exception {
        //司机用户信息
        User user = userService.getByUserId(userId);
        List<TytInvoiceDriver> drivers = invoiceDbService.getByPhone(user.getCellPhone());
        if (CollectionUtils.isEmpty(drivers)) {
            return Collections.emptyList();
        }
        List<DriverInvoiceDto> dtos = new ArrayList<>();
        for (TytInvoiceDriver driver : drivers) {
            DriverInvoiceDto dto = new DriverInvoiceDto();
            dto.setId(driver.getId());
            //车队长的信息
            TytUserIdentityAuth auth = tytUserIdentityAuthService.getByUserId(driver.getUserId().toString());
            User carUser = userService.getByUserId(driver.getUserId());
            String phone = carUser.getCellPhone().substring(carUser.getCellPhone().length() - 4);
            String content = "";
            if (null != auth) {
                content = "您好,车队长" + auth.getTrueName() + PHONE_REMARK + phone + "账户,请求添加您为其车队司机,可为您承接运单，请问是否同意授权!";
            } else {
                content = "您好,手机尾号" + phone + "账户,请求添加您为其车队司机,可为您承接运单,请问是否同意授权!";
            }
            dto.setContent(content);
            dtos.add(dto);
        }
        return dtos;
    }


    @Override
    public DriverInvoiceDto invoiceInfo(Long id)throws Exception{
        TytInvoiceDriver driver = invoiceDbService.getById(id);
        if (driver == null) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "司机不存在"));
        }

        log.info("司机授权状态{}",driver.getEmpower());
        if(0 == driver.getEmpower() || 1 == driver.getEmpower() || 3 == driver.getEmpower()){
            DriverInvoiceDto dto = new DriverInvoiceDto();
            dto.setId(id);
            //车队长的信息
            TytUserIdentityAuth auth = tytUserIdentityAuthService.getByUserId(driver.getUserId().toString());
            User carUser = userService.getByUserId(driver.getUserId());
            String phone = carUser.getCellPhone().substring(carUser.getCellPhone().length() - 4);
            String content = "";
            if (null != auth) {
                content = "您好,车队长" + auth.getTrueName() + PHONE_REMARK + phone + "账户,请求添加您为其车队司机,可为您承接运单，请问是否同意授权!";
            } else {
                content = "您好,手机尾号" + phone + "账户,请求添加您为其车队司机,可为您承接运单,请问是否同意授权!";
            }
            dto.setContent(content);
            return dto;
        }
        return null;
    }


}
