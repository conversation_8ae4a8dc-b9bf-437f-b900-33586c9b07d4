package com.tyt.plat.biz.invoice.pojo;

import com.tyt.plat.entity.base.TytInvoiceDriverCredential;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/4/24 下午5:43
 */
@Data
public class DriverDetailCredentialDTO implements Serializable {

    private Long id;

    /**
     * 图片类型(1 身份证正面、2 身份证反面、3 驾驶证正面、4 驾驶证反面、 5 从业资格证)
     */
    private Integer picType;

    /**
     * 图片url
     */
    private String picUrl;

    /**
     * 认证状态(0 认证中、1 认证成功、2 认证失败)
     */
    private Integer verifyStatus;

    /**
     * 认证结果描述
     */
    private String verifyDesc;

    public static DriverDetailCredentialDTO fromDO(TytInvoiceDriverCredential tytInvoiceDriverCredential) {
        DriverDetailCredentialDTO driverDetailCredentialDTO = new DriverDetailCredentialDTO();
        driverDetailCredentialDTO.setId(tytInvoiceDriverCredential.getId());
        driverDetailCredentialDTO.setPicType(tytInvoiceDriverCredential.getPicType());
        driverDetailCredentialDTO.setPicUrl(tytInvoiceDriverCredential.getPicUrl());
        driverDetailCredentialDTO.setVerifyStatus(tytInvoiceDriverCredential.getVerifyStatus());
        driverDetailCredentialDTO.setVerifyDesc(tytInvoiceDriverCredential.getVerifyDesc());

        return driverDetailCredentialDTO;
    }

    public DriverDetailCredentialDTO clearInfoIfVerifySuccess() {
        if (Objects.equals(picType, 1) || StringUtils.isBlank(picUrl)) {
            picUrl = null;
        }

        return this;
    }
}
