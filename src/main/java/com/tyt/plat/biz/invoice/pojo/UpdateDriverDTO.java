package com.tyt.plat.biz.invoice.pojo;

import com.tyt.plat.entity.base.TytInvoiceDriver;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@Data
public class UpdateDriverDTO implements Serializable {

    @NotNull(message = "司机id不能为空")
    private Long id;

    /**
     * 手机号
     */
    @NotNull(message = "手机号必填")
    @Size(min = 6, message = "手机号必填")
    private String phone;

    /**
     * 姓名
     */
    @NotNull(message = "姓名输入不正确")
    @Size(min = 2, max = 10, message = "姓名输入不正确")
    private String name;

    /**
     * 身份证号
     */
    @NotNull(message = "身份证号输入不正确或不存在")
    @Size(min = 15, max = 18, message = "身份证号输入不正确或不存在")
    private String idCard;

    /**
     * 身份证有效期结束日期
     */
    private Long idCardExpirationTime;

    /**
     * 从业资格证号
     */
    @NotNull(message = "从业资格证号必填")
    @Size(min = 1, message = "从业资格证号必填")
    private String qcCard;

    /**
     * 从业资格证有效期
     */
    private Long qcCardExpirationTime;

    /**
     * 身份证是否是长期
     */
    private Boolean idCardPermanent;

    /**
     * 发证机关
     */
    private String signGov;

    /**
     * 从业资格证姓名
     */
    private String qcName;

    /**
     * 从业资格证准驾车型
     */
    private String drivingClass;

    /**
     * 从业资格证类别
     */
    private String category;

    /**
     * 从业资格证发证日期
     */
    private String initialIssueDate;

    private String credentials = "";

    public void toDO(TytInvoiceDriver tytInvoiceDriver) {
        tytInvoiceDriver.setId(id);
        tytInvoiceDriver.setPhone(phone);
        tytInvoiceDriver.setName(name);
        tytInvoiceDriver.setIdCard(idCard);
        tytInvoiceDriver.setQcCard(qcCard);
        tytInvoiceDriver.setIdCardPermanent(idCardPermanent);
        if (idCardExpirationTime != null) {
            tytInvoiceDriver.setIdCardExpirationTime(new Date(idCardExpirationTime));
        }
        if (qcCardExpirationTime != null) {
            tytInvoiceDriver.setQcCardExpirationTime(new Date(qcCardExpirationTime));
        }
        if (StringUtils.isNotBlank(signGov)){
            tytInvoiceDriver.setIdCardSignGov(signGov);
        }
    }
}