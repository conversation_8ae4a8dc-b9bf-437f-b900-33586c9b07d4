package com.tyt.plat.biz.invoice.pojo;

import com.tyt.plat.constant.RegexConstant;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.util.TimeUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@Data
public class AddDriverDTO implements Serializable {

    /**
     * 手机号
     */
    @NotNull(message = "手机号必填")
    @Size(min = 11, message = "手机号为11位")
    @Pattern(regexp = RegexConstant.phone_number, message = "身份证格式不正确")
    private String phone;

    /**
     * 姓名
     */
    @NotNull(message = "姓名输入不正确")
    @Size(min = 2, max = 10, message = "姓名输入不正确")
    private String name;

    /**
     * 身份证号
     */
    @NotNull(message = "身份证号输入不正确或不存在")
    @Size(min = 15, max = 18, message = "身份证号输入不正确")
    @Pattern(regexp = RegexConstant.id_card, message = "身份证格式不正确")
    private String idCard;

    /**
     * 身份证有效期结束日期
     */
    private Long idCardExpirationTime;

    /**
     * 从业资格证号
     */
    @NotNull(message = "从业资格证号必填")
    @Size(min = 18, max = 20, message = "从业资格证号输入不正确")
    private String qcCard;

    /**
     * 从业资格证有效期
     */
    private Long qcCardExpirationTime;

    @NotNull(message = "证件照必传")
    private String credentials;

    /**
     * 身份证是否是长期
     */
    private Boolean idCardPermanent;

    /**
     * 发证机关
     */
    private String signGov;

    /**
     * 从业资格证姓名
     */
    private String qcName;

    /**
     * 从业资格证准驾车型
     */
    private String drivingClass;

    /**
     * 从业资格证类别
     */
    private String category;

    /**
     * 从业资格证发证日期
     */
    private String initialIssueDate;

    public TytInvoiceDriver toDO() {
        TytInvoiceDriver tytInvoiceDriver = new TytInvoiceDriver();
        tytInvoiceDriver.setPhone(phone);
        tytInvoiceDriver.setName(name);
        tytInvoiceDriver.setIdCard(idCard);
        tytInvoiceDriver.setQcCard(qcCard);
        tytInvoiceDriver.setIdCardPermanent(idCardPermanent);
        if (idCardExpirationTime != null && idCardExpirationTime > 0) {
            tytInvoiceDriver.setIdCardExpirationTime(new Date(idCardExpirationTime));
        } else {
            tytInvoiceDriver.setIdCardExpirationTime(null);
        }
        if (qcCardExpirationTime != null && qcCardExpirationTime > 0) {
            tytInvoiceDriver.setQcCardExpirationTime(new Date(qcCardExpirationTime));
        } else {
            tytInvoiceDriver.setQcCardExpirationTime(null);
        }
        if (StringUtils.isNotBlank(qcName)) {
            tytInvoiceDriver.setQcName(qcName);
        }
        if (StringUtils.isNotBlank(drivingClass)) {
            tytInvoiceDriver.setQcCardVehicleType(drivingClass);
        }
        if (StringUtils.isNotBlank(category)) {
            tytInvoiceDriver.setQcCardType(category);
        }
        if (StringUtils.isNotBlank(signGov)){
            tytInvoiceDriver.setIdCardSignGov(signGov);
        }
        return tytInvoiceDriver;
    }
}