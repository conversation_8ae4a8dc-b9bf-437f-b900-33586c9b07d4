package com.tyt.plat.biz.invoice.pojo;

import com.tyt.plat.entity.base.TytInvoiceDriverCredential;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.ReturnCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddDriverCredentialDTO implements Serializable {

    /**
     * 图片类型(1 身份证正面、2 身份证反面、3 驾驶证正面、4 驾驶证反面、 5 从业资格证)
     */
    @Range(min = 1, max = 5, message = "图片必传")
    private Integer picType;

    /**
     * 图片url
     */
    @Size(min = 1, message = "图片必传")
    private String picUrl;

    public TytInvoiceDriverCredential toDO() {
        if (picType < 0 || picType > 5 || StringUtils.isBlank(picUrl)) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "司机凭证参数异常"));
        }
        TytInvoiceDriverCredential driverCredential = new TytInvoiceDriverCredential();
        driverCredential.setPicUrl(picUrl);
        driverCredential.setPicType(picType);
        return driverCredential;
    }
}