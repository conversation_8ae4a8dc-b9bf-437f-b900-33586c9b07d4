package com.tyt.plat.biz.invoice.pojo;

import com.tyt.plat.entity.base.TytInvoiceDriver;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/4/24 下午5:00
 */
@Data
public class DriverListVO implements Serializable {

    private Long id;

    /**
     * 认证状态(0 认证中，1 认证成功，2 认证失败，3 未认证)
     */
    @Column(name = "verify_status")
    private Integer verifyStatus;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    @Column(name = "id_card")
    private String idCard;

    /**
     * 是否能编辑
     */
    private Boolean canEdit;

    /**
     * 过期提醒
     */
    private String expireHint;

    /**
     * 是否本人 1：不是本人 2：是本人'
     */
    private Integer self;

    /**
     * 授权状态 0：未授权 1：授权中 2：已授权 3：拒绝授权
     */
    private Integer empower;

    public static DriverListVO fromDO(TytInvoiceDriver tytInvoiceDriver) {
        DriverListVO driverListVO = new DriverListVO();
        driverListVO.setId(tytInvoiceDriver.getId());
        driverListVO.setVerifyStatus(tytInvoiceDriver.getVerifyStatus());
        driverListVO.setPhone(tytInvoiceDriver.getPhone());
        driverListVO.setName(tytInvoiceDriver.getName());
        driverListVO.setIdCard(StringUtils.replacePattern(tytInvoiceDriver.getIdCard(), "(\\d{3})\\d+(.)", "$1" +
                "****************$2"));
        driverListVO.setSelf(tytInvoiceDriver.getSelf());
        driverListVO.setEmpower(tytInvoiceDriver.getEmpower());
        return driverListVO;
    }
}
