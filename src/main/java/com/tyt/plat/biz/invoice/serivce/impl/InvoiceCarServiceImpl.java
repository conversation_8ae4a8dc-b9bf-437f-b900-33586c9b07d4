package com.tyt.plat.biz.invoice.serivce.impl;

import com.tyt.plat.biz.invoice.serivce.InvoiceCarService;
import com.tyt.plat.entity.base.TytCarVO;
import com.tyt.plat.mapper.base.TytCarMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InvoiceCarServiceImpl implements InvoiceCarService {
    @Autowired
    private TytCarMapper tytCarMapper;
    @Override
    public TytCarVO getMasterDriver(Long carId) {
        return tytCarMapper.queryMasterDriver(carId);
    }
}
