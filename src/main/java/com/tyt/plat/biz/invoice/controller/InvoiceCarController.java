package com.tyt.plat.biz.invoice.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.biz.invoice.db.InvoiceDbService;
import com.tyt.plat.biz.invoice.pojo.DriverListVO;
import com.tyt.plat.biz.invoice.serivce.IInvoiceDriverService;
import com.tyt.plat.biz.invoice.serivce.InvoiceCarService;
import com.tyt.plat.biz.invoice.serivce.impl.InvoiceDriverServiceImpl;
import com.tyt.plat.entity.base.TytCarVO;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/plat/invoice/car")
public class InvoiceCarController {

    private final InvoiceCarService invoiceCarService;

    private final InvoiceDbService invoiceDbService;

    public InvoiceCarController(InvoiceCarService invoiceCarService, InvoiceDbService invoiceDbService) {
        this.invoiceCarService = invoiceCarService;
        this.invoiceDbService = invoiceDbService;
    }

    @GetMapping("/getMasterDriver")
    public ResultMsgBean getMasterDriver(Long carId) {
        try {
            TytCarVO masterDriver = invoiceCarService.getMasterDriver(carId);
            return new ResultMsgBean(200,"查询成功",masterDriver);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("查询司机列表失败,carId: {} ", carId, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
        }
    }

    @GetMapping("/getDriver")
    public ResultMsgBean getDriver(Long userId) {
        try {
            List<TytInvoiceDriver> tytInvoiceDrivers = invoiceDbService.listByUserIdAndVerifyStatus(userId, null);
            return ResultMsgBean.successResponse(tytInvoiceDrivers);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("查询司机列表失败,carId: {} ", userId, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
        }
    }

}
