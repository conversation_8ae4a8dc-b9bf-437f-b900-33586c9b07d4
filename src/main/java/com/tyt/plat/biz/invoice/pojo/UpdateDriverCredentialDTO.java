package com.tyt.plat.biz.invoice.pojo;

import com.tyt.plat.entity.base.TytInvoiceDriverCredential;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateDriverCredentialDTO implements Serializable {

    @NotNull(message = "凭证id不能为空")
    private Long id;

    /**
     * 图片url
     */
    @Size(min = 1, message = "图片必传")
    private String picUrl;

    public TytInvoiceDriverCredential toDO() {
        TytInvoiceDriverCredential driverCredential = new TytInvoiceDriverCredential();
        driverCredential.setId(id);
        driverCredential.setPicUrl(picUrl);
        return driverCredential;
    }
}