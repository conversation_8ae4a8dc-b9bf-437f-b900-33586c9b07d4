package com.tyt.plat.biz.invoice.constant;

/**
 * 司机自动审核失败原因枚举
 * <AUTHOR>
 * @since 2025-6-17 19:47:46
 */
public enum DriverVerifyFailEnums {

    ID_CARD_AGE("年龄不符合规定范围"),
    ID_CARD_EXPIRATION_TIME_PAST("身份证已过期"),
    DL_CARD_EXPIRATION_TIME_PAST("驾驶证已过期"),
    QC_CARD_EXPIRATION_TIME_PAST("从业资格证已过期");

    private final String failReason;

    public String getFailReason() {
        return failReason;
    }

    DriverVerifyFailEnums(String failReason) {
        this.failReason = failReason;
    }
}
