package com.tyt.plat.biz.invoice.constant;

/**
 * <AUTHOR>
 * @since 2023/4/26 下午2:31
 */
public enum DriverVerifyStatus {

    VERIFYING(0, "认证中"),
    VERIFY_SUCCESS(1, "认证成功"),
    VERIFY_FAILED(2, "认证失败"),
    UN_VERIFY(3, "未认证");


    private final int state;
    private final String desc;

    public int getState() {
        return state;
    }

    public String getDesc() {
        return desc;
    }

    DriverVerifyStatus(int state, String desc) {
        this.state = state;
        this.desc = desc;
    }
}
