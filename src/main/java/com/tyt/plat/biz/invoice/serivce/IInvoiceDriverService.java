package com.tyt.plat.biz.invoice.serivce;

import com.tyt.plat.biz.invoice.pojo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/24 下午2:29
 */
public interface IInvoiceDriverService {

    /**
     * 新增司机
     *
     * @param addDriverDTO addDriverDTO
     * @param userId       操作人id
     * @return 司机id
     */
    Long addDriver(AddDriverDTO addDriverDTO, Long userId) throws Exception;

    /**
     * 同步旧司机
     *
     * @param userId 用户id
     * @param name   姓名
     * @param phone  电话
     * @return 插入的司机id
     */
    Long syncOldDriver(Long userId, String name, String phone);

    /**
     * 查询司机列表
     *
     * @param userId       userId
     * @param page         page
     * @param pageSize     pageSize
     * @param verifyStatus
     * @return List
     */
    List<DriverListVO> getDriverList(Long userId, Integer page, Integer pageSize, Integer verifyStatus);

    /**
     * 查询司机详情
     *
     * @param id id
     * @return DriverDetailVO
     */
    DriverDetailVO getDriverDetail(Long id);

    /**
     * 更新司机信息
     *
     * @param userId          userId
     * @param updateDriverDTO updateDriverDTO
     * @return 司机id
     */
    Long updateDriver(Long userId, UpdateDriverDTO updateDriverDTO) throws Exception ;

    /**
     * 修改司机-车辆服务专用修改
     *
     * @param id id
     * @param name name
     * @param phone phone
     * @return 司机id
     */
    Long updateDriverInner4Car(Long id, String name, String phone);

    /**
     * 查询司机人数
     *
     * @param userId userId
     * @return DriverCountVO
     */
    DriverCountVO getDriverCount(Long userId);

    void sendInvoice(Integer type,Long id);

    List<DriverInvoiceDto> empowerList(Long userId)throws Exception;

    DriverInvoiceDto invoiceInfo(Long id)throws Exception;

    void sendInvoiceNew(Integer type, Long id);

    void updateDelStatusByCellPhone(String cellPhone);
}
