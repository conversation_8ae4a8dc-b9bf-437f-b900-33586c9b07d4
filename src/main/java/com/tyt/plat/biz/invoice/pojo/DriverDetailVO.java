package com.tyt.plat.biz.invoice.pojo;

import com.tyt.plat.entity.base.TytInvoiceDriver;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class DriverDetailVO implements Serializable {

    private Long id;

    /**
     * 认证状态(0 认证中，1 认证成功，2 认证失败，3 未认证)
     */
    private Integer verifyStatus;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 身份证有效期结束日期
     */
    private Date idCardExpirationTime;

    /**
     * 从业资格证号
     */
    private String qcCard;

    /**
     * 从业资格证有效期
     */
    private Date qcCardExpirationTime;

    /**
     * 驾驶证有效期结束日期
     */
    private Date dlCardExpirationTime;

    /**
     * 身份证是否是长期
     */
    private Boolean idCardPermanent;

    private List<DriverDetailCredentialDTO> credentials;

    public static DriverDetailVO fromDO(TytInvoiceDriver tytInvoiceDriver) {
        DriverDetailVO driverDetailVO = new DriverDetailVO();
        driverDetailVO.setId(tytInvoiceDriver.getId());
        driverDetailVO.setVerifyStatus(tytInvoiceDriver.getVerifyStatus());
        driverDetailVO.setPhone(tytInvoiceDriver.getPhone());
        driverDetailVO.setName(tytInvoiceDriver.getName());
        driverDetailVO.setIdCardPermanent(tytInvoiceDriver.getIdCardPermanent());
        driverDetailVO.setIdCard(tytInvoiceDriver.getIdCard());
        driverDetailVO.setVerifyStatus(tytInvoiceDriver.getVerifyStatus());
        driverDetailVO.setIdCardExpirationTime(tytInvoiceDriver.getIdCardExpirationTime());
        driverDetailVO.setQcCard(tytInvoiceDriver.getQcCard());
        driverDetailVO.setQcCardExpirationTime(tytInvoiceDriver.getQcCardExpirationTime());
        driverDetailVO.setDlCardExpirationTime(tytInvoiceDriver.getDlCardExpirationTime());
        return driverDetailVO;
    }
}