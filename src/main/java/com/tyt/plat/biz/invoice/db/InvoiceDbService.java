package com.tyt.plat.biz.invoice.db;

import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.plat.entity.base.TytInvoiceDriverCredential;
import com.tyt.plat.mapper.base.TytInvoiceDriverCredentialMapper;
import com.tyt.plat.mapper.base.TytInvoiceDriverMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/24 下午2:30
 */
@Component
@Slf4j
public class InvoiceDbService {

    private final TytInvoiceDriverMapper invoiceDriverMapper;
    private final TytInvoiceDriverCredentialMapper invoiceDriverCredentialMapper;

    public InvoiceDbService(TytInvoiceDriverMapper invoiceDriverMapper,
                            TytInvoiceDriverCredentialMapper invoiceDriverCredentialMapper) {
        this.invoiceDriverMapper = invoiceDriverMapper;
        this.invoiceDriverCredentialMapper = invoiceDriverCredentialMapper;
    }

    /**
     * 保存司机
     *
     * @param invoiceDriver 司机信息
     * @return 司机id
     */
    @Transactional(transactionManager = "mybatisTransactionManager")
    public Long saveInvoiceDriver(TytInvoiceDriver invoiceDriver) {
        invoiceDriverMapper.insertSelective(invoiceDriver);
        return invoiceDriver.getId();
    }

    /**
     * 保存司机证件信息
     *
     * @param driverCredentials 司机证件信息
     */
    @Transactional(transactionManager = "mybatisTransactionManager")
    public void saveInvoiceDriverCredentials(List<TytInvoiceDriverCredential> driverCredentials) {
        invoiceDriverCredentialMapper.insertList(driverCredentials);
    }

    public List<TytInvoiceDriver> listByUserIdAndVerifyStatus(Long userId, Integer verifyStatus) {
        return invoiceDriverMapper.selectByUserIdAndVerifyStatus(userId, verifyStatus);
    }

    public TytInvoiceDriver getById(Long id) {
        return invoiceDriverMapper.selectByPrimaryKey(id);
    }

    public List<TytInvoiceDriverCredential> getCredentialsByDriverId(Long driverId) {
        return invoiceDriverCredentialMapper.selectByDriverId(driverId);
    }

    @Transactional(transactionManager = "mybatisTransactionManager")
    public void updateInvoiceDriver(TytInvoiceDriver tytInvoiceDriver) {
        invoiceDriverMapper.updateByPrimaryKeySelective(tytInvoiceDriver);
    }

    public Long countVerifiedDriver(Long userId) {
        return invoiceDriverMapper.countByUserIdAndVerifyStatus(userId, null);
    }

    @Transactional(transactionManager = "mybatisTransactionManager")
    public boolean updateByPrimaryKeySelective(TytInvoiceDriverCredential driverCredential) {
        return invoiceDriverCredentialMapper.updateByPrimaryKeySelective(driverCredential) > 0;
    }

    public TytInvoiceDriverCredential getCredentialById(Long credentialId) {
        return invoiceDriverCredentialMapper.selectByPrimaryKey(credentialId);
    }

    public long countByUserIdAndIdCard(Long userId, String idCard) {
        return invoiceDriverMapper.countByUserIdAndIdCard(userId, idCard);
    }

    public List<TytInvoiceDriver>  getByPhone(String cellPhone) {
        return invoiceDriverMapper.getByPhone(cellPhone);
    }

    public long countByUserIdAndPhone(Long userId, String phone) {
        return invoiceDriverMapper.countByUserIdAndPhone(userId, phone);
    }

    public long countByUserIdIdCard(Long userId, String idCard, Long id) {
        return invoiceDriverMapper.countByUserIdIdCard(userId, idCard, id);
    }

    public long countByUserIdPhone(Long userId, String phone, Long id) {
        return invoiceDriverMapper.countByUserIdPhone(userId, phone, id);
    }

    public List<TytInvoiceDriver> getInvoiceDriverListByDriverUserId(Long driverUserId) {
        return invoiceDriverMapper.getInvoiceDriverListByDriverUserId(driverUserId);
    }

    public void updateDelStatusByCellPhone(String cellPhone) {
        invoiceDriverMapper.updateDelStatusByCellPhone(cellPhone);
    }
}
