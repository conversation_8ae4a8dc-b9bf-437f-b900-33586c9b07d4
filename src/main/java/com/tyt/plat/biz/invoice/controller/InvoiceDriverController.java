package com.tyt.plat.biz.invoice.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.biz.invoice.pojo.*;
import com.tyt.plat.biz.invoice.serivce.IInvoiceDriverService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/24 下午2:26
 */
@RestController
@Slf4j
@RequestMapping("/plat/invoice/driver")
public class InvoiceDriverController {

    private final IInvoiceDriverService invoiceDriverService;

    private final static Integer ONE = 1;
    private final static Integer TWO = 2;
    private final static Integer THREE = 3;

    public InvoiceDriverController(IInvoiceDriverService invoiceDriverService) {
        this.invoiceDriverService = invoiceDriverService;
    }

    @PostMapping("/post")
    public ResultMsgBean addDriver(@Validated AddDriverDTO addDriverDTO,
                                   BindingResult bindingResult,
                                   Long userId) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }
        try {
            Long driverId = invoiceDriverService.addDriver(addDriverDTO, userId);
            return ResultMsgBean.successResponse(new AddDriverVO(driverId));
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("添加司机失败", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    @GetMapping("/list")
    public ResultMsgBean getDriverList(Long userId, @RequestParam Integer page, @RequestParam Integer pageSize,
                                       @RequestParam(required = false) Integer verifyStatus) {
        try {
            List<DriverListVO> drivers = invoiceDriverService.getDriverList(userId, page, pageSize, verifyStatus);
            return ResultMsgBean.successResponse(drivers);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("查询司机列表失败,userId: {}, page: {}, pageSize:{} ", userId, page, pageSize, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "查询司机列表失败");
        }
    }

    @GetMapping("/count")
    public ResultMsgBean getDriverCount(Long userId) {
        try {
            DriverCountVO driverCount = invoiceDriverService.getDriverCount(userId);
            return ResultMsgBean.successResponse(driverCount);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("查询司机数量失败,userId: {}", userId, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "服务器错误");
        }
    }

    @GetMapping("/detail")
    public ResultMsgBean getDriverDetail(Long userId, @RequestParam Long id) {
        try {
            DriverDetailVO driver = invoiceDriverService.getDriverDetail(id);
            if (driver == null) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "司机不存在"));
            }
            return ResultMsgBean.successResponse(driver);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("查询司机详情失败,userId: {}, id: {} ", userId, id, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "查询司机详情失败");
        }
    }

    @PostMapping("/update")
    public ResultMsgBean updateDriver(Long userId, @Validated UpdateDriverDTO updateDriverDTO,
                                      BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }

        try {
            Long driverId = invoiceDriverService.updateDriver(userId, updateDriverDTO);
            return ResultMsgBean.successResponse(driverId);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("更新司机详情失败,userId: {}, updateDriverDTO: {} ", userId, updateDriverDTO, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "更新司机详情失败");
        }
    }

    /**
     * 司机授权
     * @param type 分类 1请求授权  2同意授权 3 拒绝授权
     * @param id 司机列表id
     * @return ResultMsgBean
     */
    @GetMapping("/empower")
    public ResultMsgBean empower(Integer type, Long id) {
        if(null == type || null == id){
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "参数不正确");
        }
        try {
            invoiceDriverService.sendInvoice(type, id);
            return ResultMsgBean.successResponse();
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("司机授权失败,type: {}, id: {} ", type, id, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "司机授权失败");
        }
    }


    /**
     * 查询需要司机授权的信息
     *
     * @param userId 司机用户id
     * @return ResultMsgBean
     */
    @GetMapping("/empower/list")
    public ResultMsgBean empowerList(Long userId) {
        if(null == userId){
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "参数不正确");
        }
        try {
            List<DriverInvoiceDto> drivers = invoiceDriverService.empowerList(userId);
            return ResultMsgBean.successResponse(drivers);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("司机授权请求列表失败,userId: {} ", userId, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "司机授权请求列表失败");
        }
    }


    /**
     * 司机授权
     * @param id 司机表id
     * @return ResultMsgBean
     */
    @RequestMapping("/empower/invoiceInfo")
    public ResultMsgBean invoiceInfo(Long id) {
        if(null == id){
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "参数不正确");
        }
        try {
            DriverInvoiceDto drivers = invoiceDriverService.invoiceInfo(id);
            return ResultMsgBean.successResponse(drivers);
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("司机授权详情,id: {} ", id, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "司机授权详情失败");
        }
    }



    /**
     * 司机授权-新
     * @param type 分类  2同意授权 3 拒绝授权
     * @param id 司机列表id
     * @return ResultMsgBean
     */
    @GetMapping("/empowerNew")
    public ResultMsgBean empowerNew(Integer type, Long id) {
        if(null == type || null == id){
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "参数不正确");
        }
        try {
            invoiceDriverService.sendInvoiceNew(type, id);
            return ResultMsgBean.successResponse();
        } catch (TytException tytException) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, tytException.getErrorMsg());
        } catch (Exception e) {
            log.error("司机授权失败,type: {}, id: {} ", type, id, e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "司机授权失败");
        }
    }

}
