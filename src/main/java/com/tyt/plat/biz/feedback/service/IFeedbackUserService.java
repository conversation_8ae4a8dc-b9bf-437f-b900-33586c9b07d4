package com.tyt.plat.biz.feedback.service;

import com.tyt.common.bean.PageData;
import com.tyt.plat.biz.feedback.pojo.*;
import com.tyt.plat.commons.model.CheckResult;
import com.tyt.plat.entity.base.FeedbackUser;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:03
 */
public interface IFeedbackUserService {

    /**
     * 发表评价
     *
     * @param req          req
     * @param userId       用户id
     * @param postUserType 发布人类型
     * @return 返回体
     */
    FeedbackUser postFeedBack(PostFeedbackReq req, Long userId, Integer postUserType);

    Long updateFeedBack(UpdateFeedbackReq req, Long userId, Integer updateUserType);

    Long deleteFeedback(DeleteFeedbackReq req, Long userId, Integer deleteUserType);

    GetUserFeedbackCountVO getUserFeedBackCount(Long userId, Integer userType, Integer operateType);

    GetUserFeedbackRatingVO getUserFeedBackRating(Long userId, Integer userType);

    List<GetUserFeedbackLabelSumVO> getUserFeedbackLabelSum(Integer feedbackType, Integer operateType, Long userId,
                                                            Integer userType);

    PageData<GetUserFeedbackListVO> getUserFeedbackList(Integer feedbackType, Integer operateType, Long userId,
                                                        Integer userType, Long labelId, Integer pageNum,
                                                        Integer pageSize);

    UserFeedbackRatingAndLabelDTO getUserFeedbackRatingAndLabel(Long userId, Integer userType);

    CheckResult<String> checkCanPostFeedBack(Long tsId, Long userId, int postUserType) throws Exception;

    List<GetFeedbackLabelVO> getFeedbackLabels(int userType, Integer feedbackType);

    PageData<GetUserFeedbackTodoListVO> getUserFeedbackTodoList(Long userId, int userType, Integer pageNum, Integer pageSize);

    /**
     * 获取用户待评价订单数量
     * @param userId
     * @param userType
     * @return
     */
    Integer getUserFeedbackTodoCount(Long userId, int userType);

    ClaimExposureCardRewardVO claimExposureCardReward(ClaimExposureCardReq req, Long userId, int userType);

    GetExposureCardRewardInfoVO getExposureCardRewardInfo(Long feedbackId, Long userId, int userType);

    GetUserFeedbackDetailVO getFeedBackDetail(Long feedbackId);

    CheckInfoFeeExistVO checkInfoFeeExist(Long orderId, Long userId, int userType);

    /**
     * 根据申诉ID查询申诉信息
     *
     * @param feedbackAppealId 申诉ID
     * @return
     */
    FeedBackUserAppealInfoVO getAppealInfoByAppealId(Long feedbackAppealId);

    /**
     * 申诉信息保存
     *
     * @param req 申诉信息类
     * @return 申诉ID
     */
    FeedBackUserAppealInfoResp appeal(FeedBackUserAppealInfoReq req);

    /**
     * 发送短信、push、站内信
     * <AUTHOR>
     * @param userId
     * @param userType
     * @param userPhone
     * @return void
     */
    void sendMultiMessage(Long userId, Integer userType, String userPhone);
}
