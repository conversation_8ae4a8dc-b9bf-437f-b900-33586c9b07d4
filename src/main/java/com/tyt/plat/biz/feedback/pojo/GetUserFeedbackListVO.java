package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetUserFeedbackListVO implements Serializable {

    public static final int RECEIVER_HIDE_FLAG_TRUE = 0;

    public static final int RECEIVER_HIDE_FLAG_FALSE = 1;

    /**
     * 评价id
     */
    private Long id;

    /**
     * 对应的运单id
     */
    private Long tsOrderId;

    /**
     * 对应的货源id
     */
    private Long tsId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 评价对应展示名称
     */
    private String transportOrderUserName;

    /**
     * 评价对应头像 头像可能为空 有些用户会返回服务器本地路径，有些会返回图片http链接
     */
    private String avatar;

    /**
     * 对应运单出发地
     */
    private String transportOrderStartPoint;

    /**
     * 对应运单目的地
     */
    private String transportOrderDestPoint;

    /**
     * 评价类别 1好评 2中评 3差评
     */
    private Integer feedbackType;

    /**
     * 标签名集合
     */
    private List<String> labelNames;

    /**
     * 是否能编辑
     */
    private Boolean canEdit;

    /**
     * 是否能删除
     */
    private Boolean canDelete;

    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 承运车牌
     */
    private String carNo;

    /**
     * 申诉状态：1-申诉中；2-已驳回；3-申诉成功；4-申诉失败；
     */
    private Integer appealStatus;

    /**
     * 驳回原因（见字典表 feedback_appeal_reject_reason）
     */
    private Integer rejectReason;

    /**
     * 数据状态：0-有效；1-失效；
     */
    private Integer receiverHideFlag;

    /**
     * 失效原因：1-责任方评价失效；2-申诉成功失效；
     */
    private Integer loseEfficacyReason;

    /**
     * 评价申诉ID
     */
    private Long feedbackAppealId;
}
