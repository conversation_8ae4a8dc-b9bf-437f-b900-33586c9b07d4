package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetUserFeedbackCountVO implements Serializable {

    /**
     * 总评价数
     */
    private Long total;
    /**
     * 差评数
     */
    private Long negative;
    /**
     * 好评数
     */
    private Long positive;
    /**
     * 中评数
     */
    private Long neutral;
}
