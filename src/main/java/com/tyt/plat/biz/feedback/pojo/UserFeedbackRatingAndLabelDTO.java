package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/6 上午11:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFeedbackRatingAndLabelDTO implements Serializable {

    /**
     * 好评率
     */
    private String rating;

    /**
     * 好评数
     */
    private Long positiveCount;

    /**
     * 好评标签名，最多2条
     */
    private List<String> positiveLabels;

    /**
     * 差评标签名，最多1条
     */
    private List<String> negativeLabels;

    /**
     * 总评价量
     */
    private Long total;
}
