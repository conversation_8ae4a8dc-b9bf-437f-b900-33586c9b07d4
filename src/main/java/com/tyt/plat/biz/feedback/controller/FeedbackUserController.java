package com.tyt.plat.biz.feedback.controller;

import com.tyt.common.bean.PageData;
import com.tyt.model.ResultMsgBeanV2;
import com.tyt.plat.biz.feedback.pojo.*;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.entity.base.FeedbackUser;
import com.tyt.util.Constant;
import com.tyt.util.PreConditions;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.tyt.util.ReturnCodeConstant.BASIC_PARAMETER_ERROR;

/**
 * 用户评价
 * 用户评价相关接口
 *
 * <AUTHOR>
 * @since 2023/5/31 下午2:02
 */
@RestController
@Slf4j
@RequestMapping("/plat/feedback/user")
public class FeedbackUserController {

    private final IFeedbackUserService feedbackService;


    public FeedbackUserController(IFeedbackUserService feedbackService) {
        this.feedbackService = feedbackService;
    }

    /**
     * 发表评价
     * <p>
     * 2023年7月17日 参数 transportId 改为 orderId ，传订单的id
     *
     * @param req        req
     * @param userId     用户id
     * @param clientSign 客户端标识
     * @return 返回体
     */
    @PostMapping("/post")
    public ResultMsgBeanV2<PostFeedbackVO> postFeedBack(@Validated PostFeedbackReq req,
                                                        BindingResult bindingResult,
                                                        Long userId, Integer clientSign) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBeanV2.failResponse(BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }

        // 版本
        try {
            int postUserType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(postUserType == 1 || postUserType == 2, ReturnCodeConstant.ERROR, "版本过低，无法评价");

            PreConditions.check(!Pattern.matches(".*://.*", req.getComment() == null ? "" : req.getComment()),
                    ReturnCodeConstant.ERROR, "备注不符合规范");

            FeedbackUser feedbackUser = feedbackService.postFeedBack(req, userId, postUserType);

            String displayText;
            switch (req.getFeedbackType()) {
                case 1:
                    displayText = "您的评价已收到，感谢您的肯定";
                    break;
                case 2:
                    displayText = "感谢您的反馈，会继续努力改进";
                    break;
                case 3:
                    displayText = "感谢您的反馈，抱歉给您带来不好体验";
                    break;
                default:
                    displayText = "";
            }

            return ResultMsgBeanV2.successResponse(new PostFeedbackVO(feedbackUser.getId(), displayText));
        } catch (Exception e) {
            log.error("发表评价失败,userId: {}, 参数: {}", userId, req, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 查询评论详情
     *
     * @param feedbackId 评价id  req
     * @return 返回体
     */
    @PostMapping("/detail")
    public ResultMsgBeanV2<GetUserFeedbackDetailVO> getFeedBackDetail(@RequestParam Long feedbackId) {

        try {

            GetUserFeedbackDetailVO feedbackDetailVO = feedbackService.getFeedBackDetail(feedbackId);
            return ResultMsgBeanV2.successResponse(feedbackDetailVO);
        } catch (Exception e) {
            log.error("获取评价详情失败, 参数: {}", feedbackId, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 发布评价后查询可领取的曝光卡数量
     *
     * @param feedbackId 评价id
     * @param userId     用户id
     * @param clientSign 客户端标识
     * @return 返回体
     */
    @GetMapping("/reward/exposureCard/info")
    public ResultMsgBeanV2<GetExposureCardRewardInfoVO> getExposureCardRewardInfo(@RequestParam Long feedbackId,
                                                                                  Long userId,
                                                                                  Integer clientSign) {
        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法领取曝光卡");

            return ResultMsgBeanV2.successResponse(feedbackService.getExposureCardRewardInfo(feedbackId, userId,
                    userType));
        } catch (Exception e) {
            log.error("发布评价后领取曝光卡失败,userId: {}, 参数: {}", userId, feedbackId, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 发布评价后领取曝光卡
     *
     * @param req        req
     * @param userId     用户id
     * @param clientSign 客户端标识
     * @return 返回体
     */
    @PostMapping("/reward/exposureCard/claim")
    public ResultMsgBeanV2<ClaimExposureCardRewardVO> claimExposureCardReward(@Validated ClaimExposureCardReq req,
                                                                              BindingResult bindingResult,
                                                                              Long userId, Integer clientSign) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBeanV2.failResponse(BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }

        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法领取曝光卡");

            return ResultMsgBeanV2.successResponse(feedbackService.claimExposureCardReward(req, userId, userType));
        } catch (Exception e) {
            log.error("发布评价后领取曝光卡失败,userId: {}, 参数: {}", userId, req, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 修改评价
     *
     * @param req        req
     * @param userId     用户id
     * @param clientSign 客户端标识
     * @return 返回体
     */
    @PostMapping("/update")
    public ResultMsgBeanV2<UpdateFeedbackVO> updateFeedBack(@Validated UpdateFeedbackReq req,
                                                            BindingResult bindingResult,
                                                            Long userId, Integer clientSign) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBeanV2.failResponse(BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }

        try {
            int updateUserType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(updateUserType == 1 || updateUserType == 2, ReturnCodeConstant.ERROR, "版本过低，无法评价");

            Long id = feedbackService.updateFeedBack(req, userId, updateUserType);

            String displayText;
            switch (req.getFeedbackType()) {
                case 1:
                    displayText = "您的评价已收到，感谢您的肯定";
                    break;
                case 2:
                    displayText = "感谢您的反馈，会继续努力改进";
                    break;
                case 3:
                    displayText = "感谢您的反馈，抱歉给您带来不好体验";
                    break;
                default:
                    displayText = "";
            }
            return ResultMsgBeanV2.successResponse(new UpdateFeedbackVO(id, displayText));
        } catch (Exception e) {
            log.error("修改评价失败,userId: {}, 参数: {}", userId, req, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 删除评价
     *
     * @param req        req
     * @param userId     用户id
     * @param clientSign 客户端标识
     * @return 返回体
     */
    @PostMapping("/delete")
    public ResultMsgBeanV2<DeleteFeedbackVO> deleteFeedBack(@Validated DeleteFeedbackReq req,
                                                            BindingResult bindingResult,
                                                            Long userId, Integer clientSign) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBeanV2.failResponse(BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }

        try {
            int deleteUserType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(deleteUserType == 1 || deleteUserType == 2, ReturnCodeConstant.ERROR, "版本过低，无法评价");

            Long id = feedbackService.deleteFeedback(req, userId, deleteUserType);
            return ResultMsgBeanV2.successResponse(new DeleteFeedbackVO(id));
        } catch (Exception e) {
            log.error("删除评价失败,userId: {}, 参数: {}", userId, req, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 我的评价-发表的评价数量、收到的评价数量（近1年）
     * <p>
     * 2023年7月10日 增加 operateType 参数
     *
     * @param userId      用户id
     * @param operateType 类型 1 发表的评价、2 收到的评价
     * @param clientSign  客户端标识
     * @return 返回体
     */
    @GetMapping("/info/count")
    public ResultMsgBeanV2<GetUserFeedbackCountVO> getUserFeedBackCount(Long userId,
                                                                        Integer clientSign,
                                                                        @RequestParam Integer operateType) {

        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法获取评价信息");

            GetUserFeedbackCountVO vo = feedbackService.getUserFeedBackCount(userId, userType, operateType);
            return ResultMsgBeanV2.successResponse(vo);
        } catch (Exception e) {
            log.error("我的评价-发表的评价数量、收到的评价数量（近1年） 失败,userId: {}", userId, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 我的评价-好评率信息
     *
     * @param userId     用户id
     * @param clientSign 客户端标识
     * @return 返回体
     */
    @GetMapping("/info/rating")
    public ResultMsgBeanV2<GetUserFeedbackRatingVO> getUserFeedBackRating(Long userId, Integer clientSign) {

        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法获取评价信息");

            GetUserFeedbackRatingVO vo = feedbackService.getUserFeedBackRating(userId, userType);

            return ResultMsgBeanV2.successResponse(vo);
        } catch (Exception e) {
            log.error("我的评价-好评率信息 失败,userId: {}", userId, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 我的评价-评价列表标签信息
     *
     * @param feedbackType 评价类型 不传=全部  1好评 2中评 3差评
     * @param operateType  类型 1 发表的评价、2 收到的评价
     * @param userId       用户id
     * @param clientSign   客户端标识
     * @return 返回体
     */
    @GetMapping("/info/label/sum")
    public ResultMsgBeanV2<List<GetUserFeedbackLabelSumVO>> getUserFeedbackLabelSum(
            @RequestParam(value = "feedbackType", required = false) Integer feedbackType,
            @RequestParam(value = "operateType") Integer operateType,
            Long userId,
            Integer clientSign) {

        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法获取评价信息");
            PreConditions.check(feedbackType == null || feedbackType == 1 || feedbackType == 2 || feedbackType == 3,
                    ReturnCodeConstant.ERROR, "条件有误");

            List<GetUserFeedbackLabelSumVO> vo = feedbackService.getUserFeedbackLabelSum(feedbackType, operateType,
                    userId,
                    userType);

            return ResultMsgBeanV2.successResponse(vo);
        } catch (Exception e) {
            log.error("我的评价-评价列表标签信息 失败,userId: {}", userId, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 我的评价-评价列表详情信息(收到的、发表的评价)
     *
     * @param feedbackType 评价类型  不传=全部  1好评 2中评 3差评
     * @param operateType  类型 1 发表的评价、2 收到的评价
     * @param userId       用户id
     * @param clientSign   客户端标识
     * @param labelId      标签id
     * @param pageNum      页数
     * @param pageSize     每页数量
     * @return 返回体
     */
    @GetMapping("/info/list")
    public ResultMsgBeanV2<PageData<GetUserFeedbackListVO>> getUserFeedbackList(
            @RequestParam(value = "feedbackType", required = false) Integer feedbackType,
            @RequestParam(value = "operateType") Integer operateType,
            Long userId,
            Integer clientSign,
            @RequestParam(value = "labelId", required = false) Long labelId,
            @RequestParam(value = "pageNum", required = false) Integer pageNum,
            @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法获取评价信息");
            PreConditions.check(feedbackType == null || feedbackType == 1 || feedbackType == 2 || feedbackType == 3,
                    ReturnCodeConstant.ERROR, "条件有误");

            PageData<GetUserFeedbackListVO> vo = feedbackService.getUserFeedbackList(feedbackType, operateType, userId,
                    userType, labelId, pageNum, pageSize);

            return ResultMsgBeanV2.successResponse(vo);
        } catch (Exception e) {
            log.error("我的评价-评价列表详情信息(收到的、发表的评价) 失败,userId: {}", userId, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 我的评价-评价列表详情信息(待评价)
     * <p>
     * 2023年7月17日 新增返回id，发表评价的orderId传这个
     *
     * @param userId     用户id
     * @param clientSign 客户端标识
     * @param pageNum    页数
     * @param pageSize   每页数量
     * @return 返回体
     */
    @GetMapping("/info/list/todo")
    public ResultMsgBeanV2<PageData<GetUserFeedbackTodoListVO>> getUserFeedbackTodoList(
            Long userId,
            Integer clientSign,
            @RequestParam(value = "pageNum", required = false) Integer pageNum,
            @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法获取评价信息");

            PageData<GetUserFeedbackTodoListVO> vo = feedbackService.getUserFeedbackTodoList(userId, userType, pageNum,
                    pageSize);

            return ResultMsgBeanV2.successResponse(vo);
        } catch (Exception e) {
            log.error(" 我的评价-评价列表详情信息(待评价) 失败,userId: {}", userId, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 提交评价-获取提交评价页面标签列表
     *
     * @param feedbackType 评价类型 1好评 2中评 3差评
     * @return 返回体
     */
    @GetMapping("post/label/list")
    public ResultMsgBeanV2<List<GetFeedbackLabelVO>> getFeedbackLabels(Integer clientSign,
                                                                       @RequestParam(value = "feedbackType") Integer feedbackType) {

        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法获取评价信息");
            PreConditions.check(feedbackType == 1 || feedbackType == 2 || feedbackType == 3,
                    ReturnCodeConstant.ERROR, "条件有误");

            List<GetFeedbackLabelVO> result = feedbackService.getFeedbackLabels(userType, feedbackType);

            return ResultMsgBeanV2.successResponse(result);
        } catch (Exception e) {
            log.error("提交评价-获取提交评价页面标签列表 失败", e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 检查订单是否存在(是否需要跳转到详情)
     *
     * @param orderId 订单id, 未评价列表对应的id字段
     * @return 返回体
     */
    @GetMapping("/infoFee/exist/check")
    public ResultMsgBeanV2<CheckInfoFeeExistVO> checkInfoFeeExist(Integer clientSign,
                                                                  Long userId,
                                                                  @RequestParam(value = "feedbackType") Long orderId) {
        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低");

            return ResultMsgBeanV2.successResponse(feedbackService.checkInfoFeeExist(orderId, userId, userType));
        } catch (Exception e) {
            log.error("提交评价-获取提交评价页面标签列表 失败", e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }


    /**
     * 获取待评价的订单数量
     *
     * @param userId
     * @param clientSign
     * @return
     */
    @GetMapping("/info/todo/count")
    public ResultMsgBeanV2<Map<String, Object>> getUserFeedbackTodoCount(Long userId, Integer clientSign) {

        try {
            int userType = Constant.isCarOrGoodsOrOrigin(clientSign);
            PreConditions.check(userType == 1 || userType == 2, ReturnCodeConstant.ERROR, "版本过低，无法获取评价信息");

            Integer feedBackTodoCount = feedbackService.getUserFeedbackTodoCount(userId, userType);
            Map<String, Object> map = new HashMap<>();
            map.put("feedBackTodoCount", feedBackTodoCount);

            return ResultMsgBeanV2.successResponse(map);
        } catch (Exception e) {
            log.error("获取待评价的订单数量 失败,userId: {}", userId, e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 查询评价上次的申诉信息
     *
     * @param feedbackAppealId 评价ID
     * @return 返回体
     */
    @GetMapping("/appeal/info/get")
    public ResultMsgBeanV2<FeedBackUserAppealInfoVO> getAppealInfoByAppealId(
            @RequestParam(name = "feedbackAppealId") Long feedbackAppealId) {
        try {
            return ResultMsgBeanV2.successResponse(feedbackService.getAppealInfoByAppealId(feedbackAppealId));
        } catch (Exception e) {
            log.error("FeedBackUser getAppealInfoByFeedBackId error", e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 提交评价申诉信息
     *
     * @param req 申诉提交实体
     * @return 返回体
     */
    @PostMapping("/appeal/info/post")
    public ResultMsgBeanV2<FeedBackUserAppealInfoResp> appeal(@Validated FeedBackUserAppealInfoReq req,
                                                              BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBeanV2.failResponse(BASIC_PARAMETER_ERROR, bindingResult.getFieldError().getDefaultMessage());
        }
        try {
            return ResultMsgBeanV2.successResponse(feedbackService.appeal(req));
        } catch (Exception e) {
            log.error("FeedBackUser appeal error", e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }
}
