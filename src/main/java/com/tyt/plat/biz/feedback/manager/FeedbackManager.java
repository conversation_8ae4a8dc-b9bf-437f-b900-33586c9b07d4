package com.tyt.plat.biz.feedback.manager;

import com.alibaba.fastjson.JSON;
import com.tyt.common.bean.PageData;
import com.tyt.plat.biz.feedback.constant.FeedbackTypeEnum;
import com.tyt.plat.biz.feedback.manager.pojo.FeedbackCountDTO;
import com.tyt.plat.biz.feedback.manager.pojo.FeedbackLabelSumDTO;
import com.tyt.plat.biz.feedback.pojo.FeedBackUserAppealInfoReq;
import com.tyt.plat.commons.model.PageParameter;
import com.tyt.plat.commons.tools.CustomPageHelper;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.mapper.base.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.tyt.plat.entity.base.FeedbackUserAppeal.APPEAL_STATUS_DOING;

/**
 * 用户相关的评价,用于评下相关数据操作的聚合
 *
 * <AUTHOR>
 * @since 2023/5/31 下午2:45
 */
@Slf4j
@Component
public class FeedbackManager {

    private final FeedbackUserMapper feedbackUserMapper;
    private final FeedbackUserLabelMapper feedbackUserLabelMapper;
    private final FeedbackUserNegativeLabelConfigMapper feedbackUserNegativeLabelConfigMapper;
    private final FeedbackUserOperateLogMapper feedbackUserOperateLogMapper;
    private final FeedbackLabelMapper feedbackLabelMapper;

    private final FeedbackUserAppealMapper feedbackUserAppealMapper;

    private final FeedbackUserAppealInfoMapper feedbackUserAppealInfoMapper;

    public FeedbackManager(FeedbackUserMapper feedbackUserMapper, FeedbackUserLabelMapper feedbackUserLabelMapper,
                           FeedbackUserNegativeLabelConfigMapper feedbackUserNegativeLabelConfigMapper,
                           FeedbackUserOperateLogMapper feedbackUserOperateLogMapper,
                           FeedbackUserAppealMapper feedbackUserAppealMapper,
                           FeedbackUserAppealInfoMapper feedbackUserAppealInfoMapper,
                           FeedbackLabelMapper feedbackLabelMapper) {
        this.feedbackUserMapper = feedbackUserMapper;
        this.feedbackUserLabelMapper = feedbackUserLabelMapper;
        this.feedbackUserNegativeLabelConfigMapper = feedbackUserNegativeLabelConfigMapper;
        this.feedbackUserOperateLogMapper = feedbackUserOperateLogMapper;
        this.feedbackLabelMapper = feedbackLabelMapper;
        this.feedbackUserAppealMapper = feedbackUserAppealMapper;
        this.feedbackUserAppealInfoMapper = feedbackUserAppealInfoMapper;
    }

    private void saveFeedbackOperateLog(FeedbackUser feedbackUser, List<FeedbackUserLabel> oldLabels,
                                        FeedbackUser update, List<FeedbackUserLabel> newLabels, Integer operateType) {
        Date now = new Date();
        FeedbackUserOperateLog operateLog = FeedbackUserOperateLog.builder()
                .createTime(now)
                .feedbackId(feedbackUser.getId())
                .updateTime(now)
                .preOperateType(feedbackUser.getFeedbackType())
                .postOperateType(update.getFeedbackType())
                .preOperateLabel(JSON.toJSONString(oldLabels))
                .postOperateLabel(JSON.toJSONString(newLabels))
                .preOperateComment(feedbackUser.getComment())
                .postOperateComment(update.getComment())
                .operateType(operateType).build();
        feedbackUserOperateLogMapper.insertSelective(operateLog);
    }

    @Caching(
            cacheable = {
                    @Cacheable(cacheNames = "''.format('tyt:plat:feedback:info:count:post:%s-%s',#postUserId,#postUserType)",
                            cacheResolver = "spelCacheResolver",
                            condition = "#postUserId != null"),
                    @Cacheable(cacheNames = "''.format('tyt:plat:feedback:info:count:receive:%s-%s',#receiveUserId,#receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            condition = "#receiveUserId != null")
            }
    )
    public FeedbackCountDTO getFeedbackCount(Long postUserId,
                                             Integer postUserType,
                                             Long receiveUserId,
                                             Integer receiveUserType,
                                             Integer isValid) {
        Date oneYearAgo = DateUtils.addYears(new Date(), -1);

        Long negative = feedbackUserMapper.count(postUserId, postUserType, receiveUserId, receiveUserType,
                FeedbackTypeEnum.NEGATIVE.getCode(), false, isValid, oneYearAgo);

        Long positive = feedbackUserMapper.count(postUserId, postUserType, receiveUserId, receiveUserType,
                FeedbackTypeEnum.POSITIVE.getCode(), false, isValid, oneYearAgo);

        Long neutral = feedbackUserMapper.count(postUserId, postUserType, receiveUserId, receiveUserType,
                FeedbackTypeEnum.NEUTRAL.getCode(), false, isValid, oneYearAgo);

        return new FeedbackCountDTO(negative, positive, neutral);
    }

    @Caching(
            evict = {
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:count:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:count:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:labelSum:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:labelSum:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:list:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:list:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true)
            }
    )
    @Transactional(transactionManager = "mybatisTransactionManager")
    public void saveFeedback(FeedbackUser feedbackUser, List<Long> labels) {
        // 插入主表数据
        feedbackUserMapper.insertSelective(feedbackUser);
        // 保存标签
        saveFeedbackLabels(labels, feedbackUser);
        // 保存差评标签配置
        saveNegativeLabel(feedbackUser);
    }

    private List<FeedbackUserLabel> saveFeedbackLabels(List<Long> labels, FeedbackUser feedbackUser) {
        List<FeedbackUserLabel> labelBeans = labels.stream()
                .map(it -> {
                    FeedbackLabel feedbackLabel = feedbackLabelMapper.selectByPrimaryKey(it);
                    return FeedbackUserLabel.builder()
                            .labelId(it)
                            .createTime(feedbackUser.getCreateTime())
                            .updateTime(feedbackUser.getUpdateTime())
                            .labelName(feedbackLabel.getLabelName())
                            .feedbackId(feedbackUser.getId())
                            .postUserId(feedbackUser.getPostUserId())
                            .postUserType(feedbackUser.getPostUserType())
                            .receiveUserId(feedbackUser.getReceiveUserId())
                            .receiveUserType(feedbackUser.getReceiveUserType())
                            .labelFeedbackType(feedbackLabel.getFeedbackType())
                            .receiverHideFlag(feedbackUser.getReceiverHideFlag())
                            .delFlag(false)
                            .tsId(feedbackUser.getTsId())
                            .tsOrderId(feedbackUser.getTsOrderId())
                            .build();
                })
                .collect(Collectors.toList());
        if (!labels.isEmpty()) {
            feedbackUserLabelMapper.insertList(labelBeans);
        }
        return labelBeans;
    }

    private void saveNegativeLabel(FeedbackUser feedbackUser) {
        Long receiveUserId = feedbackUser.getReceiveUserId();
        Integer receiveUserType = feedbackUser.getReceiveUserType();
        // if (feedbackUser.getReceiveUserType() == 1) {
        //     // 仅针对货版
        //     return;
        // }
        FeedbackUserNegativeLabelConfig negativeLabelConfig =
                feedbackUserNegativeLabelConfigMapper.selectByUserId(receiveUserId);
        if (negativeLabelConfig != null) {
            return;
        }

        FeedbackUserNegativeLabelConfig save = FeedbackUserNegativeLabelConfig.builder().userId(receiveUserId)
                .userType(receiveUserType)
                .createTime(feedbackUser.getCreateTime())
                .updateTime(feedbackUser.getUpdateTime())
                .userPhone(feedbackUser.getReceiveUserPhone())
                .isShow(0).build();
        feedbackUserNegativeLabelConfigMapper.insertSelective(save);
    }

    @Transactional(transactionManager = "mybatisTransactionManager")
    @Caching(
            evict = {
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:count:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:count:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:labelSum:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:labelSum:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:list:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:list:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "tyt:plat:feedback:info:labelNames", key = "#feedbackUser.id"),
                    @CacheEvict(cacheNames = "tyt:plat:feedback:info:labels", key = "#feedbackUser.id"),
            }
    )
    public void updateFeedback(FeedbackUser feedbackUser, FeedbackUser update, List<Long> labels) {
        feedbackUserMapper.updateByPrimaryKeySelective(update);

        List<FeedbackUserLabel> oldLabels =
                feedbackUserLabelMapper.selectByByFeedbackIdAndDelFlag(feedbackUser.getId(), false);
        feedbackUserLabelMapper.deleteByFeedbackId(feedbackUser.getId());
        List<FeedbackUserLabel> newLabels = saveFeedbackLabels(labels, update);

        saveFeedbackOperateLog(feedbackUser, oldLabels, update, newLabels, 1);
    }

    @Transactional(transactionManager = "mybatisTransactionManager")
    @Caching(
            evict = {
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:count:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:count:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:labelSum:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:labelSum:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:list:post:%s-%s',#feedbackUser.postUserId,#feedbackUser.postUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "''.format('tyt:plat:feedback:info:list:receive:%s-%s',#feedbackUser.receiveUserId,#feedbackUser.receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            allEntries = true),
                    @CacheEvict(cacheNames = "tyt:plat:feedback:info:labelNames", key = "#feedbackUser.id"),
                    @CacheEvict(cacheNames = "tyt:plat:feedback:info:labels", key = "#feedbackUser.id"),
            }
    )
    public void deleteFeedback(FeedbackUser feedbackUser) {
        Date now = new Date();
        FeedbackUser update = FeedbackUser.builder().id(feedbackUser.getId())
                .updateTime(now)
                .delFlag(true).build();
        feedbackUserMapper.updateByPrimaryKeySelective(update);
        feedbackUserLabelMapper.deleteByFeedbackId(feedbackUser.getId());
    }

    @Caching(
            cacheable = {
                    @Cacheable(cacheNames = "''.format('tyt:plat:feedback:info:labelSum:post:%s-%s',#postUserId,#postUserType)",
                            cacheResolver = "spelCacheResolver",
                            condition = "#postUserId != null"),
                    @Cacheable(cacheNames = "''.format('tyt:plat:feedback:info:labelSum:receive:%s-%s',#receiveUserId,#receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            condition = "#receiveUserId != null")
            }
    )
    public List<FeedbackLabelSumDTO> getFeedbackLabelSum(Long postUserId,
                                                         Integer postUserType,
                                                         Long receiveUserId,
                                                         Integer receiveUserType,
                                                         Integer feedbackType) {
        Date oneYearAgo = DateUtils.addYears(new Date(), -1);
        return feedbackUserLabelMapper.sum(postUserId, postUserType, receiveUserId,
                receiveUserType, oneYearAgo, false, feedbackType);
    }

    @Caching(
            cacheable = {
                    @Cacheable(cacheNames = "''.format('tyt:plat:feedback:info:list:post:%s-%s',#postUserId,#postUserType)",
                            cacheResolver = "spelCacheResolver",
                            condition = "#postUserId != null"),
                    @Cacheable(cacheNames = "''.format('tyt:plat:feedback:info:list:receive:%s-%s',#receiveUserId,#receiveUserType)",
                            cacheResolver = "spelCacheResolver",
                            condition = "#receiveUserId != null")
            }
    )
    public PageData<FeedbackUser> getUserFeedbacks(Long postUserId,
                                                   Integer postUserType,
                                                   Long receiveUserId,
                                                   Integer receiveUserType,
                                                   Integer feedbackType,
                                                   Long labelId,
                                                   Integer pageSize,
                                                   Integer pageNum) {
        Date oneYearAgo = DateUtils.addYears(new Date(), -1);
        CustomPageHelper pageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));
        List<FeedbackUser> feedbackUsers = feedbackUserMapper.selectList(postUserId, postUserType, receiveUserId,
                receiveUserType, oneYearAgo, false, feedbackType, labelId);
        return pageHelper.endPage(feedbackUsers);
    }

    @Cacheable(cacheNames = "tyt:plat:feedback:info:labelNames", key = "#feedbackId")
    public List<String> selectLabelNames(Long feedbackId) {
        return feedbackUserLabelMapper.selectLabelNames(feedbackId, false);
    }

    @Cacheable(cacheNames = "tyt:plat:feedback:info:labels", key = "#feedbackId")
    public List<FeedbackUserLabel> getLabelsByFeedbackId(Long feedbackId) {
        return feedbackUserLabelMapper.selectByByFeedbackIdAndDelFlag(feedbackId, false);
    }

    @Transactional(transactionManager = "mybatisTransactionManager")
    public Long saveFeedbackAppeal(FeedBackUserAppealInfoReq req) {
        Date now = new Date();
        FeedbackUserAppeal appeal = FeedbackUserAppeal.builder()
                .feedbackId(req.getFeedbackId())
                .appealStatus(APPEAL_STATUS_DOING)
                .appealTime(now)
                .createTime(now)
                .modifyTime(now)
                .build();
        //保存申诉主表数据
        feedbackUserAppealMapper.insertSelective(appeal);
        //保存申诉信息数据
        feedbackUserAppealInfoMapper.insertSelective(FeedbackUserAppealInfo.builder()
                .appealId(appeal.getId())
                .appealPicture(req.getAppealPicture())
                .appealReason(req.getAppealReason())
                .appealTime(now)
                .build());
        return appeal.getId();
    }

    @Transactional(transactionManager = "mybatisTransactionManager")
    public Long updateFeedbackAppeal(FeedbackUserAppeal appeal, FeedBackUserAppealInfoReq req) {
        Date now = new Date();
        appeal.setModifyTime(now);
        appeal.setAppealStatus(APPEAL_STATUS_DOING);
        //保存申诉主表数据
        feedbackUserAppealMapper.updateByPrimaryKeySelective(appeal);
        //保存申诉信息数据
        feedbackUserAppealInfoMapper.insertSelective(FeedbackUserAppealInfo.builder()
                .appealId(appeal.getId())
                .appealPicture(req.getAppealPicture())
                .appealReason(req.getAppealReason())
                .appealTime(now)
                .build());
        return appeal.getId();
    }
}
