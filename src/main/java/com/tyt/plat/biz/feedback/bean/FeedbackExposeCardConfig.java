package com.tyt.plat.biz.feedback.bean;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@Slf4j
public class FeedbackExposeCardConfig implements Serializable {

    /**
     * 每月最大赠送次数
     */
    private Integer maxNum = 0;

    /**
     * 好评赠送次数
     */
    private Integer positiveRewardNum = 0;

    /**
     * 中评赠送次数
     */
    private Integer neutralRewardNum = 0;

    /**
     * 差评赠送次数
     */
    private Integer negativeRewardNum = 0;

    public static FeedbackExposeCardConfig fromConfigString(String configString) {
        FeedbackExposeCardConfig feedbackExposeCardConfig = new FeedbackExposeCardConfig();
        if (StringUtils.isNotBlank(configString)) {
            try {
                String[] split = configString.split("[,，]");
                feedbackExposeCardConfig.maxNum = Integer.valueOf(split[0]);
                feedbackExposeCardConfig.positiveRewardNum = Integer.valueOf(split[1]);
                feedbackExposeCardConfig.neutralRewardNum = Integer.valueOf(split[2]);
                feedbackExposeCardConfig.negativeRewardNum = Integer.valueOf(split[3]);
            } catch (Exception e) {
                log.error("评价曝光卡信息转换失败, config:{}", configString, e);
            }
        }
        return feedbackExposeCardConfig;
    }
}
