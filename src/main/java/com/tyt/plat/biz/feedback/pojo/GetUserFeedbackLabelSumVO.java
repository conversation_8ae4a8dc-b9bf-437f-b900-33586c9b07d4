package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetUserFeedbackLabelSumVO implements Serializable {

    /**
     * 标签id
     */
    private Long labelId;
    /**
     * 标签名
     */
    private String labelName;
    /**
     * 标签总数
     */
    private Long total;

    /**
     * 标签类别 1好评 2中评 3差评
     */
    private Integer labelType;
}
