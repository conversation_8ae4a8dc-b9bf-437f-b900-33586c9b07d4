package com.tyt.plat.biz.feedback.pojo;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:24
 */
@Data
public class PostFeedbackReq {

    /**
     * 订单id
     */
    @NotNull(message = "订单id必填")
    private Long orderId;

    /**
     * 评价类别 1好评 2中评 3差评
     */
    @NotNull(message = "评价类别必选")
    @Range(min = 1, max = 3, message = "评价类别有误")
    private Integer feedbackType;

    /**
     * 评价标签，传逗号分隔的标签id
     */
    @NotNull(message = "评价标签必选")
    private String labels;

    /**
     * 备注
     */
    @Size(max = 200, message = "备注最多200字")
    private String comment;
}
