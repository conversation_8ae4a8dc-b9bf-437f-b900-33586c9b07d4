package com.tyt.plat.biz.feedback.manager;

import com.tyt.plat.biz.feedback.pojo.GetFeedbackLabelVO;
import com.tyt.plat.entity.base.FeedbackLabel;
import com.tyt.plat.mapper.base.FeedbackLabelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/6/6 下午1:59
 */
@Component
@Slf4j
public class FeedbackLabelManager {

    private final FeedbackLabelMapper feedbackLabelMapper;

    public FeedbackLabelManager(FeedbackLabelMapper feedbackLabelMapper) {
        this.feedbackLabelMapper = feedbackLabelMapper;
    }

    public List<GetFeedbackLabelVO> getFeedbackLabels(Integer userType, Integer feedbackType) {

        List<FeedbackLabel> feedbackLabelList = feedbackLabelMapper.selectList(userType, feedbackType, true);
        return feedbackLabelList.stream().map(it -> GetFeedbackLabelVO.builder().id(it.getId())
                .labelName(it.getLabelName()).build()).collect(Collectors.toList());
    }

    public Long count(Integer labelType, Integer feedbackType, List<Long> labels) {
        return feedbackLabelMapper.count(labelType, feedbackType, labels);
    }

    public FeedbackLabel getById(Long id) {
        return feedbackLabelMapper.selectByPrimaryKey(id);
    }

    /**
     * 返回评价标签优先级
     *
     * @param labelType    标签类型 1:货对车，2:车对货
     * @param feedbackType 类别 1:好评，2:中评，3:差评
     * @return {"标签id":"优先级"}
     */
    // @Cacheable(cacheNames = "tyt:plat:feedback:info:labelPriority",key = "#labelType+'-'+#feedbackType")
    public Map<Long, Integer> getFeedbackLabelPriority(Integer labelType, Integer feedbackType) {
        List<FeedbackLabel> feedbackLabelList = feedbackLabelMapper.selectList(labelType, feedbackType, true);
        return feedbackLabelList.stream()
                .collect(Collectors.toMap(FeedbackLabel::getId, FeedbackLabel::getShowPriority, (k1, k2) -> k1));
    }
}
