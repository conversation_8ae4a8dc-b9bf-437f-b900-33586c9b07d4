package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetUserFeedbackRatingVO implements Serializable {

    /**
     * 总评价数
     */
    private Long total = 0L;
    /**
     * 差评数
     */
    private Long negative= 0L;
    /**
     * 好评数
     */
    private Long positive= 0L;
    /**
     * 中评数
     */
    private Long neutral= 0L;

    /**
     * 好评率
     */
    private String rating;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像可能为空 有些用户会返回服务器本地路径，有些会返回图片http链接
     */
    private String avatar;
}
