package com.tyt.plat.biz.feedback.manager.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/6/2 上午10:23
 */
@Data
public class FeedbackCountDTO implements Serializable {

    private Long total;
    private Long negative;
    private Long positive;
    private Long neutral;

    public FeedbackCountDTO() {
    }

    public FeedbackCountDTO(Long negative, Long positive, Long neutral) {
        this.negative = negative;
        this.positive = positive;
        this.neutral = neutral;
        this.total = negative + positive + neutral;
    }
}
