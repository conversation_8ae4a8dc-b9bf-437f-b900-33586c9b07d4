package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Builder
@Validated
@AllArgsConstructor
@NoArgsConstructor
public class FeedBackUserAppealInfoReq implements Serializable {

    /**
     * 申诉原因
     */
    @NotNull(message = "申诉原因不能为空")
    @Size(max = 200, message = "申诉原因最多200字")
    private String appealReason;

    /**
     * 申诉图片地址（多张用英文逗号隔开）
     */
    @NotNull(message = "申诉图片不能为空")
    @Size(max = 1024, message = "申诉图片地址过长")
    private String appealPicture;

    /**
     * 评价ID
     */
    @NotNull(message = "评价ID不能为空")
    private Long feedbackId;

}
