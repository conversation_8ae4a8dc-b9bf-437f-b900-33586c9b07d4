package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetUserFeedbackTodoListVO implements Serializable {

    /**
     * id，发表评价的orderId传这个
     */
    private Long id;

    /**
     * 对应的运单id
     */
    private Long tsOrderId;

    /**
     * 对应的货源id
     */
    private Long tsId;

    /**
     * 完成时间
     */
    private Date createTime;

    /**
     * 运单对应展示名称
     */
    private String transportOrderUserName;

    /**
     * 运单对应头像  头像可能为空 有些用户会返回服务器本地路径，有些会返回图片http链接
     */
    private String avatar;

    /**
     * 对应运单出发地
     */
    private String transportOrderStartPoint;

    /**
     * 对应运单目的地
     */
    private String transportOrderDestPoint;

    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 承运车牌
     */
    private String carNo;

    /**
     * 登录人角色: 1.车主 2.司机 3.既是车主也是司机
     */
    private Integer userRole;
}
