package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetUserFeedbackDetailVO implements Serializable {

    /**
     * 评价id
     */
    private Long id;

    /**
     * 评价类别 1好评 2中评 3差评
     */
    private Integer feedbackType;

    /**
     * 评价标签列表
     */
    private List<GetFeedbackLabelVO> labels;

    /**
     * 备注
     */
    private String comment;
}
