package com.tyt.plat.biz.feedback.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PostFeedbackVO implements Serializable {

    /**
     * 发表的评价对应的id
     */
    private Long id;

    /**
     * 发表的评价展示的信息
     */
    private String displayText;
}
