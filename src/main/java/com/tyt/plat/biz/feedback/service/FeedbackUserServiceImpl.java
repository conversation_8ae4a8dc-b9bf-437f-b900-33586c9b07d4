package com.tyt.plat.biz.feedback.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.cache.ConfigServiceImpl;
import com.tyt.common.bean.PageData;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.enums.PermissionGainTypeNewEnum;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.messagecenter.core.enums.NotifyOpenTypeEnum;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NewsMessagePush;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.TytTransportOrders;
import com.tyt.model.TytTransportWaybillEx;
import com.tyt.model.User;
import com.tyt.permission.bean.GoodsType;
import com.tyt.permission.bean.PermissionChangeType;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.aop.abtest.EnableAbTest;
import com.tyt.plat.aop.redislock.RedisLock;
import com.tyt.plat.aop.redislock.RedisLockName;
import com.tyt.plat.biz.feedback.bean.FeedbackExposeCardConfig;
import com.tyt.plat.biz.feedback.constant.FeedbackTypeEnum;
import com.tyt.plat.biz.feedback.enums.OrderStatusEnum;
import com.tyt.plat.biz.feedback.manager.FeedbackLabelManager;
import com.tyt.plat.biz.feedback.manager.FeedbackManager;
import com.tyt.plat.biz.feedback.manager.pojo.FeedbackCountDTO;
import com.tyt.plat.biz.feedback.manager.pojo.FeedbackLabelSumDTO;
import com.tyt.plat.biz.feedback.pojo.*;
import com.tyt.plat.biz.invoice.db.InvoiceDbService;
import com.tyt.plat.commons.model.CheckResult;
import com.tyt.plat.commons.model.PageParameter;
import com.tyt.plat.commons.tools.CustomPageHelper;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.PreConditions;
import com.tyt.util.ReturnCodeConstant;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tyt.plat.biz.feedback.constant.FeedbackTypeEnum.*;
import static com.tyt.plat.biz.feedback.pojo.GetUserFeedbackListVO.RECEIVER_HIDE_FLAG_FALSE;
import static com.tyt.plat.biz.feedback.pojo.GetUserFeedbackListVO.RECEIVER_HIDE_FLAG_TRUE;
import static com.tyt.plat.entity.base.FeedbackUserAppeal.*;


/**
 * <AUTHOR>
 * @since 2023/5/31 下午2:03
 */
@Service
@Slf4j
public class FeedbackUserServiceImpl implements IFeedbackUserService {

    private final FeedbackManager feedbackManager;
    private final TransportOrdersService transportOrdersService;
    private final FeedbackUserMapper feedbackUserMapper;
    private final UserService userService;
    private final FeedbackUserNegativeLabelConfigMapper feedbackUserNegativeLabelConfigMapper;
    private final FeedbackLabelManager feedbackLabelManager;
    private final UserPermissionService userPermissionService;
    private final TytConfigService tytConfigService;
    private final TransportWayBillExService transportWayBillExService;

    private final FeedbackUserAppealMapper feedbackUserAppealMapper;

    private final FeedbackUserAppealInfoMapper feedbackUserAppealInfoMapper;

    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Autowired
    private TytMessageTmplService messageTmplService;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private InvoiceDbService invoiceDbService;

    public FeedbackUserServiceImpl(FeedbackManager feedbackManager,
                                   TransportOrdersService transportOrdersService,
                                   FeedbackUserMapper feedbackUserMapper,
                                   UserService userService,
                                   FeedbackUserNegativeLabelConfigMapper feedbackUserNegativeLabelConfigMapper,
                                   FeedbackLabelManager feedbackLabelManager,
                                   FeedbackUserAppealMapper feedbackUserAppealMapper,
                                   FeedbackUserAppealInfoMapper feedbackUserAppealInfoMapper,
                                   UserPermissionService userPermissionService, TytConfigService tytConfigService, TransportWayBillExService transportWayBillExService) {
        this.feedbackManager = feedbackManager;
        this.transportOrdersService = transportOrdersService;
        this.feedbackUserMapper = feedbackUserMapper;
        this.userService = userService;
        this.feedbackUserNegativeLabelConfigMapper = feedbackUserNegativeLabelConfigMapper;
        this.feedbackLabelManager = feedbackLabelManager;
        this.userPermissionService = userPermissionService;
        this.tytConfigService = tytConfigService;
        this.transportWayBillExService = transportWayBillExService;
        this.feedbackUserAppealMapper = feedbackUserAppealMapper;
        this.feedbackUserAppealInfoMapper = feedbackUserAppealInfoMapper;
    }

    @SneakyThrows
    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    @RedisLock(name = RedisLockName.FEEDBACK, key = "#req.orderId")
    @EnableAbTest(code = "huozhan_feedback", userId = "#userId", types = 1, condition = "#postUserType == 2")
    public FeedbackUser postFeedBack(PostFeedbackReq req, Long userId, Integer postUserType) {

        Integer receiveUserType = postUserType == 1 ? 2 : 1;

        Long orderId = req.getOrderId();

        // 校验标签
        Integer feedbackType = req.getFeedbackType();
        List<Long> labels;
        try {
            labels = Arrays.stream(req.getLabels().split(" *[,，] *")).map(Long::valueOf).collect(Collectors.toList());
        } catch (Exception e) {
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "标签有误"));
        }
        boolean checkLabels = (feedbackType == POSITIVE.getCode() && labels.size() <= 5)
                || (feedbackType == NEUTRAL.getCode() && labels.size() <= 2)
                || (feedbackType == NEGATIVE.getCode() && labels.size() <= 2);
        PreConditions.check(!labels.isEmpty() && checkLabels, ReturnCodeConstant.ERROR, "标签数量不正确");
        PreConditions.check(feedbackLabelManager.count(receiveUserType, feedbackType, labels) == labels.size(),
                ReturnCodeConstant.ERROR, "标签与所选评价类别不符");

        // 校验运单和是否评价过
        CheckResult<String> checkResult = checkCanPostFeedBack(orderId, userId, postUserType);
        PreConditions.check(checkResult.isSuccess(), ReturnCodeConstant.ERROR, checkResult.getMessage());

        TytTransportOrders transportOrders = transportOrdersService.getById(orderId);
        PreConditions.check(transportOrders != null, ReturnCodeConstant.ERROR, "订单不存在");

        // 投诉不展示  1货方责任 2车方责任 3责任不清 4双方无责任
        boolean receiverHideFlag = false;
        int loseEfficacyReason = 0;

        // 只处理中差评
        if (feedbackType == NEUTRAL.getCode() || feedbackType == NEGATIVE.getCode()) {
            TytTransportWaybillEx wayBillEx = transportWayBillExService.getWayBillExByOrderId(transportOrders.getId(), 0);
            if (wayBillEx != null) {
                String exFaultParty = wayBillEx.getExFaultParty();
                if ((postUserType == 1 && (Objects.equals(exFaultParty, "2") || Objects.equals(exFaultParty, "6") || Objects.equals(exFaultParty, "9")))
                        || (postUserType == 2 && (Objects.equals(exFaultParty, "1") || Objects.equals(exFaultParty, "6") || Objects.equals(exFaultParty, "7")))) {
                    receiverHideFlag = true;
                    loseEfficacyReason = 1;
                }
            }
        }

        // 保存评价
        Date now = new Date();

        String postUserPhone = postUserType == 1 ? transportOrders.getPayCellPhone() : transportOrders.getUploadCellphone();

        Long receiveUserId = postUserType == 1 ? transportOrders.getUserId() : transportOrders.getPayUserId();
        String receiveUserPhone = postUserType == 1 ? transportOrders.getUploadCellphone() : transportOrders.getPayCellPhone();

        User postUser = userService.getByUserId(userId);
        User receiveUser = userService.getByUserId(receiveUserId);
        FeedbackUser feedbackUser = FeedbackUser.builder()
                .createTime(now)
                .updateTime(now)
                .postUserId(userId)
                .postUserType(postUserType)
                .postUserPhone(postUserPhone)
                .receiveUserId(receiveUserId)
                .receiveUserType(receiveUserType)
                .receiveUserPhone(receiveUserPhone)
                .delFlag(false)
                .tsOrderId(Long.valueOf(transportOrders.getTsOrderNo()))
                .tsId(transportOrders.getTsId())
                .orderId(orderId)
                .receiverHideFlag(receiverHideFlag)
                .loseEfficacyReason(loseEfficacyReason)
                .postUserAvatar(postUser.getHeadUrl())
                .receiveUserAvatar(receiveUser.getHeadUrl())
                .transportOrderPubUserName(transportOrders.getPubUserName())
                .transportOrderPayUserName(transportOrders.getPayUserName())
                .transportOrderStartPoint(transportOrders.getStartPoint())
                .transportOrderDestPoint(transportOrders.getDestPoint())
                .feedbackType(req.getFeedbackType())
                .feedbackDeadline(DateUtils.addDays(now, 30))
                .needHandleOnDeadline(true)
                .comment(req.getComment())
                .updateTimes(0)
                .build();

        feedbackManager.saveFeedback(feedbackUser, labels);

        if (postUserType == 2) {
            // Redis 保存曝光卡key
            RedisUtil.setObject(getFeedbackPostExposeRedisKey(feedbackUser.getId()),
                    feedbackUser.getPostUserId(),
                    (int) TimeUnit.DAYS.toSeconds(1));
        }
        //如果是好评，发送短信、push、站内信
        if(feedbackType == POSITIVE.getCode()){
            this.sendMultiMessage(receiveUserId, receiveUserType, receiveUserPhone);
        }
        return feedbackUser;
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public Long updateFeedBack(UpdateFeedbackReq req, Long userId, Integer updateUserType) {
        FeedbackUser feedbackUser = feedbackUserMapper.selectByPrimaryKey(req.getId());

        // 是否能修改
        CheckResult<String> checkResult = checkCanUpdateFeedBack(feedbackUser, userId, updateUserType);
        FeedbackUserAppeal appeal = feedbackUserAppealMapper.getAppealByFeedbackId(feedbackUser.getId());

        PreConditions.check(!(appeal!=null
                &&(appeal.getAppealStatus() == APPEAL_STATUS_DOING
                || appeal.getAppealStatus() == APPEAL_STATUS_REJECT
                || appeal.getAppealStatus() == APPEAL_STATUS_SUCCESS)), ReturnCodeConstant.ERROR, "此条评价已申诉，无法操作");
        PreConditions.check(checkResult.isSuccess(), ReturnCodeConstant.ERROR, checkResult.getMessage());

        // 评价标签
        Integer feedbackType = req.getFeedbackType();
        List<Long> labels =
                Arrays.stream(req.getLabels().split(" *[,，] *")).map(Long::valueOf).collect(Collectors.toList());
        boolean checkLabels = (feedbackType == POSITIVE.getCode() && labels.size() <= 5)
                || (feedbackType == NEUTRAL.getCode() && labels.size() <= 2)
                || (feedbackType == NEGATIVE.getCode() && labels.size() <= 2);
        PreConditions.check(!labels.isEmpty() && checkLabels, ReturnCodeConstant.ERROR, "标签数量不正确");
        PreConditions.check(feedbackLabelManager.count(feedbackUser.getReceiveUserType(), feedbackType,
                        labels) == labels.size(),
                ReturnCodeConstant.ERROR, "标签与所选评价类别不符");

        Date now = new Date();
        FeedbackUser update = new FeedbackUser();
        BeanUtils.copyProperties(feedbackUser, update);
        update.setUpdateTime(now);
        update.setUpdateTimes(feedbackUser.getUpdateTimes() + 1);
        update.setFeedbackType(req.getFeedbackType());
        update.setReceiverHideFlag(feedbackType != POSITIVE.getCode() && feedbackUser.getReceiverHideFlag());
        update.setComment(req.getComment());

        feedbackManager.updateFeedback(feedbackUser, update, labels);
        return feedbackUser.getId();
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public Long deleteFeedback(DeleteFeedbackReq req, Long userId, Integer deleteUserType) {
        FeedbackUser feedbackUser = feedbackUserMapper.selectByPrimaryKey(req.getId());

        // 是否能删除
        CheckResult<String> checkResult = checkCanDeleteFeedBack(feedbackUser, userId, deleteUserType);
        PreConditions.check(checkResult.isSuccess(), ReturnCodeConstant.ERROR, checkResult.getMessage());

        feedbackManager.deleteFeedback(feedbackUser);
        return feedbackUser.getId();
    }

    @Override
    public GetUserFeedbackCountVO getUserFeedBackCount(Long userId, Integer userType, Integer operateType) {
        FeedbackCountDTO feedbackCount;
        switch (operateType) {
            case 1: //  1 发表的评价
                feedbackCount = feedbackManager.getFeedbackCount(userId, userType, null, null, 0);
                break;
            case 2: // 2 收到的评价
                feedbackCount = feedbackManager.getFeedbackCount(null, null, userId, userType, 0);
                break;
            default:
                feedbackCount = new FeedbackCountDTO(0L, 0L, 0L);
        }

        return GetUserFeedbackCountVO.builder().total(feedbackCount.getTotal())
                .positive(feedbackCount.getPositive())
                .negative(feedbackCount.getNegative())
                .neutral(feedbackCount.getNeutral()).build();
    }

    @Override
    @SneakyThrows
    public GetUserFeedbackRatingVO getUserFeedBackRating(Long userId, Integer userType) {
        FeedbackCountDTO feedbackCount = feedbackManager.getFeedbackCount(null, null, userId, userType, 1);

        Long negative = feedbackCount.getNegative();
        Long positive = feedbackCount.getPositive();
        Long neutral = feedbackCount.getNeutral();
        long total = negative + positive + neutral;
        String rating = positive == 0 ? "0" : BigDecimal.valueOf(positive)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(total), 0, RoundingMode.HALF_UP)
                .toString();

        User user = userService.getByUserId(userId);

        String cellPhone = user.getCellPhone();
        if (StringUtils.isNotBlank(cellPhone)) {
            cellPhone = cellPhone.replaceFirst("(\\d{3})\\d*(\\d{4})", "$1****$2");
        }
        return GetUserFeedbackRatingVO.builder()
                .phone(cellPhone)
                //    .avatar(user.getHeadUrl())
                .positive(feedbackCount.getPositive())
                .neutral(feedbackCount.getNeutral())
                .negative(negative)
                .total(total)
                .rating(rating)
                .build();
    }

    @Override
    public List<GetUserFeedbackLabelSumVO> getUserFeedbackLabelSum(Integer feedbackType, Integer operateType,
                                                                   Long userId,
                                                                   Integer userType) {

        List<FeedbackLabelSumDTO> labels;
        switch (operateType) {
            case 1: //  1 发表的评价
                labels = feedbackManager.getFeedbackLabelSum(userId, userType, null, null, feedbackType);
                break;
            case 2: // 2 收到的评价
                labels = feedbackManager.getFeedbackLabelSum(null, null, userId, userType, feedbackType);
                break;
            default:
                labels = Collections.emptyList();
        }

        return labels.stream().map(it -> GetUserFeedbackLabelSumVO.builder()
                .labelId(it.getLabelId())
                .labelName(it.getLabelName())
                .labelType(it.getLabelType())
                .total(it.getTotal())
                .build()
        ).collect(Collectors.toList());
    }

    @Override
    public PageData<GetUserFeedbackListVO> getUserFeedbackList(Integer feedbackType, Integer operateType, Long userId,
                                                               Integer userType, Long labelId, Integer pageNum,
                                                               Integer pageSize) {

        PageData<FeedbackUser> feedbacks;
        switch (operateType) {
            case 1: //  1 发表的评价
                feedbacks = feedbackManager.getUserFeedbacks(userId, userType, null, null, feedbackType, labelId,
                        pageSize, pageNum);
                break;
            case 2: // 2 收到的评价
                feedbacks = feedbackManager.getUserFeedbacks(null, null, userId, userType, feedbackType, labelId,
                        pageSize, pageNum);
                break;
            default:
                feedbacks = new PageData<>(Collections.emptyList());
        }

        List<GetUserFeedbackListVO> collect = new ArrayList<>();
        if (feedbacks.getList() != null && !feedbacks.getList().isEmpty()) {
            List<Long> idList2Feedback = feedbacks.getList().stream().map(FeedbackUser::getId).collect(Collectors.toList());
            List<FeedbackUserAppeal> appealList2Feedback = feedbackUserAppealMapper.getAppealByFeedBackIdList(idList2Feedback);
            Map<Long, FeedbackUserAppeal> appealMap = appealList2Feedback.stream().collect(Collectors.toMap(FeedbackUserAppeal::getFeedbackId, order -> order, (key, value) -> key));
            List<Long> idList = feedbacks.getList().stream().map(FeedbackUser::getOrderId).collect(Collectors.toList());
            List<MybatisTytTransportOrders> ordersByIdList = transportOrdersService.getOrdersByIdList(idList);
            Map<Long, MybatisTytTransportOrders> orderMap = ordersByIdList.stream().collect(Collectors.toMap(MybatisTytTransportOrders::getId, order -> order, (key, value) -> key));
            for (FeedbackUser it : feedbacks.getList()) {
                GetUserFeedbackListVO.GetUserFeedbackListVOBuilder builder = GetUserFeedbackListVO.builder();
                MybatisTytTransportOrders order = orderMap.get(it.getOrderId());
                if (order!=null) {
                    builder.taskContent(order.getTaskContent());
                    String tailCityNo = order.getTailCity() == null ? "" : "·" + order.getTailCity() + order.getTailNo();
                    tailCityNo = order.getHeadCity() == null ? null :
                            (order.getHeadCity() + order.getHeadNo() + tailCityNo);
                    builder.carNo(tailCityNo);
                }

                FeedbackUserAppeal appeal = appealMap.get(it.getId());
                if (appeal != null) {
                    builder.appealStatus(appeal.getAppealStatus())
                            .rejectReason(appeal.getRejectReason())
                            .feedbackAppealId(appeal.getId());
                }

                collect.add(builder
                        .id(it.getId())
                        .tsOrderId(it.getTsOrderId())
                        .tsId(it.getTsId())
                        .createTime(it.getCreateTime())
                        .transportOrderStartPoint(it.getTransportOrderStartPoint())
                        .transportOrderDestPoint(it.getTransportOrderDestPoint())
                        .feedbackType(it.getFeedbackType())
                        .transportOrderUserName(userType == 1 ? it.getTransportOrderPubUserName() :
                                it.getTransportOrderPayUserName())
                        .labelNames(feedbackManager.selectLabelNames(it.getId()))
                        .canEdit(checkCanUpdateFeedBack(it, userId, userType).isSuccess())
                        .canDelete(checkCanDeleteFeedBack(it, userId, userType).isSuccess())
                        .loseEfficacyReason(it.getLoseEfficacyReason())
                        .receiverHideFlag(Boolean.TRUE.equals(it.getReceiverHideFlag()) ? RECEIVER_HIDE_FLAG_FALSE : RECEIVER_HIDE_FLAG_TRUE)
                        .build());
            }
        }
        return feedbacks.covert(collect);
    }

    @Override
    public UserFeedbackRatingAndLabelDTO getUserFeedbackRatingAndLabel(Long userId, Integer userType) {
        FeedbackCountDTO feedbackCount = feedbackManager.getFeedbackCount(null, null, userId, userType, 1);

        Long total = feedbackCount.getTotal();
        Long negative = feedbackCount.getNegative();
        Long positive = feedbackCount.getPositive();
        Long neutral = feedbackCount.getNeutral();
        BigDecimal rating = positive == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(positive)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(negative + positive + neutral), 0, RoundingMode.HALF_UP);

        // 如果好评率>=80%，按优先级返回一条好评标签
        List<String> positiveLabels;
        if (rating.compareTo(BigDecimal.valueOf(80)) >= 0) {
            List<FeedbackLabelSumDTO> feedbackLabelSum = feedbackManager.getFeedbackLabelSum(null, null,
                    userId, userType, POSITIVE.getCode());
            Map<Long, Integer> feedbackLabelPriorityMap = feedbackLabelManager.getFeedbackLabelPriority(userType, POSITIVE.getCode());
            positiveLabels = feedbackLabelSum.stream()
                    .sorted(Comparator.comparingInt(t -> feedbackLabelPriorityMap.getOrDefault(t.getLabelId(), 100)))
                    .limit(1).map(FeedbackLabelSumDTO::getLabelName).collect(Collectors.toList());
        } else {
            positiveLabels = Collections.emptyList();
        }

        List<String> negativeLabels;
        FeedbackUserNegativeLabelConfig negativeLabelConfig =
                feedbackUserNegativeLabelConfigMapper.selectByUserId(userId);
        if (negativeLabelConfig != null
        && Objects.equals(negativeLabelConfig.getIsShow(), 1)
        && DateUtils.addDays(negativeLabelConfig.getUpdateTime() == null ? new Date() : negativeLabelConfig.getUpdateTime(), 7).after(new Date())) {
            FeedbackLabel label = feedbackLabelManager.getById(negativeLabelConfig.getShowFeedbackLabelId());
            negativeLabels = label == null ? Collections.emptyList() : Collections.singletonList(label.getLabelName());
        } else {
            negativeLabels = Collections.emptyList();
        }

        return UserFeedbackRatingAndLabelDTO.builder()
                .rating(rating.toString())
                .positiveCount(positive)
                .positiveLabels(positiveLabels)
                .negativeLabels(negativeLabels)
                .total(total)
                .build();
    }

    private CheckResult<String> checkCanDeleteFeedBack(FeedbackUser feedbackUser, Long userId, int updateUserType) {
        CheckResult<String> result = new CheckResult<>(true);
        if (feedbackUser == null) {
            result.setSuccess(false);
            result.setMessage("评价不存在");
            return result;
        }

        if (Objects.equals(feedbackUser.getDelFlag(), true)) {
            result.setSuccess(false);
            result.setMessage("评价已被删除");
            return result;
        }

        if (Objects.equals(feedbackUser.getFeedbackType(), 1)) {
            result.setSuccess(false);
            result.setMessage("只能删除中评或差评");
            return result;
        }

        if (!Objects.equals(feedbackUser.getPostUserId(), userId) || feedbackUser.getPostUserType() != updateUserType) {
            result.setSuccess(false);
            result.setMessage("没有删除权限");
            return result;
        }

        if (new Date().after(feedbackUser.getFeedbackDeadline())) {
            result.setSuccess(false);
            result.setMessage("已过可操作评价时限");
            return result;
        }

        return result;
    }

    private CheckResult<String> checkCanUpdateFeedBack(FeedbackUser feedbackUser, Long userId, int updateUserType) {
        CheckResult<String> result = new CheckResult<>(true);

        if (feedbackUser == null || userId == null) {
            result.setSuccess(false);
            return result;
        }

        if (feedbackUser == null) {
            result.setSuccess(false);
            result.setMessage("评价不存在");
            return result;
        }

        if (Objects.equals(feedbackUser.getDelFlag(), true)) {
            result.setSuccess(false);
            result.setMessage("评价已被删除");
            return result;
        }

        if (Objects.equals(feedbackUser.getFeedbackType(), 1)) {
            result.setSuccess(false);
            result.setMessage("只能修改中评或差评");
            return result;
        }

        if (feedbackUser.getUpdateTimes() > 0) {
            result.setSuccess(false);
            result.setMessage("只能修改一次");
            return result;
        }

        if (!Objects.equals(feedbackUser.getPostUserId(), userId) || feedbackUser.getPostUserType() != updateUserType) {
            result.setSuccess(false);
            result.setMessage("没有修改权限");
            return result;
        }

        if (new Date().after(feedbackUser.getFeedbackDeadline())) {
            result.setSuccess(false);
            result.setMessage("已过可操作评价时限");
            return result;
        }

        return result;
    }

    @Override
    @EnableAbTest(code = "huozhan_feedback", userId = "#userId", types = 1, condition = "#postUserType == 2")
    public CheckResult<String> checkCanPostFeedBack(Long orderId, Long userId, int postUserType) throws Exception {
        CheckResult<String> result = new CheckResult<>(true);

        if (orderId == null || userId == null) {
            result.setSuccess(false);
            return result;
        }

        // 校验货源
        TytTransportOrders transportOrders = transportOrdersService.getById(orderId);

        if (transportOrders == null) {
            result.setSuccess(false);
            result.setMessage("运单不存在");
            return result;
        }

        //能否评价与运单状态有关系，与订金状态无关
        if(transportOrders.getOrderNewStatus() == null
        || transportOrders.getOrderNewStatus() == OrderStatusEnum.WAIT_RECEIVE_ORDERS.getCode()
        || transportOrders.getOrderNewStatus() == OrderStatusEnum.WAIT_SIGN.getCode()
        || transportOrders.getOrderNewStatus() == OrderStatusEnum.WAIT_LOADED.getCode()){
            result.setSuccess(false);
            result.setMessage("无法评价未完成运单");
            return result;
        }

        if (!Objects.equals(userId, postUserType == 1|| postUserType == 4 ? transportOrders.getPayUserId() : transportOrders.getUserId())) {
            result.setSuccess(false);
            result.setMessage("无法评价他人运单");
            return result;
        }

        if (transportOrders.getMtime() != null && new Date().after(DateUtils.addDays(transportOrders.getMtime(), 30))) {
            result.setSuccess(false);
            result.setMessage("已过评价时限");
            return result;
        }

        // 校验是否评价过
        FeedbackUser countQuery =
                FeedbackUser.builder().orderId(orderId).tsId(transportOrders.getTsId()).postUserId(userId).postUserType(postUserType).build();
        int count = feedbackUserMapper.selectCount(countQuery);
        if (count != 0) {
            result.setSuccess(false);
            result.setMessage("已评价过该运单");
            return result;
        }

        return result;
    }

    @Override
    public List<GetFeedbackLabelVO> getFeedbackLabels(int userType, Integer feedbackType) {
        // 车版差货标签，或版查车标签
        Integer queryUserType = userType == 1 ? 2 : 1;
        return feedbackLabelManager.getFeedbackLabels(queryUserType, feedbackType);
    }

    @Override
    public PageData<GetUserFeedbackTodoListVO> getUserFeedbackTodoList(Long userId, int userType, Integer pageNum,
                                                                       Integer pageSize) {
        CustomPageHelper pageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));

        Long pubUserId = null;
        Long payUserId = null;
        if (userType == 1) {
            payUserId = userId;
        } else {
            pubUserId = userId;
        }
        List<MybatisTytTransportOrders> orders = transportOrdersService.getUnPostFeedbackOrders(pubUserId, payUserId);

        List<GetUserFeedbackTodoListVO> collect = orders.stream().map(
                it -> {
                    try {
                        String tailCityNo = it.getTailCity() == null ? "" : "·" + it.getTailCity() + it.getTailNo();
                        return GetUserFeedbackTodoListVO.builder()
                                .id(it.getId())
                                .tsOrderId(Long.valueOf(it.getTsOrderNo()))
                                .tsId(it.getTsId())
                                .createTime(it.getCreateTime())
                                .transportOrderUserName(userType == 1 ? it.getPubUserName() : it.getPayUserName())
                                .transportOrderStartPoint(it.getStartPoint())
                                .transportOrderDestPoint(it.getDestPoint())
                                .createTime(it.getMtime())
                                .taskContent(it.getTaskContent())
                                .carNo(it.getHeadCity() == null ? null : it.getHeadCity() + it.getHeadNo() + tailCityNo)
                                .userRole(determineUserRole(userId,it))
                                .build();
                    } catch (Exception e) {
                        log.error("", e);
                        return null;
                    }
                }
        ).filter(Objects::nonNull).collect(Collectors.toList());

        return pageHelper.endPage(collect);
    }

    /**
     * 返回的角色
     * @param userId
     * @param tytTransportOrders 运单
     * @return Integer 登录人的角色
     */
    private Integer determineUserRole(Long userId,MybatisTytTransportOrders tytTransportOrders) {
        List<TytInvoiceDriver> invoiceDriverList = invoiceDbService.getInvoiceDriverListByDriverUserId(userId);
        //使用optional判invoiceDriverList为空,转换成空map 不空则转换成map
        Map<Long, TytInvoiceDriver> invoiceDriverMap = Optional.ofNullable(invoiceDriverList)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(TytInvoiceDriver::getDriverUserId, Function.identity(),(k1, k2) -> k1));

        TytInvoiceDriver tytInvoiceDriver = invoiceDriverMap.get(tytTransportOrders.getDriverId());
        if(Objects.isNull(tytInvoiceDriver) ){
            return 1;
        }
        return getUserRole(userId,tytInvoiceDriver.getDriverUserId(),tytTransportOrders.getPayUserId());

    }


    /**
     * 判断登录人角色方法: 1.车主 2.司机 3.既是车主也是司机
     * @param userId
     * @param driverUserId
     * @param payUserId
     * @return
     */
    public static Integer getUserRole(Long userId, Long driverUserId, Long payUserId) {
        if (userId != null && driverUserId != null && payUserId != null
                && userId.longValue() == payUserId.longValue()
                && userId.longValue() != driverUserId.longValue()) {
            //车主：登录人的用户ID=订单的支付人的用户ID≠订单的承运司机的用户ID
            return 1;
        } else if (userId != null && driverUserId != null && payUserId != null
                && userId.longValue() == driverUserId.longValue()
                && userId.longValue() != payUserId.longValue()) {
            //司机：登录人的用户ID=订单的承运司机的用户ID≠订单的支付人的用户ID
            return 2;
        } else if (userId != null && driverUserId != null && payUserId != null
                && userId.longValue() == driverUserId.longValue()
                && userId.longValue() == payUserId.longValue()) {
            //车主也是司机：登录人的用户ID=订单的支付人的用户ID=订单的承运司机的用户ID
            return 3;
        } else {
            return 1;
        }
    }

    /**
     * 获取用户待评价订单数量
     * @param userId
     * @param userType
     * @return
     */
    @Override
    public Integer getUserFeedbackTodoCount(Long userId, int userType) {
        Integer feedBackTodoCount = 0;

        Long pubUserId = null;
        Long payUserId = null;
        if (userType == 1) {
            payUserId = userId;
        } else {
            pubUserId = userId;
        }
        List<MybatisTytTransportOrders> orders = transportOrdersService.getUnPostFeedbackOrders(pubUserId, payUserId);
        if(CollectionUtil.isNotEmpty(orders)){
            feedBackTodoCount = orders.size();
        }
        return feedBackTodoCount;
    }

    @Override
    @RedisLock(name = RedisLockName.FEEDBACK, key = "#req.id")
    public ClaimExposureCardRewardVO claimExposureCardReward(ClaimExposureCardReq req, Long userId, int userType) {
        FeedbackUser feedbackUser = feedbackUserMapper.selectByPrimaryKey(req.getId());
        PreConditions.check(feedbackUser != null, ReturnCodeConstant.ERROR, "评价信息不存在");

        // 发布标记
        Long postUserId = RedisUtil.getObject(getFeedbackPostExposeRedisKey(req.getId()));

        PreConditions.check((Objects.equals(userId, postUserId)),
                ReturnCodeConstant.ERROR, "已经领取或超过领取时限");

        // 获取配置
        String stringValue = tytConfigService.getStringValue("tyt:feedback:exposeCard:rewardNum");
        FeedbackExposeCardConfig feedbackExposeCardConfig =
                FeedbackExposeCardConfig.fromConfigString(stringValue);


        String claimedStr = RedisUtil.getMapValue(getFeedbackExposeClaimRedisKey(), userId.toString());

        Integer claimedNum;
        if (StringUtils.isBlank(claimedStr)) {
            claimedNum = 0;
        } else {
            claimedNum = Integer.valueOf(claimedStr);
        }

        // 不能领了
        if (feedbackExposeCardConfig.getMaxNum() <= claimedNum) {
            throw TytException.createException(new ResponseCode(500, "已超过曝光卡每月领取上限"));
        }

        Integer exposureCardNum = getExposeClaimNum(feedbackUser.getFeedbackType(), feedbackExposeCardConfig);
        try {
            // 赠送n次
            for (int i = 0; i < exposureCardNum; i++) {
                userPermissionService.giveGoodsCard(userId, GoodsType.货源曝光权益7天1次, PermissionChangeType.赠送,
                        "评价赠送曝光卡", PermissionGainTypeNewEnum.评论赠送);
            }
        } catch (Exception e) {
            log.error("评价-用户曝光卡赠送失败, feedback:{}", feedbackUser, e);
        }

        claimedNum += exposureCardNum;

        RedisUtil.del(getFeedbackPostExposeRedisKey(feedbackUser.getId()));
        RedisUtil.mapPut(getFeedbackExposeClaimRedisKey(), userId.toString(), claimedNum.toString());

        try (Jedis jedis = RedisUtil.getJedis()) {
            Long ttl = jedis.ttl(getFeedbackExposeClaimRedisKey());
            // 设置过期时间
            if (ttl != null && ttl < 0) {
                // 获取当前日期
                LocalDate today = LocalDate.now();
                // 获取下个月第一天
                LocalDate nextFirstDay = today.with(TemporalAdjusters.firstDayOfNextMonth());

                // 转换为时间戳
                long timestamp = LocalDateTime.of(nextFirstDay, LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault())
                        .toInstant()
                        .toEpochMilli();
                jedis.expireAt(getFeedbackExposeClaimRedisKey(), TimeUnit.MILLISECONDS.toSeconds(timestamp));
            }
        }

        return ClaimExposureCardRewardVO.builder().exposureCardNum(exposureCardNum).build();
    }

    private static Integer getExposeClaimNum(Integer feedbackType, FeedbackExposeCardConfig feedbackExposeCardConfig) {
        Integer exposureCardNum;
        // 还能领
        if (POSITIVE.getCode() == feedbackType) {
            exposureCardNum = feedbackExposeCardConfig.getPositiveRewardNum();
        } else if (NEUTRAL.getCode() == feedbackType) {
            exposureCardNum = feedbackExposeCardConfig.getNeutralRewardNum();
        } else if (NEGATIVE.getCode() == feedbackType) {
            exposureCardNum = feedbackExposeCardConfig.getNegativeRewardNum();
        } else {
            exposureCardNum = 0;
        }

        return exposureCardNum;
    }

    @Override
    public GetExposureCardRewardInfoVO getExposureCardRewardInfo(Long feedbackId, Long userId, int userType) {
        int exposureCardNum;

        FeedbackUser feedbackUser = feedbackUserMapper.selectByPrimaryKey(feedbackId);

        // 发布标记
        Long postUserId = RedisUtil.getObject(getFeedbackPostExposeRedisKey(feedbackId));

        // 校验评价
        if (feedbackUser == null || !Objects.equals(userId, postUserId)) {
            exposureCardNum = 0;
        } else {
            // 获取配置
            String stringValue = tytConfigService.getStringValue("tyt:feedback:exposeCard:rewardNum");
            FeedbackExposeCardConfig feedbackExposeCardConfig =
                    FeedbackExposeCardConfig.fromConfigString(stringValue);

            String claimedNum = RedisUtil.getMapValue(getFeedbackExposeClaimRedisKey(), userId.toString());

            if (StringUtils.isBlank(claimedNum) || feedbackExposeCardConfig.getMaxNum() > Integer.parseInt(claimedNum)) {
                // 还能领
                exposureCardNum = getExposeClaimNum(feedbackUser.getFeedbackType(), feedbackExposeCardConfig);
            } else {
                exposureCardNum = 0;
            }
        }

        return GetExposureCardRewardInfoVO.builder().exposureCardNum(exposureCardNum).build();
    }

    @Override
    public GetUserFeedbackDetailVO getFeedBackDetail(Long feedbackId) {
        FeedbackUser feedbackUser = feedbackUserMapper.selectByPrimaryKey(feedbackId);

        PreConditions.check(feedbackUser != null, ReturnCodeConstant.ERROR, "评价不存在");
        PreConditions.check(!feedbackUser.getDelFlag(), ReturnCodeConstant.ERROR, "评价不存在");

        List<FeedbackUserLabel> feedbackLabels = feedbackManager.getLabelsByFeedbackId(feedbackId);
        List<GetFeedbackLabelVO> collect = feedbackLabels.stream().map(it -> GetFeedbackLabelVO.builder()
                .id(it.getLabelId())
                .labelName(it.getLabelName()).build()).collect(Collectors.toList());

        return GetUserFeedbackDetailVO.builder()
                .id(feedbackUser.getId())
                .feedbackType(feedbackUser.getFeedbackType())
                .comment(feedbackUser.getComment())
                .labels(collect)
                .build();
    }

    @Override
    public CheckInfoFeeExistVO checkInfoFeeExist(Long orderId, Long userId, int userType) {
        boolean exist = false;

        TytTransportOrders transportOrders = transportOrdersService.getById(orderId);

        if (transportOrders != null) {
            if (userType == 1) {
                // 车
                exist = transportOrders.getCarShow() == 0 && Objects.equals(transportOrders.getPayUserId(), userId);
            } else if (userType == 2) {
                // 货
                exist = transportOrders.getGoodsShow() == 0 && Objects.equals(transportOrders.getUserId(), userId);
            }
        }

        return CheckInfoFeeExistVO.builder().exist(exist).build();
    }

    @Override
    public FeedBackUserAppealInfoVO getAppealInfoByAppealId(Long feedbackAppealId) {
        FeedbackUserAppeal appeal = feedbackUserAppealMapper.selectByPrimaryKey(feedbackAppealId);
        PreConditions.check(appeal != null,
                ReturnCodeConstant.ERROR, "评价申诉信息不存在！");
        FeedbackUserAppealInfo info = feedbackUserAppealInfoMapper.getInfoByAppealId(feedbackAppealId);
        if (info==null) {
            return null;
        }
        return FeedBackUserAppealInfoVO.builder().appealReason(info.getAppealReason()).build();
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    @RedisLock(name = RedisLockName.FEEDBACK_APPEAL, key = "#req.feedbackId")
    public FeedBackUserAppealInfoResp appeal(FeedBackUserAppealInfoReq req) {
        FeedbackUser feedbackUser = feedbackUserMapper.selectByPrimaryKey(req.getFeedbackId());
        PreConditions.check(feedbackUser != null
                && feedbackUser.getFeedbackType() > FeedbackTypeEnum.POSITIVE.getCode(),
                ReturnCodeConstant.ERROR, "评价信息不存在或评价类型不支持申诉！");

        FeedbackUserAppeal appeal = feedbackUserAppealMapper.getAppealByFeedbackId(req.getFeedbackId());
        PreConditions.check(appeal == null || appeal.getAppealStatus() == APPEAL_STATUS_REJECT, ReturnCodeConstant.ERROR, "重复申诉或申诉状态不合法！");
        if (appeal==null) {
            Long appealId = feedbackManager.saveFeedbackAppeal(req);
            return FeedBackUserAppealInfoResp.builder().appealId(appealId).build();
        } else {
            Long appealId = feedbackManager.updateFeedbackAppeal(appeal, req);
            return FeedBackUserAppealInfoResp.builder().appealId(appealId).build();
        }
    }

    private String getFeedbackPostExposeRedisKey(Long id) {
        return "tyt:plat:feedback:post:exposeCard:postId:" + id;
    }

    private String getFeedbackExposeClaimRedisKey() {
        return "tyt:plat:feedback:exposeCard:claim";
    }

    /**
     * 发送短信、push、站内信
     * <AUTHOR>
     * @param userId
     * @param userType
     * @param userPhone
     * @return void
     */
    @Override
    public void sendMultiMessage(Long userId, Integer userType, String userPhone) {
        log.info("车货互评，发送短信、PUSH和站内信，用户评价记录信息为：userId:{},userType:{},userPhone:{}", userId, userType, userPhone);
        // 车货评价，短信内容模板
        String feedbackUserPostSmsTmpl = messageTmplService.getSmsTmpl("feedback_user_post_sms_tmpl");
        // 车货评价，push内容模板
        String feedbackUserPostPushTmpl = messageTmplService.getSmsTmpl("feedback_user_post_push_tmpl");
        // 车货评价，站内信内容模板
        String feedbackUserPostNewsTmpl = messageTmplService.getSmsTmpl("feedback_user_post_news_tmpl");

        //短信内容
        String smsContent = StringUtils.replaceEach(feedbackUserPostSmsTmpl, new String[] {}, new String[] {});
        //push内容
        String pushContent = StringUtils.replaceEach(feedbackUserPostPushTmpl, new String[] {}, new String[] {});
        //站内信内容
        String newsContent = StringUtils.replaceEach(feedbackUserPostNewsTmpl, new String[] {}, new String[] {});

        //发送短信、PUSH和站内信
        String remark = "评价提醒";

        ShortMessageBean shortMessage = new ShortMessageBean();
        shortMessage.setCellPhone(userPhone);
        shortMessage.setRemark(remark);
        MessagePushBase pushBase = new MessagePushBase();
        pushBase.addUserId(userId);
        pushBase.setRemarks(remark);
        shortMessage.setContent(smsContent);

        //push推送通知、站内信消息内容
        pushBase.setTitle("您的订单已收到好评，快来看看！");
        if(userType != null && userType == 1) {
            pushBase.setPushType(1, 0);
        }else if(userType != null && userType == 2) {
            pushBase.setPushType(0, 1);
        }
        pushBase.setContent(pushContent);

        NotifyMessagePush notifyMessagePush = NotifyMessagePush.createByPushBase(pushBase);
        notifyMessagePush.setOpenType(NotifyOpenTypeEnum.link.getCode());
        notifyMessagePush.setTypeCode(20303);

        NewsMessagePush newsMessagePush = NewsMessagePush.createByPushBase(pushBase);
        //标题
        if(userType != null && userType == 1) {
            newsMessagePush.setTitle("您的订单被评价，快来看看！");
        }else if(userType != null && userType == 2) {
            newsMessagePush.setTitle("您的订单被好评了，快来看看！");
        }
        //主题
        newsMessagePush.setSummary("通过真实评价帮助更多用户甄选优质服务");
        //内容
        newsMessagePush.setContent(newsContent);

        //车货评价短信发送开关(0:关 1:开)
        Integer feedbackUserPostSmsSwitch = tytConfigService.getIntValue("feedback_user_post_sms_switch", 1);
        if(feedbackUserPostSmsSwitch != null && feedbackUserPostSmsSwitch == 0) {
            shortMessage = null;
        }
        log.info("车货互评，发送消息实体：{},{},{}", JSON.toJSONString(shortMessage), JSON.toJSONString(newsMessagePush), JSON.toJSONString(notifyMessagePush));
        messageCenterPushService.sendMultiMessage(shortMessage, newsMessagePush, notifyMessagePush);
    }
}
