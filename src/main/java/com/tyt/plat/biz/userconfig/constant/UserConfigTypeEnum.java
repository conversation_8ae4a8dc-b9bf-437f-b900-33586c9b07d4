package com.tyt.plat.biz.userconfig.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/03/05 10:14
 */
@Getter
public enum UserConfigTypeEnum {

    /**
     * 自动扣除抢单豆
     */
    AUTO_DECREASE_DIAL_TIMES("0", Arrays.asList("0", "1"));

    private final String defaultValue;
    private final List<String> valueList;

    UserConfigTypeEnum(String defaultValue, List<String> valueList) {
        this.defaultValue = defaultValue;
        this.valueList = valueList;
    }

    public static boolean contains(String name) {
        return Arrays.stream(values()).anyMatch(it -> StringUtils.equals(name, it.name()));
    }

    public static UserConfigTypeEnum find(String name) {
        return Arrays.stream(values()).filter(it -> StringUtils.equals(name, it.name())).findFirst().orElseThrow(() -> new IllegalArgumentException("参数错误"));
    }
}
