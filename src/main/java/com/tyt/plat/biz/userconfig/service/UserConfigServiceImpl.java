package com.tyt.plat.biz.userconfig.service;

import com.tyt.plat.biz.userconfig.constant.UserConfigTypeEnum;
import com.tyt.plat.biz.userconfig.pojo.UserConfigUpdateReq;
import com.tyt.plat.biz.userconfig.pojo.UserConfigVO;
import com.tyt.plat.entity.base.TytUserConfig;
import com.tyt.plat.mapper.base.TytUserConfigMapper;
import com.tyt.util.PreConditions;
import com.tyt.util.ReturnCodeConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/03/07 15:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserConfigServiceImpl implements UserConfigService {

    private final TytUserConfigMapper userConfigMapper;

    @Override
    public List<UserConfigVO> getUserConfigList(Long userId, List<String> configTypes) {
        if (userId == null || configTypes == null || configTypes.isEmpty()) {
            return Collections.emptyList();
        }

        List<TytUserConfig> tytUserConfigs = userConfigMapper.selectByConfigTypesAndUserId(userId, configTypes);

        Map<String, TytUserConfig> collect = tytUserConfigs.stream().collect(Collectors.toMap(TytUserConfig::getType,
                Function.identity()));

        return configTypes.stream().map(type -> {
            UserConfigVO.UserConfigVOBuilder builder = UserConfigVO.builder();
            TytUserConfig tytUserConfig = collect.get(type);
            if (tytUserConfig != null) {
                builder.type(tytUserConfig.getType())
                        .value(tytUserConfig.getValue());
            } else {
                builder.type(type)
                        .value(UserConfigTypeEnum.find(type).getDefaultValue());
            }

            return builder.build();

        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(transactionManager = "mybatisTransactionManager")
    public void updateUserConfig(UserConfigUpdateReq req) {
        String type = req.getType();
        String value = req.getValue();
        Long userId = req.getUserId();
        UserConfigTypeEnum configTypeEnum = UserConfigTypeEnum.find(type);

        PreConditions.check(configTypeEnum.getValueList().contains(value), ReturnCodeConstant.ERROR, "参数有误");

        TytUserConfig userConfig = userConfigMapper.selectByUserIdAndType(userId, type);
        TytUserConfig operate = new TytUserConfig();
        Date now = new Date();
        operate.setValue(value);
        operate.setUpdateTime(now);
        if (userConfig != null) {
            operate.setId(userConfig.getId());
            userConfigMapper.updateByPrimaryKeySelective(operate);
        } else {
            operate.setUserId(userId);
            operate.setType(type);
            operate.setCreateTime(now);
            userConfigMapper.insertSelective(operate);
        }
    }
}
