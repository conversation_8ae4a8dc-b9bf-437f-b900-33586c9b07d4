package com.tyt.plat.biz.userconfig.service;

import com.tyt.plat.biz.userconfig.pojo.UserConfigUpdateReq;
import com.tyt.plat.biz.userconfig.pojo.UserConfigVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/03/07 15:04
 */
public interface UserConfigService {

    List<UserConfigVO> getUserConfigList(Long userId, List<String> configTypes);

    void updateUserConfig(UserConfigUpdateReq req);
}
