package com.tyt.plat.biz.userconfig.controller;

import com.tyt.plat.biz.userconfig.pojo.UserConfigVO;
import com.tyt.plat.entity.base.TytUserConfig;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/03/07 15:17
 */
public class UserConfigCovert {
    private UserConfigCovert() {
    }

    public static UserConfigVO covertUserConfig(TytUserConfig userConfig) {
        return UserConfigVO.builder().type(userConfig.getType()).value(userConfig.getValue()).build();
    }

    public static List<UserConfigVO> covertUserConfig(List<TytUserConfig> userConfigs) {
        return userConfigs.stream().map(UserConfigCovert::covertUserConfig).collect(Collectors.toList());
    }
}
