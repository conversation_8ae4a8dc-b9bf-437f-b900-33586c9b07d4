package com.tyt.plat.biz.userconfig.controller;

import com.tyt.model.ResultMsgBeanV2;
import com.tyt.plat.biz.userconfig.pojo.UserConfigUpdateReq;
import com.tyt.plat.biz.userconfig.pojo.UserConfigVO;
import com.tyt.plat.biz.userconfig.service.UserConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

import static com.tyt.util.ReturnCodeConstant.BASIC_PARAMETER_ERROR;

/**
 * 用户配置接口
 *
 * <AUTHOR>
 * @since 2024/03/05 10:08
 */
@RestController
@Slf4j
@RequestMapping("/plat/user/config")
@RequiredArgsConstructor
public class UserConfigController {

    private final UserConfigService userConfigService;

    /**
     * 获取用户配置
     * <p>
     * 配置类型: 抢单豆自动扣除 --> AUTO_DECREASE_DIAL_TIMES; 0不自动扣，1自动扣
     *
     * @param configType 配置类型，多种配置类型用逗号分隔
     * @return 用户所有的配置
     */
    @RequestMapping(value = "list", method = {RequestMethod.GET, RequestMethod.POST})
    public ResultMsgBeanV2<List<UserConfigVO>> getUserConfigList(Long userId,
                                                                 String configType) {
        String[] split = configType.split("[,，]");
        List<UserConfigVO> res = userConfigService.getUserConfigList(userId, Arrays.asList(split));
        return ResultMsgBeanV2.successResponse(res);
    }

    /**
     * 更新用户配置
     * <p>
     * 配置类型: 抢单豆自动扣除 --> AUTO_DECREASE_DIAL_TIMES; 0不自动扣，1自动扣
     *
     * @return 用户所有的配置
     */
    @PostMapping("update")
    public ResultMsgBeanV2<?> updateUserConfig(@Validated UserConfigUpdateReq req,
                                               BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBeanV2.failResponse(BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }
        userConfigService.updateUserConfig(req);
        return ResultMsgBeanV2.successResponse();
    }
}
