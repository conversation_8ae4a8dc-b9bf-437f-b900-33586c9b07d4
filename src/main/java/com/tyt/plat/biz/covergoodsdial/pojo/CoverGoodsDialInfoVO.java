package com.tyt.plat.biz.covergoodsdial.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/01/17 13:58
 */
@Data
public class CoverGoodsDialInfoVO {

    /**
     * 是否在配置名单内
     */
    private boolean dialUser;

    /**
     * 是否在配置捂货白名单内
     */
    private boolean dialWhiteUser;

    /**
     * 配置的x时间
     */
    private Integer configXSeconds;

    /**
     * 配置的最大x时间
     */
    private Integer configMaxXSeconds;

    /**
     * 配置的y时间
     */
    private Integer configYSeconds;

    /**
     * 免责卡剩余使用次数
     */
    private Integer leftN;

    /**
     * 服务器当前时间
     */
    private long currentTimeStamp = System.currentTimeMillis();

    /**
     * 抢单豆获取次数
     */
    private Integer gainTimes;
}
