package com.tyt.plat.biz.covergoodsdial.pojo;

import lombok.Data;

import java.sql.Date;

/**
 * 抢单豆获取实体
 *
 * <AUTHOR>
 * @since 2024/2/21 14:42
 */
@Data
public class CoverGoodsDialGainInfoVO {
    /**
     * tyt_cover_goods_beans_config_user表Id
     */
    private Long id;

    /**
     * 用户UserId
     */
    private Long userId;

    /**
     * 用户获取时间
     */
    private Date createTime;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 获取免责卡（抢单豆）单次总次数
     */
    private Integer totalNum;


    /**
     * 获取免责卡（抢单豆）使用次数
     */
    private Integer usedNum;

    /**
     * 获取免责卡（抢单豆）单次剩余次数
     */
    private Integer leftNum;

    /**
     * 抢单豆到期时间
     */
    private Date validateTime;


}
