package com.tyt.plat.biz.covergoodsdial.pojo;

import com.tyt.plat.entity.base.TytCoverGoodsBeansConfigUser;
import com.tyt.plat.entity.base.TytCoverGoodsDialUserUseLog;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/01/17 13:58
 */
@Data
public class CoverGoodsDialUserInfoVO {

    /**
     * 免责卡（抢单豆）剩余使用次数
     */
    private Integer leftN;

    /**
     * 抢单豆使用记录
     */
    private List<TytCoverGoodsDialUserUseLog> coverGoodsDialUserUseLogs;

    /**
     * 抢单豆获取记录
     */
    private List<TytCoverGoodsBeansConfigUser>  coverGoodsDialGainInfoVOLogs;


    /**
     * 服务器当前时间
     */
    private long currentTimeStamp = System.currentTimeMillis();
}
