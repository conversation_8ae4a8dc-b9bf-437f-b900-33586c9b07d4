package com.tyt.plat.biz.covergoodsdial.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/01/17 14:02
 */
@Data
public class CountdownReportReq {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 货源id
     */
    private Long tsId;

    /**
     * 倒计时是否完成, 倒计时结束此字段传 true, 未结束传 false 或者不传
     */
    private Boolean countDownFinished;

    /**
     * 要减少的倒计时的时间, 单位秒
     */
    private Integer reduceTimeInSeconds;
}
