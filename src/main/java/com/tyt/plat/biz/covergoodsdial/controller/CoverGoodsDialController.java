package com.tyt.plat.biz.covergoodsdial.controller;

import com.tyt.common.bean.PageData;
import com.tyt.model.ResultMsgBeanV2;
import com.tyt.plat.biz.covergoodsdial.pojo.CheckAutoDecreasePopConfigVO;
import com.tyt.plat.biz.covergoodsdial.pojo.CountdownReportReq;
import com.tyt.plat.biz.covergoodsdial.pojo.CountdownReportResp;
import com.tyt.plat.biz.covergoodsdial.pojo.CoverGoodsDialInfoVO;
import com.tyt.plat.biz.covergoodsdial.pojo.CoverGoodsDialUserInfoVO;
import com.tyt.plat.biz.covergoodsdial.pojo.CoverGoodsDialUserUseRecordVO;
import com.tyt.plat.biz.covergoodsdial.pojo.UseNReq;
import com.tyt.plat.biz.covergoodsdial.pojo.UseNVO;
import com.tyt.transport.service.TytCoverGoodsDialConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.tyt.util.ReturnCodeConstant.BASIC_PARAMETER_ERROR;

/**
 * 捂货车方接口, 所有接口增加前缀 /plat/plat
 *
 * <AUTHOR>
 * @since 2024/01/17 13:56
 */
@RestController
@RequestMapping("/plat/cover/goods/dial")
@RequiredArgsConstructor
public class CoverGoodsDialController {

    private final TytCoverGoodsDialConfigService coverGoodsDialConfigService;

    /**
     * 获取车方的捂货信息
     */
    @RequestMapping(value = "/info", method = {RequestMethod.GET, RequestMethod.POST})
    public ResultMsgBeanV2<CoverGoodsDialInfoVO> getCoverGoodsDialInfo(Long userId) {
        if (userId == null) {
            return ResultMsgBeanV2.failResponse(BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        return ResultMsgBeanV2.successResponse(coverGoodsDialConfigService.getCoverGoodsDialInfo(userId));
    }

    /**
     * 获取车方抢单豆信息
     */
    @GetMapping(value = "/getCoverGoodsDialUserInfo.action")
    public ResultMsgBeanV2<CoverGoodsDialUserInfoVO> getCoverGoodsDialUserInfo(@RequestParam Long userId) {
        return ResultMsgBeanV2.successResponse(coverGoodsDialConfigService.getCoverGoodsDialUserInfo(userId));
    }


    /**
     * 上报捂货倒计时
     */
    @PostMapping("/countdown/report")
    public ResultMsgBeanV2<CountdownReportResp> coverGoodsCountdownReport(CountdownReportReq req) {
        return ResultMsgBeanV2.successResponse(coverGoodsDialConfigService.coverGoodsCountdownReport(req));
    }

    /**
     * 使用免责卡
     * <p>
     * 2024年1月20日  返回体删除 leftN 字段
     */
    @PostMapping("/n/use")
    public ResultMsgBeanV2<UseNVO> useN(UseNReq req) {
        return ResultMsgBeanV2.successResponse(coverGoodsDialConfigService.useN(req));
    }

    /**
     * 检查是否上报过自动使用抢单豆弹框
     * <p>
     */
    @RequestMapping(value = "/config/autoDecreasePop/check", method = {RequestMethod.GET, RequestMethod.POST})
    public ResultMsgBeanV2<CheckAutoDecreasePopConfigVO> checkAutoDecreasePopConfig(Long userId) {
        return ResultMsgBeanV2.successResponse(coverGoodsDialConfigService.checkAutoDecreasePopConfig(userId));
    }

    /**
     * 上报自动使用抢单豆弹框
     * <p>
     */
    @PostMapping("/config/autoDecreasePop/report")
    public ResultMsgBeanV2<?> reportAutoDecreasePopConfig(Long userId) {
        coverGoodsDialConfigService.reportAutoDecreasePopConfig(userId);
        return ResultMsgBeanV2.successResponse();
    }

    /**
     * 抢单豆交易记录
     */
    @RequestMapping(value = "/n/use/record", method = {RequestMethod.GET, RequestMethod.POST})
    public ResultMsgBeanV2<PageData<CoverGoodsDialUserUseRecordVO>> getNUseRecord(
            Long userId,
            Integer pageNum,
            Integer pageSize) {
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        PageData<CoverGoodsDialUserUseRecordVO> nUseRecord = coverGoodsDialConfigService
                .getNUseRecord(userId, pageNum, pageSize)
                .covert(it -> {
                    CoverGoodsDialUserUseRecordVO result = new CoverGoodsDialUserUseRecordVO();
                    result.setUseReason(it.getUseReason());
                    result.setCreateTime(it.getCreateTime());
                    result.setChangeNum(it.getChangeNum());
                    result.setChangeType(it.getChangeType());
                    result.setExpireTime(it.getExpireTime());
                    return result;
                });
        return ResultMsgBeanV2.successResponse(nUseRecord);
    }

    /**
     * 获取默认配置
     * <p>
     * 默认配置类型:
     * 1->车主等级配置
     * 2->履约单单奖配置
     * 3->认证车辆配置
     * 4->认证司机配置
     * 5->认证企业配置
     * 6->认证车主配置
     *
     * @param type 默认配置类型
     */
    @RequestMapping(value = "/send/config/default", method = {RequestMethod.GET, RequestMethod.POST})
    public ResultMsgBeanV2<String> getSendDefaultConfig(Integer type) {
        return ResultMsgBeanV2.successResponse(coverGoodsDialConfigService.getSendDefaultConfig(type));
    }
}
