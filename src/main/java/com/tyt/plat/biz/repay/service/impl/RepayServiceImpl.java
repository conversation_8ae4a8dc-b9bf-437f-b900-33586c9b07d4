package com.tyt.plat.biz.repay.service.impl;

import com.tyt.common.bean.PageData;
import com.tyt.plat.biz.repay.pojo.RepayListReq;
import com.tyt.plat.biz.repay.pojo.RepayListResp;
import com.tyt.plat.biz.repay.service.RepayService;
import com.tyt.plat.commons.model.PageParameter;
import com.tyt.plat.commons.tools.CustomPageHelper;
import com.tyt.plat.mapper.base.TransportOrdersExRepayMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 追偿服务接口实现类
 *
 * <AUTHOR>
 * @since 2024-3-4 10:30:35
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RepayServiceImpl implements RepayService {

    private final TransportOrdersExRepayMapper transportOrdersExRepayMapper;

    @Override
    public PageData<RepayListResp> myRepayList(RepayListReq req) {
        CustomPageHelper pageHelper = CustomPageHelper.startPage(new PageParameter(req.getPageNum(), req.getPageSize()));
        List<RepayListResp> feedbackUsers = transportOrdersExRepayMapper.myRepayList(req);
        return pageHelper.endPage(feedbackUsers);
    }
}
