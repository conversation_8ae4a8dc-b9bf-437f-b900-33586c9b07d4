package com.tyt.plat.biz.repay.pojo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 我的偿还列表响应类
 *
 * <AUTHOR>
 * @since 2024/03/05 10:44
 */
@Data
public class RepayListResp implements Serializable {

    /**
     * 追偿ID
     */
    private Long repayId;

    /**
     * 偿还金额
     */
    private BigDecimal repayAmount;

    /**
     * 出发地
     */
    private String startPoint;

    /**
     * 目的地
     */
    private String destPoint;

    /**
     * 发货人昵称
     */
    private String pubUserName;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 运单号
     */
    private String tsOrderNo;

    /**
     * 还款时间
     */
    private Date repayTime;
}
