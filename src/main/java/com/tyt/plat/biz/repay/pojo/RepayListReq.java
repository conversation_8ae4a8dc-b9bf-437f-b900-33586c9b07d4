package com.tyt.plat.biz.repay.pojo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 我的偿还列表请求类
 *
 * <AUTHOR>
 * @since 2024/03/05 10:44
 */
@Data
public class RepayListReq implements Serializable {

    /**
     * 追偿状态：1-待偿还；2-已偿还；
     */
    @NotNull(message = "追偿状态为空")
    @Max(value = 2, message = "追偿状态不合法")
    @Min(value = 1, message = "追偿状态不合法")
    private Integer repayStatus;

    /**
     * 用户类型：1-车；2-货；
     */
    @NotNull(message = "用户类型为空")
    @Max(value = 2, message = "用户类型不合法")
    @Min(value = 1, message = "用户类型不合法")
    private Integer userType;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID为空")
    private Long userId;

    /**
     * 条数
     */
    private Integer pageSize = 20;

    /**
     * 页数
     */
    private Integer pageNum = 1;
}
