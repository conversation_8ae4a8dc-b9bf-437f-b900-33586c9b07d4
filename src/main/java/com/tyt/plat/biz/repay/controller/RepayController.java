package com.tyt.plat.biz.repay.controller;

import com.tyt.common.bean.PageData;
import com.tyt.model.ResultMsgBeanV2;
import com.tyt.plat.biz.repay.pojo.RepayListReq;
import com.tyt.plat.biz.repay.pojo.RepayListResp;
import com.tyt.plat.biz.repay.service.RepayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 追偿controller类
 *
 * <AUTHOR>
 * @since 2024-3-4 10:31:30
 */
@RestController
@Slf4j
@RequestMapping("/plat/repay")
@RequiredArgsConstructor
public class RepayController {

    private final RepayService repayService;

    /**
     * 我的偿还列表
     */
    @GetMapping("/myRepayList/get")
    public ResultMsgBeanV2<PageData<RepayListResp>> myRepayList(@Validated RepayListReq req) {
        try {
            PageData<RepayListResp> vo = repayService.myRepayList(req);
            return ResultMsgBeanV2.successResponse(vo);
        } catch (Exception e) {
            log.error("my repay list error:", e);
            return ResultMsgBeanV2.failResponse(e);
        }
    }
}
