package com.tyt.plat.vo.enterprise;

import lombok.Data;

/**
 * 运营商三网认证回调
 *
 * <AUTHOR>
 * @date 2024/3/1 13:18
 */
@Data
public class EsignCallBackHeaderVo {

    /**
     * 客户发生业务时的项目ID
     */
    private String appId;

    /**
     * 签名值
     */
    private String signature;

    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 使用的算法, 默认算法hmac-sha256
     */
    private String algorithm;

}
