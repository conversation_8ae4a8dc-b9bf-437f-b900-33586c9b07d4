package com.tyt.plat.vo.enterprise;

import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * 企业合同信息.
 *
 * <AUTHOR>
 * @date 2024-2-20 16:37:54
 */
@Data
public class EnterpriseContractVo {

    /**
     * sign id.
     */
    private Long id;

    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业认证统一社会信用代码
     */
    private String enterpriseCreditCode;

    /**
     * 企业法人姓名
     */
    private String legalPersonName;

    /**
     * 企业法人手机号
     */
    private String legalPersonPhone;

    /**
     * 企业法人身份证号码
     */
    private String legalPersonCard;

    /**
     * 框架协议状态(0未签署;1签署中;2已签署;3签署失败;4已失效)
     * 判断是否是签署中,如果签署中则跳页面
     */
    private Integer contractStatus;

    /**
     * 未签署的协议文件URL
     */
    private String contractBlankUrl;

    /**
     * 协议编号
     */
    private String contractNumber;

    /**
     * e签宝签署人三要素实名认证状态（0未认证,1认证中,2认证成功,3认证失败）
     */
    private Integer signerVerifyStatus;

    /**
     * 授权激活类型（1法人授权;2委托书授权）
     */
    private Integer signType;

    /**
     * 企业激活授权状态(0未认证;1认证中;2认证成功;3认证失败)
     */
    private Integer activeAuthStatus;

    /**
     * 签署意愿确认状态(0未确认;1确认中;2确认成功;3确认失败)
     */
    private Integer signConfirmStatus;

    /**
     * 授权委托书URL
     */
    private String signAuthUrl;

    /**
     * 授权委托书URL生成日期
     */
    private Date signAuthTime;

    /**
     * 企业最终激活状态(0未激活;1激活中;2激活成功;3激活失败)
     */
    private Integer contractFinalStatus;

}
