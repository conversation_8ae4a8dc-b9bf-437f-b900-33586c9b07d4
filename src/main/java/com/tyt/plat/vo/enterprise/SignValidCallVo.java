package com.tyt.plat.vo.enterprise;

import lombok.Data;

/**
 * 运营商三网认证回调
 *
 * <AUTHOR>
 * @date 2024/3/1 13:18
 */
@Data
public class SignValidCallVo {

    /**
     * 个人认证流程id
     */
    private String flowId;

    /**
     * 个人账户id，核身认证时为null
     */
    private String accountId;

    /**
     * 认证是否成功. true - 成功; false - 失败
     */
    private Boolean success;

    /**
     * 业务上下文Id
     */
    private String contextId;

    /**
     * 认证结果校验码,用于串联e签宝其他业务
     */
    private String verifycode;

    /**
     * 历史版本兼容字段，新用户忽略，等价于flowId
     */
    private String serviceId;

    /**
     * 历史版本兼容字段，新用户忽略，等价于success
     */
    private Boolean status;

    /**
     * 标记该通知的业务类型，该通知固定为：identity_psn_end.
     */
    private String action;

}
