package com.tyt.plat.vo.help;

import com.tyt.plat.entity.base.TytCourseUser;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * 课程详情.
 *
 * <AUTHOR>
 * @date 2023/11/24 16:15
 */
@Data
public class CourseDetailVo {

    private Long id;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 类型（1车，2货）
     */
    private Integer type;

    /**
     * 封面图片地址
     */
    private String coverImgUrl;

    /**
     * 标题
     */
    private String title;

    /**
     * 摘要（副标题）
     */
    private String summary;

    /**
     * 模板类型(1图文;2纯图)
     */
    private Integer templateType;

    /**
     * 页面地址
     */
    private String linkUrl;

    /**
     * 有效状态(1有效;0已删除)
     */
    private Integer enable;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sortNumber;

    /**
     * 富文本内容
     */
    private String contentText;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 学习状态（0未学习;1学习中;2学习完成），
     * 附加 null 为未下发学习.
     */
    private TytCourseUser courseUser;

}
