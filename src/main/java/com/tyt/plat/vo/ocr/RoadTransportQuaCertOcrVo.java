package com.tyt.plat.vo.ocr;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 道路运输从业资格证主页OCR结果类
 *
 * <AUTHOR>
 * @since 2024/04/26 14:03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoadTransportQuaCertOcrVo {

    /**
     * 从业资格证号
     */
    private String certificateNumber;
    /**
     * 姓名
     */
    private String name;
    /**
     * 准驾车型
     */
    private String drivingClass;
    /**
     * 有效期至
     */
    private Date expiryDate;
    /**
     * 从业资格证类别
     */
    private String category;
    /**
     * 初次领证日期
     */
    private Date initialIssueDate;
}
