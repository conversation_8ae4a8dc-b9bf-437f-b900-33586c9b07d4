package com.tyt.plat.vo.ocr;

import lombok.Data;

/**
 * 营业执照OCR RPC VO
 *
 * <AUTHOR>
 * @since 2025-6-17 09:33:35
 */
@Data
public class BusinessLicenseOcrRpcVO {

    /**
     * 企业名称
     */
    private String name;
    /**
     * 统一信用代码
     */
    private String regNum;
    /**
     * 法人姓名
     */
    private String person;
    /**
     * 有效开始时间
     */
    private String startTime;
    /**
     * 有效结束时间
     */
    private String endTime;
    /**
     * 公司地址
     */
    private String address;
    /**
     * 经营范围
     */
    private String business;
    /**
     * 注册日期
     */
    private String setDate;
    /**
     * 企业类型
     */
    private String type;
    /**
     * 结果ID
     */
    private String resultId;
}
