package com.tyt.plat.vo.ocr;

import lombok.Data;

import java.util.Date;

/**
 * 身份证反面照信息
 *
 * <AUTHOR>
 * @date 2023/4/26 16:10
 */
@Data
public class OcrIdCardBackVo extends OcrDataVo {

    private String imageStatus;

    //是否长期
    private Integer idCardLongTerm;
    private String expireDateText;
    private Date expireDate;

    //签发机关
    private String signGov;

    //签发日期
    private String signDateText;
    private Date signDate;

    /**
     * 是否过期
     */
    private Integer expireFlag;

    /**
     * 是否有风险
     */
    private Integer riskFlag;

    /**
     * 风险提示信息
     */
    private String riskMsg;

}
