package com.tyt.plat.vo.ocr;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 行驶证主页
 *
 * <AUTHOR>
 * @date 2023/4/28 11:13
 */
@Data
public class VehicleLicenseFrontVo extends OcrDataVo {

    //车辆识别代号
    private String identifyCode;

    //住址
    private String liveAddress;

    //发证日期
    private String issueDateText;
    private Date issueDate;

    //发证单位
    private String issueUnit;

    //品牌型号
    private String brandModel;

    //车辆类型
    private String carType;

    //所有人
    private String ownerName;

    //使用性质
    private String useNature;

    //发动机号码
    private String engineNumber;

    //号牌号码
    private String carNumber;

    //注册日期
    private String registerDateText;
    private Date registerDate;


    public boolean allFieldsIsNull() {
        if (StringUtils.isBlank(identifyCode) && StringUtils.isBlank(liveAddress) && StringUtils.isBlank(issueDateText) && Objects.isNull(issueDate) && StringUtils.isBlank(issueUnit) && StringUtils.isBlank(brandModel)
                && StringUtils.isBlank(carType) && StringUtils.isBlank(ownerName) && StringUtils.isBlank(useNature) && StringUtils.isBlank(engineNumber) && StringUtils.isBlank(carNumber) && StringUtils.isBlank(registerDateText)
                && Objects.isNull(registerDate)) {
            return true;
        }
        return false;
    }

}
