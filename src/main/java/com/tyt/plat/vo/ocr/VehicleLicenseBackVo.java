package com.tyt.plat.vo.ocr;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 行驶证副页
 *
 * <AUTHOR>
 * @date 2023/4/28 11:13
 */
@Data
public class VehicleLicenseBackVo extends OcrDataVo {

    //检验记录
    private String inspectionRecord;

    //核定载质量
    private String approvedLoad;

    //整备质量
    private String curbWeight;

    //外廓尺寸
    private String overallSize;

    //核定载人数
    private String passengerNumber;

    //总质量
    private String totalMass;

    //燃油类型
    private String fuelType;

    //准牵引总质量
    private String tractionMass;

    //备注
    private String remark;

    //档案编号
    private String fileNumber;

    //号牌号码
    private String carNumber;

    //证芯编号
    private String chipNumber;


    public boolean allFieldsIsNull() {
        if (StringUtils.isBlank(inspectionRecord) && StringUtils.isBlank(approvedLoad) && StringUtils.isBlank(curbWeight) && StringUtils.isBlank(overallSize) && StringUtils.isBlank(passengerNumber)
                && StringUtils.isBlank(totalMass) && StringUtils.isBlank(fuelType) && StringUtils.isBlank(tractionMass) && StringUtils.isBlank(remark) && StringUtils.isBlank(fileNumber)
                && StringUtils.isBlank(carNumber) && StringUtils.isBlank(chipNumber)) {
            return true;
        }
        return false;
    }
}
