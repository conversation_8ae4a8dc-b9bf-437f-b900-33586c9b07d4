package com.tyt.plat.vo.ts;

import lombok.Data;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/7/7 17:00
 */
@Data

public class TransportStatusOption {

    private Long userId;
    //1撤销货源/2设置成交
    private Integer operateType;
    private Long goodsId;
    //撤销原因key
    private String backoutReasonKey;
    //撤销原因Value
    private Integer backoutReasonValue;
    private String specificReason;
    private String remark;
    private Boolean saveVary;
    //移除时，是否保留急走专区
    private Boolean keepExposure;

    private String backoutReasonKeyNew;
    private Integer backoutReasonValueNew;

    public TransportStatusOption(Long userId, Integer operateType, Long goodsId, String backoutReasonKey,
                                 Integer backoutReasonValue, String specificReason, String remark, Boolean saveVary, String backoutReasonKeyNew, Integer backoutReasonValueNew) {
        this.userId = userId;
        this.operateType = operateType;
        this.goodsId = goodsId;
        this.backoutReasonKey = backoutReasonKey;
        this.backoutReasonValue = backoutReasonValue;
        this.specificReason = specificReason;
        this.remark = remark;
        this.saveVary = saveVary;
        this.backoutReasonKeyNew = backoutReasonKeyNew;
        this.backoutReasonValueNew = backoutReasonValueNew;
    }

    public TransportStatusOption() {
    }
}
