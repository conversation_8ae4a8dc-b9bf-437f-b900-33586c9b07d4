package com.tyt.plat.vo.ts;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@NoArgsConstructor
public class TransportCarFindBIDataJson {

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 车主ID
     */
    private Long carUserId;

    /**
     * 1:用豆;2:跳出
     */
    private Integer type;

    /**
     * 读秒数值
     */
    private Integer secondNum;

    /**
     * 中断方式——解除读秒方式（1:自动、2:手动、3:返回）
     */
    private Integer interruptType;

    /**
     * 中断时间
     */
    private Date interruptTime;

    /**
     * 解除时间
     */
    private Date releaseTime;

    /**
     * 客户端（1:APP、2:小程序）
     */
    private Integer clientType;

}
