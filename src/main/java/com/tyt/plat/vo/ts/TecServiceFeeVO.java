package com.tyt.plat.vo.ts;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TecServiceFeeVO {

    /**
     * 技术服务费（折后，实际要付的钱）
     */
    private BigDecimal tecServiceFee;

    /**
     * 技术服务费（折前）
     */
    private BigDecimal tecServiceFeeAfterDiscount;

    /**
     * 技术服务费折前折后差值
     */
    private BigDecimal tecServiceFeeAfterDiscountDValue;

    /**
     * 免佣、折扣文案
     */
    private String tecServiceFeeWord;

    /**
     * 跳转小会员ID，如果没有代表跳转到大会员
     */
    private Long tecServiceFeeBuySmallMemberId;

}
