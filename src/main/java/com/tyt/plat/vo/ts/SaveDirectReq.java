package com.tyt.plat.vo.ts;

import com.tyt.base.bean.BaseParameter;
import com.tyt.plat.enums.SaveDirectOptEnum;
import lombok.Data;

/**
 * 直接发布参数
 *
 * <AUTHOR>
 * @date 2023/3/22 22:49
 */
@Data
public class SaveDirectReq extends BaseParameter {
    //货源id
    private Long goodsId;
    //是否是小程序货源
    private Integer isBackendTransport;
    //转一口价/电议 货源类型（电议1，一口价2） 一口价需求新增参数，可传 null
    private Integer publishType;

    //是否修改价格
    private String price;

    //是否是加价操作
    private SaveDirectOptEnum directOptEnum;

    //当置顶时是否消耗曝光卡
    private Boolean useExposure = true;

    //加价次数
    private Integer addPriceCount;

    //确认发布重货
    private Integer confirm;

    /**
     * 是否优车运价货源 1:是 0:否
     */
    private Integer goodCarPriceTransport;

    /**
     * 是否自动优车运价货源
     */
    private Boolean automaticGoodCarPriceTransport;

    /**
     * 自动转优车定价类型 1:编辑发布自动转；2:直接发布自动转；3：填价、加价自动转；4转一口价自动转
     */
    private Integer automaticGoodCarPriceTransportType;

    /**
     * 需要额外更新的数据
     */
    private TransportUpdateDataReq updateDataReq;

    /**
     * 是否修改了目的地
     */
    private Boolean isChangeDestAddress = false;

    /**
     * 是否是货源自动自动重发
     */
    private Boolean isAutoResend = false;

    /**
     * 系统推荐金额，可为空，仅用作日志记录
     */
    private String priceSuggest;
}
