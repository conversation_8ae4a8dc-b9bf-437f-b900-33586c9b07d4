package com.tyt.plat.vo.ts;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransportBIDataJson {

    /**
     * 货源调用BI优车好货接口返回接口
     */
    private Integer iGBIResultData;

    /**
     * 编辑发货时是否实际展示过【优推好车主】
     */
    private Integer publishTransportIsShowGoodCar;

    /**
     * 出发地地址来源,1:地图选点，2：手动搜索
     */
    private Integer startAddrSource;
    /**
     * 目的地地址来源     1:地图选点，2：手动搜索
     */
    private Integer destAddrSource;

    /**
     * 建议价最小价格
     */
    private Integer thMinPrice;

    /**
     * 建议价最大价格建议价格
     */
    private Integer thMaxPrice;

    /**
     * 建议价建议价格
     */
    private Integer suggestPrice;

    //推荐最低价
    private Integer suggestMinPrice;

    //推荐最高价
    private Integer suggestMaxPrice;

    //优车定价低值
    private Integer fixPriceMin;

    //优车定价高值
    private Integer fixPriceMax;

    //优车定价最快成交价格
    private Integer fixPriceFast;

    /**
     * 1:app;2:pc
     */
    private Integer clientType;

    //是否弹出了优车定价货源卡片
    private Integer showGoodCarPriceTransportTab;

    /**
     * 自动转优车定价类型 1:编辑发布自动转；2:直接发布自动转；3：填价、加价自动转；4转一口价自动转
     */
    private Integer automaticGoodCarPriceTransportType;

    /**
     * 优车运价货源 1:是；null：否
     */
    private Integer goodCarPriceTransport;

    /**
     * 配置的捂货时间(单位秒)
     */
    private Integer xTimeInConfig;

    /**
     * 实际在线捂货时间(单位秒)
     */
    private Integer xTimeInActual;

    /**
     * 发货类型，1:首发 2:编辑重发 3:直接发布
     */
    private Integer publishType;

    /**
     * 公里数（专车发货）
     */
    private BigDecimal distanceKilometer;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 是否是优车货源（0-否，1-优车，2-专车）
     */
    private Integer excellentGoods;

    /**
     * 是否抽佣货源 1:是；0:否
     */
    private Integer commissionTransport;

    private Integer meetCommissionRules;

    /**
     * 是否是优推好车主  1：是  0 否
     */
    private Integer priorityRecommend;

    /**
     * 调用BI优车好货接口返回结果的分数
     */
    private BigDecimal goodsModelScore;

    /**
     * 抽佣标记 0 抽佣，1 不抽佣
     */
    private String commissionLabel;

    @JsonIgnore
    @Transient
    @JSONField(serialize = false)
    public String getJsonText() {
        String jsonText = JSON.toJSONString(this);

        if (jsonText.equals("{}")) {
            jsonText = "";
        }
        return jsonText;
    }

}
