package com.tyt.plat.vo.ts;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tyt.plat.biz.feedback.pojo.UserFeedbackRatingAndLabelDTO;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * transport 相关表中 label_json 内部属性
 * 在命名规范的前提下，尽量缩短字段名称
 *
 * <AUTHOR>
 * @date 2023/1/29 13:54
 */
@Data
public class TransportLabelJson {

    //测试用,可以删除
    @Deprecated
    private Integer test;

    //用户标签名称
    private String goodService;

    //用户标签id，0:无标签，1:正向（服务好），2:负向（客诉多）
    private Integer userLabelIcon;

    private Integer duplicateFlag;

    /**
     * 加价次数.
     */
    private Integer addPriceCount;

    private UserFeedbackRatingAndLabelDTO feedbackLabel;
    /**
     * 秒抢货源 1：是
     */
    private Integer instantGrab;

    //用户认证状态（1通过）
    private Integer userAuthStatus;
    //企业认证状态（1通过）
    private Integer enterpriseAuthStatus;

    /**
     * 调用BI优车好货接口返回结果
     */
    @Transient
    private Integer iGBIResultData;

    /**
     * 调用BI优车好货接口返回结果的分数
     */
    @Transient
    private BigDecimal goodsModelScore;

    /**
     * 优车运价货源 1:是；null：否
     */
    private Integer goodCarPriceTransport;

    /**
     * 是否抽佣货源 1:是；0:否
     */
    private Integer commissionTransport;

    /**
     * 是否是参与现金奖活动的货源 1:是；0:否
     */
    private Integer isCashPrizeActivityGoods;

    /**
     * 不抽佣原因
     */
    private String commissionLabel;


    /**
     * 是否是手动填写的技术服务费 1是；0否
     * 代调专车非平台发货，使用客户端的技术服务费
     */
    private Integer isManualTecServiceFee;

    /**
     * 价格标签 :0.无标签 1.该货源价格高于历史成交均价
     */
    private Integer priceLabel;

    /**
     * 是否是秒抢货源变成非秒抢 1:是；0:否
     */
    private Integer seckillDowngrade;

    @JsonIgnore
    @Transient
    @JSONField(serialize = false)
    public String getJsonText() {
        String jsonText = JSON.toJSONString(this);

        if (jsonText.equals("{}")) {
            jsonText = "";
        }
        return jsonText;
    }

    /**
     * 该字段不做序列化，只作为判断方法
     *
     * @return
     */
    @JSONField(serialize = false)
    @JsonIgnore
    @Transient
    public boolean isDuplicate() {
        boolean isDuplicate = (this.duplicateFlag != null && this.duplicateFlag == 1);
        return isDuplicate;
    }

    public static void main(String[] args) {

        TransportLabelJson labelJson = new TransportLabelJson();

        String jsonText = labelJson.getJsonText();

        System.out.println(jsonText);

    }
}
