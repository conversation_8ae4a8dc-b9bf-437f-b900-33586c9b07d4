package com.tyt.plat.vo.ts;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/3/16 14:48
 */
@Data
public class CallLogQuery extends BaseParameter {
    //拨打电话结果状态 1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易 6：已经拉走
    private Integer callResultCode;
    //所属车牌
    private String fromCar;
    //所属车辆
    private Long carId;
    //当前页数
    private Integer currentPage;

    private String cellPhone;

    private Date startDate;

    /**
     * 货源是否被出过价筛选条件
     */
    private Boolean quotedPriceOnce;

    /**
     * 是否浏览记录
     */
    private Boolean viewLog;

}
