package com.tyt.plat.vo.ts;

import com.tyt.base.bean.BaseParameter;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 货源发布后需要更新的数据
 *
 * <AUTHOR>
 * @since 2024/08/05 17:53
 */
@Getter
@Setter
public class TransportUpdateDataReq extends BaseParameter {

    /**
     * 货源id，即srcMsgId
     */
    private Long tsId;

    /**
     * 货物长单位米
     */
    private String length;

    /**
     * 货物宽单位米
     */
    private String wide;

    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 运费
     */
    private String price;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;

    // ====目的地信息=====
    /**
     * 目的地纬度
     */
    private BigDecimal destLatitude;
    /**
     * 目的地经度
     */
    private BigDecimal destLongitude;
    /**
     * 目的地X坐标
     */
    private BigDecimal destCoordX;
    /**
     * 目的地Y坐标
     */
    private BigDecimal destCoordY;

    // 目的地完整地址/省/市/县/详细地址
    private String destPoint;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private String destDetailAdd;

    /**
     * 距离
     */
    private String distance;

    /**
     * 系统推荐金额，可为空，仅用作日志记录
     */
    private String priceSuggest;

    /**
     * 用于埋点记录
     * 来源页面：0 未知；1 APP货源详情页货源诊断入口；2 APP发货成功货源诊断入口；3 APP发货页低速找车弹窗；10 PC发货前填价弹窗；
     */
    private Integer sourcePage;

}
