package com.tyt.plat.vo.remote;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 运价计算参数
 * @date 2022/3/21 10:04
 */
@NoArgsConstructor
@Data
public class CarryPriceReq {

    @JSONField(name = "start_province")
    private String startProvince;
    @JSONField(name = "start_city")
    private String startCity;
    @JSONField(name = "start_area")
    private String startArea;
    @JSONField(name = "dest_province")
    private String destProvince;
    @JSONField(name = "dest_city")
    private String destCity;
    @JSONField(name = "dest_area")
    private String destArea;
    @JSONField(name = "goods_name")
    private String goodsName;
    @JSONField(name = "goods_weight")
    private Double goodsWeight;
    @JSONField(name = "goods_length")
    private Double goodsLength;
    @JSONField(name = "goods_wide")
    private Double goodsWide;
    @JSONField(name = "goods_high")
    private Double goodsHigh;

    @JSONField(name = "api_sign")
    private String apiSign;
    @JSONField(name = "api_time")
    private String apiTime;

    private Integer publishType;

    private Integer source;

    private Long userId;

    //运费金额
    private BigDecimal price;
    //货源id
    private Long srcMsgId;

    //距离字段
    private String distance;

    // 是否是优车货源 0：否  1：是
    private Integer excellentGoods;

    //是否是优车定价货源 1 是，null或者0都不是
    private Integer goodCarPriceTransport;

}
