package com.tyt.plat.vo.remote;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 远程调用运价类
 * @date 2022/3/21 10:00
 */
@NoArgsConstructor
@Data
public class CarryPriceVo {

    //基础价
    private Integer basePrice;
    //成本价
    private Integer costPrice;
    //最小价格
    private Integer thMinPrice;
    //最大价格
    private Integer thMaxPrice;
    //建议价格
    private Integer suggestPrice;
    //推荐最低价
    private BigDecimal suggestMinPrice;
    //推荐最高价
    private BigDecimal suggestMaxPrice  ;

    //优车定价低值
    private Integer fixPriceMin;
    //优车定价高值
    private Integer fixPriceMax;
    //优车定价最快成交价格
    private Integer fixPriceFast;


    //信息费运价校验开关(1:开 2:关)
    private Integer infofeeCarryPriceEnable;

    //是否允许可电议 (1可电议，0不可电议)
    private Integer isAllowTeleNegotiation = 0;
    //路线是否支持电议有价(1支持，0不支持)
    private Integer isRouteSupportTeleNegotiation = 0;

}
