package com.tyt.plat.vo.user;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/09/24 10:01
 */
@Getter
@Setter
@ToString
public class UserExposureInfoVO {
    public static final String EXPOSURE_BLOCK_PERMANENT_MESSAGE = "您因平台管控已被限制使用曝光卡，如有疑问可联系客服";

    public static final String EXPOSURE_BLOCK_UN_PERMANENT_MESSAGE = "您因平台管控已被限制使用曝光卡，恢复时间%s";

    /**
     * 用户拥有的可用货源曝光权益总次数
     */
    private Integer userExposurePermissionCount;

    /**
     * 新增的曝光卡数
     */
    private Integer newExposureCount;

    /**
     * 曝光卡限制提示文案
     */
    private String exposureBlockMessage;

    /**
     * 待领取的曝光卡数量
     */
    private Integer unreceivedExposureCount;

    /**
     * 待领取的发货卡数量
     */
    private Integer unreceivedPunishCount;

}
