package com.tyt.plat.vo.user;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用户车方运点值
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-04-12
 */
@Data
public class UserCarryPointVo {

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 一级分类（1:基础值;2:行为分;3:服务分）
     */
    private Integer classOne;

    /**
     * 类型细分(101:新用户注册;102:老用户回归;103:导入)
     */
    private Integer classTwo;

    /**
     * 分数值，可以为负数
     */
    private BigDecimal score;

    /**
     * 分值名称
     */
    private String scoreName;

    /**
     * 有效期开始时间
     */
    private Date startTime;

    /**
     * 有效期结束时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 详情编码.
     */
    private String detailCode;

    public void addScore(BigDecimal oneScore){
        if(this.score == null){
            this.score = BigDecimal.ZERO;
        }

        this.score = this.score.add(oneScore);
    }

}
