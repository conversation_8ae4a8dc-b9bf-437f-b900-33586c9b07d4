package com.tyt.plat.vo.user;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业认证信息VO
 *
 * <AUTHOR>
 * @since 2024-8-14 13:49:29
 */
@Data
public class InvoiceEnterpriseInfoVO {

    /**
     * 企业ID
     */
    private Long id;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业法人姓名
     */
    private String legalPersonName;

    /**
     * 企业法人手机号
     */
    private String legalPersonPhone;

    /**
     * 企业法人身份证号码
     */
    private String legalPersonCard;

    /**
     * 法人身份证URL国徽面
     */
    private String legalPersonCardUrlG;

    /**
     * 法人身份证URL头像面
     */
    private String legalPersonCardUrlT;

    /**
     * 企业认证统一社会信用代码
     */
    private String enterpriseCreditCode;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 企业经营范围
     */
    private String enterpriseBusinessScope;

    /**
     * 企业归属地
     */
    private String enterpriseHomeAddress;

    /**
     * 企业注册地址
     */
    private String enterpriseDetailAddress;

    /**
     * 营业执照URL
     */
    private String licenseUrl;

    /**
     * 营业执照开始时间
     */
    private Date licenseStartTime;

    /**
     * 营业执照到期时间
     */
    private Date licenseEndTime;

    /**
     * 道路经营许可证照片URL
     */
    private String transportLicenseUrl;

    /**
     * 授权激活类型（1法人授权;2委托书授权） 
     */
    private Integer signType;

    /**
     * 协议编号(同contract_number)
     */
    private String contractNo;

    /**
     * 协议开始时间
     */
    private Date contractStartTime;

    /**
     * 协议结束时间
     */
    private Date contractEndTime;

    /**
     * （废弃）协议文件地址URL
     */
    private String contractUrl;

    /**
     * 授权人用户ID
     */
    private Long certigierUserId;

    /**
     * 授权人姓名
     */
    private String certigierUserName;

    /**
     * 授权人手机号（平台账号）
     */
    private String certigierUserPhone;

    /**
     * （废弃）授权委托书URL
     */
    private String authorizationUrl;

    /**
     * 企业发票税率
     */
    private BigDecimal enterpriseTaxRate;

    /**
     * (废弃)企业认证状态:0-未认证;1-认证通过;2-认证中;3-认证失败;4-认证过期;
     */
    private Integer enterpriseVerifyStatus;

    /**
     * 基本信息审核状态:0-未提交;1-审核中;2-已通过;3-审核驳回;4-审核失败;5-信息已过期;
     */
    private Integer infoVerifyStatus;

    /**
     * 基本信息审核失败原因
     */
    private String infoVerifyReason;

    /**
     * 基本信息提交时间
     */
    private Date infoVerifyCommitTime;

    /**
     * 框架协议状态，同contract_status(0未签署;1签署中;2已签署;3签署失败;4已失效) 
     */
    private Integer contractVerifyStatus;

    /**
     * (废弃)合同信息审核失败原因
     */
    private String contractVerifyReason;

    /**
     * (废弃)合同信息提交时间
     */
    private Date contractVerifyCommitTime;

    /**
     * 框架合同协议首次准入时间 
     */
    private Date contractAuditTime;

    /**
     * (废弃)合同信息审核人ID
     */
    private Long contractAuditUserId;

    /**
     * (废弃)合同信息审核人name
     */
    private String contractAuditUserName;

    /**
     * (废弃)授权人信息审核状态:0-未提交;1-审核中;2-已通过;3-审核驳回;4-审核失败;5-信息已过期;
     */
    private Integer certigierVerifyStatus;

    /**
     * (废弃)授权人信息审核失败原因
     */
    private String certigierVerifyReason;

    /**
     * (废弃)授权人信息提交时间
     */
    private Date certigierVerifyCommitTime;

    /**
     * (废弃)授权人信息审核时间
     */
    private Date certigierAuditTime;

    /**
     * (废弃)授权人信息审核人ID
     */
    private Long certigierAuditUserId;

    /**
     * (废弃)授权人信息审核人name
     */
    private String certigierAuditUserName;

    /**
     * 在线开票状态(0关闭;1开启) 
     */
    private Integer accountStatus;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 企业基础信息审核时间
     */
    private Date infoAuditTime;

    /**
     * 基本信息审核人ID
     */
    private Long infoAuditUserId;

    /**
     * 基本信息审核人name
     */
    private String infoAuditUserName;

    /**
     * 道路运输许可证驳回原因
     */
    private String transportRejectReason;

    /**
     * 道路运输许可证认证状态(0-未提交;1-审核中;2-已通过;3-审核驳回)
     */
    private Integer transportLicenseStatus;

    /**
     * 道路运输证提交时间
     */
    private Date transportCommitTime;

    /**
     * 道路运输证审核时间
     */
    private Date transportAuditTime;

    /**
     * 道路运输证审核人ID
     */
    private Long transportAuditUserId;

    /**
     * 道路运输证审核人name
     */
    private String transportAuditUserName;

    /**
     * 是否进行过天眼查企业核验 0：未进行；1：核验成功；2：核验失败
     */
    private Integer realVerify;

    /**
     * 营业执照是否长期 0：非长期；1：长期
     */
    private Integer licensePermanent;

    /**
     * 企业最终激活状态(0未激活;1激活中;2激活成功;3激活失败)
     */
    private Integer contractFinalStatus;

    /**
     * 企业最终激活失败原因
     */
    private String contractFinalReason;

    /**
     * 是否是企业管理员（0否;1是） 
     */
    private Integer managerFlag;

    /**
     * 是否是手动关闭开票状态(account_status)（0否；1是）
     */
    private Integer invoiceCloseFlag;

    /**
     * 认证提交来源(1车,2货)
     */
    private Integer sourceType;

    /**
     * 客户经理姓名
     */
    private String customerManagerName;

    /**
     * 客户经理手机号
     */
    private String customerManagerPhone;

    /**
     * 客户经理邮箱
     */
    private String customerManagerEmail;
}
