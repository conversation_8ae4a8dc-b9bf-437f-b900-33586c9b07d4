package com.tyt.plat.vo.user;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ElementVerifyResultV2 {

    @JsonProperty("RequestId")
    @J<PERSON>NField(name = "RequestId")
    private String requestId;

    @JsonProperty("Code")
    @JSONField(name = "Code")
    private Integer code;

    @JsonProperty("Message")
    @JSONField(name = "Message")
    private String message;

    @JsonProperty("Data")
    @JSONField(name = "Data")
    private ElementVerifyResultV2.ElementVerifyDataV2 data;

    @Data
    public static class ElementVerifyDataV2 {

        @JsonProperty("Status")
        @JSONField(name = "Status")
        private Integer status;

        //detail 为空，不需处理
        //@JSONField(name = "Detail")
        //private ElementVerifyResponseV2.ElementDetail detail;
    }

}
