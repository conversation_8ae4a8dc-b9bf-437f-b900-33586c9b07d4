package com.tyt.plat.vo.user;

import lombok.Data;

import java.util.Date;

/**
 * 问答详情.
 *
 * <AUTHOR>
 * @date 2023/9/6 14:13
 */
@Data
public class AnswerPageDetailVo {
    private Long id;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 展示范围（1内部；2全部）
     */
    private Integer showScope;

    /**
     * 标题
     */
    private String title;

    /**
     * h5地址
     */
    private String linkUrl;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sortNumber;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建管理员id
     */
    private Long createManId;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改管理员id
     */
    private Long modifyManId;

    /**
     * 是否发布（1是，0否）
     */
    private Integer publishStatus;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 发布管理员id
     */
    private Long publishManId;

    /**
     * 问答文本内容
     */
    private String contentText;

}
