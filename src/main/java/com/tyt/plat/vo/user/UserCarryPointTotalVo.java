package com.tyt.plat.vo.user;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户车方运点值
 * </p>
 *
 */
@Data
public class UserCarryPointTotalVo {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 总分
     */
    private BigDecimal carTotalScore;
    /**
     * 等级
     */
    private Integer carLevel;
    private String carLevelName;

    /**
     * bi总分时间
     */
    private Date biDate;

    private List<UserCarryPointVo> carryPointList;

}
