package com.tyt.plat.vo.user;

import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * 运营中台页面详情.
 *
 * <AUTHOR>
 * @date 2023/9/6 14:13
 */
@Data
public class FrontPageDetailVo {
    private Long id;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 客户端类型（1车，2货,3车和货）
     */
    @Column(name = "client_type")
    private Integer clientType;

    /**
     * 标题
     */
    private String title;

    /**
     * h5地址
     */
    private String linkUrl;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建管理员id
     */
    private Long createManId;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改管理员id
     */
    private Long modifyManId;

    /**
     * 是否发布（1是，0否）
     */
    private Integer publishStatus;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 发布管理员id
     */
    private Long publishManId;

    /**
     * 问答文本内容
     */
    private String contentText;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类型（1活动，2协议）
     */
    private Integer belongType;
}
