package com.tyt.plat.vo;

import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.user.querybean.QueryCar;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/07/18 10:55
 */
@Data
public class SpecialCarVo {

    private List<QueryCar> cars;

    private List<TytInvoiceDriver> drivers;

    private List<String> citys;

    private List<String> StartDests;
}
