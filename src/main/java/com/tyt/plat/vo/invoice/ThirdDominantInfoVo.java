package com.tyt.plat.vo.invoice;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/07/18 20:44
 */
@Data
public class ThirdDominantInfoVo {
    /**
     * ID
     */
    private Long id;

    /**
     * 服务商编码
     */
    private String serviceProviderCode;

    /**
     * 服务商名称
     */
    private String serviceProviderName;

    /**
     * 主体名称
     */
    private String dominantName;

    /**
     * 主体简称
     */
    private String dominantShortName;

    /**
     * 主体ID
     */
    private Long principalId;

    /**
     * 主体编码
     */
    private String principalCode;

    /**
     * 可签约：1-是；2-否；
     */
    private Integer signFlag;

    /**
     * 可发货：1-是；2-否；
     */
    private Integer publishFlag;

    /**
     * 签约类型：1-全部企业；2-部分企业；
     */
    private Integer signType;

    /**
     * 签约附加费率
     */
    private BigDecimal signTaxRate;

    /**
     * 是否删除：0-否；1-是；
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
