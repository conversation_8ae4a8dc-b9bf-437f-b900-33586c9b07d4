package com.tyt.plat.vo.invoice;

import lombok.Data;

import java.util.Date;

/**
 * 车维后台添加用户信息
 */
@Data
public class MaintainedUserInfoDto {
    private Long id;  //用户id
    private String trueName; //真实姓名
    private String userName; //昵称
    private String cellPhone; //电话号
    private String maintainMan; //维护人
    private Date endTime;//会员到期时间
    private Integer verifyPhotoSign; //身份认证状态
    private Integer identityType;//注册身份
    private Date ctime;//注册时间
    private Date lastTime;//上次登录时间
    private String isCar;//车辆认证状态0:未认证；1:认证成功；2：认证失败
    private Integer serveDays;//剩余天数
    private Integer userClass;
    private Integer userType;//0试用 1付费 2未激活

}
