package com.tyt.plat.vo.axb;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/6/14 上午10:46
 */
@Data
public class AxbInfoVO implements Serializable {

    /**
     * 数据的id
     */
    private Long id;

    /**
     * 成功绑定的A电话
     */
    private String telA;

    /**
     * 成功绑定的B电话
     */
    private String telB;

    /**
     * 成功绑定的X电话
     */
    private String telX;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 过期时间
     */
    private Date expirationDate;

    /**
     * 自定义字段
     */
    private String extraField;
}
