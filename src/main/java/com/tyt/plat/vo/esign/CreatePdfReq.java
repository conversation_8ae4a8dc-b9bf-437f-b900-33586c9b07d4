package com.tyt.plat.vo.esign;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/02/05 11:02
 */
@Data
public class CreatePdfReq {

    /**
     * pdf文件 base64
     */
    @NotNull
    private String pdfFileBase64;

    /**
     * pdf文件名
     */
    @NotNull
    private String pdfFileName;

    /**
     * 模板中包含待填充文本域时，文本域Key-Value组合
     */
    private Map<String, Object> txtFields;

}
