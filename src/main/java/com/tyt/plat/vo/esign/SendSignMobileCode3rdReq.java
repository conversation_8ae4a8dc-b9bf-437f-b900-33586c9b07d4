package com.tyt.plat.vo.esign;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/05 11:02
 */
@Data
public class SendSignMobileCode3rdReq {

    /**
     * 签署者账号标识
     */
    @NotNull
    private String accountId;
    /**
     * 待接收短信验证码的手机
     */
    @NotNull
    private String mobile;
    /**
     * 标记是否为国外账号，默认true
     * <p>
     * true-国内手机号
     * false-国外手机号
     */
    private boolean isInland = true;

}
