package com.tyt.plat.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 技术服务费计算结果
 *
 * <AUTHOR>
 * @since 2025-07-25 14:25
 */
@Data
public class TecServiceFeeConfigComputeResultVO {
    /**
     * 适用货源（单选）1:专车；2:普货；3:优车1.0；4:优车2.0；5:运满满
     */
    private Integer applyTransportType;

    /**
     * 专车签约合作商类型 1:平台；2:非平台
     */
    private Integer specialCarCooperativeType;

    /**
     * 订金模式 0:不限；1:退还；2:不退还
     */
    private Integer refundFlagType;

    /**
     * 车会员状态 0:不限；1:会员用户；2:非会员用户
     */
    private Integer carMemberType;

    /**
     * 价格模式 0:不限；1:有价电议；2:无价电议；3:一口价
     */
    private Integer pricePublishType;

    /**
     * 超时免佣 0:关闭；1:开启
     */
    private Integer freeTecServiceFeeType;

    /**
     * X分钟后免佣
     */
    private Integer freeTecServiceFeeTime;

    /**
     * 是否使用虚拟号 0:关闭；1:开启
     */
    private Integer privacyPhoneType;

    /**
     * 抽佣货源权益
     */
    private String interestsWord;

    /**
     * 抽佣货源权益链接
     */
    private String interestsUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建人用户名
     */
    private String createUserName;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改人ID
     */
    private Long modifyUserId;

    /**
     * 修改人用户名
     */
    private String modifyUserName;

    /**
     * 超时免佣满足X人查看
     */
    private Integer freeTecServiceFeeViewCount;

    /**
     * 超时免佣满足X人拨打
     */
    private Integer freeTecServiceFeeCallCount;

    /**
     * 好货标签类型：1-全部，2-部分
     */
    private Integer goodTransportLabelType;

    /**
     * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:其他
     */
    private Integer goodTransportLabel;

    /**
     * 路线类型：0-通用，1-指定路线
     */
    private Integer routeType;

    /**
     * 出发地
     */
    private String startCity;

    /**
     * 目的地
     */
    private String destCity;

    /**
     * 里程数低值
     */
    private BigDecimal distanceMin;

    /**
     * 里程数高值
     */
    private BigDecimal distanceMax;

    /**
     * 是否是多车找货并且有支付成功订单
     */
    private Boolean isMultiCarHavePayOrder = false;

    /**
     * 折后技术服务费
     */
    private BigDecimal tecServiceFee;

    /**
     * 折前技术服务费，after写错了
     */
    private BigDecimal tecServiceFeeBeforeDiscount;

    //超时免佣准备状态 0:未准备就绪；1:准备就绪
    private Boolean freeTecServiceFeeIsReady;

    //超时免佣准备就绪时间
    private Date freeTecServiceFeeIsReadyTime;

    //免佣原因：
    private List<Integer> needFreeTecTypeList;

    //是否使用抽佣分数分段配置
    private Boolean useCommissionScoreStageConfig;

    //抽佣分数
    private BigDecimal commissionScore;

    //无价货源优车指导价
    private String goodCarPriceTransportCarryPrice;

    /**
     * 抽佣分段配置随机值
     */
    private Integer transportProportionNum;

    /**
     * 运费低值
     */
    private BigDecimal priceMin;

    /**
     * 运费高值
     */
    private BigDecimal priceMax;

    /**
     * 技术服务费抽取运费百分比
     */
    private BigDecimal tecServiceFeeRate;

    /**
     * 技术服务费低值
     */
    private BigDecimal tecServiceFeeMin;

    /**
     * 技术服务费高值
     */
    private BigDecimal tecServiceFeeMax;

    /**
     * 折扣时间（距首发X分钟内）
     */
    private Integer discountTime;

    /**
     * 限时折扣（最终折扣），0-10
     */
    private BigDecimal discount;

    /**
     * 折扣配置（用于定时任务打折）
     */
    private String discountConfig;

    /**
     * 所有折扣配置
     */
    private String allDiscount;

    /**
     * 满足抽佣条件1的最早时间
     */
    private Date matchConditionEarliestTime;

    /**
     * 抽佣运费：有价货源直接取运费；无价货源取优车定价指导价最低值，取不到这位null
     */
    private BigDecimal commissionPrice;

    /**
     * 抽佣金额计算抹零方式 0：对10向下取整；1:向下取整
     */
    private Integer tecServiceFeeRoundingType;

    /**
     * 是否需要走免佣逻辑
     */
    private boolean isNeedFreeCommission = false;

    /**
     * 是否满足抽佣条件 0：不满足 1：满足
     */
    private Integer meetCommissionRules = 0;
}
