package com.tyt.plat.vo.other;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 小会员套餐配置表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-08-23
 */
@Getter
@Setter
public class GoodsSmallDO {

    public static final int GOODS_SMALL_STATUS_VALID = 1;

    public static final int GOODS_SMALL_DELETE_FLAG_VALID = 0;

    /**
     * ID
     */
    private Long id;

    /**
     * 套餐名称
     */
    private String mealName;

    /**
     * 用户类型：1-车；2-货；
     */
    private Integer userType;

    /**
     * 有效期天数
     */
    private Integer effectiveDays;

    /**
     * 货源类型：1-不限货类；
     */
    private Integer transportType;

    /**
     * 套餐次数
     */
    private Integer useNum;

    /**
     * 套餐原价
     */
    private BigDecimal originalPrice;

    /**
     * 套餐现价
     */
    private BigDecimal currentPrice;

    /**
     * tyt_goods表主键
     */
    private Long goodsId;

    /**
     * 套餐状态：1-有效；2-无效；
     */
    private Integer status;

    /**
     * 删除状态：0-未删除；1-已删除；
     */
    private Integer deleteFlag;

    /**
     * 操作人ID
     */
    private Long actionUserId;

    /**
     * 操作人姓名
     */
    private String actionUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;
}
