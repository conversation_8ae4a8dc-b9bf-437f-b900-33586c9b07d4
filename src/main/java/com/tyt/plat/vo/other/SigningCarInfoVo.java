package com.tyt.plat.vo.other;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 车辆司机信息实体
 * <AUTHOR>
 * @since 2024-06-27 18:16
 */
@Data
public class SigningCarInfoVo {
    /**
     * 车辆表主键ID
     */
    private Long carInfoId;

    /**
     * 驾驶能力
     */
    private String drivingAbility;

    /**
     * 签约合作商
     */
    private String signing;

    /**
     * 司机认证状态
     */
    private Integer verifyStatus;

    /**
     * 车头信息
     */
    private String headCityNo;

    /**
     * 挂车信息
     */
    private String tailCityNo;

    /**
     * 位置
     */
    private String location;

    /**
     * 距离
     */
    private BigDecimal distance;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 车主电话
     */
    private String phone;

    /**
     * 运输距离偏好
     */
    private String distancePreference;

    /**
     * 司机用户id
     */
    private Long driverUserId;

    /**
     * 司机标签：1-兼职运力，2-全职运力
     */
    private Integer driverTag;

    /**
     * 车型
     */
    private String carType;

    /**
     * 接单率
     */
    private BigDecimal receivingOrders;

    /**
     * 好评率
     */
    private BigDecimal favorableComment;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SigningCarInfoVo that = (SigningCarInfoVo) o;
        return Objects.equals(carInfoId, that.carInfoId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(carInfoId, drivingAbility, signing, headCityNo, location, distance);
    }
}
