package com.tyt.plat.vo.other;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class GoodCarPriceTransportTabAndBIPriceVO {

    //是否展示优车定价货源卡片
    private Boolean showTab;

    //优车定价低值
    private Integer fixPriceMin;

    //优车定价高值
    private Integer fixPriceMax;

    //优车定价最快成交价格
    private Integer fixPriceFast;

    //最小阻断价格
    private Integer thMinPrice;

    //最大阻断价格
    private Integer thMaxPrice;

    private String distance;

    //是否允许可电议 (1可电议，0不可电议)
    private Integer isAllowTeleNegotiation;
    //路线是否支持电议有价(1支持，0不支持)
    private Integer isRouteSupportTeleNegotiation;

}
