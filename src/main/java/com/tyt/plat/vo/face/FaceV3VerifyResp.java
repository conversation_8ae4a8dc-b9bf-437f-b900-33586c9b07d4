package com.tyt.plat.vo.face;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/01/11 14:08
 */
@NoArgsConstructor
@Data
public class FaceV3VerifyResp {

    private Integer timeUsed;

    private VerificationDTO verification;
    private AttackResultDTO attackResult;
    private RiskInfoDTO riskInfo;
    private String requestId;
    private ImagesDTO images;
    private String bizNo;
    private String resultMessage;
    private Integer resultCode;
    private String error;

    @NoArgsConstructor
    @Data
    public static class VerificationDTO {
        private IdcardDTO idcard;

        @NoArgsConstructor
        @Data
        public static class IdcardDTO {
            private Double confidence;
            private ThresholdsDTO thresholds;

            @NoArgsConstructor
            @Data
            public static class ThresholdsDTO {
                private Double $1e3;
                private Double $1e5;
                private Double $1e4;
                private Double $1e6;
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class AttackResultDTO {
        private Double score;
        private Double threshold;
        private Boolean result;
    }

    @NoArgsConstructor
    @Data
    public static class RiskInfoDTO {
        private String deviceInfoLevel;
        private DeviceInfoTagsDTO deviceInfoTags;

        @NoArgsConstructor
        @Data
        public static class DeviceInfoTagsDTO {
            private Integer isRoot;
            private Integer isHook;
            private Integer isInjection;
            private Integer isVirtualEnvironment;
        }
    }

    @NoArgsConstructor
    @Data
    public static class ImagesDTO {
        private String imageBest;
    }
}
