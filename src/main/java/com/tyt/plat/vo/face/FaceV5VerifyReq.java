package com.tyt.plat.vo.face;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 *
 * <AUTHOR>
 * @since 2024/01/11 14:08
 */
@NoArgsConstructor
@Data
public class FaceV5VerifyReq {
    /**
     * “默认为空”。客户业务流水号，建议设置为您的业务相关的流水串号并且唯一，
     * 会在return时原封不动的返回给您的服务器，以帮助您确认对应业务的归属。此字段不超过128字节
     */
    private String bizNo;
    /**
     * 本次请求的验证方式
     * 0：人脸比对
     * 1：人脸核身
     * 2：实证核身
     */
    @NotBlank(message = "comparisonType 不能为空")
    private String comparisonType;
    /**
     * 验证数据来源
     * 0：通过SDK 5.0.0以上进行活体验证
     * 1：自传照片进行活体验证
     */
    @NotBlank(message = "dataType 不能为空")
    private String dataType;

    /**
     * verify场景id：本次验证请求相关配置在控台可设置，并将生成的id号在接口中使用
     */
    @NotBlank(message = "verifyId 不能为空")
    private String verifyId;

    /**
     * get_biz_token接口获取的biz_token；用以标识本次核验数据对象
     */
    @NotBlank(message = "bizToken 不能为空")
    private String bizToken;

    /**
     * 是否开启传输数据加密，详细说明见：加密说明
     * 0：不开启
     * 1：SM2
     * 2：RSA
     */
    @NotBlank(message = "encryptionType 不能为空")
    private String encryptionType;

    /**
     * 需要核实对象的姓名，使用UTF-8编码
     */
    @NotBlank(message = "idCardName 不能为空")
    private String idCardName;

    /**
     * 需要核实对象的证件号码，也就是一个18位长度的字符串
     */
    @NotBlank(message = "idCardNumber 不能为空")
    private String idCardNumber;
}
