package com.tyt.plat.vo.face;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/01/11 14:43
 */
@Data
public class FaceV3VerifyReq {

    /**
     * 通过”App-GetBizToken“ API接口获取到的biz_token
     */
    @NotNull(message = "biz_token 不能为空")
    private String bizToken;

    /**
     * 若在App-GetBizToken时，使用的活体类型参数为meglive、still、flash（即：liveness_type = meglive 或 liveness_type = still 或
     * liveness_type = flash），则此参数为必选参数。此参数需要上传由FaceID MegLiveStill SDK 3
     * .0及以上版本生成的数据，内容包括活体验证过程中的数据，和采集到的人脸数据。请不要对数据包做任何调整，直接提交接口即可
     * 若在App-GetBizToken时，使用的活体类型参数为raw_image（即：liveness_type =
     * raw_image），则此参数为可选参数，API会返回在App-GetBizToken时传递的带人脸图片的比对结果
     * 返回值说明
     */
    //private MultipartFile megliveData;
    private String megliveDataBase64;

}
