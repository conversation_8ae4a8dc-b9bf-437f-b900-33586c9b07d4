package com.tyt.plat.vo.face;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/01/11 11:31
 */
@Data
public class FaceV3TokenResp {

    /**
     * 用于区分每一次请求的唯一的字符串，此字符串可以用于后续数据反查。此字段必定返回
     */
    @JsonProperty("biz_token")
    private String bizToken;
    /**
     * 整个请求所花费的时间，单位为毫秒，此字段必定返回
     */
    @JsonProperty("time_used")
    private Integer timeUsed;
    /**
     * 字符串，调用SDK时传入的信息，biz_token有效期默认1小时
     */
    @JsonProperty("request_id")
    private String requestId;
    /**
     * 当请求失败时才会返回此字符串，具体返回内容见后续错误信息章节。否则此字段不存在
     */
    private String error;
}
