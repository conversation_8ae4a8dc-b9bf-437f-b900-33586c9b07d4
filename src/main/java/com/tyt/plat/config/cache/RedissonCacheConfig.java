package com.tyt.plat.config.cache;

import com.tyt.user.service.TytConfigService;
import com.tyt.util.RedissonUtils;
import org.redisson.spring.cache.CacheConfig;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/6/5 下午5:14
 */
@Configuration
@EnableCaching
public class RedissonCacheConfig {

    private final TytConfigService configService;

    /**
     * 最长过期时间 超过缓存会被删除
     */
    private final long ttl = TimeUnit.MINUTES.toMillis(5);
    /**
     * 最大空闲时间 超过上次访问时间30m缓存会被删除
     */
    private final long maxIdleTime = TimeUnit.MINUTES.toMillis(2);

    public RedissonCacheConfig(TytConfigService configService) {
        this.configService = configService;
    }

    @Bean(name = "redissonCacheManager")
    public CacheManager redissonCacheManager() {
        String env = configService.getStringValue("tyt:config:environment");

        CacheConfig cacheConfig;
        if (Objects.equals(env, "online") || Objects.equals(env, "release")) {
            cacheConfig = new CacheConfig(ttl, maxIdleTime);
        } else {
            cacheConfig = new CacheConfig(1, 1);
        }

        return new CustomRedissonSpringCacheManager(RedissonUtils.getClient(), cacheConfig);
    }
}
