package com.tyt.plat.config.cache;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.interceptor.BasicOperation;
import org.springframework.cache.interceptor.CacheOperationInvocationContext;
import org.springframework.cache.interceptor.SimpleCacheResolver;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class UserIdCacheResolver extends SimpleCacheResolver {

    public UserIdCacheResolver(CacheManager cacheManager) {
        super(cacheManager);
    }

    @Override
    protected Collection<String> getCacheNames(CacheOperationInvocationContext<?> context) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            return super.getCacheNames(context);
        }

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
        HttpServletRequest request = servletRequestAttributes.getRequest();
        String contentType = request.getContentType();
        String userId = request.getParameter("userId");

        if (StringUtils.isNotBlank(contentType) && contentType.indexOf("multipart/form-data") != -1) {
            TreeMap<String, String> paramMap = getFormDataParam(request);
            userId = Objects.toString(paramMap.get("userId"), null);
        }

        if (StringUtils.isBlank(userId)) {
            return super.getCacheNames(context);
        }

        Optional<Set<String>> optional = Optional.ofNullable(context).map(CacheOperationInvocationContext::getOperation).map(BasicOperation::getCacheNames);
        if (!optional.isPresent()) {
            return super.getCacheNames(context);
        } else {
            final String finalUserId = userId;

            return optional.get()
                    .stream().map(it -> String.format("%s:userId:%s", it, finalUserId))
                    .collect(Collectors.toSet());
        }
    }

    /**
     * form-data请求方式参数获取
     *
     * @param httpRequest
     */
    private TreeMap<String, String> getFormDataParam(HttpServletRequest httpRequest) {
        DiskFileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload sfu = new ServletFileUpload(factory);
        try {
//			Map<String, String[]> parameterMap = httpRequest.getParameterMap();
//			logger.info("getFormDataParam httpRequest class:{} url:{}",parameterMap.getClass(),httpRequest.getServletPath());
            StringBuffer str = new StringBuffer();
            List<FileItem> list = sfu.parseRequest(httpRequest);
            TreeMap<String, String> paramMap = new TreeMap<>();
            for (FileItem fileItem : list) {
                //判断是否是普通表单
                if (fileItem.isFormField()) {
                    paramMap.put(fileItem.getFieldName(), fileItem.getString("UTF-8"));
                    str.append(fileItem.getFieldName() + "=" + fileItem.getString("UTF-8") + "&");

                    //parameterMap.put(fileItem.getFieldName(), new String[]{fileItem.getString()});

                }
            }
            return paramMap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new TreeMap<>();
    }

}
