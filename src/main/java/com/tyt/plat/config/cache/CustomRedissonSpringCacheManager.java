package com.tyt.plat.config.cache;

import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/6/5 下午5:15
 */
public class CustomRedissonSpringCacheManager extends RedissonSpringCacheManager {

    private final CacheConfig defaultCacheConfig;

    public CustomRedissonSpringCacheManager(RedissonClient redisson, CacheConfig defaultCacheConfig) {
        super(redisson);
        this.defaultCacheConfig = defaultCacheConfig;
    }

    public CustomRedissonSpringCacheManager(RedissonClient redisson, Map<String, ? extends CacheConfig> config, CacheConfig defaultCacheConfig) {
        super(redisson, config);
        this.defaultCacheConfig = defaultCacheConfig;
    }

    public CustomRedissonSpringCacheManager(RedissonClient redisson, Map<String, ? extends CacheConfig> config, Codec codec, CacheConfig defaultCacheConfig) {
        super(redisson, config, codec);
        this.defaultCacheConfig = defaultCacheConfig;
    }

    public CustomRedissonSpringCacheManager(RedissonClient redisson, String configLocation, CacheConfig defaultCacheConfig) {
        super(redisson, configLocation);
        this.defaultCacheConfig = defaultCacheConfig;
    }

    public CustomRedissonSpringCacheManager(RedissonClient redisson, String configLocation, Codec codec, CacheConfig defaultCacheConfig) {
        super(redisson, configLocation, codec);
        this.defaultCacheConfig = defaultCacheConfig;
    }

    @Override
    protected CacheConfig createDefaultConfig() {
        return defaultCacheConfig;
    }
}
