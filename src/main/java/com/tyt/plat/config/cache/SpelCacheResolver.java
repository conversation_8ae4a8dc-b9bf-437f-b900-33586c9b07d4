package com.tyt.plat.config.cache;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.interceptor.CacheOperationInvocationContext;
import org.springframework.cache.interceptor.SimpleCacheResolver;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 使cacheName应用为spel的解析器，如果你不知道怎么用，那么就不要使用它
 */
@Component
public class SpelCacheResolver extends SimpleCacheResolver {

    public SpelCacheResolver(CacheManager cacheManager) {
        super(cacheManager);
    }

    @Override
    protected Collection<String> getCacheNames(CacheOperationInvocationContext<?> context) {
        return context.getOperation().getCacheNames()
                .stream().map(it -> getBySpringElKey(context.getArgs(), context.getMethod(), it))
                .collect(Collectors.toSet());
    }

    private String getBySpringElKey(Object[] args, Method method, String key) {
        if (StringUtils.isBlank(key)) {
            return "";
        }

        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(key);
        //设置解析上下文(有哪些占位荷,以及每种占位符的值-根据方法的参数名和参数值
        EvaluationContext context = new StandardEvaluationContext();

        String[] parameterNames = new DefaultParameterNameDiscoverer().getParameterNames(method);
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }
        return expression.getValue(context).toString();
    }
}
