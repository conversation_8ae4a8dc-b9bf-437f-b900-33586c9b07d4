package com.tyt.plat.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运单状态 1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TransportStatusEnum {
    VALID(1, "有效"),
    INVALID(0, "无效"),
    PENDING(2, "待定"),
    BLOCKED(3, "阻止"),
    DEAL(4, "成交"),
    CANCEL(5, "取消");

    private final Integer code;
    private final String desc;

}
