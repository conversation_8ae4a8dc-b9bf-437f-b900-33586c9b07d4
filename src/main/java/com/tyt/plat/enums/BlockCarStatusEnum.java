package com.tyt.plat.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/12/11 13:23
 */
public enum BlockCarStatusEnum {

    CONFIG_TYPE_ZERO("0", "认证中"),
    CONFIG_TYPE_ONE("1", "认证成功"),
    CONFIG_TYPE_TWO("2", "认证失败")
    ;

    @Getter
    private final String status;
    @Getter
    private final String name;

    BlockCarStatusEnum(String status, String name) {
        this.status = status;
        this.name = name;
    }


    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getStatus().equals(reqCode);
        return result;
    }
}
