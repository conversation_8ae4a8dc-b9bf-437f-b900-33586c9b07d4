package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 货源排序方式排序枚举
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum TsSortTypeEnum {

    top(0, "正常排序"),
    bottom(1, "沉底排序"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private TsSortTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            reqCode = TsSortTypeEnum.top.getCode();
        }

        return this.code == reqCode;
    }

}