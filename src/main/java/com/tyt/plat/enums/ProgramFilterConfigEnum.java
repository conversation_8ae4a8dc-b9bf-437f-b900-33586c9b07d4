package com.tyt.plat.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * url 拦截配置
 *
 * <AUTHOR>
 * @date 2023/12/5 15:53
 */
public enum ProgramFilterConfigEnum {

    //司货课堂预览
    DIAL_N_USE_RECORD("/plat/cover/goods/dial/n/use/record", true, true),
    //获取车方抢单豆信息
    COVER_GOODS_DIAL_INFO("/plat/cover/goods/dial/info", true, true),
    //获取车方抢单豆信息
    USER_CONFIG_LIST("/plat/user/config/list", true, true),
    //运营中台.获取页面详情接口
    USER_GET("/plat/user/get.action", true, true),
    COVER_GOODS_DIAL_SEND_CONFIG_DEFAULT("/plat/cover/goods/dial/send/config/default", true, true),
    USER_CONFIG_UPDATE("/plat/user/config/update", true, true),
    FIND_TRANSPORT_LOG_BI_DATA("/plat/transport/addFindTransportLogBIData", true, true),
    ;

    /**
     * url.
     */
    @Getter
    private final String url;
    /**
     * 跳过验签.
     */
    @Getter
    private final boolean skipLegal;
    /**
     * 跳过登录校验.
     */
    @Getter
    private final boolean skipSecurity;

    private Set<String> urlSet;

    ProgramFilterConfigEnum(String url, boolean skipLegal, boolean skipSecurity) {
        this.url = url;
        this.skipLegal = skipLegal;
        this.skipSecurity = skipSecurity;
    }


    public static Set<String> getUrlSet() {

        return Arrays.stream(ProgramFilterConfigEnum.values()).map(ProgramFilterConfigEnum::getUrl).collect(Collectors.toSet());

    }

}
