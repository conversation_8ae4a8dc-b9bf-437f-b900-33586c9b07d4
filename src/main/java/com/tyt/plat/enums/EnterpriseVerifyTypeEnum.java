package com.tyt.plat.enums;

import lombok.Getter;
/**
 * 企业认证类型
 *
 * <AUTHOR>
 * @date 2023-4-24 14:16:10
 */
public enum EnterpriseVerifyTypeEnum {

    INFO(1, "基本信息"),
    CONTRACT(2, "合同信息"),
    CERTIGIER(3, "授权人信息"),
    OTHER_TYPE(0, "其他类型")
    ;

    @Getter
    private final int type;
    @Getter
    private final String name;

    EnterpriseVerifyTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }
    public static EnterpriseVerifyTypeEnum getEnum(int type){
        EnterpriseVerifyTypeEnum[] enums = EnterpriseVerifyTypeEnum.values();
        for (EnterpriseVerifyTypeEnum e: enums) {
            if (e.type == type) {
                return e;
            }
        }
        return EnterpriseVerifyTypeEnum.OTHER_TYPE;
    }
}
