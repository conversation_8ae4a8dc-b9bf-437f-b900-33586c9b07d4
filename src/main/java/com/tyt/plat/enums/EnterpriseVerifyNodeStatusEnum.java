package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 企业认证状态
 *
 * <AUTHOR>
 * @date 2023-4-24 14:38:48
 */
public enum EnterpriseVerifyNodeStatusEnum {

    /**
     * tyt_invoice_enterprise 表 info_verify_status
     */
    UNVERIFIED(0, "未认证"),
    VERIFING(1, "认证中"),
    VERIFIED(2, "认证通过"),
    VERIFY_REJECT(3, "认证驳回"),
    VERIFY_FAIL(4, "认证失败"),
    VERIFY_PAST(5, "认证过期")
    ;

    @Getter
    private final Integer status;
    @Getter
    private final String name;

    EnterpriseVerifyNodeStatusEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getStatus().equals(reqCode);
        return result;
    }

}
