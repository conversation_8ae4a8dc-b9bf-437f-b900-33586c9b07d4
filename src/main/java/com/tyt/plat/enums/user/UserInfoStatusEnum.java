package com.tyt.plat.enums.user;

import lombok.Getter;

/**
 * 认证状态.
 * <AUTHOR>
 * @date 2024-2-22 09:53:28
 */
public enum UserInfoStatusEnum {
    //（0未认证;1认证成功;2认证失败）
    UNAUTH(0, "未认证"),
    SUCCESS(1, "认证成功"),
    FAIL(2, "认证失败"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    UserInfoStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}