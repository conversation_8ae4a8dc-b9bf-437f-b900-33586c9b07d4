package com.tyt.plat.enums.enterprise;

import lombok.Getter;

/**
 * 授权激活类型
 * tyt_invoice_enterprise_sign 表 sign_type字段
 * <AUTHOR>
 * @date 2024-2-22 09:53:28
 */
public enum SignTypeEnum {
    //（1法人授权;2委托书授权）
    LEGAL(1, "法人授权"),
    AUTH(2, "委托书授权"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    SignTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}