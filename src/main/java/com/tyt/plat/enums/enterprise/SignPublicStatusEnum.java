package com.tyt.plat.enums.enterprise;

import lombok.Getter;

/**
 * 企业协议签署公用状态
 * <AUTHOR>
 * @date 2024-2-22 09:53:28
 */
public enum SignPublicStatusEnum {
    //(0未认证;1认证中;2认证成功;3认证失败)
    INIT(0, "未认证"),
    WAITING(1, "认证中"),
    SUCCESS(2, "已认证"),
    FAIL(3, "认证失败"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    SignPublicStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}