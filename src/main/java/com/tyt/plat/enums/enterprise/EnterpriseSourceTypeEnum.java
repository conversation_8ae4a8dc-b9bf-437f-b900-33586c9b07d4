package com.tyt.plat.enums.enterprise;

import lombok.Getter;

/**
 * 企业来源类型
 * tyt_invoice_enterprise 表
 * <AUTHOR>
 * @date 2024-6-3 16:00:15
 */
public enum EnterpriseSourceTypeEnum {
    //（1法人授权;2委托书授权）
    CAR(1, "车"),
    GOODS(2, "货"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    EnterpriseSourceTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}