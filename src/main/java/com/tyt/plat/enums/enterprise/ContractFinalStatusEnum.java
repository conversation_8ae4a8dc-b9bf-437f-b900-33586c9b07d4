package com.tyt.plat.enums.enterprise;

import lombok.Getter;

/**
 * 企业协议最终状态
 * <AUTHOR>
 * @date 2024-2-22 09:53:28
 */
public enum ContractFinalStatusEnum {
    //企业最终激活状态(0未激活;1激活中;2激活成功;3激活失败)
    INIT(0, "未激活"),
    WAITING(1, "激活中"),
    SUCCESS(2, "激活成功"),
    FAIL(3, "激活失败"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    ContractFinalStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}