package com.tyt.plat.enums.enterprise;

import lombok.Getter;

/**
 * 合同状态.
 * <AUTHOR>
 * @date 2024-2-22 09:53:28
 */
public enum ContractStatusEnum {
    //框架协议状态(0未签署;1签署中;2已签署;3签署失败;4已失效)
    INIT(0, "未签署"),
    WAITING(1, "签署中"),
    SUCCESS(2, "已签署"),
    FAIL(3, "签署失败"),
    EXPIRE(4, "已失效"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    ContractStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}