package com.tyt.plat.enums.enterprise;

import lombok.Getter;

/**
 * 签署状态
 * e签宝签署状态返回值
 * <AUTHOR>
 * @date 2024-2-22 09:53:28
 */
public enum AuthSignResultEnum {
    /**
     * NOTSTART - 授权书签署任务已提交
     * SIGNING - 授权书签署通知已发送法定代表人，但未签署
     * FINISHED - 法定代表人完成授权书签署
     * SIGNFAILED - 授权书签署超过签署截止时间或法定代表人拒绝签署等导致的签署失败
     */
    NOTSTART("授权书签署任务已提交"),
    SIGNING("授权书签署通知已发送法定代表人，但未签署"),
    FINISHED("法定代表人完成授权书签署"),
    SIGNFAILED("授权书签署超过签署截止时间或法定代表人拒绝签署等导致的签署失败"),
    ;

    @Getter
    private final String zhName;

    AuthSignResultEnum(String zhName) {
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(String reqCode) {
        if(reqCode == null){
            return false;
        }
        boolean result = this.name().equals(reqCode);
        return result;
    }

}