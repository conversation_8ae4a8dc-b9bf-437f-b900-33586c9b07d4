package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 货源标签code
 *
 * <AUTHOR>
 * @date 2022/8/26 15:59
 */
public enum SourceGroupCodeEnum {

    remark("transport_label", "备注"),
    carLength("car_length_label", "长度标签"),
    tailCarStyle("tail_car_style", "挂车样式"),
    tailCarType("tail_car_type", "挂车型号"),

    ;
    @Getter
    private String groupCode;

    @Getter
    private String zhName;

    SourceGroupCodeEnum(String groupCode, String zhName) {
        this.groupCode = groupCode;
        this.zhName = zhName;
    }

}
