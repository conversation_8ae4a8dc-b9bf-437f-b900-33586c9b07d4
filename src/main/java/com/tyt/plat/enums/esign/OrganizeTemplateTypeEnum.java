package com.tyt.plat.enums.esign;

import lombok.Getter;

public enum OrganizeTemplateTypeEnum {
    /**
     * e签宝枚举
     */
    STAR(EsignTemplateEnum.STAR, "simsun"),
    OVAL(EsignTemplateEnum.OVAL, "simsun"),
    RECT(EsignTemplateEnum.RECT, "simsun"),
    DEDICATED(EsignTemplateEnum.DEDICATED, "simsun");

    @Getter
    private final EsignTemplateEnum template;

    @Getter
    private final String fontName;

    OrganizeTemplateTypeEnum(EsignTemplateEnum template, String fontName) {
        this.template = template;
        this.fontName = fontName;
    }

}
