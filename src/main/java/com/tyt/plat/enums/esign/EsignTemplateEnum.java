package com.tyt.plat.enums.esign;

import lombok.Getter;

public enum EsignTemplateEnum {
    /**
     * e签宝枚举
     */
    SQUARE("square"),
    RECTANGLE("rectangle"),
    FZKC("fzkc"),
    YYGXSF("yygxsf"),
    HYLSF("hylsf"),
    STAR("star"),
    OVAL("oval"),
    BORDERLESS("borderless"),
    HWLS("hwls"),
    YGYJFCS("ygyjfcs"),
    YGYMBXS("ygymbxs"),
    HWXKBORDER("hwxkborder"),
    HWXK("hwxk"),
    RECT("rect"),
    DEDICATED("dedicated");

    @Getter
    private final String disc;

    EsignTemplateEnum(String disc) {
        this.disc = disc;
    }

}
