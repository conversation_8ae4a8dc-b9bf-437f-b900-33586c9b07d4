package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 阿里云 验证码类型.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum ValidCodeTypeEnum {

    /**
     * 实名认证
     */
    USER_VERIFY_CHANGE("user_verify_change", "SMS_464805884", "您正在修改实名认证信息，验证码${verifyCode}，在10分钟内有效，如非本人操作，请忽略此短信。", "用户实名认证修改验证码"),

    ;

    @Getter
    private final String templateKey;

    @Getter
    private final String templateCode;

    @Getter
    private final String templateContent;

    @Getter
    private final String zhName;

    ValidCodeTypeEnum(String templateKey, String templateCode, String templateContent, String zhName) {
        this.templateKey = templateKey;
        this.templateCode = templateCode;
        this.templateContent = templateContent;
        this.zhName = zhName;
    }

}