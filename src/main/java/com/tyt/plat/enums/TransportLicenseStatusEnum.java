package com.tyt.plat.enums;

import lombok.Getter;

/**
 * tyt_user_identity_auth 表 enterprise_auth_status 字段.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum TransportLicenseStatusEnum {

    //道路运输许可证认证状态(0-未提交;1-审核中;2-已通过;3-审核驳回)
    nulldefault(0, "未提交"),
    submit(1, "审核中"),
    success(2, "已通过"),
    reject(3, "审核驳回"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private TransportLicenseStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}
