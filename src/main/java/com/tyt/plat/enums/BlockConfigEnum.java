package com.tyt.plat.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/12/11 13:23
 */
public enum BlockConfigEnum {

    CONFIG_TYPE_ONE(1, "全部"),
    CONFIG_TYPE_TWO(2, "手机号归属地"),
    CONFIG_TYPE_THREE(3, "名单上传")
    ;

    @Getter
    private final Integer status;
    @Getter
    private final String name;

    BlockConfigEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }


    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getStatus().equals(reqCode);
        return result;
    }
}
