package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 企业激活状态
 * <AUTHOR>
 * @date 2024-2-21 13:48:19
 */
public enum EnterpriseActiveStatusEnum {

    /**
     * 企业最终激活状态(0未激活;1激活中;2激活成功;3激活失败)
     * tyt_invoice_enterprise certigier_verify_status
     */
    NOT_ACTIVE(0, "未激活"),
    WAITING(1, "激活中"),
    SUCCESS(2, "激活成功"),
    FAIL(3, "激活失败")
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    EnterpriseActiveStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}
