package com.tyt.plat.enums;

import com.tyt.service.common.entity.ResponseCode;

public enum PlatResponseEnum {

    /**
     * 重复货源.
     */
    transport_duplicate(2007, "您已经发布过此类货源！"),

    USER_INFO_CHANGE_BLOCK(5101, "钱包已实名认证，无法修改实名认证信息！"),

    ENTERPRISE_NOT_REAL_VERIFY(5201, "企业未进行在线验真！"),
    ENTERPRISE_NOT_MANAGER(5202, "非当前企业管理员权限！"),
    ENTERPRISE_ONLINE_FREIGHT(5203, "认证企业为网货平台！"),
    ENTERPRISE_TYPE_ERROR(5204, "企业类型错误！"),

    NOT_FACE_VERIFY(5205, "未进行人脸识别！"),
    ORDER_EXIST(5206, "您有未支付运费的大件宝订单，请完单后再更换企业！"),
    TRANDPORT_EXIST(5207, "您有发布中的大件宝货源，请先撤销货源后再更换企业!"),

    ESIGN_API_ERROR(901001, "e签宝api错误！"),

    /**
     * 重复货源.
     */
    INTERNAL_CALL_ERROR(590001, "请求服务繁忙，请稍候重试！"),

    ;

    private final Integer code;
    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    PlatResponseEnum(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public ResponseCode info(){
        ResponseCode respCode = new ResponseCode(this.code, this.msg);
        return respCode;
    }

    public ResponseCode info(String reqMsg){

        ResponseCode respCode = new ResponseCode(this.code, reqMsg);

        return respCode;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

    public static void main(String[] args) {

        String respCode = "no_found1";

        System.out.println(respCode);

    }

}
