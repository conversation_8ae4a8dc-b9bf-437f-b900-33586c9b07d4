package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 认证状态.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum VerifyStatusEnum {
    //（0未认证;1认证成功;2认证失败）
    UNAUTH(0, "未认证"),
    SUCCESS(1, "认证成功"),
    FAIL(2, "认证失败"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private VerifyStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}