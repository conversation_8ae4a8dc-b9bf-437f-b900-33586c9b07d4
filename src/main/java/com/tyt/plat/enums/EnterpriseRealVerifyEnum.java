package com.tyt.plat.enums;

import lombok.Getter;

/**
 *
 * 天眼查验真
 * <AUTHOR>
 * @date 2024-2-21 13:48:19
 */
public enum EnterpriseRealVerifyEnum {

    /**
     * 是否进行过天眼查企业核验 0：未进行；1：核验成功；2：核验失败
     * tyt_invoice_enterprise real_verify
     */
    NOT_VERIFY(0, "未进行"),
    SUCCESS(1, "核验成功"),
    FAIL(2, "核验失败")
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    EnterpriseRealVerifyEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode reqCode
     * @return boolean
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}
