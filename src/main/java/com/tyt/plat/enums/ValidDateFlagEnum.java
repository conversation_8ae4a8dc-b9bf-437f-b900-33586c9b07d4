package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 过期状态枚举.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum ValidDateFlagEnum {
    //全局状态枚举
    UNKNOWN(0, "未知"),
    VALID(1, "有效"),
    LOW_VALID(2, "即将过期"),
    EXPIRE(3, "已过期"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    ValidDateFlagEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}