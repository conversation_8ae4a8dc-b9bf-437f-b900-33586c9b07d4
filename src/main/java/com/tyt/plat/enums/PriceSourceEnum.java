package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 数据部门价格
 *
 * <AUTHOR>
 * @date 2022/8/24 11:33
 */
public enum PriceSourceEnum {

    pay(1, "支付信息费"),
    reference(2, "发货参考价"),
    publish(3, "发货校验"),
    excellentGoods(4, "优车货源");

    @Getter
    private Integer source;

    @Getter
    private String zhName;

    private PriceSourceEnum(Integer source, String zhName) {
        this.source = source;
        this.zhName = zhName;
    }

}
