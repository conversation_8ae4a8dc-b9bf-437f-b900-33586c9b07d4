package com.tyt.plat.enums;

import lombok.Getter;

/**
 * tyt_user_identity_auth 表 enterprise_auth_status 字段.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum EnterpriseAuthStatusEnum {
    //企业认证状态 0未认证1通过2认证中3认证失败
    uncommit(0, "未认证"),
    success(1, "通过"),
    waiting(2, "认证中"),
    fail(3, "认证失败"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private EnterpriseAuthStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}