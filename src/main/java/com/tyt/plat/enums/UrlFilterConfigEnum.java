package com.tyt.plat.enums;

import lombok.Getter;

/**
 * url 拦截配置
 *
 * <AUTHOR>
 * @date 2023/12/5 15:53
 */
public enum UrlFilterConfigEnum {

    //司货课堂预览
    PREVIEW_COURSE("/userFrame/userCourse/previewCourse.action", true, true),
    //获取车方抢单豆信息
    COVER_GOODS_DIAL_USER_INFO("/plat/cover/goods/dial/getCoverGoodsDialUserInfo.action", true, true),
    //获取车方抢单豆信息
    AD_LIST_FOR_H5("/plat/adposition/adListForH5.action", true, true),
    //运营中台.获取页面详情接口
    FRONT_PAGE_DETAIL("/userFrame/frontPage/getPageDetail.action", true, true),

    SIGN_VALID_CALL_BACK("/plat/enterprise/signValidCallBack.action", true, true),

    CHECK_COMMISSION("/plat/transport/V5930/checkCommission", true, true),

    AD_LIST_GOODS_SEARCH("/plat/adposition/adlistForGoodsSearch.action", true, true),

    ;

    /**
     * url.
     */
    @Getter
    private final String url;
    /**
     * 跳过验签.
     */
    @Getter
    private final boolean skipLegal;
    /**
     * 跳过登录校验.
     */
    @Getter
    private final boolean skipSecurity;

    UrlFilterConfigEnum(String url, boolean skipLegal, boolean skipSecurity) {
        this.url = url;
        this.skipLegal = skipLegal;
        this.skipSecurity = skipSecurity;
    }

}
