package com.tyt.plat.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description UserRecord
 * @date 2023/08/31 15:34
 */
public enum UserRecordEnum {
    hall_recommend_setting("hall_custom_setup","找货大厅个性化设置"),
    details_recommend_setting("details_custom_setup","货源详情个性化设置"),
    car_user_info_auth("car_user_info_auth","车方用户认证信息授权记录"),
    goods_user_info_auth("goods_user_info_auth","货方用户认证信息授权记录"),

    courseNotlearnTip("courseNotlearnTip","用户未学习提示按钮"),

    /**
     * 会员购买页红包弹窗.
     */
    VIP_RED_PACKET_POP("VIP_RED_PACKET_POP", "会员购买页红包弹窗"),

    EXPOSURE_READ_ID("EXPOSURE_READ_ID", "曝光卡查看id")
    ;

    private String code;

    private String remark;

    private UserRecordEnum(String code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public String getCode() {
        return code;
    }

    public String getRemark() {
        return remark;
    }

    public static String getRemarkByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return StringUtils.EMPTY;
        }
        for (UserRecordEnum userRecordEnum : UserRecordEnum.values()) {
            if (code.equals(userRecordEnum.code)) {
                return userRecordEnum.getRemark();
            }
        }
        return StringUtils.EMPTY;
    }
}
