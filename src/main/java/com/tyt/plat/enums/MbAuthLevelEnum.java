package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 客服问答展示范围.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum MbAuthLevelEnum {
    //实名等级（REAL_NAME_NONE：未实名；PERSON_REAL_NAME_FOUR：个人四要素实名；PERSON_REAL_NAME_SIX：个人六要素实名）
    REAL_NAME_NONE("REAL_NAME_NONE", "未实名"),
    PERSON_REAL_NAME_FOUR("PERSON_REAL_NAME_FOUR", "个人四要素实名"),
    PERSON_REAL_NAME_SIX("PERSON_REAL_NAME_SIX", "个人六要素实名"),
    ;

    @Getter
    private String code;

    @Getter
    private String zhName;

    private MbAuthLevelEnum(String code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(String reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}