package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 司货课堂课程学习记录， tyt_course_user 表 study_status.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum StudyStatusEnum {
    //学习状态（0未学习;1学习中;2学习完成）
    NOT_LEARN(0, "未学习"),
    IN_LEARN(1, "学习中"),
    HAS_LEARN(2, "已学习"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private StudyStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}