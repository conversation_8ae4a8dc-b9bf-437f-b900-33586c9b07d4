package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 司机认证状态枚举
 * 认证状态(0 认证中，1 认证成功，2 认证失败，3 未认证)
 *
 * <AUTHOR>
 * @since 2024-09-13 09:55
 */
@Getter
public enum DriverVerifyEnum {
    VERIFYING(0, "认证中"),
    VERIFY_SUCCESS(1, "认证成功"),
    VERIFY_FAILED(2, "认证失败"),
    NOT_VERIFY(3, "未认证")
    ;
    private Integer code;
    private String name;
    DriverVerifyEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
