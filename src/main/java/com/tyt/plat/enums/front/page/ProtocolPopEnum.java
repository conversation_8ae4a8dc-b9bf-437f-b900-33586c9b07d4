package com.tyt.plat.enums.front.page;

import lombok.Getter;

/**
 * 协议是否弹窗枚举类
 *
 * <AUTHOR>
 * @since 2024/05/10 14:36
 */
@Getter
public enum ProtocolPopEnum {

    /**
     * 不弹窗状态
     */
    NO_POP(0, "不弹"),
    /**
     * 弹窗状态
     */
    POP(1, "弹窗");

    private Integer code;
    private String desc;

    ProtocolPopEnum(Integer code, String description) {
        this.code = code;
        this.desc = description;
    }


    public static ProtocolPopEnum getByCode(Integer code) {
        for (ProtocolPopEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的code值：" + code);
    }
}
