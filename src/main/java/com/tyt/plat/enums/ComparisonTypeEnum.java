package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 客服问答展示范围.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum ComparisonTypeEnum {
    /**
     * 本次请求的验证方式
     * 0：人脸比对
     * 1：人脸核身
     * 2：实证核身
     */
    FACE_IMG("0", "人脸比对"),
    FACE_REAL("1", "人脸核身"),
    FACE_CARD("2", "实证核身"),
    ;

    @Getter
    private final String code;

    @Getter
    private final String zhName;

    ComparisonTypeEnum(String code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(String reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}