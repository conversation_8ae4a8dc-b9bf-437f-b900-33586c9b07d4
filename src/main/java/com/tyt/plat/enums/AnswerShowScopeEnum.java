package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 客服问答展示范围.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum AnswerShowScopeEnum {

    internal(1, "内部"),
    whole(2, "全部"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private AnswerShowScopeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}