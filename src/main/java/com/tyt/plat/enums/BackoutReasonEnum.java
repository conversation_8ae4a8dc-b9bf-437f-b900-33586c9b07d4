package com.tyt.plat.enums;

import lombok.Getter;

public enum BackoutReasonEnum {

    change_fixed_price(8, "转一口价撤销"),
    change_phone_price(9, "转电议撤销"),
    mb_sync_notify(10, "满帮支付成功撤销"),
    mb_cargo_sync_notify(100, "满帮货源同步撤销"),

    ;

    /**
     * =========================================
     **/

    @Getter
    private Integer code;
    @Getter
    private String msg;

    BackoutReasonEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
