package com.tyt.plat.enums;

import lombok.Getter;

/**
 * tyt_user_identity_label 表 goods_type_first 字段.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum GoodsTypeFirstEnum {
    //货主身份一级标签（1个人;2企业）
    personal(1, "个人"),
    enterprise(2, "企业"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private GoodsTypeFirstEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}