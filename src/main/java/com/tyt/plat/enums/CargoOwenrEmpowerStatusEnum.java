package com.tyt.plat.enums;

import lombok.Getter;

/**
 * 专车货主授权状态 1未授权 2已授权
 *
 * <AUTHOR>
 * @since 2024-09-29 11:15
 */
@Getter
public enum CargoOwenrEmpowerStatusEnum {
    UNAUTHORIZED(1, "未授权"),
    AUTHORIZED(2, "已授权")
    ;
    private Integer code;
    private String name;
    CargoOwenrEmpowerStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
