package com.tyt.plat.enums;

import lombok.Getter;

public enum TransportBusinessTypeEnum {

    //全局状态枚举
    invalid(-1, "无效"),
    automaticGoodCarPriceTransport(1, "自动转优车定价货源")
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    TransportBusinessTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

    ;

}
