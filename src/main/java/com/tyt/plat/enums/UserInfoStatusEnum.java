package com.tyt.plat.enums;

import lombok.Getter;

/**
 * tyt_user_identity_auth info_status.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum UserInfoStatusEnum {

    /**
     * 身份信息认证状态0认证中1认证成功2认证失败
     */
    WAITING(0, "认证中"),
    SUCCESS(1, "认证成功"),
    FAIL(2, "认证失败"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    UserInfoStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}