package com.tyt.receive.controller;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.tyt.infofee.bean.SingleGoodsDetailBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportBackend;
import com.tyt.plat.entity.base.OwnerCompanyRelation;
import com.tyt.plat.entity.base.TytExtendGoodsWhitelist;
import com.tyt.plat.mapper.base.TytExtendGoodsWhitelistMapper;
import com.tyt.receive.bean.HasNewOrderBean;
import com.tyt.receive.bean.StationReceiveInfoBean;
import com.tyt.receive.service.OwnerCompanyRelationService;
import com.tyt.receive.service.TransportBackendService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/12/23 09:49
 */
@Controller
@RequestMapping("/plat/station/receive")
public class StationReceiveController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String LOCK_KEY = "STATION_RECEIVE_LOCK_KEY_";

    public static final String REDIS_KEY = "STATION_RECEIVE_CACHE_KEY_";

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "transportBackendService")
    private TransportBackendService transportBackendService;
    @Autowired
    private OwnerCompanyRelationService ownerCompanyRelationService;

    @Autowired
    private TytExtendGoodsWhitelistMapper tytExtendGoodsWhitelistMapper;

    static Cache<String,String> cache = CacheBuilder.newBuilder().expireAfterWrite(180, TimeUnit.SECONDS).build();

    @RequestMapping("/hasNew")
    @ResponseBody
    public ResultMsgBean hasNewOrder(@RequestParam(name = "userId" ,required = false) Long userId,
                                     @RequestParam(name = "sign", required = false) String sign,
                                     @RequestParam(name = "clientSign", required = false) String clientSign) {
        if (userId == null) {
            return new ResultMsgBean(ResultMsgBean.ERROR, "参数有误");
        }
////        //单独处理安卓
//        if ("22".equals(clientSign)) {
//            String lockKey = LOCK_KEY + userId + sign;
//
//            String randomNumber = RandomStringUtils.randomNumeric(7);
//
//            try {
//                if (!Objects.equals(cache.get(lockKey, () -> randomNumber), randomNumber)) {
//                    logger.info("hasNew 重复请求：{}" , userId);
//                    return new ResultMsgBean(ResultMsgBean.ERROR, "重复请求");
//                }
//            } catch (ExecutionException e) {
//                e.printStackTrace();
//            }
//
//        }
        ResultMsgBean bean = new ResultMsgBean(ResultMsgBean.OK, "查询成功");
        HasNewOrderBean orderBean = new HasNewOrderBean();
        //List<OwnerCompanyRelation> ownerCompanyRelations = ownerCompanyRelationService.getOwnerCompanyRelationByUserId(userId);
        List<TytExtendGoodsWhitelist> tytExtendGoodsWhitelist = tytExtendGoodsWhitelistMapper.getWhitelist(userId);
        //不在白名单里面的用户不显示货源入口
        if (CollectionUtils.isEmpty(tytExtendGoodsWhitelist)) {
            orderBean.setIsShowLogo(false);
            orderBean.setCount(0);
            orderBean.setIsNewGoods(false);
            bean.setData(orderBean);
            return bean;
        }
        List<Integer> ids = transportBackendService.countMyOrders(userId);
        orderBean.setCount(ids.size());
        orderBean.setIsShowLogo(true);
        orderBean.setIsNewGoods(ids.size() > 0);
        bean.setData(orderBean);
        return bean;
    }

    /**
     * 获取是否显示首页企业货源入口标志
     * @param userId
     * @return
     */
    @RequestMapping(value = "isShowEnterpriseGoods")
    @ResponseBody
    public ResultMsgBean isShowEnterpriseGoods(Long userId) {
        if (Objects.isNull(userId)) {
            return new ResultMsgBean(ResultMsgBean.ERROR, "参数有误");
        }
        List<TytExtendGoodsWhitelist> tytExtendGoodsWhitelist = tytExtendGoodsWhitelistMapper.getWhitelist(userId);
        //List<OwnerCompanyRelation> ownerCompanyRelations = ownerCompanyRelationService.getOwnerCompanyRelationByUserId(userId);
        HasNewOrderBean hasNewOrderBean = new HasNewOrderBean();
        hasNewOrderBean.setIsShowLogo(tytExtendGoodsWhitelist.size() > 0);
        return ResultMsgBean.successResponse(hasNewOrderBean);
    }


    @RequestMapping(value = {"/myOrders", "/myOrders.action"})
    @ResponseBody
    public ResultMsgBean myOrders(@RequestParam Long userId,
                                  @RequestParam Long queryActionType,
                                  @RequestParam Long queryID){
        ResultMsgBean bean = new ResultMsgBean(ResultMsgBean.OK,"查询成功");
        List<StationReceiveInfoBean> orders = transportBackendService.getMyOrders(userId, queryActionType, queryID);
        Map<String, Object> result = new HashMap<>();
        result.put("receiveOrders", orders);
        result.put("time", new Date());
        bean.setData(result);
        return bean;
    }



    @RequestMapping(value = {"/info", "/info.action"})
    @ResponseBody
    public ResultMsgBean info(@RequestParam("backendId") Long backendId, @RequestParam("userId") Long userId){
        ResultMsgBean bean = new ResultMsgBean(ResultMsgBean.OK,"查询成功");
        StationReceiveInfoBean info = transportBackendService.getStationReceiveInfoById(backendId, userId);
        info.setTime(new Date());
        bean.setData(info);
        return bean;
    }


    @RequestMapping(value = {"/agree", "/agree.action"})
    @ResponseBody
    public ResultMsgBean agree(@RequestParam Long userId, @RequestParam Long backendId){
        return transportBackendService.updateAgree(userId, backendId);
    }


    @RequestMapping(value = {"/cancel", "/cancel.action"})
    @ResponseBody
    public ResultMsgBean cancel(@RequestParam Long userId, @RequestParam Long backendId, @RequestParam String cancelReason){
        return transportBackendService.updateCancel(userId, backendId, cancelReason);
    }


    @RequestMapping(value = {"/queryMyReceiveMsgIds", "/queryMyReceiveMsgIds.action"})
    @ResponseBody
    public ResultMsgBean queryMyReceiveMsgIds(@RequestParam Long userId){
        return transportBackendService.queryMyReceiveMsgIds(userId);
    }

    /**
     * 企业货源根据id获取货物详情
     * @param backendId
     * @param userId
     * @param operationType 操作类型 0：企业货源列表 1：企业货源列表重新找车
     *                      该接口主要用于企业货源编辑数据回显，如果 operationType = 0(企业货源列表) 则需要判断是否是货源已成交状态，如果为货源已成交则不允许编辑该货源
     *                      如果 operationType = 1(点击的是企业货源列表的重新找车按钮) 此时不需要判断该货源是否已成交
     * @return
     */
    @RequestMapping(value = "get/owner/goods/detail")
    @ResponseBody
    public ResultMsgBean getOwnerGoodsDetail(Long userId,Long backendId,Integer operationType) throws InvocationTargetException, IllegalAccessException {
        if (Objects.isNull(backendId) || Objects.isNull(userId) || Objects.isNull(operationType)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "缺少必填参数化");
        }
        return transportBackendService.getOwnerGoodsDetail(userId, backendId, operationType);
    }

}
