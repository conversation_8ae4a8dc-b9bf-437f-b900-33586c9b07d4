package com.tyt.receive.service;


import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportBackend;
import com.tyt.model.TytTransportBackendAxbBinder;

import java.util.List;
import java.util.Set;

public interface TransportBackendAxbBinderService extends BaseService<TytTransportBackendAxbBinder, Long> {

    //保存Axb模式绑定关系
    void saveAxbBinder(String phoneA,String phoneB);
    //Axb模式解绑
    void saveAxbUnbinder(String tel,String tel3,Long userId);

    TytTransportBackendAxbBinder getBinderByAxB(String phoneA,String phoneB);

    String getDialPhone(Long userId,String cellPhone);

    String getDialPhoneByUserId(Long userId);

    ResultMsgBean setDialPhone(Long userId, String cellPhone);

    List<TytTransportBackendAxbBinder> findByIdxSet(Set<String> idxSet);

}
