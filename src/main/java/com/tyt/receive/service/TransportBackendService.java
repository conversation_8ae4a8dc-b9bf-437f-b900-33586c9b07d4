package com.tyt.receive.service;


import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.SingleGoodsDetailBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TytTransportBackend;
import com.tyt.receive.bean.StationReceiveInfoBean;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

public interface TransportBackendService extends BaseService<TytTransportBackend, Long> {


    /**
     * 查询新货源订单列表
     * @param userId 用户id
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @param queryID 下拉是第一条信息的ID，下滑是最后一条信息的ID；（首次queryID=0）
     * <AUTHOR>
     * @date 2020/12/23
     * @return List
     */
    List<StationReceiveInfoBean> getMyOrders(Long userId, Long queryActionType, Long queryID);


    /**
     * 根据 backendId 查询 StationReceiveInfoBean
     * @param backendId 主键
     * @return StationReceiveInfoBean
     */
    StationReceiveInfoBean getStationReceiveInfoById(Long backendId,Long userId);

    /**
     * 查询新货源订单数量
     * @param userId 用户id
     * @return long
     */
    List<Integer> countMyOrders(Long userId);

    /**
     * 修改同意
     * @param userId
     * @param backendId
     * @return
     */
    ResultMsgBean updateAgree(Long userId, Long backendId);

    /**
     * 修改取消
     * @param userId
     * @param backendId
     * @param cancelReason
     * @return
     */
    ResultMsgBean updateCancel(Long userId, Long backendId, String cancelReason);

    ResultMsgBean queryMyReceiveMsgIds(Long userId);

    List<StationReceiveInfoBean> getMyBackend(Long userId, Long queryActionType, Long queryId);

    TytTransportBackend getByTsId(Long tsId);

    /**
     * 企业货源根据id获取货源详情，如果已关联srcMsgId说明是二次编辑（如撤销编辑再发布）返回 tyt_transport表货源信息
     * 如果srcMsgId为空说明未发布过该货源返回企业首次发货信息即tyt_transport_backend表货源信息
     * @param backendId
     * @return
     */
    ResultMsgBean getOwnerGoodsDetail(Long userId, Long backendId,Integer operationType) throws InvocationTargetException, IllegalAccessException;
}
