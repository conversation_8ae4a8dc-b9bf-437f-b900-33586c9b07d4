package com.tyt.receive.service.impl;

import com.tyt.plat.entity.base.OwnerCompanyLog;
import com.tyt.plat.mapper.base.OwnerCompanyLogMapper;
import com.tyt.receive.service.OwnerCompanyLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class OwnerCompanyLogServiceImpl implements OwnerCompanyLogService {
    @Autowired
    private OwnerCompanyLogMapper ownerCompanyLogMapper;

    @Override
    @Async(value="mqExecutor")
    public void addOwnerCompanyLog(OwnerCompanyLog ownerCompanyLog) {
        ownerCompanyLog.setCreateTime(new Date());
        ownerCompanyLogMapper.insertSelective(ownerCompanyLog);
    }
}
