package com.tyt.receive.service.impl;

import com.tyt.plat.entity.base.OwnerCompanyRelation;
import com.tyt.plat.mapper.base.OwnerCompanyRelationMapper;
import com.tyt.receive.service.OwnerCompanyRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OwnerCompanyRelationServiceImpl implements OwnerCompanyRelationService {
    @Autowired
    private OwnerCompanyRelationMapper ownerCompanyRelationMapper;
    @Override
    public List<OwnerCompanyRelation> getOwnerCompanyRelationByUserId(Long userId) {
        return ownerCompanyRelationMapper.selectByCompanyId(userId);
    }
}
