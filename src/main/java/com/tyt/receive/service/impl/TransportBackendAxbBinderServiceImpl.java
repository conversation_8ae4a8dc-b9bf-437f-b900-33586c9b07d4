package com.tyt.receive.service.impl;


import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportBackendAxbBinder;
import com.tyt.model.User;
import com.tyt.receive.service.TransportBackendAxbBinderService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.httputil.HttpUtil;
import com.winnerlook.model.PrivacyBindBodyAxb;
import com.winnerlook.model.PrivacyUnbindBody;
import com.winnerlook.util.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("transportBackendAxbBinderService")
public class TransportBackendAxbBinderServiceImpl extends BaseServiceImpl<TytTransportBackendAxbBinder, Long> implements TransportBackendAxbBinderService {
    public Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource(name = "transportBackendAxbBinderDao")
    public void setBaseDao(BaseDao<TytTransportBackendAxbBinder, Long> transportBackendAxbBinderDao) {
        super.setBaseDao(transportBackendAxbBinderDao);
    }
    private static final String appid = AppConfig.getProperty("winnerlook.appid");
    private static final String token = AppConfig.getProperty("winnerlook.token");
    private static final String url = AppConfig.getProperty("winnerlook.url");
    private static final String PHONE_BINDER_KEY = "phone:middleNumber:binder:";
    //用户配置的虚拟号拨号号码 redis key
    private static final String USER_MIDDLE_NUMBER_DIAL_CONFIG_KEY = "user:middleNumber:dial:user_phone";
    private static final String USER_MIDDLE_NUMBER_DIAL_PHONE_USER_CONFIG_KEY = "user:middleNumber:dial:phone_user";
    private static final String MIDDLE_NUMBER__BINDER_COUNT_KEY = "middleNumber:binderCount";

    private static final String middleNumbers = AppConfig.getProperty("winnerlook.middleNumbers");

    @Resource
    private UserService userService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Override
    public void saveAxbBinder(String phoneA, String phoneB) {
        String virtualNumberSwitch = tytConfigService.getStringValue(Constant.VIRTUAL_NUMBER_SWITCH, "0");
        if(Objects.equals(virtualNumberSwitch,"0")){
            logger.info("虚拟号功能关闭无需绑定,号码A:{},号码B:{}----------------------",phoneA,phoneB);
            return;
        }
        logger.info("号码A:{},号码B:{},绑定开始----------------------",phoneA,phoneB);
        //判断是否已有绑定关系
        TytTransportBackendAxbBinder binderByAxB = getBinderByAxB(phoneA, phoneB);
        if(binderByAxB != null){
            logger.info("号码A:{},号码B:{},绑定,已有绑定关系:{}",phoneA,phoneB,JSON.toJSONString(binderByAxB));
            binderByAxB.setBindCount(binderByAxB.getBindCount() + 1);
            binderByAxB.setUpdateTime(new Date());
            this.getBaseDao().update(binderByAxB);
            logger.info("号码A:{},号码B:{},绑定完成 增加绑定数量 绑定后:{}----------------------",phoneA,phoneB,JSON.toJSONString(binderByAxB));
            return;
        }
        //获取可绑定的虚拟小号
        String middleNumber = getMiddleNumber(phoneA,phoneB);
        if(null != middleNumber){
            logger.info("号码A:{},号码B:{},绑定,没有有绑定关系，获取到可绑定小号:{}",phoneA,phoneB,middleNumber);
            boolean isSuccess = axbBinder(phoneA, phoneB, middleNumber);
            if(isSuccess){
                TytTransportBackendAxbBinder binderInfo = new TytTransportBackendAxbBinder();
                Set<String> set=new TreeSet<String>();
                set.add(phoneA);
                set.add(phoneB);
                binderInfo.setBindIndex(StringUtils.join(set,"_"));
                binderInfo.setMiddleNumber(middleNumber);
                binderInfo.setBindNumberA(phoneA);
                binderInfo.setBindNumberB(phoneB);
                binderInfo.setBindCount(1);
                binderInfo.setBindStatus(1);
                binderInfo.setCreateTime(new Date());
                this.getBaseDao().insert(binderInfo);
            }else {
                logger.info("号码A:{},号码B:{},绑定,没有有绑定关系，没有获取到可绑定小号，绑定失败");
            }
        }else {
            logger.info("号码A:{},号码B:{},绑定,没有有绑定关系，没有获取到可绑定小号，未绑",phoneA,phoneB);
            //虚拟小号资源不足 绑定失败处理
        }
        logger.info("号码A:{},号码B:{},绑定结束----------------------",phoneA,phoneB);
    }

    private boolean axbBinder(String phoneA, String phoneB, String middleNumber) {
        String keyA = PHONE_BINDER_KEY.concat(phoneA);
        RedisUtil.mapPut(keyA, middleNumber, middleNumber);
        String keyB = PHONE_BINDER_KEY.concat(phoneB);
        RedisUtil.mapPut(keyB, middleNumber, middleNumber);
        return axbBindPhone(phoneA,phoneB,middleNumber);
    }

    private String getMiddleNumber(String phoneA, String phoneB){
        String keyA = PHONE_BINDER_KEY.concat(phoneA);
        Map<String, String> mapA = RedisUtil.getMap(keyA);
        String keyB = PHONE_BINDER_KEY.concat(phoneB);
        Map<String, String> mapB = RedisUtil.getMap(keyB);
        //phoneA 绑定的所有小号
        List<String> aBinders = Optional.ofNullable(mapA).orElse(new HashMap<>()).values().stream().collect(Collectors.toList());
        //phoneB 绑定的所有小号
        List<String> bBinders = Optional.ofNullable(mapB).orElse(new HashMap<>()).values().stream().collect(Collectors.toList());
        //汇总为一个集合
        aBinders.addAll(bBinders);
        Set<String> binderSet = aBinders.stream().collect(Collectors.toSet());
        List<String> list = new ArrayList<String>(Arrays.asList(middleNumbers.split(",")));
        if(list.size() == binderSet.size()){
            return null;
        }else {
            Set<String> middleNumberSet = list.stream().collect(Collectors.toSet());
            middleNumberSet.removeAll(binderSet);
            Iterator<String> iterator = middleNumberSet.iterator();
            return checkAndReturnMiddleNumber(iterator);
        }
    }

    private String checkAndReturnMiddleNumber(Iterator<String> iterator){
        if(iterator.hasNext()){
            String next = iterator.next();
            //校验 中间号绑定上限 上限200
            Long bindCount = RedisUtil.incr(MIDDLE_NUMBER__BINDER_COUNT_KEY.concat(next));
            if(bindCount > 200){
                RedisUtil.decr(MIDDLE_NUMBER__BINDER_COUNT_KEY.concat(next));
                if(iterator.hasNext()){
                    return checkAndReturnMiddleNumber(iterator);
                }else {
                    return null;
                }
            }
            return next;
        }else {
            return null;
        }
    }

    @Override
    public void saveAxbUnbinder(String tel,String tel3,Long userId) {
        String dialPhone = getDialPhoneByUserId(userId);
        if(StringUtils.isNotBlank(dialPhone)){
            //解绑
            unBinderByAxB(tel, dialPhone);
            if(StringUtils.isNotBlank(tel3)){
                unBinderByAxB(tel3,dialPhone);
            }
        }
    }

    private void unBinderByAxB(String phoneA, String phoneB) {
        logger.info("号码A:{},号码B:{},解除绑定开始----------------------",phoneA,phoneB);
        TytTransportBackendAxbBinder bindInfo = getBinderByAxB(phoneA,phoneB);
        if(null != bindInfo){
            if(bindInfo.getBindCount() != null && bindInfo.getBindStatus() == 1 && bindInfo.getBindCount()<=1){
                logger.info("号码A:{},号码B:{},解除绑定,只有1个绑定关系:{}----------------------",phoneA,phoneB,JSON.toJSONString(bindInfo));
                bindInfo.setBindStatus(0);
                bindInfo.setBindCount(0);
                bindInfo.setUpdateTime(new Date());
                this.getBaseDao().update(bindInfo);
                String middleNumber = bindInfo.getMiddleNumber();
                axbUnBind(bindInfo.getBindNumberA(),bindInfo.getBindNumberB(), middleNumber);
                String keyA = PHONE_BINDER_KEY.concat(phoneA);
                RedisUtil.mapRemove(keyA, middleNumber);
                String keyB = PHONE_BINDER_KEY.concat(phoneB);
                RedisUtil.mapRemove(keyB,middleNumber);
                logger.info("号码A:{},号码B:{},解除绑定后,绑定关系对象:{}----------------------",phoneA,phoneB,JSON.toJSONString(bindInfo));
            }else if(bindInfo.getBindCount() != null && bindInfo.getBindStatus() == 1 && bindInfo.getBindCount() > 1){
                logger.info("号码A:{},号码B:{},解除绑定,有多个绑定关系:{}----------------------",phoneA,phoneB,JSON.toJSONString(bindInfo));
                bindInfo.setBindCount(bindInfo.getBindCount() - 1);
                bindInfo.setUpdateTime(new Date());
                this.getBaseDao().update(bindInfo);
                logger.info("号码A:{},号码B:{},解除绑定后 绑定关系:{}----------------------",phoneA,phoneB,JSON.toJSONString(bindInfo));
            }
        }else {
            logger.info("号码A:{},号码B:{},未查询到绑定关系----------------------",phoneA,phoneB);
        }
        logger.info("号码A:{},号码B:{},解除绑定结束----------------------",phoneA,phoneB);
    }


    @Override
    public TytTransportBackendAxbBinder getBinderByAxB(String phoneA, String phoneB) {
        String virtualNumberSwitch = tytConfigService.getStringValue(Constant.VIRTUAL_NUMBER_SWITCH, "0");
        if(Objects.equals(virtualNumberSwitch,"0")){
            logger.info("虚拟号绑定关系查询----------------------");
            logger.info("虚拟号功能关闭,号码A:{},号码B:{}----------------------",phoneA,phoneB);
            return null;
        }
        TytTransportBackendAxbBinder query = new TytTransportBackendAxbBinder();
        Set<String> set=new TreeSet<String>();
        set.add(phoneA);
        set.add(phoneB);
        query.setBindIndex(StringUtils.join(set,"_"));
        query.setBindStatus(1);
        TytTransportBackendAxbBinder bindInfo = this.find(query);
        return bindInfo;
    }

    @Override
    public String getDialPhone(Long userId, String cellPhone) {
        String dialPhone = RedisUtil.getMapValue(USER_MIDDLE_NUMBER_DIAL_CONFIG_KEY,userId.toString());
        if(!org.springframework.util.StringUtils.hasLength(dialPhone)){
            dialPhone = cellPhone;
        }
        return dialPhone;
    }

    @Override
    public String getDialPhoneByUserId(Long userId) {
        String dialPhone = RedisUtil.getMapValue(USER_MIDDLE_NUMBER_DIAL_CONFIG_KEY,userId.toString());
        if(StringUtils.isBlank(dialPhone)){
            try {
                User user = userService.getByUserId(userId);
                dialPhone = user.getCellPhone();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return dialPhone;
    }

    @Override
    public ResultMsgBean setDialPhone(Long userId, String dialPhone) {
        ResultMsgBean result = new ResultMsgBean();
        String oldDialPhone = getDialPhoneByUserId(userId);
        if(Objects.equals(dialPhone,oldDialPhone)){
            result.setData(dialPhone);
            result.setMsg("设置成功");
            return result;
        }
        //check 是否被别人设置为拨打号
        String dialUserId = RedisUtil.getMapValue(USER_MIDDLE_NUMBER_DIAL_PHONE_USER_CONFIG_KEY, dialPhone);
        if(StringUtils.isNotBlank(dialUserId) && !Objects.equals(dialUserId,userId.toString())){
            result.setCode(ReturnCodeConstant.DIAL_PHONE_SET_ERROR_CODE);
            result.setMsg("该拨打号码已被其他用户占用");
            return result;
        }
        //设置拨出号码
        RedisUtil.mapPut(USER_MIDDLE_NUMBER_DIAL_PHONE_USER_CONFIG_KEY,dialPhone,userId.toString());
        RedisUtil.mapRemove(USER_MIDDLE_NUMBER_DIAL_PHONE_USER_CONFIG_KEY,oldDialPhone);
        RedisUtil.mapPut(USER_MIDDLE_NUMBER_DIAL_CONFIG_KEY,userId.toString(),dialPhone);
        //修改已有绑定 DB
        String hql="from TytTransportBackendAxbBinder where (bindNumberA = ? or bindNumberB =?) and bindStatus=1";
        List<TytTransportBackendAxbBinder> list = this.getBaseDao().find(hql, oldDialPhone,oldDialPhone);
        //修改虚拟号商绑定关系 先解绑 再绑定
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(a -> {
                axbUnBind(a.getBindNumberA(),a.getBindNumberB(),a.getMiddleNumber());
                if(Objects.equals(a.getBindNumberA(),oldDialPhone)){
                    //拨打号为 a
                    axbBinder(dialPhone,a.getBindNumberB(),a.getMiddleNumber());
                    a.setBindNumberA(dialPhone);
                }else {
                    //拨打号为b
                    axbBinder(a.getBindNumberA(),dialPhone,a.getMiddleNumber());
                    a.setBindNumberB(dialPhone);
                }
                Set<String> set=new TreeSet<String>();
                set.add(a.getBindNumberA());
                set.add(a.getBindNumberB());
                a.setBindIndex(StringUtils.join(set,"_"));
                String sql = "update tyt_transport_backend_axb_binder set bind_index = ?,bind_number_a = ?,bind_number_b = ? where id = ?;";
                this.getBaseDao().executeUpdateSql(sql,new Object[]{a.getBindIndex(),a.getBindNumberA(),a.getBindNumberB(),a.getId()});
            });
        }
        String key = PHONE_BINDER_KEY.concat(oldDialPhone);
        RedisUtil.del(key);
        result.setData(dialPhone);
        result.setMsg("设置成功");
        return result;
    }

    @Override
    public List<TytTransportBackendAxbBinder> findByIdxSet(Set<String> idxSet) {
        if(CollectionUtils.isEmpty(idxSet)){
            return new ArrayList<>();
        }
        StringBuffer idxStr = new StringBuffer();
        Iterator<String> iterator = idxSet.iterator();
        idxStr.append("'");
        while (iterator.hasNext()){
            String item = iterator.next();
            if(iterator.hasNext()){
                idxStr.append(item).append("','");
            }else {
                idxStr.append(item).append("'");
            }
        }

        String sql = "select * from tyt_transport_backend_axb_binder where bind_index in (" + idxStr.toString() + ") and bind_status = 1";
        List<TytTransportBackendAxbBinder> list = this.getBaseDao().queryForList(sql, new Object[]{});
        return list;
    }

    public boolean axbBindPhone(String phoneA , String phoneB, String middleNumber){
        /** AXB模式小号绑定接口地址*/
        String reqUrl = url +"/middleNumberAXB";

        PrivacyBindBodyAxb bindBodyAxb = new PrivacyBindBodyAxb();

        /** 设定绑定的隐私小号*/
        bindBodyAxb.setMiddleNumber(middleNumber);
        /** 设定与该隐私小号绑定的号码A*/
        bindBodyAxb.setBindNumberA(phoneA);
        /** 设定与该隐私小号绑定的号码B*/
        bindBodyAxb.setBindNumberB(phoneB);
        /** 设置是否开启通话录音  1:开启，0:关闭*/
        bindBodyAxb.setCallRec(0);
        /** 设置绑定关系有效时长 ,为空表示绑定关系永久，单位:秒*/
//        bindBodyAxb.setMaxBindingTime(3600);
        /** 设置是否透传主叫的号码到A  0:不透传; 1: 透传，不填默认不透传*/
        bindBodyAxb.setPassthroughCallerToA(0);
        /** 设置是否透传主叫的号码到B  0:不透传; 1: 透传，不填默认不透传*/
        bindBodyAxb.setPassthroughCallerToB(0);
        /** 设置用于接收呼叫结果的服务地址*/
//        bindBodyAxb.setCallbackUrl("http://myip../...");

        /** 获取当前系统时间戳*/
        long timestamp = System.currentTimeMillis();
        /** 生成Base64转码后的参数authorization*/
        String auth = appid + ":" + String.valueOf(timestamp);
        String authorization = Base64Utils.encodeToString(auth.getBytes());
        /** 生成加密参数sig*/
        String sig = MD5Util.getMD5(appid + token + timestamp);

        /** 生成最终接口访问地址*/
        reqUrl = reqUrl + "/" + appid + "/" + sig;
        String body = JSON.toJSONString(bindBodyAxb);
        String result = HttpUtil.doPost(reqUrl, authorization, body);
        logger.info("号码A:{},号码B:{},虚拟号:{},绑定结果:{}",phoneA,phoneB,middleNumber,result);
        HashMap<String,Object> resultMap = JSON.parseObject(result, HashMap.class);
        if(resultMap != null){
            Object resultCode = resultMap.get("result");
//            {"message":"SUCCESS","bindId":"211022f7d849468daee47ffdf54695e0","result":"000000","middleNumber":"15690234704"}
            if(resultCode != null && resultCode.toString().equals("000000")){
                return true;
            }else {
                return false;
            }
        }else {
            return false;
        }
    }

    public void axbUnBind(String phoneA ,String phoneB,String middleNumber){
        String reqUrl = url + "/middleNumberUnbind";
        PrivacyUnbindBody unbindBody = new PrivacyUnbindBody();
        /** 获取当前系统时间戳*/
        long timestamp = System.currentTimeMillis();

        /** 设置需要解绑的小号*/
        unbindBody.setMiddleNumber(middleNumber);
        /** 设置与该小号绑定的号码A*/
        unbindBody.setBindNumberA(phoneA);
        unbindBody.setBindNumberB(phoneB);

        /** 生成Base64转码后的参数authorization*/
        String auth = appid + ":" + String.valueOf(timestamp);
        String authorization = Base64Utils.encodeToString(auth.getBytes());
        /** 生成加密参数sig*/
        String sig = MD5Util.getMD5(appid + token + timestamp);

        /** 生成最终接口访问地址*/
        reqUrl = reqUrl + "/" + appid + "/" + sig;
        String body = JSON.toJSONString(unbindBody);
        String result = HttpUtil.doPost(reqUrl, authorization, body);
        logger.info("号码A:{},号码B:{},虚拟号:{},解除绑定结果:{}",phoneA,phoneB,middleNumber,result);
    }

}
