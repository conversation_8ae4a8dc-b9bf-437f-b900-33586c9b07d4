package com.tyt.receive.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.SingleGoodsDetailBean;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqUserMsg;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.plat.entity.base.OwnerCompanyLog;
import com.tyt.plat.entity.base.OwnerCompanyRelation;
import com.tyt.plat.entity.base.TytCity;
import com.tyt.plat.mapper.base.TytCityMapper;
import com.tyt.receive.bean.StationReceiveInfoBean;
import com.tyt.receive.constant.BackendOrderStatusEnum;
import com.tyt.receive.constant.ConfigConstant;
import com.tyt.receive.service.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportService;
import com.tyt.transport.querybean.BackendTransportBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service("transportBackendService")
public class TransportBackendServiceImpl extends BaseServiceImpl<TytTransportBackend, Long> implements TransportBackendService {


    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String LOCK_KEY = "station:receive:";

    @Resource(name = "transportBackendUserService")
    private TransportBackendUserService transportBackendUserService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "transportBackendAxbBinderService")
    private TransportBackendAxbBinderService transportBackendAxbBinderService;

    @Autowired
    private BackendTransportMapper backendTransportMapper;
    @Resource
    private TransportMainService transportMainService;
    @Autowired
    private InfoFeeBusinessService infoFeeBusinessService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Autowired
    private OwnerCompanyRelationService ownerCompanyRelationService;
    @Autowired
    private TytCityMapper tytCityMapper;

    @Autowired
    private OwnerCompanyLogService ownerCompanyLogService;


    @Resource(name = "transportBackendDao")
    public void setBaseDao(BaseDao<TytTransportBackend, Long> transportBackendLongBaseDao) {
        super.setBaseDao(transportBackendLongBaseDao);
    }

    @Override
    public StationReceiveInfoBean getStationReceiveInfoById(Long backendId, Long userId) {
        TytTransportBackend transportBackend = this.getById(backendId);
        if(Objects.nonNull(transportBackend)){
            StationReceiveInfoBean infoBean = new StationReceiveInfoBean();
            BeanUtils.copyProperties(transportBackend, infoBean);
            if(Objects.nonNull(userId)){
                TytTransportBackendUser userBackend = transportBackendUserService.getByUserIdAndBackendId(userId, backendId);
                if(Objects.nonNull(userBackend)){
                    infoBean.setReceiveOrderTime(userBackend.getReceiveOrderTime());
                    infoBean.setPublishTime(userBackend.getPublishTime());
                }
            }
            String dialPhone = transportBackendAxbBinderService.getDialPhoneByUserId(userId);
            TytTransportBackendAxbBinder binder = transportBackendAxbBinderService.getBinderByAxB(transportBackend.getTel(),dialPhone);
            if(null != binder){
                infoBean.setTel(binder.getMiddleNumber());
            }
            if(StringUtils.isNotBlank(transportBackend.getTel3())){
                TytTransportBackendAxbBinder binder2 = transportBackendAxbBinderService.getBinderByAxB(transportBackend.getTel3(),dialPhone);
                if(null != binder2){
                    infoBean.setTel3(binder2.getMiddleNumber());
                }
            }
            infoBean.setTel4(null);
            initInfo(infoBean);
            return infoBean;
        }
        return null;
    }

    @Override
    public List<StationReceiveInfoBean> getMyOrders(Long userId, Long queryActionType, Long queryID) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT tb.id as backendId, " +
                "tb.start_point as startPoint, " +
                "tb.dest_point as destPoint, " +
                "tb.task_content as taskContent, " +
                "tb.tel as tel, " +
                "tb.status as status, " +
                "tb.order_status as orderStatus, " +
                "tb.is_precise_push as isPrecisePush, " +
                "tb.source as source, " +
                "tb.ctime as ctime, " +
                "tb.price as price, " +
                "tb.user_id as userId, " +
                "tb.start_coord_x as startCoordX, " +
                "tb.start_coord_y as startCoordY, " +
                "tb.dest_coord_x as destCoordX, " +
                "tb.dest_coord_y as destCoordY, " +
                "tb.start_longitude as startLongitude, " +
                "tb.start_latitude as startLatitude, " +
                "tb.dest_longitude as destLongitude, " +
                "tb.dest_latitude as destLatitude, " +
                "tb.start_detail_add as startDetailAdd, " +
                "tb.dest_detail_add as destDetailAdd, " +
                "tb.weight as weight, " +
                "tb.length as length, " +
                "tb.wide as wide, " +
                "tb.high as high, " +
                "tb.remark as remark, " +
                "tb.tel3 as tel3, " +
                "tb.tel4 as tel4, " +
                "tb.start_city as startCity, " +
                "tb.start_provinc as startProvinc, " +
                "tb.start_area as startArea, " +
                "tb.dest_provinc as destProvinc, " +
                "tb.dest_city as destCity, " +
                "tb.dest_area as destArea, " +
                "tb.type as type, " +
                "tb.brand as brand, " +
                "tb.good_type_name as goodTypeName, " +
                "tb.loading_time as loadingTime, " +
                "tb.unload_time as unloadTime, " +
                "tb.car_min_length as carMinLength, " +
                "tb.car_max_length as carMaxLength, " +
                "tb.car_type as carType, " +
                "tb.car_style as carStyle, " +
                "tb.work_plane_min_high as workPlaneMinHigh, " +
                "tb.work_plane_max_high as workPlaneMaxHigh, " +
                "tb.work_plane_min_length as workPlaneMinLength, " +
                "tb.work_plane_max_length as workPlaneMaxLength, " +
                "tb.climb as climb, " +
                "tbu.publish_time as publishTime, " +
                "tbu.receive_order_time as receiveOrderTime FROM " +
                "tyt_transport_backend_user tbu " +
                "LEFT JOIN tyt_transport_backend tb on tbu.backend_id = tb.id and tb.batch = tbu.batch " +
                "WHERE tbu.user_id = ? and ( tbu.receive_order_time >= ? or tbu.publish_time >= ? ) and tb.status = 1 " +
                "and tbu.status != 3 AND ((tb.order_status = 11 and tbu.status = 2) OR tb.order_status = 10)";
        params.add(userId);
        params.add(new Date());
        params.add(new Date());
        if(queryActionType == 2 && queryID > 0){
            sql += " and tbu.backend_id < ? ";
            params.add(queryID);
        }
        sql +=" ORDER BY tbu.backend_id desc limit ?";
        params.add(AppConfig.getIntProperty("tyt.tyt_transport.station.receive.query.page.size"));

        Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("backendId", Hibernate.LONG);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("tel", Hibernate.STRING);
        scalarMap.put("tel3", Hibernate.STRING);
        scalarMap.put("tel4", Hibernate.STRING);
        scalarMap.put("status", Hibernate.INTEGER);
        scalarMap.put("orderStatus", Hibernate.INTEGER);
        scalarMap.put("isPrecisePush", Hibernate.INTEGER);
        scalarMap.put("source", Hibernate.INTEGER);
        scalarMap.put("ctime", Hibernate.TIMESTAMP);
        scalarMap.put("price", Hibernate.STRING);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("startDetailAdd", Hibernate.STRING);
        scalarMap.put("destDetailAdd", Hibernate.STRING);
        scalarMap.put("weight", Hibernate.STRING);
        scalarMap.put("length", Hibernate.STRING);
        scalarMap.put("wide", Hibernate.STRING);
        scalarMap.put("high", Hibernate.STRING);
        scalarMap.put("remark", Hibernate.STRING);
        scalarMap.put("startCity", Hibernate.STRING);
        scalarMap.put("startProvinc", Hibernate.STRING);
        scalarMap.put("startArea", Hibernate.STRING);
        scalarMap.put("destProvinc", Hibernate.STRING);
        scalarMap.put("destCity", Hibernate.STRING);
        scalarMap.put("destArea", Hibernate.STRING);
        scalarMap.put("type", Hibernate.STRING);
        scalarMap.put("brand", Hibernate.STRING);
        scalarMap.put("goodTypeName", Hibernate.STRING);
        scalarMap.put("loadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("unloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("carMinLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("carMaxLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("carType", Hibernate.STRING);
        scalarMap.put("carStyle", Hibernate.STRING);
        scalarMap.put("workPlaneMinHigh", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMaxHigh", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMinLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMaxLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("climb", Hibernate.STRING);
        scalarMap.put("receiveOrderTime", Hibernate.TIMESTAMP);
        scalarMap.put("publishTime", Hibernate.TIMESTAMP);
        scalarMap.put("startCoordX", Hibernate.STRING);
        scalarMap.put("startCoordY", Hibernate.STRING);
        scalarMap.put("destCoordX", Hibernate.STRING);
        scalarMap.put("destCoordY", Hibernate.STRING);
        scalarMap.put("startLongitude", Hibernate.STRING);
        scalarMap.put("startLatitude", Hibernate.STRING);
        scalarMap.put("destLongitude", Hibernate.STRING);
        scalarMap.put("destLatitude", Hibernate.STRING);

        List<StationReceiveInfoBean> list = this.getBaseDao().search(sql, scalarMap, StationReceiveInfoBean.class, params.toArray());
        String virtualNumberSwitch = tytConfigService.getStringValue(Constant.VIRTUAL_NUMBER_SWITCH, "0");
        if(CollectionUtils.isNotEmpty(list) && Objects.equals(virtualNumberSwitch,"1")){
            String dialPhone = transportBackendAxbBinderService.getDialPhoneByUserId(userId);
            Set<String> idxSet = new HashSet<>();
            list.stream().forEach(a -> {
                Set<String> set=new TreeSet<String>();
                set.add(dialPhone);
                set.add(a.getTel());
                String idx = StringUtils.join(set, "_");
                idxSet.add(idx);
                if(StringUtils.isNotBlank(a.getTel3())){
                    Set<String> set2=new TreeSet<String>();
                    set2.add(dialPhone);
                    set2.add(a.getTel3());
                    String idx2 = StringUtils.join(set2, "_");
                    idxSet.add(idx2);
                }
            });
            if(idxSet.size() > 0){
                List<TytTransportBackendAxbBinder> binders = transportBackendAxbBinderService.findByIdxSet(idxSet);
                Map<String, TytTransportBackendAxbBinder> map = binders.stream().collect(Collectors.toMap(a -> a.getBindIndex(), a -> a, (b, c) -> b));
                list.stream().forEach(a -> {
                    Set<String> set=new TreeSet<String>();
                    set.add(dialPhone);
                    set.add(a.getTel());
                    String idx = StringUtils.join(set, "_");
                    TytTransportBackendAxbBinder telBinder = Optional.ofNullable(map.get(idx)).orElse(new TytTransportBackendAxbBinder());
                    a.setTel(telBinder.getMiddleNumber());
                    if(StringUtils.isNotBlank(a.getTel3())){
                        Set<String> set2=new TreeSet<String>();
                        set2.add(dialPhone);
                        set2.add(a.getTel3());
                        String idx2 = StringUtils.join(set2, "_");
                        TytTransportBackendAxbBinder tel3Binder = Optional.ofNullable(map.get(idx2)).orElse(new TytTransportBackendAxbBinder());
                        a.setTel3(tel3Binder.getMiddleNumber());
                    }
                    a.setTel4(null);
                });
            }

        }
        list.stream().peek(TransportBackendServiceImpl::initInfo).collect(Collectors.toList());
        return list;
    }

    @Override
    public List<Integer> countMyOrders(Long userId) {
//        String sql = "select count(*) from tyt_transport_backend_user tbu LEFT JOIN tyt_transport_backend tb on tbu.backend_id = tb.id and tb.batch = tbu.batch " +
//                "WHERE tbu.user_id = ? and ( tbu.receive_order_time >= ? or tbu.publish_time >= ? ) and tb.status = 1 and tbu.status != 3 AND ((tb.order_status = 11 and tbu.status = 2) OR tb.order_status = 10)";
        String sql = "SELECT tb.id FROM tyt_transport_backend_user tbu LEFT JOIN tyt_transport_backend tb ON tbu.backend_id = tb.id AND tb.batch = tbu.batch " +
                "WHERE tbu.user_id = {0} AND tb.STATUS = 1 AND tb.order_status = 10";
        String formatSql = MessageFormat.format(sql, String.valueOf(userId));
//        BigInteger integer = this.getBaseDao().query(sql, new Object[]{userId});
        Map<String, Type> map = new HashMap<String, Type>();
        map.put("id", Hibernate.STRING);
        List<Integer> all = this.getBaseDao().findAll(formatSql, map, null);
        return all;
    }


    @Override
    public ResultMsgBean updateAgree(Long userId, Long backendId) {
        int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
        ResultMsgBean bean = new ResultMsgBean();
        String redisKey = LOCK_KEY + backendId;
        List<OwnerCompanyRelation> ownerCompanyRelations = ownerCompanyRelationService.getOwnerCompanyRelationByUserId(userId);
        try {
            if (LockUtil.lockObject("1", redisKey, redisLockTimeout)) {
                TytTransportBackend backend = super.getById(backendId);
                User user = userService.getByUserId(userId);
                Map<String, Object> result = new HashMap<>();
                if(Objects.nonNull(backend)){
                    //判断是否为企业货源，不是的话走限时接单
                    if(CollectionUtils.isEmpty(ownerCompanyRelations)){
                        //已被接单
                        if(!Objects.equals(backend.getOrderStatus(), BackendOrderStatusEnum.PENDING.getStatus())){
                            return new ResultMsgBean(ReturnCodeConstant.OTHER_RECEIVE,"货源已被他人抢先接单");
                        }
                        //计算发布过期时间
                        int publishTimeMin = tytConfigService.getIntValue(ConfigConstant.RECEIVE_ORDER_TIME, 15);
                        Date publishTime = Date.from(LocalDateTime.now().plusMinutes(publishTimeMin).atZone(ZoneId.systemDefault()).toInstant());
                        //先修改用户推送记录表,修改为已接单
                        int count = transportBackendUserService.executeUpdateSql("update tyt_transport_backend_user set status = 2, publish_time = ? where " +
                                        "backend_id = ? and user_id = ? and batch = ? and status = 1 and receive_order_time >= ?",
                                new Object[]{publishTime, backendId, userId, backend.getBatch(), new Date()} );
                        logger.info("用户:{} 接货:{} 结果:{}", userId, backend, count);
                        if(count < 1){
                            return new ResultMsgBean(ReturnCodeConstant.RECEIVE_TIMEOUT,"接单已超时");
                        }
                        count = super.executeUpdateSql("update tyt_transport_backend set order_status = 11," +
                                " mtime = ? where id = ? and order_status = 10", new Object[]{new Date(), backendId});
                        logger.info("用户:{} 接货:{} 结果:{}", userId, backend, count);
                        if(count < 1){
                            return new ResultMsgBean(ReturnCodeConstant.OTHER_RECEIVE,"货源已被他人抢先接单");
                        }
                        //将待编辑时间返回
                        result.put("publishTime", publishTime);
                        bean.setCode(ResultMsgBean.OK);
                        bean.setMsg("您已抢单成功！请尽快与货主联系编辑订单找车哦~");
                    }else{
                        //企业货源-运输公司未接单之前上游取消订单，页面未刷新状态
                        if(backend.getStatus() == 2){
                            return new ResultMsgBean(ReturnCodeConstant.COMPANY_BAN_EDIT,"货源已取消,接单失败");
                        }
                        //后期如出现可以同时推送多家运输公司，出现抢单操作或者已经接单了，再一次请求接单
                        if(!Objects.equals(backend.getOrderStatus(), BackendOrderStatusEnum.PENDING.getStatus())){
                            return new ResultMsgBean(ReturnCodeConstant.OTHER_RECEIVE,"货源已被他人接单");
                        }
                        //是企业货源-
                        transportBackendUserService.executeUpdateSql("update tyt_transport_backend_user set status = 2 where " +
                                        "backend_id = ? and user_id = ? and batch = ? and status = 1 ",
                                new Object[]{ backendId, userId, backend.getBatch()} );
                        logger.info("用户:{} 接货:{} 结果:{}", userId, backend);
                        super.executeUpdateSql("update tyt_transport_backend set status = 3,order_status = 30 ," +
                                " mtime = ?,receiving_time=?,receiver_user_id=?,receiver_phone=? where id = ? and status = 1", new Object[]{new Date(), new Date(),userId,user.getCellPhone(),backendId});
                        logger.info("用户:{} 接货:{} 结果:{}", userId, backend);
                        bean.setCode(ResultMsgBean.OK);
                        bean.setMsg("您已接单成功~");

                        //记录企业货源状态流转日志
                        OwnerCompanyLog ownerCompanyLog = new OwnerCompanyLog();
                        ownerCompanyLog.setOrderNo(backend.getOrderNo());
                        ownerCompanyLog.setBackendId(backend.getId());
                        ownerCompanyLog.setCompanyId(backend.getReceiverUserId());
                        ownerCompanyLog.setEnterpriseId(backend.getAppletsUserId());
                        ownerCompanyLog.setStatus(3);
                        ownerCompanyLog.setOrderStatus(30);
                        ownerCompanyLog.setSrcMsgId(backend.getSrcMsgId());
                        ownerCompanyLogService.addOwnerCompanyLog(ownerCompanyLog);
                    }
                    result.put("backendId", backendId);
                }

                String dialPhone = transportBackendAxbBinderService.getDialPhone(user.getId(),user.getCellPhone());
                transportBackendAxbBinderService.saveAxbBinder(backend.getTel(),dialPhone);
                if(StringUtils.isNotBlank(backend.getTel3())){
                    transportBackendAxbBinderService.saveAxbBinder(backend.getTel3(),dialPhone);
                }
                 //如果是企业货源，未被第三方取消，发送mq消息
                if(backend.getStatus() != 2){
                    if(!CollectionUtils.isEmpty(ownerCompanyRelations)){
                        //接单成功--发送mq同步山推状态
                        if (backend != null && backend.getId() != null) {
                            sendMessage2MQ(backend,userId);
                        }
                    }
                }
                bean.setData(result);
                return bean;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            LockUtil.unLockObject("1", redisKey);
        }
        return new ResultMsgBean(ResultMsgBean.ERROR,"超时");
    }

    private void sendMessage2MQ(TytTransportBackend backend,Long userId) {

        MqUserMsg mqUserMsg = new MqUserMsg();

        mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqUserMsg.setMessageType(MqBaseMessageBean.TRANSPORT_BACKEND_STATUS);
        mqUserMsg.setUserId(backend.getId());
        if (backend != null ){
            mqUserMsg.setSrcMsgId(userId);
            String content="山推订单号为【" + backend.getId() + "】的订单已接单";
            mqUserMsg.setContent(content);
        }
        // 保存mq信息到数据库
        tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

        long mqDelayTime = tytConfigService.getIntValue(Constant.publish_mq_delay_time, 1500).longValue();

        logger.info("publish_mq_delay_time : " + mqDelayTime);

        // 发送接单成功mq，通知山推
        tytMqMessageService.sendMqMessageTransportStatus(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqDelayTime);
    }

    @Override
    public ResultMsgBean updateCancel(Long userId, Long backendId, String cancelReason) {
        TytTransportBackend backend = super.getById(backendId);
        if(Objects.nonNull(backend) && Objects.equals(backend.getOrderStatus(), BackendOrderStatusEnum.NO_PUBLISH.getStatus())){

            transportBackendUserService.executeUpdateSql("update tyt_transport_backend_user set status = 3,cancel_reason =? where" +
                    " backend_id = ? and user_id = ? and batch = ? ", new Object[]{cancelReason, backendId, userId, backend.getBatch()});

            super.executeUpdateSql("update tyt_transport_backend set order_status = 10, mtime=? where id =? ",
                    new Object[]{new Date(), backendId});

        }
        transportBackendAxbBinderService.saveAxbUnbinder(backend.getTel(),backend.getTel3(),userId);
        return new ResultMsgBean(ResultMsgBean.OK,"取消成功");
    }

    @Override
    public ResultMsgBean queryMyReceiveMsgIds(Long userId) {
        List<HashMap<String, Long>> result = backendTransportMapper.selectMsgIdsByUserId(userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        resultMsgBean.setData(result);
        return resultMsgBean;
    }

    private static void initInfo(StationReceiveInfoBean bean){
        bean.setPhoneList(new ArrayList<>());
        addPhone(bean.getPhoneList(), bean.getTel());
        addPhone(bean.getPhoneList(), bean.getTel3());
        addPhone(bean.getPhoneList(), bean.getTel4());
    }

    private static void addPhone(List<String> list, String tel){
        if(StringUtils.isNotEmpty(tel)){
            list.add(tel);
        }
    }

    @Override
    public ResultMsgBean getOwnerGoodsDetail(Long userId,Long backendId,Integer operationType) throws InvocationTargetException, IllegalAccessException {
        TytTransportBackend tytTransportBackend = backendTransportMapper.selectById(backendId);
        if (Objects.isNull(tytTransportBackend)) {
            return null;
        }
        //货源取消状态不可编辑
        if (tytTransportBackend.getStatus().equals(2)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.COMPANY_BAN_EDIT, "当前状态不可编辑");
        }
        //企业货源列表非重新找车按钮时 不可编辑
        if (operationType.equals(0) && (tytTransportBackend.getStatus().equals(6) && tytTransportBackend.getOrderStatus().equals(60))) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.COMPANY_BAN_EDIT, "当前状态不可编辑");
        }
        if (Objects.nonNull(tytTransportBackend.getSrcMsgId())) {
            TransportMain transportMain = transportMainService.getTransportMainForId(tytTransportBackend.getSrcMsgId());
            if (Objects.nonNull(transportMain)) {
                SingleGoodsDetailBean singleGoodsDetailBean = infoFeeBusinessService.getSingleGoodsDetailBean(userId, transportMain);
                singleGoodsDetailBean.setBackendId(backendId);
                return ResultMsgBean.successResponse(singleGoodsDetailBean);
            }
        }
        SingleGoodsDetailBean singleGoodsDetailBean = new SingleGoodsDetailBean();
        BeanUtils.copyProperties(tytTransportBackend, singleGoodsDetailBean);
        singleGoodsDetailBean.setBackendId(backendId);
        //首次编辑发布货源 调车数量默认为1
        singleGoodsDetailBean.setShuntingQuantity(1);

        //以下为处理企业货源如果出发地或目的地省市区与tyt库不匹配则根据省市区信息调用高德接口利用高德进行一次修正，这时将高德修正的省市区信息返回给移动端，如果调用高德返回为null 则返回【出发地、目的地】这是为了兼容IOS
        // 同时并将出发地或目的地详细地址拼接上企业发货时的省市区（规则如此）
        //addrMatchingStatus 是在第三方企业发货时进行标记的，位置在 tyt-owner项目 /openapi/transport/publish 接口
        if (tytTransportBackend.getAddrMatchingStatus().equals(1)) {
            //将出发地目的地拼接到详细地址上
            singleGoodsDetailBean.setStartDetailAdd(tytTransportBackend.getStartPoint() + tytTransportBackend.getStartDetailAdd());
            String startProvince = tytTransportBackend.getStartProvinc();
            String startCity = tytTransportBackend.getStartCity();
            String startArea = tytTransportBackend.getStartArea();
            String detailAdd = tytTransportBackend.getStartDetailAdd();
            //调用高德接口进行一次修正
            GeoInfo startGeoInfo = MapUtil.getGeoInfo(startProvince + startCity + startArea + detailAdd, startCity);
            if (Objects.nonNull(startGeoInfo)) {
                singleGoodsDetailBean.setStartPoint(startGeoInfo.getProvince() + startGeoInfo.getCity() + startGeoInfo.getDistrict());
                singleGoodsDetailBean.setStartLongitude(startGeoInfo.getLongitude());
                singleGoodsDetailBean.setStartLatitude(startGeoInfo.getLatitude());
                TytCity tytCity = tytCityMapper.selectByAddress(startGeoInfo.getProvince(), startGeoInfo.getCity(), startGeoInfo.getDistrict());
                if (Objects.nonNull(tytCity)){
                    singleGoodsDetailBean.setStartCoordX(tytCity.getPx());
                    singleGoodsDetailBean.setStartCoordY(tytCity.getPy());
                }
            }else {
                //兼容IOS 具体可@霍兵兵(下同)
                singleGoodsDetailBean.setStartPoint("出发地");
            }

            singleGoodsDetailBean.setDestDetailAdd(tytTransportBackend.getDestPoint() + tytTransportBackend.getDestDetailAdd());
            String destProvince = tytTransportBackend.getDestProvinc();
            String destCity = tytTransportBackend.getDestCity();
            String destArea = tytTransportBackend.getDestArea();
            String destDetailAdd = tytTransportBackend.getDestDetailAdd();
            GeoInfo destGeoInfo = MapUtil.getGeoInfo(destProvince + destCity + destArea + destDetailAdd, destCity);
            if (Objects.nonNull(destGeoInfo)) {
                singleGoodsDetailBean.setDestPoint(destGeoInfo.getProvince() + destGeoInfo.getCity() + destGeoInfo.getDistrict());
                singleGoodsDetailBean.setDestLongitude(destGeoInfo.getLongitude());
                singleGoodsDetailBean.setDestLatitude(destGeoInfo.getLatitude());
                TytCity tytCity = tytCityMapper.selectByAddress(destGeoInfo.getProvince(),destGeoInfo.getCity(),destGeoInfo.getDistrict());
                if (Objects.nonNull(tytCity)){
                    singleGoodsDetailBean.setDestCoordX(tytCity.getPx());
                    singleGoodsDetailBean.setDestCoordY(tytCity.getPy());
                }
            }else {
                singleGoodsDetailBean.setStartPoint("目的地");
            }
        }

        if (tytTransportBackend.getAddrMatchingStatus().equals(2)) {
            singleGoodsDetailBean.setStartDetailAdd(tytTransportBackend.getStartPoint() + tytTransportBackend.getStartDetailAdd());
            String startProvince = tytTransportBackend.getStartProvinc();
            String startCity = tytTransportBackend.getStartCity();
            String startArea = tytTransportBackend.getStartArea();
            String detailAdd = tytTransportBackend.getStartDetailAdd();
            GeoInfo startGeoInfo = MapUtil.getGeoInfo(startProvince + startCity + startArea + detailAdd, startCity);
            if (Objects.nonNull(startGeoInfo)) {
                singleGoodsDetailBean.setStartPoint(startGeoInfo.getProvince() + startGeoInfo.getCity() + startGeoInfo.getDistrict());
                singleGoodsDetailBean.setStartLongitude(startGeoInfo.getLongitude());
                singleGoodsDetailBean.setStartLatitude(startGeoInfo.getLatitude());
                TytCity tytCity = tytCityMapper.selectByAddress(startGeoInfo.getProvince(), startGeoInfo.getCity(), startGeoInfo.getDistrict());
                if (Objects.nonNull(tytCity)){
                    singleGoodsDetailBean.setStartCoordX(tytCity.getPx());
                    singleGoodsDetailBean.setStartCoordY(tytCity.getPy());
                }
            }else {
                singleGoodsDetailBean.setStartPoint("出发地");
            }
        }

        if (tytTransportBackend.getAddrMatchingStatus().equals(3)) {
            singleGoodsDetailBean.setDestDetailAdd(tytTransportBackend.getDestPoint() + tytTransportBackend.getDestDetailAdd());
            String destProvince = tytTransportBackend.getDestProvinc();
            String destCity = tytTransportBackend.getDestCity();
            String destArea = tytTransportBackend.getDestArea();
            String destDetailAdd = tytTransportBackend.getDestDetailAdd();
            GeoInfo destGeoInfo = MapUtil.getGeoInfo(destProvince + destCity + destArea + destDetailAdd, destCity);
            if (Objects.nonNull(destGeoInfo)) {
                singleGoodsDetailBean.setDestPoint(destGeoInfo.getProvince() + destGeoInfo.getCity() + destGeoInfo.getDistrict());
                singleGoodsDetailBean.setDestLongitude(destGeoInfo.getLongitude());
                singleGoodsDetailBean.setDestLatitude(destGeoInfo.getLatitude());
                TytCity tytCity = tytCityMapper.selectByAddress(destGeoInfo.getProvince(),destGeoInfo.getCity(),destGeoInfo.getDistrict());
                if (Objects.nonNull(tytCity)){
                    singleGoodsDetailBean.setDestCoordX(tytCity.getPx());
                    singleGoodsDetailBean.setDestCoordY(tytCity.getPy());
                }
            }else {
                singleGoodsDetailBean.setDestPoint("目的地");
            }
        }
        return ResultMsgBean.successResponse(singleGoodsDetailBean);
    }
    /**
     * 查询限时货源列表
     * @param userId  用户id
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @param queryId 下拉是第一条信息的ID，下滑是最后一条信息的ID；（首次queryID=0）
     * @return
     */
    @Override
    public List<StationReceiveInfoBean> getMyBackend(Long userId, Long queryActionType, Long queryID) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT tb.id as backendId, " +
                "tb.start_point as startPoint, " +
                "tb.dest_point as destPoint, " +
                "tb.task_content as taskContent, " +
                "tb.tel as tel, " +
                "tb.status as status, " +
                "tb.order_status as orderStatus, " +
                "tb.is_precise_push as isPrecisePush, " +
                "tb.source as source, " +
                "tb.ctime as ctime, " +
                "tb.price as price, " +
                "tb.user_id as userId, " +
                "tb.start_coord_x as startCoordX, " +
                "tb.start_coord_y as startCoordY, " +
                "tb.dest_coord_x as destCoordX, " +
                "tb.dest_coord_y as destCoordY, " +
                "tb.start_longitude as startLongitude, " +
                "tb.start_latitude as startLatitude, " +
                "tb.dest_longitude as destLongitude, " +
                "tb.dest_latitude as destLatitude, " +
                "tb.start_detail_add as startDetailAdd, " +
                "tb.dest_detail_add as destDetailAdd, " +
                "tb.weight as weight, " +
                "tb.length as length, " +
                "tb.wide as wide, " +
                "tb.high as high, " +
                "tb.remark as remark, " +
                "tb.tel3 as tel3, " +
                "tb.tel4 as tel4, " +
                "tb.start_city as startCity, " +
                "tb.start_provinc as startProvinc, " +
                "tb.start_area as startArea, " +
                "tb.dest_provinc as destProvinc, " +
                "tb.dest_city as destCity, " +
                "tb.dest_area as destArea, " +
                "tb.type as type, " +
                "tb.brand as brand, " +
                "tb.good_type_name as goodTypeName, " +
                "tb.loading_time as loadingTime, " +
                "tb.unload_time as unloadTime, " +
                "tb.car_min_length as carMinLength, " +
                "tb.car_max_length as carMaxLength, " +
                "tb.car_type as carType, " +
                "tb.car_style as carStyle, " +
                "tb.work_plane_min_high as workPlaneMinHigh, " +
                "tb.work_plane_max_high as workPlaneMaxHigh, " +
                "tb.work_plane_min_length as workPlaneMinLength, " +
                "tb.work_plane_max_length as workPlaneMaxLength, " +
                "tb.climb as climb, " +
                "tb.find_car_type as findCarType, " +
                "tb.cancel_confirm as cancelConfirm, " +
                "tb.src_msg_id as srcMsgId, " +
                "tbu.publish_time as publishTime, " +
                "tbu.receive_order_time as receiveOrderTime, " +
                "twui.user_name as appletsUserName "+
                "FROM " +
                "tyt_transport_backend_user tbu " +
                "LEFT JOIN tyt_transport_backend tb on tbu.backend_id = tb.id and tb.batch = tbu.batch " +
                "LEFT JOIN tyt_wx_user_info twui on tb.applets_user_id = twui.user_id "+
                "WHERE tbu.user_id = ? and tb.order_no is not null";
        params.add(userId);
        if(queryActionType == 2 && queryID > 0){
            sql += " and tb.id < ? ";
            params.add(queryID);
        }
        sql +=" ORDER BY tb.id desc limit ?";
        params.add(AppConfig.getIntProperty("tyt.tyt_transport.station.receive.query.page.size"));

        Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("backendId", Hibernate.LONG);
        scalarMap.put("srcMsgId", Hibernate.LONG);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("tel", Hibernate.STRING);
        scalarMap.put("tel3", Hibernate.STRING);
        scalarMap.put("tel4", Hibernate.STRING);
        scalarMap.put("status", Hibernate.INTEGER);
        scalarMap.put("orderStatus", Hibernate.INTEGER);
        scalarMap.put("isPrecisePush", Hibernate.INTEGER);
        scalarMap.put("source", Hibernate.INTEGER);
        scalarMap.put("ctime", Hibernate.TIMESTAMP);
        scalarMap.put("price", Hibernate.STRING);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("startDetailAdd", Hibernate.STRING);
        scalarMap.put("destDetailAdd", Hibernate.STRING);
        scalarMap.put("weight", Hibernate.STRING);
        scalarMap.put("length", Hibernate.STRING);
        scalarMap.put("wide", Hibernate.STRING);
        scalarMap.put("high", Hibernate.STRING);
        scalarMap.put("remark", Hibernate.STRING);
        scalarMap.put("startCity", Hibernate.STRING);
        scalarMap.put("startProvinc", Hibernate.STRING);
        scalarMap.put("startArea", Hibernate.STRING);
        scalarMap.put("destProvinc", Hibernate.STRING);
        scalarMap.put("destCity", Hibernate.STRING);
        scalarMap.put("destArea", Hibernate.STRING);
        scalarMap.put("type", Hibernate.STRING);
        scalarMap.put("brand", Hibernate.STRING);
        scalarMap.put("goodTypeName", Hibernate.STRING);
        scalarMap.put("loadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("unloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("carMinLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("carMaxLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("carType", Hibernate.STRING);
        scalarMap.put("carStyle", Hibernate.STRING);
        scalarMap.put("workPlaneMinHigh", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMaxHigh", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMinLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMaxLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("climb", Hibernate.STRING);
        scalarMap.put("receiveOrderTime", Hibernate.TIMESTAMP);
        scalarMap.put("publishTime", Hibernate.TIMESTAMP);
        scalarMap.put("startCoordX", Hibernate.STRING);
        scalarMap.put("startCoordY", Hibernate.STRING);
        scalarMap.put("destCoordX", Hibernate.STRING);
        scalarMap.put("destCoordY", Hibernate.STRING);
        scalarMap.put("startLongitude", Hibernate.STRING);
        scalarMap.put("startLatitude", Hibernate.STRING);
        scalarMap.put("destLongitude", Hibernate.STRING);
        scalarMap.put("destLatitude", Hibernate.STRING);
        scalarMap.put("findCarType", Hibernate.INTEGER);
        scalarMap.put("cancelConfirm", Hibernate.INTEGER);
        scalarMap.put("appletsUserName", Hibernate.STRING);


        List<StationReceiveInfoBean> list = this.getBaseDao().search(sql, scalarMap, StationReceiveInfoBean.class, params.toArray());
        String virtualNumberSwitch = tytConfigService.getStringValue(Constant.VIRTUAL_NUMBER_SWITCH, "0");
        if(CollectionUtils.isNotEmpty(list) && Objects.equals(virtualNumberSwitch,"1")){
            String dialPhone = transportBackendAxbBinderService.getDialPhoneByUserId(userId);
            Set<String> idxSet = new HashSet<>();
            list.stream().forEach(a -> {
                Set<String> set=new TreeSet<String>();
                set.add(dialPhone);
                set.add(a.getTel());
                String idx = StringUtils.join(set, "_");
                idxSet.add(idx);
                if(StringUtils.isNotBlank(a.getTel3())){
                    Set<String> set2=new TreeSet<String>();
                    set2.add(dialPhone);
                    set2.add(a.getTel3());
                    String idx2 = StringUtils.join(set2, "_");
                    idxSet.add(idx2);
                }
            });
            if(idxSet.size() > 0){
                List<TytTransportBackendAxbBinder> binders = transportBackendAxbBinderService.findByIdxSet(idxSet);
                Map<String, TytTransportBackendAxbBinder> map = binders.stream().collect(Collectors.toMap(a -> a.getBindIndex(), a -> a, (b, c) -> b));
                list.stream().forEach(a -> {
                    Set<String> set=new TreeSet<String>();
                    set.add(dialPhone);
                    set.add(a.getTel());
                    String idx = StringUtils.join(set, "_");
                    TytTransportBackendAxbBinder telBinder = Optional.ofNullable(map.get(idx)).orElse(new TytTransportBackendAxbBinder());
                    a.setTel(telBinder.getMiddleNumber());
                    if(StringUtils.isNotBlank(a.getTel3())){
                        Set<String> set2=new TreeSet<String>();
                        set2.add(dialPhone);
                        set2.add(a.getTel3());
                        String idx2 = StringUtils.join(set2, "_");
                        TytTransportBackendAxbBinder tel3Binder = Optional.ofNullable(map.get(idx2)).orElse(new TytTransportBackendAxbBinder());
                        a.setTel3(tel3Binder.getMiddleNumber());
                    }
                    a.setTel4(null);
                });
            }

        }
        list.stream().peek(TransportBackendServiceImpl::initInfo).collect(Collectors.toList());
        return list;
    }

    @Override
    public TytTransportBackend getByTsId(Long tsId){
        String sql = "from TytTransportBackend where srcMsgId=?";
        List<TytTransportBackend> list = this.getBaseDao().find(sql, tsId);
        return list.size()>0?list.get(0):null;
    }

}
