package com.tyt.receive.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytTransportBackendUser;
import com.tyt.receive.service.TransportBackendUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("transportBackendUserService")
public class TransportBackendUserServiceImpl extends BaseServiceImpl<TytTransportBackendUser, Long> implements TransportBackendUserService {

    @Resource(name = "transportBackendUserDao")
    public void setBaseDao(BaseDao<TytTransportBackendUser, Long> transportBackendUserLongBaseDao) {
        super.setBaseDao(transportBackendUserLongBaseDao);
    }

    @Override
    public TytTransportBackendUser getByUserIdAndBackendId(Long userId, Long backendId) {
        List<TytTransportBackendUser> backendUsers = super.getBaseDao().find("from TytTransportBackendUser where userId = ? and backendId = ?", userId, backendId);
        if(CollectionUtils.isNotEmpty(backendUsers)){
            return backendUsers.get(0);
        }
        return null;
    }
}
