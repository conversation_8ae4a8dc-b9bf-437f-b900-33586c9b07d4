package com.tyt.receive.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytTransportBackend;
import com.tyt.receive.dao.TransportBackendDao;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2020/12/23 14:50
 */
@Repository("transportBackendDao")
public class TransportBackendDaoImpl extends BaseDaoImpl<TytTransportBackend,Long> implements TransportBackendDao {

    public TransportBackendDaoImpl() {
        this.setEntityClass(TytTransportBackend.class);
    }
}
