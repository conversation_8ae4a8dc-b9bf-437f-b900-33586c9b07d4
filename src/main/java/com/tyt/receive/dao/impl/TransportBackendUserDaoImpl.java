package com.tyt.receive.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytTransportBackendUser;
import com.tyt.receive.dao.TransportBackendUserDao;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2020/12/23 14:50
 */
@Repository("transportBackendUserDao")
public class TransportBackendUserDaoImpl extends BaseDaoImpl<TytTransportBackendUser,Long> implements TransportBackendUserDao {

    public TransportBackendUserDaoImpl() {
        this.setEntityClass(TytTransportBackendUser.class);
    }
}
