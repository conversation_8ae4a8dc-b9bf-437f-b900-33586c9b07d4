package com.tyt.receive.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytTransportBackendAxbBinder;
import com.tyt.receive.dao.TransportBackendAxbBinderDao;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2020/12/23 14:50
 */
@Repository("transportBackendAxbBinderDao")
public class TransportBackendAxbBinderDaoImpl extends BaseDaoImpl<TytTransportBackendAxbBinder,Long> implements TransportBackendAxbBinderDao {

    public TransportBackendAxbBinderDaoImpl() {
        this.setEntityClass(TytTransportBackendAxbBinder.class);
    }
}
