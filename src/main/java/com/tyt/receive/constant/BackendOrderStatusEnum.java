package com.tyt.receive.constant;

public enum BackendOrderStatusEnum {
    PENDING(10,"待接单"),
    NO_PUBLISH(11,"已接单未发布"),
    SCHEDULE_CANCEL(20,"已撤销(定时)"),
    OFFLINE_FINISH(21,"已线下成交"),
    HAND_CANCEL(22,"已线下成交"),
    DEAL_WITH(30,"已接单");

    private final int status;

    private final String text;

    BackendOrderStatusEnum(int status, String text) {
        this.status = status;
        this.text = text;
    }

    public int getStatus() {
        return status;
    }

    public String getText() {
        return text;
    }
}
