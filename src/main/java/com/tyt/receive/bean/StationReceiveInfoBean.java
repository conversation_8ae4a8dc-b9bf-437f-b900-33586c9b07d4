package com.tyt.receive.bean;


import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/23 10:22
 */
public class StationReceiveInfoBean {


    /**
     * 出发地(省市区以减号-分割开)
     */
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    private String destPoint;

    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 联系人
     */
    private List<String> phoneList;

    /**
     * 状态 1-待接单 2-已取消 3-已接单
     */
    private Integer status;

    /**
     * 10-待接单 20-已撤销 21-已线下成交 22-已撤销 30-已接单
     */
    private Integer orderStatus;

    /**
     * 是否精准推送 0不是 1是
     */
    private Integer isPrecisePush;

    /**
     * 人工/自动
     */
    private Integer source;

    /**
     * 采集时间
     */
    private Date ctime;
    /**
     * 运费
     */
    private String price;

    /**
     * 发布人usreid 关联tyt_user表id
     */
    private Long userId;

    /**
     * 出发地详细地址
     */
    private String startDetailAdd;

    /**
     * 目的地详细地址
     */
    private String destDetailAdd;

    /**
     * 重量单位吨
     */
    private String weight;

    /**
     * 货物长单位米
     */
    private String length;

    /**
     * 货物宽单位米
     */
    private String wide;

    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 备注
     */
    private String remark;

    /**
     * 出发地城市
     */
    private String startCity;

    /**
     * 出发地省
     */
    private String startProvinc;

    /**
     * 出发地区
     */
    private String startArea;

    /**
     * 目的地省
     */
    private String destProvinc;

    /**
     * 目的地市
     */
    private String destCity;

    /**
     * 目的地区
     */
    private String destArea;

    /**
     * 货物型号
     */
    private String type;

    /**
     * 货物品牌
     */
    private String brand;

    /**
     * 货物类型名称，如“装载机”，“挖掘机”
     */
    private String goodTypeName;

    /**
     * 装车时间
     */
    private Date loadingTime;

    /**
     * 卸车时间
     */
    private Date unloadTime;

    /**
     * 车辆最低长度，单位米
     */
    private BigDecimal carMinLength;

    /**
     * 车辆最大长度，单位米
     */
    private BigDecimal carMaxLength;

    /**
     * 车辆类型
     */
    private String carType;

    /**
     * 挂车样式
     */
    private String carStyle;

    /**
     * 工作面高最小值，单位米
     */
    private BigDecimal workPlaneMinHigh;

    /**
     * 工作面高最大值，单位米
     */
    private BigDecimal workPlaneMaxHigh;

    /**
     * 工作面长最小值，单位米
     */
    private BigDecimal workPlaneMinLength;

    /**
     * 工作面长最大值，单位米
     */
    private BigDecimal workPlaneMaxLength;

    /**
     * 是否需要爬梯
     */
    private String climb;

    private String tel;

    private String tel3;

    private String tel4;

    private Date receiveOrderTime;

    private Date publishTime;


    /**
     * id
     */
    private Long backendId;


    /**
     * 出发地坐标x
     */
    private String startCoordX;

    /**
     * 出发地坐标y
     */
    private String startCoordY;

    /**
     * 目的地坐标x
     */
    private String destCoordX;

    /**
     * 目的地坐标y
     */
    private String destCoordY;

    /**
     * 出发地经度
     */
    private String startLongitude;

    /**
     * 出发地纬度
     */
    private String startLatitude;

    /**
     * 目的地经度
     */
    private String destLongitude;

    /**
     * 目的地纬度
     */
    private String destLatitude;

    /**
     * 服务器当前时间
     */
    private Date time;

    /**
     * main货源id，用于关联订单查询状态
     */
    private Long srcMsgId;

    /**
     * 找车方式 0:指派熟车 1:平台找车
     */
    private Integer findCarType;

    /**
     * 是否已确认取消 0未确认 1已确认
     */
    private Integer cancelConfirm;
    /**
     * 小程序发货人昵称
     */
    private String appletsUserName;


    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getStartCoordX() {
        return startCoordX;
    }

    public void setStartCoordX(String startCoordX) {
        this.startCoordX = startCoordX;
    }

    public String getStartCoordY() {
        return startCoordY;
    }

    public void setStartCoordY(String startCoordY) {
        this.startCoordY = startCoordY;
    }

    public String getDestCoordX() {
        return destCoordX;
    }

    public void setDestCoordX(String destCoordX) {
        this.destCoordX = destCoordX;
    }

    public String getDestCoordY() {
        return destCoordY;
    }

    public void setDestCoordY(String destCoordY) {
        this.destCoordY = destCoordY;
    }

    public String getStartLongitude() {
        return startLongitude;
    }

    public void setStartLongitude(String startLongitude) {
        this.startLongitude = startLongitude;
    }

    public String getStartLatitude() {
        return startLatitude;
    }

    public void setStartLatitude(String startLatitude) {
        this.startLatitude = startLatitude;
    }

    public String getDestLongitude() {
        return destLongitude;
    }

    public void setDestLongitude(String destLongitude) {
        this.destLongitude = destLongitude;
    }

    public String getDestLatitude() {
        return destLatitude;
    }

    public void setDestLatitude(String destLatitude) {
        this.destLatitude = destLatitude;
    }

    public Long getBackendId() {
        return backendId;
    }

    public void setBackendId(Long backendId) {
        this.backendId = backendId;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public List<String> getPhoneList() {
        return phoneList;
    }

    public void setPhoneList(List<String> phoneList) {
        this.phoneList = phoneList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getIsPrecisePush() {
        return isPrecisePush;
    }

    public void setIsPrecisePush(Integer isPrecisePush) {
        this.isPrecisePush = isPrecisePush;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getStartDetailAdd() {
        return startDetailAdd;
    }

    public void setStartDetailAdd(String startDetailAdd) {
        this.startDetailAdd = startDetailAdd;
    }

    public String getDestDetailAdd() {
        return destDetailAdd;
    }

    public void setDestDetailAdd(String destDetailAdd) {
        this.destDetailAdd = destDetailAdd;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getWide() {
        return wide;
    }

    public void setWide(String wide) {
        this.wide = wide;
    }

    public String getHigh() {
        return high;
    }

    public void setHigh(String high) {
        this.high = high;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStartCity() {
        return startCity;
    }

    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }

    public String getStartProvinc() {
        return startProvinc;
    }

    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }

    public String getStartArea() {
        return startArea;
    }

    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }

    public String getDestProvinc() {
        return destProvinc;
    }

    public void setDestProvinc(String destProvinc) {
        this.destProvinc = destProvinc;
    }

    public String getDestCity() {
        return destCity;
    }

    public void setDestCity(String destCity) {
        this.destCity = destCity;
    }

    public String getDestArea() {
        return destArea;
    }

    public void setDestArea(String destArea) {
        this.destArea = destArea;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getGoodTypeName() {
        return goodTypeName;
    }

    public void setGoodTypeName(String goodTypeName) {
        this.goodTypeName = goodTypeName;
    }

    public Date getLoadingTime() {
        return loadingTime;
    }

    public void setLoadingTime(Date loadingTime) {
        this.loadingTime = loadingTime;
    }

    public Date getUnloadTime() {
        return unloadTime;
    }

    public void setUnloadTime(Date unloadTime) {
        this.unloadTime = unloadTime;
    }

    public BigDecimal getCarMinLength() {
        return carMinLength;
    }

    public void setCarMinLength(BigDecimal carMinLength) {
        this.carMinLength = carMinLength;
    }

    public BigDecimal getCarMaxLength() {
        return carMaxLength;
    }

    public void setCarMaxLength(BigDecimal carMaxLength) {
        this.carMaxLength = carMaxLength;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getCarStyle() {
        return carStyle;
    }

    public void setCarStyle(String carStyle) {
        this.carStyle = carStyle;
    }

    public BigDecimal getWorkPlaneMinHigh() {
        return workPlaneMinHigh;
    }

    public void setWorkPlaneMinHigh(BigDecimal workPlaneMinHigh) {
        this.workPlaneMinHigh = workPlaneMinHigh;
    }

    public BigDecimal getWorkPlaneMaxHigh() {
        return workPlaneMaxHigh;
    }

    public void setWorkPlaneMaxHigh(BigDecimal workPlaneMaxHigh) {
        this.workPlaneMaxHigh = workPlaneMaxHigh;
    }

    public BigDecimal getWorkPlaneMinLength() {
        return workPlaneMinLength;
    }

    public void setWorkPlaneMinLength(BigDecimal workPlaneMinLength) {
        this.workPlaneMinLength = workPlaneMinLength;
    }

    public BigDecimal getWorkPlaneMaxLength() {
        return workPlaneMaxLength;
    }

    public void setWorkPlaneMaxLength(BigDecimal workPlaneMaxLength) {
        this.workPlaneMaxLength = workPlaneMaxLength;
    }

    public String getClimb() {
        return climb;
    }

    public void setClimb(String climb) {
        this.climb = climb;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getTel3() {
        return tel3;
    }

    public void setTel3(String tel3) {
        this.tel3 = tel3;
    }

    public String getTel4() {
        return tel4;
    }

    public void setTel4(String tel4) {
        this.tel4 = tel4;
    }

    public Date getReceiveOrderTime() {
        return receiveOrderTime;
    }

    public void setReceiveOrderTime(Date receiveOrderTime) {
        this.receiveOrderTime = receiveOrderTime;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    public Integer getFindCarType() {
        return findCarType;
    }

    public void setFindCarType(Integer findCarType) {
        this.findCarType = findCarType;
    }

    public Integer getCancelConfirm() {
        return cancelConfirm;
    }

    public void setCancelConfirm(Integer cancelConfirm) {
        this.cancelConfirm = cancelConfirm;
    }

    public String getAppletsUserName() {
        return appletsUserName;
    }

    public void setAppletsUserName(String appletsUserName) {
        this.appletsUserName = appletsUserName;
    }
}
