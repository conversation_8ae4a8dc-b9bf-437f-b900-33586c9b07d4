package com.tyt.asoIntegralWall.service;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.AsoIntegralWallLocal;
import com.tyt.model.AsoIntegralWallPartner;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Date;

@Service("asoIntegralWallService")
public class AsoIntegralWallServiceImpl extends BaseServiceImpl<AsoIntegralWallPartner, Long> implements AsoIntegralWallService{

    @Resource(name = "asoIntegralWallDao")
    public void setBaseDao(BaseDao<AsoIntegralWallPartner, Long> asoIntegralWallDao) {
        super.setBaseDao(asoIntegralWallDao);
    }


    @Override
    public void saveIdfa(AsoIntegralWallLocal asoIntegralWallLocal) {
        final String sql = "INSERT IGNORE INTO `aso_integral_wall_local` (phone_type,os_version,client_version,client_sign,appid,idfa,ctime) VALUES(?,?,?,?,?,?,?);";
        final Object[] params = {
                asoIntegralWallLocal.getPhoneType(),
                asoIntegralWallLocal.getOsVersion(),
                asoIntegralWallLocal.getClientVersion(),
                asoIntegralWallLocal.getClientSign(),
                asoIntegralWallLocal.getAppid(),
                asoIntegralWallLocal.getIdfa(),
                new Date()
        };
        this.getBaseDao().executeUpdateSql(sql, params);
    }

    @Override
    public int checkIdfa(String idfa,String appid) {
        String sql = "SELECT count(*) FROM `aso_integral_wall_local` WHERE idfa=? and appid=?";
        BigInteger c = this.getBaseDao().query(sql, new Object[] {idfa,appid});
        if (c != null) {
            return c.intValue();
        }
        return 0;
    }

    @Override
    public void saveIdfaFromPanter(AsoIntegralWallPartner partner) {
        partner.setSctiveStatus(0);
        partner.setCtime(new Date());
        partner.setMtime(new Date());
        this.add(partner);
    }

    @Override
    public int updateStatus(String idfa,String channel,String appid) {
        String sql = "UPDATE `aso_integral_wall_partner` SET `active_status`=1 WHERE idfa = ? and channel=? and appid=?";
        int a = this.executeUpdateSql(sql,new Object[]{idfa,channel,appid });
        return a;
    }
}
