package com.tyt.asoIntegralWall.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.AsoIntegralWallLocal;
import com.tyt.model.AsoIntegralWallPartner;

public interface AsoIntegralWallService extends BaseService<AsoIntegralWallPartner, Long> {
    void saveIdfa(AsoIntegralWallLocal asoIntegralWallLocal);

    int checkIdfa(String idfa,String appid);

    void saveIdfaFromPanter(AsoIntegralWallPartner partner);

    int updateStatus(String idfa,String channel,String appid);
}
