package com.tyt.carinsurance.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.carinsurance.dao.CarInsuranceInquiryDao;
import com.tyt.infofee.dao.TransportOrdersDao;
import com.tyt.model.CarInsuranceInquiry;
import com.tyt.model.TytTransportOrders;
import org.springframework.stereotype.Repository;

/**
 * @Description  车险询价数据层实现类
 * <AUTHOR>
 * @Date  2019/3/12 14:59
 * @Param
 * @return
 **/
@Repository("carInsuranceInquiryDao")
public class CarInsuranceInquiryDaoImpl extends BaseDaoImpl<CarInsuranceInquiry, Long>  implements CarInsuranceInquiryDao {

    public CarInsuranceInquiryDaoImpl() {
        this.setEntityClass(CarInsuranceInquiry.class);
    }

}
