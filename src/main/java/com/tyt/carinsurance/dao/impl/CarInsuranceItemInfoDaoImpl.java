package com.tyt.carinsurance.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.carinsurance.dao.CarInsuranceInquiryDao;
import com.tyt.carinsurance.dao.CarInsuranceItemInfoDao;
import com.tyt.model.CarInsuranceInquiry;
import com.tyt.model.CarInsuranceItemInfo;
import org.springframework.stereotype.Repository;

/**
 * @Description  车险询价保项保额数据层实现类
 * <AUTHOR>
 * @Date  2019/3/12 14:59
 * @Param
 * @return
 **/
@Repository("carInsuranceItemInfoDao")
public class CarInsuranceItemInfoDaoImpl extends BaseDaoImpl<CarInsuranceItemInfo, Long>  implements CarInsuranceItemInfoDao {

    public CarInsuranceItemInfoDaoImpl() {
        this.setEntityClass(CarInsuranceItemInfo.class);
    }

}
