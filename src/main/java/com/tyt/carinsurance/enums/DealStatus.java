package com.tyt.carinsurance.enums;

/**
 * @Description 车险询价-处理结果(跟踪状态)
 *              1.待询价 2.待处理 3.待投保 4.已投保 5.不投保 6.需二次沟通 7.用户需考虑
 * <AUTHOR>
 * @Date  2019/3/13 16:31
 * @Param 
 * @return 
 **/
public enum DealStatus {

    NO_INVALID(0,"无效"),
    WAIT_DEAL(1,"待处理"),
    IN_DEALING(2,"处理中"),
    ALREADY_DEAL(3,"已处理"),
    ALREADY_INSURE(4,"已投保")
    ;
    /**
     * 状态值
     */
    private int status;
    /**
     * 前端显示
     */
    private String value;

    DealStatus(int status, String value) {
        this.status = status;
        this.value = value;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
