package com.tyt.carinsurance.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.carinsurance.bean.CarInsuranceDetailBean;
import com.tyt.carinsurance.bean.CarInsuranceDictBean;
import com.tyt.carinsurance.enums.DealStatus;
import com.tyt.carinsurance.service.CarInsuranceFileInfoService;
import com.tyt.carinsurance.service.CarInsuranceInquiryService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.CarInsuranceFileInfo;
import com.tyt.model.CarInsuranceInquiry;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.commons.tools.AliOssClient;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.service.CarService;
import com.tyt.util.*;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description  车险询价控制层
 * <AUTHOR>
 * @Date  2019/3/12 14:58
 * @Param 
 * @return 
 **/
@Controller
@RequestMapping("/plat/carInsurance/inquiry/")
public class CarInsuranceInquiryController extends BaseController {

    //文件的根路径
    private final String FILE_PATH_DOMAIN = AppConfig.getProperty("picture.path.domain");

    @Resource(name = "carInsuranceInquiryService")
    private CarInsuranceInquiryService carInsuranceInquiryService;
    @Resource(name = "carService")
    private CarService carService;
    @Resource(name = "carInsuranceFileInfoService")
    private CarInsuranceFileInfoService carInsuranceFileInfoService;

    @Autowired
    private AliOssClient aliOssClient;

    /**
     * @Description  车险询价字典列表接口
     * <AUTHOR>
     * @Date  2019/3/14 12:05
     * @Param [baseParameter]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"carInsuranceDictList","carInsuranceDictList.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean carInsuranceDictList(BaseParameter baseParameter) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户Id不能为空！");
                return rm;
            }
            CarInsuranceDictBean carInsuranceDictBean = carInsuranceInquiryService.getCarInsuranceDictList(userId);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("车险询价字典列表查询成功");
            rm.setData(carInsuranceDictBean);
        } catch (Exception ex) {
            logger.error("服务器异常", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @Description  车险询价进度查询列表接口
     * <AUTHOR>
     * @Date  2019/3/14 12:05
     * @Param [baseParameter]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"carInsuranceProcessList","carInsuranceProcessList.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean carInsuranceProcessList(BaseParameter baseParameter) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户Id不能为空！");
                return rm;
            }
            //车险询价进度查询列表
            List<CarInsuranceInquiry> carInsuranceInquiryList = carInsuranceInquiryService.getCarInsuranceInquiryList(userId);
            Map<String,List<CarInsuranceInquiry>> inquiryMap = new HashMap<String,List<CarInsuranceInquiry>>();
            inquiryMap.put("carInsuranceInquiryList", carInsuranceInquiryList);

            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("车险询价进度查询列表查询成功");
            rm.setData(inquiryMap);
        } catch (Exception ex) {
            logger.error("服务器异常", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
    * @Description  提交资料，立即询价接口(第一步)
    * <AUTHOR>
    * @Date  2019/3/14 12:07
    * @Param [baseParameter, linkPhone, carHeadCity, carHeadNo, carOwner, carHeadDrivingBook]
    * @return com.tyt.model.ResultMsgBean
    **/
    @RequestMapping(value = {"commitInfoFirst","commitInfoFirst.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean commitInfoFirst(BaseParameter baseParameter,
                                         String linkPhone,String carHeadCity,
                                         String carHeadNo,Integer carOwner,
                                         String carHeadDrivingBook) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户Id不能为空！");
                return rm;
            }
            //1.根据车头车牌号判断该辆车是否进行过询价
            CarInsuranceInquiry carInsuranceInquiry = carInsuranceInquiryService.getInquiryInfoByHeadNo(userId, carHeadCity, carHeadNo);
            //车险询价Id
            Long inquiryId = null;
            //处理结果(跟踪状态)
            Integer dealStatus = 0;
            if(carInsuranceInquiry != null)
            {
                inquiryId = carInsuranceInquiry.getId();
                //处理结果(跟踪状态)
                dealStatus = carInsuranceInquiry.getDealStatus();
                //填写查询信息页：待处理、处理中状态的车辆不能再次发起询价，
                // 已处理、已投保状态的车辆可以再次发起询价
                if(dealStatus != null && (dealStatus.intValue() == DealStatus.WAIT_DEAL.getStatus()
                                      || dealStatus.intValue() == DealStatus.IN_DEALING.getStatus())) {
                    rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    rm.setMsg("该车辆正在询价中,请到保险大厅_车险进度中查看！");
                    return rm;
                }
            }
            if(StringUtils.isBlank(linkPhone)){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("联系电话不能为空！");
                return rm;
            }
            if(!MobileUtil.isMobile(linkPhone)){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("联系电话格式有误,请重新输入！");
                return rm;
            }
            if(StringUtils.isBlank(carHeadCity) || StringUtils.isBlank(carHeadNo)){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("请选择或输入查询的车辆车牌号！");
                return rm;
            }
            if(!CarPlateNoUtil.checkPlateNumberFormat(carHeadCity+carHeadNo))
            {
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("车辆车牌号格式有误,请重新输入！");
                return rm;
            }
            if(carOwner == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("车辆所属不能为空！");
                return rm;
            }
            //车头行驶本Url
            String headDrivingUrl = "";
            //将Base64转换成MultipartFile
            MultipartFile drivingBook = null;
            if(StringUtils.isNotBlank(carHeadDrivingBook)){ //如果车头行驶本字符串不为空
                drivingBook = Base64Util.base64ToMultipart(carHeadDrivingBook);
            }
            //车头行驶本,如果上传的车头行驶本为空
            if(drivingBook == null){
                //1.先判断从车险询价证件信息是否存在
                if(carInsuranceInquiry != null)
                {
                    List<CarInsuranceFileInfo> fileInfoList = carInsuranceFileInfoService.getFileInfoList(inquiryId, 1);
                    if(fileInfoList != null && fileInfoList.size() > 0)
                    {
                        CarInsuranceFileInfo carInsuranceFileInfo = fileInfoList.get(0);
                        //车头行驶本Url,将Url转换成MultipartFile
                        headDrivingUrl = carInsuranceFileInfo.getFilePath();
                    }
                }else{//2.再判断认证车辆的信息是否存在,取认证车辆的车头行驶本
                    //认证车辆信息
                    List<QueryCar> authCarInfoList = carService.getAuthCarInfoByHeadNo(userId, carHeadCity, carHeadNo);
                    if(authCarInfoList != null && authCarInfoList.size() > 0)
                    {
                        QueryCar queryCar = authCarInfoList.get(0);
                        //车头行驶本Url,将Url转换成MultipartFile
                        headDrivingUrl = queryCar.getHeadDrivingUrl();
                    }
                }
                if(StringUtils.isBlank(headDrivingUrl))
                {
                    rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    rm.setMsg("请上传车头行驶本！");
                    return rm;
                }
                String headDrivingPath =FILE_PATH_DOMAIN + headDrivingUrl;
//                if (headDrivingUrl.startsWith("http")) { // 包含http 认为是oss文件
//                    logger.info("下载文件地址headDrivingUrl：{}" , headDrivingUrl);
//                    String url = renamePic();
//                    headDrivingPath = FILE_PATH_DOMAIN + url;
//                    logger.info("下载文件地址headDrivingPath：{}" , headDrivingPath);
//                    aliOssClient.downloadFile(headDrivingUrl, headDrivingPath);
//                }else { // 服务器文件处理
//                    headDrivingPath = FILE_PATH_DOMAIN + headDrivingUrl;
//                }
                //车头行驶本路径
                File file = new File(headDrivingPath);
                FileItem fileItem = new DiskFileItem("carHeadDrivingBook", Files.probeContentType(file.toPath()), false, file.getName(), (int) file.length(), file.getParentFile());
                //try-with-resources,不用再写finally代码块释放资源
                if(file != null){
                    try (InputStream input = new FileInputStream(file);
                         OutputStream os = fileItem.getOutputStream()) {
                        IOUtils.copy(input, os);
                        drivingBook = new CommonsMultipartFile(fileItem);
                        if(drivingBook == null){
                            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            rm.setMsg("请上传车头行驶本！");
                            return rm;
                        }
                    }
                }else{ //如果车头行驶本为空，则提示错误信息
                    rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    rm.setMsg("请上传车头行驶本！");
                    return rm;
                }
            }
            //文件大小
            Long fileSize = drivingBook.getSize();
            //限制文件大小不能超过5M,防止服务器压力过载、APP崩溃
            if(fileSize != null && fileSize.longValue() > 5*1024*1024)
            {
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("车头行驶本大小不能超过5M！");
                return rm;
            }
            //2.保存车险询价信息,获取询价信息id
            Map<String, Object> inquiryIdMap = carInsuranceInquiryService.saveInquiryInfoFirst(inquiryId,linkPhone, userId, carHeadCity, carHeadNo, carOwner, drivingBook);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("提交车险询价资料成功");
            rm.setData(inquiryIdMap);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("服务器异常", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    private String renamePic() {
        String domainurl = "/data/pictures/car/";//获取文件路径
        CreateFileUtil.createDir(AppConfig.getProperty("picture.path.domain") + domainurl);
        return domainurl + ImageUtil.renameFile();
    }

    /**
     * @Description  车险询价详情信息接口(第一步)
     *            -- 根据车头车牌号查询车险询价信息或车辆认证信息
     * <AUTHOR>
     * @Date  2019/3/14 12:08
     * @Param [baseParameter, carHeadCity, carHeadNo]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"inquiryDetailInfoFirst","inquiryDetailInfoFirst.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean inquiryDetailInfoFirst(BaseParameter baseParameter,String carHeadCity,String carHeadNo) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户Id不能为空！");
                return rm;
            }
            if(StringUtils.isBlank(carHeadCity)){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("车头牌照头字母不能为空！");
                return rm;
            }
            if(StringUtils.isBlank(carHeadNo)){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("车头车牌号不能为空！");
                return rm;
            }
            if(!CarPlateNoUtil.checkPlateNumberFormat(carHeadCity+carHeadNo))
            {
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("请输入正确的车辆车牌！");
                return rm;
            }
            CarInsuranceInquiry carInsuranceInquiry = carInsuranceInquiryService.getInquiryDetailInfoFirst(userId, carHeadCity, carHeadNo);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("获取车险询价详情信息成功");
            rm.setData(carInsuranceInquiry);
        } catch (Exception ex) {
            logger.error("服务器异常", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @Description  继续上传资料接口(第二步)
     * <AUTHOR>
     * @Date  2019/3/15 16:20
     * @Param [baseParameter, inquiryId, linkPhone, carHeadCity, carHeadNo, carOwner, identityCardFront, identityCardBack, businessLicence, carHeadDrivingBook, comInsurancePolicy, insuranceType, insuranceItemAmt]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"commitInfoSecond","commitInfoSecond.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean commitInfoSecond(BaseParameter baseParameter,
                                          Long inquiryId,String linkPhone,
                                          String carHeadCity,String carHeadNo,
                                          Integer carOwner,
                                          String identityCardFront,
                                          String identityCardBack,
                                          String businessLicence,
                                          String carHeadDrivingBook,
                                          String comInsurancePolicy,
                                          Integer insuranceType,String insuranceItemAmt) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户Id不能为空！");
                return rm;
            }
            //将Base64转换成MultipartFile
            MultipartFile identityCardFrontFile = null;
            MultipartFile identityCardBackFile = null;
            MultipartFile businessLicenceFile = null;
            MultipartFile carHeadDrivingBookFile = null;
            MultipartFile comInsurancePolicyFile = null;
            if(StringUtils.isNotBlank(identityCardFront))
            {
                identityCardFrontFile = Base64Util.base64ToMultipart(identityCardFront);
            }
            if(StringUtils.isNotBlank(identityCardBack))
            {
                identityCardBackFile = Base64Util.base64ToMultipart(identityCardBack);
            }
            if(StringUtils.isNotBlank(businessLicence))
            {
                businessLicenceFile = Base64Util.base64ToMultipart(businessLicence);
            }
            if(StringUtils.isNotBlank(carHeadDrivingBook))
            {
                carHeadDrivingBookFile = Base64Util.base64ToMultipart(carHeadDrivingBook);
            }
            if(StringUtils.isNotBlank(comInsurancePolicy))
            {
                comInsurancePolicyFile = Base64Util.base64ToMultipart(comInsurancePolicy);
            }

            //1.根据车险询价信息id查询该辆车的询价信息
            CarInsuranceInquiry carInsuranceInquiry = carInsuranceInquiryService.getById(inquiryId);
            if(carInsuranceInquiry != null)
            {
                //处理结果(跟踪状态)
                Integer dealStatus = carInsuranceInquiry.getDealStatus();
                if(dealStatus != null && dealStatus.intValue() > 0)
                {
                    //已投保状态的车辆不能修改提交资料，待处理、处理中、已处理状态的车辆可以修改提交资料
                    int status = dealStatus.intValue();
                    //已投保
                    if(status == DealStatus.ALREADY_INSURE.getStatus())
                    {
                        rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                        rm.setMsg(carHeadCity+carHeadNo+"已投保,不允许再次修改提交资料！");
                        return rm;
                    }
                    //之前为待处理状态,身份证正面不能为空
                    if(status == DealStatus.WAIT_DEAL.getStatus()){
                        if(identityCardFrontFile == null)
                        {
                            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            rm.setMsg("请上传身份证（正面）！");
                            return rm;
                        }
                        //车辆所属 1.个人 2.企业 --如果车辆所属为企业,则必须上传营业执照
                        if(carOwner != null && carOwner.intValue() == 2)
                        {
                            if(businessLicenceFile == null){
                                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                                rm.setMsg("车辆所属为企业，请上传营业执照！");
                                return rm;
                            }
                        }
                    }

                }
            }else{ //从未提交过车险询价资料，提示跳转第一步
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("请先跳转到第一步提交车险询价资料！");
                return rm;
            }
            //业务id(询价信息id)
            if(inquiryId == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("车险询价信息Id不能为空！");
                return rm;
            }
            if(StringUtils.isBlank(linkPhone)){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("联系电话不能为空！");
                return rm;
            }
            if(!MobileUtil.isMobile(linkPhone)){
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("联系电话格式有误,请重新输入！");
                return rm;
            }
            if(StringUtils.isBlank(carHeadCity) || StringUtils.isBlank(carHeadNo)){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("请选择或输入查询的车辆车牌号！");
                return rm;
            }
            if(!CarPlateNoUtil.checkPlateNumberFormat(carHeadCity+carHeadNo))
            {
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("请输入正确的车辆车牌！");
                return rm;
            }
            if(carOwner == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("车辆所属不能为空！");
                return rm;
            }
            if(insuranceType == null)
            {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("请选择险种！");
                return rm;
            }
            //车险险种 1.商业险 2.交强险 3.商业险、交强险
            int carInsuranceType = insuranceType.intValue();
            if(carInsuranceType == 1 || carInsuranceType == 3){
                if(StringUtils.isBlank(insuranceItemAmt)){
                    rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    rm.setMsg("请选择商业险保项！");
                    return rm;
                }
            }
            //2.保存车险询价信息,获取询价信息id
            Map<String, Object> inquiryIdMap = carInsuranceInquiryService.saveInquiryInfoSecond(userId,inquiryId,linkPhone,
                                                                                                carHeadCity,carHeadNo,carOwner,
                                                                                                identityCardFrontFile,identityCardBackFile,
                                                                                                businessLicenceFile, carHeadDrivingBookFile,
                                                                                                comInsurancePolicyFile,insuranceType,insuranceItemAmt);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("补充车险询价资料成功");
            rm.setData(inquiryIdMap);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("服务器异常", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * @Description  继续上传资料详情信息接口(第二步)
     *            -- 根据业务id(询价信息id)查询询价信息
     * <AUTHOR>
     * @Date  2019/3/15 11:48
     * @Param [baseParameter, inquiryId]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"inquiryDetailInfoSecond","inquiryDetailInfoSecond.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean inquiryDetailInfoSecond(BaseParameter baseParameter,Long inquiryId) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户Id不能为空！");
                return rm;
            }
            //业务id(询价信息id)
            if(inquiryId == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("车险询价信息Id不能为空！");
                return rm;
            }
            CarInsuranceDetailBean carInsuranceDetailBean = carInsuranceInquiryService.getInquiryDetailInfoSecond(userId, inquiryId);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("获取车险询价详情信息成功");
            rm.setData(carInsuranceDetailBean);
        } catch (Exception ex) {
            logger.error("服务器异常", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

}
