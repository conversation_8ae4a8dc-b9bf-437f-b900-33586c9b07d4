package com.tyt.carinsurance.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.model.CarInsuranceFileInfo;
import com.tyt.model.CarInsuranceInquiry;
import com.tyt.model.CarInsuranceItemInfo;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName CarInsuranceInfoBean
 * @Description 车险询价详情信息对象
 * <AUTHOR>
 * @Date 2019-03-15 11:50
 * @Version 1.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarInsuranceDetailBean extends CarInsuranceInquiry implements Serializable {

    //险种保项列表
    private List<CarInsuranceItemInfo> insuranceItemList;
    //车险询价证件信息列表
    private List<CarInsuranceFileInfo> insuranceFileList;

    public List<CarInsuranceItemInfo> getInsuranceItemList() {
        return insuranceItemList;
    }

    public void setInsuranceItemList(List<CarInsuranceItemInfo> insuranceItemList) {
        this.insuranceItemList = insuranceItemList;
    }

    public List<CarInsuranceFileInfo> getInsuranceFileList() {
        return insuranceFileList;
    }

    public void setInsuranceFileList(List<CarInsuranceFileInfo> insuranceFileList) {
        this.insuranceFileList = insuranceFileList;
    }
}
