package com.tyt.carinsurance.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.model.CarInsuranceInquiry;
import com.tyt.model.TytSource;
import com.tyt.user.querybean.SourceBean;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName carInsuranceDictBean
 * @Description  车险询价字典对象
 * <AUTHOR>
 * @Date 2019-03-12 15:11
 * @Version 1.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarInsuranceDictBean implements  Serializable {


    private static final long serialVersionUID = 1576347083332234448L;
    //联系电话
    private String linkPhone;
    //车头车牌号列表
    private List<CarInsuranceInquiry> carHeadNoList;
    //车辆所属字典列表
    private List<SourceBean> carOwnerList;
    //车险险种字典列表
    private List<SourceBean> insuranceTypeList;
    //商业险保项字典列表
    private List<TytSource> insuranceItemList;
    //处理结果(跟踪状态)字典列表
    private List<SourceBean> dealStatusList;


    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    public List<CarInsuranceInquiry> getCarHeadNoList() {
        return carHeadNoList;
    }

    public void setCarHeadNoList(List<CarInsuranceInquiry> carHeadNoList) {
        this.carHeadNoList = carHeadNoList;
    }

    public List<SourceBean> getCarOwnerList() {
        return carOwnerList;
    }

    public void setCarOwnerList(List<SourceBean> carOwnerList) {
        this.carOwnerList = carOwnerList;
    }

    public List<SourceBean> getInsuranceTypeList() {
        return insuranceTypeList;
    }

    public void setInsuranceTypeList(List<SourceBean> insuranceTypeList) {
        this.insuranceTypeList = insuranceTypeList;
    }

    public List<TytSource> getInsuranceItemList() {
        return insuranceItemList;
    }

    public void setInsuranceItemList(List<TytSource> insuranceItemList) {
        this.insuranceItemList = insuranceItemList;
    }

    public List<SourceBean> getDealStatusList() {
        return dealStatusList;
    }

    public void setDealStatusList(List<SourceBean> dealStatusList) {
        this.dealStatusList = dealStatusList;
    }
}
