package com.tyt.carinsurance.bean;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @ClassName CarInsuranceQueryBean
 * @Description 车险询价查询对象
 * <AUTHOR>
 * @Date 2019-04-23 11:17
 * @Version 1.0
 */
@Data
public class CarInsuranceQueryBean implements Serializable {


    private static final long serialVersionUID = 832767810330082613L;

    @NotNull(message = "车险询价信息Id不能为空！")
    private Long inquiryId;

    @NotBlank(message = "联系电话不能为空！")
    @Pattern(regexp = "1[3456789]\\d{9}$",message = "联系电话格式有误,请重新输入！")
    private String linkPhone;

    @NotBlank(message = "请输入正确的车辆牌号！")
    private String carHeadCity;

    @NotBlank(message = "请输入正确的车辆牌号！")
    private String carHeadNo;

    @NotNull(message = "车辆所属不能为空！")
    private Integer carOwner;

    private MultipartFile identityCardFront;
    private MultipartFile identityCardBack;
    private MultipartFile businessLicence;
    private MultipartFile carHeadDrivingBook;
    private MultipartFile comInsurancePolicy;

    @NotNull(message = "请选择险种！")
    private Integer insuranceType;
    private String insuranceItemAmt;


}
