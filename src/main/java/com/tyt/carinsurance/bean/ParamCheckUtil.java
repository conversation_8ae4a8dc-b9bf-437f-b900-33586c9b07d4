package com.tyt.carinsurance.bean;

import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Description    接口参数校验工具类
 *               --需要配合实体对象的注解bean validator进行参数校验
 * <AUTHOR>
 * @Date  2019/4/23 15:20
 * @Param
 * @return
 **/
public class ParamCheckUtil implements Serializable {
    
    /**
     * @Description  校验参数返回错误信息
     * <AUTHOR>
     * @Date  2019/4/26 15:43
     * @Param [obj]
     * @return com.tyt.model.ResultMsgBean
     **/
    public static String checkParam(Object obj) {

        ResultMsgBean rm = new ResultMsgBean();
        String errorMsg = "";
        List<String> validate = validate(obj);
        if(validate != null && validate.size() > 0)
        {
            errorMsg = validate.get(0);
        }
        return errorMsg;
    }

    private static ValidatorFactory factory = Validation.buildDefaultValidatorFactory();

    public static <T> List<String> validate(T t) {
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(t);

        List<String> messageList = new ArrayList<>();
        for (ConstraintViolation<T> constraintViolation : constraintViolations) {
            messageList.add(constraintViolation.getMessage());
        }
        return messageList;
    }

    public static void main(String[] args) {
        CarInsuranceCommitInfoBean bean = new CarInsuranceCommitInfoBean();
        bean.setUserId(null);
        bean.setLinkPhone("18810129850");
        bean.setCarHeadCity("京");
        bean.setCarHeadNo("A66666");
        bean.setCarOwner(1);

        String errorMsg = checkParam(bean);
        System.out.println(errorMsg);
    }

}

