package com.tyt.carinsurance.bean;

import com.tyt.util.CarPlateNoUtil;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * @ClassName CarInsuranceCommitInfoBean
 * @Description 车险询价提交信息对象
 * <AUTHOR>
 * @Date 2019-04-23 12:27
 * @Version 1.0
 */
@Data
public class CarInsuranceCommitInfoBean implements Serializable {

    @NotNull(message = "用户Id不能为空！")
    private Long userId;

    @NotBlank(message = "联系电话不能为空！")
    @Pattern(regexp = "1[3456789]\\d{9}$",message = "联系电话格式有误,请重新输入！")
    private String linkPhone;

    @NotBlank(message = "请选择或输入查询的车辆车牌号！")
    private String carHeadCity;

    @NotBlank(message = "请选择或输入查询的车辆车牌号！")
    private String carHeadNo;

    @AssertTrue(message = "车辆车牌号格式有误,请重新输入！")
    private Boolean isRegCarHeadNo(){
        return CarPlateNoUtil.checkPlateNumberFormat(carHeadCity+carHeadNo);
    }

    @NotNull(message = "车辆所属不能为空！")
    private Integer carOwner;
    private MultipartFile carHeadDrivingBook;
}
