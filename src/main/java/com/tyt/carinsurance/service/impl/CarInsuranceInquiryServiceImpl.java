package com.tyt.carinsurance.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.carinsurance.bean.CarInsuranceDetailBean;
import com.tyt.carinsurance.bean.CarInsuranceDictBean;
import com.tyt.carinsurance.enums.DealStatus;
import com.tyt.carinsurance.service.CarInsuranceFileInfoService;
import com.tyt.carinsurance.service.CarInsuranceInquiryService;
import com.tyt.carinsurance.service.CarInsuranceItemInfoService;
import com.tyt.model.*;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.querybean.SourceBean;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytSourceService;
import com.tyt.user.service.UserService;
import com.tyt.util.PictureUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description  车险询价服务层实现类
 * <AUTHOR>
 * @Date  2019/3/12 14:58
 * @Param
 * @return
 **/
@Service("carInsuranceInquiryService")
public class CarInsuranceInquiryServiceImpl extends BaseServiceImpl<CarInsuranceInquiry, Long> implements CarInsuranceInquiryService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytSourceService")
    private TytSourceService tytSourceService;

    @Resource(name = "carService")
    private CarService carService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "carInsuranceItemInfoService")
    private CarInsuranceItemInfoService carInsuranceItemInfoService;

    @Resource(name = "carInsuranceFileInfoService")
    private CarInsuranceFileInfoService carInsuranceFileInfoService;

    @Resource(name = "carInsuranceInquiryDao")
    public void setBaseDao(BaseDao<CarInsuranceInquiry, Long> carInsuranceInquiryDao) {
        super.setBaseDao(carInsuranceInquiryDao);
    }

    //上传文件类型名称，用于在相对路径中创建文件夹，与其他文件区分
    //支持多级文件目录，文件目录格式 x/y/z
    private static final String TYPENAME = "car_insurance/image/";

    //压缩尺寸
    private static final Integer COMPRESS_SIZE = 300;
    /**
     * @Description  获取车险询价字典列表
     * <AUTHOR>
     * @Date  2019/3/12 15:27
     * @Param [userId]
     * @return com.tyt.carinsurance.bean.CarInsuranceDictBean
     **/
    @Override
    public CarInsuranceDictBean getCarInsuranceDictList(Long userId) throws Exception {

        CarInsuranceDictBean carInsuranceDict = new CarInsuranceDictBean();
        //获取用户信息
        User user = userService.getByUserId(userId);
        //查询用户的所有认证成功的车辆
        List<CarInsuranceInquiry> carHeadNoList = new ArrayList<CarInsuranceInquiry>();

        //查询用户进行过车险询价的车辆
        List<CarInsuranceInquiry> inquiryCarList = getAllInquiryCarByUserId(userId);
        int index=0;
        //合并车辆询价的集合
        if(inquiryCarList != null && inquiryCarList.size()>0)
        {
            for (CarInsuranceInquiry carInsuranceInquiry : inquiryCarList) {
                CarInsuranceInquiry inquiryCar = new CarInsuranceInquiry();
                //车头牌照头字母
                inquiryCar.setCarHeadCity(carInsuranceInquiry.getCarHeadCity());
                //车头车牌号
                inquiryCar.setCarHeadNo(carInsuranceInquiry.getCarHeadNo());
                inquiryCar.setTempId(index++);
                carHeadNoList.add(inquiryCar);
            }
        }
        //车辆认证状态 0:认证中；1:认证成功；2：认证失败
//        String auth = "1";
        List<QueryCar> authCarList = carService.getAllCarByUserId(userId);
        if(authCarList != null && authCarList.size()>0)
        {
            for (QueryCar authCar : authCarList) {
                CarInsuranceInquiry inquiryCar = new CarInsuranceInquiry();
                //车头牌照头字母
                inquiryCar.setCarHeadCity(authCar.getHeadCity());
                //车头车牌号
                inquiryCar.setCarHeadNo(authCar.getHeadNo());
                inquiryCar.setTempId(index++);
                carHeadNoList.add(inquiryCar);
            }
        }
        //去除List中的重复元素
        //利用HashSet元素不重复的特性
        if(carHeadNoList != null && carHeadNoList.size() > 0)
        {
            //去重
//            carHeadNoList = new ArrayList(new HashSet(carHeadNoList));
            carHeadNoList = carHeadNoList.stream()
                    .collect(
                            Collectors.collectingAndThen(
                                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                                            o -> o.getCarHeadCity() + ";" + o.getCarHeadNo()))), ArrayList::new)
                    );

            //恢复排序
            CarInsuranceInquiry[] temp = new CarInsuranceInquiry[carHeadNoList.size()];
            carHeadNoList.toArray(temp);
            //去重后恢复排序
            Arrays.sort(temp, new Comparator<CarInsuranceInquiry>() {
                @Override
                public int compare(CarInsuranceInquiry inquiry1, CarInsuranceInquiry inquiry2) {
                    return inquiry1.getTempId() - inquiry2.getTempId();
                }
            });
            carHeadNoList = Arrays.asList(temp);
        }


        //车辆所属字典列表
        List<SourceBean> carOwnerList = tytSourceService.getByGroupCodeWithNoLimit("car_owner");
        //车险险种字典列表
        List<SourceBean> insuranceTypeList = tytSourceService.getByGroupCodeWithNoLimit("insurance_type");
        //商业险保项字典列表
        List<String> groupCode = new ArrayList<String>();
        groupCode.add("insurance_item");
        List<TytSource> insuranceItemList = tytSourceService.getByGroupCode(groupCode);
        //处理结果(跟踪状态)字典列表
        List<SourceBean> dealStatusList = tytSourceService.getByGroupCodeWithNoLimit("deal_status");


        //用户注册手机号
        carInsuranceDict.setLinkPhone(user.getCellPhone());
        //车头车牌号列表
        carInsuranceDict.setCarHeadNoList(carHeadNoList);
        carInsuranceDict.setCarOwnerList(carOwnerList);
        carInsuranceDict.setInsuranceTypeList(insuranceTypeList);
        carInsuranceDict.setInsuranceItemList(insuranceItemList);
        carInsuranceDict.setDealStatusList(dealStatusList);
        logger.info("获取车险询价字典列表成功！");
        return carInsuranceDict;
    }

    /**
     * @Description  根据用户Id查询进行过车险询价过的车辆信息
     * <AUTHOR>
     * @Date  2019/3/13 10:28
     * @Param [userId]
     * @return java.util.List<com.tyt.model.CarInsuranceInquiry>
     **/
    @Override
    public List<CarInsuranceInquiry> getAllInquiryCarByUserId(Long userId) {

        String sql = "select car_head_city as carHeadCity,car_head_no as carHeadNo from car_insurance_inquiry where user_id = :userId  and deal_status > 0 order by ctime desc";

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("userId", userId);

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("carHeadCity", Hibernate.STRING);
        scalarMap.put("carHeadNo", Hibernate.STRING);

        //车险询价车辆列表集合
        List<CarInsuranceInquiry>  inquiryCarList = this.getBaseDao().search(sql, scalarMap, CarInsuranceInquiry.class, params);
        return inquiryCarList;
    }
    
    /**
     * @Description  车险询价进度查询列表
     * <AUTHOR>
     * @Date  2019/3/13 13:54
     * @Param [userId]
     * @return java.util.List<com.tyt.model.CarInsuranceInquiry>
     **/
    @Override
    public List<CarInsuranceInquiry> getCarInsuranceInquiryList(Long userId) {

        String sql = "select id,user_id as userId,user_name as userName,link_phone as linkPhone," +
                        "car_head_city as carHeadCity,car_head_no as carHeadNo," +
                        "car_owner as carOwner,insurance_type as insuranceType," +
                        "ctime as commitTime,deal_time as dealTime,deal_status as dealStatus " +
                        " from car_insurance_inquiry where user_id = :userId and deal_status > 0 order by utime desc";

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("userId", userId);

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("userName", Hibernate.STRING);
        scalarMap.put("linkPhone", Hibernate.STRING);
        scalarMap.put("carHeadCity", Hibernate.STRING);
        scalarMap.put("carHeadNo", Hibernate.STRING);
        scalarMap.put("carOwner", Hibernate.INTEGER);
        scalarMap.put("insuranceType", Hibernate.INTEGER);
        scalarMap.put("commitTime", Hibernate.TIMESTAMP);
        scalarMap.put("dealTime", Hibernate.TIMESTAMP);
        scalarMap.put("dealStatus", Hibernate.INTEGER);
        //车险询价列表信息集合
        List<CarInsuranceInquiry>  inquiryCarList = this.getBaseDao().search(sql, scalarMap, CarInsuranceInquiry.class, params);
        return inquiryCarList;
    }

    /**
     * @Description  提交资料，立即询价接口(第一步)
     * <AUTHOR>
     * @Date  2019/3/13 15:57
     * @Param [linkPhone, userId, carHeadCity, carHeadNo, carOwner, carHeadDrivingBook]
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    @Override
    public Map<String, Object> saveInquiryInfoFirst(Long inquiryId,String linkPhone,Long userId,String carHeadCity,String carHeadNo,
                                                    Integer carOwner, MultipartFile carHeadDrivingBook) throws Exception {
        Map<String, Object> inquiryMap = new HashMap<String,Object>();
        //获取用户信息
        User user = userService.getByUserId(userId);
//        //更新原车险询价信息
//        if(inquiryId != null){
//            CarInsuranceInquiry carInsuranceInquiry = this.getBaseDao().findById(inquiryId);
//            carInsuranceInquiry.setDealStatus(DealStatus.NO_INVALID.getStatus());
//            this.getBaseDao().update(carInsuranceInquiry);
//
//            logger.info("更新车险询价信息成功！车险询价信息id：" + inquiryId);
//            //3.1将原文件信息的状态改为删除
//            carInsuranceFileInfoService.updateStatusByInquiryId(user, inquiryId, 1, 2);
//        }
        //2.保存车险询价信息
        CarInsuranceInquiry carInsuranceInquiry = new CarInsuranceInquiry();
        carInsuranceInquiry.setUserId(userId);
        carInsuranceInquiry.setUserName(user.getTrueName());
        carInsuranceInquiry.setCellPhone(user.getCellPhone());
        carInsuranceInquiry.setLinkPhone(linkPhone);
        carInsuranceInquiry.setCarHeadCity(carHeadCity);
        carInsuranceInquiry.setCarHeadNo(carHeadNo);
        carInsuranceInquiry.setCarOwner(carOwner);
        //处理结果(待处理)
        carInsuranceInquiry.setDealStatus(DealStatus.WAIT_DEAL.getStatus());
        carInsuranceInquiry.setCreateUserId(userId);
        carInsuranceInquiry.setCreateUserName(user.getTrueName());
        carInsuranceInquiry.setCtime(TimeUtil.getTimeStamp());
        this.getBaseDao().insert(carInsuranceInquiry);
        //生成车险询价信息id
        inquiryId = (Long) this.getBaseDao().insertSave(carInsuranceInquiry);
        logger.info("保存车险询价信息成功！车险询价信息id："+inquiryId);


        //2.上传并车头行驶本信息
        //文件类型: 1.车头行驶本(正页)2.车头行驶本(副页)3.车主身份证(正面)4.车主身份证(背面)
        //5.营业执照（车辆归属为企业需必传） 6.上年度商业险保单（非必传）
        int fileType  = 1;
        //文件存储的相对路径
        String typeName = TYPENAME+userId;
        carInsuranceFileInfoService.uploadFile(user, inquiryId, fileType, carHeadDrivingBook,typeName, COMPRESS_SIZE);
        //车险询价信息id
        inquiryMap.put("inquiryId", inquiryId);
        return inquiryMap;
    }

    /**
     * @Description  根据车头车牌号查询车辆信息
     * <AUTHOR>
     * @Date  2019/3/14 11:24
     * @Param [userId, carHeadCity, carHeadNo]
     * @return com.tyt.model.CarInsuranceInquiry
     **/
    @Override
    public CarInsuranceInquiry getInquiryDetailInfoFirst(Long userId, String carHeadCity, String carHeadNo) throws Exception {

        //获取用户信息
        User user = userService.getByUserId(userId);
        CarInsuranceInquiry carInsuranceInquiry = null;
        //根据车头车牌号查询车险询价信息
        carInsuranceInquiry = getInquiryInfoByHeadNo(userId, carHeadCity, carHeadNo);
        if(carInsuranceInquiry != null)
        {
            return carInsuranceInquiry;
        }

        //根据车头车牌号查询车辆认证信息
        List<QueryCar> authCarList = carService.getAuthCarInfoByHeadNo(userId, carHeadCity, carHeadNo);
        if(authCarList != null && authCarList.size()>0)
        {
            QueryCar queryCar = authCarList.get(0);
            carInsuranceInquiry = new CarInsuranceInquiry();
            carInsuranceInquiry.setUserId(user.getId());
            carInsuranceInquiry.setUserName(user.getTrueName());
            carInsuranceInquiry.setCarHeadCity(queryCar.getHeadCity());
            carInsuranceInquiry.setCarHeadNo(queryCar.getHeadNo());
            carInsuranceInquiry.setHeadDrivingUrl(PictureUtil.getPictureUrl(queryCar.getHeadDrivingUrl()));
            //设置用户的联系电话
            carInsuranceInquiry.setLinkPhone(user.getCellPhone());
            return carInsuranceInquiry;
        }
        return carInsuranceInquiry;
    }

    /**
     * @Description  根据车牌号查询车险询价信息
     * <AUTHOR>
     * @Date  2019/3/14 10:00
     * @Param [userId, carHeadCity, carHeadNo]
     * @return com.tyt.model.CarInsuranceInquiry
     **/
    @Override
    public CarInsuranceInquiry getInquiryInfoByHeadNo(Long userId, String carHeadCity, String carHeadNo) {

        String sql = "select a.id as id,a.user_id as userId,a.user_name as userName,a.link_phone as linkPhone," +
                "a.car_head_city as carHeadCity,a.car_head_no as carHeadNo," +
                "a.car_owner as carOwner,a.ctime as commitTime," +
                "a.deal_time as dealTime,a.deal_status as dealStatus " +
                " from car_insurance_inquiry a " +
                " where a.user_id =:userId and a.car_head_city =:carHeadCity  and a.car_head_no=:carHeadNo  " +
                " and deal_status > 0 " +
                " order by a.ctime desc";

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("userId", userId);
        params.put("carHeadCity", carHeadCity);
        params.put("carHeadNo", carHeadNo);

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("userName", Hibernate.STRING);
        scalarMap.put("linkPhone", Hibernate.STRING);
        scalarMap.put("carHeadCity", Hibernate.STRING);
        scalarMap.put("carHeadNo", Hibernate.STRING);
        scalarMap.put("carOwner", Hibernate.INTEGER);
        scalarMap.put("commitTime", Hibernate.TIMESTAMP);
        scalarMap.put("dealTime", Hibernate.TIMESTAMP);
        scalarMap.put("dealStatus", Hibernate.INTEGER);

        //车险询价列表信息集合
        List<CarInsuranceInquiry>  inquiryCarList = this.getBaseDao().search(sql, scalarMap, CarInsuranceInquiry.class, params);
        //车险询价信息对象
        CarInsuranceInquiry carInsuranceInquiry = null;
        if(inquiryCarList != null && inquiryCarList.size() > 0)
        {
            carInsuranceInquiry = inquiryCarList.get(0);

            Long inquiryId = carInsuranceInquiry.getId();
            //文件类型: 1.车头行驶本(正页)2.车头行驶本(副页)3.车主身份证(正面)4.车主身份证(背面)
            //5.营业执照（车辆归属为企业需必传） 6.上年度商业险保单（非必传）
            int fileType  = 1;
            List<CarInsuranceFileInfo> fileInfoList = carInsuranceFileInfoService.getFileInfoList(inquiryId, fileType);
            if(fileInfoList != null && fileInfoList.size() > 0)
            {
                CarInsuranceFileInfo fileInfo = fileInfoList.get(0);
                carInsuranceInquiry.setHeadDrivingUrl(fileInfo.getFilePath());
            }
        }
        return carInsuranceInquiry;

    }
    /**
     * @Description  提交资料，立即询价接口(第二步)
     * <AUTHOR>
     * @Date  2019/3/14 17:26
     * @Param [userId, inquiryId, linkPhone, carHeadCity, carHeadNo, carOwner, identityCardFront, identityCardBack, businessLicence, carHeadDrivingBook, comInsurancePolicy, insuranceType, insuranceItemAmt]
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    @Override
    public Map<String, Object> saveInquiryInfoSecond(Long userId, Long inquiryId, String linkPhone,
                                                     String carHeadCity, String carHeadNo, Integer carOwner,
                                                     MultipartFile identityCardFront,
                                                     MultipartFile identityCardBack,
                                                     MultipartFile businessLicence,
                                                     MultipartFile carHeadDrivingBook,
                                                     MultipartFile comInsurancePolicy,
                                                     Integer insuranceType,
                                                     String insuranceItemAmt) throws Exception {
        Map<String, Object> inquiryMap = new HashMap<String,Object>();
        //获取用户信息
        User user = userService.getByUserId(userId);
        //1.补充车险询价信息
        CarInsuranceInquiry carInsuranceInquiry = this.getBaseDao().findById(inquiryId);
        carInsuranceInquiry.setId(inquiryId);
        carInsuranceInquiry.setLinkPhone(linkPhone);
        carInsuranceInquiry.setCarHeadCity(carHeadCity);
        carInsuranceInquiry.setCarHeadNo(carHeadNo);
        carInsuranceInquiry.setCarOwner(carOwner);
        carInsuranceInquiry.setInsuranceType(insuranceType);
        //处理结果(处理中)
        carInsuranceInquiry.setDealStatus(DealStatus.IN_DEALING.getStatus());
        carInsuranceInquiry.setUpdateUserId(user.getId());
        carInsuranceInquiry.setUpdateUserName(user.getTrueName());
        carInsuranceInquiry.setUtime(TimeUtil.getTimeStamp());
        this.getBaseDao().update(carInsuranceInquiry);
        logger.info("补充车险询价信息成功！车险询价信息id："+inquiryId);

        //2.补充险种、保项和保额信息
        //2.1将原先的保额保项信息置为无效
        Map<String,Object>  itemMap = new HashMap<String,Object>();
        String updateItemSql = "update car_insurance_item_info set status = 2 where inquiry_id =:inquiryId and user_id =:userId and status = 1";
        itemMap.put("inquiryId",inquiryId);
        itemMap.put("userId",userId);
        carInsuranceItemInfoService.executeUpdateSql(updateItemSql, itemMap);

        if(insuranceType != null && (insuranceType.intValue() == 1 || insuranceType.intValue() ==3))
        {
            //商业险险种、保额信息
            if(StringUtils.isNotBlank(insuranceItemAmt))
            {
                String[] insuranceItem = insuranceItemAmt.split("#");
                for (String itemAmt : insuranceItem) {
                    String[] item = itemAmt.split("\\|");
                    //车险询价保项保额对象
                    CarInsuranceItemInfo carInsuranceItemInfo = new CarInsuranceItemInfo();
                    carInsuranceItemInfo.setInquiryId(inquiryId);
                    carInsuranceItemInfo.setUserId(userId);
                    //车险险种 1.商业险 -- 目前只有商业险有保项
                    carInsuranceItemInfo.setInsuranceType(1);
                    carInsuranceItemInfo.setInsuranceItem(Integer.parseInt(item[0]));
                    carInsuranceItemInfo.setAmtCurrency(Integer.parseInt(item[1]));
                    //保项状态: 1-正常,2-删除
                    carInsuranceItemInfo.setStatus(1);
                    carInsuranceItemInfo.setCtime(TimeUtil.getTimeStamp());
                    carInsuranceItemInfo.setUtime(TimeUtil.getTimeStamp());
                    carInsuranceItemInfoService.add(carInsuranceItemInfo);
                }
            }
        }

        //3.上传补充车险询价的证件文件
        //文件类型: 1.车头行驶本(正页)2.车头行驶本(副页)3.车主身份证(正面)4.车主身份证(背面--非必传)
        //        5.营业执照（车辆归属为企业需必传） 6.上年度商业险保单（非必传）
        //文件存储的相对路径
        String typeName = TYPENAME+userId;
        if(carHeadDrivingBook != null)
        {
            //3.1将原文件信息的状态改为删除
            carInsuranceFileInfoService.updateStatusByInquiryId(user, inquiryId, 1, 2);
            //3.2上传新文件并补充文件信息
            carInsuranceFileInfoService.uploadFile(user, inquiryId, 1, carHeadDrivingBook,typeName, COMPRESS_SIZE);
        }
        if(identityCardFront != null)
        {
            //3.1将原文件信息的状态改为删除
            carInsuranceFileInfoService.updateStatusByInquiryId(user, inquiryId, 3, 2);
            //3.2上传新文件并补充文件信息
            carInsuranceFileInfoService.uploadFile(user, inquiryId, 3, identityCardFront,typeName, COMPRESS_SIZE);
        }
        if(identityCardBack != null)
        {
            //3.1将原文件信息的状态改为删除
            carInsuranceFileInfoService.updateStatusByInquiryId(user, inquiryId, 4, 2);
            //3.2上传新文件并补充文件信息
            carInsuranceFileInfoService.uploadFile(user, inquiryId, 4, identityCardBack,typeName, COMPRESS_SIZE);
        }
        if(businessLicence != null)
        {
            //3.1将原文件信息的状态改为删除
            carInsuranceFileInfoService.updateStatusByInquiryId(user, inquiryId, 5, 2);
            //3.2上传新文件并补充文件信息
            carInsuranceFileInfoService.uploadFile(user, inquiryId, 5, businessLicence,typeName, COMPRESS_SIZE);
        }
        if(comInsurancePolicy != null)
        {
            //3.1将原文件信息的状态改为删除
            carInsuranceFileInfoService.updateStatusByInquiryId(user, inquiryId, 6, 2);
            //3.2上传新文件并补充文件信息
            carInsuranceFileInfoService.uploadFile(user, inquiryId, 6, comInsurancePolicy,typeName, COMPRESS_SIZE);
        }
        //车险询价信息id
        inquiryMap.put("inquiryId", inquiryId);
        return inquiryMap;
    }

    /**
     * @Description  根据业务id(询价信息id)获取车险询价详情信息
     * <AUTHOR>
     * @Date  2019/3/15 12:34
     * @Param [userId, inquiryId]
     * @return com.tyt.carinsurance.bean.CarInsuranceDetailBean
     **/
    @Override
    public CarInsuranceDetailBean getInquiryDetailInfoSecond(Long userId, Long inquiryId) throws InvocationTargetException, IllegalAccessException {

        //车险询价详情对象
        CarInsuranceDetailBean carInsuranceDetailBean = new CarInsuranceDetailBean();

        //1.车险询价信息
        CarInsuranceInquiry carInsuranceInquiry = this.getBaseDao().findById(inquiryId);

        //2.商业险险种保项列表
        CarInsuranceItemInfo itemInfo = new CarInsuranceItemInfo();
        itemInfo.setInquiryId(inquiryId);
        //保项状态: 1-正常,2-删除
        itemInfo.setStatus(1);
        List<CarInsuranceItemInfo> insuranceItemList = carInsuranceItemInfoService.findList(itemInfo);

        //3.车险询价证件信息列表
        List<CarInsuranceFileInfo> insuranceFileList = carInsuranceFileInfoService.getFileInfoListByInquiryId(inquiryId);

        //将车险询价信息对象赋值给车险询价详情对象
        BeanUtils.copyProperties(carInsuranceInquiry, carInsuranceDetailBean);
        carInsuranceDetailBean.setInsuranceItemList(insuranceItemList);
        carInsuranceDetailBean.setInsuranceFileList(insuranceFileList);
        return carInsuranceDetailBean;
    }
}
