package com.tyt.carinsurance.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.carinsurance.service.CarInsuranceItemInfoService;
import com.tyt.model.CarInsuranceItemInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description  车险询价保项保额服务层实现类
 * <AUTHOR>
 * @Date  2019/3/12 14:58
 * @Param
 * @return
 **/
@Service("carInsuranceItemInfoService")
public class CarInsuranceItemInfoServiceImpl extends BaseServiceImpl<CarInsuranceItemInfo, Long> implements CarInsuranceItemInfoService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());


    @Resource(name = "carInsuranceItemInfoDao")
    public void setBaseDao(BaseDao<CarInsuranceItemInfo, Long> carInsuranceItemInfoDao) {
        super.setBaseDao(carInsuranceItemInfoDao);
    }
}
