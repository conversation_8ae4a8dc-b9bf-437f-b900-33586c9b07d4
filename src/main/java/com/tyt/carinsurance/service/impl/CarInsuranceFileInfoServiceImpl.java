package com.tyt.carinsurance.service.impl;

import com.tyt.carinsurance.service.CarInsuranceFileInfoService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.CarInsuranceFileInfo;
import com.tyt.model.User;
import com.tyt.mybatis.mapper.CarInsuranceFileInfoMapper;
import com.tyt.tsinsurance.service.impl.TsInsuranceServiceImpl;
import com.tyt.util.CreateFileUtil;
import com.tyt.util.TimeUtil;
import net.coobird.thumbnailator.Thumbnails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName CarInsuranceCarInsuranceFileInfoServiceImpl
 * @Description 文件信息服务层接口实现类
 * <AUTHOR>
 * @Date 2019-01-17 14:58
 * @Version 1.0
 */
@Service("carInsuranceFileInfoService")
public class CarInsuranceFileInfoServiceImpl implements CarInsuranceFileInfoService {

    public static Logger logger = LoggerFactory.getLogger(CarInsuranceFileInfoServiceImpl.class);
    //文件的根路径
    private final String FILE_PATH_DOMAIN = AppConfig.getProperty("picture.path.domain");
    //文件相对路径
    private final String FILE_PATH_REAL = "/data/file/";
    //文件路径分割符
    private final String FILE_PATH_SEPARATION = "/";


    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private CarInsuranceFileInfoMapper carInsuranceFileInfoMapper;
    /**
     * @Description  上传文件实现方法
     * <AUTHOR>
     * @Date  2019/1/17 15:24
     * @Param [curUser, fileType, uploadFile]
     * @return java.util.Map
     **/
    @Override
    public Map uploadFile(User user, Long inquiryId, Integer fileType,
                          MultipartFile uploadFile, String typeName, Integer compressSize) throws Exception{

        logger.info("===========uploadFile:================"+uploadFile);
        //1.保存原图片文件
        //文件名称
        String originalFilename = uploadFile.getOriginalFilename();
        logger.info("===========originalFilename:================"+originalFilename);
        //文件大小
        Long fileSize = uploadFile.getSize();
        //文件后缀
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        //重命名后的文件相对路径
        String fileRealUrl = renameFileBase(uploadFile, typeName);
        //重命名后的文件名称
        String fileName = fileRealUrl.substring(fileRealUrl.lastIndexOf(FILE_PATH_SEPARATION)+1);
        //重命名后的文件全路径
        String fileAllPath = FILE_PATH_DOMAIN + fileRealUrl;
        //生成本地文件
        uploadFile.transferTo(new File(fileAllPath));
        //2.生成缩略图,保存缩略图文件
        String prefix = fileRealUrl.substring(0,fileRealUrl.lastIndexOf("."));
        //缩略图文件相对路径
        String smallFileUrl = prefix + "_" + compressSize + suffix;
        //缩略图文件名称
        String smallFileName = smallFileUrl.substring(smallFileUrl.lastIndexOf(FILE_PATH_SEPARATION)+1);
        Thumbnails.of(fileAllPath)
//				.scale(0.1f)  //按比例压缩
//				.sourceRegion(Positions.CENTER, 300,300)  //截取中心点300范围
                .size(compressSize, compressSize)   //按照尺寸比例压缩
                .toFile(FILE_PATH_DOMAIN + smallFileUrl);
        //3.将文件信息保存到数据库中
        CarInsuranceFileInfo fileInfo = new CarInsuranceFileInfo();
        //业务id(询价信息id)
        fileInfo.setInquiryId(inquiryId);
        //文件类型:1.异常上报凭证图片,
        fileInfo.setFileType(fileType);
        //文件原名称
        fileInfo.setOriginalFileName(originalFilename);
        //文件名称
        fileInfo.setFileName(fileName);
        //文件相对路径
        fileInfo.setFilePath(fileRealUrl);
        //文件大小
        fileInfo.setFileSize(fileSize);
        //文件后缀名称
        fileInfo.setFileSuffix(suffix);
        //缩略图文件名称
        fileInfo.setSmallFileName(smallFileName);
        //缩略图文件路径
        fileInfo.setSmallFilePath(smallFileUrl);
        fileInfo.setCreateUserId(user.getId());
        fileInfo.setCreateUserName(user.getTrueName());
        fileInfo.setCtime(TimeUtil.getTimeStamp());
        fileInfo.setUpdateUserId(user.getId());
        fileInfo.setUpdateUserName(user.getTrueName());
        fileInfo.setUtime(TimeUtil.getTimeStamp());
        //文件状态: 1-正常,2-删除
        fileInfo.setStatus(1);
        carInsuranceFileInfoMapper.insertFileInfo(fileInfo);
        //4.将文件信息返回
        Map<String,Object> fileInfoMap = new HashMap<String,Object>();
        fileInfoMap.put("fileInfo",fileInfo);
        fileInfoMap.put("fileRealUrl",fileRealUrl);
        fileInfoMap.put("smallFileUrl",smallFileUrl);
        return fileInfoMap;
    }
    
    /**
     * @Description 获取文件列表实现方法
     * <AUTHOR>
     * @Date  2019/1/18 11:37
     * @Param [businessId, fileType]
     * @return java.util.List<com.tyt.file.bean.FileInfo>
     **/
    @Override
    public List<CarInsuranceFileInfo> getFileInfoList(Long inquiryId, Integer fileType) {

        List<CarInsuranceFileInfo> fileList = carInsuranceFileInfoMapper.getFileInfoList(inquiryId, fileType);
        return fileList;
    }

    /**
     * @Description  根据询价信息Id获取文件列表方法
     * <AUTHOR>
     * @Date  2019/3/15 14:16
     * @Param [inquiryId]
     * @return java.util.List<com.tyt.model.CarInsuranceFileInfo>
     **/
    @Override
    public List<CarInsuranceFileInfo> getFileInfoListByInquiryId(Long inquiryId) {
        List<CarInsuranceFileInfo> fileList = carInsuranceFileInfoMapper.getFileInfoListByInquiryId(inquiryId);
        return fileList;
    }

    /**
     * @Description  获取单个文件信息的实现方法
     * <AUTHOR>
     * @Date  2019/1/18 12:30
     * @Param [id]
     * @return com.tyt.file.bean.FileInfo
     **/
    @Override
    public CarInsuranceFileInfo getSingleFileInfo(Long id) {

        CarInsuranceFileInfo singleFileInfo = carInsuranceFileInfoMapper.getSingleFileInfo(id);
        return singleFileInfo;
    }

    /**
     * @Description  更新文件信息状态的实现方法
     * <AUTHOR>
     * @Date  2019/1/18 12:30
     * @Param [curUser, id, status]
     * @return int
     **/
    @Override
    public int updateFileInfoStatus(User user, Long id, Integer status) {
        //文件信息对象
        CarInsuranceFileInfo fileInfo = new CarInsuranceFileInfo();
        //文件Id
        fileInfo.setId(id);
        fileInfo.setUpdateUserId(user.getId());
        fileInfo.setUpdateUserName(user.getTrueName());
        fileInfo.setUtime(TimeUtil.getTimeStamp());
        //文件状态
        fileInfo.setStatus(status);
        //更新结果
        int result = carInsuranceFileInfoMapper.updateFileInfo(fileInfo);
        return result;
    }
     
    /**
     * @Description  根据业务Id更新文件状态的方法
     * <AUTHOR>
     * @Date  2019/3/18 10:41 
     * @Param [user, inquiryId, fileType, status]
     * @return int
     **/
    @Override
    public int updateStatusByInquiryId(User user, Long inquiryId, Integer fileType, Integer status) {
        //文件信息对象
        CarInsuranceFileInfo fileInfo = new CarInsuranceFileInfo();
        //文件Id
        fileInfo.setInquiryId(inquiryId);
        fileInfo.setFileType(fileType);
        fileInfo.setUpdateUserId(user.getId());
        fileInfo.setUpdateUserName(user.getTrueName());
        fileInfo.setUtime(TimeUtil.getTimeStamp());
        //文件状态
        fileInfo.setStatus(status);
        //更新结果
        int result = carInsuranceFileInfoMapper.updateStatusByInquiryId(fileInfo);
        return result;
    }

    /**
     * @Description  创建上传文件保存目录，返回重命名后的文件路径
     * <AUTHOR>
     * @Date  2018/12/13 16:25
     * @Param [multipartFile, typeName]
     * @return java.lang.String
     **/
    public String renameFileBase(MultipartFile multipartFile, String typeName) {
        //文件相对路径文件
        String domainurl = FILE_PATH_REAL + typeName + FILE_PATH_SEPARATION;
        //创建文件相对路径文件夹
        CreateFileUtil.createDir(FILE_PATH_DOMAIN + domainurl);
        //文件相对全路径，包含重命名后的文件名称
        String realUrl = domainurl + renameFile(multipartFile.getOriginalFilename());
        return realUrl;
    }
    /**
     * @Description  对上传的文件重命名
     * <AUTHOR>
     * @Date  2018/12/13 16:18
     * @Param [fileName]
     * @return java.lang.String
     **/
    public static String renameFile(String fileName) {
        DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String formatDate = format.format(new Date());
        int random = new Random().nextInt(10000);
        int position = fileName.lastIndexOf(".");
        String extension = fileName.substring(position);
        return formatDate + random + extension;
    }
}
