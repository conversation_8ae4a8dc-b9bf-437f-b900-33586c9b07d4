package com.tyt.carinsurance.service;

import com.tyt.base.service.BaseService;
import com.tyt.carinsurance.bean.CarInsuranceDictBean;
import com.tyt.model.CarInsuranceInquiry;
import com.tyt.model.CarInsuranceItemInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @Description  车险询价保项保额服务层
 * <AUTHOR>
 * @Date  2019/3/12 14:58
 * @Param
 * @return
 **/
public interface CarInsuranceItemInfoService extends BaseService<CarInsuranceItemInfo, Long> {

}
