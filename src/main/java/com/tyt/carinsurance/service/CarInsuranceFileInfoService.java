package com.tyt.carinsurance.service;

import com.tyt.model.CarInsuranceFileInfo;
import com.tyt.model.User;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @ClassName CarInsuranceFileInfoService
 * @Description 文件信息服务层接口
 * <AUTHOR>
 * @Date 2019-01-17 14:58
 * @Version 1.0
 */
public interface CarInsuranceFileInfoService {

    /**
     * @Description  上传文件接口方法
     * <AUTHOR>
     * @Date  2019/1/17 15:23
     * @Param [curUser, fileType, uploadFile]
     * @return java.util.Map
     **/
    public Map uploadFile(User user, Long inquiryId, Integer fileType,
                          MultipartFile uploadFile, String typeName, Integer compressSize) throws Exception;

    /**
     * @Description  获取文件列表方法
     * <AUTHOR>
     * @Date  2019/1/18 11:36
     * @Param [businessId, fileType]
     * @return java.util.List<com.tyt.file.bean.FileInfo>
     **/
    public List<CarInsuranceFileInfo> getFileInfoList(Long inquiryId, Integer fileType);

    /**
     * @Description  根据询价信息Id获取文件列表方法
     * <AUTHOR>
     * @Date  2019/3/15 14:08
     * @Param [inquiryId, fileTypes]
     * @return java.util.List<com.tyt.model.CarInsuranceFileInfo>
     **/
    public List<CarInsuranceFileInfo> getFileInfoListByInquiryId(Long inquiryId);

    /**
     * @Description  获取单个文件信息的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:23
     * @Param [id]
     * @return com.tyt.file.bean.FileInfo
     **/
    public CarInsuranceFileInfo getSingleFileInfo(Long id);

    /**
     * @Description  更新文件信息状态的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:28
     * @Param [curUser, id, status]
     * @return int
     **/
    public int updateFileInfoStatus(User user, Long id, Integer status);
 
    /**
     * @Description  根据业务Id更新文件信息状态
     * <AUTHOR>
     * @Date  2019/3/18 10:39
     * @Param [user, inquiryId, fileType, status]
     * @return int
     **/
    public int updateStatusByInquiryId(User user, Long inquiryId,Integer fileType, Integer status);
}
