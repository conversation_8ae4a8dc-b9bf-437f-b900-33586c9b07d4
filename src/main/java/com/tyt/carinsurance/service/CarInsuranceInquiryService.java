package com.tyt.carinsurance.service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.service.BaseService;
import com.tyt.carinsurance.bean.CarInsuranceDetailBean;
import com.tyt.carinsurance.bean.CarInsuranceDictBean;
import com.tyt.model.CarInsuranceInquiry;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * @Description  车险询价服务层
 * <AUTHOR>
 * @Date  2019/3/12 14:58
 * @Param
 * @return
 **/
public interface CarInsuranceInquiryService extends BaseService<CarInsuranceInquiry, Long> {
    
    /**
     * @Description  获取车险询价字典列表
     * <AUTHOR>
     * @Date  2019/3/12 15:23
     * @Param []
     * @return com.tyt.carinsurance.bean.CarInsuranceDictBean
     **/
    public CarInsuranceDictBean getCarInsuranceDictList(Long userId) throws Exception;

    /**
     * @Description  根据用户Id查询进行过车险询价过的车辆信息
     * <AUTHOR>
     * @Date  2019/3/13 13:55
     * @Param [userId]
     * @return java.util.List<com.tyt.model.CarInsuranceInquiry>
     **/
    public List<CarInsuranceInquiry> getAllInquiryCarByUserId(Long userId);
    
    /**
     * @Description  车险询价进度查询列表
     * <AUTHOR>
     * @Date  2019/3/13 13:55
     * @Param [userId]
     * @return java.util.List<com.tyt.model.CarInsuranceInquiry>
     **/
    public List<CarInsuranceInquiry> getCarInsuranceInquiryList(Long userId);
     
    /**
     * @Description  提交资料，立即询价接口(第一步)
     * <AUTHOR>
     * @Date  2019/3/13 15:55
     * @Param [linkPhone, userId, carHeadCity, carHeadNo, carOwner, carHeadDrivingBook]
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    public Map<String,Object> saveInquiryInfoFirst(Long inquiryId,String linkPhone,Long userId,String carHeadCity,String carHeadNo,
                                                   Integer carOwner,MultipartFile carHeadDrivingBook) throws Exception;
 
    /**
     * @Description  根据车头车牌号查询车辆信息
     * <AUTHOR>
     * @Date  2019/3/14 11:23
     * @Param [userId, carHeadCity, carHeadNo]
     * @return com.tyt.model.CarInsuranceInquiry
     **/
    public CarInsuranceInquiry getInquiryDetailInfoFirst(Long userId,String carHeadCity,String carHeadNo) throws Exception;
    /**
     * @Description  根据车头车牌号查询车险询价信息
     * <AUTHOR>
     * @Date  2019/3/14 9:55
     * @Param [userId, carHeadCity, carHeadNo]
     * @return com.tyt.model.CarInsuranceInquiry
     **/
    public CarInsuranceInquiry getInquiryInfoByHeadNo(Long userId,String carHeadCity,String carHeadNo);


    /**
     * @Description  提交资料，立即询价接口(第二步)
     * <AUTHOR>
     * @Date  2019/3/14 17:25
     * @Param [userId, inquiryId, linkPhone, carHeadCity, carHeadNo, carOwner, identityCardFront, identityCardBack, businessLicence, carHeadDrivingBook, comInsurancePolicy, insuranceType, insuranceItemAmt]
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    public Map<String,Object> saveInquiryInfoSecond(Long userId,Long inquiryId,String linkPhone,
                                                    String carHeadCity,String carHeadNo,Integer carOwner,
                                                    MultipartFile identityCardFront,
                                                    MultipartFile identityCardBack,
                                                    MultipartFile businessLicence,
                                                    MultipartFile carHeadDrivingBook,
                                                    MultipartFile comInsurancePolicy,
                                                    Integer insuranceType,String insuranceItemAmt) throws Exception;
    /**
     * @Description  根据业务id(询价信息id)获取车险询价详情信息
     * <AUTHOR>
     * @Date  2019/3/15 12:33
     * @Param [userId, inquiryId]
     * @return com.tyt.carinsurance.bean.CarInsuranceDetailBean
     **/
    public CarInsuranceDetailBean getInquiryDetailInfoSecond(Long userId,Long inquiryId) throws InvocationTargetException, IllegalAccessException;
}
