package com.tyt.activityCollate.controller;

import com.tyt.activityCollate.bean.ActivityCollateDetailBean;
import com.tyt.activityCollate.bean.ActivityCollateShareBean;
import com.tyt.activityCollate.service.ActivityCollateService;
import com.tyt.goods.service.GoodsService;
import com.tyt.infofee.bean.WechatShareTransportBean;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.model.MarketingActivity;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytGoods;
import com.tyt.util.ReturnCodeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/plat/activity/collate")
public class ActivityCollateController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "activityCollateService")
    private ActivityCollateService activityCollateService;
    @Resource(name = "goodsService")
    private GoodsService goodsService;
    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;

    /**
     * 获取拼单详情
     * @param leadUserId
     * @return
     */
    @RequestMapping({"/getDetail","/getDetail.action"})
    @ResponseBody
    public ResultMsgBean getDetail(@RequestParam(value = "leadUserId") Long leadUserId,
                                   @RequestParam(value = "activityId") Long activityId){
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK,"查询成功");
        try {
            ActivityCollateDetailBean detailBean = activityCollateService.getDetailByLeadUserId(leadUserId,activityId);
            if (detailBean == null){
                rm.setCode(500);
                rm.setMsg("该团不存在");
                return rm;
            }
            TytGoods goods = goodsService.getById(detailBean.getGoodsId());
            if (goods!=null){
                detailBean.setOriginalPrice(goods.getOriginalPrice());
                detailBean.setPrice(goods.getDiscountPrice());
                String goodsName= goods.getEffectiveTime()+"年"+goods.getGoodsExplain()+"会员";
                detailBean.setGoodsName(goodsName);
            }
            MarketingActivity activity = marketingActivityService.getById(detailBean.getActivityId());
            if (activity!=null){
                detailBean.setActivityStartTime(activity.getStartTime());
                detailBean.setActivityEndTime(activity.getEndTime());
            }
            rm.setData(detailBean);
            return rm;
        }catch (Exception e){
            logger.error("拼团活动查询失败，失败原因:"+e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
    }

    /**
     * 分享到微信
     * @param userId
     * @return
     */
    @RequestMapping({"/wechatShare","/wechatShare.action"})
    @ResponseBody
    public ResultMsgBean wechatShare(@RequestParam(value = "userId") Long userId,
                                     @RequestParam(value = "activityId") Long activityId){
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK,"查询成功");
        try {
            ActivityCollateDetailBean detailBean = activityCollateService.getDetailByLeadUserId(userId,activityId);
            if (detailBean == null){
                rm.setCode(500);
                rm.setMsg("该团不存在");
                return rm;
            }
            ActivityCollateShareBean share=new ActivityCollateShareBean();
            share.setIconUrl("http://www.teyuntong.com/app/image_logo/logo120.png");
            share.setShareTitle("会员拼团购，2人成团");
            share.setShareContent("省钱TA不香吗？火速拼团享受优惠吧~");
            share.setShareUrl(detailBean.getLink());
            rm.setData(share);
            return rm;
        }catch (Exception e){
            logger.error("拼团活动分享，失败原因:"+e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
    }

    @RequestMapping({"/checkStatus","/checkStatus.action"})
    @ResponseBody
    public ResultMsgBean checkStatus(@RequestParam(value = "userId") Long userId, @RequestParam(value = "activityId")Long activityId){
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK,"查询成功");
        try {
            int status = 0; //跳转购买页
            ActivityCollateDetailBean detailBean = activityCollateService.getDetailByLeadUserId(userId,activityId);
            if (detailBean != null){
                status = 1;//跳转拼团详情
                rm.setData(status);
                return rm;
            }
            MarketingActivity activity = marketingActivityService.getById(activityId);
            long now = System.currentTimeMillis();
            if (activity.getEndTime().getTime()<now){
                status = 2; //跳转活动已结束页面
            }
            rm.setData(status);
            return rm;
        }catch (Exception e){
            logger.error("ActivityCollateController  checkStatus，失败原因:"+e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
    }



}
