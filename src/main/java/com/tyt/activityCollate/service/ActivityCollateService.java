package com.tyt.activityCollate.service;

import com.tyt.activityCollate.bean.ActivityCollateDetailBean;
import com.tyt.base.service.BaseService;
import com.tyt.model.TytActivityCollate;

public interface ActivityCollateService extends BaseService<TytActivityCollate, Long> {
    /**
     * 获取拼团活动详情
     * @param leadUserId  拼主id
     * @return
     */
    ActivityCollateDetailBean getDetailByLeadUserId(Long leadUserId,Long activityId);
}
