package com.tyt.activityCollate.service;

import com.tyt.activityCollate.bean.ActivityCollateDetailBean;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytActivityCollate;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("activityCollateService")
public class ActivityCollateServiceImpl extends BaseServiceImpl<TytActivityCollate, Long> implements ActivityCollateService {

    @Resource(name = "activityCollateDao")
    @Override
    public void setBaseDao(BaseDao<TytActivityCollate, Long> dao) {
        super.setBaseDao(dao);
    }

    @Override
    public ActivityCollateDetailBean getDetailByLeadUserId(Long leadUserId,Long activityId) {
        String sql = "SELECT id, activity_id activityId,`lead_user_id` leadUserId,`lead_head_url` leadHeadUrl,`member_user_id` memberUserId, `member_head_url` memberHeadUrl,`goods_id` goodsId,`status` status,`success_time` successTime,`create_time` createTime,`update_time` updateTime, link FROM `tyt_activity_collate` WHERE `lead_user_id` = ? and orders_status=2 and activity_id=? ORDER BY id DESC LIMIT 1";
        //传入的参数
        Object[] params = new Object[] {leadUserId,activityId};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("activityId", Hibernate.LONG);
        scalarMap.put("leadUserId", Hibernate.LONG);
        scalarMap.put("leadHeadUrl", Hibernate.STRING);
        scalarMap.put("memberUserId", Hibernate.LONG);
        scalarMap.put("memberHeadUrl", Hibernate.STRING);
        scalarMap.put("goodsId", Hibernate.LONG);
        scalarMap.put("status", Hibernate.INTEGER);
        scalarMap.put("successTime", Hibernate.TIMESTAMP);
        scalarMap.put("createTime", Hibernate.TIMESTAMP);
        scalarMap.put("updateTime", Hibernate.TIMESTAMP);
        scalarMap.put("link", Hibernate.STRING);
        List<ActivityCollateDetailBean> beanList =  this.getBaseDao().search(sql, scalarMap, ActivityCollateDetailBean.class, params);
        if (CollectionUtils.isNotEmpty(beanList)){
            return beanList.get(0);
        }
        return null;
    }
}
