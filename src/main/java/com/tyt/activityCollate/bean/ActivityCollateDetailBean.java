package com.tyt.activityCollate.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ActivityCollateDetailBean {
    private Long id;
    private Long activityId;
    private Long leadUserId;
    private String leadHeadUrl;
    private Long memberUserId;
    private String memberHeadUrl;
    private Integer status;
    private Date successTime;
    private Date createTime;
    private Date updateTime;
    private Long goodsId;
    private String goodsName;
    private BigDecimal originalPrice;
    private BigDecimal price;
    private Date activityStartTime;
    private Date activityEndTime;
    private String link;
}
