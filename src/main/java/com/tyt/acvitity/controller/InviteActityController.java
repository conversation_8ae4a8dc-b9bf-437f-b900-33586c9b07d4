package com.tyt.acvitity.controller;

import com.tyt.acvitity.service.PlatUserInviteAwardInfoService;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.user.service.*;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/plat/invite")
public class InviteActityController extends BaseController{

	@Resource(name = "platUserInviteAwardInfoService")
	private PlatUserInviteAwardInfoService platUserInviteAwardInfoService;

	/**
	 * 查询当前用户的信息和累计奖励天数
	 * @param userId  查询ID
	 * @return
	 */
	@RequestMapping(value = "/getUserInfoAndAward.action")
	@ResponseBody
	public ResultMsgBean getUserInfoAndAward(@RequestParam(value="userId",required=true) Long userId){
		ResultMsgBean result=new ResultMsgBean();
		try {
			result.setCode(200);
			result.setMsg("查询成功");
			platUserInviteAwardInfoService.getInviteAwardUserInfo(userId,result);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("服务器错误");
		}
		return result;
	}

	/**
	 * 查询当前用户的邀请历史
	 * @param userId  查询ID
	 * @return
	 */
	@RequestMapping(value = "/getAwardHistory.action")
	@ResponseBody
	public ResultMsgBean getAwardHistory(@RequestParam(value="userId",required=true) Long userId){
		ResultMsgBean result=new ResultMsgBean();
		try {
			result.setCode(200);
			result.setMsg("查询成功");
			platUserInviteAwardInfoService.getInviteAwardStatisticsInfo(userId,result);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("服务器错误");
		}
		return result;
	}

}
