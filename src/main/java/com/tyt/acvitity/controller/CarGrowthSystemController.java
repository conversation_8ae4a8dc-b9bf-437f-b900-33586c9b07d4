package com.tyt.acvitity.controller;

import com.tyt.acvitity.bean.CarActivityDetail;
import com.tyt.acvitity.bean.CarActivityReq;
import com.tyt.acvitity.bean.ParticipateActivityReq;
import com.tyt.acvitity.service.CarGrowthSystemService;
import com.tyt.acvitity.service.LotteryRecordService;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.ResultMsgBean;
import com.tyt.acvitity.bean.GrowthInfoVo;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.LockUtil;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 车方成长体系抽奖
 */
@RestController
@RequestMapping(value = "/car/growth/system")
public class CarGrowthSystemController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String LOCK_KEY_PREFIX = "ACTIVITY_LOTTERY";

    @Autowired
    private CarGrowthSystemService carGrowthSystemService;
    @Autowired
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Resource
    private LotteryRecordService lotteryRecordService;

    @Autowired
    private TytConfigService tytConfigService;

    /**
     * 车方信用体系-获取用户抽奖信息
     *
     * @return
     */
    @RequestMapping(value = {"/getUserLuckyDrawInfo", "/getUserLuckyDrawInfo.action"})
    public ResultMsgBean getUserLuckyDrawInfo(CarActivityReq param) {
        if (param.getLotteryActivityId() == null || param.getUserId() == null) {
            return ResultMsgBean.failResponse(500, "用户ID、抽奖活动ID不能为空");
        }
        //起码要达到该等级才能抽奖
        Integer minCarCreditRankLevel = tytConfigService.getIntValue("minCarCreditRankLevel", 2);
        //校验用户等级是否可以抽奖
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(param.getUserId());
        if (Objects.isNull(userCreditInfo) || StringUtils.isEmpty(userCreditInfo.getCarCreditRankLevel()) || Integer.parseInt(userCreditInfo.getCarCreditRankLevel()) < minCarCreditRankLevel) {
            return new ResultMsgBean(ReturnCodeConstant.USER_CAR_GRADE_ERR, "未达到可抽奖等级");
        }
        CarActivityDetail carActivityDetail = carGrowthSystemService.getUserLuckyDrawInfo(param);
        return ResultMsgBean.successResponse(carActivityDetail);
    }


    /**
     * 车方信用体系-用户抽奖
     *
     * @param param
     * @return
     */
    @RequestMapping(value = {"/carGrowthLucky", "/carGrowthLucky.action"})
    public ResultMsgBean carGrowthLucky(ParticipateActivityReq param) {
        //校验用户等级是否可以抽奖
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(param.getUserId());
        //起码要达到该等级才能抽奖
        Integer minCarCreditRankLevel = tytConfigService.getIntValue("minCarCreditRankLevel", 2);
        if (Objects.isNull(userCreditInfo) || StringUtils.isEmpty(userCreditInfo.getCarCreditRankLevel()) || Integer.parseInt(userCreditInfo.getCarCreditRankLevel()) < minCarCreditRankLevel) {
            return new ResultMsgBean(ReturnCodeConstant.USER_CAR_GRADE_ERR, "未达到可抽奖等级");
        }
        String key = LOCK_KEY_PREFIX + "_" + param.getActivityId();
        try {
            if (LockUtil.lockObject("1", key, 3)) {
                return lotteryRecordService.activityLottery(param.getUserId(), param.getActivityId(), param.getAgreementActivityId());
            }
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "请稍后再进行抽奖");
        } catch (Exception e) {
            logger.error("定向用户抽奖失败，失败原因:", e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        } finally {
            logger.info("ACTIVITY_LOTTERY release redis lock successed, key is: " + LOCK_KEY_PREFIX + "_" + param.getActivityId() + "_" + param.getUserId());
            LockUtil.unLockObject("1", key);
        }
    }

    /**
     * 获取车方成长体系信息
     *
     * @param userId
     */
    @RequestMapping(value = {"/growthSystemInfo", "/growthSystemInfo.action"})
    public ResultMsgBean growthSystem(Long userId) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        logger.info("获取用户成长体系信息开始，userId:{}", userId);
        GrowthInfoVo growthInfoVo = carGrowthSystemService.growthSystemInfo(userId);
        return ResultMsgBean.successResponse(growthInfoVo);
    }

}
