package com.tyt.acvitity.controller;

import com.tyt.acvitity.bean.InfoBean;
import com.tyt.acvitity.bean.StimulateLoctionBean;
import com.tyt.acvitity.service.StimulateActivityService;
import com.tyt.acvitity.service.StimulateCarLocationService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytStimulateCarLocation;
import com.tyt.util.BASE64DecodedMultipartFile;
import com.tyt.util.Base64Util;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;
import com.vdurmont.emoji.EmojiParser;
import jodd.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RequestMapping(value = "/stimulate")
@RestController
@Slf4j
public class StimulateCarLocaltionCntroller {

    @Autowired
    private StimulateCarLocationService stimulateCarLocationService;

    @Autowired
    private StimulateActivityService stimulateActivityService;



    @PostMapping(value = {"/addStimulateCarLocation", "/addStimulateCarLocation.action"})
    public ResultMsgBean addStimulateCarLocation( StimulateLoctionBean tytStimulateCarLocation){
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            TytStimulateCarLocation location = new TytStimulateCarLocation();
            BeanUtils.copyProperties(tytStimulateCarLocation,location);
            location.setType(1);
            location.setCtime(new Date());
            stimulateCarLocationService.add(location);
            stimulateActivityService.updateStatus(tytStimulateCarLocation.getStimulateActivityId());
            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("保存成功");
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }


    /**
     * 用户刷单凭证上传
     * @param infoBean
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = {"/updateStimulateOrderStatus", "/updateStimulateOrderStatus.action"})
    public ResultMsgBean updateStimulateOrderStatus(InfoBean infoBean) throws Exception {
        ResultMsgBean rm = new ResultMsgBean();
        try {

            if (null == infoBean.getId() || StringUtils.isBlank(infoBean.getReason()) ||null == infoBean.getUserId()) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("参数异常");
                return rm;
            }
            if(infoBean.getFiles()== null ||infoBean.getFiles().length == 0 ){
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("请上传图片");
                return rm;
            }
            List<MultipartFile> fileList = new ArrayList<>();

            for (MultipartFile string : infoBean.getFiles()) {
                fileList.add(string);
            }

            return stimulateCarLocationService.updateStimulateOrderStatus(infoBean.getId(), infoBean.getReason(), infoBean.getUserId(),  fileList);
        } catch (Exception e) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }


    /**
     * 根据刷单id查询申诉详情
     * @param id
     * @return
     */
    @GetMapping(value = {"/getStimulateInfo", "/getStimulateInfo.action"})
    public ResultMsgBean getStimulateInfo( Long id){
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            StimulateLoctionBean bean =  stimulateActivityService.getByIdInfo(id);
            resultMsgBean.setData(bean);
            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("保存成功");
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }

    /**
     * 新风险单申诉
     * @param infoBean
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = {"/updateStimulate", "/updateStimulate.action"})
    public ResultMsgBean updateStimulate(InfoBean infoBean) throws Exception {
        log.info("用户申诉上传凭证参数验证");
        ResultMsgBean rm = new ResultMsgBean();
        try {

            if (StringUtils.isBlank(infoBean.getHeadCity()) || StringUtils.isBlank(infoBean.getHeadNo()) ||
                StringUtils.isBlank(infoBean.getTailCity()) || StringUtils.isBlank(infoBean.getTailNo())) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("车辆信息缺失");
                return rm;
            }
            String characterFilter = "[^\\p{L}\\p{M}\\p{N}\\p{P}\\p{Z}\\p{Cf}\\p{Cs}\\s]";
            infoBean.setReason(infoBean.getReason().replaceAll(characterFilter,""));
            /*if(StringUtils.isNotEmpty(infoBean.getReason())){
                infoBean.setReason(EmojiParser.removeAllEmojis(infoBean.getReason()));
            }*/
            if(StringUtils.isBlank(infoBean.getReason())){
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("请输入正确文字");
                return rm;
            }

            if (null == infoBean.getId() || StringUtils.isBlank(infoBean.getReason()) ||null == infoBean.getUserId()) {
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("参数异常");
                return rm;
            }
            if(null == infoBean.getType()){
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("申诉方缺失");
                return rm;
            }
            /*if(infoBean.getFiles()== null ||infoBean.getFiles().length == 0 ){
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("请上传图片");
                return rm;
            }*/
            List<MultipartFile> fileList = new ArrayList<>();
            if(null != infoBean.getFiles() && infoBean.getFiles().length > 0){
                for (MultipartFile string : infoBean.getFiles()) {
                    fileList.add(string);
                }

            }

            //return stimulateCarLocationService.updateStimulateOrderStatus(infoBean.getId(), infoBean.getReason(), infoBean.getUserId(),  fileList);
            log.info("用户申诉上传凭证开始");
            return stimulateCarLocationService.updateStimulate(infoBean,  fileList);
        } catch (Exception e) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            return rm;
        }
    }

    /**
     * 删除申诉图片
     * @param id
     * @return
     */
    @GetMapping(value = {"/deleteInfo", "/deleteInfo.action"})
    public ResultMsgBean deleteInfo( Long id){
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
           stimulateCarLocationService.deleteInfo(id);
            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("图片删除成功");
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }
}
