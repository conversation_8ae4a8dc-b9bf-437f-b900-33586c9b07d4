package com.tyt.acvitity.controller;

import com.gexin.fastjson.JSON;
import com.tyt.acvitity.bean.*;
import com.tyt.acvitity.service.*;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.infofee.bean.ActivityUserAddressBean;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.MarketingActivityMapper;
import com.tyt.user.querybean.UserCreditBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
* @DES 履约活动
*/
@RequestMapping(value = "convention")
@RestController
@Slf4j
public class ConventionActivityController {
    @Autowired
    private ConventionActivityService conventionActivityService;
    @Autowired
    private MarketingActivityService marketingActivityService;
    @Autowired
    private TytConfigService tytConfigService;
    @Autowired
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;
    @Autowired
    private UserService userService;

    @Autowired
    private ActivityGradePrizeService activityGradePrizeService;

    @Autowired
    private ActivityUserAddressService activityUserAddressService;

    @Autowired
    private ConventionOrdersCensusService conventionOrdersCensusService;

    @Autowired
    private CarActivityDataService carActivityDataService;

    @Autowired
    private DrawActivityInfoService drawActivityInfoService;

    @Autowired
    private MarketingActivityMapper marketingActivityMapper;

    //服务器图片路径(旧规则)
    public static final String TYT_SERVER_PICTURE_URL_OLD = "tyt_server_picture_url_old";

    /**
     * 获取活动信息
     *
     * @param userId
     * @return
     */
    @GetMapping(value = {"getActivityInfo", "getActivityInfo.action"})
    public ResultMsgBean getConventionActivityInfo(Long userId) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        Integer activityType = tytConfigService.getIntValue("convention_activity_type", 4);
//        Integer activityScope = tytConfigService.getIntValue("convention_activity_scope", 2);
        MarketingActivity effectiveActivity = conventionActivityService.getEffectiveActivity(activityType);
        if (Objects.isNull(effectiveActivity)) {
            log.info("获取履约活动信息，未查询到履约活动相关信息");
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未找到活动");
        }
        if (effectiveActivity.getActivityScope() ==1){
            return ResultMsgBean.successResponse(new ConventionActivityVo(1, null));
        }
        //该用户不是定向活动该用户
        List<MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(effectiveActivity.getId(), userId);
        if (CollectionUtils.isEmpty(activityInfoByUserId)) {
            log.info("根据userId查询履约活动信息，未查询到该用户履约活动相关信息,userId={}", userId);
            return ResultMsgBean.successResponse(new ConventionActivityVo(0, null));
        }

        //用户完单数
//        Long orderNumByUserId = conventionActivityService.getOrderNumByUserId(effectiveActivity.getId(), userId);
//        Integer rankingByUserId = conventionActivityService.getRankingByUserId(effectiveActivity.getId(), userId);
//        log.info("根据userId查询履约活动信息,用户userId:{}排名为:{}", userId, rankingByUserId);
        return ResultMsgBean.successResponse(new ConventionActivityVo(1, null));
    }

    /**
     * 获取履约活动排名信息
     *
     * @return
     */
    @GetMapping(value = {"getActivityPrizeInfo", "getActivityPrizeInfo.action"})
    public ResultMsgBean getActivityPrizeInfo(Long userId,Long activityId) {
        try {
            if (Objects.isNull(userId)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
            }
            if (Objects.isNull(activityId)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "活动id不能为空");
            }
            MarketingActivity activity = marketingActivityService.getById(activityId);
            if (Objects.isNull(activity)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未找到相关活动");
            }
            if (activity.getActivityScope() == 1) {
                return ResultMsgBean.successResponse(new ConventionActivityVo(1, null));
            }
            //该用户不是定向活动该用户
            List <MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(activityId, userId);
            if (CollectionUtils.isEmpty(activityInfoByUserId)) {
                log.info("根据userId查询履约活动信息，未查询到该用户履约活动相关信息,userId={}", userId);
                return ResultMsgBean.successResponse(new ConventionActivityVo(0, null));
            }

            ActivityVo activityVo = conventionActivityService.getConventionActivityInfo(activity.getId(), userId);
            if (activityVo != null) {
                Date nowDate = new Date();
                if (nowDate.before(activity.getEndTime()) && nowDate.after(activity.getStartTime())) {
                    activityVo.setIsInActivity(true);
                }
                activityVo.setUserGrade(activityInfoByUserId.get(0).getUserGrade());
                //服务器当前时间
                activityVo.setNowDate(nowDate);
                //活动结束时间
                activityVo.setActivityEndTime(activity.getEndTime());
            }
            //获取等级对应奖品信息
            List<ActivityGradePrizeVo> activityGradePrizeVo = activityGradePrizeService.getByActivityIdAndGrade(activity.getId(), activityInfoByUserId.get(0).getUserGrade(),userId);

            //用户信用分和头像信息
            UserCreditBean userCreditBean = new UserCreditBean();
            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
            if (userCreditInfo != null) {
                if (null != userCreditInfo.getTotalScore()) {
                    userCreditBean.setTotalScore(userCreditInfo.getTotalScore().setScale(0, RoundingMode.DOWN));
                }
                if (null != userCreditInfo.getRankLevel() && userCreditInfo.getRankLevel() != 0) {
                    userCreditBean.setRankLevel(userCreditInfo.getRankLevel());
                } else {
                    userCreditBean.setRankLevel(1);
                }
                if (null != userCreditInfo.getCarTotalServerScore()) {
                    userCreditBean.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
                }
                if (null != userCreditInfo.getCarServerRankScore()) {
                    userCreditBean.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
                }
            } else {
                userCreditBean.setTotalScore(BigDecimal.ZERO);
                userCreditBean.setRankLevel(1);
            }
            //查询用户信息
            User user = userService.getByUserId(userId);
            if(user != null){
                //服务器图片路径(旧规则)
                String tytServerPictureUrlOld = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_OLD);
                String headUrl = user.getHeadUrl();
                if(StringUtils.isNotBlank(headUrl)){
                    userCreditBean.setHeadUrl(tytServerPictureUrlOld + headUrl);
                }
            }
            log.info("根据userId查询履约活动信息,用户userId:{},活动activityId:{},活动信息详情为:{},activityInfoByUserId:{} ", userId, activityId, JSON.toJSONString(activityVo), JSON.toJSONString(activityInfoByUserId));
            return ResultMsgBean.successResponse(new ConventionActivityVo(1, null, activityVo, userCreditBean,activityGradePrizeVo));
        } catch (Exception e) {
            e.printStackTrace();
            log.info("根据userId查询履约活动信息,用户userId:{},活动activityId:{}", userId, activityId);
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "查询履约活动信息失败！");
        }
    }


    /**
     * 获取履约活动排名信息
     *
     * @return
     */
    @GetMapping(value = {"getOrderRanking", "getOrderRanking.action"})
    public ResultMsgBean getOrderRanking(Long userId,Long activityId) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        if (Objects.isNull(activityId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "活动id不能为空");
        }
        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (Objects.isNull(activity)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未找到相关活动");
        }
        ConventionActivityRanking orderRanking = conventionActivityService.getOrderRanking(userId, activity);
        return ResultMsgBean.successResponse(orderRanking);
    }

    /**
     * @description 更新履约活动中奖信息接口
     * <AUTHOR>
     * @date 2022/7/15 16:33
     * @param userId
     * @param activityId
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = {"/updateAwardInfo", "/updateAwardInfo.action"})
    @ResponseBody
    public ResultMsgBean updateAwardInfo(@RequestParam(name="userId",required=true) Long userId,
                                         @RequestParam(name="activityId",required=true) Long activityId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            int result = conventionActivityService.updateAwardInfo(userId, activityId, 1);
            //更新成功
            if(result > 0){
                resultMsgBean.setCode(ReturnCodeConstant.OK);
                resultMsgBean.setMsg("更新履约活动中奖信息成功！");
            }else{ //更新失败
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("更新履约活动中奖信息失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            log.info("更新履约活动中奖信息失败，失败原因:"+e);
        }
        return resultMsgBean;
    }

    /**
     * @description 更新选择的目标奖品信息
     * <AUTHOR>
     * @date 2022/8/5 10:33
     * @param userId
     * @param activityId
     * @param targetPrize
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = {"/updateTargetPrize", "/updateTargetPrize.action"})
    @ResponseBody
    public ResultMsgBean updateTargetPrize(@RequestParam(name="userId",required=true) Long userId,
                                           @RequestParam(name="activityId",required=true) Long activityId,
                                           @RequestParam(name="targetPrize",required=true) Integer targetPrize) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            int result = conventionActivityService.updateTargetPrize(userId, activityId, targetPrize);
            //更新成功
            if(result > 0){
                resultMsgBean.setCode(ReturnCodeConstant.OK);
                resultMsgBean.setMsg("更新选择的目标奖品信息成功！");
                //开关（GTV活动人员是否同步单单奖）
                String exposureOnoff = tytConfigService.getStringValue("tyt:exposure:activity:onoff","0");
                //3月单单送活动
                if(Integer.valueOf(exposureOnoff) == 1){
                    Integer fourGtvId = tytConfigService.getIntValue("tyt:four:gtv:id",81);
                    String exposureAppointActivityId = null;
                    //开关（单单奖活动id使用旧版、新版）
                    if (activityId.intValue() == fourGtvId){
                        //单单送（定向）活动id--旧版
                        exposureAppointActivityId = tytConfigService.getStringValue("exposure_appoint_activity_id");
                    }else {
                        //单单送（定向）活动id--新版
                        exposureAppointActivityId = tytConfigService.getStringValue("exposure_appoint_activity_id_new");
                    }
                    if (StringUtils.isNotBlank(exposureAppointActivityId)){
                        Long exposureAppointId = Long.valueOf(exposureAppointActivityId);
                        //是否已在本活动名单
                        List <MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(exposureAppointId, userId);
                        if ( null == activityInfoByUserId || activityInfoByUserId.size() <= 0){
                            activityUserAddressService.insertActivityInfoUser(exposureAppointId,userId);
                        }
                    }
                }
            }else{ //更新失败
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("更新选择的目标奖品信息失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            log.info("更新选择的目标奖品信息失败，失败原因:"+e);
        }
        return resultMsgBean;
    }

    /**
      * <AUTHOR> Lion
      * @Description 履约活动运单详情
      * @Param [userId, activityId]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/8/5 9:56
     * userId 用户id
      */
    @GetMapping(value = {"/getOrdersListInfo", "/getOrdersListInfo.action"})
    public ResultMsgBean getOrdersListInfo(Long userId,Long activityId) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        if (Objects.isNull(activityId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "活动id不能为空");
        }
        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (Objects.isNull(activity)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未找到相关活动");
        }
        if (activity.getActivityScope() ==1){
            return ResultMsgBean.successResponse(new ConventionActivityVo(1, null));
        }
        //该用户不是定向活动该用户
        List<MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(activityId, userId);
        if (CollectionUtils.isEmpty(activityInfoByUserId)) {
            log.info("根据userId查询履约活动信息，未查询到该用户履约活动相关信息,userId={}", userId);
            return ResultMsgBean.successResponse(new ConventionActivityVo(0, null));
        }

        //查询活动履约运单
        List<TransportOrders> ordersInfoList = conventionActivityService.getOrdersInfo(activityId, userId);

        log.info("根据userId查询履约运单信息,用户userId:{},活动activityId:{},履约运单信息详情为:{} ,,activityInfoByUserId:{}", userId,activityId, JSON.toJSONString(ordersInfoList),JSON.toJSONString(activityInfoByUserId));
        return ResultMsgBean.successResponse(ordersInfoList);
    }


    /**
     * 根据userId获取该用户的收货地址
     * @param userId
     * @return
     */
    @GetMapping(value = {"getActivityUserAddressByUserId", "getActivityUserAddressByUserId.action"})
    public ResultMsgBean getActivityUserAddressByUserId(Long userId) {
        try {
            if (Objects.isNull(userId)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
            }
            User user = userService.getByUserId(userId);
            if (Objects.isNull(user)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.NOT_LOGGED_IN_CODE, "获取用户信息失败,请确认后重试");
            }
            TytActivityUserAddress activityUserAddress = activityUserAddressService.getActivityUserAddressByUserId(userId);
            //当不存在时 则仅返回该用户的基础信息
            if(activityUserAddress==null){
                TytActivityUserAddress activityUser= new TytActivityUserAddress();
                activityUser.setUserId(userId);
                activityUser.setUserName(user.getUserName());
                activityUser.setUserCellPhone(user.getCellPhone());
                return ResultMsgBean.successResponse(activityUser);
            }else{
                return ResultMsgBean.successResponse(activityUserAddress);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.info("根据userId获取该用户的收货地址异常{}", e.getMessage());
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, e.getMessage());
        }


    }

    /**
     * 编辑保存该用户的收货地址
     * @return
     */
    @PostMapping(value = {"updateOrSaveActivityUserAddress", "updateOrSaveActivityUserAddress.action"})
    public ResultMsgBean updateOrSaveActivityUserAddress(@RequestBody @Valid ActivityUserAddressBean activityUserAddress) {
        try {
            int count = activityUserAddressService.selectActivityUserAddressCount(activityUserAddress.getUserId(), activityUserAddress.getActivityId());
            if(count==0){
                activityUserAddressService.addActivityUserAddress(activityUserAddress);
            }else{
                activityUserAddressService.updateActivityUserAddress(activityUserAddress);
            }
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("编辑保存该用户的收货地址异常{}", e.getMessage());
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "编辑保存该用户的收货地址失败");
        }
    }


    /**
     * 保存该用户的收货地址
     * @return
     */
    @PostMapping(value = {"addActivityUserAddress", "addActivityUserAddress.action"})
    public ResultMsgBean addActivityUserAddress(@RequestBody @Valid ActivityUserAddressBean activityUserAddress) {
        try {
            activityUserAddressService.addActivityUserAddress(activityUserAddress);
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("保存该用户的收货地址异常{}", e.getMessage());
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "保存该用户的收货地址失败");
        }
    }

    /**
     * 活动期间的前三名用户展示
     * @param userId
     * @param activityId
     * @return
     */
    @GetMapping(value = {"getRank", "getRank.action"})
    public ResultMsgBean getRank(Long userId,Long activityId) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        if (Objects.isNull(activityId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "活动id不能为空");
        }
        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (Objects.isNull(activity)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未找到相关活动");
        }
        List<ConventionRankingList> orderRanking = conventionActivityService.getRanking(userId, activity);
        return ResultMsgBean.successResponse(orderRanking);
    }

    /**
     * 2023三月曝光活动--查询当前进度和排名个列表
     *
     * http://192.168.2.20:3300/project/37/interface/api/10582
     *
     */
    @GetMapping(value = {"/exposure2303/getRankList", "/exposure2303/getRankList.action"})
    public ResultMsgBean getExposure2303RankList(@RequestParam(name="userId",required=true)Long userId,@RequestParam(name="activityId",required=true)Long activityId) {
        ConventionRankListVo data = conventionOrdersCensusService.getCurrentOrderNumAndRankList(userId,activityId);
        return ResultMsgBean.successResponse(data);
    }

    /**
     * 2023三月曝光活动--查询中奖记录
     *
     * http://192.168.2.20:3300/project/37/interface/api/10583
     *
     */
    @GetMapping(value = {"/exposure2303/getPrizeLog", "/exposure2303/getPrizeLog.action"})
    public ResultMsgBean getExposure2303PrizeLog(@RequestParam(name="userId",required=true)Long userId,@RequestParam(name="activityId",required=true)Long activityId) {
        List<ConventionOrdersCensusVo> prizeLogList = conventionOrdersCensusService.getPrizeLog(userId,activityId);
        return ResultMsgBean.successResponse(prizeLogList);
    }

    /**
     * 单单送及全员送曝光卡运单列表
     * @param userId
     * @param activityId
     * @return
     * @throws ParseException
     */
    @GetMapping (value = {"/getActivityExposure","getActivityExposure.action"})
    public ResultMsgBean getActivityExposure(Long userId,Long activityId) {
        ResultMsgBean rm = new ResultMsgBean();
        if (Objects.isNull(userId) || Objects.isNull(activityId)){
            return rm.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "缺少必要的参数");
        }
        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (Objects.isNull(activity)) {
            return rm.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未找到相关活动");
        }

        String exposureAppointActivityId = tytConfigService.getStringValue("exposure_appoint_activity_id","0");
        Long exposureAppointId = null;
        if (StringUtils.isNotBlank(exposureAppointActivityId)){
            exposureAppointId = Long.valueOf(exposureAppointActivityId);
        }
        //3月份单单送活动
        if(activityId.equals(exposureAppointId)){
            if (activity.getActivityScope() ==1){
                return rm.successResponse(new ConventionActivityVo(1, null));
            }
            //该用户不是定向活动该用户
            List<MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(activityId, userId);
            if (CollectionUtils.isEmpty(activityInfoByUserId)) {
                log.info("(单单送曝光权益)根据userId查询履约活动信息，未查询到该用户履约活动相关信息,userId={}", userId);
                return rm.successResponse(new ConventionActivityVo(0, null));
            }
            //查询活动履约运单
            List<TransportOrders> ordersExposureList = conventionActivityService.getOrdersInfo(activityId, userId);
            rm.setCode(ResultMsgBean.OK);
            rm.setMsg(ResultMsgBean.OK_MSG);
            rm.setData(ordersExposureList);
            return rm;
        }else {
            List<TransportOrders> ordersExposureList = conventionActivityService.getOrdersExposure(activityId, userId);
            rm.setCode(ResultMsgBean.OK);
            rm.setMsg(ResultMsgBean.OK_MSG);
            rm.setData(ordersExposureList);
            return rm;
        }
    }

    /**
     * 单单送及全员送曝光卡运单列表 -- 更换新表 -- 2023.04.24
     * @param userId
     * @param activityId
     * @return
     * @throws ParseException
     */
    @GetMapping (value = {"/getActivityExposureRisk","getActivityExposureRisk.action"})
    public ResultMsgBean getActivityExposureRisk(Long userId,Long activityId) {
        ResultMsgBean rm = new ResultMsgBean();
        if (Objects.isNull(userId) || Objects.isNull(activityId)){
            return rm.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "缺少必要的参数");
        }
        MarketingActivity marketingActivity = marketingActivityService.getById(activityId);
        if (Objects.isNull(marketingActivity)) {
            return rm.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未找到相关活动");
        }

        String exposureAppointActivityId = tytConfigService.getStringValue("exposure_appoint_activity_id_new","0");
        Long exposureAppointId = null;
        if (StringUtils.isNotBlank(exposureAppointActivityId)){
            exposureAppointId = Long.valueOf(exposureAppointActivityId);
        }
        //3月份单单送活动
        if(marketingActivity.getActivityType() == 12){
            /*if (marketingActivity.getActivityScope() ==1){
                return rm.successResponse(new ConventionActivityVo(1, null));
            }

            //该用户不是定向活动该用户
            List<MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(activityId, userId);
            if (CollectionUtils.isEmpty(activityInfoByUserId)) {
                log.info("(单单送曝光权益)根据userId查询履约活动信息，未查询到该用户履约活动相关信息,userId={}", userId);
                return rm.successResponse(new ConventionActivityVo(0, null));
            }*/
            //查询活动履约运单
            List<TransportOrders> ordersExposureList = conventionActivityService.getOrdersInfoRisk(activityId, userId,marketingActivity);
            rm.setCode(ResultMsgBean.OK);
            rm.setMsg(ResultMsgBean.OK_MSG);
            rm.setData(ordersExposureList);
            return rm;
        }else {
            List<TransportOrders> ordersExposureList = conventionActivityService.getOrdersExposureRisk(activityId, userId);
            rm.setCode(ResultMsgBean.OK);
            rm.setMsg(ResultMsgBean.OK_MSG);
            rm.setData(ordersExposureList);
            return rm;
        }
    }

    /**
     * 单单送活动单量查询
     * @param userId
     * @param activityId
     * @return
     */
    @GetMapping (value = {"/getActivityExposureCount","getActivityExposureCount.action"})
    public ResultMsgBean getActivityExposureCount(Long userId,Long activityId){
        ResultMsgBean rm = new ResultMsgBean();
        ConventionGiveGoodsRecordBean recordBean = new ConventionGiveGoodsRecordBean();
        if (Objects.isNull(userId) || Objects.isNull(activityId)){
            rm.setCode(500);
            rm.setMsg("缺少必要的参数");
            return rm;
        }
        //获取活动
        boolean isValid = marketingActivityService.isValidActivity(activityId,userId);
        //该用户不是定向活动该用户
//        List<MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(activityId, userId);
        if (!isValid) {
            log.info("根据userId查询履约活动信息，未查询到该用户履约活动相关信息,userId={}", userId);
            rm.setCode(200);
            rm.setMsg(ResultMsgBean.OK_MSG);
            recordBean.setActivityStatus(0);
            rm.setData(recordBean);
            return rm;
        }
        Integer recordCount = conventionActivityService.getActivityExposureCount(userId,activityId);
            recordBean.setActivityCount(recordCount);
        Integer count = conventionActivityService.getActivityCountGoods(userId,activityId);
            recordBean.setActivityExposure(count);
            recordBean.setActivityStatus(1);
            rm.setCode(200);
            rm.setMsg(ResultMsgBean.OK_MSG);
            rm.setData(recordBean);
            return rm;
    }

    /**
     * 通过用户id和运营活动id、抽奖活动id查询车主透传活动页面所需详情信息
     * @param param param
     * @return ResultMsgBean
     */
    @GetMapping({"/carAtivityDetail","/carAtivityDetail.action"})
    public ResultMsgBean getCarAtivityDetail(CarActivityReq param){
        if (param.getMarketingActivityId() == null || param.getLotteryActivityId() == null || param.getUserId() == null) {
            return ResultMsgBean.failResponse(500, "用户ID、运营活动ID、抽奖活动ID不能为空");
        }
        MarketingActivity marketingActivityInfo = marketingActivityMapper.selectById(param.getMarketingActivityId());
        DrawActivityInfo drawActivityInfo = drawActivityInfoService.getById(param.getLotteryActivityId());
        if (!carActivityDataService.checkCarActivityContainUser(param, drawActivityInfo)) {
            return ResultMsgBean.failResponse(10002, "无活动权限");
        }
        CarActivityDetail carActivityDetail = carActivityDataService.getCarAtivityDetail(param, marketingActivityInfo, drawActivityInfo);
        return ResultMsgBean.successResponse(carActivityDetail);
    }

    /**
     * 通过用户id和运营活动id查询车主透传活动 活动单列表数据
     * @param param param
     * @return ResultMsgBean
     */
    @GetMapping({"/carAtivityOrderList","/carAtivityOrderList.action"})
    public ResultMsgBean getCarAtivityOrderList(CarActivityReq param){
        if (param.getMarketingActivityId() == null || param.getUserId() == null) {
            return ResultMsgBean.failResponse(500, "用户ID、运营活动ID不能为空");
        }
        List<TytTransportOrders> tytTransportOrders = carActivityDataService.getCarAtivityOrderList(param);
        return ResultMsgBean.successResponse(tytTransportOrders);
    }


    /**
     * 通过用户id和运营活动id查询车主透传活动 活动单列表数据 -- 更换新表 -- 2023.04.24
     * @param param param
     * @return ResultMsgBean
     */
    @GetMapping({"/carAtivityOrderRiskList","/carAtivityOrderRiskList.action"})
    public ResultMsgBean getCarAtivityOrderRiskList(Long userId,Long activityId){
        if (userId == null || activityId == null) {
            return ResultMsgBean.failResponse(500, "用户ID、运营活动ID不能为空");
        }

        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (Objects.isNull(activity)) {
            return ResultMsgBean.failResponse(500,  "未找到相关活动");
        }

        List<MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(activityId, userId);
        if (CollectionUtils.isEmpty(activityInfoByUserId)) {
            log.info("根据userId查询履约活动信息，未查询到该用户履约活动相关信息,userId={}", userId);
            return ResultMsgBean.failResponse(500, "该用户不是定向活动该用户");
        }
        List<TytTransportOrders> tytTransportOrders = carActivityDataService.getCarAtivityOrderRiskList(activityId, userId,activity);
        return ResultMsgBean.successResponse(tytTransportOrders);
    }


    /**
     * 根据用户id和分类查询全量风险单
     * @param userId 用户id
     * @param type 分类  1：货， 2 ：车
     * @param classify   0：所有风险单    1：优货风险单
     * @return
     */
    @GetMapping(value = {"/getAllOrdersListInfo", "/getAllOrdersListInfo.action"})
    public ResultMsgBean getAllOrdersListInfo(Long userId,Integer type,Long queryId,Integer queryActionType, Integer classify) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }

        //查询活动履约运单
        List<TransportOrders> ordersInfoList = conventionActivityService.getAllOrdersListInfo(userId,type,queryId,queryActionType,classify);

        log.info("根据userId查询履约运单信息,用户userId:{},分类:{},履约运单信息详情为:{}", userId,type, JSON.toJSONString(ordersInfoList));
        return ResultMsgBean.successResponse(ordersInfoList);
    }


    /**
     * 2023。5.1启用新活动单列表
     * @param userId
     * @param activityId
     * @return
     */
    @GetMapping(value = {"/getNewOrdersListInfo", "/getNewOrdersListInfo.action"})
    public ResultMsgBean getNewOrdersListInfo(Long userId,Long activityId) {
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        if (Objects.isNull(activityId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "活动id不能为空");
        }
        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (Objects.isNull(activity)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "未找到相关活动");
        }
        if (activity.getActivityScope() ==1){
            return ResultMsgBean.successResponse(new ConventionActivityVo(1, null));
        }
        //该用户不是定向活动该用户
        List<MarketingActivityUser> activityInfoByUserId = conventionActivityService.getActivityInfoByUserId(activityId, userId);
        if (CollectionUtils.isEmpty(activityInfoByUserId)) {
            log.info("根据userId查询履约活动信息，未查询到该用户履约活动相关信息,userId={}", userId);
            return ResultMsgBean.successResponse(new ConventionActivityVo(0, null));
        }

        //查询货活动履约运单
        List<TransportOrders> ordersInfoList = null;
        if(activity.getActivityType() == 7){
            //查询车活动履约运单
            ordersInfoList = conventionActivityService.getNewCarOrdersInfo(activityId, userId,activity.getStartTime(),activity.getEndTime());

        }else if(activity.getActivityType() == 15){
            //查询车活动履约运单
            ordersInfoList = conventionActivityService.getNewGoodsOrdersInfo( userId,activity.getStartTime(),activity.getEndTime());
        }else{
            //查询货活动履约运单
            ordersInfoList = conventionActivityService.getNewOrdersInfo(activityId, userId,activity.getStartTime(),activity.getEndTime());
        }

        log.info("根据userId查询履约运单信息,用户userId:{},活动activityId:{},履约运单信息详情为:{} ,,activityInfoByUserId:{}", userId,activityId, JSON.toJSONString(ordersInfoList),JSON.toJSONString(activityInfoByUserId));
        return ResultMsgBean.successResponse(ordersInfoList);
    }

}
