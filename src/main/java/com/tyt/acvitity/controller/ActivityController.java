package com.tyt.acvitity.controller;

import com.tyt.acvitity.bean.*;
import com.tyt.acvitity.service.ActivityService;
import com.tyt.acvitity.service.LotteryRecordService;
import com.tyt.acvitity.service.UserActivityService;
import com.tyt.acvitity.service.VipLotteryDrawListService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.goods.service.GoodsService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.model.*;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/23 10:13
 */
@RestController
@RequestMapping("/plat/activity")
public class ActivityController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "activityService")
    private ActivityService activityService;

    @Resource(name = "userActivityService")
    private UserActivityService userActivityService;

    @Resource(name = "transportOrdersService")
    private TransportOrdersService transportOrdersService;

    @Resource(name = "goodsService")
    private GoodsService goodsService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;
    @Resource(name = "vipLotteryDrawListService")
    private VipLotteryDrawListService vipLotteryDrawListService;

    private static final String LOCK_KEY_PREFIX = "ACTIVITY_LOTTERY";

    @Resource
    private LotteryRecordService lotteryRecordService;

    private static final String ACTIVITY_INFOFEE_AMOUNT="activity_infofee_amount";


    @RequestMapping({"/list","/list.action"})
    public Object list(BaseParameter baseParameter){
        try {
//            final List<ActivityListResp> resp = activityService.listByUserId(baseParameter.getUserId());
            int port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(baseParameter.getClientSign()));
            final List<ActivityListResp> resp = marketingActivityService.getMyActivityList(baseParameter.getUserId(),port);
            return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", resp);
        }catch (Exception e){
            logger.error("活动列表查询失败，失败原因:"+e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
    }

    /**
     * 通过用户id和活动id查询活动详情和用户最近一次的获奖记录
     * @param param
     * @return
     */
    @RequestMapping({"/activityDetail","/activityDetail.action"})
    public Object activityDetail(ParticipateActivityReq param){
        try {
            return lotteryRecordService.getActivityDetail(param.getUserId(), param.getActivityId());
        }catch (Exception e){
            logger.error("活动详情查询失败，失败原因:", e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
    }

    @RequestMapping({"/activityLottery", "/activityLottery.action"})
    public Object activityLottery(ParticipateActivityReq param) {
        String key = LOCK_KEY_PREFIX + "_" + param.getActivityId();
        try {
            if (LockUtil.lockObject("1", key, 3)) {
//                return lotteryRecordService.activityLottery(param.getUserId(), param.getActivityId(), param.getAgreementActivityId());
                return new ResultMsgBean(ReturnCodeConstant.OK, "未中奖");
            }
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "请稍后再进行抽奖");
        } catch (Exception e) {
            logger.error("定向用户抽奖失败，失败原因:", e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        } finally {
            logger.info("ACTIVITY_LOTTERY release redis lock successed, key is: " + LOCK_KEY_PREFIX + "_" + param.getActivityId() + "_" + param.getUserId());
            LockUtil.unLockObject("1", key);
        }
    }

    /**
     * 通过用户id和活动id查询活动详情和用户获奖记录
     * @param param
     * @return
     */
    @RequestMapping({"/lotteryRecord"})
    public Object lotteryRecord(LotteryRecordReq param){
        try {
            return lotteryRecordService.getLotteryRecord(param);
        }catch (Exception e){
            logger.error("获奖记录查询失败，失败原因:", e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
    }

    @RequestMapping({"/participate","/participate.action"})
    public Object participate(ParticipateActivityReq req) {
        try {
            if (req.getActivityId() == null) {
                return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "activityId 为空");
            }
            final Activity activity = activityService.getById(req.getActivityId());
            if (activity == null || activity.getStatus() != 1) {
                return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_IS_NOT_EXIT_CODE, "活动无效");
            }
            final Date currentDate = new Date();
            // 如果当前时间小于活动开始时间
            if (currentDate.before(activity.getStartTime())) {
                return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_INACTIVE_NOT_START, "活动未开始");
            }
            final UserActivity userActivity = userActivityService.getByActivityIdAndUserId(req.getActivityId(), req.getUserId());
            if (userActivity == null) {
                UserPermission carPermission = userPermissionService.getUserPermission(req.getUserId(),"100101");
                if (carPermission!=null){
                    if (carPermission.getStatus()==1 || TimeUtil.daysBetween(carPermission.getEndTime(),new Date())<30){
                        return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_NOT_PERM, "没有活动参与资格");
                    }
                }else{
                    User user = userService.getByUserId(req.getUserId());
                    if (TimeUtil.daysBetween(user.getCtime(),new Date())<30){
                        return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_NOT_PERM, "没有活动参与资格");
                    }
                }
            }
            final ActivityParticipateResp resp = new ActivityParticipateResp();
            resp.setIsCashBack(2);

            if (activity.getGoodsId() != null) {
                final TytGoods goods = goodsService.getById(activity.getGoodsId());
                if (goods == null) {
                    logger.error("商品不存在：{}", activity.getGoodsId());
                    return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_IS_NOT_EXIT_CODE, "活动异常");
                }
                resp.setGoodsId(goods.getId());
                resp.setGoodsName(goods.getName());
            }

            if (userActivity != null) {
                resp.setBuyTime(userActivity.getActivityCtime());
                resp.setActivityStatus(userActivity.getActivityStatus());
                //计算活动的失效时间
                if (activity.getEffectiveDay() > 0) {
                    Date effectiveTime = DateUtils.addDays(userActivity.getActivityCtime(), activity.getEffectiveDay());
                    long amount = tytConfigService.getIntValue(ACTIVITY_INFOFEE_AMOUNT, 20000).longValue();
                    //查询完成的订单数量
                    final int total = transportOrdersService.getDateRangFinishAndPayAmountOrderTotal(req.getUserId(), userActivity.getActivityCtime(), effectiveTime, amount);
                    resp.setEffectiveOrderTotal(total);
                    resp.setEffectiveTime(effectiveTime);
                    if (total >= 5) {
                        //获取是否已经申请返现
                        int isCashBack = activityService.getCashBack(req.getUserId());
                        resp.setIsCashBack(isCashBack);
                    }
                } else {
                    resp.setEffectiveTime(activity.getEndTime());
                }
            } else {
                //没有存在关系的活动 用户可以参与
                resp.setActivityStatus(2);
                resp.setEffectiveTime(activity.getEndTime());
            }


            resp.setActivityId(req.getActivityId());
            resp.setTitle(activity.getTitle());
            resp.setContent(activity.getContent());
            resp.setStartTime(activity.getStartTime());
            resp.setEndTime(activity.getEndTime());
            resp.setCurrentTime(new Date());
            resp.setCashbackAmount(activity.getCashbackAmount());


            // 如果当前时间大于活动结束时间
            if (currentDate.after(resp.getEffectiveTime())) {
                return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_INACTIVE_PERIOD, "活动已过期", resp);
            }

            return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", resp);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("活动列表查询失败，失败原因:" + e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
    }

    /**
     * 获取会员抽奖活动资格
     * @param userId
     * @param activityId
     * @return
     */
    @RequestMapping({"/lotteryDrawDetail.action"})
    @ResponseBody
    public ResultMsgBean lotteryDrawDetail(@RequestParam(value = "userId") Long userId, @RequestParam(value = "activityId")Integer activityId){
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK,"查询成功");
        try {
            ActivityListResp activity = marketingActivityService.getActivityByUserId(userId,activityId);
            if (activity == null || activity.getEndTime().before(new Date())){
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("活动已结束！");
                return msgBean;
            }
            msgBean.setData(activity);
        }catch (Exception e){
            logger.error("获取lotteryDrawDetail失败，失败原因:", e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
        return msgBean;
    }

    /**
     * 货会员抽奖
     * @param userId
     * @param activityId
     * @return
     */
    @RequestMapping({"/lotteryDraw.action"})
    @ResponseBody
    public ResultMsgBean lotteryDraw(@RequestParam(value = "userId") Long userId, @RequestParam(value = "activityId")Long activityId){
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK,"操作成功");
        try {
            ActivityListResp activity = marketingActivityService.getActivityByUserId(userId,activityId.intValue());
            if (activity == null || activity.getEndTime().before(new Date())){
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("活动已结束！");
                return msgBean;
            }
            if (activity.getIsJoin() == 2){
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("您已抽过了，每人仅限一次哦~");
                return msgBean;
            }
            int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3",userId.toString()+activityId.toString(),redisLockTimeout)){
                return vipLotteryDrawListService.updateLotteryDraw(userId,activityId,msgBean);
            }
        }catch (Exception e){
            logger.error("lotteryDraw失败，失败原因:", e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "操作失败");
        }finally {
            long t2 = System.currentTimeMillis();
            // 释放redis锁
            LockUtil.unLockObject("3", userId.toString()+activityId.toString());
        }
        return msgBean;
    }

    /**
     * 获取我的奖品
     * @param userId
     * @param activityId
     * @return
     */
    @RequestMapping({"/myLotteryPrize.action"})
    @ResponseBody
    public ResultMsgBean myLotteryPrize(@RequestParam(value = "userId") Long userId, @RequestParam(value = "activityId")Long activityId){
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK,"查询成功");
        try {
            ActivityListResp activity = marketingActivityService.getActivityByUserId(userId,activityId.intValue());
            if (activity == null){
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("活动不存在！");
                return msgBean;
            }
            if (activity.getIsJoin() == 1){
                msgBean.setCode(ResultMsgBean.ERROR);
                msgBean.setMsg("您还未抽奖");
                return msgBean;
            }
            return vipLotteryDrawListService.getMyLotteryPrize(userId,activityId,msgBean);
        }catch (Exception e){
            logger.error("获取myLotteryPrize失败，失败原因:", e);
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
        }
    }

}
