package com.tyt.acvitity.controller;

import com.tyt.acvitity.bean.GrantStrategyConfigDetailListBean;
import com.tyt.acvitity.constant.GrantTypeEnum;
import com.tyt.acvitity.service.GrantStrategyConfigService;
import com.tyt.model.ResultMsgBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 权益发放策略配置
 */
@RestController
@RequestMapping("/plat/grant/strategy/config")
@Slf4j
@RequiredArgsConstructor
public class GrantStrategyConfigController {

    private final GrantStrategyConfigService grantStrategyConfigService;


    /**
     * 根据发放条件获取权益发放策略配置详情
     *
     * @param userId 当前用户Id
     * @return ResultMsgBean
     */
    @GetMapping("/getStrategyConfigByGrantType")
    @ResponseBody
    public ResultMsgBean getStrategyConfigByGrantType(@RequestParam("userId") Long  userId) {
        GrantStrategyConfigDetailListBean strategyConfigByGrantType = grantStrategyConfigService.getStrategyConfigByGrantType(userId,GrantTypeEnum.REGISTER_ALL.getType());
        return ResultMsgBean.successResponse(strategyConfigByGrantType);
    }


}
