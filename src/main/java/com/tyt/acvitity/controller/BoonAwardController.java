package com.tyt.acvitity.controller;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.acvitity.bean.BoonAwardInfoBean;
import com.tyt.acvitity.service.PlatUserBoonAwardInfoService;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;

/**
 * 回馈用户活动controller
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/plat/boonAward")
public class BoonAwardController  extends BaseController {

	@Resource(name = "platUserBoonAwardInfoService")
	private PlatUserBoonAwardInfoService platUserBoonAwardInfoService;
	
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	/**
	 * 3周年庆典回馈会员活动一
	 * @param userId
	 * @return
	 */
	@RequestMapping("/getAwardInfo.action")
	@ResponseBody
	public ResultMsgBean getAwardInfo(@RequestParam(name="userId",required=true) Long userId) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			//获取活动开始时间/结束时间
			String startTime = tytConfigService.getStringValue("acvitity_start_time", "2018-08-01 00:00:00");
			String endTime = tytConfigService.getStringValue("acvitity_end_time", "2018-08-21 23:59:59");
			if(TimeUtil.parseDateString(startTime).getTime()>new Date().getTime()) {
				result.setCode(201);
				result.setMsg("活动未开始");
				return result;
			}
			if(TimeUtil.parseDateString(endTime).getTime()<new Date().getTime()) {
				result.setCode(202);
				result.setMsg("活动已结束");
				return result;
			}
			BoonAwardInfoBean awardInfo=platUserBoonAwardInfoService.verifyAwardInfo(userId);
			if(awardInfo!=null) {
				result.setData(awardInfo);
				result.setCode(ReturnCodeConstant.OK);
			}else {
				result.setMsg("用户id不存在");
				result.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器错误");
			logger.info("回馈会员用户活动查询失败，失败原因:"+e);
		}
		return result;
		
	}
	
}
