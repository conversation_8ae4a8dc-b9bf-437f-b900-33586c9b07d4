package com.tyt.acvitity.controller;

import com.tyt.acvitity.bean.GuaranteeActivityVo;
import com.tyt.acvitity.service.GuaranteeActivityService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
* @description 保障活动controller
*/
@RequestMapping(value = "/plat/guarantee")
@RestController
@Slf4j
public class GuaranteeActivityController {

    @Autowired
    private GuaranteeActivityService guaranteeActivityService;

    /**
     * 参加保障活动
     *
     * @param userId userId
     * @return ResultMsgBean
     */
    @PostMapping(value = {"joinGuaranteeActivity", "joinGuaranteeActivity.action"})
    public ResultMsgBean joinGuaranteeActivity(Long userId) {
        return guaranteeActivityService.joinGuaranteeActivity(userId);
    }

    /**
     * 获取用户是否已参与保障活动
     *
     * @param baseParameter baseParameter
     * @return ResultMsgBean
     */
    @PostMapping(value = {"isJoinGuaranteeActivityByUserId"})
    public ResultMsgBean isJoinGuaranteeActivityAndIsSelectByUserId(BaseParameter baseParameter) {
        boolean joinGuaranteeActivityByUserId = guaranteeActivityService.isJoinGuaranteeActivityByUserId(baseParameter.getUserId());
        if (joinGuaranteeActivityByUserId) {
            return ResultMsgBean.successResponse(new GuaranteeActivityVo(1));
        }
        return ResultMsgBean.successResponse(new GuaranteeActivityVo(0));
    }

}
