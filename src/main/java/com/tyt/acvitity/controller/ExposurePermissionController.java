package com.tyt.acvitity.controller;

import com.tyt.acvitity.bean.*;
import com.tyt.acvitity.service.ExposurePermissionService;
import com.tyt.common.bean.PageData;
import com.tyt.model.*;
import com.tyt.plat.entity.base.ExposurePermissionExpiredRecord;
import com.tyt.plat.entity.base.ExposurePermissionGainRecord;
import com.tyt.plat.entity.base.ExposurePermissionUsedRecord;
import com.tyt.util.TytSourceUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 我的曝光卡权益
 * <AUTHOR>
 * @date 2021/07/23 10:13
 */
@RestController
@RequestMapping("/plat/exposurePermission")
public class ExposurePermissionController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ExposurePermissionService exposurePermissionService;


    /**
     * @description 查询曝光权益获取记录
     * <AUTHOR>
     * @param userId userId
     * @param pageNum 页码
     * @param pageSize 每页数据量
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping({"/getExposurePermissionGainRecordListByUserId","/getExposurePermissionGainRecordListByUserId.action"})
    @ResponseBody
    public ResultMsgBean getExposurePermissionGainRecordListByUserId(Long userId, Integer pageNum, Integer pageSize, String ticket){
        if (userId == null) {
            return ResultMsgBean.failResponse(500, "userId不能为空");
        }
        if (ticket == null) {
            return ResultMsgBean.failResponse(500, "ticket不能为空");
        }
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        PageData<ExposurePermissionGainRecord> exposurePermissionGainRecordListByUserId = exposurePermissionService.getExposurePermissionGainRecordListByUserId(userId, pageNum, pageSize);
        return ResultMsgBean.successResponse(exposurePermissionGainRecordListByUserId);
    }

    /**
     * @description 查询曝光权益使用记录
     * <AUTHOR>
     * @param userId userId
     * @param pageNum 页码
     * @param pageSize 每页数据量
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping({"/getExposurePermissionUsedRecordListByUserId","/getExposurePermissionUsedRecordListByUserId.action"})
    @ResponseBody
    public ResultMsgBean getExposurePermissionUsedRecordListByUserId(Long userId, Integer pageNum, Integer pageSize, String ticket){
        if (userId == null) {
            return ResultMsgBean.failResponse(500, "userId不能为空");
        }
        if (ticket == null) {
            return ResultMsgBean.failResponse(500, "ticket不能为空");
        }
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        PageData<ExposurePermissionUsedRecord> exposurePermissionUsedRecordList = exposurePermissionService.getExposurePermissionUsedRecordListByUserId(userId, pageNum, pageSize);
        return ResultMsgBean.successResponse(exposurePermissionUsedRecordList);
    }

    /**
     * @description 查询曝光权益过期记录
     * <AUTHOR>
     * @param userId userId
     * @param pageNum 页码
     * @param pageSize 每页数据量
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping({"/getExposurePermissionExpiredRecordListByUserId","/getExposurePermissionExpiredRecordListByUserId.action"})
    @ResponseBody
    public ResultMsgBean getExposurePermissionExpiredRecordListByUserId(Long userId, Integer pageNum, Integer pageSize, String ticket){
        if (userId == null) {
            return ResultMsgBean.failResponse(500, "userId不能为空");
        }
        if (ticket == null) {
            return ResultMsgBean.failResponse(500, "ticket不能为空");
        }
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        PageData<ExposurePermissionExpiredRecord> exposurePermissionExpiredRecordList = exposurePermissionService.getExposurePermissionExpiredRecordListByUserId(userId, pageNum, pageSize);
        return ResultMsgBean.successResponse(exposurePermissionExpiredRecordList);
    }

    /**
     * 查询用户拥有的可用货源曝光权益总次数
     * <AUTHOR>
     * @param userId userId
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = {"getUserExposurePermissionCount", "getUserExposurePermissionCount.action"})
    @ResponseBody
    public ResultMsgBeanV2<UserExposurePermissionCountVo> getUserExposurePermissionCount(Long userId) {
        if (userId == null) {
            return ResultMsgBeanV2.failResponse(500, "userId不能为空");
        }
        UserExposurePermissionCountVo userExposurePermissionCountVo = exposurePermissionService.getUserExposurePermissionCount(userId);
        return ResultMsgBeanV2.successResponse(userExposurePermissionCountVo);
    }

    /**
     * 获取我的页面曝光卡获取信息
     * @param userId
     * @return
     */
    @GetMapping(value = "/getUserCenterExposureInfo")
    public ResultMsgBean getUserCenterExposureInfo(Long userId){
        List<UserCenterExposureInfo> exposureInfo = exposurePermissionService.getUserCenterExposureInfo(userId);
        return ResultMsgBean.successResponse(exposureInfo);
    }

    /**
     * 获取曝光卡获取方式
     * @return
     */
    @RequestMapping(value = {"getGainType", "getGainType.action"})
    public ResultMsgBean getGainType(){
        List<TytSource> sources = TytSourceUtil.getSourceList("exposure_permission_gain_type");
        return ResultMsgBean.successResponse(sources);
    }

}
