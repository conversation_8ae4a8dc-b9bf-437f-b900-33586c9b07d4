package com.tyt.acvitity.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.model.UserPermission;
import com.tyt.plat.utils.BigDecimalSerialize;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * 成长体系实体bean
 *
 * <AUTHOR>
 */
@Data
public class GrowthInfoVo {

    /**
     * 用户id
     */
    private Long userId;

    /*
     * 用户身份认证标志0未认证1通过2认证中3认证失败
     */
    private Integer userIdentityStatus = 0;

    /**
     * 企业认证状态 0未认证1通过2认证中3认证失败
     */
    private Integer enterpriseAuthStatus = 0;

    /**
     * 是否车辆认证  是否车辆认证 0未认证或认证中或认证失败 1认证成功
     */
    private Integer carVerifyFlag;

    /**
     * 车方信用等级
     */
    private String carCreditRankLevel;
    /**
     * 车方运点值
     */
    private BigDecimal carCreditScore;
    /**
     * 车主排名
     */
    private Integer carRankNum;
    /**
     * 距下一级相差的人数
     */
    private Integer carLastLevelGap;

    /**
     * 是否弹出等级升级弹框 0：未弹出；1：已弹出
     */
    private Integer isShowTable;
    /**
     * 30天内是否有严重客诉 0：无；1：有
     */
    private Integer isComplaint = 0;
    /**
     * 是否有会员权益  0：否  1：是
     */
    private Integer hasPermission = 0;

    /**
     * 是否联系过货主  0:否 1：是
     */
    private Integer isContactedGoods = 0;

    /**
     * 车方信用等级类型 1:运点值计算  2：购买会员  3：会员首次登录
     */
    private Integer carCreditRankLeveType;



}
