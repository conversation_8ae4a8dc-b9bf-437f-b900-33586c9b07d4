package com.tyt.acvitity.bean;

import com.tyt.user.querybean.UserCreditBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class ConventionActivityVo {
    /**
     * 用户是否在活动中 0:未在活动中 1:在活动中
     */
    private Integer userActivityStatus;
    /**
     * 我的排名
     */
    private Integer rank;

    /**
     * 营销活动信息详情
     */
    private ActivityVo activityVo;

    /**
     * 信用分信息
     */
    private UserCreditBean userCreditBean;

    private List<ActivityGradePrizeVo> activityGradePrizeVos;


    public ConventionActivityVo(Integer userActivityStatus, Integer rank) {
        this.userActivityStatus = userActivityStatus;
        this.rank = rank;
    }

    public ConventionActivityVo(Integer userActivityStatus, Integer rank, ActivityVo activityVo) {
        this.userActivityStatus = userActivityStatus;
        this.rank = rank;
        this.activityVo=activityVo;
    }


    public ConventionActivityVo(Integer userActivityStatus, Integer rank, ActivityVo activityVo, UserCreditBean userCreditBean) {
        this.userActivityStatus = userActivityStatus;
        this.rank = rank;
        this.activityVo=activityVo;
        this.userCreditBean = userCreditBean;
    }

    public ConventionActivityVo(Integer userActivityStatus, Integer rank, ActivityVo activityVo, UserCreditBean userCreditBean,List<ActivityGradePrizeVo> activityGradePrizeVo) {
        this.userActivityStatus = userActivityStatus;
        this.rank = rank;
        this.activityVo=activityVo;
        this.userCreditBean = userCreditBean;
        this.activityGradePrizeVos = activityGradePrizeVo;
    }

}
