package com.tyt.acvitity.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class ConventionActivityRanking {
    /**
     * 我的排名
     */
    private String rank;
    /**
     * 订单数
     */
    private Long orderNum;
    /**
     * 展示奖品还是订单数
     */
    private Integer isShowPrize;
    /**
     * 奖品 1:2000 元油卡 2:1000元油卡 3:500元油卡 4:200元油卡 5:30天会员权益
     */
    private Integer prize;
    /**
     * 排名列表 奖品
     */
    private List<ConventionActivityRankingList> rankingLists;

    public ConventionActivityRanking(String rank, Long orderNum, Integer isShowPrize, Integer prize, List<ConventionActivityRankingList> rankingLists) {
        this.rank = rank;
        this.orderNum = orderNum;
        this.isShowPrize = isShowPrize;
        this.prize = prize;
        this.rankingLists = rankingLists;
    }
}
