package com.tyt.acvitity.bean;

import lombok.Data;

import java.util.List;

@Data
public class ActivityGradePrizeVo {

    private Long id;

    private long activityId;

    private Integer grade;

    private long num;

    private Integer stage;

    private String prize;

    private Integer choose;

    private List<ActivityPrizeVo> ActivityPrizeVos;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public long getActivityId() {
        return activityId;
    }

    public void setActivityId(long activityId) {
        this.activityId = activityId;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public long getNum() {
        return num;
    }

    public void setNum(long num) {
        this.num = num;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public String getPrize() {
        return prize;
    }

    public void setPrize(String prize) {
        this.prize = prize;
    }

    public Integer getChoose() {
        return choose;
    }

    public void setChoose(Integer choose) {
        this.choose = choose;
    }

    public List<ActivityPrizeVo> getActivityPrizeVos() {
        return ActivityPrizeVos;
    }

    public void setActivityPrizeVos(List<ActivityPrizeVo> activityPrizeVos) {
        ActivityPrizeVos = activityPrizeVos;
    }
}
