package com.tyt.acvitity.bean;

import com.tyt.model.DrawActivityInfo;
import com.tyt.util.Constant;

import java.util.Date;

/**
*
*
*<AUTHOR>
*@date 2021/07/23 10:38
*
*/
public class ActivityListResp {

    private Long activityId;

    private String title;

    private String content;

    private Date startTime;

    private Date endTime;

    private Date currentTime = new Date();

    private String linkUrl;

    private Integer isJoin; //1.未参加，2.已参加


    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(Date currentTime) {
        this.currentTime = currentTime;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public Integer getIsJoin() {
        return isJoin;
    }

    public void setIsJoin(Integer isJoin) {
        this.isJoin = isJoin;
    }

    public ActivityListResp() {
    }

    public ActivityListResp(DrawActivityInfo drawActivityInfo, String linkUrl) {
        this.activityId = drawActivityInfo.getId();
        this.content = Constant.LOTTERY_ACTIVITY_CONTENT;
        this.startTime = drawActivityInfo.getStartTime();
        this.endTime = drawActivityInfo.getEndTime();
        this.linkUrl = linkUrl + Constant.LOTTERY_ACTIVITY_LINK_URL;
    }
}
