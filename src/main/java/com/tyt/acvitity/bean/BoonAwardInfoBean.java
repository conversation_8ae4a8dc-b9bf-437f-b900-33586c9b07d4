package com.tyt.acvitity.bean;

import com.tyt.model.PlatUserBoonAwardInfo;

public class BoonAwardInfoBean extends PlatUserBoonAwardInfo{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8114252222685591229L;

	private Integer isParticipateAct;//是否参与过活动1    (1:参加过/2：未参加过)
	private Integer registerDays;//注册天数

	public Integer getIsParticipateAct() {
		return isParticipateAct;
	}

	public void setIsParticipateAct(Integer isParticipateAct) {
		this.isParticipateAct = isParticipateAct;
	}

	public Integer getRegisterDays() {
		return registerDays;
	}

	public void setRegisterDays(Integer registerDays) {
		this.registerDays = registerDays;
	}
	
	
}
