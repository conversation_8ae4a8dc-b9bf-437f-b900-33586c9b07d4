package com.tyt.acvitity.bean;

import lombok.Data;

import java.util.Date;


@Data
public class ActivityVo {
    /**
     * 活动是否已结束
     */
    private Boolean isInActivity=false;
    /**
     * 用户等级
     */
    private Integer userGrade;

    /**
     * 用户完单数
     */
    private Integer orderNum;

    /**
     * 目标奖品信息
     */
    private Integer targetPrize=0;

    /**
     * 用户获取奖品信息
     */
    private Integer prize=0;

    /**
     * 奖品是否领取 （0 未领取 1 已领取）
     */
    private Integer receiveFlag;

    /**
     * 当前活动Id
     */
    private Integer activityId;

    /**
     * 服务器当前时间
     */
    private Date nowDate;


    /**
     * 活动结束时间
     */
    private Date activityEndTime;


    /**
     * 地址填写状态 0未填写，1已填写
     */
    private Integer isHasAddress;

    /**
     * 活动期间用户所有订单数（包含完单和刷单）
     */
    private Integer num = 0;

}
