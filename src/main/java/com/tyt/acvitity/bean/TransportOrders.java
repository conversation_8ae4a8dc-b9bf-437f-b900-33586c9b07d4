package com.tyt.acvitity.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.math.BigDecimal;

/**
 * @ClassName TransportOrders
 * @Description 单量详情Vo
 * <AUTHOR> Lion
 * @Date 2022/8/4 15:06
 * @Verdion 1.0
 **/
@Getter
@Setter
@ToString
public class TransportOrders {
    //运单号
    private String tsOrderNo;
    //出发地
    private String startPoint;
    //目的地
    private String destPoint;
    //货物内容
    private String taskContent;
    //车主昵称
    private String payUserName;
    //车头牌照头字母
    private String headCity;
    //车头牌照号码
    private String headNo;
    //挂车牌照头字母
    private String tailCity;
    //挂车牌照号码
    private String tailNo;
    //刷单状态
    private Integer activityStatus;
    //刷单表id
    private Long stimulateId;

    private Integer appealStatus;

    private Integer appealEvidence;

    private String remark;

    private Long payUserId;
    //货方凭证提交状态 0：未提交   1：已提交
    private Integer goodStatus;
    //车方凭证提交状态 0：未提交   1：已提交
    private Integer carStatus;
    //当前提交申诉的用户
    private Integer beforeType;

    private String weight;
    private String length;
    private String wide;
    private String high;

    private String content;

    //驳回原因
    private Integer rejectType;
    private String rejectReason;
    /**
     * 货名备注
     */
    private String machineRemark;

}
