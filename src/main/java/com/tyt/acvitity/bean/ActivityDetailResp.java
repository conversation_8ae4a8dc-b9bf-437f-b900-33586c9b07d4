package com.tyt.acvitity.bean;

import com.tyt.model.ProbCoupon;
import com.tyt.model.PromoActivity;
import com.tyt.promo.model.PromoCoupon;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Data
public class ActivityDetailResp {
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 活动表id
     */
    private Long promoActivityId;
    /**
     * 活动于奖项关系表
     */
    private Long probCouponId;
    /**
     * 活动名称
     */
//    private String activityName;
    /**
     * 活动开始时间
     */
    private Date startTime;
    /**
     * 活动结束时间
     */
    private Date endTime;
    /**
     * 活动状态(1.有效 2.无效)
     */
//    private Integer status;
    /**
     * 限制每人抽奖次数类型(1.总共 2.每天)
     */
    private Integer limitTimesType;
    /**
     * 限制每人抽奖次数
     */
    private Integer limitTimes;
    /**
     * 定向用户抽奖记录表id
     */
    private Long lotteryRecordId;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 奖项名字
     */
    private String probCouponName;
    /**
     * 过期时间
     */
    private Integer expirationTime;
    /**
     * 奖品类型 1 实物   2 代金券
     */
    private Integer awardType;
    /**
     * 奖券面值
     */
    private BigDecimal couponAmount;

    /**
     * 奖券副标题
     */
    private String couponDesc;

    /**
     * 是否中奖（0：未中奖，1：中奖）
     */
    private Integer isWin;

    public ActivityDetailResp() {
    }

    public ActivityDetailResp(Long activityId, ProbCoupon probCoupon, PromoActivity promoActivity, PromoCoupon promoCoupon) {
        this.activityId = activityId;
        this.promoActivityId = Long.valueOf(promoActivity.getId());
        this.probCouponId = probCoupon.getId();
        this.probCouponName = probCoupon.getActivityName();
        this.expirationTime = 2;
        this.sort = probCoupon.getSort();
        this.awardType = probCoupon.getAwardType();
        this.couponAmount = promoCoupon.getCouponAmount();
        this.couponDesc = promoCoupon.getCouponDesc();
    }
}
