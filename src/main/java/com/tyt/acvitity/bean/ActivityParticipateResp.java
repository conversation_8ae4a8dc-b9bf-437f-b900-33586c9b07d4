package com.tyt.acvitity.bean;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/07/23 10:42
 */
public class ActivityParticipateResp {

    /**
     * 是否可以参与活动 默认0-不可以 1-已参与 2-可以参与
     */
    private Integer activityStatus;

    private Long activityId;

    private String title;

    private String content;

    private Date startTime;

    private Date endTime;

    /**
     * 有效时间截至
     */
    private Date effectiveTime;

    /**
     * 有效的已完成订单数
     */
    private Integer effectiveOrderTotal;

    private Date currentTime;


    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     *返现金额（单位分）
     */
    private Long cashbackAmount;

    /**
     * 购买时间
     */
    private Date buyTime;

    private Integer isCashBack;

    public Date getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(Date currentTime) {
        this.currentTime = currentTime;
    }

    public Integer getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(Integer activityStatus) {
        this.activityStatus = activityStatus;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Integer getEffectiveOrderTotal() {
        return effectiveOrderTotal;
    }

    public void setEffectiveOrderTotal(Integer effectiveOrderTotal) {
        this.effectiveOrderTotal = effectiveOrderTotal;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Long getCashbackAmount() {
        return cashbackAmount;
    }

    public void setCashbackAmount(Long cashbackAmount) {
        this.cashbackAmount = cashbackAmount;
    }

    public Date getBuyTime() {
        return buyTime;
    }

    public void setBuyTime(Date buyTime) {
        this.buyTime = buyTime;
    }

    public Integer getIsCashBack() {
        return isCashBack;
    }

    public void setIsCashBack(Integer isCashBack) {
        this.isCashBack = isCashBack;
    }
}
