package com.tyt.acvitity.bean;

import com.tyt.model.DrawActivityInfo;
import com.tyt.model.ProbCoupon;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 车主透传活动详情信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarActivityDetail {

    /*
    * 抽奖活动信息
    */
    private DrawActivityInfo drawActivityInfo;

    /*
     * 活动单量
     */
    private Long conventionActivityOrderNum;

    /*
     * 用户排名
     */
    private Long ranking;

    /*
     * 前10名用户排名
     */
    private List<CarActivityUserRanking> carActivityUserRanking;

    /*
     * 可用抽奖次数
     */
    private Integer availableLotteriesNum;

    /*
     * 运营活动是否已过期 0：未过期；1：已过期
     */
    private Integer marketingActivityIsExpire;

    /*
     * 抽奖活动是否已过期 0：未过期；1：已过期
     */
    private Integer lotteryActivityIsExpire;

    /*
     * 是否弹出拨打电话提醒 0：不提示；1：提示
     */
    private Integer isShowNeedCallTab;
    /**
     * 奖品信息
     */
    private List<AwardInfo> awardInfos;

    /**
    * 奖品数据
    */
    private List<ProbCoupon> couponList;
    /**
    * 抽奖记录
    */
    private Integer totalNum;
    /**
     * 今日是否抽奖
     */
    private Integer todayNum;

}
