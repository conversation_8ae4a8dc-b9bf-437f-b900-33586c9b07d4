package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.TransportOrders;
import com.tyt.model.TransportOrdersRisk;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface TransportOrdersRiskService {
    List<TransportOrders> getGoodOrderList(Long userId, Integer type, Long queryId, Integer queryActionType, Integer classify);


    TransportOrdersRisk getByRiskId(Long id);

    Long getCountByTime(Date startTime, Date endTime, Long userId);

    Integer getGoodsCountByTime(Date startTime, Date endTime, Long userId);

    /**
     * 获取成交单数量
     *
     * @param userId
     * @param pubMonth
     * @return
     */
    Integer getOrderForTransaction(Long userId, String pubMonth);
}
