package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.InfoBean;
import com.tyt.acvitity.bean.StimulateLoctionBean;
import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytStimulateCarLocation;
import com.tyt.model.TytTransportOrders;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * @description 车辆定位服务层
 * <AUTHOR>
 * @date 2022/8/30 9:47
 */
public interface StimulateCarLocationService extends BaseService<TytStimulateCarLocation, Long> {


    ResultMsgBean updateStimulateOrderStatus(Long id, String reason,Long userId,  List<MultipartFile> fileList)throws Exception;

    StimulateLoctionBean getByStimulateId(Long id);

    ResultMsgBean updateStimulate(InfoBean infoBean, List<MultipartFile> fileList)throws Exception;

    void deleteInfo(Long id);

    public int locationDetermine(TytTransportOrders transportOrders);
}
