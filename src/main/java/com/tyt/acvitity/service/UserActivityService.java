package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.UserActivityVo;
import com.tyt.base.service.BaseService;
import com.tyt.model.UserActivity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/23 11:05
 */
public interface UserActivityService extends BaseService<UserActivity, Long> {

    List<UserActivityVo> list(List<Long> activityIds, Long userId);

    UserActivity getByActivityIdAndUserId(Long activityId, Long userId);

    List<UserActivity> getByUserId(Long userId);
}
