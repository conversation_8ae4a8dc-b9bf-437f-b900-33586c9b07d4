package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.*;
import com.tyt.model.ConventionGiveGoodsRecord;
import com.tyt.model.MarketingActivity;
import com.tyt.model.MarketingActivityUser;
import com.tyt.model.TytTransportOrders;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

public interface ConventionActivityService {
    /**
     *
     * @param activityId
     * @param userId
     * @return
     */
    List<MarketingActivityUser> getActivityInfoByUserId(Long activityId,Long userId);

    List<MarketingActivityUser> getEquitiesByUserId(Long activityId,Long userId,Integer isJoin);

    /**
     * 获取有效的活动信息
     * @param activityType
     * @param activityScope
     * @return
     */
    MarketingActivity getEffectiveActivity(Integer activityType);

    /**
     * 根据活动id以及用户id 查询活动内完成的订单数
     * @param activityId
     * @param userId
     * @return
     */
    Long getOrderNumByUserId(Long activityId,Long userId);


    /**
     * 根据活动id以及用户id 查询活动内完成的订单数
     * @param activityId
     * @param userId
     * @return
     */
    ActivityVo getConventionActivityInfo(Long activityId, Long userId);

    Integer getRankingByUserId(MarketingActivity activity,Long userId);
    /**
     * 获取履约排名列表
     * @param userId
     * @return
     */
    ConventionActivityRanking getOrderRanking(Long userId,MarketingActivity activity);

    /**
     * @description 更新履约活动中奖信息
     * <AUTHOR>
     * @date 2022/7/15 11:44
     * @param userId
     * @param activityId
     * @param receiveFlag
     * @return com.tyt.model.TytConventionActivity
     */
    Integer updateAwardInfo(Long userId, Long activityId, Integer receiveFlag);


    /**
     * @description 更新选择的目标奖品信息
     * <AUTHOR>
     * @date 2022/8/5 10:16
     * @param userId
     * @param activityId
     * @param targetPrize
     * @return java.lang.Integer
     */
    Integer updateTargetPrize(Long userId, Long activityId, Integer targetPrize);

    /**
     * <AUTHOR> Lion
     * @Description 查询活动履约运单
     * @Param [activityId, userId]
     * @return java.util.List<com.tyt.acvitity.bean.TransportOrders>
     * @Date 2022/8/4 17:40
     */
    List<TransportOrders> getOrdersInfo(Long id, Long userId);

    /**
     * @Description 查询活动单单奖 -- 更换新表 -- 2023.04.24
     * @Param [activityId, userId]
     */
    List<TransportOrders> getOrdersInfoRisk(Long id, Long userId,MarketingActivity marketingActivity);

    List<TransportOrders> getOrdersExposure(Long id, Long userId);

    /**
     * @Description 查询活动冲单奖 -- 更换新表 -- 2023.04.24
     * @Param [activityId, userId]
     */
    List<TransportOrders> getOrdersExposureRisk(Long id, Long userId);

    Integer getTargetByuserId(Long id, Long userId);

    List<ConventionRankingList> getRanking(Long userId, MarketingActivity activity);

    Integer getActivityExposureCount(Long userId, Long activityId);

    Integer getActivityCountGoods(Long userId, Long activityId);

    Integer updateUserStatus(Long activityId, Long userId,Integer isJoin);

    List<TransportOrders> getAllOrdersListInfo(Long userId, Integer type,Long queryID,Integer queryActionType,Integer classify);

    List<TransportOrders> getNewOrdersInfo(Long activityId, Long userId, Date startTime, Date endTime);

    List<TransportOrders> getNewCarOrdersInfo(Long activityId, Long userId, Date startTime, Date endTime);

    List<TransportOrders> getNewGoodsOrdersInfo(Long userId, Date startTime, Date endTime);
}
