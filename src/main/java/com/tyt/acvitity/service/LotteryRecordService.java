package com.tyt.acvitity.service;


import com.tyt.acvitity.bean.LotteryRecordReq;
import com.tyt.base.service.BaseService;
import com.tyt.model.LotteryRecord;
import com.tyt.model.ResultMsgBean;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
public interface LotteryRecordService extends BaseService<LotteryRecord, Long> {

    /**
     * 通过用户id和活动id查询活动详情和用户最近一次的获奖记录
     * @param userId
     * @param activityId
     * @return
     */
    ResultMsgBean getActivityDetail(Long userId, Long activityId);

    /**
     * 定向用户抽奖
     * @param userId
     * @param activityId
     * @return
     */
    ResultMsgBean activityLottery(Long userId, Long activityId,Long agreementActivityId);

    /**
     * 查询用户获奖记录
     * @param userId
     * @param activityId
     * @return
     */
    ResultMsgBean getLotteryRecord(LotteryRecordReq param);
}
