package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.bean.VipLotteryDrawResp;
import com.tyt.acvitity.service.ConventionActivityService;
import com.tyt.acvitity.service.VipLotteryDrawListService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.PromoActivity;
import com.tyt.model.PromoGiftCoupon;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.VipLotteryDrawList;
import com.tyt.promo.model.UserCoupon;
import com.tyt.promo.service.ICouponService;
import com.tyt.promo.service.PromoActivityService;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service("vipLotteryDrawListService")
public class VipLotteryDrawListServiceImpl extends BaseServiceImpl<VipLotteryDrawList,Long> implements VipLotteryDrawListService {

    @Override
    @Resource(name = "vipLotteryDrawListDao")
    public void setBaseDao(BaseDao<VipLotteryDrawList, Long> dao) {
        super.setBaseDao(dao);
    }

    @Resource(name = "promoActivityService")
    private PromoActivityService promoActivityService;
    @Resource(name = "couponService")
    private ICouponService couponService;
    @Autowired
    private ConventionActivityService conventionActivityService;

    @Override
    public ResultMsgBean updateLotteryDraw(Long userId,Long activityId, ResultMsgBean msgBean) throws Exception {
        VipLotteryDrawList vipLotteryDraw = getByUserId(userId,activityId);
        if (vipLotteryDraw == null){
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("该用户不在活动名单内!");
            return msgBean;
        }
        if (vipLotteryDraw.getIsGrant() == 2){
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("您已抽过了，每人仅限一次哦~");
            return msgBean;
        }
        //发优惠券
        promoActivityService.sendCouponByActivityIdMQ(userId,vipLotteryDraw.getPromoActivityId());
        //修改活动用户奖励发放状态
        updateGrantStatus(userId,activityId);
        //修改活动弹窗状态
        conventionActivityService.updateUserStatus(activityId,userId,2);
        //拼接返回数据
        VipLotteryDrawResp vipLotteryDrawResp = getDrawResp(vipLotteryDraw);
        msgBean.setData(vipLotteryDrawResp);
        return msgBean;
    }

    @Override
    public ResultMsgBean getMyLotteryPrize(Long userId, Long activityId, ResultMsgBean msgBean) throws Exception{
        VipLotteryDrawList vipLotteryDraw = getByUserId(userId,activityId);
        if (vipLotteryDraw == null){
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("该用户不在活动名单内!");
            return msgBean;
        }
        //拼接返回数据
        VipLotteryDrawResp vipLotteryDrawResp = getDrawResp(vipLotteryDraw);
        msgBean.setData(vipLotteryDrawResp);
        return msgBean;
    }

    /**
     * 根据用户id获取
     * @param userId
     * @param activityId
     * @return
     */
    private VipLotteryDrawList getByUserId(Long userId,Long activityId){
        String sql = "from VipLotteryDrawList where userId=? and activityId=?";
        List<VipLotteryDrawList> list= this.getBaseDao().find(sql,userId, activityId);
        if(CollectionUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    private void updateGrantStatus(Long userId, Long activityId){
        String sql = "UPDATE vip_lottery_draw_list SET is_grant=2,grant_time=NOW() WHERE user_id=? AND activity_id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{userId,activityId});
    }

    private VipLotteryDrawResp getDrawResp(VipLotteryDrawList vipLotteryDraw) throws Exception{
        VipLotteryDrawResp resp = new VipLotteryDrawResp();
        resp.setUserId(vipLotteryDraw.getUserId());
        resp.setCouponAmount(vipLotteryDraw.getCouponAmount());
        Integer promoActivityId = vipLotteryDraw.getPromoActivityId();
        //根据活动id获取礼包优惠券列表
        PromoActivity promoActivity = promoActivityService.getById(promoActivityId);
        //根据优惠券id获取优惠券信息
        List<UserCoupon> coupons = couponService.getByActivity(promoActivity);
        //根据优惠券信息获取优惠券到期时间
        if (CollectionUtils.isNotEmpty(coupons)){
            Date startTime = vipLotteryDraw.getGrantTime() == null?new Date():vipLotteryDraw.getGrantTime();
            for (UserCoupon coupon : coupons) {
                coupon.setValidDateBegin(startTime);
                coupon.setValidDateEnd(TimeUtil.addDay(startTime,coupon.getValidDays()));
            }
            resp.setCoupons(coupons);
        }
        return resp;
    }


}
