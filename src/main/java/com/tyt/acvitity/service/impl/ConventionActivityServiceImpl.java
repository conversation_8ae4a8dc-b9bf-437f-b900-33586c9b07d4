package com.tyt.acvitity.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.tyt.acvitity.bean.*;
import com.tyt.acvitity.service.ConventionActivityService;
import com.tyt.acvitity.service.StimulateActivityService;
import com.tyt.acvitity.service.TransportOrdersRiskService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.*;
import com.tyt.user.service.PublicResourceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytSourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ConventionActivityServiceImpl implements ConventionActivityService {
    @Autowired
    private MarketingActivityMapper marketingActivityMapper;
    @Autowired
    private MarketingActivityUserMapper marketingActivityUserMapper;
    @Autowired
    private ConventionActivityMapper conventionActivityMapper;
    @Autowired
    private PublicResourceService publicResourceService;

    @Autowired
    private ActivityPrizeMapper activityPrizeMapper;

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private ActivityUserAddressMapper activityUserAddressMapper;
    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private TransportOrdersRiskService transportOrdersRiskService;

    @Resource(name = "tytSourceService")
    private TytSourceService sourceService;


    @Override
    public List<MarketingActivityUser> getActivityInfoByUserId(Long activityId, Long userId) {
        return marketingActivityUserMapper.getByActivityId(activityId,userId);
    }

    @Override
    public List<MarketingActivityUser> getEquitiesByUserId(Long activityId, Long userId,Integer isJoin) {
        return marketingActivityUserMapper.getEquitiesByUserId(activityId,userId,isJoin);
    }

    @Override
    public MarketingActivity getEffectiveActivity(Integer activityType) {
        return marketingActivityMapper.selectByActivityType(activityType);
    }

    @Override
    public Long getOrderNumByUserId(Long activityId,Long userId) {
        return conventionActivityMapper.selectOrderNum(activityId,userId);
    }

    @Override
    public ActivityVo getConventionActivityInfo(Long activityId, Long userId) {
        ActivityVo vo = conventionActivityMapper.selectConventionActivityInfo(activityId,userId);
        Integer num = transportOrdersService.getCountNum(activityId,userId);
        if(null != vo){
            vo.setNum(num);
        }
        return vo;
    }

    @Override
    public Integer getRankingByUserId(MarketingActivity activity,Long userId) {
        ConventionActivityRanking orderRanking = getOrderRanking(userId, activity);
        if (Objects.nonNull(orderRanking) && StringUtils.isNotEmpty(orderRanking.getRank())) {
            return Integer.parseInt(orderRanking.getRank());
        }
        return 0;
    }

    @Override
    public ConventionActivityRanking getOrderRanking(Long userId,MarketingActivity activity) {
//        Integer activityType = tytConfigService.getIntValue("convention_activity_type", 4);
//        Integer activityScope = tytConfigService.getIntValue("convention_activity_scope", 2);
        // 查询活动信息
//        MarketingActivity effectiveActivity = getEffectiveActivity(activityType, activityScope);
//        MarketingActivity effectiveActivity = marketingActivityMapper.selectById(activityId);
        //履约活动最小上榜单数为 默认30
        int orderNumMin = 30;
        PublicResource orderNumMinResource = publicResourceService.getByKey("ranking_order_num_min");
        if (Objects.nonNull(orderNumMinResource)) {
            orderNumMin = Integer.parseInt(orderNumMinResource.getValue());
        }
        log.info("履约活动最小上榜单数为:orderNumMin={}", orderNumMin);
        //最大查询条数
        PublicResource maxLimitNumResource = publicResourceService.getByKey("ranking_max_limit_num");
        //默认200
        int maxLimitNum = 200;
        if (Objects.nonNull(maxLimitNumResource)) {
            maxLimitNum = Integer.parseInt(maxLimitNumResource.getValue());
        }
        log.info("履约活动最大查询条数为:maxLimitNum={}", maxLimitNum);
        //根据活动信息查询排名信息
        List<ConventionActivityRankingList> conventionActivityRankingLists = null;
        //全部用户
        if (activity.getActivityScope() == 1) {
            conventionActivityRankingLists = conventionActivityMapper.selectRankingListByAllUser(activity.getId(), orderNumMin, maxLimitNum);
        }
        //定向用户
        if (activity.getActivityScope() == 2) {
            conventionActivityRankingLists = conventionActivityMapper.selectRankingList(activity.getId(), orderNumMin, maxLimitNum);
        }
        if (CollectionUtils.isEmpty(conventionActivityRankingLists)) {
            return null;
        }
//        List<ConventionActivityRankingList> conventionActivityRankingLists = conventionActivityMapper.selectRankingList(activityId, orderNumMin);
        //我的排名
        String myRank = "";
        //我的订单数
        Long orderNum = 0L;
        //我的奖励
        Integer prize = null;
        //展示奖励还是订单数 0:显示订单数 1:显示奖励
        Integer isShowPrize = 0;

        List<String> ids = new ArrayList<>();
        //找出我的排名
        for (ConventionActivityRankingList conventionActivityRanking : conventionActivityRankingLists) {
            ids.add(conventionActivityRanking.getPrize().toString());
            if (conventionActivityRanking.getUserId().equals(userId)) {
                myRank = String.valueOf(conventionActivityRanking.getRank());
                orderNum = conventionActivityRanking.getOrderNum();
                prize = conventionActivityRanking.getPrize();
                break;
            }
        }
        //查询活动奖品
        if(null != ids && ids.size() > 0){
            List<ActivityPrizeVo> prizes = activityPrizeMapper.getByIds(ids);
            Map<Integer,String> map = new HashMap<>();
            if(null != prizes && prizes.size() > 0){
                for(ActivityPrizeVo vo : prizes){
                    map.put(vo.getId().intValue(),vo.getPrizeName());
                }
            }
            for (ConventionActivityRankingList conventionActivityRanking : conventionActivityRankingLists) {
                conventionActivityRanking.setPrizeName(map.get(conventionActivityRanking.getPrize()));
            }

        }
        PublicResource isShowPrizeResource = publicResourceService.getByKey("is_show_prize");
        if (Objects.nonNull(isShowPrizeResource)) {
            isShowPrize = Integer.valueOf(isShowPrizeResource.getValue());
        }
        log.info("履约活动 isShowPrize = {} ", isShowPrize);
        int rankingPageSize = 200;
        PublicResource rankingPageSizeResource = publicResourceService.getByKey("ranking_page_size");
        if (Objects.nonNull(rankingPageSizeResource)) {
            rankingPageSize = Integer.parseInt(rankingPageSizeResource.getValue());
        }
        log.info("履约活动 排行列表显示条数为={}", rankingPageSize);
        return new ConventionActivityRanking(myRank, orderNum, isShowPrize, prize, conventionActivityRankingLists.size() >= rankingPageSize ? conventionActivityRankingLists.subList(0, rankingPageSize) : conventionActivityRankingLists);
    }

    @Override
    public List<ConventionRankingList> getRanking(Long userId, MarketingActivity activity){
        //根据活动信息查询排名信息
        List<ConventionRankingList> conventionActivityRankingLists = null;
        //全部用户
        if (activity.getActivityScope() == 1) {
            conventionActivityRankingLists = conventionActivityMapper.selectRankingAllUser(activity.getId());
        }
        //定向用户
        if (activity.getActivityScope() == 2) {
            conventionActivityRankingLists = conventionActivityMapper.selectRanking(activity.getId());
        }
        return conventionActivityRankingLists;
    }

    /**
     * @description 更新履约活动中奖信息
     * <AUTHOR>
     * @date 2022/7/15 11:46
     * @param userId
     * @param activityId
     * @param receiveFlag
     * @return com.tyt.model.TytConventionActivity
     */
    @Override
    public Integer updateAwardInfo(Long userId, Long activityId, Integer receiveFlag) {
        //更新履约活动中奖信息
        Integer result = conventionActivityMapper.updateAwardInfo(userId, activityId, receiveFlag);
        return result;
    }

    /**
     * @description 更新选择的目标奖品信息
     * <AUTHOR>
     * @date 2022/8/5 10:16
     * @param userId
     * @param activityId
     * @param targetPrize
     * @return java.lang.Integer
     */
    @Override
    public Integer updateTargetPrize(Long userId, Long activityId, Integer targetPrize) {
        //更新选择的目标奖品信息
        Integer result = conventionActivityMapper.updateTargetPrize(userId, activityId, targetPrize);
        return result;
    }

    /**
      * <AUTHOR> Lion
      * @Description 查询活动履约运单
      * @Param [activityId, userId]
      * @return java.util.List<com.tyt.acvitity.bean.TransportOrders>
      * @Date 2022/8/4 17:40
      */
    @Override
    public List<TransportOrders> getOrdersInfo(Long activityId, Long userId) {
        List<TransportOrders> orders = marketingActivityUserMapper.selectTransportOrdersList(activityId,userId);
        MarketingActivity activity = marketingActivityMapper.selectById(activityId);
        List<TransportOrders> orderLists = transportOrdersService.selectOrdersByActivityIdAndUserId(activity,userId);
        orders.addAll(orderLists);
        return orders;
    }

    /**
     * @Description 查询活动单单奖 -- 更换新表 -- 2023.04.24
     * @Param [activityId, userId]
     */
    @Override
    public List<TransportOrders> getOrdersInfoRisk(Long activityId, Long userId,MarketingActivity marketingActivity) {
        Date startTime = marketingActivity.getStartTime();
        Date endTime = marketingActivity.getEndTime();
        List<TransportOrders> orders = marketingActivityUserMapper.selectTransportOrdersRiskList(userId,startTime,endTime);
        MarketingActivity activity = marketingActivityMapper.selectById(activityId);
        List<TransportOrders> orderLists = transportOrdersService.selectOrdersByActivityIdAndUserId(activity,userId);
        orders.addAll(orderLists);
        return orders;
    }

    @Override
    public List<TransportOrders> getOrdersExposure(Long activityId, Long userId) {
        //全员活动的当前轮次及开始时间、结束时间
        String activityJson = tytConfigService.getStringValue("TYT:ACTIVITY:CONVENTION:ROUNDTIME:JSON");
        ActivityExposureVo activityExposureVo = exposureTime(activityJson);
        Date startTime = activityExposureVo.getStartTime() != null ? activityExposureVo.getStartTime():null;
        Date endTime = activityExposureVo.getEndTime() != null ? activityExposureVo.getEndTime():null;
        List<TransportOrders> orders = marketingActivityUserMapper.selectTransportOrdersExposureList(activityId,userId,startTime,endTime);
        return orders;
    }

    /**
     * @Description 查询活动冲单奖 -- 更换新表 -- 2023.04.24
     * @Param [activityId, userId]
     */
    @Override
    public List<TransportOrders> getOrdersExposureRisk(Long activityId, Long userId) {
        //全员活动的当前轮次及开始时间、结束时间
        String activityJson = tytConfigService.getStringValue("TYT:ACTIVITY:CONVENTION:ROUNDTIME:JSON");
        ActivityExposureVo activityExposureVo = exposureTime(activityJson);
        Date startTime = activityExposureVo.getStartTime() != null ? activityExposureVo.getStartTime():null;
        Date endTime = activityExposureVo.getEndTime() != null ? activityExposureVo.getEndTime():null;
        List<TransportOrders> orders = marketingActivityUserMapper.selectTransportOrdersExposureRiskList(userId,startTime,endTime);
        return orders;
    }

    @Override
    public Integer getTargetByuserId(Long id, Long userId){
        return conventionActivityMapper.getTargetByuserId(id,userId);
    }

    @Override
    public Integer getActivityExposureCount(Long userId, Long activityId){
        return activityUserAddressMapper.selectActivityExposureCount(userId,activityId);
    }

    @Override
    public  Integer getActivityCountGoods(Long userId, Long activityId){
        return activityUserAddressMapper.getActivityCountGoods(userId,activityId);
    }

    public ActivityExposureVo exposureTime (String activityJson) {
        ActivityExposureVo activityExposureVo = new ActivityExposureVo();
        JSONArray jsonArray = JSONArray.parseArray(activityJson);
        List<ActivityJsonBean> activityJsonList = JSONArray.parseArray(jsonArray.toJSONString(), ActivityJsonBean.class);
        if (activityJsonList !=null && activityJsonList.size() >0){
            for (ActivityJsonBean activityJsonBean : activityJsonList) {
                if (activityJsonBean.getStartTime().before(new Date()) && activityJsonBean.getEndTime().after(new Date())){
                    activityExposureVo.setStartTime(activityJsonBean.getStartTime());
                    activityExposureVo.setEndTime(activityJsonBean.getEndTime());
                }
            }
        }
        return activityExposureVo;
    }

    @Override
    public Integer updateUserStatus(Long activityId, Long userId,Integer isJoin){
       return marketingActivityUserMapper.updateUserStatus(activityId,userId,isJoin);
    }

    /**
     * 根据分类查询全量订单
     * @param userId
     * @param type
     * @return
     */
    public List<TransportOrders> getAllOrdersListInfo(Long userId, Integer type,Long queryId,Integer queryActionType,Integer classify){
        List<TransportOrders> ordersInfoList = transportOrdersRiskService.getGoodOrderList(userId,type,queryId,queryActionType,classify);
        if(null !=ordersInfoList && ordersInfoList.size() > 0 ){
            List<TytSource> sourceList = sourceService.getList(" groupCode='reject_type' AND status=0", new PageBean(1,10));
            Map<String, String> collect = null;
            if (sourceList!=null&&sourceList.size()>0) {
                collect = sourceList.stream().collect(Collectors.toMap(TytSource::getValue, TytSource::getName, (k1, k2) -> k1));
            }
            for(TransportOrders order : ordersInfoList){
                //货申诉，车未申诉
                if(order.getActivityStatus() == 1){
                    if(order.getAppealStatus() == 1){
                        if( order.getBeforeType() == 1 && order.getGoodStatus() == 1 && order.getCarStatus() == 0){
                            order.setRemark("核实中，请您耐心等待");
                            //车提示
                            order.setContent("该订单货方已提交审核，请等待审核结果");
                        }
                        //车申诉，货未申诉
                        if(order.getBeforeType() == 2 && order.getGoodStatus() == 0 && order.getCarStatus() == 1){
                            order.setRemark("该订单车方已提交审核，请等待审核结果");
                            //车提示
                            order.setContent("核实中，请您耐心等待");
                        }
                        //当前申诉用户展示失败信息：货提交失败之后车提交
                        if(order.getGoodStatus() == 1 && order.getBeforeType() == 2 && order.getCarStatus() ==1){
                            //货失败提示
                            order.setRemark(order.getRemark());
                            //车提示
                            order.setContent("核实中，请您耐心等待");
                        }
                        //当前申诉用户展示失败信息：车提交失败之后货提交
                        if(order.getCarStatus() == 1 && order.getBeforeType() == 1 && order.getGoodStatus() ==1){
                            //车失败提示
                            order.setContent(order.getRemark());
                            //货提交的
                            order.setRemark("核实中，请您耐心等待");
                        }
                    } else if (order.getAppealStatus() == 2){
                        //申诉失败,货提交，车未提交
                        if(order.getGoodStatus() ==1 && order.getCarStatus() ==0){
                            order.setRemark(order.getRemark());
                            order.setContent(null);
                        }
                        //货未提交，车提交
                        if(order.getGoodStatus() ==0 && order.getCarStatus() ==1){
                            order.setContent(order.getRemark());
                            order.setRemark(null);
                        }
                    }
                }
                if(order.getActivityStatus() == 2){
                    order.setContent(order.getRemark());
                }
                if(order.getActivityStatus() == 3){
                    order.setRemark("审核通过，已解除封控");
                }

                if (order.getRejectType()!=null) {
                    String rejectName = collect.get(String.valueOf(order.getRejectType()));
                    order.setRejectReason(rejectName);
                }
            }
        }
       return ordersInfoList;

    }


    /**
     * 2023.5.1启用新活动订单   货
     * @param activityId
     * @param userId
     * @return
     */
    @Override
    public List<TransportOrders> getNewOrdersInfo(Long activityId, Long userId, Date startTime, Date endTime){
        List<TransportOrders> orders = marketingActivityUserMapper.selectNewTransportOrdersList(activityId,userId,startTime,endTime);
        return orders;
    }

    /**
     * 车履约订单
     * @param activityId
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<TransportOrders> getNewCarOrdersInfo(Long activityId, Long userId, Date startTime, Date endTime){
        List<TransportOrders> orders = marketingActivityUserMapper.selectNewCarTransportOrdersList(activityId,userId,startTime,endTime);
        return orders;
    }

    @Override
    public List<TransportOrders> getNewGoodsOrdersInfo(Long userId, Date startTime, Date endTime){
        List<TransportOrders> orders = marketingActivityUserMapper.getNewGoodsOrdersInfo(userId,startTime,endTime);
        return orders;
    }
}
