package com.tyt.acvitity.service.impl;


import com.tyt.acvitity.service.LotteryUserService;
import com.tyt.acvitity.service.ProdCouponService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.LotteryUser;
import com.tyt.model.ProbCoupon;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Service("prodCouponService")
public class ProdCouponServiceImpl extends BaseServiceImpl<ProbCoupon, Long> implements ProdCouponService {
    @Override
    @Resource(name = "prodCouponDao")
    public void setBaseDao(BaseDao<ProbCoupon, Long> dao) {
        super.setBaseDao(dao);
    }
}
