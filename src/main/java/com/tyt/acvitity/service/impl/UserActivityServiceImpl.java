package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.bean.UserActivityVo;
import com.tyt.acvitity.service.UserActivityService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.User;
import com.tyt.model.UserActivity;
import com.tyt.model.UserPermission;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/07/23 11:05
 */
@Service("userActivityService")
public class UserActivityServiceImpl extends BaseServiceImpl<UserActivity, Long> implements UserActivityService {


    @Resource(name = "userActivityDao")
    @Override
    public void setBaseDao(BaseDao<UserActivity, Long> dao) {
        super.setBaseDao(dao);
    }

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name = "userService")
    private UserService userService;

    @Override
    public List<UserActivityVo> list(List<Long> activityIds, Long userId) {

        String sql = "from UserActivity where activityId in (" + activityIds.stream().map(String::valueOf).collect(Collectors.joining(","))
                + ") and userId = ?";


        final List<UserActivityVo> activityVos = this.getBaseDao().find(sql, userId).stream()
                .peek(ua -> activityIds.remove(ua.getActivityId()))
                .filter(ua -> ua.getActivityStatus() != 0)
                .map(ua -> {
                    final UserActivityVo vo = new UserActivityVo();
                    vo.setActivityId(ua.getActivityId());
                    vo.setActivityCtime(ua.getActivityCtime());
                    return vo;
                }).collect(Collectors.toList());

        //回填未参与的活动
        if(CollectionUtils.isNotEmpty(activityIds)){
            UserPermission carPermission = userPermissionService.getUserPermission(userId,"100101");
            if (carPermission!=null){
                if (carPermission.getStatus()==1 || TimeUtil.daysBetween(carPermission.getEndTime(),new Date())<30){
                    return activityVos;
                }
            }else{
                User user = userService.getById(userId);
                if (TimeUtil.daysBetween(user.getCtime(),new Date())<30){
                    return activityVos;
                }
            }
            activityVos.addAll(activityIds.stream()
                    .map(id -> {
                        final UserActivityVo vo = new UserActivityVo();
                        vo.setActivityId(id);
                        return vo;
                    }).collect(Collectors.toList()));
        }

        return activityVos;
    }

    @Override
    public UserActivity getByActivityIdAndUserId(Long activityId, Long userId) {
        final List<UserActivity> activityList = this.getBaseDao().find("from UserActivity where activityId = ? and userId = ? and activityStatus=1", activityId, userId);
        if(CollectionUtils.isEmpty(activityList)){
            return null;
        }
        return activityList.get(0);
    }

    @Override
    public List<UserActivity> getByUserId(Long userId) {
        String sql = "select id,user_id userId,activity_id activityId, activity_status activityStatus, activity_ctime activityCtime, ctime from tyt_user_activity where activity_id in (select id from tyt_activity where status = 1 )" +
                " and user_id = ? and activity_status=1";
        final Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("activityId", Hibernate.LONG);
        scalarMap.put("activityStatus", Hibernate.INTEGER);
        scalarMap.put("activityCtime", Hibernate.TIMESTAMP);
        scalarMap.put("ctime", Hibernate.TIMESTAMP);
        return this.getBaseDao().search(sql, scalarMap, UserActivity.class, new Object[]{userId});
    }
}
