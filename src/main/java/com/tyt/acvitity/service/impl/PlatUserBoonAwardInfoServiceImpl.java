package com.tyt.acvitity.service.impl;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.bean.BoonAwardInfoBean;
import com.tyt.acvitity.service.PlatUserBoonAwardInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.message.bean.MqPushAcvitityMessage;
import com.tyt.model.PlatUserBoonAwardInfo;
import com.tyt.model.User;
import com.tyt.successaccount.service.MembersPayRecordService;
import com.tyt.successaccount.service.TytSuccessAccountService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
@Service("platUserBoonAwardInfoService")
public class PlatUserBoonAwardInfoServiceImpl extends BaseServiceImpl< PlatUserBoonAwardInfo, Long> implements PlatUserBoonAwardInfoService {

	@Resource(name = "platUserBoonAwardInfoDao")
	public void setBaseDao(BaseDao<PlatUserBoonAwardInfo, Long> platUserBoonAwardInfoDao) {
		super.setBaseDao(platUserBoonAwardInfoDao);
	}
	@Resource(name = "userService")
	private UserService userService;
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	@Resource(name = "successAccountService")
	private TytSuccessAccountService accountService;
	@Resource(name = "membersPayRecordService")
	private MembersPayRecordService membersPayRecordService;
	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;
	@Override
	public PlatUserBoonAwardInfo getBeanByUserId(Long userId) {
		String sql="SELECT * FROM plat_user_boon_award_info where user_id=? ";
		List<PlatUserBoonAwardInfo> list = this.getBaseDao().queryForList(sql, new Object[] {userId});
		if(list!=null && list.size()>0) {
			return list.get(0);
		}
		return null;
	}
	@Override
	public BoonAwardInfoBean verifyAwardInfo(Long userId) throws Exception {
		User user = userService.getByUserId(userId);
		if(user!=null) {
		PlatUserBoonAwardInfo info =this.getBeanByUserId(userId);
		BoonAwardInfoBean awardInfo=new BoonAwardInfoBean();
		
		//1.判断用户是否是会员或曾经是会员
			if(user.getPayDate()!=null) {
				//2.是会员先判断是否领过奖励
				if(info!=null && info.getAwardDays()>0) {
					//2.1此为是会员已领过奖励的操作(返回会员状态，赠送天数，注册天数，参与过活动1)
					BeanUtils.copyProperties(awardInfo, info);
					awardInfo.setIsParticipateAct(1);//1:参加过/2：未参加过
					awardInfo.setRegisterDays(TimeUtil.daysBetween(user.getCtime(),new Date()));
					return awardInfo;
				}else {
					//2.2此为是会员未领过奖励的操作(返回会员状态，赠送天数，注册天数，未参与过活动1，并实时赠送天数)
					if(info!=null) {//修改操作
						info.setUserState(user.getServeDays()>0? 2:1);//1：非会员 2：会员
						info.setAwardDays(getAwardDays(user.getPayDate(),user.getEndTime()));
						this.update(info);
						BeanUtils.copyProperties(awardInfo, info);
						awardInfo.setIsParticipateAct(2);//1:参加过/2：未参加过
						awardInfo.setRegisterDays(TimeUtil.daysBetween(user.getCtime(),new Date()));
						// 实时增加赠送天数
						addAwardDays(user, info.getAwardDays(), "3周年庆典回馈会员活动", 5);
						// 发送mq push消息给用户
						pushMessage(user,info.getAwardDays(), user.getEndTime());
						
						return awardInfo;
					}else {//新增操作
						PlatUserBoonAwardInfo award=new PlatUserBoonAwardInfo();
						award.setUserId(userId);
						award.setUserState(user.getServeDays()>0? 2:1);//1：非会员 2：会员
						award.setCtime(new Date());
						//赠送天数
						award.setAwardDays(getAwardDays(user.getPayDate(),user.getEndTime()));
						this.add(award);
						BeanUtils.copyProperties(awardInfo, award);
						awardInfo.setIsParticipateAct(2);//1:参加过/2：未参加过
						awardInfo.setRegisterDays(TimeUtil.daysBetween(user.getCtime(),new Date()));
						
						// 实时增加赠送天数
						addAwardDays(user, award.getAwardDays(), "3周年庆典回馈会员活动", 5);
						// 发送mq push消息给用户
						pushMessage(user,award.getAwardDays(), user.getEndTime());
						return awardInfo;
					}
				}
			}else{
				//3.不是会员判断是否是第一次点击
				if(info!=null) {
					//3.1此为不是会员非第一次点击（返回会员状态，赠送天数，注册天数，参与过活动1）
					BeanUtils.copyProperties(awardInfo, info);
					awardInfo.setIsParticipateAct(1);//1:参加过/2：未参加过
					awardInfo.setRegisterDays(TimeUtil.daysBetween(user.getCtime(),new Date()));
					return awardInfo;
				}else {
					//3.2此为不是会员是第一次点击（返回会员状态，赠送天数，注册天数，未与过活动1）
					PlatUserBoonAwardInfo award=new PlatUserBoonAwardInfo();
					award.setUserId(userId);
					award.setCtime(new Date());
					award.setUserState(user.getServeDays()>0? 2:1);
					award.setAwardDays(0);
					this.add(award);
					BeanUtils.copyProperties(awardInfo, award);
					awardInfo.setIsParticipateAct(2);//1:参加过/2：未参加过
					awardInfo.setRegisterDays(TimeUtil.daysBetween(user.getCtime(),new Date()));
					return awardInfo;
				}
			}
			
		}
		return null;
	}
	/**
	 * 计算会员赠送天数
	 * @param payDate 首次付费日期
	 * @param endTime 会员结束日期
	 * @return
	 * @throws ParseException 
	 */
	public Integer getAwardDays(Date payDate,Date endTime) throws ParseException {
		Integer vipDays=0;
		Integer awardDays=0;
		if(endTime.getTime()>new Date().getTime()) {
			//如果会员结束日期大于今天（会员天数=今天-首次付费日期）
			vipDays=TimeUtil.daysBetween(payDate, new Date());
		}else {
			//如果结束日期小于等于今天 （会员天数=结束日期-首次付费日期）
			vipDays=TimeUtil.daysBetween(payDate, endTime);
		}
		if(vipDays<365) {
			awardDays=7;
		}else if(vipDays>=365 && vipDays<730) {
			awardDays=10;
		}else if(vipDays>=730 && vipDays<1095) {
			awardDays=14;
		}else if(vipDays>=1095 && vipDays<1278) {
			awardDays=21;
		}else if(vipDays>=1278) {
			awardDays=30;
		}
		return awardDays;
		
	}
	/**
	 * 用户增加赠送天数操作
	 * @param user  用户bean
	 * @param extralDays  变更天数
	 * @param note  变更备注
	 * @param changeType 变更类型  1.平台缴费变更 2.后台缴费变更  3.异常扣减变更  4.vip延期变更  5.活动加赠变更 6.其他加赠变更
	 */
	public void addAwardDays(User user, Integer extralDays, String note,Integer changeType) {
		
		// 设置tyt_user_service_period表
		userService.updateUserServicePeriod(user.getEndTime(), user.getId() + "", user.getCellPhone(), user.getServeDays() + extralDays);
		// 删除缓存
		cacheService.del(Constant.CACHE_USER_KEY + user.getId());
		// 给用户增加天数
		userService.updateExtralDays(user, extralDays, note);
		user = userService.getById(user.getId());
		// 修改最后一条日期的截止时间
		accountService.updateEndTime(user.getPayNumber(), user.getId(), TimeUtil.formatDateTime(user.getEndTime()));
		// 此处修改完用户表 缴费表后 插入会员缴费流水表
		membersPayRecordService.saveMembersPayRecord(user, null, changeType, extralDays,note);
	} 
	/**
	 * 推送消息 通知栏和列表消息
	 * @param userId 用户id
	 * @param awardDays 赠送天数
	 * @param oldEndTime 旧会员结束日期
	 * @throws Exception 
	 */
	public void pushMessage(User user, Integer awardDays, Date oldEndTime) throws Exception {
		
		MqPushAcvitityMessage message = new MqPushAcvitityMessage();
		String messageSerailNum = SerialNumUtil.generateSeriaNum();
		message.setUserId(user.getId());
		message.setCellPhone(user.getCellPhone());
		message.setTrueName(user.getTrueName());
		message.setRemarks("3周年活动通知");
		message.setTitle("特运通好礼到账！");
		String summary=String.format("恭喜你参加特运通三周年会员活动，获得%s天会员！",awardDays);
		message.setSummary(summary);
//		【特运通送好运】恭喜您获得**天会员！您的会员截止日期从**年**月**日延长至**年**月**日！稍后请重新登录，即可查看会员奖励到账。
		if(user.getServeDays()==0) {//会员到期。
			oldEndTime=new Date();
		}
		Date newEndTime=TimeUtil.addDay(oldEndTime, awardDays);
		String content=String.format("【特运通送好运】恭喜您获得%s天会员！您的会员截止日期从%s延长至%s！稍后请重新登录，即可查看会员奖励到账。",awardDays,TimeUtil.formatDateYear(oldEndTime),TimeUtil.formatDateYear(newEndTime));
		message.setContent(content);
		message.setMessageSerailNum(messageSerailNum);
		message.setMessageType(MqBaseMessageBean.PUSH_ACVITITY_MESSAGE);
		tytMqMessageService.addSaveMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message),message.getMessageType());
		tytMqMessageService.sendMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message),message.getMessageType());
		
		}
	}
