package com.tyt.acvitity.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tyt.acvitity.bean.ConventionOrdersCensusVo;
import com.tyt.acvitity.bean.ConventionRankListVo;
import com.tyt.acvitity.bean.ConventionRankVo;
import com.tyt.acvitity.service.ConventionOrdersCensusService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.model.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.tyt.util.Constant.ACTIVITY_CONVENTION_ROUNDTIME_JSON;

/**
 * 活动统计service
 */
@Service("conventionOrdersCensusService")
public class ConventionOrdersCensusServiceImpl extends BaseServiceImpl<ConventionOrdersCensus, Long> implements ConventionOrdersCensusService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String ACTIVITY_CONVENTION_RANKLIST_USERID_KEY = "activity:convention:ranklist:userid:";

    private static final String ROUND = "round";
    private static final String STARTTIME = "startTime";
    private static final String ENDTIME = "endTime";

    private static final String NO_PRIZE = "未中奖";

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;

    @Resource(name = "conventionOrdersCensusDao")
    @Override
    public void setBaseDao(BaseDao<ConventionOrdersCensus, Long> dao) {
        super.setBaseDao(dao);
    }

    @Override
    public ConventionRankListVo getCurrentOrderNumAndRankList(Long userId,Long activityId) {
        ConventionRankListVo result = RedisUtil.getObject(ACTIVITY_CONVENTION_RANKLIST_USERID_KEY + userId+activityId);
        if (null==result) {
            result = getCurrentRoundTime(activityId);
            if (null!=result) {
                getCurrentRank(result,userId,activityId);
                getRankList(result,activityId);
                RedisUtil.setObject(ACTIVITY_CONVENTION_RANKLIST_USERID_KEY + userId+activityId, result, 60);
            }
        }
        return result;
    }

    @Override
    public List<ConventionOrdersCensusVo> getPrizeLog(Long userId,Long activityId) {
        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (activity == null || activity.getStatus()==2){
            return null;
        }
        List<ConventionOrdersCensusVo> result = new ArrayList<>();
        String sql = "SELECT c.round_times roundTimes,c.final_rank finalRank,c.qualify_goods_name qualifyGoodsName,c.rank_goods_name rankGoodsName FROM tyt_convention_orders_census c WHERE 1=1 AND c.user_id = :userId AND c.`status` = 2 AND c.activity_id = :activityId";
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("activityId",activityId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
        scalarMap.put("roundTimes", Hibernate.INTEGER);
        scalarMap.put("finalRank", Hibernate.INTEGER);
        scalarMap.put("qualifyGoodsName", Hibernate.STRING);
        scalarMap.put("rankGoodsName", Hibernate.STRING);

        List<ConventionOrdersCensusVo> search = this.getBaseDao().search(sql, scalarMap, ConventionOrdersCensusVo.class, params);
        int maxRoundTimes = 0;
        if (activity.getEndTime().before(new Date())){
            maxRoundTimes = getMaxRound(activityId);
        }else{
            String roundTimeConfig = tytConfigService.getStringValue(ACTIVITY_CONVENTION_ROUNDTIME_JSON, "");
            JSONArray roundArray = JSONArray.parseArray(roundTimeConfig);
            roundArray.sort(Comparator.comparing(st ->((JSONObject)st).getString(ROUND)));
            for (int i=1;i<=roundArray.size();i++) {
                JSONObject r = roundArray.getJSONObject(i-1);
                Date endTime = r.getDate(ENDTIME);
                if (endTime.after(new Date())) {
                    break;
                }
                maxRoundTimes ++;
            }
        }
        if (maxRoundTimes>0){
            for (int i=1;i<=maxRoundTimes;i++) {
                int round = i;
                List<ConventionOrdersCensusVo> collect = search.stream().filter(c -> c.getRoundTimes() == round).collect(Collectors.toList());
                if (collect.size()>0) {
                    if (StringUtils.isBlank(collect.get(0).getQualifyGoodsName())) {
                        collect.get(0).setQualifyGoodsName(NO_PRIZE);
                    }
                    if (StringUtils.isBlank(collect.get(0).getRankGoodsName())) {
                        collect.get(0).setRankGoodsName(NO_PRIZE);
                    }
                    result.add(collect.get(0));
                } else {
                    ConventionOrdersCensusVo temp = new ConventionOrdersCensusVo();
                    temp.setRoundTimes(i);
                    temp.setFinalRank(null);
                    temp.setQualifyGoodsName(NO_PRIZE);
                    temp.setRankGoodsName(NO_PRIZE);
                    result.add(temp);
                }
            }
            return result;
        }
        return null;
    }

    /**
     * 获取当前时间所在的活动场次数据(如果整个活动时间已结束则返回最后一个活动场次数据)
     *
     * @return
     */
    private ConventionRankListVo getCurrentRoundTime(Long activityId) {
        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (activity == null || activity.getStatus()==2){
            return null;
        }
        ConventionRankListVo result = new ConventionRankListVo();
        if (activity.getEndTime().before(new Date())){
            result.setStatus(1);
            //获取最后轮次
            int maxRoundTimes = getMaxRound(activityId);
            result.setRoundTimes(maxRoundTimes);
            return result;
        }
        String roundTimeConfig = tytConfigService.getStringValue(ACTIVITY_CONVENTION_ROUNDTIME_JSON, "");
        if (StringUtils.isNotBlank(roundTimeConfig)) {
            result.setStatus(0);
            JSONArray roundArray = JSONArray.parseArray(roundTimeConfig);
            roundArray.sort(Comparator.comparing(st ->((JSONObject)st).getString(ROUND)));
            Date now = new Date();
            roundArray.stream().forEach(r -> {
                Date startTime = ((JSONObject) r).getDate(STARTTIME);
                Date endTime = ((JSONObject) r).getDate(ENDTIME);
                if (startTime.before(now) && endTime.after(now)) {
                    result.setRoundTimes(((JSONObject) r).getInteger(ROUND));
                    result.setStartTime(TimeUtil.formatMinDay(startTime));
                    result.setEndTime(TimeUtil.formatMinDay(endTime));
                }
            });
            if (null==result.getRoundTimes()) {
                JSONObject round = roundArray.getJSONObject(roundArray.size() - 1);
                result.setRoundTimes(round.getInteger(ROUND));
                result.setStartTime(TimeUtil.formatMinDay(round.getDate(STARTTIME)));
                result.setEndTime(TimeUtil.formatMinDay(round.getDate(ENDTIME)));
                result.setStatus(1);
            }
            return result;
        }
        return null;
    }

    /**
     * 获取当前排名和单量
     *
     * @return
     */
    private void getCurrentRank(ConventionRankListVo result, Long userId,Long activityId) {
        String sql = "SELECT b.orders_num ordersNum,rank  from (SELECT t.*, @RANK/*'*/:=/*'*/@RANK + 1 AS rank " +
                "FROM (SELECT @RANK/*'*/:=/*'*/0) r, tyt_convention_orders_census AS t WHERE  t.round_times = :roundTimes AND t.activity_id=:activityId  ORDER BY t.orders_num DESC,t.mtime) as b where b.user_id = :userId AND b.round_times = :roundTimes AND b.activity_id=:activityId";
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("roundTimes", result.getRoundTimes());
        params.put("activityId",activityId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
        scalarMap.put("rank", Hibernate.INTEGER);
        scalarMap.put("ordersNum", Hibernate.INTEGER);
        ConventionRankListVo conventionRankListVo = this.getBaseDao().queryByMap(sql, params, ConventionRankListVo.class, scalarMap);
        result.setRank(null==conventionRankListVo?0:conventionRankListVo.getRank());
        result.setOrdersNum(null==conventionRankListVo?0:conventionRankListVo.getOrdersNum());
    }

    /**
     * 获取排行榜列表
     *
     * @return
     */
    private void getRankList(ConventionRankListVo result,Long activityId) {
        String sql = "SELECT a.orders_num ordersNum, CONCAT(LEFT(a.true_name,1),'老板') userName,@RANK/*'*/:=/*'*/@RANK + 1 AS RANK " +
                     "FROM (SELECT c.orders_num,u.true_name FROM tyt_convention_orders_census c " +
                     "LEFT JOIN tyt_user u ON c.user_id = u.id WHERE c.round_times = :roundTimes AND c.activity_id=:activityId ORDER BY c.orders_num DESC,c.mtime ASC LIMIT 10) a,(SELECT @RANK/*'*/:=/*'*/0) t;";
        Map<String, Object> params = new HashMap<>();
        params.put("roundTimes", result.getRoundTimes());
        params.put("activityId",activityId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
        scalarMap.put("rank", Hibernate.INTEGER);
        scalarMap.put("ordersNum", Hibernate.INTEGER);
        scalarMap.put("userName", Hibernate.STRING);
        List<ConventionRankVo> rankList = this.getBaseDao().search(sql,scalarMap,ConventionRankVo.class,params);
        result.setRankList(rankList);
    }

    private Integer getMaxRound(Long activityId){
        String sql = "SELECT round_times FROM `tyt_convention_orders_census` WHERE activity_id = ? ORDER BY id DESC LIMIT 1";
        Integer maxRound = this.getBaseDao().query(sql, new Object[] {activityId});
        if (maxRound == null) {
            return 0;
        }
        return maxRound;
    }
}
