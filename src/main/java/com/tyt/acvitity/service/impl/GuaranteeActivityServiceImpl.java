package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.service.GuaranteeActivityService;
import com.tyt.adposition.service.AdPositionService;
import com.tyt.model.ResultMsgBean;
import com.tyt.transport.service.TransportMainService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class GuaranteeActivityServiceImpl implements GuaranteeActivityService {

    @Autowired
    private AdPositionService adPositionService;

    @Autowired
    private TransportMainService transportMainService;

    @Override
    public ResultMsgBean joinGuaranteeActivity(Long userId) {
        //参加活动修改状态
        if (Objects.isNull(userId)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "userId不能为空");
        }
        adPositionService.joinGuaranteeActivity(userId);
        return ResultMsgBean.successResponse();
    }

    @Override
    public boolean isJoinGuaranteeActivityByUserId(Long userId) {
        Long adId = adPositionService.getGuaranteeActivityId();
        if (adId == null) {
            //保障活动广告已停用或不存在，直接返回false
            return false;
        }
        return adPositionService.getAdPositionUesrDataByAdIdAndUserId(adId, userId);
    }

}
