package com.tyt.acvitity.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.tyt.acvitity.bean.AwardInfo;
import com.tyt.acvitity.bean.CarActivityDetail;
import com.tyt.acvitity.bean.CarActivityReq;
import com.tyt.acvitity.bean.GrowthInfoVo;
import com.tyt.acvitity.constant.CarCreditUpgradePopupTyptEnum;
import com.tyt.acvitity.dao.ProdCouponDao;
import com.tyt.acvitity.helper.LotteryCheckService;
import com.tyt.acvitity.service.*;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.callPhoneRecord.service.CallPhoneRecordService;
import com.tyt.model.*;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.TytCarCreditUpgradePopup;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class CarGrowthSystemServiceImpl implements CarGrowthSystemService {
    @Autowired
    private DrawActivityInfoService drawActivityInfoService;
    @Autowired
    private CarActivityDataService carActivityDataService;
    @Autowired
    private LotteryCheckService lotteryCheckService;
    @Autowired
    private ProdCouponDao prodCouponDao;
    @Autowired
    private TytUserIdentityAuthService userIdentityAuthService;
    @Autowired
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;
    @Autowired
    private TytCarCreditUpgradePopupService tytCarCreditUpgradePopupService;
    @Autowired
    private UserPermissionService userPermissionService;
    @Autowired
    private CsComplaintRecordResultServcie csComplaintRecordResultServcie;
    @Autowired
    private UserService userService;
    @Autowired
    private CallPhoneRecordService callPhoneRecordService;

    @Override
    public CarActivityDetail getUserLuckyDrawInfo(CarActivityReq carActivityReq) {
        CarActivityDetail carActivityDetail = new CarActivityDetail();
        DrawActivityInfo drawActivityInfo = drawActivityInfoService.getById(carActivityReq.getLotteryActivityId());
        //抽奖活动详细信息
        carActivityDetail.setDrawActivityInfo(drawActivityInfo);
        //奖品信息
        List<AwardInfo> awardInfos = prodCouponDao.getAwardInfoByDrawActivityInfoId(carActivityReq.getLotteryActivityId());
        carActivityDetail.setAwardInfos(awardInfos);

        //判断抽奖活动是否过期
        boolean lotteryActivityDate = carActivityDataService.checkLotteryActivityDate(carActivityReq, drawActivityInfo);
        if (!lotteryActivityDate) {
            carActivityDetail.setLotteryActivityIsExpire(1);
        } else {
            carActivityDetail.setLotteryActivityIsExpire(0);
        }

        //今日剩余抽奖机会
        //查询对应的抽奖活动
        lotteryCheckService.setOrderStimulateExecuteType(1);
        ResultMsgBean result = lotteryCheckService.checkLottery(carActivityReq.getUserId(), drawActivityInfo, carActivityReq.getMarketingActivityId());
        if (ResultMsgBean.OK != result.getCode()) {
            carActivityDetail.setAvailableLotteriesNum(0);
        } else {
            carActivityDetail.setAvailableLotteriesNum(1);
        }

        if (ReturnCodeConstant.ACTIVITY_NOT_PERM_NEED_CALL_PUBLISHER == result.getCode()) {
            carActivityDetail.setIsShowNeedCallTab(1);
        } else {
            carActivityDetail.setIsShowNeedCallTab(0);
        }
        return carActivityDetail;
    }

    @Override
    public GrowthInfoVo growthSystemInfo(Long userId) {
        GrowthInfoVo growthInfoVo = new GrowthInfoVo();
        User user = userService.getById(userId);
        growthInfoVo.setUserId(user.getId());
        // 获取认证状态
        growthInfoVo.setCarVerifyFlag("1".equals(user.getIsCar()) ? 1 : 0);
        TytUserIdentityAuth userIdentityAuth = userIdentityAuthService.getByUserId(userId.toString());
        if (userIdentityAuth != null) {
            growthInfoVo.setUserIdentityStatus(userIdentityAuth.getIdentityStatus());
            growthInfoVo.setEnterpriseAuthStatus(userIdentityAuth.getEnterpriseAuthStatus());
        }
        // 获取成长数值
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
        if (userCreditInfo != null) {
            growthInfoVo.setCarCreditRankLevel(StringUtils.isNotBlank(userCreditInfo.getCarCreditRankLevel())?userCreditInfo.getCarCreditRankLevel(): "1");
            growthInfoVo.setCarCreditScore(userCreditInfo.getCarCreditScore());
            growthInfoVo.setCarRankNum(userCreditInfo.getCarRankNum());
            growthInfoVo.setCarLastLevelGap(userCreditInfo.getCarLastLevelGap());
            growthInfoVo.setCarCreditRankLeveType(userCreditInfo.getCarCreditRankLeveType());
        } else {
            // 当数据部门没有同步信用数据时，默认给前端返回成长等级1
            growthInfoVo.setCarCreditRankLevel("1");
        }
        // 判断是否拥有会员权益
        List<UserPermission> userPermissionList = userPermissionService.getPermissionListByUserId(userId);
        if (CollUtil.isNotEmpty(userPermissionList)) {
            for (UserPermission userPermission : userPermissionList) {
                if ((UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode().equals(userPermission.getServicePermissionTypeId())) && userPermission.getStatus() == 1) {
                    growthInfoVo.setHasPermission(1);
                }
            }
        }
        // 判断是否弹框
        TytCarCreditUpgradePopup tytCarCreditUpgradePopup = tytCarCreditUpgradePopupService.getByPopupType(CarCreditUpgradePopupTyptEnum.GROW_LEVEL_UPGRADATION.getType(), userId);
        if (tytCarCreditUpgradePopup == null) {
            growthInfoVo.setIsShowTable(0);
        } else {
            growthInfoVo.setIsShowTable(1);
        }
        // 判断30天内是否有严重客诉
        int wrongCount = csComplaintRecordResultServcie.getCountByWrongReason(user);
        if (wrongCount > 0) {
            growthInfoVo.setIsComplaint(1);
            growthInfoVo.setCarCreditRankLevel("0");
            return growthInfoVo;
        }
        // 判断是否有打过电话，只有在等级类型为3的时候才判断
        if (userCreditInfo != null && userCreditInfo.getCarCreditRankLeveType() != null && userCreditInfo.getCarCreditRankLeveType() == 3) {
            // 判断当月有没有拨打记录
            Date endTime = new Date();
            Date startTime = DateUtil.beginOfMonth(endTime).toJdkDate();
            long callCount = callPhoneRecordService.countByCarUserIdAndTimeBetween(userId, startTime, endTime);
            if (callCount > 0) {
                growthInfoVo.setIsContactedGoods(1);
            } else {
                growthInfoVo.setIsContactedGoods(0);
            }
        }

        return growthInfoVo;
    }

}
