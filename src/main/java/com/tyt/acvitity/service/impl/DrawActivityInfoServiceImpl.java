package com.tyt.acvitity.service.impl;


import com.tyt.acvitity.service.DrawActivityInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.DrawActivityInfo;
import com.tyt.model.PageBean;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Service("drawActivityInfoService")
public class DrawActivityInfoServiceImpl extends BaseServiceImpl<DrawActivityInfo,Long> implements DrawActivityInfoService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    @Resource(name = "drawActivityInfoDao")
    public void setBaseDao(BaseDao<DrawActivityInfo, Long> drawActivityInfoDao) {
        super.setBaseDao(drawActivityInfoDao);
    }
}
