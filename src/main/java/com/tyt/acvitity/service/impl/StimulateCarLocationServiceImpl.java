package com.tyt.acvitity.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.bean.CarLocation;
import com.tyt.acvitity.bean.InfoBean;
import com.tyt.acvitity.bean.StimulateLoctionBean;
import com.tyt.acvitity.dao.StimulateActivityDao;
import com.tyt.acvitity.dao.TransportOrdersRiskDao;
import com.tyt.acvitity.service.StimulateCarLocationService;

import com.tyt.acvitity.service.TransportOrdersRiskService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.file.bean.FileInfo;
import com.tyt.file.service.FileInfoService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.*;

import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.CarLocationUtils;
import com.tyt.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @description 刷单记录服务层实现类
 * <AUTHOR>
 * @date 2022/8/30 9:47
 */
@Service("stimulateCarLocationService")
@Slf4j
public class StimulateCarLocationServiceImpl extends BaseServiceImpl<TytStimulateCarLocation, Long> implements StimulateCarLocationService {

	@Override
	@Resource(name = "stimulateCarLocationDao")
	public void setBaseDao(BaseDao<TytStimulateCarLocation, Long> stimulateCarLocationDao) {
		super.setBaseDao(stimulateCarLocationDao);
	}

	@Autowired
	public StimulateActivityDao stimulateActivityDao;

	@Autowired
	private FileInfoService fileInfoService;

	@Autowired
	private UserService userService;

	@Autowired
	private StimulateCarLocationService stimulateCarLocationService;

	@Autowired
	private TransportOrdersRiskDao transportOrdersRiskDao;

	@Autowired
	private TransportOrdersRiskService transportOrdersRiskService;

	@Resource(name = "threadPoolExecutor")
	private ThreadPoolExecutor threadPoolExecutor;

	@Resource
	private TransportOrdersService transportOrdersService;

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@Autowired
	private TytConfigService tytConfigService;


	/**
	 * 用户刷单订单上传凭证
	 * @param id 刷单id
	 * @param reason 凭证描述
	 * @param userId
	 * @param fileList 凭证图片
	 * @return
	 * @throws Exception
	 */
	@Override
	public ResultMsgBean updateStimulateOrderStatus(Long id, String reason, Long userId,  List<MultipartFile> fileList)throws Exception{
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		StringBuffer sql = new StringBuffer("update tyt_stimulate_activity set appeal_evidence = 1, reason = ? where id=? ");
		int count = stimulateActivityDao.executeUpdateSql(sql.toString(), new Object[]{ reason,id});
		User user = userService.getByUserId(userId);
		//3.将文件信息保存到数据库中

		if(0 < fileList.size()){
			Map map = addUploadFile(user, id, fileList, reason,null);
			String flag = map.get("flag")+"";
			if (flag.equals("false")) {
				resultMsgBean.setCode(500);
				resultMsgBean.setMsg("id为"+ id +"上传凭证图片失败！");
				return resultMsgBean;
			}
			resultMsgBean.setData(map);
		}
		return resultMsgBean;

	}
	private Map addUploadFile(User curUser, Long id, List<MultipartFile> fileList,String reason,Long secondBusinessId) throws Exception {

		//支持多级文件目录，文件目录格式 x/y/z
		String typeName = "shuandan/image";
		String flag = "true";
		Map<String,Object> map = new HashMap<String,Object>();
		int i = 1;
		for (MultipartFile file : fileList) {
			Map fileInfoMap = fileInfoService.uploadFiles(curUser, id,  file, i++, typeName, reason,secondBusinessId);
			if(fileInfoMap == null){
				flag = "false";
			}
			if(fileList.size() == i-1){
				map = fileInfoMap;
			}

		}
		map.put("flag",flag);
		return map;
	}

	@Override
	public StimulateLoctionBean getByStimulateId(Long id){
		String sql = "select id stimulateLoctionId,head_city as headCity,head_no as headNo,tail_city as tailCity,tail_no as tailNo from tyt_stimulate_car_location where stimulate_activity_id = ? and  type = 1 order by ctime desc limit 1";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("headCity", Hibernate.STRING);
		scalarMap.put("headNo", Hibernate.STRING);
		scalarMap.put("tailCity", Hibernate.STRING);
		scalarMap.put("tailNo", Hibernate.STRING);
		scalarMap.put("stimulateLoctionId", Hibernate.LONG);
		List<StimulateLoctionBean> list = this.getBaseDao().search(sql, scalarMap, StimulateLoctionBean.class, new Object[] { id });
		if(null != list && list.size() > 0){
			return list.get(0);
		}
		return null;
	}

	@Override
	public  ResultMsgBean updateStimulate(InfoBean infoBean, List<MultipartFile> fileList) throws Exception{
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);

		User user = userService.getByUserId(infoBean.getUserId());

		TransportOrdersRisk search = transportOrdersRiskService.getByRiskId(infoBean.getId());
		//判断风险单是否已解除刷单嫌疑
		if(search.getAppealStatus() != null && search.getAppealStatus() == 3 && search.getActivityStatus() == 3){
			resultMsgBean.setCode(500);
			resultMsgBean.setMsg("该订单已被审核，无法提交");
			return resultMsgBean;
		}

		//二级业务表主键ID
		Long carLocationId = null;
		//是否需要同步图片

		//车辆信息
		TytStimulateCarLocation location = new TytStimulateCarLocation();
		location.setStimulateActivityId(infoBean.getId().intValue());
		location.setHeadCity(infoBean.getHeadCity());
		location.setHeadNo(infoBean.getHeadNo());
		location.setTailCity(infoBean.getTailCity());
		location.setTailNo(infoBean.getTailNo());
		location.setType(1);
		location.setReason(infoBean.getReason());
		location.setCtime(new Date());
		//未提交申诉
		if( search.getAppealStatus()== null || search.getAppealStatus() == 0 || search.getAppealStatus() == 4){
			//获取最近一次的用户申诉记录
			StimulateLoctionBean oldLoctionBean = stimulateCarLocationService.getByStimulateId(infoBean.getId());

			StringBuffer sql = new StringBuffer();
			//货方
			if(infoBean.getType() == 1){
				 sql = new StringBuffer("update tyt_transport_orders_risk set appeal_status = 1, appeal_evidence = 1, reason = ?,good_status = 1,before_type = 1 where id=? ");
			}else if(infoBean.getType() == 2){
				//车方
				 sql = new StringBuffer("update tyt_transport_orders_risk set appeal_status = 1, appeal_evidence = 1, reason = ?,car_status = 1,before_type = 2 where id=? ");
			}
			transportOrdersRiskDao.executeUpdateSql(sql.toString(), new Object[]{ infoBean.getReason(),infoBean.getId()});
			//添加新的用户申诉信息
			carLocationId = (Long) stimulateCarLocationService.addSave(location);

			if(oldLoctionBean != null){
				Long oldCarLocationId = oldLoctionBean.getStimulateLoctionId();
				//查询用户申诉记录对应的图片列表
				List<FileInfo> files = fileInfoService.getFileInfoList(infoBean.getId(), oldCarLocationId, 3);
				if(files != null && files.size() > 0){
					for (FileInfo fileInfo : files) {
						//过滤有删除标识的图片，重新生成新的图片记录
						if(infoBean.getFileIds() != null && infoBean.getFileIds().size() > 0
						&& infoBean.getFileIds().contains(fileInfo.getId())){
							continue;
						}
						if(Objects.equals(oldCarLocationId, carLocationId)){
							continue;
						}
						//设置新的二级业务Id
						fileInfo.setSecondBusinessId(carLocationId);
						fileInfoService.insertFileInfo(fileInfo);
					}
				}
			}
		} else if (search.getAppealStatus() != null && search.getAppealStatus() == 1) { //申诉中，只有上传申诉的人才可以修改

			//判断修改申诉用户是否为提交申诉的用户
			if(infoBean.getType().equals(search.getBeforeType())){
				//获取最新的用户申诉记录
				StimulateLoctionBean loctionBean = stimulateCarLocationService.getByStimulateId(infoBean.getId());
				if(loctionBean != null){
					carLocationId = loctionBean.getStimulateLoctionId();
				}

				StringBuffer sql  = new StringBuffer("update tyt_stimulate_car_location set  head_city = ?, head_no = ?,tail_city = ?,tail_no = ?,reason = ?,ctime = now() where stimulate_activity_id=? and id = ? and type = 1");
				stimulateCarLocationService.executeUpdateSql(sql.toString(), new Object[]{ infoBean.getHeadCity(),infoBean.getHeadNo(),infoBean.getTailCity(),infoBean.getTailNo(),infoBean.getReason(),infoBean.getId(),carLocationId});
				//货方
				StringBuffer hql = new StringBuffer();
				if(infoBean.getType() == 1){
					hql = new StringBuffer("update tyt_transport_orders_risk set  reason = ? where id=? ");
				}else if(infoBean.getType() == 2){
					//车方
					hql = new StringBuffer("update tyt_transport_orders_risk set  reason = ? where id=? ");
				}
				transportOrdersRiskDao.executeUpdateSql(hql.toString(), new Object[]{ infoBean.getReason(),infoBean.getId()});
			}else if(search.getBeforeType() == 1){
				//货方已提交申诉，车方未刷新提交
				resultMsgBean.setCode(500);
				resultMsgBean.setMsg("该订单货方已提交审核，请等待审核结果");
				return resultMsgBean;
			}
			else if(search.getBeforeType() == 2){
				//车方提交申诉，货方未刷新提交
				resultMsgBean.setCode(500);
				resultMsgBean.setMsg("该订单车方已提交审核，请等待审核结果");
				return resultMsgBean;
			}

			//先删除有标识的图片
			if(null != infoBean.getFileIds() && infoBean.getFileIds().size() > 0){
				fileInfoService.deleteByBusinessIds(infoBean.getFileIds());
			}
		}else if (search.getAppealStatus() != null && search.getAppealStatus() == 2 ) { //申诉失败
			//判断车/货是否都已申诉,无法再次申诉
			if(search.getGoodStatus() == 1 && search.getCarStatus() == 1){
				resultMsgBean.setCode(500);
				resultMsgBean.setMsg("车货双方都已申诉失败，禁止再次申诉");
				return resultMsgBean;
			}

			//判断当前申诉人是否已经申诉过
			if(infoBean.getType().equals(search.getBeforeType())){
				resultMsgBean.setCode(500);
				resultMsgBean.setMsg("申诉失败，无法再次申诉");
				return resultMsgBean;
			}
			//申诉用户不为空而且申诉用户与第一次申诉不为同一方可再次申诉
			if(infoBean.getType() != null && !infoBean.getType().equals(search.getBeforeType())){
				//获取最新的用户申诉记录
				StimulateLoctionBean loctionBean = stimulateCarLocationService.getByStimulateId(infoBean.getId());
				if(loctionBean != null){
					carLocationId = loctionBean.getStimulateLoctionId();
				}

				StringBuffer sql  = new StringBuffer("update tyt_stimulate_car_location set  head_city = ?, head_no = ?,tail_city = ?,tail_no = ?,reason = ?,ctime = now() where stimulate_activity_id=? and id = ? and type = 1");
				stimulateCarLocationService.executeUpdateSql(sql.toString(), new Object[]{ infoBean.getHeadCity(),infoBean.getHeadNo(),infoBean.getTailCity(),infoBean.getTailNo(),infoBean.getReason(),infoBean.getId(),carLocationId});
				StringBuffer hql = new StringBuffer();
				//货方
				if(infoBean.getType() == 1){
					hql = new StringBuffer("update tyt_transport_orders_risk set appeal_status = 1, appeal_evidence = 1, reason = ?,good_status = 1,before_type = 1 where id=? ");
				}else if(infoBean.getType() == 2){
					//车方
					hql = new StringBuffer("update tyt_transport_orders_risk set appeal_status = 1, appeal_evidence = 1, reason = ?,car_status = 1,before_type = 2 where id=? ");
				}
				transportOrdersRiskDao.executeUpdateSql(hql.toString(), new Object[]{ infoBean.getReason(),infoBean.getId()});
			}
			//先删除有标识的图片
			if(null != infoBean.getFileIds() && infoBean.getFileIds().size() > 0){
				fileInfoService.deleteByBusinessIds(infoBean.getFileIds());
			}
		}
		//添加凭证图片
		if(fileList != null && fileList.size() > 0){
			Map map = addUploadFile(user, infoBean.getId(), fileList, infoBean.getReason(), carLocationId);
			String flag = map.get("flag")+"";
			if (flag.equals("false")) {
				resultMsgBean.setCode(500);
				resultMsgBean.setMsg("id为"+ infoBean.getId() +"上传凭证图片失败！");
				return resultMsgBean;
			}
			resultMsgBean.setData(map);
		}
		//V6330 异步触发轨迹判定
		doLocationDetermine(search);
		return resultMsgBean;
	}

	private void doLocationDetermine(TransportOrdersRisk transportOrdersRisk){
		try {
			//线程池处理轨迹判定
			threadPoolExecutor.execute(() -> {
				TytTransportOrders transportOrders = transportOrdersService.getById(transportOrdersRisk.getOrderNumber());
				log.info("异步轨迹判定  transportOrders："+JSON.toJSONString(transportOrders));
				int trackDecide = locationDetermine(transportOrders);
				log.info("异步轨迹判定 判定结果："+ trackDecide);
				String hql = "update tyt_transport_orders_risk set track_decide = ? ,update_time = now() where id=?";
				transportOrdersRiskDao.executeUpdateSql(hql, new Object[]{ trackDecide,transportOrdersRisk.getId()});
			});
		} catch (Exception e){
			log.error("异步轨迹判定 异常：{0}",e);
		}
	}

	/**
	 * 判定行程轨迹是否合规
	 * @param transportOrders
	 * @return 0:无结果;1:符合;2:不符合
	 */
	public int locationDetermine(TytTransportOrders transportOrders) {
		//货源表获取出发点和目的地点
		TransportMain transportMain = transportMainService.getById(transportOrders.getTsId());
		log.info("异步轨迹判定  transportMain："+ JSON.toJSONString(transportMain));
		String startLat = transportMain.getStartLatitude();
		String startLon = transportMain.getStartLongitude();
		String destLat = transportMain.getDestLatitude();
		String destLon = transportMain.getDestLongitude();
		String headCity = transportOrders.getHeadCity();
		String headNo = transportOrders.getHeadNo();
		Date payEndTime = transportOrders.getPayEndTime();
		if (org.apache.commons.lang3.StringUtils.isBlank(headCity)
				|| StringUtils.isBlank(headNo)
				|| StringUtils.isBlank(startLat)
				|| StringUtils.isBlank(startLon)
				|| StringUtils.isBlank(destLat)
				|| StringUtils.isBlank(destLon)
				|| payEndTime == null) return 0;
		try {
			Date startOfDay = DateUtil.startOfDay(DateUtil.addTime(payEndTime, Calendar.DAY_OF_MONTH, -1));
			Date endOfDay = DateUtil.endOfDay(DateUtil.addTime(payEndTime, Calendar.DAY_OF_MONTH, 3));
			log.info("异步轨迹判定 查询轨迹 入参："+ startOfDay.getTime() +"  " +endOfDay.getTime());
			//BI组获取车辆轨迹数据
			List<CarLocation> carLocationList = CarLocationUtils.getCarLocation(headCity, headNo, startOfDay, endOfDay);
			log.info("异步轨迹判定 轨迹数据："+ JSON.toJSONString(carLocationList));
			if (carLocationList==null || !(carLocationList.size()>0)) return 0;
			GlobalCoordinates startCoordinate = new GlobalCoordinates(Double.parseDouble(startLat), Double.parseDouble(startLon));
			GlobalCoordinates destCoordinate = new GlobalCoordinates(Double.parseDouble(destLat), Double.parseDouble(destLon));
			boolean startFlag = false;
			boolean destFlag = false;
			int bias = tytConfigService.getIntValue(Constant.LOCATION_DETERMINE_BIAS_DISTANCE,30);
			//对比坐标点
			for (CarLocation location : carLocationList) {
				GlobalCoordinates locationCoordinate = new GlobalCoordinates(location.getLat(), location.getLon());
				if (!startFlag) {
					double startMeter = getDistanceMeter(startCoordinate, locationCoordinate);
					if (startMeter/1000<bias) startFlag = true;
				}
				if (!destFlag) {
					double destMeter = getDistanceMeter(destCoordinate, locationCoordinate);
					if (destMeter/1000<bias) destFlag = true;
				}
				//出发点和目的地点都包含才算符合
				if (startFlag&&destFlag) return 1;
			}
			if (!(startFlag&&destFlag)) return 2;
		} catch (Exception e) {
			log.error("判定行程轨迹是否合规 异常：{}",e);
			return 0;
		}
		return 0;
	}

	/**
	 * 计算两坐标点距离
	 * @param coordinateFrom
	 * @param coordinateTo
	 * @return 距离，单位:米
	 */
	public static double getDistanceMeter(GlobalCoordinates coordinateFrom, GlobalCoordinates coordinateTo) {
		GeodeticCurve geoCurve = new GeodeticCalculator().calculateGeodeticCurve(Ellipsoid.WGS84, coordinateFrom, coordinateTo);
		return geoCurve.getEllipsoidalDistance();
	}

	@Override
	public void deleteInfo(Long id){
		fileInfoService.deleteByBusinessId(id);
	}
}
