package com.tyt.acvitity.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.tyt.acvitity.bean.UserCenterExposureInfo;
import com.tyt.acvitity.bean.UserExposurePermissionCountVo;
import com.tyt.acvitity.service.ExposurePermissionService;
import com.tyt.common.bean.PageData;
import com.tyt.limituser.service.ExposureBlockService;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.TytSource;
import com.tyt.model.UserPermission;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.UserRecordEnum;
import com.tyt.plat.mapper.base.ExposurePermissionExpiredRecordMapper;
import com.tyt.plat.mapper.base.ExposurePermissionGainRecordMapper;
import com.tyt.plat.mapper.base.ExposurePermissionUsedRecordMapper;
import com.tyt.plat.service.base.TytUserRecordService;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSourceUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service("exposurePermissionService")
public class ExposurePermissionServiceImpl implements ExposurePermissionService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private ExposurePermissionGainRecordMapper exposurePermissionGainRecordMapper;

    @Autowired
    private ExposurePermissionUsedRecordMapper exposurePermissionUsedRecordMapper;

    @Autowired
    private ExposurePermissionExpiredRecordMapper exposurePermissionExpiredRecordMapper;

    @Autowired
    private TytUserRecordService tytUserRecordService;

    @Autowired
    private ExposureBlockService exposureBlockService;

    private void saveExposureRead(Long userId){

        Long maxGainId = exposurePermissionGainRecordMapper.getMaxGainId(userId);

        if(maxGainId != null){
            String exposureReadIdKey = UserRecordEnum.EXPOSURE_READ_ID.getCode();
            tytUserRecordService.saveRecord(userId, exposureReadIdKey, 1, maxGainId + "");
        }
    }

    @Override
    public PageData<ExposurePermissionGainRecord> getExposurePermissionGainRecordListByUserId(Long userId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        LocalDateTime now = LocalDateTime.now();
        String today = now.getYear() + "-" + now.getMonthValue() + "-" + now.getDayOfMonth();
        List<ExposurePermissionGainRecord> exposurePermissionGainRecordListByUserId = exposurePermissionGainRecordMapper.getExposurePermissionGainRecordListByUserId(userId, today);

        PageData<ExposurePermissionGainRecord> exposurePermissionGainRecordPageData = new PageData<>(exposurePermissionGainRecordListByUserId);

        try {
            this.saveExposureRead(userId);
        } catch (Exception e) {
            logger.error("", e);
        }

        return exposurePermissionGainRecordPageData;
    }

    @Override
    public PageData<ExposurePermissionUsedRecord> getExposurePermissionUsedRecordListByUserId(Long userId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        LocalDateTime now = LocalDateTime.now();
        String today = now.getYear() + "-" + now.getMonthValue() + "-" + now.getDayOfMonth();
        List<ExposurePermissionUsedRecord> exposurePermissionUsedRecordListByUserId = exposurePermissionUsedRecordMapper.getExposurePermissionUsedRecordListByUserId(userId, today);
        return new PageData<>(exposurePermissionUsedRecordListByUserId);
    }

    @Override
    public PageData<ExposurePermissionExpiredRecord> getExposurePermissionExpiredRecordListByUserId(Long userId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        LocalDateTime now = LocalDateTime.now();
        String today = now.getYear() + "-" + now.getMonthValue() + "-" + now.getDayOfMonth();
        List<ExposurePermissionExpiredRecord> exposurePermissionExpiredRecordListByUserId = exposurePermissionExpiredRecordMapper.getExposurePermissionExpiredRecordListByUserId(userId, today);
        return new PageData<>(exposurePermissionExpiredRecordListByUserId);

    }

    @Override
    public UserExposurePermissionCountVo getUserExposurePermissionCount(Long userId) {
        UserPermission userPermission = userPermissionService.getPermissionListByUserIdAndServicePermissionTypeId(userId);
        Integer useAbleNum = 0;
        if (userPermission != null && userPermission.getTotalNum() != null) {
            if (userPermission.getUsedNum() != null) {
                useAbleNum = userPermission.getTotalNum() - userPermission.getUsedNum();
            } else {
                useAbleNum =userPermission.getTotalNum();
            }
        }

        if(useAbleNum < 0){
            useAbleNum = 0;
        }
        UserExposurePermissionCountVo userExposurePermissionCountVo = new UserExposurePermissionCountVo();
        userExposurePermissionCountVo.setUserExposurePermissionCount(useAbleNum);

        String exposureReadIdKey = UserRecordEnum.EXPOSURE_READ_ID.getCode();

        TytUserRecord tytUserRecord = tytUserRecordService.getByUserIdAndCode(userId, exposureReadIdKey);

        Long markId = 0L;
        if(tytUserRecord != null){
            String value = tytUserRecord.getValue();
            try {
                markId = Long.parseLong(value);
            } catch (Exception e) {
                logger.error("", e);
            }
        }

        Date nowTime = new Date();

        Date startDate = DateUtil.addTime(nowTime, Calendar.DAY_OF_MONTH, -7);

        int newExposureCount = exposurePermissionGainRecordMapper.getNewExposureCount(userId, markId, startDate);
        userExposurePermissionCountVo.setNewExposureCount(newExposureCount);

        //查询曝光卡是否限制
        TytExposureBlock tytExposureBlock = exposureBlockService.selectByUserId(userId);
        if (ObjectUtil.isNotNull(tytExposureBlock)
                && tytExposureBlock.getBlockStatus() == 1) {
            if (tytExposureBlock.getPermanentBlock()) {
                userExposurePermissionCountVo.setExposureBlockMessage("您因平台管控已被限制使用曝光卡，如有疑问可联系客服");
            } else {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日HH时");
                userExposurePermissionCountVo.setExposureBlockMessage("您因平台管控已被限制使用曝光卡，恢复时间" +
                        simpleDateFormat.format(tytExposureBlock.getBlockEndTime()));
            }
        }
        return userExposurePermissionCountVo;
    }

    /**
     * 保存曝光权益使用记录
     * @param usedRecord
     * @return
     */
    @Override
    public int saveExposurePermissionUsedRecord(ExposurePermissionUsedRecord usedRecord) {

        int result = exposurePermissionUsedRecordMapper.insertSelective(usedRecord);
        return result;
    }

    @Override
    public List<UserCenterExposureInfo> getUserCenterExposureInfo(Long userId) {
        //产品要求 查询近七天
        String days = TimeUtil.minusDaysByYYYMMDD(6);
        //带聚合逻辑
//        List<UserCenterExposureInfo> userCenterExposureInfos = exposurePermissionGainRecordMapper.selectUserCenterExposureInfo(userId, days);
//        Iterator<UserCenterExposureInfo> iterator = userCenterExposureInfos.iterator();
//        int gainType2 = 0;
//        int gainType3 = 0;
//        while (iterator.hasNext()) {
//            UserCenterExposureInfo next = iterator.next();
//            if (next.getGainType() == 2) {
//                gainType2 = next.getExposureNum();
//                iterator.remove();
//            }
//            if (next.getGainType() == 3) {
//                gainType3 = next.getExposureNum();
//                iterator.remove();
//            }
//        }
//
//        if (gainType2 != 0 || gainType3 != 0) {
//            UserCenterExposureInfo userCenterExposureInfo = new UserCenterExposureInfo();
//            userCenterExposureInfo.setGainTypeName("老用户回归");
//            userCenterExposuuserCenterExposureInfos = {ArrayList@10256}  size = 5reInfo.setExposureNum(gainType2 + gainType3);
//            userCenterExposureInfos.add(userCenterExposureInfo);
//        }userCenterExposureInfos = {ArrayList@10256}  size = 5
        List<UserCenterExposureInfo> userCenterExposureInfos = exposurePermissionGainRecordMapper.selectLastExposureInfo(userId, days);
        for (UserCenterExposureInfo userCenterExposureInfo : userCenterExposureInfos) {
            String gainTypeName = "其他";
            if (userCenterExposureInfo.getGainType()!=null){
                TytSource source = TytSourceUtil.getSourceName("exposure_permission_gain_type",userCenterExposureInfo.getGainType()+"");
                if (source!=null){
                    gainTypeName = source.getName();
                }
            }
            userCenterExposureInfo.setGainTypeName(gainTypeName);
        }
        return userCenterExposureInfos;
    }
}
