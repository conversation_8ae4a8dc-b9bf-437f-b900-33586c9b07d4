package com.tyt.acvitity.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.bean.ActivityGradePrizeVo;
import com.tyt.acvitity.bean.ActivityInfo;
import com.tyt.acvitity.bean.ActivityPrizeVo;
import com.tyt.acvitity.service.ActivityGradePrizeService;
import com.tyt.acvitity.service.ActivityPrizeService;
import com.tyt.acvitity.service.ConventionActivityService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.marketingActivity.enums.ActivityTypeEnum;
import com.tyt.model.ActivityGradePrize;
import com.tyt.model.ActivityPrize;
import com.tyt.model.TytUserBuyGoods;
import com.tyt.mybatis.mapper.ActivityGradePrizeMapper;
import com.tyt.mybatis.mapper.ActivityPrizeMapper;
import com.tyt.permission.bean.ExposureRecordInfo;
import com.tyt.permission.bean.MqUserPermissionMsg;
import com.tyt.plat.mapper.base.TytTransportTecServiceFeeMapper;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service("activityGradePrizeService")
@Slf4j
public class ActivityGradePrizeServiceImpl extends BaseServiceImpl<ActivityGradePrize, Long> implements ActivityGradePrizeService {




    @Autowired
    private ActivityGradePrizeMapper activityGradePrizeMapper;

    @Autowired
    private ActivityPrizeMapper activityPrizeMapper;

    //服务器图片路径(旧规则)
    public static final String TYT_SERVER_PICTURE_URL_OLD = "tyt_server_picture_url_old";

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private ConventionActivityService conventionActivityService;

    @Autowired
    private ActivityPrizeService activityPrizeService;

    @Autowired
    private TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    /**
     * 根据活动id和相关等级查询等级对应奖品信息
     * @param id
     * @param userGrade
     * @return
     */
    @Override
    public List<ActivityGradePrizeVo> getByActivityIdAndGrade(Long id, Integer userGrade,Long userId){
        List<ActivityGradePrizeVo> vos = activityGradePrizeMapper.getByActivityIdAndGrade(id,userGrade);

        Integer targetPrize = conventionActivityService.getTargetByuserId(id,userId);
        String code = "";
        if(null != targetPrize){
            code = activityPrizeService.getCode(targetPrize.longValue());

        }

        if(null != vos && vos.size() > 0){
            StringBuffer sb = new StringBuffer();
            for(ActivityGradePrizeVo vo : vos){
                sb.append(vo.getPrize()).append(",");
            }
            String ids = sb.deleteCharAt(sb.length()-1).toString();
            List<String> prizeIds = Arrays.asList(ids.split(","));
            List<ActivityPrizeVo> prizes = activityPrizeMapper.getByIds(prizeIds);
            if(null != prizes && prizes.size() > 0){
                String tytServerPictureUrlOld = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_OLD);
                for(ActivityGradePrizeVo vo : vos){
                    List<ActivityPrizeVo> ps = new ArrayList<>();
                    for(ActivityPrizeVo prize : prizes){
                        if(vo.getPrize().contains(prize.getId().toString())){
                            String url = prize.getUrl();
                            if(StringUtils.isNotBlank(url)){
                                prize.setUrl(tytServerPictureUrlOld+url);
                            }
                            if(StringUtils.isNotBlank(prize.getPicUrl())){
                                prize.setPicUrl(tytServerPictureUrlOld + prize.getPicUrl());
                            }
                            if(StringUtils.isNotBlank(prize.getHeadUrl())){
                                prize.setHeadUrl(tytServerPictureUrlOld + prize.getHeadUrl());
                            }
                            if(StringUtils.isNotBlank(prize.getPrizeUrl())){
                                prize.setPrizeUrl(tytServerPictureUrlOld + prize.getPrizeUrl());
                            }
                            if(StringUtils.isNotBlank(prize.getTopUrl())){
                                prize.setTopUrl(tytServerPictureUrlOld + prize.getTopUrl());
                            }
                            //如果是二选一，只添加code相同的奖品
                            if(StringUtils.isNotBlank(code) && vo.getPrize().contains(",")){
                                if(prize.getCode().equals(code)){
                                    ps.add(prize);
                                }
                            }
                            //不是二选一，直接添加
                            else{
                                ps.add(prize);
                            }

                        }
                    }
                    vo.setActivityPrizeVos(ps);
                }
            }

        }
        return vos;

    }


    public void sendMq(Long userId, String cellPhone, Integer type){
        log.info("裂变活动入参用户id【{}】，手机号【{}】，类型【{}】",userId,cellPhone,type);
        try {
            List<ActivityInfo> activities = tytTransportTecServiceFeeMapper.getActivity(type);
            if(CollectionUtils.isEmpty(activities)){
                return;
            }

            for(ActivityInfo info : activities ){


                //判断有没有绑定关系
                Integer num = tytTransportTecServiceFeeMapper.getActivityRecord(info.getMarketingActivityId(),userId);
                log.info("判断用户有没有绑定关系活动id【{}】，用户id【{}】，绑定【{}】",info.getMarketingActivityId(),userId,num);
                if(0 == num){
                    continue;
                }
                Integer count = 0;

                //司机邀请司机，看用户之前有没有拨打过电话
                if(22 == info.getType()){
                    count = tytTransportTecServiceFeeMapper.getCarUserPhone(userId,info.getTime());
                    //司机邀请货主，看用户之前有没有发过货
                }else if(23 == info.getType()){
                    count =  tytTransportTecServiceFeeMapper.getGoodsUserTransport(userId,info.getTime());

                }
                log.info("该用户在活动前是否有/拨打发货用户id【{}】，活动id【{}】，拨打/发货【{}】",userId,info.getMarketingActivityId(),count);
                //有发过货，不执行
                if(0 < count){
                    continue;
                }

                sendUserPermission2MQ(userId,cellPhone,type);
                break;
            }
        }catch (Exception e){
            log.info("校验用户发货/拨打失败",e);
        }
    }


    public void sendUserPermission2MQ(Long userId,String phone ,Integer type) {

        MqUserPermissionMsg mqMsg = new MqUserPermissionMsg();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(MqBaseMessageBean.INVOTEE_USER_ACTIVITY);
        //用户ID
        mqMsg.setUserId(userId);
        mqMsg.setOrdNum(phone);
        mqMsg.setChangeType(1);
        mqMsg.setGainType(1);

        if(23 == type){
            mqMsg.setGainType(2);
        }

        // 保存发送mq
        final String messageSerailNum = mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        final int messageType = mqMsg.getMessageType();
        log.info("保存发送mq【{}】",mqJson);
        //发送并mq信息并保存到数据库
        // tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, messageType);
        // tytMqMessageService.sendMqMessage(messageSerailNum, mqJson, 0);
        // 发送新TOPIC的消息
        tytMqMessageService.sendMsgCustom(mqJson, "MARKET_CENTER_TOPIC", messageSerailNum, "IUA", 0L);
    }


    /**
     * 查询司机邀货，有没有正在进行中的活动，有的话，
     * 看查询货主是否被绑定，如有判断用户是否已实名认证
     * @param userId
     * @return Boolean
     */
    @Override
    public Boolean getUserAuth(Long userId){
        //根据用户id查询用户是否实名认证
        List<ActivityInfo> activities = tytTransportTecServiceFeeMapper.getActivity(23);
        if(CollectionUtils.isEmpty(activities)){
            return false;
        }
        for(ActivityInfo info : activities ){
            //判断有没有绑定关系
            Integer num = tytTransportTecServiceFeeMapper.getActivityRecord(info.getMarketingActivityId(),userId);
            //有绑定关系，查询有没有实名认证
            if(0 < num){
                Integer status = tytTransportTecServiceFeeMapper.getUserAuth(userId);
                if(!Objects.equals(1, status)){
                    return true;
                }
            }
        }
        return false;
    }
}
