package com.tyt.acvitity.service.impl;



import com.tyt.acvitity.bean.StimulateLoctionBean;
import com.tyt.acvitity.service.StimulateActivityService;

import com.tyt.acvitity.service.StimulateCarLocationService;
import com.tyt.acvitity.service.TransportOrdersRiskService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.file.bean.FileInfo;
import com.tyt.file.service.FileInfoService;
import com.tyt.model.PlatUserBoonAwardInfo;
import com.tyt.model.TransportOrdersRisk;
import com.tyt.model.TytStimulateActivity;

import com.tyt.model.TytStimulateCarLocation;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;


import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("stimulateActivityService")
@Slf4j
public class StimulateActivityServiceImpl extends BaseServiceImpl<TytStimulateActivity, Long>  implements StimulateActivityService {


    @Resource(name = "stimulateActivityDao")
    public void setBaseDao(BaseDao<TytStimulateActivity, Long> stimulateActivityDao) {
        super.setBaseDao(stimulateActivityDao);
    }

    @Autowired
    private com.tyt.acvitity.service.StimulateCarLocationService StimulateCarLocationService;

    @Autowired
    private FileInfoService fileInfoService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private TransportOrdersRiskService transportOrdersRiskService;


    @Override
    public void updateStatus(Integer stimulateActivityId){
        String sql="update tyt_stimulate_activity set appeal_status = 1 where id = ? ";
        this.getBaseDao().executeUpdateSql(sql,new Object[]{stimulateActivityId});

    }

    /**
     * 根据刷单id查询用户申诉详情
     * @param id
     */
    @Override
    public  StimulateLoctionBean getByIdInfo(Long id) {
        //TytStimulateActivity activity = this.getById(id);
        TransportOrdersRisk activity= transportOrdersRiskService.getByRiskId(id);
        if(null != activity){
            StimulateLoctionBean bean = new StimulateLoctionBean();
            bean.setStimulateActivityId(activity.getId());
            bean.setReason(activity.getReason());
            bean.setAppealStatus(activity.getAppealStatus());
            bean.setBeforeType(activity.getBeforeType());
            StimulateLoctionBean location = StimulateCarLocationService.getByStimulateId(id);
            if(null != location){
                bean.setHeadCity(location.getHeadCity());
                bean.setHeadNo(location.getHeadNo());
                bean.setTailCity(location.getTailCity());
                bean.setTailNo(location.getTailNo());
                List<FileInfo> fileInfoList = fileInfoService.getFileInfoList(id, location.getStimulateLoctionId(), 3);
                if(null != fileInfoList && fileInfoList.size() > 0){
                    String path = tytConfigService.getStringValue("prefix_picture");
                    for(FileInfo info : fileInfoList){
                        if(StringUtils.isNotBlank(path)){
                            info.setFilePath(path + info.getFilePath());
                        }
                    }
                    bean.setFiles(fileInfoList);
                }
            }
            return bean;
        }
        return null;
    }

}
