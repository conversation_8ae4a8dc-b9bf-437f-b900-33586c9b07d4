package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.bean.AwardUserBean;
import com.tyt.acvitity.bean.AwardUserCountBean;
import com.tyt.acvitity.bean.InviteUserBean;
import com.tyt.acvitity.dao.PlatUserInviteAwardInfoDao;
import com.tyt.acvitity.service.PlatUserInviteAwardInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.PlatUserInviteAwardInfo;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("platUserInviteAwardInfoService")
public class PlatUserInviteAwardInfoServiceImpl extends BaseServiceImpl<PlatUserInviteAwardInfo, Long> implements PlatUserInviteAwardInfoService {

	@Resource(name = "platUserInviteAwardInfoDao")
	public void setBaseDao(BaseDao<PlatUserInviteAwardInfo, Long> platUserInviteAwardInfoDao) {
		super.setBaseDao(platUserInviteAwardInfoDao);
	}
	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "tytConfigService")
	private TytConfigService configService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;


	@Override
	public void getInviteAwardUserInfo(Long userId, ResultMsgBean result)throws Exception {
//TODO 获取用户信息-
		User user = this.userService.getByUserId(userId);
		if(user == null){
			result.setCode(ReturnCodeConstant.NOT_LOGGED_IN_CODE);
			result.setMsg("未登录");
			return ;
		}
		Map<String,Object> resultMap = new HashMap<>();
		resultMap.put("userName",user.getUserName());
		resultMap.put("userHeadUrl",user.getHeadUrl());
		//TODO 开通人数,获奖总数-
		AwardUserCountBean awardUserCountBean = ((PlatUserInviteAwardInfoDao)getBaseDao()).getAwardUserCountAndAwardDaysByUserId(userId);
		resultMap.put("payPeopleCount",awardUserCountBean.getPeopleNumberSum());
		if(user.getChannel() != null && user.getRenewalYears() != null && user.getRenewalYears() > 0 && user.getChannel().intValue() == 5209 && user.getUserType().intValue() == 1 ){
			if(awardUserCountBean.getAwardDaysSum() == null){
				resultMap.put("awardDaysSum",3);
			}else{
				Integer awardSum = Integer.valueOf(awardUserCountBean.getAwardDaysSum());
				resultMap.put("awardDaysSum",(awardSum+3));
			}
		}else{
			resultMap.put("awardDaysSum",awardUserCountBean.getAwardDaysSum());
		}
		//TODO 总奖励人数
		Integer totalPeopleNumber = ((PlatUserInviteAwardInfoDao)getBaseDao()).getTotalAwardCount();
		resultMap.put("totalPeopleNumber",totalPeopleNumber);
		//TODO 最新奖励用户TOP30-
		List<AwardUserBean> awardUserList = ((PlatUserInviteAwardInfoDao)getBaseDao()).awardUserList(15);
		resultMap.put("awardUserList",awardUserList);
		result.setData(resultMap);
	}

	@Override
	public void getInviteAwardStatisticsInfo(Long userId, ResultMsgBean result)throws Exception {
		User user = this.userService.getByUserId(userId);
		if(user == null){
			result.setCode(ReturnCodeConstant.NOT_LOGGED_IN_CODE);
			result.setMsg("未登录");
			return ;
		}
		Map<String,Object> resultMap = new HashMap<>();
		//TODO 未开通列表TOP15
		List<InviteUserBean> noPayList =  ((PlatUserInviteAwardInfoDao)getBaseDao()).noPayUserListByUserId(userId,15);
		resultMap.put("noPayList",noPayList);
		//TODO 已开通列表TOP15
		List<InviteUserBean> payList =  ((PlatUserInviteAwardInfoDao)getBaseDao()).payUserListByUserId(userId,15);
		resultMap.put("payList",payList);
		//TODO 邀请总数
		Integer inviteCount = ((PlatUserInviteAwardInfoDao)getBaseDao()).getTotalAwardCountByUserId(userId);
		resultMap.put("inviteCount",inviteCount);
		//TODO 人数最大数量
		Integer maxInvietCount = ((PlatUserInviteAwardInfoDao)getBaseDao()).getInvitePeopleNumberMax();
		resultMap.put("maxInvietCount",maxInvietCount);
		//TODO 奖励最大数量
		Integer maxAwardSum = ((PlatUserInviteAwardInfoDao)getBaseDao()).getAwardDaysMaxSum();
		resultMap.put("maxAwardSum",maxAwardSum);
		result.setData(resultMap);
	}
}
