package com.tyt.acvitity.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.tyt.acvitity.bean.GrantStrategyConfigDetailBean;
import com.tyt.acvitity.bean.GrantStrategyConfigDetailListBean;
import com.tyt.acvitity.constant.GainTypeEnum;
import com.tyt.acvitity.service.GrantStrategyConfigService;
import com.tyt.model.GrantStrategyConfig;
import com.tyt.plat.mapper.base.GrantStrategyConfigMapper;
import com.tyt.plat.mapper.base.TytGrantStrategyLogMapper;
import com.tyt.service.common.redis.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/03/27 14:43
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GrantStrategyConfigServiceImpl implements GrantStrategyConfigService {

    private final GrantStrategyConfigMapper grantStrategyConfigMapper;

    private final TytGrantStrategyLogMapper grantStrategyLogMapper;

    /**
     * 定义缓存key值
     */
    private static final String REGISTER_GRANT_STRATEGY_CONFIG_USERID = "register:grant:strategy:config:userId:%d";

    @Override
    public GrantStrategyConfigDetailListBean getStrategyConfigByGrantType(Long userId, String grantType) {
        //定义总获取权益数量为0
        int totalGainNum = 0;
        //定义返回的配置信息
        GrantStrategyConfigDetailListBean detailListBean = new GrantStrategyConfigDetailListBean();
        List<GrantStrategyConfigDetailBean> ConfigDetailList = new ArrayList<>();
        //当缓存中存在且已经查询过一次有值的情况 不再返回
        if (RedisUtil.exists(String.format(REGISTER_GRANT_STRATEGY_CONFIG_USERID, userId))) {
            return new GrantStrategyConfigDetailListBean();
        }
        //查询该发放条件下配置信息
        List<GrantStrategyConfig> configs = grantStrategyConfigMapper.selectByGrantType(grantType);
        for (GrantStrategyConfig config : configs) {
            //判断当前用户是否参与此次活动
            int i = grantStrategyLogMapper.selectCountByUserId(userId, config.getId());
            if (i > 0) {
                GrantStrategyConfigDetailBean ConfigDetail = new GrantStrategyConfigDetailBean();
                //优车发货卡配置
                if (GainTypeEnum.EXCELLENT_GOODS_CARDS.getType().equals(config.getGainType())) {
                    ConfigDetail.setGainType(config.getGainType());
                    ConfigDetail.setGainNum(config.getGainNum());
                    if (ObjectUtil.isNotNull(config.getGainNum())) {
                        totalGainNum += config.getGainNum();
                    }
                    ConfigDetailList.add(ConfigDetail);
                }
                //普通发货卡
                if (GainTypeEnum.NORMAL_GOODS_CARDS.getType().equals(config.getGainType())) {
                    if (null != config.getGoodsId() && config.getGoodsId().intValue() > 0) {
                        ConfigDetail.setGainType(config.getGainType());
                        ConfigDetail.setGainNum(config.getGainNum());
                        ConfigDetail.setGoodsUseNum(config.getGoodsUseNum());
                        ConfigDetail.setGoodsEffectiveTime(config.getGoodsEffectiveTime());
                        if (ObjectUtil.isNotNull(config.getGainNum())
                                && ObjectUtil.isNotNull(config.getGoodsUseNum())) {
                            totalGainNum += config.getGainNum() * config.getGoodsUseNum();
                        }
                    }
                    ConfigDetailList.add(ConfigDetail);
                }

            }
        }
        if (ConfigDetailList.size() > 0) {
            RedisUtil.set(String.format(REGISTER_GRANT_STRATEGY_CONFIG_USERID, userId),
                    String.valueOf(System.currentTimeMillis()), 0);
        }
        detailListBean.setGrantStrategyConfigDetailBeans(ConfigDetailList);
        detailListBean.setTotalGainNum(totalGainNum);

        return detailListBean;
    }
}
