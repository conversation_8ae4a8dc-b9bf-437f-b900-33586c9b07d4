package com.tyt.acvitity.service.impl;

import com.google.common.collect.Maps;
import com.tyt.acvitity.bean.ActivityListResp;
import com.tyt.acvitity.bean.UserActivityVo;
import com.tyt.acvitity.dao.DrawActivityInfoDao;
import com.tyt.acvitity.dao.LotteryRecordDao;
import com.tyt.acvitity.dao.LotteryUserDao;
import com.tyt.acvitity.service.ActivityService;
import com.tyt.acvitity.service.LotteryRecordService;
import com.tyt.acvitity.service.LotteryUserService;
import com.tyt.acvitity.service.UserActivityService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Activity;
import com.tyt.model.User;
import com.tyt.model.UserActivity;
import com.tyt.model.UserPermission;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.model.*;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/07/23 11:05
 */
@Service("activityService")
public class ActivityServiceImpl extends BaseServiceImpl<Activity, Long> implements ActivityService {
    public Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource(name = "userActivityService")
    private UserActivityService userActivityService;
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "lotteryUserDao")
    private LotteryUserDao lotteryUserDao;
    @Resource(name = "drawActivityInfoDao")
    private DrawActivityInfoDao drawActivityInfoDao;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;


    @Resource(name = "activityDao")
    @Override
    public void setBaseDao(BaseDao<Activity, Long> dao) {
        super.setBaseDao(dao);
    }

    @Override
    public List<ActivityListResp> listByUserId(Long userId) throws Exception{
        final Date date = new Date();
//        UserPermission carPermission = userPermissionService.getUserPermission(userId,"100101");
//        if (carPermission!=null){
//            if (carPermission.getStatus()==1 || TimeUtil.daysBetween(carPermission.getEndTime(),new Date())<30){
//                return new ArrayList<>();
//            }
//        }else{
//            User user = userService.getById(userId);
//            if (TimeUtil.daysBetween(user.getCtime(),new Date())<30){
//                return new ArrayList<>();
//            }
//        }
        List<ActivityListResp> activityListResps = new ArrayList<>();
//        查询当前用户是否有资格参与定向用户抽奖活动
        List<LotteryUser> lotteryUsers = lotteryUserDao.find("from LotteryUser where userId = ?", userId);
        StringBuffer drawActivityInfoIdsBuffer = new StringBuffer();
        lotteryUsers.forEach(lotteryUser -> {
            drawActivityInfoIdsBuffer.append(lotteryUser.getDrawActivityInfoId()).append(",");
        });
        String drawActivityInfoIds = drawActivityInfoIdsBuffer.toString();
        if(!lotteryUsers.isEmpty()){
//            查询当前正在进行中或已结束的活动
            List<DrawActivityInfo> drawActivityInfos = drawActivityInfoDao.find("from DrawActivityInfo where status = 1 and startTime <= ? and isDelete = 1 and id in(" + drawActivityInfoIds.substring(0, drawActivityInfoIds.length() - 1) + ")", date);
            if(!drawActivityInfos.isEmpty()){
                String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.com/");
                for (DrawActivityInfo drawActivityInfo : drawActivityInfos) {
                    ActivityListResp activityListResp = new ActivityListResp(drawActivityInfo, tytServerPictureUrl);
                    activityListResp.setCurrentTime(new Date());
                    activityListResp.setTitle(drawActivityInfo.getActivityName());
                    activityListResps.add(activityListResp);
                }
            }
        }

        User user = userService.getByUserId(userId);
        logger.info("user{}", user);
        if (null == user.getUserClass() || 1 == user.getUserClass() || TimeUtil.daysBetween(user.getCtime(),date)<30){
            return activityListResps;
        }
        //查询当前开始的所有活动
        final List<Activity> list = getBaseDao().find("from Activity where status = 1 and startTime <= ? ", date);
        if(CollectionUtils.isEmpty(list)){
            return activityListResps;
        }

        final List<Long> activityIds = list.stream()
                .map(Activity::getId)
                .collect(Collectors.toList());

        //查询该用户可以查看到的活动
        final Map<Long, UserActivityVo> activityMap = userActivityService.list(activityIds, userId)
                .stream().collect(Collectors.toMap(UserActivityVo::getActivityId, Function.identity(), (o1, o2) -> o1));
        if(MapUtils.isEmpty(activityMap)){
            return activityListResps;
        }

        activityListResps.addAll( list.stream()
                .filter(a -> {
                    //将不符合要求的活动过滤掉
                    final UserActivityVo activity = activityMap.get(a.getId());
                    if (activity == null) {
                        return false;
                    }
                    //若活动 有配置有效时间 并且 参与活动的时间不为空 则判断是否失效
//                    if(a.getEffectiveDay() > 0 && activity.getActivityCtime() != null){
//                        final Date failureTime = DateUtils.addDays(activity.getActivityCtime(), a.getEffectiveDay());
//                        //失效时间早于当前时间则不允许查看
//                        if(failureTime.before(date)){
//                            return false;
//                        }
//                    }
                    a.setStartTime(activity.getActivityCtime());
                    if (activity.getActivityCtime() != null) {
                        a.setEndTime(DateUtils.addDays(activity.getActivityCtime(), 23));
                    }
                    return true;
                })
                .map(a -> {
                    final ActivityListResp resp = new ActivityListResp();
                    resp.setActivityId(a.getId());
                    resp.setTitle(a.getTitle());
                    resp.setContent(a.getContent());
                    resp.setStartTime(a.getStartTime());
                    resp.setEndTime(a.getEndTime());
                    resp.setCurrentTime(new Date());
                    resp.setLinkUrl(a.getLinkUrl());
                    return resp;
                }).collect(Collectors.toList()));
        return activityListResps;
    }

    @Override
    public int getPopUp(Long userId) {
        final List<UserActivity> activities = userActivityService.getByUserId(userId);
        return CollectionUtils.isEmpty(activities) ? 1 : 0;
    }

    @Override
    public int getCashBack(Long userId){
        String sql = "select count(*) from tyt_cash_back where user_id = ? and status in (2,4)";
        BigInteger count = this.getBaseDao().query(sql, new Object[] { userId });
        return count.intValue()>0 ? 3:1;
    }

}
