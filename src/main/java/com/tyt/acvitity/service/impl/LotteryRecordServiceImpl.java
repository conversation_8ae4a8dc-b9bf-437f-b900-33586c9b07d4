package com.tyt.acvitity.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.bean.ActivityDetailResp;
import com.tyt.acvitity.bean.LotteryRecordReq;
import com.tyt.acvitity.bean.LotteryRecordResp;
import com.tyt.acvitity.bean.SendAwardByActivityIdMsgBean;
import com.tyt.acvitity.dao.LotteryRecordDao;
import com.tyt.acvitity.dao.LotteryUserDao;
import com.tyt.acvitity.dao.ProdCouponDao;
import com.tyt.acvitity.dao.PromoUserCouponDao;
import com.tyt.acvitity.helper.LotteryCheckService;
import com.tyt.acvitity.service.DrawActivityInfoService;
import com.tyt.acvitity.service.LotteryRecordService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.enums.PermissionGainTypeEnum;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.PromoCouponMapper;
import com.tyt.permission.bean.GoodsType;
import com.tyt.permission.bean.PermissionChangeType;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.promo.dao.PromoActivityDao;
import com.tyt.promo.model.PromoCoupon;
import com.tyt.user.dao.CarDao;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Random;

import static com.tyt.acvitity.constant.ProbCouponAwardType.*;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Service("lotteryRecordService")
public class LotteryRecordServiceImpl extends BaseServiceImpl<LotteryRecord, Long> implements
        LotteryRecordService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource(name = "drawActivityInfoService")
    private DrawActivityInfoService drawActivityInfoService;
    @Resource(name = "lotteryRecordDao")
    private LotteryRecordDao lotteryRecordDao;
    @Resource(name = "carDao")
    private CarDao carDao;
    @Resource(name = "prodCouponDao")
    private ProdCouponDao prodCouponDao;
    @Resource(name = "userService")
    private UserService userService;
    @Resource
    private PromoCouponMapper promoCouponMapper;
    @Resource(name = "lotteryUserDao")
    private LotteryUserDao lotteryUserDao;
    @Resource
    private PromoActivityDao promoActivityDao;
    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "promoUserCouponDao")
    private PromoUserCouponDao promoUserCouponDao;
    @Autowired
    private LotteryCheckService lotteryCheckService;
    @Autowired
    private UserPermissionService userPermissionService;

    @Override
    @Resource(name = "lotteryRecordDao")
    public void setBaseDao(BaseDao<LotteryRecord, Long> dao) {
        super.setBaseDao(dao);
    }

    @Override
    public ResultMsgBean getActivityDetail(Long userId, Long activityId) {
//        查询活动信息
        DrawActivityInfo drawActivityInfo = drawActivityInfoService.getById(activityId);
        if (null == drawActivityInfo) {
            logger.error("活动详情查询失败，失败原因：活动不存在！");
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "活动不存在");
        }
        ActivityDetailResp activityDetailResp = new ActivityDetailResp();
        BeanUtils.copyProperties(drawActivityInfo, activityDetailResp);
//        查询用户最近一次的中奖信息
        List<LotteryRecord> lotteryRecords = lotteryRecordDao.find(
                "from LotteryRecord where userId = ? and isDelete = 1 and drawActivityInfoId = ? and probCouponName != '谢谢参与' order By createTime desc limit 1",
                userId, activityId);
        if (!lotteryRecords.isEmpty()) {
            List<PromoActivity> promoActivities = promoActivityDao.find("from PromoActivity where id = ?",
                    lotteryRecords.get(0).getPromoActivityId().intValue());
            if (promoActivities.isEmpty()) {
                logger.error("未获取到活动（PromoActivity）数据");
                return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", activityDetailResp);
            }
            PromoCoupon promoCoupon = promoCouponMapper.selectByPrimaryKey(
                    promoActivities.get(0).getGrantTypeId());
            if (null == promoCoupon) {
                logger.error("未获取到活动对应的奖券（PromoCoupon）数据");
                return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", activityDetailResp);
            }
            BeanUtils.copyProperties(lotteryRecords.get(0), activityDetailResp);
            activityDetailResp.setLotteryRecordId(lotteryRecords.get(0).getId());
            activityDetailResp.setCouponDesc(promoCoupon.getCouponDesc());
        }
        return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", activityDetailResp);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultMsgBean activityLottery(Long userId, Long activityId, Long agreementActivityId) {
        DrawActivityInfo drawActivityInfo = drawActivityInfoService.getById(activityId);
//        校验用户是否能抽奖
        ResultMsgBean result = lotteryCheckService.checkLottery(userId, drawActivityInfo,
                agreementActivityId);
        if (ResultMsgBean.OK != result.getCode()) {
            return result;
        }
//        4. 抽奖
        ProbCoupon probCoupon = null;
//        4.1 查询库存，如果库存全部为0那么直接返回谢谢惠顾
        List<ProbCoupon> probCoupons = prodCouponDao.find(
                "from ProbCoupon where drawActivityInfoId = ? and (awardTotalAmount - awardUsedAmount) > 0 and isDelete = 0 and awardProb > 0 order by awardProb",
                activityId);
        if (!probCoupons.isEmpty()) {
//            4.2 10次以后如果还是没有抽到结果，返回谢谢惠顾
            Integer lotteryCount = 0;
            probCoupon = this.lottery(lotteryCount, probCoupons);
        }
        ActivityDetailResp activityDetailResp = this.lotteryUpdate(probCoupon, userId, activityId,
                drawActivityInfo);
        result.setData(activityDetailResp);
        return result;
    }

    @Override
    public ResultMsgBean getLotteryRecord(LotteryRecordReq param) {
//        查询活动信息
        DrawActivityInfo drawActivityInfo = drawActivityInfoService.getById(param.getActivityId());
        if (null == drawActivityInfo) {
            logger.error("活动详情查询失败，失败原因：活动不存在！");
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "活动不存在");
        }

        LotteryRecordResp lotteryRecordResp = new LotteryRecordResp();

         Integer totalNum = lotteryRecordDao.getLotteryRecordNum(param.getUserId(),
                param.getActivityId());
        lotteryRecordResp.setTotalNum(totalNum);
         if(totalNum>0){
             //        查询用户的中奖信息
             List<LotteryRecord> lotteryRecords = lotteryRecordDao.getLotteryRecord(param.getUserId(),
                     param.getActivityId(), param.getPageNo(), param.getPageSize());
             lotteryRecordResp.setLotteryRecords(lotteryRecords);
         }


        return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", lotteryRecordResp);
    }

    public ActivityDetailResp lotteryUpdate(ProbCoupon probCoupon, Long userId, Long activityId,
            DrawActivityInfo drawActivityInfo) {
        // 修改用户表的用户是否抽奖
        LotteryUser lotteryUser = new LotteryUser();
        lotteryUser.setUserId(userId);
        lotteryUser.setTakeIn(1);
        lotteryUser.setDrawActivityInfoId(activityId);
        lotteryUserDao.updateTakeIn(lotteryUser);
        logger.info("用户抽奖的内容：{}", probCoupon);

        // 添加用户获奖记录
        User user;
        try {
            user = userService.getByUserId(userId);
            logger.info("user{}", user);
        } catch (Exception e) {
            logger.error("获取用户信息异常", e);
            throw new RuntimeException("获取用户信息异常");
        }

        // 用户中奖记录
        LotteryRecord lotteryRecord = new LotteryRecord();
        lotteryRecord.setUserId(userId);
        lotteryRecord.setDrawActivityInfoId(activityId);
        lotteryRecord.setUserCallPhone(user.getCellPhone());
        lotteryRecord.setActivityName(drawActivityInfo.getActivityName());
        lotteryRecord.setCreateTime(new Date());
        lotteryRecord.setCreateBy(userId);
        lotteryRecord.setIsDelete(1);

        // 返回体
        ActivityDetailResp activityDetailResp = new ActivityDetailResp();
        activityDetailResp.setActivityId(activityId);

        if (null == probCoupon) {// 没中
            lotteryRecord.setIsWin(0);

            activityDetailResp.setActivityId(activityId);
            activityDetailResp.setIsWin(0);
            activityDetailResp.setProbCouponName("谢谢参与");
            activityDetailResp.setCouponDesc("谢谢参与");
            activityDetailResp.setAwardType(3);
        } else {// 中了
            lotteryRecord.setIsWin(1);
            lotteryRecord.setPromoActivityId(probCoupon.getPromoActivityId());
            lotteryRecord.setProbCouponId(probCoupon.getId());
            lotteryRecord.setProbCouponName(probCoupon.getActivityName());
            lotteryRecord.setAwardType(probCoupon.getAwardType());

            activityDetailResp.setProbCouponId(probCoupon.getId());
            activityDetailResp.setProbCouponName(probCoupon.getActivityName());
            activityDetailResp.setSort(probCoupon.getSort());
            activityDetailResp.setAwardType(probCoupon.getAwardType());
            activityDetailResp.setIsWin(1);

            // 6. 奖品数量扣减和用户抽奖总次数扣减
            prodCouponDao.updateAwardUsedAmount(probCoupon);

            int awardType = probCoupon.getAwardType() == null ? 0 : probCoupon.getAwardType();
            switch (awardType) {
                case COUPON:
                    //7.1 获取奖券信息 promoCoupon
                    List<PromoActivity> promoActivities = promoActivityDao.find(
                            "from PromoActivity where id = ?",
                            probCoupon.getPromoActivityId().intValue());
                    if (promoActivities.isEmpty()) {
                        logger.error("未获取到活动（PromoActivity）数据");
                        throw new RuntimeException("获取奖券活动数据异常");
                    }
                    PromoCoupon promoCoupon = promoCouponMapper.selectByPrimaryKey(
                            promoActivities.get(0).getGrantTypeId());
                    logger.info("未获取到活动对应的奖券（PromoCoupon）数据");
                    if (null == promoCoupon) {
                        throw new RuntimeException("获取活动对应的奖券数据异常");
                    }
                    logger.info("promoCoupon{}", promoCoupon);

                    //奖券不是谢谢参与时，发送mq
                    if (!ReturnCodeConstant.THANK_YOU_FOR_PARTICIPATION_MSG.equals(
                            probCoupon.getActivityName())) {
                        //重复发放奖券
//                        PromoUserCoupon promoUserCoupon = new PromoUserCoupon(user, probCoupon, promoCoupon,
//                                promoActivities.get(0));
//                        promoUserCouponDao.insert(promoUserCoupon);
                        this.sendCouponByActivityId2MQ(userId, promoActivities.get(0).getId());
                    }
                    // 修改记录
                    lotteryRecord.setCouponAmount(promoCoupon.getCouponAmount());
                    // 修改返回体
                    activityDetailResp.setCouponDesc(promoCoupon.getCouponDesc());
                    activityDetailResp.setPromoActivityId(promoActivities.get(0).getId().longValue());
                    activityDetailResp.setCouponAmount(promoCoupon.getCouponAmount());
                    activityDetailResp.setCouponDesc(promoCoupon.getCouponDesc());
                    break;
                case STAFF:
                    // TODO 实物奖励只更新中级记录不参与发奖，奖品线下发放
                    break;
                case PERMISSION:
                    //  权益
                    Long promoActivityId = probCoupon.getPromoActivityId();
                    GoodsType goodsType = GoodsType.findById(promoActivityId);
                    userPermissionService.giveGoodsCard(userId, goodsType, PermissionChangeType.赠送,
                            "lottery(抽奖)", PermissionGainTypeEnum.赠送);
                    break;
                default:
                    logger.error("不支持的抽奖类型:{}, activityId:{}, probCoupon:{}",
                            probCoupon.getAwardType(), activityId, probCoupon);
                    throw new RuntimeException("不支持的抽奖类型");
            }
        }

        lotteryRecordDao.insert(lotteryRecord);
        return activityDetailResp;
    }

    /**
     * 通过递归实现用户抽奖
     *
     * @param lotteryCount 轮盘次数
     * @param probCoupons  奖品集合
     * @return
     */
    private ProbCoupon lottery(Integer lotteryCount, List<ProbCoupon> probCoupons) {
        lotteryCount++;
        BigDecimal probability = new BigDecimal(BigInteger.ZERO);
//        随机值边界
        Integer min = 1;
//        获取总概率
        for (ProbCoupon probCoupon : probCoupons) {
            probability = probability.add(probCoupon.getAwardProb());
        }
//        通过总概率设置随机数的边界，*100保证没有小数
        int totalProbability = probability.multiply(new BigDecimal("100")).intValue();
        Random random = new Random();
//        通过边界随机获取[1-总概率*100]一个数
        int index = (random.nextInt(totalProbability) % totalProbability + min) / 100;
//        定义返回对象
        ProbCoupon probCoupon = null;
//        判断生成的随机数对应那个对象
        int count = 0;
        for (int i = 0; i < probCoupons.size(); i++) {
            if (Math.max(count, index) == Math.min(index,
                    (probCoupons.get(i).getAwardProb().intValue() + count))) {
                probCoupon = probCoupons.get(i);
                break;
            }
            count = count + probCoupons.get(i).getAwardProb().intValue();
        }
        if (null == probCoupon && lotteryCount < 100) {
            this.lottery(lotteryCount, probCoupons);
        }
        return probCoupon;
    }

    public void sendCouponByActivityId2MQ(Long userId, Integer activityId) {
        SendAwardByActivityIdMsgBean mqMsg = new SendAwardByActivityIdMsgBean();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(MqBaseMessageBean.SEND_COUPON_BY_ACTIVITY_ID);
        //用户ID
        mqMsg.setUserId(userId);
        //活动id
        mqMsg.setActivityId(activityId);

        // 保存发送mq
        final String messageSerailNum = mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        final int messageType = mqMsg.getMessageType();

        //发送并mq信息并保存到数据库
        tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, messageType);
        tytMqMessageService.sendMqMessage(messageSerailNum, mqJson, 0);
    }
}
