package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.bean.*;
import com.tyt.acvitity.dao.LotteryRecordDao;
import com.tyt.acvitity.dao.ProdCouponDao;
import com.tyt.acvitity.helper.LotteryCheckRequest;
import com.tyt.acvitity.helper.LotteryCheckService;
import com.tyt.acvitity.service.CarActivityDataService;
import com.tyt.acvitity.service.DrawActivityInfoService;
import com.tyt.acvitity.service.TransportOrdersRiskService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.ConventionActivityMapper;
import com.tyt.mybatis.mapper.MarketingActivityMapper;
import com.tyt.mybatis.mapper.MarketingActivityUserMapper;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class CarActivityDataServiceImpl implements CarActivityDataService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MarketingActivityMapper marketingActivityMapper;

    @Autowired
    private ConventionActivityMapper conventionActivityMapper;

    @Autowired
    private MarketingActivityUserMapper marketingActivityUserMapper;

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private LotteryCheckService lotteryCheckService;

    @Autowired
    private DrawActivityInfoService drawActivityInfoService;

    @Autowired
    private TransportOrdersRiskService transportOrdersRiskService;

    @Autowired
    private ProdCouponDao prodCouponDao;

    @Autowired
    private LotteryRecordDao lotteryRecordDao;

    @Override
    public CarActivityDetail getCarAtivityDetail(CarActivityReq param, MarketingActivity marketingActivityInfo, DrawActivityInfo drawActivityInfo) {
        //result
        CarActivityDetail carActivityDetail = new CarActivityDetail();

        //判断运营活动是否过期
        boolean marketingActivityIsExpire = checkMarketingActivityDate(marketingActivityInfo);
        if (!marketingActivityIsExpire) {
            carActivityDetail.setMarketingActivityIsExpire(1);
        } else {
            carActivityDetail.setMarketingActivityIsExpire(0);
        }

        //判断抽奖活动是否过期
        boolean lotteryActivityIsExpire = checkLotteryActivityDate(param, drawActivityInfo);
        if(!lotteryActivityIsExpire){
            carActivityDetail.setLotteryActivityIsExpire(1);
        } else {
            carActivityDetail.setLotteryActivityIsExpire(0);
        }

        //抽奖活动详细信息
        carActivityDetail.setDrawActivityInfo(drawActivityInfo);

        //活动单总数
        //车方新注册用户-活动类型14，单独计算
        if(null !=marketingActivityInfo.getActivityType() && marketingActivityInfo.getActivityType() == 14){
            Long orderNum = transportOrdersRiskService.getCountByTime(marketingActivityInfo.getStartTime(),marketingActivityInfo.getEndTime(),param.getUserId());
            carActivityDetail.setConventionActivityOrderNum(orderNum);
        }else{
            //旧活动类型，直接取值
            Long orderNum = conventionActivityMapper.selectOrderNum(param.getMarketingActivityId(), param.getUserId());
            carActivityDetail.setConventionActivityOrderNum(orderNum == null || orderNum < 0 ? 0: orderNum);
        }

        //排行榜
        List<CarActivityUserRanking> carActivityUserRankingsTopTen = conventionActivityMapper.selectCarActivityUserRankingTopTen(param.getMarketingActivityId());
        carActivityDetail.setCarActivityUserRanking(carActivityUserRankingsTopTen == null ? new ArrayList<>() : carActivityUserRankingsTopTen);

        //本用户排名
        Long ranking = conventionActivityMapper.selectUserCarActivityRanking(param.getMarketingActivityId(), param.getUserId());
        carActivityDetail.setRanking(ranking == null || ranking < 0 || ranking > 100 ? 0 : ranking);

        //今日剩余抽奖机会
        //查询对应的抽奖活动
        ResultMsgBean result = lotteryCheckService.checkLottery(param.getUserId(), drawActivityInfo, param.getMarketingActivityId());
        if(ResultMsgBean.OK != result.getCode()){
            carActivityDetail.setAvailableLotteriesNum(0);
        } else {
            carActivityDetail.setAvailableLotteriesNum(1);
        }

        if (ReturnCodeConstant.ACTIVITY_NOT_PERM_NEED_CALL_PUBLISHER == result.getCode()) {
            carActivityDetail.setIsShowNeedCallTab(1);
        } else {
            carActivityDetail.setIsShowNeedCallTab(0);
        }
        //转盘抽奖-奖品数据
        List<ProbCoupon> coupons = prodCouponDao.getByDrawid(drawActivityInfo.getId());
        carActivityDetail.setCouponList(coupons);
        //查询用户中奖信息
        Integer totalNum = lotteryRecordDao.getLotteryRecordNum(param.getUserId(), drawActivityInfo.getId());
        carActivityDetail.setTotalNum(totalNum);
        //当天用户抽奖记录
        Date startTime = TimeUtil.weeHours(new Date(), 0);
        Date endTime = TimeUtil.weeHours(new Date(), 1);
        Integer todayNum = lotteryRecordDao.getNowLotter(param.getUserId(), drawActivityInfo.getId(), startTime, endTime);
        carActivityDetail.setTodayNum(todayNum);
        return carActivityDetail;
    }

    @Override
    public List<TytTransportOrders> getCarAtivityOrderList(CarActivityReq param) {
        List<TytTransportOrders> ordersByCarAtivityOrder = transportOrdersService.getOrdersByCarAtivityOrder(param.getMarketingActivityId(), param.getUserId());
        return ordersByCarAtivityOrder == null ? new ArrayList<>() : ordersByCarAtivityOrder;
    }

    @Override
    public List<TytTransportOrders> getCarAtivityOrderRiskList(Long activityId,Long userId,MarketingActivity activity) {
        List<TytTransportOrders> ordersByCarAtivityOrder = transportOrdersService.getOrdersByCarAtivityOrderRisk(activityId, userId,activity);
        return ordersByCarAtivityOrder == null ? new ArrayList<>() : ordersByCarAtivityOrder;
    }

    public boolean checkMarketingActivityDate(MarketingActivity marketingActivityInfo) {
        //判断车主透传运营活动是否过期
        if (marketingActivityInfo != null && marketingActivityInfo.getStatus() == 1) {
            Date now = new Date();
            if (marketingActivityInfo.getEndTime() != null) {
                if (now.after(marketingActivityInfo.getEndTime())) {
                    return false;
                }
            }
            if (marketingActivityInfo.getStartTime() != null) {
                if (now.before(marketingActivityInfo.getStartTime())) {
                    return false;
                }
            }
        } else {
            return false;
        }
        return true;
    }

    public boolean checkLotteryActivityDate(CarActivityReq param, DrawActivityInfo drawActivityInfo) {
        //判断车主透传抽奖活动是否过期
        LotteryCheckRequest lotteryCheckRequest = LotteryCheckRequest.builder()
                .userId(param.getUserId())
                .drawActivityInfo(drawActivityInfo)
                .agreementActivityId(param.getMarketingActivityId())
                .build();
        ResultMsgBean checkLotterySelfResultMsgBean = lotteryCheckService.checkLotterySelf(lotteryCheckRequest);
        if(ResultMsgBean.OK != checkLotterySelfResultMsgBean.getCode()){
            return false;
        }
        return true;
    }

    @Override
    public boolean checkCarActivityContainUser(CarActivityReq param, DrawActivityInfo drawActivityInfo) {
        //判断用户是否具有抽奖活动权限
        if (drawActivityInfo == null) {
            return false;
        }
        LotteryCheckRequest lotteryCheckRequest = LotteryCheckRequest.builder()
                .userId(param.getUserId())
                .drawActivityInfo(drawActivityInfo)
                .agreementActivityId(param.getMarketingActivityId())
                .build();
        ResultMsgBean result = lotteryCheckService.checkLotteryUser(lotteryCheckRequest);
        if(ResultMsgBean.OK != result.getCode()){
            return false;
        }
        //判断用户是否具有车主透传运营活动活动权限
        List<MarketingActivityUser> byActivityId = marketingActivityUserMapper.getByActivityId(param.getMarketingActivityId(), param.getUserId());
        if (byActivityId == null || byActivityId.size() == 0) {
            return false;
        }
        return true;
    }
}
