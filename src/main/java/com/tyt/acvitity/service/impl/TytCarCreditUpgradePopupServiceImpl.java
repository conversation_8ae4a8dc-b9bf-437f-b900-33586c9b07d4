package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.constant.CarCreditUpgradePopupTyptEnum;
import com.tyt.acvitity.service.CsComplaintRecordResultServcie;
import com.tyt.acvitity.service.TytCarCreditUpgradePopupService;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.marketingActivity.bean.MarketingActivityPopupBean;
import com.tyt.marketingActivity.enums.CreditUpgradeImgUrlEnum;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.User;
import com.tyt.plat.entity.base.TytCarCreditUpgradePopup;
import com.tyt.plat.mapper.base.TytCarCreditUpgradePopupMapper;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/4/25
 */
@Service("tytCarCreditUpgradePopupService")
@Slf4j
public class TytCarCreditUpgradePopupServiceImpl implements TytCarCreditUpgradePopupService {

    @Autowired
    private TytCarCreditUpgradePopupMapper tytCarCreditUpgradePopupMapper;
    @Autowired
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Autowired
    private CsComplaintRecordResultServcie csComplaintRecordResultServcie;

    @Resource(name = "userService")
    private UserService userService;

    @Override
    public TytCarCreditUpgradePopup getByPopupType(int popupType, Long userId) {
        TytCarCreditUpgradePopup tytCarCreditUpgradePopup = tytCarCreditUpgradePopupMapper.selectByUserIdPopupType(userId, popupType);
        if (tytCarCreditUpgradePopup == null) {
            tytCarCreditUpgradePopup = TytCarCreditUpgradePopup.builder()
                    .isPopup(1)
                    .popupType(CarCreditUpgradePopupTyptEnum.GROW_LEVEL_UPGRADATION.getType())
                    .popupTime(new Date())
                    .ctime(new Date())
                    .userId(userId).build();
            tytCarCreditUpgradePopupMapper.insert(tytCarCreditUpgradePopup);
            return null;
        }
        return tytCarCreditUpgradePopup;
    }

    @Override
    public synchronized MarketingActivityPopupBean getCreditUpgradePopupInfo(Long userId) {
        try {
            TytCarCreditUpgradePopup tytCarCreditUpgradePopup = tytCarCreditUpgradePopupMapper.selectByUserIdPopupType(userId, CarCreditUpgradePopupTyptEnum.SHOW_LEVEL.getType());
            //获取弹窗图片地址 看该用户到底弹升级图片还是等级不变图片
            String creditUpgradeImgUrl = getCreditUpgradeImgUrl(userId);
            //没获取到对应图片地址 我只能不弹
            if (org.apache.commons.lang3.StringUtils.isEmpty(creditUpgradeImgUrl)){
                return null;
            }
            //为null 说明没弹过直接弹
            if (Objects.isNull(tytCarCreditUpgradePopup)) {
                tytCarCreditUpgradePopup = TytCarCreditUpgradePopup.builder()
                        .isPopup(1)
                        .userId(userId)
                        .popupType(CarCreditUpgradePopupTyptEnum.SHOW_LEVEL.getType())
                        .popupTime(new Date())
                        .ctime(new Date())
                        .build();
                tytCarCreditUpgradePopupMapper.insertSelective(tytCarCreditUpgradePopup);
                MarketingActivityPopupBean marketingActivityPopupBean = new MarketingActivityPopupBean();
                marketingActivityPopupBean.setPicUrl(creditUpgradeImgUrl);
                marketingActivityPopupBean.setShowPosition(2);
                marketingActivityPopupBean.setId(0L);
                marketingActivityPopupBean.setTimeUnit("day");
                return marketingActivityPopupBean;
            }

            if (Objects.nonNull(tytCarCreditUpgradePopup)) {
                //获取本月1号时间
                Date beginTime = TimeUtil.parseDateString(TimeUtil.getMonthFirstDayFom(new Date()));
                Date endTime = new Date();
                //判断当月是否已经弹过了(判断弹窗时间popupTime 是否在本月1号-当前之间，在则不进行弹窗)
                boolean belongCalendar = TimeUtil.belongCalendar(tytCarCreditUpgradePopup.getPopupTime(), beginTime, endTime);
                //当月弹过不进行弹窗
                if (belongCalendar) {
                    return null;
                }
                //更新弹窗时间
                tytCarCreditUpgradePopup.setPopupTime(new Date());
                tytCarCreditUpgradePopup.setPopupType(CarCreditUpgradePopupTyptEnum.SHOW_LEVEL.getType());
                tytCarCreditUpgradePopupMapper.updateByPrimaryKeySelective(tytCarCreditUpgradePopup);
                MarketingActivityPopupBean marketingActivityPopupBean = new MarketingActivityPopupBean();
                marketingActivityPopupBean.setPicUrl(creditUpgradeImgUrl);
                marketingActivityPopupBean.setShowPosition(2);
                marketingActivityPopupBean.setId(0L);
                marketingActivityPopupBean.setTimeUnit("day");
                return marketingActivityPopupBean;
            }
        } catch (Exception e) {
            log.error("获取车方成长体系升级弹窗信息失败userId={},错误信息={}", userId, e.getMessage());
        }
        return null;
    }

    private String getCreditUpgradeImgUrl(Long userId) {
        // 获取成长数值
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
//        //为 null 不弹窗
//        if (Objects.isNull(userCreditInfo)) {
//            return null;
//        }
        //当前信用等级
        Integer carCreditRankLevel =Objects.isNull(userCreditInfo) || StringUtils.isEmpty(userCreditInfo.getCarCreditRankLevel()) ? 1 : Integer.parseInt(userCreditInfo.getCarCreditRankLevel());
        //上月信用等级
        Integer carLastMonthRankLevel =Objects.isNull(userCreditInfo) || StringUtils.isEmpty(userCreditInfo.getCarLastMonthRankLevel()) ? 1 : Integer.parseInt(userCreditInfo.getCarLastMonthRankLevel());
        // 判断30天内是否有严重客诉
        User user = userService.getById(userId);
        int wrongCount = csComplaintRecordResultServcie.getCountByWrongReason(user);
        if (wrongCount > 0) {
            //有严重客诉直接弹V0
            return CreditUpgradeImgUrlEnum.getCreditUpgradeImgUrlByLevel(0, 2);
        }

        //弹升级弹窗
        if (carCreditRankLevel > carLastMonthRankLevel) {
            return CreditUpgradeImgUrlEnum.getCreditUpgradeImgUrlByLevel(carCreditRankLevel, 1);
        }
        //弹本月等级弹窗
        if (carCreditRankLevel <= carLastMonthRankLevel) {
            return CreditUpgradeImgUrlEnum.getCreditUpgradeImgUrlByLevel(carCreditRankLevel, 2);
        }
        return null;
    }
}
