package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.service.CsComplaintRecordResultServcie;
import com.tyt.model.User;
import com.tyt.plat.mapper.base.CsComplaintRecordResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("csComplaintRecordResultServcie")
public class CsComplaintRecordResultServcieImpl implements CsComplaintRecordResultServcie {


    @Autowired
    private CsComplaintRecordResultMapper csComplaintRecordResultMapper;

    @Override
    public int getCountByWrongReason(User user) {
        return csComplaintRecordResultMapper.selectCountByReason(user.getCellPhone());
    }
}
