package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.bean.TransportOrders;
import com.tyt.acvitity.service.TransportOrdersRiskService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.model.TransportOrdersRisk;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

@Service
@Slf4j
public class TransportOrdersRiskServiceImpl extends BaseServiceImpl<TransportOrdersRisk, Long> implements TransportOrdersRiskService {

    @Resource(name = "transportOrdersRiskDao")
    public void setBaseDao(BaseDao<TransportOrdersRisk, Long> transportOrdersRiskDao) {
        super.setBaseDao(transportOrdersRiskDao);
    }

    @Override
    public List<TransportOrders> getGoodOrderList(Long userId, Integer type, Long queryId, Integer queryActionType, Integer classify) {
        List<Object> list = new ArrayList<Object>();
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");
        //int pageSize =3;
        StringBuffer sql = new StringBuffer("select" +
                " t.ts_order_no as tsOrderNo, " +
                " t.start_point as startPoint, " +
                " t.dest_point as destPoint, " +
                " t.task_content as taskContent, " +
                " t.pay_user_name as payUserName, " +
                " t.head_city as headCity, " +
                " t.head_no as headNo, " +
                " t.tail_city as tailCity, " +
                " a.activity_status as activityStatus, " +
                " a.appeal_status as appealStatus, " +
                " a.reject_type as rejectType, " +
                " a.id as stimulateId, " +
                " a.remark as remark, " +
                " a.appeal_evidence as appealEvidence, " +
                " t.tail_no as tailNo," +
                " a.good_status as goodStatus, " +
                " a.car_status as carStatus, " +
                " a.before_type as beforeType, " +
                " s.weight as weight, " +
                " s.length as length, " +
                " s.wide as wide, " +
                " s.high as high, " +
                " t.machine_remark as machineRemark " +
                " from tyt_transport_orders_risk a " +
                " left join tyt_transport_orders t on a.order_number = t.id " +
                " left join tyt_transport_main s on t.ts_id = s.id " +
                " where a.activity_status in (1,2,3) "
        );
        if (null != type && type == 1) {
            sql.append(" and a.user_id = ?");
        } else if (null != type && type == 2) {
            sql.append(" and a.car_user_id = ?");
        }
        if (null != classify && classify > 0) {
            sql.append(" and a.excellent_goods = 1");
        }
        list.add(userId);
        if (queryActionType == 2) {
            sql.append(" and a.id < ?");
            list.add(queryId);
        }
        sql.append(" order by a.id desc ");
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("payUserName", Hibernate.STRING);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        scalarMap.put("activityStatus", Hibernate.INTEGER);
        scalarMap.put("appealStatus", Hibernate.INTEGER);
        scalarMap.put("rejectType", Hibernate.INTEGER);
        scalarMap.put("stimulateId", Hibernate.LONG);
        scalarMap.put("remark", Hibernate.STRING);
        scalarMap.put("appealEvidence", Hibernate.INTEGER);
        scalarMap.put("goodStatus", Hibernate.INTEGER);
        scalarMap.put("carStatus", Hibernate.INTEGER);
        scalarMap.put("beforeType", Hibernate.INTEGER);
        scalarMap.put("weight", Hibernate.STRING);
        scalarMap.put("length", Hibernate.STRING);
        scalarMap.put("wide", Hibernate.STRING);
        scalarMap.put("high", Hibernate.STRING);
        scalarMap.put("machineRemark", Hibernate.STRING);
        List<TransportOrders> complaintOrdersList = this.getBaseDao().search(sql.toString(), scalarMap, TransportOrders.class, list.toArray(), 1, pageSize);
        return complaintOrdersList;
    }


    @Override
    public TransportOrdersRisk getByRiskId(Long id) {
        final List<TransportOrdersRisk> activityList = this.getBaseDao().find("from TransportOrdersRisk where id = ?", id.intValue());
        if (CollectionUtils.isEmpty(activityList)) {
            return null;
        }
        return activityList.get(0);
    }

    @Override
    public Long getCountByTime(Date startTime, Date endTime, Long userId) {
        String sql = "select count(0) from tyt_transport_orders_risk  where pay_end_time >= ? and pay_end_time <= ? and car_user_id= ? and activity_status in(0,3) ";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{startTime, endTime, userId});
        return count.longValue();

    }

    @Override
    public Integer getGoodsCountByTime(Date startTime, Date endTime, Long userId) {
        String sql = "select count(0) from tyt_transport_orders_risk  where  create_time>= ? and create_time <= ? and user_id= ? and activity_status in(0,3) ";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{startTime, endTime, userId});
        return count.intValue();

    }

    @Override
    public Integer getOrderForTransaction(Long userId, String pubMonth) {
        String sql = "select count(1) from tyt_transport_orders_risk " +
                "where excellent_goods = 1 and activity_status in (0, 3) and user_id = ? " +
                "and create_time like ?";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{userId, '%' + pubMonth + '%'});

        return count == null ? 0 : count.intValue();
    }
}
