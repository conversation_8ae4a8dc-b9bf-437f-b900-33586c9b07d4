package com.tyt.acvitity.service.impl;


import com.tyt.acvitity.service.LotteryUserService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.LotteryUser;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Service("lotteryUserService")
public class LotteryUserServiceImpl extends BaseServiceImpl<LotteryUser, Long> implements LotteryUserService {
    @Override
    @Resource(name = "lotteryUserDao")
    public void setBaseDao(BaseDao<LotteryUser, Long> dao) {
        super.setBaseDao(dao);
    }
}
