package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.service.ActivityPrizeService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.ActivityPrize;
import com.tyt.mybatis.mapper.ActivityPrizeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("activityPrizeService")
public class ActivityPrizeServiceImpl extends BaseServiceImpl<ActivityPrize, Long> implements ActivityPrizeService {

    @Autowired
    private ActivityPrizeMapper activityPrizeMapper;


    @Override
    public String getCode(long id){
        return activityPrizeMapper.getCode(id);

    }
}
