package com.tyt.acvitity.service.impl;

import com.tyt.acvitity.service.ActivityUserAddressService;
import com.tyt.infofee.bean.ActivityUserAddressBean;
import com.tyt.model.MarketingActivityUser;
import com.tyt.model.TytActivityUserAddress;
import com.tyt.model.User;
import com.tyt.mybatis.mapper.ActivityUserAddressMapper;

import com.tyt.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service("activityUserAddressService")
public class ActivityUserAddressServiceImpl implements ActivityUserAddressService {
    @Autowired
    private ActivityUserAddressMapper activityUserAddressMapper;
    @Autowired
    private UserService userService;

    @Override
    public TytActivityUserAddress getActivityUserAddressByUserId(Long userId) {
        return activityUserAddressMapper.selectActivityUserAddress(userId);
    }

    @Override
    public int selectActivityUserAddressCount(Long userId, Long activityId) {
        return activityUserAddressMapper.selectActivityUserAddressCount(userId,activityId);
    }

    @Override
    public Integer updateActivityUserAddress(ActivityUserAddressBean activityUserAddress) {
        return activityUserAddressMapper.updateActivityUserAddress(activityUserAddress);
    }

    @Override
    public void addActivityUserAddress(ActivityUserAddressBean activityUserAddress) {
      activityUserAddressMapper.addActivityUserAddress(activityUserAddress);
    }

    @Override
    public void insertActivityInfoUser(Long exposureAppointId,Long userId)throws Exception{
        User user = userService.getByUserId(userId);
        MarketingActivityUser marketingActivityUser = new MarketingActivityUser();
        marketingActivityUser.setUserId(userId);
        marketingActivityUser.setUserGrade(null);
        if ( user != null){
            marketingActivityUser.setUserCellPhone(user.getCellPhone());
        }
        marketingActivityUser.setActivityId(exposureAppointId);
        marketingActivityUser.setIsDelete(1);
        marketingActivityUser.setIsJoin(1);
        marketingActivityUser.setIsSendPush(2);
        marketingActivityUser.setOperater(null);
        marketingActivityUser.setCtime(new Date());
        marketingActivityUser.setMtime(new Date());
        activityUserAddressMapper.insertActivityInfoUser(marketingActivityUser);
    }
}
