package com.tyt.acvitity.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.PlatUserBoonAwardInfo;
import com.tyt.model.PlatUserInviteAwardInfo;
import com.tyt.model.ResultMsgBean;

import java.util.Map;

/**
 * 老用户拉新充值活动service
 * <AUTHOR>
 *
 */
public interface PlatUserInviteAwardInfoService extends BaseService<PlatUserInviteAwardInfo, Long>{
        void getInviteAwardUserInfo(Long userId,ResultMsgBean result)throws Exception;
        void getInviteAwardStatisticsInfo(Long userId,ResultMsgBean result) throws Exception;
}
