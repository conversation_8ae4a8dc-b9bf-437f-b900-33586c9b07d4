package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.ActivityListResp;
import com.tyt.base.service.BaseService;
import com.tyt.model.Activity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/23 11:05
 */
public interface ActivityService extends BaseService<Activity, Long> {

    List<ActivityListResp> listByUserId(Long userId) throws Exception;


    int getPopUp(Long userId);

    int getCashBack(Long userId);
}
