package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.CarActivityDetail;
import com.tyt.acvitity.bean.CarActivityReq;
import com.tyt.model.DrawActivityInfo;
import com.tyt.model.MarketingActivity;
import com.tyt.model.TytTransportOrders;

import java.util.List;

public interface CarActivityDataService {

    /**
     * 通过用户id和运营活动id、抽奖活动id查询车主透传活动页面所需详情信息
     * @param param param
     * @param marketingActivityInfo 车主透传活动运营活动详细信息
     * @param drawActivityInfo 车主透传活动抽奖活动详细信息
     * @return ResultMsgBean
     */
    CarActivityDetail getCarAtivityDetail(CarActivityReq param, MarketingActivity marketingActivityInfo, DrawActivityInfo drawActivityInfo);

    /**
     * 通过用户id和运营活动id查询车主透传活动 活动单列表数据
     * @param param param
     * @return ResultMsgBean
     */
    List<TytTransportOrders> getCarAtivityOrderList(CarActivityReq param);

    /**
     * 通过用户id和运营活动id查询车主透传活动 活动单列表数据 -- 更换新表 -- 2023.04.24
     * @param param param
     * @return ResultMsgBean
     */
    List<TytTransportOrders> getCarAtivityOrderRiskList(Long activityId,Long userId,MarketingActivity activity);

    /**
     * 判断运营活动是否过期
     * @param marketingActivityInfo 车主透传活动运营活动详细信息
     * @return boolean
     */
    boolean checkMarketingActivityDate(MarketingActivity marketingActivityInfo);

    /**
     * 判断抽奖活动是否过期
     * @param param param
     * @param drawActivityInfo 车主透传活动抽奖活动详细信息
     * @return boolean
     */
    boolean checkLotteryActivityDate(CarActivityReq param, DrawActivityInfo drawActivityInfo);

    /**
     * 通过用户id和运营活动id、抽奖活动id判断该用户是否具有车主透传活动权限
     * @param param param
     * @param drawActivityInfo 车主透传活动抽奖活动详细信息
     * @return boolean
     */
    boolean checkCarActivityContainUser(CarActivityReq param, DrawActivityInfo drawActivityInfo);
}
