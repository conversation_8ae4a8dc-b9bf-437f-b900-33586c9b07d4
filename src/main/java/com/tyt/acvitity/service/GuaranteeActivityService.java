package com.tyt.acvitity.service;

import com.tyt.model.ResultMsgBean;

import java.util.List;

public interface GuaranteeActivityService {

    /**
     * @description 参加活动
     * @param userId userId
     * @return ResultMsgBean
     */
    ResultMsgBean joinGuaranteeActivity(Long userId);

    /**
     * 判断该用户是否已参与保障活动
     * @param userId userId
     * @return true：已参与，false：未参与
     */
    boolean isJoinGuaranteeActivityByUserId(Long userId);

}
