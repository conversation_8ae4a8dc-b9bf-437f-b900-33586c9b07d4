package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.CarActivityDetail;
import com.tyt.acvitity.bean.CarActivityReq;
import com.tyt.acvitity.bean.GrowthInfoVo;

public interface CarGrowthSystemService {
    /**
     * 获取抽奖信息 奖品
     *
     * @param carActivityReq
     * @return
     */
    CarActivityDetail getUserLuckyDrawInfo(CarActivityReq carActivityReq);

    /**
     * 获取车方成长体系信息
     *
     * @param userId
     * @return
     */
    GrowthInfoVo growthSystemInfo(Long userId);
}
