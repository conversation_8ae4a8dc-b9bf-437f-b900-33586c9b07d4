package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.ConventionOrdersCensusVo;
import com.tyt.acvitity.bean.ConventionRankListVo;
import com.tyt.base.service.BaseService;
import com.tyt.model.ConventionOrdersCensus;

import java.util.List;

/**
 * 活动统计service
 */
public interface ConventionOrdersCensusService extends BaseService<ConventionOrdersCensus, Long> {

    /**
     * 查询当前进度和排名个列表
     * @param userId
     * @return
     */
    ConventionRankListVo getCurrentOrderNumAndRankList(Long userId,Long activityId);

    /**
     * 查询中奖记录
     * @param userId
     * @return
     */
    List<ConventionOrdersCensusVo> getPrizeLog(Long userId,Long activityId);

}
