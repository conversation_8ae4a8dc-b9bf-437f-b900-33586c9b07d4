package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.ActivityGradePrizeVo;
import com.tyt.base.service.BaseService;
import com.tyt.model.ActivityGradePrize;

import java.util.List;

public interface ActivityGradePrizeService extends BaseService<ActivityGradePrize, Long> {
    List<ActivityGradePrizeVo> getByActivityIdAndGrade(Long id, Integer userGrade,Long userId);

    void sendMq(Long userId, String cellPhone, Integer type);

    Boolean getUserAuth(Long userId);
}
