package com.tyt.acvitity.service;

import com.tyt.infofee.bean.ActivityUserAddressBean;
import com.tyt.model.TytActivityUserAddress;


public interface ActivityUserAddressService {
    /**
     * 获取用户收货地址
     * @param userId
     * @return
     */
     TytActivityUserAddress getActivityUserAddressByUserId(Long userId);


    /**
     * 获取已存在用户个数
     * @param userId
     * @param activityId
     * @return
     */
     int selectActivityUserAddressCount(Long userId,Long activityId);

    /**
     * 更新用户用户地址信息
     * @param activityUserAddress
     * @return
     */
    Integer updateActivityUserAddress(ActivityUserAddressBean activityUserAddress);


    /**
     * 插入用户收货地址信息
     * @param activityUserAddress
     * @return
     */
    void addActivityUserAddress(ActivityUserAddressBean activityUserAddress);

    void insertActivityInfoUser(Long exposureAppointId,Long userId) throws Exception;
}
