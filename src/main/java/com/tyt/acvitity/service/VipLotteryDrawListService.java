package com.tyt.acvitity.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.VipLotteryDrawList;

public interface VipLotteryDrawListService extends BaseService<VipLotteryDrawList, Long> {
    /**
     * 抽奖活动发奖
     * @param userId
     * @param activityId
     * @param msgBean
     * @return
     */
    ResultMsgBean updateLotteryDraw(Long userId, Long activityId,ResultMsgBean msgBean) throws Exception;

    /**
     * 获取我的奖品
     * @param userId
     * @param activityId
     * @param msgBean
     * @return
     */
    ResultMsgBean getMyLotteryPrize(Long userId, Long activityId, ResultMsgBean msgBean) throws Exception;
}
