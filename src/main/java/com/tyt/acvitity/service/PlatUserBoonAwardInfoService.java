package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.BoonAwardInfoBean;
import com.tyt.base.service.BaseService;
import com.tyt.model.PlatUserBoonAwardInfo;
/**
 * 回馈用户活动service
 * <AUTHOR>
 *
 */
public interface PlatUserBoonAwardInfoService extends BaseService<PlatUserBoonAwardInfo, Long>{

	/**
	 * 根据userID获取bean 
	 * @param userId
	 * @return
	 */
	PlatUserBoonAwardInfo getBeanByUserId(Long userId);

	/**
	 * 验证活动1参与情况
	 * @param userId
	 * @return
	 */
	BoonAwardInfoBean verifyAwardInfo(Long userId) throws Exception ;

}
