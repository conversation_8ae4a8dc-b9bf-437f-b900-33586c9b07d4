package com.tyt.acvitity.service;

import com.tyt.acvitity.bean.UserCenterExposureInfo;
import com.tyt.acvitity.bean.UserExposurePermissionCountVo;
import com.tyt.common.bean.PageData;
import com.tyt.plat.entity.base.ExposurePermissionExpiredRecord;
import com.tyt.plat.entity.base.ExposurePermissionUsedRecord;
import com.tyt.plat.entity.base.ExposurePermissionGainRecord;

import java.util.List;

public interface ExposurePermissionService {

    /**
     * 查询曝光权益获取记录
     * @param userId userId
     * @param pageNum pageNum
     * @param pageSize pageSize
     * @return PageData ExposurePermissionGainRecord
     */
    PageData<ExposurePermissionGainRecord> getExposurePermissionGainRecordListByUserId(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 查询曝光权益使用记录
     * @param userId userId
     * @param pageNum pageNum
     * @param pageSize pageSize
     * @return PageData ExposurePermissionGainRecord
     */
    PageData<ExposurePermissionUsedRecord> getExposurePermissionUsedRecordListByUserId(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 查询曝光权益过期记录
     * @param userId userId
     * @param pageNum pageNum
     * @param pageSize pageSize
     * @return PageData ExposurePermissionGainRecord
     */
    PageData<ExposurePermissionExpiredRecord> getExposurePermissionExpiredRecordListByUserId(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 查询用户拥有的可用货源曝光权益总次数
     * @param userId userId
     * @return UserExposurePermissionCountVo
     */
    UserExposurePermissionCountVo getUserExposurePermissionCount(Long userId);

    /**
     * 保存曝光权益使用记录
     * @param usedRecord
     * @return
     */
    int saveExposurePermissionUsedRecord(ExposurePermissionUsedRecord usedRecord);

    /**
     * 获取个人中心曝光卡信息
     * @param userId
     * @return
     */
    List<UserCenterExposureInfo> getUserCenterExposureInfo(Long userId);
}
