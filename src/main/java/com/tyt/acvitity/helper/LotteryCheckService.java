package com.tyt.acvitity.helper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tyt.acvitity.constant.LotteryBizCheckSignEnum;
import com.tyt.callPhoneRecord.service.CallPhoneRecordService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.DrawActivityInfo;
import com.tyt.model.ResultMsgBean;
import com.tyt.mybatis.mapper.ConventionActivityMapper;
import com.tyt.plat.entity.base.LotteryRecord;
import com.tyt.plat.mapper.base.LotteryRecordMapper;
import com.tyt.plat.mapper.base.LotteryUserMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/03/17
 */
@Service
@Slf4j
public class LotteryCheckService implements InitializingBean {
    //0 = 普通活动 1 = 车方成长体系抽奖
    private Integer orderStimulateExecuteType = 0;

    public Integer getOrderStimulateExecuteType() {
        return orderStimulateExecuteType;
    }

    public void setOrderStimulateExecuteType(Integer orderStimulateExecuteType) {
        this.orderStimulateExecuteType = orderStimulateExecuteType;
    }

    private static final String LOTTERY_CAR_CALL_PHONE_COUNT_REDIS_KEY = "lottery:car_call_phone_count:userId:%d";

    private final LotteryRecordMapper lotteryRecordMapper;
    private final LotteryUserMapper lotteryUserMapper;
    private final ConventionActivityMapper conventionActivityMapper;
    private final CallPhoneRecordService callPhoneRecordService;
    @Autowired
    private  TransportOrdersService transportOrdersService;

    // 所有抽奖都适用的通用检测
    private final List<Function<LotteryCheckRequest, ResultMsgBean>> basicCheck = Lists.newArrayList();
    // 针对不同策略的检测
    private final Map<String, Function<LotteryCheckRequest, ResultMsgBean>> bizCheck = Maps.newHashMap();

    public LotteryCheckService(LotteryRecordMapper lotteryRecordMapper, LotteryUserMapper lotteryUserMapper,
                               ConventionActivityMapper conventionActivityMapper,
                               CallPhoneRecordService callPhoneRecordService) {
        this.lotteryRecordMapper = lotteryRecordMapper;
        this.lotteryUserMapper = lotteryUserMapper;
        this.conventionActivityMapper = conventionActivityMapper;
        this.callPhoneRecordService = callPhoneRecordService;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 判断活动是否过期或无效
        basicCheck.add(this::checkLotterySelf);
        // 根据抽奖的次数类型判断抽奖次数是否已经用完
        basicCheck.add(this::checkDrawTimes);

        // 用户在活动名单中才能抽奖
        bizCheck.put(LotteryBizCheckSignEnum.USER_IN_LOTTERY_LIST.getBizCheckSign(),
                this::checkLotteryUser);

        // 履约订单校验，每履约一单，可以抽一次
        bizCheck.put(LotteryBizCheckSignEnum.ORDER_STIMULATE_ONCE_PRE_ORDER.getBizCheckSign(),
                this::checkOrderStimulate);

        // 拨打过货方电话才能抽奖
        bizCheck.put(LotteryBizCheckSignEnum.NEED_CALL_PUBLISHER.getBizCheckSign(),
                this::checkNeedCallPublisher);
    }

    /**
     * 校验入口，校验是否能抽奖
     */
    public ResultMsgBean checkLottery(Long userId, DrawActivityInfo drawActivityInfo,
                                      Long agreementActivityId) {

        if (userId == null || drawActivityInfo == null) {
            return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_IS_INVALID, "活动已无效");
        }

        LotteryCheckRequest lotteryCheckRequest = LotteryCheckRequest.builder()
                .userId(userId)
                .drawActivityInfo(drawActivityInfo)
                .agreementActivityId(agreementActivityId)
                .build();
        // 通用校验
        for (Function<LotteryCheckRequest, ResultMsgBean> block : basicCheck) {
            ResultMsgBean result = block.apply(lotteryCheckRequest);
            // 校验失败返回失败的校验信息
            if (result == null || !result.isSuccess()) {
                return result;
            }
        }

        // 业务校验
        String bizCheckSign = drawActivityInfo.getBizCheckSign();
        List<String> bizCheckSigns;
        if (StringUtils.isNotBlank(bizCheckSign)) {
            bizCheckSigns = Arrays.asList(bizCheckSign.split(" *[,，] *"));
        } else {
            bizCheckSigns = Collections.emptyList();
        }

        List<Function<LotteryCheckRequest, ResultMsgBean>> bizChecks = bizCheckSigns.stream()
                .map(bizCheck::get).filter(Objects::nonNull).collect(Collectors.toList());

        for (Function<LotteryCheckRequest, ResultMsgBean> block : bizChecks) {
            ResultMsgBean result = block.apply(lotteryCheckRequest);
            // 校验失败返回失败的校验信息
            if (result == null || !result.isSuccess()) {
                return result;
            }
        }

        return ResultMsgBean.successResponse();
    }

    /**
     * 判断活动是否过期或无效
     */
    public ResultMsgBean checkLotterySelf(LotteryCheckRequest request) {
        DrawActivityInfo drawActivityInfo = request.getDrawActivityInfo();
        // 判断活动是否有效
        if (null == drawActivityInfo || !DrawActivityInfo.DrawActivityInfoStatus.EFFECTIVE.getStatus()
                .equals(drawActivityInfo.getStatus())) {
            return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_IS_INVALID, "活动已无效");
        }
        if (drawActivityInfo.getIsDelete() != 1) {
            return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_IS_INVALID, "活动已无效");
        }

        Date now = new Date();
        // 判断活动是否过期
        if (drawActivityInfo.getEndTime().before(now)) {
            return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_HAS_EXPIRED, "活动已过期");
        }
        // 判断活动是否开始
        if (drawActivityInfo.getStartTime().after(now)) {
            return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_INACTIVE_NOT_START, "活动未开始");
        }

        return ResultMsgBean.successResponse();
    }

    /**
     * 判断用户是否在参与名单中
     */
    public ResultMsgBean checkLotteryUser(LotteryCheckRequest request) {
        Long userId = request.getUserId();
        DrawActivityInfo drawActivityInfo = request.getDrawActivityInfo();

        long count = lotteryUserMapper.countByUserIdAndDrawActivityId(userId, drawActivityInfo.getId());

        // 没资格
        if (count == 0) {
            return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_NOT_PERM, "没有活动参与资格");
        }

        return ResultMsgBean.successResponse();
    }


    /**
     * 根据抽奖的次数类型判断抽奖次数是否已经用完
     */
    private ResultMsgBean checkDrawTimes(LotteryCheckRequest request) {
        Long userId = request.getUserId();
        DrawActivityInfo drawActivityInfo = request.getDrawActivityInfo();

        Example example = new Example(LotteryRecord.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId)
                .andEqualTo("drawActivityInfoId", drawActivityInfo.getId());
        // 按天类型额外增加条件
        if (DrawActivityInfo.DrawActivityInfoLimitTimesType.BY_DAY.getCode()
                .equals(drawActivityInfo.getLimitTimesType())) {
            criteria.andGreaterThan("createTime", TimeUtil.today());
        }
        int count = lotteryRecordMapper.selectCountByExample(example);

        if (count >= drawActivityInfo.getLimitTimes()) {
            if (DrawActivityInfo.DrawActivityInfoLimitTimesType.BY_DAY.getCode()
                    .equals(drawActivityInfo.getLimitTimesType())) {
                return new ResultMsgBean(ReturnCodeConstant.OPPORTUNITY_USED_TODAY,
                        "今天机会已用完，明天再来");
            } else {
                return new ResultMsgBean(ReturnCodeConstant.COUNT_USED, "您的次数已用完");
            }
        }
        return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
    }

    /**
     * 履约订单校验，每履约一单，可以抽一次
     */
    private ResultMsgBean checkOrderStimulate(LotteryCheckRequest request) {
        Long userId = request.getUserId();
        DrawActivityInfo drawActivityInfo = request.getDrawActivityInfo();

        Example example = new Example(LotteryRecord.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId)
                .andEqualTo("drawActivityInfoId", drawActivityInfo.getId());

        int count = lotteryRecordMapper.selectCountByExample(example);

        //查询用户履约单数
        Long orderNum = null;
        if (this.getOrderStimulateExecuteType() ==1) {
            orderNum = transportOrdersService.getCompletedOrderNum(userId, TimeUtil.getStartOfDay(new Date()), new Date());
        }else {
            orderNum = conventionActivityMapper.selectOrderNum(request.getAgreementActivityId(), userId);
        }

        if (orderNum == null) {
            orderNum = 0L;
        }

        //默认未履约用户有一次抽奖机会
        orderNum += 1;

        if (orderNum <= count) {
            return new ResultMsgBean(ReturnCodeConstant.COUNT_USED, "您的次数已用完，履约完单获取更多。");
        }
        return ResultMsgBean.successResponse();
    }

    /**
     * 拨打过货方电话才能抽奖
     */
    private ResultMsgBean checkNeedCallPublisher(LotteryCheckRequest request) {
        DrawActivityInfo drawActivityInfo = request.getDrawActivityInfo();

        String key = String.format(LOTTERY_CAR_CALL_PHONE_COUNT_REDIS_KEY, request.getUserId());
        Long cachedCount = getFromCache(key, () -> {
            long count = callPhoneRecordService.countByCarUserIdAndTimeBetween(request.getUserId(),
                    drawActivityInfo.getStartTime(), drawActivityInfo.getEndTime());

            // 没有拨打过返回空，不走缓存
            return count == 0 ? null : count;
        }, 7L, TimeUnit.DAYS, false);

        if (cachedCount == null || cachedCount <= 0) {
            return new ResultMsgBean(ReturnCodeConstant.ACTIVITY_NOT_PERM_NEED_CALL_PUBLISHER,
                    "联系货主，即可抽奖");
        }
        return ResultMsgBean.successResponse();
    }

    /**
     * 从缓存中获取
     *
     * @param key       key
     * @param callable  缓存未命中执行的逻辑
     * @param cacheTime 缓存时间
     * @param timeUnit  时间单位
     * @param cacheNull 是否缓存null
     * @return 缓存结果
     */
    private <T> T getFromCache(String key, Callable<T> callable, Long cacheTime, TimeUnit timeUnit,
                               boolean cacheNull) {
        T value = RedisUtil.getObject(key);
        if (value == null) {
            try {
                value = callable.call();
            } catch (Exception e) {
                log.error("缓存计算异常", e);
                throw new RuntimeException(e);
            }

            // value不为空或缓存空时才set
            if (value != null || cacheNull) {
                RedisUtil.setObject(key, value, (int) timeUnit.toSeconds(cacheTime));
            }
        }
        return value;
    }
}
