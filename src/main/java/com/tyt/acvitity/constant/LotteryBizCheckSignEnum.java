package com.tyt.acvitity.constant;

/**
 * <AUTHOR>
 * @since 2023/03/17
 */
public enum LotteryBizCheckSignEnum {
    ORDER_STIMULATE_ONCE_PRE_ORDER("order_stimulate_once_pre_order", "履约订单校验，每履约一单，可以抽一次"),
    USER_IN_LOTTERY_LIST("user_in_lottery_list", "用户在活动名单中才能抽奖"),
    NEED_CALL_PUBLISHER("need_call_publisher", "拨打过货方电话才能抽奖"),

    ;
    private final String bizCheckSign;
    private final String desc;

    LotteryBizCheckSignEnum(String bizCheckSign, String desc) {
        this.bizCheckSign = bizCheckSign;
        this.desc = desc;
    }

    public String getBizCheckSign() {
        return bizCheckSign;
    }

    public String getDesc() {
        return desc;
    }
}
