package com.tyt.acvitity.constant;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/4/25
 */
public enum CarCreditUpgradePopupTyptEnum {

    SHOW_LEVEL(1, "等级展示弹窗 "),
    GROW_LEVEL_UPGRADATION(2, "成长等级升级弹窗（v1、v2 升V3）");

    private final Integer type;
    private final String desc;

    CarCreditUpgradePopupTyptEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
