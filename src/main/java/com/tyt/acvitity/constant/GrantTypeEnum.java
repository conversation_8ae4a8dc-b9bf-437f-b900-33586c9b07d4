package com.tyt.acvitity.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 发放条件
 *
 * <AUTHOR>
 * @since 2024/03/27 11:17
 */
@Getter
public enum GrantTypeEnum {
    /**
     * 无条件发放
     */
    ALL("ALL", "无条件发放"),
    /**
     * 发货
     */
    RELEASE_GOODS("RELEASE_GOODS", "发货"),
    /**
     * 成交
     */
    TRANSACTION_SUCCESS("TRANSACTION_SUCCESS", "成交"),
    /**
     * 履约
     */
    TRANSACTION_FINISH("TRANSACTION_FINISH", "履约"),

    /**
     * 用户注册(全部)
     */
    REGISTER_ALL("REGISTER_ALL", "用户注册(全部)"),
    ;
    private final String type;
    private final String desc;

    GrantTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static GrantTypeEnum getByType(String type) {
        return Arrays.stream(values()).filter(it -> StringUtils.equals(type, it.type)).findFirst().orElseThrow(() -> new IllegalArgumentException("type 有误"));
    }
}
