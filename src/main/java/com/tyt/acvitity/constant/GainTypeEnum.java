package com.tyt.acvitity.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 权益类型
 *
 * <AUTHOR>
 * @since 2024/03/27 11:17
 */
@Getter
public enum GainTypeEnum {
    /**
     * 优车发货卡
     */
    EXCELLENT_GOODS_CARDS("EXCELLENT_GOODS_CARDS", "优车发货卡"),
    /**
     * 普通发货卡
     */
    NORMAL_GOODS_CARDS("NORMAL_GOODS_CARDS", "普通发货卡"),
    ;
    private final String type;
    private final String desc;

    GainTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static GainTypeEnum getByType(String type) {
        return Arrays.stream(values()).filter(it -> StringUtils.equals(type, it.type)).findFirst().orElseThrow(() -> new IllegalArgumentException("type 有误"));
    }
}
