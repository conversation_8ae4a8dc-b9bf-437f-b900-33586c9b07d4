package com.tyt.acvitity.dao;

import com.tyt.acvitity.bean.AwardUserBean;
import com.tyt.acvitity.bean.AwardUserCountBean;
import com.tyt.acvitity.bean.InviteUserBean;
import com.tyt.base.dao.BaseDao;
import com.tyt.model.PlatUserInviteAwardInfo;

import java.util.List;
import java.util.Map;

/**
 * 老用户拉新充值活动dao
 * <AUTHOR>
 *
 */
public interface PlatUserInviteAwardInfoDao extends BaseDao<PlatUserInviteAwardInfo, Long>{

    // 开通人数,获奖总数-
    AwardUserCountBean getAwardUserCountAndAwardDaysByUserId(Long userId);
    // 总奖励人数
    Integer getTotalAwardCount();
    // 最新奖励用户TOP30-
    List<AwardUserBean> awardUserList(Integer pageSize);
    // 未开通列表TOP15-
    List<InviteUserBean> noPayUserListByUserId(Long userId,Integer pageSize);
    // 已开通列表TOP15-
    List<InviteUserBean> payUserListByUserId(Long userId,Integer pageSize);
    // 邀请总数
    Integer getTotalAwardCountByUserId(Long userId);
    // 人数最大数量
    Integer getInvitePeopleNumberMax();
    // 奖励最大数量
    Integer getAwardDaysMaxSum();
}
