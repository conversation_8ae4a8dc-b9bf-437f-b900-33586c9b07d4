package com.tyt.acvitity.dao;

import com.tyt.acvitity.bean.AwardInfo;
import com.tyt.base.dao.BaseDao;
import com.tyt.model.ProbCoupon;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
public interface ProdCouponDao extends BaseDao<ProbCoupon, Long>{

    /**
     * 修改奖品的使用数量
     * @param probCoupon
     */
    void updateAwardUsedAmount(ProbCoupon probCoupon);

    /**
     * 根据抽奖活动id 获取奖品信息
     * @param drawActivityInfoId 抽奖活动id
     * @return 奖品名称、奖品分类
     */
    List<AwardInfo> getAwardInfoByDrawActivityInfoId(Long drawActivityInfoId);

    /**
     * 查询转盘活动奖品列表
     * @param drawId 抽奖活动id
     * @return 奖品名称、奖品分类
     */
    List<ProbCoupon> getByDrawid(Long drawId);
}
