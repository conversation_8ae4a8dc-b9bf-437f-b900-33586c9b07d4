package com.tyt.acvitity.dao.impl;

import com.tyt.acvitity.dao.UserActivityDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.UserActivity;
import org.springframework.stereotype.Repository;

/**
 * 回馈用户活动dao
 * <AUTHOR>
 *
 */
@Repository("userActivityDao")
public class UserActivityDaoImpl extends BaseDaoImpl<UserActivity, Long> implements UserActivityDao {

    public UserActivityDaoImpl() {
        super.setEntityClass(UserActivity.class);
    }
}
