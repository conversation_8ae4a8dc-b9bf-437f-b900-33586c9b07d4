package com.tyt.acvitity.dao.impl;

import com.tyt.acvitity.bean.AwardUserBean;
import com.tyt.acvitity.bean.AwardUserCountBean;
import com.tyt.acvitity.bean.InviteUserBean;
import com.tyt.acvitity.dao.PlatUserInviteAwardInfoDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.PlatUserInviteAwardInfo;
import com.tyt.transport.querybean.ProvincesCountBean;
import com.tyt.util.Constant;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository("platUserInviteAwardInfoDao")
public class PlatUserInviteAwardInfoDaoImpl extends BaseDaoImpl<PlatUserInviteAwardInfo, Long> implements PlatUserInviteAwardInfoDao {

	public PlatUserInviteAwardInfoDaoImpl() {
		this.setEntityClass(PlatUserInviteAwardInfo.class);
	}

	@Override
	public AwardUserCountBean getAwardUserCountAndAwardDaysByUserId(Long userId) {
		String sql = "SELECT user_id userId,COUNT(1) peopleNumberSum,SUM(t.`award_days`) awardDaysSum FROM `plat_user_invite_award_info` t WHERE t.`user_id` = :userId AND t.`user_state` = 2 GROUP BY user_id;";
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userId", userId);

		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
		mapType.put("userId", Hibernate.STRING);
		mapType.put("peopleNumberSum", Hibernate.STRING);
		mapType.put("awardDaysSum", Hibernate.STRING);
		List<AwardUserCountBean> result = this.search(sql,mapType,AwardUserCountBean.class,map);
		if(result != null && result.size() > 0){
			return result.get(0);
		}else{
			//没有返回为0
			AwardUserCountBean awardUserCountBean = new AwardUserCountBean();
			awardUserCountBean.setAwardDaysSum("0");
			awardUserCountBean.setPeopleNumberSum("0");
			return awardUserCountBean;
		}
	}

	@Override
	public Integer getTotalAwardCount() {
		String sql = "SELECT COUNT(user_id) userCount FROM `plat_user_invite_award_info` t WHERE t.user_state = 2";
		BigInteger c = this.query(sql, new Object[] { });
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}

	@Override
	public List<AwardUserBean> awardUserList(Integer pageSize) {
		String sql = "SELECT u.`user_name` userName,u.`head_url` headUrl,u1.`user_name` inviteUserName,u1.`head_url` inviteHeadUrl,t.`award_days` awardDays FROM `plat_user_invite_award_info` t LEFT JOIN tyt_user u ON t.`user_id` = u.`id` LEFT JOIN tyt_user u1 ON t.`invite_user_id` = u1.`id` WHERE t.`user_state` = 2 ORDER BY t.mtime DESC LIMIT 0,"+pageSize+";";
		Map<String, Object> map = new HashMap<String, Object>();

		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
		mapType.put("userName", Hibernate.STRING);
		mapType.put("headUrl", Hibernate.STRING);
		mapType.put("inviteUserName", Hibernate.STRING);
		mapType.put("inviteHeadUrl", Hibernate.STRING);
		mapType.put("awardDays", Hibernate.STRING);
		List<AwardUserBean> result = this.search(sql,mapType,AwardUserBean.class,map);
		return result;
	}

	@Override
	public List<InviteUserBean> noPayUserListByUserId(Long userId, Integer pageSize) {
		String sql = "SELECT t.`invite_user_name` inviteUserName,t.`invite_cell_phone` inviteCellPhone FROM `plat_user_invite_award_info` t WHERE t.`user_id` = :userId AND t.`user_state` = 1 ORDER BY ctime DESC LIMIT 0,"+pageSize+";";
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userId", userId);

		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
		mapType.put("inviteUserName", Hibernate.STRING);
		mapType.put("inviteCellPhone", Hibernate.STRING);
		List<InviteUserBean> result = this.search(sql,mapType,InviteUserBean.class,map);
		return result;
	}

	@Override
	public List<InviteUserBean> payUserListByUserId(Long userId, Integer pageSize) {
		String sql = "SELECT t.`invite_user_name` inviteUserName,t.`invite_cell_phone` inviteCellPhone FROM `plat_user_invite_award_info` t WHERE t.`user_id` = :userId AND t.`user_state` = 2 ORDER BY mtime DESC LIMIT 0,"+pageSize+";";
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userId", userId);

		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
		mapType.put("inviteUserName", Hibernate.STRING);
		mapType.put("inviteCellPhone", Hibernate.STRING);
		List<InviteUserBean> result = this.search(sql,mapType,InviteUserBean.class,map);
		return result;
	}

	@Override
	public Integer getTotalAwardCountByUserId(Long userId) {
		String sql = "SELECT COUNT(1) numbers FROM `plat_user_invite_award_info` t WHERE t.`user_id` = ? ";
		BigInteger c = this.query(sql, new Object[] { userId});
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}

	@Override
	public Integer getInvitePeopleNumberMax() {
		String sql = "SELECT COUNT(1) numbers FROM `plat_user_invite_award_info` t GROUP BY user_id ORDER BY 1 DESC LIMIT 0,1";
		BigInteger c = this.query(sql, new Object[] {});
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}

	@Override
	public Integer getAwardDaysMaxSum() {
		String sql = "SELECT SUM(t.`award_days`) days FROM `plat_user_invite_award_info` t where t.user_state = 2 GROUP BY user_id ORDER BY 1 DESC LIMIT 0,1";
        BigDecimal c = this.query(sql, new Object[] {});
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}
}
