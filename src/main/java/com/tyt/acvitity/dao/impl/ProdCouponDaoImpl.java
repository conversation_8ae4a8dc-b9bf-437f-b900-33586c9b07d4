package com.tyt.acvitity.dao.impl;

import com.tyt.acvitity.bean.AwardInfo;
import com.tyt.acvitity.bean.InviteUserBean;
import com.tyt.acvitity.dao.ProdCouponDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.ProbCoupon;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Repository("prodCouponDao")
public class ProdCouponDaoImpl extends BaseDaoImpl<ProbCoupon, Long> implements ProdCouponDao {

    public ProdCouponDaoImpl() {
        super.setEntityClass(ProbCoupon.class);
    }

    @Override
    public void updateAwardUsedAmount(ProbCoupon probCoupon) {
        String sql = "UPDATE prob_coupon SET award_used_amount = (award_used_amount + 1), update_time = now(), update_by = ? where id = ?";
        this.executeUpdateSql(sql, new Object[] {probCoupon.getUpdateBy(), probCoupon.getId()});
    }

    @Override
    public List<AwardInfo> getAwardInfoByDrawActivityInfoId(Long drawActivityInfoId) {
        String sql = "SELECT activity_name AS awardName,award_type AS awardType FROM prob_coupon WHERE draw_activity_info_id =:drawActivityInfoId AND is_delete =0";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("drawActivityInfoId", drawActivityInfoId);

        Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
        mapType.put("awardName", Hibernate.STRING);
        mapType.put("awardType", Hibernate.INTEGER);
        List<AwardInfo> result = this.search(sql,mapType,AwardInfo.class,map);
        return result;
    }

    /**
     * 查询抽奖活动数据
     *
     * @param drawId 抽奖活动id
     * @return List<ProbCoupon>
     */
    @Override
    public List<ProbCoupon> getByDrawid(Long drawId) {
        String sql = "SELECT " +
                "id, " +
                "draw_activity_info_id AS drawActivityInfoId, " +
                "promo_activity_id AS promoActivityId, " +
                "activity_name AS activityName, " +
                "award_type AS awardType, " +
                "award_prob AS awardProb, " +
                "award_total_amount AS awardTotalAmount, " +
                "award_used_amount AS awardUsedAmount, " +
                "sort " +
                "FROM prob_coupon " +
                "WHERE draw_activity_info_id = ? AND is_delete =0 order by sort";
        List<Object> list = new ArrayList<>();
        list.add(drawId);

        Map<String, org.hibernate.type.Type> mapType = new HashMap<>();
        mapType.put("id", Hibernate.LONG);
        mapType.put("drawActivityInfoId", Hibernate.LONG);
        mapType.put("promoActivityId", Hibernate.LONG);
        mapType.put("activityName", Hibernate.STRING);
        mapType.put("awardType", Hibernate.INTEGER);
        mapType.put("awardProb", Hibernate.BIG_DECIMAL);
        mapType.put("awardTotalAmount", Hibernate.INTEGER);
        mapType.put("awardUsedAmount", Hibernate.INTEGER);
        mapType.put("sort", Hibernate.INTEGER);
        List<ProbCoupon> result = this.search(sql, mapType, ProbCoupon.class, list.toArray());
        return result;
    }
}
