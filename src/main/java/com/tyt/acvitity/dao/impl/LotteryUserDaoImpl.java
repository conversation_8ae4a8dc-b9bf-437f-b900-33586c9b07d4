package com.tyt.acvitity.dao.impl;

import com.tyt.acvitity.dao.LotteryUserDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.LotteryUser;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Repository("lotteryUserDao")
public class LotteryUserDaoImpl extends BaseDaoImpl<LotteryUser, Long> implements LotteryUserDao {

    public LotteryUserDaoImpl() {
        super.setEntityClass(LotteryUser.class);
    }

    @Override
    public void updateTakeIn(LotteryUser lotteryUser) {
        String sql = "UPDATE lottery_user SET take_in =?,total_draws_num = total_draws_num+1 WHERE user_id = ? and draw_activity_info_id = ?";
        this.executeUpdateSql(sql, new Object[] {lotteryUser.getTakeIn(), lotteryUser.getUserId(), lotteryUser.getDrawActivityInfoId()});
    }
}
