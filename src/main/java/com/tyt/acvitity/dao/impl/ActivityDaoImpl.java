package com.tyt.acvitity.dao.impl;

import com.tyt.acvitity.dao.ActivityDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.Activity;
import org.springframework.stereotype.Repository;

/**
 * 回馈用户活动dao
 * <AUTHOR>
 *
 */
@Repository("activityDao")
public class ActivityDaoImpl extends BaseDaoImpl<Activity, Long> implements ActivityDao {

    public ActivityDaoImpl() {
        super.setEntityClass(Activity.class);
    }
}
