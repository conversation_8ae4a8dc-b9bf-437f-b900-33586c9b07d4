package com.tyt.acvitity.dao.impl;

import com.tyt.acvitity.dao.LotteryRecordDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.LotteryRecord;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Repository("lotteryRecordDao")
public class LotteryRecordDaoImpl extends BaseDaoImpl<LotteryRecord, Long> implements LotteryRecordDao {

    public LotteryRecordDaoImpl() {
        super.setEntityClass(LotteryRecord.class);
    }

    @Override
    public List<LotteryRecord> getLotteryRecord(Long userId, Long activityId, Integer pageNo, Integer pageSize) {
        StringBuffer sql=new StringBuffer("SELECT user_id userId,user_call_phone userCallPhone,activity_name activityName,prob_coupon_name probCouponName,source,create_time createTime" +
                " FROM lottery_record WHERE user_id =? AND draw_activity_info_id =? AND is_win = 1 ORDER BY create_time desc ");
        List<Object> list = new ArrayList<Object>();
        list.add(userId);
        list.add(activityId);


        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("userCallPhone", Hibernate.STRING);
        scalarMap.put("activityName", Hibernate.STRING);
        scalarMap.put("probCouponName", Hibernate.STRING);
        scalarMap.put("source", Hibernate.INTEGER);
        scalarMap.put("createTime", Hibernate.TIMESTAMP);

        List<LotteryRecord> lotteryRecords = this.search(sql.toString(), scalarMap, LotteryRecord.class, list.toArray(), pageNo, pageSize);

        return lotteryRecords;
    }

    @Override
    public Integer getLotteryRecordNum(Long userId, Long activityId) {

        StringBuffer sql=new StringBuffer("SELECT user_id userId " +
                "FROM lottery_record WHERE user_id =? AND draw_activity_info_id =? AND is_win = 1 ");
        List<Object> list = new ArrayList<Object>();
        list.add(userId);
        list.add(activityId);

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("userId", Hibernate.LONG);
        List<LotteryRecord> lotteryRecords = this.search(sql.toString(), scalarMap, LotteryRecord.class, list.toArray());
        return CollectionUtils.isEmpty(lotteryRecords)?0:lotteryRecords.size();
    }

    /**
     * 查询用户当日是否已抽过奖
     * @param userId 用户id
     * @param id 抽奖活动id
     * @param startTime 当天0点
     * @param endTime 当天23点
     * @return Integer
     */
    @Override
    public Integer getNowLotter(Long userId, Long id, Date startTime, Date endTime) {
        String sql = "SELECT id " +
                "FROM lottery_record " +
                "WHERE user_id =? " +
                "AND draw_activity_info_id =? " +
                "AND is_win = 1 " +
                "and create_time BETWEEN ? and ? limit 1";

        List<Object> list = new ArrayList<>();
        list.add(userId);
        list.add(id);
        list.add(startTime);
        list.add(endTime);

        Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("id", Hibernate.LONG);
        List<LotteryRecord> lotteryRecords = this.search(sql, scalarMap, LotteryRecord.class, list.toArray());
        return CollectionUtils.isEmpty(lotteryRecords) ? 0 : 1;
    }
}
