package com.tyt.acvitity.dao.impl;


import com.tyt.acvitity.dao.PromoUserCouponDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.PromoUserCoupon;
import org.springframework.stereotype.Repository;

@Repository("promoUserCouponDao")
public class PromoUserCouponDaoImpl extends BaseDaoImpl<PromoUserCoupon, Integer> implements PromoUserCouponDao {

    public PromoUserCouponDaoImpl() {
        this.setEntityClass(PromoUserCoupon.class);
    }
}
