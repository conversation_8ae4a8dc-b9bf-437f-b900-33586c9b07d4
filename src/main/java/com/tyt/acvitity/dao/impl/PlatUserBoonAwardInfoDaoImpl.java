package com.tyt.acvitity.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.acvitity.dao.PlatUserBoonAwardInfoDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.PlatUserBoonAwardInfo;

@Repository("platUserBoonAwardInfoDao")
public class PlatUserBoonAwardInfoDaoImpl extends BaseDaoImpl<PlatUserBoonAwardInfo, Long> implements PlatUserBoonAwardInfoDao {

	public PlatUserBoonAwardInfoDaoImpl() {
		this.setEntityClass(PlatUserBoonAwardInfo.class);
	}
}
