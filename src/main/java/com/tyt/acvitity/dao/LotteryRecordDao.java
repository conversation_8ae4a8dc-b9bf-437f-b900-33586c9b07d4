package com.tyt.acvitity.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.LotteryRecord;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
public interface LotteryRecordDao extends BaseDao<LotteryRecord, Long>{

    List<LotteryRecord> getLotteryRecord(Long userId, Long activityId, Integer pageNo, Integer pageSize);

    Integer getLotteryRecordNum(Long userId, Long activityId);

    Integer getNowLotter(Long userId, Long id, Date startTime, Date endTime);
}
