package com.tyt.dispatch.owner.service;

import com.tyt.plat.entity.base.TytDispatchCargoOwner;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/26 11:33
 */
public interface DispatchOwnerService {
    /**
     * 获取授权状态
     * @param userId 用户id
     * @return int
     */
    Integer getEmpowerStatus(Long userId);

    /**
     * 授权
     * @param userId 用户id
     * @return int
     */
    Integer updateEmpowerStatus(Long userId) throws Exception;

    /**
     * 获取签约合作商列表
     * @return list
     */
    List<TytDispatchCargoOwner> getSigningList();

    TytDispatchCargoOwner getById(Long id);

    TytDispatchCargoOwner getByCooperativeId(Long cooperativeId);
}
