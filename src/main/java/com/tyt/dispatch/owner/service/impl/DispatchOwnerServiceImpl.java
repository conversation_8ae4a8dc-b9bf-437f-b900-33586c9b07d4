package com.tyt.dispatch.owner.service.impl;

import com.tyt.dispatch.owner.service.DispatchOwnerService;
import com.tyt.model.*;
import com.tyt.plat.entity.base.TytDispatchCargoOwner;
import com.tyt.plat.mapper.base.TytDispatchCargoOwnerMapper;
import com.tyt.user.service.CsMaintainedCustomService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/06/26 11:34
 */
@Service
@Slf4j
public class DispatchOwnerServiceImpl implements DispatchOwnerService {

    @Autowired
    TytDispatchCargoOwnerMapper dispatchCargoOwnerMapper;

    @Autowired
    private CsMaintainedCustomService csMaintainedCustomService;

    @Autowired
    private TytConfigService configService;

    @Autowired
    private UserService userService;

    private static final String DISPATCH_BIND_EMPLOYEE = "dispatch_bind_employee";


    @Override
    public Integer getEmpowerStatus(Long userId) {
        TytDispatchCargoOwner owner = dispatchCargoOwnerMapper.getByUserId(userId);
        if (Objects.isNull(owner)){
            return null;
        }
        return owner.getEmpowerStatus();
    }

    @Override
    public Integer updateEmpowerStatus(Long userId) throws Exception{

        TytDispatchCargoOwner owner = dispatchCargoOwnerMapper.getByUserId(userId);
        if (Objects.isNull(owner)){
            return null;
        }
        owner.setEmpowerStatus(2);
        owner.setModifyTime(new Date());
        int update = dispatchCargoOwnerMapper.updateByPrimaryKey(owner);

        CsMaintainedCustom custom = csMaintainedCustomService.getCsMaintainedCustomByUserId(userId);
        if (custom != null && custom.getDispatcherId() != null && custom.getEmpowerStatus() == 3) {
            return update;
        }
        String config = configService.getStringValue(DISPATCH_BIND_EMPLOYEE,"16633,张爽");
        String[] employee = config.split(",");
        TytInternalEmployee internalEmployee = new TytInternalEmployee();
        internalEmployee.setId(Long.parseLong(employee[0]));
        internalEmployee.setName(employee[1]);
        if (custom != null) {
            csMaintainedCustomService.updateCustom(internalEmployee, custom);
        } else {
            User user = userService.getByUserId(userId);
            csMaintainedCustomService.saveCustom(user, internalEmployee);
        }
        return update;
    }

    @Override
    public List<TytDispatchCargoOwner> getSigningList() {
        return dispatchCargoOwnerMapper.getSigningList();
    }

    @Override
    public TytDispatchCargoOwner getById(Long id){
        return dispatchCargoOwnerMapper.selectByPrimaryKey(id);
    }

    @Override
    public TytDispatchCargoOwner getByCooperativeId(Long cooperativeId) {
        return dispatchCargoOwnerMapper.getByCooperativeId(cooperativeId);
    }
}
