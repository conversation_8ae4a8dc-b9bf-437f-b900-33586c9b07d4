package com.tyt.dispatch.owner.controller;

import com.tyt.dispatch.owner.service.DispatchOwnerService;
import com.tyt.model.CsMaintainedCustom;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytInternalEmployee;
import com.tyt.plat.entity.base.TytDispatchCargoOwner;
import com.tyt.user.service.CsMaintainedCustomService;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/06/26 11:33
 */
@Slf4j
@RestController
@RequestMapping("/plat/dispatch/owner")
public class DispatchOwnerController {

    @Autowired
    private DispatchOwnerService dispatchOwnerService;


    /**
     * 获取授权状态
     *
     * @param userId 用户id
     * @return rm
     */
    @GetMapping(value = {"/getStatus", "/getStatus.action"})
    public ResultMsgBean getEmpowerStatus(@RequestParam Long userId) {

        Integer empowerStatus = dispatchOwnerService.getEmpowerStatus(userId);
        if (Objects.isNull(empowerStatus)) {
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "用户无授权信息");
        }

        return ResultMsgBean.successResponse(empowerStatus);
    }

    /**
     * 授权
     *
     * @param userId 用户id
     * @return rm
     */
    @PostMapping(value = {"/empower", "/empower.action"})
    public ResultMsgBean updateEmpowerStatus(@RequestParam Long userId) {
        try {
            Integer update = dispatchOwnerService.updateEmpowerStatus(userId);
            if (Objects.isNull(update)) {
                return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "用户无授权信息");
            }
            return ResultMsgBean.successResponse();
        }catch (Exception e){
            log.error("专车货主授权失败，", e);
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "专车货主授权失败");
        }
    }

    /**
     * 获取签约合作商列表
     *
     * @return rm
     */
    @GetMapping(value = {"/getSigningList", "/getSigningList.action"})
    public ResultMsgBean getSigningList() {

        List<TytDispatchCargoOwner> owners = dispatchOwnerService.getSigningList();

        return ResultMsgBean.successResponse(owners);
    }

}
