package com.tyt.transportquotedprice.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.SeckillGoodsTransportService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transportquotedprice.bean.TransportQuotedPriceDataInDetailPageVO;
import com.tyt.transportquotedprice.bean.TransportQuotedPriceLeaveTabVO;
import com.tyt.transportquotedprice.bean.TransportQuotedPriceTabDataVO;
import com.tyt.transportquotedprice.bean.TytTransportQuotedPricetTransportVO;
import com.tyt.transportquotedprice.service.TransportQuotedPriceService;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/plat/transportQuotedPrice")
@Slf4j
public class TransportQuotedPriceController {

    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;

    private static final String TRANSPORT_QUOTED_PRICE_BUBBLE_CLICK = "transportQuotedPriceBubble";

    @PostMapping(value = "/getCarToTransportQuotedPrice")
    @ResponseBody
    public ResultMsgBean getCarToTransportQuotedPrice(BaseParameter baseParameter, Long srcMsgId) {
        Long carUserId = baseParameter.getUserId();
        if (carUserId == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        return ResultMsgBean.successResponse(transportQuotedPriceService.getCarToTransportQuotedPrice(carUserId, srcMsgId));
    }

    @PostMapping(value = "/getTransportQuotedPriceList")
    @ResponseBody
    public ResultMsgBean getTransportQuotedPriceList(BaseParameter baseParameter, Long srcMsgId) {
        if (baseParameter.getUserId() == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //判断该用户是否是这个货的货主
        boolean userIsTransportOwner = transportQuotedPriceService.checkUserIsTransportOwner(baseParameter.getUserId(), srcMsgId);
        if (!userIsTransportOwner) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        return ResultMsgBean.successResponse(transportQuotedPriceService.getTransportQuotedPriceList(srcMsgId, baseParameter.getUserId()));
    }

    @PostMapping(value = "/getAllPublishingTransportQuotedPriceList")
    @ResponseBody
    public ResultMsgBean getAllPublishingTransportQuotedPriceList(BaseParameter baseParameter) {
        if (baseParameter.getUserId() == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        String quotedPriceListSwitch = tytConfigService.getStringValue("quoted_price_list_switch", "1");
        if (quotedPriceListSwitch.equals("1")) {
            return ResultMsgBean.successResponse(transportQuotedPriceService.getAllPublishingTransportQuotedPriceListV2(baseParameter.getUserId()));
        }
        return ResultMsgBean.successResponse(transportQuotedPriceService.getAllPublishingTransportQuotedPriceList(baseParameter.getUserId()));
    }

    /**
     * 获取货源最新一条是出价记录还是沟通记录
     * 0-无记录，1-沟通记录，2-出价记录
     *
     * @param baseParameter
     * @return
     */
    @RequestMapping(value = "/transportNewestRecordType")
    @ResponseBody
    public ResultMsgBean transportNewestRecordType(BaseParameter baseParameter) {
        if (baseParameter.getUserId() == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        return ResultMsgBean.successResponse(transportQuotedPriceService.transportNewestRecordType(baseParameter.getUserId()));
    }

    @PostMapping(value = {"/carQuotedPrice", "/carQuotedPrice.action"})
    @ResponseBody
    public ResultMsgBean carQuotedPrice(BaseParameter baseParameter, Long srcMsgId, Integer price, String reason) {
        Long carUserId = baseParameter.getUserId();

        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(srcMsgId)) {
            return ResultMsgBean.failResponse(8899010, "秒抢货源锁定期间暂不支持报价");
        }

        ResultMsgBean isCanCarQuotedPriceResilt = isCanCarQuotedPrice(carUserId, srcMsgId, price);
        if (!isCanCarQuotedPriceResilt.isSuccess()) {
            return isCanCarQuotedPriceResilt;
        }

        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        ResultMsgBean isPassCheckCarryPriceIsRules = transportQuotedPriceService.checkCarryPriceIsRules(transportMain, carUserId, null, price);
        if (isPassCheckCarryPriceIsRules.getCode() != 200) {
            return isPassCheckCarryPriceIsRules;
        }

        return transportQuotedPriceService.carQuotedPrice(carUserId, srcMsgId, price, reason, baseParameter);
    }

    @PostMapping(value = "/transportQuotedPrice")
    @ResponseBody
    public ResultMsgBean transportQuotedPrice(BaseParameter baseParameter, Long transportQuotedPriceId, Long srcMsgId, Integer price, Integer changePrice) {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(srcMsgId)) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }

        Long transportUserId = baseParameter.getUserId();
        ResultMsgBean isCanTransportQuotedPriceResult = isCanTransportQuotedPrice(transportUserId, transportQuotedPriceId, srcMsgId, price);
        if (!isCanTransportQuotedPriceResult.isSuccess()) {
            return isCanTransportQuotedPriceResult;
        }

        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        ResultMsgBean isPassCheckCarryPriceIsRules = transportQuotedPriceService.checkCarryPriceIsRules(transportMain, transportUserId, transportQuotedPriceId, price);
        if (isPassCheckCarryPriceIsRules.getCode() != 200) {
            return isPassCheckCarryPriceIsRules;
        }

        return transportQuotedPriceService.transportQuotedPrice(baseParameter, transportQuotedPriceId, price, changePrice);
    }

    @PostMapping(value = "/carAgree")
    @ResponseBody
    public ResultMsgBean carAgree(BaseParameter baseParameter, Long srcMsgId) {
        Long carUserId = baseParameter.getUserId();
        if (carUserId == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(srcMsgId)) {
            return ResultMsgBean.failResponse(8899010, "秒抢货源锁定期间暂不支持报价");
        }

        //判断货源是否失效
        ResultMsgBean isTransportValidityResult = transportQuotedPriceService.checkTransportValidity(srcMsgId);
        if (!isTransportValidityResult.isSuccess()) {
            return isTransportValidityResult;
        }

        return transportQuotedPriceService.carAgree(carUserId, srcMsgId);
    }

    @PostMapping(value = {"/transportAgree", "/transportAgree.action"})
    @ResponseBody
    public ResultMsgBean transportAgree(BaseParameter baseParameter, Long transportQuotedPriceId, Long srcMsgId, Integer autoAgree) {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(srcMsgId)) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }

        if (transportQuotedPriceId == null || baseParameter.getUserId() == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //判断该用户是否是这个货的货主
        boolean userIsTransportOwner = transportQuotedPriceService.checkUserIsTransportOwner(baseParameter.getUserId(), srcMsgId);
        if (!userIsTransportOwner) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //判断货源是否失效
        ResultMsgBean isTransportValidityResult = transportQuotedPriceService.checkTransportValidityV2(srcMsgId);
        if (!isTransportValidityResult.isSuccess()) {
            return isTransportValidityResult;
        }

        return transportQuotedPriceService.transportAgree(baseParameter, transportQuotedPriceId, baseParameter.getUserId(), autoAgree);
    }

    private ResultMsgBean isCanCarQuotedPrice(Long carUserId, Long srcMsgId, Integer price) {
        if (carUserId == null || srcMsgId == null || price == null || price <= 0) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //查看carUserId是否在车无价货源报价AB测试中
        boolean userIsInTransportQuotedPriceABTest = transportQuotedPriceService.checkUserIsInTransportQuotedPriceABTest(carUserId);
        if (!userIsInTransportQuotedPriceABTest) {
            return ResultMsgBean.failResponse(10001, "用户不可进行出价");
        }

        //判断货源是否失效
        ResultMsgBean checkTransportValidityResult = transportQuotedPriceService.checkTransportValidity(srcMsgId);
        if (!checkTransportValidityResult.isSuccess()) {
            return checkTransportValidityResult;
        }

        //判断发货人和找货人是否是同一个人
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain.getUserId().compareTo(carUserId) == 0) {
            return ResultMsgBean.failResponse(10001, "本人的货源不可出价");
        }

        if (StringUtils.isNotBlank(transportMain.getPrice()) && price <= Integer.parseInt(transportMain.getPrice())) {
            return ResultMsgBean.failResponse(10001, "您的出价低于或等于当前运费，请重新出价");
        }

        return ResultMsgBean.successResponse();
    }

    private ResultMsgBean isCanTransportQuotedPrice(Long transportUserId, Long transportQuotedPriceId, Long srcMsgId, Integer price) {
        if (transportUserId == null || transportQuotedPriceId == null || srcMsgId == null || price == null || price <= 0) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //判断该用户是否是这个货的货主
        boolean userIsTransportOwner = transportQuotedPriceService.checkUserIsTransportOwner(transportUserId, srcMsgId);
        if (!userIsTransportOwner) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //判断货源是否失效
        return transportQuotedPriceService.checkTransportValidityV2(srcMsgId);
    }

    @PostMapping(value = "/getTransportHaveAnyQuotedPrice")
    @ResponseBody
    public ResultMsgBean getTransportHaveAnyQuotedPriceAgain(BaseParameter baseParameter) {
        if (baseParameter.getUserId() == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        return transportQuotedPriceService.getTransportHaveAnyQuotedPrice(baseParameter.getUserId());
    }

    @PostMapping(value = "/getCarHaveNewTransportQuotedPrice")
    @ResponseBody
    public ResultMsgBean getCarHaveNewTransportQuotedPrice(BaseParameter baseParameter) {
        if (baseParameter.getUserId() == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        return transportQuotedPriceService.getCarHaveNewTransportQuotedPrice(baseParameter.getUserId());
    }

    /**
     * 车方 被反馈（报价被货方同意）、有回价气泡内容
     * @param baseParameter
     * @return
     */
    @PostMapping(value = "/getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice")
    @ResponseBody
    public ResultMsgBean getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice(BaseParameter baseParameter) {
        if (baseParameter.getUserId() == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        return transportQuotedPriceService.getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice(baseParameter.getUserId());
    }

    /**
     * 车离开货源详情报价挽留弹窗
     * @param baseParameter
     * @param srcMsgId
     * @return
     */
    @PostMapping(value = "/getCarLeaveTransportSingleDetailTabData")
    @ResponseBody
    public ResultMsgBean getCarLeaveTransportSingleDetailTabData(BaseParameter baseParameter, Long srcMsgId) {
        if (baseParameter.getUserId() == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        return transportQuotedPriceService.getCarLeaveTransportSingleDetailTabData(baseParameter.getUserId(), srcMsgId);
    }

    /**
     * 货在货源详情顶部报价列表
     * @param baseParameter
     * @param srcMsgId
     * @return
     */
    @PostMapping(value = "/getTransportQuotedPriceListInTransportSingleDetailPage")
    @ResponseBody
    public ResultMsgBean getTransportQuotedPriceListInTransportSingleDetailPage(BaseParameter baseParameter, Long srcMsgId) {
        if (baseParameter.getUserId() == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        List<TytTransportQuotedPricetTransportVO> transportQuotedPriceListInTransportSingleDetailPage = transportQuotedPriceService.getTransportQuotedPriceListInTransportSingleDetailPage(srcMsgId, baseParameter.getUserId());
        TransportQuotedPriceDataInDetailPageVO transportQuotedPriceDataInDetailPageVO = new TransportQuotedPriceDataInDetailPageVO();
        transportQuotedPriceDataInDetailPageVO.setTytTransportQuotedPricetTransportVOS(transportQuotedPriceListInTransportSingleDetailPage);
        transportQuotedPriceDataInDetailPageVO.setTransportQuotedPriceCount(transportQuotedPriceService.getTransportQuotedPriceCountBySrcMsgIs(srcMsgId));

        boolean haveNoLookTransport = transportQuotedPriceListInTransportSingleDetailPage.stream()
                .anyMatch(TytTransportQuotedPricetTransportVO::getTransportNoLook);

        int transportHaveOptionQuotedPriceCount = transportQuotedPriceService.getTransportHaveOptionQuotedPriceCount(srcMsgId);
        if (!haveNoLookTransport && !RedisUtil.exists(TRANSPORT_QUOTED_PRICE_BUBBLE_CLICK + ":" + srcMsgId) && transportHaveOptionQuotedPriceCount == 0) {
            transportQuotedPriceDataInDetailPageVO.setShowBubble(true);
        }

        if (!transportQuotedPriceListInTransportSingleDetailPage.isEmpty()) {
            for (TytTransportQuotedPricetTransportVO tytTransportQuotedPricetTransportVO : transportQuotedPriceListInTransportSingleDetailPage) {
                if (tytTransportQuotedPricetTransportVO.getQuotedType() != null && tytTransportQuotedPricetTransportVO.getQuotedType() == 0
                        && tytTransportQuotedPricetTransportVO.getFinalQuotedPriceIsDone() != null && tytTransportQuotedPricetTransportVO.getFinalQuotedPriceIsDone() == 1) {
                    tytTransportQuotedPricetTransportVO.setHaveCallToCarButton(1);
                }
            }
        }

        return ResultMsgBean.successResponse(transportQuotedPriceDataInDetailPageVO);
    }

    /**
     * 货在报价列表web页面顶部氛围文案
     * @param baseParameter
     * @param srcMsgId
     * @return
     */
    @PostMapping(value = "/getTransportQuotedPriceLPageWord")
    @ResponseBody
    public ResultMsgBean getTransportQuotedPriceLPageWord(BaseParameter baseParameter, Long srcMsgId) {
        if (baseParameter.getUserId() == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        String word = transportQuotedPriceService.getTransportQuotedPriceLPageWord(srcMsgId);
        return ResultMsgBean.successResponse(word);
    }

    /**
     * 货报价挽留弹窗
     * @param baseParameter
     * @param srcMsgId
     * @return
     */
    @PostMapping(value = "/getTransportQuotedPriceLeaveTab")
    @ResponseBody
    public ResultMsgBean getTransportQuotedPriceLeaveTab(BaseParameter baseParameter, Long srcMsgId) {
        if (baseParameter.getUserId() == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        TransportQuotedPriceLeaveTabVO transportQuotedPriceLeaveTab = transportQuotedPriceService.getTransportQuotedPriceLeaveTab(srcMsgId);
        return ResultMsgBean.successResponse(transportQuotedPriceLeaveTab);
    }

    /**
     * 获取报价货源详情
     * @param baseParameter
     * @param srcMsgId
     * @return
     */
    @PostMapping(value = "/getTransportVO")
    @ResponseBody
    public ResultMsgBean getTransportQuotedPriceListVO(BaseParameter baseParameter, Long srcMsgId) {
        if (baseParameter.getUserId() == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //判断该用户是否是这个货的货主
        boolean userIsTransportOwner = transportQuotedPriceService.checkUserIsTransportOwner(baseParameter.getUserId(), srcMsgId);
        if (!userIsTransportOwner) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        return ResultMsgBean.successResponse(transportQuotedPriceService.getTransportQuotedPriceListVO(srcMsgId, baseParameter.getUserId()));
    }

    /**
     * 货方货源详情顶部报价列表点击提醒报价气泡
     * @param baseParameter
     * @param srcMsgId
     * @return
     */
    @PostMapping(value = "/clickTransportQuotedPriceBubble")
    @ResponseBody
    public ResultMsgBean clickTransportQuotedPriceBubble(BaseParameter baseParameter, Long srcMsgId) {
        if (baseParameter.getUserId() == null || srcMsgId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //判断该用户是否是这个货的货主
        boolean userIsTransportOwner = transportQuotedPriceService.checkUserIsTransportOwner(baseParameter.getUserId(), srcMsgId);
        if (!userIsTransportOwner) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        RedisUtil.set(TRANSPORT_QUOTED_PRICE_BUBBLE_CLICK + ":" + srcMsgId, "1", 60 * 60 * 30);
        return ResultMsgBean.successResponse();
    }

    /**
     * 货方拒绝某个货主的报价弹窗数据
     * @param baseParameter
     * @param srcMsgId
     * @return
     */
    @PostMapping(value = "/getTransportQuotedPriceTabData")
    @ResponseBody
    public ResultMsgBean getTransportQuotedPriceTabData(BaseParameter baseParameter, Long transportQuotedPriceId, Long srcMsgId) {
        if (baseParameter.getUserId() == null || transportQuotedPriceId == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        //测试期，去掉下面的判断
        //判断该用户是否是这个货的货主
//        boolean userIsTransportOwner = transportQuotedPriceService.checkUserIsTransportOwner(baseParameter.getUserId(), srcMsgId);
//        if (!userIsTransportOwner) {
//            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
//        }

        TransportQuotedPriceTabDataVO transportQuotedPriceTabDataVO = transportQuotedPriceService.getTransportQuotedPriceTabData(transportQuotedPriceId);
        return ResultMsgBean.successResponse(transportQuotedPriceTabDataVO);
    }

}
