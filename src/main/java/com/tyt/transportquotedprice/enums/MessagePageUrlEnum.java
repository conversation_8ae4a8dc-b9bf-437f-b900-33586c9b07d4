package com.tyt.transportquotedprice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MessagePageUrlEnum {

    // 车站内信跳转页面
    CAR_FIND_GOODS_HALL("找货大厅-车", "callFindGoodHall"),
    CAR_MEMBER_PURCHASE_PAGE("会员购买页-车", "/tvipModel/carVip?noTitle=yes&isTpay=1"),
    CAR_ORDER_DETAIL_PAGE("订单详情页页-车", "callAppOrderDetail?orderId=%s&tsOrderNo=%s"),
    CAR_MY_ACTIVITY("我的活动-车", "/tytModel/myActivity?noTitle=yes"),
    CAR_GOODS_DETAIL("货源详情页-车", "callAppGoodsDetail?srcMsgId=%s&tsId=%s"),
    CAR_WALLET("钱包-车", "/tytCompany/myWallet?noTitle=yes&isHead=yes"),
    CAR_AUTHENTICATION_DETAIL_PAGE("实名认证详情页-车", "identityAuthentication"),
    CAR_ORDER_LIST_PENDING_REVIEW("订单列表-待评价-车", "callAppOrderListAll?tabType=4"),
    CAR_MY_REVIEW("我的评价-车", "/tytModel/evaluate?noTitle=yes"),
    CAR_ORDER_LIST_PENDING_CONFIRM("订单列表-待确认-车", "callAppOrderListAll?tabType=1"),
    CAR_VEHICLE_CERTIFICATION_DETAIL_PAGE("车辆认证详情页-车", "callAppVehicleAuthentication?carId=%s"),
    CAR_RISK_CONTROL_DEAL_PAGE("风控单处理页面-车", "/tytModel/riskcontrolOrder/order?noTitle=yes"),

    // 货站内信跳转页面
    GOODS_WALLET("钱包-货", "/tytCompany/myWallet?noTitle=yes&isHead=yes"),
    GOODS_ENTERPRISE_CERTIFICATION("企业认证-货", "callAppEnterpriseAuthentication"),
    GOODS_MEMBER_PAGE("会员页面-货", "/tvipModel/goodsVipNew?noTitle=yes&isTpay=1"),
    GOODS_CARGO_DETAIL_PAGE("货源详情页-货", "callAppGoodsDetail?srcMsgId=%s&tsId=%s"),
    GOODS_ORDER_DETAIL_PAGE("订单详情页-货", "callAppOrderDetail?orderId=%s&tsOrderNo=%s"),
    GOODS_PENDING_REPAYMENT_PAGE("待还款页面-货", "/tytModel/trackPay?noTitle=yes"),
    GOODS_SHIPPING_PAGE("发货页面-货", "callAppSendGoods"),
    GOODS_MY_REVIEW("我的评价-货", "/tytModel/evaluate?noTitle=yes"),

    ;

    private String remark;

    private String pageUrl;
}
