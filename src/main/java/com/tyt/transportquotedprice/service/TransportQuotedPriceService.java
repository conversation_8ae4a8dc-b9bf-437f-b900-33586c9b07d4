package com.tyt.transportquotedprice.service;

import com.alibaba.fastjson.JSONObject;
import com.tyt.base.bean.BaseParameter;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.transportquotedprice.bean.*;

import java.util.List;

public interface TransportQuotedPriceService {

    TytTransportQuotedPriceCarVO getCarToTransportQuotedPrice(Long carUserId, Long srcMsgId);

    List<TytTransportQuotedPricetTransportVO> getTransportQuotedPriceListInTransportSingleDetailPage(Long srcMsgId, Long userId);

    List<TytTransportQuotedPricetTransportVO> getTransportQuotedPriceList(Long srcMsgId, Long userId);

    ResultMsgBean carQuotedPrice(Long carUserId, Long srcMsgId, Integer price, String reason, BaseParameter baseParameter);

    ResultMsgBean transportQuotedPrice(BaseParameter baseParameter, Long transportQuotedPriceId, Integer price, Integer changePrice);

    void recordCarQuotedPrice(TransportMain transportMain);

    ResultMsgBean carAgree(Long carUserId, Long srcMsgId);

    ResultMsgBean transportAgree(BaseParameter parameter, Long transportQuotedPriceId, Long transportUserId, Integer autoAgree);

    boolean checkUserIsInTransportQuotedPriceABTest(Long carUserId);

    ResultMsgBean getTransportHaveAnyQuotedPrice(Long userId);

    ResultMsgBean getCarHaveNewTransportQuotedPrice(Long userId);

    ResultMsgBean getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice(Long carUserId);

    void recordCarLeaveTransportSingleDetail(Long carUserId, Long srcMsgId);

    boolean getCarAndThisTransportIsHaveNewTransportQuotedPrice(Long srcMsgId, Long carUserId);

    boolean getCarAndThisTransportIsHaveAgreeTransportQuotedPrice(Long srcMsgId, Long carUserId);

    Integer getTransportQuotedPriceCountBySrcMsgIs(Long srcMsgId);

    ResultMsgBean checkCarryPriceIsRules(TransportMain transportMain, Long queryUserId, Long transportQuotedPriceId, int price);

    ResultMsgBean checkTransportValidity(Long srcMsgId);

    ResultMsgBean checkTransportValidityV2(Long srcMsgId);

    boolean checkUserIsTransportOwner(Long userId, Long srcMsgId);

    boolean getCarIsHaveQuotedPriceToTransport(Long carUserId, Long goodsId);

    List<AllTransportQuotedPriceVo> getAllPublishingTransportQuotedPriceList(Long userId);
    List<AllTransportQuotedPriceVo> getAllPublishingTransportQuotedPriceListV2(Long userId);

    ResultMsgBean getCarLeaveTransportSingleDetailTabData(Long carUserId, Long srcMsgId);

    String getTransportQuotedPriceLPageWord(Long srcMsgId);

    TransportQuotedPriceLeaveTabVO getTransportQuotedPriceLeaveTab(Long srcMsgId);

    TransportMain getTransportQuotedPriceListVO(Long srcMsgId, Long userId);

    int getTransportHaveOptionQuotedPriceCount(Long srcMsgId);

    JSONObject transportNewestRecordType(Long userId);

    TransportQuotedPriceTabDataVO getTransportQuotedPriceTabData(Long transportQuotedPriceId);

}
