package com.tyt.transportquotedprice.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.callPhoneRecord.enums.RatingTypeEnum;
import com.tyt.callPhoneRecord.service.CallPhoneRecordService;
import com.tyt.infofee.bean.CreditUserInfo;
import com.tyt.messagecenter.core.enums.NativePageEnum;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NewsMessagePush;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.InfofeeDetailMapper;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.biz.feedback.pojo.UserFeedbackRatingAndLabelDTO;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.client.autocall.OuterAutoCallClient;
import com.tyt.plat.client.push.ApiInnerPushClient;
import com.tyt.plat.client.push.dto.GoodsPushDto;
import com.tyt.plat.client.push.dto.PushCodeEnum;
import com.tyt.plat.client.push.dto.PushTypeEnum;
import com.tyt.plat.entity.base.CallPhoneRecordDO;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.plat.entity.base.TytTransportValuable;
import com.tyt.plat.enums.PriceSourceEnum;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.plat.utils.NumberConvertUtil;
import com.tyt.plat.vo.esign.AutoCallTaskRequest;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.plat.vo.remote.CarryPriceReq;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.plat.vo.remote.TytQuotedPriceBiData;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.SameTransportAveragePriceData;
import com.tyt.transport.querybean.SameTransportAveragePriceReq;
import com.tyt.transport.service.BsPublishTransportService;
import com.tyt.transport.service.ExcellentPriceConfigService;
import com.tyt.transport.service.FreightDetailService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transportquotedprice.bean.*;
import com.tyt.transportquotedprice.enums.MessagePageUrlEnum;
import com.tyt.transportquotedprice.enums.QuotedTypeEnum;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class TransportQuotedPriceServiceImpl implements TransportQuotedPriceService{

    @Autowired
    private TytTransportQuotedPriceMapper tytTransportQuotedPriceMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private BsPublishTransportService bsPublishTransportService;

    @Autowired
    private InfofeeDetailMapper infofeeDetailMapper;

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private CallPhoneRecordService callPhoneRecordService;
    @Autowired
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;
    @Autowired
    private IFeedbackUserService feedbackUserService;

    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;

    @Autowired
    private TytTransportValuableMapper tytTransportValuableMapper;

    @Autowired
    private TytTransportDispatchViewDetailMapper tytTransportDispatchViewDetailMapper;

    @Autowired
    private TytAppCallLogMapper tytAppCallLogMapper;

    @Autowired
    private FreightDetailService freightDetailService;

    @Autowired
    private ExposureCardGiveawayRecordMapper exposureCardGiveawayRecordMapper;

    @Autowired
    private ExcellentPriceConfigService excellentPriceConfigService;

    @Autowired
    private TytTransportMapper transportMapper;

    @Autowired
    private ApiInnerPushClient apiInnerPushClient;

    @Autowired
    private OuterAutoCallClient outerAutoCallClient;

    public static final String TRANSPORT_QUOTED_PRICE_TOTAL_TIMES_KEY = "transport_quoted_price_total_times";

    private static final String SYSTEM_QUOTED_PRICE_AGREE_CAR_PUSH_PREFIX = "system_quoted_price_agree_car_push_";

    public static final String NO_QUOTED_PRICE = "报价不存在";

    public static final String QUOTED_PRICE_NOT_AGREE_CAN_TEL = "您的出价已被驳回，可以电话联系";
    public static final String QUOTED_PRICE_NOT_AGREE = "您的出价已被驳回，可以重新出价";
    public static final String QUOTED_PRICE_IS_AGREE = "您的出价已被接受，快支付订金成交吧";

    private static final String TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY = "transportQuotedPriceCount";

    private static final String TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY = "transportQuotedPriceCarNoLook";

    private static final String TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY = "transportQuotedPriceCarLeave";

    private static final String TRANSPORT_QUOTED_PRICE_TRANSPORT_LEAVE_KEY = "transportQuotedPriceTransportLeave";

    private static final String TRANSPORT_CAR_QUOTED_PRICE_MESSAGE_KEY = "transportCarQuotedPriceMessage";

    private static final String TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY = "transportCarQuotedPriceTransportMessage";

    private static final String TRANSPORT_QUOTED_PRICE_LOCK = "transportQuotedPriceRedisKey";

    @Override
    public TytTransportQuotedPriceCarVO getCarToTransportQuotedPrice(Long carUserId, Long srcMsgId) {
        TytTransportQuotedPriceCarVO result = new TytTransportQuotedPriceCarVO();

        //如果货源不是无价货源或者优车2.0货源或者有效货源，直接返回空
        if (checkTransportValidityV2(srcMsgId).getCode() != 200) {
            return null;
        }

        //判断发货人和找货人是否是同一个人
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain.getUserId().compareTo(carUserId) == 0) {
            return null;
        }

        //获取配置的最大报价总次数，如果配置不存在或者小于1，则直接返回1
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        TytTransportQuotedPrice tytTransportQuotedPrice = tytTransportQuotedPriceMapper.getQuotedPriceByCarUserIdAndTransportMainId(carUserId, srcMsgId);

        //从未出过价
        if (tytTransportQuotedPrice == null) {
            result.setCarIsQuotedPriceOnce(false);
            return result;
        }
        BeanUtils.copyProperties(tytTransportQuotedPrice, result);
        result.setCarIsQuotedPriceOnce(true);

        //报价已被接受
        if (result.getFinalQuotedPriceIsDone() == 1) {
            return result;
        }

        //货方未响应
        if (result.getCarQuotedPriceTimes() > result.getTransportQuotedPriceTimes()) {
            result.setTransportIsQuotedPrice(false);
            return result;
        }
        result.setTransportIsQuotedPrice(true);

        //查看carUserId是否在车无价货源报价AB测试中，不在ab测试中的不可报价
        boolean userIsInTransportQuotedPriceABTest = checkUserIsInTransportQuotedPriceABTest(carUserId);
        if (!userIsInTransportQuotedPriceABTest) {
            result.setCanQuotedPrice(false);
            return result;
        }

        //车方出价次数用完，不能再进行出价
        if (result.getCarQuotedPriceTimes() >= quotedPriceTotalTimes) {
            result.setCanQuotedPrice(false);
            return result;
        }
        result.setCanQuotedPrice(true);

        RedisUtil.del(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + carUserId + ":" + srcMsgId);

        return result;
    }

    @Override
    public List<TytTransportQuotedPricetTransportVO> getTransportQuotedPriceListInTransportSingleDetailPage(Long srcMsgId, Long userId) {
        List<TytTransportQuotedPricetTransportVO> tytTransportQuotedPricetTransportVOList = getTytTransportQuotedPricetTransportVOList(srcMsgId);
        if (CollectionUtils.isEmpty(tytTransportQuotedPricetTransportVOList)) {
            return tytTransportQuotedPricetTransportVOList;
        }
        List<TytTransportQuotedPricetTransportVO> result = tytTransportQuotedPricetTransportVOList.stream().limit(3).collect(Collectors.toList());
        String noLookQuotedPriceNumString = RedisUtil.getMapValue(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId, srcMsgId.toString());
        if (StringUtils.isNotBlank(noLookQuotedPriceNumString) && StringUtils.isNumeric(noLookQuotedPriceNumString)) {
            int noLookQuotedPriceNum = Integer.parseInt(noLookQuotedPriceNumString);
            for (int i = 0; i < Math.min(noLookQuotedPriceNum, result.size()); i++) {
                TytTransportQuotedPricetTransportVO tytTransportQuotedPricetTransportVO = result.get(i);
                tytTransportQuotedPricetTransportVO.setTransportNoLook(true);
            }
        }

        makeCarUserData(result);
        return result;
    }

    public List<TytTransportQuotedPricetTransportVO> getTytTransportQuotedPricetTransportVOList (Long srcMsgId) {
        List<TytTransportQuotedPricetTransportVO> result = new ArrayList<>();

        //获取配置的最大报价总次数，如果配置不存在或者小于1，则直接返回1
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        //如果货源不是无价货源或者优车2.0货源或者有效货源，直接返回空
        if (checkTransportValidityV2(srcMsgId).getCode() != 200) {
            return new ArrayList<>();
        }

        List<TytTransportQuotedPrice> quotedPriceList = tytTransportQuotedPriceMapper.getQuotedPriceListByTransportMainId(srcMsgId);
        if (CollectionUtils.isEmpty(quotedPriceList)) {
            return new ArrayList<>();
        }
        TransportMain transportMain = transportMainService.getBySrcMsgId(srcMsgId);
        for (TytTransportQuotedPrice tytTransportQuotedPrice : quotedPriceList) {
            TytTransportQuotedPricetTransportVO transportQuotedPricetTransportVO = new TytTransportQuotedPricetTransportVO();
            BeanUtils.copyProperties(tytTransportQuotedPrice, transportQuotedPricetTransportVO);
            transportQuotedPricetTransportVO.setPrice(transportMain.getPrice());
            transportQuotedPricetTransportVO.setGoodsTypeName(transportMain.getGoodTypeName());
            Integer goodsTypeNum = callPhoneRecordService.getGoodsTypeNum(tytTransportQuotedPrice.getCarId(), transportMain.getGoodTypeName());
            transportQuotedPricetTransportVO.setGoodsTypeNum(goodsTypeNum);
            result.add(transportQuotedPricetTransportVO);

            //报价已被接受
            if (transportQuotedPricetTransportVO.getFinalQuotedPriceIsDone() == 1) {
                continue;
            }

            //车方未响应
            if (transportQuotedPricetTransportVO.getCarQuotedPriceTimes().equals(transportQuotedPricetTransportVO.getTransportQuotedPriceTimes())) {
                transportQuotedPricetTransportVO.setCarIsQuotedPriceAgain(false);
                continue;
            }
            transportQuotedPricetTransportVO.setCarIsQuotedPriceAgain(true);

            //货方出价次数用完，不能再进行出价
            if (transportQuotedPricetTransportVO.getTransportQuotedPriceTimes() >= quotedPriceTotalTimes) {
                transportQuotedPricetTransportVO.setCanQuotedPrice(false);
                continue;
            }
            transportQuotedPricetTransportVO.setCanQuotedPrice(true);
        }
        return result;
    }

    @Override
    public List<TytTransportQuotedPricetTransportVO> getTransportQuotedPriceList(Long srcMsgId, Long userId) {
        List<TytTransportQuotedPricetTransportVO> result = getTytTransportQuotedPricetTransportVOList(srcMsgId);

        makeCarUserData(result);

        //货主查看某个货源的报价列表
        if (RedisUtil.exists(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId)
                && RedisUtil.mapExists(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId, srcMsgId.toString())) {
            //将该货源的货主未浏览报价次数清空
            RedisUtil.mapRemove(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId, srcMsgId.toString());
        }

        if (!result.isEmpty()) {
            for (TytTransportQuotedPricetTransportVO tytTransportQuotedPricetTransportVO : result) {
                if (tytTransportQuotedPricetTransportVO.getQuotedType() != null && tytTransportQuotedPricetTransportVO.getQuotedType() == 0
                        && tytTransportQuotedPricetTransportVO.getFinalQuotedPriceIsDone() != null && tytTransportQuotedPricetTransportVO.getFinalQuotedPriceIsDone() == 1) {
                    tytTransportQuotedPricetTransportVO.setHaveCallToCarButton(1);
                }
            }
        }

        return result;
    }

    private void makeCarUserData(List<TytTransportQuotedPricetTransportVO> result) {
        try {
            if (CollectionUtils.isNotEmpty(result)) {
                String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url_old", "http://www.teyuntong.com/rootdata");

                for (TytTransportQuotedPricetTransportVO tytTransportQuotedPricetTransportVO : result) {
                    fillSingleVO(tytServerPictureUrl, tytTransportQuotedPricetTransportVO);
                }
            }
        } catch (Exception e) {
            log.error("makeCarUserData error:", e);
        }
    }

    /**
     * 补充车主信息
     * @param tytServerPictureUrl
     * @param tytTransportQuotedPricetTransportVO
     */
    private void fillSingleVO(String tytServerPictureUrl, TytTransportQuotedPricetTransportVO tytTransportQuotedPricetTransportVO) throws Exception {
        //返回总交易数、与我交易数
        List<CreditUserInfo> creditUserInfos = infofeeDetailMapper.queryCreditUserInfo("(" + tytTransportQuotedPricetTransportVO.getCarId().toString() + ")", tytTransportQuotedPricetTransportVO.getTransportUserId().toString());
        if (CollectionUtils.isNotEmpty(creditUserInfos)) {
            CreditUserInfo creditUserInfo = creditUserInfos.get(0);
            tytTransportQuotedPricetTransportVO.setCoopNums(creditUserInfo.getCoopNums());
            tytTransportQuotedPricetTransportVO.setTradeNums(creditUserInfo.getTradeNums());
        }

        User user = userService.getByUserId(tytTransportQuotedPricetTransportVO.getCarId());
        if (user != null) {
            //返回拼好的头像
            if (org.apache.commons.lang3.StringUtils.isNotBlank(user.getHeadUrl())) {
                String pattern = "^(http|https)";
                Pattern compiledPattern = Pattern.compile(pattern);
                Matcher matcher = compiledPattern.matcher(user.getHeadUrl());
                if (!matcher.find()) {
                    tytTransportQuotedPricetTransportVO.setHeadUrl(tytServerPictureUrl + user.getHeadUrl());
                } else {
                    tytTransportQuotedPricetTransportVO.setHeadUrl(user.getHeadUrl());
                }
            }

            ApiDataUserCreditInfoTwo apiDataUserCreditInfoTwo = apiDataUserCreditInfoService.getById(user.getId());
            if (apiDataUserCreditInfoTwo != null) {
                tytTransportQuotedPricetTransportVO.setCarCreditRankLevel(apiDataUserCreditInfoTwo.getCarCreditRankLevel());
            }
            //查询车方的好评率和标签
            UserFeedbackRatingAndLabelDTO userFeedbackRatingAndLabel = feedbackUserService.getUserFeedbackRatingAndLabel(user.getId(), 1);
            if(userFeedbackRatingAndLabel != null){
                tytTransportQuotedPricetTransportVO.setRating(userFeedbackRatingAndLabel.getRating());
                tytTransportQuotedPricetTransportVO.setPositiveCount(userFeedbackRatingAndLabel.getPositiveCount());
                tytTransportQuotedPricetTransportVO.setPositiveLabels(userFeedbackRatingAndLabel.getPositiveLabels());
                tytTransportQuotedPricetTransportVO.setNegativeLabels(userFeedbackRatingAndLabel.getNegativeLabels());
                // 30天保护期
                if (DateUtils.addDays(user.getCtime() == null ? new Date() : user.getCtime(), 30).before(new Date())) {
                    //总评价量
                    Long total = userFeedbackRatingAndLabel.getTotal();
                    //好评数
                    Long positiveCount = userFeedbackRatingAndLabel.getPositiveCount();
                    if (total == 0) {
                        tytTransportQuotedPricetTransportVO.setRatingType(RatingTypeEnum.RECENT_NO_RATE.getCode());
                    } else if (total > 0 && total < 3) {
                        //有好评
                        if(positiveCount > 0){
                            tytTransportQuotedPricetTransportVO.setRatingType(RatingTypeEnum.RECENT_RECEIVE_POSITIVE.getCode());
                        } else { //无好评
                            tytTransportQuotedPricetTransportVO.setRatingType(RatingTypeEnum.RECENT_NOT_RECEIVE_POSITIVE.getCode());
                        }
                    } else if (total >= 3) {
                        tytTransportQuotedPricetTransportVO.setRatingType(RatingTypeEnum.SHOW_RATING.getCode());
                    }
                } else {
                    tytTransportQuotedPricetTransportVO.setRating(null);
                    tytTransportQuotedPricetTransportVO.setRatingType(RatingTypeEnum.NOT_SHOW_RATING.getCode());
                    tytTransportQuotedPricetTransportVO.setNegativeLabels(Collections.emptyList());
                }

            }

            //车是否会员
            UserPermission userPermission = userPermissionService.getUserPermission(tytTransportQuotedPricetTransportVO.getCarId(), UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode());
            tytTransportQuotedPricetTransportVO.setCarIsVip(null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus()) ? 1 : 0);

            //返回是否实名认证
            tytTransportQuotedPricetTransportVO.setRealNameAuthentication(user.getVerifyPhotoSign() != null && user.getVerifyPhotoSign() == 1);
        }
    }

    @Override
    public ResultMsgBean carQuotedPrice(Long carUserId, Long srcMsgId, Integer price, String reason, BaseParameter baseParameter) {
        //获取配置的最大报价总次数，如果配置不存在或者小于1，则直接返回1
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        User carUser = userService.getById(carUserId);
        if (carUser == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        TytTransportQuotedPrice tytTransportQuotedPrice = tytTransportQuotedPriceMapper.getQuotedPriceByCarUserIdAndTransportMainId(carUserId, srcMsgId);
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        String oldPrice = transportMain.getPrice();
        boolean transportNowHaveNoPrice = StringUtils.isBlank(transportMain.getPrice()) || Objects.equals("0", transportMain.getPrice());
        //列出各种情况，方便以后修改
        if (tytTransportQuotedPrice == null) {
            //首次报价
            tytTransportQuotedPrice = new TytTransportQuotedPrice();
            tytTransportQuotedPrice.setCarId(carUserId);
            tytTransportQuotedPrice.setCarUserName(carUser.getCarUserName() == null ? carUser.getUserName() : carUser.getCarUserName());
            tytTransportQuotedPrice.setTransportUserId(transportMain.getUserId());
            tytTransportQuotedPrice.setTransportUserName(transportMain.getNickName());
            tytTransportQuotedPrice.setSrcMsgId(srcMsgId);
            tytTransportQuotedPrice.setCarQuotedPrice(price);
            tytTransportQuotedPrice.setReason(reason);
            tytTransportQuotedPriceMapper.firstCarQuotedPriceV2(tytTransportQuotedPrice);
            // 价值货源出价更新出价状态及出价金额
            updateTransportValuableDriverGivePrice(srcMsgId, price);
            //记录货方未查看的报价相关逻辑
            recordCarQuotedPrice(transportMain);
            // 货主设置了回价助手，满足条件自动同意报价
            boolean autoAgree = priceAssistantAutoAgree(srcMsgId, price, baseParameter, transportMain, tytTransportQuotedPrice.getId());
            //给货方发车方出价短信push站内信
            carQuotedPriceSendMessageToTransportUser(carUserId, srcMsgId, transportMain.getUserId(), price, transportNowHaveNoPrice, autoAgree);
            JSONObject data = new JSONObject();
            data.put("autoAgree", autoAgree ? 1 : 0);
            data.put("oldPrice", transportNowHaveNoPrice ? 0 : Integer.parseInt(oldPrice));
            data.put("newPrice", price);
            return ResultMsgBean.successResponse(data);
        }
        if (tytTransportQuotedPrice.getFinalQuotedPriceIsDone() == 1) {
            //该车主对该货源的报价已被接受
            return ResultMsgBean.failResponse(10001, "报价已被接受，不可再次报价");
        }
        if (tytTransportQuotedPrice.getCarQuotedPriceTimes() >= quotedPriceTotalTimes) {
            //车方报价次数已超过允许的报价总次数
            return ResultMsgBean.failResponse(10001, "报价次数已超限，不可再次进行报价");
        }
        if (tytTransportQuotedPrice.getCarQuotedPriceTimes().equals(tytTransportQuotedPrice.getTransportQuotedPriceTimes())) {
            //货方拒绝了车方的报价，车方正要进行回价
            tytTransportQuotedPriceMapper.subsequentCarQuotedPrice(carUserId, carUser.getCarUserName() == null ? carUser.getUserName() : carUser.getCarUserName(), srcMsgId, price, reason);
            // 价值货源出价更新出价状态及出价金额
            updateTransportValuableDriverGivePrice(srcMsgId, price);
            //记录货方未查看的报价相关逻辑
            recordCarQuotedPrice(transportMain);
            // 货主设置了回价助手，满足条件自动同意报价
            boolean autoAgree = priceAssistantAutoAgree(srcMsgId, price, baseParameter, transportMain, tytTransportQuotedPrice.getId());
            //给货方发车方出价短信push站内信
            carQuotedPriceSendMessageToTransportUser(carUserId, srcMsgId, transportMain.getUserId(), price, transportNowHaveNoPrice, autoAgree);
            JSONObject data = new JSONObject();
            data.put("autoAgree", autoAgree ? 1 : 0);
            data.put("oldPrice", transportNowHaveNoPrice ? 0 : Integer.parseInt(oldPrice));
            data.put("newPrice", price);
            return ResultMsgBean.successResponse(data);
        } else {
            return ResultMsgBean.failResponse(10001, "车方报价货方暂未回应，货方回应前不可重复报价");
        }
    }

    /**
     * 货主设置了回价助手且满足条件，自动同意车主报价
     *
     * @param srcMsgId
     * @param price
     * @param baseParameter
     * @param transportMain
     * @param quotedPriceId
     * @return
     */
    private boolean priceAssistantAutoAgree(Long srcMsgId, Integer price, BaseParameter baseParameter, TransportMain transportMain, Long quotedPriceId) {
        TytTransportMainExtend mainExtend = tytTransportMainExtendMapper.getBySrcMsgId(srcMsgId);
        if (Objects.nonNull(mainExtend) && Objects.nonNull(mainExtend.getPriceCap()) && mainExtend.getPriceCap() > 0) {
            if (price <= mainExtend.getPriceCap()) {
                Integer userType = abtestService.getUserType("publish_price_assistant", transportMain.getUserId());
                if (Objects.equals(userType, 1)) {
                    ResultMsgBean resultMsgBean = this.transportAgree(baseParameter, quotedPriceId, transportMain.getUserId(), 1);
                    return resultMsgBean.isSuccess();
                }
            }
        }
        return false;
    }

    /**
     * 更新价值货源司机出价状态及最新出价
     *
     * @param srcMsgId
     * @param price
     */
    private void updateTransportValuableDriverGivePrice(Long srcMsgId, Integer price) {
        TytTransportValuable valuable = tytTransportValuableMapper.selectBySrcMsgId(srcMsgId);
        if (Objects.nonNull(valuable)) {
            valuable.setDriverGivePrice(1);
            valuable.setDriverLatestPrice(BigDecimal.valueOf(price));
            valuable.setModifyTime(new Date());
            tytTransportValuableMapper.updateByPrimaryKeySelective(valuable);
        }
    }

    private void carQuotedPriceSendMessageToTransportUser(Long carUserId, Long srcMsgId, Long transportUserId,
                                                          Integer price, boolean transportNowHaveNoPrice, boolean autoAgree) {
        TransportMain transportMain = transportMainService.getBySrcMsgId(srcMsgId);
        if (transportMain == null || transportMain.getUserId() == null) {
            return;
        }
        User transportUser = userService.getById(transportMain.getUserId());
        if (transportUser == null || StringUtils.isBlank(transportUser.getCellPhone())) {
            return;
        }

        User carUser = userService.getById(carUserId);
        String firstName = carUser.getTrueName() == null ? "" : carUser.getTrueName().substring(0, 1);
        if (StringUtils.isBlank(firstName)) {
            firstName = carUser.getUserName() == null ? "" : carUser.getUserName().substring(0, 1);
        }

        String content = "您发布的到" + transportMain.getDestCity() + "的货源，司机" + firstName + "师傅" + price + "元可走，快去查看>>";
        String title = "司机出价" + price + "元";

        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.addUserId(transportMain.getUserId());

        messagePushBase.setTitle(title);
        messagePushBase.setRemarks("司机出价" + price + "元");
        messagePushBase.setGoodsPush((short)1);

        //站内信
        messagePushBase.setContent("您" + transportMain.getStartCity() + "-" + transportMain.getDestCity() + "的货源，司机出价" + price + "元，请至货源详情查看");
        NewsMessagePush newsMessage = NewsMessagePush.createByPushBase(messagePushBase);
        Long tsId = transportMapper.getTsIdBySrcMsgId(transportMain.getSrcMsgId());
        newsMessage = messageCenterPushService.changeToNewDesignMessage(newsMessage, 0, 1, null, null, String.format(MessagePageUrlEnum.GOODS_CARGO_DETAIL_PAGE.getPageUrl(), transportMain.getSrcMsgId(), tsId));


        NotifyMessagePush notifyMessage = null;
        ShortMessageBean shortMessage = null;
        if (!autoAgree) {
            //push
            notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
            notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
            notifyMessage.addNativeParameter("id", transportMain.getSrcMsgId().toString());

            Integer transportReceiveQuotedPriceMessageNumRule = tytConfigService.getIntValue("transport_receive_quoted_price_message_num_rule", 5);

            //短信
            // 获取当前时间
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            // 获取明天零点时间
            java.time.LocalDateTime midnight = now.plusDays(1).toLocalDate().atStartOfDay();
            // 计算当前时间到明天零点的秒差
            Duration duration = Duration.between(now, midnight);
            int secondsUntilMidnight = (int) duration.getSeconds();
            if (!RedisUtil.exists(TRANSPORT_CAR_QUOTED_PRICE_MESSAGE_KEY + ":" + srcMsgId)) {
                if (RedisUtil.exists(TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY + ":" + transportUserId)) {
                    String transportReceiveQuotedPriceMessageNum = RedisUtil.get(TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY + ":" + transportUserId);
                    if (Integer.parseInt(transportReceiveQuotedPriceMessageNum) < transportReceiveQuotedPriceMessageNumRule) {
                        shortMessage = new ShortMessageBean();
                        shortMessage.setCellPhone(transportUser.getCellPhone());
                        String url = tytConfigService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.net");
                        shortMessage.setContent(content + url + "/jump.html?t=g&jp=cjjl");
                        shortMessage.setRemark("司机出价" + price + "元");
                        int num = Integer.parseInt(transportReceiveQuotedPriceMessageNum) + 1;
                        RedisUtil.set(TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY + ":" + transportUserId, String.valueOf(num), secondsUntilMidnight);
                    }
                } else {
                    shortMessage = new ShortMessageBean();
                    shortMessage.setCellPhone(transportUser.getCellPhone());
                    String url = tytConfigService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.net");
                    shortMessage.setContent(content + url + "/jump.html?t=g&jp=cjjl");
                    shortMessage.setRemark("司机出价" + price + "元");
                    RedisUtil.set(TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY + ":" + transportUserId, "1", secondsUntilMidnight);
                }
                RedisUtil.set(TRANSPORT_CAR_QUOTED_PRICE_MESSAGE_KEY + ":" + srcMsgId, "1", 60 * 60 * 30);
            }
        }

        messageCenterPushService.sendMultiMessage(shortMessage, newsMessage, notifyMessage);
    }

    @Override
    public void recordCarQuotedPrice(TransportMain transportMain) {
        if (RedisUtil.exists(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId())) {
            if (RedisUtil.mapExists(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString())) {
                String quotedPriceTimesCount = RedisUtil.getMapValue(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString());
                int newCount = Integer.parseInt(quotedPriceTimesCount) + 1;
                RedisUtil.mapPut(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), String.valueOf(newCount));
            } else {
                RedisUtil.mapPut(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), "1");
            }
        } else {
            long secondsUntilTomorrow = Duration.between(java.time.LocalDateTime.now(), java.time.LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).plusDays(1)).get(ChronoUnit.SECONDS);
            RedisUtil.setMap(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), "1", Integer.parseInt(String.valueOf(secondsUntilTomorrow)));
        }
    }

    @Override
    public ResultMsgBean transportQuotedPrice(BaseParameter parameter, Long transportQuotedPriceId, Integer price, Integer changePrice) {
        //获取配置的最大报价总次数，如果配置不存在或者小于1，则直接返回1
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        TytTransportQuotedPrice tytTransportQuotedPrice = tytTransportQuotedPriceMapper.getQuotedPriceById(transportQuotedPriceId);
        //列出各种情况，方便以后修改
        if (tytTransportQuotedPrice == null) {
            //报价不存在
            return ResultMsgBean.failResponse(10001, NO_QUOTED_PRICE);
        }
        if (tytTransportQuotedPrice.getFinalQuotedPriceIsDone() == 1) {
            //货方已同意车方对该货源的报价
            return ResultMsgBean.failResponse(10001, "报价已被接受，不可再次报价");
        }
        if (tytTransportQuotedPrice.getTransportQuotedPriceTimes() >= quotedPriceTotalTimes) {
            //货方报价次数已超过允许的报价总次数
            return ResultMsgBean.failResponse(10001, "报价次数已超限，不可再次进行报价");
        }
        if (tytTransportQuotedPrice.getCarQuotedPriceTimes() > tytTransportQuotedPrice.getTransportQuotedPriceTimes()) {
            boolean addPrice = false;
            boolean modifyPriceSuccess = false;

            //车方已完成报价，货方驳回，正要进行回价
            tytTransportQuotedPriceMapper.transportQuotedPrice(transportQuotedPriceId, price);

            if (changePrice != null && changePrice == 1) {
                TransportMain transportMain = transportMainService.getTransportMainForId(tytTransportQuotedPrice.getSrcMsgId());
                if (transportMain != null && StringUtils.isNotBlank(transportMain.getPrice()) && new BigDecimal(price).compareTo(new BigDecimal(transportMain.getPrice())) > 0) {
                    addPrice = true;
                } else if (transportMain != null && (StringUtils.isBlank(transportMain.getPrice()) || new BigDecimal(transportMain.getPrice()).compareTo(BigDecimal.ZERO) == 0)) {
                    addPrice = true;
                }

                //如果需要修改运费就去修改运费
                //如果货源是优车2.0货源，货方同意了报价会修改运费为车方最终报价并重新计算抽佣货源技术服务费和开票货源运费附加费
                modifyPriceSuccess = bsPublishTransportService.transportAgreeQuotedPriceEditTransportPriceAndReComputTecServiceFeeAndInvoice(parameter, tytTransportQuotedPrice.getSrcMsgId(), price, transportQuotedPriceId, 1);
            }

            //发送push消息
            try {
                transportQuotedPriceChangeNeedPush(transportQuotedPriceId, 1, addPrice && modifyPriceSuccess);
            } catch (Exception e) {
                log.info("货方驳回报价通知车方push消息失败 报价表ID：{}", tytTransportQuotedPrice.getId());
            }
            RedisUtil.set(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + tytTransportQuotedPrice.getCarId() + ":" + tytTransportQuotedPrice.getSrcMsgId(), "1", 60 * 60 * 30);
            return ResultMsgBean.successResponse();
        } else {
            return ResultMsgBean.failResponse(10001, "货方回价车方暂未回应，车方回应前不可重复报价");
        }

    }

    @Override
    public ResultMsgBean carAgree(Long carUserId, Long srcMsgId) {
        TytTransportQuotedPrice tytTransportQuotedPrice = tytTransportQuotedPriceMapper.getQuotedPriceByCarUserIdAndTransportMainId(carUserId, srcMsgId);
        //列出各种情况，方便以后修改
        if (tytTransportQuotedPrice == null) {
            return ResultMsgBean.failResponse(10001, NO_QUOTED_PRICE);
        }
        if (tytTransportQuotedPrice.getFinalQuotedPriceIsDone() == 1) {
            //该车主对该货源的报价已被接受
            return ResultMsgBean.failResponse(10001, "报价已被接受");
        }
        if (tytTransportQuotedPrice.getTransportQuotedPrice() == null) {
            //数据错误，货方报价为空，车方不能同意
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        //货方拒绝了车方的报价并已回价，车方正在同意
        tytTransportQuotedPriceMapper.carAgree(carUserId, srcMsgId);

        //记录货方未查看的报价相关逻辑
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        recordCarQuotedPrice(transportMain);

        return ResultMsgBean.successResponse();
    }

    /**
     * 货主同意出价
     *
     * @param parameter
     * @param transportQuotedPriceId
     * @param transportUserId
     * @param autoAgree     1-车主出价自动同意，2-回价助手任务设置自动同意
     * @return
     */
    @Override
    public ResultMsgBean transportAgree(BaseParameter parameter, Long transportQuotedPriceId, Long transportUserId, Integer autoAgree) {
        //5秒锁
        if (!RedisUtil.setNx(TRANSPORT_QUOTED_PRICE_LOCK + ":" + transportUserId + ":" + transportQuotedPriceId, "1", 5)) {
            return ResultMsgBean.failResponse(10001, "操作频繁，请5秒后重试");
        }

        TytTransportQuotedPrice tytTransportQuotedPrice = tytTransportQuotedPriceMapper.getQuotedPriceById(transportQuotedPriceId);
        //列出各种情况，方便以后修改
        if (tytTransportQuotedPrice == null) {
            return ResultMsgBean.failResponse(10001, NO_QUOTED_PRICE);
        }
        if (tytTransportQuotedPrice.getFinalQuotedPriceIsDone() == 1) {
            //该车主对该货源的报价已被接受
            return ResultMsgBean.failResponse(10001, "报价已被接受");
        }
        if (tytTransportQuotedPrice.getCarQuotedPrice() == null) {
            //数据错误，车方报价为空，货方不能同意
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        //货方正在同意
        int priceAssistantAutoAgree = Objects.nonNull(autoAgree) && (autoAgree == 1 || autoAgree == 2) ? 1 : 0;
        tytTransportQuotedPriceMapper.transportAgree(transportQuotedPriceId, priceAssistantAutoAgree);

        boolean addPrice = false;
        TransportMain transportMain = transportMainService.getTransportMainForId(tytTransportQuotedPrice.getSrcMsgId());
        if (transportMain != null && StringUtils.isNotBlank(transportMain.getPrice()) && new BigDecimal(tytTransportQuotedPrice.getCarQuotedPrice()).compareTo(new BigDecimal(transportMain.getPrice())) > 0) {
            addPrice = true;
        } else if (transportMain != null && (StringUtils.isBlank(transportMain.getPrice()) || new BigDecimal(transportMain.getPrice()).compareTo(BigDecimal.ZERO) == 0)) {
            addPrice = true;
        }

        // 无价货源：同意的运费不高于用户出价的推荐运费，不更新运费
        boolean needUpdateTransportPrice = getNeedUpdateTransportPrice(transportMain, tytTransportQuotedPrice.getCarQuotedPrice());
        Boolean addPirceIsSuccess = false;
        if (needUpdateTransportPrice) {
            //如果货源是优车2.0货源，货方同意了报价会修改运费为车方最终报价并重新计算抽佣货源技术服务费和开票货源运费附加费
            addPirceIsSuccess = bsPublishTransportService.transportAgreeQuotedPriceEditTransportPriceAndReComputTecServiceFeeAndInvoice(parameter, tytTransportQuotedPrice.getSrcMsgId(), tytTransportQuotedPrice.getCarQuotedPrice(), transportQuotedPriceId, 2);
        }
        //发送同意报价push消息
        try {
            if (Objects.equals(QuotedTypeEnum.SYSTEM.getCode(), tytTransportQuotedPrice.getQuotedType())) {
                // 系统出价，货主同意时给查看过货源的司机推送、短信、站内信
                if (addPrice && addPirceIsSuccess) {
                    systemQuotedPricePush(tytTransportQuotedPrice);
                }
            } else {
                if (Objects.isNull(autoAgree) || autoAgree == 2) {
                    transportQuotedPriceChangeNeedPush(transportQuotedPriceId, 2, addPrice && addPirceIsSuccess);
                }
            }
        } catch (Exception e) {
            log.info("货方同意报价报价通知车方push消息失败 报价表ID：{}", tytTransportQuotedPrice.getId());
        }

        Integer autoCallOnOff = tytConfigService.getIntValue("agree_quoted_price_auto_call_on_off", 0);
        if (autoCallOnOff == 1 && (Objects.isNull(autoAgree) || autoAgree == 2)) {
            try {
                //货方同意报价自动外呼联系车方
                AutoCallTaskRequest autoCallTaskRequest = new AutoCallTaskRequest();
                autoCallTaskRequest.setTaskName("同意报价-" + tytTransportQuotedPrice.getSrcMsgId() + "-" + tytTransportQuotedPrice.getCarId());

                User user = userService.getByUserId(tytTransportQuotedPrice.getCarId());
                if (user != null && StringUtils.isNotBlank(user.getCellPhone())) {
                    List<String> cellPhoneList = new ArrayList<>();
                    cellPhoneList.add(user.getCellPhone());
                    autoCallTaskRequest.setCallTelList(cellPhoneList);

                    TransportMain bySrcMsgId = transportMainService.getBySrcMsgId(tytTransportQuotedPrice.getSrcMsgId());
                    if (bySrcMsgId != null && StringUtils.isNotBlank(bySrcMsgId.getStartCity()) && StringUtils.isNotBlank(bySrcMsgId.getDestCity())) {
                        autoCallTaskRequest.setTaskCallValue("您报价的" + bySrcMsgId.getStartCity() + "到"
                                + bySrcMsgId.getDestCity() + "的货源已被货主采纳，价格为"
                                + numberToChinese(tytTransportQuotedPrice.getCarQuotedPrice())
                                + "元，快来抢单吧");
                        outerAutoCallClient.autoCallTask(autoCallTaskRequest).execute();
                    }
                }
            } catch (Exception e) {
                log.info("同意报价创建自动外呼失败，原因：", e);
            }
        }

        RedisUtil.set(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + tytTransportQuotedPrice.getCarId() + ":" + tytTransportQuotedPrice.getSrcMsgId(), "1", 60 * 60 * 30);

        return ResultMsgBean.successResponse();
    }

    /**
     * 是否需要更新货源运费
     * CXFS-14824 同意报价更新运费逻辑调整
     * 无价货源：同意的运费不高于用户出价的推荐运费，不更新运费
     *
     * @param transportMain
     * @param carQuotedPrice
     * @return
     */
    private boolean getNeedUpdateTransportPrice(TransportMain transportMain, Integer carQuotedPrice) {
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(transportMain.getPrice()) || new BigDecimal(transportMain.getPrice()).compareTo(BigDecimal.ZERO) == 0) {
                CarryPriceVo thPrice = excellentPriceConfigService.getThPrice(transportMain);
                if (Objects.nonNull(thPrice) && Objects.nonNull(thPrice.getThMinPrice())) {
                    BigDecimal userRecommendPrice = new BigDecimal(thPrice.getThMinPrice());
                    BigDecimal carPrice = new BigDecimal(carQuotedPrice);
                    return carPrice.compareTo(userRecommendPrice) > 0;
                }
            }
        } catch (Exception e) {
            log.error("getNeedUpdateTransportPrice error:{}", transportMain.getSrcMsgId(), e);
        }
        return true;
    }

    /**
     * 货主同意系统出价，给查看过该货源的司机推送
     *
     * @param quotedPrice
     */
    private void systemQuotedPricePush(TytTransportQuotedPrice quotedPrice) throws Exception {
        List<Long> carUserIds = tytTransportDispatchViewDetailMapper.queryViewCarUser(quotedPrice.getSrcMsgId());
        if (CollectionUtils.isEmpty(carUserIds)) {
            return;
        }

        TransportMain transportMain = transportMainService.getBySrcMsgId(quotedPrice.getSrcMsgId());
        if (Objects.isNull(transportMain)) {
            return;
        }

        //排除掉报价司机
        carUserIds.remove(quotedPrice.getCarId());

        transportQuotedPriceInPushAllViewUser(transportMain, carUserIds);

//
//        String title = "您查看过的货源涨价";
//        String content = "您查看的" + transportMain.getStartCity() + "到" + transportMain.getDestCity() + "的货源，涨价至" +
//                quotedPrice.getCarQuotedPrice() + "元，快来看看吧！";
//
//        MessagePushBase messagePushBase = new MessagePushBase();
//        //添加推送用户
//        messagePushBase.setUserIdList(carUserIds);
//
//        messagePushBase.setTitle(title);
//        messagePushBase.setContent(content);
//        messagePushBase.setRemarks("货主同意系统报价");
//        messagePushBase.setCarPush((short)1);
//
//        //push
//        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
//        notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
//        notifyMessage.addNativeParameter("id", transportMain.getSrcMsgId().toString());
//
//        //站内信
//        NewsMessagePush newsMessage = NewsMessagePush.createByPushBase(messagePushBase);
//
//        messageCenterPushService.sendMultiMessage(null, newsMessage, notifyMessage);
//
//        //短信
//        ShortMessageBean shortMessage = new ShortMessageBean();
//        String url = tytConfigService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.net");
//        shortMessage.setContent(content + url + "/jump.html?t=c&jp=cgd&id=" + transportMain.getSrcMsgId());
//        shortMessage.setRemark(title);
//
//        for (Long carUserId : carUserIds) {
//            if (RedisUtil.exists(SYSTEM_QUOTED_PRICE_AGREE_CAR_PUSH_PREFIX + carUserId)) {
//                // 每个车主每天最多发一次
//                continue;
//            }
//            User carUser = userService.getByUserId(carUserId);
//            shortMessage.setCellPhone(carUser.getCellPhone());
//            messageCenterPushService.sendMultiMessage(shortMessage, null, null);
//            long tomorrowZeroSeconds = TimeUtil.getTomorrowZeroSeconds();
//            RedisUtil.set(SYSTEM_QUOTED_PRICE_AGREE_CAR_PUSH_PREFIX + carUserId, "1", (int) tomorrowZeroSeconds);
//        }
    }

    @Override
    public boolean checkUserIsInTransportQuotedPriceABTest(Long carUserId) {
        List<String> codeList = new ArrayList<>();
        codeList.add("quoted_price_car");
        List<TytAbtestConfigVo> userTypeList = abtestService.getUserTypeList(codeList, carUserId);
        if (CollectionUtils.isNotEmpty(userTypeList)) {
            return userTypeList.get(0).getType().equals(1);
        }
        return false;
    }

    @Override
    public ResultMsgBean getTransportHaveAnyQuotedPrice(Long transportUserId) {
        //获取该货主所有被出价并且没有完结的报价的货源
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayBeginTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 0, 0, 0);
        LocalDateTime todayEndTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 23, 59, 59);
        List<Long> haveAnyQuotedPriceAgainTransportMainIdList = tytTransportQuotedPriceMapper.getTransportHaveAnyQuotedPrice(transportUserId, todayBeginTime.toDate(), todayEndTime.toDate());
        if (CollectionUtils.isEmpty(haveAnyQuotedPriceAgainTransportMainIdList)) {
            return ResultMsgBean.successResponse(false);
        }
        Integer count = transportMainService.getCountTransportMainIsValidForIdList(haveAnyQuotedPriceAgainTransportMainIdList, todayBeginTime.toDate(), todayEndTime.toDate());
        if (count > 0) {
            return ResultMsgBean.successResponse(true);
        }
        return ResultMsgBean.successResponse(false);
    }

    @Override
    public ResultMsgBean getCarHaveNewTransportQuotedPrice(Long carUserId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayBeginTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 0, 0, 0);
        LocalDateTime todayEndTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 23, 59, 59);
        List<Long> haveNewTransportQuotedPriceTransportMainIdList = tytTransportQuotedPriceMapper.getCarHaveNewTransportQuotedPrice(carUserId, todayBeginTime.toDate(), todayEndTime.toDate());
        if (CollectionUtils.isEmpty(haveNewTransportQuotedPriceTransportMainIdList)) {
            return ResultMsgBean.successResponse(false);
        }
        Integer count = transportMainService.getCountTransportMainIsValidForIdList(haveNewTransportQuotedPriceTransportMainIdList, todayBeginTime.toDate(), todayEndTime.toDate());
        if (count > 0) {
            return ResultMsgBean.successResponse(true);
        }
        return ResultMsgBean.successResponse(false);
    }

    @Override
    public ResultMsgBean getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice(Long carUserId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayBeginTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 0, 0, 0);
        LocalDateTime todayEndTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 23, 59, 59);

        boolean haveNewTransportQuotedPrice = false;
        Date carHaveNewTransportQuotedPriceLastTime = null;
        List<Long> haveNewTransportQuotedPriceTransportMainIdList = tytTransportQuotedPriceMapper.getCarHaveNewTransportQuotedPrice(carUserId, todayBeginTime.toDate(), todayEndTime.toDate());
        if (CollectionUtils.isNotEmpty(haveNewTransportQuotedPriceTransportMainIdList)) {
            List<Long> srcMsgIdList = transportMainService.getTransportMainIsValidSrcMsgIdsForIdList(haveNewTransportQuotedPriceTransportMainIdList, todayBeginTime.toDate(), todayEndTime.toDate());
            if (!srcMsgIdList.isEmpty()) {
                List<Long> srcMsgIdListNew = new ArrayList<>();
                for (Long srcMsgId : srcMsgIdList) {
                    boolean exists = RedisUtil.exists(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + carUserId + ":" + srcMsgId);
                    if (exists) {
                        srcMsgIdListNew.add(srcMsgId);
                    }
                }
                if (!srcMsgIdListNew.isEmpty()) {
                    carHaveNewTransportQuotedPriceLastTime = tytTransportQuotedPriceMapper.getCarHaveNewTransportQuotedPriceLastTime(carUserId, srcMsgIdListNew, todayBeginTime.toDate(), todayEndTime.toDate());
                    haveNewTransportQuotedPrice = true;
                }
            }
        }

        boolean haveAgreeTransportQuotedPrice = false;
        Date carHaveAgreeTransportQuotedPriceLastTime = null;
        List<Long> haveAgreeTransportQuotedPriceTransportMainIdList = tytTransportQuotedPriceMapper.getCarHaveAgreeTransportQuotedPrice(carUserId, todayBeginTime.toDate(), todayEndTime.toDate());
        if (CollectionUtils.isNotEmpty(haveAgreeTransportQuotedPriceTransportMainIdList)) {
            List<Long> srcMsgIdList = transportMainService.getTransportMainIsValidSrcMsgIdsForIdList(haveAgreeTransportQuotedPriceTransportMainIdList, todayBeginTime.toDate(), todayEndTime.toDate());
            if (!srcMsgIdList.isEmpty()) {
                List<Long> srcMsgIdListNew = new ArrayList<>();
                for (Long srcMsgId : srcMsgIdList) {
                    boolean exists = RedisUtil.exists(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + carUserId + ":" + srcMsgId);
                    if (exists) {
                        srcMsgIdListNew.add(srcMsgId);
                    }
                }
                if (!srcMsgIdListNew.isEmpty()) {
                    carHaveAgreeTransportQuotedPriceLastTime = tytTransportQuotedPriceMapper.getCarHaveAgreeTransportQuotedPriceLastTime(carUserId, srcMsgIdList, todayBeginTime.toDate(), todayEndTime.toDate());
                    haveAgreeTransportQuotedPrice = true;
                }
            }
        }

        if (haveNewTransportQuotedPrice && haveAgreeTransportQuotedPrice) {
            if (carHaveAgreeTransportQuotedPriceLastTime.compareTo(carHaveNewTransportQuotedPriceLastTime) >= 0) {
                return ResultMsgBean.successResponse("被反馈");
            }
            return ResultMsgBean.successResponse("有回价");
        }

        if (haveNewTransportQuotedPrice) {
            return ResultMsgBean.successResponse("有回价");
        }

        if (haveAgreeTransportQuotedPrice) {
            return ResultMsgBean.successResponse("被反馈");
        }

        return ResultMsgBean.successResponse("");
    }

    @Override
    public boolean getCarAndThisTransportIsHaveNewTransportQuotedPrice(Long srcMsgId, Long carUserId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayBeginTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 0, 0, 0);
        LocalDateTime todayEndTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 23, 59, 59);
        Integer count = tytTransportQuotedPriceMapper.getCarAndThisTransportIsHaveNewTransportQuotedPrice(srcMsgId, carUserId, todayBeginTime.toDate(), todayEndTime.toDate());
        return count != null && count != 0;
    }

    @Override
    public boolean getCarAndThisTransportIsHaveAgreeTransportQuotedPrice(Long srcMsgId, Long carUserId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayBeginTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 0, 0, 0);
        LocalDateTime todayEndTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 23, 59, 59);
        Integer count = tytTransportQuotedPriceMapper.getCarAndThisTransportIsHaveAgreeTransportQuotedPrice(srcMsgId, carUserId, todayBeginTime.toDate(), todayEndTime.toDate());
        return count != null && count != 0;
    }

    @Override
    public Integer getTransportQuotedPriceCountBySrcMsgIs(Long srcMsgId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayBeginTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 0, 0, 0);
        LocalDateTime todayEndTime = new LocalDateTime(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth(), 23, 59, 59);
        return tytTransportQuotedPriceMapper.getTransportQuotedPriceCountBySrcMsgIs(srcMsgId, todayBeginTime.toDate(), todayEndTime.toDate());
    }

    public Integer getQuotedPriceTotalTimes() {
        Integer quotedPriceTotalTimes = tytConfigService.getIntValue(TRANSPORT_QUOTED_PRICE_TOTAL_TIMES_KEY, 1);
        if (quotedPriceTotalTimes == null || quotedPriceTotalTimes < 1) {
            quotedPriceTotalTimes = 1;
        }
        return quotedPriceTotalTimes;
    }

    private void transportQuotedPriceChangeNeedPush(Long transportQuotedPriceId, Integer type, boolean addPrice) {
        TytTransportQuotedPrice tytTransportQuotedPrice = tytTransportQuotedPriceMapper.getQuotedPriceById(transportQuotedPriceId);
        TransportMain transportMain = transportMainService.getTransportMainForId(tytTransportQuotedPrice.getSrcMsgId());
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        User carUser;
        try {
            carUser = userService.getByUserId(tytTransportQuotedPrice.getCarId());
        } catch (Exception e) {
            log.info("车主信息不存在，无法发送无价货源报价反馈信息 userId:{}", tytTransportQuotedPrice.getCarId());
            return;
        }
        if (carUser != null && StringUtils.isNotEmpty(carUser.getCellPhone())) {
            String title;
            String content;
            String remark;

            if (type == 1) {
                //货方回价
                if (tytTransportQuotedPrice.getCarQuotedPriceTimes() >= quotedPriceTotalTimes) {
                    //车方出价次数超过配置的总次数，车方不能再进行出价
                    title = QUOTED_PRICE_NOT_AGREE_CAN_TEL;
                    content = QUOTED_PRICE_NOT_AGREE_CAN_TEL;
                    remark = QUOTED_PRICE_NOT_AGREE_CAN_TEL;
                } else {
                    //车方可以再进行出价
                    title = QUOTED_PRICE_NOT_AGREE;
                    content = QUOTED_PRICE_NOT_AGREE;
                    remark = QUOTED_PRICE_NOT_AGREE;
                }
            } else {
                //货方同意报价
                content = "货主已同意您报价的去往" + transportMain.getDestCity() + "的货源！快去接单吧>>";
                title = content;
                remark = content;
            }

            MessagePushBase messagePushBase = new MessagePushBase();
            //添加推送用户
            messagePushBase.addUserId(tytTransportQuotedPrice.getCarId());

            messagePushBase.setTitle(title);
            messagePushBase.setContent(content);
            messagePushBase.setRemarks(remark);
            messagePushBase.setCarPush((short)1);

            //push
            NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
            notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
            notifyMessage.addNativeParameter("id", transportMain.getSrcMsgId().toString());

            //站内信
            NewsMessagePush newsMessage = NewsMessagePush.createByPushBase(messagePushBase);
            Long tsId = transportMapper.getTsIdBySrcMsgId(transportMain.getSrcMsgId());

            if (type == 1) {
                newsMessage = messageCenterPushService.changeToNewDesignMessage(newsMessage, 0, 1, null, String.format(MessagePageUrlEnum.CAR_GOODS_DETAIL.getPageUrl(), transportMain.getSrcMsgId(), tsId), null);
            } else {
                newsMessage = messageCenterPushService.changeToNewDesignMessage(newsMessage, 1, 1, "查看详情", String.format(MessagePageUrlEnum.CAR_GOODS_DETAIL.getPageUrl(), transportMain.getSrcMsgId(), tsId), null);
            }

            //短信
            ShortMessageBean shortMessage = new ShortMessageBean();
            shortMessage.setCellPhone(carUser.getCellPhone());

            //短信增加跳转货源详情链接
            String url = tytConfigService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.net");
            shortMessage.setContent(content + " " + url + "/jump.html?t=c&jp=cgd&id=" + transportMain.getSrcMsgId() + "&viewSource=12");
            shortMessage.setRemark(title);

            messageCenterPushService.sendMultiMessage(shortMessage, newsMessage, notifyMessage);
        }

        //增加inpush
        transportQuotedPriceInPush(tytTransportQuotedPrice, transportMain , type, addPrice);

    }

    private void transportQuotedPriceInPush(TytTransportQuotedPrice tytTransportQuotedPrice, TransportMain transportMain, Integer type, boolean addPrice) {
        MessagePushBase messagePushBase = new MessagePushBase();
        //货方同意或拒绝报价通知报价车方
        messagePushBase.addUserId(tytTransportQuotedPrice.getCarId());

        JSONObject extraJson = new JSONObject();
        String startAddress = transportMain.getStartCity() + transportMain.getStartArea();
        String destAddress = transportMain.getDestCity() + transportMain.getDestArea();

        extraJson.put("srcMsgId", transportMain.getSrcMsgId());
        extraJson.put("price", transportMain.getPrice());
        extraJson.put("content", startAddress + "——" + destAddress);
        String goodsName = StringUtils.isBlank(transportMain.getGoodTypeName()) ? "货源" : transportMain.getGoodTypeName();
        extraJson.put("infoText", goodsName + " " + transportMain.getWeight() + "吨 ");
        extraJson.put("addPrice", addPrice);
        if (type == 1) {
            //货方回价
            messagePushBase.setTitle("货主拒绝了您的报价，您可再次出价");
            messagePushBase.setContent("货主拒绝了您的报价，您可再次出价");
            messagePushBase.setRemarks("货主拒绝了您的报价，您可再次出价");
            extraJson.put("title", "货主拒绝了您的报价，您可再次出价");
        } else {
            //货方同意
            messagePushBase.setTitle("货主同意了您的报价");
            messagePushBase.setContent("货主同意了您的报价");
            messagePushBase.setRemarks("货主同意了您的报价");
            extraJson.put("title", "货主同意了您的报价");
        }
        messagePushBase.setCarPush((short)1);
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
        notifyMessage.setPushCode(PushCodeEnum.RECOMMEND_GOODS_PUSH.getCode());
        notifyMessage.setExtraData(extraJson.toString());
        log.info("发送报价inpush {}", JSON.toJSONString(notifyMessage));
        messageCenterPushService.sendMultiMessage(null, null, notifyMessage);

        if (addPrice) {
            List<Long> viewLogUserIdListBySrcMsgId = tytAppCallLogMapper.getViewLogUserIdListBySrcMsgId(transportMain.getSrcMsgId());
            if (CollectionUtils.isNotEmpty(viewLogUserIdListBySrcMsgId)) {
                //排除掉报价司机
                viewLogUserIdListBySrcMsgId.remove(tytTransportQuotedPrice.getCarId());
                //货源因为货主同意或拒绝报价修改了运费，并且运费涨价，则通知所有查看过该货源的车主
                transportQuotedPriceInPushAllViewUser(transportMain, viewLogUserIdListBySrcMsgId);
            }
        }
    }

    private void transportQuotedPriceInPushAllViewUser(TransportMain transportMain, List<Long> viewLogUserIdListBySrcMsgId) {
        if (CollectionUtils.isEmpty(viewLogUserIdListBySrcMsgId)) {
            return;
        }
        String startAddress = transportMain.getStartCity() + transportMain.getStartArea();
        String destAddress = transportMain.getDestCity() + transportMain.getDestArea();

        GoodsPushDto pushDto = new GoodsPushDto();
        pushDto.setSrcMsgId(transportMain.getId());
        pushDto.setPushSource("货源报价推送");
        pushDto.setTitle("货主上调了运费");
        String goodsName = StringUtils.isBlank(transportMain.getGoodTypeName()) ? "货源" : transportMain.getGoodTypeName();
        pushDto.setContent("货主上调了运费");
        pushDto.setUserIdList(viewLogUserIdListBySrcMsgId);
        pushDto.setPushType(PushTypeEnum.ALL_PUSH);
        pushDto.setPushCode(PushCodeEnum.RECOMMEND_GOODS_PUSH);
        pushDto.setSrcMsgId(transportMain.getSrcMsgId());
        JSONObject extraJson = new JSONObject();
        extraJson.put("srcMsgId", transportMain.getSrcMsgId());
        extraJson.put("price", transportMain.getPrice());
        extraJson.put("title", "货主上调了运费");
        extraJson.put("content", startAddress + "——" + destAddress);
        extraJson.put("infoText", goodsName + " " + transportMain.getWeight() + "吨 ");
        extraJson.put("addPrice", true);
        pushDto.setExtraJson(extraJson.toString());
        log.info("发送所有浏览过该货源的司机报价inpush {}", JSON.toJSONString(pushDto));

        try {
            apiInnerPushClient.sendPush(pushDto).execute();
        } catch (Exception e) {
            log.info("发送所有浏览过该货源的司机报价inpush失败原因：", e);
        }
    }

    @Override
    public ResultMsgBean checkCarryPriceIsRules(TransportMain transportMain, Long queryUserId, Long transportQuotedPriceId, int price) {
        try {
            int minPrice = 100;
            int maxPrice = 999999;

            //查询货源信息
            CarryPriceReq carryPriceReq = new CarryPriceReq();
            carryPriceReq.setStartProvince(transportMain.getStartProvinc());
            carryPriceReq.setStartCity(transportMain.getStartCity());
            carryPriceReq.setStartArea(transportMain.getStartArea());
            carryPriceReq.setDestProvince(transportMain.getDestProvinc());
            carryPriceReq.setDestCity(transportMain.getDestCity());
            carryPriceReq.setDestArea(transportMain.getDestArea());
            carryPriceReq.setGoodsName(transportMain.getTaskContent());
            carryPriceReq.setGoodsWeight(NumberConvertUtil.strToDouble(transportMain.getWeight()));
            carryPriceReq.setGoodsLength(NumberConvertUtil.strToDouble(transportMain.getLength()));
            carryPriceReq.setGoodsWide(NumberConvertUtil.strToDouble(transportMain.getWide()));
            carryPriceReq.setGoodsHigh(NumberConvertUtil.strToDouble(transportMain.getHigh()));
            carryPriceReq.setPublishType(transportMain.getPublishType());
            carryPriceReq.setSource(PriceSourceEnum.pay.getSource());
            carryPriceReq.setUserId(queryUserId);
            carryPriceReq.setSrcMsgId(transportMain.getSrcMsgId());
            carryPriceReq.setDistance(transportMain.getDistance());

            //请求数据部门的运价信息
            CarryPriceVo carryPriceVo = bsPublishTransportService.reqCarryPrice(carryPriceReq);

            if (carryPriceVo != null) {
                Integer thMinPrice = carryPriceVo.getThMinPrice();
                Integer thMaxPrice = carryPriceVo.getThMaxPrice();
                if(thMinPrice != null && thMinPrice > minPrice){
                    minPrice = thMinPrice;
                }
                if(thMaxPrice != null && thMaxPrice < maxPrice){
                    maxPrice = thMaxPrice;
                }
            }

            TytQuotedPriceBiData tytQuotedPriceBiData = null;
            //保存报价获取建议价BI所需数据
            if (transportQuotedPriceId == null) {
                //车方报价，queryUserId就是车方用户ID
                tytQuotedPriceBiData = new TytQuotedPriceBiData(null, transportMain.getId(), queryUserId
                        , 1, price, carryPriceVo == null ? "" : JSON.toJSONString(carryPriceVo), new Date());
            } else {
                //货方报价，从报价记录中获取车方用户ID
                TytTransportQuotedPrice tytTransportQuotedPrice = tytTransportQuotedPriceMapper.getQuotedPriceById(transportQuotedPriceId);
                if (tytTransportQuotedPrice != null && tytTransportQuotedPrice.getCarId() != null) {
                    tytQuotedPriceBiData = new TytQuotedPriceBiData(null, transportMain.getId(), tytTransportQuotedPrice.getCarId()
                            , 2, price, carryPriceVo == null ? "" : JSON.toJSONString(carryPriceVo), new Date());
                }
            }
            if (tytQuotedPriceBiData != null) {
                tytTransportQuotedPriceMapper.addTransportQuotedPriceBIDataLog(tytQuotedPriceBiData);
            }

            if (price > maxPrice || price < minPrice) {
                if (price > maxPrice) {
                    return ResultMsgBean.failResponse(10001, "您当前的出价过高，请重新输入");
                } else {
                    return ResultMsgBean.failResponse(10001, "您当前的出价过低，请重新输入");
                }
            }
            return ResultMsgBean.successResponse();
        } catch (Exception ex) {
            log.info("校验无价货源报价是否符合数据部门给出的合理报价范围失败");
            return ResultMsgBean.successResponse();
        }
    }

    @Override
    public ResultMsgBean checkTransportValidity(Long srcMsgId) {
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain != null && transportMain.getStatus() != null && transportMain.getPubDate() != null) {
            if ((transportMain.getStatus() != 1) || !TimeUtil.isToday(transportMain.getPubDate().getTime())) {
                //状态为无效
                return ResultMsgBean.failResponse(10001, "货源已失效");
            }

            boolean isHavePriceRuleTransport = false;
            //车方查看一口价货源是判断是否可以看到报价相关组件，非一口价货源由app来判断
            Integer priceType = tytTransportMainExtendMapper.getPriceTypeBySrcMsgId(srcMsgId);
            if ((transportMain.getInvoiceTransport() == null || transportMain.getInvoiceTransport() != 1)
                    && (transportMain.getExcellentGoods() == null || transportMain.getExcellentGoods() != 2 || (priceType != null && priceType == 2))) {
                isHavePriceRuleTransport = true;
            }

            if (StringUtils.isNotBlank(transportMain.getPrice()) && !isHavePriceRuleTransport) {
                return ResultMsgBean.failResponse(10001, "该货源不允许出价");
            }
        }
        return ResultMsgBean.successResponse();
    }

    /**
     * 是否可以出价/展示出价记录判断
     * 不校验一口价和无价
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public ResultMsgBean checkTransportValidityV2(Long srcMsgId) {
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain != null && transportMain.getStatus() != null && transportMain.getPubDate() != null) {
            if ((transportMain.getStatus() != 1) || !TimeUtil.isToday(transportMain.getPubDate().getTime())) {
                //状态为无效
                return ResultMsgBean.failResponse(10001, "货源已失效");
            }

            boolean isHavePriceRuleTransport = false;
            //车方查看一口价货源是判断是否可以看到报价相关组件，非一口价货源由app来判断
            Integer priceType = tytTransportMainExtendMapper.getPriceTypeBySrcMsgId(srcMsgId);
            if ((transportMain.getInvoiceTransport() == null || transportMain.getInvoiceTransport() != 1)
                    && (transportMain.getExcellentGoods() == null || transportMain.getExcellentGoods() != 2 || (priceType != null && priceType == 2))) {
                isHavePriceRuleTransport = true;
            }

            if (!isHavePriceRuleTransport) {
                return ResultMsgBean.failResponse(10001, "该货源不允许出价");
            }
        }
        return ResultMsgBean.successResponse();
    }

    @Override
    public boolean checkUserIsTransportOwner(Long userId, Long srcMsgId) {
        if (userId == null || srcMsgId == null) {
            return false;
        }
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain == null || transportMain.getUserId() == null) {
            return false;
        }
        return userId.compareTo(transportMain.getUserId()) == 0;
    }

    @Override
    public boolean getCarIsHaveQuotedPriceToTransport(Long carUserId, Long goodsId) {
        Integer count = tytTransportQuotedPriceMapper.getCarIsHaveQuotedPriceToTransport(carUserId, goodsId);
        return count != null && count > 0;
    }

    @Override
    public List<AllTransportQuotedPriceVo> getAllPublishingTransportQuotedPriceList(Long transportUserId) {
        List<AllTransportQuotedPriceVo> result = new ArrayList<>();

        //获取所有发布中货源的ID
        List<Long> inReleaseTransportIdList =  transportMainService.getInReleaseTransportIdList(transportUserId);
        if (CollectionUtils.isEmpty(inReleaseTransportIdList)) {
            return result;
        }
        List<TytTransportQuotedPrice> tytTransportQuotedPrices = tytTransportQuotedPriceMapper.getAllQuotedPriceListBySrcMsgIdList(inReleaseTransportIdList);
        if (CollectionUtils.isEmpty(tytTransportQuotedPrices)) {
            return result;
        }

        String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url_old", "http://www.teyuntong.com/rootdata");

        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        for (TytTransportQuotedPrice tytTransportQuotedPrice : tytTransportQuotedPrices) {

            AllTransportQuotedPriceVo allTransportQuotedPriceVo = new AllTransportQuotedPriceVo();

            BeanUtils.copyProperties(tytTransportQuotedPrice, allTransportQuotedPriceVo);

            allTransportQuotedPriceVo.setId(tytTransportQuotedPrice.getId());
            allTransportQuotedPriceVo.setCarUserName(tytTransportQuotedPrice.getCarUserName());
            allTransportQuotedPriceVo.setCarQuotedPrice(tytTransportQuotedPrice.getCarQuotedPrice());
            allTransportQuotedPriceVo.setCarQuotedPriceTime(tytTransportQuotedPrice.getCarQuotedPriceTime());

            User user;
            try {
                //返回总交易数、与我交易数
                List<CreditUserInfo> creditUserInfos = infofeeDetailMapper.queryCreditUserInfo("(" + allTransportQuotedPriceVo.getCarId().toString() + ")", transportUserId.toString());
                if (CollectionUtils.isNotEmpty(creditUserInfos)) {
                    CreditUserInfo creditUserInfo = creditUserInfos.get(0);
                    allTransportQuotedPriceVo.setCoopNums(creditUserInfo.getCoopNums());
                    allTransportQuotedPriceVo.setTradeNums(creditUserInfo.getTradeNums());
                }

                user = userService.getByUserId(allTransportQuotedPriceVo.getCarId());
                if (user != null) {
                    //返回拼好的头像
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(user.getHeadUrl())) {
                        String pattern = "^(http|https)";
                        Pattern compiledPattern = Pattern.compile(pattern);
                        Matcher matcher = compiledPattern.matcher(user.getHeadUrl());
                        if (!matcher.find()) {
                            allTransportQuotedPriceVo.setHeadUrl(tytServerPictureUrl + user.getHeadUrl());
                        } else {
                            allTransportQuotedPriceVo.setHeadUrl(user.getHeadUrl());
                        }
                    }

                    //车是否会员
                    UserPermission userPermission = userPermissionService.getUserPermission(tytTransportQuotedPrice.getCarId(), UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode());
                    allTransportQuotedPriceVo.setCarIsVip(null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus()) ? 1 : 0);

                    //返回是否实名认证
                    if (user.getVerifyPhotoSign() != null && user.getVerifyPhotoSign() == 1) {
                        allTransportQuotedPriceVo.setRealNameAuthentication(true);
                    } else {
                        allTransportQuotedPriceVo.setRealNameAuthentication(false);
                    }
                }

                allTransportQuotedPriceVo.setGoodCarPriceTransport(0);
                //返回货源信息
                TransportMain transportMain = transportMainService.getTransportMainForId(tytTransportQuotedPrice.getSrcMsgId());
                if (transportMain != null) {
                    allTransportQuotedPriceVo.setStartPoint(transportMain.getStartPoint());
                    allTransportQuotedPriceVo.setDestPoint(transportMain.getDestPoint());
                    allTransportQuotedPriceVo.setTaskContent(transportMain.getTaskContent());
                    allTransportQuotedPriceVo.setWeight(transportMain.getWeight());
                    allTransportQuotedPriceVo.setLength(transportMain.getLength());
                    allTransportQuotedPriceVo.setWide(transportMain.getWide());
                    allTransportQuotedPriceVo.setHigh(transportMain.getHigh());
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(transportMain.getLabelJson())
                            && JSONObject.parseObject(transportMain.getLabelJson()).containsKey("goodCarPriceTransport")
                            && JSONObject.parseObject(transportMain.getLabelJson()).getInteger("goodCarPriceTransport") == 1) {
                        allTransportQuotedPriceVo.setGoodCarPriceTransport(1);
                    }
                }

                result.add(allTransportQuotedPriceVo);

                //报价已被接受
                if (allTransportQuotedPriceVo.getFinalQuotedPriceIsDone() == 1) {
                    continue;
                }

                //车方未响应
                if (allTransportQuotedPriceVo.getCarQuotedPriceTimes().equals(allTransportQuotedPriceVo.getTransportQuotedPriceTimes())) {
                    allTransportQuotedPriceVo.setCarIsQuotedPriceAgain(false);
                    continue;
                }
                allTransportQuotedPriceVo.setCarIsQuotedPriceAgain(true);

                //货方出价次数用完，不能再进行出价
                if (allTransportQuotedPriceVo.getTransportQuotedPriceTimes() >= quotedPriceTotalTimes) {
                    allTransportQuotedPriceVo.setCanQuotedPrice(false);
                    continue;
                }
                allTransportQuotedPriceVo.setCanQuotedPrice(true);

            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        //货主查看本人货源所有报价
        if (RedisUtil.exists(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportUserId)) {
            //将所有货源的货主未浏览报价次数清空
            RedisUtil.del(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportUserId);
        }

        return result;
    }

    /**
     * 获取所有发布中货源报价列表
     * 多线程改造版本
     * @param transportUserId
     * @return
     */
    @Override
    public List<AllTransportQuotedPriceVo> getAllPublishingTransportQuotedPriceListV2(Long transportUserId) {
        List<AllTransportQuotedPriceVo> result = new ArrayList<>();

        //获取所有发布中货源的ID
        List<Long> inReleaseTransportIdList =  transportMainService.getInReleaseTransportIdList(transportUserId);
        if (CollectionUtils.isEmpty(inReleaseTransportIdList)) {
            return result;
        }
        List<TytTransportQuotedPrice> tytTransportQuotedPrices = tytTransportQuotedPriceMapper.getAllQuotedPriceListBySrcMsgIdList(inReleaseTransportIdList);
        if (CollectionUtils.isEmpty(tytTransportQuotedPrices)) {
            return result;
        }

        String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url_old", "http://www.teyuntong.com/rootdata");

        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        ExecutorService executorService = Executors.newFixedThreadPool(5, new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "quotedPrice-thread-" + threadNumber.getAndIncrement());
            }
        });
        CountDownLatch latch = new CountDownLatch(tytTransportQuotedPrices.size());
        List<Future<AllTransportQuotedPriceVo>> futures = new ArrayList<>();

        for (TytTransportQuotedPrice tytTransportQuotedPrice : tytTransportQuotedPrices) {
            Future<AllTransportQuotedPriceVo> future = executorService.
                    submit(new TransportQuotedPriceVoTask(tytTransportQuotedPrice, tytServerPictureUrl, quotedPriceTotalTimes, latch));
            futures.add(future);
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("获取所有发布中货源出价记录 CountDownLatch await InterruptedException, transportUserId:{}", transportUserId, e);
            throw new RuntimeException(e);
        }

        for (Future<AllTransportQuotedPriceVo> future : futures) {
            try {
                AllTransportQuotedPriceVo allTransportQuotedPriceVo = future.get();
                if (allTransportQuotedPriceVo != null) {
                    if (allTransportQuotedPriceVo.getQuotedType() != null && allTransportQuotedPriceVo.getQuotedType() == 0
                            && allTransportQuotedPriceVo.getFinalQuotedPriceIsDone() != null && allTransportQuotedPriceVo.getFinalQuotedPriceIsDone() == 1) {
                        allTransportQuotedPriceVo.setHaveCallToCarButton(1);
                    }
                    result.add(allTransportQuotedPriceVo);
                }
            } catch (Exception e) {
                log.error("获取所有发布中货源出价记录，从futures中获取数据异常,transportUserId:{}", transportUserId, e);
            }
        }
        executorService.shutdown();

        //货主查看本人货源所有报价
        if (RedisUtil.exists(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportUserId)) {
            //将所有货源的货主未浏览报价次数清空
            RedisUtil.del(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportUserId);
        }

        return result;
    }

     class TransportQuotedPriceVoTask implements Callable<AllTransportQuotedPriceVo> {
        private final TytTransportQuotedPrice tytTransportQuotedPrice;
        private final String tytServerPictureUrl;
        private final int quotedPriceTotalTimes;
        private final CountDownLatch countDownLatch;

        public TransportQuotedPriceVoTask(TytTransportQuotedPrice tytTransportQuotedPrice, String tytServerPictureUrl, int quotedPriceTotalTimes, CountDownLatch countDownLatch) {
            this.tytTransportQuotedPrice = tytTransportQuotedPrice;
            this.tytServerPictureUrl = tytServerPictureUrl;
            this.quotedPriceTotalTimes = quotedPriceTotalTimes;
            this.countDownLatch = countDownLatch;
        }

        @Override
        public AllTransportQuotedPriceVo call() {
            AllTransportQuotedPriceVo allTransportQuotedPriceVo = new AllTransportQuotedPriceVo();
            try {
                BeanUtils.copyProperties(tytTransportQuotedPrice, allTransportQuotedPriceVo);

                allTransportQuotedPriceVo.setId(tytTransportQuotedPrice.getId());
                allTransportQuotedPriceVo.setCarUserName(tytTransportQuotedPrice.getCarUserName());
                allTransportQuotedPriceVo.setCarQuotedPrice(tytTransportQuotedPrice.getCarQuotedPrice());
                allTransportQuotedPriceVo.setCarQuotedPriceTime(tytTransportQuotedPrice.getCarQuotedPriceTime());
                allTransportQuotedPriceVo.setGoodCarPriceTransport(0);
                //返回货源信息
                TransportMain transportMain = transportMainService.getTransportMainForId(tytTransportQuotedPrice.getSrcMsgId());
                if (transportMain != null) {
                    allTransportQuotedPriceVo.setStartPoint(transportMain.getStartPoint());
                    allTransportQuotedPriceVo.setDestPoint(transportMain.getDestPoint());
                    allTransportQuotedPriceVo.setTaskContent(transportMain.getTaskContent());
                    allTransportQuotedPriceVo.setWeight(transportMain.getWeight());
                    allTransportQuotedPriceVo.setLength(transportMain.getLength());
                    allTransportQuotedPriceVo.setWide(transportMain.getWide());
                    allTransportQuotedPriceVo.setHigh(transportMain.getHigh());
                    allTransportQuotedPriceVo.setPrice(transportMain.getPrice());
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(transportMain.getLabelJson())
                            && JSONObject.parseObject(transportMain.getLabelJson()).containsKey("goodCarPriceTransport")
                            && JSONObject.parseObject(transportMain.getLabelJson()).getInteger("goodCarPriceTransport") == 1) {
                        allTransportQuotedPriceVo.setGoodCarPriceTransport(1);
                    }
                }

                fillSingleVO(tytServerPictureUrl, allTransportQuotedPriceVo);
                if (Objects.nonNull(transportMain)) {
                    allTransportQuotedPriceVo.setGoodsTypeName(transportMain.getGoodTypeName());
                    Integer goodsTypeNum = callPhoneRecordService.getGoodsTypeNum(tytTransportQuotedPrice.getCarId(), transportMain.getGoodTypeName());
                    allTransportQuotedPriceVo.setGoodsTypeNum(goodsTypeNum);
                }

                // 查询是否发放曝光卡
                ExposureCardGiveawayRecordDO queryGiveawayRecord = new ExposureCardGiveawayRecordDO();
                queryGiveawayRecord.setStatus(1);
                queryGiveawayRecord.setSrcMsgId(tytTransportQuotedPrice.getSrcMsgId());
                allTransportQuotedPriceVo.setGiveawayExposureCard(CollectionUtils.isEmpty(exposureCardGiveawayRecordMapper.select(queryGiveawayRecord)) ? 0 : 1);

                //报价已被接受
                if (allTransportQuotedPriceVo.getFinalQuotedPriceIsDone() == 1) {
                    return allTransportQuotedPriceVo;
                }

                //车方未响应
                if (allTransportQuotedPriceVo.getCarQuotedPriceTimes().equals(allTransportQuotedPriceVo.getTransportQuotedPriceTimes())) {
                    allTransportQuotedPriceVo.setCarIsQuotedPriceAgain(false);
                    return allTransportQuotedPriceVo;
                }
                allTransportQuotedPriceVo.setCarIsQuotedPriceAgain(true);

                //货方出价次数用完，不能再进行出价
                if (allTransportQuotedPriceVo.getTransportQuotedPriceTimes() >= quotedPriceTotalTimes) {
                    allTransportQuotedPriceVo.setCanQuotedPrice(false);
                    return allTransportQuotedPriceVo;
                }
                allTransportQuotedPriceVo.setCanQuotedPrice(true);

            } catch (Exception e) {
                log.error("获取所有发布中货源出价记录, 多线程执行出价记录输出组装失败，出价ID:{}", tytTransportQuotedPrice.getId(), e);
            } finally {
                countDownLatch.countDown();
            }
            return allTransportQuotedPriceVo;
        }
    }

    @Override
    public ResultMsgBean getCarLeaveTransportSingleDetailTabData(Long carUserId, Long srcMsgId) {
        //弹过窗的不再谈
        if (RedisUtil.mapExists(TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY + ":" + carUserId, srcMsgId.toString())) {
            return ResultMsgBean.successResponse(false);
        }

        TytTransportQuotedPrice quotedPriceByCarUserIdAndTransportMainId = tytTransportQuotedPriceMapper.getQuotedPriceByCarUserIdAndTransportMainId(carUserId, srcMsgId);
        if (quotedPriceByCarUserIdAndTransportMainId != null) {
            return ResultMsgBean.successResponse(false);
        }
        recordCarLeaveTransportSingleDetail(carUserId, srcMsgId);
        return ResultMsgBean.successResponse(true);
    }

    @Override
    public String getTransportQuotedPriceLPageWord(Long srcMsgId) {
        TransportMain bySrcMsgId = transportMainService.getBySrcMsgId(srcMsgId);
        if (bySrcMsgId == null) {
            return "";
        }
        boolean haveSameTransportInPublish = transportMainService.isHaveSameTransportInPublish(srcMsgId);
        if (haveSameTransportInPublish) {
            return "此货源多人发布，加价可提前锁定司机";
        } else {
            boolean haveSameStartCityAndDestCityTransportInPublish = transportMainService.isHaveSameStartCityAndDestCityTransportInPublish(srcMsgId);
            if (haveSameStartCityAndDestCityTransportInPublish) {
                return "当前同路线货源多，合理加价找车更快";
            }
        }
        return null;
    }

    @Override
    public TransportQuotedPriceLeaveTabVO getTransportQuotedPriceLeaveTab(Long srcMsgId) {
        TransportQuotedPriceLeaveTabVO transportQuotedPriceLeaveTabVO = new TransportQuotedPriceLeaveTabVO();
        transportQuotedPriceLeaveTabVO.setShowLeaveTab(false);

        Integer transportQuotedPriceLeaveTabShowOnOff = tytConfigService.getIntValue("transport_quoted_price_leave_tab_show_on_off", 0);
        if (transportQuotedPriceLeaveTabShowOnOff == null || transportQuotedPriceLeaveTabShowOnOff != 1) {
            return transportQuotedPriceLeaveTabVO;
        }

        if (RedisUtil.exists(TRANSPORT_QUOTED_PRICE_TRANSPORT_LEAVE_KEY + ":" +  srcMsgId)) {
            return transportQuotedPriceLeaveTabVO;
        }
        int transportHaveOptionQuotedPriceCount = getTransportHaveOptionQuotedPriceCount(srcMsgId);
        Integer transportQuotedPriceCount = getTransportQuotedPriceCountBySrcMsgIs(srcMsgId);
        if (transportHaveOptionQuotedPriceCount == 0 && transportQuotedPriceCount != 0) {
            transportQuotedPriceLeaveTabVO.setShowLeaveTab(true);

            //相似货源、同路线文案
            String word = "";
            boolean haveSameTransportInPublish = transportMainService.isHaveSameTransportInPublish(srcMsgId);
            if (haveSameTransportInPublish) {
                word = "当前相似货源多，";
            } else {
                boolean haveSameStartCityAndDestCityTransportInPublish = transportMainService.isHaveSameStartCityAndDestCityTransportInPublish(srcMsgId);
                if (haveSameStartCityAndDestCityTransportInPublish) {
                    word = "当前同路线货源多，";
                }
            }

            Integer transportQuotedPriceCountBySrcMsgIs = getTransportQuotedPriceCountBySrcMsgIs(srcMsgId);

            if (transportQuotedPriceCountBySrcMsgIs == 1) {
                TytTransportQuotedPrice quotedPriceById = tytTransportQuotedPriceMapper.getQuotedPriceLastOneBySrcMsgId(srcMsgId);
                transportQuotedPriceLeaveTabVO.setTabType(1);
                transportQuotedPriceLeaveTabVO.setNum(quotedPriceById.getCarQuotedPrice());
                transportQuotedPriceLeaveTabVO.setReason(quotedPriceById.getReason());
                String wordPrefix = "有司机出价" + quotedPriceById.getCarQuotedPrice() + "元";
                if (Objects.equals(QuotedTypeEnum.SYSTEM.getCode(), quotedPriceById.getQuotedType())) {
                    wordPrefix = "刚刚有" + quotedPriceById.getTransportNum() + "票相似货源以" + quotedPriceById.getCarQuotedPrice() + "元成交";
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(transportQuotedPriceLeaveTabVO.getReason())) {
                    wordPrefix += "（留言：" + transportQuotedPriceLeaveTabVO.getReason() + "）";
                }
                word += wordPrefix + "，可通知司机接单并更新运费";
                transportQuotedPriceLeaveTabVO.setQuotedPriceId(quotedPriceById.getId());

            } else {
                transportQuotedPriceLeaveTabVO.setTabType(2);
                transportQuotedPriceLeaveTabVO.setNum(transportQuotedPriceCountBySrcMsgIs);
                String middle = "名司机";
                int systemQuoted = tytTransportQuotedPriceMapper.countSystemQuotedPrice(srcMsgId);
                if (systemQuoted > 0) {
                    middle = "条";
                }
                word += ("有" + transportQuotedPriceCountBySrcMsgIs + middle + "出价，同意后将通知司机接单");
            }
            // TransportMain transportMain = transportMainService.getBySrcMsgId(srcMsgId);
            // if (StringUtils.isBlank(transportMain.getPrice()) || Objects.equals("0", transportMain.getPrice())) {
            //     word += "，额外获得40分曝光";
            // }
            transportQuotedPriceLeaveTabVO.setWord(word);
            RedisUtil.set(TRANSPORT_QUOTED_PRICE_TRANSPORT_LEAVE_KEY + ":" + srcMsgId, "1", 60 * 60 * 30);
        }
        return transportQuotedPriceLeaveTabVO;
    }

    @Override
    public TransportMain getTransportQuotedPriceListVO(Long srcMsgId, Long transportUserId) {
        return transportMainService.getBySrcMsgId(srcMsgId);
    }

    @Override
    public int getTransportHaveOptionQuotedPriceCount(Long srcMsgId) {
        return tytTransportQuotedPriceMapper.getTransportHaveOptionQuotedPriceCount(srcMsgId);
    }

    /**
     * 获取当前用户发布中货源最新一条是沟通记录还是出价记录
     * 0-无记录，1-沟通记录，2-出价记录
     *
     * @param userId
     * @return
     */
    @Override
    public JSONObject transportNewestRecordType(Long userId) {
        JSONObject result = new JSONObject();
        int recordType = 0;

        try {
            List<Long> srcMsgIds =  transportMainService.getUserPublishingSrcMsgIds(userId);
            if (CollectionUtils.isNotEmpty(srcMsgIds)) {
                Optional<CallPhoneRecordDO> callPhoneRecordOpt = Optional.ofNullable(callPhoneRecordService.getLatestCallRecord(srcMsgIds));
                Optional<TytTransportQuotedPrice> quotedPriceOpt = Optional.ofNullable(tytTransportQuotedPriceMapper.getLatestQuotedRecord(srcMsgIds));

                if (callPhoneRecordOpt.isPresent() && quotedPriceOpt.isPresent()) {
                    Instant callTime = callPhoneRecordOpt.get().getCreateTime().toInstant();
                    Instant quoteTime = quotedPriceOpt.get().getCarQuotedPriceTime().toInstant();

                    if (callTime.isAfter(quoteTime)) {
                        recordType = 1;
                    } else {
                        recordType = 2;
                    }
                } else if (callPhoneRecordOpt.isPresent()) {
                    recordType = 1;
                } else if (quotedPriceOpt.isPresent()) {
                    recordType = 2;
                }
            }
        } catch (Exception e) {
            log.error("transportNewestRecordType error: {}", e.getMessage(), e);
        }

        result.put("recordType", recordType);
        return result;
    }

    @Override
    public TransportQuotedPriceTabDataVO getTransportQuotedPriceTabData(Long transportQuotedPriceId) {
        TytTransportQuotedPrice quotedPrice = tytTransportQuotedPriceMapper.getQuotedPriceById(transportQuotedPriceId);

        TransportQuotedPriceTabDataVO result = new TransportQuotedPriceTabDataVO();
        result.setSrcMsgId(quotedPrice.getSrcMsgId());

        result.setTitle("司机报价太高？给司机回价");
        result.setSystemQuotedPrice(false);
        if (quotedPrice.getCarId() == -1) {
            result.setSystemQuotedPrice(true);
            int viewLogCount = tytAppCallLogMapper.getViewLogCountBySrcMsgId(quotedPrice.getSrcMsgId());
            if (viewLogCount > 0) {
                result.setTitle("有" + viewLogCount + "名司机感兴趣，等待您的回价");
            } else {
                result.setTitle("联系司机太少？回价可增加曝光");
            }
        }

        TransportMain transportMain = transportMainService.getBySrcMsgId(quotedPrice.getSrcMsgId());

        result.setTransportPrice(BigDecimal.ZERO);

        BigDecimal minPrice = new BigDecimal(quotedPrice.getCarQuotedPrice() / 2).setScale(0, BigDecimal.ROUND_HALF_UP);
        BigDecimal maxPrice = new BigDecimal(quotedPrice.getCarQuotedPrice());
        if (StringUtils.isNotBlank(transportMain.getPrice()) && new BigDecimal(transportMain.getPrice()).compareTo(BigDecimal.ZERO) > 0) {
            //货源有价并且不是系统报价
            minPrice = new BigDecimal(transportMain.getPrice());
            result.setTransportPrice(new BigDecimal(transportMain.getPrice()));
        }

        result.setHaveChangePriceButton(false);
        result.setHavePriceSeekBar(false);
        BigDecimal defaultPrice = maxPrice.multiply(new BigDecimal(0.9)).setScale(0, BigDecimal.ROUND_HALF_UP);
        if (maxPrice.compareTo(result.getTransportPrice()) > 0) {
            result.setHaveChangePriceButton(true);
            result.setHavePriceSeekBar(true);
            defaultPrice = maxPrice.add(minPrice).divide(new BigDecimal(2), 0, RoundingMode.UP);
        }
        if (quotedPrice.getCarId() == -1 && result.getTransportPrice().compareTo(BigDecimal.ZERO) > 0) {
            //有价货源系统报价无seekbar和修改运费按钮
            result.setHaveChangePriceButton(false);
            result.setHavePriceSeekBar(false);
        }


        if (quotedPrice.getCarId() == -1) {
            //系统报价时默认值取系统报价值
            defaultPrice = maxPrice;
            result.setSubheading("刚刚有" + quotedPrice.getTransportNum() + "票以" + quotedPrice.getCarQuotedPrice() + "元成交");
        } else {
            SameTransportAveragePriceReq sameTransportAveragePriceReq = new SameTransportAveragePriceReq();
            sameTransportAveragePriceReq.setSrcMsgId(quotedPrice.getSrcMsgId());
            ResultMsgBean sameTransportAveragePrice = freightDetailService.getSameTransportAveragePrice(sameTransportAveragePriceReq);
            if (sameTransportAveragePrice != null && sameTransportAveragePrice.getData() != null && sameTransportAveragePrice.getData() instanceof SameTransportAveragePriceData) {
                SameTransportAveragePriceData sameTransportAveragePriceData = (SameTransportAveragePriceData) sameTransportAveragePrice.getData();
                if (sameTransportAveragePriceData != null && sameTransportAveragePriceData.getAveragePrice() != null
                        && sameTransportAveragePriceData.getAveragePrice().compareTo(minPrice) > 0
                        && sameTransportAveragePriceData.getAveragePrice().compareTo(maxPrice) < 0) {
                    defaultPrice = sameTransportAveragePriceData.getAveragePrice();
                    result.setSubheading("平台均价参考：" + defaultPrice + "元");
                }
            }
        }

        result.setMinPrice(minPrice);
        result.setMaxPrice(maxPrice);
        result.setDefaultPrice(defaultPrice);
        return result;
    }

    @Override
    public void recordCarLeaveTransportSingleDetail(Long carUserId, Long srcMsgId) {
        long secondsUntilTomorrow = Duration.between(java.time.LocalDateTime.now(), java.time.LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).plusDays(1)).get(ChronoUnit.SECONDS);
        if (!RedisUtil.exists(TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY + ":" + carUserId)) {
            RedisUtil.setMap(TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY + ":" + carUserId, srcMsgId.toString(), "1", Integer.parseInt(String.valueOf(secondsUntilTomorrow)));
        } else {
            RedisUtil.mapPut(TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY + ":" + carUserId, srcMsgId.toString(), "1");
        }
    }

    private static final String[] UNITS = {"", "十", "百", "千"};
    private static final String[] NUMS = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] BIG_UNITS = {"", "万", "亿"};

    private String numberToChinese(int num) {
        if (num == 0) {
            return "零";
        }

        StringBuilder result = new StringBuilder();
        int unitPos = 0; // 用于标记当前在哪个数量级（个、万、亿）
        boolean needZero = false; // 用于标记是否需要加“零”

        while (num > 0) {
            StringBuilder part = new StringBuilder();
            int partNum = num % 10000;

            if (needZero && partNum < 1000) {
                result.insert(0, NUMS[0]); // 补零
            }

            boolean hasZero = false; // 标记是否有过零
            for (int i = 0; i < 4 && partNum > 0; i++) {
                int digit = partNum % 10;
                if (digit != 0) {
                    if (hasZero) {
                        part.insert(0, NUMS[0]);
                        hasZero = false;
                    }
                    part.insert(0, UNITS[i]).insert(0, NUMS[digit]);
                } else {
                    hasZero = true;
                }
                partNum /= 10;
            }

            if (part.length() > 0 || unitPos == 0) {
                part.append(BIG_UNITS[unitPos]);
            }

            result.insert(0, part);
            num /= 10000;
            unitPos++;
        }

        // 去掉末尾的“零”
        while (result.length() > 0 && result.charAt(result.length() - 1) == '零') {
            result.setLength(result.length() - 1);
        }

        // 特殊处理
        String resultStr = result.toString();

        // 添加"一"在"十"之前，若适用
        if (resultStr.startsWith("十")) {
            resultStr = "一" + resultStr;
        }

        return resultStr;
    }


}
