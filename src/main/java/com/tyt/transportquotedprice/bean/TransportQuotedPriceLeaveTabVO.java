package com.tyt.transportquotedprice.bean;

import lombok.Data;

@Data
public class TransportQuotedPriceLeaveTabVO {

    /**
     * 是否弹挽留弹窗
     */
    private Boolean showLeaveTab;

    /**
     * 弹窗样式 1:显示同意拒绝按钮；2:显示查看按钮
     */
    private Integer tabType;

    /**
     * 弹窗内容
     */
    private String word;

    /**
     * 弹窗数值，如果弹窗样式为1，则数值为车方报价金额，如果弹窗样式为2，则数值为未完结的车方报价人数
     */
    private Integer num;

    /**
     * 报价ID，仅在弹窗样式为1时有效，用于点击同意按钮时传参
     */
    private Long quotedPriceId;

    /**
     * 车方报价理由
     */
    private String reason;

}
