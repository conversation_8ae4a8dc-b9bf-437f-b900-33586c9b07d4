package com.tyt.transportquotedprice.bean;

import lombok.Data;

@Data
public class AllTransportQuotedPriceVo extends TytTransportQuotedPricetTransportVO {

    private Integer carIsVip;

    //头像，已拼好
    private String headUrl;

    //是否实名认证
    private Boolean realNameAuthentication;

    /**
     * 平台交易数
     */
    private String tradeNums;

    /**
     * 与我交易数
     */
    private String coopNums;

    /**
     * 出发地(省市区以减号-分割开)
     */
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    private String destPoint;

    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 重量单位吨
     */
    private String weight;

    /**
     * 货物长单位米
     */
    private String length;

    /**
     * 货物宽单位米
     */
    private String wide;

    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 是否优车定价，1:是，0:否
     */
    private Integer goodCarPriceTransport;

    /**
     * 是否送曝光卡：1是0否
     */
    private Integer giveawayExposureCard;
}
