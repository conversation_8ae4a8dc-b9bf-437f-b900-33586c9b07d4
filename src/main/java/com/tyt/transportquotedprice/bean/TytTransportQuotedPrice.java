package com.tyt.transportquotedprice.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_transport_quoted_price")
public class TytTransportQuotedPrice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 车主ID
     */
    @Column(name = "car_id")
    private Long carId;

    /**
     * 车主昵称
     */
    @Column(name = "car_user_name")
    private String carUserName;

    /**
     * 货主ID
     */
    @Column(name = "transport_user_id")
    private Long transportUserId;

    /**
     * 货主昵称
     */
    @Column(name = "transport_user_name")
    private String transportUserName;

    /**
     * 货源主表ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 车方报价
     */
    @Column(name = "car_quoted_price")
    private Integer carQuotedPrice;

    /**
     * 货方报价
     */
    @Column(name = "transport_quoted_price")
    private Integer transportQuotedPrice;

    /**
     * 车方是否已出价 0：未出价；1：已出价
     */
    @Column(name = "car_is_done")
    private Integer carIsDone;

    /**
     * 货方是否已出价 0：未出价；1：已出价
     */
    @Column(name = "transport_is_done")
    private Integer transportIsDone;

    /**
     * 是否完成最终报价 0：未完成；1：已完成
     */
    @Column(name = "final_quoted_price_is_done")
    private Integer finalQuotedPriceIsDone;

    /**
     * 最终报价
     */
    @Column(name = "final_quoted_price")
    private Integer finalQuotedPrice;

    /**
     * 最终报价遵循 1：遵循车方报价；2：遵循货方报价
     */
    @Column(name = "final_quoted_price_type")
    private Integer finalQuotedPriceType;

    /**
     * 车方报价时间
     */
    @Column(name = "car_quoted_price_time")
    private Date carQuotedPriceTime;

    /**
     * 货方报价时间
     */
    @Column(name = "transport_quoted_price_time")
    private Date transportQuotedPriceTime;

    /**
     * 车方报价次数
     */
    @Column(name = "car_quoted_price_times")
    private Integer carQuotedPriceTimes;

    /**
     * 出价类型：0-用户出价，1-系统出价
     */
    @Column(name = "quoted_type")
    private Integer quotedType;

    /**
     * 系统出价时相似货源数量
     */
    @Column(name = "transport_num")
    private Integer transportNum;


    /**
     * 货方报价次数
     */
    @Column(name = "transport_quoted_price_times")
    private Integer transportQuotedPriceTimes;

    /**
     *车方加价原因
     */
    @Column(name = "reason")
    private String reason;
}