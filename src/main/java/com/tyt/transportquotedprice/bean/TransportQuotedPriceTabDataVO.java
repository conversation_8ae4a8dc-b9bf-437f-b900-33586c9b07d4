package com.tyt.transportquotedprice.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransportQuotedPriceTabDataVO {

    /**
     * 右端点值
     */
    private BigDecimal maxPrice;

    /**
     * 右端点值
     */
    private BigDecimal minPrice;

    /**
     * 右端点值
     */
    private BigDecimal defaultPrice;

    /**
     * 货源运费
     */
    private BigDecimal transportPrice;

    /**
     * 是否有可修改运费按钮
     */
    private Boolean haveChangePriceButton;

    /**
     * 是否展示SeekBar（当车方报价高于货源运费时展示）
     */
    private Boolean havePriceSeekBar;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 该报价是否为系统报价
     */
    private Boolean systemQuotedPrice;

    /**
     * 主标题（分为系统报价主标题和真人报价主标题）
     */
    private String title;

    /**
     * 副标题（刚刚有X票以Y元成交、平台均价参考）
     */
    private String subheading;

}