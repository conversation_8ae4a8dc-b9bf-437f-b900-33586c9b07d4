package com.tyt.transportquotedprice.bean;

import lombok.Data;

/**
    if (!carIsQuotedPriceOnce) {
        //车方从未出过价
    } else if (finalQuotedPriceIsDone == 1) {
        //报价已被接受，页面展示 finalQuotedPrice 最终报价字段，并跳转到支付
    } else if (!transportIsQuotedPrice) {
        //货方未响应车方出价
    } else if (canQuotedPrice) {
        //货方驳回了车方的出价，并已回价
        //此时车方允许出价
        //页面展示 transportQuotedPrice 货方报价相关字段，并展示驳回、同意两个按钮
    } else {
        //车方不允许出价了
        //页面展示 transportQuotedPrice 货方报价相关字段，并只展示同意按钮
    }
 */
@Data
public class TytTransportQuotedPriceCarVO extends TytTransportQuotedPrice{

    /**
     * 车方是否出过价
     */
    private Boolean carIsQuotedPriceOnce;

    /**
     * 货方是否已回价
     */
    private Boolean transportIsQuotedPrice;

    /**
     * 车方是否可出价
     */
    private Boolean canQuotedPrice;

}