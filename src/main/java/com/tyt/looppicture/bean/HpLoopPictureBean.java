package com.tyt.looppicture.bean;


public class HpLoopPictureBean implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -747740457686591245L;
	private Long id;	
	private String pictureUrl;
	private String title;
	private String openUrl;	
	private Short sort;
	public Long getId() {
		return id;
	}
	public String getPictureUrl() {
		return pictureUrl;
	}
	public String getTitle() {
		return title;
	}
	public String getOpenUrl() {
		return openUrl;
	}
	public Short getSort() {
		return sort;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public void setOpenUrl(String openUrl) {
		this.openUrl = openUrl;
	}
	public void setSort(Short sort) {
		this.sort = sort;
	}	
}
