package com.tyt.looppicture.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.looppicture.bean.HpLoopPictureBean;
import com.tyt.looppicture.service.HpLoopPictureService;
import com.tyt.model.HpLoopPicture;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.TimeUtil;

@Service(value = "hpLoopPictureService")
public class HpLoopPictureServiceImpl extends BaseServiceImpl<HpLoopPicture, Long> implements HpLoopPictureService {
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;
	
	@Resource(name = "hpLoopPictureDao")
	public void setBaseDao(BaseDao<HpLoopPicture, Long> hpLoopPictureDao) {
		super.setBaseDao(hpLoopPictureDao);
	}
	
	private static final int activeTime = 24*60*60;
	
	public Map<String,List<HpLoopPictureBean>> getHpLoopPictureBean(String address){
		Map<String,List<HpLoopPictureBean>> map=null;
		String value = tytConfigService.getStringValue("appHomePageLoopPicture", "app_home_page_loop_picture_{date}");
		String key = StringUtils.replaceEach(value, new String[]{"{date}"},
				new String[]{TimeUtil.formatDate_(new Date())});
	
		List<HpLoopPicture> list=null;
		Object obj= RedisUtil.getObject(key) ;
		if(obj==null){
			String hql="from HpLoopPicture where status=? and  openClose=? ";
			list=this.getBaseDao().find(hql,(short)0,(short)1);
			if(list!=null && list.size()>0){
				RedisUtil.setObject(key, list, activeTime);
			}else
				return null;
		}else{
			list=(List<HpLoopPicture>)obj;
		}
		if(list!=null && list.size()>0){
			List<HpLoopPictureBean> brandLoopPictureList =new ArrayList<HpLoopPictureBean>();
	
			List<HpLoopPictureBean> carLoopPictureList =new ArrayList<HpLoopPictureBean>();
	
			List<HpLoopPictureBean> goodsLoopPictureList =new ArrayList<HpLoopPictureBean>();

			List<HpLoopPictureBean> startPictureList =new ArrayList<HpLoopPictureBean>();

			List<HpLoopPictureBean> infoFeeCarOwnerList =new ArrayList<HpLoopPictureBean>();

			List<HpLoopPictureBean> infoFeeShipperList =new ArrayList<HpLoopPictureBean>();

			List<HpLoopPictureBean> goodsDetailPictureList =new ArrayList<HpLoopPictureBean>();
			List<HpLoopPictureBean> completePaymentPictureList =new ArrayList<HpLoopPictureBean>();
			List<HpLoopPictureBean> pubGoodsPictureList =new ArrayList<HpLoopPictureBean>();
			
			for(HpLoopPicture hpLoopPicture:list){
				HpLoopPictureBean hpb=new HpLoopPictureBean();
				BeanUtils.copyProperties(hpLoopPicture, hpb);
				// 是否是启动图
				if (hpLoopPicture.getType().intValue()==5) {
					startPictureList.add(hpb);
				} else if(hpLoopPicture.getType().intValue()==1){ //是否是品宣图
					brandLoopPictureList.add(hpb);
				}else if (hpLoopPicture.getType().intValue()==6 && hpLoopPicture.getDisplaySite().intValue()==8){//货源详情页banner
					goodsDetailPictureList.add(hpb);
				}else if (hpLoopPicture.getType().intValue()==6 && hpLoopPicture.getDisplaySite().intValue()==9){//支付完成页banner
					completePaymentPictureList.add(hpb);
				}else if (hpLoopPicture.getType().intValue()==6 && hpLoopPicture.getDisplaySite().intValue()==10){//发货完成页banner
					pubGoodsPictureList.add(hpb);
				}else if (hpLoopPicture.getType().intValue()==2 && hpLoopPicture.getDisplaySite().intValue()==6){//车方信息费banner
					infoFeeCarOwnerList.add(hpb);
				}else if (hpLoopPicture.getType().intValue()==2 && hpLoopPicture.getDisplaySite().intValue()==7){//货方信息费banner
					infoFeeShipperList.add(hpb);
				}else{
					//判断是否是发货方
					if(hpLoopPicture.getType().intValue()==2 && hpLoopPicture.getDisplaySite().intValue()==1){				
						if (address != null) {
							//是否是保定
							if (address.indexOf("保定")!=-1) {
								//判读是不是保定隐藏
								if(hpLoopPicture.getBaodingHide().intValue()==0){
									goodsLoopPictureList.add(hpb);
								}
							}else{
								//判读是否是非保定隐藏
								if(hpLoopPicture.getWrongBaodingHide().intValue()==0){
									goodsLoopPictureList.add(hpb);
								}
							}
						}else	
							goodsLoopPictureList.add(hpb);
					}else{
						if(hpLoopPicture.getType().intValue()==2 && hpLoopPicture.getDisplaySite().intValue()==2){
							if (address != null) {
								//是否是保定
								if (address.indexOf("保定")!=-1)  {
									//判读是不是保定隐藏
									if(hpLoopPicture.getBaodingHide().intValue()==0){
										carLoopPictureList.add(hpb);
									}
								}else{
									//判读是否是非保定隐藏
									if(hpLoopPicture.getWrongBaodingHide().intValue()==0){
										carLoopPictureList.add(hpb);
									}
								}
							}else
							carLoopPictureList.add(hpb);
						}
					}
				}
			}
			//排序
			if(brandLoopPictureList!=null)
			sortHpLoopPictureBean(brandLoopPictureList);
			
			if(goodsLoopPictureList!=null)
			sortHpLoopPictureBean(goodsLoopPictureList);
			
			if(carLoopPictureList!=null)
			sortHpLoopPictureBean(carLoopPictureList);

			if(startPictureList!=null)
			sortHpLoopPictureBean(startPictureList);
			if(infoFeeCarOwnerList!=null)
				sortHpLoopPictureBean(infoFeeCarOwnerList);
			if(infoFeeShipperList!=null)
				sortHpLoopPictureBean(infoFeeShipperList);
			if(goodsDetailPictureList!=null)
				sortHpLoopPictureBean(goodsDetailPictureList);
			if(completePaymentPictureList!=null)
				sortHpLoopPictureBean(completePaymentPictureList);
			if(pubGoodsPictureList!=null)
				sortHpLoopPictureBean(pubGoodsPictureList);

			
			map=new HashMap<String,List<HpLoopPictureBean>>();
			map.put("brandLoopPictureList", brandLoopPictureList);
			map.put("goodsLoopPictureList", goodsLoopPictureList);
			map.put("carLoopPictureList", carLoopPictureList);
			map.put("startPictureList", startPictureList);
			map.put("infoFeeCarOwnerList", infoFeeCarOwnerList);
			map.put("infoFeeShipperList", infoFeeShipperList);
			map.put("goodsDetailPictureList", goodsDetailPictureList);
			map.put("completePaymentPictureList", completePaymentPictureList);
			map.put("pubGoodsPictureList", pubGoodsPictureList);
		}
		
		return map;
	}

	private void sortHpLoopPictureBean(List<HpLoopPictureBean> list) {
		Collections.sort(list, new Comparator<HpLoopPictureBean>() {
			@Override
			public int compare(HpLoopPictureBean o1, HpLoopPictureBean o2) {
				if (o1.getSort().intValue() == o2.getSort().intValue()) {
					return 0;
				} else if(o1.getSort().intValue() > o2.getSort().intValue()) {
					return 1;
				}else 
					return -1;
			}
		});
	}

	@Override
	public List<HpLoopPicture> getListByType(int type) {
		String hql="from HpLoopPicture where status=? and  openClose=? and type=?  ORDER BY sort ";
		List<HpLoopPicture> list=this.getBaseDao().find(hql,(short)0,(short)1,(short)type);
		if(list!=null &&list.size()>0 ) {
			return list;
		}
		return null;
	}


}
