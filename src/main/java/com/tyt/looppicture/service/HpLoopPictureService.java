package com.tyt.looppicture.service;

import java.util.List;
import java.util.Map;

import com.tyt.base.service.BaseService;
import com.tyt.looppicture.bean.HpLoopPictureBean;
import com.tyt.model.HpLoopPicture;

/**
 * 首页轮播图服务层
 * 
 * <AUTHOR>
 * @date 2017年10月11日上午10:54:17
 * @description
 */
public interface HpLoopPictureService extends BaseService<HpLoopPicture, Long> {
	public Map<String,List<HpLoopPictureBean>> getHpLoopPictureBean(String address);
	
	/**
	 * 根据类型获取轮播图列表
	 * @return
	 */
	public List<HpLoopPicture> getListByType(int type);
}
