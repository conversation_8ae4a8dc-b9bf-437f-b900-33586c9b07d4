package com.tyt.usedCarBrand.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUsedCarBrand;

import java.util.List;

/**
 * @Description  二手车品牌服务层
 * <AUTHOR>
 * @Date  2020/1/21 11:55
 * @Param
 * @return
 **/
public interface TytUsedCarBrandService extends BaseService<TytUsedCarBrand,Long> {

       /**
        * @Description  根据车辆品牌名称模糊搜索车辆品牌列表
        * <AUTHOR>
        * @Date  2020/1/21 12:05
        * @Param [brandName, brandType]
        * @return java.util.List<com.tyt.model.TytUsedCarBrand>
        **/
       public List<TytUsedCarBrand> getUsedCarBrandList(String brandName, Integer brandType);
}
