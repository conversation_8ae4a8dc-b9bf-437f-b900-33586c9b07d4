package com.tyt.usedCarBrand.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUsedCarBrand;
import com.tyt.usedCarBrand.service.TytUsedCarBrandService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description  二手车品牌服务层实现类
 * <AUTHOR>
 * @Date  2020/1/21 11:59
 * @Param
 * @return
 **/
@Service("tytUsedCarBrandService")
public class TytUsedCarBrandServiceImpl extends BaseServiceImpl<TytUsedCarBrand,Long> implements TytUsedCarBrandService{

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    @Resource(name = "tytUsedCarBrandDao")
    public void setBaseDao(BaseDao<TytUsedCarBrand, Long> tytUsedCarBrandDao) {
        super.setBaseDao(tytUsedCarBrandDao);
    }

    /**
     * @Description   根据车辆品牌名称模糊搜索车辆品牌列表
     * <AUTHOR>
     * @Date  2020/1/21 12:06
     * @Param [brandName, brandType]
     * @return java.util.List<com.tyt.model.TytUsedCarBrand>
     **/
    @Override
    public List <TytUsedCarBrand> getUsedCarBrandList(String brandName, Integer brandType) {
        String sql = "select * from tyt_used_car_brand " +
                " where car_brand_status = 1 " +
                " and (car_brand_name like ? or car_brand_spell like ?) " +
                " and car_brand_type = ? " +
                " order by id desc";
        Object[] params = new Object[]{"%"+brandName+"%","%"+brandName+"%",brandType};
        //查询二手车品牌列表
        List<TytUsedCarBrand> usedCarBrandList = this.getBaseDao().queryForList(sql, params);
        return usedCarBrandList;
    }
}
