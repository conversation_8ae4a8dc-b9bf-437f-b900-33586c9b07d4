package com.tyt.promotion.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytPromotionWinner;
import com.tyt.promotion.service.PromotionService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date  2018-03-13
 * @description   活动接口
 */
@Controller
@RequestMapping("/plat/promotion")
public class PromotionController extends BaseController {

	@Resource(name = "promotionService")
	PromotionService promotionService;

	/**
	 * 获取获奖名单列表
	 * @param proId 活动ID
	 * @return
	 */
	@RequestMapping(value = "/winnerList.action")
	@ResponseBody
	public ResultMsgBean winnerList(String proId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			 if(StringUtils.isEmpty(proId)){
				 rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				 rm.setMsg("活动ID不能为空");
				 return rm;
			 }
			TytPromotionWinner tytPromotionWinner = new TytPromotionWinner();
		    tytPromotionWinner.setProId(Integer.valueOf(proId));
		    //获取中奖用户名单列表
			List<TytPromotionWinner> promotionWinners = promotionService.getFilterList(proId);
			if(promotionWinners != null && promotionWinners.size() > 0){
				rm.setData(promotionWinners);
				rm.setTotalSize(Long.valueOf(promotionWinners.size()));
			}else{
				rm.setTotalSize(0l);
			}
			rm.setMsg("查询成功");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
