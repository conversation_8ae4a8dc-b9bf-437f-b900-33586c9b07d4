package com.tyt.promotion.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytPromotionWinner;
import com.tyt.promotion.service.PromotionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service("promotionService")
public class PromotionServiceImpl extends BaseServiceImpl<TytPromotionWinner, Long> implements PromotionService {
	public static Logger logger = LoggerFactory.getLogger(PromotionServiceImpl.class);

	@Resource(name = "promotionDao")
	public void setBaseDao(BaseDao<TytPromotionWinner, Long> promotionDao) {
		super.setBaseDao(promotionDao);
	}

	@Override
	public List<TytPromotionWinner> getFilterList(String proId) {
		String sql = "select * from tyt_promotion_winner t where t.pro_id = ? order by t.pro_time desc,t.sort_id asc ";
		List<TytPromotionWinner> tytPromotionWinners  = this.getBaseDao().queryForList(sql, new Object[]{Integer.valueOf(proId)});
		return tytPromotionWinners;
	}
}