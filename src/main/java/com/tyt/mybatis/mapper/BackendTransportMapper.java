package com.tyt.mybatis.mapper;

import com.tyt.infofee.bean.BackendDealCarBean;
import com.tyt.model.TytTransportBackend;
import com.tyt.transport.querybean.BackendTransportBean;
import org.apache.ibatis.annotations.*;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/12/30 18:23
 */
@Mapper
public interface BackendTransportMapper {
    @Update("update tyt_transport_backend set status=3,order_status=31,mtime=NOW(),receiving_time=NOW()," +
            "src_msg_id=#{transport.msgId},receiver_user_id=#{transport.receiverUserId},receiver_phone=#{transport.receiverPhone}," +
            "receiver_show_name=#{transport.receiverShowName},find_car_type=#{transport.findCarType} where id=#{transport.id}")
    void updateBackendTransportStatus(@Param("transport") BackendTransportBean transport);
    @Update("update tyt_transport_backend_user set status=4 where backend_id=#{id} and batch=(select batch from tyt_transport_backend where id=#{id}) and status=2 ")
    void updateBackendTransportUserStatus(@Param("id") Long id);
    @Select("select id,tel,tel3,tel4 from tyt_transport_backend where src_msg_id=#{msgId}")
    HashMap<String,String> selectBackendPhoneByMsgId(Long msgId);
    @Select("update tyt_transport_backend set src_msg_id=#{msgId},mtime=NOW() where src_msg_id=#{oldMsgId}")
    Long updateMsgIdByMsgId(@Param("msgId") Long msgId,@Param("oldMsgId") Long oldMsgId);
    @Select("<script> " +
            "select src_msg_id from tyt_transport_backend WHERE src_msg_id IN  " +
            "   <foreach item='id' index='index' collection='msgIds' open='(' separator=',' close=')'> " +
            "      #{id} " +
            "   </foreach> " +
            "</script>")
    List<Long> selectMsgIdsByMsgId(@Param("msgIds") Collection<Long> msgIds);

    @Select("select id, src_msg_id srcMsgId, status , order_status orderStatus ,cancel_confirm cancelConfirm from tyt_transport_backend WHERE src_msg_id = #{id} " )
    TytTransportBackend selectStatusByMsgId(Long id);

    @Select("select id, src_msg_id msgId, status,order_status orderStatus,batch,applets_user_id appletsUserId,start_point startPoint,dest_point destPoint,task_content taskContent,order_no as orderNo from tyt_transport_backend where id=#{id}")
    BackendTransportBean selectBackendStatusById(Long id);

    @Select("select id, src_msg_id msgId, status,order_status orderStatus,batch,applets_user_id appletsUserId,start_point startPoint,dest_point destPoint,task_content taskContent,order_no as orderNo from tyt_transport_backend where src_msg_id=#{id}")
    BackendTransportBean selectBackendStatusBySrcId(Long id);

    @Select("select publish_time from tyt_transport_backend_user where backend_id=#{id} and batch=#{batch} and status = 2 limit 1")
    Date selectPublishTime(@Param("id") Long id,@Param("batch") String batch);

    @Select("select count(src_msg_id) from tyt_transport_backend where src_msg_id = #{srcMsgId}")
    int selectIsTimeLimitMsg(@Param("srcMsgId") Long msgId);

    @Select("select id, src_msg_id msgId, tel,tel3,status,order_status orderStatus,batch,applets_user_id appletsUserId,start_point startPoint,dest_point destPoint,task_content taskContent,src_msg_id as msgId,receiver_user_id as receiverUserId,receiver_phone as receiverPhone,receiver_show_name as receiverShowName,order_no as orderNo from tyt_transport_backend where src_msg_id = #{srcMsgId}")
    BackendTransportBean selectBackendStatusByMsgId(@Param("srcMsgId") Long msgId);

    @Insert("insert into tyt_transport_backend_deal_car(ts_id,status,user_id,user_name,tel,car_id,head_city,head_no,tail_city,tail_no,create_time) " +
            " values(#{carBean.tsId},#{carBean.status},#{carBean.userId},#{carBean.userName},#{carBean.tel},#{carBean.carId},#{carBean.headCity},#{carBean.headNo},#{carBean.tailCity},#{carBean.tailNo},NOW())")
    void insertBackendDealCar(@Param("carBean") BackendDealCarBean carBean);

    @Select("select src_msg_id AS srcMsgId,receiver_user_id AS userId from tyt_transport_backend WHERE receiver_user_id=#{userId} AND receiving_time > ADDDATE(CURRENT_TIMESTAMP,INTERVAL -3 DAY) limit 300")
    List<HashMap<String,Long>> selectMsgIdsByUserId(@Param("userId") Long userId);

    @Select("select id,src_msg_id as srcMsgId,start_point as startPoint,dest_point as destPoint,task_content as taskContent,tel,status,order_status as orderStatus," +
            "is_precise_push as isPrecisePush,source,ctime,mtime,price,user_id as userId,start_coord_x as startCoordX,start_coord_y as startCoordY,dest_coord_x as destCoordX,dest_coord_y as destCoordY," +
            "start_longitude as startLongitude,start_latitude as startLatitude,dest_longitude as destLongitude,dest_latitude as destLatitude,start_detail_add as startDetailAdd," +
            "dest_detail_add as destDetailAdd,weight,length,wide,high,remark,tel3,tel4,start_city as startCity,start_provinc as startProvinc,start_area as startArea," +
            "dest_provinc as destProvinc,dest_city as destCity,dest_area as destArea,type,brand,good_type_name as goodTypeName,loading_time as loadingTime,unload_time as unloadTime," +
            "car_min_length as carMinLength,car_max_length as carMaxLength,car_type as carType,car_style as carStyle,work_plane_min_high as workPlaneMinHigh,work_plane_max_high as workPlaneMaxHigh," +
            "work_plane_min_length as workPlaneMinLength,climb,batch,is_valid as isValid,order_no as orderNo,addr_matching_status as addrMatchingStatus " +
            "from tyt_transport_backend where id = #{id}"
    )
    TytTransportBackend selectById(@Param("id") Long id);

    /**
     * 根据货源id修改企业货源状态
     * @param status
     * @param orderStatus
     * @param srcMsgId
     */
    @Update("UPDATE tyt_transport_backend SET `status` = #{status} ,order_status =#{orderStatus},cancel_time=NOW()   WHERE src_msg_id =#{srcMsgId}  ")
    void updateBackendStatusInfo(@Param("status") Integer status,@Param("orderStatus") Integer orderStatus,@Param("srcMsgId") Long srcMsgId);

    @Update("UPDATE tyt_transport_backend SET `status` = 6 ,order_status =60,carriage_time=NOW()   WHERE src_msg_id =#{srcMsgId}  ")
    void updateBackendStatusForDone(@Param("srcMsgId") Long srcMsgId);

}
