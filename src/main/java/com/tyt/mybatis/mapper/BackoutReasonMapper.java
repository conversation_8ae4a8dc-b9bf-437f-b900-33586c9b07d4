package com.tyt.mybatis.mapper;

import com.tyt.model.BackoutReason;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface BackoutReasonMapper {

    @Select("<script> " +
            "select " +
            "   id,\n" +
            "   src_msg_id srcMsgId,\n" +
            "   user_id userId,\n" +
            "   backout_reason_key BackoutReasonKey,\n" +
            "   backout_reason backoutReason,\n" +
            "   status,\n" +
            "   ctime" +
            " from tyt_backout_reason WHERE status = 1 AND src_msg_id IN  " +
            "   <foreach item='id' index='index' collection='msgIds' open='(' separator=',' close=')'> " +
            "      #{id} " +
            "   </foreach> " +
            "</script>")
    List<BackoutReason> selectReasonByMsgId(@Param("msgIds")List<Long> MsgId);
}
