package com.tyt.mybatis.mapper;

import com.tyt.file.bean.FileInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @ClassName FileInfoService
 * @Description 文件信息数据层
 * <AUTHOR>
 * @Date 2019-01-17 14:58
 * @Version 1.0
 */
@Mapper
public interface FileInfoMapper {

    /**
     * @Description  插入文件信息表
     * <AUTHOR>
     * @Date  2019/1/17 18:22
     * @Param [fileInfo]
     * @return int
     **/
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into tyt_file_info (business_id," +
                    "second_business_id," +
                    "file_type," +
                    "original_file_name,"+
                    "file_name," +
                    "file_path," +
                    "file_size,"+
                    "file_suffix,"+
                    "small_file_name," +
                    "small_file_path," +
                    "create_user_id," +
                    "create_user_name," +
                    "ctime," +
                    "update_user_id," +
                    "update_user_name," +
                    "utime," +
                    "status,"+
                     "sort_id,"+
                     
                      "type) values " +
                    "(#{businessId}," +
                     "#{secondBusinessId}," +
                     "#{fileType}," +
                     "#{originalFileName}," +
                     "#{fileName}," +
                     "#{filePath}," +
                     "#{fileSize}," +
                     "#{fileSuffix}," +
                     "#{smallFileName}," +
                     "#{smallFilePath}," +
                     "#{createUserId}," +
                     "#{createUserName}," +
                     "#{ctime}," +
                     "#{updateUserId}," +
                     "#{updateUserName}," +
                     "#{utime}," +
                     "#{status}," +
            "#{sortId}," +

            "#{type})")
    int insertFileInfo(FileInfo fileInfo);


    /**
     * @Description  查询文件信息列表
     * <AUTHOR>
     * @Date  2019/1/18 11:59
     * @Param [businessId, fileType]
     * @return java.util.List<com.tyt.file.bean.FileInfo>
     **/
    @Select("select a.id as id," +
            "a.business_id as businessId," +
            "a.file_type as fileType," +
            "a.original_file_name as originalFileName," +
            "a.file_name as fileName," +
            "a.file_path as filePath," +
            "a.file_size as fileSize," +
            "a.file_suffix as fileSuffix," +
            "a.small_file_name as smallFileName," +
            "a.small_file_path as smallFilePath," +
            "a.create_user_id as createUserId," +
            "a.create_user_name as createUserName," +
            "a.ctime as ctime," +
            "a.update_user_id as updateUserId," +
            "a.update_user_name as updateUserName," +
            "a.utime as utime," +
            "a.status as status, " +
            "a.sort_id as sortId, " +
            "a.type as type, " +
            "a.second_business_id as secondBusinessId " +
            " from tyt_file_info a " +
            " where a.business_id = #{businessId} and a.file_type = #{fileType} and a.status = 1 order by a.sort_id asc,a.id asc ")
    List<FileInfo> getFileInfoList(@Param("businessId") Long businessId, @Param("fileType") Integer fileType);

    /**
     * 查询文件信息列表
     * @param businessId
     * @param secondBusinessId
     * @param fileType
     * @return
     */
    @Select("select a.id as id," +
            "a.business_id as businessId," +
            "a.file_type as fileType," +
            "a.original_file_name as originalFileName," +
            "a.file_name as fileName," +
            "a.file_path as filePath," +
            "a.file_size as fileSize," +
            "a.file_suffix as fileSuffix," +
            "a.small_file_name as smallFileName," +
            "a.small_file_path as smallFilePath," +
            "a.create_user_id as createUserId," +
            "a.create_user_name as createUserName," +
            "a.ctime as ctime," +
            "a.update_user_id as updateUserId," +
            "a.update_user_name as updateUserName," +
            "a.utime as utime," +
            "a.status as status, " +
            "a.sort_id as sortId, " +
            "a.type as type, " +
            "a.second_business_id as secondBusinessId " +
            " from tyt_file_info a " +
            " where a.business_id = #{businessId} " +
            " and a.second_business_id = #{secondBusinessId} " +
            " and a.file_type = #{fileType} " +
            " and a.status = 1 order by a.sort_id asc,a.id asc ")
    List<FileInfo> getFileInfoListByCondition(@Param("businessId") Long businessId,@Param("secondBusinessId") Long secondBusinessId, @Param("fileType") Integer fileType);

    /**
     * @Description  获取单个文件列表的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:04
     * @Param [id]
     * @return com.tyt.file.bean.FileInfo
     **/
    @Select("select a.id as id," +
            "a.business_id as businessId," +
            "a.file_type as fileType," +
            "a.original_file_name as originalFileName," +
            "a.file_name as fileName," +
            "a.file_path as filePath," +
            "a.file_size as fileSize," +
            "a.file_suffix as fileSuffix," +
            "a.small_file_name as smallFileName," +
            "a.small_file_path as smallFilePath," +
            "a.create_user_id as createUserId," +
            "a.create_user_name as createUserName," +
            "a.ctime as ctime," +
            "a.update_user_id as updateUserId," +
            "a.update_user_name as updateUserName," +
            "a.utime as utime," +
            "a.status as status " +
            " from tyt_file_info a " +
            " where a.id = #{id} and  a.status = 1 ")
    FileInfo getSingleFileInfo(@Param("id") Long id);
    
    /**
     * @Description  更新文件状态的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:17
     * @Param [fileInfo]
     * @return int
     **/
    @Update("update tyt_file_info " +
              "set " +
              "update_user_id = #{updateUserId}," +
              "update_user_name = #{updateUserName}," +
              "utime = #{utime}," +
              "status = #{status} " +
              "where id = #{id} ")
    int updateFileInfo(FileInfo fileInfo);

    /**
     * 根据业务ID修改文件状态的方法
     * @param fileInfo
     * @return
     */
    @Update("update tyt_file_info " +
            "set " +
            "update_user_id = #{updateUserId}," +
            "update_user_name = #{updateUserName}," +
            "utime = #{utime}," +
            "status = #{status} " +
            "where business_id = #{businessId} ")
    int updateStatusByBusinessId(FileInfo fileInfo);

    @Delete("delete from tyt_file_info where id = #{id} and file_type = 3")
    void deleteByBusinessId(Long id);

    @Delete(" <script> "+
            " delete from tyt_file_info where id in " +
            " <foreach collection=\"ids\" close=\")\" open=\"(\" item=\"id\" separator=\",\"> " +
            " #{id} " +
            " </foreach> " +
            " and file_type = 3 " +
            " </script>")
    void deleteByBusinessIds(@Param("ids") List<Long> ids);
}
