package com.tyt.mybatis.mapper;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.pricingRoute.bean.PricingRouteConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 发货定价路线配置 mapper
 *
 * <AUTHOR>
 * @since 2024/04/19 19:14
 */
@Mapper
public interface PricingRouteConfigMapper extends CustomBaseMapper<PricingRouteConfig> {

    List<PricingRouteConfig> list(PricingRouteConfig param);

    // 校验是否是优车定价路线
    Integer checkExcellentGoodsPricingRoute(PricingRouteConfig param);

}
