package com.tyt.mybatis.mapper;

import com.tyt.acvitity.bean.CarActivityUserRanking;
import com.tyt.acvitity.bean.ConventionActivityRankingList;
import com.tyt.acvitity.bean.ActivityVo;
import com.tyt.acvitity.bean.ConventionRankingList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface ConventionActivityMapper {
    @Select("SELECT " +
            "order_num" +
            " FROM " +
            "tyt_convention_activity" +
            " WHERE " +
            " activity_id = #{activityId}" +
            " AND user_id = #{userId} " +
            " AND is_deleted = 0 order by create_time desc limit 1")
    Long selectOrderNum(@Param("activityId") Long activityId,@Param("userId") Long userId);


    @Select("SELECT" +
            " ca.activity_id as activityId ," +
            " ca.order_num as orderNum ," +
            " ca.receive_flag as receiveFlag," +
            " ca.target_prize as targetPrize," +
            " ca.prize,IF(tau.id IS NULL,0,1) isHasAddress" +
            " FROM " +
            "tyt_convention_activity ca " +
            "left join tyt_activity_user_address tau on tau.user_id =ca.user_id and tau.activity_id = ca.activity_id  " +
            " WHERE " +
            " ca.activity_id = #{activityId}" +
            " AND ca.user_id = #{userId} " +
            " AND ca.is_deleted = 0 order by ca.create_time desc limit 1")
    ActivityVo selectConventionActivityInfo(@Param("activityId") Long activityId, @Param("userId") Long userId);

    @Select("SELECT" +
            " @ROWNO := @ROWNO + 1 AS `rank`, " +
            " ROUND(T.orderNum) AS orderNum, " +
            " T.cellPhone AS cellPhone,\n" +
            " T.userId AS userId," +
            " T.prize" +
            " FROM " +
            " ( " +
            " SELECT " +
            " IFNULL(ROUND(T1.order_num),0) AS orderNum,\n" +
            " T.user_cell_phone AS cellPhone, " +
            " T.user_id AS userId," +
            " T1.prize " +
            " FROM " +
            " marketing_activity_user T " +
            " LEFT JOIN tyt_convention_activity T1 ON T.user_id = T1.user_id AND T1.activity_id = T.activity_id" +
            " WHERE " +
            " T.is_delete =1 AND T.activity_id =#{activityId} AND T1.order_num >= #{orderNumMin} GROUP BY T.user_id " +
            " ORDER BY " +
            " T1.order_num DESC,T1.last_finish_time asc LIMIT #{maxLimitNum}  " +
            " ) T, " +
            " (SELECT @ROWNO := 0) T3 " +
            "    ORDER BY `rank`")
    /**
     * 查询订单排名信息
     */
    List<ConventionActivityRankingList> selectRankingList(@Param("activityId") Long activityId,@Param("orderNumMin") Integer orderNumMin,@Param("maxLimitNum") Integer maxLimitNum);

    @Select("SELECT" +
            " @ROWNO := @ROWNO + 1 AS `rank`, " +
            " ROUND(T.orderNum) AS orderNum, " +
            " T.cellPhone AS cellPhone,\n" +
            " T.userId AS userId," +
            " T.prize" +
            " FROM " +
            " ( " +
            " SELECT " +
            " IFNULL(ROUND(T1.order_num),0) AS orderNum,\n" +
            " T1.user_cell_phone AS cellPhone, " +
            " T1.user_id AS userId," +
            " T1.prize " +
            " FROM " +
            " tyt_convention_activity T1 " +
            " WHERE " +
            " T1.is_deleted =0 AND T1.activity_id =#{activityId} AND T1.order_num >= #{orderNumMin} GROUP BY T1.user_id " +
            " ORDER BY " +
            " T1.order_num DESC,T1.last_finish_time asc LIMIT #{maxLimitNum}  " +
            " ) T, " +
            " (SELECT @ROWNO := 0) T3 " +
            "    ORDER BY `rank`")
    List<ConventionActivityRankingList> selectRankingListByAllUser(@Param("activityId") Long activityId,@Param("orderNumMin") Integer orderNumMin,@Param("maxLimitNum") Integer maxLimitNum);

    /**
     * @description 更新履约活动中奖信息
     * <AUTHOR>
     * @date 2022/7/15 16:23
     * @param userId
     * @param activityId
     * @param receiveFlag
     * @return java.lang.Integer
     */
    @Update("update tyt_convention_activity set receive_flag = #{receiveFlag} where user_id = #{userId} and activity_id = #{activityId} ")
    Integer updateAwardInfo(@Param("userId") Long userId, @Param("activityId") Long activityId, @Param("receiveFlag") Integer receiveFlag);


    /**
     * @description 更新选择的目标奖品信息
     * <AUTHOR>
     * @date 2022/8/5 10:28
     * @param userId	
     * @param activityId	
     * @param targetPrize	
     * @return java.lang.Integer
     */
    @Update("update tyt_convention_activity set target_prize = #{targetPrize} where user_id = #{userId} and activity_id = #{activityId} ")
    Integer updateTargetPrize(@Param("userId") Long userId, @Param("activityId") Long activityId, @Param("targetPrize") Integer targetPrize);

    @Select("SELECT" +
            " target_prize" +
            " FROM " +
            "tyt_convention_activity" +
            " WHERE " +
            " activity_id = #{activityId}" +
            " AND user_id = #{userId} " +
            " AND is_deleted = 0 order by create_time desc limit 1")
    Integer getTargetByuserId(@Param("activityId")Long id,@Param("userId") Long userId);
    @Select("SELECT" +
            " @ROWNO := @ROWNO + 1 AS `rank`, " +
            " ROUND(T.orderNum) AS orderNum, " +
            " T.name as name," +
            " T.userName AS userName" +
            " FROM " +
            " ( " +
            " SELECT " +
            " IFNULL(ROUND(T1.order_num),0) AS orderNum,\n" +
            " T2.true_name AS name, " +
            " T3.user_name AS userName" +
            " FROM " +
            " tyt_convention_activity T1 " +
            " LEFT JOIN tyt_user_identity_auth T2 ON T1.user_id = T2.user_id" +
            " LEFT JOIN tyt_user T3 ON T1.user_id = T3.id" +
            " WHERE " +
            " T1.is_deleted =0 AND T1.activity_id =#{id} AND T1.order_num > 0 GROUP BY T1.user_id " +
            " ORDER BY " +
            " T1.order_num DESC,T1.last_finish_time asc LIMIT 10  " +
            " ) T, " +
            " (SELECT @ROWNO := 0) T3 " +
            "    ORDER BY `rank`")
    List<ConventionRankingList> selectRankingAllUser(Long id);
    @Select("SELECT" +
            " @ROWNO := @ROWNO + 1 AS `rank`, " +
            " ROUND(T.orderNum) AS orderNum, " +
            " T.name as name," +
            " T.userName AS userName" +
            " FROM " +
            " ( " +
            " SELECT " +
            " IFNULL(ROUND(T1.order_num),0) AS orderNum,\n" +
            " T2.true_name AS name, " +
            " T3.user_name AS userName" +
            " FROM " +
            " marketing_activity_user T " +
            " LEFT JOIN tyt_convention_activity T1 ON T.user_id = T1.user_id AND T1.activity_id = T.activity_id" +
            " LEFT JOIN tyt_user_identity_auth T2 ON T.user_id = T2.user_id" +
            " LEFT JOIN tyt_user T3 ON T.user_id = T3.id" +
            " WHERE " +
            " T.is_delete =1 AND T.activity_id =#{activityId} AND T1.order_num > 0 GROUP BY T.user_id " +
            " ORDER BY " +
            " T1.order_num DESC,T1.last_finish_time asc LIMIT 10  " +
            " ) T, " +
            " (SELECT @ROWNO := 0) T3 " +
            "    ORDER BY `rank`")
    List<ConventionRankingList> selectRanking(Long id);

    /**
     * 通过活动id查询车主透传活动 TOP10排行榜
     * @param activityId 活动ID
     * @return List CarActivityUserRanking
     */
    @Select("select (@rk := @rk+1) as ranking, tab.* \n" +
            " from (select tu.user_name as userName, ca.order_num as orderNum from tyt_convention_activity ca \n" +
            "    left join tyt_user tu on ca.user_id = tu.id \n" +
            " where activity_id = #{activityId} and is_deleted = 0 and ca.order_num >= 15 order by order_num desc, ca.last_finish_time, ca.user_id limit 10) tab, (select @rk:=0) a")
    List<CarActivityUserRanking> selectCarActivityUserRankingTopTen(@Param("activityId")Long activityId);

    /**
     * 通过活动id和userId查询车主透传活动 该用户的排名
     * @param activityId 活动ID
     * @param userid userid
     * @return List CarActivityUserRanking
     */
    @Select("select resulttab.ranking \n" +
            " from (select (@rk := @rk+1) as ranking, tab.user_id \n" +
            " from (select ca.user_id from tyt_convention_activity ca \n" +
            "    left join tyt_user tu on ca.user_id = tu.id \n" +
            " where activity_id = #{activityId} and is_deleted = 0 and ca.order_num >= 15 order by order_num desc, ca.last_finish_time, ca.user_id limit 100) tab, (select @rk:=0) a) resulttab where resulttab.user_id = #{userid} limit 1")
    Long selectUserCarActivityRanking(@Param("activityId")Long activityId, @Param("userid")Long userid);
}
