package com.tyt.mybatis.mapper;

import com.tyt.universalWordConfig.bean.UniversalWordConfigInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface UniversalWordConfigMapper {

    /**
     * 通用文案配置的type字段含义修改为可以保存多个端，例如type值为12，表示车货app都使用
     * @param type
     * @return
     */
    List<UniversalWordConfigInfo> getAllUniversalWordConfigInfoListByType(@Param("type") Integer type);

    List<UniversalWordConfigInfo> getAllUniversalWordConfigInfoListByCodeList(@Param("codes") List<String> codes);
}
