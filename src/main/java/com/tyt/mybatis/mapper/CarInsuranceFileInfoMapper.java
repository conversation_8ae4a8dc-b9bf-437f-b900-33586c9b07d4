package com.tyt.mybatis.mapper;

import com.tyt.model.CarInsuranceFileInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @Description  文件信息数据层
 * <AUTHOR>
 * @Date  2019/3/15 16:13
 * @Param
 * @return
 **/
@Mapper
public interface CarInsuranceFileInfoMapper {

    /**
     * @Description  插入文件信息表
     * <AUTHOR>
     * @Date  2019/1/17 18:22
     * @Param [fileInfo]
     * @return int
     **/
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into car_insurance_file_info (inquiry_id," +
                    "file_type," +
                    "original_file_name,"+
                    "file_name," +
                    "file_path," +
                    "file_size,"+
                    "file_suffix,"+
                    "small_file_name," +
                    "small_file_path," +
                    "create_user_id," +
                    "create_user_name," +
                    "ctime," +
                    "update_user_id," +
                    "update_user_name," +
                    "utime," +
                    "status) values " +
                    "(#{inquiryId}," +
                     "#{fileType}," +
                     "#{originalFileName}," +
                     "#{fileName}," +
                     "#{filePath}," +
                     "#{fileSize}," +
                     "#{fileSuffix}," +
                     "#{smallFileName}," +
                     "#{smallFilePath}," +
                     "#{createUserId}," +
                     "#{createUserName}," +
                     "#{ctime}," +
                     "#{updateUserId}," +
                     "#{updateUserName}," +
                     "#{utime}," +
                     "#{status})")
    int insertFileInfo(CarInsuranceFileInfo fileInfo);


    /**
     * @Description  查询文件信息列表
     * <AUTHOR>
     * @Date  2019/1/18 11:59
     * @Param [inquiryId, fileType]
     * @return java.util.List<com.tyt.file.bean.FileInfo>
     **/
    @Select("select a.id as id," +
            "a.inquiry_id as inquiryId," +
            "a.file_type as fileType," +
            "a.original_file_name as originalFileName," +
            "a.file_name as fileName," +
            "a.file_path as filePath," +
            "a.file_size as fileSize," +
            "a.file_suffix as fileSuffix," +
            "a.small_file_name as smallFileName," +
            "a.small_file_path as smallFilePath," +
            "a.create_user_id as createUserId," +
            "a.create_user_name as createUserName," +
            "a.ctime as ctime," +
            "a.update_user_id as updateUserId," +
            "a.update_user_name as updateUserName," +
            "a.utime as utime," +
            "a.status as status " +
            " from car_insurance_file_info a " +
            " where a.inquiry_id = #{inquiryId} and a.file_type = #{fileType} and a.status = 1 ")
    List<CarInsuranceFileInfo> getFileInfoList(@Param("inquiryId") Long inquiryId, @Param("fileType") Integer fileType);

    /**
     * @Description  根据询价信息Id获取文件列表方法
     * <AUTHOR>
     * @Date  2019/3/15 14:16
     * @Param [inquiryId]
     * @return java.util.List<com.tyt.model.CarInsuranceFileInfo>
     **/
    @Select("select a.id as id," +
            "a.inquiry_id as inquiryId," +
            "a.file_type as fileType," +
            "a.original_file_name as originalFileName," +
            "a.file_name as fileName," +
            "a.file_path as filePath," +
            "a.file_size as fileSize," +
            "a.file_suffix as fileSuffix," +
            "a.small_file_name as smallFileName," +
            "a.small_file_path as smallFilePath," +
            "a.create_user_id as createUserId," +
            "a.create_user_name as createUserName," +
            "a.ctime as ctime," +
            "a.update_user_id as updateUserId," +
            "a.update_user_name as updateUserName," +
            "a.utime as utime," +
            "a.status as status " +
            " from car_insurance_file_info a " +
            " where a.inquiry_id = #{inquiryId} and a.file_type in (1,2,3,4,5,6) and a.status = 1 ")
    List<CarInsuranceFileInfo> getFileInfoListByInquiryId(@Param("inquiryId") Long inquiryId);

    /**
     * @Description  获取单个文件列表的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:04
     * @Param [id]
     * @return com.tyt.file.bean.FileInfo
     **/
    @Select("select a.id as id," +
            "a.inquiry_id as inquiryId," +
            "a.file_type as fileType," +
            "a.original_file_name as originalFileName," +
            "a.file_name as fileName," +
            "a.file_path as filePath," +
            "a.file_size as fileSize," +
            "a.file_suffix as fileSuffix," +
            "a.small_file_name as smallFileName," +
            "a.small_file_path as smallFilePath," +
            "a.create_user_id as createUserId," +
            "a.create_user_name as createUserName," +
            "a.ctime as ctime," +
            "a.update_user_id as updateUserId," +
            "a.update_user_name as updateUserName," +
            "a.utime as utime," +
            "a.status as status " +
            " from car_insurance_file_info a " +
            " where a.id = #{id} and  a.status = 1 ")
    CarInsuranceFileInfo getSingleFileInfo(@Param("id") Long id);
    
    /**
     * @Description  更新文件状态的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:17
     * @Param [fileInfo]
     * @return int
     **/
    @Update("update car_insurance_file_info " +
              "set " +
              "update_user_id = #{updateUserId}," +
              "update_user_name = #{updateUserName}," +
              "utime = #{utime}," +
              "status = #{status} " +
              "where id = #{id} ")
    int updateFileInfo(CarInsuranceFileInfo fileInfo);

    /**
     * @Description  根据业务Id更新文件状态的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:17
     * @Param [fileInfo]
     * @return int
     **/
    @Update("update car_insurance_file_info " +
            "set " +
            "update_user_id = #{updateUserId}," +
            "update_user_name = #{updateUserName}," +
            "utime = #{utime}," +
            "status = #{status} " +
            "where inquiry_id = #{inquiryId} " +
            "and file_type = #{fileType} " +
            "and status = 1")
    int updateStatusByInquiryId(CarInsuranceFileInfo fileInfo);
}
