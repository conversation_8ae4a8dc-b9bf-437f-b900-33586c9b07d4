package com.tyt.mybatis.mapper;

import com.tyt.infofee.bean.ActivityUserAddressBean;
import com.tyt.model.ConventionGiveGoodsRecord;
import com.tyt.acvitity.bean.TransportOrders;
import com.tyt.model.MarketingActivityUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@Mapper
public interface MarketingActivityUserMapper {
    @Select("SELECT " +
            "id," +
            "user_id as userId," +
            "user_grade as userGrade ," +
            "user_cell_phone as userCellPhone," +
            "activity_id as activityId," +
            "is_delete as isDelete,\n" +
            "is_join as isJoin ," +
            "is_send_push as isSendPush," +
            "operater," +
            "ctime," +
            "mtime" +
            " FROM " +
            "marketing_activity_user" +
            " WHERE " +
            "activity_id = #{activityId}" +
            " AND user_id = #{userId}"+
            " AND is_delete=1")
    List<MarketingActivityUser> getByActivityId(@Param("activityId") Long activityId,@Param("userId") Long userId);

    @Select("SELECT " +
            "id," +
            "user_id as userId," +
            "user_grade as userGrade ," +
            "user_cell_phone as userCellPhone," +
            "activity_id as activityId," +
            "is_delete as isDelete,\n" +
            "is_join as isJoin ," +
            "is_send_push as isSendPush," +
            "operater," +
            "ctime," +
            "mtime" +
            " FROM " +
            "marketing_activity_user" +
            " WHERE " +
            "activity_id = #{activityId}" +
            " AND user_id = #{userId}"+
            " AND is_delete=1" +
            " AND is_join = #{isJoin}")
    List<MarketingActivityUser> getEquitiesByUserId(@Param("activityId") Long activityId,@Param("userId") Long userId,@Param("isJoin") Integer isJoin);

    @Select("select" +
            " t.ts_order_no as tsOrderNo, " +
            " t.start_point as startPoint, " +
            " t.dest_point as destPoint, " +
            " t.task_content as taskContent, " +
            " t.pay_user_name as payUserName, " +
            " t.head_city as headCity, " +
            " t.head_no as headNo, " +
            " t.tail_city as tailCity, " +
            " a.activity_status as activityStatus, " +
            " a.appeal_status as appealStatus, " +
            " a.id as stimulateId, " +
            " a.remark as remark, " +
            " a.appeal_evidence as appealEvidence, " +
            " t.tail_no as tailNo" +
            " from tyt_stimulate_activity a " +
            " left join tyt_transport_orders t on a.order_number = t.id " +

            " where a.marketing_activity_id = #{activityId} " +
            " and a.user_id = #{userId} " +
            " and a.activity_status <> 5 " +
            " order by t.mtime DESC ")
    List<TransportOrders> selectTransportOrdersList(@Param("activityId") Long activityId, @Param("userId") Long userId);

    @Select("select" +
            " t.ts_order_no as tsOrderNo, " +
            " t.start_point as startPoint, " +
            " t.dest_point as destPoint, " +
            " t.task_content as taskContent, " +
            " t.pay_user_name as payUserName, " +
            " t.head_city as headCity, " +
            " t.head_no as headNo, " +
            " t.tail_city as tailCity, " +
            " r.activity_status as activityStatus, " +
            " r.appeal_status as appealStatus, " +
            " r.id as stimulateId, " +
            " r.remark as remark, " +
            " r.appeal_evidence as appealEvidence, " +
            " t.tail_no as tailNo" +
            " from tyt_transport_orders_risk r " +
            " left join tyt_transport_orders t on r.order_number = t.id " +

            " where r.activity_status in (0,3) " +
            " and r.pay_end_time >= #{startTime} and r.create_time <= #{endTime} "+
            " and r.user_id = #{userId} " +
            " order by t.mtime DESC ")
    List<TransportOrders> selectTransportOrdersRiskList(@Param("userId") Long userId,@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("select" +
            " t.ts_order_no as tsOrderNo, " +
            " t.start_point as startPoint, " +
            " t.dest_point as destPoint, " +
            " t.task_content as taskContent, " +
            " t.pay_user_name as payUserName, " +
            " t.head_city as headCity, " +
            " t.head_no as headNo, " +
            " t.tail_city as tailCity, " +
            " a.activity_status as activityStatus, " +
            " a.appeal_status as appealStatus, " +
            " a.id as stimulateId, " +
            " a.remark as remark, " +
            " a.appeal_evidence as appealEvidence, " +
            " t.tail_no as tailNo" +
            " from tyt_stimulate_activity a " +
            " left join tyt_transport_orders t on a.order_number = t.id " +
            " where a.marketing_activity_id = #{activityId} " +
            " and a.create_time >= #{startTime} "+
            " and a.create_time <= #{endTime}" +
            " and a.user_id = #{userId} " +
            " and a.activity_status <> 5 " +
            " order by t.mtime DESC ")
    List<TransportOrders> selectTransportOrdersExposureList(@Param("activityId") Long activityId, @Param("userId") Long userId, @Param("startTime")Date startTime,@Param("endTime")Date endTime);

    @Select("select" +
            " t.ts_order_no as tsOrderNo, " +
            " t.start_point as startPoint, " +
            " t.dest_point as destPoint, " +
            " t.task_content as taskContent, " +
            " t.pay_user_name as payUserName, " +
            " t.head_city as headCity, " +
            " t.head_no as headNo, " +
            " t.tail_city as tailCity, " +
            " r.activity_status as activityStatus, " +
            " r.appeal_status as appealStatus, " +
            " r.id as stimulateId, " +
            " r.remark as remark, " +
            " r.appeal_evidence as appealEvidence, " +
            " t.tail_no as tailNo" +
            " from tyt_transport_orders_risk r " +
            " left join tyt_transport_orders t on r.order_number = t.id " +
            " where r.pay_end_time >= #{startTime} "+
            " and r.create_time <= #{endTime}" +
            " and r.user_id = #{userId} " +
            " and r.activity_status in (0,3) " +
            " order by t.mtime DESC ")
    List<TransportOrders> selectTransportOrdersExposureRiskList(@Param("userId") Long userId, @Param("startTime")Date startTime,@Param("endTime")Date endTime);


    @Update("update marketing_activity_user " +
            "set " +
            "is_join = #{isJoin},  " +
            "mtime = now() " +
            "where " +
            "user_id = #{userId} " +
            "and activity_id = #{activityId} ")
    Integer updateUserStatus(@Param("activityId") Long activityId, @Param("userId") Long userId,@Param("isJoin") Integer isJoin);

    @Select("select" +
            " t.ts_order_no as tsOrderNo, " +
            " t.start_point as startPoint, " +
            " t.dest_point as destPoint, " +
            " t.task_content as taskContent, " +
            " t.pay_user_name as payUserName, " +
            " t.head_city as headCity, " +
            " t.head_no as headNo, " +
            " t.tail_city as tailCity, " +
            " a.activity_status as activityStatus, " +
            " a.appeal_status as appealStatus, " +
            " a.id as stimulateId, " +
            " a.remark as remark, " +
            " a.appeal_evidence as appealEvidence, " +
            " t.tail_no as tailNo" +
            " from tyt_transport_orders_risk a " +
            " left join tyt_transport_orders t on a.order_number = t.id " +

            " where a.pay_end_time >= #{startTime}  and a.pay_end_time <= #{endTime}" +
            " and a.create_time >= #{startTime} and a.create_time <= DATE_ADD(#{endTime}, interval 73 hour) " +
            " and a.user_id = #{userId} " +
            " and a.activity_status in (0,3) " +
            " order by t.mtime DESC ")
    List<TransportOrders> selectNewTransportOrdersList(@Param("activityId") Long activityId, @Param("userId")Long userId,@Param("startTime") Date startTime,@Param("endTime") Date endTime);



    @Update("update credit_manage_config_user " +
            "set is_join = 1,mtime = now() " +
            "where user_id = #{userId} and config_id = #{configId} and sub_id = #{subId} ")
    Integer updateUserConfigStatus(@Param("configId") Long configId, @Param("subId") Long subId,@Param("userId") Long userId);

    @Select("select" +
            " t.ts_order_no as tsOrderNo, " +
            " t.start_point as startPoint, " +
            " t.dest_point as destPoint, " +
            " t.task_content as taskContent, " +
            " t.pay_user_name as payUserName, " +
            " t.head_city as headCity, " +
            " t.head_no as headNo, " +
            " t.tail_city as tailCity, " +
            " a.activity_status as activityStatus, " +
            " a.appeal_status as appealStatus, " +
            " a.id as stimulateId, " +
            " a.remark as remark, " +
            " a.appeal_evidence as appealEvidence, " +
            " t.tail_no as tailNo" +
            " from tyt_transport_orders_risk a " +
            " left join tyt_transport_orders t on a.order_number = t.id " +

            " where a.pay_end_time >= #{startTime}  and a.pay_end_time <= #{endTime}" +
            " and a.create_time >= #{startTime} and a.create_time <= DATE_ADD(#{endTime}, interval 73 hour) " +
            " and a.car_user_id = #{userId} " +
            " and a.activity_status in (0,3) " +
            " order by t.mtime DESC ")
    List<TransportOrders> selectNewCarTransportOrdersList(@Param("activityId") Long activityId, @Param("userId")Long userId,@Param("startTime") Date startTime,@Param("endTime") Date endTime);

    @Select("select" +
            " t.ts_order_no as tsOrderNo, " +
            " t.start_point as startPoint, " +
            " t.dest_point as destPoint, " +
            " t.task_content as taskContent, " +
            " t.pay_user_name as payUserName, " +
            " t.head_city as headCity, " +
            " t.head_no as headNo, " +
            " t.tail_city as tailCity, " +
            " a.activity_status as activityStatus, " +
            " a.appeal_status as appealStatus, " +
            " a.id as stimulateId, " +
            " a.remark as remark, " +
            " a.appeal_evidence as appealEvidence, " +
            " t.tail_no as tailNo" +
            " from tyt_transport_orders_risk a " +
            " left join tyt_transport_orders t on a.order_number = t.id " +

            " where a.pay_end_time >= #{startTime}  and a.pay_end_time <= #{endTime}" +
            " and a.create_time >= #{startTime} and a.create_time <= DATE_ADD(#{endTime}, interval 73 hour) " +
            " and a.car_user_id = #{userId} " +
            " and a.activity_status in (0,3) " +
            " and a.excellent_goods = 1 " +
            " order by t.mtime DESC ")
    List<TransportOrders> getNewGoodsOrdersInfo(@Param("userId")Long userId,@Param("startTime") Date startTime,@Param("endTime") Date endTime);
}
