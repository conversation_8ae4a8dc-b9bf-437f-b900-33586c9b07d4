package com.tyt.mybatis.mapper;

import com.tyt.model.UsedCarAuditRecord;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/10 17:43
 */
public interface UsedCarAuditRecordMapper {
    /**
     * 插入二手车审核记录
     * @param record
     * @return
     */
    @Insert("INSERT INTO tyt_used_car_audit_record " +
            "( used_car_sale_id, STATUS, user_name, user_id, reason,remark,ctime) " +
            "VALUES ( #{record.usedCarSaleId,jdbcType=BIGINT}," +
            "#{record.status,jdbcType=CHAR},#{record.userName,jdbcType=VARCHAR}," +
            "#{record.userId,jdbcType=BIGINT},#{record.reason,jdbcType=CHAR}," +
            "#{record.remark,jdbcType=VARCHAR}," +
            "#{record.ctime})")
    Integer insertUsedCarAuditRecord(@Param("record") UsedCarAuditRecord record);

}
