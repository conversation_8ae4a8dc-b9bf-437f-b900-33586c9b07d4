package com.tyt.mybatis.mapper;

import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.model.TytTransportBackend;
import com.tyt.model.TytTransportOrders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DemoMapper {
    @Select("select count(1) from tyt_transport_mt")
    int countMt();

    @Select("select id , cost_status AS costStatus FROM tyt_transport_orders WHERE ts_id = #{SrcMsgId} AND pay_status = #{payStatus}")
    List<TytTransportOrders> selectOrdersById(@Param("SrcMsgId")Long SrcMsgId,@Param("payStatus")String payStatus);

    @Select("select id , pay_status AS payStatus , cost_status AS costStatus FROM tyt_transport_orders WHERE ts_id = #{SrcMsgId}")
    List<TytTransportOrders> selectOrdersByIds(@Param("SrcMsgId")Long SrcMsgId);

    @Select("select id , status,receiver_user_id as receiverUserId,applets_user_id as appletsUserId,src_msg_id as srcMsgId,order_no as orderNo FROM tyt_transport_backend WHERE src_msg_id = #{id}")
    TytTransportBackend selectbackendById(@Param("id")Long id);

    @Select("select id , info_status AS infoStatus FROM tyt_transport_main WHERE src_msg_id = #{SrcMsgId}")
    TransportMain selectmainById(@Param("SrcMsgId")Long SrcMsgId);

    @Select("select id , info_status AS infoStatus FROM tyt_transport_main WHERE src_msg_id = #{SrcMsgId} AND status = 4")
    TransportMain selectmainBySrcId(@Param("SrcMsgId")Long SrcMsgId);
}
