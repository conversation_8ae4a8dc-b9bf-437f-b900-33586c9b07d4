package com.tyt.mybatis.mapper;

import com.tyt.promo.model.PromoUserCoupon;
import java.util.List;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

public interface PromoUserCouponMapper {
    @Select({
        "select",
        "id, user_id, coupon_type_id, coupon_status, coupon_amount, consume_time, channel_name, ",
        "expire_time, mtime, ctime",
        "from promo_user_coupon",
        "where id = #{id,jdbcType=INTEGER}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="user_id", property="userId", jdbcType=JdbcType.BIGINT),
        @Result(column="coupon_type_id", property="couponTypeId", jdbcType=JdbcType.INTEGER),
        @Result(column="coupon_status", property="couponStatus", jdbcType=JdbcType.TINYINT),
        @Result(column="coupon_amount", property="couponAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="consume_time", property="consumeTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="channel_name", property="channelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="expire_time", property="expireTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="mtime", property="mtime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="ctime", property="ctime", jdbcType=JdbcType.TIMESTAMP)
    })
    PromoUserCoupon selectByPrimaryKey(Integer id);

}