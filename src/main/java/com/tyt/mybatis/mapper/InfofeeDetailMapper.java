package com.tyt.mybatis.mapper;

import com.tyt.infofee.bean.*;
import com.tyt.model.TytTransportOrders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InfofeeDetailMapper {


    @Select("select ts.src_msg_id as tsId, ts.user_id as ownerId, user.user_name as ownerName, " +
            "CASE WHEN user.identity_type IS NULL THEN 0 ELSE 1 END as authed, " +
            "user.user_type as userType," +
            "user.serve_days as serveDays," +
            "user.renewal_years as renewalYear," +
            "DATE_FORMAT(user.ctime, \"%Y-%m-%d %H:%i:%s\") as signupTime, " +
            "ts.start_point as startPoint, \n" +
            "ts.dest_point as destPoint, \n" +
            "ts.task_content as taskContent,\n" +
            "ts.shunting_quantity as shuntingQuantity,\n" +
            "ts.weight,\n" +
            "ts.distance,\n" +
            "ts.tel,\n" +
            "ts.tel3,\n" +
            "ts.tel4,\n" +
            "DATE_FORMAT(ts.pub_date, \"%Y-%m-%d %H:%i:%s\") as pubTime, " +
            "ts.price, ts.start_coord_x as startCoordx, ts.start_coord_y as startCoordy, " +
            "ts.dest_coord_x as destCoordx, ts.dest_coord_y as destCoordy," +
            "ts.start_longitude as startLongitude, " +
            "ts.start_latitude as startLatitude, " +
            "ts.dest_longitude as destLongitude, " +
            "ts.dest_latitude as destLatitude, " +
            "ts.wide as width, ts.length, ts.high as height, " +
            "ts.start_detail_add as startDetailAdd, " +
            "ts.dest_detail_add as destDetailAdd, " +
            "ts.remark," +
            "ts.loading_time as loadingTime," +
            "ts.unload_time as unloadTime," +
            "ts.car_min_length as carMinLength," +
            "ts.car_max_length as carMaxLength," +
            "ts.car_type as carType," +
            "ts.car_style as carStyle," +
            "ts.begin_loading_time as beginLoadingTime," +
            "ts.begin_unload_time as beginUnloadTime," +
            "ts.work_plane_min_high as workPlaneMinHigh," +
            "ts.work_plane_max_high as workPlaneMaxHigh," +
            "ts.work_plane_min_length as workPlaneMinLength," +
            "ts.work_plane_max_length as workPlaneMaxLength," +
            "ts.climb," +
            "(select deal_num from tyt_user_sub where user_id = ts.user_id limit 1) as tradeNums, " +
            "ts.tyre_exposed_flag AS tyreExposedFlag," +
            "ts.car_length_labels AS carLengthLabels " +
            "ts.total_score AS totalScore " +
            "ts.rank_level AS rankLevel " +
            "from tyt_transport as ts left join tyt_user as user on ts.user_id = user.id "+
            "where ts.src_msg_id = #{srcMsgId} and status >= 1 limit 1")
    BaseTransInfo queryBaseTransInfo(@Param("srcMsgId") long srcMsgId);

    @Select("select ts.user_id as ownerId, ts.start_point as startPoint, \n" +
            "ts.dest_point as destPoint, \n" +
            "ts.task_content as taskContent,\n" +
            "ts.weight,\n" +
            "ts.distance,\n" +
            "ts.tel,\n" +
            "ts.pub_time as pubTime\n" +
            "from tyt_transport as ts\n" +
            "where ts_order_no = #{tsOrderNo} limit 1")
    BaseTransInfo queryBaseTransInfoFromSource(@Param("tsOrderNo") String tsOrderNo);


    @Select("select a.id as id, " +
            "a.pay_user_id as payUserId, " +
            "a.pay_user_name as payUserName, " +
            "a.pay_cell_phone as payCellPhone, " +
            "a.ts_order_no as tsOrderNo, " +
            "a.pay_amount as payAmount, " +
            "IFNULL(a.total_order_amount,0) as totalOrderAmount, " +
            "IFNULL(a.car_amount,0) carAmount ,"+
            "IFNULL(a.goods_amount,0)  goodsAmount ,"+
            "IFNULL(a.platform_service_amount,0)  platformServiceAmount ,"+
            "IFNULL(a.car_service_amount,0)  carServiceAmount ,"+
            "a.coupon_amount as couponAmount, " +
            "a.head_city as headCity, " +
            "a.head_no as headNo, " +
            "a.tail_city as tailCity, " +
            "a.pay_service_charge as payServiceCharge, " +
            "a.tail_no as tailNo, " +
            "a.car_show as carShow, " +
            "a.goods_show as goodsShow, " +
            "a.car_id as carId, " +
            "a.is_deal_car as isDealCar, " +
            "a.delay_status as delayStatus,"+
            "a.de_payment_dueDate as dePaymentDueDate,"+
            "a.time_limit_identification as timeLimitIdentification, " +
            "DATE_FORMAT(a.pay_end_time, \"%Y-%m-%d %H:%i:%s\") as payEndTime, " +
            "IFNULL(a.refund_amount,0) as refundAmount, " +
            "a.refund_reason as refundReason, " +
            "a.tec_service_fee as tecServiceFee, " +
            "DATE_FORMAT(a.refund_time, \"%Y-%m-%d %H:%i:%s\") as refundTime, " +
            "DATE_FORMAT(a.mtime, \"%Y-%m-%d %H:%i:%s\") as actionTime, " +
            "a.pay_status as oldPaymentStatus,  " +
            "IFNULL(a.cost_status, 0) as costStatus, " +
            "a.carriage_fee as carriageFee,  " +
            "a.delay_refund_status as delayRefundStatus,   " +
            "a.refund_flag  as refundFlag," +
            "a.de_refund_dueDate deRefundDueDate,  " +
            "a.thirdparty_platform_type thirdpartyPlatformType,  " +
            "a.thirdparty_platform_order_no thirdpartyPlatformOrderNo,  " +
            "a.source_type sourceType," +
            "b.refund_amount tecServiceFeeRefundAmount," +
            "b.refund_arrival_time tecServiceFeeRefundTime  " +
            "from tyt_transport_orders a " +
            "left join tyt_transport_technical_order b " +
            "on a.technical_service_no = b.technical_service_no and b.refund_status = 2 " +
            " where a.ts_order_no = #{tsOrderNo} and a.cost_status >= 10")
    List<InfoFeePayinfo> queryPayInfoList(@Param("tsOrderNo") String tsOrderNo);

    /**
     * 返回车方名称
     * @param payUserIds
     * @param userId
     * @return
     */
    @Select("select user.id as userId, user.cell_phone as tel, user.car_user_name as userName, " +
            "CASE WHEN user.identity_type IS NULL THEN 0 ELSE 1 END as authed,  " +
            "user.user_type as userType, " +
            "user.serve_days as serveDays, " +
            "user.id_card as idCard, " +
            "DATE_FORMAT(user.ctime, \"%Y-%m-%d %H:%i:%s\") as signupTime," +
            "(select deal_num from tyt_user_sub where user_id = user.id limit 1) as tradeNums," +
            "(select deal_num from tyt_car_good_deal_num where car_id = user.id and goods_id = #{userId} and status = 1) as coopNums,  " +
            "user.renewal_years as renewalYears from tyt_user as user where user.id in ${payUserIds}")
    List<CreditUserInfo> queryCreditUserInfo(@Param("payUserIds") String payUserIds, @Param("userId") String userId);

    @Select("select user.id as userId, user.cell_phone as tel, user.user_name as userName, " +
            "CASE WHEN user.identity_type IS NULL THEN 0 ELSE 1 END as authed,  " +
            "user.user_type as userType, " +
            "user.serve_days as serveDays, " +
            "DATE_FORMAT(user.ctime, \"%Y-%m-%d %H:%i:%s\") as signupTime," +
            "(select deal_num from tyt_user_sub where user_id = user.id limit 1) as tradeNums," +
            "(select deal_num from tyt_car_good_deal_num where car_id = #{userId} and goods_id = user.id and status = 1) as coopNums,  " +
            "user.renewal_years as renewalYears from tyt_user as user where user.id in (${payUserIds})")
    List<CreditUserInfo> queryCreditUserInfoForCar(@Param("payUserIds") String payUserIds, @Param("userId") String userId);

    @Select("select sr.name as esReason, \n" +
            "ex.ex_other as esOther, \n" +
            "ex.result_opinion as esResult, " +
            "ex.ex_status as exStatus, " +
            "ex.car_amount as carAmount, " +
            "ex.goods_amount as goodsAmount, "  +
            "ex.platform_service_amount as platformServiceAmount, " +
            "ex.car_service_amount as carServiceAmount, "  +
            "(select opinion from tyt_transport_waybill_ex_result where ex_id = ex.id order by ctime desc limit 1) as comment," +
            "DATE_FORMAT(ex.ex_time, \"%Y-%m-%d %H:%i:%s\") as esTime, \n" +
            "DATE_FORMAT(ex.cancel_ex_time, \"%Y-%m-%d %H:%i:%s\") as cancelExTime, \n" +
            "DATE_FORMAT(ex.complete_time, \"%Y-%m-%d %H:%i:%s\") as esCompleteTime \n" +
            "from tyt_transport_waybill_ex as ex \n" +
            "left join (select * from tyt_source where group_code = '${dicKey}' or group_code = '${newDicKey}' ) as sr \n" +
            "on ex.ex_type = sr.value \n" +
            "where ex.order_id = #{infofeeId} and ex.ex_party='${exParty}' and ex.order_type = 0")
    List<ExceptionInfo> queryExceptionInfo(@Param("infofeeId") String infofeeId, @Param("dicKey") String dicKey, @Param("newDicKey") String newDicKey, @Param("exParty") String exParty);


    @Select("select ex_party from tyt_transport_waybill_ex where order_id = #{infofeeId} and order_type = #{orderType} GROUP BY ex_party")
    List<String> queryExParty(@Param("infofeeId") String infofeeId, @Param("orderType") Integer orderType);

    @Select("select ts_id from tyt_transport_orders where ts_order_no = #{tsOrderNo} limit 1")
    Long querySrcMsgId(@Param("tsOrderNo") String tsOrderNo);

    @Select("select ts.src_msg_id as tsId, ts.user_id as ownerId, user.user_name as ownerName, " +
            "CASE WHEN user.identity_type IS NULL THEN 0 ELSE 1 END as authed, " +
            "user.user_type as userType," +
            "user.serve_days as serveDays," +
            "user.renewal_years as renewalYear," +
            "DATE_FORMAT(user.ctime, \"%Y-%m-%d %H:%i:%s\") as signupTime, " +
            "ts.start_point as startPoint, \n" +
            "ts.dest_point as destPoint, \n" +
            "ts.task_content as taskContent,\n" +
            "ts.shunting_quantity as shuntingQuantity,\n" +
            "ts.weight,\n" +
            "ts.distance,\n" +
            "ts.tel,\n" +
            "ts.tel3,\n" +
            "ts.tel4,\n" +
            "DATE_FORMAT(ts.pub_date, \"%Y-%m-%d %H:%i:%s\") as pubTime, " +
            "ts.price, ts.start_coord_x as startCoordx, ts.start_coord_y as startCoordy, " +
            "ts.dest_coord_x as destCoordx, ts.dest_coord_y as destCoordy," +
            "ts.start_longitude as startLongitude, " +
            "ts.start_latitude as startLatitude, " +
            "ts.dest_longitude as destLongitude, " +
            "ts.dest_latitude as destLatitude, " +
            "ts.wide as width, ts.length, ts.high as height, " +
            "ts.start_detail_add as startDetailAdd, " +
            "ts.dest_detail_add as destDetailAdd, " +
            "ts.remark," +
            "ts.loading_time as loadingTime," +
            "ts.unload_time as unloadTime," +
            "ts.begin_loading_time as beginLoadingTime," +
            "ts.begin_unload_time as beginUnloadTime," +
            "ts.car_min_length as carMinLength," +
            "ts.car_max_length as carMaxLength," +
            "ts.car_type as carType," +
            "ts.car_style as carStyle," +
            "ts.work_plane_min_high as workPlaneMinHigh," +
            "ts.work_plane_max_high as workPlaneMaxHigh," +
            "ts.work_plane_min_length as workPlaneMinLength," +
            "ts.work_plane_max_length as workPlaneMaxLength," +
            "ts.climb," +
            "(select deal_num from tyt_user_sub where user_id = ts.user_id limit 1) as tradeNums, " +
            "ts.tyre_exposed_flag AS tyreExposedFlag," +
            "ts.car_length_labels AS carLengthLabels " +
            "ts.total_score AS totalScore " +
            "ts.rank_level AS rankLevel " +
            "from tyt_transport_main as ts left join tyt_user as user on ts.user_id = user.id "+
            "where ts.src_msg_id = #{srcMsgId} and status >= 1 limit 1")
    BaseTransInfo queryBaseTransInfoFromMain(@Param("srcMsgId") long srcMsgId);

    /**
     * 获取信息费交易次数（平台合作次数）
     * @param userId
     * @return
     */
    @Select("select sum(deal_num) as coopNums from tyt_car_good_deal_num where goods_id = #{userId} and status = 1  GROUP BY goods_id ")
    Integer getCoopNums(@Param("userId") long userId);

    /**
     * 获取平台交易量
     * @param userId
     * @return
     */
    @Select("select deal_num from tyt_user_sub where user_id =#{userId}  limit 1")
    Integer getTradeNums(@Param("userId") long userId);


    @Select("select  ts.user_id as ownerId,ts.start_city as startCity,ts.start_area as startArea,ts.start_provinc as startProvinc,  " +
            "ts.dest_provinc as destProvinc,ts.dest_city as destCity,ts.dest_area as destArea,ts.task_content taskContent "+
            "from tyt_transport as ts left join tyt_user as user on ts.user_id = user.id "+
            "where ts.src_msg_id = #{srcMsgId} and status >= 1 limit 1")
    TransportInfoFeeBean queryTransInfo(@Param("srcMsgId")Long srcMsgId);


    @Select("select  ts.user_id as ownerId,ts.start_city as startCity,ts.start_area as startArea,ts.start_provinc as startProvinc,  " +
            "ts.dest_provinc as destProvinc,ts.dest_city as destCity,ts.dest_area as destArea,ts.task_content taskContent "+
            "from tyt_transport_main as ts left join tyt_user as user on ts.user_id = user.id "+
            "where ts.src_msg_id = #{srcMsgId} and status >= 1 limit 1")
    TransportInfoFeeBean queryTransInfoFromMain(@Param("srcMsgId")Long srcMsgId);


    /**
     * 通过 main 表 id 从tyt_transport_order_snapshot表中获取货源信息
     * @param srcMsgId
     * @return
     */
    @Select("select ts.src_msg_id as tsId, ts.user_id as ownerId, ts.nick_name as ownerName, " +
            "CASE WHEN user.identity_type IS NULL THEN 0 ELSE 1 END as authed, " +
            "user.user_type as userType," +
            "user.serve_days as serveDays," +
            "user.renewal_years as renewalYear," +
            "DATE_FORMAT(user.ctime, \"%Y-%m-%d %H:%i:%s\") as signupTime, " +
            "ts.start_point as startPoint, \n" +
            "ts.dest_point as destPoint, \n" +
            "ts.task_content as taskContent,\n" +
            "ts.shunting_quantity as shuntingQuantity,\n" +
            "ts.weight,\n" +
            "ts.distance,\n" +
            "ts.tel,\n" +
            "ts.tel3,\n" +
            "ts.tel4,\n" +
            "DATE_FORMAT(ts.pub_date, \"%Y-%m-%d %H:%i:%s\") as pubTime, " +
            "ts.price, ts.start_coord_x as startCoordx, ts.start_coord_y as startCoordy, " +
            "ts.dest_coord_x as destCoordx, ts.dest_coord_y as destCoordy," +
            "ts.start_longitude as startLongitude, " +
            "ts.start_latitude as startLatitude, " +
            "ts.dest_longitude as destLongitude, " +
            "ts.dest_latitude as destLatitude, " +
            "ts.wide as width, ts.length, ts.high as height, " +
            "ts.start_detail_add as startDetailAdd, " +
            "ts.dest_detail_add as destDetailAdd, " +
            "ts.remark," +
            "ts.loading_time as loadingTime," +
            "ts.unload_time as unloadTime," +
            "ts.car_min_length as carMinLength," +
            "ts.car_max_length as carMaxLength," +
            "ts.car_type as carType," +
            "ts.car_style as carStyle," +
            "ts.begin_loading_time as beginLoadingTime," +
            "ts.begin_unload_time as beginUnloadTime," +
            "ts.work_plane_min_high as workPlaneMinHigh," +
            "ts.work_plane_max_high as workPlaneMaxHigh," +
            "ts.work_plane_min_length as workPlaneMinLength," +
            "ts.work_plane_max_length as workPlaneMaxLength," +
            "ts.climb," +
            "(select deal_num from tyt_user_sub where user_id = ts.user_id limit 1) as tradeNums, " +
            "ts.tyre_exposed_flag AS tyreExposedFlag," +
            "ts.car_length_labels AS carLengthLabels," +
            "ts.publish_type as publishType ," +
            "ts.excellent_goods as excellentGoods ," +
            "ts.info_fee as infoFee, " +
            "ts.tec_service_fee as tecServiceFee, " +
            "ts.shunting_quantity as shuntingQuantity, " +
            "ts.refund_flag as refundFlag, " +
            "ts.auth_name as authName, " +
            "ts.guarantee_goods guaranteeGoods, " +
            "ts.source_type sourceType, " +
            "ts.machine_remark machineRemark, " +
            "tso.cost_status costStatus " +
            "from tyt_transport_order_snapshot as ts left join tyt_user as user on ts.user_id = user.id " +
            "JOIN tyt_transport_orders tso ON tso.id = ts.order_id "+
            "where ts.src_msg_id = #{srcMsgId} and ts.ts_order_no = #{tsOrderNo} and status >= 1 AND tso.cost_status >= 10 order by ts.ctime desc  limit 1")
    BaseTransInfo baseTransInfoForSnapshot(@Param("srcMsgId") Long srcMsgId,@Param("tsOrderNo") String tsOrderNo);

    @Select("select id,ts_id tsId,ts_order_no tsOrderNo from tyt_transport_orders where ts_order_no = #{tsOrderNo} and pay_user_id = #{payUserId} order by id desc limit 1")
    TytTransportOrders queryOrderInfoByPayUserIdOrderNo(@Param("tsOrderNo") String tsOrderNo, @Param("payUserId") Long payUserId);
}
