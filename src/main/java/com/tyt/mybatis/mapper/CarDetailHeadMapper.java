package com.tyt.mybatis.mapper;

import com.tyt.infofee.bean.BaseTransInfo;
import com.tyt.infofee.bean.CreditUserInfo;
import com.tyt.infofee.bean.ExceptionInfo;
import com.tyt.infofee.bean.InfoFeePayinfo;
import com.tyt.model.TytCarDriverArchives;
import com.tyt.user.bean.DispatchCarBean;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CarDetailHeadMapper {
    @Insert("insert into tyt_car_detail_head(car_id,user_id,road_card_positive_url,road_card_other_side_url,road_license_no_url) " +
            "values (#{carId},#{userId},#{roadCardPositiveUrl},#{roadCardOtherSideUrl},#{roadLicenseNoUrl})")
    void insertCarDetailHead(@Param("carId") Long carId,@Param("userId") Long userId, @Param("roadCardPositiveUrl") String roadCardPositiveUrl,
                             @Param("roadCardOtherSideUrl") String roadCardOtherSideUrl,@Param("roadLicenseNoUrl")  String roadLicenseNoUrl);

    /**
     * 修改head表
     * @param dispatchCarBean
     */
    @Update({"<script>",
            "update tyt_car_detail_head",
            "<trim prefix='set' suffixOverrides=','>",
            " <if test='roadCardPositiveUrl != null'> road_card_positive_url = #{roadCardPositiveUrl}, </if>",
            " <if test='roadCardOtherSideUrl != null'> road_card_other_side_url = #{roadCardOtherSideUrl}, </if>",
            " <if test='roadLicenseNoUrl != null'> road_license_no_url = #{roadLicenseNoUrl}, </if>",
            " <if test='roadTransportType != null'> road_transport_type = #{roadTransportType}, </if>",
            "</trim>",
            "WHERE car_id=#{id}",
            "</script>"})
    void updateCarDetailHead(DispatchCarBean dispatchCarBean);

    @Select("select count(id) from tyt_car_detail_head where car_id=#{id}")
    int selectCarDetailHeadNumberByCarId(Long carId);

    @Update("update tyt_car set driver_id=#{driverId},driver_name=null,driver_phone=null,secondary_driver_name=null,secondary_driver_phone=null where id=#{carId}")
    void updateCarDriverId(@Param("carId") Long carId, @Param("driverId") Long driverId);
    @Select({"<script>",
            "select driver.id,driver.driver_show_name userShowName,driver.examine_status examineStatus,driver.driver_phone driverPhone," +
                    "driver.driver_show_name driverShowName,car.id carId  from tyt_car_driver_archives driver left join tyt_car car on driver.id" +
                    " =car.driver_id where car.id in " +
                    "<foreach collection='carIds' item='id' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"})
    List<TytCarDriverArchives> selectTytCarDriverArchivesById(@Param("carIds") List<Long> carIds);

    @Update("update tyt_car set head_phone=#{headPhone},tail_phone=#{tailPhone} where id=#{id}")
    Integer updateCarPhone(@Param("tailPhone")String tailPhone, @Param("headPhone")String headPhone,@Param("id") Long id);

    @Update("update tyt_car set driver_id=#{driverId} where user_id=#{userId} and (driver_phone =#{phone} or secondary_driver_phone =#{phone})")
    Integer updateCarDriverIdByPhone(@Param("phone")String phone, @Param("userId")Long userId,@Param("driverId")Long driverId);
    @Update("update tyt_car set driver_id=null where driver_id=#{driverId}")
    Integer updateCarDriverIdByDriverId(@Param("driverId")Long driverId);
    @Select("<script> " +
            " SELECT distinct car_id FROM tyt_transport_orders WHERE cost_status IN (15,20,21,25,30) AND car_id IN " +
            "  <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'> " +
            " #{id} " +
            " </foreach> "+
            " </script> ")
    List<Long> selectCarIdsByOrderStatus(@Param("ids")List<Long> ids);

    @Select("select count(id) from tyt_car where user_id=#{id} and is_delete=1 and auth=1 and car_degree=3")
    Integer selectCarIsSpecialCarCount(@Param("id") Long userId);
}
