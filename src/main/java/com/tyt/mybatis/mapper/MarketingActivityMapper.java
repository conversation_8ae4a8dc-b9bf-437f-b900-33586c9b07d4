package com.tyt.mybatis.mapper;

import com.tyt.model.MarketingActivity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface MarketingActivityMapper {
    @Select("SELECT\n" +
            "id," +
            "activity_name as activityName," +
            "is_need_popup as isNeedPopup," +
            "popup_id_one as popupIdOne," +
            "popup_id_two as popupIdTwo," +
            "activity_scope as activityScope," +
            "activity_type as activityType," +
            "activity_type_name as activityTypeName," +
            "start_time as startTime," +
            "end_time as endTime," +
            "push_title as pushTitle," +
            "push_summary as pushSummary," +
            "push_content as pushContent," +
            "push_type as pushType," +
            "activity_part as activityPart," +
            "is_need_show as isNeedShow," +
            "activity_content as activityContent," +
            "activity_url as activityUrl," +
            "status," +
            "remark," +
            "operater," +
            "ctime," +
            "mtime" +
            " FROM " +
            "marketing_activity" +
            " WHERE " +
            " activity_type =#{activityType,jdbcType=INTEGER} " +
            "AND `status`=1 " +
            "AND start_time <=NOW() " +
            "AND end_time >= NOW() " +
            "AND activity_part = 2 order by ctime desc limit 1")
    MarketingActivity selectByActivityType(@Param("activityType") Integer activityType);

    @Select("SELECT\n" +
            "id," +
            "activity_name as activityName," +
            "is_need_popup as isNeedPopup," +
            "popup_id_one as popupIdOne," +
            "popup_id_two as popupIdTwo," +
            "activity_scope as activityScope," +
            "activity_type as activityType," +
            "activity_type_name as activityTypeName," +
            "start_time as startTime," +
            "end_time as endTime," +
            "push_title as pushTitle," +
            "push_summary as pushSummary," +
            "push_content as pushContent," +
            "push_type as pushType," +
            "activity_part as activityPart," +
            "is_need_show as isNeedShow," +
            "activity_content as activityContent," +
            "activity_url as activityUrl," +
            "status," +
            "remark," +
            "operater," +
            "ctime," +
            "mtime" +
            " FROM " +
            "marketing_activity" +
            " WHERE " +
            "id = #{activityId,jdbcType=INTEGER} ")
    MarketingActivity selectById(@Param("activityId") Long activityId);
}
