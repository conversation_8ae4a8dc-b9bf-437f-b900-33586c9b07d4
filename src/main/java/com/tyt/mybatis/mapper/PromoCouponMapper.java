package com.tyt.mybatis.mapper;

import com.tyt.promo.model.PromoCoupon;

import java.util.Date;
import java.util.List;

import com.tyt.promo.model.UserCoupon;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

public interface PromoCouponMapper {
    @Select({
        "select",
        "coupon_type_id, coupon_name, coupon_amount, coupon_status, coupon_qty, coupon_desc, ",
        "use_scope_type, use_scope_detail, valid_type, valid_date_begin, valid_date_end, ",
        "valid_days, mtime, ctime",
        "from promo_coupon",
        "where coupon_type_id = #{couponTypeId,jdbcType=INTEGER}"
    })
    @Results({
        @Result(column="coupon_type_id", property="couponTypeId", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="coupon_name", property="couponName", jdbcType=JdbcType.VARCHAR),
        @Result(column="coupon_amount", property="couponAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="coupon_status", property="couponStatus", jdbcType=JdbcType.TINYINT),
        @Result(column="coupon_qty", property="couponQty", jdbcType=JdbcType.INTEGER),
        @Result(column="coupon_desc", property="couponDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="use_scope_type", property="useScopeType", jdbcType=JdbcType.TINYINT),
        @Result(column="use_scope_detail", property="useScopeDetail", jdbcType=JdbcType.VARCHAR),
        @Result(column="valid_type", property="validType", jdbcType=JdbcType.TINYINT),
        @Result(column="valid_date_begin", property="validDateBegin", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="valid_date_end", property="validDateEnd", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="valid_days", property="validDays", jdbcType=JdbcType.INTEGER),
        @Result(column="mtime", property="mtime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="ctime", property="ctime", jdbcType=JdbcType.TIMESTAMP)
    })
    PromoCoupon selectByPrimaryKey(Integer couponTypeId);

    @Select("select \n" +
            "user_coupon.id, \n" +
            "coupon.coupon_type_id as couponTypeID, \n" +
            "coupon.coupon_name as couponName, \n" +
            "coupon.coupon_desc as couponDesc, \n" +
            "coupon.coupon_amount as couponAmount, \n" +
            "coupon.use_scope_type as useScopeType, \n" +
            "coupon.use_scope_detail as useScopeDetail, \n" +
            "coupon.valid_type as validType, \n" +
            "coupon.valid_date_begin as validDateBegin, \n" +
            "coupon.valid_date_end as validDateEnd, \n" +
            "coupon.valid_days as validDays, \n" +
            "coupon.link_target as linkTarget, \n" +
            "user_coupon.coupon_status as couponStatus, \n" +
            "user_coupon.expire_time as expireTime, \n" +
            "user_coupon.ctime as collectTime \n" +
            "from \n" +
            "promo_user_coupon as user_coupon \n" +
            "left join promo_coupon as coupon \n" +
            "on user_coupon.coupon_type_id = coupon.coupon_type_id \n" +
            "where user_coupon.user_id = #{userId} \n"   +
//            "and user_coupon.coupon_status = #{status} \n" +
            "limit #{offset}, #{count}")
    List<UserCoupon> queryCouponListByUserId(@Param("userId") String userId,
                                             @Param("scopeType") int scopeType,
                                              @Param("status") int status,
                                              @Param("offset") int offset,
                                              @Param("count") int count);

    @Update("update promo_coupon set remain_qty = remain_qty - 1 where coupon_type_id = #{couponTypeId}")
    void reduce(@Param("couponTypeId") int couponTypeId);


    @Insert("insert into promo_user_coupon (user_id, coupon_type_id, coupon_status, coupon_amount, expire_time, ctime) \n" +
            "values (#{userId}, #{coupon.couponTypeId}, 2, #{coupon.couponAmount}, #{expireTime} now())")
    void addUserCoupon(@Param("userId") int userId,
                       @Param("expireTime") Date expireTime,
                       @Param("coupon") PromoCoupon coupon);

    @Select("select remain_qty from promo_coupon where coupon_type_id = #{couponTypeId}")
    int queryRemainQty(@Param("couponTypeId") int couponTypeId);

    @Select("select count(1) from promo_user_coupon where user_id = #{userId} and coupon_status = 1")
    int queryValidRemainQtyByUserId(@Param("userId") long userId);

}