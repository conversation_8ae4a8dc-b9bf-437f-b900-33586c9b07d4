package com.tyt.mybatis.mapper;

import com.tyt.acvitity.bean.ActivityPrizeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ActivityPrizeMapper {

    @Select("<script> " +
            "SELECT" +
            " id ," +
            " prize_name as prizeName ," +
            " url ," +
            " pic_url as picUrl," +
            " head_url as headUrl," +
            " prize_url as prizeUrl," +
            " top_url as topUrl," +
            " code " +
            " FROM " +
            "tyt_activity_prize" +
            " WHERE id in" +
            "   <foreach item='id' index='index' collection='prizeIds' open='(' separator=',' close=')'> " +
            "      #{id} " +
            "   </foreach> " +
            " AND status = 1 " +
            "order by create_time desc"+
            "</script>")
    List<ActivityPrizeVo> getByIds(@Param("prizeIds") List<String> prizeIds);

    @Select("select code from tyt_activity_prize where id = #{id}")
    String getCode(long id);
}
