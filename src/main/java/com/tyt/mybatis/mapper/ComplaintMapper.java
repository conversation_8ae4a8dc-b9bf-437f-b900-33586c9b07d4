package com.tyt.mybatis.mapper;

import com.tyt.transport.bean.ComplaintRecordBean;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @date 2021/4/16 16:41
 */
@Mapper
public interface ComplaintMapper {
    @Insert(" insert into tyt_complaint_record(complainant_version,complainant_id,complainants_phone,respondent_version," +
            "respondent_id,respondent_phone,msg_id,reason,picture_url,create_time,is_effective,type_desc)" +
            " values (#{recordBean.complainantVersion},#{recordBean.userId},#{recordBean.complainantsPhone},#{recordBean.respondentVersion}," +
            "#{recordBean.respondentId},#{recordBean.respondentPone},#{recordBean.msgId},#{recordBean.reason},#{recordBean.pictureUrl}," +
            "#{recordBean.createTime},3,#{recordBean.typeDesc})")
    Integer saveComplaint(@Param("recordBean") ComplaintRecordBean recordBean);

    @Select(" select id, reason,picture_url pictureUrl," +
            "create_time  createTime from tyt_complaint_record where msg_id=#{msgId} and complainant_id=#{userId}")
    ComplaintRecordBean selectComplaintRecordByMsgId(@Param("msgId") Long msgId,@Param("userId") Long userId);

    @Select("select frequency from tyt_complaint_transport where msg_id=#{msgId}")
    Integer selectComplaintTransportByMsgId(Long msgId);

    @Select("select frequency from tyt_complaint_user where user_id=#{userId}")
    Integer selectComplaintUserByUserId(Long userId);

    @Insert("insert into tyt_complaint_transport(msg_id,frequency,create_time) values (#{msgId},1,NOW())")
    Integer insertComplaintTransport(Long msgId);

    @Insert("insert into tyt_complaint_user(user_id,frequency,create_time) values (#{userId},1,NOW())")
    Integer insertComplaintUser(Long userId);

    @Update("update tyt_complaint_transport set frequency=frequency+1 where msg_id=#{msgId}")
    Integer updateComplaintTransportByMsgId(Long msgId);

    @Update("update tyt_complaint_user set frequency=frequency+1 where user_id=#{userId}")
    Integer updateComplaintUserByUserId(Long userId);
}
