package com.tyt.mybatis.mapper;

import com.tyt.acvitity.bean.ActivityGradePrizeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ActivityGradePrizeMapper {

    @Select("SELECT" +
            " id ," +
            " activity_id as activityId ," +
            " order_num as num ," +
            " prize ," +
            " choose" +
            " FROM " +
            "tyt_activity_grade_prize" +
            " WHERE " +
            " activity_id = #{activityId}" +
            " AND grade = #{userGrade} " +
            "order by stage asc")
    List<ActivityGradePrizeVo> getByActivityIdAndGrade(@Param("activityId")Long id,@Param("userGrade") Integer userGrade);
}
