package com.tyt.mybatis.mapper;

import com.tyt.model.MqSendMessage;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2020/6/1 14:57
 */
@Mapper
public interface MqSendMessageMapper {
    /**
     * 插入
     * @param mqSendMessage
     * @return
     */
    @Insert("INSERT INTO mq_send_message " +
            "( message_topic,message_tag,message_serial_num,message_content,STATUS,create_time) " +
            "VALUES (#{messageTopic}, #{messageTag}, #{messageSerialNum}, #{messageContent},#{status}, #{createTime})")
    int insertMqSendMessage(MqSendMessage mqSendMessage);

    /**
     * 根据唯一标识查询记录
     * @param messageSerialNum
     * @return
     */
    @Select("select " +
            "id,message_topic,message_tag,message_serial_num,message_content,`status`,create_time " +
            "from mq_send_message " +
            "where message_serial_num=#{messageSerialNum,jdbcType=VARCHAR}")
    MqSendMessage selectByMessageSerialNum(String messageSerialNum);

    /**
     * 根据唯一标识查询记录
     * @param messageSerialNum
     * @return
     */
    @Select("select count(1) from tyt_mq_message where message_serial_num=#{messageSerialNum,jdbcType=VARCHAR}")
    Integer selectCountMqMessage(String messageSerialNum);
}
