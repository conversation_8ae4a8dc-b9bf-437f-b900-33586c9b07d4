package com.tyt.mybatis.mapper;

import com.tyt.infofee.bean.ActivityUserAddressBean;
import com.tyt.model.MarketingActivityUser;
import com.tyt.model.TytActivityUserAddress;
import org.apache.ibatis.annotations.*;

@Mapper
public interface ActivityUserAddressMapper {

    @Select("SELECT\n" +
            "id," +
            "activity_id as activityId," +
            "user_id as userId," +
            "user_name as userName," +
            "user_cell_phone as userCellPhone," +
            "province as province," +
            "city as city," +
            "area as area," +
            "detail_addr as detailAddr," +
            "create_time as createTime," +
            "update_time as updateTime" +
            " FROM tyt_activity_user_address" +
            " WHERE " +
            " user_id =#{userId,jdbcType=INTEGER}" +
            " order by create_time desc limit 1")
    TytActivityUserAddress selectActivityUserAddress(@Param("userId") Long userId);

    @Select("SELECT count(*)  FROM tyt_activity_user_address WHERE user_id =#{userId,jdbcType=INTEGER} AND activity_id = #{activityId,jdbcType=INTEGER}")
    int selectActivityUserAddressCount(@Param("userId") Long userId,@Param("activityId") Long activityId);

    @Select("SELECT count(*) FROM tyt_convention_give_goods_record WHERE user_id =#{userId,jdbcType=INTEGER} AND activity_id = #{activityId,jdbcType=INTEGER}")
    int selectActivityExposureCount(@Param("userId") Long userId,@Param("activityId") Long activityId);

    @Update("update tyt_activity_user_address " +
            "set " +
            "user_name = #{userName}, " +
            "user_cell_phone = #{userCellPhone}, " +
            "province = #{province}, " +
            "city = #{city}," +
            "area = #{area}, " +
            "detail_addr = #{detailAddr},  " +
            "update_time = now()  " +
            "where " +
            "user_id = #{userId} " +
            "and activity_id = #{activityId} ")
    Integer updateActivityUserAddress(ActivityUserAddressBean activityUserAddress);

    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into tyt_activity_user_address (activity_id," +
            "user_id," +
            "user_name," +
            "user_cell_phone," +
            "province," +
            "city," +
            "area," +
            "detail_addr," +
            "create_time) values " +
            "(#{activityId}," +
            "#{userId}," +
            "#{userName}," +
            "#{userCellPhone}," +
            "#{province}, " +
            "#{city}, " +
            "#{area}," +
            "#{detailAddr}, " +
            "now())")
    void addActivityUserAddress(ActivityUserAddressBean activityUserAddress);

    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into marketing_activity_user (user_id," +
            "user_grade," +
            "user_cell_phone," +
            "activity_id," +
            "is_delete," +
            "is_join," +
            "is_send_push," +
            "operater," +
            "ctime,mtime) values " +
            "(#{userId}," +
            "#{userGrade}," +
            "#{userCellPhone}," +
            "#{activityId}," +
            "#{isDelete}, " +
            "#{isJoin}, " +
            "#{isSendPush}," +
            "#{operater}, " +
            "#{ctime},#{mtime})" )
    void insertActivityInfoUser(MarketingActivityUser marketingActivityUser);

    @Select("SELECT sum(use_num) FROM tyt_convention_give_goods_record WHERE user_id =#{userId,jdbcType=INTEGER} AND activity_id = #{activityId,jdbcType=INTEGER}")
    Integer getActivityCountGoods(@Param("userId") Long userId,@Param("activityId") Long activityId);


}
