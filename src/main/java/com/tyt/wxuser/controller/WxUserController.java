package com.tyt.wxuser.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytWxUserInfo;
import com.tyt.wxuser.bean.WxAuthResBean;
import com.tyt.wxuser.service.TytWxUserInfoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/plat/wx/user")
public class WxUserController extends BaseController {

    @Resource(name = "tytWxUserInfoService")
    private TytWxUserInfoService tytWxUserInfoService;

    @PostMapping("/login")
    public ResultMsgBean login(String openId, String unionId) throws Exception {
        WxAuthResBean wxAuthResBean = tytWxUserInfoService.wxLogin(openId, unionId);
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        resultMsgBean.setData(wxAuthResBean);
        return resultMsgBean;
    }

    @PostMapping("/save")
    public ResultMsgBean saveWxUserInfo(TytWxUserInfo tytWxUserInfo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        boolean success = tytWxUserInfoService.updateWxUserInfo(tytWxUserInfo);
        if(!success){
            resultMsgBean.setCode(ResultMsgBean.ERROR);
        }
        return resultMsgBean;
    }

    @PostMapping("/updateWxUserPhone")
    public ResultMsgBean updateWxUserPhone(String openId,String phone) {
        boolean success = tytWxUserInfoService.updateWxUserPhone(openId, phone);
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        if(!success){
            resultMsgBean.setCode(ResultMsgBean.ERROR);
        }
        return resultMsgBean;
    }

}
