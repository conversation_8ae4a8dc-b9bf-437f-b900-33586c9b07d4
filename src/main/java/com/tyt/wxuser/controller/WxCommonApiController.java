package com.tyt.wxuser.controller;

import cn.binarywang.wx.miniapp.api.WxMaJsapiService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaJsapiServiceImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedisBetterConfigImpl;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.redis.JedisWxRedisOps;
import me.chanjar.weixin.common.redis.RedissonWxRedisOps;
import me.chanjar.weixin.common.redis.WxRedisOps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/wx/common/api")
@Slf4j
public class WxCommonApiController {
    //车版公众号 appid
    private static final String CAR_APPID = "wx2c10cb1711720f04";
    //车版公众号appsecret
    private static final String CAR_APPSECRET = "538739bb7abe5362a246dbc31c59d900";
    //货版公众号 appid
    private static final String GOODS_APPID="wx6496fbc7c560dd6d";
    private static final String GOODS_APPSECRET = "1d95cb3a99f53977dc0d88d7546077a7";
    @Autowired
    private TytConfigService tytConfigService;

    /**
     *
     * @param type 1 获取车版ticket 2 获取货版ticket
     * @return
     */
    @RequestMapping(value = {"get/jsapiticket","get/jsapiticket.action"})
    public ResultMsgBean getJsApiTicket(@RequestParam(defaultValue = "1",name = "type") Integer type) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            WxRedisOps wxRedisOps = new RedissonWxRedisOps(RedissonUtils.getClient());
            WxMaDefaultConfigImpl config = new WxMaRedisBetterConfigImpl(wxRedisOps, "wx");
//            WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
            // 使用上面的配置时，需要同时引入jedis-lock的依赖，否则会报类无法找到的异常
            if (type ==1){
                String weixinCarAppid = tytConfigService.getStringValue("weixin_car_appid", CAR_APPID);
                String weixinCarCarAppsecret = tytConfigService.getStringValue("weixin_car_car_appsecret", CAR_APPSECRET);
                config.setAppid(weixinCarAppid);
                config.setSecret(weixinCarCarAppsecret);
            }
            if (type ==2){
                String weixinGoodsAppid = tytConfigService.getStringValue("weixin_goods_appid", GOODS_APPID);
                String weixinGoodsGoodsAppsecret = tytConfigService.getStringValue("weixin_goods_goods_appsecret", GOODS_APPSECRET);
                config.setAppid(weixinGoodsAppid);
                config.setSecret(weixinGoodsGoodsAppsecret);
            }
            config.setToken("1");
            config.setAesKey("1");
            config.setMsgDataFormat("JSON");
            WxMaService service = new WxMaServiceImpl();
            service.setWxMaConfig(config);
            WxMaJsapiService jsapiService = new WxMaJsapiServiceImpl(service);
            String jsapiTicket = jsapiService.getJsapiTicket();
            log.info("jsapiTicket={}", jsapiTicket);
            resultMsgBean.setData(jsapiTicket);
            resultMsgBean.setCode(ResultMsgBean.OK);
            resultMsgBean.setMsg(ResultMsgBean.OK_MSG);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
        }
        resultMsgBean.setCode(ResultMsgBean.ERROR);
        return resultMsgBean;
    }
}
