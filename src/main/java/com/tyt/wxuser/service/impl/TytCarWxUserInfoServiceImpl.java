package com.tyt.wxuser.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.*;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.wxuser.service.TytCarWxUserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.regex.Pattern.compile;
@Slf4j
@Service("tytCarWxUserInfoService")
public class TytCarWxUserInfoServiceImpl extends BaseServiceImpl<TytCarWxUserInfo, Long> implements
		TytCarWxUserInfoService {

	@Resource(name="tytCarWxUserInfoDao")
    public void setBaseDao(BaseDao<TytCarWxUserInfo, Long> tytCarWxUserInfoDao) {
        super.setBaseDao(tytCarWxUserInfoDao);
    }
	@Resource
	private TytUserIdentityAuthService tytUserIdentityAuthService;
	@Resource
	private UserService userService;

	@Override
	public TytCarWxUserInfo getTytCarWxUserInfoByOpenId(String openId) {
		TytCarWxUserInfo tytCarWxUserInfo = new TytCarWxUserInfo();
		tytCarWxUserInfo.setOpenId(openId);
		return this.find(tytCarWxUserInfo);
	}

	@Override
	public TytCarWxUserInfo getTytCarWxUserInfoByUserId(Long userId) {
		TytCarWxUserInfo tytCarWxUserInfo = new TytCarWxUserInfo();
		tytCarWxUserInfo.setUserId(userId);
		return this.find(tytCarWxUserInfo);
	}

	@Override
	public boolean updateBindUser(BaseWxUserInfo tytCarWxUserInfo) {
		StringBuilder sql = new StringBuilder("UPDATE tyt_car_wx_user_info " +
				"SET " +
				" user_id = :userId, " +
				" user_name = :userName, " +
				" cell_phone = :cellPhone " +
				" WHERE" +
				" id = :id ");
		Map<String, Object> params = new HashMap<>();
		params.put("userId", tytCarWxUserInfo.getUserId());
		params.put("userName", tytCarWxUserInfo.getUserName());
		params.put("cellPhone", tytCarWxUserInfo.getCellPhone());
		params.put("id", tytCarWxUserInfo.getId());
		return executeUpdateSql(sql.toString(), params) >= 1;
	}

	@Override
	public boolean updateWxUserPhoneByUserId(String openId,String cellPhone,Long userId) {
		StringBuilder sql = new StringBuilder("UPDATE tyt_car_wx_user_info " +
				"SET " +
				" open_id = :openId,cell_phone = :cellPhone" +
				" WHERE" +
				" user_id = :userId ");
		Map<String, Object> params = new HashMap<>();
		params.put("cellPhone", cellPhone);
		params.put("openId", openId);
		params.put("userId", userId);
		return executeUpdateSql(sql.toString(), params) >= 1;
	}

	@Override
	public TytCarWxUserInfo updateGetTytCarWxUserInfoByUserId(Long userId) {
		String sql = "SELECT * FROM tyt_car_wx_user_info WHERE user_id=?";
		List<TytCarWxUserInfo> list = this.getBaseDao().queryForList(sql, new Object[]{userId});
		if (CollectionUtils.isNotEmpty(list)){
			return list.get(0);
		}
		return null;
	}

}
