package com.tyt.wxuser.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.BaseWxUserInfo;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.TytWxUserInfo;
import com.tyt.model.User;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.StringUtil;
import com.tyt.wxuser.bean.WxAuthResBean;
import com.tyt.wxuser.service.TytWxUserInfoService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.*;

@Service("tytWxUserInfoService")
public class TytWxUserInfoServiceImpl extends BaseServiceImpl<TytWxUserInfo, Long> implements
	TytWxUserInfoService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name="tytWxUserInfoDao")
    public void setBaseDao(BaseDao<TytWxUserInfo, Long> tytWxUserInfoDao) {
        super.setBaseDao(tytWxUserInfoDao);
    }
	@Resource
	private TytUserIdentityAuthService tytUserIdentityAuthService;
	@Resource
	private UserService userService;

	@Override
	public WxAuthResBean wxLogin(String openId, String unionId) throws Exception {
		TytWxUserInfo wxUser = getTytWxUserInfoByOpenId(openId);
		WxAuthResBean result = new WxAuthResBean();
		result.setOpenId(openId);
		if(wxUser != null && wxUser.getUserId() != null){
			result.setUserId(wxUser.getUserId());
			User user = userService.getByUserId(wxUser.getUserId());
			result.setCellPhone(user.getCellPhone());
			TytUserIdentityAuth identityAuth = tytUserIdentityAuthService.getByUserId(wxUser.getUserId().toString());
			if(identityAuth != null){
				result.setIdentityStatus(identityAuth.getIdentityStatus());
			}
		}else if(wxUser == null){
			wxUser = new TytWxUserInfo();
			wxUser.setOpenId(openId);
			wxUser.setUnionId(unionId);
			wxUser.setCreateTime(new Date());
			this.addSave(wxUser);
		}
		result.setUserInfoAuthFlag(org.apache.commons.lang3.StringUtils.isNotBlank(wxUser.getWxAppid()) ? 1 : 0);
		return result;
	}

	@Override
	public TytWxUserInfo getTytWxUserInfoByOpenId(String openId) {
		TytWxUserInfo tytWxUserInfo = new TytWxUserInfo();
		tytWxUserInfo.setOpenId(openId);
		return this.find(tytWxUserInfo);
	}

	@Override
	public TytWxUserInfo getTytWxUserInfoByUserId(Long userId) {
		TytWxUserInfo tytWxUserInfo = new TytWxUserInfo();
		tytWxUserInfo.setUserId(userId);
		return this.find(tytWxUserInfo);
	}

	@Override
	public boolean updateWxUserInfo(TytWxUserInfo tytWxUserInfo) {
		logger.info("微信用户信息:{}",tytWxUserInfo.toString());
		StringBuilder sql = new StringBuilder("UPDATE tyt_wx_user_info " +
			"SET " +
			" union_id = :unionId," +
			" wx_appid = :wxAppid," +
			" wx_nick_name = :wxNickName," +
			" gender = :gender," +
			" city = :city," +
			" province = :province," +
			" country = :country," +
			" avatar_url = :avatarUrl" +
			" WHERE " +
			" open_id = :openid");
		boolean hasEmoji = isHasEmoji(tytWxUserInfo.getWxNickName());
		Map<String, Object> params = new HashMap<>();
		params.put("unionId", tytWxUserInfo.getUnionId());
		params.put("wxAppid", tytWxUserInfo.getWxAppid());
		if(hasEmoji){
			String nickName = tytWxUserInfo.getWxNickName().replaceAll("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]", "");
			params.put("wxNickName", nickName);
		}else {
			params.put("wxNickName", tytWxUserInfo.getWxNickName());
		}
		params.put("gender", tytWxUserInfo.getGender());
		params.put("city", tytWxUserInfo.getCity());
		params.put("province", tytWxUserInfo.getProvince());
		params.put("country", tytWxUserInfo.getCountry());
		params.put("avatarUrl", tytWxUserInfo.getAvatarUrl());
		params.put("openid", tytWxUserInfo.getOpenId());
		return executeUpdateSql(sql.toString(), params) >= 1;
	}

	@Override
	public boolean updateWxUserPhone(String openId,String phone) {
		StringBuilder sql = new StringBuilder("UPDATE tyt_wx_user_info " +
			"SET " +
			" wx_phone = :wxPhone " +
			" WHERE" +
			" open_id = :openid ");
		Map<String, Object> params = new HashMap<>();
		params.put("wxPhone", phone);
		params.put("openid", openId);
		return executeUpdateSql(sql.toString(), params) >= 1;
	}

	@Override
	public boolean updateBindUser(BaseWxUserInfo tytWxUserInfo) {
		StringBuilder sql = new StringBuilder("UPDATE tyt_wx_user_info " +
			"SET " +
			" user_id = :userId, " +
			" user_name = :userName, " +
			" cell_phone = :cellPhone " +
			" WHERE" +
			" id = :id ");
		Map<String, Object> params = new HashMap<>();
		params.put("userId", tytWxUserInfo.getUserId());
		params.put("userName", tytWxUserInfo.getUserName());
		params.put("cellPhone", tytWxUserInfo.getCellPhone());
		params.put("id", tytWxUserInfo.getId());
		return executeUpdateSql(sql.toString(), params) >= 1;
	}
	/**
	 判断字符串是否含有Emoji表情
	 **/
	private boolean isHasEmoji(String reviewerName) {
		if(StringUtils.isBlank(reviewerName)){
			return false;
		}
		Pattern pattern = compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]");
		Matcher matcher = pattern.matcher(reviewerName);
		return matcher.find();
	}

    @Override
    public boolean updateWxUserPhoneByUserId(String openId,String cellPhone,Long userId) {
        StringBuilder sql = new StringBuilder("UPDATE tyt_wx_user_info " +
                "SET " +
                " open_id = :openId,cell_phone = :cellPhone" +
                " WHERE" +
                " user_id = :userId ");
        Map<String, Object> params = new HashMap<>();
        params.put("cellPhone", cellPhone);
        params.put("openId", openId);
        params.put("userId", userId);
        return executeUpdateSql(sql.toString(), params) >= 1;
    }


}
