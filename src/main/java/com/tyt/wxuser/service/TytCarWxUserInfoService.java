package com.tyt.wxuser.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.BaseWxUserInfo;
import com.tyt.model.TytCarWxUserInfo;
import com.tyt.model.TytWxUserInfo;
import com.tyt.wxuser.bean.WxAuthResBean;

public interface TytCarWxUserInfoService extends BaseService<TytCarWxUserInfo,Long> {

    TytCarWxUserInfo getTytCarWxUserInfoByOpenId(String openId);

    TytCarWxUserInfo getTytCarWxUserInfoByUserId(Long userId);

    boolean updateBindUser(BaseWxUserInfo tytCarWxUserInfo);

    boolean updateWxUserPhoneByUserId(String openId, String cellPhone, Long userId);

    TytCarWxUserInfo updateGetTytCarWxUserInfoByUserId(Long userId);
}
