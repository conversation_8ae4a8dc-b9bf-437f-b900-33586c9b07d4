package com.tyt.wxuser.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.BaseWxUserInfo;
import com.tyt.model.Mobile;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytWxUserInfo;
import com.tyt.wxuser.bean.BindPhoneBean;
import com.tyt.wxuser.bean.BindPhoneResBean;
import com.tyt.wxuser.bean.WxAuthResBean;

public interface TytWxUserInfoService extends BaseService<TytWxUserInfo,Long> {
	WxAuthResBean wxLogin(String openId, String unionId) throws Exception;

	TytWxUserInfo getTytWxUserInfoByOpenId(String openId);

	TytWxUserInfo getTytWxUserInfoByUserId(Long openId);

	boolean updateWxUserInfo(TytWxUserInfo tytWxUserInfo);

	boolean updateWxUserPhone(String openId,String phone);

	boolean updateBindUser(BaseWxUserInfo tytWxUserInfo);

    boolean updateWxUserPhoneByUserId(String openId, String cellPhone, Long userId);
}
