package com.tyt.wxuser.dao.impl;


import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCarWxUserInfo;
import com.tyt.model.TytWxUserInfo;
import com.tyt.wxuser.dao.TytCarWxUserInfoDao;
import com.tyt.wxuser.dao.TytWxUserInfoDao;
import org.springframework.stereotype.Repository;

@Repository("tytCarWxUserInfoDao")
public class TytCarWxUserInfoDaoImpl extends BaseDaoImpl<TytCarWxUserInfo, Long> implements TytCarWxUserInfoDao {

	public TytCarWxUserInfoDaoImpl() {
	   this.setEntityClass(TytCarWxUserInfo.class);
	}




}
