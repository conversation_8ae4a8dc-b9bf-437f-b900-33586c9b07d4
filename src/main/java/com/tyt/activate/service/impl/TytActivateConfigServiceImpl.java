package com.tyt.activate.service.impl;

import com.tyt.activate.bean.ActivateCheckBean;
import com.tyt.activate.enums.ActivateDaysStatusEnum;
import com.tyt.activate.service.TytActivateConfigService;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.model.User;
import com.tyt.model.UserPermission;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.TytActivateConfig;
import com.tyt.plat.mapper.base.TytActivateConfigMapper;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.PopupTemplBean;
import com.tyt.user.service.TytUserCallPhoneRecordService;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/08/31 17:18
 */
@Service
public class TytActivateConfigServiceImpl implements TytActivateConfigService {

    private final String ACTIVATE_NO_GIVE_REDIS_KEY = "activate_no_give_";

    @Autowired
    private TytActivateConfigMapper tytActivateConfigMapper;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private UserService userService;

    @Autowired
    private TytUserCallPhoneRecordService tytUserCallPhoneRecordService;

    @Autowired
    private TytNoticePopupTemplService noticePopupTemplService;

    @Override
    public TytActivateConfig getActivateConfig() {
        return tytActivateConfigMapper.getActivateConfig();
    }

    @Override
    public boolean getBlackStatus(Long userId) {
        Integer blackStatus = tytActivateConfigMapper.getBlackStatus(userId);
        return Objects.nonNull(blackStatus) && blackStatus > 0;
    }

    @Override
    public ActivateCheckBean checkActivate(Long userId, int flag, boolean used) throws Exception{
        ActivateCheckBean checkBean = new ActivateCheckBean();
        checkBean.setActivate(false);
        //用户是否在ab测名单
        Integer userType = abtestService.getUserType("car_activate_ab_test", userId);
        if (userType != 1){
            return checkBean;
        }
        //获取配置
        TytActivateConfig config = this.getActivateConfig();
        if (Objects.isNull(config)){
            return checkBean;
        }
        checkBean.setConfig(config);
        if (flag == 2 && config.getShareGiveGoods() <= 0){
            return checkBean;
        }
        //用户是否在黑名单
        boolean blackStatus = this.getBlackStatus(userId);
        if (blackStatus){
            return checkBean;
        }
        //不赠送的天数
        if (RedisUtil.exists(ACTIVATE_NO_GIVE_REDIS_KEY + userId)){
            return checkBean;
        }
        //用户是否有权益
        List<UserPermission> userPermissions = userPermissionService.getUserPermissionByServiceIdNew(userId, 10010);
        if (CollectionUtils.isNotEmpty(userPermissions)){
            for (UserPermission userPermission : userPermissions) {
                if ("100101".equals(userPermission.getServicePermissionTypeId()) && config.getVipStatus() > 0){
                    ActivateDaysStatusEnum status = ActivateDaysStatusEnum.getByCode(config.getVipStatus());
                    if (Objects.isNull(status)){
                        return checkBean;
                    }
                    Date permissionEndTime = TimeUtil.weeHours(userPermission.getEndTime(),0);
                    if ("day".equals(status.getUnit())){
                        Date endTime = TimeUtil.addDay(permissionEndTime, status.getDays());
                        if (endTime.after(new Date())){
                            return checkBean;
                        }
                    }
                    if ("month".equals(status.getUnit())){
                        Date endTime = TimeUtil.dateAddMonth(permissionEndTime, status.getDays());
                        if (endTime.after(new Date())){
                            return checkBean;
                        }
                    }
                }
            }
        }
        User user = userService.getById(userId);
        if (Objects.isNull(user)){
            return checkBean;
        }
        //新注册限制
        if (config.getRegisterStatus() > 0){
            ActivateDaysStatusEnum status = ActivateDaysStatusEnum.getByCode(config.getRegisterStatus());
            if (Objects.isNull(status)){
                return checkBean;
            }
            Date ctime = TimeUtil.weeHours(user.getCtime() == null ? new Date():user.getCtime(),0);
            if ("day".equals(status.getUnit())){
                Date endTime = TimeUtil.addDay(ctime, status.getDays());
                if (endTime.after(new Date())){
                    return checkBean;
                }
            }
            if ("month".equals(status.getUnit())){
                Date endTime = TimeUtil.dateAddMonth(ctime, status.getDays());
                if (endTime.after(new Date())){
                    return checkBean;
                }
            }
        }

        int callCount = 0;
        if (config.getLimitCallDays() > 0 && config.getLimitCallNum() > 0){
            Date startTime = TimeUtil.dateDiff(-config.getLimitCallDays());
            callCount = tytUserCallPhoneRecordService.getByUserIdAndDate(userId, startTime);
            if (used){ //因为拨打电话先扣次数后存拨打记录，所以需要加1
                callCount += 1;
            }
            if (callCount >= config.getLimitCallNum()){
                if (!RedisUtil.exists(ACTIVATE_NO_GIVE_REDIS_KEY + userId)){
                    Date endTime = TimeUtil.weeHours(TimeUtil.addDay(config.getNoGiveDays()), 1);
                    long cacheTime = (endTime.getTime() - new Date().getTime())/1000;
                    RedisUtil.set(ACTIVATE_NO_GIVE_REDIS_KEY + userId, "1", Integer.parseInt(cacheTime+""));
                }
                return checkBean;
            }
            checkBean.setActivate(true);
            if (callCount >= config.getLimitCallNum() - config.getLoginGiveGoodsTimes()){
                PopupTemplBean templBean = noticePopupTemplService.getTemplByType(PopupTypeEnum.车促活弹窗2,null);
                checkBean.setTemplBean(templBean);
                return checkBean;
            }
        }
        PopupTemplBean templBean = noticePopupTemplService.getTemplByType(PopupTypeEnum.车促活弹窗1,null);
        checkBean.setTemplBean(templBean);
        return checkBean;
    }

}
