package com.tyt.activate.service;

import com.tyt.activate.bean.ActivateCheckBean;
import com.tyt.plat.entity.base.TytActivateConfig;

/**
 * <AUTHOR>
 * @since 2024/08/31 17:18
 */
public interface TytActivateConfigService {

    TytActivateConfig getActivateConfig();

    boolean getBlackStatus(Long userId);

    ActivateCheckBean checkActivate(Long userId, int flag, boolean used) throws Exception;
}
