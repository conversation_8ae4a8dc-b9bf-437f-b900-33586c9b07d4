package com.tyt.activate.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/08/28 10:55
 */
@Getter
public enum ActivateDaysStatusEnum {

    //1过期30天内 2过期3个月内 3过期6个月内 4过期12个月内
    EXPIRE_30_DAYS(1,30, "day","过期30天内"),
    EXPIRE_3_MONTHS(2, 3, "month", "过期3个月内"),
    EXPIRE_6_MONTHS(3, 6, "month","过期6个月内"),
    EXPIRE_12_MONTHS(4,12, "month","过期12个月内");

    private Integer code;
    private Integer days;
    private String unit;
    private String desc;

    ActivateDaysStatusEnum(Integer code, Integer days, String unit, String desc) {
        this.code = code;
        this.days = days;
        this.unit = unit;
        this.desc = desc;
    }

    public static ActivateDaysStatusEnum getByCode(Integer code) {
        for (ActivateDaysStatusEnum value : ActivateDaysStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
