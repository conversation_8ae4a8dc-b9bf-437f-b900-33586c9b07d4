package com.tyt.openapi.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytMachineTypeBrandNew;
import com.tyt.model.TytMachineTypeNew;
import com.tyt.plat.service.ts.GoodsPushService;
import com.tyt.transport.querybean.StandardStatusBean;
import com.tyt.transport.service.KeywordMatchesNewService;
import com.tyt.transport.service.MachineTypeBrandNewService;
import com.tyt.transport.service.TytKeywordShortTransformService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Created by duanwc on 2019/12/2.
 */
@Controller
@RequestMapping("/openapi/transport")
public class TransportOpenController extends BaseController {

    @Resource(name = "keywordMatchesNewService")
    private KeywordMatchesNewService keywordMatchesNewService;
    @Resource(name = "tytKeywordShortTransformService")
    private TytKeywordShortTransformService tytKeywordShortTransformService;
    @Resource(name = "machineTypeBrandNewService")
    private MachineTypeBrandNewService machineTypeBrandNewService;

    @Autowired
    private GoodsPushService goodsPushService;

    /**
     * 通过关键字搜索匹配的机器类型
     *
     * @param keyword
     * @param timestamp 时间戳
     * @return
     */
    @RequestMapping(value = "/search/matches")
    @ResponseBody
    public ResultMsgBean searchMatchesNew(@RequestParam(value = "keyword", required = true) String keyword,
                                          @RequestParam(value = "timestamp", required = true) Date timestamp) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type new begin, the keyword is [ " + keyword + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (timestamp == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("时间戳不正确");
            } else if (StringUtils.isBlank(keyword)) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                String userId = "-1000"; // openapi匹配记录ID，无实际业务意义
                List<TytMachineTypeNew> machineTypes = keywordMatchesNewService.queryMachineTypeByKeyword(keyword, userId);
                rm.setData(machineTypes);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        logger.info("query machine type new end, the keyword is [ " + keyword + " ], result is [ " + rm + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    @RequestMapping(value = "/standard/status")
    @ResponseBody
    public ResultMsgBean standardStatus(@RequestParam(value = "keyword", required = true) String keyword,
                                        @RequestParam(value = "timestamp", required = true) Date timestamp) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query standard status begin, the keyword is [ " + keyword + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (timestamp == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("时间戳不正确");
            } else if (StringUtils.isBlank(keyword)) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                StandardStatusBean standardStatusBean = keywordMatchesNewService.queryStandardStatusByKeyword(keyword);
                rm.setData(standardStatusBean);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        logger.info("query standard status end, the keyword is [ " + keyword + " ], result is: " + rm + ", waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 6000版本 通过关键字搜索匹配的机器类型
     *
     * @param keyword
     * @return
     */
    @RequestMapping(value = "/search/matches/group/v6000")
    @ResponseBody
    public ResultMsgBean searchMatchesGroupV6000(String keyword, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type group version 6000 begin, the keyword is [ " + keyword + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isBlank(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                String standardName = tytKeywordShortTransformService.queryKeywordShortByKeyword(keyword);
                List<TytMachineTypeBrandNew> mType = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(standardName, userId);
                if (CollectionUtils.isNotEmpty(mType)) {
                    rm.setData(mType);
                } else {
                    List< TytMachineTypeBrandNew > machineTypes = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(keyword, userId);
                    rm.setData(machineTypes);
                }
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info("query machine type group version 6000 end, the keyword is [ " + keyword + " ], result is [ " + rm + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 6000版本 通过关键字搜索匹配的机器类型
     *
     * @param keyword
     * @return
     */
    @RequestMapping(value = "/search/matches/v6000")
    @ResponseBody
    public ResultMsgBean searchMatchesV6000(String keyword, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type version 6000 begin, the keyword is [ " + keyword + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else if(keyword.length() > 64){
                rm.setCode(ReturnCodeConstant.MORE_THAN_LIMIT);
                rm.setMsg("keyword超出长度限制");
            } else {
                String sName = tytKeywordShortTransformService.queryKeywordShortByKeyword(keyword);
                List<TytMachineTypeBrandNew> mts = machineTypeBrandNewService.queryMachineTypeByKeyword(sName, userId);
                if(mts != null){
                    rm.setData(mts);
                }else {
                    List<TytMachineTypeBrandNew> machineTypes = machineTypeBrandNewService.queryMachineTypeByKeyword(keyword, userId);
                    rm.setData(machineTypes);
                }
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info("query machine type version 6000 end, the keyword is [ " + keyword + " ], result is [ " + rm + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    @PostMapping(value = "/pushGoodsTips")
    @ResponseBody
    public ResultMsgBean pushGoodsTips(Long userId, Long srcMsgId) {

        if(CommonUtil.hasNull(userId, srcMsgId)){
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }

        try {
            goodsPushService.pushGoodsTips(userId, srcMsgId);
        } catch (Exception e) {
            return ResultMsgBean.failResponse(e);
        }

        return ResultMsgBean.successResponse();
    }

}
