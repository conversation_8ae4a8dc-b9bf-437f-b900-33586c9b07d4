package com.tyt.openapi.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.common.bean.WordBookBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytSource;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/openapi/wordbook")
public class WordBookOpenController extends BaseController{
	String[] keys=new String[]{
            "tail_car_type",
            "tail_car_style",
			"road_transport_type"
	};
	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter){
		ResultMsgBean rm = new ResultMsgBean();
		try {	

			 Map<String,List<WordBookBean>> map=new HashMap<String,List<WordBookBean>>();
			for(String key:keys){
				 List<WordBookBean> duty=new ArrayList<WordBookBean>();	
				 List<TytSource> list=TytSourceUtil.getSourceList(key);
				 for( TytSource tytSource:list){
					 WordBookBean wordBookBean=new WordBookBean();
					 BeanUtils.copyProperties(tytSource, wordBookBean);
					 wordBookBean.setDefaults(tytSource.getRemark());
					 duty.add(wordBookBean);
				 }
				 if(list.get(0).getParent().equals("0")){
					 map.put(key, duty);
				 }else{
					 map.put(list.get(0).getParent(), duty);
				 }
				
			}		
			rm.setData(map);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("获得成功");
			
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
}
