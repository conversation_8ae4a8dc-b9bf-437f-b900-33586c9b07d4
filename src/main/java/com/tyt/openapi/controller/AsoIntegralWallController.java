package com.tyt.openapi.controller;

import com.tyt.asoIntegralWall.service.AsoIntegralWallService;
import com.tyt.base.controller.BaseController;
import com.tyt.model.AsoIntegralWallPartner;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/openapi/partner/out")
public class AsoIntegralWallController extends BaseController {

    @Resource(name = "asoIntegralWallService")
    private AsoIntegralWallService asoIntegralWallService;

    /**
     * 排重
     * @param appid
     * @param channel
     * @param idfa
     * @return
     */
    @RequestMapping(value = "/idfa/repeat")
    @ResponseBody
    public ResultMsgBean idfaRepeat(@RequestParam(value = "appid") String appid,
                                 @RequestParam(value = "channel") String channel,
                                 @RequestParam(value = "idfa") String idfa) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("排重成功");
        try {
            int local = asoIntegralWallService.checkIdfa(idfa,appid);
            if (local>0){
                rm.setCode(500);
                rm.setMsg("idfa已存在");
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        return rm;
    }

    /**
     * 上报
     * @param partner 入参
     * @return rm
     */
    @RequestMapping(value = "/task/commit")
    @ResponseBody
    public ResultMsgBean taskCommit(AsoIntegralWallPartner partner) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("上报成功");
        try {
            if (StringUtils.isBlank(partner.getIdfa()) || StringUtils.isBlank(partner.getChannel()) || StringUtils.isBlank(partner.getAppid())){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("必填项为空");
                return rm;
            }
            asoIntegralWallService.saveIdfaFromPanter(partner);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        return rm;
    }

    /**
     * 激活
     * @param appid
     * @param channel
     * @param idfa
     * @return
     */
    @RequestMapping(value = "/task/active")
    @ResponseBody
    public ResultMsgBean taskActive(@RequestParam(value = "appid") String appid,
                                 @RequestParam(value = "channel") String channel,
                                 @RequestParam(value = "idfa") String idfa) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("激活成功");
        try {
            int local = asoIntegralWallService.checkIdfa(idfa,appid);
            if (local<=0){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("激活失败");
                return rm;
            }
            int partner =asoIntegralWallService.updateStatus(idfa,channel,appid);
            if (partner<=0){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("激活失败");
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        return rm;
    }











}
