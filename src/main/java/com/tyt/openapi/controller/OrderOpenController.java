package com.tyt.openapi.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytChannelOrder;
import com.tyt.model.User;
import com.tyt.openapi.bean.RefundOrderResponse;
import com.tyt.openapi.service.OrderOpenService;
import com.tyt.openapi.service.TytChannelOrderService;
import com.tyt.payment.alipay.service.OrderService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * Created by duanwc on 2019/12/2.
 */
@Controller
@RequestMapping("/openapi/order")
public class OrderOpenController extends BaseController {

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "orderOpenService")
    private OrderOpenService orderOpenService;

    @Autowired
    private TytChannelOrderService channelOrderService;

    @Autowired
    private OrderService orderService;


    /**
     * 结算接口
     *
     * @param userId 用户ID
     * @param amount 结算金额
     * @return
     */
    @RequestMapping(value = "/withdraw/save")
    @ResponseBody
    public ResultMsgBean withdrawSave(@RequestParam(value = "userId", required = true) Long userId,
                                      @RequestParam(value = "amount", required = true) BigDecimal amount) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("withdraw save begin, the userId is [ {} ], start time is [ {} ]", userId, startTimeInMilli);
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("受理成功");
        try {
            /* 根据手机号查询用户 */
            User user = userService.getByUserId(userId);
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户不存在");
                return rm;
            }
            orderOpenService.saveWithdraw(userId, amount);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("受理失败");
        }
        logger.info("withdraw save end, the userId is [ {} ], result is [ " + rm + " ] waste time is [ {} ]", userId, rm, System.currentTimeMillis() - startTimeInMilli);
        return rm;
    }

    /**
     * 退款接口
     *
     * @param refundOrderId
     * @param originalOrderId
     * @param amount
     * @param orderAmount
     * @return
     */
    @RequestMapping(value = "/refund/save")
    @ResponseBody
    public ResultMsgBean refundSave(@RequestParam(value = "refundOrderId", required = true) String refundOrderId,
                                    @RequestParam(value = "originalOrderId", required = true) String originalOrderId,
                                    @RequestParam(value = "userId", required = true) Long userId,
                                    @RequestParam(value = "amount", required = true) BigDecimal amount,
                                    @RequestParam(value = "orderAmount", required = true) BigDecimal orderAmount
    ) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("refund save begin, the refundOrderId is [ {} ],originalOrderId is [ {} ], start time is [ {} ]", refundOrderId, originalOrderId, startTimeInMilli);
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("受理成功");
        try {
            /* 根据手机号查询用户 */
            User user = userService.getByUserId(userId);
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户不存在");
                return rm;
            }
            if(orderAmount.compareTo(amount) < 0) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("退款金额不正确");
                return rm;
            }
            TytChannelOrder channelOrder = channelOrderService.getByOrderId(originalOrderId);
            if(channelOrder == null){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("订单不存在");
                return rm;
            }
            // 1. 用户ID不是同一人，2. 订单金额不相等， 3. 订单未支付成功，以上均为订单信息不正确
            if(!userId.equals(channelOrder.getUserId()) || orderAmount.compareTo(channelOrder.getAmount()) != 0 || channelOrder.getStatus() != 2){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("订单信息不正确");
                return rm;
            }
            // 退款
            orderOpenService.saveRefund(refundOrderId, originalOrderId,userId,amount,orderAmount);
            // 设置返回
            RefundOrderResponse refundResponse = new RefundOrderResponse();
            refundResponse.setRefundOrderId(refundOrderId);
            refundResponse.setOriginalOrderId(originalOrderId);
            refundResponse.setAmount(amount);
            refundResponse.setUserId(userId);
            rm.setData(refundResponse);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("受理失败");
        }
        logger.info("refund save end, the refundOrderId is [ {} ], originalOrderId is [ {} ], result is [ " + rm + " ] waste time is [ {} ]", refundOrderId, originalOrderId, rm, System.currentTimeMillis() - startTimeInMilli);
        return rm;
    }

}
