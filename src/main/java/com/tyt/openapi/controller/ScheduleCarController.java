package com.tyt.openapi.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqSyncAuthCarMsg;
import com.tyt.model.Car;
import com.tyt.model.CarLog;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.openapi.bean.OpenCar;
import com.tyt.openapi.bean.QueryAllCarBean;
import com.tyt.openapi.service.ScheduleCarService;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.bean.DispatchCarBean;
import com.tyt.user.service.CarLogService;
import com.tyt.user.service.CarService;
import com.tyt.user.service.UserService;
import com.tyt.util.CarTypeMaxPayload;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;


/**
 * @Description  调度平台车辆管理接口
 * <AUTHOR>
 * @Date  2019/5/31 15:42
 * @Param
 * @return
 **/
@Controller
@RequestMapping("/openapi/car")
public class ScheduleCarController extends BaseController {

    @Autowired
    private ScheduleCarService scheduleCarService;

    @Autowired
    private UserService userService;

    @Resource(name = "carService")
    private CarService carService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "carLogService")
    private CarLogService carLogService;

    /**
     * 获取车辆详情接口
     * @param cellPhone
     * @param headCity
     * @param headNo
     * @param tailCity
     * @param tailNo
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/detail/get", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getCarDetail(String cellPhone,String headCity,String headNo,
                                   String tailCity,String tailNo,
                                   HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        try {
            if(StringUtils.isBlank(cellPhone)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("用户手机号不能为空！");
                return resultMsgBean;
            }
            User user = userService.getUserByCellphone(cellPhone);
            if(user == null){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("手机号对应的用户信息不存在！");
                return resultMsgBean;
            }
            if(StringUtils.isBlank(headCity)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("车头牌照头汉字不能为空！");
                return resultMsgBean;
            }
            if(StringUtils.isBlank(headNo)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("车头牌照号码不能为空！");
                return resultMsgBean;
            }
            if(StringUtils.isBlank(tailCity)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("挂车牌照头汉字不能为空！");
                return resultMsgBean;
            }
            if(StringUtils.isBlank(tailNo)){
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("挂车牌照号码不能为空！");
                return resultMsgBean;
            }
            //查询车辆详情信息
            Car carDetail = scheduleCarService.getCarDetail(user,headCity,headNo,tailCity,tailNo);
            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("查询车辆详情成功！");
            resultMsgBean.setData(carDetail);
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("查询车辆详情发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return resultMsgBean;
    }

    /*@RequestMapping(value = "/savePush", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getCarDetail(String param1,String param2,String cellPhone){
        try {
            return scheduleCarService.savePush(param1, param2, cellPhone);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultMsgBean(500,"服务器错误");
        }
    }*/

    /**
     * 获取手机号下所有认证的车辆信息
     * @param cellPhone
     * @return
     */
    @RequestMapping(value = "/allCarDetail/get", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getAllCarDetail(String cellPhone, Date updateTime){

        ResultMsgBean allCarDetailBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        try {
            User user = userService.getUserByCellphone(cellPhone);
            //验证手机号是否有效是否存在该用户
            getUserInfo(user, allCarDetailBean, cellPhone);
            // 取得该用户手机号下全部认证车辆列表
            List<QueryAllCarBean> queryAllCarBeanList =  scheduleCarService.queryAllCarBean(cellPhone, updateTime,null,null);

            allCarDetailBean.setCode(ReturnCodeConstant.OK);
            allCarDetailBean.setMsg(allCarDetailBean.getMsg());
            allCarDetailBean.setData(queryAllCarBeanList);

        }catch (Exception e) {
            allCarDetailBean.setCode(ResultMsgBean.ERROR);
            allCarDetailBean.setMsg("服务器错误！");
            logger.info("查询认证车辆详情发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return allCarDetailBean;
    }
    private ResultMsgBean getUserInfo(User user,ResultMsgBean resultMsgBean,String cellPhone){
        if(user == null){
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("手机号对应的用户信息不存在！");
            return resultMsgBean;
        }
        if(StringUtils.isBlank(cellPhone)){
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("用户手机号不能为空！");
            return resultMsgBean;
        }
        return resultMsgBean;
    }

    @RequestMapping(value = "/specialCar/get", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getSpecialCar(String cellPhone, Integer page){

        try {
            List<QueryAllCarBean> queryAllCarBeans = scheduleCarService.queryAllCarBean(cellPhone, null, page,"1");
            Map<String ,Object> map=new HashMap<>();
            map.put("list",queryAllCarBeans);
            map.put("page",page);
            return new ResultMsgBean(ResultMsgBean.OK,"查询成功",map);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultMsgBean(ResultMsgBean.ERROR,"服务器异常");
        }

    }



    /**
     * 车辆认证，此方法仅适用于 添加调度车辆
     * 规则：
     *     1. 道路运输证件类型默认为：照片
     *     2. 车辆库不存在的数据，新增；
     *     3. 车辆状态 0-认证中、1-认证成功的数据，直接返回，不做新增和修改
     *     4. 车辆状态 2-认证失败、3-系统删除、4-待认证数据，做更新操作
     *
     * @param request
     * @param response
     * @param headDrivingPic
     * @param tailDrivingPic
     */
    @RequestMapping(value = "/identity/save")
    @ResponseBody
    public void identitySave(HttpServletRequest request, HttpServletResponse response, @RequestParam MultipartFile headDrivingPic, @RequestParam(required = false) MultipartFile tailDrivingPic,
                             @RequestParam(required = false) MultipartFile tailDrivingOtherSidePic, @RequestParam(required = false) MultipartFile roadCardPositivePic,
                             @RequestParam(required = false) MultipartFile roadCardOtherSidePic , @RequestParam(required = false) MultipartFile roadLicenseNoPic) {
        String condition = null;
        try {
            logger.info("调度系统数据处理开始");
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            String cellPhone = params.get("cellPhone");
            Long userId = null;
            User user = null;

            if(StringUtils.isBlank(cellPhone)) { // 如果未传cellPhone，必传userId
                userId = Long.parseLong(params.get("userId"));
                user = userService.getByUserId(userId);
                if (user == null) {
                    backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "该用户不存在", null, 0);
                    return;
                }
            } else {
                user = userService.getUserByCellphone(cellPhone);
                if (user == null) {
                    backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "该用户不存在", null, 0);
                    return;
                }
                userId = user.getId();
                params.put("userId", userId.toString());
            }


            if (user.getIsDispatch()!=null){
                params.put("isDispatch",user.getIsDispatch().toString());
            }
            condition = "userId_" + userId + "车辆认证 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("headCity");
                    add("headNo");
                    add("tailCity");
                    add("tailNo");
                    add("clientSign");
                }
            };
            if (!validateArguments(condition, request, response, params, names)){
                return;
            }
            //开票参数验证
            if(!carParametersVerification(params,roadCardPositivePic)){
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "参数有误", null, 0);
                return;
            }
            /* 判断照片是否上传？ */
            if (headDrivingPic == null || headDrivingPic.isEmpty()) {
                logger.error(condition + "图片为空");
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "车头证件照不能为空", null, 0);
                return;
            }
            if (tailDrivingPic == null || tailDrivingPic.isEmpty()) {
                logger.error(condition + "图片为空");
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "挂车证件照不能为空", null, 0);
                return;
            }

            if(!super.isAllImg(headDrivingPic, tailDrivingPic, tailDrivingOtherSidePic, roadCardPositivePic, roadCardOtherSidePic, roadLicenseNoPic)){
                logger.error(condition + ":图片格式错误！");
                backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "图片格式错误！", null, 0);
                return;
            }

            /* 验证重复信息？ */
            Car dbcar = carService.getCarByParams(params);
            OpenCar openCar = new OpenCar();
            if (dbcar != null && ("0".equals(dbcar.getAuth()) || "1".equals(dbcar.getAuth()))) {
                logger.error(condition + "车辆已存在，请勿重复添加");
                BeanUtils.copyProperties(dbcar, openCar);
                backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "车辆已存在，请勿重复添加", openCar, 0);
                return;
            } else if(dbcar != null && ("2".equals(dbcar.getAuth()) || "3".equals(dbcar.getAuth()) || "4".equals(dbcar.getAuth()))) {
                // ============  此处为更新单个车辆信息 ==============
                condition = "userId_" + userId + "车辆认证更新 ";
                params.put("roadTransportType", "1"); // 道路运输证类型， 1照片 2卡片
                params.put("id", dbcar.getId().toString()); // 道路运输证类型， 1照片 2卡片
                if(!carParametersVerification(params,roadCardPositivePic,roadLicenseNoPic,roadCardOtherSidePic)){ // 更新时多出来的验证
                    backResponse(request, response, ReturnCodeConstant.IMAGE_ERROR_CODE, "参数有误", null, 0);
                    return;
                }
                // 车辆更新
                Car car = updateCar(params, headDrivingPic, tailDrivingPic,tailDrivingOtherSidePic);
                CarSaveBean carSaveBean = createCarSaveBran(params);
                if("1".equals(params.get("isDispatch"))){
                    DispatchCarBean dispatchCarBean = createDispatchCarBean(params, roadCardPositivePic, roadCardOtherSidePic, roadLicenseNoPic,car);
                    carService.updateCarHead(dispatchCarBean,car);
                }
                if (car == null) {
                    logger.info(condition + "更新失败");
                    backResponse(request, response, ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "没有该车", null, 0);
                    return;
                }
                if (headDrivingPic != null && !headDrivingPic.isEmpty()) {
                    headDrivingPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getHeadDrivingUrl()));
                }
                if (tailDrivingPic != null && !tailDrivingPic.isEmpty()){
                    tailDrivingPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + car.getTailDrivingUrl()));
                }
                carService.update(car);
                syncAuthCarMsg(car.getId());

                //2019-10-23 xyy 更改挂车样式
                if (StringUtils.isNotBlank(params.get("isPureFlat"))){
                    carService.saveTailDetail(car.getId(),car.getUserId(),car.getTailCity(),car.getTailNo(),Integer.parseInt(params.get("isPureFlat")),carSaveBean);
                }

                //更改user表中is_car状态
                int carAuth = carService.isCarAuth(user.getId(), "1");
                if (carAuth>0 && !"1".equals(user.getIsCar())) {
                    user.setIsCar("1");
                    user.setMtime(new Timestamp(System.currentTimeMillis()));
                    userService.update(user);
                    cacheService.del(Constant.CACHE_USER_KEY + user.getId());
                    cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
                }
                if (carAuth<=0 && "1".equals(user.getIsCar())) {
                    user.setIsCar("0");
                    user.setMtime(new Timestamp(System.currentTimeMillis()));
                    userService.update(user);
                    cacheService.del(Constant.CACHE_USER_KEY + user.getId());
                    cacheService.setObject(Constant.CACHE_USER_KEY + userId, user, AppConfig.getIntProperty("tyt.cache.user.time"));
                }
                // 设置返回的车辆信息
                openCar.setId(dbcar.getId());
                openCar.setCellPhone(cellPhone);
                openCar.setUserId(userId);
                openCar.setHeadCity(params.get("headCity"));
                openCar.setHeadNo(params.get("headNo"));
                openCar.setTailCity(params.get("tailCity"));
                openCar.setTailNo(params.get("tailNo"));
                openCar.setAuth("0"); // 新增车辆为认证中
                backResponse(request, response, ReturnCodeConstant.OK, "车辆信息更新成功", openCar, 0);
                logger.info(condition + "更新成功");
                return;
            }

            /* 创建Car */
            String headDrivingUrl = renamePic(headDrivingPic, "car");
            String tailDrivingUrl = null;
            if (!tailDrivingPic.isEmpty()){
                tailDrivingUrl = renamePic(tailDrivingPic, "car");
            }

            headDrivingPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + headDrivingUrl));
            if (tailDrivingUrl!=null){
                tailDrivingPic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + tailDrivingUrl));
            }
            //开票新增参数
            //临时形式车牌号反面
            String tailDrivingOtherSideUrl=getPicUrlByPic(tailDrivingOtherSidePic);
            //道路运输证正面
            String roadCardPositiveUrl=getPicUrlByPic(roadCardPositivePic);
            //道路运输证反面
            String roadCardOtherSideUrl=getPicUrlByPic(roadCardOtherSidePic);
            //道路运输经营许可证
            String roadLicenseNoUrl=getPicUrlByPic(roadLicenseNoPic);

            CarSaveBean car = createCarSaveBran(params);
            Long id = carService.saveCar(car,headDrivingUrl,tailDrivingUrl,tailDrivingOtherSideUrl,roadCardPositiveUrl,roadCardOtherSideUrl,roadLicenseNoUrl,
                    null,null,null,null,null,null,null,null);
            // 设置返回参数
            openCar.setId(id);
            openCar.setCellPhone(cellPhone);
            openCar.setUserId(userId);
            openCar.setHeadCity(params.get("headCity"));
            openCar.setHeadNo(params.get("headNo"));
            openCar.setTailCity(params.get("tailCity"));
            openCar.setTailNo(params.get("tailNo"));
            openCar.setAuth("0"); // 新增车辆为认证中
            backResponse(request, response, ReturnCodeConstant.OK, "车辆信息添加成功", openCar, 0);
            logger.info(condition + "车辆信息添加成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        } finally {
            logger.info("调度系统数据处理结束:"+condition);
        }
    }

    private String getPicUrlByPic(MultipartFile pic) throws IOException {
        String url=null;
        if (pic!=null&&!pic.isEmpty()){
            url=renamePic(pic, "car");
            pic.transferTo(new File(AppConfig.getProperty("picture.path.domain") + url));
        }
        return url;
    }

    /**
     * 创建Car
     *
     * @param params·
     * @return
     * @throws Exception
     */
    private CarSaveBean createCarSaveBran(Map<String, String> params) throws Exception {
        CarSaveBean car = new CarSaveBean();
        car.setUserId(Long.parseLong(params.get("userId")));
        car.setHeadCity(params.get("headCity"));
        car.setHeadNo(params.get("headNo"));
        car.setTailCity(params.get("tailCity"));
        car.setTailNo(params.get("tailNo"));
        car.setClientSign(params.get("clientSign"));
        //2019-10-23 xyy 增加挂车类型
        if (StringUtils.isNotEmpty(params.get("carType"))){
            car.setCarType(Integer.parseInt(params.get("carType")));
            car.setIsPureFlat(Integer.parseInt(params.get("isPureFlat")));
            if ("0".equals(params.get("carType"))&&params.get("otherCarType")==null){
                car.setOtherCarType("其他");
            }
            if ("0".equals(params.get("isPureFlat"))&&params.get("otherPureFlat")==null){
                car.setOtherPureFlat("其他");
            }
        }
        if (params.get("hasLadder")==null){
            car.setHasLadder(1);
        }
        //开票新增
        if ("1".equals(params.get("isDispatch"))){
            car.setIsDispatch("1");
            car.setLoadSurfaceLength(params.get("loadSurfaceLength"));
            car.setLoadSurfaceHeight(params.get("loadSurfaceHeight"));
            car.setMaxPayload(params.get("maxPayload"));
            car.setRoadTransportType(params.get("roadTransportType"));
        }else {
            car.setIsDispatch("0");
        }
        // 设置最大载重
        car.setMaxPayload(CarTypeMaxPayload.getMaxPayload(params.get("carType"), params.get("maxPayload")));
        car.setAuth("0");
        return car;
    }


    /**
     * 开票相关新增车辆参数校验
     * @param params
     * @param roadCardPositivePic
     * @return
     */
    private boolean carParametersVerification(Map<String, String> params,MultipartFile roadCardPositivePic){
        if (StringUtils.isNotEmpty(params.get("isDispatch"))&&"1".equals(params.get("isDispatch"))){
            if (roadCardPositivePic == null || roadCardPositivePic.isEmpty()) {
                return false;
            }

            List<String> names = new ArrayList<String>() {
                {
                    //工作面长
                    add("loadSurfaceLength");
                    //工作面高
                    add("loadSurfaceHeight");
                    //最大载重
                    add("maxPayload");
                }
            };
            for (String name : names) {
                if(StringUtils.isEmpty(params.get(name))){
                    logger.info("----------------车辆新增时有误参数："+name);
                    return false;
                }
            }

        }
        return true;
    }

    /**
     * 开票相关修改车辆参数校验
     * @param params
     * @param roadCardPositivePic
     * @param roadLicenseNoPic
     * @return
     */
    private boolean carParametersVerification(Map<String, String> params,MultipartFile roadCardPositivePic,MultipartFile roadLicenseNoPic,MultipartFile roadCardOtherSidePic){
        if (StringUtils.isNotEmpty(params.get("isDispatch"))&&"1".equals(params.get("isDispatch"))){
            if ((roadCardPositivePic==null||roadCardPositivePic.isEmpty())&&(roadCardOtherSidePic==null||roadCardOtherSidePic.isEmpty())){
                 return false;
            }
            if (StringUtils.isBlank(params.get("roadTransportType"))){
                logger.info("roadTransportType:{}-------------",params.get("roadTransportType"));
                return false;
            }
            return true;
        }else {
            return true;
        }

    }

    /**
     * 更新car
     *
     * @param params
     * @param headDrivingPic
     * @param tailDrivingPic
     * @return
     * @throws Exception
     */
    private Car updateCar(Map<String, String> params, MultipartFile headDrivingPic, MultipartFile tailDrivingPic,
                          MultipartFile tailDrivingOtherSidePic) throws Exception {
        Car car = carService.getById(Long.parseLong(params.get("id")));
        if (car == null) {
            return null;
        }
        // 认证成功的车辆重新审核时保存修改記錄
        if ("1".equals(car.getAuth())) {
            CarLog carLog = new CarLog();
            Car carNew = new Car();
            org.apache.commons.beanutils.BeanUtils.copyProperties(carNew, car);
            carNew.setId(null);
            org.apache.commons.beanutils.BeanUtils.copyProperties(carLog, carNew);
            carLog.setRecordTime(new Date());
            carLog.setCarId(car.getId());
            carLog.setUpdateType("3");
            carLog.setUpdateReason(params.get("updateReason"));
            carLog.setIsDisplay(1);
            carLogService.add(carLog);
        }
        car.setUserId(Long.parseLong(params.get("userId")));
        car.setHeadCity(params.get("headCity"));
        car.setHeadNo(params.get("headNo"));
        car.setTailCity(params.get("tailCity"));
        car.setTailNo(params.get("tailNo"));
        if(StringUtils.isNotBlank(params.get("carType"))){
            car.setCarType(Integer.parseInt(params.get("carType")));
        }
        // 如果认证成功的重新上传则设置AUth 为3 为后台 提示标识
        if ("1".equals(car.getAuth())) {
            car.setAuth("3");
        } else{
            car.setAuth("0");
        }

        car.setTailAuthStatus("0");
        car.setTailFailReason("");
        car.setHeadAuthStatus("0");
        car.setHeadFailReason("");
        car.setFindGoodOnOff("0");
        car.setUpdateTime(new Date());
        car.setUpdateType("3");
        car.setUpdateReason(params.get("updateReason"));

        //开票新增参数
        if ("1".equals(params.get("isDispatch"))) {
            car.setIsDispatch("1");
            car.setRoadCardStatus("0");
            car.setRoadCardFailReason("");
            car.setRoadLicenseStatus("0");
            car.setRoadLicenseReason("");
        } else {
            car.setIsDispatch("0");
        }
        String tailDrivingOtherSideUrl = getPicUrlByPic(tailDrivingOtherSidePic);
        if (tailDrivingOtherSideUrl!=null){
            car.setTailDrivingOtherSideUrl(tailDrivingOtherSideUrl);
        }
        if (headDrivingPic != null && !headDrivingPic.isEmpty()) {
            car.setHeadDrivingUrl(renamePic(headDrivingPic, "car"));
        }
        if (tailDrivingPic != null && !tailDrivingPic.isEmpty()){
            car.setTailDrivingUrl(renamePic(tailDrivingPic, "car"));
        }
        //更改偏好
        carService.updatePreferNew(car.getId());
        return car;

    }

    private DispatchCarBean createDispatchCarBean(Map<String, String> params,MultipartFile roadCardPositivePic, MultipartFile roadCardOtherSidePic ,MultipartFile roadLicenseNoPic,Car car) throws IOException {
        DispatchCarBean dispatchCarBean=new DispatchCarBean();
        dispatchCarBean.setId(Long.valueOf(params.get("id")));
        dispatchCarBean.setRoadTransportType(params.get("roadTransportType"));
        dispatchCarBean.setRoadCardPositiveUrl(getPicUrlByPic(roadCardPositivePic));
        dispatchCarBean.setRoadCardOtherSideUrl("2".equals(params.get("roadTransportType")) ? "":getPicUrlByPic(roadCardOtherSidePic));
        String picUrlByPic = getPicUrlByPic(roadLicenseNoPic);
        if ("2".equals(car.getRoadLicenseStatus())&&picUrlByPic==null){
            car.setRoadLicenseStatus(null);
            car.setRoadLicenseReason("");
            dispatchCarBean.setRoadLicenseNoUrl("");
        }else {
            dispatchCarBean.setRoadLicenseNoUrl(picUrlByPic);
        }
        return dispatchCarBean;
    }

    private void syncAuthCarMsg(Long carId) {
        MqSyncAuthCarMsg syncAuthCarMsg = new MqSyncAuthCarMsg();
        String messageSerailNum = SerialNumUtil.generateSeriaNum();
        syncAuthCarMsg.setMessageSerailNum(messageSerailNum);
        syncAuthCarMsg.setCarId(carId.intValue());
        syncAuthCarMsg.setType("2");
        syncAuthCarMsg.setMessageType(MqBaseMessageBean.SYNC_AUTH_CAR_STATUS_MESSAGE);
        logger.info("car controller syncAuthCarMsg: " + syncAuthCarMsg);
        tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(syncAuthCarMsg), MqBaseMessageBean.SYNC_AUTH_CAR_STATUS_MESSAGE);
        tytMqMessageService.sendMqMessage(syncAuthCarMsg.getMessageSerailNum(), JSON.toJSONString(syncAuthCarMsg), MqBaseMessageBean.SYNC_AUTH_CAR_STATUS_MESSAGE);
    }
}
