package com.tyt.openapi.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.message.bean.MqPushAcvitityMessage;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NewsMessagePush;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.ts.GoodsPushDataVo;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;

@Controller
@RequestMapping("/openapi/message")
public class MessagePushController extends BaseController {

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Autowired
    private MessageCenterPushService messageCenterPushService;


    /**
     * 发短信
     * @param cellPhone  手机号
     * @param content  短信内容
     * @param timestamp  时间戳
     * @return rm
     */
    @RequestMapping(value = "/sms/send")
    @ResponseBody
    public ResultMsgBean smsSend(@RequestParam(value = "cellPhone", required = true) String cellPhone,
                                          @RequestParam(value = "content", required = true) String content,
                                          @RequestParam(value = "timestamp", required = true) Date timestamp) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("send message begin, the cellPhone is [ " + cellPhone + " ],content is ["+content+"], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("成功");
        try {
            if (timestamp == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("时间戳不正确");
                return rm;
            } else if (StringUtils.isBlank(cellPhone)) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("cellPhone不能为空");
                return rm;
            } else if (StringUtils.isBlank(content)){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("content不能为空");
                return rm;
            }else{
                tytMqMessageService.sendSms(cellPhone,content);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        logger.info("send message end,  the cellPhone is [ " + cellPhone + " ],content is ["+content+"], result is [ " + rm + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 发push
     * @param cellPhone 用户账号
     * @param title  标题
     * @param summary  摘要
     * @param content 内容
     * @param type 跳转位置 1.打开好货列表  2.打开好货订单详情  3.车辆管理首页
     * @param orderId 订单id
     * @param headstockId     默认推送车头id
     * @param timestamp 时间戳
     * @return rm
     */
    @RequestMapping(value = "/push/send")
    @ResponseBody
    public ResultMsgBean pushSend(@RequestParam(value = "cellPhone", required = true) String cellPhone,
                                  @RequestParam(value = "title", required = true) String title,
                                  @RequestParam(value = "summary", required = true) String summary,
                                 @RequestParam(value = "content", required = true) String content,
                                  @RequestParam(value = "type", required = true) Integer type,
                                  @RequestParam(value = "orderId", required = false) Integer orderId,
                                  @RequestParam(value = "headstockId", required = false) Integer headstockId,
                                 @RequestParam(value = "timestamp", required = true) Date timestamp) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("send push begin, the cellPhone is [ " + cellPhone + " ],content is ["+content+"], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("成功");
        try {
            MqPushAcvitityMessage message = new MqPushAcvitityMessage();
            User user = userService.getUserByCellphone(cellPhone);
            if (user == null){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("该手机号没有注册");
                return rm;
            }
            if (type == 2 && orderId == null){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("好货订单id不能为空");
                return rm;
            }
            if (type == 2 && headstockId == null){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("默认推送车头id不能为空");
                return rm;
            }
            String messageSerailNum = SerialNumUtil.generateSeriaNum();
            message.setUserId(user.getId());
            message.setTitle(title);
            message.setSummary(summary);
            message.setContent(content);
            message.setCellPhone(user.getCellPhone());
            message.setTrueName(user.getTrueName());
            message.setMessageSerailNum(messageSerailNum);
            message.setOpenType("1");
            String appOpenUrl = tytConfigService.getStringValue("APPOpenUrl", "http://open.teyuntong.cn");
            if (type == 1){
                appOpenUrl+="?typeCode=1030";
            }else if(type == 2){
                appOpenUrl+="?typeCode=1031&id="+orderId+"&headstockId="+headstockId;
            }else if (type == 3){
                appOpenUrl+="?typeCode=1040";
            }
            message.setLinkUrl(appOpenUrl);
            message.setMessageType(MqBaseMessageBean.PUSH_ACVITITY_MESSAGE);
            tytMqMessageService.addSaveMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message),message.getMessageType());
            tytMqMessageService.sendMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message),message.getMessageType());
        }catch (Exception e){
            e.printStackTrace();
            rm.setCode(500);
            rm.setMsg("send diaodu faile"+e);
        }
        logger.info("send push end,  the cellPhone is [ " + cellPhone + " ],content is ["+content+"], result is [ " + rm + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }





    /**
     * 调度发送mq push
     * @param cellPhone 用户账号
     * @param title 标题
     * @param summary   摘要
     * @param content   内容
     * @param sendType   发送类型：0原app 1:给车发 、2:给货发
     * @param orderId 订单id
     * @param sendCode 推送信息：1 push; 2 站内信; 3 push+站内信
     * @param typeCode 跳转页码
     * @param timestamp     时间戳
     * @param linkUrl   地址
     * @return
     */
    @RequestMapping(value = "/message/send")
    @ResponseBody
    public ResultMsgBean pushSend(@RequestParam(value = "cellPhone", required = true) String cellPhone,
                                  @RequestParam(value = "title", required = true) String title,
                                  @RequestParam(value = "summary", required = true) String summary,
                                  @RequestParam(value = "content", required = true) String content,
                                  @RequestParam(value = "sendType", required = true) Long sendType,
                                  @RequestParam(value = "orderId", required = false) Long orderId,
                                  @RequestParam(value = "sendCode", required = true) Long sendCode,
                                  @RequestParam(value = "typeCode", required = false) String typeCode,
                                  @RequestParam(value = "timestamp", required = true) Date timestamp,
                                  @RequestParam(value = "linkUrl", required = true) String  linkUrl) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("send push begin, the cellPhone is [ " + cellPhone + " ],content is ["+content+"], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"成功");
        try {
            //根据用户账号查询这个用户
            User user = userService.getUserByCellphone(cellPhone);
            //判断用户是否已注册
            if (user == null){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("该手机号没有注册");
                return rm;
            }
            sendPushAndMessagePush(title, summary, content, sendType, orderId, sendCode, typeCode,  linkUrl, user.getId());
        }catch (Exception e){
            logger.info("调度发送mq push失败 原因", e);
            rm.setCode(500);
            rm.setMsg("send diaodu faile"+e);
        }
        logger.info("send push end,  the cellPhone is [ " + cellPhone + " ],content is ["+content+"], result is [ " + rm + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    private void sendPushAndMessagePush(String title, String summary, String content, Long sendType, Long orderId, Long sendCode, String typeCode, String  linkUrl, Long userId) {
        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.addUserId(userId);

        messagePushBase.setTitle(title);
        messagePushBase.setContent(content);
        messagePushBase.setRemarks(summary);

        if (sendType == 1) {
            messagePushBase.setCarPush((short)1);
        } else if (sendType == 2) {
            messagePushBase.setGoodsPush((short)1);
        }

        //push
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);

        if (typeCode!=null && typeCode.equals("1031")){
            linkUrl += "&id="+orderId;
            // 需要设置extraData才能跳转到详情页
            GoodsPushDataVo goodsPushDataVo = new GoodsPushDataVo();
            goodsPushDataVo.setSrcMsgId(orderId);
            notifyMessage.setExtraData(JSON.toJSONString(goodsPushDataVo));
        }

        notifyMessage.setLinkUrl(linkUrl);

        //站内信
        NewsMessagePush newsMessage = NewsMessagePush.createByPushBase(messagePushBase);


        if (sendCode == 1){
            newsMessage = null;
        } else if (sendCode == 2){
            notifyMessage = null;
        }

        messageCenterPushService.sendMultiMessage(null, newsMessage, notifyMessage);
    }

}
