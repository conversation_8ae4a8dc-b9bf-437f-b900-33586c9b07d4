package com.tyt.openapi.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.driver.bean.CarDriverReq;
import com.tyt.driver.service.TytCarDriverArchivesService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCarDriverArchives;
import com.tyt.model.User;
import com.tyt.openapi.bean.CarDriverDescriptionBean;
import com.tyt.openapi.bean.OpenCarDriver;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.httputil.SCRestTemplate;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestOperations;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/01/21 13:35
 */
@Controller
@RequestMapping("/openapi/car/driver")
public class OpenApiCarDriverController extends BaseController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytCarDriverArchivesService")
    private TytCarDriverArchivesService tytCarDriverArchivesService;

    @Resource(name = "scRestTemplate")
    private RestOperations restOperations;

    @Autowired
    private UserService userService;

    @ResponseBody
    @RequestMapping("/description")
    public ResultMsgBean description(@RequestParam("cellPhone") String cellPhone){
        logger.info("调度同步数据：手机号：【{}】", cellPhone);
        //验证手机号不为空 1开头的长度为11位的数字
        if(StringUtils.isEmpty(cellPhone) || cellPhone.length() != 11
                || !NumberUtils.isNumber(cellPhone) || !cellPhone.startsWith("1")){
            return new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_PHONE_ERR,"请输入正确的手机号");
        }

        List<CarDriverDescriptionBean> archives = tytCarDriverArchivesService
                .searchList(cellPhone)
                .stream()
                .map(arch -> {
                    CarDriverDescriptionBean bean = new CarDriverDescriptionBean();
                    BeanUtils.copyProperties(arch, bean);
                    bean.setDriverId(arch.getId());
                    return bean;
                }).collect(Collectors.toList());
        return new ResultMsgBean(200, "查询成功", archives);

    }


    @RequestMapping("/save")
    @ResponseBody
    public ResultMsgBean save(HttpServletRequest request,
                              @RequestParam(name = "dicFrontImage", required = false) MultipartFile dicFrontImage,
                              @RequestParam(name = "dicBackImage", required = false) MultipartFile dicBackImage,
                              @RequestParam(name = "dlFrontImage", required = false) MultipartFile dlFrontImage,
                              @RequestParam(name = "dcImageUrl", required = false) MultipartFile dcImageUrl) {

        /* 参数解析 */
        try {
            Map<String, String> params = parseRequestParams(request);
            logger.info("上传请求参数：{}", JSONObject.toJSONString(params));
            String cellPhone = params.get("cellPhone");
            if(StringUtils.isBlank(cellPhone)) {
                return new ResultMsgBean(ReturnCodeConstant.ERROR,"用户手机号不能为空");
            }
            User user = userService.getUserByCellphone(cellPhone);
            if(user == null) {
                return new ResultMsgBean(ReturnCodeConstant.ERROR,"用户不存在");
            }
            Long userId = user.getId();
            params.put("userId", userId.toString());

            CarDriverReq carDriverReq = new JSONObject(Maps.newHashMap(params)).toJavaObject(CarDriverReq.class);

            if(!super.isAllImg(dicFrontImage, dicBackImage, dlFrontImage, dcImageUrl)){
                ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.ERROR,"图片格式有误！");
                return rm;
            }

            //上传照片
            uploadImages(dicFrontImage, dicBackImage, dlFrontImage, dcImageUrl, carDriverReq);

            // 设置响应返回的对象
            OpenCarDriver carDriver = new OpenCarDriver();
            carDriver.setCellPhone(cellPhone);
            carDriver.setUserId(userId);
            carDriver.setDriverPhone(carDriverReq.getDriverPhone());
            carDriver.setDriverShowName(carDriverReq.getDriverShowName());
            // 查询当前是否存在本次新增的司机
            TytCarDriverArchives archives = tytCarDriverArchivesService.getByDriverPhone(userId, carDriverReq.getDriverPhone());
            if(archives != null && (archives.getExamineStatus().equals(1) || archives.getExamineStatus().equals(2))) { // 待认证和认证成功
                // 设置返回值
                carDriver.setId(archives.getId());
                carDriver.setExamineStatus(archives.getExamineStatus());
                return new ResultMsgBean(ReturnCodeConstant.OK,"用户已存在，且为认证中或认证成功", carDriver);
            }
            // 更新动作，设置司机ID，后续业务按此ID处理更新和新增
            if(archives != null && archives.getExamineStatus().equals(3)) {
                carDriverReq.setDriverId(archives.getId());
            }

            ResultMsgBean bean = tytCarDriverArchivesService.updateArchives(carDriverReq);
            // 3/26 要下线特运通大件运输智能调度系统，先注释掉下面代码，过三个月可以删除
            /*if(Objects.nonNull(bean.getData()) && bean.getData() instanceof TytCarDriverArchives
                    && StringUtils.isNotEmpty(((TytCarDriverArchives) bean.getData()).getUserPhone())){
                //通知变动
                sendDriverDescriptionChangeMessage(((TytCarDriverArchives) bean.getData()).getUserPhone());

                // 新增或更新成功后设置返回值
                archives = (TytCarDriverArchives)bean.getData();
                carDriver.setId(archives.getId());
                carDriver.setExamineStatus(archives.getExamineStatus());
                bean.setData(carDriver);
            }*/
            return bean;
        } catch (Exception e) {
            logger.info("添加司机重复异常：{}  【{}】", e.getClass().getName(), e.getMessage());
            //唯一索引重复添加异常
            if (e instanceof DataIntegrityViolationException) {
                return new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_PHONE_EXIST,"手机号已存在");
            }
            e.printStackTrace();
            return new ResultMsgBean(ReturnCodeConstant.ERROR,"网络异常");
        }

    }

    /**
     * 解析请求到的map集合
     * @param request Params
     * @return
     */
    @Override
    protected Map<String,String> parseRequestParams(HttpServletRequest request)throws Exception{
        Map<String, String[]> requestParams=request.getParameterMap();
        Map<String, String> params=new HashMap<String,String>();
        for(Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();){
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i].trim(): valueStr + values[i].trim() + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
//			valueStr = new String(valueStr.getBytes("iso-8859-1"),"UTF-8");
            params.put(name, valueStr);
        }
        return params;
    }


    /**
     * 上传图片
     *
     * @param file
     * @return
     */
    private Optional<String> upload(MultipartFile file) {

        try {
            if (Objects.nonNull(file) && !file.isEmpty()) {
                String fileName = super.renamePic(file, "driver");
                file.transferTo(new File(AppConfig.getProperty("picture.path.domain") + fileName));
                return Optional.of(fileName);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    /**
     * 上传照片
     *
     * @return
     */
    private void uploadImages(MultipartFile dicFrontImage,
                              MultipartFile dicBackImage,
                              MultipartFile dlFrontImage,
                              MultipartFile dcImageUrl,
                              CarDriverReq carDriverReq) {
        //身份证正面照
        carDriverReq.setDicFrontImagePath(upload(dicFrontImage));
        //身份证背面照
        carDriverReq.setDicBackImagePath(upload(dicBackImage));
        //驾驶证正面照
        carDriverReq.setDlFrontImagePath(upload(dlFrontImage));
        //就业证不能为空
        carDriverReq.setDcImageUrlPath(upload(dcImageUrl));
    }

    private void sendDriverDescriptionChangeMessage(String phone){

        Map<String, String> map= new HashMap<>();
        map.put("ownerPhone", phone);
        SCRestTemplate.DefaultResponse response = restOperations.postForObject("/app/driver/inform", map, SCRestTemplate.DefaultResponse.class);
        if(Objects.nonNull(response) && response.getStatus() == 200){
            logger.info("司机变更通知成功");
        }else{
            logger.error("司机变更通知失败 手机号为：{}", phone);
        }
    }
}
