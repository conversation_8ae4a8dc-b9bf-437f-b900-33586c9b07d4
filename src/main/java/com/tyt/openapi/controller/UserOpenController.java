package com.tyt.openapi.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqUserMsg;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.model.*;
import com.tyt.openapi.bean.UserResponse;
import com.tyt.user.service.TytChannelLogService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.tyt.user.controller.UserController.IS_MOCK_USER;

/**
 * Created by duanwc on 2019/12/2.
 */
@Controller
@RequestMapping("/openapi/user")
public class UserOpenController extends BaseController {

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "tytUserSubService")
    private TytUserSubService tytUserSubService;

    @Resource(name = "tytChannelLogService")
    private TytChannelLogService tytChannelLogService;

    @Autowired
    private IInfofeeDetailService infofeeDetailService;

    /**
     * 用户认证接口
     *
     * @param cellPhone 手机号
     * @param password MD5后的密码
     * @return
     */
    @RequestMapping(value = "/verification")
    @ResponseBody
    public ResultMsgBean userVerification(@RequestParam(value = "cellPhone", required = true) String cellPhone,
                                          @RequestParam(value = "password", required = true) String password) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query user verification begin, the cellPhone is [ {} ], start time is [ {} ]", cellPhone, startTimeInMilli);
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            /* 根据手机号查询用户 */
            User user = userService.getUserByCellphone(cellPhone);
            //马甲用户登陆提示同未注册
            if (user != null && user.getIsMock() == 1) {
                user = null;
            }
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户尚未注册");
                return rm;
            }
            if(!user.getPassword().equals(password)) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户名或密码错误");
                return rm;
            }
            Integer coopNums = infofeeDetailService.getCoopNums(user.getId());

            UserResponse userResponse = new UserResponse();
            userResponse.setId(user.getId());
            userResponse.setCellPhone(user.getCellPhone());
            userResponse.setUserName(user.getUserName());
            userResponse.setTrueName(user.getTrueName());
            userResponse.setCoopNums(coopNums);
            rm.setData(userResponse);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        logger.info("query user verification end, the cellPhone is [ {} ], result is [ " + rm + " ] waste time is [ {} ]", cellPhone, rm, System.currentTimeMillis() - startTimeInMilli);
        return rm;
    }


    /**
     * 获取用户接口
     *
     * @param cellPhone 用户手机号
     * @return
     */
    @RequestMapping(value = "/get")
    @ResponseBody
    public ResultMsgBean userGet(@RequestParam(value = "cellPhone", required = true) String cellPhone) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query user get begin, the cellPhone is [ {} ], start time is [ {} ]", cellPhone, startTimeInMilli);
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            /* 根据手机号查询用户 */
            User user = userService.getUserByCellphone(cellPhone);
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户尚未注册");
                return rm;
            }

            Integer coopNums = infofeeDetailService.getCoopNums(user.getId());

            UserResponse userResponse = new UserResponse();
            userResponse.setId(user.getId());
            userResponse.setCellPhone(user.getCellPhone());
            userResponse.setUserName(user.getUserName());
            userResponse.setTrueName(user.getTrueName());
            userResponse.setCoopNums(coopNums);
            userResponse.setVerifyFlag(user.getVerifyFlag());
            userResponse.setIdCard(user.getIdCard());
            rm.setData(userResponse);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        logger.info("query user get end, the cellPhone is [ {} ], result is [ " + rm + " ] waste time is [ {} ]", cellPhone, rm, System.currentTimeMillis() - startTimeInMilli);
        return rm;
    }

    /**
     * 用户注册接口
     *
     * @param cellPhone 用户手机号
     * @param password 用户密码
     * @param userName 用户昵称
     * @return
     */
    @RequestMapping(value = "/reg")
    @ResponseBody
    public ResultMsgBean userReg(@RequestParam(value = "cellPhone", required = true) String cellPhone,
                                 @RequestParam(value = "password", required = true) String password,
                                 @RequestParam(value = "userName", required = false) String userName,
                                 @RequestParam(value = "clientSign", required = false)String clientSign,
                                 @RequestParam(value = "clientVersion", required = false)String clientVersion,
                                 HttpServletRequest request, HttpServletResponse response) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("user reg begin, the cellPhone is [ {} ], start time is [ {} ]", cellPhone, startTimeInMilli);
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            // 往tyt_cellphone添加手机号
            try {
                userService.addCellPhoneToTemp(cellPhone);
            } catch (Exception e) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("手机号已经存在,您可以直接登录");
                return rm;
            }
            // 手机号唯一性检验
            User oldUser = userService.getUserByCellphone(cellPhone);
            if (oldUser != null && oldUser.getIsMock() != IS_MOCK_USER) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("手机号已经存在,您可以直接登录");
                return rm;
            }
            // 生成用户实体
            User user = regUser(cellPhone, password, userName);
            if(oldUser != null && oldUser.getIsMock() == IS_MOCK_USER) {
                //更新mock用户为正式用户
                user.setId(oldUser.getId());
                user.setUserName(oldUser.getUserName());
                user.setIsMock(0);
                userService.evict(oldUser);
                userService.update(user);
            }else{
				/* 保存用户信息 */
                user = userService.addUser(user);
            }
            // 发送MQ
            MqUserMsg mqUserMsg = new MqUserMsg();
            mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
            mqUserMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_USER_INIT_MESSAGE);
            mqUserMsg.setUserId(user.getId());
            mqUserMsg.setClientSign(clientSign);
            tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());
            // 发送初始化用户信息
            tytMqMessageService.sendMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());
            tytUserSubService.saveTytUserByUserId(user.getId(), 0, null);

            Map<String, String> params = new HashMap<>();
            params.put("clientSign", clientSign);
            params.put("clientVersion", clientVersion);
			/* 用户注册日志 */
            createLog(request, user, OpLog.OP_CLIENT_REG, params);

            Integer coopNums = infofeeDetailService.getCoopNums(user.getId());

            UserResponse userResponse = new UserResponse();
            userResponse.setId(user.getId());
            userResponse.setCellPhone(user.getCellPhone());
            userResponse.setUserName(user.getUserName());
            userResponse.setTrueName(user.getTrueName());
            userResponse.setCoopNums(coopNums);
            rm.setData(userResponse);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        logger.info("user reg end, the cellPhone is [ {} ], result is [ " + rm + " ] waste time is [ {} ]", cellPhone, rm, System.currentTimeMillis() - startTimeInMilli);
        return rm;
    }

    /**
     * 保存User到数据库
     *
     * @param cellPhone 用户手机号
     * @param password 用户密码
     * @param userName 用户昵称
     * @return
     * @throws Exception
     */
    private User regUser(String cellPhone, String password, String userName) throws Exception {
        User user = new User();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        user.setCtime(timestamp);
        user.setMtime(timestamp);
        user.setCellPhone(cellPhone);
        user.setPassword(password);
        user.setUserSign(1);
        user.setUserType(User.USER_TYPE_TRIAL);
        user.setInfoUploadFlag(User.INFO_UPLOAD_DISABLE);
        user.setPlatId(8); // 调度系统注册
        user.setServeDays(AppConfig.getIntProperty("tyt.trial.days"));
        user.setPhoneServeDays(AppConfig.getIntProperty("tyt.phone.trial.day"));
        user.setQqModTimes(0);
        user.setEndTime(TimeUtil.stampAdd(TimeUtil.getTimeStamp(), user.getServeDays()));
		/* 省市县 */
        String[] resultArray = MobileUtil.getMobileAddressArr(user.getCellPhone());
        if(resultArray != null && resultArray.length == 2){
            user.setProvince(resultArray[0]);
            user.setCity(resultArray[1]);
        }
        user.setUserName(userName);
        user.setVerifyFlag(0);
        user.setInfoPublishFlag(User.INFO_PUBLISH_ENABLE);
        user.setIsCar("0");
        user.setIsBank("0");
		/* 关闭QQ消息盒子 */
        user.setQqBoxFlag(User.QQ_BOX_FLAG_CLOSE);
        user.setVerifyPhotoSign(0);
        user.setUserPart(100);
        user.setSource("待定");
        user.setSourceRemark("");
        return user;
    }


    /**
     * 接收用户是否成为专车用户接口
     *
     * @param cellPhone 用户手机号
     * @return
     */
    @RequestMapping(value = "/accept/status")
    @ResponseBody
    public ResultMsgBean acceptStatus(@RequestParam(value = "cellPhone", required = true) String cellPhone) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("accept user status begin, the cellPhone is [ {} ], start time is [ {} ]", cellPhone, startTimeInMilli);
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("操作成功");
        try {
            /* 根据手机号查询用户 */
            User user = userService.getUserByCellphone(cellPhone);
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户尚未注册");
                return rm;
            }
            // 更新用户状态,升级为调度用户且名下所有车辆成为调度车
            userService.updateDispatchStatus(user, 1);
            // 删除缓存
            cacheService.del(Constant.CACHE_USER_KEY + user.getId());
            logger.info("接收调度系统请求，用户{}更新完成", user.getId());
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络异常，请稍后重试");
        }
        logger.info("accept user status end, the cellPhone is [ {} ], result is [ " + rm + " ] waste time is [ {} ]", cellPhone, rm, System.currentTimeMillis() - startTimeInMilli);
        return rm;
    }
}
