package com.tyt.openapi.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserAccount;
import com.tyt.openapi.bean.RefundOrderResponse;

import java.math.BigDecimal;


public interface OrderOpenService extends BaseService<TytUserAccount, Long> {
    /**
     * 结算
     *
     * @param userId
     * @param amount
     * @throws Exception
     */
    void saveWithdraw(Long userId, BigDecimal amount) throws Exception;

    /**
     * 退款接口
     *
     * @param refundOrderId
     * @param originalOrderId
     * @param userId
     * @param amount
     * @param orderAmount
     * @throws Exception
     */
    void saveRefund(String refundOrderId, String originalOrderId, Long userId, BigDecimal amount, BigDecimal orderAmount) throws Exception;
}
