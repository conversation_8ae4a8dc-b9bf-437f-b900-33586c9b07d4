package com.tyt.openapi.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.Car;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.openapi.bean.CarDetail;
import com.tyt.openapi.bean.QueryAllCarBean;
import com.tyt.user.bean.SpecialCar;

import java.util.Date;
import java.util.List;

/**
 * @Description 调度平台车辆服务层
 * <AUTHOR>
 * @Date 2019/5/31 15:35
 * @Param
 * @return
 **/
public interface ScheduleCarService extends BaseService<Car, Long> {

    /**
     * @return com.tyt.schedule.bean.CarDetail
     * @Description 根据车辆Id获取车辆详情信息
     * <AUTHOR>
     * @Date 2019/6/4 17:15
     * @Param [id]
     **/
    public CarDetail getCarDetail(Long id);

    /**
     * 获取车辆详情
     *
     * @param user
     * @param headCity
     * @param headNo
     * @param tailCity
     * @param tailNo
     * @return
     */
    public Car getCarDetail(User user, String headCity, String headNo, String tailCity, String tailNo);


    /**
     * 发送push消息
     * @param param1
     * @param param2
     * @param cellPhone
     */
    /*ResultMsgBean savePush(String param1, String param2, String cellPhone);*/

    /**
     * 获取手机号下全部认证过的车辆全部信息
     * @param cellPhone
     * @return
     */
    List<QueryAllCarBean> queryAllCarBean(String cellPhone, Date updateTime, Integer page,String type);

    /**
     * 根据车辆类型id获取车辆类型
     * @param id
     * @return
     */
    String getCarTypeByClassify(String id);
}
