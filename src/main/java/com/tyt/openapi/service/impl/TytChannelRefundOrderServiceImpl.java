package com.tyt.openapi.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytChannelRefundOrder;
import com.tyt.openapi.service.TytChannelRefundOrderService;
import com.tyt.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("tytChannelRefundOrderService")
public class TytChannelRefundOrderServiceImpl extends BaseServiceImpl<TytChannelRefundOrder, Long> implements TytChannelRefundOrderService {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytChannelRefundOrderDao")
    public void setBaseDao(BaseDao<TytChannelRefundOrder, Long> tytChannelRefundOrderDao) {
        super.setBaseDao(tytChannelRefundOrderDao);
    }

}
