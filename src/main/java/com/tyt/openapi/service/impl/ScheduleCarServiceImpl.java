package com.tyt.openapi.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.car.service.CarDetailHeadService;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.*;
import com.tyt.noticePopup.service.TytNoticePopupService;
import com.tyt.openapi.bean.CarDetail;
import com.tyt.openapi.bean.QueryAllCarBean;
import com.tyt.openapi.service.ScheduleCarService;
import com.tyt.service.common.common.HttpClientFactory;
import com.tyt.user.bean.CarType;
import com.tyt.user.bean.SpecialCar;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.SignUtil;
import com.tyt.util.TytSourceUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description  调度平台车辆服务层实现类
 * <AUTHOR>
 * @Date  2019/5/31 15:36
 * @Param 
 * @return 
 **/
@Service("scheduleCarService")
public class ScheduleCarServiceImpl extends BaseServiceImpl<Car, Long> implements ScheduleCarService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "scheduleCarDao")
    public void setBaseDao(BaseDao<Car, Long> scheduleCarDao) {
        super.setBaseDao(scheduleCarDao);
    }

    @Autowired
    private CarDetailHeadService carDetailHeadService;

    @Autowired
    private CarDetailTailService carDetailTailService;

    @Autowired
    private UserService userService;

    @Autowired
    private TytConfigService tytConfigService;

    @Resource(name="tytNoticePopupService")
    private TytNoticePopupService tytNoticePopupService;
    private static CloseableHttpClient httpClient = HttpClientFactory.getHttpClientWithRetry();

    public static String key = AppConfig.getProperty("tyt.private.key");

    //服务器图片路径(旧规则)
    public static final String TYT_SERVER_PICTURE_URL_OLD = "tyt_server_picture_url_old";

    private static Map<String,String> CarTypes=new ConcurrentHashMap<>();
    /**
     * @Description  根据车辆Id获取车辆详情信息
     * <AUTHOR>
     * @Date  2019/6/4 17:16
     * @Param [id]
     * @return com.tyt.schedule.bean.CarDetail
     **/
    @Override
    public CarDetail getCarDetail(Long id) {
        CarDetail carDetail = new CarDetail();
        //1.查询车辆主表详情信息
        String carSql = "select c.* from tyt_car c " +
                        " where c.is_delete=1 and c.car_degree in (1,2) and c.id = ? ";

        List<Car> carList = this.getBaseDao().queryForList(carSql, new Object[]{id});
        //车辆列表中拼接车主信息
        appendUserInfo(carList);
        if(carList != null && carList.size()>0){
            Car carMainDetail = carList.get(0);

            //2.查询车头详情信息
            CarDetailHead carDetailHead = new CarDetailHead();
            carDetailHead.setCarId(id);
            CarDetailHead carHeadDetail = carDetailHeadService.find(carDetailHead);
            //3.查询挂车详情信息
            CarDetailTail carDetailTail = new CarDetailTail();
            carDetailTail.setCarId(id);
            CarDetailTail carTailDetail = carDetailTailService.find(carDetailTail);

            //4.拼接车辆详情信息
            carDetail.setCarMainDetail(carMainDetail);
            carDetail.setCarHeadDetail(carHeadDetail);
            carDetail.setCarTailDetail(carTailDetail);
        }
        return carDetail;
    }


    /**
     * @Description  车辆列表增加车主信息（车主姓名、联系电话）、挂车相关信息
     * <AUTHOR>
     * @Date  2019/6/6 19:04
     * @Param [allCarList]
     * @return void
     **/
    private void appendUserInfo(List<Car> allCarList) {
        StringBuffer userIds = new StringBuffer();
        if(allCarList != null && allCarList.size()>0 ){
            for (int i = 0; i < allCarList.size(); i++) {
                Car car = allCarList.get(i);
                if(i != allCarList.size()-1){
                    userIds.append(car.getUserId()).append(",");
                }else{
                    userIds.append(car.getUserId());
                }
            }
            List<User> userList = userService.getUserListByIds(userIds.toString());
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < allCarList.size(); i++) {
                    Car car = allCarList.get(i);
                    for (int j = 0; j < userList.size(); j++) {
                        User user = userList.get(j);
                        if(car.getUserId().intValue() == user.getId().intValue()){
                            //车主真实姓名
                            car.setTrueName(user.getTrueName());
                            //车主联系电话
                            car.setCellPhone(user.getCellPhone());
                        }
                    }
                    //查询、拼接挂车相关字段信息
                    CarDetailTail carDetailTail = new CarDetailTail();
                    carDetailTail.setCarId(car.getId());
                    CarDetailTail carTail = carDetailTailService.find(carDetailTail);
                    //核定载重
                    car.setCheckWeight(carTail.getCheckWeight());
                    //超载限重，单位:吨(限定值：1-999)
                    car.setMaxPayload(carTail.getMaxPayload());
                    //车长:单位mm
                    car.setLength(carTail.getLength());
                    //车宽:单位mm
                    car.setWidth(carTail.getWidth());
                    //车高:单位mm
                    car.setHeight(carTail.getHeight());
                    //挂车样式(1.纯平常规 2.高低高常规 3.纯平超低 4.高低高超低)
                    car.setIsPureFlat(carTail.getIsPureFlat());
                }
            }
        }
    }

    /**
     * 获取车辆详情
     * @param user
     * @param headCity
     * @param headNo
     * @param tailCity
     * @param tailNo
     * @return
     */
    @Override
    public Car getCarDetail(User user, String headCity, String headNo, String tailCity, String tailNo) {

        String tytServerPictureUrlOld = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_OLD);
        //用户ID
        Long userId = user.getId();
        //1.查询车辆主表详情信息
        String carSql = "select c.id id,c.user_id userId," +
                " c.head_city headCity,c.head_no headNo," +
                " c.tail_city tailCity,c.tail_no tailNo," +
                " CONCAT('"+tytServerPictureUrlOld+"',c.`head_driving_url`) headDrivingUrl, " +
                " CONCAT('"+tytServerPictureUrlOld+"',c.`tail_driving_url`) tailDrivingUrl," +
                " c.auth auth " +
                " from tyt_car c " +
                " where c.is_delete = 1 " +  // and c.car_degree in (1,2)
                " and c.user_id = ?  and c.head_city = ? and c.head_no = ? " +
                " and c.tail_city = ? and c.tail_no = ? ";

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        scalarMap.put("headDrivingUrl", Hibernate.STRING);
        scalarMap.put("tailDrivingUrl", Hibernate.STRING);
        scalarMap.put("auth", Hibernate.STRING);

        //查询车辆列表信息
        List<Car> carList = this.getBaseDao().search(carSql, scalarMap, Car.class, new Object[]{userId, headCity, headNo, tailCity, tailNo});
        Car car = null;
        if(carList != null && carList.size() > 0){
            car = carList.get(0);
            car.setCellPhone(user.getCellPhone());
        }
        return car;
    }


    /*@Override
    public ResultMsgBean savePush(String param1, String param2, String cellPhone) {
        ResultMsgBean resultMsgBean=new  ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        User userByCell = userService.getUserByCell(cellPhone);
        if (userByCell==null){
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("用户不存在！");
            return resultMsgBean;
        }
        PopupSaveBean saveBean=new PopupSaveBean();
        saveBean.setPopupTypeEnum(PopupTypeEnum.专车好货提醒);
        saveBean.setReceiveId(userByCell.getId());
        saveBean.setOriginPopup(1);
        saveBean.setCarPopup(1);
        saveBean.setGoodsPopup(0);
        saveBean.setParams(param1+","+param2);
        tytNoticePopupService.savePopup(saveBean); // 弹窗
        return resultMsgBean;
    }*/

    /**
     * 获取手机号下全部认证过的车辆全部信息
     * @param cellPhone
     * @return
     */
    @Override
    public List<QueryAllCarBean> queryAllCarBean(String cellPhone, Date updateTime, Integer page,String type) {
        StringBuffer updateBuff = new StringBuffer();
        String  allCarBeanSql = " SELECT\n" +
                "  -- 个人信息\n" +
                "  c.id,\n" +
                "  c.user_id userId, \n" +
                "  u.cell_phone cellPhone,   -- 用户账号\n" +
                "  u.true_name trueName,     -- 车主姓名\n" +
                "  u.sex sex,                -- 性别\n" +
                "    u.id_card idCard,        -- 身份证号\n" +
                "  \n" +
                "  -- 车辆行驶本\n" +
                "  c.head_driving_url headDrivingUrl,  -- 车头行驶本\n" +
                "  c.tail_driving_url   tailDrivingUrl,-- 挂车行驶本\n" +
                "  \n" +
                "  -- 车头信息\n" +
                "  \n" +
                "  c.head_city headCity,    -- 车头车牌号\n" +
                "  c.head_no headNo,      -- \n" +
                "  c.create_time createTime,    -- 创建时间\n" +
                "  c.car_type carType,    -- 车辆类型\n" +
                "  c.head_name headName,    -- 所有人\n" +
                "  c.head_phone headPhone,    -- 所有人电话\n" +
                "  t.blong_type hBlongType,  -- 所有方类型\n" +
                "  h.address hAddress,      -- 地址  \n" +
                "  t.use_nature useNature,        -- 使用性质\n" +
                "  c.head_brand carBrand,        -- 车头品牌型号\n" +
                "  h.car_idcode hCarIdcode,  -- 车辆识别代号\n" +
                "  h.car_engine_no hCarEngineno, -- 发动机号\n" +
                "  h.car_register hCarRegister, -- 注册日期\n" +
                "  h.issue_date hIssueDate, -- 发证日期\n" +
                "  h.record_no hRecordNo,   -- 档案编号\n" +
                "  h.people hPeople,    -- 核定载人数\n" +
                "  h.total_weight hTotalWeight,-- 总质量\n" +
                "  h.curb_weight hCurbWeight, -- 整备质量\n" +
                "  h.check_weight hCheckWeight, -- 核定载质量\n" +
                "  h.length hLength,       -- 外廓尺寸\n" +
                "  h.tow_weight hTowWeight, -- 准牵引总质量\n" +
                "  h.scrap_date hScrapDate, -- 强制报废期\n" +
                "  h.test_date hTestDate,  -- 检验有效期\n" +
                "  h.check_record   hCheckRecord,-- 检验记录  \n" +
                "  c.car_degree carDegree,    -- 车辆等级\n" +
                "  c.driver_name  driverName,-- 主司机姓名\n" +
                "  c.driver_phone  driverPhone,-- 主司机电话\n" +
                "  c.secondary_driver_name secondaryDriverName,  -- 副司机姓名\n" +
                "  c.secondary_driver_phone secondaryDriverPhone,  -- 副司机电话\n" +
                "  u.true_name  scheduleUserName,-- 调度人姓名\n" +
                "  u.cell_phone  scheduleUserPhone,-- 调度人电话\n" +
                "  c.follow_driver_phone followDriverPhone,  -- 随车电话\n" +
                "  c.car_head_type  carHeadType,  -- 车头类型\n" +
                "  c.horse_power  horsePower,    -- 牵引马力\n" +
                "  c.driving_form   drivingForm,  -- 驱动形式\n" +
                "  c.emission_standard  emissionStandard,  -- 排放标准\n" +
                "  c.head_auth_status headAuthStatus, -- 车头认证结果\n" +
                "  \n" +
                "   -- 挂车信息\n" +
                "            \n" +
                "  c.tail_city tailCity, -- 挂车车牌号\n" +
                "  c.tail_no tailNo, -- 挂车车牌号\n" +
                "  t.car_type tCarType,  -- 车辆类型\n" +
                "  c.tail_name tailName, -- 所有人\n" +
                "  t.blong_type tBlongType,-- 所有方类型\n" +
                "  \n" +
                "  t.address tAddress,          -- 地址\n" +
                "    t.use_nature useNature,        -- 使用性质\n" +
                "           \n" +
                "  c.tail_brand tailBrand,   -- 挂车品牌型号\n" +
                "  t.car_idcode tCarIdcode, -- 车辆识别代号\n" +
                "  t.car_engine_no tCarEngineno,   -- 发动机号\n" +
                "  t.car_register tCarRegister,          -- 注册日期\n" +
                "    t.issue_date tIssueDate, -- 发证日期\n" +
                "  t.record_no tRecordNo,          -- 档案编号\n" +
                "  t.people tPeople,          -- 核定载人数\n" +
                "  t.total_weight tTotalWeight,          -- 总质量\n" +
                "  t.curb_weight tCurbWeight,          -- 整备质量\n" +
                "    t.check_weight tCheckWeight,          -- 核定载质量\n" +
                "  t.length tLength,\n" +
                "  t.width tWidth,\n" +
                "  t.height tHeight,          -- 外廓尺寸\n" +
                "  t.tow_weight tTowWeight,          -- 准牵引总质量\n" +
                "  t.scrap_date tScrapDate,          -- 强制报废期\n" +
                "  t.test_date tTestDate,          -- 检验有效期\n" +
                "  t.check_record tCheckRecord,    -- 检验记录\n" +
                "  t.is_pure_flat isPureFlat,    -- 挂车样式\n" +
                "  t.load_surface_length loadSurfaceLength,  \n" +
                "  t.load_surface_tail_length   loadSurfaceTailLength,-- 工作面长(m)\n" +
                "  t.is_joint isJoint,        -- 是否拼接\n" +
                "  t.joint_length  jointLength,        -- 拼接线数\n" +
                "  t.is_joint_pull isJointPull,          -- 是否抽拉\n" +
                "  t.joint_pull_length jointPullLength,    -- 抽拉长度\n" +
                "  t.gooseneck_height  gooseneckHeight,  -- 鹅颈高(m)\n" +
                "  t.gooseneck_length  gooseneckLength,    -- 鹅颈长(m)\n" +
                "  t.plate_height  plateHeight,    -- 板高(m)\n" +
                "  h.car_type hcarType,    -- 挂车类型\n" +
                "  c.has_ladder hasLadder,    -- 爬梯\n" +
                "  t.max_payload  maxPayload,    -- 最大载重(t)\n" +
                "  t.is_expose_tyre isExposeTyre,    -- 是否露轮胎\n" +
                "  t.is_with_wing isWithWing,    -- 是否带翅膀\n" +
                "  t.is_have_backplate  isHaveBackplate,    -- 是否有护板\n" +
                "  c.tail_driving_other_side_url tailDrivingOtherSideUrl,    -- 临牌反面url\n" +
                "  c.driver_id driverId,    -- 司机id\n" +
                "  c.is_dispatch isDispatch,    -- 是否调度车 1是 0或者null不是\n" +
                "  c.road_card_status roadCardStatus,    -- 道路运输证审核结果 0审核中1审核成功2审核失败\n" +
                "  c.road_license_status roadLicenseStatus,    -- 道路运输经营许可证审核结果 0审核中1审核成功2审核失败\n" +
                "  c.road_card_fail_reason roadCardFailReason,    -- 道路运输证审核失败原因\t\n" +
                "  c.road_license_fail_reason roadLicenseFailReason,    -- 道路运输经营许可证审核失败原因\t\n" +
                "  c.tail_phone tailPhone,    -- 车头车主电话\t\n" +
                "  t.load_surface_height loadSurfaceHeight,    -- 工作面高度\t\n" +
                "  h.energy_type energyType,    -- 车辆能源类型\t\n" +
                "  h.social_credit_code socialCreditCode,    -- 所有人统一社会信用代码或证件号码\t\n" +
                "  h.road_card_positive_url roadCardPositiveUrl,    -- 道路运输证正面\t\n" +
                "  h.road_card_other_side_url roadCardOtherSideUrl,    -- 道路运输证反面\t\n" +
                "  h.road_business_name roadBusinessName,    -- 道路运输业户名称\t\n" +
                "  h.road_card_car_city roadCardCarCity,    -- 道路运输证车牌照头字母\t\n" +
                "  h.road_card_car_no roadCardCarNo,    -- 道路运输证车牌照号码\t\n" +
                "  h.road_card_expir_date roadCardExpirDate,    -- 道路运输证到期日\t\n" +
                "  h.road_license_no_url roadLicenseNoUrl,    -- 道路运输经营许可证\t\n" +
                "  h.road_license_no roadLicenseNo,    -- 道路运输经营许可证号\t\n" +
                "  h.road_license_no_expir_start_date roadLicenseNoExpirStartDate,    -- 道路运输经营许可证有效期开始时间\t\n" +
                "  h.car_colour carColour,    -- 车辆颜色代码\t\n" +
                "  h.road_license_no_expir_end_date roadLicenseNoExpirEndDate,    -- 临牌到期时间\n" +
                "  h.road_transport_certificate_no roadTransportCertificateNo,    -- 道路运输证号\n" +
                "  h.road_transport_type roadTransportType,    -- 道路运输证类型 1照片 2卡片\n" +
                "  t.temp_licence_expires tempLicenceExpires,    -- 临牌到期时间\t\n" +
                "     c.tail_auth_status tailAuthStatus,  -- 挂车认证结果\n" +
                "      c.tail_failure_reason tailFailReason   -- 备注\n" +
                " \n" +
                "FROM\n" +
                "  tyt_car c\n" +
                "  LEFT JOIN tyt_user u ON c.user_id = u.id\n" +
                "  LEFT JOIN tyt_car_detail_head h ON c.id = h.car_id\n" +
                "  LEFT JOIN tyt_car_detail_tail t ON c.id = t.car_id \n" +
                "WHERE\n" +
                "1 = 1\n" +
                " AND c.auth ='1'\n" +
                " AND c.is_delete ='1'\n" +
                " AND c.tail_auth_status = '1' ";


//        logger.info("query All  car  sql [ " + allCarBeanSql + " ] ");
        Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();

        map.put("tailDrivingOtherSideUrl", Hibernate.STRING);
        map.put("driverId", Hibernate.LONG);
        map.put("isDispatch", Hibernate.STRING);
        map.put("roadCardStatus", Hibernate.STRING);
        map.put("roadLicenseStatus", Hibernate.STRING);
        map.put("roadCardFailReason", Hibernate.STRING);
        map.put("roadLicenseFailReason", Hibernate.STRING);
        map.put("tailPhone", Hibernate.STRING);
        map.put("loadSurfaceHeight", Hibernate.STRING);
        map.put("energyType", Hibernate.STRING);
        map.put("socialCreditCode", Hibernate.STRING);
        map.put("roadCardPositiveUrl", Hibernate.STRING);
        map.put("roadCardOtherSideUrl", Hibernate.STRING);
        map.put("roadBusinessName", Hibernate.STRING);
        map.put("roadCardCarCity", Hibernate.STRING);
        map.put("roadCardCarNo", Hibernate.STRING);
        map.put("roadCardExpirDate", Hibernate.STRING);
        map.put("roadLicenseNoUrl", Hibernate.STRING);
        map.put("roadLicenseNo", Hibernate.STRING);
        map.put("roadLicenseNoExpirStartDate", Hibernate.STRING);
        map.put("carColour", Hibernate.STRING);
        map.put("roadLicenseNoExpirEndDate", Hibernate.STRING);

        map.put("id", Hibernate.INTEGER);
        map.put("userId", Hibernate.INTEGER);
        map.put("cellPhone", Hibernate.STRING);
        map.put("trueName", Hibernate.STRING);
        map.put("sex", Hibernate.STRING);
        map.put("idCard", Hibernate.STRING);
        map.put("headDrivingUrl", Hibernate.STRING);
        map.put("tailDrivingUrl", Hibernate.STRING);
        map.put("headCity", Hibernate.STRING);
        map.put("headNo", Hibernate.STRING);
        map.put("hCarType", Hibernate.STRING);
        map.put("headName", Hibernate.STRING);
        map.put("headPhone", Hibernate.STRING);
        map.put("hBlongType", Hibernate.STRING);
        map.put("hAddress", Hibernate.STRING);
        map.put("useNature", Hibernate.STRING);
        map.put("scheduleUserPhone", Hibernate.STRING);
        map.put("carBrand", Hibernate.STRING);
        map.put("hCarIdcode", Hibernate.STRING);
        map.put("hCarEngineno", Hibernate.STRING);
        map.put("hCarRegister", Hibernate.STRING);
        map.put("hIssueDate", Hibernate.STRING);
        map.put("hRecordNo", Hibernate.STRING);
        map.put("hPeople", Hibernate.STRING);
        map.put("hTotalWeight", Hibernate.STRING);
        map.put("hCurbWeight", Hibernate.STRING);
        map.put("hCheckWeight", Hibernate.STRING);
        map.put("hLength", Hibernate.STRING);
        map.put("hTowWeight", Hibernate.STRING);
        map.put("hScrapDate", Hibernate.STRING);
        map.put("hTestDate", Hibernate.STRING);
        map.put("hCheckRecord", Hibernate.STRING);
        map.put("carDegree", Hibernate.STRING);
        map.put("driverName", Hibernate.STRING);
        map.put("driverPhone", Hibernate.STRING);
        map.put("secondaryDriverName", Hibernate.STRING);
        map.put("secondaryDriverPhone", Hibernate.STRING);
        map.put("scheduleUserName", Hibernate.STRING);
        map.put("followDriverPhone", Hibernate.STRING);
        map.put("carHeadType", Hibernate.INTEGER);
        map.put("horsePower", Hibernate.STRING);
        map.put("drivingForm", Hibernate.STRING);
        map.put("emissionStandard", Hibernate.STRING);
        map.put("headAuthStatus", Hibernate.STRING);
        map.put("tailCity", Hibernate.STRING);
        map.put("tailNo", Hibernate.STRING);
        map.put("tCarType", Hibernate.STRING);
        map.put("tailName", Hibernate.STRING);
        map.put("tBlongType", Hibernate.STRING);
        map.put("tAddress", Hibernate.STRING);
        map.put("useNature", Hibernate.STRING);
        map.put("tailBrand", Hibernate.STRING);
        map.put("tCarIdcode", Hibernate.STRING);
        map.put("tCarEngineno", Hibernate.STRING);
        map.put("tCarRegister", Hibernate.STRING);
        map.put("tIssueDate", Hibernate.STRING);
        map.put("tRecordNo", Hibernate.STRING);
        map.put("tPeople", Hibernate.STRING);
        map.put("tTotalWeight", Hibernate.STRING);
        map.put("tCurbWeight", Hibernate.STRING);
        map.put("tCheckWeight", Hibernate.STRING);
        map.put("tLength", Hibernate.STRING);
        map.put("tWidth", Hibernate.STRING);
        map.put("tHeight", Hibernate.STRING);
        map.put("tTowWeight", Hibernate.STRING);
        map.put("tScrapDate", Hibernate.STRING);
        map.put("tTestDate", Hibernate.STRING);
        map.put("tCheckRecord", Hibernate.STRING);
        map.put("isPureFlat", Hibernate.STRING);
        map.put("loadSurfaceLength", Hibernate.STRING);
        map.put("loadSurfaceTailLength", Hibernate.STRING);
        map.put("isJoint", Hibernate.STRING);
        map.put("jointLength", Hibernate.STRING);
        map.put("isJointPull", Hibernate.STRING);
        map.put("jointPullLength", Hibernate.STRING);
        map.put("gooseneckHeight", Hibernate.STRING);
        map.put("gooseneckLength", Hibernate.STRING);
        map.put("plateHeight", Hibernate.STRING);
        map.put("carType", Hibernate.INTEGER);
        map.put("hasLadder", Hibernate.INTEGER);
        map.put("maxPayload", Hibernate.STRING);
        map.put("isExposeTyre", Hibernate.INTEGER);
        map.put("isWithWing", Hibernate.INTEGER);
        map.put("isHaveBackplate", Hibernate.INTEGER);
        map.put("tempLicenceExpires", Hibernate.TIMESTAMP);
        map.put("tailAuthStatus", Hibernate.INTEGER);
        map.put("tailFailReason", Hibernate.STRING);
        map.put("roadTransportCertificateNo", Hibernate.STRING);
        map.put("createTime", Hibernate.TIMESTAMP);
        List<QueryAllCarBean> list;
        updateBuff.append(allCarBeanSql);
        List<Object> listObject = new ArrayList<Object>();
        if (StringUtils.isNotBlank(cellPhone)){
            updateBuff.append( " AND u.cell_phone= ? ");
            listObject.add(cellPhone);
        }
        if(updateTime != null){
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String  formatTime = formatter.format(updateTime);
            updateBuff.append( " AND c.update_time >= ? ");
            listObject.add(formatTime);

        }
        if ("1".equals(type)){
            updateBuff.append( " and c.car_degree= 3");
        }
        if (page!=null){
            updateBuff.append(" order by c.id limit ?,?");
            listObject.add((page-1)*100);
            listObject.add(page*100);
        }
        list = this.getBaseDao().search(updateBuff.toString(), map, QueryAllCarBean.class, listObject.toArray());
        for (QueryAllCarBean queryAllCarBean : list) {
            if (queryAllCarBean.getCarType()!=null){
                TytSource tailCarType = TytSourceUtil.getSourceName("tail_car_type", queryAllCarBean.getCarType()+"");
                if (tailCarType!=null){
                    queryAllCarBean.setCarTypeName(tailCarType.getName());
                }
            }
            if (queryAllCarBean.getIsPureFlat()!=null){
                TytSource tailCarStyle = TytSourceUtil.getSourceName("tail_car_style", queryAllCarBean.getIsPureFlat() + "");
                if (tailCarStyle!=null){
                    queryAllCarBean.setIsPureFlatName(tailCarStyle.getName());
                }
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(queryAllCarBean.getTCarType())){
                queryAllCarBean.setTCarType(getCarTypeByClassify(queryAllCarBean.getTCarType()));
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(queryAllCarBean.getHCarType())){
                queryAllCarBean.setHCarTypeName(getCarTypeByClassify(queryAllCarBean.getHCarType()));
            }
        }
        return list;
    }

    @Override
    public String getCarTypeByClassify(String id){
        if (org.apache.commons.lang.StringUtils.isBlank(id)){
            return null;
        }

        String s = CarTypes.get("tyt:car:type:id" + id);
        if(StringUtils.isNotEmpty(s)){
            return s;
        }
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("carType", Hibernate.STRING);
        String sql = "SELECT  tcd.`car_type` AS 'carType' FROM tyt_car_type tcd where tcd.id = ? ";
        List<CarType> search = this.getBaseDao().search(sql, scalarMap, CarType.class, new Object[]{Integer.valueOf(id)});
        if (search!=null&&search.size()>0){
            CarTypes.put("tyt:car:type:id"+id,search.get(0).getCarType());
            return 	search.get(0).getCarType();
        }
        return null;
    }
}
