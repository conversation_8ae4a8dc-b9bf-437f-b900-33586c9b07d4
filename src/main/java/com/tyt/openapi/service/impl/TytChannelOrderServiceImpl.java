package com.tyt.openapi.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytChannelOrder;
import com.tyt.openapi.service.TytChannelOrderService;
import com.tyt.user.service.UserService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("tytChannelOrderService")
public class TytChannelOrderServiceImpl extends BaseServiceImpl<TytChannelOrder, Long> implements TytChannelOrderService {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytChannelOrderDao")
    public void setBaseDao(BaseDao<TytChannelOrder, Long> tytChannelOrderDao) {
        super.setBaseDao(tytChannelOrderDao);
    }

    public static final String CHANNEL_ORDER_PREFIX = "channel_";


    @Override
    public TytChannelOrder getByOrderId(String orderId) {
        String sql="select * from tyt_channel_order where order_id=? ";
        List<TytChannelOrder> list = this.getBaseDao().queryForList(sql, new Object[] {orderId});
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

}
