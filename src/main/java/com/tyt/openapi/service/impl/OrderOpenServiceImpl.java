package com.tyt.openapi.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.enums.FlowTypeEnum;
import com.tyt.infofee.service.TradeInfoService;
import com.tyt.infofee.service.TytFinancialFlowService;
import com.tyt.infofee.service.TytUserAccountService;
import com.tyt.infofee.service.impl.WalletServiceServiceImpl;
import com.tyt.model.*;
import com.tyt.openapi.bean.RefundOrderResponse;
import com.tyt.openapi.service.OrderOpenService;
import com.tyt.openapi.service.TytChannelRefundOrderService;
import com.tyt.payment.alipay.service.OrderService;
import com.tyt.payment.service.OrderRefundMQService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 调度平台车辆服务层实现类
 * <AUTHOR>
 * @Date 2019/5/31 15:36
 * @Param
 * @return
 **/
@Service("orderOpenService")
public class OrderOpenServiceImpl extends BaseServiceImpl<TytUserAccount, Long> implements OrderOpenService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "userAccountDaoImpl")
    public void setBaseDao(BaseDao<TytUserAccount, Long> tytUserAccountDao) {
        super.setBaseDao(tytUserAccountDao);
    }


    @Autowired
    private UserService userService;

    @Resource(name = "tytUserAccountService")
    private TytUserAccountService userAccountService;

    @Autowired
    private TytFinancialFlowService financialFlowService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private TradeInfoService tradeInfoService;

    @Autowired
    private OrderRefundMQService orderRefundMQService;

    @Autowired
    private TytChannelRefundOrderService channelRefundOrderService;


    @Override
    public void saveWithdraw(Long userId, BigDecimal amount1) throws Exception {
        long operAmount = amount1.movePointRight(2).longValue();
        User platUser = userService.getUserByCell(WalletServiceServiceImpl.VIRTUAL_USER_ACCOUNT);
        Long platUserId = platUser.getId();

        // 公司出账账户 金额加  出账账户流水
        TytUserAccount platOutAccount = userAccountService.queryAccount(platUserId, 5);
        userAccountService.updateVirtualAccountPocket(operAmount, platUserId, 5, "+");
        financialFlowService.addFinancialFlow(FlowTypeEnum.平台出账账户流水.getId(), "+", operAmount, -1, platOutAccount.getId(), platOutAccount.getCurrentBalance(), platOutAccount.getCurrentBalance() + operAmount, platUserId);

        // 公司余额账户 金额减   余额账户流水
        TytUserAccount platBalanceAccount = userAccountService.queryAccount(platUserId, 1);
        userAccountService.updateVirtualAccountPocket(operAmount, platUserId, 1, "-");
        financialFlowService.addFinancialFlow(FlowTypeEnum.平台余额账户流水.getId(), "-", operAmount, -1, platBalanceAccount.getId(), platBalanceAccount.getCurrentBalance(), platBalanceAccount.getCurrentBalance() - operAmount, platUserId);

        // 个人余额账户 金额加   个人账户流水
        TytUserAccount userAccount = userAccountService.queryAccount(userId, 1);
        userAccountService.updateVirtualAccountPocket(operAmount, userId, 1, "+");
        financialFlowService.addFinancialFlow(FlowTypeEnum.渠道结算流水.getId(), "+", operAmount, -1, userAccount.getId(), userAccount.getCurrentBalance(), userAccount.getCurrentBalance() + operAmount, userId);
    }


    @Override
    public void saveRefund(String refundOrderId, String originalOrderId, Long userId, BigDecimal amount, BigDecimal orderAmount) throws Exception {
        //退款金额,单位:元转换成分
        Long totalFee = amount.movePointRight(2).longValue();
        //查询订单对应的支付订单
        Order oldOrder = orderService.getByOrderId(originalOrderId);
        //订单总金额，单位：分
        Integer totalAmount = oldOrder.getTotalFee();

        // 1. 添加退款单
        TytChannelRefundOrder refundOrder = new TytChannelRefundOrder();
        refundOrder.setRefundOrderId(refundOrderId);
        refundOrder.setOriginalOrderId(originalOrderId);
        refundOrder.setUserId(userId);
        refundOrder.setAmount(amount);
        refundOrder.setOrderAmount(orderAmount);
        refundOrder.setStatus(0);
        refundOrder.setCtime(new Date());
        refundOrder.setMtime(new Date());
        channelRefundOrderService.add(refundOrder);

        // 2. 流水处理
        User platUser = userService.getUserByCell(WalletServiceServiceImpl.VIRTUAL_USER_ACCOUNT);
        Long platUserId = platUser.getId();
        // 公司余额账户 金额减   余额账户流水
        TytUserAccount platBalanceAccount = userAccountService.queryAccount(platUserId, 1);
        userAccountService.updateVirtualAccountPocket(totalFee, platUserId, 1, "-");
        financialFlowService.addFinancialFlow(FlowTypeEnum.平台余额账户流水.getId(), "-", totalFee, -1, platBalanceAccount.getId(), platBalanceAccount.getCurrentBalance(), platBalanceAccount.getCurrentBalance() - totalFee, platUserId);

        // 公司出账账户 金额加  出账账户流水
        TytUserAccount platOutAccount = userAccountService.queryAccount(platUserId, 5);
        userAccountService.updateVirtualAccountPocket(totalFee, platUserId, 5, "+");
        financialFlowService.addFinancialFlow(FlowTypeEnum.平台出账账户流水.getId(), "+", totalFee, -1, platOutAccount.getId(), platOutAccount.getCurrentBalance(), platOutAccount.getCurrentBalance() + totalFee, platUserId);

        TytUserAccount userAccount = userAccountService.queryAccount(userId, 1);
        financialFlowService.addFinancialFlow(FlowTypeEnum.退回保证金.getId(), "+", totalFee, -1, userAccount.getId(), userAccount.getCurrentBalance(), userAccount.getCurrentBalance() + totalFee, userId);
        financialFlowService.addFinancialFlow(FlowTypeEnum.转出保证金.getId(), "-", totalFee, -1, userAccount.getId(), userAccount.getCurrentBalance() + totalFee, userAccount.getCurrentBalance(), userId);

        // 3. 退回交易
        TytTradeinfo tradeInfo = new TytTradeinfo();
        tradeInfo.setTrade_type(22);
        tradeInfo.setTrade_time(new Date());
        tradeInfo.setUpdate_time(new Date());
        tradeInfo.setTrade_account(totalFee.toString());
        tradeInfo.setRemark("渠道定金退款！");
        tradeInfo.setPayer_account_id(platBalanceAccount.getId());
        tradeInfo.setPayer_user_id(platBalanceAccount.getUserId());
        tradeInfo.setPay_receiver_account_id(userAccount.getId());
        tradeInfo.setPay_receiver_user_id(userAccount.getUserId());
        tradeInfo.setOrder_id(originalOrderId);
        //增加商品订单退款的交易记录,返回交易记录Id
        //交易记录Id
        Long tradeInfoId = (Long) tradeInfoService.addSave(tradeInfo);

        // 4. 发送退款MQ
        this.thirdPartyOrderRefund(userId, originalOrderId, totalFee, totalAmount);
    }


    /**
     * @return void
     * @Description 发送MQ消息调用三方支付退款、增加退款记录
     * <AUTHOR>
     * @Date 2019/10/16 18:23
     * @Param [userId, orderId, totalFee,totalAmount]
     **/
    private void thirdPartyOrderRefund(Long userId, String orderId, Long totalFee, Integer totalAmount) throws Exception {
        //查询商品支付订单
        Order order = orderService.getByOrderId(orderId);

        //查询商品订单支付交易记录
        TytTradeinfo tytTradeInfo = new TytTradeinfo();
        tytTradeInfo.setOrder_id(orderId);
        //交易类型，7：会员费充值交易
        tytTradeInfo.setTrade_type(20);
        TytTradeinfo tradeinfo = tradeInfoService.find(tytTradeInfo);
        //交易记录Id
        int tradeInfoId = tradeinfo.getId().intValue();

        //退款银行：1.支付宝 2.微信 3.易宝
        Integer transforeBank = 0;
        if (order != null && tradeinfo != null) {
            //支付订单表的支付渠道 1.支付宝 2.易宝 3.微信
            String payChannel = order.getPayChannel();
            //第三方支付平台生成的订单号
            String thirdpartyOrderSerialNum = order.getThirdpartyOrderSerialNum();
            //付款方式: 微信,支付宝,易宝,连连支付
            String payMethod = order.getPayMethod();
            if (StringUtils.isNotBlank(payChannel)) {
                if ("1".equals(payChannel)) {
                    transforeBank = 1;
                } else if ("2".equals(payChannel)) {
                    transforeBank = 3;
                } else if ("3".equals(payChannel)) {
                    transforeBank = 2;
                }
                orderRefundMQService.sendThirdPartyRefund2MQ(totalAmount.toString(), totalFee.toString(), orderId, tradeInfoId, userId.toString(), transforeBank, MqBaseMessageBean.REFUND_MESSAGE, thirdpartyOrderSerialNum, payMethod);
            }
        }
    }
}
