package com.tyt.openapi.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytChannelOrder;
import com.tyt.openapi.dao.TytChannelOrderDao;
import org.springframework.stereotype.Repository;

@Repository("tytChannelOrderDao")
public class TytChannelOrderDaoImpl extends BaseDaoImpl<TytChannelOrder, Long> implements TytChannelOrderDao {
    public TytChannelOrderDaoImpl() {
        this.setEntityClass(TytChannelOrder.class);
    }
}
