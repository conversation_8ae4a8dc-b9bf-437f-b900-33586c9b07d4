package com.tyt.openapi.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytChannelRefundOrder;
import com.tyt.openapi.dao.TytChannelRefundOrderDao;
import org.springframework.stereotype.Repository;

@Repository("tytChannelRefundOrderDao")
public class TytChannelRefundOrderDaoImpl extends BaseDaoImpl<TytChannelRefundOrder, Long> implements TytChannelRefundOrderDao {
    public TytChannelRefundOrderDaoImpl() {
        this.setEntityClass(TytChannelRefundOrder.class);
    }
}
