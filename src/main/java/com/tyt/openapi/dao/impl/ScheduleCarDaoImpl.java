package com.tyt.openapi.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.Car;
import com.tyt.openapi.dao.ScheduleCarDao;
import org.springframework.stereotype.Repository;

/**
 * @Description  调度平台车辆数据层实现类
 * <AUTHOR>
 * @Date  2019/5/30 13:46
 * @Param
 * @return
 **/
@Repository("scheduleCarDao")
public class ScheduleCarDaoImpl extends BaseDaoImpl<Car, Long> implements ScheduleCarDao {

    public ScheduleCarDaoImpl() {
        this.setEntityClass(Car.class);
    }

}
