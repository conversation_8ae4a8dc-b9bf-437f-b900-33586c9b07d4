package com.tyt.openapi.bean;

import lombok.Data;

import java.io.Serializable;

@Data
public class OpenCar implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 4178269941140050216L;

    /**
     * 车辆ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 车主手机号
     */
    private String cellPhone;
    /**
     * 车头牌照头字母
     */
    private String headCity;
    /**
     * 车头牌照号码
     */
    private String headNo;
    /**
     * 挂车牌照头字母
     */
    private String tailCity;
    /**
     * 挂车牌照号码
     */
    private String tailNo;
    /**
     * 车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
     */
    private String auth;

    // 以下信息暂不需要
//    /**
//     * 车头审核状态
//     */
//    private String headAuthStatus;
//    /**
//     * 车头审核失败原因
//     */
//    private String headFailReason;
//    /**
//     * 车挂审核状态
//     */
//    private String tailAuthStatus;
//    /**
//     * 车挂审核失败原因
//     */
//    private String tailFailReason;
//
//    /**
//     * 道路运输证审核结果 0审核中1审核成功2审核失败
//     */
//    private String roadCardStatus;
//    /**
//     * 道路运输经营许可证审核结果 0审核中1审核成功2审核失败
//     */
//    private String roadLicenseStatus;
//    /**
//     * 道路运输证审核失败原因
//     */
//    private String roadCardFailReason;
//    /**
//     * 道路运输经营许可证审核失败原因
//     */
//    private String roadLicenseReason;
}
