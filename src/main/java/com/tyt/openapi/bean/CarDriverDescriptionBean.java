package com.tyt.openapi.bean;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/01/21 13:52
 */
public class CarDriverDescriptionBean {
    private Long driverId;
    private Long userId;
    private String userPhone;
    private String userShowName;
    private String driverPhone;
    private String driverShowName;
    private Integer examineStatus;
    private String dicNumber;
    private Integer dicAgingType;
    private Date dicIssueDate;
    private Date dicExpiryDate;
    private String dlNumber;
    private Integer dlAgingType;
    private Date dlIssueDate;
    private Date dlExpiryDate;
    private String dlType;
    private String dlIssueAuthority;
    private String dcNumber;
    private Date dcIssueDate;
    private Date dcExpiryDate;
    private String dicFrontImageUrl;
    private Integer dicfExamineStatus;
    private String dicfExamineLoseNote;
    private String dicBackImageUrl;
    private Integer dicbExamineStatus;
    private String dicbExamineLoseNote;
    private String dlFrontImageUrl;
    private Integer dlExamineStatus;
    private String dlExamineLoseNote;
    private String dcImageUrl;
    private Integer dcExamineStatus;
    private String dcExamineLoseNote;
    private Date commitTime;
    private Long examineUserId;
    private String examineUserName;
    private Date examineTime;
    private Date ctime;
    private Date utime;

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserShowName() {
        return userShowName;
    }

    public void setUserShowName(String userShowName) {
        this.userShowName = userShowName;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getDriverShowName() {
        return driverShowName;
    }

    public void setDriverShowName(String driverShowName) {
        this.driverShowName = driverShowName;
    }

    public Integer getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(Integer examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getDicNumber() {
        return dicNumber;
    }

    public void setDicNumber(String dicNumber) {
        this.dicNumber = dicNumber;
    }

    public Integer getDicAgingType() {
        return dicAgingType;
    }

    public void setDicAgingType(Integer dicAgingType) {
        this.dicAgingType = dicAgingType;
    }

    public Date getDicIssueDate() {
        return dicIssueDate;
    }

    public void setDicIssueDate(Date dicIssueDate) {
        this.dicIssueDate = dicIssueDate;
    }

    public Date getDicExpiryDate() {
        return dicExpiryDate;
    }

    public void setDicExpiryDate(Date dicExpiryDate) {
        this.dicExpiryDate = dicExpiryDate;
    }

    public String getDlNumber() {
        return dlNumber;
    }

    public void setDlNumber(String dlNumber) {
        this.dlNumber = dlNumber;
    }

    public Integer getDlAgingType() {
        return dlAgingType;
    }

    public void setDlAgingType(Integer dlAgingType) {
        this.dlAgingType = dlAgingType;
    }

    public Date getDlIssueDate() {
        return dlIssueDate;
    }

    public void setDlIssueDate(Date dlIssueDate) {
        this.dlIssueDate = dlIssueDate;
    }

    public Date getDlExpiryDate() {
        return dlExpiryDate;
    }

    public void setDlExpiryDate(Date dlExpiryDate) {
        this.dlExpiryDate = dlExpiryDate;
    }

    public String getDlType() {
        return dlType;
    }

    public void setDlType(String dlType) {
        this.dlType = dlType;
    }

    public String getDlIssueAuthority() {
        return dlIssueAuthority;
    }

    public void setDlIssueAuthority(String dlIssueAuthority) {
        this.dlIssueAuthority = dlIssueAuthority;
    }

    public String getDcNumber() {
        return dcNumber;
    }

    public void setDcNumber(String dcNumber) {
        this.dcNumber = dcNumber;
    }

    public Date getDcIssueDate() {
        return dcIssueDate;
    }

    public void setDcIssueDate(Date dcIssueDate) {
        this.dcIssueDate = dcIssueDate;
    }

    public Date getDcExpiryDate() {
        return dcExpiryDate;
    }

    public void setDcExpiryDate(Date dcExpiryDate) {
        this.dcExpiryDate = dcExpiryDate;
    }

    public String getDicFrontImageUrl() {
        return dicFrontImageUrl;
    }

    public void setDicFrontImageUrl(String dicFrontImageUrl) {
        this.dicFrontImageUrl = dicFrontImageUrl;
    }

    public Integer getDicfExamineStatus() {
        return dicfExamineStatus;
    }

    public void setDicfExamineStatus(Integer dicfExamineStatus) {
        this.dicfExamineStatus = dicfExamineStatus;
    }

    public String getDicfExamineLoseNote() {
        return dicfExamineLoseNote;
    }

    public void setDicfExamineLoseNote(String dicfExamineLoseNote) {
        this.dicfExamineLoseNote = dicfExamineLoseNote;
    }

    public String getDicBackImageUrl() {
        return dicBackImageUrl;
    }

    public void setDicBackImageUrl(String dicBackImageUrl) {
        this.dicBackImageUrl = dicBackImageUrl;
    }

    public Integer getDicbExamineStatus() {
        return dicbExamineStatus;
    }

    public void setDicbExamineStatus(Integer dicbExamineStatus) {
        this.dicbExamineStatus = dicbExamineStatus;
    }

    public String getDicbExamineLoseNote() {
        return dicbExamineLoseNote;
    }

    public void setDicbExamineLoseNote(String dicbExamineLoseNote) {
        this.dicbExamineLoseNote = dicbExamineLoseNote;
    }

    public String getDlFrontImageUrl() {
        return dlFrontImageUrl;
    }

    public void setDlFrontImageUrl(String dlFrontImageUrl) {
        this.dlFrontImageUrl = dlFrontImageUrl;
    }

    public Integer getDlExamineStatus() {
        return dlExamineStatus;
    }

    public void setDlExamineStatus(Integer dlExamineStatus) {
        this.dlExamineStatus = dlExamineStatus;
    }

    public String getDlExamineLoseNote() {
        return dlExamineLoseNote;
    }

    public void setDlExamineLoseNote(String dlExamineLoseNote) {
        this.dlExamineLoseNote = dlExamineLoseNote;
    }

    public String getDcImageUrl() {
        return dcImageUrl;
    }

    public void setDcImageUrl(String dcImageUrl) {
        this.dcImageUrl = dcImageUrl;
    }

    public Integer getDcExamineStatus() {
        return dcExamineStatus;
    }

    public void setDcExamineStatus(Integer dcExamineStatus) {
        this.dcExamineStatus = dcExamineStatus;
    }

    public String getDcExamineLoseNote() {
        return dcExamineLoseNote;
    }

    public void setDcExamineLoseNote(String dcExamineLoseNote) {
        this.dcExamineLoseNote = dcExamineLoseNote;
    }

    public Date getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
    }

    public Long getExamineUserId() {
        return examineUserId;
    }

    public void setExamineUserId(Long examineUserId) {
        this.examineUserId = examineUserId;
    }

    public String getExamineUserName() {
        return examineUserName;
    }

    public void setExamineUserName(String examineUserName) {
        this.examineUserName = examineUserName;
    }

    public Date getExamineTime() {
        return examineTime;
    }

    public void setExamineTime(Date examineTime) {
        this.examineTime = examineTime;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }
}
