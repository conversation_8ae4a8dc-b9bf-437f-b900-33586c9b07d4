package com.tyt.openapi.bean;

import lombok.Data;

@Data
public class OpenCarDriver implements java.io.Serializable {
    /**
     * 司机ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户手机号
     */
    private String cellPhone;
    /**
     * 司机手机号
     */
    private String driverPhone;
    /**
     * 司机姓名
     */
    private String driverShowName;
    /**
     * 审核总状态 1-待审核 2-审核通过 3-审核失败
     */
    private Integer examineStatus;


}