package com.tyt.openapi.bean;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by duanwc on 2020/2/17.
 */
public class RefundOrderResponse implements Serializable {
    /**
     * 退款订单ID
     */
    private String refundOrderId;
    /**
     * 原订单ID
     */
    private String originalOrderId;
    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 用户ID
     */
    private Long userId;

    public String getRefundOrderId() {
        return refundOrderId;
    }

    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId;
    }

    public String getOriginalOrderId() {
        return originalOrderId;
    }

    public void setOriginalOrderId(String originalOrderId) {
        this.originalOrderId = originalOrderId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "RefundOrderResponse{" +
                "refundOrderId='" + refundOrderId + '\'' +
                ", originalOrderId='" + originalOrderId + '\'' +
                ", amount=" + amount +
                ", userId=" + userId +
                '}';
    }
}
