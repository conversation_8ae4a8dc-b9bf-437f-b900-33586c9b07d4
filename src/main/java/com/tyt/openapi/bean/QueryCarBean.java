package com.tyt.openapi.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description  查询车辆条件实体对象
 * <AUTHOR>
 * @Date  2019/5/31 16:29
 * @Param
 * @return
 **/
@Data
public class QueryCarBean implements Serializable {

    //用户Id
    private Long userId;
    //车型（来源于后台挂车二级类型）
    private String carType;
    //用户Id字符串(用于筛选车辆等级)
    private String userIds;
    //车辆等级（1.熟车  2.自有）
    private String carDegree;
    //车辆状态（1.运输中 2.找货中）
    private String carStatus;
    //调度人Id
    private String scheduleUserId;
    //归属地类型(1.出发地所在省市 2.其他省市)
    private String locationType;
    //车头牌照头缩写（例如:京）
    private String headCity;
    //车头牌照号码首字母（例如:A，必须大写）
    private String headNo;

    //板车长度(最小长度)
    private String minLength;
    //板车长度(最大长度)
    private String maxLength;
    //板面类型 -- 挂车样式(1.纯平常规 2.高低高常规 3.纯平超低 4.高低高超低)
    private String isPureFlat;
    //主司机姓名
    private String driverName;

}
