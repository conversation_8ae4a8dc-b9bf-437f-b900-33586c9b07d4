package com.tyt.openapi.bean;

import java.io.Serializable;

/**
 * Created by duanwc on 2020/2/17.
 */
public class UserResponse implements Serializable {
    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户手机号
     */
    private String cellPhone;
    /**
     * 用户昵称
     */
    private String userName;
    /**
     * 用户真实姓名
     */
    private String trueName;

    /**
     * 平台合作次数（信息费交易次数）
     */
    private Integer coopNums;
    /**
     * 身份验证标记 0 未通过 1通过
     */
    private Integer  verifyFlag;
    /**
     * 身份证号
     */
    private String idCard;

    @Override
    public String toString() {
        return "UserResponse{" +
                "id=" + id +
                ", cellPhone='" + cellPhone + '\'' +
                ", userName='" + userName + '\'' +
                ", trueName='" + trueName + '\'' +
                ", coopNums=" + coopNums +
                ", verifyFlag=" + verifyFlag +
                ", idCard='" + idCard + '\'' +
                '}';
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public Integer getVerifyFlag() {
        return verifyFlag;
    }

    public void setVerifyFlag(Integer verifyFlag) {
        this.verifyFlag = verifyFlag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTrueName() {
        return trueName;
    }

    public void setTrueName(String trueName) {
        this.trueName = trueName;
    }

    public Integer getCoopNums() {
        return coopNums;
    }

    public void setCoopNums(Integer coopNums) {
        this.coopNums = coopNums;
    }

}
