package com.tyt.openapi.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description  查询用户下所有认证的车辆全部信息
 * <AUTHOR>
 * @Date  2020/9/23 9:45
 * @Param
 * @return
 **/
@Data
public class QueryAllCarBean implements Serializable {
    //车辆id
    private Integer id;
                //个人信息
    //用户Id
    private Integer userId;
    // 用户账号
    private String cellPhone;
    // 车主姓名
    private String trueName;
    // 性别
    private String sex;
    // 身份证号
    private String idCard;

             // 车辆行驶本
    // 车头行驶本
    private String headDrivingUrl;
    private String 	tailDrivingUrl;

             // 车头信息
     // 车头车牌号
    private String headCity;
    private String headNo;
    // 车辆类型
    private String hCarType;
    private String hCarTypeName;
    // 所有人
    private String headName;
    // 所有人电话
    private String headPhone;
    // 所有方类型
    private String hBlongType;
    // 地址
    private String hAddress;
    // 挂车使用性质
    private String useTNature;
    // 车头品牌型号
    private String carBrand;
    // 车辆识别代号
    private String hCarIdcode;
    // 发动机号
    private String hCarEngineno;
    // 注册日期
    private String hCarRegister;
    // 发证日期
    private String hIssueDate;
    // 档案编号
    private String hRecordNo;
    // 核定载人数
    private String hPeople;
    // 总质量
    private String hTotalWeight;
    // 整备质量
    private String hCurbWeight;
    // 核定载质量
    private String hCheckWeight;
    // 外廓尺寸
    private String hLength;
    // 准牵引总质量
    private String hTowWeight;
    // 强制报废期
    private String hScrapDate;
    // 检验有效期
    private String hTestDate;
    // 检验记录
    private String 	hCheckRecord;
    // 车辆等级
    private String carDegree;
    // 主司机姓名
    private String	driverName;
    // 主司机电话
    private String	driverPhone;
    // 副司机姓名
    private String secondaryDriverName;
    // 副司机电话
    private String secondaryDriverPhone;
    // 调度人姓名
    private String  scheduleUserName;
    // 调度人电话
    private String	scheduleUserPhone;
    // 随车电话
    private String followDriverPhone;
    // 车头类型
    private Integer	carHeadType;
    // 牵引马力
    private String	horsePower;
    // 驱动形式
    private String	 drivingForm;
    // 排放标准
    private String	emissionStandard;
    // 车头认证结果
    private String headAuthStatus;

            // 挂车信息
// 挂车车牌号
    private String tailCity;
    private String tailNo;
    // 车辆类型
    private String tCarType;
    // 所有人
    private String tailName;
    // 所有方类型
    private String tBlongType;
    // 地址
    private String tAddress;
    // 使用性质
    private String useNature;
    // 挂车品牌型号
    private String tailBrand;
    // 车辆识别代号
    private String tCarIdcode;
    // 发动机号
    private String tCarEngineno;
    // 注册日期
    private String tCarRegister;
    // 发证日期
    private String tIssueDate;
    // 档案编号
    private String tRecordNo;
    // 核定载人数
    private String tPeople;
    // 总质量
    private String tTotalWeight;
    // 整备质量
    private String tCurbWeight;
    // 核定载质量
    private String tCheckWeight;
    // 外廓尺寸
    private String tLength;
    // 准牵引总质量
    private String tWidth;
    private String tHeight;
    private String tTowWeight;
    // 强制报废期
    private String tScrapDate;
    // 检验有效期
    private String tTestDate;
    // 检验记录
    private String tCheckRecord;
    // 挂车样式
    private String isPureFlat;
    private String loadSurfaceLength;
    // 工作面长(m)
    private String 	loadSurfaceTailLength;
    // 是否拼接
    private String isJoint;
    // 拼接线数
    private String	jointLength;
    // 是否抽拉
    private String isJointPull;
    // 抽拉长度
    private String jointPullLength;
    // 鹅颈高(m)
    private String	gooseneckHeight;
    // 鹅颈长(m)
    private String	gooseneckLength;
    // 板高(m)
    private String	plateHeight;
    // 挂车类型
    private Integer carType;
    // 爬梯
    private Integer hasLadder;
    // 最大载重(t)
    private String	maxPayload;
    // 是否露轮胎
    private Integer isExposeTyre;
    // 是否带翅膀
    private Integer isWithWing;
    // 是否有护板
    private Integer	isHaveBackplate;
    // 临牌到期时间
    private Date tempLicenceExpires;
    // 挂车认证结果
    private Integer tailAuthStatus;
    // 备注
    private String tailFailReason;
    /**
     * 挂车样式名称
     */
    private String isPureFlatName;
    /**
     * 挂号类型名称
     */
    private String carTypeName;
    /**
     * 挂号类型名称
     */
    private Date createTime;
    /**
     *临牌反面url
     */
    private String tailDrivingOtherSideUrl;
    /**
     *司机id
     */
    private Long driverId;
    /**
     *是否调度车 1是 0不是
     */
    private String isDispatch;
    /**
     *道路运输证审核结果 0审核中1审核成功2审核失败
     */
    private String roadCardStatus;
    /**
     *道路运输经营许可证审核结果 0审核中1审核成功2审核失败
     */
    private String roadLicenseStatus;
    /**
     *道路运输证审核失败原因
     */
    private String roadCardFailReason;
    /**
     *道路运输经营许可证审核失败原因
     */
    private String roadLicenseFailReason;
    /**
     *车头车主电话
     */
    private String tailPhone;
    /**
     *工作面高度
     */
    private String loadSurfaceHeight	;
    /**
     *车辆能源类型
     */
    private String energyType;
    /**
     *所有人统一社会信用代码或证件号码
     */
    private String socialCreditCode;
    /**
     *道路运输证正面
     */
    private String roadCardPositiveUrl;
    /**
     *道路运输证反面
     */
    private String roadCardOtherSideUrl;
    /**
     *道路运输业户名称
     */
    private String roadBusinessName;
    /**
     *道路运输证车牌照头字母
     */
    private String roadCardCarCity;
    /**
     *道路运输证车牌照号码
     */
    private String roadCardCarNo;
    /**
     *道路运输证到期日
     */
    private String roadCardExpirDate;
    /**
     *道路运输经营许可证
     */
    private String roadLicenseNoUrl;
    /**
     *道路运输经营许可证号
     */
    private String roadLicenseNo;
    /**
     *道路运输经营许可证有效期开始时间
     */
    private String roadLicenseNoExpirStartDate;
    /**
     *车辆颜色代码
     */
    private String carColour;
    /**
     *道路运输经营许可证有效期结束时间
     */
    private String roadLicenseNoExpirEndDate;
    /**
     * 道路运输证号
     */
    private String roadTransportCertificateNo;

    /**
     * 道路运输证类型
     */
    private String roadTransportType;



}
