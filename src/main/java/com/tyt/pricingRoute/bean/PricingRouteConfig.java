package com.tyt.pricingRoute.bean;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * 发货定价路线配置
 *
 * <AUTHOR>
 * @since 2024/04/19 19:07
 */
@Getter
@Setter
@Table(name = "tyt_pricing_route_config")
public class PricingRouteConfig {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 发货类型，1表示优车发货
     */
    @Column(name = "transport_type")
    private Integer transportType;

    /**
     * 出发地省
     */
    @Column(name = "start_province")
    private String startProvince;

    /**
     * 出发地市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 出发地县区
     */
    @Column(name = "start_area")
    private String startArea;

    /**
     * 出发地完整地址
     */
    @Column(name = "start_address")
    private String startAddress;

    /**
     * 目的地省
     */
    @Column(name = "dest_province")
    private String destProvince;

    /**
     * 目的地市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 目的地县区
     */
    @Column(name = "dest_area")
    private String destArea;

    /**
     * 目的地完整地址
     */
    @Column(name = "dest_address")
    private String destAddress;

    /**
     * 是否启用，1表示启用，0表示停用
     */
    @Column(name = "enabled_status")
    private Integer enabledStatus;

    /**
     * 是否删除，0表示未删除，1表示已删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建人姓名
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人姓名
     */
    @Column(name = "modify_name")
    private String modifyName;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

}
