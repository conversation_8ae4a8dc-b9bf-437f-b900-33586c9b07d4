package com.tyt.pricingRoute.service.impl;

import com.tyt.mybatis.mapper.PricingRouteConfigMapper;
import com.tyt.plat.entity.base.TytCity;
import com.tyt.plat.mapper.base.TytCityMapper;
import com.tyt.pricingRoute.bean.PricingRouteConfig;
import com.tyt.pricingRoute.service.PricingRouteConfigService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 发货定价路线配置 service
 *
 * <AUTHOR>
 * @since 2024/04/20 09:46
 */
@Service
@AllArgsConstructor
public class PricingRouteConfigServiceImpl implements PricingRouteConfigService {

    private final PricingRouteConfigMapper pricingRouteConfigMapper;

    /**
     * 校验是否是优车定价路线
     *
     * @param startProvince 起始省
     * @param startCity     起始市
     * @param destProvince  目的省
     * @param destCity      目的市
     * @return true校验通过
     */
    @Override
    public boolean checkExcellentGoodsPricingRoute(String startProvince, String startCity, String destProvince, String destCity) {
        PricingRouteConfig config = new PricingRouteConfig();
        config.setStartAddress(startProvince + startCity);
        config.setDestAddress(destProvince + destCity);
        return pricingRouteConfigMapper.checkExcellentGoodsPricingRoute(config) != null;
    }


}
