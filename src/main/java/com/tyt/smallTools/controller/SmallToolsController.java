package com.tyt.smallTools.controller;


import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.controller.BaseController;
import com.tyt.model.*;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupService;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.smallTools.controller.bean.TytCarBaseOilCostBean;
import com.tyt.smallTools.controller.bean.UserDetailByCellphone;
import com.tyt.transport.service.CarBaseOilCostService;
import com.tyt.user.bean.RightsCheckBean;
import com.tyt.user.service.*;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.*;

@Controller
@RequestMapping("/plat/smallTool")
public class SmallToolsController extends BaseController {

    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityService;
    @Resource(name = "tytConfigService")
    private TytConfigService configService;
    @Resource(name = "carService")
    private CarService carService;
    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "blockInfoService")
    private BlockInfoService blockInfoService;
    @Resource(name = "carBaseOilCostService")
    private CarBaseOilCostService carBaseOilCostService;
    @Resource(name = "tytUserSubService")
    private TytUserSubService userSubService;

    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService noticePopupTemplService;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    public static final String REDIS_PHONE_QUERY_TIMES_KEY = "phone_query_times_key_";//查询次数缓存key
    public static final String QUERY_TIMES_CONFIG = "credit_query_times";//信用查询次数

    /**
     * 查询手机号和登陆人状态
     *
     * @param queryPhone 查询手机号
     * @return
     */
    @RequestMapping(value = "/userCredit.action")
    @ResponseBody
    public ResultMsgBean getUserCredit(@RequestParam(value = "userId", required = true) Long userId,
                                       @RequestParam(value = "queryPhone", required = true) String queryPhone,
                                       @RequestParam(value = "clientVersion", required = true) Integer clientVersion
    ) {
        ResultMsgBean result = new ResultMsgBean();
        try {

            if(clientVersion >= 5930) {
                PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.查信用, userId);
                if (!permissionResult.getUse()) {
                    TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(), null);
                    result.setNoticeData(noticePopupTempl);
                    result.setCode(ReturnCodeConstant.NO_PERMISSION);
                    return result;
                }
            } else {
                TytUserSub userSub = userSubService.getTytUserSubByUserId(userId);
                int[] userGroups = {1, 3, 5};
                if (!ArrayUtils.contains(userGroups, userSub.getUserGroup())) {
                    result.setCode(209);
                    result.setMsg("无查看信用权益");
                    return result;
                }
                PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.查信用, userId);
                if (!permissionResult.getUse() && permissionResult.getPopupTypeEnum().name().equals("信用查询次数已用完")) {
                    result.setCode(202);
                    result.setMsg("今日信用查看次数已用完");
                    return result;
                }
            }

            // 查看用户权益
            User queryUser = userService.getUserByCellphone(queryPhone);
            User loginUser = userService.getByUserId(userId);
            if (loginUser != null) {
                if (queryUser != null) {
                    if ("11".equals(queryUser.getDeliver_type_one())) {
                        result.setCode(203);
                        result.setMsg("查询的用户无信用记录");
                        return result;
                    }
                    if (loginUser.getCellPhone().equals(queryPhone)) {
                        result.setCode(201);
                        result.setMsg("不支持查自己");
                        return result;
                    }
                } else {
                    result.setCode(203);
                    result.setMsg("查询的用户无信用记录");
                    return result;
                }
            } else {
                result.setCode(210);
                result.setMsg("userId不正确");
                return result;
            }

            UserDetailByCellphone userDetail = userService.getUserCredit(userId, queryPhone, queryUser);
            result.setCode(200);
            result.setMsg("查询成功");
            result.setData(userDetail);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }

//    /**
//     * 查询手机号和登陆人状态
//     *
//     * @param queryPhone 查询手机号
//     * @return
//     */
//    @RequestMapping(value = "/phoneState.action")
//    @ResponseBody
//    public ResultMsgBean getPhoneState(@RequestParam(value = "userId", required = true) Long userId, @RequestParam(value = "queryPhone", required = true) String queryPhone) {
//        ResultMsgBean result = new ResultMsgBean();
//        try {
//            // 查看用户权益
//            TytUserSub userSub = userSubService.getTytUserSubByUserId(userId);
//            Integer userGroup = userSub.getUserGroup();
//            if (userGroup == null) {
//                // 按照体验用户处理
//                userGroup = 1;
//            }
//            String key = REDIS_PHONE_QUERY_TIMES_KEY + userGroup + "_" + userId;
//            Integer object = RedisUtil.getObject(key);
//            if (object == null) {
//                object = 0;
//            }
//            int queryTimes = 0;
//            User queryUser = userService.getUserByCellphone(queryPhone);
//            User loginUser = userService.getByUserId(userId);
//            Date now = new Date();
//            Date date = TimeUtil.weeHours(new Date(), 1);
//            int scounds = (int) (date.getTime() - now.getTime()) / 1000;
//            if (loginUser != null) {
//                TytUserIdentityAuth loginUserIdentity = userIdentityService.getTytUserIdentityAuth(userId);
//                if (loginUserIdentity == null || loginUserIdentity.getIdentityStatus() == 0) {
//                    result.setCode(204);
//                    result.setMsg("登陆用户未认证");
//                    return result;
//                }
//                if (loginUserIdentity.getExamineStatus() == 2) {
//                    if (loginUserIdentity.getIdentityStatus() == 2) {
//                        result.setCode(207);
//                        result.setMsg("登陆用户认证中");
//                        return result;
//                    }
//                    if (loginUserIdentity.getIdentityStatus() == 3) {
//                        result.setCode(208);
//                        result.setMsg("登陆用户认证失败");
//                        return result;
//                    }
//                }
//                if (loginUserIdentity.getExamineStatus() == 0 || loginUserIdentity.getExamineStatus() == 1) {
//                    result.setCode(205);
//                    result.setMsg("登陆用户审核状态为未审核或审核中");
//                    return result;
//                }
//
//                if (userGroup == Constant.RIGHTS_EXPERIENCE) {
//                    queryTimes = tytConfigService.getIntValue("credit_experience_time", 3);
//                } else if (userGroup == Constant.RIGHTS_NORMAL) {
//                    queryTimes = tytConfigService.getIntValue("credit_normal_time", 3);
//                } else if (userGroup == Constant.RIGHTS_VIP) {
//                    queryTimes = tytConfigService.getIntValue("credit_vip_time", 500);
//                }
//                if (object >= queryTimes) {
//                    result.setCode(202);
//                    result.setMsg("今日信用查看次数已用完");
//                    return result;
//                }
//                if (loginUserIdentity.getExamineStatus() == 2 && ("10".equals(loginUser.getDeliver_type_one()) || "12".equals(loginUser.getDeliver_type_one()) || "13".equals(loginUser.getDeliver_type_one()))) {
//                    result.setCode(206);
//                    result.setMsg("登陆用户审核通过，身份为待定、无效、未跟踪");
//                    return result;
//                }
//                if (queryUser != null) {
//                    if ("11".equals(queryUser.getDeliver_type_one())) {
//                        object += 1;
//                        RedisUtil.setObject(REDIS_PHONE_QUERY_TIMES_KEY + userId, object, scounds);
//                        result.setCode(203);
//                        result.setMsg("查询的用户无信用记录");
//                        return result;
//                    }
//                    if (loginUser.getCellPhone().equals(queryPhone)) {
//                        object += 1;
//                        RedisUtil.setObject(REDIS_PHONE_QUERY_TIMES_KEY + userId, object, scounds);
//                        result.setCode(201);
//                        result.setMsg("不支持查自己");
//                        return result;
//                    }
//                } else {
//                    object += 1;
//                    RedisUtil.setObject(key, object, scounds);
//                    result.setCode(203);
//                    result.setMsg("查询的用户无信用记录");
//                    return result;
//                }
//            } else {
//                result.setCode(210);
//                result.setMsg("userId不正确");
//                return result;
//            }
//            result.setCode(200);
//            result.setMsg("审核通过");
//            object += 1;
//            int remainingTims = queryTimes - object;
//            // 小于等于3次提示
//            if (remainingTims <= 3) {
//                result.setCode(300);
//                result.setMsg(remainingTims > 0 ? "今日剩余信用查看次数 " + remainingTims + "次" : "今日信用查看次数已用完");
//            }
//            RedisUtil.setObject(key, object, scounds);
//        } catch (Exception e) {
//            e.printStackTrace();
//            result.setCode(500);
//            result.setMsg("服务器错误");
//        }
//        return result;
//    }

    /**
     * 查询结果
     *
     * @param baseParameter 基础参数
     * @param queryPhone    查询手机号
     * @return
     */
    @RequestMapping(value = "/userDetail.action")
    @ResponseBody
    public ResultMsgBean getUserDetail(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "queryPhone", required = true) String queryPhone) {
        ResultMsgBean result = new ResultMsgBean();
        try {
            //判断5930是否已开启
            boolean onOff = tytConfigService.getIntValue(Constant.EQUITY_ON_OFF, 2) == 1;
            if (onOff) {
                result.setCode(201);
                result.setMsg("不支持查询，请升级最新版本");
                return result;
            }

            UserDetailByCellphone userDetail = new UserDetailByCellphone();
            //根据手机号查询对象
            User queryUser = userService.getUserByCellphone(queryPhone);
            if (queryUser != null) {
                userDetail.setUserId(queryUser.getId());
                userDetail.setCellPhone(queryPhone);
                userDetail.setHeadUrl(queryUser.getHeadUrl());
                userDetail.setUserName(queryUser.getUserName());
                userDetail.setProvince(queryUser.getProvince());
                userDetail.setCity(queryUser.getCity());
                userDetail.setUserType(queryUser.getUserType());
                userDetail.setUserClass(queryUser.getUserClass());
                userDetail.setCtime(queryUser.getCtime());
                userDetail.setIdentityType(queryUser.getIdentityType());
                userDetail.setDeliverTypeOne(queryUser.getDeliver_type_one());
                userDetail.setBlackStatus(queryUser.getBlackStatus());
                if (queryUser.getBlackStatus() != null && queryUser.getBlackStatus() == 1) {
                    userDetail.setRiskPrompt(2);
                } else {
                    userDetail.setRiskPrompt(1);
                }
                if (queryUser.getVerifyPhotoSign() != null && queryUser.getVerifyPhotoSign() == 1) {
                    userDetail.setIdentityState(1);
                } else {
                    userDetail.setIdentityState(0);
                }
                TytUserIdentityAuth queryUserIdentity = userIdentityService.getTytUserIdentityAuth(queryUser.getId());
                if (queryUserIdentity != null) {
                    if (queryUserIdentity.getLicenseStatus() != null && queryUserIdentity.getLicenseStatus() == 1) {
                        userDetail.setLicenseState(1);
                    } else {
                        userDetail.setLicenseState(0);
                    }
                    userDetail.setExamineStatus(queryUserIdentity.getExamineStatus());
                } else {
                    userDetail.setLicenseState(0);
                }
                userDetail.setCarNum(carService.getCountByUserId(queryUser.getId()).longValue());
                BigInteger count = blockInfoService.getBlockInfoCount(queryPhone);
                if (count.intValue() > 0) {
                    userDetail.setFraudRecord(1);
                } else {
                    userDetail.setFraudRecord(0);
                }
                ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(queryUser.getId());
                if (userCreditInfo != null) {
                    if (queryUser.getUserClass() != null) {
                        if (queryUser.getUserClass() != null && queryUser.getUserClass() == 1) {
                            userDetail.setDeliverAmount(userCreditInfo.getTransport145Count());
                            userDetail.setVolume(userCreditInfo.getTransportSuccCount());
                        } else if (queryUser.getUserClass() != null && queryUser.getUserClass() == 2) {
                            userDetail.setInfofeeNum(userCreditInfo.getTransportInformationPaymentCount());
                            userDetail.setVipNum(userCreditInfo.getVipGrowthDays());
                        } else {
                            userDetail.setInfofeeNum(0L);
                            userDetail.setVipNum(0L);
                        }
                    } else {
                        userDetail.setDeliverAmount(0L);
                        userDetail.setVolume(0L);
                    }
                } else {
                    userDetail.setInfofeeNum(0L);
                    userDetail.setVipNum(0L);
                }
            } else {
                result.setCode(201);
                result.setMsg("查无此人");
                return result;
            }
            result.setCode(200);
            result.setMsg("查询成功");
            result.setData(userDetail);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }

    /**
     * 油耗成本公式接口
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/oilWear")
    @ResponseBody
    public ResultMsgBean getOilWearList(HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean();
        List<TytCarBaseOilCostBean> oilWearList = null;
        Map<String, Object> dataMap = new HashMap<String, Object>();
        try {
            Object obj = RedisUtil.getObject("plat_oil_wear_list");
            if (obj == null) {
                oilWearList = carBaseOilCostService.getAllList();
                if (oilWearList != null && oilWearList.size() > 0) {
                    RedisUtil.setObject("plat_oil_wear_list", oilWearList, 1800);
                }
            } else {
                oilWearList = (List<TytCarBaseOilCostBean>) obj;
            }
            String dieselUnitPrice = tytConfigService.getStringValue("dieselUnitPrice");
            if (!StringUtils.hasLength(dieselUnitPrice)) {
                logger.error("柴油价格获取失败");
            }
            dataMap.put("oilWearList", oilWearList);
            dataMap.put("dieselUnitPrice", dieselUnitPrice);
            result.setCode(200);
            result.setData(dataMap);
        } catch (Exception e) {
            result.setCode(500);
            e.printStackTrace();
        }
        return result;
    }

//    /**
//     * 旧版代码，确认无异常后删除
//     *
//     * 小工具权益验证接口
//     *
//     * @param userId
//     * @param type   1：信用 2：找货历史(废弃) 3：货源提醒
//     * @return
//     */
//    @RequestMapping(value = "/rightsCheck")
//    @ResponseBody
//    public ResultMsgBean rightsCheck(Long userId, Integer type) {
//        ResultMsgBean result = new ResultMsgBean();
//        try {
//            if (userId == null) {
//                result.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//                result.setMsg("userId 不能为空");
//            } else if (type == null || type > 3 || type < 1) {
//                result.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//                result.setMsg("userId 不能为空,且只能为1,2,3");
//            } else {
//                userSubService.rightCheck(userId, type, result);
//                logger.info("rightsCheck result: " + result.getData());
//            }
//        } catch (Exception e) {
//            result.setCode(500);
//            e.printStackTrace();
//        }
//        return result;
//    }

    /**
     * 小工具权益验证接口
     *
     * @param userId
     * @param type   1：信用 3：货源提醒
     * @return
     */
    @RequestMapping(value = "/V5930/rightsCheck")
    @ResponseBody
    public ResultMsgBean rightsCheckV5930(Long userId, Integer type,@RequestParam(value = "clientSign", required = true) String clientSign) {
        ResultMsgBean result = new ResultMsgBean();
        try {
            if (userId == null) {
                result.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                result.setMsg("userId 不能为空");
            } else if (type == null || (type != 3 && type != 1)) {
                result.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                result.setMsg("type 不能为空,且只能为1,3");
            } else {
                userSubService.rightCheckV5930(userId, type, result);
                logger.info("rightsCheck result: " + result.getData());

                //5930 新权益验证方式
                RightsCheckBean checkBean = (RightsCheckBean) result.getData();
                int canContinue = checkBean.getCanContinue();
                // canContinue == 2 标识未进行身份认证，需要弹出未身份认证的弹窗
                if (canContinue == 2) {
                    TytNoticePopupTempl noticePopupTempl;
                    if (Constant.ClientSignNewEnum.isClientSignEnumcode(Integer.parseInt(clientSign))){
                        noticePopupTempl = noticePopupTemplService.getTemplByType(PopupTypeEnum.未实名认证, null);
                    }else{
                        noticePopupTempl = noticePopupTemplService.getTemplByType(PopupTypeEnum.未认证身份, null);
                    }
                    result.setNoticeData(noticePopupTempl);
                    result.setCode(ReturnCodeConstant.NO_PERMISSION);
                    return result;
                }
                PermissionResult permissionResult = null;
                if (type == 3) { // 新货提醒
                    permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.新货提醒, userId);
                    if (!permissionResult.getUse()) {
                        TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(), null);
                        result.setNoticeData(noticePopupTempl);
                        result.setCode(ReturnCodeConstant.NO_PERMISSION);
                    }
                }

                if (type == 1) { // 查信用
                    permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.查信用, userId);
                    if (!permissionResult.getUse()) {
                        TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(), null);
                        result.setNoticeData(noticePopupTempl);
                        result.setCode(ReturnCodeConstant.NO_PERMISSION);
                    }
                }
            }
        } catch (Exception e) {
            result.setCode(500);
            e.printStackTrace();
        }
        return result;
    }

    
    /**
      * <AUTHOR> Lion
      * @Description 新版车主版查信用
      * @Param [userId, queryPhone, clientVersion]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/6/29 14:07
      */
    @RequestMapping(value = {"/userCarCredit.action","/userCarCredit"})
    @ResponseBody
    public ResultMsgBean getUserCarCredit(@RequestParam(value = "userId", required = true) Long userId,
                                       @RequestParam(value = "queryPhone", required = true) String queryPhone,
                                       @RequestParam(value = "clientVersion", required = true) Integer clientVersion) {

        ResultMsgBean result = new ResultMsgBean();
        try {

            if(clientVersion >= 5930) {
                PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.查信用, userId);
                if (!permissionResult.getUse()) {
                    TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(), null);
                    result.setNoticeData(noticePopupTempl);
                    result.setCode(ReturnCodeConstant.NO_PERMISSION);
                    return result;
                }
            } else {
                TytUserSub userSub = userSubService.getTytUserSubByUserId(userId);
                int[] userGroups = {1, 3, 5};
                if (!ArrayUtils.contains(userGroups, userSub.getUserGroup())) {
                    result.setCode(209);
                    result.setMsg("无查看信用权益");
                    return result;
                }
                PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.查信用, userId);
                if (!permissionResult.getUse() && permissionResult.getPopupTypeEnum().name().equals("信用查询次数已用完")) {
                    result.setCode(202);
                    result.setMsg("今日信用查看次数已用完");
                    return result;
                }
            }

            // 查看用户权益
            User queryUser = userService.getUserByCellphone(queryPhone);
            User loginUser = userService.getByUserId(userId);
            if (loginUser != null) {
                if (queryUser != null) {
                    if ("11".equals(queryUser.getDeliver_type_one())) {
                        result.setCode(203);
                        result.setMsg("查询的用户无信用记录");
                        return result;
                    }
                    //V6310版本 信用分扣除记录查询需求 把原有自己不能查自己的限制放开 需求文档：http://192.168.2.20:8090/pages/viewpage.action?pageId=54008024
                    /*if (loginUser.getCellPhone().equals(queryPhone)) {
                        result.setCode(201);
                        result.setMsg("不支持查自己");
                        return result;
                    }*/
                } else {
                    result.setCode(203);
                    result.setMsg("查询的用户无信用记录");
                    return result;
                }
            } else {
                result.setCode(210);
                result.setMsg("userId不正确");
                return result;
            }

            UserDetailByCellphone userDetail = userService.getUserCredit(userId, queryPhone, queryUser);
            Map<String, Object> userDetailMap = regDetailMap(userDetail);
//            Map<String, Object> userDetailMap = new HashMap<>();
//            userDetailMap.put("carDetail",userDetail);
//            userDetailMap.put("goodsDetail",userDetail);
            result.setCode(200);
            result.setMsg("查询成功");
            result.setData(userDetailMap);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;

    }

    private Map<String, Object> regDetailMap(UserDetailByCellphone userDetail) {
        UserDetailByCellphone carUserDetail = new UserDetailByCellphone();
        BeanUtils.copyProperties(userDetail,carUserDetail);
        Map<String, Object> userDetailMap = new HashMap<>();
        carUserDetail.setUserName(null);
        carUserDetail.setTsRank(null);
        carUserDetail.setTotalScore(null);
        carUserDetail.setInfofeeNum(null);
        carUserDetail.setVipNum(null);
        userDetailMap.put("carDetail",carUserDetail);
        userDetail.setCarUserName(null);
        userDetail.setCarTotalServerScore(null);
        userDetail.setCarServerRankScore(null);
        userDetail.setCarVipGrowthDays(null);
        userDetail.setCarTransportInformationPaymentCount(null);
        userDetailMap.put("goodsDetail",userDetail);
        return userDetailMap;
    }


}
