package com.tyt.smallTools.controller.bean;

import java.io.Serializable;
import java.util.Date;

public class TytCarBaseOilCostBean implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 4244258134136400869L;
	private Long id;
    private Integer cargoLengthBegin; //货物长度开始
    private Float carWeight;    //车辆自重
    private Float weightEnd;//吨位结束
    private Integer perDayCost;//日均成本
    private Float carOilWear;//空车油耗
    private Float oilWear20;//载重<20吨
    private Float oilWear2030;  //载重20-30吨
    private Float oilWear30;//载重>30吨
    private Date mtime;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getCargoLengthBegin() {
		return cargoLengthBegin;
	}
	public void setCargoLengthBegin(Integer cargoLengthBegin) {
		this.cargoLengthBegin = cargoLengthBegin;
	}
	public Float getCarWeight() {
		return carWeight;
	}
	public void setCarWeight(Float carWeight) {
		this.carWeight = carWeight;
	}
	public Float getWeightEnd() {
		return weightEnd;
	}
	public void setWeightEnd(Float weightEnd) {
		this.weightEnd = weightEnd;
	}
	public Integer getPerDayCost() {
		return perDayCost;
	}
	public void setPerDayCost(Integer perDayCost) {
		this.perDayCost = perDayCost;
	}
	public Float getCarOilWear() {
		return carOilWear;
	}
	public void setCarOilWear(Float carOilWear) {
		this.carOilWear = carOilWear;
	}
	public Float getOilWear20() {
		return oilWear20;
	}
	public void setOilWear20(Float oilWear20) {
		this.oilWear20 = oilWear20;
	}
	public Float getOilWear2030() {
		return oilWear2030;
	}
	public void setOilWear2030(Float oilWear2030) {
		this.oilWear2030 = oilWear2030;
	}
	public Float getOilWear30() {
		return oilWear30;
	}
	public void setOilWear30(Float oilWear30) {
		this.oilWear30 = oilWear30;
	}
	public Date getMtime() {
		return mtime;
	}
	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Override
	public String toString() {
		return "TytCarBaseOilCostBean [id=" + id + ", cargoLengthBegin="
				+ cargoLengthBegin + ", carWeight=" + carWeight
				+ ", weightEnd=" + weightEnd + ", perDayCost=" + perDayCost
				+ ", carOilWear=" + carOilWear + ", oilWear20=" + oilWear20
				+ ", oilWear2030=" + oilWear2030 + ", oilWear30=" + oilWear30
				+ ", mtime=" + mtime + "]";
	}
    
}
