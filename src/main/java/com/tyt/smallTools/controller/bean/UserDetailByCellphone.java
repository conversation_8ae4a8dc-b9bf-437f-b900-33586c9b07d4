package com.tyt.smallTools.controller.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

public class UserDetailByCellphone implements Serializable{
	/**
	 *
	 */
	private static final long serialVersionUID = 3488364784970575675L;
	private Long userId; //用户id
	private String userName; //用户名
	private String headUrl; //头像地址
	private String cellPhone; //手机号
	private String province; //省
	private String city; //市
	private Integer userClass;// 1发货方  2车辆方
	private Integer identityType;//注册身份
	private String deliverTypeOne;//销售审核一级身份
	private Integer userType; //是否付费   0试用 1付费
	private Timestamp ctime;//注册时长
	private Integer riskPrompt; //有无风险 1没有  2有
	private Long deliverAmount; //历史发货量
	private Long volume; //历史交易量
	private Long infofeeNum; //信息费交易次数
	private Long vipNum; //会员成长天数
	private Integer blackStatus; //是否在黑名单  0否  1是
	private Integer fraudRecord; //多少次诈骗记录
	private Integer identityState; //身份认证  0无  1有
	private Integer licenseState;//企业营业执照  0无  1有
	private Long carNum;//车辆认证（多少辆）
	private Integer examineStatus;//身份审核状态
	private Integer tsRank;//信用等级
	private BigDecimal totalScore;//信用分
	private Integer carTotalServerScore;//'车方服务分_new'
	private Integer carServerRankScore;//'车方等级_new'
	private Long carVipGrowthDays;//'车方尊享VIP会员天数'
	private Long carTransportInformationPaymentCount;//'车方信息费交易次数'
	private Integer registerDay;//'注册天数'
	private String carUserName; //车方用户名
	private String authNameTea;   // 官方授权昵称

	/**
	 * 车方用户违约次数
	 */
	private Integer carBreakNum;

	/**
	 * 货方用户违约次数
	 */
	private Integer goodsBreakNum;

	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getHeadUrl() {
		return headUrl;
	}
	public void setHeadUrl(String headUrl) {
		this.headUrl = headUrl;
	}
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public Integer getUserClass() {
		return userClass;
	}
	public void setUserClass(Integer userClass) {
		this.userClass = userClass;
	}

	public Integer getIdentityType() {
		return identityType;
	}
	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}
	public String getDeliverTypeOne() {
		return deliverTypeOne;
	}
	public void setDeliverTypeOne(String deliverTypeOne) {
		this.deliverTypeOne = deliverTypeOne;
	}
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	public Integer getUserType() {
		return userType;
	}
	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Integer getRiskPrompt() {
		return riskPrompt;
	}
	public void setRiskPrompt(Integer riskPrompt) {
		this.riskPrompt = riskPrompt;
	}
	public Long getDeliverAmount() {
		return deliverAmount;
	}
	public void setDeliverAmount(Long deliverAmount) {
		this.deliverAmount = deliverAmount;
	}
	public Long getVolume() {
		return volume;
	}
	public void setVolume(Long volume) {
		this.volume = volume;
	}
	public Long getInfofeeNum() {
		return infofeeNum;
	}
	public void setInfofeeNum(Long infofeeNum) {
		this.infofeeNum = infofeeNum;
	}
	public Long getVipNum() {
		return vipNum;
	}
	public void setVipNum(Long vipNum) {
		this.vipNum = vipNum;
	}
	public Integer getBlackStatus() {
		return blackStatus;
	}
	public void setBlackStatus(Integer blackStatus) {
		this.blackStatus = blackStatus;
	}
	public Integer getFraudRecord() {
		return fraudRecord;
	}
	public void setFraudRecord(Integer fraudRecord) {
		this.fraudRecord = fraudRecord;
	}
	public Integer getIdentityState() {
		return identityState;
	}
	public void setIdentityState(Integer identityState) {
		this.identityState = identityState;
	}
	public Integer getLicenseState() {
		return licenseState;
	}
	public void setLicenseState(Integer licenseState) {
		this.licenseState = licenseState;
	}
	public Long getCarNum() {
		return carNum;
	}
	public void setCarNum(Long carNum) {
		this.carNum = carNum;
	}
	public Integer getExamineStatus() {
		return examineStatus;
	}
	public void setExamineStatus(Integer examineStatus) {
		this.examineStatus = examineStatus;
	}
	public Integer getTsRank(){
		return tsRank;
	}
	public void setTsRank(Integer tsRank){
		this.tsRank = tsRank;
	}

	public BigDecimal getTotalScore(){
		return totalScore;
	}
	public void setTotalScore(BigDecimal totalScore){
		this.totalScore = totalScore;
	}

	public Integer getCarTotalServerScore() {
		return carTotalServerScore;
	}

	public void setCarTotalServerScore(Integer carTotalServerScore) {
		this.carTotalServerScore = carTotalServerScore;
	}

	public Integer getCarServerRankScore() {
		return carServerRankScore;
	}

	public void setCarServerRankScore(Integer carServerRankScore) {
		this.carServerRankScore = carServerRankScore;
	}

	public Long getCarVipGrowthDays() {
		return carVipGrowthDays;
	}

	public void setCarVipGrowthDays(Long carVipGrowthDays) {
		this.carVipGrowthDays = carVipGrowthDays;
	}

	public Long getCarTransportInformationPaymentCount() {
		return carTransportInformationPaymentCount;
	}

	public void setCarTransportInformationPaymentCount(Long carTransportInformationPaymentCount) {
		this.carTransportInformationPaymentCount = carTransportInformationPaymentCount;
	}

	public Integer getRegisterDay() {
		return registerDay;
	}

	public void setRegisterDay(Integer registerDay) {
		this.registerDay = registerDay;
	}

	public String getCarUserName() {
		return carUserName;
	}

	public void setCarUserName(String carUserName) {
		this.carUserName = carUserName;
	}

	public String getAuthNameTea() {
		return authNameTea;
	}

	public void setAuthNameTea(String authNameTea) {
		this.authNameTea = authNameTea;
	}

	public Integer getCarBreakNum() {
		return carBreakNum;
	}

	public void setCarBreakNum(Integer carBreakNum) {
		this.carBreakNum = carBreakNum;
	}

	public Integer getGoodsBreakNum() {
		return goodsBreakNum;
	}

	public void setGoodsBreakNum(Integer goodsBreakNum) {
		this.goodsBreakNum = goodsBreakNum;
	}
}
