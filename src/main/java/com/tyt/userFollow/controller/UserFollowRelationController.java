package com.tyt.userFollow.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.userFollow.bean.FollowInfoBean;
import com.tyt.userFollow.bean.FollowedUserBean;
import com.tyt.userFollow.service.UserFollowRelationService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description  用户关注关系接口
 * <AUTHOR>
 * @Date  2019/12/4 9:55
 * @Param 
 * @return 
 **/
@Controller
@RequestMapping("/plat/userFollowRelation/")
public class UserFollowRelationController extends BaseController {

       @Autowired
       private UserFollowRelationService userFollowRelationService;

       /**
        * @Description  更新关注状态接口 1.已关注 2.取消关注
        * <AUTHOR>
        * @Date  2019/12/4 16:02
        * @Param [baseParameter, followUserId, followStatus, request, response]
        * @return com.tyt.model.ResultMsgBean
        **/
       @RequestMapping(value = {"updateFollowStatus","updateFollowStatus.action"}, method = RequestMethod.POST)
       @ResponseBody
       public ResultMsgBean updateFollowStatus(BaseParameter baseParameter,
                                               Long followUserId,Integer followStatus,
                                               HttpServletRequest request, HttpServletResponse response) {
              ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
              try {
                     //货站关注关系实体对象
                     FollowInfoBean followInfoBean = new FollowInfoBean();
                     //获取登录用户Id
                     Long userId = baseParameter.getUserId();
                     if(userId == null) {
                            resultMsgBean.setMsg("登录用户Id不能为空！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            return resultMsgBean;
                     }
                     //关注用户Id(货站Id)
                     if(followUserId == null){
                            resultMsgBean.setMsg("关注用户Id不能为空！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            return resultMsgBean;
                     }
                     //关注状态
                     if(followStatus == null){
                            resultMsgBean.setMsg("关注状态不能为空！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            return resultMsgBean;
                     }
                     if(userId.longValue() == followUserId.longValue()){
                            resultMsgBean.setMsg("非常抱歉,不可以关注自己！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                            return resultMsgBean;
                     }
                     //更新关注状态
                     userFollowRelationService.updateFollowStatus(userId, followUserId, followStatus);
                     //关注状态
                     Integer status = userFollowRelationService.getFollowStatus(userId, followUserId);
                     //关注我的人数量
                     Integer followedUserCount = userFollowRelationService.getFollowedUserCount(followUserId);
                     followInfoBean.setFollowStatus(status);
                     followInfoBean.setFollowedUserCount(followedUserCount);

                     resultMsgBean.setCode(ReturnCodeConstant.OK);
                     resultMsgBean.setMsg("更新关注状态成功！");
                     resultMsgBean.setData(followInfoBean);
              } catch (Exception e) {
                     resultMsgBean.setCode(ResultMsgBean.ERROR);
                     resultMsgBean.setMsg("服务器错误");
                     logger.info("更新关注状态发生错误！"+e.getMessage());
                     e.printStackTrace();
              }
              return resultMsgBean;
       }

       /**
        * @Description  关注我的用户列表接口
        * <AUTHOR>
        * @Date  2019/12/4 17:36
        * @Param [baseParameter, followUserId, queryID, queryActionType]
        * @return com.tyt.model.ResultMsgBean
        **/
       @RequestMapping(value = {"getFollowedUserList","getFollowedUserList.action"}, method = RequestMethod.POST)
       @ResponseBody
       public ResultMsgBean getFollowedUserList(BaseParameter baseParameter,
                                                Long followUserId,
                                                @RequestParam(value = "queryID", defaultValue = "0") Long queryID,
                                                @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType) {
              ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
              try {
                     //根据传入的参数，查询关注我的用户列表
                     Map<String,Object> map = new HashMap<String,Object>();
                     //获取登录用户Id
                     Long userId = baseParameter.getUserId();
                     if(userId == null) {
                            resultMsgBean.setMsg("登录用户Id不能为空！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            return resultMsgBean;
                     }
                     //关注用户Id(货站Id)
                     if(followUserId == null){
                            resultMsgBean.setMsg("关注用户Id不能为空！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            return resultMsgBean;
                     }
                     //检查属性
                     if (checkQueryParameter(queryActionType, queryID, resultMsgBean)) {
                            List<FollowedUserBean> followedUserList = userFollowRelationService.getFollowedUserList(followUserId, queryActionType, queryID);
                            Integer followedUserCount = userFollowRelationService.getFollowedUserCount(followUserId);
                            //关注我的用户列表
                            map.put("followedUserList",followedUserList);
                            //关注我的用户人数
                            map.put("followedUserCount",followedUserCount);
                     }
                     resultMsgBean.setCode(ReturnCodeConstant.OK);
                     resultMsgBean.setMsg("获取关注我的用户列表成功！");
                     resultMsgBean.setData(map);
              } catch (Exception e) {
                     resultMsgBean.setCode(ResultMsgBean.ERROR);
                     resultMsgBean.setMsg("服务器错误");
                     logger.info("获取关注我的用户列表发生错误！"+e.getMessage());
                     e.printStackTrace();
              }
              return resultMsgBean;
       }

       /**
        * @Description  检查查询参数的方法
        * <AUTHOR>
        * @Date  2019/12/4 17:39
        * @Param [queryActionType, queryID, rm]
        * @return boolean
        **/
       private boolean checkQueryParameter(Integer queryActionType, Long queryID, ResultMsgBean rm) {
              if (queryActionType.intValue() < 1 || queryActionType.intValue() > 2) {
                     rm.setMsg("查询类型不正确！");
                     rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                     return false;
              } else if (queryID.longValue() < 0) {
                     rm.setMsg("查询标识错误，最大、最小ID为空！");
                     rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                     return false;
              }
              return true;

       }
}
