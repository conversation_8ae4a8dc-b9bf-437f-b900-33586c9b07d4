package com.tyt.userFollow.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.model.TytUserFollowRelation;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytSourceService;
import com.tyt.user.service.UserService;
import com.tyt.userFollow.bean.FollowedUserBean;
import com.tyt.userFollow.service.UserFollowRelationService;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

/**
 * @Description  用户关注关系服务层实现类
 * <AUTHOR>
 * @Date  2019/12/4 9:50
 * @Param
 * @return
 **/
@Service("userFollowRelationService")
public class UserFollowRelationServiceImpl extends BaseServiceImpl<TytUserFollowRelation, Long> implements UserFollowRelationService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    //服务器图片路径(旧规则)
    public static final String TYT_SERVER_PICTURE_URL_OLD = "tyt_server_picture_url_old";

    @Resource(name = "tytSourceService")
    private TytSourceService tytSourceService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "userFollowRelationDao")
    public void setBaseDao(BaseDao<TytUserFollowRelation, Long> userFollowRelationDao) {
        super.setBaseDao(userFollowRelationDao);
    }

    /**
     * @Description  根据用户Id、关注用户Id获取用户关注记录信息
     * <AUTHOR>
     * @Date  2019/12/4 16:22
     * @Param [userId, followUserId]
     * @return com.tyt.model.TytUserFollowRelation
     **/
    @Override
    public TytUserFollowRelation getUserFollowRelation(Long userId, Long followUserId) {
        String sql = "select id id,user_id userId," +
                "follow_user_id followUserId,follow_status followStatus," +
                "ctime ctime,utime utime " +
                " from tyt_user_follow_relation " +
                " where user_id = :userId and follow_user_id = :followUserId";
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("followUserId", Hibernate.LONG);
        scalarMap.put("followStatus", Hibernate.INTEGER);
        scalarMap.put("ctime",Hibernate.DATE);
        scalarMap.put("utime",Hibernate.DATE);

        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("userId", userId);
        paramsMap.put("followUserId", followUserId);

        TytUserFollowRelation userFollowRelation = this.getBaseDao().queryByMap(sql, paramsMap, TytUserFollowRelation.class, scalarMap);
        return userFollowRelation;
    }

    /**
     * @Description  获取关注我的人数量
     * <AUTHOR>
     * @Date  2019/12/4 12:23
     * @Param [followUserId]
     * @return java.lang.Integer
     **/
    @Override
    public Integer getFollowedUserCount(Long followUserId) {
        Integer followedUserCount = 0;
        String sql = "select count(*) from tyt_user_follow_relation " +
                     " where follow_status = 1 and follow_user_id = ? ";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{followUserId});
        if(count != null && count.intValue() > 0){
            followedUserCount = count.intValue();
        }
        return followedUserCount;
    }

    /**
     * @Description  获取关注状态：1.已关注 2.未关注/取消关注
     * <AUTHOR>
     * @Date  2019/12/4 13:41
     * @Param [userId, followUserId]
     * @return java.lang.Integer
     **/
    @Override
    public Integer getFollowStatus(Long userId, Long followUserId) {
        //关注状态 1.已关注 2.未关注/取消关注
        Integer followStatus = 2;
        //用户关注关系对象
        TytUserFollowRelation userFollowRelation = this.getUserFollowRelation(userId, followUserId);
        if(userFollowRelation != null){
            followStatus = userFollowRelation.getFollowStatus();
        }
        return followStatus;
    }
    
    /**
     * @Description  更新关注状态
     * <AUTHOR>
     * @Date  2019/12/4 16:15
     * @Param [userId, followUserId, followStatus]
     * @return void
     **/
    @Override
    public void updateFollowStatus(Long userId, Long followUserId, Integer followStatus) {
        //用户关注关系记录
        TytUserFollowRelation userFollowRelation = this.getUserFollowRelation(userId, followUserId);
        //如果关注关系存在，则进行更新
        if(userFollowRelation != null){
            userFollowRelation.setFollowStatus(followStatus);
            userFollowRelation.setUtime(new Date());
            this.getBaseDao().update(userFollowRelation);
        }else{ //不存在，则进行插入
            userFollowRelation = new TytUserFollowRelation();
            userFollowRelation.setUserId(userId);
            userFollowRelation.setFollowUserId(followUserId);
            userFollowRelation.setFollowStatus(followStatus);
            userFollowRelation.setCtime(new Date());
            userFollowRelation.setUtime(new Date());
            this.getBaseDao().insert(userFollowRelation);
        }
    }

    /**
     * @Description  获取关注我的用户列表
     * <AUTHOR>
     * @Date  2019/12/4 17:43
     * @Param [followUserId]
     * @return java.util.List<com.tyt.model.TytUserFollowRelation>
     **/
    @Override
    public List<FollowedUserBean> getFollowedUserList(Long followUserId, int queryActionType, long queryID) {

        List<Object> list = new ArrayList<Object>();
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");
        StringBuffer sql = new StringBuffer("select r.id id,r.user_id userId," +
                        " r.follow_user_id followUserId,r.follow_status followStatus," +
                        " r.ctime ctime,r.utime utime," +
                        " u.user_name userName,u.head_url headUrl," +
                        " a.identity_auth identityAuth,a.car_auth carAuth," +
                        " a.car_vip_label carVipLabel,a.good_vip_label goodVipLabel " +
                        " from tyt_user_follow_relation r " +
                        " left join tyt_user u on r.user_id = u.id " +
                        " left join tyt_user_archives a on r.user_id = a.user_id " +
                        " where 1=1 and r.follow_status = 1 ");
        //关注状态 1.已关注
        sql.append(" and r.follow_user_id = ? ");
        list.add(followUserId);

        if (queryActionType == 2) {
            sql.append(" and r.id < ?");
            list.add(queryID);
        }
        sql.append(" order by r.id desc ");

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("followUserId", Hibernate.LONG);
        scalarMap.put("followStatus", Hibernate.INTEGER);
        scalarMap.put("ctime",Hibernate.DATE);
        scalarMap.put("utime",Hibernate.DATE);

        scalarMap.put("userName",Hibernate.STRING);
        scalarMap.put("headUrl",Hibernate.STRING);
        scalarMap.put("identityAuth",Hibernate.SHORT);
        scalarMap.put("carAuth",Hibernate.SHORT);
        scalarMap.put("carVipLabel",Hibernate.STRING);
        scalarMap.put("goodVipLabel",Hibernate.STRING);

        //服务器图片路径(旧规则)
        String tytServerPictureUrlOld = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_OLD);

        //关注我的用户列表对象
        List<FollowedUserBean> followedUserBeanList =
             this.getBaseDao().search(sql.toString(), scalarMap, FollowedUserBean.class, list.toArray(), 1, pageSize);
        if(followedUserBeanList != null && followedUserBeanList.size() > 0){
            for (FollowedUserBean followedUser : followedUserBeanList) {
                //用户头像Url
                String headUrl = followedUser.getHeadUrl();
                if(StringUtils.isNotBlank(headUrl)){
                    followedUser.setHeadUrl(tytServerPictureUrlOld + headUrl);
                }
            }
        }
        return followedUserBeanList;
    }
}
