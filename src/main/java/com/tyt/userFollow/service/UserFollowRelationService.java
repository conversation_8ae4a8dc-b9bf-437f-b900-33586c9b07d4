package com.tyt.userFollow.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserFollowRelation;
import com.tyt.userFollow.bean.FollowInfoBean;
import com.tyt.userFollow.bean.FollowedUserBean;

import java.util.List;

/**
 * @Description  用户关注关系服务层
 * <AUTHOR>
 * @Date  2019/12/4 9:45
 * @Param 
 * @return 
 **/
public interface UserFollowRelationService extends BaseService<TytUserFollowRelation, Long> {
       
       /**
        * @Description  根据用户Id、关注用户Id获取用户关注记录信息
        * <AUTHOR>
        * @Date  2019/12/4 16:20
        * @Param [userId, followUserId]
        * @return com.tyt.model.TytUserFollowRelation
        **/
       public TytUserFollowRelation getUserFollowRelation(Long userId,Long followUserId);
       /**
        * @Description  获取关注我的人数量
        * <AUTHOR>
        * @Date  2019/12/4 12:13
        * @Param [followUserId]
        * @return java.lang.Integer
        **/
       public Integer getFollowedUserCount(Long followUserId);
       
       /**
        * @Description  获取关注状态：1.已关注 2.未关注/取消关注
        * <AUTHOR>
        * @Date  2019/12/4 13:38
        * @Param [userId, followUserId]
        * @return java.lang.Integer
        **/
       public Integer getFollowStatus(Long userId,Long followUserId);
        
       /**
        * @Description  更新关注状态
        * <AUTHOR>
        * @Date  2019/12/4 16:13
        * @Param [userId, followUserId, followStatus]
        * @return void
        **/
       public void updateFollowStatus(Long userId, Long followUserId, Integer followStatus);

       /**
        * @Description  获取关注我的用户列表
        * <AUTHOR>
        * @Date  2019/12/4 17:42
        * @Param [followUserId]
        * @return java.util.List<com.tyt.model.TytUserFollowRelation>
        **/
       public List<FollowedUserBean> getFollowedUserList(Long followUserId, int queryActionType, long queryID);
}
