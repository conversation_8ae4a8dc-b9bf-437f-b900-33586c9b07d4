package com.tyt.userFollow.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUserFollowRelation;
import com.tyt.userFollow.dao.UserFollowRelationDao;
import org.springframework.stereotype.Repository;

/**
 * @Description  用户关注关系数据层实现类
 * <AUTHOR>
 * @Date  2019/12/4 9:43
 * @Param 
 * @return 
 **/
@Repository("userFollowRelationDao")
public class UserFollowRelationDaoImpl extends BaseDaoImpl<TytUserFollowRelation, Long>  implements UserFollowRelationDao {

    public UserFollowRelationDaoImpl() {
        this.setEntityClass(TytUserFollowRelation.class);
    }

}
