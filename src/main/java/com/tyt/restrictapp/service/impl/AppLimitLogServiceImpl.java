package com.tyt.restrictapp.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import com.tyt.plat.mapper.base.RequestLogMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytAppLimitLog;
import com.tyt.restrictapp.service.AppLimitLogService;

@Service("appLimitLogService")
public class AppLimitLogServiceImpl extends BaseServiceImpl<TytAppLimitLog, Long> implements AppLimitLogService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "appLimitLogDao")
	public void setBaseDao(BaseDao<TytAppLimitLog, Long> appLimitLogDao) {
		super.setBaseDao(appLimitLogDao);
	}

	@Autowired
	private RequestLogMapper requestLogMapper;

	@Override
	public void addRestrictApp(BaseParameter baseParameter, String restrictId) {
		if (StringUtils.isNotEmpty(restrictId)) {
			String[] restrictIdArr = restrictId.split("-");
			for (int i = 0; i < restrictIdArr.length; i++) {
				TytAppLimitLog appLimitLog = new TytAppLimitLog();
				if (baseParameter.getClientSign() != null) {
					appLimitLog.setClientSign(Byte.valueOf(baseParameter.getClientSign()));
				}
				appLimitLog.setClientVersion(baseParameter.getClientVersion());
				appLimitLog.setCtime(new Date());
				if (restrictId != null) {
					appLimitLog.setRestrictAppId(Long.valueOf(restrictIdArr[i]));
				}
				appLimitLog.setRestrictUserid(baseParameter.getUserId());
				appLimitLog.setUtime(new Date());
				add(appLimitLog);
			}
		}
	}

	@Override
	@Async
	public void addRequestLog(Long goodsId, Long userId, Integer type) {
		if (goodsId == null || userId == null || type == null || (type != 1 && type != 2)) {
			return;
		}
		requestLogMapper.addRequestLog(goodsId, userId, type);
	}
}
