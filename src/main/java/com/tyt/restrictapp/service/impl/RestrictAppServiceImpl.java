package com.tyt.restrictapp.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.PageBean;
import com.tyt.model.TytRestrictApp;
import com.tyt.restrictapp.bean.RestrictAppResBean;
import com.tyt.restrictapp.service.RestrictAppService;

@Service("restrictAppService")
public class RestrictAppServiceImpl extends BaseServiceImpl<TytRestrictApp, Long> implements RestrictAppService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "restrictAppDao")
	public void setBaseDao(BaseDao<TytRestrictApp, Long> restrictAppDao) {
		super.setBaseDao(restrictAppDao);
	}

	@Override
	public List<RestrictAppResBean> getRestrictList(int restrictType) throws IllegalAccessException, InvocationTargetException {
		List<RestrictAppResBean> result = new ArrayList<RestrictAppResBean>();
		List<TytRestrictApp> restrictApps = getList("clientType=2 and status<3", new PageBean());;
		if (restrictApps != null && restrictApps.size() > 0) {
			RestrictAppResBean resBean;
			TytRestrictApp curRestrictApp;
			for (int i = 0; i < restrictApps.size(); i++) {
				resBean = new RestrictAppResBean();
				curRestrictApp = restrictApps.get(i);
				BeanUtils.copyProperties(resBean, curRestrictApp);
				result.add(resBean);
			}
		}
		return result;
	}
}
