package com.tyt.restrictapp.service.impl;

import java.util.List;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytRestrictUser;
import com.tyt.restrictapp.service.RestrictUserService;

@Service("restrictUserService")
public class RestrictUserServiceImpl extends BaseServiceImpl<TytRestrictUser, Long> implements RestrictUserService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "restrictUserDao")
	public void setBaseDao(BaseDao<TytRestrictUser, Long> restrictUserDao) {
		super.setBaseDao(restrictUserDao);
	}

	@Override
	public TytRestrictUser getByUserId(Long userId) {
		List<TytRestrictUser> restrictUsers = this.getBaseDao().find("from TytRestrictUser where userId=? and status=?", userId, (byte)1);
		return (restrictUsers!= null && restrictUsers.size() == 1) ? restrictUsers.get(0) : null;
	}
}
