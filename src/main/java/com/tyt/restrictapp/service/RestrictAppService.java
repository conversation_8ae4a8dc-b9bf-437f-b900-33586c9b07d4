package com.tyt.restrictapp.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytRestrictApp;
import com.tyt.restrictapp.bean.RestrictAppResBean;

public interface RestrictAppService extends BaseService<TytRestrictApp, Long> {

	List<RestrictAppResBean> getRestrictList(int restrictType) throws IllegalAccessException, InvocationTargetException;
}
