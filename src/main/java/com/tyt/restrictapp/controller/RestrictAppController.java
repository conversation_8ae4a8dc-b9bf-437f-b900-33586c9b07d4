package com.tyt.restrictapp.controller;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytRestrictUser;
import com.tyt.restrictapp.bean.RestrictAppResBean;
import com.tyt.restrictapp.service.AppLimitLogService;
import com.tyt.restrictapp.service.RestrictAppService;
import com.tyt.restrictapp.service.RestrictUserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.XXTea;

@Controller
@RequestMapping("/plat/restrict")
public class RestrictAppController extends BaseController {
	
	@Resource(name = "restrictUserService")
	private RestrictUserService restrictUserService;
	@Resource(name = "restrictAppService")
	private RestrictAppService restrictAppService;
	@Resource(name = "appLimitLogService")
	private AppLimitLogService appLimitLogService;

	/**
	 * 获取限制APP列表
	 * 
	 * @param baseParameter
	 * @return
	 */
	@RequestMapping("/getRestrictApp")
	@ResponseBody
	public ResultMsgBean getRestrictApp(BaseParameter baseParameter) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 必填项验证
			if (baseParameter.getUserId() == null) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
			} else {
				// 查看是否对当前用户限制
				TytRestrictUser restrictUser = restrictUserService.getByUserId(baseParameter.getUserId());
				List<RestrictAppResBean> restrictApps = new ArrayList<RestrictAppResBean>();
				// 有限制则查询限制软件信息
				if (restrictUser != null) {
					restrictApps = restrictAppService.getRestrictList(Constant.RESTRICT_TYPE_ANDROID);
				}
				String restrictAppsJson = "";
				if (restrictApps.size() > 0) {
					restrictAppsJson = JSON.toJSONString(restrictApps);
					restrictAppsJson = XXTea.Encrypt(restrictAppsJson,
								AppConfig.getProperty("tyt.xxtea.key"));
				}
				rm.setData(restrictAppsJson);
				rm.setMsg("查询成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
	/**
	 * 
	 * @param baseParameter
	 * @param restrictId
	 * @return
	 */
	@RequestMapping("/uploadRestrict")
	@ResponseBody
	public ResultMsgBean uploadRestrict(BaseParameter baseParameter, String restrictId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			appLimitLogService.addRestrictApp(baseParameter, restrictId);
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
