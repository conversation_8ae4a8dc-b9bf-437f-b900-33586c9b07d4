package com.tyt.restrictapp.bean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 
 * <AUTHOR>
 * @date 2018年6月26日下午5:09:45
 * @description
 */
public class RestrictAppResBean {

	private Long id;
	private String androidAppid;
	private String androidAppname;
	private String androidNotify;
	@JsonProperty(value = "useStatus")
	private Byte status;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getAndroidAppid() {
		return androidAppid;
	}

	public void setAndroidAppid(String androidAppid) {
		this.androidAppid = androidAppid;
	}

	public String getAndroidAppname() {
		return androidAppname;
	}

	public void setAndroidAppname(String androidAppname) {
		this.androidAppname = androidAppname;
	}

	public String getAndroidNotify() {
		return androidNotify;
	}

	public void setAndroidNotify(String androidNotify) {
		this.androidNotify = androidNotify;
	}

	public Byte getStatus() {
		return status;
	}

	public void setStatus(Byte status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
