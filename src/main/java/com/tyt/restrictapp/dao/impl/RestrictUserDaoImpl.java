package com.tyt.restrictapp.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytRestrictUser;
import com.tyt.restrictapp.dao.RestrictUserDao;

@Repository("restrictUserDao")
public class RestrictUserDaoImpl extends BaseDaoImpl<TytRestrictUser, Long> implements RestrictUserDao {

	public RestrictUserDaoImpl() {
		this.setEntityClass(TytRestrictUser.class);
	}
}
