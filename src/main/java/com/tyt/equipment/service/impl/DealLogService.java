package com.tyt.equipment.service.impl;



import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;
import java.util.ResourceBundle;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.cache.CacheService;
import com.tyt.equipment.service.IDealLogService;
import com.tyt.model.TytVersionNew;
import com.tyt.model.User;
import com.tyt.user.service.TytVersionNewService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;

/*import kafka.javaapi.producer.Producer;
import kafka.producer.KeyedMessage;
import kafka.producer.ProducerConfig;
import kafka.serializer.StringEncoder;*/



/**  
 * @Title: NeedWorkmanService.java
 * @Package com.tyt.service.equipment.impl
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年6月20日
 */
@SuppressWarnings("deprecation")
@Service
public class DealLogService implements IDealLogService{

	
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	@Resource(name = "userService")
	private UserService userService;
	
	@Resource(name="tytVersionNewService")
	TytVersionNewService tytVersionNewService;
	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	

	@Override
	@Async(value="mqExecutor")
	public void   productLog(HttpServletRequest request, HttpServletResponse response) {
		JSONObject jsonObject = new JSONObject();
	    try {
	/*    	String LOG = getString("LOG");
			if(!StringUtils.equals("1", LOG)){
				return ;
			}*/
	    	HttpSession  session =  request.getSession();
	    	String sessionID = "";
	    	if(session != null){
	    		sessionID = session.getId();
	    	}
			
	    	
			
	        String ip = "";
	        try{
	        	ip=request.getRemoteAddr();
		        ip = DealLogService.getIpAddress(request);
	        }catch(Exception e){logger.info("===deallog==="+e.getMessage());}
	        
	        String webBrower = request.getHeader("User-Agent");
	        String url = request.getRequestURI();
	        String contextPath = request.getContextPath() + "/";
			Map<String, String> reqMap = parseRequestParams(request);
	
	        jsonObject.put("sessionID", sessionID);
	        jsonObject.put("ip", ip);
	        jsonObject.put("webBrower", webBrower);
	        jsonObject.put("url", url);
	        jsonObject.put("args",JSON.toJSONString(reqMap));
	
	    	String clientVersion=reqMap.get("clientVersion");
	    	String clientSign=reqMap.get("clientSign");
	    	int clientSignInt = clientSign!=null?Integer.parseInt(clientSign):0;
	    	String osVersion=reqMap.get("osVersion");
	    	String clientId=reqMap.get("clientId");

		    /*获取相应的版本信息*/
			//TytVersionNew versionNew=tytVersionNewService.getByClient(clientSignInt, clientVersion, 1);
			//String version = versionNew.getVersion();
	        jsonObject.put("clientSign",clientSign);
	        jsonObject.put("osVersion",osVersion);
	        jsonObject.put("clientVersion",clientVersion);
	        jsonObject.put("clientId",clientId);
	        jsonObject.put("channelCode","");
	        jsonObject.put("certificateType","");
	        jsonObject.put("Log_type","2");
	        jsonObject.put("Log_type_name","plat");
	        
	        
	        
	    	String userId=reqMap.get("userId");

			String cellPhone = getMemcache(request);

	        jsonObject.put("cellPhone", cellPhone);
	        jsonObject.put("user_name", "");
	        jsonObject.put("ticket","");
	        jsonObject.put("user_id",userId);
	        jsonObject.put("plat","");

	       /* 
			User user = getLoginUser(request);
			if(user!=null ){			
		        jsonObject.put("user_id", user.getId());
		        jsonObject.put("user_name", user.getUserName());
		        jsonObject.put("user_login_lasttime", user.getLastTime());
		        jsonObject.put("user_phone", user.getCellPhone());
			} else {
		        jsonObject.put("user_id", "");
		        jsonObject.put("user_name", "");
		        jsonObject.put("user_login_lasttime", "");
		        jsonObject.put("user_phone", "");
			}
*/
			jsonObject.put("current_time", TimeUtil.formatDateTime(new Date()));
           
			String data = jsonObject.toJSONString();
        	//logger.info("this is log :"+data);

         //   producer.send(new KeyedMessage<String, String>(TOPIC,data));
          producer.send(new ProducerRecord<Integer, String>(topic,data)).get();

        }catch(Exception e){
        	//e.printStackTrace();
        	logger.info("this is exception :"+e.getMessage());
        } finally{
        	//producer.close();
        }
		
	
	}
	
	public final static String getIpAddress(HttpServletRequest request)  {  
        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址  
  
        String ip = request.getHeader("X-Forwarded-For");  
		try{
		  
		     if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		                ip = request.getHeader("Proxy-Client-IP");  
		                
		            }  
		            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		                ip = request.getHeader("WL-Proxy-Client-IP");  
		             
		            }  
		            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		                ip = request.getHeader("HTTP_CLIENT_IP");  
		            }  
		            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		                ip = request.getHeader("HTTP_X_FORWARDED_FOR");   
		            }  
		            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		              
		            	ip = request.getRemoteHost();//getRemoteAddr(); 
		            	String x ="df";
		            }  
		        } else if (ip.length() > 15) {  
		            String[] ips = ip.split(",");  
		            for (int index = 0; index < ips.length; index++) {  
		                String strIp = (String) ips[index];  
		                if (!("unknown".equalsIgnoreCase(strIp))) {  
		                    ip = strIp;  
		                    break;  
		                }  
		            }  
		        }  
		 
		} catch (Exception e) {
			// TODO Auto-generated catch block
			return ip;
		}
		return ip;  
	}  

	
	protected Map<String, String> parseRequestParams(HttpServletRequest request) {
		Map<String, String[]> requestParams = request.getParameterMap();
		Map<String, String> params = new HashMap<String, String>();
		for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i].trim() : valueStr + values[i].trim() + ",";
			}
			// 乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
			// valueStr = new String(valueStr.getBytes("iso-8859-1"),"UTF-8");
			params.put(name, valueStr);
		}
		return params;
	}
	public String getMemcache(HttpServletRequest request) {
	   	HttpSession  session =  request.getSession();
    	String sessionID = "";
    	if(session != null){
    		sessionID = session.getId();
    		return (String) cacheService.getObject(request.getSession().getId());
    	} else {
    		return "";
    	}
		
	}


    private static KafkaProducer<Integer, String> producer=null;
    private static String topic= "tyt_log";
    public static final ResourceBundle p=ResourceBundle.getBundle("server_url");  

	static {
		
		Properties props = new Properties();
	    props.put("bootstrap.servers", getString("LOG_SERVERS"));
	    props.put("client.id", getString("LOG_CLIENT"));
	    props.put("key.serializer", "org.apache.kafka.common.serialization.IntegerSerializer");
	    props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
	    topic = getString("LOG_TOPIC");
	    String LOG = getString("LOG");
		if(StringUtils.equals("1", LOG)){
		    producer = new KafkaProducer<Integer, String>(props);
		}
	}
    public static String getString(String key){
    	Object obj = p.getObject(key);// p.get(key);
    	String value = obj!=null?obj.toString():"";
    	return value;
    }
}
