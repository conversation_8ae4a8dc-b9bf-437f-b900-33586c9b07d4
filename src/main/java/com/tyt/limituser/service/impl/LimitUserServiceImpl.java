package com.tyt.limituser.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.limituser.service.ILimitUserService;
import com.tyt.model.LimitUser;
import com.tyt.limituser.model.UserLimitQueryBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("limitUserService")
public class LimitUserServiceImpl extends BaseServiceImpl<LimitUser, Integer> implements ILimitUserService {
    @Resource(name = "limitUserDao")
    @Override
    public void setBaseDao(BaseDao<LimitUser, Integer> baseDao) {
        super.setBaseDao(baseDao);
    }


    @Override
    public String getListCondition(UserLimitQueryBean info) {
        return " 1=1 ";
    }


    @Override
    public boolean isLimitUser(String userId) {
        /**
         * 新增加限制：对于限制用户(tyt_limit_user表)禁止拨打电话，提示引导付费
         */
        String sql = "select * from tyt_user_limit where user_id = :userID";
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("userID", userId);
        List<LimitUser> limitUserList = this.getBaseDao().searchByName(sql, paramsMap);

        return limitUserList != null && limitUserList.size() > 0;
    }
}
