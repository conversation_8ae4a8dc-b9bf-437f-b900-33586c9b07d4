package com.tyt.limituser.service.impl;

import com.tyt.limituser.service.ExposureBlockService;
import com.tyt.plat.entity.base.TytExposureBlock;
import com.tyt.plat.mapper.base.TytExposureBlockMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 曝光卡限制服务实现类
 *
 * <AUTHOR>
 * @since 2024-9-1 11:04:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExposureBlockServiceImpl implements ExposureBlockService {

    private final TytExposureBlockMapper tytExposureBlockMapper;

    @Override
    public TytExposureBlock selectByUserId(Long userId) {
        return tytExposureBlockMapper.selectByUserId(userId);
    }
}
