package com.tyt.apiDataUserCreditInfo.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.apiDataUserCreditInfo.dao.ApiDataUserCreditInfoDao;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.CardPrefixBankInfo;

@Repository("apiDataUserCreditInfoDao")
public class ApiDataUserCreditInfoDaoImpl extends BaseDaoImpl<ApiDataUserCreditInfoTwo, Long> implements ApiDataUserCreditInfoDao{
	public ApiDataUserCreditInfoDaoImpl() {
		this.setEntityClass(ApiDataUserCreditInfoTwo.class);
	}
}
