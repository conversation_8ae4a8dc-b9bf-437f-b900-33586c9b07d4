package com.tyt.apiDataUserCreditInfo.service.impl;

import javax.annotation.Resource;

import com.tyt.model.DwsNewIdentityTwoData;
import com.tyt.plat.service.user.ApiUserCarryPointService;
import com.tyt.plat.vo.user.UserCarryPointTotalVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.Hibernate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.ApiDataUserCreditInfoTwo;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("apiDataUserCreditInfoService")
public class ApiDataUserCreditInfoServiceImpl extends BaseServiceImpl<ApiDataUserCreditInfoTwo, Long> implements ApiDataUserCreditInfoService{

	@Autowired
	private ApiUserCarryPointService apiUserCarryPointService;

	@Resource(name = "apiDataUserCreditInfoDao")
	public void setBaseDao(BaseDao<ApiDataUserCreditInfoTwo, Long> apiDataUserCreditInfoDao) {
		super.setBaseDao(apiDataUserCreditInfoDao);
	}

	@Override
	public ApiDataUserCreditInfoTwo getById(Long userId) {

		ApiDataUserCreditInfoTwo creditInfoTwo = this.getBaseDao().findById(userId);

		if(creditInfoTwo == null){
			return null;
		}
		ApiDataUserCreditInfoTwo creditResult = null;
		try {
			creditResult = new ApiDataUserCreditInfoTwo();
			BeanUtils.copyProperties(creditInfoTwo, creditResult);

			UserCarryPointTotalVo pointTotalVo = apiUserCarryPointService.getUserCarryPointTotal(userId);

			if(pointTotalVo != null) {
				Integer carLevel = pointTotalVo.getCarLevel();
				BigDecimal carTotalScore = pointTotalVo.getCarTotalScore();

				creditResult.setCarCreditRankLevel(carLevel + "");
				creditResult.setCarCreditScore(carTotalScore);
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return creditResult;
	}

	@Override
	public DwsNewIdentityTwoData getIdentityByUserId(Long userId){
		String sql = "SELECT user_id userId, type type FROM `tyt_recommend`.`dws_new_identity_two_data` WHERE user_id = :userId";
		Map<String, Object> params = new HashMap<>();
		params.put("userId", userId);
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("type", Hibernate.INTEGER);
		List<DwsNewIdentityTwoData> identityTwoDataList = this.getBaseDao().search(sql, scalarMap, DwsNewIdentityTwoData.class, params);
		if (!CollectionUtils.isEmpty(identityTwoDataList)) {
			return identityTwoDataList.get(0);
		}
		return null;

	}
}
