package com.tyt.apiDataUserCreditInfo.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.DwsNewIdentityTwoData;

public interface ApiDataUserCreditInfoService extends BaseService<ApiDataUserCreditInfoTwo, Long>{

    /**
     * 旧表车方运点分字段废弃，单独处理.
     * @param userId userId
     * @return ApiDataUserCreditInfoTwo
     */
    ApiDataUserCreditInfoTwo getById(Long userId);

    DwsNewIdentityTwoData getIdentityByUserId(Long userId);
}
