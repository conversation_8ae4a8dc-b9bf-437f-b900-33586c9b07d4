package com.tyt.insurance.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.tyt.base.controller.BaseController;
import com.tyt.insurance.service.InsuranceService;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/insurance")
public class InsuranceController extends BaseController {

	@Resource(name = "insuranceService")
	private InsuranceService insuranceService;

	/**
	 * 保存用户参加保险活动的信息
	 * 
	 * @param cooperation
	 * @return
	 */
	@RequestMapping("/add")
	@ResponseBody
	public void add(String userId, String contactPerson,String clientVersion, String contactPhone, HttpServletRequest request, HttpServletResponse response) {
		logger.info("add insurance message, the param is userId: " + userId + ", contactPerson: " + contactPerson + ", contactPhone: " + contactPhone);
		ResultMsgBean rm = new ResultMsgBean();
		try {
			/*
			 * 根据userId是否为空判断当前操作用户是否登录，如果为空则为非登录用户，如果是非空则是登录用户
			 */
			if (StringUtils.isEmpty(userId)) {
				insuranceService.addNoLoginInsurance(contactPerson, contactPhone,clientVersion);
			} else {
				insuranceService.addLoginInsurance(userId);
			}
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("保存成功!");
			printJSON(request, response, rm);
			return;
		} catch (Exception e) {
			logger.error("add insurance message failed, the error message is: " + e);
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("保存失败,请稍后重试!");
			printJSON(request, response, rm);
			return;
		}
	}
	
	@RequestMapping("/saveClickPic")
	public String savePicClick(Long userId,String clientVersion){
		try {
			if(userId!=null&&userId.longValue()>0){
				/*用户ID不为空，1往预约保险表添加数据 2跳转到登陆预约保险页面*/
				insuranceService.addClickInsurance(userId, clientVersion);
				return "redirect:http://www.teyuntong.com/app/yuyuebaoxian/indexLogin.html?userId="+userId+"?clientVersion="+clientVersion;
			}
		} catch (Exception e) {
			e.printStackTrace();
			if(userId!=null&&userId.longValue()>0){
				return "redirect:http://www.teyuntong.com/app/yuyuebaoxian/indexLogin.html?userId="+userId+"?clientVersion="+clientVersion;
			}
		}
		/*用户ID为空直接跳转到未登录预约页面*/
		return "redirect:http://www.teyuntong.com/app/yuyuebaoxian/indexNoLogin.html?clientVersion="+clientVersion;
		
	}
}
