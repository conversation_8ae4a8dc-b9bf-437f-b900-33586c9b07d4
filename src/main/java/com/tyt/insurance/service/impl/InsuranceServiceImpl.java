package com.tyt.insurance.service.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.insurance.service.InsuranceService;
import com.tyt.model.TytConfig;
import com.tyt.model.TytCooperationInfo;
import com.tyt.model.User;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.user.service.UserTelService;

@Service("insuranceService")
public class InsuranceServiceImpl extends
		BaseServiceImpl<TytCooperationInfo, Long> implements InsuranceService {

	@Resource(name = "insuranceDao")
	public void setBaseDao(BaseDao<TytCooperationInfo, Long> insuranceDao) {
		super.setBaseDao(insuranceDao);
	}

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "userTelService")
	private UserTelService userTelService;

	@Override
	public void addNoLoginInsurance(String contactPerson, String contactPhone,
			String clientVersion) {
		/*
		 * 判断该手机号是否被预约过，预约过更新，没有则添加
		 */
		String sql = "SELECT COUNT(*) FROM tyt_booking_insurance tbi WHERE tbi.`telephones` LIKE ? AND tbi.`user_id` IS NULL";
		BigInteger count = this.getBaseDao().query(sql,
				new Object[] { "%" + contactPhone + "%" });
		if (count.intValue() == 0) {
			sql = "INSERT INTO `tyt`.`tyt_booking_insurance` (`source`, `deal_status`, `linkman`, `sales_man`, `telephones`, `create_time`, `update_time`,`client_version`) VALUES (?, ?, ?, ?, ?, ?, ?,?)";
			this.getBaseDao()
					.executeUpdateSql(
							sql,
							new Object[] { 2, 1, contactPerson,
									getDefaultSalesman(), contactPhone,
									new Date(), new Date(), clientVersion });
		} else if (count.intValue() == 1) {
			sql = "UPDATE tyt_booking_insurance tbi SET tbi.`linkman`=?, tbi.`update_time`=NOW(), tbi.`create_time`=NOW() WHERE tbi.`telephones` LIKE ? AND tbi.`user_id` IS NULL";
			this.executeUpdateSql(sql, new Object[] { contactPerson,
					"%" + contactPhone + "%" });
		}
	}

	@Override
	public void addLoginInsurance(String userId) {
		String sql = "UPDATE tyt_booking_insurance tbi SET tbi.`source`=?, tbi.`update_time`=NOW(), tbi.`create_time`=NOW() WHERE tbi.`user_id`=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { 2, userId });
	}

	@Override
	public void addClickInsurance(Long userId, String clientVersion)
			throws Exception {
		String sql = "SELECT COUNT(*) FROM tyt_booking_insurance tbi WHERE  tbi.`user_id`=?";
		BigInteger count = this.getBaseDao()
				.query(sql, new Object[] { userId });
		if (count != null && count.intValue() > 0) {
			sql = "UPDATE tyt_booking_insurance tbi SET tbi.`update_time`=NOW() ,tbi.`create_time`=NOW() WHERE  tbi.`user_id`=?";
			this.executeUpdateSql(sql, new Object[] { userId });
		} else {
			/* 获取用户信息 */
			User user = userService.getByUserId(userId);
			String registerTel = user.getCellPhone();
			String linkman = user.getTrueName();
			String salesMan = getDefaultSalesman();
			/* 获取用户的最后三个有效联系电话 */
			String telsStr = null;
			List<String> tels = userTelService.getLatestTels(userId, 3);
			if (tels != null && tels.size() > 0)
				telsStr = org.apache.commons.lang.StringUtils.join(
						tels.toArray(), ",");// 转化为str,str2,str3的形式
			/* 添加到tyt_booking_insurance表 */
			sql = "INSERT INTO `tyt`.`tyt_booking_insurance` (`user_id`,`source`, `deal_status`, `linkman`,`register_tel`, `sales_man`, `telephones`, `create_time`, `update_time`,`client_version`) VALUES (?,?, ?, ?,?, ?, ?, ?, ?,?)";
			this.getBaseDao().executeUpdateSql(
					sql,
					new Object[] { userId, 1, 1, linkman, registerTel,
							salesMan, telsStr, new Date(), new Date(),
							clientVersion });
		}
	}

	/**
	 * 默认业务员从tyt_config表获取，对应的name=turnPicBookingInsuranceSalesman
	 */
	private String getDefaultSalesman() {
		String salesMan = null;
		TytConfig salesmanConfig = tytConfigService
				.getValue("turnPicBookingInsuranceSalesman");
		if (salesmanConfig != null) {
			salesMan = salesmanConfig.getValue();
		}
		return salesMan;
	}
}
