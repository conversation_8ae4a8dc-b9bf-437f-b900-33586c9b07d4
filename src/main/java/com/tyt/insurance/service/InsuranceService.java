package com.tyt.insurance.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytCooperationInfo;

public interface InsuranceService extends BaseService<TytCooperationInfo, Long> {

	/**
	 * 根据手机号判断该手机号是否在非登录状态预约过，如果预约过则更新原有数据，否则添加数据
	 * 
	 * @param contactPerson
	 * @param contactPhone
	 */
	void addNoLoginInsurance(String contactPerson, String contactPhone,String clientVersion);

	/**
	 * 
	 * @param userId
	 */
	void addLoginInsurance(String userId);
	/**
	 * 点击轮播图，记录登陆用户信息
	 * @param userId用户ID
	 * @param clientVersion客户端版本号
	 * @throws Exception 
	 */
	public void addClickInsurance(Long userId,String clientVersion) throws Exception;
}
