package com.tyt.merchant.controller;

import java.io.File;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.merchant.bean.TytMerchantDetailBean;
import com.tyt.merchant.bean.TytMerchantQueryResultBean;
import com.tyt.merchant.bean.TytMerchantSaveBean;
import com.tyt.merchant.service.TytMerchantImageService;
import com.tyt.merchant.service.TytMerchantService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytMerchant;
import com.tyt.model.TytMerchantImage;
import com.tyt.statistic.service.TytMaintainerStatisticService;
import com.tyt.util.ImgCompress;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;

/**
 * 商户
 */
@Controller
@RequestMapping("/plat/merchant")
public class TytMerchantController extends BaseController {

	@Resource(name = "tytMerchantService")
	TytMerchantService tytMerchantService;

	@Resource(name = "tytMerchantImageService")
	TytMerchantImageService tytMerchantImageService;

	@Resource(name = "tytMaintainerStatisticServiceImpl")
	private TytMaintainerStatisticService tytMaintainerStatisticService;

	@RequestMapping(value = "/query")
	@ResponseBody
	public ResultMsgBean getList(String merchantType, String lable,Long userId,
			String latitude, String longitude, String currentPage,
			@RequestParam(value = "type", defaultValue = "0") String type,
			String province, String county, String city) {
		System.out.println("查询条件："+merchantType+"_"+lable
				+"_"+latitude+"_"+longitude+"_"+currentPage+"_"+type+"_"+province+"_"+county+"_"+city);
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (longitude == null || "".equals(longitude.trim())
					|| !StringUtil.isDouble(longitude.trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("longitude不能为空");
				return rm;
			}
			if (latitude == null || "".equals(latitude.trim())
					|| !StringUtil.isDouble(latitude.trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("latitude不能为空");
				return rm;
			}

			if (type != null && type.equals("2")
					&& (province == null || ("").equals(province.trim()))) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("province不能为空");
				return rm;
			}
			// 查询结果集
			TytMerchantQueryResultBean resultBean = tytMerchantService
					.queryList(merchantType, lable, latitude, longitude,
							currentPage, type, province, city, county);
			logger.info("------商户列表查询结果集：【"+resultBean+"】");
			// 保存查询记录到统计表
			tytMaintainerStatisticService.addQueryCondition(userId,1, "查询类型: " + type
					+ " 商户类型: " + merchantType + " 业务标签: " + lable + " 经度: "
					+ longitude + " 纬度: " + latitude + " 省: " + province
					+ " 市: " + city+ " 县: " + county,
					resultBean.getData() == null ? "0" : resultBean.getData()
							.size() + "");
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			rm.setData(resultBean);
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	@RequestMapping(value = "/myPublish")
	@ResponseBody
	public ResultMsgBean getMylist(Long userId, String latitude,
			String longitude, String currentPage) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (longitude == null || "".equals(longitude.trim())
					|| !StringUtil.isDouble(longitude.trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("longitude不能为空");
				return rm;
			}
			if (latitude == null || "".equals(latitude.trim())
					|| !StringUtil.isDouble(latitude.trim())) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("latitude不能为空");
				return rm;
			}

			TytMerchantQueryResultBean resultBean = tytMerchantService
					.queryMyList(userId, latitude, longitude, currentPage);
			logger.info("------我的商户列表查询结果集：【"+resultBean+"】");
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			rm.setData(resultBean);
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	/**
	 * 添加商户信息
	 * 
	 * @param merchant
	 * @param images
	 * @param surfaceImageIndex
	 * @return
	 */
	@RequestMapping("/save")
	@ResponseBody
	public ResultMsgBean saveMerchant(
			TytMerchantSaveBean merchant,
			@RequestParam MultipartFile[] images,
			@RequestParam(value = "surfaceImageIndex", defaultValue = "0") Integer surfaceImageIndex) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 非空验证
			if (!checkParamters(merchant.getCellPhone(),
					merchant.getMerchantName(), merchant.getMerchantLables(),
					merchant.getProvince(), merchant.getCity(),
					merchant.getCounty(), merchant.getLongitude(),
					merchant.getLatitude(), merchant.getCoordX(),
					merchant.getCoordY(), merchant.getFixedPosition(),
					merchant.getInputPosition(), merchant.getTelePhones(),
					images, surfaceImageIndex, rm)) {
				return rm;
			}

			// 重复信息判断
			if (tytMerchantService.queryData(merchant.getMerchantName(),
					merchant.getProvince(), merchant.getCity(),
					merchant.getCounty()) != null) {
				rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
				rm.setMsg("相同区域内,商户名称不能重复");
				return rm;
			}

			// 保存信息
			TytMerchant obj = tytMerchantService.saveData(merchant);

			// 保存图片
			if (images != null) {
				ImgCompress imgCompress = null;
				for (int i = 0; i < images.length; i++) {
					if (images[i] != null && !images[i].isEmpty()) {
						String imageName = renamePic(images[i], "merchant");
						String realImgPath = AppConfig
								.getProperty("picture.path.domain") + imageName;
						String surface = surfaceImageIndex.intValue() == i ? "1"
								: "0";
						// 上传原图片
						images[i].transferTo(new File(realImgPath));
						// 上传压缩后的图片
						imgCompress = new ImgCompress(realImgPath);
						imgCompress.resizeFix(200, 200);
						String imgCompressName = imgCompress
								.getImgUrl()
								.replace(
										AppConfig
												.getProperty("picture.path.domain"),
										"");
						tytMerchantImageService.add(new TytMerchantImage(obj
								.getId(), imageName, imgCompressName, surface,
								i));
						imageName = null;
						realImgPath=null;
						surface = null;
						imgCompressName=null;
						imgCompress=null;
					}
				}
			}

			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("保存成功");
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	/**
	 * 获取商户详情
	 * 
	 * @param id
	 * @param userId
	 * @return
	 */
	@RequestMapping("/detail")
	@ResponseBody
	public ResultMsgBean getMerchantDetail(Long id, Long userId) {
		ResultMsgBean rm = new ResultMsgBean();

		try {
			if (id == null || id.intValue() <= 0) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("id不能为空");
				return rm;
			}
			// 判断数据是否存在
			TytMerchantDetailBean bean = tytMerchantService
					.getMerchantDetailById(id);
			// 查询数据
			if (bean == null) {
				rm.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
				rm.setMsg("对象不存在");
				return rm;
			}
			// 保存浏览记录
			tytMaintainerStatisticService.addScanData(userId == null ? null
					: userId + "", id, 2);
			// 商户主表浏览次数+1
			tytMerchantService.updateReadedCount(id);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			rm.setData(bean);
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	/**
	 * 添加商户信息非空验证
	 * 
	 * @param merchantName
	 * @param merchantLables
	 * @param province
	 * @param city
	 * @param county
	 * @param longitude
	 * @param latitude
	 * @param xCoord
	 * @param yCoord
	 * @param fixedPosition
	 * @param inputPosition
	 * @param telePhones
	 * @param images
	 * @param surfaceImageIndex
	 * @param rm
	 * @return
	 */
	private boolean checkParamters(String cellPhone, String merchantName,
			String merchantLables,String province, String city, String county,
			String longitude, String latitude, String xCoord, String yCoord,
			String fixedPosition, String inputPosition, String telePhones,
			MultipartFile[] images, Integer surfaceImageIndex, ResultMsgBean rm) {

		if (cellPhone == null || "".equals(cellPhone.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("cellPhone不能为空");
			return false;
		}
		if (merchantName == null || "".equals(merchantName.trim())) {

			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("merchantName不能为空");
			return false;
		}
		if (merchantLables == null ||"".equals(merchantLables.trim())
				) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("merchantLables不能为空");
			return false;
		}
		if (province == null || "".equals(province.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("province不能为空");
			return false;
		}
		if (city == null || "".equals(city.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("city不能为空");
			return false;
		}
		if (county == null || "".equals(county.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("county不能为空");
			return false;
		}
		if (longitude == null || "".equals(longitude.trim())
				|| !StringUtil.isDouble(longitude.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("longitude不能为空");
			return false;
		}
		if (latitude == null || "".equals(latitude.trim())
				|| !StringUtil.isDouble(latitude.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("latitude不能为空");
			return false;
		}
		if (xCoord == null || "".equals(xCoord.trim())
				|| !StringUtil.isDouble(xCoord.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("xCoord不能为空");
			return false;
		}
		if (yCoord == null || "".equals(yCoord.trim())
				|| !StringUtil.isDouble(yCoord.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("yCoord不能为空");
			return false;
		}
		if ((fixedPosition == null || "".equals(fixedPosition.trim()))
				&& (inputPosition == null || "".equals(inputPosition.trim()))) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("商户地址不能为空");
			return false;
		}
		if (telePhones == null || "".equals(telePhones.trim())) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("telePhones不能为空");
			return false;
		}
		if (images == null || images.length <= 0 || images[0].isEmpty()) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("images不能为空");
			return false;
		}
		if (surfaceImageIndex == null || surfaceImageIndex.intValue() < 0) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("surfaceImageIndex值错误");
			return false;
		}

		return true;
	}

}
