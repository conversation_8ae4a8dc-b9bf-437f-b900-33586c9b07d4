package com.tyt.merchant.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.merchant.bean.TytMechantListBean;
import com.tyt.merchant.bean.TytMerchantDetailBean;
import com.tyt.merchant.bean.TytMerchantQueryResultBean;
import com.tyt.merchant.bean.TytMerchantSaveBean;
import com.tyt.merchant.service.TytMerchantImageService;
import com.tyt.merchant.service.TytMerchantService;
import com.tyt.model.TytMerchant;
import com.tyt.model.TytMerchantImage;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.DistanceUtil;
import com.tyt.util.StringUtil;

@Service("tytMerchantService")
public class TytMerchantServiceImpl extends BaseServiceImpl<TytMerchant, Long>
		implements TytMerchantService {

	@Resource(name = "tytMerchantDao")
	public void setBaseDao(BaseDao<TytMerchant, Long> tytMerchantDao) {
		super.setBaseDao(tytMerchantDao);
	}

	@Resource(name = "tytMerchantImageService")
	TytMerchantImageService tytMerchantImageService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Override
	public Long queryData(String merchantName, String province, String city,
			String county) throws Exception {
		String querySQL = "SELECT id FROM tyt_merchant WHERE merchant_name=? "
				+ "AND province=? AND city=? AND county=? AND status<>?";
		BigInteger id = this.getBaseDao().query(querySQL,
				new Object[] { merchantName, province, city, county, 2 });
		if (id == null || id.longValue() <= 0l) {
			return null;
		}
		return id.longValue();
	}

	@Override
	public TytMerchant saveData(TytMerchantSaveBean merchant) throws Exception {
		TytMerchant obj = new TytMerchant();
		obj.setStatus("0");
		obj.setCreateTime(new Date());
		obj.setUpdateTime(new Date());
		obj.setBelongUserId(merchant.getUserId());
		obj.setBelongCellPhone(merchant.getCellPhone());
		obj.setUserId(merchant.getUserId());
		obj.setCellPhone(merchant.getCellPhone());

		obj.setMerchantName(merchant.getMerchantName());
		// 标签处理
		String[] array = merchant.getMerchantLables().split("/");
		for (int i = 0; i < array.length; i++) {
			if (i == 0) {
				obj.setMerchantType(array[0]);
			}
			if (i == 1) {
				if(array[1]!=null&&!("").equals(array[1].trim())){
					obj.setLables(array[1]);
				}
				
			}
			if (i == 2) {
				if(array[2]!=null&&!("").equals(array[2].trim())){
					obj.setRemark(array[2]);
				}
			}
		}
		
		String[] arrayName = merchant.getMerchantLableNames().split("/");
		for (int i = 0; i < arrayName.length; i++) {
			if (i == 0) {
				obj.setMerchantTypeName(arrayName[0]);
			}
			if (i == 1) {
				obj.setLableNames(arrayName[1]);
			}
		}

		obj.setProvince(merchant.getProvince());
		obj.setCity(merchant.getCity());
		obj.setCounty(merchant.getCounty());
		obj.setFixedPosition(merchant.getFixedPosition());
		obj.setInputPosition(merchant.getInputPosition());
		obj.setTelePhones(merchant.getTelePhones());

		// 经纬度、坐标处理
		obj.setLongitude(new BigDecimal(merchant.getLongitude())
				.movePointRight(4).intValue());
		obj.setLatitude(new BigDecimal(merchant.getLatitude())
				.movePointRight(4).intValue());
		obj.setXCoord(new BigDecimal(merchant.getCoordX()).movePointRight(4)
				.intValue());
		obj.setYCoord(new BigDecimal(merchant.getCoordY()).movePointRight(4)
				.intValue());

		// 版本信息
		obj.setClientVersion(merchant.getClientVersion());
		obj.setClientSign(merchant.getClientSign());

		// 默认被拨打次数,倍浏览次数为0
		obj.setCallCount(0l);
		obj.setReadCount(0l);
		this.add(obj);
		return obj;
	}

	@Override
	public TytMerchantDetailBean getMerchantDetailById(Long id) {
		// 基本信息
		String querySQL = "SELECT id,merchant_type merchantType,province,"
				+ "city,county,lables,remark,tele_phones telePhones,"
				+ "status,merchant_name merchantName,fixed_position fixedPosition,"
				+ "input_position inputPosition,longitude,latitude,"
				+ "merchant_type_name merchantTypeName,lable_names lableNames "
				+ "FROM tyt_merchant WHERE id=:id";
		Map<String, org.hibernate.type.Type> cMap = new HashMap<String, org.hibernate.type.Type>();
		cMap.put("id", Hibernate.LONG);
		cMap.put("merchantType", Hibernate.STRING);
		cMap.put("province", Hibernate.STRING);
		cMap.put("city", Hibernate.STRING);
		cMap.put("county", Hibernate.STRING);
		cMap.put("lables", Hibernate.STRING);
		cMap.put("remark", Hibernate.STRING);
		cMap.put("telePhones", Hibernate.STRING);
		cMap.put("status", Hibernate.STRING);
		cMap.put("merchantName", Hibernate.STRING);
		cMap.put("fixedPosition", Hibernate.STRING);
		cMap.put("inputPosition", Hibernate.STRING);
		cMap.put("longitude", Hibernate.STRING);
		cMap.put("latitude", Hibernate.STRING);
		cMap.put("merchantTypeName", Hibernate.STRING);
		cMap.put("lableNames", Hibernate.STRING);

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("id", id);
		TytMerchantDetailBean bean = this.getBaseDao().queryByMap(querySQL,
				map, TytMerchantDetailBean.class, cMap);
		if (bean != null) {
			DecimalFormat df = new DecimalFormat("0.0000");
			String lati = df.format(Float.valueOf(bean.getLatitude()) / 10000);
			String longit = df
					.format(Float.valueOf(bean.getLongitude()) / 10000);
			bean.setLongitude(longit);
			bean.setLatitude(lati);
			// 图片信息获取
			List<TytMerchantImage> images = tytMerchantImageService
					.getImagesById(id, null);
			bean.setImageURLs(images);
			return bean;
		} else {
			return null;
		}

	}

	@Override
	public Long getMyMerchantNbr(Long userId, String status) {
		String countSQL = "SELECT COUNT(*) FROM tyt_merchant "
				+ "WHERE belong_user_id=:userId AND status IN(:status)";
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userId", userId);
		map.put("status", new Object[] { 0, 1 });
		BigInteger count = this.getBaseDao().queryByMap(countSQL, map, null,
				null);
		if (count == null || count.longValue() <= 0l) {
			return 0l;
		}
		return count.longValue();
	}

	@Override
	public TytMerchantQueryResultBean queryList(String merchantType,
			String lable, String latitude, String longitude,
			String currentPage, String type, String province, String city,
			String county) {
		TytMerchantQueryResultBean resultBean = new TytMerchantQueryResultBean();
		/*
		 * 计算从第几条开始查询到第几条
		 */
		Integer searchSize = tytConfigService.getIntValue("merchantPageSize");
		if (searchSize == null || searchSize.intValue() <= 0) {
			searchSize = 15;
		}
		if (currentPage == null || !StringUtil.isNumeric(currentPage)
				|| Integer.valueOf(currentPage) <= 0) {
			currentPage = "1";
		}
		int startPos = (Integer.valueOf(currentPage) - 1) * searchSize + 1;
		int endPos = Integer.valueOf(currentPage) * searchSize;

		StringBuffer count = new StringBuffer(
				"SELECT count(*)  FROM tyt_merchant t WHERE 1=1");
		List<Object> list = new ArrayList<Object>();
		//不显示无效信息
		StringBuffer sb = new StringBuffer(" AND status <>?");
		list.add(2);
		// 获取商户附近的查询距离
		Integer distance = tytConfigService
				.getIntValue("merchantNeighborRange");
		if (distance == null) {
			distance = 100;
		}
		if (type.equals("0") || type.equals("1")) {
			/*
			 * 根据经纬度和要查询的范围获取矩形顶点的经纬度坐标
			 */
			double[] rectanglePoints = DistanceUtil.getRectanglePointsByPos(
					Double.valueOf(longitude), Double.valueOf(latitude),
					distance);
			int leftLatitude = (int) (rectanglePoints[0] * 10000);
			int leftLongitude = (int) (rectanglePoints[1] * 10000);
			int rightLatitude = (int) (rectanglePoints[2] * 10000);
			int rightLongitude = (int) (rectanglePoints[3] * 10000);
			sb.append(" AND t.`longitude` >= ?");
			list.add(leftLongitude);
			sb.append(" AND t.`longitude` <= ?");
			list.add(rightLongitude);
			sb.append(" AND t.`latitude` >= ?");
			list.add(rightLatitude);
			sb.append(" AND t.`latitude` <= ?");
			list.add(leftLatitude + "_");
		} else {
			if (province != null && !"".equals(province.trim())) {
				sb.append(" AND t.`province` LIKE ?");
				list.add("%" + province + "%");
			}
			
			if (city != null && !"".equals(city.trim())) {
				sb.append(" AND t.`city` LIKE ?");
				list.add("%" + city + "%");
			}
			
			if (county != null && !"".equals(county.trim())) {
				sb.append(" AND t.`county` LIKE ?");
				list.add("%" + county + "%");
			}
		}

		if (merchantType != null && !"".equals(merchantType.trim())
				&& StringUtil.isNumeric(merchantType.trim())) {
			sb.append(" AND t.`merchant_type`=?");
			list.add(merchantType);

			if (lable != null && !"".equals(lable.trim())
					&& StringUtil.isNumeric(lable.trim())) {
				sb.append(" AND t.`lables` LIKE ?");
				list.add("%#" + lable + "#%");
			}
		}

		count.append(sb);
		BigInteger total = this.getBaseDao().query(count.toString(),
				list.toArray());

		// 从数据库取正方形内的全部数据

		/*
		 * 总数大于0才去查询数据
		 */
		if (total == null || total.intValue() < 1) {
			resultBean.setCurrentPage(Integer.valueOf(currentPage));
			resultBean.setMaxPage(0);
			resultBean.setPageSize(searchSize);
			resultBean.setTotalRecord(0);
		} else {
			/*
			 * 查询数据
			 */
			resultBean.setCurrentPage(Integer.valueOf(currentPage));
			int totalRecord = total.intValue();
			int pageNumber = (totalRecord + searchSize - 1) / searchSize;
			resultBean.setMaxPage(pageNumber);
			resultBean.setPageSize(searchSize);
			resultBean.setTotalRecord(totalRecord);
			if (Integer.valueOf(currentPage).intValue() <= pageNumber) {
				StringBuffer select = new StringBuffer(
						"SELECT t.`id`, t.`merchant_name` AS merchantName, "
								+ "t.`fixed_position` fixedPosition, t.`input_position` AS inputPosition, "
								+ "t.`status`, t.`longitude` ,t.`latitude`,m.`image_url` AS surfaceImageURL,"
								+ "m.`compress_image_url` AS surfaceCompressImgURL "
								+ "FROM tyt_merchant t "
								+ "LEFT JOIN tyt_merchant_image m ON  t.`id`=m.`merchant_id` WHERE m.`is_surface`='1'");

				select.append(sb);
				Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
				scalarMap.put("id", Hibernate.LONG);
				scalarMap.put("merchantName", Hibernate.STRING);
				scalarMap.put("fixedPosition", Hibernate.STRING);
				scalarMap.put("inputPosition", Hibernate.STRING);
				scalarMap.put("status", Hibernate.STRING);
				scalarMap.put("longitude", Hibernate.STRING);
				scalarMap.put("latitude", Hibernate.STRING);
				scalarMap.put("surfaceImageURL", Hibernate.STRING);
				scalarMap.put("surfaceCompressImgURL", Hibernate.STRING);
				// 查询所有在矩形区域内的数据
				List<TytMechantListBean> returnList = this.getBaseDao().search(
						select.toString(), scalarMap, TytMechantListBean.class,
						list.toArray());
				if (returnList.size() > 0) {
					Iterator<TytMechantListBean> iterator = returnList
							.iterator();
					DecimalFormat df = new DecimalFormat("0.0000");
					double dis;
					TytMechantListBean merchantListBean = null;
					while (iterator.hasNext()) {
						merchantListBean = iterator.next();
						/*
						 * 过滤并设置距离
						 */
						String lati = df.format(Float.valueOf(merchantListBean
								.getLatitude()) / 10000);
						String longit = df
								.format(Float.valueOf(merchantListBean
										.getLongitude()) / 10000);
						dis = DistanceUtil.distance(Double.valueOf(longitude),
								Double.valueOf(latitude),
								Double.valueOf(longit), Double.valueOf(lati));
						merchantListBean.setLongitude(longit);
						merchantListBean.setLatitude(lati);
						
						if (!type.equals("2")&&dis > distance * 1000) {
							iterator.remove();
						} else {
							// 设置距离
							merchantListBean.setDistance(dis + "");
						}
					}
					/*
					 * 所有的数据按照距离排序从近到远排序
					 */
					Collections.sort(returnList,
							new Comparator<TytMechantListBean>() {
								@Override
								public int compare(TytMechantListBean o1,
										TytMechantListBean o2) {
									return (int) (Double.valueOf(o1
											.getDistance()) - Double.valueOf(o2
											.getDistance()));
								}
							});
					System.out.println("-----商户信息列表满足条件的所有结果集【"+returnList+"】");
					// 根据当前查询的数据位置过滤数据
					if (type == null || "".equals(type.trim())
							|| type.equals("0"))
						returnList = filterDataByPos(returnList, startPos,
								endPos);
				}
				resultBean.setData(returnList);
			}

		}

		// 返回固定条数的结果
		return resultBean;
	}

	private List<TytMechantListBean> filterDataByPos(
			List<TytMechantListBean> returnList, int startPos, int endPos) {
		if (returnList.size() > 0) {
			returnList = returnList.subList(--startPos,
					(endPos > returnList.size()) ? returnList.size() : endPos);
		}
		return returnList;
	}

	@Override
	public TytMerchantQueryResultBean queryMyList(Long userId, String latitude,
			String longitude, String currentPage) {
		TytMerchantQueryResultBean resultBean = new TytMerchantQueryResultBean();
		/*
		 * 计算从第几条开始查询到第几条
		 */
		Integer searchSize = tytConfigService.getIntValue("merchantPageSize");
		if (searchSize == null || searchSize.intValue() <= 0) {
			searchSize = 15;
		}
		if (currentPage == null || !StringUtil.isNumeric(currentPage)
				|| Integer.valueOf(currentPage) <= 0) {
			currentPage = "1";
		}
		int startPos = (Integer.valueOf(currentPage) - 1) * searchSize + 1;
		int endPos = Integer.valueOf(currentPage) * searchSize;

		StringBuffer count = new StringBuffer(
				"SELECT count(*)  FROM tyt_merchant t WHERE 1=1");

		StringBuffer sb = new StringBuffer(
				" AND t.`belong_user_id`=:userId AND t.`status` IN(:status)");

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userId", userId);
		map.put("status", new Object[] { 0, 1 });

		BigInteger total = this.getBaseDao().queryByMap(
				count.append(sb).toString(), map, null, null);

		// 从数据库取正方形内的全部数据

		/*
		 * 总数大于0才去查询数据
		 */
		if (total == null || total.intValue() < 1) {
			resultBean.setCurrentPage(Integer.valueOf(currentPage));
			resultBean.setMaxPage(0);
			resultBean.setPageSize(searchSize);
			resultBean.setTotalRecord(0);
		} else {
			/*
			 * 查询数据
			 */
			resultBean.setCurrentPage(Integer.valueOf(currentPage));
			int totalRecord = total.intValue();
			int pageNumber = (totalRecord + searchSize - 1) / searchSize;
			resultBean.setMaxPage(pageNumber);
			resultBean.setPageSize(searchSize);
			resultBean.setTotalRecord(totalRecord);
			if (Integer.valueOf(currentPage).intValue() <= pageNumber) {
				StringBuffer select = new StringBuffer(
						"SELECT t.`id`, t.`merchant_name` AS merchantName, "
								+ "t.`fixed_position` fixedPosition, t.`input_position` AS inputPosition, "
								+ "t.`status`, t.`longitude` ,t.`latitude`,"
								+ "m.`image_url` AS surfaceImageURL,"
								+ "m.`compress_image_url` AS surfaceCompressImgURL "
								+ "FROM tyt_merchant t "
								+ "LEFT JOIN tyt_merchant_image m ON  t.`id`=m.`merchant_id` WHERE 1=1");

				select.append(sb);
				select.append(" AND m.`is_surface`=:isSurface");
				map.put("isSurface", "1");
				Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
				scalarMap.put("id", Hibernate.LONG);
				scalarMap.put("merchantName", Hibernate.STRING);
				scalarMap.put("fixedPosition", Hibernate.STRING);
				scalarMap.put("inputPosition", Hibernate.STRING);
				scalarMap.put("status", Hibernate.STRING);
				scalarMap.put("longitude", Hibernate.STRING);
				scalarMap.put("latitude", Hibernate.STRING);
				scalarMap.put("surfaceImageURL", Hibernate.STRING);
				scalarMap.put("surfaceCompressImgURL", Hibernate.STRING);
				// 查询所有在矩形区域内的数据
				List<TytMechantListBean> returnList = this.getBaseDao()
						.search(select.toString(), scalarMap,
								TytMechantListBean.class, map);
				if (returnList.size() > 0) {
					Iterator<TytMechantListBean> iterator = returnList.iterator();
					DecimalFormat df = new DecimalFormat("0.0000");
					double dis;
					TytMechantListBean merchantListBean = null;
					while (iterator.hasNext()) {
						merchantListBean = iterator.next();
						/*
						 * 过滤并设置距离
						 */
						String lati = df.format(Float.valueOf(merchantListBean
								.getLatitude()) / 10000);
						String longit = df.format(Float.valueOf(merchantListBean
								.getLongitude()) / 10000);
						dis = DistanceUtil.distance(Double.valueOf(longitude),
								Double.valueOf(latitude), Double.valueOf(longit),
								Double.valueOf(lati));
						merchantListBean.setLongitude(longit);
						merchantListBean.setLatitude(lati);
						merchantListBean.setDistance(dis + "");
					}
					/*
					 * 所有的数据按照距离排序从近到远排序
					 */
					Collections.sort(returnList,
							new Comparator<TytMechantListBean>() {
								@Override
								public int compare(TytMechantListBean o1,
										TytMechantListBean o2) {
									return (int) (Double.valueOf(o1.getDistance()) - Double
											.valueOf(o2.getDistance()));
								}
							});
					System.out.println("-----我的商户信息列表满足条件的所有结果集【"+returnList+"】");
					// 根据当前查询的数据位置过滤数据
					returnList = filterDataByPos(returnList, startPos, endPos);

				}
				resultBean.setData(returnList);
			}
		}
		// 返回固定条数的结果
		return resultBean;
	}

	@Override
	public void updateCalledCount(Long id) {
		String updateSQL = "UPDATE tyt_merchant m SET m.`call_count`=m.`call_count`+1 WHERE m.`id`=?";
		this.getBaseDao().executeUpdateSql(updateSQL, new Object[] { id });
	}

	@Override
	public void updateReadedCount(Long id) {
		String updateSQL = "UPDATE tyt_merchant m SET m.`read_count`=m.`read_count`+1 WHERE m.`id`=?";
		this.getBaseDao().executeUpdateSql(updateSQL, new Object[] { id });

	}
}
