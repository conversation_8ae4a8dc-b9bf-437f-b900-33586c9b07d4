package com.tyt.merchant.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.merchant.service.TytMerchantImageService;
import com.tyt.model.TytMerchantImage;

@Service("tytMerchantImageService")
public class TytMerchantImageServiceImpl extends
		BaseServiceImpl<TytMerchantImage, Long> implements
		TytMerchantImageService {

	@Resource(name = "tytMerchantImageDao")
	public void setBaseDao(BaseDao<TytMerchantImage, Long> tytMerchantImageDao) {
		super.setBaseDao(tytMerchantImageDao);
	}

	@Override
	public List<TytMerchantImage> getImagesById(Long id, Integer isSurface) {
		List<Object> params = new ArrayList<Object>();
		StringBuffer querySQL = new StringBuffer("entity.merchantId=?");
		params.add(id);
		if (isSurface != null && isSurface.intValue() >= 0) {
			querySQL.append(" and entity.isSurface=?");
			params.add(isSurface);
		}
		querySQL.append(" order by sort");
		List<TytMerchantImage> images = this.getBaseDao().search(
				querySQL.toString(), params.toArray(), null);
		if (images == null || images.size() <= 0) {
			return null;
		}
		return images;
	}

}
