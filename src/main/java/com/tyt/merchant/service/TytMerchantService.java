package com.tyt.merchant.service;

import com.tyt.base.service.BaseService;
import com.tyt.merchant.bean.TytMerchantDetailBean;
import com.tyt.merchant.bean.TytMerchantQueryResultBean;
import com.tyt.merchant.bean.TytMerchantSaveBean;
import com.tyt.model.TytMerchant;

public interface TytMerchantService extends BaseService<TytMerchant, Long> {
	/**
	 * 根据商户名称，省市县，查询商户
	 * 
	 * @param merchantName
	 * @param province
	 * @param city
	 * @param county
	 * @return
	 * @throws Exception
	 */
	public Long queryData(String merchantName, String province, String city,
			String county) throws Exception;

	/**
	 * 添加商户信息
	 * 
	 * @param merchant
	 * @return
	 */
	public TytMerchant saveData(TytMerchantSaveBean merchant) throws Exception;

	/**
	 * 获取商户详情
	 * 
	 * @param id
	 * @return
	 */
	public TytMerchantDetailBean getMerchantDetailById(Long id);
    /**
     * 获取我的商户信息角标数
     * @param userId
     * @param status
     * @return
     */
	public Long getMyMerchantNbr(Long userId, String status);
    /**
     * 查询商户列表
     * @param merchantType
     * @param lable
     * @param latitude
     * @param longitude
     * @param currentPage
     * @param type
     * @param province 
     * @param county 
     * @param city 
     * @return
     */
	public TytMerchantQueryResultBean queryList(String merchantType,
			String lable, String latitude, String longitude,
			String currentPage, String type, String province, String city, String county);
    /**
     * 我的商户列表查询
     * @param userId
     * @param latitude
     * @param longitude
     * @param currentPage
     * @return
     */
	public TytMerchantQueryResultBean queryMyList(Long userId, String latitude,
			String longitude, String currentPage);
	/**
	 * 修改被拨打电话次数
	 * @param id
	 */
	public void updateCalledCount(Long id);
	/**
	 * 修改被拨打浏览次数
	 * @param id
	 */
	public void updateReadedCount(Long id);
}
