package com.tyt.merchant.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TytMechantListBean{
	
	private Long id;
	private String merchantName;
	private String fixedPosition;
	private String inputPosition;
	private String status;
	private String distance;
	private String surfaceImageURL;
	private String surfaceCompressImgURL;
	private String longitude;
	private String latitude;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getMerchantName() {
		return merchantName;
	}
	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}
	public String getFixedPosition() {
		return fixedPosition;
	}
	public void setFixedPosition(String fixedPosition) {
		this.fixedPosition = fixedPosition;
	}
	public String getInputPosition() {
		return inputPosition;
	}
	public void setInputPosition(String inputPosition) {
		this.inputPosition = inputPosition;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getDistance() {
		return distance;
	}
	public void setDistance(String distance) {
		this.distance = distance;
	}
	public String getSurfaceImageURL() {
		return surfaceImageURL;
	}
	public void setSurfaceImageURL(String surfaceImageURL) {
		this.surfaceImageURL = surfaceImageURL;
	}
	public String getLongitude() {
		return longitude;
	}
	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}
	public String getLatitude() {
		return latitude;
	}
	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}
	public String getSurfaceCompressImgURL() {
		return surfaceCompressImgURL;
	}
	public void setSurfaceCompressImgURL(String surfaceCompressImgURL) {
		this.surfaceCompressImgURL = surfaceCompressImgURL;
	}
	
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
}
