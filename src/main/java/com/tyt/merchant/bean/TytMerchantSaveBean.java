package com.tyt.merchant.bean;
/**
 * 商户保存bean
 * <AUTHOR>
 * @date   2016年6月1日下午3:29:11
 *
 */
public class TytMerchantSaveBean {

	private Long userId;
	private String cellPhone;
	private String merchantName;
	private String remark;
	private String province;
	private String city;
	private String county;
	private String coordY;
	private String coordX;
	private String longitude;
	private String latitude;
	private String fixedPosition;
	private String inputPosition;
	private String telePhones;
	private String clientSign;
	private String clientVersion;
	private String merchantLables;
	private String merchantLableNames;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getMerchantName() {
		return merchantName;
	}

	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getCoordY() {
		return coordY;
	}

	public void setCoordY(String coordY) {
		this.coordY = coordY;
	}

	public String getCoordX() {
		return coordX;
	}

	public void setCoordX(String coordX) {
		this.coordX = coordX;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getFixedPosition() {
		return fixedPosition;
	}

	public void setFixedPosition(String fixedPosition) {
		this.fixedPosition = fixedPosition;
	}

	public String getInputPosition() {
		return inputPosition;
	}

	public void setInputPosition(String inputPosition) {
		this.inputPosition = inputPosition;
	}

	public String getTelePhones() {
		return telePhones;
	}

	public void setTelePhones(String telePhones) {
		this.telePhones = telePhones;
	}

	public String getClientSign() {
		return clientSign;
	}

	public void setClientSign(String clientSign) {
		this.clientSign = clientSign;
	}

	public String getClientVersion() {
		return clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	public String getMerchantLables() {
		return merchantLables;
	}

	public void setMerchantLables(String merchantLables) {
		this.merchantLables = merchantLables;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getMerchantLableNames() {
		return merchantLableNames;
	}

	public void setMerchantLableNames(String merchantLableNames) {
		this.merchantLableNames = merchantLableNames;
	}
	
}
