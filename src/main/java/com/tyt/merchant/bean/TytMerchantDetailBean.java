package com.tyt.merchant.bean;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.tyt.model.TytMerchantImage;

/**
 * 商户查询详情bean
 * 
 * <AUTHOR>
 * @date 2016年6月1日下午3:29:11
 * 
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TytMerchantDetailBean {

	private Long id;
	private String merchantType;
	private String lables;
	private String remark;
	private String province;
	private String city;
	private String county;
	private String status;
	private String telePhones;
	private List<TytMerchantImage> imageURLs;
	
	private String merchantName;
	private String fixedPosition;
	private String inputPosition;
	private String longitude;
	private String latitude;
	
	private String merchantTypeName;
	private String lableNames;
	
	

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMerchantType() {
		return merchantType;
	}

	public void setMerchantType(String merchantType) {
		this.merchantType = merchantType;
	}

	public String getLables() {
		return lables;
	}

	public void setLables(String lables) {
		this.lables = lables;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getTelePhones() {
		return telePhones;
	}

	public void setTelePhones(String telePhones) {
		this.telePhones = telePhones;
	}

	public List<TytMerchantImage> getImageURLs() {
		return imageURLs;
	}

	public void setImageURLs(List<TytMerchantImage> imageURLs) {
		this.imageURLs = imageURLs;
	}

	public String getMerchantName() {
		return merchantName;
	}

	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}

	public String getFixedPosition() {
		return fixedPosition;
	}

	public void setFixedPosition(String fixedPosition) {
		this.fixedPosition = fixedPosition;
	}

	public String getInputPosition() {
		return inputPosition;
	}

	public void setInputPosition(String inputPosition) {
		this.inputPosition = inputPosition;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getMerchantTypeName() {
		return merchantTypeName;
	}

	public void setMerchantTypeName(String merchantTypeName) {
		this.merchantTypeName = merchantTypeName;
	}

	public String getLableNames() {
		return lableNames;
	}

	public void setLableNames(String lableNames) {
		this.lableNames = lableNames;
	}
    
}
