package com.tyt.car.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.CarDetailHead;

import java.util.List;
import java.util.Map;

public interface CarDetailHeadService extends BaseService<CarDetailHead, Long> {

    Map<Long, CarDetailHead> getByCarIds(List<Long> idList);
    /**
     * 根据carId 获取车辆车头信息
     * @param carId
     * @return
     */
    CarDetailHead getByCarId(Long carId);

}
