package com.tyt.car.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.BasePoiDistince;
import com.tyt.model.CarDetailTail;

import java.util.List;
import java.util.Map;

public interface CarDetailTailService extends BaseService<CarDetailTail, Long> {

    List<CarDetailTail> selectCarDetailTailByCarIdList(List<Long> carIdList);




    /**
     * 根据车辆id获取挂车信息
     * @param carId 车辆id
     * @return
     */
    CarDetailTail getByCarId(Long carId);
    Map<Long,CarDetailTail>  getByCarIds(List<Long> idList);

    CarDetailTail getByCarIdInfo(Long id);


    BasePoiDistince getDistinct (String userId);
}
