package com.tyt.car.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.car.service.CarDetailHeadService;
import com.tyt.model.CarDetailHead;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.List;

@Service("carDetailHeadService")
public class CarDetailHeadServiceImpl extends BaseServiceImpl<CarDetailHead, Long> implements CarDetailHeadService {

	@Resource(name = "carDetailHeadDao")
	public void setBaseDao(BaseDao<CarDetailHead, Long> carDetailHeadDao) {
		super.setBaseDao(carDetailHeadDao);
	}

	/**
	 * 根据carId集合查询这些车的车头数据
	 *
	 * @param carIdList carId集合
	 * @return 挂车数据集合
	 */
	@Override
	public Map<Long, CarDetailHead> getByCarIds(List<Long> idList) {
		if (CollectionUtils.isEmpty(idList)) {
			return null;
		}
		String sql = " select id, " +
				" car_id as carId,curb_weight as  curbWeight,maximum_axle_load as maximumAxleLoad " +
				" from tyt_car_detail_head " +
				" where car_id in (:carIdList) ";
		Map<String, Object> map = new HashMap<>();
		map.put("carIdList", idList);

		Map<String, Type> returnMap = new HashMap<>();
		returnMap.put("id", Hibernate.LONG);
		returnMap.put("carId", Hibernate.LONG);
		returnMap.put("curbWeight", Hibernate.STRING);
		returnMap.put("maximumAxleLoad", Hibernate.INTEGER);
		List<CarDetailHead> search = this.getBaseDao().search(sql, returnMap, CarDetailHead.class, map);
		if (CollectionUtils.isNotEmpty(search)) {
			Map<Long, CarDetailHead> tailMap = search.stream()
					.collect(Collectors.toMap(CarDetailHead::getCarId, carDetailHead -> carDetailHead));
			return tailMap;
		}
		return null;
	}

	@Override
	public CarDetailHead getByCarId(Long carId) {
		String sql = "select * from tyt_car_detail_head where car_id =? ORDER BY id desc limit 1";
		List<CarDetailHead> carDetailHeads = this.getBaseDao().queryForList(sql, new Object[]{carId});
		if (CollectionUtils.isNotEmpty(carDetailHeads)) {
			return carDetailHeads.get(0);
		}
		return null;
	}
}
