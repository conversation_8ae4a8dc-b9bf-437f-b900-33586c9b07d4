package com.tyt.car.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.model.BasePoiDistince;
import com.tyt.model.CarDetailTail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service("carDetailTailService")
public class CarDetailTailServiceImpl extends BaseServiceImpl<CarDetailTail, Long> implements CarDetailTailService {

	@Resource(name = "carDetailTailDao")
	public void setBaseDao(BaseDao<CarDetailTail, Long> carDetailTailDao) {
		super.setBaseDao(carDetailTailDao);
	}

	/**
	 * 根据carId集合查询这些车的挂车数据
	 *
	 * @param carIdList carId集合
	 * @return 挂车数据集合
	 */
	@Override
	public List<CarDetailTail> selectCarDetailTailByCarIdList(List<Long> carIdList) {
		if (CollectionUtils.isEmpty(carIdList)) {
			return new ArrayList<>();
		}

		String sql = " select " +
				" car_id as carId, is_pure_flat as isPureFlat " +
				" from tyt_car_detail_tail " +
				" where car_id in (:carIdList) ";
		Map<String, Object> map = new HashMap<>();
		map.put("carIdList", carIdList);

		Map<String, Type> returnMap = new HashMap<>();
		returnMap.put("carId", Hibernate.LONG);
		returnMap.put("isPureFlat", Hibernate.INTEGER);
        return this.getBaseDao().search(sql, returnMap, CarDetailTail.class, map);
	}

	/**
	 * 根据carId集合查询这些车的挂车数据
	 *
	 * @param carIdList carId集合
	 * @return 挂车数据集合
	 */
	@Override
	public Map<Long, CarDetailTail> getByCarIds(List<Long> idList) {
		if (CollectionUtils.isEmpty(idList)) {
			return null;
		}
		String sql = " select t.id," +
				" t.car_id as carId,t.curb_weight as  curbWeight,t.maximum_axle_load as maximumAxleLoad,l.license_type as carType" +
				" from tyt_car_detail_tail t left join tyt_car_type c on t.car_type = c.id" +
				" left join tyt_car_type_lincense l on c.car_type = l.car_type" +
				" where t.car_id in (:carIdList) ";
		Map<String, Object> map = new HashMap<>();
		map.put("carIdList", idList);

		Map<String, Type> returnMap = new HashMap<>();
		returnMap.put("id", Hibernate.LONG);
		returnMap.put("carId", Hibernate.LONG);
		returnMap.put("curbWeight", Hibernate.STRING);
		returnMap.put("maximumAxleLoad", Hibernate.INTEGER);
		returnMap.put("carType", Hibernate.STRING);

		List<CarDetailTail> search = this.getBaseDao().search(sql, returnMap, CarDetailTail.class, map);
		if (CollectionUtils.isNotEmpty(search)) {
			Map<Long, CarDetailTail> tailMap = search.stream()
					.collect(Collectors.toMap(CarDetailTail::getCarId, carDetailTail -> carDetailTail));
			return tailMap;
		}
		return null;
	}

	/**
	 * 查询挂车信息
	 * @param id 车辆id
	 * @return CarDetailTail
	 */
	public CarDetailTail getByCarIdInfo(Long id) {
		String sql = " select id,car_id as carId, " +
				" car_type as carType " +
				" from tyt_car_detail_tail " +
				" where car_id = ? limit 1";
		Map<String, Type> returnMap = new HashMap<>();
		returnMap.put("id", Hibernate.LONG);
		returnMap.put("carId", Hibernate.LONG);
		returnMap.put("carType", Hibernate.STRING);
		List<CarDetailTail> search = this.getBaseDao().search(sql, returnMap, CarDetailTail.class, new Object[]{id});
		if (CollectionUtils.isNotEmpty(search)) {
			return search.get(0);
		}
		return null;
	}



	@Override
	public CarDetailTail getByCarId(Long carId) {
		String sql = "select * from tyt_car_detail_tail where car_id =? ORDER BY id desc limit 1";
		List<CarDetailTail> carDetailTails = this.getBaseDao().queryForList(sql, new Object[]{carId});
		if (CollectionUtils.isNotEmpty(carDetailTails)) {
			return carDetailTails.get(0);
		}
		return null;
	}


	/**
	 *
	 * @param userId
	 * @return
	 */
	@Override
	public BasePoiDistince getDistinct(String userId) {
		String sql = "select id,`poi_city` as poiCity,`poi_area` as poiArea,`warehouse` as warehouse, `distinct` as `distinct` from tyt_recommend.base_poi_distince where id =?  limit 1";
		Map<String, Type> returnMap = new HashMap<>();
		returnMap.put("id", Hibernate.STRING);
		returnMap.put("poiCity", Hibernate.STRING);
		returnMap.put("poiArea", Hibernate.STRING);
		returnMap.put("warehouse", Hibernate.STRING);
		returnMap.put("distinct", Hibernate.STRING);
		List<BasePoiDistince> search = this.getBaseDao().search(sql, returnMap, BasePoiDistince.class, new Object[]{userId});
		log.info("经分获取用户距离标记userId:{}，BasePoiDistince{}",userId, JSON.toJSONString(search));
		if (CollectionUtils.isNotEmpty(search)) {
			return search.get(0);
		}
		return null;
	}


}
