package com.tyt.car.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.car.dao.CarDetailTailDao;
import com.tyt.model.CarDetailTail;
import com.tyt.model.CarInsuranceInquiry;
import org.springframework.stereotype.Repository;

@Repository("carDetailTailDao")
public class CarDetailTailDaoImpl extends BaseDaoImpl<CarDetailTail, Long> implements CarDetailTailDao {

    public CarDetailTailDaoImpl() {
        this.setEntityClass(CarDetailTail.class);
    }
}
