package com.tyt.car.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.car.dao.CarDetailHeadDao;
import com.tyt.model.CarDetailHead;
import com.tyt.model.CarDetailTail;
import org.springframework.stereotype.Repository;

@Repository("carDetailHeadDao")
public class CarDetailHeadDaoImpl extends BaseDaoImpl<CarDetailHead, Long> implements CarDetailHeadDao {

    public CarDetailHeadDaoImpl() {
        this.setEntityClass(CarDetailHead.class);
    }
}
