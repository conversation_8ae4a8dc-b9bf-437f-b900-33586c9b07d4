package com.tyt.excellentgoodshomepage.service.impl;

import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardConfig;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetail;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetailVO;
import com.tyt.excellentgoodshomepage.service.ExcellentGoodsService;
import com.tyt.plat.mapper.base.TytExcellentGoodsCardConfigMapper;
import com.tyt.plat.mapper.base.TytExcellentGoodsCardUserDetailMapper;
import com.tyt.transport.enums.ExcellentCardTypeEnum;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.TimeUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class ExcellentGoodsServiceImpl implements ExcellentGoodsService {

    @Autowired
    private TytConfigService configService;

    public ExcellentGoodsServiceImpl(TytExcellentGoodsCardUserDetailMapper tytExcellentGoodsCardUserDetailMapper) {
        this.tytExcellentGoodsCardUserDetailMapper = tytExcellentGoodsCardUserDetailMapper;
    }

    private final TytExcellentGoodsCardUserDetailMapper tytExcellentGoodsCardUserDetailMapper;

    @Autowired
    private TytExcellentGoodsCardConfigMapper excellentGoodsCardConfigMapper;

    private static final String EXCELLENT_GOODS_CARD_AUTO_ID = "excellent_goods_card_auto_id";

    @Override
    public List<TytExcellentGoodsCardUserDetailVO> getAllNoUseCarListByUserId(Long userId, Integer pageNum) {
        int pageSize = 20;
        List<TytExcellentGoodsCardUserDetailVO> result = new ArrayList<>();
        List<TytExcellentGoodsCardUserDetail> tytExcellentGoodsCardUserDetailList = tytExcellentGoodsCardUserDetailMapper.getAllNoUseCarListByUserId(userId, pageSize * (pageNum - 1), pageSize);
        for (TytExcellentGoodsCardUserDetail tytExcellentGoodsCardUserDetail : tytExcellentGoodsCardUserDetailList) {
            TytExcellentGoodsCardUserDetailVO tytExcellentGoodsCardUserDetailVO = new TytExcellentGoodsCardUserDetailVO();
            try {
                BeanUtils.copyProperties(tytExcellentGoodsCardUserDetailVO, tytExcellentGoodsCardUserDetail);
            } catch (Exception e) {
                continue;
            }
            tytExcellentGoodsCardUserDetailVO.setIsCanUse(false);
            if (tytExcellentGoodsCardUserDetailVO.getType() == 2
                    && (tytExcellentGoodsCardUserDetailVO.getValidDateBegin().compareTo(new Date()) <= 0)
                    && (tytExcellentGoodsCardUserDetailVO.getValidDateEnd().compareTo(new Date()) >= 0)) {
                tytExcellentGoodsCardUserDetailVO.setIsCanUse(true);
            }
            getMinRefreshInterval(tytExcellentGoodsCardUserDetailVO);
            result.add(tytExcellentGoodsCardUserDetailVO);
        }
        return result;
    }



    /**
     * 计算最小间隔
     *
     * @param tytExcellentGoodsCardUserDetailVO
     * @return int
     */
    private void getMinRefreshInterval(TytExcellentGoodsCardUserDetailVO tytExcellentGoodsCardUserDetailVO) {
        Integer minFirstRefreshInterval = tytExcellentGoodsCardUserDetailVO.getFirstRefreshInterval();
        Integer minSecondRefreshInterval = tytExcellentGoodsCardUserDetailVO.getSecondRefreshInterval();
        if(minFirstRefreshInterval!=null && minFirstRefreshInterval>0  && minSecondRefreshInterval!=null && minSecondRefreshInterval>0){
            tytExcellentGoodsCardUserDetailVO.setFirstRefreshInterval(Math.min(minFirstRefreshInterval, minSecondRefreshInterval));
        }
    }

    @Override
    public TytExcellentGoodsCardUserDetailCanUseCountVO getAllCanUseCarCountNumMax100AndLimitTimeByUserId(Long userId) {
        TytExcellentGoodsCardUserDetailCanUseCountVO result = tytExcellentGoodsCardUserDetailMapper.getAllCanUseCarCountNumMax100AndLimitTimeByUserId(userId);
        if (result == null) {
            result = new TytExcellentGoodsCardUserDetailCanUseCountVO(0, null);
        }
        return result;
    }

    @Override
    public List<TytExcellentGoodsCardUserDetail> getAllCanUseCarListByUserId(Long userId) {
        List<TytExcellentGoodsCardUserDetail> result = tytExcellentGoodsCardUserDetailMapper.getAllCanUseCarListByUserId(userId);
        if (result == null) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public List<TytExcellentGoodsCardUserDetail> getAllCanUseCarListByUserIdPage(Long userId, Integer pageNum) {
        int pageSize = 10;
        List<TytExcellentGoodsCardUserDetail> result = tytExcellentGoodsCardUserDetailMapper.getAllCanUseCarListByUserIdPage(userId, pageSize * (pageNum - 1), pageSize);
        if (result == null) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public void updateType(Long excellentCardId, ExcellentCardTypeEnum excellentCardTypeEnum) {
        TytExcellentGoodsCardUserDetail cardUserDetail = tytExcellentGoodsCardUserDetailMapper.selectByPrimaryKey(excellentCardId);
        if (cardUserDetail != null) {
            tytExcellentGoodsCardUserDetailMapper.updateType(excellentCardId, excellentCardTypeEnum.getCode());
        }
    }

    @Override
    public Boolean checkType(Long excellentCardId, Long userId) {
        TytExcellentGoodsCardUserDetail goodsCardUserDetail = tytExcellentGoodsCardUserDetailMapper.selectByPrimaryKey(excellentCardId);
        if (goodsCardUserDetail == null
                || !Objects.equals(ExcellentCardTypeEnum.not_used.getCode(), goodsCardUserDetail.getType())
                || !Objects.equals(userId, goodsCardUserDetail.getUserId())) {
            return false;
        }
        Date now = new Date();
        return (now.after(goodsCardUserDetail.getValidDateBegin()) && now.before(goodsCardUserDetail.getValidDateEnd()));
    }

    @Override
    public int getAllCanUseCardCountNumByUserId(Long userId) {
        return tytExcellentGoodsCardUserDetailMapper.getAllCanUseCardCountNumByUserId(userId);
    }


    @Override
    public Long saveExcellentGoodsPriceCard(Long userId, boolean isNew) throws Exception {
        if (!isNew) {
            Long detailId = tytExcellentGoodsCardUserDetailMapper.getPriceCardIdByUserId(userId);
            if (null != detailId) {
                return detailId;
            }
        }
        int configId = configService.getIntValue(EXCELLENT_GOODS_CARD_AUTO_ID, 0);
        if (configId <= 0) {
            return 0L;
        }
        TytExcellentGoodsCardConfig config = excellentGoodsCardConfigMapper.selectByPrimaryKey(configId);
        if (null == config) {
            return 0L;
        }
        TytExcellentGoodsCardUserDetail userDetail = TytExcellentGoodsCardUserDetail.builder()
                .configId(config.getId())
                .importId(0L)
                .userId(userId)
                .type(2)
                .title(config.getTitle())
                .content(config.getContent())
                .firstRefreshTimes(config.getFirstRefreshTimes())
                .firstRefreshInterval(config.getFirstRefreshInterval())
                .secondRefreshTimes(config.getSecondRefreshTimes())
                .secondRefreshInterval(config.getSecondRefreshInterval())
                .validDateBegin(TimeUtil.weeHours(new Date(), 0))
                .validDateEnd(TimeUtil.weeHours(TimeUtil.addDay(new Date(), 30), 1))
                .createTime(new Date())
                .createUserId(0L)
                .createUserName("系统")
                .useType(2)
                .build();
        tytExcellentGoodsCardUserDetailMapper.insertSelective(userDetail);
        return userDetail.getId();

    }

}
