package com.tyt.excellentgoodshomepage.service;

import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetail;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetailVO;
import com.tyt.transport.enums.ExcellentCardTypeEnum;

import java.util.List;

public interface ExcellentGoodsService {

    /** 获取未使用的卡列表数据（所有卡有效期大于今天但未使用的卡（包括未来的卡），不包括已过期）
     *
     * @param userId id
     * @param pageNum 页码
     * @return TytExcellentGoodsCardUserDetailVO
     */
    List<TytExcellentGoodsCardUserDetailVO> getAllNoUseCarListByUserId(Long userId, Integer pageNum);

    /** 所有可以使用的卡总数，最大总数为100，减小查询扫描的数据量
     *
     * @param userId 用户ID
     * @return TytExcellentGoodsCardUserDetailCanUseCountVO
     */
    TytExcellentGoodsCardUserDetailCanUseCountVO getAllCanUseCarCountNumMax100AndLimitTimeByUserId(Long userId);

    /** 所有可以使用的卡列表（当前时间在有效期范围内并且未使用的卡，按过期时间由近到远、一阶段刷新次数由大到小、二阶段刷新次数由大到小排序）
     *
     * @param userId id
     * @param pageNum 页码
     * @return TytExcellentGoodsCardUserDetail
     */
    List<TytExcellentGoodsCardUserDetail> getAllCanUseCarListByUserIdPage(Long userId, Integer pageNum);

    /** 所有可以使用的卡列表（当前时间在有效期范围内并且未使用的卡，按过期时间由近到远、一阶段刷新次数由大到小、二阶段刷新次数由大到小排序）
     *
     * @param userId id
     * @return TytExcellentGoodsCardUserDetail
     */
    List<TytExcellentGoodsCardUserDetail> getAllCanUseCarListByUserId(Long userId);

    /**
     * 更新优车发货卡用户明细表状态
     *
     * @param excellentCardId xx
     * @param excellentCardTypeEnum xx
     */
    void updateType(Long excellentCardId, ExcellentCardTypeEnum excellentCardTypeEnum);

    /**
     * 根据id获取用户明细
     *
     * @param excellentCardId xx
     * @param userId xx
     * @return xx
     */
    Boolean checkType(Long excellentCardId,Long userId);

    /** 所有可以使用的卡总数，最大总数无限制
     *
     * @param userId 用户ID
     * @return count
     */
    int getAllCanUseCardCountNumByUserId(Long userId);

    /**
     * 获取优车定价卡
     * @param userId 用户id
     * @param isNew 是否返回新卡
     * @return long
     * @throws Exception
     */
    Long saveExcellentGoodsPriceCard(Long userId, boolean isNew) throws Exception;
}
