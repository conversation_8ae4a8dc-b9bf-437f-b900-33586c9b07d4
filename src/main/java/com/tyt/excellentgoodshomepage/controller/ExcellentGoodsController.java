package com.tyt.excellentgoodshomepage.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.excellentgoodshomepage.service.ExcellentGoodsService;
import com.tyt.model.ResultMsgBean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/plat/excellentGoods/homePage/")
public class ExcellentGoodsController extends BaseController {

    public ExcellentGoodsController(ExcellentGoodsService excellentGoodsService) {
        this.excellentGoodsService = excellentGoodsService;
    }

    private final ExcellentGoodsService excellentGoodsService;

    @RequestMapping("getAllNoUseCarListByUserId")
    @ResponseBody
    public ResultMsgBean getAllNoUseCarListByUserId(BaseParameter baseParameter, Integer pageNum) {
        //分页查询
        if (baseParameter.getUserId() == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        return ResultMsgBean.successResponse(excellentGoodsService.getAllNoUseCarListByUserId(baseParameter.getUserId(), pageNum));
    }

    @RequestMapping("getAllCanUseCarCountNumByUserId")
    @ResponseBody
    public ResultMsgBean getAllCanUseCarCountNumByUserId(BaseParameter baseParameter) {
        if (baseParameter.getUserId() == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        return ResultMsgBean.successResponse(excellentGoodsService.getAllCanUseCarCountNumMax100AndLimitTimeByUserId(baseParameter.getUserId()));
    }

    @RequestMapping("getAllCanUseCarListByUserId")
    @ResponseBody
    public ResultMsgBean getAllCanUseCarListByUserId(BaseParameter baseParameter, Integer pageNum) {
        //分页查询
        if (baseParameter.getUserId() == null) {
            return ResultMsgBean.failResponse(ResponseCodeEnum.参数错误.info());
        }
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        return ResultMsgBean.successResponse(excellentGoodsService.getAllCanUseCarListByUserIdPage(baseParameter.getUserId(), pageNum));
    }

}
