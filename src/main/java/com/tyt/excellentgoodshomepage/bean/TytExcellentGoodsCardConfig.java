package com.tyt.excellentgoodshomepage.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_excellent_goods_card_config")
public class TytExcellentGoodsCardConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 一阶段刷新次数
     */
    @Column(name = "first_refresh_times")
    private Integer firstRefreshTimes;

    /**
     * 一阶段刷新间隔
     */
    @Column(name = "first_refresh_interval")
    private Integer firstRefreshInterval;

    /**
     * 二阶段刷新次数
     */
    @Column(name = "second_refresh_times")
    private Integer secondRefreshTimes;

    /**
     * 二阶段刷新间隔
     */
    @Column(name = "second_refresh_interval")
    private Integer secondRefreshInterval;

    /**
     * 有效期类型 1：固定日期范围；2：发卡后N天
     */
    @Column(name = "valid_type")
    private Integer validType;

    /**
     * N天后过期的N
     */
    @Column(name = "valid_days")
    private Integer validDays;

    /**
     * 有效期开始时间
     */
    @Column(name = "valid_date_begin")
    private Date validDateBegin;

    /**
     * 有效期结束时间
     */
    @Column(name = "valid_date_end")
    private Date validDateEnd;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人用户名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 创建人ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人用户名
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 更新人ID
     */
    @Column(name = "update_user_id")
    private Long updateUserId;

}