package com.tyt.excellentgoodshomepage.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_excellent_goods_card_user_import")
public class TytExcellentGoodsCardUserImport {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 优车发货卡配置表ID
     */
    @Column(name = "config_id")
    private Long configId;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 发放状态 1：待发放；2：发放中；3：已发放
     */
    private Integer type;

    /**
     * 一阶段刷新次数
     */
    @Column(name = "first_refresh_times")
    private Integer firstRefreshTimes;

    /**
     * 一阶段刷新间隔
     */
    @Column(name = "first_refresh_interval")
    private Integer firstRefreshInterval;

    /**
     * 二阶段刷新次数
     */
    @Column(name = "second_refresh_times")
    private Integer secondRefreshTimes;

    /**
     * 二阶段刷新间隔
     */
    @Column(name = "second_refresh_interval")
    private Integer secondRefreshInterval;

    /**
     * 计划发放数量
     */
    @Column(name = "plan_grant_num")
    private Integer planGrantNum;

    /**
     * 实际发放数量
     */
    @Column(name = "reality_grant_num")
    private Integer realityGrantNum;

    /**
     * 有效期开始时间
     */
    @Column(name = "valid_date_begin")
    private Date validDateBegin;

    /**
     * 有效期结束时间
     */
    @Column(name = "valid_date_end")
    private Date validDateEnd;

    /**
     * 导入时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 导入人用户名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 导入人ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人用户名
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 更新人ID
     */
    @Column(name = "update_user_id")
    private Long updateUserId;
}