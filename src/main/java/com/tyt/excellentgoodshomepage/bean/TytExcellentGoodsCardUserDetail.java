package com.tyt.excellentgoodshomepage.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_excellent_goods_card_user_detail")
public class TytExcellentGoodsCardUserDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 优车发货卡配置表ID
     */
    @Column(name = "config_id")
    private Long configId;

    /**
     * 优车发货卡用户汇总信息表ID
     */
    @Column(name = "import_id")
    private Long importId;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 发放状态 1：待领取；2：待使用；3：已使用（待领取状态暂未启用，发卡后将直接进入待使用状态）
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 一阶段刷新次数
     */
    @Column(name = "first_refresh_times")
    private Integer firstRefreshTimes;

    /**
     * 一阶段刷新间隔
     */
    @Column(name = "first_refresh_interval")
    private Integer firstRefreshInterval;

    /**
     * 二阶段刷新次数
     */
    @Column(name = "second_refresh_times")
    private Integer secondRefreshTimes;

    /**
     * 二阶段刷新间隔
     */
    @Column(name = "second_refresh_interval")
    private Integer secondRefreshInterval;

    /**
     * 有效期开始时间
     */
    @Column(name = "valid_date_begin")
    private Date validDateBegin;

    /**
     * 有效期结束时间
     */
    @Column(name = "valid_date_end")
    private Date validDateEnd;

    /**
     * 发放时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 发放人用户名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 发放人ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人用户名
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 更新人ID
     */
    @Column(name = "update_user_id")
    private Long updateUserId;

    /**
     * 使用类型 1优车发货卡 2 优车定价卡
     */
    @Column(name = "use_type")
    private Integer useType;

}