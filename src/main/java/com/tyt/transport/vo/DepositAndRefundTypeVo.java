package com.tyt.transport.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.plat.utils.BigDecimalSerialize;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订金及退款方式
 * <AUTHOR>
 * @since 2024-05-30 10:36
 */
@Builder
@Data
public class DepositAndRefundTypeVo {
    /**
     * 订金
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal deposit;
    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;
    /**
     * 类型：1-智能订金，0-手动订金
     */
    private Integer depositType;
}
