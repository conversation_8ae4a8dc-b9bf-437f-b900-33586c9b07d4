package com.tyt.transport.vo;

import lombok.Data;

/**
 * @author: helian
 * @since: 2024/01/12 20:11
 */
@Data
public class CoverGoodsConfigVo {

    /**
     * 配置名称
     */
    private String name;

    /**
     * 配置明细  configCode为1时：  1：每次发货默认勾选，2：每次发货默认不勾选 3：默认勾选，记录用户操作状态;configCode为2时 0：关  1：开
     */
    private Integer configItem;

    /**
     * 配置类型标识;1：勾选配置  2：引导配置
     */
    private Integer configCode;

    /**
     * 配置额外信息，如引导文案
     */
    private String configExt;

}
