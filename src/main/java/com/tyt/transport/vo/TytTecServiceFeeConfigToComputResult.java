package com.tyt.transport.vo;

import com.tyt.plat.entity.base.TytTecServiceFeeConfig;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class TytTecServiceFeeConfigToComputResult extends TytTecServiceFeeConfig implements Serializable {

    /**
     * 好货标签类型：1-全部，2-部分
     */
    private Integer goodTransportLabelType;

    /**
     * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:其他
     */
    private Integer goodTransportLabel;

    /**
     * 路线类型：0-通用，1-指定路线
     */
    private Integer routeType;

    /**
     * 出发地
     */
    private String startCity;

    /**
     * 目的地
     */
    private String destCity;

    /**
     * 里程数低值
     */
    private BigDecimal distanceMin;

    /**
     * 里程数高值
     */
    private BigDecimal distanceMax;

    /**
     * 是否是多车找货并且有支付成功订单
     */
    private Boolean isMultiCarHavePayOrder = false;

    /**
     * 折后技术服务费
     */
    private BigDecimal tecServiceFee;

    /**
     * 折前技术服务费，after写错了
     */
    private BigDecimal tecServiceFeeBeforeDiscount;

    //超时免佣准备状态 0:未准备就绪；1:准备就绪
    private Boolean freeTecServiceFeeIsReady;

    //超时免佣准备就绪时间
    private Date freeTecServiceFeeIsReadyTime;

    //免佣原因：
    private List<Integer> needFreeTecTypeList;

    //是否使用抽佣分数分段配置
    private Boolean useCommissionScoreStageConfig;

    //抽佣分数
    private BigDecimal commissionScore;

    //无价货源优车指导价
    private String goodCarPriceTransportCarryPrice;

    private Integer transportProportionNum;

    private BigDecimal priceMin;

    /**
     * 运费高值
     */
    private BigDecimal priceMax;

    /**
     * 技术服务费抽取运费百分比
     */
    private BigDecimal tecServiceFeeRate;

    /**
     * 技术服务费低值
     */
    private BigDecimal tecServiceFeeMin;

    /**
     * 技术服务费高值
     */
    private BigDecimal tecServiceFeeMax;

    private Integer discountTime; // 折扣时间（距首发X分钟内）

    private BigDecimal discount; // 限时折扣（最终折扣），0-10

    private String discountConfig; //折扣配置（用于定时任务打折）

    private String allDiscount; //所有折扣配置

}