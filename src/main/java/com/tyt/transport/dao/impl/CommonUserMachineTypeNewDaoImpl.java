package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCommonUserMachineTypeNew;
import com.tyt.transport.dao.CommonUserMachineTypeNewDao;

@Repository("commonUserMachineTypeNewDao")
public class CommonUserMachineTypeNewDaoImpl extends BaseDaoImpl<TytCommonUserMachineTypeNew, Long> implements CommonUserMachineTypeNewDao {
	public CommonUserMachineTypeNewDaoImpl() {
		this.setEntityClass(TytCommonUserMachineTypeNew.class);
	}
}
