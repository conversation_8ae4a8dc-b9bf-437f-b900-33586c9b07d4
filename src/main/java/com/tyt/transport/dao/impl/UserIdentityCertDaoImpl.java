package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.UserIdentityCert;
import com.tyt.transport.dao.UserIdentityCertDao;

@Repository("userIdentityCertDao")
public class UserIdentityCertDaoImpl extends BaseDaoImpl<UserIdentityCert, Long> implements UserIdentityCertDao{

	public UserIdentityCertDaoImpl() {
		this.setEntityClass(UserIdentityCert.class);
	}
}
