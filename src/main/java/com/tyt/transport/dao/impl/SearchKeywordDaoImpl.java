package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytSearchKeyword;
import com.tyt.transport.dao.SearchKeywordDao;

@Repository("searchKeywordDao")
public class SearchKeywordDaoImpl extends BaseDaoImpl<TytSearchKeyword, Long> implements SearchKeywordDao {
	public SearchKeywordDaoImpl() {
		this.setEntityClass(TytSearchKeyword.class);
	}
}
