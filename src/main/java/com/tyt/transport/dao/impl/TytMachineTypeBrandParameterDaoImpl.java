package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytMachineTypeBrandParameter;
import com.tyt.transport.dao.TytMachineTypeBrandParameterDao;
import org.springframework.stereotype.Repository;

@Repository("tytMachineTypeBrandParameterDao")
public class TytMachineTypeBrandParameterDaoImpl extends BaseDaoImpl<TytMachineTypeBrandParameter, Integer> implements TytMachineTypeBrandParameterDao {

    public TytMachineTypeBrandParameterDaoImpl() {
        this.setEntityClass(TytMachineTypeBrandParameter.class);
    }

}
