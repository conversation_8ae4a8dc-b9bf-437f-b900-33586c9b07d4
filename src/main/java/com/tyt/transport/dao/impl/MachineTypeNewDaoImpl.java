package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytMachineTypeNew;
import com.tyt.transport.dao.MachineTypeNewDao;

@Repository("machineTypeNewDao")
public class MachineTypeNewDaoImpl extends BaseDaoImpl<TytMachineTypeNew, Long> implements MachineTypeNewDao {

	public MachineTypeNewDaoImpl() {
		this.setEntityClass(TytMachineTypeNew.class);
	}
}
