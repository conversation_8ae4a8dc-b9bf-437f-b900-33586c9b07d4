package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.UserIdentityCertLog;
import com.tyt.transport.dao.UserIdentityCertLogDao;
import org.springframework.stereotype.Repository;

@Repository("userIdentityCertLogDao")
public class UserIdentityCertLogDaoImpl extends BaseDaoImpl<UserIdentityCertLog, Long> implements UserIdentityCertLogDao {

	public UserIdentityCertLogDaoImpl() {
		this.setEntityClass(UserIdentityCertLog.class);
	}
}
