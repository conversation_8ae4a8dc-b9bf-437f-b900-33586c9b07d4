package com.tyt.transport.dao.impl;


import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TransportNullifyBean;
import com.tyt.transport.dao.TransportNullifyDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: Administrator Date: 13-11-10 Time: 下午4:23
 */
@Repository("transportNullifyDao")
public class TransportNullifyDaoImpl extends BaseDaoImpl<TransportNullifyBean, Long> implements TransportNullifyDao {
	public TransportNullifyDaoImpl() {
		this.setEntityClass(TransportNullifyBean.class);
	}

	protected Log logger = LogFactory.getLog(this.getClass());

	@Override
	public List<String> verifyNullifyKeywordByTaskcontent(List<String> keywords) {
		StringBuffer sql = new StringBuffer("SELECT entity.keyword_value value FROM tyt_nullify_keyword entity WHERE entity.keyword_value IN(:keywords)");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("keywords", keywords.toArray());

		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
		mapType.put("value", Hibernate.STRING);

		List<String> result = this.findAll(sql.toString(),mapType,map);
		return  result;
	}
	@Override
	public List<String> getAllNullifyKeyword() {
		StringBuffer sql = new StringBuffer("SELECT entity.keyword_value value FROM tyt_nullify_keyword entity");
		Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
		mapType.put("value", Hibernate.STRING);

		List<String> result = this.findAll(sql.toString(),mapType,null);
		return  result;
	}


}
