package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCommonUserMachineType;
import com.tyt.transport.dao.CommonUserMachineTypeDao;

@Repository("commonUserMachineTypeDao")
public class CommonUserMachineTypeDaoImpl extends BaseDaoImpl<TytCommonUserMachineType, Long> implements CommonUserMachineTypeDao {
	public CommonUserMachineTypeDaoImpl() {
		this.setEntityClass(TytCommonUserMachineType.class);
	}
}
