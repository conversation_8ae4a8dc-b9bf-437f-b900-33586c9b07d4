package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TransportMtChangeLog;
import com.tyt.transport.dao.TransportMtChangeLogDao;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository("transportMtChangeLogDao")
public class TransportMtChangeLogDaoImpl extends BaseDaoImpl<TransportMtChangeLog, Long> implements TransportMtChangeLogDao {

    public TransportMtChangeLogDaoImpl() {
        this.setEntityClass(TransportMtChangeLog.class);
    }

}
