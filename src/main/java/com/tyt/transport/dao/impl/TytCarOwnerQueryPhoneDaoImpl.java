package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCarOwnerQueryPhone;
import com.tyt.transport.dao.TytCarOwnerQueryPhoneDao;

@Repository("tytCarOwnerQueryPhoneDao")
public class TytCarOwnerQueryPhoneDaoImpl extends BaseDaoImpl<TytCarOwnerQueryPhone, Long> implements TytCarOwnerQueryPhoneDao {
	public TytCarOwnerQueryPhoneDaoImpl() {
		this.setEntityClass(TytCarOwnerQueryPhone.class);
	}
}
