package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytKeywordMatchesNew;
import com.tyt.transport.dao.KeywordMatchesNewDao;

@Repository("keywordMatchesNewDao")
public class KeywordMatchesNewDaoImpl extends BaseDaoImpl<TytKeywordMatchesNew, Long> implements KeywordMatchesNewDao {
	public KeywordMatchesNewDaoImpl() {
		this.setEntityClass(TytKeywordMatchesNew.class);
	}
}
