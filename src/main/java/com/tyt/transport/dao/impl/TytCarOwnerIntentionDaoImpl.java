package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCarOwnerIntention;
import com.tyt.transport.dao.TytCarOwnerIntentionDao;
import org.springframework.stereotype.Repository;

@Repository("tytCarOwnerIntentionDao")
public class TytCarOwnerIntentionDaoImpl extends BaseDaoImpl<TytCarOwnerIntention, Long> implements TytCarOwnerIntentionDao {
	public TytCarOwnerIntentionDaoImpl() {
		this.setEntityClass(TytCarOwnerIntention.class);
	}
}
