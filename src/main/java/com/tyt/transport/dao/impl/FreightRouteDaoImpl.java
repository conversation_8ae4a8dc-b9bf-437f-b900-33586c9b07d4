package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.FreightRecord;
import com.tyt.model.FreightRoute;
import com.tyt.transport.dao.FreightRecordDao;
import com.tyt.transport.dao.FreightRouteDao;
import org.springframework.stereotype.Repository;

/**
 * 
 * @date 2017年8月29日上午11:58:01
 * @description
 */
@Repository("freightRouteDao")
public class FreightRouteDaoImpl extends BaseDaoImpl<FreightRoute, Long> implements FreightRouteDao {

	public FreightRouteDaoImpl() {
		this.setEntityClass(FreightRoute.class);
	}
}
