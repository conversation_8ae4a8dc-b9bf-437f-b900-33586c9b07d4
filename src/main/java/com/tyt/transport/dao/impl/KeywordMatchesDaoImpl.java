package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytKeywordMatches;
import com.tyt.transport.dao.KeywordMatchesDao;

@Repository("keywordMatchesDao")
public class KeywordMatchesDaoImpl extends BaseDaoImpl<TytKeywordMatches, Long> implements KeywordMatchesDao {
	public KeywordMatchesDaoImpl() {
		this.setEntityClass(TytKeywordMatches.class);
	}
}
