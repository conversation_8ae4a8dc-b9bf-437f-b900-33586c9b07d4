package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytMachineType;
import com.tyt.transport.dao.MachineTypeDao;

@Repository("machineTypeDao")
public class MachineTypeDaoImpl extends BaseDaoImpl<TytMachineType, Long> implements MachineTypeDao {

	public MachineTypeDaoImpl() {
		this.setEntityClass(TytMachineType.class);
	}
}
