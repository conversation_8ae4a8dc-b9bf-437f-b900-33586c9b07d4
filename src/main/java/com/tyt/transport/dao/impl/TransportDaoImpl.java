package com.tyt.transport.dao.impl;

import java.math.BigInteger;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.Transport;
import com.tyt.transport.dao.TransportDao;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午4:23
 */
@SuppressWarnings({ "unchecked", "rawtypes" })
@Repository("transportDao")
public class TransportDaoImpl  extends BaseDaoImpl<Transport, Long>  implements  TransportDao {
       public TransportDaoImpl() {
           this.setEntityClass(Transport.class);
       }
    protected Log logger = LogFactory.getLog(this.getClass());

    @Override
    public List<String> getTelList(String clause) {
        StringBuffer sql = new StringBuffer();
        sql.append("select distinct entity.tel from Transport entity where ").append(clause);

        List telList = this.getHibernateTemplate().find(sql.toString());
        return telList;
    }

    @Override
    public List<Long> getQqList(String clause) {
        StringBuffer sql = new StringBuffer();
        sql.append("select distinct entity.pubQQ from Transport entity where ").append(clause);

        List telList = this.getHibernateTemplate().find(sql.toString());
        return telList;
    }

	@Override
	public List<Long> getInfoIdList(String clause) {
    	StringBuffer sql = new StringBuffer();
        sql.append("select entity.id from Transport entity where ").append(clause);

        List idList = this.getHibernateTemplate().find(sql.toString());
        return idList;
	}

    @Override
    public void deleteTransportBySrcMsgId(Long srcMsgId) {
        String sql = "UPDATE tyt_transport SET mtime = now(),is_delete = 1 WHERE src_msg_id = ?";
        executeUpdateSql(sql, new Object[]{srcMsgId});
    }
}


