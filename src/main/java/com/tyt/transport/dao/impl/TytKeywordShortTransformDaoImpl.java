package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytKeywordShortTransform;
import com.tyt.transport.dao.TytKeywordShortTransformDao;
import org.springframework.stereotype.Repository;

@Repository("tytKeywordShortTransformDao")
public class TytKeywordShortTransformDaoImpl extends BaseDaoImpl<TytKeywordShortTransform, Integer> implements TytKeywordShortTransformDao {

	public TytKeywordShortTransformDaoImpl() {
		this.setEntityClass(TytKeywordShortTransform.class);
	}
}
