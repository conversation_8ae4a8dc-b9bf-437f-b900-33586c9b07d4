package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TransportVary;
import com.tyt.transport.dao.TransportVaryDao;

@Repository("transportVaryDao")
public class TransportVaryDaoImpl extends BaseDaoImpl<TransportVary, Long>  implements  TransportVaryDao   {
	
	  public TransportVaryDaoImpl() {
	        this.setEntityClass(TransportVary.class);
	    }

}

