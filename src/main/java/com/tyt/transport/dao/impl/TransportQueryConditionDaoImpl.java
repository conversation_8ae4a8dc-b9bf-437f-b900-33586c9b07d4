package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TransportQueryCondition;
import com.tyt.transport.dao.TransportQueryConditionDao;
@Repository("transportQueryConditionDao")
public class TransportQueryConditionDaoImpl extends BaseDaoImpl<TransportQueryCondition,Long> implements TransportQueryConditionDao{
	
	public TransportQueryConditionDaoImpl(){
		   this.setEntityClass(TransportQueryCondition.class);
	   }

}
