package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.EmployeeMessage;
import com.tyt.transport.dao.EmployeeMessageDao;

@Repository("employeeMessageDao")
public class EmployeeMessageDaoImpl extends BaseDaoImpl<EmployeeMessage, Long> implements EmployeeMessageDao {
	public EmployeeMessageDaoImpl() {
		this.setEntityClass(EmployeeMessage.class);
	}
}
