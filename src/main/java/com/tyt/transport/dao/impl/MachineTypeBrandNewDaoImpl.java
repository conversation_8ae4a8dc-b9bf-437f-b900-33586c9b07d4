package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytMachineTypeBrandNew;
import com.tyt.transport.dao.MachineTypeBrandNewDao;
import org.springframework.stereotype.Repository;

@Repository("machineTypeBrandNewDao")
public class MachineTypeBrandNewDaoImpl extends BaseDaoImpl<TytMachineTypeBrandNew, Integer> implements MachineTypeBrandNewDao {

	public MachineTypeBrandNewDaoImpl() {
		this.setEntityClass(TytMachineTypeBrandNew.class);
	}
}
