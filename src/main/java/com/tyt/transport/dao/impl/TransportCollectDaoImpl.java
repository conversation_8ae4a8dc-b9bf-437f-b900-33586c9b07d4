package com.tyt.transport.dao.impl;

import java.math.BigInteger;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TransportCollect;
import com.tyt.transport.dao.TransportCollectDao;

@Repository("transportCollectDao")
public class TransportCollectDaoImpl extends BaseDaoImpl<TransportCollect, Long>  implements  TransportCollectDao {
 
	 public TransportCollectDaoImpl() {
         this.setEntityClass(TransportCollect.class);
     }
	@SuppressWarnings("unchecked")
	@Override
	public List<Long> getInfoIdList(final String sql) {
		 HibernateTemplate tmpl = getHibernateTemplate();
		 List<Long> idList=new ArrayList<Long>();
		 List<BigInteger> list=(List<BigInteger>) tmpl.execute(new HibernateCallback<Object>() {
	            @Override
	            public Object doInHibernate(Session session)
	                    throws HibernateException, SQLException {
	            	SQLQuery query = session.createSQLQuery(
	            			"select info_id from tyt_transport_collect where 1=1 and "+sql);
	                return query.list();
	            }
	        });
		 for(BigInteger id:list){
			 idList.add(id.longValue());
		 }
		 return idList;
	}
	@Override
	public void delCollect(final String cellPhone, final Long infoId) {
		HibernateTemplate tmpl = getHibernateTemplate();
		tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
            	SQLQuery query = session.createSQLQuery(
            			"update tyt_transport_collect  set status=0 where cell_phone="+cellPhone
            			+" and info_id="+infoId
            			);
                query.executeUpdate();
                return null;
            }
        });
	}
	@Override
	public void updateId(final Long oldId, final Long newId) {
		HibernateTemplate tmpl = getHibernateTemplate();
		tmpl.execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
            	SQLQuery query = session.createSQLQuery(
            			"update tyt_transport_collect  set info_id="+newId+" where info_id="+oldId
            			);
                query.executeUpdate();
                return null;
            }
        });
	}
	
	@Override
	public void delCollect(final Long userId, final Long id) throws Exception {
		getHibernateTemplate().execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
            	SQLQuery query = session.createSQLQuery("update tyt_transport_collect set status=0 where id=?");
            	return query.setParameter(0, id).executeUpdate();
            }
        });
		
	}
	
	@Override
	public void updateStatus(final Long infoId, final Integer status) {
		getHibernateTemplate().execute(new HibernateCallback<Object>() {
            @Override
            public Object doInHibernate(Session session)
                    throws HibernateException, SQLException {
            	SQLQuery query = session.createSQLQuery(
            			"update tyt_transport_collect  set status=? where info_id=?"
            			);
                query.setParameter(0, status).setParameter(1, infoId).executeUpdate();
                return null;
            }
        });
		
	}

}
