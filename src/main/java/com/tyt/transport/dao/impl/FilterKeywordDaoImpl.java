package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytFilterKeyword;
import com.tyt.transport.dao.FilterKeywordDao;

@Repository("filterKeywordDao")
public class FilterKeywordDaoImpl extends BaseDaoImpl<TytFilterKeyword, Long> implements FilterKeywordDao {

	public FilterKeywordDaoImpl() {
		this.setEntityClass(TytFilterKeyword.class);
	}
}
