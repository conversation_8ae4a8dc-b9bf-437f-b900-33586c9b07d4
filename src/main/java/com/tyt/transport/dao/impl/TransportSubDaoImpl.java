package com.tyt.transport.dao.impl;


import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TransportNullifyBean;
import com.tyt.model.TransportSubBean;
import com.tyt.transport.dao.TransportNullifyDao;
import com.tyt.transport.dao.TransportSubDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: Administrator Date: 13-11-10 Time: 下午4:23
 */
@Repository("transportSubDao")
public class TransportSubDaoImpl extends BaseDaoImpl<TransportSubBean, Long> implements TransportSubDao {
	public TransportSubDaoImpl() {
		this.setEntityClass(TransportSubBean.class);
	}
	protected Log logger = LogFactory.getLog(this.getClass());


}
