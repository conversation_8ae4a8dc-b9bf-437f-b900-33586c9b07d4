package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCallPhoneLimitNew;
import com.tyt.transport.dao.CallPhoneLimitNewDao;

/**
 * 
 * <AUTHOR>
 * @date 2018年4月12日下午1:52:57
 * @description
 */
@Repository("callPhoneLimitNewDao")
public class CallPhoneLimitNewDaoImpl extends BaseDaoImpl<TytCallPhoneLimitNew, Long> implements CallPhoneLimitNewDao {

	public CallPhoneLimitNewDaoImpl() {
		this.setEntityClass(TytCallPhoneLimitNew.class);
	}
}
