package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCarriageFeedback;
import com.tyt.transport.dao.TytCarriageFeedbackDao;
import org.springframework.stereotype.Repository;

/**
 * @ClassName TytCarriageFeedbackDaoImpl
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/9/15 13:12
 * @Verdion 1.0
 **/
@Repository("tytCarriageFeedbackDao")
public class TytCarriageFeedbackDaoImpl extends BaseDaoImpl<TytCarriageFeedback, Long> implements TytCarriageFeedbackDao {

    public TytCarriageFeedbackDaoImpl(){
        this.setEntityClass(TytCarriageFeedback.class);
    }

}
