package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TransportDone;
import com.tyt.transport.dao.TransportDoneDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;

/**
 * User: Administrator
 * Date: 19-10-23
 * Time: 下午6:23
 */
@SuppressWarnings({"unchecked", "rawtypes"})
@Repository("transportDoneDao")
public class TransportDoneDaoImpl extends BaseDaoImpl<TransportDone, Long> implements TransportDoneDao {
    public TransportDoneDaoImpl() {
        this.setEntityClass(TransportDone.class);
    }

    protected Log logger = LogFactory.getLog(this.getClass());


}

	
