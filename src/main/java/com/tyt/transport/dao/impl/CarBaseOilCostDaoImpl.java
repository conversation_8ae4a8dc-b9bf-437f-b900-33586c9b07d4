package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytCarBaseOilCost;
import com.tyt.transport.dao.CarBaseOilCostDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;

/**
 * User: tianjw
 * Date: 17-05-23
 */
@Repository("carBaseOilCostDao")
public class CarBaseOilCostDaoImpl extends BaseDaoImpl<TytCarBaseOilCost, Long> implements CarBaseOilCostDao {
   public CarBaseOilCostDaoImpl() {
       this.setEntityClass(TytCarBaseOilCost.class);
   }
   protected Log logger = LogFactory.getLog(this.getClass());
       

}

	
