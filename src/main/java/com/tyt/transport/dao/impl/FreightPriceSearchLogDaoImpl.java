package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.FreightPriceSearchLog;
import com.tyt.transport.dao.FreightPriceSearchLogDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;

/**
 * User: tianjw
 * Date: 17-05-23
 */
@Repository("freightPriceSearchLogDao")
public class FreightPriceSearchLogDaoImpl extends BaseDaoImpl<FreightPriceSearchLog, Long> implements FreightPriceSearchLogDao {
   public FreightPriceSearchLogDaoImpl() {
       this.setEntityClass(FreightPriceSearchLog.class);
   }
   protected Log logger = LogFactory.getLog(this.getClass());
       

}

	
