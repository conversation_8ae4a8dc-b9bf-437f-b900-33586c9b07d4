package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytNumberKeyword;
import com.tyt.transport.dao.NumberKeywordDao;

@Repository("numberKeywordDao")
public class NumberKeywordDaoImpl extends BaseDaoImpl<TytNumberKeyword, Long> implements NumberKeywordDao {
	public NumberKeywordDaoImpl() {
		this.setEntityClass(TytNumberKeyword.class);
	}
}
