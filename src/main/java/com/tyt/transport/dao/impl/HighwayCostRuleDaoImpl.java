package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytHighwayCostRule;
import com.tyt.transport.dao.HighwayCostRuleDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;

/**
 * User: tianjw
 * Date: 17-05-23
 */
@Repository("highwayCostRuleDao")
public class HighwayCostRuleDaoImpl extends BaseDaoImpl<TytHighwayCostRule, Long> implements HighwayCostRuleDao {
   public HighwayCostRuleDaoImpl() {
       this.setEntityClass(TytHighwayCostRule.class);
   }
   protected Log logger = LogFactory.getLog(this.getClass());

}

	
