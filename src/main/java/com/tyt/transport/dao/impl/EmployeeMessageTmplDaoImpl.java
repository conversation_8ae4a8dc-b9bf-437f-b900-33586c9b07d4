package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.EmployeeMessageTmpl;
import com.tyt.transport.dao.EmployeeMessageTmplDao;

@Repository("employeeMessageTmplDao")
public class EmployeeMessageTmplDaoImpl extends BaseDaoImpl<EmployeeMessageTmpl, Long> implements EmployeeMessageTmplDao {
	public EmployeeMessageTmplDaoImpl() {
		this.setEntityClass(EmployeeMessageTmpl.class);
	}
}
