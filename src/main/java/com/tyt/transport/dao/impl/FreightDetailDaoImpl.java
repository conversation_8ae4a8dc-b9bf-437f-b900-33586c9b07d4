package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytFreightDetailInfo;
import com.tyt.transport.dao.FreightDetailDao;
import com.tyt.transport.querybean.ProvincesCountBean;
import com.tyt.util.TimeUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Repository;

import java.awt.image.TileObserver;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: tianjw
 * Date: 17-05-23
 */
@Repository("freightDetailDao")
public class FreightDetailDaoImpl extends BaseDaoImpl<TytFreightDetailInfo, Long> implements FreightDetailDao {
   public FreightDetailDaoImpl() {
       this.setEntityClass(TytFreightDetailInfo.class);
   }
   protected Log logger = LogFactory.getLog(this.getClass());


    @Override
    public List<ProvincesCountBean> getProvincesCountList(String startProvinc, String startCity, String destProvinc, String destCity) {
        String nowDate = null;
        try {
            nowDate = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.today(),-1));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(nowDate == null){
            return null;
        }
        String sql = "SELECT `id`,`type`,`ctime`,`province`,`city`,`total_count` totalCount FROM `provinces_pub_seek_trans_count` t " +
                "WHERE t.`ctime` = '"+nowDate+"' AND ((t.`province` = :startProvinc AND t.`city` = :startCity AND t.`type` <= 2)" +
                "OR(t.`province` = :destProvinc AND t.`city` = :destCity AND t.`type` >= 3)) ORDER BY `type` ASC";

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startProvinc", startProvinc);
        map.put("startCity",startCity);
        map.put("destProvinc", destProvinc);
        map.put("destCity", destCity);

        Map<String, org.hibernate.type.Type> mapType = new HashMap<String, org.hibernate.type.Type>();
        mapType.put("id", Hibernate.LONG);
        mapType.put("type", Hibernate.INTEGER);
        mapType.put("ctime", Hibernate.TIMESTAMP);
        mapType.put("province", Hibernate.STRING);
        mapType.put("city", Hibernate.STRING);
        mapType.put("totalCount", Hibernate.INTEGER);
        List<ProvincesCountBean> result = this.search(sql.toString(),mapType,ProvincesCountBean.class,map);
        return result;
    }
}

	
