package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TransportMain;
import com.tyt.transport.dao.TransportMainDao;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository("transportMainDao")
public class TransportMainDaoImpl extends BaseDaoImpl<TransportMain, Long> implements TransportMainDao {
    public TransportMainDaoImpl() {
        this.setEntityClass(TransportMain.class);
    }

    @Override
    public void addTransportMain(Long id) throws Exception {
        String insertToMainSql = new String("insert into `tyt_transport_main` (`id`, `start_point`, `dest_point`, `task_content`, `tel`, `pub_time`, `pub_qq`, `nick_name`, `status`, `source`, `ctime`, `mtime`, `upload_cellphone`, `resend`, `start_coord`, `dest_coord`, `plat_id`, `verify_flag`, `price`, `user_id`, `price_code`, `start_coord_x`, `start_coord_y`, `dest_coord_x`, `dest_coord_y`, `start_detail_add`, `start_longitude`, `start_latitude`, `dest_detail_add`, `dest_longitude`, `dest_latitude`, `pub_date`, `goods_code`, `weight_code`, `weight`, `LENGTH`, `wide`, `high`, `is_superelevation`, `linkman`, `remark`, `distance`, `pub_goods_time`, `tel3`, `tel4`, `display_type`, `hash_code`, `is_car`, `user_type`, `pc_old_content`, `resend_counts`, `verify_photo_sign`, `user_part`, `start_city`, `src_msg_id`, `start_provinc`, `start_area`, `dest_provinc`, `dest_city`, `dest_area`, `client_version`,`is_info_fee`,`info_status`,`ts_order_no`,`release_time`,`reg_time`, `type`, `brand`, `good_type_name`, `good_number`, `is_standard`, `match_item_id`,`android_distance`,`ios_distance`,`is_display`) "
                + " select `id`, `start_point`, `dest_point`, `task_content`, `tel`, `pub_time`, `pub_qq`, `nick_name`, `status`, `source`, `ctime`, `mtime`, `upload_cellphone`, `resend`, `start_coord`, `dest_coord`, `plat_id`, `verify_flag`, `price`, `user_id`, `price_code`, `start_coord_x`, `start_coord_y`, `dest_coord_x`, `dest_coord_y`, `start_detail_add`, `start_longitude`, `start_latitude`, `dest_detail_add`, `dest_longitude`, `dest_latitude`, `pub_date`, `goods_code`, `weight_code`, `weight`, `LENGTH`, `wide`, `high`, `is_superelevation`, `linkman`, `remark`, `distance`, `pub_goods_time`, `tel3`, `tel4`, `display_type`, `hash_code`, `is_car`, `user_type`, `pc_old_content`, `resend_counts`, `verify_photo_sign`, `user_part`, `start_city`, `src_msg_id`, `start_provinc`, `start_area`, `dest_provinc`, `dest_city`, `dest_area`, `client_version`,`is_info_fee`,`info_status`,`ts_order_no`,`release_time`,`reg_time`, `type`, `brand`, `good_type_name`, `good_number`, `is_standard`, `match_item_id` ,`android_distance`,`ios_distance`,`is_display` from `tyt_transport` where id=?");
        this.executeUpdateSql(insertToMainSql, new Object[]{id});
    }

    @Override
    public void deleteTransportMainById(Long id) {
        String sql = "UPDATE tyt_transport_main SET mtime = now(),is_delete = 1 WHERE id = ?";
        executeUpdateSql(sql, new Object[]{id});
    }

    @Override
    public TransportMain getTransportMainById(Long goodsId) {
        String sql="select * from tyt_transport_main where id= ?";
        Object[] params = {goodsId};
        List<TransportMain> transportMains = this.queryForList(sql, params);
        if(transportMains!=null&&transportMains.size()>0){
            return transportMains.get(0);
        }
        return null;
    }
}


