package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytSearchWord;
import com.tyt.transport.dao.SearchWordDao;

@Repository("searchWordDao")
public class SearchWordDaoImpl extends BaseDaoImpl<TytSearchWord, Long> implements SearchWordDao {

	public SearchWordDaoImpl() {
		this.setEntityClass(TytSearchWord.class);
	}
}
