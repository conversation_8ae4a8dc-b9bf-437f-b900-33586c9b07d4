package com.tyt.transport.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytProbabilityKeyword;
import com.tyt.transport.dao.ProbabilityKeywordDao;

@Repository("probabilityKeywordDao")
public class ProbabilityKeywordDaoImpl extends BaseDaoImpl<TytProbabilityKeyword, Long> implements ProbabilityKeywordDao {
	public ProbabilityKeywordDaoImpl() {
		this.setEntityClass(TytProbabilityKeyword.class);
	}
}
