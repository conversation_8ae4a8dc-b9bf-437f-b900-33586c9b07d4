package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.FreightPriceDetailInfo;
import com.tyt.transport.dao.FreightPriceDetailInfoDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;

/**
 * User: tianjw
 * Date: 17-05-23
 */
@Repository("freightPriceDetailInfoDao")
public class FreightPriceDetailInfoDaoImpl extends BaseDaoImpl<FreightPriceDetailInfo, Long> implements FreightPriceDetailInfoDao {
   public FreightPriceDetailInfoDaoImpl() {
       this.setEntityClass(FreightPriceDetailInfo.class);
   }
   protected Log logger = LogFactory.getLog(this.getClass());
       

}

	
