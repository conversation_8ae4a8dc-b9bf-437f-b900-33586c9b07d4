package com.tyt.transport.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.EcaContract;
import com.tyt.transport.dao.EcaContractDao;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository("ecaContractDao")
public class EcaContractDaoImpl extends BaseDaoImpl<EcaContract, Long> implements EcaContractDao {

    public EcaContractDaoImpl() {
        this.setEntityClass(EcaContract.class);
    }

}
