package com.tyt.transport.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.TransportNullifyBean;

import java.util.List;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午3:26
 */
public interface TransportNullifyDao extends BaseDao<TransportNullifyBean,Long> {

    /**
     *  更新无效信息状态为有效
     * @param idList
     * @throws Exception
     */
    public List<String> verifyNullifyKeywordByTaskcontent(List<String> keywords) ;
    /**
     * 获取所用的无效信息key
     * @return
     */
    public List<String> getAllNullifyKeyword();

}
