package com.tyt.transport.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.TytFreightDetailInfo;
import com.tyt.transport.querybean.ProvincesCountBean;

import java.util.List;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午3:26
 */
public interface FreightDetailDao extends BaseDao<TytFreightDetailInfo,Long> {

    public List<ProvincesCountBean> getProvincesCountList(String startProvinc,String startCity,String destProvinc, String destCity);
}
