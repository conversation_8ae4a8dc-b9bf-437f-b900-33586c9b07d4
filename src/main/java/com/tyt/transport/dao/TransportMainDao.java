package com.tyt.transport.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.TransportMain;

import java.util.Date;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午3:26
 */
public interface TransportMainDao extends BaseDao<TransportMain,Long>{

    /**
     * 字表货物内容插入主表
     * @param id
     * @throws Exception
     */
    public void addTransportMain(Long id)throws Exception;

    void deleteTransportMainById(Long id);

    /**
     * 根据主键Id获取货源信息
     * @param goodsId
     * @return TransportMain
     */
    TransportMain getTransportMainById(Long goodsId);

}
