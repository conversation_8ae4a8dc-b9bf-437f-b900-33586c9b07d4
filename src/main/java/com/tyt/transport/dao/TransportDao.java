package com.tyt.transport.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.Transport;

import java.util.List;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午3:26
 */
public interface TransportDao extends BaseDao<Transport,Long>{

    public List<String> getTelList(String sql);

    public List<Long> getQqList(String sql);

    /**
     * tyt_plat_transport_optimize20171123 货源信息优化
     * 删除代码： public void clearTransport(int days);
     */

    public List<Long> getInfoIdList(String clause);
    /**
     * tyt_plat_transport_optimize20171123 货源信息优化
     * 删除代码：
     * public void updateStatus(Integer status,Long id)throws Exception;
     */
    void deleteTransportBySrcMsgId(Long srcMsgId);
}
