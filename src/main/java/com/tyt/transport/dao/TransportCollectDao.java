package com.tyt.transport.dao;

import java.util.List;
import com.tyt.base.dao.BaseDao;
import com.tyt.model.TransportCollect;

public interface TransportCollectDao extends BaseDao<TransportCollect,Long>{

	/**
	 * 用户收藏的所有infoId的集合
	 * @param cellPhone
	 * @return
	 */
	public List<Long> getInfoIdList(String cellPhone);
	
	/**
	 * 取消收藏
	 * @param cellPhone
	 * @param infoId
	 */
	public void delCollect(String cellPhone,Long infoId);
	/**
	 * 解决重发造成的id
	 * @param oldId
	 * @param newId
	 */
	public void updateId(Long oldId,Long newId);
	
	/**
	 * 根据用户id删除用户信息
	 * @param userId
	 * @param infoId
	 * @throws Exception
	 */
	public void delCollect(Long userId, Long infoId)throws Exception;
	
	/**
	 * 批量改变收藏状态
	 * @param infoId
	 * @param status
	 */
	public void updateStatus(Long infoId,Integer status);
}
