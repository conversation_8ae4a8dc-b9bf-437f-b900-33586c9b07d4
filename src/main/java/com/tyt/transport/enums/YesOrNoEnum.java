package com.tyt.transport.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/09 15:36
 */
public enum YesOrNoEnum {

    Y(1, "是"),
    N(0, "否"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String eName;

    YesOrNoEnum(Integer code, String eName) {
        this.code = code;
        this.eName = eName;
    }

    /**
     * 判断是否相等
     *
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {
        if (reqCode == null) {
            return false;
        }
        boolean result = (this.code == reqCode);
        return result;
    }
}
