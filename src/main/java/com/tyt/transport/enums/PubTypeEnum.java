package com.tyt.transport.enums;

import lombok.Getter;

/**
 * 发布方式枚举
 * 发布方式：1-确认发布，2-直接发布，3-加价，4-转一口价，5-填价
 * <AUTHOR>
 * @since 2025-02-18 10:27
 */
@Getter
public enum PubTypeEnum {
    CONFIRM_PUBLISH(1, "确认发布"),
    DIRECT_PUBLISH(2, "直接发布"),
    ADD_PRICE(3, "加价"),
    TRANSFER_FIX_PRICE(4, "转一口价"),
    FILL_PRICE(5, "填价")
    ;
    private Integer code;
    private String name;
    PubTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
