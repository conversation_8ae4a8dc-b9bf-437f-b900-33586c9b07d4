package com.tyt.transport.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 用户身份枚举
 * tyt_source 表 user_goods_type%
 * <AUTHOR>
 * @since 2024-05-29 17:52
 */
public enum UserGoodsTypeEnum {

    SELF_CARGO_OWNER(1, 1, "个人一手货主"),
    SELF_CARGO_TERMINAL(1, 2, "个人货站"),
    ENTERPRISE_CARGO_OWNER(2, 1, "企业一手货主"),
    ENTERPRISE_LOGISTICS_COMPANY(2, 2, "企业物流公司"),
    ENTERPRISE_CARGO_TERMINAL(2, 3, "企业货站")
    ;

    /**
     * 根据货主身份一级标签和二级标签获取身份类型
     * @param goodsTypeFirst
     * @param goodsTypeSecond
     * @return
     */
    public static UserGoodsTypeEnum getByGoodsType(Integer goodsTypeFirst, Integer goodsTypeSecond) {
        if (Objects.nonNull(goodsTypeFirst) && Objects.nonNull(goodsTypeSecond)) {
            for (UserGoodsTypeEnum item : UserGoodsTypeEnum.values()) {
                if (item.getGoodsTypeFirst().equals(goodsTypeFirst) && item.getGoodsTypeSecond().equals(goodsTypeSecond)) {
                    return item;
                }
            }
        }
        return null;
    }

    /**
     * 货主身份一级标签
     */
    @Getter
    private Integer goodsTypeFirst;
    /**
     * 货主身份二级标签
     */
    @Getter
    private Integer goodsTypeSecond;
    @Getter
    private String name;
    UserGoodsTypeEnum(Integer goodsTypeFirst, Integer goodsTypeSecond, String name) {
        this.goodsTypeFirst = goodsTypeFirst;
        this.goodsTypeSecond = goodsTypeSecond;
        this.name = name;
    }

}
