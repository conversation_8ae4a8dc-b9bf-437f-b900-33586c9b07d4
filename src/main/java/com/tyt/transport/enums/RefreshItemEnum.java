package com.tyt.transport.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 手动刷新的枚举类型
 *
 * <AUTHOR>
 * @since 2024/08/30 15:53
 */
@Getter
@AllArgsConstructor
public enum RefreshItemEnum {

    FILL_PRICE("补充了运费", 4, 0B1),
    TO_FIXED_PRICE("转了一口价", 2, 0B10),
    FILL_DEST_DETAIL("完善了目的地详细地址", 2, 0B100),
    ;
    private final String desc;
    private final int refreshTimes; // 刷新次数
    private final int binaryValue; // 二进制值，0B100=4

    /**
     * 判断是否某项已经刷新过
     */
    public static boolean check(int value, RefreshItemEnum itemEnum) {
        return (value & itemEnum.getBinaryValue()) != 0;
    }

    /**
     * 返回描述
     */
    public static List<RefreshItemEnum> getList(int value) {
        return Arrays.stream(values())
                .filter(itemEnum -> check(value, itemEnum))
                .collect(Collectors.toList());
    }
}
