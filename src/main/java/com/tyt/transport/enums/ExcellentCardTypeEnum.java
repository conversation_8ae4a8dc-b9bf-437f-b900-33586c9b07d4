package com.tyt.transport.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 是否优车枚举
 * @date 2023/10/09 15:36
 */
public enum ExcellentCardTypeEnum {

    unclaimed(1, "待领取"),
    not_used(2, "待使用"),
    used(3, "已使用"),
    ;

    @Getter
    private Integer code;

    @Getter
    private String name;

    ExcellentCardTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
