package com.tyt.transport.enums;

import lombok.Getter;

/**
 * 勾选明细状态
 *
 * @author: helian
 * @since: 2024/01/12 20:14
 */
public enum GoodsCheckItemEnum {
    /**
     * 发货勾选配置
     */
    DEFAULT_NO_CHECK(2),
    /**
     * 引导开关配置
     */
    NO_DEFAULT_CHECK(2),
    /**
     * 引导开关配置
     */
    USER_DEFAULT_CHECK(3);

    @Getter
    private final Integer configItem;

    GoodsCheckItemEnum(Integer configItem) {
        this.configItem = configItem;
    }
}
