package com.tyt.transport.enums;

import lombok.Getter;

/**
 * 捂货配置
 *
 * @author: helian
 * @since: 2024/01/12 20:14
 */
public enum CoverGoodsEnum {
    /**
     * 发货勾选配置
     */
    GOODS_CHECK(1),
    /**
     * 引导开关配置
     */
    SWITCH_GUIDE(2),

    /**
     * 优车货源默认为优推好车主
     */
    EXCELLENT_COVER(3);

    @Getter
    private final Integer configCode;

    CoverGoodsEnum(Integer configCode) {
        this.configCode = configCode;
    }
}
