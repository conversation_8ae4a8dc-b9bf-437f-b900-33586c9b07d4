package com.tyt.transport.enums;

import lombok.Getter;

/**
 * @author: helian
 * @since: 2023/11/03 11:43
 */
public enum MachineTypeEnum {
    ignore(0, "忽略"),
    audit(1, "待审核"),
    conformance(2, "合规"),
    illegal(3, "非法");

    @Getter
    public int code;
    @Getter
    public String value;

    MachineTypeEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static MachineTypeEnum getEnum(Integer code) {
        for (MachineTypeEnum typeEnum : MachineTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum;
            }
        }
        return null;
    }

}
