package com.tyt.transport.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * bi身份信息枚举
 * <AUTHOR>
 * @since 2024-05-31 09:13
 */
public enum NewIdentityEnum {
    // 1:物流公司 2:货站 3:企业货主 4:个人货主
    LOGISTICS_COMPANY(1, "物流公司"),
    CARGO_TERMINAL(2, "货站"),
    ENTERPRISE_CARGO_OWNER(3, "企业货主"),
    SELF_CARGO_OWNER(4, "个人货主")
    ;

    public static NewIdentityEnum getByCode(Integer code) {
        if (Objects.nonNull(code)) {
            for (NewIdentityEnum item : NewIdentityEnum.values()) {
                if (item.getCode().equals(code)) {
                    return item;
                }
            }
        }
        return null;
    }

    @Getter
    private Integer code;
    @Getter
    private String name;
    NewIdentityEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
