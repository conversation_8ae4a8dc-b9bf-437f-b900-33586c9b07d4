package com.tyt.transport.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-06-03 20:08
 */
@Getter
public enum CommissionSourceEnum {
    ORDINARY_EXCELLENT(1, "普货 + 优车"),
    EXCELLENT_PRICE(3, "优车定价货源"),
    YMM(4, "运满满货源"),
    SPECIAL(5, "专车"),
    ;
    private Integer code;
    private String name;
    
    CommissionSourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
