package com.tyt.transport.enums;

public enum OrderStatusEnum {

    WAIT_RECEIVE_ORDERS(5,"待接单"),
    WAIT_SING(10,"待签署"),
    WAIT_LOADED(15,"待装货"),
    WAIT_UNLOADED_OR_RECEIVED(20,"待卸货/待收货"),
    WAIT_COLLECT_OR_PAY_FREIGHT(25,"待付运费/待收运费"),
    FINISH(30,"已完成"),
    CANCEL(35,"已取消");

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    private int code;
    private final String desc;

    OrderStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
