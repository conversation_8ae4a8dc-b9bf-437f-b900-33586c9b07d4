package com.tyt.transport.enums;

import lombok.Getter;

/**
 * 发货类型：0:普货 1：优车 2：专车
 * <AUTHOR>
 * @since 2024-06-04 11:09
 */
@Getter
public enum ExcellentGoodsEnums {
    NORMAL(0, "普货"),
    EXCELLENT(1, "优车"),
    SPECIAL(2, "专车")
    ;
    private final Integer code;
    private final String name;
    ExcellentGoodsEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    public static boolean isNormal(Integer code) {
        return NORMAL.getCode().equals(code);
    }

    public static boolean isExcellent(Integer code) {
        return EXCELLENT.getCode().equals(code);
    }

    public static boolean isSpecial(Integer code) {
        return SPECIAL.getCode().equals(code);
    }

    public static ExcellentGoodsEnums getByCode(Integer code) {
        for (ExcellentGoodsEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
