package com.tyt.transport.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-06-03 20:08
 */
@Getter
public enum CommissionResultEnum {
    COMMISSION_N(0, "不满足抽佣条件"), COMMISSION_Y(1, "满足抽佣条件"), COMMISSION_FIRST_N(2, "首单免佣"),
    ;
    private Integer code;
    private String name;

    CommissionResultEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
