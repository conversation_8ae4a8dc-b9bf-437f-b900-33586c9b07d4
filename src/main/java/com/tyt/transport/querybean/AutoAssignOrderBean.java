package com.tyt.transport.querybean;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 自动派单实体
 * <AUTHOR>
 * @since 2024-06-27 19:57
 */
@Data
public class AutoAssignOrderBean {

    /**
     * 货源ID
     */
    @NotNull(message = "货源ID不能为空")
    private Long tsId;

    /**
     * 出发地
     */
    @NotBlank(message = "出发地不能为空")
    private String startCity;
    /**
     * 目的地
     */
    @NotBlank(message = "目的地不能为空")
    private String destCity;
    /**
     * 司机驾驶此类货物
     */
    private Integer driverDriving;
    /**
     * 货物类名
     */
    private String goodsTypeName;
    /**
     * 签约合作商ID
     */
    private Long cargoOwnerId;

    /**
     * 非专车自动派单司机接单距离限制
     */
    private Integer distanceLimit;

    /**
     * 发货用户ID
     */
    @NotNull(message = "发货用户ID不能为空")
    private Long userId;
}
