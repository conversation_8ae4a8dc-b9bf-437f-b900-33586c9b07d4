package com.tyt.transport.querybean;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 封装用户拨打电话信息的实体
 * 
 * <AUTHOR>
 * @date 2016-8-16上午9:33:38
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CallLogBeanWithRights {
	/*
	 * 货物出发地
	 */
	private String startPosition;
	/*
	 * 货物目的地
	 */
	private String destPosition;
	/*
	 * 联系人1
	 */
	private String tel;
	/*
	 * 联系人2
	 */
	private String tel3;
	/*
	 * 联系人3
	 */
	private String tel4;
	/*
	 * 货物id
	 */
	private String goodId;
	private Long srcMsgId;// 原货物唯一标识
	/*
	 * 通话时间
	 */
	private String callTime;
	/*
	 * 货物描述
	 */
	private String remark;
	/*
	 * 拨打电话结果码，1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易 6：已经拉走
	 */
	private String callResultCode;
	/*
	 * 被拨打电话的货物发布的时间，此字段在APP从列表进入标注页面的时候需要用到
	 */
	private String pubDate;
	/*
	 * 当前用户是否对该货物拨打过电话标示 返回结构"是否打过电话状态码_通话标注状态码"，具体参看接口文档
	 */
	private String callStatus;
	private String isInfoFee; // 是否收信息费货源 0是不需要1是需要
	private String hasMakeOrder;// 是否下过单:0否、1是
	private String tsOrderNo;// 运单号
	/*
	 * 是否可以打电话，0可以，1不可以
	 */
	private Integer isCanCall = 0;
	/*
	 * 超过电话拨打限制提示文字，不同的限制类型文本不同，该字段只有在超过拨打电话限制时才有值
	 */
	private String promptContent;
	/*
	 * 拨打电话限制类型，1：未进行车辆认证 2：未进行身份认证 3：试用期用户 4: 缴费到期
	 * 5：超过所有限制，即车辆认证，身份认证，缴费的拨打电话次数都已用完
	 */
	private Integer promptType;
	/*
	 * 是否需要对电话加密 1 需要 2 不需要
	 */
	private Integer isNeedDecrypt;

	/**
	 * 发货人昵称，2019-01-30
	 */
	private String nickName;
	
	// 1：过期 2：撤销 3：成交
	private Integer goodStatus;
	private Integer updateCache;

	public Integer getUpdateCache() {
		return updateCache;
	}

	public void setUpdateCache(Integer updateCache) {
		this.updateCache = updateCache;
	}

	public Integer getGoodStatus() {
		return goodStatus;
	}

	public void setGoodStatus(Integer goodStatus) {
		this.goodStatus = goodStatus;
	}

	public Integer getIsNeedDecrypt() {
		return isNeedDecrypt;
	}

	public void setIsNeedDecrypt(Integer isNeedDecrypt) {
		this.isNeedDecrypt = isNeedDecrypt;
	}

	public Integer getIsCanCall() {
		return isCanCall;
	}

	public void setIsCanCall(Integer isCanCall) {
		this.isCanCall = isCanCall;
	}

	public String getPromptContent() {
		return promptContent;
	}

	public void setPromptContent(String promptContent) {
		this.promptContent = promptContent;
	}

	public Integer getPromptType() {
		return promptType;
	}

	public void setPromptType(Integer promptType) {
		this.promptType = promptType;
	}

	public String getCallStatus() {
		return callStatus;
	}

	public void setCallStatus(String callStatus) {
		this.callStatus = callStatus;
	}

	public String getPubDate() {
		return pubDate;
	}

	public void setPubDate(String pubDate) {
		this.pubDate = pubDate;
	}

	public String getCallResultCode() {
		return callResultCode;
	}

	public void setCallResultCode(String callResultCode) {
		this.callResultCode = callResultCode;
	}

	public String getStartPosition() {
		return startPosition;
	}

	public void setStartPosition(String startPosition) {
		this.startPosition = startPosition;
	}

	public String getDestPosition() {
		return destPosition;
	}

	public void setDestPosition(String destPosition) {
		this.destPosition = destPosition;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getTel3() {
		return tel3;
	}

	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}

	public String getTel4() {
		return tel4;
	}

	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}

	public String getGoodId() {
		return goodId;
	}

	public void setGoodId(String goodId) {
		this.goodId = goodId;
	}

	public String getCallTime() {
		return callTime;
	}

	public void setCallTime(String callTime) {
		this.callTime = callTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getSrcMsgId() {
		return srcMsgId;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}

	public String getIsInfoFee() {
		return isInfoFee;
	}

	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}

	public String getHasMakeOrder() {
		return hasMakeOrder;
	}

	public void setHasMakeOrder(String hasMakeOrder) {
		this.hasMakeOrder = hasMakeOrder;
	}

	public String getTsOrderNo() {
		return tsOrderNo;
	}

	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

	public void clearTel() {
		this.setTel(null);
		this.setTel3(null);
		this.setTel4(null);
	}
}
