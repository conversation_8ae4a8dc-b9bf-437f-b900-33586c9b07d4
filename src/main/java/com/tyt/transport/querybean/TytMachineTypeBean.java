package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;

public class TytMachineTypeBean implements java.io.Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 5133463789450889823L;
	private Long id;
	private String brandType;
	private String machineType;
	private String brand;
	private String type;
	private String weight;
	private String length;
	private String width;
	private String height;
	private String generalMatchesItem;
	/*
	 * 是否显示长宽高，0 显示 1 不显示
	 */
	private Integer lengthWidthHeightDisplay;
	/*
	 * 是否显示重量，0 显示 1 不显示
	 */
	private Integer weightDisplay;

	public Long getId() {
		return id;
	}

	public String getBrandType() {
		return brandType;
	}

	public String getMachineType() {
		return machineType;
	}

	public String getBrand() {
		return brand;
	}

	public String getType() {
		return type;
	}

	public String getWeight() {
		return weight;
	}

	public String getLength() {
		return length;
	}

	public String getWidth() {
		return width;
	}

	public String getHeight() {
		return height;
	}

	public String getGeneralMatchesItem() {
		return generalMatchesItem;
	}

	public Integer getLengthWidthHeightDisplay() {
		return lengthWidthHeightDisplay;
	}

	public Integer getWeightDisplay() {
		return weightDisplay;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public void setBrandType(String brandType) {
		this.brandType = brandType;
	}

	public void setMachineType(String machineType) {
		this.machineType = machineType;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public void setType(String type) {
		this.type = type;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public void setLength(String length) {
		this.length = length;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public void setGeneralMatchesItem(String generalMatchesItem) {
		this.generalMatchesItem = generalMatchesItem;
	}

	public void setLengthWidthHeightDisplay(Integer lengthWidthHeightDisplay) {
		this.lengthWidthHeightDisplay = lengthWidthHeightDisplay;
	}

	public void setWeightDisplay(Integer weightDisplay) {
		this.weightDisplay = weightDisplay;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
