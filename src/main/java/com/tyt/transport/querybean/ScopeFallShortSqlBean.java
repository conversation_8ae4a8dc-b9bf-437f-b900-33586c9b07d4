package com.tyt.transport.querybean;

public class ScopeFallShortSqlBean {
    private String startProvinc;
    private String startCity;
    private String startArea;
    private long startCoordX;
    private long startCoordY;
    private long startRange;
    private int queryType;
    private long tsId;
    private int pageSize;

    public String getStartProvinc() {
        return startProvinc;
    }

    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }

    public String getStartCity() {
        return startCity;
    }

    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }

    public String getStartArea() {
        return startArea;
    }

    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }

    public long getStartCoordX() {
        return startCoordX;
    }

    public void setStartCoordX(long startCoordX) {
        this.startCoordX = startCoordX;
    }

    public long getStartCoordY() {
        return startCoordY;
    }

    public void setStartCoordY(long startCoordY) {
        this.startCoordY = startCoordY;
    }

    public long getStartRange() {
        return startRange;
    }

    public void setStartRange(long startRange) {
        this.startRange = startRange;
    }

    public int getQueryType() {
        return queryType;
    }

    public void setQueryType(int queryType) {
        this.queryType = queryType;
    }

    public long getTsId() {
        return tsId;
    }

    public void setTsId(long tsId) {
        this.tsId = tsId;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
