package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName CarriageFeedbackBean
 * @Description 运费参考价反馈
 * <AUTHOR> Lion
 * @Date 2022/9/15 13:25
 * @Verdion 1.0
 **/
@Data
public class CarriageFeedbackBean extends BaseParameter implements Serializable {


    /**
     * 重发后原信息ID
     */
    private Long srcMsgId;
    /**
     * 手机号
     */
    private String cellPhone;
    /**
     * 请求数据组接口类型: 1.支付信息费，2.发货参考价，3.发货校验
     */
    private Integer source;
    /**
     * 反馈标签: 1.价格太低，2.价格太高
     */
    private Integer feedbackType;
    /**
     * 反馈价格
     */
    private BigDecimal price;
    /**
     * 反馈内容
     */
    private String remark;

    //出发地省
    private String startProvince;
    //出发地市
    private String startCity;
    //出发地区县
    private String startArea;
    //目的地省
    private String destProvince;
    //目的地市
    private String destCity;
    //目的地区县
    private String destArea;
    //货物描述
    private String goodsName;
    //重量
    private BigDecimal goodsWeight;
    //长
    private BigDecimal goodsLength;
    //宽
    private BigDecimal goodsWide;
    //高
    private BigDecimal goodsHigh;

}
