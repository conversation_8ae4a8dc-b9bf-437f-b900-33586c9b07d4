package com.tyt.transport.querybean;

import java.io.Serializable;
import java.util.List;

/**
 * sql语句组织及参数集合bean
 * <AUTHOR>
 * @version 5920
 * @date 2019/5/18
 */


public class DoSqlParamBean  implements Serializable {
     private String sql;
     private List<Object> pram;


    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public List<Object>  getPram() {
        return pram;
    }

    public void setPram(List<Object> pram) {
        this.pram = pram;
    }

    @Override
    public String toString() {
        return "DoSqlDataBean{" +
                "sql='" + sql + '\'' +
                ", pram=" + pram +
                '}';
    }
}
