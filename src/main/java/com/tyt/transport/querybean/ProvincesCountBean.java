package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;
import java.util.Date;

public class ProvincesCountBean implements Serializable{

	private static final long serialVersionUID = 218271222603398301L;

	private Long id;
	private Integer type;
	private Date ctime;
	private String province;
	private String city;
	private Integer totalCount;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
