package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class TransportSearchBean extends BaseParameter{
    private static final long serialVersionUID = 6298046523840230233L;
    private String startCoord;
    private String destCoord;
    private Integer queryType;
    private Long querySign;
    private String startDistance;
    private String destDistance;
    private Integer sortType;
    private Long carId;
    private String headCity;
    private String headNo;
    private String numberType;
    private Integer userType;
    private String carLength;
    private String carType;
    private String specialRequired;

    //2019-12-04 xyy 增加重量查询条件
    private Integer startWeight;
    private Integer endWeight;

    //2020-07-17 新增 v6000版本筛选字段
    /**
     * 货物名称，以英文逗号隔开
     */
    private List<String> goodsName;
    /**
     * 货物尺寸长度最小值
     */
    private BigDecimal minLength;
    /**
     * 货物尺寸长度最大值
     */
    private BigDecimal maxLength;
    /**
     * 货物尺寸宽度最大值
     */
    private BigDecimal minWidth;
    /**
     * 货物尺寸宽度最大值
     */
    private BigDecimal maxWidth;
    /**
     * 货物尺寸高度最大值
     */
    private BigDecimal minHeight;
    /**
     * 货物尺寸高度最大值
     */
    private BigDecimal maxHeight;
    /**
     * 开始装车时间
     */
    private Date startLoadingTime;
    /**
     * 截止装车时间
     */
    private Date endLoadingTime;
    /**
     * 最小运价
     */
    private Integer minPrice;
    /**
     * 最大运价
     */
    private Integer maxPrice;

    public String getStartCoord() {
        return startCoord;
    }

    public void setStartCoord(String startCoord) {
        this.startCoord = startCoord;
    }

    public String getDestCoord() {
        return destCoord;
    }

    public void setDestCoord(String destCoord) {
        this.destCoord = destCoord;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public Long getQuerySign() {
        return querySign;
    }

    public void setQuerySign(Long querySign) {
        this.querySign = querySign;
    }

    public String getStartDistance() {
        return startDistance;
    }

    public void setStartDistance(String startDistance) {
        this.startDistance = startDistance;
    }

    public String getDestDistance() {
        return destDistance;
    }

    public void setDestDistance(String destDistance) {
        this.destDistance = destDistance;
    }

    public Integer getSortType() {
        return sortType;
    }

    public void setSortType(Integer sortType) {
        this.sortType = sortType;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public String getHeadCity() {
        return headCity;
    }

    public void setHeadCity(String headCity) {
        this.headCity = headCity;
    }

    public String getHeadNo() {
        return headNo;
    }

    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }

    public String getNumberType() {
        return numberType;
    }

    public void setNumberType(String numberType) {
        this.numberType = numberType;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getCarLength() {
        return carLength;
    }

    public void setCarLength(String carLength) {
        this.carLength = carLength;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getSpecialRequired() {
        return specialRequired;
    }

    public void setSpecialRequired(String specialRequired) {
        this.specialRequired = specialRequired;
    }

    public Integer getStartWeight() {
        return startWeight;
    }

    public void setStartWeight(Integer startWeight) {
        this.startWeight = startWeight;
    }

    public Integer getEndWeight() {
        return endWeight;
    }

    public void setEndWeight(Integer endWeight) {
        this.endWeight = endWeight;
    }

    public List<String> getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(List<String> goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getMinLength() {
        return minLength;
    }

    public void setMinLength(BigDecimal minLength) {
        this.minLength = minLength;
    }

    public BigDecimal getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(BigDecimal maxLength) {
        this.maxLength = maxLength;
    }

    public BigDecimal getMinWidth() {
        return minWidth;
    }

    public void setMinWidth(BigDecimal minWidth) {
        this.minWidth = minWidth;
    }

    public BigDecimal getMaxWidth() {
        return maxWidth;
    }

    public void setMaxWidth(BigDecimal maxWidth) {
        this.maxWidth = maxWidth;
    }

    public BigDecimal getMinHeight() {
        return minHeight;
    }

    public void setMinHeight(BigDecimal minHeight) {
        this.minHeight = minHeight;
    }

    public BigDecimal getMaxHeight() {
        return maxHeight;
    }

    public void setMaxHeight(BigDecimal maxHeight) {
        this.maxHeight = maxHeight;
    }

    public Date getStartLoadingTime() {
        return startLoadingTime;
    }

    public void setStartLoadingTime(Date startLoadingTime) {
        this.startLoadingTime = startLoadingTime;
    }

    public Date getEndLoadingTime() {
        return endLoadingTime;
    }

    public void setEndLoadingTime(Date endLoadingTime) {
        this.endLoadingTime = endLoadingTime;
    }

    public Integer getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(Integer minPrice) {
        this.minPrice = minPrice;
    }

    public Integer getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(Integer maxPrice) {
        this.maxPrice = maxPrice;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
