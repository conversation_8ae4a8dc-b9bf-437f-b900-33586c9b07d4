package com.tyt.transport.querybean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.orderlimit.bean.AcceptOrderLimitInfo;
import com.tyt.transport.bean.PrivacyPhoneTabInfo;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CellPhoneListBean {
    private String tel;  //联系人1
    private String tel3; //联系人2
    private String tel4;  //联系人3
    private String uploadCellPhone;//发货人账号

    /**
     * 装货联系电话
     */
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    private String unloadCellPhone;
    private Long srcMsgId;// 原货物唯一标识
    private String tsOrderNo;
    private String isInfoFee;
    private String callStatus;
    private Integer callStatusCode = 0;
    private Integer isCanCall = 0;  //是否可以打电话，0可以，1不可以
    private String hasMakeOrder;// 是否下过单:0否、1是
    private Long userId;
    private Integer goodStatus;
    /**
     * 拨打电话时提示框提示信息
     */
    private String noGoodsMemberText;

    /**
     * 接单限制信息
     */
    private AcceptOrderLimitInfo acceptOrderLimitInfo;

    private PrivacyPhoneTabInfo privacyPhoneTabInfo;

    /**
     * 是否跳过权益校验：1是，0否（默认）（比如：专车货源没有拨打权益的时候也能拨打和支付，普通货源指派专车时也能拨打和支付）
     */
    @Getter
    @Setter
    private Integer skipPermission = 0;


    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getTel3() {
        return tel3;
    }

    public void setTel3(String tel3) {
        this.tel3 = tel3;
    }

    public String getTel4() {
        return tel4;
    }

    public void setTel4(String tel4) {
        this.tel4 = tel4;
    }

    public String getUploadCellPhone() {
        return uploadCellPhone;
    }

    public void setUploadCellPhone(String uploadCellPhone) {
        this.uploadCellPhone = uploadCellPhone;
    }

    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    public String getTsOrderNo() {
        return tsOrderNo;
    }

    public void setTsOrderNo(String tsOrderNo) {
        this.tsOrderNo = tsOrderNo;
    }

    public String getIsInfoFee() {
        return isInfoFee;
    }

    public void setIsInfoFee(String isInfoFee) {
        this.isInfoFee = isInfoFee;
    }

    public String getCallStatus() {
        return callStatus;
    }

    public void setCallStatus(String callStatus) {
        this.callStatus = callStatus;
    }

    public Integer getCallStatusCode() {
        return callStatusCode;
    }

    public void setCallStatusCode(Integer callStatusCode) {
        this.callStatusCode = callStatusCode;
    }

    public Integer getIsCanCall() {
        return isCanCall;
    }

    public void setIsCanCall(Integer isCanCall) {
        this.isCanCall = isCanCall;
    }

    public String getHasMakeOrder() {
        return hasMakeOrder;
    }

    public void setHasMakeOrder(String hasMakeOrder) {
        this.hasMakeOrder = hasMakeOrder;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getGoodStatus() {
        return goodStatus;
    }

    public void setGoodStatus(Integer goodStatus) {
        this.goodStatus = goodStatus;
    }

    public String getNoGoodsMemberText() {
        return noGoodsMemberText;
    }

    public void setNoGoodsMemberText(String noGoodsMemberText) {
        this.noGoodsMemberText = noGoodsMemberText;
    }

    public AcceptOrderLimitInfo getAcceptOrderLimitInfo() {
        return acceptOrderLimitInfo;
    }

    public void setAcceptOrderLimitInfo(AcceptOrderLimitInfo acceptOrderLimitInfo) {
        this.acceptOrderLimitInfo = acceptOrderLimitInfo;
    }

    public PrivacyPhoneTabInfo getPrivacyPhoneTabInfo() {
        return privacyPhoneTabInfo;
    }

    public void setPrivacyPhoneTabInfo(PrivacyPhoneTabInfo privacyPhoneTabInfo) {
        this.privacyPhoneTabInfo = privacyPhoneTabInfo;
    }

    public String getLoadCellPhone() {
        return loadCellPhone;
    }

    public void setLoadCellPhone(String loadCellPhone) {
        this.loadCellPhone = loadCellPhone;
    }

    public String getUnloadCellPhone() {
        return unloadCellPhone;
    }

    public void setUnloadCellPhone(String unloadCellPhone) {
        this.unloadCellPhone = unloadCellPhone;
    }
}
