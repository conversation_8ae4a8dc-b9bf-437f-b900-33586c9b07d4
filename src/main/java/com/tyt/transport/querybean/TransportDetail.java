package com.tyt.transport.querybean;



import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.infofee.bean.TransportUserBean;
import com.tyt.model.TransportCollect;
/**
 * 货物详情实体
 * <AUTHOR>
 *
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportDetail {
	TransportDetailBean detailBean;//货物内容
	TransportUserBean transportUserBean;//货主信息
	Integer isCollect=0;
	Long collectId;
    
	public TransportDetail() {
		super();
		// TODO Auto-generated constructor stub
	}

	public TransportDetail(TransportDetailBean detailBean, Integer isCollect,Long collectId) {
		super();
		this.detailBean = detailBean;
		this.isCollect = isCollect;
		this.collectId=collectId;
	}

	public TransportDetail(TransportDetailBean bean, TransportCollect collect) {
		super();
		this.detailBean = bean;
		if(collect!=null){
			this.isCollect = collect.getStatus();
			this.collectId=collect.getId();
		}
	}

	public TransportDetailBean getDetailBean() {
		return detailBean;
	}

	public void setDetailBean(TransportDetailBean detailBean) {
		this.detailBean = detailBean;
	}

	public Integer getIsCollect() {
		return isCollect;
	}

	public void setIsCollect(Integer isCollect) {
		this.isCollect = isCollect;
	}

	public Long getCollectId() {
		return collectId;
	}

	public void setCollectId(Long collectId) {
		this.collectId = collectId;
	}

	public TransportUserBean getTransportUserBean() {
		return transportUserBean;
	}

	public void setTransportUserBean(TransportUserBean transportUserBean) {
		this.transportUserBean = transportUserBean;
	}

}
