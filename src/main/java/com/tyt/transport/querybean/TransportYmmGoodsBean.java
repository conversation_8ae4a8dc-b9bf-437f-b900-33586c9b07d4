package com.tyt.transport.querybean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 运满满货源转义后对象
 * @date 2023-10-09 17-09-59
 */
@Data
public class TransportYmmGoodsBean implements Serializable {

    private Long queryId;
    private Long cargoId;
    private String taskContent;
    /**
     * 货主电话
     */
    private String giveGoodsPhone;
    /**
     * 装货地址
     */
    private String startPoint;
    /**
     * //卸货地址
     */
    private String destPoint;
    /**
     * 用车数量
     */
    private Integer shuntingQuantity;
    /**
     * 重量
     */
    private double weight;


    /**
     * 装货开始时间
     */
    private Date beginLoadingTime;
    /**
     * 装货时间
     */
    private Date loadingTime;

    /**
     * 卸货开始时间
     */

    private Date beginUnloadTime;
    /**
     * 卸货时间
     */
    private Date unloadTime;
    /**
     *最小长度
     */
    private double length;
    /**
     * 最小宽度
     */
    private double wide;
    /**
     * 最小高度
     */
    private double high;
    /**
     * 货主姓名
     */
    private String contactName;
    /**
     *定金是否退还 0；不退 1：退还
     */
    private int refundFlag;
    /**
     *运费单位：分
     */
    private Long price;
    /**
     * 定金：分
     */
    private Long infoFee;
    /**
     * 货成交模式  1：一口价 2.电议 3.委托 4.OPERATOR_TRUCK_BOOK
     */
    private int publishType;
    /**
     * 装货维度
     */
    private double startLatitude;
    /**
     * 装货经度
     */
    private double startLongitude;

    private double destLatitude;

    private double destLongitude;
    /**
     *   卸货地址省、市、区
     */
    private String destProvinc;
    private String destCity;
    private String destArea;
    private String destDetailAdd;
    /**
     * YMM用户唯一标识
     */
    private String shipperId;
    /**
     *  装货地址省、市、县
     */
    private String startProvinc;
    private String startCity;
    private String startArea;
    private String startDetailAdd;
    /**
     *版本
     */
    private Integer cargoVersion;
    /**
     *   0 未删除 1已删除(下架)
     */
    private int delFlag;
    private String delReason;
    private Date createTime;
    /**
     * 车长
     */
    private String truckLengths;
    /**
     * 车型
     */
    private String truckTypes;
    private String infoJson;

    /**
     * 是否是优货货源（0:否 1：是）
     */
    private Integer excellentGoods;

    /**
     * 是否需要带爬梯 0 是 1 需要 2不需要
     */
    private String climb;

    /**
     * 挂车样式
     */
    private String carStyle;

    /**
     * 所需车辆 长度标签
     */
    private String carLengthLabels;

    /**
     * 货源来源（1货主；2调度客服；3个人货主;4:运满满货源）
     */
    private Integer sourceType;

}
