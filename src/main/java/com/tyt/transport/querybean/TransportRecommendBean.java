package com.tyt.transport.querybean;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 返回简版有好货的数据bean
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportRecommendBean implements Serializable {

	private static final long serialVersionUID = -8281258206623919634L;
	private Long id;
	private Long srcMsgId;
	private String startPoint;
	private String destPoint;
	// 出发地 目的地 省市区
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private String taskContent;
	private Integer status;
	private Date pubDate;
	private String nickName;
	private String startCoord;
	private String destCoord;
	private String price;// 运费
	private Integer userType;
	// 实名认证验证信息标志0未验证1通过3认证失败
	private Integer verifyPhotoSign;
	private String isInfoFee; // 是否收信息费货源 0是不需要1是需要
	private Date regTime;// 发货人注册时间
	private Integer androidDistance;
	// 是否需要加密昵称
	private Integer isNeedDecrypt;
	//重量 单位:吨
	private String weight;

	public Integer getIsNeedDecrypt() {
		return isNeedDecrypt;
	}

	public void setIsNeedDecrypt(Integer isNeedDecrypt) {
		this.isNeedDecrypt = isNeedDecrypt;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSrcMsgId() {
		return srcMsgId;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}

	public String getStartPoint() {
		return startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	public String getDestPoint() {
		return destPoint;
	}

	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}

	public String getStartProvinc() {
		return startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	public String getStartCity() {
		return startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	public String getStartArea() {
		return startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	public String getDestProvinc() {
		return destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	public String getDestCity() {
		return destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	public String getDestArea() {
		return destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	public String getTaskContent() {
		return taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getPubDate() {
		return pubDate;
	}

	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getStartCoord() {
		return startCoord;
	}

	public void setStartCoord(String startCoord) {
		this.startCoord = startCoord;
	}

	public String getDestCoord() {
		return destCoord;
	}

	public void setDestCoord(String destCoord) {
		this.destCoord = destCoord;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Integer getVerifyPhotoSign() {
		return verifyPhotoSign;
	}

	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
		this.verifyPhotoSign = verifyPhotoSign;
	}

	public String getIsInfoFee() {
		return isInfoFee;
	}

	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}

	public Date getRegTime() {
		return regTime;
	}

	public void setRegTime(Date regTime) {
		this.regTime = regTime;
	}

	public Integer getAndroidDistance() {
//		if (androidDistance == null)
//			return null;
//		return new BigDecimal(androidDistance).movePointLeft(2).intValue();
		return androidDistance;
	}

	public void setAndroidDistance(Integer androidDistance) {
		this.androidDistance = androidDistance;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}
}
