package com.tyt.transport.querybean;

import java.io.Serializable;
import java.util.Date;



import com.fasterxml.jackson.annotation.JsonInclude;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportMtBean implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private Long id;
	private String startPoint;
	private String destPoint;
	private String taskContent;
	
	private Integer status;
	
	private Date publishTime;

	private String uploadCellPhone;


	/* 20150716新增字段 */
	private Long userId;// '发布人usreID 关联tyt_user表id',
		// 出发地 目的地 省市区
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String destProvinc;
	private String destCity;
	private String destArea;

	private String isInfoFee; // 是否收信息费货源 0是不需要1是需要
	private String tsOrderNo;
	private Date releaseTime;
	
	/**
	 * 成交时间
	 */
	private Date loadTime;
	/**
	 * 停止时间
	 */
	private Date stopTime;
	/**
	 * 货源ID
	 */
	private Long tsId;

	//2018-11-28 人保货运险改动
	//是否购买过保险 1是(显示“查看保单”按钮)  2否(显示“买货运险”按钮)
	private Integer isBuyInsurance;
	//最后一次购买保险的保单Id
	private Long insuranceId;
	//最后一次购买保险的保单状态 0待支付 1已生效 2已退保
	private Integer insuranceStatus;


	public Long getId() {
		return id;
	}
	public String getStartPoint() {
		return startPoint;
	}
	public String getDestPoint() {
		return destPoint;
	}
	public String getTaskContent() {
		return taskContent;
	}
	public Integer getStatus() {
		return status;
	}
	public Date getPublishTime() {
		return publishTime;
	}
	public String getUploadCellPhone() {
		return uploadCellPhone;
	}
	public Long getUserId() {
		return userId;
	}
	public String getStartProvinc() {
		return startProvinc;
	}
	public String getStartCity() {
		return startCity;
	}
	public String getStartArea() {
		return startArea;
	}
	public String getDestProvinc() {
		return destProvinc;
	}
	
	public String getDestArea() {
		return destArea;
	}
	public String getIsInfoFee() {
		return isInfoFee;
	}
	public String getTsOrderNo() {
		return tsOrderNo;
	}
	public Date getReleaseTime() {
		return releaseTime;
	}
	public Date getLoadTime() {
		return loadTime;
	}
	public Date getStopTime() {
		return stopTime;
	}
	public Long getTsId() {
		return tsId;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}
	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}
	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}
	public void setUploadCellPhone(String uploadCellPhone) {
		this.uploadCellPhone = uploadCellPhone;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}
	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}
	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}
	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}
	
	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}
	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}
	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}
	public void setReleaseTime(Date releaseTime) {
		this.releaseTime = releaseTime;
	}
	public void setLoadTime(Date loadTime) {
		this.loadTime = loadTime;
	}
	public void setStopTime(Date stopTime) {
		this.stopTime = stopTime;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	public String getDestCity() {
		return destCity;
	}
	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	public Integer getIsBuyInsurance() {
		return isBuyInsurance;
	}

	public void setIsBuyInsurance(Integer isBuyInsurance) {
		this.isBuyInsurance = isBuyInsurance;
	}

	public Long getInsuranceId() {
		return insuranceId;
	}

	public void setInsuranceId(Long insuranceId) {
		this.insuranceId = insuranceId;
	}

	public Integer getInsuranceStatus() {
		return insuranceStatus;
	}

	public void setInsuranceStatus(Integer insuranceStatus) {
		this.insuranceStatus = insuranceStatus;
	}
}
