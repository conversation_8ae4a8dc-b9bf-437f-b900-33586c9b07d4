package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;

import java.io.Serializable;

public class ScopeSearchQueryBean extends BaseParameter implements Serializable {
    private static final long serialVersionUID = 1338734026206073157L;
    private String startCoord;
    private String startDistance;
    private String startProvinc;
    private String startCity;
    private String startArea;
    private Integer queryType;
    private Long querySign;

    public String getStartCoord() {
        return startCoord;
    }

    public void setStartCoord(String startCoord) {
        this.startCoord = startCoord;
    }

    public String getStartDistance() {
        return startDistance;
    }

    public void setStartDistance(String startDistance) {
        this.startDistance = startDistance;
    }

    public String getStartProvinc() {
        return startProvinc;
    }

    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }

    public String getStartCity() {
        return startCity;
    }

    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }

    public String getStartArea() {
        return startArea;
    }

    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public Long getQuerySign() {
        return querySign;
    }

    public void setQuerySign(Long querySign) {
        this.querySign = querySign;
    }
}
