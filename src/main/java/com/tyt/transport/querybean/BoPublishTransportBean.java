package com.tyt.transport.querybean;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 5920
 * @date 2019/7/4
 */
@Getter
@Setter
public class BoPublishTransportBean implements Serializable {

    private Long tsId;
    private Long srcMsgId;
    private String tsOrderNo;
    private Integer remainNumber;
    private String successMsg;
    /** V6290 新增用户等级字段 **/
    private Integer userLevel;
    // 秒抢货源 1：是
    private Integer instantGrab;
    /**
     * 是否是优车货源（0:否 1：优车 2：专车）
     */
    private Integer excellentGoods;
    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;
    /**
     * 运费
     */
    private String price;

    /**
     * 是否存在目的地详细地址（1存在，0不存在）
     */
    private Integer hasDestDetail;

    // 出发地目的地经纬度
    private String startLatitude;
    private String startLongitude;
    private String destLatitude;
    private String destLongitude;

    // 出发地完整地址/省/市/县/详细地址
    private String startPoint;
    private String startProvinc;
    private String startCity;
    private String startArea;
    private String startDetailAdd;

    // 目的地完整地址/省/市/县/详细地址
    private String destPoint;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private String destDetailAdd;

    @Override
    public String toString() {
        return "BoPublishTransportBean{" +
                "tsId=" + tsId +
                ", srcMsgId=" + srcMsgId +
                ", tsOrderNo='" + tsOrderNo + '\'' +
                ", remainNumber='" + remainNumber + '\'' +
                ", successMsg='" + successMsg + '\'' +
                ", instantGrab='" + instantGrab + '\'' +
                '}';
    }

}
