package com.tyt.transport.querybean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransPerDataBean implements Serializable {

	private static final long serialVersionUID = 3229278978624548567L;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String startCoordX;
	private String startCoordY;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private String destCoordX;
	private String destCoordY;
	private String startPointRegion;

	public String getStartProvinc() {
		return startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	public String getStartCity() {
		return startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	public String getStartArea() {
		return startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	public String getStartCoordX() {
		return startCoordX;
	}

	public void setStartCoordX(String startCoordX) {
		this.startCoordX = startCoordX;
	}

	public String getStartCoordY() {
		return startCoordY;
	}

	public void setStartCoordY(String startCoordY) {
		this.startCoordY = startCoordY;
	}

	public String getDestProvinc() {
		return destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	public String getDestCity() {
		return destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	public String getDestArea() {
		return destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	public String getDestCoordX() {
		return destCoordX;
	}

	public void setDestCoordX(String destCoordX) {
		this.destCoordX = destCoordX;
	}

	public String getDestCoordY() {
		return destCoordY;
	}

	public void setDestCoordY(String destCoordY) {
		this.destCoordY = destCoordY;
	}

	public String getStartPointRegion() {
		return startPointRegion;
	}

	public void setStartPointRegion(String startPointRegion) {
		this.startPointRegion = startPointRegion;
	}
}
