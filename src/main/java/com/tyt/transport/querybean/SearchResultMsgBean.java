package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.model.ResultMsgBean;

import java.io.Serializable;
import java.util.Date;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:57
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchResultMsgBean extends ResultMsgBean implements Serializable {

    private static final long serialVersionUID = -7618571891727534877L;

    private int isAuthUpdate;
    private Object perData;

    public int getIsAuthUpdate() {
        return isAuthUpdate;
    }

    public void setIsAuthUpdate(int isAuthUpdate) {
        this.isAuthUpdate = isAuthUpdate;
    }

    public Object getPerData() {
        return perData;
    }

    public void setPerData(Object perData) {
        this.perData = perData;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
