package com.tyt.transport.querybean;

public class CertIdentityBean {
	
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 真实姓名
	 */
	private String userName;
	/**
	 * 企业名称
	 */
	private String comName;
	/**
	 * 身份证号
	 */
	private String idCard;
	/**
	 * 注册身份   1.非企业  2.企业
	 */
	private Integer identityType;
	/**
	 * 手机
	 */
	private String cellPhone;
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getComName() {
		return comName;
	}
	public void setComName(String comName) {
		this.comName = comName;
	}
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	public Integer getIdentityType() {
		return identityType;
	}
	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Override
	public String toString() {
		return "CertIdentityBean{" +
				"userId=" + userId +
				", userName='" + userName + '\'' +
				", comName='" + comName + '\'' +
				", idCard='" + idCard + '\'' +
				", identityType=" + identityType +
				", cellPhone='" + cellPhone + '\'' +
				'}';
	}

}
