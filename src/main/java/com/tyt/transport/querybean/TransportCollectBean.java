package com.tyt.transport.querybean;

import com.tyt.infofee.bean.InfoFeeMyPublishGoodsResultBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description  货源聚合信息实体类
 * <AUTHOR>
 * @Date  2019/12/5 15:02
 * @Param
 * @return
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransportCollectBean implements Serializable {

	private static final long serialVersionUID = 2991181120953632941L;

	//货源列表信息
	private List<InfoFeeMyPublishGoodsResultBean> goodsList;

	//货源数量
	private Integer goodsCount;
}
