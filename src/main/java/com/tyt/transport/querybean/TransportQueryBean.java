package com.tyt.transport.querybean;

import java.io.Serializable;
import java.util.Date;



import com.fasterxml.jackson.annotation.JsonInclude;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportQueryBean implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 284527725456970345L;
	private Long id;
	private Long tsId;
    private String startPoint;
    private String destPoint;
    private String taskContent;
    private Integer status;
    private Date pubDate;// '发布日期',
	/**
	 * 货源类型（电议1，一口价2）
	 */
	private Integer publishType;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getTsId() {
		return tsId;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	public String getStartPoint() {
		return startPoint;
	}
	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}
	public String getDestPoint() {
		return destPoint;
	}
	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}
	public String getTaskContent() {
		return taskContent;
	}
	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getPubDate() {
		return pubDate;
	}
	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}

	public Integer getPublishType() {
		return publishType;
	}

	public void setPublishType(Integer publishType) {
		this.publishType = publishType;
	}
}
