package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * SameTransportAveragePriceData is a class that represents the average price data for the same transport.
 * It extends the BaseParameter class and implements the Serializable interface.
 *
 * Attributes:
 * - dayNum: a String representing the day number
 * - averagePrice: a BigDecimal representing the average price
 *
 * This class provides the necessary getters and setters for the attributes.
 */
@Data
public class SameTransportAveragePriceData extends BaseParameter implements Serializable {

    private String dayNum;

    private BigDecimal averagePrice;

    private Integer sameCount;
}
