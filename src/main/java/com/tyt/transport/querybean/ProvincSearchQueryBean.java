package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;

import java.io.Serializable;

public class ProvincSearchQueryBean extends BaseParameter implements Serializable {
    private static final long serialVersionUID = -2752994349071456126L;
    private String startProvinc;
    private Integer queryType;
    private Long querySign;

    public String getStartProvinc() {
        return startProvinc;
    }

    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public Long getQuerySign() {
        return querySign;
    }

    public void setQuerySign(Long querySign) {
        this.querySign = querySign;
    }
}
