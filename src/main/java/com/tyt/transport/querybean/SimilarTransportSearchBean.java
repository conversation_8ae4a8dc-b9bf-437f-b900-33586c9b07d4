package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import java.util.List;

/**
 * @ClassName SimilarTransportSearchBean
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/6/2 11:23
 * @Verdion 1.0
 **/
@Data
public class SimilarTransportSearchBean extends BaseParameter {

    /**
     * 相似编码
     */
    private String similarityCode;
    /**
     * 相似货源首发ID
     */
    private Long similarityFirstId;
    /**
     * 相似货源首发信息
     */
    private String similarityFirstInfo;
    /**
     * 货源ID
     */
    private Long goodsId;
    /**
     * 需要过滤的货主id
     */
    private List<Long> filterUserIds;
    /**
     * 需要返回的条数，之前是返回30条
     */
    private Integer pageSize = 30;

}
