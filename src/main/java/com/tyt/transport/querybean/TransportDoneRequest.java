package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportDoneRequest implements Serializable {
	private static final long serialVersionUID = 2275817212953004074L;
	/**
	 * 车辆ID
	 */
	private Long carId;
	/**
	 * 车头牌照头字母
	 */
	private String headCity;
	/**
	 * 车头牌照号码
	 */
	private String headNo;
	/**
	 * 挂车牌照头字母
	 */
	private String tailCity;
	/**
	 * 挂车牌照号码
	 */
	private String tailNo;
	/**
	 * 承运人userid
	 */
	private Long carryUserId;
	/**
	 * 承运人手机号码
	 */
	private String carryCellPhone;
	/**
	 * 承运人姓名
	 */
	private String carryName;
	/**
	 * 是否更改过所查车辆，0-未更改，1-已更改
	 */
	private Integer isChangeCar;
	/**
	 * 是否允许定位车辆，0-允许，1-拒绝
	 */
	private Integer isAllowLocation;

	private BigDecimal dealPrice;

	public Long getCarId() {
		return carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	public String getHeadCity() {
		return headCity;
	}

	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}

	public String getHeadNo() {
		return headNo;
	}

	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}

	public String getTailCity() {
		return tailCity;
	}

	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}

	public String getTailNo() {
		return tailNo;
	}

	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}

	public Long getCarryUserId() {
		return carryUserId;
	}

	public void setCarryUserId(Long carryUserId) {
		this.carryUserId = carryUserId;
	}

	public String getCarryCellPhone() {
		return carryCellPhone;
	}

	public void setCarryCellPhone(String carryCellPhone) {
		this.carryCellPhone = carryCellPhone;
	}

	public String getCarryName() {
		return carryName;
	}

	public void setCarryName(String carryName) {
		this.carryName = carryName;
	}

	public Integer getIsChangeCar() {
		return isChangeCar;
	}

	public void setIsChangeCar(Integer isChangeCar) {
		this.isChangeCar = isChangeCar;
	}

	public Integer getIsAllowLocation() {
		return isAllowLocation;
	}

	public void setIsAllowLocation(Integer isAllowLocation) {
		this.isAllowLocation = isAllowLocation;
	}

	public BigDecimal getDealPrice() {
		return dealPrice;
	}

	public void setDealPrice(BigDecimal dealPrice) {
		this.dealPrice = dealPrice;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
