package com.tyt.transport.querybean;

import com.tyt.model.TransportDone;

public class TransportDoneListBean extends TransportDone {

    private static final long serialVersionUID = -2416375555077533804L;
    /**
     * 是否后台货源0不是1是
     */
    private Integer isBackendTransport=0;

    /**
     * 订金类型（0不退还；1退还）
     */
    private Integer refundFlag;

    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    private String labelJson;

    /**
     * 是否开票货源 0：否；1：是
     */
    private Integer invoiceTransport;

    /**
     * 优车好货（秒抢货源） 1：是；0：否
     */
    private Integer instantGrab;

    /**
     * 优车好货（秒抢货源） 刷新结束展示文案
     */
    private String instantGrabResendOverWord;

    /**
     * 开票货源开票主体ID
     */
    private Long invoiceSubjectId;

    private String startLongitude;
    private String startLatitude;
    private String destLongitude;
    private String destLatitude;
    private String startDetailAdd;
    private String destDetailAdd;

    private String distance;


    public String getStartLongitude() {
        return startLongitude;
    }

    public void setStartLongitude(String startLongitude) {
        this.startLongitude = startLongitude;
    }

    public String getStartLatitude() {
        return startLatitude;
    }

    public void setStartLatitude(String startLatitude) {
        this.startLatitude = startLatitude;
    }

    public String getDestLongitude() {
        return destLongitude;
    }

    public void setDestLongitude(String destLongitude) {
        this.destLongitude = destLongitude;
    }

    public String getDestLatitude() {
        return destLatitude;
    }

    public void setDestLatitude(String destLatitude) {
        this.destLatitude = destLatitude;
    }

    public String getStartDetailAdd() {
        return startDetailAdd;
    }

    public void setStartDetailAdd(String startDetailAdd) {
        this.startDetailAdd = startDetailAdd;
    }

    public String getDestDetailAdd() {
        return destDetailAdd;
    }

    public void setDestDetailAdd(String destDetailAdd) {
        this.destDetailAdd = destDetailAdd;
    }


    public Integer getIsBackendTransport() {
        return isBackendTransport;
    }

    public void setIsBackendTransport(Integer isBackendTransport) {
        this.isBackendTransport = isBackendTransport;
    }

    public Integer getRefundFlag() {
        return refundFlag;
    }

    public void setRefundFlag(Integer refundFlag) {
        this.refundFlag = refundFlag;
    }

    public String getLabelJson() {
        return labelJson;
    }

    public void setLabelJson(String labelJson) {
        this.labelJson = labelJson;
    }

    public Integer getInvoiceTransport() {
        return invoiceTransport;
    }

    public void setInvoiceTransport(Integer invoiceTransport) {
        this.invoiceTransport = invoiceTransport;
    }

    public Integer getInstantGrab() {
        return instantGrab;
    }

    public void setInstantGrab(Integer instantGrab) {
        this.instantGrab = instantGrab;
    }

    public String getInstantGrabResendOverWord() {
        return instantGrabResendOverWord;
    }

    public void setInstantGrabResendOverWord(String instantGrabResendOverWord) {
        this.instantGrabResendOverWord = instantGrabResendOverWord;
    }

    public Long getInvoiceSubjectId() {
        return invoiceSubjectId;
    }

    public void setInvoiceSubjectId(Long invoiceSubjectId) {
        this.invoiceSubjectId = invoiceSubjectId;
    }

    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }
}
