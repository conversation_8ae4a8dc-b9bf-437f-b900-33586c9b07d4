package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * This class represents a request object for fetching the average price of same transport goods.
 * It extends the BaseParameter class and implements the Serializable interface.
 *
 * Fields:
 * - goodTypeName: The name of the goods type, such as "装载机" or "挖掘机".
 * - startCity: The city of departure.
 * - destCity: The destination city.
 * - distance: The distance between the departure city and the destination city.
 * - weight: The weight unit in tons.
 * - weightMin: The minimum weight value.
 * - weightMax: The maximum weight value.
 * - dates: A list of dates.
 *
 * This class also contains a usage example in the FreightController class:
 *
 * /**
 *  * 相似货源平均成交价
 *  * @param sameTransportAveragePriceReq sameTransportAveragePriceReq
 *  * @return ResultMsgBean
 *  * /
 * @RequestMapping(value = "/getSameTransportAveragePrice")
 * @ResponseBody
 * public ResultMsgBean getSameTransportAveragePrice(SameTransportAveragePriceReq sameTransportAveragePriceReq) {
 *     return freightDetailService.getSameTransportAveragePrice(sameTransportAveragePriceReq);
 * }
 *
 * Usage example in the TytTransportAfterOrderDataMapper class:
 *
 * @Mapper
 * public interface TytTransportAfterOrderDataMapper extends CustomBaseMapper<TytTransportAfterOrderData> {
 *
 *     List<SameTransportAveragePriceData> getSameTransportAveragePrice(@Param("req") SameTransportAveragePriceReq sameTransportAveragePriceReq);
 *
 * }
 */
@Data
public class SameTransportAveragePriceReq extends BaseParameter implements Serializable {

    private Long srcMsgId;

    /**
     * 货物类型名称,如“装载机”，“挖掘机”
     */
    private String goodTypeName;

    /**
     * 出发地城市
     */
    private String startCity;

    /**
     * 目的地市
     */
    private String destCity;

    /**
     * 出发地目的地之间距离
     */
    private BigDecimal distance;

    /**
     * 重量单位吨
     */
    private String goodsWeight;

    /**
     * 重量低值
     */
    private BigDecimal weightMin;

    /**
     * 重量高值
     */
    private BigDecimal weightMax;


    private List<Date> dates;

}
