package com.tyt.transport.querybean;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.model.TytCarOwnerIntention;

/**
 * 人工派单货源对应车辆Bean
 * 
 * <AUTHOR>
 * @date 2018-01-27 下午20:13:38
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarOwnerIntentionBean extends TytCarOwnerIntention {
	private String headCity;// 车头牌照头字母
	private String headNo;// 车头牌照号码
	private String tailCity;// 挂车牌照头字母
	private String tailNo;// 挂车牌照号码

	public String getHeadCity() {
		return headCity;
	}

	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}

	public String getHeadNo() {
		return headNo;
	}

	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}

	public String getTailCity() {
		return tailCity;
	}

	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}

	public String getTailNo() {
		return tailNo;
	}

	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}

	@Override
	public String toString() {
		return "CarOwnerIntentionBean{" +
				"headCity='" + headCity + '\'' +
				", headNo='" + headNo + '\'' +
				", tailCity='" + tailCity + '\'' +
				", tailNo='" + tailNo + '\'' +
				"} " + super.toString();
	}
}
