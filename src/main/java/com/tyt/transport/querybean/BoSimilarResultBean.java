package com.tyt.transport.querybean;

import java.io.Serializable;
import java.util.Map;

/**
 * @ClassName BoSimilarResultBean
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/6/7 10:22
 * @Verdion 1.0
 **/
public class BoSimilarResultBean  implements Serializable {

    /** 结果集 */
    private  long totalSize;
    /** 结果集 */
    private Map<Object,Object> result;

    public long getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(long totalSize) {
        this.totalSize = totalSize;
    }

    public Map<Object, Object> getResult() {
        return result;
    }

    public void setResult(Map<Object, Object> result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "BoSimilarResultBean{" +
                "totalSize=" + totalSize +
                ", result=" + result +
                '}';
    }
}
