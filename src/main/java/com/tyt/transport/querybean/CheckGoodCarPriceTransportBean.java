package com.tyt.transport.querybean;

import lombok.*;

public class CheckGoodCarPriceTransportBean extends TransportCarryBean {

    private Long userId;

    private Integer publishType;

    private String price;

    public CheckGoodCarPriceTransportBean(String startProvince, String startCity, String startArea, String destProvince, String destCity, String destArea, String goodsName, String goodsWeight, String goodsLength, String goodsWide, String goodsHigh, String distance, Integer excellentGoods, Long userId, Integer publishType, String price, String goodTypeName) {
        super(startProvince, startCity, startArea, destProvince, destCity, destArea, goodsName, goodsWeight, goodsLength, goodsWide, goodsHigh, distance, excellentGoods, goodTypeName);
        this.userId = userId;
        this.publishType = publishType;
        this.price = price;
    }

    public CheckGoodCarPriceTransportBean() {

    }

    @Override
    public Long getUserId() {
        return userId;
    }

    @Override
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getPublishType() {
        return publishType;
    }

    public void setPublishType(Integer publishType) {
        this.publishType = publishType;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }
}
