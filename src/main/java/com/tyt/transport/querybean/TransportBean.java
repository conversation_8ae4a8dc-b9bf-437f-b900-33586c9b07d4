package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.plat.utils.CityUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportBean implements Serializable {
    private static final long serialVersionUID = 2275817212953004074L;
    private Long id;
    private String startPoint;
    private String destPoint;
    private String taskContent;
    private Integer status;
    private String startCoord;
    private String destCoord;
    private Integer verifyFlag = 0;
    /* 20150716新增字段 */
    private Long userId;// '发布人usreID 关联tyt_user表id',
    private String price;// 运费
    private String startDetailAdd;// '出发地详细地址',
    private String destDetailAdd;// '目的地详细地址',
    private String startLatitude;// '出发地纬度',
    private String startLongitude;// '出发地经度',
    private String destLongitude;// '目的地经度',
    private String destLatitude;// '目的地纬度',
    private String distance;// '出发地目的地之间距离',
    private Date pubDate;// '发布日期',
    private String remark;// '备注',
    private String isInfoFee;
    private String nickName;
    private String nickNameByAes;
    private Date regTime;
    private String weight;// '重量单位吨',
    // 是否需要加密昵称
    private Integer isNeedDecrypt;

    //5600版本升级，列表增加显示以下
    private String startProvinc;
    private String startCity;
    private String startArea;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private Integer userType;
    private Long srcMsgId;

    //5920版本升级，列表增加显示以下
    private String carLength;  //车辆长度
    private String carType;   //车辆类型
    private String specialRequired;   //特殊要求

    //----------19-08-21---相似货源分组
    /**
     * 相似编码
     */
    private String similarityCode;
    /**
     * 相似货源首发ID
     */
    private Long similarityFirstId;
    /**
     * 相似货源首发信息
     */
    private String similarityFirstInfo;

    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;

    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;

    /**
     * 货物长单位米
     */
    private String length;
    /**
     * 货物宽单位米
     */
    private String wide;
    /**
     * 货物高单位米
     */
    private String high;
    //-------app6100 新增----
    /**
     * 订单编号
     */
    private String tsOrderNo;

    /**
     * 信用分
     */
    private BigDecimal totalScore;

    /**
     * 信用分等级 "1 2 3 4 5"
     */
    private Integer rankLevel;
    /**
     * (0非急走，1急走)
     */
    private Integer exposureFlag;

    /**
     * 货源来源（1货主；2调度客服；3:个人货主）
     */
    private Integer sourceType;

    /**
     * 用户发布货源成交个数(来自user_sub表的deal_num)
     */
    private Integer tradeNum;

    /**
     * 官方授权昵称
     */
    private String authName;
    /**
     * 官方授权昵称(tea加密)
     */
    private String authNameTea;

    /**
     * 是否是保障货源 0：否  1：是
     */
    private Integer guaranteeGoods;

    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    private String labelJson;

    /**
     * 出发地经度
     */
    private String startLongitudeStr;

    /**
     * 出发地纬度
     */
    private String startLatitudeStr;

    /**
     * 是否是 17.5 米专享 0：否 1：是
     */
    private Integer exclusiveType;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;

    /**
     * 优推好车主到期时间
     */
    private Date priorityRecommendExpireTime;

    /**
     * 是否是优车货源（0否，1是）
     */
    private Integer excellentGoods;
    /**
     * 货名备注
     */
    private String machineRemark;

    /**
     * 是否开票货源 0：否；1：是
     */
    private Integer invoiceTransport;

    /**
     * 附加运费
     */
    private String additionalPrice;

    /**
     * 企业税率
     */
    private BigDecimal enterpriseTaxRate;


    /**
     * 开票货源开票主体ID
     */
    private Long invoiceSubjectId;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    private Integer driverDriving;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 当前货源是否对该货主捂货 1捂货0不捂货
     */
    private Integer coverGoodsType;

    /**
     * 是否显示超额保障标签 1是 0否
     */
    private Integer showExcessCoverageLabel = 0;

    /**
     * 是否显示送订金券标签： 1是 0否
     */
    private Integer showDepositCouponsLabel = 0;

    /** ======================================== **/

    public void initStartLongitudeStr() {
        this.startLongitudeStr = CityUtil.toMapPointStr(startLongitude);

    }

    public void initStartLatitudeStr() {
        this.startLatitudeStr = CityUtil.toMapPointStr(startLatitude);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
