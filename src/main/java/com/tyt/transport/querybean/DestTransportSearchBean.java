package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;

import java.io.Serializable;

/**
 * 目的地货源搜索条件
 *
 * Date 2019-12-04  xyy
 */
public class DestTransportSearchBean extends BaseParameter {

    private static final long serialVersionUID = 7893074232098678070L;
    private String startProvinc;
    private String startCity;
    private String startArea;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private Long goodsId;

    public String getStartProvinc() {
        return startProvinc;
    }

    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }

    public String getStartCity() {
        return startCity;
    }

    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }

    public String getStartArea() {
        return startArea;
    }

    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }

    public String getDestProvinc() {
        return destProvinc;
    }

    public void setDestProvinc(String destProvinc) {
        this.destProvinc = destProvinc;
    }

    public String getDestCity() {
        return destCity;
    }

    public void setDestCity(String destCity) {
        this.destCity = destCity;
    }

    public String getDestArea() {
        return destArea;
    }

    public void setDestArea(String destArea) {
        this.destArea = destArea;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    @Override
    public String toString() {
        return "DestTransportSearchBean{" +
                "startProvinc='" + startProvinc + '\'' +
                ", startCity='" + startCity + '\'' +
                ", startArea='" + startArea + '\'' +
                ", destProvinc='" + destProvinc + '\'' +
                ", destCity='" + destCity + '\'' +
                ", destArea='" + destArea + '\'' +
                ", goodsId=" + goodsId +
                '}';
    }
}
