package com.tyt.transport.querybean;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 导航查询参数
 * <AUTHOR>
 * @since 2025/07/21 14:50
 */
@Getter
@Setter
public class NavigationQueryDTO {
    /**
     * 出发地经度
     */
    @NotNull(message = "出发地经度不能为空")
    private BigDecimal startLongitude;
    /**
     * 出发地纬度
     */
    @NotNull(message = "出发地纬度不能为空")
    private BigDecimal startLatitude;
    /**
     * 目的地经度
     */
    @NotNull(message = "目的地经度不能为空")
    private BigDecimal destLongitude;
    /**
     * 目的地纬度
     */
    @NotNull(message = "目的地纬度不能为空")
    private BigDecimal destLatitude;
    /**
     * 吨重
     */
    private BigDecimal weight;
}
