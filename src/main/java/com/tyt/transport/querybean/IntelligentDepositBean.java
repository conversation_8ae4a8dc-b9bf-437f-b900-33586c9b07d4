package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 智能订金请求参数实体
 *
 * <AUTHOR>
 * @since 2024-08-23 17:34
 */
@Data
public class IntelligentDepositBean extends BaseParameter {
    /**
     * 是否专票货源 0：否；1:是
     */
    private Integer invoiceTransport;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 距离
     */
    private BigDecimal distance;
    /**
     * 默认填充的订金
     */
    private BigDecimal defaultDeposit;
    /**
     * 默填充的退还方式
     */
    private Integer defaultRefundFlag;
    /**
     * 用户提交的订金
     */
    private BigDecimal userDeposit;
    /**
     * 用户提交的退还方式
     */
    private Integer userRefundFlag;
}
