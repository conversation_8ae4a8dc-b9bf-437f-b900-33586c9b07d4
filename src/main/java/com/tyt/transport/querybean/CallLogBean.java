package com.tyt.transport.querybean;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.plat.utils.BigDecimalSerialize;
import com.tyt.plat.utils.CityUtil;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 封装用户拨打电话信息的实体
 *
 * <AUTHOR>
 * @date 2016-8-16上午9:33:38
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties({"payStatus", "robStatus", "goodsUserId"})
@Getter
@Setter
public class CallLogBean {
    /*
     * 货物出发地
     */
    private String startPosition;
    /*
     * 货物目的地
     */
    private String destPosition;
    /**
     * 出发地 - 省
     */
    private String startProvinc;
    /**
     * 出发地 - 市
     ***/
    private String startCity;
    /**
     * 出发地 - 区
     */
    private String startArea;
    /**
     * 目的地 - 省
     */
    private String destProvinc;
    /**
     * 目的地 - 市
     */
    private String destCity;
    /**
     * 目的地 - 区
     */
    private String destArea;
    /*
     * 联系人1
     */
    private String tel;
    /*
     * 联系人2
     */
    private String tel3;
    /*
     * 联系人3
     */
    private String tel4;
    /*
     * 货物id
     */
    private String goodId;
    private Long srcMsgId;// 原货物唯一标识
    /*
     * 通话时间
     */
    private String callTime;
    /*
     * 货物描述
     */
    private String remark;
    /*
     * 拨打电话结果码，1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易 6：已经拉走
     */
    private String callResultCode;
    //电话标注中文
    private String callResultName;
    /*
     * 被拨打电话的货物发布的时间，此字段在APP从列表进入标注页面的时候需要用到
     */
    private String pubDate;
    /*
     * 当前用户是否对该货物拨打过电话标示 返回结构"是否打过电话状态码_通话标注状态码"，具体参看接口文档
     */
    private String callStatus;
    private String isInfoFee; // 是否收信息费货源 0是不需要1是需要
    private String hasMakeOrder;// 是否下过单:0否、1是
    private String tsOrderNo;// 运单号

    /**
     * 货源类型（电议1，一口价2）
     */
    @Getter
    @Setter
    private Short publishType;

    /**
     * 信息费
     */
    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal infoFee;

    /*
     * 是否可以打电话，0可以，1不可以
     */
    private Integer isCanCall = 0;
    /*
     * 超过电话拨打限制提示文字，不同的限制类型文本不同，该字段只有在超过拨打电话限制时才有值
     */
    private String promptContent;
    /*
     * 拨打电话限制类型，1：未进行车辆认证 2：未进行身份认证 3：试用期用户 4: 缴费到期
     * 5：超过所有限制，即车辆认证，身份认证，缴费的拨打电话次数都已用完
     */
    private Integer limitType;
    /*
     * 是否需要对电话加密 1 需要 2 不需要
     */
    private Integer isNeedDecrypt;

    /**
     * 发货人昵称，2019-01-30
     */
    private String nickName;

    // 1：过期 2：撤销 3：成交
    private Integer goodStatus;

    /**
     * 通话记录备注
     */
    private String reference;

    /**
     * 装货时间
     */
    private Date loadingTime;
    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;

    /**
     * 卸货时间
     */
    private Date unloadTime;

    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;

    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 长
     */
    private BigDecimal length;
    /**
     * 宽
     */
    private BigDecimal width;
    /**
     * 高
     */
    private BigDecimal height;
    /**
     * 运费
     */
    private Integer price;

    /**
     * 注册时间
     */
    private Date regTime;

    /**
     * 用户类型0试用 1付费 2未激活
     */
    private Integer userType;
    /**
     * 和我交易
     */
    private Integer tradeNums = 0;
    /**
     * 平台交易
     */
    private Integer coopNums = 0;
    /**
     * 货源持有人
     */
    private Long goodsUserId;

    /**
     * 支付状态
     */
    private Integer payStatus;
    /**
     * 支付状态
     */
    private Integer robStatus;
    /**
     * 是否显示详情页面的去支付按钮，0显示，1不显示
     */
    private Integer isPaySuccess = 0;
    /**
     * 已支付金额
     */
    private Integer payAgencyMoney;
    /**
     * 呼叫人ID
     */
    private Long callerId;

    /**
     * 是否是 17.5 米专享 0：否 1：是
     */
    private Integer exclusiveType;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;

    /**
     * 货源来源（1货主；2调度客服；3个人货主；）
     */
    private Integer sourceType;

    /**
     * 授权昵称
     */
    private String authName;

    /**
     * 授权昵称加密
     */
    private String authNameTea;

    /**
     * 是否是保障货源 0：否  1：是
     */
    private Integer guaranteeGoods;

    /**
     * 出发地经度
     */
    private Integer startLongitude;

    /**
     * 出发地纬度
     */
    private Integer startLatitude;


    /**
     * 出发地经度
     */
    private String startLongitudeStr;

    /**
     * 出发地纬度
     */
    private String startLatitudeStr;
    /**
     * 信用等级
     */
    private Integer rankLevel;

    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    private String labelJson;

    private Integer excellentGoods;
    /**
     * 技术服务费
     */
    private BigDecimal tecServiceFee;

    /**
     * 优推好车主到期时间
     */
    private Date priorityRecommendExpireTime;

    /**
     * 货名备注
     */
    private String machineRemark;

    /**
     * 货源是否有未处理的回价
     */
    private Boolean haveNewTransportQuotedPrice;

    /**
     * 对于该货源的出价是否以被同意
     */
    private Boolean haveAgreeTransportQuotedPrice;

    /**
     * 是否开票货源 0：否；1：是
     */
    private Integer invoiceTransport;

    /**
     * 附加运费
     */
    private String additionalPrice;

    /**
     * 企业税率
     */
    private BigDecimal enterpriseTaxRate;

    /**
     * 开票货源开票主体ID
     */
    private Long invoiceSubjectId;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    private Integer driverDriving;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 当前货源是否对该货主捂货 1捂货0不捂货
     */
    private Integer coverGoodsType;

    /**
     * 是否可大厅抢单 0：否；1：是
     */
    private Integer declareInPublic = 0;

    /**
     * 是否显示超额保障标签 1是 0否
     */
    private Integer showExcessCoverageLabel = 0;

    /**
     * 是否显示送订金券标签： 1是 0否
     */
    private Integer showDepositCouponsLabel = 0;

    /**
     * 车方加价原因
     */
    private String reason;

    public void initStartLongitudeStr() {
        this.startLongitudeStr = CityUtil.toMapPointStr(startLongitude);

    }

    public void initStartLatitudeStr() {
        this.startLatitudeStr = CityUtil.toMapPointStr(startLatitude);
    }


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public void clearTel() {
        this.setTel(null);
        this.setTel3(null);
        this.setTel4(null);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CallLogBean that = (CallLogBean) o;
        return tsOrderNo.equals(that.tsOrderNo) &&
                callerId.equals(that.callerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tsOrderNo, callerId);
    }

}
