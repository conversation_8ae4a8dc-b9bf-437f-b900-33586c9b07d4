package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;

/**
 * 封装具体某项的电话次数限制的实体
 * 
 * <AUTHOR>
 * @date 2017-3-9下午4:29:19
 * @description
 */
public class CallPhoneLimitItemBean {
	/*
	 * 限制项的内容
	 */
	private String limitItemName;
	/*
	 * 排序值
	 */
	private Short sortValue;
	/*
	 * 拨打电话限制次数
	 */
	private Short callPhoneLimitTime;

	public Short getSortValue() {
		return sortValue;
	}

	public void setSortValue(Short sortValue) {
		this.sortValue = sortValue;
	}

	public CallPhoneLimitItemBean() {
	}

	public CallPhoneLimitItemBean(String limitItemName, Short callPhoneLimitTime, Short sortValue) {
		this.limitItemName = limitItemName;
		this.callPhoneLimitTime = callPhoneLimitTime;
		this.sortValue = sortValue;
	}

	public String getLimitItemName() {
		return limitItemName;
	}

	public void setLimitItemName(String limitItemName) {
		this.limitItemName = limitItemName;
	}

	public Short getCallPhoneLimitTime() {
		return callPhoneLimitTime;
	}

	public void setCallPhoneLimitTime(Short callPhoneLimitTime) {
		this.callPhoneLimitTime = callPhoneLimitTime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
