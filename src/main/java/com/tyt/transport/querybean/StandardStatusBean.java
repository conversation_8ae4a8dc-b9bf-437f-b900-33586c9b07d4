package com.tyt.transport.querybean;



import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 
 * <AUTHOR>
 * @date 2017-8-7下午3:13:46
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StandardStatusBean {
	private Integer id;
	@JsonProperty(value = "displayContent")
	private String brandType;
	private String machineType;
	private String brand;
	private String type;
	private String weight;
	private String length;
	private String width;
	private String height;
	/*
	 * 是否显示长宽高，0 显示 1 不显示
	 */
	private Integer lengthWidthHeightDisplay;
	/*
	 * 是否显示重量，0 显示 1 不显示
	 */
	private Integer weightDisplay;
	/*
	 * 0 标准化数据 1 准标准化数据 2 非标准化数据
	 */
	private Integer standardStatus;
	/*
	 * 货物台数
	 */
	private Integer goodNumber;

	public Integer getGoodNumber() {
		return goodNumber;
	}

	public void setGoodNumber(Integer goodNumber) {
		this.goodNumber = goodNumber;
	}

	public StandardStatusBean() {
	}

	public StandardStatusBean(Integer standardStatus) {
		this.standardStatus = standardStatus;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getLengthWidthHeightDisplay() {
		return lengthWidthHeightDisplay;
	}

	public void setLengthWidthHeightDisplay(Integer lengthWidthHeightDisplay) {
		this.lengthWidthHeightDisplay = lengthWidthHeightDisplay;
	}

	public String getBrandType() {
		return brandType;
	}

	public void setBrandType(String brandType) {
		this.brandType = brandType;
	}

	public String getMachineType() {
		return machineType;
	}

	public void setMachineType(String machineType) {
		this.machineType = machineType;
	}

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getLength() {
		return length;
	}

	public void setLength(String length) {
		this.length = length;
	}

	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public Integer getWeightDisplay() {
		return weightDisplay;
	}

	public void setWeightDisplay(Integer weightDisplay) {
		this.weightDisplay = weightDisplay;
	}

	public Integer getStandardStatus() {
		return standardStatus;
	}

	public void setStandardStatus(Integer standardStatus) {
		this.standardStatus = standardStatus;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
