package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

public class TransportSearchSqlBean {
    private Long startCoordX;
    private Long startCoordY;
    private Long startRange;
    private Long destCoordX=0L;
    private Long destCoordY=0L;
    private Long destRange=0L;
    private Integer queryType;
    private Long tsId;
    private Integer pageSize;
    private Integer userType;
    private String carLength;
    private String carType;
    private String specialRequired;
    //2019-12-04 xyy 增加重量查询条件
    private Integer startWeight;
    private Integer endWeight;

    //2020-07-17 新增 v6000版本筛选字段
    /**
     * 货物名称，以英文逗号隔开
     */
    private List<String> goodsName;
    /**
     * 货物尺寸长度最小值
     */
    private BigDecimal minLength;
    /**
     * 货物尺寸长度最大值
     */
    private BigDecimal maxLength;
    /**
     * 货物尺寸宽度最大值
     */
    private BigDecimal minWidth;
    /**
     * 货物尺寸宽度最大值
     */
    private BigDecimal maxWidth;
    /**
     * 货物尺寸高度最大值
     */
    private BigDecimal minHeight;
    /**
     * 货物尺寸高度最大值
     */
    private BigDecimal maxHeight;
    /**
     * 开始装车时间
     */
    private Date startLoadingTime;
    /**
     * 截止装车时间
     */
    private Date endLoadingTime;
    /**
     * 最小运价
     */
    private Integer minPrice;
    /**
     * 最大运价
     */
    private Integer maxPrice;

    public Set<String> getShieldingShipperSet() {
        return shieldingShipperSet;
    }

    public void setShieldingShipperSet(Set<String> shieldingShipperSet) {
        this.shieldingShipperSet = shieldingShipperSet;
    }

    /**
     * 用户屏蔽发货人列表
     */
    private Set<String> shieldingShipperSet;



    public Long getStartCoordX() {
        return startCoordX;
    }

    public void setStartCoordX(Long startCoordX) {
        this.startCoordX = startCoordX;
    }

    public Long getStartCoordY() {
        return startCoordY;
    }

    public void setStartCoordY(Long startCoordY) {
        this.startCoordY = startCoordY;
    }

    public Long getStartRange() {
        return startRange;
    }

    public void setStartRange(Long startRange) {
        this.startRange = startRange;
    }

    public Long getDestCoordX() {
        return destCoordX;
    }

    public void setDestCoordX(Long destCoordX) {
        this.destCoordX = destCoordX;
    }

    public Long getDestCoordY() {
        return destCoordY;
    }

    public void setDestCoordY(Long destCoordY) {
        this.destCoordY = destCoordY;
    }

    public Long getDestRange() {
        return destRange;
    }

    public void setDestRange(Long destRange) {
        this.destRange = destRange;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public Long getTsId() {
        return tsId;
    }

    public void setTsId(Long tsId) {
        this.tsId = tsId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getCarLength() {
        return carLength;
    }

    public void setCarLength(String carLength) {
        this.carLength = carLength;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getSpecialRequired() {
        return specialRequired;
    }

    public void setSpecialRequired(String specialRequired) {
        this.specialRequired = specialRequired;
    }

    public Integer getStartWeight() {
        return startWeight;
    }

    public void setStartWeight(Integer startWeight) {
        this.startWeight = startWeight;
    }

    public Integer getEndWeight() {
        return endWeight;
    }

    public void setEndWeight(Integer endWeight) {
        this.endWeight = endWeight;
    }

    public List<String> getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(List<String> goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getMinLength() {
        return minLength;
    }

    public void setMinLength(BigDecimal minLength) {
        this.minLength = minLength;
    }

    public BigDecimal getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(BigDecimal maxLength) {
        this.maxLength = maxLength;
    }

    public BigDecimal getMinWidth() {
        return minWidth;
    }

    public void setMinWidth(BigDecimal minWidth) {
        this.minWidth = minWidth;
    }

    public BigDecimal getMaxWidth() {
        return maxWidth;
    }

    public void setMaxWidth(BigDecimal maxWidth) {
        this.maxWidth = maxWidth;
    }

    public BigDecimal getMinHeight() {
        return minHeight;
    }

    public void setMinHeight(BigDecimal minHeight) {
        this.minHeight = minHeight;
    }

    public BigDecimal getMaxHeight() {
        return maxHeight;
    }

    public void setMaxHeight(BigDecimal maxHeight) {
        this.maxHeight = maxHeight;
    }

    public Date getStartLoadingTime() {
        return startLoadingTime;
    }

    public void setStartLoadingTime(Date startLoadingTime) {
        this.startLoadingTime = startLoadingTime;
    }

    public Date getEndLoadingTime() {
        return endLoadingTime;
    }

    public void setEndLoadingTime(Date endLoadingTime) {
        this.endLoadingTime = endLoadingTime;
    }

    public Integer getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(Integer minPrice) {
        this.minPrice = minPrice;
    }

    public Integer getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(Integer maxPrice) {
        this.maxPrice = maxPrice;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
