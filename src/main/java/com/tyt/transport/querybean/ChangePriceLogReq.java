package com.tyt.transport.querybean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChangePriceLogReq {

    private Long srcMsgId;

    private BigDecimal price;

    private Integer publishType;

    /**
     * 操作入口 1:编辑发布；2:直接发布；3:填价、加价；4:转一口价、转电议；5:拒绝报价修改运费；6:货源诊断
     */
    private Integer operationType;

}
