package com.tyt.transport.querybean;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/5/19 19:08
 */
@Data
public class BackendTransportBean implements Serializable {
    /**
     * 后台货源表id
     */
    private Long id;
    /**
     * 货源main表id
     */
    private Long msgId;
    /**
     * 状态 1-待接单 2-已取消 3-已接单 4-已完成
     */
    private Integer status;
    /**
     * 10-待接单 11-已接单未发布 20-已撤销(定时) 21-已线下成交 22-已撤销(手动) 23-已接单取消 30-已接单 31运输中 40已完成
     */
    private Integer orderStatus;
    /**
     * 当前推送版本号  批次_分页次数_本批次总条数
     */
    private String batch;
    /**
     * 小程序发货人id
     */
    private Long appletsUserId;
    /**
     * 接单人id
     */
    private Long receiverUserId;
    /**
     * 接单人电话
     */
    private String receiverPhone;
    /**
     * 接单人展示昵称
     */
    private String receiverShowName;
    /**
     * 出发地
     */
    private String startPoint;
    /**
     * 目的地
     */
    private String destPoint;
    /**
     * 货物名称
     */
    private String taskContent;
    /**
     * 货物名称
     */
    private String tel;
    private String tel3;

    private Integer findCarType;

    private String orderNo;
}
