package com.tyt.transport.querybean;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 代调交互传输dto
 * @date 2023-08-17-08-52-45
 */
@Data
public class DispatchParamBean {

    private Long userId;
    private Long goodsId;
    private Integer operateType;
    private String backoutReasonKey;
    private Integer backoutReasonValue;
    private String specificReason;
    private String remark;
    private Integer sourceType;
    private Long cargoId;
    private Long srcMsgId;

    /**
     * 后台专用撤销原因（中文）
     */
    private String backoutReasonKeyNew;

    /**
     * 后台专用撤销原因（code）
     */
    private Integer backoutReasonValueNew;

}
