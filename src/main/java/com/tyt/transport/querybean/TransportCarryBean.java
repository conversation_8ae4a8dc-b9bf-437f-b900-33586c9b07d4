package com.tyt.transport.querybean;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName TransportCarryBean
 * @Description 运费参考价查询
 * <AUTHOR> Lion
 * @Date 2022/7/28 16:02
 * @Verdion 1.0
 **/
@Data
public class TransportCarryBean extends BaseParameter implements Serializable {

    //出发地省
    private String startProvince;
    //出发地市
    private String startCity;
    //出发地区县
    private String startArea;
    //目的地省
    private String destProvince;
    //目的地市
    private String destCity;
    //目的地区县
    private String destArea;
    //货物描述
    private String goodsName;
    //重量
    private String goodsWeight;
    //长
    private String goodsLength;
    //宽
    private String goodsWide;
    //高
    private String goodsHigh;

    //距离字段
    private String distance;
    // 是否是优车货源 0：否  1：是
    private Integer excellentGoods;

    /**
     * 货类
     */
    private String goodTypeName;

    public TransportCarryBean(String startProvince, String startCity, String startArea, String destProvince, String destCity, String destArea, String goodsName, String goodsWeight, String goodsLength, String goodsWide, String goodsHigh, String distance, Integer excellentGoods, String goodTypeName) {
        this.startProvince = startProvince;
        this.startCity = startCity;
        this.startArea = startArea;
        this.destProvince = destProvince;
        this.destCity = destCity;
        this.destArea = destArea;
        this.goodsName = goodsName;
        this.goodsWeight = goodsWeight;
        this.goodsLength = goodsLength;
        this.goodsWide = goodsWide;
        this.goodsHigh = goodsHigh;
        this.distance = distance;
        this.excellentGoods = excellentGoods;
        this.goodTypeName = goodTypeName;
    }

    public TransportCarryBean() {
    }
}
