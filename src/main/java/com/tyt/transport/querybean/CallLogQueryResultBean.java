package com.tyt.transport.querybean;

import java.io.Serializable;
import java.util.List;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 封装返回给客户端电话日志记录的信息的实体
 * 
 * <AUTHOR>
 * @date 2016-8-16上午9:39:05
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CallLogQueryResultBean implements Serializable {
	private static final long serialVersionUID = -6145087845329685473L;

	private int currentPage;
	private int totalRecord;
	private int pageSize;
	private int maxPage;
	List<CallLogBean> data;

	public int getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}

	public int getTotalRecord() {
		return totalRecord;
	}

	public void setTotalRecord(int totalRecord) {
		this.totalRecord = totalRecord;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public int getMaxPage() {
		return maxPage;
	}

	public void setMaxPage(int maxPage) {
		this.maxPage = maxPage;
	}

	public List<CallLogBean> getData() {
		return data;
	}

	public void setData(List<CallLogBean> data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
