package com.tyt.transport.querybean;
import java.io.Serializable;
import java.util.Date;


import com.fasterxml.jackson.annotation.JsonInclude;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportVaryBean implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1841330340714556270L;
	private Long id;
	private Long tsId;//运输信息表ID
	private Integer status;//状态
	private Date updateTime;//变动时间
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getTsId() {
		return tsId;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
}
