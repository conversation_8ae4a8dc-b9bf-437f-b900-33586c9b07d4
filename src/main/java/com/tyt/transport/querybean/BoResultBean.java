package com.tyt.transport.querybean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 5920
 * @date 2019/5/18
 */


public class BoResultBean<T>  implements Serializable {
    /** 结果集 */
    private  long totalSize;
    /** 结果集 */
    private List<T> result;

    public long getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(int totalSize) {
        this.totalSize = totalSize;
    }

    public List<T> getResult() {
        return result;
    }

    public void setResult(List<T> result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "MoResultBean{" +
                "totalSize=" + totalSize +
                ", result=" + result +
                '}';
    }
}
