package com.tyt.transport.querybean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.base.bean.BaseParameter;
import com.tyt.plat.utils.BigDecimalSerialize;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 5920
 * @date 2019/7/3
 */
@Data
public class TransportPublishBean extends BaseParameter implements Serializable {

    private Long tsId;
    // 人工派单
    private Integer isMt;

    private String linkMan;
    private String tel;
    private String tel3;
    private String tel4;
    //TODO 经纬度参数非0校验
    private BigDecimal startLatitude;
    private BigDecimal startLongitude;
    private BigDecimal destLatitude;
    private BigDecimal destLongitude;

    private String taskContent;
    private String remark;
    private String type;
    private String brand;
    private String goodTypeName;
    private Integer isStandard;
    private Integer machineNumber;
    private Integer matchItemId;
    private String resend;

    // 所需车辆类型/长度/特殊需求
    private String carType;
    private String carLength;
    private String specialRequired;

    private String price;


    // 距离/重量/长度/宽/高/是否超重
    private BigDecimal distance;
    private String weight;
    private String length;
    private String wide;
    private String high;
    private String isSuperelevation;

    // 出发地完整地址/省/市/县/详细地址

    private String startAdcode;//出发地行政编码
    private String startPoint;
    private String startProvinc;
    private String startCity;
    private String startArea;
    private String startDetailAdd;

    // 目的地完整地址/省/市/县/详细地址
    private String destAdcode;//目的地行政编码
    private String destPoint;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private String destDetailAdd;

    private Integer isInfoFee;


    /*
      出发地x坐标
     */
    private BigDecimal startCoordX;
    /*
      出发地y坐标
     */
    private BigDecimal startCoordY;


    /*
      目的地X坐标
     */
    private BigDecimal destCoordX;
    /*
     目的地Y坐标
    */
    private BigDecimal destCoordY;


    private String basePrice;
    private String profixRate;

    //--------------------------------------------------------------------------------------------------
    //车货拆分新增参数
    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;
    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 车辆最小长度
     */
    private BigDecimal carMinLength;
    /**
     * 车辆最大长度
     */
    private BigDecimal carMaxLength;
    /**
     * 挂车样式
     */
    private String carStyle;
    /**
     * 工作面高最小值
     */
    private BigDecimal workPlaneMinHigh;
    /**
     * 工作面高最大值
     */
    private BigDecimal workPlaneMaxHigh;
    /**
     * 工作面长最小值
     */
    private BigDecimal workPlaneMinLength;
    /**
     * 工作面长最大值
     */
    private BigDecimal workPlaneMaxLength;
    /**
     * 是否需要带爬梯 0 是 1 需要 2不需要
     */
    private String climb;
    /**
     * 是否货主接单货源 1是 2和空或者其他为否
     */
    private Long backendId;
    /**
     * 所需车辆长度标签
     */
    private String carLengthLabels;

    /**
     * * 轮胎外露标识 0不限 1是 2 否
     */
    private String tyreExposedFlag;
    /**
     * 调车数量
     */
    private Integer shuntingQuantity;

    private Integer publishType;

    private Integer busPublishType; //1推熟车  2.平台找车
    private Long friendCarUserId; //推熟车的用户id
    private Integer isBackendTransport;

    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal infoFee;

    /**
     * 是否是 17.5 米专享 0：否 1：是
     */
    @Column(name = "exclusive_type")
    private Integer exclusiveType;

    /**
     * 信用分
     */
    @Column(name = "total_score")
    private BigDecimal totalScore;

    /**
     * 信用分等级 "1 2 3 4 5"
     */
    @Column(name = "rank_level")
    private Integer rankLevel;

    /**
     * 是否显示在找货大厅（企业货源）0.不显示 1.显示
     */
    @Column(name = "is_show")
    private Integer isShow;
    /**
     * 订金类型（0不退还；1退还）
     */
    private Integer refundFlag;
    /**
     * s是否有货源保障标识（0否；1是）
     */
    private Integer guaranteeGoods;

    /**
     * 重货确认（确认时传1）
     */
    private Integer confirm;

    /**
     * 是否是优推好车主  1：是  0 否
     */
    private Integer priorityRecommend;

    /**
     * 是否是优车货源（0-否，1-优车，2-专车）
     */
    private Integer excellentGoods;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    private Integer driverDriving;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 运价模式：1-固定运价，2-灵活运价
     */
    private Integer priceType;

    /**
     * 公里数（专车发货）
     */
    private BigDecimal distanceKilometer;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 签约合作商ID
     */
    private Long cargoOwnerId;

    /**
     * 装货联系电话
     */
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    private String unloadCellPhone;

    /**
     * 满满货源版本号
     */
    private Integer cargoVersion;

    /**
     * 货源来源
     * normal(1, "货主"),
     * dispatch(2, "调度客服"),
     * goods_owner(3, "个人货主"),
     * ymm(4, "运满满"),
     */
    private Integer sourceType;
    /**
     * 运满满货源唯一标识
     */
    private Long cargoId;

    /**
     * 技术服务费
     */
    private BigDecimal tecServiceFee;

    /**
     * 运满满货源真实货主电话
     */
    private String giveGoodsPhone;

    /**
     * 运满满货源真实货主名称
     */
    private String contactName;

    /**
     * 标准货名备注
     */
    private String machineRemark;

    /**
     * 1：待审核2：合规货名  3：非法货名
     */
    private Integer machineType;

    /**
     * 优车发货卡id,没有时传空.tyt_excellent_goods_card_user_detail表的id
     */
    private Long excellentCardId;


    /**
     * 是否开票货源 0：否；1：是
     */
    private Integer invoiceTransport;

    /**
     * 附加运费
     */
    private String additionalPrice;

    /**
     * 企业税率
     */
    private BigDecimal enterpriseTaxRate;

    /**
     * 发货时是否展示了优车好货（埋点专用参数）
     */
    private Integer publishTransportIsShowGoodCar;

    /**
     * 出发地地址来源,1:地图选点，2：手动搜索
     */
    private Integer startAddrSource;
    /**
     * 目的地地址来源     1:地图选点，2：手动搜索
     */
    private Integer destAddrSource;

    /**
     * 建议价最小价格
     */
    private Integer thMinPrice;

    /**
     * 建议价最大价格建议价格
     */
    private Integer thMaxPrice;

    /**
     * 建议价建议价格
     */
    private Integer suggestPrice;

    //推荐最低价
    private Integer suggestMinPrice;

    //推荐最高价
    private Integer suggestMaxPrice;

    /**
     * 是否优车运价货源 1:是 0:否
     */
    private Integer goodCarPriceTransport;

    //优车定价低值
    private Integer fixPriceMin;

    //优车定价高值
    private Integer fixPriceMax;

    //优车定价最快成交价格
    private Integer fixPriceFast;

    //是否弹出了优车定价货源卡片
    private Integer showGoodCarPriceTransportTab;

    /**
     * 回价助手运费上限
     */
    private Integer priceCap;

    /**
     * 是否自动优车运价货源
     */
    private Boolean automaticGoodCarPriceTransport;

    /**
     * 开票货源开票主体ID
     */
    private Long invoiceSubjectId;

    /**
     * 服务商编码
     */
    private String serviceProviderCode;

    /**
     * 开票货源指派车方联系方式
     */
    private String assignCarTel;

    /**
     * XHL开票-收货人姓名
     */
    private String consigneeName;

    /**
     * XHL开票-收货人联系方式
     */
    private String consigneeTel;

    /**
     * XHL开票-收货人企业名称
     */
    private String consigneeEnterpriseName;

    /**
     * 是否自动重发：1是0否
     */
    private Integer isAutoResend;

    /**
     * 来源页面：0 未知；1 APP货源详情页货源诊断入口；2 APP发货成功货源诊断入口；3 APP发货页低速找车弹窗；10 PC发货前填价弹窗；
     */
    private Integer sourcePage;

    /**
     * 预付运费
     */
    private BigDecimal prepaidPrice;

    /**
     * 到付运费
     */
    private BigDecimal collectedPrice;

    /**
     * 回单付运费
     */
    private BigDecimal receiptPrice;
    /**
     * 回单付金额
     */
    private Integer paymentsType;

    /**
     * 优惠金额
     */
    private Integer perkPrice;

    /**
     * 发货方式：0-普通找车，10-用户出价，20-特惠优车，21-快速优车，22-极速优车，30-专车
     */
    private Integer publishGoodsType;

    /**
     * 是否是优车2.0货源 1:否 2：是
     */
    private Integer excellentGoodsTwo = 1;

}
