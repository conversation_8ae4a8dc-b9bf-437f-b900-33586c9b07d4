package com.tyt.transport.querybean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description YMM VO
 * @date 2023-08-09-17-09-59
 */
@Data
public class TransportYmmListBean implements Serializable {

    private Long id;
    private Long cargoId;
    private String cargoName;
    private String contactTelephone;//货主电话
    private String startPoint;//装货地址
    private String destPoint;//卸货地址
    private Integer truckCount;// 用车数量
    private double minWeight;//最小重量
    private double maxWeight;//最大重量
    private Date loadTime;//装货时间
    private Date unloadTime;//卸货时间
    private double minLength;//最小长度
    private double minWidth;//最小宽度
    private double minHeight;//最小高度
    private String contactName;//货主姓名
    private int depositReturn;//定金是否退还 0；不退 1：退还
    private Long expectFreight;//运费单位：分
    private Long depositAmt;//定金：分
    private int dealMode;// 货成交模式  1：一口价 2.电议 3.委托 4.OPERATOR_TRUCK_BOOK
    private double loadLat;//装货维度
    private double loadLon;//装货经度
    private double unloadLat;
    private double unloadLon;
    /**
     *   卸货地址省、市、区
     */
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadDistrictName;
    private String unloadDetailAddress;
    private String shipperId;//YMM用户唯一标识
    /**
     *  装货地址省、市、县
     */
    private String loadProvinceName;
    private String loadCityName;
    private String loadDistrictName;
    private String loadDetailAddress;
    private Integer cargoVersion;//版本
    /**
     *   0 未删除 1已删除(下架)
     */
    private int delFlag;
    private String delReason;
    private Date createTime;
    /**
     * 车长
     */
    private String truckLengths;
    /**
     * 车型
     */
    private String truckTypes;
    private String infoJson;

}
