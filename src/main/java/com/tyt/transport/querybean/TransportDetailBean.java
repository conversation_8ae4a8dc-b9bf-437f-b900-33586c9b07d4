package com.tyt.transport.querybean;

import java.io.Serializable;
import java.sql.Timestamp;



import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportDetailBean implements Serializable {

	private static final long serialVersionUID = 1L;
	String price;// 运费
	Long id;
	String startPoint;
	String destPoint;
	String taskContent;
	Integer status;
	Integer source;
	String startCoord;
	String destCoord;
	Integer verifyFlag;
	/* 20150716新增字段 */
	Long userId;// '发布人usreID 关联tyt_user表id',

	String startDetailAdd;// '出发地详细地址',
	String destDetailAdd;// '目的地详细地址',
	// Date pubDate;//'发布日期',
	Timestamp ctime;
	String weight;// '重量单位吨',
	String length;// '货物长单位米',
	String wide;// '货物宽单位米',
	String high;// '货物高单位米',
	String isSuperelevation;// '是否三超 0未超1超',
	// String linkman;//'联系人',
	String nickName;// '联系人',
	String remark;// '备注',

	String tel;
	String tel3;// 联系人2
	String tel4;// 联系人3

	String startCoordX;// '出发地坐标x',
	String startCoordY;// '出发地坐标y',
	String destCoordX;// '目的地坐标x',
	String destCoordY;// '目的地坐标y',
	String startLatitude;// '出发地纬度',
	String startLongitude;// '出发地经度',
	String destLongitude;// '目的地经度',
	String destLatitude;// '目的地纬度',
	String distance;// '出发地目的地之间距离',

	String isCar;
	Integer userType;
	// 照片认证标志0未验证1通过3认证失败
	Integer verifyPhotoSign;

	// 用户分数
	Integer userPart;

	private String uploadCellPhone;
	private String isInfoFee;   
	/*
	 * 当前用户是否对该货物拨打过电话标示 返回结构"是否打过电话状态码_通话标注状态码"，具体参看接口文档
	 */
	private String callStatus;

	public String getPrice() {
		return price;
	}

	public String getCallStatus() {
		return callStatus;
	}

	public void setCallStatus(String callStatus) {
		this.callStatus = callStatus;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getStartPoint() {
		return startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	public String getDestPoint() {
		return destPoint;
	}

	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}

	public String getTaskContent() {
		return taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	public String getStartCoord() {
		return startCoord;
	}

	public void setStartCoord(String startCoord) {
		this.startCoord = startCoord;
	}

	public String getDestCoord() {
		return destCoord;
	}

	public void setDestCoord(String destCoord) {
		this.destCoord = destCoord;
	}

	public Integer getVerifyFlag() {
		return verifyFlag;
	}

	public void setVerifyFlag(Integer verifyFlag) {
		this.verifyFlag = verifyFlag;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getStartDetailAdd() {
		return startDetailAdd;
	}

	public void setStartDetailAdd(String startDetailAdd) {
		this.startDetailAdd = startDetailAdd;
	}

	public String getDestDetailAdd() {
		return destDetailAdd;
	}

	public void setDestDetailAdd(String destDetailAdd) {
		this.destDetailAdd = destDetailAdd;
	}

	// public Date getPubDate() {
	// return pubDate;
	// }
	// public void setPubDate(Date pubDate) {
	// this.pubDate = pubDate;
	// }
	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getLength() {
		return length;
	}

	public void setLength(String length) {
		this.length = length;
	}

	public String getWide() {
		return wide;
	}

	public void setWide(String wide) {
		this.wide = wide;
	}

	public String getHigh() {
		return high;
	}

	public void setHigh(String high) {
		this.high = high;
	}

	public String getIsSuperelevation() {
		return isSuperelevation;
	}

	public void setIsSuperelevation(String isSuperelevation) {
		this.isSuperelevation = isSuperelevation;
	}

	// public String getLinkman() {
	// return linkman;
	// }
	// public void setLinkman(String linkman) {
	// this.linkman = linkman;
	// }
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getTel3() {
		return tel3;
	}

	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}

	public String getTel4() {
		return tel4;
	}

	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}

	public String getStartCoordX() {
		return startCoordX;
	}

	public void setStartCoordX(String startCoordX) {
		this.startCoordX = startCoordX;
	}

	public String getStartCoordY() {
		return startCoordY;
	}

	public void setStartCoordY(String startCoordY) {
		this.startCoordY = startCoordY;
	}

	public String getDestCoordX() {
		return destCoordX;
	}

	public void setDestCoordX(String destCoordX) {
		this.destCoordX = destCoordX;
	}

	public String getDestCoordY() {
		return destCoordY;
	}

	public void setDestCoordY(String destCoordY) {
		this.destCoordY = destCoordY;
	}

	public String getStartLatitude() {
		return startLatitude;
	}

	public void setStartLatitude(String startLatitude) {
		this.startLatitude = startLatitude;
	}

	public String getStartLongitude() {
		return startLongitude;
	}

	public void setStartLongitude(String startLongitude) {
		this.startLongitude = startLongitude;
	}

	public String getDestLongitude() {
		return destLongitude;
	}

	public void setDestLongitude(String destLongitude) {
		this.destLongitude = destLongitude;
	}

	public String getDestLatitude() {
		return destLatitude;
	}

	public void setDestLatitude(String destLatitude) {
		this.destLatitude = destLatitude;
	}

	public String getDistance() {
		return distance;
	}

	public void setDistance(String distance) {
		this.distance = distance;
	}

	public Timestamp getCtime() {
		return ctime;
	}

	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}

	public String getIsCar() {
		return isCar;
	}

	public void setIsCar(String isCar) {
		this.isCar = isCar;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public Integer getVerifyPhotoSign() {
		return verifyPhotoSign;
	}

	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
		this.verifyPhotoSign = verifyPhotoSign;
	}

	public Integer getUserPart() {
		return userPart;
	}

	public void setUserPart(Integer userPart) {
		this.userPart = userPart;
	}

	public String getUploadCellPhone() {
		return uploadCellPhone;
	}

	public void setUploadCellPhone(String uploadCellPhone) {
		this.uploadCellPhone = uploadCellPhone;
	}

	public String getIsInfoFee() {
		return isInfoFee;
	}

	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}


}
