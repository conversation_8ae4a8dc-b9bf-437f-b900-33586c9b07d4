package com.tyt.transport.querybean;


import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportMtDetailBean implements Serializable {

    private static final long serialVersionUID = 2209843617761656228L;
    private String taskContent;   // 货物详情
    private Integer price;			//运价
    private String startPoint; //出发地城市
    private String startDetailAdd; // 出发地详细地址
    private String destPoint; //目的地城市
    private String destDetailAdd; // 目的地详细地址
    private String startLongitude;       //出发地经度
    private String startLatitude;        //出发地纬度
    private String destLongitude;       //目的地经度
    private String destLatitude;        //目的地纬度
    private String androidDistance; //公里数
    private Long carId;          //承运车辆ID
    private String licensePlate;   //车牌号
    private String starLevel;   	  //星级
    private String carryUser;  	  //承运人昵称
    private String headpic;			//头像
    private Integer verifyFlag;      //身份认证状态
    private String carryPhone;       //车主电话
    private String driverPhone;       //主司机电话
    private String secondaryDriverPhone; 	//副司机电话
    private String followDriverPhone;   //随车电话
    private Long contractId;            //合同ID
    private Long srcMsgId;              //货物ID
    private Integer contractStatus;        //合同状态
    private Integer transInsuranceStatus;	  //货物险状态
    private Integer status;                   //货物状态
    private Integer infoStatus;             //信息费状态
    private Integer carLocationScanOnOff;  //是否开启车辆定位开关
    private List process;				   //签署进度

    private String tailLicensePlate;

    //2018-11-28 人保货运险改动
    //是否购买过保险 1是(显示“查看保单”按钮)  2否(显示“买货运险”按钮)
    private Integer isBuyInsurance;
    //最后一次购买保险的保单Id
    private Long insuranceId;
    //最后一次购买保险的保单状态 0待支付 1已生效 2已退保
    private Integer insuranceStatus;

    
    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getStartDetailAdd() {
        return startDetailAdd;
    }

    public void setStartDetailAdd(String startDetailAdd) {
        this.startDetailAdd = startDetailAdd;
    }

    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    public String getDestDetailAdd() {
        return destDetailAdd;
    }

    public void setDestDetailAdd(String destDetailAdd) {
        this.destDetailAdd = destDetailAdd;
    }

    public String getAndroidDistance() {
        return androidDistance;
    }

    public void setAndroidDistance(String androidDistance) {
        this.androidDistance = androidDistance;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public String getStarLevel() {
        return starLevel;
    }

    public void setStarLevel(String starLevel) {
        this.starLevel = starLevel;
    }

    public String getCarryUser() {
        return carryUser;
    }

    public void setCarryUser(String carryUser) {
        this.carryUser = carryUser;
    }

    public String getHeadpic() {
        return headpic;
    }

    public void setHeadpic(String headpic) {
        this.headpic = headpic;
    }

    public String getCarryPhone() {
        return carryPhone;
    }

    public void setCarryPhone(String carryPhone) {
        this.carryPhone = carryPhone;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getSecondaryDriverPhone() {
        return secondaryDriverPhone;
    }

    public void setSecondaryDriverPhone(String secondaryDriverPhone) {
        this.secondaryDriverPhone = secondaryDriverPhone;
    }

    public String getFollowDriverPhone() {
        return followDriverPhone;
    }

    public void setFollowDriverPhone(String followDriverPhone) {
        this.followDriverPhone = followDriverPhone;
    }

    public Integer getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(Integer contractStatus) {
        this.contractStatus = contractStatus;
    }

    public Integer getTransInsuranceStatus() {
        return transInsuranceStatus;
    }

    public void setTransInsuranceStatus(Integer transInsuranceStatus) {
        this.transInsuranceStatus = transInsuranceStatus;
    }

    public String getDestLongitude() {
        return destLongitude;
    }

    public void setDestLongitude(String destLongitude) {
        this.destLongitude = destLongitude;
    }

    public String getDestLatitude() {
        return destLatitude;
    }

    public void setDestLatitude(String destLatitude) {
        this.destLatitude = destLatitude;
    }

    public List getProcess() {
        return process;
    }

    public void setProcess(List process) {
        this.process = process;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getInfoStatus() {
        return infoStatus;
    }

    public void setInfoStatus(Integer infoStatus) {
        this.infoStatus = infoStatus;
    }

    public Integer getVerifyFlag() {
        return verifyFlag;
    }

    public void setVerifyFlag(Integer verifyFlag) {
        this.verifyFlag = verifyFlag;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    public String getStartLongitude() {
        return startLongitude;
    }

    public void setStartLongitude(String startLongitude) {
        this.startLongitude = startLongitude;
    }

    public String getStartLatitude() {
        return startLatitude;
    }

    public void setStartLatitude(String startLatitude) {
        this.startLatitude = startLatitude;
    }

    public Integer getCarLocationScanOnOff() {
        return carLocationScanOnOff;
    }

    public void setCarLocationScanOnOff(Integer carLocationScanOnOff) {
        this.carLocationScanOnOff = carLocationScanOnOff;
    }

	public String getTailLicensePlate() {
		return tailLicensePlate;
	}

	public void setTailLicensePlate(String tailLicensePlate) {
		this.tailLicensePlate = tailLicensePlate;
	}

    public Integer getIsBuyInsurance() {
        return isBuyInsurance;
    }

    public void setIsBuyInsurance(Integer isBuyInsurance) {
        this.isBuyInsurance = isBuyInsurance;
    }

    public Long getInsuranceId() {
        return insuranceId;
    }

    public void setInsuranceId(Long insuranceId) {
        this.insuranceId = insuranceId;
    }

    public Integer getInsuranceStatus() {
        return insuranceStatus;
    }

    public void setInsuranceStatus(Integer insuranceStatus) {
        this.insuranceStatus = insuranceStatus;
    }
}
