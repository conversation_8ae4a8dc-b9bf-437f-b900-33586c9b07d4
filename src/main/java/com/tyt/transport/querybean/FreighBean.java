package com.tyt.transport.querybean;

import com.alibaba.fastjson.JSON;

public class FreighBean {
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String startCoordX;
	private String startCoordY;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private String destCoordX;
	private String destCoordY;
	private String provinceRoad;
	private String freightDistance;
	private String distance;
	private Short clientSign;
	private Short status;

	public String getStartProvinc() {
		return startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	public String getStartCity() {
		return startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	public String getStartArea() {
		return startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	public String getStartCoordX() {
		return startCoordX;
	}

	public void setStartCoordX(String startCoordX) {
		this.startCoordX = startCoordX;
	}

	public String getStartCoordY() {
		return startCoordY;
	}

	public void setStartCoordY(String startCoordY) {
		this.startCoordY = startCoordY;
	}

	public String getDestProvinc() {
		return destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	public String getDestCity() {
		return destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	public String getDestArea() {
		return destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	public String getDestCoordX() {
		return destCoordX;
	}

	public void setDestCoordX(String destCoordX) {
		this.destCoordX = destCoordX;
	}

	public String getDestCoordY() {
		return destCoordY;
	}

	public void setDestCoordY(String destCoordY) {
		this.destCoordY = destCoordY;
	}

	public String getProvinceRoad() {
		return provinceRoad;
	}

	public void setProvinceRoad(String provinceRoad) {
		this.provinceRoad = provinceRoad;
	}

	public String getFreightDistance() {
		return freightDistance;
	}

	public void setFreightDistance(String freightDistance) {
		this.freightDistance = freightDistance;
	}

	public String getDistance() {
		return distance;
	}

	public void setDistance(String distance) {
		this.distance = distance;
	}

	public Short getClientSign() {
		return clientSign;
	}

	public void setClientSign(Short clientSign) {
		this.clientSign = clientSign;
	}

	public Short getStatus() {
		return status;
	}

	public void setStatus(Short status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
