package com.tyt.transport.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.GoodsDetailOrderBean;
import com.tyt.infofee.bean.ListDataBean;
import com.tyt.model.TransportMt;
import com.tyt.model.TytTransportOrders;

/**
 * 
 * <AUTHOR>
 * 
 */
public interface TransportMtService extends BaseService<TransportMt, Long> {

	public ListDataBean getTransportMtBeanList(Long userId, int queryActionType, long queryID) throws Exception;

	public TransportMt getTransportMtBySrcMsgId(Long srcMsgId);

	/**
	 * 查询导入人工派单的运单
	 * @param userId 用户ID
	 * @return List<TransportMt>
	 */
	public List<TransportMt> getAgreeList(Long userId);

	void updatePayNum(String srcMsgId, int totalPaySucCount);

	void updateAgreeInfo(GoodsDetailOrderBean orderBean, String srcMsgId);
}
