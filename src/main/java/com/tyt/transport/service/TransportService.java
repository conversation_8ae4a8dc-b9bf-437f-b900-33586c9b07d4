package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.plat.entity.base.TytAppCallLog;
import com.tyt.plat.vo.ts.CallLogQuery;
import com.tyt.service.common.exception.TytException;
import com.tyt.transport.querybean.CallLogBean;
import com.tyt.transport.querybean.CallLogBeanNew;
import com.tyt.transport.querybean.CallLogQueryResultBean;
import com.tytrecommend.model.TytPreferenceNew;

import java.util.List;
import java.util.Map;

/**
 * User: Administrator Date: 13-11-10 Time: 下午4:25
 */
public interface TransportService extends BaseService<Transport, Long> {
    List<String> getTelList(String sql);

    List<Long> getQqList(String sql);

    /*
     * tyt_plat_transport_optimize20171123 货源信息优化
     * 删除代码： public void clearTransport(final int days);
     */
    List<Long> getInfoIdList(String clause);

    /**
     * tyt_plat_transport_optimize20171123 货源信息优化
     *
     * 删除代码：
     * public void updateStatus(Integer status, Long id) throws Exception;
     */

    /**
     * 重复信息验证
     *
     * @return
     * @throws Exception
     */
    boolean isExit(Transport transport) throws Exception;

    /**
     * @param start_coord_x      出发地坐标x，xy成对出现
     * @param start_coord_y      出发地坐标y
     * @param dest_coord_x       目的地坐标x xy成对出现
     * @param dest_coord_y       目的地坐标y
     * @param queryType          查询类型 0、下滑(如果是新数据少于30条，返回实际条数（手机端累积），如果大于30条，返回30条（手机端累积)
     *                           1、第一次查询100条数据 2、上滑请求历史数据大于30条
     * @param querySign          查询标识dataType=2时最小ID，dataType=0时传最大ID
     * @param isDelayed          是否延时 true延时
     * @param startDistanceValue 出发地查询范围 单位公里
     * @param destDistanceValue  目的地查询范围 单位公里
     * @param curUserId          当前登录用户ID
     * @param userType           用户类型，0-试用，1-付费，2-未激活
     * @param carLength          车辆长度
     * @param carType            车辆类型
     * @param specialRequired    特殊要求
     * @return List<TransportBean>
     */
    Map<Object, Object> queryTransport(long start_coord_x, long start_coord_y, long dest_coord_x, long dest_coord_y,
                                       int queryType, long querySign, boolean isDelayed, long startDistanceValue, long destDistanceValue,
                                       Long curUserId, Integer userType, String carLength, String carType, String specialRequired, String clientVersion);

    /**
     * 查询相似货源
     *
     * @param tsId         当前货源SRC_MSG_ID
     * @param startProvinc 出发地-省
     * @param startCity    出发地-市
     * @param destProvinc  目的地-省
     * @param destCity     目的地-市
     * @param matchItemId  标准货ID
     * @param queryType    查询类型 0、下滑(如果是新数据少于30条，返回实际条数（手机端累积），如果大于30条，返回30条（手机端累积)
     *                     1、第一次查询100条数据 2、上滑请求历史数据大于30条
     * @param querySign    查询标识queryType=2时最大ID，queryType=0时传最小ID
     * @return
     */
    Map<Object, Object> searchSimilarity(Long tsId, String startProvinc, String startCity, String destProvinc, String destCity, Long matchItemId, int queryType, long querySign, long userId, String clientVersion);

    Long getTodayMinTransId();

    long getSerachTotalCount(StringBuffer sb, Object[] params);

    /**
     * hashcode虑重
     *
     * @param hashCode
     * @return
     */
    boolean isExitHashCode(String hashCode) throws Exception;

    /**
     * 根据hash_code修改货物状态
     *
     * @param idList
     * @param user_id
     * @param status
     * @param display
     * @param infoStatus :信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成
     *                   5异常上报 客户端是否显示
     * @return
     */
    boolean updateStatusByIds(List <Long> idList, Integer status, String infoStatus, Integer display);

    /*
     * tyt_plat_transport_optimize20171123 货源信息优化
     * 删除代码：
     * public List<Long> getTodayIdListByHashCode(String hashCode, Long userId);
     */

    /**
     * 根据hashCode获取id集合
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    List<Long> getHistoryIdListBySrcMsgId(Long srcMsgId, Long userId);

//	/**
//	 * 获取id集合
//	 *
//	 * @param todayOrHistory
//	 *            null/0-历史;1-今天
//	 * @param hashCode
//	 * @param userId
//	 * @param status
//	 * @return
//	 */
//	public List<Long> getIdList(Integer todayOrHistory, String hashCode, Long userId, Integer status);

//	/**
//	 * 设置相同hashCode值是否显示（APP重发之前的都设置为display=0）
//	 *
//	 * @param tsId
//	 * @param display
//	 */
//	public void updateDisplayTypeById(Long tsId, Integer display);

    /**
     * 设置相同hashCode值是否显示（APP重发之前的都设置为display=0）
     *
     * @param tsId
     * @param display
     * @return
     */
    int updateDisplayTypeBysrcMsgId(Long idList, Integer display);

    /**
     * 获取我的发货条数
     *
     * @param userId
     * @param status
     * @return
     */
    long getMyPublishaNbr(Long userId, int status, String isInfoFee);

    /**
     * 获取我的发货条数(信息费版本新接口)
     * 发布中的货源不与信息费关联
     *
     * @param userId
     * @param status
     * @return
     */
    long getMyPublishNew(Long userId, int status, String isInfoFee);

    /**
     * 保存拨打电话信息
     *
     * @param userId         用户ID
     * @param callResultCode 拨打电话结果状态 0:无标注 1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易 6：已经拉走
     * @param callResultName name
     * @param goodId         打电话咨询的货物id
     * @param fromCar        车牌号
     * @param carId          车辆ID
     * @param reference      备注
     * @param markerOwnerCodes  标记货主信息 可多选 多选逗号分割
     */
    void addCall(String userId, String callResultCode, String callResultName, String goodId,
                 String fromCar, Long carId, String reference,String clientSign, String clientVersion,String markerOwnerCodes);

    /**
     * 获取用户拨打电话的信息
     *
     * @param callLogQuery
     * @return
     */
    CallLogQueryResultBean getCallLog(CallLogQuery callLogQuery);

    /**
     * 通过货物id获取发货人联系电话
     *
     * @param userId
     * @param goodId
     * @param clientVersion
     * @param isNeedPhone   客户端是否需要强制返回电话 1 需要 2 不需要
     * @return
     * @throws Exception
     */
    CallLogBean getPhoneByGoodId(String userId, String goodId, String clientVersion, String clientSign, String isNeedPhone) throws Exception;

    CallLogBeanNew getPhoneByGoodIdNew(String userId, String goodId, String clientVersion, String clientSign, String moduleType, String isNeedPhone) throws Exception;

    /**
     * 支付信息费的时候修改tyt_transport
     *
     * @param infoFeeStatus 信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成
     *                      5异常上报
     * @param goodsId       :货物ID
     * @return
     * @throws Exception
     */
    int saveChangeInfoFeeStatus(String infoFeeStatus, Long goodsId) throws Exception;

    /**
     * 修改货源的srcMsgId
     *
     * @param goodsId
     * @param srcMsgId
     * @return
     * @throws Exception
     */
    int updateSrcMsgId(Long goodsId, Long srcMsgId) throws Exception;

    /**
     * 获取某一条信息今天最大的重发次数
     *
     * @param srcMsgId
     * @return
     * @throws Exception
     */
    int getMaxResendCounts(Long srcMsgId) throws Exception;

    int getCurrentSendCount(Long userId);

    /**
     * @param goodIdsList 要查询的获取id集合
     * @return List<Transport>
     */
    List<Transport> queryListByGoodIds(List <Long> goodIdsList);

    long getMyPushTytNbr(Long userId, int status, String isInfoFee);

    /*
     * tyt_plat_transport_optimize20171123 货源信息优化
     * 删除代码：
     */
    long getMaxIdbytransId(Long transId);


    //获取最新的一条货物信息
    Transport getLastBySrcMygId(Long goodsId);

    List<Long> getTodayIdListBySrcMsgId(Long srcMsgId, Long userId,
                                        Integer status);

    /**
     * @param start_coord_x      出发地坐标x，xy成对出现
     * @param start_coord_y      出发地坐标y
     * @param dest_coord_x       目的地坐标x xy成对出现
     * @param dest_coord_y       目的地坐标y
     * @param queryType          查询类型 0、下滑(如果是新数据少于30条，返回实际条数（手机端累积），如果大于30条，返回30条（手机端累积)
     *                           1、第一次查询100条数据 2、上滑请求历史数据大于30条
     * @param querySign          查询标识dataType=2时最小ID，dataType=0时传最大ID
     * @param startDistanceValue 出发地查询范围 单位公里
     * @param totalSize总查询条数
     * @return List<TransportBean>
     */
    Map<Object, Object> queryRecommendTransport(int queryType, long querySign, int startDistanceValue, TytPreferenceNew preferenceNew);

    /**
     * 记录有好货删除日志
     *
     * @param userId
     * @param srcMsgId
     * @param clientSign
     */
    void deleteLog(long userId, long srcMsgId, int clientSign);

    /**
     * 设置货源失效，不显示不可用
     *
     * @param srcMsgId
     * @return
     */
    int disableTransport(long srcMsgId);

    /**
     * 设置货源不显示
     *
     * @param srcMsgId
     * @return
     */
    int noDisplayTransport(long srcMsgId);

    String modifyQualityGoodSimilarCode(Transport tb, String similarityConnect) throws TytException;

    /**
     * 生成货源相似code
     * @param tb
     * @return
     */
    String genSimilarityCode(Transport tb);

    /**
     * 更新相似货源分组的code及首发基本信息
     *
     * @param tb
     */
    void updateSimilarityCode(Transport tb);

    /**
     * 重货需要减去的id
     * @return
     */
    Long getBottomIdSubtract();

    /**
     * 更新相似货源分组的code及首发基本信息
     *
     * @param tb transport表
     * @param tbm transport_main表
     */
    void updateSimilarityCode(Transport tb, TransportMain tbm);

    TytAppCallLog callDetail(String userId, String goodId, String clientSign);

    /**
     * 校验个人是否存在相似货源
     * @param transport
     * @param srcMsgId
     * @return true 不存在；false 存在
     */
    boolean checkPersonalSimilarity(Transport transport, Long srcMsgId);

    /**
     * 校验当天是否存在相似货源
     * @param transport
     * @return true 不存在；false 存在
     */
    boolean checkTransportSimilarity(Transport transport);

    /**
     * 校验是否存在其他相似货源(true存在，false不存在)
     * @param srcMsgId
     * @param similarityCode
     * @return
     */
    boolean checkOtherSimilarityExist(Long srcMsgId, String similarityCode);

}
