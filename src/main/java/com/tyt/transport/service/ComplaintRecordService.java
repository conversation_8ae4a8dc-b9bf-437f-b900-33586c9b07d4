package com.tyt.transport.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.transport.bean.ComplaintRecordBean;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/4/16 16:38
 */
public interface ComplaintRecordService {
    /**
     * 添加投诉记录
     * @param recordBean
     */
    ResultMsgBean addComplaintRecord(ComplaintRecordBean recordBean) throws Exception;

    /**
     * 根据货源id获取投诉记录
     * @param msgId
     * @return
     */
    ComplaintRecordBean getComplaintRecordByMsgId(Long msgId,Long userId);
}
