package com.tyt.transport.service;

import com.tyt.transport.enums.PubTypeEnum;

import java.math.BigDecimal;

/**
 * 货源发布记录
 *
 * <AUTHOR>
 * @since 2025-02-18 10:18
 */
public interface TransportPublishLogService {

    void recordPublishLog(Long userId, Long srcMsgId, PubTypeEnum pubTypeEnum, Integer requestSource);

    void changePriceLog(Long srcMsgId, String price, Integer publishType, Integer operationType);

}
