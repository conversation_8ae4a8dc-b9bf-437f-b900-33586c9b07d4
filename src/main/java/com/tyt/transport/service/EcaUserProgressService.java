package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.EcaUserProgress;

import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * 
 */
public interface EcaUserProgressService extends BaseService<EcaUserProgress, Long> {
    /**
     * 记录用户的签署进度
     * @param contractId 合同ID
     * @param srcMsgId 货物ID
     * @param type 类型
     */
    void insertEcaUserProgress(Long contractId, Long srcMsgId, Integer type);

    List<EcaUserProgress> getEcaUserProgressListById(Long contractId);

	void insertEcaUserProgressWithDate(Long contractId, Long srcMsgId,
			Integer type, Date cTime);
}
