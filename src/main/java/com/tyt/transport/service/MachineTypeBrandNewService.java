package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytMachineTypeBrandNew;
import com.tyt.transport.vo.MachineTypeVo;

import java.util.List;

/**
 * 机种类型服务层接口
 *
 * <AUTHOR>
 * @date 2017-4-14下午5:44:50
 * @description
 */
public interface MachineTypeBrandNewService extends BaseService<TytMachineTypeBrandNew, Integer> {

    /**
     * 根据搜索关键词匹配机器类型
     *
     * @param keyword
     * @return
     */
    List<TytMachineTypeBrandNew> queryMachineTypeGroupByKeyword(String keyword, String userId);

    /**
     * 根据搜索关键词匹配机器类型
     *
     * @param keyword
     * @return
     */
    List<TytMachineTypeBrandNew> queryMachineTypeByKeyword(String keyword, String userId);

    /**
     * 通过matchItemId查询标准化货源
     *
     * @param matchItemId
     * @return
     */
    TytMachineTypeBrandNew getByMatchItemId(Integer matchItemId);

    /**
     * 通过showName查询标准化货源
     *
     * @param showName
     * @return
     */
    TytMachineTypeBrandNew getByShowName(String showName);

    /**
     * 查询标准货名
     *
     * @param keyword
     * @param brand
     * @param topType
     * @param userId
     * @return
     */
    MachineTypeVo searchMatchesName(String keyword, String brand, String topType, String userId) throws Exception;

    /**
     * 获取货物型号
     * @param brand
     * @return
     */
    List<String> getTopType(String brand);
}
