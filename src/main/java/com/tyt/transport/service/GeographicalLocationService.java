package com.tyt.transport.service;

import com.tyt.model.City;
import com.tyt.base.service.BaseService;

import java.util.List;

public interface GeographicalLocationService extends BaseService<City, String> {
    /**
     * 根据城市或者省份名称查询对应的x轴和y轴
     * @param level
     * @param cityName
     * @param type 1为查询老数据 2为查询高德地图名称
     * @return
     */
    List<City> getCityByCityName(String level,String cityName,String type);

    /**
     * 插入x轴和y轴查询失败记录
     * @param primitiveType
     * @param subtype
     * @param remake
     */
    void addAccidentRecord(String primitiveType,String subtype,String remake,Long userId);

    /**
     * 根据x轴和y轴找到对应的省市区
     * @param px
     * @param py
     * @return
     */
    City getCityByPxAndPy(String px, String py);

    /**
     * 根据省市区查找到对应的城市
     * @param provinc
     * @param city
     * @param area
     * @return
     */
    List<City> getCityByCityNameAndAreaName(String provinc,String city,String area);

}
