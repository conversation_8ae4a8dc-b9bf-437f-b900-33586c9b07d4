package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.UserIdentityCertLog;

public interface UserIdentityCertLogService extends BaseService<UserIdentityCertLog, Long>{
	/**
	 * 增加一条安证通实名认证日志
	 * @param userIdentityCertLog log object
	 */
	void addIdentityCertLog(UserIdentityCertLog userIdentityCertLog);

	/**
	 * 安证通认证失败次数，是否继续进行实名认证
	 * @param userName 真实姓名
	 * @param idCard 身份证号
	 * @param userId 用户Id
	 * @return 安证通验证失败次数超出系统限制 或 已存在失败信息返回true，不存在返回false
	 */
	Boolean isExistAuthFailTimes(String userName, String idCard, Long userId);

	/**
	 * 安证通认证是否存在失败信息
	 * @param userName 真实姓名
	 * @param idCard 身份证号
	 * @return 存在失败信息返回true，不存在返回false
	 */
	Boolean isExistAuthFail(String userName, String idCard);
}
