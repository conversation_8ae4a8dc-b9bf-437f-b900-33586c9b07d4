package com.tyt.transport.service;

import java.util.List;
import com.tyt.base.service.BaseService;
import com.tyt.model.TytKeywordMatches;
import com.tyt.model.TytMachineType;

/**
 * 关键词搜索服务层接口
 * 
 * <AUTHOR>
 * @date 2017-4-14下午5:44:50
 * @description
 */
public interface KeywordMatchesService extends BaseService<TytKeywordMatches, Long> {

	/**
	 * 根据搜索关键词匹配机器类型
	 * 
	 * @param keyword
	 * @param userId 
	 * @return
	 */
	List<TytMachineType> queryMachineTypeByKeyword(String keyword, String userId);
}
