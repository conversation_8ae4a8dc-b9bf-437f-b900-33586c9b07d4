package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.EcaContract;
import com.tyt.model.ResultMsgBean;

/**
 * <AUTHOR>
 */
public interface EcaContractService extends BaseService<EcaContract, Long> {
    public EcaContract getEcaContractBySrcMsgId(Long SrcMsgId);

    public EcaContract getContractByUserId(Long shipperUserId);

    public void updateCancelContract(EcaContract ecaContract, String userId, String userName) throws Exception;

    /**
     * 同意签署合同
     *
     * @param contractId
     * @param userId
     * @param rm
     */
    void userAgreeAffix(Long contractId, Long userId, ResultMsgBean rm);

    /**
     * 签署合同
     *
     * @param contractId 合同ID
     * @param userId 用户ID
     * @param identifyingCode 验证码
     * @param rm
     */
    EcaContract userDoAffix(Long contractId, Long userId, String identifyingCode, ResultMsgBean rm);

	void updateShipperUserId(Long shipperUserId, Long contractId);

}
