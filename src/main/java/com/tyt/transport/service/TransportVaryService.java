package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TransportVary;
import com.tyt.transport.querybean.TransportVaryBean;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public interface TransportVaryService extends BaseService<TransportVary,Long> {
	/**
	 * 根据Id查询大于Id的数据
	 * @param maxId
	 * @return
	 */
	public List<TransportVaryBean>  getTransportVaryForDate(long maxId) ;
	/**
	 * 批量添加
	 * @param idList
	 * @param status
	 * @return
	 * @throws Exception 
	 */
	public void addVarys(List<Long> idList, Integer status) throws Exception;

}
