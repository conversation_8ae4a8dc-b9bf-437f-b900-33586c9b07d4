package com.tyt.transport.service;

import com.tyt.model.City;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TytTransportBackend;
import com.tyt.plat.enums.SourceGroupCodeEnum;
import com.tyt.plat.vo.remote.CarryPriceReq;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.transport.querybean.TransportCheckSimilarityBean;
import com.tyt.transport.querybean.TransportPublishBean;
import com.tyt.user.bean.CarSaveBean;

public interface InterestsGrantRecordsService {


}
