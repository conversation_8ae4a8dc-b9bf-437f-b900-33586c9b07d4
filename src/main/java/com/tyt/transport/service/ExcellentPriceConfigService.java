package com.tyt.transport.service;

import com.tyt.model.TransportMain;
import com.tyt.plat.entity.base.TytExcellentPriceConfig;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.transport.querybean.TransportCarryBean;

/**
 * <AUTHOR>
 * @since 2024/8/10 13:25
 */
public interface ExcellentPriceConfigService {
    /**
     * 优车定价货源获取建议价
     *
     * @param transportCarryBean
     */
    CarryPriceVo getSuggestPrice(TransportCarryBean transportCarryBean);

    /**
     * 优车货源获取阻断价
     *
     * @param transportCarryBean
     * @return
     */
    CarryPriceVo getThPrice(TransportCarryBean transportCarryBean);

    /**
     * 优车货源获取阻断价
     *
     * @param transportMain
     * @return
     */
    CarryPriceVo getThPrice(TransportMain transportMain);

    /**
     * 获取匹配到的优车定价2.0建议价配置。先用参数货类匹配，如果参数货类匹配不上，则用默认货类匹配，如果默认货类还是匹配不上则返回null
     * @param transportCarryBean transportCarryBean
     * @return transportCarryBean
     */
    TytExcellentPriceConfig getConfig(TransportCarryBean transportCarryBean);
}
