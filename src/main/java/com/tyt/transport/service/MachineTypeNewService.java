package com.tyt.transport.service;

import java.util.List;
import com.tyt.base.service.BaseService;
import com.tyt.model.TytMachineTypeNew;
import com.tyt.transport.querybean.TytMachineTypeBean;

/**
 * 机种类型服务层接口
 * 
 * <AUTHOR>
 * @date 2017-4-14下午5:44:50
 * @description
 */
public interface MachineTypeNewService extends BaseService<TytMachineTypeNew, Long> {
	/**
	 * 通过通用匹配方式获取机种类型
	 * 
	 * @param keyword
	 * @return
	 */
	List<TytMachineTypeNew> queryMachineTypeByGeneralMatch(int matchesTypeCacheSize, String keyword);

	public TytMachineTypeNew getTytMachineTypeForId(Long id);

	/**
	 * 通过通用匹配方式获取机种类型 ID >10000取新表
	 * @param id
	 * @return
	 */
	public TytMachineTypeBean getTytMachineTypeForIdAll(Long id);
}
