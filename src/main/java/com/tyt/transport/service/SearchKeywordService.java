package com.tyt.transport.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytSearchKeyword;

/**
 * 
 * <AUTHOR>
 * @date 2017-8-7下午2:46:19
 * @description
 */
public interface SearchKeywordService extends BaseService<TytSearchKeyword, Long> {
	TytSearchKeyword getByKeyword(String keyword);

	List<TytSearchKeyword> findListByKeywordPriority(int searchKeywordPriority);
}
