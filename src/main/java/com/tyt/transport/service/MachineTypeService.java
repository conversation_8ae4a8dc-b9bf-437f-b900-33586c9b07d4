package com.tyt.transport.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytMachineType;
import com.tyt.transport.querybean.TytMachineTypeBean;

/**
 * 机种类型服务层接口
 * 
 * <AUTHOR>
 * @date 2017-4-14下午5:44:50
 * @description
 */
public interface MachineTypeService extends BaseService<TytMachineType, Long> {

	/**
	 * 通过通用匹配方式获取机种类型
	 * 
	 * @param keyword
	 * @return
	 */
	List<TytMachineType> queryMachineTypeByGeneralMatch(int matchesTypeCacheSize, String keyword);

	/**
	 * 获得货物类型模版
	 * @param id
	 * @return
	 */
	public TytMachineType getTytMachineTypeForId(Long id);

	List<TytMachineTypeBean> getListByBrandType(String brandType);
}
