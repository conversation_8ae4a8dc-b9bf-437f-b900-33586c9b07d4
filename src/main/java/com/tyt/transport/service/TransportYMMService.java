package com.tyt.transport.service;

import com.tyt.transport.querybean.DispatchParamBean;
import com.tyt.transport.querybean.TransportYmmGoodsBean;
import com.tyt.transport.querybean.TransportYmmListReqBean;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 运满满业务处理接口
 * @date 2023-08-17-09-23-07
 */
public interface TransportYMMService {

    DispatchParamBean revokeBeanHandle(DispatchParamBean dispatchParamBean);

    void expireYMMCargoMerge(Long srcMsgId);

    void pushMbCargoExpireMessage(Long srcMsgId);

    /**
     * 保存运满满货源关系表
     *
     * @param srcMsgId
     * @param cargoId
     * @param cargoVersion
     */
    void saveOrUpdateMbMerge(Long srcMsgId, Long cargoId, Integer cargoVersion);

    /**
     * 获取运满满待接单列表
     * @param req
     * @return
     */
    List<TransportYmmGoodsBean> getTransportYmmList(TransportYmmListReqBean req);
}
