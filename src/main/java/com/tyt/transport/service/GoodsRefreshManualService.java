package com.tyt.transport.service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.model.Transport;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/08/28 11:07
 */
public interface GoodsRefreshManualService {

    /**
     * 返回最近1周的货源动态
     */
    List<String> getGoodsDynamic(Long userId, String clientSign, Long srcMsgId);

    /**
     * 增加货源刷新次数
     *
     * @param transport         新货源
     * @param originalTransport 历史货源
     */
    void addGoodsRefresh(Transport transport, BaseParameter baseParameter, Transport originalTransport);
}
