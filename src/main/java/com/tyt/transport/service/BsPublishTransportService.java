package com.tyt.transport.service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.model.*;
import com.tyt.plat.client.transport.dto.CheckIsNeedFreeTecServiceFeeVO;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.plat.enums.SourceGroupCodeEnum;
import com.tyt.plat.vo.other.GoodsSmallDO;
import com.tyt.plat.vo.remote.CarryPriceReq;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.plat.vo.ts.MeetCommissionRulesResult;
import com.tyt.plat.vo.ts.SaveDirectReq;
import com.tyt.plat.vo.ts.TransportUpdateDataReq;
import com.tyt.transport.bean.CommissionTypeBean;
import com.tyt.transport.bean.DrawCommissionReq;
import com.tyt.transport.querybean.*;
import com.tyt.transport.vo.TytTecServiceFeeConfigToComputResult;
import com.tyt.user.bean.CarSaveBean;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

public interface BsPublishTransportService {

    /**
     * 请求大数据运价计算
     * @param carryPriceReq
     * @return
     */
    CarryPriceVo reqCarryPrice(CarryPriceReq carryPriceReq);

    /**
     * 设置标准货源相关字段
     * @param transport
     */
    void setTransportStandardInfo(Transport transport);

    /**
     * 发布货物
     * @param publishBean
     * @return
     */
    ResultMsgBean savePublishTransport(TransportPublishBean publishBean)throws Exception;

    boolean checkSimilarExist(Long srcMsgId);

    ResultMsgBean getHelpBtnPopupConfig(Long userId);

    ResultMsgBean reportPopup(Long userId);

    ResultMsgBean getDepositAndRefundType(Long userId, Integer invoiceTrnaposrt);

    ResultMsgBean intelligentDeposit(IntelligentDepositBean bean) throws Exception;
    ResultMsgBean depositRecord(IntelligentDepositBean bean);

    /**
     * 生成transport hash code
     * @param transport
     * @return
     */
    String getNewHashCode(Transport transport);

    /**
     * 当前用户是否展示专车发货入口
     * @param userId
     * @return
     */
    ResultMsgBean isShowSpecial(Long userId);

    /**
     * 当前用户是否展示专车发货入口
     * 6480 专车二期
     * @param userId
     * @return
     */
    ResultMsgBean isShowSpecialV2(Long userId);

    /**
     * 6480新增
     * 是否展示签约合作商：调度账号展示，非调度账号隐藏
     * @param queryBean
     * @return
     */
    ResultMsgBean isShowCargoOwner(CargoOwnerQueryBean queryBean);

    /**
     * 6480新增
     * 是否展示司机驾驶此类货物
     * ①根据【运费所属企业】【出发地、目的地】【货物吨位】匹配运费计算规则
     * ②运费计算规则中配置了【驾驶货物费】，并且发货的货物类型名称（good_type_name）在驾驶能力枚举列表中
     * @param queryBean
     * @return
     */
    ResultMsgBean isShowDriverDriving(DriverDrivingQueryBean queryBean);

    /**
     * 专车运费测算
     * @param calculatePriceBean
     * @return
     */
    ResultMsgBean calculatePrice(CalculatePriceBean calculatePriceBean);

    /**
     * 6480 专车二期专线，运费测算
     * @param calculatePriceBean
     * @return
     */
    ResultMsgBean calculatePriceV2(CalculatePriceBean calculatePriceBean);

    /**
     * 专车发货自动派单
     * @param assignOrderBean
     * @return
     */
    ResultMsgBean autoAssignOrderForSpecialCar(AutoAssignOrderBean assignOrderBean) throws Exception;

    /**
     * 根据货物ID获取货物信息:先从tyt_transport(数据量少)表找数据，再从tyt_transport_main(所有数据)中找,过期信息可能需要在tyt_transport_main表中找原数据
     *
     * @param goodsId
     * @return
     * @throws Exception
     */
    Transport getByGoodsId(Long goodsId);

    /**
     * 重新发布货源；
     * 如果当天发布中货源，需要进行撤销；
     * @param userId
     * @param tsId
     * @param publishType 货源类型（电议1，一口价2） 一口价需求新增参数，可传 null
     * @return
     */
    ResultMsgBean saveRePublish(long userId,long tsId,String clientVersion,String clientSign)throws Exception;

    /**
     * 转电议
     * @param userId
     * @param tsId
     * @param publishType
     * @param clientVersion
     * @param clientSign
     * @param price
     * @return
     * @throws Exception
     */
    ResultMsgBean updatePublishType(long userId,long tsId,Integer publishType,String clientVersion,String clientSign,Integer isBackendTransport, String price) throws Exception;
    /**
     * 运费加价
     * @param userId 用户id
     * @param tsId tyt_transport_main id
     * @param addPrice 加价后价格
     * @param clientVersion
     * @param clientSign
     * @return
     * @throws Exception
     */
    ResultMsgBean freightAddMoneyNum(long userId,long tsId,String addPrice,String clientVersion,String clientSign,
                                     Integer isBackendTransport, Integer forceUp, Integer requestSource, Integer operationType) throws Exception;

    boolean checkExcellentGoodsForPermission(Long userId);

    /**
     * 验证用户是不是发货受限制
     * @param userId
     * @return
     */

    ResultMsgBean validationLimitPublish(Long userId) throws Exception ;

    /**
     * 根据二级城市名称查询对应的上级城市的Px和Py
     * @param province
     * @param cityName
     * @param areaName
     * @param status 省市区和xy都有的话为1 只有省市区的话为2
     * @return
     */
    ResultMsgBean pxAndPyByCityName(String province,String cityName,String areaName,String status,Long userId);

    /**
     * 根据x轴和y轴找到对应的省市区
     * @param px
     * @param py
     * @return
     */
    City getCityByPxAndPy(String px, String py);

    String checkTransportWord(String text, SourceGroupCodeEnum... codeEnumArray);

    /**
     * 屏蔽词校验
     * @param type
     * @param remark
     * @return
     */
    ResultMsgBean getScreeningWordCheck(String type,String remark);

    /**
     * 补充新版标准化货物信息的参考值
     *
     * @param transport
     */
    boolean addReferTransportNewInfo(Transport transport);

    /**
     * 补充车辆信息
     * @param carSaveBean
     * @param orderId
     * @param type
     * @return
     */
    ResultMsgBean supplementCarInfo(CarSaveBean carSaveBean, Long orderId, Integer type);

    /**
     * 补充车辆信息（manage 个人货主调度所使用）
     * @param carSaveBean
     * @param orderId
     * @param type
     * @return
     */
    ResultMsgBean supplementCarInfoForManage(CarSaveBean carSaveBean, Long orderId, Integer type);

    /**
     * 补充车辆信息
     * @param carSaveBean
     * @param tsId
     * @return
     */
    ResultMsgBean supplementCarInfoForTransport(CarSaveBean carSaveBean, Long tsId);

    ResultMsgBean checkTransportSimilarity(TransportCheckSimilarityBean transportCheckSimilarityBean);

    void sendMessage2MQ(TytTransportBackend tytTransportBackend, Long userId);

    ResultMsgBean checkSegmentedPayments(BigDecimal prepaidPrice,
                                         BigDecimal collectedPrice,
                                         BigDecimal receiptPrice,
                                         String price,
                                         Long userId, boolean direct);

    boolean checkExcellentGoods(Integer excellentGoods, Long userId, Integer youchePublishType);

    boolean checkYouchePublishRemainingCount(Long userId, Integer youchePublishType) throws Exception;

    ResultMsgBean freightAddMoneyNum1(long userId, long tsId, String addPriceReq) throws Exception;

    /**
     * 修改技术服务费
     * @param tsId 货源Id
     * @param tecServiceFee 技术服务费
     * @return ResultMsgBean
     */
    ResultMsgBean changeTecServiceFee(long tsId, BigDecimal tecServiceFee) throws Exception;

    /**
     * 校验货源是否符合自动转优车定价的资格
     * @param priceTransportBean
     * @return
     */
    boolean checkTransportIsGoodCarPriceTransport(CheckGoodCarPriceTransportBean priceTransportBean);

    /**
     * 是否展示优车定价卡片（符合优车定价条件）
     * @param parameter
     * @param transportCarryBean
     * @return
     */
    ResultMsgBean isShowGoodCarPriceTransportTab(BaseParameter parameter, TransportCarryBean transportCarryBean);

    boolean checkParameter(TransportCarryBean transportCarryBean);

    /**
     * Computes the technology service fee based on transport data.
     *
     * @param tytTransport      The transport data.
     * @param oldTransportMain  The old transport main data.
     * @return A list of TytTecServiceFeeConfigToComputResult objects.
     */
    TytTecServiceFeeConfigToComputResult computeTecServiceFeeBtTransportData(Transport tytTransport, TransportMain oldTransportMain, MeetCommissionRulesResult meetCommissionRules, boolean isGoodCarPriceTransport);

    boolean checkIsNeedFreeTecServiceFeeByTransport(Transport tytTransport, boolean isGoodCarPriceTransport, TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult);

    boolean checkIsNeedFreeTecServiceFeeByTime(TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult, Long firstPublishTimeMinute);
    // 返回手动设置技术服务费的抽佣结果
    TytTecServiceFeeConfigToComputResult getManualTecServiceFeeResult(Transport tytTransport, BigDecimal commissionScore);
    /**
     * Computes the technology service fee based on transport data.
     *
     * @param tytTransport The transport data.
     * @param oldTransportMain The old transport main data.
     * @return A list of TytTecServiceFeeConfigToComputResult objects.
     */
    TytTecServiceFeeConfigToComputResult makeTecServiceFeeData(Transport tytTransport, TransportMain oldTransportMain, MeetCommissionRulesResult meetCommissionRules, boolean isGoodCarPriceTransport, TytTransportMainExtend mainExtend);

    String makePriceByGoodCarPriceTransportCarryPrice(Transport tytTransport);
    /**
     * Computes and saves the commission transport technology service fee data.
     *
     * @param srcMsgId The source message ID.
     * @param tytTecServiceFeeConfigToComputResult A list of TytTecServiceFeeConfigToComputResult objects containing the computed technology service fee data.
     */
    void saveCommissionTransportTecServiceFeeData(Long srcMsgId, TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult);

    /**
     * 根据车主用户ID和源消息ID获取技术服务费用
     *
     * @param carUserId 车主用户ID
     * @param srcMsgId  源消息ID
     * @return 包含技术服务费用信息的ResultMsgBean对象
     */
    ResultMsgBean getTecServiceFeeByCarUserAndSrcMsgId(Long carUserId, Long srcMsgId);

    CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByCarUser(Long userId, Long srcMsgId);

    CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByTransport(Long transportUserId, String startCity, boolean isGoodCarPriceTransport);

    List<GoodsSmallDO> getUserBuyList(Long userId, Integer userType);

    /**
     * 校验货源是否抽佣
     *
     * @param req
     * @return
     */
    CommissionTypeBean checkCommission(DrawCommissionReq req,Transport transport);

    /**
     * This method is used to edit the transport price and recompute the technology service fee and invoice
     *
     * @param srcMsgId          the ID of the source message
     * @param carQuotedPrice    the new quoted price for the transport
     */
    Boolean transportAgreeQuotedPriceEditTransportPriceAndReComputTecServiceFeeAndInvoice(BaseParameter parameter, Long srcMsgId, Integer carQuotedPrice, Long transportQuotedPriceId, Integer type);

    /**
     * 修改货源的长宽高等信息
     */
    ResultMsgBean updateGoodsInfo(TransportUpdateDataReq updateDataReq) throws Exception;

    ResultMsgBean updateGoodsInfoAdapter(TransportUpdateDataReq updateDataReq) throws Exception;

    TransportCarryBean buildCarryBeanByTransport(Transport tytTransport);

    void buildAddress(TransportCarryBean transportCarryBean);

    ResultMsgBean adapterTest();

    ResultMsgBean freightAddMoneyNumAdapter(Long userId, Long tsId, String price, String clientVersion, String clientSign, Integer isBackendTransport, Integer forceUp, Integer requestSource, int i);

    ResultMsgBean saveDirectAdapter(SaveDirectReq saveDirectReq);

}
