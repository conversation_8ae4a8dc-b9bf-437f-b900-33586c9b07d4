package com.tyt.transport.service;

import java.lang.reflect.InvocationTargetException;

import com.tyt.base.service.BaseService;
import com.tyt.model.FreightRecord;
import com.tyt.transport.querybean.FreighBean;

/**
 * 
 * <AUTHOR>
 * @date 2017年8月29日上午11:55:26
 * @description
 */
public interface FreightRecordService extends BaseService<FreightRecord, Long> {

	void saveFreight(FreighBean freightBean) throws IllegalAccessException, InvocationTargetException;

	FreightRecord queryByKey(String key);
}
