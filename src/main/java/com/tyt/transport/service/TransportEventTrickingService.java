package com.tyt.transport.service;

import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.plat.vo.ts.SaveDirectReq;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.transport.querybean.TransportPublishBean;

/**
 * 货源数据埋点类
 *
 * <AUTHOR>
 * @since 2024/05/16 16:53
 */
public interface TransportEventTrickingService {


    /**
     * 发布货源/编辑货源埋点
     */
    void publishTransport(Transport transport, TransportLabelJson labelJson, TransportPublishBean publishBean);

    /**
     * 直接发布埋点
     */
    void directPublish(Transport transport, TransportLabelJson labelJson, SaveDirectReq saveDirectReq);

    /**
     * 撤销发布埋点
     */
    void cancelPublish(TransportMain transportMain);
}
