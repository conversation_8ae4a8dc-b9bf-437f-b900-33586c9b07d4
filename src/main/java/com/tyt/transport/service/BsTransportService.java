package com.tyt.transport.service;

import com.tyt.model.Transport;
import com.tyt.transport.querybean.*;

import java.util.List;
import java.util.Map;

public interface BsTransportService {
    /**
     * 范围倒短
     * @param bean
     * @return
     */
    BoResultBean<TransportBean> getScopeSearchTransportList(ScopeSearchQueryBean bean);



    /**
     * 省内倒短
     * @param bean
     * @return
     */
    BoResultBean<TransportBean> getProvicSearchTransportList(ProvincSearchQueryBean bean);

    /**
     * 找货大厅
     * @param bean
     * @return
     */
    BoResultBean<TransportBean> getTransportList(TransportSearchBean bean);

    /**
     * List<Transport> 转为List<TransportBean>t，设置昵称、及加密；
     * @param sourceList List<Transport> 源数据
     * @param userId  用户ID
     */
    List<TransportBean> filterTransportList(List<Transport> sourceList, Long userId, String clientVersion);

    /**
     * 目的地货源
     * @param bean
     * @return
     */
    BoResultBean<TransportBean> getDestTransportList(DestTransportSearchBean bean);

    /**
     * 相似货源列表
     * @param bean
     * @return
     */
    BoSimilarResultBean getSimilarTransportList(SimilarTransportSearchBean bean);
}
