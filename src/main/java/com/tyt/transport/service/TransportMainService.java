package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.WechatShareTransportBean;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.DwsNewIdentityTwoData;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.transport.querybean.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public interface TransportMainService extends BaseService<TransportMain, Long> {
	/**
	 * 字表货物内容插入主表
	 *
	 * @param id
	 * @throws Exception
	 */
	public void addTransportMain(Long id) throws Exception;

	/**
	 * 查询历史
	 *
	 * @param status
	 *            状态 0，1，4，5
	 * @param queryType
	 * @param querySign
	 * @return List<TransportBean>
	 */
	public List<TransportBean> queryTransportHistory(long user_id, int status, int queryType, long querySign);

//	/**
//	 * 从数据库获取某月我的发货条数
//	 *
//	 * @param userId
//	 * @return
//	 * @throws Exception
//	 */
//	public long myGoodsLimitFromDB(Long userId) throws Exception;
//
//	/**
//	 * 获取某月我的发货条数
//	 *
//	 * @param userId
//	 * @return
//	 * @throws Exception
//	 */
//	public boolean myGoodsLimit(Long userId) throws Exception;

	/**
	 * 根据hashCode获取id集合(今天status=1/4)
	 *
	 * @param srcMsgId
	 * @return
	 */
	public List<Long> getTodayIdListBySrcMsgId(Long srcMsgId, Long userId,Integer newStatus);

	/**
	 * 根据 srcMsgId 获取id集合(历史display=1)
	 *
	 * @param srcMsgId
	 * @return
	 */
	public List<Long> getHistoryIdListBySrcMsgId(Long srcMsgId, Long userId);

	/**
	 * 根据hash_code修改货物状态
	 * @param display是否显示
	 * @param infoStatus:信息费运单状态：0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
	 * @param transport
	 * @return
	 */
	public boolean updateStatusByIds(List<Long> idList, Integer status, String infoStatus, Integer display);

	/**
	 * 设置相同hashCode值是否显示（APP重发之前的都设置为display=0）
	 *
	 * @param tsId
	 * @param display
	 * @return
	 */
	public int updateDisplayTypeByIds(List<Long> idList, Integer display);

//	/**
//	 * 获取最大的重发次数
//	 *
//	 * @param hashCode
//	 * @param userId
//	 * @return
//	 */
//	public Integer getMaxResendCountsByHashCode(String hashCode, Long userId);

	/**
	 * 获取最新的状态值，解决APP自动重发可能造成状态不一致
	 *
	 * @param hashCode
	 * @param userId
	 * @return
	 */
	public Integer getLastStatusByHashCode(String hashCode, Long userId);

	/**
	 * 获取用户的通话标注信息
	 *
	 * @param userId
	 * @param tsId
	 * @return
	 */
	public CallLogBean getCallLog(Long userId, Long tsId);
	public TransportMain getTransportMainForId(Long tsId);
    /**
     * 获取信息费我的货源列表
     * @param queryMenuType:1发布中 2已撤销 3已过期
     * @param userId:用户ID
     * @param queryActionType:1下拉，2上滑；（首次queryActionType=1）
     * @param queryID
     * @return
     */
	public TransportCollectBean getInfoFeeMyPublish(Integer queryMenuType, Long userId,
													Integer queryActionType, Long queryID,String clientSign, Integer quotedPriceQueryType, Integer callQueryType);
	/**
	 * 支付信息费的时候修改tyt_transport_main
	 * @param infoFeeStatus 信息费运单状态：0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
	 * @param srcMsgId 货物源ID
	 * @return
	 * @throws Exception
	 */
	public int saveChangeInfoFeeStatus(String infoFeeStatus,Long srcMsgId)throws Exception;

	public int updateSrcMsgId(Long goodsId, Long srcMsgId)throws Exception;
	/**
	 * 获取货物详情
	 * @param tsId
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public TransportDetail getTransportDetail(Long tsId,Long userId)throws Exception;
	/**
	 * 根据srcMsgId获取货物信息
	 * @param srcMsgId
	 * @return
	 */
	public TransportMain getBySrcMsgId(Long srcMsgId);

	public int isPayedInfoFeeByUserIdAndTsId(Long userId, Long srcMsgId)throws Exception;

	public String replaceTaskContentByPhone(String taskContent);


	/**
	 * 通过IDS和userId查询指定前N天的数据
	 * @param userId
	 * @param ids
	 * @param day
	 * @return List<TransportMain>
	 */
	public List<TransportMain> getTransportListByIds(Long userId, List<Long> ids, Integer day);

	/**
	 * 通过IDS查询数据
	 * @param ids
	 * @return List<TransportMain>
	 */
	public List<TransportMain> getTransportListByIds(List<Long> ids);

	/**
	 * 获取微信分享页面货源和货主信息
	 * @param goodsId
	 * @return
	 */
	public WechatShareTransportBean getWechatShareDetail(Long goodsId);

	/**
	 * 设置货源不显示
	 * @param srcMsgId
	 * @return
	 */
	public  int noDisplayTransportMain(long srcMsgId);

	/**
	 * 查询用户时间范围内发货量
	 * @param userId
	 * @param date
	 * @return
	 */
	int getPublishCount(Long userId, Date date);

	/**
	 * 获取用户上一单
	 * @param userId
	 * @return
	 */
	TransportMain getLastTransport(Long userId);

    /**
     * 获取导入运单列表（货）
     * @param userId
     * @param day
     * @return
     * @throws Exception
     */
	public List<TransportMain> getImportTransportByUserId(Long userId,int day) throws Exception;

	/**
	 * 信用曝光按钮点击修改状态
	 * @param srcMsgId
	 * @return
	 */
	boolean updateCreditRetop(Long srcMsgId) throws Exception;
	void delMyGoods(Long goodsId);

	/**
	 * 根据请求参数查询当前用户在具体时间段内发布的货源的个数
	 * @param userId
	 * @param startTime
	 * @param endTime
	 * @param publishType
	 * @return
	 */
	Integer getPublishCountByParam(Long userId, Date startTime, Date endTime, Integer publishType);

	/**
	 * 查询某个时间之后有没有发布过电议有价货源和一口价货源
	 * @param detailUserId
	 * @param publishTime
	 * @return
	 */
	List<String> selectOfPublishType(Long detailUserId, Date publishTime);

	/**
	 * 校验个人货源是否重复
	 * @param oldSrdMsgId
	 * @param oldLabelJson
	 * @param transport
	 * @param refreshDuplicate
	 * @return
	 */
	TransportLabelJson checkPersonalDuplicate(Long oldSrdMsgId, TransportLabelJson oldLabelJson , Transport transport, boolean refreshDuplicate, String assignCarTel);

	/**
	 * 查询某个用户在某个时间之后有没有发布过的优车货源
	 * @param userId
	 * @param startTime
	 * @return
	 */
	int selectCountOfExcellentGoods(Long userId, Date startTime, Date endTime);
	/**
	 * 根据货源Id更新货源技术服务费信息
	 * @param SrcMsgId
	 * @param tecServiceFee
	 * @return boolean
	 */
	boolean updateTecServiceBySrcMsgId(Long SrcMsgId, BigDecimal tecServiceFee) throws Exception ;

	/**
	 * 根据货源Id更新labelJson
	 * @param SrcMsgId
	 * @param labelJson
	 * @return
	 * @throws Exception
	 */
	boolean updateLabelJsonBySrcMsgId(Long SrcMsgId, String labelJson);

	/**
	 * 根据货源ID集合获取还有效的货源总数
	 * @param haveAnyQuotedPriceAgainTransportMainIdList
	 * @return
	 */
    Integer getCountTransportMainIsValidForIdList(List<Long> haveAnyQuotedPriceAgainTransportMainIdList, Date todayBeginTime, Date todayEndTime);

	List<Long> getTransportMainIsValidSrcMsgIdsForIdList(List<Long> haveAnyQuotedPriceAgainTransportMainIdList, Date todayBeginTime, Date todayEndTime);

	/**
	 * 更新main表扩展表
	 *
	 * @param mainExtend
	 */
	void updateMainExtend(TytTransportMainExtend mainExtend);

	/**
	 * 添加main表扩展表
	 *
	 * @param mainExtend
	 */
	void addMainExtend(TytTransportMainExtend mainExtend);

	/**
	 * 获取货源扩展信息
	 *
	 * @param srcMsgId
	 * @return
	 */
	TytTransportMainExtend getMainExtend(Long srcMsgId);

	/**
	 * @param srcMsgId 货源ID
	 * @return TransportBusinessTypeEnum code,-1代表不属于任何货源类型
	 */
	Integer getTransportBusinessType(Long srcMsgId);

	Integer getPublishCountByUserId(Long userId, Date startTime, Date endTime);

	boolean checkPoorExperience(DwsNewIdentityTwoData data);

	/**
	 * 查询首次发布货源
	 *
	 * @param userId
	 * @return
	 */
	TransportMain getFirstTransport(Long userId);

    List<Long> getInReleaseTransportIdList(Long transportUserId);

    boolean isHaveSameTransportInPublish(Long srcMsgId);

	boolean isHaveSameStartCityAndDestCityTransportInPublish(Long srcMsgId);

    Integer countByGoodTypeName(List<Long> srcMsgIds, String goodTypeName);

	List<Long> getUserPublishingSrcMsgIds(Long userId);

	/**
	 * 校验是否是参与现金奖活动货源
	 * a. 有抽佣标签的货源且技术服务费>0
	 * b. 经分口径下的直客或物流公司的发布的前3个货源
	 * c. 经分口径下的直客或物流公司的货源，距离首发时间超过2小时还未成交的货源
	 */
	boolean isCashPrizeActivityTransport(TransportMain transport);
}
