package com.tyt.transport.service;


import com.tyt.base.service.BaseService;
import com.tyt.model.TytKeywordShortTransform;
import com.tyt.model.TytMachineTypeBrandParameter;

import java.util.List;

/**
 * 关键词搜索 俗称转换
 * <AUTHOR>
 * @date 2017-4-14下午5:44:50
 * @description
 */
public interface TytKeywordShortTransformService extends BaseService< TytKeywordShortTransform, Integer> {

	/**
	 * 获取俗称表list数据
	 * @param hql
	 * @param params
	 * @return
	 */
	public List<TytKeywordShortTransform> getKeywordShortList(String hql,final Object[] params);

	/**
	 * 根据搜索关键词匹配机器类型
	 *
	 * @param keyword
	 * @return
	 */
	 String queryKeywordShortByKeyword(String keyword);

	List<TytKeywordShortTransform> queryKeywordKeyword(String keyword, String userId);

	List<TytMachineTypeBrandParameter> searchMatchesGroupbarnd(String keyword, String userId);

	List<TytMachineTypeBrandParameter> searchMatchesGroupType(String keyword,String secondClass, String userId);

	List<TytMachineTypeBrandParameter> searchMatchesGroupshowName(String keyword, String userId);
}
