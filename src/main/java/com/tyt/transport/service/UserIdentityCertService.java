package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.EcaContract;
import com.tyt.model.UserIdentityCert;
import com.tyt.service.common.azt.service.IdentityInfo;
import com.tyt.transport.querybean.CertIdentityBean;

public interface UserIdentityCertService extends BaseService<UserIdentityCert, Long>{

	UserIdentityCert getUserIdentity(Long userId);
	
	/**
	 * 获取实名认证信息
	 * @param userId
	 * @return
	 */
	CertIdentityBean getCertIdentity(Long userId);

	/**
	 * 获取实名认证
	 * @param userName 用户真实姓名
	 * @param idCard 用户身份证号
	 * @return
	 */
	UserIdentityCert getUserIdentityCert(String userName, String idCard);

	/**
	 * 查看是否已实名认证
	 * @param userName 用户真实姓名
	 * @param idCard 用户身份证号
	 * @return 实名返回true，否则返回false
	 */
	Boolean checkUserIdentityCert(String userName, String idCard);

	/**
	 * 查看双方是否均已实名认证
	 * @param ecaContract 合同
	 * @return 实名返回true，否则返回false
	 */
	Boolean checkContractIdentity(EcaContract ecaContract);

	/**
	 * 保存实名认证信息
	 * @param userName 用户真实姓名
	 * @param idCard 用户身份证号
	 * @param userId 平台用户ID
	 * @return
	 */
	IdentityInfo saveUserIdentityCert(String userName, String idCard, Long userId);

	/**
	 * 忽略插入数据库，即存在相同的身份证号不插入
	 * @param identityCert
	 * @return 更新条数
	 */
	Integer addIgnoreUserIdentityCert(UserIdentityCert identityCert);
}
