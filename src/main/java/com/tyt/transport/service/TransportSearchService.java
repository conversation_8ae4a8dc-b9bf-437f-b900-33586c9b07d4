package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.Transport;
import com.tyt.transport.querybean.DestTransportSearchBean;
import com.tyt.transport.querybean.ScopeFallShortSqlBean;
import com.tyt.transport.querybean.SimilarTransportSearchBean;
import com.tyt.transport.querybean.TransportSearchSqlBean;

import java.util.List;

/**
 * 货物信息查询接口
 *
 * <AUTHOR>
 * @date  2019/5/15
 * @version   app5920编写
 */
public  interface TransportSearchService extends BaseService<Transport, Long> {


    /**
     * 省内倒短els查询
     * @param province 省份名称
     * @param queryType 查询类型 0：获取>tsId最新数据 1:首次查询；2：获取<tsId数据
     * @param tsId 货物信息序号ID
     * @param pageSize 返回最大结果集条
     * @return Map totalSize:符合条件的总数据，resultList:List<TransportBean>数据结果
     */
    List<Transport> findElsTransportForProvince(String province, int queryType, long tsId, int pageSize);

    /**
     * 省内倒短db查询
     * @param province 省份名称
     * @param queryType 查询类型 0：获取>tsId最新数据 1:首次查询；2：获取<tsId数据
     * @param tsId 货物信息序号ID
     * @param pageSize 返回最大结果集条
     * @return Map totalSize:符合条件的总数据，resultList:List<TransportBean>数据结果
     */
    List<Transport> findDbTransportForProvince(String province, int queryType, long tsId, int pageSize);



    /**
     * 获得今日之前最大货物ID
     * @return long 无值返回0
     */
    long getBeforeTodayMaxId();

    /**
     * 根据list对下长度和条数比较相等则加1返回，否则返回list大小
     * @param list 结果集
     * @param pageSize 条数
     * @return int 默认返回0
     */
    int getTotalSize(List list, int pageSize);

    /**
     * 范围倒短es查询
     * @param sqlBean 查询条件bean
     * @return list
     */
    List<Transport> findElsTransportForScope(ScopeFallShortSqlBean sqlBean);

    /**
     * 范围倒短db查询
     * @param sqlBean  查询条件bean
     * @return  list
     */
    List<Transport> findDbTransportForScope(ScopeFallShortSqlBean sqlBean);

    /**
     * 找货大厅es查询
     * @param sqlBean  查询条件bean
     * @return list
     */
    List<Transport> findElsTransportSearch(TransportSearchSqlBean sqlBean);

    /**
     * 找货大厅db查询
     * @param sqlBean  查询条件bean
     * @return list
     */
    List<Transport> findDbTransportSearch(TransportSearchSqlBean sqlBean);

    /**
     * 目的地货源els查询
     * @param bean 查询条件
     * @param searchSize 条数
     * @return list
     */
    List<Transport> findElsDestTransport(DestTransportSearchBean bean,int searchSize);

    /**
     * 目的地货源db查询
     * @param bean 查询条件
     * @param searchSize 条数
     * @return list
     */
    List<Transport> findDbDestTransport(DestTransportSearchBean bean, int searchSize);

    /**
     * 相似货源列表db查询
     */
    List<Transport> findDbSimilarTransportSearch(SimilarTransportSearchBean searchBean);
}
