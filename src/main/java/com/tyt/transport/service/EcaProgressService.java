package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.EcaProgress;

/**
 * 
 * <AUTHOR>
 * 
 */
public interface EcaProgressService extends BaseService<EcaProgress, Long> {
    /**
     * 增加用户签约进度
     * @param progress
     */
    void addEcaProgress(EcaProgress progress);

    /**
     * 作废合同时，增加的默认签约进度，代码可以提炼
     * @param userId
     * @param userName
     * @param ecaContractId
     */
    void insertEcaProgress(String userId, String userName, Long ecaContractId);
}
