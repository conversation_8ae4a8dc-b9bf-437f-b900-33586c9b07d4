package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.FreightRecord;
import com.tyt.model.FreightRoute;
import com.tyt.transport.querybean.FreighBean;

import java.lang.reflect.InvocationTargetException;

/**
 * 
 * <AUTHOR>
 * @date 2017年8月29日上午11:55:26
 * @description
 */
public interface FreightRouteService extends BaseService<FreightRoute, Long> {
	FreightRoute queryByKey(String key);
}
