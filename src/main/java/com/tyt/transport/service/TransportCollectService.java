package com.tyt.transport.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportCollect;
import com.tyt.transport.querybean.TransportQueryBean;

public interface TransportCollectService extends BaseService<TransportCollect,Long>{

	public List<Long> getInfoIdList(String cellPhone);
	
	public void delCollect(String cellPhone,Long infoId);
	
	public void updateId(Long oldId,Long newId);
	/**
	 * 是否重复收藏
	 * @param userId
	 * @param tsId
	 * @return
	 * @throws Exception
	 */
	public TransportCollect isExit(Long userId,Long tsId)throws Exception;
	/**
	 * 根据用户id获得tsId集合
	 * @param userId
	 * @return
	 */
    List<TransportQueryBean> getByUserId(Long userId,int queryType, long querySign)throws Exception;
    /**
     * 根据用户id删除信息
     * @param userId
     * @param infoId
     */
    public void delCollect(Long userId,Long infoId)throws Exception;
    /**
	 * 批量改变收藏状态
	 * @param infoId
	 * @param status
	 */
	public void updateStatus(Long infoId,Integer status);
	/**
	 * 批量置无效
	 * @param idList
	 * @param userId
	 * @param i
	 * @return
	 */
	public boolean updateStatusByIds(List<Long> idList,Integer status);

	/**
	 * 分页查询用户的收藏列表
	 * @param userId
	 * @param pageNumber
	 * @param pageSize
	 * @return
	 */
	ResultMsgBean getCollectByPageList(Long userId, Integer pageNumber, Integer pageSize);
}
