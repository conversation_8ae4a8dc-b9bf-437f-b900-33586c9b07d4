package com.tyt.transport.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytSttLimit;
import com.tyt.transport.querybean.SttLimitBean;


public interface SttLimitService extends BaseService<TytSttLimit,Long> {

	/**
	 * 返回 app端显示用的规则列表
	 * @return
	 */
	public List<SttLimitBean> getSttLimitBeanList(Long userId);
	
	/**
	 * 通过用户ID获得用户的发货条数限制类型
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public SttLimitBean getUserSttLimit(Long userId) throws Exception;
}
