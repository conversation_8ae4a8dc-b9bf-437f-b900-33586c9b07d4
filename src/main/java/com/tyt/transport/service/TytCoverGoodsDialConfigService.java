package com.tyt.transport.service;

import com.tyt.common.bean.PageData;
import com.tyt.infofee.bean.GoodsSingleDetailResultBean;
import com.tyt.model.TransportMain;
import com.tyt.plat.biz.covergoodsdial.pojo.*;
import com.tyt.plat.entity.base.TytCoverGoodsDialUserUseLog;
import com.tyt.transport.service.impl.TytCoverGoodsDialConfigServiceImpl;

/**
 * <AUTHOR>
 * @since 2024/01/15 13:38
 */
public interface TytCoverGoodsDialConfigService {
    /**
     * 获取优推好车主货方有效时间
     *
     * @return 优推好车主货方有效时间
     */
    Integer selectXTime();

    /**
     * 获取车方配置信息
     *
     * @param userId userId
     * @return 车方配置信息
     */
    CoverGoodsDialInfoVO getCoverGoodsDialInfo(Long userId);

    /**
     * 获取用户抢单豆详情
     *
     * @param userId userId
     * @return 车方配置信息
     */
    CoverGoodsDialUserInfoVO getCoverGoodsDialUserInfo(Long userId);

    /**
     * 上报倒计时
     *
     * @param req req
     */
    CountdownReportResp coverGoodsCountdownReport(CountdownReportReq req);

    /**
     * 使用免责卡
     *
     * @param req req
     */
    UseNVO useN(UseNReq req);

    /**
     * 获取详情拨打信息
     *
     * @param userId userId
     * @param tsId   userId
     * @return 详情拨打信息
     */
    TytCoverGoodsDialConfigServiceImpl.UserTsDialInfoRedisBean getUserTsDialInfoRedisBean(Long userId, Long tsId);

    CheckAutoDecreasePopConfigVO checkAutoDecreasePopConfig(Long userId);

    void reportAutoDecreasePopConfig(Long userId);

    PageData<TytCoverGoodsDialUserUseLog> getNUseRecord(Long userId, Integer pageNum, Integer pageSize);

    String getSendDefaultConfig(Integer type);

    /**
     * 捂货4.0处理逻辑
     */
    GoodsSingleDetailResultBean.CoverGoodsDialInfo coverGoodsVersion4(Long userId, TransportMain transportMain);
}
