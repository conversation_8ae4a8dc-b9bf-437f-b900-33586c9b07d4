package com.tyt.transport.service;

import com.tyt.transport.querybean.FallShortSearchLogBean;

public interface TransportLogUtilService {

	/**
	 * 货物详情浏览日志记录
	 * @param userId 用户ID  没有传0
	 * @param tsId   运输信息ID
	 * @param clientVersion 版本号
	 * @param clientSign 客户端类型
	 * @param status 运输信息状态
	 */
	public void detailsLog(long userId ,long tsId,String clientVersion,int clientSign,int status, int viewSource, int sortType, int sortIndex, int specialMark,
						   String startProvinc, String startCity, String startArea, String destProvinc, String destCity, String destArea, String benefitLabelCode, Long sharerUserId);
	/**
	 * 按时间查询日志记录
 	* @param usreID	user_id 没有传0
	* @param 出发地座标	start_coord
	* @param 出发地范围	start_range
	* @param 目的地座标	dest_coord
	* @param 目的地范围	dest_range
	* @param 车辆ID	car_id	 没有传0
	* @param 车头牌照号码	head_no
	* @param 车头牌照头字母	head_city
	* @param 客户端版本号	client_version
	* @param 客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
	* @param 排序类型	sort_type	排序类型1时间，2距离	 没有0
	* @param numberType 1首次进入列表找货 2点击查询按钮找货
	* @param osVersion 操作系统版本号（能获取到就填上）
	* @param clientId 终端唯一标识（能获取到就填上）
	*/
	public void searchLog(long userId,String startCoord,String startRange,String destCoord,String destRange,long carId,String headNo,String headCity,String clientVersion,int clientSign,int sortType,String numberType,String osVersion,String clientId,String carLength, String carType, String specialRequired,String startWeight,String endWeight);

	/**
	 * 查询后距离排序日志记录
	 * @param userId usreID
	 * @param sortType 排序类型1时间，2距离	 没有0
	 * @param clientVersion 客户端版本号
	 * @param clientSign客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
	 */
	public void searchDistanceSortLog(long userId,int sortType,String clientVersion,int clientSign);

	/**
	 * 暂时不用
	 * 拨打电话日志记录
	 * @param userId usreID
	 * @param tsId 运输信息ID
	 * @param clientVersion 客户端版本号
	 * @param clientSign客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
	 */
	public void callPhoneLog(long userId,long tsId,String clientVersion,int clientSign);

    /**
     * 倒短日志记录
     * @param bean
     */
    public void fallShortSearchLog(FallShortSearchLogBean bean);
}
