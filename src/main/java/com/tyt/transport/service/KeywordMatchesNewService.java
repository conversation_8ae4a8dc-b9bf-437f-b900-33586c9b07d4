package com.tyt.transport.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import com.tyt.base.service.BaseService;
import com.tyt.model.TytKeywordMatchesNew;
import com.tyt.model.TytMachineTypeNew;
import com.tyt.transport.querybean.StandardStatusBean;

/**
 * 关键词搜索服务层接口
 * 
 * <AUTHOR>
 * @date 2017-4-14下午5:44:50
 * @description
 */
public interface KeywordMatchesNewService extends BaseService<TytKeywordMatchesNew, Long> {

	/**
	 * 根据搜索关键词匹配机器类型
	 * 
	 * @param keyword
	 * @return
	 */
	List<TytMachineTypeNew> queryMachineTypeByKeyword(String keyword, String userId);

	/**
	 * 
	 * @param keyword
	 * @return
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 */
	StandardStatusBean queryStandardStatusByKeyword(String keyword) throws IllegalAccessException, InvocationTargetException;

}
