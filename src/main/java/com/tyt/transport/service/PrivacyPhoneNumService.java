package com.tyt.transport.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.transport.bean.PrivacyPhoneNumGoodIdReq;

public interface PrivacyPhoneNumService {

    /**
     * 根据参数获取虚拟号（调用本方法前请保证bizId在业务中存在）
     * @param privacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq
     * @param resultMsgBean resultMsgBean
     * @return resultMsgBean data(隐私号字符串)（返回data可能为null）
     */
    ResultMsgBean getPrivacyPhoneNum(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq, ResultMsgBean resultMsgBean);

    String makeAXBUserFieldParam(Long driverUserId, Long goodsId);

}
