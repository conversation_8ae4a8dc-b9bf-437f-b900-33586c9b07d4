package com.tyt.transport.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytCarOwnerIntention;
import com.tyt.transport.querybean.CarOwnerIntentionBean;
import com.tytrecommend.model.TytPreference;

public interface TytCarOwnerIntentionService extends BaseService<TytCarOwnerIntention, Long> {
	/**
	 * 获得货物与车的关系列表
	 * @param srcMsgId 货物原信息ID
	 * @return List<TytCarOwnerIntention>
	 */
	public List<TytCarOwnerIntention> getTytCarOwnerIntentionList(Long srcMsgId, Integer currentPage);

	/**
	 * 获得车辆的偏好信息
	 * @param carUserId 车主userID
	 * @param carId 车辆ID
	 * @return
	 */
	public TytPreference getTytPreference(Long carUserId, Long carId);
	public void updateStatus(String srcMsgId, String carUserId);
	public void updateAgree(String srcMsgId, String carUserId);
	
	/**
	 * 根据 货物源Id和车主Id获取车主偏好车
	 * @param srcMsgId
	 * @param carUserId
	 * @return
	 */
	public TytCarOwnerIntention getBySrcMsgIdAndCaruserId(Long srcMsgId, Long carUserId);


	/**
	 * 获取车辆信息
	 * @param tsIds
	 * @return
	 */
	public List<CarOwnerIntentionBean> getCarOwnerListByTsIds(List<Long> tsIds);


}
