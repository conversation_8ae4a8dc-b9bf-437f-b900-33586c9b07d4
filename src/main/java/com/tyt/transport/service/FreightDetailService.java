package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.*;
import com.tyt.transport.querybean.SameTransportAveragePriceReq;

/**
 * User:  tianjw
 */
public interface FreightDetailService extends BaseService<TytFreightDetailInfo, Long> {

    /**
     * Retrieves the average price for the same transport based on the given request.
     * The request contains information about the goods type, start city, destination city, distance, weight, and date range.
     * Returns a ResultMsgBean object that contains the result code, message, and data.
     *
     * @param sameTransportAveragePriceReq the request object containing the search criteria
     * @return the ResultMsgBean object with the average price information
     */
    ResultMsgBean getSameTransportAveragePrice(SameTransportAveragePriceReq sameTransportAveragePriceReq);

    /**
     * 计算高速费用
     * @param freightDetail
     * @param tonne
     * @param totalDistance
     * @return
     */
    public TytFreightDetailInfo calcHighwayCost(TytFreightDetailInfo freightDetail, Float tonne, String totalDistance, String lineDetails,Integer machineNumber);

    /**
     * 计算燃油和固定成本
     * @param freightDetail
     * @param cargoLength
     * @param tonne
     * @return
     */
    public TytFreightDetailInfo calcBaseOilCost(TytFreightDetailInfo freightDetail, String cargoLength, String tonne, String totalDistance);

    public FreightRecord getFreightRecordByStartEndPoint(String mapKey);

    public void getMinAndGuidingProfitRates(String startProvinc,String startCity,String destProvinc, String destCity, FreightPriceDetailInfo freightPriceDetailInfo);

    public Double updateDistanceByDbDistance(Double distance,Double dbDistance,FreightRoute freightRoute);

    public void sendMqMessage2MqByStart2Dest(FreightRoute freightRoute);

    /**
     * 获取货物油耗和固定成本
     * @param freightDetail
     * @param cargoLength
     * @param tonne
     * @return
     */
    public FreightPriceSearchLog getBaseOilCost(FreightPriceSearchLog freightDetail, String cargoLength, String tonne, String totalDistance);
}
