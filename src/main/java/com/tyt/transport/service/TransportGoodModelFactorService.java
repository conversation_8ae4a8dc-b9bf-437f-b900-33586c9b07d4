package com.tyt.transport.service;


import com.tyt.model.Transport;
import com.tyt.plat.entity.base.TytTransportMainExtend;

/**
 * <p>
 * 好中差货模型因子 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface TransportGoodModelFactorService {

    /**
     * 判断模型等级
     * @return >0 好中差货；<0 货参不完整的好中差货；=0 不符合打标条件
     */
    int judgeModelLevel(Transport transport, TytTransportMainExtend mainExtend);
}
