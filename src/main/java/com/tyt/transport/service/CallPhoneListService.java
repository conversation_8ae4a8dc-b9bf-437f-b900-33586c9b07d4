package com.tyt.transport.service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.transport.querybean.CallPhoneSearchResultBean;

public interface CallPhoneListService {
    /**
     * 获取电话列表
     * @param goodId  //货源id
     * @param moduleType //模块类型
     * @param base //基础参数
     * @return  //resultBean
     * @throws Exception
     */
    CallPhoneSearchResultBean addGetCallPhoneList(String goodId, String moduleType,String path, BaseParameter base) throws Exception;
}
