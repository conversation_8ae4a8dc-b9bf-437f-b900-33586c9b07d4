package com.tyt.transport.service;

import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.model.*;
import com.tyt.plat.entity.base.TytTransportExtend;
import com.tyt.plat.vo.ts.*;
import com.tyt.transport.bean.GoodModelResult;
import com.tyt.transport.querybean.BoPublishTransportBean;
import com.tyt.transport.querybean.TransportDoneRequest;
import com.tyt.transport.querybean.TransportPublishBean;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

public interface TransportBusinessInterface {

    ResultMsgBean returnLockStatus();

    /**
     * 锁定货源操作
     * @param tsId
     * @return
     */
    boolean lockTransportOpt(Long tsId);

    /**
     * 解锁
     * @param tsId
     */
    void unlockTransportOpt(Long tsId);

    /**
     * 小程序货源锁定
     * @param backendId
     * @return
     */
    boolean lockBackendTransport(Long backendId);

    /**
     * 校验货源是否可以刷新曝光
     * @param resendCounts
     * @param userId
     * @param dbCtime
     * @return
     */
    ResponseCodeEnum checkTransportRefresh(Integer topInterval, Integer maxResendCounts, Integer resendCounts, Long userId, Timestamp dbCtime);

    /**
     * 校验是否允许置顶
     * @param dbLastTransport
     * @param usePermission 使用还是校验权益
     * @return
     */
    ResponseCodeEnum transportAllowTop(Transport dbLastTransport, boolean usePermission);

    /**
     * 保存货源表，并处理是否置顶逻辑
     * @param transport
     * @param topFlag
     */
    void saveTransportAndCheckTop(Transport transport, TytTransportExtend transportExtend, boolean topFlag, Boolean useExposure);

    /**
     * 添加货物信息到两张表
     * modify by tianjw on 20170720   增加货物子表SUB表参数
     *
     * @param transport
     * @throws Exception
     */
    public Long addTransportBusiness(Transport transport, TransportSubBean transportSubBean) throws Exception;

//    /**
//     * 发布货物信息时
//     * 设置用户的发布条数
//     * @param userId
//     * @throws Exception
//     */
//    public void setPublicInfoNum(Long userId) throws Exception ;

    /**
     * 根据hashCode更新信息状态
     *
     * @param srcMsgId
     * @param status:状态               1有效（发布中），0无效，2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     * @param infoStatus:信息费运单状态：0待接单 1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
     * @return
     */
    public boolean updateStatusBusiness(Long srcMsgId, String recommendHashCode, Long userId, Integer status,
                                        String infoStatus, boolean saveVary) throws Exception;

    /**
     * 更改货源状态
     * @param srcMsgId
     * @param recommendHashCode
     * @param userId
     * @param status
     * @param infoStatus
     * @return
     * @throws Exception
     */
    boolean updateStatusBusiness(Long srcMsgId, String recommendHashCode, Long userId, Integer status, String infoStatus) throws Exception;

    public int updateDisplayTypeByIdsBusiness(List<Long> idList, List<Long> idListMain, Integer display) throws Exception;

    /**
     * 信息费版:撤销货源/成交货源
     *
     * @param userId
     * @param operateBtnType 1撤销货源 2信息费线上成交 3信息费线下成交
     *                       成交的定义:status=4(线下status=4时info_status=3;线上info_status=2时status=4)
     *                       status:状态 1有效（发布中），0无效，2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     *                       infoStatus:0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
     * @return
     * @throws Exception
     */
    public ResultMsgBean saveInfoFeeUpdateBtnStatus(Long userId,
                                                    Integer operateBtnType, Long goodsId, ResultMsgBean resultMsgBean) throws Exception;


    /**
     * 信息费版:撤销货源/成交货源（新接口）
     *
     * @param userId
     * @param operateBtnType 1撤销货源/2设置成交
     *                       成交的定义:status=4(线下status=4时info_status=3;线上info_status=2时status=4)
     *                       status:状态 1有效（发布中），0无效，2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     *                       infoStatus:0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
     * @param backoutReasonKey 撤销原因key
     * @param backoutReasonValue 撤销原因Value
     * @return
     * @throws Exception
     */
    public ResultMsgBean saveInfoFeeUpdateBtnStatusNew(Long userId, Integer operateBtnType, Long goodsId, TransportDoneRequest doneRequest,
                                                       String backoutReasonKey, Integer backoutReasonValue,String specificReason ,String remark,boolean saveVary
            , String backoutReasonKeyNew, Integer backoutReasonValueNew) throws Exception;

    ResultMsgBean saveInfoFeeUpdateBtnStatusNew(TransportStatusOption transportStatusOption, TransportDoneRequest doneRequest) throws Exception;

    /**
     * 老版更新状态：无效、成交、撤销
     *
     * @param oldTransport
     * @param status
     * @param userId
     * @throws Exception
     */
    public void updateTransportStatus(Transport oldTransport, Integer status,
                                      Long userId) throws Exception;

    /**
     * 改变tyt_transport,tyt_transport_main表的infoStatus值
     *
     * @param infoStatus:信息费运单状态0待接单 1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
     * @param transportNo:运单号
     * @return 修改的条数
     * @throws Exception
     */
    public int saveChangeInfoStatus(String infoStatus, Long srcMsgId) throws Exception;

    /**
     * 根据货物ID获取货物信息:先从tyt_transport(数据量少)表找数据，再从tyt_transport_main(所有数据)中找,过期信息可能需要在tyt_transport_main表中找原数据
     *
     * @param goodsId
     * @return
     * @throws Exception
     */
    public Transport getByGoodsId(Long goodsId) throws Exception;

    /**
     * @param goodsId 已知人工派单使用tsId， plat撤销成交为srcId
     * @return
     * @throws Exception
     */
    public Transport getByGoodsIdForUnLock(Long goodsId) throws Exception;

    public void saveInfoFeeUpdateBtnStatus(Long userId, Integer operateType,
                                           Transport oldTransport) throws Exception;

    /**
     * 修改src_msg_id(按天有效)
     *
     * @param goodsId
     * @param srcMsgId
     * @return
     * @throws Exception
     */
    public int updateSrcMsgId(Long goodsId, Long srcMsgId) throws Exception;

    /**
     * 校验个人重复货源
     * @param transport
     * @return boolean (true:重复； false：不重复)
     */
    boolean checkPersonalSimilarity(Transport transport, Long srcMsgId, String assignCarTel);

    /**
     * 刷新货源的用户相关属性
     * @param transport
     * @param tytUserSub
     * @param userCreditInfo
     */
    void refreshUserInfo(Transport transport, TytUserSub tytUserSub, ApiDataUserCreditInfoTwo userCreditInfo);

    TransportLabelJson getTransportLabelJson(TransportMain transportMain);

     TransportLabelJson getTransportLabelJson(String labelJsonText);

    /**
     * 更新标签相关
     * @param transport
     */
    TransportLabelJson refreshLabelJson(Long srcMsgId, TransportLabelJson oldLabelJson, Transport transport,
                                        ApiDataUserCreditInfoTwo userCreditInfo, boolean refreshDuplicate, TransportPublishBean publishBean);

    /**
     * 专车自动派单
     * @param newTransport
     * @param user
     */
    void autoAssignOrderForSpecialCar(Transport newTransport, User user);

    /**
     * 存储BI需要的数据
     * @param srcMsgId
     * @param transportBIDataJsonParam
     */
    void saveSomeBINeedDataResult(Long srcMsgId, TransportBIDataJson transportBIDataJsonParam);

    /**
     * 单独用子表存储BI所需的车找货埋点数据
     * @param transportCarFindBIDataJson
     */
    void saveFindTransportLogBIData(TransportCarFindBIDataJson transportCarFindBIDataJson);

    /**
     * 校验重复货源提示，并判断是否应该抛出异常
     * @param confirm
     * @param transport
     * @param oldLabelJson
     * @param newLabelJson
     */
    void checkPersonalDuplicate(Integer confirm, Transport transport, TransportLabelJson oldLabelJson,
                                TransportLabelJson newLabelJson);

    /**
     * 直接发布
     * @param saveDirectReq
     * @param price 运费
     * @param topFlag 是否置顶
     * @param saveVary 是否保存vary
     * @param refreshDuplicate 是否计算重货
     * @return
     * @throws Exception
     */
    //(Long goodsId,Integer publishType,String price, Long userId, String clientVersion,
    //Integer clientSign,Integer isBackendTransport, boolean topFlag, boolean saveVary)
    public ResultMsgBean saveGoodsDirectV5930(SaveDirectReq saveDirectReq, boolean topFlag,
                                              boolean saveVary, boolean refreshDuplicate,Long excellCardId) throws Exception;

    /**
     * 优车逻辑构造昵称
     * @param transport transport
     * @param user user
     */
    void makeNickName(Transport transport, User user);

    /**
     * 调用经分获取好货模型结果
     * @param transport
     * @return
     */
    GoodModelResult checkInstantGrab(Transport transport);

    /**
     * 获取抽佣分数
     * @param transport
     * @return
     */
    BigDecimal checkCommissionScore(Transport transport);

    /** 记录货源好货分数、好货运价分数
     *
     * @param transport
     * @param newTransportSrcMsgId
     * @param newTransportId
     */
    void recordScoreEveryTransport(Transport transport, Long newTransportSrcMsgId, Long newTransportId, boolean isHistoryGoods);

    /**
     * 开票货源指派车方
     * @param data 货源发布成功数据
     * @param userId 货主ID
     * @return
     */
    ResultMsgBean invocieTransportAssignCar(BoPublishTransportBean data, Long userId, String assignCarTel);

    /**
     * 判断是否是秒抢货源
     * @param commissionScore 好货运价模型分数
     */
    boolean checkIsSeckillGoods(Transport transport, BigDecimal commissionScore);

    /**
     * 是否发放曝光卡
     */
    void checkAndSaveExposureCardGiveaway(Long srcMsgId) throws IOException;
}
