package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytCarBaseOilCost;
import com.tyt.smallTools.controller.bean.TytCarBaseOilCostBean;

import java.util.List;

/**
 * User:  tianjw
 */
public interface CarBaseOilCostService extends BaseService<TytCarBaseOilCost, Long> {
    public List<TytCarBaseOilCost> getCarBaseOilCostRule(String cargoLength, String tonne);
    public List<TytCarBaseOilCost> getCarBaseOilCostRuleByCargoLengthGT(String cargoLength);
    /**
     * 获取所有列表
     * @return
     */
	public List<TytCarBaseOilCostBean> getAllList();
}
