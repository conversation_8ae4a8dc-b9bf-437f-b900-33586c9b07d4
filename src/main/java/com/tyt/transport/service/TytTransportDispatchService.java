package com.tyt.transport.service;

import com.tyt.model.Transport;
import com.tyt.model.CsMaintainedCustom;

import java.util.Date;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/1/6
 */
public interface TytTransportDispatchService {
    void saveTransportDispatch(Transport transport, CsMaintainedCustom csMaintainedCustom, Integer clientSign);


    void saveTransportDispatch(Transport transport, CsMaintainedCustom csMaintainedCustom, Integer clientSign, String giveGoodsPhone, String giveGoodsName);

    int countExcellentByPhone(String cellPhone, Date startTime, Date endTime);
}
