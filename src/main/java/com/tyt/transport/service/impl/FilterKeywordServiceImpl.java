package com.tyt.transport.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytFilterKeyword;
import com.tyt.transport.service.FilterKeywordService;

@Service("filterKeywordService")
public class FilterKeywordServiceImpl extends BaseServiceImpl<TytFilterKeyword, Long> implements FilterKeywordService {

	@Resource(name = "filterKeywordDao")
	public void setBaseDao(BaseDao<TytFilterKeyword, Long> filterKeywordDao) {
		super.setBaseDao(filterKeywordDao);
	}
}
