package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCarBaseOilCost;
import com.tyt.smallTools.controller.bean.TytCarBaseOilCostBean;
import com.tyt.transport.service.CarBaseOilCostService;

import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("carBaseOilCostService")
public class CarBaseOilCostServiceImpl extends BaseServiceImpl<TytCarBaseOilCost, Long> implements CarBaseOilCostService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "carBaseOilCostDao")
	public void setBaseDao(BaseDao<TytCarBaseOilCost, Long> carBaseOilCostDao) {
		super.setBaseDao(carBaseOilCostDao);
	}

	@Override
	public List<TytCarBaseOilCost> getCarBaseOilCostRule(String cargoLength, String tonne) {
		StringBuffer sb = new StringBuffer();
		sb.append("select cargo_length_begin cargoLengthBegin,cargo_length_end cargoLengthEnd,car_weight carWeight,weight_begin weightBegin,wieght_end weightEnd,per_day_cost perDayCost,oil_wear20 oilWear20,oil_wear2030 oilWear2030,oil_wear30 oilWear30 FROM tyt_car_base_oil_cost " );
		if(StringUtils.hasLength(cargoLength)){
			sb.append(" WHERE weight_begin <= ").append(tonne);
			sb.append(" AND wieght_end >= ").append(tonne);
		}
		if(StringUtils.hasLength(cargoLength)){
			sb.append(" AND cargo_length_begin <= ").append(cargoLength);
			sb.append("  AND cargo_length_end > ").append(cargoLength);
		}
		return this.getBaseDao().search(sb.toString(), null, TytCarBaseOilCost.class, new Object[]{});
	}

	@Override
	public List<TytCarBaseOilCost> getCarBaseOilCostRuleByCargoLengthGT(String cargoLength) {
		StringBuffer sb = new StringBuffer();
		sb.append("select cargo_length_begin cargoLengthBegin,cargo_length_end cargoLengthEnd,car_weight carWeight,weight_begin weightBegin,wieght_end weightEnd,per_day_cost perDayCost,oil_wear20 oilWear20,oil_wear2030 oilWear2030,oil_wear30 oilWear30 FROM tyt_car_base_oil_cost " );
			sb.append(" where cargo_length_begin >= ").append(cargoLength);
		sb.append(" ORDER BY id ASC");
		return this.getBaseDao().search(sb.toString(), null, TytCarBaseOilCost.class, new Object[]{});
	}

	@Override
	public List<TytCarBaseOilCostBean> getAllList() {
		String sql="SELECT c.`ID` id,c.`cargo_length_begin` cargoLengthBegin,"
				+ "c.`car_weight` carWeight,c.`wieght_end` weightEnd,c.`per_day_cost` perDayCost,"
				+ "c.`car_oil_wear` carOilWear,c.`oil_wear20` oilWear20,c.`oil_wear2030` oilWear2030,"
				+ "c.`oil_wear30` oilWear30,c.`mtime` mtime  FROM tyt_car_base_oil_cost c ORDER BY id ";
		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
		map.put("id", Hibernate.LONG);
		map.put("cargoLengthBegin", Hibernate.INTEGER);
		map.put("carWeight", Hibernate.FLOAT);
		map.put("weightEnd", Hibernate.FLOAT);
		map.put("perDayCost", Hibernate.INTEGER);
		map.put("carOilWear", Hibernate.FLOAT);
		map.put("oilWear20", Hibernate.FLOAT);
		map.put("oilWear2030", Hibernate.FLOAT);
		map.put("oilWear30", Hibernate.FLOAT);
		map.put("mtime", Hibernate.TIMESTAMP);
		return this.getBaseDao().search(sql, map, TytCarBaseOilCostBean.class,new Object[]{});
	}
}
