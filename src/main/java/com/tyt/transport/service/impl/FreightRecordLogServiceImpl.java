package com.tyt.transport.service.impl;

import java.util.List;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.FreightRecordLog;
import com.tyt.transport.service.FreightRecordLogService;

@Service("freightRecordLogService")
public class FreightRecordLogServiceImpl extends BaseServiceImpl<FreightRecordLog, Long> implements FreightRecordLogService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "freightRecordLogDao")
	public void setBaseDao(BaseDao<FreightRecordLog, Long> freightRecordLogDao) {
		super.setBaseDao(freightRecordLogDao);
	}

	@Override
	public FreightRecordLog queryByKeyAndDistance(String key, Integer distance) {
		List<FreightRecordLog> freightRecordLogs = this.getBaseDao().searchByHql("from FreightRecordLog where mapKey=? and distance=?", new Object[] { key, distance }, null, null);
		return freightRecordLogs.size() == 1 ? freightRecordLogs.get(0) : null;
	}
}
