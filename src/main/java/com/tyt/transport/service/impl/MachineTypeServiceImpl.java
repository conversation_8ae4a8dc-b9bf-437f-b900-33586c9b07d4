package com.tyt.transport.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.TytMachineTypeBean;
import com.tyt.user.service.TytConfigService;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytMachineType;
import com.tyt.transport.service.MachineTypeService;
import com.tyt.util.Constant;

@Service("machineTypeService")
public class MachineTypeServiceImpl extends BaseServiceImpl<TytMachineType, Long> implements MachineTypeService {

	private static final String  TRANSPORT_CARTYPE_STANDARD = "TRANSPORT_CARTYPE_STANDARD" ;
	private static final String  defaultValue="transport_carType_standard_{id}";
	private static final int activeTime = 3*24*60*60;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "machineTypeDao")
	public void setBaseDao(BaseDao<TytMachineType, Long> machineTypeDao) {
		super.setBaseDao(machineTypeDao);
	}

	@Override
	public List<TytMachineType> queryMachineTypeByGeneralMatch(int matchesTypeCacheSize, String keyword) {
		return this.getBaseDao().searchByHql("SELECT new com.tyt.model.TytMachineType(id, brand, machineType, type, weight, length, width, height) from TytMachineType where general_matches_item LIKE ? ", new Object[] { '%' + keyword + '%' }, 1, Constant.MATCHES_TYPE__CACHE_SIZE);
	}

	/**
	 * 获得货物类型模版
	 * @param id
	 * @return
	 */
	public TytMachineType getTytMachineTypeForId(Long id){
		String value = tytConfigService.getStringValue(TRANSPORT_CARTYPE_STANDARD, defaultValue);
		String key = StringUtils.replace(value, "{id}", id.toString());
		Object obj = RedisUtil.getObject(key);
		if(obj==null){
			TytMachineType tytMachineType =this.getById(id);
			RedisUtil.setObject(key, tytMachineType, activeTime);
			return tytMachineType;
		}
		return (TytMachineType) RedisUtil.getObject(key);
	}

	@Override
	public List<TytMachineTypeBean> getListByBrandType(String brandType) {
		String sql="SELECT m.id id,m.brand_type brandType,m.weight weight,m.length length,m.width width,m.height height" +
				" FROM tyt_machine_type m WHERE general_matches_item LIKE ? LIMIT 10 ";
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("brandType", Hibernate.STRING);
		scalarMap.put("weight", Hibernate.STRING);
		scalarMap.put("length", Hibernate.STRING);
		scalarMap.put("width", Hibernate.STRING);
		scalarMap.put("height", Hibernate.STRING);
		List<TytMachineTypeBean> list = this.getBaseDao().search(sql, scalarMap, TytMachineTypeBean.class, new Object[]{"%"+brandType+"%"});
		return list;
	}
}
