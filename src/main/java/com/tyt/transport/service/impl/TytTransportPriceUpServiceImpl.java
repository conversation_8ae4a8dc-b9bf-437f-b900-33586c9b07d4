package com.tyt.transport.service.impl;

import com.tyt.plat.entity.base.TytTransportPriceUp;
import com.tyt.plat.mapper.base.TytTransportPriceUpMapper;
import com.tyt.transport.service.TytTransportPriceUpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class TytTransportPriceUpServiceImpl  implements TytTransportPriceUpService {
    @Autowired
    private TytTransportPriceUpMapper tytTransportPriceUpMapper;


    @Override
    @Async(value="mqExecutor")
    public void addTytTransportPriceUpByAsync(TytTransportPriceUp tytTransportPriceUp) {
        tytTransportPriceUp.setCreateTime(new Date());
        tytTransportPriceUpMapper.insertSelective(tytTransportPriceUp);
    }
}
