package com.tyt.transport.service.impl;

import com.tyt.messagecenter.core.exception.CustomException;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.entity.base.TytTransportIntention;
import com.tyt.plat.mapper.base.TytTransportIntentionMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TytTransportIntentionService;
import com.tyt.util.LockUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;

@Slf4j
@Service("tytTransportIntentionService")
public class TytTransportIntentionServiceImpl implements TytTransportIntentionService {

    @Autowired
    private TytTransportIntentionMapper transportIntentionMapper;

    private static final Long min_change_id = 5000L;

    private synchronized Long initRedisIncr() {

        Long dbChangeId = null;
        try {
            boolean lockResult = LockUtil.lockObject("2", RedisKeyConstant.intention_change_lock, 30);

            if (!lockResult) {
                log.error("initRedisIncr_lock_error ! ");
                return null;
            }

            dbChangeId = transportIntentionMapper.getMaxChangeId();

            if (dbChangeId == null || dbChangeId < min_change_id) {
                dbChangeId = min_change_id + 1L;
            } else {
                dbChangeId = dbChangeId + 1L;
            }

            RedisUtil.set(RedisKeyConstant.IncrKey.intention_change, dbChangeId + "", 0);

            log.info("initRedisIncr : dbChangeId : {}", dbChangeId);
        } finally {
            LockUtil.unLockObject("2", RedisKeyConstant.intention_change_lock);
        }

        return dbChangeId;
    }

    @Override
    public Long getNextChangeId() {
        Long nextChangeId = RedisUtil.incr(RedisKeyConstant.IncrKey.intention_change);

        if (nextChangeId < min_change_id) {
            //现在没有redis，进行初始化
            nextChangeId = this.initRedisIncr();
            nextChangeId = RedisUtil.incr(RedisKeyConstant.IncrKey.intention_change);
        }

        if (nextChangeId < min_change_id) {
            log.error("ERROR_GET_NEXT_changeId");
            throw CustomException.createException();
        }
        return nextChangeId;
    }

    @Override
    public void disableIntentionTransport(Long srcMsgId) {
        Long nextChangeId = this.getNextChangeId();

        Example exa = new Example(TytTransportIntention.class);
        exa.and().andEqualTo("srcMsgId", srcMsgId);

        TytTransportIntention intention = new TytTransportIntention();
        intention.setStatus(0);
        intention.setChangeId(nextChangeId);
        intention.setModifyTime(new Date());

        transportIntentionMapper.updateByExampleSelective(intention, exa);
    }

}
