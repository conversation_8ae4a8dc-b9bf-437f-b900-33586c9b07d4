package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.plat.client.transport.ThPriceClient;
import com.tyt.plat.entity.base.TytCity;
import com.tyt.plat.entity.base.TytExcellentPriceConfig;
import com.tyt.plat.entity.base.TytExcellentPriceConfigUser;
import com.tyt.plat.mapper.base.TytCityMapper;
import com.tyt.plat.mapper.base.TytExcellentPriceConfigMapper;
import com.tyt.plat.mapper.base.TytExcellentPriceConfigUserMapper;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.transport.querybean.TransportCarryBean;
import com.tyt.transport.service.BsPublishTransportService;
import com.tyt.transport.service.ExcellentPriceConfigService;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.tyt.util.Constant.EXCELLENT_PRICE_THPRICE_MILEAGE;

/**
 * <AUTHOR>
 * @since 2024/8/10 13:25
 */
@Slf4j
@Service
public class ExcellentPriceConfigServiceImpl implements ExcellentPriceConfigService {

    @Autowired
    private TytExcellentPriceConfigMapper excellentPriceConfigMapper;

    @Autowired
    private TytExcellentPriceConfigUserMapper excellentPriceConfigUserMapper;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private ThPriceClient thPriceClient;

    @Override
    public CarryPriceVo getSuggestPrice(TransportCarryBean transportCarryBean) {
        CarryPriceVo carryPriceVo = new CarryPriceVo();
        // 查询配置信息
        TytExcellentPriceConfig config = this.getConfig(transportCarryBean);
        if (config == null) {
            log.info("优车定价货源 出发地目的地不符合路线配置 {}", JSON.toJSONString(transportCarryBean));
            return null;
        }
        // 超起步里程=if(实际运输里程>=起步里程,(实际运输里程-起步里程),0)
        String distance = transportCarryBean.getDistance();
        if (StringUtils.isBlank(distance)) {
            distance = "0";
        }
        BigDecimal subtract = new BigDecimal(distance).subtract(config.getStartMileage());
        BigDecimal exceedStartMileage = subtract.intValue() > 0 ? subtract : new BigDecimal(0);

        // 优车定价最小值,(起步价+超里程单公里价*超起步里程)*调节系数*用户系数
        BigDecimal fixPriceMin = (config.getStartPrice().add(config.getUnitPrice().multiply(exceedStartMileage))).multiply(config.getAdjustCoefficient());
        // 查询用户系数
        TytExcellentPriceConfigUser configUser = excellentPriceConfigUserMapper.getByConfigIdAndUserId(config.getId(), transportCarryBean.getUserId());
        if (configUser != null && configUser.getUserCoefficient() != null) {
            fixPriceMin = fixPriceMin.multiply(configUser.getUserCoefficient());
        }


        // 优车定价最大值,fixPriceMin*1.2，向上取整百+100
        BigDecimal fixPriceMax = fixPriceMin.multiply(config.getMaxPriceCoefficient()).divide(new BigDecimal("100"), 0, RoundingMode.CEILING).multiply(new BigDecimal("100")).add(new BigDecimal("100"));
        //   优车定价更快应答值     fixPriceMin*1.1，向上取整百
        BigDecimal fixPriceFast = fixPriceMin.multiply(config.getFasterCoefficient()).divide(new BigDecimal("100"), 0, RoundingMode.CEILING).multiply(new BigDecimal("100"));

        // 若fixPriceFast=fixPriceMin，则fixPriceFast=fixPriceFast+100
        if (fixPriceFast.compareTo(fixPriceMin) == 0) {
            fixPriceFast = fixPriceFast.add(new BigDecimal("100"));
        }
        // 若fixPriceFast=fixPriceMax，则fixPriceMax=fixPriceMax+100
        if (fixPriceFast.compareTo(fixPriceMax) == 0) {
            fixPriceMax = fixPriceMax.add(new BigDecimal("100"));
        }

        //  最小值向下取整百
        BigDecimal fixPriceMinAfterFlow = fixPriceMin.divide(new BigDecimal("100"), 0, RoundingMode.FLOOR).multiply(new BigDecimal("100"));

        if (fixPriceMinAfterFlow.equals(BigDecimal.ZERO)) {
            log.info("优车定价货源,计算出的最小运价为0. {}", JSON.toJSONString(transportCarryBean));
            return null;
        }

        carryPriceVo.setFixPriceMin(fixPriceMinAfterFlow.intValue());
        carryPriceVo.setFixPriceMax(fixPriceMax.intValue());
        carryPriceVo.setFixPriceFast(fixPriceFast.intValue());
        // 如果用户在 优车2.0支持电议 AB测中，且优车2.0配置支持可电议，则显示 可电议 标签
        if (config.getPriceModel() != null && config.getPriceModel().contains("2")) {
            carryPriceVo.setIsRouteSupportTeleNegotiation(1);
            Integer userType = abtestService.getUserType("excellent2_allow_tele_negotiation", transportCarryBean.getUserId());
            carryPriceVo.setIsAllowTeleNegotiation(Objects.equals(userType, 1) ? 1 : 0);
        }
        return carryPriceVo;

    }


    @Override
    public CarryPriceVo getThPrice(TransportCarryBean transportCarryBean) {
        log.info("获取优车2.0建议价请求参数 {}", JSON.toJSONString(transportCarryBean));
        try {
            Response<CarryPriceVo> response = thPriceClient.getThPrice(transportCarryBean).execute();
            if (response.isSuccessful()) {
                log.info("获取优车2.0建议价请求参数, 返回结果:{}", JSON.toJSONString(response.body()));
                return response.body();
            } else {
                log.info("获取优车2.0建议价请求参数失败, 返回结果:{}", response.code() + ":" + JSON.toJSONString(response.body()));
            }
        } catch (IOException e) {
            log.info("获取优车2.0建议价 异常", e);
            return null;
        }
        return null;

        /*CarryPriceVo carryPriceVo = getSuggestPrice(transportCarryBean);
        if (carryPriceVo != null) {
            //  阻断价最大值thMaxPrice=fixPriceMin*优车2.0阻断价最高价系数
            String thPriceMileage = tytConfigService.getStringValue(EXCELLENT_PRICE_THPRICE_MILEAGE);
            if (StringUtils.isNotBlank(thPriceMileage)) {
                carryPriceVo.setThMaxPrice(BigDecimal.valueOf(carryPriceVo.getFixPriceMin()).multiply(new BigDecimal(thPriceMileage)).intValue());
            }
        }
        return carryPriceVo;*/

    }

    @Override
    public CarryPriceVo getThPrice(TransportMain transportMain) {
        TransportCarryBean transportCarryBean = buildCarryBeanByTransport(transportMain);
        return this.getThPrice(transportCarryBean);
    }

    public TransportCarryBean buildCarryBeanByTransport(TransportMain tytTransport) {
        TransportCarryBean transportCarryBean = new TransportCarryBean();
        transportCarryBean.setStartProvince(tytTransport.getStartProvinc());
        transportCarryBean.setStartCity(tytTransport.getStartCity());
        transportCarryBean.setStartArea(tytTransport.getStartArea());
        transportCarryBean.setDestProvince(tytTransport.getDestProvinc());
        transportCarryBean.setDestCity(tytTransport.getDestCity());
        transportCarryBean.setDestArea(tytTransport.getDestArea());
        transportCarryBean.setGoodsName(tytTransport.getTaskContent());
        transportCarryBean.setGoodsWeight(tytTransport.getWeight());
        transportCarryBean.setGoodsLength(tytTransport.getLength());
        transportCarryBean.setGoodsWide(tytTransport.getWide());
        transportCarryBean.setGoodsHigh(tytTransport.getHigh());
        transportCarryBean.setExcellentGoods(tytTransport.getExcellentGoods());
        transportCarryBean.setUserId(tytTransport.getUserId());
        transportCarryBean.setDistance(tytTransport.getDistance() != null ? tytTransport.getDistance().toString() : "0");
        transportCarryBean.setGoodTypeName(tytTransport.getGoodTypeName());

        return transportCarryBean;
    }

    @Override
    public TytExcellentPriceConfig getConfig(TransportCarryBean transportCarryBean) {
        String defaultGoodTypeName = "挖掘机";
        if (StringUtils.isBlank(transportCarryBean.getGoodTypeName())) {
            log.info("优车2.0建议价 获取配置 不具备货类参数");
            transportCarryBean.setGoodTypeName(defaultGoodTypeName);
        }

        //先用参数货类匹配，如果参数货类匹配不上，则用默认货类匹配，如果默认货类还是匹配不上则返回null
        TytExcellentPriceConfig result = null;
        TytExcellentPriceConfig resultStandby = null;

        List<TytExcellentPriceConfig> configs = excellentPriceConfigMapper.getConfig(transportCarryBean);
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }
        for (TytExcellentPriceConfig config : configs) {
            if (StringUtils.isNotBlank(config.getGoodTypeName())) {
                List<String> goodTypeNames = Arrays.asList(config.getGoodTypeName().split(","));
                if (goodTypeNames.contains(transportCarryBean.getGoodTypeName())) {
                    log.info("优车2.0建议价获取到货类匹配的配置 {}", JSON.toJSONString(config));
                    result = config;
                    break;
                }
                if (resultStandby == null && goodTypeNames.contains(defaultGoodTypeName)) {
                    resultStandby = config;
                }
            }
        }
        if (result == null) {
            log.info("优车2.0建议价没获取到货类匹配的配置 {}", JSON.toJSONString(resultStandby));
            return resultStandby;
        }
        return result;
    }

}
