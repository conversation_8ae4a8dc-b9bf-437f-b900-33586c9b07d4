package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.City;
import com.tyt.transport.service.GeographicalLocationService;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Service("geographicalLocationService")
public class GeographicalLocationServiceImpl extends BaseServiceImpl<City, String> implements GeographicalLocationService {
    @Resource(name = "geographicalLocationDao")
    public void setBaseDao(BaseDao<City, String> geographicalLocationDao) {
        super.setBaseDao(geographicalLocationDao);
    }

    @Override
    public List<City> getCityByCityName(String level,String cityName,String type) {

        String sql=null;
        if ("1".equals(type)){
            sql = "select area_name areaName,city_name cityName,province_name province,px,py,map_area_name mapAreaName,map_city_name mapCityName,map_province_name mapProvinceName,longitude,latitude " +
                    "from tyt_city where `level`=? and city_name = ? and px is not null and py is not null";
        }else {
            sql = "select area_name areaName,city_name cityName,province_name province,px,py,map_area_name mapAreaName,map_city_name mapCityName,map_province_name mapProvinceName,longitude,latitude " +
                    "from tyt_city where `level`=? and map_city_name = ? and px is not null and py is not null";
        }

        Object[] params=new Object[] {level,cityName};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("areaName", Hibernate.STRING);
        scalarMap.put("cityName", Hibernate.STRING);
        scalarMap.put("province", Hibernate.STRING);

        scalarMap.put("mapAreaName", Hibernate.STRING);
        scalarMap.put("mapCityName", Hibernate.STRING);
        scalarMap.put("mapProvinceName", Hibernate.STRING);
        scalarMap.put("px", Hibernate.STRING);
        scalarMap.put("py", Hibernate.STRING);
        scalarMap.put("longitude", Hibernate.STRING);
        scalarMap.put("latitude", Hibernate.STRING);
        List<City> search = this.getBaseDao().search(sql, scalarMap, City.class, params);
        return search;
    }

    @Override
    public void addAccidentRecord(String primitiveType, String subtype, String remake,Long userId) {
        String sql="insert into tyt_accident_record (user_id,primitive_type,subtype,remake,create_time) VALUES (?,?,?,?,?)";
        Object[] params =new Object[] {userId,primitiveType,subtype,remake,new Date()};
        this.getBaseDao().executeUpdateSql(sql, params);
    }

    @Override
    public City getCityByPxAndPy(String px, String py) {
        String sql = "select id,area_code areaCode,parent_area_code parentAreaCode,level,rf,longitude,latitude,standard_name standardName,short_name shortName ," +
                "area_name areaName,city_name cityName,province_name province,px,py,type,map_area_name mapAreaName,map_city_name mapCityName,map_province_name mapProvinceName " +
                " from tyt_city where `level`='3' and px=? and py = ?";
        Object[] params=new Object[] {px,py};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("areaName", Hibernate.STRING);
        scalarMap.put("cityName", Hibernate.STRING);
        scalarMap.put("province", Hibernate.STRING);
        scalarMap.put("mapAreaName", Hibernate.STRING);
        scalarMap.put("mapCityName", Hibernate.STRING);
        scalarMap.put("mapProvinceName", Hibernate.STRING);
        scalarMap.put("px", Hibernate.STRING);
        scalarMap.put("py", Hibernate.STRING);
        scalarMap.put("areaCode", Hibernate.STRING);
        scalarMap.put("parentAreaCode", Hibernate.STRING);
        scalarMap.put("level", Hibernate.STRING);
        scalarMap.put("rf", Hibernate.STRING);
        scalarMap.put("longitude", Hibernate.STRING);
        scalarMap.put("latitude", Hibernate.STRING);
        scalarMap.put("shortName", Hibernate.STRING);
        scalarMap.put("standardName", Hibernate.STRING);
        scalarMap.put("type", Hibernate.STRING);
        List<City> search = this.getBaseDao().search(sql, scalarMap, City.class, params);
        if (search!=null &&search.size()>0){

            if(search.size()>1){
                //先使用完全能匹配上地址
                for (City city : search) {
                    if ("1".equals(city.getType())){
                        return city;
                    }
                }
                return search.get(0);
            }else {
                return search.get(0);
            }
        }
        return null;
    }

    @Override
    public List<City> getCityByCityNameAndAreaName(String provinc,String city,String area) {
        String sql = "select id,area_code areaCode,parent_area_code parentAreaCode,level,rf,longitude,latitude,standard_name standardName,short_name shortName ,area_name areaName,city_name cityName,province_name province,px,py from tyt_city " +
                " where `level`='3' and area_name like ? and city_name like ? and province_name like ?";
        Object[] params=new Object[] {area+"%",city+"%",provinc+"%"};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("areaName", Hibernate.STRING);
        scalarMap.put("cityName", Hibernate.STRING);
        scalarMap.put("province", Hibernate.STRING);
        scalarMap.put("px", Hibernate.STRING);
        scalarMap.put("py", Hibernate.STRING);


        scalarMap.put("areaCode", Hibernate.STRING);
        scalarMap.put("parentAreaCode", Hibernate.STRING);
        scalarMap.put("level", Hibernate.STRING);
        scalarMap.put("rf", Hibernate.STRING);
        scalarMap.put("longitude", Hibernate.STRING);
        scalarMap.put("latitude", Hibernate.STRING);
        scalarMap.put("shortName", Hibernate.STRING);
        scalarMap.put("standardName", Hibernate.STRING);
        List<City> search = this.getBaseDao().search(sql, scalarMap, City.class, params);
        return search;
    }
}
