package com.tyt.transport.service.impl;

import com.tyt.model.Transport;
import com.tyt.mybatis.mapper.TransportGoodModelFactorMapper;
import com.tyt.plat.entity.base.TransportGoodModelFactorDO;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.transport.service.TransportGoodModelFactorService;
import com.tyt.util.TransportUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 好中差货模型因子 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Service
@Slf4j
public class TransportGoodModelFactorServiceImpl implements TransportGoodModelFactorService {

    @Autowired
    private TransportGoodModelFactorMapper transportGoodModelFactorMapper;

    /**
     * 运距阈值，单位：公里(km)，用于判断使用总价还是吨公里价
     */
    private final static BigDecimal DISTANCE_THRESHOLD = new BigDecimal(50);


    /**
     * 判断模型等级
     * 1. 满足5个前提规则门槛
     * - a.货源长<=13且宽<=3且高<=3
     * - b.货源价格>0
     * - c.货源吨重>0
     * - d.货源距离>0
     * - e.货源长非空且<>1，且宽非空且<>1，且高非空且<>1
     * 2. 若运距<=50km，则按照总价进行判断，反之按照吨公里价进行判断
     * - 注：价格区间判断时，也是左闭右开区间
     *
     * @return 0不合符
     */
    @Override
    public int judgeModelLevel(Transport transport, TytTransportMainExtend mainExtend) {
        // 不符合前提，返回0
        if (!markingThreshold(transport)) {
            log.info("货源好中差货匹配规则，不符合门槛：hashCode:{}", transport.getHashCode());
            return 0;
        }

        BigDecimal weight = new BigDecimal(transport.getWeight());
        BigDecimal distance = new BigDecimal(transport.getDistance());
        List<TransportGoodModelFactorDO> ruleList = transportGoodModelFactorMapper.matchRules(weight, distance);
        log.info("货源好中差货匹配规则：hashCode:{}, weight:{},distance:{},ruleSize:{}",
                transport.getHashCode(), weight, distance, ruleList.size());

        // 若运距<=50km，则按照总价进行判断，反之按照吨公里价进行判断
        boolean isMatchPrice = distance.compareTo(DISTANCE_THRESHOLD) <= 0;
        BigDecimal price = getActualPrice(transport, mainExtend);
        BigDecimal unitPrice = price.divide(weight.multiply(distance), 6, RoundingMode.HALF_UP);

        int modelLevel = ruleList.stream()
                .filter(rule -> {
                    if (isMatchPrice) {
                        return price.compareTo(rule.getPriceLower()) >= 0
                                && price.compareTo(rule.getPriceUpper()) < 0;
                    } else {
                        return unitPrice.compareTo(rule.getUnitPriceLower()) >= 0
                                && unitPrice.compareTo(rule.getUnitPriceUpper()) < 0;
                    }
                })
                .map(TransportGoodModelFactorDO::getModelLevel)
                .findFirst()
                .orElse(0);
        boolean completeParam = checkTransport(transport);
        modelLevel = completeParam ? modelLevel : modelLevel * -1;
        log.info("货源好中差货匹配结果：hashCode:{}, modelLevel:{}，货参完整:{}", transport.getHashCode(), modelLevel, completeParam);
        return modelLevel;
    }

    /**
     * 运费 = 发货填写的运费 - 不退还定金 + 平台补贴
     */
    private BigDecimal getActualPrice(Transport transport, TytTransportMainExtend mainExtend) {
        // 到手运费 = 发货填写的运费 - 不退还定金 + 平台补贴
        BigDecimal actualPrice = new BigDecimal(transport.getPrice());
        if (Objects.equals(transport.getRefundFlag(), 0) && transport.getInfoFee() != null
                && transport.getInfoFee().compareTo(BigDecimal.ZERO) > 0) {
            actualPrice = actualPrice.subtract(transport.getInfoFee());
        }
        if (mainExtend.getPerkPrice() != null) {
            actualPrice = actualPrice.add(new BigDecimal(mainExtend.getPerkPrice()));
        }
        if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
            actualPrice = BigDecimal.ZERO;
        }
        return actualPrice;
    }

    /**
     * 好中差货打标门槛
     */
    private boolean markingThreshold(Transport transport) {
        return TransportUtil.hasPrice(transport.getPrice())
                && TransportUtil.hasDistance(transport.getDistance());
    }

    /**
     * 判断是否货参（长宽高重）完整
     */
    private boolean checkTransport(Transport transport) {
        return TransportUtil.hasWeight(transport.getWeight())
                && TransportUtil.isValidSize(transport.getLength(), "13")
                && TransportUtil.isValidSize(transport.getWide(), "3")
                && TransportUtil.isValidSize(transport.getHigh(), "3")
                && TransportUtil.isValidSize(transport.getWeight(), "10000");
    }
}
