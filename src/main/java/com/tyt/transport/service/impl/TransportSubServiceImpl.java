package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.TransportNullifyBean;
import com.tyt.model.TransportSubBean;
import com.tyt.transport.dao.TransportNullifyDao;
import com.tyt.transport.service.TransportNullifyService;
import com.tyt.transport.service.TransportService;
import com.tyt.transport.service.TransportSubService;
import com.tyt.transport.service.TransportVaryService;
import com.tyt.user.service.UserService;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import javax.annotation.Resource;

import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 无效货源信息
 * User: Administrator
 * Date: 17-04-24
 * Time: 下午3:16
 * ===========================modify list===================================
 *  modify by tianjw on 20170627   货物自动无效状态修改为撤销状态
 */
@Service("transportSubService")
public class TransportSubServiceImpl extends BaseServiceImpl<TransportSubBean, Long> implements TransportSubService {

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "transportService")
	private TransportService transportService;

	@Resource(name = "transportSubDao")
	public void setBaseDao(BaseDao<TransportSubBean, Long> transportDao) {
		super.setBaseDao(transportDao);
	}

	@Override
	public TransportSubBean getBySrcMsgId(Long originalSrcMsgId) {
		String  sql =" from TransportSubBean where tsId=? ";
		List<TransportSubBean> list = this.getBaseDao().find(sql, originalSrcMsgId);
		return (list!=null&&list.size()>0)?list.get(0):null;
	}

}
