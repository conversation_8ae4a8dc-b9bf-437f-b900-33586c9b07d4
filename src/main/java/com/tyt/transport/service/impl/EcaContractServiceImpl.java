package com.tyt.transport.service.impl;


import com.alibaba.fastjson.JSON;
import com.tyt.azt.ContractTemplate;
import com.tyt.azt.PDFSignPrivateSeal;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.common.service.TytSequenceService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.ShortMsgBean;
import com.tyt.message.bean.GenerateShortUrlUtil;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.CertIdentityBean;
import com.tyt.transport.service.*;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import com.tyt.verificationcode.bean.SendMsgResultBean;
import com.tyt.verificationcode.service.VerificationCodeService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Service("ecaContractService")
public class EcaContractServiceImpl extends BaseServiceImpl<EcaContract, Long> implements EcaContractService {

    public static Logger logger = LoggerFactory.getLogger(EcaContractServiceImpl.class);

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "carService")
    private CarService carService;

    @Resource(name = "tytMessageTmplService")
    private TytMessageTmplService messageTmplService;

    @Resource(name = "userIdentityCertService")
    private UserIdentityCertService userIdentityCertService;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityAuthService;

    @Resource(name = "ecaProgressService")
    EcaProgressService ecaProgressService;

    @Resource(name = "ecaUserProgressService")
    EcaUserProgressService ecaUserProgressService;

    @Resource(name = "tytSequenceService")
    private TytSequenceService tytSequenceService;

    @Resource(name = "tytCarOwnerIntentionService")
    private TytCarOwnerIntentionService tytCarOwnerIntentionService;

    @Resource(name = "transportMtService")
    TransportMtService transportMtService;
    
    @Resource(name = "transportMainService")
    TransportMainService transportMainService;

    @Resource(name = "verificationCodeService")
    private VerificationCodeService verificationCodeService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "tytMessageTmplService")
    private TytMessageTmplService tytMessageTmplService;

    @Resource(name = "employeeMessageService")
    private EmployeeMessageService employeeMessageService;

    private static String ECA_SEQUENCE_NAME = "ecacontract";
    private static String ECACONTRACT_VERIF_TEMPLATE_CODE="SMS_123669071";


    @Resource(name = "ecaContractDao")
    public void setBaseDao(BaseDao<EcaContract, Long> ecaContractDao) {
        super.setBaseDao(ecaContractDao);
    }

    @Override
    public EcaContract getEcaContractBySrcMsgId(Long SrcMsgId) {
        List<EcaContract> ecaContracts = this.getBaseDao().find("from EcaContract t where t.srcMsgId = ? and t.status < 4 ", SrcMsgId);
        return (ecaContracts != null && ecaContracts.size() > 0) ? ecaContracts.get(0) : null;
    }

    @Override
    public EcaContract getContractByUserId(Long userId) {
        List<EcaContract> ecaContract = this.getBaseDao().find("from EcaContract t where shipperUserId=? and status=3 order by ctime desc", userId);
        List<EcaContract> contract = this.getBaseDao().find("from EcaContract t where carryUserId=? and status=3 order by ctime desc", userId);
        if (ecaContract != null && ecaContract.size()>0) {
            return ecaContract.get(0);
        } else if (contract != null && contract.size()>0) {
            return contract.get(0);
        } else {
            return null;
        }
    }

    @Override
    public void updateCancelContract(EcaContract ecaContract, String userId, String userName) throws Exception {
        //作废合同后初始一个新合同
        this.update(ecaContract);
        TransportMt transportMt = transportMtService.getTransportMtBySrcMsgId(ecaContract.getSrcMsgId());
        if (transportMt != null) {
            //初始化合同
            Long ecaContractId = insertEcaConTract(transportMt, userId, userName);
            ecaProgressService.insertEcaProgress(userId, userName, ecaContractId);
            //修改 作废合同的创建时间为合同的创建时间
            ecaUserProgressService.insertEcaUserProgressWithDate(ecaContractId, transportMt.getSrcMsgId(), 1,ecaContract.getCtime());
            //查询是否存在装货完成记录
            EcaUserProgress ecaUserProgress = new EcaUserProgress();
            ecaUserProgress.setContractId(ecaContract.getId());
            ecaUserProgress.setType(2);
            EcaUserProgress ecaUserProgresses = ecaUserProgressService.find(ecaUserProgress);
            if(ecaUserProgresses != null){
            	//修改 作废合同的进度装货时间为合同的装货时间
                ecaUserProgressService.insertEcaUserProgressWithDate(ecaContractId, transportMt.getSrcMsgId(), 2,ecaUserProgresses.getCtime());
            }
        }
    }

    public Long insertEcaConTract(TransportMt transportMtNew,
                                  String userId, String userName) throws Exception {
        EcaContract ecaContract = new EcaContract();
        ecaContract.setSrcMsgId(transportMtNew.getSrcMsgId());
        //生成合同编号
        ecaContract.setContractNum(tytSequenceService.updateGetNumberForDate(ECA_SEQUENCE_NAME));
        //初始化托运方信息
        if(transportMtNew.getUserId()!=null && transportMtNew.getUserId()>1){
            User userInfo = userService.getUserByCellphone(transportMtNew.getShipperPhone());
            ecaContract.setShipperUserId(userInfo != null ? userInfo.getId() : null);
        }
        ecaContract.setShipperPhone(transportMtNew.getShipperPhone());
        //初始化承运方信息
        if(transportMtNew.getFirstPayUserId()!=null && transportMtNew.getFirstPayUserId()>1){
            ecaContract.setCarryUserId(transportMtNew.getFirstPayUserId());
            User userInfo = userService.getById(transportMtNew.getFirstPayUserId());
            ecaContract.setCarryPhone(userInfo != null ? userInfo.getCellPhone() : null);
        }
      //加载main标的tsOrderNo
  		TransportMain main = transportMainService.getTransportMainForId(transportMtNew.getSrcMsgId());
  		if(main!=null){
  			ecaContract.setTsOrderNo(main.getTsOrderNo());	
  		}else{
  			ecaContract.setTsOrderNo(transportMtNew.getTsOrderNo());
  			}
        ecaContract.setPrice(transportMtNew.getStrikePrice());
        ecaContract.setStartPoint(transportMtNew.getStartPoint());
        ecaContract.setStartDetailAdd(transportMtNew.getStartDetailAdd());
        ecaContract.setDestDetailAdd(transportMtNew.getDestDetailAdd());
        ecaContract.setDestPoint(transportMtNew.getDestPoint());
        ecaContract.setTaskContent(transportMtNew.getTaskContent());
        ecaContract.setStatus(1);
        ecaContract.setCreateUserId(Long.valueOf(userId));
        ecaContract.setCreateUserName(userName);
        ecaContract.setMtId(transportMtNew.getId());
        ecaContract.setCtime(new Date());
        ecaContract.setMtime(new Date());
        ecaContract.setShipperSignStatus(1);
        ecaContract.setCarrySignStatus(1);
        //TODO 加载运输牌照
        TytCarOwnerIntention tytCarOwnerIntention = tytCarOwnerIntentionService.getBySrcMsgIdAndCaruserId(transportMtNew.getSrcMsgId(), transportMtNew.getFirstPayUserId());
        if (tytCarOwnerIntention != null && tytCarOwnerIntention.getCarId() != null) {
            Car car = carService.getById(tytCarOwnerIntention.getCarId());
            ecaContract.setHeadLicensePlate(car.getHeadCity() + car.getHeadNo());
            ecaContract.setTailLicensePlate(car.getTailCity() + car.getTailNo());
        }
        Long ecaContractId = (Long) this.getBaseDao().insertWithReturnId(ecaContract);
        return ecaContractId;
    }

    @Override
    public void userAgreeAffix(Long contractId, Long userId, ResultMsgBean rm) {
        Map<String, Object> result = new HashMap<String, Object>();
        String otherPhone = ""; // 对方用户手机号
        String otherUserIdentify = ""; // 对方用户身份
        String otherUserName = ""; // 对方用户名称
        String currentPhone = ""; // 当前用户手机号
        // 查询合同信息
        EcaContract contract = this.getById(contractId);
        EcaContract ecaContract = new EcaContract();
        BeanUtils.copyProperties(contract, ecaContract);

        if (ecaContract.getShipperUserId()==null || ecaContract.getCarryUserId()==null) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("对方尚未实名认证，请耐心等待");
            result.put("phone", otherPhone);
            result.put("tooltip", true); // 弹出对话框
            rm.setData(result);
            return;
        }
        // 补充合同内容
        CertIdentityBean shipperBean = userIdentityCertService.getCertIdentity(ecaContract.getShipperUserId());
        if (shipperBean!=null) {
            ecaContract.setShipperUserName(shipperBean.getUserName());
            ecaContract.setShipperIdCard(shipperBean.getIdCard());
            ecaContract.setShipperPhone(shipperBean.getCellPhone());
            ecaContract.setShipperType(shipperBean.getIdentityType());
            if (shipperBean.getIdentityType()==2) {
                ecaContract.setShipperName(shipperBean.getComName());
            }
        }
        CertIdentityBean carryBean = userIdentityCertService.getCertIdentity(ecaContract.getCarryUserId());
        if (carryBean!=null) {
            ecaContract.setCarryUserName(carryBean.getUserName());
            ecaContract.setCarryIdCard(carryBean.getIdCard());
            ecaContract.setCarryPhone(carryBean.getCellPhone());
        }
        if (userId.equals(ecaContract.getShipperUserId())) {
            // 当前人手机号
            currentPhone = ecaContract.getShipperPhone();
            // 如果是托运方获取承运方电话
            otherPhone = ecaContract.getCarryPhone();
            otherUserIdentify = "承运方";
            otherUserName = ecaContract.getCarryUserName();
        } else if (userId.equals(ecaContract.getCarryUserId())) {
            // 当前人手机号
            currentPhone = ecaContract.getCarryPhone();
            // 如果是承运方获取托运方电话
            otherPhone = ecaContract.getShipperPhone();
            otherUserIdentify = "托运方";
            otherUserName = ecaContract.getShipperUserName();
        }
        if ((shipperBean.getIdCard() != null || carryBean.getIdCard() != null)
                && StringUtils.equals(shipperBean.getIdCard(), carryBean.getIdCard())) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("您不能与自己签订合同，请联系客服");
            return;
        }

        // 查看双方是否均已实名认证
        Boolean isCert = userIdentityCertService.checkContractIdentity(ecaContract);
        // 双方均已实名认证
        if (isCert) {
//            System.out.println("otherUserName="+otherUserName +" otherUserIdentify="+otherUserIdentify + " ecaContract.getContractNum()="+ecaContract.getContractNum());
            SendMsgResultBean smrb = new SendMsgResultBean();
            // 调用短信平台发送短信
//            String messageTemplate = "您在特运通与${userIdentify}${userName}就编号为${contractNum}的运输合同进行签约，校验码${verifyCode}，请勿告知他人";
//            String messageTemplate = "(${verifyCode})电子合同签名验证码，请勿泄漏";
//            Map<String, Object> data = new HashMap<>();
//            data.put("userIdentify", otherUserIdentify);
//            data.put("userName", otherUserName);
//            data.put("contractNum", ecaContract.getContractNum());
//            data.put("verifyCode", "${verifyCode}"); //保持原有,FreemarkerUtil需兼容系统二次替换
//            messageTemplate = FreemarkerUtil.render(data, messageTemplate);

            String messageTemplate = tytMessageTmplService.getSmsTmpl("sms.ecacontract.verify.code");

            ShortMessageBean shortMessageBean = verificationCodeService.sendVerifySms(currentPhone, messageTemplate, ECACONTRACT_VERIF_TEMPLATE_CODE);
            String verifyCode = shortMessageBean.getVerifyCode();

            smrb.setCode(ReturnCodeConstant.OK);
            smrb.setMsg("短信发送成功");
            smrb.setVerificationCode(MD5Util.GetMD5Code(verifyCode));
            // 发送短信
            result.put("phone", StringUtil.replaceMobile(currentPhone));
        } else {
            if(userId.equals(ecaContract.getShipperUserId())) { // 我是货方
                if(userIdentityCertService.checkUserIdentityCert(ecaContract.getShipperUserName(), ecaContract.getShipperIdCard())) {
                    rm.setCode(ReturnCodeConstant.ERROR);
                    rm.setMsg("对方尚未实名认证，请耐心等待");
                    result.put("phone", otherPhone);
                    result.put("tooltip", true); // 弹出对话框
                } else {
                    rm.setCode(ReturnCodeConstant.ERROR);
                    rm.setMsg("您的实名认证信息有误，请联系客服");
                }
            } else if (userId.equals(ecaContract.getCarryUserId())){ // 我是车方
                if(userIdentityCertService.checkUserIdentityCert(ecaContract.getCarryUserName(), ecaContract.getCarryIdCard())) {
                    rm.setCode(ReturnCodeConstant.ERROR);
                    rm.setMsg("对方尚未实名认证，请耐心等待");
                    result.put("phone", otherPhone);
                    result.put("tooltip", true); // 弹出对话框
                } else {
                    rm.setCode(ReturnCodeConstant.ERROR);
                    rm.setMsg("您的实名认证信息有误，请联系客服");
                }
            }
        }
        rm.setData(result);
    }

    @Override
    public EcaContract userDoAffix(Long contractId, Long userId, String identifyingCode, ResultMsgBean rm) {
        Map<String, Object> result = new HashMap<String, Object>();
        // 查询合同信息
        EcaContract contract = this.getById(contractId);
        EcaContract ecaContract = new EcaContract();
        BeanUtils.copyProperties(contract, ecaContract);
        // 获取合同内人员信息
        CertIdentityBean shipperBean = userIdentityCertService.getCertIdentity(ecaContract.getShipperUserId());
        if (shipperBean!=null) {
            ecaContract.setShipperUserName(shipperBean.getUserName());
            ecaContract.setShipperIdCard(shipperBean.getIdCard());
            ecaContract.setShipperPhone(shipperBean.getCellPhone());
            ecaContract.setShipperType(shipperBean.getIdentityType());
            if (shipperBean.getIdentityType()==2) {
                ecaContract.setShipperName(shipperBean.getComName());
            }
        }
        CertIdentityBean carryBean = userIdentityCertService.getCertIdentity(ecaContract.getCarryUserId());
        if (carryBean!=null) {
            ecaContract.setCarryUserName(carryBean.getUserName());
            ecaContract.setCarryIdCard(carryBean.getIdCard());
            ecaContract.setCarryPhone(carryBean.getCellPhone());
        }
        // 设定自己的手机号和对方的手机号,给对方发短信和自己校验验证码使用
        String otherPhone = ""; // 对方用户手机号
        String otherUserIdentify = ""; // 对方用户身份
        String otherUserName = ""; // 对方用户名称
        Long otherUserId = null; // 对方用户ID
        String currentPhone = ""; // 当前用户手机号
        String currentUserName="";//当前用户名称
        String currentUserIdentify="";//当前用户身份
        if (userId.equals(ecaContract.getShipperUserId())) {
            // 设定托运方签约状态
            ecaContract.setShipperSignStatus(2);
            ecaContract.setShipperSignTime(new Date());
            // 当前人手机号
            currentPhone = ecaContract.getShipperPhone();
            currentUserIdentify = "托运方";
            currentUserName = ecaContract.getShipperUserName();
            // 如果是托运方获取承运方电话
            otherPhone = ecaContract.getCarryPhone();
            otherUserIdentify = "承运方";
            otherUserName = ecaContract.getCarryUserName();
            otherUserId = ecaContract.getCarryUserId();
        } else if (userId.equals(ecaContract.getCarryUserId())) {
            // 设定托运方签约状态
            ecaContract.setCarrySignStatus(2);
            ecaContract.setCarrySignTime(new Date());
            // 当前人手机号
            currentPhone = ecaContract.getCarryPhone();
            currentUserIdentify = "承运方";
            currentUserName = ecaContract.getCarryUserName();
            // 如果是承运方获取托运方电话
            otherPhone = ecaContract.getShipperPhone();
            otherUserIdentify = "托运方";
            otherUserName = ecaContract.getShipperUserName();
            otherUserId = ecaContract.getShipperUserId();
        }
        //0. 自己不能与自己签订合同
        if ((shipperBean.getIdCard() != null || carryBean.getIdCard() != null)
                && StringUtils.equals(shipperBean.getIdCard(), carryBean.getIdCard())) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("您不能与自己签订合同，请联系客服");
            return ecaContract;
        }
        //1. 验证验证码是否正确
        String smsVerifyCode = RedisUtil.get(Constant.SMS_VERIFYCODE_PREFFIX + currentPhone);
        if(StringUtils.isEmpty(smsVerifyCode)){
            rm.setCode(ReturnCodeConstant.VERIFICATION_CODE_TIMEOUT);
            rm.setMsg("验证码超时或者不存在");
            return ecaContract;
        }
        if(!smsVerifyCode.equals(identifyingCode)){
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("验证码错误");
            return ecaContract;
        }
        //2. 查看双方是否均已实名认证
        Boolean isCert = userIdentityCertService.checkContractIdentity(ecaContract);
        if(!isCert) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("对方尚未实名认证");
            return ecaContract;
        }
        //2.5 如果合同已作废或已签订，不能再继续签约
        if(ecaContract.getStatus() != null && ecaContract.getStatus().intValue() > 2) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("您的合同已经不能进行签订，请联系客服");
            if(ecaContract.getStatus().intValue() == 3) {
                rm.setMsg("您的合同已经签订，不需再次签订");
            } else if (ecaContract.getStatus().intValue() == 4){
                rm.setMsg("您的合同已经作废，如需签订，请使用最新合同");
            }
            return ecaContract;
        }
        //3. 查看合同当前状态是否已双方签订，更新用户信息到合同表
        if(ecaContract.getShipperSignStatus().equals(2) && ecaContract.getCarrySignStatus().equals(2)) {
            ecaContract.setStatus(3); // 如果双方均签约成功，则状态为 3-已生效
        } else {
            ecaContract.setStatus(2); // 否则状态为 2-签约中
        }
        ecaContract.setMtime(new Date());
        //4. 获取模板，生成PDF文件，如果合同模板PDF已经生成，则不需再重新创建
        if(StringUtils.isBlank(ecaContract.getBlankPdfPath())) {
            String pdfPath = ContractTemplate.createContractPDF(ecaContract);
            ecaContract.setBlankPdfPath(pdfPath);
            if(StringUtils.isBlank(pdfPath)) {
                throw new RuntimeException("文件创建失败");
            }
        }
        //5. PDF文件签章
        String signedPdfPath = PDFSignPrivateSeal.pdfSign(userId, ecaContract);
        ecaContract.setSignPdfPath(signedPdfPath);
        if(StringUtils.isBlank(signedPdfPath)) {
            throw new RuntimeException("文件Ca签章失败");
        }
        // 5.1 把复制的对象copy回来，才能更新，hib的机制
        BeanUtils.copyProperties(ecaContract, contract);
        this.updateContract(contract);
        //6. 根据条件下发短信
        this.sendDoAffixSms(ecaContract.getSrcMsgId().toString(), ecaContract.getStatus(),
                otherPhone, otherUserIdentify, otherUserName, ecaContract.getTaskContent(), ecaContract.getId(), otherUserId);
        //7. 同时发送站内短信
        //获取人工派单货源的跟单人，如果不存在则取合同的创建人
        TransportMt transportMt = transportMtService.getTransportMtBySrcMsgId(ecaContract.getSrcMsgId());
        Long createUserId = null;
        String createUserName = null;
        if(transportMt != null){
            createUserId = transportMt.getTracerUserId();
            createUserName = transportMt.getTracerName();
        }
        if(createUserId == null){
            createUserId = ecaContract.getCreateUserId();
            createUserName = ecaContract.getCreateUserName();
        }
        employeeMessageService.saveEmpMessage(ecaContract.getMtId(), ecaContract.getSrcMsgId(),
                ecaContract.getTaskContent(), ecaContract.getStatus(), currentUserIdentify, currentUserName, createUserId,createUserName);
        //8. 回填对方手机号，前端拨打电话使用
        rm.setCode(ReturnCodeConstant.OK);
        if(ecaContract.getStatus().intValue() == 2) {
            rm.setMsg("等待对方签名，双方签名后合同生效");
            result.put("phone", otherPhone);
            result.put("tooltip", true); // 弹出对话框
        } else {
            rm.setMsg("双方签名成功，合同生效");
            result.put("tooltip", true); // 弹出对话框
        }
        rm.setData(result);
        return ecaContract;
    }



	//6. 根据条件下发短信
	public void sendDoAffixSms(String srcMsgId,Integer contractStatus,String cellPhone,String userIdentify,String userName,String taskContent, Long contractId, Long userId){
        String url = tytConfigService.getStringValue("tyt_sms_start_app_url");
        String serverDomain = tytConfigService.getStringValue("h5app.server.domain","http://www.teyuntong.cn");
        String certUri = tytConfigService.getStringValue("identity.cert.h5.uri","/app/contractNet/certificate.html");
        String redirectUrl = serverDomain + certUri;
        String params = null;
        try {
            params = "typeCode=7001&status=1&contractId=" + contractId + "&userId=" + userId + "&redirectUrl=" + URLEncoder.encode(redirectUrl, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //拼接业务参数生成短链接
        url = GenerateShortUrlUtil.generateShortUrl(url + "?" + params);
        if (taskContent.length() > 8) {
            taskContent = taskContent.substring(0, 8) + "...";
        }
        //单方签约成功
        if(contractStatus.intValue() == 2){
            //要替换的参数列表
            String[] keyArray = new String[] { "${userIdentify}", "${userName}", "${taskContent}","${url}" };
            //要替换成的值
            String[] valueArray = new String[4];
            valueArray[0] = userIdentify; //顺序和参数列表对应即可
            valueArray[1] = userName;
            valueArray[2] = taskContent;
            valueArray[3] = url;
            //短信模版，参考数据库  sms.ecacontract.one.smstip  sms.ecacontract.all.smstip
            String message = tytMessageTmplService.getSmsTmpl("sms.ecacontract.one.smstip");
            //定义好参数列表，替换值列表，生成URL，货物message模版，
            sendSms(cellPhone, valueArray, keyArray, message);
        }else if(contractStatus.intValue() == 3){   //双方都签约成功
            //要替换的参数列表
            String[] keyArray1 = new String[] { "${taskContent}","${url}" };
            //要替换成的值
            String[] valueArray1 = new String[2];
            //拼接端连接
            valueArray1[0] = taskContent;
            valueArray1[1] = url;
            //短信模版，参考数据库  sms.ecacontract.one.smstip  sms.ecacontract.all.smstip
            String message1 = tytMessageTmplService.getSmsTmpl("sms.ecacontract.all.smstip");
            //定义好参数列表，替换值列表，生成URL，货物message模版，
            sendSms(cellPhone, valueArray1, keyArray1, message1);
        }else{
            logger.info("当前合同状态不需发送通知短信，srcMsgId：{}，contractStatus:{}",srcMsgId,contractStatus);
        }
	}

	//发送短信接口
	private void sendSms(String cellPhone, String[] valueArray, String[] keyArray, String message) {
		//将对应参数替换成对应的值
		message = StringUtils.replaceEach(message, keyArray, valueArray);
		ShortMsgBean shortMsgBean = new ShortMsgBean();
		shortMsgBean.setContent(message);
		shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
		String messageSerailNum = SerialNumUtil.generateSeriaNum();
		shortMsgBean.setMessageSerailNum(messageSerailNum);
		shortMsgBean.setCell_phone(cellPhone);
		shortMsgBean.setRemark("");
		tytMqMessageService.addSaveMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
		tytMqMessageService.sendMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean),MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
	}
	
	@Override
	public void updateShipperUserId(Long shipperUserId,Long contractId){
		String sql="update eca_contract set shipper_user_id=? where id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[]{shipperUserId,contractId});
	}
	
	private void updateContract(EcaContract eca){
	    this.update(eca);
//		String sql="update eca_contract set shipper_name=?, shipper_type=?, shipper_phone=?, "
//				+ "shipper_user_name=?, shipper_id_card=?, shipper_sign_time=?, "
//				+ "shipper_sign_status=?, carry_user_name=?, carry_id_card=?, carry_phone=?, "
//				+ "carry_sign_time=?, carry_sign_status=?, status=?, mtime=? where id=?";
//		this.getBaseDao().executeUpdateSql(sql, new Object[]{eca.getShipperName(),eca.getShipperType(),eca.getShipperPhone(),
//				eca.getShipperUserName(),eca.getShipperIdCard(),eca.getShipperSignTime(),eca.getShipperSignStatus(),
//				eca.getCarryUserName(),eca.getCarryIdCard(),eca.getCarryPhone(),eca.getCarrySignTime(),
//				eca.getCarrySignStatus(),eca.getStatus(),eca.getMtime(),eca.getId()});
	}

}
