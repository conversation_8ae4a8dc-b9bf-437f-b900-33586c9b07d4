package com.tyt.transport.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytMatchesClick;
import com.tyt.transport.service.MatchesClickService;

@Service("matchesClickService")
public class MatchesClickServiceImpl extends BaseServiceImpl<TytMatchesClick, Long> implements MatchesClickService {

	@Resource(name = "matchesClickDao")
	public void setBaseDao(BaseDao<TytMatchesClick, Long> matchesClickDao) {
		super.setBaseDao(matchesClickDao);
	}
}
