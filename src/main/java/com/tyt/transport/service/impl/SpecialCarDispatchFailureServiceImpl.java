package com.tyt.transport.service.impl;

import com.tyt.model.TransportMain;
import com.tyt.plat.entity.base.TytSpecialCarDispatchFailure;
import com.tyt.plat.mapper.base.TytSpecialCarContactRecordMapper;
import com.tyt.plat.mapper.base.TytSpecialCarDispatchFailureMapper;
import com.tyt.transport.service.SpecialCarDispatchFailureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;


/**
 * 专车派单失败货源列表相关
 *
 * <AUTHOR>
 * @since 2024-08-12 17:15
 */
@Component
@Slf4j
public class SpecialCarDispatchFailureServiceImpl implements SpecialCarDispatchFailureService {

    @Autowired
    private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;
    @Autowired
    private TytSpecialCarContactRecordMapper tytSpecialCarContactRecordMapper;

    /**
     * 工单状态：已结单
     */
    private static final int COMPLETED = 2;

    /**
     * 处理工单状态
     *
     * @param main
     */
    @Override
    @Async(value = "mqExecutor")
    public void processWorkOrderStatus(TransportMain main) {
        log.info("修改专车派单失败货源工单状态, srcMsgId:{}", main.getSrcMsgId());
        TytSpecialCarDispatchFailure failure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(main.getSrcMsgId());
        if (Objects.isNull(failure)) {
            log.info("修改专车派单失败货源工单状态，不用处理，srcMsgId:{}", main.getSrcMsgId());
            return;
        }
        int count = tytSpecialCarContactRecordMapper.countBySrcMsgId(main.getSrcMsgId());
        if (count > 0) {
            log.info("修改专车派单失败货源工单状态，srcMsgId:{}", main.getSrcMsgId());
            // 添加过沟通记录，处理工单状态为已结单，更新结单时间
            tytSpecialCarDispatchFailureMapper.updateWorkOrderStatus(main.getSrcMsgId(), COMPLETED, new Date());
        }
    }
}
