package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.deposit.service.TytGoodsRefreshUserService;
import com.tyt.infofee.bean.InfoFeeMyPublishGoodsResultBean;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.TransportDealAfterBean;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.message.service.TytPushUserService;
import com.tyt.message.service.TytUserNotifyService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.TransportDone;
import com.tyt.model.TransportMain;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.plat.entity.base.TytTransportEnterpriseLog;
import com.tyt.plat.mapper.base.TytInvoiceEnterpriseMapper;
import com.tyt.plat.utils.CityUtil;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.TransportDoneListBean;
import com.tyt.transport.querybean.TransportDoneRequest;
import com.tyt.transport.service.TransportDoneService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SuppressWarnings("deprecation")
@Service("transportDoneService")
public class TransportDoneServiceImpl extends BaseServiceImpl<TransportDone, Long> implements TransportDoneService {
    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;
    @Resource(name = "transportOrdersService")
    TransportOrdersService transportOrdersService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "carService")
    private CarService carService;
    @Resource(name = "tytMqMessageService")
    TytMqMessageService tytMqMessageService;
    @Resource(name = "tytMessageTmplService")
    private TytMessageTmplService messageTmplService;

    @Resource(name = "tytUserSubService")
    private TytUserSubService tytUserSubService;
    @Resource(name = "tytPushUserService")
    private TytPushUserService tytPushUserService;
    @Resource(name = "tytUserNotifyService")
    private TytUserNotifyService tytUserNotifyService;
    @Autowired
    private BackendTransportMapper backendTransportMapper;

    @Autowired
    private TransportBusiness transportBusiness;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

    @Resource(name = "transportDoneDao")
    @Override
    public void setBaseDao(BaseDao<TransportDone, Long> transportDoneDao) {
        super.setBaseDao(transportDoneDao);
    }

    @Override
    public TransportDone getByTsId(Long tsId) {
        final String sql = "select * from tyt_transport_done where ts_id = ? ";
        final Object[] params = {
                tsId
        };
        List<TransportDone> list = this.getBaseDao().search(sql, params, 0, 1);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<TransportDoneListBean> getMyTransportDone(Long userId, Integer queryActionType, Long queryID,String clientSign) {
        /* 信息费我的货源列表查询天数，后台配置 */
        Integer infoFeeMyPublishDays = tytConfigService.getIntValue("infoFeeMyPublishDays", 30);
        String startDateStr = TimeUtil.formatDate(TimeUtil.dateDiff(0 - infoFeeMyPublishDays));

        StringBuilder selectSQL = new StringBuilder();// SQL查询内容

        selectSQL.append("SELECT d.id, d.ts_id tsId, d.user_id userId, d.nick_name nickName, d.cell_phone cellPhone, d.start_point startPoint, d.dest_point destPoint, d.task_content taskContent, " +
                "d.ts_order_no tsOrderNo, d.publish_time publishTime, d.car_id carId, d.head_city headCity, d.head_no headNo, d.tail_city tailCity, d.tail_no tailNo, d.carry_user_id carryUserId, " +
                "d.carry_cell_phone carryCellPhone, m.refund_flag refundFlag, d.carry_name carryName, d.is_change_car isChangeCar, d.is_allow_location isAllowLocation, d.ctime ctime, d.mtime mtime, " +
                "m.loading_time loadingTime, m.unload_time unloadTime,m.begin_loading_time beginLoadingTime, m.begin_unload_time beginUnloadTime, m.remark remark, m.price price, d.deal_price dealPrice," +
                "m.shunting_quantity AS shuntingQuantity,m.publish_type AS publishType,m.info_fee AS infoFee,m.label_json as labelJson,m.excellent_goods as excellentGoods,m.machine_remark as machineRemark,m.excellent_card_id as excellentCardId" +
                ",m.invoice_transport as invoiceTransport,m.start_longitude startLongitude, m.start_latitude startLatitude, m.dest_longitude destLongitude, m.dest_latitude destLatitude,m.start_detail_add startDetailAdd," +
                "m.dest_detail_add destDetailAdd,distance \n" +
                "FROM  tyt_transport_done d LEFT JOIN tyt_transport_main m ON d.ts_id = m.id WHERE d.user_id=? and d.ctime>? and m.is_delete = 0");
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("tsId", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("nickName", Hibernate.STRING);
        scalarMap.put("cellPhone", Hibernate.STRING);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("publishTime", Hibernate.TIMESTAMP);
        scalarMap.put("carId", Hibernate.LONG);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        scalarMap.put("carryUserId", Hibernate.LONG);
        scalarMap.put("carryCellPhone", Hibernate.STRING);
        scalarMap.put("refundFlag",Hibernate.INTEGER);
        scalarMap.put("carryName", Hibernate.STRING);
        scalarMap.put("isChangeCar", Hibernate.INTEGER);
        scalarMap.put("isAllowLocation", Hibernate.INTEGER);
        scalarMap.put("ctime", Hibernate.TIMESTAMP);
        scalarMap.put("mtime", Hibernate.TIMESTAMP);
        scalarMap.put("loadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("unloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("beginLoadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("beginUnloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("remark", Hibernate.STRING);
        scalarMap.put("price", Hibernate.STRING);
        scalarMap.put("dealPrice", Hibernate.BIG_DECIMAL);
        scalarMap.put("shuntingQuantity",Hibernate.INTEGER);
        scalarMap.put("publishType",Hibernate.INTEGER);
        scalarMap.put("infoFee",Hibernate.BIG_DECIMAL);
        scalarMap.put("labelJson",Hibernate.STRING);
        scalarMap.put("excellentGoods",Hibernate.INTEGER);
        scalarMap.put("machineRemark",Hibernate.STRING);
        scalarMap.put("excellentCardId",Hibernate.LONG);
        scalarMap.put("invoiceTransport",Hibernate.INTEGER);
        scalarMap.put("startLongitude", Hibernate.STRING);
        scalarMap.put("startLatitude", Hibernate.STRING);
        scalarMap.put("destLongitude", Hibernate.STRING);
        scalarMap.put("destLatitude", Hibernate.STRING);
        scalarMap.put("startDetailAdd", Hibernate.STRING);
        scalarMap.put("destDetailAdd", Hibernate.STRING);
        scalarMap.put("distance", Hibernate.STRING);
        List<Object> params = new ArrayList<Object>();

        params.add(userId);
        params.add(startDateStr);
        if (queryActionType == 1) {
            if (queryID > 0) {
                selectSQL.append(" and d.id>?");
                params.add(queryID);
            }
        } else if (queryActionType == 2) {
            selectSQL.append(" and d.id<?");
            params.add(queryID);
        }
        /* 信息费我的货源列表查询条数，后台配置 */
        Integer infoFeeMyPublishQuerySize = null;
        if(null != clientSign && clientSign.equals("1")){
            infoFeeMyPublishQuerySize = tytConfigService.getIntValue("infoFeeMyPublishQueryPcSize");
            // 如果是pc端，过滤掉优车货源
            selectSQL.append(" and m.excellent_goods != 1");
        }else {
            infoFeeMyPublishQuerySize = tytConfigService.getIntValue("infoFeeMyPublishQuerySize");
        }

        selectSQL.append(" order by d.ctime desc,d.id desc limit ?");
        params.add(infoFeeMyPublishQuerySize);


        List<TransportDoneListBean> doneList = this.getBaseDao().search(
                selectSQL.toString(), scalarMap, TransportDoneListBean.class,
                params.toArray());


        /*StringBuilder selectSQL = new StringBuilder();// SQL查询内容
        //我的货源 - 已成交
        selectSQL.append("select * from tyt_transport_done where user_id=? and ctime>? ");
        params.add(userId);
        params.add(startDateStr);
        if (queryActionType == 1) {
            if (queryID > 0) {
                selectSQL.append(" and id>?");
                params.add(queryID);
            }
        } else if (queryActionType == 2) {
            selectSQL.append(" and id<?");
            params.add(queryID);
        }
        selectSQL.append(" order by id desc limit ?");
        params.add(infoFeeMyPublishQuerySize);

        List<TransportDone> doneList = this.getBaseDao().queryForList(selectSQL.toString(), params.toArray());*/
        //List<TransportDoneListBean> doneListBeans = new ArrayList<>();
        List<Long> ids=new ArrayList<>();
        for (TransportDoneListBean transportDone : doneList) {
            ids.add(transportDone.getTsId());
            //TransportDoneListBean listBean = new TransportDoneListBean();
            //BeanUtils.copyProperties(transportDone,listBean);
            boolean bexists = RedisUtil.existsObject(Constant.CACHE_EXPIRE_LOCATION_APPLY + transportDone.getTsId());
            if(bexists && transportDone.getIsAllowLocation() == 2) {
                //状态设置为申请中状态
                transportDone.setIsAllowLocation(3);
            }
            //doneListBeans.add(listBean);

            //单独处理距离，库里存的12300，实际123km
            if (org.apache.commons.lang3.StringUtils.isNotBlank(transportDone.getDistance())) {
                transportDone.setDistance(CityUtil.toDistanceStr(Integer.parseInt(transportDone.getDistance())));
            }
            transportDone.setStartLongitude(CityUtil.toMapPointStr(transportDone.getStartLongitude()));
            transportDone.setStartLatitude(CityUtil.toMapPointStr(transportDone.getStartLatitude()));
            transportDone.setDestLongitude(CityUtil.toMapPointStr(transportDone.getDestLongitude()));
            transportDone.setDestLatitude(CityUtil.toMapPointStr(transportDone.getDestLatitude()));

        }
        if (ids.size()>0){
            List<Long> longs = backendTransportMapper.selectMsgIdsByMsgId(ids);
            for (Long tsId : longs) {
                for (TransportDoneListBean doneListBean : doneList) {
                    if (doneListBean.getTsId().equals(tsId)){
                        doneListBean.setIsBackendTransport(1);
                    }
                }
            }
        }

        //已完单货源列表新增展示优车好货（秒抢货源）刷新结束文案，不管是不是刷新结束直接展示
        makeInstantGrabData(doneList);

        makeInvoiceTransportData(doneList);

        return doneList;
    }

    private void makeInvoiceTransportData(List<TransportDoneListBean> doneList) {
        for (TransportDoneListBean transportDoneListBean : doneList) {
            if (transportDoneListBean != null && transportDoneListBean.getInvoiceTransport() != null && transportDoneListBean.getInvoiceTransport() == 1) {
                TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(transportDoneListBean.getTsId());
                if (transportEnterpriseLog != null && transportEnterpriseLog.getInvoiceSubjectId() != null) {
                    transportDoneListBean.setInvoiceSubjectId(transportEnterpriseLog.getInvoiceSubjectId());
                }
            }
        }
    }

    private void makeInstantGrabData(List<TransportDoneListBean> transportDones) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(transportDones)) {
            return;
        }
        for (TransportDoneListBean transportDone : transportDones) {
            if (transportDone != null && org.apache.commons.lang3.StringUtils.isNotBlank(transportDone.getLabelJson())) {
                TransportLabelJson transportLabelJson = transportBusiness.getTransportLabelJson(transportDone.getLabelJson());
                if (transportLabelJson != null && transportLabelJson.getInstantGrab() != null && transportLabelJson.getInstantGrab() == 1) {
                    transportDone.setInstantGrabResendOverWord(tytConfigService.getStringValue("transport_instant_grab_over_word", "好货权益：有价货源将有更多频次的自动刷新"));
                    transportDone.setInstantGrab(1);
                } else {
                    transportDone.setInstantGrab(0);
                }
            }
        }
    }

    @Override
    public Long saveTransportDone(Long userId, Long goodsId, TransportDoneRequest doneRequest) {
        TransportMain main = transportMainService.getTransportMainForId(goodsId);
        if (this.getByTsId(goodsId) != null) {
            logger.info("货源已存在成交的信息，货源ID：{}", goodsId);
            return goodsId;
        }
        TransportDone done = this.createTransportDone(userId, main, doneRequest);
        this.add(done);
//        this.sendTransportDealAfter2MQ(done.getTsId(), 1); // 应业务需求，移除短信通知
        return goodsId;
    }

    /**
     * 创建TransportDone 对象
     *
     * @param userId
     * @param main
     * @param doneRequest
     * @return
     */
    private TransportDone createTransportDone(Long userId, TransportMain main, TransportDoneRequest doneRequest) {
        Date currentTime = new Date();
        TransportDone done = new TransportDone();
        done.setUserId(userId);
        done.setNickName(main.getNickName());
        done.setCellPhone(main.getUploadCellPhone());
        done.setTsId(main.getId());
        done.setStartPoint(main.getStartPoint());
        done.setDestPoint(main.getDestPoint());
        done.setTaskContent(main.getTaskContent());
        done.setTsOrderNo(main.getTsOrderNo());
        done.setPublishTime(main.getCtime());
        done.setCarId(doneRequest.getCarId());
        done.setHeadCity(doneRequest.getHeadCity());
        done.setHeadNo(doneRequest.getHeadNo());
        done.setTailCity(doneRequest.getTailCity());
        done.setTailNo(doneRequest.getTailNo());
        done.setCarryUserId(doneRequest.getCarryUserId());
        done.setCarryCellPhone(doneRequest.getCarryCellPhone());
        done.setCarryName(doneRequest.getCarryName());
        done.setIsChangeCar(doneRequest.getIsChangeCar() == null ? 0 : doneRequest.getIsChangeCar());
        done.setIsAllowLocation(doneRequest.getIsAllowLocation() == null ? 0 : doneRequest.getIsAllowLocation());
        done.setDealPrice(doneRequest.getDealPrice()!=null? doneRequest.getDealPrice() : new BigDecimal(0));
        done.setExcellentGoods(main.getExcellentGoods());
        done.setExcellentGoodsTwo(main.getExcellentGoodsTwo());
        done.setPublishGoodsType(main.getPublishGoodsType());
        done.setCtime(currentTime);
        done.setMtime(currentTime);
        done.setMachineRemark(main.getMachineRemark());
        done.setDriverDriving(main.getDriverDriving());
        done.setLoadCellPhone(main.getLoadCellPhone());
        done.setUnloadCellPhone(main.getUnloadCellPhone());
        done.setCargoOwnerId(main.getCargoOwnerId());
        done.setExcellentCardId(main.getExcellentCardId());
        return done;
    }

    @Override
    public void sendTransportDealAfter2MQ(long tsId, int type) {

        TransportDealAfterBean mqMsg = new TransportDealAfterBean();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(MqBaseMessageBean.TRANSPORT_DEAL_AFTER_MESSAGE);
        //货源ID
        mqMsg.setTsId(tsId);
        //类型
        mqMsg.setType(type);
        // 保存发送mq
        final String messageSerailNum = mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        final int messageType = mqMsg.getMessageType();
        logger.info("货源设置成交之后的消息处理，发送MQ:{}", mqJson);
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                //发送并mq信息并保存到数据库
                tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, MqBaseMessageBean.TRANSPORT_DEAL_AFTER_MESSAGE);
                tytMqMessageService.sendMqMessage(messageSerailNum, mqJson, MqBaseMessageBean.TRANSPORT_DEAL_AFTER_MESSAGE);
            }
        });
        // 关闭线程
        executorService.shutdown();
    }


    @Override
    public void updateChangCarStatus(Long tsId, String headCity, String headNo, String tailCity, String tailNo, Long userId, BigDecimal dealPrice) {
        String sql = "UPDATE `tyt_transport_done` SET is_allow_location=?,head_city=?,head_no=?,tail_city=?,tail_no=?, `is_change_car`=1,carry_user_id =?,deal_price =? WHERE ts_id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{0, headCity, headNo, tailCity, tailNo, userId,dealPrice, tsId});
    }

    @Override
    public void updateAllowStatus(Long tsId, Integer isAllow) {
        String sql = "UPDATE `tyt_transport_done` SET `is_allow_location`=? WHERE ts_id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{isAllow, tsId});
    }

    @Override
    public void updateCarInfo(TransportDone transportDone) {
        String sql = "UPDATE `tyt_transport_done` SET head_city=?,head_no=?,tail_city=?,tail_no=?,car_id=?,`is_change_car`=1  WHERE ts_id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{transportDone.getHeadCity(), transportDone.getHeadNo(), transportDone.getTailCity(), transportDone.getTailNo(), transportDone.getCarId(), transportDone.getTsId()});
    }

    /**
     * @description 根据更新时间获取货源成交记录
     * <AUTHOR>
     * @date 2022/9/19 9:52
     * @param userId
     * @param carryUserId
     * @param headCity
     * @param headNo
     * @param tailCity
     * @param tailNo
     * @param ctime
     * @return com.tyt.model.TransportDone
     */
    @Override
    public TransportDone getTransportDoneByMtime(Long userId, Long carryUserId, String headCity, String headNo, String tailCity, String tailNo, Date ctime) throws Exception {
        String sql = "select * from tyt_transport_done where user_id = ? and carry_user_id = ? and head_city = ? and head_no = ? and tail_city = ? and tail_no = ? and ctime < ? order by mtime desc limit 1";
        List<TransportDone> list = this.getBaseDao().queryForList(sql, new Object[]{userId, carryUserId, headCity, headNo, tailCity, tailNo, ctime});
        if(list != null && list.size() > 0){
            return list.get(0);
        }
        return null;
    }


}
