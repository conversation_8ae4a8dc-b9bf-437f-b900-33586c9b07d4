package com.tyt.transport.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.callPhoneRecord.service.CallPhoneRecordService;
import com.tyt.common.bean.PageData;
import com.tyt.config.util.AppConfig;
import com.tyt.elasticsearch.ElasticTransportHelper;
import com.tyt.infofee.bean.CreditUserInfo;
import com.tyt.infofee.bean.SimpleOrderBean;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.limituser.service.ILimitUserService;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.*;
import com.tyt.plat.aop.abtest.EnableAbTest;
import com.tyt.plat.commons.model.PageParameter;
import com.tyt.plat.commons.tools.CustomPageHelper;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.TsSortTypeEnum;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.mapper.recommend.NewIdentityMapper;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.plat.vo.ts.CallLogQuery;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.service.common.elasticserarch.TransformUtil;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.successaccount.service.TytSuccessAccountService;
import com.tyt.transport.dao.TransportDao;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.*;
import com.tyt.user.service.*;
import com.tyt.util.*;
import com.tytrecommend.model.NewIdentity;
import com.tytrecommend.model.TytPreferenceNew;
import kafka.utils.Json;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@SuppressWarnings("deprecation")
@Service("transportService")
public class TransportServiceImpl extends BaseServiceImpl<Transport, Long> implements TransportService {

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;
    @Resource(name = "transportOrdersService")
    TransportOrdersService transportOrdersService;
    @Resource(name = "tytUserCallPhoneRecordService")
    private TytUserCallPhoneRecordService callPhoneRecordService;
    @Resource(name = "tytCallPhoneLimitService")
    private TytCallPhoneLimitService callPhoneLimitService;
    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityAuthService;
    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;
    @Resource(name = "carLogService")
    private CarLogService carlogService;
    @Resource(name = "newIdentityMapper")
    private NewIdentityMapper newIdentityMapper;

    @Resource(name = "userService")
    private UserService userService;
    //	private static Lock lock = new ReentrantLock();
    @Resource(name = "callPhoneLimitNewService")
    private TytCallPhoneLimitNewService callPhoneLimitNewService;


    @Resource(name = "carService")
    private CarService carService;

    @Resource(name = "successAccountService")
    private TytSuccessAccountService successAccountService;

    @Autowired
    private BsTransportService bsTransportService;

    @Autowired
    private ILimitUserService limitUserService;

    @Resource(name = "tytUserSubService")
    private TytUserSubService userSubService;

    @Autowired
    private IInfofeeDetailService infofeeDetailService;

    @Autowired
    private TytAppCallLogMapper tytAppCallLogMapper;

    @Autowired
    private TytTransportQuotedPriceMapper tytTransportQuotedPriceMapper;

    @Resource(name = "callPhoneRecordService")
    private CallPhoneRecordService tempCallPhoneRecordService;

    @Resource(name = "tytOwnerAuthService")
    private TytOwnerAuthService tytOwnerAuthService;

    @Autowired
    private TytTransportEnterpriseLogMapper transportEnterpriseLogMapper;
    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;
    @Autowired
    private CoverGoodsLogMapper coverGoodsLogMapper;

    @Autowired
    @Lazy
    private TransportService self;

    @Autowired
    private ExcellentPriceConfigService excellentPriceConfigService;

    @Autowired
    private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY = "transportQuotedPriceCarNoLook";

    @Resource(name = "transportDao")
    @Override
    public void setBaseDao(BaseDao<Transport, Long> transportDao) {
        super.setBaseDao(transportDao);
    }

    @Override
    public List<String> getTelList(String sql) {
        return ((TransportDao) this.getBaseDao()).getTelList(sql);
    }

    @Override
    public List<Long> getQqList(String sql) {
        return ((TransportDao) this.getBaseDao()).getQqList(sql);
    }

    @Override
    public List<Long> getInfoIdList(String clause) {
        return ((TransportDao) this.getBaseDao()).getInfoIdList(clause);
    }

    @Override
    public boolean isExit(Transport transport) throws Exception {
        StringBuffer sql = new StringBuffer();
        String tel1 = transport.getTel();
        String tel3 = transport.getTel3();
        String tel4 = transport.getTel4();
        String tel = "(" + tel1;
        if (tel3 != null && !tel3.equals(""))
            tel += "," + tel3;
        if (tel4 != null && !tel4.equals(""))
            tel += "," + tel4;
        tel += ")";
        sql.append(" entity.startPoint='" + transport.getStartPoint() + "'");
        sql.append(" and entity.destPoint='" + transport.getDestPoint() + "'");
        sql.append(" and entity.taskContent='" + transport.getTaskContent() + "'");
        sql.append(" and entity.tel in " + tel);
        sql.append(" and entity.tel3 in " + tel);
        sql.append(" and entity.tel4 in " + tel);
        sql.append(" and entity.status=1");
        return this.getList(sql.toString(), null).size() > 0;
    }

    @Override
    public Map<Object, Object> queryTransport(long start_coord_x, long start_coord_y, long dest_coord_x, long dest_coord_y,
                                              int queryType, long querySign, boolean isDelayed, long startDistanceValue, long destDistanceValue,
                                              Long curUserId, Integer userType, String carLength, String carType, String specialRequired, String clientVersion) {
        String defaultUserId = tytConfigService.getStringValue("tyt_transport_default_user_id", "4");
        int defaultUserIdAppOnoff = tytConfigService.getIntValue("tyt_transport_default_user_id_app_onoff", 0);

        Map<Object, Object> map = new HashMap<Object, Object>();
        long totalSize = 0;
        // 第一次查询大小
        int firstPageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.first.page.size");

        // 为了修复竞品爬取数据的问题，此处修改为如果不登录的用户，只返回10条 暂时不启用
//		if (curUserId == null || curUserId.longValue() <= 0) {
//			//0：下拉   1：首次进入   2：上拉
//			if(queryType == 1){
//				firstPageSize = 10;
//			}
//		}
        // 试用用户延时时间秒
        int trialDelay = AppConfig.getIntProperty("tyt.tyt_transport.trial.delay.time");
        // 缓存时间
        long cacheTime = AppConfig.getIntProperty("tyt.tyt_transport.query.cache.time");
        // 上滑动下滑动最大结果集大小
        int pageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.page.size").intValue();

        //是否使用es搜索引擎开关
        int esOnOff = tytConfigService.getIntValue("tyt_use_es_onoff", 0);

        // 范围300公里
        startDistanceValue = startDistanceValue > 500 ? Long.parseLong(String.valueOf(AppConfig.getIntProperty("tyt.tyt_transport.range"))) : startDistanceValue;
        destDistanceValue = destDistanceValue > 500 ? Long.parseLong(String.valueOf(AppConfig.getIntProperty("tyt.tyt_transport.range"))) : destDistanceValue;
        long startRange = startDistanceValue != 0 ? (startDistanceValue * 100) : Long.parseLong(String.valueOf(AppConfig.getIntProperty("tyt.tyt_transport.range") * 100));
        long destRange = destDistanceValue != 0 ? (destDistanceValue * 100) : Long.parseLong(String.valueOf(AppConfig.getIntProperty("tyt.tyt_transport.range") * 100));
        List<TransportBean> returnTbList = null;
        String endTime = TimeUtil.formatDateTime(new Date(System.currentTimeMillis() - trialDelay * 1000));
        if (!isDelayed) {
            endTime = TimeUtil.formatDate(new Date()) + " 23:59:59";
        }
        //endTime="2018-03-02 23:59:59";
        String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
        //beginTime="2018-03-02 00:00:00";
        logger.debug("beginTime=" + beginTime);
        logger.debug("endTime=" + endTime);
        List<Object> list = new ArrayList<Object>();
        StringBuffer sb = new StringBuffer(" from tyt_transport where 1=1 ");
        StringBuffer sbSql = new StringBuffer("select *  ");
        sb.append("and status=1");
        sb.append(" and display_type=1");
        // 增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表
        sb.append(" and is_display=1");

        sb.append(" and ctime>=?");
        if (esOnOff == 0) {
            list.add(beginTime);
        } else {
            list.add(TransformUtil.formatGMTDate(beginTime));
        }
        sb.append(" and ctime<=?");
        if (esOnOff == 0) {
            list.add(endTime);
        } else {
            list.add(TransformUtil.formatGMTDate(endTime));
        }

        if (userType != null && userType == 1) { // 如果用户未付费会员时
            sb.append(" and user_type =?");
            list.add(userType);
        }
        if (StringUtils.isNotBlank(carLength)) { // 车辆长度
            sb.append(" and car_length =?");
            list.add(carLength);
        }
        if (StringUtils.isNotBlank(carType)) { // 车辆类型
            sb.append(" and car_type =?");
            list.add(carType);
        }
        if (StringUtils.isNotBlank(specialRequired)) { // 特殊要求
            sb.append(" and special_required =?");
            list.add(specialRequired);
        }

        sb.append(" and start_coord_x>=?");
        list.add(start_coord_x - startRange);
        sb.append(" and start_coord_x<=?");
        list.add(start_coord_x + startRange);
        sb.append(" and start_coord_y>=?");
        list.add(start_coord_y - startRange);
        sb.append(" and start_coord_y<=?");
        list.add(start_coord_y + startRange);
        logger.debug("start_coord_x>=" + (start_coord_x - startRange));
        logger.debug("start_coord_x<=" + (start_coord_x + startRange));
        logger.debug("start_coord_y>=" + (start_coord_y - startRange));
        logger.debug("start_coord_y<=" + (start_coord_y + startRange));
        if (dest_coord_x != 0 || dest_coord_y != 0) {
            sb.append(" and dest_coord_x>=?");
            list.add(dest_coord_x - destRange);
            sb.append(" and dest_coord_x<=?");
            list.add(dest_coord_x + destRange);
            sb.append(" and dest_coord_y>=?");
            list.add(dest_coord_y - destRange);
            sb.append(" and dest_coord_y<=?");
            list.add(dest_coord_y + destRange);
        }
        int searchSize = pageSize;
        long t1 = 0, t2 = 0;
        if (querySign <= 1) {
            queryType = 1;
            logger.info("查询时上拉、下滑时客户端数据为空,进行查询类型转换为第一次查询queryType=1");
        }

        // 第一次请求
        if (queryType == 1) {
            //0时可以取缓存
            if (esOnOff == 0) {
                // 取缓存 有直接返回
                t1 = System.currentTimeMillis();
                String transportListCacheJson = cacheService.getString(getTransportCacheKey(start_coord_x, start_coord_y, dest_coord_x, dest_coord_y, queryType, querySign, startRange, destRange, userType, carLength, carType, specialRequired));
                t2 = System.currentTimeMillis();
                logger.info("缓存查询时间：" + (t2 - t1) + "ms");
                if (!StringUtils.isBlank(transportListCacheJson)) {
                    List<Transport> sourceList = JSON.parseArray(transportListCacheJson, Transport.class);


                    returnTbList = bsTransportService.filterTransportList(sourceList, curUserId, clientVersion);
                    totalSize = getSerachTotalCount(sb, list.toArray());
                    map.put("totalSize", totalSize);
                    map.put("resultList", returnTbList);
                    return map;
                }
            }
            //增加ID范围查询，优化SQL语句
            Long todayMinTransId = getTodayMinTransId();
            if (todayMinTransId != null && !"null".equals(todayMinTransId)) {
                sb.append(" and id > ?");
                list.add(todayMinTransId);
            }
            searchSize = firstPageSize;
            // 大小排序
            sb.append(" order by id desc ");
        } else
            // 下拉查新数据
            if (queryType == 0) {
                sb.append(" and id>?");
                // 小大排序
                sb.append(" order by id desc ");
                list.add(querySign);
            }
            // 上推查历史数据
            else {
                //增加ID范围查询，优化SQL语句
                Long todayMinTransId = getTodayMinTransId();
                if (todayMinTransId != null && !"null".equals(todayMinTransId)) {
                    sb.append(" and id > ?");
                    list.add(todayMinTransId);
                }

                sb.append(" and id<?");
                // 大小排序
                sb.append(" order by id desc ");
                list.add(querySign);
            }
        sbSql.append(sb);
        // 查询数据集
        long t3 = 0, t4 = 0;
        t3 = System.currentTimeMillis();

        //0时走
        if (esOnOff == 0) {
            totalSize = getSerachTotalCount(sb, list.toArray());
            if (totalSize > 0) {
                List<Transport> transportList = this.getBaseDao().search(sbSql.toString(), list.toArray(), 1, searchSize);
                t4 = System.currentTimeMillis();
                logger.debug("数据库查询时间：" + (t4 - t3) + "ms");
                int size = transportList == null ? 0 : transportList.size();
                logger.debug("数据库查询结果集条数：" + size + "条");
                String transportListJsonStr = null;
                if (transportList != null && transportList.size() > 0) {
                    returnTbList = bsTransportService.filterTransportList(transportList, curUserId, clientVersion);
                    transportListJsonStr = JSON.toJSONString(transportList);
                    if (queryType == 1) {
                        // 保存到缓存中 tbList
                        cacheService.setString(getTransportCacheKey(start_coord_x, start_coord_y, dest_coord_x, dest_coord_y, queryType, querySign, startRange, destRange, userType, carLength, carType, specialRequired), transportListJsonStr, cacheTime);
                    }
                }
            }
        } else {//1时走es
            //String sss="select *   from tytTransport where 1=1 and status=1 and displayType=1 and isDisplay=1 and ctime>='2018-03-02T00:00:00.000+0800' and ctime<='2018-03-02T23:59:59.000+0800' and startCoordX>='-3000' and startCoordX<='30000000' and startCoordY>='-3000' and startCoordY<='30000000'  order by id desc";
            Map<String, Object> esmap = ElasticTransportHelper.findMapList(sbSql.toString(), 0, searchSize, list);
            //Map<String,Object> esmap=ElasticTransportHelper.findMapList(sss, 0, searchSize, list);
            List<Transport> transportList = (List<Transport>) esmap.get("rows");
            totalSize = (Long) esmap.get("total");

            t4 = System.currentTimeMillis();
            int size = transportList == null ? 0 : transportList.size();
            logger.info("plat search接口es查询时间：" + (t4 - t3) + "ms" + " size:" + size);
            String transportListJsonStr = null;
            if (transportList != null && transportList.size() > 0) {
                returnTbList = bsTransportService.filterTransportList(transportList, curUserId, clientVersion);

            }
            logger.info("plat search接口es反序列化时间：" + (System.currentTimeMillis() - t3) + "ms" + " size:" + size);
        }
        map.put("totalSize", totalSize);
        map.put("resultList", returnTbList);
        return map;
    }

    @Override
    public Map<Object, Object> searchSimilarity(Long tsId, String startProvinc, String startCity, String destProvinc, String destCity, Long matchItemId, int queryType, long querySign, long userId, String clientVersion) {
        String defaultUserId = tytConfigService.getStringValue("tyt_transport_default_user_id", "4");
        int defaultUserIdAppOnoff = tytConfigService.getIntValue("tyt_transport_default_user_id_app_onoff", 0);

        Map<Object, Object> map = new HashMap<Object, Object>();
        long totalSize = 0;
        // 第一次查询大小 相似货源，固定30条
//		int firstPageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.first.page.size");
        int firstPageSize = 30;
        // 上滑动下滑动最大结果集大小 相似货源，固定30条
//		int pageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.page.size").intValue();
        int pageSize = 30;

        List<TransportBean> returnTbList = null;

        //beginTime="2018-03-02 00:00:00";
        String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
        //endTime="2018-03-02 23:59:59";
        String endTime = TimeUtil.formatDate(new Date()) + " 23:59:59";

        List<Object> list = new ArrayList<Object>();
        StringBuffer sb = new StringBuffer(" from tyt_transport where 1=1 ");
        StringBuffer sbSql = new StringBuffer("select *  ");
        // 标准化货源
        sb.append(" and src_msg_id != ?");
        list.add(tsId);
        // 增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表
        sb.append(" and status=1 and display_type=1 and is_display=1");
        // 标准化货源
        sb.append(" and match_item_id = ?");
        list.add(matchItemId);
        sb.append(" and start_provinc = ?");
        list.add(startProvinc);
        sb.append(" and start_city = ?");
        list.add(startCity);
        sb.append(" and dest_provinc = ?");
        list.add(destProvinc);
        sb.append(" and dest_city = ?");
        list.add(destCity);
        sb.append(" and ctime>=?");
        list.add(beginTime);
        sb.append(" and ctime<=?");
        list.add(endTime);

        int searchSize = pageSize;
        if (querySign <= 1) {
            queryType = 1;
            logger.info("查询时上拉、下滑时客户端数据为空,进行查询类型转换为第一次查询queryType=1");
        }

        // 第一次请求
        if (queryType == 1) {
            //增加ID范围查询，优化SQL语句
            Long todayMinTransId = getTodayMinTransId();
            if (todayMinTransId != null && !"null".equals(todayMinTransId)) {
                sb.append(" and id > ?");
                list.add(todayMinTransId);
            }
            searchSize = firstPageSize;
            // 大小排序
            sb.append(" order by id asc ");
        } else
            // 下拉查新数据
            if (queryType == 0) {
                sb.append(" and id>?");
                // 小大排序
                sb.append(" order by id asc ");
                list.add(querySign);
            } else { // 上推查历史数据
                //增加ID范围查询，优化SQL语句
                Long todayMinTransId = getTodayMinTransId();
                if (todayMinTransId != null && !"null".equals(todayMinTransId)) {
                    sb.append(" and id > ?");
                    list.add(todayMinTransId);
                }
                sb.append(" and id<?");
                // 大小排序
                sb.append(" order by id asc ");
                list.add(querySign);
            }
        sbSql.append(sb);
        // 查询数据集
        long t3 = 0, t4 = 0;
        t3 = System.currentTimeMillis();

        totalSize = getSerachTotalCount(sb, list.toArray());
        if (totalSize > 0) {
            List<Transport> transportList = this.getBaseDao().search(sbSql.toString(), list.toArray(), 1, searchSize);
            t4 = System.currentTimeMillis();
            logger.debug("数据库查询时间：" + (t4 - t3) + "ms");
            int size = transportList == null ? 0 : transportList.size();
            logger.debug("数据库查询结果集条数：" + size + "条");


            returnTbList = bsTransportService.filterTransportList(transportList, userId, clientVersion);

        }
        map.put("totalSize", totalSize);
        map.put("resultList", returnTbList);
        return map;
    }

    @Override
    public Long getTodayMinTransId() {
        String historyMaxId = this.cacheService.getString("common_history_max_transport_id" + TimeUtil.formatDate_(TimeUtil.today()));
        if (StringUtils.isEmpty(historyMaxId)) {
            historyMaxId = String.valueOf(initTodayMinTransId());
            if (!"null".equals(historyMaxId)) {
                this.cacheService.setString("common_history_max_transport_id" + TimeUtil.formatDate_(TimeUtil.today()), historyMaxId);
            } else {
                logger.info("APP加载找货数据，初始化货物ID为空,没有昨日数据 ");
                return null;
            }
        }
        logger.info("APP加载找货数据，初始化货物ID为：{}", historyMaxId);
        return Long.parseLong(historyMaxId);
    }

    //获取今天的最小货物ID
    private Long initTodayMinTransId() {
        String sql = "SELECT MAX(t.id) maxId FROM tyt_transport t WHERE t.`ctime` >= ? and t.`ctime` < ?";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{TimeUtil.formatDateTime(TimeUtil.addDay(-1)), TimeUtil.formatDateTime(TimeUtil.today())});
        return (count == null ? null : count.longValue());
    }

    /**
     * 获取找货总查询条数
     *
     * @param sb
     * @param params
     * @return
     */
    @Override
    public long getSerachTotalCount(StringBuffer sb, Object[] params) {
        StringBuffer countSql = new StringBuffer("SELECT COUNT(*)");
        countSql.append(sb);
        BigInteger count = this.getBaseDao().query(countSql.toString(), params);
        return count == null ? 0 : count.longValue();
    }

    /**
     * 返回 缓存的key
     *
     * @param start_coord_x
     * @param start_coord_y
     * @param dest_coord_x
     * @param dest_coord_y
     * @param queryType
     * @param querySign
     * @return
     */
    public String getTransportCacheKey(long start_coord_x, long start_coord_y, long dest_coord_x, long dest_coord_y, int queryType, long querySign, long startRange, long destRange, Integer userType, String carLength, String carType, String specialRequired) {
        StringBuffer sb = new StringBuffer(Constant.CACHE_TRANSPORT_LIST_KEY);
        sb.append("sx_");
        sb.append(start_coord_x);
        sb.append("_sy_");
        sb.append(start_coord_y);
        sb.append("_sr_");
        sb.append(startRange);
        if (dest_coord_x != 0 || dest_coord_y != 0) {
            sb.append("_dx_");
            sb.append(dest_coord_x);
            sb.append("_dy_");
            sb.append(dest_coord_y);
            sb.append("_dr_");
            sb.append(destRange);
        }
        if (userType != null) {
            sb.append("_ut_");
            sb.append(userType);
        }
        if (StringUtils.isNotBlank(carLength)) {
            sb.append("_cl_");
            sb.append(carLength);
        }
        if (StringUtils.isNotBlank(carType)) {
            sb.append("_ct_");
            sb.append(carType);
        }
        if (StringUtils.isNotBlank(specialRequired)) {
            sb.append("_sr_");
            sb.append(specialRequired);
        }
        return sb.toString();
    }

    @Override
    public boolean isExitHashCode(String hashCode) throws Exception {
        // String result=(String) cacheService.getObject(hashCode);
        // return org.springframework.util.StringUtils.hasLength(result);
        try {
            int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            LockUtil.lockObject("1", Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + hashCode, redisLockTimeout);
            String result = (String) cacheService.getObject(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + hashCode);
            boolean flag = org.springframework.util.StringUtils.hasLength(result);
            logger.info("plat货物保存检测缓存中" + (flag ? "存在" : "不存在") + "-----原" + hashCode);
            /* 保存hashcode到缓存，时间1天 */
            if (!flag) {
                cacheService.setObject(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + hashCode, hashCode, Constant.CACHE_EXPIRE_TIME_24H);
                logger.info("plat货物保存检测缓存数据" + cacheService.getObject(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + hashCode));
            }
            return flag;
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        } finally {
            LockUtil.unLockObject("1", Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + hashCode);
        }
    }

    @Override
    public boolean updateStatusByIds(List<Long> idList, Integer status, String infoStatus, Integer display) {

        try {
            if (infoStatus == null || infoStatus.trim().equals("")) {
                StringBuffer sql = new StringBuffer("update tyt_transport set status=:status,display_type=:display,mtime=:mtime,is_display=:is_display where id IN(:ids)");
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("status", status);
                map.put("display", display);
                map.put("mtime", TimeUtil.formatDateTime(new Date()));
                map.put("is_display", 1);
                map.put("ids", idList);

                return this.executeUpdateSql(sql.toString(), map) > 0;
            } else {
                StringBuffer sql = new StringBuffer("update tyt_transport set status=:status,info_status=:infoStatus,display_type=:display,mtime=:mtime,is_display=:is_display where id IN(:ids)");
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("status", status);
                map.put("infoStatus", infoStatus);
                map.put("display", display);
                map.put("mtime", TimeUtil.formatDateTime(new Date()));
                map.put("is_display", 1);
                map.put("ids", idList);
                return this.executeUpdateSql(sql.toString(), map) > 0;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public List<Long> getHistoryIdListBySrcMsgId(Long srcMsgId, Long userId) {
        StringBuffer sql = new StringBuffer("select id from tyt_transport where user_id=? and src_msg_id=? and display_type=? and ctime<?");
        List<Object> params = new ArrayList<Object>();
        params.add(userId);
        params.add(srcMsgId);
        params.add(1);
        params.add(TimeUtil.formatDateTime(TimeUtil.today()));
        Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
        map.put("id", Hibernate.LONG);
        List<Long> list = this.getBaseDao().search(sql.toString(), map, null, params.toArray(), 1, 10000);
        return list;

    }

    @Override
    public int updateDisplayTypeBysrcMsgId(Long srcMsgId, Integer display) {
        StringBuffer sql = new StringBuffer("update tyt_transport set display_type=:display" + " where src_msg_id=:srcMsgId");
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("display", display);
            map.put("srcMsgId", srcMsgId);
            return this.executeUpdateSql(sql.toString(), map);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            // 提醒垃圾回收
            sql = null;
        }

    }

    @Override
    public long getMyPublishaNbr(Long userId, int status, String isInfoFee) {
        if (isInfoFee == null || isInfoFee.trim().equals("")) {
            String SQL = "SELECT count(*) from tyt_transport where user_id=? and status=? and info_status=? and ctime>=?";
            BigInteger c = this.getBaseDao().query(SQL, new Object[]{userId, status, "0", TimeUtil.formatDate(new Date())});
            if (c == null)
                return 0;
            return c.longValue();
        } else {
            String SQL = "SELECT count(*) from tyt_transport where user_id=? and status=? and is_info_fee=? and info_status=? and ctime>=?";
            BigInteger c = this.getBaseDao().query(SQL, new Object[]{userId, status, isInfoFee, "0", TimeUtil.formatDate(new Date())});
            if (c == null)
                return 0;
            return c.longValue();
        }

    }

    /**
     * @return long
     * @Description 获取我的发货条数(信息费版本新接口方法)
     * 发布中的货源不与信息费关联
     * <AUTHOR>
     * @Date 2019/1/4 16:40
     * @Param [userId, status, isInfoFee]
     **/
    @Override
    public long getMyPublishNew(Long userId, int status, String isInfoFee) {
        if (isInfoFee == null || isInfoFee.trim().equals("")) {
            String SQL = "SELECT count(*) from tyt_transport where user_id=? and status=?  and ctime>=?";
            BigInteger c = this.getBaseDao().query(SQL, new Object[]{userId, status, TimeUtil.formatDate(new Date())});
            if (c == null)
                return 0;
            return c.longValue();
        } else {
            String SQL = "SELECT count(*) from tyt_transport where user_id=? and status=? and is_info_fee=? and ctime>=?";
            BigInteger c = this.getBaseDao().query(SQL, new Object[]{userId, status, isInfoFee, TimeUtil.formatDate(new Date())});
            if (c == null)
                return 0;
            return c.longValue();
        }

    }

    @Override
    public void addCall(String userId, String callResultCode, String callResultName, String goodId,
                        String fromCar, Long carId, String reference,String clientSign, String clientVersion,String markerOwnerCodes) {
        // Transport t=this.getBaseDao().findById(Long.valueOf(goodId));
        // tyt_transport表只保留前几天内的数据，如果过期数据查询tyt_transport表可能报错空指针，故改为如下
//		TransportMain t = transportMainService.getById(Long.valueOf(goodId));
        // 优化货源
        TransportMain transportMain = transportMainService.getTransportMainForId(Long.valueOf(goodId));
        if(null == transportMain){
            logger.info("user addCall transportMain is null goodId: " + goodId);
            return;
        }
        String redisLockKey = "plat:add:call:" + userId + transportMain.getId();
        try {
            int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            logger.info("user addCall get redis lock begin, user id is: " + userId);
            if (LockUtil.lockObject("1", redisLockKey, redisLockTimeout)) {
                if(StringUtils.isNotEmpty(callResultCode)&& StringUtils.isEmpty(callResultName)){
                    switch (callResultCode) {
                        case "1":
                            callResultName = "没联系上";
                            break;
                        case "2":
                            callResultName = "运价低";
                            break;
                        case "3":
                            callResultName = "可以承运";
                            break;
                        case "4":
                            callResultName = "已找到车";
                            break;
                        case "5":
                            callResultName = "虚假交易";
                            break;
                        case "6":
                            callResultName = "已经拉走";
                            break;
                        case "7":
                            callResultName = "每公里5元";
                            break;
                        case "8":
                            callResultName = "每公里6元";
                            break;
                        case "9":
                            callResultName = "每公里7元";
                            break;
                        case "10":
                            callResultName = "每公里8元";
                            break;
                        case "11":
                            callResultName = "每公里9元";
                            break;
                        case "12":
                            callResultName = "每公里10元";
                            break;
                        case "13":
                            callResultName = "其他价格";
                            break;
                    }
                }
                String markerOwnerNames = getMarkerOwnerNameByCodes(markerOwnerCodes);
                /*
                 * 查询用户是否存储过对该货物的标注，如果存储过则更新，否则添加
                 */
                String sql = "UPDATE tyt_app_call_log tacl SET tacl.`called_info_id`=?, tacl.`call_result_code`=?,tacl.`call_result_name`=?,tacl.`from_car`=?,tacl.`car_id`=?,tacl.`reference`=?, tacl.`call_time`=NOW(),`plat_id`=?,`marker_owner_codes`=?,`marker_owner_names`=? WHERE tacl.`call_module` =? and tacl.`src_msg_id`=? AND tacl.`caller_id`=?";
                Object[] params = new Object[]{transportMain.getId(), callResultCode, callResultName, fromCar, carId, reference,clientSign, markerOwnerCodes,markerOwnerNames,1, transportMain.getSrcMsgId(), userId};
                int c = this.getBaseDao().executeUpdateSql(sql, params);
                if (c <= 0) {
                    sql = "INSERT INTO tyt_app_call_log(`call_time`, `caller_id`, `called_info_id`, `call_result_code`,`call_result_name`,`pub_user_id`,`src_msg_id`,`from_car`,`car_id`,`reference`,`plat_id`,`marker_owner_codes`,`marker_owner_names`, `create_time`) VALUE(NOW(), ?, ?, ?,?,?,?,?,?,?,?,?,?, NOW())";
                    params = new Object[]{userId, transportMain.getId(), callResultCode, callResultName, transportMain.getUserId(), transportMain.getSrcMsgId(), fromCar, carId, reference,clientSign,markerOwnerCodes,markerOwnerNames};
                    this.getBaseDao().executeUpdateSql(sql, params);
                    // 兼容解决android版本6150-6200版本联系人缺失的问题
                    if ("21".equals(clientSign)){
                        if (6149<Integer.parseInt(clientVersion) || 6201>Integer.parseInt(clientVersion)){
                            try{
                                CallPhoneRecord record = new CallPhoneRecord();
                                record.setCarUserId(Long.parseLong(userId));
                                record.setCarUserName("");
                                record.setPlatId(clientSign);
                                record.setSrcMsgId(transportMain.getSrcMsgId());
                                record.setModule("货源详情-通话记录");
                                record.setPath("findGoodsPage2openGoodsItem_goodsDetailPage2callPhone");
                                tempCallPhoneRecordService.saveRecord(record);
                            } catch (Exception e) {
                                logger.error("拨打电话时通过拨打记录处，添加call_phone_record出现异常", e);
                            }
                        }
                    }
                    // 货源沟通数记录到redis
                    String today = TimeUtil.formatDate(TimeUtil.today());
                    Long redisCount = RedisUtil.mapHincr(Constant.PLAT_COUNT_TRANSPORT_KEY + today, Constant.CONTACT_COUNT_HASH_KEY + transportMain.getId(), (int) Constant.CACHE_EXPIRE_TIME_24H);
                }
            }
        } finally {
            logger.info("user addCall release redis lock, userid is: " + userId);
            LockUtil.unLockObject("1", redisLockKey);
        }
    }

    public String

            getMarkerOwnerNameByCodes(String markerOwnerCodes){
        List<String> markerOwnerNames = new ArrayList<>();
        if (StringUtils.isNotEmpty(markerOwnerCodes)){
            String [] markerOwnerCodeArray = markerOwnerCodes.split(",");
            for (String markerOwnerCode: markerOwnerCodeArray){
                String ownerName = getMarkerOwnerNameByCode(markerOwnerCode);
                if (StringUtils.isNotEmpty(ownerName)){
                    markerOwnerNames.add(ownerName);
                }
            }
        }
        return CollectionUtils.isEmpty(markerOwnerNames) ? null : StringUtils.join(markerOwnerNames, ",");
    }

    public String getMarkerOwnerNameByCode(String markerOwnerCode) {
        List<TytSource> sourceList = TytSourceUtil.getSourceList("tyt_marker_owner_code");
        for (TytSource tytSource : sourceList) {
            if (tytSource.getValue().equals(markerOwnerCode)) {
                return tytSource.getName();
            }
        }
        return null;
    }

    @Override
    public CallLogQueryResultBean getCallLog(CallLogQuery callLogQuery) {
        Long userId = callLogQuery.getUserId();

        Integer currentPage = callLogQuery.getCurrentPage();
        if(currentPage == null){
            currentPage = 1;
        }

        CallLogQueryResultBean resultBean = new CallLogQueryResultBean();

        /*
         * 计算从第几条开始查询到第几条
         */
        Integer searchSize = tytConfigService.getIntValue("callLogPageSize");
        if (searchSize == null || searchSize.intValue() <= 0) {
            searchSize = 100;
        }

        /*
         * 获取允许客户端获取的最大的通话记录条数
         */
        /*
        Integer maxCallLogSize = tytConfigService.getIntValue("maxCallLogSize");
        if (maxCallLogSize == null || maxCallLogSize.intValue() <= 0) {
            maxCallLogSize = 100;
        }
        */

        PageParameter pageParameter = new PageParameter(currentPage, searchSize);
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(pageParameter);

        List<CallLogBean> callLogList;

        if (!callLogQuery.getQuotedPriceOnce() && !callLogQuery.getViewLog()) {
            callLogQuery.setStartDate(DateUtil.addTime(DateUtil.startOfDay(new Date()), Calendar.DAY_OF_YEAR, -2));
            callLogList = tytAppCallLogMapper.getCallLogList(callLogQuery);
        } else if (callLogQuery.getQuotedPriceOnce()) {
            //app找货记录页面点击已出价筛选项时，列表数据取自出价记录数据，与找货记录完全无关，最近联系时间替换为车方出价时间
            callLogQuery.setStartDate(DateUtil.addTime(DateUtil.startOfDay(new Date()), Calendar.DAY_OF_YEAR, -2));
            callLogList = tytTransportQuotedPriceMapper.getQuotedPriceListByCarId(callLogQuery);
            if (CollectionUtils.isNotEmpty(callLogList)) {
                List<Long> srcMsgIds = callLogList.stream().filter(t -> t.getSrcMsgId() != null).map(CallLogBean::getSrcMsgId).collect(Collectors.toList());
                deleteCarNoLookQuotedPriceTransportCache(userId, srcMsgIds);
            }
        } else {
            //浏览记录
            callLogQuery.setStartDate(DateUtil.startOfDay(new Date()));
            callLogList = tytAppCallLogMapper.getViewLogList(callLogQuery);
        }

        PageData<CallLogBean> callLogBeanPageData = customPageHelper.endPage(callLogList);

        long totalRecord = callLogBeanPageData.getTotal();
        long maxPage = callLogBeanPageData.getPages();

        resultBean.setMaxPage((int)maxPage);
        resultBean.setPageSize(searchSize);
        resultBean.setTotalRecord((int)totalRecord);
        resultBean.setCurrentPage(currentPage);

        if(CollectionUtils.isNotEmpty(callLogList)) {

            List<CallLogBean> callLogReturnList = new ArrayList<>();

            Set<String> orderNoSet = new HashSet<>();
            List<Long> goodsUserIdList = new ArrayList<>();

            for (CallLogBean oneCall: callLogList) {
                String tsOrderNo = oneCall.getTsOrderNo();
                Long goodsUserId = oneCall.getGoodsUserId();

                if(!orderNoSet.contains(tsOrderNo)){
                    goodsUserIdList.add(goodsUserId);

                    callLogReturnList.add(oneCall);

                    orderNoSet.add(tsOrderNo);
                }
            }

            String pubUserIds = StringUtils.join(goodsUserIdList, ",");

            Map<Long, CreditUserInfo> creditUserInfoMap = infofeeDetailService.queryCreditUserInfoMapForCar(pubUserIds, userId);

            callLogReturnList.forEach(callLog -> {
                String nickName = StringUtil.hidePhoneInStr(callLog.getNickName());
                callLog.setNickName(nickName);
                // 官方授权账号
                String authNameTea = tytOwnerAuthService.getAuthNameTea(callLog.getAuthName());
                if(StringUtils.isNotBlank(authNameTea)){
                    callLog.setAuthNameTea(authNameTea);
                    callLog.setAuthName(null);
                }

                if (callLog.getGoodStatus() == 1 && !TimeUtil.isToday(Long.parseLong(callLog.getPubDate()))) { // 过期
                    callLog.setGoodStatus(0);
                }
                CreditUserInfo creditUserInfo = creditUserInfoMap.get(callLog.getGoodsUserId());
                if (creditUserInfo != null) {
                    callLog.setTradeNums(Integer.parseInt(creditUserInfo.getTradeNums()));
                    callLog.setCoopNums(Integer.parseInt(creditUserInfo.getCoopNums()));
                }

                Integer isPaySuccess = 0; // 是否显示详情页面的去支付按钮，0显示，1不显示
                if (callLog.getPayStatus() != null && callLog.getRobStatus() != null) {
                    if (callLog.getPayStatus() == 2 && callLog.getRobStatus() != 2 && callLog.getRobStatus() != 3 && callLog.getRobStatus() != 12) {
                        isPaySuccess = 1;
                    } else {
                        isPaySuccess = 0;
                    }
                }
                callLog.setIsPaySuccess(isPaySuccess);
                //返回坐标经纬度
                callLog.initStartLatitudeStr();
                callLog.initStartLongitudeStr();
            });
            handleTransportFields(userId, callLogReturnList);

            makeSpecialCarDeclareInPublic(callLogReturnList);

            resultBean.setData(callLogReturnList);
        }
        return resultBean;
    }

    private void makeSpecialCarDeclareInPublic(List<CallLogBean> callLogReturnList) {
        for (CallLogBean callLogBean : callLogReturnList) {
            if (callLogBean.getSrcMsgId() != null) {
                TransportMain bySrcMsgId = transportMainService.getBySrcMsgId(callLogBean.getSrcMsgId());
                if (bySrcMsgId != null && bySrcMsgId.getExcellentGoods() != null && bySrcMsgId.getExcellentGoods() == 2) {
                    TytSpecialCarDispatchFailure tytSpecialCarDispatchFailure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(callLogBean.getSrcMsgId());
                    if (tytSpecialCarDispatchFailure != null && tytSpecialCarDispatchFailure.getDeclareInPublic() != null && tytSpecialCarDispatchFailure.getDeclareInPublic() == 1) {
                        callLogBean.setDeclareInPublic(1);
                    }
                }
            }
        }
    }

    private void deleteCarNoLookQuotedPriceTransportCache(Long userId, List<Long> srcMsgIds) {
        if (!srcMsgIds.isEmpty()) {
            for (Long srcMsgId : srcMsgIds) {
                RedisUtil.del(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + userId + ":" + srcMsgId);
            }
        }
    }

    /**
     * 处理货源字段，如给专票货源打标；给专车货源打标
     *
     * @param transportBaseList
     */
    public void handleTransportFields(Long userId, List<CallLogBean> transportBaseList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(transportBaseList)) {
            return;
        }
        // 给专票货源打标
        List<Long> srcMsgIds = transportBaseList.stream().
                filter(t -> Objects.equals(t.getInvoiceTransport(), 1))
                .map(CallLogBean::getSrcMsgId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(srcMsgIds)) {
            Map<Long, Long> valueMap = transportEnterpriseLogMapper.getInvoiceSubjectIdBySrcMsgIds(srcMsgIds).stream()
                    .collect(Collectors.toMap(TytTransportEnterpriseLog::getSrcMsgId, TytTransportEnterpriseLog::getInvoiceSubjectId));
            transportBaseList.forEach(t -> t.setInvoiceSubjectId(valueMap.get(t.getSrcMsgId())));
        }

        // 给专车货源打标
        srcMsgIds = transportBaseList.stream().
                filter(t -> Objects.equals(t.getExcellentGoods(), 2))
                .map(CallLogBean::getSrcMsgId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(srcMsgIds)) {
            Map<Long, Integer> valueMap = tytTransportMainExtendMapper.getUseCarTypeBySrcMsgIds(srcMsgIds).stream()
                    .collect(Collectors.toMap(TytTransportMainExtend::getSrcMsgId, TytTransportMainExtend::getUseCarType));
            transportBaseList.forEach(t -> t.setUseCarType(valueMap.get(t.getSrcMsgId())));
        }

        // 该货源是否对车主捂货
        srcMsgIds = transportBaseList.stream()
                .filter(t -> t.getPriorityRecommendExpireTime() != null && t.getPriorityRecommendExpireTime().after(new Date()))
                .map(CallLogBean::getSrcMsgId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(srcMsgIds)) {
            Set<Long> coverGoodsIds = coverGoodsLogMapper.getCoverGoodsIds(userId, srcMsgIds);
            transportBaseList.forEach(t -> t.setCoverGoodsType(coverGoodsIds.contains(t.getSrcMsgId()) ? 1 : 0));
        }

        // 只有好货展示读秒货源标签，非好货把捂货过期时间置为空就隐藏了
        for (CallLogBean transportListVO : transportBaseList) {
            if (transportListVO.getLabelJson() == null || !transportListVO.getLabelJson().contains("\"iGBIResultData\":1")) {
                transportListVO.setPriorityRecommendExpireTime(null);
            }
        }

        // 是否显示超额保障标签：直客发布的货源或优车2.0或抽佣货源
        List<Long> userIds = transportBaseList.stream().map(CallLogBean::getGoodsUserId).collect(Collectors.toList());
        List<NewIdentity> newIdentities = newIdentityMapper.batchGetByUserId(userIds);
        Map<Long, Integer> newIdentityMap = newIdentities.stream().collect(Collectors.toMap(NewIdentity::getUserId, NewIdentity::getType));
        transportBaseList.forEach(t -> {
            TransportLabelJson labelJson = JSON.parseObject(t.getLabelJson(), TransportLabelJson.class);
            t.setShowExcessCoverageLabel(shouldShowExcessCoverageLabel(labelJson, newIdentityMap.get(t.getGoodsUserId())) ? 1 : 0);
            t.setShowDepositCouponsLabel(getShowDepositCouponsLabel(t.getShowExcessCoverageLabel()));
        });
    }

    /**
     * 判断是否应该显示超额投保标签
     */
    private boolean shouldShowExcessCoverageLabel(TransportLabelJson labelJson, Integer userType) {
        // 优车2.0 或 抽佣货源
        if (labelJson != null && (Objects.equals(labelJson.getGoodCarPriceTransport(), 1)
                || Objects.equals(labelJson.getCommissionTransport(), 1))) {
            return true;
        }
        // 或 直客发布货源（直客=个人货主或企业货主）
        return Objects.equals(userType, 3) || Objects.equals(userType, 4);
    }

    /**
     * 获取是否显示优惠券标签
     * 1. 条件：直客（个人货主或企业货主）发布的货源 或优车2.0 或抽佣货源
     * 2. 需要开关关闭
     */
    private Integer getShowDepositCouponsLabel(Integer showExcessCoverageLabel) {
        if (Objects.equals(showExcessCoverageLabel, 1)
                && tytConfigService.getIntValue("show_deposit_coupons_label_switch", 0) == 1) {
            return 1;
        }
        return 0;
    }

    // 记录用户以及访问次数的map(用户id->拨打次数)，通过TytSourceUtil定时更新
    public static Map<String, Integer> userAndCallTime = new HashMap<String, Integer>();
    // 定时更新过程中过度map
    public static Map<String, Integer> userAndCallTimeTemp = new HashMap<String, Integer>();
    // 用户拨打电话限制信息字符串
    public static String userCallLimitCache = "";

    /**
     * @param userId
     * @param goodId        因iOS 和 安卓 传入goodId不一致，兼容后取srcId
     * @param clientVersion
     * @param clientSign
     * @param isNeedPhone   客户端是否需要强制返回电话 1 需要 2 不需要
     * @return
     * @throws Exception
     */
    @Override
    public CallLogBean getPhoneByGoodId(String userId, String goodId, String clientVersion, String clientSign, String isNeedPhone) throws Exception {
        CallLogBean callLogBean = new CallLogBean();
        // 优化成一条记录，查找电话取srcid，再查询
        Transport transport = transportBusiness.getByGoodsIdForUnLock(Long.valueOf(goodId)); // 因iOS
        String sql = "SELECT tsm.`tel`, tsm.`tel3`, tsm.`tel4`, tsm.`src_msg_id` srcMsgId,tsm. ts_order_no tsOrderNo,tsm.is_info_fee isInfoFee FROM tyt_transport_main tsm WHERE tsm.`id`=?";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("tel", Hibernate.STRING);
        scalarMap.put("tel3", Hibernate.STRING);
        scalarMap.put("tel4", Hibernate.STRING);
        scalarMap.put("srcMsgId", Hibernate.LONG);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("isInfoFee", Hibernate.STRING);
        List<CallLogBean> callLogBeans = this.getBaseDao().search(sql, scalarMap, CallLogBean.class, new Object[]{transport.getSrcMsgId()});
        if (callLogBeans.size() >= 1) {
            callLogBean = callLogBeans.get(0);
            // 设置货源状态
            if (transport != null) {
                if (transport.getStatus().intValue() == 5) { // 撤销
                    callLogBean.setGoodStatus(2);
                } else if (transport.getStatus().intValue() == 4) { // 成交
                    callLogBean.setGoodStatus(3);
                } else if (transport.getStatus().intValue() == 1 && !TimeUtil.isToday(transport.getCtime().getTime())) { // 过期
                    callLogBean.setGoodStatus(1);
                }
            }
            /*
             * 根据货物src_msg_id用户id判断用户在当天是否拨打过该货物的电话，
             * 如果打过则直接放行执行后续操作，否则进行拨打电话限制次数判断
             */
            TytUserCallPhoneRecord callPhoneRecord = callPhoneRecordService.getBySrcMsgIdAndUserId(callLogBean.getSrcMsgId(), userId);
            /*
             * 查询用户拨打是否电话标注过该货物
             */
            scalarMap.clear();
            scalarMap.put("callResultCode", Hibernate.STRING);
            sql = "SELECT tacl.`call_result_code` AS 'callResultCode' FROM tyt_app_call_log tacl WHERE tacl.`caller_id`=? AND tacl.`src_msg_id`=?";
            callLogBeans = this.getBaseDao().search(sql, scalarMap, CallLogBean.class, new Object[]{userId, callLogBean.getSrcMsgId()});
            if (callLogBeans.size() == 0) {
                callLogBean.setCallStatus("1_0");
            } else {
                callLogBean.setCallStatus("2_" + callLogBeans.get(0).getCallResultCode());
            }
            // 是否支付过订单
            String hasMakeOrder = "0";
            if (callLogBean.getIsInfoFee().equals("1")) {
                SimpleOrderBean myLastOrder = transportOrdersService.getLastOrderByPayUser(callLogBean.getTsOrderNo(), Long.parseLong(userId));
                if (myLastOrder != null) {
                    hasMakeOrder = "1";
                }
            }
            callLogBean.setHasMakeOrder(hasMakeOrder);
            if (callPhoneRecord == null) {
                // 获取用户的一级身份
                TytUserIdentityAuth userIdentityAuth = userIdentityAuthService
                        .getByUserId(userId);
                /*
                 * 如果用户没有一级身份则设置为拨打电话限制的默认规则对应的一级身份（是一个不存在的身份）
                 */
                if (userIdentityAuth == null) {
                    userIdentityAuth = new TytUserIdentityAuth();
                    userIdentityAuth.setUserClass(
                            Integer.valueOf(Constant.USER_CLASS_DEFUALT_VALUE));
                    userIdentityAuth.setIdentityType(
                            Integer.valueOf(Constant.IDENTITY_TYPE_DEFAULT_VALUE));
                }
                User user = userService.getById(Long.valueOf(userId));
                int userType = user.getUserType();
                // 会员有效期内用户
                boolean isInVipTime = (userType == 1 && user.getServeDays() > 0);

                boolean inLimitScope = limitUserService.isLimitUser(userId);

                /** 条件： 1.非有效会员
                 *        2.在受限表(tyt_limit_user)中
                 */
                if (inLimitScope && !isInVipTime) {
                    boolean isAuth = (userIdentityAuth.getIdentityStatus().intValue() == 1);
                    //未认证先认证
                    if (!isAuth) {
                        callLogBean.clearTel();
                        callLogBean.setIsCanCall(1);
                        callLogBean.setLimitType((int) Constant.USER_IDENTITY_NO_AUTH);
                        callLogBean.setPromptContent(
                                Constant.USER_IDENTITY_NO_AUTH_PROMPT);
                        logger.info("受限用户先认证！userID:{}", userId);

                    } else {
                        callLogBean.clearTel();
                        callLogBean.setIsCanCall(1);
                        callLogBean.setLimitType((int) Constant.USER_NO_PAY);
                        callLogBean.setPromptContent(Constant.USER_NO_PAY_PROMPT);
                    }
                    logger.info("受限用户控制成功！ userID:{}", userId);
                    return callLogBean;
                }

                Integer maxCallTime = userAndCallTime.get(userId);
                logger.info("transport service single user limit isInVipTime: " + isInVipTime + " , maxCallTime: " + maxCallTime);
                // 如果用户在指定条数限制名单中并且不是付费有效期内会员则使用指定条数限制规则
                if (!isInVipTime && maxCallTime != null) {
                    String userCallTimeKey = Constant.SINGLE_USER_CALL_TIME_CACHE_KEY + "_" + userId + "_" + TimeUtil.formatDate(new Date());
                    // 查询用户当前的访问次数
                    String userCurCallTime = RedisUtil.get(userCallTimeKey);
                    logger.info("transport service single user limit userCallTimeKey: " + userCallTimeKey + " , userCurCallTime: " + userCurCallTime);
                    if (StringUtils.isEmpty(userCurCallTime)) {
                        RedisUtil.set(userCallTimeKey, "1", (int) Constant.CACHE_EXPIRE_TIME_24H);
                    } else {
                        if (Integer.valueOf(userCurCallTime) >= maxCallTime) {
                            if (isNotForceNeedPhone(clientVersion, isNeedPhone, clientSign)) {
                                logger.info("transport service single user limit clear tel");
                                // 清空电话
                                callLogBean.clearTel();
                                callLogBean.setIsCanCall(1);
                                callLogBean.setLimitType((int) Constant.USER_EXCEED_ALL_TIME);
                                callLogBean.setPromptContent(Constant.USER_EXCEED_ALL_TIME_PROMPT);
                            }
                        } else {
                            logger.info("transport service single user limit incr call time");
                            // 增加获取电话次数
                            RedisUtil.incr(userCallTimeKey);
                            // 记录拨打电话日志
                            callPhoneRecordService.add(new TytUserCallPhoneRecord(callLogBean.getSrcMsgId(), Long.valueOf(userId)));
                        }
                    }
                } else {
                    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    // 判断用户注册时间是否在新规则限制时间之后，如果是则按照新规则，否则按照老规则
                    String registerlimitMaxTime = tytConfigService
                            .getStringValue(Constant.REGISTER_LIMIT_MAX_TIME, "2018-08-29");
                    String registerTime = sdf.format(user.getCtime());
                    logger.info("registerlimitMaxTime: " + registerlimitMaxTime + ", registerTime-----: " + registerTime);
                    if (registerTime.compareTo(registerlimitMaxTime) >= 0 && !isInVipTime && isNotForceNeedPhone(clientVersion, isNeedPhone, clientSign)) {
                        logger.info("user: " + userId + " use new call phone rule");
                        // 查询用户是否已经认证通过，如果没有认证通过则直接提示身份认证，否则判断次数是否达到总上线
                        boolean isAuth = (userIdentityAuth.getIdentityStatus().intValue() == 1);
                        if (!isAuth) {
                            callLogBean.clearTel();
                            callLogBean.setIsCanCall(1);
                            callLogBean.setLimitType((int) Constant.USER_IDENTITY_NO_AUTH);
                            callLogBean.setPromptContent(
                                    Constant.USER_IDENTITY_NO_AUTH_PROMPT);
                        } else {
                            // 查询总的拨打电话次数
                            List<TytUserCallPhoneRecord> callPhoneRecordList = callPhoneRecordService.getByUserId(userId);
                            int totalAllowCallTimes = tytConfigService.getIntValue(Constant.REGISTER_LIMIT_MAX_TIMES, 4);
                            if (callPhoneRecordList.size() >= totalAllowCallTimes) {
                                callLogBean.clearTel();
                                callLogBean.setIsCanCall(1);
                                callLogBean.setLimitType((int) Constant.USER_NO_PAY);
                                callLogBean.setPromptContent(
                                        Constant.USER_NO_PAY_PROMPT);
                            } else {
                                // 记录拨打电话日志
                                callPhoneRecordService.add(new TytUserCallPhoneRecord(callLogBean.getSrcMsgId(), Long.valueOf(userId)));
                            }
                        }
                    } else {
                        if (userIdentityAuth != null) {
                            // 获取用户对应的以及身份的拨打电话限制
                            TytCallPhoneLimit callPhoneLimit = callPhoneLimitService
                                    .getByUserIdentity(userIdentityAuth.getUserClass(),
                                            userIdentityAuth.getIdentityType());
                            if (callPhoneLimit != null) {
                                /*
                                 * 如果不是三项都不做拨打电话次数限制则需要进行电话限制
                                 */
                                int limitType = checkCallTimeLimit(callPhoneLimit, userId,
                                        callLogBean.getSrcMsgId(),
                                        userIdentityAuth.getIdentityStatus(), clientVersion,
                                        clientSign, isNeedPhone, user);
                                if (limitType > 0) {
                                    // 清空电话
                                    callLogBean.clearTel();
                                    callLogBean.setIsCanCall(1);
                                    callLogBean.setLimitType(limitType);
                                    // 1：未进行车辆认证 2：未进行身份认证 3：试用期用户 4: 缴费到期
                                    // 5：超过所有限制，即车辆认证，身份认证，缴费的拨打电话次数都已用完
                                    switch (limitType) {
                                        case 1:
                                            callLogBean.setPromptContent(
                                                    Constant.USER_CAR_NO_AUTH_PROMPT);
                                            break;
                                        case 2:
                                            callLogBean.setPromptContent(
                                                    Constant.USER_IDENTITY_NO_AUTH_PROMPT);
                                            break;
                                        case 3:
                                            callLogBean.setPromptContent(Constant.USER_NO_PAY_PROMPT);
                                            break;
                                        case 4:
                                            callLogBean.setPromptContent(
                                                    Constant.USER_PAY_OUT_DATE_PROMPT);
                                            break;
                                        case 5:
                                            callLogBean.setPromptContent(
                                                    Constant.USER_EXCEED_ALL_TIME_PROMPT);
                                            break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return callLogBean;
    }

    /**
     * 判断是否不是必须返回手机号
     *
     * @param clientVersion
     * @param isNeedPhone
     * @param clientSign
     * @return
     */
    private boolean isNotForceNeedPhone(String clientVersion, String isNeedPhone, String clientSign) {
        /*
         * 版本兼容代码
         */
        Integer clientVersionInt = Integer.parseInt(clientVersion);
        Integer notCheckMaxVersionInt = null;
        if (Integer.valueOf(clientSign) == 2) {
            notCheckMaxVersionInt = tytConfigService.getIntValue("notCheckAndroidMaxVersionLinePhotoVerify");
        } else if (Integer.valueOf(clientSign) == 3) {
            notCheckMaxVersionInt = tytConfigService.getIntValue("notCheckIosMaxVersionLinePhotoVerify");
        }
        if (notCheckMaxVersionInt == null) {
            notCheckMaxVersionInt = 3100;
        }
        return clientVersionInt > notCheckMaxVersionInt && !"1".equals(isNeedPhone);
    }

    /**
     * 判断用户是否还可以进行拨打电话
     *
     * @param callPhoneLimit 用户某个一级身份对应的拨打电话限制
     * @param userId         用户id
     * @param srcMsgId       货物的原始信息id，重发也不变
     * @param identityStatus 用户身份认证标志1通过2认证中3认证失败
     * @param user
     * @return
     */
    private int checkCallTimeLimit(TytCallPhoneLimit callPhoneLimit, String userId, Long srcMsgId, int identityStatus, String clientVersion, String clientSign, String isNeedPhone, User user) {
        int limitType = 0;
        /*
         * 获取用户当前的拨打电话次数,如果获取不到值则说明用户是第一次打电话，记录拨打电话次数为一次并记录拨打电话日志，然后直接放行
         */
        String today = TimeUtil.formatDate(new Date());
        String callPhoneTime = cacheService.getString(Constant.CALL_PHONE_LIMIT_KEY + "_" + userId + today);
        if (callPhoneTime == null) {
            // 用户从没有拨打过电话则初始化拨打电话次数为0次
            callPhoneTime = "0";
            // 记录拨打电话次数为0次
            cacheService.setObject(Constant.CALL_PHONE_LIMIT_KEY + "_" + userId + today, "0", Constant.CALL_PHONE_TIME_CACHE_TIME);
        }
        List<CallPhoneLimitItemBean> callPhoneLimitItemBeans = new ArrayList<CallPhoneLimitItemBean>();
        // 按照数据库中的排序值进行排序
        sortLimitItem(callPhoneLimit, callPhoneLimitItemBeans);
        logger.info("callPhoneLimitItemBeans: " + callPhoneLimitItemBeans);
        CallPhoneLimitItemBean curCallPhoneLimitItem;
        String curCallPhoneLimitItemName;
        // 用户当前拨打电话的次数
        int userCallPhoneTime = Integer.valueOf(callPhoneTime);
        // 当前以及之前所有限制条件下允许拨打电话的总次数
        int curAndBeforeLimitTime = 0;
        // 只有是新版本并且客户端没有强制要求返回手机号才进行拨打电话限制
        if (isNotForceNeedPhone(clientVersion, isNeedPhone, clientSign)) {
            for (int i = 0; i < callPhoneLimitItemBeans.size(); i++) {
                curCallPhoneLimitItem = callPhoneLimitItemBeans.get(i);
                if (curCallPhoneLimitItem.getSortValue() != 0) {
                    curCallPhoneLimitItemName = curCallPhoneLimitItem.getLimitItemName();
                    curAndBeforeLimitTime += curCallPhoneLimitItem.getCallPhoneLimitTime();
                    if ("carSort".equals(curCallPhoneLimitItemName)) {
                        /*
                         * 判读用户是否进行车辆认证，如果没有则设置限制类型为车辆未认证退出，否则继续判断拨打电话次数是否超过
                         * 当前的限制次数，如果没有则记录拨打电话信息退出，否则继续判断次数
                         * 用户车辆认证状态码：0:未认证；1:认证成功；2：认证失败
                         */
                        if (!"1".equals(user.getIsCar())) {
                            limitType = Constant.USER_CAR_NO_AUTH;
                            break;
                        } else {
                            if (userCallPhoneTime >= curAndBeforeLimitTime && i == callPhoneLimitItemBeans.size() - 1) { // 处理所有都满足条件但还是超过拨打电话次数的情况
                                limitType = Constant.USER_EXCEED_ALL_TIME;
                                break;
                            } else if (userCallPhoneTime >= curAndBeforeLimitTime) {
                                continue;
                            } else { // 一定要break结束循环
                                break;
                            }
                        }
                    } else if ("memberSort".equals(curCallPhoneLimitItemName)) {
                        /*
                         * 判读用户是的付费状态，如果为试用期用户或者缴费过期则设置限制退出，否则继续判断拨打电话次数是否超过
                         * 当前的限制次数，如果没有则记录拨打电话信息退出，否则继续判断次数 用户类型状态码：0试用 1付费 2未激活
                         */
                        int userType = user.getUserType();
                        if (userType == 0 && user.getServeDays() <= 0) { // 试用期用户到期
                            limitType = Constant.USER_NO_PAY;
                            break;
                        } else if (userType == 1 && user.getServeDays() <= 0) { // 缴费用户到期
                            limitType = Constant.USER_PAY_OUT_DATE;
                            break;
                        } else {
                            if (userCallPhoneTime >= curAndBeforeLimitTime && i == callPhoneLimitItemBeans.size() - 1) { // 处理所有都满足条件但还是超过拨打电话次数的情况
                                limitType = Constant.USER_EXCEED_ALL_TIME;
                                break;
                            } else if (userCallPhoneTime >= curAndBeforeLimitTime) {
                                continue;
                            } else { // 一定要break结束循环
                                break;
                            }
                        }
                    } else if ("identitySor".equals(curCallPhoneLimitItemName)) {
                        /*
                         * 判读用户是的身份认证状态，如果身份认证未通过则设置限制退出，否则继续判断拨打电话次数是否超过
                         * 当前的限制次数，如果没有则记录拨打电话信息退出，否则继续判断次数
                         * 用户身份认证状态码：1通过2认证中3认证失败
                         */
                        if (identityStatus != 1) {
                            limitType = Constant.USER_IDENTITY_NO_AUTH;
                            break;
                        } else {
                            if (userCallPhoneTime >= curAndBeforeLimitTime && i == callPhoneLimitItemBeans.size() - 1) { // 处理所有都满足条件但还是超过拨打电话次数的情况
                                limitType = Constant.USER_EXCEED_ALL_TIME;
                                break;
                            } else if (userCallPhoneTime >= curAndBeforeLimitTime) {
                                continue;
                            } else { // 一定要break结束循环
                                break;
                            }
                        }
                    } /* else if () {这里添加之后其他可能的限制类型} */
                }
            }
        }
        /*
         * 如果当前可以拨打电话则记录拨打电话次数和拨打电话日志信息
         */
        if (limitType == 0) {
            // 记录拨打电话次数到缓存中并保存拨打电话记录
            cacheService.del(Constant.CALL_PHONE_LIMIT_KEY + "_" + userId + today);
            cacheService.setObject(Constant.CALL_PHONE_LIMIT_KEY + "_" + userId + today, String.valueOf(Integer.valueOf(userCallPhoneTime) + 1), Constant.CALL_PHONE_TIME_CACHE_TIME);
            // 记录拨打电话日志
            callPhoneRecordService.add(new TytUserCallPhoneRecord(srcMsgId, Long.valueOf(userId)));
        }
        return limitType;
    }

    private void sortLimitItem(TytCallPhoneLimit callPhoneLimit, List<CallPhoneLimitItemBean> callPhoneLimitItemBeans) {
        if (callPhoneLimit.getCarSort() > 0) {
            callPhoneLimitItemBeans.add(new CallPhoneLimitItemBean("carSort", callPhoneLimit.getCarNumber(), callPhoneLimit.getCarSort()));
        }
        if (callPhoneLimit.getMemberSort() > 0) {
            callPhoneLimitItemBeans.add(new CallPhoneLimitItemBean("memberSort", callPhoneLimit.getMemberNumber(), callPhoneLimit.getMemberSort()));
        }
        if (callPhoneLimit.getIdentitySor() > 0) {
            callPhoneLimitItemBeans.add(new CallPhoneLimitItemBean("identitySor", callPhoneLimit.getIdentityNumber(), callPhoneLimit.getIdentitySor()));
        }
        Collections.sort(callPhoneLimitItemBeans, new Comparator<CallPhoneLimitItemBean>() {
            @Override
            public int compare(CallPhoneLimitItemBean o1, CallPhoneLimitItemBean o2) {
                return o1.getSortValue() - o2.getSortValue();
            }
        });
    }

    @Override
    public int saveChangeInfoFeeStatus(String infoFeeStatus, Long srcMsgId) throws Exception {
        // tyt_plat_transport_optimize20171123 货源优化代码
        String updateSQL = "update tyt_transport set info_status=? where src_msg_id=?";
        return this.executeUpdateSql(updateSQL, new Object[]{infoFeeStatus, srcMsgId});
    }

    @Override
    public int updateSrcMsgId(Long goodsId, Long srcMsgId) throws Exception {
        String updateSQL = "update tyt_transport set src_msg_id=?,mtime=? where id=?";
        return this.executeUpdateSql(updateSQL, new Object[]{srcMsgId, new Date(), goodsId});
    }

    @Override
    public int getMaxResendCounts(Long srcMsgId) throws Exception {
        String selectSQL = "select max(resend_counts) from tyt_transport where src_msg_id=? and ctime>=?";
        Integer max = this.getBaseDao().query(selectSQL, new Object[]{srcMsgId, TimeUtil.formatDate(new Date())});
        if (max == null)
            return 0;
        return max.intValue();
    }

    @Override
    public int getCurrentSendCount(Long userId) {
        String selectSQL = "select COUNT(DISTINCT src_msg_id)  from tyt_transport where user_id=? and  ctime>=? and status in(1,4,5)";
        BigInteger max = this.getBaseDao().query(selectSQL, new Object[]{userId, TimeUtil.formatDate(new Date())});
        if (max == null)
            return 0;
        return max.intValue();
    }

    @SuppressWarnings("rawtypes")
    @Override
    public List<Transport> queryListByGoodIds(List<Long> goodIdsList) {
        Map<String, List> paramsMap = new HashMap<String, List>();
        paramsMap.put("idsList", goodIdsList);
        return this.getBaseDao().searchByHqlWithListParam("FROM Transport t WHERE t.id IN (:idsList)", paramsMap);
    }


    @Override
    public long getMyPushTytNbr(Long userId, int status, String isInfoFee) {

        String SQL = "SELECT count(*) from tyt_transport where ctime>=? and user_id=? and status=? and info_status in(?,?)";
        BigInteger c = this.getBaseDao().query(SQL, new Object[]{TimeUtil.formatDate(new Date()), userId, status, "1", "0"});
        if (c == null)
            return 0;
        return c.longValue();


    }

    @Override
    public long getMaxIdbytransId(Long transId) {
        if (transId != null) {
            String sql = " SELECT MAX(id) FROM tyt_transport  t WHERE t.src_msg_id=? ";
            BigInteger newId = this.getBaseDao().query(sql, new Object[]{transId});
            if (newId != null && newId.longValue() > 0) {
                return newId.longValue();
            }
        }
        return 0;
    }


    @Override
    public Transport getLastBySrcMygId(Long goodsId) {
        List<Transport> list = this.getBaseDao().find("FROM Transport t WHERE t.srcMsgId =? order by t.id desc limit 1", new Object[]{goodsId});
        return (list != null && list.size() > 0) ? list.get(0) : null;
    }

    @Override
    public List<Long> getTodayIdListBySrcMsgId(Long srcMsgId, Long userId, Integer newStatus) {
        try {
            StringBuffer sql = null;
            List<Object> params = new ArrayList<Object>();
            params.add(userId);
            params.add(srcMsgId);
            if (newStatus == 0) {
                sql = new StringBuffer("select id from tyt_transport " + " where user_id=? and src_msg_id=? and (status=? or status=? or status=?)  and ctime>=?");
                params.add(1);
                params.add(4);
                params.add(5);
            } else if (newStatus == 4) {
                sql = new StringBuffer("select id from tyt_transport " + " where user_id=? and src_msg_id=? and status=?  and ctime>=?");
                params.add(1);
            } else if (newStatus == 5) {
                sql = new StringBuffer("select id from tyt_transport " + " where user_id=? and src_msg_id=? and status=?  and ctime>=?");
                params.add(1);
            }

            params.add(TimeUtil.formatDateTime(TimeUtil.today()));
            Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
            map.put("id", Hibernate.LONG);
            List<Long> list = this.getBaseDao().search(sql.toString(), map, null, params.toArray(), 1, 10000);
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

    }

    @Override
    public Map<Object, Object> queryRecommendTransport(int queryType, long querySign, int startDistanceValue, TytPreferenceNew preferenceNew) {
        //String  defaultUserId = tytConfigService.getStringValue("tyt_transport_default_user_id", "4");
        int defaultUserIdAppOnoff = tytConfigService.getIntValue("tyt_transport_default_user_id_app_onoff", 0);

        Map<Object, Object> map = new HashMap<Object, Object>();
        long totalSize = 0;
        // 第一次查询大小
        int firstPageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.first.page.size");
        // 缓存时间
//		long cacheTime = AppConfig.getIntProperty("tyt.tyt_transport.query.cache.time");
        // 上滑动下滑动最大结果集大小
        int pageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.page.size").intValue();
        //是否使用es搜索引擎开关
        int esOnOff = tytConfigService.getIntValue("tyt_use_es_onoff", 0);

        // 范围300公里
        long startRange = startDistanceValue != 0 ? (startDistanceValue * 100) : 30000;
        List<TransportBean> returnTbList = null;
        String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
        logger.info("beginTime=" + beginTime);
        List<Object> list = new ArrayList<Object>();
        StringBuffer sb = new StringBuffer(" from tyt_transport where 1=1 ");
        StringBuffer sbSql = new StringBuffer("select `id`,`src_msg_id` srcMsgId,`start_point` startPoint,`dest_point` destPoint,`task_content` taskContent,`status`," +
                "`start_city` startCity,`start_provinc` startProvinc,`start_area` startArea,`dest_provinc` destProvinc,`dest_city` destCity,`dest_area` destArea," +
                "`ctime` pubDate,`start_coord` startCoord,`dest_coord` destCoord,`price`,`nick_name` nickName,`reg_time` regTime,`user_type` userType,`verify_photo_sign` verifyPhotoSign,`is_info_fee` isInfoFee,`android_distance` androidDistance,weight ");
        StringBuffer esSql = new StringBuffer("select id,src_msg_id,start_point,dest_point,task_content,status," +
                "start_city,start_provinc,start_area ,dest_provinc,dest_city ,dest_area ," +
                "ctime ,start_coord ,dest_coord ,price,nick_name ,reg_time ,user_type ,verify_photo_sign ,is_info_fee ,android_distance ,weight  ");

        sb.append("and status=1");
        sb.append(" and display_type=1");
        /** 特运通账户ID 默认ID为261102*/
        String companyAccountUserId = tytConfigService.getStringValue("tyt_company_account_user_id", "261102");
        sb.append(" and (is_standard = 0 or user_id = ");
        sb.append(companyAccountUserId);
        sb.append(") ");
        // 增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表 新精准不限制
//		sb.append(" and is_display=1");
        sb.append(" and ctime>=?");
        if (esOnOff == 0) {
            list.add(beginTime);
        } else
            list.add(TransformUtil.formatGMTDate(beginTime));

        if (preferenceNew != null) {
            long start_coord_x = preferenceNew.getStartCoordX();
            long start_coord_y = preferenceNew.getStartCoordY();
            sb.append(" and start_coord_x>=?");
            list.add(start_coord_x - startRange);
            sb.append(" and start_coord_x<=?");
            list.add(start_coord_x + startRange);
            sb.append(" and start_coord_y>=?");
            list.add(start_coord_y - startRange);
            sb.append(" and start_coord_y<=?");
            list.add(start_coord_y + startRange);

            //目的地
            if (StringUtils.isNotEmpty(preferenceNew.getDestProvinc())) {
                sb.append(" and dest_provinc = ?");
                list.add(preferenceNew.getDestProvinc());
                if (StringUtils.isNotEmpty(preferenceNew.getDestCity())) {
                    sb.append(" and dest_city = ?");
                    list.add(preferenceNew.getDestCity());
                    if (!preferenceNew.getDestCity().equals(preferenceNew.getDestArea())) {
                        sb.append(" and dest_area = ?");
                        list.add(preferenceNew.getDestArea());
                    }
                }
            }

            //TODO 重量区间
            if (preferenceNew.getBeginWeight() != null && preferenceNew.getBeginWeight().intValue() > 0) {
                sb.append(" and refer_weight >= ?");
                list.add(preferenceNew.getBeginWeight());
            }

            if (preferenceNew.getEndWeight() != null && preferenceNew.getEndWeight().intValue() > 0) {
                sb.append(" and refer_weight <= ?");
                list.add(preferenceNew.getEndWeight());
            }
            //TODO 小于长宽高
            if (preferenceNew.getLength() != null && preferenceNew.getLength().intValue() > 0) {
                sb.append(" and refer_length <= ?");
                list.add(preferenceNew.getLength());
            }
            if (preferenceNew.getWide() != null && preferenceNew.getWide().intValue() > 0) {
                sb.append(" and refer_width <= ?");
                list.add(preferenceNew.getWide());
            }
            if (preferenceNew.getHigh() != null && preferenceNew.getHigh().intValue() > 0) {
                sb.append(" and refer_height <= ?");
                list.add(preferenceNew.getHigh());
            }
            //TODO 车型选择
            if (StringUtils.isNotEmpty(preferenceNew.getPreferenceCar())) {
                String carListStr = "";
                if (preferenceNew.getPreferenceCar().indexOf("、") < 0) {
                    carListStr = "'" + preferenceNew.getPreferenceCar() + "'";
                } else {
                    carListStr = "'" + preferenceNew.getPreferenceCar().replace("、", "','") + "'";
                }
                sb.append(" and good_type_name in ( " + carListStr + " )");
            }
        }

        int searchSize = pageSize;
        long t1 = 0, t2 = 0;
        if (querySign <= 1) {
            queryType = 1;
            logger.info("查询时上拉、下滑时客户端数据为空,进行查询类型转换为第一次查询queryType=1");
        }
        // 第一次请求
        if (queryType == 1) {
            //增加ID范围查询，优化SQL语句
            Long todayMinTransId = getTodayMinTransId();
            if (todayMinTransId != null && !"null".equals(todayMinTransId)) {
                sb.append(" and id > ?");
                list.add(todayMinTransId);
            }
            searchSize = firstPageSize;
            // 大小排序
            sb.append(" order by id desc ");
        } else {
            // 下拉查新数据
            if (queryType == 0) {
                sb.append(" and id>?");
                // 小大排序
                sb.append(" order by id desc ");
                list.add(querySign);
            }
            // 上推查历史数据
            else {
                //增加ID范围查询，优化SQL语句
                Long todayMinTransId = getTodayMinTransId();
                if (todayMinTransId != null && !"null".equals(todayMinTransId)) {
                    sb.append(" and id > ?");
                    list.add(todayMinTransId);
                }

                sb.append(" and id<?");
                // 大小排序
                sb.append(" order by id desc ");
                list.add(querySign);
            }
        }
        sbSql.append(sb);
        // 查询数据集
        long t3 = 0, t4 = 0;
        //0时走DB
        if (esOnOff == 0) {


            t3 = System.currentTimeMillis();
//		totalSize = getSerachTotalCount(sb, list.toArray());
//		if (totalSize > 0) {
            Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
            scalarMap.put("id", Hibernate.LONG);
            scalarMap.put("srcMsgId", Hibernate.LONG);
            scalarMap.put("startPoint", Hibernate.STRING);
            scalarMap.put("destPoint", Hibernate.STRING);
            scalarMap.put("startCity", Hibernate.STRING);
            scalarMap.put("startProvinc", Hibernate.STRING);
            scalarMap.put("startArea", Hibernate.STRING);
            scalarMap.put("destProvinc", Hibernate.STRING);
            scalarMap.put("destCity", Hibernate.STRING);
            scalarMap.put("destArea", Hibernate.STRING);
            scalarMap.put("taskContent", Hibernate.STRING);
            scalarMap.put("status", Hibernate.INTEGER);
            scalarMap.put("pubDate", Hibernate.TIMESTAMP);
            scalarMap.put("nickName", Hibernate.STRING);
            scalarMap.put("startCoord", Hibernate.STRING);
            scalarMap.put("destCoord", Hibernate.STRING);
            scalarMap.put("price", Hibernate.STRING);
            scalarMap.put("userType", Hibernate.INTEGER);
            scalarMap.put("verifyPhotoSign", Hibernate.INTEGER);
            scalarMap.put("isInfoFee", Hibernate.STRING);
            scalarMap.put("regTime", Hibernate.TIMESTAMP);
            scalarMap.put("androidDistance", Hibernate.INTEGER);
            //重量 单位:吨
            scalarMap.put("weight", Hibernate.STRING);
            List<TransportRecommendBean> transportList = this.getBaseDao().search(sbSql.toString(), scalarMap, TransportRecommendBean.class, list.toArray(), 1, searchSize);
            t4 = System.currentTimeMillis();
            logger.info("数据库查询时间：" + (t4 - t3) + "ms");
            int size = transportList == null ? 0 : transportList.size();
            logger.info("数据库查询结果集条数：" + size + "条");
            if (transportList != null && transportList.size() > 0) {

                for (TransportRecommendBean trb : transportList) {
                    if (defaultUserIdAppOnoff == 1) {
                        if (trb.getNickName().indexOf("会员") != -1) {
                            trb.setNickName("会员...");
                        }
                    }
                }
                map.put("resultList", transportList);
//				returnTbList = new ArrayList<TransportBean>();
//				for (int i = 0; i < size; i++) {
//					Transport t = transportList.get(i);
//					TransportBean tb = new TransportBean();
//					// copy改简单的
//					BeanUtils.copyProperties(t, tb);
//					tb.setRemark(null);
//					tb.setPubDate(t.getCtime());
//					returnTbList.add(tb);
//				}
//				transportListJsonStr = JSON.toJSONString(returnTbList);
//				if (queryType == 1) {
//					// 保存到缓存中 tbList
//					cacheService.setString(getTransportCacheKey(start_coord_x, start_coord_y, dest_coord_x, dest_coord_y, queryType, querySign, startRange, destRange), transportListJsonStr, cacheTime);
//				}
            }
            map.put("totalSize", size);
//		}
        } else {//1时走es
            List<TransportRecommendBean> esList = null;
            esSql.append(sb);
            t3 = System.currentTimeMillis();
            Map<String, Object> esmap = ElasticTransportHelper.findMapList(esSql.toString(), 0, searchSize, list);
            List<Transport> transportList = (List<Transport>) esmap.get("rows");
            totalSize = (Long) esmap.get("total");

            t4 = System.currentTimeMillis();
            logger.info("es查询时间：" + (t4 - t3) + "ms");
            int size = transportList == null ? 0 : transportList.size();
            logger.info("es查询结果集条数：" + size + "条");
            if (transportList != null && transportList.size() > 0) {
                esList = new ArrayList<TransportRecommendBean>();
                for (int i = 0; i < size; i++) {
                    Transport t = transportList.get(i);
                    TransportRecommendBean tb = new TransportRecommendBean();
                    // copy改简单的
                    BeanUtils.copyProperties(t, tb);
                    tb.setPubDate(t.getCtime());
                    if (defaultUserIdAppOnoff == 1) {
                        //String userId=String.valueOf(t.getUserId());
                        if (tb.getNickName().indexOf("会员") != -1) {
                            //nickName=nickName.substring(2);
                            tb.setNickName("会员...");
                        }
                    }

                    esList.add(tb);
                }
            }

            map.put("resultList", esList);
            map.put("totalSize", size);
        }
        return map;
    }

    @Override
    public void deleteLog(long userId, long srcMsgId, int clientSign) {
        String sql = "INSERT INTO tytlog.tyt_transport_recommend_delete_log (src_msg_id, user_id, plat_id, ctime) VALUES  (?, ?, ?, ?)";
        Object[] params = new Object[]{srcMsgId, userId, clientSign, new Date()};
        this.getBaseDao().executeUpdateSql(sql, params);
    }

    @Override
    public CallLogBeanNew getPhoneByGoodIdNew(String userId, String goodId, String clientVersion, String clientSign,
                                              String moduleType, String isNeedPhone) throws Exception {
        CallLogBeanNew callLogBeanNew = new CallLogBeanNew();
        // 优化成一条记录，查找电话取srcid，再查询
        Transport transport = transportBusiness.getByGoodsIdForUnLock(Long.valueOf(goodId)); // 因iOS
        String sql = "SELECT tsm.`tel`, tsm.`tel3`, tsm.`tel4`, tsm.`src_msg_id` srcMsgId,tsm. ts_order_no tsOrderNo,tsm.is_info_fee isInfoFee FROM tyt_transport_main tsm WHERE tsm.`id`=?";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("tel", Hibernate.STRING);
        scalarMap.put("tel3", Hibernate.STRING);
        scalarMap.put("tel4", Hibernate.STRING);
        scalarMap.put("srcMsgId", Hibernate.LONG);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("isInfoFee", Hibernate.STRING);
        Long srcMsgId = transport.getSrcMsgId();
        List<CallLogBeanNew> callLogBeans = this.getBaseDao().search(sql, scalarMap, CallLogBeanNew.class,
                new Object[]{srcMsgId});
        if (callLogBeans.size() >= 1) {
            callLogBeanNew = callLogBeans.get(0);
            /*
             * 根据货物src_msg_id用户id判断用户在当天是否拨打过该货物的电话， 如果打过则直接放行执行后续操作，否则进行拨打电话限制次数判断
             */
            TytUserCallPhoneRecord callPhoneRecord = callPhoneRecordService
                    .getBySrcMsgIdAndUserId(callLogBeanNew.getSrcMsgId(), userId);
            /*
             * 查询用户拨打是否电话标注过该货物
             */
            scalarMap.clear();
            scalarMap.put("callResultCode", Hibernate.STRING);
            sql = "SELECT tacl.`call_result_code` AS 'callResultCode' FROM tyt_app_call_log tacl WHERE tacl.`caller_id`=? AND tacl.`src_msg_id`=?";
            callLogBeans = this.getBaseDao().search(sql, scalarMap, CallLogBeanNew.class,
                    new Object[]{userId, callLogBeanNew.getSrcMsgId()});
            if (callLogBeans.size() == 0) {
                callLogBeanNew.setCallStatus("1_0");
            } else {
                callLogBeanNew.setCallStatus("2_" + callLogBeans.get(0).getCallResultCode());
            }
            // 是否支付过订单
            String hasMakeOrder = "0";
            if (callLogBeanNew.getIsInfoFee().equals("1")) {
                SimpleOrderBean myLastOrder = transportOrdersService
                        .getLastOrderByPayUser(callLogBeanNew.getTsOrderNo(), Long.parseLong(userId));
                if (myLastOrder != null) {
                    hasMakeOrder = "1";
                }
            }
            callLogBeanNew.setHasMakeOrder(hasMakeOrder);
            // 当天没有拨打过才执行后续判断逻辑
            if (callPhoneRecord == null) {
                User user = userService.getById(Long.valueOf(userId));
                int userType = user.getUserType();
                // 会员有效期内用户
                boolean isInVipTime = (userType == 1 && user.getServeDays() > 0);
                Integer maxCallTime = userAndCallTime.get(userId);
                logger.info("transport service single user limit isInVipTime: " + isInVipTime + " , maxCallTime: "
                        + maxCallTime);
                // 如果用户在指定条数限制名单中并且不是付费有效期内会员则使用指定条数限制规则
                if (!isInVipTime && maxCallTime != null) {
                    String userCallTimeKey = Constant.SINGLE_USER_CALL_TIME_CACHE_KEY + "_" + userId + "_"
                            + TimeUtil.formatDate(new Date());
                    // 查询用户当前的访问次数
                    String userCurCallTime = RedisUtil.get(userCallTimeKey);
                    logger.info("transport service single user limit userCallTimeKey: " + userCallTimeKey
                            + " , userCurCallTime: " + userCurCallTime);
                    if (StringUtils.isEmpty(userCurCallTime)) {
                        RedisUtil.set(userCallTimeKey, "1", (int) Constant.CACHE_EXPIRE_TIME_24H);
                    } else {
                        if (Integer.valueOf(userCurCallTime) >= maxCallTime) {
                            logger.info("transport service single user limit clear tel");
                            // 清空电话
                            configLimitType(callLogBeanNew, Constant.USER_EXCEED_ALL_TIME_NEW, Constant.USER_EXCEED_ALL_TIME_PROMPT,
                                    null);
                        } else {
                            logger.info("transport service single user limit incr call time");
                            // 增加获取电话次数
                            RedisUtil.incr(userCallTimeKey);
                            // 记录拨打电话日志
                            callPhoneRecordService.add(
                                    new TytUserCallPhoneRecord(callLogBeanNew.getSrcMsgId(), Long.valueOf(userId)));
                        }
                    }
                } else {
                    // 销售审核一级身份
                    String deliverLevelOne = user.getDeliver_type_one();
                    deliverLevelOne = StringUtils.isEmpty(deliverLevelOne) ? "-1" : deliverLevelOne;
                    logger.info("transport service getphone new deliverLevelOne: " + deliverLevelOne);
                    // 获取用户的一级身份
                    TytUserIdentityAuth userIdentityAuth = userIdentityAuthService.getByUserId(userId);
                    if (userIdentityAuth == null) {
                        userIdentityAuth = new TytUserIdentityAuth();
                    }
                    TytCallPhoneLimitNew callPhoneLimitNew = new TytCallPhoneLimitNew();
                    // 如果为空则查询默认销售审核一级身份
                    List<TytCallPhoneLimitNew> callPhoneLimitNewList = callPhoneLimitNewService
                            .getList(" verifyLevelOne=" + Byte.valueOf(deliverLevelOne) + " AND STATUS=1 ", null);
                    // 如果用户对应的销售审核一级身份没有配置相应信息则获取默认的
                    if (callPhoneLimitNewList == null || callPhoneLimitNewList.size() == 0) {
                        callPhoneLimitNewList = callPhoneLimitNewService
                                .getList(" verifyLevelOne=-1 AND STATUS=1 ", null);
                    }
                    callPhoneLimitNew = callPhoneLimitNewList.get(0);
                    String today = TimeUtil.formatDate(new Date());
                    logger.info("transport service getphone new callPhoneLimitNew: " + callPhoneLimitNew);
                    int curCallTimes = curCallTimes(user.getId(), today);
                    // 判断是从大库（找货列表）还是有好货拨打的电话，因为二者规则不同所以需要分开处理
                    // 1精准货源列表或详情，2 货源列表或详情
                    if ("1".equals(moduleType)) { // 精准货源打电话逻辑
                        logger.info("transport service get phone new youhaohuo");
                        // 判断是否还有剩余天数（会员缴费和赠送天数两类）
                        // 如果是继续后续逻辑，否则直接提示缴费
                        if (user.getServeDays() <= 0) {
                            logger.info("transport service get phone new youhaohuo not in vip clear tel");
                            configLimitType(callLogBeanNew, Constant.USER_PAY_OUT_DATE_YOUHAOHUO_NEW, null,
                                    Constant.USER_PAY_OUT_DATE_YOUHAOHUO_BOTTOM_PROMPT);
                        } else {
                            // 查询当前可拨打总条数 总次数=车辆认证通过次数+身份认证通过次数+会员有效期内次数
                            int totalNum = totalCanCallNum(user, callPhoneLimitNew, userIdentityAuth);
                            if (curCallTimes >= totalNum) {
                                // 如果总次数已经用完则提示总次数用完
                                logger.info("transport service get phone new youhaohuo used all call time clear tel");
                                configLimitType(callLogBeanNew, Constant.USER_EXCEED_ALL_TIME_NEW, Constant.USER_EXCEED_ALL_TIME_PROMPT,
                                        null);
                            } else {
                                // 增加拨打电话次数
                                // 记录拨打电话次数到缓存中并保存拨打电话记录
                                incrCallNum(userId, today, curCallTimes, srcMsgId);
                            }
                        }
                    } else { // 找货列表打电话逻辑
                        // 判断是否通过车辆认证,以下条件均为车辆认证通过
                        // 1：有通过认证车辆
                        // 2：注册用户有首次提交车辆且认证状态为认证中
                        // 3：最后一次会员费付款时间在某个时间点前，并且还在有效期内
                        String isCar = user.getIsCar();
                        boolean hasAuthCar = "1".equals(isCar);
                        // 是否为第一次认证车辆且车辆在认证中标记
                        boolean isFirstAuthCar = isFirstAuthCar(userId);
                        // 是否在某个时间点前付费，并且还在有效期内
                        boolean isBeforeTimeVip = isBeforeVip(user);
                        if (!(hasAuthCar || isFirstAuthCar || isBeforeTimeVip)) {
                            configLimitType(callLogBeanNew, Constant.USER_CAR_NO_AUTH_NEW, null,
                                    StringUtils.replace(Constant.USER_CAR_NO_AUTH_BOTTOM_PROMPT, "{callTime}",
                                            String.valueOf(callPhoneLimitNew.getCarAuthNum())));
                        } else {
                            // 总次数是否用完，用完在依次判断身份认证和会员
                            int totalCanCallNum = totalCanCallNum(user, callPhoneLimitNew, userIdentityAuth);
                            if (curCallTimes >= totalCanCallNum) {
                                // 判断是否通过身份认证，否提示身份认证
                                // 用户身份认证状态码：1通过2认证中3认证失败
                                Integer identityStatus = userIdentityAuth.getIdentityStatus();
                                if (!(identityStatus != null && identityStatus.intValue() == 1)) { // 判断身份认证
                                    configLimitType(callLogBeanNew, Constant.USER_IDENTITY_NO_AUTH_NEW,
                                            StringUtils.replace(Constant.USER_IDENTITY_NO_AUTH_TOP_PROMPT, "{callTime}",
                                                    String.valueOf(totalCanCallNum)),
                                            StringUtils.replace(Constant.USER_IDENTITY_NO_AUTH_BOTTOM_PROMPT,
                                                    "{callTime}",
                                                    String.valueOf(callPhoneLimitNew.getIdentityAuthNum())));
                                } else if (user.getServeDays() == null || user.getServeDays().intValue() <= 0) { // 判断会员
                                    configLimitType(callLogBeanNew, Constant.USER_PAY_OUT_DATE_DAKU_NEW,
                                            StringUtils.replace(Constant.USER_PAY_OUT_DATE_DAKU_TOP_PROMPT,
                                                    "{callTime}", String.valueOf(curCallTimes)),
                                            Constant.USER_PAY_OUT_DATE_DAKU_BOTTOM_PROMPT);
                                } else {
                                    configLimitType(callLogBeanNew, Constant.USER_EXCEED_ALL_TIME_NEW, Constant.USER_EXCEED_ALL_TIME_PROMPT,
                                            null);
                                }
                            } else {
                                // 增加拨打电话次数
                                // 记录拨打电话次数到缓存中并保存拨打电话记录
                                incrCallNum(userId, today, curCallTimes, srcMsgId);
                            }
                        }
                    }
                }
            }
        }
        return callLogBeanNew;
    }

    private void incrCallNum(String userId, String today, int curCallTimes, Long srcMsgId) {
        cacheService.del(Constant.CALL_PHONE_LIMIT_KEY + "_" + userId + today);
        cacheService.setObject(Constant.CALL_PHONE_LIMIT_KEY + "_" + userId + today, String.valueOf(curCallTimes + 1), Constant.CALL_PHONE_TIME_CACHE_TIME);
        // 记录拨打电话日志
        callPhoneRecordService.add(new TytUserCallPhoneRecord(srcMsgId, Long.valueOf(userId)));
    }

    private void configLimitType(CallLogBeanNew callLogBeanNew, int limitType, String topContent, String bottomContent) {
        // 清空电话
        callLogBeanNew.clearTel();
        callLogBeanNew.setIsCanCall(1);
        callLogBeanNew.setLimitType(limitType);
        callLogBeanNew.setTopContent(topContent);
        callLogBeanNew.setBottomContent(bottomContent);
    }

    private boolean isFirstAuthCar(String userId) {
        boolean isFirstAuthCar = false;
        List<CarLog> carLogs = carlogService.getList(" updateType!='4' AND userId= " + Long.valueOf(userId), null);
        // 没有认证历史则继续判断是否有认证中车辆
        if (carLogs == null || carLogs.size() == 0) {
            List<Car> cars = carService.getList(" userId= " + Long.valueOf(userId) + " AND auth='0' AND isDelete=1", null);
            if (cars != null && cars.size() > 0) {
                isFirstAuthCar = true;
            }
        }
        return isFirstAuthCar;
    }

    private boolean isBeforeVip(User user) throws Exception {
        boolean isBeforeTimeVip = false;
        // 最后一次付款时间
        List<TytSuccessAccount> successAccountList = successAccountService.getList(" userId= " + user.getId() + " order by id desc ", new PageBean(1, 1));
        Date userLastPayDate = null;
        if (successAccountList != null && successAccountList.size() == 1) {
            userLastPayDate = successAccountList.get(0).getPayDate();
        } else {
            userLastPayDate = TimeUtil.parseDateString("2118-12-12 10:10:10");
        }
        if (userLastPayDate != null) {
            Long userLastPayDateInMilli = userLastPayDate.getTime();
            // 获取最后付费的时间点
            String lastPayDate = tytConfigService.getStringValue(Constant.PAY_LAST_TIME_KEY, "2018-04-20 00:00:00");
            Long lastPayDateInMilli = TimeUtil.parseDateString(lastPayDate).getTime();
            if (userLastPayDateInMilli < lastPayDateInMilli && user.getServeDays() != null && user.getServeDays().intValue() > 0) {
                isBeforeTimeVip = true;
            }
        }
        return isBeforeTimeVip;
    }

    private int curCallTimes(Long userId, String today) {
        /*
         * 获取用户当前的拨打电话次数,如果获取不到值则说明用户是第一次打电话，记录拨打电话次数为一次并记录拨打电话日志，然后直接放行
         */
        String callPhoneTime = cacheService.getString(Constant.CALL_PHONE_LIMIT_KEY + "_" + userId + today);
        if (callPhoneTime == null) {
            // 用户从没有拨打过电话则初始化拨打电话次数为0次
            callPhoneTime = "0";
            // 记录拨打电话次数为0次
            cacheService.setObject(Constant.CALL_PHONE_LIMIT_KEY + "_" + userId + today, "0", Constant.CALL_PHONE_TIME_CACHE_TIME);
        }
        return Integer.valueOf(callPhoneTime);
    }

    /**
     * 获取当前用户可拨打货源总条数
     *
     * @param user
     * @return
     */
    private int totalCanCallNum(User user, TytCallPhoneLimitNew callPhoneLimitNew, TytUserIdentityAuth userIdentityAuth) {
        // 用户身份认证状态码：1通过2认证中3认证失败
        Integer identityStatus = userIdentityAuth.getIdentityStatus();
        // 用户车辆认证状态码：0:未认证；1:认证成功；2：认证失败
        String isCar = user.getIsCar();
        // 是否为第一次认证车辆且车辆在认证中标记
        boolean isFirstAuthCar = isFirstAuthCar(String.valueOf(user.getId()));
        int totalNum = 0;
        // 身份认证通过增加身份认证的次数
        if (identityStatus != null && identityStatus.intValue() == 1) {
            totalNum += callPhoneLimitNew.getIdentityAuthNum();
        }
        // 车辆认证通过增加车辆认证的次数
        if ("1".equals(isCar) || isFirstAuthCar) {
            totalNum += callPhoneLimitNew.getCarAuthNum();
        }
        // 会员内（非缴费有剩余赠送天数，会员有剩余天数）
        if (user.getServeDays().intValue() > 0) {
            totalNum += callPhoneLimitNew.getVipNum();
        }
        return totalNum;
    }

    private void handleNormalTimeout(CallLogBeanWithRights callLogBean) {
        handleTel(callLogBean, Constant.NORMAL_TIMEOUT_PROMPTTYPE,
                Constant.NORMAL_TIMEOUT_PROMPTCONTENT, true);
    }

    private void handleVipTimeout(CallLogBeanWithRights callLogBean) {
        handleTel(callLogBean, Constant.VIP_TIMEOUT_PROMPTTYPE,
                Constant.VIP_TIMEOUT_PROMPTCONTENT, true);
    }

    private void handleExpericeTimeout(CallLogBeanWithRights callLogBean) {
        handleTel(callLogBean, tytConfigService
                        .getIntValue("rights_experience_timeout_car", 1) == 1
                        ? Constant.EXPERIENCE_TIMEOUT_CAR_PROMPTTYPE
                        : Constant.EXPERIENCE_TIMEOUT_NOCAR_PROMPTTYPE,
                Constant.EXPERIENCE_TIMEOUT_PROMPTCONTENT, true);
    }

    private void handleVip(CallLogBeanWithRights callLogBean, TytUserSub userSub) {// 是否身份认证通过
        TytUserIdentityAuth identityAuth = userIdentityAuthService
                .getByUserId(String.valueOf(userSub.getUserId()));
        if (identityAuth != null && identityAuth.getIdentityStatus() == 1) {
            boolean notAllowCall = false;
            // 剩余天数是否小于等于3天
            User user = userService.getById(userSub.getUserId());
            // 剩余次数和剩余天数
            int alreadyCalledNum = callPhoneRecordService
                    .getTodayCountByUserGroup(userSub.getUserGroup(), userSub.getUserId());
            int allowCallNum = tytConfigService.getIntValue("rights_vip_call_num", 500);
            if (alreadyCalledNum >= allowCallNum) {
                notAllowCall = true;
                handleTel(callLogBean, Constant.VIP_TODAY_OVER_PROMPTTYPE,
                        Constant.VIP_TODAY_OVER_PROMPTCONTENT, true);
            } else if (user.getServeDays() != null && user.getServeDays() <= 3) {
                handleTel(callLogBean, Constant.VIP_LESS_THREE_PROMPTTYPE,
                        Constant.VIP_LESS_THREE_PROMPTCONTENT, false);
            }
            if (!notAllowCall) {
                // 添加拨打电话记录
                addPhoneRecord(callLogBean.getSrcMsgId(), userSub.getUserGroup(), userSub.getUserId());
            }
        } else {
            handleTel(callLogBean, Constant.VIP_NO_IDENTITY_PROMPTTYPE,
                    Constant.VIP_NO_IDENTITY_PROMPTCONTENT, true);
        }
    }

    private void handleNormal(CallLogBeanWithRights callLogBean, TytUserSub userSub) throws Exception {
        // 是否身份认证通过
        TytUserIdentityAuth identityAuth = userIdentityAuthService
                .getByUserId(String.valueOf(userSub.getUserId()));
        if (identityAuth != null && identityAuth.getIdentityStatus() == 1) {
            boolean notAllowCall = false;
            // 是否车辆认证
            User user = userService.getById(userSub.getUserId());
            if (user != null && !"1".equals(user.getIsCar())) {
                notAllowCall = true;
                handleTel(callLogBean, Constant.NORMAL_NO_CAR_PROMPTTYPE,
                        Constant.NORMAL_NO_CAR_PROMPTCONTENT, true);
            } else {
                // 剩余次数和剩余天数
                int alreadyCalledNum = callPhoneRecordService
                        .getCountByUserGroup(userSub.getUserGroup(), userSub.getUserId());
                int allowCallNum = tytConfigService.getIntValue("rights_normal_call_num", 20);
                if (userSub.getLevel2BigingTime() == null) {
                    userSub.setLevel2BigingTime(new Date());
                    userSubService.updateLevelBeginTime(user.getId(), new Date());
                }
                int passedDays = TimeUtil.daysBetween(userSub.getLevel2BigingTime(), new Date());
                int notifyDays = tytConfigService.getIntValue("rights_normal_call_day", 20) - 3;
                if ((passedDays >= notifyDays
                        || alreadyCalledNum + 3 >= allowCallNum) && alreadyCalledNum < allowCallNum) {
                    handleTel(callLogBean, Constant.NORMAL_LESS_THREE_PROMPTTYPE,
                            Constant.NORMAL_LESS_THREE_PROMPTCONTENT, false);
                } else if (alreadyCalledNum + 1 == allowCallNum) { // 最后一次次数
                    handleTel(callLogBean, Constant.NORMAL_LAST_PROMPTTYPE,
                            Constant.NORMAL_LAST_PROMPTCONTENT, false);
                    updateNormalTimeout(userSub);
                } else if (alreadyCalledNum + 1 > allowCallNum) { // 无剩余次数
                    notAllowCall = true;
                    // 更改用户状态为试用会员已过期
                    updateNormalTimeout(userSub);
                }
            }
            if (!notAllowCall) {
                // 添加拨打电话记录
                addPhoneRecord(callLogBean.getSrcMsgId(), userSub.getUserGroup(), userSub.getUserId());
            }
        } else {
            handleTel(callLogBean, Constant.NORMAL_NO_IDENTITY_PROMPTTYPE,
                    Constant.NORMAL_NO_IDENTITY_PROMPTCONTENT, true);
        }
    }

    private void updateNormalTimeout(TytUserSub userSub) {
        // 更改用户状态为试用会员已过期
        userSubService.updateUserGroup(userSub.getUserId(), Constant.RIGHTS_NORMAL_TIMEOUT);
        userSubService.updateCache(userSub.getUserId());
    }

    private void handleExperience(CallLogBeanWithRights callLogBean, TytUserSub userSub, boolean hasCalled) throws Exception {
        // 是否身份认证通过
        TytUserIdentityAuth identityAuth = userIdentityAuthService
                .getByUserId(String.valueOf(userSub.getUserId()));
        if (identityAuth != null && identityAuth.getIdentityStatus() == 1) {
            int alreadyCalledNum = callPhoneRecordService
                    .getCountByUserGroup(userSub.getUserGroup(), userSub.getUserId());
            int allowCallNum = tytConfigService.getIntValue("rights_experice_call_num", 3);
            logger.info("allowCallNum: " + allowCallNum + ", alreadyCalledNum: " + alreadyCalledNum);
            if (hasCalled) {
                handleTel(callLogBean, Constant.EXPERIENCE_HAVE_CALL_PROMPTTYPE,
                        StringUtils.replace(Constant.EXPERIENCE_HAVE_CALL_PROMPTCONTENT,
                                "{num}", String.valueOf(allowCallNum - alreadyCalledNum)),
                        false);
                return;
            }
            boolean notAllowCall = false;
            if (alreadyCalledNum + 1 < allowCallNum) {
                handleTel(callLogBean, Constant.EXPERIENCE_HAVE_CALL_PROMPTTYPE,
                        StringUtils.replace(Constant.EXPERIENCE_HAVE_CALL_PROMPTCONTENT,
                                "{num}", String.valueOf(allowCallNum - alreadyCalledNum - 1)),
                        false);
            } else if (alreadyCalledNum + 1 == allowCallNum) {
                handleTel(callLogBean, Constant.EXPERIENCE_HAVE_NO_CALL_PROMPTTYPE, Constant.EXPERIENCE_HAVE_NO_CALL_PROMPTCONTENT,
                        false);
                callLogBean.setUpdateCache(5);
                // 更改用户状态为体验用户已过期
                userSubService.updateUserGroup(userSub.getUserId(), Constant.RIGHTS_EXPERIENCE_TIMEOUT);
            } else if (alreadyCalledNum >= allowCallNum) {
                notAllowCall = true;
                // 更改用户状态为体验用户已过期
                userSubService.updateUserGroup(userSub.getUserId(), Constant.RIGHTS_EXPERIENCE_TIMEOUT);
                handleTel(callLogBean, Constant.EXPERIENCE_NO_IDENTITY_PROMPTTYPE,
                        Constant.EXPERIENCE_NO_IDENTITY_PROMPTCONTENT, true);
            }
            if (!notAllowCall) {
                // 添加拨打电话记录
                addPhoneRecord(callLogBean.getSrcMsgId(), userSub.getUserGroup(), userSub.getUserId());
            }
        } else {
            handleTel(callLogBean, Constant.EXPERIENCE_NO_IDENTITY_PROMPTTYPE,
                    Constant.EXPERIENCE_NO_IDENTITY_PROMPTCONTENT, true);
        }
    }

    private void addPhoneRecord(Long srcMsgId, Integer userGroup, Long userId) {
        TytUserCallPhoneRecord record = new TytUserCallPhoneRecord();
        record.setCtime(new Date());
        record.setLevel(Long.valueOf(userGroup));
        record.setTsId(srcMsgId);
        record.setUserId(userId);
        callPhoneRecordService.add(record);
    }

    private void handleTel(CallLogBeanWithRights callLogBean, int promptType, String promptContent, boolean clear) {
        if (clear) {
            callLogBean.clearTel();
            callLogBean.setIsCanCall(1);
        }
        callLogBean.setPromptType(promptType);
        callLogBean.setPromptContent(promptContent);
    }

    private boolean bindDevice(TytUserSub userSub) {
        return userSub.getBindStatus() != null && userSub.getBindStatus() == 2;
    }

    private boolean forceNeedPhoneWithRights(String isNeedPhone) {
        return "1".equals(isNeedPhone);
    }

    /**
     * 扰乱设定用户获取的电话号码。
     *
     * @param callLogBean 电话对象
     * @param userId      用户Id
     */

    private void disturbPhone(CallLogBeanWithRights callLogBean, String userId) {

        // 设置爬虫用户返回手机号码加扰乱码, 参数等于1时执行；
        boolean isOpenDisturb = tytConfigService.isEqualsOne(Constant.IS_DISTURB);
        if (isOpenDisturb) {
            String userStr = tytConfigService.getStringValue(Constant.DISTURB_USER_ID);
            // 用户存在配置中
            if (userStr.indexOf("|" + userId + "|") > -1) {
                //用户存在扰乱中则
                String disturbPhone = "";
                if (StringUtils.isNotBlank(callLogBean.getTel())) {
                    disturbPhone = StringUtil.disturbPhone(callLogBean.getTel());
                    callLogBean.setTel(disturbPhone);
                }

                if (StringUtils.isNotBlank(callLogBean.getTel3())) {
                    disturbPhone = StringUtil.disturbPhone(callLogBean.getTel3());
                    callLogBean.setTel3(disturbPhone);
                }
                if (StringUtils.isNotBlank(callLogBean.getTel4())) {
                    disturbPhone = StringUtil.disturbPhone(callLogBean.getTel4());
                    callLogBean.setTel4(disturbPhone);
                }


            }

        }


    }

    @Override
    public int disableTransport(long srcMsgId) {
        String updateSQL = "update tyt_transport set display_type = '0',status= 0,mtime=? where src_msg_id=? and display_type ='1' and status=1 ";
        return this.executeUpdateSql(updateSQL, new Object[]{new Date(), srcMsgId});

    }

    @Override
    public int noDisplayTransport(long srcMsgId) {
        String updateSQL = "update tyt_transport set display_type = '0' ,mtime=? where src_msg_id=? and display_type = '1' and status=1 ";
        return this.executeUpdateSql(updateSQL, new Object[]{new Date(), srcMsgId});

    }


    @Override
    @Deprecated
    public void updateSimilarityCode(Transport tb, TransportMain tbm) {
        String todayDate = TimeUtil.formatDate(tb.getCtime());
        String startProvinc = tb.getStartProvinc();
        String startCity = tb.getStartCity();
        String destProvinc = tb.getDestProvinc();
        String destCity = tb.getDestCity();
        Integer matchItemId = tb.getMatchItemId();
        String taskContent = GoodsUnique.extractKeyInfo(tb.getTaskContent());

		/*
		  2019-10-22 用户新的方案进行替代
		if (tb.getIsStandard() == 1) { // 非标货源，直接结束

			//更新基本信息
			String simCode = UUID.randomUUID().toString();
			this.updateSimilarityCode(tb.getId(), simCode, null, null); //transport表
			this.updateSimilarityCodeForMain(tb.getSrcMsgId(), simCode, null, null); //transport_main表
			return;
		}


		//规则：今天的日期 + 出发地省 + 出发地市 + 目的地省 + 目的地市 + 标准化ID
		String connectStr = todayDate + startProvinc + startCity + destProvinc + destCity + matchItemId.toString();

		//相似code
		String similarityCode = MD5Util.GetMD5Code(connectStr);
		*/


        //规则：今天的日期 + 出发地省 + 出发地市 + 目的地省 + 目的地市 + 标准化ID
        String connectStr = todayDate + startProvinc + startCity + destProvinc + destCity + taskContent;
        logger.info("connectStr=" + connectStr);

        //相似code
        String similarityCode = MD5Util.GetMD5Code(connectStr);


        //首发ID
        Long similarityFirstId = tb.getSrcMsgId();
        //首发货物基本信息
        //	String similarityFirstInfo = tb.getType()+tb.getGoodTypeName() + "##" + tb.getNickName() + "##" + tb.getUserType() + "##" + tb.getIsInfoFee();
        String similarityFirstInfo = taskContent + "##" + tb.getNickName() + "##" + tb.getUserType() + "##" + tb.getIsInfoFee() +"##" + tb.getRegTime().getTime();
        Transport similarityTb = this.getFirstBySameCode(similarityCode);
        if (similarityTb != null) {
            similarityFirstId = similarityTb.getSimilarityFirstId();
            similarityFirstInfo = similarityTb.getSimilarityFirstInfo();
        }

        //设置基本信息
        tb.setSimilarityCode(similarityCode);
        tb.setSimilarityFirstId(similarityFirstId);
        tb.setSimilarityFirstInfo(similarityFirstInfo);
        //设置main表信息
        tbm.setSimilarityCode(similarityCode);
        tbm.setSimilarityFirstId(similarityFirstId);
        tbm.setSimilarityFirstInfo(similarityFirstInfo);
    }

    private String genSimilarityCode(Transport tb, String taskContentKey){
        logger.info("similarity_content_key：货源ID:{}, 货物内容:{}, 提取词:{}", tb.getSrcMsgId(), tb.getTaskContent(), taskContentKey);

        String todayDate = TimeUtil.formatDate(tb.getCtime());
        String startProvinc = tb.getStartProvinc();
        String startCity = tb.getStartCity();
        String startArea = tb.getStartArea();
        String destProvinc = tb.getDestProvinc();
        String destCity = tb.getDestCity();
        String destArea = tb.getDestArea();

        String goodTypeName = StringUtils.defaultIfBlank(tb.getGoodTypeName(), "");
        String type = StringUtils.defaultIfBlank(tb.getType(), "");
        String brand = StringUtils.defaultIfBlank(tb.getBrand(), "");

        // 重量统一改成保留2位小数
        String weight = StringUtils.isBlank(tb.getWeight()) ? "" :
                new BigDecimal(tb.getWeight()).setScale(2, RoundingMode.HALF_UP).toString();

        //规则：今天的日期 + 出发地省 + 出发地市 + 目的地省 + 目的地市 + 标准化ID
//        String similarityConnect = todayDate + startProvinc + startCity + startArea + destProvinc + destCity + destArea + weight + taskContentKey;
        String similarityConnect = todayDate + startProvinc + startCity + startArea + destProvinc + destCity + destArea + weight;
        if (StringUtils.isBlank(brand) && StringUtils.isBlank(goodTypeName) && StringUtils.isBlank(type)) {
            similarityConnect = similarityConnect + StringUtils.substring(tb.getTaskContent(), 0, 10);
        } else {
            similarityConnect = similarityConnect + brand + goodTypeName + type;
        }
        try {
            // 先判断抽佣货源的开关，走抽佣货源的逻辑
            Integer similaritySwitch = tytConfigService.getIntValue("commission_similarity_switch", 0);
            // 0：折叠  1：不折叠
            if (similaritySwitch == 1) {
                logger.info("走抽佣货源逻辑，similarity_connect = " + similarityConnect);
                String labelJson = tb.getLabelJson();
                if (StringUtils.isNotBlank(labelJson)) {
                    TransportLabelJson transportLabelJson = JSON.parseObject(labelJson, TransportLabelJson.class);
                    // 抽佣货源
                    if (transportLabelJson.getCommissionTransport() != null && transportLabelJson.getCommissionTransport() == 1) {
                        // 抽佣货源
                        similarityConnect = similarityConnect + tb.getUserId();
                    }
                }
            }

            // 6710需求：价格>=优车最低值，不折叠
            if (TransportUtil.hasPrice(tb.getPrice())) {
                TransportMain transportMain = BeanUtil.copyProperties(tb, TransportMain.class);
                CarryPriceVo thPrice = excellentPriceConfigService.getThPrice(transportMain);
                if (thPrice != null) {
                    boolean foldSwitch = Integer.parseInt(tb.getPrice()) >= thPrice.getFixPriceMin();
                    if (foldSwitch) {
                        similarityConnect = similarityConnect + tb.getUserId();
                    }
                }
            }
        } catch (TytException e) {
            // ignore, 不满足abtest条件会抛异常
        }
        logger.info("similarity_connect = " + similarityConnect);

        //相似code
        String similarityCode = MD5Util.GetMD5Code(similarityConnect);
        // 如果管理后台手动修改过similarityCode，则需要拼接后缀
        if (tb.getSrcMsgId() != null && RedisUtil.get("tyt:goods:similarity:change:" + tb.getSrcMsgId()) != null) {
            similarityCode += "_" + (int) (Math.random() * 1000);
        }
        return similarityCode;
    }

    @Override
    @EnableAbTest(code = "is_fold_quality_ts", userId = "#tb.userId", types = {0})
    public String modifyQualityGoodSimilarCode(Transport tb, String similarityConnect) throws TytException {
        if (Objects.equals(1, tb.getExcellentGoods())) {
            // 0 不折叠；1 折叠
            // 不折叠才需要修改 similarityConnect, 折叠不需要修改, 所以 types = {0} 时修改
            return similarityConnect + tb.getUserId();
        } else {
            return similarityConnect;
        }
    }

    /**
     * 生成相似货源code
     * @param tb
     * @return
     */
    @Override
    public String genSimilarityCode(Transport tb){
        String taskContentKey = GoodsUnique.extractKeyInfo(tb.getTaskContent());
        //相似code
        String similarityCode = this.genSimilarityCode(tb, taskContentKey);
        return similarityCode;
    }

    @Override
    public void updateSimilarityCode(Transport tb) {
		/*
		  2019-10-22 用户新的方案进行替代
		if (tb.getIsStandard() == 1) { // 非标货源，直接结束

			//更新基本信息
			String simCode = UUID.randomUUID().toString();
			this.updateSimilarityCode(tb.getId(), simCode, null, null); //transport表
			this.updateSimilarityCodeForMain(tb.getSrcMsgId(), simCode, null, null); //transport_main表
			return;
		}


		//规则：今天的日期 + 出发地省 + 出发地市 + 目的地省 + 目的地市 + 标准化ID
		String connectStr = todayDate + startProvinc + startCity + destProvinc + destCity + matchItemId.toString();

		//相似code
		String similarityCode = MD5Util.GetMD5Code(connectStr);
		*/
        String taskContentKey = GoodsUnique.extractKeyInfo(tb.getTaskContent());

        //相似code
        String similarityCode = this.genSimilarityCode(tb, taskContentKey);

        //首发ID
        Long similarityFirstId = tb.getSrcMsgId();

        String newNickName = StringUtil.hidePhoneInStr(tb.getNickName());
        //首发货物基本信息
        //	String similarityFirstInfo = tb.getType()+tb.getGoodTypeName() + "##" + tb.getNickName() + "##" + tb.getUserType() + "##" + tb.getIsInfoFee();
        String similarityFirstInfo = taskContentKey + "##" + newNickName + "##" + tb.getUserType() + "##" + tb.getIsInfoFee() +"##" + tb.getRegTime().getTime();
        Transport similarityTb = this.getFirstBySameCode(similarityCode);
        if (similarityTb != null) {
            similarityFirstId = similarityTb.getSimilarityFirstId();
            similarityFirstInfo = similarityTb.getSimilarityFirstInfo();
        }
        //更新基本信息
        this.updateSimilarityCode(tb.getId(), similarityCode, similarityFirstId, similarityFirstInfo);
        //更新main表信息
        this.updateSimilarityCodeForMain(tb.getSrcMsgId(), similarityCode, similarityFirstId, similarityFirstInfo);
    }

    /**
     * 沉底用，获取最小id
     * @return
     */
    public Long getBottomIdSubtract(){
        //1百万
        long defaultMinTsId = 1000000L;

        String redisKey = RedisKeyConstant.getBottomIdSubtract();

        String dayMinTsIdStr = RedisUtil.get(redisKey);
        Long dayMinTsId = null;
        if(StringUtils.isNotBlank(dayMinTsIdStr) && StringUtils.isNumeric(dayMinTsIdStr)){
            dayMinTsId= Long.parseLong(dayMinTsIdStr);
        }

        if(dayMinTsId == null || dayMinTsId < defaultMinTsId){
            dayMinTsId = this.getTodayMinTransId();

            if(dayMinTsId == null || dayMinTsId < defaultMinTsId){
                dayMinTsId = defaultMinTsId;
            }

            RedisUtil.set(redisKey, dayMinTsId + "", 24 * 60 * 60);
        }
        return dayMinTsId;
    }

    /**
     * 更新相似code、首发Id、首发基本信息
     *
     * @param tsId                货物记录ID
     * @param similarityCode      相似code
     * @param similarityFirstId   首发第一单货的src_msg_id
     * @param similarityFirstInfo 首发基本信息
     * @return 无返回值，-1表示无返回值
     */
    private int updateSimilarityCode(Long tsId, String similarityCode, Long similarityFirstId, String similarityFirstInfo) {

        Transport tb = this.getById(tsId);
        Integer sortType = tb.getSortType();

        if(TsSortTypeEnum.top.equalsCode(sortType)){
            //置顶
            tb.setSort(tsId);
        } else {
            //沉底
            Long idSubtract = this.getBottomIdSubtract();
            Long sort = tsId - idSubtract;
            tb.setSort(sort);
        }

        tb.setSimilarityCode(similarityCode);
        tb.setSimilarityFirstId(similarityFirstId);
        tb.setSimilarityFirstInfo(similarityFirstInfo);
        this.update(tb);

        return -1;
    }

    /**
     * 更新相似code、首发Id、首发基本信息
     *
     * @param srcMsgId            货源ID
     * @param similarityCode      相似code
     * @param similarityFirstId   首发第一单货的src_msg_id
     * @param similarityFirstInfo 首发基本信息
     * @return 无返回值，-1表示无返回值
     */
    private int updateSimilarityCodeForMain(Long srcMsgId, String similarityCode, Long similarityFirstId, String similarityFirstInfo) {
        TransportMain main = transportMainService.getById(srcMsgId);
        main.setSimilarityCode(similarityCode);
        main.setSimilarityFirstId(similarityFirstId);
        main.setSimilarityFirstInfo(similarityFirstInfo);
        transportMainService.update(main);
        return -1;
    }

    /**
     * 相似分组code查询transport对象
     *
     * @param similarityCode
     * @return
     */
    private Transport getFirstBySameCode(String similarityCode) {
        final String sql = "select * from tyt_transport where similarity_code = ? ";
        final Object[] params = {
                similarityCode
        };
        List<Transport> list = this.getBaseDao().search(sql, params, 0, 1);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 拨打记录回显
     * @param userId
     * @param goodId
     * @param clientSign
     * @return
     */
    public TytAppCallLog callDetail(String userId, String goodId, String clientSign) {
        return tytAppCallLogMapper.callDetail(userId, goodId, clientSign);
    }

    @Override
    public boolean checkPersonalSimilarity(Transport transport, Long srcMsgId){
        Date nowTime = new Date();

        String dayBegin = DateUtil.dateToString(DateUtil.startOfDay(nowTime), DateUtil.date_time_format);

        Long userId = transport.getUserId();

        String similarityCode = this.genSimilarityCode(transport);

        List<String> telList = new ArrayList<>();
        String tel = transport.getTel();
        String tel3 = transport.getTel3();
        String tel4 = transport.getTel4();
        if(StringUtils.isNotBlank(tel)){
            telList.add("'" + tel + "'");
        }
        if(StringUtils.isNotBlank(tel3)){
            telList.add("'" + tel3 + "'");
        }
        if(StringUtils.isNotBlank(tel4)){
            telList.add("'" + tel4 + "'");
        }

        String telStrs = StringUtils.join(telList, ",");

        List<Object> paramList = new ArrayList<>();
        paramList.add(transport.getHashCode());
        paramList.add(dayBegin);
        paramList.add(userId);

        String sql = "select id from tyt_transport where hash_code = ? " +
                " and ctime >= ? and user_id = ? and status = 1 " +
                " and ( tel in (" + telStrs + ") or tel3 in (" + telStrs + ")" +
                " or tel4 in (" + telStrs + ") ) ";
        if(srcMsgId != null){
            sql = sql + " and src_msg_id != ? ";

            paramList.add(srcMsgId);
        }
        sql = sql + " limit 1 ";

        Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("id", Hibernate.LONG);

        List<Transport> tsList = this.getBaseDao().search(sql,  scalarMap, Transport.class, paramList.toArray());

        return CollectionUtils.isEmpty(tsList);

    }

    @Override
    public boolean checkTransportSimilarity(Transport transport) {
        Date nowTime = new Date();

        String dayBegin = DateUtil.dateToString(DateUtil.startOfDay(nowTime), DateUtil.date_time_format);

        String similarityCode = this.genSimilarityCode(transport);


        List<Object> paramList = new ArrayList<>();
        paramList.add(similarityCode);
        paramList.add(dayBegin);

        String sql = "select id from tyt_transport where similarity_code = ? " +
                " and ctime >= ? and status = 1 " ;
        sql = sql + " limit 1 ";

        Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("id", Hibernate.LONG);

        List<Transport> tsList = this.getBaseDao().search(sql,  scalarMap, Transport.class, paramList.toArray());
        logger.info("checkTransportSimilarity -> similarityCode:{}, 货物内容:{}", similarityCode, transport.getTaskContent());
        return CollectionUtils.isEmpty(tsList);
    }


    @Override
    public boolean checkOtherSimilarityExist(Long srcMsgId, String similarityCode) {
        Date nowTime = new Date();

        String dayBegin = DateUtil.dateToString(DateUtil.startOfDay(nowTime), DateUtil.date_time_format);

        String sql = "select id from tyt_transport "
                + "where ctime >= ? "
                + "and status = 1 "
                + "and similarity_code = ? "
                + "and src_msg_id != ? "
                + "limit 1 " ;

        Object[] paramArray = {dayBegin, similarityCode, srcMsgId};

        Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("id", Hibernate.LONG);

        List<Transport> tsList = this.getBaseDao().search(sql,  scalarMap, Transport.class, paramArray);

        return CollectionUtils.isNotEmpty(tsList);
    }
}
