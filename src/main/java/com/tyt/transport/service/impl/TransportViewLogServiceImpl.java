package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.TransportSubBean;
import com.tyt.model.TransportViewLog;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TransportService;
import com.tyt.transport.service.TransportSubService;
import com.tyt.transport.service.TransportViewLogService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.List;

/**
 * 货源查看日志
 * User: duanwc
 * Date: 19-01-29
 * Time: 下午3:16
 */
@Service("transportViewLogService")
public class TransportViewLogServiceImpl extends BaseServiceImpl<TransportViewLog, Long> implements TransportViewLogService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());


    @Resource(name = "transportViewLogDao")
    public void setBaseDao(BaseDao<TransportViewLog, Long> transportViewLogDao) {
        super.setBaseDao(transportViewLogDao);
    }

    @Override
    public Integer addLog(long userId, long tsId, String clientVersion, int clientSign) {
        String today = TimeUtil.formatDate(TimeUtil.today());
        Integer i = this.detailsLog(userId, tsId, clientVersion, clientSign);
        Long redisCount = 0l;
        if(i > 0) {
            redisCount = RedisUtil.mapHincr(Constant.PLAT_COUNT_TRANSPORT_KEY + today, Constant.VIEW_COUNT_HASH_KEY+tsId, (int) Constant.CACHE_EXPIRE_TIME_24H);
        }
        return redisCount.intValue();
    }

    @Override
    public Integer detailsLog(long userId, long tsId, String clientVersion, int clientSign) {
        final String sql = "insert ignore into tyt_transport_view_log(user_id,ts_id,client_version,client_sign) values(?,?,?,?)";
        final Object[] params = {
                userId,
                tsId,
                clientVersion,
                clientSign
        };
        return this.getBaseDao().executeUpdateSql(sql, params);
    }

    /**
     * 是否浏览过该货源
     *
     * @param userId 用户ID
     * @param tsId   货源id
     */
    @Override
    public boolean isViewed(Long userId, Long tsId) {
        BigInteger count = this.getBaseDao().query("select count(*) from tyt_transport_view_log where user_id=? and ts_id=?",
                new Object[]{userId, tsId});
        return count != null && count.intValue() > 0;
    }
}
