package com.tyt.transport.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.tfc.analysis.KWSeeker;
import com.tfc.analysis.entity.Keyword;
import com.tfc.analysis.process.WordFinder;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.TytFilterKeyword;
import com.tyt.model.TytKeywordMatchesNew;
import com.tyt.model.TytKeywordShort;
import com.tyt.model.TytMachineTypeNew;
import com.tyt.model.TytNumberKeyword;
import com.tyt.model.TytProbabilityKeyword;
import com.tyt.model.TytSearchKeyword;
import com.tyt.model.TytSearchWord;
import com.tyt.transport.querybean.StandardStatusBean;
import com.tyt.transport.service.CommonUserMachineTypeNewService;
import com.tyt.transport.service.FilterKeywordService;
import com.tyt.transport.service.KeywordMatchesNewService;
import com.tyt.transport.service.KeywordShortService;
import com.tyt.transport.service.MachineTypeNewService;
import com.tyt.transport.service.MachineTypeService;
import com.tyt.transport.service.NumberKeywordService;
import com.tyt.transport.service.ProbabilityKeywordService;
import com.tyt.transport.service.SearchKeywordService;
import com.tyt.transport.service.SearchWordService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;

@SuppressWarnings("unchecked")
@Service("keywordMatchesNewService")
public class KeywordMatchesNewServiceImpl extends BaseServiceImpl<TytKeywordMatchesNew, Long> implements KeywordMatchesNewService {
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	private static final String STANDARD_GOODS_KEYWORD_KEY = "standardGoodsKeywordKey";
	private static final String GOOD_NUM_KEYWORD_KEY = "goodNumKeywordKey";

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "searchWordService")
	private SearchWordService searchWordService;

	@Resource(name = "tytConfigService")
	private TytConfigService configService;

	@Resource(name = "machineTypeNewService")
	private MachineTypeNewService machineTypeNewService;

	@Resource(name = "machineTypeService")
	private MachineTypeService machineTypeService;

	@Resource(name = "searchKeywordService")
	private SearchKeywordService searchKeywordService;

	@Resource(name = "probabilityKeywordService")
	private ProbabilityKeywordService probabilityKeywordService;

	@Resource(name = "numberKeywordService")
	private NumberKeywordService numberKeywordService;

	@Resource(name = "filterKeywordService")
	private FilterKeywordService filterKeywordService;

	@Resource(name = "commonUserMachineTypeNewService")
	private CommonUserMachineTypeNewService commonUserMachineTypeNewService;

	@Resource(name = "keywordShortService")
	private KeywordShortService keywordShortService;

	@Resource(name = "keywordMatchesNewDao")
	public void setBaseDao(BaseDao<TytKeywordMatchesNew, Long> keywordMatchesNewDao) {
		super.setBaseDao(keywordMatchesNewDao);
	}

	@SuppressWarnings("deprecation")
	@Override
	public List<TytMachineTypeNew> queryMachineTypeByKeyword(String keyword, String userId) {
		List<TytMachineTypeNew> machineTypeResult = null;
		/*
		 * 保存关键词搜索记录,如果是emoji表情则不保存
		 */
		Pattern pattern = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]");
		Matcher matcher = pattern.matcher(keyword);
		if (!matcher.find()) {
			/*
			 * 保存关键词搜索记录
			 */
			searchWordService.add(new TytSearchWord(keyword, new Date(), userId != null ? Integer.valueOf(userId) : null));
		}
		/*
		 * 根据关键词从缓存中搜索匹配项对应的机器类型
		 */
		String matchineTypeCacheKey = configService.getStringValue(Constant.MACHINE_TYPE_NEW_CACHE_KEY, "machine_type_new_cache_");
		String machineTypeCacheResult = cacheService.getString(matchineTypeCacheKey + keyword);
		//logger.info("get machine type new cache by key [ " + (matchineTypeCacheKey + keyword) + " ], result is [ " + machineTypeCacheResult + " ]");
		/*
		 * 如果缓存中有数据则直接解析数据, 否则从数据库中查询
		 */
		if (machineTypeCacheResult != null) {
			machineTypeResult = JSON.parseArray(machineTypeCacheResult, TytMachineTypeNew.class);
		} else {
			/*
			 * 先使用精确匹配查询，如果查询不到再使用通用匹配
			 */
			String sql = "SELECT tmt.`id`, tmt.`brand`,tmt.`brand_type` AS 'brandType', tmt.`machine_type` AS 'machineType', tmt.`type`, tmt.`weight`, tmt.`length`, tmt.`width`, tmt.`height`, tmt.`length_width_height_display` AS 'lengthWidthHeightDisplay', tmt.`weight_display` AS 'weightDisplay' FROM tyt_keyword_matches_new tkm, tyt_machine_type_new tmt WHERE tkm.`keyword`=? AND tkm.`machine_type_id`=tmt.`id` ORDER BY tkm.`priority` ASC";
			Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
			scalarMap.put("id", Hibernate.LONG);
			scalarMap.put("brandType", Hibernate.STRING);
			scalarMap.put("brand", Hibernate.STRING);
			scalarMap.put("machineType", Hibernate.STRING);
			scalarMap.put("type", Hibernate.STRING);
			scalarMap.put("weight", Hibernate.STRING);
			scalarMap.put("length", Hibernate.STRING);
			scalarMap.put("width", Hibernate.STRING);
			scalarMap.put("height", Hibernate.STRING);
			scalarMap.put("lengthWidthHeightDisplay", Hibernate.INTEGER);
			scalarMap.put("weightDisplay", Hibernate.INTEGER);
			machineTypeResult = this.getBaseDao().search(sql, scalarMap, TytMachineTypeNew.class, new Object[] { keyword }, 1, configService.getIntValue(Constant.MATCHES_TYPE_NEW_CACHE_SIZE, 20));
			/*
			 * 如果查询到结果则放到缓存中，如果没有查到则使用通用匹配查询
			 */
			if (machineTypeResult != null && machineTypeResult.size() > 0) {
				cacheMachineType(matchineTypeCacheKey + keyword, machineTypeResult, Constant.MATCHES_TYPE_CACHE_TIME);
			} else {
				machineTypeResult = machineTypeNewService.queryMachineTypeByGeneralMatch(Constant.MATCHES_TYPE__CACHE_SIZE, keyword);
				if (machineTypeResult != null && machineTypeResult.size() > 0) {
					cacheMachineType(matchineTypeCacheKey + keyword, machineTypeResult, Constant.MATCHES_TYPE_CACHE_TIME);
				}
			}
		}
		return machineTypeResult;
	}

	@SuppressWarnings("rawtypes")
	private void cacheMachineType(String key, List cacheList, long cacheTime) {
		cacheService.setString(key, JSON.toJSONString(cacheList), cacheTime);
	}

	@Override
	public StandardStatusBean queryStandardStatusByKeyword(String keyword) throws IllegalAccessException, InvocationTargetException {
		StandardStatusBean standardStatusBean = new StandardStatusBean();
		/*
		 * 查询是否出现非标准化的关键词
		 */
		List<TytFilterKeyword> filKeywords = queryFilterKeywords();
		logger.info("keyword matches filter keyword result is: " + filKeywords);
		boolean hasFilterKeyword = isHasFilterKeyword(filKeywords, keyword);
		if (!hasFilterKeyword) {
			// 查询准标准化的关键词是否在货物内容中出现多于1次，如果是则按照非标准化处理
			List<TytProbabilityKeyword> probabilityKeywords = queryProbilityKeywords();
			// 记录关键词出现次数
			int curMatchTime = 0;
			boolean isNotStandard = false;
			if (probabilityKeywords != null && probabilityKeywords.size() > 0) {
				for (int i = 0; i < probabilityKeywords.size(); i++) {
					if (StringUtils.isNotEmpty(probabilityKeywords.get(i).getKeyword()) && keyword.indexOf(probabilityKeywords.get(i).getKeyword()) != -1) {
						if (++curMatchTime > 1) {
							logger.info("keyword matches have 2 probability keyword, not standard!");
							isNotStandard = true;
							standardStatusBean.setStandardStatus(Constant.STANDARD_STATUS_NOT);
							break;
						}
					}
				}
			}
			// 获取个或台出现的次数
			int setOrEntryNum = appearNumber(keyword, "个|台");
			/*
			 * 如果出现1个以上的台或个并且可能标准化的词出现的次数是1则为准标准化,
			 * 如果出现1个以上的台或个并且可能标准化的词出现的次数是大于1则为非准标准化
			 */
			if (setOrEntryNum > 1 && curMatchTime == 1) {
				standardStatusBean.setStandardStatus(Constant.STANDARD_STATUS_PROBABILITY);
			} else if (setOrEntryNum > 1 && curMatchTime != 1) {
				standardStatusBean.setStandardStatus(Constant.STANDARD_STATUS_NOT);
			} else {
				if (!isNotStandard) {
					List<String> matchesKeywords = new ArrayList<String>();
					boolean isStandard = isStandard(keyword, matchesKeywords);
					/*
					 * 如果提取出的标准化货源关键词不是1个则认为不是标准化货源，并进一步判断是否为准标准化货源
					 */
					if (!isStandard) {
						// 判断货源是否为准标准化货源
						if (curMatchTime == 1) {
							standardStatusBean.setStandardStatus(Constant.STANDARD_STATUS_PROBABILITY);
						} else {
							standardStatusBean.setStandardStatus(Constant.STANDARD_STATUS_NOT);
						}
					} else {
						// 获取匹配项的id,先从缓存中获取，获取不到从数据库中获取
						Object matchItemIdObj = cacheService.getObject(configService.getStringValue(STANDARD_GOODS_KEYWORD_KEY, "standard_goods_keyword_") + matchesKeywords.get(0));
						logger.info("keyword matches new matchItemId is: " + matchItemIdObj);
						int matchItemId = 0;
						if (matchItemIdObj == null) {
							TytSearchKeyword searchKeyword = searchKeywordService.getByKeyword(matchesKeywords.get(0));
							if (searchKeyword != null) {
								matchItemId = searchKeyword.getMachineId();
							} else {
								logger.info("keyword matches find no result by keyword: " + keyword);
								standardStatusBean.setStandardStatus(Constant.STANDARD_STATUS_NOT);
							}
						} else {
							matchItemId = Integer.valueOf(matchItemIdObj.toString());
							logger.info("keyword matches new final matchItemId is: " + matchItemId);
						}
						if (matchItemId != 0) {
							standardStatusBean.setStandardStatus(Constant.STANDARD_STATUS_STANDARD);
							standardStatusBean.setId(matchItemId);
							/*
							 * 查询标准化数据信息
							 */
							Object machineType;
							if (matchItemId < 10000) {
								machineType = machineTypeService.getById(Long.valueOf(matchItemId));
							} else {
								machineType = machineTypeNewService.getById(Long.valueOf(matchItemId));
							}
							logger.info("keyword matches matchine type message is: " + machineType);
							if (machineType != null) {
								BeanUtils.copyProperties(standardStatusBean, machineType);
							}
							// 获取台数
							int goodNumber = fetchGoodNumber(keyword);
							standardStatusBean.setGoodNumber(goodNumber);
						}
					}
				}
			}
		} else {
			standardStatusBean.setStandardStatus(Constant.STANDARD_STATUS_NOT);
		}
		return standardStatusBean;
	}

	private boolean isStandard(String keyword, List<String> matchesKeywords) {
		boolean isStandard = false;
		boolean isContinue = true;
		// 进行全量标准化词的提取
		extractKeywords(keyword, Constant.SEARCH_KEYWORD_TYPE_ALL_PRIORITY, matchesKeywords);
		// 如果提取的关键词的个数不是1个则认为是非标准化
		if (matchesKeywords != null && matchesKeywords.size() != 1) {
			isStandard = false;
		} else {
			matchesKeywords.clear();
			/*
			 * 按照优先级分别查询三种标准化数据
			 */
			for (int i = 0; i < 3 && isContinue; i++) {
				switch (i) {
				case 0:
					extractKeywords(keyword, Constant.SEARCH_KEYWORD_TYPE_FIRST_PRIORITY, matchesKeywords);
					// 如果有匹配项并且只有一个匹配项则认为是标准化数据，如果为空则继续下一种类型的判断，如果不为空但是匹配项个数大于1则认为是非标准化数据
					if (matchesKeywords != null && matchesKeywords.size() == 1) {
						isStandard = true;
						isContinue = false;
					} else if (matchesKeywords != null && matchesKeywords.size() > 1) {
						isStandard = false;
						isContinue = false;
					}
					break;
				case 1:
					extractKeywords(keyword, Constant.SEARCH_KEYWORD_TYPE_SECOND_PRIORITY, matchesKeywords);
					// 1:如果不为空并且匹配项的个数为1则需要判断匹配项所在关键字的前一位是否是数字，如果是数字则认为是非标准化
					// 如果不是数字则认为是标准化
					// 2:如果不为空并且匹配项的个数大于1则认为是非标准化数据
					if (matchesKeywords != null && matchesKeywords.size() == 1) {
						isContinue = false;
						isStandard = true;
						// 判断提取的匹配词在关键字中出现的次数如果大于1次则进行进一步的判断否则进行型号的判断
						String matchKeyword = matchesKeywords.get(0);
						// int matchesAppearNum = appearNumber(keyword,
						// matchKeyword);
						// 判断所有位置的前面内容有为数字的，如果有则认为是非标准化的
						int curPos = 0;
						while ((curPos = keyword.indexOf(matchKeyword, curPos)) != -1 && curPos > 0 && isStandard) {
							String curPosForwardContent = keyword.substring(curPos - 1, curPos);
							logger.info("keyword matches new forward content is: " + curPosForwardContent);
							try {
								Integer.valueOf(curPosForwardContent);
								isStandard = false;
								break;
							} catch (NumberFormatException e) {
								curPos = curPos + matchKeyword.length();
							}
						}
					} else if (matchesKeywords != null && matchesKeywords.size() > 1) {
						isContinue = false;
						isStandard = false;
					}
					break;
				case 2:
					extractKeywords(keyword, Constant.SEARCH_KEYWORD_TYPE_THIRD_PRIORITY, matchesKeywords);
					// 如果有匹配词并且匹配词的个数是1则判断匹配项所在关键字的后一位是否是数字，如果是数字则认为是非标准化
					// 如果不是数字则继续通过已有机种获取当前存在的机种并且与关键字所对应的机种判断如果一致则认为是标准化数据
					// 否则认为是非标准化数据
					// 如果有匹配词并且匹配词的个数大于1则认为是非标准化数据
					if (matchesKeywords != null && matchesKeywords.size() == 1) {
						isContinue = false;
						isStandard = true;
						String matchKeyword = matchesKeywords.get(0);
						// 判断所有位置的前面内容有为数字的，如果有则认为是非标准化的
						int curPos = 0;
						while ((curPos = keyword.indexOf(matchKeyword, curPos)) != -1 && curPos < keyword.length()) {
							// 处理已到字符串最后的情况防止StringIndexOutOfBoundsException字符串索引越界异常
							if ((curPos + matchKeyword.length()) == keyword.length()) {
								isStandard = true;
								break;
							}
							String curPosBackwardContent = keyword.substring(curPos + matchKeyword.length(), curPos + matchKeyword.length() + 1);
							//logger.info("keyword matches new backward content is: " + curPosBackwardContent);
							try {
								Integer.valueOf(curPosBackwardContent);
								isStandard = false;
								break;
							} catch (NumberFormatException e) {
								curPos = curPos + matchKeyword.length();
							}
						}
						// 如果判断完型号后依然是标准化数据则继续判断机种是否符合
						if (isStandard) {
							// 查询用户输入信息的机种
							List<TytKeywordShort> keywordShortsList = keywordShortService.findList(null);
							String machineType = queryMachineTypeFromKeyword(keywordShortsList, keyword);
							// 根据匹配项查询匹配项对象
							TytSearchKeyword searchKeyword = searchKeywordService.getByKeyword(matchesKeywords.get(0));
							//logger.info("keyword matches machine type is: " + machineType + ", searchkeyword is: " + searchKeyword);
							if (searchKeyword != null) {
								if (machineType == null || (machineType != null && machineType.equals(searchKeyword.getMachineType()))) {
									isStandard = true;
								} else {
									isStandard = false;
								}
							} else {
								isStandard = false;
							}
						}
					} else if (matchesKeywords != null && matchesKeywords.size() > 1) {
						isContinue = false;
						isStandard = false;
					}
					break;
				}
			}
		}
		return isStandard;
	}

	private String queryMachineTypeFromKeyword(List<TytKeywordShort> keywordShortsList, String keyword) {
		for (int i = 0; i < keywordShortsList.size(); i++) {
			if (keyword.indexOf(keywordShortsList.get(i).getKey()) != -1) {
				return keywordShortsList.get(i).getTag();
			}
		}
		return null;
	}

	private boolean isHasFilterKeyword(List<TytFilterKeyword> filKeywords, String keyword) {
		List<Keyword> goodNumKeywordList = fetchFilterKeyword(filKeywords);
		KWSeeker kw1 = KWSeeker.getInstance(goodNumKeywordList);
		// 找出文本中所有含有上面词库中的词！
		Set<String> extractResult = (Set<String>) kw1.process(wordFinder, keyword, null);
		return extractResult != null && extractResult.size() > 0;
	}

	private List<Keyword> fetchFilterKeyword(List<TytFilterKeyword> filKeywords) {
		List<Keyword> filterKeywordsList = new ArrayList<Keyword>();
		for (int i = 0; i < filKeywords.size(); i++) {
			filterKeywordsList.add(new Keyword(filKeywords.get(i).getKeyword()));
		}
		return filterKeywordsList;
	}

	private List<TytFilterKeyword> queryFilterKeywords() {
		return filterKeywordService.findList(null);
	}

	private int fetchGoodNumber(String keyword) {
		int goodNumber = 1;
		List<Keyword> goodNumKeywordList = fetchGoodNumKeywords();
		KWSeeker kw1 = KWSeeker.getInstance(goodNumKeywordList);
		// 找出文本中所有含有上面词库中的词！
		Set<String> extractResult = (Set<String>) kw1.process(wordFinder, keyword, null);
		logger.info("keyword matches extract good num from: " + keyword + " , result is: " + extractResult);
		if (extractResult.size() == 1) {
			// 从缓存中查询台数关键词对应的台数
			String goodNum = cacheService.getString(configService.getStringValue(GOOD_NUM_KEYWORD_KEY, "good_num_keyword_") + extractResult.toArray(new String[0])[0]);
			if (StringUtils.isEmpty(goodNum)) {
				TytNumberKeyword numberKeyword = numberKeywordService.getByKeyword(extractResult.toArray(new String[0])[0]);
				if (numberKeyword != null) {
					goodNumber = numberKeyword.getNumber();
				}
			}
		}
		return goodNumber;
	}

	private List<Keyword> fetchGoodNumKeywords() {
		String searchGoodNumberCacheKey = configService.getStringValue(Constant.SEARCH_GOOD_NUMBER_KEY, "search_good_number_key");
		String searchGoodNumberCacheResult = cacheService.getString(searchGoodNumberCacheKey);
		//logger.info("get search good number cache by key [ " + searchGoodNumberCacheKey + " ], result is [ " + searchGoodNumberCacheResult + " ]");
		List<TytNumberKeyword> numberKeywords = null;
		// 是否需要重新构建KeyWord对象列表标示
		boolean isNeedRebuild = false;
		if (StringUtils.isEmpty(searchGoodNumberCacheResult) || "null".equals(searchGoodNumberCacheResult)) {
			isNeedRebuild = true;
			numberKeywords = numberKeywordService.findList(null);
			cacheService.setString(searchGoodNumberCacheKey, JSON.toJSONString(numberKeywords), Constant.CACHE_EXPIRE_TIME_24H);
		} else {
			numberKeywords = JSON.parseArray(searchGoodNumberCacheResult, TytNumberKeyword.class);
		}
		if (isNeedRebuild || goodNumKeywordsList.size() == 0) {
			goodNumKeywordsList.clear();
			for (int i = 0; i < numberKeywords.size(); i++) {
				goodNumKeywordsList.add(new Keyword(numberKeywords.get(i).getKeyword()));
			}
		}
		return goodNumKeywordsList;
	}

	private static WordFinder wordFinder = new WordFinder();
	private static List<Keyword> goodNumKeywordsList = new ArrayList<Keyword>();

	private List<Keyword> fetchStandardKeywords(int searchKeywordPriority) {
		String searchKeywordCacheKey = configService.getStringValue(Constant.SEARCH_KEYWORD_KEY + "_" + searchKeywordPriority, "search_keyword_" + searchKeywordPriority);
		String searchKeywordCacheResult = cacheService.getString(searchKeywordCacheKey);
		List<Keyword> standardKeywordsList = new ArrayList<Keyword>();
		//logger.info("get search keyword cache by key [ " + searchKeywordCacheKey + " ], result is [ " + searchKeywordCacheResult + " ]");
		List<TytSearchKeyword> searchKeywords = null;
		// 是否需要重新构建KeyWord对象列表标示
		boolean isNeedRebuild = false;
		if (StringUtils.isEmpty(searchKeywordCacheResult)) {
			isNeedRebuild = true;
			searchKeywords = searchKeywordService.findListByKeywordPriority(searchKeywordPriority);
			logger.info("keyword matches query by priority " + searchKeywordPriority + ", result is: " + searchKeywords);
			cacheService.setString(searchKeywordCacheKey, JSON.toJSONString(searchKeywords), Constant.CACHE_EXPIRE_TIME_2H);
		} else {
			searchKeywords = JSON.parseArray(searchKeywordCacheResult, TytSearchKeyword.class);
		}
		if (isNeedRebuild || standardKeywordsList.size() == 0) {
			standardKeywordsList.clear();
			for (int i = 0; i < searchKeywords.size(); i++) {
				standardKeywordsList.add(new Keyword(searchKeywords.get(i).getKeyword()));
			}
		}
		return standardKeywordsList;
	}

	private List<String> extractKeywords(String keyword, Integer priorityType, List<String> matchesKeywords) {
		List<Keyword> keywordList = fetchStandardKeywords(priorityType);
		Set<String> extractResult = extractResult(keyword, keywordList);
		matchesKeywords.addAll(extractResult);
		//logger.info("keyword matches extract keyword from: " + keyword + " , when priority type is: " + priorityType + " result is: " + matchesKeywords);
		return matchesKeywords;
	}

	private Set<String> extractResult(String keyword, List<Keyword> keywordList) {
		KWSeeker kw1 = KWSeeker.getInstance(keywordList);
		// 找出文本中所有含有上面词库中的词！
		Set<String> extractResult = (Set<String>) kw1.process(wordFinder, keyword, null);
		return extractResult;
	}

	/**
	 * 获取指定字符串出现的次数
	 * 
	 * @param srcText
	 *            源字符串
	 * @param findText
	 *            要查找的字符串
	 * @return
	 */
	public int appearNumber(String srcText, String findText) {
		int count = 0;
		Pattern p = Pattern.compile(findText);
		Matcher m = p.matcher(srcText);
		while (m.find()) {
			count++;
		}
		return count;
	}

	private List<TytProbabilityKeyword> queryProbilityKeywords() {
		String probablyStandardCacheKey = configService.getStringValue(Constant.PROBABLY_STANDARD_KEY, "probably_standard_");
		String probablyStandardCacheResult = cacheService.getString(probablyStandardCacheKey);
		//logger.debug("get search keyword cache by key [ " + probablyStandardCacheKey + " ], result is [ " + probablyStandardCacheResult + " ]");
		List<TytProbabilityKeyword> probabilityKeywords = null;
		if (StringUtils.isEmpty(probablyStandardCacheResult)) {
			probabilityKeywords = probabilityKeywordService.findList(null);
			cacheService.setString(probablyStandardCacheKey, JSON.toJSONString(probabilityKeywords), Constant.CACHE_EXPIRE_TIME_24H);
		} else {
			probabilityKeywords = JSON.parseArray(probablyStandardCacheResult, TytProbabilityKeyword.class);
		}
		return probabilityKeywords;
	}
}
