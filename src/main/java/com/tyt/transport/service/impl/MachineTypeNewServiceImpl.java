package com.tyt.transport.service.impl;

import java.util.List;
import javax.annotation.Resource;

import com.tyt.model.TytMachineType;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.TytMachineTypeBean;
import com.tyt.transport.service.MachineTypeService;
import com.tyt.user.service.TytConfigService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytMachineTypeNew;
import com.tyt.transport.service.MachineTypeNewService;
import com.tyt.util.Constant;

@Service("machineTypeNewService")
public class MachineTypeNewServiceImpl extends BaseServiceImpl<TytMachineTypeNew, Long> implements MachineTypeNewService {

	private static final String  TRANSPORT_CARTYPE_STANDARD = "TRANSPORT_CARTYPE_STANDARD" ;
	private static final int activeTime = 3*24*60*60;
	private static final String  defaultValue="transport_carType_standard_{id}";

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "machineTypeService")
	MachineTypeService machineTypeService;

	@Resource(name = "machineTypeNewDao")
	public void setBaseDao(BaseDao<TytMachineTypeNew, Long> machineTypeNewDao) {
		super.setBaseDao(machineTypeNewDao);
	}
	
	@Override
	public List<TytMachineTypeNew> queryMachineTypeByGeneralMatch(int matchesTypeCacheSize, String keyword) {
		return this.getBaseDao().searchByHql("SELECT new com.tyt.model.TytMachineTypeNew(id, brand, machineType, type, weight, length, width, height, lengthWidthHeightDisplay, weightDisplay, brandType) from TytMachineTypeNew where general_matches_item LIKE ? ", new Object[] { '%' + keyword + '%' }, 1, Constant.MATCHES_TYPE__CACHE_SIZE);
	}

	/**
	 * 获得货物类型模版
	 * @param id
	 * @return
	 */
	public TytMachineTypeNew getTytMachineTypeForId(Long id){
		String value = tytConfigService.getStringValue(TRANSPORT_CARTYPE_STANDARD, defaultValue);
		String key = StringUtils.replace(value, "{id}", id.toString());
		Object obj = RedisUtil.getObject(key);
		if(obj==null){
			TytMachineTypeNew tytMachineType =this.getById(id);
			RedisUtil.setObject(key, tytMachineType, activeTime);
			return tytMachineType;
		}
		return (TytMachineTypeNew) obj;
	}

	public TytMachineTypeBean getTytMachineTypeForIdAll(Long id){
		if(id!=null){
			TytMachineTypeBean tytMachineTypeBean= new TytMachineTypeBean();
			if(id.longValue()>10000l){
				TytMachineTypeNew tytMachineTypeNew=this.getTytMachineTypeForId(id);
				if(tytMachineTypeNew==null){
					return null;
				}
				BeanUtils.copyProperties(tytMachineTypeNew, tytMachineTypeBean);
			}else{
				TytMachineType tytMachineType=machineTypeService.getTytMachineTypeForId(id);
				if(tytMachineType==null){
					return null;
				}
				BeanUtils.copyProperties(tytMachineType, tytMachineTypeBean);
			}
			return tytMachineTypeBean;
		}
		else{
			return null;
		}
	}
}
