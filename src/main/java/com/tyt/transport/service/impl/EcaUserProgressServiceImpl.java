package com.tyt.transport.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.model.EcaUserProgress;
import com.tyt.transport.service.EcaUserProgressService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service("ecaUserProgressService")
public class EcaUserProgressServiceImpl extends BaseServiceImpl<EcaUserProgress, Long> implements EcaUserProgressService {

	public static Logger logger = LoggerFactory.getLogger(EcaUserProgressServiceImpl.class);

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "tytMessageTmplService")
	private TytMessageTmplService messageTmplService;

	@Resource(name = "tytUserIdentityAuthService")
	private TytUserIdentityAuthService userIdentityAuthService;



	@Resource(name = "ecaUserProgressDao")
	public void setBaseDao(BaseDao<EcaUserProgress, Long> ecaUserProgressDao) {
		super.setBaseDao(ecaUserProgressDao);
	}

	public void insertEcaUserProgress(Long contractId, Long srcMsgId, Integer type) {
		EcaUserProgress ecaUserProgress = new EcaUserProgress();
		ecaUserProgress.setContractId(contractId);
		ecaUserProgress.setCtime(new Date());
		ecaUserProgress.setSrcMsgId(srcMsgId);
		ecaUserProgress.setType(type);
		this.add(ecaUserProgress);
	}

	@Override
	public List<EcaUserProgress> getEcaUserProgressListById(Long contractId) {
		return this.getBaseDao().find("from EcaUserProgress t where t.contractId = ? order by t.ctime desc", contractId);
	}
	
	@Override
	public void insertEcaUserProgressWithDate(Long contractId, Long srcMsgId, Integer type, Date cTime) {
		EcaUserProgress ecaUserProgress = new EcaUserProgress();
		ecaUserProgress.setContractId(contractId);
		ecaUserProgress.setCtime(cTime);
		ecaUserProgress.setSrcMsgId(srcMsgId);
		ecaUserProgress.setType(type);
		this.add(ecaUserProgress);
	}
}
