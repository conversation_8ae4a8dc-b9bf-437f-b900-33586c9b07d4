package com.tyt.transport.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.model.EcaProgress;
import com.tyt.transport.service.EcaProgressService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Service("ecaProgressService")
public class EcaProgressServiceImpl extends BaseServiceImpl<EcaProgress, Long> implements EcaProgressService {

	public static Logger logger = LoggerFactory.getLogger(EcaProgressServiceImpl.class);

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "tytMessageTmplService")
	private TytMessageTmplService messageTmplService;

	@Resource(name = "tytUserIdentityAuthService")
	private TytUserIdentityAuthService userIdentityAuthService;



	@Resource(name = "ecaProgressDao")
	public void setBaseDao(BaseDao<EcaProgress, Long> ecaProgressDao) {
		super.setBaseDao(ecaProgressDao);
	}

	@Override
	public void addEcaProgress(EcaProgress progress){
		progress.setCtime(new Date());
		this.add(progress);
	}

	@Override
	public void insertEcaProgress(String userId, String userName, Long ecaContractId) {
		EcaProgress ecaProgress = new EcaProgress();
		ecaProgress.setContractId(ecaContractId);
		ecaProgress.setOptType(1);
		ecaProgress.setOptHandler(1);
		ecaProgress.setUserId(Long.valueOf(userId));
		ecaProgress.setUserName(userName);
		ecaProgress.setStatus(1);
		ecaProgress.setDetail("创建合同");
		ecaProgress.setCtime(new Date());
		this.add(ecaProgress);
	}
}
