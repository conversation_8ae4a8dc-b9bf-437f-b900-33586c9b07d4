package com.tyt.transport.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.acvitity.service.ExposurePermissionService;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.bi.service.IBiRequestService;
import com.tyt.cache.CacheService;
import com.tyt.callPhoneRecord.service.impl.CallPhoneRecordServiceImpl;
import com.tyt.common.service.*;
import com.tyt.deposit.service.TytGoodsRefreshUserService;
import com.tyt.excellentgoodshomepage.service.ExcellentGoodsService;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.*;
import com.tyt.infofee.service.BackoutReasonService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillService;
import com.tyt.invoicetransport.service.InvoiceTransportService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.biz.feedback.pojo.UserFeedbackRatingAndLabelDTO;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.client.trade.infofee.ApiTradeInfoFeeClient;
import com.tyt.plat.client.trade.infofee.dto.AssignOrdersSaveBillDTO;
import com.tyt.plat.client.transport.ThPriceClient;
import com.tyt.plat.client.transport.TransportTecserviceFeeClient;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.constant.RemoteApiConstant;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.*;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.service.base.impl.AbtestServiceImpl;
import com.tyt.plat.service.block.TytBlockConfigService;
import com.tyt.plat.service.ts.TransportHistoryService;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.plat.vo.other.GoodsAddressLevelRecordVo;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.plat.vo.ts.*;
import com.tyt.receive.service.OwnerCompanyLogService;
import com.tyt.receive.service.TransportBackendAxbBinderService;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.bean.AssignOrderUser;
import com.tyt.transport.bean.AssignableCarFilterBean;
import com.tyt.transport.bean.CommissionTecFeeBean;
import com.tyt.transport.bean.GoodModelResult;
import com.tyt.transport.bean.MqAutoAssignOrderBean;
import com.tyt.transport.enums.*;
import com.tyt.transport.enums.RefundFlagEnum;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.*;
import com.tyt.transport.vo.TytTecServiceFeeConfigToComputResult;
import com.tyt.user.enums.InfoVerifyStatusEnum;
import com.tyt.user.service.*;
import com.tyt.util.*;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import retrofit2.Response;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.tyt.infofee.bean.MqBaseMessageBean.SPECIAL_CAR_ASSIGN;
import static com.tyt.plat.service.base.impl.AbtestServiceImpl.SHOW_BOSS_NICK_NAME;

@Service("transportBusiness")
public class TransportBusiness implements TransportBusinessInterface {
    //投诉最大百分比
    static final BigDecimal complainLimit = new BigDecimal("0.1");
    //标签内容
    static final String goodServiceText = "投诉少";
    //清障车类型
    static final String CLEARANCE_VEHICLE_TYPE = "清障车";

    @Resource(name = "transportVaryService")
    private TransportVaryService transportVaryService;

    @Autowired
    private ThPriceClient thPriceClient;

    @Resource(name = "transportService")
    private TransportService transportService;

    @Autowired
    private TransportTecserviceFeeClient transportTecserviceFeeClient;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "tytUserSubService")
    private TytUserSubService tytUserSubService;


    @Resource(name = "tytGeoDictService")
    private TytGeoDictService tytGeoDictService;

    @Resource(name = "tytBubbleService")
    TytBubbleService tytBubbleService;

    @Resource(name = "transportWayBillService")
    TransportWayBillService transportWayBillService;

    @Resource(name = "tytSequenceService")
    TytSequenceService tytSequenceService;

    @Resource(name = "transportOrdersService")
    TransportOrdersService transportOrdersService;

    @Resource(name = "tytMapDictService")
    TytMapDictService tytMapDictService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "transportSubService")
    private TransportSubService transportSubService;

    @Resource(name = "tytMqRecommMessService")
    private TytMqRecommMessageService tytMqRecommMessageService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "machineTypeNewService")
    private MachineTypeNewService machineTypeNewService;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "transportDoneService")
    @Lazy
    private TransportDoneService transportDoneService;
    @Autowired
    private TytSpecialCarAutoDispatchConfigMapper tytSpecialCarAutoDispatchConfigMapper;
    @Autowired
    private IBiRequestService biRequestService;
    @Autowired
    private TransportGoodModelFactorService transportGoodModelFactorService;

    @Autowired
    @Lazy
    private BsPublishTransportService bsPublishTransportService;
    @Autowired
    private BackendTransportMapper backendTransportMapper;
    @Resource(name = "transportBackendAxbBinderService")
    private TransportBackendAxbBinderService transportBackendAxbBinderService;

    private static final PropertiesFileUtil propertiesFileUtil = PropertiesFileUtil.init("message");
    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BackoutReasonService backoutReasonService;

    @Autowired
    private TytTransportExposureService tytTransportExposureService;

    @Autowired
    private TytTransportIntentionService tytTransportIntentionService;

    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Autowired
    private OwnerCompanyLogService ownerCompanyLogService;

    @Autowired
    private CsMaintainedCustomService csMaintainedCustomService;

    @Autowired
    private TytOwnerAuthMapper tytOwnerAuthMapper;

    @Autowired
    private TytTransportDispatchService tytTransportDispatchService;

    @Autowired
    private ExposurePermissionService exposurePermissionService;

    @Autowired
    private TransportHistoryService transportHistoryService;

    @Autowired
    private CommonApiService commonApiService;
    @Autowired
    private IFeedbackUserService feedbackUserService;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private TytTransportMbMergeMapper tytTransportMbMergeMapper;

    @Autowired
    private TytMbCargoSyncInfoMapper tytMbCargoSyncInfoMapper;

    @Autowired
    private TransportYMMService transportYMMService;

    @Autowired
    private TytUserIdentityAuthService tytUserIdentityAuthService;

    @Autowired
    private InvoiceTransportService invoiceTransportService;

    @Autowired
    private TytBlockConfigService tytBlockConfigService;

    @Autowired
    private TytUserRecordMapper tytUserRecordMapper;

    @Autowired
    private TytDispatchCompanyMapper tytDispatchCompanyMapper;

    @Resource
    private TytDispatchCargoOwnerMapper tytDispatchCargoOwnerMapper;

    @Autowired
    private TytGoodsRefreshUserService tytGoodsRefreshUserService;

    @Autowired
    private ExcellentGoodsService excellentGoodsService;

    @Autowired
    private CoverGoodsConfigService coverGoodsConfigService;

    @Autowired
    private TytCoverGoodsDialConfigService tytCoverGoodsDialConfigService;

    @Autowired
    private TransportEventTrickingService transportEventTrickingService;

    @Autowired
    TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;
    @Autowired
    private GoodsRefreshManualService goodsRefreshManualService;

    @Autowired
    TransportExtendService transportExtendService;
    @Autowired
    private TytTransportDispatchCarTypeMapper tytTransportDispatchCarTypeMapper;
    @Autowired
    private CallPhoneRecordServiceImpl callPhoneRecordService;

    @Autowired
    private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;

    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;

    @Autowired
    private TytTransportExtendMapper tytTransportExtendMapper;

    @Autowired
    ApiTradeInfoFeeClient apiTradeInfoFeeClient;

    @Autowired
    private TytCarMapper tytCarMapper;

    @Resource(name = "threadPoolExecutor")
    private ThreadPoolExecutor threadPoolExecutor;

    @Autowired
    private TransportAutoResendService transportAutoResendService;

    @Autowired
    private TransportSeckillFactorMapper transportSeckillFactorMapper;

    @Autowired
    private TytTransportSyncYmmService tytTransportSyncYmmService;

    @Autowired
    private ExcellentPriceConfigService excellentPriceConfigService;

    @Override
    public ResultMsgBean returnLockStatus() {
        return ResultMsgBean.failResponse(ResponseEnum.sys_error.info("该货源正在操作，请稍后再试！"));
    }

    /**
     * 普通货源redis锁
     *
     * @param tsId
     * @return
     */
    private String createTransportOptKey(Long tsId) {
        String redisKey = CommonUtil.joinRedisKey(RedisKeyConstant.transport_opt_lock, tsId + "");

        return redisKey;
    }

    /**
     * 后台货源redis锁
     *
     * @param backendId
     * @return
     */
    private String createBackendTransportKey(Long backendId) {
        String redisKey = CommonUtil.joinRedisKey(RedisKeyConstant.backend_transport_lock, backendId + "");

        return redisKey;
    }

    @Override
    public boolean lockTransportOpt(Long tsId) {
        if (tsId == null) {
            return true;
        }

        String redisKey = this.createTransportOptKey(tsId);

        String lockValue = System.currentTimeMillis() + "";
        //Boolean nxResult = RedisUtil.setNx(redisKey, lockValue, 5);
        Boolean nxResult = true;

        boolean lockResult = false;
        if (nxResult != null) {
            lockResult = nxResult;
        }
        return lockResult;
    }

    @Override
    public void unlockTransportOpt(Long tsId) {
        String redisKey = this.createTransportOptKey(tsId);
        RedisUtil.del(redisKey);
    }

    @Override
    public boolean lockBackendTransport(Long backendId) {
        if (backendId == null) {
            return true;
        }

        String redisKey = this.createBackendTransportKey(backendId);

        String lockValue = System.currentTimeMillis() + "";
        //Boolean nxResult = RedisUtil.setNx(redisKey, lockValue, 5);
        Boolean nxResult = true;

        boolean lockResult = false;
        if (nxResult != null) {
            lockResult = nxResult;
        }
        return lockResult;
    }

    @Override
    public ResponseCodeEnum checkTransportRefresh(Integer topInterval, Integer maxResendCounts, Integer resendCounts, Long userId, Timestamp dbCtime) {
        Timestamp nowTimeStamp = TimeUtil.getTimeStamp();

        //1. 校验刷新时间间隔
        long nowSecond = nowTimeStamp.getTime() / 1000L;
        long dbCtimeSecond = dbCtime.getTime() / 1000L;
        if (resendCounts > 0 && (nowSecond - dbCtimeSecond) < topInterval) {
            return ResponseCodeEnum.transport_top_limit_time;
        }

        //2. 校验最大刷新次数
        if (resendCounts >= maxResendCounts) {
            return ResponseCodeEnum.transport_max_top;
        }
        return null;
    }

    /**
     * 单独校验用户是否有置顶权益
     *
     * @param userId
     * @param usePermission
     * @return
     */
    public ResponseCodeEnum checkRefreshPermission(Long userId, boolean usePermission) {
        //校验权限，并判断是否扣除权益
        PermissionResult permissionResult = null;
        if (usePermission) {
            //直接扣除
            permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货源曝光权益, userId);
        } else {
            //只校验
            permissionResult = userPermissionService.checkAuthPermission(Permission.货源曝光权益, userId);
        }

        if (!permissionResult.getUse()) {
            logger.info("user_transport_top_permission_not_available, userId : {}", userId);
            return ResponseCodeEnum.transport_top_use_up;
        }
        return null;
    }

    @Override
    public ResponseCodeEnum transportAllowTop(Transport dbLastTransport, boolean usePermission) {
        Long userId = dbLastTransport.getUserId();

        //校验权益(产品强制要求先判断权益)
        ResponseCodeEnum checkRefreshEnum = this.checkRefreshPermission(userId, false);
        if (checkRefreshEnum != null) {
            return checkRefreshEnum;
        }

        //1. 校验刷新时间间隔
        Integer transportTopInterval = tytConfigService.getIntValue(Constant.transport_top_interval, 10 * 60);
        //2. 校验最大刷新次数
        Integer maxResendCounts = tytConfigService.getIntValue(Constant.transport_top_max_count, 16);

        //校验货源本身条件
        checkRefreshEnum = this.checkTransportRefresh(transportTopInterval, maxResendCounts, dbLastTransport.getResendCounts(), dbLastTransport.getUserId(), dbLastTransport.getCtime());
        if (checkRefreshEnum != null) {
            return checkRefreshEnum;
        }

        //校验并扣除权益
        if (usePermission) {
            checkRefreshEnum = this.checkRefreshPermission(userId, usePermission);
            if (checkRefreshEnum != null) {
                return checkRefreshEnum;
            }
        }
        return null;
    }

    /**
     * 保存货源信息，区分是否置顶
     *
     * @param transport
     * @param topFlag
     */
    @Override
    public void saveTransportAndCheckTop(Transport transport, TytTransportExtend transportExtend, boolean topFlag, Boolean useExposure) {

        if (useExposure == null) {
            useExposure = true;
        }
        Timestamp nowTimeStamp = TimeUtil.getTimeStamp();

        Long srcMsgId = transport.getSrcMsgId();

        Transport dbLastTransport = transportService.getLastBySrcMygId(srcMsgId);
        if (dbLastTransport == null) {
            throw TytException.createException(ResponseEnum.request_error.info("货源信息有误或不存在！"));
        }

        Integer resendCounts = dbLastTransport.getResendCounts();
        ;

        if (topFlag) {
            resendCounts = resendCounts + 1;
        }
        String pcOldContent = "[" + resendCounts + "]." + transport.getStartPoint().trim() + "---" + transport.getDestPoint().trim() + " " + transport.getTaskContent().trim();

        /* 设置重发次数并增加序号 ，保存transport信息*/
        transport.setResendCounts(resendCounts);
        transport.setPcOldContent(pcOldContent);
        transport.setTsOrderNo(dbLastTransport.getTsOrderNo());
        transport.setSrcMsgId(srcMsgId);
        transport.setReleaseTime(dbLastTransport.getReleaseTime());


        if (topFlag) {
            //置顶操作
            if (useExposure) {
                ResponseCodeEnum topCheckEnum = this.transportAllowTop(dbLastTransport, true);
                if (topCheckEnum != null) {
                    throw TytException.createException(topCheckEnum.info());
                }
            }

            transportService.add(transport);

            transportExtend.setId(null);
            transportExtend.setTsId(transport.getId());
            transportExtend.setSrcMsgId(srcMsgId);
            transportExtend.setCreateTime(transport.getCtime());
            transportExtend.setModifyTime(transport.getMtime());
            transportExtend.setTopFlag(useExposure ? 3 : 1);
            transportExtendService.addExtend(transportExtend);
            //添加曝光记录

            if (useExposure) {
                this.addExposurePermissionUsedRecord(transport);
                logger.info("save_transport_top_yes ：src_msg_id : {}, userId {} ", dbLastTransport.getSrcMsgId(), dbLastTransport.getUserId());
            }
        } else {
            BeanUtils.copyProperties(transport, dbLastTransport, "id", "srcMsgId", "tsOrderNo", "ctime", "releaseTime");
            dbLastTransport.setMtime(nowTimeStamp);

            transportService.update(dbLastTransport);
            // 更新扩展表时间
            TytTransportExtend dbLastExtend = transportExtendService.getByTsId(dbLastTransport.getId());
            if (dbLastExtend != null) {
                BeanUtils.copyProperties(transportExtend, dbLastExtend, "id", "tsId", "srcMsgId", "createTime");
                dbLastExtend.setModifyTime(nowTimeStamp);
                transportExtendService.updateExtend(dbLastExtend);
            }

            logger.info("save_transport_top_no : id : {}, src_msgId{}", dbLastTransport.getId(), dbLastTransport.getSrcMsgId());

            transport.setId(dbLastTransport.getId());
            transport.setCtime(dbLastTransport.getCtime());
            transport.setMtime(nowTimeStamp);

        }
    }

    /**
     * 添加货物信息到两张表
     *
     * @param transport
     * @throws Exception
     */
    @Override
    public Long addTransportBusiness(Transport transport, TransportSubBean transportSubBean) throws Exception {
        // 用于判断是否重发
        Long oldId = null;
        if (transport.getId() != null && transport.getId().longValue() != 0L) {
            oldId = transport.getId();
        }
        transport.setId(null);

        transport = filterRegion(transport);
        addReferTransportInfo(transport);
        // 距离从字典中取
//         if (transport.getDistanceValue() == null) {
//             TytMapDict
//                     tytMapDict = tytMapDictService.getDistance(String.valueOf(transport.getPlatId()), transport.getStartProvinc(),
//                     transport.getStartCity(), transport.getStartArea(),
//                     transport.getDestProvinc(), transport.getDestCity(),
//                     transport.getDestArea());
//             if (tytMapDict != null) {
//                 transport.setAndroidDistance(tytMapDict.getDistance());
//                 transport.setIosDistance(tytMapDict.getIosDistance());
//             }
//         } else {
//             if (transport.getPlatId().intValue() == 2) {
//                 transport.setAndroidDistance(transport.getDistanceValue());
//                 TytMapDict
//                         tytMapDict = tytMapDictService.getDistance(String.valueOf(transport.getPlatId()), transport.getStartProvinc(),
//                         transport.getStartCity(), transport.getStartArea(),
//                         transport.getDestProvinc(), transport.getDestCity(),
//                         transport.getDestArea());
//                 if (tytMapDict != null) {
//                     transport.setIosDistance(tytMapDict.getIosDistance());
//                 }
//             } else if (transport.getPlatId().intValue() == 3) {
//                 transport.setIosDistance(transport.getDistanceValue());
//                 TytMapDict
//                         tytMapDict = tytMapDictService.getDistance(String.valueOf(transport.getPlatId()), transport.getStartProvinc(),
//                         transport.getStartCity(), transport.getStartArea(),
//                         transport.getDestProvinc(), transport.getDestCity(),
//                         transport.getDestArea());
//                 if (tytMapDict != null) {
//                     transport.setAndroidDistance(tytMapDict.getDistance());
//                 }
//             }
//         }

        // 距离从字典中取
        TytMapDict tytMapDict = tytMapDictService.getDistance(String.valueOf(transport.getPlatId()), transport.getStartProvinc(), transport.getStartCity(), transport.getStartArea(), transport.getDestProvinc(), transport.getDestCity(), transport.getDestArea());
        if (tytMapDict != null) {
            transport.setAndroidDistance(tytMapDict.getDistance());
            transport.setIosDistance(tytMapDict.getIosDistance());
        } else {
            transport.setAndroidDistance(null);
            transport.setIosDistance(null);
        }

        // 老字段不用，清空
        transport.setDistanceValue(null);
        transport.setIsDisplay(1); // 不进行精准货源推荐，直接放入找货列表

        TransportMain oldTran = null;
        TransportMain transportMain = new TransportMain();
        BeanUtils.copyProperties(transport, transportMain);
        Transport transportOld = null;


        /* 主表新增数据 */
        if (oldId != null && oldId.longValue() != 0L) {
            oldTran = transportMainService.getTransportMainForId(oldId);
            if (oldTran.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime()) {
                transportMainService.add(transportMain);
            } else {
                oldTran.setMtime(TimeUtil.getTimeStamp());
                oldTran.setResendCounts(transport.getResendCounts());
                oldTran.setStatus(1);
                logger.info("源货物的ID为{}  src_msgId{}", oldTran.getId(), oldTran.getSrcMsgId());
                BeanUtils.copyProperties(transportMain, oldTran, "id", "srcMsgId", "tsOrderNo", "ctime");
                transport.setTsOrderNo(oldTran.getTsOrderNo());
                logger.info("copy之后源货物的ID为{}  src_msgId{}", oldTran.getId(), oldTran.getSrcMsgId());
                transportMainService.update(oldTran);
            }
        } else {
            transportMainService.add(transportMain);
        }
        if (oldId != null && oldId.longValue() != 0l) {
            // 判读是不是昨天的数据　如果是昨天的数据 重发则保存SrcMsgId 为新信息ID
            if (oldTran.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime()) {

                transportMain.setSrcMsgId(transportMain.getId());
                transport.setSrcMsgId(transportMain.getId());
            } else {// 是当天重发保持原信息ID
                transport.setSrcMsgId(oldTran.getSrcMsgId());
                // 加入重发置顶队列 用于修改 收藏与拨打电放对应的货物ID
            }
        } else {
            transportMain.setSrcMsgId(transportMain.getId());
            transport.setSrcMsgId(transportMain.getId());
        }
        // 如果出发地省市区为空或者不是标准化数据则不进行精准货源推荐
        if (transport.getIsStandard() != null && transport.getIsStandard() == 0 && StringUtils.isNotEmpty(transport.getStartArea()) && StringUtils.isNotEmpty(transport.getStartCity()) && StringUtils.isNotEmpty(transport.getStartProvinc())) {
            if (oldId == null || oldId.longValue() == 0) {
                logger.info("正常发布货源，进行精准推荐,货物ID：{}", transport.getId());
                transport.setIsDisplay(0);
                transportMain.setIsDisplay(0);
            } else if (oldId != null && oldId > 0 && oldTran.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime()) {
                logger.info("非今日编辑重发货物，进行精准推荐,货物ID：{}", transport.getId());
                transport.setIsDisplay(0);
                transportMain.setIsDisplay(0);
            } else {
                logger.info("今日编辑重发货物，不进行精准推荐,货物ID：{}", transport.getId());
            }
            // 精准货源是否在找货列表显示 0：找货列表不显示 1：找货列表显示
            Integer isDisplayOnoff = this.tytConfigService.getIntValue("recommendTransportIsDisplayOnoff", 0);
            if (isDisplayOnoff.intValue() == 1) {
                logger.info("货物直接再找货列表显示,货物ID：{}", transport.getId());
                transport.setIsDisplay(1); // 直接在找货列表中显示
                transportMain.setIsDisplay(1);
            }
        }
        if (oldId != null && oldId.longValue() != 0l) {
            // 判读是不是昨天的数据　如果是昨天的数据 重发则保存SrcMsgId 为新信息ID
            if (oldTran.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime()) {

                transportMainService.update(transportMain);
            } else {// 是当天重发保持原信息ID
            }
        } else {
            transportMainService.update(transportMain);
        }
        /* 子表新增数据 */
//		transport.setTsOrderNo(transportMain.getTsOrderNo());
        transportService.add(transport);

//		if (oldId != null && oldId.longValue() != 0l) {
//			// 判读是不是昨天的数据　如果是昨天的数据 重发则保存SrcMsgId 为新信息ID
//			if (oldTran.getCtime().getTime() > TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime()) {
//
//				// 是当天重发保持原信息ID
//				// 加入重发置顶队列 用于修改 收藏与拨打电放对应的货物ID
//				tytTopQueueService.addTytTopQueue(oldId, transport.getId(), 1);
//			}
//		}


//		TransportMain tran = transportMainService.getById(transport.getId());
//		tran.setSrcMsgId(transport.getSrcMsgId());
//		tran.setRegTime(transport.getRegTime());
//		tran.setIsDisplay(transport.getIsDisplay());
//		transportMainService.update(tran);

        tytUserSubService.saveChangeTytUserSub(transport.getUserId());
        // 更新相似货源信息
        transportService.updateSimilarityCode(transport);
        // 如果为标准化货源则存储运费利润率信息,只在第一次添加时存储信息
        TransportSubBean transportSubBeanTemp = transportSubService.getBySrcMsgId(transport.getSrcMsgId());
        if (transport.getIsStandard() != null && transport.getIsStandard() == 0 && transportSubBeanTemp == null && transportSubBean != null) {

            transportSubBean.setTsId(transport.getSrcMsgId());
            transportSubService.add(transportSubBean);
        }
        // 清除缓存
        cacheService.del(Constant.CACHE_USERSUB_KEY + transport.getUserId().longValue() + "_" + TimeUtil.formatDateMonthTime(new Date()));
        /* 保存实体到缓存 */
        // cacheService.setObject(Constant.CACHE_INFO_KEY + transport.getId(),
        // transport,AppConfig.getIntProperty("tyt.cache.info.time"));
        /* 保存hashcode到缓存，时间1天 */
        // cacheService.setObject(transport.getHashCode(),
        // transport.getHashCode(), Constant.CACHE_EXPIRE_TIME_24H);
        /* 发布条数计算 */
        // setPublicInfoNum(transport.getUserId());

        // TODO 向MQ发送无效货源信息验证请求
        if (transport != null && transport.getId() != null) {

            try{

                sendNullifyMessage2MQ(transport.getId());
            }catch (Exception e){
                logger.error("发送无效货源消息失败,货源id:{}",transport.getId());
            }
        }
        return transport.getId();
    }

    // 过滤地区
    public Transport filterRegion(Transport transport) {
        // "北京,上海,天津,重庆,香港,澳门,台湾"
        String filterRqionStr = this.tytConfigService.getStringValue("editPublishTransportZhixiashiList");
        String[] filterRqionArray = filterRqionStr.split(",");

        String startArea = null;
        String destArea = null;

        boolean startStatus = false;
        boolean destStatus = false;


        for (String s : filterRqionArray) {
            if (transport.getStartPoint().length() > 2 && transport.getStartPoint().indexOf(s) != -1) {
                if (transport.getStartPoint().indexOf("市") != -1) {
                    startArea = transport.getStartPoint().substring(transport.getStartPoint().indexOf("市") + 1);
                    if (startArea != null && startArea.length() > 0)
                        startStatus = true;
                }
                break;
            }
        }
        for (String s : filterRqionArray) {
            if (transport.getDestPoint().length() > 2 && transport.getDestPoint().indexOf(s) != -1) {
                if (transport.getDestPoint().indexOf("市") != -1) {
                    destArea = transport.getDestPoint().substring(transport.getDestPoint().indexOf("市") + 1);
                    if (destArea != null && destArea.length() > 0)
                        destStatus = true;
                }
                break;
            }
        }

        // 增加出发地目的地 省市区
        TytGeoDict startTytGeoDict = tytGeoDictService.getTytGeoDict(transport.getStartCoordXValue(), transport.getStartCoordYValue());
        if (startTytGeoDict != null) {
            if (startStatus) {
                transport.setStartArea(startArea);
            } else
                transport.setStartArea(startTytGeoDict.getArea());
            transport.setStartCity(startTytGeoDict.getCity());
            transport.setStartProvinc(startTytGeoDict.getProvinc());
        }

        TytGeoDict destTytGeoDict = tytGeoDictService.getTytGeoDict(transport.getDestCoordXValue(), transport.getDestCoordYValue());
        if (destTytGeoDict != null) {
            if (destStatus) {
                transport.setDestArea(destArea);
            } else
                transport.setDestArea(destTytGeoDict.getArea());
            transport.setDestCity(destTytGeoDict.getCity());
            transport.setDestProvinc(destTytGeoDict.getProvinc());
        }
        if (startStatus) {
            TytGeoDict startTytGeoDictNew = tytGeoDictService.getTytGeoDict(transport.getStartProvinc(), transport.getStartCity(), transport.getStartArea());
            if (startTytGeoDictNew != null) {
                transport.setStartCoordXValue(startTytGeoDictNew.getPx().intValue());
                transport.setStartCoordYValue(startTytGeoDictNew.getPy().intValue());
                transport.setStartLatitudeValue(startTytGeoDictNew.getLatitude().intValue());
                transport.setStartLongitudeValue(startTytGeoDictNew.getLongitude().intValue());
                transport.setStartCoord(transport.getStartCoordX() + "," + transport.getStartCoordY());
            }
        }
        if (destStatus) {
            TytGeoDict destTytGeoDictNew = tytGeoDictService.getTytGeoDict(transport.getDestProvinc(), transport.getDestCity(), transport.getDestArea());
            if (destTytGeoDictNew != null) {
                transport.setDestCoordXValue(destTytGeoDictNew.getPx().intValue());
                transport.setDestCoordYValue(destTytGeoDictNew.getPy().intValue());
                transport.setDestLatitudeValue(destTytGeoDictNew.getLatitude().intValue());
                transport.setDestLongitudeValue(destTytGeoDictNew.getLongitude().intValue());
                transport.setDestCoord(transport.getDestCoordX() + "," + transport.getDestCoordY());
            }
        }
        if (startStatus || destStatus)
            logger.info("编辑发布货物，经纬度修正：transport=【{}】", JSON.toJSONString(transport));

        return transport;
    }

    //补充货物信息的参考值，用于推荐货物过滤 v5930
    private void addReferTransportInfo(Transport transport) {
        //TODO 增加有好货参考长宽高重数值初始化
        //TODO 优先使用用户输入的，有为空的使用如果是标准化，则使用对应类型货物的默认数值
        // 兼容pc的bug，是否标准化未传值的问题
        if (transport.getIsStandard() == null) { // 如果未传值，则认定为非标货源
            transport.setIsStandard(1);
            transport.setMatchItemId(-1);
        }
        TytMachineTypeBean tytMachineTypeBean = null;
        if (transport.getIsStandard() == 0) {
            tytMachineTypeBean = machineTypeNewService.getTytMachineTypeForIdAll(transport.getMatchItemId().longValue());
            // 由于货源表针对goodtypename不全的数据，进行补齐
            String goodTypeName = "";
            if (transport.getTaskContent().length() > 5) {
                goodTypeName = transport.getTaskContent().substring(0, 5);
            } else {
                goodTypeName = transport.getTaskContent();
            }
            if (tytMachineTypeBean != null) {
                if (org.apache.commons.lang.StringUtils.isBlank(tytMachineTypeBean.getMachineType())) {
                    transport.setGoodTypeName(goodTypeName);
                } else {
                    transport.setGoodTypeName(tytMachineTypeBean.getMachineType());
                }
                if (org.apache.commons.lang.StringUtils.isNotBlank(tytMachineTypeBean.getType())) { // 解决线上的出现的type为null的异常情况
                    transport.setType(tytMachineTypeBean.getType());
                }
            } else {
                transport.setGoodTypeName(goodTypeName);
            }
        }

        //对参考值进行赋值
        if (StringUtils.isNotEmpty(transport.getLength()) && StringUtil.isDouble(transport.getLength())) {
            transport.setReferLength(HighwayCostCalcUtil.calcMultiply(transport.getLength(), "100").intValue());
        } else {
            if (transport.getIsStandard() == 0 && tytMachineTypeBean != null && tytMachineTypeBean.getLength() != null) {
                transport.setReferLength(HighwayCostCalcUtil.calcMultiply(tytMachineTypeBean.getLength(), "100").intValue());
            }
        }
        if (StringUtils.isNotEmpty(transport.getWide()) && StringUtil.isDouble(transport.getWide())) {
            transport.setReferWidth(HighwayCostCalcUtil.calcMultiply(transport.getWide(), "100").intValue());
        } else {
            if (transport.getIsStandard() == 0 && tytMachineTypeBean != null && tytMachineTypeBean.getWidth() != null) {
                transport.setReferWidth(HighwayCostCalcUtil.calcMultiply(tytMachineTypeBean.getWidth(), "100").intValue());
            }
        }
        if (StringUtils.isNotEmpty(transport.getHigh()) && StringUtil.isDouble(transport.getHigh())) {
            transport.setReferHeight(HighwayCostCalcUtil.calcMultiply(transport.getHigh(), "100").intValue());
        } else {
            if (transport.getIsStandard() == 0 && tytMachineTypeBean != null && tytMachineTypeBean.getHeight() != null) {
                transport.setReferHeight(HighwayCostCalcUtil.calcMultiply(tytMachineTypeBean.getHeight(), "100").intValue());
            }
        }
        if (StringUtils.isNotEmpty(transport.getWeight()) && StringUtil.isDouble(transport.getWeight())) {
            transport.setReferWeight(HighwayCostCalcUtil.calcMultiply(transport.getWeight(), "100").intValue());
        } else {
            if (transport.getIsStandard() == 0 && tytMachineTypeBean != null && tytMachineTypeBean.getWeight() != null) {
                transport.setReferWeight(HighwayCostCalcUtil.calcMultiply(tytMachineTypeBean.getWeight(), "100").intValue());
            }
        }
    }


//	/**
//	 * 发布货物信息时 设置用户的发布条数
//	 *
//	 * @param userId
//	 * @throws Exception
//	 */
//	@Override
//	public void setPublicInfoNum(Long userId) throws Exception {
//		String goodsNum = cacheService.getString(Constant.CACHE_INFO_NUMBER_KEY + TimeUtil.formatDateMonthTime(new Date()) + "_" + userId);
//		long counts = 0;
//		if (goodsNum == null || goodsNum.equals("") || !StringUtil.isNumeric(goodsNum)) {
//			counts = transportMainService.myGoodsLimitFromDB(userId);
//			cacheService.setString(Constant.CACHE_INFO_NUMBER_KEY + TimeUtil.formatDateMonthTime(new Date()) + "_" + userId, counts + "", Constant.CACHE_EXPIRE_LIMIT);
//		} else {
//			counts = Long.parseLong(goodsNum) + 1;
//			cacheService.setString(Constant.CACHE_INFO_NUMBER_KEY + TimeUtil.formatDateMonthTime(new Date()) + "_" + userId, counts + "", Constant.CACHE_EXPIRE_LIMIT);
//		}
//
//	};

    /**
     * APP重发
     *
     */
    // public void updateResendStatus(Integer status, TransportMain transport){
    // try {
    // /* 更新子表 */
    // transportService.updateStatus(status, transport.getId());
    // /* 更新主表状态值 */
    // transportMainService.updateStatus(status, transport.getId());
    // /* 信息变化表添加数据 */
    // transportVaryService.add(new TransportVary(transport.getId(), status));
    // /* 置收藏表数据为无效 */
    // if (status == 0)
    // transportCollectService.updateStatus(transport.getId(), 0);
    // } catch (Exception e) {
    // // TODO Auto-generated catch block
    // e.printStackTrace();
    // throw new RuntimeException(e);
    // }
    // }

    /**
     * 根据ids变更是否显示
     *
     * @param idList
     * @param display
     */
    @Override
    public int updateDisplayTypeByIdsBusiness(List<Long> idList, List<Long> idListMain, Integer display) {
        try {
            //由于 main表只有一条数据 所以 idList为一条
            /* 更新主表 */
            int cc = 0, c = 0;
            if (idListMain != null && idListMain.size() > 0)
                cc = transportMainService.updateDisplayTypeByIds(idListMain, display);
            /* 更新子表 */
            //由于 main表只有一条数据 所以 idList为一条 由于 srcMsgId和tsId一样 所以根据
            if (idList != null && idList.size() > 0)
                c = transportService.updateDisplayTypeBysrcMsgId(idList.get(0), display);

            return c | cc;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    //该方法可能已经弃用，请使用其他方法代替，如果确认弃用，请在此补充
    @Deprecated
    public void updateTransportStatus(Transport oldTransport, Integer status, Long userId) throws Exception {
        logger.warn("Deprecated_check_use ############# ");
        // 0无效(重发的时候需要把原信息置为无效);4成交;5撤销
        String recommendHashCode = StringUtil.getHashCodeNewByRecommend(oldTransport);

        switch (status) {
            case 0:
                updateStatusBusiness(oldTransport.getSrcMsgId(), recommendHashCode, userId, 0, null);
                // 其他待支付的改成对用户不可见
                if (oldTransport.getIsInfoFee().equals("1")) {
                    transportOrdersService.saveChangeWaitToDisappear(userId, oldTransport.getTsOrderNo());
                }
                break;
            case 4:
                boolean flag = updateStatusBusiness(oldTransport.getSrcMsgId(), recommendHashCode, userId, 4, "6");
                // 若有值修改成功，则运单表增肌数据、气泡数量变化
                if (flag) {
                    // 运单表增加一条数据
                    transportWayBillService.saveOffLineDealGoodsToTransportWayBill(oldTransport, "6");
                    // 发货方 装货完成/线下成交 气泡数量加一
                    tytBubbleService.updateBubbleNumber(userId, "2", "2", 1);
                }
                break;
            case 5:
                updateStatusBusiness(oldTransport.getSrcMsgId(), recommendHashCode, userId, 5, null);
                break;
        }
    }

    /**
     * 根据hashCode更新信息状态
     *
     * @param status
     */
    @Override
    public boolean updateStatusBusiness(Long srcMsgId, String recommendHashCode, Long userId, Integer status,
                                        String infoStatus, boolean saveVary) throws Exception {

        /*
         * infoStatus:信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成
         * 5异常上报
         */
        // status=4时,infoStatus=3
        // 根据hashcode获取今天有效的ID集合
        List<Long> todayIdListMain = transportMainService.getTodayIdListBySrcMsgId(srcMsgId, userId, status);
        List<Long> todayIdList = transportService.getTodayIdListBySrcMsgId(srcMsgId, userId, status);
        logger.info("APP修改信息状态id集合【{}】", todayIdList);
        // 是否显示
        int display = status.intValue() == 0 ? 0 : 1;
        boolean flag = false;
        if (todayIdList != null && todayIdList.size() > 0) {
            /* 修改tyt_transport表今天的数据状态 */
            flag = transportService.updateStatusByIds(todayIdList, status, infoStatus, display);
            if (flag) {
                //如果置为无效的话  则不过更新状态
                if (status == 0) {
                } else {
                    /* 修改tyt_transport_main表今天的数据状态 */
                    transportMainService.updateStatusByIds(todayIdListMain, status, infoStatus, display);
                }
                /* 添加信息到transport_vary表 */
                if (saveVary) {
                    transportVaryService.addVarys(todayIdList, status);
                }
                /* 相应的缓存删除 */
                //cacheService.del(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + hashCode);
                cacheService.del(Constant.CACHE_RECOMMEND_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + recommendHashCode);

            }

        }
        // 根据hashcode获历史ID集合
        if (status == 0) {// 历史数据不可能设置为成交或者撤销
            List<Long> historyIdListMain = transportMainService.getHistoryIdListBySrcMsgId(srcMsgId, userId);
            List<Long> historyIdList = transportService.getHistoryIdListBySrcMsgId(srcMsgId, userId);
            if ((historyIdList != null && historyIdList.size() > 0)
                    || (historyIdListMain != null && historyIdListMain.size() > 0)) {// 相关历史数据置为不显示
                //由于 main表只有一条数据 所以 idList为一条
                if (this.updateDisplayTypeByIdsBusiness(historyIdList, historyIdListMain, 0) > 0) {
                    if (!flag)
                        flag = true;
                }
            }
        }
        return flag;
    }

    @Override
    public boolean updateStatusBusiness(Long srcMsgId, String recommendHashCode, Long userId, Integer status, String infoStatus) throws Exception {
        boolean result = this.updateStatusBusiness(srcMsgId, recommendHashCode, userId, status, infoStatus, true);
        return result;
    }

    //该方法可能已经弃用，请使用其他方法代替，如果你明确该方法是否弃用，请在此补充
    @Deprecated
    @Override
    public ResultMsgBean saveInfoFeeUpdateBtnStatus(Long userId, Integer operateType, Long goodsId, ResultMsgBean resultMsgBean) throws Exception {
        logger.warn("Deprecated_check_use ############# ");
        // 货源ID合法性验证
        Transport oldTransport = this.getByGoodsIdForUnLock(goodsId);
        try {
            int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("1", oldTransport.getSrcMsgId() + "", redisLockTimeout)) {
                if (oldTransport == null) {
                    resultMsgBean.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
                    resultMsgBean.setMsg("goodsId所代表的对象不存在");
                    return resultMsgBean;
                }
                // 不能修改别人的货源
                if (userId.longValue() != oldTransport.getUserId().longValue()) {
                    resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    resultMsgBean.setMsg("不能操作别人的货源");
                    return resultMsgBean;
                }
                // 有人支付成功的不能撤销
                if (operateType == 1 && oldTransport.getIsInfoFee().equals("1") && !oldTransport.getInfoStatus().equals("0")) {
                    resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_NOT_ALLOWED_CANCEL);
                    resultMsgBean.setMsg(propertiesFileUtil.getString("tyt.infofee.mygoods.cancel.error.5000"));
                    return resultMsgBean;
                }
                // 有人支付成功的不能成交
                if (operateType == 2 && oldTransport.getIsInfoFee().equals("1") && !oldTransport.getInfoStatus().equals("0")) {
                    resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_NOT_ALLOWED_DEAL);
                    resultMsgBean.setMsg(propertiesFileUtil.getString("tyt.infofee.mygoods.deal.error.5001"));
                    return resultMsgBean;
                }
                String tranportNo = oldTransport.getTsOrderNo();
                TytTransportWaybill oldTransportWayBill = transportWayBillService.getByIdForLock(tranportNo);
                /* operateBtnType 1撤销货源 2成交货源 */
                Long oldSrcMsgId = oldTransport.getSrcMsgId();
                String isInfoFee = oldTransport.getIsInfoFee();
                String recommendHashCode = StringUtil.getHashCodeNewByRecommend(oldTransport);
                logger.info("saveInfoFeeUpdateBtnStatus 精准货源 recommendHashCode is:{}", recommendHashCode);
                switch (operateType) {
                    case 1:
                        this.updateStatusBusiness(oldSrcMsgId, recommendHashCode, userId, 5, null);
                        // 其他待支付的改成对用户不可见
                        if (oldTransport.getIsInfoFee().equals("1")) {
                            transportOrdersService.saveChangeWaitToDisappear(userId, oldTransport.getTsOrderNo());
                        }

                        /**
                         *  运满满货源
                         */
                        if (SourceTypeEnum.运满满货源.getId().equals(oldTransport.getSourceType())) {
                            transportYMMService.expireYMMCargoMerge(oldSrcMsgId);

                            transportYMMService.pushMbCargoExpireMessage(oldSrcMsgId);
                        }
                        break;
                    case 2:
                        /**
                         * if:用户自己手动设置成交[线下成交] else:货源处于装货中[线上装货中的成交(infoStatus=2时status=4)]
                         */
                        if (!isInfoFee.equals("1")) {
                            boolean flag = this.updateStatusBusiness(oldSrcMsgId, recommendHashCode, userId, 4, "6");
                            if (flag) {
                                // 运单表增加一条数据
                                transportWayBillService.saveOffLineDealGoodsToTransportWayBill(oldTransport, "6");
                                // 发货方 装货完成/线下成交 气泡数量加一
                                tytBubbleService.updateBubbleNumber(userId, "2", "2", 1);
                            }
                        } else {
                            logger.info("---------------------------修改状态开始");
                            boolean flag = this.updateStatusBusiness(oldSrcMsgId, recommendHashCode, userId, 4, "6");
                            logger.info("---------------------------修改状态结束");
                            if (flag) {

                                if (oldTransportWayBill == null) {
                                    // 运单表增加一条数据
                                    transportWayBillService.saveOffLineDealGoodsToTransportWayBill(oldTransport, "6");
                                } else {
                                    // 运单表的数据修改为线下成交
                                    transportWayBillService.saveChangeInfoStatus(userId, tranportNo, "6");
                                }
                                logger.info("---------------------------修改运单结束");
                                // 发货方 装货完成/线下成交 气泡数量加一
                                tytBubbleService.updateBubbleNumber(userId, "2", "2", 1);
                                logger.info("---------------------------修改气泡结束");
                                // 待支付的全部取消
                                transportOrdersService.saveChangeWaitToDisappear(userId, tranportNo);
                                logger.info("---------------------------修改取消待支付结束");
                            }

                        }
                        break;
                }
            } else {
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("goodsId所代表的对象锁未释放");
                return resultMsgBean;
            }
        } finally {
            if (oldTransport != null) {
                logger.info("撤销、设置成交 callback get redis release lock, src msg id is: " + oldTransport.getSrcMsgId());
                LockUtil.unLockObject("1", oldTransport.getSrcMsgId() + "");
            }
        }
        return resultMsgBean;
    }

    @Override
    public ResultMsgBean saveInfoFeeUpdateBtnStatusNew(Long userId, Integer operateType, Long goodsId, TransportDoneRequest doneRequest,
                                                       String backoutReasonKey, Integer backoutReasonValue, String specificReason,
                                                       String remark, boolean saveVary, String backoutReasonKeyNew, Integer backoutReasonValueNew) throws Exception {

        TransportStatusOption transportStatusOption = new TransportStatusOption(userId, operateType, goodsId, backoutReasonKey,
                backoutReasonValue, specificReason, remark, saveVary, backoutReasonKeyNew, backoutReasonValueNew);

        ResultMsgBean resultMsgBean = this.saveInfoFeeUpdateBtnStatusNew(transportStatusOption, doneRequest);

        return resultMsgBean;
    }

    @Override
    public ResultMsgBean saveInfoFeeUpdateBtnStatusNew(TransportStatusOption transportStatusOption,
                                                       TransportDoneRequest doneRequest) throws Exception {
        Long userId = transportStatusOption.getUserId();
        Integer operateType = transportStatusOption.getOperateType();
        Long goodsId = transportStatusOption.getGoodsId();
        String backoutReasonKey = transportStatusOption.getBackoutReasonKey();
        Integer backoutReasonValue = transportStatusOption.getBackoutReasonValue();
        String specificReason = transportStatusOption.getSpecificReason();
        String remark = transportStatusOption.getRemark();
        Boolean saveVary = transportStatusOption.getSaveVary();
        Boolean keepExposure = transportStatusOption.getKeepExposure();

        String backoutReasonKeyNew = transportStatusOption.getBackoutReasonKeyNew();
        Integer backoutReasonValueNew = transportStatusOption.getBackoutReasonValueNew();

        Transport lockTransport = this.getByGoodsIdForUnLock(goodsId); // 仅仅是为了获取srcMsgId，用于分布式锁
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("1", lockTransport.getSrcMsgId() + "", redisLockTimeout)) {
                // 货源ID合法性验证
                Transport oldTransport = this.getByGoodsIdForUnLock(goodsId);
                if (oldTransport == null) {
                    resultMsgBean.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
                    resultMsgBean.setMsg("goodsId所代表的对象不存在");
                    return resultMsgBean;
                }
                // 不能修改别人的货源
                if (userId.longValue() != oldTransport.getUserId().longValue()) {
                    resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    resultMsgBean.setMsg("不能操作别人的货源");
                    return resultMsgBean;
                }
                boolean isYesterdayPublish = oldTransport.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime();
                if (!isYesterdayPublish) { // 今天的货源已成交，不能重新发布
                    if (oldTransport.getStatus() == 4) { // 如果货源已成交，则不能重新发布
                        resultMsgBean.setCode(ResultMsgBean.ERROR);
                        resultMsgBean.setMsg("您的货源已成交");
                        return resultMsgBean;
                    }
                }
                // 仅发布中的货源才可以进行撤销，非发布中的货源直接返回结果
                if (oldTransport.getStatus() != 1) { // 1为发布中
                    resultMsgBean.setCode(ReturnCodeConstant.OK);
                    resultMsgBean.setMsg("操作成功");
                    return resultMsgBean;
                }

                Long srcMsgId = oldTransport.getSrcMsgId();
                String tranportNo = oldTransport.getTsOrderNo();
                //TytTransportWaybill oldTransportWayBill = transportWayBillService.getByIdForLock(tranportNo);
                /* operateBtnType 1撤销货源 2成交货源 */
                String isInfoFee = oldTransport.getIsInfoFee();
                String recommendHashCode = StringUtil.getHashCodeNewByRecommend(oldTransport);
                logger.info("saveInfoFeeUpdateBtnStatus 精准货源 srcMsgId:{} recommendHashCode is:{}", srcMsgId, recommendHashCode);
                BackendTransportBean backendTransportBean = backendTransportMapper.selectBackendStatusByMsgId(oldTransport.getSrcMsgId());
                switch (operateType) {
                    case 1://撤销
                        if (backoutReasonValue.equals(BackoutReasonEnum.change_fixed_price.getCode())) {
                            //一口价撤销
                            Long existOrderId = transportOrdersService.checkUsefulOrderExist(srcMsgId);

                            if (existOrderId != null) {
                                resultMsgBean.setCode(ReturnCodeConstant.change_fixed_price_error);
                                resultMsgBean.setMsg("该货源已被车方支付，不可继续转为一口价货源!");
                                return resultMsgBean;
                            }
                        }

                        //1.更新tyt_transport表和tyt_transport_main表货源状态
                        this.updateStatusBusiness(srcMsgId, recommendHashCode, userId, 5, null, saveVary);
                        //2.更新tyt_transport_orders表待支付信息费订单状态为取消
                        transportOrdersService.saveChangeWaitToDisappearNew(userId, oldTransport.getTsOrderNo());
                        //3.货源撤销原因
                        backoutReasonService.addBackoutReason(lockTransport, backoutReasonKey, backoutReasonValue, specificReason, remark, backoutReasonKeyNew, backoutReasonValueNew);
                        if (backendTransportBean != null) {
                            //将企业货源设置为撤销,
                            if (backendTransportBean.getStatus() == 2) {
                                //如果山推方已取消，则stutus状态不改变，oederstatus改为31-发布中，用于在调车数量大于一时，有一人支付，其他人未支付的情况下，该订单判断是否有人支付
                                backendTransportMapper.updateBackendStatusInfo(2, 31, oldTransport.getSrcMsgId());
                                //记录企业货源状态流转日志
                                OwnerCompanyLog ownerCompanyLog = new OwnerCompanyLog();
                                ownerCompanyLog.setOrderNo(backendTransportBean.getOrderNo());
                                ownerCompanyLog.setBackendId(backendTransportBean.getId());
                                ownerCompanyLog.setCompanyId(backendTransportBean.getReceiverUserId());
                                ownerCompanyLog.setEnterpriseId(backendTransportBean.getAppletsUserId());
                                ownerCompanyLog.setStatus(2);
                                ownerCompanyLog.setOrderStatus(31);
                                ownerCompanyLog.setSrcMsgId(backendTransportBean.getMsgId());
                                ownerCompanyLogService.addOwnerCompanyLog(ownerCompanyLog);
                                transportBackendAxbBinderService.saveAxbUnbinder(backendTransportBean.getTel(), backendTransportBean.getTel3(), userId);
                            } else {
                                //如果山推方未取消，则山推状态Status改为3-已接单
                                backendTransportMapper.updateBackendStatusInfo(3, 32, oldTransport.getSrcMsgId());
                                //记录企业货源状态流转日志
                                OwnerCompanyLog ownerCompanyLog = new OwnerCompanyLog();
                                ownerCompanyLog.setOrderNo(backendTransportBean.getOrderNo());
                                ownerCompanyLog.setBackendId(backendTransportBean.getId());
                                ownerCompanyLog.setCompanyId(backendTransportBean.getReceiverUserId());
                                ownerCompanyLog.setEnterpriseId(backendTransportBean.getAppletsUserId());
                                ownerCompanyLog.setStatus(3);
                                ownerCompanyLog.setOrderStatus(32);
                                ownerCompanyLog.setSrcMsgId(backendTransportBean.getMsgId());
                                ownerCompanyLogService.addOwnerCompanyLog(ownerCompanyLog);
                                transportBackendAxbBinderService.saveAxbUnbinder(backendTransportBean.getTel(), backendTransportBean.getTel3(), userId);
                            }
                        }

                        /**
                         *  运满满货源
                         */
                        if (SourceTypeEnum.运满满货源.getId().equals(oldTransport.getSourceType())) {
                            transportYMMService.expireYMMCargoMerge(srcMsgId);

                            transportYMMService.pushMbCargoExpireMessage(srcMsgId);
                        }
                        sendBackoutMessage2MQ(oldTransport);
                        //专车 货源撤销,其他人的待支付单变成取消
                        if (oldTransport.getExcellentGoods() == 2) {
                            CompletableFuture.runAsync(() -> {
                                transportOrdersService.saveOtherUserChangeWaitToDisappearNew(userId, oldTransport.getTsOrderNo());
                            });
                            //货源撤销，把专车货源是否大厅抢单强制改为否
                            tytSpecialCarDispatchFailureMapper.updateDeclareInPublicToNo(oldTransport.getId());
                        }

                        //抽佣货源手动下架触发主动通话记录音转文

                        if (oldTransport != null
                                && StringUtils.isNotBlank(oldTransport.getLabelJson())
                                && JSONObject.parseObject(oldTransport.getLabelJson()).containsKey("commissionTransport")
                                && JSONObject.parseObject(oldTransport.getLabelJson()).getInteger("commissionTransport") == 1) {
                            Integer callPhoneRecordAsrOnOff = tytConfigService.getIntValue("call_phone_record_asr_on_off", 0);
                            if (callPhoneRecordAsrOnOff == 1) {
                                callPhoneRecordService.asrCreate(srcMsgId);
                            }
                        }
                        break;
                    case 2://成交
                        // 6610 参与现金奖活动的货源判断
                        judgeIsCashPrizeActivityGoodsWhenDealt(oldTransport);
                        //1.更新tyt_transport表和tyt_transport_main表货源状态
                        this.updateStatusBusiness(srcMsgId, recommendHashCode, userId, 4, "6", saveVary);
                        //2.更新tyt_transport_orders表待支付信息费订单状态为成交
                        transportOrdersService.saveChangeWaitToDisappearNew(userId, tranportNo);
                        //3.保存成交记录
                        transportDoneService.saveTransportDone(userId, goodsId, doneRequest);
                        //如果是取消状态 不修改为已成交
                        if (backendTransportBean != null && backendTransportBean.getStatus() != 2) {
                            backendTransportMapper.updateBackendStatusForDone(oldTransport.getSrcMsgId());

                            //记录企业货源状态流转日志
                            OwnerCompanyLog ownerCompanyLog = new OwnerCompanyLog();
                            ownerCompanyLog.setOrderNo(backendTransportBean.getOrderNo());
                            ownerCompanyLog.setBackendId(backendTransportBean.getId());
                            ownerCompanyLog.setCompanyId(backendTransportBean.getReceiverUserId());
                            ownerCompanyLog.setEnterpriseId(backendTransportBean.getAppletsUserId());
                            ownerCompanyLog.setStatus(6);
                            ownerCompanyLog.setOrderStatus(60);
                            ownerCompanyLog.setSrcMsgId(backendTransportBean.getMsgId());
                            ownerCompanyLogService.addOwnerCompanyLog(ownerCompanyLog);
                            transportBackendAxbBinderService.saveAxbUnbinder(backendTransportBean.getTel(), backendTransportBean.getTel3(), userId);
                        }
                        //专车 货源设成交,其他人的待支付单变成取消
                        if (oldTransport.getExcellentGoods() == 2) {
                            CompletableFuture.runAsync(() -> {
                                transportOrdersService.saveOtherUserChangeWaitToDisappearNew(userId, oldTransport.getTsOrderNo());
                            });
                        }
                        // 保存用户发布优车2.0电议货源次数
                        try {
                            thPriceClient.saveTransferTeleDealCount(userId, oldTransport.getSrcMsgId()).execute();
                        } catch (IOException e) {
                            logger.info("保存用户发布优车2.0电议货源次数 异常", e);
                        }
                        break;
                }

                if (keepExposure == null || !keepExposure) {
                    //移除曝光池
                    tytTransportExposureService.disableExposureTransport(srcMsgId);
                    // 移除意向货源
                    // tytTransportIntentionService.disableIntentionTransport(srcMsgId);
                }
            } else {
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("goodsId所代表的对象锁未释放");
                return resultMsgBean;
            }
        } catch (Exception e) {
            logger.error("撤销、设置成交 callback error:", e);
        } finally {
            if (lockTransport != null) {
                logger.info("撤销、设置成交 callback get redis release lock, src msg id is: " + lockTransport.getSrcMsgId());
                LockUtil.unLockObject("1", lockTransport.getSrcMsgId() + "");
            }
        }
        return resultMsgBean;
    }

    /**
     * 货源成交时校验：如果是现金奖货源，打标
     */
    private void judgeIsCashPrizeActivityGoodsWhenDealt(Transport transport) {
        TransportMain transportMain = new TransportMain();
        BeanUtils.copyProperties(transport, transportMain);
        boolean cashPrizeActivityTransport = transportMainService.isCashPrizeActivityTransport(transportMain);
        if (cashPrizeActivityTransport) {
            TransportLabelJson transportLabelJson = JSON.parseObject(transport.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson == null) {
                transportLabelJson = new TransportLabelJson();
            }
            transportLabelJson.setIsCashPrizeActivityGoods(1);
            transportMainService.updateLabelJsonBySrcMsgId(transport.getSrcMsgId(), JSON.toJSONString(transportLabelJson));
        }
    }

    /**
     * @param goodsId 已知人工派单使用tsId， plat撤销成交为srcId
     * @return
     * @throws Exception
     */
/*    public Transport getByGoodsIdForUnLock(Long goodsId) throws Exception {
        Transport oldTransport = null;
        TransportMain oldTransportMain = transportMainService.getById(goodsId);
        if (oldTransportMain != null) {
            Long maxId = transportService.getMaxIdbytransId(oldTransportMain.getId());
            oldTransport = transportService.getById(maxId);
            if (oldTransport == null) {
                oldTransport = new Transport();
                BeanUtils.copyProperties(oldTransportMain, oldTransport);
            }
        } else {
            oldTransport = transportService.getById(goodsId);
            logger.info("根据货物ID查询最新的货物ID,goodsId:{}", goodsId);
        }
        return oldTransport;
    }*/
    public Transport getByGoodsIdForUnLock(Long goodsId) throws Exception {
        TransportMain oldTransportMain = transportMainService.getById(goodsId);
        if (oldTransportMain != null) {
            Transport oldTransport = new Transport();
            BeanUtils.copyProperties(oldTransportMain, oldTransport);
            logger.info("根据货物ID查询最新的货物ID,goodsId:{}", oldTransport.getId());
            return oldTransport;
        } else {
            Transport oldTransport = transportService.getById(goodsId);
            if (oldTransport != null) {
                oldTransportMain = transportMainService.getById(oldTransport.getSrcMsgId());
                if (oldTransportMain != null) {
                    Transport transport = new Transport();
                    BeanUtils.copyProperties(oldTransportMain, transport);
                    logger.info("根据货物ID查询最新的货物ID,goodsId:{}", transport.getId());
                    return transport;
                }
            }
        }
        return null;

    }

    //该方法可能已经弃用，请使用其他方法代替，如果你明确该方法是否弃用，请在此补充
    @Deprecated
    @Override
    public void saveInfoFeeUpdateBtnStatus(Long userId, Integer operateType, Transport oldTransport) throws Exception {
        /* operateBtnType 1撤销货源 2成交货源 */
        logger.warn("Deprecated_check_use ############# ");
        String hashCode = oldTransport.getHashCode();
        Long srcMsgId = oldTransport.getSrcMsgId();
        String recommendHashCode = StringUtil.getHashCodeNewByRecommend(oldTransport);
        switch (operateType) {
            case 1:
                this.updateStatusBusiness(srcMsgId, recommendHashCode, userId, 5, null);
                // 其他待支付的改成对用户不可见
                if (oldTransport.getIsInfoFee().equals("1")) {
                    transportOrdersService.saveChangeWaitToDisappear(userId, oldTransport.getTsOrderNo());
                }
                break;
            case 2:
                /**
                 * if:用户自己手动设置成交[线下成交] else:货源处于装货中[线上装货中的成交(infoStatus=2时status=4)]
                 */
                String isInfoFee = oldTransport.getIsInfoFee();
                if (!isInfoFee.equals("1")) {
                    boolean flag = this.updateStatusBusiness(srcMsgId, recommendHashCode, userId, 4, "3");
                    if (flag) {
                        // 运单表增加一条数据
                        transportWayBillService.saveOffLineDealGoodsToTransportWayBill(oldTransport, "3");
                        // 发货方 装货完成/线下成交 气泡数量加一
                        tytBubbleService.updateBubbleNumber(userId, "2", "2", 1);
                    }
                } else {
                    this.updateStatusBusiness(srcMsgId, recommendHashCode, userId, 4, "2");
                    // 发货方 待同意 气泡数量减一
                    tytBubbleService.updateBubbleNumber(userId, "2", "1", -1);
                }
                break;
        }
    }

    @Override
    public int saveChangeInfoStatus(String infoStatus, Long srcMsgId) throws Exception {
        int c = transportService.saveChangeInfoFeeStatus(infoStatus, srcMsgId);
        if (c > 0) {
            transportMainService.saveChangeInfoFeeStatus(infoStatus, srcMsgId);
        }
        return c;
    }

    private void sendResendMessage(Long tsId) {
        int needResend = tytConfigService.getIntValue(Constant.RECOMMEND_NEED_DIRECT_REPUB, 1);
        logger.info("push recommend needResend is: " + needResend);
        if (needResend == 1) {
            ResendGoodsMsg resendGoodsMsg = new ResendGoodsMsg();
            String serialNum = SerialNumUtil.generateSeriaNum();
            resendGoodsMsg.setMessageSerailNum(serialNum);
            resendGoodsMsg.setMessageType(MqBaseMessageBean.RESEND_GOODS_MESSAGE);
            resendGoodsMsg.setTsId(tsId + "");
            logger.info("push recommend resendGoodsMsg is: " + resendGoodsMsg);
            tytMqMessageService.addSaveMqMessage(resendGoodsMsg.getMessageSerailNum(), JSON.toJSONString(resendGoodsMsg), resendGoodsMsg.getMessageType());
            String delayedTime = tytConfigService.getStringValue(Constant.RECOMMEND_PLAT_RESEND_TIME, "60");
            logger.info("push recommend resend delay time is: " + delayedTime);
            tytMqMessageService.sendDelayedMqMessage(resendGoodsMsg.getMessageSerailNum(), JSON.toJSONString(resendGoodsMsg), resendGoodsMsg.getMessageType(), Long.valueOf(delayedTime) * 1000);
        }
    }

    // 还原货物信息到找货列表
    private void updateRevertTransportStatus(Transport transport) {
        if (transport != null && transport.getId() != null && transport.getId().longValue() > 0) {
            transport = transportService.getById(transport.getId());
            transport.setIsDisplay(1);
            transportService.update(transport);
            logger.info("还原找货列表transport状态: srcId:{},id:{},ctime:{}", transport.getSrcMsgId(), transport.getId(), transport.getCtime());
            TransportMain transportMain = transportMainService.getBySrcMsgId(transport.getSrcMsgId());
            if (transportMain != null) {
                transportMain.setIsDisplay(1);
                logger.info("还原找货列表transportMain状态: srcId:{},id:{},ctime:{}", transportMain.getSrcMsgId(), transportMain.getId(), transportMain.getCtime());
                transportMainService.update(transportMain);
            }
        }
    }

    /**
     * 向MQ发送准货源推荐信息
     *
     * @param transId 货物ID
     */
    private void sendRecommendMessage2MQ(Long transId) {
        sendResendMessage(transId);
        // 发送精准货源推荐信息MQ
        MqTransportMsg mqTransportMsg = new MqTransportMsg();

        mqTransportMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqTransportMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_RECOMMEND_TRANSPORT_MESSAGE);
        mqTransportMsg.setTsId(transId);
        // 保存mq信息到数据库,并发送MQ消息
        tytMqRecommMessageService.addSaveMqMessage(mqTransportMsg.getMessageSerailNum(), JSON.toJSONString(mqTransportMsg), mqTransportMsg.getMessageType());
        tytMqRecommMessageService.sendMqMessage(mqTransportMsg.getMessageSerailNum(), JSON.toJSONString(mqTransportMsg), mqTransportMsg.getMessageType());
    }

    /**
     * 向MQ发送信息
     *
     * @param transId 货物ID
     */
    private void sendNullifyMessage2MQ(Long transId) {
        // 发送初货物无效信息MQ
        MqUserMsg mqUserMsg = new MqUserMsg();

        mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqUserMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_NULLIFY_TRANSPORT_MESSAGE);
        mqUserMsg.setUserId(transId);
        mqUserMsg.setTsId(transId);
        // 保存mq信息到数据库
        // tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

        long mqDelayTime = tytConfigService.getIntValue(Constant.publish_mq_delay_time, 1500).longValue();

        logger.info("publish_mq_delay_time : " + mqDelayTime);
        // 发送货物无效信息
//        tytMqMessageService.sendMqMessageDirect(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqDelayTime);
        tytMqMessageService.sendMsgCustom(JSON.toJSONString(mqUserMsg), "GOODS_CENTER_TOPIC", mqUserMsg.getMessageSerailNum(), "TRANSPORT_PUBLISH", mqDelayTime);

    }

    /**
     * 货源撤销想mq发送消息
     *
     * @param transport
     */
    private void sendBackoutMessage2MQ(Transport transport) {
        // 货源撤销发送mq
        MqTransportBackoutMsg mqTransportBackoutMsg = new MqTransportBackoutMsg();

        mqTransportBackoutMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqTransportBackoutMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_BACKOUT_TRANSPORT_MESSAGE);
        mqTransportBackoutMsg.setSrcMsgId(transport.getSrcMsgId());
        mqTransportBackoutMsg.setPrice(transport.getPrice());
        mqTransportBackoutMsg.setPubDate(transport.getPubDate());
        // 保存mq信息到数据库
        tytMqMessageService.addSaveMqMessage(mqTransportBackoutMsg.getMessageSerailNum(), JSON.toJSONString(mqTransportBackoutMsg), mqTransportBackoutMsg.getMessageType());

        tytMqMessageService.sendMqMessage(mqTransportBackoutMsg.getMessageSerailNum(), JSON.toJSONString(mqTransportBackoutMsg), mqTransportBackoutMsg.getMessageType());

    }


    /**
     * 原来的方法，该方法有问题，transport 的 id 永远都是错的
     *
     * @param goodsId
     * @return
     */
    @Deprecated
    public Transport getByGoodsIdBak(Long goodsId) {
        TransportMain oldTransportMain = transportMainService.getById(goodsId);
        if (oldTransportMain != null) {
            Transport oldTransport = new Transport();
            BeanUtils.copyProperties(oldTransportMain, oldTransport);
            logger.info("根据货物ID查询最新的货物ID,goodsId:{}", oldTransport.getId());
            return oldTransport;
        } else {
            //如果传递参数为非 src_msg_id，虽然兼容，但是提示错误，以便后期修复
            logger.error("wrong_src_msg_id_quest : " + goodsId);
            Transport oldTransport = transportService.getById(goodsId);
            oldTransportMain = transportMainService.getById(oldTransport.getSrcMsgId());
            if (oldTransportMain != null) {
                Transport transport = new Transport();
                BeanUtils.copyProperties(oldTransportMain, transport);
                logger.info("根据货物ID查询最新的货物ID,goodsId:{}", transport.getId());
                return transport;
            }
        }
        return null;
    }

    @Override
    public Transport getByGoodsId(Long goodsId) throws Exception {
        TransportMain oldTransportMain = transportMainService.getById(goodsId);
        if (oldTransportMain != null) {
            Transport oldTransport = new Transport();
            BeanUtils.copyProperties(oldTransportMain, oldTransport);
            logger.info("根据货物ID查询最新的货物ID,goodsId:{}", oldTransport.getId());
            return oldTransport;
        } else {
            Transport oldTransport = transportService.getById(goodsId);
            oldTransportMain = transportMainService.getById(oldTransport.getSrcMsgId());
            if (oldTransportMain != null) {
                Transport transport = new Transport();
                BeanUtils.copyProperties(oldTransportMain, transport);
                logger.info("根据货物ID查询最新的货物ID,goodsId:{}", transport.getId());
                return transport;
            }

        }
        return null;
    }

    @Override
    public int updateSrcMsgId(Long goodsId, Long srcMsgId) throws Exception {
        int c = transportService.updateSrcMsgId(goodsId, srcMsgId);
        int b = transportMainService.updateSrcMsgId(srcMsgId, srcMsgId);
        return c & b;
    }

    @Override
    public boolean checkPersonalSimilarity(Transport transport, Long srcMsgId, String assignCarTel) {

        Integer checkConfig = tytConfigService.getIntValue(Constant.check_personal_similarity, 0);
        if (checkConfig == null || checkConfig == 0) {
            //不校验个人相似货源
            return false;
        }

        // 开票货源指派不校验重货
        if (transport.getInvoiceTransport() != null && transport.getInvoiceTransport() == 1 && StringUtils.isNotBlank(assignCarTel)) {
            return false;
        }

        boolean checkResult = transportService.checkPersonalSimilarity(transport, srcMsgId);
        if (!checkResult) {
            logger.info("checkPersonalSimilarity_exist : " + transport.getUserId());
            return true;
            //return new ResultMsgBean(ReturnCodeConstant.DATA_HAS_EXIT, "您已经发布过此条信息");
        }

        return false;
    }

    @Override
    public void refreshUserInfo(Transport transport, TytUserSub tytUserSub, ApiDataUserCreditInfoTwo userCreditInfo) {

        Long userId = transport.getUserId();
        if (userId == null) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }

        //用户信用分及等级
        BigDecimal totalScore = BigDecimal.ZERO;
        Integer rankLevel = 1;
        if (null != userCreditInfo) {
            if (userCreditInfo.getTotalScore() != null) {
                totalScore = userCreditInfo.getTotalScore();
            }
            if (userCreditInfo.getRankLevel() != null) {
                rankLevel = userCreditInfo.getRankLevel();
            }
        }
        transport.setTotalScore(totalScore);
        transport.setRankLevel(rankLevel);

        //用户成交单数
        Integer tradeNum = 0;
        if (tytUserSub != null) {
            Integer userDealNum = tytUserSub.getDealNum();
            if (userDealNum != null) {
                tradeNum = userDealNum;
            }
        }
        transport.setTradeNum(tradeNum);

        //用户授权昵称
        String authName = null;

        Example oaExa = new Example(TytOwnerAuth.class);
        oaExa.and().andEqualTo("userId", userId)
                .andEqualTo("status", 2);
        TytOwnerAuth tytOwnerAuth = tytOwnerAuthMapper.selectOneByExample(oaExa);

        if (tytOwnerAuth != null) {
            String authNameTmp = tytOwnerAuth.getAuthName();

            if (org.apache.commons.lang3.StringUtils.isNotBlank(authNameTmp)) {
                authName = authNameTmp;
            }
        }
        transport.setAuthName(authName);

    }

    @Override
    public TransportLabelJson getTransportLabelJson(TransportMain transportMain) {
        if (transportMain == null) {
            return null;
        }
        String labelJsonText = transportMain.getLabelJson();
        TransportLabelJson labelJson = this.getTransportLabelJson(labelJsonText);
        return labelJson;
    }

    public TransportLabelJson getTransportLabelJson(String labelJsonText) {
        TransportLabelJson labelJson = null;
        if (StringUtils.isNotBlank(labelJsonText)) {
            labelJson = JSON.parseObject(labelJsonText, TransportLabelJson.class);
        }
        if (labelJson == null) {
            labelJson = new TransportLabelJson();
        }

        return labelJson;
    }

    /**
     * 专车自动派单
     *
     * @param newTransport
     * @param user
     */
    @Override
    @Async(value = "mqExecutor")
    public void autoAssignOrderForSpecialCar(Transport newTransport, User user) {
        try {
            AssignableCarFilterBean filterBean = getAssignableCarFilterBean(newTransport, user);
            // 路线是否在专车派单配置中
            TytSpecialCarAutoDispatchConfig dispatchConfig = null;
            if (Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), newTransport.getExcellentGoods())) {
                dispatchConfig = tytSpecialCarAutoDispatchConfigMapper
                        .selectDispatchConfigByRoute(filterBean.getStartCity(), filterBean.getDestCity());
            }
            List<TytSigningCarVO> cars = tytBlockConfigService.getAssignableCars(filterBean, dispatchConfig);
            logger.info("专车发货自动派单，货源ID:{}, 发货用户ID:{}, 匹配到的签约车辆:{}", newTransport.getSrcMsgId(),
                    user.getId(), JSONObject.toJSONString(cars));
            // 对满足条件的司机进行排序
            List<TytSigningCarVO> carUsers = sortCars(newTransport, user, filterBean, cars);

            if (Objects.nonNull(dispatchConfig) && Objects.nonNull(dispatchConfig.getMaxDispatchNum()) &&
                    CollectionUtils.isNotEmpty(cars) && Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), filterBean.getExcellentGoods())) {
                carUsers = carUsers.stream().limit(dispatchConfig.getMaxDispatchNum()).collect(Collectors.toList());
            }

            // 发送派单mq
            MqAutoAssignOrderBean assignOrderBean = getMqAutoAssignOrderBean(newTransport, user, carUsers, dispatchConfig);
            logger.info("专车发货自动派单，货源ID:{}, 发货用户ID:{}, mq信息:{}", newTransport.getSrcMsgId(), user.getId(),
                    JSONObject.toJSONString(assignOrderBean));
            tytMqMessageService.addSaveMqMessage(assignOrderBean.getMessageSerailNum(), JSONObject.toJSONString(assignOrderBean), SPECIAL_CAR_ASSIGN);
            tytMqMessageService.sendMqMessageDirect(assignOrderBean.getMessageSerailNum(), JSONObject.toJSONString(assignOrderBean), 0L);
        } catch (Exception e) {
            logger.error("专车发货自动派单异常，货源ID:{}, 发货用户ID:{}", newTransport.getSrcMsgId(), user.getId(), e);
        }
        // 自动将货主添加到专车货主管理
        autoAddCargoOwner(newTransport.getSrcMsgId(), user);
    }

    private static AssignableCarFilterBean getAssignableCarFilterBean(Transport newTransport, User user) {
        AssignableCarFilterBean filterBean = new AssignableCarFilterBean(newTransport.getSrcMsgId(), newTransport.getStartProvinc(),
                newTransport.getStartCity(), newTransport.getStartArea(), newTransport.getDestProvinc(),
                newTransport.getDestCity(), newTransport.getDriverDriving(), newTransport.getGoodTypeName(),
                newTransport.getCargoOwnerId(), newTransport.getDistanceValue(), newTransport.getExcellentGoods(), user.getId(),
                newTransport.getStartLatitude(), newTransport.getStartLongitude());
        return filterBean;
    }

    /**
     * 司机排序
     *
     * @param newTransport
     * @param user
     * @param filterBean
     * @param cars
     * @return
     */
    private List<TytSigningCarVO> sortCars(Transport newTransport, User user, AssignableCarFilterBean filterBean, List<TytSigningCarVO> cars) {
        List<TytSigningCarVO> carUsers = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cars)) {
            // 满足派单条件的司机按优先级排序
            StopWatch watch = new StopWatch();
            watch.start();
            carUsers = tytBlockConfigService.getSigningCarList(cars);
            watch.stop();
            logger.info("专车发货自动派单，货源ID:{}, 发货用户ID:{},满足派单条件司机排序结果：{},耗时:{}", newTransport.getSrcMsgId(),
                    user.getId(), JSONObject.toJSONString(carUsers), watch.getTotalTimeMillis());
        }

        BigDecimal weightDecimal = new BigDecimal(newTransport.getWeight());

        if (Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), filterBean.getExcellentGoods())) {
            // 根据是否车型匹配，重新对司机进行排序，优先指派给车型匹配的司机
            if (weightDecimal.compareTo(new BigDecimal("8")) > 0) {
                //如果货物吨位 <= 8则不走车型匹配
                carUsers = sortByCarTypeMatch(carUsers, newTransport.getWeight());
            } else {
                //不走车型匹配直接按匹配
                carUsers.forEach(v -> v.setCarTypeMatch(1));
            }
        } else {
            // 非专车优先指派给司机标签为全职运力的车辆，再指派给兼职运力，兼职运力的车辆优先指派给车型匹配的车辆
            carUsers = sortByDriverTag(carUsers, newTransport.getWeight());
        }

        // 根据货物吨位过滤车辆类型
        // 如果货物吨位 <= 8 carType必须是清障车
        // 如果货物吨位 > 8 carType必须不是清障车
        if (CollectionUtils.isNotEmpty(carUsers)) {
            if (weightDecimal.compareTo(new BigDecimal("8")) <= 0) {
                // 吨位 <= 8，只保留清障车
                carUsers = carUsers.stream()
                        .filter(v -> CLEARANCE_VEHICLE_TYPE.equals(v.getCarType()))
                        .collect(Collectors.toList());
                logger.info("专车发货自动派单，货源ID:{}, 发货用户ID:{}, 吨位<=8只保留清障车，过滤后车辆数量:{}",
                        newTransport.getSrcMsgId(), user.getId(), carUsers.size());
            } else {
                // 吨位 > 8，排除清障车
                carUsers = carUsers.stream()
                        .filter(v -> !CLEARANCE_VEHICLE_TYPE.equals(v.getCarType()))
                        .collect(Collectors.toList());
                logger.info("专车发货自动派单，货源ID:{}, 发货用户ID:{}, 吨位>8排除清障车，过滤后车辆数量:{}",
                        newTransport.getSrcMsgId(), user.getId(), carUsers.size());
            }
        }

        return carUsers;
    }

    /**
     * 非专车优先指派给司机标签为全职运力的车辆，再指派给兼职运力，兼职运力的车辆优先指派给车型匹配的车辆
     *
     * @param carUsers
     * @param weight
     * @return
     */
    private List<TytSigningCarVO> sortByDriverTag(List<TytSigningCarVO> carUsers, String weight) {
        if (CollectionUtils.isEmpty(carUsers)) {
            return new ArrayList<>();
        }

        // 全职运力
        List<TytSigningCarVO> fullTimeCars = carUsers.stream()
                .filter(v -> Objects.equals(v.getDriverTag(), DriverTagEnum.FULL_TIME.getCode()))
                .sorted(Comparator.comparing(TytSigningCarVO::getRange).reversed())
                .collect(Collectors.toList());

        // 兼职运力
        List<TytSigningCarVO> partTimeCars = carUsers.stream()
                .filter(v -> Objects.equals(v.getDriverTag(), DriverTagEnum.PART_TIME.getCode()))
                .sorted(Comparator.comparing(TytSigningCarVO::getRange).reversed())
                .collect(Collectors.toList());
        List<TytSigningCarVO> sortedPatTimeCars = this.sortByCarTypeMatch(partTimeCars, weight);
        fullTimeCars.addAll(sortedPatTimeCars);

        return fullTimeCars;
    }

    /**
     * 专车货源根据是否车型匹配，重新对司机进行排序，优先指派给车型匹配的司机
     *
     * @param carUsers 满足条件的司机列表
     * @param weight   货物吨重
     * @return
     */
    private List<TytSigningCarVO> sortByCarTypeMatch(List<TytSigningCarVO> carUsers, String weight) {
        if (CollectionUtils.isNotEmpty(carUsers) && StringUtils.isNotBlank(weight)) {
            BigDecimal weightDecimal = new BigDecimal(weight);
            List<String> carTypeList = tytTransportDispatchCarTypeMapper.selectCarTypeListByWeight(weightDecimal);
            if (CollectionUtils.isNotEmpty(carTypeList)) {
                List<TytSigningCarVO> sortedCarUsers = carUsers.stream()
                        .filter(v -> carTypeList.contains(v.getCarType()))
                        .sorted(Comparator.comparing(TytSigningCarVO::getRange).reversed())
                        .collect(Collectors.toList());
                sortedCarUsers.forEach(v -> v.setCarTypeMatch(1));

                List<TytSigningCarVO> carTypeNotMatchList = carUsers.stream()
                        .filter(v -> !carTypeList.contains(v.getCarType()))
                        .sorted(Comparator.comparing(TytSigningCarVO::getRange).reversed())
                        .collect(Collectors.toList());
                carTypeNotMatchList.forEach(v -> v.setCarTypeMatch(0));
                sortedCarUsers.addAll(carTypeNotMatchList);

                return sortedCarUsers;
            }
        }
        return carUsers;
    }

    /**
     * 组装派单mq
     *
     * @param newTransport
     * @param user
     * @param carUsers
     * @param dispatchConfig
     * @return
     */
    private MqAutoAssignOrderBean getMqAutoAssignOrderBean(Transport newTransport,
                                                           User user,
                                                           List<TytSigningCarVO> carUsers,
                                                           TytSpecialCarAutoDispatchConfig dispatchConfig) {
        MqAutoAssignOrderBean assignOrderBean = new MqAutoAssignOrderBean();
        assignOrderBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        assignOrderBean.setMessageType(SPECIAL_CAR_ASSIGN);
        assignOrderBean.setGoodsId(newTransport.getSrcMsgId());
        assignOrderBean.setPublishUserType(PublishUserTypeEnum.NORMAL_USER.getCode());
        assignOrderBean.setDispatchType(1);
        if (Objects.nonNull(dispatchConfig) && Objects.nonNull(dispatchConfig.getDispatchType())) {
            assignOrderBean.setDispatchType(dispatchConfig.getDispatchType());
            assignOrderBean.setAfterMinutes(dispatchConfig.getAfterMinutes());
        }
        // 如果发货人是代调，增加代调信息
        TytDispatchCompany dispatchCompany = tytDispatchCompanyMapper.getByUserId(user.getId());
        if (Objects.nonNull(dispatchCompany)) {
            assignOrderBean.setPublishUserType(PublishUserTypeEnum.DISPATCH.getCode());
            assignOrderBean.setDispatchUserId(user.getId());
            assignOrderBean.setDispatchUserName(user.getUserName());
            assignOrderBean.setDispatchCellPhone(user.getCellPhone());
            assignOrderBean.setDispatchDingTalkPhone(dispatchCompany.getDingTalkPhone());
        }
        List<AssignOrderUser> userList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(carUsers)) {
            // priority数字越小优先级越高
            int priority = 1;
            for (TytSigningCarVO vo : carUsers) {
                if (Objects.isNull(vo.getUserId())) {
                    continue;
                }
                AssignOrderUser orderUser = new AssignOrderUser();
                orderUser.setUserId(vo.getUserId());
                orderUser.setTelephone(vo.getPhone());
                orderUser.setPriority(priority);
                orderUser.setDriverId(vo.getDriverId());
                orderUser.setCarInfoId(vo.getId());
                orderUser.setDistance(vo.getDistance());
                orderUser.setLocation(vo.getLocation());
                orderUser.setCarTypeMatch(vo.getCarTypeMatch());
                orderUser.setDriverTag(vo.getDriverTag());
                orderUser.setCarId(getTytCarIdBySigningCarData(vo));

                if (userList.contains(orderUser)) {
                    continue;
                }
                userList.add(orderUser);
                priority = priority + 1;
            }
        }
        assignOrderBean.setUserList(userList);
        return assignOrderBean;
    }

    private Long getTytCarIdBySigningCarData(TytSigningCarVO vo) {
        if (vo.getUserId() != null && StringUtils.isNotBlank(vo.getHeadCityNo()) && vo.getHeadCityNo().length() > 2 && StringUtils.isNotBlank(vo.getTailCityNo()) && vo.getTailCityNo().length() > 2) {
            logger.info("专车发货自动派单 根据车主和车辆信息获取平台车辆表ID，用户ID:{}, 车头车牌号:{}, 挂车车牌号:{}", vo.getUserId(), vo.getHeadCityNo(), vo.getTailCityNo());
            return tytCarMapper.selectCarIdByUserIdAndHeadCityNo(vo.getUserId(), vo.getHeadCityNo().substring(0, 1), vo.getHeadCityNo().substring(1), vo.getTailCityNo().substring(0, 1), vo.getTailCityNo().substring(1));
        }
        return null;
    }

    /**
     * 专车发货自动将货主添加到专车货主管理
     *
     * @param srcMsgId
     * @param user
     */
    public void autoAddCargoOwner(Long srcMsgId, User user) {
        try {
            TytDispatchCargoOwner cargoOwner = tytDispatchCargoOwnerMapper.getByUserId(user.getId());
            if (Objects.nonNull(cargoOwner)) {
                logger.info("专车发货自动派单添加专车货主，货主已存在不需要添加，货源ID:{}，用户ID:{}", srcMsgId, user.getId());
                return;
            }
            // 是否企业认证
            Integer ownerType = CargoOwnerTypeEnum.PERSONAL.getCode();
            String cargoOwnerName = user.getUserName();
            TytInvoiceEnterprise invoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserId(user.getId(),
                    InfoVerifyStatusEnum.AUDIT_PASSED.getCode());
            if (Objects.nonNull(invoiceEnterprise)) {
                ownerType = CargoOwnerTypeEnum.ENTERPRISE.getCode();
                cargoOwnerName = invoiceEnterprise.getEnterpriseName();
            } else {
                TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getByUserId(String.valueOf(user.getId()));
                if (Objects.nonNull(userIdentityAuth) &&
                        Objects.equals(EnterpriseAuthStatusEnum.success.getCode(), userIdentityAuth.getIdentityStatus())) {
                    cargoOwnerName = userIdentityAuth.getTrueName();
                }
            }
            TytDispatchCargoOwner newCargoOwner = new TytDispatchCargoOwner();
            newCargoOwner.setUserId(user.getId());
            newCargoOwner.setCellPhone(user.getCellPhone());
            newCargoOwner.setOwnerName(cargoOwnerName);
            newCargoOwner.setSignPartner(SignPartnerEnum.NO.getCode());
            newCargoOwner.setAssignCar(AssignCarEnum.NO.getCode());
            newCargoOwner.setOwnerType(ownerType);
            newCargoOwner.setEmpowerStatus(CargoOwenrEmpowerStatusEnum.UNAUTHORIZED.getCode());
            newCargoOwner.setOperator("系统");
            newCargoOwner.setCreateTime(new Date());
            newCargoOwner.setModifyTime(new Date());
            tytDispatchCargoOwnerMapper.insertSelective(newCargoOwner);
        } catch (Exception e) {
            logger.error("专车发货自动派单添加专车货主异常，货源ID:{}, 用户ID:{}", srcMsgId, user.getId(), e);
        }
    }

    @SneakyThrows
    @Override
    public TransportLabelJson refreshLabelJson(Long srcMsgId, TransportLabelJson oldLabelJson, Transport transport,
                                               ApiDataUserCreditInfoTwo userCreditInfo, boolean refreshDuplicate, TransportPublishBean publishBean) {
        if (oldLabelJson == null) {
            oldLabelJson = new TransportLabelJson();
        }
        Date startDate = DateUtil.startOfDay(new Date());
        Long userId = transport.getUserId();

        //投诉少字段
        TransportLabelJson labelJson = new TransportLabelJson();
        //将其他字段初始化
        BeanUtils.copyProperties(oldLabelJson, labelJson);

        String goodService = null;
        BigDecimal complainRate = null;

        if (userCreditInfo != null) {
            labelJson.setUserLabelIcon(userCreditInfo.getUserLabelIcon());
            goodService = userCreditInfo.getUserLabelText();
            // 数据部门返回“无标签”时，客户端展示的时无标签。临时处理
            if ("无标签".equals(goodService)) {
                goodService = "";
            }
            complainRate = userCreditInfo.getComplainRate() == null ? BigDecimal.ZERO : userCreditInfo.getComplainRate();
        }

        Integer guaranteeGoods = transport.getGuaranteeGoods();
        Integer rankLevel = transport.getRankLevel();

        if (guaranteeGoods != null && guaranteeGoods == 1 && rankLevel != null && rankLevel == 5) {
            if (complainRate != null && complainRate.compareTo(complainLimit) < 0) {
                goodService = StringUtils.join(goodService, " ", goodServiceText).trim();
            }
        }
        labelJson.setGoodService(goodService);

        // 评价标签
        UserFeedbackRatingAndLabelDTO userFeedbackRatingAndLabel =
                feedbackUserService.getUserFeedbackRatingAndLabel(userId, 2);
        if (userFeedbackRatingAndLabel != null) {
            // 30天保护期
            User user = userService.getByUserId(transport.getUserId());
            if (user != null && DateUtils.addDays(user.getCtime() == null ? new Date() : user.getCtime(), 30).before(new Date())) {
                if (userFeedbackRatingAndLabel.getTotal() >= 3) {
                    userFeedbackRatingAndLabel.setRating("好评率: " + userFeedbackRatingAndLabel.getRating() + "%");
                } else if (userFeedbackRatingAndLabel.getTotal() > 0) {
                    userFeedbackRatingAndLabel.setRating("评价少于3");
                } else {
                    userFeedbackRatingAndLabel.setRating(null);
                }
            } else {
                userFeedbackRatingAndLabel.setRating(null);
                userFeedbackRatingAndLabel.setNegativeLabels(Collections.emptyList());
            }

            labelJson.setFeedbackLabel(userFeedbackRatingAndLabel);
        }

        //用户认证类型
        TytUserIdentityAuth tytUserIdentityAuth = tytUserIdentityAuthService.getTytUserIdentityAuth(userId);

        if (tytUserIdentityAuth != null) {
            Integer identityStatus = tytUserIdentityAuth.getIdentityStatus();
            Integer enterpriseAuthStatus = tytUserIdentityAuth.getEnterpriseAuthStatus();

            labelJson.setUserAuthStatus(identityStatus);
            labelJson.setEnterpriseAuthStatus(enterpriseAuthStatus);

        }

        // 判断是否是秒抢货源
        labelJson.setInstantGrab(null);
        if (tytGoodsRefreshUserService.isCheckInstantGrab(transport)) {
            GoodModelResult goodModelResult = checkInstantGrab(transport);
            labelJson.setIGBIResultData(goodModelResult.getLevel());
            labelJson.setGoodsModelScore(goodModelResult.getScore());
            // 6470 需求：优货也走好货模型，但不打好货的标识 2024.05.27
            // 6640 工单：普货不再自动转好货 2024.06.01 云效ID: SSRS-2350
            /*if ((transport.getExcellentGoods() == null || transport.getExcellentGoods() == 0)
                    && Objects.equals(labelJson.getIGBIResultData(), 1)) {
                labelJson.setInstantGrab(YesOrNoEnum.Y.getCode());
            }*/
        }

        if (publishBean != null) {
            //编辑发布时刷新标签才会走下面判断
            if (publishBean.getGoodCarPriceTransport() != null && publishBean.getGoodCarPriceTransport() == 1) {
                labelJson.setGoodCarPriceTransport(1);
                transport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
            } else {
                transport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
                labelJson.setGoodCarPriceTransport(null);
            }
        }

        String jsonText = labelJson.getJsonText();
        transport.setLabelJson(jsonText);

        return labelJson;
    }

    public GoodModelResult checkInstantGrab(Transport transport) {
        // bi环境参数: 1：test环境；2：dev环境；3：release环境；不传或者0为生产环境

        String env = tytConfigService.getStringValue("tyt:config:environment", "dev");
        String runType;
        switch (env) {
            case "test":
                runType = "1";
                break;
            case "online":
                runType = "0";
                break;
            case "release":
                runType = "3";
                break;
            case "dev":
            default:
                runType = "2";
                break;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("user_id", transport.getUserId());
        params.put("run_type", runType);
        params.put("price", transport.getPrice());
        params.put("distance", transport.getDistance());
        params.put("weight", transport.getWeight());
        params.put("start_city", transport.getStartCity());
        params.put("dest_city", transport.getDestCity());
        params.put("length", transport.getLength());
        params.put("width", transport.getWide());
        params.put("height", transport.getHigh());
        params.put("info_fee", transport.getInfoFee());
        params.put("refund_flag", transport.getRefundFlag());
        params.put("start_latitude", transport.getStartLatitudeValue());
        params.put("start_longitude", transport.getStartLongitudeValue());
        params.put("source_type", transport.getSourceType());
        params.put("start_detail_add", transport.getStartDetailAdd());
        params.put("excellent_goods", transport.getExcellentGoods());
        params.put("excellent_goods_two", transport.getExcellentGoodsTwo());
        ResultMsgBean resultMsgBean = biRequestService.doPost(RemoteApiConstant.IdcApi.INSTANT_GRAB_PATH, params, true);
        GoodModelResult goodModelResult = new GoodModelResult();
        if (resultMsgBean != null) {
            if (Objects.equals(200, resultMsgBean.getCode())) {
                Object data = resultMsgBean.getData();
                if (data != null) {
                    // 之前返回数字，6510返回对象：{"score":2.5,"level":3}
                    String s = data.toString();
                    if (s.contains("{")) {
                        goodModelResult = JSONUtil.toBean(s, GoodModelResult.class);
                    } else {
                        goodModelResult.setLevel(Integer.parseInt(s));
                    }
                }
            }
        }
        return goodModelResult;
    }

    public BigDecimal checkCommissionScore(Transport transport) {
        // bi环境参数: 1：test环境；2：dev环境；3：release环境；不传或者0为生产环境

        String env = tytConfigService.getStringValue("tyt:config:environment", "dev");
        String runType;
        switch (env) {
            case "test":
                runType = "1";
                break;
            case "online":
                runType = "0";
                break;
            case "release":
                runType = "3";
                break;
            case "dev":
            default:
                runType = "2";
                break;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("user_id", transport.getUserId());
        params.put("run_type", runType);
        params.put("price", transport.getPrice());
        params.put("distance", transport.getDistance());
        params.put("weight", transport.getWeight());
        params.put("start_city", transport.getStartCity());
        params.put("dest_city", transport.getDestCity());
        params.put("length", transport.getLength());
        params.put("width", transport.getWide());
        params.put("height", transport.getHigh());
        params.put("refund_flag", transport.getRefundFlag());
        params.put("info_fee", transport.getInfoFee());
        logger.info("获取抽佣分数调用BI接口 请求参数：{}", JSONObject.toJSONString(params));
        ResultMsgBean resultMsgBean = biRequestService.doPost(RemoteApiConstant.IdcApi.COMMISSION_SCORE_PATH, params, true);
        GoodModelResult goodModelResult = new GoodModelResult();
        if (resultMsgBean != null) {
            if (Objects.equals(200, resultMsgBean.getCode())) {
                Object data = resultMsgBean.getData();
                if (data != null) {
                    // 之前返回数字，6510返回对象：{"score":2.5,"level":3}
                    String s = data.toString();
                    goodModelResult = JSONUtil.toBean(s, GoodModelResult.class);
                }
            }
        }
        if (goodModelResult != null && goodModelResult.getScore() != null) {
            return goodModelResult.getScore();
        }
        return null;
    }

    @Override
    public ResultMsgBean invocieTransportAssignCar(BoPublishTransportBean data, Long userId, String assignCarTel) {
        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse();
        try {
            AssignOrdersSaveBillDTO assignOrdersSaveBillDTO = new AssignOrdersSaveBillDTO();
            assignOrdersSaveBillDTO.setTsId(data.getSrcMsgId());
            assignOrdersSaveBillDTO.setUserId(userId);
            assignOrdersSaveBillDTO.setCellPhone(assignCarTel);
            assignOrdersSaveBillDTO.setAgencyMoney(0L);
            assignOrdersSaveBillDTO.setCarriageFee(Integer.parseInt(data.getPrice()));
            assignOrdersSaveBillDTO.setTecServiceFee(0L);
            assignOrdersSaveBillDTO.setCarOwnerPayType(99);
            logger.info("开票货源指派车方请求参数，货源ID：{}, 货主ID：{}，指派车方手机号：{}，运费：{}", data.getSrcMsgId(), userId, assignCarTel, data.getPrice());
            Response<ResultMsgBean> response = apiTradeInfoFeeClient.invoiceTransportAssignCar(assignOrdersSaveBillDTO).execute();
            logger.info("开票货源指派车方 履约组指派接口返回结果 {}, {}", JSONObject.toJSONString(response), JSONObject.toJSONString(response.body()));
            if (!response.isSuccessful() || response.body() == null || response.body().getCode() == null || response.body().getCode() != 0) {
                logger.info("开票货源指派车方失败");
                String backoutReasonKey = "货主已取消货源";
                Integer backoutReasonValue = 7;
                this.saveInfoFeeUpdateBtnStatusNew(userId, 1, data.getSrcMsgId(), new TransportDoneRequest(), backoutReasonKey, backoutReasonValue, null, null, true, null, null);
                //调用履约开票货源指派车方接口，如果失败则调用货源撤销方法撤销掉货源并返回履约接口给的错误msg信息
                resultMsgBean = ResultMsgBean.failResponse(1099888, "指派订单失败，货源已撤销，您可在货源列表中点击编辑再发布重新发布");
            }
        } catch (Exception e) {
            logger.info("开票货源指派车方失败后，主动下架货源操作失败");
            resultMsgBean = ResultMsgBean.failResponse(1099888, "指派订单失败，货源已撤销，您可在货源列表中点击编辑再发布重新发布");
        }
        return resultMsgBean;
    }

    @Override
    public void saveSomeBINeedDataResult(Long srcMsgId, TransportBIDataJson transportBIDataJsonParam) {

        logger.info("保存BI需要的数据结果，srcMsgId:{}, transportBIDataJsonParam:{}", srcMsgId, JSONObject.toJSONString(transportBIDataJsonParam));
        String cacheInstantGrabBIResultString = null;
        try {
            if (srcMsgId == null || transportBIDataJsonParam == null) {
                return;
            }
            String instantGrabResendOverPushcacheKey = CommonUtil.joinRedisKey("tyt:cache:bi:data", srcMsgId.toString(), DateUtil.dateToString(new Date(), DateUtil.day_format_short));
            cacheInstantGrabBIResultString = RedisUtil.get(instantGrabResendOverPushcacheKey);
            if (StringUtils.isBlank(cacheInstantGrabBIResultString)) {
                TransportBIDataJson transportBIDataJson = new TransportBIDataJson();
                if (transportBIDataJsonParam.getIGBIResultData() != null) {
                    transportBIDataJson.setIGBIResultData(transportBIDataJsonParam.getIGBIResultData());
                }
                if (transportBIDataJsonParam.getPublishTransportIsShowGoodCar() != null) {
                    transportBIDataJson.setPublishTransportIsShowGoodCar(transportBIDataJsonParam.getPublishTransportIsShowGoodCar());
                }
                if (transportBIDataJsonParam.getStartAddrSource() != null) {
                    transportBIDataJson.setStartAddrSource(transportBIDataJsonParam.getStartAddrSource());
                }
                if (transportBIDataJsonParam.getDestAddrSource() != null) {
                    transportBIDataJson.setDestAddrSource(transportBIDataJsonParam.getDestAddrSource());
                }
                if (transportBIDataJsonParam.getThMinPrice() != null) {
                    transportBIDataJson.setThMinPrice(transportBIDataJsonParam.getThMinPrice());
                }
                if (transportBIDataJsonParam.getThMaxPrice() != null) {
                    transportBIDataJson.setThMaxPrice(transportBIDataJsonParam.getThMaxPrice());
                }
                if (transportBIDataJsonParam.getSuggestPrice() != null) {
                    transportBIDataJson.setSuggestPrice(transportBIDataJsonParam.getSuggestPrice());
                }
                if (transportBIDataJsonParam.getSuggestMinPrice() != null) {
                    transportBIDataJson.setSuggestMinPrice(transportBIDataJsonParam.getSuggestMinPrice());
                }
                if (transportBIDataJsonParam.getSuggestMaxPrice() != null) {
                    transportBIDataJson.setSuggestMaxPrice(transportBIDataJsonParam.getSuggestMaxPrice());
                }
                if (transportBIDataJsonParam.getFixPriceMin() != null) {
                    transportBIDataJson.setFixPriceMin(transportBIDataJsonParam.getFixPriceMin());
                }
                if (transportBIDataJsonParam.getFixPriceMax() != null) {
                    transportBIDataJson.setFixPriceMax(transportBIDataJsonParam.getFixPriceMax());
                }
                if (transportBIDataJsonParam.getFixPriceFast() != null) {
                    transportBIDataJson.setFixPriceFast(transportBIDataJsonParam.getFixPriceFast());
                }
                if (transportBIDataJsonParam.getClientType() != null) {
                    transportBIDataJson.setClientType(transportBIDataJsonParam.getClientType());
                }
                if (transportBIDataJsonParam.getShowGoodCarPriceTransportTab() != null) {
                    transportBIDataJson.setShowGoodCarPriceTransportTab(transportBIDataJsonParam.getShowGoodCarPriceTransportTab());
                }
                if (transportBIDataJsonParam.getAutomaticGoodCarPriceTransportType() != null) {
                    transportBIDataJson.setAutomaticGoodCarPriceTransportType(transportBIDataJsonParam.getAutomaticGoodCarPriceTransportType());
                }
                if (transportBIDataJsonParam.getGoodCarPriceTransport() != null && transportBIDataJsonParam.getGoodCarPriceTransport() == 1) {
                    transportBIDataJson.setGoodCarPriceTransport(1);
                }
                if (Objects.nonNull(transportBIDataJsonParam.getDistanceKilometer())) {
                    transportBIDataJson.setDistanceKilometer(transportBIDataJsonParam.getDistanceKilometer());
                }
                if (Objects.nonNull(transportBIDataJsonParam.getOtherFee())) {
                    transportBIDataJson.setOtherFee(transportBIDataJsonParam.getOtherFee());
                }
                if (Objects.nonNull(transportBIDataJsonParam.getCommissionTransport()) && transportBIDataJsonParam.getCommissionTransport() != -1) {
                    transportBIDataJson.setCommissionTransport(transportBIDataJsonParam.getCommissionTransport());
                }
                if (Objects.nonNull(transportBIDataJsonParam.getMeetCommissionRules())) {
                    transportBIDataJson.setMeetCommissionRules(transportBIDataJsonParam.getMeetCommissionRules());
                }
                if (Objects.nonNull(transportBIDataJsonParam.getPriorityRecommend())) {
                    transportBIDataJson.setPriorityRecommend(transportBIDataJsonParam.getPriorityRecommend());
                }
                if (Objects.nonNull(transportBIDataJsonParam.getGoodsModelScore())) {
                    transportBIDataJson.setGoodsModelScore(transportBIDataJsonParam.getGoodsModelScore());
                }
                if (Objects.nonNull(transportBIDataJsonParam.getCommissionLabel())) {
                    transportBIDataJson.setCommissionLabel(transportBIDataJsonParam.getCommissionLabel());
                }
                String jsonText = transportBIDataJson.getJsonText();
                if (StringUtils.isNotBlank(jsonText)) {
                    RedisUtil.set(instantGrabResendOverPushcacheKey, jsonText, 60 * 60 * 30);
                }
            } else {
                TransportBIDataJson transportBIDataJson = JSONObject.parseObject(cacheInstantGrabBIResultString, TransportBIDataJson.class);
                if (transportBIDataJson != null) {
                    if (transportBIDataJsonParam.getIGBIResultData() != null) {
                        transportBIDataJson.setIGBIResultData(transportBIDataJsonParam.getIGBIResultData());
                    }
                    if (transportBIDataJsonParam.getPublishTransportIsShowGoodCar() != null) {
                        transportBIDataJson.setPublishTransportIsShowGoodCar(transportBIDataJsonParam.getPublishTransportIsShowGoodCar());
                    }
                    if (transportBIDataJsonParam.getStartAddrSource() != null) {
                        transportBIDataJson.setStartAddrSource(transportBIDataJsonParam.getStartAddrSource());
                    }
                    if (transportBIDataJsonParam.getDestAddrSource() != null) {
                        transportBIDataJson.setDestAddrSource(transportBIDataJsonParam.getDestAddrSource());
                    }
                    if (transportBIDataJsonParam.getThMinPrice() != null) {
                        transportBIDataJson.setThMinPrice(transportBIDataJsonParam.getThMinPrice());
                    }
                    if (transportBIDataJsonParam.getThMaxPrice() != null) {
                        transportBIDataJson.setThMaxPrice(transportBIDataJsonParam.getThMaxPrice());
                    }
                    if (transportBIDataJsonParam.getSuggestPrice() != null) {
                        transportBIDataJson.setSuggestPrice(transportBIDataJsonParam.getSuggestPrice());
                    }
                    if (transportBIDataJsonParam.getSuggestMinPrice() != null) {
                        transportBIDataJson.setSuggestMinPrice(transportBIDataJsonParam.getSuggestMinPrice());
                    }
                    if (transportBIDataJsonParam.getSuggestMaxPrice() != null) {
                        transportBIDataJson.setSuggestMaxPrice(transportBIDataJsonParam.getSuggestMaxPrice());
                    }
                    if (transportBIDataJsonParam.getFixPriceMin() != null) {
                        transportBIDataJson.setFixPriceMin(transportBIDataJsonParam.getFixPriceMin());
                    }
                    if (transportBIDataJsonParam.getFixPriceMax() != null) {
                        transportBIDataJson.setFixPriceMax(transportBIDataJsonParam.getFixPriceMax());
                    }
                    if (transportBIDataJsonParam.getFixPriceFast() != null) {
                        transportBIDataJson.setFixPriceFast(transportBIDataJsonParam.getFixPriceFast());
                    }
                    if (transportBIDataJsonParam.getClientType() != null) {
                        transportBIDataJson.setClientType(transportBIDataJsonParam.getClientType());
                    }
                    if (transportBIDataJsonParam.getShowGoodCarPriceTransportTab() != null) {
                        transportBIDataJson.setShowGoodCarPriceTransportTab(transportBIDataJsonParam.getShowGoodCarPriceTransportTab());
                    }
                    if (transportBIDataJsonParam.getAutomaticGoodCarPriceTransportType() != null) {
                        transportBIDataJson.setAutomaticGoodCarPriceTransportType(transportBIDataJsonParam.getAutomaticGoodCarPriceTransportType());
                    }
                    if (transportBIDataJsonParam.getGoodCarPriceTransport() != null && transportBIDataJsonParam.getGoodCarPriceTransport() == 1) {
                        transportBIDataJson.setGoodCarPriceTransport(1);
                    }
                    if (Objects.nonNull(transportBIDataJsonParam.getDistanceKilometer())) {
                        transportBIDataJson.setDistanceKilometer(transportBIDataJsonParam.getDistanceKilometer());
                    }
                    if (Objects.nonNull(transportBIDataJsonParam.getOtherFee())) {
                        transportBIDataJson.setOtherFee(transportBIDataJsonParam.getOtherFee());
                    }
                    if (Objects.nonNull(transportBIDataJsonParam.getCommissionTransport()) && transportBIDataJsonParam.getCommissionTransport() != -1) {
                        transportBIDataJson.setCommissionTransport(transportBIDataJsonParam.getCommissionTransport());
                    }
                    if (Objects.nonNull(transportBIDataJsonParam.getMeetCommissionRules())) {
                        transportBIDataJson.setMeetCommissionRules(transportBIDataJsonParam.getMeetCommissionRules());
                    }
                    if (Objects.nonNull(transportBIDataJsonParam.getPriorityRecommend())) {
                        transportBIDataJson.setPriorityRecommend(transportBIDataJsonParam.getPriorityRecommend());
                    }
                    if (Objects.nonNull(transportBIDataJsonParam.getGoodsModelScore())) {
                        transportBIDataJson.setGoodsModelScore(transportBIDataJsonParam.getGoodsModelScore());
                    }
                    if (Objects.nonNull(transportBIDataJsonParam.getCommissionLabel())) {
                        transportBIDataJson.setCommissionLabel(transportBIDataJsonParam.getCommissionLabel());
                    }
                    String jsonText = transportBIDataJson.getJsonText();
                    RedisUtil.set(instantGrabResendOverPushcacheKey, jsonText, 60 * 60 * 60);
                }
            }

            //如果是优车定价货源,则立刻往五级地址表中写数据
            // 如果是专车货源，立刻往五级地址表写数据
            if ((transportBIDataJsonParam.getGoodCarPriceTransport() != null && transportBIDataJsonParam.getGoodCarPriceTransport() == 1) ||
                    ExcellentGoodsEnums.SPECIAL.getCode().equals(transportBIDataJsonParam.getExcellentGoods())) {
                Long id = tytUserRecordMapper.selectGoodsAddressLevelRecordIdBySrcMsgId(srcMsgId);
                if (id == null) {
                    tytUserRecordMapper.insertIntoGoodsAddressLevelRecordGoodCarPriceTransportIsTrue(srcMsgId,
                            transportBIDataJsonParam.getDistanceKilometer(), transportBIDataJsonParam.getOtherFee());
                } else {
                    tytUserRecordMapper.updateGoodsAddressLevelRecordGoodCarPriceTransportIsTrueBySrcMsgId(srcMsgId,
                            transportBIDataJsonParam.getDistanceKilometer(), transportBIDataJsonParam.getOtherFee());
                }
            }

        } catch (Exception e) {
            logger.error("存储调用BI优车好货结果异常 货源ID {}, param{}, cache data {} ", srcMsgId, transportBIDataJsonParam.getJsonText(), cacheInstantGrabBIResultString, e);
        }
    }

    @Override
    public void saveFindTransportLogBIData(TransportCarFindBIDataJson transportCarFindBIDataJson) {
        //五级地址子表存储车找货相关埋点数据
        tytUserRecordMapper.saveFindTransportLogBIData(transportCarFindBIDataJson);
    }

    @Override
    public void checkPersonalDuplicate(Integer confirm, Transport transport, TransportLabelJson oldLabelJson,
                                       TransportLabelJson newLabelJson) {

        if (confirm != null && confirm == 1) {
            //已确认，无需处理
            logger.info("confirm_send_duplicate");
            return;
        }
        /*
        if(oldLabelJson != null && oldLabelJson.isDuplicate()) {
            //原来是重复也无需判断
            logger.info("old_duplicate_skip ... ");
            return;
        }
        */

        if (newLabelJson != null && newLabelJson.isDuplicate()) {
            logger.info("personal_duplicate_alert !");
            throw TytException.createException(PlatResponseEnum.transport_duplicate.info());
        }

    }

    @Override
    public ResultMsgBean saveGoodsDirectV5930(SaveDirectReq saveDirectReq, boolean topFlag,
                                              boolean saveVary, boolean refreshDuplicate, Long excellCardId) throws Exception {
        Long userId = saveDirectReq.getUserId();
        String clientVersion = saveDirectReq.getClientVersion();
        Integer clientSign = Integer.parseInt(saveDirectReq.getClientSign());

        Long goodsId = saveDirectReq.getGoodsId();
        Integer isBackendTransport = saveDirectReq.getIsBackendTransport();
        Integer publishType = saveDirectReq.getPublishType();
        String price = saveDirectReq.getPrice();
        Integer confirm = saveDirectReq.getConfirm();
        SaveDirectOptEnum directOptEnum = saveDirectReq.getDirectOptEnum();
        Boolean useExposure = saveDirectReq.getUseExposure();

        //增加发货限制验证
        ResultMsgBean msgBean = bsPublishTransportService.validationLimitPublish(userId);
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            //TODO 6340版本 兼容 低版本PC提示 强升后可删除
            if (StringUtils.isNotEmpty(saveDirectReq.getClientSign()) && saveDirectReq.getClientSign().equals("1") && StringUtils.isNotEmpty(saveDirectReq.getClientVersion()) && Integer.parseInt(saveDirectReq.getClientVersion()) < 566340) {
                msgBean.setCode(2023);
                msgBean.setMsg(msgBean.getNoticeData().getMasterContent());
            }
            return msgBean;
        }

        // 查询该信息是否存在
        Transport originalTransport = this.getByGoodsId(goodsId);

        if (originalTransport == null) {
            logger.error("直接发布-信息信息ID【{}】不存在！", goodsId);
            return new ResultMsgBean(ReturnCodeConstant.OK, "");
        }

        // 专车发货，校验用户是否有专车入口权限
        // if (ExcellentGoodsEnums.SPECIAL.getCode().equals(originalTransport.getExcellentGoods())) {
        //     ResultMsgBean showSpecial = bsPublishTransportService.isShowSpecialV2(saveDirectReq.getUserId());
        //     JSONObject showJson = JSON.parseObject(showSpecial.getData().toString());
        //     if (Objects.equals(showJson.getBoolean("show"), false)) {
        //         return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "当前账号不支持发布专车货源！");
        //     }
        // }

        if (StringUtils.isNotBlank(price)) {
            originalTransport.setPrice(price);
        }

        Integer CheckGoodCarPriceTransportPublishType = originalTransport.getPublishType();
        String CheckGoodCarPriceTransportPrice = originalTransport.getPrice();

        if (saveDirectReq.getAutomaticGoodCarPriceTransportType() != null) {
            if (saveDirectReq.getAutomaticGoodCarPriceTransportType() == 3) {
                //填价或加价
                if (directOptEnum != null && SaveDirectOptEnum.fillPrice.equals(directOptEnum)) {
                    //填价，并且符合自动转一口价条件,如果是填价但是不符合转一口价条件，这里可能是null
                    CheckGoodCarPriceTransportPublishType = publishType;
                }
                CheckGoodCarPriceTransportPrice = saveDirectReq.getPrice();
            } else if (saveDirectReq.getAutomaticGoodCarPriceTransportType() == 4 && publishType != null && PublishTypeEnum.fixed.getCode().equals(publishType)) {
                //转一口价
                CheckGoodCarPriceTransportPublishType = 2;
                if (StringUtils.isNotBlank(saveDirectReq.getPrice())) {
                    CheckGoodCarPriceTransportPrice = saveDirectReq.getPrice();
                }
            }

        }

        //非优车定价一口价货源符合条件的直接转优车定价货源
        TransportLabelJson oldLabelJson = this.getTransportLabelJson(originalTransport.getLabelJson());
//        if (!Objects.equals(originalTransport.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())) {
//            boolean isChangeDest = Objects.equals(saveDirectReq.getIsChangeDestAddress(), true);
//            String destProvince = isChangeDest ? saveDirectReq.getUpdateDataReq().getDestProvinc() : originalTransport.getDestProvinc();
//            String destCity = isChangeDest ? saveDirectReq.getUpdateDataReq().getDestCity() : originalTransport.getDestCity();
//            String destArea = isChangeDest ? saveDirectReq.getUpdateDataReq().getDestArea() : originalTransport.getDestArea();
//            String distance = isChangeDest ? saveDirectReq.getUpdateDataReq().getDistance() : originalTransport.getDistance();
//
//            CheckGoodCarPriceTransportBean priceTransportBean = new CheckGoodCarPriceTransportBean(originalTransport.getStartProvinc(), originalTransport.getStartCity(), originalTransport.getStartArea()
//                    , destProvince, destCity, destArea
//                    , originalTransport.getTaskContent(), originalTransport.getWeight(), originalTransport.getLength(), originalTransport.getWide(), originalTransport.getHigh()
//                    , distance.length() > 2 ? distance.substring(0, distance.length() - 2).replace(".", "") : distance.replace(".", "")
//                    , originalTransport.getExcellentGoods(), originalTransport.getUserId(), CheckGoodCarPriceTransportPublishType, CheckGoodCarPriceTransportPrice, originalTransport.getGoodTypeName());
//            if (bsPublishTransportService.checkTransportIsGoodCarPriceTransport(priceTransportBean)) {
//                originalTransport.setExcellentGoods(1);
//                saveDirectReq.setGoodCarPriceTransport(1);
//                saveDirectReq.setAutomaticGoodCarPriceTransport(true);
//                //下面刷新lable json字段是会判断优车定价字段是否为1
//            }
//        }

//        if (Objects.equals(saveDirectReq.getGoodCarPriceTransport(), 1)) {
//            originalTransport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
//            originalTransport.setExcellentGoods(ExcellentGoodsEnum.YES_GOODS.getCode());
//        } else {
//            originalTransport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
//        }

        // 新老发货类型兼容，设置新发货类型
        TransportPublishBean publishBean = new TransportPublishBean();
        publishBean.setPrice(StringUtils.isBlank(price) ? originalTransport.getPrice() : price);
        this.getExcellentGoodsType(originalTransport, publishBean);
        if(originalTransport.getExcellentGoodsTwo() != null && Objects.equals(originalTransport.getExcellentGoodsTwo(), 2)){
            saveDirectReq.setGoodCarPriceTransport(1);
        }

        if (originalTransport.getInvoiceTransport() != null && originalTransport.getInvoiceTransport() == 1) {
            TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(originalTransport.getSrcMsgId());
            if (transportEnterpriseLog == null) {
                transportEnterpriseLog = new TytTransportEnterpriseLog();
            }
            //开票货源货主资格校验
            ResultMsgBean resultMsgBean = invoiceTransportService.checkUserCanPublishInvoiceTransportInDoSaveDirect(userId, transportEnterpriseLog.getInvoiceSubjectId());
            if (resultMsgBean.getCode() != 200) {
                return resultMsgBean;
            }

            //开票货源限制校验
            // 如果目的地修改，距离取最新距离
            String distance = Objects.equals(saveDirectReq.getIsChangeDestAddress(), true) ?
                    saveDirectReq.getUpdateDataReq().getDistance().toString() : originalTransport.getDistance();
            resultMsgBean = invoiceTransportService.checkInvoiceTransportParam(userId, distance, originalTransport.getPrice(), originalTransport.getWeight());
            if (resultMsgBean.getCode() != 200) {
                return resultMsgBean;
            }

            String priceUseAdditionalPrice = price;
            if (StringUtils.isBlank(priceUseAdditionalPrice)) {
                priceUseAdditionalPrice = originalTransport.getPrice();
            }

            //直接发布重新计算附加运费
            logger.info("重新计算附加运费 {}, {}, {}, {}", originalTransport.getSrcMsgId(), priceUseAdditionalPrice, originalTransport.getUserId(), transportEnterpriseLog.getInvoiceSubjectId());
            ResultMsgBean additionalPriceAndEnterpriseTaxRate = invoiceTransportService.getAdditionalPriceAndEnterpriseTaxRate(originalTransport.getUserId(), priceUseAdditionalPrice, transportEnterpriseLog.getInvoiceSubjectId());
            if (additionalPriceAndEnterpriseTaxRate.getCode() == 200) {
                HashMap<String, String> additionalPriceAndEnterpriseTaxRateResult = (HashMap<String, String>) additionalPriceAndEnterpriseTaxRate.getData();
                originalTransport.setEnterpriseTaxRate(new BigDecimal(additionalPriceAndEnterpriseTaxRateResult.get("enterpriseTaxRate")));
                originalTransport.setAdditionalPrice(additionalPriceAndEnterpriseTaxRateResult.get("additionalPrice"));
            }
        }

        checkYmmForDirectPublish(originalTransport, originalTransport.getSrcMsgId(), originalTransport.getUploadCellPhone());

        if (saveDirectReq.getGoodCarPriceTransport() != null && saveDirectReq.getGoodCarPriceTransport() == 1) {
            //如果直接发布参数想要将货源变成优车定价，则需刷新LabelJson字段
            oldLabelJson.setGoodCarPriceTransport(1);
        }

        Long todaySrcMsgId = originalTransport.getSrcMsgId();
        // 判读是不是昨天的数据
        boolean isHistoryGoods = originalTransport.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime();
        if (isHistoryGoods) {
            // 是否可以正常发货验证
            ResultMsgBean checkMsgBean = userService.checkUserPhotoVerifyFlag(userId, clientSign.toString());
            if (checkMsgBean.getCode() != 200) {
                return checkMsgBean;
            }
            todaySrcMsgId = null;
            if (oldLabelJson != null) {
                oldLabelJson.setDuplicateFlag(null);
            }
            //清空加价次数
            oldLabelJson.setAddPriceCount(null);

            originalTransport.setSortType(TsSortTypeEnum.top.getCode());
        }

        if (SaveDirectOptEnum.addPrice == directOptEnum) {
            //加价操作时
            oldLabelJson.setAddPriceCount(saveDirectReq.getAddPriceCount());
        }

        User user = userService.getByUserId(userId);
        TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(userId);

        //验证装卸货时间是否到期
        BackendTransportBean backendTransportBean = null;
        if (isBackendTransport != null && isBackendTransport == 1) {
            backendTransportBean = backendTransportMapper.selectBackendStatusByMsgId(originalTransport.getSrcMsgId());
            if (backendTransportBean != null && ObjectUtil.equal(backendTransportBean.getStatus(), 2)) {
                return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "该货源货主已经取消，不能进行发布");
            }
        }


        Long originalSrcMsgId = originalTransport.getSrcMsgId();


        String recommendHashCode = StringUtil.getHashCodeNewByRecommend(originalTransport);

        // 其他待支付的改成对用户不可见
        if (originalTransport.getIsInfoFee().equals("1")) {
            transportOrdersService.saveChangeWaitToDisappear(userId, originalTransport.getTsOrderNo());
        }
        // 添加一条新信息
        Transport transport = new Transport();
        BeanUtils.copyProperties(originalTransport, transport);
        //modify by tianjw on 20180529 重新发货更新用户的数据信息
        if (excellCardId != null) {
            transport.setExcellentCardId(excellCardId);
        }

        if (saveDirectReq.getUpdateDataReq() != null) {
            TransportUpdateDataReq updateDataReq = saveDirectReq.getUpdateDataReq();
            // 更新长宽高
            if (saveDirectReq.getUpdateDataReq().getLength() != null) {
                transport.setLength(updateDataReq.getLength());
            }
            if (saveDirectReq.getUpdateDataReq().getWide() != null) {
                transport.setWide(updateDataReq.getWide());
            }
            if (saveDirectReq.getUpdateDataReq().getHigh() != null) {
                transport.setHigh(updateDataReq.getHigh());
            }
            // 更新目的地
            if (Objects.equals(saveDirectReq.getIsChangeDestAddress(), true)) {
                transport.setDestPoint(updateDataReq.getDestPoint());
                transport.setDestProvinc(updateDataReq.getDestProvinc());
                transport.setDestCity(updateDataReq.getDestCity());
                transport.setDestArea(updateDataReq.getDestArea());
                transport.setDestDetailAdd(updateDataReq.getDestDetailAdd());

                transport.setDestLatitudeValue(updateDataReq.getDestLatitude().movePointRight(6).intValue());
                transport.setDestLongitudeValue(updateDataReq.getDestLongitude().movePointRight(6).intValue());
                transport.setDestCoord(updateDataReq.getDestCoordX() + "," + updateDataReq.getDestCoordY());
                transport.setDestCoordXValue(updateDataReq.getDestCoordX().movePointRight(2).intValue());
                transport.setDestCoordYValue(updateDataReq.getDestCoordY().movePointRight(2).intValue());

                try {
                    if (updateDataReq.getDistance() != null) {
                        int distance = new BigDecimal(updateDataReq.getDistance()).intValue();
                        transport.setDistanceValue(distance);

                        if (Objects.equals(clientSign, Constant.ClientSignEnum.ANDROID_CAR.code) || Objects.equals(clientSign, Constant.ClientSignEnum.ANDROID_GOODS.code)) {
                            transport.setAndroidDistance(distance);
                        }
                        if (Objects.equals(clientSign, Constant.ClientSignEnum.IOS_CAR.code) || Objects.equals(clientSign, Constant.ClientSignEnum.IOS_GOODS.code)) {
                            transport.setIosDistance(distance);
                        }
                    }
                } catch (Exception e) {
                    logger.info("distance转换异常", e);
                }
            }
        }

        originalTransport.setSort(0L);

        if (user != null && transport.getSource() == Transport.SOURCE_MANUAL) {
            transport.setVerifyFlag(user.getVerifyFlag().intValue() == 2 ? 0 : user.getVerifyFlag());
        } else {
            transport.setVerifyFlag(0);
        }
        /* 兼容老客户 */
        makeNickName(transport, user);

        transport.setPubQQ(user.getQq());
        transport.setUploadCellPhone(user.getCellPhone());
        /* 添加用户信息 */
        transport.setIsCar(user.getIsCar());
        int vipType = 0;
        if (user != null && user.getUserType() == User.USER_TYPE_VIP && user.getServeDays() > 0) {
            vipType = 1;
        }

        //用户信用分及等级
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);

        //初始化标准货源信息
        bsPublishTransportService.setTransportStandardInfo(transport);
        //必须先获取标准货物信息才能生成hashCode
        transport.setHashCode(bsPublishTransportService.getNewHashCode(transport));

        //刷新用户相关信息
        this.refreshUserInfo(transport, tytUserSub, userCreditInfo);

        //5930开关是否打开
        boolean isOpen = tytConfigService.isEqualsOne(Constant.EQUITY_ON_OFF);

        if (isOpen) {
            if (userPermissionService.isVipPublishPermission(userId)) {
                vipType = 1;
            } else {
                vipType = 0;
            }

        }

        transport.setUserType(vipType);
        if (user != null && transport.getSource() == Transport.SOURCE_MANUAL) {
            transport.setVerifyPhotoSign(user.getVerifyPhotoSign().longValue() > 1 ? 0 : user.getVerifyPhotoSign());
        } else {
            transport.setVerifyPhotoSign(0);
        }
        transport.setUserPart(user.getUserPart());
        transport.setRegTime(user.getCtime());

        Timestamp todayTime = TimeUtil.getTimeStamp();
        transport.setId(null);
        if (StringUtils.isEmpty(user.getTrueName()) || StringUtils.isEmpty(user.getIdCard())) {
            transport.setUserShowName(user.getUserName());
        } else {
            transport.setUserShowName(user.getTrueName().substring(0, 1) + IdCardUtil.getCallGender(user.getIdCard()));
        }
        transport.setClientVersion(clientVersion);
        transport.setPlatId(clientSign);
        transport.setCtime(todayTime);
        transport.setMtime(todayTime);
        transport.setPubTime(TimeUtil.formatTime(todayTime));
        transport.setPubDate(todayTime);
        transport.setReleaseTime(originalTransport.getReleaseTime());
        transport.setStatus(1);
        transport.setIsShow(1);
        if (isHistoryGoods) {
            transport.setInfoStatus("0");
        }

        //刷新货源标签相关字段
        TransportLabelJson transportLabelJson = this.refreshLabelJson(todaySrcMsgId, oldLabelJson, transport,
                userCreditInfo, refreshDuplicate, null);

        if (topFlag) {
            //曝光时置顶
            transport.setSortType(TsSortTypeEnum.top.getCode());
        }

        // 处理自动重发装卸货时间过期的问题：若原货源装卸货时间已过，则变更为“今明两日，随到随装”发布
        if (saveDirectReq.getIsAutoResend()) {
            if (transport.getLoadingTime() != null && new Date().compareTo(transport.getLoadingTime()) > 0
                    || transport.getUnloadTime() != null && new Date().compareTo(transport.getUnloadTime()) > 0) {
                transport.setLoadingTime(null);
                transport.setBeginLoadingTime(null);
                transport.setUnloadTime(null);
                transport.setBeginUnloadTime(null);
            }
        }

        // 设置捂货时间
        setPriorityRecommendExpireTime(transport, isHistoryGoods, transportLabelJson.getIGBIResultData());
        // 专车发货需要重新计算运费
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(originalTransport.getExcellentGoods())) {
            CalculatePriceBean bean = new CalculatePriceBean();
            bean.setCargoOwnerId(originalTransport.getCargoOwnerId());
            bean.setUserId(originalTransport.getUserId());
            bean.setStartCity(originalTransport.getStartCity());
            bean.setDestCity(originalTransport.getDestCity());
            bean.setDriverDriving(originalTransport.getDriverDriving());
            bean.setWeight(originalTransport.getWeight());
            bean.setClientVersion(saveDirectReq.getClientVersion());
            GoodsAddressLevelRecordVo record = tytUserRecordMapper.getGoodsAddressLevelRecordByGoodsId(originalTransport.getSrcMsgId());
            if (Objects.isNull(record)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "未获取到该货源公里数，不能直接发布");
            }
            if (Objects.equals(originalTransport.getRefundFlag(), RefundFlagEnum.NO_RETURN.getCode())) {
                bean.setInfoFee(originalTransport.getInfoFee());
            }
            bean.setDistanceKilometer(record.getDistanceKilometer());
            bean.setOtherFee(record.getOtherFee());
            TytTransportMainExtend mainExtend = transportMainService.getMainExtend(originalTransport.getSrcMsgId());
            if (Objects.nonNull(mainExtend)) {
                bean.setUseCarType(mainExtend.getUseCarType());
            }

            ResultMsgBean resultMsgBean = bsPublishTransportService.calculatePriceV2(bean);
            JSONObject priceJson = JSON.parseObject(resultMsgBean.getData().toString());
            if (Objects.equals(priceJson.getInteger("price"), 0)) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "专车货源运费计算失败，不能直接发布");
            }
            logger.info("priceJson:{}", priceJson);
            Integer perkPrice = priceJson.get("perkPrice") == null ? 0 : priceJson.getInteger("perkPrice");
            if (saveDirectReq.getDirectOptEnum().equals(SaveDirectOptEnum.direct) || saveDirectReq.getDirectOptEnum().equals(SaveDirectOptEnum.AUTO_RESEND)){
                // 获取数据库中原有的 perkPrice（int 类型）
                Integer dbPerkPrice = mainExtend.getPerkPrice();
                // 判断当前计算出的 perkPrice 是否存在且大于0
                boolean hasCurrentPerk = perkPrice > 0;
                // 判断数据库中的 perkPrice 是否存在且大于0
                boolean hasDbPerk = dbPerkPrice != null && dbPerkPrice > 0;
                if (hasCurrentPerk) {
                    // 如果当前有优惠，但数据库中没有 或者 金额不一致 → 优惠比例变化
                    if (!hasDbPerk || !dbPerkPrice.equals(perkPrice)) {
                        return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "补贴比例更新，请使用编辑再重发");
                    }
                } else {
                    // 当前无优惠，但数据库中有 → 活动已结束
                    if (hasDbPerk) {
                        return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "货源参加的活动已结束，请使用编辑再发布");
                    }
                }
            }
        }

        if (Objects.nonNull(publishType)) {
            //转电议，转一口价
            transport.setPublishType(publishType);

            if (publishType.equals(PublishTypeEnum.fixed.getCode())) {
                //转一口价
                transport.setShuntingQuantity(1);
            }
        }
        if (StringUtils.isNotEmpty(price)) {
            transport.setPrice(price);
        }

        TytTransportMainExtend mainExtendOld = transportMainService.getMainExtend(originalTransport.getSrcMsgId());
        int modelLevel = transportGoodModelFactorService.judgeModelLevel(transport, mainExtendOld);

        CommissionTecFeeBean commissionTecFeeBean = commissionTecFee(saveDirectReq, transport, modelLevel);

        transportLabelJson = transportMainService.checkPersonalDuplicate(originalSrcMsgId, oldLabelJson, transport, refreshDuplicate, null);


        //10. hashcode验证是否重复信息,运满满货源不校验重货
        if (!transport.getSourceType().equals(SourceTypeEnum.运满满货源.getId())) {
            if (refreshDuplicate) {
                this.checkPersonalDuplicate(confirm, transport, oldLabelJson, transportLabelJson);
            }
        }

//		transport.setTsOrderNo(tytSequenceService.updateGetNumberForDate(Constant.TABLE_WAYBILL_NAME));// 需要从接口获取
        // 计算重发次数：今天的第一条为0
        Integer resendCount = 0;
        if (isHistoryGoods) {
            if (excellCardId == null) {
                // 专车发货不校验发货权益
                // 2025-04-27 自动重发不扣发货权益
                if (isOpen && !Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), originalTransport.getExcellentGoods())
                        && !saveDirectReq.getIsAutoResend()) {
                    msgBean = userService.checkUserPublishTransportPermission(userId, price);
                    if (ReturnCodeConstant.OK != msgBean.getCode()) {
                        return msgBean;
                    }
                }
            }
            resendCount = 0;
            transport.setTsOrderNo(tytSequenceService.updateGetNumberForDate(Constant.TABLE_WAYBILL_NAME));// 需要从接口获取
        } else {
            resendCount = transportService.getMaxResendCounts(originalSrcMsgId);
            if (topFlag) {
                resendCount = resendCount + 1;
            }
        }
        transport.setResendCounts(resendCount);
        // 原信息置为无效,删除缓存
        this.updateStatusBusiness(originalTransport.getSrcMsgId(), recommendHashCode, userId, 0, null, saveVary);
        // 重发次数为0的不需要[].
        if (resendCount == 0) {
            transport.setPcOldContent(transport.getStartPoint().trim() + "---" + transport.getDestPoint().trim() + " " + transport.getTaskContent().trim());
        } else {
            transport.setPcOldContent("[" + resendCount + "]." + transport.getStartPoint().trim() + "---" + transport.getDestPoint().trim() + " " + transport.getTaskContent().trim());
        }
        transport.setDisplayType("1");
        transport.setIsDisplay(1);
        bsPublishTransportService.addReferTransportNewInfo(transport); // 调用v6000版本后标准货源库补全数据，之前的造成数据错误
        // 如果出发地省市区为空或者不是标准化数据则不进行精准货源推荐
        if (transport.getIsStandard() != null && transport.getIsStandard() == 0 && StringUtils.isNotEmpty(transport.getStartArea()) && StringUtils.isNotEmpty(transport.getStartCity()) && StringUtils.isNotEmpty(transport.getStartProvinc())) {
            if (isHistoryGoods) {
                logger.info("昨日获取今日重发，进行精准推荐,货物ID：{}", transport.getId());
                transport.setIsDisplay(0);
            } else {
                logger.info("今日编辑重发货物，不进行精准推荐,货物ID：{}", transport.getId());
            }
            // 精准货源是否在找货列表显示 0：找货列表不显示 1：找货列表显示
            Integer isDisplayOnoff = this.tytConfigService.getIntValue("recommendTransportIsDisplayOnoff", 0);
            if (isDisplayOnoff.intValue() == 1) {
                logger.info("货物直接再找货列表显示,货物ID：{}", transport.getId());
                transport.setIsDisplay(1); // 直接在找货列表中显示
            }
        }
        // 如果是个人货主,将sourceType置为3
        CsMaintainedCustom csMaintainedCustom = csMaintainedCustomService.getCsMaintainedCustomByUserId(user.getId());
//        transportService.add(transport);
        //判断是否是昨天的货  如果是 则新增 否则 更新数据
        Long srcMsgIdForBackend = 0L;



        if (isHistoryGoods) {
            TransportMain transportMain = new TransportMain();
            logger.info("昨日货物直接发布，transportMain货物ID：{}， src_msg_id:{},SourceType:{}", transportMain.getId(), transportMain.getSrcMsgId(), transport.getSourceType());
            if (Objects.equals(SourceTypeEnum.运满满货源.getId(), transport.getSourceType()) || Objects.equals(SourceTypeEnum.宏信货源.getId(), transport.getSourceType())
                    || Objects.equals(SourceTypeEnum.代调发货.getId(), transport.getSourceType())) {
                transport.setSourceType(transport.getSourceType());
            } else if (csMaintainedCustom != null && Objects.equals(1, csMaintainedCustom.getGoodsOwner())) {
                transport.setSourceType(3);
            } else {
                transport.setSourceType(1);
            }
            transport.setReleaseTime(todayTime);
            BeanUtils.copyProperties(transport, transportMain);
            transportMain.setId(null);
            transportMainService.add(transportMain);
            transportMain.setSrcMsgId(transportMain.getId()); // 设置srcId为新值

            // main表扩展表使用上一单数据
            TytTransportMainExtend mainExtend = transportMainService.getMainExtend(originalTransport.getSrcMsgId());
            if (Objects.isNull(mainExtend)) {
                mainExtend = new TytTransportMainExtend();
            }
            mainExtend.setId(null);
            mainExtend.setSrcMsgId(transportMain.getId());
            mainExtend.setCreateTime(transportMain.getCtime());
            mainExtend.setModifyTime(transportMain.getMtime());
            mainExtend.setGoodTransportLabel(0);
            mainExtend.setGoodTransportLabelPart(0);
            mainExtend.setSuggestMinPrice(publishBean.getFixPriceMin());
            mainExtend.setSuggestMaxPrice(publishBean.getFixPriceMax());
            mainExtend.setFixPriceFast(publishBean.getFixPriceFast());
            mainExtend.setCostPrice(publishBean.getThMinPrice());

            if (modelLevel > 0) {
                mainExtend.setGoodTransportLabel(modelLevel);
            } else if (modelLevel < 0) {
                mainExtend.setGoodTransportLabelPart(modelLevel * -1);
            }
            transportMainService.addMainExtend(mainExtend);

            transport.setSrcMsgId(transportMain.getId());
            transportService.add(transport);
            // 添加货源扩展信息
            TytTransportExtend transportExtend = new TytTransportExtend();
            BeanUtils.copyProperties(mainExtend, transportExtend);
            transportExtend.setId(null);
            transportExtend.setTsId(transport.getId());
            transportExtend.setSrcMsgId(transport.getSrcMsgId());
            transportExtend.setCreateTime(transport.getCtime());
            transportExtend.setModifyTime(transport.getMtime());
            transportExtend.setTopFlag(2); // 直接发布
            transportExtendService.addExtend(transportExtend);

//            backendTransportMapper.updateMsgIdByMsgId(transportMain.getId(),originalTransport.getSrcMsgId());
            // 修改src_msg_id(当天有效)
//            this.updateSrcMsgId(transport.getId(), transportMain.getId());
            // 因过期数据，重发后，精准推荐回退置回找货列表状态，src_msg_id为旧值，导致数据异常
//            transport.setSrcMsgId(transportMain.getId()); // 设置srcId为新值
            logger.info("昨日货物直接发布，transportMain货物ID：{}， src_msg_id:{}", transportMain.getId(), transportMain.getSrcMsgId());
            srcMsgIdForBackend = transportMain.getId();

            if (transport.getIsStandard() != null && transport.getIsStandard() == 0) {
                // 如果为标准化货源则存储运费利润率信息,只在第一次添加时存储信息
                TransportSubBean transportSubBeanTemp = transportSubService.getBySrcMsgId(transportMain.getId());
                if (transportSubBeanTemp == null) {
                    transportSubBeanTemp = new TransportSubBean();
                    transportSubBeanTemp.setTsId(transportMain.getId());
                    transportSubService.add(transportSubBeanTemp);
                }
            }

            // 专车货源需要往五级地址表插入数据
            if (Objects.equals(originalTransport.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())) {
                // 查询上一单公里数和其他费用，新货源插入数据
                GoodsAddressLevelRecordVo record = tytUserRecordMapper.getGoodsAddressLevelRecordByGoodsId(originalTransport.getSrcMsgId());
                if (Objects.nonNull(record)) {
                    tytUserRecordMapper.insertIntoGoodsAddressLevelRecordGoodCarPriceTransportIsTrue(transportMain.getId(),
                            record.getDistanceKilometer(), record.getOtherFee());
                }
            }

            // 如果是运满满货源，同步添加到运满满关系表中
            if (transport.getSourceType() != null && transport.getSourceType().equals(SourceTypeEnum.运满满货源.getId())) {
                TytTransportMbMerge tytTransportMbMerge = tytTransportMbMergeMapper.selectBySrcMsgId(originalSrcMsgId);
                if (Objects.isNull(tytTransportMbMerge)) {
                    return new ResultMsgBean(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "数据记录不存在");
                }

                transportYMMService.saveOrUpdateMbMerge(transport.getSrcMsgId(), tytTransportMbMerge.getCargoId(), tytTransportMbMerge.getCargoVersion());
            }
        } else {

            TransportMain transportMainOrgin = transportMainService.getBySrcMsgId(transport.getSrcMsgId());

            // 修改transportMain表信息  并保存
            BeanUtils.copyProperties(transport, transportMainOrgin, "id", "srcMsgId", "ctime", "releaseTime");

            transportMainOrgin.setMtime(TimeUtil.getTimeStamp());
            //oldTran.setResendCounts(transport.getResendCounts());
            transportMainOrgin.setStatus(1);
            transportMainService.update(transportMainOrgin);

            TytTransportMainExtend mainExtend = transportMainService.getMainExtend(transport.getSrcMsgId());
            if (Objects.nonNull(mainExtend)) {
                mainExtend.setModifyTime(transportMainOrgin.getMtime());
                mainExtend.setGoodTransportLabel(0);
                mainExtend.setGoodTransportLabelPart(0);

                if (modelLevel > 0) {
                    mainExtend.setGoodTransportLabel(modelLevel);
                } else if (modelLevel < 0) {
                    mainExtend.setGoodTransportLabelPart(modelLevel * -1);
                }
                mainExtend.setSuggestMinPrice(publishBean.getFixPriceMin());
                mainExtend.setSuggestMaxPrice(publishBean.getFixPriceMax());
                mainExtend.setFixPriceFast(publishBean.getFixPriceFast());
                mainExtend.setCostPrice(publishBean.getThMinPrice());
                transportMainService.updateMainExtend(mainExtend);
            }

            //save transport
            transport.setSourceType(transportMainOrgin.getSourceType());

            TytTransportExtend transportExtend = new TytTransportExtend();
            BeanUtils.copyProperties(mainExtend, transportExtend);
            this.saveTransportAndCheckTop(transport, transportExtend, topFlag, useExposure);


            srcMsgIdForBackend = transportMainOrgin.getId();

        }

        if (commissionTecFeeBean.isRecalculateTecServiceFee()) {
            if (commissionTecFeeBean.isCommissionTransport()) {
                //记录抽佣货源信息
                bsPublishTransportService.saveCommissionTransportTecServiceFeeData(transport.getSrcMsgId(), commissionTecFeeBean.getTytTecServiceFeeConfigToComputeResult());
                tytTransportTecServiceFeeMapper.saveTransportTecServiceFeeLog(transport.getSrcMsgId(), 1, 1);
            } else {
                //不抽佣则清楚抽佣货源信息
                tytTransportTecServiceFeeMapper.deleteFreeTecServiceFeeLogBySrcMsgId(transport.getSrcMsgId());
                tytTransportTecServiceFeeMapper.delete(transport.getSrcMsgId());
                tytTransportTecServiceFeeMapper.saveTransportTecServiceFeeLog(transport.getSrcMsgId(), 2, 1);
            }
        }

        // 添加到个人货主
        if ((csMaintainedCustom != null && csMaintainedCustom.getGoodsSync() == 1) || Objects.equals(SourceTypeEnum.运满满货源.getId(), transport.getSourceType())) {
            tytTransportDispatchService.saveTransportDispatch(transport, csMaintainedCustom, clientSign);
        }
        //判断是否限时货源
//        BackendTransportBean backendTransportBean = backendTransportMapper.selectBackendStatusByMsgId(transport.getSrcMsgId());
        if (backendTransportBean != null) {
            String dialPhone = transportBackendAxbBinderService.getDialPhone(userId, user.getCellPhone());
            transportBackendAxbBinderService.saveAxbBinder(backendTransportBean.getTel(), dialPhone);
            if (StringUtils.isNotEmpty(backendTransportBean.getTel3())) {
                transportBackendAxbBinderService.saveAxbBinder(backendTransportBean.getTel3(), dialPhone);
            }
            backendTransportBean.setFindCarType(1);
            backendTransportBean.setMsgId(srcMsgIdForBackend);
            backendTransportMapper.updateBackendTransportStatus(backendTransportBean);
            backendTransportMapper.updateBackendTransportUserStatus(backendTransportBean.getId());
        }
        // 更新相似货源信息
        transportService.updateSimilarityCode(transport);

        try {
            //保存历史
            Transport tbTmp = transportService.getById(transport.getId());
            transportHistoryService.saveTransportHistory(tbTmp);
        } catch (Exception e) {
            logger.error("saveTransportHistory_error : ", e);
        }

        // hashCode保存到缓存
        String hashCode = transport.getHashCode();
        cacheService.setObject(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + hashCode, hashCode, Constant.CACHE_EXPIRE_TIME_24H);
        // 加入重发置顶队列 用于修改 收藏与拨打电放对应的货物ID,非今天的数据暂定为没有必要
        if (!isHistoryGoods) {
            tytUserSubService.saveChangeTytUserSub(userId);
        }

        // 清除缓存
        cacheService.del(Constant.CACHE_USERSUB_KEY + userId.longValue() + "_" + TimeUtil.formatDateMonthTime(new Date()));
        // 向MQ发送无效货源信息验证请求
        if (transport != null && transport.getId() != null) {
            try {
                sendNullifyMessage2MQ(transport.getId());
            } catch (Exception e) {
                logger.error("发送无效货源消息失败,货源id:{}", transport.getId());
            }
        }
        // 向MQ发送精准货源货源推荐信息
        if (transport != null && transport.getIsStandard() != null && transport.getIsStandard() == 0 && transport.getIsDisplay() != null && transport.getIsDisplay() == 0 && isHistoryGoods) {
            recommendHashCode = StringUtil.getHashCodeNewByRecommend(transport);
            String result = (String) cacheService.getObject(Constant.CACHE_RECOMMEND_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + recommendHashCode);
            boolean flag = StringUtils.isNotEmpty(result);
            /* 保存hashcode到缓存，时间1天 */
            if (!flag) {
                cacheService.setObject(Constant.CACHE_RECOMMEND_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + recommendHashCode, recommendHashCode, Constant.CACHE_EXPIRE_TIME_24H);
                logger.info("非今日直接发布货物，发送精准推荐MQ,货物ID：{}", transport.getId());
                sendRecommendMessage2MQ(transport.getId());
            } else {
                logger.info("不发送精准推荐MQ,货物内容存在相同货物特征，货物ID：{}， src_msg_id:{}", transport.getId(), transport.getSrcMsgId());
                updateRevertTransportStatus(transport);
            }
        } else {
            logger.info("今日直接发布货物,不进行精准推荐MQ，货物ID：{}", transport.getId());
            updateRevertTransportStatus(transport);
        }
        //6270 当天直接发布和编辑发布的，是同一个id，就算一条货源；历史货源直接发布发布数+1
        if (isHistoryGoods) {

            Integer publishNum = Objects.isNull(tytUserSub.getPublishNum()) ? 0 : tytUserSub.getPublishNum();
            tytUserSubService.updatePublishNum(userId, publishNum + 1);
            // 更新发货权益使用记录
            if (ObjectUtil.isNotNull(msgBean) && ObjectUtil.isNotNull(msgBean.getData())) {
                userPermissionService.updatePublishPermissionTsId(msgBean.getData(), transport.getSrcMsgId());
            }
        }
        msgBean = new ResultMsgBean(ReturnCodeConstant.OK, "发布成功");
        //pc端返回goodsId字段
        if (transport != null && transport.getId() != null) {
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("goodsId", transport.getId());
            dataMap.put("tsOrderNo", transport.getTsOrderNo());
            dataMap.put("srcMsgId", transport.getSrcMsgId());
            msgBean.setData(dataMap);

        }

        String key = "automaticGoodCarPriceTransport" + ":" + transport.getSrcMsgId();
        if (saveDirectReq.getAutomaticGoodCarPriceTransport() != null && saveDirectReq.getAutomaticGoodCarPriceTransport()) {
            //自动转优车定价成功
            RedisUtil.set(key, "1", 10 * 60);
        } else {
            RedisUtil.del(key);
        }

        logger.info("BI数据存储:{}", JSONUtil.toJsonStr(commissionTecFeeBean));

        TransportBIDataJson transportBIDataJson = new TransportBIDataJson(transportLabelJson.getIGBIResultData()
                , null, null, null, null, null, null
                , Integer.parseInt(saveDirectReq.getClientSign()) == 1 ? 2 : 1, null, null, null, null, null, null
                //自动转优车定价，记录缓存并存储到五级地址表中供BI查询
                , saveDirectReq.getAutomaticGoodCarPriceTransport() != null && saveDirectReq.getAutomaticGoodCarPriceTransport() ? saveDirectReq.getAutomaticGoodCarPriceTransportType() : null, saveDirectReq.getGoodCarPriceTransport()
                , null, null, null, null, null, null,
                commissionTecFeeBean.isRecalculateTecServiceFee() && commissionTecFeeBean.isCommissionTransport() ? 1 : !commissionTecFeeBean.isRecalculateTecServiceFee() ? -1 : 0, commissionTecFeeBean.getMeetCommissionRulesResult().getMeetCommissionRules(), 1
                , transportLabelJson.getGoodsModelScore(),transportLabelJson.getCommissionLabel());
        saveSomeBINeedDataResult(transport.getSrcMsgId(), transportBIDataJson);
        // 直接发布数据埋点
        transportEventTrickingService.directPublish(transport, transportLabelJson, saveDirectReq);

        // 增加货源刷新次数
        goodsRefreshManualService.addGoodsRefresh(transport, saveDirectReq, originalTransport);
        // 专车货源直接发布自动派单
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(originalTransport.getExcellentGoods())) {
            threadPoolExecutor.execute(() -> autoAssignOrderForSpecialCar(transport, user));
        }

        //记录货源好货分数、好货运价分数
        recordScoreEveryTransport(transport, transport.getSrcMsgId(), transport.getId(), isHistoryGoods);

        // 如果是历史货源直接发布，记录数据
        if (isHistoryGoods) {
            transportAutoResendService.addResendLog(originalTransport.getSrcMsgId(), transport.getSrcMsgId(), saveDirectReq.getIsAutoResend() ? 1 : 2);
        }

        return msgBean;
    }

    /**
     * 设置优车类型，新老数据兼容
     * <pre>
     *  a. 老版本优车（1.0、2.0），在新版货源根据定价金额映射极速优车、快速优车、特惠优车，若原价格低于特惠优车或无价，映射为特惠优车
     *  b. 老版本优车，若在新版未成功获取优车定价的
     *      i. 有运费：用户出价+原来的议价方式（一口价、电议）
     *      ii. 无运费：普通找车
     *  c. 老版本货源若是优车电议，在新版本也展示为极速优车、快速优车、特惠优车+电议
     *  d. 老版本普货一口价，新版本展示为用户出价+一口价
     *  e. 老版本普货电议有价，新版本展示为用户出价+电议
     *  f. 老版本电议无价，新版本展示为普通找车
     *  g. 以上映射货源，在新版本APP进行直接发布时，按照原货源直接发布
     * </pre>
     */
    private void getExcellentGoodsType(Transport transport, TransportPublishBean publishBean) {
        // 优车三挡价格，如果客户端没传，重新获取
        if (publishBean.getFixPriceMin() == null || publishBean.getFixPriceMax() == null || publishBean.getFixPriceFast() == null) {
            TransportMain transportMain = BeanUtil.copyProperties(transport, TransportMain.class);
            CarryPriceVo carryPriceVo = excellentPriceConfigService.getThPrice(transportMain);
            if (carryPriceVo != null) {
                publishBean.setFixPriceMin(carryPriceVo.getFixPriceMin());
                publishBean.setFixPriceMax(carryPriceVo.getFixPriceMax());
                publishBean.setFixPriceFast(carryPriceVo.getFixPriceFast());
                publishBean.setThMaxPrice(carryPriceVo.getThMaxPrice());
                publishBean.setThMinPrice(carryPriceVo.getThMinPrice());

            }
        }

        if (ExcellentGoodsEnums.isNormal(transport.getExcellentGoods())) {
            // 普通发货映射有价映射为用户出价，无价映射为普通找车
            if (TransportUtil.hasPrice(transport.getPrice())) {
                transport.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
            } else {
                transport.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
            }
        } else if (ExcellentGoodsEnums.isSpecial(transport.getExcellentGoods())) {
            // 专车货源仍映射为专车货源
            transport.setPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode());

        } else {
            // 根据优车定价金额映射极速优车、快速优车、特惠优车
            if (publishBean.getFixPriceMin() != null && publishBean.getFixPriceMax() != null && publishBean.getFixPriceFast() != null) {
                PublishGoodsTypeEnum goodsTypeEnum = TransportUtil.judgeExcellentGoodsLevel(publishBean.getPrice(),
                        publishBean.getFixPriceMin(), publishBean.getFixPriceMax(), publishBean.getFixPriceFast());
                // 原价格低于特惠优车或无价，映射为特惠优车
                goodsTypeEnum = goodsTypeEnum == null ? PublishGoodsTypeEnum.EXCELLENT_GOODS : goodsTypeEnum;
                transport.setPublishGoodsType(goodsTypeEnum.getCode());
            } else {
                // 若在新版未成功获取优车定价，有价=>用户出价，无价=>普通找车
                if (TransportUtil.hasPrice(transport.getPrice())) {
                    transport.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
                    transport.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
                    transport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
                } else {
                    transport.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
                    transport.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
                    transport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
                }
            }
        }
    }

    /**
     * 计算抽佣技术服务费
     */
    private CommissionTecFeeBean commissionTecFee(SaveDirectReq saveDirectReq, Transport transport, Integer modelLevel) {

        CommissionTecFeeBean commissionTecFeeBean = new CommissionTecFeeBean();

        //只有填价加价转一口价或手动直接发布的时候需要重新计算抽佣货源的技术服务费
        boolean isRecalculateTecServiceFee = false;
        boolean isCommissionTransport = false;
        TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = null;

        if (saveDirectReq.getAutomaticGoodCarPriceTransportType() != null) {
            if (saveDirectReq.getAutomaticGoodCarPriceTransportType() == 2) {
                //手动直接发布
                isRecalculateTecServiceFee = true;
            } else if (saveDirectReq.getAutomaticGoodCarPriceTransportType() == 3) {
                //填价或加价
                isRecalculateTecServiceFee = true;
            } else if (saveDirectReq.getAutomaticGoodCarPriceTransportType() == 4
                    && saveDirectReq.getPublishType() != null && PublishTypeEnum.fixed.getCode().equals(saveDirectReq.getPublishType())
                    && StringUtils.isNotBlank(saveDirectReq.getPrice())) {
                //转一口价的同时填价
                isRecalculateTecServiceFee = true;
            }
        }

        MeetCommissionRulesResult meetCommissionRules = new MeetCommissionRulesResult();
        //抽佣货源计算技术服务费
        if (isRecalculateTecServiceFee) {
            //抽佣货源计算技术服务费
            //------------------------这一步要在所有逻辑处理结束后进行-------------------------
            TransportMain transportMainForId = transportMainService.getTransportMainForId(saveDirectReq.getGoodsId());

            boolean isGoodCarPriceTransport = (saveDirectReq.getGoodCarPriceTransport() != null && saveDirectReq.getGoodCarPriceTransport() == 1)
                    || (StringUtils.isNotBlank(transport.getLabelJson()) && JSONObject.parseObject(transport.getLabelJson()).containsKey("goodCarPriceTransport"));

            // 如果代调账号发的专车非平台，不重新计算技术服务费
            TransportLabelJson transportLabelJson = JSONObject.parseObject(transport.getLabelJson(), TransportLabelJson.class);

            TytTransportMainExtend mainExtend = transportMainService.getMainExtend(transport.getSrcMsgId());
            if (modelLevel > 0) {
                mainExtend.setGoodTransportLabel(modelLevel);
            } else if (modelLevel < 0) {
                mainExtend.setGoodTransportLabel(0);
                mainExtend.setGoodTransportLabelPart(modelLevel * -1);
            }

            if (Objects.equals(transportLabelJson.getIsManualTecServiceFee(), 1)) {
                tytTecServiceFeeConfigToComputResult = bsPublishTransportService.getManualTecServiceFeeResult(transport, mainExtend.getCommissionScore());
                // 如果技术服务费>0，算抽佣
                if (transport.getTecServiceFee() != null && transport.getTecServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                    isCommissionTransport = true;
                }
                isRecalculateTecServiceFee = false;
            } else {
                tytTecServiceFeeConfigToComputResult = bsPublishTransportService.makeTecServiceFeeData(transport, transportMainForId, meetCommissionRules, isGoodCarPriceTransport, mainExtend);
                if (tytTecServiceFeeConfigToComputResult != null) {
                    isCommissionTransport = true;
                }
            }

        }

        commissionTecFeeBean.setRecalculateTecServiceFee(isRecalculateTecServiceFee);
        commissionTecFeeBean.setCommissionTransport(isCommissionTransport);
        commissionTecFeeBean.setTytTecServiceFeeConfigToComputeResult(tytTecServiceFeeConfigToComputResult);
        commissionTecFeeBean.setMeetCommissionRulesResult(meetCommissionRules);

        return commissionTecFeeBean;

    }

    /**
     * 6500需求：只要勾选优推好车主，且走了好货模型就设置
     */
    private void setPriorityRecommendExpireTime(Transport transport, boolean isHistoryGoods, Integer iGBIResultData) {
        if (iGBIResultData == null) {
            transport.setPriorityRecommendExpireTime(null);
            return;
        }
        //判断是否是优推好车主如果是优推好车主，直接发布时重新计算有效时间
        boolean isPriorityRecommend;
        // 判断用户是否在白名单内
        if (Objects.equals(transport.getExcellentGoods(), 1)) {
            // 优车货源
            Integer userType = abtestService.getUserType("recommend_excellent", transport.getUserId());
            // 如果之前没捂货，判断优车货源是否默认为优推好车主，是则开启捂货
            if (Objects.equals(userType, 1) && transport.getPriorityRecommendExpireTime() == null) {
                TytCoverGoodsConfig coverGoodsConfig = coverGoodsConfigService.getByCode(CoverGoodsEnum.EXCELLENT_COVER.getConfigCode());
                isPriorityRecommend = coverGoodsConfig != null && Objects.equals(coverGoodsConfig.getConfigItem(), 1);
            } else {
                isPriorityRecommend = Objects.equals(userType, 1);
            }
        } else {
            // 普通货源
            Integer userType = abtestService.getUserType("priority_recommend", transport.getUserId());
            isPriorityRecommend = Objects.equals(userType, 1);
        }
        if (isPriorityRecommend) {
            // 昨天的货源直接发布是新货源，重新计算捂货时间
            if (isHistoryGoods) {
                Integer xSecond = tytCoverGoodsDialConfigService.selectXTime();
                Date priorityRecommendExpireTime = DateUtils.addSeconds(transport.getPubDate(), xSecond);
                transport.setPriorityRecommendExpireTime(priorityRecommendExpireTime);
            }
        } else {
            transport.setPriorityRecommendExpireTime(null);
        }
    }

    /**
     * 判断是否是秒抢货源
     * 1. 剔除：开票/专车/多车的货源/YMM的货源/后台发的货
     * 2. 一口价
     * 2. 定价不能为0
     * 4. 运价分=0.00的货源 或运价分超过10分 或 目的地命中圈定城市
     *
     * @since 6600
     * 工单：去掉长宽高重限制，增加运价分=0.00的货源，原有运价分>10的逻辑不变
     */
    @Override
    public boolean checkIsSeckillGoods(Transport transport, BigDecimal commissionScore) {
        // 是否开启秒抢货源判断开关
        Integer switchV = tytConfigService.getIntValue("turn_on_seckill_goods_switch", 0);
        if (switchV == 0) {
            return false;
        }
        // 非一口价直接返回
        if (!Objects.equals(transport.getPublishType(), PublishTypeEnum.fixed.getCode())) {
            logger.info("checkIsSeckillGoods 非一口价，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }
        // 定金不能为0
        if (transport.getInfoFee() == null || transport.getInfoFee().compareTo(BigDecimal.ZERO) <= 0) {
            logger.info("checkIsSeckillGoods 定金为0，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }

        // 非 开票/专车/多车的货源/YMM的货源/后台发的货 直接返回
        if (Objects.equals(transport.getInvoiceTransport(), 1)
                || Objects.equals(transport.getExcellentGoods(), 2)
                || (transport.getShuntingQuantity() != null && transport.getShuntingQuantity() > 1)
                || !ArrayUtil.contains(new int[]{SourceTypeEnum.普通货主.getId(), SourceTypeEnum.个人货主.getId()}, transport.getSourceType())) {
            logger.info("checkIsSeckillGoods 非APP发货，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }
        // 长宽高重有一个为0 直接返回
        /*if (StringUtils.isBlank(transport.getWeight()) || Double.parseDouble(transport.getWeight()) <= 0
                || StringUtils.isBlank(transport.getLength()) || Double.parseDouble(transport.getLength()) <= 0
                || StringUtils.isBlank(transport.getWide()) || Double.parseDouble(transport.getWide()) <= 0
                || StringUtils.isBlank(transport.getHigh()) || Double.parseDouble(transport.getHigh()) <= 0) {
            logger.info("checkIsSeckillGoods 长宽高重为0，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }*/
        // 运价分超过10分 或 目的地命中圈定城市 ，打标
        Integer score = tytConfigService.getIntValue("turn_on_seckill_goods_score", 10);
        logger.info("checkIsSeckillGoods 质量分超过10分或目的地命中圈定城市，srcMsgId:{}，seckillGoodsScore：{}，commissionScore:{}", transport.getSrcMsgId(), score, commissionScore);
        return (commissionScore == null || commissionScore.compareTo(BigDecimal.ZERO) == 0 || commissionScore.compareTo(new BigDecimal(score)) > 0)
                || transportSeckillFactorMapper.existDestCity(transport.getDestCity()) > 0;
    }

    /**
     * 是否发放曝光卡
     *
     * @param srcMsgId
     */
    @Override
    @Async
    public void checkAndSaveExposureCardGiveaway(Long srcMsgId) throws IOException {
        Response<Boolean> response = transportTecserviceFeeClient.checkAndSaveExposureCardGiveaway(srcMsgId).execute();
        logger.info("校验是否发放曝光卡， srcMsgId:{}，response:{}", srcMsgId, response);
    }

    /**
     * 运满满货源直接发布校验
     *
     * @param oldTransportMain
     * @param srcMsgId
     * @param callPhone
     */
    private void checkYmmForDirectPublish(Transport oldTransportMain, Long srcMsgId, String callPhone) {
        /**
         *  直接发布 版本无变化不用拦截 前端约定提示
         */
        if (SourceTypeEnum.运满满货源.getId().equals(oldTransportMain.getSourceType())) {

            String companyGoodsUsers = tytConfigService.getStringValue("company_goods_users");
            if (StringUtils.isEmpty(companyGoodsUsers) || !companyGoodsUsers.contains(callPhone)) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "当前用户非调度身份"));
            }

            TytTransportMbMerge tytTransportMbMerge = tytTransportMbMergeMapper.selectBySrcMsgId(srcMsgId);
            if (tytTransportMbMerge == null) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "数据记录不存在"));
            }
            if (tytTransportMbMerge.getStatus() == 0) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "该货源已发布，看看其它货源吧！"));
            }
            TytTransportMbMerge tytTransportMbMergeByCargoId = tytTransportMbMergeMapper.selectByCargoId(tytTransportMbMerge.getCargoId());
            if (tytTransportMbMergeByCargoId != null) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "该货源已发布，看看其它货源吧！"));
            }

            TytMbCargoSyncInfo tytMbCargoSyncInfo = tytMbCargoSyncInfoMapper.selectYMMCargoById(tytTransportMbMerge.getCargoId());
            if (tytMbCargoSyncInfo.getDelFlag() == 1) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "该货源已下架，看看其它货源吧！"));
            }
            if (!tytTransportMbMerge.getCargoVersion().equals(tytMbCargoSyncInfo.getCargoVersion())) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "该货源已有信息变更，不允许发布"));
            }
        }
    }

    @Override
    public void makeNickName(Transport transport, User user) {
        if (user == null || user.getId() == null) {
            return;
        }
        boolean isShowUserName = false;

        if (transport.getExcellentGoods() != null && transport.getExcellentGoods() == 1) {
            //如果是优车并且该货主不在AB测试中才将昵称赋值为X老板
            ArrayList<String> codes = new ArrayList<>();
            codes.add(AbtestServiceImpl.YOUCHE_IS_SHOW_USERNAMEABTEST_CODE);
            List<TytAbtestConfigVo> userTypeList = abtestService.getUserTypeList(codes, user.getId());
            TytAbtestConfigVo tytAbtestConfigVo = userTypeList.get(0);
            if (AbtestServiceImpl.YOUCHE_IS_SHOW_USERNAMEABTEST_CODE.equals(tytAbtestConfigVo.getCode()) && tytAbtestConfigVo.getType() == 1) {
                isShowUserName = true;
            }
        } else {
            Integer type = abtestService.getUserTypeList(Collections.singletonList(SHOW_BOSS_NICK_NAME), user.getId()).get(0).getType();
            if (type == 0) {
                isShowUserName = true;
            }
        }
        if (isShowUserName) {
            transport.setNickName(StringUtil.formatUserName(user.getUserName(), String.valueOf(user.getId())));
        } else {
            String surname = StringUtils.isBlank(user.getTrueName()) ? "" : Character.toString(user.getTrueName().charAt(0));
            transport.setNickName(surname + "老板");
        }
    }

    @Override
    public void recordScoreEveryTransport(Transport transport, Long newTransportSrcMsgId, Long newTransportId, boolean isHistoryGoods) {
        try {
            //调用好货模型分数、好货运价模型分数，以便后续记录
            BigDecimal score = null;
            BigDecimal limScore = null;
            Integer level = null;
            Integer limLevel = null;
            GoodModelResult goodModelResult = checkInstantGrab(transport);
            if (goodModelResult != null) {
                score = goodModelResult.getScore();
                level = goodModelResult.getLevel();
                limScore = goodModelResult.getLim_score();
                limLevel = goodModelResult.getLim_level();
            }
            BigDecimal commissionScore = checkCommissionScore(transport);

            // 6700需求：秒抢货源长时间未成交会变成非秒抢，这列货源再操作不会变成秒抢。
            // 历史货源重新发布，或者没有转非秒抢标签，判断秒抢逻辑
            boolean isSeckillGoods = false;
            if (isHistoryGoods || !Objects.equals(TransportUtil.getLabelJson(transport).getSeckillDowngrade(), 1)) {
                isSeckillGoods = this.checkIsSeckillGoods(transport, commissionScore);
            }
            // 2025-02-18需求变更，秒抢货源新增对照组，实验组70%，走原来的逻辑；对照组30%，只打标走普通货源逻辑
            int seckillGoods = isSeckillGoods ? (newTransportSrcMsgId % 10 < 7 ? 1 : 2) : 0;

            saveToMainExtend(newTransportSrcMsgId, score, limScore, level, limLevel, commissionScore, seckillGoods);
            saveToTransportExtend(newTransportSrcMsgId, newTransportId, score, limScore, level, limLevel, commissionScore, seckillGoods);

            // 6650秒抢货源不同步ymm，如果是秒抢货源，并且已同步ymm，需要通知ymm下架
            if (seckillGoods == 1 && !Objects.equals(transport.getSourceType(), SourceTypeEnum.运满满货源.getId())) {
                // 发送MQ货源打通满满货源下架
                TytTransportSyncYmm resultTransport = tytTransportSyncYmmService.findTransportSyncYmm(transport.getSrcMsgId());
                if (null != resultTransport) {
                    MqRevokeTransportSyncMsg syncMsg = new MqRevokeTransportSyncMsg();
                    String messageSerailNum = SerialNumUtil.generateSeriaNum();
                    syncMsg.setMessageType(MqBaseMessageBean.MB_SYNC_TRANSPORT_WAYBILL_MESSAGE);
                    syncMsg.setPartnerSerialNo(IdUtils.getIncreaseIdByLocalTime());
                    syncMsg.setCargoId(resultTransport.getCargoId());
                    SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
                    syncMsg.setPartnerRequestTime(format.format(new Date()));
                    syncMsg.setDeleteReason(1);
                    syncMsg.setSrcMsgId(transport.getSrcMsgId());
                    syncMsg.setMessageSerailNum(messageSerailNum);
                    String messageContent = JSON.toJSONString(syncMsg);
                    tytMqMessageService.addSaveMqMessage(messageSerailNum, messageContent, syncMsg.getMessageType());
                    tytMqMessageService.sendMbMqMessage(messageSerailNum, messageContent, syncMsg.getMessageType());
                    //更改同步货源下架状态
                    tytTransportSyncYmmService.updateTransportSyncYmmStatus(resultTransport);
                }
            }
        } catch (Exception e) {
            logger.info("保存货源分数信息到货源拓展表失败 原因:", e);
        }

    }

    private void saveToTransportExtend(Long newTransportSrcMsgId, Long newTransportId, BigDecimal score, BigDecimal limScore,
                                       Integer level, Integer limLevel, BigDecimal commissionScore, Integer seckillGoods) {
        TytTransportExtend extend = tytTransportExtendMapper.selectByTsId(newTransportId);
        if (Objects.isNull(extend)) {
            extend = new TytTransportExtend();
            extend.setSrcMsgId(newTransportSrcMsgId);
            extend.setTsId(newTransportId);
            extend.setGoodModelScore(score);
            extend.setGoodModelLevel(level);
            extend.setLimGoodModelScore(limScore);
            extend.setLimGoodModelLevel(limLevel);
            extend.setCommissionScore(commissionScore);
            extend.setSeckillGoods(seckillGoods);
            tytTransportExtendMapper.insertSelective(extend);
        } else {
            extend.setGoodModelScore(score);
            extend.setLimGoodModelScore(limScore);
            extend.setCommissionScore(commissionScore);
            extend.setModifyTime(new Date());
            extend.setSeckillGoods(seckillGoods);
            tytTransportExtendMapper.updateByPrimaryKeySelective(extend);
        }
    }

    private void saveToMainExtend(Long newTransportSrcMsgId, BigDecimal score, BigDecimal limScore, Integer level,
                                  Integer limLevel, BigDecimal commissionScore, Integer seckillGoods) {
        TytTransportMainExtend mainExtend = tytTransportMainExtendMapper.getBySrcMsgId(newTransportSrcMsgId);
        if (Objects.isNull(mainExtend)) {
            mainExtend = new TytTransportMainExtend();
            mainExtend.setSrcMsgId(newTransportSrcMsgId);
            mainExtend.setGoodModelScore(score);
            mainExtend.setGoodModelLevel(level);
            mainExtend.setLimGoodModelScore(limScore);
            mainExtend.setLimGoodModelLevel(limLevel);
            mainExtend.setCommissionScore(commissionScore);
            mainExtend.setSeckillGoods(seckillGoods);
            tytTransportMainExtendMapper.insertSelective(mainExtend);
        } else {
            mainExtend.setGoodModelScore(score);
            mainExtend.setGoodModelLevel(level);
            mainExtend.setLimGoodModelScore(limScore);
            mainExtend.setLimGoodModelLevel(limLevel);
            mainExtend.setCommissionScore(commissionScore);
            mainExtend.setSeckillGoods(seckillGoods);
            mainExtend.setModifyTime(new Date());
            tytTransportMainExtendMapper.updateByPrimaryKeySelective(mainExtend);
        }
    }

    /**
     * 添加货源曝光记录 V6300
     *
     * @param transport
     */
    private void addExposurePermissionUsedRecord(Transport transport) {
        try {
            //扣减刷新权益
            ExposurePermissionUsedRecord exposurePermissionUsedRecord = new ExposurePermissionUsedRecord();
            exposurePermissionUsedRecord.setUserId(transport.getUserId());
            exposurePermissionUsedRecord.setSrcMsgId(transport.getSrcMsgId());
            exposurePermissionUsedRecord.setStartPoint(transport.getStartPoint());
            exposurePermissionUsedRecord.setDestPoint(transport.getDestPoint());
            exposurePermissionUsedRecord.setTaskContent(transport.getTaskContent());
            exposurePermissionUsedRecord.setCtime(new Date());
            exposurePermissionService.saveExposurePermissionUsedRecord(exposurePermissionUsedRecord);
        } catch (Exception e) {
            logger.error("添加货源曝光记录异常:userId={},secMsgId={}", transport.getUserId(), transport.getSrcMsgId());
        }
    }

}
