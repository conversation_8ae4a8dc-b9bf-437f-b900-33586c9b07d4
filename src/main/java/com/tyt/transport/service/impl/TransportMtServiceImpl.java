package com.tyt.transport.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytBubbleService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.GoodsDetailOrderBean;
import com.tyt.infofee.bean.ListDataBean;
import com.tyt.model.TransportInsurance;
import com.tyt.model.TransportMt;
import com.tyt.model.User;
import com.tyt.transport.querybean.TransportMtBean;
import com.tyt.transport.service.TransportMtService;
import com.tyt.transport.service.TransportService;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service("transportMtService")
public class TransportMtServiceImpl extends BaseServiceImpl<TransportMt, Long> implements TransportMtService {

	public static Logger logger = LoggerFactory.getLogger(TransportMtServiceImpl.class);

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "tytBubbleService")
	TytBubbleService tytBubbleService;

	@Resource(name = "transportService")
	private TransportService transportService;

	@Resource(name = "transportMtDao")
	public void setBaseDao(BaseDao<TransportMt, Long> transportMtDao) {
		super.setBaseDao(transportMtDao);
	}

	@Resource(name="tsinsuranceService")
	private TsInsuranceService tsinsuranceService;

	public ListDataBean getTransportMtBeanList(Long userId, int queryActionType, long queryID) throws Exception {
		ListDataBean ListDataBean = new ListDataBean();
		List<Object> list = new ArrayList<Object>();
		int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");
		String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
		String endTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), 1)) + " 00:00:00";
		// 3个月之前的时间
		String threeMTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), -90)) + " 00:00:00";

		StringBuffer sql = new StringBuffer("select id,ts_order_no tsOrderNo,ts_id tsId,user_id userId,start_point startPoint,dest_point destPoint,start_provinc startProvinc,start_city startCity,start_area startArea,dest_provinc destProvinc,dest_city destCity,dest_area destArea,task_content taskContent,is_info_fee isInfoFee,ctime publishTime,stop_time stopTime,closing_time loadTime,release_time releaseTime,status FROM tyt_transport_mt  where 1=1 ");
		User u=userService.getById(userId);
		sql.append(" and shipper_phone=?");
		list.add(u.getCellPhone());

		sql.append(" and (status=?");
		sql.append(" or status=? or status=?)");
		list.add("1");
		list.add("4");
		list.add("5");
		sql.append(" and ctime>?");
		list.add(threeMTime);
		sql.append(" and ctime<=?");
		list.add(endTime);


		if (queryActionType == 2) {
			sql.append(" and id<?");
			list.add(queryID);
			sql.append(" order by id desc ");
		}else
		sql.append(" order by ctime desc ");

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("tsOrderNo", Hibernate.STRING);
		scalarMap.put("tsId", Hibernate.LONG);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("startPoint", Hibernate.STRING);
		scalarMap.put("destPoint", Hibernate.STRING);
		scalarMap.put("taskContent", Hibernate.STRING);
		scalarMap.put("isInfoFee", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("stopTime", Hibernate.TIMESTAMP);
		scalarMap.put("loadTime", Hibernate.TIMESTAMP);
		scalarMap.put("releaseTime", Hibernate.TIMESTAMP);
		scalarMap.put("status", Hibernate.INTEGER);		
		scalarMap.put("startProvinc", Hibernate.STRING);
		scalarMap.put("startCity", Hibernate.STRING);
		scalarMap.put("startArea", Hibernate.STRING);
		scalarMap.put("destProvinc", Hibernate.STRING); 
		scalarMap.put("destCity", Hibernate.STRING);
		scalarMap.put("destArea", Hibernate.STRING);
		
		
		List<TransportMtBean> twbl = this.getBaseDao().search(sql.toString(), scalarMap, TransportMtBean.class, list.toArray(), 1, pageSize);
		if (twbl != null && twbl.size() > 0) {
			for (TransportMtBean transportMtBean : twbl) {
				//货物信息Id
				Long tsId =  transportMtBean.getTsId();
				// 获取最新的保单
				TransportInsurance transportInsurance = tsinsuranceService.queryInsuranceByTsId(userId, tsId);
				//最后一次购买保险的保单信息
				if(transportInsurance != null)
				{
					//是否购买过保险 1是(显示“查看保单”按钮)
					transportMtBean.setIsBuyInsurance(1);
					//最后一次购买保险的保单Id
					Long insuranceId = transportInsurance.getId();
					if(insuranceId != null)
					{
						transportMtBean.setInsuranceId(insuranceId);
					}
					//最后一次购买保险的保单状态 0待支付 1已生效 2已退保
					Integer status = transportInsurance.getStatus();
					if(status != null)
					{
						transportMtBean.setInsuranceStatus(status);
					}
				}else{
					//是否购买过保险  2否(显示“买货运险”按钮)
					transportMtBean.setIsBuyInsurance(2);
				}
			}
			ListDataBean.setData(twbl);
		}
		
		ListDataBean.setBubbleNumbers(tytBubbleService.getInfoFeeMyPublishBubbleResultBeanForUserId(userId));
		ListDataBean.setCurrentTime(System.currentTimeMillis());
		return ListDataBean;
	}

	@Override
	public TransportMt getTransportMtBySrcMsgId(Long srcMsgId) {
		List<TransportMt> transportMts = this.getBaseDao().find("from TransportMt t where t.srcMsgId = ? ",srcMsgId);
		return (transportMts!=null&&transportMts.size()>0)?transportMts.get(0):null;
	}
	@Override
	public List<TransportMt> getAgreeList(Long userId) {
		String sql = "select * from tyt_transport_mt where status = 4 and shipper_phone=? and ctime > ?";
		User user= null;
		try {
			user = userService.getByUserId(userId);
		} catch (Exception e) {
			logger.error("用户不存在，userId="+userId, e);
			return null;
		}
		Date date = null;
		try {
			date = TimeUtil.parseString(TimeUtil.formatDate(TimeUtil.addDay(-3)));
		} catch (Exception e) {
			e.printStackTrace();
		}
		Integer pageSize = AppConfig.getIntProperty("transport.insur.import.page.size");
		final Object[] params = {
				user.getCellPhone(),
				date
		};
		List<TransportMt> transportMts = this.getBaseDao().search(sql, params, 0, pageSize);
		return transportMts;
	}

	@Override
	public void updatePayNum(String srcMsgId, int totalPaySucCount) {
		String sql = "";
		Object[] params = null;
		if (totalPaySucCount == 1) {
			sql = "UPDATE tyt.`tyt_transport_mt` set more_pay=more_pay-1, first_pay_user_id=?,first_pay_money=?,before_phone=?,info_status=?,mtime=? WHERE src_msg_id=? and status!=?";
			params = new Object[] { null, null, null, 0,new Date(), srcMsgId, 0 };
		} else {
			sql = "UPDATE tyt.`tyt_transport_mt` set more_pay=more_pay-1 ,mtime=? WHERE src_msg_id=? and status!=?";
			params = new Object[] { new Date(),srcMsgId, 0 };
		}
		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public void updateAgreeInfo(GoodsDetailOrderBean orderBean, String srcMsgId) {
		String sql = "UPDATE `tyt_transport_mt` set `first_pay_money`=?, `before_phone`=?, `first_pay_user_id`=?,mtime=? WHERE `src_msg_id`=? and `status`!=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { orderBean.getPayAgencyMoney(), orderBean.getCarOwnerTelephone(), orderBean.getCarOwnerUserId(),new Date(), srcMsgId, 0 });
	}

}
