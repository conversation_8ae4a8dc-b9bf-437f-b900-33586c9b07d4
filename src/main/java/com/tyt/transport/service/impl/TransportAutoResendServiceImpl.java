package com.tyt.transport.service.impl;

import com.tyt.mybatis.mapper.TransportAutoResendRecordMapper;
import com.tyt.mybatis.mapper.TransportResendLogMapper;
import com.tyt.plat.entity.base.TransportAutoResendRecordDO;
import com.tyt.plat.entity.base.TransportResendLogDO;
import com.tyt.transport.service.TransportAutoResendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 货源自动重发service
 *
 * <AUTHOR>
 * @since 2024/12/12 20:14
 */
@Slf4j
@Service
public class TransportAutoResendServiceImpl implements TransportAutoResendService {

    @Autowired
    private TransportAutoResendRecordMapper transportAutoResendRecordMapper;
    @Autowired
    private TransportResendLogMapper transportResendLogMapper;

    /**
     * 查询当前货源是否已设置自动重发
     *
     * @param srcMsgId
     */
    @Override
    public boolean isAutoResend(Long srcMsgId) {
        TransportAutoResendRecordDO queryDO = new TransportAutoResendRecordDO();
        queryDO.setOldSrcMsgId(srcMsgId);
        return transportAutoResendRecordMapper.selectOne(queryDO) != null;
    }

    /**
     * 添加货源自动重发记录
     *
     * @param userId   userId
     * @param srcMsgId 要重发的货源id
     */
    @Async
    @Override
    public void addAutoResendRecord(Integer isAutoResend, Long userId, Long srcMsgId) {
        TransportAutoResendRecordDO queryDO = new TransportAutoResendRecordDO();
        queryDO.setOldSrcMsgId(srcMsgId);
        TransportAutoResendRecordDO existOne = transportAutoResendRecordMapper.selectOne(queryDO);

        if (Objects.equals(isAutoResend, 1)) {
            // 如果勾选了自动重发，表里不存在就插入
            if (existOne == null) {
                TransportAutoResendRecordDO autoResendRecordDO = new TransportAutoResendRecordDO();
                autoResendRecordDO.setUserId(userId);
                autoResendRecordDO.setOldSrcMsgId(srcMsgId);
                transportAutoResendRecordMapper.insertSelective(autoResendRecordDO);
            }
        } else {
            // 如果取消了勾选，但表里有数据，删除数据
            if (existOne != null) {
                transportAutoResendRecordMapper.deleteByPrimaryKey(existOne.getId());
            }
        }
    }

    /**
     * 添加货源重发日志
     *
     * @param oldSrcMsgId 旧货源id
     * @param newSrcMsgId 新货源id
     * @param resendType  重发类型 1自动重发；2手动重发
     */
    @Async
    @Override
    public void addResendLog(Long oldSrcMsgId, Long newSrcMsgId, Integer resendType) {
        TransportResendLogDO logDO = new TransportResendLogDO();
        logDO.setOldSrcMsgId(oldSrcMsgId);
        logDO.setNewSrcMsgId(newSrcMsgId);
        logDO.setPublishTime(new Date());
        logDO.setResendType(resendType);
        transportResendLogMapper.insertSelective(logDO);
    }
}
