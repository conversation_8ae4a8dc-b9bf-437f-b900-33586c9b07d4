package com.tyt.transport.service.impl;

import com.tyt.model.User;
import com.tyt.plat.entity.base.TytMachineTypeAudit;
import com.tyt.plat.mapper.base.TytMachineTypeAuditMapper;
import com.tyt.transport.enums.MachineTypeEnum;
import com.tyt.transport.service.TytMachineTypeAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @author: helian
 * @since: 2023/11/06 19:49
 */
@Service("tytMachineTypeAuditService")
public class TytMachineTypeAuditServiceImpl implements TytMachineTypeAuditService {

    @Autowired
    private TytMachineTypeAuditMapper machineTypeAuditMapper;

    @Override
    public void addMachineTypeAudit(String taskContent, User user) {
        TytMachineTypeAudit machineTypeAudit = machineTypeAuditMapper.selectByShowName(taskContent);
        if (machineTypeAudit == null) {
            machineTypeAudit = TytMachineTypeAudit.builder()
                    .showName(taskContent)
                    .status(MachineTypeEnum.audit.code)
                    .conformanceCounts(0)
                    .illegalCounts(0)
                    .createName(user.getUserName())
                    .createTime(new Date())
                    .modifyName(user.getUserName())
                    .modifyTime(new Date())
                    .build();
            machineTypeAuditMapper.insert(machineTypeAudit);
        } else {
            machineTypeAudit.setStatus(MachineTypeEnum.audit.code);
            machineTypeAudit.setModifyTime(new Date());
            machineTypeAudit.setModifyName(user.getUserName());
            machineTypeAuditMapper.updateByPrimaryKey(machineTypeAudit);
        }

    }
}
