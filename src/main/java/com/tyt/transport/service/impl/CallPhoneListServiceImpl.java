package com.tyt.transport.service.impl;

import com.tyt.activate.bean.ActivateCheckBean;
import com.tyt.activate.service.TytActivateConfigService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.bean.SimpleOrderBean;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.marketingActivity.bean.ActivityEnum;
import com.tyt.marketingActivity.bean.SpecialMarketActivityCheckBean;
import com.tyt.marketingActivity.service.SpecialActivityPopupService;
import com.tyt.model.*;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.mapper.base.TytSpecialCarDispatchDetailMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.enums.YesOrNoEnum;
import com.tyt.transport.querybean.CallPhoneSearchResultBean;
import com.tyt.transport.querybean.CellPhoneListBean;
import com.tyt.transport.service.CallPhoneListService;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TytCarOwnerQueryPhoneService;
import com.tyt.user.service.*;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

@Slf4j
@Service("callPhoneListService")
public class CallPhoneListServiceImpl extends BaseServiceImpl<Transport, Long> implements CallPhoneListService {

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityAuthService;
    @Resource(name = "userCallPhoneService")
    private UserCallPhoneService userCallPhoneService;
    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;
    @Resource(name = "tytUserCallPhoneRecordService")
    private TytUserCallPhoneRecordService tytUserCallPhoneRecordService;
    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;
    @Resource(name = "transportOrdersService")
    TransportOrdersService transportOrdersService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name="tytNoticePopupTemplService")
    TytNoticePopupTemplService noticePopupTemplService;
    @Resource(name = "tytUserCallPhoneRecordService")
    private TytUserCallPhoneRecordService callPhoneRecordService;
    @Resource(name = "tytUserSubService")
    private TytUserSubService userSubService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "carOwnerQueryPhoneService")
    private TytCarOwnerQueryPhoneService carOwnerQueryPhoneService;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;
    @Resource(name = "specialActivityPopupService")
    private SpecialActivityPopupService specialActivityPopupService;

    @Autowired
    private TytActivateConfigService tytActivateConfigService;

    @Autowired
    private TytSpecialCarDispatchDetailMapper specialCarDispatchDetailMapper;


    @Override
    @Resource(name = "transportDao")
    public void setBaseDao(BaseDao<Transport, Long> transportDao) {
        super.setBaseDao(transportDao);
    }

    @Override
    public CallPhoneSearchResultBean addGetCallPhoneList(String goodId, String moduleType,String path, BaseParameter base) throws Exception{
        CallPhoneSearchResultBean resultBean = new CallPhoneSearchResultBean();
        //是否身份认证
        TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(base.getUserId()));
        Transport transport = transportBusiness.getByGoodsIdForUnLock(Long.valueOf(goodId));
        if(null == transport || null == transport.getPublishType() || (transport.getPublishType() != Transport.BUY_IT_NOW && Transport.PHONE_CONFERENCE != transport.getPublishType()) ){
            resultBean.setCode(CallPhoneSearchResultBean.PARAM_ERROR_CODE);
            return resultBean;
        }
        // 判断当前运单是否是一口价订单，如果是一口价订单，则需要判断用户是否支付过该订单，只有支付过一口价货源的用户才能查看手机号
        if(transport.getPublishType() == Transport.BUY_IT_NOW && 0 != Integer.parseInt(tytConfigService.getStringValue(Constant.check_one_mark_price, "1"))){
            // 判断当前用户是否支付过该订单
            TytTransportOrders transportOrders = transportOrdersService.userIsPay(transport.getSrcMsgId(), base.getUserId());
            if(null == transportOrders){
                resultBean.setCode(CallPhoneSearchResultBean.UNPAID_CODE);
                return resultBean;
            }
        }
        if (identityAuth == null || identityAuth.getIdentityStatus() != 1) {
            // 6560 未实名认证通过用户，获得N次免认证拨打的机会
            // 未认证用户N次豁免拨打开关，0关闭，1奇数开启，2全部开启
            Integer unAuthUserCallExemptSwitch = tytConfigService.getIntValue("unauth_user_call_exempt_switch", 0);
            if (unAuthUserCallExemptSwitch != 0) {
                boolean hasCallExempt = unAuthUserCallExemptSwitch == 1 && base.getUserId() % 2 == 1 || unAuthUserCallExemptSwitch == 2;
                log.info("未实名认证N次拨打豁免，是否能豁免：{}，userId:{}", hasCallExempt, base.getUserId());
                // 豁免的货源id如果之前拨打过，继续豁免
                TytUserCallPhoneRecord record = tytUserCallPhoneRecordService.getPhoneBySrcMsgIdAndUserId(transport.getSrcMsgId(), base.getUserId().toString());
                boolean isExempt;
                if (record != null) {
                    log.info("未实名认证N次拨打豁免，货源已豁免，userId:{}, goodsId: {}", base.getUserId(), goodId);
                    isExempt = true;
                } else {
                    int count = callPhoneRecordService.getDistinctTsCountByUserId(base.getUserId());
                    // 新增开关配置，配置豁免次数N，hasCallExempt=true是实验组，false返回对照组
                    Integer registerGetCallCount = tytConfigService.getIntValue(hasCallExempt ? "unauth_user_call_exempt_nums" : "unauth_user_call_exempt_nums_fix", 0);
                    log.info("未实名认证N次拨打豁免，豁免次数：{}，已拨打次数：{},userId:{}", registerGetCallCount, count, base.getUserId());
                    isExempt = count < registerGetCallCount;
                }
                if (isExempt) {
                    CellPhoneListBean tel = getListBean(transport, moduleType, base, path);
                    resultBean.setCellPhoneListBean(tel);
                    resultBean.setCode(ReturnCodeConstant.OK);
                    resultBean.setUnAuthUserCallExempt(1);
                    // 获取成功，记录拨打记录
                    if (record == null) {
                        TytUserSub userSub = userSubService.getTytUserSubByUserId(base.getUserId());
                        callPhoneRecordService.addPhoneRecord(transport.getSrcMsgId(), userSub.getUserGroup(), base.getUserId(), path, base.getClientSign());
                    }
                    return resultBean;
                }
            }

            //记录拨打日志 tyt_user_call_phone
            userCallPhoneService.addGetPhone(transport.getSrcMsgId().toString(),base.getUserId(),base.getClientSign(),base.getClientVersion(),moduleType,1,path);
            //提醒认证弹框
            TytNoticePopupTempl templ;
            if (Constant.ClientSignNewEnum.isClientSignEnumcode(Integer.parseInt(base.getClientSign()))){
                templ = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.未实名认证,null);
            }else{
                templ = tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.未认证身份,null);
            }
            resultBean.setNoticePopup(templ);
            // resultBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_IDENTITY_ERROR);
            return resultBean;
        }
        //查询货物电话信息 拨打记录
        //查询拨打记录
        TytUserCallPhoneRecord record = tytUserCallPhoneRecordService.getPhoneBySrcMsgIdAndUserId(transport.getSrcMsgId(),base.getUserId().toString());
        boolean hasCalled = (record != null);
        //拨打过，返回电话列表
        if (hasCalled){
            CellPhoneListBean tel = getListBean(transport,moduleType,base,path);
            resultBean.setCellPhoneListBean(tel);
            resultBean.setCode(ReturnCodeConstant.OK);
            return resultBean;
        }

        Integer count = specialCarDispatchDetailMapper.selectCountByUserAndGoodsId(transport.getSrcMsgId(), base.getUserId());

        //查询鉴权
        PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.拨打货源电话,base.getUserId());
        //促活车次卡用完提醒
        ActivateCheckBean activateCheckBean = tytActivateConfigService.checkActivate(base.getUserId(),1,permissionResult.getUse());
        //无权益
        if (!permissionResult.getUse() && count<=0){
            //记录拨打日志 tyt_user_call_phone
            userCallPhoneService.addGetPhone(transport.getSrcMsgId().toString(),base.getUserId(),base.getClientSign(),base.getClientVersion(),moduleType,1,path);
            //无权益提醒弹框
                //判断版本号
                if (Integer.parseInt(base.getClientVersion())<6110){
                    TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(),null);
                    resultBean.setNoticePopup(noticePopupTempl);
                    resultBean.setCode(ReturnCodeConstant.NO_PERMISSION);
                    return resultBean;
                }
                if (activateCheckBean.isActivate()){
                    resultBean.setNoticePopup(activateCheckBean.getTemplBean());
                    resultBean.setCode(ReturnCodeConstant.NO_PERMISSION);
                    return resultBean;
                }

                SpecialMarketActivityCheckBean checkBean = specialActivityPopupService.checkUserIsHave3CarTimes(base.getUserId(),1, ActivityEnum.促活车次卡活动);
                if (checkBean.isHave3Times()){
                    TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(PopupTypeEnum.促活找货次卡用完提醒,null);
                    resultBean.setNoticePopup(noticePopupTempl);
                    resultBean.setCode(ReturnCodeConstant.NO_PERMISSION);
                    return resultBean;
                }

                if (StringUtils.isNotBlank(checkBean.getIsCar()) && !"1".equals(checkBean.getIsCar())){
                    TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(PopupTypeEnum.促活车辆认证提醒,null);
                    resultBean.setNoticePopup(noticePopupTempl);
                    resultBean.setCode(ReturnCodeConstant.NO_PERMISSION);
                    return resultBean;
                }
                TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(),null);
                resultBean.setNoticePopup(noticePopupTempl);
                resultBean.setCode(ReturnCodeConstant.NO_PERMISSION);
                return resultBean;

        } else {
            // 获取电话 记录成功日志tyt_user_call_phone
            CellPhoneListBean tel = getListBean(transport,moduleType,base,path);
            if (permissionResult.getUse() && permissionResult.getPopupTypeEnum() != null && permissionResult.getType() == 1) {
                String param = "找货会员";
                // 需要提醒调用提醒模板，
                TytNoticePopupTempl noticePopupTempl = noticePopupTemplService.getTemplByType(permissionResult.getPopupTypeEnum(), param);
                resultBean.setNoticePopup(noticePopupTempl);
            }

            // 返回电话信息，
            resultBean.setCellPhoneListBean(tel);
            resultBean.setCode(ReturnCodeConstant.OK);
            //有权益 记录用户和货源关系记录tyt_user_call_phone_record
            TytUserSub userSub = userSubService.getTytUserSubByUserId(base.getUserId());
            callPhoneRecordService.addPhoneRecord(tel.getSrcMsgId(),userSub.getUserGroup(),base.getUserId(),path,base.getClientSign());
            // 用户与人工派单意向记录tyt_carowner_query_phone
            // 如果是人工派单货源则记录拨打电话信息（专门用户后台人工派单列表按照车方电话过滤使用）
//            String companyAccountUserId = tytConfigService.getStringValue("tyt_company_account_user_id");
//            if (companyAccountUserId != null && companyAccountUserId.equals(tel.getUserId() + "")) {
//                saveCarOwnerQuery(tel.getSrcMsgId(),tel.getUserId(),base.getUserId());
//            }
            //如果是找货次卡 更新用户权益缓存
            if (permissionResult.getUse() && permissionResult.getType() == 1){
                String permissionKey =  Constant.USER_PERMISSION_REDIS_KEY + base.getUserId();
                RedisUtil.del(permissionKey);
                UserPermissionResult result = new UserPermissionResult();
                PermissionResult userNamePermissionResult = userPermissionService.updateAuthPermissionReturn(Permission.用户昵称显示,base.getUserId());
                if (userNamePermissionResult.getUse()){
                    result.setIsUserNamePower(1);
                }
                PermissionResult contentPermissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货物内容显示,base.getUserId());
                if (contentPermissionResult.getUse()){
                    result.setIsContentPower(1);
                }
                RedisUtil.setObject(permissionKey,result,0);
            }

            if (permissionResult.getUse() && null != permissionResult.getUsedRecordId() && permissionResult.getUsedRecordId()>0){
                updateUsedRecord(permissionResult.getUsedRecordId(),transport.getSrcMsgId());
            }

            return resultBean;
        }
    }


    /**
     * 获取电话
     * @return cellPhoneList
     * @throws Exception
     */
    private CellPhoneListBean getTel(Transport transport) throws Exception{
        String sql = "SELECT tsm.`tel`, tsm.`tel3`, tsm.`tel4`, tsm.`upload_cellphone` uploadCellPhone, tsm.`load_cell_phone` loadCellPhone,tsm.`unload_cell_phone` unloadCellPhone,tsm.`src_msg_id` srcMsgId,tsm. ts_order_no tsOrderNo,tsm.is_info_fee isInfoFee,tsm.user_id userId FROM tyt_transport_main tsm WHERE tsm.`id`=?";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("tel", Hibernate.STRING);
        scalarMap.put("tel3", Hibernate.STRING);
        scalarMap.put("tel4", Hibernate.STRING);
        scalarMap.put("uploadCellPhone", Hibernate.STRING);
        scalarMap.put("loadCellPhone", Hibernate.STRING);
        scalarMap.put("unloadCellPhone", Hibernate.STRING);
        scalarMap.put("srcMsgId", Hibernate.LONG);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("isInfoFee", Hibernate.STRING);
        scalarMap.put("userId", Hibernate.LONG);
        List<CellPhoneListBean> callLogBeans = this.getBaseDao().search(sql, scalarMap, CellPhoneListBean.class, new Object[] { transport.getSrcMsgId() });
        if (callLogBeans!=null && callLogBeans.size()>0){
            CellPhoneListBean listBean = callLogBeans.get(0);
            if (transport.getStatus() == 5) { // 撤销
                listBean.setGoodStatus(2);
            } else if (transport.getStatus() == 4) { // 成交
                    listBean.setGoodStatus(3);
            } else if (transport.getStatus() == 1 && !TimeUtil.isToday(transport.getCtime().getTime())) { // 过期
                listBean.setGoodStatus(1);
            }
            return listBean;
        }
        return null;
    }

    /**
     * 获取电话记录
     * @param userId 用户id
     * @param srcMsgId  货源id
     * @return  字符串
     */
    private CellPhoneListBean getCallRecord(Long userId,Long srcMsgId){
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("callStatus", Hibernate.STRING);
        scalarMap.put("callStatusCode", Hibernate.INTEGER);
        String sql = "SELECT tacl.`call_result_name` AS 'callStatus',tacl.`call_result_code` AS 'callStatusCode' FROM tyt_app_call_log tacl WHERE tacl.`caller_id`=? AND tacl.`src_msg_id`=?";
        List<CellPhoneListBean> callLogBeans = this.getBaseDao().search(sql, scalarMap, CellPhoneListBean.class, new Object[] { userId, srcMsgId });
        if (callLogBeans != null && callLogBeans.size()>0){
            return callLogBeans.get(0);
        }
        return null;
    }

    /**
     * 处理电话返回数据
     * @param moduleType  模块
     * @param base  基础参数
     * @return cellPhoneListBean
     * @throws Exception
     */
    private CellPhoneListBean getListBean(Transport transport,String moduleType,BaseParameter base,String path)throws Exception{
        CellPhoneListBean tel = getTel(transport);
        if (tel!=null) {
            CellPhoneListBean bean = getCallRecord(base.getUserId(), tel.getSrcMsgId());
            if (bean != null) {
                tel.setCallStatus(bean.getCallStatus() == null ? "" : bean.getCallStatus());
                tel.setCallStatusCode(bean.getCallStatusCode()==null?0:bean.getCallStatusCode());
            }
            tel.setIsCanCall(0);
            String hasMakeOrder = "0";
            if (tel.getIsInfoFee().equals("1")) {
                SimpleOrderBean myLastOrder = transportOrdersService.getLastOrderByPayUser(tel.getTsOrderNo(), base.getUserId());
                if (myLastOrder != null) {
                    hasMakeOrder = "1";
                }
            }
            tel.setHasMakeOrder(hasMakeOrder);
            this.disturbPhone(tel, base.getUserId().toString());
            //记录拨打日志 tyt_user_call_phone
            userCallPhoneService.addGetPhone(transport.getSrcMsgId().toString(), base.getUserId(), base.getClientSign(), base.getClientVersion(), moduleType, 0,path);
            return tel;
        }
        return null;
    }

    /**
     * 判断是否记录过人工派单和拨打人关系
     * @param srcMsgId
     * @param userId
     * @return
     */
    private Integer getCountForCarOwnerQuery(Long srcMsgId,Long userId){
        String sql = "SELECT COUNT(*) FROM `tyt_carowner_query_phone` WHERE `ts_src_id`=? AND `car_user_id`=?";
        BigInteger count = this.getBaseDao().query(sql,new Object[]{srcMsgId,userId});
        if (count!=null){
            return count.intValue();
        }
        return 0;
    }

    /**
     * 保存人工派单和拨打人关系
     * @param srcMsgId
     * @param goodsUserId
     * @param carUserId
     */
    private void saveCarOwnerQuery(Long srcMsgId,Long goodsUserId,Long carUserId){
        int count = getCountForCarOwnerQuery(srcMsgId,carUserId);
        // 为空才保存拨打记录
        if (count > 0) {
            TytCarOwnerQueryPhone carOwnerQueryPhone = new TytCarOwnerQueryPhone();
            User user = userService.getById(carUserId);
            carOwnerQueryPhone.setCarLoginPhone(user.getCellPhone());
            carOwnerQueryPhone.setCarUserId(carUserId);
            carOwnerQueryPhone.setCtime(new Date());
            carOwnerQueryPhone.setTsId(srcMsgId);
            carOwnerQueryPhone.setTsSrcId(srcMsgId);
            carOwnerQueryPhone.setUserId(goodsUserId);
            carOwnerQueryPhoneService.add(carOwnerQueryPhone);
        }
    }

    /**
     * 扰乱设定用户获取的电话号码。
     * @param callLogBean 电话对象
     * @param userId 用户Id
     */

    private void disturbPhone(CellPhoneListBean callLogBean,String userId){

        // 设置爬虫用户返回手机号码加扰乱码, 参数等于1时执行；
        boolean isOpenDisturb=tytConfigService.isEqualsOne(Constant.IS_DISTURB);
        if(isOpenDisturb){
            String userStr=tytConfigService.getStringValue(Constant.DISTURB_USER_ID);
            // 用户存在配置中
            if(userStr.indexOf("|"+userId+"|")>-1){
                //用户存在扰乱中则
                String disturbPhone="";
                if(StringUtils.isNotBlank(callLogBean.getTel())){
                    disturbPhone=StringUtil.disturbPhone(callLogBean.getTel());
                    callLogBean.setTel(disturbPhone);
                }

                if(StringUtils.isNotBlank(callLogBean.getTel3())){
                    disturbPhone=StringUtil.disturbPhone(callLogBean.getTel3());
                    callLogBean.setTel3(disturbPhone);
                }
                if(StringUtils.isNotBlank(callLogBean.getTel4())){
                    disturbPhone=StringUtil.disturbPhone(callLogBean.getTel4());
                    callLogBean.setTel4(disturbPhone);
                }
            }
        }
    }

    private void updateUsedRecord(Long recordId, Long srcMsgId){
        String sql = "UPDATE `tyt_user_permission_used` SET src_msg_id=? WHERE id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{srcMsgId, recordId});
    }

}
