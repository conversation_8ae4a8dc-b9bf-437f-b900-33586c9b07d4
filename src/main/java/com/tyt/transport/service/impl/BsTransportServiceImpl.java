package com.tyt.transport.service.impl;

import com.tyt.config.util.AppConfig;
import com.tyt.model.Transport;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.mapper.base.TytTransportExposureMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.shielding.service.ShieldingShipperService;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.BsTransportService;
import com.tyt.transport.service.TransportSearchService;
import com.tyt.transport.service.TransportService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytOwnerAuthService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.util.AESUtil;
import com.tyt.util.Constant;
import com.tyt.util.StringUtil;
import com.tyt.util.XXTea;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("bsTransportService")
public class BsTransportServiceImpl  implements BsTransportService {

    @Resource(name = "tytUserSubService")
    TytUserSubService tytUserSubService;
    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;
    @Resource(name = "transportSearchService")
    TransportSearchService transportSearchService;
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name = "tytOwnerAuthService")
    private TytOwnerAuthService tytOwnerAuthService;
    @Autowired
    private ShieldingShipperService shieldingShipperService;

    @Autowired
    private TytTransportExposureMapper tytTransportExposureMapper;
    @Autowired
    private TransportService transportService;
    private final static String SHIELDING_LIST_KEY_PREFIX = "SHIELDING:USERID:";

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public BoResultBean<TransportBean> getScopeSearchTransportList(ScopeSearchQueryBean bean) {
        if (bean.getStartCity().equals(bean.getStartArea())){
            bean.setStartArea("");
        }
        ScopeFallShortSqlBean sqlBean = new ScopeFallShortSqlBean();
        // 上滑动下滑动最大结果集大小
        int pageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.page.size");
        sqlBean.setPageSize(pageSize);
        long tsId=bean.getQuerySign()==null ? 0 : bean.getQuerySign();
        sqlBean.setTsId(tsId);
        BeanUtils.copyProperties(bean, sqlBean);
        long rangSize = Optional.ofNullable(AppConfig.getIntProperty("tyt.tyt_transport.range.size"))
                .orElse(100).longValue();
        String[] coord = bean.getStartCoord().split(",");
        long startCoordX = new BigDecimal(coord[0]).multiply(new BigDecimal(rangSize)).longValue();
        long startCoordY = new BigDecimal(coord[1]).multiply(new BigDecimal(rangSize)).longValue();
        sqlBean.setStartCoordX(startCoordX);
        sqlBean.setStartCoordY(startCoordY);
        long startDistanceValue = Long.parseLong(bean.getStartDistance());
        long defaultStartDistance = Optional.ofNullable(AppConfig.getIntProperty("tyt.tyt_transport.range"))
                .orElse(310).longValue();
        long startRange;
        if (startDistanceValue > 500 || startDistanceValue == 0){
            startRange = defaultStartDistance  * 100;
        }else{
            startRange = startDistanceValue * 100;
        }
        sqlBean.setStartRange(startRange);
        List<Transport> transportList;
        // 是否启用Els查询
        boolean isElsOpen=tytConfigService.isEqualsValue(Constant.ELS_ONOFF,1);
        if(isElsOpen){
            transportList=transportSearchService.findElsTransportForScope(sqlBean);
        }else{
            transportList=transportSearchService.findDbTransportForScope(sqlBean);
        }
        BoResultBean<TransportBean> resultBean=new BoResultBean<TransportBean>();
        resultBean.setTotalSize(transportSearchService.getTotalSize(transportList,pageSize));
        try {
            // 结果集过滤及数据数据处理
            List<TransportBean> tbList=filterTransportList(transportList,bean.getUserId(),bean.getClientVersion());
            resultBean.setResult(tbList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultBean;
    }


   @Override
   public BoResultBean<TransportBean> getProvicSearchTransportList(ProvincSearchQueryBean bean) {

        // 上滑动下滑动最大结果集大小
        int pageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.page.size");
        long tsId=bean.getQuerySign()==null ? 0 : bean.getQuerySign();

        List<Transport> transportList;
         // 是否启用Els查询
         boolean isElsOpen=tytConfigService.isEqualsValue(Constant.ELS_ONOFF,1);
         if(isElsOpen){
             transportList=transportSearchService.findElsTransportForProvince(bean.getStartProvinc(),bean.getQueryType(),tsId,pageSize);
         }else{
             transportList=transportSearchService.findDbTransportForProvince(bean.getStartProvinc(),bean.getQueryType(),tsId,pageSize);
         }
         BoResultBean<TransportBean> resultBean=new BoResultBean<TransportBean>();
         resultBean.setTotalSize(transportSearchService.getTotalSize(transportList,pageSize));

        try {
            // 结果集过滤及数据数据处理
            List<TransportBean> tbList=filterTransportList(transportList,bean.getUserId(),bean.getClientVersion());
            resultBean.setResult(tbList);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultBean;
    }

    @Override
    public BoResultBean<TransportBean> getTransportList(TransportSearchBean bean) {
        TransportSearchSqlBean sqlBean = new TransportSearchSqlBean();
        int pageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.page.size");
        sqlBean.setPageSize(pageSize);
        long tsId=bean.getQuerySign()==null ? 0 : bean.getQuerySign();
        sqlBean.setTsId(tsId);
        BeanUtils.copyProperties(bean, sqlBean);
        // 获取当前用户屏蔽的发货人列表
        Map<String, String> shieldingShipperMap = RedisUtil.getMap(SHIELDING_LIST_KEY_PREFIX + bean.getUserId());
        Set<String> shieldingShipperSet = new HashSet<>();
        if(null != shieldingShipperMap && !shieldingShipperMap.isEmpty()){
            shieldingShipperSet = shieldingShipperMap.keySet();
        }
        dealCoord(bean,sqlBean);
        sqlBean.setShieldingShipperSet(shieldingShipperSet);
        List<Transport> transportList;
        // 是否启用Els查询
        boolean isElsOpen=tytConfigService.isEqualsValue(Constant.ELS_ONOFF,1);
        if(isElsOpen){
            long esStartTime = System.currentTimeMillis();
            transportList=transportSearchService.findElsTransportSearch(sqlBean);
            long esEndTime = System.currentTimeMillis();
            logger.info("ES查询耗时:" + (esEndTime - esStartTime) + "ms");
        }else{
            long dbStartTime = System.currentTimeMillis();
            transportList=transportSearchService.findDbTransportSearch(sqlBean);
            long dbEndTime = System.currentTimeMillis();
            logger.info("DB查询耗时:" + (dbEndTime - dbStartTime) + "ms");
        }
        BoResultBean<TransportBean> resultBean=new BoResultBean<>();
        resultBean.setTotalSize(transportSearchService.getTotalSize(transportList,pageSize));
        try {
            // 结果集过滤及数据数据处理
            List<TransportBean> tbList = filterTransportList(transportList,bean.getUserId(),bean.getClientVersion());
            resultBean.setResult(tbList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultBean;
    }

    /**
     * List<Transport> 转为List<TransportBean>t，设置昵称、及加密；
     * @param sourceList List<Transport> 源数据
     * @param userId  用户ID
     */
    @Override
    public List<TransportBean> filterTransportList(List<Transport> sourceList, Long userId, String clientVersion){

        if(CollectionUtils.isEmpty(sourceList)){
            return new ArrayList<>();
        }

        long defaultUserId = Long.parseLong(tytConfigService.getStringValue("tyt_transport_default_user_id", "4"));

         //是否需求加密
         boolean isNeedEncypt = tytConfigService.getIntValue(Constant.IS_NEED_ENCYPT_KEY, 1) == 1;
         //调用鉴权
         boolean isNamePerssion = false;
         boolean isContentPerssion = false;
         if (userId != null) {
             PermissionResult namePermissionResult = userPermissionService.updateAuthPermissionReturn(Permission.用户昵称显示, userId);
             isNamePerssion = namePermissionResult.getUse();
             PermissionResult goodPermissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货物内容显示, userId);
             isContentPerssion = goodPermissionResult.getUse();
         }

         boolean isHideNickName = !isNamePerssion;
         boolean isHideContent = !isContentPerssion;

         return sourceList.stream().map(transport -> {
             TransportBean tb = new TransportBean();
             BeanUtils.copyProperties(transport, tb);
             String newNickName = StringUtil.hidePhoneInStr(tb.getNickName());
             tb.setNickName(newNickName);
             tb.setRemark(null);
             tb.setPubDate(transport.getCtime());
             tb.setTotalScore(transport.getTotalScore());
             tb.setRankLevel(transport.getRankLevel());
             tb.setUserId(defaultUserId);
             tb.setStartLatitudeStr(transport.getStartLatitude());
             tb.setStartLongitudeStr(transport.getStartLongitude());

             // 无拨打电话权限用户  设置昵称***
             dealContentAndNickName(tb, isHideNickName, isHideContent);

             // 昵称加密处理
             if (isNeedEncypt) {
                 tb.setIsNeedDecrypt(1);
                 if (StringUtils.isNotBlank(tb.getNickName())) {
                     String nickName = tb.getNickName();

                     tb.setNickName(XXTea.Encrypt(nickName,
                             AppConfig.getProperty("tyt.xxtea.key")));
                     tb.setNickNameByAes(AESUtil.enCode(nickName,  AppConfig.getProperty("tyt.aes.key")));
                     if(!isHideNickName){
                         // 官方授权账号
                         String authNameTea = tytOwnerAuthService.getAuthNameTea(transport.getAuthName());
                         if(StringUtils.isNotBlank(authNameTea)){
                             tb.setAuthNameTea(authNameTea);
                             tb.setAuthName(null);
                         }
                     }
                 } else {
                     tb.setIsNeedDecrypt(2);
                 }
             }

             return tb;
         }).collect(Collectors.toList());

    }

    @Override
    public BoResultBean getDestTransportList(DestTransportSearchBean bean) {
        if (bean.getStartCity().equals(bean.getStartArea())) {
            bean.setStartArea("");
        }
        Integer searchSize = tytConfigService.getIntValue(Constant.DEST_TRANSPORT_SEARCH_SIZE, 20);
        List<Transport> transportList;
        // 是否启用Els查询
        boolean isElsOpen = tytConfigService.isEqualsValue(Constant.ELS_ONOFF, 1);
        if (isElsOpen) {
            transportList = transportSearchService.findElsDestTransport(bean, searchSize);
        } else {
            transportList = transportSearchService.findDbDestTransport(bean, searchSize);
        }
        BoResultBean resultBean = new BoResultBean();
        try {
            List<TransportBean> tbList = new ArrayList<TransportBean>();

            // 结果集过滤及数据数据处理
            tbList = filterTransportList(transportList, bean.getUserId(), bean.getClientVersion());

            //急走货源处理
            Long transportId = tytTransportExposureMapper.selectMaxRankLevelByDestTransportSearchBean(bean);
            if (Objects.nonNull(transportId)) {
                Transport transport = transportService.getById(transportId);
                if (Objects.nonNull(transport)) {
                    List<TransportBean> transportBeans = filterTransportList(Collections.singletonList(transport), bean.getUserId(), bean.getClientVersion());
                    if (CollectionUtils.isNotEmpty(transportBeans)) {
                        tbList.removeIf(transportBean -> transportBean.getSrcMsgId().equals(bean.getGoodsId()) || transportBean.getSrcMsgId().equals(transport.getSrcMsgId()));

                        TransportBean transportBean1 = transportBeans.get(0);
                        transportBean1.setExposureFlag(1);
                        tbList.add(0,transportBean1);
                    }
                }
                // 清除当前数据
            } else {
                // 清除当前数据
                tbList.removeIf(transportBean -> transportBean.getSrcMsgId().equals(bean.getGoodsId()));
            }
            resultBean.setResult(tbList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultBean;
    }

    @Override
    public BoSimilarResultBean getSimilarTransportList(SimilarTransportSearchBean bean) {
        // 获取当前用户屏蔽的发货人列表
        List<Long> filterUserIds = shieldingShipperService.getShieldingShipperFromCache(bean.getUserId());
        bean.setFilterUserIds(filterUserIds);

        List<Transport> transportList = transportSearchService.findDbSimilarTransportSearch(bean);

        BoSimilarResultBean boSimilarResultBean = new BoSimilarResultBean();
        try {
            // 结果集过滤及数据数据处理
            List<TransportBean> tbList = filterTransportList(transportList, bean.getUserId(), bean.getClientVersion());
            HashMap<Object, Object> map = new HashMap<>();
            map.put("tbList", tbList);
            map.put("rankLeveldisplay", tytConfigService.getStringValue("similarGoodsSourceStarDisplayOnOff"));
            boSimilarResultBean.setResult(map);
        } catch (Exception e) {
            logger.error("search similarity list error: ", e);
        }
        return boSimilarResultBean;
    }


    /**
     * 处理内容和昵称
     * @param tb
     * @param isChangNickName
     * @param isChangContent
     */
    private void dealContentAndNickName(TransportBean tb,boolean isChangNickName,boolean isChangContent){
        String similarityFirstInfo = tb.getSimilarityFirstInfo();
        String[] firstInfo = null;
        if (StringUtils.isNotBlank(similarityFirstInfo)) {
            firstInfo = similarityFirstInfo.split("##");
        }
        if (isChangNickName){
            tb.setNickName(Constant.REPLACEMENT_STARTS);
            if(firstInfo != null) {
                firstInfo[1] = Constant.REPLACEMENT_STARTS;
            }
        }
        if (isChangContent){
            tb.setTaskContent(Constant.REPLACEMENT_STARTS_CONTENT);
            tb.setWeight(null);
            tb.setCarLength(null);
            tb.setCarType(null);
            tb.setSpecialRequired(null);
            if(firstInfo != null) {
                firstInfo[0] = Constant.REPLACEMENT_STARTS_CONTENT;
            }
        }
        if(!isChangNickName){
            if (tb.getNickName().indexOf("" + tb.getUserId()) != -1) {
                // 昵称有包含用户ID 进行修改设置
                tb.setNickName("用户...");
            }
        }
        tb.setSimilarityFirstInfo(StringUtils.join(firstInfo, "##"));
    }
    /**
     * 处理坐标和范围
     * @param bean 条件bean
     * @param sqlBean sqlbean
     */
    private void dealCoord(TransportSearchBean bean,TransportSearchSqlBean sqlBean){
        long rangSize = Optional.ofNullable(AppConfig.getIntProperty("tyt.tyt_transport.range.size"))
                .orElse(100)
                .longValue();
        String[] startCoord = bean.getStartCoord().split(",");
        long startCoordX = new BigDecimal(startCoord[0]).multiply(new BigDecimal(rangSize)).longValue();
        long startCoordY = new BigDecimal(startCoord[1]).multiply(new BigDecimal(rangSize)).longValue();
        sqlBean.setStartCoordX(startCoordX);
        sqlBean.setStartCoordY(startCoordY);
        long startDistanceValue = Long.parseLong(bean.getStartDistance());
        long defaultStartDistance = Optional.ofNullable(AppConfig.getIntProperty("tyt.tyt_transport.range"))
                .orElse(310)
                .longValue();
        long startRange;
        if (startDistanceValue > 500 || startDistanceValue == 0){
            startRange = defaultStartDistance  * 100;
        }else{
            startRange = startDistanceValue * 100;
        }
        sqlBean.setStartRange(startRange);
        if (StringUtils.isNotBlank(bean.getDestCoord())){
            String[] destCoord = bean.getDestCoord().split(",");
            long destCoordX = new BigDecimal(destCoord[0]).multiply(new BigDecimal(rangSize)).longValue();
            long destCoordY = new BigDecimal(destCoord[1]).multiply(new BigDecimal(rangSize)).longValue();
            sqlBean.setDestCoordX(destCoordX);
            sqlBean.setDestCoordY(destCoordY);
            long destDistanceValue = Long.parseLong(bean.getDestDistance());
            long destRang;
            if (destDistanceValue > 500 || destDistanceValue == 0){
                destRang = defaultStartDistance  * 100;
            }else{
                destRang = destDistanceValue * 100;
            }
            sqlBean.setDestRange(destRang);
        }

    }
}
