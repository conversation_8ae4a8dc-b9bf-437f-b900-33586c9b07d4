package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Geo;
import com.tyt.transport.service.GeoService;
import com.tyt.user.dao.UserDao;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.sql.Timestamp;
import java.util.List;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:10
 */
@Service("geoService")
public class GeoServiceImpl extends BaseServiceImpl<Geo,Long> implements GeoService {

    @Resource(name="geoDao")
    public void setBaseDao(BaseDao<Geo, Long> geoDao) {
        super.setBaseDao(geoDao);
    }

    
}
