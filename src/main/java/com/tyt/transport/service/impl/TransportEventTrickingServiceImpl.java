package com.tyt.transport.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSONObject;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.plat.enums.TransportPublishSceneEnum;
import com.tyt.plat.enums.TransportStatusEnum;
import com.tyt.plat.vo.ts.SaveDirectReq;
import com.tyt.plat.vo.ts.TransportBIDataJson;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.TransportPublishBean;
import com.tyt.transport.service.TransportEventTrickingService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TytCoverGoodsDialConfigService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 货源数据埋点类
 *
 * <AUTHOR>
 * @since 2024/05/16 16:53
 */
@Slf4j
@Service
@AllArgsConstructor
public class TransportEventTrickingServiceImpl implements TransportEventTrickingService {

    private final ThreadPoolExecutor threadPoolExecutor;
    private final TransportMainService transportMainService;
    private final TytCoverGoodsDialConfigService tytCoverGoodsDialConfigService;


    /**
     * 发布货源/编辑货源埋点
     */
    @Override
    public void publishTransport(Transport transport, TransportLabelJson labelJson, TransportPublishBean publishBean) {
        threadPoolExecutor.execute(() -> {
            TransportBIDataJson transportBiDataJson = new TransportBIDataJson();
            EventType eventType = publishBean.getTsId() == null || publishBean.getTsId() == 0L ?
                    EventType.FIRST_PUBLISH : EventType.EDIT_PUBLISH;
            transportBiDataJson.setPublishType(eventType.getCode());

            this.saveSomeBiNeedDataResult(eventType, transport.getSrcMsgId(), transportBiDataJson);
        });
    }

    /**
     * 直接发布埋点。加价、转一口价、曝光都会进来
     */
    @Override
    public void directPublish(Transport transport, TransportLabelJson labelJson, SaveDirectReq saveDirectReq) {
        threadPoolExecutor.execute(() -> {
            Integer type = saveDirectReq.getAutomaticGoodCarPriceTransportType();
            // 加价，转一口价不更新捂货时间
            if (Objects.equals(TransportPublishSceneEnum.ADD_PRICE.getCode(), type)
                    || Objects.equals(TransportPublishSceneEnum.TURN_FIXED_PRICE.getCode(), type)) {
                return;
            }

            TransportBIDataJson transportBiDataJson = new TransportBIDataJson();
            EventType eventType = EventType.DIRECT_PUBLISH;
            transportBiDataJson.setPublishType(eventType.getCode());

            this.saveSomeBiNeedDataResult(eventType, transport.getSrcMsgId(), transportBiDataJson);
        });
    }

    /**
     * 撤销发布埋点，仅保存撤销时间
     */
    @Override
    public void cancelPublish(TransportMain transportMain) {
        threadPoolExecutor.execute(() -> {
            if (Objects.equals(transportMain.getStatus(), TransportStatusEnum.CANCEL.getCode())) {
                TransportBIDataJson transportBiDataJson = new TransportBIDataJson();
                this.saveSomeBiNeedDataResult(EventType.CANCEL, transportMain.getSrcMsgId(), transportBiDataJson);
            }
        });
    }

    /**
     * 存储BI需要的数据
     */
    private void saveSomeBiNeedDataResult(EventType eventType, Long srcMsgId, TransportBIDataJson biDataParam) {
        String cacheValue = "";
        try {
            String cacheKey = getCacheKey(srcMsgId);
            cacheValue = RedisUtil.get(cacheKey);

            String jsonText = "";
            if (StringUtils.isBlank(cacheValue)) {
                // 初始化捂货时间
                initXTime(eventType, srcMsgId, biDataParam);
                jsonText = biDataParam.getJsonText();

            } else {
                TransportBIDataJson transportBiDataJson = JSONObject.parseObject(cacheValue, TransportBIDataJson.class);
                if (transportBiDataJson != null) {

                    Integer oldPublishType = transportBiDataJson.getPublishType();
                    // 同类赋值，忽略null值
                    BeanUtil.copyProperties(biDataParam, transportBiDataJson, CopyOptions.create(null, true));
                    // 只记录当天首次发货类型，后面不更新
                    if (oldPublishType != null) {
                        transportBiDataJson.setPublishType(oldPublishType);
                    }
                    // 更新实际捂货时间
                    updateXTimeInActual(eventType, transportBiDataJson, srcMsgId);

                    jsonText = transportBiDataJson.getJsonText();
                }
            }

            RedisUtil.set(cacheKey, jsonText, 60 * 60 * 60);

        } catch (Exception e) {
            log.error("存储调用BI优车好货结果异常 货源ID {}, param{}, cache data {} ", srcMsgId, biDataParam.getJsonText(), cacheValue, e);
        }
    }

    /**
     * 返回埋点数据的缓存key
     */
    private String getCacheKey(Long srcMsgId) {
        return CommonUtil.joinRedisKey("tyt:cache:bi:data", srcMsgId.toString(), DateUtil.dateToString(new Date(), DateUtil.day_format_short));
    }

    /**
     * 第一次初始化捂货时间，捂货时间和实际捂货时间相等
     */
    private void initXTime(EventType eventType, Long srcMsgId, TransportBIDataJson biDataParam) {
        TransportMain transportMain = transportMainService.getBySrcMsgId(srcMsgId);
        // 捂货到期时间不为空才计算xTime
        if (transportMain != null && transportMain.getPriorityRecommendExpireTime() != null) {
            Integer xSecond = tytCoverGoodsDialConfigService.selectXTime();
            biDataParam.setXTimeInConfig(xSecond);
            biDataParam.setXTimeInActual(xSecond);
        }
    }

    /**
     * 返回实际在线捂货时间（踢掉撤销与再次发布的间隔时间）。
     *
     * @param eventType           操作类型
     * @param transportBiDataJson 缓存的埋点数据
     * @param srcMsgId            货源srcMsgId
     */
    private void updateXTimeInActual(EventType eventType, TransportBIDataJson transportBiDataJson, Long srcMsgId) {

        TransportMain transportMain = transportMainService.getBySrcMsgId(srcMsgId);
        Date expireTime; // 捂货到期时间
        if (transportMain == null || (expireTime = transportMain.getPriorityRecommendExpireTime()) == null) {
            return;
        }

        // 如果之前没初始化捂货时间，初始化
        if (transportBiDataJson.getXTimeInActual() == null) {
            Integer xSecond = tytCoverGoodsDialConfigService.selectXTime();
            transportBiDataJson.setXTimeInConfig(xSecond);
            transportBiDataJson.setXTimeInActual(xSecond);
            return;
        }

        // 撤销：如果还没到捂货到期时间就撤销了，实际捂货时间需要扣除这段时间
        if (eventType == EventType.CANCEL) {
            if (transportMain.getMtime().before(expireTime)) {
                int differ = differenceInSeconds(transportMain.getMtime(), expireTime);
                transportBiDataJson.setXTimeInActual(transportBiDataJson.getXTimeInActual() - differ);
            }
        } else if (eventType == EventType.EDIT_PUBLISH || eventType == EventType.DIRECT_PUBLISH) {
            // 编辑发布/直接发布：如果发布时还没到捂货到期时间，实际捂货时间需要加上这段时间
            if (new Date().before(expireTime)) {
                int differ = differenceInSeconds(new Date(), expireTime);
                // 实际捂货时间不会超过配置的时间，使用曝光卡可能会触发
                int xTimeInActual = Math.min(transportBiDataJson.getXTimeInActual() + differ, transportBiDataJson.getXTimeInConfig());
                transportBiDataJson.setXTimeInActual(xTimeInActual);
            }
        }
    }

    /**
     * 计算两个日期相差的秒数
     *
     * @return 相差的秒数
     */
    public static int differenceInSeconds(Date startDate, Date endDate) {
        // 获取两个日期的时间差（毫秒）
        long diffInMilliseconds = Math.abs(endDate.getTime() - startDate.getTime());
        // 将毫秒转换为秒
        return (int) TimeUnit.SECONDS.convert(diffInMilliseconds, TimeUnit.MILLISECONDS);
    }

    /**
     * 操作类型：发布、直接发布、撤销
     */
    @Getter
    @AllArgsConstructor
    enum EventType {
        // 首次发布
        FIRST_PUBLISH(1),
        // 编辑发布
        EDIT_PUBLISH(2),
        // 直接发布
        DIRECT_PUBLISH(3),
        // 撤销
        CANCEL(4);
        private final Integer code;
    }
}
