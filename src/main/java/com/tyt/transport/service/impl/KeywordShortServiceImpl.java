package com.tyt.transport.service.impl;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytKeywordShort;
import com.tyt.transport.service.KeywordShortService;

@Service("keywordShortService")
public class KeywordShortServiceImpl extends BaseServiceImpl<TytKeywordShort, Long> implements KeywordShortService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "keywordShortDao")
	public void setBaseDao(BaseDao<TytKeywordShort, Long> keywordShortDao) {
		super.setBaseDao(keywordShortDao);
	}
}
