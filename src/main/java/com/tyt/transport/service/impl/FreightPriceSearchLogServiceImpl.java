package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.FreightPriceDetailInfo;
import com.tyt.model.FreightPriceSearchLog;
import com.tyt.transport.service.FreightPriceDetailInfoService;
import com.tyt.transport.service.FreightPriceSearchLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("freightPriceSearchLogService")
public class FreightPriceSearchLogServiceImpl extends BaseServiceImpl<FreightPriceSearchLog, Long> implements FreightPriceSearchLogService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "freightPriceSearchLogDao")
	public void setBaseDao(BaseDao<FreightPriceSearchLog, Long> freightPriceSearchLog) {
		super.setBaseDao(freightPriceSearchLog);
	}

}
