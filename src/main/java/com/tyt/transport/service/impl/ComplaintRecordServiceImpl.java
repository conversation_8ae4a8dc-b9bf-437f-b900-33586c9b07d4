package com.tyt.transport.service.impl;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.User;
import com.tyt.mybatis.mapper.ComplaintMapper;
import com.tyt.transport.bean.ComplaintRecordBean;
import com.tyt.transport.service.ComplaintRecordService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/4/16 16:38
 */
@Service
public class ComplaintRecordServiceImpl implements ComplaintRecordService {
    @Autowired
    private ComplaintMapper complaintMapper;
    @Resource(name = "userService")
    UserService userService;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;
    @Override
    public ResultMsgBean addComplaintRecord(ComplaintRecordBean recordBean) throws Exception {
        ComplaintRecordBean complaintRecordBean = complaintMapper.selectComplaintRecordByMsgId(recordBean.getMsgId(), recordBean.getUserId());
        if (complaintRecordBean!=null){
            return new ResultMsgBean(ReturnCodeConstant.COMPLAINT_ALREADY,"该货源已经投诉过了");
        }
        //获取投诉人相关信息
        User complainantUser = userService.getByUserId(recordBean.getUserId());
        recordBean.setComplainantsPhone(complainantUser.getCellPhone());
        recordBean.setComplainantVersion(recordBean.getClientVersion());
        recordBean.setCreateTime(new Date());
        //获取被投诉人相关信息
        TransportMain transportMain = transportMainService.getById(recordBean.getMsgId());
        User respondentUser = userService.getByUserId(transportMain.getUserId());
        recordBean.setRespondentId(transportMain.getUserId());
        recordBean.setRespondentPone(respondentUser.getCellPhone());
        recordBean.setRespondentVersion(transportMain.getClientVersion());
        complaintMapper.saveComplaint(recordBean);
        //货源投诉次数记录
        Integer msgIdCount = complaintMapper.selectComplaintTransportByMsgId(recordBean.getMsgId());
        if(msgIdCount!=null){
            complaintMapper.updateComplaintTransportByMsgId(recordBean.getMsgId());
        }else {
            complaintMapper.insertComplaintTransport(recordBean.getMsgId());
        }
        //被投诉人次数记录
        Integer userIdCount = complaintMapper.selectComplaintUserByUserId(recordBean.getRespondentId());
        if (userIdCount!=null){
            complaintMapper.updateComplaintUserByUserId(recordBean.getRespondentId());
        }else {
            complaintMapper.insertComplaintUser(recordBean.getRespondentId());
        }
        return new ResultMsgBean(200,"操作成功");
    }

    @Override
    public ComplaintRecordBean getComplaintRecordByMsgId(Long msgId,Long userId) {
        return complaintMapper.selectComplaintRecordByMsgId(msgId,userId);
    }


}
