package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCarriageFeedback;
import com.tyt.transport.querybean.CarriageFeedbackBean;
import com.tyt.transport.service.TytCarriageFeedbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @ClassName TytCarriageFeedbackServiceImpl
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/9/15 13:05
 * @Verdion 1.0
 **/
@Service("tytCarriageFeedbackService")
public class TytCarriageFeedbackServiceImpl extends BaseServiceImpl<TytCarriageFeedback, Long> implements TytCarriageFeedbackService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytCarriageFeedbackDao")
    public void setBaseDao(BaseDao<TytCarriageFeedback, Long> tytCarriageFeedbackDao){
        super.setBaseDao(tytCarriageFeedbackDao);
    }

    @Override
    public void saveCarriageFeedback(CarriageFeedbackBean carriageFeedbackBean) {
        TytCarriageFeedback tytCarriageFeedback = new TytCarriageFeedback();
        tytCarriageFeedback.setStartProvince(carriageFeedbackBean.getStartProvince());
        tytCarriageFeedback.setStartCity(carriageFeedbackBean.getStartCity());
        tytCarriageFeedback.setStartArea(carriageFeedbackBean.getStartArea());
        tytCarriageFeedback.setDestProvince(carriageFeedbackBean.getDestProvince());
        tytCarriageFeedback.setDestCity(carriageFeedbackBean.getDestCity());
        tytCarriageFeedback.setDestArea(carriageFeedbackBean.getDestArea());
        tytCarriageFeedback.setGoodsName(carriageFeedbackBean.getGoodsName());
        tytCarriageFeedback.setGoodsWeight(carriageFeedbackBean.getGoodsWeight());
        if (null != carriageFeedbackBean.getGoodsLength()) {
            tytCarriageFeedback.setGoodsLength(carriageFeedbackBean.getGoodsLength());
        }
        if (null != carriageFeedbackBean.getGoodsWide()) {
            tytCarriageFeedback.setGoodsWide(carriageFeedbackBean.getGoodsWide());
        }
        if (null != carriageFeedbackBean.getGoodsHigh()) {
            tytCarriageFeedback.setGoodsHigh(carriageFeedbackBean.getGoodsHigh());
        }
        if(null != carriageFeedbackBean.getSrcMsgId()){
            tytCarriageFeedback.setSrcMsgId(carriageFeedbackBean.getSrcMsgId());
        }
        tytCarriageFeedback.setUserId(carriageFeedbackBean.getUserId());
        tytCarriageFeedback.setCellPhone(carriageFeedbackBean.getCellPhone());
        tytCarriageFeedback.setSource(carriageFeedbackBean.getSource());
        tytCarriageFeedback.setFeedbackType(carriageFeedbackBean.getFeedbackType());
        tytCarriageFeedback.setPrice(carriageFeedbackBean.getPrice());
        tytCarriageFeedback.setRemark(carriageFeedbackBean.getRemark());

        tytCarriageFeedback.setCreateTime(new Date());
        tytCarriageFeedback.setUpdateTime(new Date());
        this.add(tytCarriageFeedback);
    }
}
