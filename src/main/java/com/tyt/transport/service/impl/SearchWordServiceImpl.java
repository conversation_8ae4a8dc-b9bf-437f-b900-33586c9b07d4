package com.tyt.transport.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytSearchWord;
import com.tyt.transport.service.SearchWordService;

@Service("searchWordService")
public class SearchWordServiceImpl extends BaseServiceImpl<TytSearchWord, Long> implements SearchWordService {
	@Resource(name = "searchWordDao")
	public void setBaseDao(BaseDao<TytSearchWord, Long> searchWordDao) {
		super.setBaseDao(searchWordDao);
	}
}
