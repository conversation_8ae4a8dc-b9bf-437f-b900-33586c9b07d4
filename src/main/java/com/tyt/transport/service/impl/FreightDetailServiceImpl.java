package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytGeoDictService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqAMapDistanceMsg;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqTransportMsg;
import com.tyt.model.*;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.plat.mapper.base.TytTransportAfterOrderDataMapper;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.dao.FreightDetailDao;
import com.tyt.transport.enums.UseCarTypeEnum;
import com.tyt.transport.querybean.ProvincesCountBean;
import com.tyt.transport.querybean.SameTransportAveragePriceData;
import com.tyt.transport.querybean.SameTransportAveragePriceReq;
import com.tyt.transport.querybean.TransportCarryBean;
import com.tyt.transport.service.*;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.HighwayCostCalcUtil;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service("freightDetailService")
public class FreightDetailServiceImpl extends BaseServiceImpl<TytFreightDetailInfo, Long> implements FreightDetailService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "carBaseOilCostService")
	private CarBaseOilCostService carBaseOilCostService;

	@Resource(name = "tytConfigService")
    TytConfigService tytConfigService;

	@Resource(name = "highwayCostRuleService")
	private HighwayCostRuleService highwayCostRuleService;

	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;

	@Resource(name = "freightRouteService")
	private FreightRouteService freightRouteService;

	@Resource(name = "transportService")
	private TransportService transportService;

	/**
	 * 地理服务
	 */
	@Resource(name = "tytGeoDictService")
	private TytGeoDictService geoDictService;

	@Resource(name = "freightDetailDao")
	public void setBaseDao(BaseDao<TytFreightDetailInfo, Long> freightDetailDao) {
		super.setBaseDao(freightDetailDao);
	}

	@Resource
	private TytTransportAfterOrderDataMapper tytTransportAfterOrderDataMapper;

	@Autowired
	private TransportMainService transportMainService;

	@Autowired
	private BsPublishTransportService bsPublishTransportService;

	@Autowired
	private ExcellentPriceConfigService excellentPriceConfigService;

	@Override
	public ResultMsgBean getSameTransportAveragePrice(SameTransportAveragePriceReq sameTransportAveragePriceReq) {
		if (sameTransportAveragePriceReq == null) {
			return ResultMsgBean.successResponse(new SameTransportAveragePriceData());
		}

		if (sameTransportAveragePriceReq.getSrcMsgId() != null) {
			TytTransportMainExtend mainExtend = transportMainService.getMainExtend(sameTransportAveragePriceReq.getSrcMsgId());
			if (Objects.nonNull(mainExtend) && Objects.equals(UseCarTypeEnum.PART.getCode(), mainExtend.getUseCarType())) {
				return ResultMsgBean.successResponse(new SameTransportAveragePriceData());
			}
			TransportMain transportMain = transportMainService.getBySrcMsgId(sameTransportAveragePriceReq.getSrcMsgId());
			if (transportMain != null) {
				sameTransportAveragePriceReq.setStartCity(transportMain.getStartCity());
				sameTransportAveragePriceReq.setDestCity(transportMain.getDestCity());
				sameTransportAveragePriceReq.setGoodsWeight(transportMain.getWeight());
				sameTransportAveragePriceReq.setDistance(org.apache.commons.lang3.StringUtils.isBlank(transportMain.getDistance()) ? null : new BigDecimal(transportMain.getDistance()));
				sameTransportAveragePriceReq.setGoodTypeName(transportMain.getGoodTypeName());
			}
		}

		if (org.apache.commons.lang3.StringUtils.isBlank(sameTransportAveragePriceReq.getStartCity())
				|| org.apache.commons.lang3.StringUtils.isBlank(sameTransportAveragePriceReq.getDestCity())
				|| org.apache.commons.lang3.StringUtils.isBlank(sameTransportAveragePriceReq.getGoodsWeight())
				|| sameTransportAveragePriceReq.getDistance() == null) {
			return ResultMsgBean.successResponse(new SameTransportAveragePriceData());
		}

		BigDecimal weight = null;
		try {
			weight = new BigDecimal(sameTransportAveragePriceReq.getGoodsWeight());
		} catch (Exception e) {
			return ResultMsgBean.successResponse(new SameTransportAveragePriceData());
		}
		if (weight.compareTo(new BigDecimal(0)) > 0 && weight.compareTo(new BigDecimal(100)) < 0) {
			//重量转换成用于查询的区间
			if (weight.compareTo(new BigDecimal(5)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(0));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(5));
			} else if (weight.compareTo(new BigDecimal(10)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(5));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(10));
			} else if (weight.compareTo(new BigDecimal(19)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(10));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(19));
			} else if (weight.compareTo(new BigDecimal(23)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(19));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(23));
			} else if (weight.compareTo(new BigDecimal(28)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(23));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(28));
			} else if (weight.compareTo(new BigDecimal(32)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(28));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(32));
			} else if (weight.compareTo(new BigDecimal(50)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(32));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(50));
			} else if (weight.compareTo(new BigDecimal(56)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(50));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(56));
			} else if (weight.compareTo(new BigDecimal(63)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(56));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(63));
			} else if (weight.compareTo(new BigDecimal(80)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(63));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(80));
			} else if (weight.compareTo(new BigDecimal(90)) < 0) {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(80));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(90));
			} else {
				sameTransportAveragePriceReq.setWeightMin(new BigDecimal(90));
				sameTransportAveragePriceReq.setWeightMax(new BigDecimal(100));
			}
		} else {
			return ResultMsgBean.successResponse(new SameTransportAveragePriceData());
		}

		LocalDateTime now = LocalDateTime.now();
		// Get dates before 7, 15, 30, 90 days
		LocalDateTime sevenDaysAgo = now.minusDays(7);
		LocalDateTime fifteenDaysAgo = now.minusDays(15);
		LocalDateTime thirtyDaysAgo = now.minusDays(30);
		LocalDateTime ninetyDaysAgo = now.minusDays(90);

		List<Date> dates = new ArrayList<>();
		dates.add(Date.from(sevenDaysAgo.atZone(ZoneId.systemDefault()).toInstant()));
		dates.add(Date.from(fifteenDaysAgo.atZone(ZoneId.systemDefault()).toInstant()));
		dates.add(Date.from(thirtyDaysAgo.atZone(ZoneId.systemDefault()).toInstant()));
		dates.add(Date.from(ninetyDaysAgo.atZone(ZoneId.systemDefault()).toInstant()));
		sameTransportAveragePriceReq.setDates(dates);

		SameTransportAveragePriceData result = new SameTransportAveragePriceData();

		List<SameTransportAveragePriceData> sameTransportAveragePriceDatas = tytTransportAfterOrderDataMapper.getSameTransportAveragePrice(sameTransportAveragePriceReq);
		for (SameTransportAveragePriceData sameTransportAveragePriceData : sameTransportAveragePriceDatas) {
			if (sameTransportAveragePriceData.getAveragePrice() != null && sameTransportAveragePriceData.getAveragePrice().compareTo(new BigDecimal(0)) > 0) {
				BigDecimal quotient = sameTransportAveragePriceData.getAveragePrice().divide(new BigDecimal("50"), 2, RoundingMode.HALF_UP);
				// 将商向上取整
				BigDecimal roundedUp = quotient.setScale(0, RoundingMode.UP);
				// 将结果乘以50，得到最近的50的倍数
				sameTransportAveragePriceData.setAveragePrice(roundedUp.multiply(new BigDecimal("50")));
				result = sameTransportAveragePriceData;
				break;
			}
		}
		if (org.apache.commons.lang3.StringUtils.isBlank(result.getDayNum()) || result.getAveragePrice() == null || result.getAveragePrice().compareTo(new BigDecimal(0)) <= 0) {
			//取优车2.0建议价，时间返回7天
			if (sameTransportAveragePriceReq.getSrcMsgId() != null) {
				Transport transport = transportService.getLastBySrcMygId(sameTransportAveragePriceReq.getSrcMsgId());
				TransportCarryBean transportCarryBean = bsPublishTransportService.buildCarryBeanByTransport(transport);
				bsPublishTransportService.buildAddress(transportCarryBean);
				CarryPriceVo carryPriceVo = excellentPriceConfigService.getThPrice(transportCarryBean);
				if (carryPriceVo != null && carryPriceVo.getFixPriceMin() != null) {
					result.setDayNum("7");
					result.setAveragePrice(new BigDecimal(carryPriceVo.getFixPriceMin()));
					logger.info("本次获取相似货源平均成交价使用优车2.0建议价");
					return ResultMsgBean.successResponse(result);
				}
			}
			result.setDayNum(null);
			result.setAveragePrice(null);
		}
		return ResultMsgBean.successResponse(result);
	}

	@Override
	public TytFreightDetailInfo calcHighwayCost(TytFreightDetailInfo freightDetail, Float tonne, String totalDistance, String lineDetails, Integer machineNumber) {
		if(!StringUtils.hasLength(lineDetails)){
			logger.error("高速运费计算失败，高速详情信息为空");
			return null;
		}
		//为空则创建一个来进行传值
		if(freightDetail == null){
			freightDetail = new TytFreightDetailInfo();
		}
		String[] lineDetail = lineDetails.split(",");
		if(lineDetail == null || lineDetail.length <= 0){
			logger.error("高速运费计算失败，解析后高速详情信息为空");
			return null;
		}

		String[] lines = null;    //省份途经高速列表  || 计算公式列表
		String province = null;  //省份
		String highway = null;   //高速
		String distance = null;  //距离
		String calcType = null;   //计算类型
		String calcValue = null;  //计算公式
		Double tonneBegin = null; //吨位起始
		Double tonneEnd = null;   //吨位结束
		Double highwayCost = null; //单段路程运费
		Double totalCost = 0d;
		for(String lineStr : lineDetail){
			if(!StringUtils.hasLength(lineStr)){
				continue;
			}
			lines = lineStr.split("\\|\\|");
			if(lines == null || lines.length <= 0){
				logger.error("解析后线路详情为空lineStr:{},lineDetails:{}",lineStr,lineDetails);
				return null;
			}
			//处理数据
			province = lines[0];
			highway =  lines[1];
			distance = lines[2];
			if(!StringUtils.hasLength(province) || !StringUtils.hasLength(highway) ||!StringUtils.hasLength(distance) ){
				logger.error("解析后线路详情信息内容存在为空lineStr:{},lineDetails:{}",lineStr,lineDetails);
				return null;
			}
			//查询缓存后，获取计算类型和计算公式
			String ruleKey = Constant.CACHE_HIGHWAY_RULE_KEY+province+"_"+highway;
			String calcR =getCacheStringByInit(ruleKey);
			if(!StringUtils.hasLength(calcR)){
				//获取失败后尝试获取“其他”规则
				ruleKey = Constant.CACHE_HIGHWAY_RULE_KEY+province+"_其他";
				calcR =  getCacheStringByInit(ruleKey);
				if(!StringUtils.hasLength(calcR)){
					logger.error("缓存中不存在该省份的高速规则信息，lineStr:{},lineDetails:{}",lineStr,lineDetails);
					return null;
				}
			}
			lines = calcR.split(",");
			if(lines == null || lines.length <= 0){
				logger.error("运费计算失败，公式文本解析失败，lineStr:{},lineDetails:{}",lineStr,lineDetails);
				return null;
			}

			for(String calcStr : lines){
				String[] calcRules = calcStr.split("_");
				if(calcRules == null || calcRules.length <= 0){
					logger.error("运费计算失败，公式文本解析失败，lineStr:{},lineDetails:{}",lineStr,lineDetails);
					return null;
				}
				tonneBegin = Double.valueOf(calcRules[0]);
				tonneEnd = Double.valueOf(calcRules[1]);
				if(tonne > tonneBegin && tonne <= tonneEnd){
					calcType = calcRules[2];
					calcValue = calcRules[3];
					break;
				}
			}

			if(!StringUtils.hasLength(calcType) || !StringUtils.hasLength(calcValue)){
				logger.error("运费计算失败，吨位没有符合的规则，lineStr:{},lineDetails:{}",lineStr,lineDetails);
				return null;
			}

			//转换单位米为公里
			distance = String.valueOf(HighwayCostCalcUtil.calcDivide(distance,"1000.0"));
			//获取后计算金额
			highwayCost = HighwayCostCalcUtil.calcHighwayCostByType(Integer.valueOf(calcType),String.valueOf(tonne),distance,calcValue);
			if(highwayCost == null || highwayCost < 0){
				logger.error("运费计算失败，结果为空或者小于0，lineStr:{},lineDetails:{}",lineStr,lineDetails);
				return null;
			}
			System.out.println(String.format("[%s]省高速费用：[%s]",province,highwayCost));
			//累加结果
			totalCost = HighwayCostCalcUtil.calcInCrease(String.valueOf(totalCost),String.valueOf(highwayCost));
		}
		if(totalCost == null || totalCost < 0){
			logger.error("运费计算失败，结果为空或者小于0,lineDetails:{}",lineDetails);
			return null;
		}
		totalCost = HighwayCostCalcUtil.getNumberTwoPrecision(totalCost);//保留两位小数
		freightDetail.setHighwayCost(totalCost.floatValue());//高速费用

		Double globalTotalCost = HighwayCostCalcUtil.calcInCrease(String.valueOf(freightDetail.getBaseCost()),String.valueOf(freightDetail.getOilCost()));
		totalCost = HighwayCostCalcUtil.calcInCrease(String.valueOf(freightDetail.getHighwayCost()),String.valueOf(globalTotalCost));//总成本
		//多台车辆金额增加系数，默认值为0
		freightDetail.setProfitRate(0f);
		// 大于1台，则取用配置计算系数，用于APP计算最小成本和发货后存储数据计算
//		if(machineNumber > 1){
//			String profitRate = "0.55";
//			if(machineNumber == 2){ //等于2台,则取用配置项
//				profitRate = this.tytConfigService.getStringValue("highwayMinProfitRate");
//			}
//			if(StringUtils.hasLength(profitRate)){
//				//计算系数金额
//				freightDetail.setProfitRate(Float.valueOf(profitRate));
////				Double totalTemp = HighwayCostCalcUtil.calcMultiply(Double.toString(totalCost),profitRate);//系数金额
////				totalCost = HighwayCostCalcUtil.calcInCrease(Double.toString(totalCost),Double.toString(totalTemp));//累加系数金额
//			}
//		}
		totalCost = HighwayCostCalcUtil.getNumberTwoPrecision(totalCost);//保留两位小数
		freightDetail.setTotalCost(totalCost.floatValue());//增加系数后的总成本
		return freightDetail;
	}

	@Override
	public TytFreightDetailInfo calcBaseOilCost(TytFreightDetailInfo freightDetail, String cargoLength, String tonne, String totalDistance) {

		//==========计算固定成本  START============================
		//距离除以80KM/H 得出时长
		Double days = HighwayCostCalcUtil.calcDivide(totalDistance,"80.00");
		//时间+12小时（装货卸货时间）
		days  = HighwayCostCalcUtil.calcInCrease(String.valueOf(days),"12.00");
		//计算天数
		days = HighwayCostCalcUtil.calcDivide(String.valueOf(days),"24");
		//向上取整
		Integer day = HighwayCostCalcUtil.getNumberIntHalfUp(days);
		if (day == null || day <= 0) {
			logger.error("燃油，成本费计算失败，时长计算失败，totalDistance:{},tonne:{}", totalDistance, tonne);
			return null;
		}
		//如果给货物长度小于6  则按照长度6 来计算
		if(Double.valueOf(cargoLength) < 6 ){
			cargoLength = "6";
		}
		TytCarBaseOilCost carBaseOilCost = null;
		List<TytCarBaseOilCost> carBaseOilCosts = null;
		//获取计算规则
		//判断是否超限，超限则按照统一规则处理
		//查询是否存在合适规则，不匹配则向后匹配，直到匹配到最后一条为止，都不匹配则以最后一条计算
		if (Double.valueOf(cargoLength) >= 19 || Double.valueOf(tonne) > 32) {
			carBaseOilCosts = carBaseOilCostService.getCarBaseOilCostRule("19", "32");
			if (carBaseOilCosts == null || carBaseOilCosts.size() <= 0) {
				logger.error("燃油，成本费计算失败，数据库结果为空，cargoLength:{},tonne:{}", cargoLength, tonne);
				return null;
			}
			carBaseOilCost = carBaseOilCosts.get(0);
		}else{
			//如果为小数，则去除小数匹配
			if(cargoLength.indexOf(".") > 0){
				cargoLength = cargoLength.substring(0,cargoLength.indexOf("."));
			}
			carBaseOilCosts = carBaseOilCostService.getCarBaseOilCostRuleByCargoLengthGT(cargoLength);
			if (carBaseOilCosts != null && carBaseOilCosts.size() > 0) {
				//循环寻找最合适的车辆规则
				for(int i = 0; i < carBaseOilCosts.size(); i++){
					carBaseOilCost = carBaseOilCosts.get(i);
					if(Double.valueOf(tonne) <= carBaseOilCost.getWeightEnd()){
						break;
					}
				}
			}else{
				//超出数据库配置范围的，则按照最大计算
				carBaseOilCosts = carBaseOilCostService.getCarBaseOilCostRule("19", "32");
				if (carBaseOilCosts == null || carBaseOilCosts.size() <= 0) {
					logger.error("燃油，成本费计算失败，数据库结果为空，cargoLength:{},tonne:{}", cargoLength, tonne);
					return null;
				}
				carBaseOilCost = carBaseOilCosts.get(0);
			}
		}

		Double baseCost = null;
		Double oilCost = null;
        baseCost =HighwayCostCalcUtil.calcMultiply(String.valueOf(day),String.valueOf(carBaseOilCost.getPerDayCost()));
        if(baseCost == null && baseCost < 0){

            logger.error("成本费计算失败，成本计算结果小于0或者为空，cargoLength:{},tonne:{}",cargoLength,tonne);
            return null;
        }
        //==========计算固定成本  END============================

		//==========计算燃油费用  START============================
		Double tonneDouble = Double.valueOf(tonne);
        //获取柴油单价
        String dieselUnitPrice = tytConfigService.getStringValue("dieselUnitPrice");
        if(!StringUtils.hasLength(dieselUnitPrice)){
			logger.error("柴油价格获取失败，cargoLength:{},tonne:{}",cargoLength,tonne);
			return null;
		}
        //每公里油耗   =  百公里油耗/100
        Double perOilWear =  null;
        if(tonneDouble <= 20){
            perOilWear =  HighwayCostCalcUtil.calcDivide(String.valueOf(carBaseOilCost.getOilWear20()),"100");
        }else if(tonneDouble > 20 && tonneDouble <= 30){
            perOilWear =  HighwayCostCalcUtil.calcDivide(String.valueOf(carBaseOilCost.getOilWear2030()),"100");
        }else{
            perOilWear =  HighwayCostCalcUtil.calcDivide(String.valueOf(carBaseOilCost.getOilWear30()),"100");
        }

        //燃油费 =  柴油单价  *  每公里油耗 *  公里数
        oilCost = HighwayCostCalcUtil.calcMultiply(totalDistance,String.valueOf(perOilWear));
		System.out.println("每公里燃油费用："+ HighwayCostCalcUtil.calcMultiply(dieselUnitPrice,String.valueOf(perOilWear)));
        oilCost = HighwayCostCalcUtil.calcMultiply(dieselUnitPrice,String.valueOf(oilCost));
		//==========计算燃油费用  END============================


		//计算运费总成本  =  燃油费  +  固定成本  +  高速费用
		if(oilCost == null && oilCost < 0){
			logger.error("燃油费计算失败，燃油费计算结果小于0或者为空，cargoLength:{},tonne:{}",cargoLength,tonne);
			return null;
		}
		freightDetail.setSelfTonne(carBaseOilCost.getCarWeight());
		freightDetail.setBaseCost(baseCost.floatValue());
		freightDetail.setDaysNum(day);
		freightDetail.setOilCost(oilCost.floatValue());
		return freightDetail;
	}

	@Override
	public FreightRecord getFreightRecordByStartEndPoint(String mapKey) {

		return getFreightRecordByStartEndPoint(mapKey);
	}

	@Override
	public void getMinAndGuidingProfitRates(String startProvinc,String startCity,String destProvinc, String destCity, FreightPriceDetailInfo freightPriceDetailInfo) {
		// 计算最低利润率   8.198968-0.058161*LN(距离)-0.10666*LN(重量)+0.130768*LN(长度)+0.033095*LN(成本)
		String expressionStr = tytConfigService.getStringValue(Constant.FREIGHT_EXPRESSION_MINPROFITRATE);
		if(!StringUtils.hasLength(expressionStr)){
			logger.info("【ExprssionException】最小利润率的公式为空");
			return;
		}
		Double minProfitRates = HighwayCostCalcUtil.calcMinProfitRates(expressionStr,freightPriceDetailInfo.getTonne().toString(),freightPriceDetailInfo.getDistance().toString(),
				freightPriceDetailInfo.getCargoLength().toString(),freightPriceDetailInfo.getFixedCost().toString());
		if(minProfitRates == null || minProfitRates.doubleValue() <= 0){
			logger.info("【ExprssionException】最小利润率计算失败");
			return;
		}
		Double tempMinProfitRates = minProfitRates;
		minProfitRates = HighwayCostCalcUtil.calcMathPowAndSixPrecision(Math.E,minProfitRates);
		if(minProfitRates == null || minProfitRates.doubleValue() <= 0){
			logger.info("【ExprssionException1】最小利润率计算失败");
			return;
		}
		minProfitRates = HighwayCostCalcUtil.calcDivide(String.valueOf(minProfitRates),"10000");
		logger.info("人工派单-最小利润率：{}",minProfitRates);
		freightPriceDetailInfo.setMinProfitRate(minProfitRates);
		// 计算指导利润率  (LN(出发地发货总数/7)+LN(出发地找货人数/7)+LN(目的地地发货总数/7)+LN(目的地找货人数/7))^-0.35
		List<ProvincesCountBean> provincesCountBeanList = getProvincesCountBean(startProvinc,startCity,destProvinc,destCity);
		// 如果获取失败，则按照25%计算
		if(provincesCountBeanList == null || provincesCountBeanList.size() <= 3 || provincesCountBeanList.size() > 4){
			returnDefaultRate(freightPriceDetailInfo,0);
			return ;
		}
		Integer startSeekCount = 0;//出发地找货数量
		Integer startPubCount = 0;//出发地发货数量
		Integer destSeekCount = 0;//目的地找货数量
		Integer destPubCount = 0;//目的地发货数量
		//按顺序处理列表
		ProvincesCountBean provincesCountBean = provincesCountBeanList.get(0);//获取出发地找货数量
		if(provincesCountBean.getType() == null || provincesCountBean.getTotalCount() == null || provincesCountBean.getType().intValue() != 1 ||  provincesCountBean.getTotalCount().intValue() <= 0){
			//获取失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,1);
			return ;
		}
		startSeekCount = provincesCountBean.getTotalCount().intValue();

		provincesCountBean = provincesCountBeanList.get(1);//获取获取出发地发货数量
		if(provincesCountBean.getType() == null || provincesCountBean.getTotalCount() == null || provincesCountBean.getType().intValue() != 2 ||  provincesCountBean.getTotalCount().intValue() <= 0){
			//获取失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,2);
			return ;
		}
		startPubCount = provincesCountBean.getTotalCount().intValue();

		provincesCountBean = provincesCountBeanList.get(2);//获取目的地找货数量
		if(provincesCountBean.getType() == null || provincesCountBean.getTotalCount() == null || provincesCountBean.getType().intValue() != 3 ||  provincesCountBean.getTotalCount().intValue() <= 0){
			//获取失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,3);
			return ;
		}
		destSeekCount = provincesCountBean.getTotalCount().intValue();

		provincesCountBean = provincesCountBeanList.get(3);//获取目的地发货数量
		if(provincesCountBean.getType() == null || provincesCountBean.getTotalCount() == null || provincesCountBean.getType().intValue() != 4 ||  provincesCountBean.getTotalCount().intValue() <= 0){
			//获取失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,4);
			return ;
		}
		destPubCount = provincesCountBean.getTotalCount().intValue();
		if(startSeekCount == null || startSeekCount.intValue() <= 0 ||
				startPubCount == null || startPubCount.intValue() <= 0 ||
				destSeekCount == null || destSeekCount.intValue() <= 0 ||
				destPubCount == null || destPubCount.intValue() <= 0){
			//获取失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,5);
			return ;
		}
		// 计算指导利润率内部公式
		String guidingExpression = tytConfigService.getStringValue(Constant.FREIGHT_EXPRESSION_GUIDINGPROFITRATE);
		if(!StringUtils.hasLength(guidingExpression)){
			//计算失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,6);
			return ;
		}
		Double guidingProfitRate = HighwayCostCalcUtil.calcGuidingProfitRates(guidingExpression, startSeekCount.toString(), startPubCount.toString(), destSeekCount.toString(), destPubCount.toString());
		if(guidingProfitRate == null || guidingProfitRate.doubleValue() <= 0){
			//计算失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,7);
			return ;
		}
		// 计算指导利润率外部次方
		guidingExpression = tytConfigService.getStringValue(Constant.FREIGHT_EXPRESSION_GUIDINGPROFITRATE_POW);
		if(!StringUtils.hasLength(guidingExpression)){
			//计算失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,8);
			return ;
		}
		guidingProfitRate = HighwayCostCalcUtil.calcMathPowAndSixPrecision(guidingProfitRate,Double.parseDouble(guidingExpression));
		if(guidingProfitRate == null || guidingProfitRate.doubleValue() <= 0){
			//计算失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,9);
			return ;
		}
		// 累加基本利润率得出推荐利润率
		guidingProfitRate = HighwayCostCalcUtil.calcInCrease(String.valueOf(tempMinProfitRates),String.valueOf(guidingProfitRate));
		guidingProfitRate = HighwayCostCalcUtil.calcMathPowAndSixPrecision(Math.E,guidingProfitRate);
		if(guidingProfitRate == null || guidingProfitRate.doubleValue() <= 0){
			//计算失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,10);
			return ;
		}
		guidingProfitRate = HighwayCostCalcUtil.calcDivide(String.valueOf(guidingProfitRate),"10000");
		if(guidingProfitRate == null || guidingProfitRate.doubleValue() <= 0){
			//计算失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,11);
			return ;
		}

		//如果指导低于最低，则取最低加默认利润率为指导
		if(guidingProfitRate.doubleValue() <= minProfitRates.doubleValue()){
			logger.info("指导利润利率小于最低利率，取最低+默认利率=指导利率");
			//计算失败后，返回默认的指导利润率，防止计算报错
			returnDefaultRate(freightPriceDetailInfo,-1);
			return ;
		}

		guidingProfitRate = HighwayCostCalcUtil.getNumberSixPrecision(guidingProfitRate);
		logger.info("人工派单-指导利润率：{}",guidingProfitRate);
		freightPriceDetailInfo.setGuidingRate(guidingProfitRate);
	}

	@Override
	public Double updateDistanceByDbDistance(Double distance, Double dbDistance,FreightRoute freightRoute) {
		String diffRange = tytConfigService.getStringValue(Constant.FREIGHT_DISTANCE_DIFF_RANGE,"0.1");
		if(!StringUtils.hasLength(diffRange) || "0".equals(diffRange)){
			//直接返回
			return distance;
		}
		Double startRange = HighwayCostCalcUtil.calcMultiply(String.valueOf(dbDistance),String.valueOf(HighwayCostCalcUtil.calcSubtract("1",diffRange)));
		Double endRange = HighwayCostCalcUtil.calcMultiply(String.valueOf(dbDistance),String.valueOf(HighwayCostCalcUtil.calcInCrease("1",diffRange)));
		if(distance < startRange || distance > endRange){
			logger.info("数据库距离和传入距离的差距过大，删除后，重新计算线路,distance:{},dbDistance:{},disId:{}",distance,dbDistance,freightRoute.getId());
			logger.info("需要重跑的路线：{}-{}-{}---{}-{}-{}",freightRoute.getStartProvinc(),freightRoute.getStartCity(),freightRoute.getStartArea(),freightRoute.getDestProvinc(),freightRoute.getDestCity(),freightRoute.getDestArea());
			// 根据出发地目的地查询一条已有的货物ID
			sendMqMessage2MqByStart2Dest(freightRoute);
			return null;
		}
		return distance;
	}

	@Override
	public void sendMqMessage2MqByStart2Dest(FreightRoute freightRoute){
		if(freightRoute != null){
//			Long transId = transportService.getTransIdByProvinces(freightRoute.getStartProvinc(),freightRoute.getStartCity(),freightRoute.getStartArea(),
//					freightRoute.getDestProvinc(),freightRoute.getDestCity(),freightRoute.getDestArea());
//
//			if(transId != null && transId.longValue() >= 0){
//				logger.info("重新计算线路,重跑的ID：{}",transId);
//				//发送距离重跑MQ
//				sendMapTaskMessage2MQ(transId);
//			}
			// 新逻辑，不需要货物ID，可发送MQ，重新计算高德距离
			MqAMapDistanceMsg msg = new MqAMapDistanceMsg();
			msg.setStartProvinc(freightRoute.getStartProvinc());
			msg.setStartCity(freightRoute.getStartCity());
			msg.setStartArea(freightRoute.getStartArea());

			msg.setDestProvinc(freightRoute.getDestProvinc());
			msg.setDestCity(freightRoute.getDestCity());
			msg.setDestArea(freightRoute.getDestArea());
			sendMapTaskMessage2MQ(msg);
		}
	}


	/**
	 * 向MQ发送准货源推荐信息
	 *
	 * @param messageBean 计算距离的MQ消息BEAN
	 */
	private void sendMapTaskMessage2MQ(MqAMapDistanceMsg messageBean) {
		// 发送精准货源推荐信息MQ

		messageBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
		messageBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_AMAP_DISTANCE);
		// 保存mq信息到数据库,并发送MQ消息
		tytMqMessageService.addSaveMqMessage(messageBean.getMessageSerailNum(), JSON.toJSONString(messageBean), messageBean.getMessageType());
		tytMqMessageService.sendMqMessage(messageBean.getMessageSerailNum(), JSON.toJSONString(messageBean),messageBean.getMessageType());
	}

	/**
	 * 向MQ发送准货源推荐信息
	 *
	 * @param transId
	 *            货物ID
	 */
	@Deprecated
	private void sendMapTaskMessage2MQ(Long transId) {
		// 发送精准货源推荐信息MQ
		MqTransportMsg mqTransportMsg = new MqTransportMsg();

		mqTransportMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
		mqTransportMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_MAP_TASK);
		mqTransportMsg.setTsId(transId);
		// 保存mq信息到数据库,并发送MQ消息
		tytMqMessageService.addSaveMqMessage(mqTransportMsg.getMessageSerailNum(), JSON.toJSONString(mqTransportMsg), mqTransportMsg.getMessageType());
		tytMqMessageService.sendMqMessage(mqTransportMsg.getMessageSerailNum(), JSON.toJSONString(mqTransportMsg),mqTransportMsg.getMessageType());
	}

	/**
	 *
	 * @param freightPriceDetailInfo  内容
	 * @param num    错误位置
	 */
	private void returnDefaultRate(FreightPriceDetailInfo freightPriceDetailInfo,Integer num){
		String defaultGuidingRate = tytConfigService.getStringValue(Constant.PROVINCES_SEEK_FAIL_DEFAULT_GUIDING_RATE,"0.25");
		freightPriceDetailInfo.setGuidingRate(HighwayCostCalcUtil.calcInCrease(String.valueOf(freightPriceDetailInfo.getMinProfitRate()),defaultGuidingRate));
		logger.info("获取发货找货数量失败，取默认的指导利润率:{},错误流程码：{}",defaultGuidingRate,num);
	}



	private List<ProvincesCountBean> getProvincesCountBean(String startProvinc,String startCity,String destProvinc, String destCity){
		List<ProvincesCountBean> provincesCountBeanList = new ArrayList<>();
		String cacheKey = tytConfigService.getStringValue(Constant.CACHE_PROVINCES_PUB_SEEK_TRANS_COUNT_KEY,"provinces_pubseek_trans_count_");
		//从Redis中取，不存在则查询数据库，然后保存redis
		cacheKey += startProvinc+startCity+destProvinc+destCity+ TimeUtil.formatDate_(TimeUtil.today());
		provincesCountBeanList = RedisUtil.getObjectList(cacheKey);
		if(provincesCountBeanList == null || provincesCountBeanList.size() <= 0){
			//从数据库中获取
			provincesCountBeanList = ((FreightDetailDao)this.getBaseDao()).getProvincesCountList(startProvinc,startCity,destProvinc,destCity);
			if(provincesCountBeanList == null || provincesCountBeanList.size() <= 0){
				return null;
			}
			//获取成功后，数据放入缓存
			RedisUtil.setObjectList(cacheKey,provincesCountBeanList,Constant.CACHE_EXPIRE_TIME_12H_INT);
		}
		return provincesCountBeanList;
	}

	//获取缓存的高速线路信息，如果需要初始化则初始化
	private String getCacheStringByInit(String queryKey){
		String flag = cacheService.getString(Constant.CACHE_HIGHWAY_RULE_KEY+"update_flag");
		if(!StringUtils.hasLength(flag) || !"1".equals(flag)){
			logger.info("高速规则更新标识不存在，更新缓存");
			init();
		}
		return (String)cacheService.getObject(queryKey);
	}

	@PostConstruct
	public void init(){
		System.out.println("spring容器初始化完毕，获取高速费计算规则存入MemCached");
		List<TytHighwayCostRule> highwayCostRules = highwayCostRuleService.getInitList2Mc();
		if(highwayCostRules != null && highwayCostRules.size() > 0){
			String key = null;
			for(TytHighwayCostRule hcr: highwayCostRules){
				key = Constant.CACHE_HIGHWAY_RULE_KEY+hcr.getProvince()+"_"+hcr.getHighway();
				cacheService.setObject(key, hcr.getCalcRuleValue(), Constant.CACHE_TYT_GEO_DICT_TIME);//缓存一个月
//                System.out.println(key+"______"+hcr.getCalcRuleValue());
				System.out.println(key);
			}
			cacheService.setString(Constant.CACHE_HIGHWAY_RULE_KEY+"update_flag","1", Constant.CACHE_TYT_GEO_DICT_TIME);//缓存一个月
			System.out.println(String.format("更新高速费计算规则存入MemCached成功,size:【%s】",highwayCostRules.size()));
		}else{
			System.out.println("高速费计算规则存入MemCached失败，为空");
		}
	}


	@Override
	public FreightPriceSearchLog getBaseOilCost(FreightPriceSearchLog freightDetail, String cargoLength, String tonne, String totalDistance) {
		//==========计算固定成本  START============================
		//距离除以80KM/H 得出时长
		Double days = HighwayCostCalcUtil.calcDivide(totalDistance,"80.00");
		//时间+12小时（装货卸货时间）
		days  = HighwayCostCalcUtil.calcInCrease(String.valueOf(days),"12.00");
		//计算天数
		days = HighwayCostCalcUtil.calcDivide(String.valueOf(days),"24");
		//向上取整
		Integer day = HighwayCostCalcUtil.getNumberIntHalfUp(days);
		if (day == null || day <= 0) {
			logger.error("燃油，成本费计算失败，时长计算失败，totalDistance:{},tonne:{}", totalDistance, tonne);
			return null;
		}
		//如果给货物长度小于6  则按照长度6 来计算
		if(Double.valueOf(cargoLength) < 6 ){
			cargoLength = "6";
		}
		TytCarBaseOilCost carBaseOilCost = null;
		List<TytCarBaseOilCost> carBaseOilCosts = null;
		//获取计算规则
		//判断是否超限，超限则按照统一规则处理
		//查询是否存在合适规则，不匹配则向后匹配，直到匹配到最后一条为止，都不匹配则以最后一条计算
		if (Double.valueOf(cargoLength) >= 19 || Double.valueOf(tonne) > 32) {
			carBaseOilCosts = carBaseOilCostService.getCarBaseOilCostRule("19", "32");
			if (carBaseOilCosts == null || carBaseOilCosts.size() <= 0) {
				logger.error("燃油，成本费计算失败，数据库结果为空，cargoLength:{},tonne:{}", cargoLength, tonne);
				return null;
			}
			carBaseOilCost = carBaseOilCosts.get(0);
		}else{
			//如果为小数，则去除小数匹配
			if(cargoLength.indexOf(".") > 0){
				cargoLength = cargoLength.substring(0,cargoLength.indexOf("."));
			}
			carBaseOilCosts = carBaseOilCostService.getCarBaseOilCostRuleByCargoLengthGT(cargoLength);
			if (carBaseOilCosts != null && carBaseOilCosts.size() > 0) {
				//循环寻找最合适的车辆规则
				for(int i = 0; i < carBaseOilCosts.size(); i++){
					carBaseOilCost = carBaseOilCosts.get(i);
					if(Double.valueOf(tonne) <= carBaseOilCost.getWeightEnd()){
						break;
					}
				}
			}else{
				//超出数据库配置范围的，则按照最大计算
				carBaseOilCosts = carBaseOilCostService.getCarBaseOilCostRule("19", "32");
				if (carBaseOilCosts == null || carBaseOilCosts.size() <= 0) {
					logger.error("燃油，成本费计算失败，数据库结果为空，cargoLength:{},tonne:{}", cargoLength, tonne);
					return null;
				}
				carBaseOilCost = carBaseOilCosts.get(0);
			}
		}

		Double baseCost = null;
		Double oilCost = null;
		//按照用户输入的计算人工成本
		baseCost =HighwayCostCalcUtil.calcMultiply(String.valueOf(day),String.valueOf(freightDetail.getUserBaseCost()));
//		baseCost =HighwayCostCalcUtil.calcMultiply(String.valueOf(day),String.valueOf(carBaseOilCost.getPerDayCost()));
		if(baseCost == null && baseCost < 0){

			logger.error("成本费计算失败，成本计算结果小于0或者为空，cargoLength:{},tonne:{}",cargoLength,tonne);
			return null;
		}

		//==========计算固定成本  END============================

		//==========计算燃油费用  START============================
		Double tonneDouble = Double.valueOf(tonne);
		//获取柴油单价
		String dieselUnitPrice = tytConfigService.getStringValue("dieselUnitPrice");
		if(!StringUtils.hasLength(dieselUnitPrice)){
			logger.error("柴油价格获取失败，cargoLength:{},tonne:{}",cargoLength,tonne);
			return null;
		}
		//每公里油耗   =  百公里油耗/100
		Double perOilWear =  null;
		if(tonneDouble <= 20){
			freightDetail.setOilWear(carBaseOilCost.getOilWear20().doubleValue());
//			perOilWear =  HighwayCostCalcUtil.calcDivide(String.valueOf(carBaseOilCost.getOilWear20()),"100");
		}else if(tonneDouble > 20 && tonneDouble <= 30){
			freightDetail.setOilWear(carBaseOilCost.getOilWear2030().doubleValue());
//			perOilWear =  HighwayCostCalcUtil.calcDivide(String.valueOf(carBaseOilCost.getOilWear2030()),"100");
		}else{
			freightDetail.setOilWear(carBaseOilCost.getOilWear30().doubleValue());
//			perOilWear =  HighwayCostCalcUtil.calcDivide(String.valueOf(carBaseOilCost.getOilWear30()),"100");
		}
		perOilWear =  HighwayCostCalcUtil.calcDivide(String.valueOf(freightDetail.getUserOilWear()),"100");
		//燃油费 =  柴油单价  *  每公里油耗 *  公里数
		//使用用户传入的价格计算
		oilCost = HighwayCostCalcUtil.calcMultiply(totalDistance,String.valueOf(perOilWear));
		oilCost = HighwayCostCalcUtil.calcMultiply(String.valueOf(freightDetail.getUserOilPrice()),String.valueOf(oilCost));
		//==========计算燃油费用  END============================
		if(oilCost == null && oilCost < 0){
			logger.error("燃油费计算失败，燃油费计算结果小于0或者为空，cargoLength:{},tonne:{}",cargoLength,tonne);
			return null;
		}
		freightDetail.setSelfTonne(carBaseOilCost.getCarWeight().doubleValue());
		freightDetail.setPerBaseCost(carBaseOilCost.getPerDayCost().doubleValue()); //系统计算的每日人工成本
		freightDetail.setBaseCost(baseCost);
		freightDetail.setDaysNum(day);
		freightDetail.setOilPrice(Double.valueOf(dieselUnitPrice));
		freightDetail.setOilCost(oilCost.doubleValue());
		return freightDetail;
	}
}
