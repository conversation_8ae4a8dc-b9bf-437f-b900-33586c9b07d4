package com.tyt.transport.service.impl;

import com.tyt.base.service.BaseServiceImpl;
import com.tyt.plat.entity.base.TytTransportSyncYmm;
import com.tyt.plat.mapper.base.TytTransportSyncYmmMapper;
import com.tyt.transport.service.TytTransportSyncYmmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ClassName TytTransportSyncYmmServiceImpl
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/10/19 13:22
 * @Verdion 1.0
 **/
@Service("tytTransportSyncYmmService")
public class TytTransportSyncYmmServiceImpl extends BaseServiceImpl<TytTransportSyncYmm, Long> implements TytTransportSyncYmmService {

    @Autowired
    private TytTransportSyncYmmMapper tytTransportSyncYmmMapper;


    @Override
    public TytTransportSyncYmm findTransportSyncYmm(Long srcMsgId) {

        return tytTransportSyncYmmMapper.selectTytTransportSyncYmmBySrcId(srcMsgId);

    }

    @Override
    public void updateTransportSyncYmmStatus(TytTransportSyncYmm resultTransport) {
        resultTransport.setTransportStatus(1);
        resultTransport.setMtime(new Date());
        tytTransportSyncYmmMapper.updateByPrimaryKeySelective(resultTransport);
    }
}
