package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.elasticsearch.ElasticTransportHelper;
import com.tyt.model.Transport;
import com.tyt.plat.mapper.base.TytTransportMapper;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.TransportSearchService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 货物信息查询
 *
 * <AUTHOR>
 * @version app5920编写
 * @date 2019/5/15
 */
@SuppressWarnings("deprecation")
@Service("transportSearchService")
public class TransportSearchServiceImpl extends BaseServiceImpl<Transport, Long> implements TransportSearchService {

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Autowired
    private TytTransportMapper tytTransportMapper;

    public Logger logger = LoggerFactory.getLogger(this.getClass());
    /**
     * 今天之前的最大id临时变量；
     */
    private static final Map<String, Long> maxIdMap = new HashMap<>(1);

    @Override
    @Resource(name = "transportDao")
    public void setBaseDao(BaseDao<Transport, Long> transportDao) {
        super.setBaseDao(transportDao);
    }


    @Override
    public List<Transport> findElsTransportForProvince(String province, int queryType, long tsId, int pageSize) {

        long todayMinId = getBeforeTodayMaxId();
        if (tsId < todayMinId) {
            //只能查询今日货物信息
            tsId = todayMinId;
        }
        DoSqlParamBean sqlParamBean = getSqlForProvince(province, queryType, tsId, todayMinId);

        return ElasticTransportHelper.findTodayList(sqlParamBean.getSql(), 0, pageSize, sqlParamBean.getPram());


    }


    @Override
    public long getBeforeTodayMaxId() {
        String key = "MAX_ID" + TimeUtil.formatDate_(TimeUtil.today());
        return maxIdMap.computeIfAbsent(key, k ->{
            String redisMaxId = cacheService.getString(k);
            logger.info("redisMaxId ----{}", redisMaxId);
            long minTsId = 0;
            if(StringUtils.isNotEmpty(redisMaxId)){

                minTsId = Long.parseLong(redisMaxId);

            }else{

                //分布式锁获取今天之前的最大货物Id
                int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
                String redisLockKey = "LOCK_" + k;

                try {
                    if (LockUtil.lockObject("1", redisLockKey, redisLockTimeout)) {
                        redisMaxId = cacheService.getString(k);
                        if(StringUtils.isEmpty(redisMaxId)){
                            //获取今天之前最大货物id
                            minTsId = initBeforeTodayMaxId();
                            logger.info("dataSourceMaxId ----{}", minTsId);
                            cacheService.setString(k, minTsId + "", TimeUnit.HOURS.toSeconds(25));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    LockUtil.unLockObject("1", redisLockKey);
                }

            }

            maxIdMap.clear();

            logger.info("APP加载找货数据，初始化货物ID为：{}", redisMaxId);
            return minTsId;
        });

    }


    /**
     * 初始化今天之前最大货物id
     */
    private long initBeforeTodayMaxId() {
        String sql = "SELECT MAX(t.id) maxId FROM tyt_transport t WHERE  t.`ctime` < ?";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{TimeUtil.formatDateTime(TimeUtil.today())});
        return (count == null ? 0 : count.longValue());
    }

    /**
     * 省份倒短查询组织sql 语句
     *
     * @param province   省份
     * @param queryType  查询类型
     * @param tsId       货物起止id
     * @param todayMinId 当天最小货物ID
     * @return String 组织完成的sql语句
     */
    private DoSqlParamBean getSqlForProvince(String province, int queryType, long tsId, long todayMinId) {
        StringBuilder sb = new StringBuilder("select  *  from tyt_transport where 1=1 ");
        // 发布中货物 、可显示货源、增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表
        sb.append("and status=1")
                .append(" and display_type=1")
                .append(" and is_display=1");

        List<Object> returnList = new ArrayList<>();
        //出发地
        sb.append(" and start_provinc=?");
        returnList.add(province);
        //目的地
        sb.append(" and dest_provinc=?");
        returnList.add(province);

        if (Constant.QUERY_TYPE_PULL_UP == queryType) {
            //上拉 查询当天的历史数据
            sb.append(" and id < ?");
            returnList.add(tsId);
            sb.append(" and id > ?");
            returnList.add(todayMinId);

        } else {
            sb.append(" and id > ?");
            returnList.add(tsId);
        }
        sb.append(" order by id desc ");
        DoSqlParamBean sqlParamBean = new DoSqlParamBean();
        sqlParamBean.setPram(returnList);
        sqlParamBean.setSql(sb.toString());
        logger.debug("getSqlForProvince: sql= " + sqlParamBean.toString());

        return sqlParamBean;
    }


    /**
     * 当list当长度等于传入参数当大小时进行加1返回
     *
     * @param list     结果集
     * @param pageSize 数量
     * @return int类型
     */
    @Override
    public int getTotalSize(List list, int pageSize) {

        if (list == null) {
            return 0;
        }
        int totalSize = list.size();
        return totalSize == pageSize ? totalSize + 1 : totalSize;


    }

    @Override
    public List<Transport> findElsTransportForScope(ScopeFallShortSqlBean sqlBean) {
        long todayMinId = getBeforeTodayMaxId();
        if (sqlBean.getTsId() < todayMinId) {
            //只能查询今日货物信息
            sqlBean.setTsId(todayMinId);
        }
        DoSqlParamBean sqlParamBean = getSqlForScope(sqlBean, todayMinId);

        return ElasticTransportHelper.findTodayList(sqlParamBean.getSql(), 0, sqlBean.getPageSize(), sqlParamBean.getPram());
    }

    @Override
    public List<Transport> findDbTransportForScope(ScopeFallShortSqlBean sqlBean) {
        long todayMinId = getBeforeTodayMaxId();
        if (sqlBean.getTsId() < todayMinId) {
            //只能查询今日货物信息
            sqlBean.setTsId(todayMinId);
        }
        String key = Constant.CACHE_FALL_SHORT_TRANSPORT_LIST_KEY + "_" + sqlBean.getStartCoordX() + "_" + sqlBean.getStartCoordY() + "_" + sqlBean.getStartRange() + "_" + sqlBean.getQueryType() + "_" + sqlBean.getTsId();

        //其查询他走数据库和缓存
        //第一查询时使用缓存查询；
        boolean isQueryTypeFirist = Constant.QUERY_TYPE_FIRIST == sqlBean.getQueryType();
        if (isQueryTypeFirist) {

            String transportListCacheJson = cacheService.getString(key);
            // 缓存存在，从缓存中查询
            if (StringUtils.isNotBlank(transportListCacheJson)) {

                return JSON.parseArray(transportListCacheJson, Transport.class);
            }
        }
        // 进行数据库查寻sql 语句组织和参数赋值
        DoSqlParamBean sqlParamBean = getSqlForScope(sqlBean, todayMinId);
        List<Transport> resList = this.getBaseDao().search(sqlParamBean.getSql(), sqlParamBean.getPram().toArray(), 1, sqlBean.getPageSize());
        //首次查询结果放入缓存；
        if (isQueryTypeFirist) {
            // 缓存时间
            Integer cacheTime = AppConfig.getIntProperty("tyt.tyt_transport.query.cache.time");
            String transportListJsonStr = JSON.toJSONString(resList);
            cacheService.setString(key, transportListJsonStr, cacheTime == null ? 5 : cacheTime.longValue());
        }
        return resList;
    }

    @Override
    public List<Transport> findElsTransportSearch(TransportSearchSqlBean sqlBean) {
        long todayMinId = getBeforeTodayMaxId();
        if (sqlBean.getTsId() < todayMinId) {
            //只能查询今日货物信息
            sqlBean.setTsId(todayMinId);
        }
        DoSqlParamBean sqlParamBean = getSqlForTransport(sqlBean, todayMinId);
        return ElasticTransportHelper.findTodayList(sqlParamBean.getSql(), 0, sqlBean.getPageSize(), sqlParamBean.getPram());
    }

    @Override
    public List<Transport> findDbTransportSearch(TransportSearchSqlBean sqlBean) {
        long todayMinId = getBeforeTodayMaxId();
        if (sqlBean.getTsId() < todayMinId) {
            //只能查询今日货物信息
            sqlBean.setTsId(todayMinId);
        }
        String key = getTransportCacheKey(sqlBean.getStartCoordX(),
                sqlBean.getStartCoordY(),
                sqlBean.getDestCoordX(),
                sqlBean.getDestCoordY(),
                sqlBean.getQueryType(),
                sqlBean.getTsId(),
                sqlBean.getStartRange(),
                sqlBean.getDestRange(),
                sqlBean.getUserType() == null ? -1 : sqlBean.getUserType(),
                sqlBean.getCarLength() == null ? "" : sqlBean.getCarLength(),
                sqlBean.getCarType() == null ? "" : sqlBean.getCarType(),
                sqlBean.getSpecialRequired() == null ? "" : sqlBean.getSpecialRequired(),
                sqlBean.getStartWeight(),
                sqlBean.getEndWeight(),
                sqlBean.getGoodsName(),
                sqlBean.getMinLength(),
                sqlBean.getMaxLength(),
                sqlBean.getMinWidth(),
                sqlBean.getMaxWidth(),
                sqlBean.getMinHeight(),
                sqlBean.getMaxHeight(),
                sqlBean.getStartLoadingTime(),
                sqlBean.getEndLoadingTime(),
                sqlBean.getMinPrice(),
                sqlBean.getMaxPrice()
        );

        //其查询他走数据库和缓存
        //第一查询时使用缓存查询；
        boolean isQueryTypeFirist = Constant.QUERY_TYPE_FIRIST == sqlBean.getQueryType();
        if (isQueryTypeFirist) {
            String transportListCacheJson = cacheService.getString(key);
            // 缓存存在，从缓存中查询
            if (StringUtils.isNotBlank(transportListCacheJson)) {
                return JSON.parseArray(transportListCacheJson, Transport.class);
            }
        }
        // 进行数据库查寻sql 语句组织和参数赋值
        logger.info("todayMinId的值是：{},todayMaxId的值是：{}", todayMinId,sqlBean.getTsId());
        DoSqlParamBean sqlParamBean = getSqlForTransport(sqlBean, todayMinId);
        List<Transport> resList = this.getBaseDao().search(sqlParamBean.getSql(), sqlParamBean.getPram().toArray(), 1, sqlBean.getPageSize());
        //首次查询结果放入缓存；
        if (isQueryTypeFirist) {
            // 缓存时间
            Integer cacheTime = AppConfig.getIntProperty("tyt.tyt_transport.query.cache.time");
            String transportListJsonStr = JSON.toJSONString(resList);
            cacheService.setString(key, transportListJsonStr, cacheTime == null ? 5 : cacheTime.longValue());
        }
        return resList;
    }

    @Override
    public List<Transport> findElsDestTransport(DestTransportSearchBean bean, int searchSize) {
        long todayMinId = getBeforeTodayMaxId();
        DoSqlParamBean sqlParamBean = getSqlForDestTransport(bean, searchSize, todayMinId);
        return ElasticTransportHelper.findTodayList(sqlParamBean.getSql(), 0, searchSize, sqlParamBean.getPram());
    }

    @Override
    public List<Transport> findDbDestTransport(DestTransportSearchBean bean, int searchSize) {
        long todayMinId = getBeforeTodayMaxId();
        String cacheSuffix = ""; // 区分不同版本下的缓存数据
        if(StringUtils.isNotBlank(bean.getClientVersion()) && NumberUtils.isNumber(bean.getClientVersion()) && Integer.parseInt(bean.getClientVersion()) >= 6000) {
            cacheSuffix = "identity_";
        }
        String key = "";
        if(StringUtils.isNotBlank(bean.getDestProvinc())) { // 目的地货源
            key = Constant.CACHE_DEST_TRANSPORT_SEARCH_KEY + cacheSuffix + bean.getStartProvinc() + "_" + bean.getStartCity() + "_" + bean.getStartArea();
        } else { // 同路线货源
            key = Constant.CACHE_DEST_TRANSPORT_SEARCH_KEY + cacheSuffix + bean.getStartProvinc() + "_" + bean.getStartCity() + "_" + bean.getStartArea()
                    + bean.getDestProvinc() + "_" + bean.getDestCity() + "_" + bean.getDestArea();
        }

        String transportListCacheJson = cacheService.getString(key);
        // 缓存存在，从缓存中查询
        if (StringUtils.isNotBlank(transportListCacheJson)) {
            return JSON.parseArray(transportListCacheJson, Transport.class);
        }
        // 进行数据库查寻sql 语句组织和参数赋值
        DoSqlParamBean sqlParamBean = getSqlForDestTransport(bean, searchSize, todayMinId);
        List<Transport> resList = this.getBaseDao().search(sqlParamBean.getSql(), sqlParamBean.getPram().toArray(), 1, searchSize);
        // 缓存时间
        Integer cacheTime = AppConfig.getIntProperty("tyt.tyt_transport.query.cache.time");
        String transportListJsonStr = JSON.toJSONString(resList);
        cacheService.setString(key, transportListJsonStr, cacheTime == null ? 5 : cacheTime.longValue());
        return resList;
    }

    @Override
    public List<Transport> findDbSimilarTransportSearch(SimilarTransportSearchBean searchBean) {
        //缓存key
        String key = Constant.CACHE_SIMILAR_TRANSPORT_SEARCH_KEY + searchBean.getGoodsId();
        String transportListCacheJson = cacheService.getString(key);
        // 缓存存在，从缓存中查询
        if (StringUtils.isNotBlank(transportListCacheJson)) {
            return JSON.parseArray(transportListCacheJson, Transport.class);
        }

        List<Transport> similarityList = tytTransportMapper.getSimilarityList(searchBean);

        // 缓存
        String transportListJsonStr = JSON.toJSONString(similarityList);
        cacheService.setString(key, transportListJsonStr, 5);
        return similarityList;
    }




    @Override
    public <C> C getBaseDao(Class<C> clzss) {
        return super.getBaseDao(clzss);
    }


    @Override
    public List<Transport> findDbTransportForProvince(String province, int queryType, long tsId, int pageSize) {
        long todayMinId = getBeforeTodayMaxId();
        if (tsId < todayMinId) {
            //只能查询今日货物信息
            tsId = todayMinId;
        }
        String key = Constant.CACHE_FALL_SHORT_TRANSPORT_LIST_KEY + "_" + province + "_" + queryType + "_" + tsId;
        List<Transport> resList;

        //其查询他走数据库和缓存
        //第一查询时使用缓存查询；
        boolean isQueryTypeFirist = Constant.QUERY_TYPE_FIRIST == queryType;
        if (isQueryTypeFirist) {

            String transportListCacheJson = cacheService.getString(key);
            // 缓存存在，从缓存中查询
            if (StringUtils.isNotBlank(transportListCacheJson)) {
                resList = JSON.parseArray(transportListCacheJson, Transport.class);

                return resList;
            }
        }

        // 进行数据库查寻sql 语句组织和参数赋值
        DoSqlParamBean sqlParamBean = getSqlForProvince(province, queryType, tsId, todayMinId);
        resList = this.getBaseDao().search(sqlParamBean.getSql(), sqlParamBean.getPram().toArray(), 1, pageSize);

        //首次查询结果放入缓存；
        if (isQueryTypeFirist) {
            // 缓存时间
            Integer cacheTime = AppConfig.getIntProperty("tyt.tyt_transport.query.cache.time");
            String transportListJsonStr = JSON.toJSONString(resList);
            cacheService.setString(key, transportListJsonStr, cacheTime == null ? 5 : cacheTime.longValue());
        }
        return resList;
    }

    /**
     * 范围倒短sql拼写
     *
     * @param sqlBean    条件对象
     * @param todayMinId 今日发货最小id
     * @return sqlbean
     */
    private DoSqlParamBean getSqlForScope(ScopeFallShortSqlBean sqlBean, long todayMinId) {
        StringBuilder sb = new StringBuilder("select  *  from tyt_transport where 1=1 ");
        // 发布中货物 、可显示货源、增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表
        sb.append("and status=1")
                .append(" and display_type=1")
                .append(" and is_display=1");

        List<Object> returnList = new ArrayList<>();
        //出发地
        sb.append(" and start_provinc=?");
        returnList.add(sqlBean.getStartProvinc());
        sb.append(" and start_city=?");
        returnList.add(sqlBean.getStartCity());
        if (StringUtils.isNotBlank(sqlBean.getStartArea())) {
            sb.append(" and start_area=?");
            returnList.add(sqlBean.getStartArea());
        }
        //目的地
        long startCoordX = sqlBean.getStartCoordX();
        long startCoordY = sqlBean.getStartCoordY();
        long startRange = sqlBean.getStartRange();
        sb.append(" and dest_coord_x>=?");
        returnList.add(startCoordX - startRange);
        sb.append(" and dest_coord_x<=?");
        returnList.add(startCoordX + startRange);
        sb.append(" and dest_coord_y>=?");
        returnList.add(startCoordY - startRange);
        sb.append(" and dest_coord_y<=?");
        returnList.add(startCoordY + startRange);

        if (Constant.QUERY_TYPE_PULL_UP == sqlBean.getQueryType()) {
            //上拉 查询当天的历史数据
            sb.append(" and id < ?");
            returnList.add(sqlBean.getTsId());
            sb.append(" and id > ?");
            returnList.add(todayMinId);

        } else {
            sb.append(" and id > ?");
            returnList.add(sqlBean.getTsId());
        }
        sb.append(" order by id desc ");
        DoSqlParamBean sqlParamBean = new DoSqlParamBean();
        sqlParamBean.setPram(returnList);
        sqlParamBean.setSql(sb.toString());
        logger.debug("getSqlForProvince: sql= " + sqlParamBean.toString());

        return sqlParamBean;
    }

    /**
     * 拼查询sql
     *
     * @param sqlBean    查询条件
     * @param todayMinId 今日发货最小id
     * @return doSqlParamBean
     */
    private DoSqlParamBean getSqlForTransport(TransportSearchSqlBean sqlBean, long todayMinId) {
        StringBuilder sb = new StringBuilder("select  *  from tyt_transport where 1=1 ");
        // 发布中货物 、可显示货源、增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表
        sb.append("and status=1")
                .append(" and display_type=1")
                .append(" and is_display=1");

        List<Object> returnList = new ArrayList<>(200);
        //出发地
        long startCoordX = sqlBean.getStartCoordX();
        long startCoordY = sqlBean.getStartCoordY();
        long startRange = sqlBean.getStartRange();
        sb.append(" and start_coord_x>=?");
        returnList.add(startCoordX - startRange);
        sb.append(" and start_coord_x<=?");
        returnList.add(startCoordX + startRange);
        sb.append(" and start_coord_y>=?");
        returnList.add(startCoordY - startRange);
        sb.append(" and start_coord_y<=?");
        returnList.add(startCoordY + startRange);
        //目的地
        if (sqlBean.getDestCoordX() != 0 || sqlBean.getDestCoordY() != 0) {
            long destCoordX = sqlBean.getDestCoordX();
            long destCoordY = sqlBean.getDestCoordY();
            long destRange = sqlBean.getDestRange();
            sb.append(" and dest_coord_x>=?");
            returnList.add(destCoordX - destRange);
            sb.append(" and dest_coord_x<=?");
            returnList.add(destCoordX + destRange);
            sb.append(" and dest_coord_y>=?");
            returnList.add(destCoordY - destRange);
            sb.append(" and dest_coord_y<=?");
            returnList.add(destCoordY + destRange);
        }
        if (Constant.QUERY_TYPE_PULL_UP == sqlBean.getQueryType()) {
            //上拉 查询当天的历史数据
            sb.append(" and id < ?");
            returnList.add(sqlBean.getTsId());
            sb.append(" and id > ?");
            returnList.add(todayMinId);
        } else {
            sb.append(" and id > ?");
            returnList.add(sqlBean.getTsId());
        }
//        if (sqlBean.getUserType() != null && sqlBean.getUserType() == 1) { // 如果用户未付费会员时
//            sb.append(" and user_type =?");
//            returnList.add(sqlBean.getUserType());
//        }
//        if (StringUtils.isNotBlank(sqlBean.getCarLength())) { // 车辆长度
//            sb.append(" and car_length =?");
//            returnList.add(sqlBean.getCarLength());
//        }
//        if (StringUtils.isNotBlank(sqlBean.getCarType())) { // 车辆类型
//            sb.append(" and car_type =?");
//            returnList.add(sqlBean.getCarType());
//        }
//        if (StringUtils.isNotBlank(sqlBean.getSpecialRequired())) { // 特殊要求
//            sb.append(" and special_required =?");
//            returnList.add(sqlBean.getSpecialRequired());
//        }
        if (sqlBean.getStartWeight() != null) {
            sb.append(" and weight>0");
            sb.append(" and refer_weight>=?");
            returnList.add(sqlBean.getStartWeight() * 100);
        }
        if (sqlBean.getEndWeight() != null) {
            sb.append(" and weight>0");
            sb.append(" and refer_weight<=?");
            returnList.add(sqlBean.getEndWeight() * 100);
        }
        if (sqlBean.getMinLength() != null) {
            sb.append(" and length>0");
            sb.append(" and refer_length>=?");
            returnList.add(sqlBean.getMinLength().movePointRight(2));
        }
        if (sqlBean.getMaxLength() != null) {
            sb.append(" and length>0");
            sb.append(" and refer_length<=?");
            returnList.add(sqlBean.getMaxLength().movePointRight(2));
        }
        if (sqlBean.getMinWidth() != null) {
            sb.append(" and wide>0");
            sb.append(" and refer_width>=?");
            returnList.add(sqlBean.getMinWidth().movePointRight(2));
        }
        if (sqlBean.getMaxWidth() != null) {
            sb.append(" and wide>0");
            sb.append(" and refer_width<=?");
            returnList.add(sqlBean.getMaxWidth().movePointRight(2));
        }
        if (sqlBean.getMinHeight() != null) {
            sb.append(" and high>0");
            sb.append(" and refer_height>=?");
            returnList.add(sqlBean.getMinHeight().movePointRight(2));
        }
        if (sqlBean.getMaxHeight() != null) {
            sb.append(" and high>0");
            sb.append(" and refer_height<=?");
            returnList.add(sqlBean.getMaxHeight().movePointRight(2));
        }
        if (sqlBean.getMinPrice() != null) {
            sb.append(" and price>=?");
            returnList.add(sqlBean.getMinPrice());
        }
        if (sqlBean.getMaxPrice() != null) {
            sb.append(" and price<=?");
            returnList.add(sqlBean.getMaxPrice());
        }
        if (sqlBean.getStartLoadingTime() != null) {
            sb.append(" and (loading_time>=? OR loading_time IS NULL) ");
            returnList.add(sqlBean.getStartLoadingTime());
        }
        if (sqlBean.getEndLoadingTime() != null) {
            sb.append(" and (loading_time<=? OR loading_time IS NULL) ");
            returnList.add(sqlBean.getEndLoadingTime());
        }
        if(CollectionUtils.isNotEmpty(sqlBean.getGoodsName())) {
            List<String> goodsName = sqlBean.getGoodsName();
            sb.append(" and (");
            for (String s : goodsName) {
                if(StringUtils.isNotBlank(s)) {
                    sb.append(" task_content like ? or");
                    returnList.add("%"+s+"%");
                }
            }
            sb.delete(sb.lastIndexOf("or"),  sb.lastIndexOf("or")+2).append(") ");
        }
        if(null != sqlBean.getShieldingShipperSet() && !sqlBean.getShieldingShipperSet().isEmpty()){

            sb.append(" and  user_id not in ('" + StringUtils.replace(StringUtils.join(sqlBean.getShieldingShipperSet(), ","), ",", "','") + "')");
        }

        sb.append(" order by id desc ");
        DoSqlParamBean sqlParamBean = new DoSqlParamBean();
        sqlParamBean.setPram(returnList);
        sqlParamBean.setSql(sb.toString());
        logger.debug("getSqlForTransportSearch: sql= " + sqlParamBean.toString());
        return sqlParamBean;
    }

    /**
     * 找货列表缓存key
     *
     * @param start_coord_x    出发地X
     * @param start_coord_y    出发地Y
     * @param dest_coord_x     目的地X
     * @param dest_coord_y     目的地Y
     * @param queryType        上拉下滑
     * @param tsId             货源id
     * @param startRange       出发地范围
     * @param destRange        目的地范围
     * @param userType         是否是vip发货
     * @param carLength        车辆长度
     * @param carType          车辆类型
     * @param specialRequired  特殊要求
     * @param startWeight      最小重量
     * @param endWeight        最大重量
     * @param goodsName        货物名称
     * @param minLength        最小长度
     * @param maxLength        最大长度
     * @param minWidth         最小宽度
     * @param maxWidth         最大宽度
     * @param minHeight        最小高度
     * @param maxHeight        最大高度
     * @param startLoadingTime 装货开始时间
     * @param endLoadingTime   装货截止时间
     * @param minPrice         最小运价
     * @param maxPrice         最大运价
     * @return 字符串
     */
    public String getTransportCacheKey(long start_coord_x, long start_coord_y, long dest_coord_x, long dest_coord_y,
                                       int queryType, long tsId, long startRange, long destRange, Integer userType,
                                       String carLength, String carType, String specialRequired,
                                       Integer startWeight, Integer endWeight, List<String> goodsName,
                                       BigDecimal minLength, BigDecimal maxLength, BigDecimal minWidth, BigDecimal maxWidth,
                                       BigDecimal minHeight, BigDecimal maxHeight, Date startLoadingTime, Date endLoadingTime,
                                       Integer minPrice, Integer maxPrice
    ) {
        StringBuffer sb = new StringBuffer(Constant.CACHE_TRANSPORT_LIST_KEY);
        sb.append("sx_");
        sb.append(start_coord_x);
        sb.append("_sy_");
        sb.append(start_coord_y);
        sb.append("_sr_");
        sb.append(startRange);
        if (dest_coord_x != 0 || dest_coord_y != 0) {
            sb.append("_dx_");
            sb.append(dest_coord_x);
            sb.append("_dy_");
            sb.append(dest_coord_y);
            sb.append("_dr_");
            sb.append(destRange);
        }
        sb.append("_qt_");
        sb.append(queryType);
        sb.append("_ts_");
        sb.append(tsId);
        if (userType != null && userType > 0) {
            sb.append("_ut_");
            sb.append(userType);
        }
        if (StringUtils.isNotBlank(carLength)) {
            sb.append("_cl_");
            sb.append(carLength);
        }
        if (StringUtils.isNotBlank(carType)) {
            sb.append("_ct_");
            sb.append(carType);
        }
        if (StringUtils.isNotBlank(specialRequired)) {
            sb.append("_sr_");
            sb.append(specialRequired);
        }
        if (startWeight != null) {
            sb.append("_sw_");
            sb.append(startWeight);
        }
        if (endWeight != null) {
            sb.append("_ew_");
            sb.append(endWeight);
        }
        if (CollectionUtils.isNotEmpty(goodsName)) {
            sb.append("_gn_");
            for(String s: goodsName) {
                sb.append(s);
            }
        }
        if (minLength != null) {
            sb.append("_il_");
            sb.append(minLength);
        }
        if (maxLength != null) {
            sb.append("_al_");
            sb.append(maxLength);
        }
        if (minWidth != null) {
            sb.append("_iw_");
            sb.append(minWidth);
        }
        if (maxWidth != null) {
            sb.append("_aw_");
            sb.append(maxWidth);
        }
        if (minHeight != null) {
            sb.append("_ih_");
            sb.append(minHeight);
        }
        if (maxHeight != null) {
            sb.append("_ah_");
            sb.append(maxHeight);
        }
        if (startLoadingTime != null) {
            sb.append("_slt_");
            sb.append(startLoadingTime.getTime());
        }
        if (endLoadingTime != null) {
            sb.append("_elt_");
            sb.append(endLoadingTime.getTime());
        }
        if (minPrice != null) {
            sb.append("_ip_");
            sb.append(minPrice);
        }
        if (maxPrice != null) {
            sb.append("_ap_");
            sb.append(maxPrice);
        }
        return sb.toString();
    }

    /**
     * 拼查询sql
     *
     * @param sqlBean    查询条件
     * @param todayMinId 今日发货最小id
     * @return doSqlParamBean
     */
    private DoSqlParamBean getSqlForDestTransport(DestTransportSearchBean sqlBean, int searchSize, long todayMinId) {
        StringBuilder sb = new StringBuilder("select  *  from tyt_transport where 1=1 ");
        // 发布中货物 、可显示货源、增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表
        sb.append("and status=1")
                .append(" and display_type=1")
                .append(" and is_display=1");

        List<Object> returnList = new ArrayList<>();
        //出发地
        sb.append(" and start_provinc=?");
        returnList.add(sqlBean.getStartProvinc());
        sb.append(" and start_city=?");
        returnList.add(sqlBean.getStartCity());
        if (StringUtils.isNotBlank(sqlBean.getStartArea())) {
            sb.append(" and start_area=?");
            returnList.add(sqlBean.getStartArea());
        }
        //目的地
        if(StringUtils.isNotBlank(sqlBean.getDestProvinc())) {
            sb.append(" and dest_provinc=?");
            returnList.add(sqlBean.getDestProvinc());
        }
        if(StringUtils.isNotBlank(sqlBean.getDestCity())) {
            sb.append(" and dest_city=?");
            returnList.add(sqlBean.getDestCity());
        }
        if (StringUtils.isNotBlank(sqlBean.getDestArea())) {
            sb.append(" and dest_area=?");
            returnList.add(sqlBean.getDestArea());
        }
//        if(sqlBean.getGoodsId() != null) {
//            sb.append(" and src_msg_id <> ?");
//            returnList.add(sqlBean.getGoodsId());
//        }
        sb.append(" and id > ?");
        returnList.add(todayMinId);
        sb.append(" order by id desc ");
        DoSqlParamBean sqlParamBean = new DoSqlParamBean();
        sqlParamBean.setPram(returnList);
        sqlParamBean.setSql(sb.toString());
        logger.info("getSqlForDestTransportSearch: sql= " + sqlParamBean.toString());
        return sqlParamBean;
    }

    /**
      * <AUTHOR> Lion
      * @Description 拼接查询相似货源时间顺序或信用等级sql及param
      * @Param [bean, onOff, filterUserIds, ranklevelOnOff]
      * @return com.tyt.transport.querybean.DoSqlParamBean
      * @Date 2022/6/16 15:14
      */
    private DoSqlParamBean getSqlForSimilarTransport(SimilarTransportSearchBean bean, Integer onOff, String filterUserIds, String ranklevelOnOff) {
        StringBuilder sb = new StringBuilder("select  *  from tyt_transport where 1=1 ");
        // 发布中货物 、可显示货源、增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表
        sb.append(" and status=1")
                .append(" and display_type=1")
                .append(" and is_display=1");

        List<Object> returnList = new ArrayList<>();
        sb.append(" and src_msg_id<>?");
        returnList.add(bean.getSimilarityFirstId());
        if (!StringUtils.isBlank(filterUserIds)) {
            sb.append(" and user_id not in (" + filterUserIds + ")");
        }
        if( onOff==2 && !StringUtils.isBlank(ranklevelOnOff)){
            sb.append(" and rank_level in (" + ranklevelOnOff + ")");
        }
        sb.append(" and similarity_code=?");
        returnList.add(bean.getSimilarityCode());
        if (onOff != 2) {
            sb.append(" order by id desc ");
        } else {
            sb.append(" order by total_score desc , id asc ");
        }
        DoSqlParamBean sqlParamBean = new DoSqlParamBean();
        sqlParamBean.setPram(returnList);
        sqlParamBean.setSql(sb.toString());
        logger.info("getSqlForSimilarTransportSearch: sql= " + sqlParamBean.toString());
        return sqlParamBean;

    }
    /**
      * <AUTHOR> Lion
      * @Description 拼接查询相似货源信用等级用时间排序的sql及param
      * @Param [bean, onOff, filterUserIds, ranklevelOnOff]
      * @return com.tyt.transport.querybean.DoSqlParamBean
      * @Date 2022/6/16 15:16
      */
    private DoSqlParamBean getSqlForSimilarTransports(SimilarTransportSearchBean bean, Integer onOff, String filterUserIds, String ranklevelOnOff) {
        StringBuilder sb = new StringBuilder("select  *  from tyt_transport where 1=1 ");
        // 发布中货物 、可显示货源、增加精准货源过滤 0是精准货源 1 精准货源已经到货源列表
        sb.append(" and status=1")
                .append(" and display_type=1")
                .append(" and is_display=1");

        List<Object> returnList = new ArrayList<>();
        sb.append(" and src_msg_id<>?");
        returnList.add(bean.getSimilarityFirstId());
        if (!StringUtils.isBlank(filterUserIds)) {
            sb.append(" and user_id not in (" + filterUserIds + ")");
        }
        if(!StringUtils.isBlank(ranklevelOnOff)){
            sb.append(" and rank_level not in (" + ranklevelOnOff + ")");
        }
        sb.append(" and similarity_code=?");
        returnList.add(bean.getSimilarityCode());
        sb.append(" order by id asc ");
        DoSqlParamBean sqlParamBean = new DoSqlParamBean();
        sqlParamBean.setPram(returnList);
        sqlParamBean.setSql(sb.toString());
        logger.info("getSqlForSimilarTransportSearch: sql= " + sqlParamBean.toString());
        return sqlParamBean;

    }

}
