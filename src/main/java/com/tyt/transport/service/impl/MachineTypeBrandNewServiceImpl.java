package com.tyt.transport.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytMachineTypeBrandNew;
import com.tyt.model.TytSearchWord;
import com.tyt.plat.service.dic.TransportDictionaryService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.enums.MachineTypeEnum;
import com.tyt.transport.service.MachineTypeBrandNewService;
import com.tyt.transport.service.SearchWordService;
import com.tyt.transport.service.TransportNullifyService;
import com.tyt.transport.service.TytKeywordShortTransformService;
import com.tyt.transport.vo.MachineTypeVo;
import com.tyt.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service("machineTypeBrandNewService")
public class MachineTypeBrandNewServiceImpl extends BaseServiceImpl<TytMachineTypeBrandNew, Integer> implements MachineTypeBrandNewService {

    private static String BRAND_TOP_TYPE_KEY = "brand_top_type";
    private static String ACCURATE_WORDS_KEY = "accurate_words_key";

    @Resource(name = "searchWordService")
    private SearchWordService searchWordService;

    @Resource(name = "tytKeywordShortTransformService")
    private TytKeywordShortTransformService tytKeywordShortTransformService;

    @Resource(name = "transportNullifyService")
    private TransportNullifyService transportNullifyService;

    @Resource(name = "machineTypeBrandNewService")
    private MachineTypeBrandNewService machineTypeBrandNewService;

    @Resource(name = "transportDictionaryServiceImpl")
    private TransportDictionaryService transportDictionaryService;

    @Resource(name = "machineTypeBrandNewDao")
    public void setBaseDao(BaseDao<TytMachineTypeBrandNew, Integer> machineTypeBrandNewDao) {
        super.setBaseDao(machineTypeBrandNewDao);
    }

    private void addInfo(String keyword, String userId) {
        /*
         * 保存关键词搜索记录,如果是emoji表情则不保存
         */
        Pattern pattern = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]");
        Matcher matcher = pattern.matcher(keyword);
        if (!matcher.find()) {
            /*
             * 保存关键词搜索记录
             */
            searchWordService.add(new TytSearchWord(keyword, new Date(), userId != null ? Integer.valueOf(userId) : null));
        }
    }

    @Override
    public List<TytMachineTypeBrandNew> queryMachineTypeGroupByKeyword(String keyword, String userId) {
        if (StringUtils.isBlank(keyword)) {
            return new ArrayList<>();
        }

        keyword = keyword.replaceAll(" ", "");

        List<TytMachineTypeBrandNew> machineTypeResult = null;
        /*
         * 根据关键词从缓存中搜索匹配项对应的机器类型
         */
        String keyMd5 = MD5Util.GetMD5Code(keyword);
        log.info("queryMachineTypeGroupByKeyword_md5 -> keyword: {}, md5 : {}", keyword, keyMd5);
        String matchineTypeCacheKey = "machine:type:brand:new:group:cache:";
        String machineTypeCacheResult = RedisUtil.get(matchineTypeCacheKey + keyMd5);
        /*
         * 如果缓存中有数据则直接解析数据, 否则从数据库中查询
         */
        if (machineTypeCacheResult != null) {
            machineTypeResult = JSON.parseArray(machineTypeCacheResult, TytMachineTypeBrandNew.class);
        } else {
            String sql = "SELECT * FROM `tyt_machine_type_brand_new` where general_matches_item like ? order by score  LIMIT 50";
            final Object[] params = {
                    "%" + keyword + "%"
            };
            machineTypeResult = this.getBaseDao().queryForList(sql, params);
            if (CollectionUtils.isNotEmpty(machineTypeResult)) {
                RedisUtil.set(matchineTypeCacheKey + keyword, JSON.toJSONString(machineTypeResult), 300);
                addInfo(keyword, userId);
            }
        }
        return machineTypeResult;
    }

    @Override
    public List<TytMachineTypeBrandNew> queryMachineTypeByKeyword(String keyword, String userId) {
        List<TytMachineTypeBrandNew> machineTypeResult = null;

        /*
         * 根据关键词从缓存中搜索匹配项对应的机器类型
         */
        String matchineTypeCacheKey = "machine:type:brand:new:cache:";
        String machineTypeCacheResult = RedisUtil.get(matchineTypeCacheKey + keyword);
        /*
         * 如果缓存中有数据则直接解析数据, 否则从数据库中查询
         */
        if (machineTypeCacheResult != null) {
            machineTypeResult = JSON.parseArray(machineTypeCacheResult, TytMachineTypeBrandNew.class);
        } else {
            String sql = "SELECT * FROM `tyt_machine_type_brand_new` where general_matches_item like ? order by score LIMIT 50";
            final Object[] params = {
                    "%" + keyword + "%"
            };
            List<TytMachineTypeBrandNew> machineTypeDbResult = this.getBaseDao().queryForList(sql, params);
            if (machineTypeDbResult.size() > 0) {
                addInfo(keyword, userId);
                machineTypeResult = new ArrayList<>(); // 避免set值hib将数据库进行更新
                for (TytMachineTypeBrandNew dbMachine : machineTypeDbResult) {
                    TytMachineTypeBrandNew machine = new TytMachineTypeBrandNew();
                    BeanUtils.copyProperties(dbMachine, machine);
                    /**
                     * 拼接规则：
                     *   数据全：品牌+型号全称+空格+二级分类+两个空格+吨位+两个空格+尺寸信息
                     *   尺寸信息：如果只有两个值的拼写方式 长1米*宽2米
                     */
                    StringBuilder showName = new StringBuilder();
                    showName.append(StringUtils.defaultIfBlank(machine.getBrand(), ""));
                    showName.append(StringUtils.defaultIfBlank(machine.getTopType(), ""));
                    if (StringUtils.isNotBlank(machine.getBrand()) || StringUtils.isNotBlank(machine.getTopType())) {
                        showName.append(" "); // 一个空格
                    }
                    showName.append(StringUtils.defaultIfBlank(machine.getSecondClass(), ""));
                    if (StringUtils.isNotBlank(machine.getSecondClass())) {
                        showName.append("  "); // 两个空格
                    }
                    showName.append(getShowNameArg(machine));
                    machine.setShowName(showName.toString());
                    machine.setShowNameNew(dbMachine.getShowName());
                    machine.setShowNameArg(getShowNameArg(machine));
                    machineTypeResult.add(machine);
                }

                RedisUtil.set(matchineTypeCacheKey + keyword, JSON.toJSONString(machineTypeResult), 300);
            }
        }
        return machineTypeResult;
    }

    /**
     * 查参数 处理长宽高重
     *
     * @param machine
     * @return
     */
    public String getShowNameArg(TytMachineTypeBrandNew machine) {
        StringBuilder showName = new StringBuilder();
        if (machine.getWeight() != null) {
            showName.append(machine.getWeight().stripTrailingZeros().toPlainString()).append("吨").append(" ");
        }
        // 处理长宽高不同的匹配样式
        if (machine.getLength() != null && machine.getWidth() != null && machine.getHeight() != null) {
            if (machine.getLength() != null) {
                showName.append(machine.getLength().stripTrailingZeros().toPlainString()).append("*");
            }
            if (machine.getWidth() != null) {
                showName.append(machine.getWidth().stripTrailingZeros().toPlainString()).append("*");
            }
            if (machine.getHeight() != null) {
                showName.append(machine.getHeight().stripTrailingZeros().toPlainString()).append("米");
            }
        } else if (machine.getLength() == null || machine.getWidth() == null || machine.getHeight() == null) {
            if (machine.getLength() != null) {
                showName.append("长").append(machine.getLength().stripTrailingZeros().toPlainString()).append("米");
                if (machine.getWidth() != null || machine.getHeight() != null) {
                    showName.append("*");
                }
            }
            if (machine.getWidth() != null) {
                showName.append("宽").append(machine.getWidth().stripTrailingZeros().toPlainString()).append("米");
                if (machine.getHeight() != null) {
                    showName.append("*");
                }
            }
            if (machine.getHeight() != null) {
                showName.append("高").append(machine.getHeight().stripTrailingZeros().toPlainString()).append("米");
            }
        }
        // 处理尾部空字符串
        if (showName.length() != 0) {
            for (int i = 0; i < showName.length(); i++) {
                if (showName.charAt(showName.length() - 1) != ' ') {
                    break;
                }
                showName.deleteCharAt(showName.length() - 1);
            }
        }

        return showName.toString();
    }

    @Override
    public TytMachineTypeBrandNew getByMatchItemId(Integer matchItemId) {
        return this.getById(matchItemId);
    }

    @Override
    public TytMachineTypeBrandNew getByShowName(String showName) {
        String sql = "SELECT * FROM `tyt_machine_type_brand_new` where show_name = ? LIMIT 1";
        final Object[] params = {
                showName
        };
        List<TytMachineTypeBrandNew> machineTypeResult = this.getBaseDao().queryForList(sql, params);
        if (CollectionUtils.isNotEmpty(machineTypeResult)) {
            return machineTypeResult.get(0);
        }
        return null;
    }

    @Override
    public MachineTypeVo searchMatchesName(String keyword, String brand, String topType, String userId) throws Exception {

        MachineTypeVo machineTypeVo = new MachineTypeVo();
        machineTypeVo.setKeyword(keyword);
        // 判断是否是非法货名
        String illegalName = transportNullifyService.verifyNullifyTaskContent(keyword);
        if (StringUtils.isNotBlank(illegalName)) {
            machineTypeVo.setMachineType(MachineTypeEnum.illegal.code);
            return machineTypeVo;
        }
        // 判断是否是标准货名
        String standardName = tytKeywordShortTransformService.queryKeywordShortByKeyword(keyword);
        List<TytMachineTypeBrandNew> machineTypeBrandNewList = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(standardName, userId);

        if (CollectionUtils.isEmpty(machineTypeBrandNewList)) {
            machineTypeBrandNewList = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(keyword, userId);
        }
        if (CollectionUtils.isEmpty(machineTypeBrandNewList)) {
            // 判断是否是固有名词,取第一个
            List<String> dictionaryKeyWordsList = transportDictionaryService.verifyDictionaryMatches(standardName);
            if (CollUtil.isNotEmpty(dictionaryKeyWordsList)) {
                String dictionaryKeyWord = getDictionaryKeyWord(dictionaryKeyWordsList, standardName);
                machineTypeBrandNewList = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(dictionaryKeyWord, userId);
            }

        }
        if (CollectionUtils.isNotEmpty(machineTypeBrandNewList)) {
            if (StringUtils.isNotBlank(brand)) {
                for (int i = 0; i < machineTypeBrandNewList.size(); i++) {
                    TytMachineTypeBrandNew tytMachineTypeBrandNew = machineTypeBrandNewList.get(i);
                    String showName = tytMachineTypeBrandNew.getShowName();
                    if (showName.contains(brand)) {
                        machineTypeBrandNewList.remove(tytMachineTypeBrandNew);
                        machineTypeBrandNewList.add(0, tytMachineTypeBrandNew);
                    }
                }
            }

            for (int i = 0; i < machineTypeBrandNewList.size(); i++) {
                TytMachineTypeBrandNew tytMachineTypeBrandNew = machineTypeBrandNewList.get(i);
                String showName = tytMachineTypeBrandNew.getShowName();
                String completeKeyword = keyword;
                if (StringUtils.isNotBlank(topType)) {
                    completeKeyword = topType + completeKeyword;
                }
                if (StringUtils.isNotBlank(brand)) {
                    completeKeyword = brand + completeKeyword;
                }
                if (showName.equals(completeKeyword) || showName.equals(standardName)) {
                    machineTypeBrandNewList.remove(tytMachineTypeBrandNew);
                    machineTypeBrandNewList.add(0, tytMachineTypeBrandNew);
                    break;
                }

                if (showName.equals(keyword)) {
                    machineTypeBrandNewList.remove(tytMachineTypeBrandNew);
                    machineTypeBrandNewList.add(0, tytMachineTypeBrandNew);
                    break;
                }
            }
            Set<String> machineShowNameSet = machineTypeBrandNewList.stream().map(TytMachineTypeBrandNew::getShowName).collect(Collectors.toSet());
            // 如果查出来的数量不包含全匹配的名称，重新查一下该词
            if (!machineShowNameSet.contains(keyword)) {
                List<TytMachineTypeBrandNew> accurateMachineTypeBrandNewList = this.searchAccurate(keyword);
                if (CollUtil.isNotEmpty(accurateMachineTypeBrandNewList)) {
                    TytMachineTypeBrandNew accurateKeyWordsMachineType = accurateMachineTypeBrandNewList.get(0);
                    machineTypeBrandNewList.add(0, accurateKeyWordsMachineType);
                }
            }

            machineTypeVo.setMachineType(MachineTypeEnum.conformance.code);
            machineTypeVo.setList(machineTypeBrandNewList);
        } else {
            machineTypeVo.setMachineType(MachineTypeEnum.audit.code);
        }
        return machineTypeVo;
    }

    private List<TytMachineTypeBrandNew> searchAccurate(String keyword) {
        List<TytMachineTypeBrandNew> machineTypeBrandNewList = new ArrayList<>();
        if (StringUtils.isNotBlank(keyword)) {
            String accurateWordsKey = ACCURATE_WORDS_KEY + keyword;
            machineTypeBrandNewList = RedisUtil.getObjectList(accurateWordsKey);
            if (CollUtil.isEmpty(machineTypeBrandNewList)) {
                String sql = "SELECT  * FROM `tyt_machine_type_brand_new` where show_name = ?";
                Object[] params = new Object[]{keyword};
                machineTypeBrandNewList = this.getBaseDao().queryForList(sql, params);
                if (CollectionUtils.isNotEmpty(machineTypeBrandNewList)) {
                    RedisUtil.setObjectList(accurateWordsKey, machineTypeBrandNewList, 10 * 60);
                }
            }
        }
        return machineTypeBrandNewList;
    }


    private static String getDictionaryKeyWord(List<String> dictionaryKeyWordsList, String standardName) {
        String matchKeyWord = dictionaryKeyWordsList.get(0);
        int firstIndex = standardName.length();
        for (String dictionaryKeyWords : dictionaryKeyWordsList) {
            int index = standardName.indexOf(dictionaryKeyWords);
            if (index < firstIndex) {
                firstIndex = index;
                matchKeyWord = dictionaryKeyWords;
            }
        }
        return matchKeyWord;
    }

    @Override
    public List<String> getTopType(String brand) {
        String topTypeKey = BRAND_TOP_TYPE_KEY + brand;
        List<String> topTypeList = RedisUtil.getList(topTypeKey);
        if (CollUtil.isEmpty(topTypeList)) {
            String sql = "SELECT  * FROM `tyt_machine_type_brand_new` where top_type is not null and top_type !='' ";
            Object[] params = null;
            if (StringUtils.isNotBlank(brand)) {
                sql = sql + "and brand = ? ";
                params = new Object[]{brand};
            }
            sql = sql + "group by top_type limit 50";
            List<TytMachineTypeBrandNew> machineTypeBrandNewList = this.getBaseDao().queryForList(sql, params);
            if (CollectionUtils.isNotEmpty(machineTypeBrandNewList)) {
                topTypeList = machineTypeBrandNewList.stream().map(TytMachineTypeBrandNew::getTopType).collect(Collectors.toList());
                RedisUtil.setList(topTypeKey, topTypeList, 10 * 60);
                return topTypeList;
            }
            topTypeList = new ArrayList<>();
        }
        return topTypeList;
    }
}
