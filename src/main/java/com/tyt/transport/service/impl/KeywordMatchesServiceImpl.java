package com.tyt.transport.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.TytKeywordMatches;
import com.tyt.model.TytMachineType;
import com.tyt.model.TytSearchWord;
import com.tyt.transport.service.KeywordMatchesService;
import com.tyt.transport.service.MachineTypeService;
import com.tyt.transport.service.SearchWordService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;

@SuppressWarnings("deprecation")
@Service("keywordMatchesService")
public class KeywordMatchesServiceImpl extends BaseServiceImpl<TytKeywordMatches, Long> implements KeywordMatchesService {
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource(name = "searchWordService")
	private SearchWordService searchWordService;
	@Resource(name = "tytConfigService")
	private TytConfigService configService;
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	@Resource(name = "machineTypeService")
	private MachineTypeService machineTypeService;

	@Resource(name = "keywordMatchesDao")
	public void setBaseDao(BaseDao<TytKeywordMatches, Long> keywordMatchesDao) {
		super.setBaseDao(keywordMatchesDao);
	}

	@Override
	public List<TytMachineType> queryMachineTypeByKeyword(String keyword, String userId) {
		List<TytMachineType> machineTypeResult = null;
		/*
		 * 保存关键词搜索记录,如果是emoji表情则不保存
		 */
		Pattern pattern = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]");
		Matcher matcher = pattern.matcher(keyword);
		if (!matcher.find()) {
			searchWordService.add(new TytSearchWord(keyword, new Date(), userId != null ? Integer.valueOf(userId) : null));
		}
		/*
		 * 根据关键词从缓存中搜索匹配项对应的机器类型
		 */
		String matchineTypeCacheKey = configService.getStringValue(Constant.MACHINE_TYPE_CACHE_KEY);
		if (matchineTypeCacheKey == null) {
			matchineTypeCacheKey = "machine_type_cache_";
		}
		String machineTypeCacheResult = cacheService.getString(matchineTypeCacheKey + keyword);
		logger.info("get machine type cache by key [ " + (matchineTypeCacheKey + keyword) + " ], result is [ " + machineTypeCacheResult + " ]");
		/*
		 * 如果缓存中有数据则直接解析数据, 否则从数据库中查询
		 */
		if (machineTypeCacheResult != null) {
			machineTypeResult = JSON.parseArray(machineTypeCacheResult, TytMachineType.class);
		} else {
			/*
			 * 先使用精确匹配查询，如果查询不到再使用通用匹配
			 */
			String sql = "SELECT tmt.`id`, tmt.`brand`, tmt.`machine_type` AS 'machineType', tmt.`type`, tmt.`weight`, tmt.`length`, tmt.`width`, tmt.`height` FROM tyt_keyword_matches tkm, tyt_machine_type tmt WHERE tkm.`keyword`=? AND tkm.`machine_type_id`=tmt.`id` ORDER BY tkm.`priority` ASC";
			Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
			scalarMap.put("id", Hibernate.LONG);
			scalarMap.put("brand", Hibernate.STRING);
			scalarMap.put("machineType", Hibernate.STRING);
			scalarMap.put("type", Hibernate.STRING);
			scalarMap.put("weight", Hibernate.STRING);
			scalarMap.put("length", Hibernate.STRING);
			scalarMap.put("width", Hibernate.STRING);
			scalarMap.put("height", Hibernate.STRING);
			machineTypeResult = this.getBaseDao().search(sql, scalarMap, TytMachineType.class, new Object[] { keyword }, 1, Constant.MATCHES_TYPE__CACHE_SIZE);
			/*
			 * 如果查询到结果则放到缓存中，如果没有查到则使用通用匹配查询
			 */
			if (machineTypeResult != null && machineTypeResult.size() > 0) {
				cacheMachineType(matchineTypeCacheKey + keyword, machineTypeResult);
			} else {
				machineTypeResult = machineTypeService.queryMachineTypeByGeneralMatch(Constant.MATCHES_TYPE__CACHE_SIZE, keyword);
				if (machineTypeResult != null && machineTypeResult.size() > 0) {
					cacheMachineType(matchineTypeCacheKey + keyword, machineTypeResult);
				}
			}
		}
		return machineTypeResult;
	}

	private void cacheMachineType(String key, List<TytMachineType> machineTypeResult) {
		cacheService.setObject(key, JSON.toJSONString(machineTypeResult), Constant.MATCHES_TYPE__CACHE_TIME);
	}
}
