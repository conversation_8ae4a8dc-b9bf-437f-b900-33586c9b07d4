package com.tyt.transport.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.acvitity.service.ActivityGradePrizeService;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.cache.CacheService;
import com.tyt.common.bean.SimplePageGradeBean;
import com.tyt.common.service.*;
import com.tyt.deposit.bean.ExcellentGoodsRemainCountBean;
import com.tyt.deposit.entity.base.TytDepositAccount;
import com.tyt.deposit.entity.base.TytDepositApplyAudit;
import com.tyt.deposit.entity.base.TytDepositAuthorization;
import com.tyt.deposit.enums.AuditEnsureTypeEnum;
import com.tyt.deposit.enums.AuditStatusEnum;
import com.tyt.deposit.pojo.dto.DepositBlockCheckDTO;
import com.tyt.deposit.service.DepositService;
import com.tyt.excellentgoodshomepage.service.ExcellentGoodsService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqEditTransportSyncMsg;
import com.tyt.infofee.bean.MqTransportAddMoneyMsg;
import com.tyt.infofee.bean.MqUserMsg;
import com.tyt.infofee.enums.*;
import com.tyt.infofee.service.FeeCalculateService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.invoicetransport.bean.TytInvoiceTransportEnterpriseConfigLogDTO;
import com.tyt.invoicetransport.service.InvoiceTransportService;
import com.tyt.invoicetransportconfiglog.service.InvoiceTransportConfigService;
import com.tyt.marketingActivity.bean.OrderEncourageBean;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.ConvertUtil;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.client.trade.infofee.ApiTradeInfoFeeClient;
import com.tyt.plat.client.trade.infofee.dto.UserPerformanceNumDTO;
import com.tyt.plat.client.transport.ThPriceClient;
import com.tyt.plat.client.transport.TransportDirectPublishClient;
import com.tyt.plat.client.transport.TransportTecserviceFeeClient;
import com.tyt.plat.client.transport.dto.*;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.constant.PlatBaseConstant;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.*;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.service.base.CityDataService;
import com.tyt.plat.service.remote.CarryPriceService;
import com.tyt.plat.service.ts.TransportHistoryService;
import com.tyt.plat.utils.DateUtil;
import com.tyt.plat.utils.NumberConvertUtil;
import com.tyt.plat.vo.TecServiceFeeConfigComputeResultVO;
import com.tyt.plat.vo.other.GoodCarPriceTransportTabAndBIPriceVO;
import com.tyt.plat.vo.other.GoodsSmallDO;
import com.tyt.plat.vo.remote.CarryPriceReq;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.plat.vo.ts.*;
import com.tyt.pricingRoute.service.PricingRouteConfigService;
import com.tyt.promo.model.VipNoticeForGoodsBean;
import com.tyt.promo.service.ICouponService;
import com.tyt.receive.service.OwnerCompanyLogService;
import com.tyt.receive.service.TransportBackendService;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.bean.CommissionTypeBean;
import com.tyt.transport.bean.DrawCommissionReq;
import com.tyt.transport.bean.GoodModelResult;
import com.tyt.transport.bean.PriceConfigBean;
import com.tyt.transport.enums.*;
import com.tyt.transport.enums.RefundFlagEnum;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.*;
import com.tyt.transport.service.CodeMappingService;
import com.tyt.transport.vo.CargoOwnerInfoVo;
import com.tyt.transport.vo.DepositAndRefundTypeVo;
import com.tyt.transport.vo.PopupConfigVo;
import com.tyt.transport.vo.TytTecServiceFeeConfigToComputResult;
import com.tyt.upgrade.service.UpgradeCheckService;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.querybean.SourceBean;
import com.tyt.user.service.*;
import com.tyt.util.*;
import com.tytrecommend.model.NewIdentity;
import com.tytrecommend.recommend.service.NewIdentityService;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.*;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;
import static cn.hutool.core.util.StrUtil.COMMA;
import static com.tyt.deposit.service.impl.DepositServiceImpl.BALANCE_TYPE_FROZEN;
import static com.tyt.deposit.service.impl.DepositServiceImpl.BALANCE_TYPE_NORMAL;
import static com.tyt.util.Constant.EXCELLENT_GOODS_PUBLISH_TIME;
import static com.tyt.util.StringUtil.isNumeric;
import static net.sf.jsqlparser.util.validation.metadata.NamedObject.user;

@Service("bsPublishTransportService")
@Slf4j
public class BsPublishTransportServiceImpl implements BsPublishTransportService {

    private static final String EXCLUSIVE_REG = "(?<!\\d)17\\.5米(?!吨)|(?<!\\d)17米5(?!\\d)(?!吨)|(?<!\\d)17\\.5(?!\\d)(?!米吨)";

    private static final String COMMISSION_STAGE_CONFIG_AB_NUM_CACHE = "commissionStageConfigABNumCache";

    @Resource(name = "tytUserSubService")
    TytUserSubService tytUserSubService;
    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "userTelService")
    private UserTelService userTelService;
    @Autowired
    private TransportGoodModelFactorService transportGoodModelFactorService;

    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "transportNullifyService")
    private TransportNullifyService transportNullifyService;
    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "tytMapDictService")
    TytMapDictService tytMapDictService;

    @Resource(name = "tytPageGradeService")
    TytPageGradeService tytPageGradeService;

    @Resource(name = "transportService")
    private TransportService transportService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "tytGeoDictService")
    private TytGeoDictService tytGeoDictService;

    @Resource(name = "tytSequenceService")
    private TytSequenceService tytSequenceService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;
    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;
    @Resource(name = "geographicalLocationService")
    private GeographicalLocationService geographicalLocationService;
    @Resource(name = "machineTypeBrandNewService")
    private MachineTypeBrandNewService machineTypeBrandNewService;
    @Resource(name = "upgradeCheckService")
    private UpgradeCheckService upgradeCheckService;
    @Resource(name = "bsPublishTransportService")
    private BsPublishTransportService bsPublishTransportService;
    @Resource
    private TransportOrdersService transportOrdersService;
    @Resource
    private TransportDoneService transportDoneService;
    @Autowired
    private TytCityMapper tytCityMapper;
    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;
    @Autowired
    private ThPriceClient thPriceClient;

    @Autowired
    private BackendTransportMapper backendTransportMapper;
    @Resource
    private TytIntelligentDepositConfigMapper tytIntelligentDepositConfigMapper;
    @Resource
    private TytIntelligentDepositUserRecordMapper tytIntelligentDepositUserRecordMapper;
    @Resource(name = "tytSourceService")
    private TytSourceService tytSourceService;
    @Autowired
    private TytTransportPriceUpService tytTransportPriceUpService;
    @Resource
    private CarService carService;

    @Autowired
    private CityDataService cityDataService;

    @Autowired
    private CarryPriceService carryPriceService;

    @Resource(name = "newIdentityService")
    private NewIdentityService newIdentityService;
    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Autowired
    private DispatchCargoOwnerMapper dispatchCargoOwnerMapper;
    @Autowired
    private TytDispatchCooperativeMapper tytDispatchCooperativeMapper;

    @Autowired
    private TytSpecialCarPriceConfigMapper tytSpecialCarPriceConfigMapper;

    @Autowired
    private ExcellentPriceConfigService excellentPriceConfigService;

    @Autowired
    private TransportAutoResendService transportAutoResendService;
    @Autowired
    private InvoiceTransportConfigService invoiceTransportConfigService;

    @Autowired
    private TytTransportPricePerkConfigMapper tytTransportPricePerkConfigMapper;

    /**
     * 专车货主管理-平台
     */
    public static final String PLAT_CARGO_OWNER_NAME = "平台";
    public static final String ROUTE_NOT_SUPPORT = "您当前路线暂不支持专车发布";
    public static final String TONNAGE_NOT_SUPPORT = "您当前吨位暂不支持专车发布";
    /**
     * 专车货主管理-宏信建发
     */
    public static final String HONG_XIN_JIAN_FA = "宏信建发";

    @Resource(name = "tytTransportSyncYmmService")
    private TytTransportSyncYmmService tytTransportSyncYmmService;

    @Resource(name = "couponService")
    private ICouponService couponService;

    @Autowired
    private TransportBackendService transportBackendService;


    @Autowired
    private OwnerCompanyLogService ownerCompanyLogService;
    @Autowired
    private CsMaintainedCustomService csMaintainedCustomService;

    @Autowired
    private TytTransportDispatchService tytTransportDispatchService;

    @Autowired
    private TransportHistoryService transportHistoryService;

    @Autowired
    private TytKeywordMatchesNewUnstandardMapper keywordMatchesNewUnstandardMapper;

    @Autowired
    private CommonApiService commonApiService;

    @Autowired
    private DepositService depositService;
    @Autowired
    private BlacklistUserService blacklistUserService;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;
    @Autowired
    private TransportPublishLogService transportPublishLogService;

    @Autowired
    private CodeMappingService codeMappingService;

    @Autowired
    private TytTransportMbMergeMapper tytTransportMbMergeMapper;

    @Autowired
    private TytMbCargoSyncInfoMapper tytMbCargoSyncInfoMapper;

    @Autowired
    private TytDispatchCompanyMapper tytDispatchCompanyMapper;

    @Autowired
    private TytSpecialCarBiRouteMapper tytSpecialCarBiRouteMapper;

    @Autowired
    private TransportYMMService transportYMMService;

    @Autowired
    private TytMachineTypeAuditService tytMachineTypeAuditService;

    /**
     * 根据车主用户ID和源消息ID获取技术服务费用
     *
     * @param carUserId 车主用户ID
     * @param srcMsgId  源消息ID
     * @return 包含技术服务费用信息的ResultMsgBean对象
     */
    @Autowired
    private TytCoverGoodsDialConfigService tytCoverGoodsDialConfigService;

    @Autowired
    private ExcellentGoodsService excellentGoodsService;

    @Autowired
    private InvoiceTransportService invoiceTransportService;

    @Autowired
    private TytUserIdentityLabelMapper tytUserIdentityLabelMapper;

    @Autowired
    private PricingRouteConfigService pricingRouteConfigService;

    @Autowired
    private PublicResourceService publicResourceService;

    @Autowired
    private FeeCalculateService feeCalculateService;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private TransportEventTrickingService transportEventTrickingService;

    @Autowired
    TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    @Autowired
    TytTecServiceFeeConfigMapper tytTecServiceFeeConfigMapper;

    @Autowired
    TytTecServiceFeeStageConfigMapper tytTecServiceFeeStageConfigMapper;

    @Autowired
    TytDrawCommissionRuleMapper tytDrawCommissionRuleMapper;
    @Autowired
    private GoodsRefreshManualService goodsRefreshManualService;

    @Autowired
    ApiTradeInfoFeeClient apiTradeInfoFeeClient;

    @Autowired
    TransportExtendService transportExtendService;

    @Autowired
    private ActivityGradePrizeService activityGradePrizeService;

    @Resource(name = "threadPoolExecutor")
    private ThreadPoolExecutor threadPoolExecutor;

    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;

    @Autowired
    private TransportTecserviceFeeClient transportTecserviceFeeClient;

    @Autowired
    private TytUserRecordMapper tytUserRecordMapper;

    @Autowired
    private TytTecServiceFeeProportionConfigMapper tytTecServiceFeeProportionConfigMapper;

    @Autowired
    private TytTecServiceFeeDiscountConfigMapper tytTecServiceFeeDiscountConfigMapper;

    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;

    @Autowired
    private TytTransportQuotedPriceMapper tytTransportQuotedPriceMapper;

    @Autowired
    private TransportDirectPublishClient transportDirectPublishClient;

    public static final String COMMISSION_PROPORTION_TRANSPORT_NUM = "commissionProportionTransportNum";

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public ResultMsgBean freightAddMoneyNum1(long userId, long tsId, String addPriceReq) throws Exception {
        logger.info("[压测接口] ********************************数据：{},{},{}", userId, tsId, addPriceReq);
        MqEditTransportSyncMsg syncMsg = new MqEditTransportSyncMsg();
        syncMsg.setAfterPrice(Integer.parseInt(addPriceReq));
        syncMsg.setSrcMsgId(tsId);
        syncMsg.setMessageType(MqBaseMessageBean.MB_SYNC_TRANSPORT_EDIT_MESSAGE);
        String messageSerailNum = SerialNumUtil.generateSeriaNum();
        syncMsg.setMessageSerailNum(messageSerailNum);
        tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(syncMsg), syncMsg.getMessageType());
        tytMqMessageService.sendMbMqMessage(messageSerailNum, JSON.toJSONString(syncMsg), syncMsg.getMessageType());
        return new ResultMsgBean();
    }

    /**
     * @return com.tyt.plat.vo.remote.CarryPriceVo
     * <AUTHOR> Lion
     * @Description 过滤BI接口报错
     * @Param [priceReq]
     * @Date 2022/8/1 17:35
     */
    private CarryPriceVo reqCarryPriceNoJudge(CarryPriceReq priceReq) {

        CarryPriceVo carryPrice = null;
        try {
            carryPrice = carryPriceService.getCarryPrice(priceReq);
        } catch (Exception e) {
            logger.debug("request_getCarryPrice_error : ", e);
        }
        if (carryPrice == null) {
            carryPrice = new CarryPriceVo();
        }
        logger.info("getCarryPrice_priceResult_返回参数: {}", JSON.toJSONString(carryPrice));
        return carryPrice;
    }

    @Override
    public CarryPriceVo reqCarryPrice(CarryPriceReq carryPriceReq) {
        Integer publishType = carryPriceReq.getPublishType();
        //0 全部关闭 1 全部打开 2 电议打开 3 一口价打开
        Integer checkPrice = tytConfigService.getIntValue(Constant.check_low_carry_price, 0);
        //为空默认关闭 电议1，一口价2
        if (Objects.isNull(checkPrice) || Objects.isNull(publishType)) {
            return null;
        }
        //为 0 一口价、电议 全部关闭
        if (checkPrice == 0) {
            return null;
        }

        if (checkPrice == 1 || (publishType == 1 && checkPrice == 2) || (publishType == 2 && checkPrice == 3)) {

            CarryPriceVo carryPrice = null;
            try {
                carryPrice = carryPriceService.getCarryPrice(carryPriceReq);
            } catch (Exception e) {
                logger.debug("request_getCarryPrice_error : ", e);
            }

            return carryPrice;
        }
        return null;
    }

    @Override
    public TransportCarryBean buildCarryBeanByTransport(Transport tytTransport) {

        TransportCarryBean transportCarryBean = new TransportCarryBean();
        transportCarryBean.setStartProvince(tytTransport.getStartProvinc());
        transportCarryBean.setStartCity(tytTransport.getStartCity());
        transportCarryBean.setStartArea(tytTransport.getStartArea());
        transportCarryBean.setDestProvince(tytTransport.getDestProvinc());
        transportCarryBean.setDestCity(tytTransport.getDestCity());
        transportCarryBean.setDestArea(tytTransport.getDestArea());
        transportCarryBean.setGoodsName(tytTransport.getTaskContent());
        transportCarryBean.setGoodsWeight(tytTransport.getWeight());
        transportCarryBean.setGoodsLength(tytTransport.getLength());
        transportCarryBean.setGoodsWide(tytTransport.getWide());
        transportCarryBean.setGoodsHigh(tytTransport.getHigh());

        transportCarryBean.setExcellentGoods(tytTransport.getExcellentGoods());

        transportCarryBean.setUserId(tytTransport.getUserId());

        transportCarryBean.setDistance(tytTransport.getDistance() != null ? tytTransport.getDistance().toString() : "0");

        transportCarryBean.setGoodTypeName(tytTransport.getGoodTypeName());

        return transportCarryBean;
    }

    private TransportCarryBean buildCarryBean(TransportPublishBean publishBean) {

        TransportCarryBean transportCarryBean = new TransportCarryBean();
        transportCarryBean.setStartProvince(publishBean.getStartProvinc());
        transportCarryBean.setStartCity(publishBean.getStartCity());
        transportCarryBean.setStartArea(publishBean.getStartArea());
        transportCarryBean.setDestProvince(publishBean.getDestProvinc());
        transportCarryBean.setDestCity(publishBean.getDestCity());
        transportCarryBean.setDestArea(publishBean.getDestArea());
        transportCarryBean.setGoodsName(publishBean.getTaskContent());
        transportCarryBean.setGoodsWeight(publishBean.getWeight());
        transportCarryBean.setGoodsLength(publishBean.getLength());
        transportCarryBean.setGoodsWide(publishBean.getWide());
        transportCarryBean.setGoodsHigh(publishBean.getHigh());

        transportCarryBean.setUserId(publishBean.getUserId());

        transportCarryBean.setDistance(publishBean.getDistance() != null ? publishBean.getDistance().toString() : "0");

        transportCarryBean.setGoodTypeName(publishBean.getGoodTypeName());

        return transportCarryBean;
    }


    private CarryPriceReq createCarryPriceReq(TransportPublishBean publishBean) {

        Long tsId = publishBean.getTsId();

        BigDecimal price = new BigDecimal(publishBean.getPrice());

        CarryPriceReq carryPriceReq = new CarryPriceReq();
        carryPriceReq.setStartProvince(publishBean.getStartProvinc());
        carryPriceReq.setStartCity(publishBean.getStartCity());
        carryPriceReq.setStartArea(publishBean.getStartArea());
        carryPriceReq.setDestProvince(publishBean.getDestProvinc());
        carryPriceReq.setDestCity(publishBean.getDestCity());
        carryPriceReq.setDestArea(publishBean.getDestArea());
        carryPriceReq.setGoodsName(publishBean.getTaskContent());
        carryPriceReq.setGoodsWeight(NumberConvertUtil.strToDouble(publishBean.getWeight()));
        carryPriceReq.setGoodsLength(NumberConvertUtil.strToDouble(publishBean.getLength()));
        carryPriceReq.setGoodsWide(NumberConvertUtil.strToDouble(publishBean.getWide()));
        carryPriceReq.setGoodsHigh(NumberConvertUtil.strToDouble(publishBean.getHigh()));

        carryPriceReq.setPublishType(publishBean.getPublishType());
        carryPriceReq.setUserId(publishBean.getUserId());
        carryPriceReq.setPrice(price);
        carryPriceReq.setSrcMsgId(tsId);
        carryPriceReq.setDistance(publishBean.getDistance() != null ? publishBean.getDistance().toString() : null);

        if (publishBean.getExcellentGoods() != null && publishBean.getExcellentGoods() == 1) {
            carryPriceReq.setSource(PriceSourceEnum.excellentGoods.getSource());
        } else {
            carryPriceReq.setSource(PriceSourceEnum.reference.getSource());
        }

        return carryPriceReq;
    }

    /**
     * 校验价格是否允许 .
     *
     * @return ResultMsgBean
     */
    private ResultMsgBean checkPriceAllow(TransportPublishBean publishBean) {

        // 专车货源不校验价格
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
            return null;
        }

        String price = publishBean.getPrice();
        if (StringUtils.isBlank(price)) {
            return null;
        }
        CarryPriceVo carryPriceVo = null;
        String errorMsg = null;
        if (publishBean.getGoodCarPriceTransport() != null && publishBean.getGoodCarPriceTransport().equals(YesOrNoEnum.Y.getCode())) {
            // 如果是优车定价货源，自己校验阻断价
            TransportCarryBean transportCarryBean = buildCarryBean(publishBean);
            buildAddress(transportCarryBean);
            carryPriceVo = excellentPriceConfigService.getThPrice(transportCarryBean);
            if (carryPriceVo != null) {
                if (carryPriceVo.getFixPriceMin() != null && carryPriceVo.getFixPriceMax() != null) {
                    publishBean.setFixPriceMax(carryPriceVo.getFixPriceMax());
                    publishBean.setFixPriceMin(carryPriceVo.getFixPriceMin());
                    publishBean.setFixPriceFast(carryPriceVo.getFixPriceFast());
                }

            }
        } else {
            CarryPriceReq carryPriceReq = this.createCarryPriceReq(publishBean);
            carryPriceReq.setSource(PriceSourceEnum.publish.getSource());
            carryPriceVo = this.reqCarryPrice(carryPriceReq);
            if (carryPriceVo != null) {
                if (carryPriceVo.getSuggestMinPrice() != null && carryPriceVo.getSuggestMaxPrice() != null) {
                    publishBean.setSuggestMaxPrice(carryPriceVo.getSuggestMaxPrice().intValue());
                    publishBean.setSuggestMinPrice(carryPriceVo.getSuggestMinPrice().intValue());
                    publishBean.setFixPriceFast(carryPriceVo.getFixPriceFast());
                }
            }
        }

        int minPrice = 100;
        int maxPrice = 999999;

        if (carryPriceVo != null) {
            Integer thMinPrice = carryPriceVo.getThMinPrice();
            Integer thMaxPrice = carryPriceVo.getThMaxPrice();

            logger.info("reqCarryPrice_minPrice : ", thMinPrice);
            if (thMinPrice != null && thMinPrice > minPrice) {
                minPrice = thMinPrice;
            }
            if (thMaxPrice != null) {
                maxPrice = thMaxPrice;
            }

        }
        Integer priceInt = Integer.parseInt(price);
        if (priceInt < minPrice) {
            errorMsg = "您当前运费过低，将无法匹配到司机，请重新填价";
            logger.warn("fixed_price_too_low : price : {}; minPrice : {}", priceInt, minPrice);
        } else if (priceInt > maxPrice) {
            errorMsg = "您当前运费过高，可能存在填写错误，请重新填价";
            logger.warn("fixed_price_too_high : price : {}; maxPrice : {}", priceInt, maxPrice);
        }
        // 订金在不可退的情况下，订金不能大于等于运费
        if (Objects.equals(publishBean.getRefundFlag(), 0)
                && publishBean.getInfoFee() != null && StringUtils.isNotBlank(price)) {
            if (publishBean.getInfoFee().compareTo(new BigDecimal(price)) >= 0) {
                return ResultMsgBean.failResponse(1001, "您当前订金高于运费，请核对后再发货");
            }
        }
        ResultMsgBean msgBean = null;
        if (StringUtils.isNotBlank(errorMsg)) {
            msgBean = new ResultMsgBean(ReturnCodeConstant.price_check_error, errorMsg);
        }

        return msgBean;
    }

    /**
     * 通过货物名称匹配标准货源
     *
     * @param transport
     * @return
     */
    public boolean matchStandardWithContent(Transport transport) {
        boolean matchResult = false;

        String taskContent = transport.getTaskContent();
        List<String> wordList = commonApiService.splitContent(taskContent);

        if (CollectionUtils.isNotEmpty(wordList)) {
            TytKeywordMatchesNewUnstandard matchesNewUnstandard = keywordMatchesNewUnstandardMapper.getOneKeywordMatch(wordList);

            //对参考值进行赋值
            if (matchesNewUnstandard != null) {
                matchResult = true;

                String brand = StringUtil.getStringValue(matchesNewUnstandard.getBrand(), "");
                String goodTypeName = StringUtil.getStringValue(matchesNewUnstandard.getSecondClass(), "");
                String topType = StringUtil.getStringValue(matchesNewUnstandard.getSecondType(), "");

                transport.setBrand(brand);
                transport.setGoodTypeName(goodTypeName);
                transport.setType(topType);
                transport.setGoodNumber(1);

                transport.setReferLength(this.getReferNumber(transport.getLength(), matchesNewUnstandard.getLength()));
                transport.setReferWidth(this.getReferNumber(transport.getWide(), matchesNewUnstandard.getWidth()));
                transport.setReferHeight(this.getReferNumber(transport.getHigh(), matchesNewUnstandard.getHeight()));
                transport.setReferWeight(this.getReferNumber(transport.getWeight(), matchesNewUnstandard.getWeight()));

                transport.setIsStandard(Constant.STANDARD_STATUS_STANDARD);
                transport.setMatchItemId(PlatBaseConstant.delay_match_item_id);

            } else {
                transport.setReferLength(this.getReferNumber(transport.getLength(), null));
                transport.setReferWidth(this.getReferNumber(transport.getWide(), null));
                transport.setReferHeight(this.getReferNumber(transport.getHigh(), null));
                transport.setReferWeight(this.getReferNumber(transport.getWeight(), null));

                transport.setIsStandard(Constant.STANDARD_STATUS_PROBABILITY);
                transport.setMatchItemId(-1);
            }
        }
        return matchResult;
    }

    @Override
    public void setTransportStandardInfo(Transport transport) {

        Integer matchItemId = transport.getMatchItemId();

        //是否已解析成功
        boolean matchResult = false;
        if (matchItemId != null && matchItemId > 0 && matchItemId.intValue() != PlatBaseConstant.delay_match_item_id) {
            // 补充标准货物信息
            matchResult = this.addReferTransportNewInfo(transport);
        }
        /*
        if(!matchResult){
            matchResult = this.matchStandardWithContent(transport);
        }
        */

        // 兼容pc的bug，是否标准化未传值的问题
        if (!matchResult) { // 如果未传值，则认定为非标货源
            transport.setIsStandard(1);
            transport.setMatchItemId(-1);
        }

        //设置参考初始值
        if (transport.getReferLength() == null) {
            transport.setReferLength(this.getReferNumber(transport.getLength(), null));
        }
        if (transport.getReferWidth() == null) {
            transport.setReferWidth(this.getReferNumber(transport.getWide(), null));
        }
        if (transport.getReferHeight() == null) {
            transport.setReferHeight(this.getReferNumber(transport.getHigh(), null));
        }
        if (transport.getReferWeight() == null) {
            transport.setReferWeight(this.getReferNumber(transport.getWeight(), null));
        }

    }

    @Override
    public ResultMsgBean savePublishTransport(TransportPublishBean publishBean) throws Exception {
        logger.info("savePublishTransport param:{}", JSON.toJSONString(publishBean));
        ResultMsgBean msgBean = new ResultMsgBean();
        long yanzhengStartTime = System.currentTimeMillis();

        // 0. 6510拉新裂变验证
        if (activityGradePrizeService.getUserAuth(publishBean.getUserId())) {
            msgBean.setCode(ReturnCodeConstant.PULL_NEW_USER_UNVERIFIED);
            msgBean.setMsg("您已参与活动并可领取奖励，若要发货，请先进行实名认证");
            return msgBean;
        }

        //1. 版本升级防止用户跳过规则操作
        if (upgradeCheckService.checkClientInfo(publishBean.getClientVersion(), Integer.parseInt(publishBean.getClientSign()))) {
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setNoticeData(tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.老版本货站升级提示, null));
            return msgBean;
        }
        logger.info("发货传入的userId为：" + publishBean.getUserId());
        User user = userService.getByUserId(publishBean.getUserId());

        TransportMain oldTran = null;
        Long oldId = publishBean.getTsId() == null ? 0L : publishBean.getTsId();
        if (oldId != 0) {
            oldTran = transportMainService.getTransportMainForId(oldId);
        }
        //2. 验证用户身份
        msgBean = userService.checkUserPhotoVerifyFlag(user, publishBean.getClientSign());
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            // 2024.10.12工单：未认证用户发货并撤销重发不做认证拦截
            if (!(oldTran != null && oldTran.getCtime().after(DateUtils.truncate(new Date(), Calendar.DATE)))) {
                return msgBean;
            }
        }

        // 新老发货类型兼容，设置新发货类型
        this.getExcellentGoodsType( publishBean);
        //3. 增加发货限制验证
        msgBean = this.validationLimitPublish(user);
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            //TODO 6340版本 兼容 低版本PC提示 强升后可删除
            if (StringUtils.isNotEmpty(publishBean.getClientSign()) && publishBean.getClientSign().equals("1") && StringUtils.isNotEmpty(publishBean.getClientVersion()) && Integer.parseInt(publishBean.getClientVersion()) < 566340) {
                msgBean.setCode(2023);
                msgBean.setMsg(msgBean.getNoticeData().getMasterContent());
            }
            return msgBean;
        }

        //4. 验证参数非空
        msgBean = validationTransportPublishBean(publishBean);
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            return msgBean;
        }

        // 判断是否是 17.5 米专享
        Boolean isExclusive = isExclusive(publishBean);
        publishBean.setExclusiveType(isExclusive ? 1 : 0);

        if (publishBean.getFriendCarUserId() != null && publishBean.getFriendCarUserId() == user.getId()) {
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("发货人账号与熟车账号不能相同");
            return msgBean;
        }

        //6. 验证电话是否为本人联系电话，人工派单不进行验证；
        msgBean = validationTel(publishBean, user);
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            return msgBean;
        }


        //7. 验证货物内容是否有 散货信息
        msgBean = validationTaskContent(publishBean);
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            return msgBean;
        }

        //运满满货源校验
        msgBean = checkYmmStatus(publishBean, user.getCellPhone());
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            return msgBean;
        }

        if (publishBean.getGoodCarPriceTransport() != null && publishBean.getGoodCarPriceTransport() == 1) {
            //优车运价货源发优车发货卡
            Long excellentGoodsCarId = excellentGoodsService.saveExcellentGoodsPriceCard(publishBean.getUserId(), false);
            if (excellentGoodsCarId != null && excellentGoodsCarId != 0) {
                publishBean.setExcellentCardId(excellentGoodsCarId);
                publishBean.setExcellentGoods(1);
            }
        }

        //电议无价
        int youchePublishType = GoodCarPriceTransportPublishTypeEnum.noPrice.getCode();
        if (publishBean.getPublishType() != null && publishBean.getPublishType() == 2) {
            //一口价
            youchePublishType = GoodCarPriceTransportPublishTypeEnum.fixed.getCode();
        } else if (StringUtils.isNotBlank(publishBean.getPrice()) && new BigDecimal(publishBean.getPrice()).compareTo(new BigDecimal(0)) > 0) {
            //电议有价
            youchePublishType = GoodCarPriceTransportPublishTypeEnum.havePrice.getCode();
        }
        // 校验优车黑名单
        if (!Objects.equals(publishBean.getAutomaticGoodCarPriceTransport(), true) && (Objects.equals(publishBean.getExcellentGoods(), ExcellentGoodsEnums.EXCELLENT.getCode())
                || Objects.equals(publishBean.getGoodCarPriceTransport(), YesOrNoEnum.Y.getCode()))) {
            DepositBlockCheckDTO depositBlockCheckDTO = depositService.checkDepositBlock(publishBean.getUserId(), true);
            if (depositBlockCheckDTO.isBlock()) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR, "您当前账号因违反平台规则无法发优车货源，如有疑问请联系客服"));
            }

        }

        // 如果有优车发货卡，不校验优车限制
        if (publishBean.getExcellentCardId() == null) {
            // 判断是否是优车货源
            boolean flag = checkExcellentGoods(publishBean.getExcellentGoods(), publishBean.getUserId(), youchePublishType);
            if (!flag) {
                msgBean = ResultMsgBean.failResponse(ResultMsgBean.ERROR, "无法发布优车货源，请发布普通货源");
                return msgBean;
            }
        } else {
            boolean checkType = excellentGoodsService.checkType(publishBean.getExcellentCardId(), publishBean.getUserId());
            if (!checkType) {
                msgBean = ResultMsgBean.failResponse(ResultMsgBean.ERROR, "当前优车发货卡已失效，无法发布优车货源");
                return msgBean;
            }

        }


        Long userId = publishBean.getUserId();

        //校验运费价格
        String price = publishBean.getPrice();
        ResultMsgBean priceMsgBean = this.checkPriceAllow(publishBean);
        if (priceMsgBean != null) {
            return priceMsgBean;
        }

        Integer priceType = publishBean.getPublishType();
        //一口价货源运费校验
        if (priceType != null && priceType.equals(PublishTypeEnum.fixed.getCode())) {
            if (publishBean.getShuntingQuantity() == null) {
                msgBean.setCode(ReturnCodeConstant.shunting_quantity_error);
                msgBean.setMsg("一口价货源调车数量不可为空");
                return msgBean;
            }
            if (publishBean.getShuntingQuantity() > Constant.AGE1) {
                msgBean.setCode(ReturnCodeConstant.shunting_quantity_error);
                msgBean.setMsg("一口价货源调车数量不可大于1");
                return msgBean;
            }
            if (StringUtils.isBlank(price)) {
                msgBean.setCode(ReturnCodeConstant.publish_type_fee_null);
                msgBean.setMsg("一口价货源信息费和运费不能为空");
                return msgBean;
            }
        }

        boolean vipPublishPermission = userPermissionService.isVipPublishPermission(userId);
        //2022-07-29 新增电议货源校验 需满足条件: 当前为电议货源 且 当前用户为非会员
        if (TytSwitchUtil.isGoodsNonMemberActivityOn() && priceType != null && priceType.equals(PublishTypeEnum.tel.getCode()) && !vipPublishPermission) {
            //货方1+9活动Id
            Integer activityId = tytConfigService.getIntValue("goods:nonmember:activity:id", 0);
            //货方1+9活动 当前用户参与详情
            OrderEncourageBean pcEncouragePopou = marketingActivityService.getPcEncouragePopou(user.getId(), activityId);
            if (pcEncouragePopou != null) {
                //查看用户在当前活动期间 发送的电议货源的个数 如果大于0 则不允许再次发送电议货源
                Integer publishCountByParam = transportMainService.getPublishCountByParam(user.getId(), pcEncouragePopou.getStartTime(), pcEncouragePopou.getEndTime(), priceType);
                logger.info("savePublishTransport getPublishCountByParam 【userId:{}】【startTime:{}】【endTime:{}】【publishCountByParam:{}】", user.getId(), pcEncouragePopou.getStartTime(), pcEncouragePopou.getEndTime(), publishCountByParam);
                if (publishCountByParam > 0) {
                    msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
                    msgBean.setMsg(null);
                    msgBean.setData("您的电议发货次数已用尽，请使用一口价方式发货");
                    msgBean.setNoticeData(tytNoticePopupTemplService.getByType(800, 4));
                    return msgBean;
                }
            }
        }

        if (publishBean.getInvoiceTransport() != null && publishBean.getInvoiceTransport() == 1) {
            if (publishBean.getDistance() == null) {
                return ResultMsgBean.failResponse(90001, "运距获取失败，请返回上一页重选地址或联系客服");
            }

            String invoiceSujectData = tytConfigService.getStringValue("invoice_subject_data", "1,JCZY");
            String[] split = invoiceSujectData.split(",");
            String jczyId = split[0];
            String jczyCode = split[1];
            if (publishBean.getInvoiceSubjectId() == null || StringUtils.isBlank(publishBean.getServiceProviderCode())) {
                publishBean.setInvoiceSubjectId(Long.valueOf(jczyId));
                publishBean.setServiceProviderCode(jczyCode);
            }

            //开票货源货主资格校验
            ResultMsgBean resultMsgBean = invoiceTransportService.checkUserCanPublishInvoiceTransportInDoPublish(publishBean.getUserId(), publishBean.getInvoiceSubjectId());
            if (resultMsgBean.getCode() != 200) {
                return resultMsgBean;
            }

            //开票货源限制校验
            resultMsgBean = invoiceTransportService.checkInvoiceTransportParam(publishBean.getUserId(), publishBean.getDistance().toString(), publishBean.getPrice(), publishBean.getWeight());
            if (resultMsgBean.getCode() != 200) {
                return resultMsgBean;
            }

            //翔和翎开票校验收货人信息
            if (publishBean.getServiceProviderCode().equals("XHL")) {
                String clientVersion = publishBean.getClientVersion();
                if (StringUtils.isNotBlank(clientVersion)) {
                    Integer clientVersionInt = Integer.valueOf(clientVersion);
                    if (clientVersionInt < 6630) {
                        return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "若使用翔和翎发货，请升级至最新版本");
                    }
                }

                resultMsgBean = invoiceTransportService.checkConsigneeData(publishBean.getUserId()
                        , publishBean.getConsigneeName(), publishBean.getConsigneeTel(), publishBean.getConsigneeEnterpriseName());
                if (resultMsgBean.getCode() != 200) {
                    return resultMsgBean;
                }

                resultMsgBean = checkSegmentedPayments(publishBean.getPrepaidPrice(), publishBean.getCollectedPrice(),
                        publishBean.getReceiptPrice(), publishBean.getPrice(), user.getId(), false);

                if (resultMsgBean.getCode() != 200) {
                    return resultMsgBean;
                }
            }


            //专车开票货源不可选择服务商为甘肃网货的开票主体
            if (publishBean.getExcellentGoods() != null && publishBean.getExcellentGoods() == 2 && jczyCode.equals(publishBean.getServiceProviderCode())) {
                return ResultMsgBean.failResponse(99911, "专车暂不支持甘肃网货开票主体，请在发票类型中更换");
            }
        }
        // 专车发货，技术服务费赋值
//        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
//            BigDecimal tecServiceFee = feeCalculateService.calculateTechServiceFee(new BigDecimal(publishBean.getPrice()),
//                    ExcellentGoodsEnums.SPECIAL.getCode());
//            publishBean.setTecServiceFee(tecServiceFee);
//        }

        // 拼车校验是否满足拼车条件
        if (Objects.equals(UseCarTypeEnum.PART.getCode(), publishBean.getUseCarType())) {
            CarpoolMatchDTO matchDTO = new CarpoolMatchDTO();
            matchDTO.setStartCity(publishBean.getStartCity());
            matchDTO.setDestCity(publishBean.getDestCity());
            matchDTO.setInvoiceTransport(publishBean.getInvoiceTransport());
            matchDTO.setWeight(StringUtils.isNotBlank(publishBean.getWeight()) ? new BigDecimal(publishBean.getWeight()) : null);
            matchDTO.setDistance(publishBean.getDistance());
            matchDTO.setUserId(publishBean.getUserId());
            Response<Boolean> response = transportTecserviceFeeClient.checkMatchCarpool(matchDTO).execute();
            if (response.isSuccessful()) {
                Boolean match = response.body();
                if (Objects.nonNull(match) && !match) {
                    return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "不满足拼车条件，请选择整车发货");
                }
            }
        }

        //9. 生成transport对象
        Transport transport = createTransport(publishBean, user);


        //11. 设置是否会员的标识
        if (vipPublishPermission) {
            transport.setUserType(1);
        } else {
            transport.setUserType(0);
        }

        //8. 是否新发布货物
        PublisTypeEnum publishType = PublisTypeEnum.新发布货物;

        TransportLabelJson oldLabelJson = null;

        Long srcMsgId = null;
        if (oldId != 0) {
            boolean isYesterdayPublish = oldTran.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime();
            if (isYesterdayPublish) {
                publishType = PublisTypeEnum.历史货物编辑发布;

                oldLabelJson = transportBusiness.getTransportLabelJson(oldTran);
                oldLabelJson.setAddPriceCount(null);
            } else if (oldTran.getStatus() == 4) {//成交
                publishType = PublisTypeEnum.新发布货物;
            } else {
                publishType = PublisTypeEnum.当日货物编辑发布;
                srcMsgId = oldId;
                oldLabelJson = transportBusiness.getTransportLabelJson(oldTran);
            }
        }

        if (Objects.equals(publishType, PublisTypeEnum.新发布货物)) {
            transport.setFirstPublishType(transport.getPublishType());
        } else {
            transport.setFirstPublishType(oldTran.getFirstPublishType());
        }

        this.setTransportStandardInfo(transport);
        //必须先获取标准货物信息才能生成hashCode
        transport.setHashCode(this.getNewHashCode(transport));

        //更新用户相关的字段
        TytUserSub tytUserSub = tytUserSubService.getTytUserSubByUserId(user.getId());
        //用户信用分及等级
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);

        transportBusiness.refreshUserInfo(transport, tytUserSub, userCreditInfo);
        //刷新货源标签相关字段
        TransportLabelJson transportLabelJson = transportBusiness.refreshLabelJson(srcMsgId, oldLabelJson, transport, userCreditInfo, true, publishBean);

        // 设置捂货时间
        setPriorityRecommendExpireTime(transport, oldTran, publishBean.getPriorityRecommend(), transportLabelJson.getIGBIResultData(), publishType);


        // 判断用户发货权益
        if (!PublisTypeEnum.当日货物编辑发布.equals(publishType)) {
            // 非优车发货卡用户校验发货权益
            // 专车货源不校验用户权益
            if (publishBean.getExcellentCardId() == null && !ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
                msgBean = userService.checkUserPublishTransportPermission(userId, price);
                if (ReturnCodeConstant.OK != msgBean.getCode()) {
                    Integer userType = abtestService.getUserType(Constant.TRANSMISSION_AB_TEST_KEY, userId);
                    if (userType == 1 && StringUtils.isNotBlank(publishBean.getClientVersion()) && Integer.parseInt(publishBean.getClientVersion()) >= 6640) {
                        VipNoticeForGoodsBean notice = couponService.getNoticeContent(userId);
                        msgBean.setCode(ReturnCodeConstant.NO_PERMISSION_PERSONAL);
                        msgBean.setData(notice);
                        return msgBean;
                    }
                    return msgBean;
                }
            }
        }
//        else {
//            Integer sourceType = oldTran.getSourceType();
//            transport.setSourceType(sourceType);
//        }

        //10.1. 个人相似货源判断
        /*
        msgBean = this.checkPersonalSimilarity(transport, srcMsgId);
        if (msgBean.getCode() != ReturnCodeConstant.OK) {
            return msgBean;
        }
        */


        logger.info("用户:{} 内容:{} 验证耗时:{}ms", publishBean.getUserId(), publishBean.getTaskContent(), System.currentTimeMillis() - yanzhengStartTime);

        long rukuStartTime = System.currentTimeMillis();
        BackendTransportBean backendTransportBean = null;
        if (publishBean.getBackendId() != null && publishBean.getBackendId() != 0L) {
            backendTransportBean = backendTransportMapper.selectBackendStatusById(publishBean.getBackendId());
        }
        if (backendTransportBean == null && publishBean.getIsBackendTransport() != null && publishBean.getIsBackendTransport() == 1) {
            backendTransportBean = backendTransportMapper.selectBackendStatusBySrcId(publishBean.getTsId());
        }

        //校验小程序是否已发布
        if (backendTransportBean != null) {
            Long backendMsgId = backendTransportBean.getMsgId();

            if (backendMsgId == null) {
                backendMsgId = 0L;
            }
            if (!oldId.equals(backendMsgId)) {
                //证明已由其他人发过
                return ResultMsgBean.failResponse(ResponseEnum.sys_error.info("当前订单已发布，无法重新发布！"));
            }
        }
        TytTransportMainExtend mainExtend = getMainExtend(transport, publishBean);

        //抽佣货源计算技术服务费
        //------------------------这一步要在所有逻辑处理结束后进行-------------------------
        MeetCommissionRulesResult meetCommissionRules = new MeetCommissionRulesResult();
        TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = null;
        // 判断是否需要计算抽佣货源的技术服务费
        // 如果APP版本号>=6670 且是 代调账号发的专车非平台，使用客户端传的技术服务费，不计算技术服务费，
        if (Constant.ClientSignEnum.isGoods(Integer.parseInt(publishBean.getClientSign())) // APP端
                && Integer.parseInt(publishBean.getClientVersion()) >= 6670 // 版本号>=6670
                && SourceTypeEnum.isDispatch(transport.getSourceType()) // 代调
                && ExcellentGoodsEnums.SPECIAL.getCode().equals(transport.getExcellentGoods()) // 专车
                && !Objects.equals(transport.getCargoOwnerId(), 1L)) {
            // 如果佣金不为空且>0，算抽佣
            if (publishBean.getTecServiceFee() != null && publishBean.getTecServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                tytTecServiceFeeConfigToComputResult = this.getManualTecServiceFeeResult(transport, publishBean.getTecServiceFee());
            } else {
                transport.setTecServiceFee(BigDecimal.ZERO); // 必须设置为0，为null不会更新数据库
            }
            // 如果使用客户端传的技术服务费，则打标
            TransportLabelJson labelJson = transportBusiness.getTransportLabelJson(transport.getLabelJson());
            labelJson.setIsManualTecServiceFee(1);
            transport.setLabelJson(labelJson.getJsonText());
        } else {
            tytTecServiceFeeConfigToComputResult = this.makeTecServiceFeeData(transport, oldTran, meetCommissionRules,
                    publishBean.getGoodCarPriceTransport() != null && publishBean.getGoodCarPriceTransport() == 1 ? true : false, mainExtend);
        }

        boolean isCommissionTransport = false;
        if (tytTecServiceFeeConfigToComputResult != null) {
            isCommissionTransport = true;
        }

        transportLabelJson = transportMainService.checkPersonalDuplicate(srcMsgId, oldLabelJson, transport, true, publishBean.getAssignCarTel());

        //10. hashcode验证是否重复信息,运满满货源不校验重货
        if (!transport.getSourceType().equals(SourceTypeEnum.运满满货源.getId())) {
            Integer confirm = publishBean.getConfirm();
            transportBusiness.checkPersonalDuplicate(confirm, transport, oldLabelJson, transportLabelJson);
        }

        if (Objects.equals(publishBean.getGoodCarPriceTransport(), 1)) {
            transport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
        } else {
            transport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
        }

        /* 保存数据到数据库，缓存 */
        Transport newTransport = this.addTransportBusiness(transport, oldTran, publishType, publishBean, backendTransportBean, mainExtend);


        // 更新相似货源信息及首发信息
        transportService.updateSimilarityCode(newTransport);
        // 添加到个人货主
        CsMaintainedCustom csMaintainedCustom = csMaintainedCustomService.getCsMaintainedCustomByUserId(userId);
        if ((csMaintainedCustom != null && csMaintainedCustom.getGoodsSync() == 1)) {
            Integer clientSign = StringUtils.isBlank(publishBean.getClientSign()) ? null : Integer.valueOf(publishBean.getClientSign());
            tytTransportDispatchService.saveTransportDispatch(newTransport, csMaintainedCustom, clientSign, publishBean.getGiveGoodsPhone(), publishBean.getContactName());
        }

        try {
            //保存历史
            Transport tbTmp = transportService.getById(newTransport.getId());
            transportHistoryService.saveTransportHistory(tbTmp);
        } catch (Exception e) {
            logger.error("saveTransportHistory_error : ", e);
        }

        //发货次数+1
        if (!PublisTypeEnum.当日货物编辑发布.equals(publishType)) {
            Integer publishNum = Objects.isNull(tytUserSub.getPublishNum()) ? 0 : tytUserSub.getPublishNum();
            tytUserSubService.updatePublishNum(userId, publishNum + 1);

            //月发布次数
            Integer userPublishNum = RedisUtil.getObject(Constant.MONTH_PUBLISH_NUM + user.getId());
            Integer monthPublishNum = Objects.isNull(userPublishNum) ? 0 : userPublishNum;

            //获取当月剩余秒数
            int cacheSeconds = (int) TimeUtil.getMonthZeroSeconds();
            RedisUtil.setObject(Constant.MONTH_PUBLISH_NUM + user.getId(), monthPublishNum + 1, cacheSeconds);
        }
        logger.info("货物信息发布成功" + publishBean.toString());

        //如果勾选货源保障，则赠送7天10次货源曝光权益，每天最多赠送一次
        if (publishBean.getGuaranteeGoods() != null && publishBean.getGuaranteeGoods() == 1) {
            String redisKey = Constant.PERMISSION_GAIN_PUBLISH + user.getId() + "_" + TimeUtil.formatDate(new Date());
            String permissionGain = RedisUtil.get(redisKey);
            //获取当天剩余秒数
            int cacheSeconds = (int) TimeUtil.getTomorrowZeroSeconds();
            if (StringUtils.isBlank(permissionGain)) {
                userPermissionService.giveExposureCard(user);
                RedisUtil.set(redisKey, "1", cacheSeconds);
            }
        }
        // 如果是优车货源，发布优车货源时记录发布时间，货源下放时使用,并扣减发货次数
        if (publishBean.getExcellentGoods() != null && publishBean.getExcellentGoods() == 1 && publishBean.getExcellentCardId() == null) {
            cacheService.setObject(EXCELLENT_GOODS_PUBLISH_TIME + newTransport.getSrcMsgId(), System.currentTimeMillis(), 24 * 60 * 60);
            depositService.deductionCount(user.getId(), youchePublishType);
        }

        // 如果是运满满货源，同步添加到运满满关系表中
        if (publishBean.getSourceType() != null && publishBean.getSourceType().equals(SourceTypeEnum.运满满货源.getId())) {
            transportYMMService.saveOrUpdateMbMerge(newTransport.getSrcMsgId(), publishBean.getCargoId(), publishBean.getCargoVersion());
        }

        //如果是待审核货名，进入后台待审核列表
        try {
            if (publishBean.getMachineType() != null && publishBean.getMachineType() == MachineTypeEnum.audit.code) {
                tytMachineTypeAuditService.addMachineTypeAudit(publishBean.getTaskContent(), user);
            }
        } catch (Exception e) {
            logger.error("保存货名待审核数据出错,srcMsgId:{},taskContent:{}", transport.getSrcMsgId(), publishBean.getTaskContent(), e);
        }

        // 使用优车发货卡发货完成后，将该卡变为已使用状态
        if (publishBean.getExcellentCardId() != null) {
            excellentGoodsService.updateType(publishBean.getExcellentCardId(), ExcellentCardTypeEnum.used);
        }

        String key = "automaticGoodCarPriceTransport" + ":" + newTransport.getSrcMsgId();
        if (publishBean.getAutomaticGoodCarPriceTransport() != null && publishBean.getAutomaticGoodCarPriceTransport()) {
            //记录缓存表示是自动转优车定价，用于app后续展示自动转优车定价广告
            RedisUtil.set(key, "1", 10 * 60);
        } else {
            RedisUtil.del(key);
        }

        TransportBIDataJson transportBIDataJson = new TransportBIDataJson(transportLabelJson.getIGBIResultData(), publishBean.getPublishTransportIsShowGoodCar(), publishBean.getStartAddrSource(), publishBean.getDestAddrSource(), publishBean.getThMinPrice(), publishBean.getThMaxPrice(), publishBean.getSuggestPrice(), publishBean.getSuggestMinPrice(), publishBean.getSuggestMaxPrice(), publishBean.getFixPriceMin(), publishBean.getFixPriceMax(), publishBean.getFixPriceFast(), Integer.parseInt(publishBean.getClientSign()) == 1 ? 2 : 1, publishBean.getShowGoodCarPriceTransportTab()
                //自动转优车定价，记录缓存并存储到五级地址表中供BI查询,1代表编辑发布自动转优车定价货源
                , publishBean.getAutomaticGoodCarPriceTransport() != null && publishBean.getAutomaticGoodCarPriceTransport() ? 1 : null, transportLabelJson.getGoodCarPriceTransport(), null, null, null, publishBean.getDistanceKilometer(), publishBean.getOtherFee(), publishBean.getExcellentGoods(), isCommissionTransport ? 1 : 0, meetCommissionRules.getMeetCommissionRules(), publishBean.getPriorityRecommend()
                , transportLabelJson.getGoodsModelScore(),transportLabelJson.getCommissionLabel());
        transportBusiness.saveSomeBINeedDataResult(newTransport.getSrcMsgId(), transportBIDataJson);

        if (isCommissionTransport) {
            //记录抽佣货源信息
            this.saveCommissionTransportTecServiceFeeData(newTransport.getSrcMsgId(), tytTecServiceFeeConfigToComputResult);
            tytTransportTecServiceFeeMapper.saveTransportTecServiceFeeLog(newTransport.getSrcMsgId(), 1, 1);
        } else {
            //不抽佣则清楚抽佣货源信息
            tytTransportTecServiceFeeMapper.deleteFreeTecServiceFeeLogBySrcMsgId(srcMsgId);
            tytTransportTecServiceFeeMapper.delete(newTransport.getSrcMsgId());
            tytTransportTecServiceFeeMapper.saveTransportTecServiceFeeLog(newTransport.getSrcMsgId(), 2, 1);
        }

        // 发布货源/编辑货源的数据埋点
        transportEventTrickingService.publishTransport(newTransport, transportLabelJson, publishBean);

        //开票货源记录发布时货主的企业信息
        if (newTransport.getInvoiceTransport() != null && newTransport.getInvoiceTransport() == 1) {
            invoiceTransportService.saveInvoiceTransportEnterpriseData(newTransport.getUserId(), newTransport.getSrcMsgId(), publishBean.getInvoiceSubjectId()
                    , publishBean.getServiceProviderCode(), publishBean.getAssignCarTel(), newTransport.getEnterpriseTaxRate()
                    , publishBean.getConsigneeName(), publishBean.getConsigneeTel(), publishBean.getConsigneeEnterpriseName(), publishBean.getPaymentsType()
                    , publishBean.getPrepaidPrice(), publishBean.getCollectedPrice(), publishBean.getReceiptPrice());
        }

        // 专车货源，自动派单，自动添加到专车货主管理
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
            transportBusiness.autoAssignOrderForSpecialCar(newTransport, user);
        }

        // 增加刷新次数
        goodsRefreshManualService.addGoodsRefresh(newTransport, publishBean, null);

        // 更新发货权益使用记录
        if (ObjectUtil.isNotNull(msgBean) && ObjectUtil.isNotNull(msgBean.getData())) {
            userPermissionService.updatePublishPermissionTsId(msgBean.getData(), newTransport.getSrcMsgId());
        }

        //记录货源好货分数、好货运价分数
        transportBusiness.recordScoreEveryTransport(transport, newTransport.getSrcMsgId(), newTransport.getId(),
                !PublisTypeEnum.当日货物编辑发布.equals(publishType));

        // 如果勾选自动重发，添加货源自动重发记录
        transportAutoResendService.addAutoResendRecord(publishBean.getIsAutoResend(), publishBean.getUserId(), newTransport.getSrcMsgId());

        // 设置响应返回
        msgBean = getPublishMsg(newTransport);
        logger.info("用户:{} 内容:{} 入库耗时:{}ms", publishBean.getUserId(), publishBean.getTaskContent(), System.currentTimeMillis() - rukuStartTime);
        return msgBean;
    }

    /**
     * 设置优车类型，新老数据兼容
     * <pre>
     *  a. 老版本优车（1.0、2.0），在新版货源根据定价金额映射极速优车、快速优车、特惠优车，若原价格低于特惠优车或无价，映射为特惠优车
     *  b. 老版本优车，若在新版未成功获取优车定价的
     *      i. 有运费：用户出价+原来的议价方式（一口价、电议）
     *      ii. 无运费：普通找车
     *  c. 老版本货源若是优车电议，在新版本也展示为极速优车、快速优车、特惠优车+电议
     *  d. 老版本普货一口价，新版本展示为用户出价+一口价
     *  e. 老版本普货电议有价，新版本展示为用户出价+电议
     *  f. 老版本电议无价，新版本展示为普通找车
     *  g. 以上映射货源，在新版本APP进行直接发布时，按照原货源直接发布
     * </pre>
     */
    private void getExcellentGoodsType(TransportPublishBean publishBean) {

        // 因为pc端不能发优车货源，所以pc发的货可能没有excellentGoods字段，所以这里需要补上,默认是非优车货源
        if (Objects.equals(publishBean.getClientSign(), String.valueOf(Constant.ClientSignEnum.PC.getCode()))) {
            publishBean.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            publishBean.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
            if (TransportUtil.hasPrice(publishBean.getPrice())) {
                publishBean.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
            } else {
                publishBean.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
            }
            return;
        }

        // 优车三挡价格，如果客户端没传，重新获取
        if (publishBean.getFixPriceMin() == null || publishBean.getFixPriceMax() == null || publishBean.getFixPriceFast() == null) {
            TransportCarryBean transportCarryBean = buildCarryBean(publishBean);
            CarryPriceVo carryPriceVo = excellentPriceConfigService.getThPrice(transportCarryBean);
            if (carryPriceVo != null) {
                publishBean.setFixPriceMin(carryPriceVo.getFixPriceMin());
                publishBean.setFixPriceMax(carryPriceVo.getFixPriceMax());
                publishBean.setFixPriceFast(carryPriceVo.getFixPriceFast());
                publishBean.setThMinPrice(carryPriceVo.getThMinPrice());
                publishBean.setThMaxPrice(carryPriceVo.getThMaxPrice());
            }
        }

        // 6710发货改版：新版本发货兼容老字段，老版本发货兼容新字段
        if (publishBean.getPublishGoodsType() != null) { // 新版本发货（该字段为新增字段）
            if (PublishGoodsTypeEnum.isExcellentGoods(publishBean.getPublishGoodsType())) {
                publishBean.setExcellentGoods(ExcellentGoodsEnums.EXCELLENT.getCode());
                publishBean.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
            } else if (Objects.equals(publishBean.getPublishGoodsType(), PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())) {
                publishBean.setExcellentGoods(ExcellentGoodsEnums.SPECIAL.getCode());
            } else {
                publishBean.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            }
        } else { // 老版本发货
            if (ExcellentGoodsEnums.isNormal(publishBean.getExcellentGoods())) {
                // 普通发货映射有价映射为用户出价，无价映射为普通找车
                if (TransportUtil.hasPrice(publishBean.getPrice())) {
                    publishBean.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
                } else {
                    publishBean.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
                }
            } else if (ExcellentGoodsEnums.isSpecial(publishBean.getExcellentGoods())) {
                // 专车货源仍映射为专车货源
                publishBean.setPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode());
            } else {
                // 根据优车定价金额映射极速优车、快速优车、特惠优车
                if (publishBean.getFixPriceMin() != null && publishBean.getFixPriceMax() != null && publishBean.getFixPriceFast() != null) {
                    PublishGoodsTypeEnum goodsTypeEnum = TransportUtil.judgeExcellentGoodsLevel(publishBean.getPrice(),
                            publishBean.getFixPriceMin(), publishBean.getFixPriceMax(), publishBean.getFixPriceFast());
                    // 原价格低于特惠优车或无价，映射为特惠优车
                    goodsTypeEnum = goodsTypeEnum == null ? PublishGoodsTypeEnum.EXCELLENT_GOODS : goodsTypeEnum;
                    publishBean.setPublishGoodsType(goodsTypeEnum.getCode());
                } else {
                    // 若在新版未成功获取优车定价，有价=>用户出价，无价=>普通找车
                    if (TransportUtil.hasPrice(publishBean.getPrice())) {
                        publishBean.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
                        publishBean.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
                        publishBean.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
                    } else {
                        publishBean.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
                        publishBean.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
                        publishBean.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
                    }
                }
            }
        }
    }


    /**
     * @param prepaidPrice
     * @param collectedPrice
     * @param receiptPrice
     * @param price
     * @param userId
     * @param direct         是否走直接发布
     */
    @Override
    public ResultMsgBean checkSegmentedPayments(BigDecimal prepaidPrice,
                                                BigDecimal collectedPrice,
                                                BigDecimal receiptPrice,
                                                String price,
                                                Long userId, boolean direct) {

        // 如果到付金额不为空，就是分段支付
        if (collectedPrice != null) {
            prepaidPrice = prepaidPrice == null ? BigDecimal.ZERO : prepaidPrice;
            receiptPrice = receiptPrice == null ? BigDecimal.ZERO : receiptPrice;
            // 先判断当前是否允许分段支付
            Long enterpriseId = tytInvoiceEnterpriseMapper.getByTransportUserId(userId);
            if (enterpriseId != null) {
                TytInvoiceTransportEnterpriseConfigLogDTO config = invoiceTransportConfigService.getLastTytInvoiceTransportEnterpriseConfig(enterpriseId,null);
                if (config == null || config.getSegmentedPayments() != 1) {
                    if (direct) {
                        return ResultMsgBean.failResponse(40007, "当前不支持分段支付，请使用编辑再发布");

                    } else {
                        return ResultMsgBean.failResponse(40007, "当前不支持分段支付，请选择全额到付");

                    }
                }
            }
            BigDecimal totalPrice = collectedPrice.add(prepaidPrice).add(receiptPrice);
            if (StringUtils.isBlank(price) || new BigDecimal(price).compareTo(totalPrice) != 0) {
                return ResultMsgBean.failResponse(40007, "分段支付运费与运费总价不一致，请修改");

            }
            BigDecimal prepaidProportion = BigDecimal.valueOf(0.3);
            BigDecimal receiptProportion = BigDecimal.valueOf(0.2);
            PublicResource publicResourceVO = publicResourceService.getByKey("segmented_payments_price");
            String proportion = "30%,20%";
            if (publicResourceVO != null) {
                proportion = publicResourceVO.getValue();
                if (StringUtils.isNotBlank(proportion)) {
                    prepaidProportion = new BigDecimal(proportion.split(COMMA)[0].replace("%", "")).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                    receiptProportion = new BigDecimal(proportion.split(COMMA)[1].replace("%", "")).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                }
            }
            BigDecimal priceNumber = new BigDecimal(price);
            if (!prepaidPrice.equals(BigDecimal.ZERO)) {
                // 计算预付运费上限
                BigDecimal maxPrepaidPrice = priceNumber.multiply(prepaidProportion).setScale(2, RoundingMode.HALF_UP);
                if (maxPrepaidPrice.compareTo(prepaidPrice) < 0) {
                    return ResultMsgBean.failResponse(40007, "预付运费需低于司机运费" + proportion.split(COMMA)[0] + ", 请修改");

                }
            }
            if (!receiptPrice.equals(BigDecimal.ZERO)) {
                // 计算回单付运费上限
                BigDecimal maxReceipt = priceNumber.multiply(receiptProportion).setScale(2, RoundingMode.HALF_UP);
                if (maxReceipt.compareTo(receiptPrice) < 0) {
                    return ResultMsgBean.failResponse(40007, "回单付运费需低于司机运费" + proportion.split(COMMA)[1] + ", 请修改");
                }
            }
        }
        return ResultMsgBean.successResponse();
    }

    /**
     * 6500修改：勾选优推好车主 && 调用好货模型就设置
     */
    private void setPriorityRecommendExpireTime(Transport transport, TransportMain oldTran, Integer priorityRecommend, Integer iGBIResultData, PublisTypeEnum publisTypeEnum) {
        try {
            boolean isPriorityRecommend = Objects.equals(1, priorityRecommend) && iGBIResultData != null;
            log.info("是否需要捂货：{} , {} , {} ", isPriorityRecommend, JSON.toJSONString(transport), publisTypeEnum);
            if (isPriorityRecommend) {
                // 当日货物编辑发布 保持原来捂货过期时间不变，新货源和历史货源重新发货 重新计算
                if (!PublisTypeEnum.当日货物编辑发布.equals(publisTypeEnum)) {
                    Integer xSecond = tytCoverGoodsDialConfigService.selectXTime();
                    Date date = DateUtils.addSeconds(transport.getPubDate(), xSecond);
                    transport.setPriorityRecommendExpireTime(date);
                } else {
                    transport.setPriorityRecommendExpireTime(oldTran.getPriorityRecommendExpireTime());
                }
            }
        } catch (Exception e) {
            logger.error("获取最大捂货时间错误,srcMsgId:{}", transport.getSrcMsgId(), e);
        }
    }

    /**
     * 运满满货源发布校验
     *
     * @param transportReq
     * @param callPhone
     * @return
     */
    private ResultMsgBean checkYmmStatus(TransportPublishBean transportReq, String callPhone) {
        if (transportReq.getSourceType() != null && transportReq.getSourceType().equals(SourceTypeEnum.运满满货源.getId())) {

            String companyGoodsUsers = tytConfigService.getStringValue("company_goods_users");
            if (StringUtils.isEmpty(companyGoodsUsers) || !companyGoodsUsers.contains(callPhone)) {
                return new ResultMsgBean(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "当前用户非调度身份");
            }
            // 校验是否是下架状态
            TytMbCargoSyncInfo transportYMM = tytMbCargoSyncInfoMapper.selectYMMCargoById(transportReq.getCargoId());
            if (transportYMM == null) {
                return new ResultMsgBean(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "该货源不存在，看看其它货源吧！");
            }
            if (transportYMM.getDelFlag() == 1) {
                return new ResultMsgBean(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "该货源已下架，看看其它货源吧！");
            }
            if (transportReq.getCargoVersion().intValue() != transportYMM.getCargoVersion().intValue()) {
                return new ResultMsgBean(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "该货源已有信息变更，不允许发布");
            }
            if (transportReq.getRefundFlag().intValue() != transportYMM.getDepositReturn().intValue()) {
                return new ResultMsgBean(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "不可修改退还状态");
            }

            if (transportReq.getInfoFee().compareTo(new BigDecimal(transportYMM.getDepositAmt())) != 0) {
                return new ResultMsgBean(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "不可修改订金金额");
            }
            //校验该货源是否已发布
            TytTransportMbMerge transportMbMerge = tytTransportMbMergeMapper.selectByCargoId(transportReq.getCargoId());
            if (transportMbMerge != null) {
                return new ResultMsgBean(ReturnCodeConstant.YMM_TRANSPORT_CHECK, "该货源已发布，看看其它货源吧！");
            }

        }

        return new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");
    }

    @Override
    public boolean checkExcellentGoods(Integer excellentGoods, Long userId, Integer youchePublishType) {
        if (excellentGoods != null && excellentGoods == 1) {
            TytDepositAuthorization tytDepositAuthorization = depositService.selectDepositAuthByUserIdAndConfig(userId);
            if (tytDepositAuthorization == null || tytDepositAuthorization.getAuthStatus() == 0) {
                Integer responseCodeId;

                if (tytDepositAuthorization == null) {
                    //授权记录不存在
                    responseCodeId = ReturnCodeConstant.SUPERIOR_CAR_SIGN_SIGNED_ERROR_JUMP_TAB_NO_FIRST_T;
                } else {
                    boolean isConfigAll = Boolean.TRUE.equals(tytDepositAuthorization.getConfigIsAll());
                    //如果isConfigAll是true表示授权记录不存在，但是由于全开放导致赋予了用户临时授权

                    if (isConfigAll && depositService.isFirstTransportOwner(userId)) {
                        responseCodeId = ReturnCodeConstant.SUPERIOR_CAR_SIGN_SIGNED_ERROR_JUMP_TAB_FIRST_T;
                    } else {
                        responseCodeId = ReturnCodeConstant.SUPERIOR_CAR_SIGN_SIGNED_ERROR_JUMP_TAB_NO_FIRST_T;
                    }
                }

                throw TytException.createException(new ResponseCode(responseCodeId, "您还没有签约优货发布协议，暂不能使用优车功能"));
            }

            //已授权，下面维持原有逻辑
            if (tytDepositAuthorization.getBlackStatus() == 1) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR, "您当前账号因违反平台规则无法发优车货源，如有疑问请联系客服。"));
            }

            DepositBlockCheckDTO depositBlockCheckDTO = depositService.checkDepositBlock(userId, true);
            if (depositBlockCheckDTO.isBlock()) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR, depositBlockCheckDTO.getErrMessage()));
            }

//            int count = transportWayBillExService.selectCountForStatus(userId);
//            if (count > 0) {
//                throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR, "您有未处理的优货异常订单，暂不能使用优车功能"));
//            }

            // 查询是否有退保证金操作
            List<TytDepositApplyAudit> applyAuditList = depositService.selectRefundAuditByUserId(userId, AuditStatusEnum.审核中.getStatus(), AuditEnsureTypeEnum.退还保证金.getType());
            if (CollectionUtils.isNotEmpty(applyAuditList)) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_IN_REVIEW_ERROR, "您当前保证金金额不足，暂不能使用优车功能"));
            }
            // 查询余额账户
            TytDepositAccount availableAccount = depositService.getDepositAccount(userId, BALANCE_TYPE_NORMAL);
            if (availableAccount == null) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR, "账户未创建，请授权签约"));
            }
            //Integer depositMinLimit = tytConfigService.getIntValue("deposit_min_limit", 0);

            TytDepositAccount frozenAccount = depositService.getDepositAccount(userId, BALANCE_TYPE_FROZEN);
            BigDecimal totalBalance = frozenAccount == null ? availableAccount.getBalance() : availableAccount.getBalance().add(frozenAccount.getBalance());

            if (totalBalance.compareTo(tytDepositAuthorization.getRequireAmount().divide(new BigDecimal(2))) < 0) {
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NOT_ENOUGH_ERROR, "您当前保证金金额不足，暂不能使用优车功能"));
            }

            //校验优车发货剩余次数
            checkYouchePublishRemainingCount(userId, youchePublishType);
        }
        return true;
    }

    @Override
    public boolean checkYouchePublishRemainingCount(Long userId, Integer youchePublishType) {
        // 查询优货剩余使用次数
        ExcellentGoodsRemainCountBean remainingCountBean = depositService.getRemainingCount(userId);
        if (youchePublishType == GoodCarPriceTransportPublishTypeEnum.noPrice.getCode()) {
            if (remainingCountBean.getRemainingCallNoPriceCount() != null && remainingCountBean.getRemainingCallNoPriceCount() <= 0) {
                //电议无价次数已用尽
                if ((remainingCountBean.getRemainingCount() != null && remainingCountBean.getRemainingCount() > 0) || remainingCountBean.getRemainingFixedPriceCount() == null) {
                    //总次数还有剩余
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.YOUCHE_PUBLISH_TRANSPORT_NOT_ENOUGH_ERROR, "当前发布次数已用尽，暂不能直接发布，请切换电议有价或一口价"));
                } else {
                    //总次数没有剩余
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.YOUCHE_PUBLISH_TRANSPORT_NOT_ENOUGH_ERROR, "优车发布次数已用尽，暂不能发布"));
                }
            }
        } else if (youchePublishType == GoodCarPriceTransportPublishTypeEnum.havePrice.getCode()) {
            if (remainingCountBean.getRemainingCallPriceCount() != null && remainingCountBean.getRemainingCallPriceCount() <= 0) {
                //电议有价次数已用尽
                if ((remainingCountBean.getRemainingCount() != null && remainingCountBean.getRemainingCount() > 0) || remainingCountBean.getRemainingFixedPriceCount() == null) {
                    //总次数还有剩余
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.YOUCHE_PUBLISH_TRANSPORT_NOT_ENOUGH_ERROR, "当前发布次数已用尽，暂不能直接发布，请切换一口价"));
                } else {
                    //总次数没有剩余
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.YOUCHE_PUBLISH_TRANSPORT_NOT_ENOUGH_ERROR, "优车发布次数已用尽，暂不能发布"));
                }
            }
        } else if (youchePublishType == GoodCarPriceTransportPublishTypeEnum.fixed.getCode()) {
            if (remainingCountBean.getRemainingFixedPriceCount() != null && remainingCountBean.getRemainingFixedPriceCount() <= 0) {
                //一口价次数已用尽
                if ((remainingCountBean.getRemainingCount() != null && remainingCountBean.getRemainingCount() > 0) || remainingCountBean.getRemainingFixedPriceCount() == null) {
                    //总次数还有剩余
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.YOUCHE_PUBLISH_TRANSPORT_NOT_ENOUGH_ERROR, "当前发布次数已用尽，暂不能直接发布，请切换电议"));
                } else {
                    //总次数没有剩余
                    throw TytException.createException(new ResponseCode(ReturnCodeConstant.YOUCHE_PUBLISH_TRANSPORT_NOT_ENOUGH_ERROR, "优车发布次数已用尽，暂不能发布"));
                }
            }
        }
        return true;
    }

    @Override
    public boolean checkSimilarExist(Long srcMsgId) {

        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);

        if (transportMain == null) {
            throw TytException.createException(ResponseEnum.request_error.info("货物不存在"));
        }

        Transport transport = new Transport();
        BeanUtils.copyProperties(transportMain, transport);

        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        transport.setCtime(timestamp);//similarityCode 为当前天

        String similarityCode = transportService.genSimilarityCode(transport);

        boolean exist = transportService.checkOtherSimilarityExist(srcMsgId, similarityCode);

        return exist;
    }

    /**
     * 获取发货帮助提示气泡是否需要展示以及多秒以后展示
     * 展示逻辑：当用户30天内发货量小于3单，且超过5s未离开页面，（单数、秒数通过公共资源配置，一个配置即可），每个用户一天内最多展示1次
     * since V6470
     *
     * @param userId
     * @return ResultMsgBean
     * <AUTHOR>
     * @date 2024/5/28 13:26
     */
    @Override
    public ResultMsgBean getHelpBtnPopupConfig(Long userId) {
        PopupConfigVo.PopupConfigVoBuilder builder = PopupConfigVo.builder().showPopup(false);

        // 每个用户一天最多展示一次
        String cacheValue = cacheService.getString(Constant.CACHE_HELP_BTN_POPUP_KEY_PREFIX + userId);
        if (StringUtils.isNotEmpty(cacheValue)) {
            return ResultMsgBean.successResponse(builder.build());
        }

        String configValue = tytConfigService.getStringValue(Constant.HELP_BTN_POPUP_CONFIG, Constant.POPUP_CONFIG_DEFAULT_VALUE);
        String[] configArr = configValue.split(COMMA);
        // 配置单数 当前用户30天内发货量小于多少单
        int orderNum = Integer.parseInt(configArr[0]);
        // 配置秒数 超过多少秒未离开页面
        int seconds = Integer.parseInt(configArr[1]);

        Date date = DateUtils.addDays(new Date(), -30);
        int publishCount = transportMainService.getPublishCount(userId, date);
        if (publishCount < orderNum) {
            builder.showPopup(true).delaySeconds(seconds);
        }

        return ResultMsgBean.successResponse(builder.build());
    }

    /**
     * 发货帮助提示气泡展示以后上报
     *
     * @param userId
     * @return ResultMsgBean
     * <AUTHOR>
     * @date 2024/5/29 11:00
     */
    @Override
    public ResultMsgBean reportPopup(Long userId) {
        // 每个用户一天内展示一次，从0点到24点
        long seconds = TimeUtil.getTomorrowZeroSeconds();
        cacheService.setString(Constant.CACHE_HELP_BTN_POPUP_KEY_PREFIX + userId, "1", seconds);
        return ResultMsgBean.successResponse();
    }

    /**
     * since v6470
     * 获取定金及退换方式
     * a. 若用户未发过货，则默认填充为100元（金额可通过公共资源配置），退还方式根据用户身份默认填充
     * i. 经分口径个人货主、企业货主，默认选中退还
     * ii. 经分口径货站、物流公司，默认选中不退还
     * iii. 若经分身份未获取成功，则取用户注册选择的身份，若仍然无法获取身份，则默认填充为100元（金额可通过公共资源配置），退还方式默认选中退还
     * iv. 用户注册身份映射：
     * 1. 个人货主=用户注册时选择：个人下面的一手货主
     * 2. 企业货主=用户注册时选择：企业下面的一手货主
     * 3. 货站=用户注册时选择：个人下面的货站+企业下面的货站
     * 4. 物流公司=用户注册时选择：企业下面的物流公司
     * b. 若用户发过货，则默认填充上一票的订金及退还方式
     *
     * @param userId
     * @param invoiceTransport 是否开票货源
     * @return ResultMsgBean
     * <AUTHOR>
     * @date 2024/5/29 14:12
     */
    @Override
    public ResultMsgBean getDepositAndRefundType(Long userId, Integer invoiceTransport) {
        TransportMain lastTransport = transportMainService.getLastTransport(userId);

        if (Objects.nonNull(lastTransport)) {
            logger.info("getDepositAndRefundType userId:{}, 上一单定金:{}，上一单退还方式:{}", userId, lastTransport.getInfoFee(), lastTransport.getRefundFlag());

            DepositAndRefundTypeVo typeVo = DepositAndRefundTypeVo.builder().deposit(lastTransport.getInfoFee()).refundFlag(lastTransport.getRefundFlag()).build();
            if (invoiceTransport != null && invoiceTransport == 1) {
                typeVo.setRefundFlag(RefundFlagEnum.RETURN.getCode());
            }
            return ResultMsgBean.successResponse(typeVo);
        }

        RefundFlagEnum returnEnum = RefundFlagEnum.RETURN;
        String defaultDeposit = tytConfigService.getStringValue(Constant.DEPOSIT_DEFAULT_VALUE_KEY, Constant.DEPOSIT_DEFAULT_VALUE);

        // 1、先获取经分同步表中的用户身份
        RefundFlagEnum biRefundEnum = getRefundFlagEnumFromBiSyncTable(userId);
        // 2、经分身份未获取成功，获取用户注册身份
        if (Objects.isNull(biRefundEnum)) {
            returnEnum = getRefundFlagEnumFromUserRegister(userId, returnEnum);
        } else {
            returnEnum = biRefundEnum;
        }

        DepositAndRefundTypeVo vo = DepositAndRefundTypeVo.builder().deposit(new BigDecimal(defaultDeposit)).refundFlag(returnEnum.getCode()).build();
        if (invoiceTransport != null && invoiceTransport == 1) {
            vo.setRefundFlag(RefundFlagEnum.RETURN.getCode());
        }
        return ResultMsgBean.successResponse(vo);
    }

    /**
     * 获取智能订金
     *
     * @param bean
     * @return ResultMsgBean
     * <AUTHOR>
     * @date 2024/8/23 10:31
     */
    @Override
    public ResultMsgBean intelligentDeposit(IntelligentDepositBean bean) throws Exception {
        DepositTypeEnum depositType = DepositTypeEnum.NORMAL;
        if (!Objects.equals(bean.getInvoiceTransport(), InvoiceTransportEnum.YES.getCode())) {
            // 专票货源不走智能订金
            TytAbtestConfig depositTypeConfig = abtestService.getAbTestConfig("deposit_type_config");
            if (Objects.nonNull(depositTypeConfig)) {
                if (Objects.equals(depositTypeConfig.getRuleType(), AbTestRuleTypeEnum.USER.getCode())) {
                    // ab测试类型为用户，判断当前用户是否在ab测内
                    depositType = getDepositType(bean.getUserId(), depositTypeConfig);
                } else {
                    // ab测试类型为全部，直接取ab测配置
                    depositType = DepositTypeEnum.getDepositTypeByCode(depositTypeConfig.getDefaultType());
                }
            }
        }
        Integer refundFlag = RefundFlagEnum.RETURN.getCode();
        BigDecimal deposit = new BigDecimal("100");
        if (DepositTypeEnum.INTELLIGENT == depositType) {
            // 订金退还方式 0-关闭，订金退还方式取上一票；1-开启，订金退还
            Integer refundConfig = tytConfigService.getIntValue("intelligent_deposit_default_return", 0);
            if (Objects.nonNull(refundConfig) && refundConfig == 0) {
                // 用户被加入到智能订金模式时，第一次发货在智能订金的模式下都默认订金可退。 工单：SSRS-1180
                Long userRecordId = tytIntelligentDepositUserRecordMapper.selectByUserId(bean.getUserId());
                if (Objects.nonNull(userRecordId)) {
                    TransportMain lastTransport = transportMainService.getLastTransport(bean.getUserId());
                    if (Objects.nonNull(lastTransport.getRefundFlag())) {
                        refundFlag = lastTransport.getRefundFlag();
                    }
                }
            }
            // 订金金额
            if (Objects.nonNull(bean.getDistance()) && Objects.nonNull(bean.getWeight())) {
                TytIntelligentDepositConfig depositConfig = tytIntelligentDepositConfigMapper
                        .getConfigByWeightAndDistance(bean.getWeight(), bean.getDistance());
                if (Objects.nonNull(depositConfig) && Objects.nonNull(depositConfig.getDeposit())) {
                    deposit = new BigDecimal(depositConfig.getDeposit());
                }
            }
        } else {
            // 手动订金模式
            TransportMain lastTransport = transportMainService.getLastTransport(bean.getUserId());
            if (Objects.nonNull(lastTransport)) {
                // 用户发过货，默认填充上一票的订金和退还方式
                refundFlag = lastTransport.getRefundFlag();
                deposit = lastTransport.getInfoFee();
                if (Objects.isNull(deposit) || deposit.compareTo(new BigDecimal(0)) == 0) {
                    deposit = new BigDecimal(50);
                }
                if (Objects.equals(InvoiceTransportEnum.YES.getCode(), bean.getInvoiceTransport())) {
                    refundFlag = RefundFlagEnum.RETURN.getCode();
                }
            } else {
                String defaultDeposit = tytConfigService.getStringValue(Constant.DEPOSIT_DEFAULT_VALUE_KEY, Constant.DEPOSIT_DEFAULT_VALUE);
                deposit = new BigDecimal(defaultDeposit);
                if (Objects.equals(InvoiceTransportEnum.YES.getCode(), bean.getInvoiceTransport())) {
                    refundFlag = RefundFlagEnum.RETURN.getCode();
                } else {
                    RefundFlagEnum returnEnum = RefundFlagEnum.RETURN;
                    // 1、先获取经分同步表中的用户身份
                    RefundFlagEnum biRefundEnum = getRefundFlagEnumFromBiSyncTable(bean.getUserId());
                    // 2、经分身份未获取成功，获取用户注册身份
                    if (Objects.isNull(biRefundEnum)) {
                        returnEnum = getRefundFlagEnumFromUserRegister(bean.getUserId(), returnEnum);
                    } else {
                        returnEnum = biRefundEnum;
                    }
                    refundFlag = returnEnum.getCode();
                }
            }
            // 订金默认填充ab测，圈选用户不走默认填充逻辑
            Integer depositDefaultFill = abtestService.getUserType("deposit_default_fill", bean.getUserId());
            if (Objects.nonNull(depositDefaultFill) && depositDefaultFill == 1) {
                deposit = null;
            }
        }

        DepositAndRefundTypeVo vo = DepositAndRefundTypeVo.builder()
                .deposit(deposit)
                .refundFlag(refundFlag)
                .depositType(depositType.getCode())
                .build();
        return ResultMsgBean.successResponse(vo);
    }

    /**
     * 获取定金类型
     *
     * @param userId
     * @param depositTypeConfig
     * @return
     * @throws Exception
     */
    private DepositTypeEnum getDepositType(Long userId, TytAbtestConfig depositTypeConfig) throws Exception {
        // ab测试类型为用户，判断当前用户是否在ab测内
        Integer userType = abtestService.getUserTypeByAbTestIdAndUserId(depositTypeConfig.getId(), userId);
        if (Objects.nonNull(userType)) {
            return DepositTypeEnum.getDepositTypeByCode(userType);
        }
        // 当前用户不在ab测内，判断注册时间是否在配置的时间段内，并且用户类型是个人货主或企业货主
        String dateRange = tytConfigService.getStringValue("intelligent_deposit_user_match");
        if (StringUtils.isBlank(dateRange)) {
            return DepositTypeEnum.NORMAL;
        }
        String[] dateRangeArr = dateRange.split("-");
        if (dateRangeArr.length != 2) {
            return DepositTypeEnum.NORMAL;
        }
        String startDate = dateRangeArr[0];
        String endDate = dateRangeArr[1];
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return DepositTypeEnum.NORMAL;
        }
        User user = userService.getByUserId(userId);
        boolean dateInRange = TimeUtil.dateInRange(startDate, endDate, user.getCtime());
        if (!dateInRange) {
            // 注册时间不在配置的时间范围内
            return DepositTypeEnum.NORMAL;
        }
        // 判断用户注册类型是否是：个人货主，企业货主
        TytUserIdentityLabel label = tytUserIdentityLabelMapper.getUserGoodsType(userId);
        if (Objects.nonNull(label)) {
            UserGoodsTypeEnum goodsTypeEnum = UserGoodsTypeEnum.getByGoodsType(label.getGoodsTypeFirst(), label.getGoodsTypeSecond());
            if (UserGoodsTypeEnum.SELF_CARGO_OWNER == goodsTypeEnum || UserGoodsTypeEnum.ENTERPRISE_CARGO_OWNER == goodsTypeEnum) {
                return DepositTypeEnum.INTELLIGENT;
            }
        }
        return DepositTypeEnum.NORMAL;
    }

    /**
     * 根据经分同步身份表，确认用户身份
     *
     * @param userId
     * @return
     */
    private RefundFlagEnum getRefundFlagEnumFromBiSyncTable(Long userId) {
        RefundFlagEnum returnEnum = null;
        NewIdentity newIdentity = newIdentityService.getByUserId(userId);
        if (Objects.nonNull(newIdentity)) {
            NewIdentityEnum identityEnum = NewIdentityEnum.getByCode(newIdentity.getType());
            if (Objects.nonNull(identityEnum)) {
                switch (identityEnum) {
                    case CARGO_TERMINAL:
                    case LOGISTICS_COMPANY:
                        returnEnum = RefundFlagEnum.NO_RETURN;
                        break;
                    case SELF_CARGO_OWNER:
                    case ENTERPRISE_CARGO_OWNER:
                        returnEnum = RefundFlagEnum.RETURN;
                        break;
                    default:
                        break;
                }
            }
        }
        return returnEnum;
    }

    /**
     * 从用户注册身份获取退款类型
     *
     * @param userId
     * @param returnEnum
     * @return
     */
    private RefundFlagEnum getRefundFlagEnumFromUserRegister(Long userId, RefundFlagEnum returnEnum) {
        TytUserIdentityLabel label = tytUserIdentityLabelMapper.getUserGoodsType(userId);
        if (Objects.nonNull(label)) {
            UserGoodsTypeEnum goodsTypeEnum = UserGoodsTypeEnum.getByGoodsType(label.getGoodsTypeFirst(), label.getGoodsTypeSecond());
            if (Objects.nonNull(goodsTypeEnum)) {
                switch (goodsTypeEnum) {
                    case SELF_CARGO_TERMINAL:
                    case ENTERPRISE_CARGO_TERMINAL:
                    case ENTERPRISE_LOGISTICS_COMPANY:
                        returnEnum = RefundFlagEnum.NO_RETURN;
                        break;
                    default:
                        break;
                }
            }
        }
        return returnEnum;
    }

    /**
     * 保存智能订金用户操作记录
     *
     * @param bean
     * @return
     */
    @Override
    public ResultMsgBean depositRecord(IntelligentDepositBean bean) {
        TytIntelligentDepositUserRecord record = new TytIntelligentDepositUserRecord();
        BeanUtils.copyProperties(bean, record);
        tytIntelligentDepositUserRecordMapper.insertSelective(record);
        return ResultMsgBean.successResponse();
    }

    /**
     * 判断当前货源是否是 17.5 米专享货源
     *
     * @param publishBean
     * @return
     */
    public boolean isExclusive(TransportPublishBean publishBean) {
        List<BigDecimal> decList = new ArrayList<>();
        decList.add(publishBean.getCarMinLength());//最小长度
        decList.add(publishBean.getCarMaxLength());//最大长度

        decList.add(publishBean.getWorkPlaneMinLength());//最小工作面长
        decList.add(publishBean.getWorkPlaneMaxLength());//最大工作面长

        decList.add(publishBean.getWorkPlaneMinHigh());//最小工作面高
        decList.add(publishBean.getWorkPlaneMaxHigh());//最大工作面高

        BigDecimal lengthDec = new BigDecimal("17.5");
        for (BigDecimal oneDec : decList) {
            if (oneDec != null && oneDec.compareTo(lengthDec) == 0) {
                logger.info("dec_transoprt_17.5米专享： " + oneDec);
                return true;
            }
        }

        List<String> strList = new ArrayList<>();
        strList.add(publishBean.getTaskContent());//名称
        strList.add(publishBean.getRemark());//备注
        strList.add(publishBean.getCarLengthLabels());//长度标签

        strList.add(publishBean.getCarStyle());//挂车样式
        strList.add(publishBean.getCarType());//挂车型号

        //不符合的吨结尾
        String regFilterTon = "17\\.5米?吨";

        //前或后多数字
        String regFilterNum = "(\\d+17\\.5\\d*)|(\\d*17\\.5\\d+)";

        String regRight = "17\\.5米?";

        Pattern compile = Pattern.compile(EXCLUSIVE_REG);
        String lengthTxt = "17.5";
        for (String oneText : strList) {
            if (StringUtils.isNotBlank(oneText)) {
                //包含17.5米、17.5（不包含17.5吨）、17米5
                //排除：17米5吨，17.5米吨，及“以上”，及前后数字

                //替换排除为吨（以便只处理排除吨的情况）
                oneText = oneText.replace("以上", "吨");
                //替换17米5 为  17.5
                oneText = oneText.replace("17米5", "17.5");

                //移除前后多数字的
                oneText = oneText.replaceAll(regFilterNum, "@");
                //移除不符合的“吨”结尾的
                oneText = oneText.replaceAll(regFilterTon, "#");

                //判断17.5米是否存在
                boolean exist = CommonUtil.regFind(regRight, oneText);

                if (exist) {
                    return true;
                }
            }
        }

        return false;
    }

    @Override
    public boolean checkExcellentGoodsForPermission(Long userId) {
        TytDepositAuthorization tytDepositAuthorization = depositService.selectDepositAuthByUserId(userId);
        if (tytDepositAuthorization == null || tytDepositAuthorization.getAuthStatus() == 0) {
            return false;
        }
        if (tytDepositAuthorization.getBlackStatus() == 1) {
            return false;
        }

        DepositBlockCheckDTO depositBlockCheckDTO = depositService.checkDepositBlock(userId, true);
        if (depositBlockCheckDTO.isBlock()) {
            return false;
        }

        // 查询是否有退保证金操作
        List<TytDepositApplyAudit> applyAuditList = depositService.selectRefundAuditByUserId(userId, AuditStatusEnum.审核中.getStatus(), AuditEnsureTypeEnum.退还保证金.getType());
        if (CollectionUtils.isNotEmpty(applyAuditList)) {
            return false;
        }
        // 查询余额账户
        TytDepositAccount availableAccount = depositService.getDepositAccount(userId, BALANCE_TYPE_NORMAL);
        if (availableAccount == null) {
            return false;
        }

        TytDepositAccount frozenAccount = depositService.getDepositAccount(userId, BALANCE_TYPE_FROZEN);
        BigDecimal totalBalance = frozenAccount == null ? availableAccount.getBalance() : availableAccount.getBalance().add(frozenAccount.getBalance());

        if (totalBalance.compareTo(tytDepositAuthorization.getRequireAmount().divide(new BigDecimal(2))) < 0) {
            return false;
        }

        // 查询优货剩余使用次数
        ExcellentGoodsRemainCountBean remainingCountBean = depositService.getRemainingCount(userId);
        return remainingCountBean.getRemainingCount() == null || remainingCountBean.getRemainingCount() > 0;
    }

//    @Override
//    public ResultMsgBean savePublishTransport(TransportPublishBean publishBean) throws Exception {
//        ResultMsgBean msgBean = new ResultMsgBean();
//        //增加发货限制验证
//        msgBean = this.validationLimitPublish(publishBean.getUserId());
//        if (ReturnCodeConstant.OK != msgBean.getCode()) {
//            return msgBean;
//        }
//        //判断编辑后台货源是否下架
//        BackendTransportBean backendTransportBean=null;
//        if (publishBean.getBackendId()!=null&&publishBean.getBackendId()!=0L){
//            backendTransportBean = backendTransportMapper.selectBackendStatusById(publishBean.getBackendId());
//            if (backendTransportBean!=null&&!"1".equals(backendTransportBean.getStatus().toString())){
//                msgBean.setCode(ReturnCodeConstant.EDIT_TIMEOUT);
//                msgBean.setMsg(ReturnCodeConstant.ARGUMENTS_EDIT_TIMEOUT_MSG);
//                return msgBean;
//            }else {
//                Date date = backendTransportMapper.selectPublishTime(publishBean.getBackendId(), backendTransportBean.getBatch());
//                if (date!=null&&new Date().compareTo(date)>0){
//                    msgBean.setCode(ReturnCodeConstant.EDIT_TIMEOUT);
//                    msgBean.setMsg(ReturnCodeConstant.ARGUMENTS_EDIT_TIMEOUT_MSG);
//                    return msgBean;
//                }
//            }
//        }
//
//        Long oldId = publishBean.getTsId() == null ? 0L : publishBean.getTsId();
//        if (oldId!=0){
//            BackendTransportBean backendTransport = backendTransportMapper.selectBackendStatusByMsgId(oldId);
//            if (backendTransport!=null&&"2".equals(backendTransport.getStatus().toString())){
//                msgBean.setCode(ReturnCodeConstant.EDIT_TIMEOUT);
//                msgBean.setMsg(ReturnCodeConstant.ARGUMENTS_EDIT_TIMEOUT_MSG);
//                return msgBean;
//            }
//        }
//
//        //排除人工派单的货源，不进行手机号校验
//        int isMt = publishBean.getIsMt() == null ? 0 : publishBean.getIsMt();
//        Long userId = publishBean.getUserId();
//        /** 特运通账户ID */
//        String companyAccountUserId = tytConfigService.getStringValue("tyt_company_account_user_id");
//        if (isMt == 0 && userId.toString().equals(companyAccountUserId)) {
//            // 验证电话是否为本人联系电话，人工排单不进行验证；
//            msgBean = validationTel(publishBean);
//            if (ReturnCodeConstant.OK != msgBean.getCode()) {
//                return msgBean;
//            }
//        }
//
//        // 验证货物内容是否有 散货信息
//        msgBean = validationTaskContent(publishBean);
//        if (ReturnCodeConstant.OK != msgBean.getCode()) {
//            return msgBean;
//        }
//
//        // 验证用户身份
//        msgBean = userService.checkUserPhotoVerifyFlag(publishBean.getUserId(), publishBean.getClientSign());
//        if (ReturnCodeConstant.OK != msgBean.getCode()) {
//            return msgBean;
//        }
//        Transport transport = new Transport();
//
//        /* 生成transport对象 */
//        transport = createTransport(publishBean);
//        //区分新老版本的货源标准化
//        if (StringUtils.isNotEmpty(publishBean.getClientVersion()) && (Integer.valueOf(publishBean.getClientVersion()) >= 6000 || Integer.valueOf(publishBean.getClientSign()) == 1)) {
//            // 拆版后新版本标准化查询
//            if (publishBean.getMatchItemId() != null && publishBean.getMatchItemId() != -1) {
//                TytMachineTypeBrandNew machineTypeBrandNew = machineTypeBrandNewService.getById(publishBean.getMatchItemId());
//                if (machineTypeBrandNew != null) {
//                    transport.setIsStandard(Constant.STANDARD_STATUS_STANDARD);
//                    transport.setType(machineTypeBrandNew.getTopType());
//                    transport.setBrand(machineTypeBrandNew.getBrand());
//                    transport.setGoodTypeName(machineTypeBrandNew.getSecondClass());
//                    if(Integer.valueOf(publishBean.getClientSign()) == 1) { // PC客户端未传值，设置为1
//                        transport.setGoodNumber(1);
//                    }
//                }
//            } else {
//                transport.setIsStandard(1);
//                transport.setMatchItemId(-1);
//            }
//        } else {
//            // 服务器过滤标准化货源
//            if (transport.getIsStandard() == null || transport.getIsStandard().intValue() == 1) {
//                StandardStatusBean standardStatusBean = keywordMatchesNewService.queryStandardStatusByKeyword(transport.getTaskContent());
//                logger.info("query statard status by keyword: " + transport.getTaskContent() + ", result is: " + standardStatusBean);
//                if (standardStatusBean.getStandardStatus().intValue() == 0) {
//                    transport.setIsStandard(Constant.STANDARD_STATUS_STANDARD);
//                    transport.setMatchItemId(standardStatusBean.getId());
//                    transport.setGoodNumber(standardStatusBean.getGoodNumber());
//                    transport.setType(standardStatusBean.getType());
//                    transport.setBrand(standardStatusBean.getBrand());
//                    transport.setGoodTypeName(standardStatusBean.getMachineType());
//                } else {
//                    transport.setIsStandard(1);
//                    transport.setMatchItemId(-1);
//                }
//            }
//        }
//        /* 生成transport子表对象 by tianjw on 20170720 */
//        TransportSubBean transportSub = createTransportSub(publishBean);
//
//        /* hashcode验证是否重复信息 */
//        msgBean = isNotPublishTransport(transport);
//        if (msgBean.getCode() != ReturnCodeConstant.OK) {
//            return msgBean;
//        }
//
//        // 是否新发布货物
//        PublisTypeEnum publishType = PublisTypeEnum.新发布货物;
//        TransportMain oldTran = null;
//
//
//        if (oldId != 0) {
//            oldTran = transportMainService.getTransportMainForId(oldId);
//            boolean isYesterdayPublish = oldTran.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime();
//            if (isYesterdayPublish) {
//                publishType = PublisTypeEnum.历史货物编辑发布;
//            } else {
//                publishType = PublisTypeEnum.当日货物编辑发布;
//            }
//        }
//        //5930开关是否打开
//        boolean isOpen = tytConfigService.isEqualsOne(Constant.EQUITY_ON_OFF);
//        if (isOpen) {
//            if (userPermissionService.isVipPublishPermission(userId)) {
//                transport.setUserType(1);
//            } else {
//                transport.setUserType(0);
//            }
//
//        }
//
//        if (!PublisTypeEnum.当日货物编辑发布.equals(publishType)) {
//            // 是否启用权益验证 5930以后版本
//            if (isOpen) {
//                msgBean = userService.checkUserPublishTransportPermission(userId);
//                if (ReturnCodeConstant.OK != msgBean.getCode()) {
//                    return msgBean;
//                }
//            }
//        }
//
//        /* 保存数据到数据库，缓存 */
//        Transport newTransport = this.addTransportBusiness(transport, transportSub, oldTran, publishType, publishBean,backendTransportBean);
//
//        // 更新相似货源信息及首发信息
//        transportService.updateSimilarityCode(newTransport);
//
//        // 将发布成功的货源唯一值放到缓存中；
//        String key = Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + transport.getHashCode();
//        cacheService.setObject(key, transport.getHashCode(), Constant.CACHE_EXPIRE_TIME_24H);
//        logger.info("货物信息发布成功" + publishBean.toString());
//
//        msgBean = getPublishMsg(newTransport);
//
//        return msgBean;
//    }

    /**
     * 发布信息参数校验/过滤校验
     *
     * @param publishBean 发布参数
     * @return ResultMsgBean对象，成功200
     */
    private ResultMsgBean validationTransportPublishBean(TransportPublishBean publishBean) {
        logger.info("消息发布开始地址的未补全参数，省市区县:{}, x轴:{}：", "区县" + publishBean.getStartArea() + "，省" + publishBean.getStartProvinc() + "，市" + publishBean.getStartCity(), "x:" + publishBean.getStartCoordX() + "y:" + publishBean.getStartCoordY());
        logger.info("消息发布到达地址的未补全参数，省市区县:{}, x轴:{}：", "区县" + publishBean.getDestArea() + "，省" + publishBean.getDestProvinc() + "，市" + publishBean.getDestCity(), "x:" + publishBean.getDestCoordX() + "y:" + publishBean.getDestCoordY());

        /**  2020-10-19 货物重量只能为数字，非数字不允许发货 */
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getWeight())) {
            publishBean.setWeight(publishBean.getWeight().trim());
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(publishBean.getWeight()) && !StringUtil.isDouble(publishBean.getWeight())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "货物重量格式错误，请输入数字");
        }

        // 校验出发地、目的地详细地址长度是否超限
        logger.info("校验出发地目的地详细地址长度是否超限");
        if (StringUtils.isNotBlank(publishBean.getStartDetailAdd()) && publishBean.getStartDetailAdd().length() > 200) {
            logger.error("装货地地址过长，请更换附近其他地址，{}", publishBean.getStartDetailAdd());
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "装货地地址过长，请更换附近其他地址");
        }
        if (StringUtils.isNotBlank(publishBean.getDestDetailAdd()) && publishBean.getDestDetailAdd().length() > 200) {
            logger.error("卸货地地址过长，请更换附近其他地址，{}", publishBean.getDestDetailAdd());
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "卸货地地址过长，请更换附近其他地址");
        }

        //出发地和目的地以及坐标轴参数修补
        completionStartAndDestParameter(publishBean);

        /**  2020-07-12 出发地坐标为空判断，不允许发货 */

        if (publishBean.getStartCoordX() == null || publishBean.getStartCoordY() == null || publishBean.getStartCoordX().compareTo(BigDecimal.ZERO) == 0 || publishBean.getStartCoordY().compareTo(BigDecimal.ZERO) == 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "出发地不正确，请更换");
        }
        /**  2020-07-12 目的地坐标为空判断，不允许发货 */

        if (publishBean.getDestCoordX() == null || publishBean.getDestCoordY() == null || publishBean.getDestCoordX().compareTo(BigDecimal.ZERO) == 0 || publishBean.getDestCoordY().compareTo(BigDecimal.ZERO) == 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "目的地不正确，请更换");
        }

        if (org.apache.commons.lang.StringUtils.isBlank(publishBean.getStartProvinc())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "出发地参数不能为空");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(publishBean.getDestProvinc())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "目的地参数不能为空");
        }
        if (publishBean.getStartCoordX() == null) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "startCoordX" + "参数不能为空");
        }
        if (publishBean.getStartCoordY() == null) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "startCoordY" + "参数不能为空");
        }

        if (publishBean.getDestCoordX() == null) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "destCoordX" + "参数不能为空");
        }
        if (publishBean.getDestCoordY() == null) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "destCoordY" + "参数不能为空");
        }

        String timeErrorMsg = "装卸货时间已过期请重新选择";
        if (publishBean.getLoadingTime() != null && new Date().compareTo(publishBean.getLoadingTime()) > 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, timeErrorMsg);
        }
        if (publishBean.getUnloadTime() != null && new Date().compareTo(publishBean.getUnloadTime()) > 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, timeErrorMsg);
        }


        String taskContent = publishBean.getTaskContent();
        if (org.apache.commons.lang.StringUtils.isBlank(taskContent)) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "货物内容不能为空");
        }
        taskContent = EmojiParser.removeAllEmojis(taskContent);
        //清除3个字节以上的字符
        taskContent = StringUtil.clearGarbledCode(taskContent);
        logger.info("taskContent=" + publishBean.getTaskContent() + " EmotaskContent=" + taskContent);

        if (org.apache.commons.lang.StringUtils.isBlank(taskContent)) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "货物内容不能有非法字符！");
        }
        publishBean.setTaskContent(taskContent);

        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getCarType())) {
            publishBean.setCarType(EmojiParser.removeAllEmojis(publishBean.getCarType()));
            logger.info("carType:" + publishBean.getCarType() + "-----------------------");
        }

        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getCarStyle())) {
            publishBean.setCarStyle(EmojiParser.removeAllEmojis(publishBean.getCarStyle()));
            logger.info("carStyle:" + publishBean.getCarStyle() + "-----------------------");
        }

        if (publishBean.getStartLongitude() == null || publishBean.getStartLongitude().compareTo(BigDecimal.ZERO) < 1) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "startLongitude" + "参数不能小于0");
        }
        if (publishBean.getStartLatitude() == null || publishBean.getStartLatitude().compareTo(BigDecimal.ZERO) < 1) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "startLatitude" + "参数不能小于0");
        }
        if (publishBean.getDestLongitude() == null || publishBean.getDestLongitude().compareTo(BigDecimal.ZERO) < 1) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "destLongitude" + "参数不能小于0");
        }
        if (publishBean.getDestLatitude() == null || publishBean.getDestLatitude().compareTo(BigDecimal.ZERO) < 1) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "destLatitude" + "参数不能小于0");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(publishBean.getTel()) || isMobileOrFixedPhone(publishBean.getTel())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "电话格式有误或者不能为空");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getTel3()) && isMobileOrFixedPhone(publishBean.getTel3())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "电话格式有误");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getTel4()) && isMobileOrFixedPhone(publishBean.getTel4())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "电话格式有误");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getWide()) && !StringUtil.isDouble(publishBean.getWide())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "货物宽度参数格式有误");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getLength()) && !StringUtil.isDouble(publishBean.getLength())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "货物长度参数格式有误");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getHigh()) && !StringUtil.isDouble(publishBean.getHigh())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "货物高度参数格式有误");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getPrice()) && !StringUtil.isDouble(publishBean.getPrice())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "价格参数格式有误，请输入数字");
        }

        String climb = publishBean.getClimb();
        if (StringUtils.isNotBlank(climb) && !NumberUtils.isDigits(climb)) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "是否需要带爬梯格式有误！");
        }
        String tyreExposedFlag = publishBean.getTyreExposedFlag();
        if (StringUtils.isNotBlank(tyreExposedFlag) && !NumberUtils.isDigits(tyreExposedFlag)) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "轮胎外露标识格式有误！");
        }
        String temp = publishBean.getDestDetailAdd();
        if (org.apache.commons.lang.StringUtils.isNotBlank(temp)) {
            publishBean.setDestDetailAdd(EmojiParser.removeAllEmojis(temp));

        }
        if (null != publishBean.getTecServiceFee() && (new BigDecimal(0).compareTo(publishBean.getTecServiceFee()) > 0 || publishBean.getTecServiceFee().compareTo(new BigDecimal(999999)) > 0)) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "技术服务费格式有误！");
        }
        temp = publishBean.getStartDetailAdd();
        if (org.apache.commons.lang.StringUtils.isNotBlank(temp)) {

            publishBean.setStartDetailAdd(EmojiParser.removeAllEmojis(temp));
        }
        temp = publishBean.getRemark();
        if (org.apache.commons.lang.StringUtils.isNotBlank(temp)) {
            String remarkTmp = EmojiParser.removeAllEmojis(temp);
            remarkTmp = StringUtil.clearGarbledCode(remarkTmp);

            publishBean.setRemark(remarkTmp);

        }
        // 专车发货，如果是代调用户，签约合作商为必选项
        if (Objects.equals(publishBean.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())) {
            int count = tytDispatchCompanyMapper.countByUserId(publishBean.getUserId());
            if (count > 0 && (Objects.isNull(publishBean.getCargoOwnerId()) || publishBean.getCargoOwnerId() == 0)) {
                return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "请选择签约合作商");
            }
            // 校验运费，灵活运价用户调整后的运费不能小于运价下限
            CalculatePriceBean priceBean = new CalculatePriceBean();
            priceBean.setUserId(publishBean.getUserId());
            priceBean.setCargoOwnerId(publishBean.getCargoOwnerId());
            priceBean.setOtherFee(publishBean.getOtherFee());
            priceBean.setStartCity(publishBean.getStartCity());
            priceBean.setDestCity(publishBean.getDestCity());
            priceBean.setWeight(publishBean.getWeight());
            if (Objects.equals(RefundFlagEnum.NO_RETURN.getCode(), publishBean.getRefundFlag())) {
                priceBean.setInfoFee(publishBean.getInfoFee());
            }
            priceBean.setDistanceKilometer(publishBean.getDistanceKilometer());
            priceBean.setDriverDriving(publishBean.getDriverDriving());
            priceBean.setUseCarType(publishBean.getUseCarType());
            priceBean.setClientVersion(publishBean.getClientVersion());
            ResultMsgBean priceResult = calculatePriceV2(priceBean);
            JSONObject priceJson = JSONObject.parseObject(priceResult.getData().toString());
            Integer priceType = priceJson.getInteger("priceType");
            if (Objects.equals(PriceTypeEnum.FLEXIBLE.getCode(), priceType)) {
                BigDecimal lowerLimitPrice = priceJson.getBigDecimal("lowerLimitPrice");
                String lowerLimit = priceJson.getString("lowerLimit");
                BigDecimal userSubmitPrice = new BigDecimal(publishBean.getPrice());
                if (userSubmitPrice.compareTo(lowerLimitPrice) < 0) {
                    return new ResultMsgBean(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                            "您当前调整的运费过低，请在" + lowerLimit + "%范围内调整运费金额");
                }
            }
            publishBean.setPriceType(priceType);
        }
        Integer refundFlag = publishBean.getRefundFlag();
        if (Objects.isNull(refundFlag)) {
            //默认0 不退还
            publishBean.setRefundFlag(0);
        }
        return new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");

    }

    /**
     * 货站接单版相关操作
     *
     * @param backendId
     */
    private void backendTransport(Long backendId, Transport transport, BackendTransportBean backendTransportBean) {
        backendTransportBean.setId(backendId);
        backendTransportBean.setMsgId(transport.getSrcMsgId());
        backendTransportBean.setReceiverUserId(transport.getUserId());
        backendTransportBean.setReceiverPhone(transport.getUploadCellPhone());
        backendTransportBean.setReceiverShowName(transport.getUserShowName());
        backendTransportMapper.updateBackendTransportStatus(backendTransportBean);
        backendTransportMapper.updateBackendTransportUserStatus(backendId);

        //记录企业货源状态流转日志
        OwnerCompanyLog ownerCompanyLog = new OwnerCompanyLog();
        ownerCompanyLog.setOrderNo(backendTransportBean.getOrderNo());
        ownerCompanyLog.setBackendId(backendTransportBean.getId());
        ownerCompanyLog.setCompanyId(backendTransportBean.getReceiverUserId());
        ownerCompanyLog.setEnterpriseId(backendTransportBean.getAppletsUserId());
        ownerCompanyLog.setStatus(3);
        ownerCompanyLog.setOrderStatus(31);
        ownerCompanyLog.setSrcMsgId(backendTransportBean.getMsgId());
        ownerCompanyLogService.addOwnerCompanyLog(ownerCompanyLog);
       /* if (backendTransportBean.getAppletsUserId()!=null){
            // 根据短信key获取短信模板
            String content = messageTmplService.getSmsTmpl(Constant.LIMITED_TRANSPORT_RECEIVING, "${startAndDestPoint}，已被[${nameAndPhone}]接单。");
            String applySucesscontent = StringUtils.replaceEach(content, new String[]{"${startAndDestPoint}","${nameAndPhone}"}, new String[]{backendTransportBean.getStartPoint()+"→"+backendTransportBean.getDestPoint()+backendTransportBean.getTaskContent(),
                    backendTransportBean.getReceiverShowName()+StringUtil.replaceMobile(backendTransportBean.getReceiverPhone())});
            logger.info("货站接单成功通知货主短信消息内容:{}",applySucesscontent);
            tytMqMessageService.sendSms(backendTransportBean.getReceiverPhone(),applySucesscontent);
        }*/
    }

    private ResultMsgBean getPublishMsg(Transport transport) {

        BoPublishTransportBean boPublishTransportBean = new BoPublishTransportBean();
        BeanUtils.copyProperties(transport, boPublishTransportBean);
        boPublishTransportBean.setTsId(transport.getId());
        boPublishTransportBean.setSuccessMsg("");
        boPublishTransportBean.setHasDestDetail(
                (StringUtils.isNotBlank(transport.getDestDetailAdd())
                        && Objects.equals(transport.getDestDetailAdd(), transport.getDestArea())) ? 1 : 0);

        TransportLabelJson transportLabelJson = transportBusiness.getTransportLabelJson(transport.getLabelJson());
        boPublishTransportBean.setInstantGrab(transportLabelJson.getInstantGrab());
        ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.OK, "信息发布成功");
        msgBean.setData(boPublishTransportBean);
        return msgBean;


    }

    /**
     * 适配用户等级（V6290 信用曝光按钮）
     * <p>
     * return:5/4/3分别表示S/A/B （如果为0即为B级以下）（如果开关关闭返回0）
     */
    private int checkIsShowCreditExposure(Transport transport) {
        Integer onOff = tytConfigService.getIntValue(Constant.PUBLISH_CREDIT_RETOP_ONOFF, 0);
        //开关关闭直接返回空串
        if (onOff.equals(0)) {
            return 0;
        }
        Integer rankLevel = transport.getRankLevel();
        if (rankLevel == null || rankLevel < 3) {
            return 0;
        }

        return rankLevel;
    }

    /**
     * 验证用户是不是发货受限制
     *
     * @param user
     * @return
     */

    public ResultMsgBean validationLimitPublish(User user) throws Exception {

        ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");
        String limitTime = RedisUtil.get(Constant.CACHE_LIMIT_TRANSPORT_TIME + user.getId());
        /** 有没有发布权限检测 */
        if (user.getInfoPublishFlag() == User.INFO_PUBLISH_DISABLE) {

            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setMsg(null);
            msgBean.setData("您的上传权限未开通，请联系客服。");
            msgBean.setNoticeData(tytNoticePopupTemplService.getByType(800, 3));
            return msgBean;

        }
        if ("5分钟".equals(limitTime)) {
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setMsg(null);
            msgBean.setData("发货受限");
            msgBean.setNoticeData(tytNoticePopupTemplService.getByType(800, 1));
            return msgBean;
        } else if ("24小时".equals(limitTime)) {
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setMsg(null);
            msgBean.setData("发货受限");
            msgBean.setNoticeData(tytNoticePopupTemplService.getByType(800, 2));
            return msgBean;
        }
        return blacklistUserService.checkLimitPublish(user.getId());
    }

    /**
     * 验证用户是不是发货受限制
     *
     * @param userId
     * @return
     */

    public ResultMsgBean validationLimitPublish(Long userId) throws Exception {

        ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");
        String limitTime = RedisUtil.get(Constant.CACHE_LIMIT_TRANSPORT_TIME + userId);
        User user = userService.getByUserId(userId);
        /** 有没有发布权限检测 */
        if (user.getInfoPublishFlag() == User.INFO_PUBLISH_DISABLE) {

            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setMsg(null);
            msgBean.setData("您的上传权限未开通，请联系客服。");
            msgBean.setNoticeData(tytNoticePopupTemplService.getByType(800, 3));
            return msgBean;

        }
        if ("5分钟".equals(limitTime)) {
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setMsg(null);
            msgBean.setData("发货受限");
            msgBean.setNoticeData(tytNoticePopupTemplService.getByType(800, 1));
            return msgBean;
        } else if ("24小时".equals(limitTime)) {
            msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
            msgBean.setMsg(null);
            msgBean.setData("发货受限");
            msgBean.setNoticeData(tytNoticePopupTemplService.getByType(800, 2));
            return msgBean;
        }
        return blacklistUserService.checkLimitPublish(user.getId());
    }

    /**
     * 根据省市区查询对应的x轴和y轴
     *
     * @param province 省份
     * @param cityName 市
     * @param areaName 区县
     * @return
     */
    @Override
    public ResultMsgBean pxAndPyByCityName(String province, String cityName, String areaName, String status, Long userId) {
        try {
            /*判断是否是特殊地区 特殊地区做特殊处理*/
            logger.info("开始根据省市区进行匹配----------------省为" + province + ",市为：" + cityName + "，区为" + areaName + "---------------------------------------------");
            //使用高德市一级为空的情况下为市赋值为区县一级
            if (StringUtils.isEmpty(cityName)) {
                cityName = areaName;
            }
            if (province != null && (province.contains("香港") || province.contains("澳门"))) {
                return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", geographicalLocationService.getCityByCityName("2", province.substring(0, 2), "1").get(0));
            }
            if (province != null && province.contains("台湾")) {
                return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", geographicalLocationService.getCityByCityName("2", "台北市", "1").get(0));
            }
            if (cityName != null && cityName.length() > 2) {
                //查询该市区下所有三级区县先查老数据
                List<City> oldCityList = geographicalLocationService.getCityByCityName("3", cityName, "1");
                logger.info("先查询老数据查询结果：" + oldCityList + "--------------------------------");
                City city = regionMatching(oldCityList, areaName);
                logger.info("老数据匹配结果：" + city + "-------------------------------------------");
                if (city != null) {
                    return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", city);
                } else {
                    //老数据没有则使用新数据
                    List<City> newCityList = geographicalLocationService.getCityByCityName("3", cityName, "2");
                    logger.info("新数据查询结果：" + newCityList);
                    City newCity = regionMatching(newCityList, areaName);
                    logger.info("新数据匹配结果：" + newCity);
                    if (newCity != null) {
                        return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", newCity);
                    }
                }
                //三级区县匹配不到
                geographicalLocationService.addAccidentRecord("1", "2", areaName + "三级区县未查询到，二级市为" + cityName + ",省为" + province, userId);

            }
            if ("2".equals(status)) {
                logger.info("采用市级匹配-------------------------------------------------");
                //找寻不到匹配的区县返回对应的二级市
                List<City> city = geographicalLocationService.getCityByCityName("2", cityName, "2");
                if (city != null && city.size() > 0) {
                    city.get(0).setAreaName(areaName);
                    return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", city.get(0));
                }
                //市和区县都
                geographicalLocationService.addAccidentRecord("1", "3", cityName + "二级市未查询到，三级区县为" + areaName + ",省为" + province, userId);
            }
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询服务器错误");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询服务器错误");
        }
    }

    /**
     * 地区匹配
     *
     * @param areaNames
     * @param areaName
     * @return
     */
    private City regionMatching(List<City> areaNames, String areaName) {
        if (areaNames != null && areaNames.size() > 0) {
            //先进性精准匹配
            for (City name : areaNames) {
                //找寻匹配旧的区县返回
                if (areaName.equals(name.getAreaName())) {
                    return name;
                }
            }
            for (City name : areaNames) {
                //找寻匹配新的区县返回
                if (areaName.equals(name.getMapAreaName())) {
                    return name;
                }
            }
        }
        return null;
    }

    @Override
    public City getCityByPxAndPy(String px, String py) {
        return geographicalLocationService.getCityByPxAndPy(px, py);
    }

    @Override
    public String checkTransportWord(String text, SourceGroupCodeEnum... codeEnumArray) {
        if (StringUtils.isBlank(text)) {
            return null;
        }

        if (ArrayUtils.isNotEmpty(codeEnumArray)) {
            List<SourceBean> labels = tytSourceService.getByGroupCodeWithNoLimit(codeEnumArray);
            if (CollectionUtils.isNotEmpty(labels)) {
                for (SourceBean oneLabel : labels) {
                    String labelName = oneLabel.getName();
                    text = text.replaceAll(labelName, "");
                }
            }
        }

        TytMachineTypeBrandNew machineTypeBrandNew = machineTypeBrandNewService.getByShowName(text);
        if (machineTypeBrandNew != null) {
            return null;
        }

        String contentKeyValue = null;

        if (StringUtils.isNotBlank(text)) {
            try {
                contentKeyValue = transportNullifyService.verifyNullifyTaskContent(text);
            } catch (Exception e) {
                logger.error("verifyNullifyTaskContent_error : ", e);
            }
        }

        return contentKeyValue;
    }

    @Override
    public ResultMsgBean getScreeningWordCheck(String type, String remark) {

        String contentKeyValue = this.checkTransportWord(remark, SourceGroupCodeEnum.remark, SourceGroupCodeEnum.tailCarStyle, SourceGroupCodeEnum.tailCarType);

        Map<String, String> map = new HashMap<>();
        map.put("type", type);
        if (StringUtils.isNotBlank(contentKeyValue)) {
            return new ResultMsgBean(ReturnCodeConstant.CONTAINS_SHIELDING_WORDS, "货物非工程机械类，无法发布哦~", map);
        }
        return new ResultMsgBean(ReturnCodeConstant.OK, "字符不含有屏蔽词", map);

    }

    /**
     * 验证手机号码是否是本人联系人
     *
     * @param publishBean
     * @return
     */

    private ResultMsgBean validationTel(TransportPublishBean publishBean, User user) {

        Long userId = publishBean.getUserId();
        /** 特运通账户ID */
        String companyAccountUserId = tytConfigService.getStringValue("tyt_company_account_user_id");

        //添加手机号检验
        String registPhone = user.getCellPhone();
        String tel = publishBean.getTel();
        if (StringUtils.isNotBlank(tel)) {
            if (!checkPhone(tel.trim(), userId, registPhone)) {
                return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "电话号码 " + tel + " 已不存在，请重新选择");
            }
        }

        String tel3 = publishBean.getTel3();
        if (StringUtils.isNotBlank(tel3)) {
            if (!checkPhone(tel3.trim(), userId, registPhone)) {
                return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "电话号码 " + tel3 + " 已不存在，请重新选择");
            }
        }
        String tel4 = publishBean.getTel4();
        if (StringUtils.isNotBlank(tel4)) {
            if (!checkPhone(tel4.trim(), userId, registPhone)) {
                return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "电话号码 " + tel4 + " 已不存在，请重新选择");
            }
        }


        return new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");
    }

    /**
     * 验证货物内容是否包含散货关键词
     *
     * @param publishBean
     * @return
     */
    private ResultMsgBean validationTaskContent(TransportPublishBean publishBean) {

        try {
            int returnCode = 0;
            String contentKeyValue = null;

            contentKeyValue = this.checkTransportWord(publishBean.getTaskContent());

            if (StringUtils.isNotBlank(contentKeyValue)) {
                returnCode = ReturnCodeConstant.NULLIFY_NOT_ALLOW_PUBLISH;
            }

            if (returnCode <= 0) {
                String remark = publishBean.getRemark();

                contentKeyValue = this.checkTransportWord(remark, SourceGroupCodeEnum.remark);

                if (StringUtils.isNotBlank(contentKeyValue)) {
                    returnCode = ReturnCodeConstant.NULLIFY_REMARK_NOT_ALLOW_PUBLISH;
                }
            }
            if (returnCode > 0) {
                logger.info("存在关键字：{}", contentKeyValue);
                //插入到记录表
                TransportNullifyBean tnb = new TransportNullifyBean();
                tnb.setTransId(0L);
                tnb.setUserId(publishBean.getUserId());
                tnb.setTaskContent(publishBean.getTaskContent());
                tnb.setTaskRemark(publishBean.getRemark());
                tnb.setMatchingKeyword(contentKeyValue);
                tnb.setUploadCellPhone(publishBean.getTel());
                tnb.setPlatId(Integer.parseInt(publishBean.getClientSign()));
                tnb.setStartPoint(publishBean.getStartPoint());
                tnb.setDestPoint(publishBean.getDestPoint());
                tnb.setStartProvinc(publishBean.getStartProvinc());
                tnb.setStartCity(publishBean.getStartCity());
                tnb.setStartArea(publishBean.getStartArea());
                tnb.setDestProvinc(publishBean.getDestProvinc());
                tnb.setDestCity(publishBean.getDestCity());
                tnb.setDestArea(publishBean.getDestArea());
                tnb.setIsInfoFee(publishBean.getIsInfoFee());
                tnb.setCtime(new Date());
                tnb.setMtime(new Date());
                tnb.setState(-1);
                //插入无效货源表
                String msg = "货物非工程机械类，无法发布哦~";
                transportNullifyService.add(tnb);

                return new ResultMsgBean(returnCode, msg);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");
    }

    /**
     * 重货判断规则已改，该方法已弃用
     *
     * @param transport
     * @return
     */
    @Deprecated
    private ResultMsgBean isNotPublishTransport(Transport transport) {

        Object hashCode = cacheService.getObject(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + transport.getHashCode());
        if (hashCode != null) {
            return new ResultMsgBean(ReturnCodeConstant.DATA_HAS_EXIT, "您已经发布过此条信息！");
        }
        return new ResultMsgBean(ReturnCodeConstant.OK, "验证成功");

    }

    private boolean checkPhone(String tel, Long userId, String registPhone) {
        boolean isPhone = true;
        if (StringUtils.isNotBlank(tel)) {
            if (tel.equals(registPhone)) {
                return isPhone;
            } else {
                isPhone = userTelService.get(userId, tel);
                return isPhone;
            }
        } else {
            return false;
        }
    }

    /**
     * 判断是否是新版本
     *
     * @param clientSign
     * @param clientVersion
     * @return
     */
    private boolean isNewAddressVersion(String clientSign, String clientVersion) {
        boolean newVersion = false;

        int clientSignInt = 0;
        int clientVersionInt = 0;

        if (StringUtils.isNotBlank(clientSign) && StringUtils.isNumeric(clientSign)) {
            clientSignInt = Integer.parseInt(clientSign);
        }
        if (StringUtils.isNotBlank(clientVersion) && StringUtils.isNumeric(clientVersion)) {
            clientVersionInt = Integer.parseInt(clientVersion);
        }

        if (Constant.ClientSignEnum.isNewPc(clientSignInt)) {
            if (clientVersionInt >= 546050) {
                newVersion = true;
            }
        } else {
            if (clientVersionInt >= 6300) {
                newVersion = true;
            }
        }
        return newVersion;
    }

    /**
     * 校验并补全地址信息新
     *
     * @param newVersion
     * @param adcode
     * @param province
     * @param cityName
     * @param areaName
     * @param px
     * @param py
     * @param userId
     */
    private TytCity getPublishMatchCity(boolean newVersion, String adcode, String province, String cityName, String areaName, BigDecimal px, BigDecimal py, Long userId) {

        if (StringUtils.isBlank(cityName)) {
            cityName = areaName;
        }

        TytCity matchCity = cityDataService.getMatchCity(adcode, province, cityName, areaName, px, py, userId);

        if (matchCity != null) {
            return matchCity;
        }

        newVersion = false;
        //暂时先保留旧模式支持，如果没有用户反馈，该逻辑可以移除
        if (!newVersion) {
            ResultMsgBean resultMsgBean = this.parameterAssignment(province, cityName, areaName, px, py, userId);
            if (resultMsgBean.getCode().equals(200) && resultMsgBean.getData() != null) {
                City oldCity = (City) resultMsgBean.getData();
                matchCity = ConvertUtil.beanConvert(oldCity, new TytCity());
                matchCity.setProvinceName(oldCity.getProvince());
            }
        }

        return matchCity;
    }


    private void completionStartAndDestParameter(TransportPublishBean publishBean) {
        String clientVersion = publishBean.getClientVersion();
        String clientSign = publishBean.getClientSign();

        boolean newVersion = this.isNewAddressVersion(clientSign, clientVersion);

        logger.info("开始匹配出发地++++++++++++++++++++++++++++++++++++++++++++++");
        TytCity startCity = this.getPublishMatchCity(newVersion, publishBean.getStartAdcode(), publishBean.getStartProvinc(), publishBean.getStartCity(), publishBean.getStartArea(), publishBean.getStartCoordX(), publishBean.getStartCoordY(), publishBean.getUserId());

        if (startCity != null) {

            publishBean.setStartCoordX(new BigDecimal(startCity.getPx()));
            publishBean.setStartCoordY(new BigDecimal(startCity.getPy()));
            publishBean.setStartProvinc(startCity.getProvinceName());
            publishBean.setStartCity(startCity.getCityName());
            publishBean.setStartArea(startCity.getAreaName());
            publishBean.setStartPoint(updateStartAndDestPoint(startCity.getProvinceName(), startCity.getCityName(), startCity.getAreaName()));
            if (StringUtils.isBlank(publishBean.getStartDetailAdd())) {
                try {
                    checkLogitudeLatitude(publishBean.getStartLongitude(), publishBean.getStartLatitude(), new BigDecimal(startCity.getLongitude()), new BigDecimal(startCity.getLatitude()));
                } catch (RuntimeException e) {
//                    logger.info("AreaLO&LAException 出发地地区经纬度不匹配 用户id:{}；货物名称：{}； 出发地：{}，传入经纬度：{},{}；实际经纬度：{},{}；",publishBean.getUserId(),publishBean.getTaskContent(),publishBean.getStartProvinc()+publishBean.getStartCity()+publishBean.getStartArea(),publishBean.getStartLongitude(),publishBean.getStartLatitude(),city.getLongitude(),city.getLatitude());
                    publishBean.setStartLongitude(new BigDecimal(startCity.getLongitude()));
                    publishBean.setStartLatitude(new BigDecimal(startCity.getLatitude()));
                }
            }
            logger.info("消息发布出发地的补全参数，省市区县:{}, x轴:{}：", "区县" + publishBean.getStartArea() + "，省" + publishBean.getStartProvinc() + "，市" + publishBean.getStartCity(), "x:" + publishBean.getStartCoordX() + "y:" + publishBean.getStartCoordY());
        }

        logger.info("开始匹配目的地++++++++++++++++++++++++++++++++++++++++++++++");
        TytCity destCity = this.getPublishMatchCity(newVersion, publishBean.getDestAdcode(), publishBean.getDestProvinc(), publishBean.getDestCity(), publishBean.getDestArea(), publishBean.getDestCoordX(), publishBean.getDestCoordY(), publishBean.getUserId());
        if (destCity != null) {

            publishBean.setDestCoordX(new BigDecimal(destCity.getPx()));
            publishBean.setDestCoordY(new BigDecimal(destCity.getPy()));
            publishBean.setDestProvinc(destCity.getProvinceName());
            publishBean.setDestCity(destCity.getCityName());
            publishBean.setDestArea(destCity.getAreaName());
            publishBean.setDestPoint(updateStartAndDestPoint(destCity.getProvinceName(), destCity.getCityName(), destCity.getAreaName()));
            if (StringUtils.isBlank(publishBean.getDestDetailAdd())) {
                try {
                    checkLogitudeLatitude(publishBean.getDestLongitude(), publishBean.getDestLatitude(), new BigDecimal(destCity.getLongitude()), new BigDecimal(destCity.getLatitude()));
                } catch (RuntimeException e) {
//                    logger.info("AreaLO&LAException 目的地地区经纬度不匹配 用户id:{}；货物名称：{}；目的地：{}，传入经纬度：{},{}；实际经纬度：{},{}；",publishBean.getUserId(),publishBean.getTaskContent(),publishBean.getDestProvinc()+publishBean.getDestCity()+publishBean.getDestArea(),publishBean.getDestLongitude(),publishBean.getDestLatitude(),city.getLongitude(),city.getLatitude());
                    publishBean.setDestLongitude(new BigDecimal(destCity.getLongitude()));
                    publishBean.setDestLatitude(new BigDecimal(destCity.getLatitude()));
                }
            }
            logger.info("消息发布到达地址的补全参数，省市区县:{}, x轴:{}：", "区县" + publishBean.getDestArea() + "，省" + publishBean.getDestProvinc() + "，市" + publishBean.getDestCity(), "x:" + publishBean.getDestCoordX() + "y:" + publishBean.getDestCoordY());
        }

    }

    private void checkLogitudeLatitude(BigDecimal publishLogitude, BigDecimal publishLatitude, BigDecimal cityLogitude, BigDecimal cityLatitude) throws RuntimeException {
        if (publishLatitude.compareTo(cityLatitude) == 0 && publishLogitude.compareTo(cityLogitude) == 0) {
            return;
        } else {
            throw new RuntimeException("地区经纬度不匹配");
        }
    }


    private ResultMsgBean parameterAssignment(String province, String cityName, String areaName, BigDecimal px, BigDecimal py, Long userId) {
        //省市区和xy都有的话先试用省市区进行匹配
        if (cityName != null && areaName != null && px != null && py != null && px.compareTo(BigDecimal.ZERO) != 0 && py.compareTo(BigDecimal.ZERO) != 0) {
            logger.info("开始地址坐标都不为空++++++++++++++++++++++++++++++++++++++++++++++");
            ResultMsgBean resultMsgBean = bsPublishTransportService.pxAndPyByCityName(province, cityName, areaName, "1", userId);
            if (resultMsgBean.getCode().equals(ReturnCodeConstant.OK)) {
                return resultMsgBean;
            } else {
                //根据省市区查询不到再根据xy查询
                City cityByPxAndPy = bsPublishTransportService.getCityByPxAndPy(px.stripTrailingZeros().toPlainString(), py.stripTrailingZeros().toPlainString());
                if (cityByPxAndPy != null) {
                    return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", cityByPxAndPy);
                }
                return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
            }
            //省市区没有使用xy
        } else if (cityName == null && areaName == null && px != null && py != null && px.compareTo(BigDecimal.ZERO) != 0 && py.compareTo(BigDecimal.ZERO) != 0) {
            logger.info("开始地址为空++++++++++++++++++++++++++++++++++++++++++++++");
            City cityByPxAndPy = bsPublishTransportService.getCityByPxAndPy(px.stripTrailingZeros().toPlainString(), py.stripTrailingZeros().toPlainString());
            if (cityByPxAndPy != null) {
                return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", cityByPxAndPy);
            }
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
            //没有坐标的使用省市区
        } else if (cityName != null && areaName != null && (px == null || py == null || px.compareTo(BigDecimal.ZERO) == 0 || py.compareTo(BigDecimal.ZERO) == 0)) {
            logger.info("开始坐标为空++++++++++++++++++++++++++++++++++++++++++++++");
            return bsPublishTransportService.pxAndPyByCityName(province, cityName, areaName, "2", userId);
        }
        return new ResultMsgBean(ReturnCodeConstant.ERROR, "查询失败");
    }

    /**
     * 判断发货手机号是不是座机或者手机号
     *
     * @param tel
     */
    private boolean isMobileOrFixedPhone(String tel) {
        if (MobileUtil.isFixedPhone(tel) || MobileUtil.isMobile(tel)) {
            return false;
        } else {
            return true;
        }
    }


    /**
     * 拼接出发地和到达地
     *
     * @param province 省
     * @param city     市
     * @param area     区县
     * @return
     */
    private String updateStartAndDestPoint(String province, String city, String area) {
        if (org.apache.commons.lang.StringUtils.isNotBlank(city) && org.apache.commons.lang.StringUtils.isNotBlank(area)) {
            if (city.contains(province)) {
                if (area.contains(city)) {
                    return area;
                } else {
                    return city + area;
                }
            } else {
                if (area.contains(city)) {
                    return province + area;
                } else {
                    return province + city + area;
                }
            }
        } else {
            if (org.apache.commons.lang.StringUtils.isNotBlank(city)) {
                if (city.contains(province)) {
                    return city;
                } else {
                    return province + city;
                }
            } else if (org.apache.commons.lang.StringUtils.isNotBlank(area)) {
                if (area.contains(province)) {
                    return area;
                } else {
                    return province + area;
                }
            } else {
                return province;
            }
        }
    }


    /**
     * 创建Transport对象
     *
     * @param publishBean
     * @return
     * @throws Exception
     */
    private Transport createTransport(TransportPublishBean publishBean, User user) throws Exception {
        Transport transport = new Transport();
        transport.setIsDelete((short) 0);
        if (StringUtils.isNotBlank(user.getTrueName()) && StringUtils.isNotBlank(user.getIdCard())) {
            transport.setUserShowName(user.getTrueName().substring(0, 1) + IdCardUtil.getCallGender(user.getIdCard()));
        } else {
            transport.setUserShowName(user.getUserName());
        }


        transport.setUserId(publishBean.getUserId());
        transport.setPlatId(Integer.parseInt(publishBean.getClientSign()));
        transport.setStartCoord(publishBean.getStartCoordX() + "," + publishBean.getStartCoordY());
        transport.setDestCoord(publishBean.getDestCoordX() + "," + publishBean.getDestCoordY());
        transport.setDestPoint(publishBean.getDestPoint());
        transport.setStartPoint(publishBean.getStartPoint());
        String taskContent = publishBean.getTaskContent();
        transport.setTaskContent(StringUtil.filterTyt(taskContent));
        transport.setTel(publishBean.getTel());
        transport.setTel3(org.springframework.util.StringUtils.isEmpty(publishBean.getTel3()) ? null : publishBean.getTel3());
        transport.setTel4(org.springframework.util.StringUtils.isEmpty(publishBean.getTel4()) ? null : publishBean.getTel4());
        transport.setPrice(publishBean.getPrice());
        transport.setStartLatitudeValue(publishBean.getStartLatitude().movePointRight(6).intValue());
        transport.setStartLongitudeValue(publishBean.getStartLongitude().movePointRight(6).intValue());
        transport.setDestLatitudeValue(publishBean.getDestLatitude().movePointRight(6).intValue());
        transport.setDestLongitudeValue(publishBean.getDestLongitude().movePointRight(6).intValue());
        transport.setStartCoordXValue(publishBean.getStartCoordX().movePointRight(2).intValue());
        transport.setStartCoordYValue(publishBean.getStartCoordY().movePointRight(2).intValue());
        transport.setDestCoordXValue(publishBean.getDestCoordX().movePointRight(2).intValue());
        transport.setDestCoordYValue(publishBean.getDestCoordY().movePointRight(2).intValue());
        transport.setStartArea(publishBean.getStartArea());
        transport.setStartCity(publishBean.getStartCity());
        transport.setStartProvinc(publishBean.getStartProvinc());
        transport.setDestArea(publishBean.getDestArea());
        transport.setDestCity(publishBean.getDestCity());
        transport.setDestProvinc(publishBean.getDestProvinc());
        transport.setDestProvinc(publishBean.getDestProvinc());
        //车货拆分新增参数---------------------------------
        transport.setBeginLoadingTime(publishBean.getBeginLoadingTime()); //新增字段
        transport.setBeginUnloadTime(publishBean.getBeginUnloadTime()); //新增字段
        transport.setLoadingTime(publishBean.getLoadingTime());
        transport.setUnloadTime(publishBean.getUnloadTime());
        transport.setCarMinLength(publishBean.getCarMinLength());
        transport.setCarMaxLength(publishBean.getCarMaxLength());
        transport.setWorkPlaneMinHigh(publishBean.getWorkPlaneMinHigh());
        transport.setWorkPlaneMaxHigh(publishBean.getWorkPlaneMaxHigh());
        transport.setWorkPlaneMinLength(publishBean.getWorkPlaneMinLength());
        transport.setWorkPlaneMaxLength(publishBean.getWorkPlaneMaxLength());
        transport.setClimb(publishBean.getClimb());
        transport.setCarStyle(publishBean.getCarStyle());
        transport.setExclusiveType(publishBean.getExclusiveType());

        // 添加优车标识
        Integer excellentGoods = publishBean.getExcellentGoods() == null ? 0 : publishBean.getExcellentGoods();
        transport.setExcellentGoods(excellentGoods);
        transport.setPublishGoodsType(publishBean.getPublishGoodsType());

        if (Objects.nonNull(publishBean.getDriverDriving())) {
            transport.setDriverDriving(publishBean.getDriverDriving());
        }
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
            transport.setLoadCellPhone(publishBean.getLoadCellPhone());
            transport.setUnloadCellPhone(publishBean.getUnloadCellPhone());
            // 签约合作商字段赋值处理
            if (Objects.nonNull(publishBean.getCargoOwnerId()) && publishBean.getCargoOwnerId() != 0) {
                transport.setCargoOwnerId(publishBean.getCargoOwnerId());
            } else {
                DispatchCargoOwner cargoOwner = dispatchCargoOwnerMapper.selectSignedByUserId(publishBean.getUserId());
                if (Objects.nonNull(cargoOwner)) {
                    transport.setCargoOwnerId(cargoOwner.getCooperativeId());
                }
            }
        }

        if (publishBean.getIsShow() != null && publishBean.getIsShow() == 0) {
            transport.setIsShow(0);
        } else {
            transport.setIsShow(1);
        }
        try {
            if (publishBean.getDistance() != null) {
                int distance = publishBean.getDistance().movePointRight(2).intValue();
                transport.setDistanceValue(distance);

                Integer clientSign = Integer.parseInt(publishBean.getClientSign());
                if (Objects.equals(clientSign, Constant.ClientSignEnum.ANDROID_CAR.code) || Objects.equals(clientSign, Constant.ClientSignEnum.ANDROID_GOODS.code)) {
                    transport.setAndroidDistance(distance);
                }
                if (Objects.equals(clientSign, Constant.ClientSignEnum.IOS_CAR.code) || Objects.equals(clientSign, Constant.ClientSignEnum.IOS_GOODS.code)) {
                    transport.setIosDistance(distance);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //TODO 过滤货物内容的电话号码
        String taskContentTemp = transportMainService.replaceTaskContentByPhone(taskContent);
        transport.setTaskContent(taskContentTemp.replaceAll("'", "-").replaceAll("\"", "--"));
        transport.setSource(Transport.SOURCE_MANUAL);
        transport.setPcOldContent(transport.getStartPoint().trim() + "---" + transport.getDestPoint().trim() + " " + transport.getTaskContent().trim());

        if (user != null && transport.getSource() == Transport.SOURCE_MANUAL) {
            transport.setVerifyFlag(user.getVerifyFlag().intValue() == 2 ? 0 : user.getVerifyFlag());
        } else {
            transport.setVerifyFlag(0);
        }
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        // transport.setPubTime(params.get("pubTime"));
        transport.setPubTime(TimeUtil.formatTime(timestamp));
        // transport.setPubDate(TimeUtil.parseDateString(params.get("pubDate")));
        transport.setPubDate(timestamp);
        transport.setCtime(timestamp);
        transport.setMtime(timestamp);
        if (StringUtils.isNotEmpty(publishBean.getStartDetailAdd())) {
            transport.setStartDetailAdd(publishBean.getStartDetailAdd());
        }
        if (StringUtils.isNotEmpty(publishBean.getDestDetailAdd())) {
            transport.setDestDetailAdd(publishBean.getDestDetailAdd());
        }
        transport.setWeight(StringUtils.defaultIfEmpty(publishBean.getWeight(), null));
        transport.setLength(StringUtils.defaultIfEmpty(publishBean.getLength(), null));
        transport.setWide(StringUtils.defaultIfEmpty(publishBean.getWide(), null));
        transport.setHigh(StringUtils.defaultIfEmpty(publishBean.getHigh(), null));
        transport.setIsSuperelevation(publishBean.getIsSuperelevation());

        if (StringUtils.isNotEmpty(publishBean.getRemark())) {
            //过滤货物内容的电话号码
            String remarkTemp = transportMainService.replaceTaskContentByPhone(publishBean.getRemark());
            transport.setRemark(remarkTemp);
        }
        if (StringUtils.isNotEmpty(publishBean.getMachineRemark())) {
            //过滤货物内容的电话号码
            String machineRemarkTemp = transportMainService.replaceTaskContentByPhone(publishBean.getMachineRemark());
            transport.setMachineRemark(machineRemarkTemp);
        }
        /* 兼容老客户 */
        //如果是优车并且该货主不在AB测试中才将昵称赋值为X老板
        transportBusiness.makeNickName(transport, user);


        transport.setLinkman(StringUtil.formatUserName(publishBean.getLinkMan(), String.valueOf(user.getId())));

        transport.setPubQQ(user.getQq());
        transport.setUploadCellPhone(user.getCellPhone());
        /* hashcode+display */
        transport.setDisplayType("1");// 默认显示
        /* 添加用户信息 */
        transport.setIsCar(user.getIsCar());
        // transport.setUserType(user.getUserType());
        int vipType = 0;
        if (user != null && user.getUserType() == User.USER_TYPE_VIP && user.getServeDays() > 0) {
            vipType = 1;
        }


        transport.setUserType(vipType);
        /* 重发周期默认值在tyt_config表配置 */
        Integer serverDefaultResendTime = tytConfigService.getIntValue("defaultResendTime");
        transport.setResend(serverDefaultResendTime == null ? 0 : serverDefaultResendTime);
        transport.setStatus(Transport.STATUS_ENABLE);

        if (user != null && transport.getSource() == Transport.SOURCE_MANUAL) {
            transport.setVerifyPhotoSign(user.getVerifyPhotoSign().longValue() > 1 ? 0 : user.getVerifyPhotoSign());
        } else {
            transport.setVerifyPhotoSign(0);
        }
        transport.setUserPart(user.getUserPart());
        transport.setClientVersion(publishBean.getClientVersion());
        String isInfoFee = "1";
        transport.setIsInfoFee(isInfoFee);
        transport.setInfoStatus("0");// 信息费运单状态：0待接单 1有人支付成功 （货主的待同意
        // ）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成 5异常上报
        transport.setReleaseTime(timestamp);
        /* 已撤销，已过期进行直接发布，编辑再发布中,运单号重新生成 */
        transport.setTsOrderNo(tytSequenceService.updateGetNumberForDate(Constant.TABLE_WAYBILL_NAME));// 需要从接口获取

        transport.setRegTime(user.getCtime());

        transport.setType(publishBean.getType());
        transport.setBrand(publishBean.getBrand());
        transport.setGoodTypeName(publishBean.getGoodTypeName());
        transport.setGoodNumber(publishBean.getMachineNumber());

        transport.setIsStandard(publishBean.getIsStandard() == null ? 1 : publishBean.getIsStandard());
        transport.setMatchItemId(publishBean.getMatchItemId() == null ? -1 : publishBean.getMatchItemId());

        /* PC 重发字段:默认值在tyt_config表控制  走默认值*/
        String resendStr = publishBean.getResend();
        if (org.springframework.util.StringUtils.hasLength(resendStr) && isNumeric(resendStr)) {
            SimplePageGradeBean gradeBean = tytPageGradeService.getByType("2", resendStr);
            if (gradeBean == null) {
                Integer serverDefaultResendTime2 = tytConfigService.getIntValue("defaultResendTime");
                transport.setResend(serverDefaultResendTime2 == null ? 0 : serverDefaultResendTime2);
            } else {
                transport.setResend(gradeBean.getValueBeginRange());
            }

        } else {
            Integer serverDefaultResendTime2 = tytConfigService.getIntValue("defaultResendTime");
            transport.setResend(serverDefaultResendTime2 == null ? 0 : serverDefaultResendTime2);
        }
        // 5920 新增3个字段
        transport.setCarType(publishBean.getCarType());
        transport.setCarLength(publishBean.getCarLength());
        transport.setSpecialRequired(publishBean.getSpecialRequired());
        //v6110新增货源标签字段
        transport.setCarLengthLabels(publishBean.getCarLengthLabels());
        String tyreExposedFlag = publishBean.getTyreExposedFlag();
        if (StringUtils.isBlank(tyreExposedFlag)) {
            tyreExposedFlag = "0";
        }
        transport.setTyreExposedFlag(tyreExposedFlag);
        //v6120新增调车数量
        transport.setShuntingQuantity(publishBean.getShuntingQuantity());
        //v6140 货源类型（电议1，一口价2） 信息费
        transport.setPublishType(Optional.ofNullable(publishBean.getPublishType()).orElse(Constant.PUBLISH_TUPE1));
        transport.setInfoFee(publishBean.getInfoFee());

        transport.setRefundFlag(publishBean.getRefundFlag());
        // 根据导入的标签来判断是否是个人货主
        CsMaintainedCustom csMaintainedCustom = csMaintainedCustomService.getCsMaintainedCustomByUserId(user.getId());
        // 如果是个人货主
        if (publishBean.getSourceType() != null && publishBean.getSourceType().equals(SourceTypeEnum.运满满货源.getId())) {
            transport.setSourceType(SourceTypeEnum.运满满货源.getId());
        } else if (csMaintainedCustom != null && Objects.equals(1, csMaintainedCustom.getGoodsOwner())) {
            transport.setSourceType(SourceTypeEnum.个人货主.getId());
        } else {
            transport.setSourceType(SourceTypeEnum.普通货主.getId());
        }
        // 专车发货，如果是代调发货：签约合作商选择宏信建发，source_type赋值为宏信建发，否则赋值为代调发货
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
            Integer clientVersion = null;
            try {
                clientVersion = Integer.valueOf(publishBean.getClientVersion());
            } catch (Exception e) {
                log.error("客户端版本转换异常：{}", publishBean.getClientVersion());
            }
            if (Objects.nonNull(clientVersion) && clientVersion < 6480) {
                // 专车一期6470版本，source_type都为宏信
                transport.setSourceType(SourceTypeEnum.宏信货源.getId());
            } else {
                int count = tytDispatchCompanyMapper.countByUserId(publishBean.getUserId());
                if (count > 0) {
                    // 代调发货判断
                    SourceTypeEnum sourceTypeEnum = SourceTypeEnum.代调发货;
                    TytDispatchCooperative cooperative = tytDispatchCooperativeMapper.selectByName(HONG_XIN_JIAN_FA);
                    if (Objects.nonNull(cooperative) && cooperative.getId().equals(publishBean.getCargoOwnerId())) {
                        sourceTypeEnum = SourceTypeEnum.宏信货源;
                    }
                    transport.setSourceType(sourceTypeEnum.getId());
                }
            }
        }
        // 6290加入是否是保障货源标识
        transport.setGuaranteeGoods(publishBean.getGuaranteeGoods());
        //todo 是否有信用曝光权限

        //默认值
        transport.setSort(0L);
        transport.setSortType(TsSortTypeEnum.top.getCode());

        transport.setTecServiceFee(publishBean.getTecServiceFee());
        transport.setExcellentCardId(publishBean.getExcellentCardId());

        //如果是开票货源则构造企业税率、附加运费和总运费
        if (publishBean.getInvoiceTransport() != null && publishBean.getInvoiceTransport() == 1) {
            transport.setInvoiceTransport(1);
            ResultMsgBean additionalPriceAndEnterpriseTaxRate = invoiceTransportService.getAdditionalPriceAndEnterpriseTaxRate(publishBean.getUserId(), publishBean.getPrice(), publishBean.getInvoiceSubjectId());
            if (additionalPriceAndEnterpriseTaxRate.getCode() == 200) {
                HashMap<String, String> additionalPriceAndEnterpriseTaxRateResult = (HashMap<String, String>) additionalPriceAndEnterpriseTaxRate.getData();
                transport.setEnterpriseTaxRate(new BigDecimal(additionalPriceAndEnterpriseTaxRateResult.get("enterpriseTaxRate")));
                transport.setAdditionalPrice(additionalPriceAndEnterpriseTaxRateResult.get("additionalPrice"));
            }
        } else {
            transport.setInvoiceTransport(0);
        }

        return transport;
    }

    // 拼接运费信息
    private TransportSubBean createTransportSub(TransportPublishBean publishBean) throws Exception {
        TransportSubBean ts = new TransportSubBean();
        // 货主输入的运价
        String price = publishBean.getPrice();
        // 固定成本
        if (org.springframework.util.StringUtils.hasLength(publishBean.getBasePrice())) {
            ts.setFixedCost(Double.valueOf(publishBean.getBasePrice()));
            ts.setBasePrice(ts.getFixedCost());
        }
        // 最低利润率
        if (org.springframework.util.StringUtils.hasLength(publishBean.getProfixRate())) {
            ts.setMinProfitRate(Double.valueOf(publishBean.getProfixRate()));
        }

        try {
            // 计算成交底价 = 固定成本+（固定成本*最低利润率）
            if (org.springframework.util.StringUtils.hasLength(publishBean.getBasePrice())) {
                // if(StringUtils.hasLength(params.get("fixedCost")) &&
                // StringUtils.hasLength(params.get("minProfitRate"))){
                // Double minProfitRate =
                // HighwayCostCalcUtil.calcDivide(params.get("minProfitRate"),"100.0");
                // Double minProfitRate =
                // Double.valueOf(params.get("minProfitRate"));
                // Double basePrice =
                // HighwayCostCalcUtil.calcMultiply(params.get("fixedCost"),Double.toString(minProfitRate));
                // basePrice
                // =HighwayCostCalcUtil.calcInCrease(params.get("fixedCost"),Double.toString(basePrice));
                // 成交底价
                // if(basePrice != null && basePrice >= 0){
                // ts.setBasePrice(basePrice);
                // }
                // 计算成交利润率 = （货主输入运价 - 成交底价 ）/ 货主输入运价
                if (org.springframework.util.StringUtils.hasLength(price) && Double.valueOf(price) > 0 && ts.getFixedCost() > 0) {
                    // 利润金额
                    Double profitPrice = HighwayCostCalcUtil.calcSubtract(price, Double.toString(ts.getFixedCost()));
                    // 如果利润等于0，则利润率直接为0
                    if (profitPrice == null || profitPrice.intValue() == 0) {
                        ts.setProfitRate(0d);
                        return ts;
                    }
                    // 成交利润率
                    Double profitRate = HighwayCostCalcUtil.calcDivide(Double.toString(profitPrice), price);
                    if (profitRate != null) {
                        profitRate = HighwayCostCalcUtil.getNumberFourPrecision(profitRate);// 保留四位小数
                        profitRate = HighwayCostCalcUtil.calcMultiply(Double.toString(profitRate), "100");
                        ts.setProfitRate(profitRate);
                    }
                }
            }
        } catch (Exception e) {
            logger.info("[ERROR]运费SUB子表存储失败USER_ID:【{}】,出发地：【{}】，目的地：【{}】，货物：【{}】", publishBean.getUserId(), publishBean.getStartPoint(), publishBean.getDestPoint(), publishBean.getTaskContent());
        }
        return ts;
    }


    /**
     * 获得货物的hashcode(新规则),标准货源的判重标准：用户id+出发地+目的地+标准货物编号（只针对发布中的货源进行判重）
     * 非标准货源的判重标准：用户id+出发地+目的地+货物内容（只针对发布中的货源进行判重， 暂时不考虑相似度问题）
     *
     * @param transport
     * @return
     */
    @Override
    public String getNewHashCode(Transport transport) {

        Date nowDate = new Date();
        String nowDay = DateUtil.dateToString(nowDate, DateUtil.day_format);

        Long userId = transport.getUserId();

        //判断唯一时，市县就可以确认。
        String startCity = transport.getStartCity();
        String startArea = transport.getStartArea();

        String destCity = transport.getDestCity();
        String destArea = transport.getDestArea();

        // 重量统一改成保留2位小数
        String weight = StringUtils.isBlank(transport.getWeight()) ? "" :
                new BigDecimal(transport.getWeight()).setScale(2, RoundingMode.HALF_UP).toString();


        List<String> textList = new ArrayList<>();

        textList.add(nowDay);
        textList.add(userId + "");
        textList.add(startCity);
        textList.add(startArea);
        textList.add(destCity);
        textList.add(destArea);
        textList.add(weight);

        String type = transport.getType();
        String brand = transport.getBrand();
        String goodTypeName = transport.getGoodTypeName();

        if (StringUtils.isBlank(type) && StringUtils.isBlank(brand) && StringUtils.isBlank(goodTypeName)) {
            //都为空，取前10
            String taskContent = transport.getTaskContent();
            if (taskContent.length() > 10) {
                taskContent = taskContent.substring(0, 10);
            }
            textList.add(taskContent);
        } else {
            textList.add(type);
            textList.add(brand);
            textList.add(goodTypeName);
        }

        String hashCodeText = StringUtils.join(textList, "|");
        String md5HashCode = MD5Util.GetMD5Code(hashCodeText);
//        String md5HashCode = Encoder.md5(hashCodeText);

        if (md5HashCode.length() > 32) {
            throw TytException.createException(ResponseEnum.sys_error.info("hashCode error !"));
        }

        return md5HashCode;
    }

    private String getNewHashCodeBak(Transport transport) {

        String code = "";
        //如果IsStandard==0 为标准化货源   1为非标货源
        //标准货源判断方式已废弃
        /*
        if (transport.getIsStandard() == 0) {
            code = transport.getUserId() + transport.getStartPoint() + transport.getDestPoint() +
                    transport.getMatchItemId();
        } else {
            code = transport.getUserId() + transport.getStartPoint() + transport.getDestPoint() +
                    StringUtil.filterBlank(StringUtil.filterPunctuation(StringUtil.filterEmotion(transport.getTaskContent())));
        }
        */
        code = transport.getUserId() + transport.getStartPoint() + transport.getDestPoint() + StringUtil.filterBlank(StringUtil.filterPunctuation(StringUtil.filterEmotion(transport.getTaskContent())));
        return code.hashCode() + "";
    }

    /**
     * 当前用户是否展示专车发货入口
     * 专车业务开关：开-调度APP账号展示，关-全部APP账号展示
     *
     * @param userId
     * @return
     */
    @Override
    public ResultMsgBean isShowSpecial(Long userId) {
        JSONObject json = new JSONObject();
        boolean show = verifyShowSpecial(userId);
        json.put("show", show);
        return ResultMsgBean.successResponse(json);
    }

    private boolean verifyShowSpecial(Long userId) {
        Integer configValue = tytConfigService.getIntValue(Constant.ZHUANCHE_PUBLISH_OPEN_CONTROL_CONFIG_KEY, Constant.ZHUANCHE_PUBLISH_OPEN_CONTROL_CONFIG_OPEN);
        if (Constant.ZHUANCHE_PUBLISH_OPEN_CONTROL_CONFIG_OPEN.equals(configValue)) {
            int count = tytDispatchCompanyMapper.countByUserId(userId);
            if (count == 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * since 6480
     * 是否展示专车入口：专车货主管理中的用户可以展示专车入口
     * 6530：所有用户都展示专车发货入口
     * 6600: 代调展示专车入口，非代调如果路线匹配也展示专车入口(通过运费测算接口可以测算出运费就展示专车入口，APP控制)
     *
     * @param userId
     * @return
     */
    @Override
    public ResultMsgBean isShowSpecialV2(Long userId) {
        JSONObject json = new JSONObject();
        // boolean show = false;
        //
        // DispatchCargoOwner cargoOwner = dispatchCargoOwnerMapper.selectByUserId(userId);
        // if (Objects.nonNull(cargoOwner)) {
        //     show = true;
        // }
        // if (!show) {
        //     int count = tytDispatchCompanyMapper.countByUserId(userId);
        //     if (count > 0) {
        //         show = true;
        //     }
        // }
        int count = tytDispatchCompanyMapper.countByUserId(userId);
        json.put("show", count > 0);
        return ResultMsgBean.successResponse(json);
    }

    /**
     * 6480新增
     * 是否展示签约合作商：调度账号展示，非调度账号隐藏
     *
     * @param queryBean
     * @return
     */
    @Override
    public ResultMsgBean isShowCargoOwner(CargoOwnerQueryBean queryBean) {
        JSONObject json = new JSONObject();
        boolean show = false;

        int count = tytDispatchCompanyMapper.countByUserId(queryBean.getUserId());
        if (count > 0) {
            show = true;
            List<CargoOwnerInfoVo> owners = tytDispatchCooperativeMapper.selectListByName(queryBean.getCargoOwnerName());
            json.put("list", owners);
        }

        json.put("show", show);
        return ResultMsgBean.successResponse(json);
    }

    /**
     * 6480新增
     * 是否展示司机驾驶此类货物
     * ①根据【运费所属企业】【出发地、目的地】【货物吨位】匹配运费计算规则
     * ②运费计算规则中配置了【驾驶货物费】，并且发货的货物类型名称（good_type_name）在驾驶能力枚举列表中
     *
     * @param queryBean
     * @return
     */
    @Override
    public ResultMsgBean isShowDriverDriving(DriverDrivingQueryBean queryBean) {
        if (Objects.isNull(queryBean.getUserId()) || StringUtils.isEmpty(queryBean.getStartCity()) || StringUtils.isEmpty(queryBean.getDestCity()) || StringUtils.isEmpty(queryBean.getWeight()) || StringUtils.isEmpty(queryBean.getGoodTypeName())) {
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info("请求参数错误，缺少必要参数"));
        }

        JSONObject json = new JSONObject();
        boolean show = false;
        boolean showCarUseType = false;

        Long cargoOwnerId = null;
        if (Objects.nonNull(queryBean.getCargoOwnerId()) && queryBean.getCargoOwnerId() != 0) {
            TytDispatchCooperative cooperative = tytDispatchCooperativeMapper.selectByPrimaryKey(queryBean.getCargoOwnerId());
            if (Objects.nonNull(cooperative) && cooperative.getStatus() == 1) {
                cargoOwnerId = cooperative.getId();
            }
        } else {
            // 发货账号是否在专车货主管理中，如果是签约合作商取合作商，如果是非签约合作商，取平台
            DispatchCargoOwner owner = dispatchCargoOwnerMapper.selectSignedByUserId(queryBean.getUserId());
            if (Objects.isNull(owner)) {
                TytDispatchCooperative cooperative = tytDispatchCooperativeMapper.selectByName(PLAT_CARGO_OWNER_NAME);
                if (Objects.nonNull(cooperative)) {
                    cargoOwnerId = cooperative.getId();
                }
            } else {
                cargoOwnerId = owner.getCooperativeId();
            }
        }
        if (Objects.isNull(cargoOwnerId)) {
            logger.warn("isShowDriverDriving代调发货获取是否展示司机驾驶此类货物，获取签约合作商失败，参数:{}", JSONObject.toJSONString(queryBean));
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info("未获取到签约合作商信息"));
        }

        BigDecimal weight = new BigDecimal(queryBean.getWeight());
        // 获取运费配置
        TytSpecialCarPriceConfig priceConfig = tytSpecialCarPriceConfigMapper.selectMatchPriceConfig(cargoOwnerId, queryBean.getStartCity(), queryBean.getDestCity(), weight);

        if (DrivingAbilityEnum.haveAbility(queryBean.getGoodTypeName())) {
            if (Objects.nonNull(priceConfig) && priceConfig.getDrivingFee().compareTo(new BigDecimal("0")) > 0) {
                show = true;
            }
        }
        // 是否展示用车类型
        // if (Objects.nonNull(priceConfig) && StringUtils.isNotEmpty(priceConfig.getLessPriceRule())) {
        //     String tonnage = tytConfigService.getStringValue(Constant.SPECIAL_CAR_PRICE_CONFIG_TONNAGE, "28");
        //     if (weight.compareTo(new BigDecimal(tonnage)) <= 0) {
        //         showCarUseType = true;
        //     }
        // }
        // 代调账号展示用车类型，普通账号不展示用车类型
        int count = tytDispatchCompanyMapper.countByUserId(queryBean.getUserId());
        if (count > 0) {
            showCarUseType = true;
        }

        json.put("show", show);
        json.put("showCarUseType", showCarUseType);

        //当用户是非代调账号，且货主身份（签约合作商）为“平台”、空、不在专车货主管理中时隐藏
        json.put("hideSpecialCarRule", false);
        if (count == 0) {
            //非代调账号
            DispatchCargoOwner dispatchCargoOwner = dispatchCargoOwnerMapper.selectByUserId(queryBean.getUserId());
            if (dispatchCargoOwner == null) {
                //不是专车货主
                json.put("hideSpecialCarRule", true);
            } else {
                Integer ruleCount = dispatchCargoOwnerMapper.selectRuleCount(queryBean.getUserId());
                if (ruleCount > 0) {
                    //是专车货主并且签约合作商为平台或没有签约合作商
                    json.put("hideSpecialCarRule", true);
                }
            }
        }

        return ResultMsgBean.successResponse(json);
    }

    /**
     * 专车运费测算
     * 6470计算逻辑：
     * 1、公里数计算
     * 起步价：20公里，运费410元
     * 20公里~100公里，每公里11.5元
     * 100公里以上，每公里8.5元
     * <p>
     * 2、其他费用
     * 用户手动输入的其他费用
     * 3、司机驾驶此类货物计算
     * 若需要司机驾驶此类货物，运费包含交接费36.7（后台配置）
     *
     * @param calculatePriceBean
     * @return
     */
    @Override
    public ResultMsgBean calculatePrice(CalculatePriceBean calculatePriceBean) {
        JSONObject price = new JSONObject();

        BigDecimal priceDecimal = doCalculatePrice(calculatePriceBean);

        price.put("price", priceDecimal);
        return ResultMsgBean.successResponse(price);
    }

    /**
     * 执行价格计算
     *
     * @param calculatePriceBean
     * @return
     */
    private BigDecimal doCalculatePrice(CalculatePriceBean calculatePriceBean) {
        // 计算公里数运费
        BigDecimal priceDecimal = calcDistancePrice(calculatePriceBean);

        // 需要司机驾驶此类货物
        if (DriverDrivingEnum.DRIVING.getCode().equals(calculatePriceBean.getDriverDriving())) {

            String handoverFeeConfig = tytConfigService.getStringValue(Constant.ZHUANCHE_PUBLISH_HANDOVER_FEE_CONFIG_KEY, Constant.ZHUANCHE_PUBLISH_HANDOVER_FEE_DEFAULT_VALUE);
            priceDecimal = priceDecimal.add(new BigDecimal(handoverFeeConfig));
        }

        // 其他费用
        if (Objects.nonNull(calculatePriceBean.getOtherFee())) {
            priceDecimal = priceDecimal.add(calculatePriceBean.getOtherFee());
        }
        return priceDecimal.setScale(0, RoundingMode.UP);
    }

    /**
     * 计算公里数运费
     * 起步价：20公里，运费410元
     * 20公里~100公里，每公里11.5元
     * 100公里以上，每公里8.5元
     *
     * @param calculatePriceBean
     * @return
     */
    private static BigDecimal calcDistancePrice(CalculatePriceBean calculatePriceBean) {
        BigDecimal priceDecimal = new BigDecimal(Constant.ZHUANCHE_BASE_PRICE);
        BigDecimal distanceKilometer = calculatePriceBean.getDistanceKilometer();

        // 超20公里<100公里，每公里11.5元
        if (distanceKilometer.compareTo(new BigDecimal(Constant.ZHUANCHE_DISTANCE_20)) > 0 && distanceKilometer.compareTo(new BigDecimal(Constant.ZHUANCHE_DISTANCE_100)) <= 0) {

            BigDecimal subtract = distanceKilometer.subtract(new BigDecimal(Constant.ZHUANCHE_DISTANCE_20));
            priceDecimal = subtract.multiply(new BigDecimal(Constant.ZHUANCHE_PRICE_11_5)).add(priceDecimal);
        }

        // 超100公里<，每公里8.5元
        if (distanceKilometer.compareTo(new BigDecimal(Constant.ZHUANCHE_DISTANCE_100)) > 0) {

            BigDecimal subtract = distanceKilometer.subtract(new BigDecimal(Constant.ZHUANCHE_DISTANCE_100));
            BigDecimal levelTwoPrice = subtract.multiply(new BigDecimal(Constant.ZHUANCHE_PRICE_8_5));

            BigDecimal levelOnePrice = new BigDecimal(Constant.ZHUANCHE_DISTANCE_100).subtract(new BigDecimal(Constant.ZHUANCHE_DISTANCE_20)).multiply(new BigDecimal(Constant.ZHUANCHE_PRICE_11_5));

            priceDecimal = priceDecimal.add(levelOnePrice).add(levelTwoPrice);
        }
        return priceDecimal;
    }

    /**
     * 专车发货自动派单
     *
     * @param assignOrderBean
     * @return
     */
    @Override
    public ResultMsgBean autoAssignOrderForSpecialCar(AutoAssignOrderBean assignOrderBean) throws Exception {
        Transport transport = new Transport();
        transport.setSrcMsgId(assignOrderBean.getTsId());
        transport.setStartCity(assignOrderBean.getStartCity());
        transport.setDestCity(assignOrderBean.getDestCity());
        transport.setDriverDriving(assignOrderBean.getDriverDriving());
        transport.setGoodTypeName(assignOrderBean.getGoodsTypeName());
        transport.setCargoOwnerId(assignOrderBean.getCargoOwnerId());
        transport.setDistanceValue(assignOrderBean.getDistanceLimit());

        TransportMain transportMain = transportMainService.getBySrcMsgId(assignOrderBean.getTsId());
        transport.setStartArea(transportMain.getStartArea());
        transport.setStartProvinc(transportMain.getStartProvinc());
        transport.setDestProvinc(transportMain.getDestProvinc());
        transport.setExcellentGoods(transportMain.getExcellentGoods());
        transport.setWeight(transportMain.getWeight());
        transport.setStartLatitudeValue(transportMain.getStartLatitudeValue());
        transport.setStartLongitudeValue(transportMain.getStartLongitudeValue());

        User user = userService.getByUserId(assignOrderBean.getUserId());
        transportBusiness.autoAssignOrderForSpecialCar(transport, user);
        return ResultMsgBean.successResponse();
    }

    /**
     * since 6480
     * 专车二期专线，运费测算
     *
     * @param calculatePriceBean
     * @return
     */
    @Override
    public ResultMsgBean calculatePriceV2(CalculatePriceBean calculatePriceBean) {
        // 处理app传过来的地址和后台地址不匹配问题   (德宏傣族景颇族自治州 -> 德宏州)
        TytCity startCity = tytCityMapper.getShortCityName(calculatePriceBean.getStartCity());
        if (Objects.nonNull(startCity)) {
            calculatePriceBean.setStartCity(startCity.getCityName());
        }
        TytCity destCity = tytCityMapper.getShortCityName(calculatePriceBean.getDestCity());
        if (Objects.nonNull(destCity)) {
            calculatePriceBean.setDestCity(destCity.getCityName());
        }

        JSONObject price = doCalculatePriceV2(calculatePriceBean);

        return ResultMsgBean.successResponse(price);
    }

    /**
     * 执行专车价格测算
     *
     * @param calculatePriceBean
     * @return
     */
    public JSONObject doCalculatePriceV2(CalculatePriceBean calculatePriceBean) {
        JSONObject priceJson = new JSONObject();
        priceJson.put("price", 0);
        Long cargoOwnerId = calculatePriceBean.getCargoOwnerId();

        // 如果当前登录人是代调，可以跳过发货第一页运费测算
        if (Objects.nonNull(calculatePriceBean.getUserId()) && calculatePriceBean.getUserId() > 0) {
            int count = tytDispatchCompanyMapper.countByUserId(calculatePriceBean.getUserId());
            if (count > 0 && (Objects.isNull(cargoOwnerId) || cargoOwnerId == 0)) {
                // 是代调账号，并且没有选择签约合作商
                priceJson.put("price", 1);

                //没有签约合作商就一定是平台
                Integer fastDispatchMin = tytConfigService.getIntValue("special_car_dispatch_time_normal", 10);
                //专车最快接单时间
                priceJson.put("fastDispatchMin", fastDispatchMin);
                //专车最快接单时间文案
                priceJson.put("fastDispatchWord", "最快" + fastDispatchMin + "分钟接单 不扣发货次数");
                return priceJson;
            }
        }

        // 签约合作商不是有效状态，取平台
        TytDispatchCooperative plat = tytDispatchCooperativeMapper.selectByName(PLAT_CARGO_OWNER_NAME);
        DispatchCargoOwner owner = dispatchCargoOwnerMapper.selectSignedByUserId(calculatePriceBean.getUserId());

        // 匹配签约合作商
        if (Objects.isNull(cargoOwnerId) || cargoOwnerId == 0) {
            if (Objects.nonNull(owner) && Objects.nonNull(owner.getCooperativeId()) && owner.getCooperativeId() != 0) {
                cargoOwnerId = owner.getCooperativeId();
            } else {
                cargoOwnerId = plat.getId();
            }
            if (Objects.isNull(cargoOwnerId)) {
                logger.info("专车运费计算未匹配到签约合作商，userId:{}, startCity:{}, destCity:{}, weight:{}",
                        calculatePriceBean.getUserId(), calculatePriceBean.getStartCity(), calculatePriceBean.getDestCity(),
                        calculatePriceBean.getWeight());
                priceJson.put("notice", ROUTE_NOT_SUPPORT);
                return priceJson;
            }
        } else {
            TytDispatchCooperative cooperative = tytDispatchCooperativeMapper.selectByPrimaryKey(cargoOwnerId);
            if (Objects.nonNull(cooperative) && cooperative.getStatus() != 1) {
                if (Objects.isNull(plat)) {
                    logger.info("专车运费计算未匹配到签约合作商，userId:{}, startCity:{}, destCity:{}, weight:{}",
                            calculatePriceBean.getUserId(), calculatePriceBean.getStartCity(), calculatePriceBean.getDestCity(),
                            calculatePriceBean.getWeight());
                    priceJson.put("notice", ROUTE_NOT_SUPPORT);
                    return priceJson;
                }
                cargoOwnerId = plat.getId();
            }
        }

        Integer fastDispatchMin = tytConfigService.getIntValue("special_car_dispatch_time_normal", 10);
        int priceNoNormalCount = tytSpecialCarPriceConfigMapper.countMatchPriceConfigCityAndRule(calculatePriceBean.getStartCity(), calculatePriceBean.getDestCity());
        if (priceNoNormalCount > 0) {
            //如果出发地目的地在专车路线运费配置里面存在非平台的配置，则展示非平台X分钟
            fastDispatchMin = tytConfigService.getIntValue("special_car_dispatch_time_not_normal", 20);
        }

        //专车最快接单时间
        priceJson.put("fastDispatchMin", fastDispatchMin);

        //专车最快接单时间文案
        priceJson.put("fastDispatchWord", "最快" + fastDispatchMin + "分钟接单 不扣发货次数");

        // 匹配运费规则
        BigDecimal weight = new BigDecimal(calculatePriceBean.getWeight());
        TytSpecialCarPriceConfig priceConfig = tytSpecialCarPriceConfigMapper.selectMatchPriceConfig(cargoOwnerId,
                calculatePriceBean.getStartCity(), calculatePriceBean.getDestCity(), weight);
        if (Objects.isNull(priceConfig) && Objects.equals(plat.getId(), cargoOwnerId)) {
            // 无签约合作商、签约合作商为平台的，路线满足【经分定价路线】的，则取经分模型的快速优车价格
            JSONObject biRouteResult = getBiRouteDataPrice(calculatePriceBean, priceJson);
            if (biRouteResult != null) {
                return biRouteResult;
            }
        }
        if (Objects.isNull(priceConfig)) {
            logger.info("专车运费计算未匹配到运费规则配置，userId:{}, startCity:{}, destCity:{}, weight:{}，cargoOwnerId:{}",
                    calculatePriceBean.getUserId(), calculatePriceBean.getStartCity(), calculatePriceBean.getDestCity(),
                    calculatePriceBean.getWeight(), cargoOwnerId);
            String notice = ROUTE_NOT_SUPPORT;
            int count = tytSpecialCarPriceConfigMapper.countByOwnerAndRoute(cargoOwnerId, calculatePriceBean.getStartCity(),
                    calculatePriceBean.getDestCity());
            if (count > 0) {
                notice = TONNAGE_NOT_SUPPORT;
            }
            priceJson.put("notice", notice);
            return priceJson;
        }

        // 专车距离重新计算
        reacquireSpecialCarDistance(calculatePriceBean);
        // 匹配到运费计算规则，执行运费计算
        BigDecimal price = calculatePriceWithPriceRule(calculatePriceBean, priceConfig);
        if (Objects.equals(plat.getId(), cargoOwnerId)) {
            // 普通货主取平台运费规则，运费金额个位金额向下取整 1567 -> 1560
            price = price.movePointLeft(1).setScale(0, RoundingMode.DOWN).movePointRight(1);
        }
        priceJson.put("price", price);
        if (price.compareTo(new BigDecimal("0")) <= 0) {
            // 无签约合作商、签约合作商为平台的，路线满足【经分定价路线】的，则取经分模型的快速优车价格
            if (Objects.equals(plat.getId(), cargoOwnerId)) {
                JSONObject biRouteResult = getBiRouteDataPrice(calculatePriceBean, priceJson);
                if (biRouteResult != null) {
                    return biRouteResult;
                }
            }
            priceJson.put("notice", ROUTE_NOT_SUPPORT);
        } else {
            priceJson.put("priceType", priceConfig.getPriceType());
            if (Objects.equals(PriceTypeEnum.FLEXIBLE.getCode(), priceConfig.getPriceType())) {
                BigDecimal lowerLimit = Objects.isNull(calculatePriceBean.getLowerLimit()) ? new BigDecimal("0") : calculatePriceBean.getLowerLimit();
                priceJson.put("lowerLimit", lowerLimit);
                BigDecimal canAdjustPrice = lowerLimit.movePointLeft(2).multiply(price);
                BigDecimal lowerLimitPrice = price.subtract(canAdjustPrice).setScale(0, RoundingMode.UP);
                priceJson.put("lowerLimitPrice", lowerLimitPrice);
            }
            priceJson.put("specialCarTabTopAndCheck", 0);
            //若货主在【专车货主管理】中，且签约合作商为非空、非平台
            if (owner != null && owner.getCooperativeId() != null && plat != null && plat.getId() != null
                    && owner.getCooperativeId().compareTo(0L) != 0 && owner.getCooperativeId().compareTo(plat.getId()) != 0) {
                //货主在专车货主管理中，但是签约合作商为空或者不是平台
                priceJson.put("specialCarTabTopAndCheck", 1);
            }
            if(calculatePriceBean.getClientVersion() != null && Integer.parseInt(calculatePriceBean.getClientVersion()) >= 6690 && calculatePriceBean.getUseCarType() != null && calculatePriceBean.getUseCarType() == 1){
                BigDecimal perkPrice = getPerkPrice(calculatePriceBean, price);
                if (perkPrice.compareTo(BigDecimal.ZERO) > 0){
                    priceJson.put("perkPrice", perkPrice.toPlainString());
                    priceJson.put("fastDispatchWord", "100%接单 不扣发货次数");
                    priceJson.put("price", price.subtract(perkPrice).toPlainString());
                }
            }
        }
        return priceJson;
    }

    /**
     * 无签约合作商、签约合作商为平台的，路线满足【经分定价路线】的，则取经分模型的快速优车价格
     *
     * @param priceBean
     * @param result
     * @return
     */
    private JSONObject getBiRouteDataPrice(CalculatePriceBean priceBean, JSONObject result) {
        try {
            int count = tytSpecialCarBiRouteMapper.countByRoute(priceBean.getStartCity(), priceBean.getDestCity());
            if (count <= 0) {
                return null;
            }
            TransportCarryBean carryReq = new TransportCarryBean();
            carryReq.setUserId(priceBean.getUserId());
            carryReq.setStartCity(priceBean.getStartCity());
            carryReq.setDestCity(priceBean.getDestCity());
            carryReq.setGoodsWeight(priceBean.getWeight());
            carryReq.setDistance(String.valueOf(priceBean.getDistanceKilometer()));
            Response<CarryPriceVo> response = thPriceClient.getThPrice(carryReq).execute();
            if (!response.isSuccessful()) {
                return null;
            }
            CarryPriceVo thPrice = response.body();
            if (Objects.isNull(thPrice) || Objects.isNull(thPrice.getFixPriceFast())) {
                return null;
            }

            BigDecimal price = new BigDecimal(thPrice.getFixPriceFast());
            result.put("price", price);
            result.put("priceType", PriceTypeEnum.FLEXIBLE.getCode());
            BigDecimal lowerLimit = new BigDecimal("10");
            result.put("lowerLimit", lowerLimit);
            BigDecimal canAdjustPrice = lowerLimit.movePointLeft(2).multiply(price);
            BigDecimal lowerLimitPrice = price.subtract(canAdjustPrice).setScale(0, RoundingMode.UP);
            result.put("lowerLimitPrice", lowerLimitPrice);
            result.put("specialCarTabTopAndCheck", 0);

            if (StringUtils.isNotBlank(priceBean.getClientVersion()) && Integer.parseInt(priceBean.getClientVersion()) >= 6690 &&
                    Objects.equals(priceBean.getUseCarType(), UseCarTypeEnum.FULL.getCode())){
                //优惠价格计算
                BigDecimal perkPrice = getPerkPrice(priceBean, price);
                if (perkPrice.compareTo(BigDecimal.ZERO) > 0){
                    result.put("perkPrice", perkPrice.toPlainString());
                    result.put("fastDispatchWord", "100%接单 不扣发货次数");
                    result.put("price", price.subtract(perkPrice).toPlainString());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("专车运费计算getBiRouteDatePrice error:", e);
        }
        return null;
    }


    private BigDecimal getPerkPrice(CalculatePriceBean calculatePriceBean, BigDecimal price) {
        Integer userType = abtestService.getUserType("goods_price_perk_abtest", calculatePriceBean.getUserId());
        if (userType == null || userType != 1) {
            return BigDecimal.ZERO;
        }

        TytTransportPricePerkConfig config = tytTransportPricePerkConfigMapper.getConfigByCity(
                calculatePriceBean.getStartCity(), calculatePriceBean.getDestCity()
        );

        if (config == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal distance = calculatePriceBean.getDistanceKilometer();
        BigDecimal min = new BigDecimal(config.getDistanceMin());
        BigDecimal max = new BigDecimal(config.getDistanceMax());
        BigDecimal otherFee = calculatePriceBean.getOtherFee() == null ? BigDecimal.ZERO : calculatePriceBean.getOtherFee();
        BigDecimal finalPrice = price.subtract(otherFee);
        BigDecimal drivingFee = calculatePriceBean.getDrivingFee() == null ? BigDecimal.ZERO : calculatePriceBean.getDrivingFee();
        finalPrice = finalPrice.subtract(drivingFee);

        if (distance.compareTo(min) >= 0 && distance.compareTo(max) <= 0) {
            BigDecimal ratio = new BigDecimal(config.getPerkRatio()).divide(new BigDecimal("100"));
            return finalPrice.multiply(ratio).setScale(0, RoundingMode.UP);
        }
        return BigDecimal.ZERO;
    }


    /**
     * 运费测算逻辑处理
     *
     * @param calculatePriceBean
     * @param priceConfig
     * @return
     */
    public BigDecimal calculatePriceWithPriceRule(CalculatePriceBean calculatePriceBean, TytSpecialCarPriceConfig priceConfig) {
        BigDecimal result = new BigDecimal("0");

        // 使用零担运费规则
        boolean useLessPriceRule = false;
        // 配置了零担运费规则，并且货源吨位在28吨以下，使用零担运费规则计算
        String priceRule = priceConfig.getPriceRule();
        if (StringUtils.isNotEmpty(priceConfig.getLessPriceRule())) {
            String tonnage = tytConfigService.getStringValue(Constant.SPECIAL_CAR_PRICE_CONFIG_TONNAGE, "28");
            BigDecimal weight = new BigDecimal(calculatePriceBean.getWeight());
            BigDecimal tonnageDecimal = new BigDecimal(tonnage);
            if (weight.compareTo(tonnageDecimal) <= 0) {
                priceRule = priceConfig.getLessPriceRule();
                useLessPriceRule = true;
                // 展示了用车类型则必选，如果选择的是整车，使用整车运费规则
                if (Objects.equals(UseCarTypeEnum.FULL.getCode(), calculatePriceBean.getUseCarType())) {
                    priceRule = priceConfig.getPriceRule();
                    useLessPriceRule = false;
                }
            }
        }
        // 运费模式为灵活运价，为运价下限字段赋值
        if (Objects.equals(PriceTypeEnum.FLEXIBLE.getCode(), priceConfig.getPriceType())) {
            if (useLessPriceRule) {
                calculatePriceBean.setLowerLimit(priceConfig.getLessPriceLowerLimit());
            } else {
                calculatePriceBean.setLowerLimit(priceConfig.getPriceLowerLimit());
            }
        }

        List<PriceConfigBean> priceConfigBeans = convertPriceConfigBeanList(calculatePriceBean, priceRule);
        if (CollectionUtils.isEmpty(priceConfigBeans)) {
            return result;
        }

        // 根据公里数计算区间运费
        result = calculateDistanceKilometerPrice(priceConfigBeans, calculatePriceBean.getDistanceKilometer());

        // 根据运费配置计算出运费后才添加驾驶货物费和其他费用，否则运费为0，不允许发货
        if (result.compareTo(new BigDecimal("0")) > 0) {
            // 需要司机驾驶此类货物，添加驾驶货物费
//            if (DriverDrivingEnum.DRIVING.getCode().equals(calculatePriceBean.getDriverDriving()) && Objects.nonNull(priceConfig.getDrivingFee())) {
//                result = result.add(priceConfig.getDrivingFee());
//                calculatePriceBean.setDrivingFee(priceConfig.getDrivingFee());
//            }

            // 添加其他费用
            if (Objects.nonNull(calculatePriceBean.getOtherFee())) {
                result = result.add(calculatePriceBean.getOtherFee());
            }

            // 订金不退还，将定金添加到运费中
            // 6670 订金选择不退还时，不自动增加运费
            /*if (Objects.nonNull(calculatePriceBean.getInfoFee())) {
                result = result.add(calculatePriceBean.getInfoFee());
            }*/
        }

        return result.setScale(0, RoundingMode.CEILING);
    }

    /**
     * 执行公里数运费计算
     * 计算规则：
     * 1、运费配置一条为 0~200 固定价 400元 ，公里数为250公里，运费返回400
     * 2、运费配置最后一条为  100~200  固定价 400元，公里数为300公里，运费计算为：前几条运费配置计算出的价格+最后一条规则价格400
     * 3、运费配置最后一条为  100~200 公里价 10元/公里，公里数为300公里，运费计算为：前几条运费配置计算出的价格+(300-100)*10
     *
     * @param priceConfigBeans
     * @param distanceKilometer
     * @return
     */
    private BigDecimal calculateDistanceKilometerPrice(List<PriceConfigBean> priceConfigBeans, BigDecimal distanceKilometer) {
        BigDecimal result = new BigDecimal("0");

        //如果priceConfigBeans的最后一次区间的end小于当前距离，则直接返回0
        if (distanceKilometer.compareTo(priceConfigBeans.get(priceConfigBeans.size() - 1).getEnd()) > 0) {
            return result;
        }

        for (int i = 0; i < priceConfigBeans.size(); i++) {
            PriceConfigBean configBean = priceConfigBeans.get(i);
            if (distanceKilometer.compareTo(configBean.getStart()) <= 0) {
                return result;
            } else if (distanceKilometer.compareTo(configBean.getEnd()) > 0) {
                if (PriceConfigTypeEnum.KILOMETER_PRICE.getCode().equals(configBean.getType())) {
                    // 公里价
                    BigDecimal end = configBean.getEnd();
                    if (i == priceConfigBeans.size() - 1) {
                        // 最后一个配置项，需要特殊处理，如果为公里价，需要按实际公里数计算价格
                        end = distanceKilometer;
                    }
                    BigDecimal start = configBean.getStart();
                    if (i == 0) {
                        start = new BigDecimal("0");
                    }
                    BigDecimal currentPrice = end.subtract(start).multiply(configBean.getPrice());
                    result = result.add(currentPrice);
                } else {
                    // 固定价
                    result = result.add(configBean.getPrice());
                }
            } else {
                // 公里数在当前配置区间内
                if (PriceConfigTypeEnum.KILOMETER_PRICE.getCode().equals(configBean.getType())) {
                    // 公里价
                    BigDecimal start = configBean.getStart();
                    if (i == 0) {
                        start = new BigDecimal("0");
                    }
                    BigDecimal currentPrice = distanceKilometer.subtract(start).multiply(configBean.getPrice());
                    result = result.add(currentPrice);
                } else {
                    result = result.add(configBean.getPrice());
                }
            }
        }
        return result;
    }

    /**
     * 转换价格配置json为价格配置实体列表并排序
     *
     * @param calculatePriceBean
     * @param priceRule
     * @return
     */
    private List<PriceConfigBean> convertPriceConfigBeanList(CalculatePriceBean calculatePriceBean, String priceRule) {
        List<PriceConfigBean> priceConfigBeans = new ArrayList<>();
        try {
            priceConfigBeans = JSON.parseArray(priceRule, PriceConfigBean.class);
        } catch (Exception e) {
            logger.error("专车运费计算价格配置转换异常，userId:{}, startCity:{}, destCity:{}, weight:{}，priceRule:{}", calculatePriceBean.getUserId(), calculatePriceBean.getStartCity(), calculatePriceBean.getDestCity(), calculatePriceBean.getWeight(), priceRule, e);
            return priceConfigBeans;
        }

        if (CollectionUtils.isNotEmpty(priceConfigBeans)) {
            try {
                priceConfigBeans.sort(Comparator.comparing(PriceConfigBean::getStart));
            } catch (Exception e) {
                logger.error("专车运费价格计算运费配置排序失败，userId:{}, startCity:{}, destCity:{}, weight:{}，priceRule:{}", calculatePriceBean.getUserId(), calculatePriceBean.getStartCity(), calculatePriceBean.getDestCity(), calculatePriceBean.getWeight(), priceRule, e);
            }
        }

        return priceConfigBeans;
    }

    @Override
    public Transport getByGoodsId(Long goodsId) {
        TransportMain oldTransportMain = transportMainService.getById(goodsId);
        if (oldTransportMain != null) {
            Transport oldTransport = new Transport();
            BeanUtils.copyProperties(oldTransportMain, oldTransport);
            logger.info("根据货物ID查询最新的货物ID,goodsId:{}", oldTransport.getId());
            return oldTransport;
        } else {
            Transport oldTransport = transportService.getById(goodsId);
            oldTransportMain = transportMainService.getById(oldTransport.getSrcMsgId());
            if (oldTransportMain != null) {
                Transport transport = new Transport();
                BeanUtils.copyProperties(oldTransportMain, transport);
                logger.info("根据货物ID查询最新的货物ID,goodsId:{}", transport.getId());
                return transport;
            }

        }
        return null;
    }

    /**
     * @param transport
     * @param oldTran
     * @param publishType 1:新发布；2：做日数据编辑发布：3：今日数据编辑发布
     * @return
     * @throws Exception
     */
    public Transport addTransportBusiness(Transport transport, TransportMain oldTran, PublisTypeEnum publishType, TransportPublishBean publishBean,
                                          BackendTransportBean backendTransportBean, TytTransportMainExtend mainExtend) throws Exception {

        transport.setId(null);
        // 过滤地区
        //transport = filterRegion(transport);
        //区分新老版本的货源标准化，添加clientSign，为兼容PC端标准货增加
        String clientVersion = publishBean.getClientVersion();
        String clientSign = publishBean.getClientSign();

        if (publishBean.getDistance() == null) {
            // 距离从字典中取
            TytMapDict tytMapDict = tytMapDictService.getDistance(String.valueOf(transport.getPlatId()), transport.getStartProvinc(), transport.getStartCity(), transport.getStartArea(), transport.getDestProvinc(), transport.getDestCity(), transport.getDestArea());
            if (tytMapDict != null) {
                transport.setDistanceValue(tytMapDict.getDistance());
                transport.setAndroidDistance(tytMapDict.getDistance());
                transport.setIosDistance(tytMapDict.getIosDistance());
            }
        }

        // 老字段不用，清空
//        transport.setDistanceValue(null);
        transport.setIsDisplay(1); // 不进行精准货源推荐，直接放入找货列表
        if (publishBean.getBusPublishType() != null && publishBean.getBusPublishType() == 1) {
            transport.setIsShow(0);
        } else {
            transport.setIsShow(1);
        }

        //指派司机的开票货源不在找货大厅展示
        if (publishBean.getInvoiceTransport() != null && publishBean.getInvoiceTransport() == 1 && StringUtils.isNotBlank(publishBean.getAssignCarTel())) {
            transport.setIsShow(0);
        }

        TransportMain transportMain = new TransportMain();
        BeanUtils.copyProperties(transport, transportMain);


        // transport扩展表
        TytTransportExtend transportExtend = new TytTransportExtend();
        BeanUtils.copyProperties(mainExtend, transportExtend);


//        // 更新相似货源信息及首发信息
//        transportService.updateSimilarityCode(transport, transportMain);
        switch (publishType) {
            case 当日货物编辑发布:
                todayRePublish(transportMain, transport, oldTran, mainExtend, transportExtend);
                break;
            case 历史货物编辑发布:
                historyRePublish(transportMain, transport, oldTran, mainExtend, transportExtend);
                break;
            default:
                newPublish(transportMain, transport, mainExtend, transportExtend);
                break;

        }

        if (Objects.nonNull(backendTransportBean) && backendTransportBean.getStatus() != 2) {
            if (publishBean.getBusPublishType() != null && publishBean.getBusPublishType() == 1) {
                backendTransportBean.setFindCarType(0);
            } else {
                backendTransportBean.setFindCarType(1);
            }
            backendTransport(backendTransportBean.getId(), transport, backendTransportBean);
        }

        return transport;
    }

    private TytTransportMainExtend getMainExtend(Transport transport, TransportPublishBean publishBean) {
        // main扩展表
        TytTransportMainExtend mainExtend = new TytTransportMainExtend();
        mainExtend.setUseCarType(publishBean.getUseCarType());
        mainExtend.setPriceType(publishBean.getPriceType());
//        mainExtend.setSuggestMinPrice(publishBean.getSuggestMinPrice());
//        mainExtend.setSuggestMaxPrice(publishBean.getSuggestMaxPrice());

        mainExtend.setSuggestMinPrice(publishBean.getFixPriceMin());
        mainExtend.setSuggestMaxPrice(publishBean.getFixPriceMax());
        mainExtend.setFixPriceFast(publishBean.getFixPriceFast());
        mainExtend.setCostPrice(publishBean.getThMinPrice());
        mainExtend.setPriceCap(publishBean.getPriceCap());
//        if (publishBean.getGoodCarPriceTransport() != null && publishBean.getGoodCarPriceTransport().equals(1)) {
//            mainExtend.setSuggestMinPrice(publishBean.getFixPriceMin());
//            mainExtend.setSuggestMaxPrice(publishBean.getFixPriceMax());
//        }
        mainExtend.setGoodTransportLabel(0);
        mainExtend.setGoodTransportLabelPart(0);
        int modelLevel = transportGoodModelFactorService.judgeModelLevel(transport, mainExtend);
        if (modelLevel > 0) {
            mainExtend.setGoodTransportLabel(modelLevel);
        } else if (modelLevel < 0) {
            mainExtend.setGoodTransportLabelPart(modelLevel * -1);
        }
        return mainExtend;
    }


    private void newPublish(TransportMain transportMain, Transport transport, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) {
        // 保存transportMain信息
        transportMainService.add(transportMain);
        transportMain.setSrcMsgId(transportMain.getId());
        transportMainService.update(transportMain);

        // 保存main表扩展表
        mainExtend.setSrcMsgId(transportMain.getId());
        mainExtend.setCreateTime(transportMain.getCtime());
        mainExtend.setModifyTime(transportMain.getMtime());
        transportMainService.addMainExtend(mainExtend);

        // 保存transport信息
        transport.setSrcMsgId(transportMain.getId());
        transportService.add(transport);

        // 保存transport 扩展表
        transportExtend.setId(null);
        transportExtend.setTsId(transport.getId());
        transportExtend.setSrcMsgId(transport.getSrcMsgId());
        transportExtend.setCreateTime(transport.getCtime());
        transportExtend.setModifyTime(transport.getMtime());
        transportExtend.setTopFlag(1);
        transportExtendService.addExtend(transportExtend);

    }

    private void todayRePublish(TransportMain transportMain, Transport transport, TransportMain oldTran, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) throws Exception {

        Long srcMsgId = oldTran.getSrcMsgId();
        transport.setSrcMsgId(srcMsgId);

        // 将历史货物设置为transport不显示状态且无效， ；
        transportService.disableTransport(oldTran.getSrcMsgId());

        //编辑时不置顶
        transportBusiness.saveTransportAndCheckTop(transport, transportExtend, false, true);

        // 处理重发次数，pc端判充使用
        Integer resendCounts = transport.getResendCounts();
        String pcOldContent = transport.getPcOldContent();

        logger.info("源货物的ID为{}  src_msgId{}", oldTran.getId(), oldTran.getSrcMsgId());
        // 修改transportMain表信息  并保存
        BeanUtils.copyProperties(transportMain, oldTran, "id", "srcMsgId", "tsOrderNo", "ctime", "releaseTime");

        oldTran.setResendCounts(resendCounts);
        oldTran.setPcOldContent(pcOldContent);
        oldTran.setMtime(TimeUtil.getTimeStamp());
        //oldTran.setResendCounts(transport.getResendCounts());
        oldTran.setStatus(1);
        oldTran.setPriorityRecommendExpireTime(transportMain.getPriorityRecommendExpireTime());
        transportMainService.update(oldTran);

        mainExtend.setSrcMsgId(srcMsgId);
        mainExtend.setModifyTime(oldTran.getMtime());
        transportMainService.updateMainExtend(mainExtend);
    }

    private void historyRePublish(TransportMain transportMain, Transport transport, TransportMain oldTran, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) {
        Long oldSrcMsgId = oldTran.getSrcMsgId();
        // 将历史货物设置为transportMain、transport不显示状态；
        transportMainService.noDisplayTransportMain(oldSrcMsgId);
        transportService.noDisplayTransport(oldSrcMsgId);

        // 保存transportMain信息
        transportMainService.add(transportMain);
        transportMain.setSrcMsgId(transportMain.getId());
        transportMainService.update(transportMain);
        // 保存main表扩展表
        mainExtend.setSrcMsgId(transportMain.getId());
        mainExtend.setCreateTime(transportMain.getCtime());
        mainExtend.setModifyTime(transportMain.getMtime());
        transportMainService.addMainExtend(mainExtend);
        //修改后台货源表对应发货id
        backendTransportMapper.updateMsgIdByMsgId(transportMain.getId(), oldSrcMsgId);
        // 保存transport信息
        transport.setSrcMsgId(transportMain.getId());

        transportService.add(transport);

        // 保存transport 扩展表
        transportExtend.setId(null);
        transportExtend.setTsId(transport.getId());
        transportExtend.setSrcMsgId(transport.getSrcMsgId());
        transportExtend.setCreateTime(transport.getCtime());
        transportExtend.setModifyTime(transport.getMtime());
        transportExtend.setTopFlag(1);
        transportExtendService.addExtend(transportExtend);
    }


    // 过滤地区
    public Transport filterRegion(Transport transport) {
        // "北京,上海,天津,重庆,香港,澳门,台湾"
        String filterRqionStr = this.tytConfigService.getStringValue("editPublishTransportZhixiashiList");
        String[] filterRqionArray = filterRqionStr.split(",");

        String startArea = null;
        String destArea = null;

        boolean startStatus = false;
        boolean destStatus = false;


        for (String s : filterRqionArray) {
            if (transport.getStartPoint().length() > 2 && transport.getStartPoint().indexOf(s) != -1) {
                if (transport.getStartPoint().indexOf("市") != -1) {
                    startArea = transport.getStartPoint().substring(transport.getStartPoint().indexOf("市") + 1);
                    if (startArea != null && startArea.length() > 0) startStatus = true;
                }
                break;
            }
        }
        for (String s : filterRqionArray) {
            if (transport.getDestPoint().length() > 2 && transport.getDestPoint().indexOf(s) != -1) {
                if (transport.getDestPoint().indexOf("市") != -1) {
                    destArea = transport.getDestPoint().substring(transport.getDestPoint().indexOf("市") + 1);
                    if (destArea != null && destArea.length() > 0) destStatus = true;
                }
                break;
            }
        }

        // 增加出发地目的地 省市区
        TytGeoDict startTytGeoDict = tytGeoDictService.getTytGeoDict(transport.getStartCoordXValue(), transport.getStartCoordYValue());
        if (startTytGeoDict != null) {
            if (startStatus) {
                transport.setStartArea(startArea);
            } else {
                transport.setStartArea(startTytGeoDict.getArea());
            }
            transport.setStartCity(startTytGeoDict.getCity());
            transport.setStartProvinc(startTytGeoDict.getProvinc());
        }
        TytGeoDict destTytGeoDict = tytGeoDictService.getTytGeoDict(transport.getDestCoordXValue(), transport.getDestCoordYValue());
        if (destTytGeoDict != null) {
            if (destStatus) {
                transport.setDestArea(destArea);
            } else {
                transport.setDestArea(destTytGeoDict.getArea());
            }
            transport.setDestCity(destTytGeoDict.getCity());
            transport.setDestProvinc(destTytGeoDict.getProvinc());

        }
        if (startStatus) {
            TytGeoDict startTytGeoDictNew = tytGeoDictService.getTytGeoDict(transport.getStartProvinc(), transport.getStartCity(), transport.getStartArea());
            if (startTytGeoDictNew != null) {
                transport.setStartCoordXValue(startTytGeoDictNew.getPx().intValue());
                transport.setStartCoordYValue(startTytGeoDictNew.getPy().intValue());
                transport.setStartCoord(transport.getStartCoordX() + "," + transport.getStartCoordY());
            }
        }
        if (destStatus) {
            TytGeoDict destTytGeoDictNew = tytGeoDictService.getTytGeoDict(transport.getDestProvinc(), transport.getDestCity(), transport.getDestArea());
            if (destTytGeoDictNew != null) {
                transport.setDestCoordXValue(destTytGeoDictNew.getPx().intValue());
                transport.setDestCoordYValue(destTytGeoDictNew.getPy().intValue());
                transport.setDestCoord(transport.getDestCoordX() + "," + transport.getDestCoordY());
            }
        }
        if (startStatus || destStatus)
            logger.info("编辑发布货物，经纬度修正：transport=【{}】", JSON.toJSONString(transport));

        return transport;
    }


    //补充货物信息的参考值，用于推荐货物过滤
    /*
    private void addReferTransportInfo(Transport transport) {
        //TODO 增加有好货参考长宽高重数值初始化
        //TODO 优先使用用户输入的，有为空的使用如果是标准化，则使用对应类型货物的默认数值
        // 兼容pc的bug，是否标准化未传值的问题
        if (transport.getIsStandard() == null) { // 如果未传值，则认定为非标货源
            transport.setIsStandard(1);
            transport.setMatchItemId(-1);
        }
        TytMachineTypeBean tytMachineTypeBean = null;
        if (transport.getIsStandard() == 0) {
            tytMachineTypeBean = machineTypeNewService.getTytMachineTypeForIdAll(transport.getMatchItemId().longValue());
            // 由于货源表针对goodtypename不全的数据，进行补齐
            String goodTypeName = "";
            if (transport.getTaskContent().length() > 5) {
                goodTypeName = transport.getTaskContent().substring(0, 5);
            } else {
                goodTypeName = transport.getTaskContent();
            }
            if (tytMachineTypeBean != null) {
                if (StringUtils.isBlank(tytMachineTypeBean.getMachineType())) {
                    transport.setGoodTypeName(goodTypeName);
                } else {
                    transport.setGoodTypeName(tytMachineTypeBean.getMachineType());
                }
                // 解决线上的出现的type为null的异常情况
                if (StringUtils.isNotBlank(tytMachineTypeBean.getType())) {
                    transport.setType(tytMachineTypeBean.getType());
                }
            } else {
                transport.setGoodTypeName(goodTypeName);
            }
        }

        //对参考值进行赋值
        if (org.springframework.util.StringUtils.hasLength(transport.getLength()) && StringUtil.isDouble(transport.getLength())) {
            transport.setReferLength(HighwayCostCalcUtil.calcMultiply(transport.getLength(), "100").intValue());
        } else {
            if (transport.getIsStandard() == 0 && tytMachineTypeBean != null && tytMachineTypeBean.getLength() != null) {
                transport.setReferLength(HighwayCostCalcUtil.calcMultiply(tytMachineTypeBean.getLength(), "100").intValue());
            }
        }
        if (org.springframework.util.StringUtils.hasLength(transport.getWide()) && StringUtil.isDouble(transport.getWide())) {
            transport.setReferWidth(HighwayCostCalcUtil.calcMultiply(transport.getWide(), "100").intValue());
        } else {
            if (transport.getIsStandard() == 0 && tytMachineTypeBean != null && tytMachineTypeBean.getWidth() != null) {
                transport.setReferWidth(HighwayCostCalcUtil.calcMultiply(tytMachineTypeBean.getWidth(), "100").intValue());
            }
        }
        if (org.springframework.util.StringUtils.hasLength(transport.getHigh()) && StringUtil.isDouble(transport.getHigh())) {
            transport.setReferHeight(HighwayCostCalcUtil.calcMultiply(transport.getHigh(), "100").intValue());
        } else {
            if (transport.getIsStandard() == 0 && tytMachineTypeBean != null && tytMachineTypeBean.getHeight() != null) {
                transport.setReferHeight(HighwayCostCalcUtil.calcMultiply(tytMachineTypeBean.getHeight(), "100").intValue());
            }
        }
        if (org.springframework.util.StringUtils.hasLength(transport.getWeight()) && StringUtil.isDouble(transport.getWeight())) {
            transport.setReferWeight(HighwayCostCalcUtil.calcMultiply(transport.getWeight(), "100").intValue());
        } else {
            if (transport.getIsStandard() == 0 && tytMachineTypeBean != null && tytMachineTypeBean.getWeight() != null) {
                transport.setReferWeight(HighwayCostCalcUtil.calcMultiply(tytMachineTypeBean.getWeight(), "100").intValue());
            }
        }
    }
    */

    /**
     * 计算乘法
     *
     * @param reqNumber
     * @param multiple
     * @return
     */
    private Integer calcMultiply(String reqNumber, int multiple) {
        BigDecimal bigDecimal = new BigDecimal(reqNumber);
        BigDecimal multDec = new BigDecimal(multiple);

        BigDecimal resultDec = bigDecimal.multiply(multDec);
        return resultDec.intValue();
    }

    /**
     * 处理参考参数
     *
     * @param reqNumberStr
     * @param numberDec
     * @return
     */
    private Integer getReferNumber(String reqNumberStr, BigDecimal numberDec) {
        Integer referNumber = null;
        if (StringUtils.isNotBlank(reqNumberStr) && StringUtil.isDouble(reqNumberStr)) {
            referNumber = this.calcMultiply(reqNumberStr, 100);
        } else {
            if (numberDec != null) {
                referNumber = numberDec.movePointRight(2).intValue();
            }
        }
        return referNumber;
    }

    /**
     * 补充新版标准化货物信息的参考值
     *
     * @param transport
     */
    @Override
    public boolean addReferTransportNewInfo(Transport transport) {
        boolean matchResult = false;

        // 兼容pc的bug，是否标准化未传值的问题
        if (transport.getIsStandard() == null) { // 如果未传值，则认定为非标货源
            transport.setIsStandard(1);
            transport.setMatchItemId(-1);
        }
        // 查询新版本标准化
        TytMachineTypeBrandNew machineTypeBrandNew = null;
        if (transport.getMatchItemId() != null && transport.getMatchItemId() != -1) {
            machineTypeBrandNew = machineTypeBrandNewService.getById(transport.getMatchItemId());
        }

        //对参考值进行赋值
        if (machineTypeBrandNew != null) {
            matchResult = true;

            String brand = StringUtil.getStringValue(machineTypeBrandNew.getBrand(), "");
            String goodTypeName = StringUtil.getStringValue(machineTypeBrandNew.getSecondClass(), "");
            String topType = StringUtil.getStringValue(machineTypeBrandNew.getTopType(), "");

            transport.setBrand(brand);
            transport.setGoodTypeName(goodTypeName);
            transport.setType(topType);
            //transport.setGoodNumber(1);

            transport.setReferLength(this.getReferNumber(transport.getLength(), machineTypeBrandNew.getLength()));
            transport.setReferWidth(this.getReferNumber(transport.getWide(), machineTypeBrandNew.getWidth()));
            transport.setReferHeight(this.getReferNumber(transport.getHigh(), machineTypeBrandNew.getHeight()));
            transport.setReferWeight(this.getReferNumber(transport.getWeight(), machineTypeBrandNew.getWeight()));

            transport.setIsStandard(Constant.STANDARD_STATUS_STANDARD);

        } else {
            transport.setIsStandard(Constant.STANDARD_STATUS_PROBABILITY);
            transport.setMatchItemId(-1);
        }

        return matchResult;
    }

    @Override
    public ResultMsgBean supplementCarInfo(CarSaveBean carSaveBean, Long orderId, Integer type) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        // 判断当前数据是否已经添加过车辆信息
        TytTransportOrders tytTransportOrders = transportOrdersService.getById(orderId);
        TransportMain transportMain = transportMainService.getTransportMainForId(tytTransportOrders.getTsId());
        TytTransportBackend tytTransportBackend = transportBackendService.getByTsId(tytTransportOrders.getTsId());
        if (null == type || null == tytTransportOrders || null == transportMain || null == carSaveBean.getUserId() || StringUtils.isBlank(carSaveBean.getHeadNo()) || StringUtils.isBlank(carSaveBean.getHeadCity()) || StringUtils.isBlank(carSaveBean.getTailCity()) || StringUtils.isBlank(carSaveBean.getTailNo())) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("参数异常");
            return resultMsgBean;
        }

        if (StringUtils.isNotBlank(tytTransportOrders.getHeadNo()) || StringUtils.isNotBlank(tytTransportOrders.getHeadCity())) {

            //同时满足是限时货源和货主填写更换车辆
            if (tytTransportBackend != null && type == 2) {
                //判断订单是否已卸货/已取消，卸货/取消订单不允许更换车辆
                /*if(tytTransportBackend.getStatus() == 8){
                    resultMsgBean.setCode(ResultMsgBean.ERROR);
                    resultMsgBean.setMsg("订单已卸货，不允许更换车辆");
                    return resultMsgBean;
                }*/
                /*if(tytTransportBackend.getStatus() == 2){
                    resultMsgBean.setCode(ResultMsgBean.ERROR);
                    resultMsgBean.setMsg("货主已取消货源，不允许更换车辆");
                    return resultMsgBean;
                }*/
                //未卸货/取消订单，判断是否已更换条件
                if (tytTransportOrders.getCarReplace() == 1) {
                    resultMsgBean.setCode(ResultMsgBean.ERROR);
                    resultMsgBean.setMsg("每单只可更换一次车辆，您已使用~");
                    return resultMsgBean;
                } else {
                    tytTransportOrders.setCarReplace(1);

                    resultMsgBean.setExtraData(1);
                }
            } else {
                resultMsgBean.setCode(ResultMsgBean.ERROR);
                resultMsgBean.setMsg("已填写车辆信息，请刷新当前页面~");
                return resultMsgBean;
            }

        }
        // 判断调车数量，如果调车数量为 1 的话，就需要双向同步
        if (null != transportMain.getShuntingQuantity() && transportMain.getShuntingQuantity() == 1) {
            TransportDone transportDone = new TransportDone();
            transportDone.setHeadCity(carSaveBean.getHeadCity());
            transportDone.setHeadNo(carSaveBean.getHeadNo());
            transportDone.setTailCity(carSaveBean.getTailCity());
            transportDone.setTailNo(carSaveBean.getTailNo());
            transportDone.setCarId(carSaveBean.getCarId());
            transportDone.setTsId(tytTransportOrders.getTsId());
            transportDoneService.updateCarInfo(transportDone);
        }
        // 更新 tyt_transport_orders 表中的车辆信息
        tytTransportOrders.setHeadCity(carSaveBean.getHeadCity());
        tytTransportOrders.setHeadNo(carSaveBean.getHeadNo());
        tytTransportOrders.setTailCity(carSaveBean.getTailCity());
        tytTransportOrders.setTailNo(carSaveBean.getTailNo());
        tytTransportOrders.setCarId(carSaveBean.getCarId());
        Long carId = null;
        if (null == carSaveBean.getCarId() && type == 1) {
            // 调用车辆保存接口
            carSaveBean.setUserId(carSaveBean.getUserId());
            carSaveBean.setAuth("2");
            // V6000 版本将此项前移，由于程序不知道在支付信息费时，填写的车辆是否带爬梯，所以为未知
            carSaveBean.setHasLadder(2);
            //6440新增开票状态
            carSaveBean.setIsInvoice(3);
            try {
                carId = carService.saveCar(carSaveBean);
            } catch (Exception e) {
                logger.error("添加车辆信息异常", e);
            }
            tytTransportOrders.setCarId(carId);
        }
        if (null != tytTransportBackend) {
            tytTransportOrders.setIsDealCar(1);
        }
        transportOrdersService.updateCarInfo(tytTransportOrders);


        resultMsgBean.setData(carId);

        //如果是企业货源，而且第三方未取消，发送mq消息
        if (null != tytTransportBackend && tytTransportBackend.getStatus() != 2) {

            logger.info("企业货源更新车辆信息同步状态tsId:{}", tytTransportBackend.getSrcMsgId());
            sendMessage2MQ(tytTransportBackend, transportMain.getUserId());
            // 过度严密，做了一些数据库查询动作，暂时注释
//            List<OwnerCompanyRelation> ownerCompanyRelations = ownerCompanyRelationService.getOwnerCompanyRelationByUserId(tytTransportBackend.getReceiverUserId());
//            if (!CollectionUtils.isEmpty(ownerCompanyRelations)) {
//                sendMessage2MQ(tytTransportBackend, transportMain.getUserId());
//            }
        }
        return resultMsgBean;
    }

    @Override
    public ResultMsgBean supplementCarInfoForManage(CarSaveBean carSaveBean, Long orderId, Integer type) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        // 判断当前数据是否已经添加过车辆信息
        TytTransportOrders tytTransportOrders = transportOrdersService.getById(orderId);
        TransportMain transportMain = transportMainService.getTransportMainForId(tytTransportOrders.getTsId());
        TytTransportBackend tytTransportBackend = transportBackendService.getByTsId(tytTransportOrders.getTsId());
        if (null == type || null == tytTransportOrders || null == transportMain || null == carSaveBean.getUserId() || StringUtils.isBlank(carSaveBean.getHeadNo()) || StringUtils.isBlank(carSaveBean.getHeadCity()) || StringUtils.isBlank(carSaveBean.getTailCity()) || StringUtils.isBlank(carSaveBean.getTailNo())) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("参数异常");
            return resultMsgBean;
        }
        if (StringUtils.isNotBlank(tytTransportOrders.getHeadNo()) || StringUtils.isNotBlank(tytTransportOrders.getHeadCity())) {
            //同时满足是限时货源和货主填写更换车辆
            if (tytTransportBackend != null && type == 2) {
                //未卸货/取消订单，判断是否已更换条件
                tytTransportOrders.setCarReplace(1);
                resultMsgBean.setExtraData(1);
            }
        }
        // 判断调车数量，如果调车数量为 1 的话，就需要双向同步
        if (null != transportMain.getShuntingQuantity() && transportMain.getShuntingQuantity() == 1) {
            TransportDone transportDone = new TransportDone();
            transportDone.setHeadCity(carSaveBean.getHeadCity());
            transportDone.setHeadNo(carSaveBean.getHeadNo());
            transportDone.setTailCity(carSaveBean.getTailCity());
            transportDone.setTailNo(carSaveBean.getTailNo());
            transportDone.setCarId(carSaveBean.getCarId());
            transportDone.setTsId(tytTransportOrders.getTsId());
            transportDoneService.updateCarInfo(transportDone);
        }
        // 更新 tyt_transport_orders 表中的车辆信息
        tytTransportOrders.setHeadCity(carSaveBean.getHeadCity());
        tytTransportOrders.setHeadNo(carSaveBean.getHeadNo());
        tytTransportOrders.setTailCity(carSaveBean.getTailCity());
        tytTransportOrders.setTailNo(carSaveBean.getTailNo());
        tytTransportOrders.setCarId(carSaveBean.getCarId());
        Long carId = null;
        if (null == carSaveBean.getCarId() && type == 1) {
            // 调用车辆保存接口
            carSaveBean.setUserId(carSaveBean.getUserId());
            carSaveBean.setAuth("2");
            // V6000 版本将此项前移，由于程序不知道在支付信息费时，填写的车辆是否带爬梯，所以为未知
            carSaveBean.setHasLadder(2);
            try {
                carId = carService.saveCar(carSaveBean);
            } catch (Exception e) {
                logger.error("添加车辆信息异常", e);
            }
            tytTransportOrders.setCarId(carId);
        }
        if (null != tytTransportBackend) {
            tytTransportOrders.setIsDealCar(1);
        }
        transportOrdersService.updateCarInfo(tytTransportOrders);
        resultMsgBean.setData(carId);
        //如果是企业货源，而且第三方未取消，发送mq消息
        if (null != tytTransportBackend && tytTransportBackend.getStatus() != 2) {
            logger.info("企业货源更新车辆信息同步状态tsId:{}", tytTransportBackend.getSrcMsgId());
            sendMessage2MQ(tytTransportBackend, transportMain.getUserId());
        }
        return resultMsgBean;
    }

    public void sendMessage2MQ(TytTransportBackend backend, Long userId) {

        MqUserMsg mqUserMsg = new MqUserMsg();

        mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqUserMsg.setMessageType(MqBaseMessageBean.TRANSPORT_BACKEND_CAR);
        mqUserMsg.setUserId(backend.getId());
        if (backend != null) {
            mqUserMsg.setSrcMsgId(userId);
            String content = "山推订单号：【" + backend.getId() + "】的订单车辆信息已更新";
            mqUserMsg.setContent(content);
        }
        // 保存mq信息到数据库
        tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

        long mqDelayTime = tytConfigService.getIntValue(Constant.publish_mq_delay_time, 1500).longValue();

        logger.info("publish_mq_delay_time : " + mqDelayTime);

        // 发送修改车辆信息成功mq，通知山推
        tytMqMessageService.sendMqMessageTransportStatus(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqDelayTime);
    }

    @Override
    public ResultMsgBean supplementCarInfoForTransport(CarSaveBean carSaveBean, Long tsId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        TransportDone transportDone = transportDoneService.getByTsId(tsId);
        TransportMain transportMain = transportMainService.getTransportMainForId(tsId);
        TytTransportOrders tytTransportOrders = transportOrdersService.getByTsIdByTime(tsId);
        //TytTransportBackend tytTransportBackend = transportBackendService.getByTsId(tsId);
        if (null == tsId || null == transportDone || null == transportMain || null == tytTransportOrders || StringUtils.isBlank(carSaveBean.getHeadNo()) || StringUtils.isBlank(carSaveBean.getHeadCity()) || StringUtils.isBlank(carSaveBean.getTailCity()) || StringUtils.isBlank(carSaveBean.getTailNo())) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("参数异常");
        }
        if (StringUtils.isNotBlank(transportDone.getHeadCity()) || StringUtils.isNotBlank(transportDone.getHeadNo())) {
            resultMsgBean.setMsg("已经添加过车辆信息！");
            return resultMsgBean;
            //判断车辆信息不为空，更换车辆字段1为已更换，否则未更换过，变为已更换
            /*if(tytTransportOrders.getCarReplace() == 1){

            }else{
                tytTransportOrders.setCarReplace(1);
            }*/

        }
        if (null != transportMain.getShuntingQuantity() && transportMain.getShuntingQuantity() == 1) {
            tytTransportOrders.setHeadCity(carSaveBean.getHeadCity());
            tytTransportOrders.setHeadNo(carSaveBean.getHeadNo());
            tytTransportOrders.setTailCity(carSaveBean.getTailCity());
            tytTransportOrders.setTailNo(carSaveBean.getTailNo());
            tytTransportOrders.setCarId(carSaveBean.getCarId());
            transportOrdersService.updateCarInfo(tytTransportOrders);
        }
        transportDone.setHeadCity(carSaveBean.getHeadCity());
        transportDone.setHeadNo(carSaveBean.getHeadNo());
        transportDone.setTailCity(carSaveBean.getTailCity());
        transportDone.setTailNo(carSaveBean.getTailNo());
        transportDone.setCarId(carSaveBean.getCarId());
        transportDoneService.updateCarInfo(transportDone);
        /*//如果是企业货源，发送mq消息
        if(null != tytTransportBackend.getReceiverUserId()){
            List<OwnerCompanyRelation> ownerCompanyRelations = ownerCompanyRelationService.getOwnerCompanyRelationByUserId(tytTransportBackend.getReceiverUserId());
            if(!CollectionUtils.isEmpty(ownerCompanyRelations)){
                sendMessage2MQ(tytTransportBackend,transportMain.getUserId());
            }
        }*/
        return resultMsgBean;
    }

    @Override
    public ResultMsgBean checkTransportSimilarity(TransportCheckSimilarityBean transportCheckSimilarityBean) {

        try {

            Transport transport = new Transport();
            BeanUtils.copyProperties(transportCheckSimilarityBean, transport);
            transport.setCtime(new Timestamp(System.currentTimeMillis()));

            boolean checkResult = transportService.checkTransportSimilarity(transport);
            ;

            if (checkResult) {
                return new ResultMsgBean(ReturnCodeConstant.OK, "不是相似货源");
            }
            return new ResultMsgBean(ReturnCodeConstant.SIMILARITY_TRANSPORT, "该货物是相似货源");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "服务器错误");
        }
    }


    /**
     * 向MQ发送信息
     *
     * @param transId 货物ID
     */
    private void sendNullifyMessage2MQ(Long transId, BackendTransportBean backendTransportBean) {
        // 发送初货物无效信息MQ
        MqUserMsg mqUserMsg = new MqUserMsg();

        mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqUserMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_NULLIFY_TRANSPORT_MESSAGE);
        mqUserMsg.setUserId(transId);
        mqUserMsg.setTsId(transId);
//        if (backendTransportBean!=null&&backendTransportBean.getAppletsUserId()!=null){
//            String cellPhone = null;
//            mqUserMsg.setSrcMsgId(backendTransportBean.getMsgId());
//            try {
//                cellPhone = userService.getByUserId(backendTransportBean.getAppletsUserId()).getCellPhone();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            mqUserMsg.setCellPhone(cellPhone);
//            String content=backendTransportBean.getStartPoint()+"→"+backendTransportBean.getDestPoint()+backendTransportBean.getTaskContent()
//                    +"，已被【"+backendTransportBean.getReceiverShowName()+StringUtil.replaceMobile(backendTransportBean.getReceiverPhone())+"】接单。打开微信小程序“特运通货主版”查看详情";
//            mqUserMsg.setContent(content);
//        }
        // 保存mq信息到数据库
        // tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

        long mqDelayTime = tytConfigService.getIntValue(Constant.publish_mq_delay_time, 1500).longValue();

        logger.info("publish_mq_delay_time : " + mqDelayTime);

        // 发送货物无效信息
//        tytMqMessageService.sendMqMessageDirect(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqDelayTime);

        // 发送新TOPIC的消息
        tytMqMessageService.sendMsgCustom(JSON.toJSONString(mqUserMsg), "GOODS_CENTER_TOPIC", mqUserMsg.getMessageSerailNum(), "TRANSPORT_PUBLISH", mqDelayTime);

    }

    @Override
    public ResultMsgBean saveRePublish(long userId, long tsId, String clientVersion, String clientSign) throws Exception {

        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);

        Transport transport = transportBusiness.getByGoodsIdForUnLock(tsId);
        Long srcMsgId = null;
        if (transport != null) {
            srcMsgId = transport.getSrcMsgId();
            transport = transportService.getLastBySrcMygId(srcMsgId);
        }

        if (transport == null) {
            return ResultMsgBean.failResponse(ResponseEnum.sys_error.info("货源不存在或已过期！"));
        }

        //是否已过期
        boolean isYesterday = transport.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime();
        if (isYesterday) {
            return ResultMsgBean.failResponse(ResponseEnum.sys_error.info("该货源已过期！"));
        }

        tsId = transport.getId();
        String rePublishLockKey = "saveRePublish:" + srcMsgId;
        try {
            int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);

            if (LockUtil.lockObject("1", rePublishLockKey, redisLockTimeout)) {
                //校验是否可以置顶
                ResponseCodeEnum topAllowEnum = transportBusiness.transportAllowTop(transport, false);
                if (topAllowEnum != null) {
                    throw TytException.createException(topAllowEnum.info());
                }

                // 货源ID合法性验证

                boolean isYesterdayPublish = transport.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime();
                if (!isYesterdayPublish) {
                    // 增加撤销的默认原因
                    String backoutReasonKey = "直接发布撤销";
                    Integer backoutReasonValue = 0;

                    TransportStatusOption transportStatusOption = new TransportStatusOption(userId, 1, tsId, backoutReasonKey, backoutReasonValue, null, null, true, null, null);
                    transportStatusOption.setKeepExposure(true);

                    msgBean = transportBusiness.saveInfoFeeUpdateBtnStatusNew(transportStatusOption, new TransportDoneRequest());
                    if (ReturnCodeConstant.OK != msgBean.getCode()) {
                        return msgBean;
                    }
                }

                SaveDirectReq saveDirectReq = new SaveDirectReq();
                saveDirectReq.setUserId(userId);
                saveDirectReq.setClientVersion(clientVersion);
                saveDirectReq.setClientSign(clientSign);

                saveDirectReq.setGoodsId(tsId);
                saveDirectReq.setIsBackendTransport(0);

                msgBean = transportBusiness.saveGoodsDirectV5930(saveDirectReq, true, true, false, null);
                // 如果使用曝光卡，记录15分钟，用于找货大厅相似货源置顶判断
                // if (ReturnCodeConstant.OK == msgBean.getCode()) {
                //     RedisUtil.set("tyt:goods:exposure:valid:" + transport.getSrcMsgId(), System.currentTimeMillis() + "", 15 * 60);
                // }
                return msgBean;
            }
        } finally {
            if (transport != null) {
                logger.info("finally_unlock_src_msg_id : " + transport.getSrcMsgId());
                LockUtil.unLockObject("1", rePublishLockKey);
            }
        }
        return msgBean;
    }

    @Override
    public ResultMsgBean updatePublishType(long userId, long tsId, Integer publishType, String clientVersion, String clientSign, Integer isBackendTransport, String price) throws Exception {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(tsId)) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }

        BackoutReasonEnum reasonEnum = BackoutReasonEnum.change_phone_price;

        // 查询该信息是否存在
        Transport dbTransport = this.getByGoodsId(tsId);

        if (dbTransport == null) {
            return new ResultMsgBean(ReturnCodeConstant.change_fixed_price_error, "货源不存在");
        }
        TytTransportMainExtend mainExtend = tytTransportMainExtendMapper.getBySrcMsgId(dbTransport.getSrcMsgId());
        if (Integer.parseInt(clientSign) == Constant.ClientSignEnum.PC.code && mainExtend != null){
            if (null != dbTransport.getPublishGoodsType() && mainExtend.getClientFusion() != null && mainExtend.getClientFusion() == 1
                    && (dbTransport.getPublishGoodsType() == PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode()
                    || PublishGoodsTypeEnum.isExcellentGoods(dbTransport.getPublishGoodsType()))){
                return new ResultMsgBean(ReturnCodeConstant.TYPE_ERROR_CODE, "该货源享平台权益，请至APP编辑发布");
            }
        }


        Long srcMsgId = dbTransport.getSrcMsgId();
        String originalDbPrice = dbTransport.getPrice();
        TytTransportEnterpriseLog transportEnterpriseLog = null;
        if (publishType.equals(PublishTypeEnum.fixed.getCode())) {
            //转一口价
            reasonEnum = BackoutReasonEnum.change_fixed_price;

            BigDecimal infoFee = dbTransport.getInfoFee();
            if (StringUtils.isNotBlank(price)) {
                dbTransport.setPrice(price);
            }
            if (infoFee == null || originalDbPrice == null) {
                return new ResultMsgBean(ReturnCodeConstant.change_fixed_price_error, "一口价货源信息费与运费必填!");
            }

            Long orderId = transportOrdersService.checkUsefulOrderExist(srcMsgId);

            if (orderId != null) {
                //有订单
                return new ResultMsgBean(ReturnCodeConstant.change_fixed_price_error, "该货源已被车方支付，不可继续转为一口价货源!");
            }


            if (dbTransport.getInvoiceTransport() != null && dbTransport.getInvoiceTransport() == 1) {
                //开票货源转一口价时校验开票规则
                ResultMsgBean resultMsgBean = invoiceTransportService.checkInvoiceTransportParam(dbTransport.getUserId(), dbTransport.getDistance().length() > 2 ? dbTransport.getDistance().substring(0, dbTransport.getDistance().length() - 2) : dbTransport.getDistance(), dbTransport.getPrice(), dbTransport.getWeight());
                if (resultMsgBean.getCode() != ReturnCodeConstant.OK) {
                    return resultMsgBean;
                }
                 transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(dbTransport.getSrcMsgId());
                if(transportEnterpriseLog != null && Objects.equals(transportEnterpriseLog.getPaymentsType(),1) && StringUtils.isNotBlank(price)){
                    BigDecimal newPrice = new BigDecimal(price);
                    if(newPrice.compareTo(new BigDecimal((originalDbPrice))) < 0){
                        return new ResultMsgBean(ReturnCodeConstant.change_fixed_price_error, "运费不能小于当前运费，请修改!");

                    }
                }


            }

            if (StringUtils.isNotBlank(price)) {
                //如果转一口价的同时修改运费，则需校验运价
                TransportPublishBean publishBean = new TransportPublishBean();
                BeanUtils.copyProperties(dbTransport, publishBean);
                if (StringUtils.isNotEmpty(price)) {
                    publishBean.setPrice(price);
                }
                ResultMsgBean priceMsgBean = this.checkPriceAllow(publishBean);
                if (priceMsgBean != null) {
                    return priceMsgBean;
                }
            }
        }

        // 6680 优车2.0转电议需要拦截，是否有电议次数
        boolean needUseExcellentGoodsTele = false;
        if (dbTransport.getExcellentGoodsTwo().equals(2) && publishType.equals(PublishTypeEnum.tel.getCode())) {
            try {
                Response<Integer> response = thPriceClient.getRemainCount(userId).execute();
                log.info("查询是否允许发布优车2.0电议货源, 返回结果:{}", JSON.toJSONString(response.body()));
                if (response.isSuccessful()) {
                    Integer remainNum = response.body();
                    if (remainNum == null || remainNum <= 0) {
                        return new ResultMsgBean(ReturnCodeConstant.change_fixed_price_error, "您优车电议次数不足，无法转电议，请撤销再重发");
                    } else {
                        needUseExcellentGoodsTele = true;
                    }
                }
            } catch (IOException e) {
                log.info("查询是否允许发布优车2.0电议货源 异常", e);
            }
        }

        //先撤销货源
        String backoutReasonKey = reasonEnum.getMsg();
        Integer backoutReasonValue = reasonEnum.getCode();
        ResultMsgBean msgBean = transportBusiness.saveInfoFeeUpdateBtnStatusNew(userId, 1, srcMsgId, new TransportDoneRequest(), backoutReasonKey, backoutReasonValue, null, null, false, null, null);
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            return msgBean;
        }

        SaveDirectReq saveDirectReq = new SaveDirectReq();
        saveDirectReq.setUserId(userId);
        saveDirectReq.setClientVersion(clientVersion);
        saveDirectReq.setClientSign(clientSign);

        saveDirectReq.setGoodsId(tsId);
        saveDirectReq.setIsBackendTransport(isBackendTransport);
        saveDirectReq.setPublishType(publishType);
        saveDirectReq.setAutomaticGoodCarPriceTransportType(4);

        if (publishType.equals(PublishTypeEnum.fixed.getCode())) {
            //只有转一口价时才支持同步修改价格
            saveDirectReq.setPrice(price);
        }

        //再发转电议货源
        ResultMsgBean resultMsg = transportBusiness.saveGoodsDirectV5930(saveDirectReq, false, false, false, null);

        if (ReturnCodeConstant.OK == resultMsg.getCode()) {
            // 分段支付修改价格后，到付运费也要跟着修改
            if (dbTransport.getInvoiceTransport() != null && dbTransport.getInvoiceTransport() == 1 && transportEnterpriseLog != null
                    && Objects.equals(transportEnterpriseLog.getPaymentsType(), 1) && StringUtils.isNotBlank(price)) {
                BigDecimal subtract = new BigDecimal(price).subtract(new BigDecimal(originalDbPrice));
                transportEnterpriseLog.setCollectedPrice(transportEnterpriseLog.getCollectedPrice().add(subtract));

                invoiceTransportService.saveInvoiceTransportEnterpriseData(dbTransport.getUserId(), srcMsgId, transportEnterpriseLog.getInvoiceSubjectId()
                        , transportEnterpriseLog.getServiceProviderCode(), transportEnterpriseLog.getAssignCarTel(), transportEnterpriseLog.getEnterpriseTaxRate()
                        , transportEnterpriseLog.getConsigneeName(), transportEnterpriseLog.getConsigneeTel(), transportEnterpriseLog.getConsigneeEnterpriseName(),
                        transportEnterpriseLog.getPaymentsType(), transportEnterpriseLog.getPrepaidPrice(), transportEnterpriseLog.getCollectedPrice(), transportEnterpriseLog.getReceiptPrice());
            }

            //发送MQ货源打通满满编辑货源  转一口价或转电议
            TytTransportSyncYmm resultTransport = tytTransportSyncYmmService.findTransportSyncYmm(srcMsgId);
            if (null != resultTransport) {
                TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
                if (transportMain == null) {
                    throw TytException.createException(ResponseEnum.request_error.info("货物不存在"));
                }
                MqEditTransportSyncMsg syncMsg = new MqEditTransportSyncMsg();
                syncMsg.setPartnerSerialNo(IdUtils.getIncreaseIdByLocalTime());
                syncMsg.setTrackingMsg(resultTransport.getTrackingMsg());
                syncMsg.setCargoId(resultTransport.getCargoId());
                syncMsg.setTel(transportMain.getTel());
                syncMsg.setType(publishType);
                if (publishType.equals(PublishTypeEnum.fixed.getCode())) {
                    String dbPrice = transportMain.getPrice();
                    BigDecimal decimal = new BigDecimal(dbPrice);
                    syncMsg.setAfterPrice(Integer.parseInt(decimal.movePointRight(2).toString()));
                }
                syncMsg.setSrcMsgId(srcMsgId);
                syncMsg.setMessageType(MqBaseMessageBean.MB_SYNC_TRANSPORT_EDIT_MESSAGE);
                String messageSerailNum = SerialNumUtil.generateSeriaNum();
                syncMsg.setMessageSerailNum(messageSerailNum);
                tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(syncMsg), syncMsg.getMessageType());
                tytMqMessageService.sendMbMqMessage(messageSerailNum, JSON.toJSONString(syncMsg), syncMsg.getMessageType());
            }

            if (needUseExcellentGoodsTele) {
                try {
                    thPriceClient.saveTransferTeleUseCount(userId, dbTransport.getSrcMsgId()).execute();
                } catch (IOException e) {
                    log.info("保存用户发布优车2.0电议货源次数 异常", e);
                }
            }

        }
        // 转一口价记录发布日志
        if (Objects.equals(PublishTypeEnum.fixed.getCode(), publishType)) {
            transportPublishLogService.recordPublishLog(userId, srcMsgId, PubTypeEnum.TRANSFER_FIX_PRICE, null);
        }

        //记录价格变动
        transportPublishLogService.changePriceLog(srcMsgId, saveDirectReq.getPrice(), saveDirectReq.getPublishType(), 4);

        return resultMsg;
    }

    @Override
    public ResultMsgBean freightAddMoneyNum(long userId, long tsId, String addPriceReq, String clientVersion, String clientSign,
                                            Integer isBackendTransport, Integer forceUp, Integer requestSource, Integer operationType) throws Exception {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(tsId)) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }

        Transport byGoodsId = getByGoodsId(tsId);
        if (Objects.isNull(byGoodsId)) {
            return new ResultMsgBean(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "货源不存在");
        }

        Long srcMsgId = byGoodsId.getSrcMsgId();
        String dbPrice = byGoodsId.getPrice();

        SaveDirectOptEnum directOptEnum = SaveDirectOptEnum.fillPrice;
        PubTypeEnum pubTypeEnum = PubTypeEnum.FILL_PRICE;
        if (StringUtils.isNotBlank(dbPrice) && Integer.parseInt(addPriceReq) > 0) {
            //由于加价与设置价格为同一接口，当货源原来价格不为空时，并且加价金额大于零表示加价操作
            directOptEnum = SaveDirectOptEnum.addPrice;
            pubTypeEnum = PubTypeEnum.ADD_PRICE;
        }

        //加价时间间隔
        String addPriceTimeKey = RedisKeyConstant.getAddPriceTimeInterval(srcMsgId);
        if (directOptEnum.equals(SaveDirectOptEnum.addPrice)) {
            if (forceUp == null || forceUp == 0) {
                //校验时间差
                String addPriceValue = RedisUtil.get(addPriceTimeKey);

                if (StringUtils.isNotBlank(addPriceValue)) {
                    logger.info("transport_addPrice_too_busy in 10 min : " + srcMsgId);
                    return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "10分钟内已加价，请稍后再试");
                }
            }
        }

        //获取加价次数
        String addPriceCountKey = Constant.FREIGHT_ADD_MONEY_NUM + "_" + userId + "_" + byGoodsId.getSrcMsgId();
        String addMoneyNum = RedisUtil.get(addPriceCountKey);

        int addPriceCount = 0;
        if (StringUtils.isNotBlank(addMoneyNum)) {
            try {
                addPriceCount = Integer.parseInt(addMoneyNum);
            } catch (NumberFormatException e) {
                logger.error("", e);
            }
        }

        //是否消耗曝光卡
        boolean useExposure = true;
        //是否置顶
        boolean topFlag = false;
        boolean saveVary = false;

        if (directOptEnum.equals(SaveDirectOptEnum.addPrice)) {
            //加价时，不消耗曝光卡，并且置顶.
            useExposure = false;
            topFlag = true;
            saveVary = true;

            addPriceCount++;
        }

        if ((StringUtils.isNotBlank(dbPrice) && Integer.parseInt(addPriceReq) < 0) ||
                (StringUtils.isBlank(dbPrice) || Objects.equals(dbPrice, "0"))) {
            //如果加价金额小于0，则当填价处理，并且不消耗曝光卡，不置顶
            // 如果是无价货源同步报价，不曝光，不置顶
            useExposure = false;
            topFlag = false;
            saveVary = false;
        }

        /*
        去掉加价次数上限
        Integer intValue = tytConfigService.getIntValue(Constant.FREIGHT_ADD_MONEY_NUM, 3);
        if (StringUtils.isNotEmpty(addMoneyNum) && Integer.parseInt(addMoneyNum) >= intValue) {
            //不允许加价
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "加价次数已到上限");
        }
        */

        String totalReqPrice = null;
        if (StringUtils.isNotEmpty(addPriceReq)) {
            BigDecimal originalPrice = new BigDecimal(StringUtils.isNotEmpty(byGoodsId.getPrice()) ? byGoodsId.getPrice() : "0");
            totalReqPrice = originalPrice.add(new BigDecimal(addPriceReq)).toString();
        }

        //校验运价
        TransportPublishBean publishBean = new TransportPublishBean();
        BeanUtils.copyProperties(byGoodsId, publishBean);
        if (StringUtils.isNotEmpty(totalReqPrice)) {
            publishBean.setPrice(totalReqPrice);
        }
        ResultMsgBean priceMsgBean = this.checkPriceAllow(publishBean);
        if (priceMsgBean != null) {
            return priceMsgBean;
        }

        if (byGoodsId.getInvoiceTransport() != null && byGoodsId.getInvoiceTransport() == 1) {
            //开票货源填价或加价时校验开票规则
            ResultMsgBean resultMsgBean = invoiceTransportService.checkInvoiceTransportParam(byGoodsId.getUserId(), byGoodsId.getDistance().length() > 2 ? byGoodsId.getDistance().substring(0, byGoodsId.getDistance().length() - 2) : byGoodsId.getDistance(), publishBean.getPrice(), byGoodsId.getWeight());
            if (resultMsgBean != null && !resultMsgBean.isSuccess()) {
                return resultMsgBean;
            }
        }

        //先撤销货源
        String backoutReasonKey = "运费加价撤销";
        Integer backoutReasonValue = 7;
        ResultMsgBean msgBean = transportBusiness.saveInfoFeeUpdateBtnStatusNew(userId, 1, tsId, new TransportDoneRequest(), backoutReasonKey, backoutReasonValue, null, null, saveVary, null, null);
        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            return msgBean;
        }

        Integer publishType = null;
        // Integer userType = abtestService.getUserType(AbtestConstant.PRICE_TRANSPORT_UP, userId);
        // if (StringUtils.isBlank(clientVersion) || StringUtils.isBlank(clientSign) || Integer.parseInt(clientSign) == 1 || Integer.parseInt(clientVersion) < 6460 || userType == null || (userType == 1 && userId % 2 == 1)) {
        //     //老版本或者新版本展示老UI才有可能自动转一口价
        //     if (StringUtils.isBlank(byGoodsId.getPrice()) && !Objects.equals(SourceTypeEnum.运满满货源.getId(), byGoodsId.getSourceType())) {
        //         BigDecimal infoFee = byGoodsId.getInfoFee();
        //         Integer shuntingQuantity = byGoodsId.getShuntingQuantity();
        //         if (infoFee != null && shuntingQuantity == 1) {
        //             publishType = PublishTypeEnum.fixed.getCode();
        //         }
        //     }
        // }

        SaveDirectReq saveDirectReq = new SaveDirectReq();
        saveDirectReq.setUserId(userId);
        saveDirectReq.setClientVersion(clientVersion);
        saveDirectReq.setClientSign(clientSign);

        saveDirectReq.setGoodsId(tsId);
        saveDirectReq.setIsBackendTransport(isBackendTransport);
        saveDirectReq.setPublishType(publishType);
        saveDirectReq.setPrice(totalReqPrice);
        saveDirectReq.setDirectOptEnum(directOptEnum);
        saveDirectReq.setAddPriceCount(addPriceCount);
        saveDirectReq.setUseExposure(useExposure);
        saveDirectReq.setAutomaticGoodCarPriceTransportType(3);

        //再发布加价后货源
        msgBean = transportBusiness.saveGoodsDirectV5930(saveDirectReq, topFlag, saveVary, false, null);

        if (ReturnCodeConstant.OK != msgBean.getCode()) {
            return msgBean;
        }

        if (directOptEnum.equals(SaveDirectOptEnum.addPrice)) {
            //更新加价次数
            RedisUtil.set(addPriceCountKey, addPriceCount + "", (int) Constant.CACHE_EXPIRE_TIME_24H);
            //保存10分钟加价间隔
            RedisUtil.set(addPriceTimeKey, srcMsgId + "", 10 * 60);

            //记录加价信息
            TytTransportPriceUp tytTransportPriceUp = new TytTransportPriceUp();
            tytTransportPriceUp.setSrcMsgId(srcMsgId);
            tytTransportPriceUp.setUserId(userId);
            tytTransportPriceUp.setUpNumber(addPriceCount);
            BigDecimal addPrice = new BigDecimal(addPriceReq);
            tytTransportPriceUp.setUpPrice(addPrice);
            BigDecimal beforePrice = new BigDecimal(byGoodsId.getPrice());
            tytTransportPriceUp.setBeforePrice(beforePrice);
            tytTransportPriceUp.setAfterPrice(addPrice.add(beforePrice));
            tytTransportPriceUp.setCreateTime(new Date());
            tytTransportPriceUpService.addTytTransportPriceUpByAsync(tytTransportPriceUp);


            //发送加价mq
            //TODO: //发送加价mq
            sendAddMoneyMessage2MQ(srcMsgId, addPrice);

        }

        // 如果是加价报价，分段支付还要修改运费
        if (byGoodsId.getInvoiceTransport() != null && byGoodsId.getInvoiceTransport() == 1 && StringUtils.isNotBlank(addPriceReq)) {
            BigDecimal addPrice = new BigDecimal(addPriceReq);

            //这里只修改到付运费
            TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(byGoodsId.getSrcMsgId());
            if (transportEnterpriseLog != null && Objects.equals(transportEnterpriseLog.getPaymentsType(), 1) && transportEnterpriseLog.getCollectedPrice() != null) {
                transportEnterpriseLog.setCollectedPrice(transportEnterpriseLog.getCollectedPrice().add(addPrice));
                invoiceTransportService.saveInvoiceTransportEnterpriseData(byGoodsId.getUserId(), srcMsgId, transportEnterpriseLog.getInvoiceSubjectId()
                        , transportEnterpriseLog.getServiceProviderCode(), transportEnterpriseLog.getAssignCarTel(), transportEnterpriseLog.getEnterpriseTaxRate()
                        , transportEnterpriseLog.getConsigneeName(), transportEnterpriseLog.getConsigneeTel(), transportEnterpriseLog.getConsigneeEnterpriseName(),
                        transportEnterpriseLog.getPaymentsType(), transportEnterpriseLog.getPrepaidPrice(), transportEnterpriseLog.getCollectedPrice(), transportEnterpriseLog.getReceiptPrice());
            }
        }
        //发送MQ货源打通满满编辑货源 加价
        TytTransportSyncYmm resultTransport = tytTransportSyncYmmService.findTransportSyncYmm(byGoodsId.getSrcMsgId());
        TransportMain transportMain = transportMainService.getTransportMainForId(byGoodsId.getSrcMsgId());
        if (null != resultTransport) {
            if (transportMain == null) {
                throw TytException.createException(ResponseEnum.request_error.info("货物不存在"));
            }
            MqEditTransportSyncMsg syncMsg = new MqEditTransportSyncMsg();
            syncMsg.setPartnerSerialNo(IdUtils.getIncreaseIdByLocalTime());
            syncMsg.setTrackingMsg(resultTransport.getTrackingMsg());
            syncMsg.setCargoId(resultTransport.getCargoId());
            syncMsg.setTel(transportMain.getTel());
            BigDecimal addPrice = new BigDecimal(addPriceReq);
            if (StringUtils.isNotBlank(byGoodsId.getPrice())) {
                BigDecimal beforePrice = new BigDecimal(byGoodsId.getPrice());
                BigDecimal priceBig = addPrice.add(beforePrice);
                syncMsg.setAfterPrice(Integer.parseInt(priceBig.movePointRight(2).toString()));
                syncMsg.setType(0);
            } else {
                syncMsg.setType(transportMain.getPublishType());
                syncMsg.setAfterPrice(Integer.parseInt(addPrice.movePointRight(2).toString()));
            }
            syncMsg.setPublishType(transportMain.getPublishType());
            syncMsg.setSrcMsgId(byGoodsId.getSrcMsgId());
            syncMsg.setMessageType(MqBaseMessageBean.MB_SYNC_TRANSPORT_EDIT_MESSAGE);
            String messageSerailNum = SerialNumUtil.generateSeriaNum();
            syncMsg.setMessageSerailNum(messageSerailNum);
            tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(syncMsg), syncMsg.getMessageType());
            tytMqMessageService.sendMbMqMessage(messageSerailNum, JSON.toJSONString(syncMsg), syncMsg.getMessageType());
        }
        // 记录发布日志
        transportPublishLogService.recordPublishLog(userId, srcMsgId, pubTypeEnum, requestSource);

        if (operationType == 1) {
            //填价加价
            operationType = 3;
        } else if (operationType == 2) {
            //报价修改运费
            operationType = 5;
        } else if (operationType == 3) {
            //货源诊断
            operationType = 6;
        }
        //记录价格变动
        transportPublishLogService.changePriceLog(srcMsgId, saveDirectReq.getPrice(), transportMain.getPublishType(), operationType);

        return msgBean;
    }

    private enum PublisTypeEnum {
        新发布货物, 历史货物编辑发布, 当日货物编辑发布
    }

    /**
     * 货源撤销想mq发送消息
     *
     * @param srcMsgId
     * @param addPrice
     */
    private void sendAddMoneyMessage2MQ(Long srcMsgId, BigDecimal addPrice) {
        // 货源撤销发送mq
        MqTransportAddMoneyMsg mqTransportMoneyMsg = new MqTransportAddMoneyMsg();

        mqTransportMoneyMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqTransportMoneyMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_ADDMONEY_TRANSPORT_MESSAGE);
        mqTransportMoneyMsg.setSrcMsgId(srcMsgId);
        mqTransportMoneyMsg.setAddPrice(addPrice);
        // 保存mq信息到数据库
        tytMqMessageService.addSaveMqMessage(mqTransportMoneyMsg.getMessageSerailNum(), JSON.toJSONString(mqTransportMoneyMsg), mqTransportMoneyMsg.getMessageType());

        tytMqMessageService.sendMqMessage(mqTransportMoneyMsg.getMessageSerailNum(), JSON.toJSONString(mqTransportMoneyMsg), mqTransportMoneyMsg.getMessageType());

    }

    @Override
    public ResultMsgBean changeTecServiceFee(long tsId, BigDecimal tecServiceFee) throws Exception {
        logger.info("changeTecServiceFee【tsId:{}】,【tecServiceFee:{}】", tsId, tecServiceFee);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        Transport transport = getByGoodsId(tsId);
        if (Objects.isNull(transport)) {
            resultMsgBean.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
            resultMsgBean.setMsg("货源不存在");
            return resultMsgBean;
        }
        //当数据库中的技术服务费和输入的技术服务费一致直接返回
        BigDecimal dbTecServiceFee = transport.getTecServiceFee();
        if (tecServiceFee.compareTo(dbTecServiceFee) == 0) {
            return resultMsgBean;
        }
        //更新tyt_transport_main 和 tyt_transport表信息
        Long srcMsgId = transport.getSrcMsgId();
        transportMainService.updateTecServiceBySrcMsgId(srcMsgId, tecServiceFee);
        return resultMsgBean;
    }

    @Override
    public boolean checkTransportIsGoodCarPriceTransport(CheckGoodCarPriceTransportBean priceTransportBean) {
        logger.info("自动转优车货源，校验是否符合条件 {}", JSON.toJSONString(priceTransportBean));
        try {
            if (priceTransportBean.getPublishType() != null && StringUtils.isNotBlank(priceTransportBean.getPrice()) && StringUtil.isNumeric(priceTransportBean.getPrice()) && new BigDecimal(priceTransportBean.getPrice()).compareTo(new BigDecimal(0)) > 0) {

                BaseParameter baseParameter = new BaseParameter();
                baseParameter.setUserId(priceTransportBean.getUserId());

                TransportCarryBean transportCarryBean = (TransportCarryBean) priceTransportBean;

                ResultMsgBean showGoodCarPriceTransportTab = isShowGoodCarPriceTransportTab(baseParameter, transportCarryBean);
                if (showGoodCarPriceTransportTab != null && showGoodCarPriceTransportTab.isSuccess() && showGoodCarPriceTransportTab.getData() != null) {
                    GoodCarPriceTransportTabAndBIPriceVO goodCarPriceVO = (GoodCarPriceTransportTabAndBIPriceVO) showGoodCarPriceTransportTab.getData();
                    if (goodCarPriceVO != null && goodCarPriceVO.getShowTab()
                            && goodCarPriceVO.getFixPriceMin() != null
                            && new BigDecimal(priceTransportBean.getPrice()).compareTo(new BigDecimal(goodCarPriceVO.getFixPriceMin())) >= 0) {
                        // 6550新增判断，一口价自动转，有价电议：1.路线支持；2.开关开启所有用户自动转，关闭需要判断当前用户是否在AB测中
                        return priceTransportBean.getPublishType() == 2 // 一口价
                                // 有价电议
                                || (Objects.equals(goodCarPriceVO.getIsRouteSupportTeleNegotiation(), 1) // 路线支持
                                && (tytConfigService.getIntValue("excellent2_auto_tele_negotiation", 1) == 1 // 开关开启
                                || Objects.equals(goodCarPriceVO.getIsAllowTeleNegotiation(), 1))); // 或在AB测中
                    }
                }
            }
            return false;
        } catch (Exception e) {
            logger.error("非优车一口价货源符合条件的直接转优车定价货源条件校验失败 请求参数:{}", JSON.toJSONString(priceTransportBean), e);
            return false;
        }
    }

    @Override
    public ResultMsgBean isShowGoodCarPriceTransportTab(BaseParameter parameter, TransportCarryBean transportCarryBean) {
        logger.info("是否展示优车定价货源卡片，校验是否符合条件 {} : {}", JSON.toJSONString(parameter), JSON.toJSONString(transportCarryBean));
        GoodCarPriceTransportTabAndBIPriceVO goodCarPriceTransportTabAndBIPriceVO = new GoodCarPriceTransportTabAndBIPriceVO();
        goodCarPriceTransportTabAndBIPriceVO.setShowTab(false);

        if (transportCarryBean == null) {
            return ResultMsgBean.successResponse(goodCarPriceTransportTabAndBIPriceVO);
        }

        goodCarPriceTransportTabAndBIPriceVO.setDistance(transportCarryBean.getDistance());

        if (org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getStartProvince()) || org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getStartCity()) || org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getDestProvince()) || org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getDestCity()) || org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getDistance())) {
            return ResultMsgBean.successResponse(goodCarPriceTransportTabAndBIPriceVO);
        }

        //用户ID奇偶判断
        PublicResource showGoodCarPriceTransportTabConfig = publicResourceService.getByKey("show_good_car_price_transport_tab");
        if (showGoodCarPriceTransportTabConfig != null && org.apache.commons.lang.StringUtils.isNotBlank(showGoodCarPriceTransportTabConfig.getValue())) {
            int showGoodCarPriceTransportTab = Integer.parseInt(showGoodCarPriceTransportTabConfig.getValue());
            if ((showGoodCarPriceTransportTab == -1 || parameter.getUserId() == null) || (showGoodCarPriceTransportTab == 1 && parameter.getUserId() % 2 != 1) || (showGoodCarPriceTransportTab == 2 && parameter.getUserId() % 2 != 0)) {
                logger.info("优车定价货源 用户ID奇偶不符合条件 目前规则{} 请求参数 {}", showGoodCarPriceTransportTabConfig.getValue(), JSON.toJSONString(transportCarryBean));
                return ResultMsgBean.successResponse(goodCarPriceTransportTabAndBIPriceVO);
            }
        }

        // 修正地址
        buildAddress(transportCarryBean);

//        //路线是否在规定内判断
//        boolean checkExcellentGoodsPricingRoute = pricingRouteConfigService.checkExcellentGoodsPricingRoute(transportCarryBean.getStartProvince(), transportCarryBean.getStartCity(), transportCarryBean.getDestProvince(), transportCarryBean.getDestCity());
//        if (!checkExcellentGoodsPricingRoute) {
//            logger.info("优车定价货源 出发地目的地不符合路线配置 {}", JSON.toJSONString(transportCarryBean));
//            return ResultMsgBean.successResponse(goodCarPriceTransportTabAndBIPriceVO);
//        }

        //获取优车运价货源建议价
        log.info("判断是否展示优车定价卡片，获取优车定价建议价请求参数:{}", JSONObject.toJSONString(transportCarryBean));
        CarryPriceVo carryPriceVo = excellentPriceConfigService.getThPrice(transportCarryBean);


        if (carryPriceVo != null) {
            Optional.of(carryPriceVo).ifPresent(vo -> {
                if (vo.getFixPriceMin() != null && vo.getFixPriceMax() != null && vo.getFixPriceFast() != null && vo.getFixPriceMin() != 0 && vo.getFixPriceMax() != 0 && vo.getFixPriceFast() != 0) {
                    goodCarPriceTransportTabAndBIPriceVO.setFixPriceMin(vo.getFixPriceMin());
                    goodCarPriceTransportTabAndBIPriceVO.setFixPriceMax(vo.getFixPriceMax());
                    goodCarPriceTransportTabAndBIPriceVO.setFixPriceFast(vo.getFixPriceFast());
                    goodCarPriceTransportTabAndBIPriceVO.setThMaxPrice(vo.getThMaxPrice());
                    goodCarPriceTransportTabAndBIPriceVO.setThMinPrice(vo.getThMinPrice());
                    goodCarPriceTransportTabAndBIPriceVO.setIsAllowTeleNegotiation(vo.getIsAllowTeleNegotiation());
                    goodCarPriceTransportTabAndBIPriceVO.setIsRouteSupportTeleNegotiation(vo.getIsRouteSupportTeleNegotiation());
                    goodCarPriceTransportTabAndBIPriceVO.setShowTab(true);
                }
            });
        }

        return ResultMsgBean.successResponse(goodCarPriceTransportTabAndBIPriceVO);
    }

    /**
     * 修正客户端地址与后台路线地址匹配
     *
     * @param transportCarryBean
     */
    @Override
    public void buildAddress(TransportCarryBean transportCarryBean) {
        TytCity startCity = cityDataService.getShortCityName(transportCarryBean.getStartCity());
        if (startCity != null) {
            transportCarryBean.setStartProvince(startCity.getProvinceName());
            transportCarryBean.setStartCity(startCity.getCityName());
        } else {
            transportCarryBean.setStartProvince(transportCarryBean.getStartProvince().replace("省", "").replace("市", ""));
        }

        TytCity destCity = cityDataService.getShortCityName(transportCarryBean.getDestCity());
        if (destCity != null) {
            transportCarryBean.setDestProvince(destCity.getProvinceName());
            transportCarryBean.setDestCity(destCity.getCityName());
        } else {
            transportCarryBean.setDestProvince(transportCarryBean.getDestProvince().replace("省", "").replace("市", ""));
        }

    }

    /**
     * @return boolean
     * <AUTHOR> Lion
     * @Description TransportCarryBean参数校验
     * @Param [transportCarryBean, rm]
     * @Date 2022/7/28 17:03
     */
    @Override
    public boolean checkParameter(TransportCarryBean transportCarryBean) {
        if (org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getStartProvince())) {
            return false;
        }
        if (org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getStartCity())) {
            return false;
        }
        if (org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getDestProvince())) {
            return false;
        }
        if (org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getDestCity())) {
            return false;
        }
        if (org.apache.commons.lang.StringUtils.isBlank(transportCarryBean.getGoodsName())) {
            return false;
        }
        transportCarryBean.setStartCity(transportCarryBean.getStartCity().equals(transportCarryBean.getStartProvince()) ? transportCarryBean.getStartCity() + "市" : transportCarryBean.getStartCity());
        transportCarryBean.setDestCity(transportCarryBean.getDestCity().equals(transportCarryBean.getDestProvince()) ? transportCarryBean.getDestCity() + "市" : transportCarryBean.getDestCity());
        return true;
    }

    @Override
    public TytTecServiceFeeConfigToComputResult computeTecServiceFeeBtTransportData(Transport tytTransport, TransportMain oldTransportMain, MeetCommissionRulesResult meetCommissionRules, boolean isGoodCarPriceTransport) {
        //调用BI接口判断该货源是否抽佣，如果抽佣则通过配置计算技术服务费，并给该货源打上抽佣货源标识
        boolean isCommissionTransport = false;
        if (tytTransport.getRefundFlag() == null) {
            return null;
        }

        Long firstPublishTimeMinute = null;
        if (oldTransportMain != null && oldTransportMain.getId() != null && oldTransportMain.getCtime() != null) {
            //判断oldTransportMain.getCtime()是不是今天
            LocalDate ctimeLocalDate = oldTransportMain.getCtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (ctimeLocalDate.equals(LocalDate.now()) && oldTransportMain.getStatus() != null && oldTransportMain.getStatus() != 4) {
                firstPublishTimeMinute = Duration.between(oldTransportMain.getCtime().toInstant(), new Date().toInstant()).toMinutes();
            } else {
                //不是当天，则时间差为0
                firstPublishTimeMinute = 0L;
            }
        }

        TytTecServiceFeeConfig tytTecServiceFeeConfig = new TytTecServiceFeeConfig();

        //判断货源是否为专车
        boolean isSpecialCar = false;
        if (tytTransport.getExcellentGoods() != null && tytTransport.getExcellentGoods() == 2) {
            isSpecialCar = true;
        }

        //判断货源是否为YMM
        boolean isYMM = false;
        if (tytTransport.getSourceType().equals(SourceTypeEnum.运满满货源.getId())) {
            isYMM = true;
        }

        //判断货源是否为优车1.0
        boolean isExcellentGoods = false;
        if (tytTransport.getExcellentGoods() != null && tytTransport.getExcellentGoods() == 1) {
            isExcellentGoods = true;
        }

        TransportLabelJson transportLabelJson = transportBusiness.getTransportLabelJson(tytTransport.getLabelJson());

        if (isSpecialCar) {
            tytTecServiceFeeConfig.setApplyTransportType(1);
            if (tytTransport.getCargoOwnerId() == null || tytTransport.getCargoOwnerId() == 1 || tytTransport.getCargoOwnerId() == 0) {
                //专车签约合作商为平台
                tytTecServiceFeeConfig.setSpecialCarCooperativeType(1);
            } else {
                tytTecServiceFeeConfig.setSpecialCarCooperativeType(2);
            }
        } else if (isYMM) {
            tytTecServiceFeeConfig.setApplyTransportType(5);
        } else if (isGoodCarPriceTransport) {
            tytTecServiceFeeConfig.setApplyTransportType(4);
        } else if (isExcellentGoods) {
            tytTecServiceFeeConfig.setApplyTransportType(3);
        } else {
            //普通货源
            tytTecServiceFeeConfig.setApplyTransportType(2);
        }

        tytTecServiceFeeConfig.setRefundFlagType(tytTransport.getRefundFlag() == 0 ? 2 : 1);
        if (tytTransport.getPublishType() == PublishTypeEnum.tel.getCode().shortValue()) {
            if (StringUtils.isNotBlank(tytTransport.getPrice()) && !tytTransport.getPrice().equals("0")) {
                //电议有价
                tytTecServiceFeeConfig.setPricePublishType(1);
            } else {
                //电议无价
                tytTecServiceFeeConfig.setPricePublishType(2);
            }
        } else {
            //一口价
            tytTecServiceFeeConfig.setPricePublishType(3);
        }

        //调用BI接口获取抽佣分数
        BigDecimal commissionScore = new BigDecimal("-1");
        if (StringUtils.isNotBlank(tytTransport.getPrice()) && new BigDecimal(tytTransport.getPrice()).compareTo(BigDecimal.ZERO) > 0) {
            commissionScore = transportBusiness.checkCommissionScore(tytTransport);
            if (commissionScore == null || commissionScore.compareTo(BigDecimal.ZERO) < 0) {
                commissionScore = new BigDecimal("-1");
            }
        }
        log.info("抽佣金额计算-根据货源信息获取抽佣分数BI返回结果：{}", commissionScore);

        BigDecimal checkScore;
        if (StringUtils.isNotBlank(tytTransport.getPrice()) && new BigDecimal(tytTransport.getPrice()).compareTo(BigDecimal.ZERO) > 0) {
            checkScore = commissionScore;
        } else {
            if (transportLabelJson != null) {
                checkScore = transportLabelJson.getGoodsModelScore();
            } else {
                checkScore = null;
            }
        }
        log.info("抽佣金额计算-根据分数判断落在哪个阶梯：{}", checkScore);

        DrawCommissionReq drawCommissionReq = new DrawCommissionReq();
        drawCommissionReq.setSourceType(tytTransport.getSourceType());
        drawCommissionReq.setExcellentGoods(tytTransport.getExcellentGoods());
        drawCommissionReq.setGoodCarPriceTransport(isGoodCarPriceTransport ? 1 : 0);
        drawCommissionReq.setPublishType(tytTransport.getPublishType());
        drawCommissionReq.setHavePrice(StringUtils.isNotBlank(tytTransport.getPrice()) && !tytTransport.getPrice().equals("0") ? 1 : 0);
        drawCommissionReq.setRefundFlag(tytTransport.getRefundFlag());
        drawCommissionReq.setCommissionTime(new Date());
        drawCommissionReq.setUserId(tytTransport.getUserId());
        drawCommissionReq.setInvoiceTransport(tytTransport.getInvoiceTransport());
        drawCommissionReq.setGoodsModelScore(checkScore);

        CommissionTypeBean commissionTypeBean = this.checkCommission(drawCommissionReq, tytTransport);
        log.info("checkCommission:{}", JSONObject.toJSONString(commissionTypeBean));
        if (commissionTypeBean != null && commissionTypeBean.getDrawCommission() != null) {
            meetCommissionRules.setMeetCommissionRules(commissionTypeBean.getMeetCommissionRules());
            if (commissionTypeBean.getDrawCommission() == 1) {
                isCommissionTransport = true;
            }
        }

        if (!isCommissionTransport) {
            return null;
        }

        //无价货源优车指导价
        String goodCarPriceTransportCarryPrice = null;

        //默认技术服务费
        Integer defaultTecServiceFee = tytConfigService.getIntValue("default_tec_service_fee", 50);
        String priceString = tytTransport.getPrice();
        if (StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) == 0) {
            //使用优车指导价
            priceString = makePriceByGoodCarPriceTransportCarryPrice(tytTransport);
            if (StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) == 0) {
                //用户没有手填运费并且也没有通过BI接口获取到建议运费
                defaultTecServiceFee = tytConfigService.getIntValue("default_tec_service_fee_no_price", 20);
            } else {
                goodCarPriceTransportCarryPrice = priceString;
            }
        }
        log.info("抽佣金额计算-本次发货计算抽佣金额时用的运费为：{}", priceString);

        //抽佣金额计算抹零方式 0：对10向下取整；1:向下取整
        Integer tecServiceFeeRoundingType = tytConfigService.getIntValue("tec_service_fee_rounding_type", 0);

        TytTecServiceFeeConfig tecServiceFeeConfig = tytTecServiceFeeConfigMapper.getByConditionsUseToCompute(tytTecServiceFeeConfig);
        if (tecServiceFeeConfig != null) {
            TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = new TytTecServiceFeeConfigToComputResult();
            BeanUtils.copyProperties(tecServiceFeeConfig, tytTecServiceFeeConfigToComputResult);
            tytTecServiceFeeConfigToComputResult.setUseCommissionScoreStageConfig(true);
            tytTecServiceFeeConfigToComputResult.setCommissionScore(commissionScore);
            tytTecServiceFeeConfigToComputResult.setGoodCarPriceTransportCarryPrice(goodCarPriceTransportCarryPrice);

            //满足条件一的时间，如果不满足则为null
            Date matchConditionEarliestTime = null;
            if (oldTransportMain != null && oldTransportMain.getSrcMsgId() != null) {
                matchConditionEarliestTime = getMatchConditionLastTime(oldTransportMain
                        , tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount(), tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount());
            } else {
                if ((tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount() != null && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount() == 0)
                        || (tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount() != null && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount() == 0)) {
                    //当日新发货源并且查看拨打配置有0的，则满足条件一的时间为当前发货时间
                    matchConditionEarliestTime = new Date();
                }
            }

            //未满足条件一的情况下是否符合直接免佣条件
            boolean overTimeToFree = false;
            if (matchConditionEarliestTime == null) {
                overTimeToFree = checkIsNeedFreeTecServiceFeeByTime(tytTecServiceFeeConfigToComputResult, firstPublishTimeMinute);
                if (overTimeToFree) {
                    List<Integer> needFreeTecTypeList = new ArrayList<>();
                    needFreeTecTypeList.add(5);
                    tytTecServiceFeeConfigToComputResult.setNeedFreeTecTypeList(needFreeTecTypeList);
                }
            }

            boolean matchConditionToFree = checkIsNeedFreeTecServiceFeeByTransport(tytTransport, isGoodCarPriceTransport, tytTecServiceFeeConfigToComputResult);
            boolean freeTec = overTimeToFree || matchConditionToFree;

            //根据是否使用抽佣分数获取不同类型的分段配置
            List<TytTecServiceFeeStageConfig> stageConfigs = tytTecServiceFeeStageConfigMapper.getByConfigIdAndType(tecServiceFeeConfig.getId(), 2);

            if (CollectionUtils.isNotEmpty(stageConfigs) && StringUtils.isNotBlank(priceString) && new BigDecimal(priceString).compareTo(BigDecimal.ZERO) > 0 && checkScore != null) {
                BigDecimal tecServiceFee = new BigDecimal(defaultTecServiceFee);
                BigDecimal tecServiceFeeBeforeDiscount = new BigDecimal(defaultTecServiceFee);
                BigDecimal price = new BigDecimal(priceString);
                for (TytTecServiceFeeStageConfig stageConfig : stageConfigs) {
                    if (stageConfig.getPriceMin() != null && stageConfig.getPriceMax() != null
                            && checkScore.compareTo(stageConfig.getPriceMin()) > 0 && checkScore.compareTo(stageConfig.getPriceMax()) <= 0) {

                        //取百分比用新方法
                        List<TytTecServiceFeeProportionConfig> proportionConfig = tytTecServiceFeeProportionConfigMapper.getTytTecServiceFeeProportionConfigByStageId(stageConfig.getId());
                        int transportProportionNum = ThreadLocalRandom.current().nextInt(100, 200);
                        if (oldTransportMain != null && oldTransportMain.getSrcMsgId() != null) {
                            String transportNumString = RedisUtil.get(COMMISSION_PROPORTION_TRANSPORT_NUM + ":" + oldTransportMain.getSrcMsgId());
                            if (StringUtils.isNotBlank(transportNumString) && StringUtils.isNumeric(transportNumString.trim())) {
                                transportProportionNum = Integer.parseInt(transportNumString);
                            }
                        }
                        tytTecServiceFeeConfigToComputResult.setTransportProportionNum(transportProportionNum);
                        TytTecServiceFeeProportionConfig serviceFeeProportionConfig = null;
                        BigDecimal tecServiceFeeRate = BigDecimal.ZERO;
                        if (CollectionUtils.isNotEmpty(proportionConfig)) {
                            serviceFeeProportionConfig = proportionConfig.get(transportProportionNum % proportionConfig.size());
                            if (serviceFeeProportionConfig.getTecServiceFeeRate() != null && serviceFeeProportionConfig.getTecServiceFeeRate().compareTo(BigDecimal.ZERO) > 0) {
                                tecServiceFeeRate = serviceFeeProportionConfig.getTecServiceFeeRate();
                            }
                        }

                        //在配置的运费高低区间内，做开右闭
                        BigDecimal tecServiceFeeSnap = price.multiply(tecServiceFeeRate.movePointLeft(2)).setScale(2, RoundingMode.DOWN);


                        if (stageConfig.getTecServiceFeeMin() != null && stageConfig.getTecServiceFeeMax() != null) {
                            if (tecServiceFeeSnap.compareTo(stageConfig.getTecServiceFeeMin()) < 0) {
                                tecServiceFeeSnap = stageConfig.getTecServiceFeeMin();
                            } else if (tecServiceFeeSnap.compareTo(stageConfig.getTecServiceFeeMax()) > 0) {
                                tecServiceFeeSnap = stageConfig.getTecServiceFeeMax();
                            }
                        }

                        //算最终折扣前抹零，并记录折前价格
                        if (tecServiceFeeRoundingType == 0) {
                            tecServiceFeeSnap = tecServiceFeeSnap.divide(new BigDecimal(10), 0, RoundingMode.DOWN).multiply(new BigDecimal(10));
                        } else {
                            tecServiceFeeSnap = tecServiceFeeSnap.setScale(0, RoundingMode.DOWN);
                        }
                        BigDecimal tecServiceFeeAfterDiscountSnap = tecServiceFeeSnap;

                        TytTecServiceFeeDiscountConfig tytTecServiceFeeDiscountConfig = null;
                        //最终打折节点，记录折前价
                        if (matchConditionEarliestTime != null && serviceFeeProportionConfig != null && serviceFeeProportionConfig.getId() != null) {
                            tecServiceFeeAfterDiscountSnap = new BigDecimal(tecServiceFeeSnap.toString());

                            //取折扣用新方法
                            long matchConditionEarliestTimeBetweenNowMin = Duration.between(matchConditionEarliestTime.toInstant(), new Date().toInstant()).toMinutes();
                            tytTecServiceFeeDiscountConfig = tytTecServiceFeeDiscountConfigMapper.getTytTecServiceFeeDiscountConfigByProportionIdAndMin(serviceFeeProportionConfig.getId(), matchConditionEarliestTimeBetweenNowMin);
                            if (tytTecServiceFeeDiscountConfig != null && tytTecServiceFeeDiscountConfig.getDiscount() != null) {
                                tecServiceFeeSnap = tecServiceFeeSnap.multiply(tytTecServiceFeeDiscountConfig.getDiscount().movePointLeft(1)).setScale(2, RoundingMode.DOWN);
                                if (tytTecServiceFeeDiscountConfig.getDiscount().compareTo(BigDecimal.ZERO) == 0) {
                                    if (tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList() != null) {
                                        tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList().add(5);
                                    } else {
                                        List<Integer> needFreeTecTypeList = new ArrayList<>();
                                        needFreeTecTypeList.add(5);
                                        tytTecServiceFeeConfigToComputResult.setNeedFreeTecTypeList(needFreeTecTypeList);
                                    }
                                    freeTec = true;
                                }
                            }
                        }

                        //算完最终折后价后再次抹零
                        if (tecServiceFeeRoundingType == 0) {
                            tecServiceFeeSnap = tecServiceFeeSnap.divide(new BigDecimal(10), 0, RoundingMode.DOWN).multiply(new BigDecimal(10));
                        } else {
                            tecServiceFeeSnap = tecServiceFeeSnap.setScale(0, RoundingMode.DOWN);
                        }

                        tecServiceFee = tecServiceFeeSnap;
                        tecServiceFeeBeforeDiscount = tecServiceFeeAfterDiscountSnap;

                        tytTecServiceFeeConfigToComputResult.setPriceMax(stageConfig.getPriceMax());
                        tytTecServiceFeeConfigToComputResult.setPriceMin(stageConfig.getPriceMin());
                        tytTecServiceFeeConfigToComputResult.setTecServiceFeeMax(stageConfig.getTecServiceFeeMax());
                        tytTecServiceFeeConfigToComputResult.setTecServiceFeeMin(stageConfig.getTecServiceFeeMin());
                        tytTecServiceFeeConfigToComputResult.setTecServiceFeeRate(tecServiceFeeRate);
                        if (tytTecServiceFeeDiscountConfig != null) {
                            tytTecServiceFeeConfigToComputResult.setDiscountTime(tytTecServiceFeeDiscountConfig.getDiscountTime());
                            tytTecServiceFeeConfigToComputResult.setDiscount(tytTecServiceFeeDiscountConfig.getDiscount());
                        }

                        if (serviceFeeProportionConfig != null && serviceFeeProportionConfig.getId() != null) {
                            List<TytTecServiceFeeDiscountConfig> tytTecServiceFeeDiscountConfigByProportionId = tytTecServiceFeeDiscountConfigMapper.getTytTecServiceFeeDiscountConfigByProportionId(serviceFeeProportionConfig.getId());
                            if (CollectionUtils.isNotEmpty(tytTecServiceFeeDiscountConfigByProportionId)) {
                                tytTecServiceFeeConfigToComputResult.setDiscountConfig(JSON.toJSONString(tytTecServiceFeeDiscountConfigByProportionId));

                                //构造BI要的整体折扣信息例如：60,8.0,120,5.0,1440,0.0
                                StringBuilder allDiscount = new StringBuilder();
                                for (TytTecServiceFeeDiscountConfig tecServiceFeeDiscountConfig : tytTecServiceFeeDiscountConfigByProportionId) {
                                    allDiscount.append(tecServiceFeeDiscountConfig.getDiscountTime()).append(",").append(tecServiceFeeDiscountConfig.getDiscount()).append(",");
                                }
                                if (allDiscount.length() > 0) {
                                    allDiscount.deleteCharAt(allDiscount.length() - 1);
                                }
                                tytTecServiceFeeConfigToComputResult.setAllDiscount(allDiscount.toString());
                            }
                        }
                    }
                }
                if (freeTec) {
                    //超时免佣
                    tecServiceFee = new BigDecimal(0);
                }
                tytTecServiceFeeConfigToComputResult.setTecServiceFee(tecServiceFee);
                tytTecServiceFeeConfigToComputResult.setTecServiceFeeBeforeDiscount(tecServiceFeeBeforeDiscount);
            } else {
                tytTecServiceFeeConfigToComputResult.setTecServiceFeeBeforeDiscount(new BigDecimal(defaultTecServiceFee));
                if (freeTec) {
                    //超时免佣
                    tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(0));
                } else {
                    tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(defaultTecServiceFee));
                }
            }
            return tytTecServiceFeeConfigToComputResult;
        } else {
            TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = new TytTecServiceFeeConfigToComputResult();
            tytTecServiceFeeConfigToComputResult.setCarMemberType(0);
            tytTecServiceFeeConfigToComputResult.setPrivacyPhoneType(1);
            tytTecServiceFeeConfigToComputResult.setFreeTecServiceFeeType(1);
            tytTecServiceFeeConfigToComputResult.setFreeTecServiceFeeTime(60);
            tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(defaultTecServiceFee));
            tytTecServiceFeeConfigToComputResult.setUseCommissionScoreStageConfig(true);
            tytTecServiceFeeConfigToComputResult.setCommissionScore(commissionScore);
            tytTecServiceFeeConfigToComputResult.setTecServiceFeeBeforeDiscount(new BigDecimal(defaultTecServiceFee));

            if (tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeType() != null && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime() != null && firstPublishTimeMinute != null && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeType() == 1 && firstPublishTimeMinute > tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime()) {
                tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(0));
                List<Integer> needFreeTecTypeList = new ArrayList<>();
                needFreeTecTypeList.add(5);
                tytTecServiceFeeConfigToComputResult.setNeedFreeTecTypeList(needFreeTecTypeList);
            }

            boolean matchConditionToFree = checkIsNeedFreeTecServiceFeeByTransport(tytTransport, isGoodCarPriceTransport, tytTecServiceFeeConfigToComputResult);
            if (matchConditionToFree) {
                tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(0));
            }

            return tytTecServiceFeeConfigToComputResult;
        }
    }

    private Date getMatchConditionLastTime(TransportMain oldTransport, Integer freeTecServiceFeeViewCount, Integer freeTecServiceFeeCallCount) {
        if ((freeTecServiceFeeViewCount != null && freeTecServiceFeeViewCount == 0) || (freeTecServiceFeeCallCount != null && freeTecServiceFeeCallCount == 0)) {
            LocalDate ctimeLocalDate = oldTransport.getCtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (ctimeLocalDate.equals(LocalDate.now())) {
                return oldTransport.getCtime();
            } else {
                return new Date();
            }
        }
        //今日货源重发，并且条件一没有配置0的
        Date matchConditionLastViewTime = null;
        Date matchConditionLastCallTime = null;

        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime tomorrowStart = todayStart.plusDays(1);
        Date todayStartDate = asDate(todayStart);
        Date tomorrowStartDate = asDate(tomorrowStart);

        if (freeTecServiceFeeViewCount != null) {
            //获取符合条件的最近一次查看时间
            matchConditionLastViewTime = tytTecServiceFeeConfigMapper.getMatchConditionLastViewTime(oldTransport.getSrcMsgId(), freeTecServiceFeeViewCount - 1, todayStartDate, tomorrowStartDate);
        }
        if (freeTecServiceFeeCallCount != null) {
            //获取符合条件的最近一次通话时间
            matchConditionLastCallTime = tytTecServiceFeeConfigMapper.getMatchConditionLastCallTime(oldTransport.getSrcMsgId(), freeTecServiceFeeCallCount - 1, todayStartDate, tomorrowStartDate);
        }
        //取这两个时间最早的那个作为满足条件一的时间
        return getEarliestDate(matchConditionLastViewTime, matchConditionLastCallTime);
    }

    @Override
    public boolean checkIsNeedFreeTecServiceFeeByTransport(Transport tytTransport, boolean isGoodCarPriceTransport, TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult) {
        //专车非平台货源不走这些免佣逻辑
        if (tytTransport.getExcellentGoods() != null && tytTransport.getExcellentGoods() == 2 && tytTransport.getCargoOwnerId() != null && tytTransport.getCargoOwnerId() != 1) {
            log.info("判断是否符合发货直接免佣条件 专车非平台不走这些免佣条件");
            return false;
        }
        CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeVO = checkIsNeedFreeTecSericeFeeByTransport(tytTransport.getUserId(), tytTransport.getStartCity(), isGoodCarPriceTransport);
        if (checkIsNeedFreeTecServiceFeeVO != null && checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec() != null && checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec()) {
            if (CollectionUtils.isNotEmpty(tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList())) {
                tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList().addAll(checkIsNeedFreeTecServiceFeeVO.getNeedFreeTecTypeList());
            } else {
                tytTecServiceFeeConfigToComputResult.setNeedFreeTecTypeList(checkIsNeedFreeTecServiceFeeVO.getNeedFreeTecTypeList());
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean checkIsNeedFreeTecServiceFeeByTime(TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult, Long firstPublishTimeMinute) {
        //新超时免佣配置存在,看看是不是旧货源重发
        if (tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime() != null && firstPublishTimeMinute != null) {
            if (tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime() <= 0) {
                return true;
            }
            return firstPublishTimeMinute > Long.valueOf(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime());
        }
        return false;
    }

    private Date getEarliestDate(Date matchConditionLastViewTime, Date matchConditionLastCallTime) {
        return Optional.ofNullable(matchConditionLastViewTime).map(viewTime -> Optional.ofNullable(matchConditionLastCallTime).filter(callTime -> !viewTime.before(callTime)).orElse(viewTime)).orElse(matchConditionLastCallTime);
    }

    public Date asDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private String makePriceByBIGetCarryPrice(Transport tytTransport) {
        CarryPriceReq carryPriceReq = new CarryPriceReq();
        carryPriceReq.setStartProvince(tytTransport.getStartProvinc());
        carryPriceReq.setStartCity(tytTransport.getStartCity());
        carryPriceReq.setStartArea(tytTransport.getStartArea());
        carryPriceReq.setDestProvince(tytTransport.getDestProvinc());
        carryPriceReq.setDestCity(tytTransport.getDestCity());
        carryPriceReq.setDestArea(tytTransport.getDestArea());
        carryPriceReq.setGoodsName(tytTransport.getTaskContent());
        carryPriceReq.setGoodsWeight(NumberConvertUtil.strToDouble(tytTransport.getWeight()));
        carryPriceReq.setGoodsLength(NumberConvertUtil.strToDouble(tytTransport.getLength()));
        carryPriceReq.setGoodsWide(NumberConvertUtil.strToDouble(tytTransport.getWide()));
        carryPriceReq.setGoodsHigh(NumberConvertUtil.strToDouble(tytTransport.getHigh()));
        carryPriceReq.setPublishType(tytTransport.getPublishType());
        carryPriceReq.setSource(PriceSourceEnum.publish.getSource());
        carryPriceReq.setUserId(tytTransport.getUserId());
        carryPriceReq.setSrcMsgId(tytTransport.getSrcMsgId());
        carryPriceReq.setDistance(tytTransport.getDistance());

        TransportLabelJson transportLabelJson = transportBusiness.getTransportLabelJson(tytTransport.getLabelJson());
        if (transportLabelJson != null && transportLabelJson.getGoodCarPriceTransport() != null && transportLabelJson.getGoodCarPriceTransport() == 1) {
            carryPriceReq.setGoodCarPriceTransport(1);
        }

        //请求数据部门的运价信息
        CarryPriceVo carryPriceVo = null;
        try {
            carryPriceVo = bsPublishTransportService.reqCarryPrice(carryPriceReq);
        } catch (Exception e) {
            logger.info("无价货源发货命中抽佣规则计算技术服务费调用BI获取建议价接口失败：{}", e);
            return null;
        }
        if (carryPriceVo != null && carryPriceVo.getSuggestMinPrice() != null) {
            logger.info("无价货源发货命中抽佣规则计算技术服务费调用BI获取建议价接口，取建议价最低值当作运费：{}", carryPriceVo.getSuggestMinPrice());
            return carryPriceVo.getSuggestMinPrice().toString();
        }
        return null;
    }

    @Override
    public String makePriceByGoodCarPriceTransportCarryPrice(Transport tytTransport) {
        TransportCarryBean transportCarryBean = buildCarryBeanByTransport(tytTransport);
        buildAddress(transportCarryBean);
        logger.info("抽佣金额计算-发货根据货源信息获取优车定价建议价最低值请求参数：{}", JSONObject.toJSONString(transportCarryBean));
        CarryPriceVo carryPriceVo = excellentPriceConfigService.getThPrice(transportCarryBean);
        if (carryPriceVo != null && carryPriceVo.getFixPriceMin() != null) {
            logger.info("抽佣金额计算-发货根据货源信息获取优车定价建议价最低值结果：{}", carryPriceVo.getFixPriceMin());
            return carryPriceVo.getFixPriceMin().toString();
        }
        return null;
    }

    /**
     * 返回手动设置技术服务费的抽佣结果
     */
    public TytTecServiceFeeConfigToComputResult getManualTecServiceFeeResult(Transport tytTransport, BigDecimal commissionScore) {
        TytTecServiceFeeConfigToComputResult tecServiceFeeConfigComputeResult = new TytTecServiceFeeConfigToComputResult();
        tecServiceFeeConfigComputeResult.setCarMemberType(0);
        tecServiceFeeConfigComputeResult.setPrivacyPhoneType(1);
        tecServiceFeeConfigComputeResult.setFreeTecServiceFeeType(0); // 不超时免佣
        tecServiceFeeConfigComputeResult.setFreeTecServiceFeeTime(0); // 超时免佣时间数据库是非空的
        tecServiceFeeConfigComputeResult.setTecServiceFee(tytTransport.getTecServiceFee());
        tecServiceFeeConfigComputeResult.setUseCommissionScoreStageConfig(true);
        tecServiceFeeConfigComputeResult.setCommissionScore(commissionScore);
        tecServiceFeeConfigComputeResult.setTecServiceFeeBeforeDiscount(tytTransport.getTecServiceFee());
        tecServiceFeeConfigComputeResult.setApplyTransportType(1); // 目前只有专车
        tecServiceFeeConfigComputeResult.setRefundFlagType(tytTransport.getRefundFlag());
        tecServiceFeeConfigComputeResult.setPricePublishType(3);
        return tecServiceFeeConfigComputeResult;
    }

    @Override
    public TytTecServiceFeeConfigToComputResult makeTecServiceFeeData(Transport tytTransport, TransportMain oldTransportMain,
                                                                      MeetCommissionRulesResult meetCommissionRules, boolean isGoodCarPriceTransport, TytTransportMainExtend mainExtend) {

        GoodModelResult goodModelResult = transportBusiness.checkInstantGrab(tytTransport);
        TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = null;
        log.info("通过bi返回的标识确认是否抽佣,goodModelResult:{}", JSONObject.toJSONString(goodModelResult));
        log.info("抽佣数据构建，userId:{}, taskContent:{}, biGoodModelResult：{},srcMsgId:{}", tytTransport.getUserId(),
                tytTransport.getTaskContent(), JSONObject.toJSONString(goodModelResult), Objects.nonNull(tytTransport) ? tytTransport.getSrcMsgId() : null);
        if (Objects.equals(tytTransport.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())
                || goodModelResult == null || isGoodMediumTransport(tytTransport, mainExtend) || Objects.equals(goodModelResult.getCommission_flag(), 0)) {
            Integer userType = abtestService.getUserType("commission_tec_service_fee_v2", tytTransport.getUserId());
            if (Objects.equals(userType, YesOrNoEnum.Y.getCode())) {
                tytTecServiceFeeConfigToComputResult = this.computeTecServiceFeeBtTransportDataV2(tytTransport, oldTransportMain, meetCommissionRules, isGoodCarPriceTransport, mainExtend);
            } else {
                tytTecServiceFeeConfigToComputResult = this.computeTecServiceFeeBtTransportData(tytTransport, oldTransportMain, meetCommissionRules, isGoodCarPriceTransport);
            }
        }

        log.info("抽佣数据构建，userId:{}, taskContent:{}, tecServiceFeeConfigComputeResult：{}, srcMsgId:{}", tytTransport.getUserId(),
                tytTransport.getTaskContent(), JSONObject.toJSONString(tytTecServiceFeeConfigToComputResult), Objects.nonNull(tytTransport) ? tytTransport.getSrcMsgId() : null);

        Integer defaultTecServiceFee = tytConfigService.getIntValue("default_tec_service_fee", 50);

        if (tytTecServiceFeeConfigToComputResult != null) {
            // 多车找货，并且有支付成功订单，不再重新计算技术服务费
            if (isMultiCarHavePayOrder(oldTransportMain)) {
                tytTecServiceFeeConfigToComputResult.setIsMultiCarHavePayOrder(true);
            } else {
                BigDecimal tecServiceFee = tytTecServiceFeeConfigToComputResult.getTecServiceFee();
                if (tecServiceFee == null) {
                    tecServiceFee = new BigDecimal(defaultTecServiceFee);
                }
                tytTransport.setTecServiceFee(tecServiceFee);
                //抽佣货源打标
                if (StringUtils.isNotBlank(tytTransport.getLabelJson())) {
                    JSONObject jsonObject = JSON.parseObject(tytTransport.getLabelJson());
                    jsonObject.put("commissionTransport", "1");
                    if (meetCommissionRules.getMeetCommissionRules() != null) {
                        jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                    }
                    jsonObject.put("commissionLabel", goodModelResult == null ? "" : goodModelResult.getCommission_label());
                    tytTransport.setLabelJson(jsonObject.toJSONString());
                } else {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("commissionTransport", "1");
                    if (meetCommissionRules.getMeetCommissionRules() != null) {
                        jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                    }
                    jsonObject.put("commissionLabel", goodModelResult == null ? "" : goodModelResult.getCommission_label());
                    tytTransport.setLabelJson(jsonObject.toJSONString());
                }
            }
        } else {
            //抽佣货源标识去掉
            if (StringUtils.isNotBlank(tytTransport.getLabelJson())) {
                JSONObject jsonObject = JSON.parseObject(tytTransport.getLabelJson());
                if (jsonObject.containsKey("commissionTransport")) {
                    jsonObject.remove("commissionTransport");
                    //如果曾经抽佣现在不抽了，要把main表技术服务费字段修改为0
                    tytTransport.setTecServiceFee(new BigDecimal(0));
                }

                if (meetCommissionRules.getMeetCommissionRules() != null) {
                    jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                }
                jsonObject.put("commissionLabel", goodModelResult == null ? "" : goodModelResult.getCommission_label());

                tytTransport.setLabelJson(StringUtils.isBlank(jsonObject.toJSONString()) ? "{}" : jsonObject.toJSONString());
            }

        }
        return tytTecServiceFeeConfigToComputResult;
    }

    /**
     * 调用goods-service计算抽佣技术服务费
     *
     * @param tytTransport
     * @param oldTransportMain
     * @param meetCommissionRules
     * @param isGoodCarPriceTransport
     * @return
     */
    private TytTecServiceFeeConfigToComputResult computeTecServiceFeeBtTransportDataV2(Transport tytTransport,
                                                                                       TransportMain oldTransportMain,
                                                                                       MeetCommissionRulesResult meetCommissionRules,
                                                                                       boolean isGoodCarPriceTransport,
                                                                                       TytTransportMainExtend mainExtend) {
        try {
            TecServiceFeeComputeDTO computeDTO = new TecServiceFeeComputeDTO();

            TransportMainDTO transportMain = new TransportMainDTO();
            BeanUtils.copyProperties(tytTransport, transportMain);
            if (StringUtils.isNotBlank(tytTransport.getDistance())) {
                transportMain.setDistance(new BigDecimal(tytTransport.getDistance()));
            }
            computeDTO.setTransportMain(transportMain);

            TransportMainDTO oldMain = new TransportMainDTO();
            if (Objects.nonNull(oldTransportMain)) {
                BeanUtils.copyProperties(oldTransportMain, oldMain);
            }
            computeDTO.setOldMain(oldMain);

            TransportMainExtendDTO mainExtendDTO = new TransportMainExtendDTO();
            if (Objects.nonNull(mainExtend)) {
                BeanUtils.copyProperties(mainExtend, mainExtendDTO);
            }
            computeDTO.setMainExtend(mainExtendDTO);

            computeDTO.setIsExcellentGoodsTwo(isGoodCarPriceTransport);
            log.info("computeTecServiceFeeBtTransportDataV2 param:{}", JSONObject.toJSONString(computeDTO));
            Response<TecServiceFeeConfigComputeResultVO> response = transportTecserviceFeeClient.computeTecServiceFeeByTransportData(computeDTO).execute();
            if (response.isSuccessful()) {
                TecServiceFeeConfigComputeResultVO computeResultVO = response.body();
                log.info("computeTecServiceFeeBtTransportDataV2 result:{}", JSONObject.toJSONString(computeResultVO));
                if (Objects.nonNull(computeResultVO)) {
                    meetCommissionRules.setMeetCommissionRules(computeResultVO.getMeetCommissionRules());
                    if (Objects.equals(computeResultVO.getMeetCommissionRules(), YesOrNoEnum.Y.getCode())) {
                        TytTecServiceFeeConfigToComputResult computeResult = new TytTecServiceFeeConfigToComputResult();
                        BeanUtils.copyProperties(computeResultVO, computeResult);
                        return computeResult;
                    }
                }
            }
        } catch (Exception e) {
            log.error("computeTecServiceFeeBtTransportDataV2 {}, {} error:", tytTransport.getUserId(), tytTransport.getTaskContent(), e);
        }
        return null;
    }

    /**
     * 是否是多车找货，并且有支付成功订单
     *
     * @param oldMain
     * @return
     */
    public boolean isMultiCarHavePayOrder(TransportMain oldMain) {
        boolean result = false;
        if (Objects.nonNull(oldMain) && Objects.nonNull(oldMain.getShuntingQuantity()) && oldMain.getShuntingQuantity() > 1) {
            try {
                if (Objects.nonNull(oldMain.getSrcMsgId()) && oldMain.getCtime().compareTo(TimeUtil.getStartOfDay(new Date())) > 0) {

                    Response<List<TytTransportOrders>> execute = apiTradeInfoFeeClient.getByTsId(oldMain.getSrcMsgId()).execute();
                    if (execute.isSuccessful()) {
                        List<TytTransportOrders> orders = execute.body();
                        if (CollectionUtils.isNotEmpty(orders)) {
                            long paySuccessCount = orders.stream().filter(v -> Objects.equals(v.getPayStatus(), "2")).count();
                            result = paySuccessCount > 0;
                        }
                    }
                }
            } catch(Exception e){
                log.error("isMultiCarHavePayOrder error, srcMsgId:{}", oldMain.getSrcMsgId(), e);
            }
            log.info("isMultiCarHavePayOrder result:{}, srcMsgId:{}", result, oldMain.getSrcMsgId());
        }
        return result;
    }

    private boolean isGoodMediumTransport(Transport tytTransport, TytTransportMainExtend mainExtend) {
        if (Objects.isNull(tytTransport)) {
            log.info("isGoodMediumTransport false 1, srcMsgId:{}", tytTransport.getSrcMsgId());
            return false;
        }
        if (Objects.isNull(mainExtend)) {
            log.info("isGoodMediumTransport false 2, srcMsgId:{}", tytTransport.getSrcMsgId());
            return false;
        }
        Integer goodTransportLabel = mainExtend.getGoodTransportLabel();
        if (goodTransportLabel != null && ((goodTransportLabel >= 11 && goodTransportLabel <= 13) || (goodTransportLabel >= 21 && goodTransportLabel <= 23))) {
            log.info("isGoodMediumTransport true, srcMsgId:{}", tytTransport.getSrcMsgId());
            return true;
        } else {
            log.info("isGoodMediumTransport false 3, srcMsgId:{}", tytTransport.getSrcMsgId());
            return false;
        }
    }

    @Override
    public void saveCommissionTransportTecServiceFeeData(Long srcMsgId, TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult) {
        if (srcMsgId == null || tytTecServiceFeeConfigToComputResult == null) {
            return;
        }

        if (tytTecServiceFeeConfigToComputResult.getIsMultiCarHavePayOrder()) {
            log.info("抽佣货源数据记录，多车找货货源存在支付成功订单不更新技术服务费。srcMsgId:{}, computeResult:{}", srcMsgId, JSONObject.toJSONString(tytTecServiceFeeConfigToComputResult));
            return;
        }

        if (tytTecServiceFeeConfigToComputResult.getTransportProportionNum() != null) {
            RedisUtil.set(COMMISSION_PROPORTION_TRANSPORT_NUM + ":" + srcMsgId, String.valueOf(tytTecServiceFeeConfigToComputResult.getTransportProportionNum()), 60 * 60 * 24);
        }

        Integer defaultTecServiceFee = tytConfigService.getIntValue("default_tec_service_fee", 50);

        TytTransportTecServiceFee tytTransportTecServiceFee = new TytTransportTecServiceFee();
        tytTransportTecServiceFee.setSrcMsgId(srcMsgId);
        tytTransportTecServiceFee.setCreateTime(new Date());
        tytTransportTecServiceFee.setModifyTime(new Date());

        tytTransportTecServiceFee.setApplyTransportType(tytTecServiceFeeConfigToComputResult.getApplyTransportType());
        tytTransportTecServiceFee.setRefundFlagType(tytTecServiceFeeConfigToComputResult.getRefundFlagType());
        tytTransportTecServiceFee.setPricePublishType(tytTecServiceFeeConfigToComputResult.getPricePublishType());
        tytTransportTecServiceFee.setTecServiceFeeMin(tytTecServiceFeeConfigToComputResult.getTecServiceFeeMin());
        tytTransportTecServiceFee.setTecServiceFeeMax(tytTecServiceFeeConfigToComputResult.getTecServiceFeeMax());
        tytTransportTecServiceFee.setPriceMin(tytTecServiceFeeConfigToComputResult.getPriceMin());
        tytTransportTecServiceFee.setPriceMax(tytTecServiceFeeConfigToComputResult.getPriceMax());
        tytTransportTecServiceFee.setTecServiceFeeRate(tytTecServiceFeeConfigToComputResult.getTecServiceFeeRate());
        tytTransportTecServiceFee.setDiscountTime(tytTecServiceFeeConfigToComputResult.getDiscountTime());
        tytTransportTecServiceFee.setDiscount(tytTecServiceFeeConfigToComputResult.getDiscount());
        tytTransportTecServiceFee.setTransportProportionNum(tytTecServiceFeeConfigToComputResult.getTransportProportionNum());
        tytTransportTecServiceFee.setDiscountConfig(tytTecServiceFeeConfigToComputResult.getDiscountConfig());
        tytTransportTecServiceFee.setAllDiscount(tytTecServiceFeeConfigToComputResult.getAllDiscount());
        tytTransportTecServiceFee.setGoodTransportLabelType(tytTecServiceFeeConfigToComputResult.getGoodTransportLabelType());
        tytTransportTecServiceFee.setGoodTransportLabel(tytTecServiceFeeConfigToComputResult.getGoodTransportLabel());
        tytTransportTecServiceFee.setRouteType(tytTecServiceFeeConfigToComputResult.getRouteType());
        tytTransportTecServiceFee.setStartCity(tytTecServiceFeeConfigToComputResult.getStartCity());
        tytTransportTecServiceFee.setDestCity(tytTecServiceFeeConfigToComputResult.getDestCity());
        tytTransportTecServiceFee.setDistanceMin(tytTecServiceFeeConfigToComputResult.getDistanceMin());
        tytTransportTecServiceFee.setDistanceMax(tytTecServiceFeeConfigToComputResult.getDistanceMax());


        if (tytTecServiceFeeConfigToComputResult.getCommissionScore() != null) {
            tytTransportTecServiceFee.setCommissionScore(tytTecServiceFeeConfigToComputResult.getCommissionScore());
        }
        if (StringUtils.isNotBlank(tytTecServiceFeeConfigToComputResult.getGoodCarPriceTransportCarryPrice())) {
            tytTransportTecServiceFee.setGoodCarPriceTransportCarryPrice(tytTecServiceFeeConfigToComputResult.getGoodCarPriceTransportCarryPrice());
        }
        if (tytTecServiceFeeConfigToComputResult.getCarMemberType() == 0) {
            tytTransportTecServiceFee.setMemberBeforeFee(tytTecServiceFeeConfigToComputResult.getTecServiceFeeBeforeDiscount());
            tytTransportTecServiceFee.setMemberAfterFee(tytTecServiceFeeConfigToComputResult.getTecServiceFee());
            tytTransportTecServiceFee.setNoMemberBeforeFee(tytTecServiceFeeConfigToComputResult.getTecServiceFeeBeforeDiscount());
            tytTransportTecServiceFee.setNoMemberAfterFee(tytTecServiceFeeConfigToComputResult.getTecServiceFee());
            tytTransportTecServiceFee.setMemberInterestsWord(tytTecServiceFeeConfigToComputResult.getInterestsWord());
            tytTransportTecServiceFee.setMemberInterestsUrl(tytTecServiceFeeConfigToComputResult.getInterestsUrl());
            tytTransportTecServiceFee.setNoMemberInterestsWord(tytTecServiceFeeConfigToComputResult.getInterestsWord());
            tytTransportTecServiceFee.setNoMemberInterestsUrl(tytTecServiceFeeConfigToComputResult.getInterestsUrl());
            tytTransportTecServiceFee.setMemberShowPrivacyPhoneTab(tytTecServiceFeeConfigToComputResult.getPrivacyPhoneType());
            tytTransportTecServiceFee.setNoMemberShowPrivacyPhoneTab(tytTecServiceFeeConfigToComputResult.getPrivacyPhoneType());
            tytTransportTecServiceFee.setMemberFreeTecServiceFeeType(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeType());
            tytTransportTecServiceFee.setMemberFreeViewCount(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount());
            tytTransportTecServiceFee.setMemberFreeCallCount(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount());
            tytTransportTecServiceFee.setMemberFreeReadyType(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReady() != null && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReady() ? 1 : 0);
            tytTransportTecServiceFee.setMemberFreeReadyTime(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReadyTime());
            tytTransportTecServiceFee.setMemberFreeTecServiceFeeTime(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime());
            tytTransportTecServiceFee.setNoMemberFreeTecServiceFeeType(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeType());
            tytTransportTecServiceFee.setNoMemberFreeViewCount(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount());
            tytTransportTecServiceFee.setNoMemberFreeCallCount(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount());
            tytTransportTecServiceFee.setNoMemberFreeReadyType(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReady() != null && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReady() ? 1 : 0);
            tytTransportTecServiceFee.setNoMemberFreeReadyTime(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReadyTime());
            tytTransportTecServiceFee.setNoMemberFreeTecServiceFeeTime(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime());
            tytTransportTecServiceFee.setMemberUseCommissionStageType(tytTecServiceFeeConfigToComputResult.getUseCommissionScoreStageConfig() == null || !tytTecServiceFeeConfigToComputResult.getUseCommissionScoreStageConfig() ? 1 : 2);
            tytTransportTecServiceFee.setNoMemberUseCommissionStageType(tytTecServiceFeeConfigToComputResult.getUseCommissionScoreStageConfig() == null || !tytTecServiceFeeConfigToComputResult.getUseCommissionScoreStageConfig() ? 1 : 2);
        }
        if (tytTransportTecServiceFee.getMemberBeforeFee() == null) {
            tytTransportTecServiceFee.setMemberBeforeFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getMemberAfterFee() == null) {
            tytTransportTecServiceFee.setMemberAfterFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getNoMemberBeforeFee() == null) {
            tytTransportTecServiceFee.setNoMemberBeforeFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getNoMemberAfterFee() == null) {
            tytTransportTecServiceFee.setNoMemberAfterFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getMemberShowPrivacyPhoneTab() == null) {
            tytTransportTecServiceFee.setMemberShowPrivacyPhoneTab(1);
        }
        if (tytTransportTecServiceFee.getNoMemberShowPrivacyPhoneTab() == null) {
            tytTransportTecServiceFee.setNoMemberShowPrivacyPhoneTab(1);
        }
        if (tytTransportTecServiceFee.getMemberFreeTecServiceFeeType() == null) {
            tytTransportTecServiceFee.setMemberFreeTecServiceFeeType(1);
        }
        if (tytTransportTecServiceFee.getMemberFreeTecServiceFeeTime() == null) {
            tytTransportTecServiceFee.setMemberFreeTecServiceFeeTime(60);
        }
        if (tytTransportTecServiceFee.getNoMemberFreeTecServiceFeeType() == null) {
            tytTransportTecServiceFee.setNoMemberFreeTecServiceFeeType(1);
        }
        if (tytTransportTecServiceFee.getNoMemberFreeTecServiceFeeTime() == null) {
            tytTransportTecServiceFee.setNoMemberFreeTecServiceFeeTime(60);
        }
        if (tytTransportTecServiceFee.getMemberUseCommissionStageType() == null) {
            tytTransportTecServiceFee.setMemberUseCommissionStageType(1);
        }
        if (tytTransportTecServiceFee.getNoMemberUseCommissionStageType() == null) {
            tytTransportTecServiceFee.setNoMemberUseCommissionStageType(1);
        }
        TytTransportTecServiceFee bySrcMsgId = tytTransportTecServiceFeeMapper.getBySrcMsgId(srcMsgId);
        if (bySrcMsgId == null) {
            tytTransportTecServiceFeeMapper.insert(tytTransportTecServiceFee);
        } else {
            tytTransportTecServiceFeeMapper.updateBySrcMsgId(tytTransportTecServiceFee);
        }

        List<Integer> needFreeTecTypeListResult = new ArrayList<>();
        boolean memberFree = false;
        boolean noMemberFree = false;

        if (CollectionUtils.isNotEmpty(tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList())) {
            needFreeTecTypeListResult.addAll(tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList());
            if (tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList().contains(5)) {
                memberFree = true;
                noMemberFree = true;
            }
        }

        tytTransportTecServiceFeeMapper.deleteFreeTecServiceFeeLogBySrcMsgId(srcMsgId);

        TytFreeTecServiceFeeLog tytFreeTecServiceFeeLog = new TytFreeTecServiceFeeLog(null, srcMsgId, 0, 0, 0, 0, 0, new Date(), new Date());
        if (!needFreeTecTypeListResult.isEmpty()) {
            for (Integer code : needFreeTecTypeListResult) {
                if (code == 1) {
                    tytFreeTecServiceFeeLog.setNewTransportUserFree(1);
                }
                if (code == 3) {
                    tytFreeTecServiceFeeLog.setCityFree(1);
                }
                if (code == 4) {
                    tytFreeTecServiceFeeLog.setGoodCarPriceTransportFree(1);
                }
            }
            if (memberFree) {
                tytFreeTecServiceFeeLog.setMemberTimeOutFree(1);
            }
            if (noMemberFree) {
                tytFreeTecServiceFeeLog.setNoMemberTimeOutFree(1);
            }
        }
        tytTransportTecServiceFeeMapper.addFreeTecServiceFeeLog(tytFreeTecServiceFeeLog);
    }

    @Override
    public ResultMsgBean getTecServiceFeeByCarUserAndSrcMsgId(Long carUserId, Long srcMsgId) {
        TytTransportTecServiceFee tytTransportTecServiceFee = tytTransportTecServiceFeeMapper.getBySrcMsgId(srcMsgId);
        TecServiceFeeVO tecServiceFeeVO = new TecServiceFeeVO(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, null, null);
        if (tytTransportTecServiceFee != null) {
            UserPermission userPermission = userPermissionService.getUserPermission(carUserId, UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode());
            UserPermission userPermissionCarSmallMember = userPermissionService.getUserPermission(carUserId, UserPermission.PermissionTypeEnum.CAR_SMALL_MEMBER.getCode());
            if ((null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus()))
                    || (null != userPermissionCarSmallMember && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermissionCarSmallMember.getStatus()))) {
                //有车会员（包含小会员），展示车会员技术服务费
                tecServiceFeeVO.setTecServiceFee(tytTransportTecServiceFee.getMemberAfterFee());
                tecServiceFeeVO.setTecServiceFeeAfterDiscount(tytTransportTecServiceFee.getMemberBeforeFee());
                if (tytTransportTecServiceFee.getMemberAfterFee() != null && tytTransportTecServiceFee.getMemberBeforeFee() != null) {
                    tecServiceFeeVO.setTecServiceFeeAfterDiscountDValue(tytTransportTecServiceFee.getMemberBeforeFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()));
                }
            } else {
                //无车会员，展示车会员技术服务费
                tecServiceFeeVO.setTecServiceFee(tytTransportTecServiceFee.getNoMemberAfterFee());
                tecServiceFeeVO.setTecServiceFeeAfterDiscount(tytTransportTecServiceFee.getNoMemberBeforeFee());
                if (tytTransportTecServiceFee.getNoMemberAfterFee() != null && tytTransportTecServiceFee.getNoMemberBeforeFee() != null) {
                    tecServiceFeeVO.setTecServiceFeeAfterDiscountDValue(tytTransportTecServiceFee.getNoMemberBeforeFee().subtract(tytTransportTecServiceFee.getNoMemberAfterFee()));
                }
                if (tytTransportTecServiceFee.getMemberAfterFee() != null
                        && tytTransportTecServiceFee.getNoMemberAfterFee() != null
                        && tytTransportTecServiceFee.getNoMemberAfterFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()).compareTo(BigDecimal.ZERO) > 0) {
                    tecServiceFeeVO.setTecServiceFeeWord("成为会员本单为您省"
                            + tytTransportTecServiceFee.getNoMemberAfterFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()).setScale(0).toString()
                            + "元 去购买");
                    //有小会员
                    List<GoodsSmallDO> userBuyList = getUserBuyList(carUserId, 1);
                    if (CollectionUtils.isNotEmpty(userBuyList) && userBuyList.get(0) != null && userBuyList.get(0).getId() != null) {
                        tecServiceFeeVO.setTecServiceFeeBuySmallMemberId(userBuyList.get(0).getId());
                    } else {
                        tecServiceFeeVO.setTecServiceFeeBuySmallMemberId(-1L);
                    }
                }
            }

            //判断是否符合直接免佣条件
            CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeVO = checkIsNeedFreeTecSericeFeeByCarUser(carUserId, srcMsgId);
            if (checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec()) {
                tecServiceFeeVO.setTecServiceFeeWord(checkIsNeedFreeTecServiceFeeVO.getWord());
                tecServiceFeeVO.setTecServiceFee(BigDecimal.ZERO);
                //折前折后技术服务费差值直接使用折前技术服务费
                tecServiceFeeVO.setTecServiceFeeAfterDiscountDValue(tecServiceFeeVO.getTecServiceFeeAfterDiscount());
            }

        } else {
            //非抽佣货源，技术服务费正常展示
            TransportMain bySrcMsgId = transportMainService.getBySrcMsgId(srcMsgId);
            tecServiceFeeVO.setTecServiceFee(bySrcMsgId.getTecServiceFee());
            tecServiceFeeVO.setTecServiceFeeAfterDiscount(bySrcMsgId.getTecServiceFee());
            tecServiceFeeVO.setTecServiceFeeAfterDiscountDValue(BigDecimal.ZERO);
        }
        log.info("tecServiceFeeVO:{}", JSONObject.toJSONString(tecServiceFeeVO));
        return ResultMsgBean.successResponse(tecServiceFeeVO);
    }

    @Override
    public CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByCarUser(Long userId, Long srcMsgId) {
        try {
            Response<CheckIsNeedFreeTecServiceFeeVO> response = transportTecserviceFeeClient.checkIsNeedFreeTecSericeFeeByCarUser(userId, srcMsgId).execute();
            if (response.isSuccessful()) {
                logger.info("判断是否符合车方直接免佣条件,userId:{},srcMsgId:{},返回结果:{}", userId, srcMsgId, JSON.toJSONString(response.body()));
                return response.body();
            }
        } catch (IOException e) {
            logger.info("判断是否符合车方直接免佣条件接口失败,请求参数：", e);
        }
        return new CheckIsNeedFreeTecServiceFeeVO(false, null, null);
    }

    @Override
    public CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByTransport(Long transportUserId, String startCity, boolean isGoodCarPriceTransport) {
        try {
            Response<CheckIsNeedFreeTecServiceFeeVO> response = transportTecserviceFeeClient.checkIsNeedFreeTecSericeFeeByTransport(transportUserId, startCity, isGoodCarPriceTransport).execute();
            if (response.isSuccessful()) {
                logger.info("判断是否符合货源直接免佣条件,transportUserId:{},startCity:{},isGoodCarPriceTransport:{},返回结果:{}", transportUserId, startCity, isGoodCarPriceTransport, JSON.toJSONString(response.body()));
                return response.body();
            }
        } catch (IOException e) {
            logger.info("判断是否符合货源直接免佣条件接口失败,请求参数：", e);
        }
        return new CheckIsNeedFreeTecServiceFeeVO(false, null, null);
    }

    @Override
    public List<GoodsSmallDO> getUserBuyList(Long userId, Integer userType) {
        List<Long> goodsSmallIdList = tytUserRecordMapper.getGoodsSmallIdList(userId);
        if (!goodsSmallIdList.isEmpty()) {
            return tytUserRecordMapper.getByIdList(goodsSmallIdList);
        }
        return Collections.emptyList();
    }

    @Override
    public CommissionTypeBean checkCommission(DrawCommissionReq req, Transport transport) {
        logger.info("调用货源抽佣规则接口开始，req:{}", JSONUtil.toJsonStr(req));
        if (req == null || req.getSourceType() == null || req.getExcellentGoods() == null || req.getPublishType() == null || req.getHavePrice() == null || req.getRefundFlag() == null || req.getUserId() == null) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }
        if (req.getCommissionTime() == null) {
            req.setCommissionTime(new Date());
        }
        // 运满满货源
        if (Objects.equals(req.getSourceType(), SourceTypeEnum.运满满货源.getId())) {
            req.setCommissionSource(CommissionSourceEnum.YMM.getCode());
            // 优车定价货源
        } else if (Objects.equals(req.getGoodCarPriceTransport(), YesOrNoEnum.Y.getCode())) {
            req.setCommissionSource(CommissionSourceEnum.EXCELLENT_PRICE.getCode());
            // 普货 +  优车
        } else if (Objects.equals(req.getExcellentGoods(), ExcellentGoodsEnums.EXCELLENT.getCode()) || Objects.equals(req.getExcellentGoods(), ExcellentGoodsEnums.NORMAL.getCode())) {
            req.setCommissionSource(CommissionSourceEnum.ORDINARY_EXCELLENT.getCode());
            // 专车货源
        } else if (Objects.equals(req.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())) {
            req.setCommissionSource(CommissionSourceEnum.SPECIAL.getCode());

        }

        CommissionTypeBean commissionTypeBean = new CommissionTypeBean();

        if (req.getInvoiceTransport() != null && req.getInvoiceTransport().equals(YesOrNoEnum.Y.getCode()) && !req.getCommissionSource().equals(CommissionSourceEnum.SPECIAL.getCode())) {
            log.info("调用货源抽佣规则,非专车的开票货源不进行抽取,req:{}", JSONUtil.toJsonStr(req));
            return commissionTypeBean;
        }

        List<Integer> commissionSourceCodeList = Arrays.stream(CommissionSourceEnum.values()).map(CommissionSourceEnum::getCode).collect(Collectors.toList());
        if (req.getCommissionSource() == null || !commissionSourceCodeList.contains(req.getCommissionSource())) {
            log.info("调用货源抽佣规则,货源类型不正确，不进行抽取,req:{}", JSONUtil.toJsonStr(req));
            return commissionTypeBean;
        }

        int count = tytDispatchCompanyMapper.countByUserId(req.getUserId());

        // 如果是代调或个人货主且是非专车，则不抽佣(代调账号在app发的货也不抽佣)
        if ((Objects.equals(req.getSourceType(), SourceTypeEnum.代调发货.getId()) || Objects.equals(req.getSourceType(), SourceTypeEnum.个人货主.getId()) || count > 0)
                && !req.getCommissionSource().equals(CommissionSourceEnum.SPECIAL.getCode())
                && !req.getCommissionSource().equals(CommissionSourceEnum.YMM.getCode())) {
            log.info("调用货源抽佣规则,代调或个人货主非专车，不进行抽取,req:{}", JSONUtil.toJsonStr(req));
            return commissionTypeBean;
        }

//        if (req.getGoodsModelScore() == null && transport != null) {
//            GoodModelResult goodModelResult = transportBusiness.checkInstantGrab(transport);
//            if (goodModelResult != null && goodModelResult.getScore() != null) {
//                req.setGoodsModelScore(goodModelResult.getScore());
//                String labelJson = transport.getLabelJson();
//                //抽佣货源打标
//                if (StringUtils.isNotBlank(labelJson)) {
//                    JSONObject jsonObject = JSON.parseObject(transport.getLabelJson());
//                    jsonObject.put("goodsModelScore", goodModelResult.getScore());
//                    transport.setLabelJson(jsonObject.toJSONString());
//                } else {
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("goodsModelScore", goodModelResult.getScore());
//                    transport.setLabelJson(jsonObject.toJSONString());
//                }
//            }
//        }
//
//        if (req.getGoodsModelScore() == null) {
//            logger.error("调用货源抽佣规则模型评分为空,req:{}", JSONUtil.toJsonStr(req));
//            return commissionTypeBean;
//        }

        TytDrawCommissionRule drawCommissionRule = tytDrawCommissionRuleMapper.selectRule(req);

        // 满足规则条件，就打标可抽佣，是否抽佣根据下面判断确定
        if (drawCommissionRule != null) {
            log.info("调用货源抽佣规则,满足抽佣规则,id,{},req:{}", drawCommissionRule.getId(), JSONUtil.toJsonStr(req));
            commissionTypeBean.setMeetCommissionRules(CommissionResultEnum.COMMISSION_Y.getCode());

            String redisKeySuffix = req.getCommissionSource() + "_" + req.getPublishType() + "_" + req.getHavePrice() + "_" + req.getRefundFlag() + "_" + cn.hutool.core.date.DateUtil.format(new Date(), PURE_DATE_PATTERN);

            String commissionCountKey = String.format("commission_count_" + redisKeySuffix);
            int commissionCount = cacheService.getString(commissionCountKey) == null ? 0 : Integer.parseInt(cacheService.getString(commissionCountKey));
            // 如果已经超过了抽佣条数已经超过了最大值，不再抽佣
            Integer commissionMaxCount = drawCommissionRule.getCommissionMaxCount();
            if (commissionMaxCount != null && commissionCount >= commissionMaxCount) {
                log.info("调用货源抽佣规则,超过最大抽佣条数，不进行抽取,req:{}", JSONUtil.toJsonStr(req));
                return commissionTypeBean;
            }
            //如果不在抽佣时间段内，不进行抽取
//            Date startDate = drawCommissionRule.getStartDate();
//            Date endDate = drawCommissionRule.getEndDate();
//            if (req.getCommissionTime().before(startDate) || req.getCommissionTime().after(endDate)) {
//                return commissionTypeBean;
//            }

            // 如果不在当天的抽佣时间段内，不进行抽取
            LocalTime dailyStart = drawCommissionRule.getDailyStart().toLocalTime();
            LocalTime dailyEnd = drawCommissionRule.getDailyEnd().toLocalTime();
            LocalTime commissionTime = new Time(req.getCommissionTime().getTime()).toLocalTime();
            if (commissionTime.isBefore(dailyStart) || commissionTime.isAfter(dailyEnd)) {
                log.info("调用货源抽佣规则,不在当天抽佣时间段内，不进行抽取,req:{}", JSONUtil.toJsonStr(req));
                return commissionTypeBean;
            }

            // 若货主未履约过，且发货时间距离首次发货时间不足30天时，则不打抽佣标签，即“是否抽佣”标签为否
//            boolean b = checkTrade(req);
//            if (b) {
//                commissionTypeBean.setMeetCommissionRules(CommissionResultEnum.COMMISSION_FIRST_N.getCode());
//                return commissionTypeBean;
//            }

            //判断该货源有没有ab测试,如果有，就隔一条取一次
            if (Objects.equals(drawCommissionRule.getRule(), YesOrNoEnum.Y.getCode())) {
                String commissionFlagKey = String.format("commission_flag_" + redisKeySuffix);
                String commissionFlag = cacheService.getString(commissionFlagKey);
                // 如果上一条没抽，那么这一条就要抽
                if (StringUtils.isBlank(commissionFlag) || Objects.equals(commissionFlag, YesOrNoEnum.N.getCode().toString())) {
                    commissionTypeBean.setDrawCommission(YesOrNoEnum.Y.getCode());
                    // 更改标识
                    cacheService.setString(commissionFlagKey, YesOrNoEnum.Y.getCode().toString(), 24 * 60 * 60);
                    // 更改抽取数量
                    cacheService.setString(commissionCountKey, String.valueOf(commissionCount + 1), 24 * 60 * 60);
                } else {
                    // 更改标识
                    cacheService.setString(commissionFlagKey, YesOrNoEnum.N.getCode().toString(), 24 * 60 * 60);
                }
            } else {
                // 没有ab测试的话，符合条件的都抽
                commissionTypeBean.setDrawCommission(YesOrNoEnum.Y.getCode());
                cacheService.setString(commissionCountKey, String.valueOf(commissionCount + 1), 24 * 60 * 60);
            }
        }
        logger.info("调用货源抽佣规则接口结束，返回值:{}", JSONUtil.toJsonStr(commissionTypeBean));
        return commissionTypeBean;
    }

    private boolean checkTrade(DrawCommissionReq req) {
        // 判断是否满足条件
        boolean flag = false;


        TransportMain transportMain = transportMainService.getFirstTransport(req.getUserId());
        log.info("抽佣获取用户首次发货信息,userId:{},transportMain:{}", req.getUserId(), JSONUtil.toJsonStr(transportMain));
        if (transportMain == null) {
            return true;
        }

        // 如果发货时间在30天之前，还走之前的抽佣逻辑
        Date ctime = transportMain.getCtime();
        if (DateUtils.addDays(ctime, 30).before(new Date())) {
            return false;
        }

        // 判断货主是否履约过
        UserPerformanceNumDTO userPerformanceNumDTO = new UserPerformanceNumDTO();
        userPerformanceNumDTO.setUserId(req.getUserId());
        // 1:车主  2：货主
        userPerformanceNumDTO.setUserType(2);
        userPerformanceNumDTO.setRiskOrderFlag(YesOrNoEnum.Y.getCode());
        try {
            log.info("调用trade_service获取用户成交单数开始,req:{}", JSONUtil.toJsonStr(userPerformanceNumDTO));
            Response<Integer> response = apiTradeInfoFeeClient.getUserPerformanceNum(userPerformanceNumDTO).execute();
            log.info("调用trade_service获取用户成交单数结束,response:{}", JSON.toJSONString(response.toString()));
            if (response.isSuccessful()) {
                Integer body = response.body();
                log.info("调用trade_service获取用户成交单数结束,response:{}", JSON.toJSONString(body));
                //用户咩有履约过，返回是，否则返回否
                if (body != null && body <= 0) {
                    flag = true;
                }
            }
        } catch (IOException e) {
            log.error("调用履约接口失败,请求参数：{}", JSONUtil.toJsonStr(userPerformanceNumDTO), e);
        }
        return flag;
    }

    @Override
    public Boolean transportAgreeQuotedPriceEditTransportPriceAndReComputTecServiceFeeAndInvoice(BaseParameter parameter, Long srcMsgId, Integer carQuotedPrice, Long transportQuotedPriceId, Integer type) {
        //只有一口价优车2.0货源才会修改运费
        // 6550优车2.0新增有价电议，也支持修改运费
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain == null || transportMain.getPublishType() == null/* || transportMain.getPublishType() != 2*/) {
            return false;
        }

        //开票货源或者非灵活运价的专车货源不可报价的同时修改运费
        Integer priceType = tytTransportMainExtendMapper.getPriceTypeBySrcMsgId(srcMsgId);
        if ((transportMain.getInvoiceTransport() != null && transportMain.getInvoiceTransport() == 1)
                || (transportMain.getExcellentGoods() != null && transportMain.getExcellentGoods() == 2 && (priceType == null || priceType != 2))) {
            return false;
        }

        //车方报价和原有货源运费相等，则不做任何操作
        if (StringUtils.isNotBlank(transportMain.getPrice()) && Integer.parseInt(transportMain.getPrice()) == carQuotedPrice) {
            return false;
        }

        String price = transportMain.getPrice();

        Integer addPriceReq = 0;
        if (StringUtils.isBlank(transportMain.getPrice())) {
            //原本无价，相当于填价
            addPriceReq = carQuotedPrice;
        } else {
            //原本有价，相当于加价，加价金额可能是负数
            addPriceReq = carQuotedPrice - Integer.parseInt(transportMain.getPrice());
        }

        try {
            //报价修改运费不再受十分钟限制
            freightAddMoneyNum(transportMain.getUserId(), transportMain.getSrcMsgId(), String.valueOf(addPriceReq), parameter.getClientVersion(), parameter.getClientSign(), 0, 1, null, 2);
        } catch (Exception e) {
            logger.error("货方同意车方报价，一口价优车2.0自动修改价格失败 报价表ID：{}", transportQuotedPriceId, e);
        }
        if (type == 1) {
            tytTransportQuotedPriceMapper.transportQuotedPriceModifyPriceLog(transportQuotedPriceId, srcMsgId, StringUtils.isBlank(price) || new BigDecimal(price).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : new BigDecimal(price), new BigDecimal(carQuotedPrice));
        }
        return true;


//        transportMain.setPrice(String.valueOf(carQuotedPrice));
//        Transport lastBySrcMygId = transportService.getLastBySrcMygId(transportMain.getSrcMsgId());
//        //修改运费
//        transportMainService.update(transportMain);
//        transportService.update(lastBySrcMygId);
//
//
//        if (StringUtils.isNotBlank(transportMain.getPrice()) && !transportMain.getPrice().equals("0") && carQuotedPrice > Integer.parseInt(transportMain.getPrice())) {
//            //货源有价并且车方报价大于之前的运费，发送加价push
//            this.sendAddMoneyMessage2MQ(srcMsgId, new BigDecimal(carQuotedPrice - Integer.parseInt(transportMain.getPrice())));
//        }

    }

    /**
     * 修改货源的长宽高等信息
     *
     * @param updateDataReq
     */
    @Override
    public ResultMsgBean updateGoodsInfo(TransportUpdateDataReq updateDataReq) throws Exception {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(updateDataReq.getTsId())) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }

        Long tsId = updateDataReq.getTsId();
        Long userId = updateDataReq.getUserId();
        Transport transport = getByGoodsId(tsId);
        if (Objects.isNull(transport)) {
            return new ResultMsgBean(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "货源不存在");
        }

        // 转一口价，走一口价逻辑
        if (updateDataReq.getPublishType() != null) {
            ResultMsgBean resultMsgBean = this.updatePublishType(userId, tsId, updateDataReq.getPublishType(), updateDataReq.getClientVersion(),
                    updateDataReq.getClientVersion(), 0, updateDataReq.getPrice());
            return resultMsgBean;
        }

        // 如果是加价，走加价逻辑。如果货源之前价格不为空，且比原来的价格大，走加价逻辑
        if (StringUtils.isNotBlank(updateDataReq.getPrice())) {

            //校验价格高低值
            TransportPublishBean publishBean = new TransportPublishBean();
            BeanUtils.copyProperties(transport, publishBean);
            publishBean.setPrice(transport.getPrice());
            ResultMsgBean priceMsgBean = this.checkPriceAllow(publishBean);
            if (priceMsgBean != null && !priceMsgBean.isSuccess()) {
                return priceMsgBean;
            }

            BigDecimal oldPrice = TransportUtil.hasPrice(transport.getPrice()) ? new BigDecimal(transport.getPrice()) : BigDecimal.ZERO;
            BigDecimal newPrice = new BigDecimal(updateDataReq.getPrice());
            if (newPrice.compareTo(oldPrice) > 0) {
                String price = String.valueOf(newPrice.subtract(oldPrice));
                return this.freightAddMoneyNum(userId, tsId, price,
                        updateDataReq.getClientVersion(), updateDataReq.getClientSign(), 0, null, null, 3);
            }
        }

        // 如果是当天货源 并且 状态是发布中，则先撤销货源
        boolean isYesterday = transport.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime();
        if (!isYesterday && transport.getStatus() == 1) {
            // 先撤销货源
            String backoutReasonKey = "直接发布撤销";
            Integer backoutReasonValue = 0;
            ResultMsgBean msgBean = transportBusiness.saveInfoFeeUpdateBtnStatusNew(userId, 1, tsId, new TransportDoneRequest(), backoutReasonKey, backoutReasonValue, null, null, true, null, null);
            if (ReturnCodeConstant.OK != msgBean.getCode()) {
                return msgBean;
            }
        }

        SaveDirectReq saveDirectReq = new SaveDirectReq();
        saveDirectReq.setUserId(userId);
        saveDirectReq.setClientVersion(updateDataReq.getClientVersion());
        saveDirectReq.setClientSign(updateDataReq.getClientSign());
        saveDirectReq.setGoodsId(tsId);
        saveDirectReq.setIsBackendTransport(0);
        saveDirectReq.setUseExposure(false); // 是否消耗曝光卡
        saveDirectReq.setUpdateDataReq(updateDataReq);

        saveDirectReq.setPrice(updateDataReq.getPrice());
        saveDirectReq.setPublishType(updateDataReq.getPublishType());
        saveDirectReq.setPriceSuggest(updateDataReq.getPriceSuggest());

        // 如果转一口价，automaticGoodCarPriceTransportType=4；如果补充运费，automaticGoodCarPriceTransportType=3
        Integer automaticGoodCarPriceTransportType;
        PubTypeEnum pubTypeEnum = null;
        if (Objects.equals(updateDataReq.getPublishType(), PublishTypeEnum.fixed.getCode())) {
            // 转一口价，需要检验之前运费和补充运费，不能都为空
            if (TyTGoodsUtil.isPriceless(transport.getPrice()) && TyTGoodsUtil.isPriceless(updateDataReq.getPrice())) {
                return ResultMsgBean.failResponse(ResponseEnum.sys_error.info("转一口价需要补充运费"));
            }
            automaticGoodCarPriceTransportType = 4;
            pubTypeEnum = PubTypeEnum.TRANSFER_FIX_PRICE;
        } else if (!TyTGoodsUtil.isPriceless(updateDataReq.getPrice())) {
            automaticGoodCarPriceTransportType = 3;
            pubTypeEnum = PubTypeEnum.FILL_PRICE;
        } else {
            automaticGoodCarPriceTransportType = null;
        }
        saveDirectReq.setAutomaticGoodCarPriceTransportType(automaticGoodCarPriceTransportType);

        // 如果目的地不为空，设置变更标识、distance距离
        if (updateDataReq.getDestLatitude() != null && updateDataReq.getDestLongitude() != null) {

            ResultMsgBean resultMsgBean = this.pxAndPyByCityName(updateDataReq.getDestProvinc(), updateDataReq.getDestCity(),
                    updateDataReq.getDestArea(), "1", userId);
            if (resultMsgBean.getCode().equals(200) && resultMsgBean.getData() != null) {
                City city = (City) resultMsgBean.getData();
                updateDataReq.setDestCoordX(new BigDecimal(city.getPx()));
                updateDataReq.setDestCoordY(new BigDecimal(city.getPy()));
                updateDataReq.setDestProvinc(city.getProvince());
                updateDataReq.setDestCity(city.getCityName());
                updateDataReq.setDestArea(city.getAreaName());
                updateDataReq.setDestPoint(updateStartAndDestPoint(city.getProvince(), city.getCityName(), city.getAreaName()));
                logger.info("消息发布出发地的补全参数，省市区县:{}, x轴:{}，y轴:{}", updateDataReq.getDestPoint(),
                        updateDataReq.getDestCoordX(), updateDataReq.getDestCoordY());
            }
            String distance = new BigDecimal(updateDataReq.getDistance()).movePointRight(2).toString();
            updateDataReq.setDistance(distance);
            saveDirectReq.setIsChangeDestAddress(true);
        }

        //再发布货源
        boolean topFlag = true;
        // 如果是修改长宽高或者地址，不置顶刷新货源
        if (updateDataReq.getDestLongitude() != null || updateDataReq.getLength() != null || updateDataReq.getWide() != null || updateDataReq.getHigh() != null) {
            topFlag = false;
        }
        ResultMsgBean resultMsgBean = transportBusiness.saveGoodsDirectV5930(saveDirectReq, topFlag, true, false, null);

        Map<String, Object> dataMap = (Map<String, Object>) resultMsgBean.getData();
        if (ResultMsgBean.OK == resultMsgBean.getCode()) {
            Long srcMsgId = Long.valueOf(dataMap.get("srcMsgId").toString());
            //开票货源记录发布时货主的企业信息
            if (transport.getInvoiceTransport() != null && transport.getInvoiceTransport() == 1) {
                //直接发布用旧货源ID查询旧货源的开票主体ID和服务商code，然后用新生成的货源ID保存
                TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(transport.getSrcMsgId());
                invoiceTransportService.saveInvoiceTransportEnterpriseData(transport.getUserId(), srcMsgId, transportEnterpriseLog.getInvoiceSubjectId()
                        , transportEnterpriseLog.getServiceProviderCode(), transportEnterpriseLog.getAssignCarTel(), transportEnterpriseLog.getEnterpriseTaxRate()
                        , transportEnterpriseLog.getConsigneeName(), transportEnterpriseLog.getConsigneeTel(), transportEnterpriseLog.getConsigneeEnterpriseName()
                        , transportEnterpriseLog.getPaymentsType(), transportEnterpriseLog.getPrepaidPrice(), transportEnterpriseLog.getCollectedPrice(), transportEnterpriseLog.getReceiptPrice());

            }
            // 记录发布日志
            if (Objects.nonNull(pubTypeEnum)) {
                transportPublishLogService.recordPublishLog(userId, srcMsgId, pubTypeEnum, null);
            }
        }

        //记录价格变动
        transportPublishLogService.changePriceLog(saveDirectReq.getGoodsId(), transport.getPrice(), saveDirectReq.getPublishType(), 6);

        return resultMsgBean;
    }

    @Override
    public ResultMsgBean updateGoodsInfoAdapter(TransportUpdateDataReq updateDataReq) throws Exception {
        Long tsId = updateDataReq.getTsId();
        Transport transport = getByGoodsId(tsId);
        if (Objects.isNull(transport)) {
            return new ResultMsgBean(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "货源不存在");
        }

        Response<InternalWebResult<DirectPublishResultVO>> execute = null;
        String methodName = null;

        // 转一口价，走一口价逻辑
        if (updateDataReq.getPublishType() != null) {
            //调用转一口价转电议
            DirectPublishDTO directPublishDTO = new DirectPublishDTO();
            directPublishDTO.setSrcMsgId(transport.getSrcMsgId());
            directPublishDTO.setIsBackendTransport(0);
            directPublishDTO.setPublishType(updateDataReq.getPublishType());
            methodName = "transfer";
            try {
                execute = transportDirectPublishClient.transfer(directPublishDTO).execute();
            } catch (IOException e) {
                log.info("调用新接口失败，方法：{} 异常", methodName, e);
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }
        }

        // 如果是加价，走加价逻辑。如果货源之前价格不为空，且比原来的价格大，走加价逻辑
        if (StringUtils.isNotBlank(updateDataReq.getPrice())) {
            //调用填价加价
            DirectPublishDTO directPublishDTO = new DirectPublishDTO();
            directPublishDTO.setSrcMsgId(transport.getSrcMsgId());
            directPublishDTO.setIsBackendTransport(0);
            directPublishDTO.setPrice(updateDataReq.getPrice());
            directPublishDTO.setIsCheckAddPriceInterval(true);
            methodName = "updatePrice";
            try {
                execute = transportDirectPublishClient.updatePrice(directPublishDTO).execute();
            } catch (IOException e) {
                log.info("调用新接口失败，方法：{} 异常", methodName, e);
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }
        }

        if (StringUtils.isNotBlank(updateDataReq.getLength()) || StringUtils.isNotBlank(updateDataReq.getWide()) || StringUtils.isNotBlank(updateDataReq.getHigh())) {
            //调用修改长宽高
            UpdateGoodsInfoDTO updateGoodsInfoDTO = new UpdateGoodsInfoDTO();
            updateGoodsInfoDTO.setSrcMsgId(transport.getSrcMsgId());
            updateGoodsInfoDTO.setLength(updateDataReq.getLength());
            updateGoodsInfoDTO.setWide(updateDataReq.getWide());
            updateGoodsInfoDTO.setHigh(updateDataReq.getHigh());
            methodName = "updateGoodsInfo";
            try {
                execute = transportDirectPublishClient.updateGoodsInfo(updateGoodsInfoDTO).execute();
            } catch (IOException e) {
                log.info("调用新接口失败，方法：{} 异常", methodName, e);
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }
        }

        // 处理HTTP调用结果
        if (execute != null) {
            if (execute.isSuccessful() && execute.body() != null) {
                // HTTP调用成功，检查业务code
                InternalWebResult<DirectPublishResultVO> body = execute.body();
                String code = body.getCode();
                String msg = body.getMsg();

                log.info("调用新接口成功，方法：{}，返回code：{}，msg：{}", methodName, code, msg);

                // 使用CodeMappingService处理code转换
                return codeMappingService.handleCodeMapping(methodName, code, msg);
            } else {
                // HTTP调用失败
                log.error("调用新接口HTTP失败，方法：{}，HTTP状态码：{}", methodName, execute.code());
                return ResultMsgBean.failResponse(10001, "接口调用失败");
            }
        }
        return ResultMsgBean.successResponse(null);
    }

    @Override
    public ResultMsgBean adapterTest() {
        try {
            User byUserId = userService.getByUserId(1000004118L);
            LoginUserDTO loginUserDTO = new LoginUserDTO();
            BeanUtils.copyProperties(byUserId, loginUserDTO);
            loginUserDTO.setUserId(byUserId.getId());

            // 2. 序列化 + Base64 编码
            String json = JSON.toJSONString(loginUserDTO);
            String encoded = Base64.getEncoder().encodeToString(json.getBytes(StandardCharsets.UTF_8));

            // 3. 放入 Header
            Map<String, String> headers = new HashMap<>();
            headers.put("x-app-login-user", encoded);

            headers.put("x-app-client-sign", "10001");
            headers.put("x-app-os-version", "10002");
            headers.put("x-app-client-version", "10003");
            headers.put("x-app-client-id", "10004");
            headers.put("client-ip", "10005");
            headers.put("x-app-client-fusion", "10006");

            Response<InternalWebResult<DirectPublishResultVO>> executeError = transportDirectPublishClient.adapterTestError(headers).execute();
            log.info("测试 plat 调用 goods-service，http调用结果 {}, code {}", JSON.toJSONString(executeError.isSuccessful()), JSON.toJSONString(executeError.code()));
            log.info("测试 plat 调用 goods-service，返回内容 {}", JSON.toJSONString(executeError.body()));

            Response<InternalWebResult<DirectPublishResultVO>> execute = transportDirectPublishClient.adapterTest(headers).execute();
            log.info("测试 plat 调用 goods-service 2，http调用结果 {}, code {}", JSON.toJSONString(execute.isSuccessful()), JSON.toJSONString(execute.code()));
            log.info("测试 plat 调用 goods-service 2，返回内容 code: {}  data: {}", JSON.toJSONString(execute.body().getCode()), JSON.toJSONString(execute.body().getMsg()));
        } catch (Exception e) {
            log.info("测试 plat 调用 goods-service，异常返回情况", e);
        }
        return ResultMsgBean.successResponse(null);
    }

    @Override
    public ResultMsgBean freightAddMoneyNumAdapter(Long userId, Long tsId, String price, String clientVersion, String clientSign, Integer isBackendTransport, Integer forceUp, Integer requestSource, int i) {
        Response<InternalWebResult<DirectPublishResultVO>> execute = null;

        Transport transport = getByGoodsId(tsId);
        if (Objects.isNull(transport)) {
            return new ResultMsgBean(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "货源不存在");
        }

        //调用填价加价
        DirectPublishDTO directPublishDTO = new DirectPublishDTO();
        directPublishDTO.setSrcMsgId(transport.getSrcMsgId());
        directPublishDTO.setIsBackendTransport(0);
        directPublishDTO.setPrice(price);
        directPublishDTO.setIsCheckAddPriceInterval(true);

        String methodName = "updatePrice";
        try {
            execute = transportDirectPublishClient.updatePrice(directPublishDTO).execute();
        } catch (IOException e) {
            log.info("调用新接口失败，方法：{} 异常", methodName, e);
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        // 处理HTTP调用结果
        if (execute != null) {
            if (execute.isSuccessful() && execute.body() != null) {
                // HTTP调用成功，检查业务code
                InternalWebResult<DirectPublishResultVO> body = execute.body();
                String code = body.getCode();
                String msg = body.getMsg();

                log.info("调用新接口成功，方法：{}，返回code：{}，msg：{}", methodName, code, msg);

                // 使用CodeMappingService处理code转换
                return codeMappingService.handleCodeMapping(methodName, code, msg);
            } else {
                // HTTP调用失败
                log.error("调用新接口HTTP失败，方法：{}，HTTP状态码：{}", methodName, execute.code());
                return ResultMsgBean.failResponse(10001, "接口调用失败");
            }
        }
        return ResultMsgBean.successResponse(null);
    }

    @Override
    public ResultMsgBean saveDirectAdapter(SaveDirectReq saveDirectReq) {
        TransportMain transportMain = transportMainService.getById(saveDirectReq.getGoodsId());

        DirectPublishDTO directPublishDTO = new DirectPublishDTO();
        directPublishDTO.setSrcMsgId(transportMain.getSrcMsgId());

        String methodName = "directPublish";
        Response<InternalWebResult<DirectPublishResultVO>> execute = null;
        try {
            execute = transportDirectPublishClient.directPublish(directPublishDTO).execute();
        } catch (IOException e) {
            log.info("调用新接口失败，方法：{} 异常", methodName, e);
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        // 处理HTTP调用结果
        if (execute != null) {
            if (execute.isSuccessful() && execute.body() != null) {
                // HTTP调用成功，检查业务code
                InternalWebResult<DirectPublishResultVO> body = execute.body();
                String code = body.getCode();
                String msg = body.getMsg();

                log.info("调用新接口成功，方法：{}，返回code：{}，msg：{}", methodName, code, msg);

                // 使用CodeMappingService处理code转换
                return codeMappingService.handleCodeMapping(methodName, code, msg);
            } else {
                // HTTP调用失败
                log.error("调用新接口HTTP失败，方法：{}，HTTP状态码：{}", methodName, execute.code());
                return ResultMsgBean.failResponse(10001, "接口调用失败");
            }
        }
        return ResultMsgBean.successResponse(null);

    }


    /**
     * 专车距离重新计算
     * => 0-10吨，按照平台车型运距匹配规则匹配运费
     * => 10-32吨，按照大六桥车型运距匹配运费
     * => 32吨以上，按照平台运距规则匹配运费
     * 工单ID:SSRS-3652
     */
    private void reacquireSpecialCarDistance(CalculatePriceBean priceDTO) {
        try {
            double weight = new BigDecimal(priceDTO.getWeight()).doubleValue();
            if (priceDTO.getStartLongitude() != null && priceDTO.getStartLatitude() != null
                    && priceDTO.getDestLongitude() != null && priceDTO.getDestLatitude() != null
                    && 10 <= weight && weight <= 32) {
                NavigationQueryDTO navQueryDTO = new NavigationQueryDTO();
                navQueryDTO.setStartLatitude(priceDTO.getStartLatitude());
                navQueryDTO.setStartLongitude(priceDTO.getStartLongitude());
                navQueryDTO.setDestLatitude(priceDTO.getDestLatitude());
                navQueryDTO.setDestLongitude(priceDTO.getDestLongitude());
                navQueryDTO.setWeight(new BigDecimal(priceDTO.getWeight()));
                log.info("专车距离重新计算开始，navQueryDTO：{}", JSON.toJSONString(navQueryDTO));
                Response<NavigationResultVO> execute = thPriceClient.navigationDistance(navQueryDTO).execute();
                if (execute.isSuccessful() && execute.body() != null) {
                    priceDTO.setDistanceKilometer(execute.body().getDistance());
                }
            }
        } catch (Exception e) {
            log.error("专车距离重新计算异常", e);
        }
    }
}
