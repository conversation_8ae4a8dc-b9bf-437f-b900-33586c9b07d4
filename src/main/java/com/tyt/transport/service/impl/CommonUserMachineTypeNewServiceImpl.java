package com.tyt.transport.service.impl;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCommonUserMachineTypeNew;
import com.tyt.transport.service.CommonUserMachineTypeNewService;
import com.tyt.util.Constant;

@Service("commonUserMachineTypeNewService")
public class CommonUserMachineTypeNewServiceImpl extends BaseServiceImpl<TytCommonUserMachineTypeNew, Long> implements CommonUserMachineTypeNewService {

	@Resource(name = "commonUserMachineTypeNewDao")
	public void setBaseDao(BaseDao<TytCommonUserMachineTypeNew, Long> commonUserMachineTypeNewDao) {
		super.setBaseDao(commonUserMachineTypeNewDao);
	}

	@Override
	public List<TytCommonUserMachineTypeNew> queryCommonUseMachineType(int type) {
		return this.getBaseDao().searchByHql("select new com.tyt.model.TytCommonUserMachineTypeNew(machineTypeName) from TytCommonUserMachineTypeNew where type = ? order by priority asc", new Object[] {type}, 1, Constant.COMMON_USER_MACHINE_TYPE_SIZE);
	}
}
