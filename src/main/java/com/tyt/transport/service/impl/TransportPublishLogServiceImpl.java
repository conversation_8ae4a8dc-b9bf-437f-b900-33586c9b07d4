package com.tyt.transport.service.impl;

import com.tyt.plat.client.transport.ThPriceClient;
import com.tyt.plat.entity.base.TytTransportPublishLog;
import com.tyt.plat.mapper.base.TytTransportPublishLogMapper;
import com.tyt.transport.enums.PubTypeEnum;
import com.tyt.transport.querybean.ChangePriceLogReq;
import com.tyt.transport.service.TransportPublishLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 货源发布记录
 *
 * <AUTHOR>
 * @since 2025-02-18 10:19
 */
@Service
@Slf4j
public class TransportPublishLogServiceImpl implements TransportPublishLogService {
    @Autowired
    private TytTransportPublishLogMapper tytTransportPublishLogMapper;

    @Autowired
    private ThPriceClient thPriceClient;

    /**
     * 记录发布记录
     *
     * @param userId
     * @param srcMsgId
     * @param pubTypeEnum
     */
    @Override
    public void recordPublishLog(Long userId, Long srcMsgId, PubTypeEnum pubTypeEnum, Integer requestSource) {
        try {
            TytTransportPublishLog publishLog = new TytTransportPublishLog();
            publishLog.setUserId(userId);
            publishLog.setSrcMsgId(srcMsgId);
            publishLog.setPubType(pubTypeEnum.getCode());
            publishLog.setPubTime(new Date());
            publishLog.setRequestSource(requestSource);
            tytTransportPublishLogMapper.insertSelective(publishLog);
        } catch (Exception e) {
            log.error("recordPublishLog error, userId:{}, srcMsgId:{}, pubType:{}, requestSource:{}",
                    userId, srcMsgId, pubTypeEnum.getName(), requestSource);
        }
    }

    @Override
    public void changePriceLog(Long srcMsgId, String price, Integer publishType, Integer operationType) {
        try {
            BigDecimal priceDec;
            if (org.apache.commons.lang3.StringUtils.isNotBlank(price)) {
                priceDec = new BigDecimal(price);

            } else {
                priceDec = BigDecimal.ZERO;
            }
            thPriceClient.changePirceLog(new ChangePriceLogReq(srcMsgId, priceDec, publishType, operationType)).execute();
        } catch (IOException e) {
            log.info("运费修改记录失败 srcMsgId:{}, price:{}, publishType:{}, operationType:{}, 错误原因:", srcMsgId, price, publishType, operationType, e);
        }
    }
}
