package com.tyt.transport.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.EmployeeMessage;
import com.tyt.model.EmployeeMessageTmpl;
import com.tyt.transport.service.EmployeeMessageService;
import com.tyt.transport.service.EmployeeMessageTmplService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;

@Service(value = "employeeMessageService")
public class EmployeeMessageServiceImpl extends BaseServiceImpl<EmployeeMessage, Long> implements EmployeeMessageService {
	public Logger logger = LoggerFactory.getLogger("EmployeeMessageServiceImpl");

	@Resource(name = "employeeMessageDao")
	public void setBaseDao(BaseDao<EmployeeMessage, Long> EmployeeMessageDao) {
		super.setBaseDao(EmployeeMessageDao);
	}
	@Resource(name="employeeMessageTmplService")
	private EmployeeMessageTmplService employeeMessageTmplService;
	
	@Override
	public void saveEmpMessage(Long mtId,Long srcMsgId,String goodsContent, Integer ecaStatus, String signature, String userName,Long createUserId,String createUserName) {
		String titleTmpl=null;
		String contentTmpl=null;
		Long tmplId=0L;
		if (ecaStatus==3) {
			EmployeeMessageTmpl finishTmpl = employeeMessageTmplService.getEmployeeMessageTmpl(Constant.ECA_MESSAGE_TEMPLET_FINISH);
			titleTmpl=finishTmpl.getTitle();
			contentTmpl=finishTmpl.getContent();
			tmplId=finishTmpl.getId();
		}else if(ecaStatus==2){
			EmployeeMessageTmpl signTmpl = employeeMessageTmplService.getEmployeeMessageTmpl(Constant.ECA_MESSAGE_TEMPLET_SIGNING);
			titleTmpl=signTmpl.getTitle();
			contentTmpl=signTmpl.getContent();
			tmplId=signTmpl.getId();
		}
		EmployeeMessage empMsg=installEcaMessage(mtId, srcMsgId, goodsContent, ecaStatus, signature, userName, createUserId, createUserName, titleTmpl, contentTmpl, 12, tmplId);
		if (empMsg!=null) {
			this.add(empMsg);
		}
	}

	private EmployeeMessage installEcaMessage(Long mtId,Long srcMsgId,String goodsContent, Integer ecaStatus, String signature, String userName,Long createUserId,String createUserName,String ecaTitleTemplate,String ecaContentTemplate, Integer taskContentMaxLength, Long tmplId) {
		EmployeeMessage tracerMessage = new EmployeeMessage();
		String title;
		String content;
		String titleContent = goodsContent.length() > taskContentMaxLength ? goodsContent.substring(0, taskContentMaxLength) + "..." : goodsContent;
		String url = "/manage_new/back/model/html/peopleorders/goodsDetails.html?id=" + mtId + "&tsId="+srcMsgId+"&srcMsgId=" + srcMsgId + "&domIndex=168";
		title = StringUtils.replaceEach(ecaTitleTemplate, new String[] { "${content}", "${url}", "${signature}" }, new String[] { titleContent, url, signature });
		tracerMessage.setTitle(title);
		content = StringUtils.replaceEach(ecaContentTemplate, new String[] { "${signature}","${userName}", "${time}" }, new String[] { signature, userName, TimeUtil.formatDateTime(new Date()) });
		tracerMessage.setTmplId(tmplId);
		tracerMessage.setContent(content);
		tracerMessage.setCtime(new Date());
		// 设置更新时间
		tracerMessage.setUtime(new Date());
		// 设置推送状态
		tracerMessage.setPushStatus(0);
		// 设置业务操作时间
		tracerMessage.setOperationTime(new Date());
		// 设置消息类型
		tracerMessage.setType(1);
		tracerMessage.setUserId(createUserId);
		// 设置读取状态为未读
		tracerMessage.setReadStatus(0);
		tracerMessage.setUserName(createUserName);
		return tracerMessage;
	}

	@Override
	public void saveCorpMessageForPub(Long userId, String userName, String corpName, String ctime, String startPoint, String destPoint, String taskContent, String tsId) {
		logger.info("save corp pub transport inmail start ------ userid="+userId+",userName="+userName);
		EmployeeMessage tracerMessage = new EmployeeMessage();
		setMessageInfo(tracerMessage,userId,userName);

		//获取企业发货通知对接人模板
		EmployeeMessageTmpl addTrancerTmpl = employeeMessageTmplService.getEmployeeMessageTmpl(Constant.CORP_PUB_TRANSPORT_MESSAGE);
		//模板ID
		tracerMessage.setTitle(addTrancerTmpl.getTitle());
		//内容
		String contentMsg=startPoint+"-"+destPoint+","+taskContent;
		tracerMessage.setTmplId(addTrancerTmpl.getId());
		String url="/back/model/html/company/companyGoods.html?id="+tsId;
		String content = StringUtils.replaceEach(addTrancerTmpl.getContent(), new String[] { "${corpName}", "${ctime}","${url}", "${content}" }, new String[] { corpName, ctime, url,contentMsg });
		tracerMessage.setContent(content);
		//模板ID
		tracerMessage.setTmplId(addTrancerTmpl.getId());
		tracerMessage.setType(addTrancerTmpl.getType());//企业
//		tracerMessage.setPushUserId();//
		tracerMessage.setPushUserName(corpName);
		this.add(tracerMessage);
		logger.info("save corp pub transport inmail success");
		
	}

	@Override
	public void saveCorpMessageForCannel(Long userId, String userName, String corpName, String ctime, String startPoint, String destPoint, String taskContent, String tsId) {
		logger.info("save corp pub transport Cannel start ------ userid="+userId+",userName="+userName);
		EmployeeMessage tracerMessage = new EmployeeMessage();
		setMessageInfo(tracerMessage,userId,userName);
		//获取企业下架通知对接人模板
		EmployeeMessageTmpl addTrancerTmpl = employeeMessageTmplService.getEmployeeMessageTmpl(Constant.CORP_TRANSPORT_CANNEL_MESSAGE);
		//模板ID
		tracerMessage.setTitle(addTrancerTmpl.getTitle());
		//内容
		String contentMsg=startPoint+"-"+destPoint+","+taskContent;
		tracerMessage.setTmplId(addTrancerTmpl.getId());
        String url="/back/model/html/company/companyGoods.html?id="+tsId;
		String content = StringUtils.replaceEach(addTrancerTmpl.getContent(), new String[] { "${corpName}", "${ctime}","${url}", "${content}" }, new String[] { corpName,TimeUtil.formatDateTime(new Date()), url,contentMsg });
		tracerMessage.setContent(content);
		//模板ID
		tracerMessage.setTmplId(addTrancerTmpl.getId());
		tracerMessage.setType(addTrancerTmpl.getType());//企业
//		tracerMessage.setPushUserId();//
		tracerMessage.setPushUserName(corpName);
		this.add(tracerMessage);
	}
	public void setMessageInfo(EmployeeMessage tracerMessage,Long userId, String userName){
		tracerMessage.setUserId(userId);
		tracerMessage.setUserName(userName);
		tracerMessage.setCtime(new Date());
		// 设置更新时间
		tracerMessage.setUtime(new Date());
		// 设置推送状态
		tracerMessage.setPushStatus(0);
		// 设置业务操作时间
		tracerMessage.setOperationTime(new Date());
		// 设置读取状态为未读
		tracerMessage.setReadStatus(0);
	}
}
