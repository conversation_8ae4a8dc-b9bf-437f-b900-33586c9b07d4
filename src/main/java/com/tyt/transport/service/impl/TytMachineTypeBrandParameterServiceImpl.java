package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytMachineTypeBrandParameter;
import com.tyt.transport.service.TytMachineTypeBrandParameterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service("tytMachineTypeBrandParameterService")
public class TytMachineTypeBrandParameterServiceImpl extends BaseServiceImpl<TytMachineTypeBrandParameter, Integer> implements TytMachineTypeBrandParameterService {

    @Override
    @Resource(name = "tytMachineTypeBrandParameterDao")
    public void setBaseDao(BaseDao<TytMachineTypeBrandParameter, Integer> tytMachineTypeBrandParameterDao) {
        super.setBaseDao(tytMachineTypeBrandParameterDao);
    }

    @Override
    public List<TytMachineTypeBrandParameter> getKeywordShortList(String hql,final Object[] params){
        List<TytMachineTypeBrandParameter> list = this.getBaseDao().queryForList(hql,params);
        return list;
    }
}
