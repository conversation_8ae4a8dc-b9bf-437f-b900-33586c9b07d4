package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.cache.CacheService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.UserPermission;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.TytTransportTecServiceFee;
import com.tyt.plat.mapper.base.TytTransportOrdersMapper;
import com.tyt.plat.mapper.base.TytTransportTecServiceFeeMapper;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.vo.axb.AxbBindReq;
import com.tyt.plat.vo.axb.AxbBindVO;
import com.tyt.plat.vo.axb.AxbInfoVO;
import com.tyt.plat.vo.axb.AxbUserFieldParam;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.transport.bean.PrivacyPhoneNumGoodIdReq;
import com.tyt.transport.service.PrivacyPhoneNumService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class PrivacyPhoneNumServiceImpl implements PrivacyPhoneNumService {

    private static final Logger logger = LoggerFactory.getLogger(PrivacyPhoneNumServiceImpl.class);

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private CommonApiService commonApiService;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private TransportBusiness transportBusiness;

    @Autowired
    private TytTransportOrdersMapper tytTransportOrdersMapper;

    @Autowired
    private TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    @Resource(name =  "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Autowired
    private AbtestService abtestService;

    @Override
    public ResultMsgBean getPrivacyPhoneNum(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq, ResultMsgBean resultMsgBean) {
        if (privacyPhoneNumGoodIdReq == null || resultMsgBean == null
                || privacyPhoneNumGoodIdReq.getGoodId() == null || privacyPhoneNumGoodIdReq.getOperateType() == null || privacyPhoneNumGoodIdReq.getDriverUserId() == null
                || StringUtils.isBlank(privacyPhoneNumGoodIdReq.getGoodUserPhone()) || StringUtils.isBlank(privacyPhoneNumGoodIdReq.getDriverUserPhone())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "请求参数错误");
        }

        boolean operateTypeIsLegal = checkBizType(privacyPhoneNumGoodIdReq.getOperateType());
        if (!operateTypeIsLegal) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "请求参数错误");
        }

        String PrivacyPhoneNum = null;
        try {
            int operateType = privacyPhoneNumGoodIdReq.getOperateType();
            int expireTimeSec = 0;
            if (operateType == 1) {
                //找货
                Integer expireTimeConfig = tytConfigService.fetchRecommendIntValue("T_PRIVACY_PHONE_NUM_EXPIRE_TIME", 3);
                expireTimeSec = expireTimeConfig * 60 * 60;
            } else if (operateType == 2) {
                //订单
                Integer expireTimeConfig = tytConfigService.fetchRecommendIntValue("O_PRIVACY_PHONE_NUM_EXPIRE_TIME", 10);
                expireTimeSec = expireTimeConfig * 24 * 60 * 60;
            }

            List<AxbInfoVO> axbInfo = commonApiService.getAxbInfo(1, privacyPhoneNumGoodIdReq.getGoodId()
                    , privacyPhoneNumGoodIdReq.getDriverUserPhone(), privacyPhoneNumGoodIdReq.getGoodUserPhone());
            boolean isHavePrivacyPhoneNum = false;

            if (axbInfo != null && !axbInfo.isEmpty()) {
                for (AxbInfoVO axbInfoVO : axbInfo) {
                    if (StringUtils.isNotBlank(axbInfoVO.getExtraField())
                            && axbInfoVO.getExpirationDate() != null
                            && privacyPhoneNumGoodIdReq.getDriverUserId().toString().equals(axbInfoVO.getExtraField())
                            && new Date().before(axbInfoVO.getExpirationDate())) {
                        isHavePrivacyPhoneNum = true;
                        PrivacyPhoneNum = axbInfoVO.getTelX();
                        break;
                    }
                }
            }
            if (!isHavePrivacyPhoneNum) {
                String axbUserFieldParamJsonString = makeAXBUserFieldParam(privacyPhoneNumGoodIdReq.getDriverUserId(), privacyPhoneNumGoodIdReq.getGoodId());
                logger.info("构造虚拟号额外参数，内容：{}", axbUserFieldParamJsonString);

                int tianrunAccount = makeTianRunAccountChoose(privacyPhoneNumGoodIdReq);

                if (tianrunAccount != 0) {
                    logger.info("绑定虚拟号，使用天润账号：{}", tianrunAccount);
                    AxbBindVO axbBindVO = commonApiService.axbBind(new AxbBindReq(privacyPhoneNumGoodIdReq.getDriverUserPhone()
                            , privacyPhoneNumGoodIdReq.getGoodUserPhone(), 1, privacyPhoneNumGoodIdReq.getGoodId(), expireTimeSec
                            , privacyPhoneNumGoodIdReq.getDriverUserId().toString(), axbUserFieldParamJsonString, tianrunAccount));
                    if (axbBindVO != null && StringUtils.isNotBlank(axbBindVO.getTelX())) {
                        PrivacyPhoneNum = axbBindVO.getTelX();
                    }
                } else  {
                    logger.info("不绑定虚拟号");
                }

            }
        } catch (Exception e) {
            logger.error("getPrivacyPhoneNum 获取虚拟号流程执行失败 原因：" + e);
            return new ResultMsgBean(ReturnCodeConstant.GET_PRIVACY_PHONE_NUM_ERROR, "获取虚拟号流程执行失败");
        }
        resultMsgBean.setData(PrivacyPhoneNum);
        resultMsgBean.setCode(ReturnCodeConstant.OK);
        return resultMsgBean;
    }

    private int makeTianRunAccountChoose(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq) {
        //抽佣货源
        boolean isCommissionTransport = false;
        TransportMain transportMain = transportMainService.getTransportMainForId(privacyPhoneNumGoodIdReq.getGoodId());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(transportMain.getLabelJson())) {
            TransportLabelJson transportLabelJson = transportBusiness.getTransportLabelJson(transportMain.getLabelJson());
            if (transportLabelJson != null && transportLabelJson.getCommissionTransport() != null && transportLabelJson.getCommissionTransport() == 1) {
                TytTransportTecServiceFee transportTecServiceFee = tytTransportTecServiceFeeMapper.getBySrcMsgId(transportMain.getSrcMsgId());
                if (transportTecServiceFee != null) {
                    isCommissionTransport = (transportTecServiceFee.getMemberShowPrivacyPhoneTab() == null || transportTecServiceFee.getMemberShowPrivacyPhoneTab() == 1);
                } else {
                    isCommissionTransport = true;
                    logger.info("绑定虚拟号 符合抽佣货源条件");
                }
            }
        }

        //首履用户
        boolean firstHonourAnAgreementboolean = false;
        Integer firstHonourAnAgreement = tytTransportOrdersMapper.firstHonourAnAgreement(transportMain.getUserId());
        if (firstHonourAnAgreement == null || firstHonourAnAgreement == 0) {
            firstHonourAnAgreementboolean = true;
            logger.info("绑定虚拟号 符合首履货主条件");
        }

        //首履货主或者抽佣货源
        if (firstHonourAnAgreementboolean || isCommissionTransport) {
            return  1;
        }

        //在货主ab测试中，使用账号2
        List<String> abTestCodeList = new ArrayList<>();
        abTestCodeList.add("privacy_phone_tab_show_transport_abtest");
        List<TytAbtestConfigVo> userTypeList = abtestService.getUserTypeList(abTestCodeList, transportMain.getUserId());
        if (CollectionUtils.isNotEmpty(userTypeList)) {
            if (userTypeList.get(0).getType() == 1) {
                logger.info("绑定虚拟号 符合特殊货主AB测试条件");
                return 2;
            }
        }

        return 0;
    }

    private boolean checkBizType(Integer bizType) {
        return bizType == 1 || bizType == 2;
    }

    @Override
    public String makeAXBUserFieldParam(Long driverUserId, Long goodsId) {
        try {
            TransportMain transportMain = transportMainService.getById(goodsId);
            AxbUserFieldParam axbUserFieldParam = new AxbUserFieldParam();
            if (transportMain != null) {
                axbUserFieldParam.setSrcMsgId(transportMain.getSrcMsgId());
                axbUserFieldParam.setTransportUserId(transportMain.getUserId());
                axbUserFieldParam.setCarUserId(driverUserId);
                axbUserFieldParam.setPrice(transportMain.getPrice());
                axbUserFieldParam.setInfoFee(transportMain.getInfoFee() != null ? transportMain.getInfoFee().toString() : null);
                axbUserFieldParam.setTecServiceFee(transportMain.getTecServiceFee() != null ? transportMain.getTecServiceFee().toString() : null);
                axbUserFieldParam.setRefundFlag(transportMain.getRefundFlag() != null ? transportMain.getRefundFlag() == 0 ? "不退还" : "退还" : null);
                axbUserFieldParam.setDistance(transportMain.getDistance());
                axbUserFieldParam.setStartPoint(transportMain.getStartPoint());
                axbUserFieldParam.setDestPoint(transportMain.getDestPoint());
                List<String> transportTypeList = makeAXBUserFieldParamTransportTypeParam(transportMain);
                if (CollectionUtils.isNotEmpty(transportTypeList)) {
                    axbUserFieldParam.setTransportType(transportTypeList);
                }
                return JSON.toJSONString(axbUserFieldParam);
            }
        } catch (Exception e) {
            logger.info("构造虚拟号额外参数异常，原因：", e);
            return null;
        }
        return null;
    }

    private List<String> makeAXBUserFieldParamTransportTypeParam(TransportMain transportMain) {
        List<String> result = new ArrayList<>();
        if (transportMain.getSourceType() != null) {
            if (transportMain.getSourceType() == 4) {
                result.add("YMM同步货源");
            }
        }
        if (transportMain.getExcellentGoods() != null) {
            if (transportMain.getExcellentGoods() == 1) {
                result.add("优车1.0");
            } else if (transportMain.getExcellentGoods() == 2) {
                result.add("专车");
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(transportMain.getLabelJson())) {
            TransportLabelJson transportLabelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson != null && transportLabelJson.getGoodCarPriceTransport() != null && transportLabelJson.getGoodCarPriceTransport() == 1) {
                result.add("优车2.0");
            }
        }
        if (CollectionUtils.isEmpty(result)) {
            result.add("普通");
        }
        return result;
    }

}
