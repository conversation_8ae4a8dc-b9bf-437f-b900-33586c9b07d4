package com.tyt.transport.service.impl;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import com.tyt.transport.querybean.FallShortSearchLogBean;
import com.tyt.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.MDC;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.model.TytConfig;
import com.tyt.transport.service.TransportLogUtilService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.TimeUtil;

@Service("transportLogUtilService")
public class TransportLogUtilServiceImpl implements TransportLogUtilService {
	static final Logger gatherLog = LoggerFactory.getLogger("gatherLog");

	static final Logger detailsLog = LoggerFactory.getLogger("detailsLog");
	static final Logger searchLog = LoggerFactory.getLogger("searchLog");
	static final Logger searchDistanceSortLog = LoggerFactory
			.getLogger("distanceSortLog");
    static final Logger searchFallShortLog = LoggerFactory.getLogger("searchFallShortLog");
	// static final Logger callPhoneLog = LoggerFactory
	// .getLogger("callPhoneLog");

	static final String nullValue = "";

	static Map<String, TytConfig> logMap = null;

	//刷新间隔 5分钟
	  public static final long refreshInterval=300000;
	  public static long refreshTime=0l;

	@Resource(name = "tytConfigService")
	public TytConfigService tytConfigService;

	// getConfigMap
	@Override
	public void detailsLog(long userId, long tsId, String clientVersion,
			int clientSign, int status, int viewSource, int sortType, int sortIndex, int specialMark,
			String startProvinc, String startCity, String startArea, String destProvinc, String destCity, String destArea, String benefitLabelCode, Long sharerUserId) {
		try {
			if (isRecordLog("detailsLog")) {

				String dateStr = TimeUtil.formatDateTime(new Date());
				clientVersion = (null == clientVersion || "".equals(clientVersion)) ? "" : clientVersion;
				startProvinc = startProvinc == null ? "" : startProvinc;
				startCity = startCity == null ? "" : startCity;
				startArea = startArea == null ? "" : startArea;
				destProvinc = destProvinc == null ? "" : destProvinc;
				destCity = destCity == null ? "" : destCity;
				destArea = destArea == null ? "" : destArea;
				benefitLabelCode = benefitLabelCode == null ? "" : benefitLabelCode;
				sharerUserId = sharerUserId == null ? 0L : sharerUserId;
				detailsLog.info("{},{},'{}',{},{},'{}',{},'{}',{},{},'{}','{}','{}','{}','{}','{}','{}','{}'", userId, tsId,
						clientVersion, clientSign, status, dateStr, viewSource, sortType, sortIndex, specialMark, startProvinc, startCity, startArea, destProvinc, destCity, destArea, benefitLabelCode, sharerUserId);
			}
		} catch (Exception e) {
			gatherLog.error("保存detailsLog日志异常", e);
		}
	}

	@Override
	public void searchLog(long userId, String startCoord, String startRange,
			String destCoord, String destRange, long carId, String headNo,
			String headCity, String clientVersion, int clientSign, int sortType,String numberType,String osVersion,String clientId, String carLength, String carType, String specialRequired,String startWeight,String endWeight) {
		try {
			if (isRecordLog("searchLog")&&isRecordLog(clientVersion,clientSign)) {

				String dateStr = TimeUtil.formatDateTime(new Date());
				startCoord = startCoord.replaceAll(",", "_");
				startRange = (null == startRange || "".equals(startRange)) ? "300"
						: startRange;

				if (null != destCoord && !"".equals(destCoord)) {
					destRange = (null == destRange || "".equals(destRange)) ? "300"
							: destRange;
					destCoord = destCoord.replaceAll(",", "_");
				} else {
					destCoord = nullValue;
					destRange = "0";
				}

				if (carId == 0) {
					headNo = nullValue;
					headCity = nullValue;
				} else {
					headNo = (null == headNo || "".equals(headNo)) ? nullValue
							: headNo;
					headCity = (null == headCity || "".equals(headCity)) ? nullValue
							: headCity;
				}

				clientVersion = (null == clientVersion || ""
						.equals(clientVersion)) ? nullValue : clientVersion;
				numberType = (null == numberType || ""
						.equals(numberType)) ? "2" : numberType;//numberType:1首次查询2非首次查询
				osVersion = (null == osVersion || ""
						.equals(osVersion)) ? nullValue: osVersion;//操作系统版本号（能获取到就填上）
				clientId = (null == clientId || ""
						.equals(clientId)) ? nullValue : clientId;//终端唯一标识（能获取到就填上）
				carLength = (null == carLength || ""
						.equals(carLength)) ? nullValue : carLength;//卡车长度（能获取到就填上）
				carType = (null == carType || ""
						.equals(carType)) ? nullValue : carType;//卡车类型（能获取到就填上）
				specialRequired = (null == specialRequired || ""
						.equals(specialRequired)) ? nullValue : specialRequired;//特殊要求（能获取到就填上）
                startWeight = (null == startWeight || ""
                        .equals(startWeight)) ? nullValue : startWeight;//特殊要求（能获取到就填上）
                endWeight = (null == endWeight || ""
                        .equals(endWeight)) ? nullValue : endWeight;//特殊要求（能获取到就填上）

				searchLog.info(
						"{},'{}',{},'{}',{},{},'{}','{}','{}',{},{},'{}','{}','{}','{}','{}','{}','{}','{}','{}'",
						userId, startCoord, startRange, destCoord, destRange,
						carId, headNo, headCity, clientVersion, clientSign,
						sortType, dateStr,numberType,osVersion,clientId, carLength, carType, specialRequired,startWeight,endWeight);
			}
		} catch (Exception e) {
			gatherLog.error("保存searchLog日志异常", e);
		}
	}

	@Override
	public void searchDistanceSortLog(long userId, int sortType,
			String clientVersion, int clientSign) {
		try {
			if (isRecordLog("distanceSortLog")&&isRecordLog(clientVersion,clientSign)) {
				String dateStr = TimeUtil.formatDateTime(new Date());

				clientVersion = (null == clientVersion || ""
						.equals(clientVersion)) ? nullValue : clientVersion;
				MDC.put("userId", userId);
				MDC.put("sortType", sortType);
				MDC.put("clientVersion", clientVersion);
				MDC.put("clientSign", clientSign);
				MDC.put("dateStr", dateStr);

				searchDistanceSortLog.info("");
			}

			// searchDistanceSortLog.info("{},{},{},{},{}", userId,
			// sortType, clientVersion,clientSign,dateStr);

		} catch (Exception e) {
			gatherLog.error("保存DistanceSortLog日志异常", e);
		}

	}

	@Override
	public void callPhoneLog(long userId, long tsId, String clientVersion,
			int clientSign) {
		try {
			String dateStr = TimeUtil.formatDateTime(new Date());

			clientVersion = (null == clientVersion || "".equals(clientVersion)) ? nullValue
					: clientVersion;

			// callPhoneLog.info("{},{},{},{},{}", userId,
			// tsId, clientVersion,clientSign,dateStr);

		} catch (Exception e) {
			gatherLog.error("保存callPhoneLog日志异常", e);
		}
	}

    @Override
    public void fallShortSearchLog(FallShortSearchLogBean bean) {
        try {
            if (isRecordLog(bean.getClientVersion(),Integer.parseInt(bean.getClientSign()))) {

                String dateStr = TimeUtil.formatDateTime(new Date());
                Long userId = bean.getUserId();
                String startDistance = "";
                if (StringUtils.isNotBlank(bean.getStartCoord())){
                    startDistance = bean.getStartDistance();
                }
                String startCoord = "";
                if (StringUtils.isNotBlank(bean.getStartCoord())){
                    startCoord = bean.getStartCoord().replaceAll(",", "_");
                }
                String startProvinc = bean.getStartProvinc();
                String startCity = "";
                if (StringUtils.isNotBlank(bean.getStartCity())){
                    startCity = bean.getStartCity();
                }
                String startArea = "";
                if (StringUtils.isNotBlank(bean.getStartArea())){
                    startArea = bean.getStartArea();
                }
                String clientVersion = (StringUtils.isBlank(bean.getClientVersion())) ? nullValue : bean.getClientVersion();
                int clientSign = Integer.parseInt(bean.getClientSign());
                int numberType = bean.getNumberType();
                String osVersion = (StringUtils.isBlank(bean.getOsVersion())) ? nullValue: bean.getOsVersion();//操作系统版本号（能获取到就填上）
                String clientId = (StringUtils.isBlank(bean.getClientId())) ? nullValue : bean.getClientId();//终端唯一标识（能获取到就填上）

                searchFallShortLog.info(
                        "{},'{}','{}','{}','{}','{}','{}',{},{},'{}','{}',{},'{}'",
                        userId, startCoord, startDistance, startProvinc, startCity,
                        startArea, clientVersion,clientSign, numberType,
                        osVersion,clientId,bean.getSearchType(),dateStr);
            }
        } catch (Exception e) {
            gatherLog.error("保存searchFallShortLog日志异常", e);
        }
    }

	public boolean isRecordLog(String clientVersion,
			int clientSign){

		if("8.8.8.8".equals(clientVersion)&&6==clientSign)
		{
			return false;
		}

		return true;
	}

	/**
	 * 是否记录日志
	 * @param KeyName
	 * @return
	 */
	public boolean isRecordLog(String keyName) {

//		if((System.currentTimeMillis()-refreshTime)>refreshInterval){
//			logMap=tytConfigService.getConfigMap();
//			refreshTime=System.currentTimeMillis();
//		}
//		if(logMap==null||logMap.size()<1){
//			logMap=tytConfigService.getConfigMap();
//			refreshTime=System.currentTimeMillis();
//		}
//
		TytConfig tytConfig=tytConfigService.getValue(keyName);// logMap==null?null:logMap.get(keyName)==null?null:logMap.get(keyName).getValue();

		String value =tytConfig==null?null:tytConfig.getValue();
		if ("1".equals(value)) {
			return true;
		}
		return false;
	}
}
