package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.model.TransportVary;
import com.tyt.transport.dao.TransportVaryDao;
import com.tyt.transport.querybean.TransportVaryBean;
import com.tyt.transport.service.TransportVaryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:16
 */
@Service("transportVaryService")
public class TransportVaryServiceImpl extends BaseServiceImpl<TransportVary, Long> implements TransportVaryService {

    @Resource(name = "transportVaryDao")
    public void setBaseDao(BaseDao<TransportVary, Long> transportVaryDao) {
        super.setBaseDao(transportVaryDao);
    }

    @Override
    public List<TransportVaryBean> getTransportVaryForDate(long maxId) {
        int pageSize = AppConfig.getIntProperty(
                "tyt.tyt_transport.vary.page.size").intValue();
        Object[] objs = null;
        StringBuffer sb = new StringBuffer("select * from tyt_transport_vary where 1=1 and status<>2 and status<>3 ");
        if (maxId > 0) {
            sb.append(" and id>?");
            sb.append(" and id<=? ");
            objs = new Object[]{maxId, (maxId + pageSize)};
            sb.append(" order by id desc");
        } else {
            pageSize = 1;
            sb.append(" order by id desc");
        }

        List<TransportVaryBean> list = null;

        // 查询数据集
        List<TransportVary> transportVaryList = ((TransportVaryDao) this.getBaseDao())
                .search(sb.toString(), objs, 1, pageSize);

        if (CollectionUtils.isNotEmpty(transportVaryList)) {
            list = new ArrayList<TransportVaryBean>();
            for (TransportVary transportVary : transportVaryList) {
                TransportVaryBean transportVaryBean = new TransportVaryBean();
                BeanUtils.copyProperties(transportVary, transportVaryBean);

				if(maxId <= 0){
					transportVaryBean.setTsId(0L);
				}
                list.add(transportVaryBean);
            }
        }
        return list;
    }

    @Override
    public void addVarys(List<Long> idList, Integer status) throws Exception {
        try {
            for (Long id : idList) {
                this.add(new TransportVary(id, status));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
