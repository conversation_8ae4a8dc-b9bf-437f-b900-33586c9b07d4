package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytShareRec;
import com.tyt.transport.service.ShareRecService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:10
 */
@Service("shareRecService")
public class ShareRecServiceImpl extends BaseServiceImpl<TytShareRec, Long> implements ShareRecService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "userService")
    private UserService userService;


    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;


    @Resource(name = "shareRecDao")
    public void setBaseDao(BaseDao<TytShareRec, Long> SttLimitDao) {
        super.setBaseDao(SttLimitDao);
    }

    @Override
    public void addShare(TytShareRec rec) {
        rec.setCtime(new Date());
        this.add(rec);
        logger.info("用户{}分享货源到{}，地址：{}", rec.getUserId(), rec.getShareTarget(), rec.getShareLink());
    }
}
