package com.tyt.transport.service.impl;


import com.tyt.plat.entity.base.TytCoverGoodsConfig;
import com.tyt.plat.mapper.base.TytCoverGoodsConfigMapper;
import com.tyt.transport.enums.CoverGoodsEnum;
import com.tyt.transport.enums.GoodsCheckItemEnum;
import com.tyt.transport.enums.YesOrNoEnum;
import com.tyt.transport.service.CoverGoodsConfigService;
import com.tyt.transport.service.TytCoverGoodsDialConfigService;
import com.tyt.transport.vo.CoverGoodsConfigVo;
import com.tyt.transport.vo.CoverGoodsVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: helian
 * @since: 2024/01/11 15:59
 */
@Service("coverGoodsConfigService")
public class CoverGoodsConfigServiceImpl implements CoverGoodsConfigService {

    @Autowired
    private TytCoverGoodsConfigMapper tytCoverGoodsConfigMapper;

    @Autowired
    private TytCoverGoodsDialConfigService tytCoverGoodsDialConfigService;

    @Override
    public CoverGoodsVo getConfigList() {
        CoverGoodsVo coverGoodsVo = new CoverGoodsVo();
        List<CoverGoodsConfigVo> goodsConfigVoList = new ArrayList<>();
        List<TytCoverGoodsConfig> coverGoodsConfigList = tytCoverGoodsConfigMapper.selectList();
        coverGoodsConfigList.forEach(c -> {
            CoverGoodsConfigVo coverGoodsConfigVo = new CoverGoodsConfigVo();
            BeanUtils.copyProperties(c, coverGoodsConfigVo);
            if (CoverGoodsEnum.GOODS_CHECK.getConfigCode().equals(c.getConfigCode()) && c.getEnable().equals(YesOrNoEnum.N.getCode())) {
                coverGoodsConfigVo.setConfigItem(GoodsCheckItemEnum.DEFAULT_NO_CHECK.getConfigItem());
            }
            if (CoverGoodsEnum.SWITCH_GUIDE.getConfigCode().equals(c.getConfigCode()) && c.getEnable().equals(YesOrNoEnum.N.getCode())) {
                coverGoodsConfigVo.setConfigItem(YesOrNoEnum.N.getCode());
            }
            if (CoverGoodsEnum.EXCELLENT_COVER.getConfigCode().equals(c.getConfigCode()) && c.getEnable().equals(YesOrNoEnum.N.getCode())) {
                coverGoodsConfigVo.setConfigItem(YesOrNoEnum.N.getCode());
            }
            goodsConfigVoList.add(coverGoodsConfigVo);
        });
        coverGoodsVo.setList(goodsConfigVoList);

        // 获取该货主的捂货时间
        Integer maxXSecond = tytCoverGoodsDialConfigService.selectXTime();
        coverGoodsVo.setXTime(maxXSecond/60);

        return coverGoodsVo;
    }

    /**
     * 根据code获取配置
     */
    @Override
    public TytCoverGoodsConfig getByCode(Integer code) {
        Example exa = new Example(TytCoverGoodsConfig.class);
        exa.and().andEqualTo("configCode", code)
                .andEqualTo("enable", 1);
        List<TytCoverGoodsConfig> configs = tytCoverGoodsConfigMapper.selectByExample(exa);
        if (CollectionUtils.isNotEmpty(configs)) {
            return configs.get(0);
        }
        return null;
    }


}
