package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.FreightRoute;
import com.tyt.transport.service.FreightRouteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("freightRouteService")
public class FreightRouteServiceImpl extends BaseServiceImpl<FreightRoute, Long> implements FreightRouteService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "freightRouteDao")
	public void setBaseDao(BaseDao<FreightRoute, Long> freightRouteDao) {
		super.setBaseDao(freightRouteDao);
	}

	@Override
	public FreightRoute queryByKey(String key) {
		List<FreightRoute> freightRoutes = this.getBaseDao().searchByHql("from FreightRoute where mapKey=?", new Object[] { key }, null, null);
		return freightRoutes.size() == 1 ? freightRoutes.get(0) : null;
	}
}
