package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.tyt.infofee.enums.SourceTypeEnum;
import com.tyt.model.EmployeeMessage;
import com.tyt.plat.entity.base.TytMbCargoSyncInfo;
import com.tyt.plat.entity.base.TytMbCargoSyncLog;
import com.tyt.plat.entity.base.TytTransportMbMerge;
import com.tyt.plat.entity.base.TytTransportVO;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.plat.utils.DateUtil;
import com.tyt.transport.enums.CarStyleEnum;
import com.tyt.transport.enums.YesOrNoEnum;
import com.tyt.transport.querybean.DispatchParamBean;
import com.tyt.transport.querybean.TransportYmmGoodsBean;
import com.tyt.transport.querybean.TransportYmmListBean;
import com.tyt.transport.querybean.TransportYmmListReqBean;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportYMMService;
import com.tyt.util.TytClassUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 运满满业务处理实现类
 * @date 2023-08-17-09-27-26
 */
@Service
@Slf4j
public class TransportYMMServiceImpl implements TransportYMMService {

    @Autowired
    private TytTransportMbMergeMapper tytTransportMbMergeMapper;
    @Autowired
    private TytTransportMapper tytTransportMapper;
    @Autowired
    private TransportMainService transportMainService;
    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Autowired
    private EmployeeMessageMapper employeeMessageMapper;
    @Autowired
    private TytTransportDispatchMapper tytTransportDispatchMapper;

    @Autowired
    private TytMbCargoSyncInfoMapper tytMbCargoSyncInfoMapper;

    @Autowired
    private TytMbCargoSyncLogMapper tytMbCargoSyncLogMapper;


    /**
     * @param dispatchParamBean
     * @return
     * @Description 撤销处理bean
     * <AUTHOR>
     * @Version 1.0
     */
    @Override
    public DispatchParamBean revokeBeanHandle(DispatchParamBean dispatchParamBean) {
        if (dispatchParamBean == null) {
            log.info("[代调-plat] 撤销参数不合法:{}", JSON.toJSONString(dispatchParamBean));
            return new DispatchParamBean();
        }
        Integer sourceType = dispatchParamBean.getSourceType();
        Long cargoId = dispatchParamBean.getCargoId();
        if (sourceType != null && SourceTypeEnum.运满满货源.getId().equals(sourceType) && cargoId != null) {
            TytTransportMbMerge tytTransportMbMerge =
                    tytTransportMbMergeMapper.selectByCargoId(dispatchParamBean.getCargoId());

            Optional.ofNullable(tytTransportMbMerge)
                    .map(TytTransportMbMerge::getSrcMsgId)
                    .map(tytTransportMapper::selectTransportById)
                    .ifPresent(it -> {
                        dispatchParamBean.setUserId(it.getUserId());
                        dispatchParamBean.setGoodsId(it.getId());
                        dispatchParamBean.setSrcMsgId(it.getSrcMsgId());
                    });
        }
        return dispatchParamBean;
    }

    @Override
    public void expireYMMCargoMerge(Long srcMsgId) {
        tytTransportMbMergeMapper.updateCargoMerge(srcMsgId);
    }

    @Override
    public void pushMbCargoExpireMessage(Long srcMsgId) {
        //查询当前发布的运满满货源版本
        TytTransportMbMerge tytTransportMbMerge = tytTransportMbMergeMapper.selectBySrcMsgId(srcMsgId);
        TytTransportVO tytTransportVO = tytTransportDispatchMapper.getDispatchAndGoodsInfo(tytTransportMbMerge.getSrcMsgId());
        if (tytTransportVO == null) {
            return;
        }

        //查询最新同步的运满满版本，与当前发布的版本进行比较
        TytMbCargoSyncLog newMbCargoSyncLog = tytMbCargoSyncLogMapper.selectLastByCargoId(tytTransportMbMerge.getCargoId());
        if(tytTransportMbMerge.getCargoVersion()>=newMbCargoSyncLog.getCargoVersion()){
            return;
        }

        TytMbCargoSyncLog oldMbCargoSyncLog = tytMbCargoSyncLogMapper.selectByCargoIdAndVersion(tytTransportMbMerge.getCargoId()
                ,tytTransportMbMerge.getCargoVersion());

        String content = changeInfo(newMbCargoSyncLog.getInfoJson(),oldMbCargoSyncLog.getInfoJson());

        //主题
        String title = "<a href =\"/goodsDisp/publishGoods?type=6&cargoId="+tytTransportMbMerge.getCargoId()+"\">运满满货源信息变更通知</a>";

        //标题
        String summary = String.format("您发布的，发货地：%s，目的地：%s，货物名称：%s，发生货物信息变更，变更信息：%s，请确认后重新发布",
                tytTransportVO.getStartPoint(),
                tytTransportVO.getDestPoint(),
                tytTransportVO.getTaskContent(),
                content
        );

        //添加推送用户
        List<Long> pushUserIdList = Collections.singletonList(tytTransportVO.getDispatchId());
        EmployeeMessage employeeMessage=new EmployeeMessage();
        employeeMessage.setTmplId(null);
        employeeMessage.setCtime(new Date());
        employeeMessage.setOperationTime(new Date());
        employeeMessage.setPushStatus(0);
        employeeMessage.setPushUserId(0l);
        employeeMessage.setPushUserName("系统");
        employeeMessage.setReadStatus(0);
        employeeMessage.setStatus(0);
        employeeMessage.setType(2669);
        employeeMessage.setUtime(new Date());
        employeeMessage.setUserId(pushUserIdList.get(0));
        employeeMessage.setUserName(tytTransportVO.getUserShowName());
        employeeMessage.setTitle(title);
        employeeMessage.setContent(summary);
        employeeMessageMapper.insertEmployeeMessage(employeeMessage);
    }

    @Override
    public void saveOrUpdateMbMerge(Long srcMsgId, Long cargoId, Integer cargoVersion) {
        TytTransportMbMerge tytTransportMbMerge = tytTransportMbMergeMapper.selectBySrcMsgId(srcMsgId);
        if (tytTransportMbMerge == null) {
            tytTransportMbMerge = TytTransportMbMerge.builder()
                    .cargoId(cargoId)
                    .srcMsgId(srcMsgId)
                    .cargoVersion(cargoVersion)
                    .status((byte) 0)
                    .createTime(new Date())
                    .updateTime(new Date()).build();
            tytTransportMbMergeMapper.insert(tytTransportMbMerge);
        } else {
            if (cargoVersion != null) {
                tytTransportMbMerge.setCargoVersion(cargoVersion);
            }
            tytTransportMbMerge.setStatus((byte) 0);
            tytTransportMbMerge.setUpdateTime(new Date());
            tytTransportMbMergeMapper.updateByPrimaryKey(tytTransportMbMerge);
        }
    }

    @Override
    public List<TransportYmmGoodsBean> getTransportYmmList(TransportYmmListReqBean req) {
        List<TransportYmmListBean> transportYmmListBeans = tytMbCargoSyncInfoMapper.getTransportYmmList(req);

        log.info("getTransportYmmList transportYmmListBeans size:{}",transportYmmListBeans.size());

        List<TransportYmmGoodsBean> transportYmmList = new ArrayList<>();

        transportYmmListBeans.stream().forEach(transportYmmListBean -> {
            TransportYmmGoodsBean transportYmmGoodsBean = new TransportYmmGoodsBean();

            copyProperties(transportYmmListBean,transportYmmGoodsBean);
            transportYmmList.add(transportYmmGoodsBean);
        });

        log.info("getTransportYmmList transportYmmList size:{}",transportYmmList.size());
        return transportYmmList;
    }

    /**
     *  运满满货源信息与特运通信息对应处理
     * @param transportYmmListBean
     * @param transportYmmGoodsBean
     */
    private void copyProperties(TransportYmmListBean transportYmmListBean, TransportYmmGoodsBean transportYmmGoodsBean) {
        BeanUtils.copyProperties(transportYmmListBean,transportYmmGoodsBean);

        transportYmmGoodsBean.setQueryId(transportYmmListBean.getId());
        transportYmmGoodsBean.setBeginLoadingTime(transportYmmListBean.getLoadTime());
        transportYmmGoodsBean.setLoadingTime(transportYmmListBean.getLoadTime());
        transportYmmGoodsBean.setBeginUnloadTime(transportYmmListBean.getUnloadTime());
        transportYmmGoodsBean.setGiveGoodsPhone(transportYmmListBean.getContactTelephone());
        transportYmmGoodsBean.setWeight(transportYmmListBean.getMaxWeight()!=0?transportYmmListBean.getMaxWeight():transportYmmListBean.getMinWeight());
        transportYmmGoodsBean.setHigh(transportYmmListBean.getMinHeight());
        transportYmmGoodsBean.setWide(transportYmmListBean.getMinWidth());
        transportYmmGoodsBean.setLength(transportYmmListBean.getMinLength());
        transportYmmGoodsBean.setInfoFee(transportYmmListBean.getDepositAmt());
        transportYmmGoodsBean.setPrice(transportYmmListBean.getExpectFreight());
        transportYmmGoodsBean.setTaskContent(transportYmmListBean.getCargoName());
        transportYmmGoodsBean.setShuntingQuantity(transportYmmListBean.getTruckCount());

        transportYmmGoodsBean.setStartPoint(transportYmmListBean.getStartPoint().replace("省","").replaceFirst("市",""));
        transportYmmGoodsBean.setStartProvinc(transportYmmListBean.getLoadProvinceName().replace("省","").replaceFirst("市",""));
        transportYmmGoodsBean.setStartCity(transportYmmListBean.getLoadCityName());
        transportYmmGoodsBean.setStartArea(transportYmmListBean.getLoadDistrictName());
        transportYmmGoodsBean.setStartDetailAdd(transportYmmListBean.getLoadDetailAddress());
        transportYmmGoodsBean.setStartLongitude(transportYmmListBean.getLoadLon());
        transportYmmGoodsBean.setStartLatitude(transportYmmListBean.getLoadLat());

        transportYmmGoodsBean.setDestPoint(transportYmmListBean.getDestPoint().replace("省","").replaceFirst("市",""));
        transportYmmGoodsBean.setDestProvinc(transportYmmListBean.getUnloadProvinceName().replace("省","").replaceFirst("市",""));
        transportYmmGoodsBean.setDestCity(transportYmmListBean.getUnloadCityName());
        transportYmmGoodsBean.setDestArea(transportYmmListBean.getUnloadDistrictName());
        transportYmmGoodsBean.setDestDetailAdd(transportYmmListBean.getUnloadDetailAddress());
        transportYmmGoodsBean.setDestLongitude(transportYmmListBean.getUnloadLon());
        transportYmmGoodsBean.setDestLatitude(transportYmmListBean.getUnloadLat());
        transportYmmGoodsBean.setRefundFlag(transportYmmListBean.getDepositReturn());

        transportYmmGoodsBean.setExcellentGoods(YesOrNoEnum.Y.getCode());
        transportYmmGoodsBean.setPublishType(transportYmmListBean.getDealMode()==1?2:1);


        /**
         * 挂车样式
         */
        JSONArray jsonArray = JSONArray.parseArray(transportYmmListBean.getTruckTypes());
        List<Map> list = JSONArray.parseArray(jsonArray.toJSONString(), Map.class);
        List<String> carStyles = new ArrayList<>();
        list.stream().forEach(item -> {
            if(CarStyleEnum.FLAT_BOARD.getStyleName().equals(item.get("msg"))){
                carStyles.add(CarStyleEnum.FLAT.getStyleName());
            } else if(CarStyleEnum.HIGH_LOW_BOARD.getStyleName().equals(item.get("msg"))){
                carStyles.add(CarStyleEnum.HIGH.getStyleName());
            } else if(Objects.nonNull(item.get("msg"))&&String.valueOf(item.get("msg")).contains(CarStyleEnum.LADDER.getStyleName()) ){
                carStyles.add(CarStyleEnum.FLAT.getStyleName());
                carStyles.add(CarStyleEnum.HIGH.getStyleName());
                transportYmmGoodsBean.setClimb("1");
            }
        });
        transportYmmGoodsBean.setCarStyle(StringUtils.join(carStyles,"/"));

        /**
         * 所需车辆长度
         */
        JSONArray truckLengths = JSONArray.parseArray(transportYmmListBean.getTruckLengths());
        List<String> array = JSONArray.parseArray(truckLengths.toJSONString(), String.class);
        transportYmmGoodsBean.setCarLengthLabels(StringUtils.join(array,","));
        transportYmmGoodsBean.setSourceType(SourceTypeEnum.运满满货源.getId());
    }


    /**
     * 比较两个对象变更的属性
     * @param newInfoJson
     * @param oldInfoJson
     * @return
     */
    private String changeInfo(String newInfoJson, String oldInfoJson) {
        TytMbCargoSyncInfo newCarGoInfo = JSON.parseObject(newInfoJson,TytMbCargoSyncInfo.class);
        TytMbCargoSyncInfo oldCarGoInfo = JSON.parseObject(oldInfoJson,TytMbCargoSyncInfo.class);

        if (oldCarGoInfo != null && newCarGoInfo != null) {
            if (oldCarGoInfo.equals(newCarGoInfo)) {
                return "";
            }

            List<TytClassUtil.ClassProper> newMapValues = TytClassUtil.classConvertList(newCarGoInfo);
            Map<String, Object> oldMapValue = TytClassUtil.classConvertMap(oldCarGoInfo);
            List<String> changeValues = new ArrayList<>();

            newMapValues.stream().forEach(classProper -> {
                Object o = oldMapValue.get(classProper.getFileName());
                if(!Objects.equals(o,classProper.getFileValue())){
                    if(classProper.getFileType().isInstance(new Date())){
                        String val = DateUtil.dateToString((Date) classProper.getFileValue(),"yyyy-MM-dd HH:mm:ss");
                        changeValues.add(classProper.getFileName()+":"+val);
                    }else if(StringUtils.equals(classProper.getFileName(),"定金金额")||StringUtils.equals(classProper.getFileName(),"运费")){
                        changeValues.add(classProper.getFileName()+":"+new BigDecimal(classProper.getFileValue().toString()).divide(new BigDecimal(100),2, BigDecimal.ROUND_HALF_UP));
                    }else{
                        changeValues.add(classProper.getFileName()+":"+classProper.getFileValue());
                    }
                }
            });
            return StringUtils.join(changeValues,",");
        }
        return "";
    }

}
