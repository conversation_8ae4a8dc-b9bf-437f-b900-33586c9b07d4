package com.tyt.transport.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytSearchKeyword;
import com.tyt.transport.service.SearchKeywordService;
import com.tyt.util.Constant;

@Service("searchKeywordService")
public class SearchKeywordServiceImpl extends BaseServiceImpl<TytSearchKeyword, Long> implements SearchKeywordService {
	@Resource(name = "searchKeywordDao")
	public void setBaseDao(BaseDao<TytSearchKeyword, Long> searchKeywordDao) {
		super.setBaseDao(searchKeywordDao);
	}

	@Override
	public TytSearchKeyword getByKeyword(String keyword) {
		List<TytSearchKeyword> tytSearchKeywords = this.getBaseDao().searchByHql("from TytSearchKeyword where keyword=?", new Object[] { keyword }, null, null);
		return tytSearchKeywords.size() == 1 ? tytSearchKeywords.get(0) : null;
	}

	@Override
	public List<TytSearchKeyword> findListByKeywordPriority(int searchKeywordPriority) {
		String hql = null;
		Object[] params = null;
		if (searchKeywordPriority != Constant.SEARCH_KEYWORD_TYPE_ALL_PRIORITY) {
			hql = "from TytSearchKeyword where keywordType=?";
			params = new Object[] { searchKeywordPriority };
		} else {
			hql = "from TytSearchKeyword";
		}
		return this.getBaseDao().find(hql, params);
	}
}
