package com.tyt.transport.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.EmployeeMessageTmpl;
import com.tyt.transport.service.EmployeeMessageTmplService;

@Service(value = "employeeMessageTmplService")
public class EmployeeMessageTmplServiceImpl extends BaseServiceImpl<EmployeeMessageTmpl, Long> implements
		EmployeeMessageTmplService {
	public Logger logger = LoggerFactory.getLogger("EmployeeMessageTmplServiceImpl");

	@Resource(name = "employeeMessageTmplDao")
	public void setBaseDao(BaseDao<EmployeeMessageTmpl, Long> EmployeeMessageTmplDao) {
		super.setBaseDao(EmployeeMessageTmplDao);
	}

	public EmployeeMessageTmpl getEmployeeMessageTmpl(String key){
		String hql="from EmployeeMessageTmpl where tmplKey=?";
		List<EmployeeMessageTmpl> list=this.getBaseDao().find(hql, key);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}
}
