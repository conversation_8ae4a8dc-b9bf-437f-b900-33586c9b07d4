package com.tyt.transport.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.TytConfig;
import com.tyt.model.TytSttLimit;
import com.tyt.model.User;
import com.tyt.transport.querybean.SttLimitBean;
import com.tyt.transport.service.SttLimitService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.MobileUtil;
import com.tyt.util.TimeUtil;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:10
 */
@Service("sttLimitService")
public class SttLimitServiceImpl extends BaseServiceImpl<TytSttLimit,Long> implements SttLimitService {
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	@Resource(name = "userService")
	private UserService userService;


	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;


    @Resource(name="sttLimitDao")
    public void setBaseDao(BaseDao<TytSttLimit, Long> SttLimitDao) {
        super.setBaseDao(SttLimitDao);
    }

	@Override
	public List<SttLimitBean> getSttLimitBeanList(Long userId) {
			User user=userService.getById(userId);
			 int userSing=1;
			 if(ObjectUtil.isNotNull(user.getUserSign()) && user.getUserSign()==7){
				 userSing=7;
			 }else if(ObjectUtil.isNotNull(user.getUserSign()) && (user.getUserSign() == 2 || user.getUserSign() == 3)){
				 userSing=3;
			 }
			List<SttLimitBean> list =(List<SttLimitBean>)cacheService.getObject(Constant.CACHE_STTLIMIT_KEY+userSing);
			if(list==null||list.size()<1){
				String sql="select id,NAME name,TYPE type,VALUE value, warn_number warnNumber from tyt_stt_limit t where t.status=? and t.user_sign=? order by code ";
				 Map<String, org.hibernate.type.Type> scalarMap =new HashMap<String,org.hibernate.type.Type>();
				 scalarMap.put("id", Hibernate.LONG);
				 scalarMap.put("name", Hibernate.STRING);
				 scalarMap.put("type", Hibernate.INTEGER);
				 scalarMap.put("value", Hibernate.INTEGER);
				 scalarMap.put("warnNumber", Hibernate.INTEGER);


				 list= this.getBaseDao().search(sql, scalarMap, SttLimitBean.class, new Object[]{0,userSing}, 1, 1000);
					if(list!=null && list.size()>0){
					 cacheService.setObject(Constant.CACHE_STTLIMIT_KEY+userSing, list,Constant.CACHE_EXPIRE_TIME_24H);
					}
			}

		   try {
				TytConfig tytConfig=tytConfigService.getValue("pushTransportLimitOnOff");
				if("0".equals(tytConfig.getValue())){
					TytConfig tc=tytConfigService.getValue("pushTransportLimitTime");
					if(user.getCtime().getTime()>TimeUtil.parseDateString(tc.getValue()).getTime()){
						SttLimitBean sttLimitBean=list.get(0);
						list.remove(0);
						sttLimitBean.setValue(0);
						list.add(0,sttLimitBean);

						SttLimitBean slb=list.get(1);
						list.remove(1);
						slb.setValue(0);
						list.add(1,slb);
					}
				}

			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			if(user.getVerifyFlag()!=1&&isQuyu(user.getCellPhone())){
				SttLimitBean sttLimitBean=list.get(0);
				list.remove(0);
				sttLimitBean.setValue(0);
				list.add(0,sttLimitBean);
			}

			return list;
	}

	/**
	 * 判断是否在区域内
	 * @param phone
	 * @return false
	 */
	public boolean isQuyu(String phone){
			TytConfig systemIdeatityAreaTytConfig= tytConfigService.getValue("systemIdeatityArea");
			String systemIdeatityArea=systemIdeatityAreaTytConfig.getValue();
			//String systemIdeatityArea="北京,贵州,天津,郑州";
			String place=MobileUtil.getMobileAddress(phone);
			String [] systemIdeatityAreas=systemIdeatityArea==null?null:systemIdeatityArea.split(",");
			//判断是否在排除范围内
			if(systemIdeatityAreas!=null&& null!=place&&systemIdeatityAreas.length>0){
				for(String area:systemIdeatityAreas){
					if(place.indexOf(area)!=-1){
						return true;
					}
				}
			}
			return false;
	}

	public SttLimitBean getUserSttLimit(Long userId) throws Exception{
		User user=userService.getByUserId(userId);
		List<SttLimitBean> list=getSttLimitBeanList(userId);
		if(user.getVerifyPhotoSign().longValue()==1){
			SttLimitBean sttLimitBean=list.get(2);
			return sttLimitBean;
		}else if(user.getVerifyFlag().longValue()==1){
			SttLimitBean sttLimitBean=list.get(1);
			return sttLimitBean;
		}else{
			SttLimitBean sttLimitBean=list.get(0);
			//用户没实名并且在这区域中
//			if(user.getVerifyFlag()!=1&&isQuyu(user.getCellPhone())){							
//				sttLimitBean.setValue(0);
//			}

			return sttLimitBean;
		}
	}
    
}
