package com.tyt.transport.service.impl;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCarOwnerQueryPhone;
import com.tyt.transport.service.TytCarOwnerQueryPhoneService;

@Service(value = "carOwnerQueryPhoneService")
public class TytCarOwnerQueryPhoneServiceImpl extends BaseServiceImpl<TytCarOwnerQueryPhone, Long> implements TytCarOwnerQueryPhoneService {
	public Logger logger = LoggerFactory.getLogger("TytCarOwnerQueryPhoneServiceImpl");

	@Resource(name = "tytCarOwnerQueryPhoneDao")
	public void setBaseDao(BaseDao<TytCarOwnerQueryPhone, Long> tytCarOwnerQueryPhoneDao) {
		super.setBaseDao(tytCarOwnerQueryPhoneDao);
	}
}
