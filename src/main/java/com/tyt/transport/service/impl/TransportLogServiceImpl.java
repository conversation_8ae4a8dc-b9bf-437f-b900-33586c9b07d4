//package com.tyt.transport.service.impl;
//
//import java.util.Date;
//
//import com.tyt.transport.querybean.FallShortSearchLogBean;
//import org.apache.commons.lang.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import com.tyt.transport.service.TransportLogService;
//import com.tyt.util.TimeUtil;
//
////@Service("transportLogService")
//public class TransportLogServiceImpl implements TransportLogService {
//    static final Logger gatherLog = LoggerFactory
//            .getLogger("gatherLog");
//
//    static final Logger detailsLog = LoggerFactory
//            .getLogger("detailsLog");
//    static final Logger searchLog = LoggerFactory
//            .getLogger("searchLog");
//    static final Logger searchDistanceSortLog = LoggerFactory
//            .getLogger("distanceSortLog");
//    static final Logger searchFallShortLog = LoggerFactory.getLogger("searchFallShortLog");
////	 static final Logger callPhoneLog = LoggerFactory
////				.getLogger("callPhoneLog");
//
//    static final String nullValue = "";
//
//    @Override
//    public void detailsLog(long userId, long tsId, String clientVersion,
//                           int clientSign, int status) {
//        try {
//            String dateStr = TimeUtil.formatDateTime(new Date());
//            clientVersion = (null == clientVersion || "".equals(clientVersion)) ? "" : clientVersion;
//            detailsLog.info("{},{},{},{},{},{}", userId,
//                    tsId, clientVersion, clientSign, status, dateStr);
//        } catch (Exception e) {
//            gatherLog.error("保存detailsLog日志异常", e);
//        }
//    }
//
//    @Override
//    public void searchLog(long userId, String startCoord, String startRange,
//                          String destCoord, String destRange, long carId, String headNo,
//                          String headCity, String clientVersion, int clientSign, int sortType, String numberType, String osVersion, String clientId, String carLength, String carType, String specialRequired) {
//        try {
//            String dateStr = TimeUtil.formatDateTime(new Date());
//            startCoord = startCoord.replaceAll(",", "_");
//            startRange = (null == startRange || "".equals(startRange)) ? "300" : startRange;
//
//            if (null != destCoord && !"".equals(destCoord)) {
//                destRange = (null == destRange || "".equals(destRange)) ? "300" : destRange;
//                destCoord = destCoord.replaceAll(",", "_");
//            } else {
//                destCoord = nullValue;
//                destRange = "0";
//            }
//
//            if (carId == 0) {
//                headNo = nullValue;
//                headCity = nullValue;
//            } else {
//                headNo = (null == headNo || "".equals(headNo)) ? nullValue : headNo;
//                headCity = (null == headCity || "".equals(headCity)) ? nullValue : headCity;
//            }
//
//            clientVersion = (null == clientVersion || "".equals(clientVersion)) ? nullValue : clientVersion;
//
//            carLength = (null == carLength || ""
//                    .equals(carLength)) ? nullValue : carLength;//卡车长度（能获取到就填上）
//            carType = (null == carType || ""
//                    .equals(carType)) ? nullValue : carType;//卡车类型（能获取到就填上）
//            specialRequired = (null == specialRequired || ""
//                    .equals(specialRequired)) ? nullValue : specialRequired;//特殊要求（能获取到就填上）
//
//            searchLog.info(
//                    "{},'{}',{},'{}',{},{},'{}','{}','{}',{},{},'{}','{}','{}','{}','{}','{}','{}'",
//                    userId, startCoord, startRange, destCoord, destRange,
//                    carId, headNo, headCity, clientVersion, clientSign,
//                    sortType, dateStr, numberType, osVersion, clientId, carLength, carType, specialRequired);
//
//        } catch (Exception e) {
//            gatherLog.error("保存searchLog日志异常", e);
//        }
//    }
//
//    @Override
//    public void searchDistanceSortLog(long userId, int sortType,
//                                      String clientVersion, int clientSign) {
//        try {
//            String dateStr = TimeUtil.formatDateTime(new Date());
//
//            clientVersion = (null == clientVersion || "".equals(clientVersion)) ? nullValue : clientVersion;
//
//            searchDistanceSortLog.info("{},{},{},{},{}", userId,
//                    sortType, clientVersion, clientSign, dateStr);
//
//        } catch (Exception e) {
//            gatherLog.error("保存DistanceSortLog日志异常", e);
//        }
//
//    }
//
//    @Override
//    public void callPhoneLog(long userId, long tsId, String clientVersion,
//                             int clientSign) {
//        try {
//            String dateStr = TimeUtil.formatDateTime(new Date());
//
//            clientVersion = (null == clientVersion || "".equals(clientVersion)) ? nullValue : clientVersion;
//
////		callPhoneLog.info("{},{},{},{},{}", userId,
////				tsId, clientVersion,clientSign,dateStr);
//
//        } catch (Exception e) {
//            gatherLog.error("保存callPhoneLog日志异常", e);
//        }
//    }
//
//    @Override
//    public void searchFallShortLog(FallShortSearchLogBean bean) {
//
//    }
//
//}
