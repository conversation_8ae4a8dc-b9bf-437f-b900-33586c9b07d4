package com.tyt.transport.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.PageBean;
import com.tyt.model.TytCarOwnerIntention;
import com.tyt.transport.querybean.CarOwnerIntentionBean;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TytCarOwnerIntentionService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ApplicationContextUtils;
import com.tytrecommend.model.TytPreference;

@SuppressWarnings("deprecation")
@Service(value = "tytCarOwnerIntentionService")
public class TytCarOwnerIntentionServiceImpl extends BaseServiceImpl<TytCarOwnerIntention, Long> implements TytCarOwnerIntentionService {

	@Resource(name = "transportMainService")
	TransportMainService transportMainService;

	@Resource(name = "tytCarOwnerIntentionDao")
	public void setBaseDao(BaseDao<TytCarOwnerIntention, Long> tytCarOwnerIntentionDao) {
		super.setBaseDao(tytCarOwnerIntentionDao);
	}

	public List<TytCarOwnerIntention> getTytCarOwnerIntentionList(Long srcMsgId, Integer currentPage) {
		String hql = " srcMsgId=? order by lastLinkTime desc ";
		// 加分页查询
		PageBean pageBean = new PageBean();
		TytConfigService tytConfigService = (TytConfigService) ApplicationContextUtils.getBean("tytConfigService");
		int size = tytConfigService.getIntValue("tyt_manage_intention_list_record_page_size", 50);
		pageBean.setPageSize(size);
		if (currentPage == null || currentPage.intValue() < 1) {
			pageBean.setCurrentPage(1);
		} else
			pageBean.setCurrentPage(currentPage);
		return this.getBaseDao().search(hql, new Object[] { srcMsgId }, pageBean);
	}



	public TytPreference getTytPreference(Long carUserId, Long carId) {
		StringBuffer sbSql = new StringBuffer("SELECT c.id, c.car_id carId,c.start_provinc startProvinc,c.start_city startCity,c.start_area startArea from tyt_recommend.tyt_preference c where user_id=? and car_id=? and find_good_onoff=?");

		Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();

		map.put("id", Hibernate.LONG);
		map.put("carId", Hibernate.LONG);
		map.put("startProvinc", Hibernate.STRING);
		map.put("startCity", Hibernate.STRING);
		map.put("startArea", Hibernate.STRING);

		List<TytPreference> list = this.getBaseDao().search(sbSql.toString(), map, TytPreference.class, new Object[] { carUserId, carId, 1 });
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;

	}

	@Override
	public void updateStatus(String srcMsgId, String carUserId) {
		String sql = "UPDATE tyt.`tyt_car_owner_intention` tcoi SET tcoi.`status`=? WHERE tcoi.`src_msg_id`=? AND tcoi.`car_user_id`=? ";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { 3, srcMsgId, carUserId });
	}

	@Override
	public void updateAgree(String srcMsgId, String carUserId) {
		String sql = "update tyt.`tyt_car_owner_intention` set STATUS=? where src_msg_id=? and car_user_id!=? and status=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { 3, srcMsgId, carUserId, 2 });
	}

	@Override
	public TytCarOwnerIntention getBySrcMsgIdAndCaruserId(Long srcMsgId,
			Long carUserId) {
		String hql = " from  TytCarOwnerIntention where srcMsgId=?  and carUserId=?";
		List<TytCarOwnerIntention> list = this.getBaseDao().find(hql, new Object[]{srcMsgId,carUserId});
		return (list!=null&&list.size()>0)?list.get(0):null;
	}

    @Override
    public List<CarOwnerIntentionBean> getCarOwnerListByTsIds(List<Long> tsIds) {
        if(CollectionUtils.isEmpty(tsIds)) {
            return null;
        }
        // status =4
        String sql = "select o.src_msg_id srcMsgId,o.car_user_id carUserId, o.car_phone carPhone, o.car_id carId," +
                "       c.head_city headCity,c.head_no headNo,c.tail_city tailCity,c.tail_no tailNo " +
                "       from tyt_car_owner_intention o left join tyt_car c on o.car_id = c.id " +
                "     where o.status = 4 and o.src_msg_id in (:ids)";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("srcMsgId", Hibernate.LONG);
        scalarMap.put("carUserId", Hibernate.LONG);
        scalarMap.put("carPhone", Hibernate.STRING);
        scalarMap.put("carId", Hibernate.LONG);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("ids", tsIds);
        List<CarOwnerIntentionBean> result = this.getBaseDao().search(sql, scalarMap, CarOwnerIntentionBean.class, paramsMap);
        return result;
    }
}
