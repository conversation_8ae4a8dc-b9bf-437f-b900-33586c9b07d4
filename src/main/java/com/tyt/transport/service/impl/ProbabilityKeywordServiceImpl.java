package com.tyt.transport.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytProbabilityKeyword;
import com.tyt.transport.service.ProbabilityKeywordService;

@Service("probabilityKeywordService")
public class ProbabilityKeywordServiceImpl extends BaseServiceImpl<TytProbabilityKeyword, Long> implements ProbabilityKeywordService {
	@Resource(name = "probabilityKeywordDao")
	public void setBaseDao(BaseDao<TytProbabilityKeyword, Long> probabilityKeywordDao) {
		super.setBaseDao(probabilityKeywordDao);
	}

}
