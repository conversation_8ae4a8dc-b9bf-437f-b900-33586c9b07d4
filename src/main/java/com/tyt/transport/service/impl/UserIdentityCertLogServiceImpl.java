package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.EcaContract;
import com.tyt.model.TytUserIdentityAuth;
import com.tyt.model.UserIdentityCert;
import com.tyt.model.UserIdentityCertLog;
import com.tyt.service.common.azt.service.IdentityInfo;
import com.tyt.transport.querybean.CertIdentityBean;
import com.tyt.transport.service.EcaContractService;
import com.tyt.transport.service.UserIdentityCertLogService;
import com.tyt.transport.service.UserIdentityCertService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Service(value = "userIdentityCertLogService")
public class UserIdentityCertLogServiceImpl extends BaseServiceImpl<UserIdentityCertLog, Long> implements UserIdentityCertLogService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "userIdentityCertLogDao")
	public void setBaseDao(BaseDao<UserIdentityCertLog, Long> userIdentityCertDao) {
		super.setBaseDao(userIdentityCertDao);
	}

	@Resource(name="userIdentityCertService")
	private UserIdentityCertService userIdentityCertService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Override
	public void addIdentityCertLog(UserIdentityCertLog userIdentityCertLog) {
		userIdentityCertLog.setCtime(new Date());
		this.add(userIdentityCertLog);
	}

	@Override
	public Boolean isExistAuthFailTimes(String userName, String idCard, Long userId) {
		Boolean bflag = this.isExistAuthFail(userName, idCard);
		if(bflag) { // 存在失败，返回true
			return true;
		}

		final String sql = "select count(id) from user_identity_cert_log where user_id = ? and status=2 and ctime > ?";
		Date date = TimeUtil.today();
		final Object[] params = {
				userId,
				date
		};
		BigInteger count = this.getBaseDao().query(sql, params);
		int failTimes = tytConfigService.getIntValue("identity_cert_fial_times",  3);
		if(count.intValue() >= failTimes) { // 如超出限制次数，返回存在失败 true
			logger.info("用户ID:{}实名认证次数超出系统每日限制{}次,返回不查询安证通,姓名:{},身份证号:{}", userId, failTimes,userName,failTimes);
			return true;
		}
		return false;
	}

	@Override
	public Boolean isExistAuthFail(String userName, String idCard) {
		final String sql = "select * from user_identity_cert_log where user_name = ? and id_card = ? and status=2 limit 1";
		final Object[] params = {
				userName,
				idCard
		};
		List<UserIdentityCertLog> list = this.getBaseDao().queryForList(sql, params);
		if(list != null && !list.isEmpty()){ // 存在失败的信息
			logger.info("实名认证存在失败信息,姓名:{},身份证号:{}", userName, idCard);
			return true;
		}
		return false;
	}
}
