package com.tyt.transport.service.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.transport.service.TransportMainService;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.model.TransportCollect;
import com.tyt.transport.dao.TransportCollectDao;
import com.tyt.transport.querybean.TransportQueryBean;
import com.tyt.transport.service.TransportCollectService;

@Service("transportCollectService")
public class TransportCollectServiceImpl extends BaseServiceImpl<TransportCollect, Long>
		implements TransportCollectService {

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@Resource(name="transportCollectDao")
    public void setBaseDao(BaseDao<TransportCollect, Long> transportCollectDao) {
        super.setBaseDao(transportCollectDao);
    }

	@Override
	public List<Long> getInfoIdList(String cellPhone) {
		return ((TransportCollectDao)(this.getBaseDao())).getInfoIdList(cellPhone);
	}

	@Override
	public void delCollect(String cellPhone, Long infoId) {
		((TransportCollectDao)(this.getBaseDao())).delCollect(cellPhone, infoId);
		
	}

	@Override
	public void updateId(Long oldId, Long newId) {
		((TransportCollectDao)(this.getBaseDao())).updateId(oldId,newId);       		
	}

	@Override
	public TransportCollect isExit(Long userId, Long tsId) throws Exception {
		TransportMain transport =transportMainService.getTransportMainForId(tsId);

		StringBuffer sql=new StringBuffer();
		sql.append(" entity.userId="+userId);
		sql.append(" and entity.infoId="+transport.getSrcMsgId());
		sql.append(" and entity.status=1");
		List<TransportCollect> collect=this.getList(sql.toString(), null);
		if(collect.size()>0){
			return collect.get(0);
		}
		return null;
	}

	@Override
	public List<TransportQueryBean> getByUserId(Long userId,int queryType, long querySign) throws Exception{
		int pageSize = AppConfig.getIntProperty(
				"tyt.tyt_transport.query.page.size").intValue();

		StringBuffer sb = new StringBuffer( "SELECT c.id,c.info_id  tsId,m.start_point startPoint,m.dest_point "
				+ "destPoint,m.task_content taskContent,m.status,m.ctime pubDate"
				+ " FROM tyt_transport_main m,tyt_transport_collect c "
		  + "WHERE  c.status=1 AND c.user_id=? AND m.src_msg_id=c.info_id ");
		
		List<Object> listObject = new ArrayList<Object>();
		listObject.add(userId);
	   if(querySign<=1){
        	queryType = 1;
        }
	        
		// 第一次请求
		if (queryType == 1) {
			// 大小排序
			sb.append(" order by c.id desc ");
		} else
		// 下拉查新数据
		if (queryType == 0) {
			sb.append(" and c.id>?");
			// 小大排序
			sb.append(" order by c.id asc ");
			listObject.add(querySign);
		}
		// 上推查历史数据
		else {
			sb.append(" and c.id<?");
			// 大小排序
			sb.append(" order by c.id desc ");
			listObject.add(querySign);
		}
		
		Map <String,org.hibernate.type.Type> map=new HashMap<String,org.hibernate.type.Type>();
		
		map.put("id",Hibernate.LONG);
		map.put("tsId", Hibernate.LONG);
		map.put("startPoint", Hibernate.STRING);
		map.put("destPoint", Hibernate.STRING);
		map.put("taskContent",Hibernate.STRING );
		map.put("status", Hibernate.INTEGER);
		map.put("pubDate",Hibernate.TIMESTAMP );
	
		List<TransportQueryBean> list=this.getBaseDao().search(sb.toString(), map, TransportQueryBean.class, listObject.toArray(), 1, pageSize);
		return list;
	}

	@Override
	public void delCollect(Long userId, Long infoId) throws Exception{
		((TransportCollectDao)(this.getBaseDao())).delCollect(userId, infoId);
	}

	@Override
	public void updateStatus(Long infoId, Integer status) {
		((TransportCollectDao)(this.getBaseDao())).updateStatus(infoId, status);
		
	}
	@Override
	public boolean updateStatusByIds(List<Long> idList,Integer status) {
		try {
			for(Long id:idList){
				this.updateStatus(id, status);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
	}

    @Override
    public ResultMsgBean getCollectByPageList(Long userId, Integer pageNumber, Integer pageSize) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
		StringBuffer sb = new StringBuffer( "SELECT c.id,c.info_id  tsId,m.start_point startPoint,m.dest_point "
				+ "destPoint,m.task_content taskContent,if(date_format(now(),'%y-%m-%d')>m.ctime, 0, m.status) as status, m.ctime pubDate, m.publish_type as publishType"
				+ " FROM tyt_transport_main m,tyt_transport_collect c "
				+ "WHERE  c.status=1 AND c.user_id=? AND m.src_msg_id=c.info_id and c.ctime > DATE_SUB(now(), INTERVAL 3 DAY) order by c.id desc");
		List<Object> listObject = new ArrayList<Object>();
		listObject.add(userId);
		StringBuffer sbCount = new StringBuffer( "SELECT count(*)"
				+ " FROM tyt_transport_main m,tyt_transport_collect c "
				+ "WHERE  c.status=1 AND c.user_id=? AND m.src_msg_id=c.info_id and c.ctime > DATE_SUB(now(), INTERVAL 3 DAY) order by c.id desc");
		BigInteger count = this.getBaseDao().query(sbCount.toString(), new Object[] { userId });
		if(count.intValue() == 0){
			return resultMsgBean;
		}
		Map <String,org.hibernate.type.Type> map=new HashMap<String,org.hibernate.type.Type>();
		map.put("id",Hibernate.LONG);
		map.put("tsId", Hibernate.LONG);
		map.put("startPoint", Hibernate.STRING);
		map.put("destPoint", Hibernate.STRING);
		map.put("taskContent",Hibernate.STRING );
		map.put("status", Hibernate.INTEGER);
		map.put("pubDate",Hibernate.TIMESTAMP );
		map.put("publishType",Hibernate.INTEGER);
		List<TransportQueryBean> list=this.getBaseDao().search(sb.toString(), map, TransportQueryBean.class, listObject.toArray(), pageNumber, pageSize);
		resultMsgBean.setData(list);
		resultMsgBean.setTotalSize(count.longValue());
		return resultMsgBean;
    }
}
