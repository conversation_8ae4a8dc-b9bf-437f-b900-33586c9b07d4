package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.*;
import com.tyt.service.common.azt.api.IdentityInfoAPI;
import com.tyt.service.common.azt.service.IdentityInfo;
import com.tyt.transport.querybean.CertIdentityBean;
import com.tyt.transport.service.EcaContractService;
import com.tyt.transport.service.UserIdentityCertLogService;
import com.tyt.transport.service.UserIdentityCertService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service(value = "userIdentityCertService")
public class UserIdentityCertServiceImpl extends BaseServiceImpl<UserIdentityCert, Long> implements UserIdentityCertService{

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "userIdentityCertDao")
	public void setBaseDao(BaseDao<UserIdentityCert, Long> userIdentityCertDao) {
		super.setBaseDao(userIdentityCertDao);
	}
	
	@Resource(name="ecaContractService")
	private EcaContractService ecaContractService;
	@Resource(name="tytUserIdentityAuthService")
	private TytUserIdentityAuthService tytUserIdentityAuthService;
	@Resource(name="userIdentityCertService")
	private UserIdentityCertService userIdentityCertService;
	@Resource(name="userIdentityCertLogService")
	private UserIdentityCertLogService userIdentityCertLogService;

	@Resource(name = "userService")
	private UserService userService;

	@Override
	public UserIdentityCert getUserIdentity(Long userId){
		List<UserIdentityCert> cert = this.getBaseDao().find("from UserIdentityCert where userId=? ", userId);
		return (cert!=null && cert.size()>0)?cert.get(0):null;
	}
	
	@Override
	public CertIdentityBean getCertIdentity(Long userId){
		TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getTytUserIdentityAuth(userId);
		CertIdentityBean certIdentity=null;
		UserIdentityCert cert=null;
		if (userIdentityAuth!=null && userIdentityAuth.getIdentityStatus()==1) {
			certIdentity=new CertIdentityBean();
			certIdentity.setIdentityType(1);
			
			//判断身份是否为企业货主，如果是企业货主，查询用户最近签署的合同
			if (userIdentityAuth.getUserClass()==1 && userIdentityAuth.getIdentityType()==2) {
				certIdentity.setIdentityType(2);
				certIdentity.setComName(userIdentityAuth.getEnterpriseName());
				//如果企业货主有签署的合同，则取合同上的信息
				cert = getUserIdentity(userId);
				if (cert!=null) {
					certIdentity.setIdCard(cert.getIdCard());
					certIdentity.setUserName(cert.getUserName());
				}
			}else{
				certIdentity.setUserName(userIdentityAuth.getTrueName());
				certIdentity.setIdCard(userIdentityAuth.getIdCard());
			}
		} else {
			certIdentity=new CertIdentityBean();
			cert = userIdentityCertService.getUserIdentity(userId);
			certIdentity.setIdentityType(1);
			if (cert!=null) {
				certIdentity.setIdCard(cert.getIdCard());
				certIdentity.setUserName(cert.getUserName());
			}
		}
		try {
			if (userId!=null) {
				User user = userService.getByUserId(userId);
				certIdentity.setCellPhone(user.getCellPhone());
			}
		} catch (Exception e) {
			logger.error("未查询到用户信息", e);
		}
		return certIdentity;
	}

	@Override
	public UserIdentityCert getUserIdentityCert(String userName, String idCard) {
		final String sql = "select * from user_identity_cert where user_name = ? and id_card = ? limit 1";
		final Object[] params = {
				userName,
				idCard
		};
		List<UserIdentityCert> identityCert = this.getBaseDao().queryForList(sql, params);
		if(identityCert != null && !identityCert.isEmpty()){
			return identityCert.get(0);
		}
		return null;
	}

	@Override
	public IdentityInfo saveUserIdentityCert(String userName, String idCard, Long userId) {
		IdentityInfo info = null;
		if (userName==null || "".equals(userName.trim()) || idCard==null || "".equals(idCard.trim())) {
			info = new IdentityInfo();
			info.setCode("003");
			info.setCodeInfo("用户姓名或身份证号不能为空");
			return info;
		}
		// 1. 实名认证表查询
		UserIdentityCert identityCert = this.getUserIdentityCert(userName, idCard);
		
		if(identityCert != null){
			info = new IdentityInfo();
			info.setCode("005");
			info.setCodeInfo("认证信息匹配");
			// 认证信息插入数据库,插入为忽略插入，无需判断
			identityCert = new UserIdentityCert();
			identityCert.setUserId(userId);
			identityCert.setUserName(userName);
			identityCert.setIdCard(idCard);
			identityCert.setSource(identityCert.getSource()); // 使用之前日志的判断，非1即2，1-平台、2-非平台
			this.addIgnoreUserIdentityCert(identityCert);
			return info;
		}
		// 2. 若无数据，查询平台认证库
		boolean isExistFail = userIdentityCertLogService.isExistAuthFailTimes(userName,idCard,userId);
		if(isExistFail) {
			info = new IdentityInfo();
			info.setCode("003");
			info.setCodeInfo("身份证号与姓名不匹配");
			return info;
		}
		TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getTytUserIdentityAuth(userName, idCard);
		// 3. 无论平台是否存在，均需去三方认证进行身份查验
		info = IdentityInfoAPI.queryIdentityInfo(userName, idCard);
		UserIdentityCertLog certLog = new UserIdentityCertLog();
		certLog.setUserId(userId);
		certLog.setUserName(userName);
		certLog.setIdCard(idCard);
		certLog.setSource(userIdentityAuth != null ? 1 : 2); // 来源：1-平台、2-非平台
		certLog.setStatus(StringUtils.equals(info.getCode(), "005") ? 1 : 2); // 状态：1-通过、2-未通过
		certLog.setResult(JSON.toJSONString(info));
		userIdentityCertLogService.addIdentityCertLog(certLog);
		if (StringUtils.equals(info.getCode(), "005")) {
			identityCert = new UserIdentityCert();
			identityCert.setUserId(userId);
			identityCert.setUserName(userName);
			identityCert.setIdCard(idCard);
			identityCert.setSource(certLog.getSource()); // 使用之前日志的判断，非1即2，1-平台、2-非平台
			this.addIgnoreUserIdentityCert(identityCert);
		}
		return info;
	}

	@Override
	public Integer addIgnoreUserIdentityCert(UserIdentityCert identityCert) {
		final String sql = "insert ignore into user_identity_cert(user_id,user_name,id_card,source) values(?,?,?,?)";
		final Object[] params = {
				identityCert.getUserId(),
				identityCert.getUserName(),
				identityCert.getIdCard(),
				identityCert.getSource()
		};
		return this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public Boolean checkContractIdentity(EcaContract ecaContract) {
		Boolean flag = false;
		// 托运方
		String shipperUserName = ecaContract.getShipperUserName();
		String shipperIdCard = ecaContract.getShipperIdCard();
		Long shipperUserId = ecaContract.getShipperUserId();
		// 承运方
		String carryUserName = ecaContract.getCarryUserName();
		String carryIdCard = ecaContract.getCarryIdCard();
		Long carryUserId = ecaContract.getCarryUserId();

		IdentityInfo shipperIdentityInfo = this.saveUserIdentityCert(shipperUserName, shipperIdCard, shipperUserId);
		IdentityInfo carryIdentityInfo = this.saveUserIdentityCert(carryUserName, carryIdCard, carryUserId);

		UserIdentityCert shipperIdentityCert = this.getUserIdentityCert(shipperUserName, shipperIdCard);
		UserIdentityCert carryIdentityCert = this.getUserIdentityCert(carryUserName, carryIdCard);
		if (shipperIdentityCert != null && carryIdentityCert != null) {
			flag = true;
		}
		return flag;
	}

	@Override
	public Boolean checkUserIdentityCert(String userName, String idCard) {
		UserIdentityCert identityCert = this.getUserIdentityCert(userName,idCard);
		return identityCert != null;
	}
}
