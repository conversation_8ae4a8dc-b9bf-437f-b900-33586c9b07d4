package com.tyt.transport.service.impl;

import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.*;
import com.tyt.plat.entity.base.TytTransportExtend;
import com.tyt.plat.mapper.base.*;
import com.tyt.transport.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TransportExtendServiceImpl extends BaseServiceImpl<TransportMain, Long> implements TransportExtendService {


    @Autowired
    private TytTransportExtendMapper transportExtendMapper;

    @Override
    public void addExtend(TytTransportExtend transportExtend) {

        transportExtendMapper.insertSelective(transportExtend);
    }

    @Override
    public TytTransportExtend getByTsId(Long tsId) {
        return transportExtendMapper.selectByTsId(tsId);
    }

    @Override
    public void updateExtend(TytTransportExtend dbLastExtend) {
        transportExtendMapper.updateByPrimaryKey(dbLastExtend);
    }
}
