package com.tyt.transport.service.impl;

import com.tyt.base.bean.BaseParameter;
import com.tyt.infofee.enums.PublishTypeEnum;
import com.tyt.marketingActivity.enums.ActivityTypeEnum;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.marketingActivity.service.impl.MarketingActivityServiceImpl;
import com.tyt.model.PublicResource;
import com.tyt.model.Transport;
import com.tyt.plat.entity.base.GoodsRefreshManualDO;
import com.tyt.plat.entity.base.GoodsRefreshManualLogDO;
import com.tyt.plat.entity.base.TytTransportTecServiceFee;
import com.tyt.plat.mapper.base.GoodsRefreshManualLogMapper;
import com.tyt.plat.mapper.base.GoodsRefreshManualMapper;
import com.tyt.plat.mapper.base.TytTransportTecServiceFeeMapper;
import com.tyt.plat.vo.ts.SaveDirectReq;
import com.tyt.transport.enums.RefreshItemEnum;
import com.tyt.transport.querybean.TransportPublishBean;
import com.tyt.transport.service.GoodsRefreshManualService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.PublicResourceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.TransportUtil;
import com.tyt.util.TyTGoodsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/08/28 11:07
 */
@Slf4j
@Service
public class GoodsRefreshManualServiceImpl implements GoodsRefreshManualService {

    @Autowired
    private GoodsRefreshManualMapper goodsRefreshManualMapper;
    @Autowired
    private GoodsRefreshManualLogMapper goodsRefreshManualLogMapper;
    @Autowired
    private TytConfigService tytConfigService;
    @Autowired
    private PublicResourceService publicResourceService;

    @Autowired
    private MarketingActivityService marketingActivityService;

    @Autowired
    private TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    /**
     * 返回最近1周的货源动态，默认30条
     */
    @Override
    public List<String> getGoodsDynamic(Long userId, String clientSign, Long srcMsgId) {
        boolean status = marketingActivityService.userIsValidByType(ActivityTypeEnum.GOODS_COMMISSION_ACTIVITY.getId(), userId);
        if (status && null != srcMsgId){
            TytTransportTecServiceFee serviceFee = tytTransportTecServiceFeeMapper.getBySrcMsgId(srcMsgId);
            if (null != serviceFee){
                List<String> list = new ArrayList<>();
                if ("32".equals(clientSign)){
                    list.add("该货源2小时内成交有机会获5元现金奖，点击详情>");
                }else {
                    list.add("该货源2小时内成交有机会获5元现金奖");
                }
                return list;
            }
        }
        List<String> dynamicList = goodsRefreshManualMapper.getRecentlyDynamic();
        // 不足10条，拼上固定文案
        if (dynamicList.size() < 10) {
            dynamicList.add("王先生，补充了运费，增加了40分钟曝光");
            dynamicList.add("李先生，转了一口价，增加了20分钟曝光");
            dynamicList.add("张女士，完善了目的地详细地址，增加了20分钟曝光");
            dynamicList.add("黄先生，补充了运费、转了一口价，增加了60分钟曝光");
            dynamicList.add("王女士，补充了运费、转了一口价、完善了目的地详细地址，增加了80分钟曝光");
        }
        return dynamicList;
    }

    /**
     * 增加货源刷新次数
     */
    @Override
    @Async
    public void addGoodsRefresh(Transport transport, BaseParameter baseParameter, Transport originalTransport) {
        // 6710：不再增加额外曝光次数
        if (TransportUtil.getClientVersion(baseParameter.getClientVersion()) >= 6710) {
            return;
        }

        GoodsRefreshManualDO existRecord = goodsRefreshManualMapper.getBySrcMsgId(transport.getSrcMsgId());
        int itemValue = existRecord == null ? 0 : existRecord.getItemValue();
        // 如果运费不为空，且之前没填写运费，则增加
        if (!TyTGoodsUtil.isPriceless(transport.getPrice()) && !RefreshItemEnum.check(itemValue, RefreshItemEnum.FILL_PRICE)) {
            itemValue += RefreshItemEnum.FILL_PRICE.getBinaryValue();
        }
        // 一口价
        if (Objects.equals(transport.getPublishType(), PublishTypeEnum.fixed.getCode())
                && !RefreshItemEnum.check(itemValue, RefreshItemEnum.TO_FIXED_PRICE)) {
            itemValue += RefreshItemEnum.TO_FIXED_PRICE.getBinaryValue();
        }
        // 目的地
        if (StringUtils.isNotBlank(transport.getDestDetailAdd())
                && !Objects.equals(transport.getDestDetailAdd(), transport.getDestArea())
                && !RefreshItemEnum.check(itemValue, RefreshItemEnum.FILL_DEST_DETAIL)) {
            itemValue += RefreshItemEnum.FILL_DEST_DETAIL.getBinaryValue();
        }

        if (itemValue == 0 || existRecord != null && existRecord.getItemValue() == itemValue) {
            log.info("没有符合刷新的操作，不增加刷新次数");
        } else {
            Integer refreshInterval = tytConfigService.getIntValue("goods_manual_refresh_interval", 10);
            List<RefreshItemEnum> itemEnums = RefreshItemEnum.getList(itemValue);
            Integer refreshTimes = itemEnums.stream().map(RefreshItemEnum::getRefreshTimes).mapToInt(Integer::intValue).sum();
            String remark = transport.getUserShowName() + "，" + itemEnums.stream().map(RefreshItemEnum::getDesc)
                    .collect(Collectors.joining("、")) + "，增加了" + (refreshTimes * refreshInterval) + "分钟曝光";

            if (existRecord == null) {
                GoodsRefreshManualDO refreshManualDO = new GoodsRefreshManualDO();
                refreshManualDO.setSrcMsgId(transport.getSrcMsgId());
                refreshManualDO.setUserId(transport.getUserId());
                refreshManualDO.setRefreshInterval(refreshInterval);
                refreshManualDO.setTotalTimes(refreshTimes);
                refreshManualDO.setRefreshTimes(0);
                refreshManualDO.setLeftTimes(refreshTimes);
                refreshManualDO.setItemValue(itemValue);
                refreshManualDO.setRemark(remark);
                refreshManualDO.setCreateTime(new Date());
                goodsRefreshManualMapper.insert(refreshManualDO);
            } else {
                GoodsRefreshManualDO refreshManualDO = new GoodsRefreshManualDO();
                refreshManualDO.setId(existRecord.getId());
                refreshManualDO.setTotalTimes(refreshTimes);
                refreshManualDO.setLeftTimes(refreshTimes - existRecord.getRefreshTimes());
                refreshManualDO.setItemValue(itemValue);
                refreshManualDO.setRemark(remark);
                refreshManualDO.setModifyTime(new Date());
                goodsRefreshManualMapper.updateByPrimaryKeySelective(refreshManualDO);
            }
        }

        // 记录操作日志
        GoodsRefreshManualLogDO logDO = new GoodsRefreshManualLogDO();
        logDO.setSrcMsgId(transport.getSrcMsgId());
        logDO.setUserId(transport.getUserId());
        logDO.setItemValue(itemValue);
        logDO.setPriceBefore(originalTransport == null ? "" : originalTransport.getPrice());
        logDO.setPriceAfter(transport.getPrice());
        logDO.setPublishTypeBefore(originalTransport == null ? null : originalTransport.getPublishType());
        logDO.setPublishTypeAfter(transport.getPublishType());
        logDO.setPriceSuggest(baseParameter instanceof SaveDirectReq ? ((SaveDirectReq) baseParameter).getPriceSuggest() : null);
        logDO.setSourcePage(getGoodsSourcePage(transport, baseParameter));
        goodsRefreshManualLogMapper.insertSelective(logDO);
    }

    /**
     * 获取货源来源页面
     * 来源页面：0 未知；1 APP货源详情页货源诊断入口；2 APP发货成功货源诊断入口；3 APP发货页低速找车弹窗；10 PC发货前填价弹窗；
     * 1. 如果是填价加价转一口价，saveDirectReq不为空且saveDirectReq.updateDataReq不为空：
     * 1.1 如果sourcePage=2则为2（H5货源诊断入口）
     * 1.2 为空就是1（APP详情页货源诊断入口）
     * 2. saveDirectReq为空，则为货源发布，有价格才记录。如果用户在ab测中，则为3（APP发货页低速找车弹窗）。
     * 3. saveDirectReq为空 且 有价 且 pc发货 且 在 goods_publish_fill_price_pc ab测中，则为10（PC发货前填价弹窗）
     */
    private int getGoodsSourcePage(Transport transport, BaseParameter baseParameter) {
        // 填价加价转一口价
        if (baseParameter instanceof SaveDirectReq) {
            // 来源是updateGoodsInfo接口
            SaveDirectReq saveDirectReq = (SaveDirectReq) baseParameter;
            if (saveDirectReq.getUpdateDataReq() != null) {
                // H5货源诊断页传了sourcePage=2，不传就是APP详情页货源诊断
                return Objects.equals(saveDirectReq.getUpdateDataReq().getSourcePage(), 2) ? 2 : 1;
            }
        } else if (baseParameter instanceof TransportPublishBean) { // 发布，编辑发布
            // 只有有价货源才记录来源
            if (TransportUtil.hasPrice(transport.getPrice())) {
                // PC发货
                if (String.valueOf(Constant.ClientSignEnum.PC.code).equals(baseParameter.getClientSign())) {
                    TransportPublishBean transportPublishBean = (TransportPublishBean) baseParameter;
                    // 在 PC发货增加填价弹窗AB测中，返回10（PC发货前填价弹窗）
                    if (Objects.equals(transportPublishBean.getSourcePage(), 10)) {
                        return 10;
                    }
                } else { // APP发货
                    // 现在没有AB测了，发无价都弹低速找车弹窗
                    return 3;
                }
            }
        }
        return 0;
    }
}
