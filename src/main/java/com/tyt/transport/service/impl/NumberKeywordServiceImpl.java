package com.tyt.transport.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytNumberKeyword;
import com.tyt.transport.service.NumberKeywordService;

@Service("numberKeywordService")
public class NumberKeywordServiceImpl extends BaseServiceImpl<TytNumberKeyword, Long> implements NumberKeywordService {

	@Resource(name = "numberKeywordDao")
	public void setBaseDao(BaseDao<TytNumberKeyword, Long> numberKeywordDao) {
		super.setBaseDao(numberKeywordDao);
	}

	@Override
	public TytNumberKeyword getByKeyword(String numberKeyword) {
		List<TytNumberKeyword> nunberKeywords = this.getBaseDao().searchByHql("from TytNumberKeyword where keyword=?", new Object[] { numberKeyword }, null, null);
		return nunberKeywords.size() == 1 ? nunberKeywords.get(0) : null;
	}
}
