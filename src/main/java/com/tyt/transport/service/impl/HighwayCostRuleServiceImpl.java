package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytHighwayCostRule;
import com.tyt.transport.service.HighwayCostRuleService;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("highwayCostRuleService")
public class HighwayCostRuleServiceImpl extends BaseServiceImpl<TytHighwayCostRule, Long> implements HighwayCostRuleService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "highwayCostRuleDao")
	public void setBaseDao(BaseDao<TytHighwayCostRule, Long> highwayCostRuleDao) {
		super.setBaseDao(highwayCostRuleDao);
	}


	@Override
	public List<TytHighwayCostRule> getInitList2Mc() {
		String sql = "SELECT province,highway,GROUP_CONCAT(tonne_begin_value,'_',tonne_end_value,'_',calc_rule_type,'_',calc_rule_value) calcRuleValue FROM tyt_highway_cost_rule GROUP BY province,highway ";

		Map<String, Type> mapType = new HashMap<String, Type>();
		mapType.put("province", Hibernate.STRING);
		mapType.put("highway", Hibernate.STRING);
		mapType.put("calcRuleValue", Hibernate.STRING);

		return this.getBaseDao().search(sql,mapType,TytHighwayCostRule.class, new Object[]{});
	}
}
