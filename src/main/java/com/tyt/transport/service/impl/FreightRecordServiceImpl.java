package com.tyt.transport.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.tyt.transport.service.FreightCorrectKeysService;

import com.tyt.util.StringUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.FreightRecord;
import com.tyt.model.FreightRecordLog;
import com.tyt.transport.querybean.FreighBean;
import com.tyt.transport.service.FreightRecordLogService;
import com.tyt.transport.service.FreightRecordService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.MD5Util;

@Service("freightRecordService")
public class FreightRecordServiceImpl extends BaseServiceImpl<FreightRecord, Long> implements FreightRecordService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "freightRecordLogService")
	private FreightRecordLogService freightRecordLogService;
	@Resource(name = "freightCorrectKeysService")
	private FreightCorrectKeysService freightCorrectKeysService;
	@Resource(name = "tytConfigService")
	TytConfigService tytConfigService;

	@Resource(name = "freightRecordDao")
	public void setBaseDao(BaseDao<FreightRecord, Long> freightRecordDao) {
		super.setBaseDao(freightRecordDao);
	}

	@Override
	public void saveFreight(FreighBean freightBean) throws IllegalAccessException, InvocationTargetException {
		// 生成唯一值
		String key = StringUtil.getMapKey(freightBean.getStartProvinc(),freightBean.getStartCity(),freightBean.getStartArea(),
				freightBean.getDestProvinc(),freightBean.getDestCity(),freightBean.getDestArea());
		FreightRecord freightRecord = queryByKey(key);
		FreightRecord newFreightRecord = null;
		Integer startCoordX = (int) (Double.valueOf(freightBean.getStartCoordX()) * 100);
		Integer startCoordY = (int) (Double.valueOf(freightBean.getStartCoordY()) * 100);
		Integer destCoordX = (int) (Double.valueOf(freightBean.getDestCoordX()) * 100);
		Integer destCoordY = (int) (Double.valueOf(freightBean.getDestCoordY()) * 100);
		Integer distance = (int) (Double.valueOf(freightBean.getDistance()) * 100);
		Integer freightDistance = (int) (Double.valueOf(freightBean.getFreightDistance()) * 100);
		int isSaveIosDistance = tytConfigService.getIntValue(Constant.RECOMMEND_IS_SAVE_IOS_DISTANCE_KEY, 1);
		if (freightRecord == null && (Constant.PLAT_IOS != freightBean.getClientSign().intValue() || isSaveIosDistance == 0)) {
			// 实时处理无效的路段信息，替换后存储数据库
			if (freightBean != null && !StringUtils.isEmpty(freightBean.getProvinceRoad()) && freightBean.getStatus() != null && freightBean.getStatus().intValue() == 1 && freightBean.getProvinceRoad().indexOf("###") >= 0) {
				String resultMapData = freightCorrectKeysService.repairMapData(freightBean.getProvinceRoad());
				if (!StringUtils.isEmpty(resultMapData)) {
					freightBean.setProvinceRoad(resultMapData);
					freightBean.setStatus(new Short("0"));
				}
			}
			newFreightRecord = new FreightRecord();
			BeanUtils.copyProperties(newFreightRecord, freightBean);
			newFreightRecord.setCtime(new Date());
			newFreightRecord.setMapKey(key);
			newFreightRecord.setStartCoordX(startCoordX);
			newFreightRecord.setStartCoordY(startCoordY);
			newFreightRecord.setDestCoordX(destCoordX);
			newFreightRecord.setDestCoordY(destCoordY);
			newFreightRecord.setFreightDistance(freightDistance);
			newFreightRecord.setDistance(distance);
			this.getBaseDao().insert(newFreightRecord);
		}
		FreightRecordLog freightRecordLog = freightRecordLogService.queryByKeyAndDistance(key, distance);
		if (freightRecordLog == null) {
			FreightRecordLog newFreightRecordLog = new FreightRecordLog();
			BeanUtils.copyProperties(newFreightRecordLog, freightBean);
			newFreightRecordLog.setCtime(new Date());
			newFreightRecordLog.setMapKey(key);
			newFreightRecordLog.setStartCoordX(startCoordX);
			newFreightRecordLog.setStartCoordY(startCoordY);
			newFreightRecordLog.setDestCoordX(destCoordX);
			newFreightRecordLog.setDestCoordY(destCoordY);
			newFreightRecordLog.setFreightDistance(freightDistance);
			newFreightRecordLog.setDistance(distance);
			freightRecordLogService.add(newFreightRecordLog);
		}
	}

	@Override
	public FreightRecord queryByKey(String key) {
		List<FreightRecord> freightRecords = this.getBaseDao().searchByHql("from FreightRecord where mapKey=?", new Object[] { key }, null, null);
		return freightRecords.size() == 1 ? freightRecords.get(0) : null;
	}

//	private String generateKey(FreighBean freightBean) {
//		StringBuffer sb = new StringBuffer();
//		sb.append(freightBean.getStartProvinc());
//		sb.append(freightBean.getStartCity());
//		sb.append(freightBean.getStartArea());
//		sb.append(freightBean.getDestProvinc());
//		sb.append(freightBean.getDestCity());
//		sb.append(freightBean.getDestArea());
//		return MD5Util.GetMD5Code(sb.toString());
//	}
}
