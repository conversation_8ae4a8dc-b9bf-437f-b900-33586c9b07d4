package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.FreightCorrectKeys;
import com.tyt.transport.service.FreightCorrectKeysService;
import com.tyt.util.Constant;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("freightCorrectKeysService")
public class FreightCorrectKeysServiceImpl extends BaseServiceImpl<FreightCorrectKeys, Long> implements FreightCorrectKeysService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "cacheServiceMcImpl")
	protected CacheService cacheService;

	@Resource(name = "freightCorrectKeysDao")
	public void setBaseDao(BaseDao<FreightCorrectKeys, Long> freightCorrectKeysDao) {
		super.setBaseDao(freightCorrectKeysDao);
	}

	@Override
	public List<FreightCorrectKeys> findAll() {
		String sql = "from FreightCorrectKeys ";
		return this.getBaseDao().find(sql,new Object[]{});
	}

	@Override
	public String repairMapData(String mapData) {
		//校验是否需要更新基础数据
		String flag = cacheService.getString(Constant.CACHE_MAP_CORRECT_KEYS_UPDATEFLAG_KEY);
		if(StringUtils.isEmpty(flag) || !"1".equals(flag)){
			logger.info("高德地图地名纠正更新标识不存在，更新缓存");
			init();
		}
		String strTemp = "";
		String wrongStr = "";
		int wrongIndex = -1;
		strTemp = mapData;
		if(strTemp.indexOf("###") == -1){
			logger.info("线路详情不存在无效省份");
			return null;
		}
		logger.info("修复前的数据：{}",mapData);
		//循环处理多个无效地名
		do{
			wrongIndex = strTemp.indexOf("###");
			if(wrongIndex == -1){
				break;
			}
			wrongStr = strTemp.substring(wrongIndex,strTemp.indexOf("||",wrongIndex));
			strTemp = strTemp.substring(strTemp.indexOf("||",wrongIndex),strTemp.length());
			logger.info(wrongStr);
			if(StringUtils.isEmpty(wrongStr)){
				logger.info("不存在错误地市");
				continue;
			}
			//获取正确的地区名称
			String correctName = correctKeyMap.get(wrongStr.substring(3,wrongStr.length()));
			if(StringUtils.isEmpty(correctName)){
				logger.info("匹配不到正确地市，存在无效路段，入库后后续处理，【{}】",mapData);
				return null;
			}
			//替换为正确的地图名称
			mapData = mapData.replace(wrongStr,correctName);
		}while (true);
		logger.info("修复后的数据：{}",mapData);
		if(mapData.indexOf("###") == -1){
			logger.info("正确线路返回进行入库");
			// 更新数据库
			return mapData;
		}
		logger.info("线路处理失败，存在无效路段，入库后后续处理，【{}】",mapData);
		return null;
	}

	//存放正确词语对应关系表
	private static Map<String,String> correctKeyMap = new HashMap<String,String>();

	@PostConstruct
	private void init(){
		logger.info("开始地名纠正初始化");
		List<FreightCorrectKeys> freightCorrectKeys =this.findAll();
		if(freightCorrectKeys == null || freightCorrectKeys.size() <= 0){
			logger.info("地名纠正初始化为0");
			return;
		}
		correctKeyMap = new HashMap<String,String>();
		for(FreightCorrectKeys fck : freightCorrectKeys){
			correctKeyMap.put(fck.getKeyName(),fck.getMatchName());
		}
		cacheService.setString(Constant.CACHE_MAP_CORRECT_KEYS_UPDATEFLAG_KEY,"1",Constant.CALL_PHONE_TIME_CACHE_TIME);
		logger.info("地名纠正初始化成功{}",freightCorrectKeys.size());
	}
}
