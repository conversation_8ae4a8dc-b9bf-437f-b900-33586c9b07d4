package com.tyt.transport.service.impl;

import com.tyt.messagecenter.core.exception.CustomException;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.entity.base.TytTransportExposure;
import com.tyt.plat.mapper.base.TytTransportExposureMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TytTransportExposureService;
import com.tyt.util.LockUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

@Slf4j
@Service("tytTransportExposureService")
public class TytTransportExposureServiceImpl implements TytTransportExposureService {

    @Autowired
    private TytTransportExposureMapper transportExposureMapper;

    private static final Long min_change_id = 5000L;

    private synchronized Long initRedisIncr(){

        Long dbChangeId = null;
        try {
            boolean lockResult = LockUtil.lockObject("2", RedisKeyConstant.exposure_change_lock, 30);

            if(!lockResult) {
                log.error("initRedisIncr_lock_error ! ");
                return null;
            }

            dbChangeId = transportExposureMapper.getMaxChangeId();

            if(dbChangeId == null || dbChangeId < min_change_id) {
                dbChangeId = min_change_id + 1L;
            } else {
                dbChangeId = dbChangeId + 1L;
            }

            RedisUtil.set(RedisKeyConstant.IncrKey.exposure_change, dbChangeId + "", 0);

            log.info("initRedisIncr : dbChangeId : " + dbChangeId);
        } finally {
            LockUtil.unLockObject("2", RedisKeyConstant.exposure_change_lock);
        }

        return dbChangeId;
    }

    @Override
    public Long getNextChangeId(){
        Long nextChangeId = RedisUtil.incr(RedisKeyConstant.IncrKey.exposure_change);

        if(nextChangeId < min_change_id) {
            //现在没有redis，进行初始化
            nextChangeId = this.initRedisIncr();
            nextChangeId = RedisUtil.incr(RedisKeyConstant.IncrKey.exposure_change);
        }

        if(nextChangeId < min_change_id){
            log.error("ERROR_GET_NEXT_changeId");
            throw CustomException.createException();
        }
        return nextChangeId;
    }

    @Override
    public void disableExposureTransport(Long srcMsgId) {
        Long nextChangeId = this.getNextChangeId();

        Example exa = new Example(TytTransportExposure.class);
        exa.and().andEqualTo("srcMsgId", srcMsgId);

        TytTransportExposure exposure = new TytTransportExposure();
        exposure.setStatus(0);
        exposure.setChangeId(nextChangeId);

        transportExposureMapper.updateByExampleSelective(exposure, exa);
    }

}
