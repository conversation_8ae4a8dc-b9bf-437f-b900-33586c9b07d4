package com.tyt.transport.service.impl;

import com.tyt.model.Transport;
import com.tyt.model.CsMaintainedCustom;
import com.tyt.plat.entity.base.TytTransportDispatch;
import com.tyt.plat.mapper.base.TytTransportDispatchMapper;
import com.tyt.transport.bean.PublishPlatformEnum;
import com.tyt.transport.service.TytTransportDispatchService;
import com.tyt.util.Constant;
import com.tyt.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/1/6
 */
@Service("tytTransportDispatchService")
public class TytTransportDispatchServiceImpl implements TytTransportDispatchService {

    @Resource
    private TytTransportDispatchMapper tytTransportDispatchMapper;

    @Override
    public void saveTransportDispatch(Transport transport, CsMaintainedCustom csMaintainedCustom, Integer clientSign) {
        Date nowTime = new Date();
        TytTransportDispatch tytTransportDispatch = tytTransportDispatchMapper.getTytTransportDispatchBySrcId(transport.getSrcMsgId());

        String price = transport.getPrice();
        BigDecimal decPrice = null;
        if (StringUtil.isDouble(price)) {
            decPrice = new BigDecimal(price);
        }
        //TransportDispatch新增发布终端字段
        Integer publishPlatform = null;
        if (clientSign != null) {
            if (Integer.valueOf(Constant.ClientSignEnum.ANDROID_GOODS.code).equals(clientSign)
                    || Integer.valueOf(Constant.ClientSignEnum.IOS_GOODS.code).equals(clientSign)) {
                publishPlatform = PublishPlatformEnum.app.getCode();
            } else if (Integer.valueOf(Constant.ClientSignEnum.PC.code).equals(clientSign)
                    || Integer.valueOf(Constant.ClientSignEnum.PC_GOODS.code).equals(clientSign)) {
                publishPlatform = PublishPlatformEnum.pc.getCode();
            }
        }
        if (tytTransportDispatch == null) {
            tytTransportDispatch = new TytTransportDispatch();
            tytTransportDispatch.setSrcMsgId(transport.getSrcMsgId());
            tytTransportDispatch.setUserId(transport.getUserId());
            tytTransportDispatch.setPublishUserId(csMaintainedCustom.getDispatcherId());
            tytTransportDispatch.setPublishUserName(csMaintainedCustom.getDispatcherName());
            tytTransportDispatch.setDispatcherId(csMaintainedCustom.getDispatcherId());
            tytTransportDispatch.setDispatcherName(csMaintainedCustom.getDispatcherName());
            tytTransportDispatch.setOwnerFreight(decPrice);
            tytTransportDispatch.setCreateTime(nowTime);
            tytTransportDispatch.setModifyTime(nowTime);
            tytTransportDispatch.setPublishPlatform(publishPlatform);
            tytTransportDispatchMapper.insertSelective(tytTransportDispatch);
        } else {
            tytTransportDispatch.setPublishUserId(csMaintainedCustom.getDispatcherId());
            tytTransportDispatch.setPublishUserName(csMaintainedCustom.getDispatcherName());
            tytTransportDispatch.setDispatcherId(csMaintainedCustom.getDispatcherId());
            tytTransportDispatch.setDispatcherName(csMaintainedCustom.getDispatcherName());
            tytTransportDispatch.setOwnerFreight(decPrice);
            tytTransportDispatch.setModifyTime(nowTime);
            tytTransportDispatch.setPublishPlatform(publishPlatform);
            tytTransportDispatchMapper.updateByPrimaryKeySelective(tytTransportDispatch);
        }

    }

    @Override
    public void saveTransportDispatch(Transport transport, CsMaintainedCustom csMaintainedCustom, Integer clientSign, String giveGoodsPhone, String giveGoodsName) {
        Date nowTime = new Date();
        TytTransportDispatch tytTransportDispatch = tytTransportDispatchMapper.getTytTransportDispatchBySrcId(transport.getSrcMsgId());

        String price = transport.getPrice();
        BigDecimal decPrice = null;
        if (StringUtil.isDouble(price)) {
            decPrice = new BigDecimal(price);
        }
        //TransportDispatch新增发布终端字段
        Integer publishPlatform = null;
        if (clientSign != null) {
            if (Integer.valueOf(Constant.ClientSignEnum.ANDROID_GOODS.code).equals(clientSign)
                    || Integer.valueOf(Constant.ClientSignEnum.IOS_GOODS.code).equals(clientSign)) {
                publishPlatform = PublishPlatformEnum.app.getCode();
            } else if (Integer.valueOf(Constant.ClientSignEnum.PC.code).equals(clientSign)
                    || Integer.valueOf(Constant.ClientSignEnum.PC_GOODS.code).equals(clientSign)) {
                publishPlatform = PublishPlatformEnum.pc.getCode();
            }
        }
        if (tytTransportDispatch == null) {
            tytTransportDispatch = new TytTransportDispatch();
            tytTransportDispatch.setSrcMsgId(transport.getSrcMsgId());
            tytTransportDispatch.setUserId(transport.getUserId());
            tytTransportDispatch.setPublishUserId(csMaintainedCustom.getDispatcherId());
            tytTransportDispatch.setPublishUserName(csMaintainedCustom.getDispatcherName());
            tytTransportDispatch.setDispatcherId(csMaintainedCustom.getDispatcherId());
            tytTransportDispatch.setDispatcherName(csMaintainedCustom.getDispatcherName());
            tytTransportDispatch.setOwnerFreight(decPrice);
            tytTransportDispatch.setCreateTime(nowTime);
            tytTransportDispatch.setModifyTime(nowTime);
            tytTransportDispatch.setGiveGoodsPhone(giveGoodsPhone);
            tytTransportDispatch.setGiveGoodsName(giveGoodsName);
            tytTransportDispatch.setPublishPlatform(publishPlatform);
            tytTransportDispatchMapper.insertSelective(tytTransportDispatch);
        } else {
            tytTransportDispatch.setPublishUserId(csMaintainedCustom.getDispatcherId());
            tytTransportDispatch.setPublishUserName(csMaintainedCustom.getDispatcherName());
            tytTransportDispatch.setDispatcherId(csMaintainedCustom.getDispatcherId());
            tytTransportDispatch.setDispatcherName(csMaintainedCustom.getDispatcherName());
            tytTransportDispatch.setOwnerFreight(decPrice);
            tytTransportDispatch.setModifyTime(nowTime);
            tytTransportDispatch.setPublishPlatform(publishPlatform);
            tytTransportDispatchMapper.updateByPrimaryKeySelective(tytTransportDispatch);
        }
    }

    @Override
    public int countExcellentByPhone(String cellPhone, Date startTime, Date endTime) {
        return tytTransportDispatchMapper.countExcellentByPhone(cellPhone, startTime, endTime);
    }
}
