package com.tyt.transport.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.*;
import com.tyt.transport.service.FreightPriceDetailInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("freightPriceDetailInfoService")
public class FreightPriceDetailInfoServiceImpl extends BaseServiceImpl<FreightPriceDetailInfo, Long> implements FreightPriceDetailInfoService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "freightPriceDetailInfoDao")
	public void setBaseDao(BaseDao<FreightPriceDetailInfo, Long> freightPriceDetailInfo) {
		super.setBaseDao(freightPriceDetailInfo);
	}

}
