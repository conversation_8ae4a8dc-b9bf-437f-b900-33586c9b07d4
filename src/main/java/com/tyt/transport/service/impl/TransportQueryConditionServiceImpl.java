package com.tyt.transport.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TransportQueryCondition;
import com.tyt.transport.service.TransportQueryConditionService;
@Service("transportQueryConditionService")
public class TransportQueryConditionServiceImpl extends BaseServiceImpl<TransportQueryCondition, Long> implements TransportQueryConditionService {

	@Resource(name="transportQueryConditionDao")
	public void setBaseDao(BaseDao<TransportQueryCondition, Long> transportQueryCondition) {
	        super.setBaseDao(transportQueryCondition);
	}
	
		

}
