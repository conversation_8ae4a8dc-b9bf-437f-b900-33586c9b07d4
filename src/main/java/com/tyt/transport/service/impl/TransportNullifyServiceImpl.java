package com.tyt.transport.service.impl;

import com.tfc.analysis.KWSeeker;
import com.tfc.analysis.entity.Keyword;
import com.tfc.analysis.process.WordFinder;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.cache.ConfigService;
import com.tyt.model.TransportNullifyBean;
import com.tyt.plat.entity.base.TytMachineTypeWhitelist;
import com.tyt.plat.mapper.base.TytMachineTypeWhitelistMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.dao.TransportNullifyDao;
import com.tyt.transport.service.TransportNullifyService;
import com.tyt.transport.service.TransportService;
import com.tyt.transport.service.TransportVaryService;
import com.tyt.user.service.UserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 无效货源信息
 * User: Administrator
 * Date: 17-04-24
 * Time: 下午3:16
 * ===========================modify list===================================
 *  modify by tianjw on 20170627   货物自动无效状态修改为撤销状态
 */
@Service("transportNullifyService")
public class TransportNullifyServiceImpl extends BaseServiceImpl<TransportNullifyBean, Long> implements TransportNullifyService {

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "transportService")
	private TransportService transportService;

	@Resource(name = "configService")
	private ConfigService configService;

	@Resource(name = "transportVaryService")
	private TransportVaryService transportVaryService;

	@Resource(name = "userService")
	private UserService userService;

    @Resource(name = "tytMachineTypeWhitelistMapper")
    private TytMachineTypeWhitelistMapper tytMachineTypeWhitelistMapper;

    // 货名白名单缓存key
    private static final String WHITELIST_CACHE_KEY = "tyt:machine:whitelist";

	private static String SEARCH_NULLFY_KEY ="searchNullify";

	private static int  SEARCH_NULLFY_TIME =5*60;

	private static WordFinder wordFinder = new WordFinder();
	@Resource(name = "transportNullifyDao")
	public void setBaseDao(BaseDao<TransportNullifyBean, Long> transportDao) {
		super.setBaseDao(transportDao);
	}
	@PostConstruct
	public void init(){
		logger.info("初始化nullifyKey数据");
		List<String> beans = ((TransportNullifyDao) this.getBaseDao()).getAllNullifyKeyword();
		RedisUtil.setObjectList(SEARCH_NULLFY_KEY, beans, SEARCH_NULLFY_TIME);
		System.out.println("TransportNullifyServiceImpl初始化成功！==========================");
	}
	@Override
	public String verifyNullifyTaskContent(String taskContent) {
		//获取货源信息
		if(!org.springframework.util.StringUtils.hasLength(taskContent)){
			logger.info("货物信息描述为空");
			return null;
		}
		// 6450 货名先过白名单校验，再走黑名单
		taskContent = filterWhitelistWord(taskContent);

		List<Keyword> keywordList = fetchNullifyKeywords();
		//优化20171120
		List<String> result = getKeywordByTaskContentAndRemark(taskContent, keywordList);
		//把LIST按逗号拼接成字符串

		String matchingKeyword = StringUtils.join(result,",");
		logger.info("触发关键词，存在关键字：{}", matchingKeyword);
		return matchingKeyword;
	}

	/**
	 *  对货物信息分词处理
	 *  IKAnalyzer 最小粒度分词
	 * @param taskContent
	 * @return  分词的列表
	 */
	public List<String> getKeywordByTaskContentAndRemark(String taskContent,List<Keyword> keywordList){
		List<String> keywords = new ArrayList<String>();
		keywords = extractKeywords(taskContent, keywords,keywordList);
		return keywords;

	}

	private List<String> extractKeywords(String keyword, List<String> matchesKeywords,List<Keyword> keywordList) {
		if(keyword != null && keyword.length() > 0){
			Set<String> extractResult = extractResult(keyword, keywordList);
			matchesKeywords.addAll(extractResult);
		}
		return matchesKeywords;
	}

	private Set<String> extractResult(String keyword, List<Keyword> keywordList) {
		KWSeeker kw1 = KWSeeker.getInstance(keywordList);
		// 找出文本中所有含有上面词库中的词！
		Set<String> extractResult = (Set<String>) kw1.process(wordFinder, keyword, null);
		return extractResult;
	}

	private List<Keyword> fetchNullifyKeywords() {
		List<String> beans = RedisUtil.getObjectList(SEARCH_NULLFY_KEY);
		if(beans==null||beans.size()<1){
			//重新加载
			beans = ((TransportNullifyDao) this.getBaseDao()).getAllNullifyKeyword();
			RedisUtil.setObjectList(SEARCH_NULLFY_KEY, beans, SEARCH_NULLFY_TIME);
		}
		List<Keyword> nullifyKeywordsList = new ArrayList<Keyword>();
		for (int i = 0; i < beans.size(); i++) {
			nullifyKeywordsList.add(new Keyword(beans.get(i)));
		}
		return nullifyKeywordsList;
	}

	private static boolean isNumericUnit(String str){
		if(str==null||str.equals(""))return false;
		Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?[米,吨,台,座,辆,号,个,件,元]*");
		return pattern.matcher(str).matches();
	}
    /**
     * 过滤掉在货名白名单里的词
     *
     * @param content 内容
     * @return 返回去掉白名单的内容
     */
    private String filterWhitelistWord(String content) {
        // 从缓存获取货名白名单
        List<String> beans = RedisUtil.getObjectList(WHITELIST_CACHE_KEY);
        if (CollectionUtils.isEmpty(beans)) {
            // 重新加载
            List<TytMachineTypeWhitelist> whitelist = tytMachineTypeWhitelistMapper.selectAll();
			if (CollectionUtils.isNotEmpty(whitelist)) {
				beans = whitelist.stream().map(TytMachineTypeWhitelist::getShowName).collect(Collectors.toList());
				RedisUtil.setObjectList(WHITELIST_CACHE_KEY, beans, SEARCH_NULLFY_TIME);
			}
        }
		if (CollectionUtils.isEmpty(beans)) {
			return content;
		}

        // 查找匹配词
        Keyword[] keywords = beans.stream().map(Keyword::new).toArray(Keyword[]::new);
        KWSeeker kw1 = KWSeeker.getInstance(keywords);
        Set<String> words = kw1.findWords(content);

        // 移除白名单的词，先移除最长的词
        AtomicReference<String> res = new AtomicReference<>(content);
        words.stream().sorted(Comparator.comparingInt(String::length).reversed())
                .forEach(word -> res.set(res.get().replace(word, "")));
        return res.get();
    }

}
