package com.tyt.transport.service.impl;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.tyt.transport.querybean.FallShortSearchLogBean;
import com.tyt.transport.querybean.ScopeSearchQueryBean;
import org.springframework.stereotype.Service;

import com.tyt.transport.service.TransportLogService;
import com.tyt.transport.service.TransportLogUtilService;
import com.tyt.util.TytLogThread;

@Service("transportLogService")
public class TransportDBLogServiceImpl implements TransportLogService {


	@Resource(name = "transportLogUtilService")
	public TransportLogUtilService transportLogUtilService;

	public static ThreadPoolExecutor logThreadPool;
	static {
		logThreadPool=new ThreadPoolExecutor(20
				,100
				,60
				,TimeUnit.SECONDS
				,new LinkedBlockingQueue<Runnable>(1000)
				,new ThreadPoolExecutor.CallerRunsPolicy()
				);
	}

	@Override
	public void detailsLog(long userId, long tsId, String clientVersion,
						   int clientSign, int status, int viewSource, int sortType, int sortIndex, int specialMark,
						   String startProvinc, String startCity, String startArea,
						   String destProvinc, String destCity, String destArea, String benefitLabelCode, Long sharerUserId) {
		TytLogThread tytLogThread = TytLogThread.detailsLog(1, userId, tsId, clientVersion, clientSign, status, viewSource, sortType, sortIndex, specialMark, startProvinc, startCity, startArea, destProvinc, destCity, destArea, benefitLabelCode, sharerUserId);
		tytLogThread.setTransportLogUtilService(transportLogUtilService);
		logThreadPool.execute(tytLogThread);
	}

	@Override
	public void searchLog(long userId, String startCoord, String startRange,
			String destCoord, String destRange, long carId, String headNo,
			String headCity, String clientVersion, int clientSign, int sortType,String numberType,String osVersion,String clientId, String carLength, String carType, String specialRequired,String startWeight,String endWeight) {
		TytLogThread tytLogThread=TytLogThread.searchLog(2, userId, startCoord, startRange, destCoord, destRange, carId, headNo, headCity, clientVersion, clientSign, sortType,numberType,osVersion,clientId, carLength, carType, specialRequired,startWeight,endWeight);
		tytLogThread.setTransportLogUtilService(transportLogUtilService);
		logThreadPool.execute(tytLogThread);
	}

	@Override
	public void searchDistanceSortLog(long userId, int sortType,
			String clientVersion, int clientSign) {
		TytLogThread tytLogThread=TytLogThread.searchDistanceSortLog(3, userId, sortType, clientVersion, clientSign);
		tytLogThread.setTransportLogUtilService(transportLogUtilService);
		logThreadPool.execute(tytLogThread);

	}

	@Override
	public void callPhoneLog(long userId, long tsId, String clientVersion,
			int clientSign) {

	}

    @Override
    public void searchFallShortLog(FallShortSearchLogBean logBean) {
        TytLogThread tytLogThread=TytLogThread.searchFallShortLog(4,logBean);
        tytLogThread.setTransportLogUtilService(transportLogUtilService);
        logThreadPool.execute(tytLogThread);
    }
}
