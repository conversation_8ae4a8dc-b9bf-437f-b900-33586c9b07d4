package com.tyt.transport.service.impl;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCallPhoneLimitNew;
import com.tyt.transport.service.TytCallPhoneLimitNewService;

@Service("callPhoneLimitNewService")
public class TytCallPhoneLimitNewServiceImpl extends BaseServiceImpl<TytCallPhoneLimitNew, Long> implements TytCallPhoneLimitNewService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "callPhoneLimitNewDao")
	public void setBaseDao(BaseDao<TytCallPhoneLimitNew, Long> callPhoneLimitNewDao) {
		super.setBaseDao(callPhoneLimitNewDao);
	}
}
