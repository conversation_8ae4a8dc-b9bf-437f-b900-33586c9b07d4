package com.tyt.transport.service.impl;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytCommonUserMachineType;
import com.tyt.transport.service.CommonUserMachineTypeService;
import com.tyt.util.Constant;

@Service("commonUserMachineTypeService")
public class CommonUserMachineTypeServiceImpl extends BaseServiceImpl<TytCommonUserMachineType, Long> implements CommonUserMachineTypeService {

	@Resource(name = "commonUserMachineTypeDao")
	public void setBaseDao(BaseDao<TytCommonUserMachineType, Long> commonUserMachineTypeDao) {
		super.setBaseDao(commonUserMachineTypeDao);
	}

	@Override
	public List<TytCommonUserMachineType> queryCommonUseMachineType() {
		return this.getBaseDao().searchByHql("select new com.tyt.model.TytCommonUserMachineType(machineTypeName) from TytCommonUserMachineType order by priority asc", new Object[] {}, 1, Constant.COMMON_USER_MACHINE_TYPE_SIZE);
	}
}
