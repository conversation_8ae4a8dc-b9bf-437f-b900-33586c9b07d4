package com.tyt.transport.service.impl;

import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.plat.mapper.base.TytTransportMainExtendMapper;
import com.tyt.transport.service.SeckillGoodsTransportService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SeckillGoodsTransportServiceImpl implements SeckillGoodsTransportService {

    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;

    @Autowired
    private TransportService transportService;

    @Autowired
    private TransportMainService transportMainService;

    @Override
    public boolean checkIsSeckillGoodsTransportAndIsLock(Long srcMsgId) {
        if (srcMsgId == null || srcMsgId <= 0) {
            return false;
        }

        TransportMain oldTransportMain = transportMainService.getById(srcMsgId);
        if (oldTransportMain == null) {
            Transport oldTransport = transportService.getById(srcMsgId);
            if (oldTransport != null && oldTransport.getSrcMsgId() != null) {
                srcMsgId = oldTransport.getSrcMsgId();
            } else {
                return false;
            }
        }

        Integer transportIsSeckillGoods = tytTransportMainExtendMapper.getTransportIsSeckillGoods(srcMsgId);
        if (transportIsSeckillGoods == null || transportIsSeckillGoods == 0) {
            //不是秒抢货源，不算锁定状态
            return false;
        }

        Integer seckillGoodsHaveSuccess = tytTransportMainExtendMapper.getSeckillGoodsHaveSuccess(srcMsgId);
        if (seckillGoodsHaveSuccess != null && seckillGoodsHaveSuccess > 0) {
            //存在分配成功的订单就不锁定
            return false;
        }

        Integer seckillGoodsHaveError = tytTransportMainExtendMapper.getSeckillGoodsHaveError(srcMsgId);
        if (seckillGoodsHaveError != null && seckillGoodsHaveError > 0) {
            //不存在分配成功的订单，并且存在分配失败的订单
            return true;
        }

        return false;
    }

}
