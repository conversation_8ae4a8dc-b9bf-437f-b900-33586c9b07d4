package com.tyt.transport.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.adposition.enums.DwsIdentityTypeEnum;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.enumConstant.ResponseCodeEnum;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.elasticsearch.ElasticTransportHelper;
import com.tyt.infofee.bean.InfoFeeMyPublishGoodsResultBean;
import com.tyt.infofee.bean.TransportUserBean;
import com.tyt.infofee.bean.WechatShareTransportBean;
import com.tyt.infofee.enums.PublishTypeEnum;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.mybatis.mapper.BackoutReasonMapper;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.client.transport.ThPriceClient;
import com.tyt.plat.entity.base.TytExcellentPriceConfig;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.plat.entity.base.TytTransportVO;
import com.tyt.plat.enums.TransportBusinessTypeEnum;
import com.tyt.plat.enums.TransportStatusEnum;
import com.tyt.plat.enums.TsSortTypeEnum;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.utils.CityUtil;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.plat.vo.ts.TransportOrderCountVo;
import com.tyt.service.common.elasticserarch.TransformUtil;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.dao.TransportDao;
import com.tyt.transport.dao.TransportMainDao;
import com.tyt.transport.enums.TransportPublishTypeEnum;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.*;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.StringUtil;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSourceUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("transportMainService")
public class TransportMainServiceImpl extends BaseServiceImpl<TransportMain, Long> implements TransportMainService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "transportCollectService")
    private TransportCollectService collectService;
    @Resource(name = "transportService")
    private TransportService transportService;
    @Autowired
    private TransportDao transportDao;
    @Autowired
    private TransportMainDao transportMainDao;
    @Autowired
    private BackendTransportMapper backendTransportMapper;
    @Autowired
    private BackoutReasonMapper backoutReasonMapper;

    @Autowired
    private TytTransportMainMapper tytTransportMainMapper;

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Autowired
    private TransportBusinessInterface transportBusinessInterface;

    @Autowired
    private IFeedbackUserService feedbackUserService;

    @Autowired
    private TytTransportQuotedPriceMapper tytTransportQuotedPriceMapper;

    @Autowired
    private TytAppCallLogMapper tytAppCallLogMapper;
    @Autowired
    private TransportBusiness transportBusiness;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private ExcellentPriceConfigService excellentPriceConfigService;

    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Autowired
    private ThPriceClient thPriceClient;
    @Autowired
    private TytCustomFirstOrderRecordMapper tytCustomFirstOrderRecordMapper;

    @Resource(name = "transportMainDao")
    public void setBaseDao(BaseDao<TransportMain, Long> transportMainDao) {
        super.setBaseDao(transportMainDao);
    }


    @Override
    public void addTransportMain(Long id) throws Exception {
        ((TransportMainDao) this.getBaseDao()).addTransportMain(id);
    }

    @Override
    public List<TransportBean> queryTransportHistory(long user_id, int status, int queryType, long querySign) {

        // 上滑动下滑动最大结果集大小
        int pageSize = AppConfig.getIntProperty("tyt.tyt_transport.query.page.size").intValue();
        List<TransportBean> returnTbList = null;
        List<Object> list = new ArrayList<Object>();
        // StringBuffer sbCount = new StringBuffer("select Count(*)  ");
        StringBuffer sb = new StringBuffer(" from tyt_transport_main where 1=1 and source=? and display_type=? and is_info_fee=?");
        list.add("0");
        list.add("1");
        list.add("0");
        StringBuffer sbSql = new StringBuffer("select *  ");

        sb.append(" and user_id=?");
        list.add(user_id);

        // -1表示查询所有
        if (status == -1) {
            sb.append(" and status in (1,4,5) ");
        } else {
            sb.append(" and status=?");
            list.add(status);
        }

        int searchSize = pageSize;

        if (querySign <= 1) {
            queryType = 1;
        }

        // 第一次请求
        if (queryType == 1) {
            // 大小排序
            sb.append(" order by id desc ");
        } else
            // 下拉查新数据
            if (queryType == 0) {
                sb.append(" and id>?");
                // 小大排序
                sb.append(" order by id asc ");
                list.add(querySign);
            }
            // 上推查历史数据
            else {
                sb.append(" and id<?");
                // 大小排序
                sb.append(" order by id desc ");
                list.add(querySign);
            }

        sbSql.append(sb);
        // sbCount.append(sb);
        // 查询数据集
        List<TransportMain> transportList = this.getBaseDao().search(sbSql.toString(), list.toArray(), 1, searchSize);

        int size = transportList.size();

        if (transportList != null && transportList.size() > 0) {
            returnTbList = new ArrayList<TransportBean>();
            // 查新数据正序
            if (queryType == 0) {
                for (int i = size - 1; i >= 0; i--) {
                    TransportMain t = transportList.get(i);
                    TransportBean tb = new TransportBean();
                    // copy
                    BeanUtils.copyProperties(t, tb);
                    // beanCopy(t, tb);
                    tb.setPubDate(t.getCtime());
                    returnTbList.add(tb);
                }
            } else {
                for (int i = 0; i < size; i++) {
                    TransportMain t = transportList.get(i);
                    TransportBean tb = new TransportBean();
                    // copy
                    BeanUtils.copyProperties(t, tb);
                    // beanCopy(t, tb);
                    tb.setPubDate(t.getCtime());
                    returnTbList.add(tb);
                }
            }

        }
        return returnTbList;
    }

    // public void beanCopy(TransportMain transportMain,TransportBean
    // transportBean)
    // {
    //
    // transportBean.setDestCoord(transportMain.getDestCoord());
    // transportBean.setDestDetailAdd(transportMain.getDestDetailAdd());
    // transportBean.setDestLatitude(transportMain.getDestLatitude()==null?0d:transportMain.getDestLatitude().doubleValue()/100);
    // transportBean.setDestLongitude(transportMain.getDestLongitude()==null?0d:transportMain.getDestLongitude().doubleValue()/100);
    // transportBean.setDestPoint(transportMain.getDestPoint());
    // transportBean.setDistance(transportMain.getDistance()==null?0d:transportMain.getDistance().doubleValue()/100);
    // transportBean.setId(transportMain.getId());
    // transportBean.setPrice(transportMain.getPrice());
    // transportBean.setPubDate(transportMain.getPubDate());
    // transportBean.setRemark(transportMain.getRemark());
    // transportBean.setStartCoord(transportMain.getStartCoord());
    // transportBean.setStartDetailAdd(transportMain.getStartDetailAdd());
    // transportBean.setStartLatitude(transportMain.getStartLatitude()==null?0d:transportMain.getStartLatitude().doubleValue()/100);
    // transportBean.setStartLongitude(transportMain.getStartLongitude()==null?0d:transportMain.getStartLongitude().doubleValue()/100);
    // transportBean.setStartPoint(transportMain.getStartPoint());
    // transportBean.setStatus(transportMain.getStatus());
    // transportBean.setTaskContent(transportMain.getTaskContent());
    // transportBean.setUserId(transportMain.getUserId());
    // transportBean.setVerifyFlag(transportMain.getVerifyFlag());
    //
    // }
//	@Override
//	public long myGoodsLimitFromDB(Long userId) throws Exception {
//		List<Object> listObject = new ArrayList<Object>();
//
//		StringBuffer sbCount = new StringBuffer("SELECT count(*) FROM tyt_transport_main t where 1=1 ");
//		StringBuffer sb = new StringBuffer();
//
//		if (userId != null && userId.longValue() > 0L) {
//			sb.append(" AND t.user_id=? ");
//			listObject.add(userId);
//		}
//
//		sb.append(" AND date_format(t.ctime,'%Y-%m-%d')=? ");
//		listObject.add(TimeUtil.formatDate(new Date()));
//		sbCount.append(sb);
//
//		BigInteger rowCount = this.getBaseDao().query(sbCount.toString(), listObject.toArray());
//		return rowCount.longValue();
//	}

//	@Override
//	public boolean myGoodsLimit(Long userId) throws Exception {
//		String goodsNum = cacheService.getString(Constant.CACHE_INFO_NUMBER_KEY + TimeUtil.formatDateMonthTime(new Date()) + "_" + userId);
//		long counts = 0;
//		if (goodsNum == null || goodsNum.equals("")) {
//			counts = myGoodsLimitFromDB(userId);
//			cacheService.setString(Constant.CACHE_INFO_NUMBER_KEY + TimeUtil.formatDateMonthTime(new Date()) + "_" + userId, counts + "", Constant.CACHE_EXPIRE_LIMIT);
//		} else {
//			counts = Long.valueOf(goodsNum);
//		}
//		return counts >= tytConfigService.getIntValue(Constant.CONFIG_TRANSPORTCOUNTS);
//		// return
//		// counts>=AppConfig.getIntProperty("tyt.transport.end.limit.number");
//	}

    @SuppressWarnings("deprecation")
    @Override
    public List<Long> getTodayIdListBySrcMsgId(Long srcMsgId, Long userId, Integer newStatus) {
        try {
            StringBuffer sql = null;
            List<Object> params = new ArrayList<Object>();
            params.add(userId);
            params.add(srcMsgId);
            if (newStatus == 0) {
                sql = new StringBuffer("select id from tyt_transport_main " + " where user_id=? and src_msg_id=? and (status=? or status=? or status=?)  and ctime>=?");
                params.add(1);
                params.add(4);
                params.add(5);
            } else if (newStatus == 4) {
                sql = new StringBuffer("select id from tyt_transport_main " + " where user_id=? and src_msg_id=? and status=?  and ctime>=?");
                params.add(1);
            } else if (newStatus == 5) {
                sql = new StringBuffer("select id from tyt_transport_main " + " where user_id=? and src_msg_id=? and status=?  and ctime>=?");
                params.add(1);
            }

            params.add(TimeUtil.formatDateTime(TimeUtil.today()));
            Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
            map.put("id", Hibernate.LONG);
            List<Long> list = this.getBaseDao().search(sql.toString(), map, null, params.toArray(), 1, 10000);
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

    }

    @SuppressWarnings("deprecation")
    @Override
    public List<Long> getHistoryIdListBySrcMsgId(Long srcMsgId, Long userId) {
        try {
            StringBuffer sql = new StringBuffer("select id from tyt_transport_main " + " where user_id=? and src_msg_id=? and display_type=? and ctime<?");
            // StringBuffer sql = new StringBuffer(
            // "select id from tyt_transport_main "
            // +
            // " where user_id=? and hash_code=? and status=? and display_type=? and ctime<?");
            List<Object> params = new ArrayList<Object>();
            params.add(userId);
            params.add(srcMsgId);
            // params.add(1);
            params.add(1);
            params.add(TimeUtil.formatDateTime(TimeUtil.today()));
            Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
            map.put("id", Hibernate.LONG);
            List<Long> list = this.getBaseDao().search(sql.toString(), map, null, params.toArray(), 1, 10000);
            return list;
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            throw new RuntimeException(e);
        }

    }

    @Override
    public boolean updateStatusByIds(List<Long> idList, Integer status, String infoStatus, Integer display) {
        /**
         * 优化后 只有一条数据所以根据srcMsgId 更改 状态
         */
        Transport transport = null;
        if (idList != null && idList.size() > 0) {
            transport = transportService.getLastBySrcMygId(idList.get(0));
        }
        if (transport != null) {
            try {
                if (infoStatus == null || infoStatus.trim().equals("")) {
                    StringBuffer sql = new StringBuffer("update tyt_transport_main set " + "status=:status,display_type=:display,mtime=:mtime ,is_display=:is_display " + " where src_msg_id =:srcMsgId");
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("status", status);
                    map.put("display", display);
                    map.put("mtime", TimeUtil.formatDateTime(new Date()));
                    map.put("is_display", 1);
                    map.put("srcMsgId", transport.getSrcMsgId());
                    return this.executeUpdateSql(sql.toString(), map) > 0;
                } else {
                    StringBuffer sql = new StringBuffer("update tyt_transport_main set " + "status=:status,info_status=:infoStatus,display_type=:display,mtime=:mtime,is_display=:is_display " + " where src_msg_id =:srcMsgId");
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("status", status);
                    map.put("infoStatus", infoStatus);
                    map.put("display", display);
                    map.put("mtime", TimeUtil.formatDateTime(new Date()));
                    map.put("is_display", 1);
                    map.put("srcMsgId", transport.getSrcMsgId());
                    return this.executeUpdateSql(sql.toString(), map) > 0;
                }

            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        return false;
    }

    @Override
    public int updateDisplayTypeByIds(List<Long> idList, Integer display) {
        StringBuffer sql = new StringBuffer("update tyt_transport_main set display_type=:display" + " where id IN(:ids)");
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("display", display);
            map.put("ids", idList);
            return this.executeUpdateSql(sql.toString(), map);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            // 提醒垃圾回收
            sql = null;
        }

    }

//	@Override
//	public Integer getMaxResendCountsByHashCode(String hashCode, Long userId) {
//		StringBuffer sql = new StringBuffer("select MAX(resend_counts) from tyt_transport_main " + " where hash_code=? and user_id=?");
//		List<Object> params = new ArrayList<Object>();
//		try {
//			params.add(hashCode);
//			params.add(userId);
//			return this.getBaseDao().query(sql.toString(), params.toArray());
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		} finally {
//			// 提醒垃圾回收
//			params = null;
//			sql = null;
//		}
//	}

    @Override
    public Integer getLastStatusByHashCode(String hashCode, Long userId) {
        StringBuffer sql = new StringBuffer("select status from tyt_transport_main " + " where hash_code=? and user_id=? order by id desc");
        List<Object> params = new ArrayList<Object>();
        try {
            params.add(hashCode);
            params.add(userId);
            Short status = this.getBaseDao().query(sql.toString(), params.toArray());
            return status == null ? -1 : status.intValue();// 数据库误删值为-1
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            // 提醒垃圾回收
            params = null;
            sql = null;
        }
    }

    @SuppressWarnings("deprecation")
    @Override
    public CallLogBean getCallLog(Long userId, Long tsId) {
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("callStatus", Hibernate.STRING);
        String sql = "SELECT tacl.`call_result_code` AS 'callStatus' FROM tyt_app_call_log tacl WHERE tacl.`caller_id`=? AND tacl.`src_msg_id`=?";
        List<CallLogBean> callLogBeans = this.getBaseDao().search(sql, scalarMap, CallLogBean.class, new Object[]{userId, tsId});
        return callLogBeans.size() == 0 ? null : callLogBeans.get(0);
    }

    @Override
    public TransportMain getTransportMainForId(Long tsId) {
        // tyt_plat_transport_optimize20171123 货源优化代码
        TransportMain transportMain = this.getById(tsId);
        if (transportMain != null) {
            return transportMain;
        }
        Transport oldTransport = transportService.getById(tsId);
        if (oldTransport != null) {
            transportMain = this.getById(oldTransport.getSrcMsgId());
        }
        return transportMain;
    }

    /**
     * 判断转一口价按钮是否显示
     *
     * @param goodsResultBean
     */
    private void initPriceButtonShow(InfoFeeMyPublishGoodsResultBean goodsResultBean) {
        int priceButtonShow = 0;

        Integer publishType = goodsResultBean.getPublishType();

        if (publishType != null && publishType.intValue() == PublishTypeEnum.tel.getCode().intValue()) {
            String startPoint = goodsResultBean.getStartPoint();
            String startDetailAdd = goodsResultBean.getStartDetailAdd();
            String destPoint = goodsResultBean.getDestPoint();

            String price = goodsResultBean.getPrice();

            BigDecimal infoFee = goodsResultBean.getInfoFee();

            //出发地，详情，目的地，运费，信息费都不能为空
            if (StringUtils.isNotBlank(startPoint)
                    && StringUtils.isNotBlank(startDetailAdd)
                    && StringUtils.isNotBlank(destPoint)
                    && StringUtils.isNotBlank(price)
                    && infoFee != null
                    && infoFee.compareTo(BigDecimal.ZERO) > 0) {
                Integer orderCount = goodsResultBean.getOrderCount();
                if (orderCount == null || orderCount <= 0) {
                    priceButtonShow = 1;
                }
            }
        }
        goodsResultBean.setPriceButtonShow(priceButtonShow);
    }

    @SuppressWarnings("deprecation")
    @Override
    public TransportCollectBean getInfoFeeMyPublish(Integer queryMenuType, Long userId, Integer queryActionType, Long queryID, String clientSign, Integer quotedPriceQueryType, Integer callQueryType) {
        //货源信息聚合实体类对象(货源列表、货源总数量)
        TransportCollectBean transportCollectBean = new TransportCollectBean();
        //是否使用es搜索引擎开关
//		int esOnOff = tytConfigService.getIntValue("tyt_use_es_onoff",0);
        /**
         * esOnOff = 0, 在app应用中，撤销后的货源，点击直接发布，会直接转向我的发布页面。
         * 因为定时任务同步mysql数据，存在秒级别的延迟，所以在我的发布里，不能直接显示我的货源，刷新后才会有所显示。
         * 我的发布访问频次并不高，故而esOnOff = 0，直接查询数据库
         */
        int esOnOff = 0;

        List<Object> params = new ArrayList<Object>();
        List<InfoFeeMyPublishGoodsResultBean> goodsList = null;
        /* 信息费我的货源列表查询天数，后台配置 */
        Integer infoFeeMyPublishDays = tytConfigService.getIntValue("infoFeeMyPublishDays");
        if (infoFeeMyPublishDays == null)
            infoFeeMyPublishDays = 30;
        String startDateStr = TimeUtil.formatDate(TimeUtil.dateDiff(0 - infoFeeMyPublishDays));
        String todayDateStr = TimeUtil.formatDate(new Date());
        String tomorrowDateStr = TimeUtil.formatDate(TimeUtil.dateDiff(1));

        /* queryMenuType:1发布中 2已撤销 3已过期 */
        StringBuffer esSQL = new StringBuffer();// SQL查询内容
        StringBuffer selectSQL = new StringBuffer();// SQL查询内容
        StringBuffer countSQL = new StringBuffer("select COUNT(id) from ");// SQL查询结果条数
        //StringBuffer countSQL = new StringBuffer("select COUNT(id) from tyt_transport_main where 1=1");// SQL查询结果条数

        StringBuffer conditionSQL = new StringBuffer(" and user_id=? and display_type=? and source=? and is_delete = 0");// SQL查询条件
        StringBuffer orderSQL = new StringBuffer();// 排序方式SQL
        params.add(userId);
        params.add("1");
        params.add(0);// 只显示人工的信息

        /* 信息费我的货源列表查询条数，后台配置 */
        Integer infoFeeMyPublishQuerySize = null;
        if (null != clientSign && clientSign.equals("1")) {
            infoFeeMyPublishQuerySize = tytConfigService.getIntValue("infoFeeMyPublishQueryPcSize");
            // 如果是pc端，过滤掉优车货源
            conditionSQL.append(" and excellent_goods != 1");
        } else {
            infoFeeMyPublishQuerySize = tytConfigService.getIntValue("infoFeeMyPublishQuerySize");
        }
        if (infoFeeMyPublishQuerySize == null) {
            infoFeeMyPublishQuerySize = 30;
        }

        String srcMsgIdParamStr = null;
        if ((callQueryType != null && callQueryType == 1) || (quotedPriceQueryType != null && quotedPriceQueryType == 1)) {
            Set<Long> srcMsgIdParamSet = getQuoteCallQuerySrcMsgIdSet(userId, quotedPriceQueryType, callQueryType);
            if (!srcMsgIdParamSet.isEmpty()) {
                srcMsgIdParamStr = srcMsgIdParamSet.stream().map(String::valueOf).collect(Collectors.joining(","));
                logger.info("srcMsgIdStr:{}", srcMsgIdParamStr);
            } else {
                return new TransportCollectBean(new ArrayList<>(), 0);
            }
        }

        switch (queryMenuType) {
            case 1:
                //我的货源-发布中 接口旧状态
                selectSQL.append("select id tsId,loading_time loadingTime,type,refund_flag as refundFlag,brand,unload_time unloadTime," +
                        " start_provinc startProvinc, start_city startCity, start_area startArea, start_detail_add startDetailAdd, dest_provinc destProvinc, dest_city destCity, dest_area destArea, dest_detail_add destDetailAdd," +
                        " start_longitude startLongitude, start_latitude startLatitude, dest_longitude destLongitude, dest_latitude destLatitude," +
                        " begin_loading_time beginLoadingTime, begin_unload_time beginUnloadTime," +
                        " remark , user_id userId,start_point startPoint,dest_point destPoint,task_content taskContent,length," +
                        "wide,high,is_info_fee isInfoFee,info_status infoStatus,ctime publishTime,status goodStatus," +
                        "src_msg_id srcMsgId,weight,price,first_publish_type AS firstPublishType,publish_type AS publishType," +
                        "info_fee AS infoFee,tec_service_fee tecServiceFee,release_time AS releaseTime,shunting_quantity AS shuntingQuantity," +
                        "credit_retop AS creditRetop,guarantee_goods as guaranteeGoods,rank_level as userLevel,label_json as labelJson," +
                        "resend_counts as resendCounts,priority_recommend_expire_time priorityRecommendExpireTime,excellent_goods excellentGoods," +
                        "excellent_goods_two excellentGoodsTwo, source_type sourceType,machine_remark machineRemark,excellent_card_id excellentCardId " +
                        ", invoice_transport invoiceTransport, additional_price additionalPrice,enterprise_tax_rate enterpriseTaxRate, distance from tyt_transport  where 1=1");
                esSQL.append("select id ,loading_time,unload_time,remark,refund_flag as refundFlag,user_id ,start_point ,dest_point ,task_content ,length,wide,high,is_info_fee "
                        + ",ctime ,status ,src_msg_id ,weight,price,first_publish_type,publish_type,info_fee, distance from tyt_transport" + " where 1=1");
                countSQL.append(" tyt_transport  where 1=1 ");
                conditionSQL.append(" and ctime>=?");
                if (esOnOff == 0) {
                    params.add(todayDateStr);
                } else {
                    params.add(TransformUtil.formatGMTDate(todayDateStr));
                }

                if (srcMsgIdParamStr != null) {
                    conditionSQL.append(" and src_msg_id in (").append(srcMsgIdParamStr).append(") ");
                }

                if (queryActionType == 1) {
                    if (queryID > 0) {
                        conditionSQL.append(" and id>?");
                        params.add(queryID);
                    }
                } else if (queryActionType == 2) {
                    conditionSQL.append(" and id<?");
                    params.add(queryID);
                }
                conditionSQL.append("  and status=? and info_status=?");
                params.add(1);
                params.add(0);
                orderSQL.append(" order by id desc");
                break;
            case 2:
                // 撤销的时候queryId其实传的是第一条或者最后一条的cancelTime的long值
                selectSQL.append("select id tsId,loading_time loadingTime,type,refund_flag as refundFlag,brand,unload_time unloadTime, begin_loading_time beginLoadingTime," +
                        " start_provinc startProvinc, start_city startCity, start_area startArea, start_detail_add startDetailAdd, dest_provinc destProvinc, dest_city destCity, dest_area destArea, dest_detail_add destDetailAdd," +
                        " start_longitude startLongitude, start_latitude startLatitude, dest_longitude destLongitude, dest_latitude destLatitude," +
                        " begin_unload_time beginUnloadTime, remark,user_id userId,start_point startPoint, start_detail_add startDetailAdd, dest_point destPoint," +
                        "task_content taskContent,length,wide,high,is_info_fee isInfoFee,info_status infoStatus,ctime publishTime,mtime cancelTime,status goodStatus," +
                        "src_msg_id srcMsgId,weight,price,first_publish_type AS firstPublishType,publish_type AS publishType,info_fee AS infoFee,tec_service_fee tecServiceFee," +
                        "release_time AS releaseTime,shunting_quantity AS shuntingQuantity,credit_retop AS creditRetop,guarantee_goods as guaranteeGoods," +
                        "rank_level as userLevel,label_json as labelJson,priority_recommend_expire_time priorityRecommendExpireTime,excellent_goods excellentGoods," +
                        "excellent_goods_two excellentGoodsTwo, source_type sourceType,machine_remark machineRemark,excellent_card_id excellentCardId " +
                        ", invoice_transport invoiceTransport, additional_price additionalPrice,enterprise_tax_rate enterpriseTaxRate, distance from tyt_transport_main force index(IDX_USERID_CTIME) where 1=1");
                countSQL.append(" tyt_transport_main force index(IDX_USERID_CTIME) where 1=1 ");

                conditionSQL.append(" and ctime>=? and ctime<=? and status=?");
                params.add(startDateStr);
                params.add(tomorrowDateStr);
                params.add(5);
                if (queryActionType == 1) {
                    if (queryID > 0) {
                        conditionSQL.append(" and mtime>?");
                        params.add(TimeUtil.transferLongToDate("yyyy-MM-dd HH:mm:ss", queryID));
                    }
                } else if (queryActionType == 2) {
                    conditionSQL.append(" and mtime<?");
                    params.add(TimeUtil.transferLongToDate("yyyy-MM-dd HH:mm:ss", queryID));
                }
                orderSQL.append(" order by mtime desc");
                break;
            case 3:
                selectSQL.append("select id tsId,loading_time loadingTime,type,refund_flag as refundFlag,brand,unload_time unloadTime, begin_loading_time beginLoadingTime, " +
                        " start_provinc startProvinc, start_city startCity, start_area startArea, start_detail_add startDetailAdd, dest_provinc destProvinc, dest_city destCity, dest_area destArea, dest_detail_add destDetailAdd," +
                        " start_longitude startLongitude, start_latitude startLatitude, dest_longitude destLongitude, dest_latitude destLatitude," +
                        "begin_unload_time beginUnloadTime,remark,user_id userId,start_point startPoint, start_detail_add startDetailAdd,dest_point destPoint," +
                        "task_content taskContent,length,wide,high,is_info_fee isInfoFee,tec_service_fee tecServiceFee,info_status infoStatus,ctime publishTime,status goodStatus," +
                        "src_msg_id srcMsgId,weight,price,first_publish_type AS firstPublishType,publish_type AS publishType,info_fee AS infoFee,tec_service_fee tecServiceFee," +
                        "release_time AS releaseTime,shunting_quantity AS shuntingQuantity,credit_retop AS creditRetop,guarantee_goods as guaranteeGoods," +
                        "rank_level as userLevel,label_json as labelJson,priority_recommend_expire_time priorityRecommendExpireTime,excellent_goods excellentGoods," +
                        "excellent_goods_two excellentGoodsTwo, source_type sourceType,machine_remark machineRemark,excellent_card_id excellentCardId " +
                        ", invoice_transport invoiceTransport, additional_price additionalPrice,enterprise_tax_rate enterpriseTaxRate, distance from tyt_transport_main force index(IDX_USERID_CTIME) " + " where 1=1");
                countSQL.append(" tyt_transport_main force index(IDX_USERID_CTIME)  where 1=1 ");
                conditionSQL.append(" and ctime>? and ctime<=? and status=?");
                params.add(startDateStr);
                params.add(todayDateStr);
                params.add(1);
                if (queryActionType == 1) {
                    if (queryID > 0) {
                        conditionSQL.append(" and id>?");
                        params.add(queryID);
                    }
                } else if (queryActionType == 2) {
                    conditionSQL.append(" and id<?");
                    params.add(queryID);
                }
                orderSQL.append(" order by id desc");
                break;
            case 4:
                //我的货源 - 已成交
                selectSQL.append("select id tsId,loading_time loadingTime,type,refund_flag as refundFlag,brand,unload_time unloadTime, begin_loading_time beginLoadingTime, " +
                        " start_provinc startProvinc, start_city startCity, start_area startArea, start_detail_add startDetailAdd, dest_provinc destProvinc, dest_city destCity, dest_area destArea, dest_detail_add destDetailAdd," +
                        " start_longitude startLongitude, start_latitude startLatitude, dest_longitude destLongitude, dest_latitude destLatitude," +
                        "begin_unload_time beginUnloadTime,remark,user_id userId,start_point startPoint,dest_point destPoint,task_content taskContent,length," +
                        "wide,high,is_info_fee isInfoFee,tec_service_fee tecServiceFee,info_status infoStatus,ctime publishTime,mtime loadTime,status goodStatus,src_msg_id srcMsgId,weight," +
                        "id sortId,price,first_publish_type AS firstPublishType,publish_type AS publishType,info_fee AS infoFee,release_time AS releaseTime," +
                        "shunting_quantity AS shuntingQuantity,credit_retop AS creditRetop,guarantee_goods as guaranteeGoods,rank_level as userLevel," +
                        "label_json as labelJson,priority_recommend_expire_time priorityRecommendExpireTime,excellent_goods excellentGoods," +
                        "excellent_goods_two excellentGoodsTwo, source_type sourceType,machine_remark machineRemark,excellent_card_id excellentCardId " +
                        ", invoice_transport invoiceTransport, additional_price additionalPrice, enterprise_tax_rate enterpriseTaxRate, distance from tyt_transport_main force index(IDX_USERID_CTIME)" + " where 1=1");
                countSQL.append(" tyt_transport_main force index(IDX_USERID_CTIME)  where 1=1 ");
                conditionSQL.append(" and ctime>? and ctime<=? and status=?");
                params.add(startDateStr);
                params.add(tomorrowDateStr);
                params.add(4);
                if (queryActionType == 1) {
                    if (queryID > 0) {
                        conditionSQL.append(" and id>?");
                        params.add(queryID);
                    }
                } else if (queryActionType == 2) {
                    conditionSQL.append(" and id<?");
                    params.add(queryID);
                }
                orderSQL.append(" order by id desc");
                break;
            case 5:
                //我的货源-发布中 接口新状态

                String minePublishSql = "select id tsId,loading_time loadingTime,type,refund_flag refundFlag,brand,unload_time unloadTime, " +
                        "begin_loading_time beginLoadingTime, begin_unload_time beginUnloadTime,remark,user_id userId,start_point startPoint, " +
                        "start_detail_add startDetailAdd, dest_point destPoint,task_content taskContent,length,wide,high,is_info_fee isInfoFee," +
                        " start_longitude startLongitude, start_latitude startLatitude, dest_longitude destLongitude, dest_latitude destLatitude," +
                        "info_status infoStatus,ctime publishTime,status goodStatus,src_msg_id srcMsgId,weight,price,first_publish_type firstPublishType," +
                        "publish_type publishType,info_fee infoFee,tec_service_fee tecServiceFee,release_time releaseTime,shunting_quantity shuntingQuantity," +
                        "resend_counts resendCounts,start_provinc startProvinc,start_city startCity,start_area startArea, start_detail_add startDetailAdd," +
                        "dest_provinc destProvinc, dest_city destCity,dest_area destArea,dest_detail_add destDetailAdd, credit_retop AS creditRetop,guarantee_goods as guaranteeGoods,rank_level as userLevel," +
                        "label_json as labelJson,priority_recommend_expire_time priorityRecommendExpireTime,excellent_goods excellentGoods," +
                        "excellent_goods_two excellentGoodsTwo, source_type sourceType,machine_remark machineRemark,excellent_card_id excellentCardId " +
                        ", invoice_transport invoiceTransport, additional_price additionalPrice, enterprise_tax_rate enterpriseTaxRate,distance from tyt_transport  where 1=1";

                selectSQL.append(minePublishSql);
                esSQL.append("select id ,loading_time,unload_time,remark,user_id ,start_point , start_detail_add startDetailAdd, dest_point ,task_content ," +
                        "length,wide,high,is_info_fee " + ",ctime ,status ,src_msg_id ,weight,price,first_publish_type,publish_type,info_fee," +
                        "resend_counts,start_provinc,start_city,start_area,dest_provinc,dest_city,dest_area,credit_retop AS creditRetop," +
                        " guarantee_goods,rank_level as userLevel,label_json " +
                        ", invoice_transport invoiceTransport, additional_price additionalPrice, enterprise_tax_rate enterpriseTaxRate,distance from tyt_transport" + " where 1=1");
                countSQL.append(" tyt_transport where 1=1 ");
                conditionSQL.append(" and ctime>=?");
                if (esOnOff == 0) {
                    params.add(todayDateStr);
                } else {
                    params.add(TransformUtil.formatGMTDate(todayDateStr));
                }

                if (srcMsgIdParamStr != null) {
                    conditionSQL.append(" and src_msg_id in (").append(srcMsgIdParamStr).append(") ");
                }

                if (queryActionType == 1) {
                    if (queryID > 0) {
                        conditionSQL.append(" and id>?");
                        params.add(queryID);
                    }
                } else if (queryActionType == 2) {
                    conditionSQL.append(" and id<?");
                    params.add(queryID);
                }
                conditionSQL.append("  and status=? ");
                params.add(1);
                orderSQL.append(" order by ctime desc, id desc");
                break;
        }

        //0时走DB
        if ((esOnOff == 0 && queryMenuType == 1) || queryMenuType == 2 || queryMenuType == 3 || queryMenuType == 4
                || (esOnOff == 0 && queryMenuType == 5)) {
            // 查询结果条数
            String fullCountSqlStr = countSQL.append(conditionSQL).toString();

            BigInteger count = this.getBaseDao().query(fullCountSqlStr, params.toArray());
            if (count == null || count.longValue() <= 0) {
                return null;
            }
            //货源数量
            transportCollectBean.setGoodsCount(count.intValue());
            Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
            scalarMap.put("tsId", Hibernate.LONG);
            scalarMap.put("userId", Hibernate.LONG);
            scalarMap.put("startPoint", Hibernate.STRING);
            scalarMap.put("destPoint", Hibernate.STRING);
            scalarMap.put("taskContent", Hibernate.STRING);
            scalarMap.put("length", Hibernate.STRING);
            scalarMap.put("wide", Hibernate.STRING);
            scalarMap.put("high", Hibernate.STRING);
            scalarMap.put("isInfoFee", Hibernate.STRING);
            //信息费运单状态：0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
            scalarMap.put("infoStatus", Hibernate.STRING);
            scalarMap.put("goodStatus", Hibernate.INTEGER);
            scalarMap.put("srcMsgId", Hibernate.LONG);
            //重量 单位:吨
            scalarMap.put("weight", Hibernate.STRING);
            //装车卸车时间
            scalarMap.put("loadingTime", Hibernate.TIMESTAMP);
            scalarMap.put("unloadTime", Hibernate.TIMESTAMP);
            scalarMap.put("beginLoadingTime", Hibernate.TIMESTAMP);
            scalarMap.put("beginUnloadTime", Hibernate.TIMESTAMP);
            //备注
            scalarMap.put("remark", Hibernate.STRING);
            scalarMap.put("type", Hibernate.STRING);
            scalarMap.put("brand", Hibernate.STRING);
            scalarMap.put("refundFlag", Hibernate.INTEGER);
            //运费
            scalarMap.put("price", Hibernate.STRING);
            scalarMap.put("firstPublishType", Hibernate.INTEGER);
            scalarMap.put("publishType", Hibernate.INTEGER);
            scalarMap.put("infoFee", Hibernate.BIG_DECIMAL);
            scalarMap.put("tecServiceFee", Hibernate.BIG_DECIMAL);
            scalarMap.put("releaseTime", Hibernate.TIMESTAMP);
            scalarMap.put("shuntingQuantity", Hibernate.INTEGER);
            scalarMap.put("creditRetop", Hibernate.INTEGER);
            scalarMap.put("guaranteeGoods", Hibernate.INTEGER);
            scalarMap.put("userLevel", Hibernate.INTEGER);
            scalarMap.put("labelJson", Hibernate.STRING);
            scalarMap.put("priorityRecommendExpireTime", Hibernate.TIMESTAMP);
            scalarMap.put("excellentGoods", Hibernate.INTEGER);
            scalarMap.put("excellentGoodsTwo", Hibernate.INTEGER);
            scalarMap.put("sourceType", Hibernate.INTEGER);
            scalarMap.put("machineRemark", Hibernate.STRING);
            scalarMap.put("excellentCardId", Hibernate.LONG);

            scalarMap.put("invoiceTransport", Hibernate.INTEGER);
            scalarMap.put("additionalPrice", Hibernate.STRING);
            scalarMap.put("enterpriseTaxRate", Hibernate.BIG_DECIMAL);
            scalarMap.put("distance", Hibernate.STRING);

            scalarMap.put("startProvinc", Hibernate.STRING);
            scalarMap.put("startCity", Hibernate.STRING);
            scalarMap.put("startArea", Hibernate.STRING);
            scalarMap.put("startDetailAdd", Hibernate.STRING);
            scalarMap.put("startLongitude", Hibernate.STRING);
            scalarMap.put("startLatitude", Hibernate.STRING);
            scalarMap.put("destProvinc", Hibernate.STRING);
            scalarMap.put("destCity", Hibernate.STRING);
            scalarMap.put("destArea", Hibernate.STRING);
            scalarMap.put("destDetailAdd", Hibernate.STRING);
            scalarMap.put("destLongitude", Hibernate.STRING);
            scalarMap.put("destLatitude", Hibernate.STRING);

            if (queryMenuType == 5 || queryMenuType == 2 || queryMenuType == 3) {
                scalarMap.put("startDetailAdd", Hibernate.STRING);
            }

            if (queryMenuType == 1) {
                scalarMap.put("resendCounts", Hibernate.INTEGER);
            }

            if (queryMenuType == 5) {
                scalarMap.put("resendCounts", Hibernate.INTEGER);
            }

            if (queryMenuType == 2) {
                //发布时间
                scalarMap.put("publishTime", Hibernate.TIMESTAMP);
                //撤销时间
                scalarMap.put("cancelTime", Hibernate.TIMESTAMP);
            } else if (queryMenuType == 4) {
                //发布时间
                scalarMap.put("publishTime", Hibernate.TIMESTAMP);
                //成交时间
                scalarMap.put("loadTime", Hibernate.TIMESTAMP);
                //排序Id
                scalarMap.put("sortId", Hibernate.LONG);

            } else {
                scalarMap.put("publishTime", Hibernate.TIMESTAMP);
            }
            String fullQuerySqlTmp = selectSQL.append(conditionSQL).append(orderSQL).toString();
            goodsList = this.getBaseDao().search(fullQuerySqlTmp, scalarMap, InfoFeeMyPublishGoodsResultBean.class, params.toArray(), 1, infoFeeMyPublishQuerySize);
        } else {//1时走es
            long t3 = 0, t4 = 0;
            t3 = System.currentTimeMillis();
            Map<String, Object> esmap = ElasticTransportHelper.findMapList(esSQL.append(conditionSQL).append(orderSQL).toString(), 0, infoFeeMyPublishQuerySize, params);
            List<Transport> transportList = (List<Transport>) esmap.get("rows");

            t4 = System.currentTimeMillis();
            logger.info("es查询时间：" + (t4 - t3) + "ms");
            int size = transportList == null ? 0 : transportList.size();
            logger.info("es查询结果集条数：" + size + "条");
            //货源数量
            transportCollectBean.setGoodsCount(size);
            if (transportList != null && transportList.size() > 0) {
                goodsList = new ArrayList<InfoFeeMyPublishGoodsResultBean>();
                for (int i = 0; i < size; i++) {
                    Transport t = transportList.get(i);
                    InfoFeeMyPublishGoodsResultBean tb = new InfoFeeMyPublishGoodsResultBean();
                    // copy改简单的
                    BeanUtils.copyProperties(t, tb);
                    tb.setTsId(t.getId());
                    tb.setPublishTime(t.getCtime());
                    tb.setGoodStatus(t.getStatus());
                    goodsList.add(tb);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(goodsList) && (queryMenuType == 5 || queryMenuType == 3 || queryMenuType == 2)) {
            //查询订单数量
            Map<Long, InfoFeeMyPublishGoodsResultBean> publishGoodsMap = new HashMap<>();

            for (InfoFeeMyPublishGoodsResultBean myPubBean : goodsList) { // 获取列表货源ID集合
                publishGoodsMap.put(myPubBean.getSrcMsgId(), myPubBean);
            }

            //我的货源列表需要查询订单状态
            Set<Long> srcMsgIdSet = publishGoodsMap.keySet();

            List<TransportOrderCountVo> tsOrderCountList = transportOrdersService.getTsOrderCountList(srcMsgIdSet);
            if (CollectionUtils.isNotEmpty(tsOrderCountList)) {
                for (TransportOrderCountVo tsOrderCountVo : tsOrderCountList) {

                    Long oneSrcMsgId = tsOrderCountVo.getSrcMsgId();
                    Integer orderCount = tsOrderCountVo.getOrderCount();

                    InfoFeeMyPublishGoodsResultBean infoFeeMyPublishGoodsResultBean = publishGoodsMap.get(oneSrcMsgId);
                    if (infoFeeMyPublishGoodsResultBean != null) {
                        infoFeeMyPublishGoodsResultBean.setOrderCount(orderCount);
                    }
                }
            }

            for (InfoFeeMyPublishGoodsResultBean myPubBean : goodsList) {
                //判断转一口价按钮是否显示
                this.initPriceButtonShow(myPubBean);
            }
        }

        if (queryMenuType == 5 || queryMenuType == 1) {
            String today = TimeUtil.formatDate(TimeUtil.today());
            List<String> mapKeys = new ArrayList<>();

            for (InfoFeeMyPublishGoodsResultBean myPubBean : goodsList) { // 获取列表货源ID集合
                mapKeys.add(Constant.VIEW_COUNT_HASH_KEY + myPubBean.getSrcMsgId());
                mapKeys.add(Constant.CONTACT_COUNT_HASH_KEY + myPubBean.getSrcMsgId());
            }

            String[] fileds = mapKeys.toArray(new String[]{}); // 转换String数组
            // redis获取数据结果
            List<String> mapResult = RedisUtil.getMMapValue(Constant.PLAT_COUNT_TRANSPORT_KEY + today, fileds);
            Map<String, String> finalResult = new HashMap<>();
            for (int i = 0; i < fileds.length; i++) { // 遍历keys设置redis取出的值，目的匹配myPubBean的数字属性
                finalResult.put(fileds[i], mapResult.get(i));
            }
            PermissionResult permissionResult = userPermissionService.checkAuthPermission(Permission.货源曝光权益, userId);
            //2. 最大刷新次数
            Integer maxResendCounts = tytConfigService.getIntValue(Constant.transport_top_max_count, 16);
            for (InfoFeeMyPublishGoodsResultBean myPubBean : goodsList) {
                String viewCount = finalResult.get(Constant.VIEW_COUNT_HASH_KEY + myPubBean.getSrcMsgId());
                viewCount = StringUtils.isNotBlank(viewCount) ? viewCount : "0";
                String contactCount = finalResult.get(Constant.CONTACT_COUNT_HASH_KEY + myPubBean.getSrcMsgId());
                contactCount = StringUtils.isNotBlank(contactCount) ? contactCount : "0";
                myPubBean.setViewCount(viewCount);
                myPubBean.setContactCount(contactCount);
                //校验是否显示刷新按钮
                if (Objects.nonNull(permissionResult) && permissionResult.getUse()) {
                    ResponseCodeEnum responseCodeEnum = transportBusinessInterface.checkTransportRefresh(-1, maxResendCounts, myPubBean.getResendCounts(), myPubBean.getUserId(), new Timestamp(myPubBean.getPublishTime().getTime()));
                    if (Objects.nonNull(responseCodeEnum)) {
                        myPubBean.setShowGoodsRefresh(1);
                    } else {
                        myPubBean.setShowGoodsRefresh(0);
                    }
                }
            }
        }
        //货站接单版新增需求判断当前货源是不是接单货源
        List<Long> ids = new ArrayList<>();
        // 如果该用户前两个月没有发布过电议有价和一口价货源，提示限时体验标识
        Date publishTime = DateUtils.addMonths(new Date(), -2);
        List<String> priceList = selectOfPublishType(userId, publishTime);
        for (InfoFeeMyPublishGoodsResultBean myPublish : goodsList) {
            ids.add(myPublish.getSrcMsgId());
            //加价次数
            String redisKey = Constant.FREIGHT_ADD_MONEY_NUM + "_" + myPublish.getUserId() + "_" + myPublish.getSrcMsgId();
            String addMoneyNum = RedisUtil.get(redisKey);
            myPublish.setAddMoneyNum(StringUtils.isNotEmpty(addMoneyNum) ? addMoneyNum : "0");
            if (CollectionUtils.isEmpty(priceList)) {
                myPublish.setLimitTimeExperience(1);
            } else {
                priceList = priceList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(priceList)) {
                    myPublish.setLimitTimeExperience(0);
                } else {
                    myPublish.setLimitTimeExperience(1);
                }
            }

        }
        //货源列表信息
        transportCollectBean.setGoodsList(goodsList);
        List<Long> longs = backendTransportMapper.selectMsgIdsByMsgId(ids);
        for (Long tsId : longs) {
            for (InfoFeeMyPublishGoodsResultBean myPubBean : goodsList) {
                if (myPubBean.getSrcMsgId().equals(tsId)) {
                    myPubBean.setIsBackendTransport(1);
                }
            }
        }
        if (queryMenuType == 2) {
            List<BackoutReason> reasonList = backoutReasonMapper.selectReasonByMsgId(ids);
            for (BackoutReason backoutReason : reasonList) {
                for (InfoFeeMyPublishGoodsResultBean resultBean : goodsList) {
                    if (resultBean.getSrcMsgId().equals(backoutReason.getSrcMsgId())) {
                        resultBean.setBackoutReasonKey(backoutReason.getBackoutReasonKey());
                    }
                }
            }
        }

        //单独处理距离，库里存的12300，实际123km
        for (InfoFeeMyPublishGoodsResultBean myPubBean : goodsList) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(myPubBean.getDistance())) {
                myPubBean.setDistance(CityUtil.toDistanceStr(Integer.parseInt(myPubBean.getDistance())));
            }
            myPubBean.setStartLongitude(CityUtil.toMapPointStr(myPubBean.getStartLongitude()));
            myPubBean.setStartLatitude(CityUtil.toMapPointStr(myPubBean.getStartLatitude()));
            myPubBean.setDestLongitude(CityUtil.toMapPointStr(myPubBean.getDestLongitude()));
            myPubBean.setDestLatitude(CityUtil.toMapPointStr(myPubBean.getDestLatitude()));
        }

        // 优车2.0是否展示 转电议 按钮，条件：1.用户在AB测；2.货源为优车2.0且为一口价；3.优车2.0路线勾选有价电议
        if (Objects.equals(abtestService.getUserType("excellent2_allow_tele_negotiation", userId), 1)) {
            for (InfoFeeMyPublishGoodsResultBean goodsBean : goodsList) {
                goodsBean.setIsAllowTeleNegotiation(0);
                if (Objects.equals(goodsBean.getExcellentGoodsTwo(), 2) && Objects.equals(goodsBean.getPublishType(), 2)) {
                    try {
                        Response<Boolean> execute = thPriceClient.isHavePriceModelTwo(buildCarryBean(goodsBean)).execute();
                        if (Boolean.TRUE.equals(execute.body())) {
                            goodsBean.setIsAllowTeleNegotiation(1);
                        }
                    } catch (IOException e) {
                        logger.info("获取优车2.0可是否电仪失败 原因：", e);
                    }
                }
            }
        }

        return transportCollectBean;
    }

    private TransportCarryBean buildCarryBean(InfoFeeMyPublishGoodsResultBean publishBean) {

        TransportCarryBean transportCarryBean = new TransportCarryBean();
        transportCarryBean.setStartProvince(publishBean.getStartProvinc());
        transportCarryBean.setStartCity(publishBean.getStartCity());
        transportCarryBean.setStartArea(publishBean.getStartArea());
        transportCarryBean.setDestProvince(publishBean.getDestProvinc());
        transportCarryBean.setDestCity(publishBean.getDestCity());
        transportCarryBean.setDestArea(publishBean.getDestArea());
        transportCarryBean.setGoodsName(publishBean.getTaskContent());
        transportCarryBean.setGoodsWeight(publishBean.getWeight());
        transportCarryBean.setGoodsLength(publishBean.getLength());
        transportCarryBean.setGoodsWide(publishBean.getWide());
        transportCarryBean.setGoodsHigh(publishBean.getHigh());

        transportCarryBean.setUserId(publishBean.getUserId());

        transportCarryBean.setDistance(publishBean.getDistance() != null ? publishBean.getDistance().toString() : "0");

        transportCarryBean.setGoodTypeName(publishBean.getGoodTypeName());

        return transportCarryBean;
    }

    private Set<Long> getQuoteCallQuerySrcMsgIdSet(Long userId, Integer quotedPriceQueryType, Integer callQueryType) {
        Set<Long> srcMsgIdParamSet = new HashSet<>();

        LocalDate localDate = LocalDate.now();
        LocalDateTime startOfDay = localDate.atStartOfDay();
        Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        if (quotedPriceQueryType != null && quotedPriceQueryType == 1) {
            List<Long> haveQuotedPriceQuerySrcMsgIdList = tytTransportQuotedPriceMapper.getSrcMsgIdListByTransportUserIdAndDate(userId, startDate);
            if (CollectionUtils.isNotEmpty(haveQuotedPriceQuerySrcMsgIdList)) {
                srcMsgIdParamSet.addAll(haveQuotedPriceQuerySrcMsgIdList);
            }
        }
        if (callQueryType != null && callQueryType == 1) {
            List<Long> haveCallLogSrcMsgIdList = tytAppCallLogMapper.getSrcMsgIdListByTransportUserIdAndDate(userId, startDate);
            if (CollectionUtils.isNotEmpty(haveCallLogSrcMsgIdList)) {
                if (quotedPriceQueryType != null && quotedPriceQueryType == 1) {
                    srcMsgIdParamSet.retainAll(haveCallLogSrcMsgIdList);
                } else {
                    srcMsgIdParamSet.addAll(haveCallLogSrcMsgIdList);
                }
            } else {
                srcMsgIdParamSet = new HashSet<>();
            }
        }
        return srcMsgIdParamSet;
    }

    @Override
    public int saveChangeInfoFeeStatus(String infoFeeStatus, Long srcMsgId) throws Exception {
        // tyt_plat_transport_optimize20171123 货源优化代码
        // 老代码 String updateSQL="update tyt_transport_main set info_status=? where id=?";
        String updateSQL = "update tyt_transport_main set info_status=? where id=?";
        return this.executeUpdateSql(updateSQL, new Object[]{infoFeeStatus, srcMsgId});
    }

    @Override
    public int updateSrcMsgId(Long goodsId, Long srcMsgId) throws Exception {
        String updateSQL = "update tyt_transport_main set src_msg_id=?,mtime=? where id=?";
        return this.executeUpdateSql(updateSQL, new Object[]{srcMsgId, new Date(), goodsId});
    }

    @Override
    public TransportDetail getTransportDetail(Long tsId, Long userId) throws Exception {
        TransportMain transport = this.getTransportMainForId(tsId);
        if (transport == null) {
            return null;
        }
        // 2016-1-5针对自动重发，判断信息状态有没改变
        if (TimeUtil.formatDateTime(transport.getCtime()).compareTo(TimeUtil.formatDateTime(TimeUtil.today())) >= 0 && transport.getStatus() == 0 && transport.getUserId().longValue() == userId.longValue()) {
            int status = this.getLastStatusByHashCode(transport.getHashCode(), transport.getUserId());
            if (status != -1)
                transport.setStatus(status);
        }
        TransportDetail detailResult = new TransportDetail();
        TransportDetailBean bean = new TransportDetailBean();
        BeanUtils.copyProperties(transport, bean);
        /** 查看用户是否电话标注过该货物 */
        CallLogBean callLogBean = this.getCallLog(userId, transport.getSrcMsgId());
        if (callLogBean == null) {
            bean.setCallStatus("1_0");
        } else {
            bean.setCallStatus("2_" + callLogBean.getCallStatus());
        }
        detailResult.setDetailBean(bean);
        /** 用户一级身份获取 */
        User user = userService.getByUserId(bean.getUserId());
        if (user != null) {
            TransportUserBean userBean = new TransportUserBean();
            Integer identityType = 6;
            userBean.setUserId(user.getId());
            if (user.getIdentityType() == null) {
                // 获取默认值
                identityType = tytConfigService.getIntValue("defaultIdentityType");
                if (identityType == null)
                    identityType = 6;
            } else {
                identityType = user.getIdentityType();
            }
            userBean.setIdentityTypeCode(identityType);
            TytSource source = TytSourceUtil.getSourceName("user_identity_type", identityType + "");
            if (source != null) {
                userBean.setIdentityTypeName(source.getName());
            }
            detailResult.setTransportUserBean(userBean);
        }
        /** 查询收藏状态 */
        TransportCollect collect = collectService.isExit(userId, transport.getId());
        if (collect != null) {
            detailResult.setCollectId(collect.getId());
            detailResult.setIsCollect(collect.getStatus());
        }
        return detailResult;
    }

    @Override
    public TransportMain getBySrcMsgId(Long srcMsgId) {
        if (srcMsgId == null) {
            return null;
        }
        TransportMain transportMain = this.getById(srcMsgId);
        return transportMain;
    }

    @Override
    public int isPayedInfoFeeByUserIdAndTsId(Long userId, Long srcMsgId) throws Exception {
        String sql = "SELECT COUNT(1) FROM tyt_transport_orders t WHERE t.`ts_id` = ? AND t.`pay_user_id` = ? AND t.`pay_status` = 2";
        BigInteger c = this.getBaseDao().query(sql, new Object[]{srcMsgId, userId});
        if (c != null) {
            return c.intValue();
        }
        return 0;
    }

    @Override
    public String replaceTaskContentByPhone(String taskContent) {
        if (StringUtils.isEmpty(taskContent)) {
            return "";
        }
        String strTemp = taskContent;
        String tempStr = "";
        boolean flag = true;

        //TODO 是否有连字符-
        int phoneType = taskContent.indexOf("-");
        if (phoneType > -1) {
            // 验证座机逻辑
            //是否符合座机逻辑,按7位截取
            do {
                String temp = StringUtil.getNumbersFromStr7(strTemp);
                if (StringUtils.isNotEmpty(temp)) {
                    tempStr = StringUtil.replaceStr4StarByIndex(temp, 0, 4);
                    strTemp = strTemp.replace(temp, tempStr);
                } else {
                    flag = false;
                }
            } while (flag);
        } else {
            // 验证手机号逻辑
            // 是否存在11位连续数字
            do {
                String temp = StringUtil.getNumbersFromStr10(strTemp);
                if (StringUtils.isNotEmpty(temp)) {
                    tempStr = StringUtil.replaceStr4StarByIndex(temp, 3, 7);
                    strTemp = strTemp.replace(temp, tempStr);
                } else {
                    flag = false;
                }
            } while (flag);
        }
        return strTemp;
    }

    @Override
    public List<TransportMain> getTransportListByIds(Long userId, List<Long> ids, Integer day) {
        // 处理多个ID，不想使用逐个字段指定的方法
        StringBuilder separator = new StringBuilder();
        for (Long id : ids) {
            separator.append(id).append(",");
        }
        if (separator.length() > 0) {
            separator.deleteCharAt(separator.length() - 1);
        }
        String idsStr = "";
        if (StringUtils.isNotBlank(separator.toString())) {
            idsStr = "or id in(" + separator + ")";
        }
        String sql = "select * from tyt_transport_main where is_display = 1 and status = 4 and (user_id = ? " + idsStr + ") and ctime > ? order by ctime desc";
        Date date = null;
        try {
            date = TimeUtil.parseString(TimeUtil.formatDate(TimeUtil.addDay(-day)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        Integer pageSize = AppConfig.getIntProperty("transport.insur.import.page.size");

        final Object[] params = {
                userId,
                date
        };
        List<TransportMain> list = this.getBaseDao().search(sql, params, 0, pageSize);
        return list;
    }

    @Override
    public List<TransportMain> getTransportListByIds(List<Long> ids) {
        // 处理多个ID，不想使用逐个字段指定的方法
        StringBuilder separator = new StringBuilder();
        for (Long id : ids) {
            separator.append(id).append(",");
        }
        if (separator.length() > 0) {
            separator.deleteCharAt(separator.length() - 1);
        }
        String idsStr = "";
        if (StringUtils.isNotBlank(separator.toString())) {
            idsStr = "and id in(" + separator + ")";
        }
        String sql = "select * from tyt_transport_main where is_display = 1 " + idsStr + " order by ctime desc";
        Integer pageSize = AppConfig.getIntProperty("transport.insur.import.page.size");

        final Object[] params = {
        };
        List<TransportMain> list = this.getBaseDao().search(sql, params, 0, pageSize);
        return list;
    }


    @Override
    public WechatShareTransportBean getWechatShareDetail(Long goodsId) {
        StringBuffer selectSQL = new StringBuffer();
        selectSQL.append("SELECT t.id srcMsgId,t.task_content taskContent,t.start_point startPoint,t.dest_point destPoint,t.tel tel,t.tel3 tel3,t.tel4 tel4,t.nick_name userName,t.price price,t.pub_date pubDate,t.is_info_fee isInfoFee,t.status status,t.verify_photo_sign verifyPhotoSign,t.start_city startCity,"
                + "u.head_url headUrl,u.serve_days serveDays,u.user_class userClass,u.identity_type identityType FROM tyt_transport_main t LEFT JOIN tyt_user u  ON t.user_id=u.id WHERE  t.id =" + goodsId);
        System.out.println(selectSQL.toString());
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("srcMsgId", Hibernate.LONG);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("tel", Hibernate.STRING);
        scalarMap.put("tel3", Hibernate.STRING);
        scalarMap.put("tel4", Hibernate.STRING);
        scalarMap.put("userName", Hibernate.STRING);
        scalarMap.put("price", Hibernate.STRING);
        scalarMap.put("pubDate", Hibernate.TIMESTAMP);
        scalarMap.put("isInfoFee", Hibernate.STRING);
        scalarMap.put("headUrl", Hibernate.STRING);
        scalarMap.put("serveDays", Hibernate.INTEGER);
        scalarMap.put("identityType", Hibernate.INTEGER);
        scalarMap.put("userClass", Hibernate.INTEGER);
        scalarMap.put("verifyPhotoSign", Hibernate.INTEGER);
        scalarMap.put("status", Hibernate.INTEGER);
        scalarMap.put("startCity", Hibernate.STRING);
        List<WechatShareTransportBean> list = this.getBaseDao().search(selectSQL.toString(), scalarMap, WechatShareTransportBean.class, new Object[]{});
        if (list != null && list.size() > 0) {
            System.out.println(list.get(0));
            return list.get(0);
        }
        return null;
    }

    @Override
    public int noDisplayTransportMain(long srcMsgId) {
        String updateSQL = "update tyt_transport_main set display_type ='0',mtime=? where src_msg_id=? and display_type='1' ";
        return this.executeUpdateSql(updateSQL, new Object[]{new Date(), srcMsgId});

    }

    @Override
    public int getPublishCount(Long userId, Date date) {
        String sql = "select count(*) from tyt_transport_main  where  user_id=? and ctime>=?";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{userId, date});
        return count.intValue();
    }

    @Override
    public TransportMain getLastTransport(Long userId) {
        String sql = "select * from `tyt_transport_main` where user_id=? order by ctime desc";
        List<TransportMain> list = this.getBaseDao().search(sql, new Object[]{userId}, 0, 1);
        return list.size() == 0 ? null : list.get(0);
    }

    @Override
    public List<TransportMain> getImportTransportByUserId(Long userId, int day) throws Exception {
        String sql = "SELECT * FROM `tyt_transport_main` WHERE is_display = 1  AND user_id=? AND ((STATUS = 4 AND ctime>? AND ctime<?) OR ( STATUS IN (1,4) AND ctime>?))ORDER BY id DESC";
        Date beforDate = TimeUtil.parseString(TimeUtil.formatDate(TimeUtil.addDay(-day)));
        Date oneDayBefor = TimeUtil.parseString(TimeUtil.formatDate(new Date()));
        Integer pageSize = tytConfigService.getIntValue("goods_insur_import_page_size", 30);
        final Object[] params = {
                userId,
                beforDate,
                oneDayBefor,
                oneDayBefor
        };
        List<TransportMain> list = this.getBaseDao().search(sql, params, 0, pageSize);
        return list;
    }

    @Override
    public void delMyGoods(Long goodsId) {
        transportMainDao.deleteTransportMainById(goodsId);
        transportDao.deleteTransportBySrcMsgId(goodsId);
    }

    @Override
    public Integer getPublishCountByParam(Long userId, Date startTime, Date endTime, Integer publishType) {
        String sql = "select count(*) from tyt_transport_main  where  user_id=? and ctime>=? and " +
                " ctime<=? and publish_type=? order by id desc ";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{userId, startTime, endTime, publishType});
        return count.intValue();
    }

    @Override
    public boolean updateCreditRetop(Long srcMsgId) throws Exception {
        Date today = TimeUtil.parseString(TimeUtil.formatDate(new Date()));
        List<Transport> search = transportDao.queryForList("select * from tyt_transport WHERE src_msg_id = ? AND ctime > ? AND STATUS = 1 AND is_delete = 0;", new Object[]{srcMsgId, today});
        if (search.isEmpty()) {
            return false;
        }
        Integer creditRetop = search.get(0).getCreditRetop();
        if (creditRetop == null || creditRetop != 1) {
            return false;
        }
        String updateSql = "UPDATE tyt_transport SET credit_retop = 2 WHERE src_msg_id = ? AND ctime > ? AND STATUS = 1 AND is_delete = 0;";
        String updateMainSql = "UPDATE tyt_transport_main SET credit_retop = 2 WHERE src_msg_id = ?;";
        transportDao.executeUpdateSql(updateSql, new Object[]{srcMsgId, today});
        transportMainDao.executeUpdateSql(updateMainSql, new Object[]{srcMsgId});

        // 更新最新的一条transport，使刷新定时任务能查询到对应数据
        transportDao.executeUpdateSql(
                "UPDATE tyt_transport SET ctime = NOW() WHERE src_msg_id = ? ORDER BY ctime DESC LIMIT 1",
                new Object[]{srcMsgId});
        return true;
    }


    @Override
    public List<String> selectOfPublishType(Long detailUserId, Date publishTime) {
        String sql = "select price from tyt_transport_main where  user_id=? and ctime>=? and is_delete = 0 and price>0 limit 1";
        Object[] params = {
                detailUserId,
                publishTime
        };
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("price", Hibernate.STRING);

        return this.getBaseDao().search(sql, scalarMap, null, params);

    }

    @Override
    public TransportLabelJson checkPersonalDuplicate(Long oldSrdMsgId, TransportLabelJson oldLabelJson , Transport transport, boolean refreshDuplicate, String assignCarTel) {
        TransportLabelJson labelJson = transportBusiness.getTransportLabelJson(transport.getLabelJson());
        //重货标识，原来是重货的，一定是重货
        if (oldLabelJson != null && oldLabelJson.isDuplicate()) {
            labelJson.setDuplicateFlag(1);
            transport.setSortType(TsSortTypeEnum.bottom.getCode());
        } else {
            if (refreshDuplicate) {
                //如果是曝光卡，则不重新计算
                boolean similarityResult = transportBusiness.checkPersonalSimilarity(transport, oldSrdMsgId, assignCarTel);

                if (similarityResult) {
                    labelJson.setDuplicateFlag(1);
                    transport.setSortType(TsSortTypeEnum.bottom.getCode());
                }
            }
        }
        transport.setLabelJson(labelJson.getJsonText());
        return labelJson;
    }


    @Override
    public int selectCountOfExcellentGoods(Long userId, Date startTime, Date endTime) {
        List<Object> paramList = new ArrayList<>();
        paramList.add(userId);
        paramList.add(startTime);

        String sql = "select count(1) from tyt_transport_main where user_id=? and ctime>=? ";
        if (endTime != null) {
            sql = sql + " and ctime <=? ";
            paramList.add(endTime);
        }
        sql = sql + " and excellent_goods=1 and is_delete = 0  limit 1";

        Object[] params = paramList.toArray();
        BigInteger count = this.getBaseDao().query(sql, params);
        return count.intValue();
    }

    @Override
    public boolean updateTecServiceBySrcMsgId(Long srcMsgId, BigDecimal tecServiceFee) throws Exception {
        // 更新tyt_transport_main表技术服务费信息
        transportMainDao.executeUpdateSql(
                "UPDATE tyt_transport_main SET tec_service_fee=?,mtime = NOW() WHERE src_msg_id = ?",
                new Object[]{tecServiceFee, srcMsgId});
        // 更新tyt_transport表技术服务费信息
        transportDao.executeUpdateSql(
                "UPDATE tyt_transport SET tec_service_fee=?,mtime = NOW() WHERE src_msg_id = ? and status = 1",
                new Object[]{tecServiceFee, srcMsgId});
        return true;
    }

    @Override
    public boolean updateLabelJsonBySrcMsgId(Long srcMsgId, String labelJson) {
        // 更新tyt_transport_main表技术服务费信息
        transportMainDao.executeUpdateSql(
                "UPDATE tyt_transport_main SET label_json=?,mtime = NOW() WHERE src_msg_id = ?",
                new Object[]{labelJson, srcMsgId});
        // 更新tyt_transport表技术服务费信息
        transportDao.executeUpdateSql(
                "UPDATE tyt_transport SET label_json=?,mtime = NOW() WHERE src_msg_id = ? and status = 1",
                new Object[]{labelJson, srcMsgId});
        return true;
    }

    @Override
    public Integer getCountTransportMainIsValidForIdList(List<Long> haveAnyQuotedPriceAgainTransportMainIdList, Date todayBeginTime, Date todayEndTime) {
        return tytTransportQuotedPriceMapper.getCountTransportMainIsValidForIdList(haveAnyQuotedPriceAgainTransportMainIdList, todayBeginTime, todayEndTime);
    }

    @Override
    public List<Long> getTransportMainIsValidSrcMsgIdsForIdList(List<Long> haveAnyQuotedPriceAgainTransportMainIdList, Date todayBeginTime, Date todayEndTime) {
        return tytTransportQuotedPriceMapper.getTransportMainIsValidSrcMsgIdsForIdList(haveAnyQuotedPriceAgainTransportMainIdList, todayBeginTime, todayEndTime);
    }

    /**
     * 修改main表扩展表
     *
     * @param mainExtend
     */
    @Override
    public void updateMainExtend(TytTransportMainExtend mainExtend) {
        Example exa = new Example(TytTransportMainExtend.class);
        exa.and().andEqualTo("srcMsgId", mainExtend.getSrcMsgId());
        tytTransportMainExtendMapper.updateByExampleSelective(mainExtend, exa);
        // tytTransportMainExtendMapper.updateBySrcMsgId(mainExtend);
    }

    /**
     * 添加main表扩展表
     *
     * @param mainExtend
     */
    @Override
    public void addMainExtend(TytTransportMainExtend mainExtend) {
        tytTransportMainExtendMapper.insertSelective(mainExtend);
    }

    /**
     * 获取货源扩展信息
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public TytTransportMainExtend getMainExtend(Long srcMsgId) {
        return tytTransportMainExtendMapper.getBySrcMsgId(srcMsgId);
    }

    @Override
    public Integer getTransportBusinessType(Long srcMsgId) {
        if (srcMsgId == null) {
            return TransportBusinessTypeEnum.invalid.getCode();
        }
        TransportMain transportMainForId = getTransportMainForId(srcMsgId);
        if (transportMainForId == null) {
            return TransportBusinessTypeEnum.invalid.getCode();
        }
        TransportLabelJson transportLabelJson = JSONObject.parseObject(transportMainForId.getLabelJson(), TransportLabelJson.class);
        if (transportLabelJson != null && transportLabelJson.getGoodCarPriceTransport() != null && transportLabelJson.getGoodCarPriceTransport() == 1) {
            String automaticGoodCarPriceTransportRedisKey = "automaticGoodCarPriceTransport" + ":" + transportMainForId.getSrcMsgId();
            String automaticGoodCarPriceTransportFlag = RedisUtil.get(automaticGoodCarPriceTransportRedisKey);
            if (StringUtils.isNotBlank(automaticGoodCarPriceTransportFlag) && automaticGoodCarPriceTransportFlag.equals("1")) {
                return TransportBusinessTypeEnum.automaticGoodCarPriceTransport.getCode();
            }
        }
        return TransportBusinessTypeEnum.invalid.getCode();
    }

    @Override
    public Integer getPublishCountByUserId(Long userId, Date startTime, Date endTime) {
        String sql = "select count(*) from tyt_transport_main  where  user_id=? and ctime>=? and " +
                " ctime<=?";
        BigInteger count = this.getBaseDao().query(sql, new Object[]{userId, startTime, endTime});
        return count.intValue();
    }

    /**
     * 是否是一手差货主  体验差的一手货主：个人货主/企业货主 + 30天内最近一次发货当日电议无拨打或一口价未成交
     * @param data
     * @return true 是 false 否
     */
    @Override
    public boolean checkPoorExperience(DwsNewIdentityTwoData data){
        if (Objects.isNull(data) || !DwsIdentityTypeEnum.isCargoOwner(data.getType())){
            return false;
        }
        Date lastPublishTime = tytTransportMainMapper.hasUserTransportLast30Day(data.getUserId());
        if (lastPublishTime == null) {
            return false;
        }
        Long userId = data.getUserId();
        // 查询最后一次发货当天的所有货源
        String lastPublishDay = DateFormatUtils.ISO_DATE_FORMAT.format(lastPublishTime);
        List<TytTransportVO> lastDayTransports = tytTransportMainMapper.getUserSomeDayTransportData(userId, lastPublishDay);

        Map<Integer, List<TytTransportVO>> map = lastDayTransports.stream()
                .collect(Collectors.groupingBy(TytTransportVO::getPublishType));
        // 一口价货源
        List<TytTransportVO> fixedTransports = map.get(TransportPublishTypeEnum.FIXED.getCode());
        if (CollectionUtils.isNotEmpty(fixedTransports)) {
            if (fixedTransports.stream().anyMatch(t -> TransportStatusEnum.DEAL.getCode().equals(t.getStatus()))) {
                return false;
            }
        }
        // 电议货源
        List<TytTransportVO> telTransports = map.get(TransportPublishTypeEnum.TEL.getCode());
        if (CollectionUtils.isNotEmpty(telTransports)) {
            List<Long> srcMsgIds = telTransports.stream().map(TytTransportVO::getSrcMsgId).collect(Collectors.toList());
            Integer count = tytTransportMainMapper.hasContactInSrcMsgIds(srcMsgIds);
            return count == null;
        }
        return true;
    }

    @Override
    public TransportMain getFirstTransport(Long userId) {
        return tytTransportMainMapper.getFirstTransport(userId);
    }

    @Override
    public List<Long> getInReleaseTransportIdList(Long transportUserId) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        Date todayStartDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        return tytTransportMainMapper.getInReleaseTransportIdList(transportUserId, todayStartDate);
    }

    @Override
    public boolean isHaveSameTransportInPublish(Long srcMsgId) {
        TransportMain transportMain = getBySrcMsgId(srcMsgId);
        if (transportMain != null && StringUtils.isNotBlank(transportMain.getSimilarityCode().trim())) {
            String today = DateFormatUtils.ISO_DATE_FORMAT.format(new Date());
            Integer count = tytTransportMainMapper.getSameTransportInPublishCount(srcMsgId, transportMain.getSimilarityCode(), today);
            return count != null && count > 0;
        }
        return false;
    }

    @Override
    public Integer countByGoodTypeName(List<Long> srcMsgIds, String goodTypeName) {
        return tytTransportMainMapper.countByGoodTypeName(srcMsgIds, goodTypeName);
    }

    @Override
    public List<Long> getUserPublishingSrcMsgIds(Long userId) {
        return tytTransportMainMapper.getUserPublishingSrcMsgIds(userId);
    }

    @Override
    public boolean isHaveSameStartCityAndDestCityTransportInPublish(Long srcMsgId) {
        TransportMain transportMain = getBySrcMsgId(srcMsgId);
        if (transportMain != null && StringUtils.isNotBlank(transportMain.getStartCity()) && StringUtils.isNotBlank(transportMain.getDestCity())) {
            String today = DateFormatUtils.ISO_DATE_FORMAT.format(new Date());
            Integer count = tytTransportMainMapper.getSameStartCityAndDestCityTransportInPublish(srcMsgId, transportMain.getStartCity(), transportMain.getDestCity(), today);
            return count != null && count > 3;
        }
        return false;
    }

    /**
     * 校验是否是参与现金奖活动货源
     * a. 有抽佣标签的货源且技术服务费>0
     * b. 经分口径下的直客或物流公司的发布的前3个货源
     * c. 经分口径下的直客的货源，距离首发时间超过2小时还未成交的货源
     *
     * @param transport
     */
    @Override
    public boolean isCashPrizeActivityTransport(TransportMain transport) {

        boolean isCashPrizeActivityGoods = false;
        try {
            logger.info("校验是否是参与现金奖活动货源，货源id:{}", transport.getId());
            TransportLabelJson transportLabelJson = JSON.parseObject(transport.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson == null) {
                transportLabelJson = new TransportLabelJson();
            }
            // 抽佣货源&服务费>0
            if (Objects.equals(transportLabelJson.getCommissionTransport(), 1)
                    && transport.getTecServiceFee() != null && transport.getTecServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                logger.info("校验是否是参与现金奖活动货源，货源id:{}，抽佣且技术服务费>0", transport.getId());
                isCashPrizeActivityGoods = true;
            } else {
                DwsNewIdentityTwoData userIdentity = apiDataUserCreditInfoService.getIdentityByUserId(transport.getUserId());
                if (userIdentity != null) {
                    // 直客2小时内未成交
                    if (DwsIdentityTypeEnum.isCargoOwner(userIdentity.getType())) {
                        if (DateUtils.addHours(transport.getReleaseTime(), 2).before(new Date())) {
                            logger.info("校验是否是参与现金奖活动货源，货源id:{}，直客2小时内未成交", transport.getId());
                            isCashPrizeActivityGoods = true;
                        }else{
                            // 未首履过（没有剔除风险单）的直客（货主）发布的货源
                            User user = userService.getByUserId(transport.getUserId());
                            int count = tytCustomFirstOrderRecordMapper.countFinishOrder(user.getCellPhone());
                            if (count <= 0) {
                                isCashPrizeActivityGoods = true;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("校验是否是参与现金奖活动货源异常，货源id:{}", transport.getId(), e);
        }

        return isCashPrizeActivityGoods;
    }

    private List<Long> getTopNSrcMsgId(String date, Long userId, int topN) {
        String sql = "select id from tyt_transport where ctime between ? and concat(?,' 23:39:59') and user_id = ? order by id limit ?";
        Object[] params = {date, date, userId, topN};
        Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("id", Hibernate.LONG);
        List<Transport> tsList = this.getBaseDao().search(sql, scalarMap, Transport.class, params);
        return tsList.stream().map(Transport::getId).collect(Collectors.toList());
    }
}
