package com.tyt.transport.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.model.EcaContract;
import com.tyt.model.TransportMtChangeLog;
import com.tyt.transport.service.EcaContractService;
import com.tyt.transport.service.TransportMtChangeLogService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service("transportMtChangeLogService")
public class TransportMtChangeLogServiceImpl extends BaseServiceImpl<TransportMtChangeLog, Long> implements TransportMtChangeLogService {

	public static Logger logger = LoggerFactory.getLogger(TransportMtChangeLogServiceImpl.class);


	@Resource(name = "transportMtChangeLogDao")
	public void setBaseDao(BaseDao<TransportMtChangeLog, Long> transportMtChangeLogDao) {
		super.setBaseDao(transportMtChangeLogDao);
	}

}
