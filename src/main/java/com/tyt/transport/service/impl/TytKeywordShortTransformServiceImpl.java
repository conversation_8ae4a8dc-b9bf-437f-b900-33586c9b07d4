package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytKeywordShortTransform;
import com.tyt.model.TytMachineTypeBrandParameter;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.SearchWordService;
import com.tyt.transport.service.TytKeywordShortTransformService;
import com.tyt.transport.service.TytMachineTypeBrandParameterService;
import com.tyt.util.GoodsUnique;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.tyt.transport.service.impl.TransportMtServiceImpl.logger;
@Slf4j
@Service("tytKeywordShortTransformService")
public class TytKeywordShortTransformServiceImpl extends BaseServiceImpl< TytKeywordShortTransform, Integer> implements TytKeywordShortTransformService {

    @Override
    @Resource(name = "tytKeywordShortTransformDao")
    public void setBaseDao(BaseDao<TytKeywordShortTransform, Integer> tytKeywordShortTransformDao) {
        super.setBaseDao(tytKeywordShortTransformDao);
    }

    @Autowired
    private TytMachineTypeBrandParameterService tytMachineTypeBrandParameterService;

    public Map< String, String > cacheMap = new HashMap< String, String >();

    @Resource(name = "searchWordService")
    private SearchWordService searchWordService;

    @Override
    public List< TytKeywordShortTransform > getKeywordShortList(String hql,final Object[] params) {
        List<TytKeywordShortTransform> list = this.getBaseDao().queryForList(hql,params);
        return list;
    }

    /**
     * @param keyword
     * @return
     */
    @Override
    public String queryKeywordShortByKeyword(String keyword) {
        //入参去空格
        String keywordFormat = keyword.replaceAll(" ", "");
        StringBuilder standardNameBuilder = new StringBuilder();
        List< TytKeywordShortTransform > listArray = getStandardNameBuffer(keyword);
        if(listArray.size() !=0){
            for (int i = 0; i < listArray.size(); i++) {
                TytKeywordShortTransform ts = listArray.get(i);
                if(ts.getKeywordShortName().equals(keyword)){
                    return ts.getStandardName();
                }
            }
        }
        //通过分词拿到俗称去匹配标准词
        List< String > listkeys = GoodsUnique.getListkeys(keywordFormat);
        //分词没有值直接返回空
        if (CollectionUtils.isEmpty(listkeys)) {
            return null;
        }
        for (String str : listkeys) {
            String value = cacheMap.get(str);
            if(value == null){
                List< TytKeywordShortTransform > keywordShortList = getStandardNameBuffer(str);
                if(keywordShortList.isEmpty()){
                    standardNameBuilder.append(str);
                }else{
                    standardNameBuilder.append(keywordShortList.get(0).getStandardName());
                    cacheMap.put(str,keywordShortList.get(0).getStandardName());
                }
            }else{
                standardNameBuilder.append(value);
            }
        }
        logger.info("query standardName, the standardName is [ " + standardNameBuilder.toString() + " ]");
        return standardNameBuilder.toString();
    }


    /**
     * 查询俗称表数据
     * @param param
     * @return
     */
    private List< TytKeywordShortTransform > getStandardNameBuffer(String param) {
        //查询表里数据
        String hql = "select * from  `tyt_keyword_short_transform` where keyword_short_name =  ? ";
        final Object[] params = { param };
        return this.getKeywordShortList(hql, params);
    }

    /**
     * 根据关键字查询简称
     * @param keyword
     * @param userId
     * @return
     */
    @Override
    public List<TytKeywordShortTransform> queryKeywordKeyword(String keyword, String userId){
        //入参去空格
        String keywordFormat = keyword.replaceAll(" ", "");
        //根据关键词查询缓存是否有数据
        log.info("queryMachineTypeGroupByKeyword_md5 -> keyword: {}", keywordFormat);
        String matchineTypeCacheKey = "machine:type:form:";
        String machineTypeCacheResult = RedisUtil.get(matchineTypeCacheKey + keywordFormat);
        List<TytKeywordShortTransform> machineTypeResult = null;
        //缓存又数据直接解析
        if (machineTypeCacheResult != null) {
            machineTypeResult = JSON.parseArray(machineTypeCacheResult, TytKeywordShortTransform.class);
        } else {
            //没数据查询并加入缓存
            machineTypeResult = getStandardName(keywordFormat);
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(machineTypeResult)) {
                RedisUtil.set(matchineTypeCacheKey + keywordFormat, JSON.toJSONString(machineTypeResult), 6000);
                //addInfo(keywordFormat, userId);
            }
        }
        return machineTypeResult;
    }


    @Override
    public List<TytMachineTypeBrandParameter> searchMatchesGroupbarnd(String keyword, String userId){

        //根据关键词查询缓存是否有数据

        String matchineTypeCacheKey = "machine:type:brand:";
        String machineTypeCacheResult = RedisUtil.get(matchineTypeCacheKey + keyword);
        List<TytMachineTypeBrandParameter> machineTypeResult = null;
        //缓存又数据直接解析
        if (machineTypeCacheResult != null) {
            machineTypeResult = JSON.parseArray(machineTypeCacheResult, TytMachineTypeBrandParameter.class);
        } else {
            //没数据查询并加入缓存
            machineTypeResult = getBrandName(keyword);
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(machineTypeResult)) {
                RedisUtil.set(matchineTypeCacheKey + keyword, JSON.toJSONString(machineTypeResult), 6000);
            }
        }
        return machineTypeResult;
    }
    @Override
    public List<TytMachineTypeBrandParameter> searchMatchesGroupType(String keyword, String secondClass,String userId){
           return getTopType(keyword,secondClass);
    }

    /**
     * 根据货物名称查询参数
     * @param keyword
     * @param userId
     * @return
     */
    public List<TytMachineTypeBrandParameter> searchMatchesGroupshowName(String keyword, String userId){
        String sql = "SELECT * FROM `tyt_machine_type_brand_parameter` where show_name like ?  LIMIT 50";
        final Object[] params = {
                "%" + keyword + "%"
        };
        List<TytMachineTypeBrandParameter> machineTypeResult = null;
        List<TytMachineTypeBrandParameter> machineTypeDbResult = tytMachineTypeBrandParameterService.getKeywordShortList(sql,params);
        if (machineTypeDbResult.size() > 0) {
            machineTypeResult = new ArrayList<>(); // 避免set值hib将数据库进行更新
            for(TytMachineTypeBrandParameter dbMachine: machineTypeDbResult) {
                TytMachineTypeBrandParameter machine = new TytMachineTypeBrandParameter();
                BeanUtils.copyProperties(dbMachine, machine);
                /**
                 * 拼接规则：
                 *   数据全：品牌+型号全称+空格+二级分类+两个空格+吨位+两个空格+尺寸信息
                 *   尺寸信息：如果只有两个值的拼写方式 长1米*宽2米
                 */
                StringBuilder showName = new StringBuilder();
                showName.append(StringUtils.defaultIfBlank(machine.getBrand(), ""));
                showName.append(StringUtils.defaultIfBlank(machine.getTopType(), ""));
                if(StringUtils.isNotBlank(machine.getBrand()) || StringUtils.isNotBlank(machine.getTopType())) {
                    showName.append(" "); // 一个空格
                }
                showName.append(StringUtils.defaultIfBlank(machine.getSecondClass(), ""));
                if(StringUtils.isNotBlank(machine.getSecondClass())) {
                    showName.append("  "); // 两个空格
                }
                showName.append(getShowNameArg(machine));
                machine.setShowName(showName.toString());
//                machine.setShowNameNew(dbMachine.getShowName());
//                machine.setShowNameArg(getShowNameArg(machine));
                machineTypeResult.add(machine);
            }
        }
        return machineTypeResult;
    }


    private List< TytKeywordShortTransform > getStandardName(String param) {
        //查询表里数据
        String hql = "select * from  `tyt_keyword_short_transform` where keyword_short_name =  ? ";
        final Object[] params = {
                param
        };
        return this.getKeywordShortList(hql, params);
    }

    private List<TytMachineTypeBrandParameter > getBrandName(String param) {
        //查询表里数据
        String hql = "select * from  `tyt_machine_type_brand_parameter` where second_class =  ?  GROUP BY brand ";
        final Object[] params = {
                param
        };
        return tytMachineTypeBrandParameterService.getKeywordShortList(hql,params);
    }

    private List<TytMachineTypeBrandParameter > getTopType(String param,String secondClass) {
        //查询表里数据
        String hql = "select * from  `tyt_machine_type_brand_parameter` where second_class =  ? and brand = ? GROUP BY top_type";
        final Object[] params = {
                param,secondClass
        };
        return tytMachineTypeBrandParameterService.getKeywordShortList(hql,params);
    }

    public String getShowNameArg(TytMachineTypeBrandParameter machine){
        StringBuilder showName = new StringBuilder();
        if(machine.getWeight() != null) {
            showName.append(machine.getWeight().stripTrailingZeros().toPlainString()).append("吨").append(" ");
        }
        // 处理长宽高不同的匹配样式
        if(machine.getLength() != null && machine.getWidth() != null && machine.getHeight() != null) {
            if(machine.getLength() != null) {
                showName.append(machine.getLength().stripTrailingZeros().toPlainString()).append("*");
            }
            if(machine.getWidth() != null) {
                showName.append(machine.getWidth().stripTrailingZeros().toPlainString()).append("*");
            }
            if(machine.getHeight() != null) {
                showName.append(machine.getHeight().stripTrailingZeros().toPlainString()).append("米");
            }
        } else if(machine.getLength() == null || machine.getWidth() == null || machine.getHeight() == null) {
            if(machine.getLength() != null) {
                showName.append("长").append(machine.getLength().stripTrailingZeros().toPlainString()).append("米");
                if(machine.getWidth() != null || machine.getHeight() != null) {
                    showName.append("*");
                }
            }
            if(machine.getWidth() != null) {
                showName.append("宽").append(machine.getWidth().stripTrailingZeros().toPlainString()).append("米");
                if(machine.getHeight() != null) {
                    showName.append("*");
                }
            }
            if(machine.getHeight() != null) {
                showName.append("高").append(machine.getHeight().stripTrailingZeros().toPlainString()).append("米");
            }
        }
        // 处理尾部空字符串
        if(showName.length() != 0) {
            for(int i = 0; i<showName.length();i++) {
                if(showName.charAt(showName.length()-1) != ' ') {
                    break;
                }
                showName.deleteCharAt(showName.length()-1);
            }
        }

        return showName.toString();
    }
}
