package com.tyt.transport.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.gexin.fastjson.JSON;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.common.bean.PageData;
import com.tyt.infofee.bean.GoodsSingleDetailResultBean;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.TransportMain;
import com.tyt.plat.biz.covergoodsdial.pojo.CheckAutoDecreasePopConfigVO;
import com.tyt.plat.biz.covergoodsdial.pojo.CountdownReportReq;
import com.tyt.plat.biz.covergoodsdial.pojo.CountdownReportResp;
import com.tyt.plat.biz.covergoodsdial.pojo.CoverGoodsDialInfoVO;
import com.tyt.plat.biz.covergoodsdial.pojo.CoverGoodsDialUserInfoVO;
import com.tyt.plat.biz.covergoodsdial.pojo.CoverGoodsUserBeansVO;
import com.tyt.plat.biz.covergoodsdial.pojo.UseNReq;
import com.tyt.plat.biz.covergoodsdial.pojo.UseNVO;
import com.tyt.plat.commons.model.PageParameter;
import com.tyt.plat.commons.tools.CustomPageHelper;
import com.tyt.plat.entity.base.TytCoverGoodsBeansConfigUser;
import com.tyt.plat.entity.base.TytCoverGoodsDialConfig;
import com.tyt.plat.entity.base.TytCoverGoodsDialConfigUser;
import com.tyt.plat.entity.base.TytCoverGoodsDialUserUseLog;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.vo.ts.TransportBIDataJson;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.plat.entity.base.CoverGoodsLogDO;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportViewLogService;
import com.tyt.transport.service.TytCoverGoodsDialConfigService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.PreConditions;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/01/15 13:39
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TytCoverGoodsDialConfigServiceImpl implements TytCoverGoodsDialConfigService {

    private static final String COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY = "CoverGoodsDialConfigNTimes";
    private static final String CHECK_AUTO_DECREASE_POP_CONFIG_KEY = "CoverGoodsDialAutoDecreasePopConfig:{%d}";

    private static final String DIAL_INFO_REDIS_KEY = "dial:info:userId:{%d}:tsId:{%d}";

    /**
     * 车主等级配置
     */
    private static final String USER_CREDIT_COVER_GOODS_DIAL_TIMES_CONFIG_KEY = "CoverGoodsDialTimes:UserCredit";
    /**
     * 履约单单奖配置
     */
    private static final String ORDER_COVER_GOODS_DIAL_TIMES_CONFIG_KEY = "CoverGoodsDialTimes:Order";
    /**
     * 认证车辆配置
     */
    private static final String CAR_COVER_GOODS_DIAL_TIMES_CONFIG_KEY = "CoverGoodsDialTimes:Car";
    /**
     * 认证司机配置
     */
    private static final String DRIVER_COVER_GOODS_DIAL_TIMES_CONFIG_KEY = "CoverGoodsDialTimes:Driver";
    /**
     * 认证企业配置
     */
    private static final String ENTERPRISE_COVER_GOODS_DIAL_TIMES_CONFIG_KEY = "CoverGoodsDialTimes:Enterprise";
    /**
     * 认证车主配置
     */
    private static final String OWNER_COVER_GOODS_DIAL_TIMES_CONFIG_KEY = "CoverGoodsDialTimes:Owner";

    private final TytConfigService configService;
    private final TytCoverGoodsDialConfigMapper coverGoodsDialConfigMapper;
    private final TytCoverGoodsDialConfigUserMapper coverGoodsDialConfigUserMapper;
    private final TransportMainService transportMainService;
    private final TytCoverGoodsDialUserUseLogMapper coverGoodsDialUserUseLogMapper;
    private final TytCoverGoodsBeansConfigUserMapper coverGoodsBeansConfigUserMapper;
    private final TytCoverGoodsWhiteListConfigUserMapper coverGoodsWhiteListConfigUserMapper;
    private final CoverGoodsLogMapper coverGoodsLogMapper;
    private final TransportViewLogService transportViewLogService;
    private final ApiDataUserCreditInfoService apiDataUserCreditInfoService;
    private final TytConfigService tytConfigService;


    /**
     * 抢单豆状态，1-有效，2-无效-用完，3-无效-到期
     */
    private static final Integer BEANS_STATUS_EFFECTIVE = 1;
    private static final Integer BEANS_STATUS_USED = 2;
    private static final Integer BEANS_STATUS_expire = 3;


    @Override
    public Integer selectXTime() {
        Integer maxXTime = coverGoodsDialConfigMapper.selectMaxXTime();
        return maxXTime == null ? 0 : maxXTime;
    }

    @Override
    public CoverGoodsDialInfoVO getCoverGoodsDialInfo(Long userId) {
        CoverGoodsDialInfoVO coverGoodsDialInfoVO = new CoverGoodsDialInfoVO();
        TytCoverGoodsDialConfigUser configUser = coverGoodsDialConfigUserMapper.selectEnabledLastImported(userId);
        if (configUser != null) {
            TytCoverGoodsDialConfig tytCoverGoodsDialConfig =
                    coverGoodsDialConfigMapper.selectByPrimaryKey(configUser.getDialConfigId());
            coverGoodsDialInfoVO.setDialUser(true);
            coverGoodsDialInfoVO.setConfigXSeconds(tytCoverGoodsDialConfig.getXTimeInSeconds());
            coverGoodsDialInfoVO.setConfigYSeconds(tytCoverGoodsDialConfig.getYTimeInSeconds());
        } else {
            coverGoodsDialInfoVO.setDialUser(false);
            Integer configMaxXSeconds = selectXTime();
            coverGoodsDialInfoVO.setConfigMaxXSeconds(configMaxXSeconds);
            coverGoodsDialInfoVO.setConfigXSeconds(configMaxXSeconds);
            // 如果不在配置名单内，就不倒计时
            coverGoodsDialInfoVO.setConfigYSeconds(0);
        }
        coverGoodsDialInfoVO.setLeftN(getDialConfigLeftN(userId));
        //查看用户是否在捂货白名单内
        Long whiteUser = coverGoodsWhiteListConfigUserMapper.countByUserId(userId);
        if (null != whiteUser && whiteUser.intValue() > 0) {
            coverGoodsDialInfoVO.setDialWhiteUser(true);
        } else {
            coverGoodsDialInfoVO.setDialWhiteUser(false);
        }

        coverGoodsDialInfoVO.setGainTimes(coverGoodsDialUserUseLogMapper.countByUserIdAndChangeType(userId, 1));
        return coverGoodsDialInfoVO;
    }

    @Override
    public CoverGoodsDialUserInfoVO getCoverGoodsDialUserInfo(Long userId) {
        CoverGoodsDialUserInfoVO coverGoodsDialUserInfoVO = new CoverGoodsDialUserInfoVO();
        List<TytCoverGoodsBeansConfigUser> userCoverGoodsBeansLog =
                coverGoodsBeansConfigUserMapper.selectCoverGoodsDialGainLogByUserId(userId);
        if (userCoverGoodsBeansLog != null && userCoverGoodsBeansLog.size() > 0) {
            coverGoodsDialUserInfoVO.setCoverGoodsDialGainInfoVOLogs(userCoverGoodsBeansLog);
            List<TytCoverGoodsDialUserUseLog> userUseLogs =
                    coverGoodsDialUserUseLogMapper.selectUserUseLogByUserId(userId);
            coverGoodsDialUserInfoVO.setCoverGoodsDialUserUseLogs(userUseLogs);
            coverGoodsDialUserInfoVO.setLeftN(getDialConfigLeftN(userId));
        }
        return coverGoodsDialUserInfoVO;
    }

    @Override
    public CountdownReportResp coverGoodsCountdownReport(CountdownReportReq req) {
        log.info("上报coverGoods倒计时, req: {}", req);

        Long userId = req.getUserId();
        Long tsId = req.getTsId();
        PreConditions.check(userId != null && tsId != null, ReturnCodeConstant.ERROR, "货源不存在");
        CountdownReportResp countdownReportResp = new CountdownReportResp();

        TransportMain transportMain = transportMainService.getBySrcMsgId(tsId);
        // 已成交货源直接返回
        if (transportMain == null || !Objects.equals(transportMain.getStatus(), 1)) {
            countdownReportResp.setCountDownFinished(true);
            countdownReportResp.setConfigYSeconds(0);
            countdownReportResp.setLeftYSeconds(0);
            return countdownReportResp;
        }

        UserTsDialInfoRedisBean userTsDialInfoBean = getUserTsDialInfoRedisBean(userId, tsId);
        // 已经结束，直接返回
        if (Boolean.TRUE.equals(userTsDialInfoBean.countDownFinished)) {
            countdownReportResp.setCountDownFinished(true);
            countdownReportResp.setConfigYSeconds(userTsDialInfoBean.getConfigYSeconds());
            countdownReportResp.setLeftYSeconds(0);
            return countdownReportResp;
        }

        Boolean countDownFinished = req.getCountDownFinished();
        Integer reduceTimeInSeconds = req.getReduceTimeInSeconds();
        if (Boolean.TRUE.equals(countDownFinished)) {
            // 直接结束
            userTsDialInfoBean.setCountDownFinished(true);
            userTsDialInfoBean.setLeftYSeconds(0);
        } else if (reduceTimeInSeconds != null) {
            if (reduceTimeInSeconds >= userTsDialInfoBean.getLeftYSeconds()) {
                // 上报倒计时，并且扣完以后剩余倒计时归0
                userTsDialInfoBean.setCountDownFinished(true);
                userTsDialInfoBean.setLeftYSeconds(0);
            } else {
                // 扣完以后还有剩余倒计时
                userTsDialInfoBean.setLeftYSeconds(userTsDialInfoBean.getLeftYSeconds() - reduceTimeInSeconds);
            }
        }

        // save
        RedisUtil.setObject(getUserTsDialInfoRedisKey(userId, tsId), userTsDialInfoBean,
                (int) TimeUtil.getTomorrowZeroSeconds());

        countdownReportResp.setCountDownFinished(userTsDialInfoBean.isCountDownFinished());
        countdownReportResp.setConfigYSeconds(userTsDialInfoBean.getConfigYSeconds());
        countdownReportResp.setLeftYSeconds(userTsDialInfoBean.getLeftYSeconds());
        return countdownReportResp;
    }

    @Override
    public UseNVO useN(UseNReq req) {
        log.info("使用coverGoods免责卡, req: {}", req);
        UseNVO useNVO = new UseNVO();

        Long userId = req.getUserId();
        Long tsId = req.getTsId();
        String useReason = "解除读秒";

        TransportMain transportMain = transportMainService.getBySrcMsgId(tsId);
        PreConditions.check(transportMain != null && Objects.equals(transportMain.getStatus(), 1),
                ReturnCodeConstant.ERROR, "货源不存在");

        CoverGoodsDialInfoVO coverGoodsDialInfo = getCoverGoodsDialInfo(userId);

        if (!coverGoodsDialInfo.isDialUser()) {
            // 不需要用
            useNVO.setErrorMessage("不满足条件，不能使用免责卡");
        } else if (coverGoodsDialInfo.getLeftN() == null || coverGoodsDialInfo.getLeftN() <= 0) {
            // 没剩余
            useNVO.setErrorMessage("没有剩余免责卡");
        } else {
            UserTsDialInfoRedisBean userTsDialInfoBean = getUserTsDialInfoRedisBean(userId, tsId);
            if (Boolean.TRUE.equals(userTsDialInfoBean.getUseN())) {
                // 此货源已经使用过
                useNVO.setErrorMessage("此货源已经使用过免责卡");
            } else if (reduceBeansConfigLeftN(userId)) {
                // 免责卡使用成功
                // save 记录使用日志
                coverGoodsDialUserUseLogMapper.insertUserUseLogByParams(userId, useReason, tsId);
                userTsDialInfoBean.setUseN(true);
                RedisUtil.setObject(getUserTsDialInfoRedisKey(userId, tsId), userTsDialInfoBean,
                        (int) TimeUtil.getTomorrowZeroSeconds());
                useNVO.setSuccessUse(true);
            } else {
                // 没有剩余免责卡
                useNVO.setErrorMessage("没有剩余免责卡");
            }
        }

        return useNVO;
    }


    @Override
    public UserTsDialInfoRedisBean getUserTsDialInfoRedisBean(Long userId, Long tsId) {
        UserTsDialInfoRedisBean userTsDialInfoBean = RedisUtil.getObject(getUserTsDialInfoRedisKey(userId, tsId));
        log.info("getUserTsDialInfoRedisBean return userTsDialInfoBean {} KEY:{} ", userTsDialInfoBean == null ? "空"
                : JSON.toJSONString(userTsDialInfoBean), getUserTsDialInfoRedisKey(userId, tsId));
        if (userTsDialInfoBean == null) {
            // 不存在倒计时信息，初始化数据
            userTsDialInfoBean = new UserTsDialInfoRedisBean();
            CoverGoodsDialInfoVO coverGoodsDialInfo = getCoverGoodsDialInfo(userId);
            userTsDialInfoBean.setUseN(false);
            userTsDialInfoBean.setCountDownFinished(false);

            userTsDialInfoBean.setConfigYSeconds(coverGoodsDialInfo.getConfigYSeconds());
            userTsDialInfoBean.setLeftYSeconds(coverGoodsDialInfo.getConfigYSeconds());
        }
        return userTsDialInfoBean;
    }

    @Override
    public CheckAutoDecreasePopConfigVO checkAutoDecreasePopConfig(Long userId) {
        CheckAutoDecreasePopConfigVO decreasePopConfigVO = new CheckAutoDecreasePopConfigVO();
        if (userId == null) {
            decreasePopConfigVO.setReported(false);
            return decreasePopConfigVO;
        }
        String key = String.format(CHECK_AUTO_DECREASE_POP_CONFIG_KEY, userId);
        String value = RedisUtil.get(key);
        if (StringUtils.isBlank(value)) {
            decreasePopConfigVO.setReported(false);
        } else {
            decreasePopConfigVO.setReported(true);
            decreasePopConfigVO.setReportTimeMils(Long.valueOf(value));
        }
        return decreasePopConfigVO;
    }

    @Override
    public void reportAutoDecreasePopConfig(Long userId) {
        if (userId == null) {
            return;
        }

        String key = String.format(CHECK_AUTO_DECREASE_POP_CONFIG_KEY, userId);
        RedisUtil.set(key, String.valueOf(System.currentTimeMillis()), 0);
    }

    @Override
    public PageData<TytCoverGoodsDialUserUseLog> getNUseRecord(Long userId, Integer pageNum, Integer pageSize) {
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(new PageParameter(pageNum, pageSize));
        List<TytCoverGoodsDialUserUseLog> goodsDialUserUseLogs =
                coverGoodsDialUserUseLogMapper.selectUserUseLogByUserId(userId);
        return customPageHelper.endPage(goodsDialUserUseLogs);
    }

    @Override
    public String getSendDefaultConfig(Integer type) {
        String key;
        switch (type) {
            case 1:
                key = USER_CREDIT_COVER_GOODS_DIAL_TIMES_CONFIG_KEY;
                break;
            case 2:
                key = ORDER_COVER_GOODS_DIAL_TIMES_CONFIG_KEY;
                break;
            case 3:
                key = CAR_COVER_GOODS_DIAL_TIMES_CONFIG_KEY;
                break;
            case 4:
                key = DRIVER_COVER_GOODS_DIAL_TIMES_CONFIG_KEY;
                break;
            case 5:
                key = ENTERPRISE_COVER_GOODS_DIAL_TIMES_CONFIG_KEY;
                break;
            case 6:
                key = OWNER_COVER_GOODS_DIAL_TIMES_CONFIG_KEY;
                break;
            default:
                throw TytException.createException(new ResponseCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "参数有误"));
        }
        return configService.getStringValue(key);
    }


    /**
     * 从数据库中获取用户抢单豆剩余总次数
     *
     * @param userId userId
     * @return 抢单豆数量
     */
    private Integer getDialConfigLeftN(Long userId) {
        CoverGoodsUserBeansVO coverGoodsUserBeansVO =
                coverGoodsBeansConfigUserMapper.selectTotalBeansLeftNumByUserId(userId);
        if (null != coverGoodsUserBeansVO) {
            return coverGoodsUserBeansVO.getTotalLeftNum() == null ? 0 : coverGoodsUserBeansVO.getTotalLeftNum();
        }
        return 0;
    }

    /**
     * 根据Id 消耗用户抢单豆个数
     *
     * @param userId
     * @return boolean
     */
    private boolean reduceBeansConfigLeftN(Long userId) {
        //获取用户目前拥有的抢单豆信息
        List<TytCoverGoodsBeansConfigUser> gainInfoLogs = coverGoodsBeansConfigUserMapper.selectLeftNumByUserId(userId);
        //定义目前总共剩余数量为0
        Integer dialConfigNLeft = 0;
        //需要更新的记录Id为0l
        Long BeansId = 0l;
        //需要更新的记录状态值
        Integer beansStatus = BEANS_STATUS_EFFECTIVE;
        //获取目前总共剩余数量
        if (null != gainInfoLogs && gainInfoLogs.size() > 0) {
            BeansId = gainInfoLogs.get(0).getId();
            if (gainInfoLogs.get(0).getLeftNum() == 1) {
                beansStatus = BEANS_STATUS_USED;
            }
            for (TytCoverGoodsBeansConfigUser gainInfoLog : gainInfoLogs) {
                dialConfigNLeft += gainInfoLog.getLeftNum();
            }
        }
        if (dialConfigNLeft <= 0) {
            return false;
        }
        if (BeansId.intValue() > 0) {
            coverGoodsBeansConfigUserMapper.useBeansById(BeansId, beansStatus);
//            RedisUtil.mapPut(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, String.valueOf(userId),
//                    String.valueOf(dialConfigNLeft-1));
            return true;
        }
        return false;
    }

    /**
     * 扣除配置的免责卡
     *
     * @param userId userId
     * @return true 扣除成功, false 扣除失败
     */
    private boolean reduceDialConfigLeftN(Long userId) {
        String dialConfigNLeftStr = RedisUtil.getMapValue(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, String.valueOf(userId));
        if (StringUtils.isNotBlank(dialConfigNLeftStr)) {
            int dialConfigNLeft = Integer.parseInt(dialConfigNLeftStr);
            if (dialConfigNLeft <= 0) {
                return false;
            }
            dialConfigNLeft--;
            RedisUtil.mapPut(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, String.valueOf(userId),
                    String.valueOf(dialConfigNLeft));
            return true;
        }
        return false;
    }

    private String getUserTsDialInfoRedisKey(Long userId, Long tsId) {
        PreConditions.check(userId != null && tsId != null, ReturnCodeConstant.ERROR, "参数有误");
        return String.format(DIAL_INFO_REDIS_KEY, userId, tsId);
    }

    @Data
    public static class UserTsDialInfoRedisBean implements Serializable {
        /**
         * 是否使用过免责卡
         */
        private Boolean useN;
        /**
         * 配置的y的时间, 单位秒
         */
        private Integer configYSeconds;
        /**
         * y的剩余时间, 单位秒
         */
        private Integer leftYSeconds;
        /**
         * 倒计时是否结束
         */
        private boolean countDownFinished;
    }

    // =========================================捂货4.0开始=============================================================

    /**
     * 6500 捂货需求4.0
     * <pre>
     * 0. 判断是否勾选优推好车主，不勾选跳过，
     * 1. 不在捂货名单中且不在捂货白名单中，不捂货，跳过
     * 2. 高等级车主不捂货，如果车主lv>=5，直接不捂货，跳过
     * 3. 用过免责卡，不捂货，跳过
     * 4. 没走好货模型的货源不参与捂货，如代调发货、专车货源
     * 5. 每天第一次点击不捂货，只针对中货、差货
     * 7. 判断捂货记录表里是否存在该货源，存在就返回捂货信息，里面有捂货结束时间，判断是否过期
     * 8. 已浏览过的货源不捂货
     * 9. 最后根据货源级别：好货|中货|差货判断
     *      a. 好货100%捂货
     *     	a. 判断捂货次数是否 < N，大于则不捂货（好货不限制）
     *     	b. 根据缓存中浏览过的中货|差货的次数，判断是否是配置的倍数，是则捂货，不是则不捂货。
     * 10.命中捂货规则，记录到捂货记录表中
     *
     * 注意：
     * 1. 已经点过的货源不计入次数
     * 2. 已经捂过的货不能再次被捂
     * 3. 中货差货区分开逻辑
     * 4. 每天每人最多捂货N，好货不计入N
     * 5. 每天第一次点击不捂货，后面开始隔X捂货
     *
     * APP端弹出捂货弹框的条件：
     * 1. 货源priorityRecommendExpireTime != null
     * 2. 货源status == 1
     * 3. 捂货expire == false
     * 4. dialUser == true
     * 5. useN == false
     * 如果用户在捂货名单中，才会弹倒计时；只在捂货白名单中，会弹捂货提示语，不需要倒计时。
     * </pre>
     */
    @Override
    public GoodsSingleDetailResultBean.CoverGoodsDialInfo coverGoodsVersion4(Long userId, TransportMain transportMain) {
        // log.info("====================================捂货4.0处理逻辑开始==============================");
        Long srcMsgId = transportMain.getSrcMsgId();

        GoodsSingleDetailResultBean.CoverGoodsDialInfo coverGoodsDialInfo = buildCoverGoodsDialInfo(userId, srcMsgId);
        coverGoodsDialInfo.setExpire(true); // 先设置默认过期，不弹窗
        // log.info("旧逻辑查询的捂货信息：{}", JSON.toJSONString(coverGoodsDialInfo));

        // 0. 是否勾选优推好车主
        if (!isPriorityRecommend(srcMsgId)) {
            log.info("货主没有勾选优推好车主，不捂货，srcMsgId:{}", srcMsgId);
            return coverGoodsDialInfo;
        }

        // 1. 没有捂货到期时间，不捂货
        if (transportMain.getPriorityRecommendExpireTime() == null) {
            log.info("货源没有捂货过期时间，不捂货，srcMsgId:{}", srcMsgId);
            return coverGoodsDialInfo;
        }

        // 1. 不在捂货名单和捂货白名单中，不捂货
        if (!coverGoodsDialInfo.isDialUser()) {
            log.info("当前用户不在捂货名单和捂货白名单中，不捂货：{}", userId);
            return coverGoodsDialInfo;
        }

        // 3. 用过免责卡，不捂货
        if (coverGoodsDialInfo.getUseN()) {
            log.info("车主已使用过免责卡，不捂货：userId: {}, srcMsgId: {}", userId, srcMsgId);
            return coverGoodsDialInfo;
        }

        // 4. 如果没走好货模型，忽略
        TransportLabelJson transportLabelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
        if (transportLabelJson == null || transportLabelJson.getIGBIResultData() == null) {
            log.info("该货源没走好货模型，iGBIResultData是空：{}", transportMain.getLabelJson());
            return coverGoodsDialInfo;
        }

        // 2. 高等级车主不捂货
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
        if (userCreditInfo != null) {
            String carCreditRankLevel = userCreditInfo.getCarCreditRankLevel();
            if (StringUtils.isNotBlank(carCreditRankLevel) && Integer.parseInt(carCreditRankLevel) >= 5) {
                log.info("当前用户的信用等级>=5，不捂货：{}", userId);
                return coverGoodsDialInfo;
            }
        }

        // 6. 每天第一次点击不捂货，只对中货差货生效
        Integer goodsLevel = transportLabelJson.getIGBIResultData();
        String goodsViewKey = getGoodsViewKey(userId);
        Map<String, Integer> goodsViewMap = getGoodsViewMapAndInit(goodsViewKey, goodsLevel);
        if (goodsViewMap.isEmpty() && goodsLevel != 1) {
            log.info("每天第一次点击不捂货");
            return coverGoodsDialInfo;
        }

        // 7. 如果倒计时未结束，返回捂货信息
        if (coverGoodsDialInfo.getLeftYSeconds() != null && coverGoodsDialInfo.getLeftYSeconds() > 0) {
            coverGoodsDialInfo.setExpire(transportMain.getPriorityRecommendExpireTime().before(new Date()));
            log.info("该货源之前点击过，是否需要捂货，需要判断是否过期：{}", JSON.toJSONString(coverGoodsDialInfo));
            return coverGoodsDialInfo;
        }

        // 8. 已浏览过不再捂货
        boolean viewed = transportViewLogService.isViewed(userId, transportMain.getSrcMsgId());
        if (viewed) {
            log.info("该货源已浏览过，不再捂货：{}，表tyt_transport_view_log", transportMain.getSrcMsgId());
            return coverGoodsDialInfo;
        }

        // 浏览次数+1
        Integer viewTimes = goodsViewMap.getOrDefault(goodsLevel.toString(), 0) + 1;
        RedisUtil.mapObjectPut(goodsViewKey, goodsLevel.toString(), viewTimes);

        // 9. 根据货源等级，分别判断是否捂货
        int coverGoodsInterval = isCoverGoods(goodsLevel, userId, viewTimes);
        if (coverGoodsInterval == 0) {
            log.info("货源等级：{}，当前用户浏览次数：{}，不捂货，srcMsgId:{}", goodsLevel, viewTimes, srcMsgId);
            return coverGoodsDialInfo;
        }

        // 要捂货，判断是否过了捂货时间
        boolean expire = transportMain.getPriorityRecommendExpireTime().before(new Date());
        coverGoodsDialInfo.setExpire(expire);

        // 保存捂货记录
        if (!expire) {
            coverGoodsDialInfo.setLeftYSeconds(coverGoodsDialInfo.getConfigYSeconds());
            saveCoverGoodsLog(userId, transportMain, coverGoodsDialInfo, goodsLevel, viewTimes, coverGoodsInterval);
            log.info("该货源需要捂货，新的捂货信息为：{}", JSON.toJSONString(coverGoodsDialInfo));
        } else {
            log.info("该货源已过期，不需要捂货：{}", JSON.toJSONString(coverGoodsDialInfo));
        }

        // log.info("====================================捂货4.0处理逻辑结束==============================");
        return coverGoodsDialInfo;
    }

    /**
     * 构建捂货信息，这是之前的逻辑
     */
    private GoodsSingleDetailResultBean.CoverGoodsDialInfo buildCoverGoodsDialInfo(Long userId, Long srcMsgId) {
        GoodsSingleDetailResultBean.CoverGoodsDialInfo coverGoodsDialInfo = new GoodsSingleDetailResultBean.CoverGoodsDialInfo();

        CoverGoodsDialInfoVO coverGoodsDialInfoVO = this.getCoverGoodsDialInfo(userId);
        coverGoodsDialInfo.setDialUser(coverGoodsDialInfoVO.isDialUser());
        coverGoodsDialInfo.setDialWhiteUser(coverGoodsDialInfoVO.isDialWhiteUser());
        coverGoodsDialInfo.setLeftN(coverGoodsDialInfoVO.getLeftN());
        coverGoodsDialInfo.setConfigXSeconds(coverGoodsDialInfoVO.getConfigXSeconds());
        coverGoodsDialInfo.setConfigYSeconds(coverGoodsDialInfoVO.getConfigYSeconds());

        UserTsDialInfoRedisBean userTsDialInfoBean = RedisUtil.getObject(getUserTsDialInfoRedisKey(userId, srcMsgId));
        if (userTsDialInfoBean == null) {
            coverGoodsDialInfo.setUseN(false);
            coverGoodsDialInfo.setCountDownFinished(false);
        } else {
            coverGoodsDialInfo.setUseN(userTsDialInfoBean.getUseN());
            coverGoodsDialInfo.setLeftYSeconds(userTsDialInfoBean.getLeftYSeconds());
            coverGoodsDialInfo.setCountDownFinished(userTsDialInfoBean.isCountDownFinished());
        }
        return coverGoodsDialInfo;
    }

    /**
     * 是否勾选优推好车主
     */
    private boolean isPriorityRecommend(Long srcMsgId) {
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String biDataKey = CommonUtil.joinRedisKey("tyt:cache:bi:data", srcMsgId.toString(), today);
        String biData = RedisUtil.get(biDataKey);
        TransportBIDataJson transportBIDataJson = JSONObject.parseObject(biData, TransportBIDataJson.class);
        return transportBIDataJson != null && Objects.equals(transportBIDataJson.getPriorityRecommend(), 1);
    }

    /**
     * 返回缓存中的浏览记录
     * {"1":100,"2":100,"3":100} key是货物等级，value是浏览次数
     */
    private Map<String, Integer> getGoodsViewMapAndInit(String goodsViewKey, Integer goodsLevel) {
        Date now = new Date();
        Map<String, Integer> goodsViewMap = RedisUtil.getObjectMap(goodsViewKey);
        if (goodsViewMap == null) {
            goodsViewMap = new HashMap<>();
            // 缓存到今天24:00点失效
            long cacheSeconds = (DateUtil.endOfDay(now).getTime() - now.getTime()) / 1000 + 1000;
            RedisUtil.setObjectMap(goodsViewKey, goodsLevel.toString(), 1, (int) cacheSeconds);
        }
        return goodsViewMap;
    }

    private String getGoodsViewKey(Long userId) {
        return CommonUtil.joinRedisKey("tyt:goods:view", DateUtil.format(new Date(), "yyyyMMdd"), userId.toString());
    }

    /**
     * 按货物等级判断是否捂货:好货直接捂货；中货差货按间隔捂
     * @return 返回捂货间隔，如果=0不捂货；>0都捂货
     */
    private int isCoverGoods(Integer goodsLevel, Long userId, Integer viewTimes) {
        if (goodsLevel == 1) {
            log.info("好货100%捂货");
            return 1;
        }
        // 获取当天已捂货次数
        int todayCoverTimes = coverGoodsLogMapper.countTodayCoverTimesWithoutGoodGoods(userId);

        String coverGoodsConfig = tytConfigService.getStringValue("cover_goods_config", "{}");
        JSONObject coverGoodsMap = JSONObject.parseObject(coverGoodsConfig);
        int maxTimesEveryDay = coverGoodsMap.getIntValue("N");  // 每天最大捂货次数

        // 超过最大次数，不捂货
        if (todayCoverTimes >= maxTimesEveryDay) {
            log.info("今天捂货次数已超过最大捂货次数，不再捂货，今天已捂货：{}，最大捂货次数：{}", todayCoverTimes, maxTimesEveryDay);
            return 0;
        }

        // 只有是间隔的倍数才捂货
        int goodsInterval = coverGoodsMap.getIntValue(goodsLevel.toString());
        if (goodsInterval == 0) {
            log.info("没有配置当前货源类型的捂货次数，不捂货");
            return 0;
        }
        log.info("这个货源的等级是{}，浏览次数是：{}，捂货倍数是：{}，是否捂货：{}", goodsLevel, viewTimes, goodsInterval, viewTimes % goodsInterval == 0);
        return viewTimes % goodsInterval == 0 ? goodsInterval : 0;
    }

    /**
     * 保存捂货记录
     */
    private void saveCoverGoodsLog(Long userId, TransportMain transportMain,
                                   GoodsSingleDetailResultBean.CoverGoodsDialInfo coverGoodsDialInfo,
                                   Integer goodsLevel, int viewTimes,int coverGoodsInterval) {
        // 保存捂货记录
        CoverGoodsLogDO coverGoodsLog = new CoverGoodsLogDO();
        coverGoodsLog.setUserId(userId);
        coverGoodsLog.setTsId(transportMain.getSrcMsgId());
        coverGoodsLog.setGoodsLevel(goodsLevel);
        coverGoodsLog.setViewIndex(viewTimes);
        String hitRule = goodsLevel == 1 ? "好货100%捂货" : "中货差货间隔捂货";
        coverGoodsLog.setHitRule(hitRule);
        coverGoodsLog.setPriorityRecommendExpireTime(transportMain.getPriorityRecommendExpireTime());
        coverGoodsLog.setXTimeInSeconds(coverGoodsDialInfo.getConfigXSeconds());
        coverGoodsLog.setYTimeInSeconds(coverGoodsDialInfo.getConfigYSeconds());
        coverGoodsLog.setCoverInterval(coverGoodsInterval);
        coverGoodsLog.setCreateTime(new Date());
        coverGoodsLogMapper.insert(coverGoodsLog);
    }

    // =========================================捂货4.0结束=============================================================
}
