package com.tyt.transport.service;

import java.util.List;
import com.tyt.base.service.BaseService;
import com.tyt.model.TytCommonUserMachineTypeNew;

/**
 * 长发货物服务层接口
 * 
 * <AUTHOR>
 * @date 2017-4-14下午5:44:50
 * @description
 */
public interface CommonUserMachineTypeNewService extends BaseService<TytCommonUserMachineTypeNew, Long> {

	/**
	 *
	 * @param type 类型：1-旧版，2-新版V6000
	 * @return
	 */
	List<TytCommonUserMachineTypeNew> queryCommonUseMachineType(int type);

}
