package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TransportDone;
import com.tyt.transport.querybean.TransportDoneListBean;
import com.tyt.transport.querybean.TransportDoneRequest;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * User: Administrator Date: 19-10-23 Time: 下午6:25
 */
public interface TransportDoneService extends BaseService<TransportDone, Long> {

    /**
     *
     * @param tsId
     * @return List<TransportDone>
     */
    TransportDone getByTsId(Long tsId);
    /**
     * 获取已成交列表
     *
     * @param userId
     * @param queryActionType
     * @param queryID
     * @return List<TransportDone>
     */
    List<TransportDoneListBean> getMyTransportDone(Long userId, Integer queryActionType, Long queryID,String clientSign);

    /**
     * 保存货源成交信息
     *
     * @param userId      货源发布者ID
     * @param goodsId     货源ID
     * @param doneRequest 货源成交所需信息
     * @return 货源ID
     */
    Long saveTransportDone(Long userId, Long goodsId, TransportDoneRequest doneRequest);

    /**
     * 货源设置成交之后的消息处理，如：设置成交、车辆定位
     * @param tsId 货源ID
     * @param type 类型，1-设置成交， 2-车辆定位， ...
     */
    void sendTransportDealAfter2MQ(long tsId, int type);

    void updateChangCarStatus(Long tsId,String headCity,String headNo,String tailCity,String tailNo,Long userId, BigDecimal dealPrice);

    void updateAllowStatus(Long tsId, Integer isAllow);

    /**
     * 更新车辆信息
     * @param transportDone
     */
    void updateCarInfo(TransportDone transportDone);

    /**
     * @description 根据更新时间获取货源成交记录
     * <AUTHOR>
     * @date 2022/9/19 9:52
     * @param userId
     * @param carryUserId
     * @param headCity
     * @param headNo
     * @param tailCity
     * @param tailNo
     * @param ctime
     * @return com.tyt.model.TransportDone
     */
    TransportDone getTransportDoneByMtime(Long userId, Long carryUserId, String headCity, String headNo, String tailCity, String tailNo, Date ctime) throws Exception;
}
