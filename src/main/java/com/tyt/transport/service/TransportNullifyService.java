package com.tyt.transport.service;

import com.tfc.analysis.entity.Keyword;
import com.tyt.base.service.BaseService;
import com.tyt.model.TransportNullifyBean;

import java.util.List;

/**
 * 无效货源信息
 * User: Administrator
 * Date: 17-04-24
 * Time: 下午3:16
 */
public interface TransportNullifyService extends BaseService<TransportNullifyBean,Long> {

    /**
     * 验证货物信息
     * @param id
     * @throws Exception
     * @return true:正常数据    false:存在过滤关键字
     */
    public String verifyNullifyTaskContent(String taskContent) throws Exception;

    /**
     * 判断集合中是否存在关键字
     *
     * @param taskContent 关键字
     * @param keywordList 要筛选的集合
     * @return
     */
    List<String> getKeywordByTaskContentAndRemark(String taskContent, List<Keyword> keywordList);

}
