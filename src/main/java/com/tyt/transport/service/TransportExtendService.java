package com.tyt.transport.service;

import com.tyt.plat.entity.base.TytTransportExtend;

/**
 * <AUTHOR>
 * @since 2024/8/31 15:59
 */
public interface TransportExtendService {
    /**
     * 保存货源扩展信息
     *
     * @param transportExtend
     */
    void addExtend(TytTransportExtend transportExtend);

    /**
     * 根据货源id查询扩展信息
     *
     * @param tsId
     * @return
     */
    TytTransportExtend getByTsId(Long tsId);

    /**
     * 更新扩展信息
     *
     * @param dbLastExtend
     */
    void updateExtend(TytTransportExtend dbLastExtend);

}
