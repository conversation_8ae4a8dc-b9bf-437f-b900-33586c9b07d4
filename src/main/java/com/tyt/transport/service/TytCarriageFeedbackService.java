package com.tyt.transport.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.transport.querybean.CarriageFeedbackBean;

public interface TytCarriageFeedbackService {

    /***
      * <AUTHOR> Lion
      * @Description 提交用户反馈价格
      * @Param [carriageFeedbackBean]
      * @return void
      * @Date 2022/9/15 16:44
      */
    void saveCarriageFeedback(CarriageFeedbackBean carriageFeedbackBean);

}
