package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TransportViewLog;

/**
 * Created by duanwc on 2019/1/29.
 */
public interface TransportViewLogService extends BaseService<TransportViewLog, Long> {

    /**
     * 货物详情浏览日志记录
     *
     * @param userId        用户ID  没有传0
     * @param tsId          运输信息ID
     * @param clientVersion 版本号
     * @param clientSign    客户端类型
     */
    Integer addLog(long userId, long tsId, String clientVersion, int clientSign);
    /**
     * 货物详情浏览日志记录
     *
     * @param userId        用户ID  没有传0
     * @param tsId          运输信息ID
     * @param clientVersion 版本号
     * @param clientSign    客户端类型
     */
    Integer detailsLog(long userId, long tsId, String clientVersion, int clientSign);

    /**
     * 是否浏览过该货源
     *
     * @param userId 用户ID
     * @param tsId   货源id
     */
    boolean isViewed(Long userId, Long tsId);
}
