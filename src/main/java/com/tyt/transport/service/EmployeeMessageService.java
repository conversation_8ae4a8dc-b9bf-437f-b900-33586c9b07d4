package com.tyt.transport.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.EmployeeMessage;

public interface EmployeeMessageService extends BaseService<EmployeeMessage, Long> {

	public void saveEmpMessage(Long mtId, Long srcMsgId, String goodsContent,
			Integer ecaStatus, String signature, String userName,
			Long createUserId, String createUserName);

	/**
	 * 企业货源转特运通后发站内信给客服人员
	 * @param userId
	 * @param userName
	 * @param tsId 
	 * @param taskContent 
	 * @param destPoint 
	 * @param startPoint 
	 * @param ctime 
	 * @param corpName 
	 * @return
	 */
	public void saveCorpMessageForPub(Long userId, String userName, String corpName, String ctime, String startPoint, String destPoint, String taskContent, String tsId);

	/**
	 * 站内信 特运通代发货源 企业下架通知对接人
	 * @param userId
	 * @param userName
	 * @param corpName
	 * @param ctime
	 * @param startPoint
	 * @param destPoint
	 * @param taskContent
	 * @param tsId
	 */
	void saveCorpMessageForCannel(Long userId, String userName, String corpName, String ctime, String startPoint, String destPoint, String taskContent, String tsId);
}
