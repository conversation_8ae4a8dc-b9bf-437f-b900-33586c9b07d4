package com.tyt.transport.service;

/**
 * 货源自动重发service
 *
 * <AUTHOR>
 * @since 2024/12/12 20:13
 */
public interface TransportAutoResendService {

    /**
     * 查询当前货源是否已设置自动重发
     */
    boolean isAutoResend(Long srcMsgId);

    /**
     * 添加货源自动重发记录
     *
     * @param isAutoResend 是否勾选自动重发
     * @param userId       userId
     * @param srcMsgId     要重发的货源id
     */
    void addAutoResendRecord(Integer isAutoResend, Long userId, Long srcMsgId);

    /**
     * 添加货源重发日志
     *
     * @param oldSrcMsgId 旧货源id
     * @param newSrcMsgId 新货源id
     * @param resendType  重发类型 1自动重发；2手动重发
     */
    void addResendLog(Long oldSrcMsgId, Long newSrcMsgId, Integer resendType);
}
