package com.tyt.transport.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrivacyPhoneNumGoodIdReq {


    //货源ID
    private Long goodId;

    //操作类型 1:找货；2:订单
    private Integer operateType;

    //车主手机号
    private String driverUserPhone;

    //车主用户ID
    private Long driverUserId;

    //货主手机号序号
    private String goodUserPhoneIDX;

    //货主手机号
    private String goodUserPhone;

}
