package com.tyt.transport.bean;

import lombok.Getter;

/**
 * 发布终端
 *
 * <AUTHOR>
 * @date 2023/2/10 14:10
 */
public enum PublishPlatformEnum {

    manager(1, "后台"),
    app(2, "app"),
    pc(3, "pc"),
    ;

    @Getter
    private int code;
    @Getter
    private String zhName;

    PublishPlatformEnum(int code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    public boolean equalsCode(int reqCode) {
        return this.code == reqCode;
    }

}