package com.tyt.transport.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 专车运费价格配置实体对象
 * <AUTHOR>
 * @since 2024-06-22 15:06
 */
@Data
public class PriceConfigBean {
    /**
     * 起始公里
     */
    private BigDecimal start;
    /**
     * 终止公里
     */
    private BigDecimal end;
    /**
     * 价格类型：1-固定价格，2-公里价
     */
    private Integer type;
    /**
     * 运费
     */
    private BigDecimal price;
}
