package com.tyt.transport.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_hot_update_file_config")
public class TytHotUpdateFileConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 版本号
     */
    @Column(name = "release_version")
    private String releaseVersion;

    /**
     * 文件路径
     */
    @Column(name = "file_url")
    private String fileUrl;

    /**
     * 版本标识
     */
    private Integer version;

    /**
     * 发布标识；0:未发布；1:已发布
     */
    @Column(name = "publish_type")
    private Integer publishType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人用户名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 创建人用户ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;
}