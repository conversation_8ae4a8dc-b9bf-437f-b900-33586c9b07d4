package com.tyt.transport.bean;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 货源抽佣请求类
 *
 * <AUTHOR>
 * @since 2024/01/15 14:20
 */
@Data
public class DrawCommissionReq extends BaseParameter implements Serializable {

    /**
     * 对应货源表中的source_type
     */
    private Integer sourceType;
    /**
     * 对应货源表中的excellent_goods
     */
    private Integer excellentGoods;

    /**
     * 是否是优车定价货源 0 否  1是
     */
    private Integer goodCarPriceTransport;


    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;
    /**
     * 有无价格（0无价，1有价）
     */
    private Integer havePrice;
    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;
    /**
     * 抽佣发生时间
     */
    private Date commissionTime;

    /**
     * 抽佣渠道 1：普货,2优车,3优车定价,4运满满,5专车
     */
    private Integer commissionSource;
    /**
     * 好货模型分数
     */
    private BigDecimal goodsModelScore;

    /**
     * 是否开票货源 0：否；1：是
     */
    private Integer invoiceTransport;

}
