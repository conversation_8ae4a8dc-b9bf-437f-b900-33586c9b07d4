package com.tyt.transport.bean;

import com.tyt.infofee.bean.MqBaseMessageBean;
import lombok.Data;

import java.util.List;

/**
 * 专车发货自动派单mq
 * <AUTHOR>
 * @since 2024-06-05 10:57
 */
@Data
public class MqAutoAssignOrderBean extends MqBaseMessageBean {
    /**
     * 货物ID
     */
    private Long goodsId;
    /**
     * 发货用户类型：1-调度发货，2-普通用户发货
     */
    private Integer publishUserType;
    /**
     * 代调员工ID
     */
    private Long dispatchUserId;
    /**
     * 代调员工用户名
     */
    private String dispatchUserName;
    /**
     * 代调手机号
     */
    private String dispatchCellPhone;
    /**
     * 代调绑定钉钉手机号
     */
    private String dispatchDingTalkPhone;
    /**
     * 派单方式：1-同时指派，2-先后指派
     */
    private Integer dispatchType;

    /**
     * 先后指派司机x分钟未接单指派下一位
     */
    private Integer afterMinutes;
    /**
     * 可派单司机列表
     */
    private List<AssignOrderUser> userList;

}


