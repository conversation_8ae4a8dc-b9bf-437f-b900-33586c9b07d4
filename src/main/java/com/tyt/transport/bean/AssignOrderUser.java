package com.tyt.transport.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 专车发货自动派单，用户实体
 * <AUTHOR>
 * @since 2024-06-05 11:47
 */
@Data
public class AssignOrderUser {
     /**
      * 车主ID
      */
     private Long userId;
     /**
      * 司机联系电话
      */
     private String telephone;
     /**
      * 优先级：数字越小优先级越高
      */
     private Integer priority;

     /**
      * 司机ID
      */
     private Long driverId;

     /**
      * tyt_signing_car_info表ID
      */
     private Long carInfoId;

     /**
      * 位置
      */
     private String location;

     /**
      * 距离
      */
     private BigDecimal distance;

     /**
      * 是否车型匹配：0-否，1-是
      */
     private Integer carTypeMatch;

     /**
      * 司机标签：1-兼职运力，2-全职运力
      */
     private Integer driverTag;

     /**
      * tyt_car表ID
      */
     private Long carId;

     @Override
     public boolean equals(Object o) {
          if (this == o) {
               return true;
          }
          if (o == null || getClass() != o.getClass()) {
               return false;
          }
          AssignOrderUser orderUser = (AssignOrderUser) o;
          return Objects.equals(userId, orderUser.userId);
     }

     @Override
     public int hashCode() {
          return Objects.hash(userId, telephone, priority, driverId, carInfoId, location, distance);
     }
}
