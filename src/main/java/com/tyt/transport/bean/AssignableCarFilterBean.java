package com.tyt.transport.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 专车派单司机过滤参数实体
 *
 * <AUTHOR>
 * @since 2024-09-12 16:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssignableCarFilterBean {
    /**
     * 货源ID
     */
    private Long srcMsgId;
    /**
     * 出发地省
     */
    private String startProvince;
    /**
     * 出发地市
     */
    private String startCity;
    /**
     * 出发地区
     */
    private String startArea;
    /**
     * 目的地省
     */
    private String destProvince;
    /**
     * 目的地市
     */
    private String destCity;
    /**
     * 司机驾驶此类货物
     */
    private Integer driverDriving;
    /**
     * 货物类型
     */
    private String goodsTypeName;
    /**
     * 签约合作商ID
     */
    private Long cargoOwnerId;
    /**
     * 派单距离限制
     */
    private Integer distanceLimit;
    /**
     * 货源类型：0-普货，1-优车，2-专车
     */
    private Integer excellentGoods;
    /**
     * 发货人ID
     */
    private Long userId;
    /**
     * 出发地纬度
     */
    private String startLatitude;
    /**
     * 出发地经度
     */
    private String startLongitude;
}
