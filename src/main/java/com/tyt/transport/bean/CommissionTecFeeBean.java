package com.tyt.transport.bean;

import com.tyt.plat.vo.ts.MeetCommissionRulesResult;
import com.tyt.transport.vo.TytTecServiceFeeConfigToComputResult;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/6 16:07
 */
@Data
public class CommissionTecFeeBean {

    private boolean recalculateTecServiceFee;
    /**
     * 是否是抽佣货源
     */
    private boolean commissionTransport;
    /**
     * 技术服务费计算结果
     */
    private TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputeResult;

    private MeetCommissionRulesResult meetCommissionRulesResult;
}
