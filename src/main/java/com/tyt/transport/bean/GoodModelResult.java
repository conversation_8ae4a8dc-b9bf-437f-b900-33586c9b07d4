package com.tyt.transport.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/8/27 11:25
 */
@Data
public class GoodModelResult {
    /**
     * 好货模型分数
     */
    private BigDecimal score;
    /**
     * 等级
     */
    private Integer level;
    /**
     * 好差货货模型分数
     */
    private BigDecimal lim_score;
    /**
     * 好差货货模型等级
     */
    private Integer lim_level;

    /**
     * 不抽佣原因
     */
    private String commission_label;
    /**
     * 抽佣标记 0 抽佣，1 不抽佣
     */
    private Integer commission_flag;
}
