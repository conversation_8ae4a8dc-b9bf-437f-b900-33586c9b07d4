package com.tyt.transport.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/4/16 16:15
 */
@Data
public class ComplaintRecordBean implements Serializable {

    private static final long serialVersionUID = 253504723579037091L;
    /**
     * 投诉人id
     */
    private Long id;
    /**
     * 创建人id
     */
    private Long userId;
    /**
     * 投诉人id
     */
    private Long complainantId;
    /**
     * 投诉人版本号
     */
    private String complainantVersion;
    /**
     * 投诉人手机号
     */
    private String complainantsPhone;
    /**
     * 被投诉人手机号
     */
    private String respondentPone;
    /**
     * 被投诉人版本号
     */
    private String respondentVersion;
    /**
     * 被投诉人id
     */
    private Long respondentId;
    /**
     * 货源id
     */
    private Long msgId;
    /**
     * 投诉原因
     */
    private String reason;
    /**
     * 图片
     */
    private String pictureUrl;
    /**
     * 当前车主的版本号
     */
    private String clientVersion;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 投诉类型描述
     */
    private String typeDesc;

}
