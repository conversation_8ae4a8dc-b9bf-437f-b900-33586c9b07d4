package com.tyt.transport.controller;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tyt.base.controller.BaseController;
import com.tyt.model.BlockInfo;
import com.tyt.model.ResultMsgBean;
import com.tyt.user.service.BlockInfoService;
import com.tyt.util.FormatString;
import com.tyt.util.MobileUtil;
@Controller
//@RequestMapping("/accuse")
public class TransportAccuseController extends BaseController{

	@Resource(name = "blockInfoService")
	private BlockInfoService blockInfoService;
	/**
	 * 采集
	 * @return
	 */
	@RequestMapping(value = "/save")
	public void save(
//			String informerTel,Long infoId,String blockerTel,String qq,
//			String blockerName,String reason,
			@RequestParam(value = "status", defaultValue = "1")Integer status,
			@RequestParam(value = "platId", defaultValue = "1")Integer platId,
			String token,
			BlockInfo accuse,
			HttpServletRequest request,HttpServletResponse response
			){
		long t1 = 0, t2 = 0, t3 = 0, t4 = 0,t5=0;
		if (logger.isInfoEnabled())t1 = System.currentTimeMillis();
		/*参数验证*/
		if(accuse.getInformerTel()==null||accuse.getInformerTel().trim().equals("")
		 ||accuse.getBlockerTel()==null||accuse.getBlockerTel().trim().equals("")
//		 ||accuse.getBlockerName()==null||accuse.getBlockerName().trim().equals("")
		 ||accuse.getReason()==null||accuse.getReason().trim().equals("")
		 ||accuse.getInfoId()==null||accuse.getInfoId().longValue()<=0
		){
			logger.info("accuse/save> arguments error! "+accuse.info());
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"arguments required");
			printJSON(request, response, msgBean);
			return;
		}
		if (logger.isInfoEnabled())t2 = System.currentTimeMillis();
		/*token验证*/
		if (!validateToken("" + accuse.getInformerTel(), token, request, response)) {
			logger.info("accuse/save> Token Error");
			return;
		}
		if (logger.isInfoEnabled())t3 = System.currentTimeMillis();
		/*参数设置*/
		if(accuse.getBlockerName()!=null&&!accuse.getBlockerName().equals(""))accuse.setBlockerName(accuse.getBlockerName() .replaceAll("\\s*",""));
		accuse.setBlockerTel(accuse.getBlockerTel().replaceAll("\\s*",""));
		accuse.setInformerTel(accuse.getInformerTel().replaceAll("\\s*",""));
		accuse.setReason(FormatString.specialStringFilter(accuse.getReason()));
		accuse.setStatus(status);
		accuse.setPlatId(platId);
		accuse.setAddress(MobileUtil.getMobileAddress(accuse.getBlockerTel()));
		/*验证重复性*/
		StringBuffer sql=new StringBuffer();
		sql.append(" entity.informerTel="+accuse.getInformerTel());
		sql.append(" and entity.infoId="+accuse.getInfoId());
		try{
			List<BlockInfo> accuses=blockInfoService.getList(sql.toString(),null);
			if(accuses.size()>0){
				logger.info("accuse/save> accuse exit!"+accuse);
				ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"accuse exist in mysql");
				printJSON(request, response, msgBean);
				return;
			}
			if (logger.isInfoEnabled())t4 = System.currentTimeMillis();
			Timestamp ctime = new Timestamp(System.currentTimeMillis());
			accuse.setCtime(ctime);
			/*添加到DB*/
			blockInfoService.add(accuse);
		    logger.info("accuse/save> save to DB success!"+accuse);
		    ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "1");
			Map dataMap = new HashMap();
			dataMap.put("id", accuse.getId());
			msgBean.setData(dataMap);
			printJSON(request, response, msgBean);
		}catch(Exception e){
			logger.info("accuse/save> " + e.toString() + accuse);
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"add failure");
			printJSON(request, response, msgBean);
		}finally{
			if (logger.isInfoEnabled()) {
				t5 = System.currentTimeMillis();
				logger.info("accuse/save> SAVE TOTAL: " + (t5 - t1) + " Arguments Validate:" + (t2 - t1)
						+ " Token Validate:" + (t3 - t2)
						+" Exit Validate:" + (t4-t3)+ " Save DB:" + (t5-t4));
			}
		}
	}
}
