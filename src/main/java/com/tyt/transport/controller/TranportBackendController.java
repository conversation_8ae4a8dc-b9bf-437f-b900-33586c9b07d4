package com.tyt.transport.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.receive.bean.StationReceiveInfoBean;
import com.tyt.receive.service.TransportBackendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Controller
@RequestMapping("/plat/transport")
public class TranportBackendController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "transportBackendService")
    private TransportBackendService transportBackendService;

    /**
     * 限时货源列表
     * @param userId  用户id
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @param queryId 下拉是第一条信息的ID，下滑是最后一条信息的ID；（首次queryID=0）
     * @return
     */
    @RequestMapping(value = "/backend/list")
    @ResponseBody
    public ResultMsgBean myOrders(@RequestParam Long userId,
                                  @RequestParam Long queryActionType,
                                  @RequestParam Long queryID){
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query backend list begin, start time is [ {} ]", startTimeInMilli);
        ResultMsgBean bean = new ResultMsgBean(ResultMsgBean.OK,"查询成功");
        //必传参数判断
        if(userId == null ){
            return new ResultMsgBean(ResultMsgBean.ERROR,"参数有误");
        }
        if(queryActionType == null ){
            return new ResultMsgBean(ResultMsgBean.ERROR,"参数有误");
        }
        if(queryID == null ){
            return new ResultMsgBean(ResultMsgBean.ERROR,"参数有误");
        }
        try{
            //数据查询
            List<StationReceiveInfoBean> orders =  transportBackendService.getMyBackend(userId, queryActionType, queryID);
            Map<String, Object> result = new HashMap<>();
            result.put("receiveOrders", orders);
            bean.setData(result);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultMsgBean(ResultMsgBean.ERROR,"参数有误");
        }
        return bean;
    }
}
