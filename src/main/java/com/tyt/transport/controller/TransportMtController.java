package com.tyt.transport.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.tyt.infofee.bean.TransportWayBillListBean;
import com.tyt.infofee.service.TransportWayBillService;
import com.tyt.model.*;
import com.tyt.transport.querybean.EcaProcessSimpleBean;
import com.tyt.transport.querybean.TransportMtDetailBean;
import com.tyt.transport.service.*;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.util.StringUtil;
import com.tyt.util.TimeUtil;

import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.infofee.bean.ListDataBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;

/**
 * 人工派单相关接口
 * 
 * <AUTHOR>
 * create by 20180109
 * 
 */
@Controller
@RequestMapping("/plat/mt/transport")
public class TransportMtController extends BaseController {

	@Resource(name = "transportService")
	private TransportService transportService;

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@Resource(name = "transportWayBillService")
	TransportWayBillService transportWayBillService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "tytUserSubService")
	TytUserSubService tytUserSubService;


	@Resource(name = "tytConfigService")
	TytConfigService tytConfigService;

	@Resource(name = "ecaContractService")
    EcaContractService ecaContractService;

	@Resource(name = "ecaProgressService")
	EcaProgressService ecaProgressService;

	@Resource(name = "ecaUserProgressService")
	EcaUserProgressService ecaUserProgressService;

	@Resource(name = "transportMtService")
	TransportMtService transportMtService;

	@Resource(name = "transportMtChangeLogService")
	TransportMtChangeLogService transportMtChangeLogService;

	@Resource(name="tsinsuranceService")
	private TsInsuranceService tsinsuranceService;
	/**
	 * 电话调车详情
	 *
	 * @param srcMsgId
	 * @return
	 */
	@RequestMapping(value = "/getMtTransportDetail")
	@ResponseBody
	public ResultMsgBean getMtTransportDetail(Long userId,String srcMsgId) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("查询成功");
		try {
			if(!StringUtils.hasLength(srcMsgId)){
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("必要参数为空");
				return rm;
			}
			//TODO 获取运单信息
			TransportMt transportMt = transportMtService.getTransportMtBySrcMsgId(Long.valueOf(srcMsgId));
			if(transportMt == null){
				rm.setCode(ResultMsgBean.ERROR);
				rm.setMsg("此货源不存在");
				return rm;
			}
			TransportMtDetailBean tdb = new TransportMtDetailBean();
			tdb.setStartPoint(transportMt.getStartPoint());
			tdb.setStartDetailAdd(transportMt.getStartDetailAdd());
			tdb.setDestPoint(transportMt.getDestPoint());
			tdb.setDestDetailAdd(transportMt.getDestDetailAdd());
			tdb.setStartLatitude(transportMt.getStartLatitude());
			tdb.setStartLongitude(transportMt.getStartLongitude());
			tdb.setDestLatitude(transportMt.getDestLatitude());
			tdb.setDestLongitude(transportMt.getDestLongitude());

			if(transportMt.getAndroidDistance() != null){
				tdb.setAndroidDistance(new BigDecimal(transportMt.getAndroidDistance()).movePointLeft(2).intValue()+"");
			}
			tdb.setStatus(transportMt.getStatus());

			//TODO 查询主表的信息费状态
//			if(StringUtils.hasLength(transportMt.getInfoStatus())){
//				tdb.setInfoStatus(Integer.valueOf(transportMt.getInfoStatus()));
//			}else{
				TytTransportWaybill tytTransportWaybill = transportWayBillService.getTransportWaybillBySrcMsgId(transportMt.getSrcMsgId());
				if(tytTransportWaybill != null){
					tdb.setInfoStatus(Integer.valueOf(tytTransportWaybill.getInfoStatus()));
				}
//			}

			tdb.setDriverPhone(transportMt.getDriverPhone());
			tdb.setSecondaryDriverPhone(transportMt.getSecondaryDriverPhone());
			tdb.setFollowDriverPhone(transportMt.getFollowDriverPhone());
			tdb.setSrcMsgId(transportMt.getSrcMsgId());
			tdb.setCarLocationScanOnOff(transportMt.getGrantLocationStatus());
			tdb.setTaskContent(transportMt.getTaskContent());
			//TODO 获取合同信息
			EcaContract ecaContract = ecaContractService.getEcaContractBySrcMsgId(Long.valueOf(srcMsgId));
			if(ecaContract == null){
				rm.setCode(ResultMsgBean.ERROR);
				rm.setMsg("此货源不存在对应的合同");
				return rm;
			}
			if(ecaContract.getCarryUserId() == null){
				rm.setCode(ResultMsgBean.ERROR);
				rm.setMsg("此合同承运人不存在");
				return rm;
			}
			tdb.setContractId(ecaContract.getId());
			tdb.setLicensePlate(ecaContract.getHeadLicensePlate());
			tdb.setTailLicensePlate(ecaContract.getTailLicensePlate());
			tdb.setContractStatus(ecaContract.getShipperSignStatus());
			tdb.setPrice(ecaContract.getPrice());
			//TODO 获取用户信息
			User user = this.userService.getByUserId(ecaContract.getCarryUserId());
			if(user == null){
				rm.setCode(ResultMsgBean.ERROR);
				rm.setMsg("此货源承运方获取失败");
				return rm;
			}
			tdb.setCarryPhone(user.getCellPhone());
			tdb.setVerifyFlag(user.getVerifyPhotoSign());
			tdb.setCarryUser(user.getUserName());
			tdb.setHeadpic(user.getHeadUrl());
			//TODO 等待车辆评级上线后，增加此字段的值
			tdb.setStarLevel("5.0");
			//TODO 获取用户进度表信息
			EcaUserProgress ecaUserProgress = new EcaUserProgress();
			ecaUserProgress.setContractId(ecaContract.getId());
			List<EcaUserProgress> ecaUserProgresses = ecaUserProgressService.getEcaUserProgressListById(ecaContract.getId());
			if(ecaUserProgresses == null){
				rm.setCode(ResultMsgBean.ERROR);
				rm.setMsg("此货源不存在对应的用户进度");
				return rm;
			}
			tdb.setProcess(exchangeEcaProcess(ecaUserProgresses));

			//获取最新的保单Id
			//货物Id
			Long tsId = transportMt.getTsId();
			// 获取最新的保单
			TransportInsurance transportInsurance = tsinsuranceService.queryInsuranceByTsId(userId, tsId);
			//最后一次购买保险的保单信息
			if(transportInsurance != null)
			{
				//是否购买过保险 1是(显示“查看保单”按钮)
				tdb.setIsBuyInsurance(1);
				//最后一次购买保险的保单Id
				Long insuranceId = transportInsurance.getId();
				if(insuranceId != null)
				{
					tdb.setInsuranceId(insuranceId);
				}
				//最后一次购买保险的保单状态 0待支付 1已生效 2已退保
				Integer status = transportInsurance.getStatus();
				if(status != null)
				{
					tdb.setInsuranceStatus(status);
				}
			}else{
				//是否购买过保险  2否(显示“买货运险”按钮)
				tdb.setIsBuyInsurance(2);
			}
			rm.setData(tdb);
			rm.setMsg("查询成功");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	//转换用户签约进度显示格式
	private List<EcaProcessSimpleBean> exchangeEcaProcess(List<EcaUserProgress> ecaUserProgresses){
		if(ecaUserProgresses == null || ecaUserProgresses.size() <= 0){
			return null;
		}
		List<EcaProcessSimpleBean> epsbs = new ArrayList<>(4);
		EcaProcessSimpleBean ecaProcessSimpleBean = null;
		for(EcaUserProgress eup: ecaUserProgresses){
			ecaProcessSimpleBean = new EcaProcessSimpleBean();
			ecaProcessSimpleBean.setTime(TimeUtil.formatShortDateTime(eup.getCtime()));
			if(eup.getType().intValue() == 1){
				ecaProcessSimpleBean.setEvent("达成意向");
			}else if(eup.getType().intValue() == 2){
				ecaProcessSimpleBean.setEvent("装货完成");
			}else if(eup.getType().intValue() == 3){
				ecaProcessSimpleBean.setEvent("签订运输合同");
			}else if(eup.getType().intValue() == 4){
				ecaProcessSimpleBean.setEvent("购买货物险");
			}
			epsbs.add(ecaProcessSimpleBean);
		}

		return epsbs;
	}

	/**
	 *
	 *   作废电子合同
	 * @param contractId   合同ID
	 * @param shipperCancelReason   作废原因货方(托运方)
	 * @param carryCancelReason     作废原因车方(承运方)
	 * @param userName   客服姓名
	 * @param userId     客服ID
	 * @param optIp      操作IP
	 * @return
	 */
	@RequestMapping(value = "/cancelContract")
	@ResponseBody
	public ResultMsgBean cancelContract(String contractId, String shipperCancelReason,String carryCancelReason,String userName,Long currUserId,String optIp) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);

		try {
			if(!StringUtils.hasLength(contractId) || (!StringUtils.hasLength(shipperCancelReason) && !StringUtils.hasLength(carryCancelReason))){
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("必要参数为空");
				return rm;
			}
			//TODO 查询合同信息
			EcaContract ecaContract = ecaContractService.getById(Long.valueOf(contractId));
			//TODO 校验状态
			if(ecaContract == null || ecaContract.getStatus() == null || ecaContract.getStatus().intValue() < 2 || ecaContract.getStatus().intValue() > 3){
				rm.setCode(ResultMsgBean.ERROR);
				rm.setMsg("此合同当前状态不允许作废");
				return rm;
			}
			String detail = "";
			//TODO 单方取消合同需要填写单方原因
			if(ecaContract.getShipperSignStatus() != null && ecaContract.getShipperSignStatus().intValue() == 2 ){
				if(!StringUtils.hasLength(shipperCancelReason)){
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("货方作废原因为空");
					return rm;
				}
				ecaContract.setShipperCancelReason(shipperCancelReason);
				detail += ",货方理由："+shipperCancelReason;
			}
			if(ecaContract.getCarrySignStatus() != null && ecaContract.getCarrySignStatus().intValue() == 2){
				if(!StringUtils.hasLength(carryCancelReason)){
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("车方作废原因为空");
					return rm;
				}
				ecaContract.setCarryCancelReason(carryCancelReason);
				detail += ",车方理由："+carryCancelReason;
			}
			//TODO 更新状态
			ecaContract.setStatus(4);
			ecaContract.setMtime(new Date());
			ecaContractService.updateCancelContract(ecaContract,String.valueOf(currUserId),userName);
			//TODO 合同作废后需要重新初始化一些数据

			//TODO 记录作废日志
			EcaProgress eProgress = new EcaProgress();
			eProgress.setContractId(ecaContract.getId());
			eProgress.setCtime(new Date());
			eProgress.setDetail("作废当前合同"+detail);
			eProgress.setDevice("");
			eProgress.setIdCard("");
			eProgress.setImei("");
			eProgress.setIpAddress(optIp);
			eProgress.setOptHandler(4);   //客服作废合同
			eProgress.setOptType(4);   //作废操作
			eProgress.setStatus(2); 	//完成状态
			eProgress.setUserId(currUserId);
			eProgress.setUserName(userName);
			ecaProgressService.add(eProgress);
			rm.setMsg("合同作废成功");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 客服编辑合同接口
	 * @param contractId  合同ID
	 * @param ip
	 * @param userId    用户ID或者客服ID
	 * @param userName    用户姓名或者客服姓名
	 * @return
	 */
	@RequestMapping(value = "/editContract")
	@ResponseBody
	public ResultMsgBean editContract(String contractId, EcaContract ecaContract,String ip,
									   String currUserId,String userName) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);

		try {
			if(!StringUtils.hasLength(contractId)){
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("必要参数为空");
				return rm;
			}
			//TODO 查询合同信息
			EcaContract ecaContractTemp = ecaContractService.getById(Long.valueOf(contractId));
			//TODO 校验状态
			if(ecaContractTemp == null || ecaContractTemp.getStatus() == null || ecaContractTemp.getStatus().intValue() != 1){
				rm.setCode(ReturnCodeConstant.NOT_ALLOW);
				rm.setMsg("此合同当前状态不允许编辑");
				return rm;
			}

			//TODO 客服编辑合同内容后，需要记录到变更表
			TransportMtChangeLog changeLog = new TransportMtChangeLog();
			changeLog.setSrcMsgId(ecaContractTemp.getSrcMsgId());
			changeLog.setMtPrice(String.valueOf(ecaContractTemp.getPrice()));
			changeLog.setMtLicensePlate(ecaContractTemp.getHeadLicensePlate()+","+ecaContractTemp.getTailLicensePlate());

			//TODO 更新合同内容
			ecaContractTemp.setStartPoint(ecaContract.getStartPoint());
			ecaContractTemp.setStartDetailAdd(ecaContract.getStartDetailAdd());
			ecaContractTemp.setDestPoint(ecaContract.getDestPoint());
			ecaContractTemp.setDestDetailAdd(ecaContract.getDestDetailAdd());
			ecaContractTemp.setPrice(ecaContract.getPrice());
			ecaContractTemp.setCashPrice(ecaContract.getCashPrice());
			ecaContractTemp.setOilCard(ecaContract.getOilCard());
			ecaContractTemp.setTaskContent(ecaContract.getTaskContent());
			ecaContractTemp.setHeadLicensePlate(ecaContract.getHeadLicensePlate());
			ecaContractTemp.setTailLicensePlate(ecaContract.getTailLicensePlate());
			if(org.apache.commons.lang3.StringUtils.isNoneEmpty(ecaContract.getSettleAccountsType())) {
				ecaContractTemp.setSettleAccountsType(ecaContract.getSettleAccountsType());
			}
			ecaContractTemp.setMtime(new Date());
			ecaContractService.update(ecaContractTemp);

			changeLog.setConPrice(String.valueOf(ecaContract.getPrice()));
			changeLog.setConLicensePlate(ecaContract.getHeadLicensePlate()+","+ecaContract.getTailLicensePlate());
			transportMtChangeLogService.add(changeLog);
			//TODO 记录编辑日志
			saveEcaProcess(contractId,ip,currUserId,userName,1,ecaContractTemp,"","");
			rm.setMsg("合同编辑成功");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private void saveEcaProcess(String contractId,String ip,String userId,String userName,Integer optHandler,EcaContract ecaContractTemp,String device,String imei){
		//TODO 记录编辑日志
		EcaProgress eProgress = new EcaProgress();
		eProgress.setContractId(Long.valueOf(contractId));
		eProgress.setCtime(new Date());
		eProgress.setDevice(device);
		eProgress.setIdCard("");
		eProgress.setImei(imei);
		eProgress.setIpAddress(ip);
		eProgress.setOptType(2);   //编辑操作
		eProgress.setStatus(1); 	//未完成状态
		eProgress.setUserId(Long.valueOf(userId));
		eProgress.setUserName(userName);
		if(optHandler != null){
			if(optHandler.intValue() == 1){ //后台操作
				eProgress.setOptHandler(4);   //编辑
			}else if(optHandler.intValue() == 0){ //用户操作
				if(ecaContractTemp.getCarryUserId() != null && ecaContractTemp.getCarryUserId().longValue() == Long.valueOf(userId)){
					eProgress.setOptHandler(3);   //承运方编辑
				}
				if(ecaContractTemp.getShipperUserId() != null && ecaContractTemp.getShipperUserId().longValue() == Long.valueOf(userId)){
					eProgress.setOptHandler(2);   //托运方编辑
				}
			}
			eProgress.setDetail("编辑合同");
		}
		ecaProgressService.add(eProgress);
	}

	/**
	 * 用户编辑合同接口
	 * @param contractId  合同ID
	 * @param userId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/editContractByCustomer.action")
	@ResponseBody
	public ResultMsgBean editContractByCustomer(String userId,String contractId,HttpServletRequest request,
												String argNum,String valOld,String valNew,String device,String imei) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("合同编辑成功");
		try {
			if(!StringUtils.hasLength(userId)){
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("必要参数为空");
				return rm;
			}
			User user = userService.getByUserId(Long.valueOf(userId));
			if(user == null){
				rm.setCode(ReturnCodeConstant.NOT_LOGGED_IN_CODE);
				rm.setMsg("未登录");
				return rm;
			}

			if(!StringUtils.hasLength(contractId)){
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("必要参数为空");
				return rm;
			}
			//TODO 查询合同信息
			EcaContract ecaContractTemp = ecaContractService.getById(Long.valueOf(contractId));
			//TODO 校验状态
			if(ecaContractTemp == null || ecaContractTemp.getStatus() == null || ecaContractTemp.getStatus().intValue() != 1){
				rm.setCode(ReturnCodeConstant.NOT_ALLOW);
				rm.setMsg("此合同当前状态不允许编辑");
				return rm;
			}
			//根据顺序更新字段
			if("1".equals(argNum)){
				if(!StringUtils.hasLength(valNew)){
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("货物重量不能为空");
					return rm;
				}
				if(!trimNull(valOld).equals(trimNull(ecaContractTemp.getSpecification()))){
					rm.setCode(ReturnCodeConstant.NOT_ALLOW);
					rm.setMsg("对方已经编辑合同，请重新刷新");
					return rm;
				}
				ecaContractTemp.setSpecification(valNew);
			}else if("2".equals(argNum)){
				if( !trimNull(valOld).equals(trimNull((ecaContractTemp.getStartDate() != null ? TimeUtil.formatDate(ecaContractTemp.getStartDate()) : null)))){
					rm.setCode(ReturnCodeConstant.NOT_ALLOW);
					rm.setMsg("对方已经编辑合同，请重新刷新");
					return rm;
				}
				if(StringUtils.hasLength(valNew)){
					ecaContractTemp.setStartDate(TimeUtil.parseString(valNew));
				}else{
					ecaContractTemp.setStartDate(null);
				}

			}else if("3".equals(argNum)){
				if(!trimNull(valOld).equals(trimNull((ecaContractTemp.getEndDate() != null ?TimeUtil.formatDate(ecaContractTemp.getEndDate()) :null)))){
					rm.setCode(ReturnCodeConstant.NOT_ALLOW);
					rm.setMsg("对方已经编辑合同，请重新刷新");
					return rm;
				}
				if(StringUtils.hasLength(valNew)) {
					ecaContractTemp.setEndDate(TimeUtil.parseString(valNew));
				}else{
					ecaContractTemp.setEndDate(null);
				}
			}else if("4".equals(argNum)){
				if(!trimNull(valOld).equals(trimNull(ecaContractTemp.getSettleAccountsType()))){
					rm.setCode(ReturnCodeConstant.NOT_ALLOW);
					rm.setMsg("对方已经编辑合同，请重新刷新");
					return rm;
				}
				ecaContractTemp.setSettleAccountsType(valNew);
			}
			ecaContractTemp.setMtime(new Date());
			ecaContractService.update(ecaContractTemp);
			//TODO 记录编辑日志
			saveEcaProcess(contractId,StringUtil.getRealIp(request),userId,user.getTrueName(),0,ecaContractTemp,device,imei);
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private String trimNull(String val){
		if(val != null && StringUtils.hasLength(val) && !val.equals("null")){
			return val;
		}
		return "";
	}

	@RequestMapping(value = "getMyMtTransportList")
	@ResponseBody
	public ResultMsgBean getMyMtTransportList(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
		ResultMsgBean rm = new ResultMsgBean();

		try {

			// 检查属性
			if (checkQueryParameter(queryActionType, queryID, rm)) {
				ListDataBean listDataBean  = transportMtService.getTransportMtBeanList(baseParameter.getUserId(), queryActionType.intValue(),queryID.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(listDataBean);
				rm.setTotalSize(listDataBean == null ? 0l : listDataBean.getData() == null ? 0l : listDataBean.getData().size());

			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private boolean checkQueryParameter(Integer queryActionType, Long queryID, ResultMsgBean rm) {
		if (queryActionType.intValue() < 1 || queryActionType.intValue() > 2) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询类型不正确！");
			return false;
		} else if (queryID.longValue() < 0) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询标识错误，最大、最小ID为空！");
			return false;
		}
		return true;

	}
}
