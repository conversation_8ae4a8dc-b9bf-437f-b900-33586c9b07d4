package com.tyt.transport.controller;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.enumConstant.ReturnCodeEnum;
import com.tyt.model.*;
import com.tyt.plat.enums.PriceSourceEnum;
import com.tyt.plat.service.remote.CarryPriceService;
import com.tyt.plat.utils.NumberConvertUtil;
import com.tyt.plat.vo.other.GoodCarPriceTransportTabAndBIPriceVO;
import com.tyt.plat.vo.remote.CarryPriceReq;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.pricingRoute.service.PricingRouteConfigService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.CarriageFeedbackBean;
import com.tyt.transport.querybean.SameTransportAveragePriceReq;
import com.tyt.transport.querybean.TransportCarryBean;
import com.tyt.transport.service.*;
import com.tyt.user.service.PublicResourceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.transport.querybean.FreighBean;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Controller
@RequestMapping("/plat/freight")
public class FreightController extends BaseController {

	@Resource(name = "freightRecordService")
	private FreightRecordService freightRecordService;

	@Resource(name = "freightRouteService")
	private FreightRouteService freightRouteService;

	@Resource(name = "freightRecordLogService")
	private FreightRecordLogService freightRecordLogService;

	@Resource(name = "freightDetailService")
	private FreightDetailService freightDetailService;

	@Resource(name = "tytConfigService")
	TytConfigService tytConfigService;

	@Resource(name = "freightPriceDetailInfoService")
	private FreightPriceDetailInfoService freightPriceDetailInfoService;

	@Resource(name = "freightPriceSearchLogService")
	private FreightPriceSearchLogService freightPriceSearchLogService;

	@Resource(name = "tytCarriageFeedbackService")
	private TytCarriageFeedbackService tytCarriageFeedbackService;

	@Autowired
	private PricingRouteConfigService pricingRouteConfigService;

	@Autowired
	private CarryPriceService carryPriceService;

	@Autowired
	private PublicResourceService publicResourceService;

	@Autowired
	private BsPublishTransportService bsPublishTransportService;

	public FreightController() {
	}

	@RequestMapping(value = "/record/save")
	@ResponseBody
	public ResultMsgBean save(FreighBean freightBean) {
		long startTime = System.currentTimeMillis();
		logger.info("save freight beign time is: " + startTime);
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
		try {
			if (StringUtils.isEmpty(freightBean.getDestArea())) {
				rm.setMsg("destArea不能为空");
			} else if (StringUtils.isEmpty(freightBean.getDestCity())) {
				rm.setMsg("destCity不能为空");
			} else if (StringUtils.isEmpty(freightBean.getDestProvinc())) {
				rm.setMsg("destProvinc不能为空");
			} else if (StringUtils.isEmpty(freightBean.getProvinceRoad())) {
				rm.setMsg("provinceRoad不能为空");
			} else if (StringUtils.isEmpty(freightBean.getStartArea())) {
				rm.setMsg("startArea不能为空");
			} else if (StringUtils.isEmpty(freightBean.getStartCity())) {
				rm.setMsg("startCity不能为空");
			} else if (StringUtils.isEmpty(freightBean.getStartProvinc())) {
				rm.setMsg("startProvinc不能为空");
			} else if (freightBean.getDestCoordX() == null) {
				rm.setMsg("destCoordX不能为空");
			} else if (freightBean.getDestCoordY() == null) {
				rm.setMsg("destCoordY不能为空");
			} else if (freightBean.getDistance() == null) {
				rm.setMsg("distance不能为空");
			} else if (freightBean.getFreightDistance() == null) {
				rm.setMsg("freightDistance不能为空");
			} else if (freightBean.getStartCoordX() == null) {
				rm.setMsg("startCoordX不能为空");
			} else if (freightBean.getStartCoordY() == null) {
				rm.setMsg("startCoordY不能为空");
			} else {
				freightRecordService.saveFreight(freightBean);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("保存成功 ");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		logger.info("save freight end waste time is: " + (System.currentTimeMillis() - startTime));
		return rm;
	}

	/**
	 *  人工派单运价计算接口
	 * @param startProvinc  出发省份
	 * @param startCity    出发城市
	 * @param startArea    出发区域
	 * @param destProvinc   目的省份
	 * @param destCity      目的城市
	 * @param destArea      目的区域
	 * @param distance      总距离
	 * @param cargoLength   货长度
	 * @param weight        载重
	 * @param taskContent   货物内容
	 * @return
	 */
	@RequestMapping(value = "/freightCalcPrice")
	@ResponseBody
	public ResultMsgBean freightCalcPrice(String startProvinc,
										  String startCity, String startArea, String destProvinc, String destCity, String destArea,String distance,
										  String cargoLength, String weight, String taskContent) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.ERROR);
		logger.info("调用接口------freightCalcPrice");
		//参数校验
		if (StringUtils.isEmpty(startProvinc) ||StringUtils.isEmpty(startCity) ||StringUtils.isEmpty(startArea) || StringUtils.isEmpty(destProvinc)
				|| StringUtils.isEmpty(destCity) || StringUtils.isEmpty(destArea) 	|| StringUtils.isEmpty(weight) || StringUtils.isEmpty(cargoLength) || StringUtils.isEmpty(distance)) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("关键参数缺失");
			return rm;
		}
		if (Double.valueOf(cargoLength) <= 0) {
			rm.setMsg("货物长度必须大于0");
			return rm;
		}
		if (Double.valueOf(weight) <= 0) {
			rm.setMsg("吨位必须大于0");
			return rm;
		}
		if (Double.valueOf(distance) <= 0) {
			rm.setMsg("距离必须大于0");
			return rm;
		}

		long startTime = System.currentTimeMillis();
		logger.info("freightCalcPrice beign time is: " + startTime);


		// 根据出发地目的地获取收费线路详情，获取失败则直接返回0；
		String mapKey = StringUtil.getMapKey(startProvinc,startCity,startArea,destProvinc,destCity,destArea);
		FreightRoute fr = new FreightRoute();
		fr.setMapKey(mapKey);
//		fr.setStatus(Short.parseShort("0"));
		fr  = this.freightRouteService.find(fr);
		if(fr == null || fr.getDistance() == null || fr.getProvinceRoad() == null){
			logger.info("数据库高德收费距离为空,需要重跑的路线：{}-{}-{}---{}-{}-{}",startProvinc,startCity,startArea,destProvinc,destCity,destArea);
			// 发送跑地图数据的MQ
			if(fr == null){
				fr = new FreightRoute();
			}
			fr.setStartProvinc(startProvinc);
			fr.setStartCity(startCity);
			fr.setStartArea(startArea);
			fr.setDestProvinc(destProvinc);
			fr.setDestCity(destCity);
			fr.setDestArea(destArea);
			freightDetailService.sendMqMessage2MqByStart2Dest(fr);
			rm.setMsg("货物详情获取失败，未从高德获取数据");
			return rm;
		}
		String lineDetail = fr.getProvinceRoad();
		String totalDistance = String.valueOf(fr.getDistance());
		if(StringUtils.isEmpty(lineDetail)|| StringUtils.isEmpty(totalDistance)|| "null".equals(totalDistance) ){
			rm.setMsg("货物详情获取失败，未从高德获取数据");
			return rm;
		}
		if(fr.getDistance().intValue() <= 0){
			rm.setMsg("货物详情数据库获取数据距离为0");
			return rm;
		}
		// 判断传入的距离是否多次错误，多次错误则直接拒绝12H
		Integer configMaxErrorCount = tytConfigService.getIntValue("freight_distance_diff_error_count",5);
		String pageDistanceErrorCountCachKey = "plat_page_distance_error_count"+startProvinc+startCity+startArea+destProvinc+destCity+destArea;
		String errorCount = RedisUtil.get(pageDistanceErrorCountCachKey);
		if(StringUtils.isEmpty(errorCount)){
			errorCount = "0";
		}
		if(configMaxErrorCount.intValue() != 0 && configMaxErrorCount.toString().equals(errorCount)){
			rm.setMsg("距离查询多次不匹配，计算失败");
			return rm;
		}
		//还原正常数值后进行计算
		Double dbDistance = HighwayCostCalcUtil.calcDivide(totalDistance,"100");
		// 比较传入的和查询出的距离，超过范围后重跑Map数据MQ
		Double tempDistance = freightDetailService.updateDistanceByDbDistance(Double.parseDouble(distance),dbDistance,fr);
		if(tempDistance == null){
			Integer errorCountInt = Integer.parseInt(errorCount);
			errorCountInt += 1;
			RedisUtil.set(pageDistanceErrorCountCachKey,errorCountInt.toString(), 5);
			rm.setMsg("距离偏差大于限定值，计算失败");
			return rm;
		}
		totalDistance = String.valueOf(tempDistance);
		Float cargoLengthFloat = Float.valueOf(cargoLength);
		Float weightFloat = Float.valueOf(weight);

		TytFreightDetailInfo freightDetail = new TytFreightDetailInfo();
		freightDetail.setCargoLength(cargoLengthFloat);
		freightDetail.setStartPoint(startProvinc+startCity+startArea);
		freightDetail.setDestPoint(destProvinc+destCity+destArea);
		freightDetail.setDistance(Float.valueOf(totalDistance));
		freightDetail.setProvinceRoad(lineDetail);
		freightDetail.setTaskContent(taskContent);
		freightDetail.setTonne(weightFloat);

		FreightPriceDetailInfo freightPriceDetailInfo = new FreightPriceDetailInfo();

		// 先计算燃油费用，因为需要获取车辆自重，累加后计算高速费用
		// 计算燃油费用、固定成本
		freightDetail = freightDetailService.calcBaseOilCost(freightDetail, Float.toString(cargoLengthFloat), Float.toString(weightFloat), totalDistance);
		if (freightDetail == null) {
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("计算燃油费、固定成本失败");
			return rm;
		}

		if (freightDetail.getSelfTonne() == null) {
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("车辆自重获取失败");
			return rm;
		}
		// 高速费需要车货总重来计算价格，自重加上货物重量
		Float totalTonne = HighwayCostCalcUtil.calcInCrease(String.valueOf(freightDetail.getTonne()), String.valueOf(freightDetail.getSelfTonne())).floatValue();
		if (totalTonne == null || totalTonne <= 0) {
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("车辆总重计算失败");
			return rm;
		}
		// 如果总重小于15吨，则按15吨来走
		if(totalTonne.floatValue() < 15){
			totalTonne = 15.01f;
		}

		// 计算高速费用
		freightDetail = freightDetailService.calcHighwayCost(freightDetail, totalTonne, totalDistance, lineDetail, 1);

		if (freightDetail == null) {
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("计算高速运费失败");
			return rm;
		}

		freightPriceDetailInfo.setCtime(new Date());
		freightPriceDetailInfo.setMtime(new Date());
		freightPriceDetailInfo.setCargoLength(cargoLengthFloat.doubleValue());
		freightPriceDetailInfo.setStartPoint(freightDetail.getStartPoint());
		freightPriceDetailInfo.setDestPoint(freightDetail.getDestPoint());
		freightPriceDetailInfo.setDistance(Double.parseDouble(distance));
		freightPriceDetailInfo.setTaskContent(taskContent);
		freightPriceDetailInfo.setTonne(weightFloat.doubleValue());
		freightPriceDetailInfo.setSelfTonne(freightDetail.getSelfTonne().doubleValue());
		freightPriceDetailInfo.setBaseCost(HighwayCostCalcUtil.getNumberTwoPrecision(freightDetail.getBaseCost().doubleValue()));
		freightPriceDetailInfo.setDaysNum(freightDetail.getDaysNum());
		freightPriceDetailInfo.setOilCost(HighwayCostCalcUtil.getNumberTwoPrecision(freightDetail.getOilCost().doubleValue()));
		freightPriceDetailInfo.setFixedCost(HighwayCostCalcUtil.getNumberTwoPrecision(freightDetail.getTotalCost().doubleValue()));
		freightPriceDetailInfo.setHighwayCost(HighwayCostCalcUtil.getNumberTwoPrecision(freightDetail.getHighwayCost().doubleValue()));

		// 计算基本、指导利润率
		if(freightPriceDetailInfo == null || freightPriceDetailInfo.getFixedCost() == null
				|| freightPriceDetailInfo.getCargoLength() == null
				|| freightPriceDetailInfo.getTonne() == null
				|| freightPriceDetailInfo.getDistance() == null
				|| freightPriceDetailInfo.getDistance().doubleValue() <= 0
				|| freightPriceDetailInfo.getTonne().doubleValue() <= 0
				|| freightPriceDetailInfo.getCargoLength().doubleValue() <= 0
				|| freightPriceDetailInfo.getFixedCost().doubleValue() <= 0){
			if(freightPriceDetailInfo != null){
				logger.info("计算最低利润率失败，FixedCost:{},CargoLength:{},Tonne:{},Distance:{}",freightPriceDetailInfo.getFixedCost() ,freightPriceDetailInfo.getCargoLength()
				,freightPriceDetailInfo.getTonne(),freightPriceDetailInfo.getDistance() );
			}
			rm.setMsg("计算最低利润率失败，参数缺失或者错误");
			rm.setCode(ReturnCodeConstant.ERROR);
			return rm;
		}
		//基本、指导利润率逻辑
		freightDetailService.getMinAndGuidingProfitRates(startProvinc,startCity,destProvinc,destCity,freightPriceDetailInfo);
		Double minProfitRates = freightPriceDetailInfo.getMinProfitRate();
		Double guidingProfitRates = freightPriceDetailInfo.getGuidingRate();
		if(minProfitRates == null || minProfitRates.doubleValue() <= 0){
			logger.info("【ExprssionException】最小利润率计算失败");
			rm.setMsg("计算最低利润率失败");
			rm.setCode(ReturnCodeConstant.ERROR);
			return rm;
		}
		if(guidingProfitRates == null || guidingProfitRates.doubleValue() <= 0){
			logger.info("【ExprssionException】指导利润率计算失败");
			rm.setMsg("计算指导利润率失败");
			rm.setCode(ReturnCodeConstant.ERROR);
			return rm;
		}
		//如果指导低于最低，则取最低为指导
		if(guidingProfitRates.doubleValue() < minProfitRates.doubleValue()){
			guidingProfitRates = minProfitRates;
		}

		// 计算最低、指导运价
		Double minPrice = calcMinPrice(String.valueOf(freightPriceDetailInfo.getFixedCost()),String.valueOf(minProfitRates));
		Double guidingPrice = calcGuidingPrice(String.valueOf(freightPriceDetailInfo.getFixedCost()),String.valueOf(guidingProfitRates));
		if(minPrice == null || minPrice.doubleValue() <= 0 || guidingPrice == null || guidingPrice.doubleValue() <= 0){
			logger.info("【ExprssionException】价格计算失败");
			rm.setMsg("价格计算失败");
			rm.setCode(ReturnCodeConstant.ERROR);
			return rm;
		}
		freightPriceDetailInfo.setMinPrice(minPrice);
		freightPriceDetailInfo.setGuidingPrice(guidingPrice);
		// 计算预计利润率
		Integer guidingRateInt = HighwayCostCalcUtil.calcMultiply(String.valueOf(guidingProfitRates),"100").intValue();
		freightPriceDetailInfo.setEstimateProfitRate(guidingRateInt);
		// 计算预计利润金额
		Double estimateProfitPrice = HighwayCostCalcUtil.calcSubtract(String.valueOf(guidingPrice),String.valueOf(halfUpByConfig(freightPriceDetailInfo.getFixedCost())));
		freightPriceDetailInfo.setEstimateProfitPrice(estimateProfitPrice);
		// 计算最低、指导每公里费用
		Double minPerKmPrice = HighwayCostCalcUtil.getNumberOnePrecision(HighwayCostCalcUtil.calcDivide(String.valueOf(minPrice),distance));
		Double guidingPerKmPrice = HighwayCostCalcUtil.getNumberOnePrecision(HighwayCostCalcUtil.calcDivide(String.valueOf(guidingPrice),distance));
		freightPriceDetailInfo.setMinPerKmPrice(minPerKmPrice);
		freightPriceDetailInfo.setGuidingPerKmPrice(guidingPerKmPrice);
		// 保存计算记录
		freightPriceDetailInfoService.add(freightPriceDetailInfo);
		freightPriceDetailInfo.setStartPoint("");
		freightPriceDetailInfo.setDestPoint("");
		freightPriceDetailInfo.setTaskContent("");
		rm.setData(freightPriceDetailInfo);
		rm.setCode(ReturnCodeConstant.OK);
		logger.info("freightCalcPrice end waste time is: " + (System.currentTimeMillis() - startTime));
		return rm;
	}

	private Double calcMinPrice(String baseCost,String minProfitRate){
		// 最低运费=成本/（1-基本利润率）
		Double minPrice = HighwayCostCalcUtil.calcSubtract("1",minProfitRate);
		minPrice = HighwayCostCalcUtil.calcDivide(baseCost,String.valueOf(minPrice));
		return halfUpByConfig(minPrice);
	}

	private Double calcGuidingPrice(String baseCost,String guidingProfitRate){
		// 建议运费=成本/（1-推荐利润率）
		Double guidingPrice = HighwayCostCalcUtil.calcSubtract("1",guidingProfitRate);
		guidingPrice = HighwayCostCalcUtil.calcDivide(baseCost,String.valueOf(guidingPrice));
		return halfUpByConfig(guidingPrice);
	}

	private Double halfUpByConfig(Double price){
		if(price == null){
			return null;
		}
		Double tempPrice = null;
		String priceStr = String.valueOf(Math.floor(price));
		if(price.doubleValue()  > 0 && price.doubleValue() < 500){
			return 500d;
		}else if(price.doubleValue() >= 500 && price.doubleValue() < 1000){
			//取百位
			priceStr = priceStr.substring(0,1)+"00";
			tempPrice = Double.parseDouble(priceStr);
		}else if(price.doubleValue() >= 1000 && price.doubleValue() < 10000){
			//取百位
			priceStr = priceStr.substring(0,2)+"00";
			tempPrice = Double.parseDouble(priceStr);
		}else if(price.doubleValue() >= 10000 && price.doubleValue() < 100000){
			//取千位
			priceStr = priceStr.substring(0,2)+"000";
			tempPrice = Double.parseDouble(priceStr);
		}else if(price.doubleValue() >= 100000){
			//取万位
			BigDecimal bigDecimal = new BigDecimal(String.valueOf(price));
			priceStr =  bigDecimal.toString();
			if(priceStr.indexOf(".") > -1){
				priceStr = priceStr.substring(0,priceStr.indexOf("."));
			}
			priceStr = priceStr.substring(0,priceStr.length()-4)+"0000";
			tempPrice = Double.parseDouble(priceStr);
		}else{
			//其他情况不处理，返回null
		}
		return tempPrice;
	}


	/**
	 *  首页工具条运价计算接口
	 * @param startProvinc 出发地
	 * @param startCity
	 * @param startArea
	 * @param destProvinc  目的地
	 * @param destCity
	 * @param destArea
	 * @param cargoLength   长度
	 * @param weight     重量
	 * @param baseCost   固定成本
	 * @param oilPrice   柴油价格
	 * @param perHundredOilWear   百公里油耗
	 * @return
	 */
	@RequestMapping(value = "/freightDetail.action")
	@ResponseBody
	public ResultMsgBean freightDetail(String startProvinc,
										  String startCity, String startArea, String destProvinc, String destCity, String destArea,
										  String cargoLength, String weight, String baseCost,String oilPrice,String perHundredOilWear) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.ERROR);
		logger.info("调用接口------freightDetail");
		//参数校验
		if (StringUtils.isEmpty(startProvinc) ||StringUtils.isEmpty(startCity) ||StringUtils.isEmpty(startArea) || StringUtils.isEmpty(destProvinc)
				|| StringUtils.isEmpty(destCity) || StringUtils.isEmpty(destArea) 	|| StringUtils.isEmpty(weight) || StringUtils.isEmpty(cargoLength)
				|| StringUtils.isEmpty(baseCost) || StringUtils.isEmpty(oilPrice) 	|| StringUtils.isEmpty(perHundredOilWear)) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("关键参数缺失");
			return rm;
		}
		if (Double.valueOf(cargoLength) <= 0) {
			rm.setMsg("货物长度必须大于0");
			return rm;
		}
		if (Double.valueOf(weight) <= 0) {
			rm.setMsg("吨位必须大于0");
			return rm;
		}

		if (Double.valueOf(baseCost) <= 0 || Double.valueOf(oilPrice) <= 0 || Double.valueOf(perHundredOilWear) <= 0) {
			rm.setMsg("精确查询参数必须大于0");
			return rm;
		}



		long startTime = System.currentTimeMillis();
		logger.info("freightCalcPrice beign time is: " + startTime);


		// 根据出发地目的地获取收费线路详情，获取失败则直接返回0；
		String mapKey = StringUtil.getMapKey(startProvinc,startCity,startArea,destProvinc,destCity,destArea);
		FreightRoute fr = new FreightRoute();
		fr.setMapKey(mapKey);
//		fr.setStatus(Short.parseShort("0"));
		fr  = this.freightRouteService.find(fr);
		if(fr == null || fr.getDistance() == null || fr.getProvinceRoad() == null){
			logger.info("数据库高德收费距离为空,需要重跑的路线：{}-{}-{}---{}-{}-{}",startProvinc,startCity,startArea,destProvinc,destCity,destArea);
			// 发送跑地图数据的MQ
			if(fr == null){
				fr = new FreightRoute();
			}
			fr.setStartProvinc(startProvinc);
			fr.setStartCity(startCity);
			fr.setStartArea(startArea);
			fr.setDestProvinc(destProvinc);
			fr.setDestCity(destCity);
			fr.setDestArea(destArea);
			freightDetailService.sendMqMessage2MqByStart2Dest(fr);
			rm.setMsg("货物详情获取失败，未从高德获取数据");
 			return rm;
		}
		String lineDetail = fr.getProvinceRoad();
		String totalDistance = String.valueOf(fr.getDistance());
		if(StringUtils.isEmpty(lineDetail)|| StringUtils.isEmpty(totalDistance)|| "null".equals(totalDistance) ){
			rm.setMsg("货物详情获取失败，未从高德获取数据");
			return rm;
		}
		if(fr.getDistance().intValue() <= 0){
			rm.setMsg("货物详情数据库获取数据距离为0");
			return rm;
		}

		//还原正常数值后进行计算
		Double dbDistance = HighwayCostCalcUtil.calcDivide(totalDistance,"100");
		totalDistance = String.valueOf(dbDistance);
		Float cargoLengthFloat = Float.valueOf(cargoLength);
		Float weightFloat = Float.valueOf(weight);

		//最终查询返回结果
		FreightPriceSearchLog fpdui = new FreightPriceSearchLog();
		fpdui.setCargoLength(Double.valueOf(cargoLength));
		fpdui.setStartPoint(startProvinc+startCity+startArea);
		fpdui.setDestPoint(destProvinc+destCity+destArea);
		fpdui.setDistance(Double.valueOf(totalDistance));
		fpdui.setTonne(Double.valueOf(weight));


		TytFreightDetailInfo freightDetail = new TytFreightDetailInfo();
		freightDetail.setCargoLength(cargoLengthFloat);
		freightDetail.setDistance(Float.valueOf(totalDistance));
		freightDetail.setProvinceRoad(lineDetail);
		freightDetail.setTonne(weightFloat);

		//复用之前的方法，建立此对象
		FreightPriceDetailInfo freightPriceDetailInfo = new FreightPriceDetailInfo();
		//查运价日志记录

		fpdui.setUserBaseCost(Double.valueOf(baseCost));
		fpdui.setUserOilPrice(Double.valueOf(oilPrice));
		fpdui.setUserOilWear(Double.valueOf(perHundredOilWear));
		//TODO 直接按照输入的来计算运价
		freightDetailService.getBaseOilCost(fpdui,cargoLength,weight,totalDistance);

		freightDetail.setBaseCost(fpdui.getBaseCost().floatValue());
		freightDetail.setOilCost(fpdui.getOilCost().floatValue());
		freightDetail.setSelfTonne(fpdui.getSelfTonne().floatValue());

		// 先计算燃油费用，因为需要获取车辆自重，累加后计算高速费用
		// 计算燃油费用、固定成本
//		freightDetail = freightDetailService.calcBaseOilCost(freightDetail, Float.toString(cargoLengthFloat), Float.toString(weightFloat), totalDistance);
//		if (freightDetail == null) {
//			rm.setCode(ReturnCodeConstant.ERROR);
//			rm.setMsg("计算燃油费、固定成本失败");
//			return rm;
//		}

		if (freightDetail.getSelfTonne() == null) {
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("车辆自重获取失败");
			return rm;
		}
		// 高速费需要车货总重来计算价格，自重加上货物重量
		Float totalTonne = HighwayCostCalcUtil.calcInCrease(String.valueOf(freightDetail.getTonne()), String.valueOf(freightDetail.getSelfTonne())).floatValue();
		if (totalTonne == null || totalTonne <= 0) {
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("车辆总重计算失败");
			return rm;
		}
		// 如果总重小于15吨，则按15吨来走
		if(totalTonne.floatValue() < 15){
			totalTonne = 15.01f;
		}

		// 计算高速费用
		freightDetail = freightDetailService.calcHighwayCost(freightDetail, totalTonne, totalDistance, lineDetail, 1);

		if (freightDetail == null) {
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("计算高速运费失败");
			return rm;
		}
		//获取高速费用
		fpdui.setHighwayCost(freightDetail.getHighwayCost().doubleValue());

		fpdui.setCtime(new Date());
		fpdui.setMtime(new Date());
		fpdui.setFixedCost(freightDetail.getTotalCost().doubleValue());

		freightPriceDetailInfo.setCargoLength(cargoLengthFloat.doubleValue());
		freightPriceDetailInfo.setDistance(freightDetail.getDistance().doubleValue());
		freightPriceDetailInfo.setTonne(weightFloat.doubleValue());
		freightPriceDetailInfo.setBaseCost(HighwayCostCalcUtil.getNumberTwoPrecision(freightDetail.getBaseCost().doubleValue()));
		freightPriceDetailInfo.setOilCost(HighwayCostCalcUtil.getNumberTwoPrecision(freightDetail.getOilCost().doubleValue()));
		freightPriceDetailInfo.setFixedCost(HighwayCostCalcUtil.getNumberTwoPrecision(freightDetail.getTotalCost().doubleValue()));
		freightPriceDetailInfo.setHighwayCost(HighwayCostCalcUtil.getNumberTwoPrecision(freightDetail.getHighwayCost().doubleValue()));

		// 计算基本、指导利润率
		if(freightPriceDetailInfo == null || freightPriceDetailInfo.getFixedCost() == null
				|| freightPriceDetailInfo.getCargoLength() == null
				|| freightPriceDetailInfo.getTonne() == null
				|| freightPriceDetailInfo.getDistance() == null
				|| freightPriceDetailInfo.getDistance().doubleValue() <= 0
				|| freightPriceDetailInfo.getTonne().doubleValue() <= 0
				|| freightPriceDetailInfo.getCargoLength().doubleValue() <= 0
				|| freightPriceDetailInfo.getFixedCost().doubleValue() <= 0){
			if(freightPriceDetailInfo != null){
				logger.info("计算最低利润率失败，FixedCost:{},CargoLength:{},Tonne:{},Distance:{}",freightPriceDetailInfo.getFixedCost() ,freightPriceDetailInfo.getCargoLength()
						,freightPriceDetailInfo.getTonne(),freightPriceDetailInfo.getDistance() );
			}
			rm.setMsg("计算最低利润率失败，参数缺失或者错误");
			rm.setCode(ReturnCodeConstant.ERROR);
			return rm;
		}
		//基本、指导利润率逻辑
		freightDetailService.getMinAndGuidingProfitRates(startProvinc,startCity,destProvinc,destCity,freightPriceDetailInfo);
		Double minProfitRates = freightPriceDetailInfo.getMinProfitRate();
		Double guidingProfitRates = freightPriceDetailInfo.getGuidingRate();
		if(minProfitRates == null || minProfitRates.doubleValue() <= 0){
			logger.info("【ExprssionException】最小利润率计算失败");
			rm.setMsg("计算最低利润率失败");
			rm.setCode(ReturnCodeConstant.ERROR);
			return rm;
		}
		if(guidingProfitRates == null || guidingProfitRates.doubleValue() <= 0){
			logger.info("【ExprssionException】指导利润率计算失败");
			rm.setMsg("计算指导利润率失败");
			rm.setCode(ReturnCodeConstant.ERROR);
			return rm;
		}
		//如果指导低于最低，则取最低为指导
		if(guidingProfitRates.doubleValue() < minProfitRates.doubleValue()){
			guidingProfitRates = minProfitRates;
		}

		// 计算最低、指导运价
		Double minPrice = calcMinPrice(String.valueOf(freightPriceDetailInfo.getFixedCost()),String.valueOf(minProfitRates));
		Double guidingPrice = HighwayCostCalcUtil.calcSubtract("1",String.valueOf(guidingProfitRates));
		guidingPrice = HighwayCostCalcUtil.calcDivide(String.valueOf(freightPriceDetailInfo.getFixedCost()),String.valueOf(guidingPrice));

		if(minPrice == null || minPrice.doubleValue() <= 0 || guidingPrice == null || guidingPrice.doubleValue() <= 0){
			logger.info("【ExprssionException】价格计算失败");
			rm.setMsg("价格计算失败");
			rm.setCode(ReturnCodeConstant.ERROR);
			return rm;
		}

		//展示价格系数，用于不展示真实运价
		String guidingPriceRatio = tytConfigService.getStringValue("freight_guiding_price_mix_ratio","1.0");
		if(guidingPriceRatio != null && !"1.0".equals(guidingPriceRatio)){
			guidingPrice = HighwayCostCalcUtil.calcMultiply(String.valueOf(guidingPrice),guidingPriceRatio);
		}
		guidingPrice = halfUpByConfig(guidingPrice);
//		freightPriceDetailInfo.setMinPrice(minPrice);
//		freightPriceDetailInfo.setGuidingPrice(guidingPrice);
		fpdui.setMinProfitRate(freightPriceDetailInfo.getMinProfitRate());
		fpdui.setGuidingRate(freightPriceDetailInfo.getGuidingRate());
		fpdui.setMinPrice(minPrice);
		fpdui.setGuidingPrice(guidingPrice);
		freightPriceDetailInfo = null;
		freightDetail = null;
		// 保存计算记录,并返回
		freightPriceSearchLogService.add(fpdui);
		rm.setData(fpdui);
		rm.setCode(ReturnCodeConstant.OK);
		logger.info("freightDetail end waste time is: " + (System.currentTimeMillis() - startTime));
		return rm;
	}

	/**
	  * <AUTHOR> Lion
	  * @Description 发货页面运费参考价
	  * @Param [transportCarryBean]
	  * @return com.tyt.model.ResultMsgBean
	  * @Date 2022/7/29 13:04
	  */
	@RequestMapping(value = "/getCarryPrice")
	@ResponseBody
	public ResultMsgBean getCarryPrice(TransportCarryBean transportCarryBean){

		CarryPriceVo carryPriceVo = new CarryPriceVo();
		ResultMsgBean rm = ResultMsgBean.successResponse(carryPriceVo);
		// 博杰说拼车不应该限制建议价，先让这个接口不返回值
//		// 优车的建议价单独控制
//		if(transportCarryBean.getExcellentGoods() != null && transportCarryBean.getExcellentGoods() == 1){
//			Integer excellentGoodsCarryPriceSwitch = tytConfigService.getIntValue("EXCELLENT_GOODS_CARRY_PRICE_SWITCH",1);
//			if(excellentGoodsCarryPriceSwitch != 1){
//				return rm;
//			}
//		}
//
//		CarryPriceReq priceReq = new CarryPriceReq();
//		if (bsPublishTransportService.checkParameter(transportCarryBean)) {
//			CarryPriceReq carryPriceReq = createCarryPriceReq(transportCarryBean);
//			BeanUtils.copyProperties(carryPriceReq,priceReq);
//			//获取BI数据
//			carryPriceVo = reqCarryPrice(priceReq);
//			rm.setData(carryPriceVo);
//		}
		return rm;
	}

	/**
	 * 是否展示优车运价货源卡片
	 * @param parameter parameter
	 * @return ResultMsgBean 对象的json格式文件
	 */
	@RequestMapping(value = "/isShowGoodCarPriceTransportTab")
	@ResponseBody
	public ResultMsgBean isShowGoodCarPriceTransportTab(BaseParameter parameter, TransportCarryBean transportCarryBean) {
		return bsPublishTransportService.isShowGoodCarPriceTransportTab(parameter, transportCarryBean);
	}

	/**
	 * 相似货源平均成交价
	 * @param sameTransportAveragePriceReq sameTransportAveragePriceReq
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = {"/getSameTransportAveragePrice", "/getSameTransportAveragePrice.action"})
	@ResponseBody
	public ResultMsgBean getSameTransportAveragePrice(SameTransportAveragePriceReq sameTransportAveragePriceReq) {
		return freightDetailService.getSameTransportAveragePrice(sameTransportAveragePriceReq);
	}


	/**
	  * <AUTHOR> Lion
	  * @Description 运价用户反馈接口
	  * @Param [carriageFeedbackBean]
	  * @return com.tyt.model.ResultMsgBean
	  * @Date 2022/9/15 16:43
	  */
	@RequestMapping(value = "/saveCarryPrice")
	@ResponseBody
	public ResultMsgBean getCarryPrice(CarriageFeedbackBean carriageFeedbackBean){
		ResultMsgBean rm = new ResultMsgBean();
		try {
			logger.info("参数：{}", JSON.toJSONString(carriageFeedbackBean));
			//校验参数
			if( !checkFeedbackPriceParam(carriageFeedbackBean, rm) ){
				return rm;
			}
			tytCarriageFeedbackService.saveCarriageFeedback(carriageFeedbackBean);
			rm.setCode(ReturnCodeEnum.CODE_200.getCode());
			rm.setMsg("提交反馈成功");
			return rm;
		} catch (Exception e) {
			logger.error("运价用户反馈接口失败，请求参数：{}", JSON.toJSONString(carriageFeedbackBean),e);
			e.printStackTrace();
			return new ResultMsgBean(ResultMsgBean.ERROR,"服务器错误");
		}
	}

	/**
	  * <AUTHOR> Lion
	  * @Description 参数校验
	  * @Param [carriageFeedbackBean, rm]
	  * @return boolean
	  * @Date 2022/9/15 16:43
	  */
	private boolean checkFeedbackPriceParam(CarriageFeedbackBean carriageFeedbackBean, ResultMsgBean rm) {

		if(StringUtils.isBlank(carriageFeedbackBean.getStartProvince()) || StringUtils.isBlank(carriageFeedbackBean.getStartCity()) || StringUtils.isBlank(carriageFeedbackBean.getStartArea())
		|| StringUtils.isBlank(carriageFeedbackBean.getDestProvince()) || StringUtils.isBlank(carriageFeedbackBean.getDestCity()) || StringUtils.isBlank(carriageFeedbackBean.getDestArea())
		|| StringUtils.isBlank(carriageFeedbackBean.getGoodsName()) || null == carriageFeedbackBean.getGoodsWeight()){
			rm.setCode(ReturnCodeEnum.CODE_402.getCode());
			rm.setMsg(ReturnCodeEnum.CODE_402.getMsg());
			return false;
		}
		if(StringUtils.isBlank(carriageFeedbackBean.getCellPhone()) || null == carriageFeedbackBean.getSource()){
			rm.setCode(ReturnCodeEnum.CODE_402.getCode());
			rm.setMsg(ReturnCodeEnum.CODE_402.getMsg());
			return false;
		}
		if( null == carriageFeedbackBean.getPrice() ){
			rm.setCode(ReturnCodeEnum.FEEDPRICE_PRICE_EMPTY_ERROR.getCode());
			rm.setMsg(ReturnCodeEnum.FEEDPRICE_PRICE_EMPTY_ERROR.getMsg());
			return false;
		}
		if(StringUtils.isBlank(carriageFeedbackBean.getRemark())){
			rm.setCode(ReturnCodeEnum.FEEDPRICE_VIEW_EMPTY_ERROR.getCode());
			rm.setMsg(ReturnCodeEnum.FEEDPRICE_VIEW_EMPTY_ERROR.getMsg());
			return false;
		}
		if(carriageFeedbackBean.getRemark().length() < 10){
			rm.setCode(ReturnCodeEnum.FEEDPRICE_VIEW_MIN_ERROR.getCode());
			rm.setMsg(ReturnCodeEnum.FEEDPRICE_VIEW_MIN_ERROR.getMsg());
			return false;
		}
		carriageFeedbackBean.setStartCity(carriageFeedbackBean.getStartCity().equals(carriageFeedbackBean.getStartProvince())?carriageFeedbackBean.getStartCity()+"市":carriageFeedbackBean.getStartCity());
		carriageFeedbackBean.setDestCity(carriageFeedbackBean.getDestCity().equals(carriageFeedbackBean.getDestProvince())?carriageFeedbackBean.getDestCity()+"市":carriageFeedbackBean.getDestCity());
		return true;
	}

	/**
	  * <AUTHOR> Lion
	  * @Description 过滤BI接口报错
	  * @Param [priceReq]
	  * @return com.tyt.plat.vo.remote.CarryPriceVo
	  * @Date 2022/8/1 17:35
	  */
	private CarryPriceVo reqCarryPrice(CarryPriceReq priceReq) {

//		int minPrice = 100;
//		int maxPrice = 999999;
		CarryPriceVo carryPrice = null;
		try {
			carryPrice = carryPriceService.getCarryPrice(priceReq);
		} catch (Exception e) {
			logger.debug("request_getCarryPrice_error : ", e);
		}
		if(carryPrice == null){
			carryPrice = new CarryPriceVo();
//			carryPriceVo.setThMinPrice(minPrice);
//			carryPriceVo.setThMaxPrice(maxPrice);
//			return carryPriceVo;
		}
		logger.info("getCarryPrice_priceResult_返回参数: {}", JSON.toJSONString(carryPrice));
		return carryPrice;
	}

	/**
	 * <AUTHOR> Lion
	 * @Description 封装CarryPriceReq
	 * @Param [transportBean]
	 * @return com.tyt.plat.vo.remote.CarryPriceReq
	 * @Date 2022/7/28 17:03
	 */
	private CarryPriceReq createCarryPriceReq(TransportCarryBean transportBean){
		CarryPriceReq carryPriceReq = new CarryPriceReq();
		carryPriceReq.setStartProvince(transportBean.getStartProvince());
		carryPriceReq.setStartCity(transportBean.getStartCity());
		carryPriceReq.setStartArea(transportBean.getStartArea());
		carryPriceReq.setDestProvince(transportBean.getDestProvince());
		carryPriceReq.setDestCity(transportBean.getDestCity());
		carryPriceReq.setDestArea(transportBean.getDestArea());
		carryPriceReq.setGoodsName(transportBean.getGoodsName());
		carryPriceReq.setGoodsWeight(NumberConvertUtil.strToDouble(transportBean.getGoodsWeight()));
		carryPriceReq.setGoodsLength(NumberConvertUtil.strToDouble(transportBean.getGoodsLength()));
		carryPriceReq.setGoodsWide(NumberConvertUtil.strToDouble(transportBean.getGoodsWide()));
		carryPriceReq.setGoodsHigh(NumberConvertUtil.strToDouble(transportBean.getGoodsHigh()));

		carryPriceReq.setUserId(transportBean.getUserId());
		carryPriceReq.setDistance(transportBean.getDistance());
		if (transportBean.getExcellentGoods() != null && transportBean.getExcellentGoods() == 1) {
			carryPriceReq.setSource(PriceSourceEnum.excellentGoods.getSource());
		}else{
			carryPriceReq.setSource(PriceSourceEnum.reference.getSource());
		}
		return carryPriceReq;
	}

}
