package com.tyt.transport.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.mapper.base.TytHotUpdateFileConfigMapper;
import com.tyt.transport.bean.TytHotUpdateFileConfig;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/plat/appHotUpdate")
@Slf4j
public class AppHotUpdateController {

    @Autowired
    private TytHotUpdateFileConfigMapper tytHotUpdateFileConfigMapper;

    @Autowired
    private TytConfigService tytConfigService;

    /**
     * 获取APP热更新配置
     * @return List<TytHotUpdateFileConfig>
     */
    @PostMapping("/getAppHotUpdateValidAndPublishByReleaseVersion")
    public ResultMsgBean getAppHotUpdateValidAndPublishByReleaseVersion(BaseParameter base) {
        if (StringUtils.isBlank(base.getClientVersion()) || base.getClientSign() == null
                || ((Integer.parseInt(base.getClientSign()) != Constant.ClientSignEnum.IOS_CAR.code) && Integer.parseInt(base.getClientSign()) != Constant.ClientSignEnum.IOS_GOODS.code)) {
            return ResultMsgBean.successResponse();
        }

        int clentType = 1;
        if (Integer.parseInt(base.getClientSign()) == Constant.ClientSignEnum.IOS_GOODS.code) {
            clentType = 2;
        }

        TytHotUpdateFileConfig appHotUpdateValidByReleaseVersion = tytHotUpdateFileConfigMapper.getAppHotUpdateValidAndPublishByReleaseVersion(base.getClientVersion(), clentType);
        if (appHotUpdateValidByReleaseVersion != null && StringUtils.isNotBlank(appHotUpdateValidByReleaseVersion.getFileUrl())) {
            appHotUpdateValidByReleaseVersion.setFileUrl("https://" + tytConfigService.getStringValue("aliyunOSS_bucketName", "tyt-images") + "." + tytConfigService.getStringValue("aliyunOSS_endpoint", "oss-cn-beijing.aliyuncs.com") + appHotUpdateValidByReleaseVersion.getFileUrl());
        }
        return ResultMsgBean.successResponse(appHotUpdateValidByReleaseVersion);
    }

}
