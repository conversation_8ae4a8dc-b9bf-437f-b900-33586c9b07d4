package com.tyt.transport.controller;

import java.io.File;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportQueryCondition;
import com.tyt.transport.service.TransportQueryConditionService;
import com.tyt.util.CreateFileUtil;
import com.tyt.util.FileUtil;
@Controller
@RequestMapping("plat/transport/condition")
public class TransportQueryConditionController extends BaseController{
	
	@Resource(name = "transportQueryConditionService")
	private TransportQueryConditionService transportQueryConditionService;

	@RequestMapping(value="/file/write")
	@ResponseBody
	public void saveCondition(@RequestParam MultipartFile file,
			String cellPhone,String token,
			HttpServletRequest request, HttpServletResponse response){
		if (!validateToken(cellPhone, token, request, response)) {
			logger.info("confition/file/write token error."+cellPhone+","+token);
			return;
		}
		try{
			logger.info("transport/file/write "+cellPhone+"开始上传文件.");
			if (!file.isEmpty()) {  
				CreateFileUtil.createDir(AppConfig.getProperty("transport.condition.savepath"));
	            String path = AppConfig.getProperty("transport.condition.savepath") + file.getOriginalFilename();  
	            File localFile = new File(path);  
	            file.transferTo(localFile); 
	            }
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK,"1");
			printJSON(request, response, msgBean);
			return;
		}catch(Exception e){
			logger.info("transport/file/write"+cellPhone+"上传文件异常");
			e.printStackTrace();
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,e.toString());
			printJSON(request, response, msgBean);
			return;
		}finally{
			logger.info("transport/file/write结束");
		}
		    
	    }
	
	
	@RequestMapping(value="/file/read")
	@ResponseBody
	public void dealAllFile(String cellPhone,String token,HttpServletRequest request, HttpServletResponse response){
		try{
			if (!validateToken(cellPhone, token, request, response)) {
				logger.info("confition/file/read token error.");
				return;
			}
			logger.info("condition/file/read读取文件夹开始");
			List<String> filePathList=FileUtil.readFolder(AppConfig.getProperty("transport.condition.savepath"));
			logger.info("condition/file/read读取到的文件名称集合大小为"+filePathList.size());
			for(String filePath:filePathList){
		    	List<TransportQueryCondition> conditions=FileUtil.readFileByLines(filePath);
		    	for(TransportQueryCondition condition:conditions){
		    		transportQueryConditionService.add(condition);	
		    	}
		    	conditions=null;
			}
			filePathList=null;
			logger.info("condition/file/read读取文件夹结束");
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			
		}
		
	}
	
	@RequestMapping(value="/file/del")
	public void delFiles(String cellPhone,String token,HttpServletRequest request, HttpServletResponse response){
		try{
			if (!validateToken(cellPhone, token, request, response)) {
				logger.info("confition/file/read token error.");
				return;
			}
			logger.info("condition/file/del删除文件开始");
		    FileUtil.delAllFile(AppConfig.getProperty("transport.condition.savepath"));
		    logger.info("condition/file/del删除文件结束");
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
}