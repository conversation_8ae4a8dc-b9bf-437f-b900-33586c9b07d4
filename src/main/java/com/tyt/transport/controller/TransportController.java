package com.tyt.transport.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.bi.service.IBiRequestService;
import com.tyt.cache.CacheService;
import com.tyt.common.bean.SimplePageGradeBean;
import com.tyt.common.service.*;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqTransportMsg;
import com.tyt.infofee.bean.ResendGoodsMsg;
import com.tyt.model.*;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.constant.RemoteApiConstant.IdcApi;
import com.tyt.plat.entity.base.TytTransportEnterpriseLog;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.plat.mapper.base.CoverGoodsLogMapper;
import com.tyt.plat.mapper.base.TytTransportEnterpriseLogMapper;
import com.tyt.plat.mapper.base.TytTransportMainExtendMapper;
import com.tyt.plat.mapper.recommend.NewIdentityMapper;
import com.tyt.plat.vo.ts.CallLogQuery;
import com.tyt.plat.vo.ts.TransportCarFindBIDataJson;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.*;
import com.tyt.transport.vo.MachineTypeVo;
import com.tyt.transportquotedprice.service.TransportQuotedPriceService;
import com.tyt.user.service.*;
import com.tyt.util.*;
import com.tytrecommend.model.NewIdentity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.tyt.util.Constant.USER_PERMISSION_REDIS_KEY;

/**
 * 信息相关接口
 *
 * <AUTHOR> modify by 20170620 增加高速费用查询接口 tianjw
 */
@Controller
@RequestMapping("/plat/transport")
public class TransportController extends BaseController {

    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;

    @Resource(name = "transportService")
    private TransportService transportService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "transportVaryService")
    private TransportVaryService transportVaryService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "transportLogService")
    TransportLogService transportLogService;

    @Resource(name = "tytUserSubService")
    TytUserSubService tytUserSubService;

    @Resource(name = "tytSequenceService")
    TytSequenceService tytSequenceService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;

    @Resource(name = "tytMapDictService")
    TytMapDictService tytMapDictService;

    @Resource(name = "tytPageGradeService")
    TytPageGradeService tytPageGradeService;

    @Resource(name = "tytMapDictLogService")
    TytMapDictLogService tytMapDictLogService;

    @Resource(name = "transportCollectService")
    private TransportCollectService collectService;

    @Resource(name = "keywordMatchesService")
    private KeywordMatchesService keywordMatchesService;
    @Resource(name = "keywordMatchesNewService")
    private KeywordMatchesNewService keywordMatchesNewService;
    @Resource(name = "machineTypeBrandNewService")
    private MachineTypeBrandNewService machineTypeBrandNewService;

    @Resource(name = "commonUserMachineTypeService")
    private CommonUserMachineTypeService commonUserMachineTypeService;
    @Resource(name = "commonUserMachineTypeNewService")
    private CommonUserMachineTypeNewService commonUserMachineTypeNewService;

    @Resource(name = "matchesClickService")
    private MatchesClickService matchesClickService;

    @Resource(name = "transportNullifyService")
    private TransportNullifyService transportNullifyService;

    @Resource(name = "freightDetailService")
    private FreightDetailService freightDetailService;

    @Resource(name = "freightRouteService")
    private FreightRouteService freightRouteService;

    @Resource(name = "carBaseOilCostService")
    private CarBaseOilCostService carBaseOilCostService;

    @Resource(name = "highwayCostRuleService")
    private HighwayCostRuleService highwayCostRuleService;

    @Resource(name = "tytMqRecommMessService")
    private TytMqRecommMessageService tytMqRecommMessageService;
    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "userCallPhoneService")
    private UserCallPhoneService userCallPhoneService;

    @Resource(name = "tytConfigService")
    private TytConfigService configService;
    @Autowired
    private IBiRequestService biRequestService;

    @Resource(name = "carOwnerQueryPhoneService")
    private TytCarOwnerQueryPhoneService carOwnerQueryPhoneService;

    @Resource(name = "userTelService")
    private UserTelService userTelService;
    @Resource(name = "machineTypeNewService")
    private MachineTypeNewService machineTypeNewService;
    @Resource(name = "machineTypeService")
    private MachineTypeService machineTypeService;
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name = "tytKeywordShortTransformService")
    private TytKeywordShortTransformService tytKeywordShortTransformService;
    @Resource(name = "tytOwnerAuthService")
    private TytOwnerAuthService tytOwnerAuthService;

    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    private static final int activeTime = 300;

    //5930 版本以后废弃此接口 修改为 /plat/transport/V5930/publish
    @Deprecated
    @RequestMapping(value = "/save")
    public void save(HttpServletRequest request, HttpServletResponse response) {

        logger.warn("Deprecated_check_use ############# ");
        Transport transport = new Transport();

        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            /* 获取业务参数 */
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "添加货物 ";

            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("startPoint");
                    add("destPoint");
                    add("startCoordX");
                    add("startCoordY");
                    add("destCoordX");
                    add("destCoordY");
                    //add("distance");
                    add("taskContent");
                    add("pubTime");
                    add("pubDate");
                    add("startLongitude");
                    add("startLatitude");
                    add("destLongitude");
                    add("destLatitude");
                    add("tel");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }

            //排除人工派单的货源，不进行手机号校验
            int isMt = 0;//是否人工派单货源
            if (params.get("isMt") != null) {
                try {
                    isMt = Integer.parseInt(params.get("isMt"));
                } catch (Exception e) {
                    logger.info("解析是否人工派单货源异常! {}", ExceptionUtils.getStackTrace(e));
                }
            }
            /** 特运通账户ID */
            String companyAccountUserId = configService.getStringValue("tyt_company_account_user_id");
            if (isMt == 0
                    && (!StringUtils.hasLength(companyAccountUserId) || !companyAccountUserId.equals(
                    userId.toString()))) {
                //添加手机号检验
                User user = userService.getById(userId);
                String registPhone = user.getCellPhone();
                String tel = params.get("tel");
                if (org.apache.commons.lang.StringUtils.isNotBlank(tel)) {
                    if (!checkPhone(tel.trim(), userId, registPhone)) {
                        backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,
                                "手机号码格式不正确", null, 0);
                        return;
                    }
                } else {
                    backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,
                            "手机号码不能为空", null, 0);
                    return;
                }
                String tel3 = params.get("tel3");
                if (org.apache.commons.lang.StringUtils.isNotBlank(tel3)) {
                    if (!checkPhone(tel3.trim(), userId, registPhone)) {
                        backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,
                                "手机号码2格式不正确", null, 0);
                        return;
                    }
                }
                String tel4 = params.get("tel4");
                if (org.apache.commons.lang.StringUtils.isNotBlank(tel4)) {
                    if (!checkPhone(tel4.trim(), userId, registPhone)) {
                        backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,
                                "手机号码3格式不正确", null, 0);
                        return;
                    }
                }
            }
            //TODO 经纬度参数非0校验
            String startLatitude = params.get("startLatitude");
            String startLongitude = params.get("startLongitude");
            String destLatitude = params.get("destLatitude");
            String destLongitude = params.get("destLongitude");

            if ("0".equals(startLatitude) || "0".equals(startLongitude) || "0".equals(destLatitude)
                    || "0".equals(destLongitude)
                    || "0.00".equals(startLatitude) || "0.00".equals(startLongitude) || "0.00".equals(
                    destLatitude) || "0.00".equals(destLongitude)) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,
                        "出发地、目的地经纬度错误", null, 0);
                return;
            }


            /* 检测是否可以发货 */
            String clientVersion = params.get("clientVersion");
            Integer clientSign = Integer.parseInt(params.get("clientSign"));
            ResultMsgBean checkMsgBean = userService.saveGoodsCheck(userId, clientVersion, clientSign);
            if (checkMsgBean.getCode() != 200) {
                backResponse(request, response, checkMsgBean.getCode(), checkMsgBean.getMsg(), null, 0);
                return;
            }
            // 增加版本校验无效关键字校验 create by tianjw on 20170705
            // 放开限制，人工派单页需要散货校验  modify by tianjw on 20180307
            //			if (clientSign == 2 || clientSign == 3) {
            String isVerifyNullifyVersion = this.tytConfigService.getStringValue(
                    "isVerifyNullifyTaskContent");
            if (StringUtils.hasLength(isVerifyNullifyVersion) && "0".equals(isVerifyNullifyVersion)) {
                String verifyVersion = this.tytConfigService.getStringValue("verifyNullifyVersion");
                if (StringUtils.hasLength(verifyVersion)) {
                    Integer verifyVersionInt = Integer.valueOf(verifyVersion);
                    Integer clientVersionInt = Integer.parseInt(clientVersion);
                    if (clientVersionInt >= verifyVersionInt) {
                        String contentKeyValue = transportNullifyService.verifyNullifyTaskContent(
                                params.get("taskContent"));
                        int returnCode = 0;
                        if (contentKeyValue != null && !"".equals(contentKeyValue)) {
                            returnCode = ReturnCodeConstant.NULLIFY_NOT_ALLOW_PUBLISH;
                        }
                        if (returnCode <= 0) {
                            contentKeyValue = transportNullifyService.verifyNullifyTaskContent(
                                    params.get("remark"));
                            if (contentKeyValue != null && !"".equals(contentKeyValue)) {
                                returnCode = ReturnCodeConstant.NULLIFY_REMARK_NOT_ALLOW_PUBLISH;
                            }
                        }
                        if (returnCode > 0) {
                            logger.info("存在关键字：{}", contentKeyValue);
                            //插入到记录表
                            TransportNullifyBean tnb = new TransportNullifyBean();
                            tnb.setTransId(0l);
                            tnb.setUserId(userId);
                            tnb.setTaskContent(params.get("taskContent"));
                            tnb.setTaskRemark(params.get("remark"));
                            tnb.setMatchingKeyword(contentKeyValue);
                            tnb.setUploadCellPhone(params.get("tel"));
                            tnb.setPlatId(Integer.parseInt(params.get("clientSign")));
                            tnb.setStartPoint(params.get("startPoint"));
                            tnb.setDestPoint(params.get("destPoint"));
                            tnb.setStartProvinc(params.get("startProvinc"));
                            tnb.setStartCity(params.get("startCity"));
                            tnb.setStartArea(params.get("startArea"));
                            tnb.setDestProvinc(params.get("destProvinc"));
                            tnb.setDestCity(params.get("destCity"));
                            tnb.setDestArea(params.get("destArea"));
                            tnb.setIsInfoFee(Integer.valueOf(params.get("isInfoFee")));
                            tnb.setCtime(new Date());
                            tnb.setMtime(new Date());
                            tnb.setState(-1);
                            //插入无效货源表
                            String msg = "货物非工程机械类，无法发布哦~";
                            transportNullifyService.add(tnb);
                            //								String msg = "发布非工程机械运输类货源信息，当心发货权限被关哦";
                            backResponse(request, response, returnCode, msg, null, 0);
                            return;
                        }
                    }
                } else {
                    logger.info("无效校验版本状态：关闭");
                }
            } else {
                logger.info("无效校验开启状态：关闭");
            }
            //			}

            /* 生成transport对象 */
            transport = createTransport(params, userId);

            /* 生成transport子表对象 by tianjw on 20170720 */
            TransportSubBean transportSub = createTransportSub(params);
            /* 判断是否重发操作 */
            Long tsId = Long.parseLong(params.get("tsId"));
            /* 重发次数 */
            int resendCounts = 0;
            boolean isHistoryGoods = false;
            if (tsId != 0) {

                Transport oldTran = transportBusiness.getByGoodsId(tsId);
                //新版信息费，发货不判断是否有人支付
                boolean isNewVerson = TytSwitchUtil.isNewInfofeeVersion();
                if (!isNewVerson) {
                    // 有人支付的不允许重发
                    if (oldTran.getIsInfoFee().equals("1") && !oldTran.getInfoStatus().equals("0")) {
                        backResponse(request, response, ReturnCodeConstant.NOT_ALLOW, "不允许重发", null, 0);
                        return;
                    }
                }
                // 设置最新的id
                transport.setId(oldTran.getId());
                Object hashCode = cacheService.getObject(
                        Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_"
                                + transport.getHashCode());
                logger.info("[" + Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_"
                        + oldTran.getHashCode() + ":" + hashCode + "]");
                if (hashCode != null) {
                    logger.info(condition + "重复信息发布");
                    backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "您已经发布过此条信息",
                            null, 0);
                    return;
                }
                // 判读是不是昨天的数据
                isHistoryGoods =
                        oldTran.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date()))
                                .getTime();
                // 计算重发次数：今天的第一条为0
                if (isHistoryGoods) {
                    resendCounts = 0;
                } else {
                    resendCounts = transportService.getMaxResendCounts(oldTran.getSrcMsgId());
                    resendCounts = resendCounts + 1;
                }
                String recommendHashCode = StringUtil.getHashCodeNewByRecommend(oldTran);
                // 置为无效
                transportBusiness.updateStatusBusiness(oldTran.getSrcMsgId(), recommendHashCode,
                        oldTran.getUserId(), 0, null);
                logger.warn("Deprecated_check_use ############# ");
                // 置为无效，查询最大的重发次第
                // resendCounts=transportBusiness.updateResendStatus(0,
                // oldTran.getHashCode(),oldTran.getUserId()) + 1;
            }
            /* hashcode验证重复信息 */
            if (transportService.isExitHashCode(transport.getHashCode())) {
                logger.info(condition + "重复信息发布");
                backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "您已经发布过此条信息",
                        null, 0);
                return;
            }
            /* 设置重发次数并增加序号 */
            transport.setResendCounts(resendCounts);
            if (resendCounts > 0) {
                transport.setPcOldContent("[" + resendCounts + "]." + transport.getPcOldContent());
            }
            /* 保存数据到数据库，缓存 */
            Map<String, Object> map = new HashMap<String, Object>();
            // srcMsgId 返回给app 用于app控制 优先派车中展示
            Long id = transportBusiness.addTransportBusiness(transport, transportSub);
            map.put("tsId", id);
            Transport newTransport = transportService.getById(id);
            if (newTransport != null) {
                map.put("srcMsgId", newTransport.getSrcMsgId());
                map.put("tsOrderNo", newTransport.getTsOrderNo());
            }
            if (tsId != 0) {
                String value = tytConfigService.getStringValue("transportEditPublishStatus",
                        "transport_edit_publish_status_{date}_{tsId}");
                String key = org.apache.commons.lang.StringUtils.replaceEach(value,
                        new String[]{"{date}", "{tsId}"}, new String[]{TimeUtil.formatDate_(new Date()),
                                String.valueOf(newTransport.getSrcMsgId())});
                RedisUtil.setObject(key, newTransport.getSrcMsgId(), activeTime);
            }
            // 获得当天发布条数
            map.put("remainNumber", tytUserSubService.getUserRemainNumber(userId));

            /** 特运通账户ID */
            //			String companyAccountUserId = tytConfigService.getStringValue("tyt_company_account_user_id");
            if (isMt == 1 || org.apache.commons.lang3.StringUtils.equals(userId.toString(),
                    companyAccountUserId)) {
                logger.info("特运通人工派单货物，直接发送精准推荐MQ,货物ID：{}", transport.getId());
                if (tsId == null || tsId.longValue() == 0 || isHistoryGoods) { // 首次发布时，发送精准推荐mq
                    sendRecommendMessage2MQ(transport.getId());
                }
            } else if (transport != null && transport.getIsStandard() != null
                    && transport.getIsStandard() == 0 && transport.getIsDisplay() != null
                    && transport.getIsDisplay() == 0) {
                // 为不再找货列表显示的货物，发送精准货源MQ消息

                // 非重发直接发送MQ
                if (tsId == null || tsId.longValue() == 0) {
                    logger.info("非编辑重发货物，直接发送精准推荐MQ,货物ID：{}", transport.getId());
                    validRecommendHashCode(transport);
                } else if (isHistoryGoods) {
                    logger.info("非今日编辑重发货物，发送精准推荐MQ,货物ID：{}", transport.getId());
                    validRecommendHashCode(transport);
                } else {
                    logger.info("今日编辑重发货物,不进行精准推荐MQ，货物ID：{}", transport.getId());
                    // 还原货物信息到找货列表
                    updateRevertTransportStatus(transport);
                }

            }

            // 成功后返回的信息
            String successMsg = null;
            //新版本修改成功后返回信息
            if (Integer.valueOf(clientVersion) > 5800) {
                //1.config中获取总数基数（截止到昨天为止的所有收取信息费的货源数量）
                Integer infofeeCount = tytConfigService.getIntValue("transport_send_info_fee_count");
                //2.redis中获取今天信息费货源数量（半小时更新一次）
                String todayInfofeeCount = RedisUtil.get("todayInfofeeTransportCount");
                //3.config中获取返回成功信息配置文字
                String message = tytConfigService.getStringValue("transport_send_success_msg");
                String[] msgArr = message.split(",");
                //4.拼接参数返回信息
                String success = msgArr[0] + "<p>" + msgArr[1] + "<p>" + msgArr[2];
                successMsg = String.format(success,
                        infofeeCount + Integer.valueOf(todayInfofeeCount == null ? "0" : todayInfofeeCount));

                map.put("successMsg", successMsg);
            } else {
                int transport_send_count = transportService.getCurrentSendCount(userId);
                String keyHead = tytConfigService.getStringValue("transport_send_count_item_key");
                if (transport_send_count >= 99) {
                    successMsg = cacheService.getString(keyHead + TimeUtil.formatDate(new Date()) + "_99");
                } else {
                    successMsg = cacheService.getString(
                            keyHead + TimeUtil.formatDate(new Date()) + "_" + transport_send_count);
                }

                map.put("successMsg", (successMsg == null || "".equals(successMsg)) ? "今日已发布<c>DC143C,"
                        + transport_send_count + "</c>条货源" : successMsg);
            }

            backResponse(request, response, ReturnCodeConstant.OK, "信息发布成功", map, 0);
            logger.info(condition + "成功" + transport.getId());
        } catch (Exception e) {
            cacheService.del(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_"
                    + transport.getHashCode());
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
            return;
        }
    }


    private void sendResendMessage(Long tsId) {
        int needResend = configService.getIntValue(Constant.RECOMMEND_NEED_DIRECT_REPUB, 1);
        logger.info("push recommend needResend is: " + needResend);
        if (needResend == 1) {
            ResendGoodsMsg resendGoodsMsg = new ResendGoodsMsg();
            String serialNum = SerialNumUtil.generateSeriaNum();
            resendGoodsMsg.setMessageSerailNum(serialNum);
            resendGoodsMsg.setMessageType(MqBaseMessageBean.RESEND_GOODS_MESSAGE);
            resendGoodsMsg.setTsId(tsId + "");
            logger.info("push recommend resendGoodsMsg is: " + resendGoodsMsg);
            tytMqMessageService.addSaveMqMessage(resendGoodsMsg.getMessageSerailNum(),
                    JSON.toJSONString(resendGoodsMsg), resendGoodsMsg.getMessageType());
            String delayedTime = configService.getStringValue(Constant.RECOMMEND_PLAT_RESEND_TIME, "60");
            logger.info("push recommend resend delay time is: " + delayedTime);
            tytMqMessageService.sendDelayedMqMessage(resendGoodsMsg.getMessageSerailNum(),
                    JSON.toJSONString(resendGoodsMsg), resendGoodsMsg.getMessageType(),
                    Long.valueOf(delayedTime) * 1000);
        }
    }

    // 还原货物信息到找货列表
    private void updateRevertTransportStatus(Transport transport) {
        if (transport != null && transport.getId() != null && transport.getId().longValue() > 0) {
            transport.setIsDisplay(1);
            transportService.update(transport);
            TransportMain transportMain = transportMainService.getBySrcMsgId(transport.getSrcMsgId());
            if (transportMain != null) {
                transportMain.setIsDisplay(1);
                transportMainService.update(transportMain);
            }
        }
    }

    private void validRecommendHashCode(Transport transport) {
        String recommendHashCode = StringUtil.getHashCodeNewByRecommend(transport);
        String result = (String) cacheService.getObject(
                Constant.CACHE_RECOMMEND_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_"
                        + recommendHashCode);
        boolean flag = StringUtils.hasLength(result);
        /* 保存hashcode到缓存，时间1天 */
        if (!flag) {
            cacheService.setObject(
                    Constant.CACHE_RECOMMEND_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_"
                            + recommendHashCode, recommendHashCode, Constant.CACHE_EXPIRE_TIME_24H);
            logger.info("发送精准推荐MQ,货物ID：{}", transport.getId());
            sendRecommendMessage2MQ(transport.getId());
        } else {
            logger.info("不发送精准推荐MQ,货物内容存在相同货物特征，货物ID：{}", transport.getId());
            // 还原货物信息到找货列表
            updateRevertTransportStatus(transport);
        }
    }

    /**
     * 向MQ发送准货源推荐信息
     *
     * @param transId 货物ID
     */
    private void sendRecommendMessage2MQ(Long transId) {
        // 标准货源并且是新发布的货源发布延迟重发mq消息
        sendResendMessage(transId);
        // 发送精准货源推荐信息MQ
        MqTransportMsg mqTransportMsg = new MqTransportMsg();

        mqTransportMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqTransportMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_RECOMMEND_TRANSPORT_MESSAGE);
        mqTransportMsg.setTsId(transId);
        // 保存mq信息到数据库,并发送MQ消息
        tytMqRecommMessageService.addSaveMqMessage(mqTransportMsg.getMessageSerailNum(),
                JSON.toJSONString(mqTransportMsg), mqTransportMsg.getMessageType());
        tytMqRecommMessageService.sendMqMessage(mqTransportMsg.getMessageSerailNum(),
                JSON.toJSONString(mqTransportMsg), mqTransportMsg.getMessageType());
    }

    /**
     * 查询单条信息
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/get")
    public void get(HttpServletRequest request, HttpServletResponse response) {
        try {

            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            /* 获取业务参数 */
            Long userId = Long.parseLong(params.get("userId"));
            logger.info("》》》》》plat transport get controller:{}【开始】", userId);
            String condition = "userId_" + userId + "单条查询货物信息 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("tsId");
                }
            };

            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            /** 获取详情 */
            Long tsId = Long.parseLong(params.get("tsId"));
            TransportDetail detailResult = transportMainService.getTransportDetail(tsId, userId);
            backResponse(request, response, ReturnCodeConstant.OK, "单条查询货物信息成功", detailResult, 0);
            logger.info("》》》》》plat transport get controller:{}【结束】$$$$$$$$$$【{}】", userId, tsId);
            /** 记录日志 */
            if (detailResult != null) {
                TransportDetailBean detailBean = detailResult.getDetailBean();
                if (detailBean != null) {
                    Long transportUserId = detailBean.getUserId();
                    if (transportUserId != null && transportUserId.longValue() != userId.longValue()) {
                        String today = TimeUtil.formatDate(new Date());
                        String ctime = TimeUtil.formatDate(detailBean.getCtime());
                        if (ctime.equals(today)) {
                            detailsLog(userId + "", tsId, params.get("clientVersion"),
                                    Integer.parseInt(params.get("clientSign")), detailBean.getStatus());
                            logger.info("》》》》》plat transport get controller:{}【记录日志结束】$$$$$$$$$$【{}】",
                                    userId, tsId);
                        }

                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
            return;
        }
    }

    /**
     * 详情查看日志记录
     *
     * @param userId
     * @param tsId
     * @param clientVersion
     * @param clientSign
     * @param status
     */
    private void detailsLog(String userId, Long tsId, String clientVersion, Integer clientSign,
                            Integer status) {
        long userIdL = 0;
        if (StringUtil.isNumeric(userId)) {
            userIdL = Long.parseLong(userId);
        }
        transportLogService.detailsLog(userIdL, tsId, clientVersion, clientSign, status, 0, 0, 0, 0, "", "", "", "", "", "", "", null);
    }

    /**
     * 创建Transport对象
     *
     * @param params
     * @return
     * @throws Exception
     */
    private Transport createTransport(Map<String, String> params, Long userId) throws Exception {
        User user = userService.getByUserId(userId);
        Transport transport = new Transport();
        transport.setUserId(Long.parseLong(params.get("userId")));
        transport.setPlatId(Integer.parseInt(params.get("clientSign")));
        transport.setStartCoord(params.get("startCoordX") + "," + params.get("startCoordY"));
        transport.setDestCoord(params.get("destCoordX") + "," + params.get("destCoordY"));
        transport.setDestPoint(params.get("destPoint"));
        transport.setStartPoint(params.get("startPoint"));
        transport.setTaskContent(params.get("taskContent"));
        transport.setTaskContent(StringUtil.filterTyt(transport.getTaskContent()));
        transport.setTel(params.get("tel"));
        transport.setTel3((params.get("tel3") == null || "".equals(params.get("tel3")) ? null
                : params.get("tel3").trim()));
        transport.setTel4((params.get("tel4") == null || "".equals(params.get("tel4")) ? null
                : params.get("tel4").trim()));
        transport.setPrice(params.get("price"));
        transport.setStartLatitudeValue(
                new BigDecimal(params.get("startLatitude")).movePointRight(2).intValue());
        transport.setStartLongitudeValue(
                new BigDecimal(params.get("startLongitude")).movePointRight(2).intValue());
        transport.setDestLatitudeValue(
                new BigDecimal(params.get("destLatitude")).movePointRight(2).intValue());
        transport.setDestLongitudeValue(
                new BigDecimal(params.get("destLongitude")).movePointRight(2).intValue());
        transport.setStartCoordXValue(new BigDecimal(params.get("startCoordX")).movePointRight(2).intValue());
        transport.setStartCoordYValue(new BigDecimal(params.get("startCoordY")).movePointRight(2).intValue());
        transport.setDestCoordXValue(new BigDecimal(params.get("destCoordX")).movePointRight(2).intValue());
        transport.setDestCoordYValue(new BigDecimal(params.get("destCoordY")).movePointRight(2).intValue());
        try {
            if (params.get("distance") != null && !"".equals(params.get("distance"))) {
                transport.setDistanceValue(
                        new BigDecimal(params.get("distance")).movePointRight(2).intValue());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //TODO 过滤货物内容的电话号码
        String taskContentTemp = transportMainService.replaceTaskContentByPhone(params.get("taskContent"));
        transport.setTaskContent(taskContentTemp.replaceAll("'", "-").replaceAll("\"", "--"));
        transport.setSource(Transport.SOURCE_MANUAL);
        transport.setPcOldContent(
                transport.getStartPoint().trim() + "---" + transport.getDestPoint().trim() + " "
                        + transport.getTaskContent().trim());

        if (user != null && transport.getSource() == Transport.SOURCE_MANUAL) {
            transport.setVerifyFlag(user.getVerifyFlag().intValue() == 2 ? 0 : user.getVerifyFlag());
        } else {
            transport.setVerifyFlag(0);
        }
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        // transport.setPubTime(params.get("pubTime"));
        transport.setPubTime(TimeUtil.formatTime(timestamp));
        // transport.setPubDate(TimeUtil.parseDateString(params.get("pubDate")));
        transport.setPubDate(timestamp);
        transport.setCtime(timestamp);
        transport.setMtime(timestamp);
        transport.setStartDetailAdd(params.get("startDetailAdd"));
        transport.setDestDetailAdd(params.get("destDetailAdd"));

        transport.setWeight(params.get("weight"));
        transport.setLength(params.get("length"));
        transport.setWide(params.get("wide"));
        transport.setHigh(params.get("high"));
        transport.setIsSuperelevation(params.get("isSuperelevation"));

        //TODO 过滤货物内容的电话号码
        String remarkTemp = transportMainService.replaceTaskContentByPhone(params.get("remark"));
        transport.setRemark(remarkTemp);

        /* 兼容老客户 */
        transport.setNickName(StringUtil.formatUserName(user.getUserName(), String.valueOf(user.getId())));

        transport.setLinkman(StringUtil.formatUserName(params.get("linkMan"), String.valueOf(user.getId())));

        transport.setPubQQ(user.getQq());
        transport.setUploadCellPhone(user.getCellPhone());
        /* hashcode+display */
        transport.setDisplayType("1");// 默认显示
        /* 添加用户信息 */
        transport.setIsCar(user.getIsCar());
        // transport.setUserType(user.getUserType());
        int vipType = 0;
        if (user != null && user.getUserType() == User.USER_TYPE_VIP && user.getServeDays() > 0) {
            vipType = 1;
        }
        transport.setUserType(vipType);
        /* 重发周期默认值在tyt_config表配置 */
        Integer serverDefaultResendTime = tytConfigService.getIntValue("defaultResendTime");
        transport.setResend(serverDefaultResendTime == null ? 0 : serverDefaultResendTime);
        transport.setStatus(Transport.STATUS_ENABLE);

        if (user != null && transport.getSource() == Transport.SOURCE_MANUAL) {
            transport.setVerifyPhotoSign(
                    user.getVerifyPhotoSign().longValue() > 1 ? 0 : user.getVerifyPhotoSign());
        } else {
            transport.setVerifyPhotoSign(0);
        }
        transport.setUserPart(user.getUserPart());
        transport.setClientVersion(params.get("clientVersion"));
        String isInfoFee = params.get("isInfoFee");
        if (isInfoFee == null || isInfoFee.trim().length() <= 0) {
            isInfoFee = "0";
        }
        transport.setIsInfoFee(isInfoFee);
        transport.setInfoStatus("0");// 信息费运单状态：0待接单 1有人支付成功 （货主的待同意
        // ）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成 5异常上报
        transport.setReleaseTime(timestamp);
        /* 已撤销，已过期进行直接发布，编辑再发布中,运单号重新生成 */
        transport.setTsOrderNo(
                tytSequenceService.updateGetNumberForDate(Constant.TABLE_WAYBILL_NAME));// 需要从接口获取

        transport.setRegTime(user.getCtime());
        transport.setType(params.get("type"));
        transport.setBrand(params.get("brand"));
        transport.setGoodTypeName(params.get("goodTypeName"));
        transport.setIsStandard(
                params.get("isStandard") == null ? 1 : Integer.valueOf(params.get("isStandard")));
        transport.setGoodNumber(
                params.get("machineNumber") == null ? null : Integer.valueOf(params.get("machineNumber")));
        transport.setMatchItemId(
                params.get("matchItemId") == null ? -1 : Integer.valueOf(params.get("matchItemId")));

        //hasdCode开关，如果 开 走新定义的规则，关 走老规则 1为关闭，2 为开启
        Integer hashcodeSwitch = tytConfigService.getIntValue("transportGreateHashcodeSwitch", 1);
        if (hashcodeSwitch == 2) {
            transport.setHashCode(getNewHashCode(transport));
        } else {
            transport.setHashCode(getHashCode(transport));
        }

        /* PC 重发字段:默认值在tyt_config表控制 */
        String resendStr = params.get("resend");
        if (StringUtils.hasLength(resendStr) && isNumeric(resendStr)) {
            SimplePageGradeBean gradeBean = tytPageGradeService.getByType("2", resendStr);
            if (gradeBean == null) {
                Integer serverDefaultResendTime2 = tytConfigService.getIntValue("defaultResendTime");
                transport.setResend(serverDefaultResendTime2 == null ? 0 : serverDefaultResendTime2);
            } else {
                transport.setResend(gradeBean.getValueBeginRange());
            }

        } else {
            Integer serverDefaultResendTime2 = tytConfigService.getIntValue("defaultResendTime");
            transport.setResend(serverDefaultResendTime2 == null ? 0 : serverDefaultResendTime2);
        }
        // 5920 新增3个字段
        transport.setCarType(params.get("carType"));
        transport.setCarLength(params.get("carLength"));
        transport.setSpecialRequired(params.get("specialRequired"));
        return transport;
    }

    // 拼接运费信息
    private TransportSubBean createTransportSub(Map<String, String> params) throws Exception {
        TransportSubBean ts = new TransportSubBean();
        // 货主输入的运价
        String price = params.get("price");
        // 固定成本
        if (StringUtils.hasLength(params.get("basePrice"))) {
            ts.setFixedCost(Double.valueOf(params.get("basePrice")));
            ts.setBasePrice(ts.getFixedCost());
        }
        // 最低利润率
        if (StringUtils.hasLength(params.get("profixRate"))) {
            ts.setMinProfitRate(Double.valueOf(params.get("profixRate")));
        }

        try {
            // 计算成交底价 = 固定成本+（固定成本*最低利润率）
            if (StringUtils.hasLength(params.get("basePrice"))) {
                // if(StringUtils.hasLength(params.get("fixedCost")) &&
                // StringUtils.hasLength(params.get("minProfitRate"))){
                // Double minProfitRate =
                // HighwayCostCalcUtil.calcDivide(params.get("minProfitRate"),"100.0");
                // Double minProfitRate =
                // Double.valueOf(params.get("minProfitRate"));
                // Double basePrice =
                // HighwayCostCalcUtil.calcMultiply(params.get("fixedCost"),Double.toString(minProfitRate));
                // basePrice
                // =HighwayCostCalcUtil.calcInCrease(params.get("fixedCost"),Double.toString(basePrice));
                // 成交底价
                // if(basePrice != null && basePrice >= 0){
                // ts.setBasePrice(basePrice);
                // }
                // 计算成交利润率 = （货主输入运价 - 成交底价 ）/ 货主输入运价
                if (StringUtils.hasLength(price) && Double.valueOf(price) > 0 && ts.getFixedCost() > 0) {
                    // 利润金额
                    Double profitPrice = HighwayCostCalcUtil.calcSubtract(price,
                            Double.toString(ts.getFixedCost()));
                    // 如果利润等于0，则利润率直接为0
                    if (profitPrice == null || profitPrice.intValue() == 0) {
                        ts.setProfitRate(0d);
                        return ts;
                    }
                    // 成交利润率
                    Double profitRate = HighwayCostCalcUtil.calcDivide(Double.toString(profitPrice), price);
                    if (profitRate != null) {
                        profitRate = HighwayCostCalcUtil.getNumberFourPrecision(profitRate);// 保留四位小数
                        profitRate = HighwayCostCalcUtil.calcMultiply(Double.toString(profitRate), "100");
                        ts.setProfitRate(profitRate);
                    }
                }
            }
        } catch (Exception e) {
            logger.info("[ERROR]运费SUB子表存储失败USER_ID:【{}】,出发地：【{}】，目的地：【{}】，货物：【{}】",
                    params.get("userId"), params.get("startPoint"), params.get("destPoint"),
                    params.get("taskContent"));
        }
        return ts;
    }

    /**
     * 获得货物的hashcode,生成规则出发地+目的地+货物内容+电话号码（tel,tel3,tel4）
     *
     * @param transport
     * @return
     */
    private String getHashCode(Transport transport) {
        String code = transport.getStartPoint() + transport.getDestPoint() + StringUtil.filterBlank(
                StringUtil.filterPunctuation(StringUtil.filterEmotion(transport.getTaskContent())));
        if (StringUtils.hasLength(transport.getTel())) {
            code += transport.getTel();
        }
        if (StringUtils.hasLength(transport.getTel3())) {
            code += transport.getTel3();
        }
        if (StringUtils.hasLength(transport.getTel4())) {
            code += transport.getTel4();
        }

        code += transport.getStatus();
        code += TimeUtil.formatDate(new Date());
        if (StringUtils.hasLength(transport.getPubQQ() + "")) {
            code += transport.getPubQQ();
        }

        if (StringUtils.hasLength(transport.getStartDetailAdd())) {
            code += transport.getStartDetailAdd();
        }
        if (StringUtils.hasLength(transport.getDestDetailAdd())) {
            code += transport.getDestDetailAdd();
        }
        if (StringUtils.hasLength(transport.getLength())) {
            code += transport.getLength();
        }
        if (StringUtils.hasLength(transport.getWide())) {
            code += transport.getWide();
        }
        if (StringUtils.hasLength(transport.getHigh())) {
            code += transport.getHigh();
        }
        if (StringUtils.hasLength(transport.getPrice())) {
            code += transport.getPrice();
        }
        if (StringUtils.hasLength(transport.getWeight())) {
            code += transport.getWeight();
        }
        if (StringUtils.hasLength(transport.getIsSuperelevation())) {
            code += transport.getIsSuperelevation();
        }
        if (StringUtils.hasLength(transport.getRemark())) {
            code += transport.getRemark();
        }
        code += transport.getIsInfoFee();// 需要支付跟不需要支付算两条信息
        return code.hashCode() + "";
    }

    /**
     * 更改货物信息状态
     *
     * @param request
     * @param response
     */
    //该方法可能被弃用，如果确认已弃用，请在此补充
    @Deprecated
    @RequestMapping(value = "/update")
    public void updateStatus(HttpServletRequest request, HttpServletResponse response) {
        logger.warn("Deprecated_check_use ############# ");
        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            /* 获取业务参数 */
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "更改货物信息状态 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("tsId");
                    add("status");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            /* 业务参数获取 */
            Long tsId = Long.parseLong(params.get("tsId"));
            Integer status = Integer.parseInt(params.get("status"));
            /* 检验状态值是否正确？ */
            if (status != 5 && status != 4) {
                logger.info(condition + "状态值不对");
                ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.TYPE_ERROR_CODE, "状态值不对",
                        null);
                printJSON(request, response, msgBean);
                return;
            }
            /* 检查对象是否合法 */
            // 货源ID合法性验证,先从tyt_transport(数据量少)表找数据，再从tyt_transport_main(所有数据)中找
            // 把信息置为过期信息需要在tyt_transport_main表中找原数据
            Transport oldTransport = transportBusiness.getByGoodsId(tsId);
            if (oldTransport == null) {
                logger.info(condition + "不存在id=" + tsId);
                return;
            }
            // 已经支付信息费的不能撤销
            if (status == 5 && oldTransport.getIsInfoFee().equals("1") && !oldTransport.getInfoStatus()
                    .equals("0")) {
                logger.info("货物信息update老接口已经支付信息费的不能撤销");
                return;
            }
            if (status == 4 && oldTransport.getIsInfoFee().equals("1")) {
                logger.info("货物信息update老接口需要支付信息费的不能线下设置成交");
                return;
            }
            /* 修改数据库状态 */
            transportBusiness.updateTransportStatus(oldTransport, status, userId);
            /* 根据id获取货物 */
            backResponse(request, response, ReturnCodeConstant.OK, "货物状态更新成功", null, 0);
            logger.info(condition + "成功[" + tsId + "_" + status + "]");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
            return;
        }

    }

    /**
     * 我的收藏列表接口
     *
     * @param baseParameter
     * @param queryType
     * @param querySign
     * @return
     */
    @RequestMapping(value = "/collect/get")
    @ResponseBody
    public ResultMsgBean collectGetList(BaseParameter baseParameter, Integer queryType, Long querySign) {

        ResultMsgBean rm = new ResultMsgBean();

        try {
            // 检查属性
            if (checkQueryParameter(queryType, querySign, rm)) {
                List<TransportQueryBean> list = collectService.getByUserId(baseParameter.getUserId(),
                        queryType.intValue(), querySign == null ? 0 : querySign.intValue());
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(list);
            }

        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;

    }

    /**
     * 查询我的收藏通过分页
     *
     * @param baseParameter
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/collect/getByPage")
    public ResultMsgBean collectByPageList(BaseParameter baseParameter, Integer pageSize,
                                           Integer pageNumber) {
        if (null == baseParameter.getUserId() || null == pageNumber || null == pageSize) {
            ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.ERROR, "参数异常");
            return resultMsgBean;
        }
        ResultMsgBean resultMsgBean = collectService.getCollectByPageList(baseParameter.getUserId(),
                pageNumber, pageSize);
        return resultMsgBean;
    }

    /**
     * 取消收藏
     *
     * @param request
     * @param response
     */
    @RequestMapping("/collect/delete")
    public void collectDelete(HttpServletRequest request, HttpServletResponse response) {

        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            /* 获取业务参数 */
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + "取消收藏 ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("id");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            Long id = Long.parseLong(params.get("id"));
            collectService.delCollect(userId, id);
            backResponse(request, response, ReturnCodeConstant.OK, "我的收藏取消成功", null, 0);
            logger.info(condition + "成功" + id);
        } catch (Exception e) {
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
            return;
        }
    }

    /**
     * 信息收藏接口
     *
     * @param request
     * @param response
     */
    @RequestMapping("/collect/save")
    public void collectSave(HttpServletRequest request, HttpServletResponse response) {

        try {
            /* 参数解析 */
            Map<String, String> params = parseRequestParams(request);
            /* 获取业务参数 */
            Long userId = Long.parseLong(params.get("userId"));
            String condition = "userId_" + userId + " ";
            /* 业务参数验证 */
            @SuppressWarnings("serial")
            List<String> names = new ArrayList<String>() {
                {
                    add("tsId");
                }
            };
            if (!validateArguments(condition, request, response, params, names)) {
                return;
            }
            /* 获取业务参数 */
            Long tsId = Long.parseLong(params.get("tsId"));
            /* 只有有效信息才可以收藏 */
            TransportMain transportMain = transportMainService.getTransportMainForId(tsId);

            // = transportMainService.getById(tsId);

            if (transportMain != null && transportMain.getStatus() != 1) {
                backResponse(request, response, ReturnCodeConstant.OTHER_ERROR, "只有发布中的才可以收藏",
                        null, 0);
                return;
            }

            /* 是否重复收藏？ */
            if (collectService.isExit(userId, tsId) != null) {
                backResponse(request, response, ReturnCodeConstant.DATA_HAS_EXIT, "您已收藏过此条信息", null,
                        0);
                return;
            }
            params.put("tsId", String.valueOf(transportMain.getId()));
            /* 保存到数据库 */
            TransportCollect collect = createCollect(params);
            collectService.add(collect);
            Map<String, Long> map = new HashMap<String, Long>();
            map.put("id", collect.getId());
            backResponse(request, response, ReturnCodeConstant.OK, "收藏成功", map, 0);
            logger.info(condition + "收藏成功");
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
        }

    }

    /**
     * 生成收藏信息对象
     *
     * @param params
     * @return
     * @throws Exception
     * @throws NumberFormatException
     */
    private TransportCollect createCollect(Map<String, String> params)
            throws NumberFormatException, Exception {
        TransportCollect collect = new TransportCollect();
        collect.setCtime(new Timestamp(System.currentTimeMillis()));
        if (StringUtils.hasLength(params.get("carId"))) {
            collect.setCarId(Long.parseLong(params.get("carId")));
        }
        collect.setUserId(Long.parseLong(params.get("userId")));
        TransportMain transportMain = transportMainService.getTransportMainForId(
                Long.parseLong(params.get("tsId")));
        collect.setInfoId(transportMain.getSrcMsgId()); // 收藏数据变更为srcid
        collect.setPlatId(Integer.parseInt(params.get("clientSign")));
        collect.setStatus(1);
        collect.setHeadCity(params.get("headCity"));
        collect.setHeadNo(params.get("headNo"));
        collect.setTailCity(params.get("tailCity"));
        collect.setTailNo(params.get("tailNo"));
        /* 兼容老版本 手机号 */
        User user = userService.getByUserId(Long.parseLong(params.get("userId")));
        collect.setCellPhone(user.getCellPhone());
        return collect;
    }

    /**
     * 按时间查询运输信息列表，仅限登录用户使用
     *
     * @param baseParameter
     * @param startCoord
     * @param destCoord
     * @param startLonLat
     * @param destLonLat
     * @param queryType
     * @param querySign
     * @return
     */
    @RequestMapping(value = "/search/v1")
    @ResponseBody
    public ResultMsgBean searchV1(BaseParameter baseParameter, String startCoord, String destCoord,
                                  String startLonLat, String destLonLat, Integer queryType,
                                  Long querySign, String startDistance, String destDistance,
                                  @RequestParam(value = "sortType", defaultValue = "0") Integer sortType,
                                  Long carId, String headCity, String headNo, String numberType,
                                  Integer userType, String carLength, String carType, String specialRequired) {
        return this.search(baseParameter, startCoord, destCoord, startLonLat,
                destLonLat, queryType, querySign, startDistance, destDistance,
                sortType, carId, headCity, headNo, numberType,
                userType, carLength, carType, specialRequired);
    }

    /**
     * 按时间查询运输信息列表
     *
     * @param baseParameter
     * @param startCoord
     * @param destCoord
     * @param startLonLat
     * @param destLonLat
     * @param queryType
     * @param querySign
     * @return
     */
    @RequestMapping(value = "/search")
    @ResponseBody
    public ResultMsgBean search(BaseParameter baseParameter, String startCoord, String destCoord,
                                String startLonLat, String destLonLat,
                                Integer queryType, Long querySign, String startDistance, String destDistance,
                                @RequestParam(value = "sortType", defaultValue = "0") Integer sortType,
                                Long carId, String headCity, String headNo, String numberType,
                                Integer userType, String carLength, String carType, String specialRequired) {
        ResultMsgBean rm = new ResultMsgBean();
        long rangSize = Long.parseLong(
                String.valueOf(AppConfig.getIntProperty("tyt.tyt_transport.range.size")));
        long startCoordX = 0;
        long startCoordY = 0;
        long destCoordX = 0;
        long destCoordY = 0;
        long startDistanceValue = 0;
        long destDistanceValue = 0;
        try {
            // 坐标判断
            if (org.apache.commons.lang.StringUtils.isBlank(startCoord)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("出发地坐标不能为空！");
                return rm;
            } else {
                String[] coord = startCoord.split(",");
                if (coord == null || coord.length < 2) {
                    rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    rm.setMsg("出发地坐标不能为空！");
                    return rm;
                } else {
                    startCoordX = new BigDecimal(coord[0]).multiply(new BigDecimal(rangSize)).longValue();
                    startCoordY = new BigDecimal(coord[1]).multiply(new BigDecimal(rangSize)).longValue();
                    if (startDistance != null && !"".equals(startDistance) && !"null".equals(startDistance)) {
                        startDistanceValue = Long.parseLong(startDistance);
                    }
                }
            }
            if (!org.apache.commons.lang.StringUtils.isBlank(destCoord)) {
                String[] coord = destCoord.split(",");
                if (coord == null || coord.length < 2) {
                    rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    rm.setMsg("目的地坐标错误,非x,y形式");
                    return rm;
                } else {
                    destCoordX = new BigDecimal(coord[0]).multiply(new BigDecimal(rangSize)).longValue();
                    destCoordY = new BigDecimal(coord[1]).multiply(new BigDecimal(rangSize)).longValue();
                    if (destDistance != null && !"".equals(destDistance) && !"null".equals(destDistance)) {
                        destDistanceValue = Long.parseLong(destDistance);
                    }
                }
            }
            // 经纬度判断
            if (org.apache.commons.lang.StringUtils.isBlank(startLonLat)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("出发地经纬度不能为空！");
                return rm;
            } else {
                String[] coord = startLonLat.split(",");
                if (coord == null || coord.length < 2) {
                    rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    rm.setMsg("出发地经纬度不能为空！");
                    return rm;
                } else {
                }
            }
            if (!org.apache.commons.lang.StringUtils.isBlank(destLonLat)) {
                String[] coord = destLonLat.split(",");
                if (coord == null || coord.length < 2) {
                    rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    rm.setMsg("目的地经纬度错误,非x,y形式");
                    return rm;
                } else {
                }
            }
            // 为了修复竞品爬取数据的问题，此处修改为如果不登录的用户，则不可以下拉和上滑  暂时不启用
            //			if (baseParameter.getUserId() == null || baseParameter.getUserId().longValue() <= 0) {
            //				//0：下拉   1：首次进入   2：上拉
            //				if(queryType.intValue() == 0 || queryType.intValue() == 2){
            //					rm.setCode(ReturnCodeConstant.OK);
            //					rm.setMsg("查询成功.");
            //					return rm;
            //				}
            //			}
            // 检查属性
            if (checkQueryParameter(queryType, querySign, rm)) {
                // 取得用户信息用于设置 试用用户延迟显示最新数据
                User user = null;

                //是否正确显示昵称
                boolean isTrueNikeName = false;


                if (null != baseParameter.getUserId() && 0 != baseParameter.getUserId().longValue()) {
                    user = (User) cacheService.getObject(Constant.CACHE_USER_KEY + baseParameter.getUserId());
                    if (null == user) {
                        user = userService.getById(baseParameter.getUserId());
                    }
                    if (null != user) {
                        //会员天数大于0天 可以显示昵称
                        if (user.getServeDays() > 0) {
                            isTrueNikeName = true;
                        }
                    }
                }


                // 是否延时
                boolean isDelayed = true;
                // 查询延时开关0开1关
                int delayedConfig = AppConfig.getIntProperty("tyt.tyt_transport.query.delayed").intValue();
                if (delayedConfig == 1) {
                    isDelayed = false;
                } else {
                    if (null != baseParameter.getUserId() && 0 != baseParameter.getUserId().longValue()) {
                        user = (User) cacheService.getObject(
                                Constant.CACHE_USER_KEY + baseParameter.getUserId());
                        if (null == user) {
                            user = userService.getById(baseParameter.getUserId());
                        }
                        if (user != null) {
                            // 用户车辆没有认证通过延时查找
                            String isCar = user.getIsCar();
                            if (isCar != null && "1".equals(isCar.trim())) {
                                isDelayed = false;
                            }
                        }
                    }
                }
                String clientVersion = baseParameter.getClientVersion();

                Map<Object, Object> map = transportService.queryTransport(startCoordX, startCoordY,
                        destCoordX, destCoordY,
                        queryType.intValue(), querySign == null ? 0 : querySign.longValue(),
                        isDelayed, startDistanceValue, destDistanceValue, baseParameter.getUserId(),
                        userType, carLength, carType, specialRequired, clientVersion);


                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(map.get("resultList"));
                rm.setTime(System.currentTimeMillis() + "");
                //				logger.info("search result is [ " + map.get("resultList") + " ]");
                rm.setTotalSize((Long) map.get("totalSize"));
                logger.debug("总条数【{}】", map.get("totalSize"));

            }
        } catch (NumberFormatException e) {
            logger.error("坐标转换异常", e);
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("坐标转换错误");
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        /* 日志记录 */
        if (queryType == 1) {
            // modify by tianjw on 2018-01-22 放开查询类型限制，原来只记录首次的日志，放开后，都记录，其中numberType=3为本页刷新 =4为加载下一页    暂时注释掉，数据太多，只记录主动搜索的日志
            //		if(!StringUtils.hasLength(numberType)){
            //			if(queryType == 0){
            //				numberType = "3";
            //			}else if(queryType == 2){
            //				numberType = "4";
            //			}
            //		}
            searchLog(baseParameter, sortType, startCoord, startDistance, destCoord, destDistance, carId,
                    headNo, headCity, numberType, carLength, carType, specialRequired);
        }
        return rm;
    }

    @SuppressWarnings("unchecked")
    private void encyptNickname(Object object, String clientVersion) {
        if (object != null) {
            List<TransportBean> transportBeans = (List<TransportBean>) object;
            // 获取需要进行登录错误次数控制的最小版本（不包括）
            String restriceLoginFailMinVersion = tytConfigService
                    .getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5301");
            int encyptPhoneMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
            int clientVersionInt = Integer.valueOf(clientVersion).intValue();
            // 是否需要对电话加密 1 需要 2 不需要
            int isNeedEncypt = configService.getIntValue(Constant.IS_NEED_ENCYPT_KEY, 1);
            if (transportBeans != null && transportBeans.size() > 0) {
                TransportBean curTransportBean;
                String curNickname;
                for (int i = 0; i < transportBeans.size(); i++) {
                    curTransportBean = transportBeans.get(i);
                    curTransportBean.setIsNeedDecrypt(isNeedEncypt);
                    logger.info("isNeedEncypt == 1 && (clientVersionInt > restriceLoginFailMinVersionInt): "
                            + (isNeedEncypt == 1
                            && (clientVersionInt > encyptPhoneMinVersionInt)));
                    if (isNeedEncypt == 1 && (clientVersionInt > encyptPhoneMinVersionInt)) {
                        curNickname = curTransportBean.getNickName();
                        if (org.apache.commons.lang.StringUtils.isNotBlank(curNickname)) {
                            curTransportBean.setNickName(XXTea.Encrypt(curNickname,
                                    AppConfig.getProperty("tyt.xxtea.key")));
                        } else {
                            curTransportBean.setIsNeedDecrypt(2);
                        }
                    }
                }
            }
        }
    }


    /**
     * 查询日志记录
     *
     * @param baseParameter
     * @param sortType
     */
    private void searchLog(BaseParameter baseParameter, Integer sortType, String startCoord,
                           String startRange, String destCoord, String destRange, Long carId, String headNo, String headCity,
                           String numberType, String carLength, String carType, String specialRequired) {
        long userId = baseParameter.getUserId() == null ? 0 : baseParameter.getUserId();
        // 查询条件日志
        transportLogService.searchLog(userId, startCoord, startRange, destCoord, destRange,
                carId == null ? 0 : carId, headNo, headCity, baseParameter.getClientVersion(),
                Integer.parseInt(baseParameter.getClientSign()), sortType, numberType,
                baseParameter.getOsVersion(), baseParameter.getClientId(), carLength, carType,
                specialRequired, "", "");
        // 距离条件日志
        sortlog(baseParameter, sortType);
    }

    private boolean checkQueryParameter(Integer queryType, Long querySign, ResultMsgBean rm) {
        if (queryType == null || (queryType.intValue() != 0 && queryType.intValue() != 1
                && queryType.intValue() != 2)) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("查询类型不正确！");
            return false;
        } else if (queryType.intValue() != 1 && (querySign == null || querySign.longValue() < 0)) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("查询标识错误，最大、最小ID为空！");
            return false;
        }
        return true;

    }

    /**
     * 查询相似货源列表
     *
     * @param baseParameter
     * @param queryType
     * @param querySign
     * @param startProvinc
     * @param startCity
     * @param destProvinc
     * @param destCity
     * @param matchItemId
     * @return
     */
    @RequestMapping(value = "/search/similarity")
    @ResponseBody
    public ResultMsgBean searchSimilarity(BaseParameter baseParameter,
                                          Integer queryType, Long querySign,
                                          @RequestParam(value = "id", required = true) Long id,
                                          @RequestParam(value = "startProvinc", required = true) String startProvinc,
                                          @RequestParam(value = "startCity", required = true) String startCity,
                                          @RequestParam(value = "destProvinc", required = true) String destProvinc,
                                          @RequestParam(value = "destCity", required = true) String destCity,
                                          @RequestParam(value = "matchItemId", required = true) Long matchItemId) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (id == null || id < 0) { // 当条货源
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("货源ID不正确");
                return rm;
            }
            if (org.apache.commons.lang.StringUtils.isBlank(startProvinc)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("出发地省不能为空");
                return rm;
            }
            if (org.apache.commons.lang.StringUtils.isBlank(startCity)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("出发地市不能为空");
                return rm;
            }
            if (org.apache.commons.lang.StringUtils.isBlank(destProvinc)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("目的地省不能为空");
                return rm;
            }
            if (org.apache.commons.lang.StringUtils.isBlank(destCity)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("目的地市不能为空");
                return rm;
            }
            // 标准化货源判断
            if (matchItemId == null || matchItemId < 0) { // 非标准化货源
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("货源标准化ID不正确");
                return rm;
            }
            // 检查属性
            if (checkQueryParameter(queryType, querySign, rm)) {
                Map<Object, Object> map = transportService.searchSimilarity(id, startProvinc, startCity,
                        destProvinc, destCity, matchItemId, queryType, querySign == null ? 0 : querySign,
                        baseParameter.getUserId(), baseParameter.getClientVersion());

                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(map.get("resultList"));
                rm.setTime(System.currentTimeMillis() + "");
                rm.setTotalSize((Long) map.get("totalSize"));
                logger.info("总条数【{}】", map.get("totalSize"));
            }
        } catch (NumberFormatException e) {
            logger.error("标准化ID异常", e);
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("标准化ID错误");
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 查询变动运输信息
     * 已迁移到 goods-search 项目
     *
     * @param baseParameter
     * @param maxId
     * @return
     */
    @RequestMapping(value = "/vary")
    @ResponseBody
    public ResultMsgBean vary(BaseParameter baseParameter, Long maxId) {
        long startTime = System.currentTimeMillis();
        ResultMsgBean rm = new ResultMsgBean();
        long id = 0;
        try {
            if (null != maxId && maxId.longValue() != 0) {
                id = maxId.longValue();
            }
            List<TransportVaryBean> list = transportVaryService.getTransportVaryForDate(id);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("查询成功");
            rm.setData(list);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 我的发布历史列表
     *
     * @param baseParameter 基础参数
     * @param status        状态
     * @param queryType
     * @param querySign
     * @return
     */
    @RequestMapping(value = "/history")
    @ResponseBody
    public ResultMsgBean history(BaseParameter baseParameter, Integer status, Integer queryType,
                                 Long querySign) {
        ResultMsgBean rm = new ResultMsgBean();
        int zt = -1;
        try {
            if (null != status && status.intValue() != -1) {
                if (status.intValue() == 1 || status.intValue() == 0 || status.intValue() == 4
                        || status.intValue() == 5) {
                    zt = status.intValue();
                } else {
                    rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    rm.setMsg("状态不在范围");
                    return rm;
                }
            }

            // 检查属性
            if (checkQueryParameter(queryType, querySign, rm)) {

                List<TransportBean> list = transportMainService.queryTransportHistory(
                        baseParameter.getUserId(), zt, queryType.intValue(),
                        querySign == null ? 0 : querySign.longValue());
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(list);
            }

        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 距离排序日志记录接口
     */
    @RequestMapping(value = "/sortlog")
    @ResponseBody
    public ResultMsgBean sortlog(BaseParameter baseParameter,
                                 @RequestParam(value = "sortType", defaultValue = "0") Integer sortType) {
        ResultMsgBean rm = new ResultMsgBean();
        long userId = baseParameter.getUserId() == null ? 0 : baseParameter.getUserId();
        /* 调用日志接口 */
        transportLogService.searchDistanceSortLog(userId, sortType, baseParameter.getClientVersion(),
                Integer.parseInt(baseParameter.getClientSign()));

        logger.debug("plat排序方法收集接口成功_" + baseParameter.getUserId());
        // 返回客户端
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("排序采集成功");
        return rm;
    }

    /**
     * 保存拨打电话记录
     *
     * @param userId           用户id
     * @param callResultCode   拨打电话结果状态 0:无标注 1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易 6：已经拉走
     * @param goodId           打电话咨询的货物id
     * @param fromCar          车头车牌号
     * @param carId            车辆ID
     * @param reference        备注
     * @param markerOwnerCodes 标记货主code码
     * @return
     */
    @RequestMapping(value = "/addCall")
    @ResponseBody
    public ResultMsgBean addCall(String userId, String callResultCode, String callResultName, String goodId,
                                 String fromCar, Long carId, String reference, String clientSign, String clientVersion, String markerOwnerCodes) {
        ResultMsgBean rm = new ResultMsgBean();
        logger.info("add call messsage, params is userId: " + userId + ", callResultCode: " + callResultCode
                + ", goodId: " + goodId + ", fromCar: " + fromCar);
        rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(userId)) {
                rm.setMsg("userId不能为空");
            }
            //            else if (org.apache.commons.lang.StringUtils.isEmpty(callResultCode)) {
            //                rm.setMsg("callResultCode不能为空");
            //            }
            else if (org.apache.commons.lang.StringUtils.isEmpty(goodId)) {
                rm.setMsg("goodId不能为空");
            } else {
                transportService.addCall(userId, callResultCode, callResultName, goodId, fromCar, carId,
                        reference, clientSign, clientVersion, markerOwnerCodes);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("添加成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("添加失败");
            logger.error("add call messsage failed, the error message is: " + e);
        }
        return rm;
    }

    /**
     * 存储BI需要的数据
     * @param baseParameter baseParameter
     * @param transportCarFindBIDataJson transportCarFindBIDataJson
     * @return ResultMsgBean
     */
    @RequestMapping(value = "/addFindTransportLogBIData", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public ResultMsgBean addFindTransportLogBIData(BaseParameter baseParameter, TransportCarFindBIDataJson transportCarFindBIDataJson) {
        if (baseParameter == null || transportCarFindBIDataJson == null
                || transportCarFindBIDataJson.getSrcMsgId() == null || transportCarFindBIDataJson.getType() == null
                || (transportCarFindBIDataJson.getType() != 1 && transportCarFindBIDataJson.getType() != 2)) {
            return ResultMsgBean.failResponse(10001, "参数错误");
        }

        if (transportCarFindBIDataJson.getCarUserId() == null && baseParameter.getUserId() != null) {
            transportCarFindBIDataJson.setCarUserId(baseParameter.getUserId());
        }

        if (transportCarFindBIDataJson.getCarUserId() == null) {
            return ResultMsgBean.failResponse(10001, "参数错误");
        }

        if (transportCarFindBIDataJson.getClientType() == null) {
            if (baseParameter.getClientSign() == null
                    || Integer.parseInt(baseParameter.getClientSign()) != Constant.ClientSignEnum.MINI_PROGRAM_TRUCKER.code) {
                transportCarFindBIDataJson.setClientType(1);
            } else {
                transportCarFindBIDataJson.setClientType(2);
            }
        }

        transportBusiness.saveFindTransportLogBIData(transportCarFindBIDataJson);
        return ResultMsgBean.successResponse();
    }

    /**
     * 保存拨打电话记录
     *
     * @param userId 用户id
     * @param goodId 打电话咨询的货物id
     * @return
     */
    @RequestMapping(value = "/callDetail")
    @ResponseBody
    public ResultMsgBean addCall(String userId, String goodId,
                                 String clientSign) {
        ResultMsgBean rm = new ResultMsgBean();
        logger.info(
                "get call messsage, params is userId: " + userId + ", goodId: " + goodId + ", clientSign: "
                        + clientSign);
        rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(userId)) {
                rm.setMsg("userId不能为空");
            } else if (org.apache.commons.lang.StringUtils.isEmpty(goodId)) {
                rm.setMsg("goodId不能为空");
            } else {
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(transportService.callDetail(userId, goodId, clientSign));
            }
        } catch (Exception e) {
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("查询失败");
            logger.error("add call messsage failed, the error message is: " + e);
        }
        return rm;
    }

    /**
     * 找货记录
     *
     * @return
     */
    @RequestMapping(value = "/queryCallList")
    @ResponseBody
    public ResultMsgBean queryCallList(CallLogQuery callLogQuery) {

        Long userId = callLogQuery.getUserId();
        Integer callResultCode = callLogQuery.getCallResultCode();
        Integer currentPage = callLogQuery.getCurrentPage();

        //货源是否被车方报过价筛选条件
        if (callLogQuery.getQuotedPriceOnce() == null) {
            callLogQuery.setQuotedPriceOnce(false);
        }

        //浏览记录筛选条件
        if (callLogQuery.getViewLog() == null) {
            callLogQuery.setViewLog(false);
        }

        ResultMsgBean rm = new ResultMsgBean();

        rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
        try {
            if (userId == null || userId <= 0) {
                rm.setMsg("userId不能为空");
            } else if (callResultCode == null) {
                rm.setMsg("callResultCode不能为空");
            } else if (currentPage == null) {
                rm.setMsg("currentPage不能为空");
            } else {
                CallLogQueryResultBean resultBean = transportService.getCallLog(callLogQuery);
                makeCallLogListTransportQuotedPriceData(resultBean.getData(), userId);
                rm.setData(resultBean);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
            }
        } catch (Exception e) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("查询失败");
            logger.error("get call log list failed, the error message is: " + e);
        }
        return rm;
    }

    /**
     * 找货记录填充货源是否有新的货方回价
     * @param callLogBeanList 找货记录
     * @param carUserId 车方userId
     */
    private void makeCallLogListTransportQuotedPriceData(List<CallLogBean> callLogBeanList, Long carUserId) {
        if (CollectionUtils.isEmpty(callLogBeanList) || carUserId == null) {
            return;
        }
        for (CallLogBean callLogBean : callLogBeanList) {
            if (transportQuotedPriceService.checkTransportValidity(callLogBean.getSrcMsgId()).getCode() != 200) {
                callLogBean.setHaveNewTransportQuotedPrice(false);
                continue;
            }
            try {
                callLogBean.setHaveNewTransportQuotedPrice(transportQuotedPriceService.getCarAndThisTransportIsHaveNewTransportQuotedPrice(callLogBean.getSrcMsgId(), carUserId));
                callLogBean.setHaveAgreeTransportQuotedPrice(transportQuotedPriceService.getCarAndThisTransportIsHaveAgreeTransportQuotedPrice(callLogBean.getSrcMsgId(), carUserId));
            } catch (Exception e) {
                logger.info("构造找货记录填充货源是否有新的货方回价、是否有已被同意的报价异常");
            }
        }
    }

    /**
     * @param goodId
     * @return
     */
    @RequestMapping(value = "/getPhone")
    @ResponseBody
    public ResultMsgBean getPhone(String goodId, String userId, String isNeedPhone, String clientVersion,
                                  String clientSign, String moduleType) {
        ResultMsgBean rm = new ResultMsgBean();
        logger.info("get phone by goodId, params is goodId: " + goodId + ", userId: " + userId
                + ", clientVersion: " + clientVersion + ", clientSign: " + clientSign + ", isNeedPhone: "
                + isNeedPhone);
        rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(goodId)) {
                rm.setMsg("goodId不能为空");
            } else if (org.apache.commons.lang.StringUtils.isEmpty(userId)) {
                rm.setMsg("userId不能为空");
            } else {
                CallLogBean callLogBean = transportService.getPhoneByGoodId(userId, goodId, clientVersion,
                        clientSign, isNeedPhone);
                if (!"1".equals(clientSign)) {
                    // 加密电话信息
                    encyptPhone(callLogBean, clientVersion);
                }
                //modify by tianjw on 2017-12-22 去掉拨打电话无效状态校验
                // create by tianjw on 2017-12-22 如果不存在或者状态非有效，则校验是否支付过信息费
                //				if(callLogBean == null){
                //					rm.setCode(ReturnCodeConstant.VISIT_TOO_FREQUENT);
                //					rm.setMsg("该货已拉走");
                //					return rm;
                //				}

                logger.info("transport recommend isCanCall value is: " + callLogBean.getIsCanCall());
                // 只有成功获取了电话才记录
                if (callLogBean.getIsCanCall().intValue() == 0) {
                    // 如果是人工派单货源则记录拨打电话信息（专门用户后台人工派单列表按照车方电话过滤使用）
                    TransportMain transportMain = transportMainService.getById(callLogBean.getSrcMsgId());
                    /** 特运通账户ID */
                    String companyAccountUserId = configService.getStringValue("tyt_company_account_user_id");
                    logger.info("transport recommend company user id is: " + companyAccountUserId
                            + " , pub goods user id is: " + transportMain.getUserId());
                    if (companyAccountUserId != null && companyAccountUserId.equals(
                            transportMain.getUserId() + "")) {
                        TytCarOwnerQueryPhone carOwnerQueryPhone = new TytCarOwnerQueryPhone();
                        carOwnerQueryPhone.setTsSrcId(callLogBean.getSrcMsgId());
                        carOwnerQueryPhone.setCarUserId(Long.valueOf(userId));
                        carOwnerQueryPhone = carOwnerQueryPhoneService.find(carOwnerQueryPhone);
                        // 为空才保存拨打记录
                        if (carOwnerQueryPhone == null) {
                            carOwnerQueryPhone = new TytCarOwnerQueryPhone();
                            User user = userService.getById(Long.valueOf(userId));
                            carOwnerQueryPhone.setCarLoginPhone(user.getCellPhone());
                            carOwnerQueryPhone.setCarUserId(Long.valueOf(userId));
                            carOwnerQueryPhone.setCtime(new Date());
                            carOwnerQueryPhone.setTsId(callLogBean.getSrcMsgId());
                            carOwnerQueryPhone.setTsSrcId(callLogBean.getSrcMsgId());
                            carOwnerQueryPhone.setUserId(transportMain.getUserId());
                            carOwnerQueryPhoneService.add(carOwnerQueryPhone);
                        }
                    }
                }
                /*
                 * 增加获取电话以及获取电话结果的日志信息, goodId 换成srcId
                 */
                userCallPhoneService.addGetPhoneLog(callLogBean.getSrcMsgId().toString(), userId, clientSign,
                        clientVersion, moduleType, callLogBean.getIsCanCall());
                rm.setData(callLogBean);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                logger.info("get phone by goodId, result is: " + callLogBean);
            }
        } catch (Exception e) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("查询失败");
            logger.error("get phone by goodId failed, the error message is: ", e);
        }
        return rm;
    }

    //    /**
    //     * btt5910修改 获取电话  无问题后删除
    //     *
    //     * @param goodId
    //     * @return
    //     */
    //    @RequestMapping(value = "/getPhoneWithRights")
    //    @ResponseBody
    //    public ResultMsgBean getPhoneWithRights(String goodId, String userId, String isNeedPhone, String clientVersion, String clientSign, String moduleType) {
    //        ResultMsgBean rm = new ResultMsgBean();
    //        logger.info("get phone by goodId, params is goodId: " + goodId + ", userId: " + userId + ", clientVersion: " + clientVersion + ", clientSign: " + clientSign + ", isNeedPhone: " + isNeedPhone);
    //        rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
    //        try {
    //            if (org.apache.commons.lang.StringUtils.isEmpty(goodId)) {
    //                rm.setMsg("goodId不能为空");
    //            } else if (org.apache.commons.lang.StringUtils.isEmpty(userId)) {
    //                rm.setMsg("userId不能为空");
    //            } else {
    //                CallLogBeanWithRights callLogBean = transportService.getPhoneByGoodIdWithRights(userId, goodId, clientVersion, clientSign, isNeedPhone);
    //                if (callLogBean != null && callLogBean.getUpdateCache() != null && callLogBean.getUpdateCache() == 5) {
    //                    logger.info("allowCallNum updateCache");
    //                    tytUserSubService.updateCache(Long.valueOf(userId));
    //                }
    //                callLogBean.setUpdateCache(null);
    //				/*if (!"1".equals(clientSign)) {
    //                   // 加密电话信息
    //                   encyptPhone(callLogBean, clientVersion);
    //               }*/
    //                //modify by tianjw on 2017-12-22 去掉拨打电话无效状态校验
    //                // create by tianjw on 2017-12-22 如果不存在或者状态非有效，则校验是否支付过信息费
    ////				if(callLogBean == null){
    ////					rm.setCode(ReturnCodeConstant.VISIT_TOO_FREQUENT);
    ////					rm.setMsg("该货已拉走");
    ////					return rm;
    ////				}
    //
    //                logger.info("transport recommend isCanCall value is: " + callLogBean.getIsCanCall());
    //                // 只有成功获取了电话才记录
    //                if (callLogBean.getIsCanCall().intValue() == 0) {
    //                    // 如果是人工派单货源则记录拨打电话信息（专门用户后台人工派单列表按照车方电话过滤使用）
    //                    TransportMain transportMain = transportMainService.getById(callLogBean.getSrcMsgId());
    //                    /** 特运通账户ID */
    //                    String companyAccountUserId = configService.getStringValue("tyt_company_account_user_id");
    //                    logger.info("transport recommend company user id is: " + companyAccountUserId + " , pub goods user id is: " + transportMain.getUserId());
    //                    if (companyAccountUserId != null && companyAccountUserId.equals(transportMain.getUserId() + "")) {
    //                        TytCarOwnerQueryPhone carOwnerQueryPhone = new TytCarOwnerQueryPhone();
    //                        carOwnerQueryPhone.setTsSrcId(callLogBean.getSrcMsgId());
    //                        carOwnerQueryPhone.setCarUserId(Long.valueOf(userId));
    //                        carOwnerQueryPhone = carOwnerQueryPhoneService.find(carOwnerQueryPhone);
    //                        // 为空才保存拨打记录
    //                        if (carOwnerQueryPhone == null) {
    //                            carOwnerQueryPhone = new TytCarOwnerQueryPhone();
    //                            User user = userService.getById(Long.valueOf(userId));
    //                            carOwnerQueryPhone.setCarLoginPhone(user.getCellPhone());
    //                            carOwnerQueryPhone.setCarUserId(Long.valueOf(userId));
    //                            carOwnerQueryPhone.setCtime(new Date());
    //                            carOwnerQueryPhone.setTsId(callLogBean.getSrcMsgId());
    //                            carOwnerQueryPhone.setTsSrcId(callLogBean.getSrcMsgId());
    //                            carOwnerQueryPhone.setUserId(transportMain.getUserId());
    //                            carOwnerQueryPhoneService.add(carOwnerQueryPhone);
    //                        }
    //                    }
    //                }
    //                /*
    //                 * 增加获取电话以及获取电话结果的日志信息, goodId 换成srcId
    //                 */
    //                userCallPhoneService.addGetPhoneLog(callLogBean.getSrcMsgId().toString(), userId, clientSign, clientVersion, moduleType, callLogBean.getIsCanCall());
    //                rm.setData(callLogBean);
    //                rm.setCode(ReturnCodeConstant.OK);
    //                rm.setMsg("查询成功");
    //                logger.info("get phone by goodId, result is: " + callLogBean);
    //            }
    //        } catch (Exception e) {
    //            rm.setCode(ReturnCodeConstant.ERROR);
    //            rm.setMsg("查询失败");
    //            logger.error("get phone by goodId failed, the error message is: ", e);
    //        }
    //        return rm;
    //    }

    /**
     * 修改货物出发地目的地之间的距离
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/updateDistance")
    @ResponseBody
    public ResultMsgBean updateDistance(BaseParameter baseParameter, Long tsId, String distance) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
        try {
            if (tsId == null) {
                rm.setMsg("tsId不能为空");
                return rm;
            }
            if (distance == null) {
                rm.setMsg("distance不能为空");
                return rm;
            }

            // 判断是不是编辑重发，编辑重发不记录
            String value = tytConfigService.getStringValue("transportEditPublishStatus",
                    "transport_edit_publish_status_{date}_{tsId}");
            String key = org.apache.commons.lang.StringUtils.replaceEach(value,
                    new String[]{"{date}", "{tsId}"},
                    new String[]{TimeUtil.formatDate_(new Date()), String.valueOf(tsId)});
            Object obj = RedisUtil.getObject(key);
            if (obj != null) {
                RedisUtil.del(key);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("操作成功");
                return rm;
            }

            TransportMain transportMain = transportMainService.getById(tsId);
            if (transportMain != null) {
                // TODO 经纬度参数非0校验,如果为0不可以入库
                Integer startLatitude = transportMain.getStartLatitudeValue();
                Integer startLongitude = transportMain.getStartLongitudeValue();
                Integer destLatitude = transportMain.getDestLatitudeValue();
                Integer destLongitude = transportMain.getDestLongitudeValue();

                if (startLatitude == null || startLongitude == null || destLatitude == null
                        || destLongitude == null || startLatitude.intValue() == 0
                        || startLongitude.intValue() == 0 || destLatitude.intValue() == 0
                        || destLongitude.intValue() == 0) {
                    logger.info("该货源经纬度错误，无法进行距离更新");
                    rm.setCode(ReturnCodeConstant.OK);
                    rm.setMsg("操作成功");
                    return rm;
                }

                if (transportMain.getUserId().longValue() == (baseParameter.getUserId() == null ? 0l
                        : baseParameter.getUserId().longValue())) {
                    rm.setCode(ReturnCodeConstant.OK);
                    rm.setMsg("操作成功");
                    return rm;
                }
            }

            saveMapDictLog(baseParameter.getClientSign(), transportMain, distance);

            int transport_distance_record_version = tytConfigService.getIntValue(
                    "transport_distance_record_version", 5001);

            if (Integer.parseInt(baseParameter.getClientVersion()) < transport_distance_record_version) {
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("操作成功");
                return rm;
            }
            if (transportMain != null) {
                TytMapDict tytMapDict = new TytMapDict();
                Transport transport = transportService.getById(tsId);

                int transport_distance_record_type = tytConfigService.getIntValue(
                        "transport_distance_record_type", 0);

                if (baseParameter.getClientSign().equals("2")) {
                    transportMain.setAndroidDistance(new BigDecimal(distance).movePointRight(2).intValue());
                    transport.setAndroidDistance(new BigDecimal(distance).movePointRight(2).intValue());
                    tytMapDict.setDistance(new BigDecimal(distance).movePointRight(2).intValue());

                    if (transport_distance_record_type == 1) {
                        transportMain.setIosDistance(new BigDecimal(distance).movePointRight(2).intValue());
                        transport.setIosDistance(new BigDecimal(distance).movePointRight(2).intValue());
                        tytMapDict.setIosDistance(new BigDecimal(distance).movePointRight(2).intValue());
                    }

                } else if (baseParameter.getClientSign().equals("3")) {
                    if (transport_distance_record_type == 1) {
                        rm.setCode(ReturnCodeConstant.OK);
                        rm.setMsg("操作成功");
                        return rm;
                    }
                    transportMain.setIosDistance(new BigDecimal(distance).movePointRight(2).intValue());
                    transport.setIosDistance(new BigDecimal(distance).movePointRight(2).intValue());
                    tytMapDict.setIosDistance(new BigDecimal(distance).movePointRight(2).intValue());

                }
                transportMainService.update(transportMain);
                transportService.update(transport);

                tytMapDict.setCtime(new Date());
                tytMapDict.setStartProvinc(transportMain.getStartProvinc());
                tytMapDict.setStartCity(transportMain.getStartCity());
                tytMapDict.setStartArea(transportMain.getStartArea());
                tytMapDict.setStartCoordX(transportMain.getStartCoordXValue());
                tytMapDict.setStartCoordY(transportMain.getStartCoordYValue());
                tytMapDict.setStartLatitude(transportMain.getStartLatitudeValue());
                tytMapDict.setStartLongitude(transportMain.getStartLongitudeValue());

                tytMapDict.setDestProvinc(transportMain.getDestProvinc());
                tytMapDict.setDestCity(transportMain.getDestCity());
                tytMapDict.setDestArea(transportMain.getDestArea());
                tytMapDict.setDestCoordX(transportMain.getDestCoordXValue());
                tytMapDict.setDestCoordY(transportMain.getDestCoordYValue());
                tytMapDict.setDestLatitude(transportMain.getDestLatitudeValue());
                tytMapDict.setDestLongitude(transportMain.getDestLongitudeValue());

                tytMapDictService.saveMapDict(tytMapDict);

                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("操作成功");
            } else {
                rm.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
                rm.setMsg("货物信息不存在");
            }
        } catch (Exception e) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("操作失败");
        }
        return rm;
    }

    // 保存采集距离日志
    public void saveMapDictLog(String clientSign, TransportMain transportMain, String distance) {
        // 开关
        int transport_map_dict_log_onoff = tytConfigService.getIntValue("transport_map_dict_log_onoff", 0);
        if (transport_map_dict_log_onoff == 1 && transportMain != null && null != distance && !"".equals(
                distance)) {
            TytMapDictLog tytMapDictLog = new TytMapDictLog();
            if (clientSign.equals("2")) {
                tytMapDictLog.setDistance(new BigDecimal(distance).movePointRight(2).intValue());

            } else if (clientSign.equals("3")) {
                tytMapDictLog.setIosDistance(new BigDecimal(distance).movePointRight(2).intValue());
            }
            tytMapDictLog.setCtime(new Date());
            tytMapDictLog.setStartProvinc(transportMain.getStartProvinc());
            tytMapDictLog.setStartCity(transportMain.getStartCity());
            tytMapDictLog.setStartArea(transportMain.getStartArea());
            tytMapDictLog.setStartCoordX(transportMain.getStartCoordXValue());
            tytMapDictLog.setStartCoordY(transportMain.getStartCoordYValue());
            tytMapDictLog.setStartLatitude(transportMain.getStartLatitudeValue());
            tytMapDictLog.setStartLongitude(transportMain.getStartLongitudeValue());

            tytMapDictLog.setDestProvinc(transportMain.getDestProvinc());
            tytMapDictLog.setDestCity(transportMain.getDestCity());
            tytMapDictLog.setDestArea(transportMain.getDestArea());
            tytMapDictLog.setDestCoordX(transportMain.getDestCoordXValue());
            tytMapDictLog.setDestCoordY(transportMain.getDestCoordYValue());
            tytMapDictLog.setDestLatitude(transportMain.getDestLatitudeValue());
            tytMapDictLog.setDestLongitude(transportMain.getDestLongitudeValue());

            TytMapDict tytMapDict = new TytMapDict();
            BeanUtils.copyProperties(tytMapDictLog, tytMapDict);

            String mapKey = tytMapDictService.getMapKey(tytMapDict);
            tytMapDictLog.setMapKey(mapKey);
            double distances = DistanceUtil.distance(
                    new BigDecimal(tytMapDictLog.getStartLongitude()).movePointLeft(2).doubleValue(),
                    new BigDecimal(tytMapDictLog.getStartLatitude()).movePointLeft(2).doubleValue(),
                    new BigDecimal(tytMapDictLog.getDestLongitude()).movePointLeft(2).doubleValue(),
                    new BigDecimal(tytMapDictLog.getDestLatitude()).movePointLeft(2).doubleValue());
            tytMapDictLog.setStraightDistance(
                    new BigDecimal(distances).movePointLeft(1).setScale(0, BigDecimal.ROUND_HALF_UP)
                            .intValue());
            tytMapDictLog.setStrategy(0);
            tytMapDictLogService.add(tytMapDictLog);
        }
    }

    /**
     * 通过关键字搜索匹配的机器类型
     *
     * @param keyword
     * @return
     */
    @RequestMapping(value = "/searchMatches")
    @ResponseBody
    public ResultMsgBean searchMatches(String keyword, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type begin, the keyword is [ " + keyword + " ], start time is [ "
                + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                List<TytMachineType> machineTypes = keywordMatchesService.queryMachineTypeByKeyword(keyword,
                        userId);
                rm.setData(machineTypes);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info("query machine type end, the keyword is [ " + keyword + " ], result is [ " + rm
                + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 通过关键字搜索匹配的机器类型
     *
     * @param keyword
     * @return
     */
    @RequestMapping(value = "/searchMatchesNew")
    @ResponseBody
    public ResultMsgBean searchMatchesNew(String keyword, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type new begin, the keyword is [ " + keyword + " ], start time is [ "
                + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                List<TytMachineTypeNew> machineTypes = keywordMatchesNewService.queryMachineTypeByKeyword(
                        keyword, userId);
                rm.setData(machineTypes);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info("query machine type new end, the keyword is [ " + keyword + " ], result is [ " + rm
                + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 6000版本 通过关键字搜索匹配的机器类型
     *
     * @param keyword
     * @return
     */
    @RequestMapping(value = {"/search/matches/group/v6000", "/search/matches/group/v6000.action"})
    @ResponseBody
    public ResultMsgBean searchMatchesGroupV6000(String keyword, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type group version 6000 begin, the keyword is [ " + keyword
                + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isBlank(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                String standardName = tytKeywordShortTransformService.queryKeywordShortByKeyword(keyword);
                List<TytMachineTypeBrandNew> mType = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(
                        standardName, userId);
                if (CollectionUtils.isNotEmpty(mType)) {
                    rm.setData(mType);
                } else {
                    List<TytMachineTypeBrandNew> machineTypes = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(
                            keyword, userId);
                    rm.setData(machineTypes);
                }
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        //logger.info("query machine type group version 6000 end, the keyword is [ " + keyword + " ], result is [ " + rm + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    //    /**
    //     * 6380版本 通过关键字搜索匹配的机器类型
    //     *
    //     * @param keyword
    //     * @return
    //     */
    //    @RequestMapping(value = {"/search/matches/group/v6380"})
    //    @ResponseBody
    //    public ResultMsgBean searchMatchesGroupV6380(@RequestParam("keyword") String keyword, @RequestParam("userId") String userId) throws Exception {
    //
    //        MachineTypeVo machineTypeVo = new MachineTypeVo();
    //        if (org.apache.commons.lang3.StringUtils.isNotBlank(keyword)) {
    //            // 只能输入字母、数字、汉字
    //            String regex = "[^a-zA-Z0-9\u4e00-\u9fa5]";
    //            keyword = keyword.replaceAll(regex, "");
    //            if (org.apache.commons.lang3.StringUtils.isNotBlank(keyword)) {
    //                machineTypeVo.setKeyword(keyword);
    //                String standardName = tytKeywordShortTransformService.queryKeywordShortByKeyword(keyword);
    //                List<TytMachineTypeBrandNew> machineTypeBrandNewList = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(standardName, userId);
    //                if (CollectionUtils.isEmpty(machineTypeBrandNewList)) {
    //                    machineTypeBrandNewList = machineTypeBrandNewService.queryMachineTypeGroupByKeyword(keyword, userId);
    //                }
    //                if (CollectionUtils.isNotEmpty(machineTypeBrandNewList)) {
    //                    machineTypeVo.setMachineType(MachineTypeEnum.conformance.code);
    //                    machineTypeVo.setList(machineTypeBrandNewList);
    //                } else {
    //                    String contentKeyValue = transportNullifyService.verifyNullifyTaskContent(keyword);
    //                    if (org.apache.commons.lang3.StringUtils.isNotBlank(contentKeyValue)) {
    //                        machineTypeVo.setMachineType(MachineTypeEnum.illegal.code);
    //                    } else {
    //                        machineTypeVo.setMachineType(MachineTypeEnum.audit.code);
    //                    }
    //                }
    //            }
    //        }
    //
    //        return ResultMsgBean.successResponse(machineTypeVo);
    //    }

    /**
     * 6420版本，添加品牌和型号检索
     *
     * @param keyword 检索名称
     * @param brand   检索名称
     * @param topType 型号
     * @return
     */
    @RequestMapping(value = {"/search/matches/group/v6380"})
    @ResponseBody
    public ResultMsgBean searchMatchesGroupV6420(@RequestParam("keyword") String keyword,
                                                 @RequestParam(value = "brand",required = false) String brand,
                                                 @RequestParam(value = "topType",required = false) String topType,
                                                 @RequestParam(value = "userId",required = false) String userId) throws Exception {


        if (org.apache.commons.lang3.StringUtils.isBlank(keyword)) {
            return ResultMsgBean.successResponse(new MachineTypeVo());
        }
        // 只能输入字母、数字、汉字
        String regex = "[^a-zA-Z0-9\u4e00-\u9fa5]";
        keyword = keyword.replaceAll(regex, "");

        if (org.apache.commons.lang3.StringUtils.isBlank(keyword)) {
            return ResultMsgBean.successResponse(new MachineTypeVo());
        }
        return ResultMsgBean.successResponse(machineTypeBrandNewService.searchMatchesName(keyword, brand, topType, userId));
    }

    /**
     *
     *
     * @param brand 检索名称
     * @return
     */
    @RequestMapping(value = {"/search/topType"})
    @ResponseBody
    public ResultMsgBean getTopType(@RequestParam(value = "brand", required = false) String brand) throws Exception {

        return ResultMsgBean.successResponse(machineTypeBrandNewService.getTopType(brand));
    }




    /**
     * 6000版本 通过关键字搜索匹配的机器类型
     *
     * @param keyword
     * @return
     */
    @RequestMapping(value = "/search/matches/v6000")
    @ResponseBody
    public ResultMsgBean searchMatchesV6000(String keyword, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info(
                "query machine type version 6000 begin, the keyword is [ " + keyword + " ], start time is [ "
                        + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else if (keyword.length() > 100) {
                rm.setCode(ReturnCodeConstant.MORE_THAN_LIMIT);
                rm.setMsg("keyword超出长度限制");
            } else {
                String sName = tytKeywordShortTransformService.queryKeywordShortByKeyword(keyword);
                List<TytMachineTypeBrandNew> mts = machineTypeBrandNewService.queryMachineTypeByKeyword(sName,
                        userId);
                if (mts != null) {
                    rm.setData(mts);
                } else {
                    List<TytMachineTypeBrandNew> machineTypes = machineTypeBrandNewService.queryMachineTypeByKeyword(
                            keyword, userId);
                    rm.setData(machineTypes);
                }
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info(
                "query machine type version 6000 end, the keyword is [ " + keyword + " ], result is [ " + rm
                        + " ] waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 获取货物推荐货物信息
     *
     * @return
     */
    @RequestMapping(value = "/searchRecommendation")
    @ResponseBody
    public ResultMsgBean searchRecommendation() {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query recommendation begin, start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            List<TytCommonUserMachineType> commonUserMachineTypes = commonUserMachineTypeService.queryCommonUseMachineType();
            rm.setData(commonUserMachineTypes);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info("query recommendation end, result is [ " + rm + " ],  waste time is [ " + (
                System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 获取货物推荐货物信息
     *
     * @return
     */
    @RequestMapping(value = "/searchRecommendationNew")
    @ResponseBody
    public ResultMsgBean searchRecommendationNew(BaseParameter baseParameter) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query recommendation new begin, start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            int type = 1;
            if (Integer.parseInt(baseParameter.getClientVersion()) >= 6000) {
                type = 2; // 新推荐
            }
            List<TytCommonUserMachineTypeNew> commonUserMachineTypes = commonUserMachineTypeNewService.queryCommonUseMachineType(
                    type);
            rm.setData(commonUserMachineTypes);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info("query recommendation new end, result is [ " + rm + " ],  waste time is [ " + (
                System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 保存用户选择的匹配项信息
     *
     * @param keyword
     * @param matchItemId
     * @return
     */
    @RequestMapping(value = "/saveMatchItem")
    @ResponseBody
    public ResultMsgBean saveMatchItem(String keyword, String matchItemId, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("save match item begin, start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("操作成功");
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else if (org.apache.commons.lang.StringUtils.isEmpty(matchItemId)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("matchItemId不能为空");
            } else {
                matchesClickService.add(new TytMatchesClick(Integer.valueOf(matchItemId), keyword, new Date(),
                        userId == null ? null : Integer.valueOf(userId)));
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info("save match item begin end, result is [ " + rm + " ], waste time is [ " + (
                System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    @RequestMapping(value = "/standardStatus")
    @ResponseBody
    public ResultMsgBean standardStatus(String keyword) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query standard status begin, the keyword is [ " + keyword + " ], start time is [ "
                + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                StandardStatusBean standardStatusBean = keywordMatchesNewService.queryStandardStatusByKeyword(
                        keyword);
                rm.setData(standardStatusBean);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        logger.info("query standard status end, the keyword is [ " + keyword + " ], result is: " + rm
                + ", waste time is [ " + (System.currentTimeMillis() - startTimeInMilli) + " ]");
        return rm;
    }

    /**
     * 对货物信息分词处理 IKAnalyzer 最小粒度分词
     *
     * @param taskContent
     * @return 分词的列表
     */
    private List<String> getKeywordByTaskContent(String taskContent) {
        if (taskContent == null || taskContent.length() <= 0) {
            return null;
        }
        List<String> keywords = new ArrayList<String>();
        IKSegmenter ik = new IKSegmenter(new StringReader(taskContent), false);
        try {
            Lexeme word = null;
            while ((word = ik.next()) != null) {
                // 排除: 纯数字、数字+单位
                if (!isNumericUnit(word.getLexemeText())) {
                    keywords.add(word.getLexemeText());
                }
            }
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
        logger.info("分割的词语：{}", keywords.toString());
        return keywords;
    }

    private static boolean isNumericUnit(String str) {
        if (str == null || str.equals("")) {
            return false;
        }
        Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?[米,吨,台,座,辆,号,个,件,元]*");
        return pattern.matcher(str).matches();
    }

    /**
     * 货物运价计算 运价= 固定成本+燃油费+高速费
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/goodsCostCalc")
    @ResponseBody
    public ResultMsgBean goodsCostCalc(BaseParameter baseParameter, String startProvinc, String startCity,
                                       String startArea, String destProvinc, String destCity, String destArea, String cargoLength,
                                       String weight, String taskContent, String machineNumber) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
        logger.info("调用接口------goodsCostCalc");
        // 参数校验 线路详情从表中获取 !StringUtils.hasLength(lineDetail) ||
        // !StringUtils.hasLength(totalDistance) || String lineDetail,String
        // totalDistance,
        if (!StringUtils.hasLength(startProvinc) || !StringUtils.hasLength(startCity)
                || !StringUtils.hasLength(startArea) || !StringUtils.hasLength(destProvinc)
                || !StringUtils.hasLength(destCity) || !StringUtils.hasLength(destArea)
                || !StringUtils.hasLength(weight) || !StringUtils.hasLength(cargoLength)) {
            rm.setMsg("关键参数缺失");
            return rm;
        }
        if (Double.valueOf(cargoLength) <= 0) {
            rm.setMsg("货物长度必须大于0");
            return rm;
        }
        if (Double.valueOf(weight) <= 0) {
            rm.setMsg("吨位必须大于0");
            return rm;
        }
        if (!StringUtils.hasLength(machineNumber)) {
            machineNumber = "1";
        }


        //TODO 根据出发地目的地获取收费线路详情，获取失败则直接返回0；
        String mapKey = StringUtil.getMapKey(startProvinc, startCity, startArea, destProvinc, destCity,
                destArea);
        //		FreightRecord fr = new FreightRecord();
        FreightRoute fr = new FreightRoute();

        fr.setMapKey(mapKey);
        fr.setStatus(Short.parseShort("0"));
        fr = this.freightRouteService.find(fr);
        if (fr == null || fr.getDistance() == null || fr.getProvinceRoad() == null) {
            rm.setMsg("货物详情获取失败，未从高德获取数据");
            return rm;
        }
        String lineDetail = fr.getProvinceRoad();
        String totalDistance = String.valueOf(fr.getDistance());
        if (!StringUtils.hasLength(lineDetail) || !StringUtils.hasLength(totalDistance) || "null".equals(
                totalDistance)) {
            rm.setMsg("货物详情获取失败，未从高德获取数据");
            return rm;
        }
        // 还原正常数值后进行计算
        totalDistance = String.valueOf(HighwayCostCalcUtil.calcDivide(totalDistance, "100"));
        Float cargoLengthFloat = Float.valueOf(cargoLength);
        Float weightFloat = Float.valueOf(weight);
        // 如果台数大于1，则吨位和长度，都乘以台数计算
        if (Integer.valueOf(machineNumber) > 1) {
            cargoLengthFloat = HighwayCostCalcUtil.calcMultiply(cargoLength, machineNumber).floatValue();
            weightFloat = HighwayCostCalcUtil.calcMultiply(weight, machineNumber).floatValue();
        }
        TytFreightDetailInfo freightDetail = new TytFreightDetailInfo();
        freightDetail.setCtime(new Date());
        freightDetail.setCargoLength(cargoLengthFloat);
        freightDetail.setStartPoint(startProvinc + startCity + startArea);
        freightDetail.setDestPoint(destProvinc + destCity + destArea);
        //此段代码非常危险，请注意
        //为了兼容android声明距离非小数，进行一次类型转换把小数去除
        int tempDistiance = Float.valueOf(totalDistance).intValue();
        freightDetail.setDistance(Float.valueOf(tempDistiance));
        freightDetail.setMtime(new Date());
        freightDetail.setProvinceRoad(lineDetail);
        freightDetail.setTaskContent(taskContent + "[" + machineNumber + "]台");
        freightDetail.setTonne(weightFloat);
        // 先计算燃油费用，因为需要获取车辆自重，累加后计算高速费用

        // 计算燃油费用、固定成本
        freightDetail = freightDetailService.calcBaseOilCost(freightDetail, Float.toString(cargoLengthFloat),
                Float.toString(weightFloat), totalDistance);
        if (freightDetail == null) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("计算燃油费、固定成本失败");
            return rm;
        }

        if (freightDetail.getSelfTonne() == null) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("车辆自重获取失败");
            return rm;
        }
        // 高速费需要车货总重来计算价格，自重加上货物重量
        Float totalTonne = HighwayCostCalcUtil.calcInCrease(String.valueOf(freightDetail.getTonne()),
                String.valueOf(freightDetail.getSelfTonne())).floatValue();
        if (totalTonne == null || totalTonne <= 0) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("车辆总重计算失败");
            return rm;
        }
        if (!StringUtils.hasLength(machineNumber)) {
            machineNumber = "1";
        }
        // 计算高速费用
        freightDetail = freightDetailService.calcHighwayCost(freightDetail, totalTonne, totalDistance,
                lineDetail, Integer.valueOf(machineNumber));

        if (freightDetail == null) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("计算高速运费失败");
            return rm;
        }

        // 保存计算记录
        freightDetailService.add(freightDetail);
        rm.setData(freightDetail);
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("操作成功");
        return rm;
    }

    //
    //	private String getMapKey(String startProvinc, String startCity, String startArea, String destProvinc, String destCity, String destArea){
    //		StringBuffer sb = new StringBuffer();
    //		sb.append(startProvinc).append(startCity).append(startArea).append(destProvinc).append(destCity).append(destArea);
    //		return MD5Util.GetMD5Code(sb.toString());
    //	}

    /**
     * 获得货物的hashcode(新规则),标准货源的判重标准：用户id+出发地+目的地+标准货物编号（只针对发布中的货源进行判重）
     * 非标准货源的判重标准：用户id+出发地+目的地+货物内容（只针对发布中的货源进行判重， 暂时不考虑相似度问题）
     *
     * @param transport
     * @return
     */
    private String getNewHashCode(Transport transport) {
        String code = "";
        //如果IsStandard==0 为标准化货源   1为非标货源
        if (transport.getIsStandard() == 0) {
            code = transport.getUserId() + transport.getStartPoint() + transport.getDestPoint() +
                    transport.getMatchItemId();
        } else {
            code = transport.getUserId() + transport.getStartPoint() + transport.getDestPoint() +
                    StringUtil.filterBlank(StringUtil.filterPunctuation(
                            StringUtil.filterEmotion(transport.getTaskContent())));
        }
        return code.hashCode() + "";
    }

    private boolean checkPhone(String tel, Long userId, String registPhone) {
        boolean isPhone = true;
        if (org.apache.commons.lang.StringUtils.isNotBlank(tel)) {
            if (tel.equals(registPhone)) {
                return isPhone;
            } else {
                isPhone = userTelService.get(userId, tel);
                return isPhone;
            }
        } else {
            return false;
        }
    }

    /**
     * 本接口本来是给5600使用，但是后来由于产品设计原因又改回原有规则，该接口目前处于无用状态
     *
     * @param goodId
     * @return
     */
    @RequestMapping(value = "/getPhoneNew")
    @ResponseBody
    public ResultMsgBean getPhoneNew(String goodId, String userId, String clientVersion,
                                     String clientSign, String moduleType, String isNeedPhone) {
        ResultMsgBean rm = new ResultMsgBean();
        logger.info("get phone by goodId, params is goodId: " + goodId + ", userId: " + userId
                + ", clientVersion: "
                + clientVersion + ", clientSign: " + clientSign + ", moduleType: " + moduleType
                + ", isNeedPhone: " + isNeedPhone);
        rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(goodId)) {
                rm.setMsg("goodId不能为空");
            } else if (org.apache.commons.lang.StringUtils.isEmpty(userId)) {
                rm.setMsg("userId不能为空");
            } else if (org.apache.commons.lang.StringUtils.isEmpty(moduleType) || (!"1".equals(moduleType)
                    && !"2".equals(moduleType))) {
                rm.setMsg("moduleType不能为空且只能为1或2");
            } else {
                // 查询电话信息
                CallLogBeanNew callLogBeanNew = transportService.getPhoneByGoodIdNew(userId, goodId,
                        clientVersion, clientSign, moduleType, isNeedPhone);
                // 加密电话信息
                //encyptPhone(callLogBeanNew, clientVersion);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("操作成功");
                rm.setData(callLogBeanNew);
                logger.info("get phone new transport recommend isCanCall value is: "
                        + callLogBeanNew.getIsCanCall());
                // 只有成功获取了电话才记录
                if (callLogBeanNew.getIsCanCall().intValue() == 0) {
                    // 如果是人工派单货源则记录拨打电话信息（专门用户后台人工派单列表按照车方电话过滤使用）
                    TransportMain transportMain = transportMainService.getById(callLogBeanNew.getSrcMsgId());
                    /** 特运通账户ID */
                    String companyAccountUserId = configService.getStringValue("tyt_company_account_user_id");
                    logger.info(
                            "get phone new transport recommend company user id is: " + companyAccountUserId
                                    + " , pub goods user id is: " + transportMain.getUserId());
                    if (companyAccountUserId != null && companyAccountUserId.equals(
                            transportMain.getUserId() + "")) {
                        TytCarOwnerQueryPhone carOwnerQueryPhone = new TytCarOwnerQueryPhone();
                        carOwnerQueryPhone.setTsSrcId(callLogBeanNew.getSrcMsgId());
                        carOwnerQueryPhone.setCarUserId(Long.valueOf(userId));
                        carOwnerQueryPhone = carOwnerQueryPhoneService.find(carOwnerQueryPhone);
                        // 为空才保存拨打记录
                        if (carOwnerQueryPhone == null) {
                            carOwnerQueryPhone = new TytCarOwnerQueryPhone();
                            User user = userService.getById(Long.valueOf(userId));
                            carOwnerQueryPhone.setCarLoginPhone(user.getCellPhone());
                            carOwnerQueryPhone.setCarUserId(Long.valueOf(userId));
                            carOwnerQueryPhone.setCtime(new Date());
                            carOwnerQueryPhone.setTsId(callLogBeanNew.getSrcMsgId());
                            carOwnerQueryPhone.setTsSrcId(callLogBeanNew.getSrcMsgId());
                            carOwnerQueryPhone.setUserId(transportMain.getUserId());
                            carOwnerQueryPhoneService.add(carOwnerQueryPhone);
                        }
                    }
                }
                /*
                 * 增加获取电话以及获取电话结果的日志信息, goodId 换成srcId
                 */
                userCallPhoneService.addGetPhoneLog(callLogBeanNew.getSrcMsgId().toString(), userId,
                        clientSign, clientVersion, moduleType, callLogBeanNew.getIsCanCall());
                logger.info("get phone new by goodId, result is: " + callLogBeanNew);
            }
        } catch (Exception e) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("查询失败");
            logger.error("get phone by goodId failed, the error message is: ", e);
        }
        return rm;
    }


    private void encyptPhone(CallLogBean callLogBean, String clientVersion) {
        // 获取需要进行登录错误次数控制的最小版本（不包括）
        String restriceLoginFailMinVersion = tytConfigService
                .getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5301");
        int encyptPhoneMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
        int clientVersionInt = Integer.valueOf(clientVersion).intValue();
        // 是否需要对电话加密 1 需要 2 不需要
        int isNeedEncypt = configService.getIntValue(Constant.IS_NEED_ENCYPT_KEY, 1);
        callLogBean.setIsNeedDecrypt(isNeedEncypt);
        logger.info("isNeedEncypt == 1 && (clientVersionInt > restriceLoginFailMinVersionInt): " + (
                isNeedEncypt == 1 && (clientVersionInt > encyptPhoneMinVersionInt)));
        if (isNeedEncypt == 1 && (clientVersionInt > encyptPhoneMinVersionInt)) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(callLogBean.getTel())) {
                callLogBean.setTel(
                        XXTea.Encrypt(callLogBean.getTel(), AppConfig.getProperty("tyt.xxtea.key")));
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(callLogBean.getTel3())) {
                callLogBean.setTel3(
                        XXTea.Encrypt(callLogBean.getTel3(), AppConfig.getProperty("tyt.xxtea.key")));
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(callLogBean.getTel4())) {
                callLogBean.setTel4(
                        XXTea.Encrypt(callLogBean.getTel4(), AppConfig.getProperty("tyt.xxtea.key")));
            }
        }
    }

    @RequestMapping(value = "/save.action")
    public void saveAction(HttpServletRequest request, HttpServletResponse response) {
        this.save(request, response);
    }

    /* 判断是不是数字 */
    private boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * pc信息费获取电话接口
     *
     * @param goodsId
     * @param goodsIdEncrypt
     * @param userId
     * @param isNeedPhone
     * @param clientVersion
     * @param clientSign
     * @param moduleType
     * @return
     */
    @RequestMapping(value = "/getPhone.action")
    @ResponseBody
    public ResultMsgBean getPhoneForPc(String goodsId, String goodsIdEncrypt, String userId,
                                       String isNeedPhone, String clientVersion, String clientSign, String moduleType) {
        ResultMsgBean rm = new ResultMsgBean();
        String goodsIdDecrypt = XXTea.Decrypt(goodsIdEncrypt, AppConfig.getProperty("tyt.xxtea.key"));
        if (!goodsId.equals(goodsIdDecrypt)) {
            rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            rm.setMsg("参数有误");
            logger.error("pc信息费获取电话，加密goodsId与原goodsId不一致，userId:" + userId);
            return rm;
        }
        rm = this.getPhone(goodsIdDecrypt, userId, isNeedPhone, clientVersion, clientSign, moduleType);
        return rm;
    }

    /**
     * 货物信息页货物参考列表
     *
     * @param goodsId
     * @return
     */
    @RequestMapping(value = "/getGoodsReferList")
    @ResponseBody
    public ResultMsgBean getGoodsReferList(@RequestParam(value = "goodsId", required = true) Long goodsId) {
        ResultMsgBean result = new ResultMsgBean();

        try {
            //1.根据货物id查找此条货物的machineId
            logger.error("货物信息页货物参考列表  id=" + goodsId);
            TransportMain bean = transportMainService.getBySrcMsgId(goodsId);
            logger.error("货物信息页货物参考列表 bean=" + bean);
            List<TytMachineTypeBean> list = new ArrayList<>();
            if (bean != null) {
                //2.如果machineId>10000到tyt_machine_type_new 中查 brand_type ,在根据brand_type到tyt_machine_type中查询参数
                if (bean.getMatchItemId() > 10000) {
                    TytMachineTypeNew machineType = machineTypeNewService.getTytMachineTypeForId(
                            Long.valueOf(bean.getMatchItemId()));
                    list = machineTypeService.getListByBrandType(machineType.getBrandType());
                } else if (bean.getMatchItemId() <= 10000 && bean.getMatchItemId() > -1) {
                    //3.如果machineId<10000 直接到tyt_machine_type中查询所需数据
                    TytMachineType typeForId = machineTypeService.getTytMachineTypeForId(
                            Long.valueOf(bean.getMatchItemId()));
                    TytMachineTypeBean machineType = new TytMachineTypeBean();
                    BeanUtils.copyProperties(typeForId, machineType);
                    list.add(machineType);
                }
                result.setCode(ResultMsgBean.OK);
                result.setData(list);
                result.setMsg("查询成功");
            } else {
                result.setCode(ResultMsgBean.ERROR);
                result.setMsg("货物id异常");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ResultMsgBean.ERROR);
            result.setMsg("服务器异常");
        }

        return result;
    }

    /**
     * 货物详情页推荐列表
     */
    @RequestMapping(value = "/DetailRecommend")
    @ResponseBody
    public ResultMsgBean getDetailRecommendList(BaseParameter baseParameter, Long goodsId, @RequestParam(defaultValue = "1", required = false) Integer pageNum, @RequestParam(defaultValue = "30", required = false) Integer pageSize) {
        ResultMsgBean result = ResultMsgBean.successResponse();
        try {
            Long userId = baseParameter.getUserId();

            String env = tytConfigService.getStringValue("tyt:config:environment", "dev");

            ResultMsgBean resultMsgBean = getDetailRecommendFromBi(userId, env, goodsId, pageNum, pageSize);
            Object data = resultMsgBean.getData();
            List<TransportBean> transportBeans = JSON.parseArray(data == null ? "" : data.toString(),
                    TransportBean.class);
            if (!CollectionUtils.isEmpty(transportBeans)) {
                transportBeans.forEach(it -> {
                    it.initStartLongitudeStr();
                    it.initStartLatitudeStr();

                    // 替换手机号
                    it.setNickName(StringUtil.hidePhoneInStr(it.getNickName()));

                    UserPermissionResult permission = RedisUtil.getObject(USER_PERMISSION_REDIS_KEY + userId);
                    dealWithPermission(it, permission);

                    // 加密官方授权昵称
                    String authNameTea = tytOwnerAuthService.getAuthNameTea(it.getAuthName());
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(authNameTea)) {
                        it.setAuthNameTea(authNameTea);
                        it.setAuthName(null);
                    }

                });

                handleTransportFields(userId, transportBeans);
                resultMsgBean.setData(transportBeans);
                return resultMsgBean;
            }
        } catch (Exception e) {
            logger.error("", e);
            result.setCode(ResultMsgBean.ERROR);
            result.setMsg("服务器异常");
        }
        return result;
    }

    private ResultMsgBean getDetailRecommendFromBi(Long userId, String env, Long goodsId, Integer pageNum, Integer pageSize) {
        // bi环境参数: 1：test环境；2：dev环境；3：release环境；不传或者0为生产环境
        String runType;
        switch (env) {
            case "test":
                runType = "1";
                break;
            case "online":
                runType = "0";
                break;
            case "release":
                runType = "3";
                break;
            case "dev":
            default:
                runType = "2";
                break;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("run_type", runType);
        params.put("goodsId", goodsId);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        ResultMsgBean resultMsgBean = biRequestService.doPost(IdcApi.GOODS_DETAIL_RECOMMEND_PATH, params,false);

        if (!Objects.equals(200, resultMsgBean.getCode())) {
            // 2001 货源信息有误, 正常返回, 用于区分环境
            resultMsgBean.setData(Collections.emptyList());
        }
        return resultMsgBean;
    }

    public void dealWithPermission(TransportBean transport, UserPermissionResult permission) {
        boolean namePermission =
                permission != null && permission.getIsUserNamePower() == 1; //是否有查看用户名的权益 1有 0没有
        boolean contentPermission = permission != null && permission.getIsContentPower() == 1;

        String[] firstInfo =
                org.apache.commons.lang3.StringUtils.isBlank(transport.getSimilarityFirstInfo()) ? null
                        : transport.getSimilarityFirstInfo().split("##");

        String nickName = Optional.ofNullable(transport.getNickName()).orElse("");

        if (namePermission) {

            if (nickName.contains(transport.getUserId() + "")) {
                // 昵称有包含用户ID 进行修改设置
                nickName = Constant.default_nick_name;
            }
        } else {
            nickName = Constant.REPLACEMENT_STARTS;
            if (firstInfo != null) {
                firstInfo[1] = nickName;
            }
            // 将官方授权昵称置为空，不让看
            transport.setAuthNameTea(null);
        }

        String xxteaKey = AppConfig.getProperty("tyt.xxtea.key");
        //        String aesName = AESUtil.enCode(nickName, xxteaKey);

        transport.setNickName(XXTea.Encrypt(nickName, xxteaKey));
        //        transport.setNickNameByAes(aesName);

        transport.setIsNeedDecrypt(1);

        if (!contentPermission) {
            transport.setTaskContent(Constant.REPLACEMENT_STARTS_CONTENT);
            transport.setWeight(null);
            if (firstInfo != null) {
                firstInfo[0] = Constant.REPLACEMENT_STARTS_CONTENT;
            }
        }
        transport.setSimilarityFirstInfo(org.apache.commons.lang3.StringUtils.join(firstInfo, "##"));
    }

    @Autowired
    private TytTransportEnterpriseLogMapper transportEnterpriseLogMapper;
    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;
    @Autowired
    private GoodsRefreshManualService goodsRefreshManualService;
    @Autowired
    private CoverGoodsLogMapper coverGoodsLogMapper;
    @Autowired
    private NewIdentityMapper newIdentityMapper;

    /**
     * 处理货源字段，如给专票货源打标；给专车货源打标
     *
     * @param transportBaseList
     */
    public void handleTransportFields(Long userId, List<TransportBean> transportBaseList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(transportBaseList)) {
            return;
        }
        // 给专票货源打标
        List<Long> srcMsgIds = transportBaseList.stream().
                filter(t -> Objects.equals(t.getInvoiceTransport(), 1))
                .map(TransportBean::getSrcMsgId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(srcMsgIds)) {
            Map<Long, Long> valueMap = transportEnterpriseLogMapper.getInvoiceSubjectIdBySrcMsgIds(srcMsgIds).stream()
                    .collect(Collectors.toMap(TytTransportEnterpriseLog::getSrcMsgId, TytTransportEnterpriseLog::getInvoiceSubjectId));
            transportBaseList.forEach(t -> t.setInvoiceSubjectId(valueMap.get(t.getSrcMsgId())));
        }

        // 给专车货源打标
        srcMsgIds = transportBaseList.stream().
                filter(t -> Objects.equals(t.getExcellentGoods(), 2))
                .map(TransportBean::getSrcMsgId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(srcMsgIds)) {
            Map<Long, Integer> valueMap = tytTransportMainExtendMapper.getUseCarTypeBySrcMsgIds(srcMsgIds).stream()
                    .collect(Collectors.toMap(TytTransportMainExtend::getSrcMsgId, TytTransportMainExtend::getUseCarType));
            transportBaseList.forEach(t -> t.setUseCarType(valueMap.get(t.getSrcMsgId())));
        }

        // 该货源是否对车主捂货
        srcMsgIds = transportBaseList.stream()
                .filter(t -> t.getPriorityRecommendExpireTime() != null && t.getPriorityRecommendExpireTime().after(new Date()))
                .map(TransportBean::getSrcMsgId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(srcMsgIds)) {
            Set<Long> coverGoodsIds = coverGoodsLogMapper.getCoverGoodsIds(userId, srcMsgIds);
            transportBaseList.forEach(t -> t.setCoverGoodsType(coverGoodsIds.contains(t.getSrcMsgId()) ? 1 : 0));
        }

        // 只有好货展示读秒货源标签，非好货把捂货过期时间置为空就隐藏了
        for (TransportBean transportListVO : transportBaseList) {
            if (transportListVO.getLabelJson() == null || !transportListVO.getLabelJson().contains("\"iGBIResultData\":1")) {
                transportListVO.setPriorityRecommendExpireTime(null);
            }
        }

        // 是否显示超额保障标签：直客发布的货源或优车2.0或抽佣货源
        List<Long> userIds = transportBaseList.stream().map(TransportBean::getUserId).collect(Collectors.toList());
        List<NewIdentity> newIdentities = newIdentityMapper.batchGetByUserId(userIds);
        Map<Long, Integer> newIdentityMap = newIdentities.stream().collect(Collectors.toMap(NewIdentity::getUserId, NewIdentity::getType));
        transportBaseList.forEach(t -> {
            TransportLabelJson labelJson = JSON.parseObject(t.getLabelJson(), TransportLabelJson.class);
            t.setShowExcessCoverageLabel(shouldShowExcessCoverageLabel(labelJson, newIdentityMap.get(t.getUserId())) ? 1 : 0);
            t.setShowDepositCouponsLabel(getShowDepositCouponsLabel(t.getShowExcessCoverageLabel()));
        });
    }

    /**
     * 判断是否应该显示超额投保标签
     */
    private boolean shouldShowExcessCoverageLabel(TransportLabelJson labelJson, Integer userType) {
        // 优车2.0 或 抽佣货源
        if (labelJson != null && (Objects.equals(labelJson.getGoodCarPriceTransport(), 1)
                || Objects.equals(labelJson.getCommissionTransport(), 1))) {
            return true;
        }
        // 或 直客发布货源（直客=个人货主或企业货主）
        return Objects.equals(userType, 3) || Objects.equals(userType, 4);
    }

    /**
     * 获取是否显示优惠券标签
     * 1. 条件：直客（个人货主或企业货主）发布的货源 或优车2.0 或抽佣货源
     * 2. 需要开关关闭
     */
    private Integer getShowDepositCouponsLabel(Integer showExcessCoverageLabel) {
        if (Objects.equals(showExcessCoverageLabel, 1)
                && tytConfigService.getIntValue("show_deposit_coupons_label_switch", 0) == 1) {
            return 1;
        }
        return 0;
    }
}