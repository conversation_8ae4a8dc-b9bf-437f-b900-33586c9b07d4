package com.tyt.transport.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.transport.service.CoverGoodsConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 获取捂货配置
 *
 * @author: helian
 * @since: 2024/01/12 20:05
 */
@RestController
@RequestMapping("/cover/goods")
public class CoverGoodsController {

    @Autowired
    private CoverGoodsConfigService coverGoodsConfigService;

    /**
     * 获取捂货配置列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResultMsgBean getConfigList() {

        return ResultMsgBean.successResponse(coverGoodsConfigService.getConfigList());
    }

}
