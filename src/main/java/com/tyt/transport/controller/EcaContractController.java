package com.tyt.transport.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.EcaContract;
import com.tyt.model.EcaProgress;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.transport.querybean.CertIdentityBean;
import com.tyt.transport.service.EcaContractService;
import com.tyt.transport.service.EcaProgressService;
import com.tyt.transport.service.EcaUserProgressService;
import com.tyt.transport.service.UserIdentityCertService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.LockUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 电子合同接口
 *
 * <AUTHOR>
 *         create by 20180110
 */

@Controller
@RequestMapping("/plat/contract")
public class EcaContractController extends BaseController {

    @Resource(name = "ecaContractService")
    private EcaContractService ecaContractService;
    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService tytUserIdentityAuthService;
    @Resource(name = "userIdentityCertService")
    private UserIdentityCertService userIdentityCertService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "ecaProgressService")
    private EcaProgressService ecaProgressService;
    @Resource(name = "ecaUserProgressService")
    EcaUserProgressService ecaUserProgressService;

    @RequestMapping(value = "/getContractDetail.action")
    @ResponseBody
    public ResultMsgBean getContractDetail(@RequestParam(value = "contractId", required = true) Long contractId,
                                           @RequestParam(value = "userId", required = true) Long userId,
                                           @RequestParam(value = "currUserId", required = false) Long currUserId,
                                           @RequestParam(value = "userName", required = false) String userName,
                                           @RequestParam(value = "device", required = false) String device,
                                           @RequestParam(value = "imei", required = false) String imei,
                                           @RequestParam(value = "ip", required = false) String ip,
                                           HttpServletRequest request, HttpServletResponse response
    ) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            //此段代码为了兼容后台manage调用plat接口模拟登录造成的userId参数重复。。。。。
            if (currUserId != null && currUserId.longValue() > 0) {
                userId = currUserId;
            }
            EcaContract ecacontract = ecaContractService.getById(contractId);
            EcaContract contract = new EcaContract();
            BeanUtils.copyProperties(ecacontract, contract);
            if (contract.getStatus() == 3 || contract.getStatus() == 4) {
                rm.setData(contract);
                return rm;
            }

            if (contract.getStatus() == 2) {
                rm.setData(contract);
            }
            if (contract.getStatus() == 1) {
                Long shipperUserId = contract.getShipperUserId();
                if (shipperUserId == null) {
                    User user = userService.getUserByCellphone(contract.getShipperPhone());
                    if (user != null) {
                        shipperUserId = user.getId();
                        ecaContractService.updateShipperUserId(shipperUserId, contractId);
                    }
                }
                CertIdentityBean shipperBean = userIdentityCertService.getCertIdentity(shipperUserId);
                if (shipperBean != null) {
                    contract.setShipperUserName(shipperBean.getUserName());
                    contract.setShipperIdCard(shipperBean.getIdCard());
                    contract.setShipperType(shipperBean.getIdentityType());
                    if (shipperBean.getIdentityType() == 2) {
                        contract.setShipperName(shipperBean.getComName());
                    }
                }
                CertIdentityBean carryBean = userIdentityCertService.getCertIdentity(contract.getCarryUserId());
                if (carryBean != null) {
                    contract.setCarryUserName(carryBean.getUserName());
                    contract.setCarryIdCard(carryBean.getIdCard());
                }
                rm.setData(contract);
            }
            //添加查看记录
            Integer optHandler = 0;
            String ipAddress = StringUtil.getRealIp(request);
            if (userId.equals(contract.getShipperUserId())) {
                optHandler = 2;
                userName = contract.getShipperUserName();
            } else if (userId.equals(contract.getCarryUserId())) {
                optHandler = 3;
                userName = contract.getCarryUserName();
            } else {
                optHandler = 4;
                ipAddress = ip;
            }
            EcaProgress progress = new EcaProgress();
            progress.setContractId(contractId);
            progress.setOptType(3); // 查看
            progress.setOptHandler(optHandler);
            progress.setUserId(userId);
            progress.setUserName(userName);
            progress.setDevice(device);
            progress.setImei(imei);
            progress.setDetail("查看合同");
            progress.setIpAddress(ipAddress);
            progress.setStatus(1); // 进行中
            ecaProgressService.addEcaProgress(progress);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping(value = "/userAgreeAffix.action")
    @ResponseBody
    public ResultMsgBean userAgreeAffix(@RequestParam(value = "contractId", required = true) Long contractId,
                                        @RequestParam(value = "userId", required = true) Long userId,
                                        @RequestParam(value = "device", required = false) String device,
                                        @RequestParam(value = "imei", required = false) String imei,
                                        HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            ecaContractService.userAgreeAffix(contractId, userId, rm);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络繁忙，请稍后重试");
        }
        return rm;
    }

    @RequestMapping(value = "/userDoAffix.action")
    @ResponseBody
    public ResultMsgBean userDoAffix(@RequestParam(value = "contractId", required = true) Long contractId,
                                     @RequestParam(value = "userId", required = true) Long userId,
                                     @RequestParam(value = "identifyingCode", required = true) String identifyingCode,
                                     @RequestParam(value = "device", required = false) String device,
                                     @RequestParam(value = "imei", required = false) String imei,
                                     HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setMsg("签订成功");
        // 添加签约进度时，所需使用的参数
        String currentPhone = ""; // 当前用户手机号
        //添加签约记录
        Integer optHandler = 0; // 操作类型
        String ipAddress = StringUtil.getRealIp(request);
        String userName = "";
        String idCard = "";
        try {
            if (LockUtil.lockObject("1", "contract_" + contractId, 60)) {
                EcaContract ecaContract = ecaContractService.userDoAffix(contractId, userId, identifyingCode, rm);
                // 添加签约进度
                if (userId.equals(ecaContract.getShipperUserId())) {
                    optHandler = 2;
                    userName = ecaContract.getShipperUserName();
                    idCard = ecaContract.getShipperIdCard();
                    currentPhone = ecaContract.getShipperPhone();
                } else if (userId.equals(ecaContract.getCarryUserId())) {
                    optHandler = 3;
                    userName = ecaContract.getCarryUserName();
                    idCard = ecaContract.getCarryIdCard();
                    currentPhone = ecaContract.getCarryPhone();
                } else {
                    optHandler = 4;
                }
                addProgress(contractId, userId, device, imei, rm, currentPhone, optHandler, ipAddress, userName, idCard);
                // 如果双方签订，增加锁定记录，状态为3-生效，且成功时增加锁定记录，避免签订后再次进行签订产生重复记录
                if (ecaContract.getStatus().intValue() == 3 && rm.getCode().intValue() == 200) {
                    EcaProgress lastProgress = new EcaProgress();
                    lastProgress.setContractId(contractId);
                    lastProgress.setOptType(7); // 签约
                    lastProgress.setOptHandler(1);
                    lastProgress.setDetail("双方签字生效，合同锁定");
                    lastProgress.setStatus(2); // 进行中
                    ecaProgressService.addEcaProgress(lastProgress);
                    // 如果签订，记录用户进度
                    ecaUserProgressService.insertEcaUserProgress(ecaContract.getId(), ecaContract.getSrcMsgId(), 3);
                }
            }
        } catch (RuntimeException ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg(ex.getMessage());
            addProgress(contractId, userId, device, imei, rm, currentPhone, optHandler, ipAddress, userName, idCard);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络繁忙，请稍后重试");
            addProgress(contractId, userId, device, imei, rm, currentPhone, optHandler, ipAddress, userName, idCard);
        } finally {
            logger.info("contract userDoAffix finish release redis lock, contractId is: " + contractId);
            LockUtil.unLockObject("1", "contract_" + contractId);
        }
        return rm;
    }

    /**
     * 增加签约进度记录, 因catch内也需要记录失败原因，故而提次方法复用
     *
     * @param contractId
     * @param userId
     * @param device
     * @param imei
     * @param rm
     * @param currentPhone
     * @param optHandler
     * @param ipAddress
     * @param userName
     * @param idCard
     */
    private void addProgress(Long contractId,
                             Long userId,
                             String device,
                             String imei,
                             ResultMsgBean rm,
                             String currentPhone,
                             Integer optHandler,
                             String ipAddress,
                             String userName,
                             String idCard) {
        EcaProgress progress = new EcaProgress();
        progress.setContractId(contractId);
        progress.setOptType(7); // 签约
        progress.setOptHandler(optHandler);
        progress.setUserId(userId);
        progress.setUserName(userName);
        progress.setDevice(device);
        progress.setImei(imei);
        progress.setIpAddress(ipAddress);
        progress.setStatus(1); // 进行中
        String detail = "提交签名(" + idCard + "/" + userName + "/" + currentPhone + "),签名成功";
        if (rm.getCode() != 200) {
            detail = "提交签名(" + idCard + "/" + userName + "/" + currentPhone + "),签名失败：" + rm.getMsg();
        }
        progress.setDetail(detail);
        ecaProgressService.addEcaProgress(progress);
    }
}
