package com.tyt.transport.controller;

import cn.hutool.extra.emoji.EmojiUtil;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.model.TytSource;
import com.tyt.transport.bean.ComplaintRecordBean;
import com.tyt.transport.service.ComplaintRecordService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/4/16 16:11
 */
@Slf4j
@Controller
@RequestMapping("/plat/complaintRecord")
public class ComplaintController extends BaseController {

    final static String COMPLAINT_RECORD_PREFIX = "complaint_record_prerfix_";

    @Autowired
    private ComplaintRecordService complaintRecordService;
    @Autowired
    private TytConfigService configService;

    @Autowired
    private TransportMainService transportMainService;
    @Autowired
    private TransportService transportService;

    @RequestMapping("/save")
    @ResponseBody
    public ResultMsgBean save(ComplaintRecordBean recordBean,String typeValue){
        if (recordBean==null|| recordBean.getMsgId()==null|| StringUtils.isEmpty(recordBean.getReason())){
            return new ResultMsgBean(500,"参数有误");
        }

        if (StringUtils.isNotBlank(typeValue)){
            TytSource tytSource = TytSourceUtil.getSourceName("complaint_record_type", typeValue);
            if (tytSource == null) {
                return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "投诉类型有误");
            }
            recordBean.setTypeDesc(tytSource.getName());
        }

        String s = StringUtil.filterPunctuation(EmojiUtil.removeAllEmojis(recordBean.getReason()));
        recordBean.setReason(s);

        // 兼容APP入参
        recordBean.setMsgId(getSrcMsgId(recordBean.getMsgId()));

        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.ERROR, "操作失败");
        String redisKey = COMPLAINT_RECORD_PREFIX + recordBean.getMsgId() + "_" + recordBean.getComplainantId();
        try {
            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if(LockUtil.lockObject("1", redisKey, redisLockTimeout)){
                 resultMsgBean = complaintRecordService.addComplaintRecord(recordBean);
            }
            return resultMsgBean;
        } catch (Exception e) {
            log.error("保存货源投诉信息失败",e);
            return new ResultMsgBean(500,"服务器异常");
        }finally {
            LockUtil.unLockObject("1", redisKey);
        }
    }

    /**
     * 修复APP传来的货源id，可能会传srcMsgId，可能会传tsId
     */
    private Long getSrcMsgId(Long msgId) {
        TransportMain transportMain = transportMainService.getById(msgId);
        if (transportMain != null) {
            return transportMain.getSrcMsgId();
        }
        Transport transport = transportService.getById(msgId);
        if (transport != null) {
            return transport.getSrcMsgId();
        }
        return msgId;
    }

    @RequestMapping("/getByMsgId")
    @ResponseBody
    public ResultMsgBean save(Long msgId,Long userId){
        if (msgId==null){
            return new ResultMsgBean(500,"参数有误");
        }
        try {
            ComplaintRecordBean complaintRecordByMsgId = complaintRecordService.getComplaintRecordByMsgId(msgId,userId);
            if (complaintRecordByMsgId!=null){
                complaintRecordByMsgId.setReason(EmojiUtil.toUnicode(complaintRecordByMsgId.getReason()));
            }
            return new ResultMsgBean(200,"查询成功",complaintRecordByMsgId);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultMsgBean(500,"服务器异常");
        }
    }
}
