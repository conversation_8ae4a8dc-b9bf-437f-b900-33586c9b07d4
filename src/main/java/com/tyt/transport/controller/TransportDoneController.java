package com.tyt.transport.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.InfoFeeMyPublishBubbleResultBean;
import com.tyt.infofee.bean.ListDataBean;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqUserMsg;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.receive.service.TransportBackendService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.TransportDoneListBean;
import com.tyt.transport.service.BsPublishTransportService;
import com.tyt.transport.service.TransportDoneService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.CarCurrentLocationService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 货源成交相关类
 *
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/plat/transport/done")
public class TransportDoneController extends BaseController {

    @Resource(name = "tytBubbleService")
    TytBubbleService tytBubbleService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "transportDoneService")
    private TransportDoneService transportDoneService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Resource(name = "carCurrentLocationService")
    private CarCurrentLocationService carCurrentLocationService;

    @Resource(name = "tytNoticePopupTemplService")
    TytNoticePopupTemplService noticePopupTemplService;

    @Resource
    private TransportOrdersService transportOrdersService;

    @Autowired
    private TransportBackendService transportBackendService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;

    @Resource(name = "bsPublishTransportService")
    private BsPublishTransportService bsPublishTransportService;



    /**
     * 我的货源已成交列表查询接口
     *
     * @param userId
     * @param queryID
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @return
     * @date 2019-10-23
     */
    @RequestMapping(value = "list")
    @ResponseBody
    public ResultMsgBean getMyTransportDone(Long userId, Long queryID, Integer queryActionType, BaseParameter parameter) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            if(userId == null){
                logger.error("user_id_is_empty: " + com.gexin.fastjson.JSON.toJSONString(parameter));
                return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
            }
            if (queryID == null || queryID < 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("queryID参数错误");
                return resultMsgBean;
            }
            if (queryActionType == null || queryActionType < 1 || queryActionType > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("queryActionType参数错误");
                return resultMsgBean;
            }
            ListDataBean listDataBean = new ListDataBean();
            /* 获取列表数据 */
            long t1 = System.currentTimeMillis();
            List<TransportDoneListBean> goodsList = transportDoneService.getMyTransportDone(userId, queryActionType, queryID, parameter.getClientSign());
            listDataBean.setData(goodsList);
			/* 获取当前时间 */
            listDataBean.setCurrentTime(System.currentTimeMillis());
			/* 获取气泡数量 */
            List<InfoFeeMyPublishBubbleResultBean> myBubbleList = new ArrayList<InfoFeeMyPublishBubbleResultBean>();
            myBubbleList = tytBubbleService.getInfoFeeMyPublishBubbleResultBeanForUserIdNew(userId);
            listDataBean.setBubbleNumbers(myBubbleList);
            // 返回结果
            resultMsgBean.setData(listDataBean);
            long t2 = System.currentTimeMillis();
            logger.info("APP信息费我的货源查询时间【{}ms】;用户【{}】;下拉上滑【{}】;查询ID【{}】,返回值：{}", t2 - t1, userId, queryActionType, queryID, JSON.toJSONString(resultMsgBean));
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 查询车辆位置
     *
     * @param tsId
     * @return
     */
    @RequestMapping(value = "/getCarPosition")
    @ResponseBody
    public ResultMsgBean getCarPosition(@RequestParam(name = "tsId", required = true) Long tsId, String headCity, String headNo, String tailCity, String tailNo, BigDecimal dealPrice) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            TransportDone done = transportDoneService.getByTsId(tsId);
            if (done == null) {
                resultMsgBean.setCode(201);
                resultMsgBean.setMsg("该货源不存在");
                return resultMsgBean;
            }
            Date sevenDayAfter = TimeUtil.weeHours(TimeUtil.addDay(done.getCtime(), 7), 1);
            if (new Date().getTime() > sevenDayAfter.getTime()) {
                resultMsgBean.setCode(202);
                resultMsgBean.setMsg("已超出订单可查询时间");
                return resultMsgBean;
            }
            Long userId = done.getUserId();
            Long carryUserId = done.getCarryUserId();
            if (carryUserId == null) {
                User user = userService.getUserByCell(done.getCarryCellPhone());
                if (user == null){
                    resultMsgBean.setCode(203);
                    resultMsgBean.setMsg("对方为未注册用户，不能查看车辆位置");
                    return resultMsgBean;
                } else {
                    carryUserId = user.getId();
                }
            }
            boolean bexists = RedisUtil.existsObject(Constant.CACHE_EXPIRE_LOCATION_APPLY + tsId);
            if(bexists && done.getIsAllowLocation() == 2) {
                resultMsgBean.setCode(204);
                resultMsgBean.setMsg("申请次数过于频繁，请稍后再试");
                //返回申请中状态
                resultMsgBean.setExtraData(3);
                return resultMsgBean;
            }

            String carHeadCity = done.getHeadCity();
            String carHeadNo = done.getHeadNo();
            if (done.getIsAllowLocation() == 0 || done.getIsAllowLocation() == 1) {
                if (done.getIsAllowLocation() == 0 && StringUtils.isBlank(done.getHeadCity()) && StringUtils.isBlank(done.getHeadNo())) {
                    //老版本不兼容 等版本替换完成后加回校验(6070之前版本返回错误码 回弹网络连接异常 客户端已经在6070版本中做过更改)
                    // TODO: 2021/8/25 lizhao 6070之前版本返回错误码 回弹网络连接异常 客户端已经在6070版本中做过更改
//                    if(StringUtils.isBlank(headCity) || StringUtils.isBlank(headNo) || StringUtils.isBlank(tailCity) || StringUtils.isBlank(tailCity)){
//                        resultMsgBean.setCode(ReturnCodeConstant.SET_CAR_INFO_ERROR);
//                        resultMsgBean.setMsg("车辆信息未填写");
//                        return resultMsgBean;
//                    }
//                    if(dealPrice == null){
//                        resultMsgBean.setCode(ReturnCodeConstant.SET_CAR_INFO_ERROR);
//                        resultMsgBean.setMsg("运费信息未填写");
//                        return resultMsgBean;
//                    }
                    transportDoneService.updateChangCarStatus(tsId, headCity, headNo, tailCity, tailNo, carryUserId,dealPrice);
                    TransportMain transportMain = transportMainService.getTransportMainForId(tsId);
                    TytTransportOrders tytTransportOrders = transportOrdersService.getByTsIdByTime(tsId);
                    if(null != transportMain && null != transportMain.getShuntingQuantity() && transportMain.getShuntingQuantity() == 1 && null != tytTransportOrders){
                        tytTransportOrders.setHeadCity(headCity);
                        tytTransportOrders.setHeadNo(headNo);
                        tytTransportOrders.setTailCity(tailCity);
                        tytTransportOrders.setTailNo(tailNo);
                        tytTransportOrders.setCarId(null);
                        tytTransportOrders.setIsDealCar(1);
                        transportOrdersService.updateCarInfo(tytTransportOrders);
                    }

                    carHeadCity = headCity;
                    carHeadNo = headNo;

                    TytTransportBackend tytTransportBackend = transportBackendService.getByTsId(tsId);

                    //判断是否为企业货源，而且不为取消状态，保存发送Mq消息
                    if((null != tytTransportBackend) && (tytTransportBackend.getStatus() != 2)){
                        logger.info("设置成交车同步车辆状态tsId:{}", tsId);
                        bsPublishTransportService.sendMessage2MQ(tytTransportBackend, transportMain.getUserId());
                    }
                }
                Object location = carCurrentLocationService.getCurrentLocation(carHeadCity + carHeadNo);
                resultMsgBean.setData(location);

            }
            if (done.getIsAllowLocation() == 0 || done.getIsAllowLocation() == 2) {
                Date ctime = done.getCtime();
                //如果不存在同一车主同一车辆的货源成交记录，或者最近一次的货源成交记录授权状态为拒绝，
                //则给车主发送短信和push消息，否则不发送
                TransportDone transportDone = transportDoneService.getTransportDoneByMtime(userId,carryUserId,headCity,headNo,tailCity,tailNo,ctime);
                logger.info("最近的一笔历史货源成交记录为:{}", JSON.toJSONString(transportDone));
                if(transportDone == null || (transportDone != null && transportDone.getIsAllowLocation() == 2)){
                    transportDoneService.sendTransportDealAfter2MQ(tsId, 2);
                }

                if(!bexists && done.getIsAllowLocation() == 2) {
                    RedisUtil.setObject(Constant.CACHE_EXPIRE_LOCATION_APPLY + tsId, new Date(), Constant.CACHE_EXPIRE_TIME_10MIN_INT);
                    resultMsgBean.setCode(204);
                    resultMsgBean.setMsg("车方已拒绝您查看货物位置，已帮您再次申请。");
                    //返回申请中状态
                    resultMsgBean.setExtraData(3);
                    return resultMsgBean;
                }
            }
            resultMsgBean.setExtraData(done.getIsAllowLocation());
        } catch (Exception e) {
            logger.error("获取车辆定位失败 原因", e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("获取失败");
        }
        return resultMsgBean;
    }


}
