package com.tyt.transport.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytShareRec;
import com.tyt.transport.service.ShareRecService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/plat/transport/share")
public class ShareRecController extends BaseController {

    @Autowired
    private ShareRecService shareRecService;

    @RequestMapping(value = "/add")
    @ResponseBody
    public ResultMsgBean save(TytShareRec rec,
                              @RequestParam(value = "userId", required = true) Long userId,
                              @RequestParam(value = "shareTarget", required = true) String shareTarget,
                              @RequestParam(value = "shareLink", required = true) String shareLink) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            shareRecService.addShare(rec);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("保存成功 ");
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

}
