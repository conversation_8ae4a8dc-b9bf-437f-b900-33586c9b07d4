package com.tyt.transport.controller;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.acvitity.service.ActivityGradePrizeService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqUserMsg;
import com.tyt.invoicetransport.service.InvoiceTransportService;
import com.tyt.limituser.service.ExposureBlockService;
import com.tyt.model.City;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.User;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.plat.client.user.ApiUserPermissionClient;
import com.tyt.plat.entity.base.TytExposureBlock;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.user.UserExposureInfoVO;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.transport.bean.CommissionTypeBean;
import com.tyt.transport.bean.DrawCommissionReq;
import com.tyt.transport.enums.ExcellentGoodsEnums;
import com.tyt.transport.enums.PubTypeEnum;
import com.tyt.transport.enums.RefundFlagEnum;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.BsPublishTransportService;
import com.tyt.transport.service.SeckillGoodsTransportService;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.transport.service.TransportPublishLogService;
import com.tyt.upgrade.service.UpgradeCheckService;
import com.tyt.user.service.UserService;
import com.tyt.user.service.impl.TytConfigServiceImpl;
import com.tyt.util.*;
import com.vdurmont.emoji.EmojiParser;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;


/**
 * 信息相关接口
 *
 * <AUTHOR> modify by 20170620 增加高速费用查询接口 tianjw
 *
 */
@Controller
@RequestMapping("/plat/transport/V5930")
public class TransportPublishController extends BaseController {

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "bsPublishTransportService")
    private  BsPublishTransportService bsPublishTransportService;

    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;

    @Resource(name = "upgradeCheckService")
    private UpgradeCheckService upgradeCheckService;

    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;

    private static final String REPUBLISH_API_LOCK = "rePublish";
    @Autowired
    private TytConfigServiceImpl tytConfigService;
    @Autowired
    private TransportPublishLogService transportPublishLogService;

    @Autowired
    private UserService userService;

    @Autowired
    private ExposureBlockService exposureBlockService;

    @Autowired
    private ApiUserPermissionClient apiUserPermissionClient;

    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "threadPoolExecutor")
    private ThreadPoolExecutor threadPoolExecutor;
    @Autowired
    private ActivityGradePrizeService activityGradePrizeService;



    /**
     * 是否展示专车入口
     * @param parameter
     * @return
     */
    @RequestMapping(value = "/isShowSpecial")
    @ResponseBody
    public ResultMsgBean isShowSpecial(BaseParameter parameter) {
        if(Objects.isNull(parameter.getUserId())){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            return bsPublishTransportService.isShowSpecial(parameter.getUserId());
        } catch (TytException e) {
            logger.warn("isShowSpecial error, userId:{} ", parameter.getUserId(), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 是否展示专车入口
     * 6480 专车二期
     * @param parameter
     * @return
     */
    @RequestMapping(value = "/isShowSpecialV2")
    @ResponseBody
    public ResultMsgBean isShowSpecialV2(BaseParameter parameter) {
        if(Objects.isNull(parameter.getUserId())){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            return bsPublishTransportService.isShowSpecialV2(parameter.getUserId());
        } catch (Exception e) {
            logger.warn("isShowSpecialV2 error, userId:{} ", parameter.getUserId(), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 6480新增
     * 是否展示签约合作商：调度账号展示，非调度账号隐藏
     * @param queryBean
     * @return
     */
    @RequestMapping(value = "/isShowCargoOwner")
    @ResponseBody
    public ResultMsgBean isShowCargoOwner(CargoOwnerQueryBean queryBean) {
        if(Objects.isNull(queryBean.getUserId())){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            return bsPublishTransportService.isShowCargoOwner(queryBean);
        } catch (Exception e) {
            logger.warn("isShowCargoOwner error, userId:{} ", queryBean.getUserId(), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 6480新增
     * 是否展示司机驾驶此类货物
     * ①根据【运费所属企业】【出发地、目的地】【货物吨位】匹配运费计算规则
     * ②运费计算规则中配置了【驾驶货物费】，并且发货的货物类型名称（good_type_name）在驾驶能力枚举列表中
     * @param queryBean
     * @return
     */
    @RequestMapping(value = "/isShowDriverDriving")
    @ResponseBody
    public ResultMsgBean isShowDriverDriving(DriverDrivingQueryBean queryBean) {
        if(Objects.isNull(queryBean.getUserId())){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            return bsPublishTransportService.isShowDriverDriving(queryBean);
        } catch (Exception e) {
            logger.warn("isShowDriverDriving error, userId:{} ", queryBean.getUserId(), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 6470使用专车运费计算
     * @param calculatePriceBean
     * @param bindingResult
     * @return
     */
    @RequestMapping(value = "/calculatePrice")
    @ResponseBody
    public ResultMsgBean calculatePrice(@Valid CalculatePriceBean calculatePriceBean, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }

        try {
            logger.info("calculatePrice request param:{}", JSONObject.toJSONString(calculatePriceBean));
            return bsPublishTransportService.calculatePrice(calculatePriceBean);
        } catch (TytException e) {
            logger.warn("calculatePrice error, calculatePriceBean:{} ", JSONObject.toJSONString(calculatePriceBean), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 6480 专车二期-专线，运费测算
     * @param calculatePriceBean
     * @param bindingResult
     * @return
     */
    @RequestMapping(value = "/calculatePriceV2")
    @ResponseBody
    public ResultMsgBean calculatePriceV2(@Valid CalculatePriceBean calculatePriceBean, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }

        try {
            logger.info("calculatePriceV2 request param:{}", JSONObject.toJSONString(calculatePriceBean));
            return bsPublishTransportService.calculatePriceV2(calculatePriceBean);
        } catch (Exception e) {
            logger.warn("calculatePriceV2 error, calculatePriceBean:{} ", JSONObject.toJSONString(calculatePriceBean), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 专车发货自动派单
     * @param assignOrderBean
     * @param bindingResult
     * @return
     */
    @RequestMapping(value ={"/autoAssignOrderForSpecialCar", "/autoAssignOrderForSpecialCar.action"} )
    @ResponseBody
    public ResultMsgBean autoAssignOrderForSpecialCar(@Validated AutoAssignOrderBean assignOrderBean, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR,
                    bindingResult.getFieldError().getDefaultMessage());
        }

        try {
            logger.info("autoAssignOrderForSpecialCar request param:{}", JSONObject.toJSONString(assignOrderBean));
            return bsPublishTransportService.autoAssignOrderForSpecialCar(assignOrderBean);
        } catch (Exception e) {
            logger.warn("autoAssignOrderForSpecialCar error, request param:{} ", JSONObject.toJSONString(assignOrderBean), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 货源发布和编辑发布接口
     * @param publishBean 发布货源参数
     * @return ResultMsgBean 对象的json格式文件
     */
    @RequestMapping(value = "/publish")
    @ResponseBody
    public ResultMsgBean publish(TransportPublishBean publishBean) {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(publishBean.getTsId())) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }
        try {
            logger.info("publish request param:{}", JSON.toJSONString(publishBean));
            Long backendId = publishBean.getBackendId();
            if(!transportBusiness.lockBackendTransport(backendId)) {
                logger.info("lock_backent_transport_publish : " + backendId);
                //小程序锁
                return transportBusiness.returnLockStatus();
            }

            Long tsId = publishBean.getTsId();
            if (!transportBusiness.lockTransportOpt(tsId)) {
                //编辑锁
                return transportBusiness.returnLockStatus();
            }

            // 订金在不可退的情况下，订金不能大于等于运费
            if (Objects.equals(publishBean.getRefundFlag(), 0)
                    && publishBean.getInfoFee() != null && org.apache.commons.lang3.StringUtils.isNotBlank(publishBean.getPrice())) {
                BigDecimal price = new BigDecimal(publishBean.getPrice());
                if (publishBean.getInfoFee().compareTo(price) >= 0) {
                    return ResultMsgBean.failResponse(1001, "您当前订金高于运费，请核对后再发货");
                }
            }

            //若是否开票参数不符合规则，则直接默认为不开票
            if (publishBean.getInvoiceTransport() == null || (publishBean.getInvoiceTransport() != 0 && publishBean.getInvoiceTransport() != 1)) {
                publishBean.setInvoiceTransport(0);
            }
            if (publishBean.getInvoiceTransport() == 1) {
                publishBean.setRefundFlag(RefundFlagEnum.RETURN.getCode());
            }
            // 编辑发布去掉备注的固定文案
            if (publishBean.getRemark() != null && publishBean.getTsId() != null) {
                String goodsDetailRemarkPrompt = tytConfigService.getStringValue("goods_detail_remark_prompt", "");
                if (!goodsDetailRemarkPrompt.isEmpty() && publishBean.getRemark().endsWith(goodsDetailRemarkPrompt)) {
                    publishBean.setRemark(publishBean.getRemark().replace(goodsDetailRemarkPrompt, ""));
                }
            }

            // 专车发货，不走自动转优车逻辑
            if (!ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
                //非优车一口价货源判断是否符合优车定价货源条件，如果符合则自动转优车定价
                CheckGoodCarPriceTransportBean priceTransportBean = new CheckGoodCarPriceTransportBean(publishBean.getStartProvinc(), publishBean.getStartCity(), publishBean.getStartArea()
                        , publishBean.getDestProvinc(), publishBean.getDestCity(), publishBean.getDestArea()
                        , publishBean.getTaskContent(), publishBean.getWeight(), publishBean.getLength(), publishBean.getWide(), publishBean.getHigh()
                        , publishBean.getDistance() == null ? null : publishBean.getDistance().toString(), publishBean.getExcellentGoods(), publishBean.getUserId(), publishBean.getPublishType(), publishBean.getPrice(), publishBean.getGoodTypeName());
                if (bsPublishTransportService.checkTransportIsGoodCarPriceTransport(priceTransportBean)) {
                    publishBean.setGoodCarPriceTransport(1);
                    publishBean.setAutomaticGoodCarPriceTransport(true);
                }
            }

            boolean isInvoiceTransportAssignCar = false;
            //开票货源如果指派车方的话定金强制修改为0并且不在找货大厅显示
            if (publishBean.getInvoiceTransport() != null && publishBean.getInvoiceTransport() == 1 && org.apache.commons.lang3.StringUtils.isNotBlank(publishBean.getAssignCarTel())) {
                publishBean.setInfoFee(BigDecimal.ZERO);
                isInvoiceTransportAssignCar = true;
            }

            // PC客户端发货处理为普货
            if (Objects.nonNull(publishBean.getClientSign()) && "1".equals(publishBean.getClientSign())) {
                // PC低于6000版本不允许发货
                String clientVersion = publishBean.getClientVersion();
                if (StringUtils.isEmpty(clientVersion) || Integer.parseInt(clientVersion) < 586000) {
                    return ResultMsgBean.failResponse(1001, "服务升级，当前版本已不可用，请到官网下载最新版本");
                }
                publishBean.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            }

            long startTime = System.currentTimeMillis();
            ResultMsgBean msgBean = bsPublishTransportService.savePublishTransport(publishBean);
            logger.info("用户:{} 内容:{} 发货耗时:{}ms", publishBean.getUserId(), publishBean.getTaskContent(), System.currentTimeMillis() - startTime);

            if (msgBean.isSuccess()) {
                BoPublishTransportBean data = (BoPublishTransportBean) msgBean.getData();
                //开票货源如果指派车方的话调用履约派单接口
                if (isInvoiceTransportAssignCar) {
                    ResultMsgBean assignCarResult = transportBusiness.invocieTransportAssignCar(data, publishBean.getUserId(), publishBean.getAssignCarTel());
                    if (!assignCarResult.isSuccess()) {
                        return assignCarResult;
                    }
                }

                // 记录货源发布记录
                transportPublishLogService.recordPublishLog(publishBean.getUserId(), data.getSrcMsgId(), PubTypeEnum.CONFIRM_PUBLISH, null);

                //记录价格变动
                transportPublishLogService.changePriceLog(data.getSrcMsgId(), data.getPrice(), data.getPublishType(), 1);

                // 是否发放曝光卡
                transportBusiness.checkAndSaveExposureCardGiveaway(data.getSrcMsgId());

                // TODO 向MQ发送无效货源信息验证请求
                if (data.getTsId() != null) {
                    try{

                        sendNullifyMessage2MQ(data.getTsId(), null);
                        // 6510裂变拉新 异步发送mq
                        User user = userService.getByUserId(publishBean.getUserId());
                        threadPoolExecutor.execute(() -> activityGradePrizeService.sendMq(publishBean.getUserId(), user.getCellPhone(), 23));
                    }catch (Exception e){
                        logger.error("发送无效货源消息失败,货源id:{}",data.getTsId());
                    }
                }
            }
            return msgBean;

        } catch (Exception e) {
            //cacheService.del(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + null);
            //logger.error("publish_error : userId : {} ", publishBean.getUserId(), e);
            PlatCommonUtil.printErrorInfo("publish_error : userId : " + publishBean.getUserId(), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 向MQ发送信息
     *
     * @param transId 货物ID
     */
    private void sendNullifyMessage2MQ(Long transId, BackendTransportBean backendTransportBean) {
        // 发送初货物无效信息MQ
        MqUserMsg mqUserMsg = new MqUserMsg();

        mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqUserMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_NULLIFY_TRANSPORT_MESSAGE);
        mqUserMsg.setUserId(transId);
        mqUserMsg.setTsId(transId);
//        if (backendTransportBean!=null&&backendTransportBean.getAppletsUserId()!=null){
//            String cellPhone = null;
//            mqUserMsg.setSrcMsgId(backendTransportBean.getMsgId());
//            try {
//                cellPhone = userService.getByUserId(backendTransportBean.getAppletsUserId()).getCellPhone();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            mqUserMsg.setCellPhone(cellPhone);
//            String content=backendTransportBean.getStartPoint()+"→"+backendTransportBean.getDestPoint()+backendTransportBean.getTaskContent()
//                    +"，已被【"+backendTransportBean.getReceiverShowName()+StringUtil.replaceMobile(backendTransportBean.getReceiverPhone())+"】接单。打开微信小程序“特运通货主版”查看详情";
//            mqUserMsg.setContent(content);
//        }
        // 保存mq信息到数据库
        // tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

        long mqDelayTime = tytConfigService.getIntValue(Constant.publish_mq_delay_time, 1500).longValue();

        logger.info("publish_mq_delay_time : " + mqDelayTime);

        // 发送货物无效信息
//        tytMqMessageService.sendMqMessageDirect(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqDelayTime);

        // 发送新TOPIC的消息
        tytMqMessageService.sendMsgCustom(JSON.toJSONString(mqUserMsg), "GOODS_CENTER_TOPIC", mqUserMsg.getMessageSerailNum(), "TRANSPORT_PUBLISH", mqDelayTime);

    }

    /**
     * 获取抽佣规则接口
     *
     * @param req
     * @return ResultMsgBean 对象的json格式文件
     */
    @GetMapping(value = "/checkCommission")
    @ResponseBody
    public ResultMsgBean getCommissionRule(DrawCommissionReq req) {
        try {

            CommissionTypeBean commissionTypeBean = bsPublishTransportService.checkCommission(req,null);

            return ResultMsgBean.successResponse(commissionTypeBean);

        } catch (Exception e) {
            return ResultMsgBean.failResponse(e);
        }
    }


    /**
     * 获取发货帮助提示气泡是否需要展示以及多秒以后展示
     * 展示逻辑：当用户30天内发货量小于3单，且超过5s未离开页面，（单数、秒数通过公共资源配置，一个配置即可）
     * since V6470
     * <AUTHOR>
     * @date 2024/5/28 11:44
     * @param
     * @return ResultMsgBean
     */
    @PostMapping(value = "/getHelpBtnPopupConfig")
    @ResponseBody
    public ResultMsgBean getHelpBtnPopupConfig(BaseParameter parameter) {
        if(Objects.isNull(parameter.getUserId())){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            ResultMsgBean result = bsPublishTransportService.getHelpBtnPopupConfig(parameter.getUserId());
            logger.info("getHelpBtnPopupConfig userId:{}, result:{}", parameter.getUserId(), result.toString());
            return result;
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getHelpBtnPopupConfig error, userId : " + parameter.getUserId(), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 发货帮助提示气泡展示以后上报
     * since v6470
     * <AUTHOR>
     * @date 2024/5/29 11:00
     * @param parameter
     * @return ResultMsgBean
     */
    @PostMapping(value = "/reportPopup")
    @ResponseBody
    public ResultMsgBean reportPopup(BaseParameter parameter) {
        if(Objects.isNull(parameter.getUserId())){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {

            ResultMsgBean result = bsPublishTransportService.reportPopup(parameter.getUserId());
            logger.info("reportPopup userId:{}, result:{}", parameter.getUserId(), result.toString());
            return result;
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("reportPopup error, userId : " + parameter.getUserId(), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 获取订金及退还方式
     * since v6470
     * <AUTHOR>
     * @date 2024/5/29 14:10
     * @param parameter
     * @return ResultMsgBean
     */
    @PostMapping(value = "/getDepositAndRefundType")
    @ResponseBody
    public ResultMsgBean getDepositAndRefundType(BaseParameter parameter, Integer invoiceTrnaposrt) {
        if(Objects.isNull(parameter.getUserId())){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            ResultMsgBean result = bsPublishTransportService.getDepositAndRefundType(parameter.getUserId(), invoiceTrnaposrt);
            logger.info("getDepositAndRefundType userId:{}, result:{}", parameter.getUserId(), result.toString());
            return result;
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getDepositAndRefundType error, userId : " + parameter.getUserId(), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 获取智能订金
     * since 6510
     * <AUTHOR>
     * @date 2024/8/23 14:10
     * @param bean
     * @return ResultMsgBean
     */
    @PostMapping(value = "/intelligentDeposit")
    @ResponseBody
    public ResultMsgBean intelligentDeposit(IntelligentDepositBean bean) {
        if(Objects.isNull(bean.getUserId())) {
            logger.info("intelligentDeposit参数错误，param:{}", JSON.toJSONString(bean));
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            ResultMsgBean result = bsPublishTransportService.intelligentDeposit(bean);
            logger.info("intelligentDeposit param:{}, result:{}", JSON.toJSONString(bean), result.toString());
            return result;
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("intelligentDeposit error, param : " + JSON.toJSONString(bean), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 用户智能订金提交记录
     *
     * @param bean
     * @return
     */
    @PostMapping(value = "/depositRecord")
    @ResponseBody
    public ResultMsgBean depositRecord(IntelligentDepositBean bean) {
        if(Objects.isNull(bean.getUserId()) || Objects.isNull(bean.getDefaultDeposit()) ||
                Objects.isNull(bean.getDefaultRefundFlag()) || Objects.isNull(bean.getUserDeposit())
                || Objects.isNull(bean.getUserRefundFlag())){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            ResultMsgBean result = bsPublishTransportService.depositRecord(bean);
            logger.info("depositRecord param:{}, result:{}", JSON.toJSONString(bean), result.toString());
            return result;
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("depositRecord error, param : " + JSON.toJSONString(bean), e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 校验transport 是否存在相似货源
     * @param srcMsgId
     * @return
     */
    @PostMapping(value = "/checkSimilarExist")
    @ResponseBody
    public ResultMsgBean checkSimilarExist(Long srcMsgId) {

        if(srcMsgId == null){
            return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
        }

        try {
            boolean similarExist = bsPublishTransportService.checkSimilarExist(srcMsgId);
            return ResultMsgBean.successResponse(similarExist);
        } catch (TytException e) {
            logger.warn("checkSimilarExist_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 货源发布和编辑发布接口 该接口已废弃
     * @param publishBean 发布货源参数
     * @return ResultMsgBean 对象的json格式文件
     */

    @Deprecated
    @RequestMapping(value = "/publish/before")
    @ResponseBody
    public ResultMsgBean publishBefore(TransportPublishBean publishBean) {
        //校验接口是否还在使用
        logger.warn("publishBefore_check_user : 这个接口居然还用着呢！！！！！！！");

        Transport transport = new Transport();
        try {
            long startTime = System.currentTimeMillis();
            // 验证参数非空
            ResultMsgBean msgBean=validationTransportPublishBean(publishBean);
            if(ReturnCodeConstant.OK != msgBean.getCode()){
                return msgBean;
            }

            //版本升级防止用户跳过规则操作
            if(upgradeCheckService.checkClientInfo(publishBean.getClientVersion(),Integer.parseInt(publishBean.getClientSign()))){
                msgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
                msgBean.setNoticeData(tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.老版本货站升级提示,null));
                return msgBean;
            }

            msgBean=bsPublishTransportService.savePublishTransport(publishBean);
            logger.info("用户:{} 内容:{} 发货耗时:{}ms", publishBean.getUserId(), publishBean.getTaskContent(), System.currentTimeMillis() - startTime);
            return  msgBean;

        } catch (Exception e) {
            cacheService.del(Constant.CACHE_HASHCODE_KEY + TimeUtil.formatDate(new Date()) + "_" + transport.getHashCode());
            logger.error("", e);
            return ResultMsgBean.failResponse(e);
        }
    }



    /**
     * 曝光卡置顶刷新货源
     * @param parameter 公用参数
     * @param tsId 货源ID
     * @return ResultMsgBean 对象json
     */
    @RequestMapping(value = "/rePublish")
    @ResponseBody
    public ResultMsgBean rePublish(BaseParameter parameter, Long tsId) {
        Long userId = parameter.getUserId();
        try{
            //验证参数非空
            ResultMsgBean msgBean=validationRePublish(parameter,tsId);
            if(ReturnCodeConstant.OK != msgBean.getCode()){
                return msgBean;
            }

            //判断曝光卡权限是否封禁，如果封禁就返回提示信息
            TytExposureBlock tytExposureBlock = exposureBlockService.selectByUserId(userId);
            if (tytExposureBlock != null && tytExposureBlock.getBlockStatus() != null && tytExposureBlock.getBlockStatus() == 1) {
                String blockResult;
                User user = userService.getByUserId(userId);
                if (tytExposureBlock.getPermanentBlock()) {
                    blockResult = tytConfigService.getStringValue("republish_astrict_content_perpetual", "您（{tel}）因平台管控已被限制曝光卡使用权益。如有疑问请联系客服 400-6688-998");

                    blockResult = blockResult.replace("{tel}", user.getCellPhone());
                } else {
                    blockResult = tytConfigService.getStringValue("republish_astrict_content_temporary", "您（{tel}）因平台管控已被限制曝光卡使用权益，恢复时间{year}年{month}月{day}日{hour}时{min}分。如有疑问请联系客服 400-6688-998");

                    LocalDateTime localDateTime = LocalDateTime.now();
                    if (tytExposureBlock.getBlockEndTime() != null) {
                        localDateTime = new LocalDateTime(tytExposureBlock.getBlockEndTime());
                    }
                    blockResult = blockResult.replace("{tel}", user.getCellPhone())
                            .replace("{year}", String.valueOf(localDateTime.getYear()))
                            .replace("{month}", String.valueOf(localDateTime.getMonthOfYear()))
                            .replace("{day}", String.valueOf(localDateTime.getDayOfMonth()))
                            .replace("{hour}", String.valueOf(localDateTime.getHourOfDay()))
                            .replace("{min}", String.valueOf(localDateTime.getMinuteOfHour()));
                }
                return ResultMsgBean.failResponse(99810, blockResult);
            }

            // 校验成长体系-曝光卡领取权益
            Response<UserExposureInfoVO> exposureInfoVOResponse = apiUserPermissionClient.getGoodsCouponInfo(userId).execute();
            logger.info("查询用户权益接口 apiUserPermissionClient.getGoodsCouponInfo userId:{}, response.success:{}, body:{}",
                    userId, exposureInfoVOResponse.isSuccessful(), exposureInfoVOResponse.body());
            if (exposureInfoVOResponse.isSuccessful()) {
                UserExposureInfoVO userExposureInfoVO = exposureInfoVOResponse.body();
                if (userExposureInfoVO != null) {
                    // 用户无曝光次数，且有未领取的曝光卡，提示用户领取
                    if ((userExposureInfoVO.getUserExposurePermissionCount() == null || userExposureInfoVO.getUserExposurePermissionCount() == 0)
                            && (userExposureInfoVO.getUnreceivedExposureCount() != null && userExposureInfoVO.getUnreceivedExposureCount() > 0)) {
                        return ResultMsgBean.failResponse(99820, "暂无可用曝光卡，请前往领取待领取的曝光卡");
                    }
                }
            }

            if(LockUtil.lockObject("1",REPUBLISH_API_LOCK + userId, 10)){
                msgBean=bsPublishTransportService.saveRePublish(userId,tsId,parameter.getClientVersion(),parameter.getClientSign());
            }
            return  msgBean;
        }catch (Exception e){
            PlatCommonUtil.printErrorInfo("saveRePublish_error : ", e);
            return ResultMsgBean.failResponse(e);
        }finally {
            LockUtil.unLockObject("1",REPUBLISH_API_LOCK + userId);
        }

    }

    /**
     * 发货车辆屏蔽词校验接口
     * @param type
     * @param remark
     * @return
     */
    @RequestMapping(value = "/screeningWordCheck")
    @ResponseBody
    public ResultMsgBean screeningWordCheck(String type,String remark){
        return bsPublishTransportService.getScreeningWordCheck(type,remark);
    }


	private ResultMsgBean validationRePublish(BaseParameter parameter,Long tsId){
		if(tsId==null ||tsId==0){
			return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"tsId参数应大于0");
		}
		if(StringUtils.isEmpty(parameter.getClientVersion())){
			return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"clientVersion不应该为空");
		}

        if(!StringUtil.isNumeric(parameter.getClientSign())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"clientSign参数应数字");
        }
        if(parameter.getUserId() == null){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"userId不能为空");
        }


        return new ResultMsgBean(ReturnCodeConstant.OK,"验证成功");

    }


    /**
     * 相似货源判断接口
     * @param transportCheckSimilarityBean 似货源判断参数
     * @return ResultMsgBean 对象的json格式文件
     */
    @RequestMapping(value = "/checkTransportSimilarity")
    @ResponseBody
    public ResultMsgBean checkTransportSimilarity(TransportCheckSimilarityBean transportCheckSimilarityBean) {
        try {
            ResultMsgBean msgBean = bsPublishTransportService.checkTransportSimilarity(transportCheckSimilarityBean);
            logger.info("用户:{} 内容:{} 似货源判断结果:{}", transportCheckSimilarityBean.getUserId(), transportCheckSimilarityBean.getTaskContent(), msgBean.getCode());
            return msgBean;

        } catch (Exception e) {
            logger.error("相似货源判断异常:{},传入的用户ID:{},内容:{}", e, transportCheckSimilarityBean.getUserId(),transportCheckSimilarityBean.getTaskContent());
            e.printStackTrace();
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "服务器错误");
        }
    }


    /**
     * 根据车主用户和货源ID获取技术服务费用
     *
     * @param carUserId carUserId
     * @param srcMsgId srcMsgId
     * @return 返回包含技术服务费用信息的ResultMsgBean对象
     */
    @PostMapping(value = "/getTecServiceFeeByCarUserAndSrcMsgId")
    @ResponseBody
    public ResultMsgBean getTecServiceFeeByCarUserAndSrcMsgId(@RequestParam("carUserId") Long carUserId, @RequestParam("srcMsgId") Long srcMsgId){
        return bsPublishTransportService.getTecServiceFeeByCarUserAndSrcMsgId(carUserId, srcMsgId);
    }


    /**
     * 发布信息参数校验/过滤校验
     * @param publishBean 发布参数
     * @return ResultMsgBean对象，成功200
     */
    private ResultMsgBean validationTransportPublishBean(TransportPublishBean publishBean){
        logger.info("消息发布开始地址的未补全参数，省市区县:{}, x轴:{}：", "区县"+publishBean.getStartArea()+"，省"+publishBean.getStartProvinc()+"，市"+publishBean.getStartCity(), "x:"+publishBean.getStartCoordX()+"y:"+publishBean.getStartCoordY());
        logger.info("消息发布到达地址的未补全参数，省市区县:{}, x轴:{}：", "区县"+publishBean.getDestArea()+"，省"+publishBean.getDestProvinc()+"，市"+publishBean.getDestCity(), "x:"+publishBean.getDestCoordX()+"y:"+publishBean.getDestCoordY());

        /**  2020-10-19 货物重量只能为数字，非数字不允许发货 */
        if(org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getWeight())) {
            publishBean.setWeight(publishBean.getWeight().trim());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(publishBean.getWeight()) && !StringUtil.isDouble(publishBean.getWeight())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物重量格式错误，请输入数字");
        }

        //出发地和目的地以及坐标轴参数修补
        completionStartAndDestParameter(publishBean);

        /**  2020-07-12 出发地坐标为空判断，不允许发货 */

        if(publishBean.getStartCoordX()==null || publishBean.getStartCoordY()==null||publishBean.getStartCoordX().compareTo(BigDecimal.ZERO)==0||publishBean.getStartCoordY().compareTo(BigDecimal.ZERO)==0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"出发地不正确，请更换");
        }
        /**  2020-07-12 目的地坐标为空判断，不允许发货 */

        if(publishBean.getDestCoordX()==null|| publishBean.getDestCoordY()==null||publishBean.getDestCoordX().compareTo(BigDecimal.ZERO)==0||publishBean.getDestCoordY().compareTo(BigDecimal.ZERO)==0){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"目的地不正确，请更换");
        }

        if(org.apache.commons.lang.StringUtils.isBlank(publishBean.getStartProvinc())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"出发地参数不能为空");
        }
        if(org.apache.commons.lang.StringUtils.isBlank(publishBean.getDestProvinc())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"目的地参数不能为空");
        }
        if(publishBean.getStartCoordX()==null ){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"startCoordX"+"参数不能为空");
        }
        if(publishBean.getStartCoordY()==null){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"startCoordY"+"参数不能为空");
        }

        if(publishBean.getDestCoordX()==null){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"destCoordX"+"参数不能为空");
        }
        if(publishBean.getDestCoordY()==null){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"destCoordY"+"参数不能为空");
        }

        String timeErrorMsg = "装卸货时间已过期";
        if(publishBean.getLoadingTime()!=null && new Date().compareTo(publishBean.getLoadingTime())>0){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,timeErrorMsg);
        }
        if(publishBean.getUnloadTime()!=null && new Date().compareTo(publishBean.getUnloadTime())>0){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,timeErrorMsg);
        }


        String taskContent=publishBean.getTaskContent();
        if(org.apache.commons.lang.StringUtils.isBlank(taskContent)){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物内容不能为空");
        }
        taskContent= EmojiParser.removeAllEmojis(taskContent);
        logger.info("taskContent=" +publishBean.getTaskContent()+" EmotaskContent="+taskContent);

        if(org.apache.commons.lang.StringUtils.isBlank(taskContent)){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物内容不能有非法字符！");
        }
        publishBean.setTaskContent(taskContent);

        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getCarType())){
            publishBean.setCarType(EmojiParser.removeAllEmojis(publishBean.getCarType()));
            logger.info("carType:"+publishBean.getCarType()+"-----------------------");
        }

        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getCarStyle())){
            publishBean.setCarStyle(EmojiParser.removeAllEmojis(publishBean.getCarStyle()));
            logger.info("carStyle:"+publishBean.getCarStyle()+"-----------------------");
        }

        if(publishBean.getStartLongitude()==null || publishBean.getStartLongitude().compareTo(BigDecimal.ZERO)<1){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"startLongitude"+"参数不能小于0");
        }
        if(publishBean.getStartLatitude()==null || publishBean.getStartLatitude().compareTo(BigDecimal.ZERO)<1){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"startLatitude"+"参数不能小于0");
        }
        if(publishBean.getDestLongitude()==null || publishBean.getDestLongitude().compareTo(BigDecimal.ZERO)<1){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"destLongitude"+"参数不能小于0");
        }
        if(publishBean.getDestLatitude()==null || publishBean.getDestLatitude().compareTo(BigDecimal.ZERO)<1){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"destLatitude"+"参数不能小于0");
        }
        if(org.apache.commons.lang.StringUtils.isBlank(publishBean.getTel())||isMobileOrFixedPhone(publishBean.getTel())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"电话格式有误或者不能为空");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getTel3())&&isMobileOrFixedPhone(publishBean.getTel3())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"电话格式有误");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getTel4())&&isMobileOrFixedPhone(publishBean.getTel4())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"电话格式有误");
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getWide()) &&!StringUtil.isDouble(publishBean.getWide())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物宽度参数格式有误");
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getLength())&&!StringUtil.isDouble(publishBean.getLength())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物长度参数格式有误");
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getHigh())&&!StringUtil.isDouble(publishBean.getHigh())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物高度参数格式有误");
        }
        String temp=publishBean.getDestDetailAdd();
        if(org.apache.commons.lang.StringUtils.isNotBlank(temp)){
            publishBean.setDestDetailAdd(EmojiParser.removeAllEmojis(temp));

        }
        temp=publishBean.getStartDetailAdd();
        if(org.apache.commons.lang.StringUtils.isNotBlank(temp)){

            publishBean.setStartDetailAdd(EmojiParser.removeAllEmojis(temp));
        }
        temp=publishBean.getRemark();
        if(org.apache.commons.lang.StringUtils.isNotBlank(temp)){
            publishBean.setRemark(EmojiParser.removeAllEmojis(temp));

        }


        return new ResultMsgBean(ReturnCodeConstant.OK,"验证成功");

    }



    /**
     * 发布信息参数校验/过滤校验
     * @param publishBean 发布参数
     * @return ResultMsgBean对象，成功200
     */
    private ResultMsgBean validationTransportPublishBeanBefore(TransportPublishBean publishBean){
        if("5964".equalsIgnoreCase(publishBean.getClientVersion())&&"2".equals(publishBean.getClientSign())){
            /* 处理5964版本 出发地目的地 xy轴重复问题 */
            publishBean.setStartCoordX(null);
            publishBean.setStartCoordY(null);
            publishBean.setDestCoordX(null);
            publishBean.setDestCoordY(null);
        }
        logger.info("消息发布开始地址的未补全参数，省市区县:{}, x轴:{}：", "区县"+publishBean.getStartArea()+"，省"+publishBean.getStartProvinc()+"，市"+publishBean.getStartCity(), "x:"+publishBean.getStartCoordX()+"y:"+publishBean.getStartCoordY());
        logger.info("消息发布到达地址的未补全参数，省市区县:{}, x轴:{}：", "区县"+publishBean.getDestArea()+"，省"+publishBean.getDestProvinc()+"，市"+publishBean.getDestCity(), "x:"+publishBean.getDestCoordX()+"y:"+publishBean.getDestCoordY());

        /**  2020-10-19 货物重量只能为数字，非数字不允许发货 */
        if(org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getWeight())) {
            publishBean.setWeight(publishBean.getWeight().trim());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(publishBean.getWeight()) && !StringUtil.isDouble(publishBean.getWeight())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物重量格式错误，请输入数字");
        }

        //出发地和目的地以及坐标轴参数修补
        completionStartAndDestParameter(publishBean);

        /**  2020-07-12 出发地坐标为空判断，不允许发货 */

        if(publishBean.getStartCoordX()==null || publishBean.getStartCoordY()==null||publishBean.getStartCoordX().compareTo(BigDecimal.ZERO)==0||publishBean.getStartCoordY().compareTo(BigDecimal.ZERO)==0) {

            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"出发地不正确，请更换");
        }
        /**  2020-07-12 目的地坐标为空判断，不允许发货 */

        if(publishBean.getDestCoordX()==null|| publishBean.getDestCoordY()==null||publishBean.getDestCoordX().compareTo(BigDecimal.ZERO)==0||publishBean.getDestCoordY().compareTo(BigDecimal.ZERO)==0){

            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"目的地不正确，请更换");
        }

        if(org.apache.commons.lang.StringUtils.isBlank(publishBean.getStartProvinc())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"出发地参数不能为空");
        }
        if(org.apache.commons.lang.StringUtils.isBlank(publishBean.getDestProvinc())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"目的地参数不能为空");
        }
        if(publishBean.getStartCoordX()==null ){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"startCoordX"+"参数不能为空");
        }
        if(publishBean.getStartCoordY()==null){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"startCoordY"+"参数不能为空");
        }

        if(publishBean.getDestCoordX()==null){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"destCoordX"+"参数不能为空");
        }
        if(publishBean.getDestCoordY()==null){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"destCoordY"+"参数不能为空");
        }

        String timeErrorMsg = "装卸货时间已过期";
        if(publishBean.getLoadingTime()!=null && new Date().compareTo(publishBean.getLoadingTime())>0){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,timeErrorMsg);
        }
        if(publishBean.getUnloadTime()!=null && new Date().compareTo(publishBean.getUnloadTime())>0){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,timeErrorMsg);
        }

        String taskContent=publishBean.getTaskContent();
        if(org.apache.commons.lang.StringUtils.isBlank(taskContent)){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物内容不能为空");
        }
        taskContent= EmojiParser.removeAllEmojis(taskContent);
        logger.info("taskContent=" +publishBean.getTaskContent()+" EmotaskContent="+taskContent);

        if(org.apache.commons.lang.StringUtils.isBlank(taskContent)){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物内容不能有非法字符！");
        }
        publishBean.setTaskContent(taskContent);

        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getCarType())){
            publishBean.setCarType(EmojiParser.removeAllEmojis(publishBean.getCarType()));
            logger.info("carType:"+publishBean.getCarType()+"-----------------------");
        }

        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getCarStyle())){
            publishBean.setCarStyle(EmojiParser.removeAllEmojis(publishBean.getCarStyle()));
            logger.info("carStyle:"+publishBean.getCarStyle()+"-----------------------");
        }

        if(publishBean.getStartLongitude()==null || publishBean.getStartLongitude().compareTo(BigDecimal.ZERO)<1){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"startLongitude"+"参数不能小于0");
        }
        if(publishBean.getStartLatitude()==null || publishBean.getStartLatitude().compareTo(BigDecimal.ZERO)<1){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"startLatitude"+"参数不能小于0");
        }
        if(publishBean.getDestLongitude()==null || publishBean.getDestLongitude().compareTo(BigDecimal.ZERO)<1){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"destLongitude"+"参数不能小于0");
        }
        if(publishBean.getDestLatitude()==null || publishBean.getDestLatitude().compareTo(BigDecimal.ZERO)<1){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"destLatitude"+"参数不能小于0");
        }
        if(org.apache.commons.lang.StringUtils.isBlank(publishBean.getTel())||isMobileOrFixedPhone(publishBean.getTel())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"电话格式有误或者不能为空");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getTel3())&&isMobileOrFixedPhone(publishBean.getTel3())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"电话格式有误");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getTel4())&&isMobileOrFixedPhone(publishBean.getTel4())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"电话格式有误");
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getWide()) &&!StringUtil.isDouble(publishBean.getWide())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物宽度参数格式有误");
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getLength())&&!StringUtil.isDouble(publishBean.getLength())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物长度参数格式有误");
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(publishBean.getHigh())&&!StringUtil.isDouble(publishBean.getHigh())){
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"货物高度参数格式有误");
        }
        String temp=publishBean.getDestDetailAdd();
        if(org.apache.commons.lang.StringUtils.isNotBlank(temp)){
            publishBean.setDestDetailAdd(EmojiParser.removeAllEmojis(temp));

        }
        temp=publishBean.getStartDetailAdd();
        if(org.apache.commons.lang.StringUtils.isNotBlank(temp)){

            publishBean.setStartDetailAdd(EmojiParser.removeAllEmojis(temp));
        }
        temp=publishBean.getRemark();
        if(org.apache.commons.lang.StringUtils.isNotBlank(temp)){
            publishBean.setRemark(EmojiParser.removeAllEmojis(temp));

        }


        return new ResultMsgBean(ReturnCodeConstant.OK,"验证成功");

    }


    /**
     * 拼接出发地和到达地
     * @param province 省
     * @param city 市
     * @param area 区县
     * @return
     */
    private String updateStartAndDestPoint(String province,String city,String area){
        if (org.apache.commons.lang.StringUtils.isNotBlank(city)&& org.apache.commons.lang.StringUtils.isNotBlank(area)){
            if(city.contains(province)){
                if (area.contains(city)){
                    return area;
                }else {
                    return city+area;
                }
            }else {
                if (area.contains(city)){
                    return province+area;
                }else {
                    return province+city+area;
                }
            }
        }else {
            if (org.apache.commons.lang.StringUtils.isNotBlank(city)){
                if (city.contains(province)){
                    return city;
                }else {
                    return province+city;
                }
            }else if (org.apache.commons.lang.StringUtils.isNotBlank(area)){
                if (area.contains(province)){
                    return area;
                }else {
                    return province+area;
                }
            }else {
                return province;
            }
        }
    }

    private  void  completionStartAndDestParameter(TransportPublishBean publishBean){
        logger.info("开始匹配出发地++++++++++++++++++++++++++++++++++++++++++++++");
        //出发地参数补充总
        ResultMsgBean resultMsgBean = parameterAssignment(publishBean.getStartProvinc(), publishBean.getStartCity(), publishBean.getStartArea(), publishBean.getStartCoordX(), publishBean.getStartCoordY(),publishBean.getUserId());
        if (resultMsgBean.getCode().equals(200)&&resultMsgBean.getData()!=null){
            City city=(City)resultMsgBean.getData();
            publishBean.setStartCoordX(new BigDecimal(city.getPx()));
            publishBean.setStartCoordY(new BigDecimal(city.getPy()));
            publishBean.setStartProvinc(city.getProvince());
            publishBean.setStartCity(city.getCityName());
            publishBean.setStartArea(city.getAreaName());
            publishBean.setStartPoint(updateStartAndDestPoint(city.getProvince(),city.getCityName(),city.getAreaName()));
            logger.info("消息发布出发地的补全参数，省市区县:{}, x轴:{}：","区县"+publishBean.getStartArea()+"，省"+publishBean.getStartProvinc()+"，市"+publishBean.getStartCity(),"x:"+publishBean.getStartCoordX()+"y:"+publishBean.getStartCoordY());
        }

        logger.info("开始匹配目的地++++++++++++++++++++++++++++++++++++++++++++++");
        //目的参数补充
        ResultMsgBean resultMsgBean1 = parameterAssignment(publishBean.getDestProvinc(), publishBean.getDestCity(), publishBean.getDestArea(), publishBean.getDestCoordX(), publishBean.getDestCoordY(),publishBean.getUserId());
        if (resultMsgBean1.getCode().equals(200)&&resultMsgBean1.getData()!=null){
            City city=(City)resultMsgBean1.getData();
            publishBean.setDestCoordX(new BigDecimal(city.getPx()));
            publishBean.setDestCoordY(new BigDecimal(city.getPy()));
            publishBean.setDestProvinc(city.getProvince());
            publishBean.setDestCity(city.getCityName());
            publishBean.setDestArea(city.getAreaName());
            publishBean.setDestPoint(updateStartAndDestPoint(city.getProvince(),city.getCityName(),city.getAreaName()));
            logger.info("消息发布到达地址的补全参数，省市区县:{}, x轴:{}：", "区县"+publishBean.getDestArea()+"，省"+publishBean.getDestProvinc()+"，市"+publishBean.getDestCity(), "x:"+publishBean.getDestCoordX()+"y:"+publishBean.getDestCoordY());
        }

    }

    private ResultMsgBean parameterAssignment(String province,String cityName,String areaName,BigDecimal px,BigDecimal py,Long userId){
        //省市区和xy都有的话先试用省市区进行匹配
        if (cityName!=null&&areaName!=null&&px!=null&&py!=null&&px.compareTo(BigDecimal.ZERO)!=0&&py.compareTo(BigDecimal.ZERO)!=0){
            logger.info("开始地址坐标都不为空++++++++++++++++++++++++++++++++++++++++++++++");
            ResultMsgBean resultMsgBean = bsPublishTransportService.pxAndPyByCityName(province, cityName, areaName, "1",userId);
            if (resultMsgBean.getCode().equals(ReturnCodeConstant.OK)){
                return resultMsgBean;
            }else {
                //根据省市区查询不到再根据xy查询
                City cityByPxAndPy = bsPublishTransportService.getCityByPxAndPy(px.stripTrailingZeros().toPlainString(), py.stripTrailingZeros().toPlainString());
                if (cityByPxAndPy!=null){
                    return new ResultMsgBean(ReturnCodeConstant.OK,"查询成功",cityByPxAndPy);
                }
                return new ResultMsgBean(ReturnCodeConstant.ERROR,"查询失败");
            }
            //省市区没有使用xy
        }else if (cityName==null&&areaName==null&&px!=null&&py!=null&&px.compareTo(BigDecimal.ZERO)!=0&&py.compareTo(BigDecimal.ZERO)!=0){
            logger.info("开始地址为空++++++++++++++++++++++++++++++++++++++++++++++");
            City cityByPxAndPy = bsPublishTransportService.getCityByPxAndPy(px.stripTrailingZeros().toPlainString(), py.stripTrailingZeros().toPlainString());
            if (cityByPxAndPy!=null){
                return new ResultMsgBean(ReturnCodeConstant.OK,"查询成功",cityByPxAndPy);
            }
            return new ResultMsgBean(ReturnCodeConstant.ERROR,"查询失败");
            //没有坐标的使用省市区
        }else if (cityName!=null&&areaName!=null&&(px==null||py==null||px.compareTo(BigDecimal.ZERO)==0||py.compareTo(BigDecimal.ZERO)==0)){
            logger.info("开始坐标为空++++++++++++++++++++++++++++++++++++++++++++++");
            return bsPublishTransportService.pxAndPyByCityName(province,cityName,areaName,"2",userId);
        }
        return new ResultMsgBean(ReturnCodeConstant.ERROR,"查询失败");
    }

    /**
     * 判断发货手机号是不是座机或者手机号
     * @param tel
     */
    private boolean isMobileOrFixedPhone(String tel){
        if (MobileUtil.isFixedPhone(tel)||MobileUtil.isMobile(tel)){
            return false;
        }else {
            return true;
        }
    }
}
