package com.tyt.transport.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.EcaContract;
import com.tyt.model.EcaProgress;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.common.azt.service.IdentityInfo;
import com.tyt.transport.querybean.CertIdentityBean;
import com.tyt.transport.service.EcaContractService;
import com.tyt.transport.service.EcaProgressService;
import com.tyt.transport.service.UserIdentityCertService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 身份认证接口
 *
 * <AUTHOR>
 *         create by 20180110
 */

@Controller
@RequestMapping("/plat/identity")
public class UserIdentityCertController extends BaseController {

    @Resource(name = "userIdentityCertService")
    private UserIdentityCertService userIdentityCertService;
    @Resource(name = "ecaProgressService")
    private EcaProgressService ecaProgressService;
    @Resource(name="ecaContractService")
    private EcaContractService ecaContractService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @RequestMapping(value = "/cert/identityCertStatus")
    @ResponseBody
    public ResultMsgBean identityCertStatus(@RequestParam(value = "goodsId", required = true) Long goodsId,
                                            @RequestParam(value = "userId", required = true) Long userId ) {
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        // 获取合同信息
        EcaContract ecaContract = ecaContractService.getEcaContractBySrcMsgId(goodsId);
        String serverDomain = tytConfigService.getStringValue("h5app.server.domain","http://www.teyuntong.cn");
        String certUri = tytConfigService.getStringValue("identity.cert.h5.uri","/app/contractNet/certificate.html");
        String redirectUrl = serverDomain + certUri;
        int status = 0; //未实名
        try {
            CertIdentityBean bean = userIdentityCertService.getCertIdentity(userId);
            if(StringUtils.isNotBlank(bean.getIdCard())) {
                status = 1; // 已实名
            }
            Map<String, Object> result = new HashMap<String, Object>();
            result.put("status", status);
            result.put("contractId", ecaContract.getId());
            result.put("redirectUrl", redirectUrl);
            rm.setData(result);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络繁忙，请稍后重试");
        }
        return rm;
    }

    @RequestMapping(value = "/cert/userIdentityCert.action")
    @ResponseBody
    public ResultMsgBean userIdentityCert(@RequestParam(value = "contractId", required = true) Long contractId,
                                          @RequestParam(value = "userId", required = true) Long userId,
                                          @RequestParam(value = "userName", required = true) String userName,
                                          @RequestParam(value = "idCard", required = true) String idCard,
                                          @RequestParam(value = "device", required = false) String device,
                                          @RequestParam(value = "imei", required = false) String imei,
                                          HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 查询合同信息
            EcaContract ecaContract = ecaContractService.getById(contractId);
            Integer optHandler = 0;
            if(userId.equals(ecaContract.getShipperUserId())){
                optHandler = 2;
            } else if(userId.equals(ecaContract.getCarryUserId())) {
                optHandler = 3;
            }
            // 保存实名认证信息
            IdentityInfo info = userIdentityCertService.saveUserIdentityCert(userName,idCard,userId);
            String detail = "提交实名认证("+idCard+"/"+userName+"),实名认证失败：身份证号与真实姓名不匹配";
            if(StringUtils.equals(info.getCode(), "005")) {
                detail = "提交实名认证("+idCard+"/"+userName+"),实名认证成功";
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("成功");
            } else{
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg(info.getCodeInfo());
            }
            // 记录签署进度
            EcaProgress progress = new EcaProgress();
            progress.setContractId(contractId);
            progress.setOptType(6); // 认证
            progress.setOptHandler(optHandler);
            progress.setUserName(userName);
            progress.setUserId(userId);
            progress.setIdCard(idCard);
            progress.setDevice(device);
            progress.setImei(imei);
            progress.setIpAddress(StringUtil.getRealIp(request));
            progress.setStatus(1); // 进行中
            progress.setDetail(detail);
            ecaProgressService.addEcaProgress(progress);
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("网络繁忙，请稍后重试");
        }
        return rm;
    }

}
