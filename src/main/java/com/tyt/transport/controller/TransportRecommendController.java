package com.tyt.transport.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.model.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.CertIdentityBean;
import com.tyt.transport.querybean.SearchResultMsgBean;
import com.tyt.transport.querybean.TransPerDataBean;
import com.tyt.transport.querybean.TransportBean;
import com.tyt.transport.querybean.TransportRecommendBean;
import com.tyt.transport.service.*;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.StringUtil;
import com.tyt.util.XXTea;
import com.tytrecommend.model.TytPreferenceNew;
import com.tytrecommend.recommend.service.PreferNewService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *  有好货优化
 * <AUTHOR>
 * create by 20180124
 *
 */

@Controller
@RequestMapping("/plat/transport/recommend")
public class TransportRecommendController extends BaseController{

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "preferNewService")
	PreferNewService preferNewService;

	@Resource(name = "transportService")
	private TransportService transportService;

	private static ConcurrentHashMap<String,Long> visitMap = new ConcurrentHashMap<String,Long>();

	private boolean visitValid(String key){
		//获取当前用户ID
		Long visitTime = visitMap.get(key);
		Long currentTime = System.currentTimeMillis();
		if(visitTime != null){
			Long chaTime = currentTime - visitTime;
			if(chaTime <= 1000){
				return true;
			}else{
				visitMap.put(key,currentTime);
			}
		}else{
			visitMap.put(key,currentTime);
		}
		return false;
	}

	@RequestMapping(value = "/search")
	@ResponseBody
	public SearchResultMsgBean search(@RequestParam(value = "queryType", required = true) Integer queryType,
									  @RequestParam(value = "carId", required = true) Long carId,
									  @RequestParam(value = "userId", required = true) Long userId,
									  Long querySign,
									  String clientVersion,
									  HttpServletRequest request, HttpServletResponse response) {
		SearchResultMsgBean rm = new SearchResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("查询成功");
		//请求限制代码 begin
		if(queryType != null && queryType.intValue() == 0){
			String key = userId+"_recommend.search";
			if(visitValid(key)){
				rm.setCode(ReturnCodeConstant.MORE_THAN_LIMIT);
				return rm;
			}
		}
		// end

		try {
			Map<String, String> params = parseRequestParams(request);
			User loginUser = userService.getByUserId(userId);
			if(loginUser == null){
				rm.setCode(ReturnCodeConstant.ERROR);
				rm.setMsg("用户未登录");
				return rm;
			}
			TytPreferenceNew tytPreferenceNew = preferNewService.newFindPreferenceByCarId(carId);
//			int version = tytConfigService.getIntValue("car_login_youhua_version");
//			int currentVersion =  Integer.parseInt(params.get("clientVersion"));
//			if(currentVersion<=version) {
			
				if(loginUser.getUserType() == null || loginUser.getUserType().intValue() != 1 || loginUser.getEndTime() == null || loginUser.getEndTime().getTime() < System.currentTimeMillis()){
					rm.setCode(ReturnCodeConstant.ERROR);
					rm.setMsg("用户非VIP用户");
					return rm;
				}
	
				//TODO 查询车辆偏好
				if(tytPreferenceNew == null){
					rm.setCode(ReturnCodeConstant.ERROR);
					rm.setMsg("当前车辆偏好不存在");
					return rm;
				}
			
				if(tytPreferenceNew.getFindGoodOnoff() == 0){
					rm.setCode(ReturnCodeConstant.ERROR);
					rm.setMsg("当前车辆找货开关关闭");
					return rm;
				}
//			}
			//TODO 获取出发地和目的地找货范围
			int startDistanceValue = getStartDistanceByRedis(tytPreferenceNew.getStartProvinc(),tytPreferenceNew.getStartCity());
			//TODO 查询货源列表
			Map<Object, Object> map = transportService.queryRecommendTransport(queryType, querySign == null ? 0 : querySign, startDistanceValue, tytPreferenceNew);
			encyptNickname(map.get("resultList"), clientVersion);
			
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			rm.setData(map.get("resultList"));
			rm.setTotalSize(((Integer)map.get("totalSize")).longValue());
			rm.setTime(String.valueOf(System.currentTimeMillis()));
			rm.setIsAuthUpdate(2);//手动更新

			//TODO 查询是否手动选择出发地开关
			if(tytPreferenceNew.getIsAuthUpdate().intValue() == 1 && StringUtils.isNotEmpty(tytPreferenceNew.getStartProvinc()) && StringUtils.isNotEmpty(tytPreferenceNew.getStartCity()) ){
				//TODO 总开关是否开启 0开启，自动更新   1关闭，不更新
				Integer isAuthUpdateOnoff = tytConfigService.getIntValue("global_car_location_refresh_onoff",0);
				if(isAuthUpdateOnoff.intValue() == 0){
					rm.setIsAuthUpdate(1); //自动更新
				}
			}
			//返回最新的位置信息
			TransPerDataBean transPerDataBean = new TransPerDataBean();
			transPerDataBean.setStartProvinc(tytPreferenceNew.getStartProvinc());
			transPerDataBean.setStartCity(tytPreferenceNew.getStartCity());
			transPerDataBean.setStartArea(tytPreferenceNew.getStartArea());
			transPerDataBean.setStartCoordX(StringUtil.getMovePointLeft2(tytPreferenceNew.getStartCoordX()));
			transPerDataBean.setStartCoordY(StringUtil.getMovePointLeft2(tytPreferenceNew.getStartCoordY()));
			transPerDataBean.setDestProvinc(tytPreferenceNew.getDestProvinc());
			transPerDataBean.setDestCity(tytPreferenceNew.getDestCity());
			transPerDataBean.setDestArea(tytPreferenceNew.getDestArea());
			transPerDataBean.setDestCoordX(StringUtil.getMovePointLeft2(tytPreferenceNew.getDestCoordX()));
			transPerDataBean.setDestCoordY(StringUtil.getMovePointLeft2(tytPreferenceNew.getDestCoordY()));
			transPerDataBean.setStartPointRegion(String.valueOf(startDistanceValue));
			rm.setPerData(transPerDataBean);
			logger.info("总条数【{}】", map.get("totalSize"));
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("网络繁忙，请稍后重试");
		}
		return rm;
	}
	
	@SuppressWarnings("unchecked")
	private void encyptNickname(Object object, String clientVersion) {
		List<TransportRecommendBean> transportBeans = (List<TransportRecommendBean>) object;
		// 获取需要进行登录错误次数控制的最小版本（不包括）
		String restriceLoginFailMinVersion = tytConfigService
					.getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5301");
		int encyptPhoneMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
		int clientVersionInt = Integer.valueOf(clientVersion).intValue();
		// 是否需要对电话加密 1 需要 2 不需要
		int isNeedEncypt = tytConfigService.getIntValue(Constant.IS_NEED_ENCYPT_KEY, 1);
		if (transportBeans != null && transportBeans.size() > 0) {
			TransportRecommendBean curTransportBean;
			String curNickname;
			for (int i = 0; i < transportBeans.size(); i++) {
				curTransportBean = transportBeans.get(i);
				curTransportBean.setIsNeedDecrypt(isNeedEncypt);
				logger.info("isNeedEncypt == 1 && (clientVersionInt > restriceLoginFailMinVersionInt): "
							+ (isNeedEncypt == 1 && (clientVersionInt > encyptPhoneMinVersionInt)));
				if (isNeedEncypt == 1 && (clientVersionInt > encyptPhoneMinVersionInt)) {
					curNickname = curTransportBean.getNickName();
					if (org.apache.commons.lang.StringUtils.isNotBlank(curNickname)) {
						curTransportBean.setNickName(XXTea.Encrypt(curNickname, AppConfig.getProperty("tyt.xxtea.key")));
					} else {
						curTransportBean.setIsNeedDecrypt(2);
					}
				}
			}
		}
	}

	//出发地找货配置信息，redis读取，task定时更新
	public static Map<String,String> distanceInitMap = new HashMap<String,String>();


	private int getStartDistanceByRedis(String startProvinc,String startCity){
		Integer startDistance = 300;
		//TODO 缓存名称
		String distanceStr = distanceInitMap.get(startProvinc+"#"+startCity);
		if(StringUtils.isEmpty(distanceStr)){
			distanceStr = distanceInitMap.get("其他#其他");
			if(StringUtils.isEmpty(distanceStr)){
				return startDistance;
			}
		}
		startDistance = Integer.valueOf(distanceStr);
		return startDistance;
	}

	@RequestMapping(value = "/deletelog")
	@ResponseBody
	public ResultMsgBean deleteLog(Long userId,Long srcMsgId,Integer clientSign,
										HttpServletRequest request, HttpServletResponse response) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("采集成功");
		try {
			transportService.deleteLog(userId,srcMsgId,clientSign);
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("网络繁忙，请稍后重试");
		}
		return rm;
	}

}
