package com.tyt.transport.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.BsTransportService;
import com.tyt.transport.service.TransportLogService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/plat/transport/fall/short")
public class TransportFallShortController extends BaseController {

    @Autowired
    private BsTransportService bsTransportService;
    @Autowired
    private TransportLogService transportLogService;

    /**
     * 范围倒短
     * @param bean 传参
     * @return rm
     */
    @RequestMapping(value = "/scope/search")
    @ResponseBody
    public ResultMsgBean scopeSearch(ScopeSearchQueryBean bean) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (checkQueryParameter(bean,rm)){
                BoResultBean<TransportBean> resultBean= bsTransportService.getScopeSearchTransportList(bean);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(resultBean.getResult());
                rm.setTime(System.currentTimeMillis()+"");
                rm.setTotalSize(resultBean.getTotalSize());
                logger.debug("总条数【{}】", resultBean.getTotalSize());

                if (bean.getQueryType()==1){
                    FallShortSearchLogBean logBean = new FallShortSearchLogBean();
                    BeanUtils.copyProperties(bean, logBean);
                    logBean.setSearchType(1);
                    logBean.setNumberType(1);
                    transportLogService.searchFallShortLog(logBean);
                }
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 省内倒短
     * @param bean 传参
     * @return rm
     */
    @RequestMapping(value = "/provinc/search")
    @ResponseBody
    public ResultMsgBean provincSearch(ProvincSearchQueryBean bean) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (StringUtils.isBlank(bean.getStartProvinc())){
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("省不能为空！");
                return rm;
            }
            if (checkTypeParameter(bean.getQueryType(),bean.getQuerySign(),rm)) {
                BoResultBean<TransportBean> resultBean = bsTransportService.getProvicSearchTransportList(bean);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(resultBean.getResult());
                rm.setTime(System.currentTimeMillis() + "");
                rm.setTotalSize(resultBean.getTotalSize());
                logger.debug("总条数【{}】", resultBean.getTotalSize());

                if (bean.getQueryType()==1){
                    FallShortSearchLogBean logBean = new FallShortSearchLogBean();
                    BeanUtils.copyProperties(bean, logBean);
                    logBean.setSearchType(2);
                    logBean.setNumberType(1);
                    transportLogService.searchFallShortLog(logBean);
                }
            }
        }catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    private Boolean checkQueryParameter(ScopeSearchQueryBean bean, ResultMsgBean rm){
        // 坐标判断
        if (StringUtils.isBlank(bean.getStartCoord())) {
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("出发地坐标不能为空！");
            return false;
        } else {
            String[] coord = bean.getStartCoord().split(",");
            if (coord.length < 2) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("出发地坐标不能为空！");
                return false;
            }
        }
        //范围校验
        if (StringUtils.isBlank(bean.getStartDistance())) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("范围不能为空！");
            return false;
        }
        if (StringUtils.isBlank(bean.getStartProvinc())){
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("出发地省不能为空！");
            return false;
        }
        if (StringUtils.isBlank(bean.getStartCity())){
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("出发地市不能为空！");
            return false;
        }
        return checkTypeParameter(bean.getQueryType(),bean.getQuerySign(),rm);
    }

    private Boolean checkTypeParameter(Integer queryType, Long querySign, ResultMsgBean rm){
        if (queryType == null || (queryType != 0 && queryType != 1 && queryType != 2)) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("查询类型不正确！");
            return false;
        } else if (queryType != 1 && (querySign == null || querySign < 0)) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("查询标识错误，最大、最小ID为空！");
            return false;
        }
        return true;
    }

}
