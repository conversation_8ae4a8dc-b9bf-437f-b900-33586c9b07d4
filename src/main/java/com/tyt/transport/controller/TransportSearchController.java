package com.tyt.transport.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.transport.querybean.*;
import com.tyt.transport.service.BsTransportService;
import com.tyt.transport.service.TransportLogService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/plat/transport/search")
public class TransportSearchController extends BaseController {

    @Autowired
    private BsTransportService bsTransportService;
    @Autowired
    private TransportLogService transportLogService;

    /**
     * 找货大厅
     * @param bean 查询条件
     * @return rm
     */
    @RequestMapping(value = "/list",produces = {"application/json"})
    @ResponseBody
    public ResultMsgBean listSearch(TransportSearchBean bean) {
        logger.debug("找货大厅入参：{}", bean);
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (checkQueryParameter(bean,rm)){
                if (bean.getSortType()==null){
                    bean.setSortType(0);
                }
                BoResultBean<TransportBean> resultBean= bsTransportService.getTransportList(bean);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(resultBean.getResult());
                rm.setTime(System.currentTimeMillis()+"");
                rm.setTotalSize(resultBean.getTotalSize());
                logger.debug("总条数【{}】", resultBean.getTotalSize());

                if (bean.getQueryType()==1){
                    transportLogService.searchLog(bean.getUserId(), bean.getStartCoord(),
                            bean.getStartDistance(), bean.getDestCoord()==null?"":bean.getDestCoord(), bean.getDestDistance()==null?"":bean.getDestDistance(),
                            bean.getCarId() == null ? 0 : bean.getCarId(), bean.getHeadNo()==null?"":bean.getHeadNo(), bean.getHeadCity()==null?"":bean.getHeadCity(),
                            bean.getClientVersion(), Integer.parseInt(bean.getClientSign()), bean.getSortType(),
                            bean.getNumberType()==null?"0":bean.getNumberType(), bean.getOsVersion(), bean.getClientId(), bean.getCarLength()==null?"":bean.getCarLength(),
                            bean.getCarType()==null?"":bean.getCarType(), bean.getSpecialRequired()==null?"":bean.getSpecialRequired(),bean.getStartWeight()==null?"":bean.getStartWeight()+"",bean.getEndWeight()==null?"":bean.getEndWeight()+"");
                    transportLogService.searchDistanceSortLog(bean.getUserId()==null?0:bean.getUserId(), bean.getSortType(), bean.getClientVersion(), Integer.parseInt(bean.getClientSign()));
                }
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    private Boolean checkQueryParameter(TransportSearchBean bean, ResultMsgBean rm){
        // 坐标判断
        if (StringUtils.isBlank(bean.getStartCoord())) {
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("出发地坐标不能为空！");
            return false;
        } else {
            String[] startCoord = bean.getStartCoord().split(",");
            if (startCoord.length < 2) {
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("出发地坐标错误，非x,y形式！");
                return false;
            }
        }
        if (StringUtils.isBlank(bean.getStartDistance())){
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("出发地范围不能为空！");
            return false;
        }
        if (StringUtils.isNotBlank(bean.getDestCoord())) {
            String[] destCoord = bean.getDestCoord().split(",");
            if (destCoord.length < 2) {
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("目的地坐标错误,非x,y形式");
                return false;
            }
        }
        if (bean.getQueryType() == null || (bean.getQueryType() != 0 && bean.getQueryType() != 1 && bean.getQueryType() != 2)) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("查询类型不正确！");
            return false;
        } else if (bean.getQueryType() != 1 && (bean.getQuerySign() == null || bean.getQuerySign() < 0)) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("查询标识错误，最大、最小ID为空！");
            return false;
        }
        return true;
    }

//    /**
//     * 目的地货源 已移至goods-search项目
//     * @param bean  参数bean
//     * @return list
//     */
//    @RequestMapping(value = "/destList")
//    @ResponseBody
//    public ResultMsgBean destListSearch(DestTransportSearchBean bean) {
//        ResultMsgBean rm = new ResultMsgBean();
//        logger.info("destList params = {}",bean.toString());
//        try {
//            if (checkQueryParameter(bean,rm)){
//                BoResultBean<TransportBean> resultBean= bsTransportService.getDestTransportList(bean);
//                rm.setCode(ReturnCodeConstant.OK);
//                rm.setMsg("查询成功");
//                rm.setData(resultBean.getResult());
//                rm.setTime(System.currentTimeMillis()+"");
//            }
//        } catch (Exception ex) {
//            logger.error("服务器异常", ex);
//            rm.setCode(ReturnCodeConstant.ERROR);
//            rm.setMsg("服务器错误");
//        }
//        return rm;
//    }

    /**
      * <AUTHOR> Lion
      * @Description 相似货源列表
      * @Param [bean]
      * @return com.tyt.model.ResultMsgBean
      * @Date 2022/6/2 13:45
      */
    @RequestMapping(value = "/similarList")
    @ResponseBody
    public ResultMsgBean similarListSearch(SimilarTransportSearchBean bean) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if(StringUtils.isBlank(bean.getSimilarityCode())){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("相似编码不能为空！");
                return rm;
            }
            if(null == bean.getSimilarityFirstId()){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("相似货源首发ID不能为空！");
                return rm;
            }
            if(null == bean.getGoodsId()){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("货源ID不能为空！");
                return rm;
            }
            BoSimilarResultBean resultBean = bsTransportService.getSimilarTransportList(bean);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("查询成功");
            rm.setData(resultBean.getResult());
            rm.setTime(System.currentTimeMillis()+"");

        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;

    }


    private Boolean checkQueryParameter(DestTransportSearchBean bean, ResultMsgBean rm){
        if (StringUtils.isBlank(bean.getStartProvinc())) {
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("出发地省不能为空！");
            return false;
        }
        if (StringUtils.isBlank(bean.getStartCity())) {
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("出发地市不能为空！");
            return false;
        }
        return true;
    }



}
