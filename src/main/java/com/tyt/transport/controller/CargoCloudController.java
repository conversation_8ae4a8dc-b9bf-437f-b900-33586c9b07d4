package com.tyt.transport.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.GoodsSingleDetailResultBean;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqRevokeTransportSyncMsg;
import com.tyt.infofee.enums.SourceTypeEnum;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.plat.entity.base.TytTransportSyncYmm;
import com.tyt.receive.service.TransportBackendService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.DispatchParamBean;
import com.tyt.transport.querybean.TransportDoneRequest;
import com.tyt.transport.service.*;
import com.tyt.user.service.CarCurrentLocationService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.IdUtils;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName CargoCloudController
 * @Description 个人货主
 * <AUTHOR> Lion
 * @Date 2022/11/14 13:13
 * @Verdion 1.0
 **/
@Controller
@RequestMapping("/cargocloud/transport")
public class CargoCloudController extends BaseController {

    @Resource(name = "infoFeeBusinessService")
    private InfoFeeBusinessService infoFeeBusinessService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;

    @Resource(name = "tytTransportSyncYmmService")
    private TytTransportSyncYmmService tytTransportSyncYmmService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "transportDoneService")
    private TransportDoneService transportDoneService;

    @Resource
    private TransportOrdersService transportOrdersService;

    @Autowired
    private TransportBackendService transportBackendService;

    @Autowired
    private SpecialCarDispatchFailureService specialCarDispatchFailureService;

    @Resource(name = "bsPublishTransportService")
    private BsPublishTransportService bsPublishTransportService;

    @Resource(name = "carCurrentLocationService")
    private CarCurrentLocationService carCurrentLocationService;
    @Autowired
    private TransportYMMService transportYMMService;

    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;



    /**
     * 获取详情
     *
     * @return
     */
    @RequestMapping(value = "/getSingleDetail")
    public void getSingleDetail(Long userId, Long goodsId, Integer detailType,  HttpServletRequest request, HttpServletResponse response){
        logger.info("getSingleDetail userId is: " + userId + ", detailType is: " + detailType);
        try {

            // 结果获取
            GoodsSingleDetailResultBean result = infoFeeBusinessService.getGoodsInfoDetail(goodsId, userId, detailType);
            backResponse(request, response, ReturnCodeConstant.OK, "成功", result, 0);
            return;
        } catch (Exception e) {
            e.printStackTrace();
            backResponse(request, response, ReturnCodeConstant.ERROR, "失败", null, 0);
            return;
        }
    }



    /**
     * @Description  1撤销货源/2设置成交
     * <AUTHOR>
     * @Date  2018/12/24 18:50
     * @Param [dispatchParamBean] 代调业务相关参数bean
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = "/saveGoodsStatusNew",produces = "application/json")
    @ResponseBody
    public ResultMsgBean saveGoodsStatusNew(DispatchParamBean dispatchParamBean,
                                            TransportDoneRequest doneRequest) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        dispatchParamBean = transportYMMService.revokeBeanHandle(dispatchParamBean);
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(dispatchParamBean.getGoodsId())) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }
        try {
            Long goodsId = dispatchParamBean.getGoodsId();
            // 参数验证
            if (goodsId == null || goodsId.longValue() <= 0l) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("goodsId参数错误");
                return resultMsgBean;
            }
            Integer operateType = dispatchParamBean.getOperateType();
            if (operateType == null ||operateType < 1 || operateType > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("operateBtnType参数错误");
                return resultMsgBean;
            }
            Long userId = dispatchParamBean.getUserId();

            long t1 = System.currentTimeMillis();
            // 撤销、成交操作
            logger.info("APP信息费撤销/成交开始");
            resultMsgBean = transportBusiness
                    .saveInfoFeeUpdateBtnStatusNew(userId, operateType, goodsId, doneRequest == null ?
                            new TransportDoneRequest() : doneRequest, dispatchParamBean.getBackoutReasonKey(),
                            dispatchParamBean.getBackoutReasonValue(), dispatchParamBean.getSpecificReason(),
                            dispatchParamBean.getRemark(), true
                            , dispatchParamBean.getBackoutReasonKeyNew()
                            , dispatchParamBean.getBackoutReasonValueNew());
            /**
             *  运满满货源
             */
//            if(SourceTypeEnum.运满满货源.getId().equals(dispatchParamBean.getSourceType())){
//                transportYMMService.expireYMMCargoMerge(dispatchParamBean.getSrcMsgId());
//                if(dispatchParamBean.getCargoId() != null){
//                    transportYMMService.pushMbCargoExpireMessage(dispatchParamBean.getSrcMsgId());
//                }
//            }
            logger.info("[代调6期]撤销返回结果******************************{}",JSON.toJSONString(resultMsgBean));
            if (ReturnCodeConstant.OK == resultMsgBean.getCode()) {
                //撤销时 清空加价次数
                //获取加价次数
                TransportMain transportMain = transportMainService.getTransportMainForId(goodsId);
                if (Objects.nonNull(transportMain)) {
                    String freightAddMoneyNumKey = Constant.FREIGHT_ADD_MONEY_NUM + "_" + userId + "_" + transportMain.getSrcMsgId();
                    String freightAddMoneyNum = RedisUtil.get(freightAddMoneyNumKey);
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(freightAddMoneyNum)) {
                        RedisUtil.del(freightAddMoneyNumKey);
                    }
                }
                if (null != transportMain.getId() && !SourceTypeEnum.运满满货源.getId().equals(dispatchParamBean.getSourceType())) {
                    //发送MQ货源打通满满货源下架
                    TytTransportSyncYmm resultTransport = tytTransportSyncYmmService.findTransportSyncYmm(transportMain.getId());
                    if(null != resultTransport) {
                        MqRevokeTransportSyncMsg syncMsg = new MqRevokeTransportSyncMsg();
                        String messageSerailNum = SerialNumUtil.generateSeriaNum();
                        syncMsg.setMessageType(MqBaseMessageBean.MB_SYNC_TRANSPORT_WAYBILL_MESSAGE);
                        syncMsg.setPartnerSerialNo(IdUtils.getIncreaseIdByLocalTime());
                        syncMsg.setCargoId(resultTransport.getCargoId());
                        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
                        syncMsg.setPartnerRequestTime(format.format(new Date()));
                        syncMsg.setDeleteReason(1);
                        syncMsg.setSrcMsgId(goodsId);
                        syncMsg.setMessageSerailNum(messageSerailNum);
                        tytMqMessageService.addSaveMqMessage(messageSerailNum, com.alibaba.fastjson.JSON.toJSONString(syncMsg), syncMsg.getMessageType());
                        tytMqMessageService.sendMbMqMessage(messageSerailNum, com.alibaba.fastjson.JSON.toJSONString(syncMsg), syncMsg.getMessageType());
                    }
                    if (null != resultTransport) {
                        //更改同步货源下架状态
                        tytTransportSyncYmmService.updateTransportSyncYmmStatus(resultTransport);
                    }
                }
                // 处理专车货源工单状态
                specialCarDispatchFailureService.processWorkOrderStatus(transportMain);
            }

            long t2 = System.currentTimeMillis();
            logger.info("APP信息费撤销/成交时间【{}ms】,参数userId:【{}】,goodsId:【{}】,operateType:【{}】(1撤销货源 2设置成交)", t2 - t1, userId, goodsId, operateType);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("", e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }
    }


    /**
     * 更改承运车辆信息
     *
     * @param tsId
     * @return
     */
    @RequestMapping(value = "/getCarPosition",produces = "application/json")
    @ResponseBody
    public ResultMsgBean getCarPosition(@RequestParam(name = "tsId", required = true) Long tsId, String headCity, String headNo, String tailCity, String tailNo,@RequestParam(name = "dealPrice", required = true) BigDecimal dealPrice) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            TransportDone done = transportDoneService.getByTsId(tsId);
            if (done == null) {
                resultMsgBean.setCode(201);
                resultMsgBean.setMsg("该货源不存在");
                return resultMsgBean;
            }
//            Date sevenDayAfter = TimeUtil.weeHours(TimeUtil.addDay(done.getCtime(), 7), 1);
//            if (new Date().getTime() > sevenDayAfter.getTime()) {
//                resultMsgBean.setCode(202);
//                resultMsgBean.setMsg("已超出订单可查询时间");
//                return resultMsgBean;
//            }
            Long userId = done.getUserId();
            Long carryUserId = done.getCarryUserId();
            if (carryUserId == null) {
                User user = userService.getUserByCell(done.getCarryCellPhone());
                if (user == null){
                    resultMsgBean.setCode(203);
                    resultMsgBean.setMsg("对方为未注册用户，不能查看车辆位置");
                    return resultMsgBean;
                } else {
                    carryUserId = user.getId();
                }
            }

            String carHeadCity = done.getHeadCity();
            String carHeadNo = done.getHeadNo();
            if (StringUtils.isNotBlank(done.getHeadCity()) && StringUtils.isNotBlank(done.getHeadNo())) {
                if ( !(headCity.equals(done.getHeadCity()) && headNo.equals(done.getHeadNo()) && tailCity.equals(done.getTailCity()) && tailNo.equals(done.getTailNo()) && dealPrice.compareTo(done.getDealPrice())==0)) {
                    transportDoneService.updateChangCarStatus(tsId, headCity, headNo, tailCity, tailNo, carryUserId,dealPrice);
                    TransportMain transportMain = transportMainService.getTransportMainForId(tsId);
                    TytTransportOrders tytTransportOrders = transportOrdersService.getByTsIdByTime(tsId);
                    if(null != transportMain && null != transportMain.getShuntingQuantity() && transportMain.getShuntingQuantity() == 1 && null != tytTransportOrders){
                        tytTransportOrders.setHeadCity(headCity);
                        tytTransportOrders.setHeadNo(headNo);
                        tytTransportOrders.setTailCity(tailCity);
                        tytTransportOrders.setTailNo(tailNo);
                        tytTransportOrders.setCarId(null);
                        transportOrdersService.updateCarInfo(tytTransportOrders);
                    }

                    carHeadCity = headCity;
                    carHeadNo = headNo;

                    TytTransportBackend tytTransportBackend = transportBackendService.getByTsId(tsId);

                    //判断是否为企业货源，而且不为取消状态，保存发送Mq消息
                    if((null != tytTransportBackend) && (tytTransportBackend.getStatus() != 2)){
                        logger.info("设置成交车同步车辆状态tsId:{}", tsId);
                        bsPublishTransportService.sendMessage2MQ(tytTransportBackend, transportMain.getUserId());
                    }
                }
                Object location = carCurrentLocationService.getCurrentLocation(carHeadCity + carHeadNo);
                resultMsgBean.setData(location);

            } else {
                transportDoneService.updateChangCarStatus(tsId, headCity, headNo, tailCity, tailNo, carryUserId,dealPrice);
                TransportMain transportMain = transportMainService.getTransportMainForId(tsId);
                TytTransportOrders tytTransportOrders = transportOrdersService.getByTsIdByTime(tsId);
                if(null != transportMain && null != transportMain.getShuntingQuantity() && transportMain.getShuntingQuantity() == 1 && null != tytTransportOrders){
                    tytTransportOrders.setHeadCity(headCity);
                    tytTransportOrders.setHeadNo(headNo);
                    tytTransportOrders.setTailCity(tailCity);
                    tytTransportOrders.setTailNo(tailNo);
                    tytTransportOrders.setCarId(null);
                    transportOrdersService.updateCarInfo(tytTransportOrders);
                }

                carHeadCity = headCity;
                carHeadNo = headNo;

                TytTransportBackend tytTransportBackend = transportBackendService.getByTsId(tsId);

                //判断是否为企业货源，而且不为取消状态，保存发送Mq消息
                if((null != tytTransportBackend) && (tytTransportBackend.getStatus() != 2)){
                    logger.info("设置成交车同步车辆状态tsId:{}", tsId);
                    bsPublishTransportService.sendMessage2MQ(tytTransportBackend, transportMain.getUserId());
                }

                Object location = carCurrentLocationService.getCurrentLocation(carHeadCity + carHeadNo);
                resultMsgBean.setData(location);
            }
            resultMsgBean.setExtraData(done.getIsAllowLocation());
        } catch (Exception e) {
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("获取失败");
        }
        return resultMsgBean;
    }

}
