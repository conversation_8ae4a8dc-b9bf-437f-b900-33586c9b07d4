package com.tyt.transport.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytKeywordShortTransform;
import com.tyt.model.TytMachineTypeBrandNew;
import com.tyt.model.TytMachineTypeBrandParameter;
import com.tyt.transport.service.TytKeywordShortTransformService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
@Controller
@RequestMapping("/plat/machine")
public class MachineTypeBrandNewController extends BaseController {

    @Resource(name = "tytKeywordShortTransformService")
    private TytKeywordShortTransformService tytKeywordShortTransformService;


    /**
     * 新接口，根据关键词查询相关机械简称
     * @param keyword
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/search/matches/group/query"})
    @ResponseBody
    public ResultMsgBean searchMatchesGroupsearch(String keyword, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type group version 6000 begin, the keyword is [ " + keyword
                + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isBlank(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                List<TytKeywordShortTransform> forms = tytKeywordShortTransformService.queryKeywordKeyword(keyword,
                        userId);
                rm.setData(forms);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 根据机械俗称查询机械品牌
     * @param keyword
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/search/matches/group/barnd"})
    @ResponseBody
    public ResultMsgBean searchMatchesGroupbarnd(String keyword, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type group version barnd begin, the keyword is [ " + keyword
                + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isBlank(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                List<TytMachineTypeBrandParameter> forms = tytKeywordShortTransformService.searchMatchesGroupbarnd(keyword,
                        userId);
                rm.setData(forms);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 根据分类和品牌查询型号如
     * @param keyword 一拖
     * @param userId  挖掘机
     * @return
     */
    @RequestMapping(value = {"/search/matches/group/type"})
    @ResponseBody
    public ResultMsgBean searchMatchesGroupType(String keyword,String secondClass, String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type group version type begin, the keyword is [ " + keyword
                + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isBlank(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword/secondClass不能为空");
            } else {
                List<TytMachineTypeBrandParameter> forms = tytKeywordShortTransformService.searchMatchesGroupType(keyword,secondClass,
                        userId);
                rm.setData(forms);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping(value = {"/search/matches/group/showName"})
    @ResponseBody
    public ResultMsgBean searchMatchesGroupshowName(String keyword,String userId) {
        long startTimeInMilli = System.currentTimeMillis();
        logger.info("query machine type group version type begin, the keyword is [ " + keyword
                + " ], start time is [ " + startTimeInMilli + " ]");
        ResultMsgBean rm = new ResultMsgBean();
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("查询成功");
        try {
            if (org.apache.commons.lang.StringUtils.isBlank(keyword)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("keyword不能为空");
            } else {
                List<TytMachineTypeBrandParameter> forms = tytKeywordShortTransformService.searchMatchesGroupshowName(keyword,
                        userId);
                rm.setData(forms);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

}
