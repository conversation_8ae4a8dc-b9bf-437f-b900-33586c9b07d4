package com.tyt.transport.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;






import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.payment.service.PayMethodService;
import com.tyt.payment.service.PriceService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.querybean.SourceBean;
import com.tyt.user.service.TytSourceService;
import com.tyt.util.Constant;

@Controller
@RequestMapping("plat/resource")
public class ResourceController extends BaseController{
	
	@Resource(name = "payMethodService")
	private PayMethodService payMethodService;
	
	@Resource(name = "priceService")
	private PriceService priceService;
	
	@Resource(name = "tytSourceService")
	private TytSourceService sourceService;

	@RequestMapping(value = "/get")
	public void get(Integer type,String cellPhone,String token,
			@RequestParam(value = "platId", defaultValue = Constant.PLAT_PC+"") Integer platId,
			HttpServletRequest request,HttpServletResponse response){
			
		try{
			if(!StringUtils.hasLength(cellPhone)){
	        	logger.info("resource:get. cellPhone is null.");
				ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"cellPhone is null");
				printJSON(request, response, msgBean);
				return;
	        }
			
			if(type==null){
	        	logger.info("resource:get. type is null.");
				ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
						"type is null");
				printJSON(request, response, msgBean);
				return;
	        }
			
			if (!validateToken(cellPhone, token, request, response)) {
				logger.info("resource:get.token error");
				return;
			}
			
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK,"1");
			switch(type){
			case 1:
				msgBean.setData(payMethodService.getEnabledList(platId));
				break;
			case 2:
				msgBean.setData(priceService.getEnabledList());
				break;
			default:
			}
			printJSON(request, response, msgBean);
			}catch(Exception e){
				logger.info("resource:get.Exception"+cellPhone);
				e.printStackTrace();
			}
		
	}
	/**
	 * 提供资源接口地址
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = {"/getResourceInterfaceUrl","/getResourceInterfaceUrl.action"})
	public void getResourceInterfaceUrl(HttpServletRequest request,HttpServletResponse response){
		ResultMsgBean result=new ResultMsgBean();
		Map<String,String> listmap=new HashMap<String,String>();
		try {
			List<SourceBean> list=null;
			Object obj = RedisUtil.getObject(Constant.RESOURCE_INTERFACE_URL);
			if(obj==null){
				list = sourceService.getByGroupCodeWithNoLimit("resource_interface_url");
				if(list!=null && list.size()>0){
					RedisUtil.setObject(Constant.RESOURCE_INTERFACE_URL,list, Constant.RESOURCE_URL_CACHE_TIME);
				}
			}else{
				list=(List<SourceBean>) obj;
			}
			for (SourceBean sourceBean : list) {
				listmap.put(sourceBean.getName(), sourceBean.getValue());
			}
			result.setData(listmap);
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
		}
		printJSON(request, response, result);

	}

}
