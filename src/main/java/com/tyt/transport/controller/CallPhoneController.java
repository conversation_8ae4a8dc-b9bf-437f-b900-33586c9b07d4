package com.tyt.transport.controller;

import com.alibaba.fastjson.JSONObject;
import com.tyt.acvitity.service.ActivityGradePrizeService;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.ASRTaskMsg;
import com.tyt.infofee.enums.PublishTypeEnum;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.orderlimit.bean.AcceptOrderLimitInfo;
import com.tyt.orderlimit.service.AcceptOrderLimitRecordService;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.TytBlockConfig;
import com.tyt.plat.entity.base.TytTransportDispatch;
import com.tyt.plat.entity.base.TytTransportTecServiceFee;
import com.tyt.plat.mapper.base.TytTransportDispatchMapper;
import com.tyt.plat.mapper.base.TytTransportOrdersMapper;
import com.tyt.plat.mapper.base.TytTransportTecServiceFeeMapper;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.service.block.TytBlockConfigService;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.restrictapp.service.AppLimitLogService;
import com.tyt.transport.bean.PrivacyPhoneNumGoodIdReq;
import com.tyt.transport.bean.PrivacyPhoneTabInfo;
import com.tyt.transport.querybean.CallPhoneSearchResultBean;
import com.tyt.transport.querybean.CellPhoneListBean;
import com.tyt.transport.service.CallPhoneListService;
import com.tyt.transport.service.PrivacyPhoneNumService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.impl.TransportBusiness;
import com.tyt.upgrade.service.UpgradeCheckService;
import com.tyt.user.service.*;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.tyt.util.Constant.EXCELLENT_GOODS_PUBLISH_TIME;

@Controller
@RequestMapping("/plat/call/phone")
public class CallPhoneController {

    private static final Logger logger = LoggerFactory.getLogger(CallPhoneController.class);

    @Resource(name="callPhoneListService")
    private CallPhoneListService callPhoneListService;

    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;

    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Resource(name = "upgradeCheckService")
    private UpgradeCheckService upgradeCheckService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService userIdentityAuthService;

    @Resource(name = "carService")
    private CarService carService;

    @Resource(name =  "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name =  "superiorCarSignService")
    private SuperiorCarSignService superiorCarSignService;

    @Autowired
    private PrivacyPhoneNumService privacyPhoneNumService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Resource(name = "cacheServiceMcImpl")
    protected CacheService cacheService;
    @Autowired
    private BlacklistUserService blacklistUserService;

    @Autowired
    private TytTransportDispatchMapper tytTransportDispatchMapper;

    @Autowired
    private AcceptOrderLimitRecordService acceptOrderLimitRecordService;

    @Resource(name = "tytUserCallPhoneRecordService")
    private TytUserCallPhoneRecordService tytUserCallPhoneRecordService;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private TransportBusiness transportBusiness;

    @Autowired
    private ActivityGradePrizeService activityGradePrizeService;

    @Autowired
    private TytTransportOrdersMapper tytTransportOrdersMapper;

    private static final String INTERCEPT_OFF = "intercept_off";

    private static final String NEW_CAR_PERMISSION_NUM = "new_car_permission_num";

    private static final String CAR_PERMISSION_NUM = "car_permission_num";

    private static final String CAR_CALLING_NUM = "car_calling_num";

    private static final String NEW_CAR_APP_REMARK = "new_car_app_remark";

    private static final String NEW_CAR_PC_REMARK = "new_car_pc_remark";

    private static final int DAY = 30;

    private static final Integer ZERO = 0;

    @Autowired
    private TytBlockConfigService tytBlockConfigService;

    @Autowired
    private TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    @Autowired
    private AppLimitLogService appLimitLogService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;


    @RequestMapping(value = {"/get", "/get.action"})
    @ResponseBody
    public ResultMsgBean getPhoneList(@RequestParam(name="goodId",required = true) String goodId, String moduleType,String path, BaseParameter base,
                                      String clientVersion,Integer clientSign,Integer type) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK,"");
        try {
            appLimitLogService.addRequestLog(Long.valueOf(goodId), base.getUserId(), 2);

            //6450-查询拨打记录-当前货源是否打过电话
            TytUserCallPhoneRecord record = tytUserCallPhoneRecordService.getPhoneBySrcMsgIdAndUserId(Long.valueOf(goodId), base.getUserId().toString());


            //版本升级防止用户跳过规则操作
            if(upgradeCheckService.checkClientInfo(clientVersion,clientSign)){
                resultMsgBean.setCode(ReturnCodeConstant.NO_PERMISSION);
                resultMsgBean.setNoticeData(tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.老版本车主升级提示,null));
                return resultMsgBean;
            }
            //校验找货是否被限制
            ResultMsgBean msgBean = blacklistUserService.checkLimitFindGoods(base.getUserId());
            if (ReturnCodeConstant.OK != msgBean.getCode()) {

                return msgBean;
            }
            //6320需求新增 优推好车主过期时间限制
            TransportMain transportMain = transportMainService.getTransportMainForId(Long.valueOf(goodId));


            //优货判断接单限制
            if(transportMain!=null&&transportMain.getExcellentGoods()!=null&&transportMain.getExcellentGoods()==1){
                Date pubTime = getPubTime(transportMain.getId());
                if (pubTime==null) {
                    pubTime = transportMain.getCtime();
                }
                checkSuperiorCarSignForbid(pubTime,base.getUserId(),resultMsgBean);
                if (resultMsgBean.getCode()!=ResultMsgBean.OK) {
                    return resultMsgBean;
                }
            }



            //查询电话
            CallPhoneSearchResultBean resultBean = callPhoneListService.addGetCallPhoneList(goodId,moduleType,path,base);
            CellPhoneListBean cellPhoneListBean = resultBean.getCellPhoneListBean();
            if(transportMain != null && cellPhoneListBean != null){
               // 订金类型（0不退还；1退还）
                Integer refundFlag = transportMain.getRefundFlag();
                // 获取货主的会员信息
                UserPermission userPermission = userPermissionService.getUserPermission(transportMain.getUserId(), UserPermission.PermissionTypeEnum.GOODS_MEMBER.getCode());
                //当非会员发布电议【不退还】车方找货模式的货源，车主app在此处做做风险提示：
                if(transportMain.getPublishType().equals( PublishTypeEnum.tel.getCode()) && refundFlag != 1 && (null != userPermission && !UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus()))){
                    String noGoodsmemberTextRemind = configService.getStringValue("no_goodsmember_text_remind","该用户不是发货会员，请通过平台支付订金。");
                    cellPhoneListBean.setNoGoodsMemberText(noGoodsmemberTextRemind);
                }

                //填充装卸货电话
                if (StringUtils.isNotBlank(transportMain.getLoadCellPhone())) {
                    cellPhoneListBean.setLoadCellPhone(transportMain.getLoadCellPhone());
                }
                if (StringUtils.isNotBlank(transportMain.getUnloadCellPhone())) {
                    cellPhoneListBean.setUnloadCellPhone(transportMain.getUnloadCellPhone());
                }
            }





            //查询用户接单限制信息
            AcceptOrderLimitInfo acceptOrderLimitInfo = acceptOrderLimitRecordService.getAcceptOrderLimitInfo(base.getUserId(), Long.valueOf(goodId));
            if(resultBean.getCellPhoneListBean() != null){
                resultBean.getCellPhoneListBean().setAcceptOrderLimitInfo(acceptOrderLimitInfo);
            }

            //新增字段 是否弹虚拟号弹窗、弹了虚拟号弹窗之后是否可复制真实手机号
            makeTelTab(transportMain, base.getUserId(), cellPhoneListBean);

            resultMsgBean.setData(resultBean.getCellPhoneListBean());

            if (resultBean.getNoticePopup()!=null){
                resultMsgBean.setNoticeData(resultBean.getNoticePopup());
            }
            resultMsgBean.setCode(resultBean.getCode());
            if (resultBean.getCode() != ResultMsgBean.OK) {
                return resultMsgBean;
            }

            // 是否进行原来的车辆认证阻断
            if (Objects.equals(resultBean.getUnAuthUserCallExempt(), 1)) {
                return resultMsgBean;
            }

            logger.info("执行原车辆阻断逻辑,userId:{}",base.getUserId());
            //6450新增 车APP/PC-货源详情-0认证车主提醒及阻断
            if(null != transportMain && null != type && 1 == type){
                logger.info("进入阻断判断【{}】",type);
                ResultMsgBean bean = carIntercept(transportMain.getId(), base.getUserId(), clientSign,record);
                if (bean.getCode()!=ResultMsgBean.OK) {
                    return bean;
                }
            }
            //sendActivityMq( base.getUserId());
        }catch (Exception e){
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器异常");
        }
        return resultMsgBean;
    }

    public void sendActivityMq(Long userId){
        try {
            User byUserId = userService.getByUserId(userId);
            activityGradePrizeService.sendMq(userId,byUserId.getCellPhone(),22);
        }catch (Exception e){
            logger.info("裂变活动发送mq失败",e);
        }

    }
    public ResultMsgBean carIntercept(Long id, Long userId,Integer clientSign,TytUserCallPhoneRecord record) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "");
        try {

            //查询身份判断
            User user = userService.getByUserId(userId);

            //查询是否有符合的配置，没有的话直接跳过
            TytBlockConfig config = tytBlockConfigService.getByUserConfig(user);
            if(null == config){
                return resultMsgBean;
            }


            //判断开关是否开启
           /* Integer intValue = tytConfigService.getIntValue(INTERCEPT_OFF, 0);
            if (ZERO.equals(intValue)) {
                return resultMsgBean;
            }
            //判断是否在abtest组里
            Integer userType = abtestService.getUserType(AbtestConstant.CAR_USER_INTERCEPT, userId);
            logger.info("阻断是否已开启【{}】",userType);
            if (userType == null || userType.intValue() != 1) {
                return resultMsgBean;
            }*/
            //判断该 用户名下认证中/认证成功车辆=0
            Integer carCount = carService.getByUserIdAuth(userId);
            //认证中/认证成功车辆不为0，直接跳过
            logger.info("用户名下认证车辆数【{}】",carCount);
            if (!ZERO.equals(carCount)) {
                return resultMsgBean;
            }

           /* logger.info("当前货源是否拨打过电话【{}】",null != record);
            if (null != record) {
                return resultMsgBean;
            }*/

            if (null != user) {

                String remark = tytConfigService.getStringValue(NEW_CAR_APP_REMARK,"您未进行车辆认证，暂无法接单，请尽快认证");
                if(null != clientSign && 1 == clientSign){
                    remark = tytConfigService.getStringValue(NEW_CAR_PC_REMARK,"您未进行车辆认证，暂无法接单，请尽快前往特运通车主版APP进行认证");
                }
                //计算注册的天数
                int day = TimeUtil.daysBetween(user.getCtime(), new Date());
                //新注册30天内
                if (DAY >= day) {
                    logger.info("新注册用户拨打阻断");
                    //有拨打次数,走配置阻断
                    resultMsgBean = getNewUserCar(remark,userId,resultMsgBean,config);
                    return resultMsgBean;
                } else {
                    logger.info("老用户拨打阻断");
                    //会员还是非会员
                    String monthFirstDayFom = TimeUtil.getMonthFirstDayFom(new Date());
                    Date date = TimeUtil.parseDateString(monthFirstDayFom);
                    resultMsgBean = getUserCar(remark, userId, resultMsgBean,date,config);
                    return resultMsgBean;
                }
            }
        } catch (Exception e) {
            logger.info("查询阻断信息失败，用户id【{}】,货源id【{}】", userId, id);
        }
        return resultMsgBean;
    }



    /**
     * 老用户阻断
     * @param remark 返回值

     * @param userId 用户id

     * @param resultMsgBean 返回
     * @param date 当前月份第一天0点
     * @return ResultMsgBean
     */
    public ResultMsgBean getUserCar(String remark, Long userId, ResultMsgBean resultMsgBean,Date date,TytBlockConfig config ){
        Integer phones = tytUserCallPhoneRecordService.getByUserIdAndDate(userId, date);
        Boolean userPermission = getUserPermission(userId, "100101");
        if (userPermission) {
            logger.info("老用户会员拨打阻断");
            //会员走配置，自然月次数
            Integer num = config.getOldUserPermission();
            if(null == num){
                return resultMsgBean;
            }
            //Integer num = getConfigNum(CAR_PERMISSION_NUM);
            logger.info("老用户用会员权益判断，开关阻断数【{}】，已拨打次数【{}】",num,phones);
            if (null != phones && phones > num) {
                //大于阻断次数
                resultMsgBean.setCode(ReturnCodeConstant.CAR_PERMISSION_ERROR);
                resultMsgBean.setMsg(remark);
                return resultMsgBean;
            }

        } else {
            //非会员---是否有赠送拨次
            Boolean carPermission = getUserPermission(userId, "100102");
            //有拨打次数,走配置阻断
            if (carPermission) {
                logger.info("老用户非会员拨打阻断");
                Integer num = config.getOldUserPhone();
                if(null == num){
                    return resultMsgBean;
                }
                //Integer num = getConfigNum(CAR_CALLING_NUM);
                logger.info("老用户用拨打次数权益判断，开关阻断数【{}】，已拨打次数【{}】",num,phones);
                if (null != phones && phones > num) {
                    //大于不阻断次数
                    resultMsgBean.setCode(ReturnCodeConstant.CAR_PERMISSION_ERROR);
                    resultMsgBean.setMsg(remark);
                    return resultMsgBean;
                }
            }
        }
        return resultMsgBean;
    }


    /**
     * 新注册用户的阻断
     * @param remark 返回描述

     * @param userId 用户id
     * @param resultMsgBean 返回
     * @return ResultMsgBean
     */
    public ResultMsgBean getNewUserCar(String remark, Long userId, ResultMsgBean resultMsgBean,TytBlockConfig config){
        Integer num = config.getNewUserPermission();
        if(null == num){
            return resultMsgBean;
        }

        List<TytUserCallPhoneRecord> byUserId = tytUserCallPhoneRecordService.getByUserId(String.valueOf(userId));
        Boolean userPermission = getUserPermission(userId, "100101");
        if (userPermission) {
            logger.info("新用户会员会员阻断");
            //会员走配置，
            //Integer num = getConfigNum(NEW_CAR_PERMISSION_NUM);
            logger.info("新用户用会员权益判断，开关阻断数【{}】，已拨打次数【{}】",num,CollectionUtils.isNotEmpty(byUserId) ? byUserId.size() : 0);
            if (CollectionUtils.isNotEmpty(byUserId) && byUserId.size() > num) {
                //大于阻断次数
                resultMsgBean.setCode(ReturnCodeConstant.CAR_PERMISSION_ERROR);
                resultMsgBean.setMsg(remark);
                return resultMsgBean;
            }
        } else {
            //判断是否有赠送拨打次数
            Boolean carPermission = getUserPermission(userId, "100102");
            if (carPermission) {
                //Integer num = getConfigNum(NEW_CAR_PERMISSION_NUM);
                logger.info("新用户用拨打次数权益判断，开关阻断数【{}】，已拨打次数【{}】",num,CollectionUtils.isNotEmpty(byUserId) ? byUserId.size() : 0);
                if (CollectionUtils.isNotEmpty(byUserId) && byUserId.size() > num) {
                    //大于不阻断次数
                    resultMsgBean.setCode(ReturnCodeConstant.CAR_PERMISSION_ERROR);
                    resultMsgBean.setMsg(remark);
                    return resultMsgBean;
                }
            }
        }
        return resultMsgBean;
    }

    public Integer getConfigNum(String value){
        Integer num = tytConfigService.getIntValue(value, 0);
        return num;
    }

    /**
     * 查询用户权益信息
     * @param userId 用户id
     * @param typeId 权益类型id
     * @return Boolean
     */
    public Boolean getUserPermission(Long userId,String typeId){
        UserPermission userPermission = userPermissionService.getUserPermission(userId, typeId);
        if(null == userPermission){
            return false;
        }
        if(1 == userPermission.getStatus()){
            return true;
        }
        return false;
    }


    /**
     * 获取是否弹虚拟号弹窗以及是否可复制真实号码信息
     * @param base 基础信息
     * @param goodId 货源ID
     * @return
     */
    @PostMapping(value = {"/getPrivacyPhoneTabInfo", "/getPrivacyPhoneTabInfo.action"})
    @ResponseBody
    public ResultMsgBean getPrivacyPhoneTabInfo(BaseParameter base, @RequestParam(name="goodId",required = false) String goodId
            , @RequestParam(name="carUserId",required = false) Long carUserId) {
        if (StringUtils.isBlank(goodId)) {
            return ResultMsgBean.failResponse(10001, "请求参数错误");
        }

        CellPhoneListBean cellPhoneListBean = new CellPhoneListBean();

        TransportMain transportMain = transportMainService.getTransportMainForId(Long.valueOf(goodId));
        if (transportMain == null || transportMain.getSrcMsgId() == null) {
            return ResultMsgBean.failResponse(10001, "货源不存在");
        }
        if (carUserId == null) {
            carUserId = base.getUserId();
        }
        makeTelTab(transportMain, carUserId, cellPhoneListBean);
        if (cellPhoneListBean.getPrivacyPhoneTabInfo() == null) {
            return ResultMsgBean.successResponse(new PrivacyPhoneTabInfo(false, false));
        }
        return ResultMsgBean.successResponse(cellPhoneListBean.getPrivacyPhoneTabInfo());
    }

    /**
     * 找货、订单获取隐私号（车->货）
     * @param privacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq（必要项：货源ID、操作类型、车主手机号、货主手机号序号、车主用户ID）
     * @return ResultMsgBean data(隐私号字符串)
     */
    @PostMapping(value = {"/getPrivacyPhoneNumCToT", "/getPrivacyPhoneNumCToT.action"})
    @ResponseBody
    public ResultMsgBean getPrivacyPhoneNumCToT(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq, BaseParameter base) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "获取数据成功");
        if (privacyPhoneNumGoodIdReq == null || privacyPhoneNumGoodIdReq.getGoodId() == null || privacyPhoneNumGoodIdReq.getOperateType() == null
                || privacyPhoneNumGoodIdReq.getGoodUserPhoneIDX() == null || StringUtils.isBlank(privacyPhoneNumGoodIdReq.getDriverUserPhone())) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("请求参数错误");
            return resultMsgBean;
        }
        TransportMain transportMain = transportMainService.getById(privacyPhoneNumGoodIdReq.getGoodId());
        if (transportMain == null) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("货源不存在");
            return resultMsgBean;
        }
        String goodUserPhone = privacyPhoneNumGoodIdReq.getGoodUserPhoneIDX();
        if (StringUtils.isBlank(goodUserPhone)) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("货主手机号请求参数错误");
            return resultMsgBean;
        }

        //是否使用虚拟号开关
        int isUsePrivacyPhoneNum = tytConfigService.getIntValue("USE_PRIVACY_PHONE_NUM", 1);
        if (isUsePrivacyPhoneNum != 1) {
            resultMsgBean.setData(goodUserPhone);
            return resultMsgBean;
        }

        privacyPhoneNumGoodIdReq.setDriverUserId(base.getUserId());
        privacyPhoneNumGoodIdReq.setGoodUserPhone(goodUserPhone);
        logger.info("getPrivacyPhoneNumByGoodId GoodId is 【{}】, GoodUserPhone is 【{}】, DriverUserPhone is 【{}】",privacyPhoneNumGoodIdReq.getGoodId(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), privacyPhoneNumGoodIdReq.getDriverUserPhone());
        ResultMsgBean resultMsgBeanByGetPrivacyPhoneNum = privacyPhoneNumService.getPrivacyPhoneNum(privacyPhoneNumGoodIdReq, resultMsgBean);
        if (!resultMsgBeanByGetPrivacyPhoneNum.isSuccess() || resultMsgBeanByGetPrivacyPhoneNum.getData() == null) {
            logger.info("获取虚拟号失败 GoodId is 【{}】, GoodUserPhone is 【{}】, DriverUserPhone is 【{}】原因：【{}】",privacyPhoneNumGoodIdReq.getGoodId(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), privacyPhoneNumGoodIdReq.getDriverUserPhone(), resultMsgBeanByGetPrivacyPhoneNum.getMsg());
            //绑定虚拟号失败则直接返回真实手机号，保证可用性
            resultMsgBean.setData(goodUserPhone);
        } else {
            resultMsgBean.setData(resultMsgBeanByGetPrivacyPhoneNum.getData());
        }
        return resultMsgBean;
    }

    /**
     * 找货、订单获取隐私号（货->车）
     * @param privacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq（必要项：货源ID、操作类型、货主手机号、车主用户ID）
     * @return ResultMsgBean data(隐私号字符串)
     */
    @PostMapping(value = {"/getPrivacyPhoneNumTToC", "/getPrivacyPhoneNumTToC.action"})
    @ResponseBody
    public ResultMsgBean getPrivacyPhoneNumTToC(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq, BaseParameter base) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "获取数据成功");
        if (privacyPhoneNumGoodIdReq == null || privacyPhoneNumGoodIdReq.getGoodId() == null || privacyPhoneNumGoodIdReq.getOperateType() == null
                || StringUtils.isBlank(privacyPhoneNumGoodIdReq.getGoodUserPhone()) || privacyPhoneNumGoodIdReq.getDriverUserId() == null) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("请求参数错误");
            return resultMsgBean;
        }
        TransportMain transportMain = transportMainService.getById(privacyPhoneNumGoodIdReq.getGoodId());
        if (transportMain == null) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("货源不存在");
            return resultMsgBean;
        }
        String driverUserPhone = null;

        try {
            TytTransportOrders tytTransportOrders = transportOrdersService.userIsPay(transportMain.getSrcMsgId(), privacyPhoneNumGoodIdReq.getDriverUserId());
            if (tytTransportOrders != null && tytTransportOrders.getThirdpartyPlatformType() != null && tytTransportOrders.getThirdpartyPlatformType() == 1) {
                //已成单，并且承运方为运满满车主，车主手机号做特殊处理
                driverUserPhone = tytTransportOrders.getPayCellPhone();
            } else {
                User driverUser = userService.getByUserId(privacyPhoneNumGoodIdReq.getDriverUserId());
                if (driverUser != null) {
                    driverUserPhone = driverUser.getCellPhone();
                }
            }
        } catch (Exception e) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("获取车主手机号错误");
            return resultMsgBean;
        }
        if (StringUtils.isBlank(driverUserPhone)) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("车主手机号请求参数错误");
            return resultMsgBean;
        }

        //是否使用虚拟号开关
        int isUsePrivacyPhoneNum = tytConfigService.getIntValue("USE_PRIVACY_PHONE_NUM", 1);
        if (isUsePrivacyPhoneNum != 1) {
            resultMsgBean.setData(driverUserPhone);
            return resultMsgBean;
        }

        privacyPhoneNumGoodIdReq.setDriverUserPhone(driverUserPhone);
        logger.info("getPrivacyPhoneNumByGoodId GoodId is 【{}】, GoodUserPhone is 【{}】, DriverUserPhone is 【{}】",privacyPhoneNumGoodIdReq.getGoodId(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), privacyPhoneNumGoodIdReq.getDriverUserPhone());
        ResultMsgBean resultMsgBeanByGetPrivacyPhoneNum = privacyPhoneNumService.getPrivacyPhoneNum(privacyPhoneNumGoodIdReq, resultMsgBean);
        if (!resultMsgBeanByGetPrivacyPhoneNum.isSuccess() || resultMsgBeanByGetPrivacyPhoneNum.getData() == null) {
            logger.info("获取虚拟号失败 GoodId is 【{}】, GoodUserPhone is 【{}】, DriverUserPhone is 【{}】原因：【{}】",privacyPhoneNumGoodIdReq.getGoodId(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), privacyPhoneNumGoodIdReq.getDriverUserPhone(), resultMsgBeanByGetPrivacyPhoneNum.getMsg());
            //绑定虚拟号失败则直接返回真实手机号，保证可用性
            resultMsgBean.setData(driverUserPhone);
        } else {
            resultMsgBean.setData(resultMsgBeanByGetPrivacyPhoneNum.getData());
        }
        return resultMsgBean;
    }

    private Date getPubTime(Long tsId) {
        Long pubTime = (Long) cacheService.getObject(EXCELLENT_GOODS_PUBLISH_TIME + tsId);
        if (pubTime==null) return null;
        try {
            return new Date(pubTime);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 获取用户信用等级
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/getUserCreditInfo", "/getUserCreditInfo.action"})
    @ResponseBody
    public ResultMsgBean giveBack(@RequestParam(name = "userId", required = true) Long userId)
    {
        logger.info("getUserCreditInfo userId is 【{}】",userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }

            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
            resultMsgBean.setData(userCreditInfo);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        }
    }



    private void checkSuperiorCarSignForbid(Date pubTime, Long userId, ResultMsgBean resultBean) {
        //签约车直接校验签约的逻辑
//        TytSuperiorCarSign superiorCarSign = superiorCarSignService.getSuperiorCarSign(userId);

        Date now = new Date();
        //判断是否过了货源下放时间
        Integer levelTime = configService.getIntValue("SUPERIOR_CAR_SIGN:FORBID:LEVEL_TIME", 10);
        Date levelForbidTime = TimeUtil.addMinute(pubTime, levelTime);

        if (now.before(levelForbidTime)) {
//            if (superiorCarSign!=null) {
                checkSuperiorCarSignFeebid(userId,resultBean);
                if (resultBean.getCode()!=ResultMsgBean.OK) {
                    return;
                }
//            } else {
//                resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_SIGN_ERROR);
//                resultBean.setMsg("加入优车即可接单，接单可获得全能保障：价格保障、订金保障、放空保障、压车保障");
//                return;
//            }
        }
        Integer allTime = configService.getIntValue("SUPERIOR_CAR_SIGN:FORBID:ALL_TIME", 20);
        Date allForbidTime = TimeUtil.addMinute(pubTime, allTime);
        if (now.after(levelForbidTime)&&now.before(allForbidTime)) {
            //优车货源下放 等级开关（0-开启下放；1-关闭下放；）
            Integer levelOnOff = configService.getIntValue("SUPERIOR_CAR_SIGN:FORBID:LEVEL_ON_OFF", 0);
            if (levelOnOff==0) {
                String levels = configService.getStringValue("SUPERIOR_CAR_SIGN:FORBID:LEVELS", "4,5");
                Integer userLevel=getUserRankLevel(userId);
                boolean isForbid = true;
                for (String level : levels.split(",")) {
                    if (level.equals(String.valueOf(userLevel))) {
                        isForbid = false;
                    }
                }
                //符合下放等级
                if (!isForbid) {
                    //黑名单拦截
                    TytSuperiorCarSign superiorCarSign = superiorCarSignService.getSuperiorCarSign(userId);
                    if (superiorCarSign!=null&&superiorCarSign.getSignStatus()==2) {
                        resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR);
                        resultBean.setMsg("您当前的账号无法接优车货源，如有疑问请联系客服。");
                        return;
                    }
                } else {
//                    if (superiorCarSign!=null) {
                        checkSuperiorCarSignFeebid(userId,resultBean);
                        if (resultBean.getCode()!=ResultMsgBean.OK) {
                            return;
                        }
//                    } else {
//                        resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_SIGN_ERROR);
//                        resultBean.setMsg("加入优车即可接单，接单可获得全能保障：价格保障、订金保障、放空保障、压车保障");
//                        return;
//                    }
                }
            } else {
//                if (superiorCarSign!=null) {
                    checkSuperiorCarSignFeebid(userId,resultBean);
                    if (resultBean.getCode()!=ResultMsgBean.OK) {
                        return;
                    }
//                } else {
//                    resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_SIGN_ERROR);
//                    resultBean.setMsg("加入优车即可接单，接单可获得全能保障：价格保障、订金保障、放空保障、压车保障");
//                    return;
//                }
            }
        }


        if (now.after(allForbidTime)) {
            //优车货源下放 全量开关（0-开启下放；1-关闭下放；）
            Integer allOnOff = configService.getIntValue("SUPERIOR_CAR_SIGN:FORBID:ALL_ON_OFF", 0);
            //未开下放
            if (allOnOff!=0) {
//                if (superiorCarSign!=null) {
                    //优车货源下放 等级开关（0-开启下放；1-关闭下放；）
                    Integer levelOnOff = tytConfigService.getIntValue("SUPERIOR_CAR_SIGN:FORBID:LEVEL_ON_OFF", 0);
                    if (levelOnOff==0) {
                        String levels = tytConfigService.getStringValue("SUPERIOR_CAR_SIGN:FORBID:LEVELS", "");
                        Integer userLevel=getUserRankLevel(userId);
                        boolean isForbid = true;
                        for (String level : levels.split(",")) {
                            if (level.equals(String.valueOf(userLevel))) {
                                isForbid = false;
                            }
                        }
                        //符合下放等级
                        if (!isForbid) {
                            //黑名单拦截
                            TytSuperiorCarSign superiorCarSign = superiorCarSignService.getSuperiorCarSign(userId);
                            if (superiorCarSign!=null&&superiorCarSign.getSignStatus()==2) {
                                resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR);
                                resultBean.setMsg("您当前的账号无法接优车货源，如有疑问请联系客服。");
                            }
                        } else {
                            checkSuperiorCarSignFeebid(userId,resultBean);
                        }
                    } else {
                        checkSuperiorCarSignFeebid(userId,resultBean);
                    }
//                } else {
//                    resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_SIGN_ERROR);
//                    resultBean.setMsg("加入优车即可接单，接单可获得全能保障：价格保障、订金保障、放空保障、压车保障");
//                }
            } else {
                //黑名单拦截
                TytSuperiorCarSign superiorCarSign = superiorCarSignService.getSuperiorCarSign(userId);
                if (superiorCarSign!=null&&superiorCarSign.getSignStatus()==2) {
                    resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR);
                    resultBean.setMsg("您当前的账号无法接优车货源，如有疑问请联系客服。");
                }
            }
        }
    }

    private void checkSuperiorCarSignFeebid(Long userId,ResultMsgBean resultBean){
        //检查用户实名状态
//        TytUserIdentityAuth identityAuth = userIdentityAuthService.getByUserId(String.valueOf(userId));
//        if (identityAuth==null||identityAuth.getIdentityStatus()!=1) {
//            resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_IDENTITY_ERROR);
//            resultBean.setMsg("您的资料还未审核通过，暂时无法接单。如有问题请联系客服。");
//            return;
//        }
        //检查用户车辆状态
//        List<Car> listByUserId = carService.getListByUserId(userId);
//        if (listByUserId!=null) {
//            List<Car> newCars = listByUserId.stream().filter(car -> "1".equals(car.getAuth())).collect(Collectors.toList());
//            if (newCars.size()<=0) {
//                resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_CAR_ERROR);
//                resultBean.setMsg("您未车辆认证或您不是付费会员，暂时无法接单。如有问题请联系客服。");
//                return;
//            }
//        }
        //检查付费会员
//        UserPermission userPermission = userPermissionService.getUserPermission(userId,"100101");
//        if (userPermission==null||userPermission.getStatus()!=1) {
//            resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_VIP_ERROR);
//            resultBean.setMsg("您未车辆认证或您不是付费会员，暂时无法接单。如有问题请联系客服。");
//            return;
//        }
        // 6380需求要求 拨打权限下放 拥有拨打次卡也可以拨打电话找优货
        // 检查是否有车次卡
//        UserPermission userPermissionNum = userPermissionService.getUserPermission(userId, UserPermissionTypeEnum.CAR_NUM.getTypeId());
        //当没有车次卡 或者车次卡已过期时
//        if (userPermissionNum == null || userPermissionNum.getStatus() != 1) {
//            //检查付费会员
//            UserPermission userPermission = userPermissionService.getUserPermission(userId, UserPermissionTypeEnum.CAR_VIP.getTypeId());
//            //当不是付费会员或者 会员已过期时 则提示
//            if (userPermission == null || userPermission.getStatus() != 1) {
//                resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_VIP_ERROR);
//                resultBean.setMsg("您没有找货权益，暂时无法接单。如有问题请联系客服。");
//                return;
//            }
//        }
        //检查是否签约
        TytSuperiorCarSign superiorCarSign = superiorCarSignService.getSuperiorCarSign(userId);
//        if (superiorCarSign==null){
//            resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_NO_SIGN_ERROR);
//            resultBean.setMsg("加入优车即可接单，接单可获得全能保障：价格保障、订金保障、放空保障、压车保障");
//        } else {
            if (superiorCarSign!=null&&superiorCarSign.getSignStatus()==2) {
                resultBean.setCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR);
                resultBean.setMsg("您当前的账号无法接优车货源，如有疑问请联系客服。");
            }
//        }
    }


    //获取用户等级
    private Integer getUserRankLevel(Long userId){
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
        if(userCreditInfo!=null
                &&StringUtils.isNotBlank(userCreditInfo.getCarCreditRankLevel())){
            return Integer.valueOf(userCreditInfo.getCarCreditRankLevel());
        }
        return 0;
    }

    private void makeTelTab(TransportMain transportMain, Long carUserId, CellPhoneListBean cellPhoneListBean) {
        if (cellPhoneListBean == null) {
            return;
        }
        PrivacyPhoneTabInfo privacyPhoneTabInfo = new PrivacyPhoneTabInfo(false, false);
        cellPhoneListBean.setPrivacyPhoneTabInfo(privacyPhoneTabInfo);

        if (transportMain == null || transportMain.getSrcMsgId() == null) {
            return;
        }

        boolean isCommissionTransport = false;
        if (StringUtils.isNotBlank(transportMain.getLabelJson())) {
            TransportLabelJson transportLabelJson = transportBusiness.getTransportLabelJson(transportMain.getLabelJson());
            if (transportLabelJson != null && transportLabelJson.getCommissionTransport() != null && transportLabelJson.getCommissionTransport() == 1) {
                UserPermission userPermission = userPermissionService.getUserPermission(carUserId, UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode());
                TytTransportTecServiceFee transportTecServiceFee = tytTransportTecServiceFeeMapper.getBySrcMsgId(transportMain.getSrcMsgId());
                if (transportTecServiceFee != null) {
                    if (null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus())) {
                        isCommissionTransport = (transportTecServiceFee.getMemberShowPrivacyPhoneTab() == null || transportTecServiceFee.getMemberShowPrivacyPhoneTab() == 1);
                    } else {
                        isCommissionTransport = (transportTecServiceFee.getNoMemberShowPrivacyPhoneTab() == null || transportTecServiceFee.getNoMemberShowPrivacyPhoneTab() == 1);
                    }
                } else {
                    isCommissionTransport = true;
                }
            }
        }

        boolean firstHonourAnAgreementboolean = false;
        Integer firstHonourAnAgreement = tytTransportOrdersMapper.firstHonourAnAgreement(transportMain.getUserId());
        if (firstHonourAnAgreement == null || firstHonourAnAgreement == 0) {
            firstHonourAnAgreementboolean = true;
        }

        //如果是发货货主在虚拟号弹窗ab测试中 或者 货源是抽佣货源并且虚拟号开关打开 或者 首履货主 拨打电话弹窗为虚拟号弹窗
        if (isInPrivacyPhoneTabAbTestByTransportUserId(transportMain.getUserId()) || isCommissionTransport || firstHonourAnAgreementboolean) {
            privacyPhoneTabInfo.setShowPrivacyPhoneTab(true);
        }

        if (privacyPhoneTabInfo.getShowPrivacyPhoneTab()) {
            //如果弹窗为虚拟号弹窗，判断该车主是否已经支付该货源，如果已支付，则可复制真实手机号
            TytTransportOrders tytTransportOrders = transportOrdersService.userIsPay(transportMain.getSrcMsgId(), carUserId);
            if (tytTransportOrders != null) {
                privacyPhoneTabInfo.setCanCopyrealPhoneNum(true);
            }
        }
    }

    private boolean isShowPrivacyPhoneTabYMM (TransportMain transportMain) {
        if (transportMain.getSourceType() == 4 && tytConfigService.getIntValue("privacy_phone_tab_show_ymm", 1) == 1) {
            return true;
        }
        return false;
    }

    private boolean isInPrivacyPhoneTabAbTestByTransportUserId(Long transportUserId) {
        List<String> abTestCodeList = new ArrayList<>();
        abTestCodeList.add("privacy_phone_tab_show_transport_abtest");
        List<TytAbtestConfigVo> userTypeList = abtestService.getUserTypeList(abTestCodeList, transportUserId);
        if (CollectionUtils.isNotEmpty(userTypeList)) {
            return userTypeList.get(0).getType() == 1;
        }
        return false;
    }

    private void makeTelByYunManMan(TransportMain transportMain, Long carUserId, CellPhoneListBean cellPhoneListBean) {
        //判断是否为满帮代调货源
        if (transportMain == null || transportMain.getSrcMsgId() == null || transportMain.getSourceType() != 4 || carUserId == null) {
            return;
        }
        if (cellPhoneListBean == null) {
            return;
        }
        TytTransportOrders tytTransportOrders = transportOrdersService.userIsPay(transportMain.getSrcMsgId(), carUserId);
        if (tytTransportOrders != null) {
            //已付订金才能获取到运满满真实货主打电话
            TytTransportDispatch tytTransportDispatch = tytTransportDispatchMapper.getTytTransportDispatchBySrcId(transportMain.getSrcMsgId());
            //真实货主手机号
            String giveGoodsPhone = tytTransportDispatch.getGiveGoodsPhone();
            //因为符合条件的YMM货源手机号本身就是真实货主手机号，所以增加这以下判断
            if (StringUtils.isNotBlank(cellPhoneListBean.getTel()) && !cellPhoneListBean.getTel().equals(giveGoodsPhone)) {
                cellPhoneListBean.setTel4(giveGoodsPhone);
            }
        }
    }

    @GetMapping(value = {"/test", "/test.action"})
    @ResponseBody
    public void test(@RequestParam("callId") String callId) {
        ASRTaskMsg asrTaskMsg = new ASRTaskMsg();
        asrTaskMsg.setCallId(callId);
        String jsonString = JSONObject.toJSONString(asrTaskMsg);
        tytMqMessageService.sendMsgCustom(jsonString, "INFRA_ASR_TOPIC", SerialNumUtil.generateSeriaNum(), "ASR_DISTRIBUTE", 0L);
    }

}
