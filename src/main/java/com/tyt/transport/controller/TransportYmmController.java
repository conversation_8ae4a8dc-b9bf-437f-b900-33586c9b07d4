package com.tyt.transport.controller;

import com.tyt.model.ResultMsgBean;
import com.tyt.transport.querybean.TransportYmmGoodsBean;
import com.tyt.transport.querybean.TransportYmmListReqBean;
import com.tyt.transport.service.TransportYMMService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 *@Description
 *<AUTHOR>
 *@Date 2023/10/8 10:42
 *@Version 1.0
 **/
@Controller
@RequestMapping("/plat/transportYmm")
public class TransportYmmController {

    @Autowired
    private TransportYMMService transportYMMService;


    @RequestMapping(value = "getList")
    @ResponseBody
    public ResultMsgBean getList(TransportYmmListReqBean req){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");

        if (req.getQueryId() == null || req.getQueryId().longValue() < 0) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("queryID参数错误");
            return resultMsgBean;
        }

        if (req.getQueryActionType() == null || req.getQueryActionType() < 1 || req.getQueryActionType() > 2) {
            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            resultMsgBean.setMsg("queryActionType参数错误");
            return resultMsgBean;
        }

        List<TransportYmmGoodsBean> transportYmmList =  transportYMMService.getTransportYmmList(req);

        resultMsgBean.setData(transportYmmList);
        return resultMsgBean;
    }
}
