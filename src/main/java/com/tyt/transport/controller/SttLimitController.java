package com.tyt.transport.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.transport.querybean.SttLimitBean;
import com.tyt.transport.service.SttLimitService;
import com.tyt.util.ReturnCodeConstant;
/**
 * 发货限制规则接口 
 * 
 * 
 */
@Controller
@RequestMapping("/plat/sttLimit")
public class SttLimitController extends BaseController {
	
//	@Resource(name = "opLogService")
//	private OpLogService opLogService;
//
//	@Resource(name = "userService")
//	private UserService userService;
//
//	@Resource(name = "cacheServiceMcImpl")
//	private CacheService cacheService;
//
//	@Resource(name = "configService")
//	private ConfigService configService;

	@Resource(name = "sttLimitService")
	private SttLimitService sttLimitService;

	/**
	 * 发货条数限制查询接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean getList(BaseParameter baseParameter) {

		ResultMsgBean rm = new ResultMsgBean();

		try {		
				List<SttLimitBean>  list= sttLimitService.getSttLimitBeanList(baseParameter.getUserId());			
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;

	}

}
