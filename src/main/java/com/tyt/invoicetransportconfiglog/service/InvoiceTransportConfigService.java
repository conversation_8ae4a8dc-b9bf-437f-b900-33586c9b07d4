package com.tyt.invoicetransportconfiglog.service;


import com.tyt.invoicetransport.bean.TytInvoiceTransportEnterpriseConfigLogDTO;
import com.tyt.plat.entity.base.TytInvoiceTransportConfigLog;

public interface InvoiceTransportConfigService {

    TytInvoiceTransportConfigLog getLastTytInvoiceTransportConfig();

    /**
     * 获取该企业目前正在生效的开票货源发货配置
     *
     * @param enterpriseId 企业ID
     * @return
     */
    TytInvoiceTransportEnterpriseConfigLogDTO getLastTytInvoiceTransportEnterpriseConfig(Long enterpriseId, String serviceProviderCode);

}
