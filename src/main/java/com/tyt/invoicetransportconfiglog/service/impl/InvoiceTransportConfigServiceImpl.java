package com.tyt.invoicetransportconfiglog.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.infofee.enums.InvoiceServiceProviderEnum;
import com.tyt.invoicetransport.bean.TytInvoiceTransportConfigLogDTO;
import com.tyt.invoicetransport.bean.TytInvoiceTransportEnterpriseConfigLogDTO;
import com.tyt.invoicetransport.bean.TytInvoiceTransportPriceConfig;
import com.tyt.invoicetransportconfiglog.service.InvoiceTransportConfigService;
import com.tyt.plat.entity.base.TytInvoiceTransportConfigLog;
import com.tyt.plat.entity.base.TytInvoiceTransportEnterpriseConfigLog;
import com.tyt.plat.mapper.base.TytInvoiceTransportConfigLogMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.enums.YesOrNoEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;

@Service
public class InvoiceTransportConfigServiceImpl implements InvoiceTransportConfigService {

    @Autowired
    private TytInvoiceTransportConfigLogMapper tytInvoiceTransportConfigLogMapper;

    private static final String INVOICE_TRANSPORT_CONFIG_DATA = "invoice_transport_config_data";

    @Override
    public TytInvoiceTransportConfigLogDTO getLastTytInvoiceTransportConfig() {
        TytInvoiceTransportConfigLogDTO tytInvoiceTransportConfigLogDTO;
        TytInvoiceTransportConfigLog tytInvoiceTransportConfigLogCacheData = tytInvoiceTransportConfigLogMapper.getLastTytInvoiceTransportConfig();
        List<TytInvoiceTransportPriceConfig> tytInvoiceTransportPriceConfig = tytInvoiceTransportConfigLogMapper.getTytInvoiceTransportPriceConfig();
        if (tytInvoiceTransportConfigLogCacheData != null) {
            tytInvoiceTransportConfigLogDTO = new TytInvoiceTransportConfigLogDTO();
            BeanUtils.copyProperties(tytInvoiceTransportConfigLogCacheData, tytInvoiceTransportConfigLogDTO);
            tytInvoiceTransportConfigLogDTO.setTytInvoiceTransportPriceConfigList(tytInvoiceTransportPriceConfig);
            tytInvoiceTransportConfigLogDTO.setOperationType(null);
        } else {
            tytInvoiceTransportConfigLogDTO = new TytInvoiceTransportConfigLogDTO();
        }
        return tytInvoiceTransportConfigLogDTO;
    }

    @Override
    public TytInvoiceTransportEnterpriseConfigLogDTO getLastTytInvoiceTransportEnterpriseConfig(Long enterpriseId, String serviceProviderCode) {
        TytInvoiceTransportEnterpriseConfigLogDTO tytInvoiceTransportEnterpriseConfigLogDTO = new TytInvoiceTransportEnterpriseConfigLogDTO();

        Integer count = tytInvoiceTransportConfigLogMapper.isHaveLastTytInvoiceTransportEnterpriseConfig(enterpriseId);
        if (count == null || count == 0) {
            TytInvoiceTransportConfigLogDTO lastTytInvoiceTransportConfig = getLastTytInvoiceTransportConfig();
            BeanUtils.copyProperties(lastTytInvoiceTransportConfig, tytInvoiceTransportEnterpriseConfigLogDTO);
            // 如果是xhl，不进行分段支付
            if (StringUtils.isNotBlank(serviceProviderCode) && Objects.equals(serviceProviderCode, InvoiceServiceProviderEnum.XHL.getCode())) {
                tytInvoiceTransportEnterpriseConfigLogDTO.setSegmentedPayments(YesOrNoEnum.N.getCode());
            }
            return tytInvoiceTransportEnterpriseConfigLogDTO;
        }

        //该企业存在自己配置
        TytInvoiceTransportEnterpriseConfigLog tytInvoiceTransportEnterpriseConfigLogCacheData = tytInvoiceTransportConfigLogMapper.getLastTytInvoiceTransportEnterpriseConfig(enterpriseId);
        tytInvoiceTransportEnterpriseConfigLogDTO = new TytInvoiceTransportEnterpriseConfigLogDTO();
        BeanUtils.copyProperties(tytInvoiceTransportEnterpriseConfigLogCacheData, tytInvoiceTransportEnterpriseConfigLogDTO);

        List<TytInvoiceTransportPriceConfig> tytInvoiceTransportPriceEnterpriseConfig = tytInvoiceTransportConfigLogMapper.getTytInvoiceTransportPriceEnterpriseConfig(enterpriseId);
        tytInvoiceTransportEnterpriseConfigLogDTO.setTytInvoiceTransportPriceConfigList(tytInvoiceTransportPriceEnterpriseConfig);

        if (tytInvoiceTransportEnterpriseConfigLogDTO.getId() != null) {
            tytInvoiceTransportEnterpriseConfigLogDTO.setOperationType(null);
        }
        // 如果是xhl，不进行分段支付
        if (StringUtils.isNotBlank(serviceProviderCode) && Objects.equals(serviceProviderCode, InvoiceServiceProviderEnum.XHL.getCode())) {
            tytInvoiceTransportEnterpriseConfigLogDTO.setSegmentedPayments(YesOrNoEnum.N.getCode());
        }
        return tytInvoiceTransportEnterpriseConfigLogDTO;
    }
}
