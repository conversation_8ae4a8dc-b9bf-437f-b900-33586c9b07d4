package com.tyt.invoicetransportconfiglog.enums;

import lombok.Getter;

/**
* description 服务商类型
* <AUTHOR>
* @since 2025/2/20 16:38
*/
@Getter
public enum InvoiceServiceTypeEnum {
    // 定义枚举常量
    HBWJ(1, "湖北我家"),
    XHL(2, "翔和翎");

    // 定义属性
    private final Integer code;
    private final String desc;

    // 构造函数
    InvoiceServiceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // 获取code
    public Integer getCode() {
        return code;
    }

    // 获取desc
    public String getDesc() {
        return desc;
    }

    // 根据code获取对应的枚举实例
    public static InvoiceServiceTypeEnum getByCode(Integer code) {
        for (InvoiceServiceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null; // 或者抛出自定义异常，表示未找到匹配项
    }
}
