package com.tyt.filter;

import java.io.IOException;
import java.util.Enumeration;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

@Component("webLogFilter")
public class WebLogFilter extends OncePerRequestFilter {
    private static final Logger logger = LoggerFactory.getLogger(WebLogFilter.class);

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        // 使用唯一ID标识每次请求
        String traceId = IdUtil.simpleUUID();
        MDC.put("TRACE_ID", traceId);

        try {
            String requestURI = request.getRequestURI();
            ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
            logger.info("接收到请求: traceId：{}, requestURI: {}, 请求参数: {}",
                    traceId, requestURI, getFilteredParameters(requestWrapper));

            StopWatch watch = new StopWatch();
            watch.start();
            filterChain.doFilter(request, response);
            watch.stop();

            String responseStr = new String(responseWrapper.getContentAsByteArray());
            if (!StringUtils.isEmpty(responseStr) && responseStr.length() > 1000) {
                responseStr = responseStr.substring(0, 1000) + "...";
            }
            logger.info("响应完成: traceId：{}, requestURI: {}, 响应内容: {}, 耗时： {} ms",
                    traceId, requestURI, responseStr, watch.getTotalTimeMillis());
            responseWrapper.copyBodyToResponse();
        } finally {
            MDC.remove("TRACE_ID");
        }
    }

    private String getFilteredParameters(HttpServletRequest request) {
        StringBuilder params = new StringBuilder();
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            if (!paramName.equals("password") && !paramName.equals("pwd")) { // 过滤敏感字段
                params.append(paramName).append("=").append(request.getParameter(paramName));
                if (parameterNames.hasMoreElements()) {
                    params.append(", ");
                }
            }
        }
        return params.toString();
    }
}