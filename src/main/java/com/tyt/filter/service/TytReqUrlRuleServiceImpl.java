package com.tyt.filter.service;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.filter.service.parser.RuleSplitUtils;
import com.tyt.model.TytReqUrlRule;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.BloomFilterService;
import com.tyt.util.Encoder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 该类实现了`重复请求过滤`的逻辑, 主要是根据每次请求的`url`来获取规则：
 *     定义了规则：将参与签名的参数值获取`MD5`散列值，验证`redis`是否存在并且存储
 *               借助了`布隆过滤器`来实现
 *     未定义规则：直接返回 true
 * 验证规则是按照每天的格式存储(规则隔天生效,一天内的规则基本上不变)，主要分为了`本地存储`,`redis存储`,`mysql储存`
 * 规则会按照如上的顺序依次读取并且反向装入
 *
 * 使用了全局变量:
 *     LOCAL_CACHE: 定义了本地存储库，格式为 key -> 见 `REDIS_RULE_KEY`
 *                                       value -> Map,该Map定义了具体`url`的规则：key -> url
 *                                                                             value -> TytReqUrlRule
 *
 *     REDIS_RULE_KEY: 定义了当天生效的规则信息：格式为 tyt:req:filter:rule:`MM`:`dd`
 *
 *     BLOOM_KEY: 定义了`布隆过滤器`基于`redis`的key,每个`url`每天一个新的`布隆过滤器`,具体如下
 *                 tyt:filter:bloom:`url`:`MM`:`dd`
 *
 * @see TytReqUrlRule 规则存储类
 * <AUTHOR>
 * @date 2020/12/10 09:43
 */
@Service("tytReqUrlRuleService")
public class TytReqUrlRuleServiceImpl extends BaseServiceImpl<TytReqUrlRule, Long> implements TytReqUrlRuleService {

    private final static Logger logger = LoggerFactory.getLogger(TytReqUrlRuleServiceImpl.class);


    public final static String BLOOM_KEY = "tyt:filter:bloom:%s:%d:%d";

    public final static String REDIS_RULE_KEY = "tyt:req:filter:rule:%d:%d";

    private final Map<String, Map<String, TytReqUrlRule>> LOCAL_CACHE = new ConcurrentHashMap<>(8);

    @Resource(name = "bloomFilterService")
    private BloomFilterService bloomFilterService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name="tytReqUrlRuleDao")
    public void setBaseDao(BaseDao<TytReqUrlRule, Long> baseDao) {
        super.setBaseDao(baseDao);
    }


    @Override
    public List<TytReqUrlRule> getRules() {
        return getBaseDao().find("from TytReqUrlRule where status = ?", 0);
    }

    @Override
    public Optional<TytReqUrlRule> getRule(String url) {
        if(StringUtils.isEmpty(url)){
            return Optional.empty();
        }

        String reqUrl = getReqUrl(url);
        List<TytReqUrlRule> rules = getBaseDao().find("from TytReqUrlRule where reqUrl=? and status=?", reqUrl, 0);
        if(CollectionUtils.isEmpty(rules)){
            return Optional.empty();
        }
        return Optional.ofNullable(rules.get(0));
    }

    @Override
    public Optional<TytReqUrlRule> getRuleByCache(String url) {
        if(StringUtils.isEmpty(url)){
            return Optional.empty();
        }
        String reqUrl = getReqUrl(url);
        String hashKey = getRuleMapKey();
        //获取请求过滤规则  先本地缓存获取 -> redis缓存获取 -> 数据库获取 然后 依次存至缓存
        Map<String, TytReqUrlRule> urlRuleMap = LOCAL_CACHE.computeIfAbsent(hashKey, k -> {
            //缓存是否存在 储存规则
            Map<String, TytReqUrlRule> ruleMap;
            if (RedisUtil.exists(hashKey)) {
                ruleMap = RedisUtil.getObjectMap(hashKey).entrySet()
                        .stream()
                        .collect(Collectors.toMap(Map.Entry::getKey,
                                                  e -> ReqUrlRule.getRule((TytReqUrlRule)e),
                                                  (e1, e2) -> e1, ConcurrentHashMap::new));
            } else {
                List<TytReqUrlRule> rules = getRules();
                ruleMap = rules.stream()
                        .collect(Collectors.toMap(TytReqUrlRule::getReqUrl,
                                                  Function.identity(),
                                                  (e1, e2) -> e1));
                //清除本地的缓存 help GC
                deleteCache();
                if(MapUtils.isNotEmpty(ruleMap)){
                    //储至 redis 设置有效时间
                    RedisUtil.mapObjectPut(hashKey, ruleMap);
                    RedisUtil.expireAt(hashKey, TimeUnit.HOURS.toSeconds(36));
                }
                ruleMap = ruleMap.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, e -> ReqUrlRule.getRule(e.getValue()),
                                (e1, e2) -> e1, ConcurrentHashMap::new));
            }
            return ruleMap;
        });

        return Optional.ofNullable(urlRuleMap.get(reqUrl));
    }

    @Override
    public Optional<TytReqUrlRule> getRuleUnconditional(String url) {
        if(StringUtils.isEmpty(url)){
            return Optional.empty();
        }
        String reqUrl = getReqUrl(url);
        List<TytReqUrlRule> rules = getBaseDao().find("from TytReqUrlRule where reqUrl=?", reqUrl);
        if(CollectionUtils.isEmpty(rules)){
            return Optional.empty();
        }
        return rules.stream().min(Comparator.comparingInt(TytReqUrlRule::getStatus));
    }

    @Override
    public boolean deleteCache(String url) {
        String reqUrl = getReqUrl(url);
        String hashKey = getRuleMapKey();
        RedisUtil.mapRemove(hashKey, reqUrl);
        Map<String, TytReqUrlRule> map = LOCAL_CACHE.get(hashKey);
        if(Objects.nonNull(map)){
            map.remove(reqUrl);
        }
        return true;
    }


    @Override
    public boolean deleteCache() {
        LOCAL_CACHE.clear();
        return true;
    }


    @Override
    public boolean checkApply(String url, String param) {
        // 则查看开关是否开启验证
        int value = tytConfigService.getIntValue("bloomFilterSwitch", 2);
        if(value == 2){
            return true;
        }

        if(StringUtils.isEmpty(url)){
            return true;
        }

        String reqUrl = getReqUrl(url);

        Optional<TytReqUrlRule> optional = getRuleByCache(reqUrl);
        if(optional.isPresent()){
            TytReqUrlRule rule = optional.get();
            //生成签名
            Optional<String> sign = DefaultReqUrlRuleHandler.builder(rule)
                    .paramProducer(param)
                    .cycleProducer()
                    .process();
            //若未生成签名则证明 不过滤
            if (sign.isPresent()) {
                logger.debug("sign 生成：{}", sign.get());

                LocalDate now = LocalDate.now();
                String key = String.format(BLOOM_KEY, reqUrl, now.getMonthValue(), now.getDayOfMonth());

                //查看布隆过滤器是否存在该值
                boolean result = bloomFilterService.containsAndCreate(key, sign.get(),24L, TimeUnit.HOURS, f ->{
                    Optional<TytReqUrlRule> urlRule = getRule(reqUrl);
                    if(urlRule.isPresent()){
                        TytReqUrlRule r = urlRule.get();
                        return Optional.of(new BloomFilterService.BloomInitConfig(
                                r.getMaxCapacity(),
                                r.getRepeatRate()));
                    }
                    deleteCache(reqUrl);
                    return Optional.empty();
                });

                //验证重复
                logger.info("布隆过滤器验证重复 sign:【{}】 url:【{}】 param:【{}】 rule:【{}】", sign.get(), url, param, rule);
                return result;
            }
        }
        return true;
    }

    /**
     * url 转换 兼容 `//`
     * @param url url
     * @return String
     */
    private static String getReqUrl(String url){
        return url.startsWith("//") ? url.substring(1) : url;
    }

    /**
     * 获取规则map的key 每天一个key
     * @return key
     */
    private static String getRuleMapKey(){
        LocalDate now = LocalDate.now();
        return String.format(REDIS_RULE_KEY, now.getMonthValue(), now.getDayOfMonth());
    }

    /**
     * 默认 统一处理重复验证规则
     * <AUTHOR>
     * @date 2020/12/10
     */
    private static class DefaultReqUrlRuleHandler implements ReqUrlRuleHandler{


        private final TytReqUrlRule rule;

        private final List<String> keyBox;

        private DefaultReqUrlRuleHandler(TytReqUrlRule rule) {
            this.rule = rule;
            this.keyBox = new ArrayList<>();
        }


        public static ReqUrlRuleHandler builder(TytReqUrlRule rule) {
            //返回还未进行预处理参数的 NoPrepareDefaultReqUrlRuleHandler
            return new NoPrepareDefaultReqUrlRuleHandler(rule);
        }


        @Override
        public ReqUrlRuleHandler paramProducer(String param) {

            //验证是否需要过滤
            if(rule instanceof ReqUrlRule){
                Map<String, String> paramValue = new HashMap<>();
                //将参与验证的 key 存放入 keyBox
                for (String name : ((ReqUrlRule) rule).getParamNames()) {
                    RuleSplitUtils.urlGetValue(param, name).ifPresent(s -> paramValue.put(name, s));
                }
                //如果不满足验证规则直接返回
                if (Boolean.FALSE.equals(((ReqUrlRule) rule).getParser().check(paramValue))) {
                    return new NoFilterDefaultReqUrlRuleHandler();
                }
            }

            if(StringUtils.isEmpty(rule.getSecretConstructor())){
                logger.error("====== secret_constructor is not empty =======");
                throw new IllegalStateException("secret_constructor is not empty");
            }

            //将参与验证的 key 存放入 keyBox
            for (String name : RuleSplitUtils.commaSplit(rule.getSecretConstructor())) {
                RuleSplitUtils.urlGetValue(param, name).ifPresent(keyBox::add);
            }

            return this;
        }

        @Override
        public ReqUrlRuleHandler cycleProducer() {
            //若设置repeatCycle 时间周期也 参与 生成签名
            if(Objects.nonNull(rule.getRepeatCycle()) && rule.getRepeatCycle() > 0){
                Date todayStart = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                long cycle = (System.currentTimeMillis() - todayStart.getTime()) / 1000 / rule.getRepeatCycle();
                keyBox.add(String.valueOf(cycle));
            }
            return this;
        }

        @Override
        public Optional<String> process() {
            //如果keyBox为空 则throw异常
            if(keyBox.isEmpty()){
                logger.error("====== secret_constructor config error =======");
                throw new IllegalStateException("secret_constructor config error");
            }
            //如果 keyBox 大小为 1 则证明只有一个参数参与 签名验证，则直接返回该参数
            if(keyBox.size() == 1){
                return Optional.of(keyBox.get(0));
            }
            //md5 生成签名串
            return Optional.ofNullable(Encoder.md5(String.join("&", keyBox)));
        }
    }

    /**
     * 无需要过滤的处理器
     * <AUTHOR>
     * @date 2020/12/15
     */
    public static class NoFilterDefaultReqUrlRuleHandler implements ReqUrlRuleHandler{

        @Override
        public ReqUrlRuleHandler paramProducer(String param) {
            return this;
        }
        @Override
        public ReqUrlRuleHandler cycleProducer() {
            return this;
        }
        @Override
        public Optional<String> process() {
            logger.info("======无须过滤=======");
            return Optional.empty();
        }
    }

    /**没有配置参数验证的处理器
     * <AUTHOR>
     * @date 2020/12/15
     */
    public static class NoPrepareDefaultReqUrlRuleHandler implements ReqUrlRuleHandler{
        private final DefaultReqUrlRuleHandler handler;

        private NoPrepareDefaultReqUrlRuleHandler(TytReqUrlRule rule) {
            handler = new DefaultReqUrlRuleHandler(rule);
        }

        @Override
        public ReqUrlRuleHandler paramProducer(String param) {
            return handler.paramProducer(param);
        }

        @Override
        public ReqUrlRuleHandler cycleProducer() {
            return this;
        }

        @Override
        public Optional<String> process() {
            logger.error("====== secret_constructor no deal with =======");
            throw new IllegalStateException("secret_constructor no deal with");
        }
    }
}
