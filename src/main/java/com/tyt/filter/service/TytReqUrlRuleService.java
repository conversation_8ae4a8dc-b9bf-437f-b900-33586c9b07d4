package com.tyt.filter.service;


import com.tyt.base.service.BaseService;
import com.tyt.filter.service.parser.FilterConditionParser;
import com.tyt.model.TytReqUrlRule;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface TytReqUrlRuleService extends BaseService<TytReqUrlRule, Long> {

    /**
     * 获取所有有效的验证规则
     * @return 集合 无值返回 Collections.emptyList();
     */
    List<TytReqUrlRule> getRules();


    /**
     * 根据 url 获取验证规则 只能获取有效的
     * @return 单个  无值返回 Optional.empty();
     */
    Optional<TytReqUrlRule> getRule(String url);

    /**
     * 根据 url 优先从缓存获取验证规则 只能获取有效的
     * @return 单个  无值返回 Optional.empty();
     */
    Optional<TytReqUrlRule> getRuleByCache(String url);


    /**
     * 根据 url 获取验证规则 有效无效都可以获取，若重复则优先 `有效`
     * @return 单个  无值返回 Optional.empty();
     */
    Optional<TytReqUrlRule> getRuleUnconditional(String url);


    /**
     * 拦截器调用 验证 url 与 请求参数 判定是否为重复请求
     * @param url url 内部会进行处理 兼容 `//` 双开始的url
     * @param param 请求参数
     * @return 验证结果 会将验证重复结果记录 并且可以通过开关控制是否打开
     */
    boolean checkApply(String url, String param);


    /**
     * 删除缓存中的 key
     * @param url 内部会进行处理 兼容 `//` 双开始的url
     * @return 结果
     */
    boolean deleteCache(String url);

    /**
     * 所有清空缓存
     * @return 结果
     */
    boolean deleteCache();





    /**
     * 定义了统一处理 重复验证规则的 标准
     * <AUTHOR>
     * @date 2020/12/10
     */
    interface ReqUrlRuleHandler{


        /**
         * 将 请求参数 进行处理
         * @param param 参数值 eg： clientId=74:D4:35:BF:20:2E&clientSign=1&clientVersion=3720
         * @return ReqUrlRuleHandler
         */
        ReqUrlRuleHandler paramProducer(String param);

        /**
         * 将 重复周期 进行处理
         * @return ReqUrlRuleHandler
         */
        ReqUrlRuleHandler cycleProducer();

        /**
         * 将参与签名的所有参数 签名，为流程结束操作
         * @return 处理后的结果签名
         */
        Optional<String> process();
    }

    /**
     * 该类定义了 TytReqUrlRule 拥有过滤条件
     * <AUTHOR>
     * @date 2020/12/15
     */
    class ReqUrlRule extends TytReqUrlRule{

        private final TytReqUrlRule rule;

        private final FilterConditionParser parser;

        private final Set<String> paramNames;

        public ReqUrlRule(TytReqUrlRule rule) {
            this.rule = rule;
            if(StringUtils.isEmpty(rule.getFilterCondition())){
                throw new IllegalStateException("rule 错误，不存在过滤规则");
            }
            this.parser = new FilterConditionParser.OrConditionParser();
            this.paramNames = this.parser.parser(rule.getFilterCondition());
        }

        public static TytReqUrlRule getRule(TytReqUrlRule rule){
            if(StringUtils.isEmpty(rule.getFilterCondition())){
                return rule;
            }
            return new ReqUrlRule(rule);
        }

        public Set<String> getParamNames() {
            return paramNames;
        }

        public FilterConditionParser getParser() {
            return parser;
        }

        public TytReqUrlRule getRule() {
            return rule;
        }

        @Override
        public Long getId() {
            return rule.getId();
        }

        @Override
        public String getReqUrl() {
            return rule.getReqUrl();
        }

        @Override
        public Integer getRepeatCycle() {
            return rule.getRepeatCycle();
        }

        @Override
        public String getSecretConstructor() {
            return rule.getSecretConstructor();
        }

        @Override
        public Integer getMaxCapacity() {
            return rule.getMaxCapacity();
        }

        @Override
        public Double getRepeatRate() {
            return rule.getRepeatRate();
        }

        @Override
        public String getFilterCondition() {
            return rule.getFilterCondition();
        }

        @Override
        public Integer getStatus() {
            return rule.getStatus();
        }

        @Override
        public Date getCtime() {
            return rule.getCtime();
        }

        @Override
        public Date getMtime() {
            return rule.getMtime();
        }
    }


}
