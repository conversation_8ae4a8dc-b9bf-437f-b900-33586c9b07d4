package com.tyt.filter.service.parser;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Objects;

/**
 * 符号处理逻辑
 * <AUTHOR>
 * @date 2020/12/15
 */
public interface ConditionHandler {

    boolean check(String target,String value);

    static ConditionHandler getHandler(String symbol){
        if(StringUtils.isNotEmpty(symbol)){
            symbol = symbol.trim();
            switch (symbol){
                case "=":return SymbolConditionHandler.eq;
                case "!=":return SymbolConditionHandler.neq;
                case ">":return SymbolConditionHandler.gt;
                case "<":return SymbolConditionHandler.lt;
                case ">=":return SymbolConditionHandler.gte;
                case "<=":return SymbolConditionHandler.lte;
                default:
                    throw new IllegalStateException("symbol 错误：" + symbol);
            }
        }
        throw new IllegalStateException("symbol 为空错误");
    }

    enum SymbolConditionHandler implements ConditionHandler{
        eq{
            @Override
            public boolean check(String target,String value) {
                return Objects.equals(target, RuleSplitUtils.removeQuote(value));
            }
        },
        neq{
            @Override
            public boolean check(String target,String value) {
                return Boolean.FALSE.equals(Objects.equals(target, RuleSplitUtils.removeQuote(value)));
            }
        },
        lt{
            @Override
            public boolean check(String target,String value) {
                if(NumberUtils.isNumber(target) && NumberUtils.isNumber(value)){
                    return NumberUtils.createInteger(target) > NumberUtils.createInteger(value);
                }
                return false;
            }
        },
        gt{
            @Override
            public boolean check(String target,String value) {
                if(NumberUtils.isNumber(target) && NumberUtils.isNumber(value)){
                    return NumberUtils.createInteger(target) < NumberUtils.createInteger(value);
                }
                return false;
            }
        },
        lte{
            @Override
            public boolean check(String target,String value) {
                if(NumberUtils.isNumber(target) && NumberUtils.isNumber(value)){
                    return NumberUtils.createInteger(target) >= NumberUtils.createInteger(value);
                }
                return false;
            }
        },
        gte{
            @Override
            public boolean check(String target,String value) {
                if(NumberUtils.isNumber(target) && NumberUtils.isNumber(value)){
                    return NumberUtils.createInteger(target) <= NumberUtils.createInteger(value);
                }
                return false;
            }
        }

    }


}
