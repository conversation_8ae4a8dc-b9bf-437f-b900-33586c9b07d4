package com.tyt.filter.service.parser;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;
import java.util.WeakHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020/12/15 15:11
 */
public abstract class RuleSplitUtils {

    /**
     * 运算符号匹配 正则
     */
    public static final Pattern SYMBOL_SPLIT = Pattern.compile("([\\w\\d]+)\\s*(.[!><=]?[=]?)\\s*(.*)");

    /**
     * 存储url取值 Pattern
     */
    public static final Map<String, Pattern> URL_PATTERN_MAP = new WeakHashMap<>(64);


    /**
     * 去掉字符串开头与结尾的引号
     * @param value 字符串
     * <AUTHOR>
     * @date 2020/12/15
     * @return String
     */
    public static String removeQuote(String value){
        return value.replaceAll("(^'|^\")|('$|\"$)","");
    }

    /**
     * 解析sql or
     * @param value 字符串
     * <AUTHOR>
     * @date 2020/12/15
     * @return String
     */
    public static String[] orSplit(String value){
       return spiltByRegex(value,"\\s+[Oo][Rr]\\s+");
    }

    /**
     * 解析sql and
     * @param value 字符串
     * <AUTHOR>
     * @date 2020/12/15
     * @return String
     */
    public static String[] andSplit(String value){
        return spiltByRegex(value,"\\s+[Aa][Nn][Dd]\\s+");
    }

    /**
     * 解析sql 空格
     * @param value 字符串
     * <AUTHOR>
     * @date 2020/12/15
     * @return String[]
     */
    public static String[] whiteSpaceSplit(String value){
        return spiltByRegex(value,"\\s+");
    }

    /**
     * 解析sql 按照运算符号解析
     * @param value 字符串
     * @return String[]
     */
    public static String[] symbolSplit(String value){
        Matcher matcher = SYMBOL_SPLIT.matcher(value);
        if (matcher.find() && matcher.groupCount() == 3) {
            return new String[]{
                    matcher.group(1).trim(),
                    matcher.group(2).trim(),
                    matcher.group(3).trim()};
        }
        return new String[0];
    }

    /**
     * 按照 , 分割字符串
     * @param value 字符串
     * <AUTHOR>
     * @date 2020/12/15
     * @return String[]
     */
    public static String[] commaSplit(String value){
        return spiltByRegex(value,",");
    }

    /**
     *
     * @param sql sql
     * @param regex 正则表达式
     * @return String[]
     */
    private static String[] spiltByRegex(String sql, String regex){
        if(StringUtils.isEmpty(sql)){
            throw new NullPointerException("sql is not empty");
        }
        return sql.trim().split(regex);
    }

    /**
     * 获取 Pattern  从 url截取值的正则
     * @param key 需要获取的参数值
     * <AUTHOR>
     * @date 2020/12/15
     * @return Pattern
     */
    public static Pattern urlGetValuePattern(String key){
        return URL_PATTERN_MAP.computeIfAbsent(key, k -> Pattern.compile("(^|&)" + k + "=([^&]*)(&|$)"));
    }

    /**
     * 从 url截取参数值
     * @param param url
     * @param key 需要获取的参数值
     * @return Optional<String>
     */
    public static Optional<String> urlGetValue(String param, String key){
        Pattern pattern = urlGetValuePattern(key);
        Matcher matcher = pattern.matcher(param);
        if (matcher.find()) {
            return Optional.of(matcher.group(2));
        }
        return Optional.empty();
    }
}
