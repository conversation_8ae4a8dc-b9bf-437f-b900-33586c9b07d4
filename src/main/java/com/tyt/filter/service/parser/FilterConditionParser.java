package com.tyt.filter.service.parser;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 过滤条件分析器
 * <AUTHOR>
 * @date 2020/12/14 16:30
 */
public interface FilterConditionParser {


    Set<String> parser(String sql);


    boolean check(Map<String, String> paramValueMap);


    /**
     * or 过滤条件分析器 优先级最高
     * <AUTHOR>
     * @date 2020/12/14 16:30
     */
   class OrConditionParser implements FilterConditionParser{

       private final List<AndConditionParser> andConditionParsers = new ArrayList<>();

       @Override
       public Set<String> parser(String sql) {
           String[] cons = RuleSplitUtils.orSplit(sql);
           Set<String> paramSet = new HashSet<>();
           for (String s : cons) {
               AndConditionParser parser = new AndConditionParser();
               andConditionParsers.add(parser);
               paramSet.addAll(parser.parser(s));
           }
           return paramSet;
       }

       @Override
       public boolean check(Map<String, String> paramValueMap) {
           for (AndConditionParser parser : andConditionParsers) {
               if (parser.check(paramValueMap)) {
                   return true;
               }
           }
           return false;
       }
   }



    /**
     * and 过滤条件分析器 优先级次高
     * <AUTHOR>
     * @date 2020/12/14 16:30
     */
   class AndConditionParser implements FilterConditionParser{

       private final List<SymbolConditionParser> symbolConditionParsers = new ArrayList<>();

       @Override
       public Set<String> parser(String sql) {
           String[] cons = RuleSplitUtils.andSplit(sql);
           Set<String> paramSet = new HashSet<>();
           for (String s : cons) {
               SymbolConditionParser parser = new SymbolConditionParser();
               symbolConditionParsers.add(parser);
               paramSet.addAll(parser.parser(s));
           }
           return paramSet;
       }

       @Override
       public boolean check(Map<String, String> paramValueMap) {
           for (SymbolConditionParser parser : symbolConditionParsers) {
               if (Boolean.FALSE.equals(parser.check(paramValueMap))) {
                   return false;
               }
           }
           return true;
       }
   }


    /**
     * 符号 = ! > < 过滤条件分析器 优先级最低
     * <AUTHOR>
     * @date 2020/12/14 16:30
     */
   class SymbolConditionParser implements FilterConditionParser{

        private final Logger logger = LoggerFactory.getLogger(this.getClass());


        private String name;

       private String targetValue;

       private ConditionHandler handler;

       @Override
       public Set<String> parser(String sql) {
           sql = sql.trim();
           if(StringUtils.isNotBlank(sql)){
               String[] cons = RuleSplitUtils.symbolSplit(sql);
               if(cons.length != 3){
                   throw new IllegalStateException("sql 解析错误: " + sql);
               }
               this.name = cons[0];
               this.handler = ConditionHandler.getHandler(cons[1]);
               this.targetValue = RuleSplitUtils.removeQuote(cons[2]);
               return Collections.singleton(this.name);
           }
           return Collections.emptySet();
       }

       @Override
       public boolean check(Map<String, String> paramValueMap) {
           String value = paramValueMap.get(this.name);
           if(value == null){
               logger.info("name :【" + this.name +"】取值错误");
               return false;
           }
           return this.handler.check(this.targetValue, value);
       }
   }





}
