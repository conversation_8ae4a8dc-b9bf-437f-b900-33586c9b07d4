package com.tyt.model;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * User: Administrator Date: 17-11-04 Time: 下午3:47
 */
@Entity
@Table(name = "tyt_transport_mt_change_log")
public class TransportMtChangeLog implements Serializable {

	private static final long serialVersionUID = 7305431403195575343L;
	private Long id;
	private Long srcMsgId;
	private String mtPrice;
	private String mtLicensePlate;
	private String conPrice;
	private String conLicensePlate;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "src_msg_id")
	public Long getSrcMsgId() {
		return srcMsgId;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}
	@Column(name = "mt_price")
	public String getMtPrice() {
		return mtPrice;
	}

	public void setMtPrice(String mtPrice) {
		this.mtPrice = mtPrice;
	}
	@Column(name = "mt_license_plate")
	public String getMtLicensePlate() {
		return mtLicensePlate;
	}

	public void setMtLicensePlate(String mtLicensePlate) {
		this.mtLicensePlate = mtLicensePlate;
	}
	@Column(name = "con_price")
	public String getConPrice() {
		return conPrice;
	}

	public void setConPrice(String conPrice) {
		this.conPrice = conPrice;
	}
	@Column(name = "con_license_plate")
	public String getConLicensePlate() {
		return conLicensePlate;
	}

	public void setConLicensePlate(String conLicensePlate) {
		this.conLicensePlate = conLicensePlate;
	}
}
