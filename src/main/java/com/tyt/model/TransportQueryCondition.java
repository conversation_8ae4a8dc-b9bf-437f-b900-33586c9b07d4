package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="tyt_transport_query_condition")
public class TransportQueryCondition  implements Serializable{

	private static final long serialVersionUID = 1L;
	Long id;//主键
	String cellPhone;//用户手机
	Integer startX;//起始x坐标，存整数
	Integer startY;//起始y坐标
	Integer destX;//目标x坐标
	Integer destY;//目标y坐标
	String startProvince;//起始省
	String startCity;//起始市
	String startCounty;//起始县
	String destProvince;//目的省
	String destCity;//目的市
	String destCounty;//目的县
	Integer startScope;//出发地范围
	Integer destScope;//目标范围
	Integer platId;//终端标识1PC
	Date ctime;//采集时间yyyy-mm-dd
	
	@Id
	@GeneratedValue
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="start_x")
	public Integer getStartX() {
		return startX;
	}
	public void setStartX(Integer startX) {
		this.startX = startX;
	}
	@Column(name="start_y")
	public Integer getStartY() {
		return startY;
	}
	public void setStartY(Integer startY) {
		this.startY = startY;
	}
	@Column(name="dest_x")
	public Integer getDestX() {
		return destX;
	}
	public void setDestX(Integer destX) {
		this.destX = destX;
	}
	@Column(name="dest_y")
	public Integer getDestY() {
		return destY;
	}
	public void setDestY(Integer destY) {
		this.destY = destY;
	}
	@Column(name="start_province")
	public String getStartProvince() {
		return startProvince;
	}
	public void setStartProvince(String startProvince) {
		this.startProvince = startProvince;
	}
	@Column(name="start_city")
	public String getStartCity() {
		return startCity;
	}
	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}
	@Column(name="start_county")
	public String getStartCounty() {
		return startCounty;
	}
	public void setStartCounty(String startCounty) {
		this.startCounty = startCounty;
	}
	@Column(name="dest_province")
	public String getDestProvince() {
		return destProvince;
	}
	public void setDestProvince(String destProvince) {
		this.destProvince = destProvince;
	}
	@Column(name="dest_city")
	public String getDestCity() {
		return destCity;
	}
	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}
	@Column(name="dest_county")
	public String getDestCounty() {
		return destCounty;
	}
	public void setDestCounty(String destCounty) {
		this.destCounty = destCounty;
	}
	@Column(name="start_scope")
	public Integer getStartScope() {
		return startScope;
	}
	public void setStartScope(Integer startScope) {
		this.startScope = startScope;
	}
	@Column(name="dest_scope")
	public Integer getDestScope() {
		return destScope;
	}
	public void setDestScope(Integer destScope) {
		this.destScope = destScope;
	}
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}
	public void setPlatId(Integer platId) {
		this.platId = platId;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	
}
