package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/01/18 17:34
 */
@Entity
@Table(name = "tyt_car_driver_examine_log")
public class TytCarDriverExamineLog implements java.io.Serializable {

    private Long id;
    private Long driverArchivesId;
    private String content;
    private Integer examineStatus;
    private Integer dicfExamineStatus;
    private String dicfExamineLoseNote;
    private Integer dicbExamineStatus;
    private String dicbExamineLoseNote;
    private Integer dlExamineStatus;
    private String dlExamineLoseNote;
    private Integer dcExamineStatus;
    private String dcExamineLoseNote;
    private Date ctime;
    private Long createUserId;
    private String createShowName;

    public void setId(Long id){
        this.id = id;
    }

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId(){
        return this.id;
    }


    public void setDriverArchivesId(Long driverArchivesId){
        this.driverArchivesId = driverArchivesId;
    }

    @Column(name = "driver_archives_id")
    public Long getDriverArchivesId(){
        return this.driverArchivesId;
    }

    public void setContent(String content){
        this.content = content;
    }

    @Column(name = "content")
    public String getContent(){
        return this.content;
    }

    public void setExamineStatus(Integer examineStatus){
        this.examineStatus = examineStatus;
    }

    @Column(name = "examine_status")
    public Integer getExamineStatus(){
        return this.examineStatus;
    }

    public void setDicfExamineStatus(Integer dicfExamineStatus){
        this.dicfExamineStatus = dicfExamineStatus;
    }

    @Column(name = "dicf_examine_status")
    public Integer getDicfExamineStatus(){
        return this.dicfExamineStatus;
    }

    public void setDicfExamineLoseNote(String dicfExamineLoseNote){
        this.dicfExamineLoseNote = dicfExamineLoseNote;
    }

    @Column(name = "dicf_examine_lose_note")
    public String getDicfExamineLoseNote(){
        return this.dicfExamineLoseNote;
    }

    public void setDicbExamineStatus(Integer dicbExamineStatus){
        this.dicbExamineStatus = dicbExamineStatus;
    }

    @Column(name = "dicb_examine_status")
    public Integer getDicbExamineStatus(){
        return this.dicbExamineStatus;
    }

    public void setDicbExamineLoseNote(String dicbExamineLoseNote){
        this.dicbExamineLoseNote = dicbExamineLoseNote;
    }

    @Column(name = "dicb_examine_lose_note")
    public String getDicbExamineLoseNote(){
        return this.dicbExamineLoseNote;
    }

    public void setDlExamineStatus(Integer dlExamineStatus){
        this.dlExamineStatus = dlExamineStatus;
    }

    @Column(name = "dl_examine_status")
    public Integer getDlExamineStatus(){
        return this.dlExamineStatus;
    }

    public void setDlExamineLoseNote(String dlExamineLoseNote){
        this.dlExamineLoseNote = dlExamineLoseNote;
    }

    @Column(name = "dl_examine_lose_note")
    public String getDlExamineLoseNote(){
        return this.dlExamineLoseNote;
    }

    public void setDcExamineStatus(Integer dcExamineStatus){
        this.dcExamineStatus = dcExamineStatus;
    }

    @Column(name = "dc_examine_status")
    public Integer getDcExamineStatus(){
        return this.dcExamineStatus;
    }

    public void setDcExamineLoseNote(String dcExamineLoseNote){
        this.dcExamineLoseNote = dcExamineLoseNote;
    }

    @Column(name = "dc_examine_lose_note")
    public String getDcExamineLoseNote(){
        return this.dcExamineLoseNote;
    }

    public void setCtime(Date ctime){
        this.ctime = ctime;
    }

    @Column(name = "ctime")
    public Date getCtime(){
        return this.ctime;
    }

    public void setCreateUserId(Long createUserId){
        this.createUserId = createUserId;
    }

    @Column(name = "create_user_id")
    public Long getCreateUserId(){
        return this.createUserId;
    }

    public void setCreateShowName(String createShowName){
        this.createShowName = createShowName;
    }

    @Column(name = "create_show_name")
    public String getCreateShowName(){
        return this.createShowName;
    }
}
