package com.tyt.model;


import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/10 17:18
 */
public class UsedCarAuditRecord implements Serializable {
    private static final long serialVersionUID = 4328054668941296010L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 二手车id
     */
    private Long usedCarSaleId;
    /**
     * 发布状态 1审核失败 2审核通过 3下架
     */
    private String status;
    /**
     * 操作人名称
     */
    private String userName;
    /**
     * 操作人id
     */
    private Long userId;
    /**
     * 审核失败或下架原因
     */
    private String reason;
    /**
     * 描述
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date ctime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUsedCarSaleId() {
        return usedCarSaleId;
    }

    public void setUsedCarSaleId(Long usedCarSaleId) {
        this.usedCarSaleId = usedCarSaleId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
}
