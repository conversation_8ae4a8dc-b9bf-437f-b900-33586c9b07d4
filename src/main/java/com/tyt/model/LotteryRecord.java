package com.tyt.model;

import com.tyt.promo.model.PromoCoupon;
import com.tyt.util.Constant;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 定向用户抽奖记录表
 * 
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Entity
@Table(name = "lottery_record")
public class LotteryRecord{
	/**
	 * 定向用户抽奖记录表id
	 */
	private Long id;
	/**
	 * 用户表id
	 */
	private Long userId;
	/**
	 * 用户手机号（账号）
	 */
	private String userCallPhone;
	/**
	 * 活动表id（由奖项创建的活动）
	 */
	private Long promoActivityId;
	/**
	 * 活动名称
	 */
	private String activityName;
	/**
	 * 活动表id（活动的类型）
	 */
	private Long drawActivityInfoId;
	/**
	 * 活动与奖项关系表id
	 */
	private Long probCouponId;
	/**
	 * 奖项名字
	 */
	private String probCouponName;
	/**
	 * 奖品类型 1 实物   2 代金券
	 */
	private Integer awardType;
	/**
	 * 奖券面值
	 */
	private BigDecimal couponAmount;
	/**
	 * 奖券过期时间（天）
	 */
	private Integer expirationTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 创建人
	 */
	private Long createBy;
	/**
	 * 修改人
	 */
	private Long updateBy;
	/**
	 * 是否删除（0：删除，1：不删除）
	 */
	private Integer isDelete;
	/**
	 * 是否中奖（0：未中奖，1：中奖）
	 */
	private Integer isWin;

	/**
	 * 来源（1：抽奖 2：其它）
	 */
	private Integer source;


	public LotteryRecord(User user, ProbCoupon probCoupon, PromoCoupon promoCoupon, DrawActivityInfo drawActivityInfo) {
		if(null != user){
			this.userId = user.getId();
			this.userCallPhone = user.getCellPhone();
		}
		if(null != probCoupon){
			this.promoActivityId = probCoupon.getPromoActivityId();
			this.drawActivityInfoId = probCoupon.getDrawActivityInfoId();
			this.probCouponId = probCoupon.getId();
			this.probCouponName = probCoupon.getActivityName();
			this.awardType = probCoupon.getAwardType();
		}
		this.activityName = drawActivityInfo.getActivityName();
		this.couponAmount = promoCoupon.getCouponAmount();
		this.expirationTime = Constant.EXPIRATION;
		this.createTime = new Date();
		this.createBy = user.getId();
		this.isDelete = 1;
	}

	public LotteryRecord() {
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name = "user_call_phone")
	public String getUserCallPhone() {
		return userCallPhone;
	}

	public void setUserCallPhone(String userCallPhone) {
		this.userCallPhone = userCallPhone;
	}
	@Column(name = "promo_activity_id")
	public Long getPromoActivityId() {
		return promoActivityId;
	}

	public void setPromoActivityId(Long promoActivityId) {
		this.promoActivityId = promoActivityId;
	}
	@Column(name = "draw_activity_info_id")
	public Long getDrawActivityInfoId() {
		return drawActivityInfoId;
	}

	public void setDrawActivityInfoId(Long drawActivityInfoId) {
		this.drawActivityInfoId = drawActivityInfoId;
	}
	@Column(name = "prob_coupon_id")
	public Long getProbCouponId() {
		return probCouponId;
	}

	public void setProbCouponId(Long probCouponId) {
		this.probCouponId = probCouponId;
	}
	@Column(name = "prob_coupon_name")
	public String getProbCouponName() {
		return probCouponName;
	}

	public void setProbCouponName(String probCouponName) {
		this.probCouponName = probCouponName;
	}
	@Column(name = "expiration_time")
	public Integer getExpirationTime() {
		return expirationTime;
	}

	public void setExpirationTime(Integer expirationTime) {
		this.expirationTime = expirationTime;
	}
	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Column(name = "create_by")
	public Long getCreateBy() {
		return createBy;
	}

	public void setCreateBy(Long createBy) {
		this.createBy = createBy;
	}

	@Column(name = "update_by")
	public Long getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(Long updateBy) {
		this.updateBy = updateBy;
	}

	@Column(name = "is_delete")
	public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	@Column(name = "award_type")
	public Integer getAwardType() {
		return awardType;
	}

	public void setAwardType(Integer awardType) {
		this.awardType = awardType;
	}
	@Column(name = "coupon_amount")
	public BigDecimal getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(BigDecimal couponAmount) {
		this.couponAmount = couponAmount;
	}

	@Column(name = "activity_name")
	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	@Column(name = "is_win")
	public Integer getIsWin() {
		return isWin;
	}

	public void setIsWin(Integer isWin) {
		this.isWin = isWin;
	}

	@Column(name = "source")
	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}
}
