package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="tyt_linkman")
public class LinkMan  implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8272454146364947869L;
	Long id;
	String cellPhone;
	String linkMan;
	Timestamp ctime;
	Integer platId;
	
	public LinkMan() {
	}
	
	public LinkMan(String cellPhone, String linkMan, Timestamp ctime) {
		super();
		this.cellPhone = cellPhone;
		this.linkMan = linkMan;
		this.ctime = ctime;
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="linkman")
	public String getLinkMan() {
		return linkMan;
	}
	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}
	public void setPlatId(Integer platId) {
		this.platId = platId;
	}

	@Override
	public String toString() {
		return "[cellPhone:"+cellPhone+",linkMan:"+linkMan+",platId"+platId+"]";
	}
	
	
}
