package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-4-14下午6:46:48
 * @description
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "tyt_matches_click", catalog = "tyt")
public class TytMatchesClick implements java.io.Serializable {

	private long id;
	private int machineTypeId;
	private String searchWord;
	private Date createTime;
	private Integer userId;
	private Integer clientType = 1;

	public TytMatchesClick() {
	}

	public TytMatchesClick(long id, int machineTypeId, String searchWord, Date createTime) {
		this.id = id;
		this.machineTypeId = machineTypeId;
		this.searchWord = searchWord;
		this.createTime = createTime;
	}

	public TytMatchesClick(int machineTypeId, String searchWord, Date createTime, Integer userId) {
		this.machineTypeId = machineTypeId;
		this.searchWord = searchWord;
		this.createTime = new Date();
		this.userId = userId;
	}

	@Column(name = "user_id")
	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	@Column(name = "client_type")
	public Integer getClientType() {
		return clientType;
	}

	public void setClientType(Integer clientType) {
		this.clientType = clientType;
	}

	@Id
	@Column(name = "id", unique = true, nullable = false)
	public long getId() {
		return this.id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Column(name = "machine_type_id", nullable = false)
	public int getMachineTypeId() {
		return this.machineTypeId;
	}

	public void setMachineTypeId(int machineTypeId) {
		this.machineTypeId = machineTypeId;
	}

	@Column(name = "search_word", nullable = false, length = 32)
	public String getSearchWord() {
		return this.searchWord;
	}

	public void setSearchWord(String searchWord) {
		this.searchWord = searchWord;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "create_time", nullable = false, length = 0)
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
