package com.tyt.model;

import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 
 * <AUTHOR>
 * @date 2017年8月24日下午3:51:18
 * @description 台数关键字匹配台数实体
 */
@Entity
@Table(name = "tyt_number_keyword", catalog = "tyt")
public class TytNumberKeyword implements java.io.Serializable {
	private static final long serialVersionUID = -2119942192435071331L;

	private Integer id;
	private String keyword;
	private Integer number;

	public TytNumberKeyword() {
	}

	public TytNumberKeyword(String keyword, Integer number) {
		this.keyword = keyword;
		this.number = number;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "keyword")
	public String getKeyword() {
		return this.keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	@Column(name = "number")
	public Integer getNumber() {
		return this.number;
	}

	public void setNumber(Integer number) {
		this.number = number;
	}

}
