package com.tyt.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-8-14上午9:57:07
 * @description
 */
@Entity
@Table(name = "tyt_keyword_matches_new", catalog = "tyt")
public class TytKeywordMatchesNew implements java.io.Serializable {
	private static final long serialVersionUID = 3797639447181305157L;

	private Integer id;
	private String keyword;
	private Integer machineTypeId;
	private String keywordType;
	private Integer priority;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "keyword", length = 64)
	public String getKeyword() {
		return this.keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	@Column(name = "machine_type_id")
	public Integer getMachineTypeId() {
		return this.machineTypeId;
	}

	public void setMachineTypeId(Integer machineTypeId) {
		this.machineTypeId = machineTypeId;
	}

	@Column(name = "keyword_type", length = 8)
	public String getKeywordType() {
		return this.keywordType;
	}

	public void setKeywordType(String keywordType) {
		this.keywordType = keywordType;
	}

	@Column(name = "priority")
	public Integer getPriority() {
		return this.priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
