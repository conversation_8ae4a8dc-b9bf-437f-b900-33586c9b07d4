package com.tyt.model;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_credit_info_popup")
public class UserCreditInfoPopup {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 弹窗次数
     */
    @Column(name = "popup_num")
    private Integer popupNum;

    /**
     * 弹窗时间
     */
    @Column(name = "popup_time")
    private Date popupTime;

    /**
     * 查看信用分规则状态：0.未查看 1.已查看
     */
    @Column(name = "check_status")
    private Integer checkStatus;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;
}