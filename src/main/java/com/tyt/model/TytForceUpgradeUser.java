package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

@Entity
@Table(name = "tyt_force_upgrade_user")
public class TytForceUpgradeUser implements Serializable {

	private static final long serialVersionUID = 7306268130135380993L;
	
	Long  id;
	Long userId;
	String  userPhone;
	String  type;//'1安卓2ios企业3ios个人',
	String  upgradeType;//'1强制升级;2不升级',
	String  version;//'即将升级到的版本号',
	String  url;//'下载地址',
	String content;
	String clientId;//终端唯一标识
	Date create_time;
	Date updateTime;
	
	String trueName; //真实姓名
	Integer userDeliverOne;//一级身份
	Integer userDeliverTwo;//二级身份
	String attribution; //归属地
	Long taskId;
	Integer isFinished;
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name = "user_phone")
	public String getUserPhone() {
		return userPhone;
	}
	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}
	@Column(name = "type")
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	@Column(name = "upgrade_type")
	public String getUpgradeType() {
		return upgradeType;
	}
	public void setUpgradeType(String upgradeType) {
		this.upgradeType = upgradeType;
	}
	@Column(name = "version")
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	@Column(name = "url")
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	@Column(name = "create_time")
	public Date getCreate_time() {
		return create_time;
	}
	public void setCreate_time(Date create_time) {
		this.create_time = create_time;
	}
	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Column(name = "content")
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	@Column(name = "client_id")
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	@Column(name = "true_name")
	public String getTrueName() {
		return trueName;
	}
	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}
	@Column(name = "user_deliver_one")
	public Integer getUserDeliverOne() {
		return userDeliverOne;
	}
	public void setUserDeliverOne(Integer userDeliverOne) {
		this.userDeliverOne = userDeliverOne;
	}
	@Column(name = "user_deliver_two")
	public Integer getUserDeliverTwo() {
		return userDeliverTwo;
	}
	public void setUserDeliverTwo(Integer userDeliverTwo) {
		this.userDeliverTwo = userDeliverTwo;
	}
	@Column(name = "attribution")
	public String getAttribution() {
		return attribution;
	}
	public void setAttribution(String attribution) {
		this.attribution = attribution;
	}
	@Column(name = "task_id")
	public Long getTaskId() {
		return taskId;
	}
	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}
	@Column(name = "is_finished")
	public Integer getIsFinished() {
		return isFinished;
	}
	public void setIsFinished(Integer isFinished) {
		this.isFinished = isFinished;
	}
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SIMPLE_STYLE);
	}

}
