package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "call_phone_record")
public class CallPhoneRecord implements Serializable {
    private static final long serialVersionUID = 289501905774899286L;
    private Long id;
    private Long srcMsgId;
    private Long carUserId;
    private String carUserName;
    private Integer carIsVip;
    private String path;
    private String module;
    private Date createTime;
    private String platId;
    /**
     * 平台交易数
     */
    private String tradeNums="0";
    @Transient
    public String getTradeNums() {
        return tradeNums;
    }

    public void setTradeNums(String tradeNums) {
        this.tradeNums = tradeNums;
    }
    @Transient
    public String getCoopNums() {
        return coopNums;
    }

    public void setCoopNums(String coopNums) {
        this.coopNums = coopNums;
    }

    /**
     * 与我交易数
     */
    private String coopNums="0";
    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "src_msg_id")
    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    @Column(name = "car_user_id")
    public Long getCarUserId() {
        return carUserId;
    }

    public void setCarUserId(Long carUserId) {
        this.carUserId = carUserId;
    }

    @Column(name = "car_user_name")
    public String getCarUserName() {
        return carUserName;
    }

    public void setCarUserName(String carUserName) {
        this.carUserName = carUserName;
    }

    @Column(name = "car_is_vip")
    public Integer getCarIsVip() {
        return carIsVip;
    }

    public void setCarIsVip(Integer carIsVip) {
        this.carIsVip = carIsVip;
    }

    @Column(name = "path")
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @Column(name = "module")
    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Column(name = "plat_id")
    public String getPlatId() {
        return platId;
    }

    public void setPlatId(String platId) {
        this.platId = platId;
    }
}
