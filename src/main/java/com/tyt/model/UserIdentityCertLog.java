package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 用户安证通实名认证日志表
 * <AUTHOR>
 *
 */

@Entity
@Table(name="user_identity_cert_log")
public class UserIdentityCertLog implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2660746275412536938L;
	private Long id;
	/**
	 * 用户ID
	 */
	private Long userId;
	/**
	 * 真实姓名
	 */
	private String userName;
	/**
	 * 身份证号码
	 */
	private String idCard;
	/**
	 * 结果
	 */
	private String result;
	/**
	 * 来源：1-平台、2-安证通、3-之前三方
	 */
	private Integer source;
	/**
	 * 状态：1-通过、2-未通过
	 */
	private Integer status;
	/**
	 * 创建时间
	 */
	private Date ctime;
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="user_name")
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	@Column(name="id_card")
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	@Column(name="result")
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	@Column(name="source")
	public Integer getSource() {
		return source;
	}
	public void setSource(Integer source) {
		this.source = source;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Override
	public String toString() {
		return "UserIdentityCertLog{" +
				"id=" + id +
				", userId=" + userId +
				", userName='" + userName + '\'' +
				", idCard='" + idCard + '\'' +
				", result='" + result + '\'' +
				", source=" + source +
				", status=" + status +
				", ctime=" + ctime +
				'}';
	}
}
