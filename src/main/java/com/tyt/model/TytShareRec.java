package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * @ClassName TytShareRec
 * @Description 分享记录实体类
 * @Date 2019-08-25 15:00
 */
@Entity
@Table(name = "tyt_share_rec", schema = "tyt", catalog = "")
public class TytShareRec {
    private Long id;
    private Long userId;
    private String shareTarget;
    private String shareLink;
    private Date ctime;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "share_target")
    public String getShareTarget() {
        return shareTarget;
    }

    public void setShareTarget(String shareTarget) {
        this.shareTarget = shareTarget;
    }

    @Column(name = "share_link")
    public String getShareLink() {
        return shareLink;
    }

    public void setShareLink(String shareLink) {
        this.shareLink = shareLink;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
}
