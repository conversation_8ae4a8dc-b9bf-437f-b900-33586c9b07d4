package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 运费查询记录，app运价
 * Created by tianjw on 2017/5/23.
 */
@Entity
@Table(name = "tyt_freight_detail_info")
public class TytFreightDetailInfo implements Serializable{

    private static final long serialVersionUID = -8414253892291332326L;
    private Long id;
    private String startPoint;  //起点
    private String destPoint;    //终点
    private String provinceRoad; //途经省份和高速列表
    private String taskContent;//货物信息
    private Float distance;//总里程
    private Integer daysNum;//天数
    private Float cargoLength;//货物长度
    private Float selfTonne;//车辆自重
    private Float tonne;//载重（不包含自重）
    private Float baseCost;//固定成本
    private Float oilCost;//燃油费
    private Float highwayCost;//高速费
    private Float totalCost;//总费用
    private Float profitRate;//利润率
    private Date ctime;
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name="id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name="start_point")
    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }
    @Column(name="dest_point")
    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }
    @Column(name="province_road")
    public String getProvinceRoad() {
        return provinceRoad;
    }

    public void setProvinceRoad(String provinceRoad) {
        this.provinceRoad = provinceRoad;
    }
    @Column(name="task_content")
    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }
    @Column(name="distance")
    public Float getDistance() {
        return distance;
    }

    public void setDistance(Float distance) {
        this.distance = distance;
    }
    @Column(name="days_num")
    public Integer getDaysNum() {
        return daysNum;
    }

    public void setDaysNum(Integer daysNum) {
        this.daysNum = daysNum;
    }
    @Column(name="cargo_length")
    public Float getCargoLength() {
        return cargoLength;
    }

    public void setCargoLength(Float cargoLength) {
        this.cargoLength = cargoLength;
    }
    @Transient
    public Float getSelfTonne() {
        return selfTonne;
    }

    public void setSelfTonne(Float selfTonne) {
        this.selfTonne = selfTonne;
    }

    @Column(name="tonne")
    public Float getTonne() {
        return tonne;
    }

    public void setTonne(Float tonne) {
        this.tonne = tonne;
    }
    @Column(name="base_cost")
    public Float getBaseCost() {
        return baseCost;
    }

    public void setBaseCost(Float baseCost) {
        this.baseCost = baseCost;
    }
    @Column(name="oil_cost")
    public Float getOilCost() {
        return oilCost;
    }

    public void setOilCost(Float oilCost) {
        this.oilCost = oilCost;
    }
    @Column(name="highway_cost")
    public Float getHighwayCost() {
        return highwayCost;
    }

    public void setHighwayCost(Float highwayCost) {
        this.highwayCost = highwayCost;
    }
    @Column(name="total_cost")
    public Float getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Float totalCost) {
        this.totalCost = totalCost;
    }
    @Column(name="profit_rate")
    public Float getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Float profitRate) {
        this.profitRate = profitRate;
    }
    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
