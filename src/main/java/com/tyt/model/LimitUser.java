package com.tyt.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@Table(name = "tyt_user_limit")
public class LimitUser implements Serializable {
    private static final long serialVersionUID = 1L;


    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    private int id;

    @Column(name = "reg_time")
    private Date regTime;

    @Column(name = "user_id")
    private int userID;

    @Column(name = "user_phone")
    private String userPhone;

    @Column(name = "valid_time")
    private Date validTime;

    @Column(name = "operator")
    private String operator;

    @Column(name = "mtime")
    private Date mtime;

    @Column(name = "ctime")
    private Date ctime;


    public LimitUser(Integer userID) {
        this.userID = userID;
    }

    public LimitUser() {

    }
}
