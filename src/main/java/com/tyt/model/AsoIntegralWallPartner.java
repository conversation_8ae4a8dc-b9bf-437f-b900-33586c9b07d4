package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "aso_integral_wall_partner")
public class AsoIntegralWallPartner implements Serializable {

    private static final long serialVersionUID = 3119181818614263649L;
    private Long id;
    private String phoneType;
    private String osVersion;
    private String appid;
    private String idfa;
    private String keyword;
    private String clientIp;
    private String channel;
    private Integer sctiveStatus;
    private Date ctime;
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "phone_type")
    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    @Column(name = "os_version")
    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    @Column(name = "appid")
    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    @Column(name = "idfa")
    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    @Column(name = "keyword")
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Column(name = "client_ip")
    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    @Column(name = "channel")
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    @Column(name = "active_status")
    public Integer getSctiveStatus() {
        return sctiveStatus;
    }

    public void setSctiveStatus(Integer sctiveStatus) {
        this.sctiveStatus = sctiveStatus;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "AsoIntegralWallPartner{" +
                "id=" + id +
                ", phoneType='" + phoneType + '\'' +
                ", osVersion='" + osVersion + '\'' +
                ", appid='" + appid + '\'' +
                ", idfa='" + idfa + '\'' +
                ", keyword='" + keyword + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", channel='" + channel + '\'' +
                ", sctiveStatus=" + sctiveStatus +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
