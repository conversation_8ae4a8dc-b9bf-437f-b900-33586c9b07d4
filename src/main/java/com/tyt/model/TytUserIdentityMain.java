package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytUserIdentityMain entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_user_identity_main", catalog = "tyt")
public class TytUserIdentityMain implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -2687624742484981041L;
	private Long id;
	private Long userId;
	private String identity;
	private String realName;
	private String mainUrl;
	private String backUrl;
	private Date createTime;
	private Date updateTime;
	private String failureReason;
	private String remark;
	private Date infoTime;
	private Integer verifyInfoSign;
	private Integer verifyPhotoSign;
	private Date photoTime;
	private String infoResult;



	@Id
	@GeneratedValue
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id", unique = true, nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "identity", length = 50)
	public String getIdentity() {
		return this.identity;
	}

	public void setIdentity(String identity) {
		this.identity = identity;
	}

	@Column(name = "real_name", length = 50)
	public String getRealName() {
		return this.realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	@Column(name = "main_url", length = 100)
	public String getMainUrl() {
		return this.mainUrl;
	}

	public void setMainUrl(String mainUrl) {
		this.mainUrl = mainUrl;
	}

	@Column(name = "back_url", length = 100)
	public String getBackUrl() {
		return this.backUrl;
	}

	public void setBackUrl(String backUrl) {
		this.backUrl = backUrl;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "update_time")
	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "failure_reason", length = 30)
	public String getFailureReason() {
		return this.failureReason;
	}

	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}

	@Column(name = "remark")
	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(name = "info_time", length = 0)
	public Date getInfoTime() {
		return this.infoTime;
	}

	public void setInfoTime(Date infoTime) {
		this.infoTime = infoTime;
	}

	@Column(name = "verify_info_sign")
	public Integer getVerifyInfoSign() {
		return this.verifyInfoSign;
	}

	public void setVerifyInfoSign(Integer verifyInfoSign) {
		this.verifyInfoSign = verifyInfoSign;
	}

	@Column(name = "verify_photo_sign")
	public Integer getVerifyPhotoSign() {
		return this.verifyPhotoSign;
	}

	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
		this.verifyPhotoSign = verifyPhotoSign;
	}

	@Column(name = "photo_time", length = 0)
	public Date getPhotoTime() {
		return this.photoTime;
	}

	public void setPhotoTime(Date photoTime) {
		this.photoTime = photoTime;
	}

	@Column(name = "info_result", length = 100)
	public String getInfoResult() {
		return this.infoResult;
	}

	public void setInfoResult(String infoResult) {
		this.infoResult = infoResult;
	}

}