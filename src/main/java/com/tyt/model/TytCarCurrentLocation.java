package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2018年1月8日下午5:56:36
 * @description
 */
@Entity
@Table(name = "tyt_car_current_location", catalog = "tyt")
public class TytCarCurrentLocation implements java.io.Serializable {
	private static final long serialVersionUID = -8483949684423424252L;

	private Long id;
	/*
	 * 认证通过车辆的id
	 */
	private int carId;
	/*
	 * 车头牌照头字母
	 */
	private Character headCity;
	/*
	 * 车头牌照号码
	 */
	private String headNo;
	/*
	 * 挂车牌照头字母
	 */
	private Character tailCity;
	/*
	 * 挂车牌照号码
	 */
	private String tailNo;
	/*
	 * 车主认证的姓名,没有则留空
	 */
	private String carOwnerName;
	/*
	 * 车主注册帐号
	 */
	private String carOwnerCellphone;
	/*
	 * 是否使用北斗定位，1 使用 2待定 3 不使用
	 */
	private Byte useBeidou = 2;
	/*
	 * 北斗定位状态，1 正常 2 不正常 3 待定
	 */
	private Byte beidouStatus = 3;
	/*
	 * 是否是跟车型车辆， 1 是 2 不是 3待定
	 */
	private Byte followCarStatus = 3;
	/*
	 * 主司机电话
	 */
	private String firstDriverPhone;
	/*
	 * 副司机电话
	 */
	private String secondCarPhone;
	/*
	 * 随车电话
	 */
	private String followCarPhone;
	/*
	 * 最新定位时间
	 */
	private Date newLocationTime;
	/*
	 * 最新定位地址
	 */
	private String newLocation;
	/*
	 * 更新时间
	 */
	private Date utime;
	/*
	 * 创建时间
	 */
	private Date ctime;
	/*
	 * 车辆当前位置经度
	 */
	private String locationLongitude;
	/*
	 * 车辆当前位置经度
	 */
	private String locationLatitude;
	/*
	 * 车辆当前位置大地坐标X
	 */
	private String dadiX;
	/*
	 * 车辆当前位置大地坐标Y
	 */
	private String dadiY;
	/*
	 * 车辆当前位置所在省
	 */
	private String province;
	/*
	 * 车辆当前位置所在市
	 */
	private String city;
	/*
	 * 车辆所在位置的县
	 */
	private String area;
	/*
	 * 车主id
	 */
	private Integer carOwnerId;
	/*
	 * 是否需要实时获取车辆位置 1 需要 2 不需要
	 */
	private Byte needPosition = 2;
	/*
	 * 定位方式 1 北斗定位 2 待定 3 APP定位
	 */
	private Byte locaitonType = 2;
	/*
	 * 车辆当前行驶速度
	 */
	private String speed;
	/*
	 * 字符串类型（ 0 或 360：正北，大于 0 且小于 90：东北，等于 90：正东，大于 90 且小 于 180：东南，等于 180：正南，大于
	 * 180 且小于 270：西南，等于 270：正西，大 于 270 且小于等于 359：西北，其他：未知）
	 */
	private String dirvePosition;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 车牌照颜色,1：蓝色； 2：黄色
	 */
	private String licenceColor;
	/*
	 * 数据状态 1 有效 2 无效
	 */
	private Byte status = 1;

	public TytCarCurrentLocation() {
	}

	public TytCarCurrentLocation(int carId, Date utime) {
		this.carId = carId;
		this.utime = utime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "car_id", nullable = false)
	public int getCarId() {
		return this.carId;
	}

	public void setCarId(int carId) {
		this.carId = carId;
	}

	@Column(name = "head_city", length = 1)
	public Character getHeadCity() {
		return this.headCity;
	}

	public void setHeadCity(Character headCity) {
		this.headCity = headCity;
	}

	@Column(name = "head_no", length = 10)
	public String getHeadNo() {
		return this.headNo;
	}

	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}

	@Column(name = "tail_city", length = 1)
	public Character getTailCity() {
		return this.tailCity;
	}

	public void setTailCity(Character tailCity) {
		this.tailCity = tailCity;
	}

	@Column(name = "tail_no", length = 10)
	public String getTailNo() {
		return this.tailNo;
	}

	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}

	@Column(name = "car_owner_name", length = 32)
	public String getCarOwnerName() {
		return this.carOwnerName;
	}

	public void setCarOwnerName(String carOwnerName) {
		this.carOwnerName = carOwnerName;
	}

	@Column(name = "car_owner_cellphone", length = 16)
	public String getCarOwnerCellphone() {
		return this.carOwnerCellphone;
	}

	public void setCarOwnerCellphone(String carOwnerCellphone) {
		this.carOwnerCellphone = carOwnerCellphone;
	}

	@Column(name = "use_beidou")
	public Byte getUseBeidou() {
		return this.useBeidou;
	}

	public void setUseBeidou(Byte useBeidou) {
		this.useBeidou = useBeidou;
	}

	@Column(name = "status")
	public Byte getStatus() {
		return this.status;
	}

	public void setStatus(Byte status) {
		this.status = status;
	}

	@Column(name = "beidou_status")
	public Byte getBeidouStatus() {
		return this.beidouStatus;
	}

	public void setBeidouStatus(Byte beidouStatus) {
		this.beidouStatus = beidouStatus;
	}

	@Column(name = "follow_car_status")
	public Byte getFollowCarStatus() {
		return this.followCarStatus;
	}

	public void setFollowCarStatus(Byte followCarStatus) {
		this.followCarStatus = followCarStatus;
	}

	@Column(name = "first_driver_phone", length = 16)
	public String getFirstDriverPhone() {
		return this.firstDriverPhone;
	}

	public void setFirstDriverPhone(String firstDriverPhone) {
		this.firstDriverPhone = firstDriverPhone;
	}

	@Column(name = "second_car_phone", length = 16)
	public String getSecondCarPhone() {
		return this.secondCarPhone;
	}

	public void setSecondCarPhone(String secondCarPhone) {
		this.secondCarPhone = secondCarPhone;
	}

	@Column(name = "follow_car_phone", length = 16)
	public String getFollowCarPhone() {
		return this.followCarPhone;
	}

	public void setFollowCarPhone(String followCarPhone) {
		this.followCarPhone = followCarPhone;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "new_location_time", length = 0)
	public Date getNewLocationTime() {
		return this.newLocationTime;
	}

	public void setNewLocationTime(Date newLocationTime) {
		this.newLocationTime = newLocationTime;
	}

	@Column(name = "new_location", length = 100)
	public String getNewLocation() {
		return this.newLocation;
	}

	public void setNewLocation(String newLocation) {
		this.newLocation = newLocation;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime", nullable = false, length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "location_longitude", length = 16)
	public String getLocationLongitude() {
		return this.locationLongitude;
	}

	public void setLocationLongitude(String locationLongitude) {
		this.locationLongitude = locationLongitude;
	}

	@Column(name = "location_latitude", length = 16)
	public String getLocationLatitude() {
		return this.locationLatitude;
	}

	public void setLocationLatitude(String locationLatitude) {
		this.locationLatitude = locationLatitude;
	}

	@Column(name = "dadi_x", length = 16)
	public String getDadiX() {
		return this.dadiX;
	}

	public void setDadiX(String dadiX) {
		this.dadiX = dadiX;
	}

	@Column(name = "dadi_y", length = 16)
	public String getDadiY() {
		return this.dadiY;
	}

	public void setDadiY(String dadiY) {
		this.dadiY = dadiY;
	}

	@Column(name = "province", length = 16)
	public String getProvince() {
		return this.province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Column(name = "city", length = 16)
	public String getCity() {
		return this.city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name = "area", length = 16)
	public String getArea() {
		return this.area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	@Column(name = "car_owner_id")
	public Integer getCarOwnerId() {
		return this.carOwnerId;
	}

	public void setCarOwnerId(Integer carOwnerId) {
		this.carOwnerId = carOwnerId;
	}

	@Column(name = "need_position")
	public Byte getNeedPosition() {
		return this.needPosition;
	}

	public void setNeedPosition(Byte needPosition) {
		this.needPosition = needPosition;
	}

	@Column(name = "locaiton_type")
	public Byte getLocaitonType() {
		return this.locaitonType;
	}

	public void setLocaitonType(Byte locaitonType) {
		this.locaitonType = locaitonType;
	}

	@Column(name = "speed", length = 16)
	public String getSpeed() {
		return this.speed;
	}

	public void setSpeed(String speed) {
		this.speed = speed;
	}

	@Column(name = "dirve_position", length = 10)
	public String getDirvePosition() {
		return this.dirvePosition;
	}

	public void setDirvePosition(String dirvePosition) {
		this.dirvePosition = dirvePosition;
	}

	@Column(name = "remark", length = 64)
	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(name = "licence_color", length = 4)
	public String getLicenceColor() {
		return this.licenceColor;
	}

	public void setLicenceColor(String licenceColor) {
		this.licenceColor = licenceColor;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
