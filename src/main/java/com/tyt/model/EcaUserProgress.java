package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 用户签署进度表
 * <AUTHOR>
 *
 */

@Entity
@Table(name="eca_user_progress")
public class EcaUserProgress implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 3172454682089720927L;
	private Long id;
	/**
	 * 合同id
	 */
	private Long contractId;
	/**
	 * 货物原信息ID
	 */
	private Long srcMsgId;
	/**
	 * 类型 1达成意向 2 装货完成   3签订合同
	 */
	private Integer type;
	/**
	 * 创建时间
	 */
	private Date ctime;
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="contract_id")
	public Long getContractId() {
		return contractId;
	}
	public void setContractId(Long contractId) {
		this.contractId = contractId;
	}
	@Column(name="src_msg_id")
	public Long getSrcMsgId() {
		return srcMsgId;
	}
	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}
	@Column(name="type")
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	

}
