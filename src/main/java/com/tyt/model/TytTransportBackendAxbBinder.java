package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 虚拟号绑定关系表
 * <AUTHOR>
 * @date 2020/12/23 14:41
 */
@Entity
@Table(name = "tyt_transport_backend_axb_binder")
public class TytTransportBackendAxbBinder {

    private Long id;

    private String bindIndex;

    private String middleNumber;

    private String bindNumberA;

    private String bindNumberB;

    private Integer bindStatus;

    private Integer bindCount;

    private Date createTime;

    private Date updateTime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    @Column(name = "bind_index")
    public String getBindIndex() {
        return bindIndex;
    }

    public void setBindIndex(String bindIndex) {
        this.bindIndex = bindIndex;
    }

    @Column(name = "middle_number")
    public String getMiddleNumber() {
        return middleNumber;
    }

    public void setMiddleNumber(String middleNumber) {
        this.middleNumber = middleNumber;
    }

    @Column(name = "bind_number_a")
    public String getBindNumberA() {
        return bindNumberA;
    }

    public void setBindNumberA(String bindNumberA) {
        this.bindNumberA = bindNumberA;
    }

    @Column(name = "bind_number_b")
    public String getBindNumberB() {
        return bindNumberB;
    }

    public void setBindNumberB(String bindNumberB) {
        this.bindNumberB = bindNumberB;
    }

    @Column(name = "bind_status")
    public Integer getBindStatus() {
        return bindStatus;
    }

    public void setBindStatus(Integer bindStatus) {
        this.bindStatus = bindStatus;
    }

    @Column(name = "bind_count")
    public Integer getBindCount() {
        return bindCount;
    }

    public void setBindCount(Integer bindCount) {
        this.bindCount = bindCount;
    }

    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
