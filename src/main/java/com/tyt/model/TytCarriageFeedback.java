package com.tyt.model;


import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName TytCarriageFeedback
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/9/15 11:33
 * @Verdion 1.0
 **/
@Entity
@Table(name = "tyt_carriage_feedback")
public class TytCarriageFeedback implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 重发后原信息ID
     */
    private Long srcMsgId;

    /**
     * 手机号
     */
    private String cellPhone;

    /**
     * 发布人usreID 关联tyt_user表id
     */
    private Long userId;

    /**
     * 出发地省
     */
    private String startProvince;

    /**
     * 出发地市
     */
    private String startCity;

    /**
     * 出发地区县
     */
    private String startArea;

    /**
     * 目的地省
     */
    private String destProvince;

    /**
     * 目的地市
     */
    private String destCity;

    /**
     * 目的地区县
     */
    private String destArea;

    /**
     * 货物内容
     */
    private String goodsName;

    /**
     * 货物重量
     */
    private BigDecimal goodsWeight;

    /**
     * 货物长
     */
    private BigDecimal goodsLength;

    /**
     * 货物宽
     */
    private BigDecimal goodsWide;

    /**
     * 货物高
     */
    private BigDecimal goodsHigh;

    /**
     * 请求数据组接口类型: 1.支付信息费，2.发货参考价，3.发货校验
     */
    private Integer source;

    /**
     * 反馈标签: 1.价格太低，2.价格太高
     */
    private Integer feedbackType;

    /**
     * 反馈价格
     */
    private BigDecimal price;

    /**
     * 反馈内容
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "src_msg_id")
    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    @Column(name = "cell_phone")
    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "start_province")
    public String getStartProvince() {
        return startProvince;
    }

    public void setStartProvince(String startProvince) {
        this.startProvince = startProvince;
    }

    @Column(name = "start_city")
    public String getStartCity() {
        return startCity;
    }

    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }

    @Column(name = "start_area")
    public String getStartArea() {
        return startArea;
    }

    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }

    @Column(name = "dest_province")
    public String getDestProvince() {
        return destProvince;
    }

    public void setDestProvince(String destProvince) {
        this.destProvince = destProvince;
    }

    @Column(name = "dest_city")
    public String getDestCity() {
        return destCity;
    }

    public void setDestCity(String destCity) {
        this.destCity = destCity;
    }

    @Column(name = "dest_area")
    public String getDestArea() {
        return destArea;
    }

    public void setDestArea(String destArea) {
        this.destArea = destArea;
    }

    @Column(name = "goods_name")
    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    @Column(name = "goods_weight")
    public BigDecimal getGoodsWeight() {
        return goodsWeight;
    }

    public void setGoodsWeight(BigDecimal goodsWeight) {
        this.goodsWeight = goodsWeight;
    }

    @Column(name = "goods_length")
    public BigDecimal getGoodsLength() {
        return goodsLength;
    }

    public void setGoodsLength(BigDecimal goodsLength) {
        this.goodsLength = goodsLength;
    }

    @Column(name = "goods_wide")
    public BigDecimal getGoodsWide() {
        return goodsWide;
    }

    public void setGoodsWide(BigDecimal goodsWide) {
        this.goodsWide = goodsWide;
    }

    @Column(name = "goods_high")
    public BigDecimal getGoodsHigh() {
        return goodsHigh;
    }

    public void setGoodsHigh(BigDecimal goodsHigh) {
        this.goodsHigh = goodsHigh;
    }

    @Column(name = "source")
    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Column(name = "feedback_type")
    public Integer getFeedbackType() {
        return feedbackType;
    }

    public void setFeedbackType(Integer feedbackType) {
        this.feedbackType = feedbackType;
    }

    @Column(name = "price")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
