package com.tyt.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TytChannelOrder entity. <AUTHOR>
 */
@Entity
@Table(name = "tyt_channel_refund_order")
public class TytChannelRefundOrder implements java.io.Serializable {

    private static final long serialVersionUID = 5518998483542728902L;
    private Long id;
    /**
     * tyt订单编号
     */
    private String refundOrderId;
    /**
     * 渠道订单编号
     */
    private String originalOrderId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 原订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单状态，0-新增
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 更新时间
     */
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "refund_order_id")
    public String getRefundOrderId() {
        return refundOrderId;
    }

    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId;
    }
    @Column(name = "original_order_id")
    public String getOriginalOrderId() {
        return originalOrderId;
    }

    public void setOriginalOrderId(String originalOrderId) {
        this.originalOrderId = originalOrderId;
    }
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @Column(name = "amount")
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    @Column(name = "order_amount")
    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
