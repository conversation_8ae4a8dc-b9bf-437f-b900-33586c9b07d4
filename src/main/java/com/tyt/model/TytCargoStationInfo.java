package com.tyt.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName TytCargoStationInfo
 * @Description 货站基本信息对象
 * <AUTHOR>
 * @Date 2019-12-04 10:28
 * @Version 1.0
 */
@Entity
@Table(name = "tyt_cargo_station_info", schema = "tyt", catalog = "")
public class TytCargoStationInfo {
    private Long id;
    private Long stationId;
    private String stationName;
    private String bgUrl;
    private String headUrl;
    private Integer isStar;
    private Double influenceScore;
    private String stationLabels;
    private String individualSign;
    private String stationLocation;
    private Integer isEnterprise;
    private Integer isMember;
    private String transportScore;
    private String transportRank;
    private String transportScoreDegree;
    private String goodsScale;
    private String serviceDegree;
    private String specialOperate;
    private String operateHistory;
    private String industryService;
    private Date sendBroadcastTime;
    private Long createUserId;
    private String createUserName;
    private Date ctime;
    private Long updateUserId;
    private String updateUserName;
    private Date utime;
    private String remark;
    //关注我的人数量
    private Integer followedUserCount;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "station_id")
    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    @Basic
    @Column(name = "station_name")
    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    @Basic
    @Column(name = "bg_url")
    public String getBgUrl() {
        return bgUrl;
    }

    public void setBgUrl(String bgUrl) {
        this.bgUrl = bgUrl;
    }

    @Basic
    @Column(name = "head_url")
    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    @Basic
    @Column(name = "is_star")
    public Integer getIsStar() {
        return isStar;
    }

    public void setIsStar(Integer isStar) {
        this.isStar = isStar;
    }

    @Basic
    @Column(name = "influence_score")
    public Double getInfluenceScore() {
        return influenceScore;
    }

    public void setInfluenceScore(Double influenceScore) {
        this.influenceScore = influenceScore;
    }

    @Basic
    @Column(name = "station_labels")
    public String getStationLabels() {
        return stationLabels;
    }

    public void setStationLabels(String stationLabels) {
        this.stationLabels = stationLabels;
    }

    @Basic
    @Column(name = "individual_sign")
    public String getIndividualSign() {
        return individualSign;
    }

    public void setIndividualSign(String individualSign) {
        this.individualSign = individualSign;
    }

    @Basic
    @Column(name = "station_location")
    public String getStationLocation() {
        return stationLocation;
    }

    public void setStationLocation(String stationLocation) {
        this.stationLocation = stationLocation;
    }

    @Basic
    @Column(name = "is_enterprise")
    public Integer getIsEnterprise() {
        return isEnterprise;
    }

    public void setIsEnterprise(Integer isEnterprise) {
        this.isEnterprise = isEnterprise;
    }

    @Basic
    @Column(name = "is_member")
    public Integer getIsMember() {
        return isMember;
    }

    public void setIsMember(Integer isMember) {
        this.isMember = isMember;
    }

    @Basic
    @Column(name = "transport_score")
    public String getTransportScore() {
        return transportScore;
    }

    public void setTransportScore(String transportScore) {
        this.transportScore = transportScore;
    }

    @Basic
    @Column(name = "transport_rank")
    public String getTransportRank() {
        return transportRank;
    }

    public void setTransportRank(String transportRank) {
        this.transportRank = transportRank;
    }

    @Basic
    @Column(name = "transport_score_degree")
    public String getTransportScoreDegree() {
        return transportScoreDegree;
    }

    public void setTransportScoreDegree(String transportScoreDegree) {
        this.transportScoreDegree = transportScoreDegree;
    }

    @Basic
    @Column(name = "goods_scale")
    public String getGoodsScale() {
        return goodsScale;
    }

    public void setGoodsScale(String goodsScale) {
        this.goodsScale = goodsScale;
    }

    @Basic
    @Column(name = "service_degree")
    public String getServiceDegree() {
        return serviceDegree;
    }

    public void setServiceDegree(String serviceDegree) {
        this.serviceDegree = serviceDegree;
    }

    @Basic
    @Column(name = "special_operate")
    public String getSpecialOperate() {
        return specialOperate;
    }

    public void setSpecialOperate(String specialOperate) {
        this.specialOperate = specialOperate;
    }

    @Basic
    @Column(name = "operate_history")
    public String getOperateHistory() {
        return operateHistory;
    }

    public void setOperateHistory(String operateHistory) {
        this.operateHistory = operateHistory;
    }

    @Basic
    @Column(name = "industry_service")
    public String getIndustryService() {
        return industryService;
    }

    public void setIndustryService(String industryService) {
        this.industryService = industryService;
    }

    @Basic
    @Column(name = "send_broadcast_time")
    public Date getSendBroadcastTime() {
        return sendBroadcastTime;
    }

    public void setSendBroadcastTime(Date sendBroadcastTime) {
        this.sendBroadcastTime = sendBroadcastTime;
    }

    @Basic
    @Column(name = "create_user_id")
    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    @Basic
    @Column(name = "create_user_name")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @Basic
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "update_user_id")
    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    @Basic
    @Column(name = "update_user_name")
    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Basic
    @Column(name = "utime")
    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Basic
    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Transient
    public Integer getFollowedUserCount() {
        return followedUserCount;
    }

    public void setFollowedUserCount(Integer followedUserCount) {
        this.followedUserCount = followedUserCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TytCargoStationInfo that = (TytCargoStationInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(stationId, that.stationId) &&
                Objects.equals(stationName, that.stationName) &&
                Objects.equals(bgUrl, that.bgUrl) &&
                Objects.equals(headUrl, that.headUrl) &&
                Objects.equals(isStar, that.isStar) &&
                Objects.equals(influenceScore, that.influenceScore) &&
                Objects.equals(stationLabels, that.stationLabels) &&
                Objects.equals(individualSign, that.individualSign) &&
                Objects.equals(stationLocation, that.stationLocation) &&
                Objects.equals(isEnterprise, that.isEnterprise) &&
                Objects.equals(isMember, that.isMember) &&
                Objects.equals(transportScore, that.transportScore) &&
                Objects.equals(transportRank, that.transportRank) &&
                Objects.equals(transportScoreDegree, that.transportScoreDegree) &&
                Objects.equals(goodsScale, that.goodsScale) &&
                Objects.equals(serviceDegree, that.serviceDegree) &&
                Objects.equals(specialOperate, that.specialOperate) &&
                Objects.equals(operateHistory, that.operateHistory) &&
                Objects.equals(industryService, that.industryService) &&
                Objects.equals(sendBroadcastTime, that.sendBroadcastTime) &&
                Objects.equals(createUserId, that.createUserId) &&
                Objects.equals(createUserName, that.createUserName) &&
                Objects.equals(ctime, that.ctime) &&
                Objects.equals(updateUserId, that.updateUserId) &&
                Objects.equals(updateUserName, that.updateUserName) &&
                Objects.equals(utime, that.utime) &&
                Objects.equals(remark, that.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, stationId, stationName, bgUrl, headUrl, isStar, influenceScore, stationLabels, individualSign, stationLocation, isEnterprise, isMember, transportScore, transportRank, transportScoreDegree, goodsScale, serviceDegree, specialOperate, operateHistory, industryService, sendBroadcastTime, createUserId, createUserName, ctime, updateUserId, updateUserName, utime, remark);
    }
}
