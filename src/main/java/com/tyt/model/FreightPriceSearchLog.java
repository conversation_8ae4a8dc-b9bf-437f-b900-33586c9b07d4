package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 运费查询记录,人工派单使用
 * Created by tianjw on 2017/5/23.
 */
@Entity
@Table(name = "freight_price_search_log")
public class FreightPriceSearchLog implements Serializable{

    private static final long serialVersionUID = -5400465862302341660L;
    private Long id;
    private String startPoint;  //起点
    private String destPoint;    //终点
    private Double distance;//总里程
    private Integer daysNum;//天数
    private Double cargoLength;//货物长度
    private Double selfTonne;//车辆自重
    private Double tonne;//载重（不包含自重）
    private Double baseCost;//系统计算固定成本
    private Double oilCost;//燃油费

    private Double highwayCost;//高速费
    private Double fixedCost; //成本价格
    private Double minPrice;//最低价格
    private Double minProfitRate; //基本利润率
    private Double guidingPrice;//指导价格
    private Double guidingRate;//指导利润率
    private Date ctime;
    private Date mtime;

    private Double userBaseCost; //用户输入的人工成本
    private Double userOilPrice; //用户输入的柴油价格
    private Double userOilWear; //用户输入的百公里油耗
    private Double oilWear;    //系统计算的百公里油耗
    private Double oilPrice;//系统计算的柴油价格
    private Double perBaseCost; //系统计算的每天人工成本

    @Id
    @GeneratedValue
    @Column(name="id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name="start_point")
    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }
    @Column(name="dest_point")
    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    @Column(name="distance")
    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }
    @Column(name="days_num")
    public Integer getDaysNum() {
        return daysNum;
    }

    public void setDaysNum(Integer daysNum) {
        this.daysNum = daysNum;
    }
    @Column(name="cargo_length")
    public Double getCargoLength() {
        return cargoLength;
    }

    public void setCargoLength(Double cargoLength) {
        this.cargoLength = cargoLength;
    }
    @Column(name="self_tonne")
    public Double getSelfTonne() {
        return selfTonne;
    }

    public void setSelfTonne(Double selfTonne) {
        this.selfTonne = selfTonne;
    }

    @Column(name="tonne")
    public Double getTonne() {
        return tonne;
    }

    public void setTonne(Double tonne) {
        this.tonne = tonne;
    }
    @Column(name="base_cost")
    public Double getBaseCost() {
        return baseCost;
    }

    public void setBaseCost(Double baseCost) {
        this.baseCost = baseCost;
    }
    @Column(name="oil_cost")
    public Double getOilCost() {
        return oilCost;
    }

    public void setOilCost(Double oilCost) {
        this.oilCost = oilCost;
    }
    @Column(name="highway_cost")
    public Double getHighwayCost() {
        return highwayCost;
    }

    public void setHighwayCost(Double highwayCost) {
        this.highwayCost = highwayCost;
    }
    @Column(name="min_price")
    public Double getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(Double minPrice) {
        this.minPrice = minPrice;
    }
    @Column(name="min_profit_rate")
    public Double getMinProfitRate() {
        return minProfitRate;
    }

    public void setMinProfitRate(Double minProfitRate) {
        this.minProfitRate = minProfitRate;
    }

    @Column(name="guiding_price")
    public Double getGuidingPrice() {
        return guidingPrice;
    }

    public void setGuidingPrice(Double guidingPrice) {
        this.guidingPrice = guidingPrice;
    }
    @Column(name="guiding_rate")
    public Double getGuidingRate() {
        return guidingRate;
    }

    public void setGuidingRate(Double guidingRate) {
        this.guidingRate = guidingRate;
    }

    @Column(name="fixed_cost")
    public Double getFixedCost() {
        return fixedCost;
    }

    public void setFixedCost(Double fixedCost) {
        this.fixedCost = fixedCost;
    }

    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
    @Column(name="user_base_cost")
    public Double getUserBaseCost() {
        return userBaseCost;
    }

    public void setUserBaseCost(Double userBaseCost) {
        this.userBaseCost = userBaseCost;
    }
    @Column(name="user_oil_price")
    public Double getUserOilPrice() {
        return userOilPrice;
    }

    public void setUserOilPrice(Double userOilPrice) {
        this.userOilPrice = userOilPrice;
    }
    @Column(name="user_oil_wear")
    public Double getUserOilWear() {
        return userOilWear;
    }

    public void setUserOilWear(Double userOilWear) {
        this.userOilWear = userOilWear;
    }
    @Column(name="oil_wear")
    public Double getOilWear() {
        return oilWear;
    }

    public void setOilWear(Double oilWear) {
        this.oilWear = oilWear;
    }
    @Column(name="oil_price")
    public Double getOilPrice() {
        return oilPrice;
    }

    public void setOilPrice(Double oilPrice) {
        this.oilPrice = oilPrice;
    }
    @Column(name="per_base_cost")
    public Double getPerBaseCost() {
        return perBaseCost;
    }

    public void setPerBaseCost(Double perBaseCost) {
        this.perBaseCost = perBaseCost;
    }
}
