package com.tyt.model;

import javax.persistence.*;
import java.sql.Date;

/**
 * TytMqMessage entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_mq_msg_record")
public class TytMqMsgRecord implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -8374687211899864002L;
	/**
	 * 
	 */
	private Integer id;
	private String messageSerialNum;
	private String messageContent;
	private Integer dealStatus;
	private Date createTime;
	private Date updateTime;
	private Integer messageType;
	private Integer sendNbr;
	private Integer msgType;
	private Long timingSendTime;
	
	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "message_serial_num")
	public String getMessageSerialNum() {
		return this.messageSerialNum;
	}

	public void setMessageSerialNum(String messageSerialNum) {
		this.messageSerialNum = messageSerialNum;
	}

	@Column(name = "message_content")
	public String getMessageContent() {
		return this.messageContent;
	}

	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}

	@Column(name = "deal_status")
	public Integer getDealStatus() {
		return this.dealStatus;
	}

	public void setDealStatus(Integer dealStatus) {
		this.dealStatus = dealStatus;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "update_time")
	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "message_type")
	public Integer getMessageType() {
		return this.messageType;
	}

	public void setMessageType(Integer messageType) {
		this.messageType = messageType;
	}
	@Column(name = "send_nbr")
	public Integer getSendNbr() {
		return sendNbr;
	}

	public void setSendNbr(Integer sendNbr) {
		this.sendNbr = sendNbr;
	}
	@Column(name = "msg_type")
	public Integer getMsgType() {
		return msgType;
	}
	@Column(name = "timing_send_time")
	public Long getTimingSendTime() {
		return timingSendTime;
	}

	public void setMsgType(Integer msgType) {
		this.msgType = msgType;
	}

	public void setTimingSendTime(Long timingSendTime) {
		this.timingSendTime = timingSendTime;
	}

}