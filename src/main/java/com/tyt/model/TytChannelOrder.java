package com.tyt.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TytChannelOrder entity. <AUTHOR>
 */
@Entity
@Table(name = "tyt_channel_order")
public class TytChannelOrder implements java.io.Serializable {

    private static final long serialVersionUID = 5518998483542728902L;
    private Long id;
    /**
     * tyt订单编号
     */
    private String orderId;
    /**
     * 渠道订单编号
     */
    private String channelOrderId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 商品主题
     */
    private String subject;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 同步通知地址
     */
    private String returnUrl;
    /**
     * 异步通知地址
     */
    private String notifyUrl;
    /**
     * 渠道编号
     */
    private String channelNo;
    /**
     * 订单状态，0-新增，1-待支付，2-成功，3-失败，4-关闭
     */
    private Integer status;
    /**
     * 通知状态 0-新增 1-成功 2-失败
     */
    private Integer quartzStatus;
    /**
     * 通知的次数
     */
    private Integer times;

    /**
     * 下次通知时间
     */
    private Date nextNotifyTime;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 更新时间
     */
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "order_id")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    @Column(name = "channel_order_id")
    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @Column(name = "subject")
    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }
    @Column(name = "amount")
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    @Column(name = "return_url")
    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }
    @Column(name = "notify_url")
    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
    @Column(name = "channel_no")
    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    @Column(name = "quartz_status")
    public Integer getQuartzStatus() {
        return quartzStatus;
    }

    public void setQuartzStatus(Integer quartzStatus) {
        this.quartzStatus = quartzStatus;
    }
    @Column(name = "times")
    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    @Column(name = "next_notify_time")
    public Date getNextNotifyTime() {
        return nextNotifyTime;
    }


    public void setNextNotifyTime(Date nextNotifyTime) {
        this.nextNotifyTime = nextNotifyTime;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }


    @Override
    public String toString() {
        return "TytChannelOrder{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", channelOrderId='" + channelOrderId + '\'' +
                ", userId=" + userId +
                ", subject='" + subject + '\'' +
                ", amount=" + amount +
                ", returnUrl='" + returnUrl + '\'' +
                ", notifyUrl='" + notifyUrl + '\'' +
                ", channelNo='" + channelNo + '\'' +
                ", status=" + status +
                ", quartzStatus=" + quartzStatus +
                ", times=" + times +
                ", nextNotifyTime=" + nextNotifyTime +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }

}
