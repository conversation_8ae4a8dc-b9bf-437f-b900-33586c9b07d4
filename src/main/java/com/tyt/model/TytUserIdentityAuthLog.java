package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_user_identity_auth_log")
public class TytUserIdentityAuthLog implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Long uiaId;
	private Long userId;
	private String mobile;
	private Integer identityStatus;
	private Integer userClass;
	private Integer identityType;
	private String trueName;
	private String sex;
	private String idCard;
	private String address;
	private String nation;
	private Integer infoStatus;
	private String infoFailureReason;
	private String mainUrl;
	private Integer mainStatus;
	private String mainFailureReason;
	private String backUrl;
	private Integer backStatus;
	private String backFailueReason;
	private String qualificationsUrl;
	private String enterpriseName;
	private Integer enterpriseType;
	private Integer enterpriseAmount;
	private String enterprisePhone;
	private String licenseUrl;
	private Integer licenseStatus;
	private String licenseFailureReason;
	private String workType;
	private Date ctime;
	private Integer examineStatus;
	private Date examineTime;
	private Long examineUserId;
	private String examineUserName;
	private Date addTime;
	private Date utime;
	private Integer dataType=0;
	
	
	private String iPhotoUrl;
	private Integer iPhotoStatus=0;
	private String iPhotoFailureReason;
	
	
	
	// Constructors

	/** default constructor */
	public TytUserIdentityAuthLog() {
	}


	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "uia_id")
	public Long getUiaId() {
		return this.uiaId;
	}

	public void setUiaId(Long uiaId) {
		this.uiaId = uiaId;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "mobile")
	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	@Column(name = "identity_status")
	public Integer getIdentityStatus() {
		return this.identityStatus;
	}

	public void setIdentityStatus(Integer identityStatus) {
		this.identityStatus = identityStatus;
	}

	@Column(name = "user_class")
	public Integer getUserClass() {
		return this.userClass;
	}

	public void setUserClass(Integer userClass) {
		this.userClass = userClass;
	}

	@Column(name = "identity_type")
	public Integer getIdentityType() {
		return this.identityType;
	}

	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

	@Column(name = "true_name", length = 30)
	public String getTrueName() {
		return this.trueName;
	}

	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}

	@Column(name = "sex", length = 5)
	public String getSex() {
		return this.sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	@Column(name = "id_card", length = 18)
	public String getIdCard() {
		return this.idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	@Column(name = "address", length = 100)
	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	@Column(name = "nation", length = 20)
	public String getNation() {
		return this.nation;
	}

	public void setNation(String nation) {
		this.nation = nation;
	}

	@Column(name = "info_status")
	public Integer getInfoStatus() {
		return this.infoStatus;
	}

	public void setInfoStatus(Integer infoStatus) {
		this.infoStatus = infoStatus;
	}

	@Column(name = "info_failure_reason", length = 40)
	public String getInfoFailureReason() {
		return this.infoFailureReason;
	}

	public void setInfoFailureReason(String infoFailureReason) {
		this.infoFailureReason = infoFailureReason;
	}

	@Column(name = "main_url", length = 100)
	public String getMainUrl() {
		return this.mainUrl;
	}

	public void setMainUrl(String mainUrl) {
		this.mainUrl = mainUrl;
	}

	@Column(name = "main_status")
	public Integer getMainStatus() {
		return this.mainStatus;
	}

	public void setMainStatus(Integer mainStatus) {
		this.mainStatus = mainStatus;
	}

	@Column(name = "main_failure_reason", length = 40)
	public String getMainFailureReason() {
		return this.mainFailureReason;
	}

	public void setMainFailureReason(String mainFailureReason) {
		this.mainFailureReason = mainFailureReason;
	}

	@Column(name = "back_url", length = 100)
	public String getBackUrl() {
		return this.backUrl;
	}

	public void setBackUrl(String backUrl) {
		this.backUrl = backUrl;
	}

	@Column(name = "back_status")
	public Integer getBackStatus() {
		return this.backStatus;
	}

	public void setBackStatus(Integer backStatus) {
		this.backStatus = backStatus;
	}

	@Column(name = "back_failue_reason", length = 40)
	public String getBackFailueReason() {
		return this.backFailueReason;
	}

	public void setBackFailueReason(String backFailueReason) {
		this.backFailueReason = backFailueReason;
	}

	@Column(name = "qualifications_url", length = 100)
	public String getQualificationsUrl() {
		return this.qualificationsUrl;
	}

	public void setQualificationsUrl(String qualificationsUrl) {
		this.qualificationsUrl = qualificationsUrl;
	}

	@Column(name = "enterprise_name", length = 100)
	public String getEnterpriseName() {
		return this.enterpriseName;
	}

	public void setEnterpriseName(String enterpriseName) {
		this.enterpriseName = enterpriseName;
	}

	@Column(name = "enterprise_type")
	public Integer getEnterpriseType() {
		return this.enterpriseType;
	}

	public void setEnterpriseType(Integer enterpriseType) {
		this.enterpriseType = enterpriseType;
	}

	@Column(name = "enterprise_amount")
	public Integer getEnterpriseAmount() {
		return this.enterpriseAmount;
	}

	public void setEnterpriseAmount(Integer enterpriseAmount) {
		this.enterpriseAmount = enterpriseAmount;
	}

	@Column(name = "enterprise_phone", length = 13)
	public String getEnterprisePhone() {
		return this.enterprisePhone;
	}

	public void setEnterprisePhone(String enterprisePhone) {
		this.enterprisePhone = enterprisePhone;
	}

	@Column(name = "license_url", length = 200)
	public String getLicenseUrl() {
		return this.licenseUrl;
	}

	public void setLicenseUrl(String licenseUrl) {
		this.licenseUrl = licenseUrl;
	}

	@Column(name = "license_status")
	public Integer getLicenseStatus() {
		return this.licenseStatus;
	}

	public void setLicenseStatus(Integer licenseStatus) {
		this.licenseStatus = licenseStatus;
	}

	@Column(name = "license_failure_reason", length = 40)
	public String getLicenseFailureReason() {
		return this.licenseFailureReason;
	}

	public void setLicenseFailureReason(String licenseFailureReason) {
		this.licenseFailureReason = licenseFailureReason;
	}

	@Column(name = "work_type")
	public String getWorkType() {
		return this.workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "examine_status")
	public Integer getExamineStatus() {
		return this.examineStatus;
	}

	public void setExamineStatus(Integer examineStatus) {
		this.examineStatus = examineStatus;
	}

	@Column(name = "examine_time", length = 0)
	public Date getExamineTime() {
		return this.examineTime;
	}

	public void setExamineTime(Date examineTime) {
		this.examineTime = examineTime;
	}

	@Column(name = "examine_user_id")
	public Long getExamineUserId() {
		return this.examineUserId;
	}

	public void setExamineUserId(Long examineUserId) {
		this.examineUserId = examineUserId;
	}

	@Column(name = "examine_user_name", length = 40)
	public String getExamineUserName() {
		return this.examineUserName;
	}

	public void setExamineUserName(String examineUserName) {
		this.examineUserName = examineUserName;
	}

	@Column(name = "add_time", length = 0)
	public Date getAddTime() {
		return this.addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Column(name = "utime", length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name = "data_type")
	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	@Column(name = "i_photo_url")
	public String getiPhotoUrl() {
		return iPhotoUrl;
	}

	@Column(name = "i_photo_status")
	public Integer getiPhotoStatus() {
		return iPhotoStatus;
	}

	@Column(name = "i_photo_failure_reason")
	public String getiPhotoFailureReason() {
		return iPhotoFailureReason;
	}


	public void setiPhotoUrl(String iPhotoUrl) {
		this.iPhotoUrl = iPhotoUrl;
	}


	public void setiPhotoStatus(Integer iPhotoStatus) {
		this.iPhotoStatus = iPhotoStatus;
	}


	public void setiPhotoFailureReason(String iPhotoFailureReason) {
		this.iPhotoFailureReason = iPhotoFailureReason;
	}
}