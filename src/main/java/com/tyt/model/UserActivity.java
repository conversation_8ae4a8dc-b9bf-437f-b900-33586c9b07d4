package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/07/23 10:54
 */
@Entity
@Table(name = "tyt_user_activity")
public class UserActivity {

    private Long id;

    private Long userId;

    private Long activityId;

    private Integer activityStatus;

    private Date activityCtime;

    private Date ctime;


    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Column(name = "activity_status")
    public Integer getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(Integer activityStatus) {
        this.activityStatus = activityStatus;
    }

    @Column(name = "activity_ctime")
    public Date getActivityCtime() {
        return activityCtime;
    }

    public void setActivityCtime(Date activityCtime) {
        this.activityCtime = activityCtime;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
}
