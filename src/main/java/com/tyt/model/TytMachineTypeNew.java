package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 
 * <AUTHOR>
 * @date 2017-4-14下午5:41:30
 * @description
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "tyt_machine_type_new", catalog = "tyt")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = { "priority" })
public class TytMachineTypeNew implements java.io.Serializable {

	private Long id;
	@JsonProperty(value = "displayContent")
	private String brandType;
	private String machineType;
	private String brand;
	private String type;
	private String weight;
	private String length;
	private String width;
	private String height;
	private String generalMatchesItem;
	/*
	 * 是否显示长宽高，0 显示 1 不显示
	 */
	private Integer lengthWidthHeightDisplay;
	/*
	 * 是否显示重量，0 显示 1 不显示
	 */
	private Integer weightDisplay;

	public TytMachineTypeNew() {
	}

	public TytMachineTypeNew(Long id, String machineType, String brand, String type, String weight, String length, String width, String height, byte priority) {
		this.id = id;
		this.machineType = machineType;
		this.brand = brand;
		this.type = type;
		this.weight = weight;
		this.length = length;
		this.width = width;
		this.height = height;
	}

	public TytMachineTypeNew(Long id, String brand, String machineType, String type, String weight, String length, String width, String height, Integer lengthWidthHeightDisplay, Integer weightDisplay, String brandType) {
		this.id = id;
		this.machineType = machineType;
		this.brand = brand;
		this.type = type;
		this.weight = weight;
		this.length = length;
		this.width = width;
		this.height = height;
		this.lengthWidthHeightDisplay = lengthWidthHeightDisplay;
		this.weightDisplay = weightDisplay;
		this.brandType = brandType;
	}

	public TytMachineTypeNew(Long id, String brandType, String machineType, String brand, String type, String weight, String length, String width, String height, byte priority, String generalMatchesItem) {
		this.id = id;
		this.brandType = brandType;
		this.machineType = machineType;
		this.brand = brand;
		this.type = type;
		this.weight = weight;
		this.length = length;
		this.width = width;
		this.height = height;
		this.generalMatchesItem = generalMatchesItem;
	}

	@Id
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "length_width_height_display")
	public Integer getLengthWidthHeightDisplay() {
		return lengthWidthHeightDisplay;
	}

	public void setLengthWidthHeightDisplay(Integer lengthWidthHeightDisplay) {
		this.lengthWidthHeightDisplay = lengthWidthHeightDisplay;
	}

	@Column(name = "weight_display")
	public Integer getWeightDisplay() {
		return weightDisplay;
	}

	public void setWeightDisplay(Integer weightDisplay) {
		this.weightDisplay = weightDisplay;
	}

	@Column(name = "brand_type", length = 64)
	public String getBrandType() {
		return this.brandType;
	}

	public void setBrandType(String brandType) {
		this.brandType = brandType;
	}

	@Column(name = "machine_type", nullable = false, length = 16)
	public String getMachineType() {
		return this.machineType;
	}

	public void setMachineType(String machineType) {
		this.machineType = machineType;
	}

	@Column(name = "brand", nullable = false, length = 16)
	public String getBrand() {
		return this.brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	@Column(name = "type", nullable = false, length = 16)
	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "weight", nullable = false, length = 8)
	public String getWeight() {
		return this.weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	@Column(name = "length", nullable = false, length = 8)
	public String getLength() {
		return this.length;
	}

	public void setLength(String length) {
		this.length = length;
	}

	@Column(name = "width", nullable = false, length = 8)
	public String getWidth() {
		return this.width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	@Column(name = "height", nullable = false, length = 8)
	public String getHeight() {
		return this.height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	@Column(name = "general_matches_item", length = 64)
	public String getGeneralMatchesItem() {
		return this.generalMatchesItem;
	}

	public void setGeneralMatchesItem(String generalMatchesItem) {
		this.generalMatchesItem = generalMatchesItem;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
