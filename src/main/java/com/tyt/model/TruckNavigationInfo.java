package com.tyt.model;

import com.tyt.shielding.bean.DTO.TruckNavigationInfoDTO;
import com.tyt.shielding.constant.TruckConstant;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2022-01-25 11:16:52
 */
@Entity
@Table(name = "tyt_truck_navigation_info")
public class TruckNavigationInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键 id
	 */
	private Long id;
	/**
	 * 用户 id
	 */
	private Long userId;
	/**
	 * 车牌号
	 */
	private String carNumber;
	/**
	 * 货车长度
	 */
	private Float truckLong;
	/**
	 * 货车宽
	 */
	private Float truckWidth;
	/**
	 * 货车高
	 */
	private Float truckHigh;
	/**
	 * 货车重
	 */
	private Float truckWeight;
	/**
	 * 货车核定载重
	 */
	private Float truckApprovedLoad;
	/**
	 * 轴数
	 */
	private String axesNumber;
	/**
	 * 排放标准
	 */
	private String emissionStandards;
	/**
	 * 车牌颜色。1.蓝牌；2.黄牌；3.黑牌; 4.白牌; 5.绿牌(新能源、农用车)；6.黄绿
	 */
	private Integer plateColor;

	/**
	 * 货车类型 1 微型车 2 轻型车 3 中型车 4 重型车
	 */
	private Integer truckType;

	/**
	 * 导航类型 （1 高德 2 腾讯）
	 */
	private Integer navigateType;

	/**
	 * 是否删除 0 否，1 是
	 */
	private Integer isDelete;
	/**
	 * 创建人 id
	 */
	private Long createId;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改人 id
	 */
	private Long updateId;
	/**
	 * 修改时间
	 */
	private Date updateTime;

	public TruckNavigationInfo(TruckNavigationInfoDTO truckNavigationInfoDTO) {
		this.userId = truckNavigationInfoDTO.getUserId();
		this.carNumber = truckNavigationInfoDTO.getCarNumber();
		this.truckLong = truckNavigationInfoDTO.getTruckLong();
		this.truckWidth = truckNavigationInfoDTO.getTruckWidth();
		this.truckHigh = truckNavigationInfoDTO.getTruckHigh();
		this.truckWeight = truckNavigationInfoDTO.getTruckWeight();
		this.truckApprovedLoad = truckNavigationInfoDTO.getTruckApprovedLoad();
		this.axesNumber = truckNavigationInfoDTO.getAxesNumber();
		this.emissionStandards = truckNavigationInfoDTO.getEmissionStandards();
		this.isDelete = 0;
		this.createId = truckNavigationInfoDTO.getUserId();
		this.createTime = new Date();
		this.navigateType = truckNavigationInfoDTO.getNavigateType();
		this.plateColor= truckNavigationInfoDTO.getPlateColor();
		this.truckType= truckNavigationInfoDTO.getTruckType();
	}

	public TruckNavigationInfo() {

	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "car_number")
	public String getCarNumber() {
		return carNumber;
	}

	public void setCarNumber(String carNumber) {
		this.carNumber = carNumber;
	}

	@Column(name = "truck_long")
	public Float getTruckLong() {
		return truckLong;
	}

	public void setTruckLong(Float truckLong) {
		this.truckLong = truckLong;
	}

	@Column(name = "truck_width")
	public Float getTruckWidth() {
		return truckWidth;
	}

	public void setTruckWidth(Float truckWidth) {
		this.truckWidth = truckWidth;
	}

	@Column(name = "truck_high")
	public Float getTruckHigh() {
		return truckHigh;
	}

	public void setTruckHigh(Float truckHigh) {
		this.truckHigh = truckHigh;
	}

	@Column(name = "truck_weight")
	public Float getTruckWeight() {
		return truckWeight;
	}

	public void setTruckWeight(Float truckWeight) {
		this.truckWeight = truckWeight;
	}

	@Column(name = "truck_approved_load")
	public Float getTruckApprovedLoad() {
		return truckApprovedLoad;
	}

	public void setTruckApprovedLoad(Float truckApprovedLoad) {
		this.truckApprovedLoad = truckApprovedLoad;
	}

	@Column(name = "axes_number")
	public String getAxesNumber() {
		return axesNumber;
	}

	public void setAxesNumber(String axesNumber) {
		this.axesNumber = axesNumber;
	}

	@Column(name = "emission_standards")
	public String getEmissionStandards() {
		return emissionStandards;
	}

	public void setEmissionStandards(String emissionStandards) {
		this.emissionStandards = emissionStandards;
	}

	@Column(name = "is_delete")
	public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	@Column(name = "create_id")
	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "update_id")
	public Long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}

	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "plate_color")
	public Integer getPlateColor() {
		return plateColor;
	}

	public void setPlateColor(Integer plateColor) {
		this.plateColor = plateColor;
	}

	@Column(name = "truck_type")
	public Integer getTruckType() {
		return truckType;
	}

	public void setTruckType(Integer truckType) {
		this.truckType = truckType;
	}

	@Column(name = "navigate_type")
	public Integer getNavigateType() {
		return navigateType;
	}

	public void setNavigateType(Integer navigateType) {
		this.navigateType = navigateType;
	}
}
