package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytPushTask entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_push_task")
public class TytPushTask implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 3131850489303854673L;
	private Long id;
	private Long jobId;
	private Long ntfId;
	private String businessType;
	private String title;
	private String content;
	private String pushMode;
	private Date pushTime;
	private String openType;
	private String linkUrl;
	private Integer timeLong;
	private String dissectType;
	private String pushPattern;
	private String pushType;
	private Integer clientSign;
	private String offline;
	private String pushNetWorkType;
	private String pushStatus;
	private Date pushStartTime;
	private Date pushEndTime;
	private Date ctime;
	private Date mtime;
	private String threeTaskId;
	private String pushResultStr;
	private String pushResult;
	private Integer notifyBadge;
	private String jobType="0";
	private Integer taskType;
	
	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "job_id")
	public Long getJobId() {
		return this.jobId;
	}

	public void setJobId(Long jobId) {
		this.jobId = jobId;
	}

	@Column(name = "ntf_id")
	public Long getNtfId() {
		return this.ntfId;
	}

	public void setNtfId(Long ntfId) {
		this.ntfId = ntfId;
	}

	@Column(name = "business_type", nullable = false, length = 4)
	public String getBusinessType() {
		return this.businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	@Column(name = "title", length = 50)
	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Column(name = "content", length = 1000)
	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Column(name = "push_mode", nullable = false, length = 4)
	public String getPushMode() {
		return this.pushMode;
	}

	public void setPushMode(String pushMode) {
		this.pushMode = pushMode;
	}

	@Column(name = "push_time")
	public Date getPushTime() {
		return this.pushTime;
	}

	public void setPushTime(Date pushTime) {
		this.pushTime = pushTime;
	}

	@Column(name = "open_type", nullable = false, length = 4)
	public String getOpenType() {
		return this.openType;
	}

	public void setOpenType(String openType) {
		this.openType = openType;
	}

	@Column(name = "link_url", length = 200)
	public String getLinkUrl() {
		return this.linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	@Column(name = "time_long")
	public Integer getTimeLong() {
		return this.timeLong;
	}

	public void setTimeLong(Integer timeLong) {
		this.timeLong = timeLong;
	}

	@Column(name = "dissect_type", length = 4)
	public String getDissectType() {
		return this.dissectType;
	}

	public void setDissectType(String dissectType) {
		this.dissectType = dissectType;
	}

	@Column(name = "push_pattern", length = 4)
	public String getPushPattern() {
		return this.pushPattern;
	}

	public void setPushPattern(String pushPattern) {
		this.pushPattern = pushPattern;
	}

	@Column(name = "push_type", nullable = false, length = 4)
	public String getPushType() {
		return this.pushType;
	}

	public void setPushType(String pushType) {
		this.pushType = pushType;
	}

	@Column(name = "client_sign")
	public Integer getClientSign() {
		return this.clientSign;
	}

	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name = "offline", nullable = false, length = 4)
	public String getOffline() {
		return this.offline;
	}

	public void setOffline(String offline) {
		this.offline = offline;
	}

	@Column(name = "push_net_work_type", nullable = false, length = 4)
	public String getPushNetWorkType() {
		return this.pushNetWorkType;
	}

	public void setPushNetWorkType(String pushNetWorkType) {
		this.pushNetWorkType = pushNetWorkType;
	}

	@Column(name = "push_status", nullable = false, length = 4)
	public String getPushStatus() {
		return this.pushStatus;
	}

	public void setPushStatus(String pushStatus) {
		this.pushStatus = pushStatus;
	}

	@Column(name = "push_start_time")
	public Date getPushStartTime() {
		return this.pushStartTime;
	}

	public void setPushStartTime(Date pushStartTime) {
		this.pushStartTime = pushStartTime;
	}

	@Column(name = "push_end_time")
	public Date getPushEndTime() {
		return this.pushEndTime;
	}

	public void setPushEndTime(Date pushEndTime) {
		this.pushEndTime = pushEndTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name = "three_task_id", length = 50)
	public String getThreeTaskId() {
		return this.threeTaskId;
	}

	public void setThreeTaskId(String threeTaskId) {
		this.threeTaskId = threeTaskId;
	}

	@Column(name = "push_result_str")
	public String getPushResultStr() {
		return this.pushResultStr;
	}

	public void setPushResultStr(String pushResultStr) {
		this.pushResultStr = pushResultStr;
	}

	@Column(name = "push_result", length = 50)
	public String getPushResult() {
		return this.pushResult;
	}

	public void setPushResult(String pushResult) {
		this.pushResult = pushResult;
	}
	@Column(name = "notify_badge")
	public Integer getNotifyBadge() {
		return notifyBadge;
	}

	public void setNotifyBadge(Integer notifyBadge) {
		this.notifyBadge = notifyBadge;
	}

	@Column(name = "job_type")
	public String getJobType() {
		return jobType;
	}

	public void setJobType(String jobType) {
		this.jobType = jobType;
	}
	@Column(name = "task_type")
	public Integer getTaskType() {
		return taskType;
	}

	public void setTaskType(Integer taskType) {
		this.taskType = taskType;
	}
}