package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 履约活动统计表
 */
@Entity
@Table(name = "tyt_convention_orders_census", catalog = "tyt")
public class ConventionOrdersCensus {

    private Long id;
    private Long userId;
    private Integer roundTimes;
    private Integer ordersNum;
    private Integer finalRank;
    private Long qualifyGoodsId;
    private String qualifyGoodsName;
    private Long rankGoodsId;
    private String rankGoodsName;
    private Integer status;
    private Date ctime;
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @Column(name = "round_times")
    public Integer getRoundTimes() {
        return roundTimes;
    }

    public void setRoundTimes(Integer roundTimes) {
        this.roundTimes = roundTimes;
    }
    @Column(name = "orders_num")
    public Integer getOrdersNum() {
        return ordersNum;
    }

    public void setOrdersNum(Integer ordersNum) {
        this.ordersNum = ordersNum;
    }
    @Column(name = "final_rank")
    public Integer getFinalRank() {
        return finalRank;
    }

    public void setFinalRank(Integer finalRank) {
        this.finalRank = finalRank;
    }
    @Column(name = "qualify_goods_id")
    public Long getQualifyGoodsId() {
        return qualifyGoodsId;
    }

    public void setQualifyGoodsId(Long qualifyGoodsId) {
        this.qualifyGoodsId = qualifyGoodsId;
    }
    @Column(name = "qualify_goods_name")
    public String getQualifyGoodsName() {
        return qualifyGoodsName;
    }

    public void setQualifyGoodsName(String qualifyGoodsName) {
        this.qualifyGoodsName = qualifyGoodsName;
    }
    @Column(name = "rank_goods_id")
    public Long getRankGoodsId() {
        return rankGoodsId;
    }

    public void setRankGoodsId(Long rankGoodsId) {
        this.rankGoodsId = rankGoodsId;
    }
    @Column(name = "rank_goods_name")
    public String getRankGoodsName() {
        return rankGoodsName;
    }

    public void setRankGoodsName(String rankGoodsName) {
        this.rankGoodsName = rankGoodsName;
    }
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
