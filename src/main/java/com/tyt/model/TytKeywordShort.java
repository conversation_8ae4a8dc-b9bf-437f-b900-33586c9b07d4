package com.tyt.model;

import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017年10月19日下午7:09:20
 * @description
 */
@Entity
@Table(name = "tyt_keyword_short", catalog = "tyt")
public class TytKeywordShort implements java.io.Serializable {
	private static final long serialVersionUID = 1627837500143225253L;

	private Integer id;
	private String key;
	private String tag;

	public TytKeywordShort() {
	}

	public TytKeywordShort(String key, String tag) {
		this.key = key;
		this.tag = tag;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "key")
	public String getKey() {
		return this.key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	@Column(name = "tag")
	public String getTag() {
		return this.tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
