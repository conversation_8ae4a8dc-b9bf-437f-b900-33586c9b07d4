package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * CarNewsBrand entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "car_news_brand")
public class CarNewsBrand implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 919115187498642263L;
	private Long id;
	private Long carNewsId;
	private Integer carTypeCode;
	private String carType;
	private String brandCode;
	private String brand;
	private Date ctime;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "car_news_id")
	public Long getCarNewsId() {
		return this.carNewsId;
	}

	public void setCarNewsId(Long carNewsId) {
		this.carNewsId = carNewsId;
	}

	@Column(name = "car_type_code")
	public Integer getCarTypeCode() {
		return this.carTypeCode;
	}

	public void setCarTypeCode(Integer carTypeCode) {
		this.carTypeCode = carTypeCode;
	}

	@Column(name = "car_type")
	public String getCarType() {
		return this.carType;
	}

	public void setCarType(String carType) {
		this.carType = carType;
	}

	@Column(name = "brand_code")
	public String getBrandCode() {
		return this.brandCode;
	}

	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}

	@Column(name = "brand")
	public String getBrand() {
		return this.brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

}