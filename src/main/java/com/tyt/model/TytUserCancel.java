package com.tyt.model;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TytUserCancel
 * @description 用户账号注销实体类
 * @date 2021-12-22 10:40
 */
@Entity
@Table(name = "tyt_user_cancel", schema = "tyt", catalog = "")
public class TytUserCancel {
    private Long id;
    private Long userId;
    private String userName;
    private String trueName;
    private String cellPhone;
    private Integer status;
    private Integer reason;
    private String reasonDetail;
    private String remark;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer clientSign;

    @Id
    @GeneratedValue
    @Column(name = "id", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "user_id", nullable = true)
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "user_name", nullable = true, length = 30)
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Basic
    @Column(name = "true_name", nullable = true, length = 30)
    public String getTrueName() {
        return trueName;
    }

    public void setTrueName(String trueName) {
        this.trueName = trueName;
    }

    @Basic
    @Column(name = "cell_phone", nullable = true, length = 30)
    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    @Basic
    @Column(name = "status", nullable = true)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "reason", nullable = true)
    public Integer getReason() {
        return reason;
    }

    public void setReason(Integer reason) {
        this.reason = reason;
    }

    @Basic
    @Column(name = "reason_detail", nullable = true, length = 255)
    public String getReasonDetail() {
        return reasonDetail;
    }

    public void setReasonDetail(String reasonDetail) {
        this.reasonDetail = reasonDetail;
    }

    @Basic
    @Column(name = "remark", nullable = true, length = 30)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Basic
    @Column(name = "ctime", nullable = true)
    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "mtime", nullable = true)
    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Basic
    @Column(name = "client_sign", nullable = true)
    public Integer getClientSign() {
        return clientSign;
    }

    public void setClientSign(Integer clientSign) {
        this.clientSign = clientSign;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TytUserCancel that = (TytUserCancel) o;
        return Objects.equals(id, that.id) && Objects.equals(userId, that.userId) && Objects.equals(userName, that.userName) && Objects.equals(trueName, that.trueName) && Objects.equals(cellPhone, that.cellPhone) && Objects.equals(status, that.status) && Objects.equals(reason, that.reason) && Objects.equals(reasonDetail, that.reasonDetail) && Objects.equals(remark, that.remark) && Objects.equals(ctime, that.ctime) && Objects.equals(mtime, that.mtime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, userId, userName, trueName, cellPhone, status, reason, reasonDetail, remark, ctime, mtime);
    }
}
