package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2018年6月26日下午3:52:22
 * @description
 */
@Entity
@Table(name = "tyt_app_limit_log", catalog = "tyt")
public class TytAppLimitLog implements java.io.Serializable {
	private static final long serialVersionUID = 4533342910908890961L;
	
	private Long id;
	private Long restrictAppId;
	private Long restrictUserid;
	private Byte clientSign;
	private String clientVersion;
	private Date ctime;
	private Date utime;

	public TytAppLimitLog() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "restrict_app_id")
	public Long getRestrictAppId() {
		return this.restrictAppId;
	}

	public void setRestrictAppId(Long restrictAppId) {
		this.restrictAppId = restrictAppId;
	}

	@Column(name = "restrict_userid")
	public Long getRestrictUserid() {
		return this.restrictUserid;
	}

	public void setRestrictUserid(Long restrictUserid) {
		this.restrictUserid = restrictUserid;
	}

	@Column(name = "client_sign")
	public Byte getClientSign() {
		return this.clientSign;
	}

	public void setClientSign(Byte clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name = "client_version", length = 32)
	public String getClientVersion() {
		return this.clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime", length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
