package com.tyt.model;

import java.util.Date;
/**
 * 广告推广位实体
 *
 * <AUTHOR>
 */
public class AdPositionBean{

    private Long id;
    /**
     * 图片显示位置，参见tyt_source表ad_position
     */
    private Integer showPosition;
    /**
     * 图片url
     */
    private String picUrl;
    /**
     * 图片链接地址
     */
    private String picLinkUrl;

    /**
     * 链接类型
     */
    private String linkType;

    private String wordLinkUrl;


    /**
     * 文字内容标题
     */
    private String wordContentTitle;

    /**
     * 文字内容
     */
    private String wordContent;
    /**
     * 优先级，数字越大越靠前
     */
    private Integer sort;
    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 客户端登录限制 0-无需登录 1 需要登录
     */

    private Integer loginRestriction;

    private Integer invoiceTransport;


    private Integer deliverylag;

    private String showRuleContent;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getShowPosition() {
        return showPosition;
    }

    public void setShowPosition(Integer showPosition) {
        this.showPosition = showPosition;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getPicLinkUrl() {
        return picLinkUrl;
    }

    public void setPicLinkUrl(String picLinkUrl) {
        this.picLinkUrl = picLinkUrl;
    }

    public String getLinkType() {
        return linkType;
    }

    public void setLinkType(String linkType) {
        this.linkType = linkType;
    }

    public String getWordLinkUrl() {
        return wordLinkUrl;
    }

    public void setWordLinkUrl(String wordLinkUrl) {
        this.wordLinkUrl = wordLinkUrl;
    }

    public String getWordContentTitle() {
        return wordContentTitle;
    }

    public void setWordContentTitle(String wordContentTitle) {
        this.wordContentTitle = wordContentTitle;
    }

    public String getWordContent() {
        return wordContent;
    }

    public void setWordContent(String wordContent) {
        this.wordContent = wordContent;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public Integer getLoginRestriction() {
        return loginRestriction;
    }

    public void setLoginRestriction(Integer loginRestriction) {
        this.loginRestriction = loginRestriction;
    }

    public String getShowRuleContent() {
        return showRuleContent;
    }

    public void setShowRuleContent(String showRuleContent) {
        this.showRuleContent = showRuleContent;
    }

    public Integer getInvoiceTransport() {
        return invoiceTransport;
    }

    public void setInvoiceTransport(Integer invoiceTransport) {
        this.invoiceTransport = invoiceTransport;
    }

    public Integer getDeliverylag() {
        return deliverylag;
    }

    public void setDeliverylag(Integer deliverylag) {
        this.deliverylag = deliverylag;
    }
}
