package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytBcarJob entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_bcar_job")
public class TytBcarJob implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 5518998483542728902L;
	private Long id;
	private Long sortId;
	private String title;
	private String telName;
	private String telephone;
	private Integer age;
	private Integer years;
	private Integer salaryCode;
	private String salary;
	private Integer carTypeCode;
	private String carType;
	private Integer dutyCode;
	private String duty;
	private Integer provinceCode;
	private String province;
	private Integer cityCode;
	private String city;
	private Integer countyCode;
	private String county;
	private String remark;
	private Date publishTime;
	private Integer status;
	private String cellPhone;
	private Long userId;
	private String md5;
	private Integer readNbr;
	private Date utime;
	private Date ctime;
	private String clientSign;
	private String clientVersion;
	private Integer resendCounts;

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "sort_id")
	public Long getSortId() {
		return this.sortId;
	}

	public void setSortId(Long sortId) {
		this.sortId = sortId;
	}

	@Column(name = "title")
	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Column(name = "tel_name")
	public String getTelName() {
		return this.telName;
	}

	public void setTelName(String telName) {
		this.telName = telName;
	}

	@Column(name = "telephone")
	public String getTelephone() {
		return this.telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	@Column(name = "age")
	public Integer getAge() {
		return this.age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	@Column(name = "years")
	public Integer getYears() {
		return this.years;
	}

	public void setYears(Integer years) {
		this.years = years;
	}

	@Column(name = "salary_code")
	public Integer getSalaryCode() {
		return this.salaryCode;
	}

	public void setSalaryCode(Integer salaryCode) {
		this.salaryCode = salaryCode;
	}

	@Column(name = "salary")
	public String getSalary() {
		return this.salary;
	}

	public void setSalary(String salary) {
		this.salary = salary;
	}

	@Column(name = "car_type_code")
	public Integer getCarTypeCode() {
		return this.carTypeCode;
	}

	public void setCarTypeCode(Integer carTypeCode) {
		this.carTypeCode = carTypeCode;
	}

	@Column(name = "car_type")
	public String getCarType() {
		return this.carType;
	}

	public void setCarType(String carType) {
		this.carType = carType;
	}

	@Column(name = "duty_code")
	public Integer getDutyCode() {
		return this.dutyCode;
	}

	public void setDutyCode(Integer dutyCode) {
		this.dutyCode = dutyCode;
	}

	@Column(name = "duty")
	public String getDuty() {
		return this.duty;
	}

	public void setDuty(String duty) {
		this.duty = duty;
	}

	@Column(name = "province_code")
	public Integer getProvinceCode() {
		return this.provinceCode;
	}

	public void setProvinceCode(Integer provinceCode) {
		this.provinceCode = provinceCode;
	}

	@Column(name = "province")
	public String getProvince() {
		return this.province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Column(name = "city_code")
	public Integer getCityCode() {
		return this.cityCode;
	}

	public void setCityCode(Integer cityCode) {
		this.cityCode = cityCode;
	}

	@Column(name = "city")
	public String getCity() {
		return this.city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name = "county_code")
	public Integer getCountyCode() {
		return this.countyCode;
	}

	public void setCountyCode(Integer countyCode) {
		this.countyCode = countyCode;
	}

	@Column(name = "county")
	public String getCounty() {
		return this.county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	@Column(name = "remark")
	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(name = "publish_time")
	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "cell_phone")
	public String getCellPhone() {
		return this.cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "md5")
	public String getMd5() {
		return this.md5;
	}

	public void setMd5(String md5) {
		this.md5 = md5;
	}

	@Column(name = "read_nbr")
	public Integer getReadNbr() {
		return this.readNbr;
	}

	public void setReadNbr(Integer readNbr) {
		this.readNbr = readNbr;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name = "client_sign")
	public String getClientSign() {
		return clientSign;
	}

	public void setClientSign(String clientSign) {
		this.clientSign = clientSign;
	}
	@Column(name = "client_version")
	public String getClientVersion() {
		return clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}
	@Column(name = "resend_counts")
	public Integer getResendCounts() {
		return resendCounts;
	}

	public void setResendCounts(Integer resendCounts) {
		this.resendCounts = resendCounts;
	}

}