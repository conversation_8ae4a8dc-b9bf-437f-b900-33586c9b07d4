package com.tyt.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-26
 */
@Getter
@Setter
public class RewardTaskVO {

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 任务类型1-活动；2-登录；3-拨打；4-分享；5-履约
     */
    private Integer taskType;

    /**
     * 主标题
     */
    private String mainTitle;

    /**
     * 奖励项
     */
    private String bonus;

    /**
     * 1-进行中；2-已完成
     */
    private Integer taskStatus;

    /**
     * 1-去邀请；2-已结束；3-去完成；4-已获得；5-联系货主；6-去抽奖；7-去分享；8-去找货
     */
    private Integer buttonName;

    /**
     * 是否分享 0-否；1-是
     */
    private Integer isShare;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 链接
     */
    private String activityUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
