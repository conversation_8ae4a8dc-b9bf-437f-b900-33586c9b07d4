package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytMerchantImage entity. <AUTHOR>
@Entity
@Table(name = "tyt_merchant_image")
public class TytMerchantImage implements java.io.Serializable {

	private static final long serialVersionUID = 5104682927950182643L;
	// Fields
	private Long id;
	private Long merchantId;
	private String imageUrl;
	private String isSurface;
	private Integer sort;
	private String compressImageUrl;// 压缩图片地址

	// Constructors

	/** default constructor */
	public TytMerchantImage() {
	}

	/** full constructor */
	public TytMerchantImage(Long merchantId, String imageUrl,
			String compressImageUrl, String isSurface, Integer sort) {
		this.merchantId = merchantId;
		this.imageUrl = imageUrl;
		this.compressImageUrl = compressImageUrl;
		this.isSurface = isSurface;
		this.sort = sort;
	}

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "merchant_id")
	public Long getMerchantId() {
		return this.merchantId;
	}

	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}

	@Column(name = "image_url")
	public String getImageUrl() {
		return this.imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	@Column(name = "is_surface")
	public String getIsSurface() {
		return this.isSurface;
	}

	public void setIsSurface(String isSurface) {
		this.isSurface = isSurface;
	}

	@Column(name = "sort")
	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	@Column(name = "compress_image_url")
	public String getCompressImageUrl() {
		return compressImageUrl;
	}

	public void setCompressImageUrl(String compressImageUrl) {
		this.compressImageUrl = compressImageUrl;
	}

}