package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="tyt_blocker")
public class Blocker implements Serializable {
	 
	 private static final long serialVersionUID = 1L;
	 private Long id;
	 private int isBlack;
	 private int blockType;
     private String blockContent;
     private Timestamp ctime;
   /*  private int  platId = Constant.PLAT_PC; */
     
     public static final int BLACK_LIST = 1;
     public static final int WHITE_LIST = 2;
     public static final int BULK_LIST = 3;//散货
     public static final int AD_LIST = 4;//广告
     
     public static final int BLOCK_TYPE_PUB_QQ = 1;
     public static final int BLOCK_TYPE_OTHER = 2;
     
 	@Id
 	@GeneratedValue
 	@Column(name = "id", unique = true, nullable = false) 
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name="is_black")
	public int getIsBlack() {
		return isBlack;
	}
	public void setIsBlack(int isBlack) {
		this.isBlack = isBlack;
	}
	
	@Column(name="block_type")
	public int getBlockType() {
		return blockType;
	}
	public void setBlockType(int blockType) {
		this.blockType = blockType;
	}
	
	@Column(name="block_content")
	public String getBlockContent() {
		return blockContent;
	}
	public void setBlockContent(String blockContent) {
		this.blockContent = blockContent;
	}
	
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	
/*	@Column(name="plat_id")
	public int getPlatId() {
		return platId;
	}
	public void setPlatId(int platId) {
		this.platId = platId;
	}*/
	@Override
	public String toString() {
		return "Blocker [id=" + id + ", isBlack=" + isBlack + ", blockType="
				+ blockType + ", blockContent=" + blockContent + "]";
	}
     
     
     
     
}
