package com.tyt.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_grant_strategy_config")
public class GrantStrategyConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 生效时间
     */
    @Column(name = "begin_time")
    private Date beginTime;

    /**
     * 截止时间实际
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 发放条件
     */
    @Column(name = "grant_type")
    private String grantType;

    /**
     * 达成几次条件才会发放
     */
    @Column(name = "times_to_grant")
    private Integer timesToGrant;

    /**
     * 权益类型
     */
    @Column(name = "gain_type")
    private String gainType;

    /**
     * 发放数量
     */
    @Column(name = "gain_num")
    private Integer gainNum;

    /**
     * 商品次数
     */
    @Column(name = "goods_use_num")
    private Integer goodsUseNum;

    /**
     * 商品有效期天数
     */
    @Column(name = "goods_effective_time")
    private Integer goodsEffectiveTime;


    /**
     * 权益类型对应id
     */
    @Column(name = "gain_type_id")
    private Long gainTypeId;

    /**
     * 普通发货卡对应的商品Id
     */
    @Column(name = "goods_id")
    private Long goodsId;

    /**
     * 停止条件
     */
    @Column(name = "stop_type")
    private String stopType;

    /**
     * 启用状态 0 失效 1 生效中
     */
    @Column(name = "enable_flag")
    private Integer enableFlag;

    /**
     * 达成几次停止条件才会停止
     */
    @Column(name = "times_to_stop")
    private Integer timesToStop;

    /**
     * 重复频率，单位毫秒，小于等于0为不重复
     */
    @Column(name = "repeat_frequency")
    private Long repeatFrequency;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人用户名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 创建人ID
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人用户名
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 更新人ID
     */
    @Column(name = "update_user_id")
    private Long updateUserId;
}