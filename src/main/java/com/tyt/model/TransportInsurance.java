package com.tyt.model;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonInclude;


/**
 * TransportInsurance entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name="transport_insurance")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportInsurance  implements java.io.Serializable {


    // Fields    

     /**
	 * 
	 */
	private static final long serialVersionUID = 3694754003145140768L;
	 private Long id;
     private Long userId;
     private String cellPhone;
     private String userName;
     private String applicantName;
     private String applicantId;
     private String applicantPhone;
     private String insuredName;
     private String insuredId;
     private String insuredPhone;
     private String company;
     private Integer type;
     private String number;
     private String headNo;
     private String tailNo;
     private String taskContent1;
     private String taskContent2;
     private String taskContent3;
     private String taskContent4;
     private Integer weight;
     private Date startTime;
     private String startPoint;
     private String destPoint;
     private String startCity;
     private String startProvinc;
     private String startArea;
     private String destProvinc;
     private String destCity;
     private String destArea;
     private String transferStation1;
     private String transferStation2;
     private String transferStation3;
     private String transferStation4;
     private Long tsId;
     private Integer distance;
     private Integer amtCurrency;
     private Integer premium;
     private Integer premiumCurrency;
     private Integer category;
     private Date effectiveTime;
     private Integer status;
     private Integer delStatus;
     private Date ctime;
     private Integer payType;
     private Date payTime;
     private Date policyCtime;
     private String pdfUrl;
     private String imgUrl;
     private Date retreatTime;
     private Integer retreatCurrency;
     private Date utime;
     private Integer applicantType;
     private Integer insuredType;
     private String insuranceNo;
     private String orderNo;
     //人保支付页面Url(type=2时存在)
     private String piccPayUrl;
     private String contentNewOld;
     private String packing;

 	@Id
 	@GeneratedValue
 	@Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return this.id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name="user_id")

    public Long getUserId() {
        return this.userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Column(name="cell_phone")

    public String getCellPhone() {
        return this.cellPhone;
    }
    
    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }
    
    @Column(name="user_name")

    public String getUserName() {
        return this.userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    @Column(name="applicant_name")

    public String getApplicantName() {
        return this.applicantName;
    }
    
    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }
    
    @Column(name="applicant_id")

    public String getApplicantId() {
        return this.applicantId;
    }
    
    public void setApplicantId(String applicantId) {
        this.applicantId = applicantId;
    }
    
    @Column(name="applicant_phone")

    public String getApplicantPhone() {
        return this.applicantPhone;
    }
    
    public void setApplicantPhone(String applicantPhone) {
        this.applicantPhone = applicantPhone;
    }
    
    @Column(name="insured_name")

    public String getInsuredName() {
        return this.insuredName;
    }
    
    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }
    
    @Column(name="insured_id")

    public String getInsuredId() {
        return this.insuredId;
    }
    
    public void setInsuredId(String insuredId) {
        this.insuredId = insuredId;
    }
    
    @Column(name="insured_phone")

    public String getInsuredPhone() {
        return this.insuredPhone;
    }
    
    public void setInsuredPhone(String insuredPhone) {
        this.insuredPhone = insuredPhone;
    }
    
    @Column(name="company")

    public String getCompany() {
        return this.company;
    }
    
    public void setCompany(String company) {
        this.company = company;
    }
    
    @Column(name="type")

    public Integer getType() {
        return this.type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    @Column(name="number")

    public String getNumber() {
        return this.number;
    }
    
    public void setNumber(String number) {
        this.number = number;
    }
    
    @Column(name="head_no")

    public String getHeadNo() {
        return this.headNo;
    }
    
    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }
    
    @Column(name="tail_no")

    public String getTailNo() {
        return this.tailNo;
    }
    
    public void setTailNo(String tailNo) {
        this.tailNo = tailNo;
    }
    
    @Column(name="task_content1")

    public String getTaskContent1() {
        return this.taskContent1;
    }
    
    public void setTaskContent1(String taskContent1) {
        this.taskContent1 = taskContent1;
    }
    
    @Column(name="task_content2")

    public String getTaskContent2() {
        return this.taskContent2;
    }
    
    public void setTaskContent2(String taskContent2) {
        this.taskContent2 = taskContent2;
    }
    
    @Column(name="task_content3")

    public String getTaskContent3() {
        return this.taskContent3;
    }
    
    public void setTaskContent3(String taskContent3) {
        this.taskContent3 = taskContent3;
    }
    
    @Column(name="task_content4")

    public String getTaskContent4() {
        return this.taskContent4;
    }
    
    public void setTaskContent4(String taskContent4) {
        this.taskContent4 = taskContent4;
    }
    
    @Column(name="weight")

    public Integer getWeight() {
        return this.weight;
    }
    
    public void setWeight(Integer weight) {
        this.weight = weight;
    }
    
    @Column(name="start_time")

    public Date getStartTime() {
        return this.startTime;
    }
    
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    @Column(name="start_point")

    public String getStartPoint() {
        return this.startPoint;
    }
    
    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }
    
    @Column(name="dest_point")

    public String getDestPoint() {
        return this.destPoint;
    }
    
    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }
    
    @Column(name="start_city")

    public String getStartCity() {
        return this.startCity;
    }
    
    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }
    
    @Column(name="start_provinc")

    public String getStartProvinc() {
        return this.startProvinc;
    }
    
    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }
    
    @Column(name="start_area")

    public String getStartArea() {
        return this.startArea;
    }
    
    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }
    
    @Column(name="dest_provinc")

    public String getDestProvinc() {
        return this.destProvinc;
    }
    
    public void setDestProvinc(String destProvinc) {
        this.destProvinc = destProvinc;
    }
    
    @Column(name="dest_city")

    public String getDestCity() {
        return this.destCity;
    }
    
    public void setDestCity(String destCity) {
        this.destCity = destCity;
    }
    
    @Column(name="dest_area")

    public String getDestArea() {
        return this.destArea;
    }
    
    public void setDestArea(String destArea) {
        this.destArea = destArea;
    }
    
    @Column(name="transfer_station1")

    public String getTransferStation1() {
        return this.transferStation1;
    }
    
    public void setTransferStation1(String transferStation1) {
        this.transferStation1 = transferStation1;
    }
    
    @Column(name="transfer_station2")

    public String getTransferStation2() {
        return this.transferStation2;
    }
    
    public void setTransferStation2(String transferStation2) {
        this.transferStation2 = transferStation2;
    }
    
    @Column(name="transfer_station3")

    public String getTransferStation3() {
        return this.transferStation3;
    }
    
    public void setTransferStation3(String transferStation3) {
        this.transferStation3 = transferStation3;
    }
    
    @Column(name="transfer_station4")

    public String getTransferStation4() {
        return this.transferStation4;
    }
    
    public void setTransferStation4(String transferStation4) {
        this.transferStation4 = transferStation4;
    }
    
    @Column(name="ts_id")

    public Long getTsId() {
        return this.tsId;
    }
    
    public void setTsId(Long tsId) {
        this.tsId = tsId;
    }
    
    @Column(name="distance")

    public Integer getDistance() {
        return this.distance;
    }
    
    public void setDistance(Integer distance) {
        this.distance = distance;
    }
    
    @Column(name="amt_currency")

    public Integer getAmtCurrency() {
        return this.amtCurrency;
    }
    
    public void setAmtCurrency(Integer amtCurrency) {
        this.amtCurrency = amtCurrency;
    }
    
    @Column(name="premium")

    public Integer getPremium() {
        return this.premium;
    }
    
    public void setPremium(Integer premium) {
        this.premium = premium;
    }
    
    @Column(name="premium_currency")

    public Integer getPremiumCurrency() {
        return this.premiumCurrency;
    }
    
    public void setPremiumCurrency(Integer premiumCurrency) {
        this.premiumCurrency = premiumCurrency;
    }
    
    @Column(name="category")
    public Integer getCategory() {
        return this.category;
    }
    
    public void setCategory(Integer category) {
        this.category = category;
    }
    
    @Column(name="effective_time")

    public Date getEffectiveTime() {
        return this.effectiveTime;
    }
    
    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }
    
    @Column(name="status")

    public Integer getStatus() {
        return this.status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    @Column(name="del_status")

    public Integer getDelStatus() {
        return this.delStatus;
    }
    
    public void setDelStatus(Integer delStatus) {
        this.delStatus = delStatus;
    }
    
    @Column(name="ctime")

    public Date getCtime() {
        return this.ctime;
    }
    
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    
    @Column(name="pay_type")

    public Integer getPayType() {
        return this.payType;
    }
    
    public void setPayType(Integer payType) {
        this.payType = payType;
    }
    
    @Column(name="pay_time")

    public Date getPayTime() {
        return this.payTime;
    }
    
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }
    
    @Column(name="policy_ctime")

    public Date getPolicyCtime() {
        return this.policyCtime;
    }
    
    public void setPolicyCtime(Date policyCtime) {
        this.policyCtime = policyCtime;
    }
    
    @Column(name="pdf_url")

    public String getPdfUrl() {
        return this.pdfUrl;
    }
    
    public void setPdfUrl(String pdfUrl) {
        this.pdfUrl = pdfUrl;
    }
    
    @Column(name="img_url")

    public String getImgUrl() {
        return this.imgUrl;
    }
    
    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
    
    @Column(name="retreat_time")

    public Date getRetreatTime() {
        return this.retreatTime;
    }
    
    public void setRetreatTime(Date retreatTime) {
        this.retreatTime = retreatTime;
    }
    
    @Column(name="retreat_currency")

    public Integer getRetreatCurrency() {
        return this.retreatCurrency;
    }
    
    public void setRetreatCurrency(Integer retreatCurrency) {
        this.retreatCurrency = retreatCurrency;
    }
    
    @Column(name="utime")

    public Date getUtime() {
        return this.utime;
    }
    
    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Column(name="applicant_type")
	public Integer getApplicantType() {
		return applicantType;
	}

    @Column(name="insured_type")
	public Integer getInsuredType() {
		return insuredType;
	}

    @Column(name="insurance_no")
	public String getInsuranceNo() {
		return insuranceNo;
	}

	public void setApplicantType(Integer applicantType) {
		this.applicantType = applicantType;
	}

	public void setInsuredType(Integer insuredType) {
		this.insuredType = insuredType;
	}

	public void setInsuranceNo(String insuranceNo) {
		this.insuranceNo = insuranceNo;
	}
	@Column(name="order_no")
	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

    @Column(name="picc_pay_url")
    public String getPiccPayUrl() {
        return piccPayUrl;
    }

    public void setPiccPayUrl(String piccPayUrl) {
        this.piccPayUrl = piccPayUrl;
    }

    @Column(name="content_new_old")
    public String getContentNewOld() {
        return contentNewOld;
    }

    public void setContentNewOld(String contentNewOld) {
        this.contentNewOld = contentNewOld;
    }

    @Column(name="packing")
    public String getPacking() {
        return packing;
    }

    public void setPacking(String packing) {
        this.packing = packing;
    }
}