package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytMerchant entity. <AUTHOR>
@Entity
@Table(name = "tyt_merchant")
public class TytMerchant implements java.io.Serializable {

	private static final long serialVersionUID = -7118542687263824479L;
	
	// Fields
	private Long id;
	private Long belongUserId;
	private String belongCellPhone;
	private Long userId;
	private String cellPhone;
	private String merchantName;
	private String remark;
	private String province;
	private String city;
	private String county;
	private Integer YCoord;
	private Integer XCoord;
	private Integer longitude;
	private Integer latitude;
	private String fixedPosition;
	private String inputPosition;
	private String telePhones;
	private String status;
	private Date createTime;
	private Date updateTime;
	private String clientSign;
	private String clientVersion;
	private Long readCount;
	private Long callCount;
	private String merchantType;
	private String lables;
	private String merchantTypeName;
	private String lableNames;
	/*虚拟阅读次数*/
	private Integer vritualReadTimes=0;
	/*收藏人数*/
	private Integer collectPeopleNumber=0;
	/*阅读人数*/
	private Integer	readPeopleNumber=0;
	
	


	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "belong_user_id")
	public Long getBelongUserId() {
		return this.belongUserId;
	}

	public void setBelongUserId(Long belongUserId) {
		this.belongUserId = belongUserId;
	}

	@Column(name = "belong_cell_phone")
	public String getBelongCellPhone() {
		return this.belongCellPhone;
	}

	public void setBelongCellPhone(String belongCellPhone) {
		this.belongCellPhone = belongCellPhone;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "cell_phone")
	public String getCellPhone() {
		return this.cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "merchant_name")
	public String getMerchantName() {
		return this.merchantName;
	}

	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}

	@Column(name = "remark")
	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(name = "province")
	public String getProvince() {
		return this.province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Column(name = "city")
	public String getCity() {
		return this.city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name = "county")
	public String getCounty() {
		return this.county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	@Column(name = "y_coord")
	public Integer getYCoord() {
		return this.YCoord;
	}

	public void setYCoord(Integer YCoord) {
		this.YCoord = YCoord;
	}

	@Column(name = "x_coord")
	public Integer getXCoord() {
		return this.XCoord;
	}

	public void setXCoord(Integer XCoord) {
		this.XCoord = XCoord;
	}

	@Column(name = "longitude")
	public Integer getLongitude() {
		return this.longitude;
	}

	public void setLongitude(Integer longitude) {
		this.longitude = longitude;
	}

	@Column(name = "latitude")
	public Integer getLatitude() {
		return this.latitude;
	}

	public void setLatitude(Integer latitude) {
		this.latitude = latitude;
	}

	@Column(name = "fixed_position")
	public String getFixedPosition() {
		return this.fixedPosition;
	}

	public void setFixedPosition(String fixedPosition) {
		this.fixedPosition = fixedPosition;
	}

	@Column(name = "input_position")
	public String getInputPosition() {
		return this.inputPosition;
	}

	public void setInputPosition(String inputPosition) {
		this.inputPosition = inputPosition;
	}

	@Column(name = "tele_phones")
	public String getTelePhones() {
		return this.telePhones;
	}

	public void setTelePhones(String telePhones) {
		this.telePhones = telePhones;
	}

	@Column(name = "status")
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "update_time")
	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "client_sign")
	public String getClientSign() {
		return this.clientSign;
	}

	public void setClientSign(String clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name = "client_version")
	public String getClientVersion() {
		return this.clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	@Column(name = "read_count")
	public Long getReadCount() {
		return this.readCount;
	}

	public void setReadCount(Long readCount) {
		this.readCount = readCount;
	}

	@Column(name = "call_count")
	public Long getCallCount() {
		return this.callCount;
	}

	public void setCallCount(Long callCount) {
		this.callCount = callCount;
	}

	@Column(name = "merchant_type")
	public String getMerchantType() {
		return this.merchantType;
	}

	public void setMerchantType(String merchantType) {
		this.merchantType = merchantType;
	}

	@Column(name = "lables")
	public String getLables() {
		return this.lables;
	}

	public void setLables(String lables) {
		this.lables = lables;
	}
	@Column(name = "merchant_type_name")
	public String getMerchantTypeName() {
		return merchantTypeName;
	}

	public void setMerchantTypeName(String merchantTypeName) {
		this.merchantTypeName = merchantTypeName;
	}
	@Column(name = "lable_names")
	public String getLableNames() {
		return lableNames;
	}

	public void setLableNames(String lableNames) {
		this.lableNames = lableNames;
	}
	@Column(name = "vritual_read_times")
	public Integer getVritualReadTimes() {
		return vritualReadTimes;
	}

	public void setVritualReadTimes(Integer vritualReadTimes) {
		this.vritualReadTimes = vritualReadTimes;
	}
	@Column(name = "collect_people_number")
	public Integer getCollectPeopleNumber() {
		return collectPeopleNumber;
	}

	public void setCollectPeopleNumber(Integer collectPeopleNumber) {
		this.collectPeopleNumber = collectPeopleNumber;
	}
	@Column(name = "read_people_number")
	public Integer getReadPeopleNumber() {
		return readPeopleNumber;
	}

	public void setReadPeopleNumber(Integer readPeopleNumber) {
		this.readPeopleNumber = readPeopleNumber;
	}
}