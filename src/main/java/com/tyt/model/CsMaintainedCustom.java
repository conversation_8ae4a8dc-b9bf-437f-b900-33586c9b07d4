package com.tyt.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cs_maintained_custom")
public class CsMaintainedCustom {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 所属方  1.车维 2，货维 3.续费
     */
    @Column(name = "belong_to")
    private Short belongTo;

    /**
     * 客户id
     */
    @Column(name = "custom_id")
    private Long customId;

    /**
     * 客户电话
     */
    @Column(name = "custom_phone")
    private String customPhone;

    /**
     * 车维护人id
     */
    @Column(name = "maintainer_id")
    private Long maintainerId;

    /**
     * 车维护人姓名
     */
    @Column(name = "maintainer_name")
    private String maintainerName;

    /**
     * 货维护人id
     */
    @Column(name = "goods_maintainer_id")
    private Long goodsMaintainerId;

    /**
     * 货维护人姓名
     */
    @Column(name = "goods_maintainer_name")
    private String goodsMaintainerName;

    /**
     * 上次沟通时间
     */
    @Column(name = "last_comm_time")
    private Date lastCommTime;

    /**
     * 未付费一级原因
     */
    @Column(name = "no_pay_reason_one")
    private Long noPayReasonOne;

    /**
     * 未付费二级原因
     */
    @Column(name = "not_pay_reason_two")
    private Long notPayReasonTwo;

    /**
     * 未付费三级原因
     */
    @Column(name = "no_pay_reason_three")
    private Long noPayReasonThree;

    /**
     * 更新时间
     */
    private Date utime;

    /**
     * 状态 1：有效 2：无效 3：删除
     */
    private Short status;

    /**
     * 修改人id
     */
    @Column(name = "modify_id")
    private Long modifyId;

    /**
     * 修改人姓名
     */
    @Column(name = "modify_name")
    private String modifyName;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 回访时间
     */
    @Column(name = "rvisit_time")
    private Date rvisitTime;

    /**
     * 用户标签
     */
    @Column(name = "user_label")
    private String userLabel;

    /**
     * 是否需要分配 1.是 2.不是
     */
    @Column(name = "is_need_defender")
    private Short isNeedDefender;

    /**
     * 是否需要流转 1.内部流转 2.不流转 3.跨团队流转
     */
    @Column(name = "is_need_move")
    private Short isNeedMove;

    /**
     * 是否需要维护 1.是 2.不是
     */
    @Column(name = "is_need_show")
    private Short isNeedShow;

    /**
     * 是否需要强制显示 1.是 2.不是
     */
    @Column(name = "is_need_force_show")
    private Short isNeedForceShow;

    /**
     * 显示时间
     */
    @Column(name = "show_time")
    private Date showTime;

    /**
     * 意向级别 1.放弃 2.有意向 3.潜在培养 4.再跟进
     */
    @Column(name = "intention_rank")
    private Integer intentionRank;

    /**
     * 原维护人id
     */
    @Column(name = "previous_maintainer_id")
    private Long previousMaintainerId;

    /**
     * 原维护人
     */
    @Column(name = "previous_maintainer_name")
    private String previousMaintainerName;

    /**
     * 区域类型 1.北重点省市 2.南重点省市 3.非重点省市
     */
    @Column(name = "area_type")
    private Short areaType;

    /**
     * 授权状态 1.未授权 2.授权中 3.已授权
     */
    @Column(name = "empower_status")
    private Short empowerStatus;

    /**
     * 货主意向 1.已加微信 2.暂时婉拒 3.拒绝合作
     */
    @Column(name = "goods_intention")
    private Short goodsIntention;

    /**
     * 调度人id
     */
    @Column(name = "dispatcher_id")
    private Long dispatcherId;

    /**
     * 调度人
     */
    @Column(name = "dispatcher_name")
    private String dispatcherName;

    /**
     * 调度绑定时间
     */
    @Column(name = "binding_dispatch_time")
    private Date bindingDispatchTime;

    /**
     * 是否是个人货主（0不是，1是）
     */
    @Column(name = "goods_owner")
    private Integer goodsOwner;

    /**
     * 是否同步货源（0未同步，1已同步）
     */
    @Column(name = "goods_sync")
    private Integer goodsSync;


    /**
     * 备注
     */
    private String remark;

    /**
     * 客户来源
     */
    private String source;
}