package com.tyt.model;


import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytTopQueue entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_top_queue")
public class TytTopQueue implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5154958329830391197L;
	// Fields

	private Long id;
	private Long sourceMsgId;
	private Long newMsgId;
	private Integer type;
	private Integer status;
	private Date ctime;
	private Date utime;

	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "source_msg_id")
	public Long getSourceMsgId() {
		return this.sourceMsgId;
	}

	public void setSourceMsgId(Long sourceMsgId) {
		this.sourceMsgId = sourceMsgId;
	}

	@Column(name = "new_msg_id")
	public Long getNewMsgId() {
		return this.newMsgId;
	}

	public void setNewMsgId(Long newMsgId) {
		this.newMsgId = newMsgId;
	}

	@Column(name = "type")
	public Integer getType() {
		return this.type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

}