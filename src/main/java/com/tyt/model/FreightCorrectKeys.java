package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date 2017年8月29日上午11:53:36
 * @description
 */
@Entity
@Table(name = "freight_correct_keys", catalog = "tyt")
public class FreightCorrectKeys implements java.io.Serializable {

	private static final long serialVersionUID = 6419721067899034236L;
	private Long id;
	private String keyName;
	private String matchName;
	private Date ctime;
	private Date mtime;

	public FreightCorrectKeys() {
	}

	@Id
	@GeneratedValue
	@Column(name = "id")
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "key_name")
	public String getKeyName() {
		return keyName;
	}

	public void setKeyName(String keyName) {
		this.keyName = keyName;
	}

	@Column(name = "match_name")
	public String getMatchName() {
		return matchName;
	}

	public void setMatchName(String matchName) {
		this.matchName = matchName;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name = "mtime")
	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
}
