package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * TytUserMac entity.
 * <AUTHOR>
 */
@Entity
@Table(name = "tyt_user_mac", catalog = "tyt")
public class TytUserMac implements Serializable {

	private static final long serialVersionUID = 446389864448422028L;
	/**
	 *
	 */
	private Long id;
	/**
	 * 终端唯一标识
	 */
	private String mac;
	/**
	 * 用户ID
	 */
	private Long userId;
	/**
	 * 用户姓名
	 */
	private String userName;
	/**
	 * 手机号(账号)
	 */
	private String cellPhone;
	/**
	 * 身份证号
	 */
	private String idcard;
	/**
	 * 终端类型
	 */
	private String clientType;
	/**
	 * 客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
	 */
	private Integer clientSign;
	/**
	 * 登录次数
	 */
	private Integer times;
	/**
	 * 绑定状态 0-未绑定，1-已绑定
	 */
	private Integer bindStatus;
	/**
	 * 创建时间
	 */
	private Date ctime;
	/**
	 * 更新时间
	 */
	private Date mtime;


	
	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "mac")
	public String getMac() {
		return mac;
	}

	public void setMac(String mac) {
		this.mac = mac;
	}
	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name = "user_name")
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
	@Column(name = "cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name = "idcard")
	public String getIdcard() {
		return idcard;
	}

	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}
	@Column(name = "client_type")
	public String getClientType() {
		return clientType;
	}

	public void setClientType(String clientType) {
		this.clientType = clientType;
	}
	@Column(name = "client_sign")
	public Integer getClientSign() {
		return clientSign;
	}

	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}
	@Column(name = "times")
	public Integer getTimes() {
		return times;
	}

	public void setTimes(Integer times) {
		this.times = times;
	}
	@Column(name = "bind_status")
	public Integer getBindStatus() {
		return bindStatus;
	}

	public void setBindStatus(Integer bindStatus) {
		this.bindStatus = bindStatus;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name = "mtime")
	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Override
	public String toString() {
		return "TytUserMac{" +
				"id=" + id +
				", mac='" + mac + '\'' +
				", userId=" + userId +
				", userName='" + userName + '\'' +
				", cellPhone='" + cellPhone + '\'' +
				", idcard='" + idcard + '\'' +
				", clientType='" + clientType + '\'' +
				", clientSign=" + clientSign +
				", times=" + times +
				", bindStatus=" + bindStatus +
				", ctime=" + ctime +
				", mtime=" + mtime +
				'}';
	}
}
