package com.tyt.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName LuckdrawRealWinner
 * @Description 抽奖活动真实中奖用户对象
 * <AUTHOR>
 * @Date 2020-03-09 11:51
 * @Version 1.0
 */
@Entity
@Table(name = "luckdraw_real_winner", schema = "tyt", catalog = "")
public class LuckdrawRealWinner {
    private Long id;
    private Long presetId;
    private Integer activityId;
    private String activityName;
    private Integer prizeId;
    private Integer prizeType;
    private String prizeName;
    private BigDecimal prizeAmount;
    private Long userId;
    private String userName;
    private String cellphone;
    private Date ctime;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "preset_id")
    public Long getPresetId() {
        return presetId;
    }

    public void setPresetId(Long presetId) {
        this.presetId = presetId;
    }

    @Basic
    @Column(name = "activity_id")
    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    @Basic
    @Column(name = "activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Basic
    @Column(name = "prize_id")
    public Integer getPrizeId() {
        return prizeId;
    }

    public void setPrizeId(Integer prizeId) {
        this.prizeId = prizeId;
    }

    @Basic
    @Column(name = "prize_type")
    public Integer getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(Integer prizeType) {
        this.prizeType = prizeType;
    }

    @Basic
    @Column(name = "prize_name")
    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    @Basic
    @Column(name = "prize_amount")
    public BigDecimal getPrizeAmount() {
        return prizeAmount;
    }

    public void setPrizeAmount(BigDecimal prizeAmount) {
        this.prizeAmount = prizeAmount;
    }

    @Basic
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "user_name")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Basic
    @Column(name = "cellphone")
    public String getCellphone() {
        return cellphone;
    }

    public void setCellphone(String cellphone) {
        this.cellphone = cellphone;
    }

    @Basic
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LuckdrawRealWinner that = (LuckdrawRealWinner) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(presetId, that.presetId) &&
                Objects.equals(activityId, that.activityId) &&
                Objects.equals(activityName, that.activityName) &&
                Objects.equals(prizeId, that.prizeId) &&
                Objects.equals(prizeType, that.prizeType) &&
                Objects.equals(prizeName, that.prizeName) &&
                Objects.equals(prizeAmount, that.prizeAmount) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(userName, that.userName) &&
                Objects.equals(cellphone, that.cellphone) &&
                Objects.equals(ctime, that.ctime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, presetId, activityId, activityName, prizeId, prizeType, prizeName, prizeAmount, userId, userName, cellphone, ctime);
    }
}
