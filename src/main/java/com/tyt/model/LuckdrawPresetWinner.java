package com.tyt.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName LuckdrawPresetWinner
 * @Description 抽奖活动预设中奖用户对象
 * <AUTHOR>
 * @Date 2020-03-09 11:51
 * @Version 1.0
 */
@Entity
@Table(name = "luckdraw_preset_winner", schema = "tyt", catalog = "")
public class LuckdrawPresetWinner {
    private Long id;
    private Integer activityId;
    private String activityName;
    private Integer prizeId;
    private Integer prizeType;
    private String prizeName;
    private BigDecimal prizeAmount;
    private Long userId;
    private String userName;
    private String cellphone;
    private Integer isValid;
    private Integer drawStatus; //抽奖状态(1.未抽奖 2.已抽奖)
    private Long createUserId;
    private String createUserName;
    private Date ctime;
    private Long updateUserId;
    private String updateUserName;
    private Date utime;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "activity_id")
    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    @Basic
    @Column(name = "activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Basic
    @Column(name = "prize_id")
    public Integer getPrizeId() {
        return prizeId;
    }

    public void setPrizeId(Integer prizeId) {
        this.prizeId = prizeId;
    }

    @Basic
    @Column(name = "prize_type")
    public Integer getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(Integer prizeType) {
        this.prizeType = prizeType;
    }

    @Basic
    @Column(name = "prize_name")
    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    @Basic
    @Column(name = "prize_amount")
    public BigDecimal getPrizeAmount() {
        return prizeAmount;
    }

    public void setPrizeAmount(BigDecimal prizeAmount) {
        this.prizeAmount = prizeAmount;
    }

    @Basic
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "user_name")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Basic
    @Column(name = "cellphone")
    public String getCellphone() {
        return cellphone;
    }

    public void setCellphone(String cellphone) {
        this.cellphone = cellphone;
    }

    @Basic
    @Column(name = "is_valid")
    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    @Basic
    @Column(name = "draw_status")
    public Integer getDrawStatus() {
        return drawStatus;
    }

    public void setDrawStatus(Integer drawStatus) {
        this.drawStatus = drawStatus;
    }

    @Basic
    @Column(name = "create_user_id")
    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    @Basic
    @Column(name = "create_user_name")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @Basic
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "update_user_id")
    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    @Basic
    @Column(name = "update_user_name")
    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Basic
    @Column(name = "utime")
    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LuckdrawPresetWinner that = (LuckdrawPresetWinner) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(activityId, that.activityId) &&
                Objects.equals(activityName, that.activityName) &&
                Objects.equals(prizeId, that.prizeId) &&
                Objects.equals(prizeType, that.prizeType) &&
                Objects.equals(prizeName, that.prizeName) &&
                Objects.equals(prizeAmount, that.prizeAmount) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(userName, that.userName) &&
                Objects.equals(cellphone, that.cellphone) &&
                Objects.equals(isValid, that.isValid) &&
                Objects.equals(drawStatus, that.drawStatus) &&
                Objects.equals(createUserId, that.createUserId) &&
                Objects.equals(createUserName, that.createUserName) &&
                Objects.equals(ctime, that.ctime) &&
                Objects.equals(updateUserId, that.updateUserId) &&
                Objects.equals(updateUserName, that.updateUserName) &&
                Objects.equals(utime, that.utime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, activityId, activityName, prizeId, prizeType, prizeName, prizeAmount, userId, userName, cellphone, isValid, drawStatus, createUserId, createUserName, ctime, updateUserId, updateUserName, utime);
    }
}
