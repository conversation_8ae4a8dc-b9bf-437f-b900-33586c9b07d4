package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytBubble entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_bubble")
public class TytBubble implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8899761713722003396L;

	

	private Long id;
	private Long userId;
	private String type1;
	private String type2;
	private Integer number;
	private String isFunction;
	private String status;
	private Date ctime;
	private Date utime;

	// Property accessors
		@Id
		@GeneratedValue
		@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "type1")
	public String getType1() {
		return this.type1;
	}

	public void setType1(String type1) {
		this.type1 = type1;
	}

	@Column(name = "type2")
	public String getType2() {
		return this.type2;
	}

	public void setType2(String type2) {
		this.type2 = type2;
	}

	@Column(name = "number")
	public Integer getNumber() {
		return this.number;
	}

	public void setNumber(Integer number) {
		this.number = number;
	}

	@Column(name = "is_function")
	public String getIsFunction() {
		return this.isFunction;
	}

	public void setIsFunction(String isFunction) {
		this.isFunction = isFunction;
	}

	@Column(name = "status")
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

}