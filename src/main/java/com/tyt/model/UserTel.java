package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
@Entity
@Table(name="tyt_user_tel")
public class UserTel implements Serializable{


	/**
	 * 
	 */
	private static final long serialVersionUID = 3204603582680615048L;
	Long id;//id
	Long userId;//用户id
	String tell;//电话
	Date createTime;//创建时间


	/**
	 * 是否验证1-已验证；2未验证
	 */
	private Integer isVerified;



	/**
	 * 修改时间
	 */
	private Date updateTime;
	String status;//状态 0无效 1有效
	String type;//类型1手机 2固话 
	public UserTel() {
		super();
	}

	public UserTel(Long userId, String tell,String status,String type) {
		super();
		this.userId = userId;
		this.tell = tell;
		this.status = status;
		this.createTime = new Date();
		this.type=type;
	}

	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="tell")
	public String getTell() {
		return tell;
	}
	public void setTell(String tell) {
		this.tell = tell;
	}
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name="is_verified")
	public Integer getIsVerified() {
		return isVerified;
	}

	public void setIsVerified(Integer isVerified) {
		this.isVerified = isVerified;
	}

	@Column(name="update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name="status")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	@Column(name="type")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
	
}
