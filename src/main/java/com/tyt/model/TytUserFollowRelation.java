package com.tyt.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName TytUserFollowRelation
 * @Description 用户关注关系对象
 * <AUTHOR>
 * @Date 2019-12-03 15:27
 * @Version 1.0
 */
@Entity
@Table(name = "tyt_user_follow_relation", schema = "tyt", catalog = "")
public class TytUserFollowRelation {
    private Long id;
    private Long userId;
    private Long followUserId;
    private Integer followStatus;
    private Date ctime;
    private Date utime;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "follow_user_id")
    public Long getFollowUserId() {
        return followUserId;
    }

    public void setFollowUserId(Long followUserId) {
        this.followUserId = followUserId;
    }

    @Basic
    @Column(name = "follow_status")
    public Integer getFollowStatus() {
        return followStatus;
    }

    public void setFollowStatus(Integer followStatus) {
        this.followStatus = followStatus;
    }

    @Basic
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "utime")
    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TytUserFollowRelation that = (TytUserFollowRelation) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(followUserId, that.followUserId) &&
                Objects.equals(followStatus, that.followStatus) &&
                Objects.equals(ctime, that.ctime) &&
                Objects.equals(utime, that.utime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, userId, followUserId, followStatus, ctime, utime);
    }
}
