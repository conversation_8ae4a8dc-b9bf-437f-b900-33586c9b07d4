package com.tyt.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>
 * 货源曝光卡发放记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Getter
@Setter
@Table(name = "tyt_exposure_card_giveaway_record")
public class ExposureCardGiveawayRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 货源id
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 曝光卡发放配置表id
     */
    @Column(name = "config_id")
    private Long configId;

    /**
     * 商品id
     */
    @Column(name = "goods_id")
    private Long goodsId;

    /**
     * 赠送曝光卡次数
     */
    @Column(name = "giveaway_num")
    private Integer giveawayNum;

    /**
     * 状态：0暂定状态；1确定发放；2不符合条件
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 货源首发时间
     */
    @Column(name = "release_time")
    private Date releaseTime;

    /**
     * 配置的进线条件，格式X,Y，发货X分钟内，拨打/查看比例低于Y%
     */
    @Column(name = "inline_condition")
    private String inlineCondition;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}
