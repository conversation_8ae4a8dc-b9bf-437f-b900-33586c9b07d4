package com.tyt.model;


import com.tyt.service.common.exception.TytException;
import com.tyt.util.ReturnCodeConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * 生成api文档用，文档确定后考虑更换成{@link ResultMsgBean}
 *
 * @param <T>
 */
@Data
public class ResultMsgBeanV2<T> implements Serializable {

    /**** 正确结果 **/
    public static final int OK = 200;
    public static final String OK_MSG = "操作成功";


    /**** 错误结果 **/
    public static final int ERROR = 500;

    private int code = OK; // code
    private String msg; // msg
    private T data; // 具体数据

    public ResultMsgBeanV2() {
    }

    public ResultMsgBeanV2(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public ResultMsgBeanV2(int code, String msg, T data) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 返回成功
     */
    public static <T> ResultMsgBeanV2<T> successResponse(T obj) {
        ResultMsgBeanV2<T> resp = new ResultMsgBeanV2<>(ReturnCodeConstant.OK, "success");
        resp.setData(obj);
        return resp;
    }

    /**
     * 返回成功
     */
    public static <T> ResultMsgBeanV2<T> successResponse() {
        return new ResultMsgBeanV2<>(ReturnCodeConstant.OK, "success");
    }

    /**
     * 返回失败
     */
    public static <T> ResultMsgBeanV2<T> failResponse(int errorCode, String errorMsg) {
        return new ResultMsgBeanV2<>(errorCode, errorMsg);
    }

    /**
     * 返回失败
     */
    public static <T> ResultMsgBeanV2<T> failResponse(Throwable t) {

        TytException customExc = TytException.createException(t);
        Integer errorCode = customExc.getErrorCode();
        String errorMsg = customExc.getErrorMsg();

        return new ResultMsgBeanV2<>(errorCode, errorMsg);
    }

    public boolean isSuccess() {
        return code == OK;
    }
}
