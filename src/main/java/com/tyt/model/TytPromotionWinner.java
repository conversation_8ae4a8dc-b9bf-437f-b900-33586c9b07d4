package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

/**
 *
 * <AUTHOR>
 * @date 2018年3月13日下午2:11:26
 * @description
 */
@Entity
@Table(name = "tyt_promotion_winner", catalog = "tyt")
public class TytPromotionWinner implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Integer proId;
    private Date proTime;
    private String name;
    private String cellphone;
    private String cellphoneTxt;
    private Date ctime;
    //顺序
    private Integer sortId;
    //奖品
    private String prize;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "pro_id")
    public Integer getProId() {
        return this.proId;
    }

    public void setProId(Integer proId) {
        this.proId = proId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "pro_time", length = 0)
    public Date getProTime() {
        return this.proTime;
    }

    public void setProTime(Date proTime) {
        this.proTime = proTime;
    }

    @Column(name = "name", length = 20)
    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "cellphone", length = 15)
    public String getCellphone() {
        return this.cellphone;
    }

    public void setCellphone(String cellphone) {
        this.cellphone = cellphone;
    }

    @Column(name = "cellphone_txt", length = 15)
    public String getCellphoneTxt() {
        return this.cellphoneTxt;
    }

    public void setCellphoneTxt(String cellphoneTxt) {
        this.cellphoneTxt = cellphoneTxt;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ctime", length = 0)
    public Date getCtime() {
        return this.ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "sort_id")
    public Integer getSortId() {
        return sortId;
    }

    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }

    @Column(name = "prize")
    public String getPrize() {
        return prize;
    }

    public void setPrize(String prize) {
        this.prize = prize;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
