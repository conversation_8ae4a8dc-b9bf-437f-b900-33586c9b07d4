package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytChannelLog entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_channel_log", catalog = "tyt")
public class TytChannelLog implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 446389864448422028L;
	private Long id;
	private Integer recordType;
	private Long userId;
	private String clientId;
	private String clientVersion;
	private Integer clientSign;
	private String osVersion;
	private Integer certificateType;
	private Integer channelCode;
	private String channelName;
	private String pagePath;
	private Integer versionType;
	private String phoneType;
	private String details;
	private Date ctime;

	// Constructors

	
	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "record_type")
	public Integer getRecordType() {
		return this.recordType;
	}

	public void setRecordType(Integer recordType) {
		this.recordType = recordType;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "client_id")
	public String getClientId() {
		return this.clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	@Column(name = "client_version")
	public String getClientVersion() {
		return this.clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	@Column(name = "client_sign")
	public Integer getClientSign() {
		return this.clientSign;
	}

	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name = "os_version")
	public String getOsVersion() {
		return this.osVersion;
	}

	public void setOsVersion(String osVersion) {
		this.osVersion = osVersion;
	}

	@Column(name = "certificate_type")
	public Integer getCertificateType() {
		return this.certificateType;
	}

	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}

	@Column(name = "channel_code")
	public Integer getChannelCode() {
		return this.channelCode;
	}

	public void setChannelCode(Integer channelCode) {
		this.channelCode = channelCode;
	}

	@Column(name = "channel_name")
	public String getChannelName() {
		return this.channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	@Column(name = "page_path")
	public String getPagePath() {
		return this.pagePath;
	}

	public void setPagePath(String pagePath) {
		this.pagePath = pagePath;
	}

	@Column(name = "versionType")
	public Integer getVersionType() {
		return this.versionType;
	}

	public void setVersionType(Integer versionType) {
		this.versionType = versionType;
	}

	@Column(name = "phoneType")
	public String getPhoneType() {
		return this.phoneType;
	}

	public void setPhoneType(String phoneType) {
		this.phoneType = phoneType;
	}

	@Column(name = "details")
	public String getDetails() {
		return this.details;
	}

	public void setDetails(String details) {
		this.details = details;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

}