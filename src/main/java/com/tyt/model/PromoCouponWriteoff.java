package com.tyt.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name="promo_coupon_writeoff")
public class PromoCouponWriteoff {
    /**
     *
     "id"	"int(11)"	"NO"	"PRI"	NULL	"auto_increment"
     "user_id"	"bigint(20)"	"NO"	"MUL"	NULL	""
     "order_id"	"varchar(64)"	"NO"	"MUL"	NULL	""
     "order_amount"	"decimal(10,2)"	"NO"	""	"0.00"	""
     "coupon_id"	"int(11)"	"NO"	""	NULL	""
     "coupon_type_id"	"int(11)"	"NO"	""	NULL	""
     "coupon_amount"	"decimal(10,2)"	"NO"	""	"0.00"	""
     "coupon_status"	"tinyint(4)"	"NO"	""	"1"	""
     "mtime"	"timestamp"	"NO"	""	"CURRENT_TIMESTAMP"	""
     "ctime"	"datetime"	"YES"	""	NULL	""
     */
    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    private int id;
    @Column(name = "user_id")
    private long userId;
    @Column(name = "order_id")
    private String orderId;
    @Column(name = "order_amount")
    private BigDecimal orderAmount;
    @Column(name = "coupon_id")
    private int couponId;
    @Column(name = "coupon_name")
    private String couponName;
    @Column(name = "coupon_type_id")
    private int couponTypeId;
    @Column(name = "coupon_amount")
    private BigDecimal couponAmount;
    @Column(name = "coupon_status")
    private int couponStatus;
    @Column(name = "ctime")
    private Date ctime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public int getCouponId() {
        return couponId;
    }

    public void setCouponId(int couponId) {
        this.couponId = couponId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public int getCouponTypeId() {
        return couponTypeId;
    }

    public void setCouponTypeId(int couponTypeId) {
        this.couponTypeId = couponTypeId;
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public int getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(int couponStatus) {
        this.couponStatus = couponStatus;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
}
