package com.tyt.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_activity_collate")
public class TytActivityCollate implements Serializable {

    private static final long serialVersionUID = 1214334919338010421L;
    private Long id;

    private Long activityId;

    private String activityName;

    private Long leadUserId;

    private String leadPhone;

    private String leadHeadUrl;

    private String leadOrderId;

    private Long memberUserId;

    private String memberPhone;

    private String memberName;

    private String memberHeadUrl;

    private String memberOrderId;

    private Long goodsId;

    private String goodsName;

    private String link;

    private Integer status;

    private Date successTime;

    private Long opId;

    private String opName;

    private Date createTime;

    private Date updateTime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Column(name = "activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Column(name = "lead_user_id")
    public Long getLeadUserId() {
        return leadUserId;
    }

    public void setLeadUserId(Long leadUserId) {
        this.leadUserId = leadUserId;
    }

    @Column(name = "lead_phone")
    public String getLeadPhone() {
        return leadPhone;
    }

    public void setLeadPhone(String leadPhone) {
        this.leadPhone = leadPhone;
    }

    @Column(name = "lead_head_url")
    public String getLeadHeadUrl() {
        return leadHeadUrl;
    }

    public void setLeadHeadUrl(String leadHeadUrl) {
        this.leadHeadUrl = leadHeadUrl;
    }

    @Column(name = "lead_order_id")
    public String getLeadOrderId() {
        return leadOrderId;
    }

    public void setLeadOrderId(String leadOrderId) {
        this.leadOrderId = leadOrderId;
    }

    @Column(name = "member_user_id")
    public Long getMemberUserId() {
        return memberUserId;
    }

    public void setMemberUserId(Long memberUserId) {
        this.memberUserId = memberUserId;
    }

    @Column(name = "member_phone")
    public String getMemberPhone() {
        return memberPhone;
    }

    public void setMemberPhone(String memberPhone) {
        this.memberPhone = memberPhone;
    }

    @Column(name = "member_name")
    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    @Column(name = "member_head_url")
    public String getMemberHeadUrl() {
        return memberHeadUrl;
    }

    public void setMemberHeadUrl(String memberHeadUrl) {
        this.memberHeadUrl = memberHeadUrl;
    }

    @Column(name = "member_order_id")
    public String getMemberOrderId() {
        return memberOrderId;
    }

    public void setMemberOrderId(String memberOrderId) {
        this.memberOrderId = memberOrderId;
    }

    @Column(name = "goods_id")
    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    @Column(name = "goods_name")
    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    @Column(name = "link")
    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "success_time")
    public Date getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(Date successTime) {
        this.successTime = successTime;
    }

    @Column(name = "op_id")
    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    @Column(name = "op_name")
    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "TytActivityCollate{" +
                "id=" + id +
                ", activityId=" + activityId +
                ", activityName='" + activityName + '\'' +
                ", leadUserId=" + leadUserId +
                ", leadPhone='" + leadPhone + '\'' +
                ", leadHeadUrl='" + leadHeadUrl + '\'' +
                ", leadOrderId='" + leadOrderId + '\'' +
                ", memberUserId=" + memberUserId +
                ", memberPhone='" + memberPhone + '\'' +
                ", memberName='" + memberName + '\'' +
                ", memberHeadUrl='" + memberHeadUrl + '\'' +
                ", memberOrderId='" + memberOrderId + '\'' +
                ", goodsId=" + goodsId +
                ", goodsName='" + goodsName + '\'' +
                ", link='" + link + '\'' +
                ", status=" + status +
                ", successTime=" + successTime +
                ", opId=" + opId +
                ", opName='" + opName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
