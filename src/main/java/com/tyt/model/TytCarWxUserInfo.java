package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 微信用户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Entity
@Table(name="tyt_car_wx_user_info")
public class TytCarWxUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;


    private String openId;


    private Long userId;
    private String userName;
    private String cellPhone;

    private String unionId;

    private String wxAppid;

    private String wxNickName;

    private String wxPhone;

    private String gender;

    private String city;

    private String province;

    private String country;

    private String avatarUrl;

    private Date createTime;

    private Date lastLoginTime;

    @Id
    @GeneratedValue
    @Column(name="id",nullable=false,unique=true)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name="open_id")
    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
    @Column(name="user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @Column(name="user_name")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    @Column(name="cell_phone")
    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    @Column(name="union_id")
    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }
    @Column(name="wx_appid")
    public String getWxAppid() {
        return wxAppid;
    }

    public void setWxAppid(String wxAppid) {
        this.wxAppid = wxAppid;
    }

    @Column(name="wx_nick_name")
    public String getWxNickName() {
        return wxNickName;
    }

    public void setWxNickName(String wxNickName) {
        this.wxNickName = wxNickName;
    }

    @Column(name="wx_phone")
    public String getWxPhone() {
        return wxPhone;
    }

    public void setWxPhone(String wxPhone) {
        this.wxPhone = wxPhone;
    }

    @Column(name="gender")
    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    @Column(name="city")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name="province")
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    @Column(name="country")
    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }
    @Column(name="avatar_url")
    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    @Column(name="create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Column(name="last_login_time")
    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    @Override
    public String toString() {
        return "TytWxUserInfo{" +
        "id=" + id +
        ", openId=" + openId +
        ", userId=" + userId +
        ", unionId=" + unionId +
        ", wxAppid=" + wxAppid +
        ", wxNickName=" + wxNickName +
        ", wxPhone=" + wxPhone +
        ", gender=" + gender +
        ", city=" + city +
        ", province=" + province +
        ", country=" + country +
        ", avatarUrl=" + avatarUrl +
        ", createTime=" + createTime +
        "}";
    }
}
