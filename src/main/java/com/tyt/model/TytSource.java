package com.tyt.model;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonInclude;
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name="tyt_source")
public class TytSource implements java.io.Serializable{

	private static final long serialVersionUID = -6331750162365428950L;
	Long id;//'主键',
	 String groupCode;//'分组代码',
	 String groupName;//'分组名称',
	 String value;//'属性值',
	 String name;//'属性名称',
	 String shortName;//'属性简称',
	 String remark;//'描述',
	 Integer sort;//排序
	//String code;
	 String parent;
	 Integer status;
	 List<TytSource> subset;
	 Integer dictStatus;
	
	@Id 
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="group_code")
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	@Column(name="group_name")
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	@Column(name="value")
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	@Column(name="name")
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	@Column(name="short_name")
	public String getShortName() {
		return shortName;
	}
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}
	@Column(name="remark")
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	@Column(name="sort")
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
//	@Column(name="code")
//	public String getCode() {
//		return code;
//	}
//	public void setCode(String code) {
//		this.code = code;
//	}
	@Column(name="parent")
	public String getParent() {
		return parent;
	}
	public void setParent(String parent) {
		this.parent = parent;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Transient
	public List<TytSource> getSubset() {
		return subset;
	}
	public void setSubset(List<TytSource> subset) {
		this.subset = subset;
	}
	@Column(name="dict_status")
	public Integer getDictStatus() {
		return dictStatus;
	}
	public void setDictStatus(Integer dictStatus) {
		this.dictStatus = dictStatus;
	}
	 
}
