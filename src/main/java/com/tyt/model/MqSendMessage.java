package com.tyt.model;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Table(name = "`mq_send_message`")
public class MqSendMessage implements Serializable {
    @Id
    @Column(name = "`id`")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 消息主题
     */
    @Column(name = "`message_topic`")
    private String messageTopic;

    /**
     * 消息tag
     */
    @Column(name = "`message_tag`")
    private String messageTag;

    /**
     * 消息唯一标识
     */
    @Column(name = "`message_serial_num`")
    private String messageSerialNum;

    /**
     * 消息内容
     */
    @Column(name = "`message_content`")
    private String messageContent;

    /**
     * 状态 1已发送
     */
    @Column(name = "`status`")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "`create_time`")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取消息主题
     *
     * @return message_topic - 消息主题
     */
    public String getMessageTopic() {
        return messageTopic;
    }

    /**
     * 设置消息主题
     *
     * @param messageTopic 消息主题
     */
    public void setMessageTopic(String messageTopic) {
        this.messageTopic = messageTopic == null ? null : messageTopic.trim();
    }

    /**
     * 获取消息tag
     *
     * @return message_tag - 消息tag
     */
    public String getMessageTag() {
        return messageTag;
    }

    /**
     * 设置消息tag
     *
     * @param messageTag 消息tag
     */
    public void setMessageTag(String messageTag) {
        this.messageTag = messageTag == null ? null : messageTag.trim();
    }

    /**
     * 获取消息唯一标识
     *
     * @return message_serial_num - 消息唯一标识
     */
    public String getMessageSerialNum() {
        return messageSerialNum;
    }

    /**
     * 设置消息唯一标识
     *
     * @param messageSerialNum 消息唯一标识
     */
    public void setMessageSerialNum(String messageSerialNum) {
        this.messageSerialNum = messageSerialNum == null ? null : messageSerialNum.trim();
    }

    /**
     * 获取消息内容
     *
     * @return message_content - 消息内容
     */
    public String getMessageContent() {
        return messageContent;
    }

    /**
     * 设置消息内容
     *
     * @param messageContent 消息内容
     */
    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent == null ? null : messageContent.trim();
    }

    /**
     * 获取状态 1已发送
     *
     * @return status - 状态 1已发送
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置状态 1已发送
     *
     * @param status 状态 1已发送
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", messageTopic=").append(messageTopic);
        sb.append(", messageTag=").append(messageTag);
        sb.append(", messageSerialNum=").append(messageSerialNum);
        sb.append(", messageContent=").append(messageContent);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}