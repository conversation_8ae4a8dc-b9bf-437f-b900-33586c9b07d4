package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name="cs_business_user_bind")
public class CsBusunessUserBind {
    /**
     * 主键
     */
    private Long id;

    /**
     * 客服平台关联账户
     */
    private String csAccount;

    /**
     * 系统用户名
     */
    private String name;

    /**
     * 登录手机号
     */
    private String loginPhoneNo;

    /**
     * 密码
     */
    private String password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门表ID
     */
    private Long departmentId;

    /**
     * 上级领导ID
     */
    private Long leaderId;

    /**
     * 下级转移
     */
    private Long subordinateId;

    /**
     * 是否有效(1 有效 0 无效)
     */
    private Integer isValid;

    /**
     * 最后一次登录时间
     */
    private Date lastLoginTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 最后修改人（员工表ID）
     */
    private Long lastUpdater;

    /**
     * 角色Id
     */
    private Long roleId;

    /**
     * 最后修改人名
     */
    private String lastUpdaterName;

    /**
     * 坐席小号
     */
    private String smallPhone;

    /**
     * 默认-100，代表null值，处理null过滤问题
     */
    private Long deptTopId;

    /**
     * 主键
     * @return id 主键
     */
    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 客服平台关联账户
     * @return cs_account 客服平台关联账户
     */
    @Column(name="cs_account")
    public String getCsAccount() {
        return csAccount;
    }

    /**
     * 客服平台关联账户
     * @param csAccount 客服平台关联账户
     */
    public void setCsAccount(String csAccount) {
        this.csAccount = csAccount == null ? null : csAccount.trim();
    }

    /**
     * 系统用户名
     * @return name 系统用户名
     */
    @Column(name="name")
    public String getName() {
        return name;
    }

    /**
     * 系统用户名
     * @param name 系统用户名
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 登录手机号
     * @return login_phone_no 登录手机号
     */
    @Column(name="login_phone_no")
    public String getLoginPhoneNo() {
        return loginPhoneNo;
    }

    /**
     * 登录手机号
     * @param loginPhoneNo 登录手机号
     */
    public void setLoginPhoneNo(String loginPhoneNo) {
        this.loginPhoneNo = loginPhoneNo == null ? null : loginPhoneNo.trim();
    }

    /**
     * 密码
     * @return password 密码
     */
    @Column(name="password")
    public String getPassword() {
        return password;
    }

    /**
     * 密码
     * @param password 密码
     */
    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    /**
     * 真实姓名
     * @return real_name 真实姓名
     */
    @Column(name="real_name")
    public String getRealName() {
        return realName;
    }

    /**
     * 真实姓名
     * @param realName 真实姓名
     */
    public void setRealName(String realName) {
        this.realName = realName == null ? null : realName.trim();
    }

    /**
     * 邮箱
     * @return email 邮箱
     */
    @Column(name="email")
    public String getEmail() {
        return email;
    }

    /**
     * 邮箱
     * @param email 邮箱
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * 部门表ID
     * @return department_id 部门表ID
     */
    @Column(name="department_id")
    public Long getDepartmentId() {
        return departmentId;
    }

    /**
     * 部门表ID
     * @param departmentId 部门表ID
     */
    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    /**
     * 上级领导ID
     * @return leader_id 上级领导ID
     */
    @Column(name="leader_id")
    public Long getLeaderId() {
        return leaderId;
    }

    /**
     * 上级领导ID
     * @param leaderId 上级领导ID
     */
    public void setLeaderId(Long leaderId) {
        this.leaderId = leaderId;
    }

    /**
     * 下级转移
     * @return subordinate_id 下级转移
     */
    @Column(name="subordinate_id")
    public Long getSubordinateId() {
        return subordinateId;
    }

    /**
     * 下级转移
     * @param subordinateId 下级转移
     */
    public void setSubordinateId(Long subordinateId) {
        this.subordinateId = subordinateId;
    }

    /**
     * 是否有效(1 有效 0 无效)
     * @return is_valid 是否有效(1 有效 0 无效)
     */
    @Column(name="is_valid")
    public Integer getIsValid() {
        return isValid;
    }

    /**
     * 是否有效(1 有效 0 无效)
     * @param isValid 是否有效(1 有效 0 无效)
     */
    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    /**
     * 最后一次登录时间
     * @return last_login_time 最后一次登录时间
     */
    @Column(name="last_login_time")
    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    /**
     * 最后一次登录时间
     * @param lastLoginTime 最后一次登录时间
     */
    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    @Column(name="create_time")
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间
     * @return update_time 修改时间
     */
    @Column(name="update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 最后修改人（员工表ID）
     * @return last_updater 最后修改人（员工表ID）
     */
    @Column(name="last_updater")
    public Long getLastUpdater() {
        return lastUpdater;
    }

    /**
     * 最后修改人（员工表ID）
     * @param lastUpdater 最后修改人（员工表ID）
     */
    public void setLastUpdater(Long lastUpdater) {
        this.lastUpdater = lastUpdater;
    }

    /**
     * 角色Id
     * @return role_id 角色Id
     */
    @Column(name="role_id")
    public Long getRoleId() {
        return roleId;
    }

    /**
     * 角色Id
     * @param roleId 角色Id
     */
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    /**
     * 最后修改人名
     * @return last_updater_name 最后修改人名
     */
    @Column(name="last_updater_name")
    public String getLastUpdaterName() {
        return lastUpdaterName;
    }

    /**
     * 最后修改人名
     * @param lastUpdaterName 最后修改人名
     */
    public void setLastUpdaterName(String lastUpdaterName) {
        this.lastUpdaterName = lastUpdaterName == null ? null : lastUpdaterName.trim();
    }

    /**
     * 坐席小号
     * @return small_phone 坐席小号
     */
    @Column(name="small_phone")
    public String getSmallPhone() {
        return smallPhone;
    }

    /**
     * 坐席小号
     * @param smallPhone 坐席小号
     */
    public void setSmallPhone(String smallPhone) {
        this.smallPhone = smallPhone == null ? null : smallPhone.trim();
    }

    /**
     * 默认-100，代表null值，处理null过滤问题
     * @return dept_top_id 默认-100，代表null值，处理null过滤问题
     */
    @Column(name="dept_top_id")
    public Long getDeptTopId() {
        return deptTopId;
    }

    /**
     * 默认-100，代表null值，处理null过滤问题
     * @param deptTopId 默认-100，代表null值，处理null过滤问题
     */
    public void setDeptTopId(Long deptTopId) {
        this.deptTopId = deptTopId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", csAccount=").append(csAccount);
        sb.append(", name=").append(name);
        sb.append(", loginPhoneNo=").append(loginPhoneNo);
        sb.append(", password=").append(password);
        sb.append(", realName=").append(realName);
        sb.append(", email=").append(email);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", leaderId=").append(leaderId);
        sb.append(", subordinateId=").append(subordinateId);
        sb.append(", isValid=").append(isValid);
        sb.append(", lastLoginTime=").append(lastLoginTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", lastUpdater=").append(lastUpdater);
        sb.append(", roleId=").append(roleId);
        sb.append(", lastUpdaterName=").append(lastUpdaterName);
        sb.append(", smallPhone=").append(smallPhone);
        sb.append(", deptTopId=").append(deptTopId);
        sb.append("]");
        return sb.toString();
    }
}