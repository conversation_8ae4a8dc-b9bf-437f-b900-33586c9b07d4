package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="tyt_newcar")
public class NewCar implements Serializable{
	
  private static final long serialVersionUID = 1L;
  private Long id;
  private String title;//标题
  private Integer model;//车型
  private Integer carName;//品牌
  private Integer category;//类别
  private String province;//省
  private String city;//市
  private String county;//县
  private String scope;//经营范围
  private String telName;//联系人
  private String telephone;//联系电话
  private String images;//图片
  private Timestamp ctime;//发布时间
  private String detail;//详细描述
  private Integer status;//状态 0未审核
  private String  cellPhone;//账号
  private Integer saveDay;//保存时间
  private Integer topDay;//置顶时间
  
@Id
@GeneratedValue
@Column(name = "id", unique = true, nullable = false)
public Long getId() {
	return id;
}
public void setId(Long id) {
	this.id = id;
}
@Column(name="car_name")
public Integer getCarName() {
	return carName;
}
public void setCarName(Integer carName) {
	this.carName = carName;
}
@Column(name="title")
public String getTitle() {
	return title;
}
public void setTitle(String title) {
	this.title = title;
}
@Column(name="tel_name")
public String getTelName() {
	return telName;
}
public void setTelName(String telName) {
	this.telName = telName;
}
@Column(name="detail")
public String getDetail() {
	return detail;
}
public void setDetail(String detail) {
	this.detail = detail;
}
@Column(name="model")
public Integer getModel() {
	return model;
}
public void setModel(Integer model) {
	this.model = model;
}
@Column(name="category")
public Integer getCategory() {
	return category;
  }
  public void setCategory(Integer category) {
	this.category = category;
  }

@Column(name="ctime")
public Timestamp getCtime() {
	return ctime;
}
public void setCtime(Timestamp ctime) {
	this.ctime = ctime;
}
@Column(name="save_day")
public Integer getSaveDay() {
	return saveDay;
}
public void setSaveDay(Integer saveDay) {
	this.saveDay = saveDay;
}
@Column(name="top_day")
public Integer getTopDay() {
	return topDay;
}
public void setTopDay(Integer topDay) {
	this.topDay = topDay;
}
@Column(name="telephone")
public String getTelephone() {
	return telephone;
}
public void setTelephone(String telephone) {
	this.telephone = telephone;
}
@Column(name="scope")
public String getScope() {
	return scope;
}
public void setScope(String scope) {
	this.scope = scope;
}
@Column(name="images")
public String getImages() {
	return images;
}
public void setImages(String images) {
	this.images = images;
}
@Column(name="new_status")
public Integer getStatus() {
	return status;
}
public void setStatus(Integer status) {
	this.status = status;
}
@Column(name="cell_phone")
public String getCellPhone() {
	return cellPhone;
}
public void setCellPhone(String cellPhone) {
	this.cellPhone = cellPhone;
}
@Column(name="province")
public String getProvince() {
	return province;
}
public void setProvince(String province) {
	this.province = province;
}
@Column(name="city")
public String getCity() {
	return city;
}
public void setCity(String city) {
	this.city = city;
	
}
@Column(name="county")
public String getCounty() {
	return county;
}
public void setCounty(String county) {
	this.county = county;
}

}
