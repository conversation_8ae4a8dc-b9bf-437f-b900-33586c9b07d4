package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
/**
 * 用户车辆信息记录表
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "tyt_car_log")
public class CarLog  implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4178269941140050216L;
	Long id;// id
	Long carId;
	Long userId;// 用户id
	Date recordTime;
//	String typeCode;// 车辆类型代码 1平板车
//	String type;// 车辆类型名称
	String headCity;// 车头牌照头字母
	String headNo;// 车头牌照号码
	String tailCity;// 挂车牌照头字母
	String tailNo;// 挂车牌照号码
//	String lengthCode;// 车长代码
//	String length;// 车长单位米
//	String carryCode;// 载重代码
//	String carry;// 载重单位吨
	String headDrivingUrl;// 车头行驶本url
	String tailDrivingUrl;// 挂车行驶本URL
//	String insuranceCode;// 保险代码
//	String insurance;// 购买保险类别
//	Date expireTime;// 保险到期时间
	Date createTime;// 创建日期
	Date updateTime;// 更新时间
	String auth;// 车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
//	String tailAuth;// 挂车行驶本认证状态0:未认证；1:认证成功；2：认证失败
	String remark;// 描述
//	String headPhotoUrl;// 车头照片
	String failureReason;// 失败原因
	
	/*2015-08-31*/
	String gpsUrl ;// 'gps定位网址',
	String gpsName;//'gps网站用户名',
	String gpsPwd;//'gps网站密码',
	
	/*2015-09-08*/
	String headName;//'车头车主姓名',
	String headBrand;//'车头品牌型号',
	String tailName;//'挂车车主姓名',
	String tailBrand;//'挂车品牌型号',
	Integer isDelete=1;
	
	String carName;
	String sex;
	String card;
	
	//2017-07-19
	String headAuthStatus;
	String headFailReason;
	String tailAuthStatus;
	String tailFailReason;
	String findGoodOnOff;
	Long sort;
	Date examineTime;
	String examineName;
	Long examineEmplId;
	String deleteReason;
	String updateReason;
	String updateType;
	Integer isDisplay=0;
	
	@Id
	@GeneratedValue
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="car_id")
	public Long getCarId() {
		return carId;
	}
	public void setCarId(Long carId) {
		this.carId = carId;
	}
	@Column(name="record_time")
	public Date getRecordTime() {
		return recordTime;
	}
	public void setRecordTime(Date recordTime) {
		this.recordTime = recordTime;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
//	@Column(name="type_code")
//	public String getTypeCode() {
//		return typeCode;
//	}
//	public void setTypeCode(String typeCode) {
//		this.typeCode = typeCode;
//	}
//	@Column(name="type")
//	public String getType() {
//		return type;
//	}
//	public void setType(String type) {
//		this.type = type;
//	}
	@Column(name="head_city")
	public String getHeadCity() {
		return headCity;
	}
	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}
	@Column(name="head_no")
	public String getHeadNo() {
		return headNo;
	}
	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}
	@Column(name="tail_city")
	public String getTailCity() {
		return tailCity;
	}
	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}
	@Column(name="tail_no")
	public String getTailNo() {
		return tailNo;
	}
	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}
//	@Column(name="length_code")
//	public String getLengthCode() {
//		return lengthCode;
//	}
//	public void setLengthCode(String lengthCode) {
//		this.lengthCode = lengthCode;
//	}
//	@Column(name="length")
//	public String getLength() {
//		return length;
//	}
//	public void setLength(String length) {
//		this.length = length;
//	}
//	
//	@Column(name="carry_code")
//	public String getCarryCode() {
//		return carryCode;
//	}
//	public void setCarryCode(String carryCode) {
//		this.carryCode = carryCode;
//	}
//	@Column(name="carry")
//	public String getCarry() {
//		return carry;
//	}
//	public void setCarry(String carry) {
//		this.carry = carry;
//	}
	@Column(name="head_driving_url")
	public String getHeadDrivingUrl() {
		return headDrivingUrl;
	}
	public void setHeadDrivingUrl(String headDrivingUrl) {
		this.headDrivingUrl = headDrivingUrl;
	}
	@Column(name="tail_driving_url")
	public String getTailDrivingUrl() {
		return tailDrivingUrl;
	}
	public void setTailDrivingUrl(String tailDrivingUrl) {
		this.tailDrivingUrl = tailDrivingUrl;
	}
//	@Column(name="insurance_code")
//	public String getInsuranceCode() {
//		return insuranceCode;
//	}
//	public void setInsuranceCode(String insuranceCode) {
//		this.insuranceCode = insuranceCode;
//	}
//	@Column(name="insurance")
//	public String getInsurance() {
//		return insurance;
//	}
//	public void setInsurance(String insurance) {
//		this.insurance = insurance;
//	}
//	@Column(name="expire_time")
//	public Date getExpireTime() {
//		return expireTime;
//	}
//	public void setExpireTime(Date expireTime) {
//		this.expireTime = expireTime;
//	}
	
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}
	
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name="update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Column(name="auth")
	public String getAuth() {
		return auth;
	}
	public void setAuth(String auth) {
		this.auth = auth;
	}
	@Column(name="remark")
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
//	@Column(name="head_photo_url")
//	public String getHeadPhotoUrl() {
//		return headPhotoUrl;
//	}
//	public void setHeadPhotoUrl(String headPhotoUrl) {
//		this.headPhotoUrl = headPhotoUrl;
//	}
	@Column(name="failure_reason")
	public String getFailureReason() {
		return failureReason;
	}
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	
	
	@Column(name="gps_url")
	public String getGpsUrl() {
		return gpsUrl;
	}
	public void setGpsUrl(String gpsUrl) {
		this.gpsUrl = gpsUrl;
	}
	@Column(name="gps_name")
	public String getGpsName() {
		return gpsName;
	}
	public void setGpsName(String gpsName) {
		this.gpsName = gpsName;
	}
	@Column(name="gps_pwd")
	public String getGpsPwd() {
		return gpsPwd;
	}
	public void setGpsPwd(String gpsPwd) {
		this.gpsPwd = gpsPwd;
	}
	@Column(name="head_name")
	public String getHeadName() {
		return headName;
	}
	public void setHeadName(String headName) {
		this.headName = headName;
	}
	@Column(name="head_brand")
	public String getHeadBrand() {
		return headBrand;
	}
	public void setHeadBrand(String headBrand) {
		this.headBrand = headBrand;
	}
	@Column(name="tail_name")
	public String getTailName() {
		return tailName;
	}
	public void setTailName(String tailName) {
		this.tailName = tailName;
	}
	@Column(name="tail_brand")
	public String getTailBrand() {
		return tailBrand;
	}
	public void setTailBrand(String tailBrand) {
		this.tailBrand = tailBrand;
	}
	@Column(name="is_delete")
	public Integer getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}
	@Column(name="car_name")
	public String getCarName() {
		return carName;
	}
	
	public void setCarName(String carName) {
		this.carName = carName;
	}
	@Column(name="sex")
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	@Column(name="card")
	public String getCard() {
		return card;
	}
	public void setCard(String card) {
		this.card = card;
	}
	
	@Column(name="head_auth_status")
	public String getHeadAuthStatus() {
		return headAuthStatus;
	}
	public void setHeadAuthStatus(String headAuthStatus) {
		this.headAuthStatus = headAuthStatus;
	}
	@Column(name="head_failure_reason")
	public String getHeadFailReason() {
		return headFailReason;
	}
	public void setHeadFailReason(String headFailReason) {
		this.headFailReason = headFailReason;
	}
	@Column(name="tail_auth_status")
	public String getTailAuthStatus() {
		return tailAuthStatus;
	}
	public void setTailAuthStatus(String tailAuthStatus) {
		this.tailAuthStatus = tailAuthStatus;
	}
	@Column(name="tail_failure_reason")
	public String getTailFailReason() {
		return tailFailReason;
	}
	public void setTailFailReason(String tailFailReason) {
		this.tailFailReason = tailFailReason;
	}
	@Column(name="find_good_onoff")
	public String getFindGoodOnOff() {
		return findGoodOnOff;
	}
	public void setFindGoodOnOff(String findGoodOnOff) {
		this.findGoodOnOff = findGoodOnOff;
	}
	@Column(name="sort")
	public Long getSort() {
		return sort;
	}
	public void setSort(Long sort) {
		this.sort = sort;
	}
	@Column(name="examine_time")
	public Date getExamineTime() {
		return examineTime;
	}
	public void setExamineTime(Date examineTime) {
		this.examineTime = examineTime;
	}
	@Column(name="examine_name")
	public String getExamineName() {
		return examineName;
	}
	public void setExamineName(String examineName) {
		this.examineName = examineName;
	}
	@Column(name="examine_empl_id")
	public Long getExamineEmplId() {
		return examineEmplId;
	}
	public void setExamineEmplId(Long examineEmplId) {
		this.examineEmplId = examineEmplId;
	}
	@Column(name="delete_reason")
	public String getDeleteReason() {
		return deleteReason;
	}
	public void setDeleteReason(String deleteReason) {
		this.deleteReason = deleteReason;
	}
	@Column(name="update_reason")
	public String getUpdateReason() {
		return updateReason;
	}
	public void setUpdateReason(String updateReason) {
		this.updateReason = updateReason;
	}
	@Column(name="update_type")
	public String getUpdateType() {
		return updateType;
	}
	public void setUpdateType(String updateType) {
		this.updateType = updateType;
	}
	@Column(name="is_display")
	public Integer getIsDisplay() {
		return isDisplay;
	}
	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	
	
}
