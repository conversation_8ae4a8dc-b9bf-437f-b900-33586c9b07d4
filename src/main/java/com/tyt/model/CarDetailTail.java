package com.tyt.model;

import javax.persistence.*;
import java.util.Objects;

/**
 * @ClassName TytCarDetailTail
 * @Description 挂车详情信息表
 * <AUTHOR>
 * @Date 2019-05-29 18:54
 * @Version 1.0
 */
@Entity
@Table(name = "tyt_car_detail_tail", schema = "tyt", catalog = "")
public class CarDetailTail {
    private Long id;
    private Long carId;
    private Long userId;
    private String city;
    private String carNo;
    private String carType;
    private String owner;
    private String address;
    private String useNature;
    private String carBrand;
    private String carIdcode;
    private String carEngineNo;
    private String carRegister;
    private String issueDate;
    private String recordNo;
    private String people;
    private String totalWeight;
    private String curbWeight;
    private String checkWeight;
    private String towWeight;
    private String scrapDate;
    private String testDate;
    private String length;
    private String width;
    private String height;
    private String state;
    private String tailSuccRemark;
    private String blongType;
    private String carBrandDetail;
    private String checkRecord;
    private Integer isPureFlat;
    private String loadSurfaceLength;
    private String loadSurfaceHeight;
    private String gooseneckHeight;
    private String gooseneckLength;
    private String plateHeight;
    private String maxPayload;
    private Integer isExposeTyre;
    private Integer isWithWing;
    private Integer isHaveBackplate;
    //载货面尾部长度(单位:米)
    private String loadSurfaceTailLength;
    //是否拼接抽拉(1.否 2.拼接 3.抽拉)
    private Integer isJointPull;
    //拼接抽拉长度(单位:米)
    private String jointPullLength;
    private String carTypeName;
    private String isPureFlatName;
    private Integer hasLadder;
    /**
     *是否拼接，1-是 2-否
     */
    private String isJoint;
    /**
     *所有人电话
     */
    private String tailPhone;
    /**
     * 拼接线数
     */
    private String jointLength;

    /**
     * 车头最大轴荷
     */
    private Integer maximumAxleLoad;

    /**
     * 发证机关
     */
    private String tailIssueAuthority;

    @Transient
    public String getCarTypeName() {
        return carTypeName;
    }

    public void setCarTypeName(String carTypeName) {
        this.carTypeName = carTypeName;
    }
    @Transient
    public String getIsPureFlatName() {
        return isPureFlatName;
    }

    public void setIsPureFlatName(String isPureFlatName) {
        this.isPureFlatName = isPureFlatName;
    }

    @Basic
    @Column(name = "joint_length")
    public String getJointLength() {
        return jointLength;
    }

    public void setJointLength(String jointLength) {
        this.jointLength = jointLength;
    }

    @Basic
    @Column(name = "load_surface_height")
    public String getLoadSurfaceHeight() {
        return loadSurfaceHeight;
    }

    public void setLoadSurfaceHeight(String loadSurfaceHeight) {
        this.loadSurfaceHeight = loadSurfaceHeight;
    }

    @Transient
    public String getTailPhone() {
        return tailPhone;
    }

    public void setTailPhone(String tailPhone) {
        this.tailPhone = tailPhone;
    }

    @Basic
    @Column(name = "is_joint")
    public String getIsJoint() {
        return isJoint;
    }

    public void setIsJoint(String isJoint) {
        this.isJoint = isJoint;
    }

    @Transient
    public Integer getHasLadder() {
        return hasLadder;
    }

    public void setHasLadder(Integer hasLadder) {
        this.hasLadder = hasLadder;
    }

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "car_id")
    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }
    @Basic
    @Column(name = "maximum_axle_load")
    public Integer getMaximumAxleLoad() {
        return maximumAxleLoad;
    }

    public void setMaximumAxleLoad(Integer maximumAxleLoad) {
        this.maximumAxleLoad = maximumAxleLoad;
    }
    @Basic
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "city")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Basic
    @Column(name = "car_no")
    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    @Basic
    @Column(name = "car_type")
    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    @Basic
    @Column(name = "owner")
    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    @Basic
    @Column(name = "address")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Basic
    @Column(name = "use_nature")
    public String getUseNature() {
        return useNature;
    }

    public void setUseNature(String useNature) {
        this.useNature = useNature;
    }

    @Basic
    @Column(name = "car_brand")
    public String getCarBrand() {
        return carBrand;
    }

    public void setCarBrand(String carBrand) {
        this.carBrand = carBrand;
    }

    @Basic
    @Column(name = "car_idcode")
    public String getCarIdcode() {
        return carIdcode;
    }

    public void setCarIdcode(String carIdcode) {
        this.carIdcode = carIdcode;
    }

    @Basic
    @Column(name = "car_engine_no")
    public String getCarEngineNo() {
        return carEngineNo;
    }

    public void setCarEngineNo(String carEngineNo) {
        this.carEngineNo = carEngineNo;
    }

    @Basic
    @Column(name = "car_register")
    public String getCarRegister() {
        return carRegister;
    }

    public void setCarRegister(String carRegister) {
        this.carRegister = carRegister;
    }

    @Basic
    @Column(name = "issue_date")
    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    @Basic
    @Column(name = "record_no")
    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo;
    }

    @Basic
    @Column(name = "people")
    public String getPeople() {
        return people;
    }

    public void setPeople(String people) {
        this.people = people;
    }

    @Basic
    @Column(name = "total_weight")
    public String getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(String totalWeight) {
        this.totalWeight = totalWeight;
    }

    @Basic
    @Column(name = "curb_weight")
    public String getCurbWeight() {
        return curbWeight;
    }

    public void setCurbWeight(String curbWeight) {
        this.curbWeight = curbWeight;
    }

    @Basic
    @Column(name = "check_weight")
    public String getCheckWeight() {
        return checkWeight;
    }

    public void setCheckWeight(String checkWeight) {
        this.checkWeight = checkWeight;
    }

    @Basic
    @Column(name = "tow_weight")
    public String getTowWeight() {
        return towWeight;
    }

    public void setTowWeight(String towWeight) {
        this.towWeight = towWeight;
    }

    @Basic
    @Column(name = "scrap_date")
    public String getScrapDate() {
        return scrapDate;
    }

    public void setScrapDate(String scrapDate) {
        this.scrapDate = scrapDate;
    }

    @Basic
    @Column(name = "test_date")
    public String getTestDate() {
        return testDate;
    }

    public void setTestDate(String testDate) {
        this.testDate = testDate;
    }

    @Basic
    @Column(name = "length")
    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    @Basic
    @Column(name = "width")
    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    @Basic
    @Column(name = "height")
    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    @Basic
    @Column(name = "state")
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Basic
    @Column(name = "tail_succ_remark")
    public String getTailSuccRemark() {
        return tailSuccRemark;
    }

    public void setTailSuccRemark(String tailSuccRemark) {
        this.tailSuccRemark = tailSuccRemark;
    }

    @Basic
    @Column(name = "blong_type")
    public String getBlongType() {
        return blongType;
    }

    public void setBlongType(String blongType) {
        this.blongType = blongType;
    }

    @Basic
    @Column(name = "car_brand_detail")
    public String getCarBrandDetail() {
        return carBrandDetail;
    }

    public void setCarBrandDetail(String carBrandDetail) {
        this.carBrandDetail = carBrandDetail;
    }

    @Basic
    @Column(name = "check_record")
    public String getCheckRecord() {
        return checkRecord;
    }

    public void setCheckRecord(String checkRecord) {
        this.checkRecord = checkRecord;
    }

    @Basic
    @Column(name = "is_pure_flat")
    public Integer getIsPureFlat() {
        return isPureFlat;
    }

    public void setIsPureFlat(Integer isPureFlat) {
        this.isPureFlat = isPureFlat;
    }

    @Basic
    @Column(name = "load_surface_length")
    public String getLoadSurfaceLength() {
        return loadSurfaceLength;
    }

    public void setLoadSurfaceLength(String loadSurfaceLength) {
        this.loadSurfaceLength = loadSurfaceLength;
    }

    @Basic
    @Column(name = "gooseneck_height")
    public String getGooseneckHeight() {
        return gooseneckHeight;
    }

    public void setGooseneckHeight(String gooseneckHeight) {
        this.gooseneckHeight = gooseneckHeight;
    }

    @Basic
    @Column(name = "gooseneck_length")
    public String getGooseneckLength() {
        return gooseneckLength;
    }

    public void setGooseneckLength(String gooseneckLength) {
        this.gooseneckLength = gooseneckLength;
    }

    @Basic
    @Column(name = "plate_height")
    public String getPlateHeight() {
        return plateHeight;
    }

    public void setPlateHeight(String plateHeight) {
        this.plateHeight = plateHeight;
    }

    @Basic
    @Column(name = "max_payload")
    public String getMaxPayload() {
        return maxPayload;
    }

    public void setMaxPayload(String maxPayload) {
        this.maxPayload = maxPayload;
    }

    @Basic
    @Column(name = "is_expose_tyre")
    public Integer getIsExposeTyre() {
        return isExposeTyre;
    }

    public void setIsExposeTyre(Integer isExposeTyre) {
        this.isExposeTyre = isExposeTyre;
    }

    @Basic
    @Column(name = "is_with_wing")
    public Integer getIsWithWing() {
        return isWithWing;
    }

    public void setIsWithWing(Integer isWithWing) {
        this.isWithWing = isWithWing;
    }

    @Basic
    @Column(name = "is_have_backplate")
    public Integer getIsHaveBackplate() {
        return isHaveBackplate;
    }

    public void setIsHaveBackplate(Integer isHaveBackplate) {
        this.isHaveBackplate = isHaveBackplate;
    }

    @Basic
    @Column(name = "load_surface_tail_length")
    public String getLoadSurfaceTailLength() {
        return loadSurfaceTailLength;
    }

    public void setLoadSurfaceTailLength(String loadSurfaceTailLength) {
        this.loadSurfaceTailLength = loadSurfaceTailLength;
    }

    @Basic
    @Column(name = "is_joint_pull")
    public Integer getIsJointPull() {
        return isJointPull;
    }

    public void setIsJointPull(Integer isJointPull) {
        this.isJointPull = isJointPull;
    }

    @Basic
    @Column(name = "joint_pull_length")
    public String getJointPullLength() {
        return jointPullLength;
    }

    public void setJointPullLength(String jointPullLength) {
        this.jointPullLength = jointPullLength;
    }

    @Basic
    @Column(name = "tail_issue_authority")
    public String getTailIssueAuthority() {
        return tailIssueAuthority;
    }

    public void setTailIssueAuthority(String tailIssueAuthority) {
        this.tailIssueAuthority = tailIssueAuthority;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CarDetailTail that = (CarDetailTail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(carId, that.carId) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(city, that.city) &&
                Objects.equals(carNo, that.carNo) &&
                Objects.equals(carType, that.carType) &&
                Objects.equals(owner, that.owner) &&
                Objects.equals(address, that.address) &&
                Objects.equals(useNature, that.useNature) &&
                Objects.equals(carBrand, that.carBrand) &&
                Objects.equals(carIdcode, that.carIdcode) &&
                Objects.equals(carEngineNo, that.carEngineNo) &&
                Objects.equals(carRegister, that.carRegister) &&
                Objects.equals(issueDate, that.issueDate) &&
                Objects.equals(recordNo, that.recordNo) &&
                Objects.equals(people, that.people) &&
                Objects.equals(totalWeight, that.totalWeight) &&
                Objects.equals(curbWeight, that.curbWeight) &&
                Objects.equals(checkWeight, that.checkWeight) &&
                Objects.equals(towWeight, that.towWeight) &&
                Objects.equals(scrapDate, that.scrapDate) &&
                Objects.equals(testDate, that.testDate) &&
                Objects.equals(length, that.length) &&
                Objects.equals(width, that.width) &&
                Objects.equals(height, that.height) &&
                Objects.equals(state, that.state) &&
                Objects.equals(tailSuccRemark, that.tailSuccRemark) &&
                Objects.equals(blongType, that.blongType) &&
                Objects.equals(carBrandDetail, that.carBrandDetail) &&
                Objects.equals(checkRecord, that.checkRecord) &&
                Objects.equals(isPureFlat, that.isPureFlat) &&
                Objects.equals(loadSurfaceLength, that.loadSurfaceLength) &&
                Objects.equals(gooseneckHeight, that.gooseneckHeight) &&
                Objects.equals(gooseneckLength, that.gooseneckLength) &&
                Objects.equals(plateHeight, that.plateHeight) &&
                Objects.equals(maxPayload, that.maxPayload) &&
                Objects.equals(isExposeTyre, that.isExposeTyre) &&
                Objects.equals(isWithWing, that.isWithWing) &&
                Objects.equals(isHaveBackplate, that.isHaveBackplate) &&
                Objects.equals(loadSurfaceTailLength, that.loadSurfaceTailLength) &&
                Objects.equals(isJointPull, that.isJointPull) &&
                Objects.equals(jointPullLength, that.jointPullLength);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, carId, userId, city, carNo, carType, owner, address, useNature, carBrand, carIdcode, carEngineNo, carRegister, issueDate, recordNo, people, totalWeight, curbWeight, checkWeight, towWeight, scrapDate, testDate, length, width, height, state, tailSuccRemark, blongType, carBrandDetail, checkRecord, isPureFlat, loadSurfaceLength, gooseneckHeight, gooseneckLength, plateHeight, maxPayload, isExposeTyre, isWithWing, isHaveBackplate, loadSurfaceTailLength, isJointPull, jointPullLength);
    }
}
