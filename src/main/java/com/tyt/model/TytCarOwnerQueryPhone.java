package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytCarownerQueryPhone entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_carowner_query_phone")
public class TytCarOwnerQueryPhone implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 824656352795345316L;
	private Long id;
	private Long userId;
	private Long tsId;
	private Long tsSrcId;
	private Long carUserId;
	private String carLoginPhone;
	private Date ctime;

	

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "ts_id")
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "ts_src_id")
	public Long getTsSrcId() {
		return this.tsSrcId;
	}

	public void setTsSrcId(Long tsSrcId) {
		this.tsSrcId = tsSrcId;
	}

	@Column(name = "car_user_id")
	public Long getCarUserId() {
		return this.carUserId;
	}

	public void setCarUserId(Long carUserId) {
		this.carUserId = carUserId;
	}

	@Column(name = "car_login_phone")
	public String getCarLoginPhone() {
		return this.carLoginPhone;
	}

	public void setCarLoginPhone(String carLoginPhone) {
		this.carLoginPhone = carLoginPhone;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

}