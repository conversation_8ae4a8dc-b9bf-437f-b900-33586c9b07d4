package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytMessage entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_message")
public class TytMessage implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6636614831218406549L;
	// Fields

	private Long id;
	private String remarks;
	private String title;
	private String type;
	private String icon;
	private String photo;
	private String summary;
	private String details;
	private String detailsContent;
	private String detailsUrl;
	private String sendStatus;
	private Date sendTime;
	private Date endTime;
	private Date ctime;
	private Date mtime;
	private String status;
	private String iconType;
	
	private String searchExplain;
	private String searchStr;
	private String sendRange;
	
	private String msgType;
	// Constructors

	/** default constructor */
	public TytMessage() {
	}

	/** minimal constructor */
	public TytMessage(String title, String type, String details,
			String sendStatus, Date ctime, String status) {
		this.title = title;
		this.type = type;
		this.details = details;
		this.sendStatus = sendStatus;
		this.ctime = ctime;
		this.status = status;
	}

	/** full constructor */
	public TytMessage(String remarks, String title, String type, String icon,
			String photo, String summary, String details,
			String detailsContent, String detailsUrl, String sendStatus,
			Date sendTime, Date endTime, Date ctime, Date mtime, String status) {
		this.remarks = remarks;
		this.title = title;
		this.type = type;
		this.icon = icon;
		this.photo = photo;
		this.summary = summary;
		this.details = details;
		this.detailsContent = detailsContent;
		this.detailsUrl = detailsUrl;
		this.sendStatus = sendStatus;
		this.sendTime = sendTime;
		this.endTime = endTime;
		this.ctime = ctime;
		this.mtime = mtime;
		this.status = status;
	}

	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "remarks")
	public String getRemarks() {
		return this.remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	@Column(name = "title")
	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Column(name = "type")
	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "icon")
	public String getIcon() {
		return this.icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	@Column(name = "photo")
	public String getPhoto() {
		return this.photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	@Column(name = "summary")
	public String getSummary() {
		return this.summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	@Column(name = "details")
	public String getDetails() {
		return this.details;
	}

	public void setDetails(String details) {
		this.details = details;
	}

	@Column(name = "details_content")
	public String getDetailsContent() {
		return this.detailsContent;
	}

	public void setDetailsContent(String detailsContent) {
		this.detailsContent = detailsContent;
	}

	@Column(name = "details_url")
	public String getDetailsUrl() {
		return this.detailsUrl;
	}

	public void setDetailsUrl(String detailsUrl) {
		this.detailsUrl = detailsUrl;
	}

	@Column(name = "send_status")
	public String getSendStatus() {
		return this.sendStatus;
	}

	public void setSendStatus(String sendStatus) {
		this.sendStatus = sendStatus;
	}

	@Column(name = "send_time")
	public Date getSendTime() {
		return this.sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	@Column(name = "end_time")
	public Date getEndTime() {
		return this.endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name = "status")
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	@Column(name = "icon_type")
	public String getIconType() {
		return iconType;
	}

	public void setIconType(String iconType) {
		this.iconType = iconType;
	}
	@Column(name = "search_explain")
	public String getSearchExplain() {
		return searchExplain;
	}

	public void setSearchExplain(String searchExplain) {
		this.searchExplain = searchExplain;
	}
	@Column(name = "search_str")
	public String getSearchStr() {
		return searchStr;
	}

	public void setSearchStr(String searchStr) {
		this.searchStr = searchStr;
	}
	@Column(name = "send_range")
	public String getSendRange() {
		return sendRange;
	}

	public void setSendRange(String sendRange) {
		this.sendRange = sendRange;
	}
	@Column(name = "msg_type")
	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

}