package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2018年3月5日下午1:44:47
 * @description
 */
@Entity
@Table(name = "tyt_url_limit", catalog = "tyt")
public class TytUrlLimit implements java.io.Serializable {
	private static final long serialVersionUID = 1415272820346654063L;

	private Long id;
	private String url;
	private int limtiTime;
	private int limtiFrequency;
	private byte serverType;
	private byte status;
	private Date ctime;
	private Date utime;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "url", nullable = false, length = 64)
	public String getUrl() {
		return this.url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Column(name = "limti_time", nullable = false)
	public int getLimtiTime() {
		return this.limtiTime;
	}

	public void setLimtiTime(int limtiTime) {
		this.limtiTime = limtiTime;
	}

	@Column(name = "limti_frequency", nullable = false)
	public int getLimtiFrequency() {
		return this.limtiFrequency;
	}

	public void setLimtiFrequency(int limtiFrequency) {
		this.limtiFrequency = limtiFrequency;
	}

	@Column(name = "server_type", nullable = false)
	public byte getServerType() {
		return this.serverType;
	}

	public void setServerType(byte serverType) {
		this.serverType = serverType;
	}

	@Column(name = "status", nullable = false)
	public byte getStatus() {
		return this.status;
	}

	public void setStatus(byte status) {
		this.status = status;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime", nullable = false, length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
