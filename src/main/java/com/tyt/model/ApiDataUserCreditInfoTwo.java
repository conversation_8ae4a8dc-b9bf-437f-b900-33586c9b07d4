package com.tyt.model;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 表名：api_data_user_credit_info_two
 */
@Data
@Entity
@Table(name = "api_data_user_credit_info_two", catalog = "tyt_recommend")
public class ApiDataUserCreditInfoTwo implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -5514356951408660297L;

    /**
     * 用户id
     */
    @Id
    @Column(name = "user_id")
    private Long userId;

    /**
     * 身份一级分类  直接存中文身份名称
     */
    @Column(name = "user_identity_type1")
    private String userIdentityType1;

    /**
     * 是否黑名单 0：不是 1：是
     */
    @Column(name = "is_blacklist_user")
    private Integer isBlacklistUser;

    /**
     * 诈骗记录的次数
     */
    @Column(name = "fraud_count")
    private Long fraudCount;

    /**
     * 信息费交易次数
     */
    @Column(name = "transport_information_payment_count")
    private Long transportInformationPaymentCount;

    /**
     * 尊享VIP会员天数
     */
    @Column(name = "vip_growth_days")
    private Long vipGrowthDays;

    /**
     * 货方历史发货量
     */
    @Column(name = "transport_145_count")
    private Long transport145Count;

    /**
     * 货方历史成交量
     */
    @Column(name = "transport_succ_count")
    private Long transportSuccCount;

    /**
     * 数据更新日期
     */
    @Column(name = "up_date")
    private Date upDate;

    /**
     * 发货最终得分
     */
    @Column(name = "transport_score")
    private String transportScore;

    /**
     * 高中低评级
     */
    @Column(name = "ts_rank")
    private String tsRank;

    /**
     * 身份认证得分_new
     */
    @Column(name = "identity_status_score")
    private Integer identityStatusScore;

    /**
     * 企业认证得分_new
     */
    @Column(name = "enterprise_auth_status_score")
    private Integer enterpriseAuthStatusScore;

    /**
     * 发货量得分_new
     */
    @Column(name = "trans_score")
    private BigDecimal transScore;

    /**
     * 拨打得分_new
     */
    @Column(name = "boda_score")
    private BigDecimal bodaScore;

    /**
     * 订单量得分_new
     */
    @Column(name = "orders_score")
    private Long ordersScore;

    /**
     * 履约率得分_new
     */
    @Column(name = "lvyuelv_score")
    private BigDecimal lvyuelvScore;

    /**
     * 总得分_new
     */
    @Column(name = "total_score")
    private BigDecimal totalScore;

    /**
     * 信用分等级_new
     */
    @Column(name = "rank_level")
    private Integer rankLevel;

    /**
     * 距下一等级相差人数（货）
     */
    @Column(name = "last_level_gap")
    private Integer lastLevelGap;

    /**
     * 车方身份认证得分_new
     */
    @Column(name = "car_identity_status_score")
    private Integer carIdentityStatusScore;

    /**
     * 车方企业认证得分_new
     */
    @Column(name = "car_enterprise_auth_status_score")
    private Integer carEnterpriseAuthStatusScore;

    /**
     * 车方车辆认证数量得分_new
     */
    @Column(name = "car_identity_numbers_score")
    private Integer carIdentityNumbersScore;

    /**
     * 车方历史履约得分_new
     */
    @Column(name = "car_history_lvyue_score")
    private Integer carHistoryLvyueScore;

    /**
     * 车方近两个月履约得分_new
     */
    @Column(name = "car_recent_two_months_lvyue_score")
    private Integer carRecentTwoMonthsLvyueScore;

    /**
     * 车方服务分_new
     */
    @Column(name = "car_total_server_score")
    private Integer carTotalServerScore;

    /**
     * 车方等级_new
     */
    @Column(name = "car_server_rank_score")
    private Integer carServerRankScore;

    /**
     * 车方尊享VIP会员天数
     */
    @Column(name = "car_vip_growth_days")
    private Long carVipGrowthDays;

    /**
     * 车方信息费交易次数
     */
    @Column(name = "car_transport_information_payment_count")
    private Long carTransportInformationPaymentCount;

    /**
     * 上周信用分等级_new
     */
    @Column(name = "last_week_rank_level")
    private Integer lastWeekRankLevel;

    /**
     * 投诉百分比
     */
    @Column(name = "complain_rate")
    private BigDecimal complainRate;

    /**
     * 履约量得分
     */
    @Column(name = "performance_num_score")
    private BigDecimal performanceNumScore;

    /**
     * 成交率得分
     */
    @Column(name = "closing_ratio_score")
    private BigDecimal closingRatioScore;

    /**
     * 取消率得分
     */
    @Column(name = "cancel_ratio_score")
    private BigDecimal cancelRatioScore;

    /**
     * 有责客诉量得分
     */
    @Column(name = "duty_complaint_num_score")
    private BigDecimal dutyComplaintNumScore;

    /**
     * 任务分无投诉
     */
    @Column(name = "task_no_complaint_score")
    private BigDecimal taskNoComplaintScore;

    /**
     * 任务分无取消
     */
    @Column(name = "task_no_cancel_score")
    private BigDecimal taskNoCancelScore;

    /**
     * 重货率得分
     */
    @Column(name = "duplicate_goods_ratio_score")
    private BigDecimal duplicateGoodsRatioScore;

    /**
     * 用户标签名称
     */
    @Column(name = "user_label_text")
    private String userLabelText;

    /**
     * 用户标签id，0:无标签，1:服务好，2:客诉多
     */
    @Column(name = "user_label_icon")
    private Integer userLabelIcon;

    /**
     * 上期履约量得分
     */
    @Column(name = "previous_issue_performance_num_score")
    private BigDecimal previousIssuePerformanceNumScore;

    /**
     * 上期成交率得分
     */
    @Column(name = "previous_issue_closing_ratio_score")
    private BigDecimal previousIssueClosingRatioScore;

    /**
     * 上期取消率得分
     */
    @Column(name = "previous_issue_cancel_ratio_score")
    private BigDecimal previousIssueCancelRatioScore;

    /**
     * 上期有责客诉量得分
     */
    @Column(name = "previous_issue_duty_complaint_num_score")
    private BigDecimal previousIssueDutyComplaintNumScore;

    /**
     * 上期任务分无投诉
     */
    @Column(name = "previous_issue_task_no_complaint_score")
    private BigDecimal previousIssueTaskNoComplaintScore;

    /**
     * 上期任务分无取消
     */
    @Column(name = "previous_issue_task_no_cancel_score")
    private BigDecimal previousIssueTaskNoCancelScore;

    /**
     * 上期重货率得分
     */
    @Column(name = "previous_issue_duplicate_goods_ratio_score")
    private BigDecimal previousIssueDuplicateGoodsRatioScore;

    /**
     * 上期信用得分
     */
    @Column(name = "previous_issue_credit_score")
    private BigDecimal previousIssueCreditScore;

    /**
     * 上期信用等级，0或空:无信用等级，1:D,2:C,3:B,4:A,5:S
     */
    @Column(name = "previous_issue_rank_level")
    private Integer previousIssueRankLevel;

    /**
     * 车方信用等级
     */
    @Column(name = "car_credit_rank_level")
    private String carCreditRankLevel;

    /**
     * 车方信用等级类型 1:运点值计算  2：购买会员  3：会员首次登录
     */
    @Column(name = "car_credit_rank_leve_type")
    private Integer carCreditRankLeveType;

    /**
     * 车方运点值
     */
    @Getter
    @Setter
    @Column(name = "car_credit_score")
    private BigDecimal carCreditScore;
    /**
     * 车主排名
     */
    @Getter
    @Setter
    @Column(name = "car_rank_num")
    private Integer carRankNum;
    /**
     * 距下一级相差的人数
     */
    @Getter
    @Setter
    @Column(name = "car_last_level_gap")
    private Integer carLastLevelGap;

    /**
     * 车方上月信用等级
     */
    @Column(name = "car_last_month_rank_level")
    private String carLastMonthRankLevel;

    /**
     * 货方完单进度
     */
    @Column(name = "goods_cur_order_num")
    private Integer goodsCurOrderNum;
}
