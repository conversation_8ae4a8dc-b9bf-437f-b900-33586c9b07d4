package com.tyt.model;

import javax.persistence.*;
import java.sql.Timestamp;

@Entity
@Table(name = "practice_the_car", schema = "tyt", catalog = "")
public class PracticeTheCar implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    //id
    private Long id;
    //发布人usreID
    private Long userId;
    //车主user_id
    private Long payUserId;
    //车主真实姓名
    private String carTrueName;
    //车主昵称
    private String payUseName;
    //车主注册账号
    private String payCellPhone;
    //车主联系电话
    private String payLinkPhone;
    //用户身份认证标志0未认证1通过2认证中3认证失败
    private Integer identityAuth;
    //交易次数
    private Integer tradeSuccessfully;
    //创建时间
    private Timestamp ctime;
    //修改时间
    private Timestamp mtime;
    //是否删除 0不删除 1已删除
    private Integer remove;

    @Id
    @GeneratedValue
    @Column(name = "id", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "user_id", nullable = true)
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "pay_user_id", nullable = true)
    public Long getPayUserId() {
        return payUserId;
    }

    public void setPayUserId(Long payUserId) {
        this.payUserId = payUserId;
    }

    @Basic
    @Column(name = "car_true_name", nullable = true)
    public String getCarTrueName() {
        return carTrueName;
    }

    public void setCarTrueName(String carTrueName) {
        this.carTrueName = carTrueName;
    }

    @Basic
    @Column(name = "pay_user_name", nullable = true)
    public String getPayUseName() {
        return payUseName;
    }

    public void setPayUseName(String payUseName) {
        this.payUseName = payUseName;
    }

    @Basic
    @Column(name = "pay_cell_phone", nullable = true)
    public String getPayCellPhone() {
        return payCellPhone;
    }

    public void setPayCellPhone(String payCellPhone) {
        this.payCellPhone = payCellPhone;
    }

    @Basic
    @Column(name = "pay_link_phone", nullable = true)
    public String getPayLinkPhone() {
        return payLinkPhone;
    }

    public void setPayLinkPhone(String payLinkPhone) {
        this.payLinkPhone = payLinkPhone;
    }

    @Basic
    @Column(name = "trade_successfully", nullable = true)
    public Integer getTradeSuccessfully() {
        return tradeSuccessfully;
    }

    public void setTradeSuccessfully(Integer tradeSuccessfully) {
        this.tradeSuccessfully = tradeSuccessfully;
    }

    @Basic
    @Column(name = "ctime", nullable = true)
    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "mtime", nullable = true)
    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Basic
    @Column(name = "identity_status", nullable = true)
    public Integer getIdentityAuth() {
        return identityAuth;
    }

    public void setIdentityAuth(Integer identityAuth) {
        this.identityAuth = identityAuth;
    }

    @Basic
    @Column(name = "remove", nullable = true)
    public Integer getRemove() {
        return remove;
    }

    public void setRemove(Integer remove) {
        this.remove = remove;
    }
}
