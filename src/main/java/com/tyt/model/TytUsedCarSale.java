package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "tyt_used_car_sale")
public class TytUsedCarSale implements Serializable {
    private static final long serialVersionUID = -714894800054548025L;
    /**
     * id
     */
    private Long id;

    /**
     * 车头标题
     */
    private String headTitle;

    /**
     * 发布人id
     */
    private Long userId;

    /**
     * 发布人姓名
     */
    private String userName;

    /**
     * 售车人姓名
     */
    private String saleName;

    /**
     * 售车人手机号
     */
    private String saleCellPhone;

    /**
     * 出厂时间
     */
    private Date headFactoryTime;

    /**
     * 上牌时间
     */
    private Date headCheckInTime;

    /**
     * 车头品牌
     */
    private String headBrand;

    /**
     * 车头型号
     */
    private String headModel;

    /**
     * 马力
     */
    private BigDecimal headHorsepower;

    /**
     * 驱动形式
     */
    private String headDriveStyle;

    /**
     * 速比
     */
    private String headSpeedRatio;

    /**
     * 排放标准
     */
    private String headDischargeStandard;

    /**
     * 驾驶室
     */
    private String headCab;

    /**
     * 行车公里数(公里)
     */
    private BigDecimal headDriveKilometers;

    /**
     * 是否有保险 1.是 2.否
     */
    private Short headInsurance;

    /**
     * 车头车源所在地省
     */
    private String headSourceProvince;

    /**
     * 板车种类
     */
    private String tailKind;

    /**
     * 平台长度(米)
     */
    private BigDecimal tailPlatformLength;

    /**
     * 货台面离地高度(厘米)
     */
    private BigDecimal tailMesaToGround;

    /**
     * 板车自重(吨)
     */
    private BigDecimal tailDeadWeight;

    /**
     * 保货吨位(吨)
     */
    private BigDecimal tailLoad;

    /**
     * 爬梯样式
     */
    private String tailLadderStyle;

    /**
     * 车轴品牌
     */
    private String tailAxleBrand;

    /**
     * 是否带抽拉 1.是 2.否
     */
    private Short tailHaveDrawing;
    /**
     * 板车上牌日期
     */
    private Date tailCheckInTime;

    /**
     * 板车车龄（年）
     */
    private BigDecimal tailAge;

    /**
     * 车头车源所在地市
     */
    private String headSourceCity;

    /**
     * 车头车源所在地区
     */
    private String headSourceArea;

    /**
     * 车头车源所在地详细地址
     */
    private String headSourceAddress;

    /**
     * 车头车源所在地全地址
     */
    private String headSourceFullAddress;

    /**
     * 板车品牌
     */
    private String tailBrand;

    /**
     * 板车长度（米）
     */
    private BigDecimal tailLength;

    /**
     * 板车类型 （tyt_source表tail_car_type）
     */
    private String tailType;

    /**
     * 板车车源所在地省
     */
    private String tailSourceProvince;

    /**
     * 列表展示图缩略图地址
     */
    private String firstPicUrl;

    /**
     * 发布状态 1.上架 2.下架 3.草稿
     */
    private Short postStatus;

    /**
     * 审核状态 1.审核中 2.审核通过 3.审核失败
     */
    private Short auditStatus;

    /**
     * 信息来源 1.app提交 2.后台提交
     */
    private Short infoSource;

    /**
     * 创建时间（提交时间）
     */
    private Date ctime;

    /**
     * 后台发布人id
     */
    private Long createId;

    /**
     * 后台发布人姓名
     */
    private String createName;

    /**
     * 审核人id
     */
    private Long auditId;

    /**
     * 审核人姓名
     */
    private String auditName;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核失败原因
     */
    private String auditFail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改时间
     */
    private Date utime;

    /**
     * 板车车源所在地市
     */
    private String tailSourceCity;

    /**
     * 板车车源所在地区
     */
    private String tailSourceArea;

    /**
     * 板车车头车源所在地详细地址
     */
    private String tailSourceAddress;

    /**
     * 板车车头车源所在地全地址
     */
    private String tailSourceFullAddress;

    /**
     * 是否可拆开售卖 1.是 2.否
     */
    private Short isBreakSale;

    /**
     * 信息类型 1.仅车头 2.仅挂车 3.一主一挂
     */
    private Short infoType;

    private String tailTitle;

    private Short headIsSale;
    private Short tailIsSale;
    private Date headSaleTime;
    private Date tailSaleTime;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * id
     * @return id id
     */
    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    /**
     * id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 标题
     * @return title 标题
     */
    @Column(name = "head_title")
    public String getHeadTitle() {
        return headTitle;
    }

    public void setHeadTitle(String headTitle) {
        this.headTitle = headTitle;
    }

    /**
     * 发布人id
     * @return user_id 发布人id
     */
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    /**
     * 发布人id
     * @param userId 发布人id
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * 发布人姓名
     * @return user_name 发布人姓名
     */
    @Column(name = "user_name")
    public String getUserName() {
        return userName;
    }

    /**
     * 发布人姓名
     * @param userName 发布人姓名
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 售车人姓名
     * @return sale_name 售车人姓名
     */
    @Column(name = "sale_name")
    public String getSaleName() {
        return saleName;
    }

    /**
     * 售车人姓名
     * @param saleName 售车人姓名
     */
    public void setSaleName(String saleName) {
        this.saleName = saleName;
    }

    /**
     * 售车人手机号
     * @return sale_cell_phone 售车人手机号
     */
    @Column(name = "sale_cell_phone")
    public String getSaleCellPhone() {
        return saleCellPhone;
    }

    /**
     * 售车人手机号
     * @param saleCellPhone 售车人手机号
     */
    public void setSaleCellPhone(String saleCellPhone) {
        this.saleCellPhone = saleCellPhone;
    }

    /**
     * 出厂时间
     * @return head_factory_time 出厂时间
     */
    @Column(name = "head_factory_time")
    public Date getHeadFactoryTime() {
        return headFactoryTime;
    }

    /**
     * 出厂时间
     * @param headFactoryTime 出厂时间
     */
    public void setHeadFactoryTime(Date headFactoryTime) {
        this.headFactoryTime = headFactoryTime;
    }

    /**
     * 上牌时间
     * @return head_check_in_time 上牌时间
     */
    @Column(name = "head_check_in_time")
    public Date getHeadCheckInTime() {
        return headCheckInTime;
    }

    /**
     * 上牌时间
     * @param headCheckInTime 上牌时间
     */
    public void setHeadCheckInTime(Date headCheckInTime) {
        this.headCheckInTime = headCheckInTime;
    }

    /**
     * 车头品牌
     * @return head_brand 车头品牌
     */
    @Column(name = "head_brand")
    public String getHeadBrand() {
        return headBrand;
    }

    /**
     * 车头品牌
     * @param headBrand 车头品牌
     */
    public void setHeadBrand(String headBrand) {
        this.headBrand = headBrand;
    }


    /**
     * 车头型号
     * @return head_model 车头型号
     */
    @Column(name = "head_model")
    public String getHeadModel() {
        return headModel;
    }

    /**
     * 车头型号
     * @param headModel 车头型号
     */
    public void setHeadModel(String headModel) {
        this.headModel = headModel;
    }

    /**
     * 马力
     * @return head_horsepower 马力
     */
    @Column(name = "head_horsepower")
    public BigDecimal getHeadHorsepower() {
        return headHorsepower;
    }

    /**
     * 马力
     * @param headHorsepower 马力
     */
    public void setHeadHorsepower(BigDecimal headHorsepower) {
        this.headHorsepower = headHorsepower;
    }

    /**
     * 驱动形式
     * @return head_drive_style 驱动形式
     */
    @Column(name = "head_drive_style")
    public String getHeadDriveStyle() {
        return headDriveStyle;
    }

    /**
     * 驱动形式
     * @param headDriveStyle 驱动形式
     */
    public void setHeadDriveStyle(String headDriveStyle) {
        this.headDriveStyle = headDriveStyle;
    }

    /**
     * 速比
     * @return head_speed_ratio 速比
     */
    @Column(name = "head_speed_ratio")
    public String getHeadSpeedRatio() {
        return headSpeedRatio;
    }

    /**
     * 速比
     * @param headSpeedRatio 速比
     */
    public void setHeadSpeedRatio(String headSpeedRatio) {
        this.headSpeedRatio = headSpeedRatio;
    }

    /**
     * 排放标准
     * @return head_discharge_standard 排放标准
     */
    @Column(name = "head_discharge_standard")
    public String getHeadDischargeStandard() {
        return headDischargeStandard;
    }

    /**
     * 排放标准
     * @param headDischargeStandard 排放标准
     */
    public void setHeadDischargeStandard(String headDischargeStandard) {
        this.headDischargeStandard = headDischargeStandard;
    }

    /**
     * 驾驶室
     * @return head_cab 驾驶室
     */
    @Column(name = "head_cab")
    public String getHeadCab() {
        return headCab;
    }

    /**
     * 驾驶室
     * @param headCab 驾驶室
     */
    public void setHeadCab(String headCab) {
        this.headCab = headCab;
    }

    /**
     * 行车公里数(公里)
     * @return head_drive_kilometers 行车公里数(公里)
     */
    @Column(name = "head_drive_kilometers")
    public BigDecimal getHeadDriveKilometers() {
        return headDriveKilometers;
    }

    /**
     * 行车公里数(公里)
     * @param headDriveKilometers 行车公里数(公里)
     */
    public void setHeadDriveKilometers(BigDecimal headDriveKilometers) {
        this.headDriveKilometers = headDriveKilometers;
    }

    /**
     * 是否有保险 1.是 2.否
     * @return head_insurance 是否有保险 1.是 2.否
     */
    @Column(name = "head_insurance")
    public Short getHeadInsurance() {
        return headInsurance;
    }

    /**
     * 是否有保险 1.是 2.否
     * @param headInsurance 是否有保险 1.是 2.否
     */
    public void setHeadInsurance(Short headInsurance) {
        this.headInsurance = headInsurance;
    }

    /**
     * 车头车源所在地省
     * @return head_source_province 车头车源所在地省
     */
    @Column(name = "head_source_province")
    public String getHeadSourceProvince() {
        return headSourceProvince;
    }

    /**
     * 车头车源所在地省
     * @param headSourceProvince 车头车源所在地省
     */
    public void setHeadSourceProvince(String headSourceProvince) {
        this.headSourceProvince = headSourceProvince;
    }

    /**
     * 板车种类
     * @return tail_kind 板车种类
     */
    @Column(name = "tail_kind")
    public String getTailKind() {
        return tailKind;
    }

    /**
     * 板车种类
     * @param tailKind 板车种类
     */
    public void setTailKind(String tailKind) {
        this.tailKind = tailKind;
    }

    /**
     * 平台长度(米)
     * @return tail_platform_length 平台长度(米)
     */
    @Column(name = "tail_platform_length")
    public BigDecimal getTailPlatformLength() {
        return tailPlatformLength;
    }

    /**
     * 平台长度(米)
     * @param tailPlatformLength 平台长度(米)
     */
    public void setTailPlatformLength(BigDecimal tailPlatformLength) {
        this.tailPlatformLength = tailPlatformLength;
    }

    /**
     * 货台面离地高度(厘米)
     * @return tail_mesa_to_ground 货台面离地高度(厘米)
     */
    @Column(name = "tail_mesa_to_ground")
    public BigDecimal getTailMesaToGround() {
        return tailMesaToGround;
    }

    /**
     * 货台面离地高度(厘米)
     * @param tailMesaToGround 货台面离地高度(厘米)
     */
    public void setTailMesaToGround(BigDecimal tailMesaToGround) {
        this.tailMesaToGround = tailMesaToGround;
    }

    /**
     * 板车自重(吨)
     * @return tail_dead_weight 板车自重(吨)
     */
    @Column(name = "tail_dead_weight")
    public BigDecimal getTailDeadWeight() {
        return tailDeadWeight;
    }

    /**
     * 板车自重(吨)
     * @param tailDeadWeight 板车自重(吨)
     */
    public void setTailDeadWeight(BigDecimal tailDeadWeight) {
        this.tailDeadWeight = tailDeadWeight;
    }

    /**
     * 保货吨位(吨)
     * @return tail_load 保货吨位(吨)
     */
    @Column(name = "tail_load")
    public BigDecimal getTailLoad() {
        return tailLoad;
    }

    /**
     * 保货吨位(吨)
     * @param tailLoad 保货吨位(吨)
     */
    public void setTailLoad(BigDecimal tailLoad) {
        this.tailLoad = tailLoad;
    }

    /**
     * 爬梯样式
     * @return tail_ladder_style 爬梯样式
     */
    @Column(name = "tail_ladder_style")
    public String getTailLadderStyle() {
        return tailLadderStyle;
    }

    /**
     * 爬梯样式
     * @param tailLadderStyle 爬梯样式
     */
    public void setTailLadderStyle(String tailLadderStyle) {
        this.tailLadderStyle = tailLadderStyle;
    }

    /**
     * 车轴品牌
     * @return tail_axle_brand 车轴品牌
     */
    @Column(name = "tail_axle_brand")
    public String getTailAxleBrand() {
        return tailAxleBrand;
    }

    /**
     * 车轴品牌
     * @param tailAxleBrand 车轴品牌
     */
    public void setTailAxleBrand(String tailAxleBrand) {
        this.tailAxleBrand = tailAxleBrand;
    }

    /**
     * 是否带抽拉 1.是 2.否
     * @return tail_have_drawing 是否带抽拉 1.是 2.否
     */
    @Column(name = "tail_have_drawing")
    public Short getTailHaveDrawing() {
        return tailHaveDrawing;
    }

    /**
     * 是否带抽拉 1.是 2.否
     * @param tailHaveDrawing 是否带抽拉 1.是 2.否
     */
    public void setTailHaveDrawing(Short tailHaveDrawing) {
        this.tailHaveDrawing = tailHaveDrawing;
    }

    /**
     * 板车车龄（年）
     * @return tail_age 板车车龄（年）
     */
    @Column(name = "tail_age")
    public BigDecimal getTailAge() {
        return tailAge;
    }

    /**
     * 板车车龄（年）
     * @param tailAge 板车车龄（年）
     */
    public void setTailAge(BigDecimal tailAge) {
        this.tailAge = tailAge;
    }

    /**
     * 车头车源所在地市
     * @return head_source_city 车头车源所在地市
     */
    @Column(name = "head_source_city")
    public String getHeadSourceCity() {
        return headSourceCity;
    }

    /**
     * 车头车源所在地市
     * @param headSourceCity 车头车源所在地市
     */
    public void setHeadSourceCity(String headSourceCity) {
        this.headSourceCity = headSourceCity;
    }

    /**
     * 车头车源所在地区
     * @return head_source_area 车头车源所在地区
     */
    @Column(name = "head_source_area")
    public String getHeadSourceArea() {
        return headSourceArea;
    }

    /**
     * 车头车源所在地区
     * @param headSourceArea 车头车源所在地区
     */
    public void setHeadSourceArea(String headSourceArea) {
        this.headSourceArea = headSourceArea;
    }

    /**
     * 车头车源所在地详细地址
     * @return head_source_address 车头车源所在地详细地址
     */
    @Column(name = "head_source_address")
    public String getHeadSourceAddress() {
        return headSourceAddress;
    }

    /**
     * 车头车源所在地详细地址
     * @param headSourceAddress 车头车源所在地详细地址
     */
    public void setHeadSourceAddress(String headSourceAddress) {
        this.headSourceAddress = headSourceAddress;
    }

    /**
     * 车头车源所在地全地址
     * @return head_source_full_address 车头车源所在地全地址
     */
    @Column(name = "head_source_full_address")
    public String getHeadSourceFullAddress() {
        return headSourceFullAddress;
    }

    /**
     * 车头车源所在地全地址
     * @param headSourceFullAddress 车头车源所在地全地址
     */
    public void setHeadSourceFullAddress(String headSourceFullAddress) {
        this.headSourceFullAddress = headSourceFullAddress;
    }

    /**
     * 板车品牌
     * @return tail_brand 板车品牌
     */
    @Column(name = "tail_brand")
    public String getTailBrand() {
        return tailBrand;
    }

    /**
     * 板车品牌
     * @param tailBrand 板车品牌
     */
    public void setTailBrand(String tailBrand) {
        this.tailBrand = tailBrand;
    }

    /**
     * 板车长度（米）
     * @return tail_length 板车长度（米）
     */
    @Column(name = "tail_length")
    public BigDecimal getTailLength() {
        return tailLength;
    }

    /**
     * 板车长度（米）
     * @param tailLength 板车长度（米）
     */
    public void setTailLength(BigDecimal tailLength) {
        this.tailLength = tailLength;
    }

    /**
     * 板车类型 （tyt_source表tail_car_type）
     * @return tail_type 板车类型 （tyt_source表tail_car_type）
     */
    @Column(name = "tail_type")
    public String getTailType() {
        return tailType;
    }

    /**
     * 板车类型 （tyt_source表tail_car_type）
     * @param tailType 板车类型 （tyt_source表tail_car_type）
     */
    public void setTailType(String tailType) {
        this.tailType = tailType;
    }

    /**
     * 板车车源所在地省
     * @return tail_source_province 板车车源所在地省
     */
    @Column(name = "tail_source_province")
    public String getTailSourceProvince() {
        return tailSourceProvince;
    }

    /**
     * 板车车源所在地省
     * @param tailSourceProvince 板车车源所在地省
     */
    public void setTailSourceProvince(String tailSourceProvince) {
        this.tailSourceProvince = tailSourceProvince;
    }

    /**
     * 列表展示图缩略图地址
     * @return first_pic_url 列表展示图缩略图地址
     */
    @Column(name = "first_pic_url")
    public String getFirstPicUrl() {
        return firstPicUrl;
    }

    /**
     * 列表展示图缩略图地址
     * @param firstPicUrl 列表展示图缩略图地址
     */
    public void setFirstPicUrl(String firstPicUrl) {
        this.firstPicUrl = firstPicUrl;
    }

    /**
     * 发布状态 1.上架 2.下架
     * @return post_status 发布状态 1.上架 2.下架
     */
    @Column(name = "post_status")
    public Short getPostStatus() {
        return postStatus;
    }

    /**
     * 发布状态 1.上架 2.下架
     * @param postStatus 发布状态 1.上架 2.下架
     */
    public void setPostStatus(Short postStatus) {
        this.postStatus = postStatus;
    }

    /**
     * 审核状态 1.审核中 2.审核通过 3.审核失败
     * @return audit_status 审核状态 1.审核中 2.审核通过 3.审核失败
     */
    @Column(name = "audit_status")
    public Short getAuditStatus() {
        return auditStatus;
    }

    /**
     * 审核状态 1.审核中 2.审核通过 3.审核失败
     * @param auditStatus 审核状态 1.审核中 2.审核通过 3.审核失败
     */
    public void setAuditStatus(Short auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * 信息来源 1.app提交 2.后台提交
     * @return info_source 信息来源 1.app提交 2.后台提交
     */
    @Column(name = "info_source")
    public Short getInfoSource() {
        return infoSource;
    }

    /**
     * 信息来源 1.app提交 2.后台提交
     * @param infoSource 信息来源 1.app提交 2.后台提交
     */
    public void setInfoSource(Short infoSource) {
        this.infoSource = infoSource;
    }

    /**
     * 创建时间（提交时间）
     * @return ctime 创建时间（提交时间）
     */
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    /**
     * 创建时间（提交时间）
     * @param ctime 创建时间（提交时间）
     */
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    /**
     * 后台发布人id
     * @return create_id 后台发布人id
     */
    @Column(name = "create_id")
    public Long getCreateId() {
        return createId;
    }

    /**
     * 后台发布人id
     * @param createId 后台发布人id
     */
    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    /**
     * 后台发布人姓名
     * @return create_name 后台发布人姓名
     */
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    /**
     * 后台发布人姓名
     * @param createName 后台发布人姓名
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 审核人id
     * @return audit_id 审核人id
     */
    @Column(name = "audit_id")
    public Long getAuditId() {
        return auditId;
    }

    /**
     * 审核人id
     * @param auditId 审核人id
     */
    public void setAuditId(Long auditId) {
        this.auditId = auditId;
    }

    /**
     * 审核人姓名
     * @return audit_name 审核人姓名
     */
    @Column(name = "audit_name")
    public String getAuditName() {
        return auditName;
    }

    /**
     * 审核人姓名
     * @param auditName 审核人姓名
     */
    public void setAuditName(String auditName) {
        this.auditName = auditName;
    }

    /**
     * 审核时间
     * @return audit_time 审核时间
     */
    @Column(name = "audit_time")
    public Date getAuditTime() {
        return auditTime;
    }

    /**
     * 审核时间
     * @param auditTime 审核时间
     */
    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    /**
     * 审核失败原因
     * @return audit_fail 审核失败原因
     */
    @Column(name = "audit_fail")
    public String getAuditFail() {
        return auditFail;
    }

    /**
     * 审核失败原因
     * @param auditFail 审核失败原因
     */
    public void setAuditFail(String auditFail) {
        this.auditFail = auditFail;
    }

    /**
     * 备注
     * @return remark 备注
     */
    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 修改时间
     * @return utime 修改时间
     */
    @Column(name = "utime")
    public Date getUtime() {
        return utime;
    }

    /**
     * 修改时间
     * @param utime 修改时间
     */
    public void setUtime(Date utime) {
        this.utime = utime;
    }

    /**
     * 板车车源所在地市
     * @return tail_source_city 板车车源所在地市
     */
    @Column(name = "tail_source_city")
    public String getTailSourceCity() {
        return tailSourceCity;
    }

    /**
     * 板车车源所在地市
     * @param tailSourceCity 板车车源所在地市
     */
    public void setTailSourceCity(String tailSourceCity) {
        this.tailSourceCity = tailSourceCity;
    }

    /**
     * 板车车源所在地区
     * @return tail_source_area 板车车源所在地区
     */
    @Column(name = "tail_source_area")
    public String getTailSourceArea() {
        return tailSourceArea;
    }

    /**
     * 板车车源所在地区
     * @param tailSourceArea 板车车源所在地区
     */
    public void setTailSourceArea(String tailSourceArea) {
        this.tailSourceArea = tailSourceArea;
    }

    /**
     * 板车车头车源所在地详细地址
     * @return tail_source_address 板车车头车源所在地详细地址
     */
    @Column(name = "tail_source_address")
    public String getTailSourceAddress() {
        return tailSourceAddress;
    }

    /**
     * 板车车头车源所在地详细地址
     * @param tailSourceAddress 板车车头车源所在地详细地址
     */
    public void setTailSourceAddress(String tailSourceAddress) {
        this.tailSourceAddress = tailSourceAddress;
    }

    /**
     * 板车车头车源所在地全地址
     * @return tail_source_full_address 板车车头车源所在地全地址
     */
    @Column(name = "tail_source_full_address")
    public String getTailSourceFullAddress() {
        return tailSourceFullAddress;
    }

    /**
     * 板车车头车源所在地全地址
     * @param tailSourceFullAddress 板车车头车源所在地全地址
     */
    public void setTailSourceFullAddress(String tailSourceFullAddress) {
        this.tailSourceFullAddress = tailSourceFullAddress;
    }

    /**
     * 是否可拆开售卖 1.是 2.否
     * @return is_break_sale 是否可拆开售卖 1.是 2.否
     */
    @Column(name = "is_break_sale")
    public Short getIsBreakSale() {
        return isBreakSale;
    }

    /**
     * 是否可拆开售卖 1.是 2.否
     * @param isBreakSale 是否可拆开售卖 1.是 2.否
     */
    public void setIsBreakSale(Short isBreakSale) {
        this.isBreakSale = isBreakSale;
    }

    /**
     * 信息类型 1.仅车头 2.仅挂车 3.一主一挂
     * @return info_type 信息类型 1.仅车头 2.仅挂车 3.一主一挂
     */
    @Column(name = "info_type")
    public Short getInfoType() {
        return infoType;
    }

    /**
     * 信息类型 1.仅车头 2.仅挂车 3.一主一挂
     * @param infoType 信息类型 1.仅车头 2.仅挂车 3.一主一挂
     */
    public void setInfoType(Short infoType) {
        this.infoType = infoType;
    }

    @Column(name = "tail_title")
    public String getTailTitle() {
        return tailTitle;
    }

    public void setTailTitle(String tailTitle) {
        this.tailTitle = tailTitle;
    }

    @Column(name = "head_is_sale")
    public Short getHeadIsSale() {
        return headIsSale;
    }

    public void setHeadIsSale(Short headIsSale) {
        this.headIsSale = headIsSale;
    }

    @Column(name = "tail_is_sale")
    public Short getTailIsSale() {
        return tailIsSale;
    }

    public void setTailIsSale(Short tailIsSale) {
        this.tailIsSale = tailIsSale;
    }

    @Column(name = "head_sale_time")
    public Date getHeadSaleTime() {
        return headSaleTime;
    }

    public void setHeadSaleTime(Date headSaleTime) {
        this.headSaleTime = headSaleTime;
    }

    @Column(name = "tail_sale_time")
    public Date getTailSaleTime() {
        return tailSaleTime;
    }

    public void setTailSaleTime(Date tailSaleTime) {
        this.tailSaleTime = tailSaleTime;
    }

    @Column(name = "tail_check_in_time")
    public Date getTailCheckInTime() {
        return tailCheckInTime;
    }

    public void setTailCheckInTime(Date tailCheckInTime) {
        this.tailCheckInTime = tailCheckInTime;
    }

    @Column(name = "publish_time")
    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    @Override
    public String toString() {
        return "TytUsedCarSale{" +
                "id=" + id +
                ", headTitle='" + headTitle + '\'' +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", saleName='" + saleName + '\'' +
                ", saleCellPhone='" + saleCellPhone + '\'' +
                ", headFactoryTime=" + headFactoryTime +
                ", headCheckInTime=" + headCheckInTime +
                ", headBrand='" + headBrand + '\'' +
                ", headModel='" + headModel + '\'' +
                ", headHorsepower=" + headHorsepower +
                ", headDriveStyle='" + headDriveStyle + '\'' +
                ", headSpeedRatio='" + headSpeedRatio + '\'' +
                ", headDischargeStandard='" + headDischargeStandard + '\'' +
                ", headCab='" + headCab + '\'' +
                ", headDriveKilometers=" + headDriveKilometers +
                ", headInsurance=" + headInsurance +
                ", headSourceProvince='" + headSourceProvince + '\'' +
                ", tailKind='" + tailKind + '\'' +
                ", tailPlatformLength=" + tailPlatformLength +
                ", tailMesaToGround=" + tailMesaToGround +
                ", tailDeadWeight=" + tailDeadWeight +
                ", tailLoad=" + tailLoad +
                ", tailLadderStyle='" + tailLadderStyle + '\'' +
                ", tailAxleBrand='" + tailAxleBrand + '\'' +
                ", tailHaveDrawing=" + tailHaveDrawing +
                ", tailCheckInTime=" + tailCheckInTime +
                ", tailAge=" + tailAge +
                ", headSourceCity='" + headSourceCity + '\'' +
                ", headSourceArea='" + headSourceArea + '\'' +
                ", headSourceAddress='" + headSourceAddress + '\'' +
                ", headSourceFullAddress='" + headSourceFullAddress + '\'' +
                ", tailBrand='" + tailBrand + '\'' +
                ", tailLength=" + tailLength +
                ", tailType='" + tailType + '\'' +
                ", tailSourceProvince='" + tailSourceProvince + '\'' +
                ", firstPicUrl='" + firstPicUrl + '\'' +
                ", postStatus=" + postStatus +
                ", auditStatus=" + auditStatus +
                ", infoSource=" + infoSource +
                ", ctime=" + ctime +
                ", createId=" + createId +
                ", createName='" + createName + '\'' +
                ", auditId=" + auditId +
                ", auditName='" + auditName + '\'' +
                ", auditTime=" + auditTime +
                ", auditFail='" + auditFail + '\'' +
                ", remark='" + remark + '\'' +
                ", utime=" + utime +
                ", tailSourceCity='" + tailSourceCity + '\'' +
                ", tailSourceArea='" + tailSourceArea + '\'' +
                ", tailSourceAddress='" + tailSourceAddress + '\'' +
                ", tailSourceFullAddress='" + tailSourceFullAddress + '\'' +
                ", isBreakSale=" + isBreakSale +
                ", infoType=" + infoType +
                ", tailTitle='" + tailTitle + '\'' +
                ", headIsSale=" + headIsSale +
                ", tailIsSale=" + tailIsSale +
                ", headSaleTime=" + headSaleTime +
                ", tailSaleTime=" + tailSaleTime +
                ", publishTime=" + publishTime +
                '}';
    }
}