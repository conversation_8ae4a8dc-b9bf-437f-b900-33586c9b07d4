package com.tyt.model;

import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 **/
@Entity
@Table(name = "draw_activity_info")
public class DrawActivityInfo {
    private Long id;
    private String activityName;
    private Date startTime;
    private Date endTime;
    private Long createUserId;
    private String createUserName;
    private Integer status;
    private Integer limitTimesType;
    private Integer limitTimes;
    private Long updateUserId;
    private String updateUserName;
    private Integer appType;
    private Date createTime;
    private Date updateTime;
    private Integer isDelete;

    /**
     * 用于标识进行哪种业务校验，为空只进行基础校验，不进行业务校验
     */
    private String bizCheckSign;

    /**
     * 定向用户抽奖活动状态枚举
     * 活动状态(1.有效 2.无效)
     */
    public enum DrawActivityInfoStatus{
        EFFECTIVE(1, "有效"),
        INVALID(2, "无效");

        private Integer status;
        private String msg;

        DrawActivityInfoStatus(Integer status, String msg) {
            this.status = status;
            this.msg = msg;
        }

        public Integer getStatus() {
            return status;
        }

        public String getMsg() {
            return msg;
        }
    }

    /**
     * 定向用户抽奖活动抽奖次数类型
     * 限制每人抽奖次数类型(1.总共 2.每天)
     */
    public enum  DrawActivityInfoLimitTimesType{
        BY_ALL(1, "总共"),
        BY_DAY(2, "按天");

        private Integer code;
        private String msg;

        DrawActivityInfoLimitTimesType(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Basic
    @Column(name = "start_time")
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Basic
    @Column(name = "end_time")
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Basic
    @Column(name = "create_user_id")
    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    @Basic
    @Column(name = "create_user_name")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @Basic
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "limit_times_type")
    public Integer getLimitTimesType() {
        return limitTimesType;
    }

    public void setLimitTimesType(Integer limitTimesType) {
        this.limitTimesType = limitTimesType;
    }

    @Basic
    @Column(name = "limit_times")
    public Integer getLimitTimes() {
        return limitTimes;
    }

    public void setLimitTimes(Integer limitTimes) {
        this.limitTimes = limitTimes;
    }

    @Basic
    @Column(name = "update_user_id")
    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    @Basic
    @Column(name = "update_user_name")
    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Basic
    @Column(name = "app_type")
    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    @Basic
    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Basic
    @Column(name = "is_delete")
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Basic
    @Column(name = "biz_check_sign")
    public String getBizCheckSign() {
        return bizCheckSign;
    }

    public void setBizCheckSign(String bizCheckSign) {
        this.bizCheckSign = bizCheckSign;
    }

}
















