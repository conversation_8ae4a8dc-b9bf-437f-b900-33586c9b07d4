package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * TytUserMessage entity. <AUTHOR>
@Entity
@Table(name = "tyt_user_staff")
public class TytUserStaff implements java.io.Serializable {

	private static final long serialVersionUID = -5250269083987336810L;

	private Long id;
	private Long userId;
	private String staffName;
	private String staffPhone;
	private Date createTime;
	private Date updateTime;


	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "staff_name")
	public String getStaffName() {
		return staffName;
	}

	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	@Column(name = "staff_phone")
	public String getStaffPhone() {
		return staffPhone;
	}

	public void setStaffPhone(String staffPhone) {
		this.staffPhone = staffPhone;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}
