package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name="tyt_stimulate_activity")
public class TytStimulateActivity {

    private Integer id;
    /**
     * 运营活动表id
     */
    private Long marketingActivityId;
    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型 1会员权益 2订单量
     */
    private Integer activityType;
    /**
     * 用户id
     */
    private Long userId;
    /**
     *  用户账户
     */
    private String userAccount;
    /**
     * 订单状态 0 运输中 1 已完成
     */
    private Integer orderStatus;
    /**
     * 订单编号
     */
    private Long orderNumber;

    /**
     * 运单号
     */
    private String tsOrderNo;
    /**
     * 活动状态 0:正常 1：刷单嫌疑 2：已确认刷单 3：已解除刷单
     */
    private Integer activityStatus;
    /**
     * 赠送天数
     */
    private Integer donateDays;
    /**
     * 撤回天数
     */
    private Integer repealDays;
    /**
     * 操作人
     */
    private String updateUserName;
    /**
     * 0:接单人与发货人为同一认证人 1:接单人与发货人认证同一企业 2:接单人与发货人认证同一辆车 3:1人同一天，接3单及以上，接单的车辆为同一车牌号 4:发货时间与接单时间间隔小于1分钟且确认付款时间间隔小于5分钟
     */
    private String suspectType;
    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;
    /**
     * 赠送商品支付订单号
     */
    private String goodsOrderId;

    /**
     * 定时任务扫描刷单标识 0：已扫描 1：未扫描
     */
    private Integer taskFlag;

    private Long carUserId;

    private String carUserAccount;

    /**
     * 用户刷单申诉状态
     */
    private Integer appealStatus;

    /**
     * 用户申诉证据上传
     */
    private Integer appealEvidence;



    /**
     * 用户刷单凭证描述
     */
    private String reason;


    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name="marketing_activity_id")
    public Long getMarketingActivityId() {
        return marketingActivityId;
    }

    public void setMarketingActivityId(Long marketingActivityId) {
        this.marketingActivityId = marketingActivityId;
    }

    @Column(name="activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Column(name="user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name="user_account")
    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    @Column(name="order_status")
    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    @Column(name="order_number")
    public Long getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Long orderNumber) {
        this.orderNumber = orderNumber;
    }

    @Column(name="activity_status")
    public Integer getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(Integer activityStatus) {
        this.activityStatus = activityStatus;
    }

    @Column(name="donate_days")
    public Integer getDonateDays() {
        return donateDays;
    }

    public void setDonateDays(Integer donateDays) {
        this.donateDays = donateDays;
    }

    @Column(name="repeal_days")
    public Integer getRepealDays() {
        return repealDays;
    }

    public void setRepealDays(Integer repealDays) {
        this.repealDays = repealDays;
    }

    @Column(name="update_user_name")
    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Column(name="suspect_type")
    public String getSuspectType() {
        return suspectType;
    }

    public void setSuspectType(String suspectType) {
        this.suspectType = suspectType;
    }

    @Column(name="create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Column(name = "goods_order_id")
    public String getGoodsOrderId() {
        return goodsOrderId;
    }

    public void setGoodsOrderId(String goodsOrderId) {
        this.goodsOrderId = goodsOrderId;
    }

    @Column(name = "activity_type")
    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    @Column(name = "ts_order_no")
    public String getTsOrderNo() {
        return tsOrderNo;
    }

    public void setTsOrderNo(String tsOrderNo) {
        this.tsOrderNo = tsOrderNo;
    }

    @Column(name = "task_flag")
    public Integer getTaskFlag() {
        return taskFlag;
    }

    public void setTaskFlag(Integer taskFlag) {
        this.taskFlag = taskFlag;
    }

    @Column(name = "car_user_id")
    public Long getCarUserId() {
        return carUserId;
    }

    public void setCarUserId(Long carUserId) {
        this.carUserId = carUserId;
    }

    @Column(name = "car_user_account")
    public String getCarUserAccount() {
        return carUserAccount;
    }

    public void setCarUserAccount(String carUserAccount) {
        this.carUserAccount = carUserAccount;
    }

    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "appeal_status")
    public Integer getAppealStatus() {
        return appealStatus;
    }

    public void setAppealStatus(Integer appealStatus) {
        this.appealStatus = appealStatus;
    }
    @Column(name = "appeal_evidence")
    public Integer getAppealEvidence() {
        return appealEvidence;
    }

    public void setAppealEvidence(Integer appealEvidence) {
        this.appealEvidence = appealEvidence;
    }



    @Column(name = "reason")
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }


}
