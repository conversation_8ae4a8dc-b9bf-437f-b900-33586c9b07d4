package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytCarNews entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_car_news")
public class TytCarNews implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1304340092122965334L;
	private Long id;
	private String abstracts;
	private Integer carTypeCode;
	private String carType;
	private String brandCode;
	private String brand;
	private Integer provinceCode;
	private String province;
	private Integer cityCode;
	private String city;
	private Integer countyCode;
	private String county;
	private String carVersion;
	private Double price;
	private String detailsPrice;
	private Integer pushLevel;
	private Integer isSpecial;
	private String specialUrl;
	private String pictureUrl;
	private String webPicUrl;
	private String factoryName;
	private String factoryAddress;
	private String factoryLinkman;
	private String factoryPhone1;
	private String factoryPhone2;
	private Date publishTime;
	private Integer status;
	private Long userId;
	private Date utime;
	private Date ctime;

	private String webSpecialUrl;
	
	private Integer sellRange;
	private Integer businessType;
	private Long clickAmount=0l;
	private Long callAmount=0l;
	private Integer displayStyle;
	private String pictureNewUrl1;
	private String pictureNewUrl2;
	private String pictureNewUrl3;
	
	
	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "abstracts", length = 100)
	public String getAbstracts() {
		return this.abstracts;
	}

	public void setAbstracts(String abstracts) {
		this.abstracts = abstracts;
	}

	@Column(name = "car_type_code")
	public Integer getCarTypeCode() {
		return this.carTypeCode;
	}

	public void setCarTypeCode(Integer carTypeCode) {
		this.carTypeCode = carTypeCode;
	}

	@Column(name = "car_type")
	public String getCarType() {
		return this.carType;
	}

	public void setCarType(String carType) {
		this.carType = carType;
	}

	@Column(name = "brand_code")
	public String getBrandCode() {
		return this.brandCode;
	}

	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}

	@Column(name = "brand")
	public String getBrand() {
		return this.brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	@Column(name = "province_code")
	public Integer getProvinceCode() {
		return this.provinceCode;
	}

	public void setProvinceCode(Integer provinceCode) {
		this.provinceCode = provinceCode;
	}

	@Column(name = "province")
	public String getProvince() {
		return this.province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Column(name = "city_code")
	public Integer getCityCode() {
		return this.cityCode;
	}

	public void setCityCode(Integer cityCode) {
		this.cityCode = cityCode;
	}

	@Column(name = "city")
	public String getCity() {
		return this.city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name = "county_code")
	public Integer getCountyCode() {
		return this.countyCode;
	}

	public void setCountyCode(Integer countyCode) {
		this.countyCode = countyCode;
	}

	@Column(name = "county")
	public String getCounty() {
		return this.county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	@Column(name = "car_version", length = 1000)
	public String getCarVersion() {
		return this.carVersion;
	}

	public void setCarVersion(String carVersion) {
		this.carVersion = carVersion;
	}

	@Column(name = "price")
	public Double getPrice() {
		return this.price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}

	@Column(name = "details_price", length = 100)
	public String getDetailsPrice() {
		return this.detailsPrice;
	}

	public void setDetailsPrice(String detailsPrice) {
		this.detailsPrice = detailsPrice;
	}

	@Column(name = "push_level")
	public Integer getPushLevel() {
		return this.pushLevel;
	}

	public void setPushLevel(Integer pushLevel) {
		this.pushLevel = pushLevel;
	}

	@Column(name = "is_special")
	public Integer getIsSpecial() {
		return this.isSpecial;
	}

	public void setIsSpecial(Integer isSpecial) {
		this.isSpecial = isSpecial;
	}

	@Column(name = "special_url", length = 200)
	public String getSpecialUrl() {
		return this.specialUrl;
	}

	public void setSpecialUrl(String specialUrl) {
		this.specialUrl = specialUrl;
	}

	@Column(name = "picture_url", length = 200)
	public String getPictureUrl() {
		return this.pictureUrl;
	}

	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}

	@Column(name = "factory_name", length = 80)
	public String getFactoryName() {
		return this.factoryName;
	}

	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}

	@Column(name = "factory_address", length = 200)
	public String getFactoryAddress() {
		return this.factoryAddress;
	}

	public void setFactoryAddress(String factoryAddress) {
		this.factoryAddress = factoryAddress;
	}

	@Column(name = "factory_linkman")
	public String getFactoryLinkman() {
		return this.factoryLinkman;
	}

	public void setFactoryLinkman(String factoryLinkman) {
		this.factoryLinkman = factoryLinkman;
	}

	@Column(name = "factory_phone1")
	public String getFactoryPhone1() {
		return this.factoryPhone1;
	}

	public void setFactoryPhone1(String factoryPhone1) {
		this.factoryPhone1 = factoryPhone1;
	}

	@Column(name = "factory_phone2")
	public String getFactoryPhone2() {
		return this.factoryPhone2;
	}

	public void setFactoryPhone2(String factoryPhone2) {
		this.factoryPhone2 = factoryPhone2;
	}

	@Column(name = "publish_time")
	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name = "web_pic_url")
	public String getWebPicUrl() {
		return webPicUrl;
	}

	public void setWebPicUrl(String webPicUrl) {
		this.webPicUrl = webPicUrl;
	}
	@Column(name = "sell_range")
	public Integer getSellRange() {
		return sellRange;
	}

	public void setSellRange(Integer sellRange) {
		this.sellRange = sellRange;
	}
	@Column(name = "business_type")
	public Integer getBusinessType() {
		return businessType;
	}

	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}
	@Column(name = "click_amount")
	public Long getClickAmount() {
		return clickAmount;
	}

	public void setClickAmount(Long clickAmount) {
		this.clickAmount = clickAmount;
	}
	@Column(name = "call_amount")
	public Long getCallAmount() {
		return callAmount;
	}

	public void setCallAmount(Long callAmount) {
		this.callAmount = callAmount;
	}
	@Column(name = "display_style")
	public Integer getDisplayStyle() {
		return displayStyle;
	}

	public void setDisplayStyle(Integer displayStyle) {
		this.displayStyle = displayStyle;
	}
	@Column(name = "picture_new_url1")
	public String getPictureNewUrl1() {
		return pictureNewUrl1;
	}

	public void setPictureNewUrl1(String pictureNewUrl1) {
		this.pictureNewUrl1 = pictureNewUrl1;
	}
	@Column(name = "picture_new_url2")
	public String getPictureNewUrl2() {
		return pictureNewUrl2;
	}

	public void setPictureNewUrl2(String pictureNewUrl2) {
		this.pictureNewUrl2 = pictureNewUrl2;
	}
	@Column(name = "picture_new_url3")
	public String getPictureNewUrl3() {
		return pictureNewUrl3;
	}
	public void setPictureNewUrl3(String pictureNewUrl3) {
		this.pictureNewUrl3 = pictureNewUrl3;
	}
	@Column(name = "web_special_url")
	public String getWebSpecialUrl() {
		return webSpecialUrl;
	}

	public void setWebSpecialUrl(String webSpecialUrl) {
		this.webSpecialUrl = webSpecialUrl;
	}

}