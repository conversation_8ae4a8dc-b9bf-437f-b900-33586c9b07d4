package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 设备招聘-用户
 */
@Entity
@Table(name = "tyt_scar_recruit_user")
public class TytScarRecruitUser implements java.io.Serializable {

	// Fields

	private static final long serialVersionUID = 8332652170302649385L;
	private Long id;
	private Long brId;
	private Long userId;
	private Integer status;
	private Date ctime;
	private Date utime;

	

	public TytScarRecruitUser() {
		super();
	}

	/** full constructor */
	public TytScarRecruitUser(Long brId, Long userId, Integer status) {
			
		this.brId = brId;
		this.userId = userId;
		this.status = status;
		this.ctime = new Date();
		this.utime = new Date();
	}

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "br_id")
	public Long getBrId() {
		return this.brId;
	}

	public void setBrId(Long brId) {
		this.brId = brId;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

}