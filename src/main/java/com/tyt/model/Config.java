package com.tyt.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
/**
 * 服务器配置
 * 
 * <AUTHOR>
 *
 */
@Entity
@Table(name="tyt_system_arg")
public class Config  implements Serializable {
	 /**
	 * 
	 */
	private static final long serialVersionUID = -8801469605487358940L;
	private Long id;

	/**
	  * 信息停止查询/下载标识 默认允许，不停止
	  * 
	  */
	 private Integer  infoQueryStop;
	 
	 /**
	  * 试用用户信息停止查询/下载标识
	  */
	 private Integer  infoQueryStopOfTrialUser;
	 
	 /**
	  * VIP收费用户信息停止查询/下载标识
	  */
	 private Integer  infoQueryStopOfVipUser;

	 /**
	  * 所有用户（包括销售和管理员在内）信息停止上传标识 默认允许，不停止
	  * 
	  */   
	 private Integer  infoUploadStopAll;
	 
	 /**
	  * 用户信息停止上传标识  默认允许，不停止
	  * 
	  */      
	 private Integer  infoUploadStopUser;
	 
	 public static final int FLAG_OPEN = 0;
	 
	 public static final int FLAG_CLOSE = 1;
	 
	 /**
	  * 试用用户信息查询间隔  单位秒
	  * 
	  */
	 private Integer  queryIntervalOfTrialUser;
	 
	 /**
	  * VIP收费用户信息查询间隔  单位秒
	  * 
	  */
	 private Integer  queryIntervalOfVipUser;
	 
	 /**
	  * 用户信息查询间隔上限 -> 无效信息查询间隔  单位秒
	  * 
	  */
	 private Integer  queryIntervalUpperLimit;
	 
	 /**
	  * 手机试用用户信息查询间隔  单位秒
	  * 
	  */
	 private Integer  queryIntervalOfTrialUserPhone;
	 
	 /**
	  * 手机VIP收费用户信息查询间隔  单位秒
	  * 
	  */
	 private Integer  queryIntervalOfVipUserPhone;
	 
	 /**
	  * 手机用户信息查询间隔上限 -> 手机无效信息查询间隔  单位秒
	  * 
	  */
	 private Integer  queryIntervalOfInvalidateInfoPhone;
	 
	 
	 /**
	  * 0  主服务器、默认  1  备用服务器
	  * 
	  */
	 private Integer  switchBackup; 
	 
	 public static final int MASTER_SERVICE = 0;
	 public static final int BACKUP_SERVICE = 1;
     /**
      * QQ屏蔽开关
      */
	 private Integer qqBlackListStop;
	 /**
	  * 信息查询缓存开关
	  */
	 private Integer infoQueryCacheStop;
	 /*
	  * 系统公告
	  */
	 private String notice;
	 
	 /*
	  * 发货公告
	  */
	 private String sendGoodsNotice;
	 
	 /*
	  * 找货公告
	  */
	 private String searchGoodsNotice;
	 
	 /*
	  * 版权公告
	  */
	 private String copyRightGoodsNotice;
	 
	 /*seconds
	 tyt.info.cache.query.expire.time=15*/
	 private Integer infoQueryCacheExpire;
	 
	 private Date createTime;
	 private Date updateTime;
	
	 
	 @Id
	 @GeneratedValue
	 @Column(name="id",nullable=false,unique=true)
	 public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	
    public Config() {
		super();
	}

	@Column(name="infoQueryStop")
	public Integer getInfoQueryStop() {
		return infoQueryStop;
	 }

	public void setInfoQueryStop(Integer infoQueryStop) {
		this.infoQueryStop = infoQueryStop;
	}
	@Column(name="infoUploadStopAll")
	public Integer getInfoUploadStopAll() {
		return infoUploadStopAll;
	}

	public void setInfoUploadStopAll(Integer infoUploadStopAll) {
		this.infoUploadStopAll = infoUploadStopAll;
	}
	@Column(name="infoUploadStopUser")
	public Integer getInfoUploadStopUser() {
		return infoUploadStopUser;
	}

	public void setInfoUploadStopUser(Integer infoUploadStopUser) {
		this.infoUploadStopUser = infoUploadStopUser;
	}
	@Column(name="queryIntervalOfTrialUser")
	public Integer getQueryIntervalOfTrialUser() {
		return queryIntervalOfTrialUser;
	}

	public void setQueryIntervalOfTrialUser(Integer queryIntervalOfTrialUser) {
		this.queryIntervalOfTrialUser = queryIntervalOfTrialUser;
	}
	@Column(name="queryIntervalOfVipUser")
	public Integer getQueryIntervalOfVipUser() {
		return queryIntervalOfVipUser;
	}

	public void setQueryIntervalOfVipUser(Integer queryIntervalOfVipUser) {
		this.queryIntervalOfVipUser = queryIntervalOfVipUser;
	}
	@Column(name="queryIntervalUpperLimit")
	public Integer getQueryIntervalUpperLimit() {
		return queryIntervalUpperLimit;
	}

	public void setQueryIntervalUpperLimit(Integer queryIntervalUpperLimit) {
		this.queryIntervalUpperLimit = queryIntervalUpperLimit;
	}
	
	@Column(name="switchBackup")
	public Integer getSwitchBackup() {
		return switchBackup;
	}

	public void setSwitchBackup(Integer switchBackup) {
		this.switchBackup = switchBackup;
	}

	@Column(name="infoQueryStopOfVipUser")
	public Integer getInfoQueryStopOfVipUser() {
		return infoQueryStopOfVipUser;
	}

	public void setInfoQueryStopOfVipUser(Integer infoQueryStopOfVipUser) {
		this.infoQueryStopOfVipUser = infoQueryStopOfVipUser;
	}
	@Column(name="infoQueryStopOfTrialUser")
	public Integer getInfoQueryStopOfTrialUser() {
		return infoQueryStopOfTrialUser;
	}

	public void setInfoQueryStopOfTrialUser(Integer infoQueryStopOfTrialUser) {
		this.infoQueryStopOfTrialUser = infoQueryStopOfTrialUser;
	}
	
	@Column(name="qqBlackListStop")
	public Integer getQqBlackListStop() {
		return qqBlackListStop;
	}

	public void setQqBlackListStop(Integer qqBlackListStop) {
		this.qqBlackListStop = qqBlackListStop;
	}
	@Column(name="infoQueryCacheStop")
	public Integer getInfoQueryCacheStop() {
		return infoQueryCacheStop;
	}

	public void setInfoQueryCacheStop(Integer infoQueryCacheStop) {
		this.infoQueryCacheStop = infoQueryCacheStop;
	}
	@Column(name="notice")
	public String getNotice() {
		return notice;
	}

	public void setNotice(String notice) {
		this.notice = notice;
	}
	
	
	@Column(name="infoQueryCacheExpire")
	public Integer getInfoQueryCacheExpire() {
		return infoQueryCacheExpire;
	}

	public void setInfoQueryCacheExpire(Integer queryCacheExpire) {
		this.infoQueryCacheExpire = queryCacheExpire;
	}
	
	
	@Column(name="queryIntervalOfTrialUserPhone")
	public Integer getQueryIntervalOfTrialUserPhone() {
		return queryIntervalOfTrialUserPhone;
	}

	public void setQueryIntervalOfTrialUserPhone(Integer queryIntervalOfTrialUserPhone) {
		this.queryIntervalOfTrialUserPhone = queryIntervalOfTrialUserPhone;
	}
	@Column(name="queryIntervalOfVipUserPhone")
	public Integer getQueryIntervalOfVipUserPhone() {
		return queryIntervalOfVipUserPhone;
	}

	public void setQueryIntervalOfVipUserPhone(Integer queryIntervalOfVipUserPhone) {
		this.queryIntervalOfVipUserPhone = queryIntervalOfVipUserPhone;
	}

	
	@Column(name="queryIntervalOfInvalidateInfoPhone")
	public Integer getQueryIntervalOfInvalidateInfoPhone() {
		return queryIntervalOfInvalidateInfoPhone;
	}

	public void setQueryIntervalOfInvalidateInfoPhone(
			Integer queryIntervalOfInvalidateInfoPhone) {
		this.queryIntervalOfInvalidateInfoPhone = queryIntervalOfInvalidateInfoPhone;
	}
	
	@Column(name="sendGoodsNotice")
	public String getSendGoodsNotice() {
		return sendGoodsNotice;
	}

	public void setSendGoodsNotice(String sendGoodsNotice) {
		this.sendGoodsNotice = sendGoodsNotice;
	}
	@Column(name="searchGoodsNotice")
	public String getSearchGoodsNotice() {
		return searchGoodsNotice;
	}

	public void setSearchGoodsNotice(String searchGoodsNotice) {
		this.searchGoodsNotice = searchGoodsNotice;
	}
	@Column(name="copyRightGoodsNotice")
	public String getCopyRightGoodsNotice() {
		return copyRightGoodsNotice;
	}
	
	public void setCopyRightGoodsNotice(String copyRightGoodsNotice) {
		this.copyRightGoodsNotice = copyRightGoodsNotice;
	}
	
	@Column(name="createTime")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name="updateTime")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	

	@Override
	public String toString() {
		return "Config [infoQueryStop=" + infoQueryStop
				+ ", infoQueryStopOfTrialUser=" + infoQueryStopOfTrialUser
				+ ", infoQueryStopOfVipUser=" + infoQueryStopOfVipUser
				+ ", infoUploadStopAll=" + infoUploadStopAll
				+ ", infoUploadStopUser=" + infoUploadStopUser
				+ ", queryIntervalOfTrialUser=" + queryIntervalOfTrialUser
				+ ", queryIntervalOfVipUser=" + queryIntervalOfVipUser
				+ ", queryIntervalUpperLimit=" + queryIntervalUpperLimit
				+ ", switchBackup=" + switchBackup + ", qqBlackListStop="
				+ qqBlackListStop + ", infoQueryCacheStop="
				+ infoQueryCacheStop + ", notice=" + notice
				+ ", queryCacheExpire=" + infoQueryCacheExpire
				+", queryIntervalOfTrialUserPhone="+queryIntervalOfTrialUserPhone
				+", queryIntervalOfVipUserPhone="+queryIntervalOfVipUserPhone
				+", queryIntervalOfInvalidateInfoPhone="+queryIntervalOfInvalidateInfoPhone
				+", copyRightGoodsNotice="+copyRightGoodsNotice
				+", searchGoodsNotice="+searchGoodsNotice
				+", sendGoodsNotice="+sendGoodsNotice
				+"]";
	}

	

	
}
