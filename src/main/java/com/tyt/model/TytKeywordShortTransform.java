package com.tyt.model;

import javax.persistence.*;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020年09月16日上午11:33:20
 * @description
 */
@Entity
@Table(name = "tyt_keyword_short_transform", schema = "tyt")
public class TytKeywordShortTransform implements java.io.Serializable {
    private static final long serialVersionUID = 1627837500143225253L;

    private Integer id;
    private String keywordShortName;
    private String standardName;
    private String keywordShortLength;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "keyword_short_name")
    public String getKeywordShortName() {
        return keywordShortName;
    }

    public void setKeywordShortName(String keywordShortName) {
        this.keywordShortName = keywordShortName;
    }

    @Basic
    @Column(name = "standard_name")
    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    @Basic
    @Column(name = "keyword_short_length")
    public String getKeywordShortLength() {
        return keywordShortLength;
    }

    public void setKeywordShortLength(String keywordShortLength) {
        this.keywordShortLength = keywordShortLength;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TytKeywordShortTransform that = (TytKeywordShortTransform) o;
        return id == that.id &&
                Objects.equals(keywordShortName, that.keywordShortName) &&
                Objects.equals(standardName, that.standardName) &&
                Objects.equals(keywordShortLength, that.keywordShortLength);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, keywordShortName, standardName, keywordShortLength);
    }
}
