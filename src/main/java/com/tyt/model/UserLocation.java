package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
@Entity
@Table(name="tyt_user_location")
public class UserLocation implements Serializable{

	private static final long serialVersionUID = -1934225555517948854L;
	   Long id;//'主键',
	   Long userId;//'用户ID',
	   String cellPhone;//'账号',
	   Integer userSign;//'身份',
	   String deliverType;//'核实身份',
	   Integer clientSign;//'客户端标识 1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB',
	   String clientVersion;//'客户端版本号',
	   Date createTime;//'创建时间',
	   String opType;//'1打开APP2附近加油站3附近高速路口 4附近住宿5附近餐饮6附近洗浴7附近银行',
	   String country;//'国家',
	   String province;//'省',
	   String city;//'市',
	   String district;//'区',
	   String road;//'道路或者街道',
	   String longitude;//'经度',
	   String latitude;//'纬度',
       //2020-02-10 xyy
       String aoiName; //aoi名称
       String poiName; //poi名称
       String address; //详细地址
       String number; //门牌号码
	
	   
	@GeneratedValue
	@Id
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="user_sign")
	public Integer getUserSign() {
		return userSign;
	}
	public void setUserSign(Integer userSign) {
		this.userSign = userSign;
	}
	@Column(name="deliver_type")
	public String getDeliverType() {
		return deliverType;
	}
	public void setDeliverType(String deliverType) {
		this.deliverType = deliverType;
	}
	@Column(name="client_sign")
	public Integer getClientSign() {
		return clientSign;
	}
	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}
	@Column(name="client_version")
	public String getClientVersion() {
		return clientVersion;
	}
	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name="op_type")
	public String getOpType() {
		return opType;
	}
	public void setOpType(String opType) {
		this.opType = opType;
	}
	@Column(name="country")
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	@Column(name="province")
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	@Column(name="city")
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	@Column(name="district")
	public String getDistrict() {
		return district;
	}
	public void setDistrict(String district) {
		this.district = district;
	}
	@Column(name="road")
	public String getRoad() {
		return road;
	}
	public void setRoad(String road) {
		this.road = road;
	}
	@Column(name="longitude")
	public String getLongitude() {
		return longitude;
	}
	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}
	@Column(name="latitude")
	public String getLatitude() {
		return latitude;
	}
	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}
    @Column(name="aoi_name")
    public String getAoiName() {
        return aoiName;
    }

    public void setAoiName(String aoiName) {
        this.aoiName = aoiName;
    }
    @Column(name="poi_name")
    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }
    @Column(name="address")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    @Column(name="number")
    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }
}
