package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
/**
 * 用户车辆信息表
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "tyt_car")
public class Car  implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4178269941140050216L;
	Long id;// 车头认证状态
	Long userId;// 用户id
	Long driverId;// 用户id
//	String typeCode;// 车辆类型代码 1平板车
//	String type;// 车辆类型名称
	String headCity;// 车头牌照头字母
	String headNo;// 车头牌照号码
	String tailCity;// 挂车牌照头字母
	String tailNo;// 挂车牌照号码
//	String lengthCode;// 车长代码
//	String length;// 车长单位米
//	String carryCode;// 载重代码
//	String carry;// 载重单位吨
	String headDrivingUrl;// 车头行驶本url
	String tailDrivingUrl;// 挂车行驶本URL
	/**
	 * 临牌反面url
	 */
	String tailDrivingOtherSideUrl;
//	String insuranceCode;// 保险代码
//	String insurance;// 购买保险类别
//	Date expireTime;// 保险到期时间
	Date createTime;// 创建日期
	Date updateTime;// 更新时间
	String auth;// 车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
//	String tailAuth;// 挂车行驶本认证状态0:未认证；1:认证成功；2：认证失败
	String remark;// 描述
//	String headPhotoUrl;// 车头照片
	String failureReason;// 失败原因
	
	/*2015-08-31*/
	String gpsUrl ;// 'gps定位网址',
	String gpsName;//'gps网站用户名',
	String gpsPwd;//'gps网站密码',
	
	/*2015-09-08*/
	String headName;//'车头车主姓名',
	String headBrand;//'车头品牌型号',
	String tailName;//'挂车车主姓名',
	String tailBrand;//'挂车品牌型号',
	Integer isDelete=1;
	
	String carName;
	String sex;
	String card;
	
	//2017-07-19
	String headAuthStatus; //车头审核状态
	String headFailReason;//车头审核失败原因
	String tailAuthStatus; //车挂审核状态
	String tailFailReason; //车挂审核失败原因
	String findGoodOnOff; //找货开关
	Long sort; //排序
	Date examineTime; //审核时间
	String examineName; //审核人姓名
	Long examineEmplId; // 审核人ID
	
	String deleteReason; //删除原因
	String updateReason;//重新审核原因
	String updateType; //更改類型


	//2019-05-29 app调度平台增加字段
	//车辆二级类型（来源于后台挂车二级类型）
	private Integer carType;
	//主司机姓名
	private String driverName;
	//主司机电话
	private String driverPhone;
	//副司机姓名
	private String secondaryDriverName;
	//副司机电话
	private String secondaryDriverPhone;
	//随车电话
	private String followDriverPhone;
	//牵引马力
	private Integer horsePower;

	//核定载重
	private String checkWeight;
	//超载限重，单位:吨(限定值：1-999)
	private String maxPayload;
	//车长:单位mm
	private String length;
	//车宽:单位mm
	private String width;
	//车高:单位mm
	private String height;
	//车辆类型名称
	private String carTypeName;

	//调度平台APP车辆列表补充字段
	//车主姓名
	private String trueName;
	//车主联系电话
	private String cellPhone;

	//2019-08-22 调度平台二期增加字段
	//挂车样式(1.纯平常规 2.高低高常规 3.纯平超低 4.高低高超低)
	private Integer isPureFlat;
	//车头车主电话(所有人电话)
	private String headPhone;

	//维护人
	private String maintainerName;
	/**
	 * 车头类型1.高顶 2.低顶
	 */
	private Integer carHeadType;

	/**
	 * 是否带爬梯  0 不带 1 带
	 */
	private Integer hasLadder;

	/**
	 * 客户端标识
	 */
	private String clientSign;
	/**
	 * 是否调度车0不是1是
	 */
	private String isDispatch="0";
	/**
	 * 道路运输证审核结果 0审核中1审核成功2审核失败
	 */
	private String roadCardStatus;
	/**
	 * 道路运输经营许可证审核结果 0审核中1审核成功2审核失败
	 */
	private String roadLicenseStatus;
	/**
	 * 道路运输证审核失败原因
	 */
	private String roadCardFailReason;
	/**
	 * 道路运输经营许可证审核失败原因
	 */
	private String roadLicenseReason;

	/**
	 *挂车相关信息
	 */
	private CarDetailTail carTailData;
	/**
	 * 车头信息
	 */
	private CarDetailHead carHeadData;
	/**
	 * 所属司机相关信息
	 */
	private TytCarDriverArchives driverData;
	/**
	 *驱动形式
	 */
	private String drivingForm;
	/**
	 *排放标准
	 */
	private String emissionStandard;
	/**
	 *挂车所有人电话
	 */
	private String tailPhone;
	/**
	 * 车辆等级（1.熟车 2.自有 3专车 默认:0
	 */
	private Integer carDegree;
	/**
	 * 车辆来源0市场1客服2app
	 */
	private Integer source;
	/**
	 * 车辆认证来源
	 */
	private String authPath;

	/**
	 * 车头行驶证副页反面url
	 */
	private String headDrivingSubpageUrl;
	/**
	 * 车头道路运输证主页url
	 */
	private String headTransportHomepageUrl;
	/**
	 * 车头道路运输证副页url
	 */
	private String headTransportSubpageUrl;
	/**
	 * 挂车行驶证副页反面url
	 */
	private String tailDrivingSubpageUrl;
	/**
	 * 挂车道路运输证主页url
	 */
	private String tailTransportHomepageUrl;
	/**
	 * 挂车道路运输证副页url
	 */
	private String tailTransportSubpageUrl;

	/**
	 * 车头道路运输证审核状态
	 * 0 认证中
	 * 1 认证通过
	 * 2 认证失败
	 */
	private Integer headTransportAuthStatus;

	private String headTransportFailReason;

	/**
	 * 挂车道路运输证审核状态
	 * 0 认证中
	 * 1 认证通过
	 * 2 认证失败
	 */
	private Integer tailTransportAuthStatus;

	/**
	 * 挂车道路运输证审核失败原因
	 */
	private String tailTransportFailReason;
	/**
	 * 车头行驶证有效期
	 */
	private Date headDrivingExpiredTime;
	/**
	 * 车头道路运输证号
	 */
	private String headTransportNo;
	/**
	 * 车头车牌颜色
	 */
	private String headLicensePlateColor;
	/**
	 * 车辆能源类型
	 */
	private String headVehicleEnergyType;
	/**
	 * 车头吨位
	 */
	private Integer headTonnage;
	/**
	 * 车头道路运输证有效期
	 */
	private Date headTransportExpiredTime;
	/**
	 * 挂车道路运输证号
	 */
	private String tailTransportNo;
	/**
	 * 挂车行驶证有效期
	 */
	private Date tailDrivingExpiredTime;
	/**
	 * 挂车道路运输证有效期
	 */
	private Date tailTransportExpiredTime;

	/**
	 * 主司机用户ID
	 */
	private Long driverUserId;

	/**
	 * 副司机用户ID
	 */
	private Long secondaryDriverUserId;
	/**
	 * 挂车照片
	 */
	private String tailPhotoUrl;
	/**
	 * 车头道路运输证经营许可证号
	 */
	private String headBusinessLicenseNo;
	/**
	 * 挂车道路运输证经营许可证号
	 */
	private String tailBusinessLicenseNo;

	private Integer isInvoice;

	// 车头道路运输证信息
	private String headCarNo;
	private String headVehicleType;
	private String headBusinessScope;
	private Date headIssueDate;

	//挂车道路运输证信息
	private String tailCarNo;
	private String tailVehicleType;
	private String tailBusinessScope;
	private Date tailIssueDate;

	private Integer thirdPartyRequire;

	private Integer xhlPartyRequire;

	@Column(name="xhl_party_require")
	public Integer getXhlPartyRequire() {
		return xhlPartyRequire;
	}

	public void setXhlPartyRequire(Integer xhlPartyRequire) {
		this.xhlPartyRequire = xhlPartyRequire;
	}

	@Column(name="car_degree")
	public Integer getCarDegree() {
		return carDegree;
	}

	public void setCarDegree(Integer carDegree) {
		this.carDegree = carDegree;
	}
	@Column(name="source")
	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	@Transient
	public TytCarDriverArchives getDriverData() {
		return driverData;
	}

	public void setDriverData(TytCarDriverArchives driverData) {
		this.driverData = driverData;
	}

	@Column(name="tail_phone")
	public String getTailPhone() {
		return tailPhone;
	}

	public void setTailPhone(String tailPhone) {
		this.tailPhone = tailPhone;
	}

	@Column(name="driving_form")
	public String getDrivingForm() {
		return drivingForm;
	}

	public void setDrivingForm(String drivingForm) {
		this.drivingForm = drivingForm;
	}
	@Column(name="emission_standard")
	public String getEmissionStandard() {
		return emissionStandard;
	}

	public void setEmissionStandard(String emissionStandard) {
		this.emissionStandard = emissionStandard;
	}

	@Column(name="driver_id")
	public Long getDriverId() {
		return driverId;
	}

	public void setDriverId(Long driverId) {
		this.driverId = driverId;
	}

	@Transient
	public CarDetailTail getCarTailData() {
		return carTailData;
	}

	public void setCarTailData(CarDetailTail carTailData) {
		this.carTailData = carTailData;
	}
	@Transient
	public CarDetailHead getCarHeadData() {
		return carHeadData;
	}

	public void setCarHeadData(CarDetailHead carHeadData) {
		this.carHeadData = carHeadData;
	}

	@Column(name="road_card_fail_reason")
	public String getRoadCardFailReason() {
		return roadCardFailReason;
	}

	public void setRoadCardFailReason(String roadCardFailReason) {
		this.roadCardFailReason = roadCardFailReason;
	}
	@Column(name="road_license_fail_reason")
	public String getRoadLicenseReason() {
		return roadLicenseReason;
	}

	public void setRoadLicenseReason(String roadLicenseReason) {
		this.roadLicenseReason = roadLicenseReason;
	}

	@Column(name="road_card_status")
	public String getRoadCardStatus() {
		return roadCardStatus;
	}

	public void setRoadCardStatus(String roadCardStatus) {
		this.roadCardStatus = roadCardStatus;
	}
	@Column(name="road_license_status")
	public String getRoadLicenseStatus() {
		return roadLicenseStatus;
	}

	public void setRoadLicenseStatus(String roadLicenseStatus) {
		this.roadLicenseStatus = roadLicenseStatus;
	}

	@Column(name="tail_driving_other_side_url")
	public String getTailDrivingOtherSideUrl() {
		return tailDrivingOtherSideUrl;
	}

	public void setTailDrivingOtherSideUrl(String tailDrivingOtherSideUrl) {
		this.tailDrivingOtherSideUrl = tailDrivingOtherSideUrl;
	}
	@Column(name="is_dispatch")
	public String getIsDispatch() {
		return isDispatch;
	}

	public void setIsDispatch(String isDispatch) {
		this.isDispatch = isDispatch;
	}

	@Column(name="plat_id")
	public String getClientSign() {
		return clientSign;
	}

	public void setClientSign(String clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name="has_ladder")
	public Integer getHasLadder() {
		return hasLadder;
	}

	public void setHasLadder(Integer hasLadder) {
		this.hasLadder = hasLadder;
	}

	@Column(name="car_head_type")
	public Integer getCarHeadType() {
		return carHeadType;
	}

	public void setCarHeadType(Integer carHeadType) {
		this.carHeadType = carHeadType;
	}

	@Id
	@GeneratedValue
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
//	@Column(name="type_code")
//	public String getTypeCode() {
//		return typeCode;
//	}
//	public void setTypeCode(String typeCode) {
//		this.typeCode = typeCode;
//	}
//	@Column(name="type")
//	public String getType() {
//		return type;
//	}
//	public void setType(String type) {
//		this.type = type;
//	}
	@Column(name="head_city")
	public String getHeadCity() {
		return headCity;
	}
	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}
	@Column(name="head_no")
	public String getHeadNo() {
		return headNo;
	}
	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}
	@Column(name="tail_city")
	public String getTailCity() {
		return tailCity;
	}
	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}
	@Column(name="tail_no")
	public String getTailNo() {
		return tailNo;
	}
	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}
//	@Column(name="length_code")
//	public String getLengthCode() {
//		return lengthCode;
//	}
//	public void setLengthCode(String lengthCode) {
//		this.lengthCode = lengthCode;
//	}
//	@Column(name="length")
//	public String getLength() {
//		return length;
//	}
//	public void setLength(String length) {
//		this.length = length;
//	}
//	
//	@Column(name="carry_code")
//	public String getCarryCode() {
//		return carryCode;
//	}
//	public void setCarryCode(String carryCode) {
//		this.carryCode = carryCode;
//	}
//	@Column(name="carry")
//	public String getCarry() {
//		return carry;
//	}
//	public void setCarry(String carry) {
//		this.carry = carry;
//	}
	@Column(name="head_driving_url")
	public String getHeadDrivingUrl() {
		return headDrivingUrl;
	}
	public void setHeadDrivingUrl(String headDrivingUrl) {
		this.headDrivingUrl = headDrivingUrl;
	}
	@Column(name="tail_driving_url")
	public String getTailDrivingUrl() {
		return tailDrivingUrl;
	}
	public void setTailDrivingUrl(String tailDrivingUrl) {
		this.tailDrivingUrl = tailDrivingUrl;
	}
//	@Column(name="insurance_code")
//	public String getInsuranceCode() {
//		return insuranceCode;
//	}
//	public void setInsuranceCode(String insuranceCode) {
//		this.insuranceCode = insuranceCode;
//	}
//	@Column(name="insurance")
//	public String getInsurance() {
//		return insurance;
//	}
//	public void setInsurance(String insurance) {
//		this.insurance = insurance;
//	}
//	@Column(name="expire_time")
//	public Date getExpireTime() {
//		return expireTime;
//	}
//	public void setExpireTime(Date expireTime) {
//		this.expireTime = expireTime;
//	}
	
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}
	
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name="update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Column(name="auth")
	public String getAuth() {
		return auth;
	}
	public void setAuth(String auth) {
		this.auth = auth;
	}
	@Column(name="remark")
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
//	@Column(name="head_photo_url")
//	public String getHeadPhotoUrl() {
//		return headPhotoUrl;
//	}
//	public void setHeadPhotoUrl(String headPhotoUrl) {
//		this.headPhotoUrl = headPhotoUrl;
//	}
	@Column(name="failure_reason")
	public String getFailureReason() {
		return failureReason;
	}
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	
	
	@Column(name="gps_url")
	public String getGpsUrl() {
		return gpsUrl;
	}
	public void setGpsUrl(String gpsUrl) {
		this.gpsUrl = gpsUrl;
	}
	@Column(name="gps_name")
	public String getGpsName() {
		return gpsName;
	}
	public void setGpsName(String gpsName) {
		this.gpsName = gpsName;
	}
	@Column(name="gps_pwd")
	public String getGpsPwd() {
		return gpsPwd;
	}
	public void setGpsPwd(String gpsPwd) {
		this.gpsPwd = gpsPwd;
	}
	@Column(name="head_name")
	public String getHeadName() {
		return headName;
	}
	public void setHeadName(String headName) {
		this.headName = headName;
	}
	@Column(name="head_brand")
	public String getHeadBrand() {
		return headBrand;
	}
	public void setHeadBrand(String headBrand) {
		this.headBrand = headBrand;
	}
	@Column(name="tail_name")
	public String getTailName() {
		return tailName;
	}
	public void setTailName(String tailName) {
		this.tailName = tailName;
	}
	@Column(name="tail_brand")
	public String getTailBrand() {
		return tailBrand;
	}
	public void setTailBrand(String tailBrand) {
		this.tailBrand = tailBrand;
	}
	@Column(name="is_delete")
	public Integer getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}
	@Column(name="car_name")
	public String getCarName() {
		return carName;
	}
	
	public void setCarName(String carName) {
		this.carName = carName;
	}
	@Column(name="sex")
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	@Column(name="card")
	public String getCard() {
		return card;
	}
	public void setCard(String card) {
		this.card = card;
	}
	
	@Column(name="head_auth_status")
	public String getHeadAuthStatus() {
		return headAuthStatus;
	}
	public void setHeadAuthStatus(String headAuthStatus) {
		this.headAuthStatus = headAuthStatus;
	}
	@Column(name="head_failure_reason")
	public String getHeadFailReason() {
		return headFailReason;
	}
	public void setHeadFailReason(String headFailReason) {
		this.headFailReason = headFailReason;
	}
	@Column(name="tail_auth_status")
	public String getTailAuthStatus() {
		return tailAuthStatus;
	}
	public void setTailAuthStatus(String tailAuthStatus) {
		this.tailAuthStatus = tailAuthStatus;
	}
	@Column(name="tail_failure_reason")
	public String getTailFailReason() {
		return tailFailReason;
	}
	public void setTailFailReason(String tailFailReason) {
		this.tailFailReason = tailFailReason;
	}
	@Column(name="find_good_onoff")
	public String getFindGoodOnOff() {
		return findGoodOnOff;
	}
	public void setFindGoodOnOff(String findGoodOnOff) {
		this.findGoodOnOff = findGoodOnOff;
	}
	@Column(name="sort")
	public Long getSort() {
		return sort;
	}
	public void setSort(Long sort) {
		this.sort = sort;
	}
	@Column(name="examine_time")
	public Date getExamineTime() {
		return examineTime;
	}
	public void setExamineTime(Date examineTime) {
		this.examineTime = examineTime;
	}
	@Column(name="examine_name")
	public String getExamineName() {
		return examineName;
	}
	public void setExamineName(String examineName) {
		this.examineName = examineName;
	}
	@Column(name="examine_empl_id")
	public Long getExamineEmplId() {
		return examineEmplId;
	}
	public void setExamineEmplId(Long examineEmplId) {
		this.examineEmplId = examineEmplId;
	}
	@Column(name="delete_reason")
	public String getDeleteReason() {
		return deleteReason;
	}
	public void setDeleteReason(String deleteReason) {
		this.deleteReason = deleteReason;
	}
	@Column(name="update_reason")
	public String getUpdateReason() {
		return updateReason;
	}
	public void setUpdateReason(String updateReason) {
		this.updateReason = updateReason;
	}
	@Column(name="update_type")
	public String getUpdateType() {
		return updateType;
	}
	public void setUpdateType(String updateType) {
		this.updateType = updateType;
	}

	@Column(name="car_type")
	public Integer getCarType() {
		return carType;
	}

	public void setCarType(Integer carType) {
		this.carType = carType;
	}

	@Column(name="driver_name")
	public String getDriverName() {
		return driverName;
	}

	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}

	@Column(name="driver_phone")
	public String getDriverPhone() {
		return driverPhone;
	}

	public void setDriverPhone(String driverPhone) {
		this.driverPhone = driverPhone;
	}

	@Column(name="secondary_driver_name")
	public String getSecondaryDriverName() {
		return secondaryDriverName;
	}

	public void setSecondaryDriverName(String secondaryDriverName) {
		this.secondaryDriverName = secondaryDriverName;
	}

	@Column(name="secondary_driver_phone")
	public String getSecondaryDriverPhone() {
		return secondaryDriverPhone;
	}

	public void setSecondaryDriverPhone(String secondaryDriverPhone) {
		this.secondaryDriverPhone = secondaryDriverPhone;
	}

	@Column(name="follow_driver_phone")
	public String getFollowDriverPhone() {
		return followDriverPhone;
	}

	public void setFollowDriverPhone(String followDriverPhone) {
		this.followDriverPhone = followDriverPhone;
	}

	@Column(name="horse_power")
	public Integer getHorsePower() {
		return horsePower;
	}

	public void setHorsePower(Integer horsePower) {
		this.horsePower = horsePower;
	}

    @Transient
	public String getCheckWeight() {
		return checkWeight;
	}

	public void setCheckWeight(String checkWeight) {
		this.checkWeight = checkWeight;
	}

	@Transient
	public String getMaxPayload() {
		return maxPayload;
	}

	public void setMaxPayload(String maxPayload) {
		this.maxPayload = maxPayload;
	}

	@Transient
	public String getLength() {
		return length;
	}

	public void setLength(String length) {
		this.length = length;
	}

	@Transient
	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	@Transient
	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	@Transient
	public String getTrueName() {
		return trueName;
	}

	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}

	@Transient
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Transient
	public String getCarTypeName() {
		return carTypeName;
	}

	public void setCarTypeName(String carTypeName) {
		this.carTypeName = carTypeName;
	}

	@Transient
	public Integer getIsPureFlat() {
		return isPureFlat;
	}

	public void setIsPureFlat(Integer isPureFlat) {
		this.isPureFlat = isPureFlat;
	}

	@Column(name = "head_phone")
	public String getHeadPhone() {
		return headPhone;
	}

	public void setHeadPhone(String headPhone) {
		this.headPhone = headPhone;
	}

	@Column(name = "maintainer_name")
	public String getMaintainerName() {
		return maintainerName;
	}

	public void setMaintainerName(String maintainerName) {
		this.maintainerName = maintainerName;
	}

	@Column(name = "auth_path")
	public String getAuthPath() {
		return authPath;
	}

	public void setAuthPath(String authPath) {
		this.authPath = authPath;
	}

	@Column(name = "head_driving_subpage_url")
	public String getHeadDrivingSubpageUrl() {
		return headDrivingSubpageUrl;
	}

	public void setHeadDrivingSubpageUrl(String headDrivingSubpageUrl) {
		this.headDrivingSubpageUrl = headDrivingSubpageUrl;
	}

	@Column(name = "head_transport_homepage_url")
	public String getHeadTransportHomepageUrl() {
		return headTransportHomepageUrl;
	}

	public void setHeadTransportHomepageUrl(String headTransportHomepageUrl) {
		this.headTransportHomepageUrl = headTransportHomepageUrl;
	}

	@Column(name = "head_transport_subpage_url")
	public String getHeadTransportSubpageUrl() {
		return headTransportSubpageUrl;
	}

	public void setHeadTransportSubpageUrl(String headTransportSubpageUrl) {
		this.headTransportSubpageUrl = headTransportSubpageUrl;
	}

	@Column(name = "tail_driving_subpage_url")
	public String getTailDrivingSubpageUrl() {
		return tailDrivingSubpageUrl;
	}

	public void setTailDrivingSubpageUrl(String tailDrivingSubpageUrl) {
		this.tailDrivingSubpageUrl = tailDrivingSubpageUrl;
	}

	@Column(name = "tail_transport_homepage_url")
	public String getTailTransportHomepageUrl() {
		return tailTransportHomepageUrl;
	}

	public void setTailTransportHomepageUrl(String tailTransportHomepageUrl) {
		this.tailTransportHomepageUrl = tailTransportHomepageUrl;
	}

	@Column(name = "tail_transport_subpage_url")
	public String getTailTransportSubpageUrl() {
		return tailTransportSubpageUrl;
	}

	public void setTailTransportSubpageUrl(String tailTransportSubpageUrl) {
		this.tailTransportSubpageUrl = tailTransportSubpageUrl;
	}

	@Column(name = "head_transport_auth_status")
	public Integer getHeadTransportAuthStatus() {
		return headTransportAuthStatus;
	}

	public void setHeadTransportAuthStatus(Integer headTransportAuthStatus) {
		this.headTransportAuthStatus = headTransportAuthStatus;
	}

	@Column(name = "tail_transport_auth_status")
	public Integer getTailTransportAuthStatus() {
		return tailTransportAuthStatus;
	}

	public void setTailTransportAuthStatus(Integer tailTransportAuthStatus) {
		this.tailTransportAuthStatus = tailTransportAuthStatus;
	}

	@Column(name = "head_transport_fail_reason")
	public String getHeadTransportFailReason() {
		return headTransportFailReason;
	}

	public void setHeadTransportFailReason(String headTransportFailReason) {
		this.headTransportFailReason = headTransportFailReason;
	}

	@Column(name = "tail_transport_fail_reason")
	public String getTailTransportFailReason() {
		return tailTransportFailReason;
	}

	public void setTailTransportFailReason(String tailTransportFailReason) {
		this.tailTransportFailReason = tailTransportFailReason;
	}

	@Column(name = "head_driving_expired_time")
	public Date getHeadDrivingExpiredTime() {
		return headDrivingExpiredTime;
	}

	public void setHeadDrivingExpiredTime(Date headDrivingExpiredTime) {
		this.headDrivingExpiredTime = headDrivingExpiredTime;
	}

	@Column(name = "head_transport_no")
	public String getHeadTransportNo() {
		return headTransportNo;
	}

	public void setHeadTransportNo(String headTransportNo) {
		this.headTransportNo = headTransportNo;
	}

	@Column(name = "head_license_plate_color")
	public String getHeadLicensePlateColor() {
		return headLicensePlateColor;
	}

	public void setHeadLicensePlateColor(String headLicensePlateColor) {
		this.headLicensePlateColor = headLicensePlateColor;
	}

	@Column(name = "head_vehicle_energy_type")
	public String getHeadVehicleEnergyType() {
		return headVehicleEnergyType;
	}

	public void setHeadVehicleEnergyType(String headVehicleEnergyType) {
		this.headVehicleEnergyType = headVehicleEnergyType;
	}

	@Column(name = "head_tonnage")
	public Integer getHeadTonnage() {
		return headTonnage;
	}

	public void setHeadTonnage(Integer headTonnage) {
		this.headTonnage = headTonnage;
	}

	@Column(name = "head_transport_expired_time")
	public Date getHeadTransportExpiredTime() {
		return headTransportExpiredTime;
	}

	public void setHeadTransportExpiredTime(Date headTransportExpiredTime) {
		this.headTransportExpiredTime = headTransportExpiredTime;
	}

	@Column(name = "tail_transport_no")
	public String getTailTransportNo() {
		return tailTransportNo;
	}

	public void setTailTransportNo(String tailTransportNo) {
		this.tailTransportNo = tailTransportNo;
	}

	@Column(name = "tail_driving_expired_time")
	public Date getTailDrivingExpiredTime() {
		return tailDrivingExpiredTime;
	}

	public void setTailDrivingExpiredTime(Date tailDrivingExpiredTime) {
		this.tailDrivingExpiredTime = tailDrivingExpiredTime;
	}

	@Column(name = "tail_transport_expired_time")
	public Date getTailTransportExpiredTime() {
		return tailTransportExpiredTime;
	}

	public void setTailTransportExpiredTime(Date tailTransportExpiredTime) {
		this.tailTransportExpiredTime = tailTransportExpiredTime;
	}

	@Column(name = "driver_user_id")
	public Long getDriverUserId() {
		return driverUserId;
	}

	public void setDriverUserId(Long driverUserId) {
		this.driverUserId = driverUserId;
	}

	@Column(name = "secondary_driver_user_id")
	public Long getSecondaryDriverUserId() {
		return secondaryDriverUserId;
	}

	public void setSecondaryDriverUserId(Long secondaryDriverUserId) {
		this.secondaryDriverUserId = secondaryDriverUserId;
	}

	@Column(name = "tail_photo_url")
	public String getTailPhotoUrl() {
		return tailPhotoUrl;
	}

	public void setTailPhotoUrl(String tailPhotoUrl) {
		this.tailPhotoUrl = tailPhotoUrl;
	}

	@Column(name = "head_business_license_no")
	public String getHeadBusinessLicenseNo() {
		return headBusinessLicenseNo;
	}

	public void setHeadBusinessLicenseNo(String headBusinessLicenseNo) {
		this.headBusinessLicenseNo = headBusinessLicenseNo;
	}

	@Column(name = "tail_business_license_no")
	public String getTailBusinessLicenseNo() {
		return tailBusinessLicenseNo;
	}

	public void setTailBusinessLicenseNo(String tailBusinessLicenseNo) {
		this.tailBusinessLicenseNo = tailBusinessLicenseNo;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}


	@Column(name = "is_invoice")
	public Integer getIsInvoice() {
		return isInvoice;
	}


	public void setIsInvoice(Integer isInvoice) {
		this.isInvoice = isInvoice;
	}

	@Column(name = "head_car_no")
	public String getHeadCarNo() {
		return headCarNo;
	}

	public void setHeadCarNo(String headCarNo) {
		this.headCarNo = headCarNo;
	}

	@Column(name = "head_vehicle_type")
	public String getHeadVehicleType() {
		return headVehicleType;
	}

	public void setHeadVehicleType(String headVehicleType) {
		this.headVehicleType = headVehicleType;
	}

	@Column(name = "head_business_scope")
	public String getHeadBusinessScope() {
		return headBusinessScope;
	}

	public void setHeadBusinessScope(String headBusinessScope) {
		this.headBusinessScope = headBusinessScope;
	}

	@Column(name = "head_issue_date")
	public Date getHeadIssueDate() {
		return headIssueDate;
	}

	public void setHeadIssueDate(Date headIssueDate) {
		this.headIssueDate = headIssueDate;
	}

	@Column(name = "tail_car_no")
	public String getTailCarNo() {
		return tailCarNo;
	}

	public void setTailCarNo(String tailCarNo) {
		this.tailCarNo = tailCarNo;
	}

	@Column(name = "tail_vehicle_type")
	public String getTailVehicleType() {
		return tailVehicleType;
	}

	public void setTailVehicleType(String tailVehicleType) {
		this.tailVehicleType = tailVehicleType;
	}

	@Column(name = "tail_business_scope")
	public String getTailBusinessScope() {
		return tailBusinessScope;
	}

	public void setTailBusinessScope(String tailBusinessScope) {
		this.tailBusinessScope = tailBusinessScope;
	}

	@Column(name = "tail_issue_date")
	public Date getTailIssueDate() {
		return tailIssueDate;
	}

	public void setTailIssueDate(Date tailIssueDate) {
		this.tailIssueDate = tailIssueDate;
	}

	@Column(name = "third_party_require")
	public Integer getThirdPartyRequire() {
		return thirdPartyRequire;
	}

	public void setThirdPartyRequire(Integer thirdPartyRequire) {
		this.thirdPartyRequire = thirdPartyRequire;
	}
}
