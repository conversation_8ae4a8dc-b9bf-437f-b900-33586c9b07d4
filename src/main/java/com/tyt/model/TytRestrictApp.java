package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2018年6月26日下午4:00:37
 * @description
 */
@Entity
@Table(name = "tyt_restrict_app", catalog = "tyt")
public class TytRestrictApp implements java.io.Serializable {
	private static final long serialVersionUID = 849955241629436679L;
	
	private Long id;
	private Byte clientType;
	private String androidAppid;
	private String androidAppname;
	private String pcSoftname;
	private String pcPath;
	private String androidNotify;
	private String pcNotify;
	private Byte status;
	private String remark;
	private Date ctime;
	private Date utime;
	private String pcShortcutName;
	private String pcSoftwareWindowText;

	public TytRestrictApp() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "client_type")
	public Byte getClientType() {
		return this.clientType;
	}

	public void setClientType(Byte clientType) {
		this.clientType = clientType;
	}

	@Column(name = "android_appid", length = 64)
	public String getAndroidAppid() {
		return this.androidAppid;
	}

	public void setAndroidAppid(String androidAppid) {
		this.androidAppid = androidAppid;
	}

	@Column(name = "android_appname", length = 64)
	public String getAndroidAppname() {
		return this.androidAppname;
	}

	public void setAndroidAppname(String androidAppname) {
		this.androidAppname = androidAppname;
	}

	@Column(name = "pc_softname", length = 64)
	public String getPcSoftname() {
		return this.pcSoftname;
	}

	public void setPcSoftname(String pcSoftname) {
		this.pcSoftname = pcSoftname;
	}

	@Column(name = "pc_path", length = 500)
	public String getPcPath() {
		return this.pcPath;
	}

	public void setPcPath(String pcPath) {
		this.pcPath = pcPath;
	}

	@Column(name = "android_notify", length = 100)
	public String getAndroidNotify() {
		return this.androidNotify;
	}

	public void setAndroidNotify(String androidNotify) {
		this.androidNotify = androidNotify;
	}

	@Column(name = "pc_notify", length = 100)
	public String getPcNotify() {
		return this.pcNotify;
	}

	public void setPcNotify(String pcNotify) {
		this.pcNotify = pcNotify;
	}

	@Column(name = "status")
	public Byte getStatus() {
		return this.status;
	}

	public void setStatus(Byte status) {
		this.status = status;
	}

	@Column(name = "remark", length = 100)
	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime", length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "pc_shortcut_name", length = 60)
	public String getPcShortcutName() {
		return this.pcShortcutName;
	}

	public void setPcShortcutName(String pcShortcutName) {
		this.pcShortcutName = pcShortcutName;
	}

	@Column(name = "pc_software_window_text", length = 60)
	public String getPcSoftwareWindowText() {
		return this.pcSoftwareWindowText;
	}

	public void setPcSoftwareWindowText(String pcSoftwareWindowText) {
		this.pcSoftwareWindowText = pcSoftwareWindowText;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
