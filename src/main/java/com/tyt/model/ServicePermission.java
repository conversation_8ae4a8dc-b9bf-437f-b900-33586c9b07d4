package com.tyt.model;

import javax.persistence.*;

@Entity
@Table(name = "service_permission")
public class ServicePermission {
    /**
     * 
     */
    private Long id;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否依赖其他的权益，数据库默认为false
     */
    private boolean parasitic;

    /**
     * 日限制次数
     */
    private Integer dayLimit;

    /**
     * 日限制提示信息
     */
    private String dayLimitMsg;

    /**
     * 次数使用完提示信息
     */
    private String noPermissionMsg;

    /**
     * 快到期天数，提示信息
     */
    private Integer lessThanTimes;

    /**
     * 
     */
    private Integer lessThanDay;

    /**
     * 快到期提示信息
     */
    private String expireMsg;

    /**
     * 权益绑定的依附关系，以英文逗号进行分割，对应用户权益表内的servicePermissionTypeId
     */
    private String bindDep;

    /**
     * 
     * @return id 
     */
    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    /**
     * 
     * @param id 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 权益名称
     * @return name 权益名称
     */
    @Column(name = "name")
    public String getName() {
        return name;
    }

    /**
     * 权益名称
     * @param name 权益名称
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 备注信息
     * @return remark 备注信息
     */
    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    /**
     * 备注信息
     * @param remark 备注信息
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * 日限制次数
     * @return day_limit 日限制次数
     */
    @Column(name = "day_limit")
    public Integer getDayLimit() {
        return dayLimit;
    }

    /**
     * 日限制次数
     * @param dayLimit 日限制次数
     */
    public void setDayLimit(Integer dayLimit) {
        this.dayLimit = dayLimit;
    }

    /**
     * 日限制提示信息
     * @return day_limit_msg 日限制提示信息
     */
    @Column(name = "day_limit_msg")
    public String getDayLimitMsg() {
        return dayLimitMsg;
    }

    /**
     * 日限制提示信息
     * @param dayLimitMsg 日限制提示信息
     */
    public void setDayLimitMsg(String dayLimitMsg) {
        this.dayLimitMsg = dayLimitMsg == null ? null : dayLimitMsg.trim();
    }

    /**
     * 次数使用完提示信息
     * @return no_permission_msg 次数使用完提示信息
     */
    @Column(name = "no_permission_msg")
    public String getNoPermissionMsg() {
        return noPermissionMsg;
    }

    /**
     * 次数使用完提示信息
     * @param noPermissionMsg 次数使用完提示信息
     */
    public void setNoPermissionMsg(String noPermissionMsg) {
        this.noPermissionMsg = noPermissionMsg == null ? null : noPermissionMsg.trim();
    }

    /**
     * 快到期天数，提示信息
     * @return less_than_times 快到期天数，提示信息
     */
    @Column(name = "less_than_times")
    public Integer getLessThanTimes() {
        return lessThanTimes;
    }

    /**
     * 快到期天数，提示信息
     * @param lessThanTimes 快到期天数，提示信息
     */
    public void setLessThanTimes(Integer lessThanTimes) {
        this.lessThanTimes = lessThanTimes;
    }

    /**
     * 
     * @return less_than_day 
     */
    @Column(name = "less_than_day")
    public Integer getLessThanDay() {
        return lessThanDay;
    }

    /**
     * 
     * @param lessThanDay 
     */
    public void setLessThanDay(Integer lessThanDay) {
        this.lessThanDay = lessThanDay;
    }

    /**
     * 快到期提示信息
     * @return expire_msg 快到期提示信息
     */
    @Column(name = "expire_msg")
    public String getExpireMsg() {
        return expireMsg;
    }

    /**
     * 快到期提示信息
     * @param expireMsg 快到期提示信息
     */
    public void setExpireMsg(String expireMsg) {
        this.expireMsg = expireMsg == null ? null : expireMsg.trim();
    }

    /**
     * 权益绑定的依附关系，以英文逗号进行分割，对应用户权益表内的servicePermissionTypeId
     * @return bindDep 绑定关系
     */
    @Column(name = "bind_dep")
    public String getBindDep() {
        return bindDep;
    }

    public void setBindDep(String bindDep) {
        this.bindDep = bindDep;
    }

    /**
     * 是否依赖其他的权益，数据库默认为false
     * @return parasitic 是否寄生关系
     */
    @Column(name = "parasitic")
    public boolean getParasitic() {
        return parasitic;
    }

    public void setParasitic(boolean parasitic) {
        this.parasitic = parasitic;
    }

    @Override
    public String toString() {
        return "ServicePermission{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", remark='" + remark + '\'' +
                ", dayLimit=" + dayLimit +
                ", dayLimitMsg='" + dayLimitMsg + '\'' +
                ", noPermissionMsg='" + noPermissionMsg + '\'' +
                ", lessThanTimes=" + lessThanTimes +
                ", lessThanDay=" + lessThanDay +
                ", expireMsg='" + expireMsg + '\'' +
                ", bindDep='" + bindDep + '\'' +
                '}';
    }
}
