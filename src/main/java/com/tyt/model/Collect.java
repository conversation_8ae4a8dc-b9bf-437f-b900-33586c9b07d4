package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
@Entity
@Table(name = "tyt_collect")
public class Collect implements Serializable{
	 
	/**
	 * 
	 */
	 private static final long serialVersionUID = 3725581238549466325L;
	 private Long id;
	 private Integer collectStatus;
	 private Long userId;
	 private Long employeeId;
	 private Long seekId;
	 private Long secondCarId;
	 private Long forSecondId;
	 private Long newCarId;
	 private Long insureId;
	 private Long takeCarId;
	 private Timestamp ctime;// 采集时间
	 
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="collect_status")
	public Integer getCollectStatus() {
		return collectStatus;
	}
	public void setCollectStatus(Integer collectStatus) {
		this.collectStatus = collectStatus;
	}
	@Column(name="employee_id")
	public Long getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(Long employeeId) {
		this.employeeId = employeeId;
	}
	@Column(name="seek_id")
	public Long getSeekId() {
		return seekId;
	}
	public void setSeekId(Long seekId) {
		this.seekId = seekId;
	}
	@Column(name="secondcar_id")
	public Long getSecondCarId() {
		return secondCarId;
	}
	public void setSecondCarId(Long secondCarId) {
		this.secondCarId = secondCarId;
	}
	@Column(name="forsecond_id")
	public Long getForSecondId() {
		return forSecondId;
	}
	public void setForSecondId(Long forSecondId) {
		this.forSecondId = forSecondId;
	}
	@Column(name="newcar_id")
	public Long getNewCarId() {
		return newCarId;
	}
	public void setNewCarId(Long newCarId) {
		this.newCarId = newCarId;
	}
	@Column(name="insure_id")
	public Long getInsureId() {
		return insureId;
	}
	public void setInsureId(Long insureId) {
		this.insureId = insureId;
	}
	@Column(name="takecar_id")
	public Long getTakeCarId() {
		return takeCarId;
	}
	public void setTakeCarId(Long takeCarId) {
		this.takeCarId = takeCarId;
	}
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	 
}
