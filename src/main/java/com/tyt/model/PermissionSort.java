package com.tyt.model;

import javax.persistence.*;

@Entity
@Table(name = "permission_sort")
public class PermissionSort {
    /**
     * 权益ID
     */
    private Long id;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 权益一级类型(对应service_permission表的id,共8类)
     */
    private Integer servicePermissionId;

    /**
     * 权益二级类型(1.时间 2.次数 3.布尔值)
     */
    private Integer type;

    /**
     * 权益类型对应的唯一标识
     */
    private String servicePermissionTypeId;
    /**
     * 权益类型对应名称（货会员、车会员、发货次数、拨打次数）
     */
    private String servicePermissionTypeName;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 权益状态(1.有效 2.无效)
     */
    private Integer status;

    /**
     * 有效期
     */
    private Integer effectiveTime;

    /**
     * 单位 （day:天; month:月;year:年）
     */
    private String unit;

    /**
     * 使用次数
     */
    private Integer useNum;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    @Column(name = "service_permission_id")
    public Integer getServicePermissionId() {
        return servicePermissionId;
    }

    public void setServicePermissionId(Integer servicePermissionId) {
        this.servicePermissionId = servicePermissionId;
    }
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    @Column(name = "priority")
    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    @Column(name = "effective_time")
    public Integer getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Integer effectiveTime) {
        this.effectiveTime = effectiveTime;
    }
    @Column(name = "unit")
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
    @Column(name = "use_num")
    public Integer getUseNum() {
        return useNum;
    }

    public void setUseNum(Integer useNum) {
        this.useNum = useNum;
    }

    @Column(name = "service_permission_type_id")
    public String getServicePermissionTypeId() {
        return servicePermissionTypeId;
    }

    public void setServicePermissionTypeId(String servicePermissionTypeId) {
        this.servicePermissionTypeId = servicePermissionTypeId;
    }
    @Column(name = "service_permission_type_name")
    public String getServicePermissionTypeName() {
        return servicePermissionTypeName;
    }

    public void setServicePermissionTypeName(String servicePermissionTypeName) {
        this.servicePermissionTypeName = servicePermissionTypeName;
    }

    @Override
    public String toString() {
        return "PermissionSort{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", servicePermissionId=" + servicePermissionId +
                ", type=" + type +
                ", servicePermissionTypeId='" + servicePermissionTypeId + '\'' +
                ", servicePermissionTypeName='" + servicePermissionTypeName + '\'' +
                ", priority=" + priority +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                ", effectiveTime=" + effectiveTime +
                ", unit='" + unit + '\'' +
                ", useNum=" + useNum +
                '}';
    }
}
