package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 广告位导入用户
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "tyt_ad_position_user")
public class AdPositionUser implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户等级
     */
    private Integer userGrade;
    /**
     * 广告位Id
     */
    private Long adId;

    /**
     *  是否删除 1.未删除 2.已删除
     */
    private Integer isDelete;

    /**
     *  广告参与类型 1.未参加 2.已参加
     */
    private Integer joinType;

    /**
     * 操作人
     */
    private String operater;

    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 更新时间
     */
    private Date mtime;


    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @Column(name = "user_grade")
    public Integer getUserGrade() {
        return userGrade;
    }

    public void setUserGrade(Integer userGrade) {
        this.userGrade = userGrade;
    }
    @Column(name = "ad_id")
    public Long getAdId() {
        return adId;
    }

    public void setAdId(Long adId) {
        this.adId = adId;
    }
    @Column(name = "is_delete")
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Column(name = "join_type")
    public Integer getJoinType() {
        return joinType;
    }

    public void setJoinType(Integer joinType) {
        this.joinType = joinType;
    }

    @Column(name = "operater")
    public String getOperater() {
        return operater;
    }

    public void setOperater(String operater) {
        this.operater = operater;
    }
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
