package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_secondcar")
public class SecondCar implements Serializable {
	private static final long serialVersionUID = 8632436789992881207L;
	private Long id;
	private Integer distinguish;// 区分待售二手车主与求购二手车人
	private Integer model;// 类型
	private Integer carName;// 车名
	private Integer carAge;// 车龄
	private Float mileage;// 行驶里程
	private Integer bearing; // 载重
	private Integer horsePower;// 马力
	private String province;// 省
	private String city;// 市
	private String county;// 县
	private Integer history;// 事故历史
	private Integer price;// 价格
	private Integer subsection;// 分期
	private String telName;// 联系人
	private String telephone;// 电话
	private String qq;// QQ
	private String title;// 一句话广告
	private String wishes;// 买家寄语
	private Timestamp ctime;
	private String images;
	private Integer status;// 审核否 0未审核 1审核
	private String cellPhone;
	private Integer saveDay;
	private Integer topDay;
	/* 阅读人数 */
	private Integer readNumber=0;
	/* 阅读次数 */
	private Integer readTimes=0;
	/* 虚拟阅读次数 */
	private Integer vritualReadTimes=0;
	/* 用户ID */
	private Long userId;
	/* 0否、1删除 */
	private String deleteStatus="0";
	/*开放关闭状态;0开放1关闭*/
	private String displayStatus="0";
	//收藏人数
	private Integer collectPeopleNumber=0;
	private Date updateTime;
	@Column(name = "read_number")
	public Integer getReadNumber() {
		return readNumber;
	}

	public void setReadNumber(Integer readNumber) {
		this.readNumber = readNumber;
	}

	@Column(name = "read_times")
	public Integer getReadTimes() {
		return readTimes;
	}

	public void setReadTimes(Integer readTimes) {
		this.readTimes = readTimes;
	}

	@Column(name = "vritual_read_times")
	public Integer getVritualReadTimes() {
		return vritualReadTimes;
	}

	public void setVritualReadTimes(Integer vritualReadTimes) {
		this.vritualReadTimes = vritualReadTimes;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "delete_status")
	public String getDeleteStatus() {
		return deleteStatus;
	}

	public void setDeleteStatus(String deleteStatus) {
		this.deleteStatus = deleteStatus;
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "distinguish")
	public Integer getDistinguish() {
		return distinguish;
	}

	public void setDistinguish(Integer distinguish) {
		this.distinguish = distinguish;
	}

	@Column(name = "model")
	public Integer getModel() {
		return model;
	}

	public void setModel(Integer model) {
		this.model = model;
	}

	@Column(name = "car_name")
	public Integer getCarName() {
		return carName;
	}

	public void setCarName(Integer carName) {
		this.carName = carName;
	}

	@Column(name = "car_age")
	public Integer getCarAge() {
		return carAge;
	}

	public void setCarAge(Integer carAge) {
		this.carAge = carAge;
	}

	@Column(name = "mileage")
	public Float getMileage() {
		return mileage;
	}

	public void setMileage(Float mileage) {
		this.mileage = mileage;
	}

	@Column(name = "bearing")
	public Integer getBearing() {
		return bearing;
	}

	public void setBearing(Integer bearing) {
		this.bearing = bearing;
	}

	@Column(name = "horse_power")
	public Integer getHorsePower() {
		return horsePower;
	}

	public void setHorsePower(Integer horsePower) {
		this.horsePower = horsePower;
	}

	@Column(name = "province")
	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Column(name = "city")
	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name = "county")
	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	@Column(name = "history")
	public Integer getHistory() {
		return history;
	}

	public void setHistory(Integer history) {
		this.history = history;
	}

	@Column(name = "price")
	public Integer getPrice() {
		return price;
	}

	public void setPrice(Integer price) {
		this.price = price;
	}

	@Column(name = "subsection")
	public Integer getSubsection() {
		return subsection;
	}

	public void setSubsection(Integer subsection) {
		this.subsection = subsection;
	}

	@Column(name = "tel_name")
	public String getTelName() {
		return telName;
	}

	public void setTelName(String telName) {
		this.telName = telName;
	}

	@Column(name = "telephone")
	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	@Column(name = "QQ")
	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	@Column(name = "title")
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Column(name = "wishes")
	public String getWishes() {
		return wishes;
	}

	public void setWishes(String wishes) {
		this.wishes = wishes;
	}

	@Column(name = "ctime")
	public Timestamp getCtime() {
		return ctime;
	}

	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}

	@Column(name = "second_status")
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "images")
	public String getImages() {
		return images;
	}

	public void setImages(String images) {
		this.images = images;
	}

	@Column(name = "save_day")
	public Integer getSaveDay() {
		return saveDay;
	}

	public void setSaveDay(Integer saveDay) {
		this.saveDay = saveDay;
	}

	@Column(name = "top_day")
	public Integer getTopDay() {
		return topDay;
	}

	public void setTopDay(Integer topDay) {
		this.topDay = topDay;
	}
	@Column(name = "display_status")
	public String getDisplayStatus() {
		return displayStatus;
	}

	public void setDisplayStatus(String displayStatus) {
		this.displayStatus = displayStatus;
	}
	@Column(name = "collect_people_number")
	public Integer getCollectPeopleNumber() {
		return collectPeopleNumber;
	}

	public void setCollectPeopleNumber(Integer collectPeopleNumber) {
		this.collectPeopleNumber = collectPeopleNumber;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	

}
