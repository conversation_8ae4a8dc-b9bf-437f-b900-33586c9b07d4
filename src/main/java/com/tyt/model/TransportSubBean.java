package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name="tyt_transport_sub")
public class TransportSubBean implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long tsId; //货物ID
	private Double fixedCost; //固定成本
	private Double minProfitRate;//最低利润率
	private Double basePrice; //成交底价
	private Double profitRate;//成交利润率

	@Id
	@GeneratedValue
	@Column(name="id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name="ts_id")
	public Long getTsId() {
		return tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name="fixed_cost")
	public Double getFixedCost() {
		return fixedCost;
	}

	public void setFixedCost(Double fixedCost) {
		this.fixedCost = fixedCost;
	}
	@Column(name="min_profit_rate")
	public Double getMinProfitRate() {
		return minProfitRate;
	}

	public void setMinProfitRate(Double minProfitRate) {
		this.minProfitRate = minProfitRate;
	}
	@Column(name="base_price")
	public Double getBasePrice() {
		return basePrice;
	}

	public void setBasePrice(Double basePrice) {
		this.basePrice = basePrice;
	}
	@Column(name="profit_rate")
	public Double getProfitRate() {
		return profitRate;
	}

	public void setProfitRate(Double profitRate) {
		this.profitRate = profitRate;
	}
}
