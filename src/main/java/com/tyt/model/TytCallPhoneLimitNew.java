package com.tyt.model;


import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.gexin.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2018年4月20日上午10:44:36
 * @description
 */
@Entity
@Table(name = "tyt_call_phone_limit_new", catalog = "tyt")
public class TytCallPhoneLimitNew implements java.io.Serializable {
	private static final long serialVersionUID = 146832262492192977L;
	
	private Integer id;
	private byte verifyLevelOne;
	private short carAuthNum;
	private short identityAuthNum;
	private short vipNum;
	private Long operatorId;
	private String operatorName;
	private Byte status;
	private Date ctime;
	private Date utime;

	public TytCallPhoneLimitNew() {
	}

	public TytCallPhoneLimitNew(byte verifyLevelOne, short carAuthNum, short identityAuthNum, short vipNum) {
		this.verifyLevelOne = verifyLevelOne;
		this.carAuthNum = carAuthNum;
		this.identityAuthNum = identityAuthNum;
		this.vipNum = vipNum;
	}

	public TytCallPhoneLimitNew(byte verifyLevelOne, short carAuthNum, short identityAuthNum, short vipNum, Long operatorId, String operatorName, Byte status, Date ctime, Date utime) {
		this.verifyLevelOne = verifyLevelOne;
		this.carAuthNum = carAuthNum;
		this.identityAuthNum = identityAuthNum;
		this.vipNum = vipNum;
		this.operatorId = operatorId;
		this.operatorName = operatorName;
		this.status = status;
		this.ctime = ctime;
		this.utime = utime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "verify_level_one", nullable = false)
	public byte getVerifyLevelOne() {
		return this.verifyLevelOne;
	}

	public void setVerifyLevelOne(byte verifyLevelOne) {
		this.verifyLevelOne = verifyLevelOne;
	}

	@Column(name = "car_auth_num", nullable = false)
	public short getCarAuthNum() {
		return this.carAuthNum;
	}

	public void setCarAuthNum(short carAuthNum) {
		this.carAuthNum = carAuthNum;
	}

	@Column(name = "identity_auth_num", nullable = false)
	public short getIdentityAuthNum() {
		return this.identityAuthNum;
	}

	public void setIdentityAuthNum(short identityAuthNum) {
		this.identityAuthNum = identityAuthNum;
	}

	@Column(name = "vip_num", nullable = false)
	public short getVipNum() {
		return this.vipNum;
	}

	public void setVipNum(short vipNum) {
		this.vipNum = vipNum;
	}

	@Column(name = "operator_id")
	public Long getOperatorId() {
		return this.operatorId;
	}

	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}

	@Column(name = "operator_name", length = 32)
	public String getOperatorName() {
		return this.operatorName;
	}

	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}

	@Column(name = "status")
	public Byte getStatus() {
		return this.status;
	}

	public void setStatus(Byte status) {
		this.status = status;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime", length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
