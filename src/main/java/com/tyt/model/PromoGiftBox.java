package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name="promo_gift_box")
public class PromoGiftBox implements Serializable {
    private static final long serialVersionUID = -3590598106298234365L;

    private Integer id;//int(11) NOT NULL
    private String name;//varchar(100) NULL礼包名称
    private Integer status;//tinyint(4) NULL礼包状态1:有效 2:无效 3:删除
    private Date validTime;//date NULL礼包有效期(最小优惠券截止日期)
    private Integer totalNum;//int(11) NULL礼包中券的总数量
    private BigDecimal totalAmount;//decimal(10,2) NULL礼包中券的总金额
    private Date ctime;//timestamp NOT NULL创建时间
    private Date mtime;//timestamp NOT NULL修改时间

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @Column(name="name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    @Column(name="status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    @Column(name="valid_time")
    public Date getValidTime() {
        return validTime;
    }

    public void setValidTime(Date validTime) {
        this.validTime = validTime;
    }
    @Column(name="total_num")
    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }
    @Column(name="total_amount")
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
