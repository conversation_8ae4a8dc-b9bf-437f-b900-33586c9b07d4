package com.tyt.model;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * @ClassName CarInsuranceFileInfo
 * @Description 车险询价证件信息表
 * <AUTHOR>
 * @Date 2019-03-13 14:50
 * @Version 1.0
 */
@Entity
@Table(name = "car_insurance_file_info", schema = "tyt", catalog = "")
public class CarInsuranceFileInfo {
    private Long id;
    private Long inquiryId;
    private Integer fileType;
    private String originalFileName;
    private String fileName;
    private String filePath;
    private Long fileSize;
    private String fileSuffix;
    private String smallFileName;
    private String smallFilePath;
    private Long createUserId;
    private String createUserName;
    private Timestamp ctime;
    private Long updateUserId;
    private String updateUserName;
    private Timestamp utime;
    private Integer status;

    @Id
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "inquiry_id")
    public Long getInquiryId() {
        return inquiryId;
    }

    public void setInquiryId(Long inquiryId) {
        this.inquiryId = inquiryId;
    }

    @Basic
    @Column(name = "file_type")
    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    @Basic
    @Column(name = "original_file_name")
    public String getOriginalFileName() {
        return originalFileName;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    @Basic
    @Column(name = "file_name")
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Basic
    @Column(name = "file_path")
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Basic
    @Column(name = "file_size")
    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    @Basic
    @Column(name = "file_suffix")
    public String getFileSuffix() {
        return fileSuffix;
    }

    public void setFileSuffix(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    @Basic
    @Column(name = "small_file_name")
    public String getSmallFileName() {
        return smallFileName;
    }

    public void setSmallFileName(String smallFileName) {
        this.smallFileName = smallFileName;
    }

    @Basic
    @Column(name = "small_file_path")
    public String getSmallFilePath() {
        return smallFilePath;
    }

    public void setSmallFilePath(String smallFilePath) {
        this.smallFilePath = smallFilePath;
    }

    @Basic
    @Column(name = "create_user_id")
    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    @Basic
    @Column(name = "create_user_name")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @Basic
    @Column(name = "ctime")
    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "update_user_id")
    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    @Basic
    @Column(name = "update_user_name")
    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Basic
    @Column(name = "utime")
    public Timestamp getUtime() {
        return utime;
    }

    public void setUtime(Timestamp utime) {
        this.utime = utime;
    }

    @Basic
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CarInsuranceFileInfo that = (CarInsuranceFileInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(inquiryId, that.inquiryId) &&
                Objects.equals(fileType, that.fileType) &&
                Objects.equals(originalFileName, that.originalFileName) &&
                Objects.equals(fileName, that.fileName) &&
                Objects.equals(filePath, that.filePath) &&
                Objects.equals(fileSize, that.fileSize) &&
                Objects.equals(fileSuffix, that.fileSuffix) &&
                Objects.equals(smallFileName, that.smallFileName) &&
                Objects.equals(smallFilePath, that.smallFilePath) &&
                Objects.equals(createUserId, that.createUserId) &&
                Objects.equals(createUserName, that.createUserName) &&
                Objects.equals(ctime, that.ctime) &&
                Objects.equals(updateUserId, that.updateUserId) &&
                Objects.equals(updateUserName, that.updateUserName) &&
                Objects.equals(utime, that.utime) &&
                Objects.equals(status, that.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, inquiryId, fileType, originalFileName, fileName, filePath, fileSize, fileSuffix, smallFileName, smallFilePath, createUserId, createUserName, ctime, updateUserId, updateUserName, utime, status);
    }
}
