package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 骗子投诉类
 * 
 * <AUTHOR>
 * 
 */
@Entity
@Table(name = "tyt_info_blocker")
public class BlockInfo implements Serializable {

	private static final long serialVersionUID = 283432946463063492L;
	private Long id;
	private Long infoId;// 信息id
	private String informerTel;
	private Long qq;
	private String blockerTel;
	private String blockerName;
	private String reason;
	private String address;
	private String note;
	private Integer status;
	private Timestamp ctime;
	private Integer platId;// 客户端标识
	private Integer verifyCause;
	private String cellPhone;
	private String complaintSource = "1";
	private String complaintType = "2";
	/**
	 * 动作来源，汉字标注：大厅列表、详情、相似货源、倒短
	 */
	private String beFrom;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "info_id")
	public Long getInfoId() {
		return infoId;
	}

	public void setInfoId(Long infoId) {
		this.infoId = infoId;
	}

	@Column(name = "informer_tel")
	public String getInformerTel() {
		return informerTel;
	}

	public void setInformerTel(String informerTel) {
		this.informerTel = informerTel;
	}

	@Column(name = "qq")
	public Long getQq() {
		return qq;
	}

	public void setQq(Long qq) {
		this.qq = qq;
	}

	@Column(name = "blocker_tel")
	public String getBlockerTel() {
		return blockerTel;
	}

	public void setBlockerTel(String blockerTel) {
		this.blockerTel = blockerTel;
	}

	@Column(name = "blocker_name")
	public String getBlockerName() {
		return blockerName;
	}

	public void setBlockerName(String blockerName) {
		this.blockerName = blockerName;
	}

	@Column(name = "reason")
	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	@Column(name = "address")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	@Column(name = "note")
	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "ctime")
	public Timestamp getCtime() {
		return ctime;
	}

	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}

	@Column(name = "plat_id")
	public Integer getPlatId() {
		return platId;
	}

	public void setPlatId(Integer platId) {
		this.platId = platId;
	}

	@Override
	public String toString() {
		return " transportAccuse [id:" + id + "informerTel:" + informerTel
				+ " blockerTel:" + blockerTel + " blockerName:" + blockerName
				+ " qq:" + qq + " infoId:" + infoId + " reason:" + reason
				+ " status:" + status + " address:" + address + " note:" + note
				+ " ctime:" + ctime + " platId:" + platId + "]";
	}

	public String info() {
		return " transportAccuse [informerTel:" + informerTel + " blockerTel:"
				+ blockerTel + " blockerName:" + blockerName + " qq:" + qq
				+ " infoId:" + infoId + " reason:" + reason + " status:"
				+ status + " platId:" + platId + "]";
	}

	@Column(name = "verify_cause")
	public Integer getVerifyCause() {
		return verifyCause;
	}

	public void setVerifyCause(Integer verifyCause) {
		this.verifyCause = verifyCause;
	}

	@Column(name = "cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "complaint_source")
	public String getComplaintSource() {
		return complaintSource;
	}

	public void setComplaintSource(String complaintSource) {
		this.complaintSource = complaintSource;
	}

	@Column(name = "complaint_type")
	public String getComplaintType() {
		return complaintType;
	}

	public void setComplaintType(String complaintType) {
		this.complaintType = complaintType;
	}

	@Column(name = "be_from")
	public String getBeFrom() {
		return beFrom;
	}

	public void setBeFrom(String beFrom) {
		this.beFrom = beFrom;
	}
}
