package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 反馈信息实体
 * <AUTHOR>
 *
 */
@Entity
@Table(name="tyt_req_url_rule")
public class TytReqUrlRule implements Serializable {
	
	private static final long serialVersionUID = 1L;

	private Long id;
	private String reqUrl;
	private Integer repeatCycle;
	private String secretConstructor;
	private Integer maxCapacity;
	private Double repeatRate;
	private String filterCondition;
	private Integer status;
	private Date ctime;
	private Date mtime;


	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "req_url")
	public String getReqUrl() {
		return reqUrl;
	}

	public void setReqUrl(String reqUrl) {
		this.reqUrl = reqUrl;
	}

	@Column(name = "repeat_cycle")
	public Integer getRepeatCycle() {
		return repeatCycle;
	}

	public void setRepeatCycle(Integer repeatCycle) {
		this.repeatCycle = repeatCycle;
	}

	@Column(name = "secret_constructor")
	public String getSecretConstructor() {
		return secretConstructor;
	}

	public void setSecretConstructor(String secretConstructor) {
		this.secretConstructor = secretConstructor;
	}

	@Column(name = "max_capacity")
	public Integer getMaxCapacity() {
		return maxCapacity;
	}

	public void setMaxCapacity(Integer maxCapacity) {
		this.maxCapacity = maxCapacity;
	}

	@Column(name = "repeat_rate")
	public Double getRepeatRate() {
		return repeatRate;
	}

	public void setRepeatRate(Double repeatRate) {
		this.repeatRate = repeatRate;
	}

	@Column(name = "filter_condition")
	public String getFilterCondition() {
		return filterCondition;
	}

	public void setFilterCondition(String filterCondition) {
		this.filterCondition = filterCondition;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Override
	public String toString() {
		return "{"
				+ "\"id\":"
				+ id + ","
				+ "\"reqUrl\":"
				+ "\"" + reqUrl + "\","
				+ "\"repeatCycle\":"
				+ repeatCycle + ","
				+ "\"secretConstructor\":"
				+ "\"" + secretConstructor + "\","
				+ "\"maxCapacity\":"
				+ maxCapacity + ","
				+ "\"repeatRate\":"
				+ repeatRate + ","
				+ "\"status\":"
				+ status + ","
				+ "\"ctime\":"
				+ "\"" + ctime + "\","
				+ "\"mtime\":"
				+ "\"" + mtime + "\""
				+ "}";
	}
}
