package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name="tyt_transport_auto_nullify")
public class TransportNullifyBean implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long transId;
	private Long userId;
	private String startPoint;
	private String destPoint;

	// 出发地 目的地 省市区
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String destProvinc;
	private String destCity;
	private String destArea;

	private String taskContent;
	private Date ctime;
	private Date mtime;
	private String uploadCellPhone;
	private Integer  platId;
	private Integer  isInfoFee;
	private String  matchingKeyword;
	private Integer  state;
	
	//新增货物备注
	private String taskRemark;

	@Id
	@GeneratedValue
	@Column(name="id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="trans_id")
	public Long getTransId() {
		return transId;
	}

	public void setTransId(Long trans_id) {
		this.transId = trans_id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="start_point")
	public String getStartPoint() {
		return startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}
	@Column(name="dest_point")
	public String getDestPoint() {
		return destPoint;
	}

	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}
	@Column(name="start_provinc")
	public String getStartProvinc() {
		return startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}
	@Column(name="start_city")
	public String getStartCity() {
		return startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}
	@Column(name="start_area")
	public String getStartArea() {
		return startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}
	@Column(name="dest_provinc")
	public String getDestProvinc() {
		return destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}
	@Column(name="dest_city")
	public String getDestCity() {
		return destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}
	@Column(name="dest_area")
	public String getDestArea() {
		return destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}
	@Column(name="task_content")
	public String getTaskContent() {
		return taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name="mtime")
	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Column(name="upload_cellPhone")
	public String getUploadCellPhone() {
		return uploadCellPhone;
	}

	public void setUploadCellPhone(String uploadCellPhone) {
		this.uploadCellPhone = uploadCellPhone;
	}
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}

	public void setPlatId(Integer platId) {
		this.platId = platId;
	}
	@Column(name="is_info_fee")
	public Integer getIsInfoFee() {
		return isInfoFee;
	}

	public void setIsInfoFee(Integer isInfoFee) {
		this.isInfoFee = isInfoFee;
	}
	@Column(name="matching_keyword")
	public String getMatchingKeyword() {
		return matchingKeyword;
	}

	public void setMatchingKeyword(String matchingKeyword) {
		this.matchingKeyword = matchingKeyword;
	}
	@Column(name="state")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}
	
	@Column(name="task_remark")
	public String getTaskRemark() {
		return taskRemark;
	}

	public void setTaskRemark(String taskRemark) {
		this.taskRemark = taskRemark;
	}
	
}
