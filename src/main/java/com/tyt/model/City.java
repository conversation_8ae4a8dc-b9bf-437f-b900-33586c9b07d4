package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "tyt_city", catalog = "tyt")
public class City implements Serializable {

    private static final long serialVersionUID = 6017414689552876549L;
    /**
     * 主键id
     */
    private Long id;

    private String areaCode;

    private String parentAreaCode;

    private String level;
    /**
     * 区县名称
     */
    private String areaName;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 省份
     */
    private String province;

    /**
     * 区县名称
     */
    private String mapAreaName;
    /**
     * 市名称
     */
    private String mapCityName;
    /**
     * 省份
     */
    private String mapProvinceName;

    /**
     * rf
     */
    private String rf;
    /**
     * px
     */
    private String px;
    /**
     * py
     */
    private String py;
    /**
     *  经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 城市名称
     */
    private String shortName;

    /**
     * 城市名称
     */
    private String standardName;
    /**
     * 类别：1.完全匹配2.不完全匹配
     */
    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public City() {
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    @Column(name = "map_area_name")
    public String getMapAreaName() {
        return mapAreaName;
    }

    public void setMapAreaName(String mapAreaName) {
        this.mapAreaName = mapAreaName;
    }

    @Column(name = "map_city_name")
    public String getMapCityName() {
        return mapCityName;
    }

    public void setMapCityName(String mapCityName) {
        this.mapCityName = mapCityName;
    }

    @Column(name = "map_province_name")
    public String getMapProvinceName() {
        return mapProvinceName;
    }

    public void setMapProvinceName(String mapProvinceName) {
        this.mapProvinceName = mapProvinceName;
    }

    @Id
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "area_code")
    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }
    @Column(name = "parent_area_code")
    public String getParentAreaCode() {
        return parentAreaCode;
    }

    public void setParentAreaCode(String parentAreaCode) {
        this.parentAreaCode = parentAreaCode;
    }
    @Column(name = "level")
    public String getLevel() {
        return level;
    }
    public void setLevel(String level) {
        this.level = level;
    }

    @Column(name = "city_name")
    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }
    @Column(name = "rf")
    public String getRf() {
        return rf;
    }

    public void setRf(String rf) {
        this.rf = rf;
    }
    @Column(name = "px")
    public String getPx() {
        return px;
    }

    public void setPx(String px) {
        this.px = px;
    }
    @Column(name = "py")
    public String getPy() {
        return py;
    }

    public void setPy(String py) {
        this.py = py;
    }
    @Column(name = "longitude")
    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
    @Column(name = "latitude")
    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
    @Column(name = "short_name")
    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }
    @Column(name = "standard_name")
    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }
}
