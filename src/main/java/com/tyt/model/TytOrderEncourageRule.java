package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_order_encourage_rule")
public class TytOrderEncourageRule implements Serializable {

    private static final long serialVersionUID = 571028043687488856L;
    private Long id;
    private Long activityId;
    private Integer beginNum;
    private Integer endNum;
    private Integer completeOrders;
    private Integer giveDays;
    private Long giveGoodsId;
    private String ruleContent;
    private String remark;
    private Date ctime;
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Column(name = "begin_num")
    public Integer getBeginNum() {
        return beginNum;
    }

    public void setBeginNum(Integer beginNum) {
        this.beginNum = beginNum;
    }

    @Column(name = "end_num")
    public Integer getEndNum() {
        return endNum;
    }

    public void setEndNum(Integer endNum) {
        this.endNum = endNum;
    }

    @Column(name = "complete_orders")
    public Integer getCompleteOrders() {
        return completeOrders;
    }

    public void setCompleteOrders(Integer completeOrders) {
        this.completeOrders = completeOrders;
    }

    @Column(name = "give_days")
    public Integer getGiveDays() {
        return giveDays;
    }

    public void setGiveDays(Integer giveDays) {
        this.giveDays = giveDays;
    }

    @Column(name = "give_goods_id")
    public Long getGiveGoodsId() {
        return giveGoodsId;
    }

    public void setGiveGoodsId(Long giveGoodsId) {
        this.giveGoodsId = giveGoodsId;
    }

    @Column(name = "rule_content")
    public String getRuleContent() {
        return ruleContent;
    }

    public void setRuleContent(String ruleContent) {
        this.ruleContent = ruleContent;
    }

    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "TytOrderEncourageRule{" +
                "id=" + id +
                ", activityId=" + activityId +
                ", beginNum=" + beginNum +
                ", endNum=" + endNum +
                ", completeOrders=" + completeOrders +
                ", giveDays=" + giveDays +
                ", giveGoodsId=" + giveGoodsId +
                ", ruleContent='" + ruleContent + '\'' +
                ", remark='" + remark + '\'' +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
