package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_notice_popup_templ")
public class TytNoticePopupTempl implements Serializable {
    private static final long serialVersionUID = 5524260488540548795L;
    private Long id;
    private Integer type1;
    private Integer type2;
    private String remark;
    private String style;
    private String title;
    private String titleColor;
    private String masterContent;
    private String masterContentColor;
    private String leftButtonContent;
    private String leftButtonLink;
    private String leftButtonType;
    private String leftButtonBackcolor;
    private String leftButtonContentColor;
    private String rightButtonContent;
    private String rightButtonLink;
    private String rightButtonType;
    private String rightButtonBackcolor;
    private String rightButtonContentColor;
    private String guideContent;
    private String guideContentLink;
    private String guideContentType;
    private String guideContentBackcolor;
    private String guideContentColor;
    private String guideButtonType;
    private String guideButtonContent;
    private String guideButtonBackcolor;
    private String guideButtonContentColor;
    private String guideButtonLink;
    private String pictureAddr;
    private Integer status;
    private Date ctime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "type1")
    public Integer getType1() {
        return type1;
    }

    public void setType1(Integer type1) {
        this.type1 = type1;
    }

    @Column(name = "type2")
    public Integer getType2() {
        return type2;
    }

    public void setType2(Integer type2) {
        this.type2 = type2;
    }

    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "style")
    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    @Column(name = "title")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Column(name = "title_color")
    public String getTitleColor() {
        return titleColor;
    }

    public void setTitleColor(String titleColor) {
        this.titleColor = titleColor;
    }

    @Column(name = "master_content")
    public String getMasterContent() {
        return masterContent;
    }

    public void setMasterContent(String masterContent) {
        this.masterContent = masterContent;
    }

    @Column(name = "master_content_color")
    public String getMasterContentColor() {
        return masterContentColor;
    }

    public void setMasterContentColor(String masterContentColor) {
        this.masterContentColor = masterContentColor;
    }

    @Column(name = "left_button_content")
    public String getLeftButtonContent() {
        return leftButtonContent;
    }

    public void setLeftButtonContent(String leftButtonContent) {
        this.leftButtonContent = leftButtonContent;
    }

    @Column(name = "left_button_link")
    public String getLeftButtonLink() {
        return leftButtonLink;
    }

    public void setLeftButtonLink(String leftButtonLink) {
        this.leftButtonLink = leftButtonLink;
    }

    @Column(name = "left_button_backcolor")
    public String getLeftButtonBackcolor() {
        return leftButtonBackcolor;
    }

    public void setLeftButtonBackcolor(String leftButtonBackcolor) {
        this.leftButtonBackcolor = leftButtonBackcolor;
    }

    @Column(name = "right_button_content")
    public String getRightButtonContent() {
        return rightButtonContent;
    }

    public void setRightButtonContent(String rightButtonContent) {
        this.rightButtonContent = rightButtonContent;
    }

    @Column(name = "left_button_content_color")
    public String getLeftButtonContentColor() {
        return leftButtonContentColor;
    }

    public void setLeftButtonContentColor(String leftButtonContentColor) {
        this.leftButtonContentColor = leftButtonContentColor;
    }

    @Column(name = "right_button_link")
    public String getRightButtonLink() {
        return rightButtonLink;
    }

    public void setRightButtonLink(String rightButtonLink) {
        this.rightButtonLink = rightButtonLink;
    }

    @Column(name = "right_button_backcolor")
    public String getRightButtonBackcolor() {
        return rightButtonBackcolor;
    }

    public void setRightButtonBackcolor(String rightButtonBackcolor) {
        this.rightButtonBackcolor = rightButtonBackcolor;
    }

    @Column(name = "right_button_content_color")
    public String getRightButtonContentColor() {
        return rightButtonContentColor;
    }

    public void setRightButtonContentColor(String rightButtonContentColor) {
        this.rightButtonContentColor = rightButtonContentColor;
    }

    @Column(name = "guide_content")
    public String getGuideContent() {
        return guideContent;
    }

    public void setGuideContent(String guideContent) {
        this.guideContent = guideContent;
    }

    @Column(name = "guide_content_link")
    public String getGuideContentLink() {
        return guideContentLink;
    }

    public void setGuideContentLink(String guideContentLink) {
        this.guideContentLink = guideContentLink;
    }

    @Column(name = "guide_content_type")
    public String getGuideContentType() {
        return guideContentType;
    }

    public void setGuideContentType(String guideContentType) {
        this.guideContentType = guideContentType;
    }

    @Column(name = "guide_content_backcolor")
    public String getGuideContentBackcolor() {
        return guideContentBackcolor;
    }

    public void setGuideContentBackcolor(String guideContentBackcolor) {
        this.guideContentBackcolor = guideContentBackcolor;
    }

    @Column(name = "guide_content_color")
    public String getGuideContentColor() {
        return guideContentColor;
    }

    public void setGuideContentColor(String guideContentColor) {
        this.guideContentColor = guideContentColor;
    }

    @Column(name = "guide_button_content")
    public String getGuideButtonContent() {
        return guideButtonContent;
    }

    public void setGuideButtonContent(String guideButtonContent) {
        this.guideButtonContent = guideButtonContent;
    }

    @Column(name = "guide_button_backcolor")
    public String getGuideButtonBackcolor() {
        return guideButtonBackcolor;
    }

    public void setGuideButtonBackcolor(String guideButtonBackcolor) {
        this.guideButtonBackcolor = guideButtonBackcolor;
    }

    @Column(name = "guide_button_content_color")
    public String getGuideButtonContentColor() {
        return guideButtonContentColor;
    }

    public void setGuideButtonContentColor(String guideButtonContentColor) {
        this.guideButtonContentColor = guideButtonContentColor;
    }

    @Column(name = "guide_button_link")
    public String getGuideButtonLink() {
        return guideButtonLink;
    }

    public void setGuideButtonLink(String guideButtonLink) {
        this.guideButtonLink = guideButtonLink;
    }

    @Column(name = "picture_addr")
    public String getPictureAddr() {
        return pictureAddr;
    }

    public void setPictureAddr(String pictureAddr) {
        this.pictureAddr = pictureAddr;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "left_button_type")
    public String getLeftButtonType() {
        return leftButtonType;
    }

    public void setLeftButtonType(String leftButtonType) {
        this.leftButtonType = leftButtonType;
    }

    @Column(name = "right_button_type")
    public String getRightButtonType() {
        return rightButtonType;
    }

    public void setRightButtonType(String rightButtonType) {
        this.rightButtonType = rightButtonType;
    }

    @Column(name = "guide_button_type")
    public String getGuideButtonType() {
        return guideButtonType;
    }

    public void setGuideButtonType(String guideButtonType) {
        this.guideButtonType = guideButtonType;
    }

    @Override
    public String toString() {
        return "TytNoticePopupTempl{" +
                "id=" + id +
                ", type1=" + type1 +
                ", type2=" + type2 +
                ", remark='" + remark + '\'' +
                ", style='" + style + '\'' +
                ", title='" + title + '\'' +
                ", titleColor='" + titleColor + '\'' +
                ", masterContent='" + masterContent + '\'' +
                ", masterContentColor='" + masterContentColor + '\'' +
                ", leftButtonContent='" + leftButtonContent + '\'' +
                ", leftButtonLink='" + leftButtonLink + '\'' +
                ", leftButtonType='" + leftButtonType + '\'' +
                ", leftButtonBackcolor='" + leftButtonBackcolor + '\'' +
                ", leftButtonContentColor='" + leftButtonContentColor + '\'' +
                ", rightButtonContent='" + rightButtonContent + '\'' +
                ", rightButtonLink='" + rightButtonLink + '\'' +
                ", rightButtonType='" + rightButtonType + '\'' +
                ", rightButtonBackcolor='" + rightButtonBackcolor + '\'' +
                ", rightButtonContentColor='" + rightButtonContentColor + '\'' +
                ", guideContent='" + guideContent + '\'' +
                ", guideContentColor='" + guideContentColor + '\'' +
                ", guideButtonType='" + guideButtonType + '\'' +
                ", guideButtonContent='" + guideButtonContent + '\'' +
                ", guideButtonBackcolor='" + guideButtonBackcolor + '\'' +
                ", guideButtonContentColor='" + guideButtonContentColor + '\'' +
                ", guideButtonLink='" + guideButtonLink + '\'' +
                ", pictureAddr='" + pictureAddr + '\'' +
                ", status=" + status +
                ", ctime=" + ctime +
                '}';
    }
}
