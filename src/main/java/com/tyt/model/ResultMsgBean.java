package com.tyt.model;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.util.ReturnCodeConstant;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:57
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResultMsgBean implements Serializable {

    private static final long serialVersionUID = 1L;

	/**** 正确结果 **/
    public static final int OK = 200;
    public static final String OK_MSG = "操作成功";

    /**** 错误结果 **/
    public static final int ERROR = 500;

    private int code=OK;
    private String msg;
    private Object data;
    private String time;
    private String hatch;
    private Long totalSize;
    //为了扩展兼容以前的接口，额外增加的字段
    private Object extraData;
    private TytNoticePopupTempl noticeData;

    @Getter
    @Setter
    private Date ts = new Date();
    
	public ResultMsgBean(){
	}
    public ResultMsgBean(int code, String msg) {
        super();
        this.code = code;
        this.msg = msg;
    }
    public ResultMsgBean(int code, String msg, Object data) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
    public ResultMsgBean(int code, String msg, Object data,TytNoticePopupTempl noticeData) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.noticeData = noticeData;
    }
	public Integer getCode() {
        return code;
    }
    public void setCode(int code) {
        this.code = code;
    }
    public String getMsg() {
        return msg;
    }
    public void setMsg(String msg) {
        this.msg = msg;
    }


    /**
     * @return the data
     */
    public Object getData() {
        return data;
    }
    /**
     * @param data the data to set
     */
    public void setData(Object data) {
        this.data = data;
    }
    
    
    public String getTime() {
		return time;
	}
	public void setTime(String time) {
		this.time = time;
	}
	
	public String getHatch() {
		return hatch;
	}
	public void setHatch(String hatch) {
		this.hatch = hatch;
	}
	
	public Long getTotalSize() {
		return totalSize;
	}
	public void setTotalSize(Long totalSize) {
		this.totalSize = totalSize;
	}
	@Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public Object getExtraData() {
        return extraData;
    }

    public void setExtraData(Object extraData) {
        this.extraData = extraData;
    }

    public TytNoticePopupTempl getNoticeData() {
        return noticeData;
    }

    public void setNoticeData(TytNoticePopupTempl noticeData) {
        this.noticeData = noticeData;
    }

    /**
     * 返回成功
     * @param obj
     */
    public static ResultMsgBean successResponse(Object obj){
        ResultMsgBean resp = new ResultMsgBean(ReturnCodeConstant.OK,"success");
        resp.setData(obj);
        return resp;
    }

    /**
     * 返回成功
     * @return
     */
    public static ResultMsgBean successResponse(){
        ResultMsgBean resp = new ResultMsgBean(ReturnCodeConstant.OK,"success");
        return resp;
    }

    /**
     * 返回失败
     */
    public static ResultMsgBean failResponse(int errorCode, String errorMsg) {
        ResultMsgBean resp = new ResultMsgBean(errorCode,errorMsg);
        return resp;
    }

    /**
     * 返回失败
     */
    public static ResultMsgBean failResponse(Throwable t) {

        TytException customExc = TytException.createException(t);
        Integer errorCode = customExc.getErrorCode();
        String errorMsg = customExc.getErrorMsg();

        ResultMsgBean resp = new ResultMsgBean(errorCode, errorMsg);
        return resp;
    }

    /**
     * 返回失败
     */
    public static ResultMsgBean failResponse(ResponseCode responseCode) {
        TytException exce = TytException.createException(responseCode);
        return failResponse(exce);
    }

    public boolean isSuccess() {
        return code == OK;
    }
}
