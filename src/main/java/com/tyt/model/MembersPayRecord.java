package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="members_pay_record")
public class MembersPayRecord implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -2024110965093997843L;
	private Long id;//主键id
	private Long userId;//用户id
	private Integer changeType;//变更类型 有：1.平台缴费变更 2.后台缴费变更  3.异常扣减变更  4.vip延期变更  5.活动加赠变更 6.其他加赠变更
	private String changeRemark;//变更备注
	private Long changeAccountId;//变更业务id (对应缴费表id)
	private Integer changeDays;//变更天数
	private Date beforeMembersStartDate;//变更前会员开始日期（变更前会员开始日期结束日期没有的，空）
	private Date beforeMembersEndDate;//变更前会员结束日期（变更前会员开始日期结束日期没有的，空）
	private Date afterMembersStartDate;//变更后会员开始日期
	private Date afterMembersEndDate;//变更后会员结束日期
	private Integer beforeChangeStatus;//变更前用户状态1VIP有效、2VIP到期、3试用
	private Integer afterChangeStatus;//变更后用户状态1VIP有效、2VIP到期、3试用
	private Integer payNumber;//缴费次数（除了首费和续费，其他原因的变更不在填写缴费次数）
	private Long operatorId;//操作人id
	private String operatorName;//操作人姓名
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="change_type")
	public Integer getChangeType() {
		return changeType;
	}
	public void setChangeType(Integer changeType) {
		this.changeType = changeType;
	}
	@Column(name="change_remark")
	public String getChangeRemark() {
		return changeRemark;
	}
	public void setChangeRemark(String changeRemark) {
		this.changeRemark = changeRemark;
	}
	@Column(name="change_account_id")
	public Long getChangeAccountId() {
		return changeAccountId;
	}
	public void setChangeAccountId(Long changeAccountId) {
		this.changeAccountId = changeAccountId;
	}
	@Column(name="change_days")
	public Integer getChangeDays() {
		return changeDays;
	}
	public void setChangeDays(Integer changeDays) {
		this.changeDays = changeDays;
	}
	@Column(name="before_members_start_date")
	public Date getBeforeMembersStartDate() {
		return beforeMembersStartDate;
	}
	public void setBeforeMembersStartDate(Date beforeMembersStartDate) {
		this.beforeMembersStartDate = beforeMembersStartDate;
	}
	@Column(name="before_members_end_date")
	public Date getBeforeMembersEndDate() {
		return beforeMembersEndDate;
	}
	public void setBeforeMembersEndDate(Date beforeMembersEndDate) {
		this.beforeMembersEndDate = beforeMembersEndDate;
	}
	@Column(name="after_members_start_date")
	public Date getAfterMembersStartDate() {
		return afterMembersStartDate;
	}
	public void setAfterMembersStartDate(Date afterMembersStartDate) {
		this.afterMembersStartDate = afterMembersStartDate;
	}
	@Column(name="after_members_end_date")
	public Date getAfterMembersEndDate() {
		return afterMembersEndDate;
	}
	public void setAfterMembersEndDate(Date afterMembersEndDate) {
		this.afterMembersEndDate = afterMembersEndDate;
	}
	@Column(name="before_change_status")
	public Integer getBeforeChangeStatus() {
		return beforeChangeStatus;
	}
	public void setBeforeChangeStatus(Integer beforeChangeStatus) {
		this.beforeChangeStatus = beforeChangeStatus;
	}
	@Column(name="after_change_status")
	public Integer getAfterChangeStatus() {
		return afterChangeStatus;
	}
	public void setAfterChangeStatus(Integer afterChangeStatus) {
		this.afterChangeStatus = afterChangeStatus;
	}
	@Column(name="pay_number")
	public Integer getPayNumber() {
		return payNumber;
	}
	public void setPayNumber(Integer payNumber) {
		this.payNumber = payNumber;
	}
	@Column(name="operator_id")
	public Long getOperatorId() {
		return operatorId;
	}
	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}
	@Column(name="operator_name")
	public String getOperatorName() {
		return operatorName;
	}
	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}

	
}
