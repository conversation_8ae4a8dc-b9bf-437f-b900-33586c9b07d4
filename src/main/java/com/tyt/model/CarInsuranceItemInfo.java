package com.tyt.model;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * @ClassName CarInsuranceItemInfo
 * @Description 车险询价保项保额信息对象
 * <AUTHOR>
 * @Date 2019-03-15 18:41
 * @Version 1.0
 */
@Entity
@Table(name = "car_insurance_item_info", schema = "tyt", catalog = "")
public class CarInsuranceItemInfo {
    private Integer id;
    private Long inquiryId;
    private Long userId;
    private Integer insuranceType;
    private Integer insuranceItem;
    private Integer amtCurrency;
    private Timestamp ctime;
    private Timestamp utime;
    private Integer status;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "inquiry_id")
    public Long getInquiryId() {
        return inquiryId;
    }

    public void setInquiryId(Long inquiryId) {
        this.inquiryId = inquiryId;
    }

    @Basic
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "insurance_type")
    public Integer getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(Integer insuranceType) {
        this.insuranceType = insuranceType;
    }

    @Basic
    @Column(name = "insurance_item")
    public Integer getInsuranceItem() {
        return insuranceItem;
    }

    public void setInsuranceItem(Integer insuranceItem) {
        this.insuranceItem = insuranceItem;
    }

    @Basic
    @Column(name = "amt_currency")
    public Integer getAmtCurrency() {
        return amtCurrency;
    }

    public void setAmtCurrency(Integer amtCurrency) {
        this.amtCurrency = amtCurrency;
    }

    @Basic
    @Column(name = "ctime")
    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "utime")
    public Timestamp getUtime() {
        return utime;
    }

    public void setUtime(Timestamp utime) {
        this.utime = utime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CarInsuranceItemInfo that = (CarInsuranceItemInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(inquiryId, that.inquiryId) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(insuranceType, that.insuranceType) &&
                Objects.equals(insuranceItem, that.insuranceItem) &&
                Objects.equals(amtCurrency, that.amtCurrency) &&
                Objects.equals(ctime, that.ctime) &&
                Objects.equals(utime, that.utime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, inquiryId, userId, insuranceType, insuranceItem, amtCurrency, ctime, utime);
    }

    @Basic
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
