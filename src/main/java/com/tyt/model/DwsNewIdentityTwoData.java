package com.tyt.model;

import com.tyt.adposition.enums.DwsIdentityTypeEnum;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/06/12 11:24
 */
@Data
@Entity
@Table(name = "dws_new_identity_two_data", catalog = "tyt_recommend")
public class DwsNewIdentityTwoData implements Serializable {

    /**
     * id
     */
    @Id
    @Column(name = "id")
    private Integer id;

    /**
     * 日期
     */
    @Column(name = "cal_dt")
    private Date calDt;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 1:物流公司 2:货站 3:企业货主 4:个人货主
     *
     * @see DwsIdentityTypeEnum
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 主体名称
     */
    @Column(name = "main_name")
    private String mainName;

    /**
     * 维护人
     */
    @Column(name = "user_follow")
    private String userFollow;

    /**
     * 优车签约状态 0：未签约 1：已签约
     */
    @Column(name = "auth_status")
    private Integer authStatus;

    /**
     * 创建日期
     */
    @Column(name = "ctime")
    private Date ctime;
}
