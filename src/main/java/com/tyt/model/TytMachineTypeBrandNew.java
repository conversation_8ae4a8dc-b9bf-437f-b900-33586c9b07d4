package com.tyt.model;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-07-20 13:30:30
 * @description
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "tyt_machine_type_brand_new", catalog = "tyt")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TytMachineTypeBrandNew implements java.io.Serializable {

    /**
     * ID
     */
    private Integer id;
    /**
     * 显示名称
     */
    private String showName;
    /**
     * 一级分类
     */
    private String topClass;
    /**
     * 二级分类
     */
    private String secondClass;
    /**
     * 具体品牌
     */
    private String brand;
    /**
     * 品牌型号
     */
    private String brandType;
    /**
     * 一级型号
     */
    private String topType;
    /**
     * 二级型号
     */
    private String secondType;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 长度
     */
    private BigDecimal length;
    /**
     * 宽度
     */
    private BigDecimal width;
    /**
     * 高度
     */
    private BigDecimal height;

    /**
     * 备注
     */
    private String remarks;
    /**
     * 通用匹配项，用于在精确匹配匹配不到数据的时候进行模糊匹配
     */
    private String generalMatchesItem;
    /**
     * 排序分值
     */
    private BigDecimal score;
    /**
     * 是否显示，0-不显示，1-显示
     */
    private Integer display;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 更新时间
     */
    private Date mtime;
    /**
     * 6270新增字段 (为了不影响老版本)设备名称
     */
    private String showNameNew;
    /**
     * 6270新增字段（为了不影响老版本） 长宽高重
     */
    private String showNameArg;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @Column(name = "show_name")
    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }
    @Column(name = "brand")
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    @Column(name = "brand_type")
    public String getBrandType() {
        return brandType;
    }

    public void setBrandType(String brandType) {
        this.brandType = brandType;
    }



    @Column(name = "top_class")
    public String getTopClass() {
        return topClass;
    }

    public void setTopClass(String topClass) {
        this.topClass = topClass;
    }
    @Column(name = "second_class")
    public String getSecondClass() {
        return secondClass;
    }

    public void setSecondClass(String secondClass) {
        this.secondClass = secondClass;
    }

    @Column(name = "top_type")
    public String getTopType() {
        return topType;
    }

    public void setTopType(String topType) {
        this.topType = topType;
    }
    @Column(name = "second_type")
    public String getSecondType() {
        return secondType;
    }

    public void setSecondType(String secondType) {
        this.secondType = secondType;
    }
    @Column(name = "weight")
    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }
    @Column(name = "length")
    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }
    @Column(name = "width")
    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }
    @Column(name = "height")
    public BigDecimal getHeight() {
        return height;
    }
    public void setHeight(BigDecimal height) {
        this.height = height;
    }
    @Column(name = "remarks")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
    @Column(name = "general_matches_item")
    public String getGeneralMatchesItem() {
        return generalMatchesItem;
    }

    public void setGeneralMatchesItem(String generalMatchesItem) {
        this.generalMatchesItem = generalMatchesItem;
    }
    @Column(name = "score")
    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }
    @Column(name = "display")
    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Transient
    public String getShowNameNew() {
        return showNameNew;
    }

    public void setShowNameNew(String showNameNew) {
        this.showNameNew = showNameNew;
    }

    @Transient
    public String getShowNameArg() {
        return showNameArg;
    }

    public void setShowNameArg(String showNameArg) {
        this.showNameArg = showNameArg;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
