package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.util.TimeUtil;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "tyt_user_identity_auth")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TytUserIdentityAuth implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Long userId;
	private String mobile;
	private Integer identityStatus = 0;
	private Integer userClass;
	private Integer identityType;
	private String trueName;
	private String sex;
	private String idCard;
	private String nation;
	private String address;
	private Integer infoStatus = 0;
	private String infoFailureReason;
	private String mainUrl;
	private Integer mainStatus = 0;
	private String mainFailureReason;
	private String backUrl;
	private Integer backStatus = 0;
	private String backFailueReason;
	private String qualificationsUrl;
	private String enterpriseName;
	private Integer enterpriseType;
	private Integer enterpriseAmount ;
	private String enterprisePhone;
	private String licenseUrl;
	private Integer licenseStatus = 0;
	private String licenseFailureReason;
	private String workType ;
	private Date ctime;
	private Integer examineStatus = 0;
	private Date examineTime;
	private Long examineUserId ;
	private String examineUserName;
	private Date utime;
	private Integer dataType=0;
	private String platId;

	private String iPhotoUrl;
	private Integer iPhotoStatus=0;
	private String iPhotoFailureReason;
	private String enterpriseAuthLicenseUrl;
	private String enterpriseAuthCompanyName;
	private String enterpriseAuthCreditCode;
	private Integer enterpriseAuthStatus =0;
	private String enterpriseAuthFailureReason;
	private Long enterpriseExamineUserId;
	private String enterpriseExamineUserName;
	private Date enterpriseAuthCtime;
	private Date enterpriseAuthUtime;

	private String userAuthPath;
	private String enterpriseAuthPath;

	/**
	 * 实名二要素（0未认证;1认证成功;2认证失败）
	 */
	@Getter(onMethod_={@Column(name = "real_verify")})
	@Setter
	private Integer realVerify = 0;

	/**
	 * 人脸识别（0未认证;1认证成功;2认证失败）
	 */
	@Getter(onMethod_={@Column(name = "face_verify")})
	@Setter
	private Integer faceVerify = 0;

	/**
	 * 人脸识别活体照片链接
	 */
	@Getter(onMethod_={@Column(name = "face_verify_image_url")})
	@Setter
	private String faceVerifyImageUrl;

	/**
	 * 身份证有效期
	 */
	@Getter(onMethod_={@Column(name = "id_card_valid_date")})
	@Setter
	private Date idCardValidDate;

	/**
	 * 是否是长期（0否；1是）
	 */
	@Getter(onMethod_={@Column(name = "id_card_long_term")})
	@Setter
	private Integer idCardLongTerm = 0;

	public void init(Integer sourceUserClassValue, Integer userIdentityTypeValue) {
		this.setAddress(null);
		this.setBackFailueReason(null);
		this.setBackStatus(0);
		this.setBackUrl(null);
		this.setCtime(new Date());
		this.setEnterpriseAmount(null);
		this.setEnterpriseName(null);
		this.setEnterprisePhone(null);
		this.setEnterpriseType(null);
		this.setExamineStatus(0);
		this.setExamineTime(null);
		this.setExamineUserId(null);
		this.setExamineUserName(null);
		this.setIdCard(null);
		this.setIdentityStatus(0);
		this.setInfoFailureReason(null);
		this.setInfoStatus(0);
		this.setLicenseFailureReason(null);
		this.setLicenseStatus(0);
		this.setLicenseUrl(null);
		this.setMainFailureReason(null);
		this.setMainStatus(0);
		this.setMainUrl(null);
		this.setNation(null);
		this.setQualificationsUrl(null);
		this.setSex(null);
		this.setTrueName(null);
		this.setUtime(new Date());
		this.setWorkType(null);
		this.setUserClass(sourceUserClassValue);
		this.setIdentityType(userIdentityTypeValue);
		this.setiPhotoStatus(0);
		this.setiPhotoFailureReason(null);
		this.setiPhotoUrl(null);
	}

	/** default constructor */
	public TytUserIdentityAuth() {
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "mobile")
	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	@Column(name = "identity_status")
	public Integer getIdentityStatus() {
		return this.identityStatus;
	}

	public void setIdentityStatus(Integer identityStatus) {
		this.identityStatus = identityStatus;
	}

	@Column(name = "user_class")
	public Integer getUserClass() {
		return this.userClass;
	}

	public void setUserClass(Integer userClass) {
		this.userClass = userClass;
	}

	@Column(name = "identity_type")
	public Integer getIdentityType() {
		return this.identityType;
	}

	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

	@Column(name = "true_name")
	public String getTrueName() {
		return this.trueName;
	}

	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}

	@Column(name = "sex")
	public String getSex() {
		return this.sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	@Column(name = "id_card")
	public String getIdCard() {
		return this.idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	@Column(name = "nation")
	public String getNation() {
		return this.nation;
	}

	public void setNation(String nation) {
		this.nation = nation;
	}

	@Column(name = "address")
	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	@Column(name = "info_status")
	public Integer getInfoStatus() {
		return this.infoStatus;
	}

	public void setInfoStatus(Integer infoStatus) {
		this.infoStatus = infoStatus;
	}

	@Column(name = "info_failure_reason")
	public String getInfoFailureReason() {
		return this.infoFailureReason;
	}

	public void setInfoFailureReason(String infoFailureReason) {
		this.infoFailureReason = infoFailureReason;
	}

	@Column(name = "main_url")
	public String getMainUrl() {
		return this.mainUrl;
	}

	public void setMainUrl(String mainUrl) {
		this.mainUrl = mainUrl;
	}

	@Column(name = "main_status")
	public Integer getMainStatus() {
		return this.mainStatus;
	}

	public void setMainStatus(Integer mainStatus) {
		this.mainStatus = mainStatus;
	}

	@Column(name = "main_failure_reason")
	public String getMainFailureReason() {
		return this.mainFailureReason;
	}

	public void setMainFailureReason(String mainFailureReason) {
		this.mainFailureReason = mainFailureReason;
	}

	@Column(name = "back_url")
	public String getBackUrl() {
		return this.backUrl;
	}

	public void setBackUrl(String backUrl) {
		this.backUrl = backUrl;
	}

	@Column(name = "back_status")
	public Integer getBackStatus() {
		return this.backStatus;
	}

	public void setBackStatus(Integer backStatus) {
		this.backStatus = backStatus;
	}

	@Column(name = "back_failue_reason")
	public String getBackFailueReason() {
		return this.backFailueReason;
	}

	public void setBackFailueReason(String backFailueReason) {
		this.backFailueReason = backFailueReason;
	}

	@Column(name = "qualifications_url")
	public String getQualificationsUrl() {
		return this.qualificationsUrl;
	}

	public void setQualificationsUrl(String qualificationsUrl) {
		this.qualificationsUrl = qualificationsUrl;
	}

	@Column(name = "enterprise_name")
	public String getEnterpriseName() {
		return this.enterpriseName;
	}

	public void setEnterpriseName(String enterpriseName) {
		this.enterpriseName = enterpriseName;
	}

	@Column(name = "enterprise_type")
	public Integer getEnterpriseType() {
		return this.enterpriseType;
	}

	public void setEnterpriseType(Integer enterpriseType) {
		this.enterpriseType = enterpriseType;
	}

	@Column(name = "enterprise_amount")
	public Integer getEnterpriseAmount() {
		return this.enterpriseAmount;
	}

	public void setEnterpriseAmount(Integer enterpriseAmount) {
		this.enterpriseAmount = enterpriseAmount;
	}

	@Column(name = "enterprise_phone")
	public String getEnterprisePhone() {
		return this.enterprisePhone;
	}

	public void setEnterprisePhone(String enterprisePhone) {
		this.enterprisePhone = enterprisePhone;
	}

	@Column(name = "license_url")
	public String getLicenseUrl() {
		return this.licenseUrl;
	}

	public void setLicenseUrl(String licenseUrl) {
		this.licenseUrl = licenseUrl;
	}

	@Column(name = "license_status")
	public Integer getLicenseStatus() {
		return this.licenseStatus;
	}

	public void setLicenseStatus(Integer licenseStatus) {
		this.licenseStatus = licenseStatus;
	}

	@Column(name = "license_failure_reason")
	public String getLicenseFailureReason() {
		return this.licenseFailureReason;
	}

	public void setLicenseFailureReason(String licenseFailureReason) {
		this.licenseFailureReason = licenseFailureReason;
	}

	@Column(name = "work_type")
	public String getWorkType() {
		return this.workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "examine_status")
	public Integer getExamineStatus() {
		return this.examineStatus;
	}

	public void setExamineStatus(Integer examineStatus) {
		this.examineStatus = examineStatus;
	}

	@Column(name = "examine_time")
	public Date getExamineTime() {
		return this.examineTime;
	}

	public void setExamineTime(Date examineTime) {
		this.examineTime = examineTime;
	}

	@Column(name = "examine_user_id")
	public Long getExamineUserId() {
		return this.examineUserId;
	}

	public void setExamineUserId(Long examineUserId) {
		this.examineUserId = examineUserId;
	}

	@Column(name = "examine_user_name")
	public String getExamineUserName() {
		return this.examineUserName;
	}

	public void setExamineUserName(String examineUserName) {
		this.examineUserName = examineUserName;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name = "data_type")
	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	@Column(name = "i_photo_url")
	public String getiPhotoUrl() {
		return iPhotoUrl;
	}

	@Column(name = "i_photo_status")
	public Integer getiPhotoStatus() {
		return iPhotoStatus;
	}

	@Column(name = "i_photo_failure_reason")
	public String getiPhotoFailureReason() {
		return iPhotoFailureReason;
	}


	public void setiPhotoUrl(String iPhotoUrl) {
		this.iPhotoUrl = iPhotoUrl;
	}

    public void setiPhotoStatus(Integer iPhotoStatus) {
		this.iPhotoStatus = iPhotoStatus;
	}


	public void setiPhotoFailureReason(String iPhotoFailureReason) {
		this.iPhotoFailureReason = iPhotoFailureReason;
	}

    @Column(name = "plat_id")
    public String getPlatId() {
        return platId;
    }

    public void setPlatId(String platId) {
        this.platId = platId;
    }

	@Column(name = "enterprise_auth_license_url")
	public String getEnterpriseAuthLicenseUrl() {
		return enterpriseAuthLicenseUrl;
	}

	public void setEnterpriseAuthLicenseUrl(String enterpriseAuthLicenseUrl) {
		this.enterpriseAuthLicenseUrl = enterpriseAuthLicenseUrl;
	}

	@Column(name = "enterprise_auth_company_name")
	public String getEnterpriseAuthCompanyName() {
		return enterpriseAuthCompanyName;
	}

	public void setEnterpriseAuthCompanyName(String enterpriseAuthCompanyName) {
		this.enterpriseAuthCompanyName = enterpriseAuthCompanyName;
	}
	@Column(name = "enterprise_auth_credit_code")
	public String getEnterpriseAuthCreditCode() {
		return enterpriseAuthCreditCode;
	}

	public void setEnterpriseAuthCreditCode(String enterpriseAuthCreditCode) {
		this.enterpriseAuthCreditCode = enterpriseAuthCreditCode;
	}
	@Column(name = "enterprise_auth_status")
	public Integer getEnterpriseAuthStatus() {
		return enterpriseAuthStatus;
	}

	public void setEnterpriseAuthStatus(Integer enterpriseAuthStatus) {
		this.enterpriseAuthStatus = enterpriseAuthStatus;
	}
	@Column(name = "enterprise_auth_failure_reason")
	public String getEnterpriseAuthFailureReason() {
		return enterpriseAuthFailureReason;
	}

	public void setEnterpriseAuthFailureReason(String enterpriseAuthFailureReason) {
		this.enterpriseAuthFailureReason = enterpriseAuthFailureReason;
	}

	@Column(name = "enterprise_auth_user_id")
	public Long getEnterpriseExamineUserId() {
		return enterpriseExamineUserId;
	}

	public void setEnterpriseExamineUserId(Long enterpriseExamineUserId) {
		this.enterpriseExamineUserId = enterpriseExamineUserId;
	}
	@Column(name = "enterprise_auth_user_name")
	public String getEnterpriseExamineUserName() {
		return enterpriseExamineUserName;
	}

	public void setEnterpriseExamineUserName(String enterpriseExamineUserName) {
		this.enterpriseExamineUserName = enterpriseExamineUserName;
	}

	@Column(name = "enterprise_auth_ctime")
	public Date getEnterpriseAuthCtime() {
		return enterpriseAuthCtime;
	}

	public void setEnterpriseAuthCtime(Date enterpriseAuthCtime) {
		this.enterpriseAuthCtime = enterpriseAuthCtime;
	}

	@Column(name = "enterprise_auth_utime")
	public Date getEnterpriseAuthUtime() {
		return enterpriseAuthUtime;
	}

	public void setEnterpriseAuthUtime(Date enterpriseAuthUtime) {
		this.enterpriseAuthUtime = enterpriseAuthUtime;
	}

	@Column(name = "user_auth_path")
	public String getUserAuthPath() {
		return userAuthPath;
	}

	public void setUserAuthPath(String userAuthPath) {
		this.userAuthPath = userAuthPath;
	}

	@Column(name = "enterprise_auth_path")
	public String getEnterpriseAuthPath() {
		return enterpriseAuthPath;
	}

	public void setEnterpriseAuthPath(String enterpriseAuthPath) {
		this.enterpriseAuthPath = enterpriseAuthPath;
	}
}
