package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytPushUser entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_push_user")
public class TytPushUser implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9054430986246909795L;
	// Fields

	private Long id;
	private Long userId;
	private String cid;
	private Date ctime;
	private Date utime;
	private String clientVersion;
	private Integer clientSign;
	private String osVersion;
	private String deviceId;
	private String carDeviceId;
	private String goodsDeviceId;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "cid", length = 50)
	public String getCid() {
		return this.cid;
	}

	public void setCid(String cid) {
		this.cid = cid;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "client_version", length = 30)
	public String getClientVersion() {
		return this.clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	@Column(name = "client_sign")
	public Integer getClientSign() {
		return this.clientSign;
	}

	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name = "os_version", length = 30)
	public String getOsVersion() {
		return this.osVersion;
	}

	public void setOsVersion(String osVersion) {
		this.osVersion = osVersion;
	}
	@Column(name = "device_id")
	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

    @Column(name = "car_device_id")
    public String getCarDeviceId() {
        return carDeviceId;
    }

    public void setCarDeviceId(String carDeviceId) {
        this.carDeviceId = carDeviceId;
    }

    @Column(name = "goods_device_id")
    public String getGoodsDeviceId() {
        return goodsDeviceId;
    }

    public void setGoodsDeviceId(String goodsDeviceId) {
        this.goodsDeviceId = goodsDeviceId;
    }
}