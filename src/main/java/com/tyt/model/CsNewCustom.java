package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "cs_new_custom")
public class CsNewCustom {
    /**
     * 主键
     */
    private Long id;

    /**
     * 客户姓名
     */
    private String customName;

    /**
     * 客户电话
     */
    private String customPhone;

    /**
     * 客户来源
     */
    private String customSource;

    /**
     * 注册状态 1：已注册 2：未注册
     */
    private Short registerStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改人id
     */
    private Long modifyId;

    /**
     * 修改人姓名
     */
    private String modifyName;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;

    /**
     * 状态 1：有效 2：无效 3：删除
     */
    private Short status;

    private Integer belongTo;

    /**
     * 主键
     * @return id 主键
     */
    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 客户姓名
     * @return custom_name 客户姓名
     */
    @Column(name="custom_name")
    public String getCustomName() {
        return customName;
    }

    /**
     * 客户姓名
     * @param customName 客户姓名
     */
    public void setCustomName(String customName) {
        this.customName = customName == null ? null : customName.trim();
    }

    /**
     * 客户电话
     * @return custom_phone 客户电话
     */
    @Column(name="custom_phone")
    public String getCustomPhone() {
        return customPhone;
    }

    /**
     * 客户电话
     * @param customPhone 客户电话
     */
    public void setCustomPhone(String customPhone) {
        this.customPhone = customPhone == null ? null : customPhone.trim();
    }

    /**
     * 客户来源
     * @return custom_source 客户来源
     */
    @Column(name="custom_source")
    public String getCustomSource() {
        return customSource;
    }

    /**
     * 客户来源
     * @param customSource 客户来源
     */
    public void setCustomSource(String customSource) {
        this.customSource = customSource == null ? null : customSource.trim();
    }

    /**
     * 注册状态 1：已注册 2：未注册
     * @return register_status 注册状态 1：已注册 2：未注册
     */
    @Column(name="register_status")
    public Short getRegisterStatus() {
        return registerStatus;
    }

    /**
     * 注册状态 1：已注册 2：未注册
     * @param registerStatus 注册状态 1：已注册 2：未注册
     */
    public void setRegisterStatus(Short registerStatus) {
        this.registerStatus = registerStatus;
    }

    /**
     * 备注
     * @return remark 备注
     */
    @Column(name="remark")
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * 修改人id
     * @return modify_id 修改人id
     */
    @Column(name="modify_id")
    public Long getModifyId() {
        return modifyId;
    }

    /**
     * 修改人id
     * @param modifyId 修改人id
     */
    public void setModifyId(Long modifyId) {
        this.modifyId = modifyId;
    }

    /**
     * 修改人姓名
     * @return modify_name 修改人姓名
     */
    @Column(name="modify_name")
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 修改人姓名
     * @param modifyName 修改人姓名
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName == null ? null : modifyName.trim();
    }

    /**
     * 创建时间
     * @return ctime 创建时间
     */
    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    /**
     * 创建时间
     * @param ctime 创建时间
     */
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    /**
     * 修改时间
     * @return utime 修改时间
     */
    @Column(name="utime")
    public Date getUtime() {
        return utime;
    }

    /**
     * 修改时间
     * @param utime 修改时间
     */
    public void setUtime(Date utime) {
        this.utime = utime;
    }

    /**
     * 状态 1：有效 2：无效 3：删除
     * @return status 状态 1：有效 2：无效 3：删除
     */
    @Column(name="status")
    public Short getStatus() {
        return status;
    }

    /**
     * 状态 1：有效 2：无效 3：删除
     * @param status 状态 1：有效 2：无效 3：删除
     */
    public void setStatus(Short status) {
        this.status = status;
    }

    @Column(name="belong_to")
    public Integer getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(Integer belongTo) {
        this.belongTo = belongTo;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", customName=").append(customName);
        sb.append(", customPhone=").append(customPhone);
        sb.append(", customSource=").append(customSource);
        sb.append(", registerStatus=").append(registerStatus);
        sb.append(", remark=").append(remark);
        sb.append(", modifyId=").append(modifyId);
        sb.append(", modifyName=").append(modifyName);
        sb.append(", ctime=").append(ctime);
        sb.append(", utime=").append(utime);
        sb.append(", status=").append(status);
        sb.append("]");
        return sb.toString();
    }
}