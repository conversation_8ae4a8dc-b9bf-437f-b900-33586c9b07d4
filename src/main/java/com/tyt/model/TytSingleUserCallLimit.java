package com.tyt.model;

import com.alibaba.fastjson.JSON;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * 
 * <AUTHOR>
 * @date 2018年3月5日下午1:44:47
 * @description
 */
@Entity
@Table(name = "tyt_single_user_call_limit", catalog = "tyt")
public class TytSingleUserCallLimit implements java.io.Serializable {

	private static final long serialVersionUID = -7813390153165891582L;
	private Long id;
	private Long userId;
	private int callTime;
	private Date ctime;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id", nullable = false, length = 20)
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "call_time", nullable = false, length = 20)
	public int getCallTime() {
		return callTime;
	}

	public void setCallTime(int callTime) {
		this.callTime = callTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
