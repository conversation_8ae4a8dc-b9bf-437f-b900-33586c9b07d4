package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytUserSubId entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_user_sub")
public class TytUserSub implements java.io.Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -4344202922946043264L;
	private Long id;
	private Long userId;
	private Integer verifyFlag;
	private Integer sendTptType;
	private Integer sendTptNumber;

	private String cid;
	private Integer clientSign;
	private Integer notifyBadge;
	private Integer newMsgNbr;

	private Date utime;

	//2016/2/22
	private String maintainMan;//维护人
	private String bcarIdentityLables;//大板车身份标签
	private String scarIdentityLables;//设备身份标签

	private String auditBcarIdentityLables;//审核身份 大板车身份标签
	private String auditScarIdentityLables;//审核身份 设备身份标签
	//我的货源菜单1-仅有电话车 2-货主仅有APP发货货源 3-全有
	private Integer myGoodsMenu=2;
	private String deviceId;
	private String pocketPwd;
	private Integer pocketPwdStatus;
	private Integer dealNum;
	private Integer userGroup;
	private String bindCliendid;
	private Integer bindStatus;
	private Date level2BigingTime;
	private String carDeviceId;
	private String goodsDeviceId;
	private Integer carNotifyBadge;
	private Integer goodsNotifyBadge;
	private Integer carNewMsgNbr;
	private Integer goodsNewMsgNbr;
	/**
	 * 平台货源发布次数
	 */
	private Integer publishNum;

	/**
	 * 车方用户违约次数
	 */
	private Integer carBreakNum;

	/**
	 * 货方用户违约次数
	 */
	private Integer goodsBreakNum;

	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="level2_biging_time")
	public Date getLevel2BigingTime() {
		return level2BigingTime;
	}
	public void setLevel2BigingTime(Date level2BigingTime) {
		this.level2BigingTime = level2BigingTime;
	}
	@Column(name="bind_status")
	public Integer getBindStatus() {
		return bindStatus;
	}
	public void setBindStatus(Integer bindStatus) {
		this.bindStatus = bindStatus;
	}
	@Column(name="bind_cliendid")
	public String getBindCliendid() {
		return bindCliendid;
	}
	public void setBindCliendid(String bindCliendid) {
		this.bindCliendid = bindCliendid;
	}
	@Column(name="user_group")
	public Integer getUserGroup() {
		return userGroup;
	}
	public void setUserGroup(Integer userGroup) {
		this.userGroup = userGroup;
	}
	@Column(name="pocket_pwd_status")
	public Integer getPocketPwdStatus() {
		return pocketPwdStatus;
	}
	public void setPocketPwdStatus(Integer pocketPwdStatus) {
		this.pocketPwdStatus = pocketPwdStatus;
	}
	@Column(name="deal_num")
	public Integer getDealNum() {
		return dealNum;
	}
	public void setDealNum(Integer dealNum) {
		this.dealNum = dealNum;
	}
	@Column(name="pocket_pwd")
	public String getPocketPwd() {
		return pocketPwd;
	}
	public void setPocketPwd(String pocketPwd) {
		this.pocketPwd = pocketPwd;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="verify_flag")
	public Integer getVerifyFlag() {
		return verifyFlag;
	}
	public void setVerifyFlag(Integer verifyFlag) {
		this.verifyFlag = verifyFlag;
	}
	@Column(name="send_tpt_type")
	public Integer getSendTptType() {
		return sendTptType;
	}
	public void setSendTptType(Integer sendTptType) {
		this.sendTptType = sendTptType;
	}
	@Column(name="send_tpt_number")
	public Integer getSendTptNumber() {
		return sendTptNumber;
	}
	public void setSendTptNumber(Integer sendTptNumber) {
		this.sendTptNumber = sendTptNumber;
	}
	@Column(name="maintain_man")
	public String getMaintainMan() {
		return maintainMan;
	}
	public void setMaintainMan(String maintainMan) {
		this.maintainMan = maintainMan;
	}
	@Column(name="cid")
	public String getCid() {
		return cid;
	}
	public void setCid(String cid) {
		this.cid = cid;
	}
	@Column(name="client_sign")
	public Integer getClientSign() {
		return clientSign;
	}
	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}
	@Column(name="notify_badge")
	public Integer getNotifyBadge() {
		return notifyBadge;
	}
	public void setNotifyBadge(Integer notifyBadge) {
		this.notifyBadge = notifyBadge;
	}
	@Column(name="new_msg_nbr")
	public Integer getNewMsgNbr() {
		return newMsgNbr;
	}
	public void setNewMsgNbr(Integer newMsgNbr) {
		this.newMsgNbr = newMsgNbr;
	}
	@Column(name="utime")
	public Date getUtime() {
		return utime;
	}
	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name="bcar_identity_lables")
	public String getBcarIdentityLables() {
		return bcarIdentityLables;
	}
	public void setBcarIdentityLables(String bcarIdentityLables) {
		this.bcarIdentityLables = bcarIdentityLables;
	}
	@Column(name="scar_identity_lables")
	public String getScarIdentityLables() {
		return scarIdentityLables;
	}
	public void setScarIdentityLables(String scarIdentityLables) {
		this.scarIdentityLables = scarIdentityLables;
	}
	@Column(name="audit_bcar_identity_lables")
	public String getAuditBcarIdentityLables() {
		return auditBcarIdentityLables;
	}
	public void setAuditBcarIdentityLables(String auditBcarIdentityLables) {
		this.auditBcarIdentityLables = auditBcarIdentityLables;
	}
	@Column(name="audit_scar_identity_lables")
	public String getAuditScarIdentityLables() {
		return auditScarIdentityLables;
	}
	public void setAuditScarIdentityLables(String auditScarIdentityLables) {
		this.auditScarIdentityLables = auditScarIdentityLables;
	}
	@Column(name="my_goods_menu")
	public Integer getMyGoodsMenu() {
		return myGoodsMenu;
	}
	public void setMyGoodsMenu(Integer myGoodsMenu) {
		this.myGoodsMenu = myGoodsMenu;
	}
	@Column(name = "device_id")
	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

    @Column(name = "car_device_id")
    public String getCarDeviceId() {
        return carDeviceId;
    }

    public void setCarDeviceId(String carDeviceId) {
        this.carDeviceId = carDeviceId;
    }

    @Column(name = "goods_device_id")
    public String getGoodsDeviceId() {
        return goodsDeviceId;
    }

    public void setGoodsDeviceId(String goodsDeviceId) {
        this.goodsDeviceId = goodsDeviceId;
    }

    @Column(name = "car_notify_badge")
    public Integer getCarNotifyBadge() {
        return carNotifyBadge;
    }

    public void setCarNotifyBadge(Integer carNotifyBadge) {
        this.carNotifyBadge = carNotifyBadge;
    }

    @Column(name = "goods_notify_badge")
    public Integer getGoodsNotifyBadge() {
        return goodsNotifyBadge;
    }

    public void setGoodsNotifyBadge(Integer goodsNotifyBadge) {
        this.goodsNotifyBadge = goodsNotifyBadge;
    }

    @Column(name = "car_new_msg_nbr")
    public Integer getCarNewMsgNbr() {
        return carNewMsgNbr;
    }

    public void setCarNewMsgNbr(Integer carNewMsgNbr) {
        this.carNewMsgNbr = carNewMsgNbr;
    }

    @Column(name = "goods_new_msg_nbr")
    public Integer getGoodsNewMsgNbr() {
        return goodsNewMsgNbr;
    }

    public void setGoodsNewMsgNbr(Integer goodsNewMsgNbr) {
        this.goodsNewMsgNbr = goodsNewMsgNbr;
    }
	@Column(name = "publish_num")
	public Integer getPublishNum() {
		return publishNum;
	}

	public void setPublishNum(Integer publishNum) {
		this.publishNum = publishNum;
	}

	@Column(name = "car_break_num")
	public Integer getCarBreakNum() {
		return carBreakNum;
	}

	public void setCarBreakNum(Integer carBreakNum) {
		this.carBreakNum = carBreakNum;
	}

	@Column(name = "goods_break_num")
	public Integer getGoodsBreakNum() {
		return goodsBreakNum;
	}

	public void setGoodsBreakNum(Integer goodsBreakNum) {
		this.goodsBreakNum = goodsBreakNum;
	}

	@Override
	public String toString() {
		return "TytUserSub{" +
				"id=" + id +
				", userId=" + userId +
				", verifyFlag=" + verifyFlag +
				", sendTptType=" + sendTptType +
				", sendTptNumber=" + sendTptNumber +
				", cid='" + cid + '\'' +
				", clientSign=" + clientSign +
				", notifyBadge=" + notifyBadge +
				", newMsgNbr=" + newMsgNbr +
				", utime=" + utime +
				", maintainMan='" + maintainMan + '\'' +
				", bcarIdentityLables='" + bcarIdentityLables + '\'' +
				", scarIdentityLables='" + scarIdentityLables + '\'' +
				", auditBcarIdentityLables='" + auditBcarIdentityLables + '\'' +
				", auditScarIdentityLables='" + auditScarIdentityLables + '\'' +
				", myGoodsMenu=" + myGoodsMenu +
				", deviceId='" + deviceId + '\'' +
				", pocketPwd='" + pocketPwd + '\'' +
				", pocketPwdStatus=" + pocketPwdStatus +
				", dealNum=" + dealNum +
				", userGroup=" + userGroup +
				", bindCliendid='" + bindCliendid + '\'' +
				", bindStatus=" + bindStatus +
				", level2BigingTime=" + level2BigingTime +
				'}';
	}
}
