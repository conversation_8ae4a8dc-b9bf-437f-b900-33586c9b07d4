package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 反馈信息实体
 * <AUTHOR>
 *
 */
@Entity
@Table(name="card_prefix_bank_info")
public class CardPrefixBankInfo implements Serializable {

	private static final long serialVersionUID = 1644739266337840172L;

	private Long id;
	private String cardId;//卡号前缀
	private String cardType;//卡类型 DC:借记卡 CC：贷记卡 SCC：准贷记卡 PC: 预付费卡
	private String bankName;//开户行名称
	private String bankWord; //英文缩写
	private Date mtime;
	private Date ctime;

	@Id
	@GeneratedValue
	@Column(name = "id")
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "card_id")
	public String getCardId() {
		return cardId;
	}

	public void setCardId(String cardId) {
		this.cardId = cardId;
	}
	@Column(name = "card_type")
	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}
	@Column(name = "bank_name")
	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	@Column(name = "bank_word")
	public String getBankWord() {
		return bankWord;
	}

	public void setBankWord(String bankWord) {
		this.bankWord = bankWord;
	}
	@Column(name = "mtime")
	public Date getMtime() {
		return mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Column(name = "ctime")
	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
}
