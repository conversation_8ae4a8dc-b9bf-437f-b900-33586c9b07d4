package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_public_resource")
public class PublicResource implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;
	Long id;// '主键',
	String name;// '键',
	String value;// '值',
//	Date createTime;// '创建时间',
//	Date updateTime;// '更新时间',
//	String type;// '类型'
//	String remark;// 描述

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "name")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "value" ,updatable=false)
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

//	@Column(name = "create_time")
//	public Date getCreateTime() {
//		return createTime;
//	}
//
//	public void setCreateTime(Date createTime) {
//		this.createTime = createTime;
//	}
//
//	@Column(name = "update_time", updatable=false)
//	public Date getUpdateTime() {
//		return updateTime;
//	}
//
//	public void setUpdateTime(Date updateTime) {
//		this.updateTime = updateTime;
//	}
//
//	@Column(name = "type")
//	public String getType() {
//		return type;
//	}
//
//	public void setType(String type) {
//		this.type = type;
//	}
//
//	@Column(name = "remark")
//	public String getRemark() {
//		return remark;
//	}
//
//	public void setRemark(String remark) {
//		this.remark = remark;
//	}

	@Override
	public String toString() {
		return "PublicResource [id=" + id + ", name=" + name + ", value=" + value + "]";
	}
}
