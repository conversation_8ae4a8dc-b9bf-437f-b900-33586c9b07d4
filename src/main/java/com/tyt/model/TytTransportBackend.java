package com.tyt.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/12/23 10:29
 */
@Entity
@Table(name = "tyt_transport_backend")
public class TytTransportBackend implements java.io.Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * main表货源id
     */
    private Long srcMsgId;

    /**
     * 出发地(省市区以减号-分割开)
     */
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    private String destPoint;

    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 联系人
     */
    private String tel;

    /**
     * 状态 1-待接单 2-已取消 3-已接单
     */
    private Integer status;

    /**
     * 10-待接单 11-已接单未发布 20-已撤销(定时) 21-已线下成交 22-已撤销(手动)
     */
    private Integer orderStatus;

    /**
     * 是否精准推送 0不是 1是
     */
    private Integer isPrecisePush;

    /**
     * 人工/自动
     */
    private Integer source;

    /**
     * 采集时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 运费
     */
    private String price;

    /**
     * 发布人usreid 关联tyt_user表id
     */
    private Long userId;

    /**
     * 出发地坐标x
     */
    private String startCoordX;

    /**
     * 出发地坐标y
     */
    private String startCoordY;

    /**
     * 目的地坐标x
     */
    private String destCoordX;

    /**
     * 目的地坐标y
     */
    private String destCoordY;

    /**
     * 出发地经度
     */
    private String startLongitude;

    /**
     * 出发地纬度
     */
    private String startLatitude;

    /**
     * 目的地经度
     */
    private String destLongitude;

    /**
     * 目的地纬度
     */
    private String destLatitude;

    /**
     * 出发地详细地址
     */
    private String startDetailAdd;

    /**
     * 目的地详细地址
     */
    private String destDetailAdd;

    /**
     * 重量单位吨
     */
    private String weight;

    /**
     * 货物长单位米
     */
    private String length;

    /**
     * 货物宽单位米
     */
    private String wide;

    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系人电话3
     */
    private String tel3;

    /**
     * 联系人电话4
     */
    private String tel4;

    /**
     * 出发地城市
     */
    private String startCity;

    /**
     * 出发地省
     */
    private String startProvinc;

    /**
     * 出发地区
     */
    private String startArea;

    /**
     * 目的地省
     */
    private String destProvinc;

    /**
     * 目的地市
     */
    private String destCity;

    /**
     * 目的地区
     */
    private String destArea;

    /**
     * 货物型号
     */
    private String type;

    /**
     * 货物品牌
     */
    private String brand;

    /**
     * 货物类型名称，如“装载机”，“挖掘机”
     */
    private String goodTypeName;

    /**
     * 装车时间
     */
    private Date loadingTime;

    /**
     * 卸车时间
     */
    private Date unloadTime;

    /**
     * 车辆最低长度，单位米
     */
    private BigDecimal carMinLength;

    /**
     * 车辆最大长度，单位米
     */
    private BigDecimal carMaxLength;

    /**
     * 车辆类型
     */
    private String carType;

    /**
     * 挂车样式
     */
    private String carStyle;

    /**
     * 工作面高最小值，单位米
     */
    private BigDecimal workPlaneMinHigh;

    /**
     * 工作面高最大值，单位米
     */
    private BigDecimal workPlaneMaxHigh;

    /**
     * 工作面长最小值，单位米
     */
    private BigDecimal workPlaneMinLength;

    /**
     * 工作面长最大值，单位米
     */
    private BigDecimal workPlaneMaxLength;

    /**
     * 是否需要爬梯
     */
    private String climb;

    /**
     * 当前推送版本号 批次_分页次数
     */
    private String batch;

    /**
     * 当前货源是否有效0有效1无效
     */
    private String isValid;

    /**
     * 货源订单编号（第三方企业生成）
     */
    private String orderNo;

    /**
     * 是否已确认取消 0未确认 1已确认
     */
    private Integer cancelConfirm;

    private String telephoneOne;
    private String telephoneTwo;
    private String telephoneThree;

    /**
     * 接单人id
     */
    private Long receiverUserId;
    /**
     * 第三方企业发货发货 地址匹配状态：0：出发地、目的地全部匹配 1：出发地、目的地都不匹配 2：出发地不匹配 3：目的地不匹配
     */
    private Integer addrMatchingStatus;

    private Long appletsUserId;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "start_point")
    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    @Column(name = "dest_point")
    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    @Column(name = "task_content")
    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    @Column(name = "tel")
    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "order_status")
    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    @Column(name = "is_precise_push")
    public Integer getIsPrecisePush() {
        return isPrecisePush;
    }

    public void setIsPrecisePush(Integer isPrecisePush) {
        this.isPrecisePush = isPrecisePush;
    }

    @Column(name = "source")
    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Column(name = "price")
    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "start_detail_add")
    public String getStartDetailAdd() {
        return startDetailAdd;
    }

    public void setStartDetailAdd(String startDetailAdd) {
        this.startDetailAdd = startDetailAdd;
    }

    @Column(name = "dest_detail_add")
    public String getDestDetailAdd() {
        return destDetailAdd;
    }

    public void setDestDetailAdd(String destDetailAdd) {
        this.destDetailAdd = destDetailAdd;
    }

    @Column(name = "weight")
    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    @Column(name = "length")
    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    @Column(name = "wide")
    public String getWide() {
        return wide;
    }

    public void setWide(String wide) {
        this.wide = wide;
    }

    @Column(name = "high")
    public String getHigh() {
        return high;
    }

    public void setHigh(String high) {
        this.high = high;
    }

    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "tel3")
    public String getTel3() {
        return tel3;
    }

    public void setTel3(String tel3) {
        this.tel3 = tel3;
    }

    @Column(name = "tel4")
    public String getTel4() {
        return tel4;
    }

    public void setTel4(String tel4) {
        this.tel4 = tel4;
    }

    @Column(name = "start_city")
    public String getStartCity() {
        return startCity;
    }

    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }

    @Column(name = "start_provinc")
    public String getStartProvinc() {
        return startProvinc;
    }

    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }

    @Column(name = "start_area")
    public String getStartArea() {
        return startArea;
    }

    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }

    @Column(name = "dest_provinc")
    public String getDestProvinc() {
        return destProvinc;
    }

    public void setDestProvinc(String destProvinc) {
        this.destProvinc = destProvinc;
    }

    @Column(name = "dest_city")
    public String getDestCity() {
        return destCity;
    }

    public void setDestCity(String destCity) {
        this.destCity = destCity;
    }

    @Column(name = "dest_area")
    public String getDestArea() {
        return destArea;
    }

    public void setDestArea(String destArea) {
        this.destArea = destArea;
    }

    @Column(name = "type")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "brand")
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    @Column(name = "good_type_name")
    public String getGoodTypeName() {
        return goodTypeName;
    }

    public void setGoodTypeName(String goodTypeName) {
        this.goodTypeName = goodTypeName;
    }

    @Column(name = "loading_time")
    public Date getLoadingTime() {
        return loadingTime;
    }

    public void setLoadingTime(Date loadingTime) {
        this.loadingTime = loadingTime;
    }

    @Column(name = "unload_time")
    public Date getUnloadTime() {
        return unloadTime;
    }

    public void setUnloadTime(Date unloadTime) {
        this.unloadTime = unloadTime;
    }

    @Column(name = "car_min_length")
    public BigDecimal getCarMinLength() {
        return carMinLength;
    }

    public void setCarMinLength(BigDecimal carMinLength) {
        this.carMinLength = carMinLength;
    }

    @Column(name = "car_max_length")
    public BigDecimal getCarMaxLength() {
        return carMaxLength;
    }

    public void setCarMaxLength(BigDecimal carMaxLength) {
        this.carMaxLength = carMaxLength;
    }

    @Column(name = "car_type")
    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    @Column(name = "car_style")
    public String getCarStyle() {
        return carStyle;
    }

    public void setCarStyle(String carStyle) {
        this.carStyle = carStyle;
    }

    @Column(name = "work_plane_min_high")
    public BigDecimal getWorkPlaneMinHigh() {
        return workPlaneMinHigh;
    }

    public void setWorkPlaneMinHigh(BigDecimal workPlaneMinHigh) {
        this.workPlaneMinHigh = workPlaneMinHigh;
    }

    @Column(name = "work_plane_max_high")
    public BigDecimal getWorkPlaneMaxHigh() {
        return workPlaneMaxHigh;
    }

    public void setWorkPlaneMaxHigh(BigDecimal workPlaneMaxHigh) {
        this.workPlaneMaxHigh = workPlaneMaxHigh;
    }

    @Column(name = "work_plane_min_length")
    public BigDecimal getWorkPlaneMinLength() {
        return workPlaneMinLength;
    }

    public void setWorkPlaneMinLength(BigDecimal workPlaneMinLength) {
        this.workPlaneMinLength = workPlaneMinLength;
    }

    @Column(name = "work_plane_max_length")
    public BigDecimal getWorkPlaneMaxLength() {
        return workPlaneMaxLength;
    }

    public void setWorkPlaneMaxLength(BigDecimal workPlaneMaxLength) {
        this.workPlaneMaxLength = workPlaneMaxLength;
    }

    @Column(name = "climb")
    public String getClimb() {
        return climb;
    }

    public void setClimb(String climb) {
        this.climb = climb;
    }

    @Column(name = "batch")
    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    @Column(name = "src_msg_id")
    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    @Column(name = "start_coord_x")
    public String getStartCoordX() {
        return startCoordX;
    }

    public void setStartCoordX(String startCoordX) {
        this.startCoordX = startCoordX;
    }

    @Column(name = "start_coord_y")
    public String getStartCoordY() {
        return startCoordY;
    }

    public void setStartCoordY(String startCoordY) {
        this.startCoordY = startCoordY;
    }

    @Column(name = "dest_coord_x")
    public String getDestCoordX() {
        return destCoordX;
    }

    public void setDestCoordX(String destCoordX) {
        this.destCoordX = destCoordX;
    }

    @Column(name = "dest_coord_y")
    public String getDestCoordY() {
        return destCoordY;
    }

    public void setDestCoordY(String destCoordY) {
        this.destCoordY = destCoordY;
    }

    @Column(name = "start_longitude")
    public String getStartLongitude() {
        return startLongitude;
    }

    public void setStartLongitude(String startLongitude) {
        this.startLongitude = startLongitude;
    }

    @Column(name = "start_latitude")
    public String getStartLatitude() {
        return startLatitude;
    }

    public void setStartLatitude(String startLatitude) {
        this.startLatitude = startLatitude;
    }

    @Column(name = "dest_longitude")
    public String getDestLongitude() {
        return destLongitude;
    }

    public void setDestLongitude(String destLongitude) {
        this.destLongitude = destLongitude;
    }

    @Column(name = "dest_latitude")
    public String getDestLatitude() {
        return destLatitude;
    }

    public void setDestLatitude(String destLatitude) {
        this.destLatitude = destLatitude;
    }

    @Column(name = "is_valid")
    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    @Column(name = "order_no")
    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
    @Transient
    public String getTelephoneOne() {
        return telephoneOne;
    }

    public void setTelephoneOne(String telephoneOne) {
        this.telephoneOne = telephoneOne;
    }
    @Transient
    public String getTelephoneTwo() {
        return telephoneTwo;
    }

    public void setTelephoneTwo(String telephoneTwo) {
        this.telephoneTwo = telephoneTwo;
    }
    @Transient
    public String getTelephoneThree() {
        return telephoneThree;
    }

    public void setTelephoneThree(String telephoneThree) {
        this.telephoneThree = telephoneThree;
    }

    @Column(name = "cancel_confirm")
    public Integer getCancelConfirm() {
        return cancelConfirm;
    }

    public void setCancelConfirm(Integer cancelConfirm) {
        this.cancelConfirm = cancelConfirm;
    }

    @Column(name = "receiver_user_id")
    public Long getReceiverUserId() {
        return receiverUserId;
    }

    public void setReceiverUserId(Long receiverUserId) {
        this.receiverUserId = receiverUserId;
    }

    @Column(name = "addr_matching_status")
    public Integer getAddrMatchingStatus() {
        return addrMatchingStatus;
    }

    public void setAddrMatchingStatus(Integer addrMatchingStatus) {
        this.addrMatchingStatus = addrMatchingStatus;
    }

    @Column(name = "applets_user_id")
    public Long getAppletsUserId() {
        return appletsUserId;
    }

    public void setAppletsUserId(Long appletsUserId) {
        this.appletsUserId = appletsUserId;
    }
}
