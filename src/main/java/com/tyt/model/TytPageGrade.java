package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 分页级别表
 */
@Entity
@Table(name = "tyt_page_grade", catalog = "tyt")
public class TytPageGrade implements java.io.Serializable {

	private static final long serialVersionUID = 5192159608657993688L;

	private Long id;
	private String gradeCode;
	private Integer valueBeginRange;
	private Integer valueEndRange;
	private String chineseMean;
	private Date updateTime;
	private String type;
	private String valueUnit;
	private Integer orderNumber;
	private String status;


	public TytPageGrade() {
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true,nullable=false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "grade_code")
	public String getGradeCode() {
		return this.gradeCode;
	}

	public void setGradeCode(String gradeCode) {
		this.gradeCode = gradeCode;
	}

	@Column(name = "value_begin_range")
	public Integer getValueBeginRange() {
		return this.valueBeginRange;
	}

	public void setValueBeginRange(Integer valueBeginRange) {
		this.valueBeginRange = valueBeginRange;
	}

	@Column(name = "value_end_range")
	public Integer getValueEndRange() {
		return this.valueEndRange;
	}

	public void setValueEndRange(Integer valueEndRange) {
		this.valueEndRange = valueEndRange;
	}

	@Column(name = "chinese_mean")
	public String getChineseMean() {
		return this.chineseMean;
	}

	public void setChineseMean(String chineseMean) {
		this.chineseMean = chineseMean;
	}

	@Column(name = "type")
	public String getType() {
		return this.type;
	}
	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "value_unit")
	public String getValueUnit() {
		return this.valueUnit;
	}

	public void setValueUnit(String valueUnit) {
		this.valueUnit = valueUnit;
	}
	@Column(name = "order_number")
	public Integer getOrderNumber() {
		return orderNumber;
	}

	public void setOrderNumber(Integer orderNumber) {
		this.orderNumber = orderNumber;
	}
	@Column(name = "status")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}