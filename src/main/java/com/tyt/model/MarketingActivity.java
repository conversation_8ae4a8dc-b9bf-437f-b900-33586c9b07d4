package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name="marketing_activity")
public class MarketingActivity implements Serializable {

    private static final long serialVersionUID = 7573953477717062269L;
    private Long id;
    private String activityName;
    private Integer isNeedPopup;
    private Long popupIdOne;
    private Long popupIdTwo;
    private Integer activityScope;
    private Integer activityType;
    private String activityTypeName;
    private Date startTime;
    private Date endTime;
    private String pushTitle;
    private String pushSummary;
    private String pushContent;
    private Integer pushType;
    private Integer activityPart;
    private Integer isNeedShow;
    private String activityContent;
    private String activityUrl;
    private Integer status;
    private String remark;
    private String operater;
    private Date ctime;
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name="id",nullable=false,unique=true)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name="activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
    @Column(name="is_need_popup")
    public Integer getIsNeedPopup() {
        return isNeedPopup;
    }

    public void setIsNeedPopup(Integer isNeedPopup) {
        this.isNeedPopup = isNeedPopup;
    }
    @Column(name="popup_id_one")
    public Long getPopupIdOne() {
        return popupIdOne;
    }

    public void setPopupIdOne(Long popupIdOne) {
        this.popupIdOne = popupIdOne;
    }
    @Column(name="popup_id_two")
    public Long getPopupIdTwo() {
        return popupIdTwo;
    }

    public void setPopupIdTwo(Long popupIdTwo) {
        this.popupIdTwo = popupIdTwo;
    }
    @Column(name="activity_scope")
    public Integer getActivityScope() {
        return activityScope;
    }

    public void setActivityScope(Integer activityScope) {
        this.activityScope = activityScope;
    }
    @Column(name="activity_type")
    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }
    @Column(name="activity_type_name")
    public String getActivityTypeName() {
        return activityTypeName;
    }

    public void setActivityTypeName(String activityTypeName) {
        this.activityTypeName = activityTypeName;
    }
    @Column(name="start_time")
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    @Column(name="end_time")
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    @Column(name="push_title")
    public String getPushTitle() {
        return pushTitle;
    }

    public void setPushTitle(String pushTitle) {
        this.pushTitle = pushTitle;
    }
    @Column(name="push_summary")
    public String getPushSummary() {
        return pushSummary;
    }

    public void setPushSummary(String pushSummary) {
        this.pushSummary = pushSummary;
    }
    @Column(name="push_content")
    public String getPushContent() {
        return pushContent;
    }

    public void setPushContent(String pushContent) {
        this.pushContent = pushContent;
    }
    @Column(name="push_type")
    public Integer getPushType() {
        return pushType;
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
    }
    @Column(name="activity_part")
    public Integer getActivityPart() {
        return activityPart;
    }

    public void setActivityPart(Integer activityPart) {
        this.activityPart = activityPart;
    }
    @Column(name="is_need_show")
    public Integer getIsNeedShow() {
        return isNeedShow;
    }

    public void setIsNeedShow(Integer isNeedShow) {
        this.isNeedShow = isNeedShow;
    }
    @Column(name="activity_content")
    public String getActivityContent() {
        return activityContent;
    }

    public void setActivityContent(String activityContent) {
        this.activityContent = activityContent;
    }

    @Column(name="activity_url")
    public String getActivityUrl() {
        return activityUrl;
    }

    public void setActivityUrl(String activityUrl) {
        this.activityUrl = activityUrl;
    }
    @Column(name="status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    @Column(name="remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    @Column(name="operater")
    public String getOperater() {
        return operater;
    }

    public void setOperater(String operater) {
        this.operater = operater;
    }
    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
