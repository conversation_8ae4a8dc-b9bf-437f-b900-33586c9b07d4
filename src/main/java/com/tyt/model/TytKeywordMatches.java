package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-4-17上午9:57:24
 * @description
 */
@Entity
@Table(name = "tyt_keyword_matches", catalog = "tyt")
public class TytKeywordMatches implements java.io.Serializable {

	private static final long serialVersionUID = 3958068571201428276L;
	private int id;
	private String keyword;
	private int machineTypeId;
	private String keywordType;
	private byte priority;

	public TytKeywordMatches() {
	}

	public TytKeywordMatches(int id, String keyword, int machineTypeId, String keywordType, byte priority) {
		this.id = id;
		this.keyword = keyword;
		this.machineTypeId = machineTypeId;
		this.keywordType = keywordType;
		this.priority = priority;
	}

	@Id
	@Column(name = "id", unique = true, nullable = false)
	public int getId() {
		return this.id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "keyword", nullable = false, length = 16)
	public String getKeyword() {
		return this.keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	@Column(name = "machine_type_id", nullable = false)
	public int getMachineTypeId() {
		return this.machineTypeId;
	}

	public void setMachineTypeId(int machineTypeId) {
		this.machineTypeId = machineTypeId;
	}

	@Column(name = "keyword_type", nullable = false, length = 8)
	public String getKeywordType() {
		return this.keywordType;
	}

	public void setKeywordType(String keywordType) {
		this.keywordType = keywordType;
	}

	@Column(name = "priority", nullable = false)
	public byte getPriority() {
		return this.priority;
	}

	public void setPriority(byte priority) {
		this.priority = priority;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
