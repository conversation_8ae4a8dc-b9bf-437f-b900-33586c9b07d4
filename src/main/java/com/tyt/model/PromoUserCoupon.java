package com.tyt.model;

import com.tyt.promo.model.PromoCoupon;
import com.tyt.util.Constant;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Entity
@Table(name = "promo_user_coupon")
public class PromoUserCoupon implements Serializable {
    private static final long serialVersionUID = 6990024478820539678L;

    private Integer id;//int(11) NOT NULL卡券标示id，唯一自增生成
    private Long userId;//bigint(20) NOT NULL
    private Integer couponTypeId;//int(11) NOT NULL卡券类型id，关联tyt_coupon表
    private String couponName;//varchar(100) NULL优惠券名称
    private Integer couponStatus;//tinyint(4) NOT NULL1 未使用 2已使用 3无效 4已过期
    private BigDecimal couponAmount;//decimal(10,2) NOT NULL
    private Date consumeTime;//datetime NULL消费时间
    private Integer activityId;//int(11) NOT NULL活动id
    private Integer activityType;//tinyint(4) NULL活动类型
    private Long logId;//bigint(20) NULL记录ID
    private Date expireTime;//datetime NULL截止时间
    private Date mtime;//timestamp NOT NULL
    private Date ctime;//ctimedatetime NOT NULL
    private Integer grantType;//tinyint(4) NULL发放类型 1：单券 2：礼包
    private Integer giftId;//int(11) NULL礼包id
    private Integer groupId;//int(11) NULL活动分组id

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @Column(name="user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @Column(name="coupon_type_id")
    public Integer getCouponTypeId() {
        return couponTypeId;
    }

    public void setCouponTypeId(Integer couponTypeId) {
        this.couponTypeId = couponTypeId;
    }
    @Column(name="coupon_name")
    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }
    @Column(name="coupon_status")
    public Integer getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(Integer couponStatus) {
        this.couponStatus = couponStatus;
    }
    @Column(name="coupon_amount")
    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }
    @Column(name="consume_time")
    public Date getConsumeTime() {
        return consumeTime;
    }

    public void setConsumeTime(Date consumeTime) {
        this.consumeTime = consumeTime;
    }
    @Column(name="activity_id")
    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }
    @Column(name="activity_type")
    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }
    @Column(name="log_id")
    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }
    @Column(name="expire_time")
    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name="grant_type")
    public Integer getGrantType() {
        return grantType;
    }

    public void setGrantType(Integer grantType) {
        this.grantType = grantType;
    }
    @Column(name="gift_id")
    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }
    @Column(name="group_id")
    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    @Override
    public String toString() {
        return "PromoUserCoupon{" +
                "id=" + id +
                ", userId=" + userId +
                ", couponTypeId=" + couponTypeId +
                ", couponName='" + couponName + '\'' +
                ", couponStatus=" + couponStatus +
                ", couponAmount=" + couponAmount +
                ", consumeTime=" + consumeTime +
                ", activityId=" + activityId +
                ", activityType=" + activityType +
                ", logId=" + logId +
                ", expireTime=" + expireTime +
                ", mtime=" + mtime +
                ", ctime=" + ctime +
                ", grantType=" + grantType +
                ", giftId=" + giftId +
                ", groupId=" + groupId +
                '}';
    }
    public PromoUserCoupon(User user, ProbCoupon probCoupon, PromoCoupon promoCoupon, PromoActivity promoActivity){
        this.userId = user.getId();
        this.couponTypeId = promoCoupon.getCouponTypeId();
        this.couponStatus = CouponStatusEnum.UNUSED.getStatus();
        this.couponAmount = promoCoupon.getCouponAmount();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, Constant.EXPIRATION);
        this.expireTime = calendar.getTime();
        this.couponName = promoCoupon.getCouponName();
        this.activityId = promoActivity.getId();
        this.activityType = promoActivity.getActivityType();
        this.logId = 0L;
        this.ctime = new Date();
        this.grantType = promoActivity.getGrantType();
        this.giftId = 0;
        this.groupId = promoActivity.getGroupId();
    }

    public PromoUserCoupon() {
    }

    public PromoUserCoupon(Integer id, Long userId, Integer couponTypeId, String couponName, Integer couponStatus, BigDecimal couponAmount, Date consumeTime, Integer activityId, Integer activityType, Long logId, Date expireTime, Date mtime, Date ctime, Integer grantType, Integer giftId, Integer groupId) {
        this.id = id;
        this.userId = userId;
        this.couponTypeId = couponTypeId;
        this.couponName = couponName;
        this.couponStatus = couponStatus;
        this.couponAmount = couponAmount;
        this.consumeTime = consumeTime;
        this.activityId = activityId;
        this.activityType = activityType;
        this.logId = logId;
        this.expireTime = expireTime;
        this.mtime = mtime;
        this.ctime = ctime;
        this.grantType = grantType;
        this.giftId = giftId;
        this.groupId = groupId;
    }

    /**
     * 优惠券使用状态 1 未使用 2已使用 3无效 4已过期
     */
    public static enum CouponStatusEnum{
        UNUSED(1, "未使用"),
        USED(2, "已使用"),
        INVALID(3, "无效"),
        EXPIRED(4, "已过期");
        private Integer status;
        private String msg;

        public Integer getStatus() {
            return status;
        }

        public String getMsg() {
            return msg;
        }

        CouponStatusEnum(Integer status, String msg) {
            this.status = status;
            this.msg = msg;
        }
    }
}
