package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="tyt_transport_vary")
public class TransportVary implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1051853119481944984L;
	Long id;
	Long tsId;//运输信息表ID
	Integer status;//状态
	Date updateTime;//变动时间
	
	
	public TransportVary() {
		super();
	}
	public TransportVary(Long tsId, Integer status) {
		super();
		this.tsId = tsId;
		this.status = status;
		this.updateTime=new Date();
	}
	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="ts_id")
	public Long getTsId() {
		return tsId;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	

}
