package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-8-7下午2:53:42
 * @description
 */
@Entity
@Table(name = "tyt_probability_keyword", catalog = "tyt")
public class TytProbabilityKeyword implements java.io.Serializable {
	private static final long serialVersionUID = -7782944577338854060L;

	private String keyword;

	public TytProbabilityKeyword() {
	}

	public TytProbabilityKeyword(String keyword) {
		this.keyword = keyword;
	}

	@Id
	@Column(name = "keyword")
	public String getKeyword() {
		return this.keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
