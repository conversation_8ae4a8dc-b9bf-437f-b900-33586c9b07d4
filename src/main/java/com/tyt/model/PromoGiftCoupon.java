package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
@Entity
@Table(name="promo_gift_coupon")
public class PromoGiftCoupon implements Serializable {
    private static final long serialVersionUID = -9216137224300208825L;
    private Integer id;//int(11) NOT NULL
    private Integer giftId;//int(11) NULL礼包id
    private Integer couponId;//int(11) NULL优惠券id
    private Integer couponNum;//int(11) NULL优惠券数量
    private Date ctime;//timestamp NOT NULL创建时间
    private Date mtime;//timestamp NOT NULL修改时间


    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @Column(name="gift_id")
    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }
    @Column(name="coupon_id")
    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }
    @Column(name="coupon_nu")
    public Integer getCouponNum() {
        return couponNum;
    }

    public void setCouponNum(Integer couponNum) {
        this.couponNum = couponNum;
    }
    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
