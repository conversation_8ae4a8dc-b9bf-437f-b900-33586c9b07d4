package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 维修师表对应的实体
 * 
 * <AUTHOR>
 * @date 2016-6-1上午10:45:50
 * @description
 */
@Entity
@Table(name = "tyt_mechant_service")
public class TytMechantService implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	/* 维修师所属用户的id */
	private Long userId;
	/* 维修师所属用户的电话 */
	private String cellPhone;
	/* 维修师头像地址 */
	private String headImageUrl;
	/* 维修师的名称 */
	private String name;
	/* 维修师手机号，以、号分隔 */
	private String telePhones;
	/* 创建时间 */
	private Date createTime;
	/* 更新时间 */
	private Date updateTime;
	/* 被浏览次数 */
	private Integer readCount;
	/* 被拨打电话次数 */
	private Integer callCount;
	/* 最后一次所在的省 */
	private String province;
	/* 最后一次所在的市 */
	private String city;
	/* 最后一次所在的县 */
	private String county;
	/* 最后一次所在的x轴坐标（公司给所有的区县给的坐标） */
	private Integer xCoord;
	/* 最后一次所在的y轴坐标（公司给所有的区县给的坐标） */
	private Integer yCoordint;
	/* 最后一次所在的经度（数据库乘以一万存储） */
	private Integer longitude;
	/* 最后一次所在的纬度（数据库乘以一万存储） */
	private Integer latitude;
	/* 最后一次在线时间 */
	private Date lastOnlineTime;
	/* 客户端标示 */
	private String clientSign;
	/* 客户端版本号 */
	private String clientVersion;
	/* 接活状态 0休息中 1接活中 2忙碌中 */
	private Integer status;
	/* 首页是否显示 0否 1是 */
	private Integer homePageDisplay;
	/* 说明信息 */
	private String remark;
	/* 0禁用 1正常*/
	private String enabled;
	/*虚拟阅读次数*/
	private Integer vritualReadTimes=0;
	/*收藏人数*/
	private Integer collectPeopleNumber=0;
	/*阅读人数*/
	private Integer	readPeopleNumber=0;

	@Column
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "head_image_url")
	public String getHeadImageUrl() {
		return headImageUrl;
	}

	public void setHeadImageUrl(String headImageUrl) {
		this.headImageUrl = headImageUrl;
	}

	@Column(name = "name")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "tele_phones")
	public String getTelePhones() {
		return telePhones;
	}

	public void setTelePhones(String telePhones) {
		this.telePhones = telePhones;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "read_count")
	public Integer getReadCount() {
		return readCount;
	}

	public void setReadCount(Integer readCount) {
		this.readCount = readCount;
	}

	@Column(name = "call_count")
	public Integer getCallCount() {
		return callCount;
	}

	public void setCallCount(Integer callCount) {
		this.callCount = callCount;
	}

	@Column(name = "province")
	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Column(name = "city")
	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name = "county")
	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	@Column(name = "x_coord")
	public Integer getxCoord() {
		return xCoord;
	}

	public void setxCoord(Integer xCoord) {
		this.xCoord = xCoord;
	}

	@Column(name = "y_coord")
	public Integer getyCoordint() {
		return yCoordint;
	}

	public void setyCoordint(Integer yCoordint) {
		this.yCoordint = yCoordint;
	}

	@Column(name = "longitude")
	public Integer getLongitude() {
		return longitude;
	}

	public void setLongitude(Integer longitude) {
		this.longitude = longitude;
	}

	@Column(name = "latitude")
	public int getLatitude() {
		return latitude;
	}

	public void setLatitude(Integer latitude) {
		this.latitude = latitude;
	}

	@Column(name = "last_online_time")
	public Date getLastOnlineTime() {
		return lastOnlineTime;
	}

	public void setLastOnlineTime(Date lastOnlineTime) {
		this.lastOnlineTime = lastOnlineTime;
	}

	@Column(name = "client_sign")
	public String getClientSign() {
		return clientSign;
	}

	public void setClientSign(String clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name = "client_version")
	public String getClientVersion() {
		return clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "home_page_display")
	public Integer getHomePageDisplay() {
		return homePageDisplay;
	}

	public void setHomePageDisplay(Integer homePageDisplay) {
		this.homePageDisplay = homePageDisplay;
	}
	
	@Column(name = "enabled")
	public String getEnabled() {
		return enabled;
	}

	public void setEnabled(String enabled) {
		this.enabled = enabled;
	}
	@Column(name = "vritual_read_times")
	public Integer getVritualReadTimes() {
		return vritualReadTimes;
	}

	public void setVritualReadTimes(Integer vritualReadTimes) {
		this.vritualReadTimes = vritualReadTimes;
	}
	@Column(name = "collect_people_number")
	public Integer getCollectPeopleNumber() {
		return collectPeopleNumber;
	}

	public void setCollectPeopleNumber(Integer collectPeopleNumber) {
		this.collectPeopleNumber = collectPeopleNumber;
	}
	@Column(name = "read_people_number")
	public Integer getReadPeopleNumber() {
		return readPeopleNumber;
	}

	public void setReadPeopleNumber(Integer readPeopleNumber) {
		this.readPeopleNumber = readPeopleNumber;
	}

	@Override
	public String toString() {
		return "TytMechantService [id=" + id + ", userId=" + userId + ", cellPhone=" + cellPhone + ", headImageUrl=" + headImageUrl + ", name=" + name + ", telePhones=" + telePhones + ", createTime=" + createTime + ", updateTime=" + updateTime + ", readCount=" + readCount + ", callCount=" + callCount + ", province=" + province + ", city=" + city + ", county=" + county + ", xCoord=" + xCoord + ", yCoordint=" + yCoordint + ", longitude=" + longitude + ", latitude=" + latitude + ", lastOnlineTime=" + lastOnlineTime + ", clientSign=" + clientSign + ", clientVersion=" + clientVersion + ", status=" + status + ", homePageDisplay=" + homePageDisplay + ", remark=" + remark + ", enabled=" + enabled+"]";
	}
}
