package com.tyt.model;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * @ClassName CarInsuranceInquiry
 * @Description TODO
 * <AUTHOR>
 * @Date 2019-03-13 14:50
 * @Version 1.0
 */
@Entity
@Table(name = "car_insurance_inquiry", schema = "tyt", catalog = "")
public class CarInsuranceInquiry {
    private Long id;
    private Long userId;
    private String userName;
    private String cellPhone;
    private String linkPhone;
    private String linkPhone1;
    private String linkPhone2;
    private String carHeadCity;
    private String carHeadNo;
    private Integer carOwner;
    private Integer insuranceType;
    private Long serviceUserId;
    private String serviceUserName;
    private Timestamp dealTime;
    //处理结果(跟踪状态) 1.待处理 2.处理中 3.已处理 4.已投保
    private Integer dealStatus;
    private String remark;
    private Long createUserId;
    private String createUserName;
    private Timestamp ctime;
    private Long updateUserId;
    private String updateUserName;
    private Timestamp utime;

    //车险询价信息-提交时间
    private Timestamp commitTime;
    //车头行驶本url
    private String headDrivingUrl;
    /** 临时排序用字段*/
    private Integer tempId;
    //车辆认证状态 0:认证中；1:认证成功；2：认证失败
    private String auth;

    @Transient
    public String getAuth() {
        return auth;
    }

    public void setAuth(String auth) {
        this.auth = auth;
    }

    @Transient
    public Integer getTempId() {
        return tempId;
    }

    public void setTempId(Integer tempId) {
        this.tempId = tempId;
    }

    @Transient
    public Timestamp getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(Timestamp commitTime) {
        this.commitTime = commitTime;
    }

    @Transient
    public String getHeadDrivingUrl() {
        return headDrivingUrl;
    }

    public void setHeadDrivingUrl(String headDrivingUrl) {
        this.headDrivingUrl = headDrivingUrl;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "user_name")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Basic
    @Column(name = "cell_phone")
    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    @Basic
    @Column(name = "link_phone")
    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    @Basic
    @Column(name = "link_phone1")
    public String getLinkPhone1() {
        return linkPhone1;
    }

    public void setLinkPhone1(String linkPhone1) {
        this.linkPhone1 = linkPhone1;
    }

    @Basic
    @Column(name = "link_phone2")
    public String getLinkPhone2() {
        return linkPhone2;
    }

    public void setLinkPhone2(String linkPhone2) {
        this.linkPhone2 = linkPhone2;
    }

    @Basic
    @Column(name = "car_head_city")
    public String getCarHeadCity() {
        return carHeadCity;
    }

    public void setCarHeadCity(String carHeadCity) {
        this.carHeadCity = carHeadCity;
    }

    @Basic
    @Column(name = "car_head_no")
    public String getCarHeadNo() {
        return carHeadNo;
    }

    public void setCarHeadNo(String carHeadNo) {
        this.carHeadNo = carHeadNo;
    }

    @Basic
    @Column(name = "car_owner")
    public Integer getCarOwner() {
        return carOwner;
    }

    public void setCarOwner(Integer carOwner) {
        this.carOwner = carOwner;
    }

    @Basic
    @Column(name = "insurance_type")
    public Integer getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(Integer insuranceType) {
        this.insuranceType = insuranceType;
    }

    @Basic
    @Column(name = "service_user_id")
    public Long getServiceUserId() {
        return serviceUserId;
    }

    public void setServiceUserId(Long serviceUserId) {
        this.serviceUserId = serviceUserId;
    }

    @Basic
    @Column(name = "service_user_name")
    public String getServiceUserName() {
        return serviceUserName;
    }

    public void setServiceUserName(String serviceUserName) {
        this.serviceUserName = serviceUserName;
    }

    @Basic
    @Column(name = "deal_time")
    public Timestamp getDealTime() {
        return dealTime;
    }

    public void setDealTime(Timestamp dealTime) {
        this.dealTime = dealTime;
    }

    @Basic
    @Column(name = "deal_status")
    public Integer getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(Integer dealStatus) {
        this.dealStatus = dealStatus;
    }

    @Basic
    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Basic
    @Column(name = "create_user_id")
    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    @Basic
    @Column(name = "create_user_name")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @Basic
    @Column(name = "ctime")
    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "update_user_id")
    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    @Basic
    @Column(name = "update_user_name")
    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Basic
    @Column(name = "utime")
    public Timestamp getUtime() {
        return utime;
    }

    public void setUtime(Timestamp utime) {
        this.utime = utime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CarInsuranceInquiry that = (CarInsuranceInquiry) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(userName, that.userName) &&
                Objects.equals(cellPhone, that.cellPhone) &&
                Objects.equals(linkPhone, that.linkPhone) &&
                Objects.equals(linkPhone1, that.linkPhone1) &&
                Objects.equals(linkPhone2, that.linkPhone2) &&
                Objects.equals(carHeadCity, that.carHeadCity) &&
                Objects.equals(carHeadNo, that.carHeadNo) &&
                Objects.equals(carOwner, that.carOwner) &&
                Objects.equals(insuranceType, that.insuranceType) &&
                Objects.equals(serviceUserId, that.serviceUserId) &&
                Objects.equals(serviceUserName, that.serviceUserName) &&
                Objects.equals(dealTime, that.dealTime) &&
                Objects.equals(dealStatus, that.dealStatus) &&
                Objects.equals(remark, that.remark) &&
                Objects.equals(createUserId, that.createUserId) &&
                Objects.equals(createUserName, that.createUserName) &&
                Objects.equals(ctime, that.ctime) &&
                Objects.equals(updateUserId, that.updateUserId) &&
                Objects.equals(updateUserName, that.updateUserName) &&
                Objects.equals(utime, that.utime) &&
                Objects.equals(commitTime, that.commitTime) &&
                Objects.equals(headDrivingUrl, that.headDrivingUrl) &&
                Objects.equals(tempId, that.tempId) &&
                Objects.equals(auth, that.auth);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, userId, userName, cellPhone, linkPhone, linkPhone1, linkPhone2, carHeadCity, carHeadNo, carOwner, insuranceType, serviceUserId, serviceUserName, dealTime, dealStatus, remark, createUserId, createUserName, ctime, updateUserId, updateUserName, utime, commitTime, headDrivingUrl, tempId, auth);
    }
}
