package com.tyt.model;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.persistence.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * User: Administrator Date: 17-11-04 Time: 下午3:47
 */
@Entity
@Table(name = "tyt_transport_mt")
public class TransportMt implements Serializable {

	private static final long serialVersionUID = 1L;
	private String price;// 运费
	private Long id;
	private String startPoint;
	private String destPoint;
	private String taskContent;
	private String tel;
	private Long pubQQ;
	private Integer status;
	private Integer source;
	private String pubTime;
	private Timestamp ctime;
	private Timestamp mtime;
	private String nickName;
	private String uploadCellPhone;
	private Integer resend;
	private String startCoord;
	private String destCoord;
	private Integer platId;
	private Integer verifyFlag;

	/* 20150716新增字段 */
	Long userId;// '发布人usreID 关联tyt_user表id',
	String priceCode;// '运费代码',
	String startDetailAdd;// '出发地详细地址',
	String destDetailAdd;// '目的地详细地址',
	Date pubDate;// '发布日期',
	String goodsCode;// '货物代码 与 货物对应 task_content 字段是一对',
	String weightCode;// '重量代码',
	String weight;// '重量单位吨',
	String length;// '货物长单位米',
	String wide;// '货物宽单位米',
	String high;// '货物高单位米',
	String isSuperelevation;// '是否三超 0未超1超',
	String linkman;// '联系人',
	String remark;// '备注',
	Date pubGoodsTime;// '发货日期',
	String tel3;// 联系人2
	String tel4;// 联系人3

	/* 辅助字段 */
	String startCoordX;// '出发地坐标x',
	String startCoordY;// '出发地坐标y',
	String destCoordX;// '目的地坐标x',
	String destCoordY;// '目的地坐标y',
	String startLatitude;// '出发地纬度',
	String startLongitude;// '出发地经度',
	String destLongitude;// '目的地经度',
	String destLatitude;// '目的地纬度',
	String distance;// '出发地目的地之间距离', 直线距离

	Integer startCoordXValue;// '出发地坐标x',
	Integer startCoordYValue;// '出发地坐标y',
	Integer destCoordXValue;// '目的地坐标x',
	Integer destCoordYValue;// '目的地坐标y',
	Integer startLatitudeValue;// '出发地纬度',
	Integer startLongitudeValue;// '出发地经度',
	Integer destLongitudeValue;// '目的地经度',
	Integer destLatitudeValue;// '目的地纬度',
	Integer distanceValue;// '出发地目的地之间距离',直线距离

	/* 2015-08-21为解决pc问题新增 */
	String displayType;
	String hashCode;

	/* 2015-08-24 */
	String isCar;
	Integer userType;
	/* 2015-09-12 */
	String pcOldContent;

	/* 2015-09-22 */
	Integer resendCounts = 0;
	// 实名认证验证信息标志0未验证1通过3认证失败
	Integer verifyPhotoSign;

	// 用户分数
	Integer userPart;

	// 重发后原信息ID
	Long srcMsgId;

	// 出发地 目的地 省市区
	String startProvinc;
	String startCity;
	String startArea;
	String destProvinc;
	String destCity;
	String destArea;
	String clientVersion;// 版本号

	private String isInfoFee; // 是否收信息费货源 0是不需要1是需要
	private String infoStatus;// 信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货
								// ）3车主装货完成 4系统装货完成 5异常上报
	private String tsOrderNo;
	private Date releaseTime;
	private Date regTime;// 发货人注册时间
	/*
	 * 货物型号
	 */
	private String type;
	/*
	 * 货物品牌
	 */
	private String brand;
	/*
	 * 货物类型，如"挖掘机"等
	 */
	private String goodTypeName;
	/*
	 * 货物的台数，针对标准化的数据
	 */
	private Integer goodNumber;
	/*
	 * 是否是标准化数据：0是，1不是
	 */
	private Integer isStandard;
	/*
	 * 匹配项的ID，针对标准化的数据
	 */
	private Integer matchItemId;

	private Integer androidDistance;
	private Integer iosDistance;
	private Integer isDisplay;

	// 聚合表字段

	/**
	 * 货主类型，2-企业货主，1-个人货主
	 */
	private Integer shipperType;
	/**
	 * 货方名称或公司名称
	 */
	private String shipperName;
	/**
	 * 货主电话
	 */
	private String shipperPhone;
	/**
	 * 公里数
	 */
	private Integer routeDistance;
	/**
	 * 特殊要求，不对外展示
	 */
	private String innerNote;
	/**
	 * 信息费
	 */
	private Integer agencyMoney;
	/**
	 * 首个支付信息费人员ID
	 */
	private Long firstPayUserId;
	/**
	 * 首个支付信息费人员
	 */
	private Integer firstPayMoney;
	/**
	 * 最终成交价
	 */
	private Integer strikePrice;
	/**
	 * 预留手机
	 */
	private String beforePhone;
	/**
	 * 承运方电话
	 */
	private String carryPhone;
	/**
	 * 司机电话
	 */
	private String driverPhone;
	/**
	 * 几人已付信息费
	 */
	private Integer morePay;
	/**
	 * 成交时间
	 */
	private Date closingTime;
	/**
	 * 停止时间
	 */
	private Date stopTime;
	/**
	 * 货源ID
	 */
	private Long tsId;
	/**
	 * 操作人
	 */
	private String operator;
	/**
	 * 操作人ID
	 */
	private Long operatorId;
	/**
	 * 公司客服发布人员
	 */
	private String publisher;
	/**
	 * 公司客服发布人员ID
	 */
	private Long publisherId;

	// 跟踪人用户ID
	private Long tracerUserId;
	// 跟踪人姓名
	private String tracerName;
	// 装车时间
	private Date loadingTime;
	// 运价参考值 存完整的字符串
	private String priceReference;
	/*
	 * 1 授权状态 2 未授权状态 该状态只用于已成交货源
	 */
	private Integer grantLocationStatus = 2;


	/**
	 * 司机驾驶此类货物：1-需要，2-不需要
	 */
	private Integer driverDriving;

	/**
	 * 装货联系电话
	 */
	private String loadCellPhone;

	/**
	 * 卸货联系电话
	 */
	private String unloadCellPhone;

	/**
	 * 签约合作商ID
	 */
	private Long cargoOwnerId;

	//增加随车电话和副司机电话20180110
	private String secondaryDriverPhone;
	private String followDriverPhone;

	@Column(name = "match_item_id")
	public Integer getMatchItemId() {
		return matchItemId;
	}

	public void setMatchItemId(Integer matchItemId) {
		this.matchItemId = matchItemId;
	}

	@Column(name = "good_number")
	public Integer getGoodNumber() {
		return goodNumber;
	}

	public void setGoodNumber(Integer goodNumber) {
		this.goodNumber = goodNumber;
	}

	@Column(name = "is_standard")
	public Integer getIsStandard() {
		return isStandard;
	}

	public void setIsStandard(Integer isStandard) {
		this.isStandard = isStandard;
	}

	@Column(name = "good_type_name")
	public String getGoodTypeName() {
		return goodTypeName;
	}

	public void setGoodTypeName(String goodTypeName) {
		this.goodTypeName = goodTypeName;
	}

	@Column
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column
	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "start_point")
	public String getStartPoint() {
		return startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	@Column(name = "dest_point")
	public String getDestPoint() {
		return destPoint;
	}

	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}

	@Column(name = "task_content")
	public String getTaskContent() {
		return taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	@Column(name = "nick_name")
	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	@Column(name = "tel")
	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	@Column(name = "pub_qq")
	public Long getPubQQ() {
		return pubQQ;
	}

	public void setPubQQ(Long pubQQ) {
		this.pubQQ = pubQQ;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "source")
	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	@Column(name = "pub_time")
	public String getPubTime() {
		return pubTime;
	}

	public void setPubTime(String pubTime) {
		this.pubTime = pubTime;
	}

	@Column(name = "ctime")
	public Timestamp getCtime() {
		return ctime;
	}

	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Timestamp getMtime() {
		return mtime;
	}

	public void setMtime(Timestamp mtime) {
		this.mtime = mtime;
	}

	@Column(name = "upload_cellphone")
	public String getUploadCellPhone() {
		return uploadCellPhone;
	}

	public void setUploadCellPhone(String uploadCellPhone) {
		this.uploadCellPhone = uploadCellPhone;
	}

	@Column(name = "resend")
	public Integer getResend() {
		return resend;
	}

	public void setResend(Integer resend) {
		this.resend = resend;
	}

	@Column(name = "start_coord")
	public String getStartCoord() {
		return startCoord;
	}

	public void setStartCoord(String startCoord) {
		this.startCoord = startCoord;
	}

	@Column(name = "dest_coord")
	public String getDestCoord() {
		return destCoord;
	}

	public void setDestCoord(String destCoord) {
		this.destCoord = destCoord;
	}

	@Column(name = "plat_id")
	public Integer getPlatId() {
		return platId;
	}

	public void setPlatId(Integer platId) {
		this.platId = platId;
	}

	@Column(name = "verify_flag")
	public Integer getVerifyFlag() {
		return verifyFlag;
	}

	public void setVerifyFlag(Integer verifyFlag) {
		this.verifyFlag = verifyFlag;
	}

	@Column(name = "price")
	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "price_code")
	public String getPriceCode() {
		return priceCode;
	}

	public void setPriceCode(String priceCode) {
		this.priceCode = priceCode;
	}

	@Column(name = "start_detail_add")
	public String getStartDetailAdd() {
		return startDetailAdd;
	}

	public void setStartDetailAdd(String startDetailAdd) {
		this.startDetailAdd = startDetailAdd;
	}

	@Column(name = "dest_detail_add")
	public String getDestDetailAdd() {
		return destDetailAdd;
	}

	public void setDestDetailAdd(String destDetailAdd) {
		this.destDetailAdd = destDetailAdd;
	}

	@Column(name = "pub_date")
	public Date getPubDate() {
		return pubDate;
	}

	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}

	@Column(name = "goods_code")
	public String getGoodsCode() {
		return goodsCode;
	}

	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}

	@Column(name = "weight_code")
	public String getWeightCode() {
		return weightCode;
	}

	public void setWeightCode(String weightCode) {
		this.weightCode = weightCode;
	}

	@Column(name = "weight")
	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	@Column(name = "length")
	public String getLength() {
		return length;
	}

	public void setLength(String length) {
		this.length = length;
	}

	@Column(name = "wide")
	public String getWide() {
		return wide;
	}

	public void setWide(String wide) {
		this.wide = wide;
	}

	@Column(name = "high")
	public String getHigh() {
		return high;
	}

	public void setHigh(String high) {
		this.high = high;
	}

	@Column(name = "is_superelevation")
	public String getIsSuperelevation() {
		return isSuperelevation;
	}

	public void setIsSuperelevation(String isSuperelevation) {
		this.isSuperelevation = isSuperelevation;
	}

	@Column(name = "linkman")
	public String getLinkman() {
		return linkman;
	}

	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}

	@Column(name = "remark")
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(name = "pub_goods_time")
	public Date getPubGoodsTime() {
		return pubGoodsTime;
	}

	public void setPubGoodsTime(Date pubGoodsTime) {
		this.pubGoodsTime = pubGoodsTime;
	}

	@Column(name = "tel3")
	public String getTel3() {
		return tel3;
	}

	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}

	@Column(name = "tel4")
	public String getTel4() {
		return tel4;
	}

	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}

	@Column(name = "start_coord_x")
	public Integer getStartCoordXValue() {
		return startCoordXValue;
	}

	public void setStartCoordXValue(Integer startCoordXValue) {
		this.startCoordXValue = startCoordXValue;
	}

	@Column(name = "start_coord_y")
	public Integer getStartCoordYValue() {
		return startCoordYValue;
	}

	public void setStartCoordYValue(Integer startCoordYValue) {
		this.startCoordYValue = startCoordYValue;
	}

	@Column(name = "dest_coord_x")
	public Integer getDestCoordXValue() {
		return destCoordXValue;
	}

	public void setDestCoordXValue(Integer destCoordXValue) {
		this.destCoordXValue = destCoordXValue;
	}

	@Column(name = "dest_coord_y")
	public Integer getDestCoordYValue() {
		return destCoordYValue;
	}

	public void setDestCoordYValue(Integer destCoordYValue) {
		this.destCoordYValue = destCoordYValue;
	}

	@Column(name = "start_latitude")
	public Integer getStartLatitudeValue() {
		return startLatitudeValue;
	}

	public void setStartLatitudeValue(Integer startLatitudeValue) {
		this.startLatitudeValue = startLatitudeValue;
	}

	@Column(name = "start_longitude")
	public Integer getStartLongitudeValue() {
		return startLongitudeValue;
	}

	public void setStartLongitudeValue(Integer startLongitudeValue) {
		this.startLongitudeValue = startLongitudeValue;
	}

	@Column(name = "dest_longitude")
	public Integer getDestLongitudeValue() {
		return destLongitudeValue;
	}

	public void setDestLongitudeValue(Integer destLongitudeValue) {
		this.destLongitudeValue = destLongitudeValue;
	}

	@Column(name = "dest_latitude")
	public Integer getDestLatitudeValue() {
		return destLatitudeValue;
	}

	public void setDestLatitudeValue(Integer destLatitudeValue) {
		this.destLatitudeValue = destLatitudeValue;
	}

	@Column(name = "distance")
	public Integer getDistanceValue() {
		return distanceValue;
	}

	public void setDistanceValue(Integer distanceValue) {
		this.distanceValue = distanceValue;
	}

	/* 辅助 */
	@Transient
	public String getStartCoordX() {
		if (startCoordXValue == null)
			return "";
		return new BigDecimal(startCoordXValue).movePointLeft(2).toString();
	}

	public void setStartCoordX(String startCoordX) {
		this.startCoordX = startCoordX;
	}

	@Transient
	public String getStartCoordY() {
		if (startCoordYValue == null)
			return "";
		return new BigDecimal(startCoordYValue).movePointLeft(2).toString();
	}

	public void setStartCoordY(String startCoordY) {
		this.startCoordY = startCoordY;
	}

	@Transient
	public String getDestCoordX() {
		if (destCoordXValue == null)
			return "";
		return new BigDecimal(destCoordXValue).movePointLeft(2).toString();
	}

	public void setDestCoordX(String destCoordX) {
		this.destCoordX = destCoordX;
	}

	@Transient
	public String getDestCoordY() {
		if (destCoordYValue == null)
			return "";
		return new BigDecimal(destCoordYValue).movePointLeft(2).toString();
	}

	public void setDestCoordY(String destCoordY) {
		this.destCoordY = destCoordY;
	}

	@Transient
	public String getStartLatitude() {
		if (startLatitudeValue == null)
			return "";
		return new BigDecimal(startLatitudeValue).movePointLeft(2).toString();
	}

	public void setStartLatitude(String startLatitude) {
		this.startLatitude = startLatitude;
	}

	@Transient
	public String getStartLongitude() {
		if (startLongitudeValue == null)
			return "";
		return new BigDecimal(startLongitudeValue).movePointLeft(2).toString();
	}

	public void setStartLongitude(String startLongitude) {
		this.startLongitude = startLongitude;
	}

	@Transient
	public String getDestLongitude() {
		if (destLongitudeValue == null)
			return "";
		return new BigDecimal(destLongitudeValue).movePointLeft(2).toString();
	}

	public void setDestLongitude(String destLongitude) {
		this.destLongitude = destLongitude;
	}

	@Transient
	public String getDestLatitude() {
		if (destLatitudeValue == null)
			return "";
		return new BigDecimal(destLatitudeValue).movePointLeft(2).toString();
	}

	public void setDestLatitude(String destLatitude) {
		this.destLatitude = destLatitude;
	}

	@Transient
	public String getDistance() {
		if (distanceValue == null)
			return "";
		return new BigDecimal(distanceValue).movePointLeft(2).toString();
	}

	public void setDistance(String distance) {
		this.distance = distance;
	}

	@Column(name = "display_type")
	public String getDisplayType() {
		return displayType;
	}

	public void setDisplayType(String displayType) {
		this.displayType = displayType;
	}

	@Column(name = "hash_code")
	public String getHashCode() {
		return hashCode;
	}

	public void setHashCode(String hashCode) {
		this.hashCode = hashCode;
	}

	@Column(name = "is_car")
	public String getIsCar() {
		return isCar;
	}

	public void setIsCar(String isCar) {
		this.isCar = isCar;
	}

	@Column(name = "user_type")
	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	@Column(name = "pc_old_content")
	public String getPcOldContent() {
		return pcOldContent;
	}

	public void setPcOldContent(String pcOldContent) {
		this.pcOldContent = pcOldContent;
	}

	@Column(name = "resend_counts")
	public Integer getResendCounts() {
		return resendCounts;
	}

	public void setResendCounts(Integer resendCounts) {
		this.resendCounts = resendCounts;
	}

	@Column(name = "verify_photo_sign")
	public Integer getVerifyPhotoSign() {
		return verifyPhotoSign;
	}

	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
		this.verifyPhotoSign = verifyPhotoSign;
	}

	@Column(name = "user_part")
	public Integer getUserPart() {
		return userPart;
	}

	public void setUserPart(Integer userPart) {
		this.userPart = userPart;
	}

	@Column(name = "src_msg_id")
	public Long getSrcMsgId() {
		return srcMsgId;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}

	@Column(name = "start_provinc")
	public String getStartProvinc() {
		return startProvinc;
	}

	@Column(name = "start_city")
	public String getStartCity() {
		return startCity;
	}

	@Column(name = "start_area")
	public String getStartArea() {
		return startArea;
	}

	@Column(name = "dest_provinc")
	public String getDestProvinc() {
		return destProvinc;
	}

	@Column(name = "dest_city")
	public String getDestCity() {
		return destCity;
	}

	@Column(name = "dest_area")
	public String getDestArea() {
		return destArea;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "client_version")
	public String getClientVersion() {
		return clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	@Column(name = "is_info_fee")
	public String getIsInfoFee() {
		return isInfoFee;
	}

	@Column(name = "info_status")
	public String getInfoStatus() {
		return infoStatus;
	}

	@Column(name = "ts_order_no")
	public String getTsOrderNo() {
		return tsOrderNo;
	}

	@Column(name = "release_time")
	public Date getReleaseTime() {
		return releaseTime;
	}

	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}

	public void setInfoStatus(String infoStatus) {
		this.infoStatus = infoStatus;
	}

	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}

	public void setReleaseTime(Date releaseTime) {
		this.releaseTime = releaseTime;
	}

	@Column(name = "reg_time")
	public Date getRegTime() {
		return regTime;
	}

	public void setRegTime(Date regTime) {
		this.regTime = regTime;
	}

	@Column(name = "android_distance")
	public Integer getAndroidDistance() {
		return androidDistance;
	}

	@Column(name = "ios_distance")
	public Integer getIosDistance() {
		return iosDistance;
	}

	@Column(name = "is_display")
	public Integer getIsDisplay() {
		return isDisplay;
	}

	public void setAndroidDistance(Integer androidDistance) {
		this.androidDistance = androidDistance;
	}

	public void setIosDistance(Integer iosDistance) {
		this.iosDistance = iosDistance;
	}

	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}

	@Column(name = "shipper_type")
	public Integer getShipperType() {
		return shipperType;
	}

	public void setShipperType(Integer shipperType) {
		this.shipperType = shipperType;
	}

	@Column(name = "shipper_phone")
	public String getShipperPhone() {
		return shipperPhone;
	}

	public void setShipperPhone(String shipperPhone) {
		this.shipperPhone = shipperPhone;
	}

	@Column(name = "route_distance")
	public Integer getRouteDistance() {
		return routeDistance;
	}

	public void setRouteDistance(Integer routeDistance) {
		this.routeDistance = routeDistance;
	}

	@Column(name = "inner_note")
	public String getInnerNote() {
		return innerNote;
	}

	public void setInnerNote(String innerNote) {
		this.innerNote = innerNote;
	}

	@Column(name = "agency_money")
	public Integer getAgencyMoney() {
		return agencyMoney;
	}

	public void setAgencyMoney(Integer agencyMoney) {
		this.agencyMoney = agencyMoney;
	}

	@Column(name = "first_pay_money")
	public Integer getFirstPayMoney() {
		return firstPayMoney;
	}

	public void setFirstPayMoney(Integer firstPayMoney) {
		this.firstPayMoney = firstPayMoney;
	}

	@Column(name = "strike_price")
	public Integer getStrikePrice() {
		return strikePrice;
	}

	public void setStrikePrice(Integer strikePrice) {
		this.strikePrice = strikePrice;
	}

	@Column(name = "before_phone")
	public String getBeforePhone() {
		return beforePhone;
	}

	public void setBeforePhone(String beforePhone) {
		this.beforePhone = beforePhone;
	}

	@Column(name = "carry_phone")
	public String getCarryPhone() {
		return carryPhone;
	}

	public void setCarryPhone(String carryPhone) {
		this.carryPhone = carryPhone;
	}

	@Column(name = "driver_phone")
	public String getDriverPhone() {
		return driverPhone;
	}

	public void setDriverPhone(String driverPhone) {
		this.driverPhone = driverPhone;
	}

	@Column(name = "more_pay")
	public Integer getMorePay() {
		return morePay;
	}

	public void setMorePay(Integer morePay) {
		this.morePay = morePay;
	}

	@Column(name = "ts_id")
	public Long getTsId() {
		return tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "shipper_name")
	public String getShipperName() {
		return shipperName;
	}

	public void setShipperName(String shipperName) {
		this.shipperName = shipperName;
	}

	@Column(name = "closing_time")
	public Date getClosingTime() {
		return closingTime;
	}

	public void setClosingTime(Date closingTime) {
		this.closingTime = closingTime;
	}

	@Column(name = "stop_time")
	public Date getStopTime() {
		return stopTime;
	}

	public void setStopTime(Date stopTime) {
		this.stopTime = stopTime;
	}

	@Column(name = "operator")
	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	@Column(name = "operator_id")
	public Long getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}

	@Column(name = "first_pay_user_id")
	public Long getFirstPayUserId() {
		return firstPayUserId;
	}

	public void setFirstPayUserId(Long firstPayUserId) {
		this.firstPayUserId = firstPayUserId;
	}

	@Column(name = "publisher")
	public String getPublisher() {
		return publisher;
	}

	public void setPublisher(String publisher) {
		this.publisher = publisher;
	}

	@Column(name = "publisher_id")
	public Long getPublisherId() {
		return publisherId;
	}

	public void setPublisherId(Long publisherId) {
		this.publisherId = publisherId;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	@Column(name = "tracer_user_id")
	public Long getTracerUserId() {
		return tracerUserId;
	}

	@Column(name = "tracer_name")
	public String getTracerName() {
		return tracerName;
	}

	@Column(name = "loading_time")
	public Date getLoadingTime() {
		return loadingTime;
	}

	@Column(name = "price_reference")
	public String getPriceReference() {
		return priceReference;
	}

	public void setTracerUserId(Long tracerUserId) {
		this.tracerUserId = tracerUserId;
	}

	public void setTracerName(String tracerName) {
		this.tracerName = tracerName;
	}

	public void setLoadingTime(Date loadingTime) {
		this.loadingTime = loadingTime;
	}

	public void setPriceReference(String priceReference) {
		this.priceReference = priceReference;
	}

	@Column(name = "grant_location_status")
	public Integer getGrantLocationStatus() {
		return grantLocationStatus;
	}

	public void setGrantLocationStatus(Integer grantLocationStatus) {
		this.grantLocationStatus = grantLocationStatus;
	}

	@Column(name = "driver_driving")
	public Integer getDriverDriving() {
		return driverDriving;
	}

	public void setDriverDriving(Integer driverDriving) {
		this.driverDriving = driverDriving;
	}

	@Column(name = "load_cell_phone")
	public String getLoadCellPhone() {
		return loadCellPhone;
	}

	public void setLoadCellPhone(String loadCellPhone) {
		this.loadCellPhone = loadCellPhone;
	}

	@Column(name = "unload_cell_phone")
	public String getUnloadCellPhone() {
		return unloadCellPhone;
	}

	public void setUnloadCellPhone(String unloadCellPhone) {
		this.unloadCellPhone = unloadCellPhone;
	}

	@Column(name = "cargo_owner_id")
	public Long getCargoOwnerId() {
		return cargoOwnerId;
	}

	public void setCargoOwnerId(Long cargoOwnerId) {
		this.cargoOwnerId = cargoOwnerId;
	}

	@Column(name = "secondary_driver_phone")
	public String getSecondaryDriverPhone() {
		return secondaryDriverPhone;
	}

	public void setSecondaryDriverPhone(String secondaryDriverPhone) {
		this.secondaryDriverPhone = secondaryDriverPhone;
	}

	@Column(name = "follow_driver_phone")
	public String getFollowDriverPhone() {
		return followDriverPhone;
	}

	public void setFollowDriverPhone(String followDriverPhone) {
		this.followDriverPhone = followDriverPhone;
	}

}
