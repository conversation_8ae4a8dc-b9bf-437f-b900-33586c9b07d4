package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-4-14下午6:48:05
 * @description
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "tyt_search_word", catalog = "tyt")
public class TytSearchWord implements java.io.Serializable {

	private long id;
	private String searchWord;
	private Date createTime;
	private Integer userId;
	private Integer clientType = 1;

	public TytSearchWord() {
	}

	public TytSearchWord(long id, String searchWord, Date createTime) {
		this.id = id;
		this.searchWord = searchWord;
		this.createTime = createTime;
	}

	public TytSearchWord(String searchWord, Date createTime, Integer userId) {
		this.searchWord = searchWord;
		this.createTime = createTime;
		this.userId = userId;
	}

	@Column(name = "user_id")
	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	@Column(name = "client_type")
	public Integer getClientType() {
		return clientType;
	}

	public void setClientType(Integer clientType) {
		this.clientType = clientType;
	}

	@Id
	@Column(name = "id", unique = true, nullable = false)
	public long getId() {
		return this.id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Column(name = "search_word", nullable = false, length = 32)
	public String getSearchWord() {
		return this.searchWord;
	}

	public void setSearchWord(String searchWord) {
		this.searchWord = searchWord;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "create_time", nullable = false, length = 0)
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
