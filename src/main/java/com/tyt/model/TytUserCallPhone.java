package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytUserCallPhone entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_user_call_phone", catalog = "tyt")
public class TytUserCallPhone implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 6699540596448984103L;
	private Long id;
	private Long userId;
	private Long tsId;
	private Date createTime;
	private String clientVersion;
	private Integer clientSign;
	private String path;
	/*
	 * 1精准货源模块，2 货源列表模块 3 货源详情模块 4 板车购买模块 5 板车司机招聘模块 6 板车维修模块 7 板车配件模块 8
	 * 机械设备维修模块 9 机械设备配件模块 10 维修师模块 11 机械司机招聘模块 12 机械司机求职模块 13.货站主页
	 */
	private String muduleType;
	/*
	 * 1 获取电话 2 获取电话成功 3 获取电话失败 4 拨打电话
	 */
	private Integer actionType;

	// Constructors

	/** default constructor */
	public TytUserCallPhone() {
	}

	/** full constructor */
	public TytUserCallPhone(long userId, long tsId, Date createTime) {
		this.userId = userId;
		this.tsId = tsId;
		this.createTime = createTime;
	}

	// Property accessors
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "action_type")
	public Integer getActionType() {
		return actionType;
	}

	public void setActionType(Integer actionType) {
		this.actionType = actionType;
	}

	@Column(name = "mudule_type")
	public String getMuduleType() {
		return muduleType;
	}

	public void setMuduleType(String muduleType) {
		this.muduleType = muduleType;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "ts_id")
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "client_version")
	public String getClientVersion() {
		return clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	@Column(name = "client_sign")
	public Integer getClientSign() {
		return clientSign;
	}

	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}

    @Column(name = "path")
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}