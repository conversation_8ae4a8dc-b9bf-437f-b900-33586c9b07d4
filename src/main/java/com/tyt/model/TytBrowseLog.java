package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytBrowseLog entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_browse_log")
public class TytBrowseLog implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 963661787510480295L;
	private Long id;
	private Long userId;
	private Long msgId;
	private String phone;
	private Integer moduleType;
	private Integer functionType;
	private Date ctime;
	private String clientSign;
	private String clientVersion;
	private String details;
	private String t1;
	private String t2;
	private String t3;
	private String t4;
	private String t5;
	private String t6;
	private String t7;
	private String t8;
	private String t9;
	private String t10;
	private String t11;
	private String t12;

	

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "msg_id")
	public Long getMsgId() {
		return this.msgId;
	}

	public void setMsgId(Long msgId) {
		this.msgId = msgId;
	}

	@Column(name = "phone", length = 20)
	public String getPhone() {
		return this.phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	@Column(name = "module_type")
	public Integer getModuleType() {
		return this.moduleType;
	}

	public void setModuleType(Integer moduleType) {
		this.moduleType = moduleType;
	}

	@Column(name = "function_type")
	public Integer getFunctionType() {
		return this.functionType;
	}

	public void setFunctionType(Integer functionType) {
		this.functionType = functionType;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "client_sign", length = 5)
	public String getClientSign() {
		return this.clientSign;
	}

	public void setClientSign(String clientSign) {
		this.clientSign = clientSign;
	}

	@Column(name = "client_version", length = 10)
	public String getClientVersion() {
		return this.clientVersion;
	}

	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}

	@Column(name = "details", length = 65535)
	public String getDetails() {
		return this.details;
	}

	public void setDetails(String details) {
		this.details = details;
	}

	@Column(name = "t1", length = 50)
	public String getT1() {
		return this.t1;
	}

	public void setT1(String t1) {
		this.t1 = t1;
	}

	@Column(name = "t2", length = 50)
	public String getT2() {
		return this.t2;
	}

	public void setT2(String t2) {
		this.t2 = t2;
	}

	@Column(name = "t3", length = 50)
	public String getT3() {
		return this.t3;
	}

	public void setT3(String t3) {
		this.t3 = t3;
	}

	@Column(name = "t4", length = 50)
	public String getT4() {
		return this.t4;
	}

	public void setT4(String t4) {
		this.t4 = t4;
	}

	@Column(name = "t5", length = 50)
	public String getT5() {
		return this.t5;
	}

	public void setT5(String t5) {
		this.t5 = t5;
	}

	@Column(name = "t6", length = 50)
	public String getT6() {
		return this.t6;
	}

	public void setT6(String t6) {
		this.t6 = t6;
	}

	@Column(name = "t7", length = 50)
	public String getT7() {
		return this.t7;
	}

	public void setT7(String t7) {
		this.t7 = t7;
	}

	@Column(name = "t8", length = 50)
	public String getT8() {
		return this.t8;
	}

	public void setT8(String t8) {
		this.t8 = t8;
	}

	@Column(name = "t9", length = 50)
	public String getT9() {
		return this.t9;
	}

	public void setT9(String t9) {
		this.t9 = t9;
	}

	@Column(name = "t10", length = 50)
	public String getT10() {
		return this.t10;
	}

	public void setT10(String t10) {
		this.t10 = t10;
	}

	@Column(name = "t11", length = 50)
	public String getT11() {
		return this.t11;
	}

	public void setT11(String t11) {
		this.t11 = t11;
	}

	@Column(name = "t12", length = 50)
	public String getT12() {
		return this.t12;
	}

	public void setT12(String t12) {
		this.t12 = t12;
	}

}