package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytSequence entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_sequence")
public class TytSequence implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -2000792721467951858L;
	private String name;
	private Long number;
	private String dates;
	// Constructors

	/** default constructor */
	public TytSequence() {
	}

	/** full constructor */
	public TytSequence(Long number) {
		this.number = number;
	}

	@Id
	@GeneratedValue
	@Column(name = "name", unique = true, nullable = false, length = 30)
	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "number")
	public Long getNumber() {
		return this.number;
	}

	public void setNumber(Long number) {
		this.number = number;
	}
	@Column(name = "dates")
	public String getDates() {
		return dates;
	}

	public void setDates(String dates) {
		this.dates = dates;
	}

}