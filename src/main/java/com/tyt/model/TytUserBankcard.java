package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_user_bankcard")
public class TytUserBankcard implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long id;//   
	private Long userId;//'银行卡所属用户id',
	private String cardNum;//'银行卡号',
	private String bankName;//'银行卡所属银行名称',
	private String fenhangName;//'银行卡所属分行名称',
	private Integer status;//'1:有效 2：无效 3：删除',
	private Date ctime;//'创建时间',
	private Date utime;//'更新时间'
	private String bankIcon;//银行图标

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name = "card_num")
	public String getCardNum() {
		return cardNum;
	}
	public void setCardNum(String cardNum) {
		this.cardNum = cardNum;
	}
	@Column(name = "bank_name")
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	@Column(name = "fenhang_name")
	public String getFenhangName() {
		return fenhangName;
	}
	public void setFenhangName(String fenhangName) {
		this.fenhangName = fenhangName;
	}
	@Column(name = "status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name = "ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name = "utime")
	public Date getUtime() {
		return utime;
	}
	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name = "bank_icon")
	public String getBankIcon() {
		return bankIcon;
	}
	public void setBankIcon(String bankIcon) {
		this.bankIcon = bankIcon;
	}
	@Override
	public String toString() {
		return "TytUserBankcard [id=" + id + ", userId=" + userId + ", cardNum=" + cardNum + ", bankName=" + bankName
				+ ", fenhangName=" + fenhangName + ", status=" + status + ", ctime=" + ctime + ", utime=" + utime
				+ ", bankIcon=" + bankIcon + "]";
	}
	
	

}
