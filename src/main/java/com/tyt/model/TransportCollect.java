package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

@Entity
@Table(name="tyt_transport_collect")
public class TransportCollect  implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1865873643327092985L;
	Long id ;
	String cellPhone;
	Long infoId ;//运输信息id
	Integer platId;//终端
	Timestamp ctime;
	Integer status;
	
	//20150730新增字段
	Long userId;//车ID
	Long carId;//车ID
	String headCity;//车头牌照头字母
	String headNo;//车头牌照号码
	String tailCity;//挂车牌照头字母
	String tailNo;//挂车牌照号码
	@Id
	@GeneratedValue
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="info_id")
	public Long getInfoId() {
		return infoId;
	}
	public void setInfoId(Long infoId) {
		this.infoId = infoId;
	}
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}
	public void setPlatId(Integer platId) {
		this.platId = platId;
	}
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
    @Column(name="car_id")	
	public Long getCarId() {
		return carId;
	}
	public void setCarId(Long carId) {
		this.carId = carId;
	}
	@Column(name="head_city")
	public String getHeadCity() {
		return headCity;
	}
	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}
	@Column(name="head_no")
	public String getHeadNo() {
		return headNo;
	}
	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="tail_city")
	public String getTailCity() {
		return tailCity;
	}
	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}
	@Column(name="tail_no")
	public String getTailNo() {
		return tailNo;
	}
	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
