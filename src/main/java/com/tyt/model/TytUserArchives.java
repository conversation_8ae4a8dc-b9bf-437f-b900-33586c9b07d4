package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_user_archives")
public class TytUserArchives implements Serializable {

    private static final long serialVersionUID = 7797272925813200670L;
    private Long id;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userNickname;

    /**
     * 用户手机号
     */
    private String userCellPhone;

    /**
     * 用户真实姓名
     */
    private String userTrueName;

    /**
     * 注册身份
     */
    private String identity;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 身份认证状态 0未认证1通过2认证中3认证失败
     */
    private Short identityAuth;

    /**
     * 车辆认证状态 0:未认证；1:认证成功；2：认证失败
     */
    private Short carAuth;

    /**
     * 销售审核一级身份
     */
    private String deliverTypeOne;

    /**
     * 上次登录时间
     */
    private Date lastLoginTime;

    /**
     * 车身份标签（包括免费车体验，体验使用完，体验过期，免费试用，试用使用完，试用过期，车会员，过期车会员）
     */
    private String carVipLabel;

    /**
     * 车身份标签到期时间/使用完时间
     */
    private Date carvipDueDate;

    /**
     * 货身份标签（包括免费货体验，体验使用完，体验过期，免费试用，试用过期，试用使用完，货会员，过期货会员）
     */
    private String goodVipLabel;

    /**
     * 货身份标签到期时间/使用完时间
     */
    private Date goodvipDueDate;

    /**
     * 车会员第一次缴费时间
     */
    private Date carvipFirstPayDate;

    /**
     * 车会员最近一次缴费时间
     */
    private Date carvipLastPayDate;

    /**
     * 车会员缴费次数
     */
    private Integer carvipPayTimes;

    /**
     * 货会员第一次缴费时间
     */
    private Date goodvipFirstPayDate;

    /**
     * 货会员最近一次缴费时间
     */
    private Date goodvipLastPayDate;

    /**
     * 货会员缴费次数
     */
    private Integer goodvipPayTimes;

    /**
     * 发货次卡第一次缴费时间
     */
    private Date deliverGoodsFirstPayDate;

    /**
     * 发货次卡最近一次缴费时间
     **/
    private Date deliverGoodsLastPayDate;
    /**
     * 发货次卡到期时间
     */
    private Date deliverGoodsDueDate;

    /**
     * 发货次卡缴费次数
     */
    private Integer deliverGoodsPayTimes;

    /**
     * 找货次卡标签（货次卡有效，货次卡过期，货次卡使用完）
     */
    private String deliverGoodsLabel;

    /**
     * 第一次发货时间
     */
    private Date fistSendGoodsDate;

    /**
     * 最近一次发货时间
     */
    private Date lastSendGoodsDate;

    /**
     * 第一次找货时间
     */
    private Date firstCallPhoneDate;

    /**
     * 最近一次找货时间
     */
    private Date lastCallPhoneDate;

    /**
     *
     */
    private Date ctime;

    /**
     *
     */
    private Date utime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 用户id
     * @return user_id 用户id
     */
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    /**
     * 用户id
     * @param userId 用户id
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * 用户昵称
     * @return user_nickname 用户昵称
     */
    @Column(name = "user_nickname")
    public String getUserNickname() {
        return userNickname;
    }

    /**
     * 用户昵称
     * @param userNickname 用户昵称
     */
    public void setUserNickname(String userNickname) {
        this.userNickname = userNickname;
    }

    /**
     * 用户手机号
     * @return user_cell_phone 用户手机号
     */
    @Column(name = "user_cell_phone")
    public String getUserCellPhone() {
        return userCellPhone;
    }

    /**
     * 用户手机号
     * @param userCellPhone 用户手机号
     */
    public void setUserCellPhone(String userCellPhone) {
        this.userCellPhone = userCellPhone;
    }

    /**
     * 用户真实姓名
     * @return user_true_name 用户真实姓名
     */
    @Column(name = "user_true_name")
    public String getUserTrueName() {
        return userTrueName;
    }

    /**
     * 用户真实姓名
     * @param userTrueName 用户真实姓名
     */
    public void setUserTrueName(String userTrueName) {
        this.userTrueName = userTrueName;
    }

    /**
     * 注册身份
     * @return identity 注册身份
     */
    @Column(name = "identity")
    public String getIdentity() {
        return identity;
    }

    /**
     * 注册身份
     * @param identity 注册身份
     */
    public void setIdentity(String identity) {
        this.identity = identity;
    }

    /**
     * 注册时间
     * @return register_time 注册时间
     */
    @Column(name = "register_time")
    public Date getRegisterTime() {
        return registerTime;
    }

    /**
     * 注册时间
     * @param registerTime 注册时间
     */
    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    /**
     * 身份认证状态 0未认证1通过2认证中3认证失败
     * @return identity_auth 身份认证状态 0未认证1通过2认证中3认证失败
     */
    @Column(name = "identity_auth")
    public Short getIdentityAuth() {
        return identityAuth;
    }

    /**
     * 身份认证状态 0未认证1通过2认证中3认证失败
     * @param identityAuth 身份认证状态 0未认证1通过2认证中3认证失败
     */
    public void setIdentityAuth(Short identityAuth) {
        this.identityAuth = identityAuth;
    }

    /**
     * 车辆认证状态 0:未认证；1:认证成功；2：认证失败
     * @return car_auth 车辆认证状态 0:未认证；1:认证成功；2：认证失败
     */
    @Column(name = "car_auth")
    public Short getCarAuth() {
        return carAuth;
    }

    /**
     * 车辆认证状态 0:未认证；1:认证成功；2：认证失败
     * @param carAuth 车辆认证状态 0:未认证；1:认证成功；2：认证失败
     */
    public void setCarAuth(Short carAuth) {
        this.carAuth = carAuth;
    }

    /**
     * 销售审核一级身份
     * @return deliver_type_one 销售审核一级身份
     */
    @Column(name = "deliver_type_one")
    public String getDeliverTypeOne() {
        return deliverTypeOne;
    }

    /**
     * 销售审核一级身份
     * @param deliverTypeOne 销售审核一级身份
     */
    public void setDeliverTypeOne(String deliverTypeOne) {
        this.deliverTypeOne = deliverTypeOne;
    }

    /**
     * 上次登录时间
     * @return last_login_time 上次登录时间
     */
    @Column(name = "last_login_time")
    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    /**
     * 上次登录时间
     * @param lastLoginTime 上次登录时间
     */
    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    /**
     * 车身份标签（包括免费车体验，体验使用完，体验过期，免费试用，试用使用完，试用过期，车会员，过期车会员）
     * @return car_vip_label 车身份标签（包括免费车体验，体验使用完，体验过期，免费试用，试用使用完，试用过期，车会员，过期车会员）
     */
    @Column(name = "car_vip_label")
    public String getCarVipLabel() {
        return carVipLabel;
    }

    /**
     * 车身份标签（包括免费车体验，体验使用完，体验过期，免费试用，试用使用完，试用过期，车会员，过期车会员）
     * @param carVipLabel 车身份标签（包括免费车体验，体验使用完，体验过期，免费试用，试用使用完，试用过期，车会员，过期车会员）
     */
    public void setCarVipLabel(String carVipLabel) {
        this.carVipLabel = carVipLabel;
    }

    /**
     * 车身份标签到期时间/使用完时间
     * @return carvip_due_date 车身份标签到期时间/使用完时间
     */
    @Column(name = "carvip_due_date")
    public Date getCarvipDueDate() {
        return carvipDueDate;
    }

    /**
     * 车身份标签到期时间/使用完时间
     * @param carvipDueDate 车身份标签到期时间/使用完时间
     */
    public void setCarvipDueDate(Date carvipDueDate) {
        this.carvipDueDate = carvipDueDate;
    }

    /**
     * 货身份标签（包括免费货体验，体验使用完，体验过期，免费试用，试用过期，试用使用完，货会员，过期货会员）
     * @return good_vip_label 货身份标签（包括免费货体验，体验使用完，体验过期，免费试用，试用过期，试用使用完，货会员，过期货会员）
     */
    @Column(name = "good_vip_label")
    public String getGoodVipLabel() {
        return goodVipLabel;
    }

    /**
     * 货身份标签（包括免费货体验，体验使用完，体验过期，免费试用，试用过期，试用使用完，货会员，过期货会员）
     * @param goodVipLabel 货身份标签（包括免费货体验，体验使用完，体验过期，免费试用，试用过期，试用使用完，货会员，过期货会员）
     */
    public void setGoodVipLabel(String goodVipLabel) {
        this.goodVipLabel = goodVipLabel;
    }

    /**
     * 货身份标签到期时间/使用完时间
     * @return goodvip_due_date 货身份标签到期时间/使用完时间
     */
    @Column(name = "goodvip_due_date")
    public Date getGoodvipDueDate() {
        return goodvipDueDate;
    }

    /**
     * 货身份标签到期时间/使用完时间
     * @param goodvipDueDate 货身份标签到期时间/使用完时间
     */
    public void setGoodvipDueDate(Date goodvipDueDate) {
        this.goodvipDueDate = goodvipDueDate;
    }

    /**
     * 车会员第一次缴费时间
     * @return carvip_first_pay_date 车会员第一次缴费时间
     */
    @Column(name = "carvip_first_pay_date")
    public Date getCarvipFirstPayDate() {
        return carvipFirstPayDate;
    }

    /**
     * 车会员第一次缴费时间
     * @param carvipFirstPayDate 车会员第一次缴费时间
     */
    public void setCarvipFirstPayDate(Date carvipFirstPayDate) {
        this.carvipFirstPayDate = carvipFirstPayDate;
    }

    /**
     * 车会员最近一次缴费时间
     * @return carvip_last_pay_date 车会员最近一次缴费时间
     */
    @Column(name = "carvip_last_pay_date")
    public Date getCarvipLastPayDate() {
        return carvipLastPayDate;
    }

    /**
     * 车会员最近一次缴费时间
     * @param carvipLastPayDate 车会员最近一次缴费时间
     */
    public void setCarvipLastPayDate(Date carvipLastPayDate) {
        this.carvipLastPayDate = carvipLastPayDate;
    }

    /**
     * 车会员缴费次数
     * @return carvip_pay_times 车会员缴费次数
     */
    @Column(name = "carvip_pay_times")
    public Integer getCarvipPayTimes() {
        return carvipPayTimes;
    }

    /**
     * 车会员缴费次数
     * @param carvipPayTimes 车会员缴费次数
     */
    public void setCarvipPayTimes(Integer carvipPayTimes) {
        this.carvipPayTimes = carvipPayTimes;
    }

    /**
     * 货会员第一次缴费时间
     * @return goodvip_first_pay_date 货会员第一次缴费时间
     */
    @Column(name = "goodvip_first_pay_date")
    public Date getGoodvipFirstPayDate() {
        return goodvipFirstPayDate;
    }

    /**
     * 货会员第一次缴费时间
     * @param goodvipFirstPayDate 货会员第一次缴费时间
     */
    public void setGoodvipFirstPayDate(Date goodvipFirstPayDate) {
        this.goodvipFirstPayDate = goodvipFirstPayDate;
    }

    /**
     * 货会员最近一次缴费时间
     * @return goodvip_last_pay_date 货会员最近一次缴费时间
     */
    @Column(name = "goodvip_last_pay_date")
    public Date getGoodvipLastPayDate() {
        return goodvipLastPayDate;
    }

    /**
     * 货会员最近一次缴费时间
     * @param goodvipLastPayDate 货会员最近一次缴费时间
     */
    public void setGoodvipLastPayDate(Date goodvipLastPayDate) {
        this.goodvipLastPayDate = goodvipLastPayDate;
    }

    /**
     * 货会员缴费次数
     * @return goodvip_pay_times 货会员缴费次数
     */
    @Column(name = "goodvip_pay_times")
    public Integer getGoodvipPayTimes() {
        return goodvipPayTimes;
    }

    /**
     * 货会员缴费次数
     * @param goodvipPayTimes 货会员缴费次数
     */
    public void setGoodvipPayTimes(Integer goodvipPayTimes) {
        this.goodvipPayTimes = goodvipPayTimes;
    }

    /**
     * 发货次卡第一次缴费时间
     * @return deliver_goods_first_pay_date 发货次卡第一次缴费时间
     */
    @Column(name = "deliver_goods_first_pay_date")
    public Date getDeliverGoodsFirstPayDate() {
        return deliverGoodsFirstPayDate;
    }

    /**
     * 发货次卡第一次缴费时间
     * @param deliverGoodsFirstPayDate 发货次卡第一次缴费时间
     */
    public void setDeliverGoodsFirstPayDate(Date deliverGoodsFirstPayDate) {
        this.deliverGoodsFirstPayDate = deliverGoodsFirstPayDate;
    }

    @Column(name = "deliver_goods_last_pay_date")
    public Date getDeliverGoodsLastPayDate() {
        return deliverGoodsLastPayDate;
    }

    public void setDeliverGoodsLastPayDate(Date deliverGoodsLastPayDate) {
        this.deliverGoodsLastPayDate = deliverGoodsLastPayDate;
    }

    /**
     * 发货次卡到期时间
     * @return deliver_goods_due_date 发货次卡到期时间
     */
    @Column(name = "deliver_goods_due_date")
    public Date getDeliverGoodsDueDate() {
        return deliverGoodsDueDate;
    }

    /**
     * 发货次卡到期时间
     * @param deliverGoodsDueDate 发货次卡到期时间
     */
    public void setDeliverGoodsDueDate(Date deliverGoodsDueDate) {
        this.deliverGoodsDueDate = deliverGoodsDueDate;
    }

    /**
     * 发货次卡缴费次数
     * @return deliver_goods_pay_times 发货次卡缴费次数
     */
    @Column(name = "deliver_goods_pay_times")
    public Integer getDeliverGoodsPayTimes() {
        return deliverGoodsPayTimes;
    }

    /**
     * 发货次卡缴费次数
     * @param deliverGoodsPayTimes 发货次卡缴费次数
     */
    public void setDeliverGoodsPayTimes(Integer deliverGoodsPayTimes) {
        this.deliverGoodsPayTimes = deliverGoodsPayTimes;
    }

    /**
     * 找货次卡标签（货次卡有效，货次卡过期，货次卡使用完）
     * @return deliver_goods_label 找货次卡标签（货次卡有效，货次卡过期，货次卡使用完）
     */
    @Column(name = "deliver_goods_label")
    public String getDeliverGoodsLabel() {
        return deliverGoodsLabel;
    }

    /**
     * 找货次卡标签（货次卡有效，货次卡过期，货次卡使用完）
     * @param deliverGoodsLabel 找货次卡标签（货次卡有效，货次卡过期，货次卡使用完）
     */
    public void setDeliverGoodsLabel(String deliverGoodsLabel) {
        this.deliverGoodsLabel = deliverGoodsLabel;
    }

    /**
     * 第一次发货时间
     * @return fist_send_goods_date 第一次发货时间
     */
    @Column(name = "fist_send_goods_date")
    public Date getFistSendGoodsDate() {
        return fistSendGoodsDate;
    }

    /**
     * 第一次发货时间
     * @param fistSendGoodsDate 第一次发货时间
     */
    public void setFistSendGoodsDate(Date fistSendGoodsDate) {
        this.fistSendGoodsDate = fistSendGoodsDate;
    }

    /**
     * 最近一次发货时间
     * @return last_send_goods_date 最近一次发货时间
     */
    @Column(name = "last_send_goods_date")
    public Date getLastSendGoodsDate() {
        return lastSendGoodsDate;
    }

    /**
     * 最近一次发货时间
     * @param lastSendGoodsDate 最近一次发货时间
     */
    public void setLastSendGoodsDate(Date lastSendGoodsDate) {
        this.lastSendGoodsDate = lastSendGoodsDate;
    }

    /**
     * 第一次找货时间
     * @return first_call_phone_date 第一次找货时间
     */
    @Column(name = "first_call_phone_date")
    public Date getFirstCallPhoneDate() {
        return firstCallPhoneDate;
    }

    /**
     * 第一次找货时间
     * @param firstCallPhoneDate 第一次找货时间
     */
    public void setFirstCallPhoneDate(Date firstCallPhoneDate) {
        this.firstCallPhoneDate = firstCallPhoneDate;
    }

    /**
     * 最近一次找货时间
     * @return last_call_phone_date 最近一次找货时间
     */
    @Column(name = "last_call_phone_date")
    public Date getLastCallPhoneDate() {
        return lastCallPhoneDate;
    }

    /**
     * 最近一次找货时间
     * @param lastCallPhoneDate 最近一次找货时间
     */
    public void setLastCallPhoneDate(Date lastCallPhoneDate) {
        this.lastCallPhoneDate = lastCallPhoneDate;
    }

    /**
     *
     * @return ctime
     */
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    /**
     *
     * @param ctime
     */
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    /**
     *
     * @return utime
     */
    @Column(name = "utime")
    public Date getUtime() {
        return utime;
    }

    /**
     *
     * @param utime
     */
    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", userId=").append(userId);
        sb.append(", userNickname=").append(userNickname);
        sb.append(", userCellPhone=").append(userCellPhone);
        sb.append(", userTrueName=").append(userTrueName);
        sb.append(", identity=").append(identity);
        sb.append(", registerTime=").append(registerTime);
        sb.append(", identityAuth=").append(identityAuth);
        sb.append(", carAuth=").append(carAuth);
        sb.append(", deliverTypeOne=").append(deliverTypeOne);
        sb.append(", lastLoginTime=").append(lastLoginTime);
        sb.append(", carVipLabel=").append(carVipLabel);
        sb.append(", carvipDueDate=").append(carvipDueDate);
        sb.append(", goodVipLabel=").append(goodVipLabel);
        sb.append(", goodvipDueDate=").append(goodvipDueDate);
        sb.append(", carvipFirstPayDate=").append(carvipFirstPayDate);
        sb.append(", carvipLastPayDate=").append(carvipLastPayDate);
        sb.append(", carvipPayTimes=").append(carvipPayTimes);
        sb.append(", goodvipFirstPayDate=").append(goodvipFirstPayDate);
        sb.append(", goodvipLastPayDate=").append(goodvipLastPayDate);
        sb.append(", goodvipPayTimes=").append(goodvipPayTimes);
        sb.append(", deliverGoodsFirstPayDate=").append(deliverGoodsFirstPayDate);
        sb.append(", deliverGoodsDueDate=").append(deliverGoodsDueDate);
        sb.append(", deliverGoodsPayTimes=").append(deliverGoodsPayTimes);
        sb.append(", deliverGoodsLabel=").append(deliverGoodsLabel);
        sb.append(", fistSendGoodsDate=").append(fistSendGoodsDate);
        sb.append(", lastSendGoodsDate=").append(lastSendGoodsDate);
        sb.append(", firstCallPhoneDate=").append(firstCallPhoneDate);
        sb.append(", lastCallPhoneDate=").append(lastCallPhoneDate);
        sb.append(", ctime=").append(ctime);
        sb.append(", utime=").append(utime);
        sb.append("]");
        return sb.toString();
    }
}
