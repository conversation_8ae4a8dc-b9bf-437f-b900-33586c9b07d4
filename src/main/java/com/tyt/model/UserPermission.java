package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "tyt_user_permission")
public class UserPermission {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 权益一级类型(对应service_permission表的ID,共8类)
     */
    private Integer servicePermissionId;

    /**
     * 权益二级类型(1.时间 2.次数 3.布尔类型)
     */
    private Integer permissionSortType;

    /**
     * 权益类型对应的唯一标识
     */
    private String servicePermissionTypeId;
    /**
     * 权益类型对应名称（货会员、车会员、发货次数、拨打次数）
     */
    private String servicePermissionTypeName;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 权益状态，1-有效，2-无效
     */
    private Integer status;

    /**
     * 权益总次数
     */
    private Integer totalNum;

    /**
     * 已使用次数
     */
    private Integer usedNum;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date utime;

    private Integer subType;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @Column(name = "service_permission_id")
    public Integer getServicePermissionId() {
        return servicePermissionId;
    }

    public void setServicePermissionId(Integer servicePermissionId) {
        this.servicePermissionId = servicePermissionId;
    }
    @Column(name = "permission_sort_type")
    public Integer getPermissionSortType() {
        return permissionSortType;
    }

    public void setPermissionSortType(Integer permissionSortType) {
        this.permissionSortType = permissionSortType;
    }
    @Column(name = "priority")
    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    @Column(name = "start_time")
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    @Column(name = "end_time")
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    @Column(name = "total_num")
    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }
    @Column(name = "used_num")
    public Integer getUsedNum() {
        return usedNum;
    }

    public void setUsedNum(Integer usedNum) {
        this.usedNum = usedNum;
    }
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name = "utime")
    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Column(name = "service_permission_type_id")
    public String getServicePermissionTypeId() {
        return servicePermissionTypeId;
    }

    public void setServicePermissionTypeId(String servicePermissionTypeId) {
        this.servicePermissionTypeId = servicePermissionTypeId;
    }
    @Column(name = "service_permission_type_name")
    public String getServicePermissionTypeName() {
        return servicePermissionTypeName;
    }

    public void setServicePermissionTypeName(String servicePermissionTypeName) {
        this.servicePermissionTypeName = servicePermissionTypeName;
    }

    @Column(name = "sub_type")
    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    @Override
    public String toString() {
        return "UserPermission{" +
                "userId=" + userId +
                ", servicePermissionTypeId='" + servicePermissionTypeId + '\'' +
                ", servicePermissionTypeName='" + servicePermissionTypeName + '\'' +
                '}';
    }

    public static enum PermissionTypeEnum{
        CAR_MEMBER("100101", "车会员"),
        GOODS_MEMBER("100201", "货会员"),
        CAR_SMALL_MEMBER("100103", "车小会员");
        private String code;
        private String msg;

        PermissionTypeEnum(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

    public static enum StatusEnum{
        EFFICIENT(1, "有效"),
        INVALID(2, "无效");
        private Integer code;
        private String msg;

        StatusEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

}
