package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 定向抽奖用户表
 * 
 * <AUTHOR>
 * @date 2021-09-04 10:14:10
 */
@Entity
@Table(name = "lottery_user")
public class LotteryUser{

	/**
	 * 定向抽奖用户表主键（自增）
	 */
	private Long id;
	/**
	 * 用户账号（手机号）
	 */
	private String userCallPhone;
	/**
	 * 用户表id
	 */
	private Long userId;
	/**
	 * 活动表id
	 */
	private Long drawActivityInfoId;
	/**
	 * 总抽奖次数
	 */
	private Long totalDrawsNum;
	/**
	 * 用户是否抽奖 0未参与 1已参与
	 */
	private Integer takeIn;

	@Column(name = "take_in")
	public Integer getTakeIn() {
		return takeIn;
	}

	public void setTakeIn(Integer takeIn) {
		this.takeIn = takeIn;
	}

	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 创建人
	 */
	private Long createBy;
	/**
	 * 修改人
	 */
	private Long updateBy;
	/**
	 * 是否删除（0：删除，1：不删除）
	 */
	private Integer isDelete;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	@Column(name = "user_call_phone")
	public String getUserCallPhone() {
		return userCallPhone;
	}
	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}
	@Column(name = "draw_activity_info_id")
	public Long getDrawActivityInfoId() {
		return drawActivityInfoId;
	}
	@Column(name = "total_draws_num")
	public Long getTotalDrawsNum() {
		return totalDrawsNum;
	}
	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}
	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	@Column(name = "create_by")
	public Long getCreateBy() {
		return createBy;
	}
	@Column(name = "update_by")
	public Long getUpdateBy() {
		return updateBy;
	}
	@Column(name = "is_delete")
	public Integer getIsDelete() {
		return isDelete;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public void setUserCallPhone(String userCallPhone) {
		this.userCallPhone = userCallPhone;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public void setDrawActivityInfoId(Long drawActivityInfoId) {
		this.drawActivityInfoId = drawActivityInfoId;
	}

	public void setTotalDrawsNum(Long totalDrawsNum) {
		this.totalDrawsNum = totalDrawsNum;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public void setCreateBy(Long createBy) {
		this.createBy = createBy;
	}

	public void setUpdateBy(Long updateBy) {
		this.updateBy = updateBy;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	public LotteryUser() {
	}

	public LotteryUser(Long id, String userCallPhone, Long userId, Long drawActivityInfoId, Long totalDrawsNum, Date createTime, Date updateTime, Long createBy, Long updateBy, Integer isDelete) {
		this.id = id;
		this.userCallPhone = userCallPhone;
		this.userId = userId;
		this.drawActivityInfoId = drawActivityInfoId;
		this.totalDrawsNum = totalDrawsNum;
		this.createTime = createTime;
		this.updateTime = updateTime;
		this.createBy = createBy;
		this.updateBy = updateBy;
		this.isDelete = isDelete;
	}

	public LotteryUser(Long userId, Integer isDelete) {
		this.userId = userId;
	}
}
