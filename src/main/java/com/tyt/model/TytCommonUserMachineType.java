package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 
 * <AUTHOR>
 * @date 2017-4-17下午3:52:14
 * @description
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "tyt_common_user_machine_type", catalog = "tyt")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = { "priority" })
public class TytCommonUserMachineType implements java.io.Serializable {

	private Long id;
	private String machineTypeName;
	private byte priority;

	public TytCommonUserMachineType() {
	}

	public TytCommonUserMachineType(String machineTypeName, byte priority) {
		this.machineTypeName = machineTypeName;
		this.priority = priority;
	}

	public TytCommonUserMachineType(String machineTypeName) {
		this.machineTypeName = machineTypeName;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@JsonProperty("machineType")
	@Column(name = "machine_type_name", nullable = false, length = 32)
	public String getMachineTypeName() {
		return this.machineTypeName;
	}

	public void setMachineTypeName(String machineTypeName) {
		this.machineTypeName = machineTypeName;
	}

	@Column(name = "priority", nullable = false)
	public byte getPriority() {
		return this.priority;
	}

	public void setPriority(byte priority) {
		this.priority = priority;
	}
	
	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
