package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "vip_lottery_draw_list")
public class VipLotteryDrawList implements Serializable {

    private static final long serialVersionUID = -4921310224600323194L;
    private Long id;
    private Long activityId;
    private Long userId;
    private Integer couponAmount;
    private Integer promoActivityId;
    private Integer isGrant; //优惠券是否已发放 1未发放 2已发放
    private Date grantTime;
    private Long buyvipPrizeOne;
    private Long buyvipPrizeTwo;
    private Long buyvipPrizeThree;
    private Date ctime;
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "coupon_amount")
    public Integer getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(Integer couponAmount) {
        this.couponAmount = couponAmount;
    }

    @Column(name = "promo_activity_id")
    public Integer getPromoActivityId() {
        return promoActivityId;
    }

    public void setPromoActivityId(Integer promoActivityId) {
        this.promoActivityId = promoActivityId;
    }

    @Column(name = "is_grant")
    public Integer getIsGrant() {
        return isGrant;
    }

    public void setIsGrant(Integer isGrant) {
        this.isGrant = isGrant;
    }

    @Column(name = "grant_time")
    public Date getGrantTime() {
        return grantTime;
    }

    public void setGrantTime(Date grantTime) {
        this.grantTime = grantTime;
    }

    @Column(name = "buyvip_prize_one")
    public Long getBuyvipPrizeOne() {
        return buyvipPrizeOne;
    }

    public void setBuyvipPrizeOne(Long buyvipPrizeOne) {
        this.buyvipPrizeOne = buyvipPrizeOne;
    }

    @Column(name = "buyvip_prize_two")
    public Long getBuyvipPrizeTwo() {
        return buyvipPrizeTwo;
    }

    public void setBuyvipPrizeTwo(Long buyvipPrizeTwo) {
        this.buyvipPrizeTwo = buyvipPrizeTwo;
    }

    @Column(name = "buyvip_prize_three")
    public Long getBuyvipPrizeThree() {
        return buyvipPrizeThree;
    }

    public void setBuyvipPrizeThree(Long buyvipPrizeThree) {
        this.buyvipPrizeThree = buyvipPrizeThree;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
