package com.tyt.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TytStimulateCarLocation
 * @description TODO
 * @date 2022-08-30 12:43
 */
@Entity
@Table(name = "tyt_stimulate_car_location", schema = "tyt", catalog = "")
public class TytStimulateCarLocation {
    private Long id;
    private Integer stimulateActivityId;
    private String headCity;
    private String headNo;
    private String tailCity;
    private String tailNo;
    private Date beginTime;
    private Date endTime;
    private Date ctime;

    private Integer type;

    private String reason;

    @Id
    @GeneratedValue
    @Column(name = "id", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "stimulate_activity_id", nullable = true)
    public Integer getStimulateActivityId() {
        return stimulateActivityId;
    }

    public void setStimulateActivityId(Integer stimulateActivityId) {
        this.stimulateActivityId = stimulateActivityId;
    }

    @Basic
    @Column(name = "head_city", nullable = true, length = 20)
    public String getHeadCity() {
        return headCity;
    }

    public void setHeadCity(String headCity) {
        this.headCity = headCity;
    }

    @Basic
    @Column(name = "head_no", nullable = true, length = 20)
    public String getHeadNo() {
        return headNo;
    }

    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }

    @Basic
    @Column(name = "tail_city", nullable = true, length = 20)
    public String getTailCity() {
        return tailCity;
    }

    public void setTailCity(String tailCity) {
        this.tailCity = tailCity;
    }

    @Basic
    @Column(name = "tail_no", nullable = true, length = 20)
    public String getTailNo() {
        return tailNo;
    }

    public void setTailNo(String tailNo) {
        this.tailNo = tailNo;
    }

    @Basic
    @Column(name = "begin_time", nullable = true)
    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    @Basic
    @Column(name = "end_time", nullable = true)
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Basic
    @Column(name = "ctime", nullable = true)
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "reason")
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TytStimulateCarLocation that = (TytStimulateCarLocation) o;
        return Objects.equals(id, that.id) && Objects.equals(stimulateActivityId, that.stimulateActivityId) && Objects.equals(headCity, that.headCity) && Objects.equals(headNo, that.headNo) && Objects.equals(tailCity, that.tailCity) && Objects.equals(tailNo, that.tailNo) && Objects.equals(beginTime, that.beginTime) && Objects.equals(endTime, that.endTime) && Objects.equals(ctime, that.ctime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, stimulateActivityId, headCity, headNo, tailCity, tailNo, beginTime, endTime, ctime);
    }
}
