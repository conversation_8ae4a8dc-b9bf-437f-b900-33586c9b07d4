package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;

/**
 * User: Administrator
 * Date: 13-12-21
 * Time: 下午11:29
 */
@Entity
@Table(name="tyt_verify_code")
public class VerifyCode implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 5318580072985962618L;
	private Long id;
    private String verifyCode;
    private Integer usedCount;

    public static Integer MAX_USED_COUNT = 1;


    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name="verify_code")
    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }
    @Column(name="used_count")
    public Integer getUsedCount() {
        return usedCount;
    }


    @Override
    public String toString() {
        return "VerifyCode{" +
                "id=" + id +
                ", verifyCode='" + verifyCode + '\'' +
                ", usedCount=" + usedCount +
                '}';
    }

    public void setUsedCount(Integer usedCount) {
        this.usedCount = usedCount;
    }


}
