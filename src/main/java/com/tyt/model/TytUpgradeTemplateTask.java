package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/7/30 16:15
 * @Version 1.0
 **/
@Entity
@Table(name = "tyt_upgrade_templatetask")
public class TytUpgradeTemplateTask implements Serializable {

    private Long id;
    //任务标题
    private String taskTitle;
    //模板id
    private String templateId;
    //模板名称
    private String templateName;
    //任务开始时间
    private Long taskStartTime;
    //任务结束时间
    private Long taskEndTime;
    //任务状态 0 暂停状态  1 启用状态
    private Long taskStatus;
    //可关闭标识 1 可关闭 0 不可关闭
    private Date closeFlag;
    //用户标识 1 全部用户 0 自定义用户'
    private Integer allUserFlag;
    //创建人
    private String creator;
    private Long creator_id;
    //修改人
    private String operator;
    private Long operatorId;
    private Date ctime;
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "task_title")
    public String getTaskTitle() {
        return taskTitle;
    }

    public void setTaskTitle(String taskTitle) {
        this.taskTitle = taskTitle;
    }
    @Column(name = "template_id")
    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }
    @Column(name = "template_name")
    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
    @Column(name = "task_start_time")
    public Long getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(Long taskStartTime) {
        this.taskStartTime = taskStartTime;
    }
    @Column(name = "task_end_time")
    public Long getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(Long taskEndTime) {
        this.taskEndTime = taskEndTime;
    }
    @Column(name = "task_status")
    public Long getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Long taskStatus) {
        this.taskStatus = taskStatus;
    }
    @Column(name = "close_flag")
    public Date getCloseFlag() {
        return closeFlag;
    }

    public void setCloseFlag(Date closeFlag) {
        this.closeFlag = closeFlag;
    }
    @Column(name = "all_user_flag")
    public Integer getAllUserFlag() {
        return allUserFlag;
    }

    public void setAllUserFlag(Integer allUserFlag) {
        this.allUserFlag = allUserFlag;
    }
    @Column(name = "creator")
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    @Column(name = "creator_id")
    public Long getCreator_id() {
        return creator_id;
    }

    public void setCreator_id(Long creator_id) {
        this.creator_id = creator_id;
    }
    @Column(name = "operator")
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    @Column(name = "operator_id")
    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
