package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 *  省份、高速、收费规则
 * Created by tianjw on 2017/5/23.
 * 0：按次数 （ 固定金额）
 * 1：按公里数 （固定金额X公里数）
 * 2：按公里数 （根据吨位计算金额X公里数）（需要表达式支持）0.06-(T-15)*0.03/25
 * 3：按吨公里数 （固定金额X公里数X吨数）
 * 4：按吨公里数 （根据吨位计算金额X公里数X吨数）（需要表达式支持）0.06-(T-15)*0.03/25
 */
@Entity
@Table(name = "tyt_highway_cost_rule")
public class TytHighwayCostRule implements Serializable{


    private static final long serialVersionUID = 249167014193410113L;
    private Long id;
    private String province; //省份
    private String highway;//高速
    private Float tonneBeginValue;//吨位起始值
    private Float tonneEndValue;//吨位结束值
    private Integer calcRuleType;//
    private String calcRuleValue;//
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name="id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name="province")
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }
    @Column(name="highway")
    public String getHighway() {
        return highway;
    }

    public void setHighway(String highway) {
        this.highway = highway;
    }
    @Column(name="tonne_begin_value")
    public Float getTonneBeginValue() {
        return tonneBeginValue;
    }

    public void setTonneBeginValue(Float tonneBeginValue) {
        this.tonneBeginValue = tonneBeginValue;
    }
    @Column(name="tonne_end_value")
    public Float getTonneEndValue() {
        return tonneEndValue;
    }

    public void setTonneEndValue(Float tonneEndValue) {
        this.tonneEndValue = tonneEndValue;
    }
    @Column(name="calc_rule_type")
    public Integer getCalcRuleType() {
        return calcRuleType;
    }

    public void setCalcRuleType(Integer calcRuleType) {
        this.calcRuleType = calcRuleType;
    }
    @Column(name="calc_rule_value")
    public String getCalcRuleValue() {
        return calcRuleValue;
    }

    public void setCalcRuleValue(String calcRuleValue) {
        this.calcRuleValue = calcRuleValue;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
