package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytNotify entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_notify")
public class TytNotify implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -4642230071851325702L;
	private Long id;
	private String remarks;
	private String title;
	private String content;
	private String openType;
	private String linkUrl;
	private Integer timeLong;
	private String searchExplain;
	private String searchStr;
	private String sendStatus;
	private Date sendTime;
	private Date ctime;
	private Date mtime;
	private String status;
	private String sendRange;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "remarks", length = 100)
	public String getRemarks() {
		return this.remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	@Column(name = "title", length = 50)
	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Column(name = "content", length = 100)
	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Column(name = "open_type", nullable = false, length = 4)
	public String getOpenType() {
		return this.openType;
	}

	public void setOpenType(String openType) {
		this.openType = openType;
	}

	@Column(name = "link_url", length = 200)
	public String getLinkUrl() {
		return this.linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	@Column(name = "time_long")
	public Integer getTimeLong() {
		return this.timeLong;
	}

	public void setTimeLong(Integer timeLong) {
		this.timeLong = timeLong;
	}

	@Column(name = "search_explain", length = 200)
	public String getSearchExplain() {
		return this.searchExplain;
	}

	public void setSearchExplain(String searchExplain) {
		this.searchExplain = searchExplain;
	}

	@Column(name = "search_str", length = 500)
	public String getSearchStr() {
		return this.searchStr;
	}

	public void setSearchStr(String searchStr) {
		this.searchStr = searchStr;
	}

	@Column(name = "send_status", nullable = false, length = 4)
	public String getSendStatus() {
		return this.sendStatus;
	}

	public void setSendStatus(String sendStatus) {
		this.sendStatus = sendStatus;
	}

	@Column(name = "send_time")
	public Date getSendTime() {
		return this.sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name = "status", nullable = false, length = 4)
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	@Column(name = "send_range")
	public String getSendRange() {
		return sendRange;
	}

	public void setSendRange(String sendRange) {
		this.sendRange = sendRange;
	}

}