package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017年8月29日上午11:54:12
 * @description
 */
@Entity
@Table(name = "freight_record_log", catalog = "tyt")
public class FreightRecordLog implements java.io.Serializable {
	private static final long serialVersionUID = -2137475722231147073L;

	private Long id;
	private String mapKey;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private Integer startCoordX;
	private Integer startCoordY;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private Integer destCoordX;
	private Integer destCoordY;
	private String provinceRoad;
	private Integer freightDistance;
	private Integer distance;
	private Short clientSign;
	private Date ctime;
	private Short status;

	public FreightRecordLog() {
	}

	public FreightRecordLog(String mapKey, String startProvinc, String startCity, String startArea, Integer startCoordX, Integer startCoordY, String destProvinc, String destCity, String destArea, Integer destCoordX, Integer destCoordY, String provinceRoad, Integer freightDistance, Integer distance, Short clientSign, Date ctime) {
		this.mapKey = mapKey;
		this.startProvinc = startProvinc;
		this.startCity = startCity;
		this.startArea = startArea;
		this.startCoordX = startCoordX;
		this.startCoordY = startCoordY;
		this.destProvinc = destProvinc;
		this.destCity = destCity;
		this.destArea = destArea;
		this.destCoordX = destCoordX;
		this.destCoordY = destCoordY;
		this.provinceRoad = provinceRoad;
		this.freightDistance = freightDistance;
		this.distance = distance;
		this.clientSign = clientSign;
		this.ctime = ctime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "map_key", length = 32)
	public String getMapKey() {
		return this.mapKey;
	}

	public void setMapKey(String mapKey) {
		this.mapKey = mapKey;
	}

	@Column(name = "status")
	public Short getStatus() {
		return status;
	}

	public void setStatus(Short status) {
		this.status = status;
	}

	@Column(name = "start_provinc", length = 50)
	public String getStartProvinc() {
		return this.startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	@Column(name = "start_city", length = 50)
	public String getStartCity() {
		return this.startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	@Column(name = "start_area", length = 50)
	public String getStartArea() {
		return this.startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	@Column(name = "start_coord_x")
	public Integer getStartCoordX() {
		return this.startCoordX;
	}

	public void setStartCoordX(Integer startCoordX) {
		this.startCoordX = startCoordX;
	}

	@Column(name = "start_coord_y")
	public Integer getStartCoordY() {
		return this.startCoordY;
	}

	public void setStartCoordY(Integer startCoordY) {
		this.startCoordY = startCoordY;
	}

	@Column(name = "dest_provinc", length = 50)
	public String getDestProvinc() {
		return this.destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	@Column(name = "dest_city", length = 50)
	public String getDestCity() {
		return this.destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	@Column(name = "dest_area", length = 50)
	public String getDestArea() {
		return this.destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "dest_coord_x")
	public Integer getDestCoordX() {
		return this.destCoordX;
	}

	public void setDestCoordX(Integer destCoordX) {
		this.destCoordX = destCoordX;
	}

	@Column(name = "dest_coord_y")
	public Integer getDestCoordY() {
		return this.destCoordY;
	}

	public void setDestCoordY(Integer destCoordY) {
		this.destCoordY = destCoordY;
	}

	@Column(name = "province_road", length = 2000)
	public String getProvinceRoad() {
		return this.provinceRoad;
	}

	public void setProvinceRoad(String provinceRoad) {
		this.provinceRoad = provinceRoad;
	}

	@Column(name = "freight_distance")
	public Integer getFreightDistance() {
		return this.freightDistance;
	}

	public void setFreightDistance(Integer freightDistance) {
		this.freightDistance = freightDistance;
	}

	@Column(name = "distance")
	public Integer getDistance() {
		return this.distance;
	}

	public void setDistance(Integer distance) {
		this.distance = distance;
	}

	@Column(name = "client_sign")
	public Short getClientSign() {
		return this.clientSign;
	}

	public void setClientSign(Short clientSign) {
		this.clientSign = clientSign;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
