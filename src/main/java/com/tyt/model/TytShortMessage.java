package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytShortMessage entity. <AUTHOR>
@Entity
@Table(name = "tyt_short_message")
public class TytShortMessage implements java.io.Serializable {

	private static final long serialVersionUID = 7535185595224367322L;
	private Long id;
	private String msgType;
	private Long msgId;
	private Long userId;
	private String fromPhone;
	private String toPhone;
	private String content;
	private Date createTime;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "msg_type")
	public String getMsgType() {
		return this.msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	@Column(name = "msg_id")
	public Long getMsgId() {
		return this.msgId;
	}

	public void setMsgId(Long msgId) {
		this.msgId = msgId;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "from_phone")
	public String getFromPhone() {
		return this.fromPhone;
	}

	public void setFromPhone(String fromPhone) {
		this.fromPhone = fromPhone;
	}

	@Column(name = "to_phone")
	public String getToPhone() {
		return this.toPhone;
	}

	public void setToPhone(String toPhone) {
		this.toPhone = toPhone;
	}

	@Column(name = "content")
	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}