package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

@Entity
@Table(name="tyt_user")
public class User implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
    private String cellPhone;
    private String recommenderTel;//推荐人手机号
	private Long qq;
    private String userName;
    private String password;
    private String trueName;
    private String idCard;
    private Integer userType;
    private String homePhone;
    private String pcSign;
    private Integer serveDays;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer userSign;
    private Integer qqModTimes;
    private Timestamp qqModTime;
    private String verifyCode;
    private String province;
    private String city;
    private String county;
    private Timestamp payDate;
    private Timestamp renewalDate;
    private Integer renewalYears;
    private String ticket;//登陆ticket
    private Integer contactNum;//QQ联系人数量
    private Integer infoUploadFlag;
    private Integer infoPublishFlag = 2;
    private Integer killBill =0;//0 1速易通
    private String sales;//跟踪销售
    private Integer  verifyFlag = 0 ;//身份验证标记 0 未通过 1通过
    private Integer  platId;
    private String note;
    private Integer phoneOpenFlag=0;//手机开通字段
    private Integer qqBoxFlag;//qq消息盒子标记位 打开 1 可不打开0 默认1
    private Integer payStatus;//付费状态
    private Integer phoneServeDays;//手机剩余天数
    private String source;//用户来源
    private Timestamp endTime;//用户到期时间
    private Timestamp appointTime;//预约时间
    private int isMock; //是否马甲用户
    private String mbUserId;//传递给满帮的userId
    private String mbMemberID;//满帮钱包会员号

    /**
     * 2017-08-30
     * 黑名单状态，0-否，1-是
     */
    private Integer blackStatus = 0;

    /*********新增以下字段*/
    String isCar;// '是否完善了车的信息',
    String isBank;// '是否绑定银行卡信息',
    String headUrl;//'头像url',
    String payPassword;//'支付密码',
    Integer clientSign;//'客户端标识 1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB',
    Date    lastTime;// '最后登陆时间',
    String clientVersion;//'客户端版本号',
    String osVersion;//系统版本号

    /*2015-8-31*/
    String sex;
    /*2015-09-07*/
    String deliverType;//发货方身份0货主1货站
    /*2015-11-10*/
    String isEnabled;//'用户有效标识 0无效 1有效',
    String sourceRemark;//'来源备注',
    String paymentReason;//'未付款原因',
    String bank;//'银行',
    Integer money;//'金额',

    /*2015-12-14*/
    Integer extraDays=0;//赠送天数
    Integer payNumber=0;//缴费次第
    String  payChannel;//'缴费渠道（1线上支付 2线下支付）',
    //实名认证验证照片标志0未验证1通过3认证失败
    Integer verifyPhotoSign;

    //用户分数
    Integer userPart;
    Integer remainNumber;

    //渠道
    Integer channel=0;
    String c1;
    String c2;
    String c3;

    //2017-03-06
    String deliver_type_one;//销售审核一级身份
    Integer userClass;//用户分类1、发货方2、车辆方 见 source  user_class
    Integer identityType;//用户身份见source表 user_identity_type

    private Integer carUserSign;
    private Integer goodsUserSign;
    private String carUserName;
    private Date carLastLoginTime;
    private Date goodsLastLoginTime;

    // 2021-01-23 开票、成为调度车
    /**
     * 是否调度车 1是 0不是
     */
    private Integer isDispatch;
    /**
     * 用户注册时所选的注册身份
     */
    private Integer registerIdentity;

    /*用户状态*/
    public static final int USER_TYPE_TRIAL = 0;
	public static final int USER_TYPE_VIP = 1;
    public static final int USER_TYPE_TRIAL_NOT_VERIFY =2;
    public static final int USER_SIGN_CAR_OWNER = 1;//车主
    public static final int USER_SIGN_EXCHANGE_STATION = 2;//配货站
    public static final int USER_SIGN_GOODS_OWNER = 3;//货主
    public static final int USER_SIGN_SALES = 4;//销售
    public static final int USER_SIGN_ADMIN = 5;//超级管理员
    public static final int USER_SIGN_ADMIN_GENERAL = 6;//普通管理员
    public static final int USER_SIGN_DRIVER = 7;//司机
    public static final int USER_SIGN_SALES_MANAGER = 8;//销售主管
    public static final int USER_SIGN_DATA = 9;//数据部
    public static final int USER_SIGN_WEB = 10;//网站人员
    public static final int USER_SIGN_CLEAN = 11;//信息清理员
    public static final int INFO_UPLOAD_DISABLE = 1;//默认值
    public static final int INFO_UPLOAD_ENABLE = 2;
    public static final int INFO_UPLOAD_RESERVED = 0;
    public static final int INFO_PUBLISH_DISABLE = 1;
    public static final int INFO_PUBLISH_ENABLE = 2;
    public static final int VERIFY_DISABLE = 0;
    public static final int VERIFY_ENABLE = 1;
    public static final int VERIFY_WAIT = 2;//认证中
    public static final int PHONE_OPEN_ENABLE = 1;//开通
    public static final int PHONE_OPEN_DISABLE = 0;//未开通
    public static final int QQ_BOX_FLAG_OPEN = 1;//打开
    public static final int QQ_BOX_FLAG_CLOSE = 0;//未打开

    /*续费年限*/
    public static final int RENEWAL_YEARS_ONE = 1;
    public static final int RENEWAL_YEARS_TWO = 2;
    public static final int RENEWAL_YEARS_THREE = 3;

    /*付费状态*/
    public static final int PAY_STATUS_FIRST = 1;
    public static final int PAY_STATUS_RENEWAL = 2;

    /*黑名单状态*/
    public static final int BLACKLIST_STATUS_Y = 1; // 是
    public static final int BLACKLIST_STATUS_N = 0; // 否
    /**
     * 用户自选身份
     */
    private Integer selectionIdentity;

    /**
     * 初始货量数量
     */
    private Integer initialNum;
    /**
     * 初始车量数量
     */
    private Integer initialCarNum;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name="recommender_tel")
    public String getRecommenderTel() {
		return recommenderTel;
	}
	public void setRecommenderTel(String recommenderTel) {
		this.recommenderTel = recommenderTel;
	}

    @Column(name="cell_phone")
    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    @Column(name="qq")
    public Long getQq() {
        return qq;
    }

    public void setQq(Long qq) {
        this.qq = qq;
    }

    @Column(name="user_name")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Column(name="password")
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Column(name="true_name")
    public String getTrueName() {
        return trueName;
    }

    public void setTrueName(String trueName) {
        this.trueName = trueName;
    }

    @Column(name="id_card")
    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    @Column(name="user_type")
    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    @Column(name="home_phone")
    public String getHomePhone() {
        return homePhone;
    }

    public void setHomePhone(String homePhone) {
        this.homePhone = homePhone;
    }

    @Column(name="pc_sign")
    public String getPcSign() {
        return pcSign;
    }

    public void setPcSign(String pcSign) {
        this.pcSign = pcSign;
    }

    @Column(name="serve_days")
    public Integer getServeDays() {
        return serveDays;
    }


    public void setServeDays(Integer serveDays) {
        this.serveDays = serveDays;
    }

    @Column(name="ctime")
    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    @Column(name="mtime")
    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }
    @Column(name="user_sign")
    public Integer getUserSign() {
        return userSign;
    }

    public void setUserSign(Integer userSign) {
        this.userSign = userSign;
    }
    @Column(name="qq_mod_times")
    public Integer getQqModTimes() {
        return qqModTimes;
    }

    @Column(name="qq_mod_time")
    public Timestamp getQqModTime() {
        return qqModTime;
    }

    public void setQqModTimes(Integer qqModTimes) {
        this.qqModTimes = qqModTimes;
    }

    public void setQqModTime(Timestamp qqModTime) {
        this.qqModTime = qqModTime;
    }

    @Column(name="verify_code")
    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }


    @Column(name="province")
    public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Column(name="city")
	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name="county")
    public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

    @Column(name="ticket")
    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    @Column(name="pay_date")
	public Timestamp getPayDate() {
		return payDate;
	}

	public void setPayDate(Timestamp payDate) {
		this.payDate = payDate;
	}
	@Column(name="info_upload_flag")
    public Integer getInfoUploadFlag() {
		return infoUploadFlag;
	}

	public void setInfoUploadFlag(Integer infoUploadFlag) {
		this.infoUploadFlag = infoUploadFlag;
	}

	@Column(name="contact_num")
	public Integer getContactNum() {
		return contactNum;
	}

	public void setContactNum(Integer contactNum) {
		this.contactNum = contactNum;
	}


	@Column(name="info_publish_flag")
	public Integer getInfoPublishFlag() {
		return infoPublishFlag;
	}

	public void setInfoPublishFlag(Integer infoPublishFlag) {
		this.infoPublishFlag = infoPublishFlag;
	}

	@Column(name="kill_bill")
	public Integer getKillBill() {
		return killBill;
	}

	public void setKillBill(Integer killBill) {
		this.killBill = killBill;
	}


	@Column(name="sales")
	public String getSales() {
		return sales;
	}

	public void setSales(String sales) {
		this.sales = sales;
	}
	@Column(name="verify_flag")
	public Integer getVerifyFlag() {
		return verifyFlag;
	}

	public void setVerifyFlag(Integer verifyFlag) {
		this.verifyFlag = verifyFlag;
	}
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}

	public void setPlatId(Integer platId) {
		this.platId = platId;
	}
	@Column(name="note")
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}

	@Column(name="phone_open_flag")
	public Integer getPhoneOpenFlag() {
		return phoneOpenFlag;
	}

	public void setPhoneOpenFlag(Integer phoneOpenFlag) {
		this.phoneOpenFlag = phoneOpenFlag;
	}

	@Column(name="qq_box_flag")
	public Integer getQqBoxFlag() {
		return qqBoxFlag;
	}

	public void setQqBoxFlag(Integer qqBoxFlag) {
		this.qqBoxFlag = qqBoxFlag;
	}

	@Column(name="renewal_date")
	public Timestamp getRenewalDate() {
		return renewalDate;
	}
	public void setRenewalDate(Timestamp renewalDate) {
		this.renewalDate = renewalDate;
	}


	@Column(name="renewal_years")
	public Integer getRenewalYears() {
		return renewalYears;
	}

	public void setRenewalYears(Integer renewalYears) {
		this.renewalYears = renewalYears;
	}

	@Column(name="pay_status")
	public Integer getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(Integer payStatus) {
		this.payStatus = payStatus;
	}

	@Column(name="phone_serve_days")
	public Integer getPhoneServeDays() {
		return phoneServeDays;
	}

	public void setPhoneServeDays(Integer phoneServeDays) {
		this.phoneServeDays = phoneServeDays;
	}
    @Column(name="source")
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	@Column(name="end_time")
	public Timestamp getEndTime() {
		return endTime;
	}
	public void setEndTime(Timestamp endTime) {
		this.endTime = endTime;
	}
	@Column(name="appoint_time")
	public Timestamp getAppointTime() {
		return appointTime;
	}

	public void setAppointTime(Timestamp appointTime) {
		this.appointTime = appointTime;
	}
    @Column(name="is_car")
	public String getIsCar() {
		return isCar;
	}
	public void setIsCar(String isCar) {
		this.isCar = isCar;
	}
	@Column(name="is_bank")
	public String getIsBank() {
		return isBank;
	}
	public void setIsBank(String isBank) {
		this.isBank = isBank;
	}
    @Column(name="head_url")
	public String getHeadUrl() {
		return headUrl;
	}

	public void setHeadUrl(String headUrl) {
		this.headUrl = headUrl;
	}
	@Column(name="pay_password")
	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}
    @Column(name="client_sign")
	public Integer getClientSign() {
		return clientSign;
	}
	public void setClientSign(Integer clientSign) {
		this.clientSign = clientSign;
	}
	@Column(name="last_time")
	public Date getLastTime() {
		return lastTime;
	}
	public void setLastTime(Date lastTime) {
		this.lastTime = lastTime;
	}
	@Column(name="client_version")
	public String getClientVersion() {
		return clientVersion;
	}
	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}
	@Column(name="os_version")
	public String getOsVersion() {
		return osVersion;
	}
	public void setOsVersion(String osVersion) {
		this.osVersion = osVersion;
	}
	@Column(name="sex")
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}

	@Column(name="deliver_type")
	public String getDeliverType() {
		return deliverType;
	}
	public void setDeliverType(String deliverType) {
		this.deliverType = deliverType;
	}

	@Column(name="is_enabled")
	public String getIsEnabled() {
		return isEnabled;
	}
	public void setIsEnabled(String isEnabled) {
		this.isEnabled = isEnabled;
	}
	@Column(name="source_remark")
	public String getSourceRemark() {
		return sourceRemark;
	}

	public void setSourceRemark(String sourceRemark) {
		this.sourceRemark = sourceRemark;
	}
	@Column(name="payment_reason")
	public String getPaymentReason() {
		return paymentReason;
	}
	public void setPaymentReason(String paymentReason) {
		this.paymentReason = paymentReason;
	}
	@Column(name="bank")
	public String getBank() {
		return bank;
	}
	public void setBank(String bank) {
		this.bank = bank;
	}
	@Column(name="money")
	public Integer getMoney() {
		return money;
	}

	public void setMoney(Integer money) {
		this.money = money;
	}

	@Column(name="extra_days")
    public Integer getExtraDays() {
			return extraDays;
	}
	public void setExtraDays(Integer extraDays) {
		this.extraDays = extraDays;
	}
	@Column(name="pay_number")
	public Integer getPayNumber() {
		return payNumber;
	}
	public void setPayNumber(Integer payNumber) {
		this.payNumber = payNumber;
	}
	@Column(name="pay_channel")
	public String getPayChannel() {
		return payChannel;
	}

	public void setPayChannel(String payChannel) {
		this.payChannel = payChannel;
	}

    @Column(name="is_dispatch")
    public Integer getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(Integer isDispatch) {
        this.isDispatch = isDispatch;
    }

    @Column(name="selection_identity")
    public Integer getSelectionIdentity() {
        return selectionIdentity;
    }

    public void setSelectionIdentity(Integer selectionIdentity) {
        this.selectionIdentity = selectionIdentity;
    }

    @Column(name="initial_num")
    public Integer getInitialNum() {
        return initialNum;
    }

    public void setInitialNum(Integer initialNum) {
        this.initialNum = initialNum;
    }

    @Column(name="initial_car_num")
    public Integer getInitialCarNum() {
        return initialCarNum;
    }

    public void setInitialCarNum(Integer initialCarNum) {
        this.initialCarNum = initialCarNum;
    }

    /**
     * 用户身份枚举
     *
     */
    public enum UserSignEnum {

    	车主(1),货主(3),司机(7);
    	public int code;
    	UserSignEnum(int code) {
              this.code = code;
          }
        public static UserSignEnum getClientSignEnum(int code) {
        	UserSignEnum[] userSignEnums = UserSignEnum.values();
            for (UserSignEnum userSignEnum : userSignEnums) {
                if (userSignEnum.code==code) {
                    return userSignEnum;
                }
            }
            return UserSignEnum.车主;
        }
        /**
         * 判读code是否正确
         * @param code
         * @return
         */
        public static boolean isUserSignEnumcode( int code) {
        	UserSignEnum[] userSignEnums = UserSignEnum.values();
            for (UserSignEnum userSignEnum : userSignEnums) {
                if (userSignEnum.code==code) {
                    return true;
                }
            }
            return false;
        }
    }

    @Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

    private Map<String,String> urlMap=new HashMap<String,String>();

    @Transient
    public Map<String, String> getUrlMap() {
		return urlMap;
	}
	public void setUrlMap(Map<String, String> urlMap) {
		this.urlMap = urlMap;
	}

    public User() {


    }
    @Column(name="verify_photo_sign")
   	public Integer getVerifyPhotoSign() {
   		return verifyPhotoSign;
   	}

   	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
   		this.verifyPhotoSign = verifyPhotoSign;
   	}

   	@Column(name="user_part")
   	public Integer getUserPart() {
   		return userPart;
   	}

   	public void setUserPart(Integer userPart) {
   		this.userPart = userPart;
   	}
	@Transient
	public Integer getRemainNumber() {
		return remainNumber;
	}

	public void setRemainNumber(Integer remainNumber) {
		this.remainNumber = remainNumber;
	}

	@Column(name="channel")
	public Integer getChannel() {
		return channel;
	}

	public void setChannel(Integer channel) {
		this.channel = channel;
	}
	@Column(name="c1")
	public String getC1() {
		return c1;
	}

	public void setC1(String c1) {
		this.c1 = c1;
	}
	@Column(name="c2")
	public String getC2() {
		return c2;
	}

	public void setC2(String c2) {
		this.c2 = c2;
	}
	@Column(name="c3")
	public String getC3() {
		return c3;
	}

	public void setC3(String c3) {
		this.c3 = c3;
	}
	@Column(name="deliver_type_one")
	public String getDeliver_type_one() {
		return deliver_type_one;
	}

	public void setDeliver_type_one(String deliver_type_one) {
		this.deliver_type_one = deliver_type_one;
	}
	@Column(name="user_class")
	public Integer getUserClass() {
		return userClass;
	}

	public void setUserClass(Integer userClass) {
		this.userClass = userClass;
	}
	@Column(name="identity_type")
	public Integer getIdentityType() {
		return identityType;
	}

	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

    @Column(name="black_status")
    public Integer getBlackStatus() {
        return blackStatus;
    }

    public void setBlackStatus(Integer blackStatus) {
        this.blackStatus = blackStatus;
    }

    @Column(name = "is_mock")
    public int getIsMock() {
        return isMock;
    }

    public void setIsMock(int isMock) {
        this.isMock = isMock;
    }

    @Column(name = "car_user_sign")
    public Integer getCarUserSign() {
        return carUserSign;
    }

    public void setCarUserSign(Integer carUserSign) {
        this.carUserSign = carUserSign;
    }

    @Column(name = "goods_user_sign")
    public Integer getGoodsUserSign() {
        return goodsUserSign;
    }

    public void setGoodsUserSign(Integer goodsUserSign) {
        this.goodsUserSign = goodsUserSign;
    }

    @Column(name = "car_user_name")
    public String getCarUserName() {
        return carUserName;
    }

    public void setCarUserName(String carUserName) {
        this.carUserName = carUserName;
    }

    @Column(name = "car_last_login_time")
    public Date getCarLastLoginTime() {
        return carLastLoginTime;
    }

    public void setCarLastLoginTime(Date carLastLoginTime) {
        this.carLastLoginTime = carLastLoginTime;
    }

    @Column(name = "goods_last_login_time")
    public Date getGoodsLastLoginTime() {
        return goodsLastLoginTime;
    }

    public void setGoodsLastLoginTime(Date goodsLastLoginTime) {
        this.goodsLastLoginTime = goodsLastLoginTime;
    }

    @Column(name = "mb_user_id")
    public String getMbUserId() {
        return mbUserId;
    }

    public void setMbUserId(String mbUserId) {
        this.mbUserId = mbUserId;
    }

    @Column(name = "mb_member_id")
    public String getMbMemberID() {
        return mbMemberID;
    }

    public void setMbMemberID(String mbMemberID) {
        this.mbMemberID = mbMemberID;
    }

    @Column(name = "register_identity")
    public Integer getRegisterIdentity() {
        return registerIdentity;
    }

    public void setRegisterIdentity(Integer registerIdentity) {
        this.registerIdentity = registerIdentity;
    }

    /**
     * 车辆认证状态枚举
     * 是否车辆认证 车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
     */
    public enum CarAuthEnum{
        UNAUTHORIZED("0", "未认证"),
        AUTHENTICATION_SUCCESS("1", "认证成功"),
        AUTHENTICATION_FAILURE("2", "认证失败");

        private String code;
        private String msg;

        public String getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }

        CarAuthEnum(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }
}
