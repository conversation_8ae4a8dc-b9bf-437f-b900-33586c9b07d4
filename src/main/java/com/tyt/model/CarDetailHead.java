package com.tyt.model;

import javax.persistence.*;
import java.util.Objects;

/**
 * @ClassName TytCarDetailHead
 * @Description 车头详情信息表
 * <AUTHOR>
 * @Date 2019-06-04 16:54
 * @Version 1.0
 */
@Entity
@Table(name = "tyt_car_detail_head", schema = "tyt", catalog = "")
public class CarDetailHead {
    private Long id;
    private Long carId;
    private Long userId;
    private String city;
    private String carNo;
    private String carType;
    private String owner;
    private String address;
    private String useNature;
    private String carBrand;
    private String carIdcode;
    private String carEngineNo;
    private String carRegister;
    private String issueDate;
    private String recordNo;
    private String people;
    private String totalWeight;
    private String curbWeight;
    private String checkWeight;
    private String towWeight;
    private String scrapDate;
    private String testDate;
    private String length;
    private String width;
    private String height;
    private String state;
    private String headSuccRemark;
    private String headBelongType;
    private String blongType;
    private String carBrandDetail;
    private String checkRecord;
    /**
     *驱动形式
     */
    private String drivingForm;
    /**
     *排放标准
     */
    private String emissionStandard;
    /**
     *所有人电话
     */
    private String headPhone;
    /**
     *道路运输证正面
     */
    private String roadCardPositiveUrl;
    /**
     *道路运输证反面
     */
    private String roadCardOtherSideUrl;
    /**
     *道路运输经营许可证
     */
    private String roadLicenseNoUrl;
    /**
     * 道路运输证类型
     */
    private String roadTransportType;
    private String carTypeName;
    /**
     * 牵引马力
     */
    private Integer horsePower;
    /**
     * 挂车最大轴荷
     * 车头最大轴荷
     */
    private Integer maximumAxleLoad;

    private String headIssueAuthority;
    @Transient
    public Integer getHorsePower() {
        return horsePower;
    }

    public void setHorsePower(Integer horsePower) {
        this.horsePower = horsePower;
    }

    @Transient
    public String getCarTypeName() {
        return carTypeName;
    }

    public void setCarTypeName(String carTypeName) {
        this.carTypeName = carTypeName;
    }

    @Column(name = "road_transport_type")
    public String getRoadTransportType() {
        return roadTransportType;
    }

    public void setRoadTransportType(String roadTransportType) {
        this.roadTransportType = roadTransportType;
    }

    @Column(name = "road_card_positive_url")
    public String getRoadCardPositiveUrl() {
        return roadCardPositiveUrl;
    }

    public void setRoadCardPositiveUrl(String roadCardPositiveUrl) {
        this.roadCardPositiveUrl = roadCardPositiveUrl;
    }
    @Column(name = "road_card_other_side_url")
    public String getRoadCardOtherSideUrl() {
        return roadCardOtherSideUrl;
    }

    public void setRoadCardOtherSideUrl(String roadCardOtherSideUrl) {
        this.roadCardOtherSideUrl = roadCardOtherSideUrl;
    }
    @Column(name = "road_license_no_url")
    public String getRoadLicenseNoUrl() {
        return roadLicenseNoUrl;
    }

    public void setRoadLicenseNoUrl(String roadLicenseNoUrl) {
        this.roadLicenseNoUrl = roadLicenseNoUrl;
    }

    @Transient
    public String getHeadPhone() {
        return headPhone;
    }

    public void setHeadPhone(String headPhone) {
        this.headPhone = headPhone;
    }

    @Transient
    public String getDrivingForm() {
        return drivingForm;
    }

    public void setDrivingForm(String drivingForm) {
        this.drivingForm = drivingForm;
    }
    @Transient
    public String getEmissionStandard() {
        return emissionStandard;
    }

    public void setEmissionStandard(String emissionStandard) {
        this.emissionStandard = emissionStandard;
    }

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "car_id")
    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    @Basic
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "city")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Basic
    @Column(name = "car_no")
    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    @Basic
    @Column(name = "car_type")
    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    @Basic
    @Column(name = "owner")
    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    @Basic
    @Column(name = "address")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Basic
    @Column(name = "use_nature")
    public String getUseNature() {
        return useNature;
    }

    public void setUseNature(String useNature) {
        this.useNature = useNature;
    }

    @Basic
    @Column(name = "car_brand")
    public String getCarBrand() {
        return carBrand;
    }

    public void setCarBrand(String carBrand) {
        this.carBrand = carBrand;
    }

    @Basic
    @Column(name = "car_idcode")
    public String getCarIdcode() {
        return carIdcode;
    }

    public void setCarIdcode(String carIdcode) {
        this.carIdcode = carIdcode;
    }

    @Basic
    @Column(name = "car_engine_no")
    public String getCarEngineNo() {
        return carEngineNo;
    }

    public void setCarEngineNo(String carEngineNo) {
        this.carEngineNo = carEngineNo;
    }

    @Basic
    @Column(name = "car_register")
    public String getCarRegister() {
        return carRegister;
    }

    public void setCarRegister(String carRegister) {
        this.carRegister = carRegister;
    }

    @Basic
    @Column(name = "issue_date")
    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    @Basic
    @Column(name = "record_no")
    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo;
    }

    @Basic
    @Column(name = "people")
    public String getPeople() {
        return people;
    }

    public void setPeople(String people) {
        this.people = people;
    }

    @Basic
    @Column(name = "total_weight")
    public String getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(String totalWeight) {
        this.totalWeight = totalWeight;
    }

    @Basic
    @Column(name = "curb_weight")
    public String getCurbWeight() {
        return curbWeight;
    }

    public void setCurbWeight(String curbWeight) {
        this.curbWeight = curbWeight;
    }

    @Basic
    @Column(name = "check_weight")
    public String getCheckWeight() {
        return checkWeight;
    }

    public void setCheckWeight(String checkWeight) {
        this.checkWeight = checkWeight;
    }

    @Basic
    @Column(name = "tow_weight")
    public String getTowWeight() {
        return towWeight;
    }

    public void setTowWeight(String towWeight) {
        this.towWeight = towWeight;
    }

    @Basic
    @Column(name = "scrap_date")
    public String getScrapDate() {
        return scrapDate;
    }

    public void setScrapDate(String scrapDate) {
        this.scrapDate = scrapDate;
    }

    @Basic
    @Column(name = "test_date")
    public String getTestDate() {
        return testDate;
    }

    public void setTestDate(String testDate) {
        this.testDate = testDate;
    }

    @Basic
    @Column(name = "length")
    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    @Basic
    @Column(name = "width")
    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    @Basic
    @Column(name = "height")
    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    @Basic
    @Column(name = "state")
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Basic
    @Column(name = "head_succ_remark")
    public String getHeadSuccRemark() {
        return headSuccRemark;
    }

    public void setHeadSuccRemark(String headSuccRemark) {
        this.headSuccRemark = headSuccRemark;
    }

    @Basic
    @Column(name = "head_belong_type")
    public String getHeadBelongType() {
        return headBelongType;
    }

    public void setHeadBelongType(String headBelongType) {
        this.headBelongType = headBelongType;
    }

    @Basic
    @Column(name = "blong_type")
    public String getBlongType() {
        return blongType;
    }

    public void setBlongType(String blongType) {
        this.blongType = blongType;
    }

    @Basic
    @Column(name = "car_brand_detail")
    public String getCarBrandDetail() {
        return carBrandDetail;
    }

    public void setCarBrandDetail(String carBrandDetail) {
        this.carBrandDetail = carBrandDetail;
    }

    @Basic
    @Column(name = "check_record")
    public String getCheckRecord() {
        return checkRecord;
    }

    public void setCheckRecord(String checkRecord) {
        this.checkRecord = checkRecord;
    }
    @Basic
    @Column(name = "maximum_axle_load")
    public Integer getMaximumAxleLoad() {
        return maximumAxleLoad;
    }

    public void setMaximumAxleLoad(Integer maximumAxleLoad) {
        this.maximumAxleLoad = maximumAxleLoad;
    }

    @Basic
    @Column(name = "head_issue_authority")
    public String getHeadIssueAuthority() {
        return headIssueAuthority;
    }

    public void setHeadIssueAuthority(String headIssueAuthority) {
        this.headIssueAuthority = headIssueAuthority;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CarDetailHead that = (CarDetailHead) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(carId, that.carId) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(city, that.city) &&
                Objects.equals(carNo, that.carNo) &&
                Objects.equals(carType, that.carType) &&
                Objects.equals(owner, that.owner) &&
                Objects.equals(address, that.address) &&
                Objects.equals(useNature, that.useNature) &&
                Objects.equals(carBrand, that.carBrand) &&
                Objects.equals(carIdcode, that.carIdcode) &&
                Objects.equals(carEngineNo, that.carEngineNo) &&
                Objects.equals(carRegister, that.carRegister) &&
                Objects.equals(issueDate, that.issueDate) &&
                Objects.equals(recordNo, that.recordNo) &&
                Objects.equals(people, that.people) &&
                Objects.equals(totalWeight, that.totalWeight) &&
                Objects.equals(curbWeight, that.curbWeight) &&
                Objects.equals(checkWeight, that.checkWeight) &&
                Objects.equals(towWeight, that.towWeight) &&
                Objects.equals(scrapDate, that.scrapDate) &&
                Objects.equals(testDate, that.testDate) &&
                Objects.equals(length, that.length) &&
                Objects.equals(width, that.width) &&
                Objects.equals(height, that.height) &&
                Objects.equals(state, that.state) &&
                Objects.equals(headSuccRemark, that.headSuccRemark) &&
                Objects.equals(headBelongType, that.headBelongType) &&
                Objects.equals(blongType, that.blongType) &&
                Objects.equals(carBrandDetail, that.carBrandDetail) &&
                Objects.equals(checkRecord, that.checkRecord);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, carId, userId, city, carNo, carType, owner, address, useNature, carBrand, carIdcode, carEngineNo, carRegister, issueDate, recordNo, people, totalWeight, curbWeight, checkWeight, towWeight, scrapDate, testDate, length, width, height, state, headSuccRemark, headBelongType, blongType, carBrandDetail, checkRecord);
    }
}
