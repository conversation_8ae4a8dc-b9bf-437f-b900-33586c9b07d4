package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

@Entity
@Table(name="tyt_version_new")
public class TytVersionNew implements Serializable{
 
	private static final long serialVersionUID = 4708270929704910347L;
	
	Long id;//id
	Date createTime;//'创建时间'
	Date updateTime;//'更新时间'
	String startVersion;// '开始版本号',
	String endVersion; //'结束版本号',
	String version;//'升级到的版本号',
	String downloadUrl;//'下载地址',
	String clientType;//'客户端类型 1：PC;2：android; 3:ios',
	String showContent;//'PC提示内容',
	String upgradeType;//'升级标识 0:提示升级；1：强制升级；2：是最新版本号',
	String status;//'状态 0：无效；1：有效',
	String remark;//'备注',
	
	String showContentNew;//<p>表示换行
	String downloadType;//1个人版2企业版

	Integer appMarket; //是否走应用市场 0否 1是

	Integer grayPeriod; //是否走灰度 0否 1是
	
	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="start_version")
	public String getStartVersion() {
		return startVersion;
	}
	public void setStartVersion(String startVersion) {
		this.startVersion = startVersion;
	}
	@Column(name="end_version")
	public String getEndVersion() {
		return endVersion;
	}
	public void setEndVersion(String endVersion) {
		this.endVersion = endVersion;
	}
	@Column(name="version")
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	@Column(name="download_url")
	public String getDownloadUrl() {
		return downloadUrl;
	}
	public void setDownloadUrl(String downloadUrl) {
		this.downloadUrl = downloadUrl;
	}
	@Column(name="client_type")
	public String getClientType() {
		return clientType;
	}
	public void setClientType(String clientType) {
		this.clientType = clientType;
	}
	@Column(name="show_content")
	public String getShowContent() {
		return showContent;
	}
	public void setShowContent(String showContent) {
		this.showContent = showContent;
	}
	@Column(name="upgrade_type")
	public String getUpgradeType() {
		return upgradeType;
	}
	public void setUpgradeType(String upgradeType) {
		this.upgradeType = upgradeType;
	}
	@Column(name="status")
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	@Column(name="remark")
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name="update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Column(name="show_content_new")
	public String getShowContentNew() {
		return showContentNew;
	}
	public void setShowContentNew(String showContentNew) {
		this.showContentNew = showContentNew;
	}
	@Column(name="download_type")
	public String getDownloadType() {
		return downloadType;
	}
	public void setDownloadType(String downloadType) {
		this.downloadType = downloadType;
	}

	@Column(name="app_market")
	public Integer getAppMarket() {
		return appMarket;
	}

	public void setAppMarket(Integer appMarket) {
		this.appMarket = appMarket;
	}

	@Column(name="gray_period")
	public Integer getGrayPeriod() {
		return grayPeriod;
	}

	public void setGrayPeriod(Integer grayPeriod) {
		this.grayPeriod = grayPeriod;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
