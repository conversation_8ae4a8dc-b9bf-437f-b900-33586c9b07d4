package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Entity
@Table(name="promo_coupon")
public class PromoCoupon implements Serializable {
    private static final long serialVersionUID = -2121484598156764988L;
    private int couponTypeId;

    private String couponName;

    private BigDecimal couponAmount;

    private int couponStatus;

    private int couponQty;
    private int remainQty;

    private String couponDesc;

    private int useScopeType;

    private String useScopeDetail;

    private int validType;

    private Date validDateBegin;

    private Date validDateEnd;

    private int validDays;

    private Date mtime;

    private Date ctime;

    @Id
    @GeneratedValue
    @Column(name = "coupon_type_id", unique = true, nullable = false)
    public int getCouponTypeId() {
        return couponTypeId;
    }

    public void setCouponTypeId(int couponTypeId) {
        this.couponTypeId = couponTypeId;
    }
    @Column(name="coupon_name")
    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }
    @Column(name="coupon_amount")
    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }
    @Column(name="coupon_status")
    public int getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(int couponStatus) {
        this.couponStatus = couponStatus;
    }
    @Column(name="coupon_qty")
    public int getCouponQty() {
        return couponQty;
    }

    public void setCouponQty(int couponQty) {
        this.couponQty = couponQty;
    }
    @Column(name="remain_qty")
    public int getRemainQty() {
        return remainQty;
    }

    public void setRemainQty(int remainQty) {
        this.remainQty = remainQty;
    }
    @Column(name="coupon_desc")
    public String getCouponDesc() {
        return couponDesc;
    }

    public void setCouponDesc(String couponDesc) {
        this.couponDesc = couponDesc;
    }
    @Column(name="use_scope_type")
    public int getUseScopeType() {
        return useScopeType;
    }

    public void setUseScopeType(int useScopeType) {
        this.useScopeType = useScopeType;
    }
    @Column(name="use_scope_detail")
    public String getUseScopeDetail() {
        return useScopeDetail;
    }

    public void setUseScopeDetail(String useScopeDetail) {
        this.useScopeDetail = useScopeDetail;
    }
    @Column(name="valid_type")
    public int getValidType() {
        return validType;
    }

    public void setValidType(int validType) {
        this.validType = validType;
    }
    @Column(name="valid_date_begin")
    public Date getValidDateBegin() {
        return validDateBegin;
    }

    public void setValidDateBegin(Date validDateBegin) {
        this.validDateBegin = validDateBegin;
    }
    @Column(name="valid_date_end")
    public Date getValidDateEnd() {
        return validDateEnd;
    }

    public void setValidDateEnd(Date validDateEnd) {
        this.validDateEnd = validDateEnd;
    }
    @Column(name="valid_days")
    public int getValidDays() {
        return validDays;
    }

    public void setValidDays(int validDays) {
        this.validDays = validDays;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
}
