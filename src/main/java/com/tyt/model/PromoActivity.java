package com.tyt.model;


import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "promo_activity")
public class PromoActivity {
    /**
     * "id"	"int(11)"	"NO"	"PRI"	NULL	"auto_increment"
     * "name"	"varchar(100)"	"NO"	""	""	""
     * "activity_date_begin"	"datetime"	"YES"	""	NULL	""
     * "valid_type"	"tinyint(4)"	"NO"	""	"0"	""
     * "valid_date_begin"	"datetime"	"YES"	""	NULL	""
     * "valid_date_end"	"datetime"	"YES"	""	NULL	""
     * "valid_days"	"int(11)"	"NO"	""	"0"	""
     * "condition"	"tinyint(4)"	"NO"	""	"1"	""
     * "user_scope"	"tinyint(4)"	"NO"	""	"1"	""
     * "user_identify"	"tinyint(4)"	"NO"	""	"1"	""
     * "user_status"	"tinyint(4)"	"NO"	""	"1"	""
     * "coupon_type_id"	"int(11)"	"NO"	""	"0"	""
     * "coupon_qty"	"int(11)"	"NO"	""	"0"	""
     * "coupon_remain_qty"	"int(11)"	"NO"	""	"0"	""
     * "activity_status"	"tinyint(4)"	"NO"	""	"1"	""
     * "mtime"	"timestamp"	"NO"	""	"CURRENT_TIMESTAMP"	""
     * "ctime"	"datetime"	"NO"	""	NULL	""
     * "activity_type"	"tinyint(4)"	"NO"	""	NULL	""
     */

    @Id
    @GeneratedValue
    private Integer id;

    @Column(name = "name")
    private String name;

    @Column(name = "activity_date_begin")
    private Date activityDateBegin;

    @Column(name = "activity_date_end")
    private Date activityDateEnd;

    @Column(name = "condition")
    private Integer condition;

    @Column(name = "grant_type_id")
    private Integer grantTypeId;

    @Column(name = "grant_type_name")
    private String grantTypeName;

    @Column(name = "grant_type")
    private Integer grantType;

    @Column(name = "group_id")
    private Integer groupId;

    @Column(name = "user_identify")
    private Integer userIdentify;

    @Column(name = "user_status")
    private String userStatus;

    @Column(name = "activity_status")
    private Integer activityStatus;

    @Column(name = "activity_type")
    private Integer activityType;

    @Column(name = "is_repeat")
    private Integer isRepeat;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getActivityDateBegin() {
        return activityDateBegin;
    }

    public void setActivityDateBegin(Date activityDateBegin) {
        this.activityDateBegin = activityDateBegin;
    }

    public Date getActivityDateEnd() {
        return activityDateEnd;
    }

    public void setActivityDateEnd(Date activityDateEnd) {
        this.activityDateEnd = activityDateEnd;
    }

    public Integer getCondition() {
        return condition;
    }

    public void setCondition(Integer condition) {
        this.condition = condition;
    }

    public Integer getGrantTypeId() {
        return grantTypeId;
    }

    public void setGrantTypeId(Integer grantTypeId) {
        this.grantTypeId = grantTypeId;
    }

    public String getGrantTypeName() {
        return grantTypeName;
    }

    public void setGrantTypeName(String grantTypeName) {
        this.grantTypeName = grantTypeName;
    }

    public Integer getGrantType() {
        return grantType;
    }

    public void setGrantType(Integer grantType) {
        this.grantType = grantType;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getUserIdentify() {
        return userIdentify;
    }

    public void setUserIdentify(Integer userIdentify) {
        this.userIdentify = userIdentify;
    }

    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public Integer getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(Integer activityStatus) {
        this.activityStatus = activityStatus;
    }

    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    public Integer getIsRepeat() {
        return isRepeat;
    }

    public void setIsRepeat(Integer isRepeat) {
        this.isRepeat = isRepeat;
    }

    @Override
    public String toString() {
        return "PromoActivity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", activityDateBegin=" + activityDateBegin +
                ", activityDateEnd=" + activityDateEnd +
                ", condition=" + condition +
                ", grantTypeId=" + grantTypeId +
                ", grantTypeName='" + grantTypeName + '\'' +
                ", grantType=" + grantType +
                ", groupId=" + groupId +
                ", userIdentify=" + userIdentify +
                ", userStatus='" + userStatus + '\'' +
                ", activityStatus=" + activityStatus +
                ", activityType=" + activityType +
                ", isRepeat=" + isRepeat +
                '}';
    }
}
