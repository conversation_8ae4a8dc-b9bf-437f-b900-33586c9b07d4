package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
@Entity
@Table(name="tyt_apply")
public class Apply implements Serializable {
	  /**
	  * 
	  */
	  private static final long serialVersionUID = 1L;
	  private Long id;
	  private String trueName;
	  private String cellPhone;
	  private String qq;
	  private Timestamp ctime;
      private String note;
	  private int status;
	  public static final int STATUS_DEFAULT = 0;
	  public static final int STATUS_HANDLED = 1;
	  
	  @Id
	  @GeneratedValue
	  @Column(name = "id", unique = true, nullable = false)
	  public Long getId() {
		  return id;
	  }
	  public void setId(Long id) {
		  this.id = id;
	  }
	  @Column(name="true_name")
	  public String getTrueName() {
		  return trueName;
	  }
	  public void setTrueName(String trueName) {
		  this.trueName = trueName;
	  }
	  @Column(name="cell_phone")
	  public String getCellPhone() {
		  return cellPhone;
	  }
	  public void setCellPhone(String cellPhone) {
		  this.cellPhone = cellPhone;
	  }
	
	  @Column(name="qq")
	  public String getQq() {
		  return qq;
	  }
	  public void setQq(String qq) {
		  this.qq = qq;
	  }
	  @Column(name="ctime")
	  public Timestamp getCtime() {
		  return ctime;
	  }
	  public void setCtime(Timestamp ctime) {
		  this.ctime = ctime;
	  }
		@Column(name="note")  
		public String getNote() {
			return note;
		}
		public void setNote(String note) {
			this.note = note;
		}
		@Column(name="status")  
		public int getStatus() {
			return status;
		}
		public void setStatus(int status) {
			this.status = status;
		}
		
	
	public Apply(String trueName, String cellPhone, String qq) {
		super();
		this.trueName = trueName;
		this.cellPhone = cellPhone;
		this.qq = qq;
		this.ctime = new Timestamp(System.currentTimeMillis());
		this.status = Apply.STATUS_DEFAULT;
	}
  
	public Apply() {
		
	}
  
  
  
}
