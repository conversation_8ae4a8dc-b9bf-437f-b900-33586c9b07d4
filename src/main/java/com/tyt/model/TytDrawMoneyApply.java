package com.tyt.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2016-11-26上午9:36:03
 * @description
 */
@Entity
@Table(name = "tyt_draw_money_apply", catalog = "tyt")
public class TytDrawMoneyApply {
	private Integer id;
	/*
	 * 用户提交提款申请的时间
	 */
	private String applyTime;
	/*
	 * 当前申请提现的用户id值
	 */
	private Integer userId;
	/*
	 * 提现的银行卡持卡人姓名
	 */
	private String bankCardOwner;
	/*
	 * 提现银行卡的卡号
	 */
	private String bankCardNumber;
	/*
	 * 银行卡所属的银行
	 */
	private String cardBlongedBank;
	/*
	 * 银行卡开户的支行信息
	 */
	private String cardDepositBank;
	/*
	 * 提现金额
	 */
	private String drawAccount;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "apply_time")
	public String getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(String applyTime) {
		this.applyTime = applyTime;
	}

	@Column(name = "user_id")
	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	@Column(name = "bank_card_owner")
	public String getBankCardOwner() {
		return bankCardOwner;
	}

	public void setBankCardOwner(String bankCardOwner) {
		this.bankCardOwner = bankCardOwner;
	}

	@Column(name = "bank_card_number")
	public String getBankCardNumber() {
		return bankCardNumber;
	}

	public void setBankCardNumber(String bankCardNumber) {
		this.bankCardNumber = bankCardNumber;
	}

	@Column(name = "card_blonged_bank")
	public String getCardBlongedBank() {
		return cardBlongedBank;
	}

	public void setCardBlongedBank(String cardBlongedBank) {
		this.cardBlongedBank = cardBlongedBank;
	}

	@Column(name = "card_deposit_bank")
	public String getCardDepositBank() {
		return cardDepositBank;
	}

	public void setCardDepositBank(String cardDepositBank) {
		this.cardDepositBank = cardDepositBank;
	}

	@Column(name = "draw_account")
	public String getDrawAccount() {
		return drawAccount;
	}

	public void setDrawAccount(String drawAccount) {
		this.drawAccount = drawAccount;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
