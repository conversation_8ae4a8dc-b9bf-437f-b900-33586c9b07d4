package com.tyt.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
/**
 * 
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "tyt_car_driver_phone_info", catalog = "tyt")
public class TytCarDriverPhoneInfo implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 505912617825718555L;

	private Long id;//id
	private Long carId;//车辆id
	private Date createTime;//创建日期
	private Date updateTime;//更新时间
	private String driverName;//司机姓名
	private String driverPhone;//司机电话
	private String secondDriverName;//副司机姓名
	private String secondDriverPhone;//副司机电话
	private String followerPhone;//随车电话
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "carId")
	public Long getCarId() {
		return carId;
	}
	public void setCarId(Long carId) {
		this.carId = carId;
	}
	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Column(name = "driver_name")
	public String getDriverName() {
		return driverName;
	}
	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}
	@Column(name = "driver_phone")
	public String getDriverPhone() {
		return driverPhone;
	}
	public void setDriverPhone(String driverPhone) {
		this.driverPhone = driverPhone;
	}
	@Column(name = "second_driver_name")
	public String getSecondDriverName() {
		return secondDriverName;
	}
	public void setSecondDriverName(String secondDriverName) {
		this.secondDriverName = secondDriverName;
	}
	@Column(name = "second_driver_phone")
	public String getSecondDriverPhone() {
		return secondDriverPhone;
	}
	public void setSecondDriverPhone(String secondDriverPhone) {
		this.secondDriverPhone = secondDriverPhone;
	}

	@Column(name = "follower_phone")
	public String getFollowerPhone() {
		return followerPhone;
	}
	public void setFollowerPhone(String followerPhone) {
		this.followerPhone = followerPhone;
	}
	
	@Override
	public String toString() {
		return "TytCarDriverPhoneInfo [id=" + id + ", carId=" + carId + ", createTime=" + createTime + ", updateTime="
				+ updateTime + ", driverName=" + driverName + ", driverPhone=" + driverPhone + ", secondDriverName="
				+ secondDriverName + ", secondDriverPhone=" + secondDriverPhone + ", followerPhone=" + followerPhone + "]";
	}
	
	
}
