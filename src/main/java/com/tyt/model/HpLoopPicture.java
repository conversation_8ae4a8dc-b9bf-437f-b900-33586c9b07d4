package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

@Entity
@Table(name = "hp_loop_picture", catalog = "tyt")
public class HpLoopPicture implements java.io.Serializable {
	private static final long serialVersionUID = -933290126986162435L;

	private Long id;
	private Short type;
	private String pictureUrl;
	private String title;
	private String openUrl;
	private Short baodingHide;
	private Short wrongBaodingHide;
	private Short displaySite;
	private Short openClose;
	private Short sort;
	private Short status;
	private Long userId;
	private Date utime;
	private Date ctime;

	public HpLoopPicture() {
	}

	public HpLoopPicture(Short type, String pictureUrl, String title, String openUrl, Short baodingHide, Short wrongBaodingHide, Short displaySite, Short openClose, Short sort, Short status, Long userId, Date utime, Date ctime) {
		this.type = type;
		this.pictureUrl = pictureUrl;
		this.title = title;
		this.openUrl = openUrl;
		this.baodingHide = baodingHide;
		this.wrongBaodingHide = wrongBaodingHide;
		this.displaySite = displaySite;
		this.openClose = openClose;
		this.sort = sort;
		this.status = status;
		this.userId = userId;
		this.utime = utime;
		this.ctime = ctime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "TYPE")
	public Short getType() {
		return this.type;
	}

	public void setType(Short type) {
		this.type = type;
	}

	@Column(name = "picture_url")
	public String getPictureUrl() {
		return this.pictureUrl;
	}

	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}

	@Column(name = "title")
	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Column(name = "open_url")
	public String getOpenUrl() {
		return this.openUrl;
	}

	public void setOpenUrl(String openUrl) {
		this.openUrl = openUrl;
	}

	@Column(name = "baoding_hide")
	public Short getBaodingHide() {
		return this.baodingHide;
	}

	public void setBaodingHide(Short baodingHide) {
		this.baodingHide = baodingHide;
	}

	@Column(name = "wrong_baoding_hide")
	public Short getWrongBaodingHide() {
		return this.wrongBaodingHide;
	}

	public void setWrongBaodingHide(Short wrongBaodingHide) {
		this.wrongBaodingHide = wrongBaodingHide;
	}

	@Column(name = "display_site")
	public Short getDisplaySite() {
		return this.displaySite;
	}

	public void setDisplaySite(Short displaySite) {
		this.displaySite = displaySite;
	}

	@Column(name = "open_close")
	public Short getOpenClose() {
		return this.openClose;
	}

	public void setOpenClose(Short openClose) {
		this.openClose = openClose;
	}

	@Column(name = "sort")
	public Short getSort() {
		return this.sort;
	}

	public void setSort(Short sort) {
		this.sort = sort;
	}

	@Column(name = "STATUS")
	public Short getStatus() {
		return this.status;
	}

	public void setStatus(Short status) {
		this.status = status;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}
