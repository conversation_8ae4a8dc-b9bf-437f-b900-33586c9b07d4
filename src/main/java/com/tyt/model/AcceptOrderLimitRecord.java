package com.tyt.model;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "accept_order_limit_record")
public class AcceptOrderLimitRecord {
    /**
     * 自增ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 手机号
     */
    @Column(name = "cell_phone")
    private String cellPhone;

    /**
     * 接单限制开始时间
     */
    @Column(name = "accept_order_limit_start_time")
    private Date acceptOrderLimitStartTime;

    /**
     * 接单限制结束时间
     */
    @Column(name = "accept_order_limit_end_time")
    private Date acceptOrderLimitEndTime;

    /**
     * 接单限制处罚天数(单位:天)
     */
    @Column(name = "accept_order_limit_num")
    private Integer acceptOrderLimitNum;

    /**
     * 接单限制处罚项(单位:秒)
     */
    @Column(name = "accept_order_limit_item")
    private Integer acceptOrderLimitItem;

    /**
     * 接单限制处罚状态: 1.有效 2.导入解除 3.处罚结束
     */
    @Column(name = "accept_order_limit_status")
    private Integer acceptOrderLimitStatus;

    /**
     * 操作人ID
     */
    @Column(name = "opera_user_id")
    private Long operaUserId;

    /**
     * 操作人姓名
     */
    @Column(name = "opera_user_name")
    private String operaUserName;

    /**
     * 操作时间
     */
    @Column(name = "opera_time")
    private Date operaTime;

    /**
     * 解除人ID
     */
    @Column(name = "lift_user_id")
    private Long liftUserId;

    /**
     * 解除人姓名
     */
    @Column(name = "lift_user_name")
    private String liftUserName;

    /**
     * 解除时间
     */
    @Column(name = "lift_time")
    private Date liftTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}