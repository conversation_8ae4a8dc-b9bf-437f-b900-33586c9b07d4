package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="tyt_paymethod")
public class PayMethod  implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5903876605208403140L;
	Long id;
	String name;
	String rf;
	String imageWeb;
	String imagePhone;
	Integer statusWeb;
	Integer statusPhone;
	Timestamp ctime;
	Timestamp mtime;
	String type;
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="name")
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	@Column(name="rf")
	public String getRf() {
		return rf;
	}
	public void setRf(String rf) {
		this.rf = rf;
	}
	@Column(name="image_web")
	public String getImageWeb() {
		return imageWeb;
	}
	public void setImageWeb(String imageWeb) {
		this.imageWeb = imageWeb;
	}
	@Column(name="image_phone")
	public String getImagePhone() {
		return imagePhone;
	}
	public void setImagePhone(String imagePhone) {
		this.imagePhone = imagePhone;
	}
	
	@Column(name="status_web")
	public Integer getStatusWeb() {
		return statusWeb;
	}
	public void setStatusWeb(Integer statusWeb) {
		this.statusWeb = statusWeb;
	}
	@Column(name="status_phone")
	public Integer getStatusPhone() {
		return statusPhone;
	}
	public void setStatusPhone(Integer statusPhone) {
		this.statusPhone = statusPhone;
	}
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	@Column(name="mtime")
	public Timestamp getMtime() {
		return mtime;
	}
	public void setMtime(Timestamp mtime) {
		this.mtime = mtime;
	}
	@Column(name="type")
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	
}
