package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_start_advert")
public class TytStartAdvert implements Serializable {
    private static final long serialVersionUID = 3497374692773105494L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 展示位置(见tyt_source表中start_ad_position)
     */
    private Integer showPosition;

    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * 缩略图地址
     */
    private String smallPicUrl;

    /**
     * local:原生 web:h5
     */
    private String linkType;

    /**
     * 图片链接地址
     */
    private String picLinkUrl;

    /**
     * 1.有效 2.无效
     */
    private Short status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 时间间隔（分钟）
     */
    private Integer timeInterval;

    /**
     * 显示次数
     */
    private Integer times;

    /**
     * 创建人id
     */
    private Long createOpId;

    /**
     * 创建人姓名
     */
    private String createOpName;

    /**
     * 修改人id
     */
    private Long updateOpId;

    /**
     * 修改人姓名
     */
    private String updateOpName;

    /**
     * 是否需要登录 1.需要 2.不需要
     */
    private Short isNeedLogin;

    /**
     * 
     */
    private Date ctime;

    /**
     * 
     */
    private Date mtime;

    /**
     * 单位 （second:秒;minute:分;day:天）
     */
    private String timeUnit;


    /**
     * 主键id
     * @return id 主键id
     */
    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    /**
     * 主键id
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 标题
     * @return title 标题
     */
    @Column(name = "title")
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     * @param title 标题
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * 展示位置(见tyt_source表中start_ad_position)
     * @return show_position 展示位置(见tyt_source表中start_ad_position)
     */
    @Column(name = "show_position")
    public Integer getShowPosition() {
        return showPosition;
    }

    /**
     * 展示位置(见tyt_source表中start_ad_position)
     * @param showPosition 展示位置(见tyt_source表中start_ad_position)
     */
    public void setShowPosition(Integer showPosition) {
        this.showPosition = showPosition;
    }

    /**
     * 图片地址
     * @return pic_url 图片地址
     */
    @Column(name = "pic_url")
    public String getPicUrl() {
        return picUrl;
    }

    /**
     * 图片地址
     * @param picUrl 图片地址
     */
    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    /**
     * 缩略图地址
     * @return small_pic_url 缩略图地址
     */
    @Column(name = "small_pic_url")
    public String getSmallPicUrl() {
        return smallPicUrl;
    }

    /**
     * 缩略图地址
     * @param smallPicUrl 缩略图地址
     */
    public void setSmallPicUrl(String smallPicUrl) {
        this.smallPicUrl = smallPicUrl;
    }

    /**
     * local:原生 web:h5
     * @return link_type local:原生 web:h5
     */
    @Column(name = "link_type")
    public String getLinkType() {
        return linkType;
    }

    /**
     * local:原生 web:h5
     * @param linkType local:原生 web:h5
     */
    public void setLinkType(String linkType) {
        this.linkType = linkType;
    }

    /**
     * 图片链接地址
     * @return pic_link_url 图片链接地址
     */
    @Column(name = "pic_link_url")
    public String getPicLinkUrl() {
        return picLinkUrl;
    }

    /**
     * 图片链接地址
     * @param picLinkUrl 图片链接地址
     */
    public void setPicLinkUrl(String picLinkUrl) {
        this.picLinkUrl = picLinkUrl;
    }

    /**
     * 1.有效 2.无效
     * @return status 1.有效 2.无效
     */
    @Column(name = "status")
    public Short getStatus() {
        return status;
    }

    /**
     * 1.有效 2.无效
     * @param status 1.有效 2.无效
     */
    public void setStatus(Short status) {
        this.status = status;
    }

    /**
     * 开始时间
     * @return start_time 开始时间
     */
    @Column(name = "start_time")
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 开始时间
     * @param startTime 开始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 结束时间
     * @return end_time 结束时间
     */
    @Column(name = "end_time")
    public Date getEndTime() {
        return endTime;
    }

    /**
     * 结束时间
     * @param endTime 结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 时间间隔（分钟）
     * @return time_interval 时间间隔（分钟）
     */
    @Column(name = "time_interval")
    public Integer getTimeInterval() {
        return timeInterval;
    }

    /**
     * 时间间隔（分钟）
     * @param timeInterval 时间间隔（分钟）
     */
    public void setTimeInterval(Integer timeInterval) {
        this.timeInterval = timeInterval;
    }

    /**
     * 显示次数
     * @return times 显示次数
     */
    @Column(name = "times")
    public Integer getTimes() {
        return times;
    }

    /**
     * 显示次数
     * @param times 显示次数
     */
    public void setTimes(Integer times) {
        this.times = times;
    }

    /**
     * 创建人id
     * @return create_op_id 创建人id
     */
    @Column(name = "create_op_id")
    public Long getCreateOpId() {
        return createOpId;
    }

    /**
     * 创建人id
     * @param createOpId 创建人id
     */
    public void setCreateOpId(Long createOpId) {
        this.createOpId = createOpId;
    }

    /**
     * 创建人姓名
     * @return create_op_name 创建人姓名
     */
    @Column(name = "create_op_name")
    public String getCreateOpName() {
        return createOpName;
    }

    /**
     * 创建人姓名
     * @param createOpName 创建人姓名
     */
    public void setCreateOpName(String createOpName) {
        this.createOpName = createOpName == null ? null : createOpName.trim();
    }

    /**
     * 修改人id
     * @return update_op_id 修改人id
     */
    @Column(name = "update_op_id")
    public Long getUpdateOpId() {
        return updateOpId;
    }

    /**
     * 修改人id
     * @param updateOpId 修改人id
     */
    public void setUpdateOpId(Long updateOpId) {
        this.updateOpId = updateOpId;
    }

    /**
     * 修改人姓名
     * @return update_op_name 修改人姓名
     */
    @Column(name = "update_op_name")
    public String getUpdateOpName() {
        return updateOpName;
    }

    /**
     * 修改人姓名
     * @param updateOpName 修改人姓名
     */
    public void setUpdateOpName(String updateOpName) {
        this.updateOpName = updateOpName == null ? null : updateOpName.trim();
    }

    @Column(name = "is_need_login")
    public Short getIsNeedLogin() {
        return isNeedLogin;
    }

    public void setIsNeedLogin(Short isNeedLogin) {
        this.isNeedLogin = isNeedLogin;
    }

    /**
     * 
     * @return ctime 
     */
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    /**
     * 
     * @param ctime 
     */
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    /**
     * 
     * @return mtime 
     */
    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    /**
     * 
     * @param mtime 
     */
    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Column(name = "time_unit")
    public String getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
    }


}