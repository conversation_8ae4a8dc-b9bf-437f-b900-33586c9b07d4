package com.tyt.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_convention_give_goods_record")
public class ConventionGiveGoodsRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     *  用户id
     */
    private Long userId;
    /**
     *  活动id
     */
    private Long activityId;
    /**
     *  商品id
     */
    private Long goodsId;
    /**
     *  订单id
     */
    private Long orderId;
    /**
     *  赠送时间
     */
    private Date ctime;

    @Id
    @GeneratedValue
    @Column(name="id",nullable=false,unique=true)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name="user_id")
    public Long getUserId() {
        return userId;
    }


    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name="activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Column(name="goods_id")
    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    @Column(name="order_id")
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @Column(name="ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "ConventionGiveGoodsRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", activityId=" + activityId +
                ", goodsId=" + goodsId +
                ", orderId=" + orderId +
                ", ctime=" + ctime +
                '}';
    }
}
