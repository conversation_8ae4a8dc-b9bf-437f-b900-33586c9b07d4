package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_backout_reason")
public class BackoutReason implements Serializable {


    private Long id;

    /**
     * 出发地
     */
    private String startPoint;

    /**
     * 目的地
     */
    private String destPoint;

    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 货源撤销原因key 对应字典 name值
     */
    private String backoutReasonKey;

    /**
     * 货源撤销原因 对应字典 value值
     */
    private Integer backoutReason;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 货源撤销状态，0-无效 1-有效
     */
    private Integer status;
    /*
        代调6期新增
     */
    private String specificReason;
    private String remark;


    /**
     * 后台专用撤销原因（中文）
     */
    private String backoutReasonKeyNew;

    /**
     * 后台专用撤销原因（code）
     */
    private Integer backoutReasonNew;



    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    @Column(name = "specific_reason")
    public String getSpecificReason() {
        return specificReason;
    }

    public void setSpecificReason(String specificReason) {
        this.specificReason = specificReason;
    }

    public BackoutReason() {
    }
    @Column(name = "start_point")
    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    @Column(name = "dest_point")
    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    @Column(name = "task_content")
    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    @Column(name = "src_msg_id")
    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "backout_reason_key")
    public String getBackoutReasonKey() {
        return backoutReasonKey;
    }

    public void setBackoutReasonKey(String backoutReasonKey) {
        this.backoutReasonKey = backoutReasonKey;
    }

    @Column(name = "backout_reason")
    public Integer getBackoutReason() {
        return backoutReason;
    }

    public void setBackoutReason(Integer backoutReason) {
        this.backoutReason = backoutReason;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "backout_reason_key_new")
    public String getBackoutReasonKeyNew() {
        return backoutReasonKeyNew;
    }

    public void setBackoutReasonKeyNew(String backoutReasonKeyNew) {
        this.backoutReasonKeyNew = backoutReasonKeyNew;
    }

    @Column(name = "backout_reason_new")
    public Integer getBackoutReasonNew() {
        return backoutReasonNew;
    }

    public void setBackoutReasonNew(Integer backoutReasonNew) {
        this.backoutReasonNew = backoutReasonNew;
    }

    @Override
    public String toString() {
        return "BackoutReason{" +
                "id=" + id +
                ", startPoint='" + startPoint + '\'' +
                ", destPoint='" + destPoint + '\'' +
                ", taskContent='" + taskContent + '\'' +
                ", srcMsgId=" + srcMsgId +
                ", userId=" + userId +
                ", backoutReasonKey='" + backoutReasonKey + '\'' +
                ", backoutReason=" + backoutReason +
                ", ctime=" + ctime +
                ", status=" + status +
                ", specificReason='" + specificReason + '\'' +
                ", remark='" + remark + '\'' +
                ", backoutReasonKeyNew='" + backoutReasonKeyNew + '\'' +
                ", backoutReasonNew='" + backoutReasonNew + '\'' +
                '}';
    }
}