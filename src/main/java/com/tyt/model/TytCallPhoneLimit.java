package com.tyt.model;

import static javax.persistence.GenerationType.IDENTITY;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-3-9下午2:43:51
 * @description
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "tyt_call_phone_limit", catalog = "tyt")
public class TytCallPhoneLimit implements java.io.Serializable {

	private Long id;
	private Short userClass;
	private Short identityType;
	private Short identitySor;
	private Short identityNumber;
	private Short memberSort;
	private Short memberNumber;
	private Short carSort;
	private Short carNumber;
	private Date utime;

	public TytCallPhoneLimit() {
	}

	public TytCallPhoneLimit(Short userClass, Short identityType, Short identitySor, Short identityNumber, Short memberSort, Short memberNumber, Short carSort, Short carNumber, Date utime) {
		this.userClass = userClass;
		this.identityType = identityType;
		this.identitySor = identitySor;
		this.identityNumber = identityNumber;
		this.memberSort = memberSort;
		this.memberNumber = memberNumber;
		this.carSort = carSort;
		this.carNumber = carNumber;
		this.utime = utime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_class")
	public Short getUserClass() {
		return this.userClass;
	}

	public void setUserClass(Short userClass) {
		this.userClass = userClass;
	}

	@Column(name = "identity_type")
	public Short getIdentityType() {
		return this.identityType;
	}

	public void setIdentityType(Short identityType) {
		this.identityType = identityType;
	}

	@Column(name = "identity_sor")
	public Short getIdentitySor() {
		return this.identitySor;
	}

	public void setIdentitySor(Short identitySor) {
		this.identitySor = identitySor;
	}

	@Column(name = "identity_number")
	public Short getIdentityNumber() {
		return this.identityNumber;
	}

	public void setIdentityNumber(Short identityNumber) {
		this.identityNumber = identityNumber;
	}

	@Column(name = "member_sort")
	public Short getMemberSort() {
		return this.memberSort;
	}

	public void setMemberSort(Short memberSort) {
		this.memberSort = memberSort;
	}

	@Column(name = "member_number")
	public Short getMemberNumber() {
		return this.memberNumber;
	}

	public void setMemberNumber(Short memberNumber) {
		this.memberNumber = memberNumber;
	}

	@Column(name = "car_sort")
	public Short getCarSort() {
		return this.carSort;
	}

	public void setCarSort(Short carSort) {
		this.carSort = carSort;
	}

	@Column(name = "car_number")
	public Short getCarNumber() {
		return this.carNumber;
	}

	public void setCarNumber(Short carNumber) {
		this.carNumber = carNumber;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime", length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
