package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytUserPush entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_user_account")
public class TytUserAccount implements java.io.Serializable {

	private static final long serialVersionUID = 451524157163870518L;

	private Long id;
	private Long userId;       
	private Long currentBalance;
	private Integer affiliatedType;  
	private Date createTime;      
	private Date updateTime;      

	

	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}


	@Column(name = "current_balance")
	public Long getCurrentBalance() {
		return currentBalance;
	}

	public void setCurrentBalance(Long currentBalance) {
		this.currentBalance = currentBalance;
	}
	@Column(name = "affiliated_type")
	public Integer getAffiliatedType() {
		return affiliatedType;
	}

	public void setAffiliatedType(Integer affiliatedType) {
		this.affiliatedType = affiliatedType;
	}
	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}


}