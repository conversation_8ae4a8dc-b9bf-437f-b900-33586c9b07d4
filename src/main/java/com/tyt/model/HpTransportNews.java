package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.alibaba.fastjson.JSON;

@Entity
@Table(name = "hp_transport_news", catalog = "tyt")
public class HpTransportNews implements java.io.Serializable {
	private static final long serialVersionUID = 354243283270446144L;

	private Long id;
	private String source;
	private String newsTitle;
	private String pictureUrl;
	private String openUrl;
	private Date publishTime;
	private Short status;
	private Long userId;
	private Date utime;
	private Date ctime;
	private Short openClose;

	public HpTransportNews() {
	}

	public HpTransportNews(String source, String newsTitle, String pictureUrl, String openUrl, Date publishTime, Short status, Long userId, Date utime, Date ctime) {
		this.source = source;
		this.newsTitle = newsTitle;
		this.pictureUrl = pictureUrl;
		this.openUrl = openUrl;
		this.publishTime = publishTime;
		this.status = status;
		this.userId = userId;
		this.utime = utime;
		this.ctime = ctime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "source")
	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Column(name = "news_title")
	public String getNewsTitle() {
		return this.newsTitle;
	}

	public void setNewsTitle(String newsTitle) {
		this.newsTitle = newsTitle;
	}

	@Column(name = "picture_url")
	public String getPictureUrl() {
		return this.pictureUrl;
	}

	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}

	@Column(name = "open_url")
	public String getOpenUrl() {
		return this.openUrl;
	}

	public void setOpenUrl(String openUrl) {
		this.openUrl = openUrl;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "publish_time")
	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	@Column(name = "status")
	public Short getStatus() {
		return this.status;
	}

	public void setStatus(Short status) {
		this.status = status;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
	@Column(name = "open_close")
	public Short getOpenClose() {
		return openClose;
	}

	public void setOpenClose(Short openClose) {
		this.openClose = openClose;
	}

}
