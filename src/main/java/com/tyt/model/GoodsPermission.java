package com.tyt.model;

import javax.persistence.*;

@Entity
@Table(name = "tyt_goods_permission")
public class GoodsPermission {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 服务权益分类ID(对应permission_sort表的ID)
     */
    private Long permissionSortId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 主键
     * @return id 主键
     */
    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 商品ID
     * @return goods_id 商品ID
     */
    @Column(name = "goods_id")
    public Long getGoodsId() {
        return goodsId;
    }

    /**
     * 商品ID
     * @param goodsId 商品ID
     */
    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * 服务权益分类ID(对应permission_sort表的ID)
     * @return permission_sort_id 服务权益分类ID(对应permission_sort表的ID)
     */
    @Column(name = "permission_sort_id")
    public Long getPermissionSortId() {
        return permissionSortId;
    }

    /**
     * 服务权益分类ID(对应permission_sort表的ID)
     * @param permissionSortId 服务权益分类ID(对应permission_sort表的ID)
     */
    public void setPermissionSortId(Long permissionSortId) {
        this.permissionSortId = permissionSortId;
    }

    /**
     * 数量
     * @return quantity 数量
     */
    @Column(name = "quantity")
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 数量
     * @param quantity 数量
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", permissionSortId=").append(permissionSortId);
        sb.append(", quantity=").append(quantity);
        sb.append("]");
        return sb.toString();
    }
}
