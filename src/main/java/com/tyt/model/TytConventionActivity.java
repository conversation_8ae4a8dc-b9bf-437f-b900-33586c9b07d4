package com.tyt.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TytConventionActivity
 * @description 履约活动表
 * @date 2022-07-15 11:27
 */
@Entity
@Table(name = "tyt_convention_activity", schema = "tyt", catalog = "")
public class TytConventionActivity {
    private Long id;
    private Long activityId;
    private Long userId;
    private String userCellPhone;
    private Long orderNum;
    private Integer prize;
    private Date createTime;
    private Date updateTime;
    private Integer isDeleted;
    private Date lastFinishTime;
    private Integer receiveFlag;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "activity_id", nullable = false)
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Basic
    @Column(name = "user_id", nullable = false)
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "user_cell_phone", nullable = false, length = 20)
    public String getUserCellPhone() {
        return userCellPhone;
    }

    public void setUserCellPhone(String userCellPhone) {
        this.userCellPhone = userCellPhone;
    }

    @Basic
    @Column(name = "order_num", nullable = true)
    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    @Basic
    @Column(name = "prize", nullable = true)
    public Integer getPrize() {
        return prize;
    }

    public void setPrize(Integer prize) {
        this.prize = prize;
    }

    @Basic
    @Column(name = "create_time", nullable = false)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "update_time", nullable = true)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Basic
    @Column(name = "is_deleted", nullable = false)
    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Basic
    @Column(name = "last_finish_time", nullable = true)
    public Date getLastFinishTime() {
        return lastFinishTime;
    }

    public void setLastFinishTime(Date lastFinishTime) {
        this.lastFinishTime = lastFinishTime;
    }

    @Basic
    @Column(name = "receive_flag", nullable = true)
    public Integer getReceiveFlag() {
        return receiveFlag;
    }

    public void setReceiveFlag(Integer receiveFlag) {
        this.receiveFlag = receiveFlag;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TytConventionActivity that = (TytConventionActivity) o;
        return Objects.equals(id, that.id) && Objects.equals(activityId, that.activityId) && Objects.equals(userId, that.userId) && Objects.equals(userCellPhone, that.userCellPhone) && Objects.equals(orderNum, that.orderNum) && Objects.equals(prize, that.prize) && Objects.equals(createTime, that.createTime) && Objects.equals(updateTime, that.updateTime) && Objects.equals(isDeleted, that.isDeleted) && Objects.equals(lastFinishTime, that.lastFinishTime) && Objects.equals(receiveFlag, that.receiveFlag);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, activityId, userId, userCellPhone, orderNum, prize, createTime, updateTime, isDeleted, lastFinishTime, receiveFlag);
    }
}
