package com.tyt.model;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * @ClassName CarInsuranceDealRecord
 * @Description 车险询价处理记录表对象
 * <AUTHOR>
 * @Date 2019-03-13 14:50
 * @Version 1.0
 */
@Entity
@Table(name = "car_insurance_deal_record", schema = "tyt", catalog = "")
public class CarInsuranceDealRecord {

    private Integer id;
    private Long inquiryId;
    private Timestamp dealTime;
    private Long dealUserId;
    private String dealUserName;
    private String dealContent;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "inquiry_id")
    public Long getInquiryId() {
        return inquiryId;
    }

    public void setInquiryId(Long inquiryId) {
        this.inquiryId = inquiryId;
    }

    @Basic
    @Column(name = "deal_time")
    public Timestamp getDealTime() {
        return dealTime;
    }

    public void setDealTime(Timestamp dealTime) {
        this.dealTime = dealTime;
    }

    @Basic
    @Column(name = "deal_user_id")
    public Long getDealUserId() {
        return dealUserId;
    }

    public void setDealUserId(Long dealUserId) {
        this.dealUserId = dealUserId;
    }

    @Basic
    @Column(name = "deal_user_name")
    public String getDealUserName() {
        return dealUserName;
    }

    public void setDealUserName(String dealUserName) {
        this.dealUserName = dealUserName;
    }


    @Basic
    @Column(name = "deal_content")
    public String getDealContent() {
        return dealContent;
    }

    public void setDealContent(String dealContent) {
        this.dealContent = dealContent;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CarInsuranceDealRecord that = (CarInsuranceDealRecord) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(inquiryId, that.inquiryId) &&
                Objects.equals(dealTime, that.dealTime) &&
                Objects.equals(dealUserId, that.dealUserId) &&
                Objects.equals(dealUserName, that.dealUserName) &&
                Objects.equals(dealContent, that.dealContent);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, inquiryId, dealTime, dealUserId, dealUserName, dealContent);
    }
}
