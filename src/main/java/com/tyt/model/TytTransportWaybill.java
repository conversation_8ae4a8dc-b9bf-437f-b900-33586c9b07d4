package com.tyt.model;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TytTransportWaybill entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_transport_waybill")
public class TytTransportWaybill implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -268146136255092198L;
	private String tsOrderNo;
	private String startPoint;
	private String destPoint;
	private String taskContent;
	private String tel;
	private String pubTime;
	private Date ctime;
	private String uploadCellphone;
	private String tel3;
	private String tel4;
	private Long userId;
	private Long sortId;
	private String isInfoFee;
	private String infoStatus;
	private Long payUserId;
	private String payCellPhone;
	private String payLinkPhone;
	private Long payAmount;
	private Date payTime;
	private Integer payNumber;
	private Date agreeTime;
	private Date loadTime;
	private Date mtime;
	private Long tsId;
	private Date createTime;
	private String linkman;
	//发布人昵称
	private String pubUserName;
	//车主昵称
	private String payUserName;

	private Integer timeLimitIdentification;

	/**
	 * 保障货源（1是；0否；）
	 */
	private Integer guaranteeGoods;

	/**
	 * 标准货名备注
	 */
	private String machineRemark;


	@Id
	@GeneratedValue(generator = "paymentableGenerator")
    @GenericGenerator(name = "paymentableGenerator", strategy = "assigned")
	@Column(name = "ts_order_no", unique = true, nullable = false)
	public String getTsOrderNo() {
		return this.tsOrderNo;
	}

	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}

	@Column(name = "start_point")
	public String getStartPoint() {
		return this.startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	@Column(name = "dest_point")
	public String getDestPoint() {
		return this.destPoint;
	}

	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}

	@Column(name = "task_content")
	public String getTaskContent() {
		return this.taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	@Column(name = "tel", length = 100)
	public String getTel() {
		return this.tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	@Column(name = "pub_time")
	public String getPubTime() {
		return this.pubTime;
	}

	public void setPubTime(String pubTime) {
		this.pubTime = pubTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "upload_cellphone", nullable = false)
	public String getUploadCellphone() {
		return this.uploadCellphone;
	}

	public void setUploadCellphone(String uploadCellphone) {
		this.uploadCellphone = uploadCellphone;
	}

	@Column(name = "tel3")
	public String getTel3() {
		return this.tel3;
	}

	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}

	@Column(name = "tel4")
	public String getTel4() {
		return this.tel4;
	}

	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}

	@Column(name = "user_id", nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "sort_id", nullable = false)
	public Long getSortId() {
		return this.sortId;
	}

	public void setSortId(Long sortId) {
		this.sortId = sortId;
	}

	@Column(name = "is_info_fee")
	public String getIsInfoFee() {
		return this.isInfoFee;
	}

	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}

	@Column(name = "info_status", nullable = false, length = 4)
	public String getInfoStatus() {
		return this.infoStatus;
	}

	public void setInfoStatus(String infoStatus) {
		this.infoStatus = infoStatus;
	}

	@Column(name = "pay_user_id")
	public Long getPayUserId() {
		return this.payUserId;
	}

	public void setPayUserId(Long payUserId) {
		this.payUserId = payUserId;
	}

	@Column(name = "pay_cell_phone")
	public String getPayCellPhone() {
		return this.payCellPhone;
	}

	public void setPayCellPhone(String payCellPhone) {
		this.payCellPhone = payCellPhone;
	}

	@Column(name = "pay_link_phone")
	public String getPayLinkPhone() {
		return this.payLinkPhone;
	}

	public void setPayLinkPhone(String payLinkPhone) {
		this.payLinkPhone = payLinkPhone;
	}

	@Column(name = "pay_time")
	public Date getPayTime() {
		return this.payTime;
	}

	@Column(name = "pay_amount")
	public Long getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(Long payAmount) {
		this.payAmount = payAmount;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	@Column(name = "pay_number")
	public Integer getPayNumber() {
		return this.payNumber;
	}

	public void setPayNumber(Integer payNumber) {
		this.payNumber = payNumber;
	}

	@Column(name = "agree_time")
	public Date getAgreeTime() {
		return this.agreeTime;
	}

	public void setAgreeTime(Date agreeTime) {
		this.agreeTime = agreeTime;
	}

	@Column(name = "load_time")
	public Date getLoadTime() {
		return this.loadTime;
	}

	public void setLoadTime(Date loadTime) {
		this.loadTime = loadTime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name = "ts_id")
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "create_time", nullable = false)
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "linkman")
	public String getLinkman() {
		return this.linkman;
	}

	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}

	@Column(name = "pub_user_name")
	public String getPubUserName() {
		return pubUserName;
	}

	public void setPubUserName(String pubUserName) {
		this.pubUserName = pubUserName;
	}

	@Column(name = "pay_user_name")
	public String getPayUserName() {
		return payUserName;
	}

	public void setPayUserName(String payUserName) {
		this.payUserName = payUserName;
	}


	@Column(name = "time_limit_identification")
	public Integer getTimeLimitIdentification() {
		return timeLimitIdentification;
	}

	public void setTimeLimitIdentification(Integer timeLimitIdentification) {
		this.timeLimitIdentification = timeLimitIdentification;
	}

	@Column(name = "guarantee_goods")
	public Integer getGuaranteeGoods() {
		return guaranteeGoods;
	}

	public void setGuaranteeGoods(Integer guaranteeGoods) {
		this.guaranteeGoods = guaranteeGoods;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	@Column(name = "machine_remark")
	public String getMachineRemark() {
		return machineRemark;
	}

	public void setMachineRemark(String machineRemark) {
		this.machineRemark = machineRemark;
	}

}