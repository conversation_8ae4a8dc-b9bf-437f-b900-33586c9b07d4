package com.tyt.model;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName TytUsedCarBrand
 * @Description 二手车车辆品牌
 * <AUTHOR>
 * @Date 2020-01-21 11:44
 * @Version 1.0
 */
@Entity
@Table(name = "tyt_used_car_brand", schema = "tyt", catalog = "")
public class TytUsedCarBrand {
    private Integer id;
    private Integer carBrandType;
    private String carBrandName;
    private String carBrandSpell;
    private Integer carBrandStatus;
    private Date ctime;
    private Date utime;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "car_brand_type")
    public Integer getCarBrandType() {
        return carBrandType;
    }

    public void setCarBrandType(Integer carBrandType) {
        this.carBrandType = carBrandType;
    }

    @Basic
    @Column(name = "car_brand_name")
    public String getCarBrandName() {
        return carBrandName;
    }

    public void setCarBrandName(String carBrandName) {
        this.carBrandName = carBrandName;
    }

    @Basic
    @Column(name = "car_brand_spell")
    public String getCarBrandSpell() {
        return carBrandSpell;
    }

    public void setCarBrandSpell(String carBrandSpell) {
        this.carBrandSpell = carBrandSpell;
    }

    @Basic
    @Column(name = "car_brand_status")
    public Integer getCarBrandStatus() {
        return carBrandStatus;
    }

    public void setCarBrandStatus(Integer carBrandStatus) {
        this.carBrandStatus = carBrandStatus;
    }

    @Basic
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "utime")
    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TytUsedCarBrand that = (TytUsedCarBrand) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(carBrandType, that.carBrandType) &&
                Objects.equals(carBrandName, that.carBrandName) &&
                Objects.equals(carBrandSpell, that.carBrandSpell) &&
                Objects.equals(carBrandStatus, that.carBrandStatus) &&
                Objects.equals(ctime, that.ctime) &&
                Objects.equals(utime, that.utime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, carBrandType, carBrandName, carBrandSpell, carBrandStatus, ctime, utime);
    }
}
