package com.tyt.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytUserIdentityInfo entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_user_identity_info", catalog = "tyt")
public class TytUserIdentityInfo implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5169206676208692327L;
	// Fields

	private Long id;
	private Long userId;
	private String trueName;
	private String idCard;
	private Date ctime;
	private Date utime;
	private String result;
	private Integer status;

	
	@Id
	@GeneratedValue
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "true_name", length = 30)
	public String getTrueName() {
		return this.trueName;
	}

	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}

	@Column(name = "id_card", length = 18)
	public String getIdCard() {
		return this.idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "result", length = 100)
	public String getResult() {
		return this.result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

}