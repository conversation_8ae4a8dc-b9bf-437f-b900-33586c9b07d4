package com.tyt.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.plat.utils.BigDecimalSerialize;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * User: Administrator Date: 19-10-22 Time: 下午5:47
 */
@Entity
@Table(name = "tyt_transport_done")
public class TransportDone implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * TransportMain表货源ID
     */
    private Long tsId;
    /**
     * 发货人ID
     */
    private Long userId;
    /**
     * 发货人昵称
     */
    private String nickName;
    /**
     * 发货人手机号
     */
    private String cellPhone;
    /**
     * 出发地
     */
    private String startPoint;
    /**
     * 目的地
     */
    private String destPoint;
    /**
     * 货物内容
     */
    private String taskContent;
    /**
     * 运单编号
     */
    private String tsOrderNo;
    /**
     * 发布时间 transprt_main的ctime
     */
    private Date publishTime;
    /**
     * 车辆ID
     */
    private Long carId;
    /**
     * 车头牌照头字母
     */
    private String headCity;
    /**
     * 车头牌照号码
     */
    private String headNo;
    /**
     * 挂车牌照头字母
     */
    private String tailCity;
    /**
     * 挂车牌照号码
     */
    private String tailNo;
    /**
     * 承运人userid
     */
    private Long carryUserId;
    /**
     * 承运人手机号码
     */
    private String carryCellPhone;
    /**
     * 承运人姓名
     */
    private String carryName;
    /**
     * 是否更改过所查车辆，0-未更改，1-已更改
     */
    private Integer isChangeCar;
    /**
     * 是否允许定位车辆，0-允许，1-拒绝
     */
    private Integer isAllowLocation;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 修改时间
     */
    private Date mtime;
    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;
    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 运价
     */
    private String price;
    /**
     * 成交价
     */
    private BigDecimal dealPrice;
    /**
     * 调车数量
     */
    private Integer shuntingQuantity;

    /**
     * 货源类型（电议1，一口价2）
     * @return
     */
    private Integer publishType;
    /**
     * 信息费
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal infoFee;


    private Integer excellentGoods;

    /**
     * 是否优车2.0货源：1-否，2-是
     */
    private Integer excellentGoodsTwo;

    /**
     * 发货方式：0-普通找车，10-用户出价，20-特惠优车，21-快速优车，22-极速优车，30-专车
     */
    private Integer publishGoodsType;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    private Integer driverDriving;

    /**
     * 装货联系电话
     */
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    private String unloadCellPhone;

    /**
     * 签约合作商ID
     */
    private Long cargoOwnerId;

    /**
     * 标准货名备注
     */
    private String machineRemark;
    /**
     * 优车发货卡id
     */
    private Long excellentCardId;

    @Transient
    public Integer getShuntingQuantity() {
        return shuntingQuantity;
    }

    public void setShuntingQuantity(Integer shuntingQuantity) {
        this.shuntingQuantity = shuntingQuantity;
    }

    @Transient
    public Integer getPublishType() {
        return publishType;
    }

    public void setPublishType(Integer publishType) {
        this.publishType = publishType;
    }

    @Transient
    public BigDecimal getInfoFee() {
        return infoFee;
    }

    public void setInfoFee(BigDecimal infoFee) {
        this.infoFee = infoFee;
    }

    @Transient
    public Date getLoadingTime() {
        return loadingTime;
    }

    public void setLoadingTime(Date loadingTime) {
        this.loadingTime = loadingTime;
    }
    @Transient
    public Date getUnloadTime() {
        return unloadTime;
    }

    public void setUnloadTime(Date unloadTime) {
        this.unloadTime = unloadTime;
    }
    @Transient
    public Date getBeginLoadingTime() {
        return beginLoadingTime;
    }

    public void setBeginLoadingTime(Date beginLoadingTime) {
        this.beginLoadingTime = beginLoadingTime;
    }
    @Transient
    public Date getBeginUnloadTime() {
        return beginUnloadTime;
    }

    public void setBeginUnloadTime(Date beginUnloadTime) {
        this.beginUnloadTime = beginUnloadTime;
    }

    @Transient
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    @Transient
    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    @Column(name = "ts_id")
    public Long getTsId() {
        return tsId;
    }

    public void setTsId(Long tsId) {
        this.tsId = tsId;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @Column(name = "nick_name")
    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
    @Column(name = "cell_phone")
    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    @Column(name = "start_point")
    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    @Column(name = "dest_point")
    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    @Column(name = "task_content")
    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    @Column(name = "ts_order_no")
    public String getTsOrderNo() {
        return tsOrderNo;
    }

    public void setTsOrderNo(String tsOrderNo) {
        this.tsOrderNo = tsOrderNo;
    }

    @Column(name = "publish_time")
    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    @Column(name = "car_id")
    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    @Column(name = "head_city")
    public String getHeadCity() {
        return headCity;
    }

    public void setHeadCity(String headCity) {
        this.headCity = headCity;
    }

    @Column(name = "head_no")
    public String getHeadNo() {
        return headNo;
    }

    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }

    @Column(name = "tail_city")
    public String getTailCity() {
        return tailCity;
    }

    public void setTailCity(String tailCity) {
        this.tailCity = tailCity;
    }

    @Column(name = "tail_no")
    public String getTailNo() {
        return tailNo;
    }

    public void setTailNo(String tailNo) {
        this.tailNo = tailNo;
    }

    @Column(name = "carry_user_id")
    public Long getCarryUserId() {
        return carryUserId;
    }

    public void setCarryUserId(Long carryUserId) {
        this.carryUserId = carryUserId;
    }

    @Column(name = "carry_cell_phone")
    public String getCarryCellPhone() {
        return carryCellPhone;
    }

    public void setCarryCellPhone(String carryCellPhone) {
        this.carryCellPhone = carryCellPhone;
    }

    @Column(name = "carry_name")
    public String getCarryName() {
        return carryName;
    }

    public void setCarryName(String carryName) {
        this.carryName = carryName;
    }

    @Column(name = "is_change_car")
    public Integer getIsChangeCar() {
        return isChangeCar;
    }

    public void setIsChangeCar(Integer isChangeCar) {
        this.isChangeCar = isChangeCar;
    }

    @Column(name = "is_allow_location")
    public Integer getIsAllowLocation() {
        return isAllowLocation;
    }

    public void setIsAllowLocation(Integer isAllowLocation) {
        this.isAllowLocation = isAllowLocation;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Column(name = "deal_price")
    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    @Column(name = "excellent_goods")
    public Integer getExcellentGoods() {
        return excellentGoods;
    }

    public void setExcellentGoods(Integer excellentGoods) {
        this.excellentGoods = excellentGoods;
    }

    @Column(name = "excellent_goods_two")
    public Integer getExcellentGoodsTwo() {
        return excellentGoodsTwo;
    }

    public void setExcellentGoodsTwo(Integer excellentGoodsTwo) {
        this.excellentGoodsTwo = excellentGoodsTwo;
    }

    @Column(name = "publish_goods_type")
    public Integer getPublishGoodsType() {
        return publishGoodsType;
    }

    public void setPublishGoodsType(Integer publishGoodsType) {
        this.publishGoodsType = publishGoodsType;
    }

    @Column(name = "machine_remark")
    public String getMachineRemark() {
        return machineRemark;
    }

    public void setMachineRemark(String machineRemark) {
        this.machineRemark = machineRemark;
    }

    @Column(name = "driver_driving")
    public Integer getDriverDriving() {
        return driverDriving;
    }

    public void setDriverDriving(Integer driverDriving) {
        this.driverDriving = driverDriving;
    }

    @Column(name = "load_cell_phone")
    public String getLoadCellPhone() {
        return loadCellPhone;
    }

    public void setLoadCellPhone(String loadCellPhone) {
        this.loadCellPhone = loadCellPhone;
    }

    @Column(name = "unload_cell_phone")
    public String getUnloadCellPhone() {
        return unloadCellPhone;
    }

    public void setUnloadCellPhone(String unloadCellPhone) {
        this.unloadCellPhone = unloadCellPhone;
    }

    @Column(name = "cargo_owner_id")
    public Long getCargoOwnerId() {
        return cargoOwnerId;
    }

    public void setCargoOwnerId(Long cargoOwnerId) {
        this.cargoOwnerId = cargoOwnerId;
    }

    @Column(name = "excellent_card_id")
    public Long getExcellentCardId() {
        return excellentCardId;
    }

    public void setExcellentCardId(Long excellentCardId) {
        this.excellentCardId = excellentCardId;
    }

}
