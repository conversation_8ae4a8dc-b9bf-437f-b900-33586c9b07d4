package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 计算货车固定费用燃油费用表达式
 * Created by tianjw on 2017/5/23.
 */
@Entity
@Table(name = "tyt_car_base_oil_cost")
public class TytCarBaseOilCost implements Serializable {

    private static final long serialVersionUID = 5784219447303843476L;
    private Long id;
    private Integer cargoLengthBegin; //货物长度开始
    private Integer cargoLengthEnd;//货物长度结束
    private Float carWeight;    //车辆自重
    private Float weightBegin;//吨位开始
    private Float weightEnd;//吨位结束
    private Integer perDayCost;//日均成本
    private Float carOilWear;//空车油耗
    private Float oilWear20;//载重<20吨
    private Float oilWear2030;  //载重20-30吨
    private Float oilWear30;//载重>30吨
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name="id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Column(name="cargo_length_begin")
    public Integer getCargoLengthBegin() {
        return cargoLengthBegin;
    }

    public void setCargoLengthBegin(Integer cargoLengthBegin) {
        this.cargoLengthBegin = cargoLengthBegin;
    }
    @Column(name="cargo_length_end")
    public Integer getCargoLengthEnd() {
        return cargoLengthEnd;
    }

    public void setCargoLengthEnd(Integer cargoLengthEnd) {
        this.cargoLengthEnd = cargoLengthEnd;
    }
    @Column(name="car_weight")
    public Float getCarWeight() {
        return carWeight;
    }

    public void setCarWeight(Float carWeight) {
        this.carWeight = carWeight;
    }
    @Column(name="weight_begin")
    public Float getWeightBegin() {
        return weightBegin;
    }

    public void setWeightBegin(Float weightBegin) {
        this.weightBegin = weightBegin;
    }
    @Column(name="wieght_end")
    public Float getWeightEnd() {
        return weightEnd;
    }

    public void setWeightEnd(Float weightEnd) {
        this.weightEnd = weightEnd;
    }
    @Column(name="per_day_cost")
    public Integer getPerDayCost() {
        return perDayCost;
    }

    public void setPerDayCost(Integer perDayCost) {
        this.perDayCost = perDayCost;
    }
    @Column(name="car_oil_wear")
    public Float getCarOilWear() {
        return carOilWear;
    }

    public void setCarOilWear(Float carOilWear) {
        this.carOilWear = carOilWear;
    }
    @Column(name="oil_wear20")
    public Float getOilWear20() {
        return oilWear20;
    }

    public void setOilWear20(Float oilWear20) {
        this.oilWear20 = oilWear20;
    }
    @Column(name="oil_wear2030")
    public Float getOilWear2030() {
        return oilWear2030;
    }

    public void setOilWear2030(Float oilWear2030) {
        this.oilWear2030 = oilWear2030;
    }
    @Column(name="oil_wear30")
    public Float getOilWear30() {
        return oilWear30;
    }

    public void setOilWear30(Float oilWear30) {
        this.oilWear30 = oilWear30;
    }
    @Column(name="mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}
