package com.tyt.model;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_transport_view_log")
public class TransportViewLog implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1865873643527092985L;
    /**
     * 自增ID 无意义
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 货源ID，值为src_msg_id
     */
    private Long tsId;

    /**
     * 客户端版本号
     */
    private String clientVersion;

    /**
     * 客户端标识 1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
     */
    private Integer clientSign;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 更新时间
     */
    private Date mtime;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "ts_id")
    public Long getTsId() {
        return tsId;
    }

    public void setTsId(Long tsId) {
        this.tsId = tsId;
    }

    @Column(name = "client_version")
    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    @Column(name = "client_sign")
    public Integer getClientSign() {
        return clientSign;
    }

    public void setClientSign(Integer clientSign) {
        this.clientSign = clientSign;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "mtime")
    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
