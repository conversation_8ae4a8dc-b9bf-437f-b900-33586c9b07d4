package com.tyt.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：2021:09:06 18:16
 * @description：
 * @ClassName : com.tyt.model.ProbCoupon
 */
@Entity
@Table(name = "prob_coupon")
@Data
public class ProbCoupon {
    private Long id;

    /**
     * 活动详情ID
     */
    private Long drawActivityInfoId;

    /**
     * 活动ID
     */
    private Long promoActivityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 奖品类型 1 实物   2 代金券
     */
    private Integer awardType;

    /**
     * 中奖概率
     */
    private BigDecimal awardProb;

    /**
     * 奖品数量
     */
    private Integer awardTotalAmount;

    /**
     * 奖品使用数量
     */
    private Integer awardUsedAmount;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 是否删除 0 不删除  1 删除
     */
    private Integer isDelete;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Column(name = "create_by")
    private Long createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Column(name = "update_by")
    private Long updateBy;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }
    @Column(name = "draw_activity_info_id")
    public Long getDrawActivityInfoId() {
        return drawActivityInfoId;
    }
    @Column(name = "promo_activity_id")
    public Long getPromoActivityId() {
        return promoActivityId;
    }
    @Column(name = "activity_name")
    public String getActivityName() {
        return activityName;
    }
    @Column(name = "award_type")
    public Integer getAwardType() {
        return awardType;
    }
    @Column(name = "award_prob")
    public BigDecimal getAwardProb() {
        return awardProb;
    }
    @Column(name = "award_total_amount")
    public Integer getAwardTotalAmount() {
        return awardTotalAmount;
    }
    @Column(name = "award_used_amount")
    public Integer getAwardUsedAmount() {
        return awardUsedAmount;
    }
    @Column(name = "is_delete")
    public Integer getIsDelete() {
        return isDelete;
    }
    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }
    @Column(name = "create_by")
    public Long getCreateBy() {
        return createBy;
    }
    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }
    @Column(name = "update_by")
    public Long getUpdateBy() {
        return updateBy;
    }
    @Column(name = "sort")
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setDrawActivityInfoId(Long drawActivityInfoId) {
        this.drawActivityInfoId = drawActivityInfoId;
    }

    public void setPromoActivityId(Long promoActivityId) {
        this.promoActivityId = promoActivityId;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public void setAwardType(Integer awardType) {
        this.awardType = awardType;
    }

    public void setAwardProb(BigDecimal awardProb) {
        this.awardProb = awardProb;
    }

    public void setAwardTotalAmount(Integer awardTotalAmount) {
        this.awardTotalAmount = awardTotalAmount;
    }

    public void setAwardUsedAmount(Integer awardUsedAmount) {
        this.awardUsedAmount = awardUsedAmount;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public ProbCoupon() {
    }

}
