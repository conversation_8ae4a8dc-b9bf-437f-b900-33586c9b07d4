package com.tyt.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName LuckdrawActivityInfo
 * @Description 抽奖活动信息对象
 * <AUTHOR>
 * @Date 2020-03-09 11:51
 * @Version 1.0
 */
@Entity
@Table(name = "luckdraw_activity_info", schema = "tyt", catalog = "")
public class LuckdrawActivityInfo {
    private Integer id;
    private String activityName;
    private Date startTime;
    private Date endTime;
    private Integer status;
    private Integer limitTimes;
    private Integer limitTimesType;
    private Long createUserId;
    private String createUserName;
    private Date ctime;
    private Long updateUserId;
    private String updateUserName;
    private Date utime;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Basic
    @Column(name = "start_time")
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Basic
    @Column(name = "end_time")
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Basic
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "limit_times")
    public Integer getLimitTimes() {
        return limitTimes;
    }

    public void setLimitTimes(Integer limitTimes) {
        this.limitTimes = limitTimes;
    }

    @Basic
    @Column(name = "limit_times_type")
    public Integer getLimitTimesType() {
        return limitTimesType;
    }

    public void setLimitTimesType(Integer limitTimesType) {
        this.limitTimesType = limitTimesType;
    }

    @Basic
    @Column(name = "create_user_id")
    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    @Basic
    @Column(name = "create_user_name")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @Basic
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Basic
    @Column(name = "update_user_id")
    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    @Basic
    @Column(name = "update_user_name")
    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Basic
    @Column(name = "utime")
    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LuckdrawActivityInfo that = (LuckdrawActivityInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(activityName, that.activityName) &&
                Objects.equals(startTime, that.startTime) &&
                Objects.equals(endTime, that.endTime) &&
                Objects.equals(status, that.status) &&
                Objects.equals(limitTimes, that.limitTimes) &&
                Objects.equals(limitTimesType, that.limitTimesType) &&
                Objects.equals(createUserId, that.createUserId) &&
                Objects.equals(createUserName, that.createUserName) &&
                Objects.equals(ctime, that.ctime) &&
                Objects.equals(updateUserId, that.updateUserId) &&
                Objects.equals(updateUserName, that.updateUserName) &&
                Objects.equals(utime, that.utime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, activityName, startTime, endTime, status, limitTimes, limitTimesType, createUserId, createUserName, ctime, updateUserId, updateUserName, utime);
    }
}
