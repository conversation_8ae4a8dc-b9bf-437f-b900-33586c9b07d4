package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_internal_employee")
public class TytInternalEmployee implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6793322559053374796L;
	private Long id;
	// `name`,
	private String name;
	// `login_phone_no`,
	private String loginPhoneNo;
	// `password`,
	private String passWord;
	// `real_name`,
	private String realName;
	// `email`,
	private String email;
	// `department_id`,
	private Long departmentId;
	// `leader_id`,
	// private Long leaderId;
	private String upLeaderName;
	// `subordinate_id`,
	private String changeLeaderName;
	// `is_valid`,
	private Integer isValid;
	// `last_login_time`,
	private Date lastLoginTime;
	// `create_time`,
	private Date createTime;
	// `update_time`,
	private Date updateTime;
	// `last_updater`
	private Long lastUpdater;

	private Long positionId;
	private Long institutionId;
	/*
	 * 数据权限类型 0 不做限制 1 销售模块查看自己及下级数据，2 销售模块 查看所有数据，如果是多个数字使用逗号分隔如1,2
	 */
	private String dataInstitutionType;
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "name")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "login_phone_no")
	public String getLoginPhoneNo() {
		return loginPhoneNo;
	}

	public void setLoginPhoneNo(String loginPhoneNo) {
		this.loginPhoneNo = loginPhoneNo;
	}

	@Column(name = "password")
	public String getPassWord() {
		return passWord;
	}

	public void setPassWord(String passWord) {
		this.passWord = passWord;
	}

	@Column(name = "real_name")
	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	@Column(name = "email")
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Column(name = "department_id")
	// @ManyToOne(targetEntity=TytDepartment.class)
	// @JoinColumn(name="id")
	public Long getDepartmentId() {
		return departmentId;
	}

	public void setDepartmentId(Long departmentId) {
		this.departmentId = departmentId;
	}

	@Column(name = "up_leader_name")
	public String getUpLeaderName() {
		return upLeaderName;
	}

	public void setUpLeaderName(String upLeaderName) {
		this.upLeaderName = upLeaderName;
	}

	@Column(name = "change_leader_name")
	public String getChangeLeaderName() {
		return changeLeaderName;
	}

	public void setChangeLeaderName(String changeLeaderName) {
		this.changeLeaderName = changeLeaderName;
	}

	@Column(name = "is_valid")
	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	@Column(name = "last_login_time")
	public Date getLastLoginTime() {
		return lastLoginTime;
	}

	public void setLastLoginTime(Date lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "last_updater")
	public Long getLastUpdater() {
		return lastUpdater;
	}

	public void setLastUpdater(Long lastUpdater) {
		this.lastUpdater = lastUpdater;
	}

	@Column(name = "position_id")
	public Long getPositionId() {
		return positionId;
	}

	public void setPositionId(Long positionId) {
		this.positionId = positionId;
	}

	@Column(name = "institution_id")
	public Long getInstitutionId() {
		return institutionId;
	}

	public void setInstitutionId(Long institutionId) {
		this.institutionId = institutionId;
	}
	@Column(name = "data_institution_type")
	public String getDataInstitutionType() {
		return dataInstitutionType;
	}

	public void setDataInstitutionType(String dataInstitutionType) {
		this.dataInstitutionType = dataInstitutionType;
	}
}
