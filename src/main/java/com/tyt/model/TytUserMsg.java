package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytUserMsg entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_user_msg")
public class TytUserMsg implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 4996844272608616557L;
	private Long id;
	private Long userId;
	private String cellPhone;
	private String trueName;
	private Long msgId;
	private Date ctime;
	private String readStatus;
	private Date mtime;
	private String delStatus;
	// Constructors

	/** default constructor */
	public TytUserMsg() {
	}

	/** full constructor */
	public TytUserMsg(Long userId, Long msgId, Date ctime, String readStatus,
			Date mtime) {
		this.userId = userId;
		this.msgId = msgId;
		this.ctime = ctime;
		this.readStatus = readStatus;
		this.mtime = mtime;
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "msg_id")
	public Long getMsgId() {
		return this.msgId;
	}

	public void setMsgId(Long msgId) {
		this.msgId = msgId;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "read_status")
	public String getReadStatus() {
		return this.readStatus;
	}

	public void setReadStatus(String readStatus) {
		this.readStatus = readStatus;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Column(name = "del_status")
	public String getDelStatus() {
		return delStatus;
	}

	public void setDelStatus(String delStatus) {
		this.delStatus = delStatus;
	}


	@Column(name = "cell_phone", length = 30)
	public String getCellPhone() {
		return this.cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "true_name", length = 30)
	public String getTrueName() {
		return this.trueName;
	}

	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}


}