package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * 
 * @date 2017年8月29日上午11:53:36
 * @description
 */
@Entity
@Table(name = "freight_route", catalog = "tyt")
public class FreightRoute implements java.io.Serializable {

	private static final long serialVersionUID = -8821293481830284267L;
	private Long id;
	private String mapKey;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private Integer startCoordX;
	private Integer startCoordY;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private Integer destCoordX;
	private Integer destCoordY;
	private Integer startLongitude;
	private Integer startLatitude;
	private Integer destLongitude;
	private Integer destLatitude;
	private String provinceRoad;
	private Integer freightDistance;
	private Integer distance;
	private Integer diffDistance;
	private Date ctime;
	private Date utime;
	private Short status;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "map_key", length = 32)
	public String getMapKey() {
		return this.mapKey;
	}

	public void setMapKey(String mapKey) {
		this.mapKey = mapKey;
	}

	@Column(name = "start_provinc", length = 50)
	public String getStartProvinc() {
		return this.startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	@Column(name = "start_city", length = 50)
	public String getStartCity() {
		return this.startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	@Column(name = "start_area", length = 50)
	public String getStartArea() {
		return this.startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	@Column(name = "start_coord_x")
	public Integer getStartCoordX() {
		return this.startCoordX;
	}

	public void setStartCoordX(Integer startCoordX) {
		this.startCoordX = startCoordX;
	}

	@Column(name = "start_coord_y")
	public Integer getStartCoordY() {
		return this.startCoordY;
	}

	public void setStartCoordY(Integer startCoordY) {
		this.startCoordY = startCoordY;
	}

	@Column(name = "dest_provinc", length = 50)
	public String getDestProvinc() {
		return this.destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	@Column(name = "dest_city", length = 50)
	public String getDestCity() {
		return this.destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	@Column(name = "dest_area", length = 50)
	public String getDestArea() {
		return this.destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "dest_coord_x")
	public Integer getDestCoordX() {
		return this.destCoordX;
	}

	public void setDestCoordX(Integer destCoordX) {
		this.destCoordX = destCoordX;
	}

	@Column(name = "dest_coord_y")
	public Integer getDestCoordY() {
		return this.destCoordY;
	}

	public void setDestCoordY(Integer destCoordY) {
		this.destCoordY = destCoordY;
	}

	@Column(name = "province_road", length = 2000)
	public String getProvinceRoad() {
		return this.provinceRoad;
	}

	public void setProvinceRoad(String provinceRoad) {
		this.provinceRoad = provinceRoad;
	}

	@Column(name = "freight_distance")
	public Integer getFreightDistance() {
		return this.freightDistance;
	}

	public void setFreightDistance(Integer freightDistance) {
		this.freightDistance = freightDistance;
	}

	@Column(name = "distance")
	public Integer getDistance() {
		return this.distance;
	}

	public void setDistance(Integer distance) {
		this.distance = distance;
	}

	@Column(name = "status")
	public Short getStatus() {
		return status;
	}

	public void setStatus(Short status) {
		this.status = status;
	}
	@Column(name = "start_longitude")
	public Integer getStartLongitude() {
		return startLongitude;
	}

	public void setStartLongitude(Integer startLongitude) {
		this.startLongitude = startLongitude;
	}
	@Column(name = "start_latitude")
	public Integer getStartLatitude() {
		return startLatitude;
	}

	public void setStartLatitude(Integer startLatitude) {
		this.startLatitude = startLatitude;
	}
	@Column(name = "dest_longitude")
	public Integer getDestLongitude() {
		return destLongitude;
	}

	public void setDestLongitude(Integer destLongitude) {
		this.destLongitude = destLongitude;
	}
	@Column(name = "dest_latitude")
	public Integer getDestLatitude() {
		return destLatitude;
	}

	public void setDestLatitude(Integer destLatitude) {
		this.destLatitude = destLatitude;
	}
	@Column(name = "diff_distance")
	public Integer getDiffDistance() {
		return diffDistance;
	}

	public void setDiffDistance(Integer diffDistance) {
		this.diffDistance = diffDistance;
	}
	@Column(name = "utime")
	public Date getUtime() {
		return utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
}
