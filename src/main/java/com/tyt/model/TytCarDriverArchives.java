package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "tyt_car_driver_archives")
public class TytCarDriverArchives implements java.io.Serializable {
    private Long id;
    /**
     * 所属车辆id
     */
    private Long carId;
    private Long userId;
    private String userPhone;
    private String userShowName;
    private String driverPhone;
    private String driverShowName;
    private Integer examineStatus;
    private String dicNumber;
    private Integer dicAgingType;
    private Date dicIssueDate;
    private Date dicExpiryDate;
    private String dlNumber;
    private Integer dlAgingType;
    private Date dlIssueDate;
    private Date dlExpiryDate;
    private String dlType;
    private String dlIssueAuthority;
    private String dcNumber;
    private Date dcIssueDate;
    private Date dcExpiryDate;
    private String dicFrontImageUrl;
    private Integer dicfExamineStatus;
    private String dicfExamineLoseNote;
    private String dicBackImageUrl;
    private Integer dicbExamineStatus;
    private String dicbExamineLoseNote;
    private String dlFrontImageUrl;
    private Integer dlExamineStatus;
    private String dlExamineLoseNote;
    private String dcImageUrl;
    private Integer dcExamineStatus;
    private String dcExamineLoseNote;
    private Date commitTime;
    private Long examineUserId;
    private String examineUserName;
    private Date examineTime;
    private Date ctime;
    private Date utime;

    public void setId(Long id){
        this.id = id;
    }

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId(){
        return this.id;
    }

    public void setUserId(Long userId){
        this.userId = userId;
    }

    @Column(name = "user_id")
    public Long getUserId(){
        return this.userId;
    }

    public void setUserPhone(String userPhone){
        this.userPhone = userPhone;
    }

    @Column(name = "user_phone")
    public String getUserPhone(){
        return this.userPhone;
    }

    public void setUserShowName(String userShowName){
        this.userShowName = userShowName;
    }

    @Column(name = "user_show_name")
    public String getUserShowName(){
        return this.userShowName;
    }

    public void setDriverPhone(String driverPhone){
        this.driverPhone = driverPhone;
    }

    @Column(name = "driver_phone")
    public String getDriverPhone(){
        return this.driverPhone;
    }

    public void setDriverShowName(String driverShowName){
        this.driverShowName = driverShowName;
    }

    @Column(name = "driver_show_name")
    public String getDriverShowName(){
        return this.driverShowName;
    }

    public void setExamineStatus(Integer examineStatus){
        this.examineStatus = examineStatus;
    }

    @Column(name = "examine_status")
    public Integer getExamineStatus(){
        return this.examineStatus;
    }

    public void setDicNumber(String dicNumber){
        this.dicNumber = dicNumber;
    }

    @Column(name = "dic_number")
    public String getDicNumber(){
        return this.dicNumber;
    }

    public void setDicAgingType(Integer dicAgingType){
        this.dicAgingType = dicAgingType;
    }

    @Column(name = "dic_aging_type")
    public Integer getDicAgingType(){
        return this.dicAgingType;
    }

    public void setDicIssueDate(Date dicIssueDate){
        this.dicIssueDate = dicIssueDate;
    }

    @Column(name = "dic_issue_date")
    public Date getDicIssueDate(){
        return this.dicIssueDate;
    }

    public void setDicExpiryDate(Date dicExpiryDate){
        this.dicExpiryDate = dicExpiryDate;
    }

    @Column(name = "dic_expiry_date")
    public Date getDicExpiryDate(){
        return this.dicExpiryDate;
    }

    public void setDlNumber(String dlNumber){
        this.dlNumber = dlNumber;
    }

    @Column(name = "dl_number")
    public String getDlNumber(){
        return this.dlNumber;
    }

    public void setDlAgingType(Integer dlAgingType){
        this.dlAgingType = dlAgingType;
    }

    @Column(name = "dl_aging_type")
    public Integer getDlAgingType(){
        return this.dlAgingType;
    }

    public void setDlIssueDate(Date dlIssueDate){
        this.dlIssueDate = dlIssueDate;
    }

    @Column(name = "dl_issue_date")
    public Date getDlIssueDate(){
        return this.dlIssueDate;
    }

    public void setDlExpiryDate(Date dlExpiryDate){
        this.dlExpiryDate = dlExpiryDate;
    }

    @Column(name = "dl_expiry_date")
    public Date getDlExpiryDate(){
        return this.dlExpiryDate;
    }

    public void setDlType(String dlType){
        this.dlType = dlType;
    }

    @Column(name = "dl_type")
    public String getDlType(){
        return this.dlType;
    }

    public void setDlIssueAuthority(String dlIssueAuthority){
        this.dlIssueAuthority = dlIssueAuthority;
    }

    @Column(name = "dl_issue_authority")
    public String getDlIssueAuthority(){
        return this.dlIssueAuthority;
    }

    public void setDcNumber(String dcNumber){
        this.dcNumber = dcNumber;
    }

    @Column(name = "dc_number")
    public String getDcNumber(){
        return this.dcNumber;
    }

    public void setDcIssueDate(Date dcIssueDate){
        this.dcIssueDate = dcIssueDate;
    }

    @Column(name = "dc_issue_date")
    public Date getDcIssueDate(){
        return this.dcIssueDate;
    }

    public void setDcExpiryDate(Date dcExpiryDate){
        this.dcExpiryDate = dcExpiryDate;
    }

    @Column(name = "dc_expiry_date")
    public Date getDcExpiryDate(){
        return this.dcExpiryDate;
    }

    public void setDicFrontImageUrl(String dicFrontImageUrl){
        this.dicFrontImageUrl = dicFrontImageUrl;
    }

    @Column(name = "dic_front_image_url")
    public String getDicFrontImageUrl(){
        return this.dicFrontImageUrl;
    }

    public void setDicfExamineStatus(Integer dicfExamineStatus){
        this.dicfExamineStatus = dicfExamineStatus;
    }

    @Column(name = "dicf_examine_status")
    public Integer getDicfExamineStatus(){
        return this.dicfExamineStatus;
    }

    public void setDicfExamineLoseNote(String dicfExamineLoseNote){
        this.dicfExamineLoseNote = dicfExamineLoseNote;
    }

    @Column(name = "dicf_examine_lose_note")
    public String getDicfExamineLoseNote(){
        return this.dicfExamineLoseNote;
    }

    public void setDicBackImageUrl(String dicBackImageUrl){
        this.dicBackImageUrl = dicBackImageUrl;
    }

    @Column(name = "dic_back_image_url")
    public String getDicBackImageUrl(){
        return this.dicBackImageUrl;
    }

    public void setDicbExamineStatus(Integer dicbExamineStatus){
        this.dicbExamineStatus = dicbExamineStatus;
    }

    @Column(name = "dicb_examine_status")
    public Integer getDicbExamineStatus(){
        return this.dicbExamineStatus;
    }

    public void setDicbExamineLoseNote(String dicbExamineLoseNote){
        this.dicbExamineLoseNote = dicbExamineLoseNote;
    }

    @Column(name = "dicb_examine_lose_note")
    public String getDicbExamineLoseNote(){
        return this.dicbExamineLoseNote;
    }

    public void setDlFrontImageUrl(String dlFrontImageUrl){
        this.dlFrontImageUrl = dlFrontImageUrl;
    }

    @Column(name = "dl_front_image_url")
    public String getDlFrontImageUrl(){
        return this.dlFrontImageUrl;
    }

    public void setDlExamineStatus(Integer dlExamineStatus){
        this.dlExamineStatus = dlExamineStatus;
    }

    @Column(name = "dl_examine_status")
    public Integer getDlExamineStatus(){
        return this.dlExamineStatus;
    }

    public void setDlExamineLoseNote(String dlExamineLoseNote){
        this.dlExamineLoseNote = dlExamineLoseNote;
    }

    @Column(name = "dl_examine_lose_note")
    public String getDlExamineLoseNote(){
        return this.dlExamineLoseNote;
    }

    public void setDcImageUrl(String dcImageUrl){
        this.dcImageUrl = dcImageUrl;
    }

    @Column(name = "dc_image_url")
    public String getDcImageUrl(){
        return this.dcImageUrl;
    }

    public void setDcExamineStatus(Integer dcExamineStatus){
        this.dcExamineStatus = dcExamineStatus;
    }

    @Column(name = "dc_examine_status")
    public Integer getDcExamineStatus(){
        return this.dcExamineStatus;
    }

    public void setDcExamineLoseNote(String dcExamineLoseNote){
        this.dcExamineLoseNote = dcExamineLoseNote;
    }

    @Column(name = "dc_examine_lose_note")
    public String getDcExamineLoseNote(){
        return this.dcExamineLoseNote;
    }

    public void setCommitTime(Date commitTime){
        this.commitTime = commitTime;
    }

    @Column(name = "commit_time")
    public Date getCommitTime(){
        return this.commitTime;
    }

    public void setExamineUserId(Long examineUserId){
        this.examineUserId = examineUserId;
    }

    @Column(name = "examine_user_id")
    public Long getExamineUserId(){
        return this.examineUserId;
    }

    public void setExamineUserName(String examineUserName){
        this.examineUserName = examineUserName;
    }

    @Column(name = "examine_user_name")
    public String getExamineUserName(){
        return this.examineUserName;
    }

    public void setExamineTime(Date examineTime){
        this.examineTime = examineTime;
    }

    @Column(name = "examine_time")
    public Date getExamineTime(){
        return this.examineTime;
    }

    public void setCtime(Date ctime){
        this.ctime = ctime;
    }

    @Column(name = "ctime")
    public Date getCtime(){
        return this.ctime;
    }

    public void setUtime(Date utime){
        this.utime = utime;
    }

    @Column(name = "utime")
    public Date getUtime(){
        return this.utime;
    }
    @Transient
    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }
}