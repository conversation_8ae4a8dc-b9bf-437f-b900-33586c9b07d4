package com.tyt.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TytTransportOrders entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_transport_orders")
public class TytTransportOrders implements java.io.Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 3575430860556473139L;
	private Long id;
	private String startPoint;
	private String destPoint;
	private String taskContent;
	private String tel;
	private String pubTime;
	private Date ctime;
	private String uploadCellphone;
	private Long userId;
	private String linkman;
	private String tel3;
	private String tel4;
	private Long sortId;
	private String tsOrderNo;
	private Long tsId;
	private Long payUserId;
	private String payCellPhone;
	private String payLinkPhone;
	private int infoFeeType;
	private String robStatus;
	private String payStatus;
	private Date createTime;
	private String payNo;
	private String payType;
	private Long payAmount;
	private Long payFeeAmount;
	private Date payEndTime;
	private Date agreeTime;
	private Date loadTime;
	private Date refuseTime;
	private Date mtime;
	private Integer costStatus;
	private Long refundAmount;
	private Date refundTime;
	private String refundReason;
	//发布人昵称
	private String pubUserName;
	//车主昵称
	private String payUserName;
	/**
	 * 车头城市
	 */
	private String headCity;
	/**
	 * 车头号码
	 */
	private String headNo;
	/**
	 * 挂车城市
	 */
	private String tailCity;
	/**
	 * 挂车号码
	 */
	private String tailNo;
	/**
	 * 车辆ID
	 */
	private Long carId;
	/**
	 * 操作人ID
	 */
	private Long opId;
	/**
	 * 操作人姓名
	 */
	private String opName;
	/**
	 * 支付子渠道
	 */
	private String paySubChannel;
	/**
	 * 支付订单号
	 */
	private String payOrderNo;

	/**
	 * 技术服务费单号
	 */
	private String technicalServiceNo;

	/**
	 * 运费金额 单位元
	 */
	private Integer carriageFee;

	/**
	 * 服务费
	 */
	private BigDecimal payServiceCharge;

	/**
	 * 是否限时货源标识
	 */
	private Integer timeLimitIdentification;
	/**
	 * 限时货源是否成交车标识 0不是 1是
	 */
	private Integer isDealCar;

	/**
	 * 实际到期时间
	 */
	private  Date  dePaymentDueDate;

	/**
	 * 延迟付款状态 0 未延迟 1 延迟付款  2 拒绝退款延迟
	 */
	private int delayStatus;

	/**
	 * 车方详情是否展示 0展示 1不展示
	 */
	private int carShow;

	/**
	 * 货方详情是否展示 0展示 1不展示
	 */
	private int goodsShow;

	/**
	 *是否更换过车辆 0未更换 1已更换
	 */
	private int carReplace;

	private Integer refundFlag;


	private Integer refundStatus;

	private Integer delayRefundStatus;

	private Date deRefundDueDate;

	private Date deLoadDueDate;

	/**
	 * 三方平台类型
	 * 0:特运通货源订单
	 * 1:特运通货源，满帮订单
	 * 2:满帮货源，特运通订单
	 */
	private Integer thirdpartyPlatformType;

	/**
	 * 三方平台单号
	 */
	private String thirdpartyPlatformOrderNo;

	private Integer loadingStatus;

	/**
	 * 订单总金额(单位分)
	 */
	private Long totalOrderAmount;

	/**
	 * 优惠券金额(单位分)
	 */
	private Long couponAmount;

	private Integer sourceType;

	/**
	 * 异常上报取消状态:0初始化 1车方操作过撤销(货方未操作过撤销) 2 货方操作过撤销(车方未操作过撤销) 3 车方货方均操作过撤销
	 **/
	private int exCancelStatus;

	private String refundSpecificReason;

	private String  refundRemark;
	/**
	 * 技术服务费 单位分
	 */
	private Long tecServiceFee;
	/**
	 * 标准货名备注
	 */
	private String machineRemark;

	/**
	 * 技术服务费-平台分配金额 单位分
	 */
	private Long platformServiceAmount;

	/**
	 * 技术服务费-车方分配金额 单位分
	 */
	private Long carServiceAmount;

	/**
	 * 车方分配金额
	 */
	private Long carAmount;

	private Integer driverId;
	/**
	 * 货方分配金额
	 */
	private Long goodsAmount;

	private Integer orderNewStatus;//新订单状态

	private Integer invoiceTransport;

	/**
	 * 服务商编码：JCZY-自营 HBWJ-我家
	 */
	private String invoiceServiceCode;

	/**
	 * 开票主体id
	 */
	private Long invoiceIssuerId;

	/**
	 * 开票主体编码
	 */
	private String invoiceIssuerCode;

	/**
	 * 开票三方订单id
	 */
    private String invoiceThirdPartyId;

	/**
	 * 开票三方订单号
	 */
	private String invoiceThirdPartyNo;


	/**
	 * 是否是开票指派单（0:否 1:是）
	 */
	private Integer isAssignOrder;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	@Column(name = "invoice_transport")
	public Integer getInvoiceTransport() {
		return invoiceTransport;
	}

	public void setInvoiceTransport(Integer invoiceTransport) {
		this.invoiceTransport = invoiceTransport;
	}

	@Column(name = "refund_specific_reason")
	public String getRefundSpecificReason() {
		return refundSpecificReason;
	}

	public void setRefundSpecificReason(String refundSpecificReason) {
		this.refundSpecificReason = refundSpecificReason;
	}
	@Column(name = "refund_remark")
	public String getRefundRemark() {
		return refundRemark;
	}

	public void setRefundRemark(String refundRemark) {
		this.refundRemark = refundRemark;
	}

	@Column(name = "start_point")
	public String getStartPoint() {
		return this.startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	@Column(name = "dest_point")
	public String getDestPoint() {
		return this.destPoint;
	}

	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}

	@Column(name = "task_content")
	public String getTaskContent() {
		return this.taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	@Column(name = "tel")
	public String getTel() {
		return this.tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	@Column(name = "pub_time")
	public String getPubTime() {
		return this.pubTime;
	}

	public void setPubTime(String pubTime) {
		this.pubTime = pubTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "upload_cellphone")
	public String getUploadCellphone() {
		return this.uploadCellphone;
	}

	public void setUploadCellphone(String uploadCellphone) {
		this.uploadCellphone = uploadCellphone;
	}

	@Column(name = "user_id", nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "linkman")
	public String getLinkman() {
		return this.linkman;
	}

	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}

	@Column(name = "tel3")
	public String getTel3() {
		return this.tel3;
	}

	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}

	@Column(name = "tel4")
	public String getTel4() {
		return this.tel4;
	}

	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}

	@Column(name = "sort_id", nullable = false)
	public Long getSortId() {
		return this.sortId;
	}

	public void setSortId(Long sortId) {
		this.sortId = sortId;
	}

	@Column(name = "ts_order_no", nullable = false)
	public String getTsOrderNo() {
		return this.tsOrderNo;
	}

	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}

	@Column(name = "ts_id", nullable = false)
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "pay_user_id")
	public Long getPayUserId() {
		return this.payUserId;
	}

	public void setPayUserId(Long payUserId) {
		this.payUserId = payUserId;
	}

	@Column(name = "pay_cell_phone")
	public String getPayCellPhone() {
		return this.payCellPhone;
	}

	public void setPayCellPhone(String payCellPhone) {
		this.payCellPhone = payCellPhone;
	}

	@Column(name = "pay_link_phone")
	public String getPayLinkPhone() {
		return this.payLinkPhone;
	}

	public void setPayLinkPhone(String payLinkPhone) {
		this.payLinkPhone = payLinkPhone;
	}

	@Column(name = "info_fee_type")
	public int getInfoFeeType() {
		return infoFeeType;
	}

	public void setInfoFeeType(int infoFeeType) {
		this.infoFeeType = infoFeeType;
	}

	@Column(name = "rob_status")
	public String getRobStatus() {
		return this.robStatus;
	}

	public void setRobStatus(String robStatus) {
		this.robStatus = robStatus;
	}

	@Column(name = "pay_status")
	public String getPayStatus() {
		return this.payStatus;
	}

	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "pay_no")
	public String getPayNo() {
		return this.payNo;
	}

	public void setPayNo(String payNo) {
		this.payNo = payNo;
	}

	@Column(name = "pay_type")
	public String getPayType() {
		return this.payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	@Column(name = "pay_fee_amount")
	public Long getPayFeeAmount() {
		return this.payFeeAmount;
	}

	@Column(name = "pay_amount")
	public Long getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(Long payAmount) {
		this.payAmount = payAmount;
	}

	public void setPayFeeAmount(Long payFeeAmount) {
		this.payFeeAmount = payFeeAmount;
	}

	@Column(name = "pay_end_time")
	public Date getPayEndTime() {
		return this.payEndTime;
	}

	public void setPayEndTime(Date payEndTime) {
		this.payEndTime = payEndTime;
	}

	@Column(name = "agree_time")
	public Date getAgreeTime() {
		return this.agreeTime;
	}

	public void setAgreeTime(Date agreeTime) {
		this.agreeTime = agreeTime;
	}

	@Column(name = "load_time")
	public Date getLoadTime() {
		return this.loadTime;
	}

	public void setLoadTime(Date loadTime) {
		this.loadTime = loadTime;
	}

	@Column(name = "refuse_time")
	public Date getRefuseTime() {
		return this.refuseTime;
	}

	public void setRefuseTime(Date refuseTime) {
		this.refuseTime = refuseTime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name = "cost_status")
	public Integer getCostStatus() {
		return costStatus;
	}

	public void setCostStatus(Integer costStatus) {
		this.costStatus = costStatus;
	}

	@Column(name = "refund_amount")
	public Long getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Long refundAmount) {
		this.refundAmount = refundAmount;
	}

	@Column(name = "refund_time")
	public Date getRefundTime() {
		return refundTime;
	}

	public void setRefundTime(Date refundTime) {
		this.refundTime = refundTime;
	}

	@Column(name = "refund_reason")
	public String getRefundReason() {
		return refundReason;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}

	@Column(name = "pub_user_name")
	public String getPubUserName() {
		return pubUserName;
	}

	public void setPubUserName(String pubUserName) {
		this.pubUserName = pubUserName;
	}

	@Column(name = "pay_user_name")
	public String getPayUserName() {
		return payUserName;
	}

	public void setPayUserName(String payUserName) {
		this.payUserName = payUserName;
	}

	@Column(name = "head_city")
	public String getHeadCity() {
		return headCity;
	}

	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}

	@Column(name = "head_no")
	public String getHeadNo() {
		return headNo;
	}

	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}

	@Column(name = "tail_city")
	public String getTailCity() {
		return tailCity;
	}

	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}

	@Column(name = "tail_no")
	public String getTailNo() {
		return tailNo;
	}

	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}

	@Column(name = "car_id")
	public Long getCarId() {
		return carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "op_id")
	public Long getOpId() {
		return opId;
	}

	public void setOpId(Long opId) {
		this.opId = opId;
	}

	@Column(name = "op_name")
	public String getOpName() {
		return opName;
	}

	public void setOpName(String opName) {
		this.opName = opName;
	}

	@Column(name = "pay_sub_channel")
	public String getPaySubChannel() {
		return paySubChannel;
	}

	public void setPaySubChannel(String paySubChannel) {
		this.paySubChannel = paySubChannel;
	}

	@Column(name = "pay_order_no")
	public String getPayOrderNo() {
		return payOrderNo;
	}

	public void setPayOrderNo(String payOrderNo) {
		this.payOrderNo = payOrderNo;
	}

	@Column(name = "carriage_fee")
	public Integer getCarriageFee() {
		return carriageFee;
	}

	public void setCarriageFee(Integer carriageFee) {
		this.carriageFee = carriageFee;
	}

	@Column(name = "pay_service_charge")
	public BigDecimal getPayServiceCharge() {
		return payServiceCharge;
	}

	public void setPayServiceCharge(BigDecimal payServiceCharge) {
		this.payServiceCharge = payServiceCharge;
	}

	@Column(name = "time_limit_identification")
	public Integer getTimeLimitIdentification() {
		return timeLimitIdentification;
	}

	public void setTimeLimitIdentification(Integer timeLimitIdentification) {
		this.timeLimitIdentification = timeLimitIdentification;
	}
	@Column(name = "is_deal_car")
	public Integer getIsDealCar() {
		return isDealCar;
	}

	public void setIsDealCar(Integer isDealCar) {
		this.isDealCar = isDealCar;
	}
	@Column(name = "de_payment_dueDate")
	public Date getDePaymentDueDate() {
		return dePaymentDueDate;
	}

	public void setDePaymentDueDate(Date dePaymentDueDate) {
		this.dePaymentDueDate = dePaymentDueDate;
	}


	@Column(name = "delay_status")
	public int getDelayStatus() {
		return delayStatus;
	}

	public void setDelayStatus(int delayStatus) {
		this.delayStatus = delayStatus;
	}


	@Column(name = "ex_cancel_status")
	public Integer getExCancelStatus() {
		return exCancelStatus;
	}

	public void setExCancelStatus(Integer exCancelStatus) {
		this.exCancelStatus = exCancelStatus;
	}


	@Column(name = "car_show")
	public int getCarShow() {
		return carShow;
	}

	public void setCarShow(int carShow) {
		this.carShow = carShow;
	}
	@Column(name = "goods_show")
	public int getGoodsShow() {
		return goodsShow;
	}

	public void setGoodsShow(int goodsShow) {
		this.goodsShow = goodsShow;
	}
	@Column(name = "car_replace")
	public int getCarReplace() {
		return carReplace;
	}

	public void setCarReplace(int carReplace) {
		this.carReplace = carReplace;
	}

	@Column(name = "refund_flag")
	public Integer getRefundFlag() {
		return refundFlag;
	}

	public void setRefundFlag(Integer refundFlag) {
		this.refundFlag = refundFlag;
	}

	@Column(name = "refund_status")
	public Integer getRefundStatus() {
		return refundStatus;
	}

	public void setRefundStatus(Integer refundStatus) {
		this.refundStatus = refundStatus;
	}

	@Column(name = "delay_refund_status")
	public Integer getDelayRefundStatus() {
		return delayRefundStatus;
	}

	public void setDelayRefundStatus(Integer delayRefundStatus) {
		this.delayRefundStatus = delayRefundStatus;
	}

	@Column(name = "de_refund_dueDate")
	public Date getDeRefundDueDate() {
		return deRefundDueDate;
	}

	public void setDeRefundDueDate(Date deRefundDueDate) {
		this.deRefundDueDate = deRefundDueDate;
	}

	@Column(name = "de_load_due_date")
	public Date getDeLoadDueDate() {
		return deLoadDueDate;
	}

	public void setDeLoadDueDate(Date deLoadDueDate) {
		this.deLoadDueDate = deLoadDueDate;
	}

	public void setExCancelStatus(int exCancelStatus) {
		this.exCancelStatus = exCancelStatus;
	}

	@Column(name = "thirdparty_platform_type")
	public Integer getThirdpartyPlatformType() {
		return thirdpartyPlatformType;
	}

	public void setThirdpartyPlatformType(Integer thirdpartyPlatformType) {
		this.thirdpartyPlatformType = thirdpartyPlatformType;
	}

	@Column(name = "thirdparty_platform_order_no")
	public String getThirdpartyPlatformOrderNo() {
		return thirdpartyPlatformOrderNo;
	}

	public void setThirdpartyPlatformOrderNo(String thirdpartyPlatformOrderNo) {
		this.thirdpartyPlatformOrderNo = thirdpartyPlatformOrderNo;
	}

	@Column(name = "loading_status")
	public Integer getLoadingStatus() {
		return loadingStatus;
	}

	public void setLoadingStatus(Integer loadingStatus) {
		this.loadingStatus = loadingStatus;
	}

	@Column(name = "total_order_amount")
	public Long getTotalOrderAmount() {
		return totalOrderAmount;
	}

	public void setTotalOrderAmount(Long totalOrderAmount) {
		this.totalOrderAmount = totalOrderAmount;
	}

	@Column(name = "coupon_amount")
	public Long getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}
	@Column(name = "source_type")
	public Integer getSourceType() {
		return sourceType;
	}

	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}

	@Column(name = "tec_service_fee")
	public Long getTecServiceFee() {
		return tecServiceFee;
	}

	public void setTecServiceFee(Long tecServiceFee) {
		this.tecServiceFee = tecServiceFee;
	}
	
	@Column(name = "platform_service_amount")
	public Long getPlatformServiceAmount() {
		return platformServiceAmount;
	}

	public void setPlatformServiceAmount(Long platformServiceAmount) {
		this.platformServiceAmount = platformServiceAmount;
	}

	@Column(name = "car_service_amount")
	public Long getCarServiceAmount() {
		return carServiceAmount;
	}

	public void setCarServiceAmount(Long carServiceAmount) {
		this.carServiceAmount = carServiceAmount;
	}
	@Column(name = "car_amount")
	public Long getCarAmount() {
		return carAmount;
	}

	public void setCarAmount(Long carAmount) {
		this.carAmount = carAmount;
	}

	@Column(name = "goods_amount")
	public Long getGoodsAmount() {
		return goodsAmount;
	}

	public void setGoodsAmount(Long goodsAmount) {
		this.goodsAmount = goodsAmount;
	}

	@Column(name = "technical_service_no")
	public String getTechnicalServiceNo() {
		return technicalServiceNo;
	}

	public void setTechnicalServiceNo(String technicalServiceNo) {
		this.technicalServiceNo = technicalServiceNo;
	}

	@Column(name = "machine_remark")
	public String getMachineRemark() {
		return machineRemark;
	}
	@Column(name = "order_new_status")
	public Integer getOrderNewStatus() {
		return orderNewStatus;
	}
	@Column(name = "driver_id")
	public Integer getDriverId() {
		return driverId;
	}

	public void setDriverId(Integer driverId) {
		this.driverId = driverId;
	}

	public void setOrderNewStatus(Integer orderNewStatus) {
		this.orderNewStatus = orderNewStatus;
	}

	public void setMachineRemark(String machineRemark) {
		this.machineRemark = machineRemark;
	}

	@Column(name = "invoice_service_code")
	public String getInvoiceServiceCode() {
		return invoiceServiceCode;
	}

	public void setInvoiceServiceCode(String invoiceServiceCode) {
		this.invoiceServiceCode = invoiceServiceCode;
	}

	@Column(name = "invoice_issuer_id")
	public Long getInvoiceIssuerId() {
		return invoiceIssuerId;
	}

	public void setInvoiceIssuerId(Long invoiceIssuerId) {
		this.invoiceIssuerId = invoiceIssuerId;
	}

	@Column(name = "invoice_issuer_code")
	public String getInvoiceIssuerCode() {
		return invoiceIssuerCode;
	}

	public void setInvoiceIssuerCode(String invoiceIssuerCode) {
		this.invoiceIssuerCode = invoiceIssuerCode;
	}

	@Column(name = "invoice_third_party_id")
	public String getInvoiceThirdPartyId() {
		return invoiceThirdPartyId;
	}

	public void setInvoiceThirdPartyId(String invoiceThirdPartyId) {
		this.invoiceThirdPartyId = invoiceThirdPartyId;
	}

	@Column(name = "invoice_third_party_no")
	public String getInvoiceThirdPartyNo() {
		return invoiceThirdPartyNo;
	}

	public void setInvoiceThirdPartyNo(String invoiceThirdPartyNo) {
		this.invoiceThirdPartyNo = invoiceThirdPartyNo;
	}
	@Column(name = "is_assign_order")
	public Integer getIsAssignOrder() {
		return isAssignOrder;
	}

	public void setIsAssignOrder(Integer isAssignOrder) {
		this.isAssignOrder = isAssignOrder;
	}
}
