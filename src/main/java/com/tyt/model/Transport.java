package com.tyt.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.plat.utils.BigDecimalSerialize;
import com.tyt.plat.utils.CityUtil;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * User: Administrator Date: 13-11-10 Time: 下午3:47
 */
@Data
@Entity
@Table(name = "tyt_transport")
public class Transport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 货源类型
     */
    public static final int PHONE_CONFERENCE = 1;
    public static final int BUY_IT_NOW = 2;

    public static final int STATUS_ENABLE = 1;
    public static final int STATUS_DISABLE = 0;
    public static final int STATUS_UNKNOWN = 2;
    public static final int STATUS_BLOCKED = 3;
    public static final int STATUS_DEAl = 4;

    public static final int SOURCE_AUTO = 1;
    public static final int SOURCE_MANUAL = 0;

    public static final String NO_REGISTER = "未注册";
    public static final String REGISTER_PAY = "付费使用";
    public static final String REGISTER_NO_PAY = "已注册试用";
    public static final String PAY_OTHER = "付费用户，未用注册手机发布";
    public static final String REGISTER_OTHER_NO_PAY = "已注册试用";
    public static final String PAY_NO_USE = "付费,未使用";
    public static final String NO_PAY_NO_USE = "已注册试用";

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 出发地(省市区以减号-分割开)
     */
    @Column(name = "start_point")
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    @Column(name = "dest_point")
    private String destPoint;

    /**
     * 货物内容
     */
    @Column(name = "task_content")
    private String taskContent;

    /**
     * 联系人
     */
    private String tel;

    /**
     * 发布时间
     */
    @Column(name = "pub_time")
    private String pubTime;

    /**
     * qq
     */
    @Column(name = "pub_qq")
    private Long pubQQ;

    /**
     * 昵称
     */
    @Column(name = "nick_name")
    private String nickName;

    /**
     * 用户认证昵称
     */
    @Column(name = "user_show_name")
    private String userShowName;

    /**
     * 状态 1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     */
    private Integer status;

    /**
     * 人工/自动
     */
    private Integer source;

    /**
     * 采集时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 上传电话
     */
    @Column(name = "upload_cellphone")
    private String uploadCellPhone;

    /**
     * 重发时间
     */
    private Integer resend;

    /**
     * 出发地坐标
     */
    @Column(name = "start_coord")
    private String startCoord;

    /**
     * 目的地坐标
     */
    @Column(name = "dest_coord")
    private String destCoord;

    /**
     * 终端标识
     */
    @Column(name = "plat_id")
    private Integer platId;

    /**
     * 验证标识
     */
    @Column(name = "verify_flag")
    private Integer verifyFlag;

    /**
     * 运费
     */
    private String price;

    /**
     * 发布人usreID 关联tyt_user表id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 运费代码
     */
    @Column(name = "price_code")
    private String priceCode;

    /**
     * 出发地坐标x
     */
    @Column(name = "start_coord_x")
    private Integer startCoordXValue;// '出发地坐标x',

    /**
     * 出发地坐标y
     */
    @Column(name = "start_coord_y")
    private Integer startCoordYValue;

    /**
     * 目的地坐标x
     */
    @Column(name = "dest_coord_x")
    private Integer destCoordXValue;// '目的地坐标x',

    /**
     * 目的地坐标y
     */
    @Column(name = "dest_coord_y")
    private Integer destCoordYValue;// '目的地坐标y',

    /**
     * 出发地详细地址
     */
    @Column(name = "start_detail_add")
    private String startDetailAdd;

    /**
     * 出发地经度
     */
    @Column(name = "start_longitude")
    private Integer startLongitudeValue;

    /**
     * 出发地纬度
     */
    @Column(name = "start_latitude")
    private Integer startLatitudeValue;

    /**
     * 目的地详细地址
     */
    @Column(name = "dest_detail_add")
    private String destDetailAdd;

    /**
     * 目的地经度
     */
    @Column(name = "dest_longitude")
    private Integer destLongitudeValue;

    /**
     * 目的地纬度
     */
    @Column(name = "dest_latitude")
    private Integer destLatitudeValue;

    /**
     * 发布日期
     */
    @Column(name = "pub_date")
    private Date pubDate;

    /**
     * 货物代码 与 货物对应 task_content 字段是一对
     */
    @Column(name = "goods_code")
    private String goodsCode;

    /**
     * 重量代码
     */
    @Column(name = "weight_code")
    private String weightCode;

    /**
     * 重量单位吨
     */
    private String weight;

    /**
     * 货物长单位米
     */
    private String length;

    /**
     * 货物宽单位米
     */
    private String wide;

    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 是否三超 0未超1超
     */
    @Column(name = "is_superelevation")
    private String isSuperelevation;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 备注
     */
    private String remark;

    /**
     * 出发地目的地之间距离,直线距离
     */
    @Column(name = "distance")
    private Integer distanceValue;

    /**
     * 发货日期
     */
    @Column(name = "pub_goods_time")
    private Date pubGoodsTime;

    /**
     * 联系人电话3
     */
    private String tel3;

    /**
     * 联系人电话4
     */
    private String tel4;

    /**
     * 显示类型 0不显示  1显示
     */
    @Column(name = "display_type")
    private String displayType;

    /**
     * hash_code
     */
    @Column(name = "hash_code")
    private String hashCode;

    /**
     * 是否完善了车的信息 车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
     */
    @Column(name = "is_car")
    private String isCar;

    /**
     * 用户类型0试用 1付费 2未激活
     */
    @Column(name = "user_type")
    private Integer userType;

    @Column(name = "pc_old_content")
    private String pcOldContent;

    /**
     * 重发次数
     */
    @Column(name = "resend_counts")
    private Integer resendCounts = 0;

    /**
     * 照片认证标志0未认证1通过2认证中3认证失败
     */
    @Column(name = "verify_photo_sign")
    private Integer verifyPhotoSign;

    /**
     * 用户分数
     */
    @Column(name = "user_part")
    private Integer userPart;

    /**
     * 出发地城市
     */
    @Column(name = "start_city")
    private String startCity;

    /**
     * 重发后原信息ID
     */
    @Column(name = "src_msg_id")
    private Long srcMsgId;

    /**
     * 出发地省
     */
    @Column(name = "start_provinc")
    private String startProvinc;

    /**
     * 出发地区
     */
    @Column(name = "start_area")
    private String startArea;

    /**
     * 目的地省
     */
    @Column(name = "dest_provinc")
    private String destProvinc;

    /**
     * 目的地市
     */
    @Column(name = "dest_city")
    private String destCity;

    /**
     * 目的地区
     */
    @Column(name = "dest_area")
    private String destArea;

    /**
     * 客户端版本号
     */
    @Column(name = "client_version")
    private String clientVersion;

    /**
     * 是否收信息费货源   0是不需要1是需要
     */
    @Column(name = "is_info_fee")
    private String isInfoFee;

    /**
     * 信息费运单状态：0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
     */
    @Column(name = "info_status")
    private String infoStatus;

    /**
     * 运单号
     */
    @Column(name = "ts_order_no")
    private String tsOrderNo;

    /**
     * 第一次发布时间
     */
    @Column(name = "release_time")
    private Date releaseTime;

    /**
     * 发货人注册时间
     */
    @Column(name = "reg_time")
    private Date regTime;

    /**
     * 货物型号
     */
    private String type;

    /**
     * 货物品牌
     */
    private String brand;

    /**
     * 货物类型名称,如“装载机”，“挖掘机”
     */
    @Column(name = "good_type_name")
    private String goodTypeName;

    /**
     * 货物的台数，针对标准化的数据，该字段基本无用，如果有用请指明
     */
    @Column(name = "good_number")
    private Integer goodNumber;

    /**
     * 是否是标准化数据：0是，1不是
     */
    @Column(name = "is_standard")
    private Integer isStandard;

    /**
     * 匹配项的ID，针对标准化的数据
     */
    @Column(name = "match_item_id")
    private Integer matchItemId;

    /**
     * android两点距离
     */
    @Column(name = "android_distance")
    private Integer androidDistance;

    /**
     * IOS两点距离
     */
    @Column(name = "ios_distance")
    private Integer iosDistance;

    /**
     * 是否展示在找货列表 0不显示，1是显示
     */
    @Column(name = "is_display")
    private Integer isDisplay;

    /**
     * 参考长度，有好货使用
     */
    @Column(name = "refer_length")
    private Integer referLength;

    /**
     * 参考宽度，有好货使用
     */
    @Column(name = "refer_width")
    private Integer referWidth;

    /**
     * 参考高度，有好货使用
     */
    @Column(name = "refer_height")
    private Integer referHeight;

    /**
     * 参考重量，有好货使用
     */
    @Column(name = "refer_weight")
    private Integer referWeight;

    @Column(name = "change_time")
    private Date changeTime;

    /**
     * 车辆长度
     */
    @Column(name = "car_length")
    private String carLength;

    /**
     * 车辆类型
     */
    @Column(name = "car_type")
    private String carType;

    /**
     * 装货开始时间
     */
    @Column(name = "begin_loading_time")
    private Date beginLoadingTime;

    /**
     * 装车时间
     */
    @Column(name = "loading_time")
    private Date loadingTime;

    /**
     * 卸货开始时间
     */
    @Column(name = "begin_unload_time")
    private Date beginUnloadTime;

    /**
     * 卸车时间
     */
    @Column(name = "unload_time")
    private Date unloadTime;

    /**
     * 车辆最低长度，单位米
     */
    @Column(name = "car_min_length")
    private BigDecimal carMinLength;

    /**
     * 车辆最大长度，单位米
     */
    @Column(name = "car_max_length")
    private BigDecimal carMaxLength;

    /**
     * 挂车样式
     */
    @Column(name = "car_style")
    private String carStyle;

    /**
     * 工作面高最小值，单位米
     */
    @Column(name = "work_plane_min_high")
    private BigDecimal workPlaneMinHigh;

    /**
     * 工作面高最大值，单位米
     */
    @Column(name = "work_plane_max_high")
    private BigDecimal workPlaneMaxHigh;

    /**
     * 工作面长最小值，单位米
     */
    @Column(name = "work_plane_min_length")
    private BigDecimal workPlaneMinLength;

    /**
     * 工作面长最大值，单位米
     */
    @Column(name = "work_plane_max_length")
    private BigDecimal workPlaneMaxLength;

    /**
     * 是否需要爬梯
     */
    private String climb;

    /**
     * 订单量，貌似无用了@@
     */
    @Column(name = "order_number")
    private Integer orderNumber;

    /**
     * 好评度，貌似无用了@@
     */
    private Integer evaluate;

    /**
     * 特殊要求
     */
    @Column(name = "special_required")
    private String specialRequired;

    /**
     * 相似编码
     */
    @Column(name = "similarity_code")
    private String similarityCode;

    /**
     * 相似货源首发ID
     */
    @Column(name = "similarity_first_id")
    private Long similarityFirstId;

    /**
     * 相似货源首发信息
     */
    @Column(name = "similarity_first_info")
    private String similarityFirstInfo;

    /**
     * 轮胎外露标识 1 是 0 否
     */
    @Column(name = "tyre_exposed_flag")
    private String tyreExposedFlag;

    /**
     * 所需车辆长度标签
     */
    @Column(name = "car_length_labels")
    private String carLengthLabels;

    /**
     * 调车数量
     */
    @Column(name = "shunting_quantity")
    private Integer shuntingQuantity;

    /**
     * 首发货源类型（电议1，一口价2）
     */
    @Column(name = "first_publish_type")
    private Integer firstPublishType;

    /**
     * 货源类型（电议1，一口价2）
     */
    @Column(name = "publish_type")
    private Integer publishType;

    /**
     * 信息费（元）
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    @Column(name = "info_fee")
    private BigDecimal infoFee;

    /**
     * 是否是 17.5 米专享 0：否 1：是
     */
    @Column(name = "exclusive_type")
    private Integer exclusiveType;

    /**
     * 是否删除 0:未删除 1:已删除
     */
    @Column(name = "is_delete")
    private Short isDelete;

    /**
     * 信用分
     */
    @Column(name = "total_score")
    private BigDecimal totalScore;

    /**
     * 信用分等级 "1 2 3 4 5"
     */
    @Column(name = "rank_level")
    private Integer rankLevel;

    /**
     * 是否显示在找货大厅（企业货源）0.不显示 1.显示
     */
    @Column(name = "is_show")
    private Integer isShow = 1;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    @Column(name = "refund_flag")
    private Integer refundFlag;

    /**
     * 货源来源（1货主；2调度客服；3个人货主;4:运满满货源）
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 用户发布货源成交个数(来自user_sub表的deal_num)
     */
    @Column(name = "trade_num")
    private Integer tradeNum;

    /**
     * 官方授权昵称
     */
    @Column(name = "auth_name")
    private String authName;

    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    @Column(name = "label_json")
    private String labelJson;

    /**
     * 保障货源（1是；0否；）
     */
    @Column(name = "guarantee_goods")
    private Integer guaranteeGoods;

    /**
     * 是否有信用曝光权限（0没有；1有（未点击）；2有（已点击）；）
     */
    @Column(name = "credit_retop")
    private Integer creditRetop;

    /**
     * 排序类型（0默认，1沉底）
     */
    @Column(name = "sort_type")
    private Integer sortType;

    /**
     * 优推好车主到期时间
     */
    @Column(name = "priority_recommend_expire_time")
    private Date priorityRecommendExpireTime;

    /**
     * 是否是优车货源（0否，1是）
     */
    @Column(name = "excellent_goods")
    private Integer excellentGoods;

    /**
     * 是否优车2.0货源：1-否，2-是
     */
    @Column(name = "excellent_goods_two")
    private Integer excellentGoodsTwo;

    /**
     * 发货方式：0-普通找车，10-用户出价，20-特惠优车，21-快速优车，22-极速优车，30-专车
     */
    @Column(name = "publish_goods_type")
    private Integer publishGoodsType;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    @Column(name = "driver_driving")
    private Integer driverDriving;

    /**
     * 装货联系电话
     */
    @Column(name = "load_cell_phone")
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    @Column(name = "unload_cell_phone")
    private String unloadCellPhone;

    /**
     * 签约合作商ID
     */
    @Column(name = "cargo_owner_id")
    private Long cargoOwnerId;

    /**
     * 技术服务费
     */
    @Column(name = "tec_service_fee")
    private BigDecimal tecServiceFee;

    /**
     * 标准货名备注
     */
    @Column(name = "machine_remark")
    private String machineRemark;

    /**
     * 优车发货卡id
     */
    @Column(name = "excellent_card_id")
    private Long excellentCardId;

    /**
     * 是否开票货源 0：否；1：是
     */
    @Column(name = "invoice_transport")
    private Integer invoiceTransport;

    /**
     * 附加运费
     */
    @Column(name = "additional_price")
    private String additionalPrice;

    /**
     * 企业税率
     */
    @Column(name = "enterprise_tax_rate")
    private BigDecimal enterpriseTaxRate;



    /* ============================== 辅助字段，旧代码将数据库字段做了别名 ============================== */
    @Transient
    private String startCoordX;// '出发地坐标x',
    @Transient
    private String startCoordY;// '出发地坐标y',
    @Transient
    private String destCoordX;// '目的地坐标x',
    @Transient
    private String destCoordY;// '目的地坐标y',
    @Transient
    private String startLatitude;// '出发地纬度',
    @Transient
    private String startLongitude;// '出发地经度',
    @Transient
    private String destLongitude;// '目的地经度',
    @Transient
    private String destLatitude;// '目的地纬度',
    @Transient
    private String distance;// '出发地目的地之间距离', 直线距离

    /* 辅助 */
    public String getStartCoordX() {
        return CityUtil.toCoordStr(startCoordXValue);
    }

    public String getStartCoordY() {
        return CityUtil.toCoordStr(startCoordYValue);
    }

    public String getDestCoordX() {
        return CityUtil.toCoordStr(destCoordXValue);
    }

    public String getDestCoordY() {
        return CityUtil.toCoordStr(destCoordYValue);
    }

    /**
     * 注意：旧的代码在 transport 转经纬度时，有问题
     *
     * @return
     */
    public String getStartLatitude() {
        return CityUtil.toMapPointStr(startLatitudeValue);
    }

    public String getStartLongitude() {
        return CityUtil.toMapPointStr(startLongitudeValue);
    }

    public String getDestLongitude() {
        return CityUtil.toMapPointStr(destLongitudeValue);
    }

    public String getDestLatitude() {
        return CityUtil.toMapPointStr(destLatitudeValue);
    }

    public String getDistance() {
        return CityUtil.toDistanceStr(distanceValue);
    }

}
