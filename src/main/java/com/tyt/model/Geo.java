package com.tyt.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
@Entity
@Table(name="tyt_geo")
public class Geo  implements Serializable {
	  /**
	 * 
	 */
	private static final long serialVersionUID = -7146664182729197320L;
	private Long id;
	  /**
	   * 父级ID
	   */
	  private long pid;
	  /**
	   * 名称
	   */
	  private String name;
	  /**
	   * 别名
	   */
	  private String alias;
	  /**
	   * 拼音简写
	   */
	  private String rf;
	  /**
	   * 层级：省市县镇
	   */
	  private int type;
	  
	  public static final int TYPE_PRO = 1;
	  public static final int TYPE_CITY = 2;
	  public static final int TYPE_COUNTRY = 3;
	  public static final int TYPE_TOWN = 4;
	  /**
	   * 平面坐标x
	   */
	  private double px;
	  /**
	   * 平面坐标y
	   */
	  private double py;
	  
	  @Id
	  @GeneratedValue
	  @Column(name = "id", unique = true, nullable = false)
	  public Long getId() {
		  return id;
	  }

	@Column(name="pid")  
	public long getPid() {
		return pid;
	}

	public void setPid(long pid) {
		this.pid = pid;
	}
	
	@Column(name="name")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name="alias")
	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}
	
	@Column(name="rf")
	public String getRf() {
		return rf;
	}

	public void setRf(String rf) {
		this.rf = rf;
	}
	
	@Column(name="type")
	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}
	
	@Column(name="px")
	public double getPx() {
		return px;
	}

	public void setPx(double px) {
		this.px = px;
	}
	
	@Column(name="py")
	public double getPy() {
		return py;
	}

	public void setPy(double py) {
		this.py = py;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Geo(String name, String rf, int type) {
		super();
		this.name = name;
		this.rf = rf;
		this.type = type;
		
	}

	
	
	public Geo() {
		super();
	}

	@Override
	public String toString() {
		return "Geo [id=" + id + ", pid=" + pid + ", name=" + name + ", alias="
				+ alias + ", rf=" + rf + ", type=" + type + ", px=" + px
				+ ", py=" + py + "]";
	}
	  
	
	
	
	  
}
