package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 用户实名认证表，平台、之前的三方、安证通
 * <AUTHOR>
 *
 */

@Entity
@Table(name="user_identity_cert")
public class UserIdentityCert implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7937678987573504894L;
	private Long id;
	/**
	 * 用户ID
	 */
	private Long userId;
	/**
	 * 真实姓名
	 */
	private String userName;
	/**
	 * 身份证号码
	 */
	private String idCard;
	/**
	 * 来源：1-平台、2-安证通、3-之前三方
	 */
	private Integer source;
	/**
	 * 创建时间
	 */
	private Date ctime;
	/**
	 * 修改时间
	 */
	private Date mtime;
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="user_name")
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	@Column(name="id_card")
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	@Column(name="source")
	public Integer getSource() {
		return source;
	}
	public void setSource(Integer source) {
		this.source = source;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name="mtime")
	public Date getMtime() {
		return mtime;
	}
	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	
	

}
