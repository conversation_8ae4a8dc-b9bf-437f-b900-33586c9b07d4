package com.tyt.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-3-9下午12:51:34
 * @description
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "tyt_user_call_phone_record", catalog = "tyt")
public class TytUserCallPhoneRecord implements java.io.Serializable {

	private Long id;
	private Long tsId;
	private Long userId;
	private Date ctime = new Date();
	private Long level;
	private String path;
	private String platId;
	
	public TytUserCallPhoneRecord() {
	}

	public TytUserCallPhoneRecord(Long tsId, Long userId, Date ctime) {
		this.tsId = tsId;
		this.userId = userId;
		this.ctime = ctime;
	}

	public TytUserCallPhoneRecord(Long tsId, Long userId) {
		this.tsId = tsId;
		this.userId = userId;
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = "level")
	public Long getLevel() {
		return level;
	}

	public void setLevel(Long level) {
		this.level = level;
	}

	@Column(name = "ts_id")
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

    public String getPath() {
        return path;
    }

    @Column(name = "path")
    public void setPath(String path) {
        this.path = path;
    }

    @Column(name = "plat_id")
    public String getPlatId() {
        return platId;
    }

    public void setPlatId(String platId) {
        this.platId = platId;
    }

    @Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
