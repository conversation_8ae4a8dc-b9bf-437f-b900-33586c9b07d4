package com.tyt.model;

import java.io.Serializable;

public class UserPermissionResult implements Serializable {

    private static final long serialVersionUID = -7782773634637401327L;
    private int isUserNamePower=0; //是否有查看用户名的权益 1有 0没有
    private int isContentPower=0; //是否有查看货物内同的权益 1有 0没有

    public int getIsUserNamePower() {
        return isUserNamePower;
    }

    public void setIsUserNamePower(int isUserNamePower) {
        this.isUserNamePower = isUserNamePower;
    }

    public int getIsContentPower() {
        return isContentPower;
    }

    public void setIsContentPower(int isContentPower) {
        this.isContentPower = isContentPower;
    }

    @Override
    public String toString() {
        return "UserPermissionResult{" +
                "isUserNamePower=" + isUserNamePower +
                ", isContentPower=" + isContentPower +
                '}';
    }
}
