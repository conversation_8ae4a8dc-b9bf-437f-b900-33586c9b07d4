package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * TytTransportWaybillEx entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_transport_waybill_ex_voucher")
public class TytTransportWaybillExVoucher implements java.io.Serializable {

	private Long id;
	private Long userId;
	private String proofReason;
	private String pictureVouchers;
	private String exParty;
	private Date ctime;
	private Date mtime;
	private Long tsId;
	private Long orderId;
	private Long exId;
	//1 异常上报图片及原因 2 补充凭证
	private Long voucherType;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "proof_reason")
	public String getProofReason() {
		return proofReason;
	}

	public void setProofReason(String proofReason) {
		this.proofReason = proofReason;
	}
	@Column(name = "picture_vouchers")
	public String getPictureVouchers() {
		return pictureVouchers;
	}

	public void setPictureVouchers(String pictureVouchers) {
		this.pictureVouchers = pictureVouchers;
	}
	@Column(name = "ex_party")
	public String getExParty() {
		return exParty;
	}

	public void setExParty(String exParty) {
		this.exParty = exParty;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name = "ts_id")
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
    @Column(name = "order_id")
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
	@Column(name = "ex_id")
	public Long getExId() {
		return exId;
	}

	public void setExId(Long exId) {
		this.exId = exId;
	}

	@Column(name = "voucher_type")
	public Long getVoucherType() {
		return voucherType;
	}

	public void setVoucherType(Long voucherType) {
		this.voucherType = voucherType;
	}
}
