package com.tyt.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.*;
import java.util.Date;

/**
 * TytCarOwnerIntention entity. <AUTHOR> Persistence Tools
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "tyt_car_owner_intention", catalog = "tyt")
public class TytCarOwnerIntention implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 9081328664662772450L;
	private Long id;
	private Long srcMsgId;
	private Long userId;
	private String cellPhone;
	private Long carUserId;
	private String carPhone;
	private Date carRegTime;
	private Integer identityType;
	private String identityName;
	private Long carId;
	private Integer status;
	private Integer payAmount;
	private String payPhone;
	private Date lastLinkTime;
	private Date ctime;
	private Date utime;
	private String carUserName;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "src_msg_id")
	public Long getSrcMsgId() {
		return this.srcMsgId;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "cell_phone")
	public String getCellPhone() {
		return this.cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	@Column(name = "car_user_id")
	public Long getCarUserId() {
		return this.carUserId;
	}

	public void setCarUserId(Long carUserId) {
		this.carUserId = carUserId;
	}

	@Column(name = "car_phone")
	public String getCarPhone() {
		return this.carPhone;
	}

	public void setCarPhone(String carPhone) {
		this.carPhone = carPhone;
	}

	@Column(name = "car_reg_time")
	public Date getCarRegTime() {
		return this.carRegTime;
	}

	public void setCarRegTime(Date carRegTime) {
		this.carRegTime = carRegTime;
	}

	@Column(name = "identity_type")
	public Integer getIdentityType() {
		return this.identityType;
	}

	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

	@Column(name = "identity_name")
	public String getIdentityName() {
		return this.identityName;
	}

	public void setIdentityName(String identityName) {
		this.identityName = identityName;
	}

	@Column(name = "car_id")
	public Long getCarId() {
		return this.carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "pay_amount")
	public Integer getPayAmount() {
		return this.payAmount;
	}

	public void setPayAmount(Integer payAmount) {
		this.payAmount = payAmount;
	}

	@Column(name = "pay_phone")
	public String getPayPhone() {
		return this.payPhone;
	}

	public void setPayPhone(String payPhone) {
		this.payPhone = payPhone;
	}

	@Column(name = "last_link_time")
	public Date getLastLinkTime() {
		return this.lastLinkTime;
	}

	public void setLastLinkTime(Date lastLinkTime) {
		this.lastLinkTime = lastLinkTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name = "car_user_name")
	public String getCarUserName() {
		return carUserName;
	}

	public void setCarUserName(String carUserName) {
		this.carUserName = carUserName;
	}

}
