package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

@Entity
@Table(name="tyt_version")
public class Version implements Serializable{
 
	private static final long serialVersionUID = 1L;
	Long id;
	String pcVersion;
	String androidVersion;
	String iosVersion;
	String pcDownloadUrl;
	String androidDownloadUrl;
	String iosDownloadUrl;
	String pcShowContent;
	String androidShowContent;
	String iosShowContent;
	String iosIsAutoUpgrade;
	String androidIsAutoUpgrade;
	String pcIsAutoUpgrade="1";
	Integer status;
	String desc;
	Date createTime;
	Date updateTime;
	
	
	@Id
	@GeneratedValue
	@Column(name="id",nullable=false,unique=true)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="pc_version")
	public String getPcVersion() {
		return pcVersion;
	}
	public void setPcVersion(String pcVersion) {
		this.pcVersion = pcVersion;
	}
	@Column(name="android_version")
	public String getAndroidVersion() {
		return androidVersion;
	}
	public void setAndroidVersion(String androidVersion) {
		this.androidVersion = androidVersion;
	}
	@Column(name="ios_version")
	public String getIosVersion() {
		return iosVersion;
	}
	public void setIosVersion(String iosVersion) {
		this.iosVersion = iosVersion;
	}
	@Column(name="pc_download_url")
	public String getPcDownloadUrl() {
		return pcDownloadUrl;
	}
	public void setPcDownloadUrl(String pcDownloadUrl) {
		this.pcDownloadUrl = pcDownloadUrl;
	}
	@Column(name="android_download_url")
	public String getAndroidDownloadUrl() {
		return androidDownloadUrl;
	}
	public void setAndroidDownloadUrl(String androidDownloadUrl) {
		this.androidDownloadUrl = androidDownloadUrl;
	}
	@Column(name="ios_download_url")
	public String getIosDownloadUrl() {
		return iosDownloadUrl;
	}
	public void setIosDownloadUrl(String iosDownloadUrl) {
		this.iosDownloadUrl = iosDownloadUrl;
	}
	@Column(name="pc_show_content")
	public String getPcShowContent() {
		return pcShowContent;
	}
	public void setPcShowContent(String pcShowContent) {
		this.pcShowContent = pcShowContent;
	}
	@Column(name="android_show_content")
	public String getAndroidShowContent() {
		return androidShowContent;
	}
	public void setAndroidShowContent(String androidShowContent) {
		this.androidShowContent = androidShowContent;
	}
	@Column(name="ios_show_content")
	public String getIosShowContent() {
		return iosShowContent;
	}
	public void setIosShowContent(String iosShowContent) {
		this.iosShowContent = iosShowContent;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="remark")
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
	@Column(name="ios_is_auto_upgrade")
	public String getIosIsAutoUpgrade() {
		return iosIsAutoUpgrade;
	}
	public void setIosIsAutoUpgrade(String iosIsAutoUpgrade) {
		this.iosIsAutoUpgrade = iosIsAutoUpgrade;
	}
	@Column(name="android_is_auto_upgrade")
	public String getAndroidIsAutoUpgrade() {
		return androidIsAutoUpgrade;
	}
	public void setAndroidIsAutoUpgrade(String androidIsAutoUpgrade) {
		this.androidIsAutoUpgrade = androidIsAutoUpgrade;
	}
	@Column(name="pc_is_auto_upgrade")
	public String getPcIsAutoUpgrade() {
		return pcIsAutoUpgrade;
	}
	public void setPcIsAutoUpgrade(String pcIsAutoUpgrade) {
		this.pcIsAutoUpgrade = pcIsAutoUpgrade;
	}
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name="update_time")
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
}
