package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name="blacklist_user")
public class BlacklistUser implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5285084508702789237L;
	private Long id;
	private Long userId; //用户id
	private String userName; //用户名称
	private String idcard; //身份证号
	private String cellPhone; //手机号
	private Integer status; //黑名单状态 0-否    1-是
	/**
	 * 是否永久（0-否；1-是；）
	 */
	private Integer perpetual;
	/**
	 * 拉黑天数
	 */
	private Integer restrictNum;
	/**
	 * 拉黑开始时间
	 */
	private Date restrictStartTime;
	/**
	 * 拉黑结束时间
	 */
	private Date restrictEndTime;
	private Integer cause; //加入理由,参考tyt_source 配置中
	private String extCause; //加入黑名单补充理由
	private String rmCause; //移除黑名单理由
	private Date limitEndTime; // 发货限制解除时间
	private Long operaUserId; //操作人Id
	private String operaUserName; //操作人名称
	private Date ctime; //创建时间
	private Date mtime; //更新时间
	/**
	 * 找货限制原因,参考tyt_source 配置中
	 */
	private Integer carCause;
	/**
	 * 发货限制开始时间
	 */
	private Date limitStartTime;
	/**
	 * 找货限制开始时间
	 */
	private Date carLimitStartTime;
	/**
	 * 找货限制结束时间
	 */
	private Date carLimitEndTime;
	/**
	 * 找货限制分钟数
	 */
	private Integer carLimitMinutes;
	/**
	 * 发货限制分钟数
	 */
	private Integer limitMinutes;
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="user_name")
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	@Column(name="idcard")
	public String getIdcard() {
		return idcard;
	}
	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="perpetual")
	public Integer getPerpetual() {
		return perpetual;
	}

	public void setPerpetual(Integer perpetual) {
		this.perpetual = perpetual;
	}
	@Column(name="restrict_num")
	public Integer getRestrictNum() {
		return restrictNum;
	}

	public void setRestrictNum(Integer restrictNum) {
		this.restrictNum = restrictNum;
	}
	@Column(name="restrict_start_time")
	public Date getRestrictStartTime() {
		return restrictStartTime;
	}

	public void setRestrictStartTime(Date restrictStartTime) {
		this.restrictStartTime = restrictStartTime;
	}
	@Column(name="restrict_end_time")
	public Date getRestrictEndTime() {
		return restrictEndTime;
	}

	public void setRestrictEndTime(Date restrictEndTime) {
		this.restrictEndTime = restrictEndTime;
	}

	@Column(name="cause")
	public Integer getCause() {
		return cause;
	}
	public void setCause(Integer cause) {
		this.cause = cause;
	}
	@Column(name="ext_cause")
	public String getExtCause() {
		return extCause;
	}
	public void setExtCause(String extCause) {
		this.extCause = extCause;
	}
	@Column(name="rm_cause")
	public String getRmCause() {
		return rmCause;
	}
	public void setRmCause(String rmCause) {
		this.rmCause = rmCause;
	}
	@Column(name="opera_user_id")
	public Long getOperaUserId() {
		return operaUserId;
	}
	public void setOperaUserId(Long operaUserId) {
		this.operaUserId = operaUserId;
	}
	@Column(name="opera_user_name")
	public String getOperaUserName() {
		return operaUserName;
	}
	public void setOperaUserName(String operaUserName) {
		this.operaUserName = operaUserName;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name="mtime")
	public Date getMtime() {
		return mtime;
	}
	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}
	@Column(name="limit_end_time")
	public Date getLimitEndTime() {
		return limitEndTime;
	}

	public void setLimitEndTime(Date limitEndTime) {
		this.limitEndTime = limitEndTime;
	}
	@Column(name="car_cause")
	public Integer getCarCause() {
		return carCause;
	}

	public void setCarCause(Integer carCause) {
		this.carCause = carCause;
	}
	@Column(name="limit_start_time")
	public Date getLimitStartTime() {
		return limitStartTime;
	}

	public void setLimitStartTime(Date limitStartTime) {
		this.limitStartTime = limitStartTime;
	}
	@Column(name="car_limit_start_time")
	public Date getCarLimitStartTime() {
		return carLimitStartTime;
	}

	public void setCarLimitStartTime(Date carLimitStartTime) {
		this.carLimitStartTime = carLimitStartTime;
	}
	@Column(name="car_limit_end_time")
	public Date getCarLimitEndTime() {
		return carLimitEndTime;
	}

	public void setCarLimitEndTime(Date carLimitEndTime) {
		this.carLimitEndTime = carLimitEndTime;
	}
	@Column(name="car_limit_minutes")
	public Integer getCarLimitMinutes() {
		return carLimitMinutes;
	}

	public void setCarLimitMinutes(Integer carLimitMinutes) {
		this.carLimitMinutes = carLimitMinutes;
	}
	@Column(name="limit_minutes")
	public Integer getLimitMinutes() {
		return limitMinutes;
	}

	public void setLimitMinutes(Integer limitMinutes) {
		this.limitMinutes = limitMinutes;
	}

	@Override
	public String toString() {
		return "BlacklistUser{" +
				"id=" + id +
				", userId=" + userId +
				", userName='" + userName + '\'' +
				", idcard='" + idcard + '\'' +
				", cellPhone='" + cellPhone + '\'' +
				", status=" + status +
				", cause=" + cause +
				", extCause='" + extCause + '\'' +
				", rmCause='" + rmCause + '\'' +
				", limitEndTime=" + limitEndTime +
				", operaUserId=" + operaUserId +
				", operaUserName='" + operaUserName + '\'' +
				", ctime=" + ctime +
				", mtime=" + mtime +
				'}';
	}
}
