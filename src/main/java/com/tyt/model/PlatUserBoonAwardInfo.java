package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 回馈用户活动model
 * <AUTHOR>
 *
 */
@Entity
@Table(name="plat_user_boon_award_info")
public class PlatUserBoonAwardInfo implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3709489405294913207L;


	private Long id;//bigint(20) NOT NULL
	private Long userId;//bigint(20) NULL用户ID
	private Integer awardDays;//int(11) NULL会员奖励天数，非会员为0
	private Integer userState;//int(11) NULL1：非会员 2：会员
	private Date ctime;//datetime NULL0
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="award_days")
	public Integer getAwardDays() {
		return awardDays;
	}
	public void setAwardDays(Integer awardDays) {
		this.awardDays = awardDays;
	}
	@Column(name="user_state")
	public Integer getUserState() {
		return userState;
	}
	public void setUserState(Integer userState) {
		this.userState = userState;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	
	
}
