package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

@Entity
@Table(name="tyt_log")
public class OpLog implements Serializable {
	
	private static final long serialVersionUID = 4794588810798707801L;

	private Long id;
	private Long userId;
	private Integer opType;
	private Timestamp opTime;
	private String opContent;
	private String ip;
	private Integer platId;
	private String version;
	private String cellPhone;
	private String ticket;
	private String pcSign;
	
	public static final Integer OP_CLIENT_REG = 100;
	public static final Integer OP_CLIENT_LOGIN = 101;
	public static final Integer OP_CLIENT_USER_EDIT = 102;
	public static final Integer OP_CLIENT_PUBLISH = 103;
	public static final Integer OP_CLIENT_INFO_EDIT = 104;
	public static final Integer OP_CLIENT_USER_UPDATE_PASSWORD = 105;
	
	public static final Integer OP_WEBSITE_LOGIN = 200;
	public static final Integer OP_WEBSITE_LOGOUT = 201;
	public static final Integer OP_WEBSITE_USER_EDIT = 202;
	public static final Integer OP_WEBSITE_USER_PAY = 203; //用户收费操作
	public static final Integer OP_WEBSITE_USER_RENEWAL = 204; //用户续费操作

    public OpLog() {
    	this.opTime = new Timestamp(System.currentTimeMillis());
    }
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="op_type")
	public Integer getOpType() {
		return opType;
	}

	public void setOpType(Integer opType) {
		this.opType = opType;
	}
	
	@Column(name="op_time")
	public Timestamp getOpTime() {
		return opTime;
	}

	public void setOpTime(Timestamp opTime) {
		this.opTime = opTime;
	}

	@Column(name="op_content")
	public String getOpContent() {
		return opContent;
	}

	public void setOpContent(String opContent) {
		this.opContent = opContent;
	}

	@Column(name="ip")
	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}

	public void setPlatId(Integer platId) {
		this.platId = platId;
	}

	@Column(name="version")
	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="ticket")
	public String getTicket() {
		return ticket;
	}

	public void setTicket(String ticket) {
		this.ticket = ticket;
	}
	@Column(name="pc_sign")
	public String getPcSign() {
		return pcSign;
	}

	public void setPcSign(String pcSign) {
		this.pcSign = pcSign;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

}
