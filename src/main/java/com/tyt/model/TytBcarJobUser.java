package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytBcarJobUser entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_bcar_job_user")
public class TytBcarJobUser implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2627506181248021572L;
	// Fields

	private Long id;
	private Long brId;
	private Long userId;
	private Integer status;
	private Date ctime;
	private Date utime;


	public TytBcarJobUser(Long brId, Long userId, Integer status) {
		super();
		this.brId = brId;
		this.userId = userId;
		this.status = status;
		this.ctime=new Date();
		this.utime=new Date();
	}

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "br_id")
	public Long getBrId() {
		return this.brId;
	}

	public void setBrId(Long brId) {
		this.brId = brId;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime")
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

}