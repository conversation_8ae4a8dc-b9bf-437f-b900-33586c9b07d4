package com.tyt.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytMapDict entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_map_dict")
public class TytMapDict implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 670135567339954220L;
	private Long id;
	private String mapKey;
	private Integer startCoordX;
	private Integer startCoordY;
	private Integer destCoordX;
	private Integer destCoordY;
	private Integer startLongitude;
	private Integer startLatitude;
	private Integer destLongitude;
	private Integer destLatitude;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private Integer distance;
	private Date ctime;
	private Integer iosDistance;
	private Integer straightDistance;
	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "map_key", unique = true, length = 32)
	public String getMapKey() {
		return this.mapKey;
	}

	public void setMapKey(String mapKey) {
		this.mapKey = mapKey;
	}

	@Column(name = "start_coord_x")
	public Integer getStartCoordX() {
		return this.startCoordX;
	}

	public void setStartCoordX(Integer startCoordX) {
		this.startCoordX = startCoordX;
	}

	@Column(name = "start_coord_y")
	public Integer getStartCoordY() {
		return this.startCoordY;
	}

	public void setStartCoordY(Integer startCoordY) {
		this.startCoordY = startCoordY;
	}

	@Column(name = "dest_coord_x")
	public Integer getDestCoordX() {
		return this.destCoordX;
	}

	public void setDestCoordX(Integer destCoordX) {
		this.destCoordX = destCoordX;
	}

	@Column(name = "dest_coord_y")
	public Integer getDestCoordY() {
		return this.destCoordY;
	}

	public void setDestCoordY(Integer destCoordY) {
		this.destCoordY = destCoordY;
	}

	@Column(name = "start_longitude")
	public Integer getStartLongitude() {
		return this.startLongitude;
	}

	public void setStartLongitude(Integer startLongitude) {
		this.startLongitude = startLongitude;
	}

	@Column(name = "start_latitude")
	public Integer getStartLatitude() {
		return this.startLatitude;
	}

	public void setStartLatitude(Integer startLatitude) {
		this.startLatitude = startLatitude;
	}

	@Column(name = "dest_longitude")
	public Integer getDestLongitude() {
		return this.destLongitude;
	}

	public void setDestLongitude(Integer destLongitude) {
		this.destLongitude = destLongitude;
	}

	@Column(name = "dest_latitude")
	public Integer getDestLatitude() {
		return this.destLatitude;
	}

	public void setDestLatitude(Integer destLatitude) {
		this.destLatitude = destLatitude;
	}

	@Column(name = "start_provinc", length = 50)
	public String getStartProvinc() {
		return this.startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	@Column(name = "start_city", length = 50)
	public String getStartCity() {
		return this.startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	@Column(name = "start_area", length = 50)
	public String getStartArea() {
		return this.startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	@Column(name = "dest_provinc", length = 50)
	public String getDestProvinc() {
		return this.destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	@Column(name = "dest_city", length = 50)
	public String getDestCity() {
		return this.destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	@Column(name = "dest_area", length = 50)
	public String getDestArea() {
		return this.destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "distance")
	public Integer getDistance() {
		return this.distance;
	}

	public void setDistance(Integer distance) {
		this.distance = distance;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Column(name = "ios_distance")
	public Integer getIosDistance() {
		return iosDistance;
	}

	public void setIosDistance(Integer iosDistance) {
		this.iosDistance = iosDistance;
	}
	
	@Column(name = "straight_distance")
	public Integer getStraightDistance() {
		return straightDistance;
	}

	public void setStraightDistance(Integer straightDistance) {
		this.straightDistance = straightDistance;
	}

}