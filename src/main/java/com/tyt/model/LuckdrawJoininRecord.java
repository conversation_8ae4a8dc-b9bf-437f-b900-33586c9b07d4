package com.tyt.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName LuckdrawJoininRecord
 * @Description 抽奖活动参与记录对象
 * <AUTHOR>
 * @Date 2020-03-09 11:51
 * @Version 1.0
 */
@Entity
@Table(name = "luckdraw_joinin_record", schema = "tyt", catalog = "")
public class LuckdrawJoininRecord {
    private Long id;
    private Integer activityId;
    private String activityName;
    private Long userId;
    private String userName;
    private String cellphone;
    private Date ctime;

    @Id
    @GeneratedValue
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "activity_id")
    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    @Basic
    @Column(name = "activity_name")
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Basic
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic
    @Column(name = "user_name")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Basic
    @Column(name = "cellphone")
    public String getCellphone() {
        return cellphone;
    }

    public void setCellphone(String cellphone) {
        this.cellphone = cellphone;
    }

    @Basic
    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LuckdrawJoininRecord that = (LuckdrawJoininRecord) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(activityId, that.activityId) &&
                Objects.equals(activityName, that.activityName) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(userName, that.userName) &&
                Objects.equals(cellphone, that.cellphone) &&
                Objects.equals(ctime, that.ctime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, activityId, activityName, userId, userName, cellphone, ctime);
    }
}
