package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_employee")
public class Employee implements Serializable {

	private static final long serialVersionUID = -4970649159964127533L;
	private Long id;
	private String telName;// 联系人
	private Integer sex;// 性别
	private Integer age;// 年龄
	private Integer years;// 驾龄要求
	private String company;// 公司名称
	private Integer identity;// 身份
	private String title;// 标题
	private Integer salary;// 工资
	private Integer education;// 学历
	private Integer position;// 招聘职位‘
	private Integer duty;//职务
	

	private String province;// 省
	private String city;// 市
	private String county;// 县
	private String birthProvince;// 省
	private String birthCity;// 市
	private String birthCounty;// 县
	private String subsidy;// 福利
	private String telephone;// 联系电话
	private Integer distinguish;// 区分招聘者与求职者
	private String qq;// QQ
	private String pDescribe;// 职位描述
	private String sDescribe;// 薪资描述
	private Timestamp ctime;// 采集时间
	private Integer count;// 招聘人数
	private String self;// 自我简介
	private Integer status;// 状态
	private String cellPhone;// 发布电话
	private Integer saveDay;// 信息有效期
	private Integer topDay;// 信息至顶天数

    @Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="tel_name")
	public String getTelName() {
		return telName;
	}
	public void setTelName(String telName) {
		this.telName = telName;
	}
	@Column(name="sex")
	public Integer getSex() {
		return sex;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	@Column(name="age")
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	@Column(name="years")
	public Integer getYears() {
		return years;
	}
	public void setYears(Integer years) {
		this.years = years;
	}
	@Column(name="company")
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	@Column(name="identity")
	public Integer getIdentity() {
		return identity;
	}
	public void setIdentity(Integer identity) {
		this.identity = identity;
	}
	@Column(name="title")
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}
	@Column(name="salary")
	public Integer getSalary() {
		return salary;
	}
	public void setSalary(Integer salary) {
		this.salary = salary;
	}
	@Column(name="education")
	public Integer getEducation() {
		return education;
	}
	public void setEducation(Integer education) {
		this.education = education;
	}
	@Column(name="province")
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	@Column(name="city")
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	@Column(name="county")
	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}
	@Column(name="subsidy")
	public String getSubsidy() {
		return subsidy;
	}

	public void setSubsidy(String subsidy) {
		this.subsidy = subsidy;
	}
	@Column(name="telephone")
	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}
	@Column(name="distinguish")
	public Integer getDistinguish() {
		return distinguish;
	}

	public void setDistinguish(Integer distinguish) {
		this.distinguish = distinguish;
	}
	@Column(name="qq")
	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}
	@Column(name="pDescribe")
	public String getpDescribe() {
		return pDescribe;
	}

	public void setpDescribe(String pDescribe) {
		this.pDescribe = pDescribe;
	}
	@Column(name="sDescribe")
	public String getsDescribe() {
		return sDescribe;
	}

	public void setsDescribe(String sDescribe) {
		this.sDescribe = sDescribe;
	}
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}

	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	@Column(name="count")
	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}
	@Column(name="self")
	public String getSelf() {
		return self;
	}

	public void setSelf(String self) {
		this.self = self;
	}
	@Column(name="emp_status")
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="save_day")
	public Integer getSaveDay() {
		return saveDay;
	}
	public void setSaveDay(Integer saveDay) {
		this.saveDay = saveDay;
	}
	@Column(name="top_day")
	public Integer getTopDay() {
		return topDay;
	}
	public void setTopDay(Integer topDay) {
		this.topDay = topDay;
	}
	@Column(name="birth_province")
	public String getBirthProvince() {
		return birthProvince;
	}

	public void setBirthProvince(String birthProvince) {
		this.birthProvince = birthProvince;
	}
	@Column(name="birth_city")
	public String getBirthCity() {
		return birthCity;
	}

	public void setBirthCity(String birthCity) {
		this.birthCity = birthCity;
	}
	@Column(name="birth_county")
	public String getBirthCounty() {
		return birthCounty;
	}

	public void setBirthCounty(String birthCounty) {
		this.birthCounty = birthCounty;
	}

	@Column(name="position")
	public Integer getPosition() {
		return position;
	}

	public void setPosition(Integer position) {
		this.position = position;
	}
	@Column(name="duty")
	public Integer getDuty() {
		return duty;
	}
	public void setDuty(Integer duty) {
		this.duty = duty;
	}
	

}
