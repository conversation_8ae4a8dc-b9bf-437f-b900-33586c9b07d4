package com.tyt.model;

import java.io.Serializable;

/**
 * 用于分页查询的PageBean 对象
 * <p>Default Values</p>
 * <li>当前页数 currentPage: 1</li>
 * <li>总共的数据的行数 rowCount: 0</li>
 * <li>共计多少页 maxPage: 0</li>
 * <li>页面的大小 pageSize: 5</li>
 */
public class PageBean implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private int currentPage = 1;// 当前页数

    private long rowCount=0; // 总共的数据的行数

    private long maxPage=0; // 共计多少页

    private int pageSize = 5;// 页面的大小,默认为一页显示5条数据

    /**
     * 无参构造函数
     *
     */
    public PageBean() {
    }

    /**
     *
     * @param currentPage 当前页数
     * @param pageSize 页面的大小,默认为一页显示5条数据
     */
    public PageBean(int currentPage,int pageSize){
        setCurrentPage(currentPage);
        setPageSize(pageSize);
    }

    /**
     * 构造函数
     * @param currentPage  当前页数
     * @param rowCount 总共的数据的行数
     * @param pageSize 页面的大小,默认为一页显示5条数据
     */
    public PageBean(int currentPage,int rowCount,int pageSize){
        setCurrentPage(currentPage);
        setRowCount(rowCount);
        setPageSize(pageSize);
    }
    /**
     * 得到当前页数
     * @return
     */
    public int getCurrentPage() {
        return currentPage;
    }
    /**
     * 设置当前的页数
     * @param currentPage
     */
    public void setCurrentPage(int currentPage) {
        if (currentPage <= 1) {
            this.currentPage = 1;
        } else {
            this.currentPage = currentPage;
        }
    }

    /**
     * 获得一共的多少页
     * @return
     */
    public long getMaxPage() {
        //如果没有数据
        if(rowCount==0){
            return 0;
        }else if(pageSize==0){
            pageSize=1;
        }else if(rowCount%pageSize==0){//如果刚好是一个整数页
            maxPage=rowCount/pageSize;
        }else{
            maxPage=rowCount/pageSize+1;
        }
        return maxPage;
    }

    /**
     * 获得当前一页显示的多少行数据
     * @return
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 设置第页显示多少行数据
     * @param pageSize
     */
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 得到总共有多少行数据
     * @return
     */
    public long getRowCount() {
        return rowCount;
    }
    /**
     * 设置查询到的总的行数
     * @param rowCount
     */
    public void setRowCount(long rowCount) {
        this.rowCount = rowCount;
    }
}
