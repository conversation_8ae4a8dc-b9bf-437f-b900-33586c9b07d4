package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * tyt_transport_waybill_ex_evaluate
 */
@Entity
@Table(name = "tyt_transport_waybill_ex_evaluate")
public class TytTransportWaybillExEvaluate implements java.io.Serializable {

	private Long id;
	private Long exId;
	private Long userId;
	private Integer evaluateIdentity;
	private Integer dealResultEvaluate;
	private Integer dealTimeEvaluate;
	private Integer serviceAttitudeEvaluate;
	/**
	 * 平台判责规则评价：1满意 2 一般 3不满意 4未告知
	 */
	private Integer decideResponsibilityEvaluate;
	private String remark;
	private Date ctime;
	private Date mtime;
	private Long recordId;

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "ex_id")
	public Long getExId() {
		return exId;
	}

	public void setExId(Long exId) {
		this.exId = exId;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "evaluate_identity")
	public Integer getEvaluateIdentity() {
		return evaluateIdentity;
	}

	public void setEvaluateIdentity(Integer evaluateIdentity) {
		this.evaluateIdentity = evaluateIdentity;
	}

	@Column(name = "deal_result_evaluate")
	public Integer getDealResultEvaluate() {
		return dealResultEvaluate;
	}

	public void setDealResultEvaluate(Integer dealResultEvaluate) {
		this.dealResultEvaluate = dealResultEvaluate;
	}

	@Column(name = "deal_time_evaluate")
	public Integer getDealTimeEvaluate() {
		return dealTimeEvaluate;
	}

	public void setDealTimeEvaluate(Integer dealTimeEvaluate) {
		this.dealTimeEvaluate = dealTimeEvaluate;
	}

	@Column(name = "service_attitude_evaluate")
	public Integer getServiceAttitudeEvaluate() {
		return serviceAttitudeEvaluate;
	}

	public void setServiceAttitudeEvaluate(Integer serviceAttitudeEvaluate) {
		this.serviceAttitudeEvaluate = serviceAttitudeEvaluate;
	}

	@Column(name = "decide_responsibility_evaluate")
	public Integer getDecideResponsibilityEvaluate() {
		return decideResponsibilityEvaluate;
	}

	public void setDecideResponsibilityEvaluate(Integer decideResponsibilityEvaluate) {
		this.decideResponsibilityEvaluate = decideResponsibilityEvaluate;
	}

	@Column(name = "remark")
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

    @Column(name = "record_id")
    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
}
