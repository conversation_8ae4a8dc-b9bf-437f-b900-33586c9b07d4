package com.tyt.model;


import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2022-01-25 11:16:52
 */
@Entity
@Table(name = "tyt_shielding_shipper")
public class ShieldingShipper implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@JsonIgnore
	private Long id;
	/**
	 * 用户 id
	 */
	@JsonIgnore
	private Long userId;
	/**
	 * 被屏蔽人 id
	 */
	private Long shieldingUserId;
	/**
	 * 是否删除 0 否，1 是
	 */
	@JsonIgnore
	private Integer isDelete;
	/**
	 * 创建人 id
	 */
	@JsonIgnore
	private Long createId;
	/**
	 * 创建时间
	 */
	@JsonIgnore
	private Date createTime;
	/**
	 * 修改人 id
	 */
	@JsonIgnore
	private Long updateId;
	/**
	 * 修改时间
	 */
	@JsonIgnore
	private Date updateTime;

	public ShieldingShipper(Long userId, Long shieldingUserId) {
		this.userId = userId;
		this.shieldingUserId = shieldingUserId;
		this.isDelete = 0;
		this.createId = userId;
		this.createTime = new Date();
	}

	public ShieldingShipper() {
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "shielding_user_id")
	public Long getShieldingUserId() {
		return shieldingUserId;
	}

	public void setShieldingUserId(Long shieldingUserId) {
		this.shieldingUserId = shieldingUserId;
	}

	@Column(name = "is_delete")
	public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	@Column(name = "create_id")
	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	@Column(name = "create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Column(name = "update_id")
	public Long getUpdateId() {
		return updateId;
	}


	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}

	@Column(name = "update_time")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}
