package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
@Entity
@Table(name="tyt_invite_friends")
public class InviteFriends  implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5822033487992619633L;
	Long id;
	Long userId;
	String friendCell;
	String friendName;
	Timestamp ctime;
	Integer counts;
	Integer platId;
	Integer friendType;//类别 0货主1货站2车主3司机
	String remark;//备注
	
	@Id
	@GeneratedValue
	@Column(name="id",unique=true,nullable=false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
    @Column(name="user_id")	
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="friend_cell")
	public String getFriendCell() {
		return friendCell;
	}
	public void setFriendCell(String friendCell) {
		this.friendCell = friendCell;
	}
	@Column(name="friend_name")
	public String getFriendName() {
		return friendName;
	}
	public void setFriendName(String friendName) {
		this.friendName = friendName;
	}
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	@Column(name="counts")
	public Integer getCounts() {
		return counts;
	}
	public void setCounts(Integer counts) {
		this.counts = counts;
	}
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}
	public void setPlatId(Integer platId) {
		this.platId = platId;
	}
	@Column(name="friend_type")
	public Integer getFriendType() {
		return friendType;
	}
	public void setFriendType(Integer friendType) {
		this.friendType = friendType;
	}
	public String getRemark() {
		return remark;
	}
	@Column(name="remark")
	public void setRemark(String remark) {
		this.remark = remark;
	}
	@Override
	public  String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
}
