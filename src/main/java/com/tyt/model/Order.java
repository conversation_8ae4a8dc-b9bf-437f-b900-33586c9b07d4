package com.tyt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

@Entity
@Table(name="tyt_old_order")
public class Order  implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3675329980168556928L;
	Long id;
	String orderId;
	String cellPhone;
	Integer payStatus;
	Integer renewalYears;
	String payMethod;
	String defaultBank;
	Integer status;
	Timestamp ctime;
	Timestamp mtime;
	Integer totalFee;
	Integer platId;
	Long userId;

	/**
	 * 渠道 1支付宝 2易宝
	 */
	private String payChannel;
	/**
	 * 第三方支付平台生成的订单号
	 */
	private String thirdpartyOrderSerialNum;

	public static final int ORDER_STATUS_NO_PAY=0;
	public static final int ORDER_STATUS_PAY_FAILURE=1;
	public static final int ORDER_STATUS_PAY_SUCCESS=2;
	public static final int ORDER_STATUS_SYNC_VERIFY_FAILURE=3;
	public static final int ORDER_STATUS_ASYNC_VERIFY_FAILURE=4;
	
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="order_id")
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	@Column(name="cell_phone")
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	@Column(name="pay_status")
	public Integer getPayStatus() {
		return payStatus;
	}
	public void setPayStatus(Integer payStatus) {
		this.payStatus = payStatus;
	}
	@Column(name="renewal_years")
	public Integer getRenewalYears() {
		return renewalYears;
	}
	public void setRenewalYears(Integer renewalYears) {
		this.renewalYears = renewalYears;
	}
	@Column(name="paymethod")
	public String getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="ctime")
	public Timestamp getCtime() {
		return ctime;
	}
	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}
	@Column(name="mtime")
	public Timestamp getMtime() {
		return mtime;
	}
	public void setMtime(Timestamp mtime) {
		this.mtime = mtime;
	}
	
	@Column(name="total_fee")
	public Integer getTotalFee() {
		return totalFee;
	}
	public void setTotalFee(Integer totalFee) {
		this.totalFee = totalFee;
	}
	@Column(name="defaultbank")
	public String getDefaultBank() {
		return defaultBank;
	}
	public void setDefaultBank(String defaultBank) {
		this.defaultBank = defaultBank;
	}
	@Column(name="plat_id")
	public Integer getPlatId() {
		return platId;
	}
	public void setPlatId(Integer platId) {
		this.platId = platId;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="pay_channel")
	public String getPayChannel() {
		return payChannel;
	}

	public void setPayChannel(String payChannel) {
		this.payChannel = payChannel;
	}
	@Column(name="thirdparty_order_serial_num")
	public String getThirdpartyOrderSerialNum() {
		return thirdpartyOrderSerialNum;
	}

	public void setThirdpartyOrderSerialNum(String thirdpartyOrderSerialNum) {
		this.thirdpartyOrderSerialNum = thirdpartyOrderSerialNum;
	}

	@Override
	public String toString() {
//		return "[id:"+id+",orderId:"+orderId+",cellPhone:"+cellPhone
//				+",payStatus:"+payStatus+",renewalYears:"+renewalYears
//				+",payMethod:"+payMethod+",defaultBank:"+defaultBank
//				+",status:"+status+",ctime:"
//				+ctime+",mtime:"+mtime+",totalFee:"+totalFee+
//				",platId:"+platId+"]";
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
	
	
}
