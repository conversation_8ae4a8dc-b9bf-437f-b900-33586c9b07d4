package com.tyt.model;

import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-8-7下午2:44:33
 * @description
 */
@Entity
@Table(name = "tyt_search_keyword", catalog = "tyt")
public class TytSearchKeyword implements java.io.Serializable {
	private static final long serialVersionUID = -4155859357348377107L;

	private Integer id;
	private String keyword;
	private Integer machineId;
	private String machineType;
	private Integer keywordType;

	public TytSearchKeyword() {
	}

	public TytSearchKeyword(String keyword, Integer machineId) {
		this.keyword = keyword;
		this.machineId = machineId;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "machine_type")
	public String getMachineType() {
		return machineType;
	}

	public void setMachineType(String machineType) {
		this.machineType = machineType;
	}

	@Column(name = "keyword_type")
	public Integer getKeywordType() {
		return keywordType;
	}

	public void setKeywordType(Integer keywordType) {
		this.keywordType = keywordType;
	}

	@Column(name = "keyword")
	public String getKeyword() {
		return this.keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	@Column(name = "machine_id")
	public Integer getMachineId() {
		return this.machineId;
	}

	public void setMachineId(Integer machineId) {
		this.machineId = machineId;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
