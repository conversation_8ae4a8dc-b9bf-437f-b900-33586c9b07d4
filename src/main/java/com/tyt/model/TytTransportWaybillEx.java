package com.tyt.model;

import javax.persistence.*;
import java.util.Date;

/**
 * TytTransportWaybillEx entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_transport_waybill_ex")
public class TytTransportWaybillEx implements java.io.Serializable {

	/**
	 * 订单类型：异常上报
	 */
	public static final int WAYBILL_EX_ORDER_TYPE_EX = 0;
	/**
	 * 订单类型：订单投诉
	 */
	public static final int WAYBILL_EX_ORDER_TYPE_COMPLAINT = 1;

	/**
	 * 
	 */
	private static final long serialVersionUID = 4907494822306657304L;
	private Long id;
	private String startPoint;
	private String destPoint;
	private String taskContent;
	private String tel;
	private String pubTime;
	private Date ctime;
	private String uploadCellphone;
	private Long userId;
	private String linkman;
	private String tel3;
	private String tel4;
	private String tsOrderNo;
	private Long payUserId;
	private String payCellPhone;
	private String payLinkPhone;
	private Long payAmount;
	private Date exTime;
	private String exParty;
	private String exType;
	private String exOther;
	private String exStatus;
	private Date completeTime;
	private String resultOpinion;
	private Long carAmount;
	private Long goodsAmount;
	private Long actionUserId;
	private String actionCellPhone;
	private String actionName;
	private Date mtime;
	private Long tsId;
	private Long orderId;

	private String exTypeOther;
	private String exFaultParty;

	private Integer orderType;
	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "start_point")
	public String getStartPoint() {
		return this.startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	@Column(name = "dest_point")
	public String getDestPoint() {
		return this.destPoint;
	}

	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}

	@Column(name = "task_content")
	public String getTaskContent() {
		return this.taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	@Column(name = "tel")
	public String getTel() {
		return this.tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	@Column(name = "pub_time")
	public String getPubTime() {
		return this.pubTime;
	}

	public void setPubTime(String pubTime) {
		this.pubTime = pubTime;
	}

	@Column(name = "ctime")
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "upload_cellphone")
	public String getUploadCellphone() {
		return this.uploadCellphone;
	}

	public void setUploadCellphone(String uploadCellphone) {
		this.uploadCellphone = uploadCellphone;
	}

	@Column(name = "user_id")
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "linkman")
	public String getLinkman() {
		return this.linkman;
	}

	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}

	@Column(name = "tel3")
	public String getTel3() {
		return this.tel3;
	}

	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}

	@Column(name = "tel4")
	public String getTel4() {
		return this.tel4;
	}

	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}

	@Column(name = "ts_order_no")
	public String getTsOrderNo() {
		return this.tsOrderNo;
	}

	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}

	@Column(name = "pay_user_id")
	public Long getPayUserId() {
		return this.payUserId;
	}

	public void setPayUserId(Long payUserId) {
		this.payUserId = payUserId;
	}

	@Column(name = "pay_cell_phone")
	public String getPayCellPhone() {
		return this.payCellPhone;
	}

	public void setPayCellPhone(String payCellPhone) {
		this.payCellPhone = payCellPhone;
	}

	@Column(name = "pay_link_phone")
	public String getPayLinkPhone() {
		return this.payLinkPhone;
	}

	public void setPayLinkPhone(String payLinkPhone) {
		this.payLinkPhone = payLinkPhone;
	}

	@Column(name = "pay_amount")
	public Long getPayAmount() {
		return this.payAmount;
	}

	public void setPayAmount(Long payAmount) {
		this.payAmount = payAmount;
	}

	@Column(name = "ex_time")
	public Date getExTime() {
		return this.exTime;
	}

	public void setExTime(Date exTime) {
		this.exTime = exTime;
	}

	@Column(name = "ex_party")
	public String getExParty() {
		return this.exParty;
	}

	public void setExParty(String exParty) {
		this.exParty = exParty;
	}

	@Column(name = "ex_type")
	public String getExType() {
		return this.exType;
	}

	public void setExType(String exType) {
		this.exType = exType;
	}

	@Column(name = "ex_status")
	public String getExStatus() {
		return this.exStatus;
	}

	public void setExStatus(String exStatus) {
		this.exStatus = exStatus;
	}

	@Column(name = "complete_time")
	public Date getCompleteTime() {
		return this.completeTime;
	}

	public void setCompleteTime(Date completeTime) {
		this.completeTime = completeTime;
	}

	@Column(name = "result_opinion")
	public String getResultOpinion() {
		return this.resultOpinion;
	}

	public void setResultOpinion(String resultOpinion) {
		this.resultOpinion = resultOpinion;
	}

	@Column(name = "car_amount")
	public Long getCarAmount() {
		return this.carAmount;
	}

	public void setCarAmount(Long carAmount) {
		this.carAmount = carAmount;
	}

	@Column(name = "goods_amount")
	public Long getGoodsAmount() {
		return this.goodsAmount;
	}

	public void setGoodsAmount(Long goodsAmount) {
		this.goodsAmount = goodsAmount;
	}

	@Column(name = "action_user_id")
	public Long getActionUserId() {
		return this.actionUserId;
	}

	public void setActionUserId(Long actionUserId) {
		this.actionUserId = actionUserId;
	}

	@Column(name = "action_cell_phone")
	public String getActionCellPhone() {
		return this.actionCellPhone;
	}

	public void setActionCellPhone(String actionCellPhone) {
		this.actionCellPhone = actionCellPhone;
	}

	@Column(name = "action_name")
	public String getActionName() {
		return this.actionName;
	}

	public void setActionName(String actionName) {
		this.actionName = actionName;
	}

	@Column(name = "mtime")
	public Date getMtime() {
		return this.mtime;
	}

	public void setMtime(Date mtime) {
		this.mtime = mtime;
	}

	@Column(name = "ts_id")
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	@Column(name = "ex_other")
	public String getExOther() {
		return exOther;
	}

	public void setExOther(String exOther) {
		this.exOther = exOther;
	}
    @Column(name = "order_id")
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

	@Column(name = "ex_type_other")
	public String getExTypeOther() {
		return exTypeOther;
	}

	public void setExTypeOther(String exTypeOther) {
		this.exTypeOther = exTypeOther;
	}

	@Column(name = "ex_fault_party")
	public String getExFaultParty() {
		return exFaultParty;
	}

	public void setExFaultParty(String exFaultParty) {
		this.exFaultParty = exFaultParty;
	}

	@Column(name = "order_type")
	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}
}