package com.tyt.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tyt_notice_popup")
public class  TytNoticePopup implements Serializable {
    private static final long serialVersionUID = 8947831380537113790L;
    private Long id;
    private Integer type; //业务类型
    private String title;  //标题
    private String msgId;  //信息id
    private String content;  //弹窗内容
    private Long templId;  //模板id
    private Long productionId;  //通知人id
    private Long receiveId;  //被通知人id
    private Date receiveTime;  //通知时间
    private Integer receiveStatus;  //通知状态1.未发送 2.已发送
    private Integer status;  //1.正常，2.无效
    private Date ctime;
    private Integer originPopup;
    private Integer carPopup;
    private Integer goodsPopup;

    @Id
    @GeneratedValue
    @Column(name = "id", unique = true, nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "title")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Column(name = "msg_id")
    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    @Column(name = "content")
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Column(name = "templ_id")
    public Long getTemplId() {
        return templId;
    }

    public void setTemplId(Long templId) {
        this.templId = templId;
    }

    @Column(name = "production_id")
    public Long getProductionId() {
        return productionId;
    }

    public void setProductionId(Long productionId) {
        this.productionId = productionId;
    }

    @Column(name = "receive_id")
    public Long getReceiveId() {
        return receiveId;
    }

    public void setReceiveId(Long receiveId) {
        this.receiveId = receiveId;
    }

    @Column(name = "receive_time")
    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    @Column(name = "receive_status")
    public Integer getReceiveStatus() {
        return receiveStatus;
    }

    public void setReceiveStatus(Integer receiveStatus) {
        this.receiveStatus = receiveStatus;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "ctime")
    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Column(name = "origin_popup")
    public Integer getOriginPopup() {
        return originPopup;
    }

    public void setOriginPopup(Integer originPopup) {
        this.originPopup = originPopup;
    }

    @Column(name = "car_popup")
    public Integer getCarPopup() {
        return carPopup;
    }

    public void setCarPopup(Integer carPopup) {
        this.carPopup = carPopup;
    }

    @Column(name = "goods_popup")
    public Integer getGoodsPopup() {
        return goodsPopup;
    }

    public void setGoodsPopup(Integer goodsPopup) {
        this.goodsPopup = goodsPopup;
    }
}
