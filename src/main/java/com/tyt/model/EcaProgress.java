package com.tyt.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 签署进度表
 * <AUTHOR>
 *
 */

@Entity
@Table(name="eca_progress")
public class EcaProgress implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2126307303362381320L;
	private Long id;
	/**
	 * 合同id
	 */
	private Long contractId;
	/**
	 * 操作方，1-创建 2编辑 3查看 4作废 5下载
	 */
	private Integer optType;
	/**
	 * 操作方，1-系统， 2-拖运方，3-承运方，4-客服
	 */
	private Integer optHandler;
	/**
	 * 用户名称
	 */
	private String userName;
	/**
	 * 用户ID
	 */
	private Long userId;
	/**
	 * 身份证号
	 */
	private String idCard;
	/**
	 * 细节描述
	 */
	private String detail;
	/**
	 * 设备
	 */
	private String device;
	/**
	 * imei
	 */
	private String imei;
	/**
	 * ip地址
	 */
	private String ipAddress;
	/**
	 * 状态 1进行中 2完成
	 */
	private Integer status;

	/**
	 * 创建时间
	 */
	private Date ctime;
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name="contract_id")
	public Long getContractId() {
		return contractId;
	}
	public void setContractId(Long contractId) {
		this.contractId = contractId;
	}
	@Column(name="opt_type")
	public Integer getOptType() {
		return optType;
	}
	public void setOptType(Integer optType) {
		this.optType = optType;
	}
	@Column(name="opt_handler")
	public Integer getOptHandler() {
		return optHandler;
	}
	public void setOptHandler(Integer optHandler) {
		this.optHandler = optHandler;
	}
	@Column(name="user_name")
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	@Column(name="user_id")
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name="id_card")
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	@Column(name="detail")
	public String getDetail() {
		return detail;
	}
	public void setDetail(String detail) {
		this.detail = detail;
	}
	@Column(name="device")
	public String getDevice() {
		return device;
	}
	public void setDevice(String device) {
		this.device = device;
	}
	@Column(name="imei")
	public String getImei() {
		return imei;
	}
	public void setImei(String imei) {
		this.imei = imei;
	}
	@Column(name="ip_address")
	public String getIpAddress() {
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	@Column(name="status")
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	@Column(name="ctime")
	public Date getCtime() {
		return ctime;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	
	

}
