package com.tyt.model;


import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tyt_pay_back")
public class PayBack  implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7855636069061943834L;
	Long id;
	Integer amount;// 交易金额 单位分
	String status;// 支付返回结果
	String payNo;// 支付流水号(支付订单号)
	String tradeNo;// 第三方渠道的交易流水号
	String other;// 请求所有参数；
	Date createTime;// 创建时间

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
    @Column(name="amount")
	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}
	@Column(name="status")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	@Column(name="pay_no")
	public String getPayNo() {
		return payNo;
	}

	public void setPayNo(String payNo) {
		this.payNo = payNo;
	}
	@Column(name="trade_no")
	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}
	@Column(name="other")
	public String getOther() {
		return other;
	}

	public void setOther(String other) {
		this.other = other;
	}
	@Column(name="create_time")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

}
