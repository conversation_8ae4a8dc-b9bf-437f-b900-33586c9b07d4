package com.tyt.common.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.common.dao.TytMessageTmplDao;
import com.tyt.model.TytMessageTmpl;
@Repository("tytMessageTmplDao")
public class TytMessageTmplDaoImpl extends BaseDaoImpl<TytMessageTmpl,Long> implements TytMessageTmplDao {
	
	public TytMessageTmplDaoImpl(){
		   this.setEntityClass(TytMessageTmpl.class);
	   }
	
	

}
