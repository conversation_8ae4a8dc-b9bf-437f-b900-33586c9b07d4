package com.tyt.common.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.common.dao.InternalEmployeeDao;
import com.tyt.model.TytInternalEmployee;
@Repository("internalEmployeeDao")
public class InternalEmployeeDaoImpl extends BaseDaoImpl<TytInternalEmployee,Long> implements InternalEmployeeDao {

	public InternalEmployeeDaoImpl(){
		   this.setEntityClass(TytInternalEmployee.class);
	   }
}
