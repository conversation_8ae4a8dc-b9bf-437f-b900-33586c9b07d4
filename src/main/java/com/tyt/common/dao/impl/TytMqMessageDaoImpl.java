package com.tyt.common.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.common.dao.TytMqMessageDao;
import com.tyt.model.TytMqMessage;
@Repository("tytMqMessageDao")
public class TytMqMessageDaoImpl extends BaseDaoImpl<TytMqMessage,Long> implements TytMqMessageDao {
	
	public TytMqMessageDaoImpl(){
		   this.setEntityClass(TytMqMessage.class);
	   }

}
