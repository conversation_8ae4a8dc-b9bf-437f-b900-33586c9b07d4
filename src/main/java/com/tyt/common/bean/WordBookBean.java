package com.tyt.common.bean;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class WordBookBean implements Serializable{

	 /**
	 * 
	 */
	private static final long serialVersionUID = -5797321948276654770L;
	Long id;//'主键',
	 String groupCode;//'分组代码',
	 String groupName;//'分组名称',
	 String value;//'属性值',
	 String name;//'属性名称',
	 String defaults;//'默认显示顺序',
	 Integer sort;//排序
	 String parent;
	 
	 String shortName;//'属性简称',
	 String remark;//'描述',
	 Integer dictStatus;
	 
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDefaults() {
		return defaults;
	}
	public void setDefaults(String defaults) {
		this.defaults = defaults;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public String getParent() {
		return parent;
	}
	public void setParent(String parent) {
		this.parent = parent;
	}
	public String getShortName() {
		return shortName;
	}
	public String getRemark() {
		return remark;
	}
	public Integer getDictStatus() {
		return dictStatus;
	}
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public void setDictStatus(Integer dictStatus) {
		this.dictStatus = dictStatus;
	}

	 
}
