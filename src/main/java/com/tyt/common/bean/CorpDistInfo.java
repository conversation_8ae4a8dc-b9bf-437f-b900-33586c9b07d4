package com.tyt.common.bean;

public class CorpDistInfo {

	private Long startPx;
	private Long startPy;
	private Long startLongitude;
	private Long startLatitude;
	private Long destPx;
	private Long destPy;
	private Long destLongitude;
	private Long destLatitude;
	private Integer distance;
	private Integer iosDistance;
	private Integer androidDistance;
	public Long getStartPx() {
		return startPx;
	}
	public void setStartPx(Long startPx) {
		this.startPx = startPx;
	}
	public Long getStartPy() {
		return startPy;
	}
	public void setStartPy(Long startPy) {
		this.startPy = startPy;
	}
	public Long getStartLongitude() {
		return startLongitude;
	}
	public void setStartLongitude(Long startLongitude) {
		this.startLongitude = startLongitude;
	}
	public Long getStartLatitude() {
		return startLatitude;
	}
	public void setStartLatitude(Long startLatitude) {
		this.startLatitude = startLatitude;
	}
	public Long getDestPx() {
		return destPx;
	}
	public void setDestPx(Long destPx) {
		this.destPx = destPx;
	}
	public Long getDestPy() {
		return destPy;
	}
	public void setDestPy(Long destPy) {
		this.destPy = destPy;
	}
	public Long getDestLongitude() {
		return destLongitude;
	}
	public void setDestLongitude(Long destLongitude) {
		this.destLongitude = destLongitude;
	}
	public Long getDestLatitude() {
		return destLatitude;
	}
	public void setDestLatitude(Long destLatitude) {
		this.destLatitude = destLatitude;
	}
	public Integer getDistance() {
		return distance;
	}
	public void setDistance(Integer distance) {
		this.distance = distance;
	}
	public Integer getIosDistance() {
		return iosDistance;
	}
	public void setIosDistance(Integer iosDistance) {
		this.iosDistance = iosDistance;
	}
	public Integer getAndroidDistance() {
		return androidDistance;
	}
	public void setAndroidDistance(Integer androidDistance) {
		this.androidDistance = androidDistance;
	}
	
	
}
