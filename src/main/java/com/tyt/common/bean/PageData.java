package com.tyt.common.bean;

import com.github.pagehelper.Page;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
//分页统一返回类
@ToString
public class PageData<T> implements Serializable {
    //总条数
    private long total;

    //总页数
    private long pages;

    //当前页码
    private Integer pageNum = 1;

    //每页显示条数
    private Integer pageSize = 10;

    //分页数据集合
    private List<T> list;

    //附加数据
    private Object extraData;

    public PageData(List<T> list) {

        if (list instanceof Page) {
            Page<T> page = (Page<T>) list;
            this.pageNum = page.getPageNum();
            this.pageSize = page.getPageSize();
            this.total = page.getTotal();
            this.pages = page.getPages();
            this.list = page;
        }
    }

    public <U> PageData<U> covert(List<U> list) {
        PageData<U> pageData = new PageData<>();
        pageData.pageNum = this.pageNum;
        pageData.pageSize = this.pageSize;
        pageData.total = this.total;
        pageData.pages = this.pages;
        pageData.list = list;
        return pageData;
    }

    /**
     * 通过map方式将附加数据返回
     * @param objKey
     * @param objValue
     */
    public void putExtraData(Object objKey, Object objValue){
        if(extraData == null){
            extraData = new HashMap<>();
        }else {
            if(!(extraData instanceof Map)){
                throw TytException.createException(ResponseEnum.class_type_error.info());
            }
        }

        ((Map) extraData).put(objKey, objValue);
    }

    public <R> PageData<R> covert(Function<T, R> mappingFunction) {
        if (mappingFunction == null) {
            throw new IllegalArgumentException("mappingFunction 不能为空");
        }

        if (list != null) {
            List<R> newList = list.stream().map(mappingFunction).collect(Collectors.toList());
            return covert(newList);
        }

        return covert((List<R>) null);
    }
}
