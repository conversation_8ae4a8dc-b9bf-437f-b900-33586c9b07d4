package com.tyt.common.bean;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class NoticeRemindListBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1172876482934347316L;
	private Long id;
	private String type1;
	private String type2;
	private String isPopup;
	private String content;
	private String msgId;
	private Long productionId;
	private Date ctime;
	public Long getId() {
		return id;
	}
	public String getType1() {
		return type1;
	}
	public String getType2() {
		return type2;
	}
	public String getIsPopup() {
		return isPopup;
	}
	public String getContent() {
		return content;
	}
	public String getMsgId() {
		return msgId;
	}
	public Long getProductionId() {
		return productionId;
	}
	public Date getCtime() {
		return ctime;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setType1(String type1) {
		this.type1 = type1;
	}
	public void setType2(String type2) {
		this.type2 = type2;
	}
	public void setIsPopup(String isPopup) {
		this.isPopup = isPopup;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}
	public void setProductionId(Long productionId) {
		this.productionId = productionId;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
}
