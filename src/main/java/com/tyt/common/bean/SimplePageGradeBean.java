package com.tyt.common.bean;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class SimplePageGradeBean implements Serializable{
	
	private static final long serialVersionUID = -868343996555398654L;
	private String gradeCode;
	private Integer valueBeginRange;
	private Integer valueEndRange;
	private String chineseMean;
	private String valueUnit;
	private Integer orderNumber;
	private String type;
	public String getGradeCode() {
		return gradeCode;
	}
	public void setGradeCode(String gradeCode) {
		this.gradeCode = gradeCode;
	}
	public Integer getValueBeginRange() {
		return valueBeginRange;
	}
	public void setValueBeginRange(Integer valueBeginRange) {
		this.valueBeginRange = valueBeginRange;
	}
	public Integer getValueEndRange() {
		return valueEndRange;
	}
	public void setValueEndRange(Integer valueEndRange) {
		this.valueEndRange = valueEndRange;
	}
	public String getChineseMean() {
		return chineseMean;
	}
	public void setChineseMean(String chineseMean) {
		this.chineseMean = chineseMean;
	}
	public String getValueUnit() {
		return valueUnit;
	}
	public void setValueUnit(String valueUnit) {
		this.valueUnit = valueUnit;
	}
	public Integer getOrderNumber() {
		return orderNumber;
	}
	public void setOrderNumber(Integer orderNumber) {
		this.orderNumber = orderNumber;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
	
}
