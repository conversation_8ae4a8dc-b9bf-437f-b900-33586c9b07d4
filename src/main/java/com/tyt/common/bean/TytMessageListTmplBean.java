package com.tyt.common.bean;


public class TytMessageListTmplBean implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -472542958580472864L;
	//详情类型 0 文本1链接 
	private String details;
	//详情内容
	private String detailsContent;
	//消息类型 参考tyt_source 表group_code=message_msg_type
	private String msgType;
	//说明
	private String remarks;
	//摘要
	private String summary;
	//标题
	private String title;
	//列表消息类型 0 文本1图片
	private String type;
	//列表图标类型
	private String iconType;
	//列表图标
	private String icon;
	
	
	public String getDetails() {
		return details;
	}
	public String getDetailsContent() {
		return detailsContent;
	}
	public String getMsgType() {
		return msgType;
	}
	public String getRemarks() {
		return remarks;
	}
	public String getSummary() {
		return summary;
	}
	public String getTitle() {
		return title;
	}
	public void setDetails(String details) {
		this.details = details;
	}
	public void setDetailsContent(String detailsContent) {
		this.detailsContent = detailsContent;
	}
	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public void setSummary(String summary) {
		this.summary = summary;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getType() {
		return type;
	}
	public String getIconType() {
		return iconType;
	}
	public String getIcon() {
		return icon;
	}
	public void setType(String type) {
		this.type = type;
	}
	public void setIconType(String iconType) {
		this.iconType = iconType;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}

	
	

}