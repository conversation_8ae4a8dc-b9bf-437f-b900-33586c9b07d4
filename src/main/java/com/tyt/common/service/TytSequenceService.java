package com.tyt.common.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytSequence;

public interface TytSequenceService extends BaseService<TytSequence,String> {

	
	/**
	 * 或得下一个序列的值
	 * @param sequenceName 虚列的名字
	 * @return
	 */
	public Long updateGetNextSequenceNbr(String sequenceName);
	/**
	 * 返回运单号
	 * @param sequenceName
	 * @return yyMMDD00000001
	 * @throws Exception
	 */
	public String updateGetNumberForDate(String sequenceName) throws Exception;

	public String updateGetOrderNumberForDateTime(String sequenceName) throws Exception;
	/**
	 * 
	 * @param headStr 头字符串
	 * @param formatTime 格式化的日期 如20180126181818
	 * @param sequenceName 序列名
	 * @param position 尾部位数如8位
	 * @return String
	 * @throws Exception
	 */
	public String updateGetNumberForDateTime(String headStr,String formatTime,String sequenceName,double position) throws Exception;
		

}
