package com.tyt.common.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.bean.TytMessageListTmplBean;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.model.TytMessageTmpl;
@Service("tytMessageTmplService")
public class TytMessageTmplServiceImpl extends BaseServiceImpl<TytMessageTmpl,Long> implements TytMessageTmplService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	
	@Resource(name = "tytMessageTmplDao")
	public void setBaseDao(BaseDao<TytMessageTmpl, Long> tytMessageTmplDao) {
		super.setBaseDao(tytMessageTmplDao);
	}
	
	public String getSmsTmpl(String key){
		String hql="from TytMessageTmpl where type=? and tmplKey=? and status=?";
		List<TytMessageTmpl> list=this.getBaseDao().find(hql, "0",key,"0");
		if(list!=null &&list.size()>0){
			return list.get(0).getContent();
		}
		return null;
	}
	
	public String getSmsTmpl(String key, String defaultValue){
		String hql="from TytMessageTmpl where type=? and tmplKey=? and status=?";
		List<TytMessageTmpl> list=this.getBaseDao().find(hql, "0",key,"0");
		if(list!=null &&list.size()>0){
			return list.get(0).getContent();
		}
		return defaultValue;
	}
	
	public TytMessageListTmplBean getListMessage(String key){
		String hql="from TytMessageTmpl where type=? and tmplKey=? and status=?";
		List<TytMessageTmpl> list=this.getBaseDao().find(hql, "1",key,"0");
		if(list!=null &&list.size()>0){
			 String content=list.get(0).getContent();
			 TytMessageListTmplBean tytMessageListTmplBean=JSON.parseObject(content, TytMessageListTmplBean.class);
			 return tytMessageListTmplBean;
		}
		return null;
	}
}
