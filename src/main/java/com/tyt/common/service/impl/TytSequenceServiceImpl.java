package com.tyt.common.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytSequenceService;
import com.tyt.model.TytSequence;
import com.tyt.util.TimeUtil;
@Service("tytSequenceService")
public class TytSequenceServiceImpl extends BaseServiceImpl<TytSequence,String> implements TytSequenceService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@Resource(name = "tytSequenceDao")
	public void setBaseDao(BaseDao<TytSequence, String> tytSequenceDao) {
		super.setBaseDao(tytSequenceDao);
	}
//	@Override
//	public Long updateGetNextSequenceNbr(String sequenceName) {
//		TytSequence tytSequence=this.getBaseDao().getByIdForLock(sequenceName);
//		tytSequence.setNumber(tytSequence.getNumber()+1);
//		this.getBaseDao().update(tytSequence);
//		return tytSequence.getNumber();
//	}

	
	@Override
	public Long updateGetNextSequenceNbr(String sequenceName) {
		String sql ="update tyt_sequence set number=number+1 where name=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[]{sequenceName});
		TytSequence tytSequence=this.getById(sequenceName);		
		return tytSequence.getNumber();
	}
	
	public String updateGetNumberForDate(String sequenceName) throws Exception{
		String sql="select * from tyt_sequence where name=:name for update";
		Map <String, org.hibernate.type.Type> cMap= new HashMap<String, org.hibernate.type.Type>();
		cMap.put("number", Hibernate.LONG);
		cMap.put("name", Hibernate.STRING);
		cMap.put("dates", Hibernate.STRING);
		Map <String,Object>p=new HashMap<String,Object>();
		p.put("name", sequenceName);
		TytSequence tytSequence=this.getBaseDao().queryByMap(sql,p,TytSequence.class , cMap);
		//TytSequence tytSequence=this.getBaseDao().getByIdForLock(sequenceName);
		String d=TimeUtil.formatDateMin(TimeUtil.today());
		if(tytSequence.getDates().equals(d)){
			tytSequence.setNumber(tytSequence.getNumber()+1);
		}else{
			tytSequence.setDates(d);
			tytSequence.setNumber(1l);
		}
		this.getBaseDao().update(tytSequence);
		if("ecacontract".equals(sequenceName)){
			String hhmm = TimeUtil.formatTimeMin(new Date());
			BigDecimal date=new BigDecimal(tytSequence.getDates()).movePointRight(8).add(new BigDecimal(tytSequence.getNumber()).movePointRight(4))
			.add(new BigDecimal(hhmm));
			return date.toPlainString();
		}else{
			BigDecimal date=new BigDecimal(tytSequence.getDates()).movePointRight(8).add(new BigDecimal(tytSequence.getNumber()));
			return date.toPlainString();
		}
	}

	public String updateGetOrderNumberForDateTime(String sequenceName) throws Exception{
		String sql="select * from tyt_sequence where name=:name for update";
		Map <String, org.hibernate.type.Type> cMap= new HashMap<String, org.hibernate.type.Type>();
		cMap.put("number", Hibernate.LONG);
		cMap.put("name", Hibernate.STRING);
		cMap.put("dates", Hibernate.STRING);
		Map <String,Object>p=new HashMap<String,Object>();
		p.put("name", sequenceName);
		TytSequence tytSequence=this.getBaseDao().queryByMap(sql,p,TytSequence.class , cMap);
		String d=TimeUtil.formatDateMin(TimeUtil.today());
		if(tytSequence.getDates().equals(d)){
			tytSequence.setNumber(tytSequence.getNumber()+1);
		}else{
			tytSequence.setDates(d);
			tytSequence.setNumber(1l);
		}
		this.getBaseDao().update(tytSequence);
		String hhmm = TimeUtil.formatTimeNoPoint(new Date());
		BigDecimal date=new BigDecimal(tytSequence.getDates()).movePointRight(10).add(new BigDecimal(tytSequence.getNumber()).movePointRight(6))
				.add(new BigDecimal(hhmm));
		return date.toPlainString();

	}

	public String updateGetNumberForDateTime(String headStr,String formatTime,String sequenceName,double position) throws Exception{
		String sql="select * from tyt_sequence where name=:name for update";
		Map <String, org.hibernate.type.Type> cMap= new HashMap<String, org.hibernate.type.Type>();
		cMap.put("number", Hibernate.LONG);
		cMap.put("name", Hibernate.STRING);
		cMap.put("dates", Hibernate.STRING);
		Map <String,Object>p=new HashMap<String,Object>();
		p.put("name", sequenceName);
		TytSequence tytSequence=this.getBaseDao().queryByMap(sql,p,TytSequence.class , cMap);
		//TytSequence tytSequence=this.getBaseDao().getByIdForLock(sequenceName);
		String d=TimeUtil.formatDateMin(TimeUtil.today());
		if(tytSequence.getDates().equals(d)){
			tytSequence.setNumber(tytSequence.getNumber()+1);
		}else{
			tytSequence.setDates(d);
			tytSequence.setNumber(1l);
		}
		this.getBaseDao().update(tytSequence);
		
		//headStr+headStr+position
		String tail=new BigDecimal(Math.pow(10d,position)+tytSequence.getNumber()).setScale(0, BigDecimal.ROUND_HALF_UP).toString().substring(1);
		return headStr+formatTime+tail;
	}
}
