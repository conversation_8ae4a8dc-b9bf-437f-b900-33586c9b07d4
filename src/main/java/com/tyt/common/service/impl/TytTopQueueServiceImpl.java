package com.tyt.common.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytTopQueueService;
import com.tyt.model.TytTopQueue;
@Service("tytTopQueueService")
public class TytTopQueueServiceImpl extends BaseServiceImpl<TytTopQueue,Long> implements TytTopQueueService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@Resource(name = "tytTopQueueDao")
	public void setBaseDao(BaseDao<TytTopQueue, Long> tytTopQueueDao) {
		super.setBaseDao(tytTopQueueDao);
	}
	@Override
	public void addTytTopQueue(Long sourceMsgId, Long newMsgId, Integer type) {
		TytTopQueue tytTopQueue= new TytTopQueue();
		tytTopQueue.setCtime(new Date());
		tytTopQueue.setNewMsgId(newMsgId);
		tytTopQueue.setSourceMsgId(sourceMsgId);
		tytTopQueue.setStatus(0);
		tytTopQueue.setType(type);
		this.getBaseDao().insert(tytTopQueue);
	}

}
