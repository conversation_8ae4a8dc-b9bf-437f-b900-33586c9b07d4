package com.tyt.common.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.tyt.model.City;
import com.tyt.transport.service.GeographicalLocationService;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.common.bean.NoticeRemindListBean;
import com.tyt.common.service.TytGeoDictService;
import com.tyt.model.TytGeoDict;
import com.tyt.util.Constant;
import com.tyt.util.DistanceUtil;
@Service("tytGeoDictService")
public class TytGeoDictServiceImpl extends BaseServiceImpl<TytGeoDict,String> implements TytGeoDictService {
    public Logger logger = LoggerFactory
            .getLogger(this.getClass());
    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;
    @Resource(name = "geographicalLocationService")
    GeographicalLocationService geographicalLocationService;

    @Resource(name = "tytGeoDictDao")
    public void setBaseDao(BaseDao<TytGeoDict, String> TytGeoDictDao) {
        super.setBaseDao(TytGeoDictDao);
    }

    public TytGeoDict getTytGeoDict(String provinc,String city,String area){
        boolean t=false;
        if(area!=null&& !"".equals(area.trim())){
            t=true;
        }
        String key=provinc+"_"+city+"_"+(t?area:city);
        TytGeoDict tytGeoDict=(TytGeoDict)cacheService.getObject(Constant.CACHE_TYT_GEO_DICT_NEW+key);
        if (tytGeoDict == null) {

			/*String hql = "from TytGeoDict t where type>1 and provinc=? and city=? and area=? ";

			List<TytGeoDict> tytGeoDictList = this.getBaseDao().find(hql,
					provinc, city, t ? area : city);*/
            List<City> cityByCityNameAndAreaName = geographicalLocationService.getCityByCityNameAndAreaName(provinc, city, area);
            if (cityByCityNameAndAreaName != null&&cityByCityNameAndAreaName.size()>0) {
                tytGeoDict = cityToTytGeoDict(cityByCityNameAndAreaName.get(0));
                cacheService.setObject(
                        Constant.CACHE_TYT_GEO_DICT_NEW + tytGeoDict.getProvinc()
                                + "_" + tytGeoDict.getCity() + "_"
                                + tytGeoDict.getArea(), tytGeoDict,
                        Constant.CACHE_TYT_GEO_DICT_TIME);

            }else{
                tytGeoDict=this.getTytGeoDictOld(provinc, city, area);
            }
        }
        return tytGeoDict;
    }


    public TytGeoDict getTytGeoDict(Integer px,Integer py){
        BigDecimal newPx=new BigDecimal(px);
        BigDecimal newPy=new BigDecimal(py);
        String key=px.intValue()+"_"+py.intValue();
        //TytGeoDict tytGeoDict=(TytGeoDict)cacheService.getObject(Constant.CACHE_TYT_GEO_DICT_PX_PY_NEW+key);
        TytGeoDict tytGeoDict=null;
        if (tytGeoDict == null) {

			/*String hql = "from TytGeoDict t where type>1 and px=? and py=? ";

			List<TytGeoDict> tytGeoDictList = this.getBaseDao().find(hql,
					Long.valueOf(String.valueOf(px)), Long.valueOf(String.valueOf(py)));*/
            City cityByPxAndPy = geographicalLocationService.getCityByPxAndPy(newPx.movePointLeft(2).stripTrailingZeros().toPlainString(), newPy.movePointLeft(2).stripTrailingZeros().toPlainString());
            if (cityByPxAndPy != null) {
                tytGeoDict = cityToTytGeoDict(cityByPxAndPy);
                cacheService.setObject(
                        Constant.CACHE_TYT_GEO_DICT_PX_PY_NEW +key, tytGeoDict,
                        Constant.CACHE_TYT_GEO_DICT_TIME);

            }else{
                tytGeoDict=this.getTytGeoDictOld(px, py);
            }
        }
        return tytGeoDict;
    }
    public double getDistace(String startProvinc,String startCity,
                             String startArea,
                             String destProvinc,
                             String destCity,
                             String destArea){

        TytGeoDict startPoint=getTytGeoDict(startProvinc,startCity,
                startArea);
        if(startPoint==null){
            return Double.MAX_VALUE;
        }
        TytGeoDict destPoint=getTytGeoDict(destProvinc,
                destCity,
                destArea);

        if(destPoint==null){
            return Double.MAX_VALUE;
        }
        double distance=DistanceUtil.distance(startPoint.getLongitude(), startPoint.getLatitude(), destPoint.getLongitude(), destPoint.getLatitude());
        return new BigDecimal(distance).movePointLeft(3).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

    }

    /**
     * 获取旧的地区字典
     * @param px
     * @param py
     * @return
     */
    public TytGeoDict getTytGeoDictOld(Integer px,Integer py){

        String key=px.intValue()+"_"+py.intValue();
        TytGeoDict tytGeoDict=(TytGeoDict)cacheService.getObject(Constant.CACHE_TYT_GEO_DICT_PX_PY+key);
        if (tytGeoDict == null) {

            StringBuffer sql = new StringBuffer("select id,pid,name,alias,rf,type,"
                    + "px,py,longitude,latitude,shortname,provinc,city,area "
                    + "from tyt_geo_dict_old where type>1 and px=? and py=? ");
            List<Object> list = new ArrayList<Object>();
            list.add(px);
            list.add(py);
            Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
            scalarMap.put("id", Hibernate.LONG);
            scalarMap.put("pid", Hibernate.LONG);
            scalarMap.put("name", Hibernate.STRING);
            scalarMap.put("alias", Hibernate.STRING);
            scalarMap.put("rf", Hibernate.STRING);
            scalarMap.put("type", Hibernate.INTEGER);
            scalarMap.put("px", Hibernate.LONG);
            scalarMap.put("py", Hibernate.LONG);
            scalarMap.put("longitude", Hibernate.LONG);
            scalarMap.put("latitude", Hibernate.LONG);
            scalarMap.put("shortname", Hibernate.STRING);
            scalarMap.put("provinc", Hibernate.STRING);
            scalarMap.put("city", Hibernate.STRING);
            scalarMap.put("area", Hibernate.STRING);
            List <TytGeoDict> tytGeoDictList=this.getBaseDao().search(sql.toString(), scalarMap, TytGeoDict.class, list.toArray(), 1, 1);
            if (tytGeoDictList != null&&tytGeoDictList.size()>0) {
                tytGeoDict = tytGeoDictList.get(0);
                cacheService.setObject(
                        Constant.CACHE_TYT_GEO_DICT_PX_PY +key, tytGeoDict,
                        Constant.CACHE_TYT_GEO_DICT_TIME);

            }

        }
        return tytGeoDict;
    }
    /**
     * 获取旧的地区字典
     * @param px
     * @param py
     * @return
     */
    public TytGeoDict getTytGeoDictOld(String provinc,String city,String area){
        boolean t=false;
        if(area!=null&& !"".equals(area.trim())){
            t=true;
        }
        String key=provinc+"_"+city+"_"+(t?area:city);
        TytGeoDict tytGeoDict=(TytGeoDict)cacheService.getObject(Constant.CACHE_TYT_GEO_DICT+key);
        if (tytGeoDict == null) {

            StringBuffer sql = new StringBuffer("select id,pid,name,alias,rf,type,"
                    + "px,py,longitude,latitude,shortname,provinc,city,area "
                    + "from tyt_geo_dict_old where type>1 and provinc=? and city=? and area=? ");
            List<Object> list = new ArrayList<Object>();
            list.add(provinc);
            list.add(city);
            list.add(area);

            Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
            scalarMap.put("id", Hibernate.LONG);
            scalarMap.put("pid", Hibernate.LONG);
            scalarMap.put("name", Hibernate.STRING);
            scalarMap.put("alias", Hibernate.STRING);
            scalarMap.put("rf", Hibernate.STRING);
            scalarMap.put("type", Hibernate.INTEGER);
            scalarMap.put("px", Hibernate.LONG);
            scalarMap.put("py", Hibernate.LONG);
            scalarMap.put("longitude", Hibernate.LONG);
            scalarMap.put("latitude", Hibernate.LONG);
            scalarMap.put("shortname", Hibernate.STRING);
            scalarMap.put("provinc", Hibernate.STRING);
            scalarMap.put("city", Hibernate.STRING);
            scalarMap.put("area", Hibernate.STRING);
            List <TytGeoDict> tytGeoDictList=this.getBaseDao().search(sql.toString(), scalarMap, TytGeoDict.class, list.toArray(), 1, 1);

            if (tytGeoDictList != null&&tytGeoDictList.size()>0) {
                tytGeoDict = tytGeoDictList.get(0);
                cacheService.setObject(
                        Constant.CACHE_TYT_GEO_DICT + tytGeoDict.getProvinc()
                                + "_" + tytGeoDict.getCity() + "_"
                                + tytGeoDict.getArea(), tytGeoDict,
                        Constant.CACHE_TYT_GEO_DICT_TIME);

            }
        }
        return tytGeoDict;
    }

    private TytGeoDict cityToTytGeoDict(City city) {
        TytGeoDict tytGeoDict=new TytGeoDict();
        tytGeoDict.setAlias(city.getAreaName());
        tytGeoDict.setShortname(city.getShortName());
        tytGeoDict.setArea(city.getAreaName());
        tytGeoDict.setRf(city.getRf());
        tytGeoDict.setProvinc(city.getProvince());
        tytGeoDict.setCity(city.getCityName());
        tytGeoDict.setName(city.getAreaName());

        tytGeoDict.setPx(Long.valueOf(new BigDecimal(city.getPx()).movePointRight(2).toString()));
        tytGeoDict.setPy(Long.valueOf(new BigDecimal(city.getPy()).movePointRight(2).toString()));


        return tytGeoDict;

    }
}
