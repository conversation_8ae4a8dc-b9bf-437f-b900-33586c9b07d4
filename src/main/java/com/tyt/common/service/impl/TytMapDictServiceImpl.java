package com.tyt.common.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.tyt.model.City;
import com.tyt.model.PingAnPortCode;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMapDictService;
import com.tyt.model.TytMapDict;
import com.tyt.util.DistanceUtil;
import com.tyt.util.MD5Util;
@Service("tytMapDictService")
public class TytMapDictServiceImpl extends BaseServiceImpl<TytMapDict,Long> implements TytMapDictService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	
	@Resource(name = "tytMapDictDao")
	public void setBaseDao(BaseDao<TytMapDict, Long> TytMapDictDao) {
		super.setBaseDao(TytMapDictDao);
	}
	
	public void saveMapDict(TytMapDict tytMapDict){
		String mapKey=getMapKey(tytMapDict);
		if(mapKey!=null){
			TytMapDict tmd=getTytMapDict(mapKey);
			if(tmd==null){
				tytMapDict.setMapKey(mapKey);
				tytMapDict.setCtime(new Date());
				double distance=DistanceUtil.distance(new BigDecimal(tytMapDict.getStartLongitude()).movePointLeft(2).doubleValue(), new BigDecimal(tytMapDict.getStartLatitude()).movePointLeft(2).doubleValue(), new BigDecimal(tytMapDict.getDestLongitude()).movePointLeft(2).doubleValue(), new BigDecimal(tytMapDict.getDestLatitude()).movePointLeft(2).doubleValue());
				tytMapDict.setStraightDistance(new BigDecimal(distance).movePointLeft(1).setScale(0, BigDecimal.ROUND_HALF_UP).intValue());
				this.add(tytMapDict);
			}else{
			/*	if(tytMapDict.getDistance()!=null){
					tmd.setDistance(tytMapDict.getDistance());
				}
				if(tytMapDict.getIosDistance()!=null){
					tmd.setIosDistance(tytMapDict.getDistance());
				}*/
				///this.update(tmd);
			}
		}
	}
	
	public TytMapDict getTytMapDict(String mapKey){
		String hql="from TytMapDict where mapKey=?";
		List<TytMapDict> list=this.getBaseDao().find(hql, mapKey);
		if(list!=null && list.size()>0){
			return list.get(0);
		}
		return null;		
	}
	
	public String getMapKey(TytMapDict tytMapDict){
		if(StringUtils.isNotBlank(tytMapDict.getStartProvinc())&&StringUtils.isNotBlank(tytMapDict.getStartCity())&&StringUtils.isNotBlank(tytMapDict.getDestProvinc())&&StringUtils.isNotBlank(tytMapDict.getDestCity())){
			StringBuffer sb=new StringBuffer();
			sb.append(tytMapDict.getStartProvinc());
			sb.append("-");
			sb.append(tytMapDict.getStartCity());
			if(StringUtils.isNotBlank(tytMapDict.getStartArea())){
				sb.append("-");
				sb.append(tytMapDict.getStartArea());
			}
			sb.append("__");
			sb.append(tytMapDict.getDestProvinc());
			sb.append("-");
			sb.append(tytMapDict.getDestCity());
			if(StringUtils.isNotBlank(tytMapDict.getDestArea())){
				sb.append("-");
				sb.append(tytMapDict.getDestArea());
			}
			return MD5Util.GetMD5Code(sb.toString());
		}
		return null;
	}
	
	public TytMapDict getDistance(String clientSign,String startProvinc,String startCity,String startArea,String destProvinc,String destCity,String destArea){
		TytMapDict tytMapDict=new TytMapDict();
		tytMapDict.setStartProvinc(startProvinc);
		tytMapDict.setStartCity(startCity);
		tytMapDict.setStartArea(startArea);
		tytMapDict.setDestProvinc(destProvinc);
		tytMapDict.setDestCity(destCity);
		tytMapDict.setDestArea(destArea);
		
		String mapKey=getMapKey(tytMapDict);
		TytMapDict tmd=getTytMapDict( mapKey);
		if(tmd!=null){
			return tmd;
		}
		return null;
	}

	@Override
	public PingAnPortCode fingPingAnPortCode(String cityName, String provinceName) {
		String sql = "select id,city_name cityName,city_code cityCode,province_name provinceName,province_code provinceCode,area_name areaName,area_code areaCode from pingan_port_code where province_name like  ?";
		Object[] params=new Object[] {provinceName+"%"};
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("cityName", Hibernate.STRING);
		scalarMap.put("cityCode", Hibernate.STRING);
		scalarMap.put("provinceName", Hibernate.STRING);
		scalarMap.put("provinceCode", Hibernate.STRING);
        scalarMap.put("areaName", Hibernate.STRING);
        scalarMap.put("areaCode", Hibernate.STRING);
		List<PingAnPortCode> search = this.getBaseDao().search(sql, scalarMap, PingAnPortCode.class, params);
		 cityName = cityName.substring(0, cityName.length() - 1);

		for (PingAnPortCode pingAnPortCode : search) {
			if (pingAnPortCode.getCityName().contains(cityName)){
				return pingAnPortCode;
			}
		}
		return null;
	}

	@Override
	public int getDistanceCity(String city, String district, String transportCity, String transportArea){
		String sql ="select id,distance from tyt_map_dict where start_city = ? and start_area = ? and dest_city = ? and dest_area = ? ";
		Object[] params=new Object[] {city,district,transportCity,transportArea};
		Map<String, Type> scalarMap = new HashMap<String, Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("distance", Hibernate.INTEGER);
		List<TytMapDict> list = this.getBaseDao().search(sql, scalarMap, TytMapDict.class, params);
		if(list!=null && list.size()>0){
			return list.get(0).getDistance();
		}
		return 0;
	}

}
