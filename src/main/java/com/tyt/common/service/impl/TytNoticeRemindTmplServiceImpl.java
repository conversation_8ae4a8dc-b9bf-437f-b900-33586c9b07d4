package com.tyt.common.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytNoticeRemindTmplService;
import com.tyt.model.TytNoticeRemindTmpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
@Service("tytNoticeRemindTmplService")
public class TytNoticeRemindTmplServiceImpl extends BaseServiceImpl<TytNoticeRemindTmpl,Long> implements TytNoticeRemindTmplService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@Resource(name = "tytNoticeRemindTmplDao")
	public void setBaseDao(BaseDao<TytNoticeRemindTmpl, Long> tytNoticeRemindTmplDao) {
		super.setBaseDao(tytNoticeRemindTmplDao);
	}
	
	public TytNoticeRemindTmpl getTytNoticeRemindTmpl(String type1,String type2){
		String hql="from TytNoticeRemindTmpl where type1=? and type2=? and status=?";
		List<TytNoticeRemindTmpl> list=this.getBaseDao().find(hql,type1 ,type2,"0");
		if(list!=null &&list.size()>0){
			return list.get(0);
		}
		return null;
	}

	@Override
	public String queryContent(String type1, String type2, String defaultContent) {
		String content;
		TytNoticeRemindTmpl remindTmpl = getTytNoticeRemindTmpl(type1, type2);
		if (remindTmpl == null) {
			content = defaultContent;
		} else {
			content = remindTmpl.getContent();
		}
		return content;
	}
}
