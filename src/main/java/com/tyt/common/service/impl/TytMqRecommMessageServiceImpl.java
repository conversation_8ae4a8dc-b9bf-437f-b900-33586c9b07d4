package com.tyt.common.service.impl;

import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqRecommMessageService;
import com.tyt.infofee.bean.MqMessageLogBean;
//import com.tyt.message.mqservice.IMqRecommProducer;
import com.tyt.model.TytMqMsgRecord;

import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("tytMqRecommMessService")
public class TytMqRecommMessageServiceImpl extends BaseServiceImpl<TytMqMsgRecord, Long> implements TytMqRecommMessageService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());
	/*
	 * mq消息处理状态 1：未处理 2：已处理 3: 处理失败
	 */
	private static final int MQ_MESSAGE_STATUS_DEAL_ACCOMPLISH = 2;

	@Resource(name = "tytMqReMessageDao")
	public void setBaseDao(BaseDao<TytMqMsgRecord, Long> TytMqMessageDao) {
		super.setBaseDao(TytMqMessageDao);
	}

//	@Resource
//	private IMqRecommProducer producer;

	@Override
	public void addSaveMqMessage(String serialNum, String messageContent, int messageType) {
//		String sql = "INSERT INTO `tyt`.`tyt_mq_msg_record` ( `message_serial_num`, `message_content`, `create_time`, `message_type`) VALUES (?, ?, ?, ?)";
//		Object[] params = new Object[] { serialNum, messageContent, new Date(), messageType };
//		this.getBaseDao().executeUpdateSql(sql, params);
		// producer.sendMsg(messageContent, null,serialNum, null);
	}

	@Override
	public void addSaveMqMessage(String serialNum, String messageContent, int messageType, int msgType, long timingSendTime) {
//		String sql = "INSERT INTO `tyt`.`tyt_mq_msg_record` ( `message_serial_num`, `message_content`, `create_time`, `message_type`, `msg_type`, `timing_send_time`) VALUES (?, ?, ?, ?, ?, ?)";
//		Object[] params = new Object[] { serialNum, messageContent, new Date(), messageType, msgType, timingSendTime };
//		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public void sendMqMessage(String serialNum, String messageContent, int messageType) {
//		producer.sendMsg(messageContent, null, serialNum, null);
	}

	@Override
	public boolean sendMqMessageWithResult(String serialNum, String messageContent, int messageType) {
//		SendResult sendResult = producer.sendMsg(messageContent, null, serialNum, null);
//		return sendResult != null;
        return false;
	}

	@Override
	public void addSaveTimerMqMessage(String serialNum, String messageContent, int messageType, long time) {
//		String sql = "INSERT INTO `tyt`.`tyt_mq_msg_record` ( `message_serial_num`, `message_content`, `create_time`, `message_type`,`msg_type`,`timing_send_time`) VALUES (?, ?, ?, ?, ?, ?)";
//		Object[] params = new Object[] { serialNum, messageContent, new Date(), messageType, 1, time };
//		this.getBaseDao().executeUpdateSql(sql, params);
		// producer.sendMsg(messageContent, null,serialNum, null);
	}

	@Override
	public void sendTimerMqMessage(String serialNum, String messageContent, int messageType, long time) {
//		producer.sendTimerMsg(messageContent, null, serialNum, time);
	}

	@Override
	public boolean sendTimerMqMessageWithResult(String serialNum, String messageContent, int messageType, long time) {
//		SendResult sendResult = producer.sendTimerMsg(messageContent, null, serialNum, time);
//		return sendResult != null;
        return false;
	}

	@SuppressWarnings("deprecation")
	@Override
	public boolean isMessageProcessed(String messageSerailNum) {
//		String sql = "SELECT tmm.`id`, tmm.`message_serial_num` AS 'messageSerialNum', tmm.`message_content` AS 'messageContent', tmm.`deal_status` AS 'dealStatus', tmm.`create_time` AS 'createTime', tmm.`update_time` AS 'updateTime', tmm.`message_type` AS 'messageType' FROM tyt_mq_msg_record tmm WHERE tmm.`message_serial_num`=?";
//		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
//		scalarMap.put("id", Hibernate.INTEGER);
//		scalarMap.put("messageSerialNum", Hibernate.STRING);
//		scalarMap.put("messageContent", Hibernate.STRING);
//		scalarMap.put("dealStatus", Hibernate.INTEGER);
//		scalarMap.put("createTime", Hibernate.STRING);
//		scalarMap.put("updateTime", Hibernate.STRING);
//		scalarMap.put("messageType", Hibernate.INTEGER);
//		Object[] params = new Object[] { messageSerailNum };
//		List<MqMessageLogBean> messageLogBeans = this.getBaseDao().search(sql, scalarMap, MqMessageLogBean.class, params, 1, 1);
//		logger.info("get message bean result is: " + messageLogBeans);
//		if (messageLogBeans.size() == 1) {
//			return messageLogBeans.get(0).getDealStatus() == MQ_MESSAGE_STATUS_DEAL_ACCOMPLISH;
//		} else {
//			return true;
//		}
        return false;
	}

	@Override
	public void updateMqMessageStatus(String messageSerailNum, int mqMessageStatusDealFailed) {
//		String sql = "UPDATE tyt_mq_msg_record SET deal_status=? WHERE message_serial_num=?";
//		Object[] params = new Object[] { mqMessageStatusDealFailed, messageSerailNum };
//		this.getBaseDao().executeUpdateSql(sql, params);
	}
}
