package com.tyt.common.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.bean.NoticeRemindListBean;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.common.service.TytNoticeRemindTmplService;
import com.tyt.model.TytNoticeRemind;
import com.tyt.model.TytNoticeRemindTmpl;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.TytSwitchUtil;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("tytNoticeRemindService")
public class TytNoticeRemindServiceImpl extends BaseServiceImpl<TytNoticeRemind,Long> implements TytNoticeRemindService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@Resource(name = "tytNoticeRemindTmplService")
	TytNoticeRemindTmplService tytNoticeRemindTmplService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	@Resource(name = "tytNoticeRemindDao")
	public void setBaseDao(BaseDao<TytNoticeRemind, Long> tytNoticeRemindDao) {
		super.setBaseDao(tytNoticeRemindDao);
	}
	
	public List<NoticeRemindListBean> updateGetUserNoticePopupList(Long userId){
		boolean isNewVerson = TytSwitchUtil.isNewInfofeeVersion();
		StringBuffer sql = new StringBuffer("select id,type1,type2,is_popup isPopup,content,msg_id msgId,production_id productionId,ctime from tyt_notice_remind where 1=1 ");
		List<Object> list = new ArrayList<Object>();
		sql.append(" and receive_id=?");
		list.add(userId);
		sql.append(" and receive_status=?");
		list.add("0");
		sql.append(" and status=?");
		list.add("0");
		if(isNewVerson){
			sql.append(" and version_type=1 ");
		}else{
			sql.append(" and version_type IS NULL ");
		}
		//排序
		String shorSql=" order by id desc ";
		
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("type1", Hibernate.STRING);
		scalarMap.put("type2", Hibernate.STRING);		
		scalarMap.put("isPopup", Hibernate.STRING);
		scalarMap.put("content", Hibernate.STRING);
		scalarMap.put("msgId", Hibernate.STRING);
		scalarMap.put("productionId", Hibernate.LONG);
		scalarMap.put("ctime", Hibernate.TIMESTAMP);
		List <NoticeRemindListBean> noticeRemindList=this.getBaseDao().search(sql.toString()+shorSql, scalarMap, NoticeRemindListBean.class, list.toArray(), 1, 1);
		//修改通知为已读
		if(noticeRemindList!=null && noticeRemindList.size()>0){
//			List <Long> ids=new ArrayList<Long>();
//			for(NoticeRemindListBean nrlb:noticeRemindList){
//				ids.add(nrlb.getId());
//			}
			NoticeRemindListBean nrlb=noticeRemindList.get(0);
			//第一条是车主查货主有没有
			if(nrlb.getType1().equals("1")){
				String s="  and type1=? ";
				list.add("2");
				List <NoticeRemindListBean> huoList=this.getBaseDao().search(sql.toString()+s+shorSql, scalarMap, NoticeRemindListBean.class, list.toArray(), 1, 1);
				//货主没有，在查一条车主的
				if(huoList!=null&&huoList.size()>0){
					noticeRemindList.add(huoList.get(0));
				}else{
					 s=" and id<? ";
					 list.remove(3);
					list.add( nrlb.getId());
					List <NoticeRemindListBean> cheList=this.getBaseDao().search(sql.toString()+s+shorSql, scalarMap, NoticeRemindListBean.class, list.toArray(), 1, 1);
					if(cheList!=null&&cheList.size()>0){
						noticeRemindList.add(cheList.get(0));
					}
				}
			}else{//第一条是货主就查车主有没有
				String s=" and type1=? ";
				list.add("1");
				List <NoticeRemindListBean> cheList=this.getBaseDao().search(sql.toString()+s+shorSql, scalarMap, NoticeRemindListBean.class, list.toArray(), 1, 1);
				//车主没有，在查一条货主的
				if(cheList!=null&&cheList.size()>0){
					noticeRemindList.add(cheList.get(0));
				}else{
					 s=" and id<? ";
					 list.remove(3);
					list.add( nrlb.getId());
					List <NoticeRemindListBean> huoList=this.getBaseDao().search(sql.toString()+s+shorSql, scalarMap, NoticeRemindListBean.class, list.toArray(), 1, 1);
					if(huoList!=null&&huoList.size()>0){
						noticeRemindList.add(huoList.get(0));
					}
				}
			}
			
			String updateSql="update tyt_notice_remind set receive_status=:receiveStatus,receive_time=now(),utime=now() where receive_id=:receive_id and receive_status=:receiveStatus2 and status=:status and id <=:ids";
			Map<String ,Object> map =new HashMap<String,Object>();
			map.put("receiveStatus", "1");
			map.put("receive_id", userId);
			map.put("receiveStatus2", "0");
			map.put("status", "0");
			map.put("ids", nrlb.getId());
			
			int n=this.getBaseDao().executeUpdateSql(updateSql, map);
			logger.info("查出{}条通知数据，修改了{}条通知数据为已传输",noticeRemindList.size(),n);
		}
		
		return noticeRemindList;
	}
	
	
	public void saveNoticeRemind(String type1, String type2, String msgId,
			Long productionId, Long receiveId) {
		//发弹窗通知
		TytNoticeRemindTmpl tytNoticeRemindTmpl=tytNoticeRemindTmplService.getTytNoticeRemindTmpl(type1,type2);
		if(tytNoticeRemindTmpl!=null){
			TytNoticeRemind tytNoticeRemind=new TytNoticeRemind();
			tytNoticeRemind.setType1(tytNoticeRemindTmpl.getType1());
			tytNoticeRemind.setType2(tytNoticeRemindTmpl.getType2());
			tytNoticeRemind.setPriority(tytNoticeRemindTmpl.getPriority());
			tytNoticeRemind.setIsPopup(tytNoticeRemindTmpl.getIsPopup());
			tytNoticeRemind.setContent(tytNoticeRemindTmpl.getContent());
			tytNoticeRemind.setMsgId(msgId);
			tytNoticeRemind.setProductionId(productionId);
			tytNoticeRemind.setReceiveId(receiveId);
			tytNoticeRemind.setCtime(new Date());
			tytNoticeRemind.setReceiveStatus("0");
			tytNoticeRemind.setStatus("0");
			this.getBaseDao().insert(tytNoticeRemind);
		}
		
	}
	

	public List<NoticeRemindListBean> updateGetUserNoticePopupListAll(Long userId){
		StringBuffer sql = new StringBuffer("select id,type1,type2,is_popup isPopup,content,msg_id msgId,production_id productionId,ctime from tyt_notice_remind where 1=1 ");
		List<Object> list = new ArrayList<Object>();
		sql.append(" and receive_id=?");
		list.add(userId);
		sql.append(" and receive_status=?");
		list.add("0");
		sql.append(" and status=?");
		list.add("0");
		//排序
		String shorSql=" order by id desc ";
		
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("type1", Hibernate.STRING);
		scalarMap.put("type2", Hibernate.STRING);		
		scalarMap.put("isPopup", Hibernate.STRING);
		scalarMap.put("content", Hibernate.STRING);
		scalarMap.put("msgId", Hibernate.STRING);
		scalarMap.put("productionId", Hibernate.LONG);
		scalarMap.put("ctime", Hibernate.TIMESTAMP);
		List <NoticeRemindListBean> noticeRemindList=this.getBaseDao().search(sql.toString()+shorSql, scalarMap, NoticeRemindListBean.class, list.toArray());
		//修改通知为已读
		if(noticeRemindList!=null && noticeRemindList.size()>0){
			NoticeRemindListBean nrlb=noticeRemindList.get(0);
			String updateSql="update tyt_notice_remind set receive_status=:receiveStatus,receive_time=now(),utime=now() where receive_id=:receive_id and receive_status=:receiveStatus2 and status=:status and id <=:ids";
			Map<String ,Object> map =new HashMap<String,Object>();
			map.put("receiveStatus", "1");
			map.put("receive_id", userId);
			map.put("receiveStatus2", "0");
			map.put("status", "0");
			map.put("ids", nrlb.getId());
			
			int n=this.getBaseDao().executeUpdateSql(updateSql, map);
			logger.info("查出{}条通知数据，修改了{}条通知数据为已传输",noticeRemindList.size(),n);
		}

		return noticeRemindList;
	}

	@Override
	public List<NoticeRemindListBean> updateUserNoticePopupList(Long userId, Integer versionType, Integer time) {

		//用户权益弹框要求只弹最后一条，所以优先查询
		List<NoticeRemindListBean> remindListBeans = updateRightsNoticePopupList(userId, versionType, time, 100);
		if(remindListBeans!=null && remindListBeans.size()>0){
			return remindListBeans;
		}

		StringBuffer sql = new StringBuffer("select id,type1,type2,is_popup isPopup,content,msg_id msgId,production_id productionId,ctime from tyt_notice_remind  force index(IDX_RECEIVEID_TIME)  where 1=1 ");
		List<Object> list = new ArrayList<Object>();
		sql.append(" and receive_id=?");
		list.add(userId);
		sql.append(" and receive_status=?");
		list.add("0");
		sql.append(" and status=?");
		list.add("0");
		if(null != versionType && versionType.intValue()>0){
			sql.append(" and version_type=? ");
			list.add(versionType);
		}
		sql.append(" AND ctime>DATE_SUB(CURDATE(), INTERVAL ? DAY) ");
		list.add(time);
		//排序
		String shorSql=" order by id LIMIT 1 ";

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("type1", Hibernate.STRING);
		scalarMap.put("type2", Hibernate.STRING);
		scalarMap.put("isPopup", Hibernate.STRING);
		scalarMap.put("content", Hibernate.STRING);
		scalarMap.put("msgId", Hibernate.STRING);
		scalarMap.put("productionId", Hibernate.LONG);
		scalarMap.put("ctime", Hibernate.TIMESTAMP);
		List<NoticeRemindListBean> noticeRemindList = this.getBaseDao().search(sql.toString() + shorSql, scalarMap, NoticeRemindListBean.class, list.toArray());
		//修改通知为已读
		if(noticeRemindList!=null && noticeRemindList.size()>0){
//			int num=0;
//			List<NoticeRemindListBean> removeList=new ArrayList<>();
//			for(NoticeRemindListBean bean:noticeRemindList){
//				if(bean.getType1().equals("100")){
//					num++;
//					if(num>1){
//						removeList.add(bean);
//					}
//				}
//			}
//			noticeRemindList.removeAll(removeList);
			NoticeRemindListBean nrlb=noticeRemindList.get(0);
			String updateSql="update tyt_notice_remind set receive_status=:receiveStatus,receive_time=now(),utime=now() where receive_id=:receive_id and receive_status=:receiveStatus2 and status=:status and id =:ids";
			Map<String ,Object> map =new HashMap<String,Object>();
			map.put("receiveStatus", "1");
			map.put("receive_id", userId);
			map.put("receiveStatus2", "0");
			map.put("status", "0");
			map.put("ids", nrlb.getId());

			int n=this.getBaseDao().executeUpdateSql(updateSql, map);
			logger.info("查出{}条通知数据，修改了{}条通知数据为已传输",noticeRemindList.size(),n);
		}

		return noticeRemindList;

	}

	public List<NoticeRemindListBean> updateRightsNoticePopupList(Long userId, Integer versionType, Integer time,Integer type1){

		StringBuffer sql = new StringBuffer("select id,type1,type2,is_popup isPopup,content,msg_id msgId,production_id productionId,ctime from tyt_notice_remind  force index(IDX_RECEIVEID_TIME)  where 1=1 ");
		List<Object> list = new ArrayList<Object>();
		sql.append(" and receive_id=?");
		list.add(userId);
		sql.append(" and receive_status=?");
		list.add("0");
		sql.append(" and status=?");
		list.add("0");
		if(null != versionType && versionType.intValue()>0){
			sql.append(" and version_type=? ");
			list.add(versionType);
		}
		sql.append(" and type1=?");
		list.add(type1);
		sql.append(" AND ctime>DATE_SUB(CURDATE(), INTERVAL ? DAY) ");
		list.add(time);
		//排序
		String shorSql=" order by id desc LIMIT 1 ";

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("type1", Hibernate.STRING);
		scalarMap.put("type2", Hibernate.STRING);
		scalarMap.put("isPopup", Hibernate.STRING);
		scalarMap.put("content", Hibernate.STRING);
		scalarMap.put("msgId", Hibernate.STRING);
		scalarMap.put("productionId", Hibernate.LONG);
		scalarMap.put("ctime", Hibernate.TIMESTAMP);
		List<NoticeRemindListBean> noticeRemindList = this.getBaseDao().search(sql.toString() + shorSql, scalarMap, NoticeRemindListBean.class, list.toArray());
		if(noticeRemindList!=null && noticeRemindList.size()>0){
			NoticeRemindListBean nrlb=noticeRemindList.get(0);
			String updateSql="update tyt_notice_remind set receive_status=:receiveStatus,receive_time=now(),utime=now() where receive_id=:receive_id and receive_status=:receiveStatus2 and status=:status and id <=:ids and type1=:type1 ";
			Map<String ,Object> map =new HashMap<String,Object>();
			map.put("receiveStatus", "1");
			map.put("receive_id", userId);
			map.put("receiveStatus2", "0");
			map.put("status", "0");
			map.put("ids", nrlb.getId());
			map.put("type1", type1);
			int n=this.getBaseDao().executeUpdateSql(updateSql, map);
			logger.info("查出{}条通知数据，修改了{}条通知数据为已传输",noticeRemindList.size(),n);
		}
		return noticeRemindList;
	}

	@Override
	public void saveRightsInfo(String type1, String type2, String defualtContent, Long userId) {
		String content = tytNoticeRemindTmplService.queryContent(type1, type2, defualtContent);
		// 100 1  使用会员身份认证未成功 恭喜您获得{count}次免费查看电话机会！认证身份后开始使用！
		// 100	2	身份认证成功提醒 恭喜您获得{count}次免费查看电话机会！可立即开始找货！
		// 100	4	车辆认证成功提醒 恭喜您获得{day}天试用会员！可免费试用{count}次查看电话机会！
		String[] needReplaceArr = null;
		String[] replaceArr = null;
		if ("100".equals(type1) && ("1".equals(type2) || "2".equals(type2))) {
			int allowCallNum = tytConfigService.getIntValue("rights_experice_call_num", 3);
			needReplaceArr = new String[]{"{count}"};
			replaceArr = new String[]{String.valueOf(allowCallNum)};
		} else if ("100".equals(type1) && "4".equals(type2)) {
			int normalCallNum = tytConfigService.getIntValue("rights_normal_call_num", 20);
			int normalCallDays = tytConfigService.getIntValue("rights_normal_call_day", 20);
			needReplaceArr = new String[]{"{day}", "{count}"};
			replaceArr = new String[]{String.valueOf(normalCallDays), String.valueOf(normalCallNum)};
		}
		if (needReplaceArr == null) {
			needReplaceArr = new String[0];
		}
		if (replaceArr == null) {
			replaceArr = new String[0];
		}
		content = StringUtils.replaceEach(content, needReplaceArr, replaceArr);
		TytNoticeRemind noticeRemind = new TytNoticeRemind();
		noticeRemind.setType1(type1);
		noticeRemind.setType2(type2);
		noticeRemind.setContent(content);
		noticeRemind.setCtime(new Date());
		noticeRemind.setIsPopup("1");
		noticeRemind.setReceiveId(userId);
		noticeRemind.setReceiveStatus("0");
		noticeRemind.setStatus("0");
		noticeRemind.setProductionId(0L);
		noticeRemind.setVersionType(2);
		noticeRemind.setUtime(new Date());
		add(noticeRemind);
	}


}
