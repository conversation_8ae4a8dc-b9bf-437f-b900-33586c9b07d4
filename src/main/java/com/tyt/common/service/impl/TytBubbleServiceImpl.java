package com.tyt.common.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytBubbleService;
import com.tyt.infofee.bean.InfoFeeMyPublishBubbleResultBean;
import com.tyt.model.TytBubble;
import com.tyt.model.User;
import com.tyt.transport.service.TransportService;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
@Service("tytBubbleService")
public class TytBubbleServiceImpl extends BaseServiceImpl<TytBubble,Long> implements TytBubbleService {
	
	@Resource(name = "transportService")
	TransportService transportService;

	@Resource(name = "userService")
	private UserService userService;

	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@Resource(name = "tytBubbleDao")
	public void setBaseDao(BaseDao<TytBubble, Long> tytBubbleDao) {
		super.setBaseDao(tytBubbleDao);
	}
	
	public List<TytBubble> getTytBubbleForUserId(Long userId){
		String hql="from TytBubble where userId=? and status=?";
		return this.getBaseDao().find(hql, userId,"0");
	}
	
	@Override
	public List<TytBubble> getNewBubbleForUserId(Long userId){
		String hql="from TytBubble where userId=? and status=? order by id desc ";
		return this.getBaseDao().find(hql, userId,"0");
	}
	public List<InfoFeeMyPublishBubbleResultBean> getInfoFeeMyPublishBubbleResultBeanForUserId(Long userId){
		List<InfoFeeMyPublishBubbleResultBean> bublleBeanList=new ArrayList<InfoFeeMyPublishBubbleResultBean>();
		List<TytBubble> list=this.getTytBubbleForUserId(userId);
		if(list!=null&& list.size()>0){
			for(TytBubble tytBubble:list){
				InfoFeeMyPublishBubbleResultBean infoFeeMyPublishBubbleResultBean=new InfoFeeMyPublishBubbleResultBean();
				BeanUtils.copyProperties(tytBubble, infoFeeMyPublishBubbleResultBean);
				bublleBeanList.add(infoFeeMyPublishBubbleResultBean);
			}
		}
		//获取货源发布中的发布条数
		Long myTodayGoodCount=transportService.getMyPublishaNbr(userId, 1,null);
		InfoFeeMyPublishBubbleResultBean myTodayGoodCountBubbleBean=new InfoFeeMyPublishBubbleResultBean();
		myTodayGoodCountBubbleBean.setType1("2");
		myTodayGoodCountBubbleBean.setType2("4");
		myTodayGoodCountBubbleBean.setIfFunction("1");
		//下边这一步如果用户发货多的话可能会损失条数,但是气泡条数设置的是Integer类型，暂时考虑到用户一天内的货物条数不会很多，多以Long转化成Integer
		myTodayGoodCountBubbleBean.setNumber(myTodayGoodCount.intValue());
		bublleBeanList.add(myTodayGoodCountBubbleBean);
		
		//电话调车气泡数
		User u=userService.getById(userId);
		Integer peopleOrdersCount=this.getPerpleOrders(u.getCellPhone());
		InfoFeeMyPublishBubbleResultBean peopleOrdersCountBubbleBean=new InfoFeeMyPublishBubbleResultBean();
		peopleOrdersCountBubbleBean.setType1("2");
		peopleOrdersCountBubbleBean.setType2("5");
		peopleOrdersCountBubbleBean.setIfFunction("1");
		//下边这一步如果用户发货多的话可能会损失条数,但是气泡条数设置的是Integer类型，暂时考虑到用户一天内的货物条数不会很多，多以Long转化成Integer
		peopleOrdersCountBubbleBean.setNumber(peopleOrdersCount.intValue());
		bublleBeanList.add(peopleOrdersCountBubbleBean);
				
		return bublleBeanList;
	}

    /**
     * @Description  查询用户气泡数(信息费版本新接口)
     * <AUTHOR>
     * @Date  2019/1/4 16:35
     * @Param [userId]
     * @return java.util.List<com.tyt.infofee.bean.InfoFeeMyPublishBubbleResultBean>
     **/
	public List<InfoFeeMyPublishBubbleResultBean> getInfoFeeMyPublishBubbleResultBeanForUserIdNew(Long userId){
		List<InfoFeeMyPublishBubbleResultBean> bublleBeanList=new ArrayList<InfoFeeMyPublishBubbleResultBean>();
		List<TytBubble> list=this.getTytBubbleForUserId(userId);
		if(list!=null&& list.size()>0){
			for(TytBubble tytBubble:list){
				InfoFeeMyPublishBubbleResultBean infoFeeMyPublishBubbleResultBean=new InfoFeeMyPublishBubbleResultBean();
				BeanUtils.copyProperties(tytBubble, infoFeeMyPublishBubbleResultBean);
				bublleBeanList.add(infoFeeMyPublishBubbleResultBean);
			}
		}
		//获取货源发布中的发布条数(信息费版本新接口)
		Long myTodayGoodCount=transportService.getMyPublishNew(userId, 1,null);
		InfoFeeMyPublishBubbleResultBean myTodayGoodCountBubbleBean=new InfoFeeMyPublishBubbleResultBean();
		myTodayGoodCountBubbleBean.setType1("2");
		myTodayGoodCountBubbleBean.setType2("4");
		myTodayGoodCountBubbleBean.setIfFunction("1");
		//下边这一步如果用户发货多的话可能会损失条数,但是气泡条数设置的是Integer类型，暂时考虑到用户一天内的货物条数不会很多，多以Long转化成Integer
		myTodayGoodCountBubbleBean.setNumber(myTodayGoodCount.intValue());
		bublleBeanList.add(myTodayGoodCountBubbleBean);

		//电话调车气泡数
		User u=userService.getById(userId);
		Integer peopleOrdersCount=this.getPerpleOrders(u.getCellPhone());
		InfoFeeMyPublishBubbleResultBean peopleOrdersCountBubbleBean=new InfoFeeMyPublishBubbleResultBean();
		peopleOrdersCountBubbleBean.setType1("2");
		peopleOrdersCountBubbleBean.setType2("5");
		peopleOrdersCountBubbleBean.setIfFunction("1");
		//下边这一步如果用户发货多的话可能会损失条数,但是气泡条数设置的是Integer类型，暂时考虑到用户一天内的货物条数不会很多，多以Long转化成Integer
		peopleOrdersCountBubbleBean.setNumber(peopleOrdersCount.intValue());
		bublleBeanList.add(peopleOrdersCountBubbleBean);

		return bublleBeanList;
	}
	public int  updateBubbleNumber(Long userId,String type1,String type2,int number){
		String sql=null;
		if(number==0){
			 sql="update tyt_bubble set number=0 ,utime=now() where user_id=? and type1=? and type2=?";
		}else if(number<0){
			 sql="update tyt_bubble set number=number-1 ,utime=now() where user_id=? and type1=? and type2=? and number>0";
		}else if(number>0){
			 sql="update tyt_bubble set number=number+1 ,utime=now() where user_id=? and type1=? and type2=?";
		}
		return this.getBaseDao().executeUpdateSql(sql, new Object []{userId,type1,type2});
	}
	
	public int getUserReceivedOrderNbr(Long userId){
		String sql="SELECT SUM(number) FROM tyt_bubble b WHERE b.user_id=? AND b.status=? ";
		BigDecimal nbr=this.getBaseDao().query(sql, new Object[]{userId,0});
		return nbr==null?0:nbr.intValue();
	}
	
	public int getPerpleOrders(String phone){
		String sql="SELECT count(1) FROM tyt_transport_mt b WHERE b.shipper_phone=? AND b.status=? and ctime>?";
		BigInteger nbr=this.getBaseDao().query(sql, new Object[]{phone,1,TimeUtil.formatDate(new Date())+" 00:00:00"});
		return nbr==null?0:nbr.intValue();
	}

	@Override
	public List<InfoFeeMyPublishBubbleResultBean> mergeBubble(List<InfoFeeMyPublishBubbleResultBean> cbubble, List<InfoFeeMyPublishBubbleResultBean> bbubble) {
		Map<String, InfoFeeMyPublishBubbleResultBean> bubbleMap = new HashMap<>();
		for (InfoFeeMyPublishBubbleResultBean cbean : cbubble) {
			bubbleMap.put(cbean.getType1() + cbean.getType2(), cbean);
		}
		if(bbubble != null) {
			for (InfoFeeMyPublishBubbleResultBean bbean : bbubble) {
				InfoFeeMyPublishBubbleResultBean tempBean = bubbleMap.get(bbean.getType1() + bbean.getType2());
				if (tempBean == null) {
					bubbleMap.put(bbean.getType1() + bbean.getType2(), bbean);
				} else {
					tempBean.setNumber(NumberUtils.toInt(String.valueOf(tempBean.getNumber()), 0)
							+ NumberUtils.toInt(String.valueOf(bbean.getNumber()), 0));
					bubbleMap.put(bbean.getType1() + bbean.getType2(), tempBean);
				}
			}
		}
		List<InfoFeeMyPublishBubbleResultBean> allBubble = new ArrayList<>();
		for (Map.Entry<String, InfoFeeMyPublishBubbleResultBean> entry : bubbleMap.entrySet()) {
			allBubble.add(entry.getValue());
		}
		return allBubble;
	}

	@Override
	public int getInfoFeeBubble(Long userId, int type1, int type2) {
		String sql="SELECT SUM(number) FROM `tyt_bubble` WHERE user_id=? AND STATUS=? AND type1=? AND type2=? ";
		BigDecimal nbr=this.getBaseDao().query(sql, new Object[]{userId,0,type1,type2});
		return nbr==null?0:nbr.intValue();
	}

	@Override
	public void updateRightsInfoBubble(Long userId) {
		this.getBaseDao().executeUpdateSql(
					"UPDATE tyt.`tyt_bubble` tb SET tb.`status`=? WHERE tb.`user_id`=? AND tb.`status`=? ",
					new Object[] { 1, userId, 0 });
	}

}
