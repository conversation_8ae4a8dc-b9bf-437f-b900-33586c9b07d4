package com.tyt.common.service.impl;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMapDictLogService;
import com.tyt.model.TytMapDictLog;
@Service("tytMapDictLogService")
public class TytMapDictLogServiceImpl extends BaseServiceImpl<TytMapDictLog,Long> implements TytMapDictLogService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	
	@Resource(name = "tytMapDictDao")
	public void setBaseDao(BaseDao<TytMapDictLog, Long> tytMapDictLogDao) {
		super.setBaseDao(tytMapDictLogDao);
	}
	
}
