package com.tyt.common.service.impl;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytBrowseLogService;
import com.tyt.model.TytBrowseLog;
@Service("tytBrowseLogService")
public class TytBrowseLogServiceImpl extends BaseServiceImpl<TytBrowseLog,String> implements TytBrowseLogService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@Resource(name = "tytBrowseLogDao")
	public void setBaseDao(BaseDao<TytBrowseLog, String> tytBrowseLogDao) {
		super.setBaseDao(tytBrowseLogDao);
	}

	
}
