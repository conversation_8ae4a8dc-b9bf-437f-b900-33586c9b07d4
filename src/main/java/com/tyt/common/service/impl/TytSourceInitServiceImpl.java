package com.tyt.common.service.impl;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.common.service.TytSourceInitService;
import com.tyt.util.TytSourceUtil;
@Service
public class TytSourceInitServiceImpl implements TytSourceInitService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@PostConstruct
	@Override
	public void initSource() {
		TytSourceUtil.init();		
	}
	
	
}
