package com.tyt.common.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.InternalEmployeeService;
import com.tyt.model.TytInternalEmployee;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("internalEmployeeService")
public class InternalEmployeeServiceImpl extends BaseServiceImpl<TytInternalEmployee,Long> implements InternalEmployeeService {

	@Resource(name = "internalEmployeeDao")
	public void setBaseDao(BaseDao<TytInternalEmployee, Long> internalEmployeeDao) {
		super.setBaseDao(internalEmployeeDao);
	}

	@Override
	public TytInternalEmployee getByPhone(String phone){
		String sql = "select * from tyt_internal_employee where login_phone_no = ? and is_valid = ? order by id desc";
		List<TytInternalEmployee> list = this.getBaseDao().queryForList(sql, new Object[]{phone, 1});
		if (list!=null && list.size()>0) {
			return list.get(0);
		}
		return null;
	}
}
