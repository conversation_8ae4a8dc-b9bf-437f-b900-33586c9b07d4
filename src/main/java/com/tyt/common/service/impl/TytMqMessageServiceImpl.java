package com.tyt.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.RefundReasonTypeEnum;
import com.tyt.message.bean.TytTransportBackendMessageBean;
import com.tyt.message.mqservice.IMqProducer;
import com.tyt.model.TytMqMessage;
import com.tyt.model.TytTransportOrders;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TytSwitchUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

//import com.tyt.message.mqservice.IMqRecommProducer;

@Service("tytMqMessageService")
public class TytMqMessageServiceImpl extends BaseServiceImpl<TytMqMessage, Long> implements TytMqMessageService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "tytMqMessageDao")
	public void setBaseDao(BaseDao<TytMqMessage, Long> TytMqMessageDao) {
		super.setBaseDao(TytMqMessageDao);
	}

	@Resource(name = "mqTransactionProducer")
	private IMqProducer producer;

	@Resource(name = "mbMqTransactionProducer")
	private IMqProducer mbMqProducer;

	@Autowired
	private TytConfigService tytConfigService;

//	@Resource
//	private IMqRecommProducer recommProducer;

	@Override
	public void addSaveMqMessage(String serialNum, String messageContent, int messageType) {
		String sql = "INSERT INTO `tyt`.`tyt_mq_message` ( `message_serial_num`, `message_content`, `create_time`, `message_type`) VALUES (?, ?, ?, ?)";
		Object[] params = new Object[] { serialNum, messageContent, new Date(), messageType };
		this.getBaseDao().executeUpdateSql(sql, params);
		// producer.sendMsg(messageContent, null,serialNum, null);
	}

	@Override
	public void addSaveRecommendMqMessage(String serialNum, String messageContent, int messageType) {
//		String sql = "INSERT INTO `tyt`.`tyt_mq_msg_record` ( `message_serial_num`, `message_content`, `create_time`, `message_type`) VALUES (?, ?, ?, ?)";
//		Object[] params = new Object[] { serialNum, messageContent, new Date(), messageType };
//		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public MqMoneyRefundMsg addRefundMqMessage(List<String> orderIds, List<String> userIds) throws Exception {
		return this.addRefundMqMessage(orderIds, userIds, null);
	}

	@Override
	public MqMoneyRefundMsg addRefundMqMessage(List<String> orderIds, List<String> userIds, Integer refundType) throws Exception {
		MqMoneyRefundMsg moneyRefundMsg = new MqMoneyRefundMsg();
		String serialNum = SerialNumUtil.generateSeriaNum();
		moneyRefundMsg.setMessageSerailNum(serialNum);
		moneyRefundMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_RERUND);
		moneyRefundMsg.setOrderIds(orderIds);
		moneyRefundMsg.setUserId(userIds);
		if(refundType != null) { // 新增，兼容其它退款类型
			moneyRefundMsg.setRefundType(refundType);
		}
		this.addSaveMqMessage(serialNum, JSON.toJSONString(moneyRefundMsg), MqBaseMessageBean.MESSAGETYPE_RERUND);
		return moneyRefundMsg;
	}

	@Override
	public void sendRefundMqMessage(MqMoneyRefundMsg mqMoneyRefundMsg) throws Exception {
		this.sendMqMessage(mqMoneyRefundMsg.getMessageSerailNum(), JSON.toJSONString(mqMoneyRefundMsg), MqBaseMessageBean.MESSAGETYPE_RERUND);
	}

	@Override
	public void sendASRTaskdMqMessageDistribute(ASRTaskMsg asrTaaskMsg) {
		this.sendMqMessage(asrTaaskMsg.getMessageSerailNum(), JSON.toJSONString(asrTaaskMsg), MqBaseMessageBean.ASR_TASK_CREATE_MESSAGE_DISTRIBUTE);
	}

	@Override
	public ASRTaskMsg addASRTaskdMqMessageDistribute(String callId, Long srcMsgId, Long carUserId) {
		ASRTaskMsg asrTaskMsg = new ASRTaskMsg();
		String serialNum = SerialNumUtil.generateSeriaNum();
		asrTaskMsg.setMessageSerailNum(serialNum);
		asrTaskMsg.setMessageType(MqBaseMessageBean.ASR_TASK_CREATE_MESSAGE_DISTRIBUTE);
		asrTaskMsg.setCallId(callId);
		asrTaskMsg.setSrcMsgId(srcMsgId);
		asrTaskMsg.setCarUserId(carUserId);
		this.addSaveMqMessage(serialNum, JSON.toJSONString(asrTaskMsg), MqBaseMessageBean.ASR_TASK_CREATE_MESSAGE_DISTRIBUTE);
		return asrTaskMsg;
	}

	@Override
	public void sendMqMessage(String serialNum, String messageContent, int messageType) {
		producer.sendMsg(messageContent, null, serialNum, null,null);
	}

	@Override
	public void sendMqMessageDirect(String serialNum, String messageContent, Long delayedTime) {
		producer.sendMsgDirect(messageContent, null, serialNum, null,delayedTime);
	}

	@Override
	public void sendRecommendMqMessage(String serialNum, String messageContent, int messageType) {
//		recommProducer.sendMsg(messageContent, null, serialNum, null);
	}

	@Override
	public boolean sendMqMessageWithResult(String serialNum, String messageContent, int messageType) {
		SendResult sendResult = producer.sendMsg(messageContent, null, serialNum, null,null);
		return sendResult != null;
	}

	@Override
	public void sendDelayedMqMessage(String serialNum, String messageContent, int messageType, long delayTimeInMilli) {
		producer.sendMsg(messageContent, null, serialNum, null, delayTimeInMilli);
	}

	@Override
    public void saveAndSendInfofeeMq(TytTransportOrders orders, Integer opStatus, Integer refundType){
        MqInfoFeeOperateMsg operateMsg = new MqInfoFeeOperateMsg();
		//此处新增131 是和13一样的逻辑 新增技术服务费全额退款标识 为避免修改saveAndSendInfofeeMq多次入参调用处
		if(opStatus==131){
			opStatus=13;
			operateMsg.setTecRefundType(1);
		}
        operateMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        operateMsg.setMessageType(MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
        operateMsg.setOpStatus(opStatus);
        if (opStatus == 3 || opStatus == 4 ||opStatus == 19){
            operateMsg.setAmount(orders.getRefundAmount()==null?"0":orders.getRefundAmount()+"");
        }else{
            operateMsg.setAmount(orders.getPayAmount()+"");
        }

        operateMsg.setCarOwnerUserId(orders.getPayUserId());
        operateMsg.setShipperUserId(orders.getUserId());
        operateMsg.setStartPoint(orders.getStartPoint());
        operateMsg.setDestPoint(orders.getDestPoint());
        operateMsg.setTaskContent(orders.getTaskContent());
        operateMsg.setTsId(orders.getTsId());
        operateMsg.setTsOrderNo(orders.getTsOrderNo());
        operateMsg.setOrderId(orders.getId());
		operateMsg.setPayNo(orders.getPayNo());
        //存在服务费
        if(Objects.nonNull(orders.getPayServiceCharge())){
			operateMsg.setInfoFeeServiceFee(orders.getPayServiceCharge().toString());
		}
        if (refundType != null && refundType>0){
            operateMsg.setRefundType(refundType);
        }
        //为兼容新增opStatus==13
		// 此处传递过来的refundType 代表退款申请方 1 车方 2 货方
		//operateMsg 中的refundType  1代表全额退款 2 代表部分退款
		if(opStatus==13){
			operateMsg.setRefundOriginator(refundType);
			operateMsg.setRefundType(1);
		}

		operateMsg.setTotalOrderAmount(orders.getTotalOrderAmount().toString());
		operateMsg.setTecServiceFee(orders.getTecServiceFee().toString());
		//当类型为19时 设置技术服务费分配金额
		if(opStatus==19){
			operateMsg.setPlatformServiceAmount(orders.getPlatformServiceAmount()==null?"":String.valueOf(orders.getPlatformServiceAmount().doubleValue()/100));
			operateMsg.setCarServiceAmount(orders.getCarServiceAmount()==null?"":String.valueOf(orders.getCarServiceAmount().doubleValue()/100));
		}
		// 建立线程池
		ExecutorService executorService = Executors.newSingleThreadExecutor();
		executorService.execute(() -> {
			//货源成交平台  0 特运通  1:满帮
			Integer thirdpartyPlatformType = orders.getThirdpartyPlatformType();
			if(thirdpartyPlatformType != null && thirdpartyPlatformType == 1) {
				if(TytSwitchUtil.isMbOrderOperateMqOn()){
					sendMsgCustom(JSON.toJSONString(operateMsg), "TRADE_INFO_FEE_TOPIC", operateMsg.getMessageSerailNum(), "manbang_order", 3000L);
				}else{
					//发送并mq信息并保存到数据库
					addSaveMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg), MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
					sendMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg),MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
				}
			}else{
				if (TytSwitchUtil.isTytOrderOperateMqOn()) {
					sendMsgCustom(JSON.toJSONString(operateMsg), "TRADE_INFO_FEE_TOPIC", operateMsg.getMessageSerailNum(), "tyt_order", 3000L);
				}else{
					//发送并mq信息并保存到数据库
					addSaveMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg), MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
					sendMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg),MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
				}
			}
		});
		// 关闭线程
		executorService.shutdown();
	}

	/**
	 * @description 保存并发送满帮MQ消息
	 * <AUTHOR>
	 * @date 2022/10/21 9:39
	 * @param orders
	 * @return void
	 */
	@Override
	public void saveAndSendMbInfofeeMq(TytTransportOrders orders) {

		MqOrderRefundDepositBean orderRefundDepositBean = new MqOrderRefundDepositBean();
		orderRefundDepositBean.setOrderId(orders.getThirdpartyPlatformOrderNo());
		//退款原因
		String refundReason = orders.getRefundReason();
		//退订金原因类型
		Integer refundReasonType = RefundReasonTypeEnum.其他.getCode();
		//退订金原因描述
		String refundReasonDesc = RefundReasonTypeEnum.其他.getMsg();
		if(StringUtils.isNotBlank(refundReason)){
			switch (refundReason) {
				case "已装货,退还订金":
					refundReasonType = RefundReasonTypeEnum.司机到厂装货.getCode();
					refundReasonDesc = RefundReasonTypeEnum.司机到厂装货.getMsg();
					break;
				case "货已送达,退还订金":
					refundReasonType = RefundReasonTypeEnum.货已送达.getCode();
					refundReasonDesc = RefundReasonTypeEnum.货已送达.getMsg();
					break;
				case "已装货,协商退款":
					refundReasonType = RefundReasonTypeEnum.已装货.getCode();
					refundReasonDesc = RefundReasonTypeEnum.已装货.getMsg();
					break;
				case "货已送达,协商退款":
					refundReasonType = RefundReasonTypeEnum.已送达.getCode();
					refundReasonDesc = RefundReasonTypeEnum.已送达.getMsg();
					break;
				case "未装货,协商退款":
					refundReasonType = RefundReasonTypeEnum.司机原因.getCode();
					refundReasonDesc = RefundReasonTypeEnum.司机原因.getMsg();
					break;
				case "货源取消,协商退款":
					refundReasonType = RefundReasonTypeEnum.厂家不发货.getCode();
					refundReasonDesc = RefundReasonTypeEnum.厂家不发货.getMsg();
					break;
			}
		}
		orderRefundDepositBean.setRefundReasonType(refundReasonType);
		orderRefundDepositBean.setRefundReasonDesc(refundReasonDesc);

		String serialNum = SerialNumUtil.generateSeriaNum();
		orderRefundDepositBean.setMessageSerailNum(serialNum);
		orderRefundDepositBean.setMessageType(MqBaseMessageBean.MB_REFUND_DEPOSIT_ORDER_MESSAGE);

		// 建立线程池
		ExecutorService executorService = Executors.newSingleThreadExecutor();
		executorService.execute(() -> {
			//发送并mq信息并保存到数据库
			addSaveMqMessage(orderRefundDepositBean.getMessageSerailNum(), JSON.toJSONString(orderRefundDepositBean), MqBaseMessageBean.MB_REFUND_DEPOSIT_ORDER_MESSAGE);
            sendMbMqMessage(orderRefundDepositBean.getMessageSerailNum(), JSON.toJSONString(orderRefundDepositBean), MqBaseMessageBean.MB_REFUND_DEPOSIT_ORDER_MESSAGE);
		});
		// 关闭线程
		executorService.shutdown();
	}

	/***
	 * @description: 保存并发送特运通司机申请退还订金MQ消息
	 * @author: zgz
	 * @date: 2023/8/18 17:07
	 * @version: 1.0.0
	 * @param: orderId
	 * @param: reasonId
	 * @param: otherReason
	 * @return: void
	 * @throws:
	 */
	@Override
	public void saveAndSendDriverRefundDepositMq(String orderId, Integer reasonId, String otherReason) {
		DriverRefundDepositRequest driverRefundDepositRequest = new DriverRefundDepositRequest();
		driverRefundDepositRequest.setOrderId(orderId);
		driverRefundDepositRequest.setReasonId(reasonId);
		driverRefundDepositRequest.setOtherReason(otherReason);

		String serialNum = SerialNumUtil.generateSeriaNum();
		driverRefundDepositRequest.setMessageSerailNum(serialNum);
		driverRefundDepositRequest.setMessageType(MqBaseMessageBean.TYT_DRIVER_REFUND_DEPOSIT_MESSAGE);

		// 建立线程池
		ExecutorService executorService = Executors.newSingleThreadExecutor();
		executorService.execute(() -> {
			//发送并mq信息并保存到数据库
			addSaveMqMessage(driverRefundDepositRequest.getMessageSerailNum(), JSON.toJSONString(driverRefundDepositRequest), MqBaseMessageBean.TYT_DRIVER_REFUND_DEPOSIT_MESSAGE);
			sendMbMqMessage(driverRefundDepositRequest.getMessageSerailNum(), JSON.toJSONString(driverRefundDepositRequest), MqBaseMessageBean.TYT_DRIVER_REFUND_DEPOSIT_MESSAGE);
		});
		// 关闭线程
		executorService.shutdown();
	}

	@Override
    public void sendSms(String cellPhone, String message) {
        ShortMsgBean shortMsgBean = new ShortMsgBean();
        shortMsgBean.setContent(message);
        shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
        String messageSerailNum = SerialNumUtil.generateSeriaNum();
        shortMsgBean.setMessageSerailNum(messageSerailNum);
        shortMsgBean.setCell_phone(cellPhone);
        shortMsgBean.setRemark("");
        addSaveMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
        sendMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean),MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
    }

	@Override
	public void sendTransportBackendMessageMq(Long srcMsgId,String type) {
		TytTransportBackendMessageBean message = new TytTransportBackendMessageBean();
		String messageSerailNum = SerialNumUtil.generateSeriaNum();
		message.setMessageSerailNum(messageSerailNum);
		message.setType(type);
		message.setSrcMsgId(srcMsgId);
		message.setMessageType(MqBaseMessageBean.TRANSPORT_BACKEND_MESSAGE);
		addSaveMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message),message.getMessageType());
		sendMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message),message.getMessageType());
	}

	@Override
	public void sendMqMessageTransportStatus(String serialNum, String messageContent, long delayedTime){
		producer.sendMsgDirect(messageContent , null, serialNum, null,delayedTime);

	}

	@Override
	public void sendMbMqMessage(String serialNum, String messageContent, int messageType) {
		mbMqProducer.sendMsg(messageContent, null, serialNum, null,null);
	}

	@Override
	public void sendDingDingMq(String enterpriseName, String customerManagerPhone, Long carUserId, String tsOrderNo) {
		DingDingPushMsg pushMsg=new DingDingPushMsg();
		pushMsg.setCarUserId(carUserId);
		pushMsg.setEnterpriseName(enterpriseName);
		pushMsg.setCustomerManagerPhone(customerManagerPhone);
		pushMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
		pushMsg.setMessageType(MqBaseMessageBean.DING_DING_PUSH_MESSAGE);
		pushMsg.setTsOrderNo(tsOrderNo);
		logger.info("sendDingDingMq pushMsg:{}",JSON.toJSONString(pushMsg));
		int dingdingSwitch = tytConfigService.getIntValue("trade_center_mq_dingding_push_switch", 0);
		if (1 == dingdingSwitch){
			sendMsgCustom(JSON.toJSONString(pushMsg), "TRADE_CENTER_TOPIC", pushMsg.getMessageSerailNum(), "ding_push", null);
		}else{
			addSaveMqMessage(pushMsg.getMessageSerailNum(),JSON.toJSONString(pushMsg), MqBaseMessageBean.DING_DING_PUSH_MESSAGE);
			sendMqMessage(pushMsg.getMessageSerailNum(), JSON.toJSONString(pushMsg), MqBaseMessageBean.DING_DING_PUSH_MESSAGE);
		}


	}

	@Override
	public void sendMsgCustom(String messages, String topic, String key, String tag, Long delayedTime) {
		producer.sendMsgCustom(messages, topic, key, tag, delayedTime);
	}

}
