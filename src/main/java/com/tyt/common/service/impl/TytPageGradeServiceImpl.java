package com.tyt.common.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.common.bean.SimplePageGradeBean;
import com.tyt.common.service.TytPageGradeService;
import com.tyt.model.TytPageGrade;
import com.tyt.util.Constant;

@Service("tytPageGradeService")
public class TytPageGradeServiceImpl extends BaseServiceImpl<TytPageGrade,Long> implements TytPageGradeService {
   
   @Resource(name = "cacheServiceMcImpl")
   CacheService cacheService;
   
   public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	
	@Resource(name = "tytPageGradeDao")
	public void setBaseDao(BaseDao<TytPageGrade, Long> tytPageGradeDao) {
		super.setBaseDao(tytPageGradeDao);
	}

	@SuppressWarnings("deprecation")
	@Override
	public List<SimplePageGradeBean> getByTypeFromDB(String type) throws Exception {
		StringBuffer selectSql=new StringBuffer("select grade_code gradeCode,value_begin_range valueBeginRange,"
				+ "value_end_range valueEndRange,chinese_mean chineseMean,value_unit valueUnit,order_number orderNumber,type"
				+ " from tyt_page_grade");
		List<Object> params=new ArrayList<Object>();
		if(type==null||type.trim().equals("")||type.equals("0")){//0表示查询全部
			selectSql.append(" where status=?");
			params.add(1);
		}else{
			selectSql.append(" where type=? and status=?");
			params.add(type);
			params.add(1);
		}
		selectSql.append(" order by order_number asc");
		Map<String, org.hibernate.type.Type> scalarMap=new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("gradeCode", Hibernate.STRING);
		scalarMap.put("valueBeginRange", Hibernate.INTEGER);
		scalarMap.put("valueEndRange", Hibernate.INTEGER);
		scalarMap.put("chineseMean", Hibernate.STRING);
		scalarMap.put("valueUnit", Hibernate.STRING);
		scalarMap.put("orderNumber", Hibernate.INTEGER);
		scalarMap.put("type", Hibernate.STRING);
		List<SimplePageGradeBean> simpleGrades=this.getBaseDao().search(selectSql.toString(), scalarMap, SimplePageGradeBean.class, params.toArray());
		return simpleGrades;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<SimplePageGradeBean> getByType(String type) throws Exception{
		Object obj=cacheService.getObject(Constant.CACHE_PAGE_GRADE+type);
		logger.info("PageGradeBean getByType cacheGetObject:结果集【{}】,cacheKey【{}】",obj,Constant.CACHE_PAGE_GRADE+type);
		List<SimplePageGradeBean> simpleGrades=null;
		if(obj==null){
			simpleGrades=this.getByTypeFromDB(type);
			if(simpleGrades!=null&&simpleGrades.size()>0){
				cacheService.setObject(Constant.CACHE_PAGE_GRADE+type,simpleGrades,Constant.CACHE_EXPIRE_TIME_24H);
				for(SimplePageGradeBean bean:simpleGrades){
					cacheService.setObject(Constant.CACHE_PAGE_GRADE+type+"_"+bean.getGradeCode(),bean,Constant.CACHE_EXPIRE_TIME_24H);
				}
			}
		}else{
			simpleGrades=(List<SimplePageGradeBean>) obj;
		}
		logger.info("PageGradeBean getByType:type【{}】,结果集【{}】,cacheKey【{}】",type,simpleGrades,Constant.CACHE_PAGE_GRADE+type);
		return simpleGrades;
	}

	@Override
	public SimplePageGradeBean getByType(String type, String gradeCode)
			throws Exception {
		Object obj=cacheService.getObject(Constant.CACHE_PAGE_GRADE+type+"_"+gradeCode);
		SimplePageGradeBean bean=null;
		logger.info("PageGradeBean getByType cacheGetObject:结果集【{}】,cacheKey【{}】",obj,Constant.CACHE_PAGE_GRADE+type+"_"+gradeCode);

		if(obj==null){
			List<SimplePageGradeBean> simpleGrades=this.getByType(type);
			for(SimplePageGradeBean simpleBean:simpleGrades){
				if(simpleBean.getGradeCode().equals(gradeCode)){
					bean=simpleBean;
					break;
				}
			}
		}else{
			bean=(SimplePageGradeBean) obj;
		}
		if(bean!=null){
			cacheService.setObject(Constant.CACHE_PAGE_GRADE+type+"_"+gradeCode,bean,Constant.CACHE_EXPIRE_TIME_24H);
		}
		logger.info("PageGradeBean getByType:type【{}】,gradeCode【{}】,结果集【{}】,cacheKey【{}】",type,gradeCode,bean,Constant.CACHE_PAGE_GRADE+type+"_"+gradeCode);
		return bean;
	}

}
