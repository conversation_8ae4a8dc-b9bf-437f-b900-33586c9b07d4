package com.tyt.common.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytMqMsgRecord;

public interface TytMqRecommMessageService extends BaseService<TytMqMsgRecord, Long> {
	/**
	 * 保存发送Mq
	 * 
	 * @param serialNum
	 *            消息序列号
	 * @param messageContent
	 *            消息内容
	 * @param messageType
	 *            消息类别
	 */
	public void addSaveMqMessage(String serialNum, String messageContent, int messageTypeInfofee);

	/**
	 * 
	 * @param serialNum
	 * @param messageContent
	 * @param messageTypeInfofee
	 * @param msgType
	 * @param timingSendTime
	 */
	public void addSaveMqMessage(String serialNum, String messageContent, int messageTypeInfofee, int msgType, long timingSendTime);

	/**
	 * 发送Mq
	 * 
	 * @param serialNum
	 *            消息序列号
	 * @param messageContent
	 *            消息内容
	 * @param messageType
	 *            消息类别
	 */
	public void sendMqMessage(String serialNum, String messageContent, int messageType);

	boolean sendMqMessageWithResult(String serialNum, String messageContent, int messageType);

	/**
	 * 保存定时MQ消息到数据库
	 * 
	 * @param serialNum
	 * @param messageContent
	 * @param messageType
	 * @param time
	 *            发送时间的毫秒值 一般System.currentTimeMillis() + 延时时间
	 */
	public void addSaveTimerMqMessage(String serialNum, String messageContent, int messageType, long time);

	/**
	 * 发送定时消息到MQ服务器
	 * 
	 * @param serialNum
	 * @param messageContent
	 * @param messageType
	 * @param time
	 *            发送时间的毫秒值 一般System.currentTimeMillis() + 延时时间
	 */
	public void sendTimerMqMessage(String serialNum, String messageContent, int messageType, long time);

	/**
	 * 发送定时消息到MQ服务器有返回值
	 * 
	 * @param serialNum
	 * @param messageContent
	 * @param messageType
	 * @param time
	 *            发送时间的毫秒值 一般System.currentTimeMillis() + 延时时间
	 */
	public boolean sendTimerMqMessageWithResult(String serialNum, String messageContent, int messageType, long time);

	/**
	 * 
	 * @param messageSerailNum
	 * @return
	 */
	boolean isMessageProcessed(String messageSerailNum);

	/**
	 * 更改状态
	 * 
	 * @param messageSerailNum
	 * @param mqMessageStatusDealFailed
	 */
	void updateMqMessageStatus(String messageSerailNum, int mqMessageStatusDealFailed);

}
