package com.tyt.common.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.PingAnPortCode;
import com.tyt.model.TytMapDict;

public interface TytMapDictService extends BaseService<TytMapDict,Long> {

	/**
	 * 保存一个地图字典，有就不保存
	 * @param tytMapDict
	 */
	public void saveMapDict(TytMapDict tytMapDict);
	
	/**
	 * 获得一个地图字典对象
	 * @param mapKey 
	 * @return TytMapDict
	 */
	public TytMapDict getTytMapDict(String mapKey);
	
	/**
	 * 根据 出发地 省市区 目的地省市区生成 mapKey
	 * @param tytMapDict
	 * @return
	 */
	public String getMapKey(TytMapDict tytMapDict);
	
	/**
	 * 根据 出发地 省市区 目的地省市区 获得两点距离 单位分米
	 * @param startProvinc
	 * @param startCity
	 * @param startArea
	 * @param destProvinc
	 * @param destCity
	 * @param destArea
	 * @return
	 */
	public TytMapDict getDistance(String clientSign,String startProvinc,String startCity,String startArea,String destProvinc,String destCity,String destArea);

	/**
	 * 根据省市名称找到对应的编码
	 * @param cityName
	 * @param provinceName
	 * @return
	 */
	PingAnPortCode fingPingAnPortCode(String cityName,String provinceName);

	int getDistanceCity(String city, String district, String transportCity, String transportArea);
}
