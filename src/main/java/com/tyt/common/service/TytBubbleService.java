package com.tyt.common.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.InfoFeeMyPublishBubbleResultBean;
import com.tyt.model.TytBubble;

public interface TytBubbleService extends BaseService<TytBubble,Long> {
	//查询用户气泡数
	public List<TytBubble> getTytBubbleForUserId(Long userId);	
	//查询用户气泡数
	public List<InfoFeeMyPublishBubbleResultBean> getInfoFeeMyPublishBubbleResultBeanForUserId(Long userId);

	//查询用户气泡数(信息费版本新接口)
	public List<InfoFeeMyPublishBubbleResultBean> getInfoFeeMyPublishBubbleResultBeanForUserIdNew(Long userId);
	/**
	 * 修改用户指定气泡的值 number是为0时清0为-1时减1，1时加1
	 * @param userId
	 * @param type1,具体内容请查看《APP接口文档》-气泡数量类型说明
	 * @param type2
	 * @param number
	 * @return
	 */
	public int  updateBubbleNumber(Long userId,String type1,String type2,int number);

	public int getUserReceivedOrderNbr(Long userId);
	
	public int getPerpleOrders(String phone);

	/**
	 * 气泡对象整合后返回
	 * @param cbubble A气泡列表
	 * @param bbubble B气泡列表
	 * @return 整合后的气泡对象
	 */
	List<InfoFeeMyPublishBubbleResultBean> mergeBubble(List<InfoFeeMyPublishBubbleResultBean> cbubble, List<InfoFeeMyPublishBubbleResultBean> bbubble);

	/**
	 * 获取信息费货源气泡数量
	 * @param userId
	 * @param type1
	 * @param type2
	 * @return
	 */
	int getInfoFeeBubble(Long userId, int type1, int type2);
	List<TytBubble> getNewBubbleForUserId(Long userId);
	public void updateRightsInfoBubble(Long userId);


}
