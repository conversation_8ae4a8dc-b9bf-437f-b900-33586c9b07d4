package com.tyt.common.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.ASRTaskMsg;
import com.tyt.infofee.bean.MqMoneyRefundMsg;
import com.tyt.model.TytMqMessage;
import com.tyt.model.TytTransportOrders;

public interface TytMqMessageService extends BaseService<TytMqMessage, Long> {
	/**
	 * 保存发送Mq
	 *
	 * @param serialNum
	 *            消息序列号
	 * @param messageContent
	 *            消息内容
	 * @param messageType
	 *            消息类别
	 */
	public void addSaveMqMessage(String serialNum, String messageContent, int messageTypeInfofee);

	/**
	 * 保存发送Mq
	 *
	 * @param serialNum
	 *            消息序列号
	 * @param messageContent
	 *            消息内容
	 * @param messageType
	 *            消息类别
	 */
	public void addSaveRecommendMqMessage(String serialNum, String messageContent, int messageTypeInfofee);

	/**
	 * 保存退款的MQ
	 *
	 * @param orderIds
	 *            要退款的订单ID集合
	 * @param userIds
	 *            要退款的支付者ID集合
	 * @throws Exception
	 */
	public MqMoneyRefundMsg addRefundMqMessage(List<String> orderIds, List<String> userIds) throws Exception;

	/**
	 * 保存退款的MQ，车方取消运单
	 *
	 * @param orderIds
	 *            要退款的订单ID集合
	 * @param userIds
	 *            要退款的支付者ID集合
	 * @param refundType
	 *            退款类型，处理发送短信的模板
	 * @throws Exception
	 */
	public MqMoneyRefundMsg addRefundMqMessage(List<String> orderIds, List<String> userIds, Integer refundType) throws Exception;

	/**
	 * 发送退款的MQ
	 *
	 * @param mqMoneyRefundMsg
	 *            退款Msg
	 * @throws Exception
	 */
	public void sendRefundMqMessage(MqMoneyRefundMsg mqMoneyRefundMsg) throws Exception;

	void sendASRTaskdMqMessageDistribute(ASRTaskMsg asrTaaskMsg);

	ASRTaskMsg addASRTaskdMqMessageDistribute(String callId, Long srcMsgId, Long carUserId);

	/**
	 * 发送Mq
	 *
	 * @param serialNum
	 *            消息序列号
	 * @param messageContent
	 *            消息内容
	 * @param messageType
	 *            消息类别
	 */
	public void sendMqMessage(String serialNum, String messageContent, int messageType);

	/**
	 * 发送直接提交的mq消息
	 * @param serialNum
	 * @param messageContent
	 * @param delayedTime
	 */
	void sendMqMessageDirect(String serialNum, String messageContent, Long delayedTime);

	boolean sendMqMessageWithResult(String serialNum, String messageContent, int messageType);

	/**
	 * 发送Mq
	 *
	 * @param serialNum
	 *            消息序列号
	 * @param messageContent
	 *            消息内容
	 * @param messageType
	 *            消息类别
	 */
	public void sendRecommendMqMessage(String serialNum, String messageContent, int messageType);

	/**
	 * 发送延迟mq消息
	 *
	 * @param serialNum
	 * @param messageContent
	 * @param messageType
	 * @param delayTimeInMilli
	 *            延迟的毫秒数
	 */
	void sendDelayedMqMessage(String serialNum, String messageContent, int messageType, long delayTimeInMilli);

    /**
     * 发送信息费mq
     * @param orders
     * @param opStatus
     * @param refundType
     */
    void saveAndSendInfofeeMq(TytTransportOrders orders, Integer opStatus, Integer refundType);

    /**
     * @description 保存并发送满帮MQ消息
     * <AUTHOR>
     * @date 2022/10/21 9:39
     * @param orders
     * @return void
     */
	void saveAndSendMbInfofeeMq(TytTransportOrders orders);

	/***
	* @description: 保存并发送特运通司机申请退还订金MQ消息
	* @author: zgz
	* @date: 2023/8/18 17:07
	* @version: 1.0.0
	* @param: orderId
    * @param: reasonId
    * @param: otherReason
	* @return: void
	* @throws:
	*/
	void saveAndSendDriverRefundDepositMq(String orderId, Integer reasonId, String otherReason);

    /**
     * 发送短信的mq
     * @param cellPhone
     * @param message
     */
    void sendSms(String cellPhone, String message);

    /**
     * 设为成交车 取消成交车 推送消息mq
     * @param srcMsgId
     * @param type
     * <AUTHOR>
     * @return
     * @date 2021-05-28 11:14:38
     */
    void sendTransportBackendMessageMq(Long srcMsgId,String type);

	/**
	 * 运输公司接单成功，推送mq消息
	 * @param messageSerailNum
	 * @param toJSONString
	 * @param mqDelayTime
	 */
	void sendMqMessageTransportStatus(String messageSerailNum, String toJSONString, long mqDelayTime);

	 void sendMbMqMessage(String serialNum, String messageContent, int messageType);

	/**
	 *  发送钉钉PUSH消息
	 * <AUTHOR>
	 * @param enterpriseName 企业名称
	 * @param customerManagerPhone 客户经理手机号
	 * @param carUserId 车主Id
	 * @param carUserId 车主Id
	 * @return void
	 */
	void sendDingDingMq(String enterpriseName, String customerManagerPhone, Long carUserId, String tsOrderNo);

	void sendMsgCustom(String messages, String topic, String key, String tag, Long delayedTime);

}
