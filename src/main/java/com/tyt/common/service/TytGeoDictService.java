package com.tyt.common.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytGeoDict;

public interface TytGeoDictService extends BaseService<TytGeoDict,String> {
/**
 *  通过 省市区获得坐标
 * @param provinc 省
 * @param city 市
 * @param area 区    区可以为空
 * @return TytGeoDict
 */
	public TytGeoDict getTytGeoDict(String provinc,String city,String area);
	/**
	 * 通过坐标获得 地区
	 * @param px 
	 * @param py
	 * @return
	 */
	public TytGeoDict getTytGeoDict(Integer px,Integer py);
	
	/**
	 * 通过出发地 目的地 算出距离
	 * @param startProvinc
	 * @param startCity
	 * @param startArea
	 * @param destProvinc
	 * @param destCity
	 * @param destArea
	 * @return
	 */
	public double getDistace(String startProvinc,String startCity,
			String startArea,
			String destProvinc,
			String destCity,
			String destArea);
}
