package com.tyt.common.service;

import com.tyt.base.service.BaseService;
import com.tyt.common.bean.TytMessageListTmplBean;
import com.tyt.model.TytMessageTmpl;

public interface TytMessageTmplService extends BaseService<TytMessageTmpl,Long> {
	/**
	 * 获得sms模板
	 * @param key sms key
	 * @return String 
	 */
	public String getSmsTmpl(String key);
	public String getSmsTmpl(String key, String defaultValue);
	/**
	 * 获得列表消息模板
	 * @param key 列表消息的 key
	 * @return  TytMessageListTmplBean
	 */
	public TytMessageListTmplBean getListMessage(String key);
	
	
}
