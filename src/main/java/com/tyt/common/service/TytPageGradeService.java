package com.tyt.common.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.common.bean.SimplePageGradeBean;
import com.tyt.model.TytPageGrade;

public interface TytPageGradeService extends BaseService<TytPageGrade,Long> {
	 
	/**
	 * 直接查询数据库，根据类型查询相应的分页级别配置
	 * @param type:类型(1PC我的发布)
	 * @return
	 * @throws Exception
	 */
	public List<SimplePageGradeBean> getByTypeFromDB(String type)throws Exception;
	/**
	 * 直接查询Memcache缓存，根据类型查询相应的分页级别配置
	 * @param type:类型(0查询所有；1PC我的发布)
	 * @return:json字符串
	 * @throws Exception
	 */
	public List<SimplePageGradeBean> getByType(String type) throws Exception;
	/**
	 * 根据类型，级别码查询记录
	 * @param type:类型(1PC我的发布)
	 * @param groupCode
	 * @return
	 * @throws Exception
	 */
	public SimplePageGradeBean getByType(String type,String gradeCode) throws Exception;
	
}
