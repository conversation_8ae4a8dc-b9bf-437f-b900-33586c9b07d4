package com.tyt.common.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytNoticeRemindTmpl;

public interface TytNoticeRemindTmplService extends BaseService<TytNoticeRemindTmpl,Long> {
	
	/**
	 * 获得一个通知弹窗模版 
	 * @param type1 类型1
	 * @param type2 类型2
	 * @return TytNoticeRemindTmpl
	 */
	public TytNoticeRemindTmpl getTytNoticeRemindTmpl(String type1,String type2);

	/**
	 * 查看模板内容
	 * @param type1
	 * @param type2
	 * @param defaultContent 默认模板内容
	 * @return
	 */
	String queryContent(String type1, String type2, String defaultContent);
}
