package com.tyt.common.service;

import com.tyt.base.service.BaseService;
import com.tyt.common.bean.NoticeRemindListBean;
import com.tyt.model.TytNoticeRemind;

import java.util.List;

public interface TytNoticeRemindService extends BaseService<TytNoticeRemind,Long> {
	/**
	 * 获得用户通知弹窗列表
	 * @param userId
	 * @return List<NoticeRemindListBean>
	 */
	public List<NoticeRemindListBean> updateGetUserNoticePopupList(Long userId);
	/**
	 * 保存弹窗通知
	 * @param type1 通知类型1
	 * @param type2 通知类型1
	 * @param msgId 消息ID
	 * @param productionId 通知人id(0系统)
	 * @param receiveId 被通知人id
	 * @throws Exception
	 */
	public void saveNoticeRemind(String type1, String type2, String msgId,
			Long productionId, Long receiveId);
	
	/**
	 * 获得用户通知弹窗列表
	 * @param userId
	 * @return List<NoticeRemindListBean>
	 */
	public List<NoticeRemindListBean> updateGetUserNoticePopupListAll(Long userId);

	void saveRightsInfo(String type1, String type2, String defualtContent, Long userId);
	/**
	 * 新增弹框通知 ，供APP使用（会员权益，卡券等）
	 * @param userId
	 * @param versionType
	 * @param time
	 * @return
	 */
	List<NoticeRemindListBean> updateUserNoticePopupList(Long userId, Integer versionType, Integer time);
}
