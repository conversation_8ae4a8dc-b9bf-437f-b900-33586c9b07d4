package com.tyt.common.controller;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.common.service.InternalEmployeeService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytInternalEmployee;
import com.tyt.transport.service.EmployeeMessageService;

@Controller
@RequestMapping("/plat/internalEmployee/")
public class InternalEmployeeController extends BaseController {
	
	@Resource(name = "internalEmployeeService")
	private InternalEmployeeService internalEmployeeService;
	@Resource(name = "employeeMessageService")
	private EmployeeMessageService employeeMessageService;
	
	@RequestMapping("getById")
	@ResponseBody
	public ResultMsgBean getemployee(@RequestParam(value = "id", required = true) String id) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			TytInternalEmployee employee = internalEmployeeService.getById(Long.valueOf(id));
			result.setCode(200);
			result.setData(employee.getRealName());
			result.setMsg("查询成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("服务器异常");
		}
		return result;
		
	}

	/**
	 * 站内信 -企业发货特运通代发通知对接人
	 * @param userId
	 * @param userName
	 * @param corpName
	 * @param ctime
	 * @param startPoint
	 * @param destPoint
	 * @param taskContent
	 * @param tsId
	 * @return
	 */
	@RequestMapping("inMailForPub")
	@ResponseBody
	public ResultMsgBean inMailForPub(@RequestParam(value = "joinUserId", required = true) Long joinUserId,
			@RequestParam(value = "joinUserName", required = true) String joinUserName,
			@RequestParam(value = "corpName", required = true) String corpName,
			@RequestParam(value = "ctime", required = true) String ctime,
			@RequestParam(value = "startPoint", required = true) String startPoint,
			@RequestParam(value = "destPoint", required = true) String destPoint,
			@RequestParam(value = "taskContent", required = true) String taskContent,
			@RequestParam(value = "tsId", required = true) String tsId
			) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			employeeMessageService.saveCorpMessageForPub(joinUserId,joinUserName,corpName,ctime,startPoint,destPoint,taskContent,tsId);
			result.setCode(200);
			result.setMsg("站内信发送成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("服务器异常");
		}
		return result;
		
	}

	/**
	 * 站内信 特运通代发货源 企业下架通知对接人
	 * @param userId
	 * @param userName
	 * @param corpName
	 * @param ctime
	 * @param startPoint
	 * @param destPoint
	 * @param taskContent
	 * @param tsId
	 * @return
	 */
	@RequestMapping("inMailForCannel")
	@ResponseBody
	public ResultMsgBean inMailForCannel(@RequestParam(value = "joinUserId", required = true) String joinUserId,
								@RequestParam(value = "joinUserName", required = true) String joinUserName,
								@RequestParam(value = "corpName", required = true) String corpName,
								@RequestParam(value = "ctime", required = true) String ctime,
								@RequestParam(value = "startPoint", required = true) String startPoint,
								@RequestParam(value = "destPoint", required = true) String destPoint,
								@RequestParam(value = "taskContent", required = true) String taskContent,
								@RequestParam(value = "tsId", required = true) String tsId
	) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			employeeMessageService.saveCorpMessageForCannel(Long.valueOf(joinUserId),joinUserName,corpName,ctime,startPoint,destPoint,taskContent,tsId);
			result.setCode(200);
			result.setMsg("站内信发送成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("服务器异常");
		}
		return result;

	}
}
