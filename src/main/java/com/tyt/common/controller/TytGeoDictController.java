package com.tyt.common.controller;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.common.bean.CorpDistInfo;
import com.tyt.common.service.TytGeoDictService;
import com.tyt.common.service.TytMapDictService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytGeoDict;
import com.tyt.model.TytMapDict;
import com.tyt.service.common.amap.api.AMapServiceAPI;
import com.tyt.service.common.amap.service.Route;
import com.tyt.util.ArithUtil;

@Controller
@RequestMapping("/plat/geoDict/")
public class TytGeoDictController  extends BaseController{

	@Resource(name = "tytGeoDictService")
	private TytGeoDictService tytGeoDictService;
	@Resource(name = "tytMapDictService")
	private TytMapDictService tytMapDictService;
	
	@RequestMapping("getDict")
	@ResponseBody
	public ResultMsgBean getDict(@RequestParam(value = "startProvinc", required = true) String startProvinc,
			@RequestParam(value = "startCity", required = true) String startCity, 
			@RequestParam(value = "startArea", required = true) String startArea,
			@RequestParam(value = "destProvinc", required = true) String destProvinc,
			@RequestParam(value = "destCity", required = true) String destCity,
			@RequestParam(value = "destArea", required = true) String destArea
			 ) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			TytGeoDict startDict = tytGeoDictService.getTytGeoDict(startProvinc, startCity, startArea);
			TytGeoDict destDict = tytGeoDictService.getTytGeoDict(destProvinc, destCity, destArea);
			CorpDistInfo distInfo=new CorpDistInfo();
			distInfo.setStartPx(startDict.getPx());
			distInfo.setStartPy(startDict.getPy());
			distInfo.setStartLongitude(startDict.getLongitude());
			distInfo.setStartLatitude(startDict.getLatitude());
			distInfo.setDestPx(destDict.getPx());
			distInfo.setDestPy(destDict.getPy());
			distInfo.setDestLongitude(destDict.getLongitude());
			distInfo.setDestLatitude(destDict.getLatitude());
			TytMapDict mapDict = tytMapDictService.getDistance("", startProvinc, startCity, startArea, destProvinc, destCity, destArea);
			if (mapDict != null) {
				distInfo.setAndroidDistance(mapDict.getDistance());
				distInfo.setIosDistance(mapDict.getIosDistance());
			} else {
				Route route = this.drivingRoute(startDict, destDict);
				if (route != null) {
					Double distance = ArithUtil.calcDivide(route.getDistance().toString(), "1000");
					distInfo.setDistance(ArithUtil.calcMultiply(distance.toString(), "100").intValue()); // 出发地目的地之间距离（小数点后最多两位）
																										// -　必填
				}
				distInfo.setAndroidDistance(distInfo.getDistance());
				distInfo.setIosDistance(distInfo.getDistance());
			}
			result.setCode(200);
			result.setData(distInfo);
			result.setMsg("查询成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(500);
			result.setMsg("服务器异常");
		}
		
		return result;
		
	}
	
	public Route drivingRoute(TytGeoDict startGeo, TytGeoDict destGeo) {
		if (startGeo == null || destGeo == null) {
			return null;
		}
		String startLng = ArithUtil.calcDivide(startGeo.getLongitude().toString(), "100").toString();
		String startLat = ArithUtil.calcDivide(startGeo.getLatitude().toString(), "100").toString();
		String destLng = ArithUtil.calcDivide(destGeo.getLongitude().toString(), "100").toString();
		String destLat = ArithUtil.calcDivide(destGeo.getLatitude().toString(), "100").toString();
		Route route = AMapServiceAPI.drivingRoute(startLng, startLat, destLng, destLat);
		return route;
	}
}
