package com.tyt.common.controller;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;

import com.tyt.user.service.TytConfigService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.common.bean.NoticeRemindListBean;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/notice/popup/")
public class TransportNoticeRemindController extends BaseController {
	@Resource(name = "tytNoticeRemindService")
	private TytNoticeRemindService tytNoticeRemindService;
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	private static final ConcurrentHashMap<String,Long> visitMap = new ConcurrentHashMap<String,Long>();

	private boolean visitValid(String key){
		//获取当前用户ID
		Long visitTime = visitMap.get(key);
		Long currentTime = System.currentTimeMillis();
		if(visitTime != null){
			Long chaTime = currentTime - visitTime;
			if(chaTime <= 5000){
				return true;
			}else{
				visitMap.put(key,currentTime);
			}
		}else{
			visitMap.put(key,currentTime);
		}
		return false;
	}
	/**
	 * 通知弹窗列表查询接口（pc使用）
	 * 
	 * @param baseParameter
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = "list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter) {
		ResultMsgBean rm = new ResultMsgBean();
		//请求限制代码 begin
		String userId = baseParameter.getUserId()+"_popup.list";
		if(visitValid(userId)){
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			return rm;
		}
		// end

		try {
				List<NoticeRemindListBean> list=tytNoticeRemindService.updateGetUserNoticePopupList(baseParameter.getUserId());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
				rm.setTotalSize(list==null?0l:list.size());
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 通知弹窗列表查询接口
	 * 
	 * @param baseParameter
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = "all/list")
	@ResponseBody
	public ResultMsgBean allList(BaseParameter baseParameter) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
				List<NoticeRemindListBean> list=tytNoticeRemindService.updateGetUserNoticePopupListAll(baseParameter.getUserId());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
				rm.setTotalSize(list==null?0l:list.size());
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
	@RequestMapping(value = "list.action")
	@ResponseBody
	public ResultMsgBean listAction(BaseParameter baseParameter) {
		return this.list(baseParameter);
	}


	/**
	 * 通知弹窗列表查询接口（新增供APP使用）
	 *
	 * @param baseParameter
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = "app/list")
	@ResponseBody
	public ResultMsgBean getList(BaseParameter baseParameter,Integer versionType) {
		ResultMsgBean rm = new ResultMsgBean();
		String userId=baseParameter.getUserId()+"_popup.list";
		if(visitValid(userId)){
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			return rm;
		}
		try {
			//获取配置中 弹框查询 几天内的数据
			Integer time = tytConfigService.getIntValue("notice_remind_time", 3);
			List<NoticeRemindListBean> list=tytNoticeRemindService.updateUserNoticePopupList(baseParameter.getUserId(),versionType,time);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			rm.setData(list);
			rm.setTotalSize(list==null?0l:list.size());

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
