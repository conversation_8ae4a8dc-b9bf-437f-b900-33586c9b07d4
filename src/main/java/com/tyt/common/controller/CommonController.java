package com.tyt.common.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.common.bean.WordBookBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytSource;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 获取公共配置版本信息
 * 本地比较后决定是否调用配置接口
 */
@Controller
@RequestMapping("/plat/common")
public class CommonController extends BaseController{

	/**
	 *  获取各个配置接口的最新版本号
	 * @return 各个配置接口的最新版本号
	 */
	@RequestMapping(value = "/confVersionCheck")
	@ResponseBody
	public ResultMsgBean list(){
		ResultMsgBean rm = new ResultMsgBean();
		try {
			//从tyt_source表中获取配置接口版本
			List<TytSource> list=TytSourceUtil.getSourceList("common_conf_version_last_verison");
			if(list != null && list.size() > 0){
				//组装VO返回给app使用
				Map<String,String> sourceMap = new HashMap<String,String>(list.size());
				for(TytSource tytSource:list){
					sourceMap.put(tytSource.getName(),tytSource.getValue());
				}
				rm.setData(sourceMap);
			}
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("获得成功");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
}
