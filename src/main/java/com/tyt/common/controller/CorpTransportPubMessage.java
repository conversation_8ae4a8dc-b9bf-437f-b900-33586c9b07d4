package com.tyt.common.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.common.bean.CorpNoticeRemind;
import com.tyt.common.bean.CorpPubMessage;
import com.tyt.common.bean.CorpPubNotify;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.ShortMsgBean;
import com.tyt.message.bean.GenerateShortUrlUtil;
import com.tyt.message.bean.MqPushAcvitityMessage;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.SerialNumUtil;


import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Controller
@RequestMapping("/plat/PubMessage/")
public class CorpTransportPubMessage {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

    // 发送熟人短信模版key
    private static final String CORP_PUBTRANSPORT_NOTIFY_FAMILIAR = "corp.pubTransport.notify.familiar";
    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "tytMessageTmplService")
    private TytMessageTmplService messageTmplService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "tytNoticeRemindService")
    private TytNoticeRemindService tytNoticeRemindService;
    /**
     * 企业发货 给熟车发送短信
     * @param message
     * @return
     */
    @ResponseBody
    @RequestMapping(value="sendFamiliarNote")
    public ResultMsgBean sendFamiliarNote (CorpPubMessage message) {
        ResultMsgBean result=new ResultMsgBean();
        try {
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.execute(new Runnable() {
                @Override
                public void run() {

                    MqPushAcvitityMessage mqMessage = new MqPushAcvitityMessage();
                    // 根据短信key获取短信模板
                    mqMessage.setMessageType(MqBaseMessageBean.PUSH_NOTIFY_FOR_CORP);
                    String messageSerailNum = SerialNumUtil.generateSeriaNum();
                    mqMessage.setMessageSerailNum(messageSerailNum);
                    String content = messageTmplService.getSmsTmpl(CORP_PUBTRANSPORT_NOTIFY_FAMILIAR);
    //            ${corpName}“${corpPubName}”邀您接单啦:${taskcontent},${city},请尽快点击${url}前往“${corpName}合作平台特运通”接单
                    String city=message.getStartPoint()+"->"+message.getDestPoint();
                    //URL 企业货源发送短信跳转H5页
                    String url = tytConfigService.getStringValue("tyt_corp_share_app_url");
                    String[] cellphoneArr = message.getCellPhone().split(",");
                    List<String> cellList=new ArrayList<String>();
                    logger.info("企业发货发送短信，length="+cellphoneArr.length);
                    for (int i = 0; i <cellphoneArr.length ; i++) {
                        String params = "id=" + message.getTsId()+"&cellphone="+cellphoneArr[i];
                        String shortUrl = GenerateShortUrlUtil.generateShortUrl(url + "?" + params);
                        String sendContent = StringUtils.replaceEach(content, new String[] { "${corpName}", "${corpPubName}", "${taskcontent}", "${city}", "${url}", "${corpName}" }, new String[] { message.getCorpName(), message.getCorpPubName(), message.getTaskContent(),city," "+shortUrl+" ",message.getCorpName() });
                        logger.info("-----企业发货短信,cellphone="+cellphoneArr[i]+"--content:"+sendContent);
                        cellList.add(cellphoneArr[i]+";"+sendContent);

                    }
                    mqMessage.setContent(city+",tsId="+message.getTsId());
                    mqMessage.setSendType(1);
                    RedisUtil.setList(mqMessage.getContent(),cellList,(int)Constant.CACHE_EXPIRE_TIME_24H);
                    tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(mqMessage),mqMessage.getMessageType());
                    tytMqMessageService.sendMqMessage(messageSerailNum, JSON.toJSONString(mqMessage),mqMessage.getMessageType());
                }
            });
            //关闭线程
            executor.shutdown();

            result.setCode(200);
            result.setMsg("send success");
        }catch (Exception e){
            e.printStackTrace();
            result.setCode(500);
            result.setMsg("send faile"+e);
        }
        return result;
    }
    
    @ResponseBody
    @RequestMapping(value="/sendSmsForCorp")
    public ResultMsgBean sendSmsForCorp(String cellphone, String content) {
		logger.info("sendSmsForCorp cellphone is: " + cellphone + ", content is: " + content);
        ResultMsgBean result=new ResultMsgBean();
        try {
            ShortMsgBean shortMsgBean = new ShortMsgBean();
            // 根据短信key获取短信模板
            shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
            String messageSerailNum = SerialNumUtil.generateSeriaNum();
            shortMsgBean.setMessageSerailNum(messageSerailNum);
            shortMsgBean.setContent(content);
            shortMsgBean.setCell_phone(cellphone);
            shortMsgBean.setRemark("");
            tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(shortMsgBean),MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
            tytMqMessageService.sendMqMessage(messageSerailNum, JSON.toJSONString(shortMsgBean),MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
            result.setCode(200);
            result.setMsg("send success");
        }catch (Exception e){
            e.printStackTrace();
            result.setCode(500);
            result.setMsg("send faile"+e);
        }
        return result;
    }

    /**
     * 企业发货 给熟车发送通知欄
     * @param notify
     * @return
     */
    @ResponseBody
    @RequestMapping(value="sendFamiliarNotify")
    public ResultMsgBean sendFamiliarNotify (CorpPubNotify notify) {
        ResultMsgBean result=new ResultMsgBean();
        try {
            MqPushAcvitityMessage message = new MqPushAcvitityMessage();
            String messageSerailNum = SerialNumUtil.generateSeriaNum();
//            message.setUserArr(notify.getUserArr());
            message.setRemarks(notify.getRemarks());
            message.setTitle(notify.getTitle());
            message.setSummary(notify.getSummary());
            message.setContent(notify.getContent());
            message.setLinkUrl(notify.getLinkUrl());
            message.setOpenType(notify.getOpenType());
            message.setMessageSerailNum(messageSerailNum);
            message.setMessageType(MqBaseMessageBean.PUSH_NOTIFY_FOR_CORP);
            message.setSendType(2);
            RedisUtil.set(notify.getLinkUrl(),notify.getUserArr(),(int)Constant.CACHE_EXPIRE_TIME_24H);

            tytMqMessageService.addSaveMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message),message.getMessageType());
            tytMqMessageService.sendMqMessage(message.getMessageSerailNum(), JSON.toJSONString(message),message.getMessageType());
            result.setCode(200);
            result.setMsg("send success");
        }catch (Exception e){
            e.printStackTrace();
            result.setCode(500);
            result.setMsg("send faile"+e);
        }
        return result;
    }
    
    /**
     * 存入通知提醒
     * @param remind
     * @return
     */
    @ResponseBody
    @RequestMapping(value="saveNoticeRemind")
    public ResultMsgBean saveNoticeRemind (CorpNoticeRemind remind) {
        ResultMsgBean result=new ResultMsgBean();
        try {
        	tytNoticeRemindService.saveNoticeRemind(remind.getType1(), remind.getType2(), remind.getMsgId(), Long.valueOf(remind.getProductionId()), Long.valueOf(remind.getReceiveId()));
            result.setCode(200);
            result.setMsg("save notice success");
        }catch (Exception e){
            e.printStackTrace();
            result.setCode(500);
            result.setMsg("save notice faile"+e);
        }
        return result;
    }
}
