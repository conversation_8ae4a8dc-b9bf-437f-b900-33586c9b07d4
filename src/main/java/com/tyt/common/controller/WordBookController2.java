/*package com.tyt.common.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.common.bean.WordBookBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytSource;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;
@Controller
@RequestMapping("/plat/wordbook")
public class WordBookController2  extends BaseController{
	@Resource(name = "cacheServiceMcImpl")
	CacheService cacheService;
	//父级groupCode集合
	String[] keys=new String[]{
			"duty",
			"carType",
//			"salaryMon",
//			"salaryMonF",
//			"salaryDate",
			"salaryMonS",
			"deviceType",
//			"tfMachine",
//			"lmMachine",
//			"qzMachine",
//			"hntMachine",
//			"zgMachine",
//			"ksMachine",
//			"wjjFormat",
//			"zzjFormat",
//			"pdjFormat",
//			"tpjFormat",
//			"xpjFormat",
//			"dgylFormat",
//			"lqjbzFormat",
//			"jlyljFormat",
//			"sgyljFormat",
//			"qcdFormat",
//			"lddFormat",
//			"bcFormat",
//			"hntjbcFormat",
//			"xwzFormat"
			};
	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter){
		ResultMsgBean rm = new ResultMsgBean();
		try {	

			 Map<String,List<WordBookBean>> map=new HashMap<String,List<WordBookBean>>();
			 //父级
			for(String key:keys){
				 List<TytSource> list=TytSourceUtil.getSourceList(key);
				 List<WordBookBean> duty=getWorkBookBean(list);
				 map.put(key, duty);
				//子级
				 for(TytSource tytSource:list){
					 Long id=tytSource.getId();
					 List<TytSource> subList=TytSourceUtil.getTytSourceSubSet(id);
					 if(subList!=null)map.put(id+"",getWorkBookBean(subList));
				 }
				 
			}
			
			rm.setData(map);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("获得成功");
			
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
	private List<WordBookBean> getWorkBookBean(List<TytSource> list){
		List<WordBookBean> duty=new ArrayList<WordBookBean>();	
		 for( TytSource tytSource:list){
			 WordBookBean wordBookBean=new WordBookBean();
			 BeanUtils.copyProperties(tytSource, wordBookBean);
			 wordBookBean.setDefaults(tytSource.getRemark());
			 duty.add(wordBookBean);
			 
		 }
		 return duty;
	}
	
}
*/