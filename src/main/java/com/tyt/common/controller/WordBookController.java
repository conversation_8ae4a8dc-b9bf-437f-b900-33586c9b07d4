package com.tyt.common.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.common.bean.WordBookBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytSource;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Controller
@RequestMapping("/plat/wordbook")
public class WordBookController  extends BaseController{
	@Resource(name = "cacheServiceMcImpl")
	CacheService cacheService;
	String[] keys=new String[]{
			"duty",
			"carType",
			"salaryMon",
			"salaryMonF",
			"salaryDate",
			"salaryMonS",
			"deviceType",
			"tfMachine",
			"lmMachine",
			"qzMachine",
			"hntMachine",
			"zgMachine",
			"ksMachine",
			"wjjFormat",
			"zzjFormat",
			"pdjFormat",
			"tpjFormat",
			"xpjFormat",
			"dgylFormat",
			"lqjbzFormat",
			"jlyljFormat",
			"sgyljFormat",
			"qcdFormat",
			"lddFormat",
			"bcFormat",
			"hntjbcFormat",
			"hntjbzFormat",
			"xwzFormat",
			"new_car_type",
			"new_car_brand",
			"new_car_query_price",
			"user_class",
			"user_identity_type_1",
			"user_identity_type_2",
			"user_ienterprise_type",
			"user_enterprise_amount",
			"user_work_type",
			"carLength",
			"newCarType",
			"specialRequired",
            "complainReason",
            "callPhoneMark",
            "tail_car_type",
            "tail_car_style",
			"road_transport_type",
			"backout_reason",
			"car_length_label",
			"transport_label",
			"refund_reason",
            "ts_detail_notice",
            "tyt_company_qualification",
			"app_call_result_code",
			"fixed_price_remind",
			"user_deliver_goods_identity",
			"user_deliver_car_identity",
			"user_deliver_goods_quantity",
			"user_deliver_car_quantity",
            "start_address_city",
			"pay_deposit_amount",
			"transport_complaint_reason",
			"advice_type_transport",
			"advice_type_car",
			"complaint_record_type",
			"load_y_child_type",
			"load_n_child_type",
			"ex_complaint_reason_type_goods",
			"ex_complaint_reason_type_car",
			"tyt_marker_owner_code",
			"car_limit_cause",
			"goods_limit_cause",
			"monitor_user_add_reason",
			"car_limit_minutes",
			"goods_limit_minutes",
			"ex_report_popup_window_config",
			"frozen_account_popup_window_config",
			"standard_brand_group",
			"standard_second_class_group",
			"unload_type",
			"frozen_account_popup_window_config",
			"standard_brand_group",
			"standard_second_class_group",
			"refund_reason_not_load_refund",
			"order_finish_refund_tec_reason",
			"order_cancel_reason","order_redun_no_cancel_reason","cargo_reason_second","car_reason_second","other_reason_second"
			,"order_car_cancel_reason","car_cancel_good_reason_second","car_cancel_reason_second","car_cancel_other_reason_second"
			,"quote_price_reason"
			,"ex_complaint_report_type_car"
			,"ex_complaint_report_type_goods"
	};
	@RequestMapping(value = {"/list", "/list.action"})
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter){
		ResultMsgBean rm = new ResultMsgBean();
		try {

			 Map<String,List<WordBookBean>> map=new HashMap<String,List<WordBookBean>>();
			for(String key:keys){
				 List<WordBookBean> duty=new ArrayList<WordBookBean>();
				 List<TytSource> list=TytSourceUtil.getSourceList(key);
				 if (list != null) {
					 for( TytSource tytSource:list){
						 WordBookBean wordBookBean=new WordBookBean();
						 BeanUtils.copyProperties(tytSource, wordBookBean);
						 wordBookBean.setDefaults(tytSource.getRemark());
						 duty.add(wordBookBean);
					 }
					 if(list.get(0).getParent().equals("0")){
						 map.put(key, duty);
					 }else{
						 map.put(list.get(0).getParent(), duty);
					 }
				 }
			}
			rm.setData(map);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("获得成功");


		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

}
