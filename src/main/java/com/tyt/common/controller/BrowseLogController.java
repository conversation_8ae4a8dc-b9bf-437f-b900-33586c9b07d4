package com.tyt.common.controller;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.carnews.util.CarNewUtil;
import com.tyt.common.bean.NewCarQueue;
import com.tyt.common.service.TytBrowseLogService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytBrowseLog;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/browseLog")
public class BrowseLogController extends BaseController {

	@Resource(name = "cacheServiceMcImpl")
	CacheService cacheService;

	@Resource(name = "tytBrowseLogService")
	TytBrowseLogService tytBrowseLogService;

	@RequestMapping(value = "/save")
	@ResponseBody
	public ResultMsgBean save( Long msgId,
			Long userId, Integer moduleType, Integer functionType,
			String phone, String clientSign, String clientVersion) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (moduleType != null && null != functionType) {

				if (1 == functionType.intValue()) {
					TytBrowseLog tytBrowseLog = getTytBrowseLog(msgId, userId,
							moduleType, functionType, phone, clientSign,
							clientVersion);
					tytBrowseLogService.add(tytBrowseLog);
					rm.setCode(ReturnCodeConstant.OK);
					rm.setMsg("获得成功");
				} else if (2 == functionType.intValue()) {
					if (msgId == null) {
						rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
						rm.setMsg("信息ID不能为空");
					} else {
						TytBrowseLog tytBrowseLog = getTytBrowseLog(msgId,
								userId, moduleType, functionType, phone,
								clientSign, clientVersion);
						tytBrowseLogService.add(tytBrowseLog);
						if(moduleType==1) {
							//如果是新车资讯详情，记录详情页面点击量
							NewCarQueue bean =new NewCarQueue();
							bean.setId(msgId);
							bean.setType(functionType);
							CarNewUtil.carNewQueue.offer(bean);
						}
						rm.setCode(ReturnCodeConstant.OK);
						rm.setMsg("获得成功");
					}
				} else if (3 == functionType.intValue()) {
					if (msgId == null) {
						rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
						rm.setMsg("信息ID不能为空");
					} else if (phone == null || "".equals(phone)) {
						rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
						rm.setMsg("电话号吗不能为空");
					} else {
						TytBrowseLog tytBrowseLog = getTytBrowseLog(msgId,
								userId, moduleType, functionType, phone,
								clientSign, clientVersion);
						tytBrowseLogService.add(tytBrowseLog);
						if(moduleType==1) {
							//如果是新车资讯详情，拨打电话记录点击量
							NewCarQueue bean =new NewCarQueue();
							bean.setId(msgId);
							bean.setType(functionType);
							CarNewUtil.carNewQueue.offer(bean);
						}
						rm.setCode(ReturnCodeConstant.OK);
						rm.setMsg("获得成功");
					}
				} else {
					rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
					rm.setMsg("功能类型参数错误");
				}

			} else {

				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("类型不能为空");
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	public TytBrowseLog getTytBrowseLog(Long msgId, Long userId,
			Integer moduleType, Integer functionType, String phone,
			String clientSign, String clientVersion) {
		TytBrowseLog tytBrowseLog = new TytBrowseLog();
		tytBrowseLog.setClientSign(clientSign);
		tytBrowseLog.setClientVersion(clientVersion);
		tytBrowseLog.setCtime(new Date());
		tytBrowseLog.setFunctionType(functionType);
		tytBrowseLog.setModuleType(moduleType);
		tytBrowseLog.setMsgId(msgId);
		tytBrowseLog.setPhone(phone);
		tytBrowseLog.setUserId(userId);
		return tytBrowseLog;
	}
	@RequestMapping(value = "/caiji")
	public void caiji(BaseParameter baseParameter, Long msgId,
			Long userId, Integer moduleType, Integer functionType,
			String phone, String clientSign, String clientVersion) {
		try {

			if (moduleType != null && null != functionType) {

				if (1 == functionType.intValue()) {
					TytBrowseLog tytBrowseLog = getTytBrowseLog(msgId, userId,
							moduleType, functionType, phone, clientSign,
							clientVersion);
					tytBrowseLogService.add(tytBrowseLog);

					logger.info("获得成功");
				} else if (2 == functionType.intValue()) {
					if (msgId == null) {
						logger.info("信息ID不能为空");
					} else {
						TytBrowseLog tytBrowseLog = getTytBrowseLog(msgId,
								userId, moduleType, functionType, phone,
								clientSign, clientVersion);
						tytBrowseLogService.add(tytBrowseLog);
						logger.info("获得成功");
					}
				} else if (3 == functionType.intValue()) {
					if (msgId == null) {
						logger.info("信息ID不能为空");
					} else if (phone == null || "".equals(phone)) {
						logger.info("电话号吗不能为空");
					} else {
						TytBrowseLog tytBrowseLog = getTytBrowseLog(msgId,
								userId, moduleType, functionType, phone,
								clientSign, clientVersion);
						tytBrowseLogService.add(tytBrowseLog);
						logger.info("获得成功");
					}
				} else {
					logger.info("功能类型参数错误");
				}

			} else {

				logger.info("类型不能为空");
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
		}
	}
	/**
	 * 板车购买拨打电话记录
	 * @param msgId
	 * @param userId
	 * @param moduleType
	 * @param functionType
	 * @param phone
	 * @param clientSign
	 * @param clientVersion
	 * @return
	 */
	@RequestMapping(value="/saveCallNum.action")
	@ResponseBody
	public ResultMsgBean saveCallNum(Long msgId,
			Long userId, Integer moduleType, Integer functionType,
			String phone, String clientSign, String clientVersion) {
		ResultMsgBean result = new ResultMsgBean();
		try {
			result = this.save(msgId, userId, moduleType, functionType, phone, clientSign, clientVersion);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
		
	}
}
