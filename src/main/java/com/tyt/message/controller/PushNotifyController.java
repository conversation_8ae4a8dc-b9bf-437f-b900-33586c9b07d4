package com.tyt.message.controller;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.message.service.TytPushUserService;
import com.tyt.message.service.TytUserNotifyService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserSub;
import com.tyt.user.service.TytUserSubService;
import com.tyt.util.PushUtil;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/notify")
public class PushNotifyController extends BaseController{
	
	@Resource(name = "tytUserSubService")
	TytUserSubService tytUserSubService;
	@Resource(name = "tytPushUserService")
	TytPushUserService tytPushUserService;
	@Resource(name = "tytUserNotifyService")
	TytUserNotifyService tytUserNotifyService;
	/**
	 * IOS 角标清0
	 * @return
	 */
	@RequestMapping(value = "/delBadge")
	@ResponseBody
	public ResultMsgBean delBadge(BaseParameter baseParameter){
		ResultMsgBean rm = new ResultMsgBean();
		try {	
			if(baseParameter.getUserId()!=null){
				//tytUserSubService.delNotifyBadge(baseParameter.getUserId());
				TytUserSub tytUserSub=tytUserSubService.getTytUserSubByUserId(baseParameter.getUserId());
				if(tytUserSub!=null && tytUserSub.getCid()!=null){
					PushUtil.clearBadge(tytUserSub.getCid());
				}
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("保存成功");
			}else{
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId参数不能为空");
			}
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
	@RequestMapping(value = "/collection")
	@ResponseBody
	public ResultMsgBean cidCollection(BaseParameter baseParameter, String cid, String carDeviceId, String goodsDeviceId){
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if(StringUtils.isBlank(baseParameter.getDeviceId()) && StringUtils.isBlank(carDeviceId) && StringUtils.isBlank(goodsDeviceId)){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("deviceId,carDeviceId,goodsDeviceId至少一项不能为空");
			}else{
                tytPushUserService.savePushCid(baseParameter.getUserId(), cid, Integer.parseInt(baseParameter.getClientSign()), baseParameter.getClientVersion(), baseParameter.getOsVersion(),baseParameter.getDeviceId(),carDeviceId,goodsDeviceId);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("保存成功");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	

	@RequestMapping(value = "/updateRead")
	@ResponseBody
	public ResultMsgBean updateRead(BaseParameter baseParameter,Long id){
		ResultMsgBean rm = new ResultMsgBean();
		try {	
			if(baseParameter.getUserId()!=null){
                if(id!=null){
                    tytUserNotifyService.updateRead(baseParameter.getUserId(), id);
                    rm.setCode(ReturnCodeConstant.OK);
                    rm.setMsg("保存成功");
                }else{
                    rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    rm.setMsg("通知消息id参数不能为空");
                }
			}else{
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId参数不能为空");
			}
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
}
