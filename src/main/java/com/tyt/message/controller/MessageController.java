package com.tyt.message.controller;

import java.util.*;

import javax.annotation.Resource;

import com.tyt.util.Constant;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.message.bean.TytMessageBean;
import com.tyt.message.service.TytMessageService;
import com.tyt.message.service.TytUserMsgService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserSub;
import com.tyt.user.service.TytUserSubService;
import com.tyt.util.ReturnCodeConstant;
@Controller
@RequestMapping("/plat/message")
public class MessageController  extends BaseController{
	@Resource(name = "tytUserMsgService")
	TytUserMsgService tytUserMsgService;
	@Resource(name = "tytMessageService")
	TytMessageService tytMessageService;
	@Resource(name = "tytUserSubService")
	TytUserSubService tytUserSubService;
	@Resource(name = "cacheServiceMcImpl")
	CacheService cacheService;
	
	@RequestMapping(value = {"/updateRead","/updateRead.action"})
	@ResponseBody
	public ResultMsgBean updateRead(BaseParameter baseParameter,Long id){
		ResultMsgBean rm = new ResultMsgBean();
		try {	
			if(id!=null){
				tytUserMsgService.updateRead(baseParameter.getUserId(), id);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("保存成功");
			}else{
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("列表消息id参数不能为空");
			}
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
	@RequestMapping(value = {"/del","/del.action"})
	@ResponseBody
	public ResultMsgBean delMessae(BaseParameter baseParameter,Long id){
		ResultMsgBean rm = new ResultMsgBean();
		try {	
				if(id!=null){
					tytUserMsgService.delMessage(baseParameter.getUserId(), id);
					rm.setCode(ReturnCodeConstant.OK);
					rm.setMsg("保存成功");
				}else{
					rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					rm.setMsg("列表消息id参数不能为空");
				}
			
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 查询最新消息数
	 * @param baseParameter
	 * @return
	 */
	@RequestMapping(value = {"/newMsgNbr","/newMsgNbr.action"})
	@ResponseBody
	public ResultMsgBean newMsgNbr(BaseParameter baseParameter) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
            int pushPort = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(baseParameter.getClientSign()));

			Integer num = tytMessageService.queryMyMessageCount(baseParameter.getUserId(), pushPort);
			int nbr = Optional.ofNullable(num).orElse(0);

			Map <String,Integer> map=new HashMap<String,Integer>();
			map.put("newMsgNbr", nbr);
			rm.setMsg("查询成功");
            rm.setCode(ReturnCodeConstant.OK);
			rm.setData(map);
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
	/**
	 * 清空消息数
	 * @param baseParameter
	 * @return
	 */
	@RequestMapping(value = {"/clearMsgNbr","/clearMsgNbr.action"})
	@ResponseBody
	public ResultMsgBean clearMsgNbr(BaseParameter baseParameter) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
            int pushPort = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(baseParameter.getClientSign()));
			TytUserSub tytUserSub=tytUserSubService.getTytUserSubByUserId(baseParameter.getUserId());
			if(null!=tytUserSub){
			    if (pushPort == 0){
                    tytUserSub.setNewMsgNbr(0);
                }else if (pushPort == 1 || pushPort == 4){
                    tytUserSub.setCarNewMsgNbr(0);
                }else {
                    tytUserSub.setGoodsNewMsgNbr(0);
                }
				tytUserSub.setUtime(new Date());
				tytUserSubService.update(tytUserSub);
//				cacheService.del(
//						Constant.CACHE_USERSUB_KEY + baseParameter.getUserId().longValue() + "_"
//								+ TimeUtil.formatDateMonthTime(new Date()));
			}
			
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("操作成功");
			

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
	@RequestMapping(value = {"/list","/list.action"})
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter,
			Integer queryType, Long querySign) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
            int pushPort = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(baseParameter.getClientSign()));
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
				List<TytMessageBean> list = tytMessageService.queryMyMessage(baseParameter.getUserId(),queryType.intValue(), querySign == null ? 0 : querySign.longValue(),pushPort);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	private boolean checkQueryParameter(Integer queryType, Long querySign,
			ResultMsgBean rm) {
		if (queryType == null
				|| (queryType.intValue() != 0 && queryType.intValue() != 1 && queryType
						.intValue() != 2)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询类型不正确！");
			return false;
		} else if (queryType.intValue() != 1
				&& (querySign == null || querySign.longValue() < 0)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询标识错误，最大、最小ID为空！");
			return false;
		}
		return true;

	}
}
