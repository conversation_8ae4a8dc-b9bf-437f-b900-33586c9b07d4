package com.tyt.message.mqservice;

import com.aliyun.openservices.ons.api.SendResult;

/**
 * @Title: IMQConsumer.java
 * @Package com.tyt.message.consumer.service
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年11月17日
 */
public interface IMqProducer {

	SendResult sendMsg(String messages, String topic, String key, String tag, Long delayedTime);

	SendResult sendMsgCustom(String messages, String topic, String key, String tag, Long delayedTime);

	/**
	 * 发送非事务 mq 消息，直接发送
	 * @param messages
	 * @param topic
	 * @param key
	 * @param tag
	 * @param delayedTime
	 * @return
	 */
	SendResult sendMsgDirect(String messages, String topic, String key, String tag, Long delayedTime);

}
