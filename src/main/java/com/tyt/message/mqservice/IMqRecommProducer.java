//package com.tyt.message.mqservice;
//
//import com.aliyun.openservices.ons.api.SendResult;
//
///**
// * @Title: IMQConsumer.java
// * @Package com.tyt.message.consumer.service
// * @Description: TODO
// * <AUTHOR>
// * @date 2016年11月17日
// */
//public interface IMqRecommProducer {
//
//	/**
//	 * @Title: IMqConsumer.java
//	 * @Package com.tyt.message.consumer.service
//	 * @Description: TODO
//	 * <AUTHOR>
//	 * @date 2016年11月17日
//	 */
//	void test();
//
//	/**
//	 * @Title: IMqProducer.java
//	 * @Package com.tyt.message.mqservice
//	 * @Description: TODO
//	 * <AUTHOR>
//	 * @date 2016年11月22日
//	 */
//
//	/**
//	 * @Title: IMqProducer.java
//	 * @Package com.tyt.message.mqservice
//	 * @Description: TODO
//	 * <AUTHOR>
//	 * @date 2016年11月22日
//	 */
//	SendResult sendTransactionMsg(String msg, String topic, String tag);
//
//	/**
//	 * @Title: IMqProducer.java
//	 * @Package com.tyt.message.mqservice
//	 * @Description: TODO
//	 * <AUTHOR>
//	 * @date 2016年12月23日
//	 */
//	SendResult sendMsg(String msg, String topic, String key, String tag);
//
//	/**
//	 * 定时消息
//	 * @param msg
//	 * @param topic
//	 * @param key
//	 * @param timeStamp 发送时间的毫秒值 一般System.currentTimeMillis() + 延时时间
//	 * @return
//	 */
//
//		public SendResult sendTimerMsg(String msg,String topic,String key,long timeStamp);
//
//
//
//}
