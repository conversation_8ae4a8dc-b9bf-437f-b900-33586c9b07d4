package com.tyt.message.mqservice.impl;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.transaction.LocalTransactionExecuter;
import com.aliyun.openservices.ons.api.transaction.TransactionStatus;
import com.tyt.message.service.MqSendMessageService;
import com.tyt.model.MqSendMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: mq事务类
 * @date 2022/4/8 12:40
 */
@Slf4j
public class TytMqTransactionExecuter implements LocalTransactionExecuter {

    private MqSendMessageService mqSendMessageService;
    private String topic;
    private String tag;
    private String key;
    private String messageJson;

    public TytMqTransactionExecuter(MqSendMessageService mqSendMessageService, String topic, String tag,
                                    String key, String messageJson) {
        this.mqSendMessageService = mqSendMessageService;
        this.topic = topic;
        this.tag = tag;
        this.key = key;
        this.messageJson = messageJson;
    }

    @Override
    public TransactionStatus execute(Message msg, Object arg) {
        TransactionStatus transactionStatus = TransactionStatus.Unknow;
        try {
            boolean insert = mqSendMessageService.saveMqSendMessage(topic, tag, key, messageJson);
        } catch (Exception e) {
            log.error("MqTransactionExecuter_error", e);
        }
        return transactionStatus;
    }

}
