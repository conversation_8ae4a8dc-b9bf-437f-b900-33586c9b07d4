/*
package com.tyt.message.mqservice.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.ResourceBundle;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.ONSFactory;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.transaction.LocalTransactionExecuter;
import com.aliyun.openservices.ons.api.transaction.TransactionProducer;
import com.aliyun.openservices.ons.api.transaction.TransactionStatus;
import com.tyt.message.mqservice.IMqProducer;

*/
/**
 * @Title: MQConsumer.java
 * @Package com.tyt.message.consumer.service.impl
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年11月17日
 *//*

@Service
public class MqProducer implements IMqProducer {

	// @Resource
	// private IMqDatabase mqDatabase;
	// {"msg_type":1,"msg_data":{"key":"sms_take_money","cell_phone":"15210686201","content":"","tixian":123,"url":"www.teyuntong.com"},"msg_remark":"this short msg","msg_time":"yyyy-mm-dd hh:mm:ss"}//fastjson
	// msg_type= 1:短信2:支付3:退款4:提现

	@Override
	public SendResult sendMsg(String msg, String topic, String key, String tag) {
		if (StringUtils.isBlank(topic)) {
			topic = getString("TOPIC");
		}
		// if(StringUtils.isBlank(tag)){
		tag = getString("TAG");
		// }
		if (StringUtils.isBlank(key)) {
			System.out.println("Time=0=of==the==message==has==been==sent key=" + key + "=====product message, the content is: " + msg);
			return null;
		}
		Message message1 = new Message(topic, tag, key, msg.getBytes());
		SendResult sendResult = null;
		if (producer != null) {
			try {
				message1.setStartDeliverTime(System.currentTimeMillis() + 1500);
				System.out.println("Time=1=of==the==message==has==been==sent " + (System.currentTimeMillis()) + "=====product message, the content is: " + msg);
				sendResult = producer.send(message1);
				System.out.println("Time=2=of==the==message==has==been==sent " + sendResult.getMessageId() + "=====product message, the content is: " + msg);
			} catch (Exception e) {
				System.out.println("Time=3=of==the==message==has==been==sent " + e.getMessage() + "=====product message, the content is: " + msg);
			}
		} else {
			System.out.println("Time=4=of==the==message==has==been==sent " + (System.currentTimeMillis()) + "=====product message, the content is: " + msg);
		}
		return sendResult;
	}

	@Override
	public SendResult sendTransactionMsg(String msg, String topic, String tag) {
		if (StringUtils.isBlank(topic)) {
			topic = getString("TOPIC");
		}
		// if(StringUtils.isBlank(tag)){
		tag = getString("TAG");
		// }
		Message message1 = new Message(topic, tag, msg.getBytes());
		SendResult sendResult = null;
		if (transactionProducer != null) {
			sendResult = transactionProducer.send(message1, new LocalTransactionExecuter() {
				@Override
				public TransactionStatus execute(Message msg, Object arg) {
					System.out.println("执行本地事务");
					return TransactionStatus.CommitTransaction;
					// 根据本地事务执行结果来返回不同的TransactionStatus
				}
			}, null);
		}
		return sendResult;
	}

	private static Properties properties = new Properties();
	private static Producer producer = null;
	private static TransactionProducer transactionProducer = null;

	public static final ResourceBundle p = ResourceBundle.getBundle("server_url");

	static {
		properties.setProperty(PropertyKeyConst.ProducerId, getString("PRODUCER_ID"));
		properties.setProperty(PropertyKeyConst.AccessKey, getString("ACCESS_KEY"));
		properties.setProperty(PropertyKeyConst.SecretKey, getString("SECRET_KEY"));
		properties.setProperty(PropertyKeyConst.ONSAddr, getString("ONSAddr"));
		producer = ONSFactory.createProducer(properties);
		producer.start();

		transactionProducer = ONSFactory.createTransactionProducer(properties, new MqProducerTransactionChecker());
		transactionProducer.start();
	}

	public static String getString(String key) {
		Object obj = p.getObject(key);// p.get(key);
		String value = obj != null ? obj.toString() : "";
		return value;
	}

	@Override
	public void test() {
		System.out.println("product开始执行！");
		*/
/*
		 * Message message = new Message(MqConfig.TOPIC, MqConfig.TAG,
		 * "mq send transaction message test".getBytes()); SendResult sendResult
		 * = producer.send(message);
		 *//*

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd H:m:s");

		String msg = "task mq " + format.format(new Date()) + " send transaction message test";
		Message message1 = new Message(getString("TOPIC"), getString("TAG"), msg.getBytes());
		SendResult sendResult1 = null;

		if (producer != null) {
			sendResult1 = producer.send(message1);
		}
		if (sendResult1 != null) {
			System.out.println(" the producer:===" + msg);
		}
	}

	@Override
	public SendResult sendMsg(String msg, String topic, String key, String tag, long delayedTime) {
		if (StringUtils.isBlank(topic)) {
			topic = getString("TOPIC");
		}
		// if(StringUtils.isBlank(tag)){
		tag = getString("TAG");
		// }
		if (StringUtils.isBlank(key)) {
			System.out.println("Time=0=of==the==message==has==been==sent key=" + key + "=====product message, the content is: " + msg);
			return null;
		}
		Message message1 = new Message(topic, tag, key, msg.getBytes());
		SendResult sendResult = null;
		if (producer != null) {
			try {
				message1.setStartDeliverTime(System.currentTimeMillis() + delayedTime);
				System.out.println("Time=1=of==the==message==has==been==sent " + (System.currentTimeMillis()) + "=====product message, the content is: " + msg);
				sendResult = producer.send(message1);
				System.out.println("Time=2=of==the==message==has==been==sent " + sendResult.getMessageId() + "=====product message, the content is: " + msg);
			} catch (Exception e) {
				System.out.println("Time=3=of==the==message==has==been==sent " + e.getMessage() + "=====product message, the content is: " + msg);
			}
		} else {
			System.out.println("Time=4=of==the==message==has==been==sent " + (System.currentTimeMillis()) + "=====product message, the content is: " + msg);
		}
		return sendResult;
	}

}
*/
