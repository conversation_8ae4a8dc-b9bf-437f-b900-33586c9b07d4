package com.tyt.message.mqservice.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.*;
import com.aliyun.openservices.ons.api.transaction.TransactionProducer;
import com.tyt.message.mqservice.IMqProducer;
import com.tyt.message.service.MqSendMessageService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Properties;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @date 2020/5/27 16:22
 */
@Service
public class MqTransactionProducer implements IMqProducer {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MqSendMessageService mqSendMessageService;

    public static final ResourceBundle serverResource = ResourceBundle.getBundle("server_url");

    public final static String msg_topic;
    public final static String msg_tag;

    //事务producer
    private static TransactionProducer transactionProducer = null;

    //非事务producer
    private static Producer normalProducer = null;

    static {
        msg_topic = getString("TOPIC");
        msg_tag = getString("TAG");

        //事务producer
        initTransactionProducer();

        //普通producer
        initNormalProducer();

    }

    /**
     * 事务producer
     */
    private static void initTransactionProducer(){
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, getString("GROUP_ID"));
        properties.setProperty(PropertyKeyConst.AccessKey, getString("ACCESS_KEY"));
        properties.setProperty(PropertyKeyConst.SecretKey, getString("SECRET_KEY"));
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, getString("NAMESRV_ADDR"));
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, getString("SendMsgTimeoutMillis"));

        transactionProducer = ONSFactory.createTransactionProducer(properties, new MqProducerTransactionChecker());
        transactionProducer.start();
    }

    /**
     * 非事务，直接提交的producer
     */
    private static void initNormalProducer(){
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, getString("GROUP_ID"));
        properties.setProperty(PropertyKeyConst.AccessKey, getString("ACCESS_KEY"));
        properties.setProperty(PropertyKeyConst.SecretKey, getString("SECRET_KEY"));
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, getString("NAMESRV_ADDR"));
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, getString("SendMsgTimeoutMillis"));

        normalProducer = ONSFactory.createProducer(properties);
        normalProducer.start();
    }

    public static String getString(String key) {
        Object obj = serverResource.getObject(key);
        String value = obj != null ? obj.toString() : "";
        return value;
    }

    private Message createMessage(String topic, String tag, String key, String messages, Long delayedTime){

        Message message = new Message(topic, tag, key, messages.getBytes(StandardCharsets.UTF_8));

        if (delayedTime!=null) {
            message.setStartDeliverTime(System.currentTimeMillis() + delayedTime);
        }
        return message;
    }

    @Override
    public SendResult sendMsg(String messages, String topic, String key, String tag, Long delayedTime) {
        if (transactionProducer == null) {
            logger.error("sendMsg_producer_is_null : " + messages);
            return null;
        }
        topic = msg_topic;
        tag = msg_tag;

        if (StringUtils.isBlank(key)) {
            return null;
        }
        Message message1 = this.createMessage(topic, tag, key, messages, delayedTime);
        SendResult sendResult = null;

        try {
            TytMqTransactionExecuter transactionExecuter = new TytMqTransactionExecuter(mqSendMessageService, topic, tag, key, messages);
            sendResult = transactionProducer.send(message1, transactionExecuter, null);
            logger.info("Time=2=of==the==message==has==been==sent " + sendResult.getMessageId() + "=====product message, the content is: " + messages);
        } catch (Exception e) {
            logger.info("MqTransactionProducer sendMsg Exception message is 【{}】", JSON.toJSONString(message1));
            logger.error("Time=3=of==the==message==has==been==sent ", e);
        }
        return sendResult;
    }

    @Override
    public SendResult sendMsgDirect(String messages, String topic, String key, String tag, Long delayedTime) {
        if (normalProducer == null) {
            logger.error("normalProducer_is_null : " + messages);
            return null;
        }
        topic = msg_topic;
        tag = msg_tag;

        if (StringUtils.isBlank(key)) {
            return null;
        }
        Message message1 = this.createMessage(topic, tag, key, messages, delayedTime);
        SendResult sendResult = null;

        try {
            mqSendMessageService.saveMqSendMessage(topic, tag, key, messages);
            sendResult = normalProducer.send(message1);
            logger.info("normalProducer_send_done : " + sendResult.getMessageId() + " =====product_message : " + messages);
        } catch (Exception e) {
            logger.info("normalProducer_send_Exception_msg : ", JSON.toJSONString(message1));
            logger.error("normalProducer_send_ERROR : ", e);
        }
        return sendResult;
    }

    @Override
    public SendResult sendMsgCustom(String messages, String topic, String key, String tag, Long delayedTime) {
        if (normalProducer == null) {
            logger.error("normalProducer_is_null : " + messages);
            return null;
        }
        if (StringUtils.isBlank(key)) {
            return null;
        }
        Message message1 = this.createMessage(topic, tag, key, messages, delayedTime);
        SendResult sendResult = null;
        try {
            sendResult = normalProducer.send(message1);
            logger.info("normalProducer_send_done : {} =====product_message : {}", sendResult.getMessageId(), messages);
        } catch (Exception e) {
            logger.info("normalProducer_send_Exception_msg : {}", JSON.toJSONString(message1));
            logger.error("normalProducer_send_ERROR : ", e);
        }
        return sendResult;
    }

}
