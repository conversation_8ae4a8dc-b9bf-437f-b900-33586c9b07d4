package com.tyt.message.mqservice.impl;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.transaction.LocalTransactionChecker;
import com.aliyun.openservices.ons.api.transaction.TransactionStatus;
import com.tyt.message.service.MqSendMessageService;
import com.tyt.model.MqSendMessage;
import com.tyt.util.ApplicationContextUtils;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**  
 * @Title: MqProducerTransactionChecker.java
 * @Package com.tyt.message.mqservice.impl
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年11月22日
 */
public class MqProducerTransactionChecker implements LocalTransactionChecker {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	public TransactionStatus check(Message msg) {
		//当本地事务执行完毕Broker收到的是事务状态为TransactionStatus.Unknow时就会回调该接口，此时应该检查该半事务消息对应的本地事务的状态
		//确认无误返回TransactionStatus.CommitTransaction 有问题需要回滚则返回TransactionStatus.RollbackTransaction
		try {
			logger.info("事务消息执行回查，mq消息messageSerialNum【{}】，事务回查时间【{}】", msg.getKey(), TimeUtil.formatDateTime(new Date()));
			MqSendMessageService mqSendMessageService = ApplicationContextUtils.getBean("mqSendMessageService");
			Integer countMqMessage = mqSendMessageService.selectCountMqMessage(msg.getKey());
			if (countMqMessage>0){
				return  TransactionStatus.CommitTransaction;
			}
		}catch (Exception e){
			e.printStackTrace();
		}
		return TransactionStatus.RollbackTransaction;
	}
}