package com.tyt.message.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.message.bean.TytMessageBean;
import com.tyt.model.TytMessage;

public interface TytMessageService extends BaseService<TytMessage,Long> {

	List<TytMessageBean> queryMyMessage(long user_id, int queryType, long querySign, Integer pushPort);

	Integer queryMyMessageCount(Long userId, int pushPort);
}
