package com.tyt.message.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytPushUser;

public interface TytPushUserService extends BaseService<TytPushUser,Long> {
	/**
	 * 设置CID和版本信息
	 * @param userId
	 * @param cid　　
	 * @param clientSign　客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
	 * @param clientVersion　　客户端版本号
	 * @param osVersion　系统版本号
	 */
    void savePushCid(Long userId, String cid, Integer clientSign, String clientVersion, String osVersion);
	void savePushCid(Long userId, String cid, Integer clientSign,
                     String clientVersion, String osVersion, String deviceId, String carDeviceId, String goodsDeviceId) ;
}
