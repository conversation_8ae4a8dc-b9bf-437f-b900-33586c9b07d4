package com.tyt.message.service;


import com.tyt.model.MqSendMessage;

/**
 * <AUTHOR>
 * @date 2020/5/28 12:16
 */
public interface MqSendMessageService {

    /**
     * 保存mq发送消息
     * @param topic
     * @param tag
     * @param key
     * @param messageJson
     * @return
     */
    boolean saveMqSendMessage(String topic, String tag, String key, String messageJson);

    /**
     * 插入
     * @param mqSendMessage
     * @return
     */
    boolean saveMqSendMessage(MqSendMessage mqSendMessage);

    /**
     * 根据唯一标识查询
     * @param messageSerialNum
     * @return
     */
    MqSendMessage selectMqSendMessageBymMessageSerialNum(String messageSerialNum);

    /**
     * 根据唯一标识查询mq详细的数量
     * @param messageSerialNum
     * @return
     */
    Integer selectCountMqMessage(String messageSerialNum);
}
