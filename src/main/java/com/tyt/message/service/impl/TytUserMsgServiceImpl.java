package com.tyt.message.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.message.service.TytUserMsgService;
import com.tyt.model.TytUserMsg;
@Service("tytUserMsgService")
public class TytUserMsgServiceImpl extends BaseServiceImpl<TytUserMsg,Long> implements TytUserMsgService {

	@Resource(name = "tytUserMsgDao")
	public void setBaseDao(BaseDao<TytUserMsg, Long> tytUserMsgDao) {
		super.setBaseDao(tytUserMsgDao);
	}

	@Override
	public void updateRead(Long userId, Long id) {
		String sql="UPDATE tyt_user_msg SET read_status=?,mtime=NOW() where  msg_id=? and user_id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[]{"1",id,userId});
	}

	@Override
	public void delMessage(Long userId, Long id) {
		String sql="UPDATE tyt_user_msg SET del_status=?,mtime=NOW() where  msg_id=? and user_id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[]{"1",id,userId});
	}
}
