package com.tyt.message.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.message.service.TytUserNotifyService;
import com.tyt.model.TytUserNotify;
@Service("tytUserNotifyService")
public class TytUserNotifyServiceImpl extends BaseServiceImpl<TytUserNotify,Long> implements TytUserNotifyService {

	@Resource(name = "tytUserNotifyDao")
	public void setBaseDao(BaseDao<TytUserNotify, Long> tytUserNotifyDao) {
		super.setBaseDao(tytUserNotifyDao);
	}

	@Override
	public void updateRead(Long userId, Long id) {
		String sql="UPDATE tyt_user_notify SET read_status=?,mtime=NOW() where  ntf_id=? and user_id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[]{"1",id,userId});
		
	}

}
