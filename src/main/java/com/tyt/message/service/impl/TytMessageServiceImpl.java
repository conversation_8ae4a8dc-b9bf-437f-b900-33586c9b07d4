package com.tyt.message.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.tyt.plat.mapper.base.TytMessageMapper;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.config.util.AppConfig;
import com.tyt.message.bean.TytMessageBean;
import com.tyt.message.service.TytMessageService;
import com.tyt.model.TytMessage;
@Service("tytMessageService")
public class TytMessageServiceImpl extends BaseServiceImpl<TytMessage,Long> implements TytMessageService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@Resource(name = "tytMessageDao")
	public void setBaseDao(BaseDao<TytMessage, Long> tytMessageDao) {
		super.setBaseDao(tytMessageDao);
	}

	@Autowired
	private TytMessageMapper tytMessageMapper;

	@Override
	public List<TytMessageBean> queryMyMessage(long user_id, int queryType,
			long querySign,Integer pushPort) {
		List<TytMessageBean> returnTbList = null;
		// 第一次查询大小
		int firstPageSize = AppConfig
				.getIntProperty("tyt.message.query.first.page.size");
		// 上滑动下滑动最大结果集大小
		int pageSize =AppConfig
				.getIntProperty("tyt.message.query.page.size");

	
		List<Object> list = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer(" SELECT  m.id,m.title,m.type,"
				+ "m.icon_type iconType,m.icon,m.photo,"
				+ "m.summary,m.details,m.details_content detailsContent,"
				+ "m.details_url detailsUrl,m.send_status sendStatus,"
				+ "m.send_time sendTime,m.end_time endTime,"
				+ "m.new_design newDesign, m.enter_detail enterDetail, m.jump_page jumpPage, m.jump_button_text jumpButtonText, m.jump_page_url jumpPageUrl, m.jump_page_url_goods jumpPageUrlGoods,"
				+ "u.read_status readStatus"
				+ " FROM tyt_user_msg u LEFT JOIN tyt_message m ON u.msg_id=m.id WHERE u.user_id=? AND u.del_status='0' AND m.status=0 AND m.send_status='2' and m.end_time>=now()  AND m.send_time<=NOW() ");
		
		list.add(user_id);
		
		int searchSize = pageSize;
        
//        if(querySign<1){
//        	queryType = 1;
//        	  logger.info("查询时上拉、下滑时客户端数据为空,进行查询类型转换为第一次查询queryType=1");
//        }
        sb.append(" and u.push_port=?");
        list.add(pushPort);
		// 第一次请求
		if (queryType == 1) {					
			searchSize = firstPageSize;
			// 大小排序
			sb.append(" order by m.id desc ");
		} else
		// 下拉查新数据
		if (queryType == 0) {
			if(querySign==0){
				sb.append(" and m.id>?");
				// 小大排序
				sb.append(" order by m.id desc ");
			}else{
				sb.append(" and m.id>?");
				// 小大排序
				sb.append(" order by m.id asc ");
			}
			list.add(querySign);
		}
		// 上推查历史数据
		else {
			sb.append(" and m.id<?");
			// 大小排序
			sb.append(" order by m.id desc ");
			list.add(querySign);
		}

		// 查询数据集
		long t3=0,t4=0;
		t3=System.currentTimeMillis();

		 Map<String, org.hibernate.type.Type> scalarMap =new HashMap<String,org.hibernate.type.Type>();
		 scalarMap.put("id", Hibernate.LONG);
		 scalarMap.put("title", Hibernate.STRING);
		 scalarMap.put("type", Hibernate.STRING);
		 scalarMap.put("iconType", Hibernate.STRING);
		 scalarMap.put("icon", Hibernate.STRING);
		 scalarMap.put("photo", Hibernate.STRING);
		 scalarMap.put("summary", Hibernate.STRING);
		 scalarMap.put("details", Hibernate.STRING);
		 scalarMap.put("detailsContent", Hibernate.STRING);
		 scalarMap.put("detailsUrl", Hibernate.STRING);		 
		 scalarMap.put("sendStatus", Hibernate.STRING);
		 scalarMap.put("sendTime", Hibernate.TIMESTAMP);
		 scalarMap.put("endTime", Hibernate.TIMESTAMP);
		 scalarMap.put("readStatus", Hibernate.STRING);
		 scalarMap.put("newDesign", Hibernate.INTEGER);
		 scalarMap.put("enterDetail", Hibernate.INTEGER);
		 scalarMap.put("jumpPage", Hibernate.INTEGER);
		 scalarMap.put("jumpButtonText", Hibernate.STRING);
		 scalarMap.put("jumpPageUrl", Hibernate.STRING);
		 scalarMap.put("jumpPageUrlGoods", Hibernate.STRING);

		 List<TytMessageBean> queryList= this.getBaseDao().search(sb.toString(), scalarMap, TytMessageBean.class, list.toArray(), 1, searchSize);
			
        t4=System.currentTimeMillis();
        logger.info("数据库查询时间："+(t4-t3)+"ms");
		
		if (queryList != null&& queryList.size()>0) {
			returnTbList = new ArrayList<TytMessageBean>();
			int size = queryList.size();
			if(queryType == 0&&querySign>0){
				for (int i = size-1; i >=0 ; i--) {
					TytMessageBean t = queryList.get(i);
					returnTbList.add(t);
				}
			}else{
				returnTbList=queryList;
			}
		}
		return returnTbList;
	}

	@Override
	public Integer queryMyMessageCount(Long userId, int pushPort) {
		return tytMessageMapper.queryMyMessageCount(userId, pushPort);
	}


}
