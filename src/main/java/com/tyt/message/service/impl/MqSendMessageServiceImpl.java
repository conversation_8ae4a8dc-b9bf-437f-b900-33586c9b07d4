package com.tyt.message.service.impl;

import com.tyt.message.service.MqSendMessageService;
import com.tyt.model.MqSendMessage;
import com.tyt.mybatis.mapper.MqSendMessageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/28 12:16
 */
@Service("mqSendMessageService")
public class MqSendMessageServiceImpl implements MqSendMessageService {
    @Autowired
    private MqSendMessageMapper mqSendMessageMapper;

    @Override
    public boolean saveMqSendMessage(String topic, String tag, String key, String messageJson) {
        MqSendMessage mqSendMessage = new MqSendMessage();
        mqSendMessage.setMessageTopic(topic);
        mqSendMessage.setMessageTag(tag);
        mqSendMessage.setMessageSerialNum(key);
        mqSendMessage.setMessageContent(messageJson);
        mqSendMessage.setCreateTime(new Date());
        mqSendMessage.setStatus("1");
        boolean insert = this.saveMqSendMessage(mqSendMessage);
        return insert;
    }

    @Override
    public boolean saveMqSendMessage(MqSendMessage mqSendMessage) {
        int insert = mqSendMessageMapper.insertMqSendMessage(mqSendMessage);
        if (insert==1){
            return true;
        }
        return false;
    }

    @Override
    public MqSendMessage selectMqSendMessageBymMessageSerialNum(String messageSerialNum) {
        MqSendMessage message = mqSendMessageMapper.selectByMessageSerialNum(messageSerialNum);
        return message;
    }

    @Override
    public Integer selectCountMqMessage(String messageSerialNum) {
        return mqSendMessageMapper.selectCountMqMessage(messageSerialNum);
    }
}
