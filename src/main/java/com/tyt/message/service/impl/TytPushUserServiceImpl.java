package com.tyt.message.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.message.service.TytPushUserService;
import com.tyt.model.TytPushUser;
@Service("tytPushUserService")
public class TytPushUserServiceImpl extends BaseServiceImpl<TytPushUser,Long> implements TytPushUserService {

	@Resource(name = "tytPushUserDao")
	public void setBaseDao(BaseDao<TytPushUser, Long> tytPushUserDao) {
		super.setBaseDao(tytPushUserDao);
	}

	@Override
	public void savePushCid(Long userId, String cid, Integer clientSign,
			String clientVersion, String osVersion) {
		if(cid!=null && !"".equals(cid)){
			if(2==clientSign.intValue()||3==clientSign.intValue()){
				String hql="from TytPushUser where cid=?";
				List<TytPushUser> tytPushUserList=this.getBaseDao().find(hql, cid);
				if(tytPushUserList!=null&&tytPushUserList.size()>0){
					TytPushUser tytPushUser=tytPushUserList.get(0);
					if(null!=userId){
						tytPushUser.setUserId(userId);
					}
					tytPushUser.setClientSign(clientSign);
					tytPushUser.setClientVersion(clientVersion);
					tytPushUser.setOsVersion(osVersion!=null?osVersion:tytPushUser.getOsVersion());
					tytPushUser.setUtime(new Date());
					this.getBaseDao().update(tytPushUser);
				}else{
					TytPushUser tytPushUser=new TytPushUser();
					if(null!=userId){
						tytPushUser.setUserId(userId);
					}
					tytPushUser.setClientSign(clientSign);
					tytPushUser.setClientVersion(clientVersion);
					tytPushUser.setOsVersion(osVersion!=null?osVersion:tytPushUser.getOsVersion());
					tytPushUser.setUtime(new Date());
					tytPushUser.setCtime(new Date());
					tytPushUser.setCid(cid);
					this.getBaseDao().insert(tytPushUser);
				}
			}
		}
	}

    @Override
	public void savePushCid(Long userId, String cid, Integer clientSign,
			String clientVersion, String osVersion,String deviceId,String carDeviceId, String goodsDeviceId) {
        String hql = "";
        String param = "";
        if (StringUtils.isNotBlank(deviceId)){
            hql="from TytPushUser where deviceId=?";
            param = deviceId;
        }else if(StringUtils.isNotBlank(carDeviceId)){
            hql="from TytPushUser where carDeviceId=?";
            param = carDeviceId;
        }else if (StringUtils.isNotBlank(goodsDeviceId)){
            hql="from TytPushUser where goodsDeviceId=?";
            param = goodsDeviceId;
        }else{
            return;
        }
        List<TytPushUser> tytPushUserList=this.getBaseDao().find(hql, param);
        if(tytPushUserList!=null&&tytPushUserList.size()>0){
            TytPushUser tytPushUser=tytPushUserList.get(0);
            if(null!=userId){
                tytPushUser.setUserId(userId);
            }
            tytPushUser.setClientSign(clientSign);
            tytPushUser.setClientVersion(clientVersion);
            tytPushUser.setOsVersion(osVersion!=null?osVersion:tytPushUser.getOsVersion());
            tytPushUser.setUtime(new Date());
            if (StringUtils.isNotBlank(deviceId)) {
                tytPushUser.setDeviceId(deviceId);
            }
            if(StringUtils.isNotBlank(carDeviceId)){
                tytPushUser.setCarDeviceId(carDeviceId);
            }
            if (StringUtils.isNotBlank(goodsDeviceId)){
                tytPushUser.setGoodsDeviceId(goodsDeviceId);
            }
            this.getBaseDao().update(tytPushUser);
        }else{
            TytPushUser tytPushUser=new TytPushUser();
            if(null!=userId){
                tytPushUser.setUserId(userId);
            }
            tytPushUser.setClientSign(clientSign);
            tytPushUser.setClientVersion(clientVersion);
            tytPushUser.setOsVersion(osVersion!=null?osVersion:tytPushUser.getOsVersion());
            tytPushUser.setUtime(new Date());
            tytPushUser.setCtime(new Date());
            if (StringUtils.isNotBlank(cid)){
                tytPushUser.setCid(cid);
            }
            if (StringUtils.isNotBlank(deviceId)) {
                tytPushUser.setDeviceId(deviceId);
            }
            if(StringUtils.isNotBlank(carDeviceId)){
                tytPushUser.setCarDeviceId(carDeviceId);
            }
            if (StringUtils.isNotBlank(goodsDeviceId)){
                tytPushUser.setGoodsDeviceId(goodsDeviceId);
            }
            this.getBaseDao().insert(tytPushUser);
        }
	}

}
