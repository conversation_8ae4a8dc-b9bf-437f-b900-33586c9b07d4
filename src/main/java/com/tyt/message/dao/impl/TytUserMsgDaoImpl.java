package com.tyt.message.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.message.dao.TytUserMsgDao;
import com.tyt.model.TytUserMsg;
@Repository("tytUserMsgDao")
public class TytUserMsgDaoImpl extends BaseDaoImpl<TytUserMsg,Long> implements TytUserMsgDao {
	
	public TytUserMsgDaoImpl(){
		   this.setEntityClass(TytUserMsg.class);
	   }
	
	

}
