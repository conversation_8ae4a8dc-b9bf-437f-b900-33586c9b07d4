package com.tyt.message.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.message.dao.TytMessageDao;
import com.tyt.model.TytMessage;
@Repository("tytMessageDao")
public class TytMessageDaoImpl extends BaseDaoImpl<TytMessage,Long> implements TytMessageDao {
	
	public TytMessageDaoImpl(){
		   this.setEntityClass(TytMessage.class);
	   }
	
	

}
