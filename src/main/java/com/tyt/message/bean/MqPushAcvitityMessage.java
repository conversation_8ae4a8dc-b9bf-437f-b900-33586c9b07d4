package com.tyt.message.bean;


import com.tyt.infofee.bean.MqBaseMessageBean;

/**
 * 3周年庆典mq push
 * <AUTHOR>
 *
 */
public class MqPushAcvitityMessage extends MqBaseMessageBean {

	private Long userId;
	private String cellPhone;//用户手机号
	private String trueName;//真实姓名
	private String remarks;//说明
	private String title;//标题
	private String summary;//摘要
	private String content;//内容
	private String userArr;
	private String linkUrl;//链接地址
	private String openType;//打开方式0打开应用,1打开连接
	private Integer sendType;//发送类型 1：企业版发货短信通知 2：企业版发货通知栏相关通知
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	public String getTrueName() {
		return trueName;
	}
	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getSummary() {
		return summary;
	}
	public void setSummary(String summary) {
		this.summary = summary;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}

	public String getUserArr() {
		return userArr;
	}

	public void setUserArr(String userArr) {
		this.userArr = userArr;
	}

	public String getLinkUrl() {
		return linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	public String getOpenType() {
		return openType;
	}

	public void setOpenType(String openType) {
		this.openType = openType;
	}

	public Integer getSendType() {
		return sendType;
	}

	public void setSendType(Integer sendType) {
		this.sendType = sendType;
	}
}
