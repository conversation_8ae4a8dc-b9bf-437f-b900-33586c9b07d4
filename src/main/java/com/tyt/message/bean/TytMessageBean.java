package com.tyt.message.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.Column;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TytMessageBean implements java.io.Serializable {



	/**
	 * 
	 */
	private static final long serialVersionUID = -5754734440253824527L;
	private Long id;
	private String title;
	private String type;
	private String iconType;
	private String icon;
	private String photo;
	private String summary;
	private String details;
	private String detailsContent;
	private String detailsUrl;
	private String sendStatus;
	private Date sendTime;
	private Date endTime;
	private String readStatus;

	/**
	 * 是否改版：1-是，0否
	 */
	private Integer newDesign;

	/**
	 * 是否进详情：1-是，0-否
	 */
	private Integer enterDetail;

	/**
	 * 是否跳转页面：1-是，0-否
	 */
	private Integer jumpPage;

	/**
	 * 跳转按钮文案
	 */
	private String jumpButtonText;

	/**
	 * 跳转页面链接
	 */
	private String jumpPageUrl;

	/**
	 * 跳转页面链接-货
	 */
	private String jumpPageUrlGoods;

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getIconType() {
		return iconType;
	}
	public void setIconType(String iconType) {
		this.iconType = iconType;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getPhoto() {
		return photo;
	}
	public void setPhoto(String photo) {
		this.photo = photo;
	}
	public String getSummary() {
		return summary;
	}
	public void setSummary(String summary) {
		this.summary = summary;
	}
	public String getDetails() {
		return details;
	}
	public void setDetails(String details) {
		this.details = details;
	}
	public String getDetailsContent() {
		return detailsContent;
	}
	public void setDetailsContent(String detailsContent) {
		this.detailsContent = detailsContent;
	}
	public String getDetailsUrl() {
		return detailsUrl;
	}
	public void setDetailsUrl(String detailsUrl) {
		this.detailsUrl = detailsUrl;
	}
	public String getSendStatus() {
		return sendStatus;
	}
	public void setSendStatus(String sendStatus) {
		this.sendStatus = sendStatus;
	}
	public Date getSendTime() {
		return sendTime;
	}
	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public String getReadStatus() {
		return readStatus;
	}
	public void setReadStatus(String readStatus) {
		this.readStatus = readStatus;
	}

	public Integer getNewDesign() {
		return newDesign;
	}

	public void setNewDesign(Integer newDesign) {
		this.newDesign = newDesign;
	}

	public Integer getEnterDetail() {
		return enterDetail;
	}

	public void setEnterDetail(Integer enterDetail) {
		this.enterDetail = enterDetail;
	}

	public Integer getJumpPage() {
		return jumpPage;
	}

	public void setJumpPage(Integer jumpPage) {
		this.jumpPage = jumpPage;
	}

	public String getJumpButtonText() {
		return jumpButtonText;
	}

	public void setJumpButtonText(String jumpButtonText) {
		this.jumpButtonText = jumpButtonText;
	}

	public String getJumpPageUrl() {
		return jumpPageUrl;
	}

	public void setJumpPageUrl(String jumpPageUrl) {
		this.jumpPageUrl = jumpPageUrl;
	}

	public String getJumpPageUrlGoods() {
		return jumpPageUrlGoods;
	}

	public void setJumpPageUrlGoods(String jumpPageUrlGoods) {
		this.jumpPageUrlGoods = jumpPageUrlGoods;
	}
}