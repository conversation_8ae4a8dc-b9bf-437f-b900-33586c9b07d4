package com.tyt.message.bean;


import com.tyt.infofee.bean.MqBaseMessageBean;

/**
 * 调度mq push
 * <AUTHOR>
 */
public class MqPushDispatchMessage extends MqBaseMessageBean {

	private Long userId;	//用户id
	private String cellPhone;//用户手机号
	private String trueName;//真实姓名
	private String remarks;//说明1
	private String title;//标题1
	private String summary;//摘要1
	private String content;//内容1
	private Long sendType;//0：原app 1：给车发 2：给货发
	private String linkUrl;//链接地址
	private String openType;//打开方式0打开应用,1打开连接
	private Long sendCode;//推送信息：1 push; 2 站内信; 3 push+站内信

	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	public String getTrueName() {
		return trueName;
	}
	public void setTrueName(String trueName) {
		this.trueName = trueName;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getSummary() {
		return summary;
	}
	public void setSummary(String summary) {
		this.summary = summary;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}

	public Long getSendType() {
		return sendType;
	}

	public void setSendType(Long sendType) {
		this.sendType = sendType;
	}

	public String getLinkUrl() {
		return linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	public String getOpenType() {
		return openType;
	}

	public void setOpenType(String openType) {
		this.openType = openType;
	}

	public Long getSendCode() {
		return sendCode;
	}

	public void setSendCode(Long sendCode) {
		this.sendCode = sendCode;
	}
}
