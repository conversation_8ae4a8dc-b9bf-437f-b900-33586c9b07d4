package com.tyt.message.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.infofee.bean.MqBaseMessageBean;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TytTransportBackendMessageBean extends MqBaseMessageBean implements java.io.Serializable {

	private static final long serialVersionUID = 5330040890872345339L;
	private Long srcMsgId;
	// 0 车辆取消通知 1 订单派车通知
	private String type;

	public Long getSrcMsgId() {
		return srcMsgId;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
