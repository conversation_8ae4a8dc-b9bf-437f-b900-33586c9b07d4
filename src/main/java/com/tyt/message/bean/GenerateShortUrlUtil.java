package com.tyt.message.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tyt.service.common.common.HttpClientFactory;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: GenerateShortUrlUtil.java
 * @Package com.tyt.message.bean
 * @Description: TODO
 * @date 2016年12月5日
 */
public class GenerateShortUrlUtil {
    /**
     * 段文昌
     * 这段代码会抛出如下异常，大概的意思是：上一个请求还没有完成，下一个又来了，简单处理的话，增加 synchronized
     * java.lang.IllegalStateException: Invalid use of BasicClientConnManager: connection still allocated.
     * Make sure to release the connection before allocating another one.
     */
//    public static DefaultHttpClient httpclient;
//    static {
//        httpclient = new DefaultHttpClient();
//       // httpclient = (DefaultHttpClient) HttpClientConnectionManager.getSSLInstance(httpclient); // 接受任何证书的浏览器客户端
//    }
    /**
     * 这个生成httpclient经过定义配置进行处理，保证了一个请求完成后再进行下一个
     */
    private static CloseableHttpClient httpclient = HttpClientFactory.getHttpClientWithRetry();

    /**
     * 生成端连接信息
     *
     * @author-cp:wangfeng
     */
    public static String generateShortUrl(String url) {
        CloseableHttpResponse response = null;
        try {
            //HttpPost httpost = new HttpPost("http://dwz.cn/create.php"); 
            HttpPost httpost = new HttpPost("http://api.t.sina.com.cn/short_url/shorten.json");


            List<NameValuePair> params = new ArrayList<NameValuePair>();
            //params.add(new BasicNameValuePair("url", url)); // 用户名称  
            params.add(new BasicNameValuePair("url_long", url)); // 用户名称  
            params.add(new BasicNameValuePair("source", "3271760578")); // 用户名称  

            httpost.setEntity(new UrlEncodedFormEntity(params, "utf-8"));
            response = httpclient.execute(httpost);
            String jsonStr = EntityUtils
                    .toString(response.getEntity(), "utf-8");
            System.out.println(jsonStr);
            //  JSONObject object = JSON.parseObject(jsonStr);
            // System.out.println(object.getString("tinyurl"));
            //  return object.getString("tinyurl");
            JSONArray jsonArray = JSON.parseObject(jsonStr, JSONArray.class);
            JSONObject object = jsonArray.getJSONObject(0);
            System.out.println(object.getString("url_short"));
            try {
                Thread.sleep(20);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return object.getString("url_short");
        } catch (Exception e) {
            e.printStackTrace();
            return "Error";
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 测试生成端连接
     *
     * @param args
     * @author-cp:wangfeng
     */
    public static void main(String[] args) {
        generateShortUrl("http://www.teyuntong.com/info_fee_sms.html");
        generateShortUrl("http://help.baidu.com/index?xx=456");

    }
}  




