package com.tyt.web.service;

import java.sql.Timestamp;
import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.SecondCar;

public interface SecondCarService extends  BaseService<SecondCar, Long>  {
	public List<SecondCar> QueryByDistinguish(int distinguish);
	public SecondCar findById(int id);
	public SecondCar getDuplicate(SecondCar secondCar);
    public void insertCollect(final Integer visiteStatus,final Long userId,final Long secondCarId,final Timestamp ctime);
	public void updateServeDays();

}
