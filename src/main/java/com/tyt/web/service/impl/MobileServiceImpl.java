package com.tyt.web.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Mobile;
import com.tyt.web.dao.MobileDao;
import com.tyt.web.service.MobileService;
@Service("mobileService")
public class MobileServiceImpl extends BaseServiceImpl<Mobile, Long> implements
		MobileService {

	@Resource(name="mobileDao")
    public void setBaseDao(BaseDao<Mobile, Long> mobileDao) {
        super.setBaseDao(mobileDao);
    }
	@Override
	public String getByArea(String area) {
		return ((MobileDao)this.getBaseDao()).getByArea(area);
	}

	
}
