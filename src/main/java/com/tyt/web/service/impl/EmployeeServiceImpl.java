package com.tyt.web.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Employee;
import com.tyt.util.TimeUtil;
import com.tyt.web.service.EmployeeService;

@Service("employeeService")
public class EmployeeServiceImpl extends BaseServiceImpl<Employee,Long> implements EmployeeService {
	
	@Resource(name="employeeDao")
	public void setBaseDao(BaseDao<Employee, Long> employeeDao) {
	        super.setBaseDao(employeeDao);
	}
	@Override
	public Employee getDuplicate(Employee e) {
		StringBuffer sql=new StringBuffer();
		boolean hasfirst = false;
		
		if (StringUtils.hasLength(e.getTitle())) {
			sql.append(" entity.title='").append(e.getTitle()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if(hasfirst)sql.append(" and ");
		sql.append(" date_format(entity.ctime,'%Y-%m-%d')='").append(TimeUtil.formatDate(new Date())).append("'");
		if(StringUtils.hasLength(e.getCellPhone())){
			if(hasfirst)sql.append(" and");
			sql.append(" entity.cellPhone='").append(e.getCellPhone()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		
		if (!hasfirst)sql.append(" 1=1 ");
			
        Employee emp=new Employee();
		List<Employee> employees=getList(sql.toString(), null);
		if(employees.size()>0){
			emp=employees.get(0);
			return emp;
		}
        return null;
}
	
}
