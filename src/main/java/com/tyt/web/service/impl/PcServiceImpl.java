package com.tyt.web.service.impl;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Collect;
import com.tyt.web.dao.PcDao;
import com.tyt.web.service.PcService;
@Service("pcService")
public class PcServiceImpl extends BaseServiceImpl<Collect,Long> implements PcService{

	@Autowired 
	private PcDao pcDao;
	@Resource(name="pcDao")
	public void setBaseDao(BaseDao<Collect, Long> pcDao) {
	        super.setBaseDao(pcDao);
	}
	@Override
	public void deleteCollectByEmpId(Long employeeId,Long userId) {
		pcDao.deleteCollectByEmpId(employeeId, userId);
	}
	@Override
	public void deleteCollectBySeekId(Long seekId,Long userId) {
		pcDao.deleteCollectBySeekId(seekId, userId);
		
	}
	@Override
	public void deleteCollectBySecondCarId(Long secondcarId,Long userId) {
		pcDao.deleteCollectBySecondCarId(secondcarId, userId);
		
	}
	@Override
	public void deleteCollectByForSecondId(Long forsecondId,Long userId) {
		pcDao.deleteCollectByForSecondId(forsecondId, userId);
		
	}
	@Override
	public void deleteCollectByNewCarId(Long newcarId,Long userId) {
		pcDao.deleteCollectByNewCarId(newcarId, userId);
		
	}
	@Override
	public void deleteCollectByInsureId(Long insureId,Long userId) {
		pcDao.deleteCollectByInsureId(insureId, userId);
		
	}
	@Override
	public void deleteCollectByTakeCarId(Long takecarId,Long userId) {
		pcDao.deleteCollectByTakeCarId(takecarId, userId);
		
	}
	@Override
	public void deleteTransportByTransportId(Long transportId,String cellPhone) {
		pcDao.deleteTransportById(transportId, cellPhone);
		
	}

}
