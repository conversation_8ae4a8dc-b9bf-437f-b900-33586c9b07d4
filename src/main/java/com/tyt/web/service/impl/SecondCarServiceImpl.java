package com.tyt.web.service.impl;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.SecondCar;
import com.tyt.util.TimeUtil;
import com.tyt.web.dao.EmployeeDao;
import com.tyt.web.dao.SecondCarDao;
import com.tyt.web.service.SecondCarService;

@Service("secondCarService")
public class SecondCarServiceImpl extends BaseServiceImpl<SecondCar,Long> implements SecondCarService {
	@Autowired 
	private SecondCarDao secondCarDao;
	@Resource(name="secondCarDao")
	public void setBaseDao(BaseDao<SecondCar, Long> secondCarDao) {
	        super.setBaseDao(secondCarDao);
	}
    @Override
    public List<SecondCar> QueryByDistinguish(int distinguish){
    	List<SecondCar> list=secondCarDao.QueryByDistinguish(distinguish);
		return list;
    }
    
    @Override
    public SecondCar findById(int id){
    	return secondCarDao.findById(id);
    }
	@Override
	public SecondCar getDuplicate(SecondCar secondCar) {
		StringBuffer sql=new StringBuffer();
		boolean hasfirst = false;
		
		if (StringUtils.hasLength(secondCar.getTitle())) {
			sql.append(" entity.title='").append(secondCar.getTitle()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if(hasfirst)sql.append(" and ");
		sql.append(" date_format(entity.ctime,'%Y-%m-%d')='").append(TimeUtil.formatDate(new Date())).append("'");
		if(StringUtils.hasLength(secondCar.getCellPhone())){
			if(hasfirst)sql.append(" and");
			sql.append(" entity.cellPhone='").append(secondCar.getCellPhone()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		
		if (!hasfirst)sql.append(" 1=1 ");
			
		SecondCar car=new SecondCar();
		List<SecondCar> secondCars=secondCarDao.search(sql.toString(),null);
		if(secondCars.size()>0){
			car=secondCars.get(0);
			secondCars.clear();
			secondCars=null;
			return car;
		}
        return null;
	}
	
	
	public void insertCollect( Integer visiteStatus,Long userId,Long secondCarId, Timestamp ctime) {
		secondCarDao.insertCollect( visiteStatus,userId,secondCarId,ctime);
	}
	@Override
	public void updateServeDays() {
		((SecondCarDao) this.getBaseDao()).decreServeDays();
		
	}
}
