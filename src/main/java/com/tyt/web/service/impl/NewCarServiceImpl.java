package com.tyt.web.service.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.NewCar;
import com.tyt.util.TimeUtil;
import com.tyt.web.dao.NewCarDao;
import com.tyt.web.service.NewCarService;
@Service("newCarService")
public class NewCarServiceImpl extends BaseServiceImpl<NewCar,Long> implements NewCarService {
	@Resource(name="newCarDao")
	public void setBaseDao(BaseDao<NewCar, Long> newCarDao) {
	        super.setBaseDao(newCarDao);
	}
	
	@Override
	public void insertCollect(Integer visiteStatus, Long userId, Long newCarId,
			Timestamp ctime) {
		((NewCarDao)this.getBaseDao()).insertCollect(visiteStatus, userId, newCarId, ctime);
	}
    
	 
	@Override
	public NewCar getDuplicate(NewCar n) {
		StringBuffer sql=new StringBuffer();
		boolean hasfirst = false;
		if (StringUtils.hasLength(n.getTitle())) {
			sql.append(" entity.title='").append(n.getTitle()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if(hasfirst)sql.append(" and ");
		sql.append(" date_format(entity.ctime,'%Y-%m-%d')='").append(TimeUtil.formatDate(new Date())).append("'");
		if(StringUtils.hasLength(n.getCellPhone())){
			if(hasfirst)sql.append(" and");
			sql.append(" entity.cellPhone='").append(n.getCellPhone()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if (!hasfirst)sql.append(" 1=1 ");
        NewCar newcar=new NewCar();
		List<NewCar> newCars=this.getBaseDao().search(sql.toString(),null);
		if(newCars.size()>0){
			newcar=newCars.get(0);
			return newcar;
		}
        return null;
	}
}
