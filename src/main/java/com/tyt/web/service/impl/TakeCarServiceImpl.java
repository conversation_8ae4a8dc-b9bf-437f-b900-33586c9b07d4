package com.tyt.web.service.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TakeCar;
import com.tyt.util.TimeUtil;
import com.tyt.web.dao.TakeCarDao;
import com.tyt.web.service.TakeCarService;
@Service("takeCarService")
public class TakeCarServiceImpl extends BaseServiceImpl<TakeCar,Long> implements TakeCarService{
	@Resource(name="takeCarDao")
	public void setBaseDao(BaseDao<TakeCar, Long> takeCarDao) {
	        super.setBaseDao(takeCarDao);
	}
	@Override
	public void insertCollect(Integer visiteStatus, Long userId,
			Long takeCarId, Timestamp ctime) {
		    ((TakeCarDao)this.getBaseDao()).insertCollect(visiteStatus, userId, takeCarId, ctime);
		
	}
	@Override
	public TakeCar getDuplicate(TakeCar t) {
		StringBuffer sql=new StringBuffer();
		boolean hasfirst = false;
		if (StringUtils.hasLength(t.getTitle())) {
			sql.append(" entity.title='").append(t.getTitle()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if(hasfirst)sql.append(" and ");
		sql.append(" date_format(entity.ctime,'%Y-%m-%d')='").append(TimeUtil.formatDate(new Date())).append("'");
		if(StringUtils.hasLength(t.getCellPhone())){
			if(hasfirst)sql.append(" and");
			sql.append(" entity.cellPhone='").append(t.getCellPhone()).append("'");
			if (!hasfirst)hasfirst = true;
		}
		if (!hasfirst)sql.append(" 1=1 ");
		TakeCar takecar=new TakeCar();
		List<TakeCar> takecars=this.getBaseDao().search(sql.toString(),null);
		if(takecars.size()>0){
			takecar=takecars.get(0);
			return takecar;
		}
        return null;
	}
}
