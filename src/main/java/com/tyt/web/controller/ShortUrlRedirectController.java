package com.tyt.web.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.service.common.obfuscate.ObfuscateUtil;
import com.tyt.transport.service.TransportDoneService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/s-url")
public class ShortUrlRedirectController extends BaseController {

    @Resource(name = "transportDoneService")
    private TransportDoneService transportDoneService;

    /**
     *
     * @param oTsId
     * @param oStatus  1允许 2拒绝
     * @return
     */
    @RequestMapping(value = "/location/{oTsId}/{oStatus}")
    @ResponseBody
    public String setAllowLocationForCar(@PathVariable String oTsId, @PathVariable String oStatus) {

        Long tsId = ObfuscateUtil.fromObfuscatedId(oTsId);
        int status = ObfuscateUtil.fromObfuscatedId(oStatus).intValue();
        logger.info("车方反馈,是否同意查看定位,货源ID:{},状态:{}", tsId, status);

        transportDoneService.updateAllowStatus(tsId,status);
        String showContent = "";
        // 显示的文字内容
        if (status==1){
            showContent = "已同意货方查看货物位置";
        }else{
            showContent = "已拒绝货方查看货物位置";
        }
//        ObfuscateUtil.toObfuscatedId();
        String result = "<!DOCTYPE html>" +
                "<html>" +
                "<head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" /></head>" +
                "<body style=\"width: 100%;height: 100%;padding: 0;margin: 0;\"><p style=\"position: absolute;left: 0;top: 25%;width: 100%;text-align: center;color: #666; font-size:4.5rem;\">" +
                showContent +
                "</p></body>" +
                "</html>";
        return result;
    }
}
