package com.tyt.web.querybean;

import java.sql.Timestamp;

public class TempInfo{
	   
	   Long id;
	   Integer type;
	   String title;
	   Timestamp pubTime;
	   Integer status;
	public TempInfo() {
	}
	public TempInfo(Long id, Integer type, String title,
			Timestamp pubTime, Integer status) {
		this.id = id;
		this.type = type;
		this.title = title;
		this.pubTime = pubTime;
		this.status = status;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public Timestamp getPubTime() {
		return pubTime;
	}
	public void setPubTime(Timestamp pubTime) {
		this.pubTime = pubTime;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
}