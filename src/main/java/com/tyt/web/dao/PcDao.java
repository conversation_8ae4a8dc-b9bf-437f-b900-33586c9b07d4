package com.tyt.web.dao;
import com.tyt.base.dao.BaseDao;
import com.tyt.model.Collect;

public interface PcDao extends BaseDao<Collect,Long> {
	public void deleteCollectByEmpId(Long employeeId,Long userId);
	public void deleteCollectBySeekId(Long seekId,Long userId);
	public void deleteCollectBySecondCarId(Long secondcarId,Long userId);
	public void deleteCollectByForSecondId(Long forsecondId,Long userId);
	public void deleteCollectByNewCarId(Long newcarId,Long userId);
	public void deleteCollectByInsureId(Long insureId,Long userId);
	public void deleteCollectByTakeCarId(Long takecarId,Long userId);
	public void deleteTransportById(Long transportId,String cellPhone);
}
