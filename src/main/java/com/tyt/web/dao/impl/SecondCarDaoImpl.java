package com.tyt.web.dao.impl;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.SecondCar;
import com.tyt.util.Constant;
import com.tyt.web.dao.SecondCarDao;
@Repository("secondCarDao")
public class SecondCarDaoImpl extends BaseDaoImpl<SecondCar,Long> implements SecondCarDao{
       public SecondCarDaoImpl(){
		   this.setEntityClass(SecondCar.class);
	   }
	@Override
	public List<SecondCar> QueryByDistinguish(int distinguish) {
		StringBuffer sql = new StringBuffer();
		sql.append("from SecondCarQueryBean where distinguish="+distinguish);
		List<SecondCar> list= (List<SecondCar>) getHibernateTemplate().find(sql.toString());
		return list;
	};
	@Override
	public SecondCar findById(int id){
		StringBuffer sql=new StringBuffer();
		sql.append("from SecondCar where id="+id);
		List<SecondCar> list= (List<SecondCar>) getHibernateTemplate().find(sql.toString());
		if(list.size()!=0){
			return list.get(0);
			
		}
    	return null;
    }
	@Override
	public void insertCollect(final Integer visiteStatus, final Long userId,
			final Long secondCarId, final Timestamp ctime) {
		HibernateTemplate tmpl = getHibernateTemplate();
	    tmpl.execute(new HibernateCallback<Object>() {
	        @Override
	        public Object doInHibernate(Session session)
	                throws HibernateException, SQLException {
	            SQLQuery query = session.createSQLQuery("insert into tyt_collect(visite_status,user_id,secondcar_id,ctime) values("+visiteStatus+","+userId+","+secondCarId+",'"+ctime+"')");
	            query.executeUpdate();
	            return null;
	        }
	    });
		
	}
	@Override
	public void decreServeDays() {
	    HibernateTemplate tmpl = getHibernateTemplate();
	    tmpl.execute(new HibernateCallback<Object>() {
	        @Override
	        public Object doInHibernate(Session session)
	                throws HibernateException, SQLException {
	            SQLQuery query = session.createSQLQuery("update tyt_secondcar set save_day = save_day-1 where save_day>=1 and DATEDIFF( CURDATE() , ctime ) >=1");
	            SQLQuery Out = session.createSQLQuery("update tyt_secondcar set second_status="+Constant.INFO_STATUS_OUT+" where save_day=0 and second_status!="+Constant.INFO_STATUS_OUT);
	            System.out.println("secondcar save days change:"+query.executeUpdate()+"--"+Out.executeUpdate());
	            return null;
	        }
	    });
	}

	
}
