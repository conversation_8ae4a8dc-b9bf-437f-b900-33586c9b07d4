package com.tyt.web.dao.impl;
import java.sql.SQLException;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.Collect;
import com.tyt.web.dao.PcDao;

@Repository("pcDao")
public class PcDaoImpl extends BaseDaoImpl<Collect,Long> implements PcDao{
   public PcDaoImpl(){
	   this.setEntityClass(Collect.class);
   }


@Override
public void deleteCollectByEmpId(final Long employeeId,final Long userId) {
	HibernateTemplate tmpl = getHibernateTemplate();
    tmpl.execute(new HibernateCallback<Object>() {
        @Override
        public Object doInHibernate(Session session)
                throws HibernateException, SQLException {
            SQLQuery query = session.createSQLQuery("delete from  tyt_collect where user_id="+userId+" and employee_id="+employeeId);
            query.executeUpdate();
            return null;
        }
    });
}

@Override
public void deleteCollectBySeekId(final Long seekId,final Long userId) {
	HibernateTemplate tmpl = getHibernateTemplate();
    tmpl.execute(new HibernateCallback<Object>() {
        @Override
        public Object doInHibernate(Session session)
                throws HibernateException, SQLException {
            SQLQuery query = session.createSQLQuery("delete from  tyt_collect where user_id="+userId+" and seek_id="+seekId);
            query.executeUpdate();
            return null;
        }
    });
	
}

@Override
public void deleteCollectBySecondCarId(final Long secondcarId,final Long userId) {
	HibernateTemplate tmpl = getHibernateTemplate();
    tmpl.execute(new HibernateCallback<Object>() {
        @Override
        public Object doInHibernate(Session session)
                throws HibernateException, SQLException {
            SQLQuery query = session.createSQLQuery("delete from  tyt_collect where user_id="+userId+" and secondcar_id="+secondcarId);
            query.executeUpdate();
            return null;
        }
    });
}

@Override
public void deleteCollectByForSecondId(final Long forsecondId,final Long userId) {
	HibernateTemplate tmpl = getHibernateTemplate();
    tmpl.execute(new HibernateCallback<Object>() {
        @Override
        public Object doInHibernate(Session session)
                throws HibernateException, SQLException {
            SQLQuery query = session.createSQLQuery("delete from  tyt_collect where user_id="+userId+" and forsecond_id="+forsecondId);
            query.executeUpdate();
            return null;
        }
    });
}

@Override
public void deleteCollectByNewCarId(final Long newcarId,final Long userId) {
	HibernateTemplate tmpl = getHibernateTemplate();
    tmpl.execute(new HibernateCallback<Object>() {
        @Override
        public Object doInHibernate(Session session)
                throws HibernateException, SQLException {
            SQLQuery query = session.createSQLQuery("delete from  tyt_collect where user_id="+userId+" and newcar_id="+newcarId);
            query.executeUpdate();
            return null;
        }
    });
}

@Override
public void deleteCollectByInsureId(final Long insureId,final Long userId) {
	HibernateTemplate tmpl = getHibernateTemplate();
    tmpl.execute(new HibernateCallback<Object>() {
        @Override
        public Object doInHibernate(Session session)
                throws HibernateException, SQLException {
            SQLQuery query = session.createSQLQuery("delete from  tyt_collect where user_id="+userId+" and insure_id="+insureId);
            query.executeUpdate();
            return null;
        }
    });
}

@Override
public void deleteCollectByTakeCarId(final Long takecarId,final Long userId) {
	HibernateTemplate tmpl = getHibernateTemplate();
    tmpl.execute(new HibernateCallback<Object>() {
        @Override
        public Object doInHibernate(Session session)
                throws HibernateException, SQLException {
            SQLQuery query = session.createSQLQuery("delete from  tyt_collect where user_id="+userId+" and takecar_id="+takecarId);
            query.executeUpdate();
            return null;
        }
    });
}


@Override
public void deleteTransportById(final Long transportId,final String cellPhone) {
	HibernateTemplate tmpl = getHibernateTemplate();
    tmpl.execute(new HibernateCallback<Object>() {
        @Override
        public Object doInHibernate(Session session)
                throws HibernateException, SQLException {
            SQLQuery query = session.createSQLQuery("update tyt_transport_collect set status=0 where cell_phone='"+cellPhone+"' and info_id="+transportId);
            query.executeUpdate();
            return null;
        }
    });
}









}
