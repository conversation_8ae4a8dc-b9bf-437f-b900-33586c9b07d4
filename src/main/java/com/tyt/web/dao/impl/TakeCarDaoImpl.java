package com.tyt.web.dao.impl;

import java.sql.SQLException;
import java.sql.Timestamp;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TakeCar;
import com.tyt.web.dao.TakeCarDao;
@Repository("takeCarDao")
public class TakeCarDaoImpl extends BaseDaoImpl<TakeCar,Long> implements TakeCarDao  {
	public TakeCarDaoImpl(){
		   this.setEntityClass(TakeCar.class);
	   }

	@Override
	public void insertCollect(final Integer visiteStatus, final Long userId,
			final Long takeCarId,final Timestamp ctime) {
		HibernateTemplate tmpl = getHibernateTemplate();
	    tmpl.execute(new HibernateCallback<Object>() {
	        @Override
	        public Object doInHibernate(Session session)
	                throws HibernateException, SQLException {
	            SQLQuery query = session.createSQLQuery("insert into tyt_collect(visite_status,user_id,takecar_id,ctime) values("+visiteStatus+","+userId+","+takeCarId+",'"+ctime+"')");
	            query.executeUpdate();
	            return null;
	        }
	    });
	}
}
