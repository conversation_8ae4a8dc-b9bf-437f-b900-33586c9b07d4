package com.tyt.web.dao.impl;


import java.sql.SQLException;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.Geo;
import com.tyt.model.Mobile;
import com.tyt.web.dao.MobileDao;
@Repository("mobileDao")
public class MobileDaoImpl extends BaseDaoImpl<Mobile, Long> implements MobileDao {
	
	public MobileDaoImpl() {
	        this.setEntityClass(Mobile.class);
	    }
	
	@Override
	public String getByArea(final String area) {
		HibernateTemplate tmpl = getHibernateTemplate();
	    return (String) tmpl.execute(new HibernateCallback<Object>() {
	        @Override
	        public Object doInHibernate(Session session)
	                throws HibernateException, SQLException {
	            SQLQuery query = session.createSQLQuery("SELECT DISTINCT(address) FROM tyt_mobile WHERE area="+area);
	            return query.uniqueResult();
	        }
	    });
	}

	
}
