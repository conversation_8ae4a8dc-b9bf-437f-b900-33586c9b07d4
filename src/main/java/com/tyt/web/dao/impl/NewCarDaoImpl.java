package com.tyt.web.dao.impl;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;
import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.NewCar;
import com.tyt.web.dao.NewCarDao;
@Repository("newCarDao")
public class NewCarDaoImpl extends BaseDaoImpl<NewCar,Long> implements NewCarDao{
	public NewCarDaoImpl(){
		   this.setEntityClass(NewCar.class);
	   }
	@Override
	public void insertCollect(final Integer visiteStatus,final Long userId,final Long newCarId,final Timestamp ctime) {
		HibernateTemplate tmpl = getHibernateTemplate();
	    tmpl.execute(new HibernateCallback<Object>() {
	        @Override
	        public Object doInHibernate(Session session)
	                throws HibernateException, SQLException {
	            SQLQuery query = session.createSQLQuery("insert into tyt_collect(visite_status,user_id,newcar_id,ctime) values("+visiteStatus+","+userId+","+newCarId+",'"+ctime+"')");
	            query.executeUpdate();
	            return null;
	        }
	    });
		
	}

}
