package com.tyt.bi.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.plat.constant.RemoteApiConstant;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/12/16
 */
public interface IBiRequestService {

    /**
     * 请求bi
     *
     * @param biPath biPath
     * @param params 业务参数, 不需要api_time和api_sign
     * @return ResultMsgBean
     * @see RemoteApiConstant
     */
    ResultMsgBean doPost(String biPath, Map<String, Object> params,boolean isNew);
}
