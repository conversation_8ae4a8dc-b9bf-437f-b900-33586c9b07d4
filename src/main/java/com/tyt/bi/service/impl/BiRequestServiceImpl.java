package com.tyt.bi.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.bi.service.IBiRequestService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.CircuitBreakerUtil;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/12/16
 */
@Slf4j
@Service
public class BiRequestServiceImpl implements IBiRequestService {
    @Autowired
    private TytConfigService tytConfigService;

    @Override
    public ResultMsgBean doPost(String biPath, Map<String, Object> params,boolean isNew) {
        ResultMsgBean result = ResultMsgBean.successResponse();
        try {
            Map<String, Object> paramMap = new HashMap<>();

            if (params != null) {
                paramMap.putAll(params);
            }

            paramMap.put("api_time", TimeUtil.formatDateTime(new Date()));
            paramMap.put("api_sign", "8d60c1fdf478100169faf26408504b4b");
            String dbcHost = tytConfigService.getStringValue("tyt:config:private:bi_data", "http://bidata.teyuntong.net");
            if (isNew) {
                dbcHost = "http://bi-qualitymodel.teyuntong.private:8080";

            }

            String apiUrl = CommonUtil.joinPath(dbcHost, biPath);
            log.info("bi请求开始,dbcHost:{}, biPath:{}, params:{}",dbcHost, biPath, params);
            String response = CircuitBreakerUtil.executeWithCircuitBreaker(
                    CircuitBreakerUtil.CircuitBreakerConfiguration.BI_SERVICE,
                    () -> HttpUtil.post(apiUrl, paramMap, 2000),
                    null);

            log.info("bi请求成功, biPath:{}, params:{}, response:{}", biPath, params,
                    StringUtils.substring(response, 0, 300));

            ResultMsgBean resultMsgBean = JSON.parseObject(response, ResultMsgBean.class);
            return resultMsgBean == null ? result : resultMsgBean;
        } catch (Exception e) {
            log.warn("请求bi失败, biPath:{}, params:{}", biPath, params, e);
            result.setCode(ResultMsgBean.ERROR);
            result.setMsg("服务器异常");
        }
        return result;
    }
}
