package com.tyt.htbx;

import java.io.ByteArrayOutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.stream.XMLStreamException;

import com.tyt.model.TransportInsurance;
import com.tyt.util.JaxbUtil;
import com.tyt.util.MD5Util;

public class Test2 {
	
	
	
	public static void main(String[] args) throws MalformedURLException, JAXBException, XMLStreamException {
		WebServiceServerImplService service=new WebServiceServerImplService(new URL("http://202.108.103.154:8080/HT_interfacePlatform/webservice/ImportService?wsdl"));
		//System.out.println(service.getWSDLDocumentLocation());
		WebServiceServer webServiceServer=service.getPort(WebServiceServer.class);
		///System.out.println(MD5Util.GetMD5Code("111111").toUpperCase());
		//2.6无投保申请直接出单
		//String  result=webServiceServer.impPolicy(getPolicy(), "U070000644-1000", MD5Util.GetMD5Code("111111"), MD5Util.GetMD5Code("1Qaz2Wsx"+getPolicy()).toUpperCase());
		String  result=webServiceServer.impPolicy(getPolicy(), "TYT", MD5Util.GetMD5Code("HuoYun@321"), MD5Util.GetMD5Code("2wsx1qaz"+getPolicy()).toUpperCase());
		
		//2.2投保申请
		//String  result=webServiceServer.appPolicy(getPolicy(), "TYT", MD5Util.GetMD5Code("HuoYun@321"), MD5Util.GetMD5Code("2wsx1qaz"+getPolicy()).toUpperCase());
		//String  result=webServiceServer.appPolicy(getPolicy(), "U070000644-1000", MD5Util.GetMD5Code("111111"), MD5Util.GetMD5Code("1Qaz2Wsx"+getPolicy()).toUpperCase());
				
		
		System.out.println(result);

	Result r=new JaxbUtil(Result.class).fromXml(result);
	System.out.println(r.getMsg()+"__"+r.getPdfURL());
	//String aa=	webServiceServer.queryPolicyManual(r.getInsurancePolicy(), "U070000644-1000", MD5Util.GetMD5Code("111111"), MD5Util.GetMD5Code("1Qaz2Wsx"+r.getInsurancePolicy()).toUpperCase());
	
	//System.out.println(aa);
	
	}
	
	public static String getStr(){
		String PlyContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Policy><general><issueTime>2018-01-18T16:41:22</issueTime><serialNumber></serialNumber><InsurancePolicy></InsurancePolicy><InsuranceCode>3501</InsuranceCode><InsuranceName>出口海洋运输货物保险</InsuranceName><EffectivTime>2018-01-18</EffectivTime><TerminalTime>2018-04-18</TerminalTime><Copy>1</Copy><SignTM>2018-01-18</SignTM></general><Freight><Sign>YJ106</Sign><PackAndQuantity>5 PALLETS / 150 PIECES</PackAndQuantity><FregihtItem>GROUND MODULE</FregihtItem><InvoiceNumber>YJ106</InvoiceNumber><BillNumber>TCLQD17070050</BillNumber><FreightType>SX001422</FreightType><FreightDetail>SX00140100</FreightDetail><InvoiceMoney>12300</InvoiceMoney><InvoiceBonus>1.100</InvoiceBonus><Amt>13530</Amt><AmtCurrency>03</AmtCurrency><ExchangeRate>6.809000</ExchangeRate><ChargeRate>0.*********</ChargeRate><Premium>22.11</Premium><PremiumCurrency>01</PremiumCurrency><PremiumPrit>02</PremiumPrit><TransportType>SX001501</TransportType><TransportDetail>01</TransportDetail><TrafficNumber>REVERENCE</TrafficNumber><FlightsCheduled>1369E</FlightsCheduled><BuildYear></BuildYear><FromCountry>HTC01</FromCountry><FromArea>QINGDAO PORT, CHINA</FromArea><PassPort></PassPort><ToContry>HTC03</ToContry><ToArea>INCHON PORT</ToArea><SurveyAdrID>501422494569</SurveyAdrID><SurveyAdr>Rm 903, Baiknam Building 188-3, 1-Ka Choong-ku Seoul 100-191 Korea, Republic ofTEL:(82) 2 7522963/4. 82 2 548 1229FAX:(82) 2 7717150.</SurveyAdr><TrantsTool></TrantsTool><StartTM>2018-01-18T00:00:00</StartTM><EndTM>2018-04-18T00:00:00</EndTM><OriginalSum>2</OriginalSum><DatePritType>2</DatePritType><Mark>DEDUCTIBLE:M04431706NS00064</Mark><CreditNO></CreditNO><CreditNODesc></CreditNODesc><TrailerNum></TrailerNum></Freight><InsureRdrs><InsureRdr><RdrCde>SX300105</RdrCde><RdrName>一切险HUATAI</RdrName><RdrDesc>.</RdrDesc></InsureRdr></InsureRdrs><Applicant><AppCode></AppCode><ApplicantName>国际货运代理有限公司</ApplicantName><Gender>9</Gender><Birthday></Birthday><IDType>99</IDType><ID>*</ID><Phone></Phone><Cell>********</Cell><Zip></Zip><Address>青岛市市北区连云港</Address><Email></Email><TaxDeduct>0</TaxDeduct><AccountBank></AccountBank><AccountNumber></AccountNumber></Applicant><Insured><InsuredName>被保险人名字</InsuredName><Gender>9</Gender><Birthday></Birthday><IDType>99</IDType><ID>*</ID><Phone></Phone><Cell>********</Cell><Zip></Zip><Address></Address><Email></Email></Insured></Policy>";
		return PlyContent;
	}
	
	
	public static String  getPolicy(){
		Policy policy =new Policy();
		//公共信息
		Policy.General general=new Policy.General();
		//流水号 特运通公司的
		general.setSerialNumber("11");
		//*份数
		general.setCopy("3");
		//*保险起期=起运时间
		general.setEffectivTime("2018-01-28");
		//*险种代码
		general.setInsuranceCode("3601");
		//*险种名称
		general.setInsuranceName("国内水路、陆路货物运输保险");
		//*保单号 默认空值由我方返回保单号   投保申请接口时不要有这个
		general.setInsurancePolicy("");
		//*出单时间 FORMAT YYYY-MM-DDTHH24:MI:SS
		general.setIssueTime("2018-01-24T12:24:37");
		
		//*签单时间
		general.setSignTM("2018-01-24");
		//*保险止期
		general.setTerminalTime("2018-02-28");
		
		policy.setGeneral(general);
		
		//货物信息
		Policy.Freight freight=new Policy.Freight();	
		//*保险金额 
		freight.setAmt("2000000.00");
		//*保险金额币种 （编码）
		freight.setAmtCurrency("01");
		//*提单号 特运通的运单号 没有给0
		freight.setBillNumber("AS PER B/L");
		//建造年份
		freight.setBuildYear("");
		//* 费率‰-
		freight.setChargeRate("0.25");
		//信用证编码
		freight.setCreditNO("");
		//信用证描述
		freight.setCreditNODesc("");
		//*日期打印方式(编码） 1:中文 2:英文 3:提单号 4:AS PER AWB
		freight.setDatePritType("1");
		//*预计抵达时间
		freight.setEndTM("2018-02-28T12:24:37");
		//*汇率
		freight.setExchangeRate("1");
		// 航次
		freight.setFlightsCheduled("");
		//*货物项目
		freight.setFregihtItem("360 挖机一台 \r\n20挖机一台\r\n货物重辆：100吨");
		//*二级货物明细（编码）
		freight.setFreightDetail("SX00140087");
		//*货物类型（编码）
		freight.setFreightType("SX001416");
		//*起运地
		freight.setFromArea("河北保定徐水县");
		//*起运地国家（编码）
		freight.setFromCountry("HTC01");
		//*加成
		freight.setInvoiceBonus("1");
		// *发票金额
		freight.setInvoiceMoney("2000000.00");
		//发票号
		freight.setInvoiceNumber("");
		//特别约定
		freight.setMark("");
/*		freight.setMark(""+			
"1，保险责任自起运时开始生效，责任终止时间以条款说明为准；投保务必在起运前完成，否则本保险无效，保险人不承担赔偿责任，已收取的保险费退还投保人。\r\n"+					
"2，出险后如发现实际为超限货物，免赔适用2000RMB或损失金额的25%，两者以高者为准；【是否超限运输以“超限运输车辆行驶公路管理规定（中华人民共和国交通运输部令2016年第62号）”为衡量标准】。\r\n"+				
"3，承运车辆必须遵守道路运输相关法律法规，违反法律法规的情况下发生任何货物损失，保险人不负责赔偿。\r\n"+
"4，承运车辆通过道路/桥梁/隧道/立交桥/限宽门（桩）等各类道路设施时，需符合设施规定的通行限制，否则造成任何损失不予赔付。\r\n"+
"5，承运车辆及驾驶人员必须具备合格驾驶证、行驶证及营运证/许可证，否则本保险无效，保险人不负赔偿责任，已收取的保险费退还投保人。\r\n"+
"6，运输全程需开启车辆定位系统；\r\n"+
"7，货物起运前，应在货物可能的所有移动方向上进行强度足够的绑扎固定；\r\n"+					
"8，列明需单独申报的货物，不能直接适用以上条件自动承保，需逐笔申报人工审核；申报时需提供尺寸、吨位、运输工具及绑扎情况、路线路况等信息；该类货物未经保险人认可而自动生成保单，该保险无效，保险人不负赔偿责任，已收取的保险费退还投保人。【需单独申报：货值500万RMB或以上/100吨以上的设备及机械车/大型变压器/风电设备（包括并不限于机身、叶片）】\r\n"+
"9，当投保金额低于货物实际价值的90%时，保险人按照投保比例（投保金额/货物实际价值）承担赔偿责任。投保金额与货物实际价值差异在10%以内的，不计算不足额比例。");
	*/	//*正文份数
		freight.setOriginalSum("3");
		//*包装及数量
		freight.setPackAndQuantity("2台");
		//途径港 
		freight.setPassPort("山东济南 河南安阳  江苏南京");
		//*保险费 
		freight.setPremium("500.00");
		//*保险费币种 （编码）默认01 RMB(01:人民币;02:港币;03:美元;04-英镑;12-欧元)
		freight.setPremiumCurrency("01");
		//* 保费打印
		freight.setPremiumPrit("02");
		//*货物标记
		freight.setSign(".");
		//*起运时间
		freight.setStartTM("2018-01-28T12:24:37");
		//查勘地址内容
		freight.setSurveyAdr("华泰财产保险有限公司北京分公司 \r\n 5 floor De Sheng Shang Cheng (North Tower), 25 De Sheng Men Wai Street, Xicheng District, Beijing \r\nTEL:4006095509 \r\nFAX:(010)59375924");
		//*查勘地址地址编码
		freight.setSurveyAdrID("501422495584");
		//*目的地
		freight.setToArea("上海市");
		//*目的地国家 （编码）
		freight.setToContry("HTC01");
		//*船名航班车号
		freight.setTrafficNumber("京A1234 京挂7890");
		//挂车车牌号
		freight.setTrailerNum("京挂7890");
		//*运输方式明细（编码）
		freight.setTransportDetail("01");
		//*运输方式（编码）
		freight.setTransportType("SX001502");
		//转运工具
		freight.setTrantsTool("汽车");
		policy.setFreight(freight);
		
		//险别信息
		Policy.InsureRdrs insureRdrs=new Policy.InsureRdrs();
		//险种清单
		List<Policy.InsureRdrs.InsureRdr>  insureRdrsList=insureRdrs.getInsureRdr();//new ArrayList<Policy.InsureRdrs.InsureRdr>();
		//险种
		Policy.InsureRdrs.InsureRdr insureRdr=new Policy.InsureRdrs.InsureRdr();
		//*责任险种编码 参数：险种清单合同结构码
		insureRdr.setRdrCde("SX300211");
		//*责任险种描述
		insureRdr.setRdrDesc("basicRisk");
		//*险种名称 
		insureRdr.setRdrName("基本险");
		
		insureRdrsList.add(insureRdr);
		policy.setInsureRdrs(insureRdrs);
		
		//投保人信息
		Policy.Applicant applicant=new Policy.Applicant();
		//*开户银行
		applicant.setAccountBank("");
		//*银行账号
		applicant.setAccountNumber("");
		//地址
		applicant.setAddress("");
		//投保人编码,固定投保人填写华泰提供的编码，投保人不固定则为空
		applicant.setAppCode("");
		//*投保人名称
		applicant.setApplicantName("王小明");
		//*出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填-
		applicant.setBirthday("1988-08-08");
		//联系手机
		applicant.setCell("***********");
		//email
		applicant.setEmail("");
		//性别，个人客户必填, 1：男；2：女；9：未说明
		applicant.setGender("1");
		//*证件号码
		applicant.setID("350881197904151535");
		//*证件类型 注意：机构客户，必选填写 97（税务登记证）
		applicant.setIDType("01");
		//固定电话
		applicant.setPhone("");
		//*是否需要增值税专用发票 0：否 1：是
		applicant.setTaxDeduct("0");
		//邮政编码
		applicant.setZip("");
		policy.setApplicant(applicant);
		
		//被保险人信息
		Policy.Insured insured=new Policy.Insured();
		//地址
		insured.setAddress("");
		//出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填
		insured.setBirthday("1988-05-18");
		//联系手机
		insured.setCell("18842082829");
		//email
		insured.setEmail("");
		//性别，个人客户必填, 1：男；2：女；9：未说明
		insured.setGender("2");
		//*证件号码
		insured.setID("130683199404267822");
		//*证件类型
		insured.setIDType("01");
		//*被保险人信息
		insured.setInsuredName("李小天");
		//固定电话
		insured.setPhone("");
		//邮政编码
		insured.setZip("");
		
		policy.setInsured(insured);
		
		//生成xml
	    String str= new JaxbUtil(Policy.class).toXml(policy, "UTF-8");
		System.out.println(str.replace(" standalone=\"yes\"", ""));
		return str;
	}
}
