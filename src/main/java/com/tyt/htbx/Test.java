package com.tyt.htbx;

import java.io.ByteArrayOutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.stream.XMLStreamException;

import com.tyt.util.JaxbUtil;

public class Test {
	
	/*public static class Mythread extends Thread {
		 private String aa="";
		  public Mythread(String aa){
			  this.aa=aa;
		  }
	        @Override
	        public void run() {
	        	new PolicyBack.Applicant().setAccountBank(aa);;
	        	System.out.println(new PolicyBack.Applicant().getAccountBank());
	        }
	    }*/

	
	public static void main(String[] args) throws MalformedURLException, JAXBException, XMLStreamException {
		WebServiceServerImplService service=new WebServiceServerImplService(new URL("http://202.108.103.154:8080/HT_interfacePlatform/webservice/ImportService?wsdl"));
		//System.out.println(service.getWSDLDocumentLocation());
		WebServiceServer webServiceServer=service.getPort(WebServiceServer.class);
		///System.out.println(MD5Util.GetMD5Code("111111").toUpperCase());
		//System.out.println(webServiceServer.impPolicy(getStr(), "U070000644-1000", MD5Util.GetMD5Code("111111"), MD5Util.GetMD5Code("1Qaz2Wsx"+getStr()).toUpperCase()));
		PolicyBack c =new PolicyBack();
		PolicyBack.Applicant a=new PolicyBack.Applicant();
		PolicyBack.Insured b=new PolicyBack.Insured();
		PolicyBack.InsureRdrs rdrs=new PolicyBack.InsureRdrs();
	List<PolicyBack.InsureRdrs.InsureRdr>  rdsList=rdrs.getInsureRdr();//new ArrayList<PolicyBack.InsureRdrs.InsureRdr>();
	PolicyBack.InsureRdrs.InsureRdr rdr=new PolicyBack.InsureRdrs.InsureRdr();
	rdr.setRdrCde("45646");
	rdsList.add(rdr);
	//rdrs.insureRdr=rdsList;
	b.setAddress("aaaaa");
	a.setAccountBank("4564");
	List<Object>  list=c.getGeneralOrFreightOrInsureRdrs();
	list.add(a);
	list.add(b);
	list.add(rdrs);
	//c.generalOrFreightOrInsureRdrs=list;
	

   JAXBContext context = JAXBContext.newInstance(PolicyBack.class);    // 获取上下文对象  
    Marshaller marshaller = context.createMarshaller(); // 根据上下文获取marshaller对象  
    marshaller.setProperty(Marshaller.JAXB_ENCODING, "utf-8");  // 设置编码字符集  
    marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true); // 格式化XML输出，有分行和缩进  
    marshaller.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.FALSE);
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    marshaller.marshal(c, baos);  
    String xmlObj = new String(baos.toByteArray());         // 生成XML字符串  
    System.out.println(xmlObj.replace(" standalone=\"yes\"", ""));  
    
    
    
   String dd= new JaxbUtil(PolicyBack.class).toXml(c, "UTF-8");
	System.out.println(dd.replace(" standalone=\"yes\"", ""));
	//String aa=	webServiceServer.queryPolicyManual("T072435012018000015", "U070000644-1000", MD5Util.GetMD5Code("111111"), MD5Util.GetMD5Code("1Qaz2Wsx"+"T072435012018000015").toUpperCase());
	//System.out.println(aa);
	
	}
	
	public static String getStr(){
		String PlyContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Policy><general><issueTime>2018-01-18T16:41:22</issueTime><serialNumber></serialNumber><InsurancePolicy></InsurancePolicy><InsuranceCode>3501</InsuranceCode><InsuranceName>出口海洋运输货物保险</InsuranceName><EffectivTime>2018-01-18</EffectivTime><TerminalTime>2018-04-18</TerminalTime><Copy>1</Copy><SignTM>2018-01-18</SignTM></general><Freight><Sign>YJ106</Sign><PackAndQuantity>5 PALLETS / 150 PIECES</PackAndQuantity><FregihtItem>GROUND MODULE</FregihtItem><InvoiceNumber>YJ106</InvoiceNumber><BillNumber>TCLQD17070050</BillNumber><FreightType>SX001422</FreightType><FreightDetail>SX00140100</FreightDetail><InvoiceMoney>12300</InvoiceMoney><InvoiceBonus>1.100</InvoiceBonus><Amt>13530</Amt><AmtCurrency>03</AmtCurrency><ExchangeRate>6.809000</ExchangeRate><ChargeRate>0.*********</ChargeRate><Premium>22.11</Premium><PremiumCurrency>01</PremiumCurrency><PremiumPrit>02</PremiumPrit><TransportType>SX001501</TransportType><TransportDetail>01</TransportDetail><TrafficNumber>REVERENCE</TrafficNumber><FlightsCheduled>1369E</FlightsCheduled><BuildYear></BuildYear><FromCountry>HTC01</FromCountry><FromArea>QINGDAO PORT, CHINA</FromArea><PassPort></PassPort><ToContry>HTC03</ToContry><ToArea>INCHON PORT</ToArea><SurveyAdrID>501422494569</SurveyAdrID><SurveyAdr>Rm 903, Baiknam Building 188-3, 1-Ka Choong-ku Seoul 100-191 Korea, Republic ofTEL:(82) 2 7522963/4. 82 2 548 1229FAX:(82) 2 7717150.</SurveyAdr><TrantsTool></TrantsTool><StartTM>2018-01-18T00:00:00</StartTM><EndTM>2018-04-18T00:00:00</EndTM><OriginalSum>2</OriginalSum><DatePritType>2</DatePritType><Mark>DEDUCTIBLE:M04431706NS00064</Mark><CreditNO></CreditNO><CreditNODesc></CreditNODesc><TrailerNum></TrailerNum></Freight><InsureRdrs><InsureRdr><RdrCde>SX300105</RdrCde><RdrName>一切险HUATAI</RdrName><RdrDesc>.</RdrDesc></InsureRdr></InsureRdrs><Applicant><AppCode></AppCode><ApplicantName>国际货运代理有限公司</ApplicantName><Gender>9</Gender><Birthday></Birthday><IDType>99</IDType><ID>*</ID><Phone></Phone><Cell>********</Cell><Zip></Zip><Address>青岛市市北区连云港</Address><Email></Email><TaxDeduct>0</TaxDeduct><AccountBank></AccountBank><AccountNumber></AccountNumber></Applicant><Insured><InsuredName>被保险人名字</InsuredName><Gender>9</Gender><Birthday></Birthday><IDType>99</IDType><ID>*</ID><Phone></Phone><Cell>********</Cell><Zip></Zip><Address></Address><Email></Email></Insured></Policy>";
		return PlyContent;
	}
	public String  getPolicy(){
		PolicyBack policy =new PolicyBack();
		List<Object>  list=policy.getGeneralOrFreightOrInsureRdrs();
		//公共信息
		PolicyBack.General general=new PolicyBack.General();	
		
		list.add(general);
		//货物信息
		PolicyBack.Freight freight=new PolicyBack.Freight();	
		

		list.add(freight);
		
		//险别信息
		PolicyBack.InsureRdrs insureRdrs=new PolicyBack.InsureRdrs();
		//险种清单
		List<PolicyBack.InsureRdrs.InsureRdr>  insureRdrsList=insureRdrs.getInsureRdr();//new ArrayList<PolicyBack.InsureRdrs.InsureRdr>();
		//险种
		PolicyBack.InsureRdrs.InsureRdr insureRdr=new PolicyBack.InsureRdrs.InsureRdr();
		insureRdr.setRdrCde("45646");
		insureRdrsList.add(insureRdr);
		
		
		list.add(insureRdrs);
		
		//投保人信息
		PolicyBack.Applicant applicant=new PolicyBack.Applicant();
		
		
		
		list.add(applicant);
		//被保险人信息
		PolicyBack.Insured insured=new PolicyBack.Insured();
		
		
		
		
		list.add(insured);
		
		//生成xml
	    String str= new JaxbUtil(PolicyBack.class).toXml(policy, "UTF-8");
		System.out.println(str.replace(" standalone=\"yes\"", ""));
		return str;
	}
}
