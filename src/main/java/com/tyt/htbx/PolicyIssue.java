//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.19 at 02:04:44 PM CST 
//


package com.tyt.htbx;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "serialNumber",
    "insurancePolicy",
    "requestTime",
    "payMode",
    "transExeTime",
    "insrpakNo",
    "paymentPrm"
})
@XmlRootElement(name = "Policy")
public class PolicyIssue {

     @XmlElement(name = "SerialNumber", required = true)
     protected String serialNumber;
     @XmlElement(name = "InsurancePolicy", required = true)
     protected String insurancePolicy;
     @XmlElement(name = "RequestTime", required = true)
     protected String requestTime;
     @XmlElement(name = "PayMode", required = true)
     protected String payMode;
     @XmlElement(name = "TransExeTime", required = true)
     protected String transExeTime;
     @XmlElement(name = "InsrpakNo", required = true)
     protected String insrpakNo;
     @XmlElement(name = "PaymentPrm", required = true)
     protected String paymentPrm;
     
	public String getSerialNumber() {
		return serialNumber;
	}
	public String getInsurancePolicy() {
		return insurancePolicy;
	}
	public String getRequestTime() {
		return requestTime;
	}
	public String getPayMode() {
		return payMode;
	}
	public String getTransExeTime() {
		return transExeTime;
	}
	public String getInsrpakNo() {
		return insrpakNo;
	}
	public String getPaymentPrm() {
		return paymentPrm;
	}
	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}
	public void setInsurancePolicy(String insurancePolicy) {
		this.insurancePolicy = insurancePolicy;
	}
	public void setRequestTime(String requestTime) {
		this.requestTime = requestTime;
	}
	public void setPayMode(String payMode) {
		this.payMode = payMode;
	}
	public void setTransExeTime(String transExeTime) {
		this.transExeTime = transExeTime;
	}
	public void setInsrpakNo(String insrpakNo) {
		this.insrpakNo = insrpakNo;
	}
	public void setPaymentPrm(String paymentPrm) {
		this.paymentPrm = paymentPrm;
	}
}
