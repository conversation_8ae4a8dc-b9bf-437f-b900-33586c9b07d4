

<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Policy">
    <xs:annotation>
      <xs:documentation>无投保申请直接出单</xs:documentation>
    </xs:annotation>
    <xs:complexType>
      <xs:sequence>
        <xs:element name="General">
          <xs:annotation>
            <xs:documentation>*签单时间</xs:documentation>
          </xs:annotation>
          <xs:complexType>
            <xs:sequence>
              <xs:element type="xs:dateTime" name="IssueTime">
                <xs:annotation>
                  <xs:documentation>公共信息</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:int" name="SerialNumber">
                <xs:annotation>
                  <xs:documentation>*出单时间 FORMAT YYYY-MM-DDTHH24:MI:SS</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="InsurancePolicy">
                <xs:annotation>
                  <xs:documentation>流水号</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:short" name="InsuranceCode">
                <xs:annotation>
                  <xs:documentation>*保单号 默认空值由我方返回保单号</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="InsuranceName">
                <xs:annotation>
                  <xs:documentation>参数: 险种清单*险种代码</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:date" name="EffectivTime">
                <xs:annotation>
                  <xs:documentation>参数: 险种清单*险种名称</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:date" name="TerminalTime">
                <xs:annotation>
                  <xs:documentation>*保险起期=起运时间</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="Copy">
                <xs:annotation>
                  <xs:documentation>*保险止期</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:date" name="SignTM">
                <xs:annotation>
                  <xs:documentation>*份数</xs:documentation>
                </xs:annotation>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Freight">
          <xs:annotation>
            <xs:documentation>挂车车牌号</xs:documentation>
          </xs:annotation>
          <xs:complexType>
            <xs:sequence>
              <xs:element type="xs:string" name="Sign"/>
              <xs:element type="xs:string" name="PackAndQuantity">
                <xs:annotation>
                  <xs:documentation>*货物标记</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="FregihtItem">
                <xs:annotation>
                  <xs:documentation>*包装及数量</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="InvoiceNumber">
                <xs:annotation>
                  <xs:documentation>*货物项目</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="BillNumber">
                <xs:annotation>
                  <xs:documentation>发票号</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="FreightType">
                <xs:annotation>
                  <xs:documentation>*提单号</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="FreightDetail">
                <xs:annotation>
                  <xs:documentation>参数: 货物类型SX0014*货物类型（编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:float" name="InvoiceMoney">
                <xs:annotation>
                  <xs:documentation>参数: 货物类型SX0014*二级货物明细（编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:float" name="InvoiceBonus">
                <xs:annotation>
                  <xs:documentation>*发票金额</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:float" name="Amt">
                <xs:annotation>
                  <xs:documentation>*加成</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="AmtCurrency">
                <xs:annotation>
                  <xs:documentation>*保险金额</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:float" name="ExchangeRate">
                <xs:annotation>
                  <xs:documentation>参数：102 *保险金额币种 （编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:float" name="ChargeRate">
                <xs:annotation>
                  <xs:documentation>*汇率</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:float" name="Premium">
                <xs:annotation>
                  <xs:documentation>* 费率‰</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="PremiumCurrency">
                <xs:annotation>
                  <xs:documentation>*保险费</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="PremiumPrit">
                <xs:annotation>
                  <xs:documentation>默认01 RMB(01:人民币;02:港币;03:美元;04-英镑;12-欧元)*保险费币种 （编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="TransportType">
                <xs:annotation>
                  <xs:documentation>参数：保费打印SX0019 * 保费打印</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="TransportDetail">
                <xs:annotation>
                  <xs:documentation>参数: 运输方式SX0015 *运输方式（编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="TrafficNumber">
                <xs:annotation>
                  <xs:documentation>参数: 运输方式SX0015 *运输方式明细（编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="FlightsCheduled">
                <xs:annotation>
                  <xs:documentation>*船名航班车号</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:short" name="BuildYear">
                <xs:annotation>
                  <xs:documentation>航次</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="FromCountry">
                <xs:annotation>
                  <xs:documentation>建造年份</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="FromArea">
                <xs:annotation>
                  <xs:documentation>参数: 检查人国家地区HTC560130*起运地国家（编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="PassPort">
                <xs:annotation>
                  <xs:documentation>*起运地</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="ToContry">
                <xs:annotation>
                  <xs:documentation>途径港</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="ToArea">
                <xs:annotation>
                  <xs:documentation>参数: 检查人国家地区HTC560130*目的地国家 （编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:long" name="SurveyAdrID">
                <xs:annotation>
                  <xs:documentation>*目的地</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="SurveyAdr">
                <xs:annotation>
                  <xs:documentation>参数: 检查人清单*查勘地址地址编码</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="TrantsTool">
                <xs:annotation>
                  <xs:documentation>参数: 检查人清单查勘地址内容</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:dateTime" name="StartTM">
                <xs:annotation>
                  <xs:documentation>转运工具</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:dateTime" name="EndTM">
                <xs:annotation>
                  <xs:documentation>*起运时间</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="OriginalSum">
                <xs:annotation>
                  <xs:documentation>*预计抵达时间</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="DatePritType">
                <xs:annotation>
                  <xs:documentation>*正文份数</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="Mark">
                <xs:annotation>
                  <xs:documentation>1:中文 2:英文 3:提单号 4:AS PER AWB *日期打印方式(编码）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="CreditNO">
                <xs:annotation>
                  <xs:documentation>特别约定</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="CreditNODesc">
                <xs:annotation>
                  <xs:documentation>信用证编码</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="TrailerNum">
                <xs:annotation>
                  <xs:documentation>信用证描述</xs:documentation>
                </xs:annotation>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="InsureRdrs">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="InsureRdr" maxOccurs="unbounded" minOccurs="0">
                <xs:annotation>
                  <xs:documentation>险别信息 描述 描述</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                  <xs:sequence>
                    <xs:element type="xs:string" name="RdrCde"/>
                    <xs:element type="xs:string" name="RdrName">
                      <xs:annotation>
                        <xs:documentation>参数：险种清单合同结构码  *编码 参数：险种清单合同结构码  *编码</xs:documentation>
                      </xs:annotation>
                    </xs:element>
                    <xs:element type="xs:string" name="RdrDesc">
                      <xs:annotation>
                        <xs:documentation>*名称 *名称</xs:documentation>
                      </xs:annotation>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Applicant">
          <xs:annotation>
            <xs:documentation>银行账号</xs:documentation>
          </xs:annotation>
          <xs:complexType mixed="true">
            <xs:sequence>
              <xs:element type="xs:string" name="AppCode">
                <xs:annotation>
                  <xs:documentation>投保人信息</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="ApplicantName">
                <xs:annotation>
                  <xs:documentation>投保人编码,固定投保人填写华泰提供的编码，投保人不固定则为空</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="Gender">
                <xs:annotation>
                  <xs:documentation>*投保人名称</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:date" name="Birthday">
                <xs:annotation>
                  <xs:documentation>性别，个人客户必填, 1：男；2：女；9：未说明</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="IDType">
                <xs:annotation>
                  <xs:documentation>出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:long" name="ID">
                <xs:annotation>
                  <xs:documentation>参数: 证件类型*证件类型 注意：机构客户，必选填写 97（税务登记证）</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:int" name="Phone">
                <xs:annotation>
                  <xs:documentation>*证件号码</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:long" name="Cell">
                <xs:annotation>
                  <xs:documentation>固定电话</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:int" name="Zip">
                <xs:annotation>
                  <xs:documentation>联系手机</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="Address">
                <xs:annotation>
                  <xs:documentation>邮政编码</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="Email">
                <xs:annotation>
                  <xs:documentation>地址</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="TaxDeduct">
                <xs:annotation>
                  <xs:documentation>Email</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="AccountBank">
                <xs:annotation>
                  <xs:documentation>是否需要增值税专用发票 0：否 1：是</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="AccountNumber">
                <xs:annotation>
                  <xs:documentation>开户银行</xs:documentation>
                </xs:annotation>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Insured">
          <xs:annotation>
            <xs:documentation>Email</xs:documentation>
          </xs:annotation>
          <xs:complexType>
            <xs:sequence>
              <xs:element type="xs:string" name="InsuredName">
                <xs:annotation>
                  <xs:documentation>被保险人信息</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="Gender">
                <xs:annotation>
                  <xs:documentation>*被保险人名称</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:date" name="Birthday">
                <xs:annotation>
                  <xs:documentation>性别，个人客户必填, 1：男；2：女；9：未说明</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:byte" name="IDType">
                <xs:annotation>
                  <xs:documentation>出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:long" name="ID">
                <xs:annotation>
                  <xs:documentation>参数: 证件类型*证件类型</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:int" name="Phone">
                <xs:annotation>
                  <xs:documentation>*证件号码</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:long" name="Cell">
                <xs:annotation>
                  <xs:documentation>固定电话</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:int" name="Zip">
                <xs:annotation>
                  <xs:documentation>联系手机</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="Address">
                <xs:annotation>
                  <xs:documentation>邮政编码</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element type="xs:string" name="Email">
                <xs:annotation>
                  <xs:documentation>地址</xs:documentation>
                </xs:annotation>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>

