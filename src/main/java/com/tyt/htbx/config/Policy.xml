<?xml version="1.0" encoding="utf-8"?>
<!--无投保申请直接出单-->
<Policy>
	<General>
		<!--公共信息-->
		<IssueTime>2015-04-12T10:24:37</IssueTime>
		<!--*出单时间 FORMAT YYYY-MM-DDTHH24:MI:SS-->
		<SerialNumber>2015112301</SerialNumber>
		<!--流水号-->
		<InsurancePolicy></InsurancePolicy>
		<!--*保单号 默认空值由我方返回保单号-->
		<InsuranceCode>3501</InsuranceCode><!--参数: 险种清单-->
		<!--*险种代码-->
		<InsuranceName>出口海洋运输货物保险</InsuranceName><!--参数: 险种清单-->
		<!--*险种名称-->
		<EffectivTime>2015-11-23</EffectivTime>
		<!--*保险起期=起运时间-->
		<TerminalTime>2015-01-23</TerminalTime>
		<!--*保险止期-->
		<Copy>1</Copy>
		<!--*份数-->
		<SignTM>2015-11-23</SignTM>
		<!--*签单时间-->
	</General>
	<Freight>
		<Sign>.</Sign>
		<!--*货物标记-->
		<PackAndQuantity>01*20GP</PackAndQuantity>
		<!--*包装及数量-->
		<FregihtItem>.....</FregihtItem>
		<!-- *货物项目-->
		<InvoiceNumber>KST-2008-160</InvoiceNumber>
		<!--发票号 -->
		<BillNumber>AS PER B/L</BillNumber>
		<!--*提单号 -->
		<FreightType>SX001401</FreightType><!--参数: 货物类型SX0014-->
		<!--*货物类型（编码） -->
		<FreightDetail>SX00140001</FreightDetail><!--参数: 货物类型SX0014-->
		<!--*二级货物明细（编码） -->
		<InvoiceMoney>10000.00</InvoiceMoney>
		<!-- *发票金额-->
		<InvoiceBonus>1.1</InvoiceBonus>
		<!--*加成 -->
		<Amt>1100.00</Amt>
		<!--*保险金额 -->
		<AmtCurrency>01</AmtCurrency><!--参数：102 -->
		<!--*保险金额币种 （编码）-->
		<ExchangeRate>6.3586</ExchangeRate>
		<!-- *汇率-->
		<ChargeRate>0.50</ChargeRate>
		<!--* 费率‰-->
		<Premium>3497.23</Premium>
		<!--*保险费 -->
		<PremiumCurrency>01</PremiumCurrency>
		<!--默认01 RMB(01:人民币;02:港币;03:美元;04-英镑;12-欧元)-->
		<!--*保险费币种 （编码）-->
		<PremiumPrit>01</PremiumPrit><!--参数：保费打印SX0019 -->
		<!--* 保费打印-->
		<TransportType>SX001503</TransportType><!--参数: 运输方式SX0015-->
		<!-- *运输方式（编码）-->
		<TransportDetail>01</TransportDetail><!--参数: 运输方式SX0015-->
		<!-- *运输方式明细（编码）-->
		<TrafficNumber>SD23456</TrafficNumber>
		<!--*船名航班车号 -->
		<FlightsCheduled>1</FlightsCheduled>
		<!-- 航次-->
		<BuildYear>2015</BuildYear>
		<!-- 建造年份-->
		<FromCountry>HTC01</FromCountry><!--参数: 检查人国家地区HTC560130-->
		<!--*起运地国家（编码） -->
		<FromArea>北京</FromArea>
		<!--*起运地 -->
		<PassPort>aast</PassPort>
		<!--途径港 -->
		<ToContry>HTC01</ToContry><!--参数: 检查人国家地区HTC560130-->
		<!--*目的地国家 （编码）-->
		<ToArea>广州</ToArea>
		<!--*目的地-->
		<SurveyAdrID>501422496537</SurveyAdrID><!--参数: 检查人清单-->
		<!--*查勘地址地址编码-->
		<SurveyAdr>北京某地某人 电话：12345678</SurveyAdr><!--参数: 检查人清单-->
		<!--查勘地址内容-->
		<TrantsTool>汽车</TrantsTool>
		<!--转运工具 -->
		<StartTM>2015-11-23T13:00:00</StartTM>
		<!--*起运时间-->
		<EndTM>2015-12-23T13:00:00</EndTM>
		<!--*预计抵达时间-->
		<OriginalSum>1</OriginalSum>
		<!--*正文份数-->
		<DatePritType>1</DatePritType><!-- 1:中文 2:英文 3:提单号 4:AS PER AWB -->
		<!--*日期打印方式(编码） -->
		<Mark>保险理赔......</Mark>
		<!--特别约定 -->
		<CreditNO></CreditNO>
		<!--信用证编码-->
		<CreditNODesc></CreditNODesc>
		<!--信用证描述-->
		<TrailerNum></TrailerNum>
		<!--挂车车牌号-->
	</Freight>
	<InsureRdrs>
		<!--险别信息 -->
		<InsureRdr>
			<RdrCde>SX300105</RdrCde><!--参数：险种清单合同结构码  -->
			<!--*编码 -->
			<RdrName>一切险HUATAI</RdrName>
			<!--*名称 -->
			<RdrDesc>描述该险种...</RdrDesc>
			<!--描述 -->
		</InsureRdr>
	</InsureRdrs>
	<Applicant>
		<!--投保人信息-->
		<AppCode>U0123456789</AppCode>
		<!--投保人编码,固定投保人填写华泰提供的编码，投保人不固定则为空-->
		<ApplicantName>某物流公司</ApplicantName>
		<!--*投保人名称-->
		<Gender>9</Gender>
		<!-- 性别，个人客户必填, 1：男；2：女；9：未说明 -->
		<Birthday>1970-01-02</Birthday>
		<!--出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填-->
		<IDType>01</IDType><!--参数: 证件类型-->
		<!--*证件类型 注意：机构客户，必选填写 97（税务登记证）-->
		<ID>*****************</ID>
		<!--*证件号码-->
		<Phone>********</Phone>
		<!--固定电话-->
		<Cell>***********</Cell>
		<!--联系手机-->
		<Zip>100100</Zip>
		<!--邮政编码-->
		<Address>No.35 JR Street BJ.China</Address>
		<!--地址-->
		<Email><EMAIL></Email>
		<!--Email-->
		*注意：增值税发票：1、证件类型：97（税务登记证）；2、证件号码：纳税人识别号；3、证件类型、证件号码、开户银行、银行账号、电话号码、地址必填；
		<TaxDeduct>1</TaxDeduct>
		<!--是否需要增值税专用发票 0：否 1：是-->
		<AccountBank></AccountBank>
		<!--开户银行-->
		<AccountNumber></AccountNumber>
		<!--银行账号-->
	</Applicant>
	<Insured>
		<!--被保险人信息-->
		<InsuredName>某货运公司</InsuredName>
		<!--*被保险人名称-->
		<Gender>9</Gender>
		<!-- 性别，个人客户必填, 1：男；2：女；9：未说明 -->
		<Birthday>1970-01-02</Birthday>
		<!--出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填-->
		<IDType>01</IDType><!--参数: 证件类型-->
		<!--*证件类型-->
		<ID>*****************</ID>
		<!--*证件号码-->
		<Phone>********</Phone>
		<!--固定电话-->
		<Cell>***********</Cell>
		<!--联系手机-->
		<Zip>100100</Zip>
		<!--邮政编码-->
		<Address>No.35 JR Street BJ.China</Address>
		<!--地址-->
		<Email><EMAIL></Email>
		<!--Email-->
	</Insured>
</Policy>