<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Policy" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="Policy" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="General">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IssueTime" type="xs:string" minOccurs="0" />
              <xs:element name="SerialNumber" type="xs:string" minOccurs="0" />
              <xs:element name="InsurancePolicy" type="xs:string" minOccurs="0" />
              <xs:element name="InsuranceCode" type="xs:string" minOccurs="0" />
              <xs:element name="InsuranceName" type="xs:string" minOccurs="0" />
              <xs:element name="EffectivTime" type="xs:string" minOccurs="0" />
              <xs:element name="TerminalTime" type="xs:string" minOccurs="0" />
              <xs:element name="Copy" type="xs:string" minOccurs="0" />
              <xs:element name="SignTM" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Freight">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Sign" type="xs:string" minOccurs="0" />
              <xs:element name="PackAndQuantity" type="xs:string" minOccurs="0" />
              <xs:element name="FregihtItem" type="xs:string" minOccurs="0" />
              <xs:element name="InvoiceNumber" type="xs:string" minOccurs="0" />
              <xs:element name="BillNumber" type="xs:string" minOccurs="0" />
              <xs:element name="FreightType" type="xs:string" minOccurs="0" />
              <xs:element name="FreightDetail" type="xs:string" minOccurs="0" />
              <xs:element name="InvoiceMoney" type="xs:string" minOccurs="0" />
              <xs:element name="InvoiceBonus" type="xs:string" minOccurs="0" />
              <xs:element name="Amt" type="xs:string" minOccurs="0" />
              <xs:element name="AmtCurrency" type="xs:string" minOccurs="0" />
              <xs:element name="ExchangeRate" type="xs:string" minOccurs="0" />
              <xs:element name="ChargeRate" type="xs:string" minOccurs="0" />
              <xs:element name="Premium" type="xs:string" minOccurs="0" />
              <xs:element name="PremiumCurrency" type="xs:string" minOccurs="0" />
              <xs:element name="PremiumPrit" type="xs:string" minOccurs="0" />
              <xs:element name="TransportType" type="xs:string" minOccurs="0" />
              <xs:element name="TransportDetail" type="xs:string" minOccurs="0" />
              <xs:element name="TrafficNumber" type="xs:string" minOccurs="0" />
              <xs:element name="FlightsCheduled" type="xs:string" minOccurs="0" />
              <xs:element name="BuildYear" type="xs:string" minOccurs="0" />
              <xs:element name="FromCountry" type="xs:string" minOccurs="0" />
              <xs:element name="FromArea" type="xs:string" minOccurs="0" />
              <xs:element name="PassPort" type="xs:string" minOccurs="0" />
              <xs:element name="ToContry" type="xs:string" minOccurs="0" />
              <xs:element name="ToArea" type="xs:string" minOccurs="0" />
              <xs:element name="SurveyAdrID" type="xs:string" minOccurs="0" />
              <xs:element name="SurveyAdr" type="xs:string" minOccurs="0" />
              <xs:element name="TrantsTool" type="xs:string" minOccurs="0" />
              <xs:element name="StartTM" type="xs:string" minOccurs="0" />
              <xs:element name="EndTM" type="xs:string" minOccurs="0" />
              <xs:element name="OriginalSum" type="xs:string" minOccurs="0" />
              <xs:element name="DatePritType" type="xs:string" minOccurs="0" />
              <xs:element name="Mark" type="xs:string" minOccurs="0" />
              <xs:element name="CreditNO" type="xs:string" minOccurs="0" />
              <xs:element name="CreditNODesc" type="xs:string" minOccurs="0" />
              <xs:element name="TrailerNum" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="InsureRdrs">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="InsureRdr" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="RdrCde" type="xs:string" minOccurs="0" />
                    <xs:element name="RdrName" type="xs:string" minOccurs="0" />
                    <xs:element name="RdrDesc" type="xs:string" minOccurs="0" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Applicant">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AppCode" type="xs:string" minOccurs="0" />
              <xs:element name="ApplicantName" type="xs:string" minOccurs="0" />
              <xs:element name="Gender" type="xs:string" minOccurs="0" />
              <xs:element name="Birthday" type="xs:string" minOccurs="0" />
              <xs:element name="IDType" type="xs:string" minOccurs="0" />
              <xs:element name="ID" type="xs:string" minOccurs="0" />
              <xs:element name="Phone" type="xs:string" minOccurs="0" />
              <xs:element name="Cell" type="xs:string" minOccurs="0" />
              <xs:element name="Zip" type="xs:string" minOccurs="0" />
              <xs:element name="Address" type="xs:string" minOccurs="0" />
              <xs:element name="Email" type="xs:string" minOccurs="0" />
              <xs:element name="TaxDeduct" type="xs:string" minOccurs="0" />
              <xs:element name="AccountBank" type="xs:string" minOccurs="0" />
              <xs:element name="AccountNumber" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Insured">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="InsuredName" type="xs:string" minOccurs="0" />
              <xs:element name="Gender" type="xs:string" minOccurs="0" />
              <xs:element name="Birthday" type="xs:string" minOccurs="0" />
              <xs:element name="IDType" type="xs:string" minOccurs="0" />
              <xs:element name="ID" type="xs:string" minOccurs="0" />
              <xs:element name="Phone" type="xs:string" minOccurs="0" />
              <xs:element name="Cell" type="xs:string" minOccurs="0" />
              <xs:element name="Zip" type="xs:string" minOccurs="0" />
              <xs:element name="Address" type="xs:string" minOccurs="0" />
              <xs:element name="Email" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>