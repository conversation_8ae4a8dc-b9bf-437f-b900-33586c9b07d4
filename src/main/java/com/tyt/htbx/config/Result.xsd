<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Result">
    <xs:complexType>
      <xs:sequence>
        <xs:element type="xs:string" name="SerialNumber">
          <xs:annotation>
            <xs:documentation>流水号</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element type="xs:byte" name="Flag">
          <xs:annotation>
            <xs:documentation>Flag为返回类型  0: 自动校验不通过（节点信息为空，格式不合法，用户验证）;  1：自动核保成功返回投保单号;  2：自动校验通过，但是自动核保不通过，需要提交人工核保</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element type="xs:string" name="Msg">
          <xs:annotation>
            <xs:documentation>信息提示</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element type="xs:string" name="InsurancePolicy"/>
        <xs:element type="xs:anyURI" name="PdfURL"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>