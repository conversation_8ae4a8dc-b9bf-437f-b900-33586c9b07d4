//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.19 at 02:04:44 PM CST 
//


package com.tyt.htbx.config;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="General">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="IssueTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SerialNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="InsurancePolicy" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="InsuranceCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="InsuranceName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="EffectivTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="TerminalTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Copy" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SignTM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="Freight">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="Sign" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="PackAndQuantity" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="FregihtItem" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="InvoiceNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="BillNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="FreightType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="FreightDetail" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="InvoiceMoney" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="InvoiceBonus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Amt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="AmtCurrency" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ExchangeRate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ChargeRate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Premium" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="PremiumCurrency" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="PremiumPrit" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="TransportType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="TransportDetail" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="TrafficNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="FlightsCheduled" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="BuildYear" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="FromCountry" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="FromArea" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="PassPort" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ToContry" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ToArea" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SurveyAdrID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SurveyAdr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="TrantsTool" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="StartTM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="EndTM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="OriginalSum" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="DatePritType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Mark" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="CreditNO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="CreditNODesc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="TrailerNum" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="InsureRdrs">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="InsureRdr" maxOccurs="unbounded" minOccurs="0">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="RdrCde" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="RdrName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="RdrDesc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="Applicant">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="AppCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ApplicantName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Gender" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Birthday" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="IDType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Phone" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Cell" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Zip" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Email" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="TaxDeduct" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="AccountBank" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="AccountNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="Insured">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="InsuredName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Gender" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Birthday" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="IDType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Phone" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Cell" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Zip" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Email" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "general",
    "freight",
    "insureRdrs",
    "applicant",
    "insured"
})
@XmlRootElement(name = "Policy")
public class Policy {

    @XmlElement(name = "General", required = true)
    protected Policy.General general;
    @XmlElement(name = "Freight", required = true)
    protected Policy.Freight freight;
    @XmlElement(name = "InsureRdrs", required = true)
    protected Policy.InsureRdrs insureRdrs;
    @XmlElement(name = "Applicant", required = true)
    protected Policy.Applicant applicant;
    @XmlElement(name = "Insured", required = true)
    protected Policy.Insured insured;

    /**
     * Gets the value of the general property.
     * 
     * @return
     *     possible object is
     *     {@link Policy.General }
     *     
     */
    public Policy.General getGeneral() {
        return general;
    }

    /**
     * Sets the value of the general property.
     * 
     * @param value
     *     allowed object is
     *     {@link Policy.General }
     *     
     */
    public void setGeneral(Policy.General value) {
        this.general = value;
    }

    /**
     * Gets the value of the freight property.
     * 
     * @return
     *     possible object is
     *     {@link Policy.Freight }
     *     
     */
    public Policy.Freight getFreight() {
        return freight;
    }

    /**
     * Sets the value of the freight property.
     * 
     * @param value
     *     allowed object is
     *     {@link Policy.Freight }
     *     
     */
    public void setFreight(Policy.Freight value) {
        this.freight = value;
    }

    /**
     * Gets the value of the insureRdrs property.
     * 
     * @return
     *     possible object is
     *     {@link Policy.InsureRdrs }
     *     
     */
    public Policy.InsureRdrs getInsureRdrs() {
        return insureRdrs;
    }

    /**
     * Sets the value of the insureRdrs property.
     * 
     * @param value
     *     allowed object is
     *     {@link Policy.InsureRdrs }
     *     
     */
    public void setInsureRdrs(Policy.InsureRdrs value) {
        this.insureRdrs = value;
    }

    /**
     * Gets the value of the applicant property.
     * 
     * @return
     *     possible object is
     *     {@link Policy.Applicant }
     *     
     */
    public Policy.Applicant getApplicant() {
        return applicant;
    }

    /**
     * Sets the value of the applicant property.
     * 
     * @param value
     *     allowed object is
     *     {@link Policy.Applicant }
     *     
     */
    public void setApplicant(Policy.Applicant value) {
        this.applicant = value;
    }

    /**
     * Gets the value of the insured property.
     * 
     * @return
     *     possible object is
     *     {@link Policy.Insured }
     *     
     */
    public Policy.Insured getInsured() {
        return insured;
    }

    /**
     * Sets the value of the insured property.
     * 
     * @param value
     *     allowed object is
     *     {@link Policy.Insured }
     *     
     */
    public void setInsured(Policy.Insured value) {
        this.insured = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="AppCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ApplicantName" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Gender" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Birthday" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="IDType" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Phone" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Cell" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Zip" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Email" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="TaxDeduct" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="AccountBank" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="AccountNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "appCode",
        "applicantName",
        "gender",
        "birthday",
        "idType",
        "id",
        "phone",
        "cell",
        "zip",
        "address",
        "email",
        "taxDeduct",
        "accountBank",
        "accountNumber"
    })
    public static class Applicant {

        @XmlElement(name = "AppCode")
        protected String appCode;
        @XmlElement(name = "ApplicantName")
        protected String applicantName;
        @XmlElement(name = "Gender")
        protected String gender;
        @XmlElement(name = "Birthday")
        protected String birthday;
        @XmlElement(name = "IDType")
        protected String idType;
        @XmlElement(name = "ID")
        protected String id;
        @XmlElement(name = "Phone")
        protected String phone;
        @XmlElement(name = "Cell")
        protected String cell;
        @XmlElement(name = "Zip")
        protected String zip;
        @XmlElement(name = "Address")
        protected String address;
        @XmlElement(name = "Email")
        protected String email;
        @XmlElement(name = "TaxDeduct")
        protected String taxDeduct;
        @XmlElement(name = "AccountBank")
        protected String accountBank;
        @XmlElement(name = "AccountNumber")
        protected String accountNumber;

        /**
         * Gets the value of the appCode property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAppCode() {
            return appCode;
        }

        /**
         * Sets the value of the appCode property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAppCode(String value) {
            this.appCode = value;
        }

        /**
         * Gets the value of the applicantName property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getApplicantName() {
            return applicantName;
        }

        /**
         * Sets the value of the applicantName property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setApplicantName(String value) {
            this.applicantName = value;
        }

        /**
         * Gets the value of the gender property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getGender() {
            return gender;
        }

        /**
         * Sets the value of the gender property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setGender(String value) {
            this.gender = value;
        }

        /**
         * Gets the value of the birthday property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBirthday() {
            return birthday;
        }

        /**
         * Sets the value of the birthday property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBirthday(String value) {
            this.birthday = value;
        }

        /**
         * Gets the value of the idType property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getIDType() {
            return idType;
        }

        /**
         * Sets the value of the idType property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setIDType(String value) {
            this.idType = value;
        }

        /**
         * Gets the value of the id property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getID() {
            return id;
        }

        /**
         * Sets the value of the id property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setID(String value) {
            this.id = value;
        }

        /**
         * Gets the value of the phone property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPhone() {
            return phone;
        }

        /**
         * Sets the value of the phone property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPhone(String value) {
            this.phone = value;
        }

        /**
         * Gets the value of the cell property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCell() {
            return cell;
        }

        /**
         * Sets the value of the cell property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCell(String value) {
            this.cell = value;
        }

        /**
         * Gets the value of the zip property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getZip() {
            return zip;
        }

        /**
         * Sets the value of the zip property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setZip(String value) {
            this.zip = value;
        }

        /**
         * Gets the value of the address property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAddress() {
            return address;
        }

        /**
         * Sets the value of the address property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAddress(String value) {
            this.address = value;
        }

        /**
         * Gets the value of the email property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getEmail() {
            return email;
        }

        /**
         * Sets the value of the email property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setEmail(String value) {
            this.email = value;
        }

        /**
         * Gets the value of the taxDeduct property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTaxDeduct() {
            return taxDeduct;
        }

        /**
         * Sets the value of the taxDeduct property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTaxDeduct(String value) {
            this.taxDeduct = value;
        }

        /**
         * Gets the value of the accountBank property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAccountBank() {
            return accountBank;
        }

        /**
         * Sets the value of the accountBank property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAccountBank(String value) {
            this.accountBank = value;
        }

        /**
         * Gets the value of the accountNumber property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAccountNumber() {
            return accountNumber;
        }

        /**
         * Sets the value of the accountNumber property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAccountNumber(String value) {
            this.accountNumber = value;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="Sign" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="PackAndQuantity" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="FregihtItem" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="InvoiceNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="BillNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="FreightType" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="FreightDetail" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="InvoiceMoney" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="InvoiceBonus" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Amt" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="AmtCurrency" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ExchangeRate" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ChargeRate" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Premium" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="PremiumCurrency" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="PremiumPrit" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="TransportType" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="TransportDetail" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="TrafficNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="FlightsCheduled" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="BuildYear" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="FromCountry" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="FromArea" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="PassPort" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ToContry" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ToArea" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SurveyAdrID" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SurveyAdr" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="TrantsTool" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="StartTM" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="EndTM" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="OriginalSum" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="DatePritType" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Mark" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="CreditNO" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="CreditNODesc" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="TrailerNum" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "sign",
        "packAndQuantity",
        "fregihtItem",
        "invoiceNumber",
        "billNumber",
        "freightType",
        "freightDetail",
        "invoiceMoney",
        "invoiceBonus",
        "amt",
        "amtCurrency",
        "exchangeRate",
        "chargeRate",
        "premium",
        "premiumCurrency",
        "premiumPrit",
        "transportType",
        "transportDetail",
        "trafficNumber",
        "flightsCheduled",
        "buildYear",
        "fromCountry",
        "fromArea",
        "passPort",
        "toContry",
        "toArea",
        "surveyAdrID",
        "surveyAdr",
        "trantsTool",
        "startTM",
        "endTM",
        "originalSum",
        "datePritType",
        "mark",
        "creditNO",
        "creditNODesc",
        "trailerNum"
    })
    public static class Freight {

        @XmlElement(name = "Sign", required = true)
        protected String sign;
        @XmlElement(name = "PackAndQuantity", required = true)
        protected String packAndQuantity;
        @XmlElement(name = "FregihtItem", required = true)
        protected String fregihtItem;
        @XmlElement(name = "InvoiceNumber", required = true)
        protected String invoiceNumber;
        @XmlElement(name = "BillNumber", required = true)
        protected String billNumber;
        @XmlElement(name = "FreightType", required = true)
        protected String freightType;
        @XmlElement(name = "FreightDetail", required = true)
        protected String freightDetail;
        @XmlElement(name = "InvoiceMoney", required = true)
        protected String invoiceMoney;
        @XmlElement(name = "InvoiceBonus", required = true)
        protected String invoiceBonus;
        @XmlElement(name = "Amt", required = true)
        protected String amt;
        @XmlElement(name = "AmtCurrency", required = true)
        protected String amtCurrency;
        @XmlElement(name = "ExchangeRate", required = true)
        protected String exchangeRate;
        @XmlElement(name = "ChargeRate", required = true)
        protected String chargeRate;
        @XmlElement(name = "Premium", required = true)
        protected String premium;
        @XmlElement(name = "PremiumCurrency", required = true)
        protected String premiumCurrency;
        @XmlElement(name = "PremiumPrit", required = true)
        protected String premiumPrit;
        @XmlElement(name = "TransportType", required = true)
        protected String transportType;
        @XmlElement(name = "TransportDetail", required = true)
        protected String transportDetail;
        @XmlElement(name = "TrafficNumber", required = true)
        protected String trafficNumber;
        @XmlElement(name = "FlightsCheduled", required = true)
        protected String flightsCheduled;
        @XmlElement(name = "BuildYear", required = true)
        protected String buildYear;
        @XmlElement(name = "FromCountry", required = true)
        protected String fromCountry;
        @XmlElement(name = "FromArea", required = true)
        protected String fromArea;
        @XmlElement(name = "PassPort", required = true)
        protected String passPort;
        @XmlElement(name = "ToContry", required = true)
        protected String toContry;
        @XmlElement(name = "ToArea", required = true)
        protected String toArea;
        @XmlElement(name = "SurveyAdrID", required = true)
        protected String surveyAdrID;
        @XmlElement(name = "SurveyAdr", required = true)
        protected String surveyAdr;
        @XmlElement(name = "TrantsTool", required = true)
        protected String trantsTool;
        @XmlElement(name = "StartTM", required = true)
        protected String startTM;
        @XmlElement(name = "EndTM", required = true)
        protected String endTM;
        @XmlElement(name = "OriginalSum", required = true)
        protected String originalSum;
        @XmlElement(name = "DatePritType", required = true)
        protected String datePritType;
        @XmlElement(name = "Mark", required = true)
        protected String mark;
        @XmlElement(name = "CreditNO", required = true)
        protected String creditNO;
        @XmlElement(name = "CreditNODesc", required = true)
        protected String creditNODesc;
        @XmlElement(name = "TrailerNum", required = true)
        protected String trailerNum;

        /**
         * Gets the value of the sign property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSign() {
            return sign;
        }

        /**
         * Sets the value of the sign property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSign(String value) {
            this.sign = value;
        }

        /**
         * Gets the value of the packAndQuantity property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPackAndQuantity() {
            return packAndQuantity;
        }

        /**
         * Sets the value of the packAndQuantity property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPackAndQuantity(String value) {
            this.packAndQuantity = value;
        }

        /**
         * Gets the value of the fregihtItem property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getFregihtItem() {
            return fregihtItem;
        }

        /**
         * Sets the value of the fregihtItem property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setFregihtItem(String value) {
            this.fregihtItem = value;
        }

        /**
         * Gets the value of the invoiceNumber property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getInvoiceNumber() {
            return invoiceNumber;
        }

        /**
         * Sets the value of the invoiceNumber property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setInvoiceNumber(String value) {
            this.invoiceNumber = value;
        }

        /**
         * Gets the value of the billNumber property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBillNumber() {
            return billNumber;
        }

        /**
         * Sets the value of the billNumber property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBillNumber(String value) {
            this.billNumber = value;
        }

        /**
         * Gets the value of the freightType property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getFreightType() {
            return freightType;
        }

        /**
         * Sets the value of the freightType property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setFreightType(String value) {
            this.freightType = value;
        }

        /**
         * Gets the value of the freightDetail property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getFreightDetail() {
            return freightDetail;
        }

        /**
         * Sets the value of the freightDetail property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setFreightDetail(String value) {
            this.freightDetail = value;
        }

        /**
         * Gets the value of the invoiceMoney property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getInvoiceMoney() {
            return invoiceMoney;
        }

        /**
         * Sets the value of the invoiceMoney property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setInvoiceMoney(String value) {
            this.invoiceMoney = value;
        }

        /**
         * Gets the value of the invoiceBonus property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getInvoiceBonus() {
            return invoiceBonus;
        }

        /**
         * Sets the value of the invoiceBonus property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setInvoiceBonus(String value) {
            this.invoiceBonus = value;
        }

        /**
         * Gets the value of the amt property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAmt() {
            return amt;
        }

        /**
         * Sets the value of the amt property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAmt(String value) {
            this.amt = value;
        }

        /**
         * Gets the value of the amtCurrency property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAmtCurrency() {
            return amtCurrency;
        }

        /**
         * Sets the value of the amtCurrency property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAmtCurrency(String value) {
            this.amtCurrency = value;
        }

        /**
         * Gets the value of the exchangeRate property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getExchangeRate() {
            return exchangeRate;
        }

        /**
         * Sets the value of the exchangeRate property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setExchangeRate(String value) {
            this.exchangeRate = value;
        }

        /**
         * Gets the value of the chargeRate property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getChargeRate() {
            return chargeRate;
        }

        /**
         * Sets the value of the chargeRate property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setChargeRate(String value) {
            this.chargeRate = value;
        }

        /**
         * Gets the value of the premium property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPremium() {
            return premium;
        }

        /**
         * Sets the value of the premium property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPremium(String value) {
            this.premium = value;
        }

        /**
         * Gets the value of the premiumCurrency property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPremiumCurrency() {
            return premiumCurrency;
        }

        /**
         * Sets the value of the premiumCurrency property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPremiumCurrency(String value) {
            this.premiumCurrency = value;
        }

        /**
         * Gets the value of the premiumPrit property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPremiumPrit() {
            return premiumPrit;
        }

        /**
         * Sets the value of the premiumPrit property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPremiumPrit(String value) {
            this.premiumPrit = value;
        }

        /**
         * Gets the value of the transportType property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTransportType() {
            return transportType;
        }

        /**
         * Sets the value of the transportType property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTransportType(String value) {
            this.transportType = value;
        }

        /**
         * Gets the value of the transportDetail property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTransportDetail() {
            return transportDetail;
        }

        /**
         * Sets the value of the transportDetail property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTransportDetail(String value) {
            this.transportDetail = value;
        }

        /**
         * Gets the value of the trafficNumber property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTrafficNumber() {
            return trafficNumber;
        }

        /**
         * Sets the value of the trafficNumber property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTrafficNumber(String value) {
            this.trafficNumber = value;
        }

        /**
         * Gets the value of the flightsCheduled property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getFlightsCheduled() {
            return flightsCheduled;
        }

        /**
         * Sets the value of the flightsCheduled property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setFlightsCheduled(String value) {
            this.flightsCheduled = value;
        }

        /**
         * Gets the value of the buildYear property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBuildYear() {
            return buildYear;
        }

        /**
         * Sets the value of the buildYear property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBuildYear(String value) {
            this.buildYear = value;
        }

        /**
         * Gets the value of the fromCountry property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getFromCountry() {
            return fromCountry;
        }

        /**
         * Sets the value of the fromCountry property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setFromCountry(String value) {
            this.fromCountry = value;
        }

        /**
         * Gets the value of the fromArea property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getFromArea() {
            return fromArea;
        }

        /**
         * Sets the value of the fromArea property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setFromArea(String value) {
            this.fromArea = value;
        }

        /**
         * Gets the value of the passPort property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPassPort() {
            return passPort;
        }

        /**
         * Sets the value of the passPort property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPassPort(String value) {
            this.passPort = value;
        }

        /**
         * Gets the value of the toContry property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getToContry() {
            return toContry;
        }

        /**
         * Sets the value of the toContry property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setToContry(String value) {
            this.toContry = value;
        }

        /**
         * Gets the value of the toArea property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getToArea() {
            return toArea;
        }

        /**
         * Sets the value of the toArea property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setToArea(String value) {
            this.toArea = value;
        }

        /**
         * Gets the value of the surveyAdrID property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSurveyAdrID() {
            return surveyAdrID;
        }

        /**
         * Sets the value of the surveyAdrID property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSurveyAdrID(String value) {
            this.surveyAdrID = value;
        }

        /**
         * Gets the value of the surveyAdr property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSurveyAdr() {
            return surveyAdr;
        }

        /**
         * Sets the value of the surveyAdr property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSurveyAdr(String value) {
            this.surveyAdr = value;
        }

        /**
         * Gets the value of the trantsTool property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTrantsTool() {
            return trantsTool;
        }

        /**
         * Sets the value of the trantsTool property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTrantsTool(String value) {
            this.trantsTool = value;
        }

        /**
         * Gets the value of the startTM property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getStartTM() {
            return startTM;
        }

        /**
         * Sets the value of the startTM property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setStartTM(String value) {
            this.startTM = value;
        }

        /**
         * Gets the value of the endTM property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getEndTM() {
            return endTM;
        }

        /**
         * Sets the value of the endTM property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setEndTM(String value) {
            this.endTM = value;
        }

        /**
         * Gets the value of the originalSum property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getOriginalSum() {
            return originalSum;
        }

        /**
         * Sets the value of the originalSum property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setOriginalSum(String value) {
            this.originalSum = value;
        }

        /**
         * Gets the value of the datePritType property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDatePritType() {
            return datePritType;
        }

        /**
         * Sets the value of the datePritType property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDatePritType(String value) {
            this.datePritType = value;
        }

        /**
         * Gets the value of the mark property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getMark() {
            return mark;
        }

        /**
         * Sets the value of the mark property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setMark(String value) {
            this.mark = value;
        }

        /**
         * Gets the value of the creditNO property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCreditNO() {
            return creditNO;
        }

        /**
         * Sets the value of the creditNO property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCreditNO(String value) {
            this.creditNO = value;
        }

        /**
         * Gets the value of the creditNODesc property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCreditNODesc() {
            return creditNODesc;
        }

        /**
         * Sets the value of the creditNODesc property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCreditNODesc(String value) {
            this.creditNODesc = value;
        }

        /**
         * Gets the value of the trailerNum property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTrailerNum() {
            return trailerNum;
        }

        /**
         * Sets the value of the trailerNum property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTrailerNum(String value) {
            this.trailerNum = value;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="IssueTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SerialNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="InsurancePolicy" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="InsuranceCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="InsuranceName" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="EffectivTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="TerminalTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Copy" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SignTM" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "issueTime",
        "serialNumber",
        "insurancePolicy",
        "insuranceCode",
        "insuranceName",
        "effectivTime",
        "terminalTime",
        "copy",
        "signTM"
    })
    public static class General {

        @XmlElement(name = "IssueTime", required = true)
        protected String issueTime;
        @XmlElement(name = "SerialNumber", required = true)
        protected String serialNumber;
        @XmlElement(name = "InsurancePolicy", required = true)
        protected String insurancePolicy;
        @XmlElement(name = "InsuranceCode", required = true)
        protected String insuranceCode;
        @XmlElement(name = "InsuranceName", required = true)
        protected String insuranceName;
        @XmlElement(name = "EffectivTime", required = true)
        protected String effectivTime;
        @XmlElement(name = "TerminalTime", required = true)
        protected String terminalTime;
        @XmlElement(name = "Copy", required = true)
        protected String copy;
        @XmlElement(name = "SignTM", required = true)
        protected String signTM;

        /**
         * Gets the value of the issueTime property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getIssueTime() {
            return issueTime;
        }

        /**
         * Sets the value of the issueTime property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setIssueTime(String value) {
            this.issueTime = value;
        }

        /**
         * Gets the value of the serialNumber property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSerialNumber() {
            return serialNumber;
        }

        /**
         * Sets the value of the serialNumber property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSerialNumber(String value) {
            this.serialNumber = value;
        }

        /**
         * Gets the value of the insurancePolicy property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getInsurancePolicy() {
            return insurancePolicy;
        }

        /**
         * Sets the value of the insurancePolicy property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setInsurancePolicy(String value) {
            this.insurancePolicy = value;
        }

        /**
         * Gets the value of the insuranceCode property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getInsuranceCode() {
            return insuranceCode;
        }

        /**
         * Sets the value of the insuranceCode property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setInsuranceCode(String value) {
            this.insuranceCode = value;
        }

        /**
         * Gets the value of the insuranceName property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getInsuranceName() {
            return insuranceName;
        }

        /**
         * Sets the value of the insuranceName property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setInsuranceName(String value) {
            this.insuranceName = value;
        }

        /**
         * Gets the value of the effectivTime property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getEffectivTime() {
            return effectivTime;
        }

        /**
         * Sets the value of the effectivTime property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setEffectivTime(String value) {
            this.effectivTime = value;
        }

        /**
         * Gets the value of the terminalTime property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTerminalTime() {
            return terminalTime;
        }

        /**
         * Sets the value of the terminalTime property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTerminalTime(String value) {
            this.terminalTime = value;
        }

        /**
         * Gets the value of the copy property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCopy() {
            return copy;
        }

        /**
         * Sets the value of the copy property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCopy(String value) {
            this.copy = value;
        }

        /**
         * Gets the value of the signTM property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSignTM() {
            return signTM;
        }

        /**
         * Sets the value of the signTM property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSignTM(String value) {
            this.signTM = value;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="InsureRdr" maxOccurs="unbounded" minOccurs="0">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="RdrCde" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="RdrName" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="RdrDesc" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "insureRdr"
    })
    public static class InsureRdrs {

        @XmlElement(name = "InsureRdr")
        protected List<Policy.InsureRdrs.InsureRdr> insureRdr;

        /**
         * Gets the value of the insureRdr property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the insureRdr property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getInsureRdr().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link Policy.InsureRdrs.InsureRdr }
         * 
         * 
         */
        public List<Policy.InsureRdrs.InsureRdr> getInsureRdr() {
            if (insureRdr == null) {
                insureRdr = new ArrayList<Policy.InsureRdrs.InsureRdr>();
            }
            return this.insureRdr;
        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="RdrCde" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="RdrName" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="RdrDesc" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rdrCde",
            "rdrName",
            "rdrDesc"
        })
        public static class InsureRdr {

            @XmlElement(name = "RdrCde", required = true)
            protected String rdrCde;
            @XmlElement(name = "RdrName", required = true)
            protected String rdrName;
            @XmlElement(name = "RdrDesc", required = true)
            protected String rdrDesc;

            /**
             * Gets the value of the rdrCde property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getRdrCde() {
                return rdrCde;
            }

            /**
             * Sets the value of the rdrCde property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setRdrCde(String value) {
                this.rdrCde = value;
            }

            /**
             * Gets the value of the rdrName property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getRdrName() {
                return rdrName;
            }

            /**
             * Sets the value of the rdrName property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setRdrName(String value) {
                this.rdrName = value;
            }

            /**
             * Gets the value of the rdrDesc property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getRdrDesc() {
                return rdrDesc;
            }

            /**
             * Sets the value of the rdrDesc property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setRdrDesc(String value) {
                this.rdrDesc = value;
            }

        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="InsuredName" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Gender" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Birthday" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="IDType" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Phone" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Cell" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Zip" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Email" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "insuredName",
        "gender",
        "birthday",
        "idType",
        "id",
        "phone",
        "cell",
        "zip",
        "address",
        "email"
    })
    public static class Insured {

        @XmlElement(name = "InsuredName", required = true)
        protected String insuredName;
        @XmlElement(name = "Gender", required = true)
        protected String gender;
        @XmlElement(name = "Birthday", required = true)
        protected String birthday;
        @XmlElement(name = "IDType", required = true)
        protected String idType;
        @XmlElement(name = "ID", required = true)
        protected String id;
        @XmlElement(name = "Phone", required = true)
        protected String phone;
        @XmlElement(name = "Cell", required = true)
        protected String cell;
        @XmlElement(name = "Zip", required = true)
        protected String zip;
        @XmlElement(name = "Address", required = true)
        protected String address;
        @XmlElement(name = "Email", required = true)
        protected String email;

        /**
         * Gets the value of the insuredName property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getInsuredName() {
            return insuredName;
        }

        /**
         * Sets the value of the insuredName property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setInsuredName(String value) {
            this.insuredName = value;
        }

        /**
         * Gets the value of the gender property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getGender() {
            return gender;
        }

        /**
         * Sets the value of the gender property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setGender(String value) {
            this.gender = value;
        }

        /**
         * Gets the value of the birthday property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBirthday() {
            return birthday;
        }

        /**
         * Sets the value of the birthday property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBirthday(String value) {
            this.birthday = value;
        }

        /**
         * Gets the value of the idType property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getIDType() {
            return idType;
        }

        /**
         * Sets the value of the idType property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setIDType(String value) {
            this.idType = value;
        }

        /**
         * Gets the value of the id property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getID() {
            return id;
        }

        /**
         * Sets the value of the id property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setID(String value) {
            this.id = value;
        }

        /**
         * Gets the value of the phone property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPhone() {
            return phone;
        }

        /**
         * Sets the value of the phone property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPhone(String value) {
            this.phone = value;
        }

        /**
         * Gets the value of the cell property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCell() {
            return cell;
        }

        /**
         * Sets the value of the cell property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCell(String value) {
            this.cell = value;
        }

        /**
         * Gets the value of the zip property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getZip() {
            return zip;
        }

        /**
         * Sets the value of the zip property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setZip(String value) {
            this.zip = value;
        }

        /**
         * Gets the value of the address property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAddress() {
            return address;
        }

        /**
         * Sets the value of the address property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAddress(String value) {
            this.address = value;
        }

        /**
         * Gets the value of the email property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getEmail() {
            return email;
        }

        /**
         * Sets the value of the email property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setEmail(String value) {
            this.email = value;
        }

    }

}
