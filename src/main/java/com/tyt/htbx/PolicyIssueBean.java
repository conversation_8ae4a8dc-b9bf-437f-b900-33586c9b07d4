//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.19 at 02:04:44 PM CST 
//


package com.tyt.htbx;

import java.util.Date;

public class PolicyIssueBean {
//流水号
     protected String serialNumber;
     // 投保单号
     protected String insurancePolicy;
     //请求时间
     protected Date requestTime;
     //支付模式 22: 微信，23：支付宝
     protected String payMode;
     //银行交易时间
     protected Date transExeTime;
     //支付流水号
     protected String insrpakNo;
     //合计保费
     protected String paymentPrm;
     
	public String getSerialNumber() {
		return serialNumber;
	}
	public String getInsurancePolicy() {
		return insurancePolicy;
	}
	public Date getRequestTime() {
		return requestTime;
	}
	public String getPayMode() {
		return payMode;
	}
	public Date getTransExeTime() {
		return transExeTime;
	}
	public String getInsrpakNo() {
		return insrpakNo;
	}
	public String getPaymentPrm() {
		return paymentPrm;
	}
	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}
	public void setInsurancePolicy(String insurancePolicy) {
		this.insurancePolicy = insurancePolicy;
	}
	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}
	public void setPayMode(String payMode) {
		this.payMode = payMode;
	}
	public void setTransExeTime(Date transExeTime) {
		this.transExeTime = transExeTime;
	}
	public void setInsrpakNo(String insrpakNo) {
		this.insrpakNo = insrpakNo;
	}
	public void setPaymentPrm(String paymentPrm) {
		this.paymentPrm = paymentPrm;
	}
}
