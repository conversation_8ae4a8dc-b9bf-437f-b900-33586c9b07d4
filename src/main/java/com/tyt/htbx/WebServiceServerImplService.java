
package com.tyt.htbx;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;

import com.tyt.util.PropertiesUtil;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "WebServiceServerImplService", targetNamespace = "http://webservice.module.ssh.com/", wsdlLocation = "http://202.108.103.154:8080/HT_interfacePlatform/webservice/ImportService?wsdl")
public class WebServiceServerImplService
    extends Service
{

    private final static URL WEBSERVICESERVERIMPLSERVICE_WSDL_LOCATION;
    private final static WebServiceException WEBSERVICESERVERIMPLSERVICE_EXCEPTION;
    private final static QName WEBSERVICESERVERIMPLSERVICE_QNAME = new QName("http://webservice.module.ssh.com/", "WebServiceServerImplService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
        	PropertiesUtil propertiesUtil = PropertiesUtil.init("server_url");
        	String urls=propertiesUtil.getString("htbx.webservice.url");
            url = new URL(urls);
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        WEBSERVICESERVERIMPLSERVICE_WSDL_LOCATION = url;
        WEBSERVICESERVERIMPLSERVICE_EXCEPTION = e;
    }

    public WebServiceServerImplService() {
        super(__getWsdlLocation(), WEBSERVICESERVERIMPLSERVICE_QNAME);
    }

    public WebServiceServerImplService(WebServiceFeature... features) {
        super(__getWsdlLocation(), WEBSERVICESERVERIMPLSERVICE_QNAME, features);
    }

    public WebServiceServerImplService(URL wsdlLocation) {
        super(wsdlLocation, WEBSERVICESERVERIMPLSERVICE_QNAME);
    }

    public WebServiceServerImplService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, WEBSERVICESERVERIMPLSERVICE_QNAME, features);
    }

    public WebServiceServerImplService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public WebServiceServerImplService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns WebServiceServer
     */
    @WebEndpoint(name = "WebServiceServerImplPort")
    public WebServiceServer getWebServiceServerImplPort() {
        return super.getPort(new QName("http://webservice.module.ssh.com/", "WebServiceServerImplPort"), WebServiceServer.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns WebServiceServer
     */
    @WebEndpoint(name = "WebServiceServerImplPort")
    public WebServiceServer getWebServiceServerImplPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.module.ssh.com/", "WebServiceServerImplPort"), WebServiceServer.class, features);
    }

    private static URL __getWsdlLocation() {
        if (WEBSERVICESERVERIMPLSERVICE_EXCEPTION!= null) {
            throw WEBSERVICESERVERIMPLSERVICE_EXCEPTION;
        }
        return WEBSERVICESERVERIMPLSERVICE_WSDL_LOCATION;
    }

}
