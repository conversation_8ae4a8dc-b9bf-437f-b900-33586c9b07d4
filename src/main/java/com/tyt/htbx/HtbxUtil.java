package com.tyt.htbx;

import java.math.BigDecimal;
import java.net.URL;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.tyt.model.TransportInsurance;
import com.tyt.util.IdCardUtil;
import com.tyt.util.JaxbUtil;
import com.tyt.util.MD5Util;
import com.tyt.util.PropertiesUtil;
import com.tyt.util.TimeUtil;

public class HtbxUtil {

	static Logger logger = LoggerFactory.getLogger("htbx");
	static Logger htbxpayLogger = LoggerFactory.getLogger("htbxpay");
	
	static PropertiesUtil propertiesUtil = PropertiesUtil.init("server_url");
	static String url=propertiesUtil.getString("htbx.webservice.url");
	static String userName=propertiesUtil.getString("htbx.webservice.user.name");
	static String userPwd=propertiesUtil.getString("htbx.webservice.user.pwd");
	static String key=propertiesUtil.getString("htbx.webservice.key");
	
	/**2.2投保申请
	 * 成功返回 投保号，失败为null
	 */
	public  static String insuranceApply(TransportInsurance transportInsurance){
		try{
			WebServiceServerImplService service=new WebServiceServerImplService(new URL(url));
			WebServiceServer htbxServer=service.getPort(WebServiceServer.class);
			String xml=getPolicy(transportInsurance);
			logger.info("htbx_appPolicy_request_xml="+xml);
			//String  resultStr=htbxServer.impPolicy(xml, userName, MD5Util.GetMD5Code(userPwd), MD5Util.GetMD5Code(key+xml).toUpperCase());
			String  resultStr=htbxServer.appPolicy(xml, userName, MD5Util.GetMD5Code(userPwd), MD5Util.GetMD5Code(key+xml).toUpperCase());
			
			logger.info("htbx_appPolicy_request_result_xml="+resultStr);
			Result result=new JaxbUtil(Result.class).fromXml(resultStr);
			if(result!=null){
				if("1".equals(result.getFlag())){
					return result.getInsurancePolicy();
				}else{
					logger.error("htbx_appPolicy_error_result_getFlag="+result.getFlag());
					return null;
				}
			}else{
				logger.error("htbx_appPolicy_error_result=null");
				return null;
			}
		}catch(Exception e){
			e.printStackTrace();
			logger.error("htbx_appPolicy_error:",e);
			return null;
		}
		
	}
	
	

	public static String  getPolicy(TransportInsurance transportInsurance){
		Policy policy =new Policy();
		//公共信息
		Policy.General general=new Policy.General();
		//流水号 特运通公司的
		general.setSerialNumber(transportInsurance.getOrderNo());
		//*份数
		general.setCopy("3");
		//*保险起期=起运时间 2018-02-25
		general.setEffectivTime(TimeUtil.formatDate(transportInsurance.getStartTime()));
		//*险种代码
		general.setInsuranceCode("3601");
		//*险种名称
		general.setInsuranceName("国内水路、陆路货物运输保险");
		//*保单号 默认空值由我方返回保单号   投保申请接口时不要有这个
		//general.setInsurancePolicy("");
		//*出单时间 FORMAT YYYY-MM-DDTHH24:MI:SS
		general.setIssueTime(TimeUtil.formatHTBXDateTime(transportInsurance.getCtime()));
		
		//*签单时间"2018-01-24"
		general.setSignTM(TimeUtil.formatDate(transportInsurance.getCtime()));
		//*保险止期"2018-02-25"
		general.setTerminalTime(TimeUtil.formatDate(transportInsurance.getEffectiveTime()));
		
		policy.setGeneral(general);
		
		//货物信息
		Policy.Freight freight=new Policy.Freight();	
		//*保险金额 2000000.00
		freight.setAmt(new BigDecimal(transportInsurance.getAmtCurrency()).movePointRight(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
		//*保险金额币种 （编码）
		freight.setAmtCurrency("01");
		//*提单号 特运通的运单号 没有给0
		freight.setBillNumber("AS PER B/L");
		//建造年份
		freight.setBuildYear("");
		//* 费率‰-0.25
		freight.setChargeRate(new BigDecimal(transportInsurance.getPremium()).movePointLeft(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
		//信用证编码
		freight.setCreditNO("");
		//信用证描述
		freight.setCreditNODesc("");
		//*日期打印方式(编码） 1:中文 2:英文 3:提单号 4:AS PER AWB
		freight.setDatePritType("1");
		//*预计抵达时间"2018-02-25T12:24:37"
		freight.setEndTM(TimeUtil.formatHTBXDateTime(transportInsurance.getEffectiveTime()));
		//*汇率
		freight.setExchangeRate("1");
		// 航次
		freight.setFlightsCheduled("");
		//*货物项目 "360 挖机一台 \r\n20挖机一台\r\n货物重辆：100吨"
		StringBuffer sb =new StringBuffer();
		int n=0;
		if(transportInsurance.getTaskContent1()!=null&&!"".equalsIgnoreCase(transportInsurance.getTaskContent1())){
			sb.append(transportInsurance.getTaskContent1());
			n=n+1;
		}
		if(transportInsurance.getTaskContent2()!=null&&!"".equalsIgnoreCase(transportInsurance.getTaskContent2())){
			sb.append("\r\n"+transportInsurance.getTaskContent2());
			n=n+1;
		}
		if(transportInsurance.getTaskContent3()!=null&&!"".equalsIgnoreCase(transportInsurance.getTaskContent3())){
			sb.append("\r\n"+transportInsurance.getTaskContent3());
			n=n+1;
		}
		if(transportInsurance.getTaskContent4()!=null&&!"".equalsIgnoreCase(transportInsurance.getTaskContent4())){
			sb.append("\r\n"+transportInsurance.getTaskContent4());
			n=n+1;
		}
		sb.append("\r\n货物重量："+new BigDecimal(transportInsurance.getWeight()).movePointLeft(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString()+"吨");
		freight.setFregihtItem(sb.toString());
		//*二级货物明细（编码）
		freight.setFreightDetail("SX00140087");
		//*货物类型（编码）
		freight.setFreightType("SX001416");
		//*起运地
		freight.setFromArea(transportInsurance.getStartPoint());
		//*起运地国家（编码）
		freight.setFromCountry("HTC01");
		//*加成
		freight.setInvoiceBonus("1");
		// *发票金额"2000000.00"
		freight.setInvoiceMoney(new BigDecimal(transportInsurance.getAmtCurrency()).movePointRight(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
		//发票号
		freight.setInvoiceNumber("");
		//特别约定
		freight.setMark("");
	/*	freight.setMark(""+			
"1，保险责任自起运时开始生效，责任终止时间以条款说明为准；投保务必在起运前完成，否则本保险无效，保险人不承担赔偿责任，已收取的保险费退还投保人。\r\n"+					
"2，出险后如发现实际为超限货物，免赔适用2000RMB或损失金额的25%，两者以高者为准；【是否超限运输以“超限运输车辆行驶公路管理规定（中华人民共和国交通运输部令2016年第62号）”为衡量标准】。\r\n"+				
"3，承运车辆必须遵守道路运输相关法律法规，违反法律法规的情况下发生任何货物损失，保险人不负责赔偿。\r\n"+
"4，承运车辆通过道路/桥梁/隧道/立交桥/限宽门（桩）等各类道路设施时，需符合设施规定的通行限制，否则造成任何损失不予赔付。\r\n"+
"5，承运车辆及驾驶人员必须具备合格驾驶证、行驶证及营运证/许可证，否则本保险无效，保险人不负赔偿责任，已收取的保险费退还投保人。\r\n"+
"6，运输全程需开启车辆定位系统；\r\n"+
"7，货物起运前，应在货物可能的所有移动方向上进行强度足够的绑扎固定；\r\n"+					
"8，列明需单独申报的货物，不能直接适用以上条件自动承保，需逐笔申报人工审核；申报时需提供尺寸、吨位、运输工具及绑扎情况、路线路况等信息；该类货物未经保险人认可而自动生成保单，该保险无效，保险人不负赔偿责任，已收取的保险费退还投保人。【需单独申报：货值500万RMB或以上/100吨以上的设备及机械车/大型变压器/风电设备（包括并不限于机身、叶片）】\r\n"+
"9，当投保金额低于货物实际价值的90%时，保险人按照投保比例（投保金额/货物实际价值）承担赔偿责任。投保金额与货物实际价值差异在10%以内的，不计算不足额比例。");*/
		//*正文份数
		freight.setOriginalSum("3");
		//*包装及数量
		freight.setPackAndQuantity(n+"台");
		//途径港 "山东济南 河南安阳  江苏南京"
		sb =new StringBuffer();
		if(transportInsurance.getTransferStation1()!=null&&!"".equals(transportInsurance.getTransferStation1())){
			sb.append(transportInsurance.getTransferStation1());
		}
		if(transportInsurance.getTransferStation2()!=null&&!"".equals(transportInsurance.getTransferStation2())){
			sb.append("\\"+transportInsurance.getTransferStation2());
		}
		if(transportInsurance.getTransferStation3()!=null&&!"".equals(transportInsurance.getTransferStation3())){
			sb.append("\\"+transportInsurance.getTransferStation3());
		}
		if(transportInsurance.getTransferStation4()!=null&&!"".equals(transportInsurance.getTransferStation4())){
			sb.append("\\"+transportInsurance.getTransferStation4());
		}
		
		freight.setPassPort(sb.toString());
		//*保险费 500.00
		freight.setPremium(new BigDecimal(transportInsurance.getPremiumCurrency()).movePointLeft(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
		//*保险费币种 （编码）默认01 RMB(01:人民币;02:港币;03:美元;04-英镑;12-欧元)
		freight.setPremiumCurrency("01");
		//* 保费打印
		freight.setPremiumPrit("01");
		//*货物标记
		freight.setSign(".");
		//*起运时间"2018-01-24T12:24:37"
		freight.setStartTM(TimeUtil.formatHTBXDateTime(transportInsurance.getStartTime()));
		//查勘地址内容
		freight.setSurveyAdr("华泰财产保险有限公司北京分公司 \r\n 5 floor De Sheng Shang Cheng (North Tower), 25 De Sheng Men Wai Street, Xicheng District, Beijing \r\nTEL:4006095509 \r\nFAX:(010)59375924");
		//*查勘地址地址编码
		freight.setSurveyAdrID("501422495584");
		//*目的地
		freight.setToArea(transportInsurance.getDestPoint());
		//*目的地国家 （编码）
		freight.setToContry("HTC01");
		//*船名航班车号
		//freight.setTrafficNumber(transportInsurance.getHeadNo()+" "+transportInsurance.getTailNo());
		freight.setTrafficNumber(transportInsurance.getHeadNo());
		//挂车车牌号
		freight.setTrailerNum(transportInsurance.getTailNo());
		//*运输方式明细（编码）
		freight.setTransportDetail("01");
		//*运输方式（编码）
		freight.setTransportType("SX001502");
		//转运工具
		freight.setTrantsTool("汽车");
		policy.setFreight(freight);
		
		//险别信息
		Policy.InsureRdrs insureRdrs=new Policy.InsureRdrs();
		//险种清单
		List<Policy.InsureRdrs.InsureRdr>  insureRdrsList=insureRdrs.getInsureRdr();//new ArrayList<Policy.InsureRdrs.InsureRdr>();
		//险种
		Policy.InsureRdrs.InsureRdr insureRdr=new Policy.InsureRdrs.InsureRdr();
		
		if(transportInsurance.getCategory().intValue()==1){
			//*责任险种编码 参数：险种清单合同结构码
			insureRdr.setRdrCde("SX300211");
			//*责任险种描述
			insureRdr.setRdrDesc("按照华泰财产保险有限公司《国内水路、陆路运输货物保险条款》规定，承保基本险。");
			//*险种名称 
			insureRdr.setRdrName("基本险");
		}else{
			//*责任险种编码 参数：险种清单合同结构码
			insureRdr.setRdrCde("SX300212");
			//*责任险种描述
			insureRdr.setRdrDesc("按照华泰财产保险有限公司《国内水路、陆路运输货物保险条款》规定，承保综合险。");
			//*险种名称 
			insureRdr.setRdrName("综合险");
		}
		insureRdrsList.add(insureRdr);
		policy.setInsureRdrs(insureRdrs);
		
		//投保人信息
		Policy.Applicant applicant=new Policy.Applicant();
		//*开户银行
		applicant.setAccountBank("");
		//*银行账号
		applicant.setAccountNumber("");
		//地址
		applicant.setAddress("");
		//投保人编码,固定投保人填写华泰提供的编码，投保人不固定则为空
		applicant.setAppCode("");
		//*投保人名称
		applicant.setApplicantName(transportInsurance.getApplicantName());
		//*出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填-
		if(transportInsurance.getApplicantType().intValue()==1){
			//取得生日
			applicant.setBirthday(IdCardUtil.getBirthday(transportInsurance.getApplicantId()));
			//*证件类型 注意：机构客户，必选填写 97（税务登记证）
			applicant.setIDType("01");
			//性别，个人客户必填, 1：男；2：女；9：未说明
			if("1".equals(IdCardUtil.getGender(transportInsurance.getApplicantId()))){
				applicant.setGender("1");
			}else{
				applicant.setGender("2");
			}
		}else
		{
			//*证件类型 注意：机构客户，必选填写 97（税务登记证）
			applicant.setIDType("97");
			applicant.setBirthday("");
			//性别，个人客户必填, 1：男；2：女；9：未说明
			applicant.setGender("9");
		}
		
		//联系手机
		applicant.setCell(transportInsurance.getApplicantPhone());
		//email
		applicant.setEmail("");
		//*证件号码
		applicant.setID(transportInsurance.getApplicantId());
		//固定电话
		applicant.setPhone("");
		//*是否需要增值税专用发票 0：否 1：是
		applicant.setTaxDeduct("0");
		//邮政编码
		applicant.setZip("");
		policy.setApplicant(applicant);
		
		//被保险人信息
		Policy.Insured insured=new Policy.Insured();
		//地址
		insured.setAddress("");
		//出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填
		if(transportInsurance.getInsuredType().intValue()==1){
			//*证件类型
			insured.setIDType("01");
			//性别，个人客户必填, 1：男；2：女；9：未说明
			if("1".equals(IdCardUtil.getGender(transportInsurance.getInsuredId()))){
				insured.setGender("1");
			}else{
				insured.setGender("2");
			}
			insured.setBirthday(IdCardUtil.getBirthday(transportInsurance.getInsuredId()));
		}else{
			//*证件类型
			insured.setIDType("97");
			insured.setBirthday("");
			insured.setGender("9");
		}
		
		
		//联系手机
		insured.setCell(transportInsurance.getInsuredPhone());
		//email
		insured.setEmail("");
		
		//*证件号码
		insured.setID(transportInsurance.getInsuredId());
		
		//*被保险人信息
		insured.setInsuredName(transportInsurance.getInsuredName());
		//固定电话
		insured.setPhone("");
		//邮政编码
		insured.setZip("");
		
		policy.setInsured(insured);
		
		//生成xml
	    String str= new JaxbUtil(Policy.class).toXml(policy, "UTF-8");
		str.replace(" standalone=\"yes\"", "");
		return str;
	}
	
	
	
	/**2.3出单确认
	 * 成功返回 投保号 与PDF路径，失败为null
	 */
	public  static IssuePolicyResult insuranceConfrim(PolicyIssueBean policyIssueBean){
		try{
			WebServiceServerImplService service=new WebServiceServerImplService(new URL(url));
			WebServiceServer htbxServer=service.getPort(WebServiceServer.class);
			String xml=getPolicyIssue(policyIssueBean);
			htbxpayLogger.info("htbx_issuePolicy_request_xml="+xml);
			String  resultStr=htbxServer.issuePolicy(xml, userName, MD5Util.GetMD5Code(userPwd), MD5Util.GetMD5Code(key+xml).toUpperCase());
			htbxpayLogger.info("htbx_issuePolicy_request_result_xml="+resultStr);
			Result result=new JaxbUtil(Result.class).fromXml(resultStr);
			if(result!=null){
				IssuePolicyResult ipr=new IssuePolicyResult();
				if("1".equals(result.getFlag())){
					ipr.setNumber(result.getInsurancePolicy());
					ipr.setPdfUrl(result.getPdfURL());
					return ipr;
				}else{
					htbxpayLogger.error("htbx_issuePolicy_error_result_getFlag="+result.getFlag());
					return null;
				}
			}else{
				htbxpayLogger.error("htbx_issuePolicy_error_result=null");
				return null;
			}
		}catch(Exception e){
			e.printStackTrace();
			htbxpayLogger.error("htbx_issuePolicy_error:",e);
			return null;
		}
	}
	
	public  static String getPolicyIssue(PolicyIssueBean policyIssueBean){
		PolicyIssue pi=new PolicyIssue();
		pi.setInsrpakNo(policyIssueBean.getInsrpakNo());
		pi.setInsurancePolicy(policyIssueBean.getInsurancePolicy());
		pi.setPaymentPrm(policyIssueBean.getPaymentPrm());
		pi.setPayMode(policyIssueBean.getPayMode());
		pi.setRequestTime(TimeUtil.formatHTBXDateTime(policyIssueBean.getRequestTime()));
		pi.setSerialNumber(policyIssueBean.getSerialNumber());
		pi.setTransExeTime(TimeUtil.formatHTBXDateTime(policyIssueBean.getTransExeTime()));
		//生成xml
	    String str= new JaxbUtil(PolicyIssue.class).toXml(pi, "UTF-8");
		str.replace(" standalone=\"yes\"", "");
		return str;
	}
	public static void main(String[] args)throws Exception {
		//测试投保申请
		HtbxUtil.testInsuranceApply();
		//HtbxUtil.testInsuranceConfrim();
		//double d=Math.pow(10d,8d);
		//d=d+1;
		//System.out.println(new BigDecimal(d).setScale(0, BigDecimal.ROUND_HALF_UP).toString().substring(1));
	}
	
	public static void testInsuranceConfrim() throws Exception {
		PolicyIssueBean policyIssueBean=new PolicyIssueBean();
		//支付宝/微信，支付流水号
		policyIssueBean.setInsrpakNo("88888888888");
		//投保单号
		policyIssueBean.setInsurancePolicy("T017136012018000008");
		//合计保费
		policyIssueBean.setPaymentPrm("500.00");
		//支付模式 22: 微信，23：支付宝
		policyIssueBean.setPayMode("23");
		///请求时间
		policyIssueBean.setRequestTime(new Date());
		//流水号
		policyIssueBean.setSerialNumber("1000582018012916160100000001");
		//银行交易时间 
		policyIssueBean.setTransExeTime(new Date());
		
		IssuePolicyResult ipr=HtbxUtil.insuranceConfrim( policyIssueBean);
		System.out.println(ipr.getNumber());
		System.out.println(ipr.getPdfUrl());
	}
	
	public static void testInsuranceApply() throws Exception {
		TransportInsurance transportInsurance =new TransportInsurance();
		//公共信息
		//流水号 特运通公司的
				transportInsurance.setId(10002l);;
				//*保险起期=起运时间 2018-02-25
		transportInsurance.setStartTime(TimeUtil.addDay(new Date(),2));
				//*出单时间 FORMAT YYYY-MM-DDTHH24:MI:SS
		transportInsurance.setCtime(new Date());
				
				//*签单时间"2018-01-24"
				transportInsurance.setCtime(new Date());
				//*保险止期"2018-02-25"
			transportInsurance.setEffectiveTime(TimeUtil.addDay(new Date(),30));
				
				
				//*保险金额 2000000.00
				transportInsurance.setAmtCurrency(20000);
				//* 费率‰-0.25
				transportInsurance.setPremium(25);
				//*预计抵达时间"2018-02-25T12:24:37"
				transportInsurance.setEffectiveTime(TimeUtil.addDay(new Date(),30));
				transportInsurance.setTaskContent1("20 挖机");
				transportInsurance.setTaskContent2("120 挖机");
				transportInsurance.setTaskContent3("300 钩机");
				transportInsurance.setTaskContent4("210 挖机");
				transportInsurance.setWeight(20000);
				//*起运地
				transportInsurance.setStartPoint("北京市");
				// *发票金额"2000000.00"
				transportInsurance.setAmtCurrency(20000);
				//途径港 "山东济南 河南安阳  江苏南京"
				transportInsurance.setTransferStation1("天津");
				transportInsurance.setTransferStation2("河南郑州");
				transportInsurance.setTransferStation3("山东济南");
				transportInsurance.setTransferStation4("江苏南京");
				
				//*保险费 500.00
			  transportInsurance.setPremiumCurrency(50000);
			  //起运时间
				transportInsurance.setStartTime(new Date());
				//*目的地
				transportInsurance.setDestPoint("上海");
				//*船名航班车号
				transportInsurance.setHeadNo("京A8521");
				transportInsurance.setTailNo("京挂9635");
				
				
				transportInsurance.setCategory(1);
				//*投保人名称
				transportInsurance.setApplicantName("张天才");
				//*出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填-
				transportInsurance.setApplicantType(1);
				transportInsurance.setApplicantId("522623199009282633");				
				//联系手机
				transportInsurance.setApplicantPhone("18881882178");
				
				transportInsurance.setInsuredType(1);
				//联系手机
				transportInsurance.setInsuredPhone("189189921278");
				//*证件号码
				transportInsurance.setInsuredId("441427199301115406");
				//*被保险人信息
				transportInsurance.setInsuredName("张发才");
			
				transportInsurance.setOrderNo("1000582018012916160100000020");
		String aa= HtbxUtil.insuranceApply(transportInsurance);
		System.out.println(aa);
	}
}
