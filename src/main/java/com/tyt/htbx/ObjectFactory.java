//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.19 at 01:39:47 PM CST 
//


package com.tyt.htbx;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.tyt.htbx package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _PolicyApplicantPhone_QNAME = new QName("", "Phone");
    private final static QName _PolicyApplicantCell_QNAME = new QName("", "Cell");
    private final static QName _PolicyApplicantAccountBank_QNAME = new QName("", "AccountBank");
    private final static QName _PolicyApplicantTaxDeduct_QNAME = new QName("", "TaxDeduct");
    private final static QName _PolicyApplicantAccountNumber_QNAME = new QName("", "AccountNumber");
    private final static QName _PolicyApplicantAppCode_QNAME = new QName("", "AppCode");
    private final static QName _PolicyApplicantBirthday_QNAME = new QName("", "Birthday");
    private final static QName _PolicyApplicantEmail_QNAME = new QName("", "Email");
    private final static QName _PolicyApplicantGender_QNAME = new QName("", "Gender");
    private final static QName _PolicyApplicantIDType_QNAME = new QName("", "IDType");
    private final static QName _PolicyApplicantAddress_QNAME = new QName("", "Address");
    private final static QName _PolicyApplicantID_QNAME = new QName("", "ID");
    private final static QName _PolicyApplicantZip_QNAME = new QName("", "Zip");
    private final static QName _PolicyApplicantApplicantName_QNAME = new QName("", "ApplicantName");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.tyt.htbx
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Policy }
     * 
     */
    public Policy createPolicy() {
        return new Policy();
    }

    /**
     * Create an instance of {@link Policy.InsureRdrs }
     * 
     */
    public Policy.InsureRdrs createPolicyInsureRdrs() {
        return new Policy.InsureRdrs();
    }

    /**
     * Create an instance of {@link Policy.General }
     * 
     */
    public Policy.General createPolicyGeneral() {
        return new Policy.General();
    }

    /**
     * Create an instance of {@link Policy.Freight }
     * 
     */
    public Policy.Freight createPolicyFreight() {
        return new Policy.Freight();
    }

    /**
     * Create an instance of {@link Policy.Applicant }
     * 
     */
    public Policy.Applicant createPolicyApplicant() {
        return new Policy.Applicant();
    }

    /**
     * Create an instance of {@link Policy.Insured }
     * 
     */
    public Policy.Insured createPolicyInsured() {
        return new Policy.Insured();
    }

    /**
     * Create an instance of {@link Policy.InsureRdrs.InsureRdr }
     * 
     */
    public Policy.InsureRdrs.InsureRdr createPolicyInsureRdrsInsureRdr() {
        return new Policy.InsureRdrs.InsureRdr();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "Phone", scope = Policy.Applicant.class)
    public JAXBElement<Integer> createPolicyApplicantPhone(Integer value) {
        return new JAXBElement<Integer>(_PolicyApplicantPhone_QNAME, Integer.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Long }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "Cell", scope = Policy.Applicant.class)
    public JAXBElement<Long> createPolicyApplicantCell(Long value) {
        return new JAXBElement<Long>(_PolicyApplicantCell_QNAME, Long.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "AccountBank", scope = Policy.Applicant.class)
    public JAXBElement<String> createPolicyApplicantAccountBank(String value) {
        return new JAXBElement<String>(_PolicyApplicantAccountBank_QNAME, String.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Byte }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "TaxDeduct", scope = Policy.Applicant.class)
    public JAXBElement<Byte> createPolicyApplicantTaxDeduct(Byte value) {
        return new JAXBElement<Byte>(_PolicyApplicantTaxDeduct_QNAME, Byte.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "AccountNumber", scope = Policy.Applicant.class)
    public JAXBElement<String> createPolicyApplicantAccountNumber(String value) {
        return new JAXBElement<String>(_PolicyApplicantAccountNumber_QNAME, String.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "AppCode", scope = Policy.Applicant.class)
    public JAXBElement<String> createPolicyApplicantAppCode(String value) {
        return new JAXBElement<String>(_PolicyApplicantAppCode_QNAME, String.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "Birthday", scope = Policy.Applicant.class)
    public JAXBElement<XMLGregorianCalendar> createPolicyApplicantBirthday(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_PolicyApplicantBirthday_QNAME, XMLGregorianCalendar.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "Email", scope = Policy.Applicant.class)
    public JAXBElement<String> createPolicyApplicantEmail(String value) {
        return new JAXBElement<String>(_PolicyApplicantEmail_QNAME, String.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Byte }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "Gender", scope = Policy.Applicant.class)
    public JAXBElement<Byte> createPolicyApplicantGender(Byte value) {
        return new JAXBElement<Byte>(_PolicyApplicantGender_QNAME, Byte.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Byte }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "IDType", scope = Policy.Applicant.class)
    public JAXBElement<Byte> createPolicyApplicantIDType(Byte value) {
        return new JAXBElement<Byte>(_PolicyApplicantIDType_QNAME, Byte.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "Address", scope = Policy.Applicant.class)
    public JAXBElement<String> createPolicyApplicantAddress(String value) {
        return new JAXBElement<String>(_PolicyApplicantAddress_QNAME, String.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Long }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "ID", scope = Policy.Applicant.class)
    public JAXBElement<Long> createPolicyApplicantID(Long value) {
        return new JAXBElement<Long>(_PolicyApplicantID_QNAME, Long.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "Zip", scope = Policy.Applicant.class)
    public JAXBElement<Integer> createPolicyApplicantZip(Integer value) {
        return new JAXBElement<Integer>(_PolicyApplicantZip_QNAME, Integer.class, Policy.Applicant.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "ApplicantName", scope = Policy.Applicant.class)
    public JAXBElement<String> createPolicyApplicantApplicantName(String value) {
        return new JAXBElement<String>(_PolicyApplicantApplicantName_QNAME, String.class, Policy.Applicant.class, value);
    }

}
