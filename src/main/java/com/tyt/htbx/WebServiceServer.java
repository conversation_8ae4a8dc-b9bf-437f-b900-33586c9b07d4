
package com.tyt.htbx;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.2
 * 
 */
@WebService(name = "WebServiceServer", targetNamespace = "http://webservice.module.ssh.com/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface WebServiceServer {


    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "IMPPolicy")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "IMPPolicy", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.IMPPolicy")
    @ResponseWrapper(localName = "IMPPolicyResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.IMPPolicyResponse")
    public String impPolicy(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "EndPolicy")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "EndPolicy", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.EndPolicy")
    @ResponseWrapper(localName = "EndPolicyResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.EndPolicyResponse")
    public String endPolicy(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryPolicy")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "QueryPolicy", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.QueryPolicy")
    @ResponseWrapper(localName = "QueryPolicyResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.QueryPolicyResponse")
    public String queryPolicy(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "EDRIssuePolicy")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "EDRIssuePolicy", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.EDRIssuePolicy")
    @ResponseWrapper(localName = "EDRIssuePolicyResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.EDRIssuePolicyResponse")
    public String edrIssuePolicy(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "TankLinkPolicy")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "TankLinkPolicy", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.TankLinkPolicy")
    @ResponseWrapper(localName = "TankLinkPolicyResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.TankLinkPolicyResponse")
    public String tankLinkPolicy(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "IssuePolicy")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "IssuePolicy", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.IssuePolicy")
    @ResponseWrapper(localName = "IssuePolicyResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.IssuePolicyResponse")
    public String issuePolicy(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "EDRPolicy")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "EDRPolicy", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.EDRPolicy")
    @ResponseWrapper(localName = "EDRPolicyResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.EDRPolicyResponse")
    public String edrPolicy(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "APPPolicy")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "APPPolicy", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.APPPolicy")
    @ResponseWrapper(localName = "APPPolicyResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.APPPolicyResponse")
    public String appPolicy(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

    /**
     * 
     * @param sign
     * @param pwd
     * @param plyContent
     * @param usr
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryPolicyManual")
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "QueryPolicyManual", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.QueryPolicyManual")
    @ResponseWrapper(localName = "QueryPolicyManualResponse", targetNamespace = "http://webservice.module.ssh.com/", className = "com.tyt.htbx.QueryPolicyManualResponse")
    public String queryPolicyManual(
        @WebParam(name = "PlyContent", targetNamespace = "")
        String plyContent,
        @WebParam(name = "Usr", targetNamespace = "")
        String usr,
        @WebParam(name = "Pwd", targetNamespace = "")
        String pwd,
        @WebParam(name = "Sign", targetNamespace = "")
        String sign);

}
