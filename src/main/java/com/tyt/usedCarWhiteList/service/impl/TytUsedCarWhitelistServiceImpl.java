package com.tyt.usedCarWhiteList.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUsedCarBrand;
import com.tyt.model.TytUsedCarWhitelist;
import com.tyt.usedCarBrand.service.TytUsedCarBrandService;
import com.tyt.usedCarWhiteList.service.TytUsedCarWhitelistService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description  二手车发布白名单服务层实现类
 * <AUTHOR>
 * @Date  2020/1/21 11:59
 * @Param
 * @return
 **/
@Service("tytUsedCarWhitelistService")
public class TytUsedCarWhitelistServiceImpl extends BaseServiceImpl<TytUsedCarWhitelist,Long> implements TytUsedCarWhitelistService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    @Resource(name = "tytUsedCarWhitelistDao")
    public void setBaseDao(BaseDao<TytUsedCarWhitelist, Long> tytUsedCarWhitelistDao) {
        super.setBaseDao(tytUsedCarWhitelistDao);
    }


}
