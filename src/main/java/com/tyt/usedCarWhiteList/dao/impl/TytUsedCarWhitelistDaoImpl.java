package com.tyt.usedCarWhiteList.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUsedCarWhitelist;
import com.tyt.usedCarWhiteList.dao.TytUsedCarWhitelistDao;
import org.springframework.stereotype.Repository;

/**
 * @Description  二手车发布白名单数据层实现类
 * <AUTHOR>
 * @Date  2020/1/21 11:55
 * @Param
 * @return
 **/
@Repository("tytUsedCarWhitelistDao")
public class TytUsedCarWhitelistDaoImpl extends BaseDaoImpl<TytUsedCarWhitelist,Long> implements TytUsedCarWhitelistDao {
    public TytUsedCarWhitelistDaoImpl() {
        this.setEntityClass(TytUsedCarWhitelist.class);
    }
}