package com.tyt.luckdraw.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.luckdraw.dao.LuckdrawActivityInfoDao;
import com.tyt.model.LuckdrawActivityInfo;
import org.springframework.stereotype.Repository;

@Repository("luckdrawActivityInfoDao")
public class LuckdrawActivityInfoDaoImpl extends BaseDaoImpl <LuckdrawActivityInfo,Integer> implements LuckdrawActivityInfoDao {
    public LuckdrawActivityInfoDaoImpl() {
        this.setEntityClass(LuckdrawActivityInfo.class);
    }
}