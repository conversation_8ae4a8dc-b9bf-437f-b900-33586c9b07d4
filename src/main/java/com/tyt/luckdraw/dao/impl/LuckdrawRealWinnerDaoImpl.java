package com.tyt.luckdraw.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.luckdraw.dao.LuckdrawRealWinnerDao;
import com.tyt.model.LuckdrawRealWinner;
import org.springframework.stereotype.Repository;

@Repository("luckdrawRealWinnerDao")
public class LuckdrawRealWinnerDaoImpl extends BaseDaoImpl <LuckdrawRealWinner,Long> implements LuckdrawRealWinnerDao {
    public LuckdrawRealWinnerDaoImpl() {
        this.setEntityClass(LuckdrawRealWinner.class);
    }
}