package com.tyt.luckdraw.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.luckdraw.dao.LuckdrawJoininRecordDao;
import com.tyt.model.LuckdrawJoininRecord;
import org.springframework.stereotype.Repository;

@Repository("luckdrawJoininRecordDao")
public class LuckdrawJoininRecordDaoImpl extends BaseDaoImpl <LuckdrawJoininRecord,Long> implements LuckdrawJoininRecordDao {
    public LuckdrawJoininRecordDaoImpl() {
        this.setEntityClass(LuckdrawJoininRecord.class);
    }
}