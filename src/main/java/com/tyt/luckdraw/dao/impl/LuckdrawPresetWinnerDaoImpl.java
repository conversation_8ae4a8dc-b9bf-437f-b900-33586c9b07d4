package com.tyt.luckdraw.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.luckdraw.dao.LuckdrawPresetWinnerDao;
import com.tyt.model.LuckdrawPresetWinner;
import org.springframework.stereotype.Repository;

@Repository("luckdrawPresetWinnerDao")
public class LuckdrawPresetWinnerDaoImpl extends BaseDaoImpl <LuckdrawPresetWinner,Long> implements LuckdrawPresetWinnerDao {
    public LuckdrawPresetWinnerDaoImpl() {
        this.setEntityClass(LuckdrawPresetWinner.class);
    }
}