package com.tyt.luckdraw.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.LuckdrawActivityInfo;

public interface LuckdrawActivityInfoService extends BaseService <LuckdrawActivityInfo,Integer> {

       /**
        * @Description  根据活动ID查询活动信息的相关方法
        * <AUTHOR>
        * @Date  2020/3/13 11:32
        * @Param [activityId, status]
        * @return com.tyt.model.LuckdrawActivityInfo
        **/
       LuckdrawActivityInfo getLuckdrawActivityInfo(Integer activityId);
}
