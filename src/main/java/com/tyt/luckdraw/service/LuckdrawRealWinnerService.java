package com.tyt.luckdraw.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.LuckdrawRealWinner;

import java.util.List;

public interface LuckdrawRealWinnerService extends BaseService <LuckdrawRealWinner,Long> {

    /**
     * @Description  查询中奖用户名单列表
     * <AUTHOR>
     * @Date  2020/3/12 10:13
     * @Param [activityId]
     * @return java.util.List<com.tyt.model.LuckdrawRealWinner>
     **/
    List<LuckdrawRealWinner> getWinnerList(Integer activityId);
}
