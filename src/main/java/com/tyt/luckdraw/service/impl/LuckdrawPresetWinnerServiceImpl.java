package com.tyt.luckdraw.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.luckdraw.service.LuckdrawPresetWinnerService;
import com.tyt.model.LuckdrawPresetWinner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("luckdrawPresetWinnerService")
public class LuckdrawPresetWinnerServiceImpl extends BaseServiceImpl <LuckdrawPresetWinner,Long> implements LuckdrawPresetWinnerService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());


    @Resource(name = "luckdrawPresetWinnerDao")
    public void setBaseDao(BaseDao <LuckdrawPresetWinner, Long> luckdrawPresetWinnerDao) {
        super.setBaseDao(luckdrawPresetWinnerDao);
    }

}
