package com.tyt.luckdraw.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.luckdraw.service.LuckdrawJoininRecordService;
import com.tyt.model.LuckdrawJoininRecord;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("luckdrawJoininRecordService")
public class LuckdrawJoininRecordServiceImpl extends BaseServiceImpl <LuckdrawJoininRecord,Long> implements LuckdrawJoininRecordService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());


    @Resource(name = "luckdrawJoininRecordDao")
    public void setBaseDao(BaseDao <LuckdrawJoininRecord, Long> luckdrawJoininRecordDao) {
        super.setBaseDao(luckdrawJoininRecordDao);
    }

    /**
     * @Description  查询用户已参与抽奖次数
     * <AUTHOR>
     * @Date  2020/3/9 18:11 
     * @Param [activityId, userId, limitTimesType]
     * @return java.lang.Integer
     **/
    @Override
    public Integer queryUserJoininTimes(Integer activityId, Long userId, Integer limitTimesType) {

        List <Object> list = new ArrayList <Object>();
        StringBuffer countSql = new StringBuffer("select count(*) from luckdraw_joinin_record  r ");
        StringBuffer sb = new StringBuffer(" where r.activity_id = ?  and  r.user_id = ? ");
        list.add(activityId);
        list.add(userId);
        if(limitTimesType == 1){ //总抽奖次数

        }else if(limitTimesType == 2){ // 每天抽奖次数
            Date todayDate = new Date(); //今天日期
            String beginTime = TimeUtil.formatDate(todayDate) + " 00:00:00"; //开始时间
            String endTime = TimeUtil.formatDate(todayDate) + " 23:59:59"; //结束时间
            sb.append(" and r.ctime >= ? and r.ctime <= ? ");
            list.add(beginTime);
            list.add(endTime);
        }
        countSql.append(sb);
        //查询用户已参与抽奖次数
        BigInteger count = this.getBaseDao().query(countSql.toString(),list.toArray());
        return count.intValue();
    }
}
