package com.tyt.luckdraw.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.luckdraw.service.LuckdrawRealWinnerService;
import com.tyt.model.LuckdrawRealWinner;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("luckdrawRealWinnerService")
public class LuckdrawRealWinnerServiceImpl extends BaseServiceImpl <LuckdrawRealWinner,Long> implements LuckdrawRealWinnerService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());


    @Resource(name = "luckdrawRealWinnerDao")
    public void setBaseDao(BaseDao <LuckdrawRealWinner, <PERSON>> luckdrawRealWinnerDao) {
        super.setBaseDao(luckdrawRealWinnerDao);
    }

    /**
     * @Description  查询中奖用户名单列表
     * <AUTHOR>
     * @Date  2020/3/12 10:24
     * @Param [activityId]
     * @return java.util.List<com.tyt.model.LuckdrawRealWinner>
     **/
    @Override
    public List <LuckdrawRealWinner> getWinnerList(Integer activityId) {
        String sql = "select id id," +
                     "activity_id activityId," +
                     "activity_name activityName," +
                     "prize_id prizeId," +
                     "prize_type prizeType," +
                     "prize_name prizeName," +
                     "prize_amount prizeAmount," +
                     "user_name userName," +
                     "ctime ctime " +
                     " from luckdraw_real_winner " +
                     " where activity_id = ? " +
                     " order by id desc limit 100";

        Map <String, Type> scalarMap = new HashMap <String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("activityId", Hibernate.INTEGER);
        scalarMap.put("activityName", Hibernate.STRING);
        scalarMap.put("prizeId", Hibernate.INTEGER);
        scalarMap.put("prizeType", Hibernate.INTEGER);
        scalarMap.put("prizeName", Hibernate.STRING);
        scalarMap.put("prizeAmount", Hibernate.BIG_DECIMAL);
        scalarMap.put("userName", Hibernate.STRING);
        scalarMap.put("ctime", Hibernate.TIMESTAMP);

        //查询中奖用户名单列表
        List<LuckdrawRealWinner> winnerList = this.getBaseDao().search(sql, scalarMap, LuckdrawRealWinner.class, new Object[]{activityId});
        return winnerList;
    }
}
