package com.tyt.luckdraw.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.luckdraw.bean.WiningInfoPushMsg;
import com.tyt.luckdraw.service.LuckdrawService;
import com.tyt.model.LuckdrawRealWinner;
import com.tyt.util.SerialNumUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service("luckdrawService")
public class LuckdrawServiceImpl implements LuckdrawService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TytMqMessageService tytMqMessageService;
    /**
     * @Description  发送用户中奖信息的mq消息
     * <AUTHOR>
     * @Date  2020/3/11 16:29
     * @Param [realWinner]
     * @return void
     **/
    @Override
    public void sendWinningInfoPushMq(LuckdrawRealWinner realWinner) {
        //中奖信息的pushMQ消息对象
        WiningInfoPushMsg mqMsg = new WiningInfoPushMsg();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(MqBaseMessageBean.SEND_WINNING_INFO_MESSAGE);

        mqMsg.setPrizeId(realWinner.getPrizeId());
        mqMsg.setPrizeType(realWinner.getPrizeType());
        mqMsg.setPrizeName(realWinner.getPrizeName());
        mqMsg.setPrizeAmount(realWinner.getPrizeAmount());
        mqMsg.setUserId(realWinner.getUserId());
        // 保存发送mq
        final String messageSerailNum =mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        final int messageType = mqMsg.getMessageType();

        logger.info("====发送用户中奖信息的mq消息内容为:===="+mqJson);
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                //发送并mq信息并保存到数据库
                tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, messageType);
                tytMqMessageService.sendMqMessage(messageSerailNum, mqJson, messageType);
            }
        });
        // 关闭线程
        executorService.shutdown();
    }
}
