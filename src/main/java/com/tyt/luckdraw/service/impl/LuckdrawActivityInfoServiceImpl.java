package com.tyt.luckdraw.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.luckdraw.service.LuckdrawActivityInfoService;
import com.tyt.model.LuckdrawActivityInfo;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("luckdrawActivityInfoService")
public class LuckdrawActivityInfoServiceImpl extends BaseServiceImpl <LuckdrawActivityInfo,Integer> implements LuckdrawActivityInfoService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());


    @Resource(name = "luckdrawActivityInfoDao")
    public void setBaseDao(BaseDao <LuckdrawActivityInfo, Integer> luckdrawActivityInfoDao) {
        super.setBaseDao(luckdrawActivityInfoDao);
    }
    
    /**
     * @Description  根据活动ID查询活动信息的相关方法
     * <AUTHOR>
     * @Date  2020/3/13 11:34 
     * @Param [activityId, status]
     * @return com.tyt.model.LuckdrawActivityInfo
     **/
    @Override
    public LuckdrawActivityInfo getLuckdrawActivityInfo(Integer activityId) {

        LuckdrawActivityInfo luckdrawActivityInfo = new LuckdrawActivityInfo();
        String sql = "select id id," +
                     "activity_name activityName," +
                     "start_time startTime," +
                     "end_time endTime," +
                     "status status," +
                     "limit_times_type limitTimesType," +
                     "limit_times limitTimes " +
                     " from luckdraw_activity_info " +
                     " where 1=1 and id = ? ";

        Map <String, Type> scalarMap = new HashMap <String, Type>();
        scalarMap.put("id", Hibernate.INTEGER);
        scalarMap.put("activityName", Hibernate.STRING);
        scalarMap.put("startTime", Hibernate.TIMESTAMP);
        scalarMap.put("endTime", Hibernate.TIMESTAMP);
        scalarMap.put("status", Hibernate.INTEGER);
        scalarMap.put("limitTimes", Hibernate.INTEGER);
        scalarMap.put("limitTimesType", Hibernate.INTEGER);

        List <LuckdrawActivityInfo> activityInfoList = this.getBaseDao().search(sql, scalarMap, LuckdrawActivityInfo.class, new Object[]{activityId});
        if(activityInfoList != null && activityInfoList.size() > 0){
            luckdrawActivityInfo = activityInfoList.get(0);
        }
        return luckdrawActivityInfo;
    }
}
