package com.tyt.luckdraw.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.LuckdrawJoininRecord;

public interface LuckdrawJoininRecordService extends BaseService <LuckdrawJoininRecord,Long> {

       /**
        * @Description  查询用户已参与抽奖次数
        * <AUTHOR>
        * @Date  2020/3/9 18:09
        * @Param [activityId, userId, limitTimesType]
        * @return java.lang.Integer
        **/
       public Integer queryUserJoininTimes(Integer activityId,Long userId,Integer limitTimesType);
}
