package com.tyt.luckdraw.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.luckdraw.service.*;
import com.tyt.model.*;
import com.tyt.promo.service.ICouponService;
import com.tyt.promo.service.PromoActivityService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description  抽奖活动接口
 * <AUTHOR>
 * @Date  2020/3/9 16:11
 * @Param
 * @return
 **/
@Controller
@RequestMapping("/luckdraw/")
public class LuckdrawController extends BaseController {

    @Autowired
    private LuckdrawActivityInfoService luckdrawActivityInfoService;
    @Autowired
    private LuckdrawJoininRecordService luckdrawJoininRecordService;
    @Autowired
    private LuckdrawPresetWinnerService luckdrawPresetWinnerService;
    @Autowired
    private LuckdrawRealWinnerService luckdrawRealWinnerService;
    @Autowired
    private LuckdrawService luckdrawService;
    @Autowired
    private UserService userService;
    @Autowired
    private ICouponService couponService;
    @Autowired
    private PromoActivityService promoActivityService;
    /**
     * @Description  1.查询用户剩余抽奖次数和中奖用户名单接口
     * <AUTHOR>
     * @Date  2020/3/9 16:27
     * @Param [activityId, userId]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"queryRemainTimesAndWinnerList","queryRemainTimesAndWinnerList.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean queryRemainTimesAndWinnerList(Integer activityId,Long userId) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //参数校验
            if(activityId == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("活动ID不能为空！");
                return rm;
            }
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID不能为空！");
                return rm;
            }
            User user = userService.getById(userId);
            if(user == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID对应的用户信息不存在！");
                return rm;
            }
            //1.根据活动ID查询活动相关信息
            LuckdrawActivityInfo luckdrawActivityInfo = luckdrawActivityInfoService.getLuckdrawActivityInfo(activityId);
            if(luckdrawActivityInfo == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("活动ID对应的活动信息不存在！");
                return rm;
            }
            //活动状态(1.有效 2.无效)
            Integer status = luckdrawActivityInfo.getStatus();
            if(status == 2){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("非常抱歉,活动已失效！");
                return rm;
            }
            //限制每人抽奖次数类型(1.总共 2.每天)
            Integer limitTimesType = luckdrawActivityInfo.getLimitTimesType();
            //限制每人抽奖次数
            Integer limitTimes = luckdrawActivityInfo.getLimitTimes();
            //2.查询用户已参与抽奖次数
            Integer joininTimes = luckdrawJoininRecordService.queryUserJoininTimes(activityId, userId, limitTimesType);

            //3.计算用户剩余抽奖次数
            //用户剩余抽奖次数 = 限制每人抽奖次数 - 用户已参与抽奖次数
            int remainTimes = limitTimes - joininTimes;
            if(remainTimes <= 0){
                remainTimes = 0;
            }
            //4.查询中奖用户名单
            List<LuckdrawRealWinner> winnerList = luckdrawRealWinnerService.getWinnerList(activityId);

            Map<String,Object> map = new HashMap<String,Object>();
            map.put("activityInfo",luckdrawActivityInfo); //抽奖活动信息
            map.put("remainTimes",remainTimes);  //用户剩余抽奖次数
            map.put("winnerList",winnerList);    //中奖用户名单
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("查询用户抽奖资格和中奖用户名单成功！");
            rm.setData(map);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("查询用户抽奖资格和中奖用户名单失败！", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @Description  2.用户点击抽奖并获取抽奖结果接口
     * <AUTHOR>
     * @Date  2020/3/10 11:18
     * @Param [activityId, userId]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"clickDrawAndReturnResult","clickDrawAndReturnResult.action"},method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean clickDrawAndReturnResult(Integer activityId,Long userId) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //参数校验
            if(activityId == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("活动ID不能为空！");
                return rm;
            }
            if(userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID不能为空！");
                return rm;
            }
            User user = userService.getById(userId);
            if(user == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID对应的用户信息不存在！");
                return rm;
            }
            //1.根据活动ID查询活动相关信息
            LuckdrawActivityInfo luckdrawActivityInfo = luckdrawActivityInfoService.getLuckdrawActivityInfo(activityId);
            if(luckdrawActivityInfo == null){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("活动ID对应的活动信息不存在！");
                return rm;
            }
            //活动状态(1.有效 2.无效)
            Integer status = luckdrawActivityInfo.getStatus();
            if(status == 2){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("非常抱歉,活动已失效！");
                return rm;
            }
            //限制每人抽奖次数类型(1.总共 2.每天)
            Integer limitTimesType = luckdrawActivityInfo.getLimitTimesType();
            //限制每人抽奖次数
            Integer limitTimes = luckdrawActivityInfo.getLimitTimes();
            //2.查询用户已参与抽奖次数
            Integer joininTimes = luckdrawJoininRecordService.queryUserJoininTimes(activityId, userId, limitTimesType);

            //3.计算用户剩余抽奖次数
            //用户剩余抽奖次数 = 限制每人抽奖次数 - 用户已参与抽奖次数
            int remainTimes = limitTimes - joininTimes;
            if(remainTimes <= 0){ //抽奖次数已用完
                rm.setCode(ReturnCodeConstant.LUCKDRAW_NO_TIMES);
                rm.setMsg("非常抱歉，您的抽奖次数已用完！");
                return rm;
            }
            //4.记录用户参与抽奖记录
            //活动名称
            String activityName = luckdrawActivityInfo.getActivityName();
            //用户名
            String userName = user.getUserName();
            //手机号
            String cellPhone = user.getCellPhone();

            LuckdrawJoininRecord joininRecord = new LuckdrawJoininRecord();
            joininRecord.setActivityId(activityId);
            joininRecord.setActivityName(activityName);
            joininRecord.setUserId(userId);
            joininRecord.setUserName(userName);
            joininRecord.setCellphone(cellPhone);
            //参与抽奖日期
            joininRecord.setCtime(new Date());
            luckdrawJoininRecordService.addSave(joininRecord);
            //5.查询用户是否在预设中奖用户名单内
            LuckdrawPresetWinner presetWinner = new LuckdrawPresetWinner();
            presetWinner.setActivityId(activityId);
            presetWinner.setUserId(userId);
            presetWinner.setIsValid(1); //状态(1.有效 2.无效)
            presetWinner.setDrawStatus(1);//抽奖状态(1.未抽奖 2.已抽奖)
            //预设中奖用户名单记录
            List<LuckdrawPresetWinner> presetWinnerList = luckdrawPresetWinnerService.findList(presetWinner);

            //6.如果用户在预设中奖用户名单，记录真实获奖用户名单
            LuckdrawRealWinner realWinner = new LuckdrawRealWinner();
            if(presetWinnerList != null && presetWinnerList.size() > 0){
                //取第一条未抽奖的预设抽奖名单
                LuckdrawPresetWinner luckdrawPresetWinner = presetWinnerList.get(0);
                //预设中奖用户名单ID
                Long presetId = luckdrawPresetWinner.getId();
                //将预设名单的抽奖状态更新为 2.已抽奖
                luckdrawPresetWinner.setDrawStatus(2);//抽奖状态(1.未抽奖 2.已抽奖)
                luckdrawPresetWinnerService.update(luckdrawPresetWinner);

                //记录真实获奖用户名单
                realWinner.setPresetId(presetId); //预设中奖用户名单ID
                realWinner.setActivityId(activityId); //活动ID
                realWinner.setActivityName(activityName); //活动名称
                realWinner.setPrizeId(luckdrawPresetWinner.getPrizeId()); //奖品ID(优惠券活动ID)
                realWinner.setPrizeType(luckdrawPresetWinner.getPrizeType()); //奖品类型(1.会员券 2.广告券)
                realWinner.setPrizeName(luckdrawPresetWinner.getPrizeName()); //奖品名称
                realWinner.setPrizeAmount(luckdrawPresetWinner.getPrizeAmount()); //奖品金额(单位: 元)
                realWinner.setUserId(userId); //真实中奖用户ID
                realWinner.setUserName(userName); //真实中奖用户姓名
                realWinner.setCellphone(cellPhone); //真实中奖用户手机号
                realWinner.setCtime(new Date()); //中奖日期
                luckdrawRealWinnerService.addSave(realWinner);
            }else{//如果不在预设中奖用户名单,提示谢谢惠顾
                rm.setCode(ReturnCodeConstant.LUCKDRAW_NO_PRIZE);
                rm.setMsg("谢谢惠顾！很遗憾您未中奖");
                return rm;
            }
            //7.给用户发优惠券奖品并发送push消息通知
            //优惠券活动ID
            Integer promoActivityId = realWinner.getPrizeId();
            //查询优惠券活动
            PromoActivity promoActivity = promoActivityService.getById(promoActivityId);
            if(promoActivity != null){
                //优惠券活动类型
                Integer activityType = promoActivity.getActivityType();
                //给用户发优惠券奖品的mq消息
                couponService.giveShareCoupon(userId, activityType);
                //给用户发送中奖push推送的mq消息
                luckdrawService.sendWinningInfoPushMq(realWinner);
            }
            //返回抽奖结果信息
            Map<String,Object> map = new HashMap<String,Object>();
            map.put("prizeInfo",realWinner); //奖品信息
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("恭喜您中奖！");
            rm.setData(map);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("获取抽奖结果失败！", ex.getMessage());
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

}
