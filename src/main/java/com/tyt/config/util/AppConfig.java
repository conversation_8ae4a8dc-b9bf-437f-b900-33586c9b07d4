package com.tyt.config.util;

import java.io.*;

import java.util.Properties;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * 应用程序配置文件处理
 *
 * <AUTHOR>
 *
 */
public class AppConfig extends Thread
{
    static Log logger = LogFactory.getLog(AppConfig.class.getName());
    private static Properties appConfig = null;
    private static AppConfig instance = new AppConfig();

    public static AppConfig getInstance()
    {
        return instance;
    }

    private AppConfig()
    {
        appConfig = new Properties();
        loadConfig();
        //start();  //TODO 上线时需取消注释，启动线程
    }

    public void loadConfig()
    {
        logger.debug( "Reading config file.");
        synchronized ( appConfig )
        {
            InputStream conf = null;
            try
            {
                conf = getClass().getResourceAsStream( "/server.properties" );
                appConfig.load(conf);
            }
            catch (Exception ex2)
            {
                logger.fatal("Connot load WPS Server configuration file. [server.properties], see the following errors...");
                logger.fatal(ex2);
            }
            finally
            {
                try
                {
                    conf.close();
                }
                catch (Exception ex1)
                {
                    logger.fatal( ex1 );
                }
            }
        }
        logger.debug( "Reading config file done." );
    }

    public void run()
    {
        while ( ! appConfig.getProperty( "ServerStop" ).equals( "0" ))
        {
            try
            {
                sleep( 5000 );
            }
            catch (Exception ex1)
            {
                logger.fatal( ex1 );
            }
            loadConfig();
        }
        logger.debug( "AppConfig thread is exiting..." );
    }

    public static String getProperty(String propName)
    {
        synchronized ( appConfig )
        {
            if ( appConfig == null )
                return null;

            return appConfig.getProperty(propName);
        }
    }

    public static Integer getIntProperty(String propName)
    {
        synchronized ( appConfig )
        {
            if ( appConfig == null )
                return null;

            return new Integer(appConfig.getProperty(propName));
        }
    }

    public static String[][] getServerGroup(String propName)
    {
        synchronized ( appConfig )
        {
            if ( appConfig == null )
                return null;
            String tempResult = appConfig.getProperty(propName);
            String[] tempGroup = tempResult.split(",");
            String[][] serverGroup = new String[tempGroup.length][2];
            for ( int i = 0; i < tempGroup.length; i++ )
            {
                String[] tempServer = tempGroup[i].split(":");
                serverGroup[i] = tempServer;
            }
            return serverGroup;
        }
    }
}

//TODO 需要更好的方式处理数据同步


