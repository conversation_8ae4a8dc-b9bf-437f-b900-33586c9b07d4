package com.tyt.azt;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.esa2000.pdfsign.util.CommonUtil;
import com.tyt.config.util.AppConfig;
import com.tyt.service.common.common.HttpClientFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 一签通(安证通)证书多CA申请API
 * 附录，证件类型：
 * 0-居民身份证
 * 1-护照
 * 2-军人身份证
 * 3-工商登记证
 * 4-税务登记证
 * 5-股东代码证
 * 6-社会保障卡
 * 7-组织机构代码证
 * 8-企业营业执照
 * 9-法人代码证
 * A-武装警察身份证件
 * B-港澳居民往来内地通行证
 * C-台湾居民来往大陆通行证
 * E-户口簿
 * F-临时居民身份证
 * G-警察(警官)证
 * H-事业单位法人证书
 * J-社会团体登记证书
 * K-民办非企业登记证书
 * L-外国(地区)企业常驻代表机构登记证
 * M-政府批文
 * N-统一社会信用代码证
 * P-外国人永久居留证
 * Z-其他
 * Created by duanwc on 18/1/17.
 */
public class AloneCertMultiCaAPI {
    public static Logger logger = LoggerFactory.getLogger(AloneCertMultiCaAPI.class);

    private static CloseableHttpClient httpClient = HttpClientFactory.getHttpClientWithRetry();

    /**
     * 指定字符集
     */
    public static final String Charset_UTF_8 = "UTF-8";

    /**
     * 申请证书接口
     *
     * @param userName   姓名
     * @param idcard 身份证号
     * @return 返回结果
     */
    public static String applyCert(String userName, String idcard, String cellPhone) {
        // 生成json数据体
        String jsonString = getHttpJson(userName,idcard,cellPhone);
        System.out.println(jsonString);

        long start = System.currentTimeMillis();
        String result = getID(jsonString);
        long end = System.currentTimeMillis();
        System.out.println("耗时：" + (end - start));
        return result;
    }

    /**
     * 安证通申请证书ID
     * @param jsonString 请求数据体
     * @return certId
     */
    private static String getID(String jsonString) {
//        String url = "http://test.easysign.cn/APWebPF/ApplyCertOrServlet";
        String url = AppConfig.getProperty("azt.cert.applyUrl");

        String certId = null;
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;
        try {
            StringEntity body = new StringEntity(jsonString, Charset_UTF_8);
            body.setContentType("application/json");
            httpPost.setEntity(body);
            httpPost.setHeader("Content-Type", "application/json");
            response = httpClient.execute(httpPost);
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
            }
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            if (StringUtils.isBlank(result)) {
                logger.error("安证通多CA申请证书API服务请求未返回任何结果,请求json数据：{}", jsonString);
                return null;
            }
            /**
             * 响应样式:
             * 其中业务使用certId，其它数据无用。其中status含义：000 操作成功 001条件参数不能为空 002参数类型不正确 003服务器异常
             {
                 "msg":[
                     {
                         "status":"000",
                         "message":"5bey57uP5a2Y5Zyo5pyq6L+H5pyf55qE6K+B5Lmm77yM55u05o6l6L+U5Zue77yB",
                         "certId":"ff80808161094f2101610d64df3c0115",
                         "serialNumber":"343723811433",
                         "startTime":"2018-01-19 15:49:29",
                         "endTime":"2018-01-20 15:49:28",
                         "dn":"UTA0OU1EVXhRT2VKdWVXNXRPV2RoMEJh....AAA.....TkRFd01UazRO==",
                         "pfxdata":"MIIKWgIBAzCCChQGCSqGSIb3DQEHAaCCCgUEggoBMIIJ/....HVGVYC/HH.."
                     }
                 ]
             }
             */
            System.out.println(result);
            JSONObject sourceJson = JSONObject.parseObject(result.trim());
            JSONObject resJson = sourceJson.getJSONArray("msg").getJSONObject(0); //获取第一个数据
            String status = resJson.getString("status");
            String message = resJson.getString("message");
            if(StringUtils.equals(status,"000")) { // 成功返回certId
                certId = resJson.getString("certId");
                logger.info("安证通多CA申请证书API服务申请成功,certId:{}", certId);
                return certId;
            }
            logger.info("安证通多CA申请证书API服务申请失败,status:{},message:{},请求json数据：{}", status,CommonUtil.base64DecodeString(message, "utf-8"),jsonString);
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("安证通多CA申请证书API服务请求异常,请求json数据："+jsonString, e);
            return null;
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 生成JSON数据,中文需base64处理
     * @param userName
     * @param idCard
     * @param cellPhone
     * @return
     */
    private static String getHttpJson(String userName, String idCard, String cellPhone) {
        String aztUserName = AppConfig.getProperty("azt.auth.username");
        String aztPassword = AppConfig.getProperty("azt.auth.password");

        // 必选项，但里面也有非必选项，为什么？三方定义
        JSONObject required = new JSONObject(true);
        required.put("certType", "1"); //1个人  0企业
        required.put("certModel", "0");//0短期证书  1长期证书
        required.put("custName", CommonUtil.base64EncodeString(userName, "UTF-8")); //使用者
        required.put("identType", "0"); // 证件类型
        required.put("identNo", idCard); //证件号码
        required.put("validity", "1"); //申请期限 单位(天)
        required.put("keyAlg", "rsa"); //密钥算法 默认：rsa
        required.put("certPass", "123456"); //证书密码，自定义密码，但是安证通公司在代码里写死了"123456"
        required.put("projectID", ""); //项目ID（一签通用户名和密码为空，则不需要填写）

//        required.put("username", "xiaobai"); //用户名称(一签通项目ID为空，这里必须填写)
//        required.put("password", "e10adc3949ba59abbe56e057f20f883e"); //用户密码(一签通项目ID为空，这里必须填写)
        required.put("username", aztUserName); //用户名称(一签通项目ID为空，这里必须填写)
        required.put("password", aztPassword); //用户密码(一签通项目ID为空，这里必须填写)

        required.put("versions", "1.0"); //版本号 当前：1.0
        required.put("authenticate", "1"); // 认证方式（0 证书 1用户名密码 2令牌 3项目ID 目前只有一种方式默认填写 3）

        // 非必选项
        JSONObject optional = new JSONObject(true);
        optional.put("email", ""); //邮箱地址
        optional.put("telphone", cellPhone); //电话号码
        optional.put("address", ""); //详细地址
        optional.put("org", ""); //单位
        optional.put("unit", ""); //部门
        optional.put("country", "CN"); //国家 例如：CN
        optional.put("province", ""); //省份
        optional.put("city", "");//城市
        optional.put("postcode", "");//邮编

        // 仅仅为了适应格式，为什么这么定义？？问问一签通
        JSONArray requiredList = new JSONArray();
        requiredList.add(required);
        JSONArray optionalList = new JSONArray();
        optionalList.add(optional);

        String requiredStr = requiredList.toJSONString();
        String optionalStr = optionalList.toJSONString();

        /**
         * 不要问我为什么这么写，我不知道，安证通说：required 这个必须在前面，否则不能用
         */
        StringBuilder result = new StringBuilder();
        result.append("{");
        result.append("\"").append("required").append("\":");
        result.append(requiredStr);
        result.append(",");
        result.append("\"").append("optional").append("\":");
        result.append(optionalStr);
        result.append("}");
        return result.toString();
    }

    public static void main(String[] args) {
        String certId = applyCert("特年均","410198563002060015","13788886666");
        System.out.println(certId);
    }
}
