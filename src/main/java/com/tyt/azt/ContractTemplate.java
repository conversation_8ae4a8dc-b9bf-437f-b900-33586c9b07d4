package com.tyt.azt;

import com.esa2000.doc.main.BuildEngine;
import com.esa2000.doc.util.CommonUtil;
import com.esa2000.doc.util.FileUtil;
import com.tyt.config.util.AppConfig;
import com.tyt.model.EcaContract;
import com.tyt.util.TimeUtil;
import com.tyt.util.XmlUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by duanwc on 18/1/17.
 */
public class ContractTemplate implements WordTemplate {

    public static Logger logger = LoggerFactory.getLogger(ContractTemplate.class);

    /**
     * 通过模板创建PDF
     * @param contract 合同对象
     * @param pdfPath 生成文件的路径，用于处理是否成功创建文件
     * @return 成功返回pdf路径，失败返回null
     */
    private static String createContractPDF(EcaContract contract, String pdfPath) {
        BuildEngine buildEngine = new BuildEngine();
        String classPath = PDFSignPrivateSeal.class.getResource("/").getPath();
        //xml模板
        String templatePath = classPath + "/contractTemplate.xml";
        byte[] templateByte = FileUtil.readBytesFromFile(templatePath);
        String xmlString = contract2Xml(contract);
        //业务数据xml
        Document doc = parseXMLDocument(xmlString);
        try {
            //word数据填充，word转PDF
            //返回PDF字节数组
            byte[] pdfbyte = buildEngine.buildPdfByDocx(templateByte, doc);
            System.out.println(pdfPath);
            String dirName = StringUtils.substringBeforeLast(pdfPath, "/");
            System.out.println(dirName);
            FileUtil.createDirectory(dirName);
            CommonUtil.writeBytesToFile(pdfbyte, pdfPath);
            return pdfPath;
        } catch (Exception e) {
            logger.error("模板服务异常，生成PDF文件失败", e);
            return null;
        }
    }

    /**
     * 通过模板创建PDF
     * @param contract 合同对象
     * @return 成功返回pdf路径，失败返回null
     */
    public static String createContractPDF(EcaContract contract) {
        String savePath = AppConfig.getProperty("tranportmt.contractpdf.savepath");
        String date = TimeUtil.formatDateYYYYMM(new Date());
        String fileName = renameFile();
        String pdfPath = savePath + "/" + date + "/" + fileName;
        return createContractPDF(contract, pdfPath);
    }

    /**
     * 合同对象转换xml
     * @param contract 合同对象
     * @return xmlString
     */
    private static String contract2Xml(EcaContract contract) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("contractNum", contract.getContractNum());
        map.put("tsOrderNo", contract.getTsOrderNo());
        if(contract.getShipperType() == null) {
            contract.setShipperType(1);
        }
        if (contract.getShipperType().intValue() == 2) { // 如果是企业
            map.put("dynamicName", "企  业：");
            map.put("proxyName", "代理人：");
            map.put("shipperName", contract.getShipperName());
            map.put("shipperUserName", contract.getShipperUserName());
        } else {
            map.put("dynamicName", "签约人：");
            map.put("shipperName", contract.getShipperUserName());
        }

        map.put("shipperIdCard", contract.getShipperIdCard());
        map.put("shipperPhone", contract.getShipperPhone());

        map.put("carryUserName", contract.getCarryUserName());
        map.put("carryIdCard", contract.getCarryIdCard());
        map.put("carryPhone", contract.getCarryPhone());

        map.put("startPoint", contract.getStartPoint());
        map.put("startDetailAdd", contract.getStartDetailAdd());
        map.put("destPoint", contract.getDestPoint());
        map.put("destDetailAdd", contract.getDestDetailAdd());
        map.put("taskContent", contract.getTaskContent());
        map.put("headLicensePlate", contract.getHeadLicensePlate());
        map.put("tailLicensePlate", contract.getTailLicensePlate());
        map.put("price", contract.getPrice() + "元");
        map.put("specification", contract.getSpecification());
        if(StringUtils.isNotBlank(contract.getSettleAccountsType())) {
            map.put("settleAccountsType", contract.getSettleAccountsType());
        } else {
            map.put("settleAccountsType", "--");
        }
        if(contract.getStartDate() != null) {
            map.put("startDate",TimeUtil.formatDate(contract.getStartDate()));
        } else {
            map.put("startDate","--");
        }
        if (contract.getEndDate() != null) {
            map.put("endDate",TimeUtil.formatDate(contract.getEndDate()));
        } else {
            map.put("endDate","--");
        }
//        if(contract.getShipperSignTime() != null) {
//            map.put("shipperSignTime",TimeUtil.formatDateTime(contract.getShipperSignTime()));
//        }
//        if(contract.getCarrySignTime() != null) {
//            map.put("carrySignTime",TimeUtil.formatDateTime(contract.getCarrySignTime()));
//        }

        String xmlString = XmlUtil.mapToXML(map, "data");
        return xmlString;
    }

    /**
     * xml string parse w3c.Document
     * @param xmlString
     * @return
     */
    public static Document parseXMLDocument(String xmlString) {
        if (xmlString == null) {
            throw new IllegalArgumentException();
        }
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        try {
            DocumentBuilder db = dbf.newDocumentBuilder();
            return db.parse(
                    new InputSource(new StringReader(xmlString)));
        } catch (Exception e) {
            logger.error("xml解析异常:" + xmlString);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 生成文件名称
     * @return
     */
    private static String renameFile(){
        //文件前缀，后缀，文件名称
        String prefix = "con", suffix = "", fileName = "";
        String time = TimeUtil.formatDateHm(new Date());
        suffix= String.valueOf((int)(Math.random()*1000));
        fileName = prefix + time + suffix +".pdf";
        return fileName;
    }


    public static void main(String[] args) throws Exception {
        EcaContract contract = new EcaContract();
        contract.setContractNum("18010800081908");
        contract.setTsOrderNo("17112400000088");
        contract.setShipperName("合肥永清货运有限责任公司");
        contract.setShipperUserName("刘沙沙");
        contract.setShipperIdCard("33226219900803106832");
        contract.setShipperPhone("17790908080");

        contract.setCarryUserName("祁雅雅");
        contract.setCarryIdCard("23366219900308102739");
        contract.setCarryPhone("13101098888");

        contract.setStartPoint("北京市昌平区");
        contract.setStartDetailAdd("北关环岛向南500米左转大院内东侧");
        contract.setDestPoint("南京市北东区");
        contract.setDestDetailAdd("金福路256号");
        contract.setTaskContent("220挖机加斗加锤加配件");
        contract.setHeadLicensePlate("京A35E89");
        contract.setTailLicensePlate("京A6030B");
        contract.setPrice(5000);
        contract.setSpecification("20吨");
        contract.setStartDate(new Date());
        contract.setEndDate(new Date());
        contract.setSettleAccountsType("四五一付款");
        contract.setShipperSignTime(new Date());
        contract.setCarrySignTime(new Date());
        contract.setShipperType(2);

        String result = createContractPDF(contract);
        FileUtil.openFile(result); //打开文件
        System.out.println(result);
//        renameFile("ss");
    }
}
