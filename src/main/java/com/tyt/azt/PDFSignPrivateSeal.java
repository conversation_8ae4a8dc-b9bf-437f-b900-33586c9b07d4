package com.tyt.azt;

import com.esa2000.PfxSignShell;
import com.esa2000.pdfsign.server.ShellExtendForSubCerter;
import com.esa2000.pdfsign.util.CommonUtil;

import com.esa2000.seal.draw.ImageProcess;
import com.tyt.config.util.AppConfig;
import com.tyt.model.EcaContract;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class PDFSignPrivateSeal {

    public static Logger logger = LoggerFactory.getLogger(PDFSignPrivateSeal.class);

    /**
     * PDF签章辅助类
     * @param userId 当前用户ID
     * @param contract 合同对象
     * @return 签章成功返回签章的PDF路径,否则返回null
     */
    public static String pdfSign(Long userId, EcaContract contract) {
        String pdfPath = "";
        String signedPdfPath = "";
        if(contract.getStatus() == 2) { //单方签约
            pdfPath = contract.getBlankPdfPath();
            signedPdfPath = renameFile(contract.getBlankPdfPath());
        } else if(contract.getStatus() == 3) { // 双方签约
            pdfPath = contract.getSignPdfPath();
            signedPdfPath = renameFile(contract.getSignPdfPath());
        } else { // 信息不对等返回
            logger.info("合同状态不正确，无法签章，状态：" + contract.getStatus());
            return null;
        }
        String signKeyWord = ""; // 签名人关键字
        String signTimeKeyWord = ""; // 签名时间关键字
        String signUserName = ""; // 签名人真实姓名
        String signIdCard = ""; // 签名人身份证号
        String signCellPhone = ""; // 签名人手机号
        if (userId.equals(contract.getShipperUserId())) { // 如果是托运方
            signUserName = contract.getShipperUserName();
            signIdCard = contract.getShipperIdCard();
            signCellPhone = contract.getShipperPhone();
            signKeyWord = "##shipperSign##";
            signTimeKeyWord = "##shipperSignTime##";
        } else if (userId.equals(contract.getCarryUserId())) { // 如果是承运方
            signUserName = contract.getCarryUserName();
            signIdCard = contract.getCarryIdCard();
            signCellPhone = contract.getCarryPhone();
            signKeyWord = "##carryerSign##";
            signTimeKeyWord = "##carryerSignTime##";
        } else { // 信息不对等返回
            logger.info("合同内人员与当前用户不匹配,无法签章,当前用户：{},合同ID：{}", userId, contract.getId());
            return null;
        }


        ShellExtendForSubCerter shellExtend = new ShellExtendForSubCerter();

        // 下载软证书用的URL
//        String certQueryServerUrl = "http://test.easysign.cn/APWebPF/CertQueryServlet";
        String certQueryServerUrl = AppConfig.getProperty("azt.cert.queryUrl");

        // 证书ID
        //个人证书id
        String certId = AloneCertMultiCaAPI.applyCert(signUserName,signIdCard,signCellPhone); //多通道
//        String certId = AloneCertAPI.applyCert(signUserName,signIdCard,signCellPhone); // 单通道
//        String certId = "ff808081600c38cd0160343151bc0f28";
//        String certId = "ff80808161094f21016113c1bb5d0200";
        if(StringUtils.isBlank(certId)) { //如果申请证书失败，则返回
            logger.info("安证通申请certId失败,无法签章,用户姓名：{},用户身份证号：{}", signUserName, signIdCard);
            return null;
        }
        // 设置编码方式
        shellExtend.setCharsetName("UTF-8");
        // 下载证书并生成图片
//        int result = shellExtend.initCert(certQueryServerUrl, certId); // 单通道获取证书
        int result = shellExtend.initCACert(certQueryServerUrl, certId, "mca"); // 多通道获取证书 与 单通道授权方式不同
        if(result == 0) {// 成功
            /**
             * 第一个参数：待签章文件路径
             * 第二个参数：签章后文件保存路径
             */
            // 加盖印章
            PfxSignShell signShell = new PfxSignShell();
            signShell.init(pdfPath, signedPdfPath, true);
            signShell.initSoftSeal(shellExtend);
            byte[] imgBytes = ImageProcess.drawRectangleSealImg(signUserName);
            signShell.initSoftSeal(shellExtend.getPfxBytes(),imgBytes,shellExtend.getPassword());
            signShell.addSeal(signKeyWord); // 签章
//            signShell.setLeftOffset(-30); //10号字体，带时分秒
            signShell.setLeftOffset(-45); //12号字体，不带时分秒
            signShell.setUpOffset(5);
            signShell.addSealText(TimeUtil.formatDate(new Date()),signTimeKeyWord); //增加时间
            signShell.sign();
            signShell.close();
            return signedPdfPath;
        } else {
            logger.info("获取软证书失败,错误代码如下:" + result + ",certId="+certId);
        }
        return null;
    }

    /**
     * 更新文件名称
     * @param path 路径含文件名称
     * @return 变更后的文件名称，含路径
     */
    private static String renameFile(String path){
//        int isContain = StringUtils.lastIndexOf(path, "_sign");
//        if (isContain == -1){
//            path = StringUtils.substringBeforeLast(path, ".") + "_sign.pdf";
//        }
        // 流不可重复读
        path = StringUtils.substringBeforeLast(path, ".") + "_sign.pdf";
        return path;
    }


    public static void main(String[] args) {
        EcaContract contract = new EcaContract();
        contract.setContractNum("18010800081908");
        contract.setTsOrderNo("17112400000088");
        contract.setShipperName("合肥永清货运有限责任公司");
        contract.setShipperUserId(39319l);
        contract.setShipperUserName("刘沙沙");
        contract.setShipperIdCard("33226219900803106832");
        contract.setShipperPhone("17790908080");
        contract.setShipperType(2);

        contract.setCarryUserId(136278l);
        contract.setCarryUserName("祁雅雅");
        contract.setCarryIdCard("23366219900308102739");
        contract.setCarryPhone("13101098888");

        contract.setStartPoint("北京市昌平区");
        contract.setStartDetailAdd("北关环岛向南500米左转大院内东侧");
        contract.setDestPoint("南京市北东区");
        contract.setDestDetailAdd("金福路256号");
        contract.setTaskContent("220挖机加斗加锤加配件");
        contract.setHeadLicensePlate("京A35E89");
        contract.setTailLicensePlate("京A6030B");
        contract.setPrice(5000);
        contract.setSpecification("20吨");
        contract.setStartDate(new Date());
        contract.setEndDate(new Date());
        contract.setSettleAccountsType("四五一付款");
        contract.setShipperSignTime(new Date());
        contract.setCarrySignTime(new Date());
        contract.setStatus(2);

//        contract.setStatus(3);
//        contract.setBlankPdfPath("/Users/<USER>/d/cecece/con01202106358421.pdf");
//        contract.setSignPdfPath("/Users/<USER>/d/cecece/con01202106358421_sign.pdf");

//        String pdfPath = ContractTemplate.createContractPDF(contract);
//        contract.setBlankPdfPath(pdfPath);
//        System.out.println("签章前路径："+ pdfPath);
//        136278l 39319l
//        String signedPdfPath = pdfSign(39319l, contract);
//        String signedPdfPath = pdfSign(136278l, contract);
//        System.out.println("签章后路径："+ signedPdfPath);
//        CommonUtil.openFile(signedPdfPath);
    }

}
