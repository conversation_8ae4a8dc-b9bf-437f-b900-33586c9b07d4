package com.tyt.azt;

import com.esa2000.pdfsign.util.CommonUtil;
import com.tyt.service.common.common.HttpClientFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by duanwc on 18/1/17.
 * 单通道证书申请
 * 不建议使用
 */
public class AloneCertAPI {
    public static Logger logger = LoggerFactory.getLogger(AloneCertAPI.class);

    private static CloseableHttpClient httpClient = HttpClientFactory.getHttpClientWithRetry();

    /**
     * 指定字符集
     */
    public static final String Charset_UTF_8 = "UTF-8";

    /**
     * 申请证书接口
     *
     * @param name   姓名
     * @param idcard 身份证号
     * @return 返回结果
     */
    public static String applyCert(String name, String idcard, String cellPhone) {
        String url = "http://test.easysign.cn/APWebPF/CfcaCertServlet";
        // 法人姓名
        String userName = name;
        // 法人身份证号码
        String idNumber = idcard;
        // 用户身份证号码
        String userIdNo = idcard;//注意出生年月日的日期的格式为真实的
        // 手机号码
        String mobile = cellPhone;
        // 机构名称
        String orgName = "安证通"; // 必填项
        // 机构缩写
        String EngName = "azt"; // 必填项
        // 机构编码
        String organCode = "**********"; // 必填项

        // 电子邮箱
        String email = "";
        // 地址
        String address = "";
        //哈希值
        String selfExtValue = "536E4635BAA3D5E751AEBB28951D1BB5B032F283";

        //授权码用户
        String account = "xiaobai";
        //授权密码
        String password = "123456";

        long start = System.currentTimeMillis();
        String result = getID(url, userName, idNumber, email, mobile, orgName, EngName, userIdNo, organCode, address, account, password);
        long end = System.currentTimeMillis();
        logger.info(name + "证书申请耗时：" + (end - start));
        return result;
    }

    /**
     * 请求证书类
     * @param path
     * @param name
     * @param idNumber
     * @param email
     * @param mobile
     * @param orgName
     * @param EngName
     * @param userIdNo
     * @param organCode
     * @param address
     * @param account
     * @param password
     * @return
     */
    private static String getID(String path, String name, String idNumber, String email, String mobile,
                               String orgName, String EngName, String userIdNo, String organCode, String address, String account, String password) {
        String dataType = "1";// 1 -返回证书id 2-返回完整的证书内容
        String certType = "2";// 证书类型1机构证书  2个人证书
        String certModel = "2";// 收费模式
        String IdTypeCode = "0";// 证件类型 0-2为个人 3-9为企业
        String userName = name;
        userName = base64EncodeString(userName);
        orgName = base64EncodeString(orgName);
        EngName = base64EncodeString(EngName);

        String TelNo = mobile;
        String userIdNum = idNumber;

        List<NameValuePair> params = new ArrayList<NameValuePair>();
        params.add(new BasicNameValuePair("dataType", dataType));
        params.add(new BasicNameValuePair("certType", certType));
        params.add(new BasicNameValuePair("certModel", certModel));
        params.add(new BasicNameValuePair("userName", userName));
        params.add(new BasicNameValuePair("EngName", EngName));
        params.add(new BasicNameValuePair("userIdNo", userIdNo));
        params.add(new BasicNameValuePair("IdTypeCode", IdTypeCode));
        params.add(new BasicNameValuePair("email", email));
        params.add(new BasicNameValuePair("organCode", organCode));
        params.add(new BasicNameValuePair("orgName", orgName));
        params.add(new BasicNameValuePair("address", address));
        params.add(new BasicNameValuePair("TelNo", TelNo));
        params.add(new BasicNameValuePair("userIdNum", userIdNum));
        params.add(new BasicNameValuePair("ACCOUNT", account));
        params.add(new BasicNameValuePair("PASSWORD", password));

        HttpPost httpPost = new HttpPost(path);
        CloseableHttpResponse response = null;
        try {
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            httpPost.setEntity(new UrlEncodedFormEntity(params, Charset_UTF_8));
            response = httpClient.execute(httpPost);
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
            }
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            if (StringUtils.isBlank(result)) {
                logger.error("安证通单CA申请证书API服务请求未返回任何结果,请求姓名：{}，身份证号：{}", name, idNumber);
                return null;
            }
            if (result != null && !"".equals(result)) {
                result = result.trim();
                if (result.startsWith("<msg>")) {
                    String baseResult = result.substring(5, result.length() - 6);
                    logger.info("安证通单CA申请证书API服务申请失败,错误信息：{}", CommonUtil.base64DecodeString(baseResult, "GBK"));
                    System.out.println("错误信息：" + CommonUtil.base64DecodeString(baseResult, "GBK"));
                    return null;
                } else {
                    logger.info("安证通单CA申请证书API服务申请成功，name:{}, certId:{}：", name, result);
                    return result;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("安证通申请证书API服务请求异常,请求姓名："+name+"，身份证号："+idNumber, e);
            return null;
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 对字符串进行Base64编码
     *
     * @param value 需编码的字符串
     * @return 编码后的字符串
     */
    private static String base64EncodeString(String value) {
        String encodeValue = null;
        if (value != null) {
            try {
                encodeValue = new sun.misc.BASE64Encoder().encode(value.getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            return encodeValue;
        }
        return encodeValue;
    }

    public static void main(String[] args) {
        applyCert("张新新", "420881199405246231", "13267779276");
    }
}
