package com.tyt.manbang.enums;

/**
 * @description 订单取消原因类型枚举类
 * <AUTHOR>
 * @date 2022/10/17 10:03
 */
public enum CancelReasonTypeEnum {
    OTHER(0, "其他"),
    DRIVER(1, "司机时间有变，协商取消"),
    FACTORY(2, "因厂家原因取消订单"),
    WEATHER(3, "因天气或其他不可抗力原因取消订单");

    private Integer code;
    private String msg;

    private CancelReasonTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }
}
