package com.tyt.manbang.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.infofee.enums.OrderStatusType;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.manbang.bean.request.*;
import com.tyt.manbang.service.MbInfoFeeDetailService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TytTransportOrders;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.ReturnCodeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/plat/manBang/infoFee")
public class MbInfoFeeDetailController {
    private static final Logger logger = LoggerFactory.getLogger(MbInfoFeeDetailController.class);

    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Resource(name = "transportOrdersService")
    private TransportOrdersService transportOrdersService;

    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;

    @Resource(name = "mbInfoFeeDetailService")
    private MbInfoFeeDetailService mbInfoFeeDetailService;

    @Resource(name = "transportWayBillExService")
    private TransportWayBillExService transportWayBillExService;

    /**
     * 订金状态 refundedToDriver：已退款到司机  settledToShipper：已结算到货主
     */
    private static final String SETTLED_TO_SHIPPER="settledToShipper";
    private static final String REFUNDED_TO_DRIVER="refundedToDriver";


    @PostMapping(value = "/saveOrderCreateSuccessInfo")
    public ResultMsgBean saveOrderCreateSuccessInfo(MbOrderCreateSucNoticeBean orderCreateSucNoticeBean) {
        logger.info("saveOrderCreateSuccessInfo【orderCreateSucNoticeBean:{}】", JSON.toJSONString(orderCreateSucNoticeBean));
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        long t1 = System.currentTimeMillis();
        //根据第三方单号 查询tyt_transport_orders表 判断该条数据是否已处理成功
        Long thirdOrderNo = orderCreateSucNoticeBean.getOrderId();
        TytTransportOrders thirdPartyPlatformOrder = transportOrdersService.getByThirdPartyPlatformOrderNo(String.valueOf(thirdOrderNo));
        if(thirdPartyPlatformOrder!=null){
            logger.info("saveOrderCreateSuccessInfo【三方平台订单号:{}】,已处理成功", thirdOrderNo);
            return resultMsgBean;
        }
        //获取满帮货物Id
        Long cargoId = orderCreateSucNoticeBean.getCargoId();
        //根据满帮货物Id查询对应的特运通系统的srcMsgId  (获取不到 代表该货物 已下架)
        Long srcMsgId = transportOrdersService.getSrcMsgIdByCargoId(cargoId);
        try {
            //查看该货物信息 是否已被支付过
            int paySuccessCount = transportOrdersService.getGoodsPaySuccessCount(srcMsgId);
            logger.info("saveOrderCreateSuccessInfo【srcMsgId:{}】【PaySuccessCount:{}】 ", srcMsgId, paySuccessCount);
            if (paySuccessCount > 0) {
                // 10001：取消订单接口MQ消息
                mbInfoFeeDetailService.saveAndSendOrderCancelMbMq(orderCreateSucNoticeBean);
            } else {
                Transport transport = transportBusiness.getByGoodsId(srcMsgId);
                if (transport != null) {
                    //具体业务相关处理
                    resultMsgBean = mbInfoFeeDetailService.saveMbPayOrderBusiness(orderCreateSucNoticeBean, srcMsgId,resultMsgBean);
                } else {
                    resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                    resultMsgBean.setMsg("失败");
                }
            }
            long t2 = System.currentTimeMillis();
            logger.info("saveOrderCreateSuccessInfo总耗时【{}】ms", t2 - t1);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }
    }

    @PostMapping(value = "/handlerOrderRefundInfo")
    public ResultMsgBean handlerOrderRefundInfo(MbOrderRefundNoticeBean orderRefundNoticeBean) {
        logger.info("handlerOrderRefundInfo【orderRefundNoticeBean:{}】", JSON.toJSONString(orderRefundNoticeBean));
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        long t1 = System.currentTimeMillis();
        //根据第三方单号 查询tyt_transport_orders表 判断该条数据是否已处理成功
        Long thirdOrderNo = orderRefundNoticeBean.getOrderId();
        TytTransportOrders  tranSportOrders = transportOrdersService.getByThirdPartyPlatformOrderNo(String.valueOf(thirdOrderNo));
        if(tranSportOrders!=null&&((tranSportOrders.getCostStatus()==OrderStatusType.REFUNDED.getStatus()&&tranSportOrders.getRefundStatus()>=2)||(tranSportOrders.getCostStatus()==OrderStatusType.CONFIRM_PAY.getStatus()))){
            logger.info("handlerOrderRefundInfo【三方平台订单号:{}】,已处理成功",thirdOrderNo);
            return resultMsgBean;
        }
        //订金状态:refundedToDriver：已退款到司机  settledToShipper：已结算到货主
        String infoFeeStatus = orderRefundNoticeBean.getStatus();
        try {
            if (tranSportOrders != null) {
                int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
                if (LockUtil.lockObject("3", String.valueOf(tranSportOrders.getId()), redisLockTimeout)){
                    if (infoFeeStatus.equals(REFUNDED_TO_DRIVER)) {
                        resultMsgBean = mbInfoFeeDetailService.handlerRefundToDriverBusiness(orderRefundNoticeBean, tranSportOrders, resultMsgBean);
                    } else if(infoFeeStatus.equals(SETTLED_TO_SHIPPER)) {
                        resultMsgBean = mbInfoFeeDetailService.handlerSettledToShipperBusiness(orderRefundNoticeBean, tranSportOrders, resultMsgBean);
                    }else{
                        logger.info("handlerOrderRefundInfo infoFeeStatus 【{}】不在处理范围内",infoFeeStatus);
                        resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                        resultMsgBean.setMsg("失败");
                    }
                }
            } else {
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("失败");
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }finally {
            long t2 = System.currentTimeMillis();
            logger.info("handlerOrderRefundInfo总耗时【{}】ms,【thirdOrderNo{}", t2 - t1, thirdOrderNo);
            if (tranSportOrders != null) {
                LockUtil.unLockObject("3", tranSportOrders.getId() + "");
                //释放redis锁
                logger.info("handlerOrderRefundInfo release redis lock success");
            }
        }
    }


    @PostMapping(value = "/saveCarExceptionReporting")
    public ResultMsgBean saveCarExceptionReporting(CarExceptionReportingBean carExceptionReportingBean) {
        logger.info("saveCarExceptionReporting【carExceptionReportingBean:{}】", JSON.toJSONString(carExceptionReportingBean));
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        TytTransportOrders orders = null;
        try {
            orders = transportOrdersService.getTransportByThirdpartyNo(carExceptionReportingBean.getOrderId());
            if (orders == null) {
                resultMsgBean.setCode(501);
                resultMsgBean.setMsg("该运单不存在");
                return resultMsgBean;
            }

            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3", String.valueOf(orders.getId()), redisLockTimeout)) {
                logger.info("saveCarExceptionReporting transport order ex save get redis lock successed, order id is: " + orders.getId());
                if (orders.getCostStatus() == OrderStatusType.FREEZE.getStatus() //冻结中
                        || orders.getCostStatus() == OrderStatusType.REFUSE_REFUND.getStatus() //拒绝退款
                        || (orders.getCostStatus() == OrderStatusType.PAY.getStatus()) //支付成功，车、货均可
                        || (orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus())) {

                    transportWayBillExService.saveCarExceptionReporting(carExceptionReportingBean,orders);
                } else {
                    int code = OrderStatusType.getFailCode(orders.getCostStatus());
                    String name = OrderStatusType.getName(orders.getCostStatus());
                    resultMsgBean.setCode(code);
                    resultMsgBean.setMsg(name);
                }
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        } finally {
            if ( orders != null) {
                LockUtil.unLockObject("3", String.valueOf(orders.getId()));
                //释放redis锁
                logger.info("saveCarExceptionReporting release redis lock success 【orderId{}】,【thirdPartyPlatformOrderNo{}】 ", orders.getId(), carExceptionReportingBean.getOrderId());
            }
        }
    }

    @PostMapping(value = "/saveShipperExceptionReporting")
    public ResultMsgBean saveShipperExceptionReporting(ShipperExceptionReportingBean shipperExceptionReportingBean) {
        logger.info("saveShipperExceptionReporting【ShipperExceptionReportingBean:{}】", JSON.toJSONString(shipperExceptionReportingBean));
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            TytTransportOrders orders = transportOrdersService.getTransportByThirdpartyNo(shipperExceptionReportingBean.getOrderId());
            //获取当前订单状态
            if (orders.getCostStatus() == OrderStatusType.FREEZE.getStatus() //冻结中
                    || orders.getCostStatus() == OrderStatusType.REFUSE_REFUND.getStatus() //拒绝退款
                    || (orders.getCostStatus() == OrderStatusType.PAY.getStatus()) //支付成功，车、货均可
                    || (orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus())) {

                transportWayBillExService.saveShipperExceptionReporting(shipperExceptionReportingBean,orders);
            } else {
                int code = OrderStatusType.getFailCode(orders.getCostStatus());
                String name = OrderStatusType.getName(orders.getCostStatus());
                resultMsgBean.setCode(code);
                resultMsgBean.setMsg(name);
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }
    }


    @PostMapping(value = "/cancelShipperExceptionReporting")
    public ResultMsgBean cancelShipperExceptionReporting(ShipperCancelExceptionReportingBean req) {
        logger.info("cancelShipperExceptionReporting【ShipperCancelExceptionBean:{}】", JSON.toJSONString(req));
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            TytTransportOrders transportOrders = transportOrdersService.getTransportByThirdpartyNo(req.getOrderId());
            resultMsgBean=transportWayBillExService.cancelExSave(req.getExId(),transportOrders.getExCancelStatus(),Constant.GOODS_EX_CANCEL,transportOrders.getId(),resultMsgBean);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }
    }


    @PostMapping(value = "/handleExceptionInfo")
    public ResultMsgBean handleExceptionInfo(HandleExceptionInfoBean handleExceptionInfoBean) {
        logger.info("handleExceptionInfo【handleExceptionInfoBean:{}】", JSON.toJSONString(handleExceptionInfoBean));
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        TytTransportOrders orders = null;
        try {
            orders = transportOrdersService.getTransportByThirdpartyNo(handleExceptionInfoBean.getOrderId());
            if (orders == null) {
                resultMsgBean.setCode(501);
                resultMsgBean.setMsg("该运单不存在");
                return resultMsgBean;
            }

            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3", String.valueOf(orders.getId()), redisLockTimeout)) {
                logger.info("handleExceptionInfo transport order ex get redis lock successed, order id is: " + orders.getId());

                transportWayBillExService.handleExceptionInfo(handleExceptionInfoBean,orders);
            }

            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        } finally {
            if ( orders != null) {
                LockUtil.unLockObject("3", String.valueOf(orders.getId()));
                //释放redis锁
                logger.info("handleExceptionInfo transport order ex release redis lock success 【orderId{}】,【thirdPartyPlatformOrderNo{}】 ",
                        orders.getId(), handleExceptionInfoBean.getOrderId());
            }
        }
    }
}
