package com.tyt.manbang.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.tyt.config.util.AppConfig;
import com.tyt.manbang.bean.response.CreatePersonalResponse;
import com.tyt.manbang.service.UserOpenApplyService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.user.bean.Resp;
import com.tyt.util.tpay.TpayUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * @Description
 * <AUTHOR>
 * @Date 2020/9/10 10:01
 * @Version 1.0
 **/
@Service("userOpenApplyService")
public class UserOpenApplyServiceImpl  implements UserOpenApplyService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());


    /**
     * 用户中心地址
     */
    private  final String userCenterUrl = AppConfig.getProperty("user.center.api.url");

    private final String MERCHANT_ID = AppConfig.getProperty("tpay.merchantId");
    private final String VERSION = AppConfig.getProperty("tpay.version");

    /**
     * 满帮个人开户接口
     *
     * @param user
     * @return
     */
    @Override
    public Pair<Boolean, String> mbOpenAcctInactiveApply(User user) {
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("userId", user.getId());
            paramMap.put("timestamp",System.currentTimeMillis()+"");
            paramMap.put("merchantId",MERCHANT_ID);
            paramMap.put("version",VERSION);

            paramMap.put("uid",user.getMbUserId());
            paramMap.put("loginName",user.getUserName());
            paramMap.put("mobile",user.getCellPhone());
            paramMap.put("real_name",user.getTrueName());

            String inactiveApplyResult = TpayUtil.sendBodyRequest(userCenterUrl + "/mbOpenAcct/inactive/apply", paramMap);
            Resp resp = JSON.parseObject(inactiveApplyResult, Resp.class);
            logger.info("<Manage服务已注册用户调用满帮个人开户>结果:【{}】", JSON.toJSONString(resp));
            if (resp != null && resp.getCode().equals(ResultMsgBean.OK)) {
                CreatePersonalResponse createPersonalResponse = JSON.parseObject(JSON.toJSONString(resp.getData()), CreatePersonalResponse.class);
                if ("T".equals(createPersonalResponse.getIsSuccess())) {
                    return Pair.of(true, createPersonalResponse.getMember_id());
                } else {
                    return Pair.of(false, createPersonalResponse.getErrorMessage());
                }
            }
        } catch (Exception e) {
            logger.error("<Manage已注册用户调用满帮个人开户>异常,userId:{},userPhone:{}", user.getId(), user.getCellPhone());
        }
        return Pair.of(false, null);
    }

}
