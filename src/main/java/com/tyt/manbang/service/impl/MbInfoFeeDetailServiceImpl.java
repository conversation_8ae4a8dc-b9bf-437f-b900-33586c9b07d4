package com.tyt.manbang.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqInfoFeeOperateMsg;
import com.tyt.infofee.enums.RefundReasonTypeEnum;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.infofee.service.TransportWayBillService;
import com.tyt.manbang.bean.mqBean.OrderRefundDepositBean;
import com.tyt.manbang.bean.request.MbOrderCreateSucNoticeBean;
import com.tyt.manbang.bean.request.MbOrderRefundNoticeBean;
import com.tyt.manbang.service.MbInfoFeeDetailService;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.plat.enums.BackoutReasonEnum;
import com.tyt.transport.enums.OrderStatusEnum;
import com.tyt.transport.querybean.TransportDoneRequest;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TytSwitchUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/22 10:01
 * @Version 1.0
 **/
@Service("mbInfoFeeDetailService")
public class MbInfoFeeDetailServiceImpl implements MbInfoFeeDetailService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "transportWayBillService")
    private TransportWayBillService transportWayBillService;

    @Resource(name = "transportOrdersService")
    TransportOrdersService transportOrdersService;

    @Resource(name = "infoFeeBusinessService")
    InfoFeeBusinessService infoFeeBusinessService;

    @Resource(name = "transportBusiness")
    TransportBusinessInterface transportBusiness;

    @Autowired
    private BackendTransportMapper backendTransportMapper;

    @Resource(name = "transportWayBillExService")
    TransportWayBillExService transportWayBillExService;


    //信息费操作mq类型  1 支付   2 确认给货方 3 退还给车方
    private static final Integer OP_STATUS_PAY = 1;
    private static final Integer OP_STATUS_CONFIRM = 2;
    private static final Integer OP_STATUS_REFUND = 3;

    //交易记录tyt_trade_info表中TRADE_TYPE
    private static final int TRADE_TYPE_PAY_INFOFEE = 1;
    private static final int TRADE_TYPE_REFUND_INFOFEE = 2;
    private static final int TRADE_TYPE_CONFIRM_INFOFEE = 4;

    @Override
    public ResultMsgBean saveMbPayOrderBusiness(MbOrderCreateSucNoticeBean orderCreateSucNoticeBean, Long srcMsgId, ResultMsgBean resultMsgBean) throws Exception {
        long s1 = System.currentTimeMillis();
        logger.info("一:满帮订金创单成功通知service处理开始时间【{}】ms", s1);
        //通过配置文件获取userId 组装payUser请求参数
         Long syncUserId = Long.valueOf(tytConfigService.getStringValue("mb_sync_userId"));
        User payUser = new User();
        payUser.setId(syncUserId);
        payUser.setUserName(orderCreateSucNoticeBean.getDriverName());
        String carOwnerTelephone = orderCreateSucNoticeBean.getDriverTelephone().toString();
        payUser.setCellPhone(orderCreateSucNoticeBean.getDriverTelephone().toString());
        //支付金额
        Long agencyMoney = orderCreateSucNoticeBean.getDeposit();
        //车辆信息
        CarSaveBean infoFeeCarRequest = new CarSaveBean();
        TransportDoneRequest doneRequest = new TransportDoneRequest();
        // 根据产品要求 不再展示满帮支付的车辆信息
//        String driverCarNo = orderCreateSucNoticeBean.getDriverCarNo();
//        if (StringUtils.isNotEmpty(driverCarNo)) {
//            infoFeeCarRequest.setHeadCity(driverCarNo.substring(0, 1));
//            infoFeeCarRequest.setHeadNo(driverCarNo.substring(1));
//            doneRequest.setHeadCity(driverCarNo.substring(0, 1));
//            doneRequest.setHeadCity(driverCarNo.substring(1));
//        }
        // 根据srcMsgId从货物主表中获取数据
        TransportMain originalTransport = transportMainService.getById(srcMsgId);
        Transport transport = new Transport();
        BeanUtils.copyProperties(originalTransport, transport);
        //查询是否限时货源
        int timeLimitIdentification = backendTransportMapper.selectIsTimeLimitMsg(srcMsgId);
        long s2 = System.currentTimeMillis();
        logger.info("二:满帮订金创单成功通知service处理组装参数,当前时间【{}】,此操作总耗时【{}】ms", s2, s2 - s1);
        //一:如果没有该运单信息，则添加记录到运单表
        TytTransportWaybill tytTransportWaybill = transportWayBillService.getTytTransportWaybillForLock(originalTransport.getTsOrderNo());
        if (tytTransportWaybill == null) {
            transportWayBillService.saveOnLineWayBill(0,orderCreateSucNoticeBean, transport, payUser, timeLimitIdentification, BigDecimal.ZERO);
        }else{
            transportWayBillService.updateOnLineWayBill(tytTransportWaybill,orderCreateSucNoticeBean, payUser, timeLimitIdentification);
        }
        long s3 = System.currentTimeMillis();
        logger.info("三:满帮订金创单成功通知service添加记录到运单表,当前时间【{}】,此操作总耗时【{}】ms", s3, s3 - s2);
        //二:如果没有该运单信息，则添加记录到订单表

        orderCreateSucNoticeBean.setOrderNewStatus(OrderStatusEnum.WAIT_LOADED.getCode());

        TytTransportOrders tytTransportOrders = transportOrdersService.saveOrderInfo(0,orderCreateSucNoticeBean, transport, carOwnerTelephone, agencyMoney, payUser,
                timeLimitIdentification, null, infoFeeCarRequest, BigDecimal.ZERO, null,"", null,OrderStatusEnum.WAIT_LOADED.getCode(),null,originalTransport.getInvoiceTransport(), null);
        Long orderId = tytTransportOrders.getId();
        long s4 = System.currentTimeMillis();
        logger.info("四:满帮订金创单成功通知service添加记录到运单表,当前时间【{}】,此操作总耗时【{}】ms", s4, s4 - s3);
        //三:保存订单货源快照
        infoFeeBusinessService.saveTransportOrderSnapshot(originalTransport, tytTransportOrders, null,null,null);
        long s5 = System.currentTimeMillis();
        logger.info("五:满帮订金创单成功通知service保存订单货源快照,当前时间【{}】,此操作总耗时【{}】ms", s5, s5 - s4);
        //四:插入tyt_old_order表一条数据
        transportOrdersService.addOldOrderLog(tytTransportOrders);
        long s6 = System.currentTimeMillis();
        logger.info("六:满帮订金创单成功通知service插入支付表,当前时间【{}】,此操作总耗时【{}】ms", s6, s6 - s5);
        //五:插入tyt_trade_info表一条支付信息费数据
        transportWayBillService.addTradeInfo(TRADE_TYPE_PAY_INFOFEE, tytTransportOrders, tytTransportOrders.getPayUserId().intValue(), tytTransportOrders.getUserId().intValue(), 0);
        long s7 = System.currentTimeMillis();
        logger.info("七:满帮订金创单成功通知service插入支付表,当前时间【{}】,总耗时【{}】ms", s7, s7 - s6);
        //六:撤销货源
        transportBusiness.saveInfoFeeUpdateBtnStatusNew(tytTransportOrders.getUserId(), 2, srcMsgId, doneRequest, null, BackoutReasonEnum.mb_sync_notify.getCode(), null,null,true, null, null);
        long s8 = System.currentTimeMillis();
        logger.info("八:满帮订金创单成功通知service插入撤销货源,当前时间【{}】,此操作总耗时【{}】ms", s8, s8 - s7);
        //七:发送满帮订金创单成功通知处理成功mq消息
        saveAndSendThirdInfofeeMq(tytTransportOrders, OP_STATUS_PAY, null);
        resultMsgBean.setData(orderId);
        long s9 = System.currentTimeMillis();
        logger.info("八:满帮订金创单成功通知service发送订单处理成功mq,当前时间【{}】,此操作总耗时【{}】ms", s9, s9 - s8);
        return resultMsgBean;
    }

    @Override
    public ResultMsgBean handlerRefundToDriverBusiness(MbOrderRefundNoticeBean orderRefundNoticeBean, TytTransportOrders tranSportOrders, ResultMsgBean resultMsgBean) throws Exception {
        long s1 = System.currentTimeMillis();
        logger.info("满帮订金已退款到司机通知service处理开始时间【{}】ms", s1);
        Long refundAmount = orderRefundNoticeBean.getAmount();
        String refundReason = orderRefundNoticeBean.getReasonDesc();
        Date refundArrivalTime = new Date(orderRefundNoticeBean.getDeductSuccessTime());
        String thirdPartyPlatformOrderNo = orderRefundNoticeBean.getOrderId().toString();

        //一:更新tyt_transport_orders表退款字段相关信息
        transportOrdersService.updateOrderForRefundSuccess(thirdPartyPlatformOrderNo, refundAmount, refundReason, refundArrivalTime);
        //二:插入tyt_trade_info表一条退款信息费数据
        int tradeId = transportWayBillService.addTradeInfo(TRADE_TYPE_REFUND_INFOFEE, tranSportOrders, tranSportOrders.getUserId().intValue(), tranSportOrders.getPayUserId().intValue(), tranSportOrders.getPayAmount().intValue());
        //三:插入tyt_refund表一条退款记录
        transportOrdersService.addRefundForRefundSuccess(tranSportOrders, refundAmount, refundArrivalTime, tradeId);
        //四:查询该订单在特运通App是否存在 异常上报处理中状态 如果存在回填数据 并将状态变更为处理中
        transportWayBillExService.HandlerTransportWaybillExForThirdPlatform(tranSportOrders.getId().toString(), tranSportOrders.getLoadingStatus(), tranSportOrders.getPayAmount(), 0L);
        //五:满帮订金已退款到司机通知处理成功mq消息
        saveAndSendThirdInfofeeMq(tranSportOrders, OP_STATUS_REFUND, 1);
        long s2 = System.currentTimeMillis();
        logger.info("满帮订金已退款到司机通知service处理结束时间【{}】ms,用时【{}】ms", s2, s2 - s1);
        return resultMsgBean;
    }


    @Override
    public ResultMsgBean handlerSettledToShipperBusiness(MbOrderRefundNoticeBean orderRefundNoticeBean, TytTransportOrders tranSportOrders, ResultMsgBean resultMsgBean) throws Exception {
        long s1 = System.currentTimeMillis();
        logger.info("满帮订金已结算到货主通知service处理开始时间【{}】ms", s1);
        String thirdPartyPlatformOrderNo = orderRefundNoticeBean.getOrderId().toString();
        //一:更新tyt_transport_orders表确认到账字段相关信息
        transportOrdersService.updateOrderForConfirmSuccess(thirdPartyPlatformOrderNo);
        //二:更新tyt_transport_waybill表确认到账字段相关信息
        transportWayBillService.updateWayBillForConfirmSuccess(tranSportOrders.getTsOrderNo());
        //三:插入tyt_trade_info表一条支付信息费数据
        transportWayBillService.addTradeInfo(TRADE_TYPE_CONFIRM_INFOFEE, tranSportOrders, tranSportOrders.getPayUserId().intValue(), tranSportOrders.getUserId().intValue(), 0);
        //四:查询该订单在特运通App是否存在 异常上报处理中状态 如果存在回填数据 并将状态变更为处理中
        transportWayBillExService.HandlerTransportWaybillExForThirdPlatform(tranSportOrders.getId().toString(), 1, 0l, tranSportOrders.getPayAmount());
        //五:满帮订金已结算到货主通知处理成功mq消息
        saveAndSendThirdInfofeeMq(tranSportOrders, OP_STATUS_CONFIRM, null);
        long s2 = System.currentTimeMillis();
        logger.info("满帮订金已结算到货主通知service处理结束时间【{}】ms,用时【{}】ms", s2, s2 - s1);
        return resultMsgBean;
    }


    @Override
    public void saveAndSendOrderCancelMbMq(MbOrderCreateSucNoticeBean orderCreateSucNoticeBean) {
        //组装发送mq的实体类
        OrderRefundDepositBean orderRefundDeposit = new OrderRefundDepositBean();
        orderRefundDeposit.setOrderId(orderCreateSucNoticeBean.getOrderId().toString());
        orderRefundDeposit.setRefundReasonType(RefundReasonTypeEnum.厂家不发货.getCode());
        orderRefundDeposit.setRefundReasonDesc(RefundReasonTypeEnum.厂家不发货.getMsg());
        orderRefundDeposit.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        orderRefundDeposit.setMessageType(MqBaseMessageBean.MB_REFUND_DEPOSIT_ORDER_MESSAGE);
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(() -> {
            //发送并mq信息并保存到数据库
            tytMqMessageService.addSaveMqMessage(orderRefundDeposit.getMessageSerailNum(), JSON.toJSONString(orderRefundDeposit), MqBaseMessageBean.MB_CANCEL_ORDER_MESSAGE);
            tytMqMessageService.sendMbMqMessage(orderRefundDeposit.getMessageSerailNum(), JSON.toJSONString(orderRefundDeposit), MqBaseMessageBean.MB_CANCEL_ORDER_MESSAGE);
        });
        // 关闭线程
        executorService.shutdown();
    }



    private void saveAndSendThirdInfofeeMq(TytTransportOrders orders, Integer opStatus, Integer refundType){
        MqInfoFeeOperateMsg operateMsg = new MqInfoFeeOperateMsg();
        operateMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        operateMsg.setMessageType(MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
        operateMsg.setOpStatus(opStatus);
        operateMsg.setAmount(orders.getPayAmount()+"");
        operateMsg.setCarOwnerUserId(orders.getPayUserId());
        operateMsg.setShipperUserId(orders.getUserId());
        operateMsg.setStartPoint(orders.getStartPoint());
        operateMsg.setDestPoint(orders.getDestPoint());
        operateMsg.setTaskContent(orders.getTaskContent());
        operateMsg.setTsId(orders.getTsId());
        operateMsg.setTsOrderNo(orders.getTsOrderNo());
        operateMsg.setOrderId(orders.getId());
        operateMsg.setPayNo(orders.getPayNo());
        if (refundType != null && refundType>0){
            operateMsg.setRefundType(refundType);
        }
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(() -> {
            //货源成交平台  0 特运通  1:满帮
            Integer thirdpartyPlatformType = orders.getThirdpartyPlatformType();
            if(thirdpartyPlatformType != null && thirdpartyPlatformType == 1) {
                if(TytSwitchUtil.isMbOrderOperateMqOn()){
                    tytMqMessageService.sendMsgCustom(JSON.toJSONString(operateMsg), "TRADE_INFO_FEE_TOPIC", operateMsg.getMessageSerailNum(), "manbang_order", 3000L);
                }else{
                    //发送并mq信息并保存到数据库
                    tytMqMessageService.addSaveMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg), MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
                    tytMqMessageService.sendMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg),MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
                }
            }else{
                if (TytSwitchUtil.isTytOrderOperateMqOn()) {
                    tytMqMessageService.sendMsgCustom(JSON.toJSONString(operateMsg), "TRADE_INFO_FEE_TOPIC", operateMsg.getMessageSerailNum(), "tyt_order", 3000L);
                }else{
                    //发送并mq信息并保存到数据库
                    tytMqMessageService.addSaveMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg), MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
                    tytMqMessageService.sendMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg),MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
                }
            }
        });
        // 关闭线程
        executorService.shutdown();
    }

}
