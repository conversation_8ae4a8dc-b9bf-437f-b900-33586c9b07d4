package com.tyt.manbang.service;
import com.tyt.manbang.bean.request.MbOrderCreateSucNoticeBean;
import com.tyt.manbang.bean.request.MbOrderRefundNoticeBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;


public interface MbInfoFeeDetailService {

    ResultMsgBean saveMbPayOrderBusiness(MbOrderCreateSucNoticeBean orderCreateSucNoticeBean, Long srcMsgId, ResultMsgBean resultMsgBean) throws Exception;

   ResultMsgBean handlerRefundToDriverBusiness(MbOrderRefundNoticeBean orderRefundNoticeBean, TytTransportOrders tranSportOrders, ResultMsgBean resultMsgBean)throws Exception;

    ResultMsgBean handlerSettledToShipperBusiness(MbOrderRefundNoticeBean orderRefundNoticeBean, TytTransportOrders tranSportOrders, ResultMsgBean resultMsgBean)throws Exception;

    void saveAndSendOrderCancelMbMq(MbOrderCreateSucNoticeBean orderCreateSucNoticeBean);


}
