package com.tyt.manbang.bean.mqBean;

import com.tyt.infofee.bean.MqBaseMessageBean;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderCancelBean
 * @description 取消订单实体类
 * @date 2022-10-17 14:00
 */
@Data
public class OrderCancelBean extends MqBaseMessageBean {

    /**
     * 订单 ID，满满平台的订单号
     */
    private String orderId;

    /**
     * 退订金：1、退订金 2、不退订金，默认值为 1
     */
    private Integer refundDeposit;

    /**
     * 订单取消原因类型
     */
    private Integer cancelReasonType;

    /**
     * 订单取消原因描述
     */
    private String cancelReasonDesc;

}
