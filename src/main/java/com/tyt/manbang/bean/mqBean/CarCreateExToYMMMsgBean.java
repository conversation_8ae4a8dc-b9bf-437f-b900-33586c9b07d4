package com.tyt.manbang.bean.mqBean;

import com.tyt.infofee.bean.MqBaseMessageBean;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/08/15 9:42
 * @Version 1.0
 **/
@Data
public class CarCreateExToYMMMsgBean extends MqBaseMessageBean {

    /**
     * 订单ID，满满平台的订单号
     */
    @NotBlank(message = "订单Id不能为空")
    private String orderId;

    /**
     * 车方投诉类型
     * 9、实际货物信息与描述不符
     * 10、无货/货物被他人承运
     * 11、货方变更装、卸位置
     * 12、货方变更装、卸时间
     * 13、订金延迟结算
     * 14、运价纠纷
     * 15、不想拉了
     * 16、其他
     *
     */
    private Integer complainType;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 附件
     */
    private List<String> attachments;

    /**
     * 平台来源
     */
    private Integer platformSource;
}
