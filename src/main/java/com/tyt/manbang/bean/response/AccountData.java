package com.tyt.manbang.bean.response;


import com.alibaba.fastjson.annotation.JSONField;

public class AccountData {
    /**
     * 账户编号
     */
    @JSONField(name = "account_id")
    private String accountId;

    /**
     * 账户类型
     */
    @JSONField(name = "account_type")
    private String accountType;

    /**
     * 账户余额
     */
    @JSONField(name = "balance")
    private String balance;

    /**
     * 账户可用余额
     */
    @JSONField(name = "available_balance")
    private String availableBalance;

    /**
     * 账户冻结金额
     */
    @JSONField(name = "frozen_balance")
    private String frozenBalance;


    /**
     * 账户限制金额
     */
    @JSONField(name = "limit_balance")
    private String limitBalance;


    private Long activate_status;

    private Long freeze_status;

    private Long life_cycle_status;

    private String member_id;

    public Long getActivate_status() {
        return activate_status;
    }

    public void setActivate_status(Long activate_status) {
        this.activate_status = activate_status;
    }

    public Long getFreeze_status() {
        return freeze_status;
    }

    public void setFreeze_status(Long freeze_status) {
        this.freeze_status = freeze_status;
    }

    public Long getLife_cycle_status() {
        return life_cycle_status;
    }

    public void setLife_cycle_status(Long life_cycle_status) {
        this.life_cycle_status = life_cycle_status;
    }

    public String getMember_id() {
        return member_id;
    }

    public void setMember_id(String member_id) {
        this.member_id = member_id;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getAvailableBalance() {
        return availableBalance;
    }

    public void setAvailableBalance(String availableBalance) {
        this.availableBalance = availableBalance;
    }

    public String getFrozenBalance() {
        return frozenBalance;
    }

    public void setFrozenBalance(String frozenBalance) {
        this.frozenBalance = frozenBalance;
    }

    public String getLimitBalance() {
        return limitBalance;
    }

    public void setLimitBalance(String limitBalance) {
        this.limitBalance = limitBalance;
    }
}
