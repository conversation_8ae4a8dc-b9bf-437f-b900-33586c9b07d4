package com.tyt.manbang.bean.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 20223/08/15 16:06
 * @Version 1.0
 **/
@Data
public class ShipperCancelExceptionReportingBean{
    /**
     * 异常上报ID，tyt_transport_waybill_ex 表Id
     */
    @NotBlank(message = "异常上报ID不能为空")
    private Long exId;

    /**
     * 订单ID，满满平台的订单号
     */
    @NotBlank(message = "订单Id不能为空")
    private String orderId;

}
