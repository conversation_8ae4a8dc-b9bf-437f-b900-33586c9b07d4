package com.tyt.manbang.bean.request;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description  满帮订单创建成功Bean
 * <AUTHOR>
 * @Date 2022/10/14 18:25
 * @Version 1.0
 **/
@Data
@ToString
public class MbOrderCreateSucNoticeBean implements Serializable {
    /**
     * 订单Id
     */
    @NotBlank(message = "订单Id不能为空")
    private Long orderId;

    /**
     * 货源Id
     */
    @NotBlank(message = "货源Id不能为空")
    private Long cargoId;

    /**
     * 订金自动结算时间不能为空
     */
    @NotBlank(message = "订金自动结算时间不能为空")
    private Long depositDelayTime;

    /**
     * 支付订金时间
     */
    private Long payDepositTime;

    /**
     * 支付定金金额 (单位分)
     */
    @NotBlank(message = "支付定金金额不能为空")
    private Long deposit;

    /**
     * 运费金额 (单位元)
     */
    private Long carriageFee;

    /**
     * 订单创建时间
     */
    @NotBlank(message = "订单创建时间不能为空")
    private Long orderCreatedTime;

    /**
     * 司机姓名
     */
    @NotBlank(message = "司机姓名不能为空")
    private String driverName;


    /**
     * 司机车牌号
     */
    private String driverCarNo;

    /**
     * 司机手机号
     */
    @NotBlank(message = "司机手机号不能为空")
    private Long driverTelephone;

    /**
     * 司机年龄范围
     */
    private String driverAgeScope;


    /**
     * 司机驾龄范围
     */
    private String drivingYearScope;


    /**
     * 车型
     */
    private String truckType;

    /**
     * 车长
     */
    private String truckLength;

    /**
     * 货源渠道
     */
    private Integer source;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 行驶证日期
     */
    private String drivingLseDate;

    private Integer orderNewStatus;
}
