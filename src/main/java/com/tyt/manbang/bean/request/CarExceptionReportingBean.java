package com.tyt.manbang.bean.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/25 16:06
 * @Version 1.0
 **/
@Data
public class CarExceptionReportingBean implements Serializable {
    /**
     * 订单Id
     */
    @NotBlank(message = "订单Id不能为空")
    private String orderId;

    /**
     * 车方投诉类型
     * 1、发货人爽约
     * 2、货被他人拉走
     * 3、虚假信息
     * 4、实际货物信息与描述不符
     * 5、运价纠纷
     * 6、不想拉了
     * 7、装货时间延长，订金延迟结算
     * 8、其他
     *
     */
    @NotBlank(message = "投诉类型不能为空")
    private String complainType;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 运单状态
     */
    @NotBlank(message = "运单状态不能为空")
    private String orderStatus;
}
