package com.tyt.manbang.bean.request;

import com.tyt.model.TytTransportOrders;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 20223/08/15 16:06
 * @Version 1.0
 **/
@Data
public class ShipperExceptionReportingBean implements Serializable {
    /**
     * 订单ID，满满平台的订单号
     */
    @NotBlank(message = "订单Id不能为空")
    private String orderId;

    /**
     * 货方投诉类型
     * 7、车方无故取消装货
     * 8、因装货时间延长，延迟退款
     * 9、车方无理由加价/多收钱
     * 10、车方装货迟到
     * 11、押金纠纷（不寄回单/回单丢失）
     * 12、其他
     */
    @NotBlank(message = "投诉类型不能为空")
    private String complainType;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 运单状态
     */
    @NotBlank(message = "运单状态不能为空")
    private String orderStatus;
}
