package com.tyt.manbang.bean.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/26 10:54
 * @Version 1.0
 **/
@Data
public class HandleExceptionInfoBean implements Serializable {

    /**
     * 订单Id
     */
    @NotBlank(message = "订单Id不能为空")
    private String orderId;

    /**
     * 订单Id
     */
    @NotBlank(message = "车主金额不能为空")
    private Long driverAmount;

    /**
     * 订单Id
     */
    @NotBlank(message = "货主金额不能为空")
    private Long shipperAmount;

    /**
     * 订单Id
     */
    @NotBlank(message = "完成时间不能为空")
    private Long closeTime;

    /**
     * 订单Id
     */
    @NotBlank(message = "处理结论不能为空")
    private String conclusion;

    /**
     * 订单Id
     */
    @NotBlank(message = "投诉类型不能为空")
    private Integer complainType;

    /**
     * 责任方
     */
    @NotBlank(message = "责任方不能为空")
    private Integer responsibleParty;


}
