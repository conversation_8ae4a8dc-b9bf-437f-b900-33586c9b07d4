package com.tyt.manbang.bean.request;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description  满帮定金退款状态通知Bean
 * <AUTHOR>
 * @Date 2022/10/14 18:25
 * @Version 1.0
 **/
@Data
@ToString
public class MbOrderRefundNoticeBean implements Serializable {
    /**
     * 订单Id
     */
    @NotBlank(message = "订单Id不能为空")
    private Long orderId;

    /**
     * 货源Id
     */
    @NotBlank(message = "货源Id不能为空")
    private Long cargoId;

    /**
     * 金额(单位分)
     */
    @NotBlank(message = "金额不能为空")
    private Long amount;

    /**
     * 退款实际到账时间
     */
    @NotBlank(message = "退款实际到账时间不能为空")
    private Long deductSuccessTime;

    /**
     * 退款原因 ID
     */
    private Integer reasonId;

    /**
     * 退款原因文案
     */
    @NotBlank(message = "退款原因文案不能为空")
    private String reasonDesc;


    /**
     * 费用项Id
     */
    private Long billItemId;

    /**
     *  定金状态
     *  refundedToDriver：已退款到司机
     *  settledToShipper：已结算到货主
     */
    @NotBlank(message = "定金状态不能为空")
    private String status;


}
