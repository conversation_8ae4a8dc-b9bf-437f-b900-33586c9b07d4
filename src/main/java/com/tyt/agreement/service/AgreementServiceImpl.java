package com.tyt.agreement.service;

import com.tyt.agreement.bean.AgreementListBean;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.marketingActivity.bean.MarketingActivityPopupBean;
import com.tyt.model.TytAgreement;
import com.tyt.plat.mapper.base.TytAgreementMapper;
import com.tyt.util.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("agreementService")
public class AgreementServiceImpl extends BaseServiceImpl<TytAgreement,Long> implements AgreementService {

    @Resource(name = "agreementDao")
    public void setBaseDao(BaseDao<TytAgreement, Long> agreementDao) {
        super.setBaseDao(agreementDao);
    }

    @Autowired
    private TytAgreementMapper tytAgreementMapper;

    @Override
    public List<AgreementListBean> getAgreementList(String clientSign) {
        Integer port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
        String sql = "SELECT name,link_url linkUrl FROM tyt_agreement WHERE is_valid=1 AND STATUS=2 AND show_port IN (0,?) AND (show_type=1 OR (show_start_time<NOW() AND show_end_time>NOW())) ORDER BY sort DESC";
        //传入的参数
        Object[] params = new Object[] {port};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("name", Hibernate.STRING);
        scalarMap.put("linkUrl", Hibernate.STRING);
        List<AgreementListBean> agreementList =  this.getBaseDao().search(sql, scalarMap, AgreementListBean.class, params);
        if (CollectionUtils.isNotEmpty(agreementList)){
            return agreementList;
        }
        return new ArrayList<>();
    }

    @Override
    public List<AgreementListBean> getAgreementListNew(String clientSign,Integer tabType) {
        Integer port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
        return tytAgreementMapper.selectAgreementList(port, tabType);
    }
}
