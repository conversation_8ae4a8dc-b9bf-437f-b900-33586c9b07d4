package com.tyt.agreement.service;

import com.tyt.agreement.bean.AgreementListBean;
import com.tyt.base.service.BaseService;
import com.tyt.model.TytAgreement;

import java.util.List;

public interface AgreementService extends BaseService<TytAgreement,Long> {

    List<AgreementListBean> getAgreementList(String clientSign);

    /**
     * 新版获取协议信息 V6130
     * @param clientSign
     * @param tabType 标签类型 1：规则中心 2：公告通知
     * @return
     */
    List<AgreementListBean> getAgreementListNew(String clientSign,Integer tabType);
}
