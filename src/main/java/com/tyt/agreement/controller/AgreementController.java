package com.tyt.agreement.controller;

import com.tyt.agreement.bean.AgreementListBean;
import com.tyt.agreement.service.AgreementService;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

@Controller
@RequestMapping("/plat/agreement")
public class AgreementController extends BaseController {

    @Resource(name = "agreementService")
    private AgreementService agreementService;


    /**
     * 获取协议列表
     * @param baseParameter
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/list")
    @ResponseBody
    public ResultMsgBean getList(BaseParameter baseParameter, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            List<AgreementListBean> list =agreementService.getAgreementList(baseParameter.getClientSign());
            rm.setData(list);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 获取协议列表
     * V6310 改为H5页面 新写一个接口 老接口兼容旧版APP
     * @param clientSign
     * @return
     */
    @RequestMapping(value = {"/getListNew","/getListNew.action"})
    @ResponseBody
    public ResultMsgBean getAgreementListNew(String clientSign,Integer tabType) {
        ResultMsgBean rm = new ResultMsgBean();
        if (StringUtils.isEmpty(clientSign) || Objects.isNull(tabType)) {
            rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
            rm.setMsg("缺少必要参数");
            return rm;
        }
        try {
            List<AgreementListBean> agreementListNew = agreementService.getAgreementListNew(clientSign, tabType);
            rm.setData(agreementListNew);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }
}
