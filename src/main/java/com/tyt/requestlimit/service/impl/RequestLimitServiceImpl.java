package com.tyt.requestlimit.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytUrlLimit;
import com.tyt.requestlimit.service.RequestLimitService;

@Service("requestLimitService")
public class RequestLimitServiceImpl extends BaseServiceImpl<TytUrlLimit, Long> implements RequestLimitService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "requestLimitDao")
	public void setBaseDao(BaseDao<TytUrlLimit, Long> requestLimitDao) {
		super.setBaseDao(requestLimitDao);
	}

	@Override
	public List<TytUrlLimit> getLimit() {
		return this.getBaseDao().find("from TytUrlLimit where serverType=? and status=?", new Object[] { (byte) 1, (byte) 1 });
	}
}
