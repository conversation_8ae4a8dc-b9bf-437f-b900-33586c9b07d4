package com.tyt.requestlimit.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytUrlLimit;
import com.tyt.requestlimit.dao.RequestLimitDao;

@Repository("requestLimitDao")
public class RequestLimitDaoImpl extends BaseDaoImpl<TytUrlLimit, Long> implements RequestLimitDao {

	public RequestLimitDaoImpl() {
		this.setEntityClass(TytUrlLimit.class);
	}
}
