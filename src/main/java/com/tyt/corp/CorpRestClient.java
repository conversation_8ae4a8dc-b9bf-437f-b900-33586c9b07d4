package com.tyt.corp;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.corp.bean.OrderExListResponse;
import com.tyt.corp.bean.OrderRequest;
import com.tyt.corp.bean.OrderListResponse;
import com.tyt.model.User;
import com.tyt.service.common.common.HttpClientFactory;
import com.tyt.service.common.entity.AccountType;
import com.tyt.service.common.entity.HeaderInfo;
import com.tyt.service.common.entity.SessionEntity;
import com.tyt.service.common.interceptor.TokenInterceptor;
import com.tyt.service.common.json.CreateObjectMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.UserService;
import com.tyt.util.ApplicationContextUtils;
import com.tyt.util.Constant;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpMessage;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by duanwc on 2018/8/18.
 */
public class CorpRestClient {

    private static final Logger logger = LoggerFactory.getLogger(CorpRestClient.class.getName());

    private static String CORP_ENDPOINT_PROD = "http://59.110.104.99:8096/originservice/";

    private static String CORP_ENDPOINT_RELE = "http://59.110.104.99:8096/originservice/";

    private static String CORP_ENDPOINT_TEST = "http://59.110.104.99/originservice/";
//    private static String CORP_ENDPOINT_TEST = "http://59.110.104.99:8096/originservice/";

    private ObjectMapper mapper = CreateObjectMapper.instance();
    private String baseUrl;
    private static CorpRestClient instance;
    private CloseableHttpClient httpClient;

    /**
     * 缓存service
     */
    private static CacheService cacheService;
    /**
     * 用户service
     */
    private static UserService userService;


    private CorpRestClient(final String domain) {
        if (domain != null && domain.equals("prod")) {
            this.baseUrl = CORP_ENDPOINT_PROD;
        } else if (domain != null && domain.equals("release")) {
            this.baseUrl = CORP_ENDPOINT_RELE;
        } else {
            this.baseUrl = CORP_ENDPOINT_TEST;
        }
        this.baseUrl = AppConfig.getProperty("corp.server.domin.service");
        this.httpClient = HttpClientFactory.getHttpClientWithRetry();
    }

    public static CorpRestClient getInstance() {
        if (null == instance) {
            logger.info("Initializing CorpRestClient");
//            String systemDomain = System.getenv("TYT_DOMAIN");
            String systemDomain = "test";
            logger.info("System domain: " + systemDomain);

            // 获取service
            cacheService = ApplicationContextUtils.getBean("cacheServiceMcImpl");
            userService = ApplicationContextUtils.getBean("userService");
            // 初始化对象
            instance = new CorpRestClient(systemDomain);
            logger.info("base url: {}", instance.baseUrl);
        }
        return instance;
    }

    /**
     * 设置当前用户的head信息
     *
     * @param userId
     * @return HeaderInfo
     */
    public HeaderInfo setCorpHeads(Long userId) {
        String headerStr = RedisUtil.get(Constant.PLAT_USER_HEADER + userId);
        JSONObject jsonHeader = JSONObject.parseObject(headerStr);
        HeaderInfo header = new HeaderInfo();
        header.setClientSign(jsonHeader.getInteger("clientSign"));
        header.setClientVersion(jsonHeader.getString("clientVersion"));
        header.setClientId(jsonHeader.getString("clientId"));
        header.setOsVersion(jsonHeader.getString("osVersion"));
        header.setPhoneType(jsonHeader.getString("phoneType"));
        header.setDeviceId(jsonHeader.getString("deviceId"));
        header.setCid(jsonHeader.getString("cid"));
        header.setSource("1"); //plat
        try {
            User user = userService.getByUserId(userId);
            SessionEntity session = new SessionEntity();
            session.setThirdUserId(userId);
            session.setThirdUserName(user.getTrueName());
            session.setThirdCellPhone(user.getCellPhone());
            session.setLoginTokenId(user.getTicket());
            session.setAccountType(AccountType.C_CLIENT.getName()); // plat登录属于C端用户
            TokenInterceptor.setSessionEntity(session);
            // header 内存储 ticket, 需要从用户信息内获取
            header.setToken(user.getTicket());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return header;
    }

    /**
     * 将信息添加到header
     *
     * @param httpMessage
     * @param headerInfo
     */
    private void addHeaderToHttpMethod(HttpMessage httpMessage, HeaderInfo headerInfo) {
        if (headerInfo != null) {
            if (headerInfo.getClientSign() != null) {
                httpMessage.addHeader("clientSign", headerInfo.getClientSign().toString());
            }
            if (StringUtils.isNotBlank(headerInfo.getClientVersion())) {
                httpMessage.addHeader("clientVersion", headerInfo.getClientVersion());
            }
            if (StringUtils.isNotBlank(headerInfo.getClientId())) {
                httpMessage.addHeader("clientId", headerInfo.getClientId());
            }
            if (StringUtils.isNotBlank(headerInfo.getOsVersion())) {
                httpMessage.addHeader("osVersion", headerInfo.getOsVersion());
            }
            if (StringUtils.isNotBlank(headerInfo.getPhoneType())) {
                httpMessage.addHeader("phoneType", headerInfo.getPhoneType());
            }
            if (StringUtils.isNotBlank(headerInfo.getDeviceId())) {
                httpMessage.addHeader("deviceId", headerInfo.getDeviceId());
            }
            if (StringUtils.isNotBlank(headerInfo.getCid())) {
                httpMessage.addHeader("cid", headerInfo.getCid());
            }
            if (StringUtils.isNotBlank(headerInfo.getToken())) {
                httpMessage.addHeader("token", headerInfo.getToken());
            }
            httpMessage.addHeader("source", headerInfo.getSource());
            httpMessage.addHeader("Content-Type", "application/json");
        }
    }

    /**
     * 获取企业货源订单接口
     *
     * @param userId
     * @param queryActionType
     * @param queryMenuType
     * @param queryID
     * @param limit
     * @return
     * @throws Exception
     */
    public OrderListResponse getOrderList(Long userId, Integer queryActionType, Integer queryMenuType, Long queryID, Integer limit) throws Exception {
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("queryActionType", obj2String(queryActionType)));
        params.add(new BasicNameValuePair("queryMenuType", obj2String(queryMenuType)));
        params.add(new BasicNameValuePair("queryID", obj2String(queryID)));
        params.add(new BasicNameValuePair("limit", obj2String(limit)));

        String url = baseUrl + "order/list";
        url = url + "?" + URLEncodedUtils.format(params, StandardCharsets.UTF_8);
        HttpGet httpGet = new HttpGet(url);
        HeaderInfo headerInfo = setCorpHeads(userId);
        addHeaderToHttpMethod(httpGet, headerInfo);

        CloseableHttpResponse response = this.httpClient.execute(httpGet);
        try {
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
                return null;
            }

            String strContent = EntityUtils.toString(response.getEntity());
            logger.info("Body content = " + strContent);
            JSONObject resJson = JSONObject.parseObject(strContent);
            if (resJson.getInteger("code") != 200) {
                logger.info("corp服务器未查询到数据或错误，code: " + resJson.getInteger("code") + ",msg:" + resJson.getString("msg"));
                return null;
            }
//            List<OrderResponse> result = mapper.readValue(strContent, new TypeReference<List<OrderResponse>>() {}); // 直接返回list时使用
            strContent = resJson.getString("data");
            OrderListResponse result = mapper.readValue(strContent, OrderListResponse.class);
            return result;
        } catch (Exception e) {
            logger.error("order/list 调用企业服务异常", e);
            return null;
        } finally {
            response.close();
        }
    }

    /**
     * 获取企业货源未读气泡数量接口
     *
     * @param userId
     * @return
     * @throws Exception
     */
    public String getCorpTransBubleNumber(Long userId) throws Exception {
        String url = baseUrl + "transport/app/bubleNumber";
        HttpGet httpGet = new HttpGet(url);
        HeaderInfo headerInfo = setCorpHeads(userId);
        addHeaderToHttpMethod(httpGet, headerInfo);

        CloseableHttpResponse response = this.httpClient.execute(httpGet);
        try {
            if (response.getStatusLine().getStatusCode() != 200) {
                return null;
            }
            String strContent = EntityUtils.toString(response.getEntity());
            logger.info("Body content = " + strContent);
            JSONObject resJson = JSONObject.parseObject(strContent);
            if (resJson.getInteger("code") != 200) {
                logger.info("corp服务器未查询到数据或错误，code: " + resJson.getInteger("code") + ",msg:" + resJson.getString("msg"));
                return null;
            }
            strContent = resJson.getString("data");
            return strContent;
        } catch (Exception e) {
            logger.error("transport/app/bubleNumber 调用企业服务异常", e);
            return null;
        } finally {
            response.close();
        }
    }

    /**
     * 获取企业货源总气泡数量接口
     *
     * @param userId
     * @return
     * @throws Exception
     */
    public String getCorpTransTotalBubleNumber(Long userId) throws Exception {
        String url = baseUrl + "transport/app/totalBubleNumber";
        HttpGet httpGet = new HttpGet(url);
        HeaderInfo headerInfo = setCorpHeads(userId);
        addHeaderToHttpMethod(httpGet, headerInfo);

        CloseableHttpResponse response = this.httpClient.execute(httpGet);
        try {
            if (response.getStatusLine().getStatusCode() != 200) {
                return null;
            }
            String strContent = EntityUtils.toString(response.getEntity());
            logger.info("Body content = " + strContent);
            JSONObject resJson = JSONObject.parseObject(strContent);
            if (resJson.getInteger("code") != 200) {
                logger.info("corp服务器未查询到数据或错误，code: " + resJson.getInteger("code") + ",msg:" + resJson.getString("msg"));
                return null;
            }
            strContent = resJson.getString("data");
            return strContent;
        } catch (Exception e) {
            logger.error("transport/app/totalBubleNumber 调用企业服务异常", e);
            return null;
        } finally {
            response.close();
        }
    }

    /**
     * 获取企业订单异常接口
     *
     * @param userId
     * @param queryActionType
     * @param queryMenuType
     * @param queryID
     * @param limit
     * @return
     * @throws Exception
     */
    public OrderExListResponse getMyOrderExList(Long userId, Integer queryActionType, Integer queryMenuType, Long queryID, Integer limit) throws Exception {
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("queryActionType", obj2String(queryActionType)));
        params.add(new BasicNameValuePair("queryMenuType", obj2String(queryMenuType)));
        params.add(new BasicNameValuePair("queryID", obj2String(queryID)));
        params.add(new BasicNameValuePair("limit", obj2String(limit)));

        String url = baseUrl + "order/ex/list";
        url = url + "?" + URLEncodedUtils.format(params, StandardCharsets.UTF_8);
        HttpGet httpGet = new HttpGet(url);
        HeaderInfo headerInfo = setCorpHeads(userId);
        addHeaderToHttpMethod(httpGet, headerInfo);

        CloseableHttpResponse response = this.httpClient.execute(httpGet);
        try {
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
                return null;
            }

            String strContent = EntityUtils.toString(response.getEntity());
            logger.info("Body content = " + strContent);
            JSONObject resJson = JSONObject.parseObject(strContent);
            if (resJson.getInteger("code") != 200) {
                logger.info("corp服务器未查询到数据或错误，code: " + resJson.getInteger("code") + ",msg:" + resJson.getString("msg"));
                return null;
            }
            strContent = resJson.getString("data");
            OrderExListResponse result = mapper.readValue(strContent, OrderExListResponse.class);
            return result;
        } catch (Exception e) {
            logger.error("order/ex/list 调用企业服务异常", e);
            return null;
        } finally {
            response.close();
        }
    }

    /**
     * 企业货源POST模拟，参考使用
     *
     * @param userId
     * @param orderRequest
     * @return
     * @throws Exception
     */
    public OrderListResponse getOrderList(Long userId, OrderRequest orderRequest) throws Exception {
        HttpPost httpPost = new HttpPost(baseUrl + "order/post/list");

        // 模拟数据
        orderRequest = new OrderRequest();
        orderRequest.setQueryActionType(1);
        orderRequest.setQueryID(0l);
        orderRequest.setQueryMenuType(2);
        orderRequest.setLimit(12);

        StringEntity body = new StringEntity(mapper.writeValueAsString(orderRequest), "UTF-8");
        body.setContentType("application/json");
        httpPost.setEntity(body);
        // 数据添加至header
        HeaderInfo headerInfo = setCorpHeads(userId);
        addHeaderToHttpMethod(httpPost, headerInfo);

        CloseableHttpResponse response = this.httpClient.execute(httpPost);
        try {
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
                return null;
            }

            String strContent = EntityUtils.toString(response.getEntity());
            logger.info("Body content = " + strContent);
            JSONObject resJson = JSONObject.parseObject(strContent);
            if (resJson.getInteger("code") != 200) {
                logger.info("corp服务器未查询到数据或错误，code: " + resJson.getInteger("code") + ",msg:" + resJson.getString("msg"));
                return null;
            }
//            List<OrderResponse> result = mapper.readValue(strContent, new TypeReference<List<OrderResponse>>() {}); // 直接返回list时使用
            strContent = resJson.getString("data");
            OrderListResponse result = mapper.readValue(strContent, OrderListResponse.class);
            return result;
        } catch (Exception e) {
            logger.error("order/post/list 调用企业服务异常", e);
            return null;
        } finally {
            response.close();
        }
    }


    public static void main(String[] args) throws Exception {

    }


    /**
     * 对象转换字符串，如null，则返回null
     *
     * @param object 对象类
     * @return String
     */
    private static String obj2String(final Object object) {
        if (object == null) {
            return null;
        }
        return String.valueOf(object);
    }
}

