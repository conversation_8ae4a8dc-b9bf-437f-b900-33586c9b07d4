package com.tyt.corp.bean;

import java.io.Serializable;

/**
 * Created by duanwc on 2018/8/18.
 */
public class OrderRequest implements Serializable {

    /**
     * queryActionType  1下拉，2上滑；（首次queryActionType=1）
     */
    private Integer queryActionType;
    /**
     * queryMenuType  1待支付2待同意3待装货4已成交5决绝退费
     */
    private Integer queryMenuType;
    /**
     * queryID 下拉是0，上滑是最小sortId；（首次queryID=0）
     */
    private Long queryID;
    /**
     * 查询条数
     */
    private Integer limit;

    public Integer getQueryActionType() {
        return queryActionType;
    }

    public void setQueryActionType(Integer queryActionType) {
        this.queryActionType = queryActionType;
    }

    public Integer getQueryMenuType() {
        return queryMenuType;
    }

    public void setQueryMenuType(Integer queryMenuType) {
        this.queryMenuType = queryMenuType;
    }

    public Long getQueryID() {
        return queryID;
    }

    public void setQueryID(Long queryID) {
        this.queryID = queryID;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }
}
