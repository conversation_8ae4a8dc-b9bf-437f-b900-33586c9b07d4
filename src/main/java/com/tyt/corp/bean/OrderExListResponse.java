package com.tyt.corp.bean;

import com.tyt.infofee.bean.InfoFeeMyPublishBubbleResultBean;
import com.tyt.infofee.bean.TransportOrdersListBean;
import com.tyt.infofee.bean.TransportWayBillExListBean;

import java.io.Serializable;
import java.util.List;

/**
 * Created by duanwc on 2018/8/18.
 */
public class OrderExListResponse implements Serializable {
    private static final long serialVersionUID = 8677943109241038360L;
    /**
     * 列表数据
     */
    private List<TransportWayBillExListBean> data;
    /**
     * 气泡列表
     */
    private List<InfoFeeMyPublishBubbleResultBean> bubbleNumbers;
    /*
     * 当前时间
     */
    private Long currentTime;

    public List<TransportWayBillExListBean> getData() {
        return data;
    }

    public void setData(List<TransportWayBillExListBean> data) {
        this.data = data;
    }

    public List<InfoFeeMyPublishBubbleResultBean> getBubbleNumbers() {
        return bubbleNumbers;
    }

    public void setBubbleNumbers(List<InfoFeeMyPublishBubbleResultBean> bubbleNumbers) {
        this.bubbleNumbers = bubbleNumbers;
    }

    public Long getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(Long currentTime) {
        this.currentTime = currentTime;
    }
}
