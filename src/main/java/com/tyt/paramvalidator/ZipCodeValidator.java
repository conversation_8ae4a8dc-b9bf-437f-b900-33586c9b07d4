package com.tyt.paramvalidator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * @Description  邮政编码验证自定义注解验证器
 * <AUTHOR>
 * @Date  2020/1/10 15:39
 * @Param
 * @return
 **/
public class ZipCodeValidator implements ConstraintValidator<ZipCode, String> {

    private String zipCodeReg = "[1-9]\\d{5}(?!d)";//表示邮编的正则表达式
    private Pattern zipCodePattern = Pattern.compile(zipCodeReg);


    @Override
    public void initialize(ZipCode zipCode) {

    }

    @Override
    public boolean isValid(String val, ConstraintValidatorContext constraintValidatorContext) {
        if(val == null) {
            return true;
        }
        return zipCodePattern.matcher(val).matches();
    }

}
