package com.tyt.paramvalidator;

import org.apache.commons.collections.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.Validator;
import java.util.*;

/**
 * @Description  对象验证器
 * <AUTHOR>
 * @Date  2020/1/10 11:22
 * @Param 
 * @return 
 **/
public class BeanValidator {

    /**
     * 验证某个bean的参数
     *
     * @param object 被校验的参数
     * @throws ValidationException 如果参数校验不成功则抛出此异常
     */
    public static <T> void validate(T object) {
        //获得验证器
        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        //执行验证
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(object);
        //如果有验证信息，则取出来包装成异常返回
        if (CollectionUtils.isEmpty(constraintViolations)) {
            return;
        }
        throw new ValidationException(convertErrorMsg2String(constraintViolations));
    }

    /**
     * @Description  转换异常信息
     * <AUTHOR>
     * @Date  2020/1/10 11:23
     * @Param [set]
     * @return java.lang.String
     **/
    private static <T> Map convertErrorMsg2Map(Set<ConstraintViolation<T>> set) {
        Map<String, StringBuilder> errorMap = new HashMap<>();
        String property;
        for (ConstraintViolation<T> cv : set) {
            //这里循环获取错误信息，可以自定义格式
            property = cv.getPropertyPath().toString();
            if (errorMap.get(property) != null) {
                errorMap.get(property).append("," + cv.getMessage());
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append(cv.getMessage());
                errorMap.put(property, sb);
            }
        }
        return errorMap;
    }


    private static <T> String convertErrorMsg2String(Set<ConstraintViolation<T>> set) {
        //错误信息
        String errorMsg = "";
        for (ConstraintViolation<T> cv : set) {
            //这里循环获取错误信息，可以自定义格式
            String property = cv.getPropertyPath().toString();
            String message = cv.getMessage();
            errorMsg = message;
            break;
        }
        return errorMsg;
    }
    /**
     * @Description  参数验证测试方法
     * <AUTHOR>
     * @Date  2020/1/10 11:49
     * @Param [args]
     * @return void
     **/
    public static void main(String[] args) {
        UserBean userBean = new UserBean();

        userBean.setName("张三");
        userBean.setAddress("124444444112");
        userBean.setEmail("<EMAIL>");

        userBean.setZipCode("100001");
        try {
            //调用对象验证器对所有参数进行校验
            BeanValidator.validate(userBean);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }
}
