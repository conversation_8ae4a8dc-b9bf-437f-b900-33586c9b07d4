package com.tyt.base.service;

import com.tyt.model.PageBean;
import com.tyt.model.User;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:05
 */
public interface BaseService<T,PK extends Serializable> {
    public void add(T t);
    public Serializable addSave(T t);
    public  void update(T t);
    public void delete(PK pk);
    public T getById(PK pk);
    public List<T> getList(String condition,PageBean page);
    public T find(T t);
    public List<T> findList(T t);
    public int executeUpdateSql(final String sql, final Object[] parameters) ;
    public int executeUpdateSql(final String sql, final  Map<String, Object> map) ;
    public T getByIdForLock(PK id);

    void evict(T t);

    void flush();

    void refresh(T t);
}
