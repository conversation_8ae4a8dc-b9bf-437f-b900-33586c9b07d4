package com.tyt.base.dao;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.annotation.Resource;

import org.hibernate.HibernateException;
import org.hibernate.LockMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.AliasToBeanResultTransformer;
import org.hibernate.transform.ResultTransformer;
import org.hibernate.transform.Transformers;
import org.hibernate.type.Type;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;

import com.tyt.model.PageBean;

/**
 * <p>
 * Basic Data Access implements by Hibernate
 * </p>
 * <p>
 * Provide basic insert, delete, update, find
 * 
 * <AUTHOR> <li>mail: <EMAIL></li>
 * @param <T>
 *            entity
 * @param <PK>
 *            primary key
 */
public class BaseDaoImpl<T, PK extends Serializable> implements BaseDao<T, PK> {

	/**
	 * support for HibernateTemplate Provide for SessionFactory
	 */
	private HibernateTemplate hibernateTemplate;

	// private HibernateTemplate readHibernateTemplate;

	@SuppressWarnings("unchecked")
	private Class entityClass;

	public HibernateTemplate getHibernateTemplate() {
		return hibernateTemplate;
	}

	@Override
	public org.hibernate.classic.Session getCurrentSession() {

		org.hibernate.classic.Session currentSession = this.hibernateTemplate.getSessionFactory().getCurrentSession();

		return currentSession;
	}

	@Resource(name = "hibernateTemplate")
	public void setHibernateTemplate(HibernateTemplate hibernateTemplate) {
		this.hibernateTemplate = hibernateTemplate;
	}

	/*
	 * public HibernateTemplate getReadHibernateTemplate() { return
	 * readHibernateTemplate; }
	 * 
	 * @PublicResource(name="readHibernateTemplate") public void
	 * setReadHibernateTemplate(HibernateTemplate readHibernateTemplate) {
	 * this.readHibernateTemplate = readHibernateTemplate; }
	 */
	@SuppressWarnings("unchecked")
	public void setEntityClass(Class entityClass) {
		this.entityClass = entityClass;
	}

	@SuppressWarnings("unchecked")
	public Class getEntityClass() {
		return entityClass;
	}

	public void delete(T entity) {
		hibernateTemplate.delete(entity);
	}

	@SuppressWarnings("unchecked")
	public void deleteById(PK id) {
		hibernateTemplate.delete(hibernateTemplate.get(entityClass, id));
	}

	@SuppressWarnings("unchecked")
	public T findById(PK id) {
		return (T) hibernateTemplate.get(entityClass, id);
	}

	public void insert(T entity) {
		hibernateTemplate.save(entity);
	}

	public Serializable insertWithReturnId(T entity) {
		return hibernateTemplate.save(entity);
	}

	public void update(T entity) {
		hibernateTemplate.update(entity);
	}

	@SuppressWarnings("unchecked")
	public List<T> search(T condition) {

		// If condition is empty, then search all
		if (null == condition) {
			// entity name
			String entityName = entityClass.toString();
			// table name
			String tableName = entityName.substring(entityName.lastIndexOf(".") + 1);
			return (List<T>) hibernateTemplate.find("from " + tableName);

		} else {
			return hibernateTemplate.findByExample(condition);
		}
	}

	@SuppressWarnings("unchecked")
	public List<T> search(final String condition, final PageBean pageBean) {
		// If the condition is empty then search all
		if (null == condition && null == pageBean) {
			return search(null);
		}

		return (List<T>) hibernateTemplate.executeFind(new HibernateCallback<T>() {
			// entity name
			String entityName = entityClass.toString();
			// table name
			String tableName = entityName.substring(entityName.lastIndexOf(".") + 1);
			// HQL
			StringBuffer hql = new StringBuffer("from ");
			StringBuffer Counthql = new StringBuffer("select count(*) from ");

			public T doInHibernate(Session session) throws HibernateException, SQLException {
				// Assembly HQL
				hql.append(tableName);
				Counthql.append(tableName);

				hql.append(" as entity where 1=1 ");
				Counthql.append(" as entity where 1=1 ");

				if (!"".equals(condition) && null != condition) {
					hql.append(" and " + condition);
					Counthql.append(" and " + condition);
				}
				Query query = session.createQuery(hql.toString());
				// If Page is not empty then setting page
				if (null != pageBean) {
					// set all count
					Query countQuery = session.createQuery(Counthql.toString());
					pageBean.setRowCount(Long.parseLong(countQuery.uniqueResult().toString()));
					// pageBean.setRowCount(query.list().size());
					// Page setting
					int firstResultIndex = pageBean.getPageSize() * (pageBean.getCurrentPage() - 1);
					query.setFirstResult(firstResultIndex);
					query.setMaxResults(pageBean.getPageSize());
				}
				return (T) query.list();

			}
		});
	}

	@Override
	public List<T> search(Map<String, Object> map, Map<String, Object> orderBy, PageBean pageBean) {
		return search(null, pageBean);
	}

	/**
	 * 
	 * @param hql
	 *            附带有命名参数的HQL
	 * @param params
	 *            命名参数的名数组
	 * @param pageBean
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<T> search(final String condition, final Object[] params, final PageBean pageBean) {
		// If the condition is empty then search all
		if (null == condition && null == pageBean) {
			return search(null);
		}

		return (List<T>) hibernateTemplate.executeFind(new HibernateCallback<T>() {
			// entity name
			String entityName = entityClass.toString();
			// table name
			String tableName = entityName.substring(entityName.lastIndexOf(".") + 1);
			// HQL
			StringBuffer hql = new StringBuffer("from ");
			StringBuffer Counthql = new StringBuffer("select count(*) from ");

			public T doInHibernate(Session session) throws HibernateException, SQLException {
				// Assembly HQL
				hql.append(tableName);
				Counthql.append(tableName);

				hql.append(" as entity where 1=1 ");
				Counthql.append(" as entity where 1=1 ");

				if (!"".equals(hql) && null != hql) {
					hql.append(" and " + condition);
					Counthql.append(" and " + condition);
				}

				Query query = session.createQuery(hql.toString());

				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				// If Page is not empty then setting page
				if (null != pageBean) {
					// set all count
					Query countQuery = session.createQuery(Counthql.toString());
					if (params != null) {
						int count = params.length;
						for (int i = 0; i < count; i++) {
							countQuery.setParameter(i, params[i]);
						}
					}
					pageBean.setRowCount(Long.parseLong(countQuery.uniqueResult().toString()));
					// pageBean.setRowCount(query.list().size());
					// Page setting
					int firstResultIndex = pageBean.getPageSize() * (pageBean.getCurrentPage() - 1);
					query.setFirstResult(firstResultIndex);
					query.setMaxResults(pageBean.getPageSize());
				}
				return (T) query.list();

			}
		});
	}

	@Override
	public List<T> find(String queryString, Object value) {
		return (List<T>) hibernateTemplate.find(queryString, value);
	}

	@Override
	public List<T> find(String queryString, Object... values) {
		return (List<T>) hibernateTemplate.find(queryString, values);
	}

	/**
	 * sql语句分页查询结果集合
	 * 
	 * @param sql
	 *            sql语句需要包含表名
	 * @param params
	 *            参数值
	 * @param pageNumber
	 *            页数
	 * @param pageSize
	 *            页大小
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<T> search(final String sql, final Object[] params, final int pageNumber, final int pageSize) {
		// If the condition is empty then search all
		if (null == sql && pageNumber != 0 && pageSize != 0) {
			return search(null);
		}

		return (List<T>) hibernateTemplate.executeFind(new HibernateCallback<T>() {

			public T doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql.toString());
				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				int offRow = ((pageNumber == 0 ? 1 : pageNumber) - 1) * pageSize;
				query.setFirstResult(offRow);
				query.setMaxResults(pageSize);
				query.addEntity(entityClass);
				return (T) query.list();
			}
		});
	}

	/**
	 * 返回查询sql的一个对象
	 * 
	 * @param sql
	 *            sql语句包含表名
	 * @param params
	 *            参数数组
	 * @return <O> O
	 */
	@SuppressWarnings("unchecked")
	public <O> O query(final String sql, final Object[] params) {
		HibernateTemplate tmpl = getHibernateTemplate();
		return tmpl.execute(new HibernateCallback<O>() {

			public O doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				List<O> list = query.list();
				if (list != null && list.size() > 0) {
					return list.get(0);
				}
				return null;
			}
		});
	}

	@SuppressWarnings("unchecked")
	public <O> O queryByCondition(final String sql, final Object[] params) {
		HibernateTemplate tmpl = getHibernateTemplate();
		return tmpl.execute(new HibernateCallback<O>() {

			public O doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				return (O) query.list();
			}
		});
	}

	@Override
	public <O> O queryByMap(final String sql, final Map<String, Object> map, final Class<O> c, final Map<String, org.hibernate.type.Type> cMap) {
		HibernateTemplate tmpl = getHibernateTemplate();
		return tmpl.execute(new HibernateCallback<O>() {

			public O doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (map != null) {
					Set<String> keySet = map.keySet();
					for (String string : keySet) {
						Object obj = map.get(string);
						// 这里考虑传入的参数是什么类型，不同类型使用的方法不同
						if (obj instanceof Collection<?>) {
							query.setParameterList(string, (Collection<?>) obj);
						} else if (obj instanceof Object[]) {
							query.setParameterList(string, (Object[]) obj);
						} else {
							query.setParameter(string, obj);
						}
					}
				}
				if (null != cMap) {
					for (Map.Entry<String, org.hibernate.type.Type> entry : cMap.entrySet()) {
						query.addScalar(entry.getKey(), entry.getValue());
					}
				}
				if (c != null)
					query.setResultTransformer(Transformers.aliasToBean(c));
				// List<O> list = query.list();
				Object obj = query.uniqueResult();
				if (obj != null) {
					return (O) obj;
				}
				return null;
			}
		});
	}

	/**
	 * sql语句查询结果集合
	 *
	 * @param sql
	 *            sql语句需要包含表名
	 * @param params
	 *            参数值
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<T> queryForList(final String sql, final Object[] params) {
		// If the condition is empty then search all
		if (null == sql) {
			return search(null);
		}
		return (List<T>) hibernateTemplate.executeFind(new HibernateCallback<T>() {
			public T doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql.toString());
				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				query.addEntity(entityClass);
				return (T) query.list();
			}
		});
	}

	/**
	 * sql语句分页查询结果集合
	 * 
	 * @param <O>
	 * 
	 * @param sql
	 *            sql语句需要包含表名
	 * @param params
	 *            参数值
	 * @param pageNumber
	 *            页数
	 * @param pageSize
	 *            页大小
	 * @param scalarMap
	 *            类属性类型key字段名，value hibernate类型
	 * @param c
	 *            实体类Class
	 * @return <O> List<O >
	 */
	@SuppressWarnings("unchecked")
	public <O> List<O> search(final String sql, final Map<String, org.hibernate.type.Type> scalarMap, final Class<O> c, final Object[] params, final int pageNumber, final int pageSize) {
		// If the condition is empty then search all
		if (null == sql && pageNumber != 0 && pageSize != 0) {
			return null;
		}

		return (List<O>) hibernateTemplate.executeFind(new HibernateCallback<O>() {

			public O doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql.toString());
				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				if (null != scalarMap) {
					for (Entry<String, Type> entry : scalarMap.entrySet()) {
						query.addScalar(entry.getKey(), entry.getValue());
					}
				}

				if (c != null)
					query.setResultTransformer(Transformers.aliasToBean(c));

				int offRow = ((pageNumber == 0 ? 1 : pageNumber) - 1) * pageSize;
				query.setFirstResult(offRow);
				query.setMaxResults(pageSize);
				return (O) query.list();
			}
		});
	}

	@Override
	public List<T> searchByName(final String sql, final Map<String, Object> map) {
		return (List<T>) hibernateTemplate.executeFind(new HibernateCallback<T>() {

			public T doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (map != null) {
					Set<String> keySet = map.keySet();
					for (String string : keySet) {
						Object obj = map.get(string);
						// 这里考虑传入的参数是什么类型，不同类型使用的方法不同
						if (obj instanceof Collection<?>) {
							query.setParameterList(string, (Collection<?>) obj);
						} else if (obj instanceof Object[]) {
							query.setParameterList(string, (Object[]) obj);
						} else {
							query.setParameter(string, obj);
						}
					}
				}
				query.addEntity(entityClass);
				return (T) query.list();
			}
		});
	}

	/**
	 * sql语句查询结果
	 * 
	 * @param <O>
	 * 
	 * @param sql
	 *            sql语句需要包含表名
	 * @param params
	 *            参数值
	 * @param pageNumber
	 *            页数
	 * @param pageSize
	 *            页大小
	 * @param scalarMap
	 *            类属性类型key字段名，value hibernate类型
	 * @param c
	 *            实体类Class
	 * @return <O> List<O >
	 */
	@SuppressWarnings("unchecked")
	public <O> List<O> search(final String sql, final Map<String, org.hibernate.type.Type> scalarMap, final Class<O> c, final Object[] params) {
		return (List<O>) hibernateTemplate.executeFind(new HibernateCallback<O>() {
			public O doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql.toString());
				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				if (null != scalarMap) {
					for (Entry<String, Type> entry : scalarMap.entrySet()) {
						query.addScalar(entry.getKey(), entry.getValue());
					}
				}
				if (c != null)
					query.setResultTransformer(Transformers.aliasToBean(c));
				return (O) query.list();
			}
		});
	}

	public int executeUpdateSql(final String sql, final Object[] parameters) {

		return hibernateTemplate.execute(new HibernateCallback<Integer>() {

			public Integer doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (parameters != null) {
					int count = parameters.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, parameters[i]);
					}
				}
				return query.executeUpdate();
			}
		}).intValue();
	}

	public int executeUpdateSql(final String sql, final Map<String, Object> map) {

		return hibernateTemplate.execute(new HibernateCallback<Integer>() {

			public Integer doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (map != null) {
					Set<String> keySet = map.keySet();
					for (String string : keySet) {
						Object obj = map.get(string);
						// 这里考虑传入的参数是什么类型，不同类型使用的方法不同
						if (obj instanceof Collection<?>) {
							query.setParameterList(string, (Collection<?>) obj);
						} else if (obj instanceof Object[]) {
							query.setParameterList(string, (Object[]) obj);
						} else {
							query.setParameter(string, obj);
						}
					}
				}
				return query.executeUpdate();
			}
		}).intValue();
	}

	@SuppressWarnings("unchecked")
	@Override
	public <O> List<O> search(final String sql, final Map<String, Type> scalarMap, final Class<O> c, final Map<String, Object> paramMap) {
		return (List<O>) hibernateTemplate.executeFind(new HibernateCallback<O>() {
			public O doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql.toString());
				if (paramMap != null) {
					Set<String> keySet = paramMap.keySet();
					for (String string : keySet) {
						Object obj = paramMap.get(string);
						// 这里考虑传入的参数是什么类型，不同类型使用的方法不同
						if (obj instanceof Collection<?>) {
							query.setParameterList(string, (Collection<?>) obj);
						} else if (obj instanceof Object[]) {
							query.setParameterList(string, (Object[]) obj);
						} else {
							query.setParameter(string, obj);
						}
					}
				}
				if (null != scalarMap) {
					for (Entry<String, Type> entry : scalarMap.entrySet()) {
						query.addScalar(entry.getKey(), entry.getValue());
					}
				}
				if (c != null) {
					query.setResultTransformer(Transformers.aliasToBean(c));
				}
				return (O) query.list();
			}
		});
	}

	public T getByIdForLock(PK id) {
		return (T) hibernateTemplate.get(entityClass, id, LockMode.UPGRADE);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<T> searchByHql(final String hql, final Object[] params, final Integer currentPage, final Integer pageSize) {
		return (List<T>) hibernateTemplate.executeFind(new HibernateCallback<List>() {
			public List doInHibernate(Session session) throws HibernateException, SQLException {
				Query query = session.createQuery(hql);
				// If Page is not empty then setting page
				if (currentPage != null) {
					query.setFirstResult((currentPage - 1) * pageSize);
				}
				if (pageSize != null) {
					query.setMaxResults(currentPage * pageSize);
				}
				/*
				 * 设置参数
				 */
				if (params != null) {
					for (int i = 0; i < params.length; i++) {
						query.setParameter(i, params[i]);
					}
				}
				return query.list();
			}
		});
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public List<T> searchByHqlWithListParam(final String hql, final Map<String, List> paramsMap) {
		return (List<T>) hibernateTemplate.executeFind(new HibernateCallback<List>() {
			public List doInHibernate(Session session) throws HibernateException, SQLException {
				Query query = session.createQuery(hql);
				/*
				 * 设置参数
				 */
				if (paramsMap != null && paramsMap.size() > 0) {
					for (Entry<String, List> entry : paramsMap.entrySet()) {
						query.setParameterList(entry.getKey(), entry.getValue());
					}
				}
				return query.list();
			}
		});
	}

	@Override
	public <O> List<O> findAll(final String sql, final Map<String, org.hibernate.type.Type> scalarMap, final Map<String, Object> map) {

		return (List<O>) hibernateTemplate.executeFind(new HibernateCallback<O>() {

			public O doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql.toString());
				if (null != scalarMap) {
					for (Entry<String, Type> entry : scalarMap.entrySet()) {
						query.addScalar(entry.getKey(), entry.getValue());
					}
				}
				if (map != null) {
					Set<String> keySet = map.keySet();
					for (String string : keySet) {
						Object obj = map.get(string);
						// 这里考虑传入的参数是什么类型，不同类型使用的方法不同
						if (obj instanceof Collection<?>) {
							query.setParameterList(string, (Collection<?>) obj);
						} else if (obj instanceof Object[]) {
							query.setParameterList(string, (Object[]) obj);
						} else {
							query.setParameter(string, obj);
						}
					}
				}
				return (O) query.list();
			}
		});
	}

	@Override
	public Serializable insertSave(T entity) {
		// TODO Auto-generated method stub
		return hibernateTemplate.save(entity);
	}

	@Override
	public void evict(T t) {
		hibernateTemplate.evict(t);
	}

	@Override
	public void flush() {
		hibernateTemplate.flush();

	}

	@Override
	public void refresh(T t) {
		hibernateTemplate.refresh(t);
	}
}
