package com.tyt.base.filter;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.tyt.equipment.service.IDealLogService;

/**
 * Servlet Filter implementation class LegalRequestFilter
 */
@Component("filter1")
public class ValidateSessionFilter implements Filter {
	private static final Logger logger = LoggerFactory.getLogger(ValidateSessionFilter.class);
	private static Pattern excludePage = Pattern
			.compile("(.*\\.css|.*\\.js|.*\\.jpg|.*\\.gif|.*\\.png|.*\\.bmp|.*\\.jpeg|.*\\.gzjs|.*\\.gzcss|.*\\.htm?|.*\\.html?|.*\\.xml|.*\\.jspa|.*\\.json|.*\\.swf)");//设置不过滤url请求的正则表达式
    public static final ResourceBundle p=ResourceBundle.getBundle("server_url");  


	@Resource
	private IDealLogService dealLogService;
	public void doFilter(ServletRequest arg0, ServletResponse arg1,
			FilterChain chain) throws IOException, ServletException {

		HttpServletResponse response = (HttpServletResponse) arg1;
		response.setCharacterEncoding("UTF-8");
		HttpServletRequest request = (HttpServletRequest) arg0;
		request.setCharacterEncoding("UTF-8");
//		String servletPath = request.getServletPath();
/*		Object obj = cacheService.getObject(Constant.WEB_CACHE_LOGIN_USER_ID
				+ request.getSession().getId());*/

		// 注释 kafka未使用程序及减少日志输出
//		Map<String, String> reqMap = parseRequestParams(request);
//		logger.info("==logdeal=0=args===="+reqMap.toString());
//		logger.info("==logdeal=0=="+System.currentTimeMillis());
//		String uri = request.getRequestURI();
//		if (!excludePage.matcher(uri).matches()&&StringUtils.equals("1", getString("LOG"))) {
//			dealLogService.productLog(request,response);
//		}
//		logger.info("==logdeal=1=="+System.currentTimeMillis());
//		Map<String, String> reqMap1 = parseRequestParams(request);
//		logger.info("==logdeal=1=args===="+reqMap1.toString());
		chain.doFilter(request, response);


	}

	
	public void destroy() {

	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		// TODO Auto-generated method stub
		
	}
    public static String getString(String key){
    	Object obj = p.getObject(key);// p.get(key);
    	String value = obj!=null?obj.toString():"";
    	return value;
    }
    protected Map<String, String> parseRequestParams(HttpServletRequest request) {
		Map<String, String[]> requestParams = request.getParameterMap();
		Map<String, String> params = new HashMap<String, String>();
		for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i].trim() : valueStr + values[i].trim() + ",";
			}
			// 乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
			// valueStr = new String(valueStr.getBytes("iso-8859-1"),"UTF-8");
			params.put(name, valueStr);
		}
		return params;
    }
}
