package com.tyt.base.enumConstant;

/**
 * Created by duanwc on 2019/12/26.
 *
 * 操作状态返回码
 */
public enum ReturnCodeEnum {

    CODE_200(200, "操作成功"),
    CODE_201(201, "请进行短信验证"),
    CODE_500(500, "服务器错误"),
    CODE_510(510, "服务器返回NULL"),
    CODE_400(400, "输入有误，请更正重试"),
    CODE_401(401, "不在白名单"),
    CODE_402(402, "基础参数错误"),
    CODE_405(405, "输入有误，请更正重试"),
    CODE_408(408, "网络繁忙，请稍候重试"),
    CODE_431(431, "身份验证错误"),
    CODE_433(433, "无效签名"),

    STARTPROVINCE_EMPTY_ERROR(2001, "出发地省不能为空！"),
    STARTCITY_EMPTY_ERROR(2002, "出发地市不能为空！"),
    STARTAREA_EMPTY_ERROR(2003, "出发地区县不能为空！"),
    DESTPROVINCE_EMPTY_ERROR(2004, "目的地省不能为空！"),
    DESTCITY_EMPTY_ERROR(2005, "目的地市不能为空！"),
    DESTAREA_EMPTY_ERROR(2006, "目的地区县不能为空！"),
    GOODSNAME_EMPTY_ERROR(2007, "货物描述不能为空！"),
    GOODSWEIGHT_EMPTY_ERROR(2008, "重量不能为空！"),
    CARRYPRICE_EMPTY_ERROR(2009,"参考价获取为空！"),
    FEEDPRICE_PRICE_EMPTY_ERROR(2011,"请输入建议价格！"),
    FEEDPRICE_VIEW_EMPTY_ERROR(2012,"请输入反馈意见！"),
    FEEDPRICE_VIEW_MIN_ERROR(2013,"文本至少输入10个汉字！");


    private int code;
    private String msg;

    ReturnCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
