package com.tyt.base.enumConstant;

import com.tyt.service.common.entity.ResponseCode;

/**
 * <AUTHOR>
 * @version 5920
 * @date 2019/7/18
 */
public enum ResponseCodeEnum {

    成功(200, "成功"),
    错误(500, "错误"),
    参数错误(1001, "参数错误"),
    未登录(1003, "未登录"),
    无此权益(1010, "无此权益"),
    需要升级(2006, "需求升级"),
    其他客户端登录(1004, "其他客户端登录"),
    货物信息有敏感词(7002, "货物信息有敏感词"),
    备注信息有敏感词(7002, "备注信息有敏感词"),

    transport_top_limit_time(500011, "该货源已刷新"),//刷新间隔过于频繁
    transport_max_top(500012, "该货源今日已到达刷新上限～"),
    transport_top_use_up(500013, "您的刷新权益已用尽，您可通过线上履约获得更多曝光机会"),

    ;
    private int code;
    private String msg;

    ResponseCodeEnum(int code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public ResponseCodeEnum getResponseCodeEnum(int code) {
        for (ResponseCodeEnum codeEnum : ResponseCodeEnum.values()) {
            if (codeEnum.code == code) {
                return codeEnum;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ResponseCode info(){
        ResponseCode respCode = new ResponseCode(this.code, this.msg);
        return respCode;
    }
    public ResponseCode info(String msg){
        ResponseCode respCode = new ResponseCode(this.code, msg);
        return respCode;
    }
}
