package com.tyt.base.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.OpLog;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytNoticePopupTempl;
import com.tyt.model.User;
import com.tyt.user.service.OpLogService;
import com.tyt.user.service.VersionService;
import com.tyt.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.PropertyEditorSupport;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:23
 */
public class BaseController {

    @Resource(name = "opLogService")
    private OpLogService opLogService;

    @Resource(name = "cacheServiceMcImpl")
    protected CacheService cacheService;

    @Resource(name = "versionService")
    protected VersionService versionService;

    /**
     * 图片文件后缀
     */
    private static final Set<String> img_suffix_set = new HashSet<>();
    static {

        img_suffix_set.add(".bmp");
        img_suffix_set.add(".gif");
        img_suffix_set.add(".jpeg");
        img_suffix_set.add(".jpg");
        img_suffix_set.add(".png");

        img_suffix_set.add("image");
        img_suffix_set.add("bmp");
        img_suffix_set.add("gif");
        img_suffix_set.add("jpeg");
        img_suffix_set.add("jpg");
        img_suffix_set.add("png");

    }

    /**
     * 表单日期格式数据处理
     *
     * @param binder
     */
    @InitBinder
    public void binder(WebDataBinder binder) {
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            public void setAsText(String value) {
                try {
                    Date parsedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(value);
                    setValue(new Date(parsedDate.getTime()));
                } catch (ParseException e) {
                    try {
                        Date parsedDate = new SimpleDateFormat("yyyy-MM-dd").parse(value);
                        setValue(new Date(parsedDate.getTime()));
                    } catch (ParseException e1) {
                        try { // 处理是否时间戳
                            long time = Long.parseLong(value);
                            if (value.length() >= 10 && value.length() <= 13) {
                                setValue(new Date(time));
                            } else {
                                throw new NumberFormatException();
                            }
                        } catch (NumberFormatException e2) {
                            setValue(null);
                        }
                    }
                }
            }
        });
    }

    // protected Log logger = LogFactory.getLog(this.getClass());
    public Logger logger = LoggerFactory.getLogger(this.getClass());

    static {
        System.out.println(TimeZone.getDefault()); //输出当前默认时区
        final TimeZone zone = TimeZone.getTimeZone("GMT+8"); //获取中国时区
        TimeZone.setDefault(zone); //设置时区
        System.out.println(TimeZone.getDefault()); //输出验证
    }


    /**
     * 输入JSON数据
     *
     * @param servletRequest
     * @param servletResponse
     * @param obj
     */
    protected void printJSON(HttpServletRequest servletRequest,
                             HttpServletResponse servletResponse, Object obj) {
        PrintWriter writer = null;
        try {
//        	servletResponse.setHeader("Content-Encoding","gzip");
//        	servletResponse.setHeader("Content-Type","application/gzip");
//        	servletResponse.setContentType("application/gzip");
            servletResponse.setContentType("text/html;charset=UTF-8");
            servletResponse.setCharacterEncoding("UTF-8");
            writer = servletResponse.getWriter();
            String jsoncallback = servletRequest.getParameter("jsoncallback");
            if (null == jsoncallback || jsoncallback.isEmpty()) {
                writer.print(JSON.toJSONString(obj));
            } else {
                writer.print(jsoncallback + "(" + JSON.toJSONString(obj) + ")");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("输出信息失败！" + e.getMessage());
        } finally {
            if (null != writer) {
                writer.close();
            }
        }
    }

    /**
     * 向页面输出信息
     *
     * @param info
     */
    protected void writeInfo(HttpServletResponse servletResponse, String info) {
        PrintWriter writer = null;
        try {
            servletResponse.setContentType("text/html;charset=UTF-8");
//        	servletResponse.setHeader("Content-Encoding","gzip");
//        	servletResponse.setContentType("application/gzip");

            servletResponse.setCharacterEncoding("UTF-8");
            writer = servletResponse.getWriter();
            writer.print(info);
        } catch (Exception e) {
            logger.error("输出信息失败！" + e.getMessage());
        } finally {
            if (null != writer) {
                writer.close();
            }
        }
    }

    /**
     * token验证
     */
    protected boolean validateToken(String argument, String token, HttpServletRequest request, HttpServletResponse response) {

        String md5 = Encoder.md5(argument + AppConfig.getProperty("tyt.private.key"));
        if (token != null && token.equalsIgnoreCase("CharacterEncodingFilter")) {
            return true;
        }
        if (!md5.equalsIgnoreCase(token)) {
            ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR, "Token Error.");
            printJSON(request, response, msgBean);
            return false;
        }
        return true;
    }

    /**
     * 参数
     *
     * @param request
     * @param response
     * @param path
     * @param obj
     */
    protected boolean validateArguments(String condition, HttpServletRequest request, HttpServletResponse response, Map<String, String> params, List<String> names) throws Exception {
        //判断参数存在否？
        for (String name : names) {
            if (!params.containsKey(name) || !isNull(params.get(name))) {
                ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, name + "参数问题");
                printJSON(request, response, msgBean);
                return false;
            }

        }
        return true;
    }

    /**
     * 参数
     *
     * @param request
     * @param response
     * @param path
     * @param obj
     */
    protected boolean smsLoginValidateArguments(String condition, HttpServletRequest request, HttpServletResponse response, Map<String, String> params, List<String> names) throws Exception {
        //判断参数存在否？
        for (String name : names) {
            if (!params.containsKey(name) || !isNull(params.get(name))) {
                ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, name + "参数问题");
                printJSON(request, response, msgBean);
                return false;
            }

        }
        return true;
    }

    /**
     * 判空
     *
     * @param obj
     * @return
     */
    private static boolean isNull(String obj) {
        return StringUtils.hasLength(obj);
    }
    /**
     * 得到参数为空具体的error message
     * @param path
     * @param obj
     * @return
     */
//	private String getArgReturnCode(String path,String name){
//		switch(name){
//
//			case "cellPhone":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_CELLPHONE_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_CELLPHONE_IS_NULL_MSG;
//			case "tel":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_TEL_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_TEL_IS_NULL_MSG;
//			case "password":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_PASSWORD_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_PASSWORD_IS_NULL_MSG;
//			case "oldPassword":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_PASSWORD_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_PASSWORD_IS_NULL_MSG;
//			case "newPassword":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_PASSWORD_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_PASSWORD_IS_NULL_MSG;
//			case "userSign":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_USERSIGN_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_USERSIGN_IS_NULL_MSG;
//			case "title":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_ADVICE_TITLE_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_ADVICE_TITLE_IS_NULL_MSG;
//			case "content":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_ADVICE_CONTENT_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_ADVICE_CONTENT_IS_NULL_MSG;
//			case "identity":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_USER_IDENTITY_IDENTITY_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_USER_IDENTITY_IDENTITY_IS_NULL_MSG;
//			case "realName":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_USER_IDENTITY_REALNAME_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_USER_IDENTITY_REALNAME_IS_NULL_MSG;
//			case "mainurl":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_USER_IDENTITY_MAINURL_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_USER_IDENTITY_MAINURL_IS_NULL_MSG;
//			case "backurl":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_USER_IDENTITY_BACKURL_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_USER_IDENTITY_BACKURL_IS_NULL_MSG;
//			case "tsId":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_ID_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_ID_IS_NULL_MSG;
//			case "id":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_ID_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_ID_IS_NULL_MSG;
//			case "distance":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_DISTANCE_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_DISTANCE_IS_NULL_MSG;
//			case "startCoordX":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_STARTCOORDX_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_STARTCOORDX_IS_NULL_MSG;
//			case "startCoordY":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_STARTCOORDY_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_STARTCOORDY_IS_NULL_MSG;
//			case "destCoordX":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_DESTCOORDX_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_DESTCOORDX_IS_NULL_MSG;
//			case "destCoordY":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_DESTCOORDY_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_DESTCOORDY_IS_NULL_MSG;
//			case "taskContent":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_TASKCONTENT_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_TASKCONTENT_IS_NULL_MSG;
//			case "pubTime":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_PUBTIME_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_PUBTIME_IS_NULL_MSG;
//			case "pubDate":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_PUBDATE_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_PUBDATE_IS_NULL_MSG;
//			case "startLongitude":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_STARTLONGITUDE_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_STARTLONGITUDE_IS_NULL_MSG;
//			case "startLatitude":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_STARTLATITUDE_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_STARTLATITUDE_IS_NULL_MSG;
//			case "destLatitude":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_DESTLATITUDE_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_DESTLATITUDE_IS_NULL_MSG;
//			case "destLongitude":
//				logger.info(path+ReturnCodeConstant.ARGUMENTS_DESTLONGITUDE_IS_NULL_MSG);
//				return ReturnCodeConstant.ARGUMENTS_DESTLONGITUDE_IS_NULL_MSG;
//			default:
//				logger.info(path+"未记录的参数");
//				return "参数不能为空";
//		}
//	}

    /**
     * 添加日志到数据库
     *
     * @param userId
     * @param opType
     * @param ip
     * @param platId
     * @param cellPhone
     * @param version
     * @param ticket
     * @return
     */
    protected void createLog(HttpServletRequest request, User user, Integer opType,
                             Map<String, String> params) throws Exception {
        OpLog log = new OpLog();
        log.setUserId(user.getId());
        log.setOpType(opType);
        log.setIp(StringUtil.getRealIp(request));
        log.setPlatId(Integer.parseInt(params.get("clientSign")));
        log.setCellPhone(user.getCellPhone());
        log.setTicket(user.getTicket());
        log.setVersion(params.get("clientVersion"));
        opLogService.add(log);
    }

    /**
     * 解析请求到的map集合
     *
     * @param request
     * @return
     */
    protected Map<String, String> parseRequestParams(HttpServletRequest request) throws Exception {
        Map<String, String[]> requestParams = request.getParameterMap();
        Map<String, String> params = new HashMap<String, String>();
        for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i].trim() : valueStr + values[i].trim() + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
//			valueStr = new String(valueStr.getBytes("iso-8859-1"),"UTF-8");
            params.put(name, valueStr);
        }
        return params;
    }

    /**
     * 验证Ticket
     *
     * @param ticket
     * @param userId
     * @return
     */
    protected boolean validateTicket(String ticket, Long userId) {
        String objTicketKey = UserTicketUtil.getObjTicketKey(userId.toString());
        String mcTicket = cacheService.getString(objTicketKey);
        return mcTicket.equals(ticket);
    }

    /**
     * 生成响应信息
     *
     * @param request
     * @param response
     * @param code       操作码
     * @param msg        提示信息
     * @param data       响应数据，没有不显示
     * @param encryption 是否加密0否 1是
     */
    protected void backResponse(HttpServletRequest request, HttpServletResponse response, Integer code, String msg, Object data, Integer encryption) {
        ResultMsgBean msgBean;
        if (data != null) {
            msgBean = new ResultMsgBean(code, msg, data);
        } else {
            msgBean = new ResultMsgBean(code, msg);
        }
        if (encryption == 1) {
            String jsonValue = JSON.toJSONString(msgBean);
            jsonValue = XXTea.Encrypt(jsonValue, AppConfig.getProperty("tyt.xxtea.key"));
            printJSON(request, response, jsonValue);
        } else {
            printJSON(request, response, msgBean);
        }
        return;
    }

    /**
     * 生成响应信息
     *
     * @param request
     * @param response
     * @param code       操作码
     * @param msg        提示信息
     * @param data       响应数据，没有不显示
     * @param encryption 是否加密0否 1是
     */
    protected void backResponseAddNotice(HttpServletRequest request, HttpServletResponse response, Integer code, String msg, Object data, Integer encryption, TytNoticePopupTempl noticeData) {
        ResultMsgBean msgBean;
        if (data != null) {
            if(noticeData != null){
                msgBean = new ResultMsgBean(code, msg, data, noticeData);
            }else{
                msgBean = new ResultMsgBean(code, msg, data);
            }
        } else if (noticeData != null) {
            msgBean = new ResultMsgBean(code, msg, null, noticeData);
        } else {
            msgBean = new ResultMsgBean(code, msg);
        }
        if (encryption == 1) {
            String jsonValue = JSON.toJSONString(msgBean);
            jsonValue = XXTea.Encrypt(jsonValue, AppConfig.getProperty("tyt.xxtea.key"));
            printJSON(request, response, jsonValue);
        } else {
            printJSON(request, response, msgBean);
        }
        return;
    }

    private boolean checkIsImg(String text){

        if(StringUtils.isEmpty(text)){
            return false;
        }

        text = text.toLowerCase();

        for(String oneText : img_suffix_set){
            if(text.contains(oneText)){
                return true;
            }
        }
        return false;
    }

    /**
     * 重命名图片
     *
     * @param pic      待上传的图片
     * @param typeName 文件保存分目录名称
     * @return
     */
    protected String renamePic(MultipartFile pic, String typeName) {
//			String fileSeparator=System.getProperty("file.separator");//获取系统文件分隔符
        String domainurl = "/data/pictures/" + typeName + "/";//获取文件路径
        CreateFileUtil.createDir(AppConfig.getProperty("picture.path.domain") + domainurl);
        return domainurl + ImageUtil.renameFile(pic.getOriginalFilename());
    }

    public boolean isAllImg(MultipartFile ... fileArray) {
        boolean imgFlag = true;

        for(MultipartFile oneFile : fileArray){
            if(oneFile == null){
                //跳过空文件
                continue;
            }

            String originalFileName = oneFile.getOriginalFilename();

            String lowerFileName = originalFileName.toLowerCase();

            String fileSuffix = CommonUtil.getFileSuffix(lowerFileName);

            if(!img_suffix_set.contains(fileSuffix)){

                String contentType = oneFile.getContentType();

                if(this.checkIsImg(contentType)){
                    //是图片
                    logger.info("conteng-type : " + contentType);
                }else {
                    imgFlag = false;
                    break;
                }
            }
        }

        return imgFlag;
    }

}
