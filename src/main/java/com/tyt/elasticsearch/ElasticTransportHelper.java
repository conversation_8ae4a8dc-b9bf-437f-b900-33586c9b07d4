package com.tyt.elasticsearch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.tyt.model.Transport;
import com.tyt.service.common.elasticserarch.ElasticsearchUtil;
import com.tyt.service.common.elasticserarch.TransformUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 * Created by duanwc on 2018/03/09.
 */
public class ElasticTransportHelper {

    private static final Logger logger = LoggerFactory.getLogger(ElasticTransportHelper.class);

    /**
     * transport 索引库
     */
    private final static String indexDatabase = "transport";

    /**
     * 通过ID查询ES DOC
     * @param id
     * @return
     */
    public static Transport getById(Long id) {
        String resJson = ElasticsearchUtil.queryString(indexDatabase + "_*", "q=id:" + id);
        logger.debug("es getById res : {}",resJson);
        String res = ElasticsearchUtil.parseRowsString(resJson);
        Transport transport = JSON.parseObject(res, Transport.class);
        logger.debug("es getById transport : {}",transport);
        return transport;
    }

    /**
     * 通过sql查询列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param from 开始位置
     * @param size 条数
     * @param params 参数
     * @return
     */
    public static List<Transport> findList(String sql, Integer from, Integer size, Object... params) {
        String dsl = "";
        List<Transport> list = null;
        try {
            // 查询要使用到ES语句的wildcard，需要使用到keyword查询，需要对内容进行字符串转换
            sql = sql.replaceAll("task_content", "task_content.keyword");
            dsl = TransformUtil.sqlToEsQuery(sql, params);
            String resJson = ElasticsearchUtil.queryWithDsl(indexDatabase + "_*", dsl, from, size);
            logger.debug("es findList res : {}",resJson);
            list = JSON.parseArray(ElasticsearchUtil.parseRowListString(resJson), Transport.class);
            logger.debug("es findList transport size : {}",list.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 通过sql查询当天货源列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param from 开始位置
     * @param size 条数
     * @param params 参数
     * @return
     */
    public static List<Transport> findTodayList(String sql, Integer from, Integer size, Object... params ) {
        String dsl = "";
        List<Transport> list = null;
        try {
            // 查询要使用到ES语句的wildcard，需要使用到keyword查询，需要对内容进行字符串转换
            sql = sql.replaceAll("task_content", "task_content.keyword");
            dsl = TransformUtil.sqlToEsQuery(sql, params);
            String today = TimeUtil.formatDate_(TimeUtil.today());
            String resJson = ElasticsearchUtil.queryWithDsl(indexDatabase + "_"+today, dsl, from, size);
            logger.debug("es findList res : {}",resJson);
            list = JSON.parseArray(ElasticsearchUtil.parseRowListString(resJson), Transport.class);
            logger.debug("es findList transport size : {}",list.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 通过sql查询列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param params 参数
     * @return
     */
    public static List<Transport> findList(String sql, Object... params) {
        return findList(sql, null, null, params);
    }

    /**
     * 通过sql查询列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param from 开始位置
     * @param size 条数
     * @param params 参数
     * @return
     */
    public static List<Transport> findList(String sql, Integer from, Integer size, List<Object> params) {
        Object[] args = params.toArray();
        return findList(sql, from, size, args);
    }

    /**
     * 通过sql查询当天货源列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param from 开始位置
     * @param size 条数
     * @param params 参数
     * @return
     */
    public static List<Transport> findTodayList(String sql, Integer from, Integer size, List<Object> params) {
        Object[] args = params.toArray();
        return findTodayList(sql, from, size, args);
    }

    /**
     * 通过sql查询列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param params 参数
     * @return
     */
    public static List<Transport> findList(String sql, List<Object> params) {
        Object[] args = params.toArray();
        return findList(sql, null, null, args);
    }

    /**
     * 通过sql查询列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param from 开始位置
     * @param size 条数
     * @param params 参数
     * @return Map<String, Object>  共返回两个参数，rows:List<Transport>(transport列表-需强制转换)，total:Long(总条数-需强制转换)
     */
    public static Map<String, Object> findMapList(String sql, Integer from, Integer size, Object... params) {
        String dsl = "";
        List<Transport> list = null;
        Map<String, Object> mapList = new HashMap<>();
        try {
            dsl = TransformUtil.sqlToEsQuery(sql, params);
            String resJson = ElasticsearchUtil.queryWithDsl(indexDatabase + "_*", dsl, from, size);
            logger.debug("es findMapList res : {}",resJson);
            Map<String, Object> resMap = ElasticsearchUtil.parseHits(resJson);
            if (resMap != null) {
                list = JSON.parseArray(JSON.toJSONString(resMap.get("rows")), Transport.class);
                mapList.put("rows", list);
                Long total = Long.valueOf(resMap.get("total").toString());
                mapList.put("total", total);
                logger.debug("es findMapList transport size : {}",list.size());
            } else {
                logger.debug("es findMapList transport size : {}",0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mapList;
    }

    /**
     * 通过sql查询列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param params 参数
     * @return Map<String, Object>  共返回两个参数，rows:List<Transport>(transport列表-需强制转换)，total:Long(总条数-需强制转换)
     */
    public static Map<String, Object> findMapList(String sql, Object... params) {
        return findMapList(sql, null, null, params);
    }

    /**
     * 通过sql查询列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param from 开始位置
     * @param size 条数
     * @param params 参数
     * @return Map<String, Object>  共返回两个参数，rows:List<Transport>(transport列表-需强制转换)，total:Long(总条数-需强制转换)
     */
    public static Map<String, Object> findMapList(String sql, Integer from, Integer size, List<Object> params) {
        Object[] args = params.toArray();
        return findMapList(sql, from, size, args);
    }

    /**
     * 通过sql查询列表
     * @param sql sql语句，不支持where 条件不支持 别名.字段，eg: where t.id = 1；支持的格式：where id = 1
     * @param params 参数
     * @return Map<String, Object>  共返回两个参数，rows:List<Transport>(transport列表-需强制转换)，total:Long(总条数-需强制转换)
     */
    public static Map<String, Object> findMapList(String sql, List<Object> params) {
        Object[] args = params.toArray();
        return findMapList(sql, null, null, args);
    }


    public static void main(String[] args) {
//        ElasticsearchUtil.readJsonToString("transport_mapping.json");
//        String tr = "{\"androidDistance\":152057,\"clientVersion\":\"5200\",\"ctime\":1519984201000,\"destArea\":\"龙海市\",\"destCity\":\"漳州市\",\"destCoord\":\"583.15,2705.43\",\"destCoordX\":58315,\"destCoordY\":270543,\"destLatitude\":2445,\"destLongitude\":11782,\"destPoint\":\"福建漳州市龙海市\",\"destProvinc\":\"福建\",\"displayType\":\"1\",\"distance\":0,\"goodNumber\":1,\"goodTypeName\":\"大大的铲车\",\"hashCode\":\"-1652858057\",\"high\":\"\",\"id\":37114713,\"infoStatus\":\"0\",\"iosDistance\":152057,\"isCar\":\"0\",\"isDisplay\":1,\"isInfoFee\":\"1\",\"isStandard\":0,\"isSuperelevation\":\"\",\"length\":\"\",\"linkman\":\"会员33670\",\"matchItemId\":10138,\"mtime\":1519984201000,\"nickName\":\"陈女士\",\"pcOldContent\":\"[3].贵州黔南州惠水县---福建漳州市龙海市 18吨30铲车\",\"platId\":2,\"pubDate\":1519961252000,\"pubQq\":33670,\"pubTime\":\"17:50:01\",\"regTime\":1460436691000,\"releaseTime\":1519961252000,\"remark\":\"\",\"resend\":20,\"resendCounts\":3,\"source\":0,\"srcMsgId\":34090485,\"startArea\":\"惠水县\",\"startCity\":\"黔南州\",\"startCoord\":\"-535.42,2892.34\",\"startCoordX\":-53542,\"startCoordY\":289234,\"startLatitude\":2613,\"startLongitude\":10665,\"startPoint\":\"贵州黔南州惠水县\",\"startProvinc\":\"贵州-测试23\",\"status\":1,\"taskContent\":\"18吨30铲车\",\"tel\":\"13658508562\",\"tsOrderNo\":\"18030200013631\",\"type\":\"30\",\"uploadCellphone\":\"13658508562\",\"userId\":33670,\"userPart\":100,\"userType\":0,\"verifyFlag\":1,\"verifyPhotoSign\":1,\"weight\":\"18\",\"wide\":\"\"}";
//        Transport transport = JSONObject.parseObject(tr, Transport.class);
//        modifyTransport(transport);

//        String sql = "select * from tyt_transport where id = ? or id = ? or id = 38062378 and info_status=0" ;
//        List<Transport> list = findList(sql, 38062376l, 38062377);
//        System.out.println(list);

//        Transport t = getById(38062376l);
//        System.out.println(t);

        String sql = "select * from tyt_transport where id = ? or id = ? or id = 38062378 and info_status=0" ;
        Map<String, Object> mapList = findMapList(sql, 38062376l, 38062377);
        System.out.println(mapList.get("total"));
        System.out.println(mapList.get("rows"));
    }
}
