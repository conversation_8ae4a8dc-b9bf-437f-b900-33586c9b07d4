package com.tyt.elasticsearch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.model.Transport;
import com.tyt.service.common.elasticserarch.ElasticsearchUtil;
import com.tyt.service.common.elasticserarch.TransformUtil;
import com.tyt.service.common.elasticserarch.xcontent.JsonXContent;

import java.io.IOException;
import java.util.*;

/**
 * Created by duanwc on 18/3/12.
 */
public class MainTest {

    private static final String tr1 = "{\"androidDistance\":68355,\"clientVersion\":\"5201\",\"ctime\":1519984200000,\"destArea\":\"天等县\",\"destCity\":\"崇左市\",\"destCoord\":\"-511.67,2555.03\",\"destCoordX\":-51167,\"destCoordY\":255503,\"destLatitude\":2308,\"destLongitude\":10713,\"destPoint\":\"广西崇左市天等县\",\"destProvinc\":\"广西\",\"displayType\":\"1\",\"distance\":0,\"goodNumber\":1,\"hashCode\":\"-416078852\",\"id\":38114710,\"infoStatus\":\"0\",\"iosDistance\":68355,\"isCar\":\"1\",\"isDisplay\":1,\"isInfoFee\":\"0\",\"isStandard\":1,\"linkman\":\"会员43615\",\"matchItemId\":-1,\"mtime\":1519984200000,\"nickName\":\"莫先生\",\"pcOldContent\":\"[3].广西贺州市八步区---广西崇左市天等县 37吨50铲车，压路机。\",\"platId\":3,\"pubDate\":1519961294000,\"pubQq\":43615,\"pubTime\":\"17:50:00\",\"regTime\":1463137438000,\"releaseTime\":1519961294000,\"remark\":\"\",\"resend\":20,\"resendCounts\":3,\"source\":0,\"srcMsgId\":34090510,\"startArea\":\"八步区\",\"startCity\":\"贺州市\",\"startCoord\":\"-53.01,2699.75\",\"startCoordX\":-5301,\"startCoordY\":269975,\"startLatitude\":2440,\"startLongitude\":11155,\"startPoint\":\"广西贺州市八步区\",\"startProvinc\":\"广西\",\"status\":1,\"taskContent\":\"37吨50铲车，压路机。\",\"tel\":\"13878446906\",\"tsOrderNo\":\"18030200013663\",\"uploadCellphone\":\"13878446906\",\"userId\":43615,\"userPart\":100,\"userType\":1,\"verifyFlag\":1,\"verifyPhotoSign\":1,\"weight\":\"37\"}";
    private static final String tr2 = "{\"androidDistance\":0,\"brand\":\"\",\"clientVersion\":\"5201\",\"ctime\":1519984201000,\"destArea\":\"大兴区\",\"destCity\":\"北京市\",\"destCoord\":\"442.56,4399.84\",\"destCoordX\":44256,\"destCoordY\":439984,\"destLatitude\":3973,\"destLongitude\":11633,\"destPoint\":\"北京市大兴区\",\"destProvinc\":\"北京\",\"displayType\":\"1\",\"distance\":0,\"goodNumber\":1,\"goodTypeName\":\"钻机\",\"hashCode\":\"1298097078\",\"id\":38114711,\"infoStatus\":\"0\",\"iosDistance\":0,\"isCar\":\"0\",\"isDisplay\":1,\"isInfoFee\":\"1\",\"isStandard\":0,\"linkman\":\"会员97803\",\"matchItemId\":10321,\"mtime\":1519984201000,\"nickName\":\"会员97803\",\"pcOldContent\":\"[3].北京市大兴区---北京市大兴区 60吨220钻机\",\"platId\":3,\"pubDate\":1519961245000,\"pubQq\":97803,\"pubTime\":\"17:50:01\",\"regTime\":1478937991000,\"releaseTime\":1519961245000,\"remark\":\"需爬梯板车一辆\",\"resend\":20,\"resendCounts\":3,\"source\":0,\"srcMsgId\":34090482,\"startArea\":\"大兴区\",\"startCity\":\"北京市\",\"startCoord\":\"442.56,4399.84\",\"startCoordX\":44256,\"startCoordY\":439984,\"startLatitude\":3973,\"startLongitude\":11633,\"startPoint\":\"北京市大兴区\",\"startProvinc\":\"北京\",\"status\":1,\"taskContent\":\"60吨220钻机\",\"tel\":\"13911867233\",\"tsOrderNo\":\"18030200013626\",\"type\":\"220\",\"uploadCellphone\":\"13911867233\",\"userId\":97803,\"userPart\":100,\"userType\":0,\"verifyFlag\":1,\"verifyPhotoSign\":1,\"weight\":\"60\"}";
    private static final String tr3 = "{\"androidDistance\":35514,\"clientVersion\":\"5200\",\"ctime\":1519984201000,\"destArea\":\"临清市\",\"destCity\":\"聊城市\",\"destCoord\":\"384.05,4080.73\",\"destCoordX\":38405,\"destCoordY\":408073,\"destLatitude\":3685,\"destLongitude\":11570,\"destPoint\":\"山东聊城市临清市\",\"destProvinc\":\"山东\",\"displayType\":\"1\",\"distance\":0,\"goodNumber\":1,\"goodTypeName\":\"\",\"hashCode\":\"200266420\",\"high\":\"\",\"id\":38114712,\"infoStatus\":\"0\",\"iosDistance\":35514,\"isCar\":\"0\",\"isDisplay\":1,\"isInfoFee\":\"1\",\"isStandard\":1,\"isSuperelevation\":\"\",\"length\":\"\",\"linkman\":\"会员291474\",\"matchItemId\":-1,\"mtime\":1519984201000,\"nickName\":\"张女士\",\"pcOldContent\":\"[3].河南商丘市夏邑县---山东聊城市临清市 4吨收割机\",\"platId\":2,\"pubDate\":1519961311000,\"pubQq\":291474,\"pubTime\":\"17:50:01\",\"regTime\":1519871812000,\"releaseTime\":1519961311000,\"remark\":\"\",\"resend\":20,\"resendCounts\":3,\"source\":0,\"srcMsgId\":34090516,\"startArea\":\"夏邑县\",\"startCity\":\"商丘市\",\"startCoord\":\"419.84,3789.58\",\"startCoordX\":41984,\"startCoordY\":378958,\"startLatitude\":3423,\"startLongitude\":11613,\"startPoint\":\"河南商丘市夏邑县\",\"startProvinc\":\"河南\",\"status\":1,\"taskContent\":\"4吨收割机\",\"tel\":\"18240765688\",\"tsOrderNo\":\"18030200013670\",\"uploadCellphone\":\"18240765688\",\"userId\":291474,\"userPart\":100,\"userType\":0,\"verifyFlag\":1,\"verifyPhotoSign\":1,\"weight\":\"4\",\"wide\":\"\"}";
//    private static String tr4 = "{\"androidDistance\":152057,\"clientVersion\":\"5200\",\"ctime\":1519984201000,\"destArea\":\"龙海市\",\"destCity\":\"漳州市\",\"destCoord\":\"583.15,2705.43\",\"destCoordX\":58315,\"destCoordY\":270543,\"destLatitude\":2445,\"destLongitude\":11782,\"destPoint\":\"福建漳州市龙海市\",\"destProvinc\":\"福建\",\"displayType\":\"1\",\"distance\":0,\"goodNumber\":1,\"goodTypeName\":\"铲车\",\"hashCode\":\"-1652858057\",\"high\":\"\",\"id\":38114713,\"infoStatus\":\"0\",\"iosDistance\":152057,\"isCar\":\"0\",\"isDisplay\":1,\"isInfoFee\":\"1\",\"isStandard\":0,\"isSuperelevation\":\"\",\"length\":\"\",\"linkman\":\"会员33670\",\"matchItemId\":10138,\"mtime\":1519984201000,\"nickName\":\"陈女士\",\"pcOldContent\":\"[3].贵州黔南州惠水县---福建漳州市龙海市 18吨30铲车\",\"platId\":2,\"pubDate\":1519961252000,\"pubQq\":33670,\"pubTime\":\"17:50:01\",\"regTime\":1460436691000,\"releaseTime\":1519961252000,\"remark\":\"\",\"resend\":20,\"resendCounts\":3,\"source\":0,\"srcMsgId\":34090485,\"startArea\":\"惠水县\",\"startCity\":\"黔南州\",\"startCoord\":\"-535.42,2892.34\",\"startCoordX\":-53542,\"startCoordY\":289234,\"startLatitude\":2613,\"startLongitude\":10665,\"startPoint\":\"贵州黔南州惠水县\",\"startProvinc\":\"贵州-测试23\",\"status\":1,\"taskContent\":\"18吨30铲车\",\"tel\":\"13658508562\",\"tsOrderNo\":\"18030200013631\",\"type\":\"30\",\"uploadCellphone\":\"13658508562\",\"userId\":33670,\"userPart\":100,\"userType\":0,\"verifyFlag\":1,\"verifyPhotoSign\":1,\"weight\":\"18\",\"wide\":\"\"}";
    private static final String tr4 = "{\"androidDistance\":152057,\"clientVersion\":\"5200\",\"ctime\":1519984201000,\"destArea\":\"龙海市\",\"destCity\":\"漳州市\",\"destCoord\":\"583.15,2705.43\",\"destCoordX\":58315,\"destCoordY\":270543,\"destLatitude\":2445,\"destLongitude\":11782,\"destPoint\":\"福建漳州市龙海市\",\"destProvinc\":\"福建\",\"displayType\":\"1\",\"distance\":0,\"goodNumber\":1,\"goodTypeName\":\"铲车\",\"hashCode\":\"-1652858057\",\"high\":\"\",\"id\":37114713,\"infoStatus\":\"0\",\"iosDistance\":152057,\"isCar\":\"0\",\"isDisplay\":1,\"isInfoFee\":\"1\",\"isStandard\":0,\"isSuperelevation\":\"\",\"length\":\"\",\"linkman\":\"会员33670\",\"matchItemId\":10138,\"mtime\":1519984201000,\"nickName\":\"陈女士\",\"pcOldContent\":\"[3].贵州黔南州惠水县---福建漳州市龙海市 18吨30铲车\",\"platId\":2,\"pubDate\":1519961252000,\"pubQq\":33670,\"pubTime\":\"17:50:01\",\"regTime\":1460436691000,\"releaseTime\":1519961252000,\"remark\":\"\",\"resend\":20,\"resendCounts\":3,\"source\":0,\"srcMsgId\":34090485,\"startArea\":\"惠水县\",\"startCity\":\"黔南州\",\"startCoord\":\"-535.42,2892.34\",\"startCoordX\":-53542,\"startCoordY\":289234,\"startLatitude\":2613,\"startLongitude\":10665,\"startPoint\":\"贵州黔南州惠水县\",\"startProvinc\":\"贵州-测试23\",\"status\":1,\"taskContent\":\"18吨30铲车\",\"tel\":\"13658508562\",\"tsOrderNo\":\"18030200013631\",\"type\":\"30\",\"uploadCellphone\":\"13658508562\",\"userId\":33670,\"userPart\":100,\"userType\":0,\"verifyFlag\":1,\"verifyPhotoSign\":1,\"weight\":\"18\",\"wide\":\"\"}";
//    private static String tr4 = "{\"androidDistance\":152057,\"goodNumber\":5,\"startPoint\":\"北京\",\"startProvinc\":\"贵州-测试234\"}";

    public static void main(String[] args) {
        MainTest test = new MainTest();
//        test.saveIndex();
//        test.updateMapping();
//        test.createMapping();
//        test.saveDoc();
//        test.saveDocById();
//        test.updateDocById();
//        test.updateDocByIdLocal();
//        test.updateDocByIdLocal2();
//        test.deleteIndex();
//        test.deleteDoc();
//        test.bulkIndex();
        test.queryString();
//        test.queryStringSize();
//        test.queryStringIndex();
//        test.queryWithDsl();
//        test.queryWithAggs();
//        test.queryIndicesForSet();



        System.exit(0);
    }

    public void queryIndicesForSet(){
        Set<String> set = ElasticsearchUtil.queryIndicesForSet();
        boolean b = set.contains("transport_201802247");
        System.out.println(set);
        System.out.println(b);
    }

    public void saveIndex(){
        ElasticsearchUtil.save("transport_0313");
    }
    public void saveIndexType(){
        ElasticsearchUtil.save("transport_0313", "data");
    }

    public void updateMapping(){
//        ElasticsearchUtil.updateMapping("transport_0312", "data", ElasticsearchUtil.readJsonToString("transport_mapping.json"));
    }

    public void createMapping(){
//        ElasticsearchUtil.createMapping("transport_0312", "data", ElasticsearchUtil.readJsonToString("transport_mapping.json"));
    }

    public void saveDoc(){
        JSONObject object = JSONObject.parseObject(tr1);
        ElasticsearchUtil.save("transport_0312", "data", object);
    }
    public void saveDocById(){
        JSONObject object1 = JSONObject.parseObject(tr1);
        ElasticsearchUtil.save("transport_0312", "data", "38114710", object1);
        JSONObject object2 = JSONObject.parseObject(tr2);
        ElasticsearchUtil.save("transport_0312", "data", "38114711", object2);
        JSONObject object3 = JSONObject.parseObject(tr3);
        ElasticsearchUtil.save("transport_0312", "data", "38114712", object3);
        JSONObject object4 = JSONObject.parseObject(tr4);
        ElasticsearchUtil.save("transport_0312", "data", "38114713", object4);
    }

    public void updateDocById(){
        Object object = JSONObject.parseObject(tr4);
        ElasticsearchUtil.update("transport_20180302", "data", "37114713", object);
    }

    public void updateDocByIdLocal(){
        Transport object = JSONObject.parseObject(tr4, Transport.class);
        System.out.println(object);
        ElasticsearchUtil.update("transport_0312", "data", "38114713", object, true);
    }

    public void updateDocByIdLocal2(){
        Map map = JSONObject.parseObject(tr4, HashMap.class);
        System.out.println(map);
        ElasticsearchUtil.update("transport_0312", "data", "38114713", map);
    }

    public void deleteIndex(){
        ElasticsearchUtil.delete("transport_0312");
    }

    public void deleteDoc(){
        ElasticsearchUtil.delete("transport_0312","data", "38114711");
    }

    public void bulkIndex(){
        ElasticsearchUtil.delete("transport_0312","data", "38114710");
        ElasticsearchUtil.delete("transport_0312","data", "38114711");
        ElasticsearchUtil.delete("transport_0312","data", "38114712");
        ElasticsearchUtil.delete("transport_0312","data", "38114713");

        Transport t1 = JSONObject.parseObject(tr1, Transport.class);
        Transport t2 = JSONObject.parseObject(tr2, Transport.class);
        Transport t3 = JSONObject.parseObject(tr3, Transport.class);
        Transport t4 = JSONObject.parseObject(tr4, Transport.class);

        List<Transport> list = new ArrayList<>();
        list.add(t1);
        list.add(t2);
        list.add(t3);
        list.add(t4);

        List<Map<String, Object>> dataList = TransformUtil.objectsToMaps(list);
        System.out.println(dataList);
        ElasticsearchUtil.bulkIndex("transport_0312","data", dataList);
    }

    public void queryString(){
//        String result = ElasticsearchUtil.queryString("transport_*", "destArea:大理州&from=0&size=5&pretty");
//        Map map = ElasticsearchUtil.parseHits(result);
//        System.out.println(map);
//        System.out.println(result);


//        String result1 = ElasticsearchUtil.queryString("transport_*", "id:37987637&pretty");
//        String s1 = ElasticsearchUtil.parseHitsString(result1);
//        System.out.println(s1);
//        System.out.println(result1);

//        String s2 = ElasticsearchUtil.parseRowsString(result1);
//        System.out.println(JSON.parseObject(s2, Transport.class));

//        String result = ElasticsearchUtil.queryString("transport_*", "destArea:大理州&from=0&size=5&pretty");
//        String s3 = ElasticsearchUtil.parseRowListString(result);
//        List<Transport> list = JSON.parseArray(s3, Transport.class);
//        System.out.println(list.size());
//        System.out.println(list);

//        boolean b = ElasticsearchUtil.isExists("search");
//        System.out.println(b);

        String s = ElasticsearchUtil.queryString("transport_*", "q=id:38114624");
        System.out.println(s);
        String res = ElasticsearchUtil.parseRowsString(s);
        System.out.println(res);
        Transport transport = JSON.parseObject(res, Transport.class);
        System.out.println(JSON.toJSONString(transport));
        System.out.println(transport);

    }


    public void queryStringIndex(){
        String result = ElasticsearchUtil.queryString("transport_0312","女士", 0, 5);
        System.out.println(result);
    }

    public void queryWithDsl(){
        String body = "{\n" +
                "  \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must\": [\n" +
                "        {\n" +
                "          \"query_string\": {\n" +
                "            \"default_field\": \"_all\",\n" +
                "            \"query\": \"北京\"\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"range\": {\n" +
                "            \"ctime\": {\n" +
                "              \"gt\": \"1519388760000\"\n" +
                "            }\n" +
                "          }\n" +
                "        }\n" +
                "      ],\n" +
                "      \"must_not\": [],\n" +
                "      \"should\": []\n" +
                "    }\n" +
                "  },\n" +
                "  \"from\": 0,\n" +
                "  \"size\": 10,\n" +
                "  \"sort\": [],\n" +
                "  \"aggs\": {}\n" +
                "}";

        JsonXContent xcontent = JsonXContent.getXContent();
        try {
            try {
                xcontent.startObject()
                            .startObject("query")
                                .startObject("bool")
                                    .startArray("must")
                                        .startObject()
                                            .startObject("range")
                                                .startObject("ctime")
//                                                    .field("gt", TimeUtil.parseDateString("2018-03-02 17:48:15"), ISODateTimeFormat.dateTime().withZone(DateTimeZone.forTimeZone(TimeZone.getTimeZone("GMT+:08:00"))))
//                                                    .field("lt", TimeUtil.parseDateString("2018-03-02 17:48:15"), ISODateTimeFormat.dateTime().withZone(DateTimeZone.forTimeZone(TimeZone.getTimeZone("GMT+:08:00"))))
//                                                    .field("gt", TimeUtil.parseDateString("2018-03-02 17:48:15"), JsonXContent.DEFAULT_DATE_PRINTER)
//                                                    .field("lt", TimeUtil.parseDateString("2018-03-02 17:48:15"), JsonXContent.DEFAULT_DATE_PRINTER)
                                                    .field("gt", "2018-03-02T17:48:13.000+0800")
                                                    .field("lt", "2018-03-02T17:48:15.000+0800")
                                                .endObject()
                                            .endObject()
                                        .endObject()
                                    .endArray()
                                    .startArray("must_not")
                                    .endArray()
                                    .startArray("should")
                                    .endArray()
                                .endObject()
                            .endObject()
                            .field("from", 0)
                            .field("size", 10)
                        .endObject();
            } catch (Exception e) {
                e.printStackTrace();
            }

            String s= xcontent.string();
            body = s;
            System.out.println(s);
        } catch (IOException e) {
            e.printStackTrace();
        }

        String result = null;
        try {
            result = ElasticsearchUtil.queryWithDsl("transport_*",body,0, 10);

            System.out.println(result);
            System.out.println(ElasticsearchUtil.parseRowListString(result));
            List<Transport> tr = JSON.parseArray(ElasticsearchUtil.parseRowListString(result), Transport.class);
            System.out.println("tr="+tr);
            System.out.println(tr.get(0).getCtime());


//            result = ElasticsearchUtil.queryWithDsl("transport_*",body,0, 10);
//            System.out.println(result);
////            Map<String, Object> map = Elasticsearch2XUtil.parseHits(result);
//            Map<String, Object> map = ElasticsearchUtil.groupByType(result, 0, 10);
//            System.out.println("===="+map);
//            System.out.println(JSON.toJSONString(map));
//
//            Map<String, Object> map1 = ElasticsearchUtil.getTypeDetails(result,"transport_20180226","repertory", 0, 10);
//            System.out.println("==="+map1);
//            System.out.println(JSON.toJSONString(map1));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    public void queryWithAggs(){
//        String result = null;
//        try {
//            result = Elasticsearch2XUtil.queryWithAggs("北京", "transport_*", "","destArea", 10);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        System.out.println(result);
//    }
}
