package com.tyt.scar.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScarRecruitBean {
	private Long id;
	private String title;
	private String telName;
	private String telephone;
	private String salary;
	private Integer salaryCode;
	private Integer deviceTypeOneCode;
	private String deviceTypeOne;
	private Integer deviceTypeTwoCode;
	private String deviceTypeTwo;
	private Integer formatCode;
	private String format;
	private String province;
	private String city;
	private String county;
	private String remark;
	private Date publishTime;
	private Long userId;
	private Integer readNbr;
	private Integer isCollect;
	/* 在30天内是否发布过大板车求职信息，1：发布过 2：没有发布过 */
	private Integer hasPublished;

	public Integer getHasPublished() {
		return hasPublished;
	}

	public void setHasPublished(Integer hasPublished) {
		this.hasPublished = hasPublished;
	}

	public Integer getIsCollect() {
		return isCollect;
	}

	public void setIsCollect(Integer isCollect) {
		this.isCollect = isCollect;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTelName() {
		return telName;
	}

	public void setTelName(String telName) {
		this.telName = telName;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getSalary() {
		return salary;
	}

	public void setSalary(String salary) {
		this.salary = salary;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getPublishTime() {
		return publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	public Integer getSalaryCode() {
		return salaryCode;
	}

	public void setSalaryCode(Integer salaryCode) {
		this.salaryCode = salaryCode;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getDeviceTypeOneCode() {
		return deviceTypeOneCode;
	}

	public void setDeviceTypeOneCode(Integer deviceTypeOneCode) {
		this.deviceTypeOneCode = deviceTypeOneCode;
	}

	public String getDeviceTypeOne() {
		return deviceTypeOne;
	}

	public void setDeviceTypeOne(String deviceTypeOne) {
		this.deviceTypeOne = deviceTypeOne;
	}

	public Integer getDeviceTypeTwoCode() {
		return deviceTypeTwoCode;
	}

	public void setDeviceTypeTwoCode(Integer deviceTypeTwoCode) {
		this.deviceTypeTwoCode = deviceTypeTwoCode;
	}

	public String getDeviceTypeTwo() {
		return deviceTypeTwo;
	}

	public void setDeviceTypeTwo(String deviceTypeTwo) {
		this.deviceTypeTwo = deviceTypeTwo;
	}

	public Integer getFormatCode() {
		return formatCode;
	}

	public void setFormatCode(Integer formatCode) {
		this.formatCode = formatCode;
	}

	public String getFormat() {
		return format;
	}

	public void setFormat(String format) {
		this.format = format;
	}

	public Integer getReadNbr() {
		return readNbr;
	}

	public void setReadNbr(Integer readNbr) {
		this.readNbr = readNbr;
	}

}
