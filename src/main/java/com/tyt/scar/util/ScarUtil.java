package com.tyt.scar.util;

import com.tyt.model.ResultMsgBean;
import com.tyt.util.Encoder;
import com.tyt.util.ReturnCodeConstant;

public class ScarUtil {
	
	
	/**
	 * 生成md5, 字段排序:按字母从小到大
	 * age+city+county+dutyCode+province+remark+userId+salaryCode
	 * +telephone+telName+title+years
	 * 
	 * @param bCar
	 * @return
	 */
	public static String createMd5(Integer age, String city, String county,
			Integer deviceTypeOneCode,Integer deviceTypeTwoCode,Integer formatCode,
			String province, 
			String remark, Long userId,
			Integer salaryCode, String telephone, String telName, String title,
			Integer years) {
		StringBuffer md5Str = new StringBuffer("");
		if (age != null) {
			md5Str.append(age);
		}
		md5Str.append(city);
		md5Str.append(county);
		md5Str.append(deviceTypeOneCode);
		md5Str.append(deviceTypeTwoCode);
		md5Str.append(formatCode);
		md5Str.append(province);
		if (remark != null && !remark.trim().equals("")) {
			md5Str.append(remark);
		}
		md5Str.append(userId);
		md5Str.append(salaryCode);
		md5Str.append(telephone);
		md5Str.append(telName);
		md5Str.append(title);
		if (years != null) {
			md5Str.append(years);
		}
		String bCarMd5 = Encoder.md5(md5Str.toString());
		return bCarMd5;
	}
	
	public static boolean validateParams(ResultMsgBean rm, String title,
			String telName,String telePhone,  String province, String city,
			String county, Integer deviceTypeOneCode,Integer deviceTypeTwoCode, 
			Integer formatCode,Integer salaryCode
			) {
		if (title == null || title.trim().equals("")) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("title不能为空");
			return false;
		}
		if (telName == null || telName.trim().equals("")) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("telName不能为空");
			return false;
		}
		if (deviceTypeOneCode == null) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("deviceTypeOneCode不能为空");
			return false;
		}
		if (deviceTypeTwoCode == null) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("deviceTypeTwoCode不能为空");
			return false;
		}
//		if (formatCode == null) {
//			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
//			rm.setMsg("formatCode不能为空");
//			return false;
//		}
		if (province == null || province.trim().equals("")) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("province不能为空");
			return false;
		}
		if (city == null || city.trim().equals("")) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("city不能为空");
			return false;
		}
		if (county == null || county.trim().equals("")) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("county不能为空");
			return false;
		}
		if (salaryCode == null) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("salaryCode不能为空");
			return false;
		}
		if (telePhone == null || telePhone.trim().equals("")) {
			rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			rm.setMsg("telephone不能为空");
			return false;
		}
		return true;
	}

}
