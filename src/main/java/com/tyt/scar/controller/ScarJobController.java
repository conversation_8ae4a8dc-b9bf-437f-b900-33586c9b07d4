package com.tyt.scar.controller;

import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.bcar.service.BcarJobService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytScarJob;
import com.tyt.scar.bean.ScarJobBean;
import com.tyt.scar.bean.ScarJobQueryBean;
import com.tyt.scar.bean.ScarTopBean;
import com.tyt.scar.service.ScarJobService;
import com.tyt.scar.util.ScarUtil;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/scar/job")
public class ScarJobController extends BaseController {
	private static final int JOB_STATUS_OPEN = 0;
	private static final int JOB_STATUS_CLOSE = 1;
	/* 设备求职收藏状态码 */
	private static final int SJOB_STATUS = 4;
	@Resource(name = "sCarJobService")
	ScarJobService sCarJobService;
	@Resource(name = "bCarJobService")
	BcarJobService bCarJobService;

	/**
	 * APP保存求职信息
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/save")
	@ResponseBody
	public ResultMsgBean saveScarJob(BaseParameter baseParameter, TytScarJob sCar) {

		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 必填项验证
			if (!ScarUtil.validateParams(rm, sCar.getTitle(), sCar.getTelName(), sCar.getTelephone(), sCar.getProvince(), sCar.getCity(), sCar.getCounty(), sCar.getDeviceTypeOneCode(), sCar.getDeviceTypeTwoCode(), sCar.getFormatCode(), sCar.getSalaryCode()))

				return rm;
			// 发布
			if (!sCarJobService.addScarJob(sCar, baseParameter.getClientSign(), baseParameter.getClientVersion())) {
				rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
				rm.setMsg("您已经发布过此条信息!");
				return rm;
			} else {
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("发布成功");
				return rm;
			}

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	/**
	 * APP编辑求职
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/update")
	@ResponseBody
	public ResultMsgBean updateBcarJob(BaseParameter baseParameter, TytScarJob sCar) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 必填项验证
			if (sCar.getId() == null || sCar.getId().longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职id不能为空");
				return rm;
			}
			// 老数据
			TytScarJob sCarOld = sCarJobService.getById(sCar.getId());
			if (sCarOld == null) {
				rm.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
				rm.setMsg("非法信息编辑");
				return rm;
			}
			// 必填项验证
			if (!ScarUtil.validateParams(rm, sCar.getTitle(), sCar.getTelName(), sCar.getTelephone(), sCar.getProvince(), sCar.getCity(), sCar.getCounty(), sCar.getDeviceTypeOneCode(), sCar.getDeviceTypeTwoCode(), sCar.getFormatCode(), sCar.getSalaryCode()))
				return rm;
			if (sCarJobService.updateBcarJob(sCarOld, sCar, baseParameter.getClientSign(), baseParameter.getClientVersion())) {
				// 更新认证状态和开放状态
				sCarJobService.updateVerifyAndOpenStatus(sCar.getId());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("修改成功");
				return rm;
			} else {
				rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
				rm.setMsg("您已经发布过此条信息!");
				return rm;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	/**
	 * APP查询求职详情
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping("/info")
	@ResponseBody
	public ResultMsgBean getScarJobInfo(Long id, Long userId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (id == null || id.longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职id不能为空");
				return rm;
			}
			// 查询信息
			ScarJobBean sCarJob = sCarJobService.updateRead(id, userId);
			boolean isHasCollected = bCarJobService.isHasColleced(userId + "", id + "", 4);
			sCarJob.setIsCollect(isHasCollected ? 0 : 1);
			// 查询用户在30天内是否发布过板车招聘信息
			boolean isHasPublished = sCarJobService.isHasPublishedInDays(userId, 30);
			sCarJob.setHasPublished(isHasPublished ? 1 : 2);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("获取成功");
			rm.setData(sCarJob);
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * APP删除求职信息
	 * 
	 * @param id
	 */
	@RequestMapping("/del")
	@ResponseBody
	public ResultMsgBean delScarJob(Long id) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (id == null || id.longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职id不能为空");
				return rm;
			}
			sCarJobService.delScarJob(id, 1);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("删除成功");
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * App求职置顶
	 * 
	 * @param id
	 */
	@RequestMapping("/top")
	@ResponseBody
	public ResultMsgBean updateScarJobTop(Long id) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (id == null || id.longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职id不能为空");
				return rm;
			}
			ScarTopBean sCarTopBean = sCarJobService.updateScarJobTop(id);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("信息排位已经提前");
			rm.setData(sCarTopBean);
			return rm;
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * APP查询招聘列表接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter, Integer queryType, Long querySign, String province, String city, String county, String deviceTypeOneCode, String deviceTypeTwoCode, String formatCode) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
				List<ScarJobQueryBean> list = sCarJobService.query(province, city, county, deviceTypeOneCode, deviceTypeTwoCode, formatCode, queryType.intValue(), querySign == null ? 0 : querySign.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * APP我的求职列表接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/myPublish")
	@ResponseBody
	public ResultMsgBean myPublish(BaseParameter baseParameter, Integer queryType, Long querySign) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
				List<ScarJobQueryBean> list = sCarJobService.myScarJobList(baseParameter.getUserId(), queryType.intValue(), querySign == null ? 0 : querySign.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private boolean checkQueryParameter(Integer queryType, Long querySign, ResultMsgBean rm) {
		if (queryType == null || (queryType.intValue() != 0 && queryType.intValue() != 1 && queryType.intValue() != 2)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询类型不正确！");
			return false;
		} else if (queryType.intValue() != 1 && (querySign == null || querySign.longValue() < 0)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询标识错误，最大、最小ID为空！");
			return false;
		}
		return true;

	}

	@RequestMapping("collect")
	@ResponseBody
	public ResultMsgBean collect(String userId, String id, String type) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(id)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职信息id不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(type) || (!"1".equals(type) && !"2".equals(type))) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("操作类型type不能为空且只能1和2");
				return rm;
			}
			// 检测用户是否已经收藏了该求职信息
			boolean isHasCollected = bCarJobService.isHasColleced(userId, id, 4);
			/*
			 * 1：收藏 2：取消收藏
			 */
			switch (Integer.valueOf(type)) {
			case 1:
				if (isHasCollected) {
					rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
					rm.setMsg("已收藏过该求职信息，不能重复收藏");
				} else {
					// 保存收藏求职信息
					bCarJobService.saveCollect(userId, id, 4);
				}
				break;
			case 2:
				if (!isHasCollected) {
					rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
					rm.setMsg("您还未收藏该求职信息，不能取消收藏");
				} else {
					// 取消收藏求职信息
					bCarJobService.deleteCollect(userId, id, 4);
				}
				break;
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}

	@RequestMapping("changeStatus")
	@ResponseBody
	public ResultMsgBean changeStatus(String userId, String id, String type) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(id)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("信息id不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(type) || (!"1".equals(type) && !"2".equals(type))) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("操作类型type不能为空且只能1和2");
				return rm;
			}
			// 查询求职信息是否属于用户
			boolean isBlongUser = sCarJobService.isBlongUser(userId, id);
			if (isBlongUser) {
				// 获取求职信息是否处于开放状态
				boolean isOpen = sCarJobService.isJobOpen(id, JOB_STATUS_OPEN);
				/*
				 * 1：开放 2：关闭
				 */
				switch (Integer.valueOf(type)) {
				case 1:
					if (isOpen) {
						rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
						rm.setMsg("该信息已经处于开放状态");
					} else {
						// 开放信息
						sCarJobService.updateJobStatus(id, JOB_STATUS_OPEN);
					}
					break;
				case 2:
					if (!isOpen) {
						rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
						rm.setMsg("该信息已经处于关闭状态");
					} else {
						// 开放信息
						sCarJobService.updateJobStatus(id, JOB_STATUS_CLOSE);
					}
					break;
				}
			} else {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("只能操作自己发布的信息");
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}

	@RequestMapping("mulUncollect")
	@ResponseBody
	public ResultMsgBean mulUncollect(String userId, String ids) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(ids)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("信息ids不能为空");
				return rm;
			}
			/*
			 * ids如果为-1则取消全部，否则按照指定的id集合进行操作
			 */
			if ("-1".equals(ids)) {
				sCarJobService.updateCollectStatus(userId, SJOB_STATUS);
			} else {
				// 按照id集合取消收藏
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}
}
