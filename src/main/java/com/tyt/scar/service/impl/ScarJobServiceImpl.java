package com.tyt.scar.service.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytSequenceService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.TytScarJob;
import com.tyt.model.TytScarJobUser;
import com.tyt.model.TytSource;
import com.tyt.model.User;
import com.tyt.scar.bean.ScarJobBean;
import com.tyt.scar.bean.ScarJobQueryBean;
import com.tyt.scar.bean.ScarTopBean;
import com.tyt.scar.service.ScarJobService;
import com.tyt.scar.service.ScarJobUserService;
import com.tyt.scar.util.ScarUtil;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSourceUtil;

@SuppressWarnings("deprecation")
@Service("sCarJobService")
public class ScarJobServiceImpl extends BaseServiceImpl<TytScarJob, Long> implements ScarJobService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "userService")
	UserService userService;

	@Resource(name = "tytSequenceService")
	TytSequenceService tytSequenceService;

	@Resource(name = "sCarJobUserService")
	ScarJobUserService sCarJobUserService;

	@Resource(name = "sCarJobDao")
	public void setBaseDao(BaseDao<TytScarJob, Long> sCarJobDao) {
		super.setBaseDao(sCarJobDao);
	}

	@Override
	public boolean addScarJob(TytScarJob sCar, String clientSign, String clientVersion) throws Exception {
		// 当前用户信息
		User user = userService.getByUserId(sCar.getUserId());
		// 生成md5
		String sCarMd5 = ScarUtil.createMd5(sCar.getAge(), sCar.getCity(), sCar.getCounty(), sCar.getDeviceTypeOneCode(), sCar.getDeviceTypeTwoCode(), sCar.getFormatCode(), sCar.getProvince(), sCar.getRemark(), sCar.getUserId(), sCar.getSalaryCode(), sCar.getTelephone(), sCar.getTelName(), sCar.getTitle(), sCar.getYears());
		// 滤重
		Long has = this.getByMd5(sCarMd5, sCar.getUserId());
		if (has == null) {
			sCar.setMd5(sCarMd5);
			sCar.setPublishTime(new Date());
			sCar.setCtime(new Date());
			sCar.setUtime(new Date());
			sCar.setCellPhone(user.getCellPhone());
			sCar.setClientSign(clientSign);
			sCar.setClientVersion(clientVersion);
			sCar.setStatus(Constant.INFO_STATUS_PASS);
			sCar.setSortId(tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_SCAR_JOB_NAME));
			// 一级设备
			TytSource deviceTypeOneCodeSource = TytSourceUtil.getSourceName("deviceType", sCar.getDeviceTypeOneCode() + "");
			sCar.setDeviceTypeOne(deviceTypeOneCodeSource == null ? null : deviceTypeOneCodeSource.getName());

			// 获取二级设备
			TytSource deviceTypeTwoCodeSource = TytSourceUtil.getSourceNameByParentId(deviceTypeOneCodeSource.getId(), sCar.getDeviceTypeTwoCode() + "");
			sCar.setDeviceTypeTwo(deviceTypeTwoCodeSource == null ? null : deviceTypeTwoCodeSource.getName());
			// 获取规格
			TytSource formatCodeSource = TytSourceUtil.getSourceNameByParentId(deviceTypeTwoCodeSource.getId(), sCar.getFormatCode() + "");
			sCar.setFormat(formatCodeSource == null ? null : formatCodeSource.getName());
			TytSource salarySource = TytSourceUtil.getSourceName("salaryMonS", sCar.getSalaryCode() + "");
			sCar.setSalary(salarySource == null ? null : salarySource.getName());
			sCar.setReadNbr(0);
			sCar.setResendCounts(0);
			this.add(sCar);
			return true;
		} else {
			return false;

		}
	}

	private Long getByMd5(String sCarMd5, Long userId) throws Exception {
		String selectSQL = "SELECT id FROM tyt_scar_job where md5=? and (status=? or status=?) and user_id=?";
		Object[] params = new Object[] { sCarMd5,2,3, userId };
		BigInteger obj = this.getBaseDao().query(selectSQL, params);
		if (obj == null) {
			return null;
		} else {
			return obj.longValue();
		}
	}

	@Override
	public boolean updateBcarJob(TytScarJob sCarOld, TytScarJob sCar, String clientSign, String clientVersion) throws Exception {
		// 生成md5
		String sCarMd5 = ScarUtil.createMd5(sCar.getAge(), sCar.getCity(), sCar.getCounty(), sCar.getDeviceTypeOneCode(), sCar.getDeviceTypeTwoCode(), sCar.getFormatCode(), sCar.getProvince(), sCar.getRemark(), sCar.getUserId(), sCar.getSalaryCode(), sCar.getTelephone(), sCar.getTelName(), sCar.getTitle(), sCar.getYears());
		// 滤重
		Long has = this.getByMd5(sCarMd5, sCar.getUserId());
		if (has == null || has != null && has.longValue() == sCar.getId().longValue()) {
			sCarOld.setTitle(sCar.getTitle());
			sCarOld.setTelName(sCar.getTelName());
			sCarOld.setProvince(sCar.getProvince());
			sCarOld.setCity(sCar.getCity());
			sCarOld.setCounty(sCar.getCounty());
			sCarOld.setAge(sCar.getAge());
			sCarOld.setYears(sCar.getYears());
			sCarOld.setTelephone(sCar.getTelephone());
			sCarOld.setRemark(sCar.getRemark());
			sCarOld.setMd5(sCarMd5);
			sCarOld.setPublishTime(new Date());
			sCarOld.setUtime(new Date());
			sCarOld.setSortId(tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_SCAR_JOB_NAME));
			// 一级设备
			sCarOld.setDeviceTypeOneCode(sCar.getDeviceTypeOneCode());
			TytSource deviceTypeOneCodeSource = TytSourceUtil.getSourceName("deviceType", sCar.getDeviceTypeOneCode() + "");
			sCarOld.setDeviceTypeOne(deviceTypeOneCodeSource == null ? null : deviceTypeOneCodeSource.getName());

			// 获取二级设备
			sCarOld.setDeviceTypeTwoCode(sCar.getDeviceTypeTwoCode());
			TytSource deviceTypeTwoCodeSource = TytSourceUtil.getSourceNameByParentId(deviceTypeOneCodeSource.getId(), sCar.getDeviceTypeTwoCode() + "");
			sCarOld.setDeviceTypeTwo(deviceTypeTwoCodeSource == null ? null : deviceTypeTwoCodeSource.getName());
			// 获取规格
			sCarOld.setFormatCode(sCar.getFormatCode());
			TytSource formatCodeSource = TytSourceUtil.getSourceNameByParentId(deviceTypeTwoCodeSource.getId(), sCar.getFormatCode() + "");
			sCarOld.setFormat(formatCodeSource == null ? null : formatCodeSource.getName());
			// 薪资待遇
			sCarOld.setSalaryCode(sCar.getSalaryCode());
			TytSource salarySource = TytSourceUtil.getSourceName("salaryMonS", sCar.getSalaryCode() + "");
			sCarOld.setSalary(salarySource == null ? null : salarySource.getName());
			this.update(sCarOld);
			return true;
		} else {
			return false;
		}
	}

	@Override
	public ScarJobBean updateRead(Long id, Long userId) throws Exception {
		// 获取详情
		TytScarJob sCar = this.getById(id);
		if (sCar != null) {
			if (sCar.getUserId().longValue() != userId.longValue()) {
				BigInteger jobUserId = sCarJobUserService.getJobUser(id, userId, 1);// 浏览记录存在否？
				if (jobUserId == null) {
					sCarJobUserService.add(new TytScarJobUser(id, userId, 1));// 添加浏览记录
					this.updateReadCounts(id);// 修改浏览人数
				}
			}
			ScarJobBean sCarJob = new ScarJobBean();
			BeanUtils.copyProperties(sCar, sCarJob);
			return sCarJob;
		}
		return null;
	}

	private void updateReadCounts(Long id) throws Exception {
		String updateSQL = "update tyt_scar_job set read_nbr=read_nbr+1 ,utime=?  where id=?";
		this.getBaseDao().executeUpdateSql(updateSQL, new Object[] { new Date(), id });

	}

	@Override
	public boolean delScarJob(Long id, Integer status) throws Exception {
		String delSQL = "UPDATE tyt_scar_job SET del_status=? where id=?";
		Object[] params = new Object[] { status, id };
		this.executeUpdateSql(delSQL, params);
		return true;
	}

	@Override
	public ScarTopBean updateScarJobTop(Long id) throws Exception {
		Long sortId = tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_SCAR_JOB_NAME);
		Date now = new Date();
		String topSQL = "UPDATE tyt_scar_job SET resend_counts=resend_counts+1,sort_id=?,publish_time=?,utime=?" + " where id=? and status=?";
		Object[] params = new Object[] { sortId, now, now, id, Constant.INFO_STATUS_PASS };
		this.executeUpdateSql(topSQL, params);
		ScarTopBean sCarTopBean = new ScarTopBean(sortId, now);
		return sCarTopBean;
	}

	@Override
	public List<ScarJobQueryBean> query(String province, String city, String county, String deviceTypeOneCode, String deviceTypeTwoCode, String formatCode, int queryType, long querySign) {
		List<ScarJobQueryBean> returnList = null;
		// 第一次查询大小
		int firstPageSize = AppConfig.getIntProperty("tyt.scar.job.query.first.page.size");
		// 上滑动下滑动最大结果集大小
		int pageSize = AppConfig.getIntProperty("tyt.scar.job.query.page.size");

		List<Object> list = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer(" SELECT  id,sort_id sortId,title,salary_code salaryCode,salary,province,city,county,publish_time publishTime,user_id userId FROM tyt_scar_job m " + " WHERE (status=? OR status=?) AND del_status=? AND display_status=?");
		list.add(2);
		list.add(3);
		list.add(0);
		list.add(0);
		int searchSize = pageSize;
		if (null != province && !"".equals(province.trim())) {
			sb.append(" and province=? ");
			list.add(province.trim());
		}
		if (null != city && !"".equals(city.trim()) && city.indexOf(province) == -1) {
			sb.append(" and city=? ");
			list.add(city.trim());
		}
		if (null != county && !"".equals(county.trim()) && !county.equals(city)) {
			sb.append(" and county=? ");
			list.add(county.trim());
		}

		if (null != deviceTypeOneCode && !"".equals(deviceTypeOneCode.trim())) {
			sb.append(" and device_type_one_code=? ");
			list.add(deviceTypeOneCode.trim());
			if (null != deviceTypeTwoCode && !"".equals(deviceTypeTwoCode.trim())) {
				sb.append(" and device_type_two_code=? ");
				list.add(deviceTypeTwoCode.trim());

				if (null != formatCode && !"".equals(formatCode.trim())) {
					sb.append(" and format_code=? ");
					list.add(formatCode.trim());
				}
			}
		}
		// if(querySign<1){
		// queryType = 1;
		// logger.info("查询时上拉、下滑时客户端数据为空,进行查询类型转换为第一次查询queryType=1");
		// }
		// 第一次请求
		if (queryType == 1) {
			searchSize = firstPageSize;
			// 大小排序
			sb.append(" order by m.sort_id desc ");
		} else
		// 下拉查新数据
		if (queryType == 0) {
			if (querySign == 0) {
				sb.append(" and m.sort_id>?");
				// 小大排序
				sb.append(" order by m.sort_id desc ");
			} else {
				sb.append(" and m.sort_id>?");
				// 小大排序
				sb.append(" order by m.sort_id asc ");
			}
			list.add(querySign);
		}
		// 上推查历史数据
		else {
			sb.append(" and m.sort_id<?");
			// 大小排序
			sb.append(" order by m.sort_id desc ");
			list.add(querySign);
		}

		// 查询数据集
		long t3 = 0, t4 = 0;
		t3 = System.currentTimeMillis();

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("title", Hibernate.STRING);
		scalarMap.put("sortId", Hibernate.LONG);
		scalarMap.put("salaryCode", Hibernate.INTEGER);
		scalarMap.put("salary", Hibernate.STRING);
		scalarMap.put("province", Hibernate.STRING);
		scalarMap.put("city", Hibernate.STRING);
		scalarMap.put("county", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("userId", Hibernate.LONG);

		List<ScarJobQueryBean> queryList = this.getBaseDao().search(sb.toString(), scalarMap, ScarJobQueryBean.class, list.toArray(), 1, searchSize);

		t4 = System.currentTimeMillis();
		logger.info("数据库查询时间：" + (t4 - t3) + "ms");

		if (queryList != null && queryList.size() > 0) {
			returnList = new ArrayList<ScarJobQueryBean>();
			int size = queryList.size();
			if (queryType == 0 && querySign > 0) {
				for (int i = size - 1; i >= 0; i--) {
					ScarJobQueryBean t = queryList.get(i);
					returnList.add(t);
				}
			} else {
				returnList = queryList;
			}
		}
		return returnList;
	}

	@Override
	public List<ScarJobQueryBean> myScarJobList(long user_id, int queryType, long querySign) {
		List<ScarJobQueryBean> returnList = null;
		// 第一次查询大小
		int firstPageSize = AppConfig.getIntProperty("tyt.scar.job.query.first.page.size");
		// 上滑动下滑动最大结果集大小
		int pageSize = AppConfig.getIntProperty("tyt.scar.job.query.page.size");
		List<Object> list = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer("   SELECT STATUS AS 'verifyStatus', display_status AS 'openStatus', id,sort_id sortId,title,salary_code salaryCode,salary,province,city,county,publish_time publishTime,user_id userId FROM tyt_scar_job m " + " WHERE del_status=? and user_id=? ");
		list.add(0);
		list.add(user_id);
		int searchSize = pageSize;
		// 第一次请求
		if (queryType == 1) {
			searchSize = firstPageSize;
			// 大小排序
			sb.append(" order by m.sort_id desc ");
		} else
		// 下拉查新数据
		if (queryType == 0) {
			if (querySign == 0) {
				sb.append(" and m.sort_id>?");
				// 小大排序
				sb.append(" order by m.sort_id desc ");
			} else {
				sb.append(" and m.sort_id>?");
				// 小大排序
				sb.append(" order by m.sort_id asc ");
			}
			list.add(querySign);
		}
		// 上推查历史数据
		else {
			sb.append(" and m.sort_id<?");
			// 大小排序
			sb.append(" order by m.sort_id desc ");
			list.add(querySign);
		}

		// 查询数据集
		long t3 = 0, t4 = 0;
		t3 = System.currentTimeMillis();

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("title", Hibernate.STRING);
		scalarMap.put("sortId", Hibernate.LONG);
		scalarMap.put("salaryCode", Hibernate.INTEGER);
		scalarMap.put("salary", Hibernate.STRING);
		scalarMap.put("province", Hibernate.STRING);
		scalarMap.put("city", Hibernate.STRING);
		scalarMap.put("county", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("openStatus", Hibernate.INTEGER);
		scalarMap.put("verifyStatus", Hibernate.INTEGER);
		List<ScarJobQueryBean> queryList = this.getBaseDao().search(sb.toString(), scalarMap, ScarJobQueryBean.class, list.toArray(), 1, searchSize);
		t4 = System.currentTimeMillis();
		logger.info("数据库查询时间：" + (t4 - t3) + "ms");
		if (queryList != null && queryList.size() > 0) {
			returnList = new ArrayList<ScarJobQueryBean>();
			int size = queryList.size();
			if (queryType == 0 && querySign > 0) {
				for (int i = size - 1; i >= 0; i--) {
					ScarJobQueryBean t = queryList.get(i);
					returnList.add(t);
				}
			} else {
				returnList = queryList;
			}
		}
		return returnList;
	}

	@Override
	public int getMyPublishaNbr(Long userId) {
		String sql = "select count(*) from tyt_scar_job where user_id=? and  status<? and status>? ";
		BigInteger c = this.getBaseDao().query(sql, new Object[] { userId, Constant.INFO_STATUS_NEVER, Constant.INFO_STATUS_DISABLE });
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}

	@Override
	public int getNewPublishaNbr(Long sortId) {
		String sql = "select count(*) from tyt_scar_job where sort_id>? and  (status=? OR status=?) AND del_status=? AND display_status=? ";
		BigInteger c = this.getBaseDao().query(sql, new Object[] { sortId, 2,3,0,0 });
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}

	@Override
	public boolean isBlongUser(String userId, String id) {
		String sql = "SELECT COUNT(*) FROM tyt_scar_job tsju WHERE tsju.`id`=? AND tsju.`user_id`=?";
		BigInteger count = this.getBaseDao().query(sql, new Object[] { id, userId });
		return count.intValue() == 1;
	}

	@Override
	public boolean isJobOpen(String id, int jobStatusOpen) {
		String sql = "select count(*) from tyt_scar_job tsj where tsj.`id`=? and tsj.`display_status`=?";
		BigInteger count = this.getBaseDao().query(sql, new Object[] { id, jobStatusOpen });
		return count.intValue() == 1;
	}

	@Override
	public void updateJobStatus(String id, int jobStatusOpen) {
		// 获取当前排序字段
		Long sortId = tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_SCAR_JOB_NAME);
		String sql = "UPDATE tyt_scar_job tsj SET tsj.`display_status`=?, tsj.`sort_id`=?, tsj.`utime`=NOW(), tsj.`publish_time`=NOW() WHERE tsj.`id`=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { jobStatusOpen, sortId, id });
	}

	@Override
	public void updateCollectStatus(String userId, int status) {
		String sql = "UPDATE tyt_collect_info tci SET tci.`status`=1 WHERE tci.`user_id`=? AND tci.`type`=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { userId, status });
	}

	@Override
	public void updateVerifyAndOpenStatus(Long id) {
		/*
		 * 获取信息执行如下操作 1.如果当前信息为审核未通过状态，编辑后变为未审核，关闭状态，需要置顶。
		 * 2.当前信息为未审核状态并且开放状态，编辑后需要置顶。 3.当前信息为未审核并且关闭状态，编辑后变为开放状态，并执行置顶操作。
		 * 4.当前信息为审核且开放状态，编辑后需要置顶。 5.当前信息为审核且关闭状态，编辑后变为开放状态，并执行置顶操作。
		 */
		TytScarJob job = this.getById(id);
		/*
		 * 审核状态 0;无效1;审核未通过2;待审核3;审核通过
		 */
		if (job.getStatus() == 1) {
			updateOpenAndVerifyStatus(id, 0, 2);
		} else if (job.getStatus() == 2) {
			updateOpenAndVerifyStatus(id, 0, 2);
		} else if (job.getStatus() == 3) {
			updateOpenAndVerifyStatus(id, 0, 3);
		}
	}

	/**
	 * 更新开放状态和审核状态
	 * 
	 * @param id
	 * @param openStatus
	 * @param verifyStatus
	 */
	private void updateOpenAndVerifyStatus(Long id, int openStatus, int verifyStatus) {
		String sql = "UPDATE tyt_scar_job tbj SET tbj.`status`=?, tbj.`display_status`=? WHERE tbj.`id`=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { verifyStatus, openStatus, id });
	}

	@Override
	public List<ScarJobQueryBean> getScarJobListByIds(List<Long> ids, Integer currentPage) {

		Map<String, Object> params = new HashMap<String, Object>();
		StringBuffer sb = new StringBuffer(" SELECT  id,sort_id sortId,title,salary_code salaryCode,salary,province,city,county,publish_time publishTime,user_id userId,display_status openStatus,status verifyStatus FROM tyt_scar_job m " + " WHERE id IN(:ids) AND STATUS IN(:status) and del_status IN(:delStatus) order by m.sort_id desc");
		params.put("ids", ids);
		params.put("status", new Object[] { 2, 3 });
		params.put("delStatus", new Object[] { 0 });

		// 查询数据集
		long t3 = 0, t4 = 0;
		t3 = System.currentTimeMillis();

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("title", Hibernate.STRING);
		scalarMap.put("sortId", Hibernate.LONG);
		scalarMap.put("salaryCode", Hibernate.INTEGER);
		scalarMap.put("salary", Hibernate.STRING);
		scalarMap.put("province", Hibernate.STRING);
		scalarMap.put("city", Hibernate.STRING);
		scalarMap.put("county", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("openStatus", Hibernate.INTEGER);
		scalarMap.put("verifyStatus", Hibernate.INTEGER);

		List<ScarJobQueryBean> queryList = this.getBaseDao().search(sb.toString(), scalarMap, ScarJobQueryBean.class, params);

		t4 = System.currentTimeMillis();
		logger.info("数据库查询时间：" + (t4 - t3) + "ms");

		return queryList;
	}

	@Override
	public boolean isHasPublishedInDays(Long userId, int days) {
		String sql = "SELECT COUNT(*) FROM tyt_scar_recruit tbj WHERE UNIX_TIMESTAMP(tbj.`publish_time`)>? AND tbj.`user_id`=?";
		// 获取指定天数前的日期
		Date date = TimeUtil.addDay(-days);
		long secondDaysAgo = date.getTime() / 1000;
		BigInteger count = this.getBaseDao().query(sql, new Object[] { secondDaysAgo, userId });
		return count.intValue() > 0;
	}
}
