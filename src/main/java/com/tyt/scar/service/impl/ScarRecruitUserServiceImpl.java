package com.tyt.scar.service.impl;

import java.math.BigInteger;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytBcarRecruitUser;
import com.tyt.model.TytScarRecruitUser;
import com.tyt.scar.service.ScarRecruitUserService;



@Service("sCarRecruitUserService")
public class ScarRecruitUserServiceImpl extends BaseServiceImpl<TytScarRecruitUser, Long> implements
		ScarRecruitUserService {

	@Resource(name="sCarRecruitUserDao")
	public void setBaseDao(BaseDao<TytScarRecruitUser, Long> sCarRecruitUserDao) {
		super.setBaseDao(sCarRecruitUserDao);
	}


	@Override
	public  TytScarRecruitUser getTytScarRecruitUser(Long userId, Long brId) {
		String sql="from TytScarRecruitUser where userId=? and brId=?";
		List<TytScarRecruitUser> truList= this.getBaseDao().find(sql,userId, brId);
		if(truList!=null&& truList.size()>0){
			return truList.get(0);
		}
		return null;
	}


	@Override
	public BigInteger getRecruitUser(Long id, Long userId, int status) {
		String selectSQL="SELECT id from tyt_scar_recruit_user "
				+ "where br_id=? and user_id=? and status=?";
		Object[] params=new Object[]{id,userId,status};
		return this.getBaseDao().query(selectSQL, params);
	}


}
