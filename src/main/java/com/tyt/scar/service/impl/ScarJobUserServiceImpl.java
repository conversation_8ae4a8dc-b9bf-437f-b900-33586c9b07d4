package com.tyt.scar.service.impl;

import java.math.BigInteger;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytScarJobUser;
import com.tyt.scar.service.ScarJobUserService;




@Service("sCarJobUserService")
public class ScarJobUserServiceImpl extends BaseServiceImpl<TytScarJobUser, Long> implements
		ScarJobUserService {

	@Resource(name="sCarJobUserDao")
	public void setBaseDao(BaseDao<TytScarJobUser, Long> sCarJobUserDao) {
		super.setBaseDao(sCarJobUserDao);
	}

	@Override
	public BigInteger getJobUser(Long id, Long userId, int status)
			throws Exception {
		String selectSQL="SELECT id from tyt_scar_job_user "
				+ "where br_id=? and user_id=? and status=?";
		Object[] params=new Object[]{id,userId,status};
		return this.getBaseDao().query(selectSQL, params);
	}


	
	
}
