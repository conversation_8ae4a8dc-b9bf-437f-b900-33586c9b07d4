package com.tyt.scar.service;

import java.util.List;
import java.util.Map;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytScarRecruit;
import com.tyt.scar.bean.ScarRecruitBean;
import com.tyt.scar.bean.ScarRecruitQueryBean;

public interface ScarRecruitService extends BaseService<TytScarRecruit, Long> {
	/**
	 * 获得大板车招聘列表
	 * 
	 * @param province
	 * @param city
	 * @param county
	 * @param dutyCode
	 * @param queryType
	 * @param querySign
	 * @return List<BcarRecruitQueryBean>
	 */

	public List<ScarRecruitQueryBean> query(String province, String city, String county, String deviceTypeOneCode, String deviceTypeTwoCode, String formatCode, int queryType, long querySign);

	/**
	 * 获得大板车招聘列表
	 * 
	 * @param user_id
	 * @param queryType
	 * @param querySign
	 * @return List<BcarRecruitQueryBean>
	 */

	public List<ScarRecruitQueryBean> myScarRecruitList(long user_id, int queryType, long querySign);

	/**
	 * 获得我的发布数量
	 * 
	 * @param id
	 * @return
	 */
	public int getMyPublishaNbr(Long userId);

	/**
	 * 获得新发布数量
	 * 
	 * @param sortId
	 * @return
	 */
	public int getNewPublishaNbr(Long sortId);

	/**
	 * 置顶
	 * 
	 * @param id
	 * @return
	 */
	public Map<String, Long> updateTop(Long id);

	/**
	 * 设为已读
	 * 
	 * @param id
	 * @return
	 */
	public void updateSetRead(Long userId, Long id);

	/**
	 * 删除
	 * 
	 * @param id
	 */
	public void delTytScarRecruit(Long id);

	public boolean addScarRecruit(TytScarRecruit sCar, String clientSign, String clientVersion) throws Exception;

	public boolean updateBcarJob(TytScarRecruit sCarOld, TytScarRecruit sCar) throws Exception;

	public ScarRecruitBean updateRead(Long id, Long userId) throws Exception;

	/**
	 * 查询设备招聘信息是否属于用于
	 * 
	 * @param userId
	 * @param id
	 * @return
	 */
	public boolean isBlongUser(String userId, String id);

	/**
	 * 获取设备招聘信息是否处于开放状态
	 * 
	 * @param id
	 * @param recruitStatusOpen
	 * @return
	 */
	public boolean isRecruitOpen(String id, int recruitStatusOpen);

	/**
	 * 开放招聘信息
	 * 
	 * @param id
	 * @param recruitStatusOpen
	 */
	public void updateRecruitStatus(String id, int recruitStatusOpen);

	/**
	 * 更新开放和认证状态
	 * 
	 * @param id
	 */
	public void updateVerifyAndOpenStatus(Long id);

	/**
	 * 我的收藏
	 * 
	 * @param ids
	 *            id集合
	 * @param currentPage当前页面
	 * @return
	 */
	public List<ScarRecruitQueryBean> getScarRecruitListByIds(List<Long> ids, Integer currentPage);

	/**
	 * 用户是否在指定时间内发布过设备求职信息
	 * 
	 * @param userId
	 * @param i
	 * @return
	 */
	public boolean isHasPublishedInDays(Long userId, int i);
}
