package com.tyt.scar.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytScarJob;
import com.tyt.scar.bean.ScarJobBean;
import com.tyt.scar.bean.ScarJobQueryBean;
import com.tyt.scar.bean.ScarTopBean;

public interface ScarJobService extends BaseService<TytScarJob, Long> {

	public boolean addScarJob(TytScarJob sCar, String clientSign, String clientVersion) throws Exception;

	public boolean updateBcarJob(TytScarJob sCarOld, TytScarJob sCar, String clientSign, String clientVersion) throws Exception;

	public ScarJobBean updateRead(Long id, Long userId) throws Exception;

	public boolean delScarJob(Long id, Integer status) throws Exception;

	public ScarTopBean updateScarJobTop(Long id) throws Exception;

	/**
	 * 获得设备求职列表
	 * 
	 * @param province
	 * @param city
	 * @param county
	 * @param dutyCode
	 * @param queryType
	 * @param querySign
	 * @return List<BcarJobQueryBean>
	 */

	public List<ScarJobQueryBean> query(String province, String city, String county, String deviceTypeOneCode, String deviceTypeTwoCode, String formatCode, int queryType, long querySign);

	/**
	 * 获得设备求职列表
	 * 
	 * @param user_id
	 * @param queryType
	 * @param querySign
	 * @return List<BcarJobQueryBean>
	 */

	public List<ScarJobQueryBean> myScarJobList(long user_id, int queryType, long querySign);

	/**
	 * 获得我的发布数量
	 * 
	 * @param id
	 * @return
	 */
	public int getMyPublishaNbr(Long userId);

	/**
	 * 获得新发布数量
	 * 
	 * @param sortId
	 * @return
	 */
	public int getNewPublishaNbr(Long sortId);

	/**
	 * 查询求职信息是否属于用户
	 * 
	 * @param userId
	 * @param id
	 * @return
	 */
	public boolean isBlongUser(String userId, String id);

	/**
	 * 获取求职信息是否处于开放状态
	 * 
	 * @param id
	 * @param jobStatusOpen
	 * @return
	 */
	public boolean isJobOpen(String id, int jobStatusOpen);

	/**
	 * 修改求职信息的招聘状态
	 * 
	 * @param id
	 * @param jobStatusOpen
	 */
	public void updateJobStatus(String id, int jobStatusOpen);

	/**
	 * 全部取消收藏
	 * 
	 * @param userId
	 * @param status
	 *            取消收藏的类型
	 */
	public void updateCollectStatus(String userId, int status);
    /**
     * 我的收藏列表
     * @param ids id集合
     * @param currentPage当前页面
     * @return
     */
	public List<ScarJobQueryBean> getScarJobListByIds(List<Long> ids, Integer currentPage);

	/**
	 * 更新认证状态和开放状态
	 * 
	 * @param id
	 */
	public void updateVerifyAndOpenStatus(Long id);

	public boolean isHasPublishedInDays(Long userId, int i);

}
