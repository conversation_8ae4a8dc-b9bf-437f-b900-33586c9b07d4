package com.tyt.verificationcode.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.BlacklistUser;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.BlacklistUserService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.MD5Util;
import com.tyt.util.MobileUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.verificationcode.bean.SendMsgResultBean;
import com.tyt.verificationcode.service.VerificationCodeService;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 发送手机短信验证码
 * 
 * <AUTHOR>
 * @date 2016-6-14上午11:52:29
 * @description
 */
@Controller
@RequestMapping("/plat/verificationCode")
public class VerificationCodeController extends BaseController {
	@Resource(name = "verificationCodeService")
	private VerificationCodeService verificationCodeService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "blacklistUserService")
	private BlacklistUserService blacklistUserService;

	/**
	 * 发送短信接口
	 * 
	 * @param phoneNumber
	 *            要发送验证短信短信的号码
	 * @param type
	 *            验证码类型，1：注册验证码 2：忘记密码验证码，3：添加电话，4：钱包提现
	 * @return
	 */
	@RequestMapping(value = {"/send", "/send.action"})
	@ResponseBody
	public SendMsgResultBean send(String phoneNumber, Integer userId, Integer type,
								  @RequestParam(required = false) String imageCode,
								  @RequestParam(required = false) Integer clientSign) {
		logger.info("send message param, phoneNumber: " + phoneNumber + ", type: " + type);
		SendMsgResultBean rm = new SendMsgResultBean();
		try {
			if (phoneNumber == null || "".equals(phoneNumber.trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("电话号码不能为空");
			} else if (type == null) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("验证类型不能为空");
			}else if (userId == null && (type.intValue() == 5 || type.intValue() == 6)) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("type为5,6时userId必填");
			} else {

				if (StringUtils.isNotBlank(imageCode)) {
					String cacheImageCode = RedisUtil.get(Constant.LOGIN_VERIFY_CODE_REDIS_KEY + phoneNumber);
					if (StringUtils.isBlank(cacheImageCode) || !imageCode.equals(cacheImageCode)) {
						rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
						rm.setMsg("图形验证码错误");

						// 验证失败清除验证码信息
						RedisUtil.del(Constant.LOGIN_VERIFY_CODE_REDIS_KEY + phoneNumber);

						return rm;
					}
				}

				// 调用短信平台发送短信
				String[] resultsArr;
				if (type.intValue() == 5 || type.intValue() == 6) {
					User user = userService.getById(userId.longValue());

					verificationCodeService.sendVerifySms(user.getCellPhone(), type);
				} else {
					checkCellPhoneIsBlack(phoneNumber, rm);
					if (ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_CODE == rm.getCode()) {
						return rm;
					}
					ShortMessageBean shortMessageBean = verificationCodeService.sendVerifySms(phoneNumber, type);
					String verifyCode = shortMessageBean.getVerifyCode();
					rm.setVerificationCode(MD5Util.GetMD5Code(verifyCode));
				}

				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("短信发送成功");

				// 货主web版不返回短信验证码
				if (Constant.ClientSignEnum.WEB_GOODS.code == clientSign) {
					rm.setVerificationCode(null);
				}
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	@RequestMapping(value = {"/verify", "/verify.action"})
	@ResponseBody
	public ResultMsgBean list(String phoneNumber, String userId, String verificationCode) {
		logger.info("verify code param, phoneNumber: " + phoneNumber + ", verificationCode: " + verificationCode);
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (phoneNumber == null || "".equals(phoneNumber.trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("电话号码不能为空");
			} else if (verificationCode == null || "".equals(verificationCode.trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("验证码不能为空");
			} else {
				if (userId != null) {
					phoneNumber = userService.getById(Long.valueOf(userId)).getCellPhone();
				}
				// 验证验证码
				verificationCodeService.verify(phoneNumber, verificationCode, rm);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 校验用户实名认证修改 验证码.
	 * @param userId userId
	 * @param validCode validCode
	 * @return ResultMsgBean
	 */
	@PostMapping("/userAuthValid")
	@ResponseBody
	public ResultMsgBean userAuthValid(Long userId, String validCode) {

		try {
			if(CommonUtil.hasNull(userId, validCode)){
				return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
			}

			// 验证验证码
			int verifyStatus = verificationCodeService.verifyUserCode(userId, validCode);

			return ResultMsgBean.successResponse(verifyStatus);

		} catch (Exception e) {
			PlatCommonUtil.printErrorInfo("", e);
			return ResultMsgBean.failResponse(e);
		}
	}

	/**
	 * 发送短信接口 v6000版本
	 *
	 * @param phoneNumber 要发送验证短信短信的号码
	 * @param type        验证码类型，1：注册验证码 2：忘记密码验证码，3：添加电话，4：钱包提现
	 * @return
	 */
	@RequestMapping(value = {"/v6000/send", "/v6000/send.action"})
	@ResponseBody
	public SendMsgResultBean sendCode(String phoneNumber, Integer userId, Integer type,
									  @RequestParam(required = false) String imageCode) {
		logger.info("send message param, phoneNumber: " + phoneNumber + ", type: " + type);
		SendMsgResultBean rm = new SendMsgResultBean();
		try {
			if (phoneNumber == null || "".equals(phoneNumber.trim())) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("电话号码不能为空");
			} else if (type == null) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("验证类型不能为空");
			} else if (userId == null && (type == 5 || type == 6)) {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("type为5,6时userId必填");
			} else if(BooleanUtils.isFalse(MobileUtil.isMobile(phoneNumber))){
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("电话号码格式错误！");
			}else{
				if (StringUtils.isNotBlank(imageCode)) {
					String cacheImageCode = RedisUtil.get(Constant.LOGIN_VERIFY_CODE_REDIS_KEY + phoneNumber);
					if (StringUtils.isBlank(cacheImageCode) || !imageCode.equals(cacheImageCode)) {
						rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
						rm.setMsg("图形验证码错误");

						// 验证失败清除验证码信息
						RedisUtil.del(Constant.LOGIN_VERIFY_CODE_REDIS_KEY + phoneNumber);
						return rm;
					}
				}

				// 调用短信平台发送短信
				if (type == 5 || type == 6) {
					User user = userService.getById(userId.longValue());
					verificationCodeService.sendVerifySms(user.getCellPhone(), type);
				} else {
					checkCellPhoneIsBlack(phoneNumber, rm);
					if (ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_CODE == rm.getCode()) {
						return rm;
					}
					ShortMessageBean shortMessageBean = verificationCodeService.sendVerifySms(phoneNumber, type);
					String verifyCode = shortMessageBean.getVerifyCode();
					rm.setVerificationCode(MD5Util.GetMD5Code(verifyCode));
				}

				rm.setVerificationCode(null);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private SendMsgResultBean checkCellPhoneIsBlack(String cellPhone, SendMsgResultBean sendMsgResultBean) {
		BlacklistUser blacklistUser = blacklistUserService.getBlackByCellPhone(cellPhone);
		if(blacklistUser != null && blacklistUser.getStatus() == 1){
			sendMsgResultBean.setCode(ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_CODE);
			sendMsgResultBean.setMsg("该手机号存在风险，暂时无法完成注册或相关服务申请，请更换其他手机号码或联系客服400-6688-998");
			return sendMsgResultBean;
		}
		return sendMsgResultBean;
	}

}
