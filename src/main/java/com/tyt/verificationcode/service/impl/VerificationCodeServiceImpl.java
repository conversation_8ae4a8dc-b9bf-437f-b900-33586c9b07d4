package com.tyt.verificationcode.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytMechantService;
import com.tyt.model.User;
import com.tyt.plat.enums.GlobalStatusEnum;
import com.tyt.plat.enums.ValidCodeTypeEnum;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.UserService;
import com.tyt.user.service.VerifyLogService;
import com.tyt.user.service.impl.VerifyLogServiceImpl;
import com.tyt.util.Constant;
import com.tyt.util.RandomUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.verificationcode.service.VerificationCodeService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("verificationCodeService")
public class VerificationCodeServiceImpl extends BaseServiceImpl<TytMechantService, Long> implements VerificationCodeService {
    private final Logger logger = LoggerFactory.getLogger(VerifyLogServiceImpl.class);

    private static final String REGISTER_TEMPLATE_KEY = "register.template";
    private static final String FORGET_PASSWORD_TEMPLATE_KEY = "forget.password.template";
    private static final String ADD_TELEPHONE_TEMPLATE_KEY = "add.telephone.template";
    private static final String WALLET_WITHDRAW_TEMPLATE_KEY = "wallet.withdraw.template";
    private static final String FORGET_POCKET_PWD_TEMPLATE_KEY = "forget.pocket.pwd.template";
    private static final String SETUP_POCKET_PWD_TEMPLATE_KEY = "setup.withdraw.template";
    private static final String LOGIN_TEMPLATE_KEY = "login.template";
    private static final String BIND_TEMPLATE_KEY = "bind.template";
    private static final String USER_CANCEL_TEMPLATE_KEY = "user.cancel.template";
    private static final String REGISTER_TEMPLATE_CODE = "SMS_139980394";
    private static final String FORGET_PASSWORD_TEMPLATE_CODE = "SMS_139975384";
    private static final String ADD_TELEPHONE_TEMPLATE_CODE = "SMS_139970417";
    private static final String WALLET_WITHDRAW_TEMPLATE_CODE = "SMS_139985214";
    private static final String FORGET_POCKET_PWD_TEMPLATE_CODE = "SMS_153985171";
    private static final String SETUP_POCKET_PWD_TEMPLATE_CODE = "SMS_153985171";
    private static final String LOGIN_TEMPLATE_CODE = "SMS_153985171";
    private static final String BIND_TEMPLATE_CODE = "SMS_153985171";
    private static final String USER_CANCEL_TEMPLATE_CODE = "SMS_153985171";
    private static final String SMS_VERIFIED = "yes";

    /* 保存手机号和对应验证码， 格式 手机号->验证码#验证码生成时间毫秒值 */
    //private static Map<String, String> phoneVerifycode = new HashMap<String, String>();

    @Resource(name = "verifyLogService")
    private VerifyLogService verifyLogService;
    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "tytMessageTmplService")
    private TytMessageTmplService messageTmplService;

    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Autowired
    private UserService userService;

    @Resource(name = "verificationCodeDao")
    @Override
    public void setBaseDao(BaseDao<TytMechantService, Long> verificationCodeDao) {
        super.setBaseDao(verificationCodeDao);
    }

    @Override
    public ShortMessageBean sendVerifySms(String phoneNumber, Integer type) {
        /*
         * 根据不同的验证类型生成发送内容，1：注册验证码，2：修改密码验证码,3: 添加手机号
         */
        String templateCode = null;
        String messageTemplate = null;
        switch (type) {
            case 1:
                messageTemplate = queryTemplateByKey(REGISTER_TEMPLATE_KEY, "验证码${verifyCode}，感谢您选择特运通，使用过程有任何疑问客服随时帮您400-6688-998");
                templateCode = REGISTER_TEMPLATE_CODE;
                break;
            case 2:
                messageTemplate = queryTemplateByKey(FORGET_PASSWORD_TEMPLATE_KEY, "验证码${verifyCode}，请尽快输入验证码完成重置密码，若非本人操作请忽略");
                templateCode = FORGET_PASSWORD_TEMPLATE_CODE;
                break;
            case 3:
                messageTemplate = queryTemplateByKey(ADD_TELEPHONE_TEMPLATE_KEY, "亲爱的用户，您正在添加电话，验证码为${verifyCode}，10分钟内有效，请勿将验证码泄露给他人。");
                templateCode = ADD_TELEPHONE_TEMPLATE_CODE;
                break;
            case 4:
                messageTemplate = queryTemplateByKey(WALLET_WITHDRAW_TEMPLATE_KEY, "您的提现验证码是 ${verifyCode}用于账号安全验证，请勿提供给他人，以避免造成经济损失。如非本人操作，请联系客服：400-6688-998");
                templateCode = WALLET_WITHDRAW_TEMPLATE_CODE;
                break;
            case 5: // 忘记钱包密码
                messageTemplate = queryTemplateByKey(FORGET_POCKET_PWD_TEMPLATE_KEY, "您的手机验证码是${verifyCode}，在10分钟内有效。如非本人操作忽略本短信。");
                templateCode = FORGET_POCKET_PWD_TEMPLATE_CODE;
                break;
            case 6: // 设置钱包密码
                messageTemplate = queryTemplateByKey(SETUP_POCKET_PWD_TEMPLATE_KEY, "您的手机验证码是${verifyCode}，在10分钟内有效。如非本人操作忽略本短信。");
                templateCode = SETUP_POCKET_PWD_TEMPLATE_CODE;
                break;
            case 7: // 登录 验证码
                messageTemplate = queryTemplateByKey(LOGIN_TEMPLATE_KEY, "您的手机验证码是${verifyCode}，在10分钟内有效。如非本人操作忽略本短信。");
                templateCode = LOGIN_TEMPLATE_CODE;
                break;
            case 8: // 小程序绑定手机号
                messageTemplate = queryTemplateByKey(BIND_TEMPLATE_KEY, "您的手机验证码是${verifyCode}，在10分钟内有效。如非本人操作忽略本短信。");
                templateCode = BIND_TEMPLATE_CODE;
                break;
            case 9: // 用户注销 验证码
                messageTemplate = queryTemplateByKey(USER_CANCEL_TEMPLATE_KEY, "您的手机验证码是${verifyCode}，在10分钟内有效。如非本人操作忽略本短信。");
                templateCode = USER_CANCEL_TEMPLATE_CODE;
                break;
            case 10: //手机号变更发送验证码
                messageTemplate = queryTemplateByKey(LOGIN_TEMPLATE_KEY, "您的手机验证码是${verifyCode}，在10分钟内有效。如非本人操作忽略本短信。");
                templateCode = LOGIN_TEMPLATE_CODE;
				break;
            case 11: //手机号变更发送验证码
                messageTemplate = ValidCodeTypeEnum.USER_VERIFY_CHANGE.getTemplateContent();
                templateCode = ValidCodeTypeEnum.USER_VERIFY_CHANGE.getTemplateCode();
                break;
            default:
				break;

        }

        return this.sendVerifySms(phoneNumber, messageTemplate, templateCode);

    }

    @Override
    public ShortMessageBean sendVerifySms(String phoneNumber, String messageTemplate, String templateCode) {
        // 生成验证码
        String verifycode = RandomUtil.getSixRandom();
        String content = StringUtils.replace(messageTemplate, "${verifyCode}", verifycode);

        ShortMessageBean verifyShortMessage = messageCenterPushService.createVerifyShortMessage(phoneNumber, content, templateCode, verifycode, "plat验证码");
        messageCenterPushService.sendMultiMessage(verifyShortMessage, null, null);

        //验证码缓存
        RedisUtil.set(Constant.SMS_VERIFYCODE_PREFFIX + phoneNumber, verifycode,
                Constant.CACHE_EXPIRE_TIME_10MIN_INT);

        logger.info("sendVerifySms_done : phone : {} , content : {}", phoneNumber, content);

        return verifyShortMessage;
    }

    private String queryTemplateByKey(String templateKey, String defaultTemplate) {
        String messageTemplate;
        messageTemplate = messageTmplService.getSmsTmpl(templateKey);
        logger.info("get message template by key: " + templateKey + ",result is: " + messageTemplate);
        return messageTemplate == null ? defaultTemplate : messageTemplate;
    }

    @Override
    public void verify(String phoneNumber, String verificationCode, ResultMsgBean rm) {
        String verifyKey = Constant.SMS_VERIFYCODE_PREFFIX + phoneNumber;
        String realVerifyCode = RedisUtil.get(verifyKey);
        if (verificationCode.equals(realVerifyCode)) {
            // 删除验证码
            RedisUtil.del(verifyKey);
            // 设置该手机号验证了验证码，在真正修改密码或者是设置密码的时候使用
            RedisUtil.set(Constant.SMS_VERIFIED_PREFFIX + phoneNumber, SMS_VERIFIED, Constant.CACHE_EXPIRE_TIME_10MIN_INT);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("验证成功");
        } else {
            rm.setCode(ReturnCodeConstant.INVALID_VERIFY_CODE);
            rm.setMsg("无效的验证码");
        }
    }

    @Override
    public int verifyUserCode(Long userId, String verifyCode) throws Exception{

        User dbUser = userService.getByUserId(userId);

        if(dbUser == null){
            throw TytException.createException(ResponseEnum.request_error.info());
        }

        String cellPhone = dbUser.getCellPhone();

        ResultMsgBean rm = new ResultMsgBean();

        this.verify(cellPhone, verifyCode, rm);

        if(rm.isSuccess()){
            return GlobalStatusEnum.yes.getCode();
        }
        return GlobalStatusEnum.no.getCode();
    }

    @Override
    public ResultMsgBean verify(String phoneNumber, String verificationCode) {
        String verifyKey = Constant.SMS_VERIFYCODE_PREFFIX + phoneNumber;
        String realVerifyCode = RedisUtil.get(verifyKey);
        if (verificationCode.equals(realVerifyCode)) {
            // 删除验证码
            RedisUtil.del(verifyKey);
            return ResultMsgBean.successResponse();
        }
        return ResultMsgBean.failResponse(ReturnCodeConstant.INVALID_VERIFY_CODE, "验证码错误，请重新输入");
    }

}
