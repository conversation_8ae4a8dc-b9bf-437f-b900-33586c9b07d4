package com.tyt.verificationcode.service;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.ShortMsgBean;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytMechantService;
import com.tyt.verificationcode.bean.SendMsgResultBean;

/**
 * 发送短信服务层
 * 
 * <AUTHOR>
 * @date 2016-6-14下午1:57:36
 * @description
 */
public interface VerificationCodeService extends BaseService<TytMechantService, Long> {

	/**
	 * 发送短信
	 * 
	 * @param phoneNumber
	 * @param type
	 *            1：注册验证码，2：修改密码验证码
	 * @return
	 */
	ShortMessageBean sendVerifySms(String phoneNumber, Integer type);

	/**
	 * 发送短信
	 *
	 * @param phoneNumber
	 * @param messageTemplate 短信模板
	 * @return
	 */
	ShortMessageBean sendVerifySms(String phoneNumber, String messageTemplate, String templateCode);

	/**
	 * 验证验证码
	 * 
	 * @param phoneNumber
	 * @param verificationCode
	 * @param rm
	 */
	void verify(String phoneNumber, String verificationCode, ResultMsgBean rm);

	/**
	 * 验证码校验.
	 * @param userId
	 * @param verifyCode
	 * @return
	 */
	int verifyUserCode(Long userId, String verifyCode) throws Exception;

	ResultMsgBean verify(String phoneNumber, String verificationCode);

}
