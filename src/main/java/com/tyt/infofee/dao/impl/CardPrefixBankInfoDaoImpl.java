package com.tyt.infofee.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.infofee.dao.CardPrefixBankInfoDao;
import com.tyt.model.CardPrefixBankInfo;
import org.springframework.stereotype.Repository;

@Repository("cardPrefixBankInfoDao")
public class CardPrefixBankInfoDaoImpl extends BaseDaoImpl<CardPrefixBankInfo, Long> implements CardPrefixBankInfoDao {
	public CardPrefixBankInfoDaoImpl() {
		this.setEntityClass(CardPrefixBankInfo.class);
	}
}
