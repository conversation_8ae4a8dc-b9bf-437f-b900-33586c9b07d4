package com.tyt.infofee.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.infofee.dao.TytUserBankcardDao;
import com.tyt.model.TytUserBankcard;

@Repository("tytUserBankcardDao")
public class TytUserBankcardDaoImpl extends BaseDaoImpl<TytUserBankcard, Long> implements TytUserBankcardDao {
	public TytUserBankcardDaoImpl() {
		this.setEntityClass(TytUserBankcard.class);
	}
}
