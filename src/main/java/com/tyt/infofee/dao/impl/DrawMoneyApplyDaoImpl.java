package com.tyt.infofee.dao.impl;

import org.springframework.stereotype.Repository;
import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.infofee.dao.DrawMoneyApplyDao;
import com.tyt.model.TytDrawMoneyApply;

@Repository("drawMoneyApplyDao")
public class DrawMoneyApplyDaoImpl extends BaseDaoImpl<TytDrawMoneyApply, Integer> implements DrawMoneyApplyDao {
	public DrawMoneyApplyDaoImpl() {
		this.setEntityClass(TytDrawMoneyApply.class);
	}
}
