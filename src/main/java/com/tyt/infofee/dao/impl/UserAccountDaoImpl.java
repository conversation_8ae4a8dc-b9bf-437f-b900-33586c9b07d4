package com.tyt.infofee.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.infofee.dao.UserAccountDao;
import com.tyt.model.TytUserAccount;

@Repository
public class UserAccountDaoImpl extends BaseDaoImpl<TytUserAccount, Long> implements UserAccountDao {

    public UserAccountDaoImpl() {
        this.setEntityClass(TytUserAccount.class);
    }

}
