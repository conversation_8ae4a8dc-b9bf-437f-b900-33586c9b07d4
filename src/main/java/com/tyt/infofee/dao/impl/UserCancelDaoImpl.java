package com.tyt.infofee.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.infofee.dao.UserCancelDao;
import com.tyt.model.TytUserCancel;
import org.springframework.stereotype.Repository;

@Repository("userCancelDao")
public class UserCancelDaoImpl extends BaseDaoImpl<TytUserCancel, Long> implements UserCancelDao {
	public UserCancelDaoImpl() {
		this.setEntityClass(TytUserCancel.class);
	}
}
