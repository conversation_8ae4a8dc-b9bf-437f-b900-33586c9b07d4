package com.tyt.infofee.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.infofee.dao.TransportOrdersDao;
import com.tyt.model.TytTransportOrders;

@Repository("transportOrdersDao")
public class TransportOrdersDaoImpl extends BaseDaoImpl<TytTransportOrders, Long>  implements  TransportOrdersDao   {

    public TransportOrdersDaoImpl() {
        this.setEntityClass(TytTransportOrders.class);
    }

}
