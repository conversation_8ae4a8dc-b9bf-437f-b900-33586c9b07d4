package com.tyt.infofee.bean;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class InfoFeePayinfo {
    private String id;
    //车主
    private String payUserId;

    //车主昵称
    @Getter
    @Setter
    private String payUserName;

    //车主手机号
    @Getter
    @Setter
    private String payCellPhone;

    private String tsOrderNo;
    //信息费金额
    private String payAmount;
    //订单总金额
    private String totalOrderAmount;
    //优惠券金额（减免金额）
    private String couponAmount;
    //信息费状态
    private String costStatus;

    //旧支付状态
    private String oldPaymentStatus; //支付状态0待支付1支付失败2支付成功

    //支付时间
    private String payEndTime = "";
    //异常上报时间
    private String exceptionTime = "";
    //退款金额
    private String refundAmount = "0";
    //退款时间
    private String refundTime = "";
    //退款原因
    private String refundReason= "";
    //动作时间
    private String actionTime = "";
    //备注
    private String comment = "";

    private String exStatus = "";

    private String exComment = "";
    //异常处理意见
    private String exResult = "";

    private String exResultComment = "";

    private String carAmount = ""; //单位分

    private String goodsAmount = ""; //单位分

    private String platformServiceAmount="";//单位分

    private String carServiceAmount="";//单位分
    private CreditUserInfo driverUserInfo;

    private List<InfoFeeExceptionInfo> infoFeeExceptionInfoList;
    /**
     * 车头城市
     */
    private String headCity;
    /**
     * 车头编码
     */
    private String headNo;
    /**
     * 挂车城市
     */
    private String tailCity;
    /**
     * 挂车编码
     */
    private String tailNo;
    /**
     * 车辆id
     */
    private Long carId;

    /**
     * 车辆是否为待完善车辆
     */
    @Getter
    @Setter
    private Boolean carIsNeedImprovenData;

    /**
     * 车辆认证状态
     */
    @Getter
    @Setter
    private Integer carAuth;

    /**
     * 服务费
     */
    private BigDecimal payServiceCharge;

    /**
     * 显示货源是否成交车标识 0不是 1是
     */
    private Integer isDealCar;

    /**
     * 实际到期时间
     */
    private Date dePaymentDueDate;

    /**
     * 延迟付款状态 0 未延迟 1 延迟付款  2 拒绝退款延迟
     */
    private int delayStatus;
    /**
     * 车方详情是否展示 0展示 1不展示
     */
    private int carShow;

    /**
     * 货方详情是否展示 0展示 1不展示
     */
    private int goodsShow;


    /**
     * 确认付款时间大于1天时：还剩 ？天自动结算，确认付款时间小于等于1天时:即将自动结算
     */
    private String confirmPaymentRemark;
    /**
     * 限时货源标识0和null不是1是
     */
    private Integer timeLimitIdentification;

    /**
     * 状态描述
     */
    @Getter
    @Setter
    private String statusDesc;

    /**
     * 延迟退款状态 0 未延迟退款 1 延迟退款
     */
    @Getter
    @Setter
    private Integer delayRefundStatus;


    /**
     * 预计退款到账日期
     */
    @Getter
    @Setter
    private Date deRefundDueDate;

    /**
     * 确认退款时间大于1天时：还剩 ？天自动结算，确认付款时间小于等于1天时:即将自动结算
     */
    @Getter
    @Setter
    private String confirmRefundRemark;
    /**
     * 订金类型（0不退还；1退还）
     */
    @Getter
    @Setter
    private Integer refundFlag;

    /**
     * 三方平台类型 1:满帮
     */
    @Getter
    @Setter
    private Integer thirdpartyPlatformType;

    /**
     * 三方平台单号
     */
    @Getter
    @Setter
    private String thirdpartyPlatformOrderNo;


    /**
     * 技术服务费/TransportOrdersListBean.java
     */
    @Getter
    @Setter
    private String tecServiceFee;

    /**
     * 货源来源（1货主；2调度客服；3:个人货主 4:运满满）
     */
    @Getter
    @Setter
    private Integer sourceType;

    /**
     * 车方技术服务费退款金额 单位元
     */
    @Getter
    @Setter
    private Long tecServiceFeeRefundAmount;

    /**
     * 车方技术服务费退款到账时间
     */
    @Getter
    @Setter
    private Date tecServiceFeeRefundTime;


    public Integer getTimeLimitIdentification() {
        return timeLimitIdentification;
    }

    public void setTimeLimitIdentification(Integer timeLimitIdentification) {
        this.timeLimitIdentification = timeLimitIdentification;
    }

    public Integer getIsDealCar() {
        return isDealCar;
    }

    public void setIsDealCar(Integer isDealCar) {
        this.isDealCar = isDealCar;
    }

    public BigDecimal getPayServiceCharge() {
        return payServiceCharge;
    }

    public void setPayServiceCharge(BigDecimal payServiceCharge) {
        this.payServiceCharge = payServiceCharge;
    }

    private Integer carriageFee;

    public Integer getCarriageFee() {
        return carriageFee;
    }

    public void setCarriageFee(Integer carriageFee) {
        this.carriageFee = carriageFee;
    }

    public String getHeadCity() {
        return headCity;
    }

    public void setHeadCity(String headCity) {
        this.headCity = headCity;
    }

    public String getHeadNo() {
        return headNo;
    }

    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }

    public String getTailCity() {
        return tailCity;
    }

    public void setTailCity(String tailCity) {
        this.tailCity = tailCity;
    }

    public String getTailNo() {
        return tailNo;
    }

    public void setTailNo(String tailNo) {
        this.tailNo = tailNo;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public String getTsOrderNo() {
        return tsOrderNo;
    }

    public void setTsOrderNo(String tsOrderNo) {
        this.tsOrderNo = tsOrderNo;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
    }

    public String getPayUserName() {
        return payUserName;
    }

    public void setPayUserName(String payUserName) {
        this.payUserName = payUserName;
    }

    public String getPayCellPhone() {
        return payCellPhone;
    }

    public void setPayCellPhone(String payCellPhone) {
        this.payCellPhone = payCellPhone;
    }

    public String getTotalOrderAmount() {
        return totalOrderAmount;
    }

    public void setTotalOrderAmount(String totalOrderAmount) {
        this.totalOrderAmount = totalOrderAmount;
    }

    public String getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(String couponAmount) {
        this.couponAmount = couponAmount;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Integer getDelayRefundStatus() {
        return delayRefundStatus;
    }

    public void setDelayRefundStatus(Integer delayRefundStatus) {
        this.delayRefundStatus = delayRefundStatus;
    }

    public Date getDeRefundDueDate() {
        return deRefundDueDate;
    }

    public void setDeRefundDueDate(Date deRefundDueDate) {
        this.deRefundDueDate = deRefundDueDate;
    }

    public String getConfirmRefundRemark() {
        return confirmRefundRemark;
    }

    public void setConfirmRefundRemark(String confirmRefundRemark) {
        this.confirmRefundRemark = confirmRefundRemark;
    }

    public Integer getRefundFlag() {
        return refundFlag;
    }

    public void setRefundFlag(Integer refundFlag) {
        this.refundFlag = refundFlag;
    }

    public Integer getThirdpartyPlatformType() {
        return thirdpartyPlatformType;
    }

    public void setThirdpartyPlatformType(Integer thirdpartyPlatformType) {
        this.thirdpartyPlatformType = thirdpartyPlatformType;
    }

    public String getThirdpartyPlatformOrderNo() {
        return thirdpartyPlatformOrderNo;
    }

    public void setThirdpartyPlatformOrderNo(String thirdpartyPlatformOrderNo) {
        this.thirdpartyPlatformOrderNo = thirdpartyPlatformOrderNo;
    }

    public String getCostStatus() {
        return costStatus;
    }

    public void setCostStatus(String costStatus) {
        this.costStatus = costStatus;
    }

    public String getActionTime() {
        return actionTime;
    }

    public void setActionTime(String actionTime) {
        this.actionTime = actionTime;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getPayUserId() {
        return payUserId;
    }

    public void setPayUserId(String payUserId) {
        this.payUserId = payUserId;
    }

    public CreditUserInfo getDriverUserInfo() {
        return driverUserInfo;
    }

    public void setDriverUserInfo(CreditUserInfo driverUserInfo) {
        this.driverUserInfo = driverUserInfo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExceptionTime() {
        return exceptionTime;
    }

    public void setExceptionTime(String exceptionTime) {
        this.exceptionTime = exceptionTime;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getRefundReason() {
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }

    public String getOldPaymentStatus() {
        return oldPaymentStatus;
    }

    public void setOldPaymentStatus(String oldPaymentStatus) {
        this.oldPaymentStatus = oldPaymentStatus;
    }

    public String getPayEndTime() {
        return payEndTime;
    }

    public void setPayEndTime(String payEndTime) {
        this.payEndTime = payEndTime;
    }

    public String getExResult() {
        return exResult;
    }

    public void setExResult(String exResult) {
        this.exResult = exResult;
    }

    public String getExComment() {
        return exComment;
    }

    public void setExComment(String exComment) {
        this.exComment = exComment;
    }

    public String getExResultComment() {
        return exResultComment;
    }

    public void setExResultComment(String exResultComment) {
        this.exResultComment = exResultComment;
    }

    public String getCarAmount() {
        return carAmount;
    }

    public void setCarAmount(String carAmount) {
        this.carAmount = carAmount;
    }

    public String getGoodsAmount() {
        return goodsAmount;
    }

    public void setGoodsAmount(String goodsAmount) {
        this.goodsAmount = goodsAmount;
    }

    public String getPlatformServiceAmount() {
        return platformServiceAmount;
    }

    public void setPlatformServiceAmount(String platformServiceAmount) {
        this.platformServiceAmount = platformServiceAmount;
    }

    public String getCarServiceAmount() {
        return carServiceAmount;
    }

    public void setCarServiceAmount(String carServiceAmount) {
        this.carServiceAmount = carServiceAmount;
    }

    public String getExStatus() {
        return exStatus;
    }

    public void setExStatus(String exStatus) {
        this.exStatus = exStatus;
    }

    public Date getDePaymentDueDate() {
        return dePaymentDueDate;
    }

    public void setDePaymentDueDate(Date dePaymentDueDate) {
        this.dePaymentDueDate = dePaymentDueDate;
    }

    public int getDelayStatus() {
        return delayStatus;
    }

    public void setDelayStatus(int delayStatus) {
        this.delayStatus = delayStatus;
    }

    public int getCarShow() {
        return carShow;
    }

    public void setCarShow(int carShow) {
        this.carShow = carShow;
    }

    public int getGoodsShow() {
        return goodsShow;
    }

    public void setGoodsShow(int goodsShow) {
        this.goodsShow = goodsShow;
    }

    public String getConfirmPaymentRemark() {
        return confirmPaymentRemark;
    }

    public void setConfirmPaymentRemark(String confirmPaymentRemark) {
        this.confirmPaymentRemark = confirmPaymentRemark;
    }

    public List<InfoFeeExceptionInfo> getInfoFeeExceptionInfoList() {
        return infoFeeExceptionInfoList;
    }

    public void setInfoFeeExceptionInfoList(List<InfoFeeExceptionInfo> infoFeeExceptionInfoList) {
        this.infoFeeExceptionInfoList = infoFeeExceptionInfoList;
    }
}
