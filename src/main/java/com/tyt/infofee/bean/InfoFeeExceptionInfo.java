package com.tyt.infofee.bean;


public class InfoFeeExceptionInfo {


    private String exStatus = "";

    private String exComment = "";

    //动作时间
    private String actionTime = "";

    //异常上报时间
    private String exceptionTime = "";

    //异常上报撤销时间
    private String cancelExTime="";

    //异常处理意见
    private String exResult = "";

    private String exResultComment = "";

    private String carAmount = ""; //单位分

    private String goodsAmount = ""; //单位分

    private String carServiceAmount = ""; //技术服务费车方分配金额 单位分

    private String platformServiceAmount = ""; //技术服务费平台分配金额 单位分


    public String getExStatus() {
        return exStatus;
    }

    public void setExStatus(String exStatus) {
        this.exStatus = exStatus;
    }

    public String getExComment() {
        return exComment;
    }

    public void setExComment(String exComment) {
        this.exComment = exComment;
    }

    public String getActionTime() {
        return actionTime;
    }

    public void setActionTime(String actionTime) {
        this.actionTime = actionTime;
    }

    public String getExceptionTime() {
        return exceptionTime;
    }

    public void setExceptionTime(String exceptionTime) {
        this.exceptionTime = exceptionTime;
    }

    public String getExResult() {
        return exResult;
    }

    public void setExResult(String exResult) {
        this.exResult = exResult;
    }

    public String getExResultComment() {
        return exResultComment;
    }

    public void setExResultComment(String exResultComment) {
        this.exResultComment = exResultComment;
    }

    public String getCarAmount() {
        return carAmount;
    }

    public void setCarAmount(String carAmount) {
        this.carAmount = carAmount;
    }

    public String getGoodsAmount() {
        return goodsAmount;
    }

    public void setGoodsAmount(String goodsAmount) {
        this.goodsAmount = goodsAmount;
    }

    public String getCancelExTime() {
        return cancelExTime;
    }

    public void setCancelExTime(String cancelExTime) {
        this.cancelExTime = cancelExTime;
    }

    public String getCarServiceAmount() {
        return carServiceAmount;
    }

    public void setCarServiceAmount(String carServiceAmount) {
        this.carServiceAmount = carServiceAmount;
    }

    public String getPlatformServiceAmount() {
        return platformServiceAmount;
    }

    public void setPlatformServiceAmount(String platformServiceAmount) {
        this.platformServiceAmount = platformServiceAmount;
    }

    @Override
    public String toString() {
        return "InfoFeeExceptionInfo{" +
                "exStatus='" + exStatus + '\'' +
                ", exComment='" + exComment + '\'' +
                ", actionTime='" + actionTime + '\'' +
                ", exceptionTime='" + exceptionTime + '\'' +
                ", cancelExTime='" + cancelExTime + '\'' +
                ", exResult='" + exResult + '\'' +
                ", exResultComment='" + exResultComment + '\'' +
                ", carAmount='" + carAmount + '\'' +
                ", goodsAmount='" + goodsAmount + '\'' +
                ", carServiceAmount='" + carServiceAmount + '\'' +
                ", platformServiceAmount='" + platformServiceAmount + '\'' +
                '}';
    }
}
