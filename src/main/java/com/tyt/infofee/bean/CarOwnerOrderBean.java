package com.tyt.infofee.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarOwnerOrderBean {
	
	Long id;//订单表ID
	String goodsOrderNo;//运单号
	String carOwnerTelephone;//车主联系电话
	Long payAgencyMoney;//	已付信息费金额（分）
	Date payEndTime;//车主支付成功的时间(毫秒)
	Integer payChannel;//1支付宝 2易宝/银行卡 3微信
	Date goodsOwnerAgreeTime;//货主同意装货时间（毫秒）
	Date carOwnerLoadfinishedTime;//车主装货完成时间（毫秒）
	Date exceptionTime;//异常上报时间（毫秒）
	Date exceptionFinishedTime;//异常上报处理完成时间
	String lastResult;//最终意见
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getGoodsOrderNo() {
		return goodsOrderNo;
	}
	public void setGoodsOrderNo(String goodsOrderNo) {
		this.goodsOrderNo = goodsOrderNo;
	}
	public String getCarOwnerTelephone() {
		return carOwnerTelephone;
	}
	public void setCarOwnerTelephone(String carOwnerTelephone) {
		this.carOwnerTelephone = carOwnerTelephone;
	}
	public Long getPayAgencyMoney() {
		return payAgencyMoney;
	}
	public void setPayAgencyMoney(Long payAgencyMoney) {
		this.payAgencyMoney = payAgencyMoney;
	}
	public Date getPayEndTime() {
		return payEndTime;
	}
	public void setPayEndTime(Date payEndTime) {
		this.payEndTime = payEndTime;
	}
	public Integer getPayChannel() {
		return payChannel;
	}
	public void setPayChannel(Integer payChannel) {
		this.payChannel = payChannel;
	}
	public Date getGoodsOwnerAgreeTime() {
		return goodsOwnerAgreeTime;
	}
	public void setGoodsOwnerAgreeTime(Date goodsOwnerAgreeTime) {
		this.goodsOwnerAgreeTime = goodsOwnerAgreeTime;
	}
	public Date getCarOwnerLoadfinishedTime() {
		return carOwnerLoadfinishedTime;
	}
	public void setCarOwnerLoadfinishedTime(Date carOwnerLoadfinishedTime) {
		this.carOwnerLoadfinishedTime = carOwnerLoadfinishedTime;
	}
	public Date getExceptionTime() {
		return exceptionTime;
	}
	public void setExceptionTime(Date exceptionTime) {
		this.exceptionTime = exceptionTime;
	}
	public Date getExceptionFinishedTime() {
		return exceptionFinishedTime;
	}
	public void setExceptionFinishedTime(Date exceptionFinishedTime) {
		this.exceptionFinishedTime = exceptionFinishedTime;
	}
	public String getLastResult() {
		return lastResult;
	}
	public void setLastResult(String lastResult) {
		this.lastResult = lastResult;
	}
	
}
