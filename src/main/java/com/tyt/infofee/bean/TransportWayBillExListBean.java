package com.tyt.infofee.bean;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportWayBillExListBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 6171737656162374694L;
	private Long id;
	private Long orderId; // 只有企业版异常上报详情时，才存在
	private String tsOrderNo;
	private Long tsId;
	private Long userId;
	private String startPoint;
	private String destPoint;
	private String taskContent;
	private Date publishTime;
	private String payLinkPhone;
	private Long payAmount;
	private Date exTime;
	private String exParty;
	private String exType;
	private String exOther;
	private String exStatus;

	/**
	 * 默认值，此属性为订单内tyt库的唯一标识，由'tyt-'加Id组成
	 */
	private String uniqueId;
	/**
	 * C端订单类型标识，默认为0
	 */
	private Integer orderType = 0;

	public Long getId() {
		return id;
	}
	public String getTsOrderNo() {
		return tsOrderNo;
	}
	public Long getTsId() {
		return tsId;
	}
	public Long getUserId() {
		return userId;
	}
	public String getStartPoint() {
		return startPoint;
	}
	public String getDestPoint() {
		return destPoint;
	}
	public String getTaskContent() {
		return taskContent;
	}
	public Date getPublishTime() {
		return publishTime;
	}
	public String getPayLinkPhone() {
		return payLinkPhone;
	}
	public Long getPayAmount() {
		return payAmount;
	}
	public Date getExTime() {
		return exTime;
	}
	public String getExParty() {
		return exParty;
	}
	public String getExType() {
		return exType;
	}
	public String getExOther() {
		return exOther;
	}
	public String getExStatus() {
		return exStatus;
	}
	public void setId(Long id) {
		this.id = id;
		this.uniqueId = "plat-" + id;
	}
	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}
	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}
	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}
	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}
	public void setPayLinkPhone(String payLinkPhone) {
		this.payLinkPhone = payLinkPhone;
	}
	public void setPayAmount(Long payAmount) {
		this.payAmount = payAmount;
	}
	public void setExTime(Date exTime) {
		this.exTime = exTime;
	}
	public void setExParty(String exParty) {
		this.exParty = exParty;
	}
	public void setExType(String exType) {
		this.exType = exType;
	}
	public void setExOther(String exOther) {
		this.exOther = exOther;
	}
	public void setExStatus(String exStatus) {
		this.exStatus = exStatus;
	}


//	@JsonIgnore
	public String getUniqueId() {
		return uniqueId;
	}

	public void setUniqueId(String uniqueId) {
		this.uniqueId = uniqueId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
}
