package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2017-5-17下午8:08:54
 * @description
 */
@SuppressWarnings("serial")
public class OriginalStandardMsg implements java.io.Serializable {

	private Integer lengthWidthHeightDisplay;
	private Integer weightDisplay;
	private String displayContent;
	private String machineType;
	private String brand;
	private String type;
	private String weight;
	private String length;
	private String width;
	private String height;



	public Integer getLengthWidthHeightDisplay() {
		return lengthWidthHeightDisplay;
	}

	public Integer getWeightDisplay() {
		return weightDisplay;
	}

	public String getDisplayContent() {
		return displayContent;
	}

	public void setLengthWidthHeightDisplay(Integer lengthWidthHeightDisplay) {
		this.lengthWidthHeightDisplay = lengthWidthHeightDisplay;
	}

	public void setWeightDisplay(Integer weightDisplay) {
		this.weightDisplay = weightDisplay;
	}

	public void setDisplayContent(String displayContent) {
		this.displayContent = displayContent;
	}

	public String getMachineType() {
		return machineType;
	}

	public void setMachineType(String machineType) {
		this.machineType = machineType;
	}

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getLength() {
		return length;
	}

	public void setLength(String length) {
		this.length = length;
	}

	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
