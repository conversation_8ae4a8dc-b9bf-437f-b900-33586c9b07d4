package com.tyt.infofee.bean;

import com.tyt.base.bean.BaseParameter;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/09/22 18:25
 * @Version 1.0
 **/
@Data
public class ActivityUserAddressBean{

    @NotBlank(message = "用户ID不能为空")
    private Long userId;

    @NotBlank(message = "活动ID不能为空")
    private Long activityId;

    @NotBlank(message = "用户名称不能为空")
    private String userName;

    @NotBlank(message = "用户手机号不能为空")
    private String userCellPhone;

    @NotBlank(message = "省份不能为空")
    private String province;

    @NotBlank(message = "市不能为空")
    private String city;

    @NotBlank(message = "区不能为空")
    private String area;

    @NotBlank(message = "详细地址不能为空")
    private String detailAddr;
}
