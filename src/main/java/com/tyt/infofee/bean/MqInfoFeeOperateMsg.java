package com.tyt.infofee.bean;

import java.io.Serializable;

/**
 * 20181220信息费流转操作 --包含气泡，短信，日志，消息中心，push消息
 */
public class MqInfoFeeOperateMsg extends MqBaseMessageBean implements Serializable {

    private Integer opStatus;//操作方式 1:支付信息费 2:装货完成支付信息费 3:货方发起信息费退款 4:车方同意退款 5:车主拒绝退款
                            //6:车主发起信息费冻结 7:车方信息费解冻 8:车主异常上报 9:货方异常上报 10:信息费即将7天自动支付 11:

    //气泡
    private Long shipperUserId; //货主id
    private Long carOwnerUserId;//车主ID

    //日志
    private Long tsId; //mainb表id
    private String tsOrderNo; //运单编号
    private Long orderId;//订单id
    private String payNo;//tyt_transport_orders的pay_no 和 tyt_old_order表order_id

    private String startPoint; //出发地
    private String destPoint;//目的地
    private String taskContent;// 货物内容
    private String amount;//金额
    private Integer refundType;//1 全额退款 2:部分退款
    private Integer tecRefundType;// 技术服务费 1全额退款
    private Integer refundOriginator;//退款发起方 1车方  2:货方

    private String infoFeeServiceFee;// 平台服务费

    private String totalOrderAmount;//订单总金额
    /**
     * 技术服务费 单位分
     */
    private String tecServiceFee;

    /**
     * 技术服务费 平台分配金额
     */
    private String platformServiceAmount;

    /**
     * 技术服务费 车方分配金额
     */
    private String carServiceAmount;

    public String getInfoFeeServiceFee() {
        return infoFeeServiceFee;
    }

    public void setInfoFeeServiceFee(String infoFeeServiceFee) {
        this.infoFeeServiceFee = infoFeeServiceFee;
    }

    public String getPlatformServiceAmount() {
        return platformServiceAmount;
    }

    public void setPlatformServiceAmount(String platformServiceAmount) {
        this.platformServiceAmount = platformServiceAmount;
    }

    public String getCarServiceAmount() {
        return carServiceAmount;
    }

    public void setCarServiceAmount(String carServiceAmount) {
        this.carServiceAmount = carServiceAmount;
    }

    public Integer getOpStatus() {
        return opStatus;
    }

    public void setOpStatus(Integer opStatus) {
        this.opStatus = opStatus;
    }

    public Long getShipperUserId() {
        return shipperUserId;
    }

    public void setShipperUserId(Long shipperUserId) {
        this.shipperUserId = shipperUserId;
    }

    public Long getCarOwnerUserId() {
        return carOwnerUserId;
    }

    public void setCarOwnerUserId(Long carOwnerUserId) {
        this.carOwnerUserId = carOwnerUserId;
    }

    public Long getTsId() {
        return tsId;
    }

    public void setTsId(Long tsId) {
        this.tsId = tsId;
    }

    public String getTsOrderNo() {
        return tsOrderNo;
    }

    public void setTsOrderNo(String tsOrderNo) {
        this.tsOrderNo = tsOrderNo;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public Integer getRefundType() {
        return refundType;
    }

    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }

    public Integer getTecRefundType() {
        return tecRefundType;
    }

    public void setTecRefundType(Integer tecRefundType) {
        this.tecRefundType = tecRefundType;
    }

    public Integer getRefundOriginator() {
        return refundOriginator;
    }

    public void setRefundOriginator(Integer refundOriginator) {
        this.refundOriginator = refundOriginator;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public String getTotalOrderAmount() {
        return totalOrderAmount;
    }

    public void setTotalOrderAmount(String totalOrderAmount) {
        this.totalOrderAmount = totalOrderAmount;
    }

    public String getTecServiceFee() {
        return tecServiceFee;
    }

    public void setTecServiceFee(String tecServiceFee) {
        this.tecServiceFee = tecServiceFee;
    }
}
