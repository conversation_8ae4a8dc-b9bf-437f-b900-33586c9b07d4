package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.orderlimit.bean.AcceptOrderLimitInfo;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class GoodsSingleDetailResultBean {

    private SingleGoodsDetailBean detailBean;
    private Object agencyMoneyList;
    private Integer isPaySuccess = 0;// 是否显示详情页面的去支付按钮，0显示，1不显示
    private String hasMakeOrder;// 是否下过订单：0否、1是
    private TransportUserBean transportUserBean;// 货主信息
    /**
     * 服务器时间
     */
    private Date time;

    /**
     * 货源详情页风险提示
     */
    private String riskTip;
    /**
     * 货源详情页担保提示
     */
    private String assureTip;
    /**
     * 货源详情页担保提示链接
     */
    private String assureTipLink;

    /**
     * 显示超额保障提示文案
     */
    private String excessCoverageTip;
    /**
     * 是否显示送订金券标签： 1是 0否
     */
    private Integer showDepositCouponsLabel = 0;

    /**
     * 是否是参与现金奖活动的货源 1:是；0:否
     */
    private Integer isCashPrizeActivityGoods;

    /**
     * 是否收藏
     */
    private Long collectId;

    /**
     * 接单限制信息
     */
    private AcceptOrderLimitInfo acceptOrderLimitInfo;

    private CoverGoodsDialInfo coverGoodsDialInfo;

    private Integer callingStatus;
    /**
     * 是否跳过权益校验：1是，0否（默认）（比如：专车货源没有拨打权益的时候也能拨打和支付，普通货源指派专车时也能拨打和支付）
     */
    private Integer skipPermission = 0;

    /**
     * 是否弹出价格提示框：1是，0否（默认）
     */
    private Integer isPopupPriceBox;

    /**
     * 各种提示文案
     */
    private TipInfoCollective tipInfoCollective;

    /**
     * 保障透传文案
     */
    private ExcessCoveragePrompt excessCoveragePrompt;


    @Data
    public static class CoverGoodsDialInfo {
        /**
         * 是否在配置名单内
         */
        private boolean dialUser;

        /**
         * 是否在配置捂货白名单内
         */
        private boolean dialWhiteUser;
        /**
         * x是否过期
         */
        private boolean expire;
        /**
         * 是否使用过免责卡
         */
        private Boolean useN;
        /**
         * 剩余免责卡数量
         */
        private Integer leftN;
        /**
         * 配置的x的时间, 单位秒
         */
        private Integer configXSeconds;
        /**
         * 配置的y的时间, 单位秒
         */
        private Integer configYSeconds;
        /**
         * y的剩余时间, 单位秒
         */
        private Integer leftYSeconds;
        /**
         * 倒计时是否结束
         */
        private boolean countDownFinished;
    }

    /**
     * 各种提示文案
     */
    @Getter
    @Setter
    public static class TipInfoCollective {
        private String topPageTip; // 顶部提示
        private String middlePageTip; // 中部提示
        private Integer toastType; // 进入详情页的toast：0不弹；1送曝光卡toast；
        private String toastTitle; // toast标题
        private String toastContent; // toast内容
        private String exposureContent; // 曝光提示内容
        private Integer giveawayNum; //赠送数
        private String lastCallFeedBack; // 最新的拨打反馈文案
    }

    /**
     * 保障透传文案
     */
    @Getter
    @Setter
    public static class ExcessCoveragePrompt {
        private Integer type; // 类型：1平台保障 2专属保障 3优车货源 4专车保障
        private String title; // 标题
        private String content; // 文案
        private String url; // 跳转链接
    }

    /**
     * 找货次数
     */
    private Integer searchPermissionNum;

}
