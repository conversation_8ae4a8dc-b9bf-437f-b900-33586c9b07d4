package com.tyt.infofee.bean;

/**
 * mq消息的基类
 *
 * <AUTHOR>
 * @date 2016-11-21下午1:56:04
 * @description
 */
public class MqBaseMessageBean {
	/*
	 * 消息的类型
	 */
	public int messageType;
	/*
	 * 消息的序列号，每个消息唯一
	 */
	public String messageSerailNum;

	/*
	 * 微信支付成功回调
	 */
	public static int MESSAGETYPE_INFOFEE_WEIXIN_PAYSUC_CALLBACK = 1;
	/*
	 * 支付宝支付成功回调
	 */
	public static int MESSAGETYPE_INFOFEE_ZHIFUBAO_PAYSUC_CALLBACK = 2;
	/*
	 * 医保支付成功回调
	 */
	public static int MESSAGETYPE_INFOFEE_YIBAO_PAYSUC_CALLBACK = 3;
	/*
	 * 退款
	 */
	public static int MESSAGETYPE_RERUND = 4;
	/*
	 * 提现
	 */
	public static int MESSAGETYPE_WITHDRAW = 5;
	/*
	 * 装货完成
	 */
	public static int MESSAGETYPE_AGREE_LOADING = 6;
	/*
	 * 异常上报退款
	 */
	public static int MESSAGETYPE_EXCEPTION_REPORT = 7;
	/*
	 * 发送短信
	 */
	public static int MESSAGETYPE_SEDN_MESSAGE = 8;
	// 异常
	public static int MESSAGETYPE_EXCEPTION_MESSAGE = 10;
	// 用户初始化
	public static int MESSAGETYPE_USER_INIT_MESSAGE = 11;

	// 无效货源过滤
	public static int MESSAGETYPE_NULLIFY_TRANSPORT_MESSAGE = 12;

	/*
	 * 地图距离信息补全
	 */
	public static final int MESSAGETYPE_MAP_TASK = 16;

	// 精准货源推荐
	public static int MESSAGETYPE_RECOMMEND_TRANSPORT_MESSAGE = 1001;

	// 猜你喜欢列表生成
	public static int MESSAGETYPE_GUESS_YOU_LIKE_MESSAGE = 1002;

	// 精准货源推荐 找货开关开启
	public static int MESSAGETYPE_RECOMMEND_TRANSPORT_FINDGOOD_MESSAGE = 1003;

	// 猜你喜欢生成-全量MQ
	public static int MESSAGETYPE_GUESS_YOU_LIKE_ALL_MESSAGE = 1004;

	// 猜你喜欢生成-增量MQ
	public static int MESSAGETYPE_GUESS_YOU_LIKE_INCREMENT_MESSAGE = 1005;

	// 车辆偏好缓冲-MQ
	public static int MESSAGETYPE_CAR_CACHE_MESSAGE = 1006;
	/*
	 * 信息费支付成功状态同步消息
	 */
	public static int MESSAGETYPE_INFOFEE_PAY_SUC_SYNC_STATUS_MESSAGE = 17;
	/*
	 * 信息费退款成功状态同步消息
	 */
	public static int MESSAGETYPE_INFOFEE_REFUND_SUC_SYNC_STATUS_MESSAGE = 18;
	/*
	 * 货主同意装货状态同步
	 */
	public static int MESSAGETYPE_AGREE_LOAD_SYNC_STATUS_MESSAGE = 19;
	/*
	 * 异常上报状态同步
	 */
	public static int MESSAGETYPE_EXCEPTION_REPORT_STATUS_SYNC_STATUS_MESSAGE = 20;
	/*
	 * 车主装货完成状态同步
	 */
	public static int MESSAGETYPE_LOAD_OVER_STATUS_SYNC_STATUS_MESSAGE = 21;
	/*
	 * 系统装货完成状态同步
	 */
	public static int MESSAGETYPE_SYSTEM_LOAD_OVER_STATUS_SYNC_STATUS_MESSAGE = 22;
	/*
	 * 货源撤销状态同步
	 */
	public static int MESSAGETYPE_GOODS_REVOCATION_STATUS_SYNC_STATUS_MESSAGE = 23;

	/*
	 * 客服系统 货物与意向车主关系建立MQ
	 */
	public static final int TYT_CAR_OWNER_INTENTION = 24;

	/*
	 * 修改意向表状态
	 */
	public static final int TYT_CAR_OWNER_STATUS = 25;

	/*
	 * PLAT货物与意向车主关系建立MQ
	 */
	public static final int TYT_CAR_OWNER_CREATE_INTENTION = 26;

	/**
	 * 高德计算距离MQ
	 */
	public static final int MESSAGETYPE_AMAP_DISTANCE = 27;
	/*
	 * 同步认证车辆状态
	 */
	public static final int SYNC_AUTH_CAR_STATUS_MESSAGE = 30;
	/*
	 * 同步认证车辆状态
	 */
	public static final int RESEND_GOODS_MESSAGE = 31;

	/**
	 * 3周年庆典活动push
	 */
	public static final int PUSH_ACVITITY_MESSAGE = 33;
	/**
	 * 企业货源发布通知熟车（短信）
	 */
	public static final int CORP_PUB_NOTIFY_FAMILIAR = 34;

	/**
	 * 企业通知相关（通知栏）
	 */
	public static final int PUSH_NOTIFY_FOR_CORP = 35;

    /**
     * APP信息费操作
     */
    public static final int INFO_FEE_OPERATE_DEAL = 36;

	/**
	 * 用户活动
	 */
	public static final int USER_ACVITITY_MESSAGE = 39;

	// 2019-07-02 zgz 增加分配用户权益的消息
	public static int MESSAGETYPE_USER_PERMISSION_MESSAGE = 40;

	/**
	 * 用户活动
	 */
	public static final int GIVE_USER_COUPON = 41;

	//2019-10-15 xyy 退款mq
	public static final int REFUND_MESSAGE = 43;

	// 2019-10-25 处理货源设置成交、车辆定位sms、push内容
	public static int TRANSPORT_DEAL_AFTER_MESSAGE = 44;

	//2019-12-06 货站发送货源广播消息
	public static final int SEND_GOODS_BROADCAST_MESSAGE = 45;
	// 2021-09-01 定向用户抽奖活动
	public static final int SEND_COUPON_BY_ACTIVITY_ID = 46;

	//2020-03-11 抽奖活动发送中奖push消息
	public static final int SEND_WINNING_INFO_MESSAGE = 50;

	/**
	 * 调度 mq  push
	 */
	public static final int PUSH_DISPATCH_MESSAGE = 59;

	/**
	 * transport backend message
	 * 设为成交车 取消设为成交车 发送ma 推送短信，小程序订阅消息
	 */
	public static final int TRANSPORT_BACKEND_MESSAGE = 62;
	/**
	 * 登录相关操作
	 */
	public static final int TYTPC_LOGIN_RELATE_OPERATION = 52;

	/**
	 * 企业货源接单成功发送mq
	 */
	public static final int TRANSPORT_BACKEND_STATUS = 63;

	/**
	 * 企业货源订单添加承运车辆成功发送mq
	 */
	public static final int TRANSPORT_BACKEND_CAR = 64;
	/**
	 * 企业货源状态
	 */
	public static int MESSAGETYPE_BACKEND_STATUS_MESSAGE = 65;
	/**
	 * 企业货源给后台调度员发送消息
	 */
	public static  int BACKEND_SEND = 69;

	// 货源撤销发送mq消息
	public static int MESSAGETYPE_BACKOUT_TRANSPORT_MESSAGE = 73;

	// 货源加价发送mq消息
	public static int MESSAGETYPE_ADDMONEY_TRANSPORT_MESSAGE = 76;

	/**
	 *  发送抢单豆
	 */
	public static final int SEND_DIAL_TIMES = 82;

	/**
	 * 专车派单
	 */
	public static final int SPECIAL_CAR_ASSIGN = 90;

	/**
	 *  裂变活动
	 */
	public static final int INVOTEE_USER_ACTIVITY = 96;

	/**
	 * 10000：查询订单信息接口MQ消息
	 */
	public static int MB_QUERY_ORDER_MESSAGE = 10000;

	/**
	 * 10001：取消订单接口MQ消息
	 */
	public static int MB_CANCEL_ORDER_MESSAGE = 10001;

	/**
	 * 10002：退订金接口MQ消息
	 */
	public static int MB_REFUND_DEPOSIT_ORDER_MESSAGE = 10002;


	/**
	 * 10003：发起异常MQ消息
	 */
	public static int MB_EXCEPTION_REQUEST_MESSAGE = 10003;

	/**
	 * 10010：货源下架MQ消息
	 */
	public static int MB_SYNC_TRANSPORT_WAYBILL_MESSAGE = 10010;

	/**
	 * 10011：货源编辑MQ消息
	 */
	public static int MB_SYNC_TRANSPORT_EDIT_MESSAGE = 10011;


	/**
	 * 20002: 运满满货源 - TYT完成异常上报处理通知YMM
	 */
	public static int YMM_ORDER_EX_FINISH_NOTIFY_MESSAGE = 20002;

	/**
	 * 20003: 运满满货源 - TYT车方发起异常上报处理通知YMM
	 */
	public static int YMM_CAR_CREATE_EX_NOTIFY_MESSAGE = 20003;

	/**
	 * 20004：特运通司机申请退订金
	 */
	public static final int TYT_DRIVER_REFUND_DEPOSIT_MESSAGE = 20004;

	/**
	 * 20005：特运通司机撤销异常上报通知运满满
	 */
	public static final int YMM_ORDER_EX_CANCEL_NOTIFY_MESSAGE = 20005;
	/**
	 *  订单操作 状态同步阔库
	 */
	public static final int ORDER_OPERATE_STATUS_SYNC = 1077;
	/**
	 *  订单操作 状态同步阔库
	 */
	public static final int ORDER_OPERATE_REASON = 1078;

	/**
	 *  钉钉推送消息
	 */
	public static final int DING_DING_PUSH_MESSAGE = 99012;

	/**
	 * 手动音转文分发
	 */
	public static final int ASR_TASK_CREATE_MESSAGE_DISTRIBUTE = 99010;

	public int getMessageType() {
		return messageType;
	}

	public void setMessageType(int messageType) {
		this.messageType = messageType;
	}

	public String getMessageSerailNum() {
		return messageSerailNum;
	}

	public void setMessageSerailNum(String messageSerailNum) {
		this.messageSerailNum = messageSerailNum;
	}

	@Override
	public String toString() {
		return "MqBaseMessageBean [messageType=" + messageType + ", messageSerailNum=" + messageSerailNum + "]";
	}
}
