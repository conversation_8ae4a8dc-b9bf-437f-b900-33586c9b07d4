package com.tyt.infofee.bean;

import org.apache.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.tyt.model.ResultMsgBean;

/**
 * 
 * <AUTHOR>
 * @date 2016-11-19上午10:44:45
 * @description
 */
@SuppressWarnings("serial")
public class PocketResultBean extends ResultMsgBean {
	/*
	 * 钱包余额
	 */
	private String remaining;
	/*
	 * 冻结余额
	 */
	private String frozenRemaining;

	public String getTotalRemaining() {
		Long result = 0L;
		if (StringUtils.isNotEmpty(remaining)) {
			result += Long.valueOf(remaining);
		}
		if (StringUtils.isNotBlank(frozenRemaining)) {
			result += Long.valueOf(frozenRemaining);
		}
		return String.valueOf(result);
	}

	public String getFrozenRemaining() {
		return frozenRemaining;
	}

	public void setFrozenRemaining(String frozenRemaining) {
		this.frozenRemaining = frozenRemaining;
	}

	public String getRemaining() {
		return remaining;
	}

	public void setRemaining(String remaining) {
		this.remaining = remaining;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
