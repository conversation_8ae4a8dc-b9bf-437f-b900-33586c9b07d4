package com.tyt.infofee.bean;

/**
 * 用户初始化消息bean
 * <AUTHOR>
 *
 */
public class MqMsg extends MqBaseMessageBean {

    private Long bachendId;

    private Long userId;

    private String userName;

    private Long dispatcherId;

    private String dispatcherName;

    private Integer type;

    public Long getBachendId() {
        return bachendId;
    }

    public void setBachendId(Long bachendId) {
        this.bachendId = bachendId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getDispatcherId() {
        return dispatcherId;
    }

    public void setDispatcherId(Long dispatcherId) {
        this.dispatcherId = dispatcherId;
    }

    public String getDispatcherName() {
        return dispatcherName;
    }

    public void setDispatcherName(String dispatcherName) {
        this.dispatcherName = dispatcherName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
