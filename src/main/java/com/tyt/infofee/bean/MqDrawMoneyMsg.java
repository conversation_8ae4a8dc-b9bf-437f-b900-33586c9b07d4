package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;

/**
 * 提现申请的消息
 * 
 * <AUTHOR>
 * @date 2016-11-24下午2:50:40
 * @description
 */
public class MqDrawMoneyMsg extends MqBaseMessageBean {
	/*
	 * 提现用户id
	 */
	private String userId;
	/*
	 * 提现金额，单位是分
	 */
	private Integer withdrawAccount;
	/*
	 * 提款申请id
	 */
	private Integer applyId;
	/*
	 * 提款申请银行名称
	 */
	private String bankName;

	public Integer getApplyId() {
		return applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public Integer getWithdrawAccount() {
		return withdrawAccount;
	}

	public void setWithdrawAccount(Integer withdrawAccount) {
		this.withdrawAccount = withdrawAccount;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}
