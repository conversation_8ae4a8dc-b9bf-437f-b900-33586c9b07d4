package com.tyt.infofee.bean;

import java.util.Date;

/**
 * @ClassName MqRevokeTransportSyncMsg
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/10/19 15:25
 * @Verdion 1.0
 **/

public class MqRevokeTransportSyncMsg extends MqBaseMessageBean {

    /**
     * 集团返回的合作商流水号,货源编辑、下架接口要用
     */
    private String partnerSerialNo;

    /**
     * 集团货源id 集团返回
     */
    private Long cargoId;

    /**
     * 合作商交易时间
     */
    private String partnerRequestTime;

    /**
     * 删除原因
     */
    private Integer deleteReason;

    private Long srcMsgId;


    public String getPartnerSerialNo() {
        return partnerSerialNo;
    }

    public void setPartnerSerialNo(String partnerSerialNo) {
        this.partnerSerialNo = partnerSerialNo;
    }

    public Long getCargoId() {
        return cargoId;
    }

    public void setCargoId(Long cargoId) {
        this.cargoId = cargoId;
    }

    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    public String getPartnerRequestTime() {
        return partnerRequestTime;
    }

    public void setPartnerRequestTime(String partnerRequestTime) {
        this.partnerRequestTime = partnerRequestTime;
    }

    public Integer getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(Integer deleteReason) {
        this.deleteReason = deleteReason;
    }
}
