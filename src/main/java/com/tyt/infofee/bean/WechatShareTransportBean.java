package com.tyt.infofee.bean;

import java.util.Date;

public class WechatShareTransportBean {

	private String shareUrl;//分享url
	private String shareTitle;//分享标题
	private String shareContent;//分享副标题
	private String iconUrl;//特运通图标地址

	private Long srcMsgId;// src_msg_id
	private String startPoint;// 出发地
	private String destPoint;// 目的地
	private String taskContent;// 货物内容
	private Date pubDate;// 发布时间
	private String price;// 运费
	private String isInfoFee;// 0不需要、1需要
	private Long userId;// 发布人ID
	private String userName;//发布人昵称
	private String headUrl;//发布人头像
	private Integer identityType;//注册身份
	private String identityTypeName;//发布人注册身份
	private Integer serveDays;//会员剩余天数
	private String tel;//电话
	private String tel3;//电话
	private String tel4;//电话
	private Integer isOwnerGoods;//是否是货主自己的货源。1 是 2 否
	private Integer status;//货源状态
	private String startCity;//出发地城市
	private Integer userClass;//
	private Integer verifyPhotoSign;//身份认证标识
	/**
	 * 分析类型 0:h5 1:小程序
	 */
	private Integer shareType;
	/**
	 * 分享小程序所需的小程序userName
	 */
	private String appletUserName;
	/**
	 * 分享小程序所需的小程序path
	 */
	private String path;
	/**
	 * 二维码信息
	 */
	private String qrCode;
	/**
	 * 分享小程序的环境 0：正式 1：测试 2：体验
	 */
	private Integer appletEnvironment;
	public String getShareUrl() {
		return shareUrl;
	}
	public void setShareUrl(String shareUrl) {
		this.shareUrl = shareUrl;
	}
	public String getShareTitle() {
		return shareTitle;
	}
	public void setShareTitle(String shareTitle) {
		this.shareTitle = shareTitle;
	}
	public String getShareContent() {
		return shareContent;
	}
	public void setShareContent(String shareContent) {
		this.shareContent = shareContent;
	}
	public String getIconUrl() {
		return iconUrl;
	}
	public void setIconUrl(String iconUrl) {
		this.iconUrl = iconUrl;
	}
	public Long getSrcMsgId() {
		return srcMsgId;
	}
	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}
	public String getStartPoint() {
		return startPoint;
	}
	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}
	public String getDestPoint() {
		return destPoint;
	}
	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}
	public String getTaskContent() {
		return taskContent;
	}
	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}
	public Date getPubDate() {
		return pubDate;
	}
	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}
	public String getPrice() {
		return price;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public String getIsInfoFee() {
		return isInfoFee;
	}
	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getHeadUrl() {
		return headUrl;
	}
	public void setHeadUrl(String headUrl) {
		this.headUrl = headUrl;
	}
	public Integer getIdentityType() {
		return identityType;
	}
	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}
	public String getIdentityTypeName() {
		return identityTypeName;
	}
	public void setIdentityTypeName(String identityTypeName) {
		this.identityTypeName = identityTypeName;
	}
	public Integer getServeDays() {
		return serveDays;
	}
	public void setServeDays(Integer serveDays) {
		this.serveDays = serveDays;
	}
	public String getTel() {
		return tel;
	}
	public void setTel(String tel) {
		this.tel = tel;
	}
	public String getTel3() {
		return tel3;
	}
	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}
	public String getTel4() {
		return tel4;
	}
	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}
	public Integer getIsOwnerGoods() {
		return isOwnerGoods;
	}
	public void setIsOwnerGoods(Integer isOwnerGoods) {
		this.isOwnerGoods = isOwnerGoods;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getStartCity() {
		return startCity;
	}
	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}
	public Integer getUserClass() {
		return userClass;
	}
	public void setUserClass(Integer userClass) {
		this.userClass = userClass;
	}
	public Integer getVerifyPhotoSign() {
		return verifyPhotoSign;
	}
	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
		this.verifyPhotoSign = verifyPhotoSign;
	}

	public Integer getShareType() {
		return shareType;
	}

	public void setShareType(Integer shareType) {
		this.shareType = shareType;
	}

	public String getAppletUserName() {
		return appletUserName;
	}

	public void setAppletUserName(String appletUserName) {
		this.appletUserName = appletUserName;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public Integer getAppletEnvironment() {
		return appletEnvironment;
	}

	public void setAppletEnvironment(Integer appletEnvironment) {
		this.appletEnvironment = appletEnvironment;
	}

	@Override
	public String toString() {
		return "WechatShareTransportBean [shareUrl=" + shareUrl + ", shareTitle=" + shareTitle + ", shareContent="
				+ shareContent + ", iconUrl=" + iconUrl + ", srcMsgId=" + srcMsgId + ", startPoint=" + startPoint
				+ ", destPoint=" + destPoint + ", taskContent=" + taskContent + ", pubDate=" + pubDate + ", price="
				+ price + ", isInfoFee=" + isInfoFee + ", userId=" + userId + ", userName=" + userName + ", headUrl="
				+ headUrl + ", identityType=" + identityType + ", identityTypeName=" + identityTypeName + ", serveDays="
				+ serveDays + ", tel=" + tel + ", tel3=" + tel3 + ", tel4=" + tel4 + ", isOwnerGoods=" + isOwnerGoods
				+ ", status=" + status + ", startCity=" + startCity + ", userClass=" + userClass + ", verifyPhotoSign="
				+ verifyPhotoSign + "]";
	}


}
