package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InfoFeeMyPublishBubbleResultBean implements Serializable{
	
//	typeOne	typeTwo	ifFunction	number
//	1	     1	     1	               车主方 待支付
//	1	     2	     1	               车主方 待装货
//	1	     3	     0	               车主方 已成交（变更为待装货完成）
//	1	     4	     0		      	   车主方 拒绝/退款
//	1	     5	     0	               车主方 违约/异常
//	1	     6	     0	               车主方 待同意（企业版新增）
//	1	     7	     0	               车主方 待评价
//	2	     1	     1	               发货方 待同意
//	2	     2	     0	               发货方 装货完成/线下成交
//	2	     3	     0	               发货方 违约/异常
//	2	     6	     0	               发货方 装货中

	/**
	 * 
	 */
	private static final long serialVersionUID = 8232626585148059435L;
	String type1;
	String type2;
	String ifFunction;
	Integer number;

	public String getType1() {
		return type1;
	}
	public String getType2() {
		return type2;
	}
	public void setType1(String type1) {
		this.type1 = type1;
	}
	public void setType2(String type2) {
		this.type2 = type2;
	}
	public String getIfFunction() {
		return ifFunction;
	}
	public void setIfFunction(String ifFunction) {
		this.ifFunction = ifFunction;
	}
	public Integer getNumber() {
		return number;
	}
	public void setNumber(Integer number) {
		this.number = number;
	}

	public InfoFeeMyPublishBubbleResultBean() {
	}

	public InfoFeeMyPublishBubbleResultBean(String type1, String type2, String ifFunction, Integer number) {
		this.type1 = type1;
		this.type2 = type2;
		this.ifFunction = ifFunction;
		this.number = number;
	}
}
