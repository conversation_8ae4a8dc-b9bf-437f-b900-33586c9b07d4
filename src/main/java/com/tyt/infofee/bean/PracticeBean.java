package com.tyt.infofee.bean;

import com.tyt.model.PracticeTheCar;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PracticeBean implements Serializable {

    private static final long serialVersionUID = 1L;

    //用户是否实名认证
    private Integer identityAuth;

    //用户真实姓名
    private String trueName;

    //用户手机号
    private String cellPhone;

    //用id
    private Long id;

    //交易次数
    private Integer tradeSuccessfully;

    //熟车列表信息
    private List<PracticeTheCar> PracticeList;

    //熟车数量
    private Integer PracticeCount;

}
