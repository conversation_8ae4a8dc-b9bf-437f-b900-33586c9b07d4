package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 
 * <AUTHOR>
 * @date 2016-11-19上午11:30:58
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserWalletFlowBean {
	/*
	 * 流水金额，单位是分
	 */
	private String money;
	/*
	 * 运单号，货物的唯一标示，重发该字段的值也不变
	 */
	private String waybillNumber;
	/*
	 * 银行名称
	 */
	private String payBankName;
	/*
	 * 支付渠道的名称
	 */
	private String payChannelName;
	/*
	 * 流水时间
	 */
	private String time;
	/*
	 * 流水类型
	 */
	private Integer type;
	/*
	 * 会员费支付内部订单号，该字段仅用于会员费支付的信息
	 */
	private String orderNumber;

	public String getOrderNumber() {
		return orderNumber;
	}

	public void setOrderNumber(String orderNumber) {
		this.orderNumber = orderNumber;
	}

	public String getMoney() {
		if (money != null && (money.startsWith("-") || money.startsWith("+"))) {
			String symbol = money.substring(0, 1);
			double moneyInYuan = Double.valueOf(money.substring(1)) / 100.0;
			return symbol + moneyInYuan + ((moneyInYuan + "").endsWith("0") ? "0" : "");
		} else {
			return money + ((money + "").endsWith("0") ? "0" : "");
		}
	}

	public void setMoney(String money) {
		this.money = money;
	}

	public String getWaybillNumber() {
		return waybillNumber;
	}

	public void setWaybillNumber(String waybillNumber) {
		this.waybillNumber = waybillNumber;
	}

	public String getPayBankName() {
		return payBankName;
	}

	public void setPayBankName(String payBankName) {
		this.payBankName = payBankName;
	}

	public String getPayChannelName() {
		String channelName = null;
		if ("1".equals(payChannelName)) {
			channelName = "支付宝";
		} else if ("2".equals(payChannelName)) {
			channelName = "微信";
		} else if ("3".equals(payChannelName)) {
			channelName = "银行卡";
		} else if ("4".equals(payChannelName)) {
			channelName = "线下信息费";
		} else {
			channelName = "";
		}
		return channelName;
	}

	public void setPayChannelName(String payChannelName) {
		this.payChannelName = payChannelName;
	}

	public String getTime() {
		return (time != null && time.length() == 21) ? time.substring(0, time.length() - 2) : time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getTypeName() {
		String name = null;
		if (type == 1) {
			name = "支付信息费";
		} else if (type == 2) {
			name = "收到信息费";
		} else if (type == 3) {
			name = "退回信息费";
		} else if (type == 4) {
			name = "转出信息费";
		} else if (type == 6) {
			name = "提现";
		} else if (type == 7) {
			name = "异常上报";
		} else if (type == 8) {
			name = "支付手续费";
		} else if (type == 9) {
			name = "转入会员费";
		} else if (type == 10) {
			name = "支付会员费";
		} else if (type == 0) {
			name = "用户转帐";
		} else if (type == 11){
			name = "好友推荐奖励收益";
		} else if (type == 12) {
			name = "提现退款";
		} else if (type == 13) {
			name = "活动返现";
		} else if (type == 14) {
			name = "退回会员费";
		} else if (type == 15) {
			name = "转出会员费";
		} else if (type == 100) {
			name = "冻结信息费";
		} else if (type == 110) {
			name = "平台入账账户流水";
		} else if (type == 120) {
			name = "平台手续费流水";
		} else if (type == 130) {
			name = "平台出账账户流水";
		} else if (type == 140) {
			name = "平台余额账户流水";
		} else if (type == 150) {
			name = "转入保证金";
		} else if (type == 160) {
			name = "支付保证金";
		} else if (type == 170) {
			name = "渠道结算";
		}else if (type == 180) {
			name = "退回保证金";
		} else if (type == 190) {
			name = "转出保证金";
		}  else if (type == 200) {
			name = "平台信息费入账";
		}  else if (type == 210) {
			name = "平台退款出账";
		}  else if (type == 220) {
			name = "平台提现出账";
		}  else if (type == 230) {
			name = "平台商品入账";
		}  else if (type == 240) {
			name = "平台保证金入账";
		} else if (type == 250) {
			name = "平台保证金出账";
		}else if (type == 310) {
			name = "系统对账修正";
		}
		return name;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
