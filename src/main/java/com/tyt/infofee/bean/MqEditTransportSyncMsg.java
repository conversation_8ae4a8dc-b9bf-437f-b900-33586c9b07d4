package com.tyt.infofee.bean;


/**
 * @ClassName MqEditTransportSyncMsg
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/10/19 15:26
 * @Verdion 1.0
 **/

public class MqEditTransportSyncMsg extends MqBaseMessageBean {

    /**
     * 集团返回的合作商流水号,货源编辑、下架接口要用
     */
    private String partnerSerialNo;

    /**
     * 集团货源id 集团返回
     */
    private Long cargoId;

    /**
     * 合作商追踪信息 货源编辑接口要用非必填字段 集团返回
     */
    private String trackingMsg;

    /**
     * 货源编辑，0 加价;1 转电议;2 转一口价;
     */
    private Integer type;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;

    /**
     * 货联系人手机号码;
     */
    private String tel;

    /**
     * 运费 单位：分  ;
     */
    private Integer afterPrice;

    private Long srcMsgId;

    public String getPartnerSerialNo() {
        return partnerSerialNo;
    }

    public void setPartnerSerialNo(String partnerSerialNo) {
        this.partnerSerialNo = partnerSerialNo;
    }

    public Long getCargoId() {
        return cargoId;
    }

    public void setCargoId(Long cargoId) {
        this.cargoId = cargoId;
    }

    public String getTrackingMsg() {
        return trackingMsg;
    }

    public void setTrackingMsg(String trackingMsg) {
        this.trackingMsg = trackingMsg;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public Integer getAfterPrice() {
        return afterPrice;
    }

    public void setAfterPrice(Integer afterPrice) {
        this.afterPrice = afterPrice;
    }

    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    public Integer getPublishType() {
        return publishType;
    }

    public void setPublishType(Integer publishType) {
        this.publishType = publishType;
    }
}
