package com.tyt.infofee.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsDetailOrderExceptionBean {
	
	Date time;//异常上报时间（毫秒）
	Date finishedTime;//异常上报处理完成时间
	String lastResult;//最终意见
	Long carOwnerMoney;//车主金额（分）
	Long goodsOwnerMoney;//货主金额（分）
	String exStatus;//异常上报处理状态0初始化1处理中2处理完成
	
	public Date getTime() {
		return time;
	}
	public void setTime(Date time) {
		this.time = time;
	}
	public Date getFinishedTime() {
		return finishedTime;
	}
	public void setFinishedTime(Date finishedTime) {
		this.finishedTime = finishedTime;
	}
	public String getLastResult() {
		return lastResult;
	}
	public void setLastResult(String lastResult) {
		this.lastResult = lastResult;
	}
	
	public Long getCarOwnerMoney() {
		return carOwnerMoney;
	}
	public void setCarOwnerMoney(Long carOwnerMoney) {
		this.carOwnerMoney = carOwnerMoney;
	}
	
	public Long getGoodsOwnerMoney() {
		return goodsOwnerMoney;
	}
	public void setGoodsOwnerMoney(Long goodsOwnerMoney) {
		this.goodsOwnerMoney = goodsOwnerMoney;
	}
	public String getExStatus() {
		return exStatus;
	}
	public void setExStatus(String exStatus) {
		this.exStatus = exStatus;
	}

}
