package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;

/**
 * @Description  车主信息费列表实体类
 * <AUTHOR>
 * @Date  2018/12/24 10:45
 * @Param
 * @return
 **/
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarOwnerInfoFeeBean implements Serializable{


	private static final long serialVersionUID = 687623733271646078L;
    //待支付信息费笔数
	private Integer carOwnerNopayInfofeeNum;
	//信息费订单列表(不包含待支付)
	private List<TransportOrdersListBean> carOwnerInfofeeList;

	private List<InfoFeeMyPublishBubbleResultBean> bubbleNumbers;

	public Integer getCarOwnerNopayInfofeeNum() {
		return carOwnerNopayInfofeeNum;
	}

	public void setCarOwnerNopayInfofeeNum(Integer carOwnerNopayInfofeeNum) {
		this.carOwnerNopayInfofeeNum = carOwnerNopayInfofeeNum;
	}

	public List<TransportOrdersListBean> getCarOwnerInfofeeList() {
		return carOwnerInfofeeList;
	}

	public void setCarOwnerInfofeeList(List<TransportOrdersListBean> carOwnerInfofeeList) {
		this.carOwnerInfofeeList = carOwnerInfofeeList;
	}

	public List<InfoFeeMyPublishBubbleResultBean> getBubbleNumbers() {
		return bubbleNumbers;
	}

	public void setBubbleNumbers(List<InfoFeeMyPublishBubbleResultBean> bubbleNumbers) {
		this.bubbleNumbers = bubbleNumbers;
	}
}
