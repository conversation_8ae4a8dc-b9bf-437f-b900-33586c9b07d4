package com.tyt.infofee.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserCancelBean
 * @description 用户账号注销申请实体类
 * @date 2021-12-22 13:28
 */
@Data
public class UserCancelBean {

    /** 终端标识(1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB) */
    private Integer clientSign;

    /** 操作系统版本号 */
    private String osVersion;

    /** 终端软件版本号 */
    private String clientVersion;

    /** 终端唯一标识 */
    private String clientId;

    /** 登录ticket */
    private String ticket;

    /** 用户ID */
    private Long userId;

    /** 短信验证码 */
    private String verificationCode;

    /** 注销原因：1.转行了 2.需要解绑手机 3.这是多余的账户 4.其他原因 */
    private Integer reason;

    /** 注销原因详情 */
    private String reasonDetail;

}
