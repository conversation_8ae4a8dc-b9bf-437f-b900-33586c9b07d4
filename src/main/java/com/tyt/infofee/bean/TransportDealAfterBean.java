package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;

/**  
 * @Title: TransportDealAfterBean.java
 * @Package com.tyt.message.bean
 * @Description: 主要处理货源设置成交后的消息业务
 * <AUTHOR>
 * @date 2019年10月25日
 */
public class TransportDealAfterBean extends MqBaseMessageBean {
	/*
	 * 货源ID
	 */
	private long tsId;
	/*
	 * 类型，1-设置成交短信和push； 2-查看车辆定位短信和push消息
	 */
	private int type;

	public long getTsId() {
		return tsId;
	}

	public void setTsId(long tsId) {
		this.tsId = tsId;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
