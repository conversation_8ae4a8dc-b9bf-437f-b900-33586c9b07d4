package com.tyt.infofee.bean;

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
*  异常订单退还信息实体
* <AUTHOR>
* @since 2023/11/8 11:13
*/
@Data
public class ExInfoFeeGiveBackBean {

    /**
     * 退款人Id
     */
    @NotBlank(message = "用户ID不能为空")
    private Long userId;

    /**
     * 订单Id
     */
    @NotBlank(message = "订单Id不能为空")
    private Long orderId;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款子原因
     */
    private String refundSpecificReason;

    /**
     * 退款原因
     */
    private String refundRemark;

    /**
     * 退款金额 单位分
     */
    private Long refundAmount;

    /**
     * 技术服务费-平台分配金额 单位分
     */
    private Long platformServiceAmount;

    /**
     * 技术服务费-车方分配金额 单位分
     */
    private Long carServiceAmount;

    /**
     * 是否退优惠券 0 不退 1 退还
     */
    private String isRefundCoupon;
}
