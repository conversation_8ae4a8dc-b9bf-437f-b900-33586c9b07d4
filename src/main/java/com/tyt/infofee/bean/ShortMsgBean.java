package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;
import com.tyt.infofee.bean.MqBaseMessageBean;

/**  
 * @Title: ShortMsgBean.java
 * @Package com.tyt.message.bean
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年12月2日
 */
public class ShortMsgBean extends MqBaseMessageBean {
	/*
	 * 手机号码
	 */
	private String cell_phone;
	/*
	 * 短信内容
	 */
	private String content;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 连接
	 */
	private String url;
	//消息模板keys
	private String tmpl_key;
	
    private Integer isVerifyCode;
	
	private String templateCode;
	
	private String verifyCode;
	
	
	
	
	
	
	
	
	public String getTmpl_key() {
		return tmpl_key;
	}


	public void setTmpl_key(String tmpl_key) {
		this.tmpl_key = tmpl_key;
	}


	public String getCell_phone() {
		return cell_phone;
	}


	public void setCell_phone(String cell_phone) {
		this.cell_phone = cell_phone;
	}


	public String getContent() {
		return content;
	}


	public void setContent(String content) {
		this.content = content;
	}


	public String getRemark() {
		return remark;
	}


	public void setRemark(String remark) {
		this.remark = remark;
	}


	public String getUrl() {
		return url;
	}


	public void setUrl(String url) {
		this.url = url;
	}
	
	


	public Integer getIsVerifyCode() {
		return isVerifyCode;
	}


	public void setIsVerifyCode(Integer isVerifyCode) {
		this.isVerifyCode = isVerifyCode;
	}


	public String getTemplateCode() {
		return templateCode;
	}


	public void setTemplateCode(String templateCode) {
		this.templateCode = templateCode;
	}


	public String getVerifyCode() {
		return verifyCode;
	}


	public void setVerifyCode(String verifyCode) {
		this.verifyCode = verifyCode;
	}


	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
