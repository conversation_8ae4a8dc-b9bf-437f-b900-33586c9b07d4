package com.tyt.infofee.bean;

import java.io.Serializable;

/**
 * @discrepsion 异常上报撤销通知运满满消息体
 * <AUTHOR>
 * @date 2023-8-24 13:34:06
 * @version 1.0
 */
public class OrderExCancelNotifyYmmMsg extends MqBaseMessageBean implements Serializable {

    /**
     * 运满满订单ID
     */
    private Long orderId;
    /**
     * 投诉方向1、司机发起，2、货主发起
     */
    private Long complaintType = 1L;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getComplaintType() {
        return complaintType;
    }

    public void setComplaintType(Long complaintType) {
        this.complaintType = complaintType;
    }
}
