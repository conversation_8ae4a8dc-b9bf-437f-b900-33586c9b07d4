package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;

public class CreditUserInfo {
    private String userId;
    private String userName;
    private String idCard;
    //联系电话
    private String tel = "";
    //注册时间
    private String signupTime = "";
    //注册年限
    private String signupYears = "";
    //缴费年限
    private int renewalYears;
    //平台交易次数
    private String tradeNums = "0";
    //合作次数
    private String coopNums = "0";
    //是否认证 0 否 1 是
    private String authed = "0";
    //是否有效会员 0 否 1 是
    private String userType = "0";
    /**
     * 车方会员状态
     */
    private String carPermissionMember;

    //付费剩余天数
    private String serveDays = "0";


    /**
     * 车方信用等级
     */
    @Getter
    @Setter
    private String carCreditRankLevel;





    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getSignupTime() {
        return signupTime;
    }

    public void setSignupTime(String signupTime) {
        this.signupTime = signupTime;
    }

    public String getTradeNums() {
        return tradeNums;
    }

    public void setTradeNums(String tradeNums) {
        this.tradeNums = tradeNums;
    }

    public String getCoopNums() {
        return coopNums;
    }

    public void setCoopNums(String coopNums) {
        this.coopNums = coopNums;
    }

    public String getAuthed() {
        return authed;
    }

    public void setAuthed(String authed) {
        this.authed = authed;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public int getRenewalYears() {
        return renewalYears;
    }

    public void setRenewalYears(int renewalYears) {
        this.renewalYears = renewalYears;
    }

    public String getSignupYears() {
        return signupYears;
    }

    public void setSignupYears(String signupYears) {
        this.signupYears = signupYears;
    }

    public String getServeDays() {
        return serveDays;
    }

    public void setServeDays(String serveDays) {
        this.serveDays = serveDays;
    }

    public String getCarPermissionMember() {
        return carPermissionMember;
    }

    public void setCarPermissionMember(String carPermissionMember) {
        this.carPermissionMember = carPermissionMember;
    }
}
