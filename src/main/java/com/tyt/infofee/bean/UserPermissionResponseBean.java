package com.tyt.infofee.bean;

import lombok.Data;
import java.util.Date;
@Data
public class UserPermissionResponseBean {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 权益类型对应的唯一标识   100201(发货会员)  100202(发货次数)  100101(车会员)  100102(拨打次数)
     */
    private String servicePermissionTypeId;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 系统时间
     */
    private Date systemTime;

    /**
     * 权益状态，1-有效，2-无效-用完，3-无效-到期
     */
    private Integer status;

    /**
     * 权益剩余次数
     */
    private Integer remainNum;

    /**
     * 优车发货卡总数（仅在100202下才有值）
     */
    private Integer excellentCardNum;

}
