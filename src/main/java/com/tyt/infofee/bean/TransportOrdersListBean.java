package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyt.base.bean.MapEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportOrdersListBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -9031854227483484363L;
	private Long id;
	private Long sortId;
	private String tsOrderNo;
	private Long tsId;
	private Long userId;
	private String startPoint;
	private String destPoint;
	private String taskContent;
	private Date publishTime;
	private String payLinkPhone;
	private Long payAmount;
	private Long totalOrderAmount;
	private Long couponAmount;
	private Date  createTime;
	private Date  payEndTime;
	private Date  agreeTime;
	private Date  loadTime;
	private Date unloadTime;
	private Date refuseTime;
	private Date confirmUploadTime;
	private String robStatus;

	/**
	 * 默认值，此属性为订单内tyt库的唯一标识，由'tyt-'加sortId组成
	 */
	private String uniqueId;
	/**
	 * C端订单类型标识，默认为0
	 */
	private Integer orderType = 0;

	//2018-11-28 人保货运险改动
	//是否购买过保险 1是(显示“查看保单”按钮)  2否(显示“买货运险”按钮)
	private Integer isBuyInsurance;
	//最后一次购买保险的保单Id
	private Long insuranceId;
	//最后一次购买保险的保单状态 0待支付 1已生效 2已退保
	private Integer insuranceStatus;

    //2018-12-24 信息费改版
	//退款金额 单位分
	private Long refundAmount;
	//退回原因
	private String refundReason;
	//信息费状态：10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成
    private Integer costStatus;
    //货主姓名
	private String userName;
	//支付信息人Id
	private Long payUserId;
	//发布人昵称(车主昵称)
    private String pubUserName;
    //车主昵称
	private String payUserName;

	//重量 单位:吨
	private String weight;

	//--------------------------------------------------------------------------------------------------
	//车货拆分新增参数
	/**
	 * 货物长单位米
	 */
	private String length;
	/**
	 * 货物宽单位米
	 */
	private String wide;
	/**
	 * 货物高单位米
	 */
	private String high;

	/**
	 * 开始装车时间
	 */
	private Date beginLoadingTime;
	/**
	 * 开始卸车时间
	 */
	private Date beginUnloadTime;
	/**
	 * 装车时间
	 */
	private Date loadingTime;
	/**
	 * 车辆最小长度
	 */
	private BigDecimal carMinLength;
	/**
	 * 车辆最大长度
	 */
	private BigDecimal carMaxLength;
	/**
	 * 挂车样式
	 */
	private String carStyle;
	/**
	 * 车辆类型
	 */
	private String carType;

	/**
	 * 挂车样式
	 */
	private Integer isPureFlat;

	/**
	 * 工作面高最小值
	 */
	private BigDecimal workPlaneMinHigh;
	/**
	 * 工作面高最大值
	 */
	private BigDecimal workPlaneMaxHigh;
	/**
	 * 工作面长最小值
	 */
	private BigDecimal workPlaneMinLength;
	/**
	 * 工作面长最大值
	 */
	private BigDecimal workPlaneMaxLength;
	/**
	 * 是否需要带爬梯 0 是 1 需要 2不需要
	 */
	private String climb;

	/**
	 * 运价
	 */
	private String price;
	/**
	 * 车辆id
	 */
	private Long carId;
	/**
	 * 车头城市
	 */
	private String headCity;
	/**
	 * 车头号码
	 */
	private String headNo;
	/**
	 * 挂车城市
	 */
	private String tailCity;
	/**
	 * 挂车号码
	 */
	private String tailNo;

	/**
	 * 车辆是否为待完善车辆
	 */
	private Boolean carIsNeedImprovenData;

	/**
	 * 平台服务费
	 */
	private Long payServiceCharge;

	/**
	 * 技术服务费
	 */
	private Long tecServiceFee;

	/**
	 * 车辆认证状态
	 */
	private Integer auth;
	/**
	 *更换车辆标识 0未更换，1已更换
	 */
	private Integer carReplace;
	/**
	 * 限时货源是否成交车标识 0不是 1是  2货主取消货源
	 */
	private Integer isDealCar;

	/**
	 * 实际到期时间
	 */
	private  Date  dePaymentDueDate;

	/**
	 * 延迟付款状态 0 未延迟 1 延迟付款  2 拒绝退款延迟
	 */
	private Integer delayStatus;
	/**
	 * 异常上报取消状态:0初始化 1车方操作过撤销(货方未操作过撤销) 2 货方操作过撤销(车方未操作过撤销) 3 车方货方均操作过撤销
	 **/
	private Integer exCancelStatus;

	/**
	 * 确认付款时间大于1天时：还剩 ？天自动结算，确认付款时间小于等于1天时:即将自动结算
	 */
	private String confirmPaymentRemark;
	/**
	 * 车方详情是否展示 0展示 1不展示
	 */
	private int carShow;


	/**
	 * 货方详情是否展示 0展示 1不展示
	 */
	private int goodsShow;
	/**
	 * 限时货源标识0和null不是1是
	 */
	private Integer timeLimitIdentification;

	private Date ctime;

	private String tel;
	private String tel3;
	private String tel4;

	/**
	 * 货源类型（电议1，一口价2）
	 */
	@Getter
	@Setter
	private Short publishType;

	private List<MapEntity> goodsPhoneList;


	/**
	 * 订金类型（0不退还；1退还）
	 */
	@Getter
	@Setter
	private Integer refundFlag;

	/**
	 * 延迟退款状态 0 未延迟退款 1 延迟退款
	 */
	@Getter
	@Setter
	private Integer delayRefundStatus;

	/**
	 * 预计退款到账日期
	 */
	@Getter
	@Setter
	private Date deRefundDueDate;

	/**
	 * 确认退款时间大于1天时：还剩 ？天自动结算，确认付款时间小于等于1天时:即将自动结算
	 */
	@Getter
	@Setter
	private String confirmRefundRemark;

	/**
	 * 订单状态描述
	 */
	@Getter
	@Setter
	private String statusDesc;

	/**
	 * 评价状态 0 未评价 1 已评价
	 */
	@Getter
	@Setter
	private Integer evaluateStatus;

	/**
	 * 官方授权昵称
	 */
	@Getter
	@Setter
	private String authName;

	/**
	 * 官方授权昵称(tea加密)
	 */
	@Getter
	@Setter
	private String authNameTea;

	/**
	 * 保障货源（1是；0否；）
	 */
	@Getter
	@Setter
	private Integer guaranteeGoods;
	/**
	 * 该订单最新一条异常上报方  1 车方 2货方
	 */
	private String exParty;

	/**
	 * 三方平台类型 0 特运通  1:满帮
	 **/
	private Integer thirdpartyPlatformType;

	/**
	 * 车方信用等级
	 */
	@Getter
	@Setter
	private String carCreditRankLevel;

	/**
	 * 用户在平台的交易量
	 */
	@Getter
	@Setter
	private String tradeNums;

	/**
	 * 是否能发表评论
	 */
	@Getter
	@Setter
	private Boolean canPostFeedBack = false;


	/**
	 * 是否是优车货源（0:否 1：是）
	 **/
	@Getter
	@Setter
	private Integer excellentGoods;

	/**
	 * 货源来源（1货主；2调度客服；3个人货主;4:运满满货源）
	 */
	@Getter
	@Setter
	private Integer sourceType;

	/**
	 * 标准货名备注
	 */
	@Getter
	@Setter
	private String machineRemark;

	/**
	 * 车方技术服务费退款金额 单位元
	 */
	@Getter
	@Setter
	private Long tecServiceFeeRefundAmount;

	/**
	 * 车方技术服务费退款到账时间
	 */
	@Getter
	@Setter
	private Date tecServiceFeeRefundTime;


	public Integer getTimeLimitIdentification() {
		return timeLimitIdentification;
	}

	public void setTimeLimitIdentification(Integer timeLimitIdentification) {
		this.timeLimitIdentification = timeLimitIdentification;
	}

	public Integer getIsDealCar() {
		return isDealCar;
	}

	public void setIsDealCar(Integer isDealCar) {
		this.isDealCar = isDealCar;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getTel3() {
		return tel3;
	}

	public void setTel3(String tel3) {
		this.tel3 = tel3;
	}

	public String getTel4() {
		return tel4;
	}

	public void setTel4(String tel4) {
		this.tel4 = tel4;
	}

	public List<MapEntity> getGoodsPhoneList() {
		return goodsPhoneList;
	}

	public void setGoodsPhoneList(List<MapEntity> goodsPhoneList) {
		this.goodsPhoneList = goodsPhoneList;
	}

	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	public Integer getAuth() {
		return auth;
	}

	public void setAuth(Integer auth) {
		this.auth = auth;
	}


	public Long getPayServiceCharge() {
		return payServiceCharge;
	}

	public void setPayServiceCharge(Long payServiceCharge) {
		this.payServiceCharge = payServiceCharge;
	}

	public Long getCarId() {
		return carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	public String getHeadCity() {
		return headCity;
	}

	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}

	public String getHeadNo() {
		return headNo;
	}

	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}

	public String getTailCity() {
		return tailCity;
	}

	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}

	public String getTailNo() {
		return tailNo;
	}

	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}

	public Long getId() {
		return id;
	}
	public Long getSortId() {
		return sortId;
	}
	public String getTsOrderNo() {
		return tsOrderNo;
	}
	public Long getTsId() {
		return tsId;
	}
	public Long getUserId() {
		return userId;
	}
	public String getStartPoint() {
		return startPoint;
	}
	public String getDestPoint() {
		return destPoint;
	}
	public String getTaskContent() {
		return taskContent;
	}

	
	public Date getPublishTime() {
		return publishTime;
	}
	public String getPayLinkPhone() {
		return payLinkPhone;
	}
	public Long getPayAmount() {
		return payAmount;
	}

	public Long getTotalOrderAmount() {
		return totalOrderAmount;
	}

	public Long getCouponAmount() {
		return couponAmount;
	}

	public Short getPublishType() {
		return publishType;
	}

	public Integer getRefundFlag() {
		return refundFlag;
	}

	public Integer getDelayRefundStatus() {
		return delayRefundStatus;
	}

	public Date getDeRefundDueDate() {
		return deRefundDueDate;
	}

	public String getConfirmRefundRemark() {
		return confirmRefundRemark;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	public Date getCreateTime() {
		return createTime;
	}
	public Date getPayEndTime() {
		return payEndTime;
	}
	public Date getAgreeTime() {
		return agreeTime;
	}
	public Date getLoadTime() {
		return loadTime;
	}
	
	public Date getUnloadTime() {
		return unloadTime;
	}
	public void setUnloadTime(Date unloadTime) {
		this.unloadTime = unloadTime;
	}
	public Date getRefuseTime() {
		return refuseTime;
	}
	public Date getConfirmUploadTime() {
		return confirmUploadTime;
	}
	public void setConfirmUploadTime(Date confirmUploadTime) {
		this.confirmUploadTime = confirmUploadTime;
	}
	public String getRobStatus() {
		return robStatus;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setSortId(Long sortId) {
		this.sortId = sortId;
		if(this.orderType == 0) {
			this.uniqueId = "plat-" + sortId;
		}
	}
	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}
	public void setDestPoint(String destPoint) {
		this.destPoint = destPoint;
	}
	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}
	public void setPayLinkPhone(String payLinkPhone) {
		this.payLinkPhone = payLinkPhone;
	}
	public void setPayAmount(Long payAmount) {
		this.payAmount = payAmount;
	}

	public void setTotalOrderAmount(Long totalOrderAmount) {
		this.totalOrderAmount = totalOrderAmount;
	}

	public void setCouponAmount(Long couponAmount) {
		this.couponAmount = couponAmount;
	}

	public void setPublishType(Short publishType) {
		this.publishType = publishType;
	}

	public void setRefundFlag(Integer refundFlag) {
		this.refundFlag = refundFlag;
	}

	public void setDelayRefundStatus(Integer delayRefundStatus) {
		this.delayRefundStatus = delayRefundStatus;
	}

	public void setDeRefundDueDate(Date deRefundDueDate) {
		this.deRefundDueDate = deRefundDueDate;
	}

	public void setConfirmRefundRemark(String confirmRefundRemark) {
		this.confirmRefundRemark = confirmRefundRemark;
	}

	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public void setPayEndTime(Date payEndTime) {
		this.payEndTime = payEndTime;
	}
	public void setAgreeTime(Date agreeTime) {
		this.agreeTime = agreeTime;
	}
	public void setLoadTime(Date loadTime) {
		this.loadTime = loadTime;
	}
	public void setRefuseTime(Date refuseTime) {
		this.refuseTime = refuseTime;
	}
	public void setRobStatus(String robStatus) {
		this.robStatus = robStatus;
	}

	public String getUniqueId() {
		return uniqueId;
	}

	public void setUniqueId(String uniqueId) {
		this.uniqueId = uniqueId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public Integer getIsBuyInsurance() {
		return isBuyInsurance;
	}

	public void setIsBuyInsurance(Integer isBuyInsurance) {
		this.isBuyInsurance = isBuyInsurance;
	}

	public Long getInsuranceId() {
		return insuranceId;
	}

	public void setInsuranceId(Long insuranceId) {
		this.insuranceId = insuranceId;
	}

	public Integer getInsuranceStatus() {
		return insuranceStatus;
	}

	public void setInsuranceStatus(Integer insuranceStatus) {
		this.insuranceStatus = insuranceStatus;
	}

	public Long getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Long refundAmount) {
		this.refundAmount = refundAmount;
	}

	public String getRefundReason() {
		return refundReason;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}

	public Integer getCostStatus() {
		return costStatus;
	}

	public void setCostStatus(Integer costStatus) {
		this.costStatus = costStatus;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Long getPayUserId() {
		return payUserId;
	}

	public void setPayUserId(Long payUserId) {
		this.payUserId = payUserId;
	}

	public String getPubUserName() {
		return pubUserName;
	}

	public void setPubUserName(String pubUserName) {
		this.pubUserName = pubUserName;
	}

	public String getPayUserName() {
		return payUserName;
	}

	public void setPayUserName(String payUserName) {
		this.payUserName = payUserName;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getLength() {
		return length;
	}

	public void setLength(String length) {
		this.length = length;
	}

	public String getWide() {
		return wide;
	}

	public void setWide(String wide) {
		this.wide = wide;
	}

	public String getHigh() {
		return high;
	}

	public void setHigh(String high) {
		this.high = high;
	}

	public Date getLoadingTime() {
		return loadingTime;
	}

	public void setLoadingTime(Date loadingTime) {
		this.loadingTime = loadingTime;
	}

	public BigDecimal getCarMinLength() {
		return carMinLength;
	}

	public void setCarMinLength(BigDecimal carMinLength) {
		this.carMinLength = carMinLength;
	}

	public BigDecimal getCarMaxLength() {
		return carMaxLength;
	}

	public void setCarMaxLength(BigDecimal carMaxLength) {
		this.carMaxLength = carMaxLength;
	}

	public String getCarStyle() {
		return carStyle;
	}

	public void setCarStyle(String carStyle) {
		this.carStyle = carStyle;
	}

	public String getCarType() {
		return carType;
	}

	public void setCarType(String carType) {
		this.carType = carType;
	}

	public BigDecimal getWorkPlaneMinHigh() {
		return workPlaneMinHigh;
	}

	public void setWorkPlaneMinHigh(BigDecimal workPlaneMinHigh) {
		this.workPlaneMinHigh = workPlaneMinHigh;
	}

	public BigDecimal getWorkPlaneMaxHigh() {
		return workPlaneMaxHigh;
	}

	public void setWorkPlaneMaxHigh(BigDecimal workPlaneMaxHigh) {
		this.workPlaneMaxHigh = workPlaneMaxHigh;
	}

	public BigDecimal getWorkPlaneMinLength() {
		return workPlaneMinLength;
	}

	public void setWorkPlaneMinLength(BigDecimal workPlaneMinLength) {
		this.workPlaneMinLength = workPlaneMinLength;
	}

	public BigDecimal getWorkPlaneMaxLength() {
		return workPlaneMaxLength;
	}

	public void setWorkPlaneMaxLength(BigDecimal workPlaneMaxLength) {
		this.workPlaneMaxLength = workPlaneMaxLength;
	}

	public String getClimb() {
		return climb;
	}

	public void setClimb(String climb) {
		this.climb = climb;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public Date getBeginLoadingTime() {
		return beginLoadingTime;
	}

	public void setBeginLoadingTime(Date beginLoadingTime) {
		this.beginLoadingTime = beginLoadingTime;
	}

	public Date getBeginUnloadTime() {
		return beginUnloadTime;
	}

	public void setBeginUnloadTime(Date beginUnloadTime) {
		this.beginUnloadTime = beginUnloadTime;
	}

	public Date getDePaymentDueDate() {
		return dePaymentDueDate;
	}

	public void setDePaymentDueDate(Date dePaymentDueDate) {
		this.dePaymentDueDate = dePaymentDueDate;
	}

	public Integer getDelayStatus() {
		return delayStatus;
	}

	public void setDelayStatus(Integer delayStatus) {
		this.delayStatus = delayStatus;
	}

	public String getConfirmPaymentRemark() {
		return confirmPaymentRemark;
	}

	public void setConfirmPaymentRemark(String confirmPaymentRemark) {
		this.confirmPaymentRemark = confirmPaymentRemark;
	}

	public int getCarShow() {
		return carShow;
	}

	public void setCarShow(int carShow) {
		this.carShow = carShow;
	}

	public int getGoodsShow() {
		return goodsShow;
	}

	public void setGoodsShow(int goodsShow) {
		this.goodsShow = goodsShow;
	}

	public Integer getCarReplace() {
		return carReplace;
	}

	public void setCarReplace(Integer carReplace) {
		this.carReplace = carReplace;
	}

	public Integer getExCancelStatus() {
		return exCancelStatus;
	}

	public void setExCancelStatus(Integer exCancelStatus) {
		this.exCancelStatus = exCancelStatus;
	}

	public Integer getEvaluateStatus() {
		return evaluateStatus;
	}

	public void setEvaluateStatus(Integer evaluateStatus) {
		this.evaluateStatus = evaluateStatus;
	}

	public String getAuthName() {
		return authName;
	}

	public void setAuthName(String authName) {
		this.authName = authName;
	}

	public String getAuthNameTea() {
		return authNameTea;
	}

	public void setAuthNameTea(String authNameTea) {
		this.authNameTea = authNameTea;
	}

	public Integer getGuaranteeGoods() {
		return guaranteeGoods;
	}

	public void setGuaranteeGoods(Integer guaranteeGoods) {
		this.guaranteeGoods = guaranteeGoods;
	}

	public String getExParty() {
		return exParty;
	}

	public void setExParty(String exParty) {
		this.exParty = exParty;
	}

	public Integer getThirdpartyPlatformType() {
		return thirdpartyPlatformType;
	}

	public void setThirdpartyPlatformType(Integer thirdpartyPlatformType) {
		this.thirdpartyPlatformType = thirdpartyPlatformType;
	}

	public Long getTecServiceFee() {
		return tecServiceFee;
	}

	public void setTecServiceFee(Long tecServiceFee) {
		this.tecServiceFee = tecServiceFee;
	}

	public Boolean isCarIsNeedImprovenData() {
		return carIsNeedImprovenData;
	}

	public void setCarIsNeedImprovenData(Boolean carIsNeedImprovenData) {
		this.carIsNeedImprovenData = carIsNeedImprovenData;
	}

	public Integer getIsPureFlat() {
		return isPureFlat;
	}

	public void setIsPureFlat(Integer isPureFlat) {
		this.isPureFlat = isPureFlat;
	}

	@Override
	public String toString() {
		return "TransportOrdersListBean{" +
				"id=" + id +
				", sortId=" + sortId +
				", tsOrderNo='" + tsOrderNo + '\'' +
				", tsId=" + tsId +
				", userId=" + userId +
				", startPoint='" + startPoint + '\'' +
				", destPoint='" + destPoint + '\'' +
				", taskContent='" + taskContent + '\'' +
				", publishTime=" + publishTime +
				", payLinkPhone='" + payLinkPhone + '\'' +
				", payAmount=" + payAmount +
				", createTime=" + createTime +
				", payEndTime=" + payEndTime +
				", agreeTime=" + agreeTime +
				", loadTime=" + loadTime +
				", unloadTime=" + unloadTime +
				", refuseTime=" + refuseTime +
				", confirmUploadTime=" + confirmUploadTime +
				", robStatus='" + robStatus + '\'' +
				", uniqueId='" + uniqueId + '\'' +
				", orderType=" + orderType +
				", isBuyInsurance=" + isBuyInsurance +
				", insuranceId=" + insuranceId +
				", insuranceStatus=" + insuranceStatus +
				", refundAmount=" + refundAmount +
				", costStatus=" + costStatus +
				", userName='" + userName + '\'' +
				", payUserId=" + payUserId +
				", pubUserName='" + pubUserName + '\'' +
				", payUserName='" + payUserName + '\'' +
				", weight='" + weight + '\'' +
				", length='" + length + '\'' +
				", wide='" + wide + '\'' +
				", high='" + high + '\'' +
				", beginLoadingTime=" + beginLoadingTime +
				", beginUnloadTime=" + beginUnloadTime +
				", loadingTime=" + loadingTime +
				", carMinLength=" + carMinLength +
				", carMaxLength=" + carMaxLength +
				", carStyle='" + carStyle + '\'' +
				", carType='" + carType + '\'' +
				", workPlaneMinHigh=" + workPlaneMinHigh +
				", workPlaneMaxHigh=" + workPlaneMaxHigh +
				", workPlaneMinLength=" + workPlaneMinLength +
				", workPlaneMaxLength=" + workPlaneMaxLength +
				", climb='" + climb + '\'' +
				", price='" + price + '\'' +
				", carId=" + carId +
				", headCity='" + headCity + '\'' +
				", headNo='" + headNo + '\'' +
				", tailCity='" + tailCity + '\'' +
				", tailNo='" + tailNo + '\'' +
				", payServiceCharge=" + payServiceCharge +
				", auth=" + auth +
				", isDealCar=" + isDealCar +
				", dePaymentDueDate=" + dePaymentDueDate +
				", delayStatus=" + delayStatus +
				", confirmPaymentRemark='" + confirmPaymentRemark + '\'' +
				", carShow=" + carShow +
				", goodsShow=" + goodsShow +
				", timeLimitIdentification=" + timeLimitIdentification +
				", ctime=" + ctime +
				", tel='" + tel + '\'' +
				", tel3='" + tel3 + '\'' +
				", tel4='" + tel4 + '\'' +
				", goodsPhoneList=" + goodsPhoneList +
				'}';
	}
}
