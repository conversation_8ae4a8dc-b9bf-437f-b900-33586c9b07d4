package com.tyt.infofee.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

/**
 * 货物详情中显示的订单信息
 * <AUTHOR>
 *
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsDetailOrderBean {
	private Long id;//主键
	private String goodsOrderNo;//运单号
	private Long carOwnerUserId;//车主user_id
	private String carOwnerRegisterPhone;//车主注册账号
	private String carOwnerTelephone;//车主联系电话
	private Long payAgencyMoney;//支付金额单位分
	private Date payEndTime;//支付完成时间
	private String payChannel;//1支付宝 2易宝/银行卡 3微信
	private String robStatus;//接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
	//运费
	@Getter
	@Setter
	private Integer carriageFee;

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getGoodsOrderNo() {
		return goodsOrderNo;
	}
	public void setGoodsOrderNo(String goodsOrderNo) {
		this.goodsOrderNo = goodsOrderNo;
	}
	public Long getCarOwnerUserId() {
		return carOwnerUserId;
	}
	public void setCarOwnerUserId(Long carOwnerUserId) {
		this.carOwnerUserId = carOwnerUserId;
	}
	public String getCarOwnerRegisterPhone() {
		return carOwnerRegisterPhone;
	}
	public void setCarOwnerRegisterPhone(String carOwnerRegisterPhone) {
		this.carOwnerRegisterPhone = carOwnerRegisterPhone;
	}
	public String getCarOwnerTelephone() {
		return carOwnerTelephone;
	}
	public void setCarOwnerTelephone(String carOwnerTelephone) {
		this.carOwnerTelephone = carOwnerTelephone;
	}
	public Long getPayAgencyMoney() {
		return payAgencyMoney;
	}
	public void setPayAgencyMoney(Long payAgencyMoney) {
		this.payAgencyMoney = payAgencyMoney;
	}
	public Date getPayEndTime() {
		return payEndTime;
	}
	public void setPayEndTime(Date payEndTime) {
		this.payEndTime = payEndTime;
	}
	public String getPayChannel() {
		return payChannel;
	}
	public void setPayChannel(String payChannel) {
		this.payChannel = payChannel;
	}
	public String getRobStatus() {
		return robStatus;
	}
	public void setRobStatus(String robStatus) {
		this.robStatus = robStatus;
	}

}
