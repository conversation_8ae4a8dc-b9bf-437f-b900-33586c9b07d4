package com.tyt.infofee.bean;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class ToPayResultBean {

    private String redirectURL;
    private Map<String, Object> redirectParams = new HashMap<String, Object>();

    private BigDecimal payAmount;

    public String getRedirectURL() {
        return redirectURL;
    }

    public void setRedirectURL(String redirectURL) {
        this.redirectURL = redirectURL;
    }

    public Map<String, Object> getRedirectParams() {
        return redirectParams;
    }

    public void setRedirectParams(Map<String, Object> redirectParams) {
        this.redirectParams = redirectParams;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }
}
