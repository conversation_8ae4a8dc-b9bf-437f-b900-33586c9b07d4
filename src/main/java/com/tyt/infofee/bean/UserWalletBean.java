package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;

/**
 * 用户钱包信息的实体
 * 
 * <AUTHOR>
 * @date 2016-12-2上午9:06:00
 * @description
 */
public class UserWalletBean {
	private Integer id;
	/*
	 * 所属用户id，关联tyt_user表
	 */
	private Integer userId;
	/*
	 * 用户当前的钱包，积分等余额，具体哪种余额通过affilicated_info_type字段区分
	 */
	private Long currentBalance;
	/*
	 * 余额类型，1: 钱包余额，2：积分，3：冻结资金,其他待定
	 */
	private Integer affiliatedType;
	/*
	 * 信息创建时间
	 */
	private String createTime;
	/*
	 * 信息更新时间
	 */
	private String updateTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Long getCurrentBalance() {
		return currentBalance;
	}

	public void setCurrentBalance(Long currentBalance) {
		this.currentBalance = currentBalance;
	}

	public Integer getAffiliatedType() {
		return affiliatedType;
	}

	public void setAffiliatedType(Integer affiliatedType) {
		this.affiliatedType = affiliatedType;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
