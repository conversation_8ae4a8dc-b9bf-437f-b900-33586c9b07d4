package com.tyt.infofee.bean;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
/**
 * 列表数据bean
 * <AUTHOR>
 *
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ListDataBean implements Serializable {
	private static final long serialVersionUID = 8677943109241038360L;
	/**
	 * 列表数据
	 */
	private List data;
	/**
	 * 气泡列表
	 */
	private List bubbleNumbers;
	/*
	 * 当前时间
	 */
	private Long currentTime;
	/**
	 * plat 车方已接单 查询queryID
	 */
	private Long queryID;
	/**
	 * corp 订单 查询queryCID
	 */
	private Long queryCID;
	public List getData() {
		return data;
	}
	public List getBubbleNumbers() {
		return bubbleNumbers;
	}
	public Long getCurrentTime() {
		return currentTime;
	}
	public void setData(List data) {
		this.data = data;
	}
	public void setBubbleNumbers(List bubbleNumbers) {
		this.bubbleNumbers = bubbleNumbers;
	}
	public void setCurrentTime(Long currentTime) {
		this.currentTime = currentTime;
	}

	public Long getQueryID() {
		return queryID;
	}

	public void setQueryID(Long queryID) {
		this.queryID = queryID;
	}

	public Long getQueryCID() {
		return queryCID;
	}

	public void setQueryCID(Long queryCID) {
		this.queryCID = queryCID;
	}
}
