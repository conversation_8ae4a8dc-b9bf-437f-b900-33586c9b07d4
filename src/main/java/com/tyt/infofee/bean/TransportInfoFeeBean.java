package com.tyt.infofee.bean;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/9/8 16:47
 * @Version 1.0
 **/
public class TransportInfoFeeBean {

    private String ownerId;

    private String startProvinc;

    private String startCity;

    private String startArea;

    private String destArea;

    private String destCity;

    private String destProvinc;

    private String taskContent;

    private String headNo;

    private String tailNo;

    private String userName;

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getStartProvinc() {
        return startProvinc;
    }

    public void setStartProvinc(String startProvinc) {
        this.startProvinc = startProvinc;
    }

    public String getStartCity() {
        return startCity;
    }

    public void setStartCity(String startCity) {
        this.startCity = startCity;
    }

    public String getStartArea() {
        return startArea;
    }

    public void setStartArea(String startArea) {
        this.startArea = startArea;
    }

    public String getDestArea() {
        return destArea;
    }

    public void setDestArea(String destArea) {
        this.destArea = destArea;
    }

    public String getDestCity() {
        return destCity;
    }

    public void setDestCity(String destCity) {
        this.destCity = destCity;
    }

    public String getDestProvinc() {
        return destProvinc;
    }

    public void setDestProvinc(String destProvinc) {
        this.destProvinc = destProvinc;
    }

    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public String getHeadNo() {
        return headNo;
    }

    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }

    public String getTailNo() {
        return tailNo;
    }

    public void setTailNo(String tailNo) {
        this.tailNo = tailNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
