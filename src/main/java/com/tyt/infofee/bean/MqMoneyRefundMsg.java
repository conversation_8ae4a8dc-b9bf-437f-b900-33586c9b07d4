package com.tyt.infofee.bean;

import java.util.List;
import com.alibaba.fastjson.JSON;

/**
 * 退款消息
 * 
 * <AUTHOR>
 * @date 2016-11-24下午2:50:40
 * @description
 */
public class MqMoneyRefundMsg extends MqBaseMessageBean {
	/*
	 * 退款类型 0是发货方拒绝退款，1是发货方超时未处理自动拒绝退款， 2是车方取消退款
	 */
	private int refundType = 0;
	/*
	 * 申请退款的信息费支付订单id集合
	 */
	private List<String> orderIds;
	/*
	 * 退款用户集合，与orderIds一一对应
	 */
	private List<String> userId;

	public List<String> getUserId() {
		return userId;
	}

	public void setUserId(List<String> userId) {
		this.userId = userId;
	}

	public List<String> getOrderIds() {
		return orderIds;
	}

	public void setOrderIds(List<String> orderIds) {
		this.orderIds = orderIds;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

	public int getRefundType() {
		return refundType;
	}

	public void setRefundType(int refundType) {
		this.refundType = refundType;
	}
}
