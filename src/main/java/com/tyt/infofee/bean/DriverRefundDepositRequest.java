package com.tyt.infofee.bean;

import lombok.Data;

/**
 * @ClassName: DriverRefundDepositRequest
 * @Description: 特运通司机申请退还订金接口实体
 * @Author: zgz
 * @Date: 2023-08-17 15:56
 * @Version: 1.0
 */
@Data
public class DriverRefundDepositRequest extends MqBaseMessageBean{

    /**
     * 订单ID，满满平台的订单号
     */
    private String orderId;
    /**
     * 申请退订金原因
     */
    private Integer reasonId;
    /**
     * 申请退订金备注内容
     */
    private String otherReason;
}
