package com.tyt.infofee.bean;

import java.io.Serializable;

public class RefundMsg extends MqBaseMessageBean implements Serializable {
    private static final long serialVersionUID = -7195155414623337632L;
    private String totalAmount; //总付款金额
    private String amount;//金额,单位:分
    private String outTradeNo;//内部订单号-orderId
    private int tradeInfoId;  //支付交易Id
    private String userId;
    private int transforeBank; //1.支付宝  2.微信 3.易宝
    private String thirdpartyOrderSerialNum; //第三方支付平台生成的订单号
    private String payMethod;  //付款方式: 微信,支付宝,易宝,连连支付

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public int getTradeInfoId() {
        return tradeInfoId;
    }

    public void setTradeInfoId(int tradeInfoId) {
        this.tradeInfoId = tradeInfoId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getTransforeBank() {
        return transforeBank;
    }

    public void setTransforeBank(int transforeBank) {
        this.transforeBank = transforeBank;
    }

    public String getThirdpartyOrderSerialNum() {
        return thirdpartyOrderSerialNum;
    }

    public void setThirdpartyOrderSerialNum(String thirdpartyOrderSerialNum) {
        this.thirdpartyOrderSerialNum = thirdpartyOrderSerialNum;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    @Override
    public String toString() {
        return "RefundMsg{" +
                "totalAmount='" + totalAmount + '\'' +
                ", amount='" + amount + '\'' +
                ", outTradeNo='" + outTradeNo + '\'' +
                ", tradeInfoId=" + tradeInfoId +
                ", userId='" + userId + '\'' +
                ", transforeBank=" + transforeBank +
                ", thirdpartyOrderSerialNum='" + thirdpartyOrderSerialNum + '\'' +
                ", payMethod='" + payMethod + '\'' +
                "} " + super.toString();
    }
}
