package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 信息费详情内代表是否收藏的实体
 * <AUTHOR>
 *
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SingleGoodsCollectBean {
	
	Integer isCollect;//0否  1是
	Long collectId;//收藏信息ID
	public Integer getIsCollect() {
		return isCollect;
	}
	public void setIsCollect(Integer isCollect) {
		this.isCollect = isCollect;
	}
	public Long getCollectId() {
		return collectId;
	}
	public void setCollectId(Long collectId) {
		this.collectId = collectId;
	}
	public SingleGoodsCollectBean(Integer isCollect, Long collectId) {
		super();
		this.isCollect = isCollect;
		this.collectId = collectId;
	}
	public SingleGoodsCollectBean() {
		super();
	}
}
