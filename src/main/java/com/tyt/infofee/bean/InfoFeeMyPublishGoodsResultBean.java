package com.tyt.infofee.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.plat.utils.BigDecimalSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InfoFeeMyPublishGoodsResultBean {

    private Long tsId;//	ID
    private Long userId;//发布人id
    private String startPoint;//出发地
    private String startDetailAdd;
    private String destPoint;//目的地
    private String taskContent;//货物内容
    private String length;//长
    private String wide;//宽
    private String high;//高
    private String isInfoFee;//0是不需要、1是需要
    private Date publishTime;//发布日期 (毫秒)
    private Date cancelTime;//撤销日期 (毫秒)
    private Date loadTime;  //成交时间(毫秒)
    private Integer goodStatus;//货物状态， 1有效（发布中），0无效，2待定（QQ专用），3阻止（QQ专用），4成交，5撤销状态
    private Long srcMsgId;//	srcMsgId
    private String weight;  // 重量单位吨
    //2021.10.08 新增参数 货源撤销原因
    private String backoutReasonKey;
    //2018-12-25 编辑重发按钮逻辑判断
    private String infoStatus;// 信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货）3车主装货完成 4系统装货完成 5异常上报

    // 2019-01-28日新增 查看人与联系人查看数据
    /**
     * 查看次数
     */
    private String viewCount = "0";
    /**
     * 联系人数
     */
    private String contactCount = "0";

    /**
     * 2019-05-21 增加排序Id
     */
    private Long sortId;
    /**
     * 2019-12-05 运费
     */
    private String price;
    /**
     * 首发货源类型（电议1，一口价2）
     */
    private Integer firstPublishType;
    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;
    /**
     * 信息费（分）
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal infoFee;


    /**
     * 技术服务费（分）
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal tecServiceFee;

    /**
     * 加价次数
     */
    private String addMoneyNum;

    private Date releaseTime;
    /**
     * 调车数量
     */
    private Integer shuntingQuantity;

    //车货拆分新增参数------------------------------------------------------
    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;
    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 货物型号
     */
    private String type;
    /**
     * 货物品牌
     */
    private String brand;
    /**
     * 订金类型（0不退还；1退还）
     */
    private Integer refundFlag;
    /**
     * 是否后台货源0不是1是
     */
    private Integer isBackendTransport = 0;

    private Integer orderCount = 0;

    /**
     * 是否显示转一口价按钮
     */
    private Integer priceButtonShow = 1;

    /**
     * 刷新次数
     */
    private Integer resendCounts;

    // 出发地 目的地 省市区
    private String startProvinc;
    private String startCity;
    private String startArea;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private String destDetailAdd;

    private String startLongitude;
    private String startLatitude;
    private String destLongitude;
    private String destLatitude;

    /**
     * 是否是保障货源 0：否  1：是
     */
    private Integer guaranteeGoods;

    //是否在两个月内没有发过电议有价货源或者一口价货源（展示限时体验按钮） 0 否 1 是
    private Integer limitTimeExperience;

    /**
     * 是否有信用曝光权限（0没有；1有（未点击）；2有（已点击）；）
     */
    private Integer creditRetop;

    /**
     * 是否有信用曝光权限（0没有；1有（未点击）；2有（已点击）；）
     */
    private Integer userLevel;

    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    private String labelJson;
    /**
     * 货源是否可以刷新 0 可以 1不可以
     */
    private Integer showGoodsRefresh = 1;

    /**
     * 优推好车主过期时间
     */
    private Date priorityRecommendExpireTime;

    /**
     * 是否是优车货源（0:否 1：是）
     */
    private Integer excellentGoods;

    /**
     * 是否优车2.0货源：1-否，2-是
     */
    private Integer excellentGoodsTwo;

    /**
     * 货源来源（1货主；2调度客服；3:个人货主  4：运满满货源）
     */
    private Integer sourceType;

    /**
     * 标准货名备注
     */
    private String machineRemark;
    /**
     * 优车发货卡id
     */
    private Long excellentCardId;

    /**
     * 这个货被车方报价的总次数
     */
    private Integer transportQuotedPriceTimes;

    /**
     * 是否开票货源 0：否；1：是
     */
    private Integer invoiceTransport;

    /**
     * 附加运费
     */
    private String additionalPrice;

    /**
     * 企业税率
     */
    private BigDecimal enterpriseTaxRate;

    /**
     * 优车好货（秒抢货源） 1：是；0：否
     */
    private Integer instantGrab;

    /**
     * 优车好货（秒抢货源） 刷新未结束或结束展示文案
     */
    private String instantGrabResendOverWord;

    /**
     * 加价按钮展示样式 1:按钮；2:卡片
     */
    private Integer addMoneyButtonStyle;


    private String distance;

    /**
     * 开票货源开票主体ID
     */
    private Long invoiceSubjectId;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    private Integer driverDriving;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 是否允许优车2.0可电议 (1可电议，0不可电议)
     */
    private Integer isAllowTeleNegotiation = 0;

    /**
     * 开票货源指派车方联系方式
     */
    private String assignCarTel;

    /**
     * 货类
     */
    private String goodTypeName;

}
