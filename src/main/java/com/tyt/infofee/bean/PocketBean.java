package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2016-11-19上午11:05:32
 * @description
 */
public class PocketBean {

	private Integer id;
	/*
	 * 用户id
	 */
	private Integer userId;

	/*
	 * 当前余额
	 */
	private Long currentBalance;
	/*
	 * 余额类型，1: 钱包余额，2：积分，3：冻结资金, 4: 公司入账 5：公司出账 6：手续费 其他待定
	 */
	private Integer affiliatedType;
	/*
	 * 创建时间
	 */
	private String createTime;
	/*
	 * 更新时间
	 */
	private String updateTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Long getCurrentBalance() {
		return currentBalance;
	}

	public void setCurrentBalance(Long currentBalance) {
		this.currentBalance = currentBalance;
	}

	public Integer getAffiliatedType() {
		return affiliatedType;
	}

	public void setAffiliatedType(Integer affiliatedType) {
		this.affiliatedType = affiliatedType;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
