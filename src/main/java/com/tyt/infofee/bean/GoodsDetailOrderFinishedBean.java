package com.tyt.infofee.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 信息费详情，装货中的部分订单信息
 * <AUTHOR>
 *
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsDetailOrderFinishedBean {
	
	private String goodsOrderNo;//运单号
	private String carOwnerTelephone;//车主联系电话
	private Long payAgencyMoney;//支付金额单位分
	private Date payEndTime;//支付完成时间
	private Date goodsOwnerAgreeTime;//同意装货时间
	private Date carOwnerLoadfinishedTime;//车主装货完成时间（毫秒）
	
	public String getGoodsOrderNo() {
		return goodsOrderNo;
	}
	public void setGoodsOrderNo(String goodsOrderNo) {
		this.goodsOrderNo = goodsOrderNo;
	}
	public String getCarOwnerTelephone() {
		return carOwnerTelephone;
	}
	public void setCarOwnerTelephone(String carOwnerTelephone) {
		this.carOwnerTelephone = carOwnerTelephone;
	}
	public Long getPayAgencyMoney() {
		return payAgencyMoney;
	}
	public void setPayAgencyMoney(Long payAgencyMoney) {
		this.payAgencyMoney = payAgencyMoney;
	}
	public Date getPayEndTime() {
		return payEndTime;
	}
	public void setPayEndTime(Date payEndTime) {
		this.payEndTime = payEndTime;
	}
	public Date getGoodsOwnerAgreeTime() {
		return goodsOwnerAgreeTime;
	}
	public void setGoodsOwnerAgreeTime(Date goodsOwnerAgreeTime) {
		this.goodsOwnerAgreeTime = goodsOwnerAgreeTime;
	}
	public Date getCarOwnerLoadfinishedTime() {
		return carOwnerLoadfinishedTime;
	}
	public void setCarOwnerLoadfinishedTime(Date carOwnerLoadfinishedTime) {
		this.carOwnerLoadfinishedTime = carOwnerLoadfinishedTime;
	}
	
}
