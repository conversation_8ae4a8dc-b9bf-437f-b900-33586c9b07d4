package com.tyt.infofee.bean;

import java.util.List;
import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 * @date 2016-11-19下午2:47:19
 * @description
 */
public class UserWalletFlowListBean {
	private int currentPage;
	private int totalRecord;
	private int pageSize;
	private int maxPage;
	List<UserWalletFlowBean> data;

	public int getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}

	public int getTotalRecord() {
		return totalRecord;
	}

	public void setTotalRecord(int totalRecord) {
		this.totalRecord = totalRecord;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public int getMaxPage() {
		return maxPage;
	}

	public void setMaxPage(int maxPage) {
		this.maxPage = maxPage;
	}

	public List<UserWalletFlowBean> getData() {
		return data;
	}

	public void setData(List<UserWalletFlowBean> data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
