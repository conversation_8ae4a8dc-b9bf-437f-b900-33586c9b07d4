package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;
import com.tyt.infofee.bean.MqBaseMessageBean;

/**
 * 同步认证车辆状态到车辆实时位置表消息
 * 
 * <AUTHOR>
 * @date 2018年1月8日下午7:30:22
 * @description
 */
public class MqSyncAuthCarMsg extends MqBaseMessageBean {
	/*
	 * 认证状态 1 未认证成功状态车辆变为认证成功状态 2 认证成功状态车辆变为认证失败状态 3
	 * 编辑认证成功状态的车辆修改了车头牌照信息或者是车尾牌照信息
	 */
	private String type;
	/*
	 * 车头牌照头字母
	 */
	private String headCity;
	/*
	 * 车头牌照号码
	 */
	private String headNo;
	/*
	 * 挂车牌照头字母
	 */
	private String tailCity;
	/*
	 * 挂车牌照号码
	 */
	private String tailNo;
	/*
	 * 车主id
	 */
	private int carOwnerId;
	/*
	 * 车主注册帐号
	 */
	private String carOwnerCellphone;
	/*
	 * 车主认证姓名
	 */
	private String carOwnerName;
	/*
	 * 车辆id
	 */
	private int carId;

	public int getCarId() {
		return carId;
	}

	public void setCarId(int carId) {
		this.carId = carId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getHeadCity() {
		return headCity;
	}

	public void setHeadCity(String headCity) {
		this.headCity = headCity;
	}

	public String getHeadNo() {
		return headNo;
	}

	public void setHeadNo(String headNo) {
		this.headNo = headNo;
	}

	public String getTailCity() {
		return tailCity;
	}

	public void setTailCity(String tailCity) {
		this.tailCity = tailCity;
	}

	public String getTailNo() {
		return tailNo;
	}

	public void setTailNo(String tailNo) {
		this.tailNo = tailNo;
	}

	public int getCarOwnerId() {
		return carOwnerId;
	}

	public void setCarOwnerId(int carOwnerId) {
		this.carOwnerId = carOwnerId;
	}

	public String getCarOwnerCellphone() {
		return carOwnerCellphone;
	}

	public void setCarOwnerCellphone(String carOwnerCellphone) {
		this.carOwnerCellphone = carOwnerCellphone;
	}

	public String getCarOwnerName() {
		return carOwnerName;
	}

	public void setCarOwnerName(String carOwnerName) {
		this.carOwnerName = carOwnerName;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
