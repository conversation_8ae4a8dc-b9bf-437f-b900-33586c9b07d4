package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;

/**
 * 封装mq消息处理内容以及处理状态的实体
 * 
 * <AUTHOR>
 * @date 2016-12-1上午10:46:31
 * @description
 */
public class MqMessageLogBean {
	private Integer id;
	/*
	 * 消息序列号，每个消息有一个唯一的序列号，用于唯一标示一条消息
	 */
	private String messageSerialNum;
	/*
	 * 消息的原始完整内容
	 */
	private String messageContent;
	/*
	 * 消息的处理状态，1：未处理 2：已处理
	 */
	private Integer dealStatus;
	private String createTime;
	private String updateTime;
	/*
	 * 1：退款 2：提现 3：异常 4：成交(车主与货主成交) 5：信息费
	 */
	private Integer messageType;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getMessageSerialNum() {
		return messageSerialNum;
	}

	public void setMessageSerialNum(String messageSerialNum) {
		this.messageSerialNum = messageSerialNum;
	}

	public String getMessageContent() {
		return messageContent;
	}

	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}

	public Integer getDealStatus() {
		return dealStatus;
	}

	public void setDealStatus(Integer dealStatus) {
		this.dealStatus = dealStatus;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getMessageType() {
		return messageType;
	}

	public void setMessageType(Integer messageType) {
		this.messageType = messageType;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
