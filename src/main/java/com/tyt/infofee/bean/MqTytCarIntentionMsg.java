package com.tyt.infofee.bean;

import java.io.Serializable;
import java.util.Date;

/**
 * 货物与意向车主关系
 * <AUTHOR>
 *
 */
public class MqTytCarIntentionMsg extends MqBaseMessageBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -8208049084346430301L;

	//车主ID
	private Long userId;
	//货物原ID
	private Long srcMsgId;
	//utime
	private Date utime;
	
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getSrcMsgId() {
		return srcMsgId;
	}

	public Date getUtime() {
		return utime;
	}

	public void setSrcMsgId(Long srcMsgId) {
		this.srcMsgId = srcMsgId;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}
}
