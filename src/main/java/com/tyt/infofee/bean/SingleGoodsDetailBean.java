package com.tyt.infofee.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.plat.utils.BigDecimalSerialize;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 信息费货物详情
 *
 * <AUTHOR>
 *
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SingleGoodsDetailBean {

	private Long id;// 信息ID
	private Long srcMsgId;// src_msg_id
	private Long userId;// 发布人ID

	/**
	 * 发布人账号
	 */
	private String cellPhone;
	private String startPoint;// 出发地
	private String destPoint;// 目的地
	private String taskContent;// 货物内容
	private Date publishTime;// 发布时间(毫秒)
	/**
	 * 发布时间
	 */
	private Date publishTimeNew;
	private String goodTypeName;
	private Date firstPublishTime;// 首次发布时间(毫秒)
	private String startCoordX;// 出发地坐标x
	private String startCoordY;// 出发地坐标y
	private String destCoordX;// 目的地坐标x
	private String destCoordY;// 目的地坐标y
	private Integer verifyFlag;// 验证标识 0未验证 1验证 信息认证标识共用
	private String startDetailAdd;// 出发地详细地址
	private String startLatitude;// 出发地纬度
	private String startLongitude;// 出发地经度
	private String destDetailAdd;// 目的地详细地址
	private String destLongitude;// 目的地经度
	private String destLatitude;// 目的地纬度
	private String remark;// 其他说明
	private String length;// 长
	private String wide;// 宽
	private String high;// 高
	private String weight;// 重量
	private String telephoneOne;// 联系人1
	private String telephoneTwo;// 联系人2
	private String telephoneThree;// 联系人3
	private String isSuperelevation;// 是否三超(是，否/不/””/null)
	private Integer userType;// 用户类型 1VIP 0使用 2未激活
	private String isCar;// 0否 1是
	private String uploadCellPhone;// 用户发布电话
	private Integer verifyPhotoSign;// 照片认证标志0未认证1通过
	private Integer userPart;// 用户诚信分数
	private Integer status;// 状态 1有效（发布中），0无效，2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
	private String infoStatus;// 信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货 ）3车主装货完成
	// 4系统装货完成 5异常上报6线下成交
	private String isInfoFee;// 0不需要、1需要
	private String nickName;
	/**
	 * 通过aes加密的昵称
	 */
	private String nickNameByAes;

	private String tsOrderNo;
	private String price;// 运费
	private Integer source;
	private String distance;
	private Date regTime;
	/**
	 * 发货人的会员状态 0不是 1是
	 */
	private String goodsPermissionMember;
	/*
	 * 货物型号
	 */
	private String type;
	/*
	 * 货物品牌
	 */
	private String brand;
	/*
	 * 货物的台数，针对标准化的数据
	 */
	private Integer goodNumber;
	/*
	 * 是否是标准化数据：0是，1不是
	 */
	private Integer isStandard;
	/*
	 * 匹配项的ID，针对标准化的数据
	 */
	private Integer matchItemId;

	private Integer androidDistance;
	private Integer iosDistance;

	/**
	 * 2018-1-24 xyy 保险
	 */
	private String startProvinc;//出发地省
	private String startCity;//出发地市
	private String startArea;//出发地区
	private String destProvinc;//目的地省
	private String destCity;//目的地市
	private String destArea;//目的地区
	// 是否需要加密昵称
	private Integer isNeedDecrypt;

	//2018-11-01 xyy pc信息费获取电话加密货物id
	private String goodsIdEncrypt;

	//2018-11-28 人保货运险改动
	//是否购买过保险 1是(显示“查看保单”按钮)  2否(显示“买货运险”按钮)
	private Integer isBuyInsurance;
	//最后一次购买保险的保单Id
	private Long insuranceId;

	//2019-05-09
	private String carLength;  //车辆长度
	private String carType;   //车辆类型
	private String specialRequired;   //特殊要求

	//----------19-08-21---相似货源分组
	/**
	 * 相似编码
	 */
	private String similarityCode;
	/**
	 * 相似货源首发ID
	 */
	private Long similarityFirstId;
	/**
	 * 相似货源首发信息
	 */
	private String similarityFirstInfo;


	//--------------------------------------------------------------------------------------------------
	//车货拆分新增参数
	/**
	 * 开始装车时间
	 */
	private Date beginLoadingTime;
	/**
	 * 装车时间
	 */
	private Date loadingTime;
	/**
	 * 开始卸车时间
	 */
	private Date beginUnloadTime;
	/**
	 * 卸车时间
	 */
	private Date unloadTime;
	/**
	 * 车辆最小长度
	 */
	private BigDecimal carMinLength;
	/**
	 * 车辆最大长度
	 */
	private BigDecimal carMaxLength;
	/**
	 * 挂车样式
	 */
	private String carStyle;
	/**
	 * 工作面高最小值
	 */
	private BigDecimal workPlaneMinHigh;
	/**
	 * 工作面高最大值
	 */
	private BigDecimal workPlaneMaxHigh;
	/**
	 * 工作面长最小值
	 */
	private BigDecimal workPlaneMinLength;
	/**
	 * 工作面长最大值
	 */
	private BigDecimal workPlaneMaxLength;
	/**
	 * 是否需要带爬梯 0 是 1 需要 2不需要
	 */
	private String climb;

	/**
	 * 后台发货货主实际电话
	 */
	private String backendPhone;
	/**
	 * 后台发货货主实际电话2
	 */
	private String backendPhone2;
	/**
	 * 后台发货货主实际电话3
	 */
	private String backendPhone3;

	//所需车辆长度标签 v6110
	private String carLengthLabels;
	/**
	 * 轮胎外露标识 0不限 1是 2 否
	 */
	private String tyreExposedFlag;

	/**
	 * 调车数量
	 */
	private Integer shuntingQuantity;

	/**
	 * 首发货源类型（电议1，一口价2）
	 */
	private Integer firstPublishType;
	/**
	 * 货源类型（电议1，一口价2）
	 */
	private Integer publishType;
	/**
	 * 信息费（分）
	 */
	@JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
	@JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
	private BigDecimal infoFee;
	/**
	 * 车头牌照头字母
	 */
	private String headCity;
	/**
	 * 车头牌照号码
	 */
	private String headNo;
	/**
	 * 挂车牌照头字母
	 */
	private String tailCity;
	/**
	 * 挂车牌照号码
	 */
	private String tailNo;

	/**
	 * 是否是 17.5 米专享 0：否 1：是
	 */
	private Integer exclusiveType;

	/**
	 * 信用分
	 */
	private BigDecimal totalScore;

	/**
	 * 信用分等级 "1 2 3 4 5"
	 */
	private Integer rankLevel;

	/**
	 * 订金类型（0不退还；1退还）
	 */
	private Integer refundFlag;

	/**
	 * 回价助手运费上限
	 */
	private Integer priceCap;

	/**
	 * 企业货源id
	 */
	private Long backendId;

	private Integer orderExist;

	private Integer resendCounts;

	/**
	 * 货源来源1货主；2调度客服；
	 */
	Integer sourceType;

	/**
	 * 官方授权昵称(tea加密)
	 */
	private String authNameTea;

	/**
	 * 官方授权昵称
	 */
	private String authName;

	/**
	 * 是否是保障货源 0：否  1：是
	 */
	private Integer guaranteeGoods;

	/**
	 * 是否已投诉 0：否  1：是
	 */
	private Integer complainedStatus;

	/**
	 * json格式的标签字符串（参考plat内TransportLabelJson类）
	 */
	private String labelJson;

	/**
	 * 出发地经度
	 */
	private String startLongitudeStr;

	/**
	 * 出发地纬度
	 */
	private String startLatitudeStr;

	//是否在两个月内没有发过电议有价货源或者一口价货源（展示限时体验按钮） 0 否 1 是
	private Integer limitTimeExperience;

	private Date priorityRecommendExpireTime;
	/**
	 * 技术服务费
	 */
	private BigDecimal tecServiceFee;

	/**
	 * 是否是优车货源（0:否 1：是）
	 */
	private Integer excellentGoods;

	/**
	 * 司机驾驶此类货物：1-需要，2-不需要
	 */
	private Integer driverDriving;

	/**
	 * 装货联系电话
	 */
	private String loadCellPhone;

	/**
	 * 卸货联系电话
	 */
	private String unloadCellPhone;

	/**
	 * 是否后台货源0不是1是
	 */
	private Integer isBackendTransport = 0;

	/**
	 * YMM 货源版本
	 */
	private String cargoVersion;
	/**
	 * YMM 货源ID
	 */
	private Long cargoId;

	/**
	 *  运满满货源真实货主电话
	 */
	private String giveGoodsPhone;

	/**
	 * 标准货名备注
	 */
	private String machineRemark;
	/**
	 * 优车发货卡id
	 */
	private Long excellentCardId;

	/**
	 * 是否开票货源 0：否；1：是
	 */
	private Integer invoiceTransport;

	/**
	 * 附加运费
	 */
	private String additionalPrice;

	/**
	 * 企业税率
	 */
	private BigDecimal enterpriseTaxRate;

	/**
	 * 是否弹一口价优车2.0二次查看货源详情挽留弹窗
	 */
	private Integer goodCarPriceTransportDetainment;

	/**
	 * 技术服务费（折前），after写错了
	 */
	private BigDecimal tecServiceFeeAfterDiscount;

	/**
	 * 技术服务费折前折后差值
	 */
	private BigDecimal tecServiceFeeAfterDiscountDValue;

	/**
	 * 抽佣文案
	 */
	private String tecServiceFeeInterestsWord;

	/**
	 * 抽佣链接
	 */
	private String tecServiceFeeInterestsUrl;

	/**
	 * 免佣、折扣文案
	 */
	private String tecServiceFeeWord;

	/**
	 * 跳转小会员ID，如果没有代表跳转到大会员
	 */
	private Long tecServiceFeeBuySmallMemberId;

	/**
	 * 开票货源开票主体ID
	 */
	private Long invoiceSubjectId;

	/**
	 * 开票主体简称
	 */
	private String invoiceSubjectTag;

	/**
	 * 开票主体服务商code
	 */
	private String serviceProviderCode;

	private boolean mainSubjectId;

	/**
	 * 报价总人数
	 */
	private Integer quotedPriceCount;

	/**
	 * 最近报价时间
	 */
	private Date lastQuotedPriceTime;

	/**
	 * 用车类型：1-整车，2-零担
	 */
	private Integer useCarType;

	/**
	 * 车方查看一口价货源是否可以看到报价相关组件
	 */
	private Integer carCanQuotedPriceToPriceTransport;

	/**
	 * 专车货源是否可大厅抢单 0：否；1：是
	 */
	private Integer declareInPublic;

	/**
	 * 是否自动重发：1是0否
	 */
	private Integer isAutoResend;

	/**
	 * 秒杀货源：1是0否 (好差单)
	 */
	private Integer seckillGoods;

	/**
	 * 是否隐藏出价按钮：1是0否
	 */
	private Integer hidePriceBox;

	/**
	 * XHL开票-收货人姓名
	 */
	private String consigneeName;

	/**
	 * XHL开票-收货人联系方式
	 */
	private String consigneeTel;

	/**
	 * XHL开票-收货人企业名称
	 */
	private String consigneeEnterpriseName;

	/**
	 * 预付金额
	 */
	private BigDecimal prepaidPrice;
	/**
	 * 到付金额
	 */
	private BigDecimal collectedPrice;
	/**
	 * 回单付金额
	 */
	private BigDecimal receiptPrice;
	/**
	 * 付款方式 0：全额支付  1：分段支付
	 */
	private Integer paymentsType;


	public void initStartLongitudeStr() {
		this.startLongitudeStr = startLongitude;
	}

	public void initStartLatitudeStr() {
		this.startLatitudeStr = startLatitude;
	}

	/**
	 * 相似货源是否有运费
	 */
	private Boolean similarityTransportHavePrice = false;

	/**
	 * 专车签约合作商是否是平台 1:是；0:否
	 */
	private Integer specialCarCooperativeIsNormal;

	/**
	 * 签约合作商ID
	 */
	private Long cargoOwnerId;

	/**
	 * 该货源是否同步至YMM
	 */
	private Boolean syncYmm = false;

	/**
	 * 专车派单总人数
	 */
	private Integer specialCarDispatchCount = 0;

	/**
	 * 货源利益点文案
	 */
	private String benefitLabel;

	/**
	 * 货源利益点标签code
	 */
	private String benefitLabelCode;

	/**
	 * 优惠金额
	 */
	private Integer perkPrice;

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}