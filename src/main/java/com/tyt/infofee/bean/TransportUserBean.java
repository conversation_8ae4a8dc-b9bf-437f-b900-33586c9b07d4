package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportUserBean {

    Integer userClassCode;
    String userClassName;
    Integer identityTypeCode;//一级身份CODE
    String identityTypeName;//一级身份中文名称
    Long userId;//货主用户ID
    //2018-04-12增加销售审核一级身份
    String deliverTypeOneCode;//销售审核一级身份
    String deliverTypeOneName;//销售审核一级身份
    // 2019-01-29 增加 交易合作次数
    //平台交易次数
    private String tradeNums = "0";
    //合作次数
    private String coopNums = "0";
    /**
     * 查看次数
     */
    private String viewCount = "0";
    /**
     * 联系人数
     */
    private String contactCount = "0";

    /**
     * 是否是小套餐会员：0-否，1-是
     */
    @Getter
    @Setter
    private Integer smallMealVip = 0;

    private String transportScore;

    private String tsRank;
    /**
     * 用户显示名称
     */
    private String userShowName;

    /**
     * 用户性别
     */
    private String sex;

    private String callPhone;

    /**
     * 注册时间
     */
    private Date regTime;

    /**
     * 是否需要解密昵称，1 需要 2 不需要
     */
    private Integer isNeedDecrypt;

    /**
     * 用户昵称（加密）
     */
    private String nickName;


    /**
     * 信用分
     */
    private BigDecimal totalScore;

    /**
     * 信用分等级 "1 2 3 4 5"
     */
    private Integer rankLevel;

    //是否认证 0 否 1 是
    private String authed = "0";
    /**
     * 车方信用等级
     */
    @Getter
    @Setter
    private String carCreditRankLevel;
    /**
     * 好评率
     */
    @Getter
    @Setter
    private String feedbackPositiveRating;

    /**
     * 企业名称
     */
    @Getter
    @Setter
    private String enterpriseName;


    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getRankLevel() {
        return rankLevel;
    }

    public void setRankLevel(Integer rankLevel) {
        this.rankLevel = rankLevel;
    }

    public Integer getIsNeedDecrypt() {
        return isNeedDecrypt;
    }

    public void setIsNeedDecrypt(Integer isNeedDecrypt) {
        this.isNeedDecrypt = isNeedDecrypt;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Date getRegTime() {
        return regTime;
    }

    public void setRegTime(Date regTime) {
        this.regTime = regTime;
    }

    public Integer getIdentityTypeCode() {
        return identityTypeCode;
    }

    public void setIdentityTypeCode(Integer identityTypeCode) {
        this.identityTypeCode = identityTypeCode;
    }

    public String getIdentityTypeName() {
        return identityTypeName;
    }

    public void setIdentityTypeName(String identityTypeName) {
        this.identityTypeName = identityTypeName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserClassCode() {
        return userClassCode;
    }

    public void setUserClassCode(Integer userClassCode) {
        this.userClassCode = userClassCode;
    }

    public String getUserClassName() {
        return userClassName;
    }

    public void setUserClassName(String userClassName) {
        this.userClassName = userClassName;
    }

    public String getDeliverTypeOneCode() {
        return deliverTypeOneCode;
    }

    public void setDeliverTypeOneCode(String deliverTypeOneCode) {
        this.deliverTypeOneCode = deliverTypeOneCode;
    }

    public String getDeliverTypeOneName() {
        return deliverTypeOneName;
    }

    public void setDeliverTypeOneName(String deliverTypeOneName) {
        this.deliverTypeOneName = deliverTypeOneName;
    }

    public String getTradeNums() {
        return tradeNums;
    }

    public void setTradeNums(String tradeNums) {
        this.tradeNums = tradeNums;
    }

    public String getCoopNums() {
        return coopNums;
    }

    public void setCoopNums(String coopNums) {
        this.coopNums = coopNums;
    }

    public String getViewCount() {
        return viewCount;
    }

    public void setViewCount(String viewCount) {
        this.viewCount = viewCount;
    }

    public String getContactCount() {
        return contactCount;
    }

    public void setContactCount(String contactCount) {
        this.contactCount = contactCount;
    }

    public String getTransportScore() {
        return transportScore;
    }

    public void setTransportScore(String transportScore) {
        this.transportScore = transportScore;
    }

    public String getTsRank() {
        return tsRank;
    }

    public void setTsRank(String tsRank) {
        this.tsRank = tsRank;
    }

    public String getUserShowName() {
        return userShowName;
    }

    public void setUserShowName(String userShowName) {
        this.userShowName = userShowName;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSex() {
        return sex;
    }

    public String getCallPhone() {
        return callPhone;
    }

    public String getAuthed() {
        return authed;
    }

    public void setAuthed(String authed) {
        this.authed = authed;
    }

    public void setCallPhone(String callPhone) {
        if(StringUtils.isNotBlank(callPhone)){
            this.callPhone = callPhone.replaceAll("(\\d{3})\\d{7}(\\d)","$1*******$2");
        }
    }

    @Override
    public String toString() {
        return "TransportUserBean{" +
                "userClassCode=" + userClassCode +
                ", userClassName='" + userClassName + '\'' +
                ", identityTypeCode=" + identityTypeCode +
                ", identityTypeName='" + identityTypeName + '\'' +
                ", userId=" + userId +
                ", deliverTypeOneCode='" + deliverTypeOneCode + '\'' +
                ", deliverTypeOneName='" + deliverTypeOneName + '\'' +
                ", tradeNums='" + tradeNums + '\'' +
                ", coopNums='" + coopNums + '\'' +
                ", viewCount='" + viewCount + '\'' +
                ", contactCount='" + contactCount + '\'' +
                ", transportScore='" + transportScore + '\'' +
                ", tsRank='" + tsRank + '\'' +
                ", userShowName='" + userShowName + '\'' +
                '}';
    }

}
