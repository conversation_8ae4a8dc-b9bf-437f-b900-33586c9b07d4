package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Description  货主信息费列表对象
 * <AUTHOR>
 * @Date  2018/12/24 15:46
 * @Param
 * @return
 **/
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShipperInfoFeeBean implements Serializable{

	private static final long serialVersionUID = 4457689154128788264L;
	//货主运单列表
    private List<TransportWayBillListBean> shipperInfofeeList;

	/**
	 * 待评价订单数量
	 */
	@Getter
	@Setter
	private Integer feedBackTodoCount;


	public List<TransportWayBillListBean> getShipperInfofeeList() {
		return shipperInfofeeList;
	}

	public void setShipperInfofeeList(List<TransportWayBillListBean> shipperInfofeeList) {
		this.shipperInfofeeList = shipperInfofeeList;
	}
}
