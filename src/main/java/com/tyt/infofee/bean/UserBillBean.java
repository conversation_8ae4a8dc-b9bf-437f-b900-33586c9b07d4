package com.tyt.infofee.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 开户对象
 * <AUTHOR>
 * @date 2020-08-22 11:55:51
 */
@Data
public class UserBillBean {

    /** userId */
    private Long userId;
    /** 订单号 */
    private String orderNo;
    /** 金额 */
    private BigDecimal amount;
    /** 时间 */
    private Date createTime;
    /** 流水描述 */
    private String flowDescription;

    public enum BillTypeEnum {

        //收入 支出 退款
        INCOME,
        EXPEND,
        REFUND;
    }
}
