package com.tyt.infofee.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MqOrderRefundDepositBean
 * @description 退定金实体类
 * @date 2022-10-17 14:00
 */
@Data
public class MqOrderRefundDepositBean extends MqBaseMessageBean {

    /**
     * 订单 ID，满满平台的订单号
     */
    private String orderId;

    /**
     * 退订金原因类型
     */
    private Integer refundReasonType;

    /**
     * 退订金原因描述
     */
    private String refundReasonDesc;

}
