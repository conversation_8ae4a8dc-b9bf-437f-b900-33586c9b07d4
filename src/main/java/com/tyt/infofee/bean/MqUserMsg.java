package com.tyt.infofee.bean;

/**
 * 用户初始化消息bean
 * <AUTHOR>
 *
 */
public class MqUserMsg extends MqBaseMessageBean {
	private Long userId;
	private Long srcMsgId;
    private String cellPhone;
    private String content;
    private Long tsId;

    private String clientSign;

    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

    public String getClientSign() {
        return clientSign;
    }

    public void setClientSign(String clientSign) {
        this.clientSign = clientSign;
    }

    public Long getTsId() {
        return tsId;
    }

    public void setTsId(Long tsId) {
        this.tsId = tsId;
    }
}
