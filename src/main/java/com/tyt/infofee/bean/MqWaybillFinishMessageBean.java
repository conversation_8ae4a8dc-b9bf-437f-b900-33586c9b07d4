package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class MqWaybillFinishMessageBean extends MqBaseMessageBean {
	//运单号
	public String tsOrderNo;
	//车主，支付方userID
	public Long payUserId;
	//货主 收款方userID
	public Long receiverUserId;
	//支付金额
	public Long payAmount;
	
	public String getTsOrderNo() {
		return tsOrderNo;
	}
	public Long getPayUserId() {
		return payUserId;
	}
	public Long getReceiverUserId() {
		return receiverUserId;
	}
	public void setTsOrderNo(String tsOrderNo) {
		this.tsOrderNo = tsOrderNo;
	}
	public void setPayUserId(Long payUserId) {
		this.payUserId = payUserId;
	}
	public void setReceiverUserId(Long receiverUserId) {
		this.receiverUserId = receiverUserId;
	}
	public Long getPayAmount() {
		return payAmount;
	}
	public void setPayAmount(Long payAmount) {
		this.payAmount = payAmount;
	}
}
