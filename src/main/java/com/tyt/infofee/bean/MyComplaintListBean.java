package com.tyt.infofee.bean;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/19
 */
@Data
public class MyComplaintListBean implements Serializable {

    private String tsOrderNo;
    private String startPoint;
    private String destPoint;
    //发布人昵称
    private String pubUserName;
    //车主昵称
    private String payUserName;
    private Integer costStatus;
    private Date payEndTime;

    private Long userId;

    /**
     * 订金类型（0不退还；1退还）
     */
    private Integer refundFlag;

    /**
     * 延迟付款状态 0 未延迟 1 延迟付款  2 拒绝退款延迟
     */
    private Integer delayStatus;

    /**
     * 延迟退款状态 0 未延迟退款 1 延迟退款
     */
    private Integer delayRefundStatus;

    /**
     * 预计退款到账日期
     */
    private Date deRefundDueDate;
    /**
     * 订单状态描述
     */
    private String statusDesc;
    /**
     * 采集时间
     */
    private Date ctime;
    /**
     * 异常上报时间
     */
    private Date exTime;
    /**
     * 异常上报处理状态0初始化1处理中2处理完成3自行处理
     */
    private String exStatus;
    /**
     * 订单id
     */
    private Long orderId;
}
