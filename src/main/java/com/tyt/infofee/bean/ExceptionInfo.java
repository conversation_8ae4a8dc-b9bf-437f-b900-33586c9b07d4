package com.tyt.infofee.bean;

public class ExceptionInfo {
    private String esReason = "";

    private String esOther = "";

    private String esResult = "";

    private String exStatus = "";

    private String comment = "";

    private String carAmount = "";

    private String goodsAmount = "";

    private String platformServiceAmount = "";

    private String carServiceAmount = "";

    private String esTime = "";

    private String esCompleteTime = "";

    private String cancelExTime="";

    public String getEsReason() {
        return esReason;
    }

    public void setEsReason(String esReason) {
        this.esReason = esReason;
    }

    public String getEsOther() {
        return esOther;
    }

    public void setEsOther(String esOther) {
        this.esOther = esOther;
    }

    public String getEsResult() {
        return esResult;
    }

    public void setEsResult(String esResult) {
        this.esResult = esResult;
    }

    public String getCarAmount() {
        return carAmount;
    }

    public void setCarAmount(String carAmount) {
        this.carAmount = carAmount;
    }

    public String getGoodsAmount() {
        return goodsAmount;
    }

    public void setGoodsAmount(String goodsAmount) {
        this.goodsAmount = goodsAmount;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getEsTime() {
        return esTime;
    }

    public void setEsTime(String esTime) {
        this.esTime = esTime;
    }

    public String getEsCompleteTime() {
        return esCompleteTime;
    }

    public void setEsCompleteTime(String esCompleteTime) {
        this.esCompleteTime = esCompleteTime;
    }

    public String getExStatus() {
        return exStatus;
    }

    public void setExStatus(String exStatus) {
        this.exStatus = exStatus;
    }

    public String getCancelExTime() {
        return cancelExTime;
    }

    public void setCancelExTime(String cancelExTime) {
        this.cancelExTime = cancelExTime;
    }

    public String getPlatformServiceAmount() {
        return platformServiceAmount;
    }

    public void setPlatformServiceAmount(String platformServiceAmount) {
        this.platformServiceAmount = platformServiceAmount;
    }

    public String getCarServiceAmount() {
        return carServiceAmount;
    }

    public void setCarServiceAmount(String carServiceAmount) {
        this.carServiceAmount = carServiceAmount;
    }
}
