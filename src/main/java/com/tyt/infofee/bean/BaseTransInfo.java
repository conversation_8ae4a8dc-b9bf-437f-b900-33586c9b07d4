package com.tyt.infofee.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tyt.plat.utils.BigDecimalSerialize;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import com.tyt.config.util.AppConfig;
import com.tyt.util.XXTea;

import java.math.BigDecimal;
import java.util.Date;

public class BaseTransInfo {
    private String tsId = "";
    private String ownerId = "";
    private String ownerName = "";
    private String tradeNums = "0";
    //是否认证 0 否 1 是
    private String authed = "0";
    //是否会员 0 试用 1 是 2未激活
    private String userType = "0";
    //付费剩余天数
    private String serveDays = "0";
    //注册时间
    private String signupTime = "";
    //注册年限
    private String signupYears = "";
    //缴费年限
    private int renewalYears;
    private String taskContent = ""; //货物内容
    private String startPoint = ""; //出发地
    private String destPoint = ""; //目的地
    private String weight = ""; //重量
    private String price = ""; //运价
    private String length = "";
    private String width = "";
    private String height = "";
    private String distance = ""; //距离
    private String startCoordx = "";
    private String startCoordy = "";
    private String destCoordx = "";
    private String destCoordy = "";
    private String startLongitude = "";
    private String startLatitude = "";
    private String destLongitude = "";
    private String destLatitude = "";
    private String startDetailAdd = "";// start_detail_add
    private String destDetailAdd = "";// dest_detail_add
    private String tel = ""; //联系电话
    private String tel3 = ""; //联系电话3
    private String tel4 = ""; //联系电话4
    private String pubTime = ""; //发布时间
    private String remark = ""; //备注
    private Integer isDealCar;
    private Integer excellentGoods;
    /**
     * 调车数量
     */
    private Integer shuntingQuantity;

    public Integer getIsDealCar() {
        return isDealCar;
    }

    public void setIsDealCar(Integer isDealCar) {
        this.isDealCar = isDealCar;
    }
//--------------------------------------------------------------------------------------------------
    //车货拆分新增参数
    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;
    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 车辆最小长度
     */
    private BigDecimal carMinLength;
    /**
     * 车辆最大长度
     */
    private BigDecimal carMaxLength;
    /**
     * 挂车样式
     */
    private String carStyle;
    /**
     * 车辆类型
     */
    private String carType;
    /**
     * 工作面高最小值
     */
    private BigDecimal workPlaneMinHigh;
    /**
     * 工作面高最大值
     */
    private BigDecimal workPlaneMaxHigh;
    /**
     * 工作面长最小值
     */
    private BigDecimal workPlaneMinLength;
    /**
     * 工作面长最大值
     */
    private BigDecimal workPlaneMaxLength;
    /**
     * 货主会员信息
     */
    private String goodsPermissionMember;
    /**
     * 是否需要带爬梯 0 是 1 需要 2不需要
     */
    private String climb;
    /**
     * 后台发货货主实际电话
     */
    private String backendPhone;
    /**
     * 所需车辆长度标签
     */
    private String carLengthLabels;
    /**
     * 轮胎外露标识 0不限 1是 2 否
     */
    private String tyreExposedFlag;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;


    /**
     * 信息费
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal infoFee;

    /**
     * 技术服务费
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal tecServiceFee;


    /**
     * 分配给平台方技术服务费
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal platformServiceAmount;


    /**
     * 分配给车方技术服务费
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal carServiceAmount;

    /**
     * 分配给车方订金
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal carAmount;


    /**
     * 分配给货方订金
     */
    @JsonSerialize(using = BigDecimalSerialize.JacksonSerializer.class)
    @JSONField(serializeUsing = BigDecimalSerialize.FastJsonSerializer.class)
    private BigDecimal goodsAmount;




    /**
     * 信用分
     */
    private BigDecimal totalScore;

    /**
     * 信用分等级 "1 2 3 4 5"
     */
    private Integer rankLevel;

    /**
     * 订金类型（0不退还；1退还）
     */
    @Getter
    @Setter
    private Integer refundFlag;

    /**
     * 官方授权名称
     */
    @Getter
    @Setter
    private String authName;

    /**
     * 官方授权名称（tea加密）
     */
    @Getter
    @Setter
    private String authNameTea;

    /**
     * 保障货源（1是；0否；）
     */
    @Getter
    @Setter
    private Integer guaranteeGoods;

    /**
     * 货源来源（1货主；2调度客服；3:个人货主 4:运满满）
     */
    @Getter
    @Setter
    private Integer sourceType;

    /**
     * 货名备注
     */
    @Getter
    @Setter
    private String machineRemark;

    /**
     * 是否开票货源 0：否；1：是
     */
    @Getter
    @Setter
    private Integer invoiceTransport;

    /**
     * 附加运费
     */
    @Getter
    @Setter
    private String additionalPrice;

    /**
     * 企业税率
     */
    @Getter
    @Setter
    private BigDecimal enterpriseTaxRate;

    /**
     * 评价状态 0 未评价 1 已评价
     */
    private Integer evaluateStatus = 0;

    /**
     * 是否能发表评论
     */
    private boolean canPostFeedBack;

    /**
     * 订单状态
     */
    private Integer costStatus;


    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getRankLevel() {
        return rankLevel;
    }

    public void setRankLevel(Integer rankLevel) {
        this.rankLevel = rankLevel;
    }

    public String getBackendPhone() {
        return backendPhone;
    }

    public void setBackendPhone(String backendPhone) {
        this.backendPhone = backendPhone;
    }

    public String getGoodsIdEncrypt() {
        return XXTea.Encrypt(tsId, AppConfig.getProperty("tyt.xxtea.key"));
    }
    public String getTsId() {
        return tsId;
    }

    public void setTsId(String tsId) {
        this.tsId = tsId;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getTradeNums() {
        return tradeNums;
    }

    public void setTradeNums(String tradeNums) {
        this.tradeNums = tradeNums;
    }

    public String getAuthed() {
        return authed;
    }

    public void setAuthed(String authed) {
        this.authed = authed;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getTaskContent() {
        return taskContent;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getDestPoint() {
        return destPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getTel3() {
        return tel3;
    }

    public void setTel3(String tel3) {
        this.tel3 = tel3;
    }

    public String getTel4() {
        return tel4;
    }

    public void setTel4(String tel4) {
        this.tel4 = tel4;
    }

    public String getPubTime() {
        return pubTime;
    }

    public void setPubTime(String pubTime) {
        this.pubTime = pubTime;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getStartCoordx() {
        if(StringUtils.isNotEmpty(startCoordx)){
            BigDecimal startCoodxDec = new BigDecimal(startCoordx);
            return startCoodxDec.divide(new BigDecimal("100")).toString();
        }
        return startCoordx;
    }

    public void setStartCoordx(String startCoordx) {
        this.startCoordx = startCoordx;
    }

    public String getStartCoordy() {
        if(StringUtils.isNotEmpty(startCoordy)){
            BigDecimal startCoordyDec = new BigDecimal(startCoordy);
            return startCoordyDec.divide(new BigDecimal("100")).toString();
        }
        return startCoordy;
    }

    public void setStartCoordy(String startCoordy) {
        this.startCoordy = startCoordy;
    }

    public String getDestCoordx() {
        if(StringUtils.isNotEmpty(destCoordx)){
            BigDecimal destCoodxDec = new BigDecimal(destCoordx);
            return destCoodxDec.divide(new BigDecimal("100")).toString();
        }
        return destCoordx;
    }

    public void setDestCoordx(String destCoordx) {
        this.destCoordx = destCoordx;
    }

    public String getDestCoordy() {
        if(StringUtils.isNotEmpty(destCoordy)){
            BigDecimal destCoodyDec = new BigDecimal(destCoordy);
            return destCoodyDec.divide(new BigDecimal("100")).toString();
        }
        return destCoordy;
    }

    public void setDestCoordy(String destCoordy) {
        this.destCoordy = destCoordy;
    }

    public String getStartDetailAdd() {
        return startDetailAdd;
    }

    public void setStartDetailAdd(String startDetailAdd) {
        this.startDetailAdd = startDetailAdd;
    }

    public String getDestDetailAdd() {
        return destDetailAdd;
    }

    public void setDestDetailAdd(String destDetailAdd) {
        this.destDetailAdd = destDetailAdd;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSignupTime() {
        return signupTime;
    }

    public void setSignupTime(String signupTime) {
        this.signupTime = signupTime;
    }

    public int getRenewalYears() {
        return renewalYears;
    }

    public void setRenewalYears(int renewalYears) {
        this.renewalYears = renewalYears;
    }

    public String getSignupYears() {
        return signupYears;
    }

    public void setSignupYears(String signupYears) {
        this.signupYears = signupYears;
    }


    public String getStartLongitude() {
        if(StringUtils.isNotEmpty(startLongitude)){
            BigDecimal temp = new BigDecimal(startLongitude);
            if(startLongitude.length()>6) {
                return temp.movePointLeft(6).toString();
            } else {
                return temp.movePointLeft(2).toString();
            }
        }
        return startLongitude;
    }

    public void setStartLongitude(String startLongitude) {
        this.startLongitude = startLongitude;
    }

    public String getStartLatitude() {
        if(StringUtils.isNotEmpty(startLatitude)){
            BigDecimal temp = new BigDecimal(startLatitude);
            if(startLatitude.length()>6) {
                return temp.movePointLeft(6).toString();
            } else {
                return temp.movePointLeft(2).toString();
            }
        }
        return startLatitude;
    }

    public void setStartLatitude(String startLatitude) {
        this.startLatitude = startLatitude;
    }

    public String getDestLongitude() {
        if(StringUtils.isNotEmpty(destLongitude)){
            BigDecimal temp = new BigDecimal(destLongitude);
            if(destLongitude.length()>6) {
                return temp.movePointLeft(6).toString();
            } else {
                return temp.movePointLeft(2).toString();
            }
        }
        return destLongitude;
    }

    public void setDestLongitude(String destLongitude) {
        this.destLongitude = destLongitude;
    }

    public String getDestLatitude() {
        if(StringUtils.isNotEmpty(destLatitude)){
            BigDecimal temp = new BigDecimal(destLatitude);
            if(destLatitude.length()>6) {
                return temp.movePointLeft(6).toString();
            } else {
                return temp.movePointLeft(2).toString();
            }
        }
        return destLatitude;
    }

    public void setDestLatitude(String destLatitude) {
        this.destLatitude = destLatitude;
    }

    public String getServeDays() {
        return serveDays;
    }

    public void setServeDays(String serveDays) {
        this.serveDays = serveDays;
    }

    public Date getLoadingTime() {
        return loadingTime;
    }

    public void setLoadingTime(Date loadingTime) {
        this.loadingTime = loadingTime;
    }

    public Date getUnloadTime() {
        return unloadTime;
    }

    public void setUnloadTime(Date unloadTime) {
        this.unloadTime = unloadTime;
    }

    public BigDecimal getCarMinLength() {
        return carMinLength;
    }

    public void setCarMinLength(BigDecimal carMinLength) {
        this.carMinLength = carMinLength;
    }

    public BigDecimal getCarMaxLength() {
        return carMaxLength;
    }

    public void setCarMaxLength(BigDecimal carMaxLength) {
        this.carMaxLength = carMaxLength;
    }

    public String getCarStyle() {
        return carStyle;
    }

    public void setCarStyle(String carStyle) {
        this.carStyle = carStyle;
    }

    public BigDecimal getWorkPlaneMinHigh() {
        return workPlaneMinHigh;
    }

    public void setWorkPlaneMinHigh(BigDecimal workPlaneMinHigh) {
        this.workPlaneMinHigh = workPlaneMinHigh;
    }

    public BigDecimal getWorkPlaneMaxHigh() {
        return workPlaneMaxHigh;
    }

    public void setWorkPlaneMaxHigh(BigDecimal workPlaneMaxHigh) {
        this.workPlaneMaxHigh = workPlaneMaxHigh;
    }

    public BigDecimal getWorkPlaneMinLength() {
        return workPlaneMinLength;
    }

    public void setWorkPlaneMinLength(BigDecimal workPlaneMinLength) {
        this.workPlaneMinLength = workPlaneMinLength;
    }

    public BigDecimal getWorkPlaneMaxLength() {
        return workPlaneMaxLength;
    }

    public void setWorkPlaneMaxLength(BigDecimal workPlaneMaxLength) {
        this.workPlaneMaxLength = workPlaneMaxLength;
    }

    public String getClimb() {
        return climb;
    }

    public void setClimb(String climb) {
        this.climb = climb;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public Date getBeginLoadingTime() {
        return beginLoadingTime;
    }

    public void setBeginLoadingTime(Date beginLoadingTime) {
        this.beginLoadingTime = beginLoadingTime;
    }

    public Date getBeginUnloadTime() {
        return beginUnloadTime;
    }

    public void setBeginUnloadTime(Date beginUnloadTime) {
        this.beginUnloadTime = beginUnloadTime;
    }

    public String getGoodsPermissionMember() {
        return goodsPermissionMember;
    }

    public void setGoodsPermissionMember(String goodsPermissionMember) {
        this.goodsPermissionMember = goodsPermissionMember;
    }

    public String getCarLengthLabels() {
        return carLengthLabels;
    }

    public void setCarLengthLabels(String carLengthLabels) {
        this.carLengthLabels = carLengthLabels;
    }

    public String getTyreExposedFlag() {
        return tyreExposedFlag;
    }

    public void setTyreExposedFlag(String tyreExposedFlag) {
        this.tyreExposedFlag = tyreExposedFlag;
    }


    public Integer getShuntingQuantity() {
        return shuntingQuantity;
    }

    public void setShuntingQuantity(Integer shuntingQuantity) {
        this.shuntingQuantity = shuntingQuantity;
    }

    public Integer getPublishType() {
        return publishType;
    }

    public void setPublishType(Integer publishType) {
        this.publishType = publishType;
    }

    public Integer getExcellentGoods() {
        return excellentGoods;
    }

    public void setExcellentGoods(Integer excellentGoods) {
        this.excellentGoods = excellentGoods;
    }

    public BigDecimal getInfoFee() {
        return infoFee;
    }

    public void setInfoFee(BigDecimal infoFee) {
        this.infoFee = infoFee;
    }

    public BigDecimal getTecServiceFee() {
        return tecServiceFee;
    }

    public void setTecServiceFee(BigDecimal tecServiceFee) {
        this.tecServiceFee = tecServiceFee;
    }

    public Integer getEvaluateStatus() {
        return evaluateStatus;
    }

    public void setEvaluateStatus(Integer evaluateStatus) {
        this.evaluateStatus = evaluateStatus;
    }

    public boolean isCanPostFeedBack() {
        return canPostFeedBack;
    }

    public void setCanPostFeedBack(boolean canPostFeedBack) {
        this.canPostFeedBack = canPostFeedBack;
    }

    public Integer getCostStatus() {
        return costStatus;
    }

    public void setCostStatus(Integer costStatus) {
        this.costStatus = costStatus;
    }
    public BigDecimal getPlatformServiceAmount() {
        return platformServiceAmount;
    }

    public void setPlatformServiceAmount(BigDecimal platformServiceAmount) {
        this.platformServiceAmount = platformServiceAmount;
    }

    public BigDecimal getCarServiceAmount() {
        return carServiceAmount;
    }

    public void setCarServiceAmount(BigDecimal carServiceAmount) {
        this.carServiceAmount = carServiceAmount;
    }

    public BigDecimal getCarAmount() {
        return carAmount;
    }

    public void setCarAmount(BigDecimal carAmount) {
        this.carAmount = carAmount;
    }

    public BigDecimal getGoodsAmount() {
        return goodsAmount;
    }

    public void setGoodsAmount(BigDecimal goodsAmount) {
        this.goodsAmount = goodsAmount;
    }
}
