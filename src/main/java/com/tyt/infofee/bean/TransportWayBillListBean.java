package com.tyt.infofee.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransportWayBillListBean implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -3356382774976190449L;
    private String tsOrderNo;
    private Long tsId;
    private Long sortId;
    private Long userId;
    private String startPoint;
    private String destPoint;
    private String taskContent;
    private String isInfoFee;
    private Date publishTime;
    private Date payEndTime;
    private String payLinkPhone;
    private Long payAmount;
    private Integer payNumber;
    private Date agreeTime;
    private String infoStatus;

    public Date getPayEndTime() {
        return payEndTime;
    }

    public void setPayEndTime(Date payEndTime) {
        this.payEndTime = payEndTime;
    }

    private Date loadTime;
    private Long payUserId;

    //2018-11-28 人保货运险改动
    //是否购买过保险 1是(显示“查看保单”按钮)  2否(显示“买货运险”按钮)
    private Integer isBuyInsurance;
    //最后一次购买保险的保单Id
    private Long insuranceId;
    //最后一次购买保险的保单状态 0待支付 1已生效 2已退保
    private Integer insuranceStatus;

    //2018-11-24 货主信息费列表
    private List<TransportOrdersListBean> infofeeList;
    //创建时间
    private Date createTime;
    //重量 单位：吨
    private String weight;
    //--------------------------------------------------------------------------------------------------
    //车货拆分新增参数
    /**
     * 货物长单位米
     */
    private String length;
    /**
     * 货物宽单位米
     */
    private String wide;
    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;
    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 运价
     */
    private String price;
    /**
     * 是否后台货源0不是1是
     */
    private Integer isBackendTransport=0;
    /**
     * 限时货源标识
     */
    private Integer timeLimitIdentification;

    /**
     * 货源类型（电议1，一口价2）
     */
    @Getter
    @Setter
    private Short publishType;

    /**
     * 限时货源状态
     */
    private Integer status;

    /**
     * 状态
     */
    private Integer orderStatus;
    /**
     * 是否已确认取消 0未确认 1已确认
     */
    private Integer cancelConfirm;

    /**
     * 保障货源（1是；0否；）
     */
    private Integer guaranteeGoods;

    /**
     * 是否是优车货源（0:否 1：是）
     **/
    private Integer excellentGoods;

    /**
     * 标准货名备注
     */
    private String machineRemark;

    public Integer getCancelConfirm() {
        return cancelConfirm;
    }

    public void setCancelConfirm(Integer cancelConfirm) {
        this.cancelConfirm = cancelConfirm;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getTimeLimitIdentification() {
        return timeLimitIdentification;
    }

    public void setTimeLimitIdentification(Integer timeLimitIdentification) {
        this.timeLimitIdentification = timeLimitIdentification;
    }

    public Integer getIsBackendTransport() {
        return isBackendTransport;
    }

    public void setIsBackendTransport(Integer isBackendTransport) {
        this.isBackendTransport = isBackendTransport;
    }

    public String getTsOrderNo() {
        return tsOrderNo;
    }

    public Long getTsId() {
        return tsId;
    }

    public Long getSortId() {
        return sortId;
    }

    public Long getUserId() {
        return userId;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public String getDestPoint() {
        return destPoint;
    }

    public String getTaskContent() {
        return taskContent;
    }

    public String getIsInfoFee() {
        return isInfoFee;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public String getPayLinkPhone() {
        return payLinkPhone;
    }

    public Long getPayAmount() {
        return payAmount;
    }

    public Integer getPayNumber() {
        return payNumber;
    }

    public Date getAgreeTime() {
        return agreeTime;
    }

    public Date getLoadTime() {
        return loadTime;
    }

    public void setTsOrderNo(String tsOrderNo) {
        this.tsOrderNo = tsOrderNo;
    }

    public void setTsId(Long tsId) {
        this.tsId = tsId;
    }

    public void setSortId(Long sortId) {
        this.sortId = sortId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public void setDestPoint(String destPoint) {
        this.destPoint = destPoint;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public void setIsInfoFee(String isInfoFee) {
        this.isInfoFee = isInfoFee;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public void setPayLinkPhone(String payLinkPhone) {
        this.payLinkPhone = payLinkPhone;
    }

    public void setPayAmount(Long payAmount) {
        this.payAmount = payAmount;
    }

    public void setPayNumber(Integer payNumber) {
        this.payNumber = payNumber;
    }

    public void setAgreeTime(Date agreeTime) {
        this.agreeTime = agreeTime;
    }

    public void setLoadTime(Date loadTime) {
        this.loadTime = loadTime;
    }

    public Long getPayUserId() {
        return payUserId;
    }

    public void setPayUserId(Long payUserId) {
        this.payUserId = payUserId;
    }

    public String getInfoStatus() {
        return infoStatus;
    }

    public void setInfoStatus(String infoStatus) {
        this.infoStatus = infoStatus;
    }

    public Integer getIsBuyInsurance() {
        return isBuyInsurance;
    }

    public void setIsBuyInsurance(Integer isBuyInsurance) {
        this.isBuyInsurance = isBuyInsurance;
    }

    public Long getInsuranceId() {
        return insuranceId;
    }

    public void setInsuranceId(Long insuranceId) {
        this.insuranceId = insuranceId;
    }

    public Integer getInsuranceStatus() {
        return insuranceStatus;
    }

    public void setInsuranceStatus(Integer insuranceStatus) {
        this.insuranceStatus = insuranceStatus;
    }

    public List<TransportOrdersListBean> getInfofeeList() {
        return infofeeList;
    }

    public void setInfofeeList(List<TransportOrdersListBean> infofeeList) {
        this.infofeeList = infofeeList;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getWide() {
        return wide;
    }

    public void setWide(String wide) {
        this.wide = wide;
    }

    public String getHigh() {
        return high;
    }

    public void setHigh(String high) {
        this.high = high;
    }

    public Date getLoadingTime() {
        return loadingTime;
    }

    public void setLoadingTime(Date loadingTime) {
        this.loadingTime = loadingTime;
    }

    public Date getUnloadTime() {
        return unloadTime;
    }

    public void setUnloadTime(Date unloadTime) {
        this.unloadTime = unloadTime;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Date getBeginLoadingTime() {
        return beginLoadingTime;
    }

    public void setBeginLoadingTime(Date beginLoadingTime) {
        this.beginLoadingTime = beginLoadingTime;
    }

    public Date getBeginUnloadTime() {
        return beginUnloadTime;
    }

    public void setBeginUnloadTime(Date beginUnloadTime) {
        this.beginUnloadTime = beginUnloadTime;
    }

    public void addInfofee(TransportOrdersListBean infoFeeBean){
        if(this.infofeeList == null){
            this.infofeeList = new ArrayList<>();
        }
        this.infofeeList.add(infoFeeBean);
    }

    public Integer getGuaranteeGoods() {
        return guaranteeGoods;
    }

    public void setGuaranteeGoods(Integer guaranteeGoods) {
        this.guaranteeGoods = guaranteeGoods;
    }

    public Integer getExcellentGoods() {
        return excellentGoods;
    }

    public void setExcellentGoods(Integer excellentGoods) {
        this.excellentGoods = excellentGoods;
    }

    public String getMachineRemark() {
        return machineRemark;
    }

    public void setMachineRemark(String machineRemark) {
        this.machineRemark = machineRemark;
    }

}
