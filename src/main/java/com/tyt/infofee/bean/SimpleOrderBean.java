package com.tyt.infofee.bean;

public class SimpleOrderBean {

    private Long id;//主键
    private String robStatus;//接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态） 12车方取消订单 13.异常处理完成
    private String payStatus;//支付状态0待支付1支付失败2支付成功
    private Integer costStatus;//信息费状态：5货源撤销退款 6待支付关闭 10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成
    private Long payAmount;//支付金额

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRobStatus() {
        return robStatus;
    }

    public void setRobStatus(String robStatus) {
        this.robStatus = robStatus;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public Integer getCostStatus() {
        return costStatus;
    }

    public void setCostStatus(Integer costStatus) {
        this.costStatus = costStatus;
    }

    public Long getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(Long payAmount) {
        this.payAmount = payAmount;
    }
}
