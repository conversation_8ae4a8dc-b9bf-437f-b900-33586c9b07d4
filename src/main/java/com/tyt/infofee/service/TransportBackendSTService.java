package com.tyt.infofee.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.*;

import java.util.List;

public interface TransportBackendSTService extends BaseService<TytTransportBackend , Long> {

    ResultMsgBean confirm(TytTransportBackend transportBackend,Long id,Long userId);

    TytTransportBackend selectById(Long id);

    TytTransportBackend selectbackendById(Long id);

    void updateById(Long id);

    TytTransportOrders selectOrdersByIds(Long SrcMsgId);

    TytTransportOrders selectOrdersById(Long SrcMsgId);

    TransportMain selectmainById(Long srcMsgId);

    TransportMain selectmainBySrcId(Long srcMsgId);

    /**
     * 企业货源 装货/卸货 状态
     * @param id backend表 id
     * @param status 状态 1-待接单 2-已取消 3-已接单 4-已完成 5-无效 6-货源已成交 7-已装货 8-已卸货
     * @param orderStatus 订单状态 10-待接单 20-已取消 30-已接单未发布 31-已接单发布中 32-已接单撤销 33-已接单过期 40-已完成 50-后台设置为无效 60-货源已成交 70-已装货 80-已卸货
     */
    void updateUnloadById(Long id , Integer status , Integer orderStatus);
}
