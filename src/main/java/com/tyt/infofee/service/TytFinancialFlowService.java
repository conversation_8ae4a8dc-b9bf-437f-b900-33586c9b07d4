package com.tyt.infofee.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.Order;
import com.tyt.model.TytTradeinfo;

public interface TytFinancialFlowService extends BaseService<TytTradeinfo, Long> {

    /**
     * 添加流水记录
     *
     * @param flowType
     * @param accountSymbolPositive 符号
     * @param amount
     * @param applyId               提现表申请id
     * @param acountId
     * @param beforeRemaining
     * @param afterRemaining
     * @param userId
     */
    void addFinancialFlow(int flowType, String accountSymbolPositive, long amount, int applyId, long acountId, long beforeRemaining, long afterRemaining, long userId);


}
