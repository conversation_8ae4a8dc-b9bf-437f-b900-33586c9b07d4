package com.tyt.infofee.service;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.PracticeBean;
import com.tyt.model.PracticeTheCar;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;
import com.tyt.user.bean.CarSaveBean;

import java.util.List;

public interface PushPracticeTheCarService extends BaseService<TytTransportOrders, Long> {


    List<PracticeTheCar> getPractice(Long userId, Long queryTrade);

    ResultMsgBean saveWayBill(Long userId, Long goodsId, String telephone, Long agencyMoney, Integer carOwnerPayType, Integer carriageFee, CarSaveBean infoFeeCarRequest,Integer driverId,Integer invoiceTransport);

}
