package com.tyt.infofee.service;

import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserBankcard;

public interface TytUserBankcardService extends BaseService<TytUserBankcard, Long>{

	/**
	 * 根据userid获取该用户所有银行卡
	 * @param userId
	 * @return
	 */
	List<TytUserBankcard> getByUserId(Long userId);

	/**
	 * 保存银行卡
	 * @param userId
	 * @param card_num
	 * @param bankName 
	 * @return 
	 */
	String saveBankcard(Long userId, String card_num);

	/**
	 * 删除银行卡操作
	 * @param bankcardId
	 */
	void deleteBankcard(Long bankcardId);

}
