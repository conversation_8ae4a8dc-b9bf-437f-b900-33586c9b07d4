package com.tyt.infofee.service;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.UserCancelBean;
import com.tyt.infofee.bean.UserCancelNoticeBean;
import com.tyt.model.TytUserCancel;
import com.tyt.model.User;

public interface UserCancelService extends BaseService<TytUserCancel, Long> {

    /**
     * @description 将用户账号注销信息保存到数据库
     * <AUTHOR>
     * @date 2021/12/22 16:48
     * @param userCancelBean
     * @param user
     * @param cellPhone
     * @return void
     */
    void insertUserCancel(UserCancelBean userCancelBean, User user, String cellPhone);

    /**
     * @description 修改用户相关信息并退出登录
     * <AUTHOR>
     * @date 2021/12/27 10:39
     * @param userCancelBean
     * @param userId
     * @param user
     * @param cellPhone
     * @return void
     */
    void updateUserRelInfoAndLogOut(UserCancelBean userCancelBean, Long userId, User user, String cellPhone) throws Exception;

    /**
     * @description 根据手机号查询用户注销记录是否存在
     * <AUTHOR>
     * @date 2022/2/9 18:37
     * @param cellPhone 手机号
     * @return boolean
     */
    boolean isUserCancelInfoExist(String cellPhone);

    Integer getCellPhone(String cellPhone);

    UserCancelNoticeBean getCountByCellPhoneAndGoodsPort(String cellPhone);
}
