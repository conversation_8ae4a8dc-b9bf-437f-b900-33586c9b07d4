package com.tyt.infofee.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.BackoutReason;
import com.tyt.model.Transport;


public interface BackoutReasonService extends BaseService<BackoutReason, Long>{

	/**
	 * 增加撤销原因
	 * @param transport 货源信息对象
	 * @param backoutReasonKey 撤销原因key 字典的name
	 * @param backoutReasonValue 撤销原因Value 字典的value
	 */
	void addBackoutReason(Transport transport, String backoutReasonKey, Integer backoutReasonValue,String specificReason,String remark, String backoutReasonKeyNew, Integer backoutReasonValueNew);

	//修改状态
	void updateStatus(Transport lockTransport);

}
