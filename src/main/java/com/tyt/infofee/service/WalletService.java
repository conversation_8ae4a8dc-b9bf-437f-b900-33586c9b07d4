package com.tyt.infofee.service;

import java.math.BigDecimal;
import java.util.List;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.PocketBean;
import com.tyt.infofee.bean.ShortMsgBean;
import com.tyt.infofee.bean.UserWalletBean;
import com.tyt.infofee.bean.UserWalletFlowBean;
import com.tyt.infofee.bean.UserWalletFlowListBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;

/**
 * 
 * <AUTHOR>
 * @date 2016-11-19上午10:56:13
 * @description
 */
public interface WalletService extends BaseService<TytTransportOrders, Long> {

	List<PocketBean> getAccountByUserId(String userId);

	List<UserWalletFlowBean> getUserPocketFlowTopThree(String userId);

	/**
	 * 
	 * @param userId
	 * @param currentPage
	 * @return
	 */
	UserWalletFlowListBean getUserPocketFlow(String userId, String currentPage, String year, String month,
				String day, String type,String pageSize);

	/**
	 * 保存提现申请信息
	 * 
	 * @param userId
	 *            用户id
	 * @param cardOwnerName
	 *            卡所属人姓名
	 * @param cardNumber
	 *            卡号
	 * @param cardDepositBank
	 *            卡开户行
	 * @param drawAccount
	 *            提现金额
	 * @param cardBlongedBank
	 *            卡所属银行
	 */
	ShortMsgBean saveWithdraw(String userId, String cardOwnerName, String cardNumber, String cardDepositBank, Integer drawAccount, String cardBlongedBank);

	/**
	 * 根据用户id获取用户的钱包账户信息
	 * 
	 * @param userId
	 * @return
	 */
	UserWalletBean getUserWalletAccount(String userId);

	void saveSendMessage(String userId, String cardBlongedBank, String cardNumber, Integer drawAccount);

	void updatePocketPwd(String userId, Integer type, String pwd, ResultMsgBean rm);

	BigDecimal getUserSumWalletAccount(String userId);

	/**
	 * 获取新记账系统账户余额
	 * @param userId
	 * @return
	 */
	BigDecimal getUserAccountBalance(String userId) throws Exception;

	/**
	 * 获取满帮系统账户余额
	 * @param userId
	 * @return
	 */
	BigDecimal getManBangUserAccountBalance(String userId,String ticket) throws Exception;
}
