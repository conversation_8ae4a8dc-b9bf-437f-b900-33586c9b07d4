package com.tyt.infofee.service;

import com.tyt.acvitity.bean.TransportOrders;
import com.tyt.base.service.BaseService;
import com.tyt.callPhoneRecord.bean.RecentCallListBean;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.InfofeeStatusEnum;
import com.tyt.manbang.bean.request.MbOrderCreateSucNoticeBean;
import com.tyt.model.*;
import com.tyt.plat.entity.base.MybatisTytTransportOrders;
import com.tyt.plat.entity.base.TytMbCargoSyncInfo;
import com.tyt.plat.vo.invoice.ThirdDominantInfoVo;
import com.tyt.plat.vo.ts.TransportOrderCountVo;
import com.tyt.user.bean.CarSaveBean;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface TransportOrdersService extends BaseService<TytTransportOrders, Long> {
    /**
     * 已接列表查询（待支付/待同意/待装货/已成交/决绝退费）
     *
     * @param userId
     * @param queryActionType
     * @param queryMenuType   1待支付2待同意3待装货4已成交5决绝退费
     * @param queryID
     * @return ListDataBean
     */
    ListDataBean updateGetMyOrdersList(Long userId, int queryActionType, int queryMenuType, long queryID) throws Exception;

    /**
     * 已接列表查询（全部状态）
     * @param userId
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @param queryID 下拉是0，上滑是最小sortId；（首次queryID=0）
     * @return
     * @throws Exception
     */
    ListDataBean updateGetMyOrdersAllList(Long userId, int queryActionType, long queryID) throws Exception;



    /**
     * 信息费详情删除
     * @param deParty 1车方 2 货方
     * @param tsOrderNo 运单号
     * @param payUserId  车方userId
     * @throws Exception
     */
    int updateOrderIsDisplay(String tsOrderNo,String deParty, Long payUserId) throws Exception;

    /**
     * 已接列表查询（待支付/待同意/待装货/已成交/拒绝退费/卸货完成）
     *
     * @param userId
     * @param queryActionType
     * @param queryMenuType   1.待支付（企业不支持）、2.待同意、3.待装货、4.已成交（企业不支持）、5.拒绝/退费（企业不支持）、6.装货完成、7.卸货完成
     * @param queryID
     * @param queryCID B端企业订单ID
     * @return ListDataBean
     */
    ListDataBean getMergeOrdersList(Long userId, int queryActionType, int queryMenuType, long queryID, long queryCID) throws Exception;

    /**
     * 车主待支付信息费接口 - 信息费改版新接口方法
     *
     * @param userId
     * @param queryActionType
     * @param queryMenuType   1.待支付
     * @param queryID
     * @return ListDataBean
     */
    ListDataBean getCarOwnerUnpaidList(Long userId, int queryActionType, int queryMenuType, long queryID) throws Exception;

    /**
     * 保存订单信息
     *
     * @param originalTransport
     * @param carOwnerTelephone
     * @param agencyMoney
     * @param payUser
     * @param infoFeeCarRequest:车辆部分信息
     * @throws Exception
     */
    TytTransportOrders saveOrderInfo(Integer zeroAssignOrder,MbOrderCreateSucNoticeBean orderCreateSucNoticeBean, Transport originalTransport, String carOwnerTelephone,
                                     Long agencyMoney, User payUser, Integer timeLimitIdentification, Integer carriageFee, CarSaveBean infoFeeCarRequest,
                                     BigDecimal couponAmount, Long tecServiceFee,String technicalServiceNo, TytMbCargoSyncInfo tytMbCargoSyncInfo,
                                     Integer orderNewStatus, Integer driverId, Integer invoiceTransport, ThirdDominantInfoVo thirdDominantInfoVo) throws Exception;
    /**
     * 改变某车主的接单状态
     * @param payUserId:车主userID
     * @param transportNo:运单号
     * @param userId:用户ID
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货
     * @return
     * @throws Exception
     */
//	  public int saveChangeOrderRobStatus(Long carOwnerUserId,String transportNo,String robStatus)throws Exception;

    /**
     * 根据ID修改订单robStatus
     *
     * @param id
     * @param robStatus
     * @return
     * @throws Exception
     */
    int saveChangeRobStatusById(Long id, String robStatus) throws Exception;

    /**
     * 根据ID修改信息费状态(新)
     * 接单状态 robStatus
     * 信息费状态 costStatus
     * @param id
     * @param robStatus
     * @return
     * @throws Exception
     */
    int saveChangeRobStatusByIdNew(Long id, String robStatus, Integer costStatus) throws Exception;
    /**
     * 同意订单
     *
     * @param payUserId:支付人userID 车主的userID,数组集合
     * @param transportNo:运单号
     * @param userId:用户ID
     * @param status:接单状态0待接单     1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货
     * @return
     * @throws Exception
     */
    int saveChangeAgreeOrderStatus(Long[] payUserId, String transportNo,
                                   Long userId) throws Exception;

    /**
     * 获取某笔运单下的下单成功的用户ID
     *
     * @param userId
     * @param transportNo
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @return
     */
    Long[] getOrderPayUserIdByRobStatus(Long userId,
                                        String transportNo, String robStatus) throws Exception;

    /**
     * 根据货主ID、车主ID、运单号获取订单支付部分信息
     *
     * @param userId:货主ID
     * @param carOwnerUserId:车主ID
     * @param transportNo:运单号
     * @return
     */
    GoodsDetailOrderBean getOrder(Long userId, Long carOwnerUserId,
                                  String transportNo, String robStatus);

    /**
     * 获取某车主某运单待支付的记录ID
     *
     * @param userId:车主ID
     * @param carOwnerUserId:车主ID
     * @param transportNo:运单号
     * @return
     */
    Long getOrderId(Long userId, Long carOwnerUserId,
                    String transportNo);

    /**
     * 设置待支付的为接单失败:场景为当同意某个人装货的时候
     *
     * @param userId:货主ID
     * @param transportNo:运单号
     * @param robStatus:抢单状态
     * @return
     */
    int saveChangeWaitPayToFailure(Long userId, String transportNo) throws Exception;

    /**
     * 修改某订单的车主信息
     *
     * @param orderId
     * @param carOwnerTelephone
     * @param agencyMoney
     * @return
     */
    int saveChangeWaitPay(Long orderId, String carOwnerTelephone,
                          Long agencyMoney);

    /**
     * 货物详情中显示的订单信息
     *
     * @param userId:用户ID
     * @param tsOrderNo:运单号
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @return
     */
    List<GoodsDetailOrderBean> getByUser(Long userId,
                                         String tsOrderNo, String robStatus);

    /**
     * 货物详情中显示的订单信息（信息费改版新接口）
     *
     * @param userId:用户ID
     * @param tsOrderNo:运单号
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @param costStatus: 信息费状态 10待支付
     * @return
     */
    List<GoodsDetailOrderBean> getByUserNew(Long userId,
                                            String tsOrderNo, String robStatus, Integer costStatus);
    /**
     * count(*)某笔运单下，除了某些支付者之外的其他支付者订单信息
     *
     * @param userId
     * @param tsOrderNo
     * @param robStatus
     * @param notInPayUserIds:被排除的支付者ID
     * @return
     */
    List<GoodsDetailOrderBean> getExceptPartPayUser(Long userId,
                                                    String tsOrderNo, String robStatus, Long[] notInPayUserIds);

    /**
     * count(*)某笔运单下，除了某些支付者之外的其他支付者数量
     *
     * @param userId
     * @param tsOrderNo
     * @param robStatus
     * @param notInPayUserIds:被排除的支付者ID
     * @return
     */
    Long getExceptPartPayUserCount(Long userId,
                                   String tsOrderNo, String robStatus, Long[] notInPayUserIds);
    /**
     * 货物详情中查询发货方的订单信息
     * @param payUserId:支付用户ID
     * @param tsOrderNo:运单号
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @return
     */
//	  public List<GoodsDetailOrderBean> getByPayUser(Long payUserId,
//				String tsOrderNo,String robStatus)throws Exception;

    /**
     * 货物详情中查询订货方的订单信息
     *
     * @param payUserId:支付用户ID
     * @param tsOrderNo:运单号
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @return
     */
    List<GoodsDetailOrderLoadingBean> getLoadingByPayUser(Long payUserId,
                                                          String tsOrderNo, String robStatus) throws Exception;

    List<PayUserOrderBean> getByPayUserOnce(Long payUserId,
                                            String tsOrderNo) throws Exception;

    /**
     * 查询订单信息
     *
     * @param tsOrderNo
     * @param payUserIds
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @return
     * @throws Exception
     */
    List<GoodsDetailOrderBean> getOrdersByPayUser(String tsOrderNo, Long[] payUserIds, String robStatus) throws Exception;
    /**
     * 查询用户对某运单的所有支付记录
     * @param transportNo:运单号
     * @param payUserId:支付者ID
     * @return
     * @throws Exception
     */
//	  public List<SimpleOrderBean> getPayUserAllRecord(String transportNo, Long payUserId)throws Exception;

    /**
     * 获取某支付者某运单下的最后一笔支付订单
     *
     * @param transportNo
     * @param payUserId
     * @return
     * @throws Exception
     */
    SimpleOrderBean getLastOrderByPayUser(String transportNo, Long payUserId) throws Exception;
    /**
     * 查询用户对某运单的某支付状态下的所有支付记录
     * @param transportNo
     * @param payUserId
     * @return
     * @throws Exception
     */
//	  public List<SimpleOrderBean> getPayUserAllRecord(String transportNo,Long payUserId, String payStatus) throws Exception;

    /**
     * 根据车主ID，运单号查询记录
     *
     * @param userId
     * @param transportNo
     * @param payStatus:支付状态0待支付1支付失败2支付成功
     * @return
     * @throws Exception
     */
    Long getOrderId(Long carOwnerUserId, String transportNo, String payStatus) throws Exception;

    /**
     * 获取车主的订单ID
     *
     * @param carOwnerUserId
     * @param transportNo
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @return
     * @throws Exception
     */
    List<Long> getCarOwnerOrderId(Long carOwnerUserId, String transportNo, String robStatus) throws Exception;


    /**
     * 根据车主ID，运单号查询记录集合
     * @param userId
     * @param transportNo
     * @param payStatus:支付状态0待支付1支付失败2支付成功
     * @return
     * @throws Exception
     */
//	  public List<String> getOrderId(Long[] carOwnerUserId, String transportNo,String payStatus)throws Exception;

    /**
     * 获得订单信息
     *
     * @param tsOrderNo
     * @param robStatus
     * @param payUserId
     * @return
     */
    TytTransportOrders getTytTransportOrders(String tsOrderNo, String robStatus, Long payUserId);

    /**
     * 撤销、无效货源时将待支付的设置为不显示
     *
     * @param userId
     * @param transportOrderNo
     * @throws Exception
     */
    void saveChangeWaitToDisappear(Long userId, String transportOrderNo) throws Exception;


    /**
     * 撤销、无效货源时将待支付状态设置为 6待支付关闭
     *
     * @param userId
     * @param transportOrderNo
     * @throws Exception
     */
    void saveChangeWaitToDisappearNew(Long userId, String transportOrderNo) throws Exception;

    /**
     * 获取用户下近N天所有已成交数据的tsId
     *
     * @param userId             用户Id
     * @param day                指定的天数
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @return
     */
    List<Long> getTsIdsByRobStatus(Long userId, Integer day, String... robStatus);

    TytTransportOrders getTytOfflineTransportOrders(String tsOrderNo, Long payUserId);

    TytTransportOrders getTransportOrders(String tsOrderNo, Long payUserId);

    List<TytTransportOrders> getTransportOrdersList(String tsOrderNo,String deParty, Long payUserId);

    /**
     * 信息费操作
     * @param orders
     * @param operateType
     * @throws Exception
     */
    ResultMsgBean updateOrderCostStatus(TytTransportOrders orders, Integer operateType) throws Exception;

    /**
     * 退还信息费申请
     * @param orders
     * @param refundAmount
     * @throws Exception
     */
    void updateOrderForGiveBack(TytTransportOrders orders, Long refundAmount,String refundReason) throws Exception;


    /**
     * 后台管理系统异常订单退还
     * @param orders
     * @param giveBackBean
     * @throws Exception
     */
    void updateExInfoFeeOrderForGiveBack(TytTransportOrders orders,ExInfoFeeGiveBackBean giveBackBean) throws Exception;


    /**
     * 车方订金退还申请/货方退回订金 接口
     * @param orders
     * @param refundType  接口类型 1 车方主动申请  2 货方退回
     * @throws Exception
     */
    void updateOrderForInfoFeeGiveBack(TytTransportOrders orders ,Integer refundType, String refundReason) throws Exception;

    /**
     * @Description  获取车主信息费列表接口
     * <AUTHOR>
     * @Date  2018/12/24 10:51
     * @Param [userId, queryActionType, queryID]
     * @return com.tyt.infofee.bean.CarOwnerInfoFeeBean
     **/
    CarOwnerInfoFeeBean getCarOwnerInfofeeList(Long userId, int queryActionType, long queryID, String clientVersion, Integer queryType,String clientSign) throws Exception;


    /**
     * @Description  获取货主信息费列表接口
     * <AUTHOR>
     * @Date  2018/12/24 15:41
     * @Param [userId, queryActionType, queryID]
     * @return com.tyt.infofee.bean.ShipperInfoFeeBean
     **/
    ShipperInfoFeeBean getShipperInfofeeList(Long userId, int queryActionType, long queryID,int costStatus) throws Exception;

    /**
     * 根据货源id查询查询订单数量
     * @param srcMsgId
     * @return
     */
    Integer getCountBySrcMsgId(Long srcMsgId);

    /**
     * 查询发布中有效订单数量（包括未支付的）
     * @param srcMsgId
     * @return
     */
    Long checkUsefulOrderExist(Long srcMsgId);

    /**
     * 获取发布中货源订单数量
     * @param srcMsgIds
     * @return
     */
    List<TransportOrderCountVo> getTsOrderCountList(Collection<Long> srcMsgIds);

    /**
     * get order count.
     * @param srcMsgId
     * @return
     */
    int getTsOrderCount(Long srcMsgId);

    List<TytTransportOrders> getOtherPaidOrders(Long userId, String tsOrderNo, Long[] notInPayUserIds);

    TytTransportOrders getNewTransportOrders(String tsOrderNo, Long payUserId, InfofeeStatusEnum infofeeStatusEnum);

    Integer getCountOrdersbyTsOrderNo(String tsOrderNo, Long payUserId);

    List<RecentCallListBean> getInfofeeCell(Long tsId);

    /**
     * 获取车辆定位信息
     * @param carId
     * @return
     */
    ResultMsgBean getCarPosition(Long carId);

    int unpaidCancel(Long userId, Long orderId);

    /**
     * 获取 交易中 和冻结中的 气泡数
     * @param userId
     * <AUTHOR>
     * @date 2021/4/7
     * @return List
     */
    List<InfoFeeMyPublishBubbleResultBean> getTradingAndFreezeBubble(Long userId);

    /**
     * 查询待评价数量
     *
     * @param userId   用户ID
     * @param userType 用户类型
     * @return
     */
    Long getUserFeedbackTodoCount(Long userId, int userType);

    /**
     * 修改是否成交车
     * @param id
     * @param status
     * @return
     */
    ResultMsgBean updateDealCar(Long id,Integer status,Long tsId);

    /**
     * 获取限时货源联系电话
     * @param tsId
     * @return
     */
    ResultMsgBean getBackendTransportPhones(Long tsId);


    /**
     * 查询指定范围内支付并且完成而且金额大于某个单位的订单
     * @return
     */
    int getDateRangFinishAndPayAmountOrderTotal(Long userId, Date payDate, Date endDate, Long payAmount);

    /**
     * 获得用户处于未完结状态的订单信息数量
     * @param userId
     * @return
     */
    long getUnfinishedOrders(Long userId);

    /**
     * 根据用户id获取处于未完结状态的订单总金额(即冻结金额)
     * @param userId
     * @return
     */
    BigDecimal getTotalFrozenAmountByUserId(String userId);

    /**
     * 通过 main 表 id 和用户 id 判断当前用户有没有支付过这个货源
     * @param srcMsgId
     * @param userId
     * @return
     */
    TytTransportOrders userIsPay(Long srcMsgId, Long userId);

    /**
     * 根据货源id 查询支付成功的信息
     * @param tsId
     * @return
     */
    TytTransportOrders getByTsId(Long tsId);

    /**
     * 修改订单中的车辆信息
     * @param tytTransportOrders
     * @return
     */
    int updateCarInfo(TytTransportOrders tytTransportOrders);

    /**
     * 根据货源id 查询支付成功的信息 取最早的一条
     * @param tsId
     * @return
     */
    TytTransportOrders getByTsIdByTime(Long tsId);

    /**
     * 获取某用户 一定时间段内已完成订单数
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    Long getCompletedOrderNum(Long userId,Date startTime,Date endTime);

    /**
     * 根据满帮货物Id查询对应的特运通货源Id
     * @param cargoId
     * @return
     */
    Long getSrcMsgIdByCargoId(Long cargoId);

    /**
     * 根据特运通货物Id查询对应的货源被支付成功的个数
     * @param srcMsgId
     * @return
     */
    int getGoodsPaySuccessCount(Long srcMsgId);

    /**
     * 根据第三方平台传递过来的单号获取TytTransportOrders实体类
     * @param thirdPartyPlatformOrderNo
     * @return
     */
    TytTransportOrders getByThirdPartyPlatformOrderNo(String thirdPartyPlatformOrderNo);

    /**
     * 根据满帮发过来的确认给车方成功的消息 更新tyt_transport_orders表记录
     * @param thirdPartyPlatformOrderNo
     * @return
     * @throws Exception
     */
    int updateOrderForConfirmSuccess(String thirdPartyPlatformOrderNo) throws Exception;

    /**
     * 根据TytTransportOrders实体类 添加tyt_old_order表记录
     * @param orders 订单表实体类
     */
    void addOldOrderLog(TytTransportOrders orders);

    /**
     * 0元指派单添加tyt_old_Order表记录
     * <AUTHOR>
     * @param orders
     * @return void
     */
    void addZeroAssignOrderLog(TytTransportOrders orders);

    /**
     * 根据满帮发过来的退款成功的消息 更新tyt_transport_orders表记录
     * @param thirdPartyPlatformOrderNo 三方平台订单号
     * @param refundAmount  退款金额
     * @param refundReason  退款原因
     * @param refundArrivalTime 退款实际到账日期
     * @return
     * @throws Exception
     */
    int updateOrderForRefundSuccess(String thirdPartyPlatformOrderNo,Long refundAmount, String refundReason, Date refundArrivalTime) throws Exception;

    /**
     * 根据满帮发过来的退款成功的消息 添加tyt_refund表记录
     * @param tranSportOrders TytTransportOrders实体类
     * @param refundAmount 退款金额
     * @param refundArrivalTime 退款实际到账日期
     * @param tradeId  交易记录表(tyt_trade_info)Id
     * @throws Exception
     */
    void addRefundForRefundSuccess(TytTransportOrders tranSportOrders,Long refundAmount, Date refundArrivalTime,Integer tradeId) throws Exception;

    /**
     *  通过三方平台单号查询运单信息
     * @param thirdpartyNo
     * @return
     */
    TytTransportOrders getTransportByThirdpartyNo(String thirdpartyNo);

    int checkDepositReadOnly(Long srcMsgId);

    List<TransportOrders> selectOrdersByActivityIdAndUserId(MarketingActivity activity, Long userId);

    Integer getCountNum(Long activityId, Long userId);

    /**
     * @description 获取投诉订单列表
     * <AUTHOR>
     * @date 2022/12/14 13:58
     * @param userId
     * @param queryActionType
     * @param queryID
     * @param clientVersion
     * @param queryType
     * @param clientSign
     * @return java.util.List<com.tyt.model.TransportOrdersListBean>
     */
    List <TransportOrdersListBean> getComplaintOrdersList(Long userId, int queryActionType, long queryID, String clientVersion, Integer queryType, String clientSign) throws Exception;

    /**
     * @description 通过用户id和活动id查询车主透传活动 活动单列表数据
     * @param activityId 活动ID
     * @param userId userId
     * @return List TytTransportOrders
     */
    List<TytTransportOrders> getOrdersByCarAtivityOrder(Long activityId, Long userId);

    /**
     * @description 通过用户id和活动id查询车主透传活动 活动单列表数据 -- 更换新表 -- 2023.04.24
     * @param activityId 活动ID
     * @param userId userId
     * @return List TytTransportOrders
     */
    List<TytTransportOrders> getOrdersByCarAtivityOrderRisk(Long activityId, Long userId,MarketingActivity activity);

    /**
     * 查询未评价的订单
     * @param userId
     * @param payUserId
     * @param beginTime
     * @return
     */
    List<MybatisTytTransportOrders> getUnPostFeedbackOrders(Long userId, Long payUserId);

    /**
     * 根据ID集合查询运单列表
     *
     * @param idList 运单ID集合
     * @return
     */
    List<MybatisTytTransportOrders> getOrdersByIdList(List<Long> idList);

    /**
     * 撤销、无效货源时将其他人待支付单取消
     * 待支付状态设置为 6待支付关闭
     *
     * @param userId
     * @param tsOrderNo
     */
    void saveOtherUserChangeWaitToDisappearNew(Long userId, String tsOrderNo);
}
