package com.tyt.infofee.service;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.*;
import com.tyt.model.CardPrefixBankInfo;
import com.tyt.model.TytTransportOrders;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2016-11-19上午10:56:13
 * @description
 */
public interface CardPrefixBankInfoService extends BaseService<CardPrefixBankInfo, Long> {
	List<CardPrefixBankInfo> getAllCardPrefixBankInfo();

	String addRemoteAliBankName(String cardId);

	public String getCacheStringByInit(String queryKey);
}
