package com.tyt.infofee.service;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.*;
import com.tyt.model.*;
import com.tyt.plat.entity.base.TytMbCargoSyncInfo;
import com.tyt.plat.entity.base.TytTransportOrderSnapshot;
import com.tyt.plat.vo.user.InvoiceEnterpriseInfoVO;
import com.tyt.user.bean.CarSaveBean;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

public interface InfoFeeBusinessService extends BaseService<TytTransportWaybill, Long> {

	/**
	 * 保存货源订单快照表
	 * @param originalTransport
	 * @param tytTransportOrders
	 * @return
	 */
	TytTransportOrderSnapshot saveTransportOrderSnapshot(TransportMain originalTransport, TytTransportOrders tytTransportOrders, TytMbCargoSyncInfo tytMbCargoSyncInfo, InvoiceEnterpriseInfoVO invoiceEnterpriseInfoVO,String projectName);

	/**
	 * 下单：详情页面的支付运前信息费
	 * @param originalTransport:原货物transport实体
	 * @param carOwnerTelephone:联系电话
	 * @param timeLimitIdentification:是否限时货源 1是 0否
	 * @param carriageFee:运费
	 * @param agencyMoney:运前信息费
	 * @param payUser:支付者User实体
	 * @param infoFeeCarRequest:信息费车辆请求参数
	 * @param couponId 优惠券ID
	 * @return
	 * @throws Exception
	 */
	ResultMsgBean savePayOrderBusiness(Integer zeroAssignOrder,Long goodsId, String carOwnerTelephone, Integer carriageFee,
									   Long agencyMoney, User payUser, ResultMsgBean resultMsgBean, Long srcMsgId, CarSaveBean infoFeeCarRequest, Integer couponId, Long tecServiceFee,Integer driverId,String clientVersionStr) throws Exception;
    /**
     * 拒绝装货/同意装货
     * @param long1
     * @param userId:当前操作用户的ID
     * @param payUserId:将被拒绝的所有用户ID集合
     * @param tsOrderNo:运单号
     * @param operateType:操作类型 1拒绝装货 2同意装货
     * @throws Exception
     */
	MqMoneyRefundMsg saveChangeOrderRefuseOrAgree(Long userId, Long[] payUserId,
												  String tsOrderNo, Integer operateType, Long srcMsgId) throws Exception;

	/**
	 * 车方取消装货
	 * @param userId:当前操作用户的ID，取消的支付的用户ID
	 * @param tsOrderNo:运单号
	 * @param operateType:操作类型 1取消装货
	 * @throws Exception
	 */
	MqMoneyRefundMsg saveChangeOrderCancelForCar(Long userId,
												 String tsOrderNo, Integer operateType, Long srcMsgId) throws Exception;

	/**
	 * 信息费版-获取详情
	 * @param goodsId:货物ID
	 * @param userId:用户ID
	 * @param detailType:1找货列表/收藏列表的详情 2我的货源列表的详情 3已接单列表的详情
	 * @return
	 * @throws Exception
	 */
	GoodsSingleDetailResultBean getGoodsInfoDetail(Long goodsId,
												   Long userId, Integer detailType) throws Exception;

	/**
	 * 获取货物详情
	 * @param userId
	 * @param transportMain
	 * @return
	 */
	SingleGoodsDetailBean getSingleGoodsDetailBean(Long userId,TransportMain transportMain) throws InvocationTargetException, IllegalAccessException;
	/**
	 * 调用退款接口
	 * @param transportNo
	 * @param payUserId
	 * @throws Exception
	 */
	MqMoneyRefundMsg saveToRefund(List <GoodsDetailOrderBean> refuseOrderBeans) throws Exception;

	/**
	 * 调用车方取消退款接口
	 * @param refuseOrderBeans
	 * @throws Exception
	 */
	MqMoneyRefundMsg saveToCancel(List <GoodsDetailOrderBean> refuseOrderBeans) throws Exception;

	void saveMqMessage(Long goodsId, User payUser, MqTytCarIntentionMsg carIntentionMsg) throws Exception;

    void saveChangeOrderRefuseOrAgreeWithNewInfofee(Long userId, Long[] payUserId, String transportNo, Integer operateType, Long srcMsgId) throws Exception;

	/**
	 * 获取货物用户基础信息
	 * @param userId
	 * @param detailUserId
	 * @param srcMsgId
	 * @return
	 */
	TransportUserBean getUserBeanDetail(Long userId, Long detailUserId,Long srcMsgId) throws Exception;
}
