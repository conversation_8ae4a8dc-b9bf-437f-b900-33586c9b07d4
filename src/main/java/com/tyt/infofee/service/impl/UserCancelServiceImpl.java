package com.tyt.infofee.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.bcar.service.BcarJobService;
import com.tyt.bcar.service.BcarRecruitService;
import com.tyt.infofee.bean.UserCancelBean;
import com.tyt.infofee.bean.UserCancelNoticeBean;
import com.tyt.infofee.service.UserCancelService;
import com.tyt.maintainer.service.MaintainerService;
import com.tyt.model.*;
import com.tyt.plat.biz.invoice.serivce.IInvoiceDriverService;
import com.tyt.plat.enterprise.service.EnterpriseService;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.enums.EnterpriseVerifyNodeStatusEnum;
import com.tyt.plat.enums.PlatResponseEnum;
import com.tyt.plat.mapper.base.TytInvoiceEnterpriseMapper;
import com.tyt.plat.mapper.base.TytInvoiceEnterpriseSignMapper;
import com.tyt.plat.service.user.ApiInvoiceEnterpriseService;
import com.tyt.plat.vo.user.EnterpriseModifyCheckVo;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.usedCarSale.service.TytUsedCarSaleService;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.service.*;
import com.tyt.util.TimeUtil;
import com.tyt.util.UserTicketUtil;
import com.tyt.wxuser.service.TytCarWxUserInfoService;
import com.tyt.wxuser.service.TytWxUserInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service(value = "userCancelService")
public class UserCancelServiceImpl extends BaseServiceImpl<TytUserCancel, Long> implements UserCancelService {
	public Logger logger = LoggerFactory.getLogger("UserCancelServiceImpl");

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "tytUserIdentityAuthService")
	private TytUserIdentityAuthService tytUserIdentityAuthService;

	@Resource(name = "tytUserSubService")
	private TytUserSubService tytUserSubService;

	@Resource(name = "tytUsedCarSaleService")
	private TytUsedCarSaleService usedCarSaleService;

	@Resource(name = "bCarJobService")
	BcarJobService bCarJobService;

	@Resource(name = "bCarRecruitService")
	BcarRecruitService bCarRecruitService;

	@Resource(name = "maintainerService")
	private MaintainerService maintainerService;

    @Resource(name = "tytWxUserInfoService")
    private TytWxUserInfoService wxUserInfoService;
    @Resource(name = "tytCarWxUserInfoService")
    private TytCarWxUserInfoService carWxUserInfoService;

	@Autowired
	private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

	@Autowired
	private ApiInvoiceEnterpriseService apiInvoiceEnterpriseService;
	@Autowired
	private TytInvoiceEnterpriseSignMapper tytInvoiceEnterpriseSignMapper;

	@Autowired
	private EnterpriseService enterpriseService;

	@Autowired
	private IInvoiceDriverService iInvoiceDriverService;

	@Autowired
	private CarService carService;

	@Autowired
	private BlacklistUserService blacklistUserService;

	// 货主小程序认证key
	private final static String WX_SESSION_KEY = "wx:session:key:";
	public static final String WX_APPLET_TICKET_KEY = "login:cargo:applet:";
    // 车主小程序认证key
	public final static String TRUCKER_WX_SESSION_KEY = "wx:session:trucker:key:";
	public static final String TRUCKER_WX_APPLET_TICKET_KEY = "login:cargo:trucker:applet:";

	@Override
	@Resource(name = "userCancelDao")
	public void setBaseDao(BaseDao<TytUserCancel, Long> userCancelDao) {
		super.setBaseDao(userCancelDao);
	}

	/**
	 * @description 将用户账号注销信息保存到数据库
	 * <AUTHOR>
	 * @date 2021/12/22 16:48
	 * @param userCancelBean
	 * @param user
	 * @param cellPhone
	 * @return void
	 */
	@Override
	public void insertUserCancel(UserCancelBean userCancelBean, User user, String cellPhone) {
		//用户账号注销实体类
		TytUserCancel tytUserCancel = new TytUserCancel();
		tytUserCancel.setUserId(userCancelBean.getUserId());
		tytUserCancel.setUserName(user.getUserName());
		tytUserCancel.setTrueName(user.getTrueName());
		tytUserCancel.setCellPhone(cellPhone);
		tytUserCancel.setStatus(2);
		tytUserCancel.setReason(userCancelBean.getReason());
		tytUserCancel.setReasonDetail(userCancelBean.getReasonDetail());
		tytUserCancel.setRemark("");
		tytUserCancel.setCtime(TimeUtil.getTimeStamp());
		tytUserCancel.setMtime(TimeUtil.getTimeStamp());
		tytUserCancel.setClientSign(userCancelBean.getClientSign());
		//插入用户账号注销表
		this.getBaseDao().insertSave(tytUserCancel);
	}

	private void checkEnterpriseModifyAllow(Long userId){
		EnterpriseModifyCheckVo modifyCheckVo = apiInvoiceEnterpriseService.checkModifyAllow(userId);
		if(modifyCheckVo != null){
			Boolean orderExist = modifyCheckVo.getOrderExist();
			Boolean transportExist = modifyCheckVo.getTransportExist();

			if(BooleanUtils.isTrue(orderExist)){
				throw TytException.createException(PlatResponseEnum.ORDER_EXIST.info());
			}

			if(BooleanUtils.isTrue(transportExist)){
				throw TytException.createException(PlatResponseEnum.ORDER_EXIST.info());
			}
		}
	}

	/**
	 * @description 修改用户相关信息并退出登录
	 * <AUTHOR>
	 * @date 2021/12/27 10:39
	 * @param userCancelBean
	 * @param userId
	 * @param user
	 * @param cellPhone
	 * @return void
	 */
	@Override
	public void updateUserRelInfoAndLogOut(UserCancelBean userCancelBean, Long userId, User user, String cellPhone) throws Exception {

		TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(userId);

		if(tytInvoiceEnterprise != null) {
			Integer infoVerifyStatus = tytInvoiceEnterprise.getInfoVerifyStatus();

			if(EnterpriseVerifyNodeStatusEnum.VERIFING.equalsCode(infoVerifyStatus)
					|| EnterpriseVerifyNodeStatusEnum.VERIFIED.equalsCode(infoVerifyStatus)){
				this.checkEnterpriseModifyAllow(userId);
				enterpriseService.destroyEnterpriseVerify(tytInvoiceEnterprise);
			}
		}

		//1.修改用户的手机号
		String suffix = "(已注销)";
		//1.1查询用户存在身份认证信息，则将手机号尾部添加"-已注销"字样
		TytUserIdentityAuth userIdentityAuth = tytUserIdentityAuthService.getTytUserIdentityAuth(userId);
		if (userIdentityAuth != null) {
			String mobile = userIdentityAuth.getMobile();
			userIdentityAuth.setMobile(mobile + suffix);
			tytUserIdentityAuthService.update(userIdentityAuth);
		}
		//1.2查询用户子表信息是否存在，将用户极光推送ID信息清空
		// 即：tyt_user_sub表内car_device_id=NULL，goods_device_id=NULL；
		TytUserSub userSub = tytUserSubService.getTytUserSubByUserId(userId);
		if (userSub != null) {
			userSub.setCarDeviceId(null);
			userSub.setGoodsDeviceId(null);
			tytUserSubService.update(userSub);
		}
		//1.3将用户注册时必要验证信息表手机号，删除该手机号记录；
		userService.deleteFromTytCellphone(cellPhone);
		//1.4用户信息表有效标识更改为无效状态，即：tyt_user内 is_enabled=0，
		// 同时手机号尾部添加"-已注销"字样,即cell_phone字段；
		user.setIsEnabled("0");
		user.setCellPhone(cellPhone + suffix);
        user.setMtime(new Timestamp(System.currentTimeMillis()));
        userService.update(user);

		//2.如果有用户有发布过二手车、求职招聘、维修师相关信息，则下架相关的发布信息
		//2.1更新二手车发布状态
		usedCarSaleService.updateCarStatusByUserId(userId, 2);
		//2.2更新求职信息显示状态
		bCarJobService.updateJobStatusByUserId(userId,1);
		//2.3更新招聘信息显示状态
		bCarRecruitService.updateRecruitStatusByUserId(userId,1);
		//2.4更新维修师显示状态
		maintainerService.updateStatusByUserId(userId, 0);

		//3.记录用户账号注销信息
		this.insertUserCancel(userCancelBean, user, cellPhone);
		//4.将用户踢出登录
		UserTicketUtil.kickOutAllClient(userId + "");
		//5.清理用户的车辆关系、司机关系
		delDriverAndCar(userId, cellPhone);
		// 用户拉黑表手机号追加已注销
		updateBlackUserCellPhone(cellPhone);
		// 判断用户是否是小程序用户
        TytWxUserInfo wxUserInfo = wxUserInfoService.getTytWxUserInfoByUserId(userId);
        if (wxUserInfo!=null){
            // 1.将用户appId和账号设置为已注销
            String openId = wxUserInfo.getOpenId() + suffix + "_" + userId;
            String phone = wxUserInfo.getCellPhone().concat(suffix);
            wxUserInfoService.updateWxUserPhoneByUserId(openId,phone,userId);
            // 2.清除微信小程序登录缓存
            RedisUtil.del(WX_SESSION_KEY+wxUserInfo.getOpenId());
            RedisUtil.del(WX_APPLET_TICKET_KEY+userId);
        }

		// 判断用户是否是车方小程序用户
		TytCarWxUserInfo carWxUserInfo = carWxUserInfoService.getTytCarWxUserInfoByUserId(userId);
		if (carWxUserInfo != null) {
			// 1.将用户appId和账号设置为已注销
			String openId = carWxUserInfo.getOpenId() + suffix + "_" + userId;
			String phone = carWxUserInfo.getCellPhone().concat(suffix);
			carWxUserInfoService.updateWxUserPhoneByUserId(openId, phone, userId);
			// 2.清除微信小程序登录缓存
			RedisUtil.del(TRUCKER_WX_SESSION_KEY + carWxUserInfo.getOpenId());
			RedisUtil.del(TRUCKER_WX_APPLET_TICKET_KEY + userId);
		}
	}

	private void updateBlackUserCellPhone(String cellPhone) {
		try {
			blacklistUserService.updateCellPhoneCancel(cellPhone);
		} catch (Exception e) {
			logger.error("update black user cell phone cancel error", e);
		}
	}

	private void delDriverAndCar(Long userId, String cellPhone) {
		try {
			iInvoiceDriverService.updateDelStatusByCellPhone(cellPhone);
			List<QueryCar> authCarList = carService.getAllCarByUserId(userId);
			if (authCarList != null && !authCarList.isEmpty()) {
				for (QueryCar authCar : authCarList) {
					carService.deleteCar(authCar.getId().longValue(), "注销");
				}
			}
		} catch (Exception e) {
			logger.error("delete driver and car error", e);
		}
	}

	/**
	 * @description 根据手机号查询用户注销记录是否存在
	 * <AUTHOR>
	 * @date 2022/2/9 18:37
	 * @param cellPhone 手机号
	 * @return boolean
	 */
	@Override
	public boolean isUserCancelInfoExist(String cellPhone) {
        String sql = "select * from tyt_user_cancel where cell_phone = ? and status = 2";
		List <TytUserCancel> userCancelList = this.getBaseDao().queryForList(sql, new Object[]{cellPhone});
		if(userCancelList != null && userCancelList.size() > 0){
			return true;
		}
		return false;
	}

	@Override
	public Integer getCellPhone(String cellPhone) {
		BigInteger count = this.getBaseDao().query(
				"select count(*) from tyt_user_cancel where cell_phone = ? and status = 2",
				new Object[]{cellPhone});
		return count.intValue();
	}

    @Override
    public UserCancelNoticeBean getCountByCellPhoneAndGoodsPort(String cellPhone) {
		String sql = "SELECT cell_phone cellPhone, COUNT(*) count,MAX(mtime) maxDate FROM `tyt_user_cancel` WHERE cell_phone = ? and status = 2 and client_sign in(22,32)";
        //传入的参数
        Object[] params = new Object[] {cellPhone};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("cellPhone", Hibernate.STRING);
        scalarMap.put("count", Hibernate.INTEGER);
        scalarMap.put("maxDate", Hibernate.TIMESTAMP);
        List<UserCancelNoticeBean> noticeBeans =  this.getBaseDao().search(sql, scalarMap, UserCancelNoticeBean.class, params);
        if (CollectionUtils.isNotEmpty(noticeBeans)){
            if (noticeBeans.get(0).getCount()>0){
                return noticeBeans.get(0);
            }
        }
        return null;
    }


}
