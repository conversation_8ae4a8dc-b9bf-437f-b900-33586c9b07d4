package com.tyt.infofee.service.impl;

import com.gexin.fastjson.JSON;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.ExPartyEnum;
import com.tyt.infofee.enums.InfofeeStatusEnum;
import com.tyt.infofee.enums.RefundFlagEnum;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.*;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.mybatis.mapper.InfofeeDetailMapper;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.commons.model.CheckResult;
import com.tyt.user.querybean.QueryCar;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytOwnerAuthService;
import com.tyt.util.CalYearsUtil;
import com.tyt.util.Constant;
import com.tyt.util.StringUtil;
import com.tyt.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class InfofeeDetailServiceImpl implements IInfofeeDetailService {
    private static final Logger logger = LoggerFactory.getLogger(InfofeeDetailServiceImpl.class);
    public static final String COMMA = ",";
    public static final String LEFT_BRACE = "(";
    public static final String RIGHT_BRACE = ")";
    public static final String OLD_PAID_STATUS = "1";
    public static final String YUAN = "元";
    public static final String USER_TYPE_ONE = "1";
    public static final String USER_TYPE_ZERO = "0";
    public static final int ZERO = 0;


    @Autowired
    private InfofeeDetailMapper infofeeDetailMapper;
    @Autowired
    private BackendTransportMapper backendTransportMapper;
    @Resource
    private UserPermissionService userPermissionService;
    @Resource
    private TransportOrdersService transportOrdersService;
    @Resource
    private CarService carService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;
    @Resource(name = "tytOwnerAuthService")
    private TytOwnerAuthService tytOwnerAuthService;

    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Autowired
    private CarDetailTailService carDetailTailService;

    @Autowired
    IFeedbackUserService feedbackUserService;

    @Autowired
    TransportWayBillExService transportWayBillExService;


    @Override
    public InfoFeeDetail ownerInfofeeDetail(String tsOrderNo, String ownerId, boolean isPc, String clientSign) {
        InfoFeeDetail infoFeeDetail = new InfoFeeDetail();
        //运单基本信息
        Long srcMsgId = infofeeDetailMapper.querySrcMsgId(tsOrderNo);
        if (srcMsgId == null) {
            logger.info("没有查询到货源srcMsgId信息! {} {}", tsOrderNo, ownerId);
            return infoFeeDetail;
        }
//        BaseTransInfo baseTransInfo = infofeeDetailMapper.queryBaseTransInfo(srcMsgId);
//        if (baseTransInfo == null) {
//            //从历史表中查询
//            baseTransInfo = infofeeDetailMapper.queryBaseTransInfoFromMain(srcMsgId);
//        }

        BaseTransInfo baseTransInfo = infofeeDetailMapper.baseTransInfoForSnapshot(srcMsgId,tsOrderNo);
        if (baseTransInfo == null) {
            logger.info("没有查询到货源信息! {} {}", tsOrderNo, ownerId);
            return infoFeeDetail;
        }
        String newName = StringUtil.hidePhoneInStr(baseTransInfo.getOwnerName());
        baseTransInfo.setOwnerName(newName);
        // 官方授权账号
//        String authNameTea = tytOwnerAuthService.getAuthNameTea(baseTransInfo.getAuthName());
//        if(StringUtils.isNotBlank(authNameTea)){
//            baseTransInfo.setAuthNameTea(authNameTea);
//            baseTransInfo.setAuthName(null);
//        }
        if (StringUtils.isNotEmpty(baseTransInfo.getSignupTime())) {
            baseTransInfo.setSignupYears(CalYearsUtil.calculatePeriod(baseTransInfo.getSignupTime(), LocalDate.now()));
        }
        //会员有效期 是会员,并且有效期大于0天
        if (StringUtils.isNotEmpty(baseTransInfo.getUserType()) && StringUtils.isNotEmpty(baseTransInfo.getServeDays())) {
            if (USER_TYPE_ONE.equals(baseTransInfo.getUserType()) && Integer.parseInt(baseTransInfo.getServeDays()) > ZERO) {
                baseTransInfo.setUserType(USER_TYPE_ONE);
            } else {
                baseTransInfo.setUserType(USER_TYPE_ZERO);
            }
        } else {
            baseTransInfo.setUserType(USER_TYPE_ZERO);
        }
        // 获取货主的会员信息
        UserPermission userPermission = userPermissionService.getUserPermission(Long.valueOf(baseTransInfo.getOwnerId()), UserPermission.PermissionTypeEnum.GOODS_MEMBER.getCode());
        if(null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus())){
            baseTransInfo.setGoodsPermissionMember(USER_TYPE_ONE);
        }else {
            baseTransInfo.setGoodsPermissionMember(USER_TYPE_ZERO);
        }

        HashMap<String, String> telMap = backendTransportMapper.selectBackendPhoneByMsgId(Long.valueOf(baseTransInfo.getTsId()));
        if (telMap!=null&&StringUtils.isNotEmpty(telMap.get("tel"))){
            baseTransInfo.setBackendPhone(telMap.get("tel"));
        }


        //信息费列表
        List<InfoFeePayinfo> payinfoList = getInfoFeePayinfos(tsOrderNo, ownerId, "");
        if (!isPc) {
            //不是pc客户端，只查询已支付
            logger.info("isPc {}, 过滤未支付信息费订单", isPc);
            payinfoList = payinfoList.stream().filter(infoFeePayinfo -> !infoFeePayinfo.getCostStatus().equals(InfofeeStatusEnum.待支付.getId()))
                    .collect(Collectors.toList());
        }

//        //是否能评价, 完成订单才有
//        try {
//            CheckResult<String> checkResult = feedbackUserService.checkCanPostFeedBack(baseTransInfo.getOrderId(), Long.valueOf(ownerId), Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign)));
//            baseTransInfo.setCanPostFeedBack(checkResult.isSuccess());
//        } catch (Exception e) {
//            logger.error("订单详情查询是否能评价 异常：", e);
//        }
//        //是否能服务评价
//        if (baseTransInfo.getCostStatus()==50) {
//            baseTransInfo.setEvaluateStatus(transportWayBillExService.isExFeedBack(baseTransInfo.getOrderId(), Long.valueOf(ownerId)));
//        }
        infoFeeDetail.setBaseTransInfo(baseTransInfo);
        infoFeeDetail.setInfoFeePayinfoList(payinfoList);

        return infoFeeDetail;
    }

    private List<InfoFeePayinfo> getInfoFeePayinfos(String tsOrderNo, String ownerId, String userId) {
        List<InfoFeePayinfo> payinfoList = infofeeDetailMapper.queryPayInfoList(tsOrderNo);
        boolean isOwner = StringUtils.isEmpty(userId);
        if (!isOwner) {
            //车主
            payinfoList = payinfoList.stream().
                    filter(infoFeePayinfo -> userId.equals(infoFeePayinfo.getPayUserId())&&infoFeePayinfo.getCarShow()!=1)
                    .collect(Collectors.toList());
        }else{
            //货主
            payinfoList = payinfoList.stream().
                    filter(infoFeePayinfo -> infoFeePayinfo.getGoodsShow()!=1)
                    .collect(Collectors.toList());
        }

        if (!payinfoList.isEmpty()) {

            //构造carId-挂车样式map数据，方面后面直接获取
            List<Long> carIds = payinfoList.stream().map(InfoFeePayinfo::getCarId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, Integer> carIdIsPureFlatMap = makeCarDetailTailCarIdIsPureFlatMap(carIds);

            // 当车辆 id 为空，但是车辆信息不为空时，获取到车辆的 id 并 set
            payinfoList.forEach(payInfo ->{
                if(null != payInfo && StringUtils.isNotBlank(payInfo.getHeadCity()) && StringUtils.isNotBlank(payInfo.getHeadNo()) && StringUtils.isNotBlank(payInfo.getTailCity())
                        && StringUtils.isNotBlank(payInfo.getTailNo()) && null == payInfo.getCarId()){
                    List<QueryCar> cars = carService.getAuthCarInfoByHeadNo(Long.valueOf(payInfo.getPayUserId()), payInfo.getHeadCity(), payInfo.getHeadNo());
                    if(null != cars && !cars.isEmpty()){
                        Long carId = cars.get(0).getId().longValue();
                        payInfo.setCarId(carId);
                        TytTransportOrders tytTransportOrders = new TytTransportOrders();
                        tytTransportOrders.setCarId(carId);
                        tytTransportOrders.setTailCity(payInfo.getTailCity());
                        tytTransportOrders.setTailNo(payInfo.getTailNo());
                        tytTransportOrders.setHeadCity(payInfo.getHeadCity());
                        tytTransportOrders.setHeadNo(payInfo.getHeadNo());
                        tytTransportOrders.setId(Long.parseLong(payInfo.getId()));
                        tytTransportOrders.setCarReplace(transportOrdersService.getById(Long.parseLong(payInfo.getId())).getCarReplace());
                        transportOrdersService.updateCarInfo(tytTransportOrders);
                    }
                }

                //构造车辆是否属于待完善数据
                if (payInfo != null) {
                    if (payInfo.getCarId() != null) {
                        Car car = carService.getById(payInfo.getCarId());
                        car.setIsPureFlat(carIdIsPureFlatMap.get(payInfo.getCarId()));
                        payInfo.setCarAuth(car.getAuth() == null ? null : Integer.valueOf(car.getAuth()));
                        payInfo.setCarIsNeedImprovenData(carService.carIsNeedImprovenData(car));
                    } else {
                        payInfo.setCarIsNeedImprovenData(false);
                    }
                }

            });

            //兼容旧数据支付状态
            payinfoList.stream().filter(infoFeePayinfo -> StringUtils.isEmpty(infoFeePayinfo.getCostStatus()))
                    .forEach(infoFeePayinfo -> {
                        if (OLD_PAID_STATUS.equals(infoFeePayinfo.getOldPaymentStatus())) {
                            infoFeePayinfo.setOldPaymentStatus(InfofeeStatusEnum.已支付.getId());
                        } else {
                            infoFeePayinfo.setOldPaymentStatus(InfofeeStatusEnum.待支付.getId());
                        }
                    });

            //兼容退款时间
            payinfoList.stream().filter(infoFeePayinfo -> InfofeeStatusEnum.退款中.getId().equals(infoFeePayinfo.getCostStatus())
                    || InfofeeStatusEnum.已退款.getId().equals(infoFeePayinfo.getCostStatus()))
                    .forEach(infoFeePayinfo -> {
                        if (StringUtils.isNotEmpty(infoFeePayinfo.getRefundTime())) {
                            infoFeePayinfo.setActionTime(infoFeePayinfo.getRefundTime());
                        }
                    });

            StringBuilder payUserIdsBuilder = new StringBuilder(LEFT_BRACE);
            if (isOwner) {
                //货主
                payinfoList
                        .forEach(infoFeePayinfo -> payUserIdsBuilder.append(infoFeePayinfo.getPayUserId()).append(COMMA));
                payUserIdsBuilder.deleteCharAt(payUserIdsBuilder.length() - 1).append(RIGHT_BRACE);
            } else {
                //车主
                payUserIdsBuilder.append(userId).append(")");
            }

            //信用信息
            List<CreditUserInfo> driverCreditUserInfoList = infofeeDetailMapper.queryCreditUserInfo(payUserIdsBuilder.toString(), ownerId);

            //
            if (driverCreditUserInfoList != null && driverCreditUserInfoList.size() > 0) {
                List<CreditUserInfo> finalDriverCreditUserInfoList = driverCreditUserInfoList;
                payinfoList.stream().forEach(infoFeePayinfo -> {
                    String payUserId = infoFeePayinfo.getPayUserId();
                    Optional<CreditUserInfo> driverUserInfo = finalDriverCreditUserInfoList.stream()
                            .filter(tempUserInfo -> tempUserInfo.getUserId().equalsIgnoreCase(payUserId))
                            .findFirst();
                    if (driverUserInfo.isPresent()) {
                        CreditUserInfo userInfo = driverUserInfo.get();
                        if (StringUtils.isNotEmpty(userInfo.getSignupTime())) {
                            userInfo.setSignupYears(CalYearsUtil.calculatePeriod(userInfo.getSignupTime(), LocalDate.now()));
                        }
                        //会员有效期 是会员,并且有效期大于0天
                        if (StringUtils.isNotEmpty(userInfo.getUserType()) && StringUtils.isNotEmpty(userInfo.getServeDays())) {
                            if (USER_TYPE_ONE.equals(userInfo.getUserType()) && Integer.parseInt(userInfo.getServeDays()) > ZERO) {
                                userInfo.setUserType(USER_TYPE_ONE);
                            } else {
                                userInfo.setUserType(USER_TYPE_ZERO);
                            }
                        } else {
                            userInfo.setUserType(USER_TYPE_ZERO);
                        }

                        UserPermission userPermission = userPermissionService.getUserPermission(Long.valueOf(userInfo.getUserId()), UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode());
                        if(null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus())){
                            userInfo.setCarPermissionMember(USER_TYPE_ONE);
                        }else {
                            userInfo.setCarPermissionMember(USER_TYPE_ZERO);
                        }
                        //如果是满帮平台司机接单，则对车主信息重新赋值
                        //三方平台类型 1:满帮
                        Integer thirdpartyPlatformType = infoFeePayinfo.getThirdpartyPlatformType();

                        logger.info("信息费对象为：{},三方平台类型为：{}", JSON.toJSONString(infoFeePayinfo),thirdpartyPlatformType);
                        if(thirdpartyPlatformType != null && thirdpartyPlatformType.intValue() == 1){
                            //车主昵称
                            userInfo.setUserName(infoFeePayinfo.getPayUserName());
                            //车主手机号
                            userInfo.setTel(infoFeePayinfo.getPayCellPhone());
                            //是否认证 0 否 1 是
                            userInfo.setAuthed(USER_TYPE_ONE);
                            //车方会员状态
                            userInfo.setCarPermissionMember(USER_TYPE_ZERO);
                            //注册时间默认是2年、交易次数36次、合作12次；
                            //注册时间
                            userInfo.setSignupTime(TimeUtil.formatDateTime(TimeUtil.dateAddMonth(new Date(),-25)));
                            //交易次数
                            userInfo.setTradeNums("36");
                            //合作次数
                            userInfo.setCoopNums("12");
                        }

                        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(Long.parseLong(payUserId));
                        if (userCreditInfo != null) {
                            userInfo.setCarCreditRankLevel(userCreditInfo.getCarCreditRankLevel());
                        }
                        infoFeePayinfo.setDriverUserInfo(userInfo);
                    }
                });

                //异常信息
                payinfoList.stream().forEach(infoFeePayinfo -> {
                    //需要先知道上报异常的是车房还是货方(字典表原因)
                    List<String> exPartys = infofeeDetailMapper.queryExParty(infoFeePayinfo.getId(), 0);
                    //定义异常信息集合
                    List<InfoFeeExceptionInfo> infoFeeexceptionInfos = new ArrayList<>();
                    for (String exParty : exPartys) {
                        if (StringUtils.isEmpty(exParty)) {
                            return;
                        }
                        ExPartyEnum exPartyEnum = ExPartyEnum.getExPartyEnumById(Integer.parseInt(exParty));
                        if (exPartyEnum == null) {
                            return;
                        }

                        List<ExceptionInfo> exceptionInfos = infofeeDetailMapper.queryExceptionInfo(infoFeePayinfo.getId(), exPartyEnum.getDicKey(),exPartyEnum.getNewDicKey(),exParty);
                        for (ExceptionInfo exceptionInfo : exceptionInfos) {
                            InfoFeeExceptionInfo infoExceptionInfo = new InfoFeeExceptionInfo();
                            if (exceptionInfo != null) {
//                              infoFeePayinfo.setComment(exceptionInfo.getEsReason());
                                infoExceptionInfo.setExComment(exceptionInfo.getComment());
                                infoExceptionInfo.setExStatus(exceptionInfo.getExStatus());
                                infoExceptionInfo.setActionTime(exceptionInfo.getEsCompleteTime());
                                infoExceptionInfo.setExceptionTime(exceptionInfo.getEsTime());
                                String exResult = composeExResult(exceptionInfo);
                                infoExceptionInfo.setExResult(exResult);
                                infoExceptionInfo.setExResultComment(exceptionInfo.getEsResult());
                                infoExceptionInfo.setCarAmount(exceptionInfo.getCarAmount());
                                infoExceptionInfo.setGoodsAmount(exceptionInfo.getGoodsAmount());
                                infoExceptionInfo.setCarServiceAmount(exceptionInfo.getCarServiceAmount());
                                infoExceptionInfo.setPlatformServiceAmount(exceptionInfo.getPlatformServiceAmount());
                                infoExceptionInfo.setCancelExTime(exceptionInfo.getCancelExTime());
                                //---兼容历史版本 6310之前的版本仍需用到以下字段开始------
                                infoFeePayinfo.setExComment(exceptionInfo.getComment());
                                infoFeePayinfo.setExStatus(exceptionInfo.getExStatus());
                                if (infoFeePayinfo.getCostStatus().equalsIgnoreCase(InfofeeStatusEnum.异常完成.getId())) {
                                    infoFeePayinfo.setActionTime(exceptionInfo.getEsCompleteTime());
                                    infoFeePayinfo.setExceptionTime(exceptionInfo.getEsTime());
                                    infoFeePayinfo.setExResult(exResult);
                                    infoFeePayinfo.setExResultComment(exceptionInfo.getEsResult());
                                    infoFeePayinfo.setCarAmount(exceptionInfo.getCarAmount());
                                    infoFeePayinfo.setGoodsAmount(exceptionInfo.getGoodsAmount());
                                } else {
                                    infoFeePayinfo.setActionTime(exceptionInfo.getEsTime());
                                    infoFeePayinfo.setExceptionTime(exceptionInfo.getEsTime());
                                }
                                //---兼容历史版本 6310之前的版本仍需用到以下字段结束------
                            }
                            infoFeeexceptionInfos.add(infoExceptionInfo);
                        }
                    }
                    Collections.sort(infoFeeexceptionInfos, Comparator.comparing(InfoFeeExceptionInfo::getExceptionTime));
                    infoFeePayinfo.setInfoFeeExceptionInfoList(infoFeeexceptionInfos);
                });

                //对于交易中的状态 新增确认付款时间
                payinfoList.stream()
                        .filter(infoFeePayinfo -> infoFeePayinfo.getCostStatus().equalsIgnoreCase(InfofeeStatusEnum.已支付.getId())
                                || infoFeePayinfo.getCostStatus().equalsIgnoreCase(InfofeeStatusEnum.拒绝退款.getId())
                                )
                        .forEach(infoFeePayinfo -> {
                            //当前日期
                            String today = TimeUtil.formatDateTime(new Date(System.currentTimeMillis()));
                            try {
                                //实际应到账时间
                                Date actualPayDate = infoFeePayinfo.getDePaymentDueDate();
                                //剩余自动结算天数=实际应到账时间-当前日期
                                int tDay = TimeUtil.getDays(today, TimeUtil.formatDateTime(actualPayDate));
                                logger.info("getConfirmPaymentRemark method today:【{}】, actualPayDate: 【{}】, tDay:【{}】",today,actualPayDate,tDay);
                                if (tDay > 1) {
                                    infoFeePayinfo.setConfirmPaymentRemark("还剩" + tDay + "天自动结算");
                                } else {
                                    infoFeePayinfo.setConfirmPaymentRemark("即将自动结算");
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                logger.info("信息费详情获取确认付款时间异常,异常信息{}",e);
                            }
                        });

            }
            payinfoList.stream().forEach(
                    infoFeePayinfo -> {
                        String statusDesc =infoFeeStatusDesc(isOwner,infoFeePayinfo.getRefundFlag(),infoFeePayinfo.getCostStatus(),
                                infoFeePayinfo.getDelayStatus(),infoFeePayinfo.getDelayRefundStatus(),infoFeePayinfo.getDeRefundDueDate());
                        infoFeePayinfo.setStatusDesc(statusDesc);

                        if(null != infoFeePayinfo.getDeRefundDueDate()){
                            //当前日期
                            String today = TimeUtil.formatDateTime(new Date(System.currentTimeMillis()));
                            try {
                                //实际应到账时间
                                Date actualPayDate =infoFeePayinfo.getDeRefundDueDate();
                                //剩余自动结算天数=实际应到账时间-当前日期
                                int tDay = TimeUtil.getDays(today, TimeUtil.formatDateTime(actualPayDate));
                                logger.info("getConfirmPaymentRemark method today:【{}】, actualPayDate: 【{}】, tDay:【{}】",today,actualPayDate,tDay);
                                if (tDay > 1) {
                                    infoFeePayinfo.setConfirmRefundRemark("还剩" + tDay + "天自动退还");
                                } else {
                                    infoFeePayinfo.setConfirmRefundRemark("即将自动退还");
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                logger.info("信息费列表获取确认付款时间异常,异常信息{}",e);
                            }

                        }

                    }
            );
        }
        return payinfoList;
    }

    /**
     * 构造carId-挂车样式map数据，方面后面直接获取
     * @param carIds 车辆ID List
     * @return true：属于待完善车辆；false：不属于
     */
    private Map<Long, Integer> makeCarDetailTailCarIdIsPureFlatMap(List<Long> carIds) {
        Map<Long, Integer> carDetailTailCarIdIsPureFlatMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(carIds)) {
            List<Long> carIdList = carIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (carIdList.isEmpty()) {
                return carDetailTailCarIdIsPureFlatMap;
            }
            List<CarDetailTail> carDetailTails = carDetailTailService.selectCarDetailTailByCarIdList(carIdList);
            if (CollectionUtils.isNotEmpty(carDetailTails)) {
                for (CarDetailTail carDetailTail : carDetailTails) {
                    carDetailTailCarIdIsPureFlatMap.put(carDetailTail.getCarId(), carDetailTail.getIsPureFlat());
                }
            }
        }
        return carDetailTailCarIdIsPureFlatMap;
    }

    private String composeExResult(ExceptionInfo exceptionInfo) {
        StringBuilder exResultBuilder = new StringBuilder();
        int carAmount = 0;
        int goodsAmount = 0;
        int platformServiceAmount=0;
        int carServiceAmount=0;
        if (StringUtils.isNotEmpty(exceptionInfo.getCarAmount())) {
            carAmount = Integer.parseInt(exceptionInfo.getCarAmount()) / 100;
        }
        if (StringUtils.isNotEmpty(exceptionInfo.getGoodsAmount())) {
            goodsAmount = Integer.parseInt(exceptionInfo.getGoodsAmount()) / 100;
        }
        if (StringUtils.isNotEmpty(exceptionInfo.getPlatformServiceAmount())) {
            platformServiceAmount = Integer.parseInt(exceptionInfo.getPlatformServiceAmount()) / 100;
        }
        if (StringUtils.isNotEmpty(exceptionInfo.getCarServiceAmount())) {
            carServiceAmount = Integer.parseInt(exceptionInfo.getCarServiceAmount()) / 100;
        }
        exResultBuilder.append("车方分配:").append(carAmount).append(YUAN).append(COMMA);
        exResultBuilder.append("货方分配:").append(goodsAmount).append(YUAN);
        if (platformServiceAmount > 0 || carServiceAmount > 0) {
            exResultBuilder.append(System.lineSeparator());
            exResultBuilder.append("技术服务费我司分配:").append(platformServiceAmount).append(YUAN).append(COMMA);
            exResultBuilder.append("技术服务费车方分配:").append(carServiceAmount).append(YUAN);
        }
        return exResultBuilder.toString();
    }

    @Override
    public InfoFeeDetail driverInfofeeDetail(String tsOrderNo, String userId, boolean isPc, Long id, String clientSign) {
        InfoFeeDetail infoFeeDetail = new InfoFeeDetail();
        //运单基本信息
        Long srcMsgId = infofeeDetailMapper.querySrcMsgId(tsOrderNo);
        if (srcMsgId == null) {
            logger.info("没有查询到货源srcMsgId信息! {} {}", tsOrderNo, userId);
            return infoFeeDetail;
        }
//        BaseTransInfo baseTransInfo = infofeeDetailMapper.queryBaseTransInfo(srcMsgId);
//        if (baseTransInfo == null) {
//            //从历史表中查询
//            baseTransInfo = infofeeDetailMapper.queryBaseTransInfoFromMain(srcMsgId);
//        }

        BaseTransInfo baseTransInfo = infofeeDetailMapper.baseTransInfoForSnapshot(srcMsgId,tsOrderNo);
        if (baseTransInfo == null) {
            logger.info("没有查询到货源srcMsgId信息! {} {}", tsOrderNo, userId);
            return infoFeeDetail;
        }
        if (StringUtils.isNotEmpty(baseTransInfo.getSignupTime())) {
            baseTransInfo.setSignupYears(CalYearsUtil.calculatePeriod(baseTransInfo.getSignupTime(), LocalDate.now()));
        }
        // 官方授权账号
//        String authNameTea = tytOwnerAuthService.getAuthNameTea(baseTransInfo.getAuthName());
//        if(StringUtils.isNotBlank(authNameTea)){
//            baseTransInfo.setAuthNameTea(authNameTea);
//            baseTransInfo.setAuthName(null);
//        }
        //会员有效期 是会员,并且有效期大于0天
        if (StringUtils.isNotEmpty(baseTransInfo.getUserType()) && StringUtils.isNotEmpty(baseTransInfo.getServeDays())) {
            if (USER_TYPE_ONE.equals(baseTransInfo.getUserType()) && Integer.parseInt(baseTransInfo.getServeDays()) > ZERO) {
                baseTransInfo.setUserType(USER_TYPE_ONE);
            } else {
                baseTransInfo.setUserType(USER_TYPE_ZERO);
            }
        } else {
            baseTransInfo.setUserType(USER_TYPE_ZERO);
        }

        // 获取货主的会员信息
        UserPermission userPermission = userPermissionService.getUserPermission(Long.valueOf(baseTransInfo.getOwnerId()), UserPermission.PermissionTypeEnum.GOODS_MEMBER.getCode());
        if(null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus())){
            baseTransInfo.setGoodsPermissionMember(USER_TYPE_ONE);
        }else {
            baseTransInfo.setGoodsPermissionMember(USER_TYPE_ZERO);
        }
        // 获取订单是否已设为成交车
        if(null != id){
            TytTransportOrders tytTransportOrders = transportOrdersService.getById(id);
            baseTransInfo.setIsDealCar(null != tytTransportOrders && null != tytTransportOrders.getIsDealCar() ? tytTransportOrders.getIsDealCar() : null);
        }

        HashMap<String, String> telMap = backendTransportMapper.selectBackendPhoneByMsgId(Long.valueOf(baseTransInfo.getTsId()));
        if (telMap!=null&&StringUtils.isNotEmpty(telMap.get("tel"))){
            baseTransInfo.setBackendPhone(telMap.get("tel"));
        }
        //信息费列表
        List<InfoFeePayinfo> payinfoList = getInfoFeePayinfos(tsOrderNo, baseTransInfo.getOwnerId(), userId);
        if (!isPc) {
            //不是pc客户端，只查询已支付
            logger.info("isPc {}, 过滤未支付信息费订单", isPc);
            payinfoList = payinfoList.stream().filter(infoFeePayinfo -> !infoFeePayinfo.getCostStatus().equals(InfofeeStatusEnum.待支付.getId()))
                    .collect(Collectors.toList());
        }

        TytTransportOrders transportOrders = infofeeDetailMapper.queryOrderInfoByPayUserIdOrderNo(tsOrderNo, Long.valueOf(userId));

        //是否能评价, 完成订单才有
        try {
            CheckResult<String> checkResult = feedbackUserService.checkCanPostFeedBack(transportOrders.getId(), Long.valueOf(userId), Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign)));
            baseTransInfo.setCanPostFeedBack(checkResult.isSuccess());
        } catch (Exception e) {
            logger.error("订单详情查询是否能评价 异常：", e);
        }
        //是否能服务评价
        if (baseTransInfo.getCostStatus()==50) {
            baseTransInfo.setEvaluateStatus(transportWayBillExService.isExFeedBack(transportOrders.getId(), Long.valueOf(userId)));
        } else {
            baseTransInfo.setEvaluateStatus(1);
        }

        infoFeeDetail.setBaseTransInfo(baseTransInfo);
        infoFeeDetail.setInfoFeePayinfoList(payinfoList);
        return infoFeeDetail;
    }

    @Override
    public Integer getCoopNums(Long userId) {
        Integer coopNums = infofeeDetailMapper.getCoopNums(userId);
        return coopNums;
    }

    @Override
    public List<CreditUserInfo> queryCreditUserInfo(String carUserIds, String userId) {
        List<CreditUserInfo> list = infofeeDetailMapper.queryCreditUserInfo(carUserIds, userId);
        return list;
    }

    @Override
    public Map<Long, CreditUserInfo> queryCreditUserInfoMapForCar(String goodsUserIds, Long carUserId) {
        List<CreditUserInfo> list = infofeeDetailMapper.queryCreditUserInfoForCar(goodsUserIds, carUserId + "");
        Map<Long, CreditUserInfo> map = new HashMap<>();
        for (CreditUserInfo creditUserInfo : list) {
            map.put(Long.parseLong(creditUserInfo.getUserId()), creditUserInfo);
        }
        return map;
    }

    @Override
    public CreditUserInfo getCreditUserInfoForCar(String goodsUserId, String carUserId) {

        List<CreditUserInfo> list = infofeeDetailMapper.queryCreditUserInfoForCar(goodsUserId, carUserId);
        if(CollectionUtils.isNotEmpty(list)){
            CreditUserInfo creditUserInfo = list.get(0);
            creditUserInfo.setUserName(StringUtil.hidePhoneInStr(creditUserInfo.getUserName()));
            return creditUserInfo;
        }
        return null;
    }

    @Override
    public Integer getTradeNums(Long userId) {
        Integer tradeNums = infofeeDetailMapper.getTradeNums(userId);
        return tradeNums;
    }

    @Override
    public TransportInfoFeeBean queryInfofee(String tsOrderNo, String userId, boolean isPc) {
        TransportInfoFeeBean baseTransInfo = new TransportInfoFeeBean();
        //运单基本信息
        Long srcMsgId = infofeeDetailMapper.querySrcMsgId(tsOrderNo);
        if (srcMsgId == null) {
            logger.info("没有查询到货源srcMsgId信息! {} {}", tsOrderNo, userId);
            return baseTransInfo;
        }
        baseTransInfo = infofeeDetailMapper.queryTransInfo(srcMsgId);
        if (baseTransInfo == null) {
            //从历史表中查询
            baseTransInfo = infofeeDetailMapper.queryTransInfoFromMain(srcMsgId);
        }

        if (baseTransInfo == null) {
            logger.info("没有查询到货源srcMsgId信息! {} {}", tsOrderNo, userId);
            return baseTransInfo;
        }

        //信息费列表
        List<InfoFeePayinfo> payinfoList = getInfoFeePayinfos(tsOrderNo, baseTransInfo.getOwnerId(), userId);
        if (!isPc) {
            //不是pc客户端，只查询已支付
            logger.info("isPc {}, 过滤未支付信息费订单", isPc);
            payinfoList = payinfoList.stream().filter(infoFeePayinfo -> !infoFeePayinfo.getCostStatus().equals(InfofeeStatusEnum.待支付.getId()))
                    .collect(Collectors.toList());
        }

        if(!CollectionUtils.isEmpty(payinfoList)){
            InfoFeePayinfo infoFeePayinfo = payinfoList.get(0);
            baseTransInfo.setHeadNo(infoFeePayinfo.getHeadCity()+infoFeePayinfo.getHeadNo());
            baseTransInfo.setTailNo(infoFeePayinfo.getTailCity()+infoFeePayinfo.getTailNo());
        }

        return baseTransInfo;
    }

    public static String infoFeeStatusDesc(boolean isOwner, Integer refundFlag,String costStatus,Integer delayStatus,
                                           Integer delayRefundStatus,Date deRefundDueDate) {
        String today = TimeUtil.formatDateTime(new Date(System.currentTimeMillis()));
        //剩余自动结算天数=实际应到账时间-当前日期
        try {
            int tDay=0;
            if(null != deRefundDueDate){
                tDay = TimeUtil.getDays(today, TimeUtil.formatDateTime(deRefundDueDate));
            }
            if(isOwner && RefundFlagEnum.退还.getId().equals(refundFlag)){
                //货方退还
                switch (costStatus){
                    case "10":
                        return "待支付";
                    case "15":
                        if(delayRefundStatus==0){
                            if(tDay>1){
                                return "车方已支付订金，"+tDay+"天后将自动退还";
                            }else{
                                return "车方已支付订金，即将自动退还";
                            }
                        }else{
                            if(tDay>1){
                                return"订金已延迟退款，"+tDay+"天后将自动退还";
                            }else{
                                return"订金已延迟退款，即将自动退还";
                            }
                        }
                    case "35":
                        return"订金已退还";
//                    case "40":
//                        return"收到订金(可提现)");
//                        continue;
//                    case "45":
//                        return"收到订金(可提现)");
//                        continue;
                    case "30":
                        if(delayRefundStatus==0){
                            if(tDay>1){
                                return"车方申请退还订金，"+tDay+"天后将自动退还";
                            }else{
                                return"车方申请退还订金，即将自动退还";
                            }
                        }else{
                            if(tDay>1){
                                return"订金已延迟退款，"+tDay+"天后将自动退还";
                            }else{
                                return"订金已延迟退款，即将自动退还";
                            }
                        }
//                    case "21":
//                        return"车方拒绝退款，订金延时到账");
//                        continue;
                    case "25":
                        return"已发起异常上报，请联系客服解决";
                    case "50":
                        return"异常上报处理完成";
                    default:
                }
            }else if(isOwner && !RefundFlagEnum.退还.getId().equals(refundFlag)){
                //货方不退还
                switch (costStatus){
                    case "10":
                        return"待支付";
                    case "15":
                        if(delayStatus==1){
                            return"车方已操作延迟付款";
                        }else if (delayStatus==2){
                            return"车方拒绝退款，订金延时到账";
                        }else{
                            return"车方已支付订金";
                        }
                    case "35":
                        return"订金已退还";
                    case "40":
                        return"收到订金(可提现)";
                    case "45":
                        return"收到订金(可提现)";
                    case "30":
                        return"已退款等待车主收款";
                    case "21":
                        return"车方拒绝退款，订金延时到账";
                    case "25":
                        return"已发起异常上报，请联系客服解决";
                    case "50":
                        return"异常上报处理完成";
                    default:
                        return "";
                }
            }else if(!isOwner && RefundFlagEnum.退还.getId().equals(refundFlag)){
                //车方退还
                switch (costStatus){
                    case "10":
                        return"待支付";
                    case "15":
                        if(delayRefundStatus==0){
                            return"订金已支付";
                        }else{
                            return"货方已操作延迟退款";
                        }
                    case "35":
                        return"订金已退还";

//                    case "40":
//                        return"收到订金(可提现)");
//                        continue;
//                    case "45":
//                        return"收到订金(可提现)");
//                        continue;
                    case "30":
                        if(delayRefundStatus==0){
                            return"已申请退还订金，等待货方确认";
                        }else{
                            return"货方已操作延迟退款";
                        }

//                    case "21":
//                        return"车方拒绝退款，订金延时到账");
//                        continue;
                    case "25":
                        return"已发起异常上报，请联系客服解决";
                    case "50":
                        return"异常上报处理完成";
                    default:
                        return "";
                }
            }else{
                //车方不退还
                switch (costStatus){
                    case "10":
                        return"待支付";
                    case "15":
                        if(delayStatus==1){
                            return"订金已延迟付款";
                        }else if (delayStatus==2){
                            return"已拒绝退款，订金已延迟支付";
                        }else{
                            return"订金已支付";
                        }
                    case "35":
                        return"订金已退还";
                    case "40":
                        return"订金付款已确认";
                    case "45":
                        return"订金付款已确认";
                    case "30":
                        return"货方退款等待确认";
                    case "21":
                        return"已拒绝退款，订金已延迟支付";
                    case "25":
                        return"已发起异常上报，请联系客服解决";
                    case "50":
                        return"异常上报处理完成";
                    default:
                        return "";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
