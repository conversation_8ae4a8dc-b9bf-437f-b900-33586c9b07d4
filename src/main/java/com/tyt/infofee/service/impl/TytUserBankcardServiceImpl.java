package com.tyt.infofee.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.tyt.util.BankUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.CardPrefixBankInfoService;
import com.tyt.infofee.service.TytUserBankcardService;
import com.tyt.model.TytUserBankcard;
import com.tyt.util.Constant;
@Service("tytUserBankcardService")
public class TytUserBankcardServiceImpl extends BaseServiceImpl<TytUserBankcard, Long> implements TytUserBankcardService {

	@Resource(name = "cardPrefixBankInfoService")
	private CardPrefixBankInfoService cardPrefixBankInfoService;
	
	@Resource(name = "tytUserBankcardDao")
	public void setBaseDao(BaseDao<TytUserBankcard, Long> tytUserBankcardDao) {
		super.setBaseDao(tytUserBankcardDao);
	}

	@Override
	public List<TytUserBankcard> getByUserId(Long userId) {
		String sql="select * from tyt_user_bankcard where user_id=? and status =?";
		return this.getBaseDao().queryForList(sql, new Object[] {userId,1});
	}

	@Override
	public String saveBankcard(Long userId, String card_num) {
		String sql="select * from tyt_user_bankcard where user_id=? and card_num=? and status=?";
		List<TytUserBankcard> list = this.getBaseDao().queryForList(sql, new Object[] {userId,card_num, 1});
		if(list!=null && list.size()>0) {
			return null;
		}
		String cardIdPrefix = card_num.substring(0,6);
		//根据前缀获取缓存的银行名称
		String bankKey = Constant.CARDID_PREFIX_BANKNAME+cardIdPrefix;
		String bankName = cardPrefixBankInfoService.getCacheStringByInit(bankKey);
//		if(StringUtils.isEmpty(bankName)){
//			//如果不存在则去调用阿里接口，并保存查询结果
//			bankName = cardPrefixBankInfoService.addRemoteAliBankName(card_num);
//		}
		if(StringUtils.isEmpty(bankName)){
			bankName="未知银行";
		}
		String bankIcon = BankUtil.getBankLogo(bankName);
		TytUserBankcard bean=new TytUserBankcard();
		bean.setUserId(userId);
		bean.setCardNum(card_num);
		bean.setBankName(bankName);
		bean.setBankIcon(Constant.CDN_DOMAIN + bankIcon);
		bean.setStatus(1);
		this.getBaseDao().insert(bean);
		return bankName;
	}

	@Override
	public void deleteBankcard(Long bankcardId) {
		String sql ="update tyt_user_bankcard set status=?, utime=? where id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] {3,new Date(),bankcardId});
		
	} 
}
