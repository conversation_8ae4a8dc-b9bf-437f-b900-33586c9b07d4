package com.tyt.infofee.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.FinancialInOutEnum;
import com.tyt.infofee.service.*;
import com.tyt.manbang.bean.response.AccountData;
import com.tyt.model.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import com.tyt.util.tpay.TpayUtil;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("walletService")
public class WalletServiceServiceImpl extends BaseServiceImpl<TytTransportOrders, Long> implements WalletService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "drawMoneyApplyService")
	private DrawMoneyApplyService drawMoneyApplyService;
	@Resource(name = "tytMqMessageService")
	private TytMqMessageService tytMqMessageService;
	@Resource(name = "userService")
	private UserService userService;
	@Resource(name = "tytMessageTmplService")
	private TytMessageTmplService messageTmplService;
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	@Resource(name = "tytUserSubService")
	private TytUserSubService userSubService;
	@Resource(name = "tradeInfoService")
	private TradeInfoService tradeInfoService;
	@Resource(name = "tytUserAccountService")
	private TytUserAccountService accountService;
	@Resource(name = "transportOrdersService")
	private TransportOrdersService transportOrdersService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;


	private static final int AFFILIATED_TYPE_WALLET = 1;
	// 提现申请成功的短信消息键
	private static final String WITHDRAW_APPLY_SUCCESS_MESSAGE_TEMPLATE_KEY = "sms_remind_now";
	private static final int FLOW_TYPE_WITHDRAW = 6;
	public static final String VIRTUAL_USER_ACCOUNT = "***********";
	private static final int FLOW_TYPE_PLAT_OUT = 130;
	private static final int FLOW_TYPE_CMP_ACCOUNT = 140;
	private final String tpayAccountUrl = AppConfig.getProperty("tpay.account.api.url");
	private final String userCenterUrl = AppConfig.getProperty("user.center.api.url");

	public static final String userWeb =AppConfig.getProperty("user.service.api.url");

	private final String MERCHANT_ID = AppConfig.getProperty("tpay.merchantId");
	private final String VERSION = AppConfig.getProperty("tpay.version");

	@Resource(name = "walletDao")
	public void setBaseDao(BaseDao<TytTransportOrders, Long> walletDao) {
		super.setBaseDao(walletDao);
	}

	@SuppressWarnings("deprecation")
	@Override
	public List<PocketBean> getAccountByUserId(String userId) {
		String sql = "SELECT tua.`current_balance` AS 'currentBalance', tua.`affiliated_type` AS 'affiliatedType' FROM tyt_user_account tua WHERE tua.`user_id`=:userId ";
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("userId", userId);
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("currentBalance", Hibernate.LONG);
		scalarMap.put("affiliatedType", Hibernate.INTEGER);
		return this.getBaseDao().search(sql, scalarMap, PocketBean.class, paramMap);
	}

	@SuppressWarnings("deprecation")
	@Override
	public List<UserWalletFlowBean> getUserPocketFlowTopThree(String userId) {
		String sql = "SELECT tff.`money_amount` AS 'money', tff.`waybill_number` AS 'waybillNumber', tff.`orde_number` AS 'orderNumber', tff.`bank_name` AS 'payBankName', tff.`transfter_bank` AS 'payChannelName', tff.`create_time` AS 'time', tff.`flow_type` AS 'type' FROM tyt_financial_flow tff WHERE tff.`user_id`=:userId AND SUBSTR(tff.`money_amount`,2)!='0' ORDER BY tff.`id` DESC LIMIT 6";
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("userId", userId);
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("money", Hibernate.STRING);
		scalarMap.put("orderNumber", Hibernate.STRING);
		scalarMap.put("payBankName", Hibernate.STRING);
		scalarMap.put("waybillNumber", Hibernate.STRING);
		scalarMap.put("payChannelName", Hibernate.STRING);
		scalarMap.put("time", Hibernate.STRING);
		scalarMap.put("type", Hibernate.INTEGER);
		List<UserWalletFlowBean> pocketFlowBeans = this.getBaseDao().search(sql, scalarMap, UserWalletFlowBean.class, paramMap);
		return pocketFlowBeans;
	}

	@SuppressWarnings("deprecation")
	@Override
	public UserWalletFlowListBean getUserPocketFlow(String userId, String currentPage, String year,
													String month, String day, String type,String pageSize) {
		UserWalletFlowListBean resultBean = new UserWalletFlowListBean();
		int searchSize = 30;
		if(!Strings.isEmpty(pageSize)){
			searchSize=Integer.valueOf(pageSize);
		}
		int startPos = (Integer.valueOf(currentPage) - 1) * searchSize;
		StringBuilder count = new StringBuilder("SELECT count(*)  FROM tyt_financial_flow tff where tff.user_id=? AND SUBSTR(tff.`money_amount`,2)!='0' ");
		List<Object> paramsList = new LinkedList<>();
		paramsList.add(userId);
		StringBuilder where = new StringBuilder();
		if (StringUtils.isNotBlank(year)) {
			where.append(" AND tff.create_time like ? ");
			paramsList.add(year + "%");
		}
		if (StringUtils.isNotBlank(month)) {
			where.append(" AND MONTH(tff.create_time)=? ");
			paramsList.add(month);
		}
		if (StringUtils.isNotBlank(day)) {
			where.append(" AND DAY(tff.create_time)=? ");
			paramsList.add(day);
		}
		// 信息费优化添加(过滤用户冻结账户生成流水)
		TytUserAccount frozenAccount = accountService.queryAccount(Long.valueOf(userId), 3);
		if (frozenAccount != null) {
			where.append(" AND (tff.account_id != ? OR tff.account_id is NULL) ");
			paramsList.add(frozenAccount.getId());
		}
		if (StringUtils.isNotBlank(type) && !"1".equals(type)) {
			if ("2".equals(type)) {
				where.append(" AND tff.flow_type=? ");
				paramsList.add(2);
			} else if ("3".equals(type)) {
				where.append(" AND tff.flow_type=? ");
				paramsList.add(3);
			} else if ("4".equals(type)) {
				where.append(" AND (tff.flow_type=? OR tff.flow_type=? OR tff.flow_type=?) ");
				paramsList.add(1);
				paramsList.add(8);
				paramsList.add(10);
			}
		}
		BigInteger total = this.getBaseDao().query(count.append(where.toString()).toString(), paramsList.toArray(new Object[] {}));
		/*
		 * 总数大于0才去查询数据
		 */
		if (total == null || total.intValue() < 1) {
			resultBean.setMaxPage(0);
			resultBean.setPageSize(searchSize);
			resultBean.setTotalRecord(0);
		} else {
			/*
			 * 查询数据
			 */
			int totalRecord = total.intValue();
			int pageNumber = (totalRecord + searchSize - 1) / searchSize;
			resultBean.setMaxPage(pageNumber);
			resultBean.setPageSize(searchSize);
			resultBean.setTotalRecord(totalRecord);
			resultBean.setCurrentPage(Integer.valueOf(currentPage));
			if (Integer.valueOf(currentPage).intValue() <= pageNumber) {
				StringBuilder sql = new StringBuilder("SELECT tff.`money_amount` AS 'money', tff.`waybill_number` AS 'waybillNumber', tff.`orde_number` AS 'orderNumber', tff.`bank_name` AS 'payBankName', tff.`transfter_bank` AS 'payChannelName', tff.`create_time` AS 'time', tff.`flow_type` AS 'type' FROM tyt_financial_flow tff WHERE tff.`user_id`=? AND SUBSTR(tff.`money_amount`,2)!='0' ");
				sql.append(where.toString());
				sql.append(" ORDER BY tff.`id` DESC LIMIT ?,? ");
				paramsList.add(startPos);
				paramsList.add(searchSize);
				Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
				scalarMap.put("money", Hibernate.STRING);
				scalarMap.put("waybillNumber", Hibernate.STRING);
				scalarMap.put("orderNumber", Hibernate.STRING);
				scalarMap.put("payBankName", Hibernate.STRING);
				scalarMap.put("payChannelName", Hibernate.STRING);
				scalarMap.put("time", Hibernate.STRING);
				scalarMap.put("type", Hibernate.INTEGER);
				List<UserWalletFlowBean> pocketFlowBeans = this.getBaseDao().search(sql.toString(), scalarMap,
						UserWalletFlowBean.class, paramsList.toArray(new Object[] {}));
				resultBean.setData(pocketFlowBeans);
			}
		}
		return resultBean;
	}

	@SuppressWarnings("deprecation")
	@Override
	public ShortMsgBean saveWithdraw(String userId, String cardOwnerName, String cardNumber, String cardDepositBank, Integer drawAccount, String cardBlongedBank) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		/*
		 * 添加提款申请记录
		 */
		String sql = "INSERT INTO `tyt`.`tyt_draw_money_apply` ( `apply_time`, `user_id`, `bank_card_owner`, `bank_card_number`, `card_blonged_bank`, `card_deposit_bank`, `draw_account`) VALUES (?,?,?,?,?,?,?)";
		Object[] parameters = new Object[] { new Date(), userId, cardOwnerName, cardNumber, cardBlongedBank, cardDepositBank, drawAccount * 100 };
		TytDrawMoneyApply drawMoneyApply = new TytDrawMoneyApply();
		drawMoneyApply.setApplyTime(sdf.format(new Date()));
		drawMoneyApply.setBankCardOwner(cardOwnerName);
		drawMoneyApply.setUserId(Integer.valueOf(userId));
		drawMoneyApply.setBankCardNumber(cardNumber);
		drawMoneyApply.setCardBlongedBank(cardBlongedBank);
		drawMoneyApply.setCardDepositBank(cardDepositBank);
		drawMoneyApply.setDrawAccount(String.valueOf(drawAccount * 100));
		drawMoneyApplyService.add(drawMoneyApply);
		/*
		 * 更新用户钱包的金额
		 */
		sql = "UPDATE tyt_user_account SET current_balance = current_balance-? WHERE user_id=? AND affiliated_type=?";
		parameters = new Object[] { drawAccount * 100, userId, AFFILIATED_TYPE_WALLET };
		this.getBaseDao().executeUpdateSql(sql, parameters);
		User cmpUser = userService.getUserByCell(VIRTUAL_USER_ACCOUNT);
		TytUserAccount cmpAccount = accountService.queryAccount(cmpUser.getId(), 1);
		String cumUserId = cmpUser.getId() + "";
		// 信息费新版修改
		// 增加信息费提现交易
		addWithdrawTrade(cumUserId, drawAccount, cmpUser, cmpAccount);
		// 减平台余额，记录流水
		operatePlat(cumUserId, drawAccount, cardBlongedBank, sdf, drawMoneyApply, cmpAccount);
		// 减公司出账账户余额，记录流水
		operateCmpAccount(drawAccount, cardBlongedBank, sdf, drawMoneyApply, cmpUser,userId, cardOwnerName);
		/*
		 * 获取用户当前的余额
		 */
		sql = "SELECT tua.`id`, tua.`current_balance` AS 'currentBalance' FROM tyt_user_account tua WHERE tua.`affiliated_type`=1 AND tua.`user_id`=?";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("currentBalance", Hibernate.LONG);
		scalarMap.put("id", Hibernate.INTEGER);
		parameters = new Object[] { userId };
		UserAccountBean accountBean = this.getBaseDao().search(sql, scalarMap, UserAccountBean.class, parameters, 1, 1).get(0);
		// 增加一条用户提现流水记录
		logger.info("save money withdraw flow message");
		sql = "INSERT INTO `tyt`.`tyt_financial_flow` (`create_time`, `user_id`, `flow_type`, `bank_name`, `apply_id`, `money_amount`, `before_reaminging`, `after_remaining`,account_id) VALUES (?,?,?,?,?,?,?,?,?)";
		parameters = new Object[] { sdf.format(new Date()), userId, FLOW_TYPE_WITHDRAW, cardBlongedBank, drawMoneyApply.getId(), "-" + drawAccount * 100, accountBean.getCurrentBalance() + drawAccount * 100, accountBean.getCurrentBalance(),accountBean.getId() };
		this.getBaseDao().executeUpdateSql(sql, parameters);

		ShortMsgBean shortMsgBean = new ShortMsgBean();
		// 根据短信key获取短信模板
		String content = messageTmplService.getSmsTmpl(WITHDRAW_APPLY_SUCCESS_MESSAGE_TEMPLATE_KEY);
		shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
		String messageSerailNum = SerialNumUtil.generateSeriaNum();
		shortMsgBean.setMessageSerailNum(messageSerailNum);
		String applySucesscontent = StringUtils.replaceEach(content, new String[] { "${tixian}", "${yinhang}", "${weihao}" }, new String[] { drawAccount + "", cardBlongedBank, cardNumber.substring(cardNumber.length() - 4) });
		shortMsgBean.setContent(applySucesscontent);
		User user = userService.getById(Long.valueOf(userId));
		shortMsgBean.setCell_phone(user.getCellPhone());
		shortMsgBean.setRemark("");
		tytMqMessageService.addSaveMqMessage(messageSerailNum, JSON.toJSONString(shortMsgBean),MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
		return shortMsgBean;
	}

	private void operateCmpAccount(Integer drawAccount, String cardBlongedBank,
								   SimpleDateFormat sdf, TytDrawMoneyApply drawMoneyApply, User cmpUser,String userId, String cardOwnerName) {
		TytUserAccount cmpOutAccount = accountService.queryAccount(cmpUser.getId(), 5);
		long newOutBalacne = cmpOutAccount.getCurrentBalance() + drawAccount * 100;
		cmpOutAccount.setCurrentBalance(newOutBalacne);
		accountService.update(cmpOutAccount);
		FinancialInOutRemarkBean remarkBean = new FinancialInOutRemarkBean();
		remarkBean.setUserId(Long.parseLong(userId));
		remarkBean.setTrueName(cardOwnerName);
		remarkBean.setRemark(cardBlongedBank);
		String remark = JSONObject.fromObject(remarkBean).toString();
		String sql = "INSERT INTO `tyt`.`tyt_financial_flow` (`create_time`, `user_id`, `flow_type`, `bank_name`, `apply_id`, `money_amount`, `before_reaminging`, `after_remaining`, `account_id`,remark) VALUES (?,?,?,?,?,?,?,?,?,?)";
//		Object[] parameters = new Object[] { sdf.format(new Date()), cmpUser.getId(), FLOW_TYPE_PLAT_OUT, cardBlongedBank, drawMoneyApply.getId(), "+" + drawAccount * 100, newOutBalacne - drawAccount * 100, newOutBalacne, cmpOutAccount.getId() };
		Object[] parameters = new Object[] { sdf.format(new Date()), cmpUser.getId(), FinancialInOutEnum.平台提现出账.getId(), cardBlongedBank, drawMoneyApply.getId(), "+" + drawAccount * 100, newOutBalacne - drawAccount * 100, newOutBalacne, cmpOutAccount.getId(),remark };
		this.getBaseDao().executeUpdateSql(sql, parameters);
	}

	private void operatePlat(String userId, Integer drawAccount, String cardBlongedBank,
							 SimpleDateFormat sdf, TytDrawMoneyApply drawMoneyApply, TytUserAccount cmpAccount) {
		String sql;
		Object[] parameters;
		long newBalacne = cmpAccount.getCurrentBalance() - drawAccount * 100;
		cmpAccount.setCurrentBalance(newBalacne);
		accountService.update(cmpAccount);
		sql = "INSERT INTO `tyt`.`tyt_financial_flow` (`create_time`, `user_id`, `flow_type`, `bank_name`, `apply_id`, `money_amount`, `before_reaminging`, `after_remaining`, `account_id`) VALUES (?,?,?,?,?,?,?,?,?)";
		parameters = new Object[] { sdf.format(new Date()), userId, FLOW_TYPE_CMP_ACCOUNT, cardBlongedBank, drawMoneyApply.getId(), "-" + drawAccount * 100, newBalacne + drawAccount * 100, newBalacne, cmpAccount.getId() };
		this.getBaseDao().executeUpdateSql(sql, parameters);
	}

	private void addWithdrawTrade(String userId, Integer drawAccount, User cmpUser,
								  TytUserAccount cmpAccount) {
		TytTradeinfo withdrawTrade = new TytTradeinfo();
		withdrawTrade.setAllow_pay_back_refund("0");
		withdrawTrade.setGood_info_fee_id(null);
		withdrawTrade.setOrder_id(null);
		withdrawTrade.setPay_receiver_account_id(null);
		withdrawTrade.setPay_receiver_user_id(Long.valueOf(userId));
		withdrawTrade.setPayer_account_id(cmpAccount.getId());
		withdrawTrade.setPayer_user_id(cmpUser.getId());
		withdrawTrade.setRefunding_account("0");
		withdrawTrade.setTrade_account(String.valueOf(drawAccount * 100));
		Date now = new Date();
		withdrawTrade.setTrade_time(now);
		withdrawTrade.setTrade_type(3);
		withdrawTrade.setUpdate_time(now);
		tradeInfoService.add(withdrawTrade);
	}

	@SuppressWarnings("deprecation")
	@Override
	public UserWalletBean getUserWalletAccount(String userId) {
		String sql = "SELECT tua.`id`, tua.`user_id` AS 'userId', tua.`affiliated_type` AS 'affiliatedType', tua.`create_time` AS 'createTime', tua.`current_balance` AS 'currentBalance', tua.`update_time` AS 'updateTime' FROM tyt_user_account tua WHERE tua.`user_id`=:userId AND tua.`affiliated_type`=1;";
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("userId", userId);
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.INTEGER);
		scalarMap.put("userId", Hibernate.INTEGER);
		scalarMap.put("currentBalance", Hibernate.LONG);
		scalarMap.put("affiliatedType", Hibernate.INTEGER);
		scalarMap.put("createTime", Hibernate.STRING);
		scalarMap.put("updateTime", Hibernate.STRING);
		List<UserWalletBean> userWalletBeans = this.getBaseDao().search(sql, scalarMap, UserWalletBean.class, paramMap);
		return userWalletBeans.get(0);
	}

	@Override
	public void saveSendMessage(String userId, String cardBlongedBank, String cardNumber, Integer drawAccount) {
		/*
		 * 发送提现申请成功短信，不需要发送站内信
		 */
		ShortMsgBean shortMsgBean = new ShortMsgBean();
		// 根据短信key获取短信模板
		String content = messageTmplService.getSmsTmpl(WITHDRAW_APPLY_SUCCESS_MESSAGE_TEMPLATE_KEY);
		shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
		String messageSerailNum = SerialNumUtil.generateSeriaNum();
		shortMsgBean.setMessageSerailNum(messageSerailNum);
		String applySucesscontent = StringUtils.replaceEach(content, new String[] { "${tixian}", "${yinhang}", "${weihao}" }, new String[] { drawAccount + "", cardBlongedBank, cardNumber.substring(cardNumber.length() - 4) });
		shortMsgBean.setContent(applySucesscontent);
		User user = userService.getById(Long.valueOf(userId));
		shortMsgBean.setCell_phone(user.getCellPhone());
		shortMsgBean.setRemark("");
		String messageContent = JSON.toJSONString(shortMsgBean);
		logger.info("send mq mesage, the content is: " + messageContent);
		tytMqMessageService.sendMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
	}

	@Override
	public void updatePocketPwd(String userId, Integer type, String pwd, ResultMsgBean rm) {
		User user = userService.getById(Long.valueOf(userId));
		String cellphone = user.getCellPhone();
		String verifyKey = Constant.SMS_VERIFIED_PREFFIX + cellphone;
		// 验证是否经过了验证码的验证步骤
		if (RedisUtil.get(verifyKey) != null || type.intValue() == 3) {
			// 更新密码
			String sql = "UPDATE tyt.`tyt_user_sub` tus SET tus.`pocket_pwd_status`=?, tus.`pocket_pwd`=?,tus.`utime`=? WHERE tus.`user_id`=?";
			this.getBaseDao().executeUpdateSql(sql,
					new Object[] { 2, MD5Util.GetMD5Code(pwd + userId), new Date(), userId });
			RedisUtil.del(verifyKey);
			recacheUserSubCache(userId);
			rm.setCode(200);
			rm.setMsg("密码设置成功");
		} else {
			rm.setCode(300);
			rm.setMsg("密码设置失败");
		}
	}

	private void recacheUserSubCache(String userId) {
		// 删除缓存的旧user_sub信息，重新缓存新的
		cacheService.del(Constant.CACHE_USERSUB_KEY + userId + "_"
				+ TimeUtil.formatDateMonthTime(new Date()));
		TytUserSub tytUserSub = userSubService.getTytUserSubByUserId(Long.valueOf(userId));
		cacheService.setObject(
				Constant.CACHE_USERSUB_KEY + userId + "_"
						+ TimeUtil.formatDateMonthTime(new Date()),
				tytUserSub, Constant.CACHE_EXPIRE_TIME_24H);
	}

	@Override
	public BigDecimal getUserSumWalletAccount(String userId) {
		String sql = "SELECT SUM(current_balance) as totalRemaining  FROM tyt_user_account tua WHERE tua.`user_id`=:userId";
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("userId", userId);
		BigDecimal totalRemaining = this.getBaseDao().queryByMap(sql, paramMap, null, null);
		return totalRemaining;
	}

	@Override
	public BigDecimal getUserAccountBalance(String userId) throws Exception {

		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("userId", userId);
		paramMap.put("timestamp",System.currentTimeMillis()+"");
		paramMap.put("merchantId",MERCHANT_ID);
		paramMap.put("version",VERSION);

		String result = TpayUtil.sendBodyRequest(tpayAccountUrl + "/account/info/findBalanceByUserId", paramMap);
		ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);

		if(resultMsgBean!=null&&resultMsgBean.getCode()== ReturnCodeConstant.OK){
			AccountBalanceBean accountBalanceBean =JSON.parseObject(resultMsgBean.getData().toString(), AccountBalanceBean.class);
			logger.info("获取账户余额信息:{}", resultMsgBean);
			if(accountBalanceBean!=null){
				return accountBalanceBean.getBalance().add(accountBalanceBean.getFreezeBalance());
			}
			return new BigDecimal(BigInteger.ZERO);
		}else {
			return new BigDecimal(BigInteger.ZERO);
		}
	}


	@Override
	public BigDecimal getManBangUserAccountBalance(String userId,String ticket) throws Exception {
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("userId", Long.valueOf(userId));
		paramMap.put("timestamp",System.currentTimeMillis()+"");
		paramMap.put("merchantId",MERCHANT_ID);
		paramMap.put("version",VERSION);
		Integer interfaceMoveSwitch = tytConfigService.getIntValue("interface_move_switch", 0);
		logger.info("【接口迁移】开关:{}",interfaceMoveSwitch);
		String baseUrl=interfaceMoveSwitch == 1?userCenterUrl+"/acct/query/group/balance.action":userWeb+"/acct/query/group/balance";
		String result = TpayUtil.sendBodyRequest(baseUrl, paramMap);
		logger.info("获取满帮账户余额信息结果，baseUrl:{} result:{}",baseUrl, result);
		ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
		if(resultMsgBean!=null&&resultMsgBean.getCode()== ReturnCodeConstant.OK&&Objects.nonNull(resultMsgBean.getData())){
			AccountData accountData =JSON.parseObject(resultMsgBean.getData().toString(), AccountData.class);
			logger.info("获取满帮账户余额信息:{}", resultMsgBean);
			if(accountData!=null){
				BigDecimal totalFrozenAmountByUserId = transportOrdersService.getTotalFrozenAmountByUserId(userId);
				return new BigDecimal(accountData.getAvailableBalance()).add(totalFrozenAmountByUserId);
			}
			return new BigDecimal(BigInteger.ZERO);
		}else {
			return new BigDecimal(BigInteger.ZERO);
		}
	}




}
