package com.tyt.infofee.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.TytTransportWaybillExEvaluateService;
import com.tyt.model.TytTransportWaybillExEvaluate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/15 10:30
 * @Version 1.0
 **/
@Service("transportWaybillExEvaluateService")
public class TytTransportWaybillExEvaluateServiceImpl extends BaseServiceImpl<TytTransportWaybillExEvaluate, Long> implements TytTransportWaybillExEvaluateService {

    @Resource(name = "transportWaybillExEvaluateDao")
    public void setBaseDao(BaseDao<TytTransportWaybillExEvaluate, Long> transportWaybillExEvaluateDao) {
        super.setBaseDao(transportWaybillExEvaluateDao);
    }
}
