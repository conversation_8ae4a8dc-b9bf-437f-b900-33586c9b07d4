package com.tyt.infofee.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.TytUserAccountService;
import com.tyt.model.TytUserAccount;

@Service("tytUserAccountService")
public class TytUserAccountServiceImpl extends BaseServiceImpl<TytUserAccount, Long> implements TytUserAccountService {

    @Resource(name = "userAccountDaoImpl")
    public void setBaseDao(BaseDao<TytUserAccount, Long> tytUserAccountDao) {
        super.setBaseDao(tytUserAccountDao);
    }

    @Override
    public TytUserAccount queryAccount(Long platUserId, int accountType) {
        TytUserAccount userAccount = new TytUserAccount();
        userAccount.setAffiliatedType(accountType);
        userAccount.setUserId(platUserId);
        List<TytUserAccount> userAccountsList = this.getBaseDao().search(userAccount);
        return userAccountsList.size() == 1 ? userAccountsList.get(0) : null;
    }


    @Override
    public void updateVirtualAccountPocket(Long totalFee, Long userId, int accountTypePocket, String synbolType) {
        String sql = "UPDATE tyt_user_account SET current_balance=current_balance" + synbolType + "? WHERE user_id=? AND affiliated_type=?";
        Object[] params = new Object[]{totalFee, userId, accountTypePocket};
        this.getBaseDao().executeUpdateSql(sql, params);
    }
}
