package com.tyt.infofee.service.impl;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.DrawMoneyApplyService;
import com.tyt.model.TytDrawMoneyApply;

@Service("drawMoneyApplyService")
public class DrawMoneyApplyServiceImpl extends BaseServiceImpl<TytDrawMoneyApply, Integer> implements DrawMoneyApplyService {
	@Resource(name = "drawMoneyApplyDao")
	public void setBaseDao(BaseDao<TytDrawMoneyApply, Integer> drawMoneyApplyDao) {
		super.setBaseDao(drawMoneyApplyDao);
	}
}
