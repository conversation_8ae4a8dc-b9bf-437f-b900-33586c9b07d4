package com.tyt.infofee.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.TransportWayBillExVoucherService;
import com.tyt.model.TytTransportWaybillEx;
import com.tyt.model.TytTransportWaybillExVoucher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service(value = "transportWayBillExVoucherService")
public class TransportWayBillExVoucherServiceImpl extends BaseServiceImpl<TytTransportWaybillExVoucher, Long> implements TransportWayBillExVoucherService {
    @Resource(name = "transportWayBillExVoucherDao")
    public void setBaseDao(BaseDao<TytTransportWaybillExVoucher, Long> transportWaybillExVoucherDao) {
        super.setBaseDao(transportWaybillExVoucherDao);
    }
}
