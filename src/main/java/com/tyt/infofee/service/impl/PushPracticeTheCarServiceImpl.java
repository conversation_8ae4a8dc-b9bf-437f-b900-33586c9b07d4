package com.tyt.infofee.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.infofee.service.PushPracticeTheCarService;
import com.tyt.model.*;
import com.tyt.transport.service.BsPublishTransportService;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.ReturnCodeConstant;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("pushPracticeTheCarService")
public class PushPracticeTheCarServiceImpl extends BaseServiceImpl<TytTransportOrders, Long> implements PushPracticeTheCarService {

    @Resource(name = "PushPracticeTheCarDao")
    public void setBaseDao(BaseDao<TytTransportOrders, Long> PushPracticeTheCarDao) {
        super.setBaseDao(PushPracticeTheCarDao);
    }
    @Resource(name = "pushPracticeTheCarService")
    private PushPracticeTheCarService pushPracticeTheCarService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "bsPublishTransportService")
    private BsPublishTransportService bsPublishTransportService;
    @Resource(name = "infoFeeBusinessService")
    InfoFeeBusinessService infoFeeBusinessService;
    @Resource(name = "tytMqMessageService")
    TytMqMessageService tytMqMessageService;
    @Resource(name = "tytConfigService")
    private TytConfigService configService;
    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Override
    public List<PracticeTheCar> getPractice(Long userId, Long queryTrade) {
        List<Object> params = new ArrayList<Object>();
        List<PracticeTheCar> practiceList = null;
        // SQL查询条件
        StringBuffer conditionSQL = new StringBuffer(" where 1 = 1 ");

        if ( userId != null ) {
            conditionSQL.append("and ptc.user_id = ?");
            params.add(userId);
        }
        //分页与排序SQL 最多返回1000条数据
        if (queryTrade >= 1 && queryTrade <= 34){
            conditionSQL.append(" order by trade_successfully desc,id desc limit ?,?");
            if (queryTrade == 34){
                params.add((queryTrade-1)*30);
                params.add(10);
            }else {
                params.add((queryTrade-1)*30);
                params.add(30);
            }
        }

        StringBuffer querySQL = new StringBuffer("SELECT id ,ptc.pay_user_id AS payUserId , ptc.car_true_name AS carTrueName , ptc.pay_cell_phone AS payCellPhone , ptc.trade_successfully AS tradeSuccessfully , ptc.identity_status AS identityAuth FROM `Practice_the_car` ptc ");

        Map<String, org.hibernate.type.Type> Map = new HashMap<String, org.hibernate.type.Type>();
        Map.put("id",Hibernate.LONG);
        Map.put("payUserId",Hibernate.LONG);
        Map.put("carTrueName",Hibernate.STRING);
        Map.put("payCellPhone",Hibernate.STRING);
        Map.put("tradeSuccessfully",Hibernate.INTEGER);
        Map.put("identityAuth",Hibernate.INTEGER);
        practiceList = this.getBaseDao().search(querySQL.append(conditionSQL).toString(), Map, PracticeTheCar.class, params.toArray());
        return practiceList;
    }



    @Override
    public ResultMsgBean saveWayBill(Long userId, Long goodsId, String telephone, Long agencyMoney, Integer carOwnerPayType, Integer carriageFee, CarSaveBean infoFeeCarRequest,Integer driverId,Integer invoiceTransport) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        Transport transport = null;
        try {
            // 必填项验证
            User payUser = userService.getByUserId(userId);
            if (payUser == null) {
                resultMsgBean.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
                resultMsgBean.setMsg("userId代表的对象不存在");
                return resultMsgBean;
            }
            if (goodsId == null || goodsId.longValue() <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("goodsId参数错误");
                return resultMsgBean;
            }
            if (telephone == null || telephone.trim().equals("")) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("telephone参数错误");
                return resultMsgBean;
            }
            if (agencyMoney == null || agencyMoney.longValue() <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("agencyMoney参数错误");
                return resultMsgBean;
            }
            // tyt_plat_transport_optimize20171123 货源优化代码
            transport = transportBusiness.getByGoodsId(goodsId);
            // 在操作之前先上redis锁
            if (transport != null) {
                int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
                if (LockUtil.lockObject("1", transport.getSrcMsgId() + "", redisLockTimeout)) {
                    // 添加或者修改运单表，修改货物信息，同时添加数据到接单表
                    resultMsgBean = infoFeeBusinessService.savePayOrderBusiness(0,goodsId, telephone , carriageFee, agencyMoney, payUser, resultMsgBean, transport.getSrcMsgId(), infoFeeCarRequest, null,null,driverId, null);
                    // 保存并发送用户创建订单的消息
//                    MqTytCarIntentionMsg carIntentionMsg = new MqTytCarIntentionMsg();
//                    infoFeeBusinessService.saveMqMessage(transport.getSrcMsgId(), payUser, carIntentionMsg);
                    // 只有在发货人是公司账户的才发送消息
                    TransportMain oldTransport = transportMainService.getById(goodsId);
                    /** 特运通账户ID */
                    String companyAccountUserId = configService.getStringValue("tyt_company_account_user_id");
                    //2021-10-09update by sissy 新增oldTransport!=null判断 并将原来的||改为&&
//                    if (oldTransport!=null&& StringUtils.isNotEmpty(companyAccountUserId) && companyAccountUserId.equals(String.valueOf(oldTransport.getUserId()))) {
//                        tytMqMessageService.sendMqMessage(carIntentionMsg.getMessageSerailNum(), JSON.toJSONString(carIntentionMsg), carIntentionMsg.getMessageType());
//                    }
                }
            } else {
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("失败");
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        } finally {
            long t2 = System.currentTimeMillis();
            // 释放redis锁
            if (transport != null) {
                LockUtil.unLockObject("1", transport.getSrcMsgId() + "");
            }
        }
    }

}
