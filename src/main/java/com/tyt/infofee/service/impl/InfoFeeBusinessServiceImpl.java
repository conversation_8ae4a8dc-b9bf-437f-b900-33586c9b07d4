package com.tyt.infofee.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.common.service.TytNoticeRemindTmplService;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.bean.plat.PriceReq;
import com.tyt.infofee.enums.*;
import com.tyt.infofee.service.*;
import com.tyt.invoicetransport.bean.OrderOperateReasonVO;
import com.tyt.marketingActivity.enums.ActivityTypeEnum;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.mybatis.mapper.InfofeeDetailMapper;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.biz.feedback.pojo.GetUserFeedbackRatingVO;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.biz.invoice.db.InvoiceDbService;
import com.tyt.plat.client.invoice.InvoiceOpenApiClient;
import com.tyt.plat.client.transport.ThPriceClient;
import com.tyt.plat.client.transport.TransportSearchClient;
import com.tyt.plat.client.transport.TransportTecserviceFeeClient;
import com.tyt.plat.client.transport.dto.BenefitLabelVO;
import com.tyt.plat.client.transport.dto.CheckIsNeedFreeTecServiceFeeVO;
import com.tyt.plat.client.user.ApiInvoiceEnterpriseClient;
import com.tyt.plat.client.user.ApiUserInvoiceClient;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.utils.NumberConvertUtil;
import com.tyt.plat.vo.invoice.*;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.plat.vo.user.InvoiceEnterpriseInfoVO;
import com.tyt.promo.service.ICouponService;
import com.tyt.receive.service.TransportBackendAxbBinderService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.bean.ComplaintRecordBean;
import com.tyt.transport.enums.CarStyleEnum;
import com.tyt.transport.enums.NewIdentityEnum;
import com.tyt.transport.enums.OrderStatusEnum;
import com.tyt.transport.enums.YesOrNoEnum;
import com.tyt.transport.querybean.CallLogBean;
import com.tyt.transport.querybean.TransportYmmListBean;
import com.tyt.transport.querybean.TransportYmmSyncLogBean;
import com.tyt.transport.service.*;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.service.*;
import com.tyt.util.*;
import com.tytrecommend.model.NewIdentity;
import com.tytrecommend.recommend.service.NewIdentityService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("infoFeeBusinessService")
public class InfoFeeBusinessServiceImpl extends BaseServiceImpl<TytTransportWaybill, Long> implements InfoFeeBusinessService {
    private static final PropertiesFileUtil propertiesFileUtil = PropertiesFileUtil.init("message");
    public static final String USER_TYPE_ONE = "1";
    public static final String USER_TYPE_ZERO = "0";

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "transportBusiness")
    @Lazy
    TransportBusinessInterface transportBusiness;

    @Resource(name = "transportWayBillService")
    private TransportWayBillService transportWayBillService;

    @Resource(name = "transportOrdersService")
    TransportOrdersService transportOrdersService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Autowired
    private PublicResourceService publicResourceService;

    @Resource(name = "transportCollectService")
    TransportCollectService collectService;

    @Resource(name = "transportWayBillExService")
    private TransportWayBillExService transportWayBillExService;

    @Resource(name = "tytBubbleService")
    TytBubbleService tytBubbleService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "tytNoticeRemindService")
    TytNoticeRemindService tytNoticeRemindService;

    @Resource(name = "tytNoticeRemindTmplService")
    TytNoticeRemindTmplService tytNoticeRemindTmplService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;

    @Autowired
    private ApiInvoiceEnterpriseClient apiInvoiceEnterpriseClient;

    @Resource(name = "userService")
    UserService userService;
    @Resource(name = "machineTypeService")
    private MachineTypeService machineTypeService;
    @Resource(name = "machineTypeNewService")
    private MachineTypeNewService machineTypeNewService;

    @Resource(name = "tytCarOwnerIntentionService")
    private TytCarOwnerIntentionService carOwnerIntentionService;

    @Resource(name = "transportMtService")
    private TransportMtService transportMtService;

    @Autowired
    private InfofeeDetailMapper infofeeDetailMapper;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;


    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Autowired
    private BackendTransportMapper backendTransportMapper;
    @Resource(name = "transportBackendAxbBinderService")
    private TransportBackendAxbBinderService transportBackendAxbBinderService;
    @Resource
    private TransportService transportService;

    @Resource
    private TransportCollectService transportCollectService;

    @Autowired
    private TytTransportOrderSnapshotMapper tytTransportOrderSnapshotMapper;

    @Autowired
    private TransportDoneService transportDoneService;

    @Autowired
    private ICouponService couponService;

    @Autowired
    private TytOwnerAuthService tytOwnerAuthService;

    @Autowired
    private ComplaintRecordService complaintRecordService;

    @Autowired
    private IFeedbackUserService feedbackUserService;

    @Autowired
    private TytMbCargoSyncInfoMapper tytMbCargoSyncInfoMapper;

    @Autowired
    private TytUserRecordMapper tytUserRecordMapper;

    @Autowired
    private TytTransportTechnicalOrderMapper transportTechnicalOrderMapper;

    @Autowired
    private TytTransportMbMergeMapper tytTransportMbMergeMapper;

    @Autowired
    private TytCoverGoodsDialConfigService coverGoodsDialConfigService;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;
    @Autowired
    private ExposureCardGiveawayRecordMapper exposureCardGiveawayRecordMapper;

    @Autowired
    private InvoiceOpenApiClient invoiceOpenApiClient;

    @Autowired
    private ApiUserInvoiceClient apiUserInvoiceClient;

    @Autowired
    private InvoiceDbService invoiceDbService;

    @Autowired
    private TransportEnterpriseLogService transportEnterpriseLogService;

    @Autowired
    private CarService carService;

    @Autowired
    private TytTransportMainExtendMapper transportMainExtendMapper;

    @Autowired
    private TytCityMapper tytCityMapper;

    @Autowired
    private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;

    @Autowired
    private TransportViewLogService transportViewLogService;

    @Autowired
    private NewIdentityService newIdentityService;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private TransportAutoResendService autoResendService;

    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;

    @Autowired
    private MarketingActivityService marketingActivityService;

    @Autowired
    private ThPriceClient thPriceClient;

    @Autowired
    private TransportSearchClient transportSearchClient;

    @Autowired
    private CallFeedbackLogMapper callFeedbackLogMapper;

    private static final String GOODS_SOURCE_PROJECT_NAME = "-";


    /**
     * 保存订单货源快照表
     * @param transportMain
     * @param transportOrders
     * @return
     */

    /**
     * 保存订单货源快照表
     *
     * @param transportMain
     * @param transportOrders
     * @return
     */
    @Override
    public TytTransportOrderSnapshot saveTransportOrderSnapshot(TransportMain transportMain, TytTransportOrders transportOrders, TytMbCargoSyncInfo tytMbCargoSyncInfo, InvoiceEnterpriseInfoVO invoiceEnterpriseInfoVO, String projectName) {
        //用户昵称
        String nickName = "";
        if (tytMbCargoSyncInfo != null) {
            nickName = tytMbCargoSyncInfo.getContactName();
        } else {
            nickName = transportMain.getNickName();
        }
        //优车s城市，英文逗号分隔
        String excellentSCity = tytConfigService.getStringValue("excellent_s_city", "广州市,深圳市");
        //是否是S城直客（0:否 1:是）
        int isScityDirectCustomer = 0;
        NewIdentity newIdentity = newIdentityService.getByUserId(transportMain.getUserId());
        if (Objects.nonNull(newIdentity)) {
            NewIdentityEnum identityEnum = NewIdentityEnum.getByCode(newIdentity.getType());
            if (Objects.nonNull(identityEnum)
                    && StringUtils.isNotBlank(excellentSCity)
                    && (NewIdentityEnum.ENTERPRISE_CARGO_OWNER.equals(identityEnum)
                    || NewIdentityEnum.SELF_CARGO_OWNER.equals(identityEnum))
                    && excellentSCity.contains(transportMain.getStartCity())
            ) {
                isScityDirectCustomer = 1;
            }
        }

        String invoiceGoodsSource = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(projectName)) {
            String[] projectNameArray = projectName.split(GOODS_SOURCE_PROJECT_NAME);
            if (projectNameArray.length > 1) {
                invoiceGoodsSource = projectNameArray[0];
            }
        }

        //两者实体类类型不同，不可使用BeanUtils.copyProperties
        TytTransportOrderSnapshot tytTransportOrderSnapshot = TytTransportOrderSnapshot.builder()
                .id(null)
                .srcMsgId(transportMain.getId())
                .orderId(transportOrders.getId())
                .snapshotTime(transportOrders.getCreateTime())

                .startPoint(transportMain.getStartPoint())
                .destPoint(transportMain.getDestPoint())
                .taskContent(transportMain.getTaskContent())
                .tel(transportMain.getTel())
                .pubTime(transportMain.getPubTime())
                .pubQq(transportMain.getPubQQ())
                .nickName(nickName)
                .userShowName(transportMain.getUserShowName())
                .status(NumberConvertUtil.toShort(transportMain.getStatus()))
                .source(NumberConvertUtil.toShort(transportMain.getSource()))
                .ctime(transportMain.getCtime())
                .mtime(transportMain.getMtime())
                .uploadCellphone(transportMain.getUploadCellPhone())
                .resend(NumberConvertUtil.toShort(transportMain.getResend()))
                .startCoord(transportMain.getStartCoord())
                .destCoord(transportMain.getDestCoord())
                .platId(NumberConvertUtil.toShort(transportMain.getPlatId()))
                .verifyFlag(NumberConvertUtil.toShort(transportMain.getVerifyFlag()))
                .price("" + transportOrders.getCarriageFee())
                .userId(transportMain.getUserId())
                .priceCode(transportMain.getPriceCode())

                .startCoordX(transportMain.getStartCoordXValue())
                .startCoordY(transportMain.getStartCoordYValue())
                .destCoordX(transportMain.getDestCoordXValue())
                .destCoordY(transportMain.getDestCoordYValue())
                .startDetailAdd(transportMain.getStartDetailAdd())
                .startLongitude(transportMain.getStartLongitudeValue())
                .startLatitude(transportMain.getStartLatitudeValue())
                .destDetailAdd(transportMain.getDestDetailAdd())
                .destLongitude(transportMain.getDestLongitudeValue())
                .destLatitude(transportMain.getDestLatitudeValue())
                .pubDate(transportMain.getPubDate())
                .goodsCode(transportMain.getGoodsCode())
                .weightCode(transportMain.getWeightCode())
                .weight(transportMain.getWeight())
                .length(transportMain.getLength())
                .wide(transportMain.getWide())
                .high(transportMain.getHigh())
                .isSuperelevation(transportMain.getIsSuperelevation())
                .linkman(transportMain.getLinkman())
                .remark(transportMain.getRemark())
                .distance(transportMain.getDistanceValue())
                .pubGoodsTime(transportMain.getPubGoodsTime())
                .tel3(transportMain.getTel3())
                .tel4(transportMain.getTel4())
                .displayType(transportMain.getDisplayType())
                .hashCode(transportMain.getHashCode())
                .isCar(transportMain.getIsCar())
                .userType(NumberConvertUtil.toShort(transportMain.getUserType()))
                .pcOldContent(transportMain.getPcOldContent())
                .resendCounts(transportMain.getResendCounts())
                .verifyPhotoSign(NumberConvertUtil.toShort(transportMain.getVerifyPhotoSign()))
                .userPart(transportMain.getUserPart())
                .startCity(transportMain.getStartCity())
                .startProvinc(transportMain.getStartProvinc())
                .startArea(transportMain.getStartArea())
                .destProvinc(transportMain.getDestProvinc())
                .destCity(transportMain.getDestCity())
                .destArea(transportMain.getDestArea())
                .clientVersion(transportMain.getClientVersion())
                .isInfoFee(transportMain.getIsInfoFee())
                .infoStatus(transportMain.getInfoStatus())
                .tsOrderNo(transportMain.getTsOrderNo())
                .releaseTime(transportMain.getReleaseTime())
                .regTime(transportMain.getRegTime())
                .type(transportMain.getType())
                .brand(transportMain.getBrand())
                .goodTypeName(transportMain.getGoodTypeName())
                .goodNumber(transportMain.getGoodNumber())
                .isStandard(NumberConvertUtil.toByte(transportMain.getIsStandard()))
                .matchItemId(transportMain.getMatchItemId())
                .androidDistance(transportMain.getAndroidDistance())
                .iosDistance(transportMain.getIosDistance())
                .isDisplay(NumberConvertUtil.toShort(transportMain.getIsDisplay()))
                .referLength(transportMain.getReferLength())
                .referWidth(transportMain.getReferWidth())
                .referHeight(transportMain.getReferHeight())
                .referWeight(transportMain.getReferWeight())
                .carLength(transportMain.getCarLength())
                .loadingTime(transportMain.getLoadingTime())
                .beginUnloadTime(transportMain.getBeginUnloadTime())
                .unloadTime(transportMain.getUnloadTime())
                .carMinLength(transportMain.getCarMinLength())
                .carMaxLength(transportMain.getCarMaxLength())
                .carType(transportMain.getCarType())
                .beginLoadingTime(transportMain.getBeginLoadingTime())
                .carStyle(transportMain.getCarStyle())
                .workPlaneMinHigh(transportMain.getWorkPlaneMinHigh())
                .workPlaneMaxHigh(transportMain.getWorkPlaneMaxHigh())
                .workPlaneMinLength(transportMain.getWorkPlaneMinLength())
                .workPlaneMaxLength(transportMain.getWorkPlaneMaxLength())
                .climb(transportMain.getClimb())
                //.orderNumber(transportMain.getorderNumber())
                //.evaluate(transportMain.getevaluate())
                .specialRequired(transportMain.getSpecialRequired())
                .similarityCode(transportMain.getSimilarityCode())
                .similarityFirstId(transportMain.getSimilarityFirstId())
                .similarityFirstInfo(transportMain.getSimilarityFirstInfo())
                .tyreExposedFlag(transportMain.getTyreExposedFlag())
                .carLengthLabels(transportMain.getCarLengthLabels())
                .shuntingQuantity(transportMain.getShuntingQuantity())
                .firstPublishType(NumberConvertUtil.toShort(transportMain.getFirstPublishType()))
                .publishType(NumberConvertUtil.toShort(transportMain.getPublishType()))
                .excellentGoods(transportMain.getExcellentGoods())
                .infoFee(transportMain.getInfoFee())
                .tecServiceFee(transportMain.getTecServiceFee())
                .isDelete(transportMain.getIsDelete())
                .exclusiveType(transportMain.getExclusiveType())
                .totalScore(transportMain.getTotalScore())
                .rankLevel(transportMain.getRankLevel())
                .refundFlag(transportMain.getRefundFlag())
                .sourceType(transportMain.getSourceType())
                .authName(transportMain.getAuthName())
                .labelJson(transportMain.getLabelJson())
                .guaranteeGoods(transportMain.getGuaranteeGoods())
                .machineRemark(transportMain.getMachineRemark())
                .tsCustomerManagePhone(Objects.nonNull(invoiceEnterpriseInfoVO) ? invoiceEnterpriseInfoVO.getCustomerManagerPhone() : "")
                .tsCustomerManageName(Objects.nonNull(invoiceEnterpriseInfoVO) ? invoiceEnterpriseInfoVO.getCustomerManagerName() : "")
                .isScityDirectCustomer(isScityDirectCustomer)
                .invoiceGoodsSource(invoiceGoodsSource)
                .projectName(projectName)
                .build();
        PriceReq priceReq = ThreadLocalUtil.get();
        if (Objects.nonNull(priceReq)) {
            tytTransportOrderSnapshot.setPromotionPrice(new BigDecimal(priceReq.getPromotionPrice()));
            tytTransportOrderSnapshot.setStrikeThroughPrice(new BigDecimal(priceReq.getStrikeThroughPrice()));
        }
        //查询货源扩展表
        TytTransportMainExtend extendInfo = transportMainExtendMapper.getBySrcMsgId(transportMain.getSrcMsgId());
        if (Objects.nonNull(extendInfo)) {
            //秒抢货源：1是0否
            tytTransportOrderSnapshot.setSeckillGoods(extendInfo.getSeckillGoods());
            //订单分配状态 0初始化 1分配成功 2分配失败
            tytTransportOrderSnapshot.setOrderAllocateState(0);
        }
        tytTransportOrderSnapshotMapper.insertSelective(tytTransportOrderSnapshot);
        return tytTransportOrderSnapshot;
    }

    @Override
    public ResultMsgBean savePayOrderBusiness(Integer zeroAssignOrder, Long goodsId, String carOwnerTelephone, Integer carriageFee, Long agencyMoney, User payUser,
                                              ResultMsgBean resultMsgBean, Long srcMsgId, CarSaveBean infoFeeCarRequest, Integer couponId, Long tecServiceFee, Integer driverId, String clientVersionStr) throws Exception {
        long t1 = System.currentTimeMillis();
        logger.info("支付运前信息费service处理开始时间【{}】ms", t1);
        logger.info("info business save savePayOrderBusiness srcMsgId: " + srcMsgId);
        // tyt_plat_transport_optimize20171123 货源优化代码
        // 根据srcMsgId从货物主表中获取数据
        TransportMain originalTransport = transportMainService.getById(srcMsgId);
        if (originalTransport == null) {
            resultMsgBean.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
            resultMsgBean.setMsg("goodsId所代表的对象不存在");
            return resultMsgBean;
        }
        if (payUser.getId().longValue() == originalTransport.getUserId().longValue()) {
            resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_NOT_ALLOWED_TO_PAY);
            resultMsgBean.setMsg("不能支付自己的信息");
            return resultMsgBean;
        }
        // 开票货源，检测用户使用的APP版本
        int clientVersion = tytConfigService.getIntValue(Constant.check_invoice_transport_version);
        if (originalTransport.getInvoiceTransport() != null
                && originalTransport.getInvoiceTransport() == 1
                && StringUtils.isNotBlank(clientVersionStr)
                && (Integer.parseInt(clientVersionStr) < clientVersion)) {
            resultMsgBean.setCode(ReturnCodeConstant.VERSION_ERROR_CODE);
            resultMsgBean.setMsg("请升级当前新版本，才可以接开票货源");
            return resultMsgBean;
        }
        Integer checkMarkPrice = tytConfigService.getIntValue(Constant.check_one_mark_price, 1);
        if (checkMarkPrice != null && checkMarkPrice.intValue() == 0) {
            //不校验
        } else {
            //校验信息费
            Integer publishType = originalTransport.getPublishType();
            if (publishType != null && publishType.equals(PublishTypeEnum.fixed.getCode())) {
                //一口价需要校验金额
                String price = originalTransport.getPrice();
                BigDecimal dbInfoFee = originalTransport.getInfoFee();

                if (CommonUtil.hasNull(price, dbInfoFee)) {
                    resultMsgBean.setCode(ReturnCodeConstant.publish_type_fee_null);
                    resultMsgBean.setMsg("一口价货源信息费和运费不能为空");
                    return resultMsgBean;
                }
                if (CommonUtil.hasNull(carriageFee, agencyMoney)) {
                    resultMsgBean.setCode(ReturnCodeConstant.fee_must_not_null);
                    resultMsgBean.setMsg("信息费和运费不能为空");
                    return resultMsgBean;
                }
                BigDecimal dbPrice = new BigDecimal(price);

                BigDecimal carriageFeeDec = new BigDecimal(carriageFee);
                BigDecimal agencyMoneyDec = new BigDecimal(agencyMoney);

                if (dbPrice.compareTo(carriageFeeDec) != 0 || dbInfoFee.compareTo(agencyMoneyDec) != 0) {
                    resultMsgBean.setCode(ReturnCodeConstant.fee_not_match);
                    resultMsgBean.setMsg("一口价货源信息费和运费不能改变");
                    return resultMsgBean;
                }
            }
        }

        //判断是否使用卡券
        //优惠券金额
        BigDecimal couponAmount = BigDecimal.ZERO;
        //卡券使用范围
        PromoCoupon coupon = null;
        if (couponId != null) {
            if (!RedisUtil.setNxAtom("COUPON_" + couponId, "COUPON_" + couponId, 2)) {
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("优惠券已使用!");
                return resultMsgBean;
            }
            //订金类型（0不退还；1退还）
            Integer refundFlag = originalTransport.getRefundFlag();
            if (refundFlag != null && refundFlag.intValue() == 1) {
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("订金减免券只能在支付订金（不退还）货源时使用!");
                return resultMsgBean;
            }
            // 卡券校验 1 券未使用 2 有效期
            PromoUserCoupon userCoupon = couponService.queryUserCoupon(payUser.getId(), couponId);
            if (userCoupon == null || userCoupon.getCouponStatus() != 1) {
                logger.error("无效卡券！goodsId {} couponId {}", goodsId, couponId);
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("无效卡券！");
                return resultMsgBean;
            }
            coupon = couponService.queryCouponTypeInfo(userCoupon.getCouponTypeId());
            //判断卡券类型信息是否存在
            if (coupon == null) {
                logger.error("卡券类型信息不符！ goodsId {} couponId {}", goodsId, couponId);
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("卡券类型信息不符！");
                return resultMsgBean;
            }
            //优惠券金额
            couponAmount = coupon.getCouponAmount();
            //订金金额
            BigDecimal agencyMoneyDec = new BigDecimal(agencyMoney);
            //订金金额必须大于优惠券金额
            if (agencyMoneyDec.compareTo(couponAmount) <= 0) {
                logger.error("订金金额必须大于该优惠券金额！ goodsId {} couponId {}", goodsId, couponId);
                resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                resultMsgBean.setMsg("订金金额必须大于该优惠券金额！");
                return resultMsgBean;
            }
            //订金金额 = 原订金金额 - 优惠券金额
            agencyMoney = agencyMoneyDec.subtract(couponAmount).longValue();
        }

        // 如果没有该运单信息，则添加记录到运单表，多订运单
        TytTransportWaybill tytTransportWaybill = transportWayBillService.getTytTransportWaybillForLock(originalTransport.getTsOrderNo());
        long t2 = System.currentTimeMillis();
        logger.info("支付运前信息费锁表结束时间【{}】ms,用时【{}】ms", t2, t2 - t1);
        // 判读是不是昨天的数据
        boolean isHistoryGoods = originalTransport.getCtime().getTime() < TimeUtil.parseString(TimeUtil.formatDate(new Date())).getTime();
        Transport transport = new Transport();
        BeanUtils.copyProperties(originalTransport, transport);

        //查询是否限时货源
        int timeLimitIdentification = backendTransportMapper.selectIsTimeLimitMsg(srcMsgId);

        if (payUser.getId().longValue() == originalTransport.getUserId().longValue()) {
            resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_NOT_ALLOWED_TO_PAY);
            resultMsgBean.setMsg("测试-阻挡继续支付流程！！@@@");
            return resultMsgBean;
        }

        // 开票业务-调用三方服务商相关信息获取
        // 1.开票-查询服务商、主体信息
        ThirdDominantInfoVo thirdDominantInfoVo = null;
        // 项目名称
        String projectName = "";
        // 用户编码
        String userCode = "";
        TytTransportEnterpriseLog transportEnterpriseLog = transportEnterpriseLogService.getBySrcMsgId(srcMsgId);
        if (Objects.nonNull(transportEnterpriseLog)) {
            logger.info("开票-调用用户中心获取主体信息接口-请求参数：invoiceSubjectId:{}", transportEnterpriseLog.getInvoiceSubjectId());
            Response<InternalWebResult<ThirdDominantInfoVo>> thirdDominantInfoResp = apiUserInvoiceClient.getDominantInfoById(transportEnterpriseLog.getInvoiceSubjectId()).execute();

            thirdDominantInfoVo = InternalClientUtil.getDataDetail(thirdDominantInfoResp);
            logger.info("开票-调用用户中心获取主体信息接口-返回参数：{}", JSON.toJSONString(thirdDominantInfoVo));
            if (Objects.nonNull(thirdDominantInfoVo)
                    && InvoiceServiceProviderEnum.HBWJ.getCode().equals(thirdDominantInfoVo.getServiceProviderCode())) {
                // 2.开票-查询项目信息
                logger.info("开票-调用用户中心查询项目信息接口-请求参数：userId:{}, dominantId:{}", originalTransport.getUserId(), thirdDominantInfoVo.getId());
                Response<InternalWebResult<ThirdEnterpriseInfoVo>> thirdEnterpriseInfoResp = apiUserInvoiceClient.getInfoByUserId(originalTransport.getUserId(), thirdDominantInfoVo.getId()).execute();
                ThirdEnterpriseInfoVo thirdEnterpriseInfoVo = InternalClientUtil.getDataDetail(thirdEnterpriseInfoResp);
                logger.info("开票-调用用户中心查询项目信息接口-返回参数：{}", JSON.toJSONString(thirdEnterpriseInfoVo));
                if (Objects.nonNull(thirdEnterpriseInfoVo)) {
                    // 项目名称
                    projectName = thirdEnterpriseInfoVo.getProjectName();
                }

                // 3.开票-获取用户编码
                logger.info("开票-调用用户中心获取用户编码接口-请求参数：userId:{}, dominantId:{}", originalTransport.getUserId(), thirdDominantInfoVo.getId());
                Response<InternalWebResult<ThirdEnterpriseUserCodeVo>> thirdEnterpriseUserCodeResp = apiUserInvoiceClient.getUserCode(originalTransport.getUserId(), thirdDominantInfoVo.getId()).execute();
                ThirdEnterpriseUserCodeVo thirdEnterpriseUserCodeVo = InternalClientUtil.getDataDetail(thirdEnterpriseUserCodeResp);
                logger.info("开票-调用用户中心获取用户编码接口-返回参数：{}", JSON.toJSONString(thirdEnterpriseUserCodeVo));
                if (Objects.nonNull(thirdEnterpriseUserCodeVo)) {
                    // 用户编码
                    userCode = thirdEnterpriseUserCodeVo.getUserCode();
                }
            }
        }

        // 货源是否处于发布中，以确定是否可以被支付
        if (!isHistoryGoods && originalTransport.getStatus() == 1) {
            //查询是否为满帮的货源以及满帮货源信息
            TytMbCargoSyncInfo tytMbCargoSyncInfo = null;
            Integer sourceType = originalTransport.getSourceType();
            if (sourceType != null && sourceType == 4) {
                tytMbCargoSyncInfo = tytMbCargoSyncInfoMapper.getTytMbCargoSyncInfo(srcMsgId);
            }
            //2019-01-10修改 传入支付人信息
            if (tytTransportWaybill == null) {
                tytTransportWaybill = transportWayBillService.saveOnLineWayBill(zeroAssignOrder, null, transport, payUser, timeLimitIdentification, couponAmount);
            }
            String transportNo = originalTransport.getTsOrderNo();
            SimpleOrderBean myLastOrder = transportOrdersService.getLastOrderByPayUser(transportNo, payUser.getId());
            long t3 = System.currentTimeMillis();
            logger.info("支付运前信息费获取我的最后一条订单信息结束时间【{}】ms,用时【{}】ms", t3, t3 - t2);
            // 没有支付过或者被拒绝后均可重新支付，新增车主取消=12
            Long orderId = null;

            InvoiceEnterpriseInfoVO invoiceEnterpriseInfoVO = new InvoiceEnterpriseInfoVO();
            //todo 调用user-service接口 获取客户经理信息
            if (originalTransport.getInvoiceTransport() != null && originalTransport.getInvoiceTransport() == 1) {
                Response<InternalWebResult<InvoiceEnterpriseInfoVO>> internalWebResultResponse = apiInvoiceEnterpriseClient.getInfoByUserId(originalTransport.getUserId()).execute();
                logger.info("apiInvoiceEnterpriseClient getInfoByUserId userId:{} internalWebResultResponse:{}", originalTransport.getUserId(), JSON.toJSONString(internalWebResultResponse));
                invoiceEnterpriseInfoVO = InternalClientUtil.getDataDetail(internalWebResultResponse);
            }

            //2021-04-22 update by sissy 为防止网络延迟 重复请求该接口 导致一个用户生成多条接单信息而造成重复着支付问题 每次新增前 查看最新接单表中的状态
            Long payAmount = 0L;
            TytTransportOrders tytTransportOrders = null;
            if (myLastOrder == null || myLastOrder.getRobStatus().equals("2") || myLastOrder.getRobStatus().equals("3")
                    || myLastOrder.getRobStatus().equals("11") || myLastOrder.getRobStatus().equals("12")) {

                //生成技术服务费信息
                String technicalServiceNo = saveTechnicalService(tecServiceFee, originalTransport.getUserId(), payUser);
                //saveWayBill插入tyt_transport_orders表记录
                tytTransportOrders = transportOrdersService.saveOrderInfo(zeroAssignOrder, null, transport, carOwnerTelephone, agencyMoney, payUser, timeLimitIdentification,
                        carriageFee, infoFeeCarRequest, couponAmount, tecServiceFee, technicalServiceNo, tytMbCargoSyncInfo,
                        OrderStatusEnum.WAIT_RECEIVE_ORDERS.getCode(), driverId, originalTransport.getInvoiceTransport(), thirdDominantInfoVo);
                // 发送状态变更mq
                OrderOperateStatusLogBean oos = new OrderOperateStatusLogBean();
                try {
                    oos.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
                    oos.setOrderStatus(OrderStatusEnum.WAIT_RECEIVE_ORDERS.getCode());//待接单状态
                    oos.setOperateType(5);//下单操作
                    oos.setMessageType(MqBaseMessageBean.ORDER_OPERATE_STATUS_SYNC);
                    oos.setCreateTime(new Date());
                    oos.setOrderId(tytTransportOrders.getId());
                    oos.setCostStatus(tytTransportOrders.getCostStatus());
                    int operateSwitch = tytConfigService.getIntValue("trade_center_mq_operate_status_switch", 0);
                    if (operateSwitch == 1){
                        tytMqMessageService.sendMsgCustom(JSON.toJSONString(oos), "TRADE_CENTER_TOPIC", oos.getMessageSerailNum(), "order_operate_log", 0L);
                    }else {
                        tytMqMessageService.addSaveMqMessage(oos.getMessageSerailNum(), JSON.toJSONString(oos), MqBaseMessageBean.ORDER_OPERATE_STATUS_SYNC);
                        tytMqMessageService.sendMqMessage(oos.getMessageSerailNum(), JSON.toJSONString(oos), MqBaseMessageBean.ORDER_OPERATE_STATUS_SYNC);
                    }

                } catch (Exception e) {
                    logger.error("【下单记录订单状态异常】：{}", JSON.toJSONString(oos), e);
                }
                // 车主待支付气泡加1
                tytBubbleService.updateBubbleNumber(payUser.getId(), "1", "1", 1);
                orderId = tytTransportOrders.getId();
                payAmount = tytTransportOrders.getPayAmount();

                // todo cjg 项目名称保存在快照表
                //生成货源快照信息
                TytTransportOrderSnapshot tytTransportOrderSnapshot = this.saveTransportOrderSnapshot(originalTransport, tytTransportOrders, tytMbCargoSyncInfo, invoiceEnterpriseInfoVO, projectName);

            } else {
                orderId = myLastOrder.getId();
                payAmount = myLastOrder.getPayAmount();
            }
            if (orderId == null || orderId.longValue() <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_FAIL_TO_MAKE_ORDER);
                resultMsgBean.setMsg(propertiesFileUtil.getString("tyt.infofee.mygoods.to.make.order.failure.error.5003"));
                return resultMsgBean;
            }
            // 开票货源对接三方服务(必须是非0元指派单)
            if (Objects.equals(AssignOrderTypeEnum.no_assign_order.getCode(), zeroAssignOrder) && originalTransport.getInvoiceTransport() != null
                    && originalTransport.getInvoiceTransport() == 1
                    && Objects.nonNull(thirdDominantInfoVo)
                    && InvoiceServiceProviderEnum.HBWJ.getCode().equals(thirdDominantInfoVo.getServiceProviderCode())
                    && Objects.nonNull(tytTransportOrders)
            ) {
                // 1.开票-创建运力
                logger.info("开票-调用用户中心创建运力接口-请求参数：userId:{},carId:{},driverId:{},orderId:{},userCode:{}", payUser.getId(), infoFeeCarRequest.getCarId(), driverId, tytTransportOrders.getId(), userCode);
                Response<InternalWebResult<CarDriverResponseVO>> sendCarInvoiceCarResp = apiUserInvoiceClient.sendCarInvoice(payUser.getId(), infoFeeCarRequest.getCarId(), driverId.longValue(), tytTransportOrders.getId(), userCode).execute();
                logger.info("开票-调用用户中心创建运力接口-解析前返回参数：{}", JSON.toJSONString(sendCarInvoiceCarResp));
				/*OrderOperateReasonVO oor = new OrderOperateReasonVO();
				oor.setType(201);
				oor.setOrderId(tytTransportOrders.getId());
				oor.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
				oor.setReason("【建单-创建运力】:"+sendCarInvoiceCarResp.body().getMsg());
				tytMqMessageService.addSaveMqMessage(oor.getMessageSerailNum(),JSON.toJSONString(oor),MqBaseMessageBean.ORDER_OPERATE_REASON);
				tytMqMessageService.sendMqMessage(oor.getMessageSerailNum(),JSON.toJSONString(oor),MqBaseMessageBean.ORDER_OPERATE_REASON);*/
                // 2.开票-创建运单
                CreateWaybillThreeRequest request = new CreateWaybillThreeRequest();
                // 用户会员代码
                request.setUserCode(userCode);
                // 设置三方配置
                CreateWaybillThreeRequest.ThreeConfig threeConfig = new CreateWaybillThreeRequest.ThreeConfig();
                threeConfig.setCreateWaybillStatus(1); //建单后运单状态：传1的时候，建单后 ~ 起运前，都可以编辑运力。
                threeConfig.setIsSendWaybillContract(false); // 设置发送运输合同为true
                request.setThreeConfig(threeConfig);

                // 设置运单信息
                CreateWaybillThreeRequest.WaybillDto waybillDto = new CreateWaybillThreeRequest.WaybillDto();
                waybillDto.setBusinessSource("WAYBILL"); // 设置业务来源为直接派单
                waybillDto.setWaybillType(1); // 设置运单类型为直接建单
                waybillDto.setUpstreamCode(tytTransportOrders.getId() + ""); //三方单号
                waybillDto.setThreeCreateTime(TimeUtil.formatDateTime(new Date()));
                waybillDto.setTransportType("CAR"); // 设置运输方式为汽运
                waybillDto.setValuationType("CAR"); // 设置计价方式为单车/船
                request.setWaybillDto(waybillDto);

                // 设置运力信息
                CreateWaybillThreeRequest.CarDriverDto carDriverDto = new CreateWaybillThreeRequest.CarDriverDto();
                TytInvoiceDriver invoiceDriver = invoiceDbService.getById(driverId.longValue());
                if (Objects.nonNull(invoiceDriver)) {
                    carDriverDto.setDriverName(invoiceDriver.getName()); // 设置驾驶人姓名
                    carDriverDto.setDriverPhone(invoiceDriver.getPhone()); // 设置驾驶人手机号
                }
                // 只传车头的车牌号
                Car car = carService.getById(infoFeeCarRequest.getCarId());
                if (Objects.nonNull(car)) {
                    String travelNum = car.getHeadCity() + car.getHeadNo();
                    carDriverDto.setTravelNum(travelNum); // 设置车船号
                    request.setCarDriverDto(carDriverDto);
                }

                // 设置路线信息
                List<String> municipalities = Arrays.asList("北京市", "天津市", "上海市", "重庆市");

                CreateWaybillThreeRequest.RouteInfoDto routeInfoDto = new CreateWaybillThreeRequest.RouteInfoDto();
                String startProvinc = originalTransport.getStartProvinc();
                String startCity = originalTransport.getStartCity();
                String startArea = originalTransport.getStartArea();
                routeInfoDto.setSendProvince(ProvinceNameCompleter.completeProvinceName(startProvinc)); // 设置发货省

                TytCityDto tytCityDto = new TytCityDto();
                tytCityDto.setCityName(startCity);
                tytCityDto.setAreaName(startArea);
                TytCity tytCity = tytCityMapper.getCityDataByCondition(tytCityDto);
                if (Objects.nonNull(tytCity) && !municipalities.contains(startCity)) {
                    routeInfoDto.setSendCity(tytCity.getMapCityName()); // 设置发货市
                } else {
                    routeInfoDto.setSendCity(startCity); // 设置发货市
                }

                if (StringUtils.isNotBlank(startCity)
                        && StringUtils.isNotBlank(startArea)
                        && !startCity.equals(startArea)) {
                    if (Objects.nonNull(tytCity)) {
                        routeInfoDto.setSendArea(tytCity.getMapAreaName()); // 设置发货区
                    } else {
                        routeInfoDto.setSendArea(startArea); // 设置发货区
                    }
                }
                routeInfoDto.setSendAddress(originalTransport.getStartDetailAdd()); // 设置发货地址
                String destProvinc = originalTransport.getDestProvinc();
                String destCity = originalTransport.getDestCity();
                String destArea = originalTransport.getDestArea();

                routeInfoDto.setReceiveProvince(ProvinceNameCompleter.completeProvinceName(destProvinc)); // 设置收货省

                tytCityDto = new TytCityDto();
                tytCityDto.setCityName(destCity);
                tytCityDto.setAreaName(destArea);
                tytCity = tytCityMapper.getCityDataByCondition(tytCityDto);
                if (Objects.nonNull(tytCity) && !municipalities.contains(destCity)) {
                    routeInfoDto.setReceiveCity(tytCity.getMapCityName()); // 设置收货市
                } else {
                    routeInfoDto.setReceiveCity(destCity); // 设置收货市
                }

                if (StringUtils.isNotBlank(destCity)
                        && StringUtils.isNotBlank(destArea)
                        && !destCity.equals(destArea)) {
                    if (Objects.nonNull(tytCity)) {
                        routeInfoDto.setReceiveArea(tytCity.getMapAreaName()); // 设置收货区
                    } else {
                        routeInfoDto.setReceiveArea(destArea); // 设置收货区
                    }
                }
                routeInfoDto.setReceiveAddress(originalTransport.getDestDetailAdd()); // 设置收货地址
                request.setRouteInfoDto(routeInfoDto);

                // 设置货物信息
                CreateWaybillThreeRequest.GoodsInfoDto goodsInfoDto = new CreateWaybillThreeRequest.GoodsInfoDto();
                goodsInfoDto.setGoodsCategory("1200"); // 设置货物品类为 1200机械、设备、电器
                goodsInfoDto.setGoodsUnit("TON"); // 设置货物计量单位为吨
                goodsInfoDto.setGoodsWeight(new BigDecimal(originalTransport.getWeight())); // 设置货物重量
                goodsInfoDto.setAmount(new BigDecimal(carriageFee)); // 设置运输金额
                goodsInfoDto.setGoodsName(originalTransport.getTaskContent()); // 设置货物品名
                request.setGoodsInfoDto(goodsInfoDto);

                // 设置项目信息
                CreateWaybillThreeRequest.ProjectStoreDto projectStoreDto = new CreateWaybillThreeRequest.ProjectStoreDto();
                projectStoreDto.setIsAddProject(false); // 设置新增项目为false
                projectStoreDto.setProjectName(projectName); // 设置项目名称 (用户中心获取)
                request.setProjectStoreDto(projectStoreDto);

                logger.info("开票-调用三方服务商创建运单接口请求参数：{}", JSON.toJSONString(request));
                Response<InternalWebResult<CreateWaybillThreeResponse>> response = invoiceOpenApiClient.addWaybillThree(request).execute();
                OrderOperateReasonVO oor = new OrderOperateReasonVO();
                oor.setType(101);
                oor.setOrderId(tytTransportOrders.getId());
                oor.setReason("【创建运单】:" + response.body().getMsg());
                oor.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
                oor.setMessageType(MqBaseMessageBean.ORDER_OPERATE_REASON);
                tytMqMessageService.addSaveMqMessage(oor.getMessageSerailNum(), JSON.toJSONString(oor), 1078);
                tytMqMessageService.sendMqMessage(oor.getMessageSerailNum(), JSON.toJSONString(oor), 1078);
                if (!response.isSuccessful()) {
                    //开票-调用三方服务商创建订单失败，则取消订单
                    transportOrdersService.unpaidCancel(tytTransportOrders.getPayUserId(), tytTransportOrders.getId());
                    logger.error("开票-调用三方创建运单接口错误，响应报文为：{}", JSON.toJSONString(response));
                    resultMsgBean.setCode(ReturnCodeConstant.INVOICE_CREATE_WAYBILL_THREE_ERROR);
                    resultMsgBean.setMsg("开票-调用三方创建运单接口失败，请返回后重试");
                    return resultMsgBean;
                }

                InternalWebResult<CreateWaybillThreeResponse> result = response.body();
                logger.info("开票-调用三方服务商创建运单接口返回参数：{}", JSON.toJSONString(result));
                if (Objects.nonNull(result)) {
                    String code = result.getCode();
                    String msg = result.getMsg();
                    //如果开票调用三方服务商返回成功
                    if ("10000".equals(code)) {
                        CreateWaybillThreeResponse createWaybillThreeResponse = (CreateWaybillThreeResponse) result.getData();
                        if (Objects.nonNull(createWaybillThreeResponse)) {
                            //运单ID
                            Integer waybillId = createWaybillThreeResponse.getWaybillId();
                            //运单号
                            String waybillCode = createWaybillThreeResponse.getWaybillCode();
                            //更新运单信息
                            tytTransportOrders.setInvoiceThirdPartyId(String.valueOf(waybillId));
                            tytTransportOrders.setInvoiceThirdPartyNo(waybillCode);
                            transportOrdersService.update(tytTransportOrders);
                            //发送钉钉消息mq
                            Integer dingDingSwitch = tytConfigService.getIntValue("trade_dingDing_customer_switch", 0);
                            if (Objects.nonNull(invoiceEnterpriseInfoVO) && 1 == dingDingSwitch) {
                                tytMqMessageService.sendDingDingMq(invoiceEnterpriseInfoVO.getEnterpriseName(),
                                        invoiceEnterpriseInfoVO.getCustomerManagerPhone(),
                                        tytTransportOrders.getPayUserId(),
                                        tytTransportOrders.getTsOrderNo());
                            }

                        }
                    } else {
                        //开票-调用三方服务商创建订单失败，则取消订单
                        transportOrdersService.unpaidCancel(tytTransportOrders.getPayUserId(), tytTransportOrders.getId());
                        logger.error("开票-调用三方创建运单接口错误，响应报文为：{}，错误信息为：{}", JSON.toJSONString(response), msg);
                        resultMsgBean.setCode(ReturnCodeConstant.INVOICE_CREATE_WAYBILL_THREE_ERROR);
                        resultMsgBean.setMsg(msg + "，请返回后重试");
                        return resultMsgBean;
                    }
                } else {
                    //开票-调用三方服务商创建订单失败，则取消订单
                    transportOrdersService.unpaidCancel(tytTransportOrders.getPayUserId(), tytTransportOrders.getId());
                    logger.error("开票-调用三方创建运单接口错误，响应报文为空");
                    resultMsgBean.setCode(ReturnCodeConstant.INVOICE_CREATE_WAYBILL_THREE_ERROR);
                    resultMsgBean.setMsg("开票-调用三方创建运单接口失败，请返回后重试");
                    return resultMsgBean;
                }
            }

            // 返回结果
            ToPayResultBean toPayResultBean = new ToPayResultBean();
            String infoFeeToPayRedirectUrlForServer = tytConfigService.getStringValue("infoFeeToPayRedirectUrlForServer");
            if (infoFeeToPayRedirectUrlForServer == null || infoFeeToPayRedirectUrlForServer.trim().equals("")) {
                infoFeeToPayRedirectUrlForServer = "http://www.teyuntong.cn/tytpc/tytpc/infoPayment/commonPay/getPaymentResult";
            }
            //卡券核销
            if (couponId != null) {
                couponService.addWriteOffCoupon(orderId, agencyMoney, coupon, couponId, payUser.getId());
            }
            toPayResultBean.setRedirectURL(infoFeeToPayRedirectUrlForServer);
            toPayResultBean.getRedirectParams().put("orderId", orderId);

            toPayResultBean.setPayAmount(BigDecimal.valueOf(payAmount).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
            resultMsgBean.setData(toPayResultBean);
            if (Objects.equals(AssignOrderTypeEnum.assign_order.getCode(), zeroAssignOrder) && Objects.nonNull(tytTransportOrders)) {
                transportOrdersService.addZeroAssignOrderLog(tytTransportOrders);
            }
            return resultMsgBean;

        } else {
            logger.info("支付运前信息费不允许支付,时间【{}】ms", System.currentTimeMillis());
            resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_NOT_ALLOWED_TO_PAY);
            resultMsgBean.setMsg(propertiesFileUtil.getString("tyt.infofee.mygoods.to.pay.error.5002"));
            return resultMsgBean;
        }

    }

    private String saveTechnicalService(Long tecServiceFee, Long userId, User payUser) {

        if (Objects.nonNull(tecServiceFee) && tecServiceFee > 0) {

            String technicalServiceNo = IdUtils.getIncreaseIdByLocalTime();
            TytTransportTechnicalOrder transportTechnicalOrder = new TytTransportTechnicalOrder();

            transportTechnicalOrder.setUserId(userId);
            transportTechnicalOrder.setTechnicalServiceNo(technicalServiceNo);
            transportTechnicalOrder.setPayUserId(payUser.getId());
            transportTechnicalOrder.setTechnicalServiceFee(tecServiceFee);
            transportTechnicalOrder.setCtime(new Date());

            transportTechnicalOrderMapper.insertSelective(transportTechnicalOrder);
            return technicalServiceNo;
        }

        return "";
    }

    //该方法可能已经弃用，请使用其他方法代替，如果你明确该方法是否弃用，请在此补充
    @Deprecated
    @Override
    public MqMoneyRefundMsg saveChangeOrderRefuseOrAgree(Long userId, Long[] payUserId, String transportNo, Integer operateType, Long srcMsgId) throws Exception {

        logger.warn("Deprecated_check_use ############# ");
        MqMoneyRefundMsg mqMoneyRefundMsg = null;
        logger.info("信息费拒绝/同意service开始时间{}", System.currentTimeMillis());
        // 获取第一个人的信息，如果是同意操作就是同意者的订单信息，否则，只是作为查询货物ID使用
        TytTransportOrders originalOrder = transportOrdersService.getTytTransportOrders(transportNo, "1", payUserId[0]);
        boolean isOfflineInfoFee = false;
        if (originalOrder == null) {
            //线下信息费
            logger.info("tsOrderNo:{}, payUserId: {}", transportNo, payUserId[0]);
            originalOrder = transportOrdersService.getTytOfflineTransportOrders(transportNo, payUserId[0]);
            if (originalOrder != null) {
                isOfflineInfoFee = true;
            }
//				originalOrder = transportOrdersService.getTytOfflineTransportOrders("18102100000025", 148368l);

            logger.info("originalOrder {}", originalOrder == null ? "null" : new Gson().toJson(originalOrder));
        }
        if (originalOrder == null) {
            logger.info("订单不存在" + payUserId + "," + transportNo);
            return null;
        }
        // tyt_plat_transport_optimize20171123 货源优化代码
        TransportMain transportMain = transportMainService.getById(srcMsgId);
        Transport oldTransport = new Transport();
        BeanUtils.copyProperties(transportMain, oldTransport);
        /* 要进行同意、拒绝操作，该运单需处于有人支付成功的状态 */
        // tyt_plat_transport_optimize20171123 货源优化代码
        TytTransportWaybill tytTransportWaybill = transportWayBillService.getById(transportNo);
        if (!isOfflineInfoFee) {
            //只有不是线下信息费订单，才判断
            if (tytTransportWaybill == null || !tytTransportWaybill.getInfoStatus().equals("1")) {
                logger.info("拒绝/同意操作失败,tytTransportWaybill【" + tytTransportWaybill + "】");
                return null;
            }
        }
        logger.info("信息费拒绝/同意service加锁结束时间{}", System.currentTimeMillis());
        /**
         * operateType:1拒绝装货 2同意装货
         * 拒绝的步骤:1修改订单表为拒绝;2查询订单表该运单号下支付成功的记录;3修改运单表;4修改货物表5退款;
         * 同意的步骤:1修改订单表为同意;2
         * 修改该运单下其他为拒绝;3修改运单表;4修改货物表5退款6将待支付的信息全部置为不显示，即rob_status设置为接单失败
         * （用户同意别人装货，对没有支付成功的支付信息的操作状态）
         */
        // 由于sort_id是唯一的，所以得一条一条的更新。(本人觉得这样比较糟糕)
        if (operateType == 1) {/* 拒绝装货 */
            logger.info("信息费拒绝/同意service-拒绝处理开始时间{}", System.currentTimeMillis());
            List<GoodsDetailOrderBean> refuseOrderBeans = transportOrdersService.getOrdersByPayUser(transportNo, payUserId, "1");
            if (refuseOrderBeans != null && refuseOrderBeans.size() > 0) {
                for (GoodsDetailOrderBean orderBean : refuseOrderBeans) {
                    commonRefuseMethod(orderBean.getId(), userId, orderBean.getCarOwnerUserId(), transportNo);
                }
                logger.info("信息费拒绝/同意service-拒绝处理退款开始时间{}", System.currentTimeMillis());
                // 调用退款接口
                mqMoneyRefundMsg = this.saveToRefund(refuseOrderBeans);
                logger.info("信息费拒绝/同意service-拒绝处理退款结束时间{}", System.currentTimeMillis());
                /* 查询其他支付成功的运单 */
                Long otherPayUserIdCounts = transportOrdersService.getExceptPartPayUserCount(userId, transportNo, "1", payUserId);
                logger.info("信息费拒绝/同意service-拒绝处理查询其他抢单成功的人的结束时间{}", System.currentTimeMillis());
                /* 改变运单表的车主信息:全部拒绝的时候,运单表车主信息为空,货物状态为待支付;部分拒绝时运单信息为最后支付成功着的信息 */
                if (otherPayUserIdCounts == null || otherPayUserIdCounts.longValue() < 1L) {
                    transportWayBillService.saveChangeCarOwnerInfo(null, transportNo, "0", null, null, null, null, 0);
                    // tyt_plat_transport_optimize20171123 货源优化代码
                    transportBusiness.saveChangeInfoStatus("0", srcMsgId);
                    // 货主的待同意气泡减一
                    tytBubbleService.updateBubbleNumber(userId, "2", "1", -1);
                } else {
                    transportWayBillService.saveChangePayNumber(transportNo, otherPayUserIdCounts.intValue());

                }

            }
            logger.info("信息费拒绝/同意service-拒绝处理结束时间{}", System.currentTimeMillis());
        } else if (operateType == 2) {/* 同意装货 */
            logger.info("信息费拒绝/同意service-同意处理开始时间{}", System.currentTimeMillis());
            Long beAgreedOrderId = originalOrder.getId();
            if (beAgreedOrderId != null && beAgreedOrderId.longValue() > 0) {
                commonAgreeMethod(beAgreedOrderId, userId, payUserId[0], transportNo, oldTransport, isOfflineInfoFee);
                // 查询剩余的下单成功订单并拒绝
                List<GoodsDetailOrderBean> waitLoadList = transportOrdersService.getExceptPartPayUser(userId, transportNo, "1", payUserId);
                for (GoodsDetailOrderBean order : waitLoadList) {
                    commonRefuseMethod(order.getId(), userId, order.getCarOwnerUserId(), transportNo);
                }
                logger.info("信息费拒绝/同意service-同意处理退款开始时间{}", System.currentTimeMillis());
                // 调用退款接口
                mqMoneyRefundMsg = this.saveToRefund(waitLoadList);
                logger.info("信息费拒绝/同意service-同意处理退款结束时间{}", System.currentTimeMillis());
                // 运单改为装货中.有多条下单成功的时候、只有这一条的时候（区别一下要不要修改运单的车主信息）
                if (waitLoadList != null && waitLoadList.size() > 0) {
                    transportWayBillService.saveChangeCarOwnerInfo(originalOrder.getPayUserId(), transportNo, "2", originalOrder.getPayCellPhone(), originalOrder.getPayLinkPhone(), originalOrder.getPayAmount(), originalOrder.getPayEndTime(), 1);

                } else {
                    transportWayBillService.saveChangeInfoStatus(userId, transportNo, "2");
                }
                logger.info("信息费拒绝/同意service-同意处理修改运单结束时间{}", System.currentTimeMillis());
                // 其他待支付的改成对用户不可见
                if (oldTransport.getIsInfoFee().equals("1")) {
                    transportOrdersService.saveChangeWaitToDisappear(userId, transportNo);
                }
                logger.info("信息费拒绝/同意service-同意处理修改待支付订单结束时间{}", System.currentTimeMillis());

            }
            logger.info("信息费拒绝/同意service-同意处理结束时间{}", System.currentTimeMillis());
        }

        if (isOfflineInfoFee) {
            //如果是线下信息费订单, 更新或增加运单表tyt_transport_waybill
            transportWayBillService.saveOffLineDealGoodsToTransportWayBill(oldTransport, "6");
        }
        return mqMoneyRefundMsg;
    }

    //该方法可能已经弃用，请使用其他方法代替，如果你明确该方法是否弃用，请在此补充
    @Deprecated
    @Override
    public void saveChangeOrderRefuseOrAgreeWithNewInfofee(Long userId, Long[] payUserId, String transportNo, Integer operateType, Long srcMsgId) throws Exception {
        {
            logger.warn("Deprecated_check_use ############# ");
            logger.info("信息费新版本拒绝/同意service开始时间{}", System.currentTimeMillis());
            // 获取第一个人的信息，如果是同意操作就是同意者的订单信息，否则，只是作为查询货物ID使用
            TytTransportOrders originalOrder = transportOrdersService.getNewTransportOrders(transportNo, payUserId[0], InfofeeStatusEnum.已支付);
            boolean isOfflineInfoFee = false;
            if (originalOrder == null) {
                //线下信息费
                logger.info("tsOrderNo:{}, payUserId: {}", transportNo, payUserId[0]);
                originalOrder = transportOrdersService.getTytOfflineTransportOrders(transportNo, payUserId[0]);
                if (originalOrder != null) {
                    isOfflineInfoFee = true;
                }
//				originalOrder = transportOrdersService.getTytOfflineTransportOrders("18102100000025", 148368l);

                logger.info("originalOrder {}", originalOrder == null ? "null" : new Gson().toJson(originalOrder));
            }
            if (originalOrder == null) {
                logger.info("订单不存在" + payUserId + "," + transportNo);
                return;
            }
            // tyt_plat_transport_optimize20171123 货源优化代码
            TransportMain transportMain = transportMainService.getById(srcMsgId);
            Transport oldTransport = new Transport();
            BeanUtils.copyProperties(transportMain, oldTransport);
            /* 要进行同意、拒绝操作，该运单需处于有人支付成功的状态 */
            // tyt_plat_transport_optimize20171123 货源优化代码
            TytTransportWaybill tytTransportWaybill = transportWayBillService.getById(transportNo);
            if (!isOfflineInfoFee) {
                //只有不是线下信息费订单，才判断
                if (tytTransportWaybill == null) {
                    logger.info("拒绝/同意操作失败,tytTransportWaybill【" + tytTransportWaybill + "】");
                    return;
                }
            }
            logger.info("信息费拒绝/同意service加锁结束时间{}", System.currentTimeMillis());
            /**
             * operateType:1拒绝装货 2同意装货
             * 拒绝的步骤:1修改订单表为拒绝;2查询订单表该运单号下支付成功的记录;3修改运单表;4修改货物表5退款;
             * 同意的步骤:1修改订单表为同意;2
             * 修改该运单下其他为拒绝;3修改运单表;4修改货物表5退款6将待支付的信息全部置为不显示，即rob_status设置为接单失败
             * （用户同意别人装货，对没有支付成功的支付信息的操作状态）
             */
            // 由于sort_id是唯一的，所以得一条一条的更新。(本人觉得这样比较糟糕)
            if (operateType == 2) {/* 同意装货 */
                logger.info("信息费拒绝/同意service-同意处理开始时间{}", System.currentTimeMillis());
                Long beAgreedOrderId = originalOrder.getId();
                if (beAgreedOrderId != null && beAgreedOrderId.longValue() > 0) {
                    commonAgreeMethod(beAgreedOrderId, userId, payUserId[0], transportNo, oldTransport, isOfflineInfoFee);
                    // 查询剩余的下单成功订单并拒绝
                    List<TytTransportOrders> waitLoadList = transportOrdersService.getOtherPaidOrders(userId, transportNo, payUserId);
                    for (TytTransportOrders order : waitLoadList) {
                        commonRefuseMethod(order.getId(), userId, order.getPayUserId(), transportNo);
                        logger.info("信息费新版本拒绝/同意service-同意处理退款开始时间{}, tsOrderNo {}, payUserId {}", System.currentTimeMillis(), order.getTsOrderNo(), order.getPayUserId());
                        // 调用退款接口
                        transportOrdersService.updateOrderForGiveBack(order, order.getPayAmount(), null);
                        logger.info("信息费新版本拒绝/同意service-同意处理退款结束时间{}, tsOrderNo {}, payUserId {}", System.currentTimeMillis(), order.getTsOrderNo(), order.getPayUserId());
                    }

                    // 运单改为装货中.有多条下单成功的时候、只有这一条的时候（区别一下要不要修改运单的车主信息）
                    if (waitLoadList != null && waitLoadList.size() > 0) {
                        transportWayBillService.saveChangeCarOwnerInfo(originalOrder.getPayUserId(), transportNo, "2", originalOrder.getPayCellPhone(), originalOrder.getPayLinkPhone(), originalOrder.getPayAmount(), originalOrder.getPayEndTime(), 1);

                    } else {
                        transportWayBillService.saveChangeInfoStatus(userId, transportNo, "2");
                    }
                    logger.info("信息费拒绝/同意service-同意处理修改运单结束时间{}", System.currentTimeMillis());
                    // 其他待支付的改成对用户不可见
                    if (oldTransport.getIsInfoFee().equals("1")) {
                        transportOrdersService.saveChangeWaitToDisappear(userId, transportNo);
                    }
                    logger.info("信息费拒绝/同意service-同意处理修改待支付订单结束时间{}", System.currentTimeMillis());

                }
                logger.info("信息费拒绝/同意service-同意处理结束时间{}", System.currentTimeMillis());
            }

            if (isOfflineInfoFee) {
                //如果是线下信息费订单, 更新或增加运单表tyt_transport_waybill
                transportWayBillService.saveOffLineDealGoodsToTransportWayBill(oldTransport, "6");
            }
        }
    }

    @Override
    public MqMoneyRefundMsg saveChangeOrderCancelForCar(Long userId, String transportNo, Integer operateType, Long srcMsgId) throws Exception {
        MqMoneyRefundMsg mqMoneyRefundMsg = null;
        logger.info("信息费车方取消service开始时间{}", System.currentTimeMillis());
        // 获取第一个人的信息，如果是同意操作就是同意者的订单信息，否则，只是作为查询货物ID使用
        TytTransportOrders originalOrder = transportOrdersService.getTytTransportOrders(transportNo, "1", userId);
        if (originalOrder == null) {
            logger.info("订单不存在" + userId + "," + transportNo);
            return null;
        }
        // tyt_plat_transport_optimize20171123 货源优化代码
        TransportMain transportMain = transportMainService.getById(srcMsgId);
        Transport oldTransport = new Transport();
        BeanUtils.copyProperties(transportMain, oldTransport);
        /* 要进行同意、拒绝操作，该运单需处于有人支付成功的状态 */
        // tyt_plat_transport_optimize20171123 货源优化代码
        TytTransportWaybill tytTransportWaybill = transportWayBillService.getById(transportNo);
        if (tytTransportWaybill == null || !tytTransportWaybill.getInfoStatus().equals("1")) {
            logger.info("车方取消操作失败,tytTransportWaybill【" + tytTransportWaybill + "】");
            return null;
        }
        logger.info("信息费车方取消service加锁结束时间{}", System.currentTimeMillis());
        /**
         * operateType:1车方取消装货
         * 拒绝的步骤:1修改订单表为拒绝;2查询订单表该运单号下支付成功的记录;3修改运单表;4修改货物表5退款;
         * 修改该运单下其他为拒绝;3修改运单表;4修改货物表5退款6将待支付的信息全部置为不显示，即rob_status设置为接单失败
         */
        // 由于sort_id是唯一的，所以得一条一条的更新。(本人觉得这样比较糟糕)
        if (operateType == 1) {/* 取消装货 */
            logger.info("信息费车方取消service-拒绝处理开始时间{}", System.currentTimeMillis());
            Long[] userIds = {userId};
            List<GoodsDetailOrderBean> refuseOrderBeans = transportOrdersService.getOrdersByPayUser(transportNo, userIds, "1");
            if (refuseOrderBeans != null && refuseOrderBeans.size() > 0) {
                for (GoodsDetailOrderBean orderBean : refuseOrderBeans) {
                    commonCarCancelMethod(orderBean.getId(), userId, orderBean.getCarOwnerUserId(), transportNo);
                }
                logger.info("信息费车方取消service-拒绝处理退款开始时间{}", System.currentTimeMillis());
                // 调用退款接口
                mqMoneyRefundMsg = this.saveToCancel(refuseOrderBeans);
                logger.info("信息费车方取消service-拒绝处理退款结束时间{}", System.currentTimeMillis());
                /**
                 * 更新tyt_car_owner_intention状态为退款=3
                 */
                carOwnerIntentionService.updateStatus(srcMsgId + "", userId + "");
                /*
                 * 查询其他支付成功的运单
                 * originalOrder.getUserId() 货方ID
                 * */
                List<GoodsDetailOrderBean> otherPayUsers = transportOrdersService.getExceptPartPayUser(originalOrder.getUserId(), transportNo, "1", userIds);
                Integer otherPayUserIdCounts = otherPayUsers.size(); // 优化：通过获取对象长度来取得size,替换之前查count的方法
                logger.info("信息费车方取消service-拒绝处理查询其他抢单成功的人的结束时间{}", System.currentTimeMillis());
                /* 改变运单表的车主信息:全部拒绝的时候,运单表车主信息为空,货物状态为待支付;部分拒绝时运单信息为最后支付成功着的信息 */
                if (otherPayUserIdCounts == null || otherPayUserIdCounts.intValue() < 1L) {
                    transportWayBillService.saveChangeCarOwnerInfo(null, transportNo, "0", null, null, null, null, 0);
                    // tyt_plat_transport_optimize20171123 货源优化代码
                    transportBusiness.saveChangeInfoStatus("0", srcMsgId);
                    // 货主的待同意气泡减一
                    tytBubbleService.updateBubbleNumber(originalOrder.getUserId(), "2", "1", -1);
                    // 更新transport_mt表状态等信息，全部拒绝时需要恢复数据的初始状态
                    transportMtService.updatePayNum(srcMsgId + "", 1);
                } else {
                    if (otherPayUserIdCounts == 1) { //只剩余1人付款时，将waybill表的金额更新为此人，用于显示货方单人支付金额
                        GoodsDetailOrderBean onePayUser = otherPayUsers.get(0);
                        transportWayBillService.updateWaybillOneInfo(onePayUser.getGoodsOrderNo(), onePayUser.getCarOwnerUserId(),
                                onePayUser.getCarOwnerRegisterPhone(), onePayUser.getCarOwnerTelephone(), onePayUser.getPayAgencyMoney());
                        // 更新transport_mt表状态等信息，只有1人付款，当前人信息更新至transport_mt表
                        transportMtService.updateAgreeInfo(onePayUser, srcMsgId + "");
                    } else {
                        // 更新transport_mt表状态等信息，多人付款时，只更新transport_mt表支付人数
                        transportMtService.updatePayNum(srcMsgId + "", otherPayUserIdCounts);
                    }
                    transportWayBillService.saveChangePayNumber(transportNo, otherPayUserIdCounts.intValue());
                }

            }
            logger.info("信息费车方取消service-拒绝处理结束时间{}", System.currentTimeMillis());
        }
        return mqMoneyRefundMsg;
    }

    /**
     * 调用退款接口
     *
     * @param refuseOrderBeans
     * @return
     * @throws Exception
     */
    @Override
    public MqMoneyRefundMsg saveToRefund(List<GoodsDetailOrderBean> refuseOrderBeans) throws Exception {

        List<String> orderIds = new ArrayList<String>();
        List<String> userIds = new ArrayList<String>();
        for (GoodsDetailOrderBean orderBean : refuseOrderBeans) {
            orderIds.add(orderBean.getId() + "");
            userIds.add(orderBean.getCarOwnerUserId() + "");
        }
        if (orderIds != null && orderIds.size() > 0) {
            MqMoneyRefundMsg mqMoneyRefundMsg = tytMqMessageService.addRefundMqMessage(orderIds, userIds);
            return mqMoneyRefundMsg;
        }
        return null;
    }

    @Override
    public MqMoneyRefundMsg saveToCancel(List<GoodsDetailOrderBean> refuseOrderBeans) throws Exception {

        List<String> orderIds = new ArrayList<String>();
        List<String> userIds = new ArrayList<String>();
        for (GoodsDetailOrderBean orderBean : refuseOrderBeans) {
            orderIds.add(orderBean.getId() + "");
            userIds.add(orderBean.getCarOwnerUserId() + "");
        }
        if (orderIds != null && orderIds.size() > 0) {
            /**
             * 车方取消退款类型为2
             */
            MqMoneyRefundMsg mqMoneyRefundMsg = tytMqMessageService.addRefundMqMessage(orderIds, userIds, 2);
            return mqMoneyRefundMsg;
        }
        return null;
    }

    /**
     * @param userId        用户ID
     * @param transportMain 运输信息表主表
     * @return
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    private SingleGoodsDetailBean getSingleGoodsDetail(Long userId, TransportMain transportMain, Integer detailType) throws IllegalAccessException, InvocationTargetException {
        SingleGoodsDetailBean detailBean = new SingleGoodsDetailBean();
        BeanUtils.copyProperties(transportMain, detailBean);
        if (StringUtils.isBlank(detailBean.getTyreExposedFlag())) {
            detailBean.setTyreExposedFlag("0");
        }
        //承运人 车辆信息
        if (transportMain.getStatus() == 4) {
            TransportDone transportDone = transportDoneService.getByTsId(transportMain.getId());
            if (Objects.nonNull(transportDone)) {
                detailBean.setHeadCity(transportDone.getHeadCity());
                detailBean.setHeadNo(transportDone.getHeadNo());
                detailBean.setTailCity(transportDone.getTailCity());
                detailBean.setTailNo(transportDone.getTailNo());
            }
        }
        detailBean.setPublishTime(transportMain.getCtime());
        detailBean.setTelephoneOne(transportMain.getTel());
        detailBean.setTelephoneThree(transportMain.getTel4());
        detailBean.setTelephoneTwo(transportMain.getTel3());
        detailBean.setFirstPublishTime(transportMain.getReleaseTime());
        detailBean.setDistance(transportMain.getDistance());
        detailBean.setAndroidDistance(null);
        detailBean.setIosDistance(null);
        detailBean.setCellPhone(transportMain.getUploadCellPhone());
        detailBean.setResendCounts(transportMain.getResendCounts());
        detailBean.setSourceType(transportMain.getSourceType());
        detailBean.initStartLatitudeStr();
        detailBean.initStartLongitudeStr();

        // 如果该用户前两个月没有发布过电议有价和一口价货源，提示限时体验标识
        Date publishTime = DateUtils.addMonths(new Date(), -2);
        List<String> priceList = transportMainService.selectOfPublishType(userId, publishTime);
        if (CollectionUtils.isEmpty(priceList)) {
            detailBean.setLimitTimeExperience(1);
        } else {
            priceList = priceList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(priceList)) {
                detailBean.setLimitTimeExperience(0);
            } else {
                detailBean.setLimitTimeExperience(1);
            }
        }

        //v6210新增信用分/信用分等级字段
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(transportMain.getUserId());
        if (null != userCreditInfo) {
            detailBean.setTotalScore(userCreditInfo.getTotalScore() == null ? new BigDecimal(0) : userCreditInfo.getTotalScore());
            detailBean.setRankLevel(userCreditInfo.getRankLevel() == null ? 1 : userCreditInfo.getRankLevel());
        }

        //V6250新增返回参数 订金类型（0不退还；1退还）
        detailBean.setRefundFlag(transportMain.getRefundFlag());

        // 获取货主的会员信息
        UserPermission userPermission = userPermissionService.getUserPermission(Long.valueOf(transportMain.getUserId()), UserPermission.PermissionTypeEnum.GOODS_MEMBER.getCode());
        if (null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus())) {
            detailBean.setGoodsPermissionMember(USER_TYPE_ONE);
        } else {
            detailBean.setGoodsPermissionMember(USER_TYPE_ZERO);
        }
        if (userId.equals(transportMain.getUserId())) {
            HashMap<String, String> telMap = backendTransportMapper.selectBackendPhoneByMsgId(transportMain.getSrcMsgId());
            if (telMap != null) {
                //替换为货主联系方式为 虚拟号
                detailBean.setBackendPhone(telMap.get("tel"));
                detailBean.setBackendPhone2(telMap.get("tel3"));
                String dialPhone = transportBackendAxbBinderService.getDialPhoneByUserId(userId);
                TytTransportBackendAxbBinder binder = transportBackendAxbBinderService.getBinderByAxB(telMap.get("tel"), dialPhone);
                if (null != binder) {
                    detailBean.setBackendPhone(binder.getMiddleNumber());
                }
                if (StringUtils.isNotBlank(telMap.get("tel3"))) {
                    TytTransportBackendAxbBinder binder2 = transportBackendAxbBinderService.getBinderByAxB(telMap.get("tel3"), dialPhone);
                    if (null != binder2) {
                        detailBean.setBackendPhone2(binder2.getMiddleNumber());
                    }
                }
                //第三个真实手机号先不显示
//                detailBean.setBackendPhone3(telMap.get("tel4"));
            }
            //该段代码是否可以去掉？
            DateTime dateTime = DateUtil.beginOfDay(new Date());
            if (detailBean.getPublishTime().compareTo(dateTime) < 0 && (4 != detailBean.getStatus() && 5 != detailBean.getStatus())) {
                detailBean.setStatus(0);
            }

            //我的货源详情查询订单数量
            Long srcMsgId = transportMain.getSrcMsgId();
            Long existOrderId = transportOrdersService.checkUsefulOrderExist(srcMsgId);
            detailBean.setOrderExist(existOrderId == null ? 0 : 1);
        } else {
            //车详情增加 是否被投诉标识
            ComplaintRecordBean complaintRecordBean = complaintRecordService.getComplaintRecordByMsgId(transportMain.getSrcMsgId(), userId);
            detailBean.setComplainedStatus(1);
            if (complaintRecordBean == null) {
                detailBean.setComplainedStatus(0);
            }

            //如果车主ID在 防飞单（隐藏货主信息）AB测试中，则将货源货主昵称修改为特运通老板
            List<String> abTestCode = new ArrayList<>();
            abTestCode.add("not_show_transport_user_data");
            TytAbtestConfigVo userType = abtestService.getUserTypeList(abTestCode, userId).get(0);
            if (userType.getType() == 1) {
                detailBean.setNickName("特运通老板");
            }
        }

        // 获取官方授权账号
        String authNameTea = tytOwnerAuthService.getAuthNameTea(transportMain.getAuthName());
        if (StringUtils.isNotBlank(authNameTea)) {
            detailBean.setAuthNameTea(authNameTea);
            detailBean.setAuthName(null);
        }

        // todo 优车设置技术服务费，这个地方暂时设置为0，待后期需求更新后调整
      /*  if (transportMain.getExcellentGoods() != null && transportMain.getExcellentGoods() == 1) {
            if (transportMain.getSourceType().equals(SourceTypeEnum.运满满货源.getId())) {
                detailBean.setTecServiceFee(transportMain.getTecServiceFee());
            } else {
                detailBean.setTecServiceFee(new BigDecimal("0"));
            }
        }*/
        detailBean.setTecServiceFee(transportMain.getTecServiceFee());
        detailBean.setExcellentGoods(transportMain.getExcellentGoods());
        detailBean.setMachineRemark(transportMain.getMachineRemark());
        detailBean.setExcellentCardId(transportMain.getExcellentCardId());
        detailBean.setInvoiceTransport(transportMain.getInvoiceTransport());
        detailBean.setAdditionalPrice(transportMain.getAdditionalPrice());
        detailBean.setEnterpriseTaxRate(transportMain.getEnterpriseTaxRate());

        // 增加是否是小程序货源或者后台货源字段
        TytTransportBackend tytTransportBackend = backendTransportMapper.selectStatusByMsgId(transportMain.getSrcMsgId());
        if (tytTransportBackend != null) {
            detailBean.setIsBackendTransport(1);
        }

        // 专车发货字段填充
        TytTransportMainExtend extendInfo = transportMainExtendMapper.getBySrcMsgId(transportMain.getSrcMsgId());
        if (extendInfo != null && extendInfo.getSeckillGoods() != null && extendInfo.getSeckillGoods() == 1) {
            boolean seckillGoodsIsLock = seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(transportMain.getSrcMsgId());
            detailBean.setHidePriceBox(seckillGoodsIsLock ? 1 : 0);
            detailBean.setSeckillGoods(extendInfo.getSeckillGoods());
        }

        detailBean.setDriverDriving(transportMain.getDriverDriving());
        detailBean.setLoadCellPhone(transportMain.getLoadCellPhone());
        detailBean.setUnloadCellPhone(transportMain.getUnloadCellPhone());
        if (extendInfo != null) {
            // 回价助手
            if (Objects.nonNull(extendInfo.getPriceCap())) {
                if (StringUtils.isBlank(detailBean.getPrice()) ||
                        new BigDecimal(detailBean.getPrice()).compareTo(new BigDecimal(extendInfo.getPriceCap())) < 0) {
                    Integer userType = abtestService.getUserType("publish_price_assistant", detailBean.getUserId());
                    if (Objects.equals(userType, 1)) {
                        detailBean.setPriceCap(extendInfo.getPriceCap());
                    }
                }
            }

            detailBean.setUseCarType(extendInfo.getUseCarType());
        }

        if (detailType == 2 && SourceTypeEnum.运满满货源.getId().equals(transportMain.getSourceType())) {
            /**
             *  YMM 货源编辑发布 查询最新 修改后数据组装
             */
            logger.info("[YMM-编辑发布] 回显数据处理之前，结果：{}", detailBean);
            Long detailId = detailBean.getId();
            TransportYmmListBean transportYMMListVO = tytTransportMbMergeMapper.selectBeforeVersionJson(transportMain.getSrcMsgId());
            TransportYmmSyncLogBean transportYMMSyncLogVO = tytTransportMbMergeMapper.selectLastVersionJson(transportYMMListVO.getCargoId());
            if (transportYMMListVO != null && transportYMMSyncLogVO != null) {
                //判断最新版本号是否大于已发布版本号
                TytMbCargoSyncInfo tytMbCargoSyncInfo = JSONObject.parseObject(transportYMMSyncLogVO.getInfoJson(), TytMbCargoSyncInfo.class);
                if (transportYMMSyncLogVO.getCargoVersion() > transportYMMListVO.getCargoVersion()) {
                    copyProperties(tytMbCargoSyncInfo, detailBean);
                }
                detailBean.setId(detailId);
                detailBean.setGiveGoodsPhone(tytMbCargoSyncInfo.getContactTelephone());
                detailBean.setCargoId(transportYMMListVO.getCargoId());
                detailBean.setCargoVersion(transportYMMSyncLogVO.getCargoVersion() + "");
                logger.info("[YMM-编辑发布] 回显数据处理完成后，结果：{}", detailBean);
            }
        }

        //专车货源是否大厅抢单字段
        if (detailBean.getExcellentGoods() != null && detailBean.getExcellentGoods() == 2) {
            TytSpecialCarDispatchFailure tytSpecialCarDispatchFailure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(transportMain.getSrcMsgId());
            if (tytSpecialCarDispatchFailure != null) {
                detailBean.setDeclareInPublic(tytSpecialCarDispatchFailure.getDeclareInPublic());
            }
        }

        //无价货源是否存在有价的相似货源
        try {
            if (StringUtils.isBlank(detailBean.getPrice()) || "0".equals(detailBean.getPrice())) {
                Response<Boolean> execute = thPriceClient.getSimilarityTransportHavePriceCount(transportMain.getSimilarityCode()).execute();
                Boolean havePriceCount = execute.body();
                if (Boolean.TRUE.equals(havePriceCount)) {
                    detailBean.setSimilarityTransportHavePrice(true);
                }
            }
        } catch (Exception e) {
            logger.info("获取无价货源是否存在有价的相似货源失败 原因", e);
        }

        // 获取分段支付字段
        TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(transportMain.getId());
        if (transportEnterpriseLog != null) {
            detailBean.setPaymentsType(transportEnterpriseLog.getPaymentsType());
            if (Objects.equals(transportEnterpriseLog.getPaymentsType(), YesOrNoEnum.Y.getCode())) {
                detailBean.setPrepaidPrice(transportEnterpriseLog.getPrepaidPrice());
                detailBean.setCollectedPrice(transportEnterpriseLog.getCollectedPrice());
                detailBean.setReceiptPrice(transportEnterpriseLog.getReceiptPrice());
            }
        }
        BenefitLabelVO labelVO  = getBenefitLabel(transportMain.getSrcMsgId());
        if  (labelVO != null) {
            detailBean.setBenefitLabel(labelVO.getLabel());
            detailBean.setBenefitLabelCode(labelVO.getCode());
        }
        return detailBean;
    }

    public BenefitLabelVO getBenefitLabel(Long srcMsgId) {
        try {
            Response<BenefitLabelVO> response = transportSearchClient.getTransportBenefitLabel(srcMsgId).execute();
            if (response.isSuccessful()) {
                logger.info("获取货源利益点标签,srcMsgId:{},返回结果:{}", srcMsgId, JSON.toJSONString(response.body()));
                return response.body();
            }
        } catch (IOException e) {
            logger.info("获取货源利益点标签失败，", e);
        }
        return null;
    }


    /**
     * 信息费获取某信息收藏状态
     *
     * @param goodsId :货物ID
     * @param userId  :用户ID
     * @return
     * @throws Exception
     */
    private SingleGoodsCollectBean getSingleGoodsCollect(Long goodsId, Long userId) throws Exception {
        SingleGoodsCollectBean collectBean = null;
        TransportCollect collect = collectService.isExit(userId, goodsId);
        if (collect == null) {
            collectBean = new SingleGoodsCollectBean(0, null);
        } else {
            collectBean = new SingleGoodsCollectBean(1, collect.getId());
        }
        return collectBean;
    }

    /**
     * 用户对某条货物信息的联系电话标注情况
     *
     * @param goodsId :货物信息ID
     * @param userId  :用户ID
     * @return
     */
    private String getSingleGoodsCallLog(Long goodsId, Long userId) {
        /* 查看用户是否电话标注过该货物 */
        CallLogBean callLogBean = transportMainService.getCallLog(userId, goodsId);
        if (callLogBean == null) {
            return "1_0";
        } else {
            return "2_" + callLogBean.getCallStatus();
        }
    }

    private void resetMatchItem(SingleGoodsDetailBean detailBean) {
        Integer matchItemId = detailBean.getMatchItemId();
        if (matchItemId != null && matchItemId == Constant.back_match_item_id) {
            detailBean.setIsStandard(1);
            detailBean.setMatchItemId(-1);
        }
    }

    private String getAssureTipByRefund(Integer refundFlag) {

        // 6500李孟然的需求要求所有货源都改为同一个文案
//		String assureTip = "该货源收取订货订金，您装货后支付给货方。";
        String assureTip = "若线下交易订金，所有费用平台不负责追偿。";

        //订金类型（0不退还；1退还）
//		if(refundFlag != null && refundFlag == 1){
//			assureTip = "该货源收取订货押金，货方收货后退还给您。";
//		}

        return assureTip;
    }

    @Override
    public GoodsSingleDetailResultBean getGoodsInfoDetail(Long goodsId, Long userId, Integer detailType) throws Exception {
        GoodsSingleDetailResultBean result = new GoodsSingleDetailResultBean();
        //服务器时间
        result.setTime(new Date());
        //货源详情页风险提示
        result.setRiskTip(tytConfigService.getStringValue("risk_tip"));

        //货源详情页担保提示链接
        result.setAssureTipLink(tytConfigService.getStringValue("assure_tip_link"));
        //获取运输信息表基本信息
        TransportMain transportMain = transportMainService.getTransportMainForId(goodsId);
        if (transportMain == null) {
            return null;
        }

        try {
            result.setCoverGoodsDialInfo(getCoverGoodsDialInfo(userId, transportMain));
        } catch (Exception e) {
            logger.error("货源捂货信息获取失败, userId: {}, tsId: {}", userId, goodsId, e);
        }

        //货源详情页担保提示
        result.setAssureTip(this.getAssureTipByRefund(transportMain.getRefundFlag()));
        // 显示超额保障提示文案，非超额保障返回空。已点击货源返回空。
        result.setExcessCoverageTip(getGoodsDetailExcessCoverageTip(userId, transportMain));

        // 设置提示文案
        result.setTipInfoCollective(getTipInfoCollective(userId, transportMain));
        // 设置保障文案
        result.setExcessCoveragePrompt(getExcessCoveragePrompt(transportMain));

        //获取货物信息,收藏信息,标注信息
        SingleGoodsDetailBean detailBean = getSingleGoodsDetail(userId, transportMain, detailType);

        this.resetMatchItem(detailBean);

        Transport transport = transportService.getLastBySrcMygId(transportMain.getSrcMsgId());
        detailBean.setPublishTimeNew((null == transport || null == transport.getPubDate()) ? detailBean.getFirstPublishTime() : transport.getCtime());
        // 获取当前用户是否收藏过此货源
        TransportCollect transportCollect = transportCollectService.isExit(userId, goodsId);
        if (null != transportCollect && null != transportCollect.getId()) {
            result.setCollectId(transportCollect.getId());
        }
        detailBean.setIsAutoResend(autoResendService.isAutoResend(transportMain.getSrcMsgId()) ? 1 : 0);

        //设置信息费货物详
        result.setDetailBean(detailBean);
        //获取货源用户相关信息 (平台交易次数 合作次数 查看次数 联系人数等)
        TransportUserBean userBean = getUserBeanDetail(userId, detailBean.getUserId(), detailBean.getSrcMsgId());
        if (userBean != null) {
            // 获取好评率
            User user = userService.getByUserId(userId);
            // 30天保护期
            if (DateUtils.addDays(user.getCtime() == null ? new Date() : user.getCtime(), 30).before(new Date())) {
                GetUserFeedbackRatingVO userFeedBackRating = feedbackUserService.getUserFeedBackRating(userId, 2);
                if (userFeedBackRating.getTotal() > 0) {
                    if (userFeedBackRating.getTotal() >= 3) {
                        userBean.setFeedbackPositiveRating("好评率: " + userFeedBackRating.getRating() + "%");
                    } else if (userFeedBackRating.getTotal() < 3 && userFeedBackRating.getPositive() > 0) {
                        userBean.setFeedbackPositiveRating("近期有好评");
                    } else {
                        userBean.setFeedbackPositiveRating("近期无好评");
                    }
                } else {
                    userBean.setFeedbackPositiveRating("暂无评价");
                }
            }

            //YMM货源不展示货主真实姓名，因为YMM货源的货主真实姓名是调度的
            if (Objects.equals(transportMain.getSourceType(), SourceTypeEnum.运满满货源.getId())) {
                userBean.setUserShowName("");
            }

            result.setTransportUserBean(userBean);
        }

        // 是否显示价格弹框： 当前用户为货主 & 货源有进线（联系）且货源为无价货源 & 每个货源每天最多自动弹一次
        boolean isShowPriceBox = false;
        if (transportMain.getUserId().equals(userId) && transportMain.getStatus().equals(1)
                && transportMain.getCtime().after(DateUtil.beginOfDay(new Date()))
                && (StringUtils.isBlank(transportMain.getPrice()) || new BigDecimal(transportMain.getPrice()).compareTo(new BigDecimal(0)) == 0)
                && !"0".equals(userBean.getContactCount())) {
            Object obj = RedisUtil.getObject("tyt:plat:detail:popUpPrice:" + transportMain.getSrcMsgId());
            if (obj == null) {
                isShowPriceBox = true;
                RedisUtil.setObject("tyt:plat:detail:popUpPrice:" + transportMain.getSrcMsgId(), 1, 86400);
            }
        }
        result.setIsPopupPriceBox(isShowPriceBox ? 1 : 0);

        //重发后原信息Id
        Long srcMsgId = detailBean.getSrcMsgId();
        // 过期的信息只显示货源相关的东西
        int compareTime = TimeUtil.formatDateTime(detailBean.getPublishTime()).compareTo(TimeUtil.formatDate(new Date()));
        // ①:不需要信息费的直接返回  ②:需要信息费但信息费状态为线下成交的也直接返回
        // ③:无效的、撤销的信息只显示货源相关的东西也直接返回 ④:过期的信息只显示货源相关的东西也直接返回
        if (detailBean.getIsInfoFee().equals("0") ||
                (detailBean.getIsInfoFee().equals("1") && detailBean.getInfoStatus().equals("6")) ||
                (detailBean.getStatus() == 5 || detailBean.getStatus() == 0) ||
                (compareTime < 0 && detailBean.getStatus() == 1)) {
            //v6130pc 详情需要展示货源状态
            if ((compareTime < 0 && detailBean.getStatus() == 1)) {
                detailBean.setStatus(0);
            }
            return result;
        }
        String hasMakeOrder = "0";// 是否下过订单：0否、1是
        String transportNo = detailBean.getTsOrderNo();
        Integer isPaySuccess = 0;// 默认未支付
        // 判断详情按发货方的走还是按车主方的走
        if (userId.longValue() != detailBean.getUserId().longValue()) {
            detailType = 3;
        } else {
            detailType = 2;
        }
        logger.info("getSingleDetail service userId is: " + userId + ", detailType is: " + detailType);
        if (detailType == 2) {
            // 信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成
            // 5异常上报6线下成交
            switch (detailBean.getInfoStatus()) {
                case "0":
                    break;
                case "1":
                    result.setAgencyMoneyList(transportOrdersService.getByUser(userId, transportNo, "1"));
                    break;
                case "2":
                    result.setAgencyMoneyList(transportWayBillService.getOrderLoadingInfo(transportNo));
                    break;
                case "3":
                case "4":
                    result.setAgencyMoneyList(transportWayBillService.getOrderFinishedInfo(transportNo));
                    break;
                case "5":
                    result.setAgencyMoneyList(transportWayBillService.getOrderLoadingInfo(transportNo));
                    break;
            }

        } else if (detailType == 3) {
            List<PayUserOrderBean> myOrders = transportOrdersService.getByPayUserOnce(userId, transportNo);

            List<PayUserOrderBean> agencyList = new ArrayList<PayUserOrderBean>();
            // 判断用户是否可以继续支付:被拒绝后也可以继续支付
            String payStatus = "0";
            String robStatus = "-100";// 随便给一数据库没有额初始值
            if (myOrders == null || myOrders.size() <= 0) {
                isPaySuccess = 0;
            } else {
                hasMakeOrder = "1";
                agencyList.add(myOrders.get(0));
                payStatus = myOrders.get(0).getPayStatus();
                robStatus = myOrders.get(0).getRobStatus();
                logger.info("getSingleDetail service payStatus is: " + payStatus + ", robStatus is: " + robStatus);
                if (payStatus.equals("2") && !robStatus.equals("2") && !robStatus.equals("3") && !robStatus.equals("12")) {
                    isPaySuccess = 1;
                } else {
                    isPaySuccess = 0;
                }
            }
            // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款
            // 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
            switch (robStatus) {
                case "-100":
                case "0":
                case "10":
                case "11":
                    break;
                case "1":
                    // 新增显示已支付信息费
                    result.setAgencyMoneyList(transportOrdersService.getOrdersByPayUser(transportNo, new Long[]{userId}, "1"));
                    break;
                case "2":
                case "3":
                case "8":
                case "9":
                    result.setAgencyMoneyList(agencyList);
                    break;
                case "4":
                    result.setAgencyMoneyList(agencyList);
                    break;
                case "5":
                case "6":
                    result.setAgencyMoneyList(agencyList);
                    break;
                case "7":
                    result.setAgencyMoneyList(agencyList);
                    break;

            }

        }

        // 是否是参与现金奖活动的货源，有AB测
        if (marketingActivityService.userIsValidByType(ActivityTypeEnum.car_gtv.getId(), userId)) {
            logger.info("校验是否是参与现金奖活动货源,在活动中，userid:{}", userId);

            boolean cashPrizeActivityTransport = transportMainService.isCashPrizeActivityTransport(transportMain);
            result.setIsCashPrizeActivityGoods(cashPrizeActivityTransport ? 1 : 0);
        }
        if(!Objects.equals(result.getIsCashPrizeActivityGoods(),1)){
            // 是否显示送订金券标签
            result.setShowDepositCouponsLabel(getShowDepositCouponsLabel(transportMain));
        }
        logger.info("货源详情返回值：{}", JSONUtil.toJsonStr(result));

        result.setIsPaySuccess(isPaySuccess);
        result.setHasMakeOrder(hasMakeOrder);
        return result;
    }

    /**
     * 详情页设置提示信息
     */
    private GoodsSingleDetailResultBean.TipInfoCollective getTipInfoCollective(Long userId, TransportMain transportMain) {
        GoodsSingleDetailResultBean.TipInfoCollective tipInfoCollective = new GoodsSingleDetailResultBean.TipInfoCollective();
        // 顶部文案，规则：晚上20点-24点展示
        tipInfoCollective.setTopPageTip(DateUtil.hour(new Date(), true) >= 20 ? "夜间车辆较少，请耐心等待" : "");

        // 中部显示装卸货电话按钮：条件：专车货源 & 未成交 & 开关开启 & 货源有装卸货联系电话
        if (Objects.equals(transportMain.getExcellentGoods(), 2)
                && Objects.equals(transportMain.getStatus(), 1)
                && Objects.equals(tytConfigService.getIntValue("detail_show_load_phone_btn"), 1)
                && (StringUtils.isNotBlank(transportMain.getLoadCellPhone())
                || StringUtils.isNotBlank(transportMain.getUnloadCellPhone()))
        ) {
            tipInfoCollective.setMiddlePageTip("可联系具体装卸货人，避免无法承运");
        }

        ExposureCardGiveawayRecordDO queryGiveawayRecord = new ExposureCardGiveawayRecordDO();
        queryGiveawayRecord.setStatus(1);
        queryGiveawayRecord.setSrcMsgId(transportMain.getSrcMsgId());
        List<ExposureCardGiveawayRecordDO> recordList = exposureCardGiveawayRecordMapper.select(queryGiveawayRecord);
        if (CollectionUtils.isNotEmpty(recordList)) {
            Integer giveawayNum = recordList.get(0).getGiveawayNum();
            tipInfoCollective.setExposureContent("线上成交额外送" + giveawayNum + "张曝光卡");
            if (RedisUtil.get("tyt:goods:detail:exposure:toast:" + transportMain.getSrcMsgId()) == null) {
                tipInfoCollective.setToastType(1);
                tipInfoCollective.setToastTitle("该货源线上成交");
                tipInfoCollective.setGiveawayNum(giveawayNum);
                RedisUtil.set("tyt:goods:detail:exposure:toast:" + transportMain.getSrcMsgId(), "1", 86400);
            }
        }

        String lastFeedback = callFeedbackLogMapper.getLastFeedback(userId, transportMain.getSrcMsgId());
        tipInfoCollective.setLastCallFeedBack(lastFeedback == null ? null : "上次沟通结果：" + lastFeedback);

        return tipInfoCollective;
    }

    public GoodsSingleDetailResultBean.CoverGoodsDialInfo getCoverGoodsDialInfo(Long userId,
                                                                                TransportMain transportMain) {
        PreConditions.check(transportMain != null, ReturnCodeConstant.ERROR, "货源不存在");
        return coverGoodsDialConfigService.coverGoodsVersion4(userId, transportMain);
    }

    /**
     * 运满满货源信息与特运通信息对应处理
     *
     * @param tytMbCargoSyncInfo
     * @param detailBean
     */
    private void copyProperties(TytMbCargoSyncInfo tytMbCargoSyncInfo, SingleGoodsDetailBean detailBean) {
        BeanUtils.copyProperties(tytMbCargoSyncInfo, detailBean);

        detailBean.setLoadingTime(tytMbCargoSyncInfo.getLoadTime());
        detailBean.setGiveGoodsPhone(tytMbCargoSyncInfo.getContactTelephone());
        detailBean.setWeight(String.valueOf(tytMbCargoSyncInfo.getMaxWeight() != 0 ? tytMbCargoSyncInfo.getMaxWeight() : tytMbCargoSyncInfo.getMinWeight()));
        detailBean.setHigh(String.valueOf(tytMbCargoSyncInfo.getMinHeight()));
        detailBean.setWide(String.valueOf(tytMbCargoSyncInfo.getMinWidth()));
        detailBean.setLength(String.valueOf(tytMbCargoSyncInfo.getMinLength()));
        detailBean.setInfoFee(BigDecimal.valueOf(tytMbCargoSyncInfo.getDepositAmt() / 100));
        detailBean.setPrice(String.valueOf(tytMbCargoSyncInfo.getExpectFreight() / 100));
        detailBean.setTaskContent(String.valueOf(tytMbCargoSyncInfo.getCargoName()));
        detailBean.setShuntingQuantity(tytMbCargoSyncInfo.getTruckCount());

        detailBean.setStartProvinc(tytMbCargoSyncInfo.getLoadProvinceName().replace("省", "").replace("市", ""));
        detailBean.setStartCity(tytMbCargoSyncInfo.getLoadCityName());
        detailBean.setStartArea(tytMbCargoSyncInfo.getLoadDistrictName());
        detailBean.setStartDetailAdd(tytMbCargoSyncInfo.getLoadDetailAddress());
        detailBean.setStartLongitude(String.valueOf(tytMbCargoSyncInfo.getLoadLon()));
        detailBean.setStartLatitude(String.valueOf(tytMbCargoSyncInfo.getLoadLat()));

        detailBean.setDestProvinc(tytMbCargoSyncInfo.getUnloadProvinceName().replace("省", "").replace("市", ""));
        detailBean.setDestCity(tytMbCargoSyncInfo.getUnloadCityName());
        detailBean.setDestArea(tytMbCargoSyncInfo.getUnloadDistrictName());
        detailBean.setDestDetailAdd(tytMbCargoSyncInfo.getUnloadDetailAddress());
        detailBean.setDestLongitude(String.valueOf(tytMbCargoSyncInfo.getUnloadLon()));
        detailBean.setDestLatitude(String.valueOf(tytMbCargoSyncInfo.getUnloadLat()));
        detailBean.setRefundFlag(tytMbCargoSyncInfo.getDepositReturn());

        detailBean.setExcellentGoods(YesOrNoEnum.Y.getCode());
        detailBean.setPublishType(tytMbCargoSyncInfo.getDealMode() == 1 ? 2 : 1);


        /**
         * 挂车样式
         */
        JSONArray jsonArray = JSONArray.parseArray(tytMbCargoSyncInfo.getTruckTypes());
        List<Map> list = JSONArray.parseArray(jsonArray.toJSONString(), Map.class);
        List<String> carStyles = new ArrayList<>();
        list.stream().forEach(item -> {
            if (CarStyleEnum.FLAT_BOARD.getStyleName().equals(item.get("msg"))) {
                carStyles.add(CarStyleEnum.FLAT.getStyleName());
            } else if (CarStyleEnum.HIGH_LOW_BOARD.getStyleName().equals(item.get("msg"))) {
                carStyles.add(CarStyleEnum.HIGH.getStyleName());
            } else if (Objects.nonNull(item.get("msg")) && String.valueOf(item.get("msg")).contains(CarStyleEnum.LADDER.getStyleName())) {
                carStyles.add(CarStyleEnum.FLAT.getStyleName());
                carStyles.add(CarStyleEnum.HIGH.getStyleName());
                detailBean.setClimb("1");
            }
        });
        detailBean.setCarStyle(org.apache.commons.lang3.StringUtils.join(carStyles, "/"));

        /**
         * 所需车辆长度
         */
        JSONArray truckLengths = JSONArray.parseArray(tytMbCargoSyncInfo.getTruckLengths());
        List<String> array = JSONArray.parseArray(truckLengths.toJSONString(), String.class);
        detailBean.setCarLengthLabels(org.apache.commons.lang3.StringUtils.join(array, ","));
    }

    @Override
    public SingleGoodsDetailBean getSingleGoodsDetailBean(Long userId, TransportMain transportMain) throws InvocationTargetException, IllegalAccessException {
        return getSingleGoodsDetail(userId, transportMain, null);
    }

    /**
     * 获取货物用户基础信息
     *
     * @param userId
     * @param detailUserId 货物发布人UserId
     * @param srcMsgId     货源Id
     * @return TransportUserBean
     * @throws Exception
     */
    @Override
    public TransportUserBean getUserBeanDetail(Long userId, Long detailUserId, Long srcMsgId) throws Exception {
        TransportUserBean userBean = new TransportUserBean();
        User user = userService.getByUserId(detailUserId);
        if (user != null) {
            userBean.setUserId(user.getId());
            Integer identityType = user.getIdentityType();
            //2018-04-12app货源详情页身份改为销售审核一级身份
            String deliverTypeOne = user.getDeliver_type_one();
            userBean.setDeliverTypeOneCode(deliverTypeOne);
            TytSource sourceName = TytSourceUtil.getSourceName("user_deliver_type_one", deliverTypeOne);
            if (sourceName != null) {
                if (sourceName.getName().equals("待定") || sourceName.getName().equals("内部员工") ||
                        sourceName.getName().equals("无效") || sourceName.getName().equals("未跟踪")) {
                    userBean.setDeliverTypeOneName("待定");
                } else {
                    userBean.setDeliverTypeOneName(sourceName.getName());
                }
            }
            Integer userClass = user.getUserClass();
            if (user.getUserClass() == null) {
                userClass = tytConfigService.getIntValue(ArgumentsNamePropertiesUtil.getStringValue("user.identity.auth.user_class.dafault.name"));
            }
            userBean.setUserClassCode(userClass);
            TytSource source = TytSourceUtil.getSourceName("user_class", userClass + "");
            if (source != null) {
                userBean.setUserClassName(source.getName());
            }
            if (identityType == null) {
                // 获取默认值
                identityType = tytConfigService.getIntValue(ArgumentsNamePropertiesUtil.getStringValue("user.identity.auth.identity_type.dafault.name"));
            }
            userBean.setIdentityTypeCode(identityType);

            TytSource source2 = TytSourceUtil.getSourceName("user_class", userClass + "", identityType + "");
            if (source2 != null) {
                userBean.setIdentityTypeName(source2.getName());
            }
            // 增加货源详情显示 交易次数和合作次数
            //信用信息
            List<CreditUserInfo> driverCreditUserInfoList = infofeeDetailMapper.queryCreditUserInfoForCar(detailUserId.toString(), userId.toString());
            if (CollectionUtils.isNotEmpty(driverCreditUserInfoList)) {
                CreditUserInfo creditUserInfo = driverCreditUserInfoList.get(0);
                userBean.setCoopNums(creditUserInfo.getCoopNums());
                userBean.setTradeNums(creditUserInfo.getTradeNums());
            }
            // 获取货源查看和联系次数
            PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货源拨打量显示, userId);
            if (permissionResult.getUse()) { // 有查看货源拨打量时展示
                Map<String, String> finalResult = getViewAndContactCount(srcMsgId);
                String viewCount = finalResult.get(Constant.VIEW_COUNT_HASH_KEY + srcMsgId);
                String contactCount = finalResult.get(Constant.CONTACT_COUNT_HASH_KEY + srcMsgId);
                userBean.setViewCount(viewCount);
                userBean.setContactCount(contactCount);
            } else {
                userBean.setViewCount("0");
                userBean.setContactCount("0");
            }
            // 是否是小套餐会员
            UserPermission userPermission = userPermissionService.getUserPermission(userId, UserPermission.PermissionTypeEnum.CAR_SMALL_MEMBER.getCode());
            if (Objects.nonNull(userPermission) && Objects.equals(userPermission.getStatus(), 1) &&
                    Objects.nonNull(userPermission.getEndTime()) && userPermission.getEndTime().after(new Date()) &&
                    Objects.nonNull(userPermission.getTotalNum()) && Objects.nonNull(userPermission.getUsedNum()) &&
                    userPermission.getUsedNum() < userPermission.getTotalNum()) {
                userBean.setSmallMealVip(YesOrNoEnum.Y.getCode());
            }

            //2019-11-04 xyy 详情增加信任分和评级
            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(detailUserId);
            if (userCreditInfo != null) {
                if (StringUtils.isNotBlank(userCreditInfo.getTransportScore())) {
                    userBean.setTransportScore(userCreditInfo.getTransportScore());
                }
                if (StringUtils.isNotBlank(userCreditInfo.getTsRank())) {
                    userBean.setTsRank(userCreditInfo.getTsRank());
                }
                // 添加车主等级
                userBean.setCarCreditRankLevel(userCreditInfo.getCarCreditRankLevel());

                // 添加货主等级
                userBean.setRankLevel(userCreditInfo.getRankLevel());
            }


            // 增加用户名称显示
            String userShowName = "";
            if (StringUtils.isNotBlank(user.getIdCard()) && StringUtils.isNotBlank(user.getTrueName())) {
                userShowName = user.getTrueName().charAt(0) + IdCardUtil.getCallGender(user.getIdCard());
            } else {
                userShowName = user.getUserName();
            }
            userBean.setUserShowName(userShowName);
            userBean.setSex(user.getSex());

            //用户认证状态,未认证通过的名称"-"标识
            if (null != user.getVerifyFlag() && user.getVerifyFlag().intValue() == 1) {
                userBean.setAuthed("1");
            } else {
                userBean.setUserShowName("--");
            }

            //企业名称
            TytInvoiceEnterprise dto = new TytInvoiceEnterprise();
            dto.setCertigierUserId(detailUserId);
            TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectOne(dto);

            if (Objects.nonNull(tytInvoiceEnterprise)) {
                userBean.setEnterpriseName(tytInvoiceEnterprise.getEnterpriseName());
            }

        }
        return userBean;
    }


    /**
     * 获取货源查看和联系次数
     *
     * @param srcMsgId
     * @return
     */
    public Map<String, String> getViewAndContactCount(Long srcMsgId) {
        String today = TimeUtil.formatDate(TimeUtil.today());
        List<String> mapKeys = new ArrayList<>();
        mapKeys.add(Constant.VIEW_COUNT_HASH_KEY + srcMsgId);
        mapKeys.add(Constant.CONTACT_COUNT_HASH_KEY + srcMsgId);
        String[] fileds = mapKeys.toArray(new String[]{}); // 转换String数组
        // redis获取数据结果
        List<String> mapResult = RedisUtil.getMMapValue(Constant.PLAT_COUNT_TRANSPORT_KEY + today, fileds);
        Map<String, String> finalResult = new HashMap<>();
        for (int i = 0; i < fileds.length; i++) { // 遍历keys设置redis取出的值，目的匹配userBean的数字属性
            finalResult.put(fileds[i], mapResult.get(i));
        }
        String viewCount = finalResult.get(Constant.VIEW_COUNT_HASH_KEY + srcMsgId);
        if (StringUtils.isBlank(viewCount)) {
            finalResult.put(Constant.VIEW_COUNT_HASH_KEY + srcMsgId, "0");
        }
        String contactCount = finalResult.get(Constant.CONTACT_COUNT_HASH_KEY + srcMsgId);
        if (StringUtils.isBlank(contactCount)) {
            finalResult.put(Constant.CONTACT_COUNT_HASH_KEY + srcMsgId, "0");
        }
        return finalResult;
    }

    private OriginalStandardMsg fetchOriginalStandardMsg(Integer matchItemId) throws IllegalAccessException, InvocationTargetException {
        OriginalStandardMsg originalStandardMsg = null;

        if (matchItemId.intValue() < 10000) {
            TytMachineType machineType = machineTypeService.getById(Long.valueOf(matchItemId));

            if (machineType != null) {
                originalStandardMsg = new OriginalStandardMsg();
                BeanUtils.copyProperties(machineType, originalStandardMsg);
            }
        } else {
            TytMachineTypeNew machineType = machineTypeNewService.getById(Long.valueOf(matchItemId));

            if (machineType != null) {
                originalStandardMsg = new OriginalStandardMsg();
                BeanUtils.copyProperties(machineType, originalStandardMsg);
                originalStandardMsg.setDisplayContent(machineType.getBrandType());
            }

        }
        return originalStandardMsg;
    }

    private void commonRefuseMethod(Long orderId, Long userId, Long payUserId, String transportNo) throws Exception {
        // 修改订单的状态为拒绝
        int c = transportOrdersService.saveChangeRobStatusById(orderId, "2");
        if (c > 0) {
            // 车主的拒绝/退费气泡加1
            tytBubbleService.updateBubbleNumber(payUserId, "1", "4", 1);
            // 发送弹窗通知
            tytNoticeRemindService.saveNoticeRemind("1", "4", transportNo, userId, payUserId);
        }

    }

    /**
     * 车方取消运单
     *
     * @param orderId
     * @param userId
     * @param payUserId
     * @param transportNo
     * @throws Exception
     */
    private void commonCarCancelMethod(Long orderId, Long userId, Long payUserId, String transportNo) throws Exception {
        // 修改订单的状态为车方取消 12-车方取消
        int c = transportOrdersService.saveChangeRobStatusById(orderId, "12");
        if (c > 0) {
            // 车主的拒绝/退费气泡加1
            tytBubbleService.updateBubbleNumber(payUserId, "1", "4", 1);
        }
    }

    //该方法可能已经弃用，请使用其他方法代替，如果你明确该方法是否弃用，请在此补充
    @Deprecated
    private void commonAgreeMethod(Long orderId, Long userId, Long payUserId, String transportNo, Transport transport, boolean isOfflineInfoFee) throws Exception {

        logger.warn("Deprecated_check_use ############# ");
        // 修改订单的状态为货主同意
        // 接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
        // 线下订单装货完成
        if (!isOfflineInfoFee) {
            transportOrdersService.saveChangeRobStatusById(orderId, "4");
        } else {
            transportOrdersService.saveChangeRobStatusById(orderId, "5");
        }
        // 货物信息设置成交(线上装货中,需要把货物的status设置成成交).//货主的待同意气泡减一
        transportBusiness.saveInfoFeeUpdateBtnStatus(userId, 2, transport);
        // tytBubbleService.updateBubbleNumber(userId, "2", "1", -1);
        // 车主的待装货气泡加一
        if (!isOfflineInfoFee) {
            //线上信息费做一下处理
            tytBubbleService.updateBubbleNumber(payUserId, "1", "2", 1);
            // 弹窗通知
            tytNoticeRemindService.saveNoticeRemind("1", "1", transportNo, userId, payUserId);
        }
    }

    @Override
    public void saveMqMessage(Long srcMsgId, User payUser, MqTytCarIntentionMsg carIntentionMsg) throws Exception {
        String serialNum = SerialNumUtil.generateSeriaNum();
        carIntentionMsg.setMessageSerailNum(serialNum);
        carIntentionMsg.setMessageType(MqBaseMessageBean.TYT_CAR_OWNER_CREATE_INTENTION);
        carIntentionMsg.setSrcMsgId(srcMsgId);
        carIntentionMsg.setUserId(payUser.getId());
        // 保存mq消息
        tytMqMessageService.addSaveMqMessage(serialNum, JSON.toJSONString(carIntentionMsg), carIntentionMsg.getMessageType());
    }

    /**
     * 设置显示超额保障提示文案：
     * 1. 开关控制，并可以修改文案
     * 2. 一人+一个货源只弹一次
     * 3. 直客发布的货源或优车2.0的货源或有抽佣标签的货源才提示
     */
    private String getGoodsDetailExcessCoverageTip(Long userId, TransportMain transportMain) {
        String excessCoverageTip = tytConfigService.getStringValue("excess_coverage_tip");
        // 为空代表开关关了
        if (StringUtils.isEmpty(excessCoverageTip)) {
            return "";
        }
        // 已浏览返回空
        boolean viewed = transportViewLogService.isViewed(userId, transportMain.getSrcMsgId());
        if (viewed) {
            return "";
        }
        // 直客发布的货源或优车2.0的货源或有抽佣标签的货源才提示
        TransportLabelJson labelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
        // 优车2.0 或 抽佣货源
        if (labelJson != null && (Objects.equals(labelJson.getGoodCarPriceTransport(), 1)
                || Objects.equals(labelJson.getCommissionTransport(), 1))) {
            return excessCoverageTip;
        }
        NewIdentity newIdentity = newIdentityService.getByUserId(transportMain.getUserId());
        // 或 直客发布货源（直客=个人货主或企业货主）
        if (newIdentity != null && (Objects.equals(newIdentity.getType(), 3)
                || Objects.equals(newIdentity.getType(), 4))) {
            return excessCoverageTip;
        }
        return "";
    }

    /**
     * 设置保障文案：
     * 目前只有货APP需要展示
     */
    private GoodsSingleDetailResultBean.ExcessCoveragePrompt getExcessCoveragePrompt(TransportMain transportMain) {
        GoodsSingleDetailResultBean.ExcessCoveragePrompt excessCoveragePrompt = new GoodsSingleDetailResultBean.ExcessCoveragePrompt();
        // 是否是专属保障货源（抽佣&直客）
        boolean isExcessCoverageGoods = false;
        TransportLabelJson labelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
        // 抽佣货源
        if (labelJson != null && Objects.equals(labelJson.getCommissionTransport(), 1)) {
            isExcessCoverageGoods = true;
        } else {
            NewIdentity newIdentity = newIdentityService.getByUserId(transportMain.getUserId());
            // 或 直客发布货源（直客=个人货主或企业货主）
            if (newIdentity != null && (Objects.equals(newIdentity.getType(), 3) || Objects.equals(newIdentity.getType(), 4))) {
                isExcessCoverageGoods = true;
            }
        }

        Integer excellentGoods = transportMain.getExcellentGoods();
        if (excellentGoods == 0 && !isExcessCoverageGoods) {
            excessCoveragePrompt.setType(1);
            excessCoveragePrompt.setTitle("爽约保障 延误保障 加价管控");
            excessCoveragePrompt.setContent("线下交易或取消订单将不再享受平台保障");
        } else if (excellentGoods == 0) {
            excessCoveragePrompt.setType(2);
            excessCoveragePrompt.setTitle("挟货保障 延误保障 实时追踪");
            excessCoveragePrompt.setContent("线下交易或取消订单将不再享受平台保障");
            excessCoveragePrompt.setUrl(publicResourceService.getByKey("goods_guarantee_page_url").getValue());
        } else if (excellentGoods == 1) {
            excessCoveragePrompt.setType(3);
            excessCoveragePrompt.setTitle("挟货保障 延误保障 实时追踪");
            excessCoveragePrompt.setContent("线下交易或取消订单将不再享受平台保障");
            excessCoveragePrompt.setUrl(publicResourceService.getByKey("youche_landing_page_url").getValue());
        } else if (excellentGoods == 2) {
            excessCoveragePrompt.setType(4);
            excessCoveragePrompt.setTitle("极速接单 专人服务 优选司机");
            excessCoveragePrompt.setContent("线下交易或取消订单将不再享受平台保障");
            excessCoveragePrompt.setUrl(publicResourceService.getByKey("special_goods_guarantee_page_url").getValue());
        }

        return excessCoveragePrompt;
    }

    /**
     * 获取是否显示优惠券标签
     * 1. 条件：直客（个人货主或企业货主）发布的货源 或优车2.0 或抽佣货源
     * 2. 需要开关关闭
     */
    private Integer getShowDepositCouponsLabel(TransportMain transportMain) {
        Integer labelSwitch = tytConfigService.getIntValue("show_deposit_coupons_label_switch", 0);
        if (labelSwitch == 1) {
            // 直客发布的货源或优车2.0的货源或有抽佣标签的货源才提示
            TransportLabelJson labelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
            // 优车2.0 或 抽佣货源
            if (labelJson != null && (Objects.equals(labelJson.getGoodCarPriceTransport(), 1)
                    || Objects.equals(labelJson.getCommissionTransport(), 1))) {
                return 1;
            }
            NewIdentity newIdentity = newIdentityService.getByUserId(transportMain.getUserId());
            // 或 直客发布货源（直客=个人货主或企业货主）
            if (newIdentity != null && (Objects.equals(newIdentity.getType(), 3)
                    || Objects.equals(newIdentity.getType(), 4))) {
                return 1;
            }
        }
        return 0;
    }
}
