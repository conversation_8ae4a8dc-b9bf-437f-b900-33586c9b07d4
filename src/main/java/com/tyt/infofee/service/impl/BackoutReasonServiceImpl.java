package com.tyt.infofee.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.BackoutReasonService;
import com.tyt.infofee.service.CardPrefixBankInfoService;
import com.tyt.infofee.service.TytUserBankcardService;
import com.tyt.model.BackoutReason;
import com.tyt.model.Transport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service("backoutReasonService")
public class BackoutReasonServiceImpl extends BaseServiceImpl<BackoutReason, Long> implements BackoutReasonService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "backoutReasonDao")
    public void setBaseDao(BaseDao<BackoutReason, Long> backoutReasonDao) {
        super.setBaseDao(backoutReasonDao);
    }

    @Override
    public void addBackoutReason(Transport transport, String backoutReasonKey, Integer backoutReasonValue,String specificReason,String remark, String backoutReasonKeyNew, Integer backoutReasonValueNew) {
        //将新增之前的撤销原因的状态修改为0
        this.updateStatus(transport);

        BackoutReason backoutReason = new BackoutReason();
        backoutReason.setBackoutReasonKey(backoutReasonKey);
        backoutReason.setBackoutReason(backoutReasonValue);
        backoutReason.setStartPoint(transport.getStartPoint());
        backoutReason.setDestPoint(transport.getDestPoint());
        backoutReason.setTaskContent(transport.getTaskContent());
        backoutReason.setSrcMsgId(transport.getSrcMsgId());
        backoutReason.setUserId(transport.getUserId());
        backoutReason.setSpecificReason(specificReason);
        backoutReason.setRemark(remark);
        backoutReason.setCtime(new Date());
        backoutReason.setStatus(1);
        backoutReason.setBackoutReasonKeyNew(backoutReasonKeyNew);
        backoutReason.setBackoutReasonNew(backoutReasonValueNew);
        this.add(backoutReason);
    }

    @Override
    public void updateStatus(Transport lockTransport) {
        Long srcMsgId = lockTransport.getSrcMsgId();
        String sql="UPDATE `tyt_backout_reason` set status = ? WHERE src_msg_id = ? and status = 1";
        this.executeUpdateSql(sql, new Object[] { 0, srcMsgId });
    }
}
