package com.tyt.infofee.service.impl;

import com.tyt.infofee.service.TransportEnterpriseLogService;
import com.tyt.plat.entity.base.TytTransportEnterpriseLog;
import com.tyt.plat.mapper.base.TytTransportEnterpriseLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 开票货源企业信息服务层实现类
 *
 * <AUTHOR>
 * @since 2024/07/24 09:50
 */
@Service
@Slf4j
public class TransportEnterpriseLogServiceImpl implements TransportEnterpriseLogService {

    @Autowired
    private TytTransportEnterpriseLogMapper transportEnterpriseLogMapper;

    /**
     * 根据srcMsgId获取开票货源企业信息
     *
     * <AUTHOR>
     * @param srcMsgId 货源ID
     * @return TytTransportEnterpriseLog
     */
    @Override
    public TytTransportEnterpriseLog getBySrcMsgId(Long srcMsgId) {
        //查询货源对应的开票主体id
        Example example = new Example(TytTransportEnterpriseLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("srcMsgId", srcMsgId);
        example.setOrderByClause(" id desc ");
        List<TytTransportEnterpriseLog> transportEnterpriseLogs = transportEnterpriseLogMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(transportEnterpriseLogs)) {
            return transportEnterpriseLogs.get(0);
        }
        return null;
    }
}
