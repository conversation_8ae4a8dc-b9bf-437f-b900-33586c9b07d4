package com.tyt.infofee.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.common.service.TytSequenceService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.AssignOrderTypeEnum;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillService;
import com.tyt.manbang.bean.request.MbOrderCreateSucNoticeBean;
import com.tyt.model.*;
import com.tyt.transport.service.EcaContractService;
import com.tyt.transport.service.EcaUserProgressService;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * User: Administrator Date: 13-11-10 Time: 下午5:10
 */
@SuppressWarnings("deprecation")
@Service("transportWayBillService")
public class TransportWayBillServiceImpl extends BaseServiceImpl<TytTransportWaybill, String> implements TransportWayBillService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource(name = "tytBubbleService")
	TytBubbleService tytBubbleService;

	@Resource(name = "tytSequenceService")
	TytSequenceService tytSequenceService;

	@Resource(name = "transportWayBillService")
	TransportWayBillService transportWayBillService;

	@Resource(name = "tytNoticeRemindService")
	TytNoticeRemindService tytNoticeRemindService;

	@Resource(name = "tytMqMessageService")
	TytMqMessageService tytMqMessageService;

	@Resource(name = "transportOrdersService")
	TransportOrdersService transportOrdersService;

	@Resource(name = "userService")
	UserService userService;

	@Resource(name = "ecaContractService")
	EcaContractService ecaContractService;

	@Resource(name = "ecaUserProgressService")
	EcaUserProgressService ecaUserProgressService;

	@Resource(name="tsinsuranceService")
	private TsInsuranceService tsinsuranceService;

	@Resource(name = "transportWayBillDao")
	public void setBaseDao(BaseDao<TytTransportWaybill, String> transportWayBillDao) {
		super.setBaseDao(transportWayBillDao);
	}

	private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	public ListDataBean updateGetMyWayBillList(Long userId, int queryActionType, int queryMenuType, long queryID) throws Exception {
		ListDataBean ListDataBean = new ListDataBean();
		List<Object> list = new ArrayList<Object>();
		int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");
		String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
		String endTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), 1)) + " 00:00:00";
		// 3个月之前的时间
		String threeMTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), -90)) + " 00:00:00";

		StringBuffer sql = new StringBuffer("select pay_user_id payUserId,ts_order_no tsOrderNo,ts_id tsId,sort_id sortId,user_id userId,start_point startPoint," + "dest_point destPoint, task_content taskContent, is_info_fee isInfoFee," + "ctime  publishTime,pay_time payEndTime,pay_link_phone payLinkPhone,pay_amount payAmount," + "pay_number payNumber,agree_time agreeTime,load_time loadTime,info_status infoStatus FROM tyt_transport_waybill FORCE INDEX(Index_userId_status_time)  where 1=1 ");

		sql.append(" and user_id=?");
		list.add(userId);

		switch (queryMenuType) {
		case 1:
			sql.append(" and info_status=?");
			list.add("1");
			sql.append(" and ctime>?");
			list.add(beginTime);
			sql.append(" and ctime<=?");
			list.add(endTime);
			break;
		case 2:
			sql.append(" and info_status=?");
			list.add("2");
			break;
		case 3:
			sql.append(" and (info_status=?");
			sql.append(" or info_status=? or info_status=?)");
			list.add("3");
			list.add("4");
			list.add("6");
			sql.append(" and load_time>?");
			list.add(threeMTime);
			sql.append(" and load_time<=?");
			list.add(endTime);
			break;

		}

		if (queryActionType == 2) {
			sql.append(" and sort_id<?");
			list.add(queryID);
		}

		sql.append(" order by sort_id desc ");

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("payUserId", Hibernate.LONG);
		scalarMap.put("tsOrderNo", Hibernate.STRING);
		scalarMap.put("tsId", Hibernate.LONG);
		scalarMap.put("sortId", Hibernate.LONG);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("startPoint", Hibernate.STRING);
		scalarMap.put("destPoint", Hibernate.STRING);
		scalarMap.put("taskContent", Hibernate.STRING);
		scalarMap.put("isInfoFee", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
		scalarMap.put("payLinkPhone", Hibernate.STRING);
		scalarMap.put("payAmount", Hibernate.LONG);
		scalarMap.put("payNumber", Hibernate.INTEGER);
		scalarMap.put("agreeTime", Hibernate.TIMESTAMP);
		scalarMap.put("loadTime", Hibernate.TIMESTAMP);
		scalarMap.put("infoStatus", Hibernate.STRING);

		List<TransportWayBillListBean> twbl = this.getBaseDao().search(sql.toString(), scalarMap, TransportWayBillListBean.class, list.toArray(), 1, pageSize);
		if (twbl != null && twbl.size() > 0) {
			for (TransportWayBillListBean transportWayBillListBean : twbl) {
				//货物信息Id
				Long tsId =  transportWayBillListBean.getTsId();
				// 获取最新的保单
				TransportInsurance transportInsurance = tsinsuranceService.queryInsuranceByTsId(userId, tsId);
				//最后一次购买保险的保单信息
				if(transportInsurance != null)
				{
					//是否购买过保险 1是(显示“查看保单”按钮)
					transportWayBillListBean.setIsBuyInsurance(1);
					//最后一次购买保险的保单Id
					Long insuranceId = transportInsurance.getId();
					if(insuranceId != null)
					{
						transportWayBillListBean.setInsuranceId(insuranceId);
					}
					//最后一次购买保险的保单状态 0待支付 1已生效 2已退保
					Integer status = transportInsurance.getStatus();
					if(status != null)
					{
						transportWayBillListBean.setInsuranceStatus(status);
					}
				}else{
					//是否购买过保险  2否(显示“买货运险”按钮)
					transportWayBillListBean.setIsBuyInsurance(2);
				}
			}
			ListDataBean.setData(twbl);
		}
		// 如果是装货完成 要修改气泡数为0
		if (queryMenuType == 3) {
			int n = tytBubbleService.updateBubbleNumber(userId, "2", "2", 0);
			logger.info("修改了type1{},type2{},{}条气泡数据", "2", "2", n);
		}
		ListDataBean.setBubbleNumbers(tytBubbleService.getInfoFeeMyPublishBubbleResultBeanForUserId(userId));
		ListDataBean.setCurrentTime(System.currentTimeMillis());
		return ListDataBean;
	}

	@Override
	public void saveOffLineDealGoodsToTransportWayBill(Transport transport, String infoStatus) throws Exception {
		// 先判断是否已经存在该src_msg_id的数据，如果不存在才插入,存在则更新
		List<TytTransportWaybill> transportWaybillList = this.getList(" tsId= " + transport.getSrcMsgId(), null);
		Date todayDate = new Date();
		TytTransportWaybill tytTransportWaybill = new TytTransportWaybill();
		if (transportWaybillList == null || transportWaybillList.size() == 0) {
			tytTransportWaybill = new TytTransportWaybill();
		} else {
			tytTransportWaybill = transportWaybillList.get(0);
		}
		tytTransportWaybill.setStartPoint(transport.getStartPoint());
		tytTransportWaybill.setDestPoint(transport.getDestPoint());
		tytTransportWaybill.setTaskContent(transport.getTaskContent());
		tytTransportWaybill.setTel(transport.getTel());
		tytTransportWaybill.setPubTime(transport.getPubTime());
		tytTransportWaybill.setCtime(transport.getReleaseTime());
		tytTransportWaybill.setUploadCellphone(transport.getUploadCellPhone());
		tytTransportWaybill.setLinkman(transport.getLinkman());
		tytTransportWaybill.setTel3(transport.getTel3());
		tytTransportWaybill.setTel4(transport.getTel4());
		tytTransportWaybill.setUserId(transport.getUserId());
		tytTransportWaybill.setSortId(tytSequenceService
				.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME));
		tytTransportWaybill.setTsOrderNo(transport.getTsOrderNo());
		tytTransportWaybill.setIsInfoFee(transport.getIsInfoFee());
		tytTransportWaybill.setInfoStatus(infoStatus);// 线下成交的属于装货完成
		tytTransportWaybill.setCreateTime(todayDate);
		tytTransportWaybill.setPayUserId(null);
		tytTransportWaybill.setPayCellPhone(null);
		tytTransportWaybill.setPayLinkPhone(null);
		tytTransportWaybill.setPayAmount(null);
		tytTransportWaybill.setPayTime(null);
		tytTransportWaybill.setPayNumber(0);
		tytTransportWaybill.setAgreeTime(todayDate);
		tytTransportWaybill.setLoadTime(todayDate);
		tytTransportWaybill.setMtime(todayDate);
		tytTransportWaybill.setTsId(transport.getSrcMsgId());
		if (transportWaybillList == null || transportWaybillList.size() == 0) {
			this.getBaseDao().insert(tytTransportWaybill);
		} else {
			this.getBaseDao().update(tytTransportWaybill);
		}
	}

	@Override
	public TytTransportWaybill saveOnLineWayBill(Integer zeroAssignOrder,MbOrderCreateSucNoticeBean orderCreateSucNoticeBean,Transport originalTransport, User payUser, Integer timeLimitIdentification, BigDecimal couponAmount) throws Exception {
		try {
			Date todayDate = new Date();
			TytTransportWaybill tytTransportWaybill = new TytTransportWaybill();
			tytTransportWaybill.setStartPoint(originalTransport.getStartPoint());
			tytTransportWaybill.setDestPoint(originalTransport.getDestPoint());
			tytTransportWaybill.setTaskContent(originalTransport.getTaskContent());
			tytTransportWaybill.setTel(originalTransport.getTel());
			tytTransportWaybill.setPubTime(originalTransport.getPubTime());
			tytTransportWaybill.setCtime(originalTransport.getReleaseTime());
			tytTransportWaybill.setUploadCellphone(originalTransport.getUploadCellPhone());
			tytTransportWaybill.setLinkman(originalTransport.getLinkman());
			tytTransportWaybill.setTel3(originalTransport.getTel3());
			tytTransportWaybill.setTel4(originalTransport.getTel4());
			tytTransportWaybill.setUserId(originalTransport.getUserId());
			tytTransportWaybill.setSortId(tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME));
			tytTransportWaybill.setTsOrderNo(originalTransport.getTsOrderNo());
			tytTransportWaybill.setIsInfoFee(originalTransport.getIsInfoFee());
			tytTransportWaybill.setCreateTime(todayDate);
			tytTransportWaybill.setLoadTime(null);
			tytTransportWaybill.setMtime(todayDate);
			// tyt_plat_transport_optimize20171123 货源信息优化 老代码：tytTransportWaybill.setTsId(originalTransport.getId());
			tytTransportWaybill.setTsId(originalTransport.getSrcMsgId());
			//2019-01-10 信息费改版新增字段
			//发布人昵称
			tytTransportWaybill.setPubUserName(originalTransport.getNickName());
			//车主昵称(支付人昵称)
			tytTransportWaybill.setPayUserName(payUser.getUserName());
			tytTransportWaybill.setTimeLimitIdentification(timeLimitIdentification);
			//保障货源（1是；0否；）
			tytTransportWaybill.setGuaranteeGoods(originalTransport.getGuaranteeGoods());
			tytTransportWaybill.setMachineRemark(originalTransport.getMachineRemark());
			//当订单为0元指派单时 默认支付成功 初始化支付成功数据
			if (Objects.equal(AssignOrderTypeEnum.assign_order.getCode(), zeroAssignOrder)) {
				tytTransportWaybill.setInfoStatus("2");
				tytTransportWaybill.setPayUserId(payUser.getId());
				tytTransportWaybill.setPayCellPhone(payUser.getCellPhone());
				tytTransportWaybill.setPayLinkPhone(payUser.getCellPhone());
				tytTransportWaybill.setPayAmount(0L);
				tytTransportWaybill.setPayTime(new Date());
				tytTransportWaybill.setAgreeTime(new Date());
				tytTransportWaybill.setPayNumber(1);
			} else {
				//判断当前信息的来源 orderCreateSucNoticeBean 不为空 则表示该订单已支付成功
				if (orderCreateSucNoticeBean != null) {
					tytTransportWaybill.setInfoStatus("2");
					tytTransportWaybill.setPayUserId(payUser.getId());
					tytTransportWaybill.setPayCellPhone(payUser.getCellPhone());
					tytTransportWaybill.setPayLinkPhone(payUser.getCellPhone());
					tytTransportWaybill.setPayAmount(orderCreateSucNoticeBean.getDeposit());
					tytTransportWaybill.setPayTime(new Date(orderCreateSucNoticeBean.getPayDepositTime()));
					tytTransportWaybill.setAgreeTime(new Date(orderCreateSucNoticeBean.getPayDepositTime()));
					tytTransportWaybill.setPayNumber(1);
				} else {
					tytTransportWaybill.setInfoStatus("0");
					tytTransportWaybill.setPayUserId(null);
					tytTransportWaybill.setPayCellPhone(null);
					tytTransportWaybill.setPayLinkPhone(null);
					tytTransportWaybill.setPayAmount(null);
					tytTransportWaybill.setPayTime(null);
					tytTransportWaybill.setAgreeTime(null);
					tytTransportWaybill.setPayNumber(0);
				}
			}
			this.add(tytTransportWaybill);
			return tytTransportWaybill;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	@Override
	public int saveChangeCarOwnerInfo(Long carOwnerUserId, String transportNo, String infoStatus, String carOwnerRegisterPhone, String carOwnerTelephone, Long agencyMoney, Date carOwnerPayEndTime, Integer payNumber) throws Exception {
		String updateSQL = "";
		if (infoStatus.equals("2")) {
			updateSQL = "update tyt_transport_waybill set" + " info_status=:infoStatus,pay_user_id=:carOwnerUserId," + "pay_cell_phone=:carOwnerRegisterPhone,pay_link_phone=:carOwnerTelephone," + "pay_amount=:agencyMoney,pay_number=:payNumber,pay_time=:carOwnerPayEndTime,mtime=NOW(),agree_time=NOW(),sort_id=:sortId" + " where ts_order_no=:transportNo";
		} else {
			updateSQL = "update tyt_transport_waybill set" + " info_status=:infoStatus,pay_user_id=:carOwnerUserId," + "pay_cell_phone=:carOwnerRegisterPhone,pay_link_phone=:carOwnerTelephone," + "pay_amount=:agencyMoney,pay_number=:payNumber,pay_time=:carOwnerPayEndTime,mtime=NOW(),sort_id=:sortId" + " where ts_order_no=:transportNo";
		}
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("infoStatus", infoStatus);
		paramsMap.put("carOwnerUserId", carOwnerUserId);
		paramsMap.put("carOwnerRegisterPhone", carOwnerRegisterPhone);
		paramsMap.put("carOwnerTelephone", carOwnerTelephone);
		paramsMap.put("agencyMoney", agencyMoney);
		paramsMap.put("transportNo", transportNo);
		paramsMap.put("payNumber", payNumber);
		paramsMap.put("carOwnerPayEndTime", carOwnerPayEndTime);
		paramsMap.put("sortId", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME));
		return this.executeUpdateSql(updateSQL, paramsMap);
	}

	public TytTransportWaybill getTytTransportWaybillForLock(String tsOrderNo) {
		return this.getBaseDao().getByIdForLock(tsOrderNo);
	}

	@Override
	public int saveChangePayNumber(String tsOrderNo, int payNumber) throws Exception {
		String updateSQL = "update tyt_transport_waybill set pay_number=?,mtime=NOW() where  ts_order_no=?";
		return this.executeUpdateSql(updateSQL, new Object[] { payNumber, tsOrderNo });
	}

	public Map<String, Object> updateWayBillFinish(Long userId, String tsOrderNo, TytTransportWaybill transportWayBill, TytTransportOrders tytTransportOrders, Long srcMsgId) {
		// tyt_plat_transport_optimize20171123 货源信息优化
		Map<String, Object> map = new HashMap<String, Object>();
		if (transportWayBill == null) {
			map.put("code", 500);
			return map;
		}
		if (transportWayBill.getInfoStatus().equals("5")) {
			map.put("code", 211001);
			return map;
		}
		if (!transportWayBill.getInfoStatus().equals("2")) {
			map.put("code", 501);
			return map;
		}
		// tyt_plat_transport_optimize20171123 货源信息优化
		User gsUser = userService.getUserByCellphone(AppConfig.getProperty("tyt.user.account.company.account"));
		// 修改 transport 表，
		// tyt_plat_transport_optimize20171123 货源信息优化
		String sql = "update tyt_transport set info_status=? ,mtime=now() where src_msg_id=? and status=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { "3", srcMsgId, 1 });
		// 修改 transport_mian 表
		// tyt_plat_transport_optimize20171123 货源信息优化
		sql = "update tyt_transport_main set info_status=? ,mtime=now() where src_msg_id=? and status=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { "3", srcMsgId, 1 });
		// 修改 tyt_transport_waybill
		sql = "update tyt_transport_waybill set info_status=? ,load_time=now(),mtime=now(),sort_id=? where ts_order_no=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { "3", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME), tsOrderNo });
		// 修改tyt_transport_orders表
		sql = "update tyt_transport_orders set rob_status=? ,load_time=now(),mtime=now(),sort_id=? where pay_user_id=? and ts_order_no=? and rob_status=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { "5", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME), transportWayBill.getPayUserId(), tsOrderNo, "4" });
		// 增加交易表信息 装货完成
		// 调用转帐，把钱转给货主 增加交易表记录
		sql = "INSERT INTO `tyt`.`tyt_trade_info` (`trade_type`, `trade_time`, `update_time`, `trade_account`, `allow_pay_back_refund`, `refunding_account`, `good_info_fee_id`, `payer_account_id` ,`payer_user_id`, `pay_receiver_account_id`,`pay_receiver_user_id`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
		Object[] params = new Object[] { "4", new Date(), new Date(), transportWayBill.getPayAmount(), transportWayBill.getPayAmount(), 0, tytTransportOrders.getId(), this.getUserAccount(gsUser.getId(), 1), gsUser.getId(), this.getUserAccount(transportWayBill.getUserId(), 1), transportWayBill.getUserId() };
		this.getBaseDao().executeUpdateSql(sql, params);
		// 调用转帐，把钱转给货主
		MqWaybillFinishMessageBean waybillFinishMessageBean = new MqWaybillFinishMessageBean();
		waybillFinishMessageBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
		waybillFinishMessageBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_AGREE_LOADING);
		waybillFinishMessageBean.setPayUserId(transportWayBill.getPayUserId());
		waybillFinishMessageBean.setReceiverUserId(transportWayBill.getUserId());
		waybillFinishMessageBean.setTsOrderNo(transportWayBill.getTsOrderNo());
		waybillFinishMessageBean.setPayAmount(transportWayBill.getPayAmount());
		tytMqMessageService.addSaveMqMessage(waybillFinishMessageBean.getMessageSerailNum(), JSON.toJSONString(waybillFinishMessageBean), MqBaseMessageBean.MESSAGETYPE_AGREE_LOADING);
		// 发车主主动装货完成弹窗给发货方通知
		tytNoticeRemindService.saveNoticeRemind("2", "3", transportWayBill.getTsOrderNo(), transportWayBill.getPayUserId(), transportWayBill.getUserId());
		// 发货方 装货完成/线下成交 气泡加一
		tytBubbleService.updateBubbleNumber(transportWayBill.getUserId(), "2", "2", 1);
		// 车主方 已成交气泡加一
		tytBubbleService.updateBubbleNumber(transportWayBill.getPayUserId(), "1", "3", 1);
		// 车主方 待装货车主气泡减一
		tytBubbleService.updateBubbleNumber(transportWayBill.getPayUserId(), "1", "2", -1);

		//TODO 如果存在合同信息，则更新用户签约进度信息 20180116
		EcaContract ecaContract = ecaContractService.getEcaContractBySrcMsgId(srcMsgId);
		if(ecaContract != null){
			//增加用户签署进度日志
			EcaUserProgress eUserProgress = new EcaUserProgress();
			eUserProgress.setContractId(ecaContract.getId());
			eUserProgress.setCtime(new Date());
			eUserProgress.setSrcMsgId(srcMsgId);
			eUserProgress.setType(2);
			ecaUserProgressService.add(eUserProgress);
		}
		map.put("mqBean", waybillFinishMessageBean);
		map.put("code", 200);
		return map;
	}

	@Override
	public List<GoodsDetailOrderLoadingBean> getOrderLoadingInfo(String transportNo) throws Exception {
		String selectSQL = "select ts_order_no goodsOrderNo,pay_link_phone carOwnerTelephone,"
				+ "pay_amount payAgencyMoney,pay_time payEndTime,agree_time goodsOwnerAgreeTime"
				+ " from tyt_transport_waybill where ts_order_no=:transportNo";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("goodsOrderNo", Hibernate.STRING);
		scalarMap.put("carOwnerTelephone", Hibernate.STRING);
		scalarMap.put("payAgencyMoney", Hibernate.LONG);
		scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
		scalarMap.put("goodsOwnerAgreeTime", Hibernate.TIMESTAMP);
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("transportNo", transportNo);
		return this.getBaseDao().search(selectSQL, scalarMap, GoodsDetailOrderLoadingBean.class, paramsMap);
	}

	@Override
	public int saveChangeInfoStatus(Long userId, String transportNo, String infoStatus) {
		String updateSQL = "";
		if (infoStatus.equals("2")) {
			updateSQL = "update tyt_transport_waybill set info_status=?,sort_id=?,agree_time=NOW() where user_id=? and ts_order_no=?";
		} else if (infoStatus.equals("6")) {
			updateSQL = "update tyt_transport_waybill set info_status=?,sort_id=?,load_time=NOW() where user_id=? and ts_order_no=?";
		} else {
			updateSQL = "update tyt_transport_waybill set info_status=?,sort_id=? where user_id=? and ts_order_no=?";
		}
		Long sortId = tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME);
		return this.executeUpdateSql(updateSQL, new Object[] { infoStatus, sortId, userId, transportNo });
	}

	@Override
	public List<GoodsDetailOrderFinishedBean> getOrderFinishedInfo(String transportNo) throws Exception {
		String selectSQL = "select ts_order_no goodsOrderNo,pay_link_phone carOwnerTelephone, pay_amount payAgencyMoney,pay_time payEndTime,"
				+ "agree_time goodsOwnerAgreeTime,load_time carOwnerLoadfinishedTime" + " from tyt_transport_waybill where ts_order_no=:transportNo";
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("goodsOrderNo", Hibernate.STRING);
		scalarMap.put("carOwnerTelephone", Hibernate.STRING);
		scalarMap.put("payAgencyMoney", Hibernate.LONG);
		scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
		scalarMap.put("goodsOwnerAgreeTime", Hibernate.TIMESTAMP);
		scalarMap.put("carOwnerLoadfinishedTime", Hibernate.TIMESTAMP);
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("transportNo", transportNo);
		return this.getBaseDao().search(selectSQL, scalarMap, GoodsDetailOrderFinishedBean.class, paramsMap);
	}

	public int getUserAccount(Long userId, int affiliatedType) {
		String sql = "select id from tyt_user_account where user_id=:userId and affiliated_type=:affiliatedType";
		Map<String, org.hibernate.type.Type> cMap = new HashMap<String, org.hibernate.type.Type>();
		cMap.put("id", Hibernate.LONG);
		Map<String, Object> p = new HashMap<String, Object>();
		p.put("userId", userId);
		p.put("affiliatedType", affiliatedType);
		TytUserAccount tytUserAccount = this.getBaseDao().queryByMap(sql, p, TytUserAccount.class, cMap);
		return tytUserAccount.getId().intValue();
	}

	@Override
	public TytTransportWaybill getTransportWaybillBySrcMsgId(Long srcMsgId) {
		String hql = "from TytTransportWaybill t where t.tsId = ? ";
		List<TytTransportWaybill> tytTransportWaybills = this.getBaseDao().find(hql,srcMsgId);
		if(tytTransportWaybills != null && tytTransportWaybills.size() > 0){
			return tytTransportWaybills.get(0);
		}
		return null;
	}

	@Override
	public Boolean updateWaybillOneInfo(String tsOrderNo, Long payUserId, String payCellPhone, String payLinkPhone, Long payAmount) {
		String updateSQL = "update tyt_transport_waybill set pay_user_id=?,pay_cell_phone=?,pay_link_phone=?,pay_amount=?,mtime=now() where ts_order_no=?";
		Object [] params = {
				payUserId,
				payCellPhone,
				payLinkPhone,
				payAmount,
				tsOrderNo
		};
		int count = this.executeUpdateSql(updateSQL, params);
		return count >= 0;
	}

	@Override
	public int updateWayBillForConfirmSuccess(String tsOrderNo) throws Exception {
		String updateSQL = "update tyt_transport_waybill set info_status=?," +
			            	"load_time=now(),mtime=NOW(),sort_id=? where ts_order_no=?";
		return this.executeUpdateSql(updateSQL, new Object[]{"4", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME), tsOrderNo});
	}

	@Override
	public int addTradeInfo(Integer tradeType,TytTransportOrders orders,Integer payUserId,Integer payReceiverUserId, Integer refundingAccount) {
		String sql = "INSERT INTO `tyt`.`tyt_trade_info` (`trade_type`, `trade_time`, `update_time`, `trade_account`, `allow_pay_back_refund`, `refunding_account`, `good_info_fee_id`, `payer_user_id`, `order_id`, `pay_receiver_user_id`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
		Object[] params = new Object[]{tradeType,sdf.format(new Date()), sdf.format(new Date()), orders.getPayAmount(), orders.getPayAmount(), refundingAccount, orders.getId(), payUserId, orders.getPayNo(), payReceiverUserId};
		this.getBaseDao().executeUpdateSql(sql, params);
		sql = "SELECT MAX(id) FROM tyt_trade_info";
		Integer maxId = this.getBaseDao().query(sql, new Object[] {});
		return maxId.intValue();
	}

	@Override
	public void updateOnLineWayBill( TytTransportWaybill transportWaybill,MbOrderCreateSucNoticeBean orderCreateSucNoticeBean, User payUser, Integer timeLimitIdentification) throws Exception {
			Date todayDate = new Date();
			transportWaybill.setSortId(tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME));
			transportWaybill.setCreateTime(todayDate);
			transportWaybill.setLoadTime(null);
			transportWaybill.setMtime(todayDate);
			//发布人昵称
			//车主昵称(支付人昵称)
			transportWaybill.setPayUserName(payUser.getUserName());
			transportWaybill.setTimeLimitIdentification(timeLimitIdentification);
			//判断当前信息的来源 orderCreateSucNoticeBean 不为空 则表示该订单已支付成功
			transportWaybill.setInfoStatus("2");
			transportWaybill.setPayUserId(payUser.getId());
			transportWaybill.setPayCellPhone(payUser.getCellPhone());
			transportWaybill.setPayLinkPhone(payUser.getCellPhone());
			transportWaybill.setPayAmount(orderCreateSucNoticeBean.getDeposit());
			transportWaybill.setPayTime(new Date(orderCreateSucNoticeBean.getPayDepositTime()));
			transportWaybill.setAgreeTime(new Date(orderCreateSucNoticeBean.getPayDepositTime()));
		    transportWaybill.setPayNumber(transportWaybill.getPayNumber()+1);
			this.update(transportWaybill);
	}

}
