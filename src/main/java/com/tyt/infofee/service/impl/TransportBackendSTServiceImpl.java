package com.tyt.infofee.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.infofee.bean.MqMsg;
import com.tyt.model.*;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqUserMsg;
import com.tyt.infofee.service.TransportBackendSTService;
import com.tyt.mybatis.mapper.DemoMapper;
import com.tyt.model.CsMaintainedCustom;
import com.tyt.plat.entity.base.OwnerCompanyLog;
import com.tyt.receive.service.OwnerCompanyLogService;
import com.tyt.transport.querybean.TransportDoneRequest;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.user.service.CsMaintainedCustomService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service("transportBackendSTService")
public class TransportBackendSTServiceImpl extends BaseServiceImpl<TytTransportBackend, Long> implements TransportBackendSTService {

    @Resource(name = "transportBackendSTDao")
    public void setBaseDao(BaseDao<TytTransportBackend, Long> transportBackendSTDao) {
        super.setBaseDao(transportBackendSTDao);
    }

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;
    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;
    @Autowired
    private DemoMapper demoMapper;

    @Autowired
    private OwnerCompanyLogService ownerCompanyLogService;

    @Autowired
    private CsMaintainedCustomService csMaintainedCustomService;

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public TytTransportBackend selectById(Long id) {
        TytTransportBackend transportBackend = this.getById(id);
        return transportBackend;
    }

    @Override
    public ResultMsgBean confirm(TytTransportBackend transportBackend, Long id,Long userId){
        ResultMsgBean rm = new ResultMsgBean();

        //tyt_transport_backend order_status状态
        //10-待接单 20-已取消 30-已接单未发布 31-已接单发布中 32-已接单撤销 33-已接单过期 40-已完成 50-后台设置为无效 60-货源已成交 70-已装货 80-已卸货
        int orderStatus = transportBackend.getOrderStatus();

        //场景1：10-待接单
        if (orderStatus == 10) {
            updateById(id);
            transportBackend.setCancelConfirm(1);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("货源取消成功");
            rm.setData(transportBackend.getCancelConfirm());
            return rm;
        }

        //判断是否有手动设成交的订单
        TransportMain main = selectmainBySrcId(transportBackend.getSrcMsgId());
        //查询该货源是否有已支付订单
        TytTransportOrders order = selectOrdersById(transportBackend.getSrcMsgId());
        //查询该货源是否有订单
        TytTransportOrders transportOrders = selectOrdersByIds(transportBackend.getSrcMsgId());

        //场景2：30-已接单未发布 32-已接单撤销 33-已接单过期
        if (orderStatus == 30 || orderStatus == 32 || orderStatus == 33 || (orderStatus == 60 && main != null && order == null && transportOrders == null) ) {
            updateById(id);
            transportBackend.setCancelConfirm(1);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("该货源未接单，系统已拦截货源自动下架");
            rm.setData(transportBackend.getCancelConfirm());
            return rm;
        }

        TransportMain transportMain = selectmainById(transportBackend.getSrcMsgId());

        if ( orderStatus == 31 || orderStatus == 60 ){
            //场景4： 31-已接单发布中 60-货源已成交，已支付，货源已成交
            //车方如果已支付，车方交易中状态不变，需要货方联系车方协商并退还信息费，退还成功后按车方原有逻辑变更为已完成订单
            if (order != null ){
                updateById(id);
                transportBackend.setCancelConfirm(1);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("该货源车方已支付，请联系车方协商");
                rm.setData(transportBackend.getCancelConfirm());
                return rm;
            }else if (transportOrders != null && !transportOrders.getPayStatus().equals("2")){
                //场景3.1：31-已接单发布中，货方已发布，车方直接生成待支付订单，未支付，熟车场景。
                //未支付订单货源自动下架，会关联把我的货源撤销处理，撤销原因需要有个默认原因，货主货源已取消,车方如果待支付订单已取消，待支付下将自动清除该订单
                if(transportBackend.getSrcMsgId() != null){
                    //判断是否有手动设置成交，如果有则不撤销
                    if(main == null){
                        String backoutReasonKey = "货主已取消货源";
                        Integer backoutReasonValue = 7;
                        try {
                            transportBusiness.saveInfoFeeUpdateBtnStatusNew(userId, 1, transportBackend.getSrcMsgId(), new TransportDoneRequest(),backoutReasonKey, backoutReasonValue,null,null, true, null, null);
                        }catch (Exception e){
                            e.printStackTrace();
                            rm.setCode(ReturnCodeConstant.ERROR);
                            rm.setMsg("失败");
                            return rm;
                        }
                    }
                }
                updateById(id);
                transportBackend.setCancelConfirm(1);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("该货源已接单，系统已拦截货源自动下架，请与车方说明");
                rm.setData(transportBackend.getCancelConfirm());
                return rm;
            }else if (transportMain !=null && !transportMain.getInfoStatus().equals("2")){
                //场景3.2：31-已接单发布中，货方已发布，车方未接单，未生成待支付订单，未支付，平台场景。
                if(transportBackend.getSrcMsgId() != null){
                    //判断是否有手动设置成交，如果有则不撤销
                    if (main == null){
                        String backoutReasonKey = "货主已取消货源";
                        Integer backoutReasonValue = 7;
                        try {
                            transportBusiness.saveInfoFeeUpdateBtnStatusNew(userId, 1, transportBackend.getSrcMsgId(), new TransportDoneRequest(),backoutReasonKey, backoutReasonValue,null,null, true, null, null);
                        }catch (Exception e){
                            e.printStackTrace();
                            rm.setCode(ReturnCodeConstant.ERROR);
                            rm.setMsg("失败");
                            return rm;
                        }
                    }
                }
                    updateById(id);
                    transportBackend.setCancelConfirm(1);
                    rm.setCode(ReturnCodeConstant.OK);
                    rm.setMsg("该货源未接单，系统已拦截货源自动下架");
                    rm.setData(transportBackend.getCancelConfirm());
                    return rm;
                }
            }

        //记录企业货源状态流转日志
        OwnerCompanyLog ownerCompanyLog = new OwnerCompanyLog();
        ownerCompanyLog.setOrderNo(transportBackend.getOrderNo());
        ownerCompanyLog.setBackendId(transportBackend.getId());
        ownerCompanyLog.setCompanyId(transportBackend.getReceiverUserId());
        ownerCompanyLog.setEnterpriseId(transportBackend.getAppletsUserId());
        ownerCompanyLog.setStatus(2);
        ownerCompanyLog.setOrderStatus(20);
        ownerCompanyLog.setSrcMsgId(transportBackend.getSrcMsgId());
        ownerCompanyLogService.addOwnerCompanyLog(ownerCompanyLog);
        rm.setCode(ReturnCodeConstant.ERROR);
        rm.setMsg("取消失败");
        return rm;

    }

    @Override
    public void updateById(Long id) {
        //状态修改为 20-手动撤销
        String sql = "UPDATE tyt_transport_backend ttb  SET ttb.`order_status` = ? , ttb.`cancel_time` = ? , ttb.`cancel_confirm` = ? WHERE ttb.`id` = ? ";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{20,new Date(), 1, id});
    }

    @Override
    public TytTransportOrders selectOrdersByIds(Long SrcMsgId) {
        List<TytTransportOrders> transportOrders = demoMapper.selectOrdersByIds(SrcMsgId);
        if (transportOrders != null && transportOrders.size() > 0){
            return transportOrders.get(0);
        }
        return null;
    }

    @Override
    public TytTransportOrders selectOrdersById(Long SrcMsgId) {
        String payStatus = "2";
        List<TytTransportOrders> transportOrders = demoMapper.selectOrdersById(SrcMsgId,payStatus);
        if (transportOrders != null && transportOrders.size() > 0){
            return transportOrders.get(0);
        }
       return null;
    }

    public TytTransportBackend selectbackendById(Long id){
        TytTransportBackend backend = demoMapper.selectbackendById(id);
        return backend;
    }

    @Override
    public TransportMain selectmainById(Long SrcMsgId) {
        TransportMain transportMain = demoMapper.selectmainById(SrcMsgId);
        return transportMain;
    }

    @Override
    public TransportMain selectmainBySrcId(Long SrcMsgId) {
        TransportMain main = demoMapper.selectmainBySrcId(SrcMsgId);
        return main;
    }

    @Override
    public void updateUnloadById(Long id , Integer status , Integer orderStatus) {
        //状态 修改
        String sql = "UPDATE tyt_transport_backend SET status = ? , order_status = ?, mtime =NOW() WHERE id = ? ";
        if (status == 7){
            sql = "UPDATE tyt_transport_backend SET status = ? , order_status = ?, mtime =NOW(),carriage_time=NOW() WHERE id = ? ";
        }
        if (status == 8){
            sql = "UPDATE tyt_transport_backend SET status = ? , order_status = ?, mtime =NOW(),complete_time=NOW() WHERE id = ? ";
        }
        this.getBaseDao().executeUpdateSql(sql, new Object[]{status,orderStatus,id});
        sendNullifyMessage2MQ(id,status);

        try {
            if (status == 8){
                sendToMq(id);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     * 向MQ发送信息
     *
     * @param id backend表 id
     */
    private void sendNullifyMessage2MQ(Long id , Integer status) {
        // 发送初货物无效信息MQ
        MqUserMsg mqUserMsg = new MqUserMsg();

        TytTransportBackend tytTransportBackend = this.getById(id);

        mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqUserMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_BACKEND_STATUS_MESSAGE);
        mqUserMsg.setUserId(id);
        mqUserMsg.setSrcMsgId(tytTransportBackend.getReceiverUserId());
        if (status == 8){
            String content= "货物单号为【" + id + "】的货物已卸货";
            mqUserMsg.setContent(content);
        }else {
            String content= "货物单号为【" + id + "】的货物已装货";
            mqUserMsg.setContent(content);
        }

        // 保存mq信息到数据库
        tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

        long mqDelayTime = tytConfigService.getIntValue(Constant.publish_mq_delay_time, 1500).longValue();

        logger.info("publish_mq_delay_time : " + mqDelayTime);

        // 发送货物无效信息
        tytMqMessageService.sendMqMessageDirect(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqDelayTime);
    }

    /**
     * 卸货发送消息到后台调度员
     * @param id
     */
    private void sendToMq(Long id ) throws Exception{

        MqMsg mqUserMsg = new MqMsg();
        TytTransportBackend backend = this.getById(id);
        //根据货站用户查询货站关联的调度员
        CsMaintainedCustom custom = csMaintainedCustomService.getCsMaintainedCustomByUserId(backend.getReceiverUserId());
        //有关联的调度员，发送信息
        if(null != custom){
            if(null != custom.getDispatcherId()){
                mqUserMsg.setBachendId(id);
                mqUserMsg.setDispatcherId(custom.getDispatcherId());
                mqUserMsg.setDispatcherName(custom.getDispatcherName());
                mqUserMsg.setUserId(backend.getAppletsUserId());
                try {
                    User user = userService.getByUserId(backend.getAppletsUserId());

                    mqUserMsg.setUserName(user.getUserName());
                }catch (Exception e){
                    e.printStackTrace();
                }
                mqUserMsg.setType(1);
                mqUserMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
                mqUserMsg.setMessageType(MqBaseMessageBean.BACKEND_SEND);
                // 保存mq信息到数据库
                tytMqMessageService.addSaveMqMessage(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqUserMsg.getMessageType());

                long mqDelayTime = tytConfigService.getIntValue(Constant.publish_mq_delay_time, 1500).longValue();

                logger.info("publish_mq_delay_time : " + mqDelayTime);

                // 企业货源给后台发送消息
                tytMqMessageService.sendMqMessageDirect(mqUserMsg.getMessageSerailNum(), JSON.toJSONString(mqUserMsg), mqDelayTime);
            }
        }

    }
}
