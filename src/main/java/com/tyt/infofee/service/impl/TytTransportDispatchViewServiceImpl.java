package com.tyt.infofee.service.impl;

import com.tyt.infofee.service.TytTransportDispatchViewService;
import com.tyt.model.User;
import com.tyt.plat.entity.base.TytTransportDispatchView;
import com.tyt.plat.entity.base.TytTransportDispatchViewDetail;
import com.tyt.plat.mapper.base.TytTransportDispatchViewDetailMapper;
import com.tyt.plat.mapper.base.TytTransportDispatchViewMapper;
import com.tyt.user.service.UserService;
import com.tyt.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2022/11/18
 */
@Service
public class TytTransportDispatchViewServiceImpl implements TytTransportDispatchViewService {

    @Autowired
    private TytTransportDispatchViewMapper tytTransportDispatchViewMapper;
    @Autowired
    private TytTransportDispatchViewDetailMapper tytTransportDispatchViewDetailMapper;


    /**
     * @param user     车主
     * @param srcMsgId 货源id（src_msg_id）
     * @param type     查看类型 1：查看  2：联系
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTransportView(User user, Long srcMsgId, Integer type) throws Exception {
        if (user != null) {
            saveOrUpdateView(user, srcMsgId, type);
            saveViewDetail(user, srcMsgId, type);
        }
    }

    private void saveViewDetail(User user, Long srcMsgId, Integer type) {
        TytTransportDispatchViewDetail tytTransportDispatchViewDetail = new TytTransportDispatchViewDetail();
        tytTransportDispatchViewDetail.setSrcMsgId(srcMsgId);
        tytTransportDispatchViewDetail.setCarUserId(user.getId());
        tytTransportDispatchViewDetail.setCarUserName(StringUtil.formatUserName(user.getTrueName(), String.valueOf(user.getId())));
        tytTransportDispatchViewDetail.setCarNickName(StringUtil.formatUserName(user.getUserName(), String.valueOf(user.getId())));
        tytTransportDispatchViewDetail.setCarPhone(user.getCellPhone());
        //1:查看  2：联系
        tytTransportDispatchViewDetail.setType(type);
        tytTransportDispatchViewDetail.setCreateTime(new Date());
        tytTransportDispatchViewDetail.setModifyTime(new Date());
        tytTransportDispatchViewDetailMapper.insert(tytTransportDispatchViewDetail);
    }

    private void saveOrUpdateView(User user, Long srcMsgId, Integer type) {
        Example example = new Example(TytTransportDispatchView.class);
        example.createCriteria().andEqualTo("srcMsgId", srcMsgId)
                .andEqualTo("carUserId", user.getId());
        TytTransportDispatchView tytTransportDispatchView = tytTransportDispatchViewMapper.selectOneByExample(example);
        if (tytTransportDispatchView == null) {
            tytTransportDispatchView = new TytTransportDispatchView();
            tytTransportDispatchView.setSrcMsgId(srcMsgId);
            tytTransportDispatchView.setCarUserId(user.getId());
            tytTransportDispatchView.setCarUserName(StringUtil.formatUserName(user.getTrueName(), String.valueOf(user.getId())));
            tytTransportDispatchView.setCarNickName(StringUtil.formatUserName(user.getUserName(), String.valueOf(user.getId())));
            tytTransportDispatchView.setCarPhone(user.getCellPhone());
            //1:查看  2：联系
            if (type == 1) {
                tytTransportDispatchView.setViewTime(new Date());
                tytTransportDispatchView.setViewCount(1);
                tytTransportDispatchView.setContactCount(0);
            } else {
                tytTransportDispatchView.setContactTime(new Date());
                tytTransportDispatchView.setContactCount(1);
                tytTransportDispatchView.setViewCount(0);
            }
            tytTransportDispatchView.setCreateTime(new Date());
            tytTransportDispatchView.setModifyTime(new Date());
            tytTransportDispatchViewMapper.insert(tytTransportDispatchView);
        } else {
            //1:查看  2：联系
            if (type == 1) {
                tytTransportDispatchView.setViewTime(new Date());
                tytTransportDispatchView.setViewCount(tytTransportDispatchView.getViewCount() == null
                        ? 0 : tytTransportDispatchView.getViewCount() + 1);
            } else {
                tytTransportDispatchView.setContactTime(new Date());
                tytTransportDispatchView.setContactCount(tytTransportDispatchView.getContactCount() == null
                        ? 0 : tytTransportDispatchView.getContactCount() + 1);
            }
            tytTransportDispatchView.setModifyTime(new Date());
            tytTransportDispatchViewMapper.updateByPrimaryKeySelective(tytTransportDispatchView);
        }

    }

}
