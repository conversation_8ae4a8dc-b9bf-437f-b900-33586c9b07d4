package com.tyt.infofee.service.impl;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.TradeInfoService;
import com.tyt.model.TytTradeinfo;

@Service(value = "tradeInfoService")
public class TradeInfoServiceImpl extends BaseServiceImpl<TytTradeinfo, Long> implements TradeInfoService {
	public Logger logger = LoggerFactory.getLogger("TradeInfoServiceImpl");

	@Resource(name = "tradeInfoDao")
	public void setBaseDao(BaseDao<TytTradeinfo, Long> tradeInfoDao) {
		super.setBaseDao(tradeInfoDao);
	}
}
