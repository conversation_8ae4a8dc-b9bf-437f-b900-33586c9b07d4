package com.tyt.infofee.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.infofee.service.CardPrefixBankInfoService;
import com.tyt.model.CardPrefixBankInfo;
import com.tyt.util.Constant;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
//银行卡号识别接口Service
@Service("cardPrefixBankInfoService")
public class CardPrefixBankInfoServiceImpl extends BaseServiceImpl<CardPrefixBankInfo, Long> implements CardPrefixBankInfoService {
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	//银行对应的缩写    支付宝专用，其他地方的缩写不一定一样
	public static HashMap<String, String> bankMap = new HashMap<String, String>() {
		private static final long serialVersionUID = 8101110561547696714L;
		{
			put("BOC", "中国银行");
			put("CCB", "建设银行");
			put("ABC", "农业银行");
			put("ICBC", "工商银行");
			put("PSBC", "邮政储蓄");
			put("CITIC", "中信银行");
			put("ECITIC", "中信银行");
			put("CEB", "光大银行");
			put("HXBANK", "华夏银行");
			put("HXB", "华夏银行");
			put("CMB", "招商银行");
			put("CIB", "兴业银行");
			put("SPDB", "浦发银行");
			put("PINGAN", "平安银行");
			put("SPABANK", "平安银行");
			put("GDB", "广发银行");
			put("BCCB", "北京银行");
			put("BJBANK", "北京银行");
			put("BJRCB", "北京农村商业银行");
			put("SHB", "上海银行");
			put("CMBC", "民生银行");
			put("BOCO", "交通银行");
			put("BCM", "交通银行");
			put("COMM", "交通银行");
			put("SDB", "深圳发展银行");
			put("BHB", "河北银行");


		}
	};

	@Resource(name = "cacheServiceMcImpl")
	protected CacheService cacheService;

	@Resource(name = "cardPrefixBankInfoDao")
	public void setBaseDao(BaseDao<CardPrefixBankInfo, Long> cardPrefixBankInfoDao) {
		super.setBaseDao(cardPrefixBankInfoDao);
	}

	//获取所有的银行对应编码信息
	@Override
	public List<CardPrefixBankInfo> getAllCardPrefixBankInfo() {
		return this.getBaseDao().find("from CardPrefixBankInfo");
	}

	//缓存中不存在则获取阿里接口的银行信息
	@Override
	public String addRemoteAliBankName(String cardId) {
 		String url = "https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8&cardNo="+cardId+"&cardBinCheck=true";
 		String bankName = "";
		try {
			URLConnection connection = (URLConnection) new URL(url)
					.openConnection();
			connection.setDoOutput(true);
			InputStream os = connection.getInputStream();
			Thread.sleep(100);
			int length = os.available();
			byte[] buff = new byte[length];
			os.read(buff);
			String result= new String(buff, "utf-8");
			logger.info("阿里银行接口返回：{}",result);
			if(StringUtils.isEmpty(result)){
				return "";
			}
			Map map1 = JSON.parseObject(result);
			if(map1 == null || map1.size() <= 0 ){
				return "";
			}
			String state = String.valueOf(map1.get("stat"));
			String validated = String.valueOf(map1.get("validated"));
			if(!"ok".equals(state) && !"true".equals(validated)){
				logger.info("阿里银行接口查询失败或者解析后卡号未验证");
				return "";
			}
			String carType = String.valueOf(map1.get("cardType"));
			String bank = String.valueOf(map1.get("bank"));
			if(StringUtils.isEmpty(bank) || "null".equals(bank)){
				logger.info("阿里银行接口查询失败，解析后银行编码为空");
				return "";
			}
			logger.info("阿里银行接口返回结果解析成功");
			//查询成功的结果保存数据库
			CardPrefixBankInfo cpbi = new CardPrefixBankInfo();
			cpbi.setBankName(bank);
			//根据银行缩写查询中文名称
			bankName = getBankName(cpbi.getBankName());
			cpbi.setBankName(bankName);
			cpbi.setBankWord(bank);
			cardId = cardId.substring(0,6);
			cpbi.setCardId(cardId);
			cpbi.setCardType(carType);
			cpbi.setCtime(new Date());
			cpbi.setMtime(new Date());
			this.getBaseDao().insert(cpbi);
			cacheService.setString(Constant.CARDID_PREFIX_BANKNAME+cardId,bankName, Constant.CACHE_TYT_GEO_DICT_TIME);
			buff = null;
			os.close();
			connection = null;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return bankName;
	}

	private String getBankName(String bankWord){
		logger.info("匹配对应的银行中文名称{}",bankWord);
		return bankMap.get(bankWord) == null ? "" : bankMap.get(bankWord);
	}

	//获取缓存的高速线路信息，如果需要初始化则初始化
	@Override
	public String getCacheStringByInit(String queryKey){
		String flag = cacheService.getString(Constant.CARDID_PREFIX_BANKNAME+"update_flag");
		if(StringUtils.isEmpty(flag) || !"1".equals(flag)){
			logger.info("银行名称更新标识不存在，更新缓存");
			initBankName();
		}
		return cacheService.getString(queryKey);
	}

	@PostConstruct
	public void initBankName(){
		List<CardPrefixBankInfo> cardPrefixList = this.getAllCardPrefixBankInfo();
		if(cardPrefixList != null && cardPrefixList.size() > 0){
			logger.info("开始初始化银行对应前缀数据列表到缓存,{}",cardPrefixList.size());
		/*	for (CardPrefixBankInfo cardInfo: cardPrefixList){
				cacheService.setString( Constant.CARDID_PREFIX_BANKNAME+cardInfo.getCardId(),cardInfo.getBankName(),Constant.CACHE_TYT_GEO_DICT_TIME);//缓存一个月
			}*/
			cacheService.setString(Constant.CARDID_PREFIX_BANKNAME+"update_flag","1", Constant.CACHE_TYT_GEO_DICT_TIME);//缓存一个月
			logger.info("完成初始化银行对应前缀数据列表到缓存,{}",cardPrefixList.size());
		}else{
			logger.info("初始化银行对应前缀数据列表为空");
		}
	}

}
