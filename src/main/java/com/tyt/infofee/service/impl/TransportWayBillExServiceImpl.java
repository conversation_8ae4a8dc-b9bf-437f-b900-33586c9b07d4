package com.tyt.infofee.service.impl;

import cn.hutool.extra.emoji.EmojiUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.page.PageMethod;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.*;
import com.tyt.config.util.AppConfig;
import com.tyt.corp.CorpRestClient;
import com.tyt.corp.bean.OrderExListResponse;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.OrderStatusType;
import com.tyt.infofee.enums.YMMOrderStatusEnum;
import com.tyt.infofee.service.*;
import com.tyt.manbang.bean.mqBean.CarCreateExToYMMMsgBean;
import com.tyt.manbang.bean.mqBean.ShipperExceptionRequestBean;
import com.tyt.manbang.bean.request.CarExceptionReportingBean;
import com.tyt.manbang.bean.request.HandleExceptionInfoBean;
import com.tyt.manbang.bean.request.ShipperExceptionReportingBean;
import com.tyt.model.*;
import com.tyt.plat.mapper.base.TytTransportWaybillExMapper;
import com.tyt.transport.enums.OrderStatusEnum;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.tyt.common.bean.OrderConstants.THIRD_PARTY_PLATFORM_TYPE_YMM;

/**
 * User: Administrator Date: 13-11-10 Time: 下午5:10
 */
@SuppressWarnings("deprecation")
@Service("transportWayBillExService")
public class TransportWayBillExServiceImpl extends BaseServiceImpl<TytTransportWaybillEx, Long> implements TransportWayBillExService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());

	/*
	 * 异常处理后货方有钱的短信模板key
	 */
	private static final String EX_GOOD_HAVE_MONEY_KEY = "ex.good.have.money";
	/*
	 * 异常处理后货方无钱的短信模板key
	 */
	private static final String EX_GOOD_HAVE_NO_MONEY_KEY = "ex.good.have.no.money";

	/*
	 * 装车状态 0 未装车 1 已装车
	 */
	private static final int NO_LOADING_STATUS=0;
	private static final int ALREADY_LOADING_STATUS=1;

	/*
	 * 异常上报发起方 1 车方 2 货方
	 */
	private static final String CAR_EX_CREATE="1";
	private static final String GOODS_EX_CREATE="2";


	@Resource(name = "tytSequenceService")
	TytSequenceService tytSequenceService;

	@Resource(name = "tytBubbleService")
	TytBubbleService tytBubbleService;

	@Resource(name = "transportWayBillService")
	TransportWayBillService transportWayBillService;

	@Resource(name = "tytNoticeRemindService")
	TytNoticeRemindService tytNoticeRemindService;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "transportWayBillExVoucherService")
	private TransportWayBillExVoucherService transportWayBillExVoucherService;

	@Resource(name = "tytMessageTmplService")
	private TytMessageTmplService messageTmplService;

	@Resource(name = "userService")
	private UserService userService;


	@Resource(name = "transportOrdersService")
	private TransportOrdersService transportOrdersService;

	@Resource(name = "tytConfigService")
	TytConfigService tytConfigService;

	@Resource(name = "transportWaybillExEvaluateService")
	TytTransportWaybillExEvaluateService transportWaybillExEvaluateService;

	@Resource(name = "transportWayBillExService")
	TransportWayBillExService transportWayBillExService;

	@Autowired
	private TytTransportWaybillExMapper tytTransportWaybillExMapper;

	@Resource(name = "transportWayBillExDao")
	public void setBaseDao(BaseDao<TytTransportWaybillEx, Long> transportWayBillExDao) {
		super.setBaseDao(transportWayBillExDao);
	}

	public ListDataBean updateGetMyWayBillExList(Long userId, int queryActionType, int queryMenuType, long queryID) throws Exception {
		ListDataBean ListDataBean = new ListDataBean();
		List<Object> list = new ArrayList<Object>();
		int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");

		StringBuffer sql = new StringBuffer("select id, ts_order_no tsOrderNo,ts_id tsId,user_id userId,start_point startPoint," + "dest_point destPoint, task_content taskContent," + "ctime  publishTime,pay_link_phone payLinkPhone,pay_amount payAmount," + "ex_time exTime,ex_party exParty,ex_type exType,ex_other exOther,ex_status exStatus FROM tyt_transport_waybill_ex  where 1=1 ");

		switch (queryMenuType) {
		case 1:
			sql.append(" and pay_user_id=?");
			list.add(userId);
			break;
		case 2:
			sql.append(" and user_id=?");
			list.add(userId);
			break;
		}

		if (queryActionType == 2) {
			sql.append(" and id<?");
			list.add(queryID);
		}

		sql.append(" order by id desc ");

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("tsOrderNo", Hibernate.STRING);
		scalarMap.put("tsId", Hibernate.LONG);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("startPoint", Hibernate.STRING);
		scalarMap.put("destPoint", Hibernate.STRING);
		scalarMap.put("taskContent", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("payLinkPhone", Hibernate.STRING);
		scalarMap.put("payAmount", Hibernate.LONG);
		scalarMap.put("exTime", Hibernate.TIMESTAMP);
		scalarMap.put("exParty", Hibernate.STRING);
		scalarMap.put("exType", Hibernate.STRING);
		scalarMap.put("exOther", Hibernate.STRING);
		scalarMap.put("exStatus", Hibernate.STRING);

		List<TransportWayBillExListBean> twbl = this.getBaseDao().search(sql.toString(), scalarMap, TransportWayBillExListBean.class, list.toArray(), 1, pageSize);
		if (twbl != null && twbl.size() > 0) {
			ListDataBean.setData(twbl);
		}
		// 如果是装货完成 要修改气泡数为0
		if (queryMenuType == 1) {
			int n = tytBubbleService.updateBubbleNumber(userId, "1", "5", 0);
			logger.info("修改了type1{},type2{},{}条气泡数据", "1", "5", n);
		} else {
			int n = tytBubbleService.updateBubbleNumber(userId, "2", "3", 0);
			logger.info("修改了type1{},type2{},{}条气泡数据", "2", "3", n);
		}
		ListDataBean.setBubbleNumbers(tytBubbleService.getInfoFeeMyPublishBubbleResultBeanForUserId(userId));
		ListDataBean.setCurrentTime(System.currentTimeMillis());
		return ListDataBean;
	}

	@Override
	public ListDataBean updateGetMergeExList(Long userId, int queryActionType, int queryMenuType, long queryID, long queryCID) throws Exception {
		ListDataBean returnBean = new ListDataBean(); // 回传对象
		int pageSize = AppConfig.getIntProperty("info.fee.query.page.size"); // 每页条数
		//1. 获取C端异常上报数据
		ListDataBean cdataBean = this.updateGetMyWayBillExList(userId, queryActionType, queryMenuType, queryID);
		System.out.println(cdataBean.getBubbleNumbers());

		//2. 获取B端异常上报数据
		OrderExListResponse bdataBean = CorpRestClient.getInstance().getMyOrderExList(userId, queryActionType, queryMenuType, queryCID, pageSize);
		// C端异常上报数据
		List<TransportWayBillExListBean> ctexl = cdataBean.getData();
		// B端异常上报数据
		List<TransportWayBillExListBean> btexl = null;
		if(bdataBean != null) {
			btexl = bdataBean.getData();
		}
		// ALL异常上报数据
		List<TransportWayBillExListBean> allExList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(ctexl)) {
			allExList.addAll(ctexl);
		}
		if (CollectionUtils.isNotEmpty(btexl)) {
			allExList.addAll(btexl);
		}

		// 数据截取，并按createTime 倒序进行排序
		List<TransportWayBillExListBean> listResult = allExList
				.stream()
				.sorted(new Comparator<TransportWayBillExListBean>() { // 时间倒序
					@Override
					public int compare(TransportWayBillExListBean o1, TransportWayBillExListBean o2) {
						int flag = o2.getExTime().compareTo(o1.getExTime());
						return flag;
					}
				}).limit(pageSize)
				.collect(Collectors.toList()); //收集并返回
		// 协助记录C端 和 B端 最小queryId
		Map<String, Long> queryIDMap = new HashMap<>();
		// 找出C端数据，最小的queryID
		if (CollectionUtils.isNotEmpty(ctexl)) {
			for (TransportWayBillExListBean t : ctexl) {
				boolean b = listResult.stream().anyMatch(u -> u.getUniqueId().equals(t.getUniqueId()));
				System.out.println(t.getUniqueId() + "-" + b);
				if (b) {
					queryIDMap.put("plat", t.getId());
				}
			}
		}
		// 找出B端数据，最小的queryID
		if (CollectionUtils.isNotEmpty(btexl)) {
			for (TransportWayBillExListBean t : btexl) {
				boolean b = listResult.stream().anyMatch(u -> u.getUniqueId().equals(t.getUniqueId()));
				System.out.println(t.getUniqueId() + "-" + b);
				if (b) {
					queryIDMap.put("corp", t.getId());
				}
			}
		}
		System.out.println(queryIDMap.get("plat"));
		System.out.println(queryIDMap.get("corp"));


		// 设置queryID
		Long reQueryID = NumberUtils.toLong(String.valueOf(queryIDMap.get("plat")), 0);
		if (queryID !=0 && reQueryID == 0) {
			reQueryID = queryID;
		}
		Long reQueryCID = NumberUtils.toLong(String.valueOf(queryIDMap.get("corp")), 0);
		if (queryCID != 0 && reQueryCID == 0) {
			reQueryCID = queryCID;
		}
		returnBean.setQueryID(reQueryID);
		returnBean.setQueryCID(reQueryCID);
		returnBean.setData(listResult);

		// 处理气泡
		List<InfoFeeMyPublishBubbleResultBean> cbubble = cdataBean.getBubbleNumbers(); // c端气泡
		List<InfoFeeMyPublishBubbleResultBean> bbubble = null; // b端气泡
		if(bdataBean != null) {
			bbubble = bdataBean.getBubbleNumbers(); // b端气泡
		}
		// 遍历多端气泡，相加
		List<InfoFeeMyPublishBubbleResultBean> allBubble = tytBubbleService.mergeBubble(cbubble, bbubble);
		returnBean.setBubbleNumbers(allBubble);
		returnBean.setCurrentTime(cdataBean.getCurrentTime());
		return returnBean;
	}

	public int save(Long userId, String tsOrderNo, String exParty, String exType, String exOther, TytTransportWaybill transportWayBill, Long srcMsgId) {
		// tyt_plat_transport_optimize20171123 货源信息优化 删除代码： TytTransportWaybill
		// transportWayBill
		// =transportWayBillService.getTytTransportWaybillForLock(tsOrderNo);
		if (transportWayBill == null) {
			return 500;
		}
		if (transportWayBill.getInfoStatus().equals("4") || transportWayBill.getInfoStatus().equals("3")) {
			return 210903;
		}
		if (exParty.equals("2") && transportWayBill.getInfoStatus().equals("5")) {
			return 210901;
		}
		if (exParty.equals("1") && transportWayBill.getInfoStatus().equals("5")) {
			return 210902;
		}
		if (!transportWayBill.getInfoStatus().equals("2")) {
			return 501;
		}
		TytTransportWaybillEx waybillEx = new TytTransportWaybillEx();
		BeanUtils.copyProperties(transportWayBill, waybillEx);
		waybillEx.setExStatus("0");
		waybillEx.setExParty(exParty);
		waybillEx.setExType(exType);
		waybillEx.setExTime(new Date());
		waybillEx.setMtime(new Date());
		if ((exParty.equals("1") && exType.equals("8")) || (exParty.equals("2") && exType.equals("2"))) {
			waybillEx.setExOther(exOther);
		}
		this.getBaseDao().insert(waybillEx);
		// tyt_plat_transport_optimize20171123 货源信息优化
		String sql = "update tyt_transport set info_status=? ,mtime=now() where src_msg_id=? and status=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { "5", srcMsgId, 4 });
		// tyt_plat_transport_optimize20171123 货源信息优化
		// 修改 transport_mian 表
		sql = "update tyt_transport_main set info_status=? ,mtime=now() where id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { "5", srcMsgId });
		// 修改 tyt_transport_waybill
		sql = "update tyt_transport_waybill set info_status=? ,mtime=now(),sort_id=? where ts_order_no=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { "5", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME), tsOrderNo });
		// 修改tyt_transport_orders表
		sql = "update tyt_transport_orders set rob_status=? ,mtime=now(),sort_id=? where pay_user_id=? and ts_order_no=? and rob_status=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { "7", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME), transportWayBill.getPayUserId(), tsOrderNo, "4" });
		// 发送弹窗通知
		if (exParty.equals("1")) {
			tytNoticeRemindService.saveNoticeRemind(exParty.equals("1") ? "2" : "1", exParty.equals("1") ? "4" : "6", transportWayBill.getTsOrderNo(), transportWayBill.getPayUserId(), transportWayBill.getUserId());
		} else {
			tytNoticeRemindService.saveNoticeRemind(exParty.equals("1") ? "2" : "1", exParty.equals("1") ? "4" : "6", transportWayBill.getTsOrderNo(), transportWayBill.getUserId(), transportWayBill.getPayUserId());
		}
		// 气泡
		// 发货方 违约/异常加一
		tytBubbleService.updateBubbleNumber(transportWayBill.getUserId(), "2", "3", 1);
		// 车主方 违约/异常加一
		tytBubbleService.updateBubbleNumber(transportWayBill.getPayUserId(), "1", "5", 1);
		// 车主方 待装货车主气泡减一
		tytBubbleService.updateBubbleNumber(transportWayBill.getPayUserId(), "1", "2", -1);
		return 200;
	}

	@Override
	public GoodsDetailOrderExceptionBean getGoodsDetail(String tsOrderNo) throws Exception {
		String selectSQL = "select ex_time time,complete_time finishedTime,result_opinion lastResult," + "car_amount carOwnerMoney,goods_amount goodsOwnerMoney,ex_status exStatus" + " from tyt_transport_waybill_ex where ts_order_no=:tsOrderNo";
		Map<String, Type> paramsMap = new HashMap<String, Type>();
		paramsMap.put("time", Hibernate.TIMESTAMP);
		paramsMap.put("finishedTime", Hibernate.TIMESTAMP);
		paramsMap.put("lastResult", Hibernate.STRING);
		paramsMap.put("carOwnerMoney", Hibernate.LONG);
		paramsMap.put("goodsOwnerMoney", Hibernate.LONG);
		paramsMap.put("exStatus", Hibernate.STRING);
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("tsOrderNo", tsOrderNo);
		return this.getBaseDao().queryByMap(selectSQL, params, GoodsDetailOrderExceptionBean.class, paramsMap);
	}

	@Override
	public Integer isExFeedBack(Long tsOrderId, Long userId) {
		String selectSQL = "select CASE a.cost_status WHEN 50 THEN IF(trwe.id is null,0,1) ELSE 1 END AS evaluateStatus " +
				"from tyt_transport_orders a FORCE INDEX (Index_user_id_status_time) left join tyt_transport_order_snapshot b on a.ts_id = b.src_msg_id and a.id = b.order_id " +
				" LEFT JOIN tyt_transport_waybill_ex tre ON tre.order_id = a.id and tre.ex_status=2 AND tre.order_type = 0 " +
				"LEFT JOIN tyt_transport_waybill_ex_evaluate trwe ON trwe.ex_id = tre.id and trwe.user_id= :userId where a.id = :tsOrderId";
		Map<String, Object> params = new HashMap<>();
		params.put("userId", userId);
		params.put("tsOrderId", tsOrderId);
		BigInteger evaluateStatus = this.getBaseDao().queryByMap(selectSQL, params, null, null);
		return evaluateStatus.intValue();
	}

	@Override
    public void saveEx(TytTransportOrders orders, String exParty,String exType, String exOther,Integer loadingStatus,Integer loadingChildStatus,
					   String proofReason,
					   String pictureVouchers,Long userId) throws Exception{
        TytTransportWaybillEx waybillEx = new TytTransportWaybillEx();
        BeanUtils.copyProperties(orders, waybillEx);
        waybillEx.setOrderId(orders.getId());
        waybillEx.setExStatus("0");
        waybillEx.setExParty(exParty);
        waybillEx.setExType(exType);
        waybillEx.setExTime(new Date());
        waybillEx.setMtime(new Date());

		//订单类型：0.异常上报订单  1.投诉订单
		waybillEx.setOrderType(0);
        // 9 为信息费冻结转异常上报
        if ((exParty.equals("1") && (exType.equals("8") || exType.equals("9"))) || (exParty.equals("2") && exType.equals("2"))) {
            waybillEx.setExOther(exOther);
        }
        Long exId = (Long) this.getBaseDao().insertWithReturnId(waybillEx);
        if(StringUtils.isNotBlank(proofReason) || StringUtils.isNotBlank(pictureVouchers)){
			TytTransportWaybillExVoucher exVoucher = new TytTransportWaybillExVoucher();
			exVoucher.setUserId(userId);
			if(StringUtils.isNotBlank(proofReason)){
				//根据产品要求 满帮货源同步过来的异常上报信息 不过滤
				String s=proofReason;
				if(orders.getThirdpartyPlatformType()!=2){
					s = StringUtil.filterPunctuation(EmojiUtil.removeAllEmojis(proofReason));
				}
				exVoucher.setProofReason(s);
			}
			exVoucher.setPictureVouchers(pictureVouchers);
			exVoucher.setExParty(exParty);
			exVoucher.setCtime(new Date());
			exVoucher.setTsId(waybillEx.getTsId());
			exVoucher.setOrderId(waybillEx.getOrderId());
            exVoucher.setExId(exId);
			exVoucher.setVoucherType(1L);
			transportWayBillExVoucherService.addSave(exVoucher);
		}
        TytTransportWaybill waybill = transportWayBillService.getById(orders.getTsOrderNo());
        if ("2".equals(waybill.getInfoStatus())){
            // 修改 tyt_transport_waybill
            String sql = "update tyt_transport_waybill set info_status=? ,mtime=now(),sort_id=? where ts_order_no=?";
            this.getBaseDao().executeUpdateSql(sql, new Object[] { "5", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME), orders.getTsOrderNo() });
        }
        // 修改tyt_transport_orders表
        String sql = "update tyt_transport_orders set rob_status=?,cost_status=?,loading_status=?, loading_child_status=?,mtime=now(),order_new_status = ? where id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[] { "7", OrderStatusType.EX_REPORT.getStatus(),loadingStatus,loadingChildStatus, OrderStatusEnum.WAIT_LOADED.getCode(),orders.getId()});

        int opStatus = 0;
        if ("1".equals(exParty)){
            opStatus=8;
        }else if ("2".equals(exParty)){
            opStatus=9;
        }
        //发mq 记录日志，发送短信，添加气泡，发送push消息
        tytMqMessageService.saveAndSendInfofeeMq(orders,opStatus,null);

        //特运通货源 在满帮生成的订单 货方发送请求集团发起异常上报
		if(orders.getThirdpartyPlatformType()!=null && orders.getThirdpartyPlatformType()==1&&"2".equals(exParty)){
			//组装发送mq的实体类
			ShipperExceptionRequestBean shipperExceptionRequestBean = new ShipperExceptionRequestBean();
			shipperExceptionRequestBean.setOrderId(orders.getThirdpartyPlatformOrderNo());
			shipperExceptionRequestBean.setComplainType(Integer.valueOf(exType));
			shipperExceptionRequestBean.setContent(proofReason);
			if(StringUtils.isNotBlank(pictureVouchers)){
				shipperExceptionRequestBean.setAttachments(Arrays.asList(pictureVouchers.split(",")));
			}
			shipperExceptionRequestBean.setPlatformSource(1);
			shipperExceptionRequestBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
			shipperExceptionRequestBean.setMessageType(MqBaseMessageBean.MB_EXCEPTION_REQUEST_MESSAGE);
			// 建立线程池
			ExecutorService executorService = Executors.newSingleThreadExecutor();
			executorService.execute(() -> {
				//发送并mq信息并保存到数据库
				tytMqMessageService.addSaveMqMessage(shipperExceptionRequestBean.getMessageSerailNum(), JSON.toJSONString(shipperExceptionRequestBean), MqBaseMessageBean.MB_EXCEPTION_REQUEST_MESSAGE);
				tytMqMessageService.sendMbMqMessage(shipperExceptionRequestBean.getMessageSerailNum(), JSON.toJSONString(shipperExceptionRequestBean), MqBaseMessageBean.MB_EXCEPTION_REQUEST_MESSAGE);
			});
			// 关闭线程
			executorService.shutdown();
		}

		//满帮货源 在特运通生成的订单 车方发送请求集团发起异常上报
		if(orders.getThirdpartyPlatformType()!=null && orders.getThirdpartyPlatformType()==2&&"1".equals(exParty)){
			//组装发送mq的实体类
			CarCreateExToYMMMsgBean carCreateExToYMMMsgBean = new CarCreateExToYMMMsgBean();
			carCreateExToYMMMsgBean.setOrderId(orders.getThirdpartyPlatformOrderNo());
			carCreateExToYMMMsgBean.setComplainType(Integer.valueOf(exType));
			carCreateExToYMMMsgBean.setContent(proofReason);
			if(StringUtils.isNotBlank(pictureVouchers)){
				carCreateExToYMMMsgBean.setAttachments(Arrays.asList(pictureVouchers.split(",")));
			}
			carCreateExToYMMMsgBean.setPlatformSource(1);
			carCreateExToYMMMsgBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
			carCreateExToYMMMsgBean.setMessageType(MqBaseMessageBean.YMM_CAR_CREATE_EX_NOTIFY_MESSAGE);
			// 建立线程池
			ExecutorService executorService = Executors.newSingleThreadExecutor();
			executorService.execute(() -> {
				//发送并mq信息并保存到数据库
				tytMqMessageService.addSaveMqMessage(carCreateExToYMMMsgBean.getMessageSerailNum(), JSON.toJSONString(carCreateExToYMMMsgBean), MqBaseMessageBean.YMM_CAR_CREATE_EX_NOTIFY_MESSAGE);
				tytMqMessageService.sendMbMqMessage(carCreateExToYMMMsgBean.getMessageSerailNum(), JSON.toJSONString(carCreateExToYMMMsgBean), MqBaseMessageBean.YMM_CAR_CREATE_EX_NOTIFY_MESSAGE);
			});
			// 关闭线程
			executorService.shutdown();
		}
    }

	/**
	 * 订单发起投诉
	 *
	 * @param orders
	 * @param exParty  //异常上报方身份1车主上报，2货主上报
	 * @param exType  //异常上报类型
	 * @param exOther  //异常上报类型 其他类型信息
	 * @throws Exception
	 */
	@Override
	public void saveComplaint(TytTransportOrders orders, String exParty, String exType, String exOther, Integer loadingStatus,Integer loadingChildStatus, String proofReason, String pictureVouchers, Long userId) throws Exception {
		TytTransportWaybillEx waybillEx = new TytTransportWaybillEx();
		BeanUtils.copyProperties(orders, waybillEx);
		waybillEx.setOrderId(orders.getId());
		waybillEx.setExStatus("0");
		waybillEx.setExParty(exParty);
		waybillEx.setExType(exType);
		waybillEx.setExTime(new Date());
		waybillEx.setMtime(new Date());
		//订单类型：0.异常上报订单  1.投诉订单
		waybillEx.setOrderType(1);
		// 9 为信息费冻结转异常上报
		if ((exParty.equals("1") && (exType.equals("8") || exType.equals("9"))) || (exParty.equals("2") && exType.equals("2"))) {
			waybillEx.setExOther(exOther);
		}
        Long exId = (Long)this.getBaseDao().insertWithReturnId(waybillEx);
        if(StringUtils.isNotBlank(proofReason) || StringUtils.isNotBlank(pictureVouchers)){
			TytTransportWaybillExVoucher exVoucher = new TytTransportWaybillExVoucher();
			exVoucher.setUserId(userId);
			//update by sissy on 20220117
			if(StringUtils.isNotBlank(proofReason)){
				String s = StringUtil.filterPunctuation(EmojiUtil.removeAllEmojis(proofReason));
				exVoucher.setProofReason(s);
			}
			exVoucher.setPictureVouchers(pictureVouchers);
			exVoucher.setExParty(exParty);
			exVoucher.setCtime(new Date());
			exVoucher.setTsId(waybillEx.getTsId());
			exVoucher.setOrderId(waybillEx.getOrderId());
            exVoucher.setExId(exId);
			exVoucher.setVoucherType(1L);
			transportWayBillExVoucherService.addSave(exVoucher);
		}

		// 修改tyt_transport_orders表
		String sql = "update tyt_transport_orders set is_complaint = 1, loading_status=?,loading_child_status=?, mtime=now() where is_complaint = 0 and id = ? ";
		this.getBaseDao().executeUpdateSql(sql,new Object[]{loadingStatus,loadingChildStatus, orders.getId()});

		//发mq 发起工单
		tytMqMessageService.saveAndSendInfofeeMq(orders,16,null);
	}

	@Override
	public void addVoucher(TytTransportOrders orders,Long exId, String exParty, String proofReason, String pictureVouchers, Long userId) {
		TytTransportWaybillExVoucher exVoucher = new TytTransportWaybillExVoucher();
		exVoucher.setUserId(userId);
		//update by sissy on 20220117
		if(StringUtils.isNotBlank(proofReason)){
			String s = StringUtil.filterPunctuation(EmojiUtil.removeAllEmojis(proofReason));
			exVoucher.setProofReason(s);
		}
		exVoucher.setPictureVouchers(pictureVouchers);
		exVoucher.setExParty(exParty);
		exVoucher.setCtime(new Date());
		exVoucher.setTsId(orders.getTsId());
		exVoucher.setOrderId(orders.getId());
		exVoucher.setExId(exId);
		exVoucher.setVoucherType(2L);
		transportWayBillExVoucherService.addSave(exVoucher);
	}

	@Override
	public void HandlerTransportWaybillExForThirdPlatform(String orderId, Integer loadingStatus, Long carAmount, Long goodsAmount) throws Exception {
		 //第一步:取实体类
		String sql = "SELECT * FROM tyt_transport_waybill_ex WHERE order_id = ? and ex_status in (0,1) ORDER BY ctime DESC ";
		List<TytTransportWaybillEx> transportWaybillExList = this.getBaseDao().queryForList(sql, new Object[]{orderId});
		if (transportWaybillExList != null && transportWaybillExList.size() > 0) {
			TytTransportWaybillEx tytTransportWaybillEx = transportWaybillExList.get(0);
			//第二步:更新信息
			String updateSql = "UPDATE tyt_transport_waybill_ex SET ex_fault_party=?,ex_type_other=?,result_opinion=?,ex_status=?,car_amount=?," +
					"goods_amount=?,mtime=NOW(),complete_time=NOW() WHERE id=? ";
			Object[] updateParams = new Object[]{4,6,"满帮成交订单,车方已取消异常上报",2, carAmount, goodsAmount, tytTransportWaybillEx.getId()};
			this.getBaseDao().executeUpdateSql(updateSql, updateParams);
			//第三步:插入结果表
			String insertSql = "INSERT INTO `tyt`.`tyt_transport_waybill_ex_result` (`ex_id`, `opinion`, `is_last`, `user_id`, `cell_phone`, `ctime`, `status`, `loading_status`) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
			Object[] insertParams = new Object[]{tytTransportWaybillEx.getId(), "满帮成交订单,车方已取消异常上报", 1, null, "", new Date(), 0, loadingStatus};
			this.getBaseDao().executeUpdateSql(insertSql, insertParams);
		}
	}

	@Override
	public void saveCarExceptionReporting(CarExceptionReportingBean carExceptionReportingBean, TytTransportOrders orders) throws Exception {
		//上报凭证
		String attachments = carExceptionReportingBean.getAttachments();
		String urlStr ="";
		if(StringUtils.isNotBlank(attachments)){
			attachments = URLDecoder.decode(attachments,"UTF-8");
			String[] attachmentArr = attachments.split(",");
			List<String> urlArr = new ArrayList<>();
			for(String attachment : attachmentArr){
				logger.info("saveCarExceptionReporting download file attachment:{}",attachment);

				String fileName= DownFileUtil.getSaveFilePathName("exception",System.currentTimeMillis()+".jpg");
				String filepath = AppConfig.getProperty("picture.path.domain")+fileName;
				DownFileUtil.downloadNew(attachment, filepath);

				urlArr.add(tytConfigService.getStringValue("prefix_picture","http://dev.teyuntong.net/rootdata")+fileName);
			}
			urlStr = urlArr.stream().collect(Collectors.joining(","));
		}

		logger.info("saveCarExceptionReporting 上报凭证 urlStr:{}",urlStr);
		//集团运单状态为 25已装车、30货已送达、60已收回单、90已完成 对应已装车，其余状态对应未装车 2022.10.28 姬雨池、张红玉沟通结果
		Integer loadingStatus = 0;
		if("25".equals(carExceptionReportingBean.getOrderStatus())||"30".equals(carExceptionReportingBean.getOrderStatus())||
				"60".equals(carExceptionReportingBean.getOrderStatus())||"90".equals(carExceptionReportingBean.getOrderStatus())){
			loadingStatus = 1;
		}
		this.saveEx(orders, CAR_EX_CREATE, carExceptionReportingBean.getComplainType(),
				null,loadingStatus,null,carExceptionReportingBean.getContent(),urlStr,orders.getPayUserId());

	}

	@Override
	public void saveShipperExceptionReporting(ShipperExceptionReportingBean shipperExceptionReportingBean, TytTransportOrders orders) throws Exception {
		//上报凭证
		String attachments = shipperExceptionReportingBean.getAttachments();
		String urlStr = "";
		if (StringUtils.isNotBlank(attachments)) {
			attachments = URLDecoder.decode(attachments, "UTF-8");
			String[] attachmentArr = attachments.split(",");
			List<String> urlArr = new ArrayList<>();
			Long begin = System.currentTimeMillis();
			for (String attachment : attachmentArr) {

				logger.info("saveShipperExceptionReporting download file attachment:{}", attachment);

				String fileName = DownFileUtil.getSaveFilePathName("exception", System.currentTimeMillis() + ".jpg");
				String filepath = AppConfig.getProperty("picture.path.domain") + fileName;
				DownFileUtil.downloadNew(attachment, filepath);
				logger.info("saveShipperExceptionReporting download file step1 time:{}",System.currentTimeMillis()-begin);


				urlArr.add(tytConfigService.getStringValue("prefix_picture", "http://dev.teyuntong.net/rootdata") + fileName);
			}
			urlStr = urlArr.stream().collect(Collectors.joining(","));
		}
		logger.info("saveShipperExceptionReporting 上报凭证 urlStr:{}", urlStr);

		//由于【运满满】撤销异常上报通知不会同步至TYT 且订单在【运满满】撤销后可以再次发起
		//此处需查询该订单在TYT是否存在 异常上报处理中状态 如果存在则进行撤销操作
//		transportWayBillExService.HandlerTransportWaybillExForThirdPlatform(orders.getId().toString(), orders.getLoadingStatus(), orders.getPayAmount(), 0L);

		//集团运单状态为 25已装车、30货已送达、60已收回单、90已完成 对应已装车，其余状态对应未装车
		Integer loadingStatus = NO_LOADING_STATUS;
		if (YMMOrderStatusEnum.LOADED.getCode().toString().equals(shipperExceptionReportingBean.getOrderStatus()) || YMMOrderStatusEnum.ARRIVING.getCode().toString().equals(shipperExceptionReportingBean.getOrderStatus()) ||
				YMMOrderStatusEnum.RECEIPTED.getCode().toString().equals(shipperExceptionReportingBean.getOrderStatus()) || YMMOrderStatusEnum.COMPLETE.getCode().toString().equals(shipperExceptionReportingBean.getOrderStatus())) {
			loadingStatus = ALREADY_LOADING_STATUS;
		}
		this.saveEx(orders, GOODS_EX_CREATE, shipperExceptionReportingBean.getComplainType(),
				null, loadingStatus, null, shipperExceptionReportingBean.getContent(), urlStr, orders.getPayUserId());

	}




	@Override
	public void handleExceptionInfo(HandleExceptionInfoBean handleExceptionInfoBean, TytTransportOrders orders) throws Exception {
		//货物内容
		String taskContent = orders.getTaskContent();
		//货方userId
		String goodsUserId = String.valueOf(orders.getUserId());
		//车方userId
		String carUserId = String.valueOf(orders.getPayUserId());
		//运单号
		String tsOrderNo = orders.getTsOrderNo();
		//分配给车方费用(单位元)
		String car_amountStr = new BigDecimal(handleExceptionInfoBean.getDriverAmount()).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString();;
		//分配给货方费用(单位元)
		String goods_amountStr = new BigDecimal(handleExceptionInfoBean.getShipperAmount()).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
		//总支付费用(单位元)
		String payAmountStr = orders.getPayAmount()+"";

		//1.判断分配金额是否大于信息费
		if (org.apache.commons.lang.StringUtils.isNotBlank(payAmountStr)) {
			double car_amountd = Double.parseDouble(car_amountStr);
			long car_amount = (long) (car_amountd * 100);
			double goods_amountd = Double.parseDouble(goods_amountStr);
			long goods_amount = (long) (goods_amountd * 100);
			logger.info("异常上报处理结果车方：{}元，货方：{}元",car_amount,goods_amount);
			long sum_amount = car_amount + goods_amount;
			double payAmountd = Double.parseDouble(payAmountStr);
			//long payAmount = (long) (payAmountd * 100);
			long payAmount = (long) payAmountd ;
			if (sum_amount > payAmount) {
				throw new RuntimeException("分配金额大于信息费！");
			}
		}
		//2.给货主发送短信通知(mq)
		if (org.apache.commons.lang.StringUtils.isNotBlank(goods_amountStr)) {
			double goods_amountd = Double.parseDouble(goods_amountStr);
			long goods_amount = (long) (goods_amountd * 100);
//				reqMap.put("goods_amount", goods_amount + "");
			String content = null;
			ShortMsgBean goodsShortMsgBean = new ShortMsgBean();

			// 根据分配给车主的金额是否为0发送不同的短信给车主
			if (goods_amount > 0) {
				// 根据短信key获取短信模板
				content = messageTmplService.getSmsTmpl(EX_GOOD_HAVE_MONEY_KEY, "运单号${orderNum}纠纷处理完成，信息费${money}元已转入您的钱包，请登录特运通查看");
				content = org.apache.commons.lang.StringUtils.replaceEach(content, new String[]{"${orderNum}", "${money}"}, new String[]{tsOrderNo, goods_amountd + ""});
			} else {
				// 根据短信key获取短信模板
				content = messageTmplService.getSmsTmpl(EX_GOOD_HAVE_NO_MONEY_KEY, "运单号${orderNum}纠纷处理完成，请登录特运通查看");
				content = org.apache.commons.lang.StringUtils.replaceEach(content, new String[]{"${orderNum}"}, new String[]{tsOrderNo});
			}
			goodsShortMsgBean.setContent(content);
			goodsShortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			String messageSerailNum = SerialNumUtil.generateSeriaNum();
			goodsShortMsgBean.setMessageSerailNum(messageSerailNum);
			User goodsUser = userService.getById(orders.getUserId());
			goodsShortMsgBean.setCell_phone(goodsUser.getCellPhone());
			goodsShortMsgBean.setRemark("");
			String messageContent = JSON.toJSONString(goodsShortMsgBean);
			logger.info("exception deal goods send mq message is: " + goodsShortMsgBean);
			tytMqMessageService.addSaveMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
			tytMqMessageService.sendMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
		} else {
			throw new RuntimeException("没有分配货主金额！");
		}


        //4.更新运单异常上报表的记录
	    TytTransportWaybillEx tytTransportWaybillEx = this.getWayBillExByOrderId(orders.getId(),0);
		if(null==tytTransportWaybillEx){
			throw new RuntimeException("不存在异常上报信息！");
		}
		tytTransportWaybillEx.setExStatus("2");
		tytTransportWaybillEx.setCtime(new Date());
		tytTransportWaybillEx.setCompleteTime(new Date(handleExceptionInfoBean.getCloseTime()));
		tytTransportWaybillEx.setResultOpinion(handleExceptionInfoBean.getConclusion());
		tytTransportWaybillEx.setCarAmount(handleExceptionInfoBean.getDriverAmount());
		tytTransportWaybillEx.setGoodsAmount(handleExceptionInfoBean.getShipperAmount());
		tytTransportWaybillEx.setOrderId(orders.getId());
		tytTransportWaybillEx.setExTypeOther("11");
		tytTransportWaybillEx.setExFaultParty(handleExceptionInfoBean.getResponsibleParty()+"");
		tytTransportWaybillEx.setResultOpinion(handleExceptionInfoBean.getConclusion());
		this.update(tytTransportWaybillEx);

		//第三步:插入结果表
		String insertSql = "INSERT INTO `tyt`.`tyt_transport_waybill_ex_result` (`ex_id`, `opinion`, `is_last`, `user_id`, `cell_phone`, `ctime`, `status`, `loading_status`) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
		Object[] insertParams = new Object[]{tytTransportWaybillEx.getId(), handleExceptionInfoBean.getConclusion(), 1, null, "", new Date(), 0, orders.getLoadingStatus()};
		this.getBaseDao().executeUpdateSql(insertSql, insertParams);


		//4.更新信息费表的接单状态 rob_status为 13.异常处理完成  cost_status为 50.异常完成
		Long orderId = orders.getId();
		int updateRobStatus = transportOrdersService.saveChangeRobStatusByIdNew(orderId, "13", 50);
		if (updateRobStatus > 0) {
			logger.info("信息费表的接单状态更新成功！更新成功条数：{}", updateRobStatus);
		}
		//5.更新运单表的信息费状态 info_status为 7.异常处理完成
		int updateInfoStatus = transportWayBillService.saveChangeInfoStatus(orders.getUserId(),tsOrderNo, "7");
		if (updateInfoStatus > 0) {
			logger.info("运单表的信息费状态更新成功！更新成功条数：{}", updateInfoStatus);
		}

		//发mq 记录日志，发送短信，添加气泡，发送push消息
		tytMqMessageService.saveAndSendInfofeeMq(orders,11,null);
	}

	@Override
	public TytTransportWaybillEx getWayBillExByOrderId(Long orederId,Integer orderType) {
		String hql = "from TytTransportWaybillEx where order_id=? and order_type =?  order by id desc";
		List<TytTransportWaybillEx> list = this.getBaseDao().find(hql, orederId,orderType);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<TytTransportWaybillEx> getWayBillExsByOrderId(Long orderId,Integer orderType,String cancelExParty) {
        String hql="";
        if("0".equals(cancelExParty)){
            hql = "from TytTransportWaybillEx where order_id=? and order_type =? order by id desc limit 10 ";
            List<TytTransportWaybillEx> list = this.getBaseDao().find(hql, orderId,orderType);
            return list;
        }
        if("1".equals(cancelExParty)){
            hql = "from TytTransportWaybillEx where order_id=? and order_type =? and ex_party=? order by id desc limit 10 ";
        }
        if("2".equals(cancelExParty)){
            hql = "from TytTransportWaybillEx where order_id=? and order_type =? and ex_party=? order by id desc limit 10 ";
        }
        List<TytTransportWaybillEx> list = this.getBaseDao().find(hql, orderId,orderType,cancelExParty);
        return list;

	}

	@Override
	public TytTransportWaybillEx getWayBillExByOrderIdAndUserId(Long orederId, Long userId) {
		//投诉单不能评价
		String hql = "from TytTransportWaybillEx where order_id=? and (user_id =? or pay_user_id =?) and order_type = 0 order by id desc";
		List<TytTransportWaybillEx> list = this.getBaseDao().find(hql, orederId,userId,userId);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public ResultMsgBean evaluate(Long orderId, Long userId, Integer evaluateParty,
								  Integer dealResultEvaluate, Integer dealTimeEvaluate,
								  Integer serviceAttitudeEvaluate, Integer decideResponsibilityEvaluate,String evaluateDetail) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");

		TytTransportWaybillEx tytTransportWaybillEx = this.getWayBillExByOrderIdAndUserId(orderId,userId);
		if(null == tytTransportWaybillEx){
			resultMsgBean.setCode(ReturnCodeConstant.EX_NONE);
			resultMsgBean.setMsg("该订单不存在异常上报");
			return resultMsgBean;
		}

		if(null==tytTransportWaybillEx.getExStatus()||!StringUtils.equals(tytTransportWaybillEx.getExStatus(),"2")){
			resultMsgBean.setCode(ReturnCodeConstant.EX_NONE_FINISH);
			resultMsgBean.setMsg("该订单异常上报未处理完成");
			return resultMsgBean;
		}

		TytTransportWaybillExEvaluate transportWaybillExEvaluate = new TytTransportWaybillExEvaluate();
		transportWaybillExEvaluate.setExId(tytTransportWaybillEx.getId());
		transportWaybillExEvaluate.setUserId(userId);
		transportWaybillExEvaluate.setDealTimeEvaluate(dealTimeEvaluate);
		transportWaybillExEvaluate.setDealResultEvaluate(dealResultEvaluate);
		transportWaybillExEvaluate.setServiceAttitudeEvaluate(serviceAttitudeEvaluate);
		transportWaybillExEvaluate.setDecideResponsibilityEvaluate(decideResponsibilityEvaluate);
		transportWaybillExEvaluate.setEvaluateIdentity(evaluateParty);
		if(StringUtils.isNotBlank(evaluateDetail)){
			transportWaybillExEvaluate.setRemark(StringUtil.filterPunctuation(EmojiUtil.removeAllEmojis(evaluateDetail)));
		}
		transportWaybillExEvaluate.setCtime(new Date());
		transportWaybillExEvaluate.setMtime(new Date());

		//获取投诉工单id
        Long recordId =getComplaintRecordId(tytTransportWaybillEx.getId());
        if (recordId!=null){
            transportWaybillExEvaluate.setRecordId(recordId);
            //修改工单回访状态
            updateComplaintRecordStatus(evaluateParty,recordId,dealResultEvaluate);
        }
		transportWaybillExEvaluateService.addSave(transportWaybillExEvaluate);
		return resultMsgBean;
	}


    @Override
    public List<MyComplaintListBean> getMyComplaintList(BaseParameter baseParameter, Integer page,
            Integer size) {
		try (com.github.pagehelper.Page<Object> objectPage = PageMethod.startPage(page, size)) {
			Long userId = baseParameter.getUserId();
			String clientSign = baseParameter.getClientSign();

			List<MyComplaintListBean> complaintOrdersList;

			if (Integer.parseInt(clientSign) == Constant.ClientSignEnum.ANDROID_CAR.code
					|| Integer.parseInt(clientSign) == Constant.ClientSignEnum.IOS_CAR.code) {
				complaintOrdersList = tytTransportWaybillExMapper.getMyComplaintList(null, userId);
			} else if (Integer.parseInt(clientSign) == Constant.ClientSignEnum.ANDROID_GOODS.code
					|| Integer.parseInt(clientSign) == Constant.ClientSignEnum.IOS_GOODS.code
					|| Integer.parseInt(clientSign) == Constant.ClientSignEnum.WEB_GOODS.code) {
				complaintOrdersList = tytTransportWaybillExMapper.getMyComplaintList(userId, null);
			} else {
				complaintOrdersList = Collections.emptyList();
			}

			complaintOrdersList.forEach(transportOrders -> {
				boolean isOwner = false;
				//如果用户ID等于货方账户ID
				if (userId.longValue() == transportOrders.getUserId().longValue()) {
					isOwner = true;
				}
				//订单状态描述
				String statusDesc = InfofeeDetailServiceImpl.infoFeeStatusDesc(isOwner,
						transportOrders.getRefundFlag(), String.valueOf(transportOrders.getCostStatus()),
						transportOrders.getDelayStatus(), transportOrders.getDelayRefundStatus(),
						transportOrders.getDeRefundDueDate());
				transportOrders.setStatusDesc(statusDesc);
			});
			return complaintOrdersList;
		}
	}

	@Override
	public ResultMsgBean cancelExSave(TytTransportOrders orders, Long userId, String cancelExParty) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        //根据订单信息查看当前异常上报信息
        Long orderId = orders.getId();
		List<TytTransportWaybillEx> wayBillExs = getWayBillExsByOrderId(orderId, 0,cancelExParty);
		if(wayBillExs==null||wayBillExs.size()==0){
			resultMsgBean.setCode(501);
			resultMsgBean.setMsg("该运单不存在异常上报信息");
			return resultMsgBean;
		}
		if(wayBillExs.size()>=2){
            resultMsgBean.setCode(501);
            resultMsgBean.setMsg("该运单只能操作一次撤销");
            return resultMsgBean;
        }
		//获取最新一条异常上报记录
        TytTransportWaybillEx wayBillEx = wayBillExs.get(0);
        //异常上报处理状态0初始化1处理中2处理完成3撤销
        if(Strings.isNotEmpty(wayBillEx.getExStatus())&&"2".equals(wayBillEx.getExStatus())){
            resultMsgBean.setCode(501);
            resultMsgBean.setMsg("该异常上报已处理完成,不能撤销异常上报");
            return resultMsgBean;
        }
        //异常上报处理状态0初始化1处理中2处理完成3撤销
        if(Strings.isNotEmpty(wayBillEx.getExStatus())&&"3".equals(wayBillEx.getExStatus())){
            resultMsgBean.setCode(501);
            resultMsgBean.setMsg("该异常上报已撤销完成,不能再次撤销异常上报");
            return resultMsgBean;
        }
        //异常上报处理状态0初始化1处理中2处理完成3撤销
        if(Strings.isNotEmpty(wayBillEx.getExParty())&&!cancelExParty.equals(wayBillEx.getExParty())){
            resultMsgBean.setCode(501);
            resultMsgBean.setMsg("只能自己撤销自己的上报");
            return resultMsgBean;
        }
		//处理相应业务逻辑
		resultMsgBean=cancelExSave(wayBillEx.getId(),orders.getExCancelStatus(),cancelExParty,orders.getId(),resultMsgBean);

        //第四步:发mq 同步工单状态
        tytMqMessageService.saveAndSendInfofeeMq(orders,17,null);

		//运满满同步过来的货源撤销同步给运满满
		if (orders.getThirdpartyPlatformType() == THIRD_PARTY_PLATFORM_TYPE_YMM) {
			this.sendMqForCancelNotifyYmm(orders);
			logger.info("【货源同步】发送异常上报撤销通知运满满MQ成功 运单号：{}",orders.getTsOrderNo());
		}
		return resultMsgBean;
	}

	/**
	* @description 发送异常上报撤销MQ
	* <AUTHOR>
	* @date 2023-8-24 13:32:35
	* @version 1.0
	*/
	private void sendMqForCancelNotifyYmm(TytTransportOrders orders) {
		OrderExCancelNotifyYmmMsg msg = new OrderExCancelNotifyYmmMsg();
		msg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
		msg.setMessageType(MqBaseMessageBean.YMM_ORDER_EX_CANCEL_NOTIFY_MESSAGE);
		msg.setOrderId(Long.valueOf(orders.getThirdpartyPlatformOrderNo()));
		try {
			tytMqMessageService.addSaveMqMessage(msg.getMessageSerailNum(),JSON.toJSONString(msg),msg.getMessageType());
			tytMqMessageService.sendMbMqMessage(msg.getMessageSerailNum(),JSON.toJSONString(msg),msg.getMessageType());
		} catch (Exception e) {
			logger.error("发送异常上报撤销MQ 异常:",e);
		}
	}

	@Override
	public ResultMsgBean cancelExSave(Long exId, Integer exCancelStatus, String cancelExParty, Long orderId, ResultMsgBean resultMsgBean) {
		try {
			//第一步:更新异常上报表(tyt_transport_waybill_ex)处理状态为3(已撤销) 和撤销上报时间cancel_ex_time
			String updateSql = "UPDATE tyt_transport_waybill_ex SET ex_status=?,cancel_ex_time=NOW(), mtime=NOW()  WHERE id=? ";
			Object[] updateParams = new Object[]{3, exId};
			this.getBaseDao().executeUpdateSql(updateSql, updateParams);

			//第二步:更新订单表(tyt_transport_orders)得cost_status
			//①:撤销异常上报后，自动到账时间和自动退款时间重新计算周期
			Date delayedPaymentTime = null;
			Date delayedRefundTime = null;

			//延迟付款T天数
			int delayedPaymentDays = Integer.parseInt(tytConfigService.getValue("delayedPaymentDays").getValue());
			delayedPaymentTime = TimeUtil.addDay(new Date(), delayedPaymentDays);
			//延迟退款T天数
			int delayedRefundDays = Integer.parseInt(tytConfigService.getValue("delayedRefundDays").getValue());
			delayedRefundTime = TimeUtil.addDay(new Date(), delayedRefundDays);

			//②:修改tyt_transport_orders表
			if (exCancelStatus == 0) {//当原本的撤销状态为初始化状态时  车方或货方更新 则进行更新具体的撤销方 1 车方 2 货方
				exCancelStatus = Integer.parseInt(cancelExParty);

			} else {
				//车方货方均操作了撤销
				exCancelStatus = 3;
			}
			String sql = "update tyt_transport_orders set cost_status = ?,ex_cancel_status=?,de_refund_dueDate=?,de_payment_dueDate=?,mtime=NOW() where id = ?";
			this.getBaseDao().executeUpdateSql(sql, new Object[]{OrderStatusType.PAY.getStatus(), exCancelStatus, delayedRefundTime, delayedPaymentTime, orderId});
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("服务器错误");
		}
		return resultMsgBean;
	}

	/**
     * 获取投诉工单id
     * @param exId
     * @return
     */
	private Long getComplaintRecordId(Long exId){
	    String sql = "SELECT id FROM `cs_complaint_record` WHERE service_id=?";
        BigInteger recordId = this.getBaseDao().query(sql, new Object[]{exId});
        if (recordId!=null && recordId.longValue()>0){
            return recordId.longValue();
        }
	    return null;
    }

    /**
     * 修改工单回访状态
     * @param part  1.车 2.货
     * @param recordId
     */
    private void updateComplaintRecordStatus(Integer part,Long recordId,Integer dealResultEvaluate){
        if (part!=1 && part!=2){
            return ;
        }
        StringBuffer sql = new StringBuffer("UPDATE `cs_complaint_record` SET");
	    if (part == 1){
            sql.append(" car_rvisit_status=2,car_satisfied=?");
        }
        if (part == 2){
            sql.append(" goods_rvisit_status=2,goods_satisfied=?");
        }
        sql.append(" where id=?");
        this.getBaseDao().executeUpdateSql(sql.toString(),new Object[]{dealResultEvaluate,recordId});
    }

	@Override
	public int selectCountForStatus(Long userId) {
		String sql = "SELECT count(1) FROM tyt_transport_waybill_ex twe left join tyt_transport_main tm on twe.ts_id = tm.src_msg_id" +
				"  WHERE twe.user_id=? and twe.ex_status in (0,1) and tm.excellent_goods = 1";
		BigInteger count = this.getBaseDao().query(sql, new Object[]{userId});
		return count.intValue();
	}

	@Override
	public TytTransportWaybillEx getExById(Long waybillExId) {
		String hql = "from TytTransportWaybillEx where id = ?";
		List<TytTransportWaybillEx> list = this.getBaseDao().find(hql, waybillExId);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}
}

