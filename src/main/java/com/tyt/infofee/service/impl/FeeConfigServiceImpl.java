package com.tyt.infofee.service.impl;

import com.tyt.infofee.service.FeeConfigService;
import com.tyt.plat.entity.base.TytFeeConfig;
import com.tyt.plat.mapper.base.TytFeeConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/03 13:48
 */
@Slf4j
@Service
public class FeeConfigServiceImpl implements FeeConfigService {

    @Autowired
    private TytFeeConfigMapper tytFeeConfigMapper;

    @Override
    public List<TytFeeConfig> listFeeConfig() {
        return tytFeeConfigMapper.listFeeConfig();
    }
}
