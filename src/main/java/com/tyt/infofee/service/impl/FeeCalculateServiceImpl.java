package com.tyt.infofee.service.impl;

import com.tyt.infofee.enums.ExcellentGoodsEnum;
import com.tyt.infofee.service.FeeCalculateService;
import com.tyt.infofee.service.FeeConfigService;
import com.tyt.plat.entity.base.TytFeeConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/06/03 14:01
 */
@Service
@Slf4j
public class FeeCalculateServiceImpl implements FeeCalculateService {

    @Autowired
    private FeeConfigService feeConfigService;

    @Override
    public BigDecimal calculateTechServiceFee(BigDecimal carriageFee, Integer excellentGoods) {
        log.info("calculateTechServiceFee carriageFee:{},excellentGoods:{}", carriageFee, excellentGoods);
        try {
            if(Objects.isNull(carriageFee) || Objects.isNull(excellentGoods)){
                return BigDecimal.ZERO;
            }
            //判断是不是专车货源
            if(!ExcellentGoodsEnum.SPECIAL_CAR_GOODS.getCode().equals(excellentGoods)){
                return BigDecimal.ZERO;
            }
            //查询费用配置
            List<TytFeeConfig> tytFeeConfigs = feeConfigService.listFeeConfig();
            //查看落在哪个区间,计算费用
            //大于最小值 && 小于等于最大值
            BigDecimal bDfinalCarriageFee = carriageFee;
            Optional<TytFeeConfig> feeConfigOptional = Optional.ofNullable(tytFeeConfigs)
                    .orElse(new ArrayList<>()).stream()
                    .filter(tytFeeConfig -> bDfinalCarriageFee.compareTo(tytFeeConfig.getMinVal()) > 0 && bDfinalCarriageFee.compareTo(tytFeeConfig.getMaxVal()) <= 0 )
                    .findFirst();
            if(feeConfigOptional.isPresent() &&
                    Objects.nonNull(feeConfigOptional.get().getRatio()) &&
                    feeConfigOptional.get().getRatio().compareTo(BigDecimal.ZERO) > 0){
                TytFeeConfig tytFeeConfig = feeConfigOptional.get();
                BigDecimal ratio = tytFeeConfig.getRatio();
                BigDecimal techServiceFee = ratio.multiply(bDfinalCarriageFee).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("calculateTechServiceFee carriageFee:{},techServiceFee:{}", carriageFee, techServiceFee);
                return techServiceFee.setScale(0, RoundingMode.CEILING);
            }
        } catch (Exception e) {
            log.error("calculateTechServiceFee error, carriageFee:{},excellentGoods:{}", carriageFee, excellentGoods, e);
        }
        return BigDecimal.ZERO;
    }
}
