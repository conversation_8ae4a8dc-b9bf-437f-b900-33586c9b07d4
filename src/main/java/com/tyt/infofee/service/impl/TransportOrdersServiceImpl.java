package com.tyt.infofee.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.google.gson.Gson;
import com.tyt.acvitity.bean.TransportOrders;
import com.tyt.base.bean.MapEntity;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.callPhoneRecord.bean.RecentCallListBean;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.common.service.TytSequenceService;
import com.tyt.config.util.AppConfig;
import com.tyt.corp.CorpRestClient;
import com.tyt.corp.bean.OrderListResponse;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.*;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.infofee.service.TransportWayBillService;
import com.tyt.manbang.bean.request.MbOrderCreateSucNoticeBean;
import com.tyt.model.TytTransportWaybillEx;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.payment.alipay.util.MakeOrderNum;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.commons.model.CheckResult;
import com.tyt.plat.constant.AbtestConstant;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.mapper.base.TytCustomInformationMapper;
import com.tyt.plat.mapper.base.TytTransportDispatchMapper;
import com.tyt.plat.mapper.base.TytTransportOrdersMapper;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.service.user.ApiUserCarryPointService;
import com.tyt.plat.vo.invoice.ThirdDominantInfoVo;
import com.tyt.plat.vo.ts.TransportOrderCountVo;
import com.tyt.plat.vo.user.UserCarryPointTotalVo;
import com.tyt.transport.enums.OrderStatusEnum;
import com.tyt.transport.querybean.BackendTransportBean;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.transport.service.TransportMainService;
import com.tyt.tsinsurance.service.TsInsuranceService;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.service.*;
import com.tyt.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * User: Administrator Date: 13-11-10 Time: 下午5:10
 */
@SuppressWarnings("deprecation")
@Slf4j
@Service("transportOrdersService")
public class TransportOrdersServiceImpl extends BaseServiceImpl<TytTransportOrders, Long> implements TransportOrdersService {
    public Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource(name = "tytBubbleService")
    TytBubbleService tytBubbleService;

    @Resource(name = "tytSequenceService")
    TytSequenceService tytSequenceService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;

    @Resource(name = "transportWayBillService")
    private TransportWayBillService transportWayBillService;

    @Resource(name = "transportBusiness")
    TransportBusinessInterface transportBusiness;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "carService")
    private CarService carService;

    @Resource(name = "publicResourceService")
    private PublicResourceService publicResourceService;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private UserPermissionService userPermissionService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "transportOrdersDao")
    public void setBaseDao(BaseDao<TytTransportOrders, Long> transportOrdersDao) {
        super.setBaseDao(transportOrdersDao);
    }

    @Resource(name = "tsinsuranceService")
    private TsInsuranceService tsinsuranceService;

    @Autowired
    private BackendTransportMapper backendTransportMapper;

    @Resource(name = "carCurrentLocationService")
    private CarCurrentLocationService carCurrentLocationService;

    @Autowired
    private TransportWayBillExService transportWayBillExService;

    @Resource(name = "tytOwnerAuthService")
    private TytOwnerAuthService tytOwnerAuthService;

    @Autowired
    private IInfofeeDetailService infofeeDetailService;


    @Autowired
    private UserBuyGoodsService userBuyGoodsService;

    @Autowired
    private IFeedbackUserService feedbackUserService;

    @Autowired
    private TytTransportOrdersMapper transportOrdersMapper;

    @Autowired
    private TytCustomInformationMapper customInformationMapper;

    @Autowired
    private TytTransportDispatchMapper tytTransportDispatchMapper;

    @Resource(name = "carDetailTailService")
    private CarDetailTailService carDetailTailService;

    @Autowired
    private ApiUserCarryPointService apiUserCarryPointService;

    /**
     * 技术服务费全额退款标识
     */
    public static final int TECHNICAL_FEE_REFUND_TYPE = 1;

    @Override
    public ListDataBean updateGetMyOrdersList(Long userId, int queryActionType, int queryMenuType, long queryID) throws Exception {
        ListDataBean ListDataBean = new ListDataBean();
        List<Object> list = new ArrayList<Object>();
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");
        String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
        String endTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), 1)) + " 00:00:00";

        StringBuffer sql = new StringBuffer("select a.id id,a.sort_id sortId, a.ts_order_no tsOrderNo,a.ts_id tsId,"
                + " a.user_id userId,a.start_point startPoint,"
                + " a.dest_point destPoint, a.task_content taskContent,"
                + " a.ctime  publishTime,a.pay_link_phone payLinkPhone,a.pay_amount payAmount,"
                + " a.create_time createTime,a.pay_end_time payEndTime,a.agree_time agreeTime,"
                + " a.load_time loadTime,a.refuse_time refuseTime,a.rob_status robStatus,a.cost_status costStatus,"
                + " b.weight weight, b.length length, b.wide wide, b.high high, b.loading_time loadingTime, "
                + " b.unload_time unloadTime, b.car_min_length carMinLength, b.car_max_length carMaxLength, b.car_type carType, "
                + " b.car_style carStyle, b.work_plane_min_high workPlaneMinHigh, b.work_plane_max_high workPlaneMaxHigh, "
                + " b.work_plane_min_length workPlaneMinLength, b.work_plane_max_length workPlaneMaxLength, b.climb climb "
                + " FROM tyt_transport_orders a left join tyt_transport_main b on a.ts_id = b.id  "
                + " where 1=1 ");

        sql.append(" and a.pay_user_id=?");
        list.add(userId);
        switch (queryMenuType) {
            case 1:
                sql.append(" and a.rob_status=?");
                list.add("0");
                sql.append(" and (a.pay_status=?");
                list.add("0");
                sql.append("  or a.pay_status=?)");
                list.add("1");
                sql.append(" and a.ctime>?");
                list.add(beginTime);
                sql.append(" and a.ctime<=?");
                list.add(endTime);
                break;
            case 2:
                sql.append(" and a.rob_status=?");
                list.add("1");
                sql.append(" and a.pay_status=?");
                list.add("2");
                break;
            case 3:
                sql.append(" and a.rob_status=?");
                list.add("4");
                break;
            case 4:
                sql.append(" and (a.rob_status=?");
                list.add("5");
                sql.append(" or a.rob_status=?)");
                list.add("6");
                break;
            case 5:
                sql.append(" and (a.rob_status=?");
                list.add("2");
                sql.append(" or a.rob_status=?");
                list.add("3");
                sql.append(" or a.rob_status=?)");
                list.add("12");
                break;
            case 6:
                sql.append(" and (a.rob_status=?)");
                list.add("-100");
                break;
        }

        if (queryActionType == 2) {
            sql.append(" and a.sort_id<?");
            list.add(queryID);
        }

        sql.append(" order by a.sort_id desc ");

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("sortId", Hibernate.LONG);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("tsId", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("publishTime", Hibernate.TIMESTAMP);
        scalarMap.put("payLinkPhone", Hibernate.STRING);
        scalarMap.put("payAmount", Hibernate.LONG);
        scalarMap.put("createTime", Hibernate.TIMESTAMP);
        scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
        scalarMap.put("agreeTime", Hibernate.TIMESTAMP);
        scalarMap.put("loadTime", Hibernate.TIMESTAMP);
        scalarMap.put("refuseTime", Hibernate.TIMESTAMP);
        scalarMap.put("robStatus", Hibernate.STRING);
        //重量 单位:吨 长宽高 单位：米
        scalarMap.put("weight", Hibernate.STRING);
        scalarMap.put("length", Hibernate.STRING);
        scalarMap.put("wide", Hibernate.STRING);
        scalarMap.put("loadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("unloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("carMinLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("carMaxLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("carType", Hibernate.STRING);
        scalarMap.put("carStyle", Hibernate.STRING);
        scalarMap.put("workPlaneMinHigh", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMaxHigh", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMinLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("workPlaneMaxLength", Hibernate.BIG_DECIMAL);
        scalarMap.put("climb", Hibernate.STRING);
        List<TransportOrdersListBean> twbl = this.getBaseDao().search(sql.toString(), scalarMap, TransportOrdersListBean.class, list.toArray(), 1, pageSize);
        if (twbl != null && twbl.size() > 0) {
            ListDataBean.setData(twbl);
        }
        // 如果是装货完成 要修改气泡数为0
        if (queryMenuType == 4) {
            int n = tytBubbleService.updateBubbleNumber(userId, "1", "3", 0);
            logger.info("修改了type1{},type2{},{}条气泡数据", "1", "3", n);
        } else if (queryMenuType == 5) {
            int n = tytBubbleService.updateBubbleNumber(userId, "1", "4", 0);
            logger.info("修改了type1{},type2{},{}条气泡数据", "1", "4", n);
        }
        ListDataBean.setBubbleNumbers(tytBubbleService.getInfoFeeMyPublishBubbleResultBeanForUserId(userId));
        ListDataBean.setCurrentTime(System.currentTimeMillis());
        return ListDataBean;
    }

    @Override
    public ListDataBean getMergeOrdersList(Long userId, int queryActionType, int queryMenuType, long queryID, long queryCID) throws Exception {
        ListDataBean returnBean = new ListDataBean(); // 回传对象
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size"); // 每页条数
        //1. 获取C端订单数据
        ListDataBean cdataBean = this.updateGetMyOrdersList(userId, queryActionType, queryMenuType, queryID);
        System.out.println(cdataBean.getBubbleNumbers());
        if (cdataBean != null) {
            logger.info("C端气泡数: {}", new Gson().toJson(cdataBean.getBubbleNumbers()));
        }
        //2. 获取B端订单数据
        OrderListResponse bdataBean = CorpRestClient.getInstance().getOrderList(userId, queryActionType, queryMenuType, queryCID, pageSize);
        if (bdataBean != null) {
            logger.info("B端气泡数: {}", new Gson().toJson(bdataBean.getBubbleNumbers()));
        }
        // C端订单数据
        List<TransportOrdersListBean> ctwbl = cdataBean.getData();
        // B端订单数据
        List<TransportOrdersListBean> btwbl = null;
        if (bdataBean != null) {
            btwbl = bdataBean.getData();
        }

        // ALL订单数据
        List<TransportOrdersListBean> allOrders = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ctwbl)) {
            allOrders.addAll(ctwbl);
        }
        if (CollectionUtils.isNotEmpty(btwbl)) {
            allOrders.addAll(btwbl);
        }
        //如果ALL订单数据不为空
        if (allOrders != null && allOrders.size() > 0) {
            for (TransportOrdersListBean allOrder : allOrders) {
                //货物信息Id
                Long tsId = allOrder.getTsId();
                // 获取最新的保单
                TransportInsurance transportInsurance = tsinsuranceService.queryInsuranceByTsId(userId, tsId);
                //最后一次购买保险的保单信息
                if (transportInsurance != null) {
                    //是否购买过保险 1是(显示“查看保单”按钮)
                    allOrder.setIsBuyInsurance(1);
                    //最后一次购买保险的保单Id
                    Long insuranceId = transportInsurance.getId();
                    if (insuranceId != null) {
                        allOrder.setInsuranceId(insuranceId);
                    }
                    //最后一次购买保险的保单状态 0待支付 1已生效 2已退保
                    Integer status = transportInsurance.getStatus();
                    if (status != null) {
                        allOrder.setInsuranceStatus(status);
                    }
                } else {
                    //是否购买过保险  2否(显示“买货运险”按钮)
                    allOrder.setIsBuyInsurance(2);
                }
            }
        }

        // 数据截取，并按createTime 倒序进行排序
        List<TransportOrdersListBean> listResult = allOrders
                .stream()
                .sorted(new Comparator<TransportOrdersListBean>() { // 时间倒序
                    @Override
                    public int compare(TransportOrdersListBean o1, TransportOrdersListBean o2) {
                        int flag = o2.getCreateTime().compareTo(o1.getCreateTime());
                        return flag;
                    }
                }).limit(pageSize)
                .collect(Collectors.toList()); //收集并返回

        // 协助记录C端 和 B端 最小queryId
        Map<String, Long> queryIDMap = new HashMap<>();
        // 找出C端数据，最小的queryID
        if (CollectionUtils.isNotEmpty(ctwbl)) {
            for (TransportOrdersListBean t : ctwbl) {
                boolean b = listResult.stream().anyMatch(u -> u.getUniqueId().equals(t.getUniqueId()));
                System.out.println(t.getUniqueId() + "-" + b);
                if (b) {
                    queryIDMap.put("plat", t.getSortId());
                }
            }
        }
        // 找出B端数据，最小的queryID
        if (CollectionUtils.isNotEmpty(btwbl)) {
            for (TransportOrdersListBean t : btwbl) {
                boolean b = listResult.stream().anyMatch(u -> u.getUniqueId().equals(t.getUniqueId()));
                System.out.println(t.getUniqueId() + "-" + b);
                if (b) {
                    queryIDMap.put("corp", t.getId());
                }
            }
        }

        // 设置queryID
        Long reQueryID = NumberUtils.toLong(String.valueOf(queryIDMap.get("plat")), 0);
        if (queryID != 0 && reQueryID == 0) {
            reQueryID = queryID;
        }
        Long reQueryCID = NumberUtils.toLong(String.valueOf(queryIDMap.get("corp")), 0);
        if (queryCID != 0 && reQueryCID == 0) {
            reQueryCID = queryCID;
        }
        returnBean.setQueryID(reQueryID);
        returnBean.setQueryCID(reQueryCID);
        returnBean.setData(listResult);

        // 处理气泡
        List<InfoFeeMyPublishBubbleResultBean> cbubble = cdataBean.getBubbleNumbers(); // c端气泡
        List<InfoFeeMyPublishBubbleResultBean> bbubble = null; // b端气泡
        if (bdataBean != null) {
            bbubble = bdataBean.getBubbleNumbers(); // b端气泡
        }
        // 遍历多端气泡，相加
        List<InfoFeeMyPublishBubbleResultBean> allBubble = tytBubbleService.mergeBubble(cbubble, bbubble);
        returnBean.setBubbleNumbers(allBubble);
        returnBean.setCurrentTime(cdataBean.getCurrentTime());
        return returnBean;
    }

    /**
     * @return com.tyt.infofee.bean.ListDataBean
     * @Description 车主待支付信息费接口 - 信息费改版新接口方法
     * <AUTHOR>
     * @Date 2019/1/4 17:21
     * @Param [userId, queryActionType, queryMenuType, queryID, queryCID]
     **/
    @Override
    public ListDataBean getCarOwnerUnpaidList(Long userId, int queryActionType, int queryMenuType, long queryID) throws Exception {
        ListDataBean returnBean = new ListDataBean(); // 回传对象
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size"); // 每页条数
        //1. 获取C端订单数据
        ListDataBean cdataBean = this.updateGetMyOrdersList(userId, queryActionType, queryMenuType, queryID);
        // C端订单数据
        List<TransportOrdersListBean> ctwbl = cdataBean.getData();
        // ALL订单数据
        List<TransportOrdersListBean> allOrders = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ctwbl)) {
            allOrders.addAll(ctwbl);
        }
        //如果ALL订单数据不为空
        if (allOrders != null && allOrders.size() > 0) {
            for (TransportOrdersListBean allOrder : allOrders) {
                //货物信息Id
                Long tsId = allOrder.getTsId();
                // 获取最新的保单
                TransportInsurance transportInsurance = tsinsuranceService.queryInsuranceByTsId(userId, tsId);
                //最后一次购买保险的保单信息
                if (transportInsurance != null) {
                    //是否购买过保险 1是(显示“查看保单”按钮)
                    allOrder.setIsBuyInsurance(1);
                    //最后一次购买保险的保单Id
                    Long insuranceId = transportInsurance.getId();
                    if (insuranceId != null) {
                        allOrder.setInsuranceId(insuranceId);
                    }
                    //最后一次购买保险的保单状态 0待支付 1已生效 2已退保
                    Integer status = transportInsurance.getStatus();
                    if (status != null) {
                        allOrder.setInsuranceStatus(status);
                    }
                } else {
                    //是否购买过保险  2否(显示“买货运险”按钮)
                    allOrder.setIsBuyInsurance(2);
                }
            }
        }
        // 数据截取，并按createTime 倒序进行排序
        List<TransportOrdersListBean> listResult = allOrders
                .stream()
                .sorted(new Comparator<TransportOrdersListBean>() { // 时间倒序
                    @Override
                    public int compare(TransportOrdersListBean o1, TransportOrdersListBean o2) {
                        int flag = o2.getCreateTime().compareTo(o1.getCreateTime());
                        return flag;
                    }
                }).limit(pageSize)
                .collect(Collectors.toList()); //收集并返回

        // 协助记录C端最小queryId
        Map<String, Long> queryIDMap = new HashMap<>();
        // 找出C端数据，最小的queryID
        if (CollectionUtils.isNotEmpty(ctwbl)) {
            for (TransportOrdersListBean t : ctwbl) {
                boolean b = listResult.stream().anyMatch(u -> u.getUniqueId().equals(t.getUniqueId()));
                System.out.println(t.getUniqueId() + "-" + b);
                if (b) {
                    queryIDMap.put("plat", t.getSortId());
                }
            }
        }
        // 设置queryID
        Long reQueryID = NumberUtils.toLong(String.valueOf(queryIDMap.get("plat")), 0);
        if (queryID != 0 && reQueryID == 0) {
            reQueryID = queryID;
        }
        returnBean.setQueryID(reQueryID);
        returnBean.setData(listResult);
        returnBean.setCurrentTime(cdataBean.getCurrentTime());
        return returnBean;
    }

    @Override
    public ListDataBean updateGetMyOrdersAllList(Long userId, int queryActionType, long queryID) throws Exception {
        ListDataBean ListDataBean = new ListDataBean();
        List<Object> list = new ArrayList<Object>();
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");
        String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
        String endTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), 1)) + " 00:00:00";

        StringBuffer sql = new StringBuffer("select id,sort_id sortId, ts_order_no tsOrderNo,ts_id tsId,user_id userId,start_point startPoint," +
                "dest_point destPoint, task_content taskContent," + "ctime  publishTime,pay_link_phone payLinkPhone,pay_amount payAmount," +
                "create_time createTime,pay_end_time payEndTime,agree_time agreeTime,load_time loadTime,refuse_time refuseTime,rob_status robStatus FROM tyt_transport_orders  where 1=1 ");

        sql.append(" and pay_user_id=? AND rob_status in (1,4,5,6,2,3,7) AND pay_status in (2) ");
        list.add(userId);
        sql.append("OR ( rob_status =0 AND pay_user_id =? AND (pay_status =0 OR pay_status =1)  AND ctime >? ");
        list.add(userId);
        list.add(beginTime);
        sql.append(" AND ctime <=? ");
        list.add(endTime);
        sql.append(")");

        if (queryActionType == 2) {
            sql.append(" and sort_id<?");
            list.add(queryID);
        }

        sql.append(" order by sort_id desc ");

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("sortId", Hibernate.LONG);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("tsId", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("publishTime", Hibernate.TIMESTAMP);
        scalarMap.put("payLinkPhone", Hibernate.STRING);
        scalarMap.put("payAmount", Hibernate.LONG);
        scalarMap.put("createTime", Hibernate.TIMESTAMP);
        scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
        scalarMap.put("agreeTime", Hibernate.TIMESTAMP);
        scalarMap.put("loadTime", Hibernate.TIMESTAMP);
        scalarMap.put("refuseTime", Hibernate.TIMESTAMP);
        scalarMap.put("robStatus", Hibernate.STRING);

        List<TransportOrdersListBean> twbl = this.getBaseDao().search(sql.toString(), scalarMap, TransportOrdersListBean.class, list.toArray(), 1, pageSize);
        if (twbl != null && twbl.size() > 0) {
            ListDataBean.setData(twbl);
        }
        ListDataBean.setBubbleNumbers(tytBubbleService.getInfoFeeMyPublishBubbleResultBeanForUserId(userId));
        ListDataBean.setCurrentTime(System.currentTimeMillis());
        return ListDataBean;
    }

    /**
     * 保存订单信息
     *
     * @param originalTransport
     * @param carOwnerTelephone
     * @param agencyMoney
     * @param payUser
     * @param infoFeeCarRequest:车辆部分信息
     * @throws Exception
     */
    @Override
    public TytTransportOrders saveOrderInfo(Integer zeroAssignOrder,MbOrderCreateSucNoticeBean orderCreateSucNoticeBean, Transport originalTransport, String carOwnerTelephone, Long agencyMoney,
                                            User payUser, Integer timeLimitIdentification, Integer carriageFee, CarSaveBean infoFeeCarRequest, BigDecimal couponAmount,
                                            Long tecServiceFee, String technicalServiceNo, TytMbCargoSyncInfo tytMbCargoSyncInfo,Integer OrderNewStatus,Integer driverId,Integer invoiceTransport,
                                            ThirdDominantInfoVo thirdDominantInfoVo) throws Exception {
        //发布人昵称
        String pubUserName = "";
        if (tytMbCargoSyncInfo != null) {
            pubUserName = tytMbCargoSyncInfo.getContactName();
        } else {
            pubUserName = originalTransport.getNickName();
        }
        Date todayDate = new Date();
        // 费率
        String infoRate = tytConfigService.getStringValue("infoFeeRate");
        if (infoRate == null || infoRate.trim().equals("")) {
            infoRate = "0.004";
        }
        TytTransportOrders tytTransportOrders = new TytTransportOrders();
        tytTransportOrders.setStartPoint(originalTransport.getStartPoint());
        tytTransportOrders.setDestPoint(originalTransport.getDestPoint());
        tytTransportOrders.setTaskContent(originalTransport.getTaskContent());
        tytTransportOrders.setTel(originalTransport.getTel());
        tytTransportOrders.setPubTime(originalTransport.getPubTime());
        tytTransportOrders.setCtime(originalTransport.getReleaseTime());
        tytTransportOrders.setUploadCellphone(originalTransport.getUploadCellPhone());
        tytTransportOrders.setUserId(originalTransport.getUserId());
        tytTransportOrders.setLinkman(originalTransport.getLinkman());
        tytTransportOrders.setTel3(originalTransport.getTel3());
        tytTransportOrders.setTel4(originalTransport.getTel4());
        tytTransportOrders.setSortId(tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME));
        tytTransportOrders.setTsOrderNo(originalTransport.getTsOrderNo());
        tytTransportOrders.setTsId(originalTransport.getId());
        tytTransportOrders.setPayUserId(payUser.getId());
        tytTransportOrders.setPayCellPhone(payUser.getCellPhone());
        tytTransportOrders.setPayLinkPhone(carOwnerTelephone);
        tytTransportOrders.setCreateTime(todayDate);// 接单时间
        tytTransportOrders.setOrderNewStatus(OrderNewStatus);
        tytTransportOrders.setDriverId(driverId);
        tytTransportOrders.setInvoiceTransport(invoiceTransport);
        // 支付方式 1支付宝 2易宝银行卡 3微信 4线下支付 5tpay支付
        String payType = null;
        //是否启用tpay收银台版本(true:是,false:否)
        boolean isTpayVersion = TytSwitchUtil.isTpayVersion();
        //是否启用满帮收银台版本(true:是,false:否)
        if (isTpayVersion) {
            payType = "5";
            //设置支付手续费金额:0元，支付成功后进行更新
            tytTransportOrders.setPayFeeAmount(0L);// 支付手续费金额单位分
        } else {
            //设置支付手续费金额
            tytTransportOrders.setPayFeeAmount(MoneyTools.getBankInterest(agencyMoney + "", infoRate).movePointRight(2).longValue());// 支付手续费金额单位分
        }

        //--------------------------此代码仅用于开发付款使用 start--------------------
        if ("true".equals(AppConfig.getProperty("tyt.test.dev.red.cent"))) {
            // 便于开发调试，将金额调整为1分钱，tyt.test.dev.red.cent 此配置不上生产环境
            tytTransportOrders.setPayAmount(1L);
            tytTransportOrders.setPayFeeAmount(0L);
            tytTransportOrders.setTecServiceFee(0L);
        }
        //--------------------------此代码仅用于开发付款使用 end-----------------------

        tytTransportOrders.setAgreeTime(null);// 同意装货时间（成交时间）
        tytTransportOrders.setLoadTime(null);// 装货完成时间
        tytTransportOrders.setRefuseTime(null);// 拒绝装货完成时间
        tytTransportOrders.setMtime(todayDate);
        //支付订单号
        String payOrderNo = IdUtils.getIncreaseIdByLocalTime();
        tytTransportOrders.setPayOrderNo(payOrderNo);
        tytTransportOrders.setTechnicalServiceNo(technicalServiceNo);

        //2019-01-10 信息费改版新增字段
        //发布人昵称
        tytTransportOrders.setPubUserName(pubUserName);
        //车主昵称(支付人昵称)
        tytTransportOrders.setPayUserName(StringUtils.isNotBlank(payUser.getCarUserName()) ?
                payUser.getCarUserName() :
                payUser.getUserName());
        // 5950新增车辆类字段
        if (infoFeeCarRequest != null) {
            if (StringUtils.isNotBlank(infoFeeCarRequest.getHeadNo())) {
                tytTransportOrders.setHeadCity(infoFeeCarRequest.getHeadCity());
                tytTransportOrders.setHeadNo(infoFeeCarRequest.getHeadNo());
                tytTransportOrders.setTailCity(infoFeeCarRequest.getTailCity());
                tytTransportOrders.setTailNo(infoFeeCarRequest.getTailNo());
                tytTransportOrders.setCarId(infoFeeCarRequest.getCarId());
                if (infoFeeCarRequest.getCarId() == null && orderCreateSucNoticeBean == null) {
                    // 调用车辆保存接口
                    infoFeeCarRequest.setUserId(payUser.getId());
                    infoFeeCarRequest.setAuth("2");
                    // V6000 版本将此项前移，由于程序不知道在支付信息费时，填写的车辆是否带爬梯，所以为未知
                    infoFeeCarRequest.setHasLadder(2);
                    //6440,新增状态
                    infoFeeCarRequest.setIsInvoice(3);
                    Long carId = carService.saveCar(infoFeeCarRequest);
                    tytTransportOrders.setCarId(carId);
                }
            }
        }

        tytTransportOrders.setCarriageFee(carriageFee);
        tytTransportOrders.setTimeLimitIdentification(timeLimitIdentification);
        //限时订单 设置服务费
        if (Objects.equals(WaybillTimeLimitEnum.TIME_LIMIT.getType(), timeLimitIdentification)) {
            String rate = tytConfigService.getStringValue("payServiceChargeRate", "0.05");
            if (null != tytTransportOrders.getPayAmount()) {
                tytTransportOrders.setPayServiceCharge(BigDecimal.valueOf(tytTransportOrders.getPayAmount()).multiply(new BigDecimal(rate)));
            }
        }
        tytTransportOrders.setRefundFlag(originalTransport.getRefundFlag());
        tytTransportOrders.setDelayRefundStatus(0);
        tytTransportOrders.setIsAssignOrder(zeroAssignOrder);
        //当订单为开票0元指派单时 则默认已支付成功
        if(Objects.equals(AssignOrderTypeEnum.assign_order.getCode(),zeroAssignOrder)){
            tytTransportOrders.setPayAmount(0L);
            tytTransportOrders.setTecServiceFee(0L);
            tytTransportOrders.setRobStatus("4");
            tytTransportOrders.setPayStatus("2");
            //生成tyt_old_order表主键order_id
            String orderNo = MakeOrderNum.getOrderNo();
            tytTransportOrders.setPayNo(orderNo);
            tytTransportOrders.setCostStatus(15);
            tytTransportOrders.setPayEndTime(new Date());
            tytTransportOrders.setPaySubChannel("groupPay");
            tytTransportOrders.setThirdpartyPlatformType(0);
            tytTransportOrders.setPayType("6");
            tytTransportOrders.setTotalOrderAmount(0L);
            tytTransportOrders.setCouponAmount(0L);
            tytTransportOrders.setPayFeeAmount(0L);
            tytTransportOrders.setDePaymentDueDate(TimeUtil.addDay(new Date(),Integer.parseInt(tytConfigService.getValue("autoFinishDayNbr").getValue())));
            tytTransportOrders.setDeRefundDueDate( TimeUtil.addDay(new Date(), Integer.parseInt(tytConfigService.getValue("delayedRefundDays").getValue())));
            tytTransportOrders.setDeLoadDueDate( TimeUtil.addDay(new Date(), Integer.parseInt(tytConfigService.getValue("autoFinishDayNbr").getValue())));
            tytTransportOrders.setOrderNewStatus(OrderStatusEnum.WAIT_SING.getCode());
        }else{
            //判断当前信息的来源 orderCreateSucNoticeBean 不为空时 则表示该订单属于满帮订单
            if (orderCreateSucNoticeBean != null) {
                tytTransportOrders.setPayAmount(agencyMoney);//三方平台传递过来的单位为分
                // 技术服务费 单位分
                tytTransportOrders.setTecServiceFee(0L);
                // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货
                // 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货
                tytTransportOrders.setRobStatus("4");
                // 支付状态0待支付1支付失败2支付成功
                tytTransportOrders.setPayStatus("2");
                //生成tyt_old_order表主键order_id
                String orderNo = MakeOrderNum.getOrderNo();
                tytTransportOrders.setPayNo(orderNo);
                //同步支付状态  15已支付
                tytTransportOrders.setCostStatus(15);
                // 支付完成时间
                tytTransportOrders.setPayEndTime(new Date(orderCreateSucNoticeBean.getPayDepositTime()));
                tytTransportOrders.setPaySubChannel("groupPay");
                //三方平台订单号
                tytTransportOrders.setThirdpartyPlatformOrderNo(orderCreateSucNoticeBean.getOrderId().toString());
                //三方订单自动结算日期
                if(Objects.nonNull(orderCreateSucNoticeBean.getDepositDelayTime())){
                    tytTransportOrders.setDePaymentDueDate(new Date(orderCreateSucNoticeBean.getDepositDelayTime()));
                    tytTransportOrders.setDeRefundDueDate(new Date(orderCreateSucNoticeBean.getDepositDelayTime()));
                }else{
                    tytTransportOrders.setDePaymentDueDate(TimeUtil.addDay(new Date(),Integer.parseInt(tytConfigService.getValue("autoFinishDayNbr").getValue())));
                    tytTransportOrders.setDeRefundDueDate(TimeUtil.addDay(new Date(), Integer.parseInt(tytConfigService.getValue("delayedRefundDays").getValue())));
                }
                //三方平台类型 1:满帮
                tytTransportOrders.setThirdpartyPlatformType(1);
                //直接设置为支付后的 满帮支付6
                tytTransportOrders.setPayType("6"); //支付方式
                //订单总金额(单位分)
                tytTransportOrders.setTotalOrderAmount(agencyMoney);
                //优惠券金额(单位分)
                tytTransportOrders.setCouponAmount(0L);
                //运费金额
                Long carriageFeeMb = orderCreateSucNoticeBean.getCarriageFee();
                if (carriageFeeMb != null) {
                    tytTransportOrders.setCarriageFee(carriageFeeMb.intValue());
                }
            } else {
                tytTransportOrders.setPayAmount(new BigDecimal(agencyMoney).movePointRight(2).longValue());
                //技术服务费 单位分
                tytTransportOrders.setTecServiceFee(new BigDecimal(tecServiceFee).movePointRight(2).longValue());
                // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货
                // 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货
                tytTransportOrders.setRobStatus("0");
                // 支付状态0待支付1支付失败2支付成功
                tytTransportOrders.setPayStatus("0");
                tytTransportOrders.setPayNo(null);
                //同步支付状态  15已支付
                tytTransportOrders.setCostStatus(10);
                tytTransportOrders.setPayEndTime(null);// 支付完成时间
                //设置为支付后的
                tytTransportOrders.setPayType(payType); //支付方式
                ///**
                //	 * 三方平台类型
                //	 * 0:特运通货源订单
                //	 * 1:特运通货源，满帮订单
                //	 * 2:满帮货源，特运通订单
                //	 */
                Integer thirdpartyPlatformType = 0;
                Integer sourceType = originalTransport.getSourceType();
                if (sourceType != null && sourceType == 4) {
                    thirdpartyPlatformType = 2;
                }
                tytTransportOrders.setThirdpartyPlatformType(thirdpartyPlatformType);
                //订单总金额(单位分)
                tytTransportOrders.setTotalOrderAmount(new BigDecimal(agencyMoney).add(couponAmount).movePointRight(2).longValue());
                //优惠券金额(单位分)
                tytTransportOrders.setCouponAmount(couponAmount.movePointRight(2).longValue());
            }
        }
        //如果是开票货源且存在三方服务商
        if(originalTransport.getInvoiceTransport() != null
        && originalTransport.getInvoiceTransport() == 1
        && Objects.nonNull(thirdDominantInfoVo)){
            // 服务商编码：JCZY-自营 HBWJ-我家
            tytTransportOrders.setInvoiceServiceCode(thirdDominantInfoVo.getServiceProviderCode());
            // 开票主体id
            tytTransportOrders.setInvoiceIssuerId(thirdDominantInfoVo.getId());
            // 开票主体编码
            tytTransportOrders.setInvoiceIssuerCode(thirdDominantInfoVo.getPrincipalCode());
        }
        tytTransportOrders.setSourceType(originalTransport.getSourceType());
        tytTransportOrders.setMachineRemark(originalTransport.getMachineRemark());
        this.add(tytTransportOrders);
        return tytTransportOrders;
    }

    @Override
    public int saveChangeAgreeOrderStatus(Long[] payUserId, String transportNo, Long userId) throws Exception {
        String updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",agree_time=NOW(),mtime=NOW(),sort_id=:sortId" + " where user_id=:userId and ts_order_no=:transportNo" + " and pay_user_id in(:payUserId)";
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款
        // 9系统撤销货源退款 10车主取销装货
        paramsMap.put("robStatus", "4");
        paramsMap.put("userId", userId);
        paramsMap.put("transportNo", transportNo);
        paramsMap.put("payUserId", payUserId);
        paramsMap.put("sortId", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME));
        return this.executeUpdateSql(updateSQL, paramsMap);
    }

    @Override
    public Long[] getOrderPayUserIdByRobStatus(Long userId, String transportNo, String robStatus) throws Exception {
        String selectSQL = "select pay_user_id userId from tyt_transport_orders where user_id=? and ts_order_no=? and rob_status=? order by sort_id desc";
        List<BigInteger> idList = this.getBaseDao().query(selectSQL, new Object[]{userId, transportNo, robStatus});
        if (idList == null || idList.size() < 1)
            return null;
        Long[] idArray = new Long[idList.size()];
        for (int i = 0; i < idList.size(); i++) {
            idArray[i] = idList.get(i).longValue();
        }
        return idArray;
    }

    @Override
    public GoodsDetailOrderBean getOrder(Long userId, Long carOwnerUserId, String transportNo, String robStatus) {
        String selectSQL = "select pay_user_id carOwnerUserId,pay_cell_phone carOwnerRegisterPhone," + "pay_link_phone carOwnerTelephone,pay_amount payAgencyMoney,pay_end_time payEndTime from tyt_transport_orders " + "where user_id=:userId and ts_order_no=:transportNo and pay_user_id=:carOwnerUserId and rob_status=:robStatus";
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("carOwnerUserId", Hibernate.LONG);
        scalarMap.put("carOwnerRegisterPhone", Hibernate.STRING);
        scalarMap.put("carOwnerTelephone", Hibernate.STRING);
        scalarMap.put("payAgencyMoney", Hibernate.LONG);
        scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("userId", userId);
        paramsMap.put("transportNo", transportNo);
        paramsMap.put("carOwnerUserId", carOwnerUserId);
        paramsMap.put("robStatus", robStatus);
        return this.getBaseDao().queryByMap(selectSQL, paramsMap, GoodsDetailOrderBean.class, scalarMap);
    }

    @Override
    public int saveChangeWaitPayToFailure(Long userId, String transportNo) throws Exception {
        String updateSQL = "update tyt_transport_orders set rob_status=?" + ",mtime=NOW(),sort_id=? where user_id=? and ts_order_no=? and rob_status=?";
        // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款
        // 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
        return this.executeUpdateSql(updateSQL, new Object[]{"11", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME), userId, transportNo, "0"});
    }

    @Override
    public Long getOrderId(Long userId, Long carOwnerUserId, String transportNo) {
        String selectSQL = "SELECT id from tyt_transport_orders where user_id=? and ts_order_no=? and pay_user_id=?";
        BigInteger orderId = this.getBaseDao().query(selectSQL, new Object[]{userId, transportNo, carOwnerUserId});
        return orderId == null ? null : orderId.longValue();
    }

    @Override
    public int saveChangeWaitPay(Long orderId, String carOwnerTelephone, Long agencyMoney) {
        // 费率
        String infoRate = tytConfigService.getStringValue("infoFeeRate");
        if (infoRate == null || infoRate.trim().equals("")) {
            infoRate = "0.004";
        }
        Long agencyMoneyCents = new BigDecimal(agencyMoney).movePointRight(2).longValue();
        Long rateCents = MoneyTools.getBankInterest(agencyMoney + "", infoRate).movePointRight(2).longValue();// 支付手续费金额单位分

        String updateSQL = "update tyt_transport_orders set pay_link_phone=?,pay_amount=?,pay_fee_amount=?,create_time=NOW(),mtime=NOW() where id=?";
        return this.getBaseDao().executeUpdateSql(updateSQL, new Object[]{carOwnerTelephone, agencyMoneyCents, rateCents, orderId});
    }

    @Override
    public List<GoodsDetailOrderBean> getByUser(Long userId, String tsOrderNo, String robStatus) {
        String selectSQL = "select id,ts_order_no goodsOrderNo,pay_user_id carOwnerUserId,pay_cell_phone carOwnerRegisterPhone, "
                + "pay_link_phone carOwnerTelephone,pay_amount payAgencyMoney,pay_end_time payEndTime,pay_type payChannel, "
                + " carriage_fee carriageFee "
                + " from tyt_transport_orders where user_id=? and ts_order_no=? and rob_status=? order by sort_id desc";
        Map<String, Type> paramsMap = new HashMap<String, Type>();
        paramsMap.put("id", Hibernate.LONG);
        paramsMap.put("goodsOrderNo", Hibernate.STRING);
        paramsMap.put("carOwnerUserId", Hibernate.LONG);
        paramsMap.put("carOwnerRegisterPhone", Hibernate.STRING);
        paramsMap.put("carOwnerTelephone", Hibernate.STRING);
        paramsMap.put("payAgencyMoney", Hibernate.LONG);
        paramsMap.put("payEndTime", Hibernate.TIMESTAMP);
        paramsMap.put("payChannel", Hibernate.STRING);
        paramsMap.put("carriageFee", Hibernate.INTEGER);

        return this.getBaseDao().search(selectSQL, paramsMap, GoodsDetailOrderBean.class, new Object[]{userId, tsOrderNo, robStatus});
    }

    /**
     * 货物详情中显示的订单信息（信息费改版新接口）
     *
     * @param userId:用户ID
     * @param tsOrderNo:运单号
     * @param robStatus:接单状态0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * @param costStatus:        信息费状态 10待支付
     * @return
     */
    @Override
    public List<GoodsDetailOrderBean> getByUserNew(Long userId, String tsOrderNo, String robStatus, Integer costStatus) {
        String selectSQL = "select id,ts_order_no goodsOrderNo,pay_user_id carOwnerUserId,pay_cell_phone carOwnerRegisterPhone" + ",pay_link_phone carOwnerTelephone,pay_amount payAgencyMoney,pay_end_time payEndTime,pay_type payChannel" + " from tyt_transport_orders where user_id=? and ts_order_no=? and rob_status=? and cost_status=? order by sort_id desc";
        Map<String, Type> paramsMap = new HashMap<String, Type>();
        paramsMap.put("id", Hibernate.LONG);
        paramsMap.put("goodsOrderNo", Hibernate.STRING);
        paramsMap.put("carOwnerUserId", Hibernate.LONG);
        paramsMap.put("carOwnerRegisterPhone", Hibernate.STRING);
        paramsMap.put("carOwnerTelephone", Hibernate.STRING);
        paramsMap.put("payAgencyMoney", Hibernate.LONG);
        paramsMap.put("payEndTime", Hibernate.TIMESTAMP);
        paramsMap.put("payChannel", Hibernate.STRING);
        return this.getBaseDao().search(selectSQL, paramsMap, GoodsDetailOrderBean.class, new Object[]{userId, tsOrderNo, robStatus, costStatus});
    }

    @Override
    public Long getOrderId(Long carOwnerUserId, String transportNo, String payStatus) throws Exception {
        String selectSQL = "SELECT id from tyt_transport_orders where ts_order_no=? and pay_user_id=? and pay_status=?";
        BigInteger orderId = this.getBaseDao().query(selectSQL, new Object[]{transportNo, carOwnerUserId, payStatus});
        return orderId == null ? null : orderId.longValue();
    }

    @Override
    public List<GoodsDetailOrderBean> getOrdersByPayUser(String tsOrderNo, Long[] payUserIds, String robStatus) throws Exception {
        String selectSQL = "select id,ts_order_no goodsOrderNo,pay_user_id carOwnerUserId,pay_cell_phone carOwnerRegisterPhone" + ",pay_link_phone carOwnerTelephone,pay_amount payAgencyMoney,pay_end_time payEndTime,pay_type payChannel" + " from tyt_transport_orders where ts_order_no=:tsOrderNo and pay_user_id in(:payUserIds)  and rob_status=:robStatus";
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("goodsOrderNo", Hibernate.STRING);
        scalarMap.put("carOwnerUserId", Hibernate.LONG);
        scalarMap.put("carOwnerRegisterPhone", Hibernate.STRING);
        scalarMap.put("carOwnerTelephone", Hibernate.STRING);
        scalarMap.put("payAgencyMoney", Hibernate.LONG);
        scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
        scalarMap.put("payChannel", Hibernate.STRING);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("tsOrderNo", tsOrderNo);
        paramMap.put("payUserIds", payUserIds);
        paramMap.put("robStatus", robStatus);
        return this.getBaseDao().search(selectSQL, scalarMap, GoodsDetailOrderBean.class, paramMap);
    }

    @Override
    public List<Long> getCarOwnerOrderId(Long carOwnerUserId, String transportNo, String robStatus) throws Exception {
        String selectSQL = "SELECT id from tyt_transport_orders where ts_order_no=:transportNo and pay_user_id=:carOwnerUserId and rob_status in(:robStatus) order by sort_id desc";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("transportNo", transportNo);
        paramMap.put("carOwnerUserId", carOwnerUserId);
        paramMap.put("robStatus", robStatus);
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        List<Long> orderIds = this.getBaseDao().search(selectSQL, scalarMap, null, paramMap);
        return orderIds;
    }

    @Override
    public int saveChangeRobStatusById(Long id, String robStatus) throws Exception {
        // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款
        // 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态） 12车方取消装货
        String updateSQL = "";
        switch (robStatus) {
            case "2":
            case "12":
                updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",refuse_time=NOW(),mtime=NOW(),sort_id=:sortId" + " where  id=:id";
                break;
            case "4":
                updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",agree_time=NOW(),mtime=NOW(),sort_id=:sortId" + " where  id=:id and rob_status <> 5";
                break;
            default:
                updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",mtime=NOW(),sort_id=:sortId" + " where  id=:id";

        }
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("robStatus", robStatus);
        paramsMap.put("sortId", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME));
        paramsMap.put("id", id);
        return this.executeUpdateSql(updateSQL, paramsMap);
    }

    /**
     * @return int
     * @Description 撤销货源改变订单表的状态（新方法：更新 接单状态robStatus 信息费状态costStatus）
     * <AUTHOR>
     * @Date 2019/1/10 17:10
     * @Param [id, robStatus]
     **/
    @Override
    public int saveChangeRobStatusByIdNew(Long id, String robStatus, Integer costStatus) throws Exception {
        // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款
        // 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态） 12车方取消装货
        String updateSQL = "";
        switch (robStatus) {
            case "2":
            case "12":
                updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",refuse_time=NOW(),mtime=NOW(),sort_id=:sortId" + " where  id=:id";
                break;
            case "4":
                updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",agree_time=NOW(),mtime=NOW(),sort_id=:sortId" + " where  id=:id and rob_status <> 5";
                break;
            //已关闭货源，将待支付信息费状态costStatus改为 6待支付关闭
            //支付状态payStatus改为 1支付失败
            case "11":
                updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",pay_status=1,order_new_status=35,mtime=NOW(),sort_id=:sortId,cost_status=:costStatus" + " where  id=:id and cost_status=10";
                break;
            case "13":
                updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",mtime=NOW(),order_new_status=30,sort_id=:sortId,cost_status=:costStatus" + " where id=:id ";
                break;
            default:
                updateSQL = "update tyt_transport_orders set rob_status=:robStatus" + ",mtime=NOW(),sort_id=:sortId" + " where  id=:id";

        }
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("robStatus", robStatus);
        paramsMap.put("sortId", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_ORDERS_NAME));
        paramsMap.put("id", id);
        //信息费状态： 6待支付关闭
        paramsMap.put("costStatus", costStatus);
        return this.executeUpdateSql(updateSQL, paramsMap);
    }

    /**
     * 获得订单信息
     *
     * @param tsOrderNo
     * @param robStatus
     * @param payUserId
     * @return
     */
    public TytTransportOrders getTytTransportOrders(String tsOrderNo, String robStatus, Long payUserId) {
        String hql = "from TytTransportOrders where payUserId=? and  tsOrderNo=? and robStatus=?";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, payUserId, tsOrderNo, robStatus);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 线下信息费订单
     *
     * @param tsOrderNo
     * @param payUserId
     * @return
     */
    @Override
    public TytTransportOrders getTytOfflineTransportOrders(String tsOrderNo, Long payUserId) {
        String hql = "from TytTransportOrders where payUserId=? and  tsOrderNo=? and infoFeeType != 0";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, payUserId, tsOrderNo);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public TytTransportOrders getTransportOrders(String tsOrderNo, Long payUserId) {
        String hql = "from TytTransportOrders where payUserId=? and  tsOrderNo=?";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, payUserId, tsOrderNo);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<TytTransportOrders> getTransportOrdersList(String tsOrderNo, String deParty, Long payUserId) {
        String hql = "from TytTransportOrders where tsOrderNo=?";
        List<TytTransportOrders> list = new ArrayList<>();
        if ((Integer.valueOf(deParty) == 1) && payUserId != null && payUserId > 0) {
            hql += " and payUserId=? ";
            list = this.getBaseDao().find(hql, tsOrderNo, payUserId);
        } else if ((Integer.valueOf(deParty) == 2)) {
            hql += " and payStatus=2 ";
            list = this.getBaseDao().find(hql, tsOrderNo);
        }
        logger.info("getTransportOrdersList hql is {} tsOrderNo is {} deParty is {} payUserId is {}", hql, tsOrderNo, deParty, payUserId);
        return list;
    }

    @Override
    public ResultMsgBean updateOrderCostStatus(TytTransportOrders orders, Integer operateType) throws Exception {
        //operateType 1.冻结2.解冻3.同意退款4.拒绝退款 5.确认打款 6延迟付款
        //操作方式 1:支付信息费 2:装货完成支付信息费 3:货方发起信息费退款 4:车方同意退款 5:车主拒绝退款
        //6:车主发起信息费冻结 7:车方信息费解冻 8:车主异常上报 9:货方异常上报 10:信息费即将7天自动支付 11:异常上报处理完成 12:车方发起延迟付款
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        int opStatus = 0;
        if (operateType == 1) {
            // 车方冻结信息费业务，6100版本变更为异常上报
            //orders.setCostStatus(OrderStatusType.FREEZE.getStatus());
            //opStatus = 6;


            //冻结信息费按自动提交异常上报处理
            orders.setCostStatus(OrderStatusType.EX_REPORT.getStatus());
            orders.setOrderNewStatus(OrderStatusEnum.WAIT_LOADED.getCode());
            opStatus = 8;
            TytTransportWaybillEx waybillEx = new TytTransportWaybillEx();
            waybillEx.setExParty("1");
            waybillEx.setExType("9");
            waybillEx.setExOther("冻结信息费转异常上报");
            transportWayBillExService.saveEx(orders, waybillEx.getExParty(), waybillEx.getExType(), waybillEx.getExOther(), null, null, null, null, null);
        }
        if (operateType == 2) {
            orders.setCostStatus(OrderStatusType.PAY.getStatus());
            orders.setRefundAmount(null);
            orders.setRefundTime(null);
            opStatus = 7;
            //update by sissy 2021-11-02 解冻后 T天自动到账
            int autoFinishDayNbr = Integer.parseInt(tytConfigService.getValue("autoFinishDayNbr").getValue());
            //实际应到账时间 实际应到账时间=在当前时间 + T天自动到账日期
            try {
                Date actualPayDate = TimeUtil.addDay(new Date(), autoFinishDayNbr);
                orders.setDePaymentDueDate(actualPayDate);
            } catch (Exception e) {
                e.printStackTrace();
                logger.info("解冻后 设置预估实际到账日期异常,异常信息为【{}】", e.getMessage());
            }
        }
        if (operateType == 3) {
            orders.setCostStatus(OrderStatusType.REFUNDED.getStatus());
            orders.setRobStatus("2");
            orders.setRefundTime(new Date());
            orders.setRefuseTime(new Date());
            opStatus = 4;
        }
        if (operateType == 4) {
            Integer delayStatus = orders.getDelayStatus();
            orders.setCostStatus(OrderStatusType.REFUSE_REFUND.getStatus());
            orders.setOrderNewStatus(OrderStatusEnum.WAIT_LOADED.getCode());
            orders.setRefundStatus(3);
            //限时货源 拒绝退款后服务费初始化
            if (Objects.equals(WaybillTimeLimitEnum.TIME_LIMIT.getType(), orders.getTimeLimitIdentification())) {
                String rate = tytConfigService.getStringValue("payServiceChargeRate", "0.05");
                orders.setPayServiceCharge(BigDecimal.valueOf(orders.getPayAmount()).multiply(new BigDecimal(rate)));
            }
            opStatus = 5;
            //当该订单没有拒绝退款过 需要对该订单进行延迟付款设置
            if (delayStatus != 2) {
                if (delayStatus == 0) {
                    //原本的实际到账日期
                    Date dePaymentDueDate = orders.getDePaymentDueDate();
                    //延迟付款T天数
                    int delayedPaymentDays = Integer.parseInt(tytConfigService.getValue("delayedPaymentDays").getValue());
                    //计算到期日期
                    Date dueDate = TimeUtil.addDay(dePaymentDueDate, delayedPaymentDays);
                    orders.setDePaymentDueDate(dueDate);
                }
                orders.setDelayStatus(2);
            }

        }
        if (operateType == 5) {
            orders.setCostStatus(OrderStatusType.CONFIRM_PAY.getStatus());
            orders.setOrderNewStatus(OrderStatusEnum.WAIT_UNLOADED_OR_RECEIVED.getCode());
            orders.setRobStatus("5");
            orders.setLoadTime(new Date());
            orders.setLoadingStatus(1);
            opStatus = 2;
            //从未购买过年货会员与货会员或已经全部过期小套餐的用户,订金限制在x天后结算
            Integer buyVipGoodsNum = userBuyGoodsService.getUserBuyGoodsRecord(orders.getUserId(), 1);
            // 判断是否是会员，如果是会员，判断购买金额是否大于1
            boolean vipPublishPermission = userPermissionService.isVipPublishPermission(orders.getUserId());
            // 判断是否是次卡
            Integer buyCardGoodsNum = userBuyGoodsService.getUserBuyGoodsRecord(orders.getUserId(), 2);
            boolean publishCardPermission = userPermissionService.isPublishCardPermission(orders.getUserId());
            // 是会员是次卡
            if (vipPublishPermission && publishCardPermission) {
                if ((buyVipGoodsNum == null || buyVipGoodsNum <= 0) && (buyCardGoodsNum == null || buyCardGoodsNum <= 0)) {
                    fitDePaymentMsg(orders, resultMsgBean);
                }
            } else if (vipPublishPermission && !publishCardPermission) {
                if (buyVipGoodsNum == null || buyVipGoodsNum <= 0) {
                    fitDePaymentMsg(orders, resultMsgBean);
                }
            } else if (!vipPublishPermission && publishCardPermission) {
                if ((buyCardGoodsNum == null || buyCardGoodsNum <= 0)) {
                    fitDePaymentMsg(orders, resultMsgBean);
                }
            } else {
                fitDePaymentMsg(orders, resultMsgBean);
            }
        }
        if (operateType == 6) {
            //原本的实际到账日期
            Date dePaymentDueDate = orders.getDePaymentDueDate();
            //延迟付款T天数
            int delayedPaymentDays = Integer.parseInt(tytConfigService.getValue("delayedPaymentDays").getValue());
            //计算到期日期
            Date dueDate = TimeUtil.addDay(dePaymentDueDate, delayedPaymentDays);
            orders.setDelayStatus(1);
            orders.setDePaymentDueDate(dueDate);
            opStatus = 12;//车方发起延迟付款
        }
        if (operateType == 7) {
            //原本的预计退款日期
            Date deRefundDueDate = orders.getDeRefundDueDate();
            //延迟退款T天数
            int delayedRefundDays = Integer.parseInt(tytConfigService.getValue("delayedRefundDays").getValue());
            //计算到期日期
            Date dueDate = TimeUtil.addDay(deRefundDueDate, delayedRefundDays);
            orders.setDelayRefundStatus(1);
            orders.setDeRefundDueDate(dueDate);
            opStatus = 14;//货方发起延迟退款
        }
        orders.setMtime(new Date());
        this.getBaseDao().update(orders);
        TytTransportWaybill waybill = transportWayBillService.getById(orders.getTsOrderNo());
        if (operateType == 5 && "2".equals(waybill.getInfoStatus())) {
            // 修改 tyt_transport_waybill
            String sql = "update tyt_transport_waybill set info_status=? ,load_time=now(),mtime=now(),sort_id=? where ts_order_no=?";
            this.getBaseDao().executeUpdateSql(sql, new Object[]{"4", tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_TRANSPORT_WAYBILL_NAME), orders.getTsOrderNo()});
        }
//        if (operateType == 3 &&ObjectUtil.equal(orders.getIsDealCar(),1)){
//            updateDealCar(orders.getId(),0,orders.getTsId());
//        }
        // 装货完成时更新客户成单信息
        if (opStatus == 2) {
            updateCustomInformation(orders);
        }
        //记日志，发短信，发push消息
        tytMqMessageService.saveAndSendInfofeeMq(orders, opStatus, null);
        return resultMsgBean;
    }

    /**
     * @return void
     * @description 装货完成更新客户信息
     * @date 2023/9/14 16:58
     * @Param transportOrder:
     */
    private void updateCustomInformation(TytTransportOrders transportOrder) {
        // 先查是否有自己发的货,如果没有，再查询是否有代调代发得货
        User user = null;
        try {
            user = userService.getByUserId(transportOrder.getUserId());
            if (user != null) {
                String cellPhone = user.getCellPhone();
                TytCustomInformation tytCustomInformation = customInformationMapper.selectByGoodsPhone(cellPhone);
                if (tytCustomInformation != null) {
                    tytCustomInformation.setPerformanceTime(new Date());
                    customInformationMapper.updateByPrimaryKeySelective(tytCustomInformation);
                } else {
                    // 根据货源id和给货货主手机号，查看是否是这个人的货
                    TytTransportDispatch tytTransportDispatch = tytTransportDispatchMapper.getTytTransportDispatchBySrcId(transportOrder.getTsId());
                    if (tytTransportDispatch != null && StringUtils.isNotBlank(tytTransportDispatch.getGiveGoodsPhone())) {
                        tytCustomInformation = customInformationMapper.selectByGoodsPhone(tytTransportDispatch.getGiveGoodsPhone());
                        if (tytCustomInformation != null) {
                            tytCustomInformation.setPerformanceTime(new Date());
                            customInformationMapper.updateByPrimaryKeySelective(tytCustomInformation);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("更新客户成单信息失败，订单userId:{}", transportOrder.getUserId(), e);
        }
    }

    private ResultMsgBean fitDePaymentMsg(TytTransportOrders orders, ResultMsgBean resultMsgBean) throws Exception {
        //当前日期
        String today = TimeUtil.formatDateTime(new Date(System.currentTimeMillis()));
        //实际应到账时间
        Date actualPayDate = orders.getDePaymentDueDate();
        //剩余自动结算天数=实际应到账时间-当前日期
        int tDay = TimeUtil.getDays(today, TimeUtil.formatDateTime(actualPayDate));
        logger.info("updateOrderCostStatus method today:【{}】, actualPayDate: 【{}】, tDay:【{}】", today, actualPayDate, tDay);
        if (tDay >= 1) {
            resultMsgBean.setMsg("与您交易的用户为非会员，订金将在" + tDay + "天后结算给货方(订金即时到账权益仅会员用户享有)");
        } else {
            resultMsgBean.setMsg("订金将于今日结算，请及时同交易方确认");
        }
        resultMsgBean.setCode(ReturnCodeConstant.NO_MEMBER_REMIND);
        return resultMsgBean;
    }

    @Override
    public int updateOrderIsDisplay(String tsOrderNo, String deParty, Long payUserId) throws Exception {
        if (Integer.valueOf(deParty) == 1) {
            return super.getBaseDao().executeUpdateSql("update tyt_transport_orders set car_show = 1,mtime=mtime   where ts_order_no = ? and pay_user_Id=? ", new Object[]{tsOrderNo, payUserId});
        } else {
            return super.getBaseDao().executeUpdateSql("update tyt_transport_orders set goods_show = 1,mtime=mtime   where ts_order_no = ?  ", new Object[]{tsOrderNo});
        }

    }

    @Override
    public void updateOrderForGiveBack(TytTransportOrders orders, Long refundAmount, String refundReason) throws Exception {
        Integer refundType = null;
        boolean flag = orders.getTotalOrderAmount().longValue() == refundAmount.longValue();
        if (flag) {
            orders.setCostStatus(OrderStatusType.REFUNDED.getStatus());
            orders.setOrderNewStatus(OrderStatusEnum.WAIT_UNLOADED_OR_RECEIVED.getCode());
            orders.setRobStatus("2");
            //如果退款金额等于支付金额 不收取服务费
            if (Objects.equals(WaybillTimeLimitEnum.TIME_LIMIT.getType(), orders.getTimeLimitIdentification())) {
                orders.setPayServiceCharge(BigDecimal.ZERO);
            }
            if (ObjectUtil.equal(orders.getIsDealCar(), 1)) {
                orders.setIsDealCar(0);
            }
            refundType = 1;
        } else {
            orders.setCostStatus(OrderStatusType.REFUNDING.getStatus());
            orders.setOrderNewStatus(OrderStatusEnum.WAIT_LOADED.getCode());
            //限时订单 设置服务费
            if (Objects.equals(WaybillTimeLimitEnum.TIME_LIMIT.getType(), orders.getTimeLimitIdentification())) {
                String rate = tytConfigService.getStringValue("payServiceChargeRate", "0.05");
                orders.setPayServiceCharge(BigDecimal.valueOf(orders.getPayAmount() - refundAmount).multiply(new BigDecimal(rate)));
            }
            refundType = 2;
        }
        if (Strings.isNotBlank(refundReason)) {
            orders.setRefundReason(refundReason);
            if ("已装货,协商退款".equals(refundReason) || "已装货,退还订金".equals(refundReason) || "货已送达,协商退款".equals(refundReason) || "货已送达,退还订金".equals(refundReason)) {
                orders.setLoadingStatus(1);
                orders.setLoadTime(new Date());
                if(flag){
                    orders.setOrderNewStatus(OrderStatusEnum.WAIT_UNLOADED_OR_RECEIVED.getCode());
                }
            }else{
                //未装货原因
                if(flag){
                    orders.setOrderNewStatus(OrderStatusEnum.CANCEL.getCode());
                }
            }
        }
        orders.setRefundAmount(refundAmount);
        orders.setRefundTime(new Date());
        orders.setRefundStatus(1); //退款状态更新为 1.退款中
        this.getBaseDao().update(orders);
        //三方平台类型 1:满帮
        Integer thirdpartyPlatformType = orders.getThirdpartyPlatformType();
        if (thirdpartyPlatformType != null && thirdpartyPlatformType == 1) {
            //发送满帮平台退款的MQ消息
            tytMqMessageService.saveAndSendMbInfofeeMq(orders);
        } else {
            //记日志，发短信，发push消息
            tytMqMessageService.saveAndSendInfofeeMq(orders, 3, refundType);
        }
    }

    @Override
    public List<GoodsDetailOrderLoadingBean> getLoadingByPayUser(Long payUserId, String tsOrderNo, String robStatus) throws Exception {
        String selectSQL = "select ts_order_no goodsOrderNo,pay_link_phone carOwnerTelephone," + "pay_amount payAgencyMoney,pay_end_time payEndTime,agree_time goodsOwnerAgreeTime" + " from tyt_transport_orders where pay_user_id=? and ts_order_no=? and rob_status=? order by sort_id desc";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("goodsOrderNo", Hibernate.STRING);
        scalarMap.put("carOwnerTelephone", Hibernate.STRING);
        scalarMap.put("payAgencyMoney", Hibernate.LONG);
        scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
        scalarMap.put("goodsOwnerAgreeTime", Hibernate.TIMESTAMP);
        return this.getBaseDao().search(selectSQL, scalarMap, GoodsDetailOrderLoadingBean.class, new Object[]{payUserId, tsOrderNo, robStatus});
    }

    @Override
    public List<PayUserOrderBean> getByPayUserOnce(Long payUserId, String tsOrderNo) throws Exception {
        String selectSQL = "select id,ts_order_no goodsOrderNo,pay_user_id carOwnerUserId,pay_cell_phone carOwnerRegisterPhone" + ",pay_link_phone carOwnerTelephone,pay_amount payAgencyMoney,pay_end_time payEndTime,pay_type payChannel" + ",rob_status robStatus,agree_time goodsOwnerAgreeTime,refuse_time refuseTime,load_time carOwnerLoadfinishedTime,pay_status payStatus from tyt_transport_orders where pay_user_id=? and ts_order_no=? order by sort_id desc";
        Map<String, Type> paramsMap = new HashMap<String, Type>();
        paramsMap.put("id", Hibernate.LONG);
        paramsMap.put("goodsOrderNo", Hibernate.STRING);
        paramsMap.put("carOwnerUserId", Hibernate.LONG);
        paramsMap.put("carOwnerRegisterPhone", Hibernate.STRING);
        paramsMap.put("carOwnerTelephone", Hibernate.STRING);
        paramsMap.put("payAgencyMoney", Hibernate.LONG);
        paramsMap.put("payEndTime", Hibernate.TIMESTAMP);
        paramsMap.put("payChannel", Hibernate.STRING);
        paramsMap.put("robStatus", Hibernate.STRING);
        paramsMap.put("goodsOwnerAgreeTime", Hibernate.TIMESTAMP);
        paramsMap.put("refuseTime", Hibernate.TIMESTAMP);
        paramsMap.put("carOwnerLoadfinishedTime", Hibernate.TIMESTAMP);
        paramsMap.put("payStatus", Hibernate.STRING);
        return this.getBaseDao().search(selectSQL, paramsMap, PayUserOrderBean.class, new Object[]{payUserId, tsOrderNo});

    }

    @Override
    public void saveChangeWaitToDisappear(Long userId, String transportOrderNo) throws Exception {
        List<GoodsDetailOrderBean> noPayList = this.getByUser(userId, transportOrderNo, "0");
        for (GoodsDetailOrderBean noPayOrder : noPayList) {
            this.saveChangeRobStatusById(noPayOrder.getId(), "11");
            // 车主的待支付气泡减一
            tytBubbleService.updateBubbleNumber(noPayOrder.getCarOwnerUserId(), "1", "1", -1);
        }

    }

    /**
     * @return void
     * @Description 撤销、无效货源时将待支付状态设置为 6待支付关闭
     * <AUTHOR>
     * @Date 2019/1/9 10:22
     * @Param [userId, transportOrderNo, costStatus]
     **/
    @Override
    public void saveChangeWaitToDisappearNew(Long userId, String transportOrderNo) throws Exception {
        List<GoodsDetailOrderBean> noPayList = this.getByUserNew(userId, transportOrderNo, "0", 10);
        for (GoodsDetailOrderBean noPayOrder : noPayList) {
            //接单状态：11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
            this.saveChangeRobStatusByIdNew(noPayOrder.getId(), "11", 6);
            // 车主的待支付气泡减一
            tytBubbleService.updateBubbleNumber(noPayOrder.getCarOwnerUserId(), "1", "1", -1);
        }
    }

    @Override
    public Long getExceptPartPayUserCount(Long userId, String tsOrderNo, String robStatus, Long[] notInPayUserIds) {
        String selectSQL = "select count(*) from tyt_transport_orders" + " where user_id=:userId and ts_order_no=:tsOrderNo and rob_status=:robStatus and pay_user_id not in(:notInPayUserIds)";
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("userId", userId);
        paramsMap.put("tsOrderNo", tsOrderNo);
        paramsMap.put("robStatus", robStatus);
        paramsMap.put("notInPayUserIds", notInPayUserIds);
        BigInteger count = this.getBaseDao().queryByMap(selectSQL, paramsMap, null, null);
        if (count == null)
            return 0l;
        return count.longValue();
    }

    @Override
    public List<GoodsDetailOrderBean> getExceptPartPayUser(Long userId, String tsOrderNo, String robStatus, Long[] notInPayUserIds) {
        String selectSQL = "select id,ts_order_no goodsOrderNo,pay_user_id carOwnerUserId,pay_cell_phone carOwnerRegisterPhone" + ",pay_link_phone carOwnerTelephone,pay_amount payAgencyMoney,pay_end_time payEndTime,pay_type payChannel" + " from tyt_transport_orders where user_id=:userId and ts_order_no=:tsOrderNo and rob_status=:robStatus and pay_user_id not in(:notInPayUserIds)";
        Map<String, Type> typeMap = new HashMap<String, Type>();
        typeMap.put("id", Hibernate.LONG);
        typeMap.put("goodsOrderNo", Hibernate.STRING);
        typeMap.put("carOwnerUserId", Hibernate.LONG);
        typeMap.put("carOwnerRegisterPhone", Hibernate.STRING);
        typeMap.put("carOwnerTelephone", Hibernate.STRING);
        typeMap.put("payAgencyMoney", Hibernate.LONG);
        typeMap.put("payEndTime", Hibernate.TIMESTAMP);
        typeMap.put("payChannel", Hibernate.STRING);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        paramMap.put("tsOrderNo", tsOrderNo);
        paramMap.put("notInPayUserIds", notInPayUserIds);
        paramMap.put("robStatus", robStatus);
        return this.getBaseDao().search(selectSQL, typeMap, GoodsDetailOrderBean.class, paramMap);

    }

    @Override
    public SimpleOrderBean getLastOrderByPayUser(String transportNo, Long payUserId) throws Exception {
        String selectSQL = "select id,rob_status robStatus,pay_status payStatus,cost_status costStatus,pay_amount payAmount from tyt_transport_orders where sort_id=(select max(sort_id) from tyt_transport_orders where pay_user_id=:payUserId and ts_order_no=:transportNo)";
        Map<String, Type> typeMap = new HashMap<String, Type>();
        typeMap.put("id", Hibernate.LONG);
        typeMap.put("robStatus", Hibernate.STRING);
        typeMap.put("payStatus", Hibernate.STRING);
        typeMap.put("costStatus", Hibernate.INTEGER);
        typeMap.put("payAmount", Hibernate.LONG);
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("transportNo", transportNo);
        paramsMap.put("payUserId", payUserId);
        return this.getBaseDao().queryByMap(selectSQL, paramsMap, SimpleOrderBean.class, typeMap);
    }

    @Override
    public List<Long> getTsIdsByRobStatus(Long userId, Integer day, String... robStatus) {
        String selectSQL = "select * from tyt_transport_orders where pay_user_id=? and rob_status in (?,?,?) and create_time > ? order by sort_id desc limit ?";
        Date date = null;
        try {
            date = TimeUtil.parseString(TimeUtil.formatDate(TimeUtil.addDay(-day)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        Integer pageSize = tytConfigService.getIntValue("car_insur_import_page_size", 30);
        final Object[] params = {
                userId,
                robStatus[0],
                robStatus[1],
                robStatus[2],
                date,
                pageSize
        };
        List<TytTransportOrders> idList = this.getBaseDao().queryForList(selectSQL, params);
        if (idList == null || idList.size() < 1) {
            return null;
        }
        List<Long> tsIds = new ArrayList<>();
        for (TytTransportOrders orders : idList) {
            tsIds.add(orders.getTsId());
        }
        return tsIds;
    }

    /**
     * @return com.tyt.infofee.bean.CarOwnerInfoFeeBean
     * @Description 获取车主信息费列表接口
     * <AUTHOR>
     * @Date 2018/12/24 10:57
     * @Param [userId, queryActionType, queryID]
     **/
    @Override
    public CarOwnerInfoFeeBean getCarOwnerInfofeeList(Long userId, int queryActionType, long queryID, String clientVersion, Integer queryType, String clientSign) throws Exception {

        CarOwnerInfoFeeBean carOwnerInfoFeeBean = new CarOwnerInfoFeeBean();
        //待支付信息费笔数
        int unPaidOrdersNum = this.getUnPaidOrdersNum(userId);
        //设置待支付信息费笔数updateOrderForInfoFeeGiveBack
        carOwnerInfoFeeBean.setCarOwnerNopayInfofeeNum(unPaidOrdersNum);

        List<Object> list = new ArrayList<Object>();
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");

        StringBuffer sql = new StringBuffer("select a.id id,a.sort_id sortId, a.ts_order_no tsOrderNo, " +
                " a.start_point startPoint,a.dest_point destPoint,a.task_content taskContent, a.car_show carShow, a.goods_show goodsShow, " +
                " a.pay_end_time payEndTime,a.user_id userId,a.pub_user_name userName, " +
                " a.de_payment_dueDate DePaymentDueDate,a.delay_status delayStatus,a.ex_cancel_status exCancelStatus, a.thirdparty_platform_type thirdpartyPlatformType," +
                " IFNULL(a.total_order_amount,0) totalOrderAmount,a.coupon_amount couponAmount,a.tec_service_fee tecServiceFee," +
                " a.pay_amount payAmount,a.cost_status costStatus,IFNULL(a.refund_amount,0) refundAmount, a.is_deal_car isDealCar,a.time_limit_identification timeLimitIdentification," +
                " a.ts_id tsId, a.head_city headCity, a.head_no headNo, a.tail_city tailCity, a.tail_no tailNo, a.tel tel, a.tel3 tel3, a.tel4 tel4,  " +
                " (select auth from tyt_car where id = a.car_id) as auth, (select car_type from tyt_car where id = a.car_id) as carType, a.car_id carId, a.ctime createTime, b.ctime ctime, " +
                " b.weight weight, b.length length, b.wide wide, b.high high, b.begin_loading_time beginLoadingTime, " +
                "b.begin_unload_time beginUnloadTime, b.loading_time loadingTime, b.unload_time unloadTime, b.price price, b.auth_name authName, b.excellent_goods excellentGoods," +
                "b.publish_type publishType,a.refund_flag refundFlag,a.delay_refund_status delayRefundStatus" +
                ",CASE a.cost_status WHEN 50 THEN IF(trwe.id is null,0,1) ELSE 1 END AS evaluateStatus,b.guarantee_goods guaranteeGoods,a.machine_remark machineRemark," +
                "teco.refund_amount tecServiceFeeRefundAmount," +
                "teco.refund_arrival_time tecServiceFeeRefundTime " +
                " from tyt_transport_orders a FORCE INDEX (Index_user_id_status_time) left join tyt_transport_order_snapshot b on a.ts_id = b.src_msg_id and a.id = b.order_id " +
                " LEFT JOIN tyt_transport_waybill_ex tre ON tre.order_id = a.id and tre.ex_status=2 AND tre.order_type = 0 LEFT JOIN tyt_transport_waybill_ex_evaluate trwe ON trwe.ex_id = tre.id and trwe.user_id= ? " +
                " left join tyt_transport_technical_order teco on a.technical_service_no = teco.technical_service_no and teco.refund_status = 2 " +
                " where 1=1 and car_show=0");

        list.add(userId);
        //新增||Integer.parseInt(clientSign)==1 兼容PC调用时 可使用查询条件
        if (Integer.parseInt(clientVersion) >= 6040 || (Integer.parseInt(clientSign) == 1 && Integer.parseInt(clientVersion) > 3721)) {
            String beginTime, endTime;
            switch (queryType) {
                case 0://全部 信息费支付状态>=10 退款中接口

                    beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
                    endTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), 1)) + " 00:00:00";
                    sql.append(" and (( a.cost_status = 10 ");
                    sql.append(" and a.ctime>?");
                    list.add(beginTime);
                    sql.append(" and a.ctime<=?)");
                    list.add(endTime);
                    sql.append(" or a.cost_status >= 15 )");

                    break;
                case 1://待支付 信息费支付状态
                    beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
                    endTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), 1)) + " 00:00:00";
                    sql.append(" and a.cost_status = 10 ");
                    sql.append(" and a.ctime>? ");
                    list.add(beginTime);
                    sql.append(" and a.ctime<=? ");
                    list.add(endTime);
                    break;
                case 2: //交易中 信息费支付状态 15已支付 21拒绝退款 30退款中 update by sissy 提升线上成交率需求 删除冻结中状态  将异常上报的数据 放入交易中
                    sql.append(" and a.cost_status in (15,21,30,25) ");
                    break;
                case 3: //冻结中 20已冻结 25异常上报
                    sql.append(" and a.cost_status in(20) ");
                    break;
                case 4: //已完成 35已退款，40已打款，45自动收款，50异常完成
                    sql.append(" and a.cost_status >= 35 ");
                    break;
                case 5: //待评价 35已退款，40已打款，45自动收款，50异常完成
                    sql.append(" and a.cost_status >= 35 ");
                    Date beginDateTime = DateUtils.addDays(new Date(), -30);
                    sql.append(" AND a.mtime > ? ");
                    list.add(beginDateTime);
                    sql.append(" AND a.pay_user_id = ? ");
                    list.add(userId);
                    sql.append(" and a.id not in (select order_id from feedback_user where create_time > ? and post_user_type = 1 and post_user_id= ? )");
                    list.add(beginDateTime);
                    list.add(userId);
                    sql.append(" AND (CASE a.cost_status WHEN 50 THEN IF(trwe.id is null,0,1) ELSE 1 END) = 1");
                    break;
                case 8: //此处的8 仅仅是为了兼容pc端 等后期 老版本pc页面替换后 不再使用
                    sql.append(" and a.cost_status >= 15 ");
                    break;
                default:
            }


        } else {
            sql.append(" and a.cost_status >= 15 "); //信息费支付状态>=15
        }

        sql.append(" and a.pay_user_id = ? ");
        list.add(userId);

        if (queryActionType == 2) {
            sql.append(" and a.sort_id<?");
            list.add(queryID);
        }
        sql.append(" order by a.sort_id desc ");

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("sortId", Hibernate.LONG);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("userName", Hibernate.STRING);
        scalarMap.put("payAmount", Hibernate.LONG);
        scalarMap.put("tecServiceFee", Hibernate.LONG);
        scalarMap.put("costStatus", Hibernate.INTEGER);
        scalarMap.put("refundAmount", Hibernate.LONG);
        scalarMap.put("tsId", Hibernate.LONG);
        //重量 单位:吨 长宽高 单位：米
        scalarMap.put("weight", Hibernate.STRING);
        scalarMap.put("length", Hibernate.STRING);
        scalarMap.put("wide", Hibernate.STRING);
        scalarMap.put("high", Hibernate.STRING);
        scalarMap.put("loadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("unloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("beginLoadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("beginUnloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("price", Hibernate.STRING);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        scalarMap.put("auth", Hibernate.INTEGER);
        scalarMap.put("carType", Hibernate.STRING);
        scalarMap.put("carId", Hibernate.LONG);
        scalarMap.put("ctime", Hibernate.TIMESTAMP);
        scalarMap.put("createTime", Hibernate.TIMESTAMP);
        scalarMap.put("tel", Hibernate.STRING);
        scalarMap.put("tel3", Hibernate.STRING);
        scalarMap.put("tel4", Hibernate.STRING);
        scalarMap.put("isDealCar", Hibernate.INTEGER);
        scalarMap.put("timeLimitIdentification", Hibernate.INTEGER);
        scalarMap.put("dePaymentDueDate", Hibernate.TIMESTAMP);
        scalarMap.put("delayStatus", Hibernate.INTEGER);
        scalarMap.put("exCancelStatus", Hibernate.INTEGER);
        scalarMap.put("totalOrderAmount", Hibernate.LONG);
        scalarMap.put("couponAmount", Hibernate.LONG);
        scalarMap.put("carShow", Hibernate.INTEGER);
        scalarMap.put("goodsShow", Hibernate.INTEGER);
        scalarMap.put("publishType", Hibernate.SHORT);
        scalarMap.put("refundFlag", Hibernate.INTEGER);
        scalarMap.put("delayRefundStatus", Hibernate.INTEGER);
        scalarMap.put("evaluateStatus", Hibernate.INTEGER);
        scalarMap.put("excellentGoods", Hibernate.INTEGER);
        scalarMap.put("authName", Hibernate.STRING);
        scalarMap.put("guaranteeGoods", Hibernate.INTEGER);
        scalarMap.put("thirdpartyPlatformType", Hibernate.INTEGER);
        scalarMap.put("machineRemark", Hibernate.STRING);
        scalarMap.put("tecServiceFeeRefundAmount", Hibernate.LONG);
        scalarMap.put("tecServiceFeeRefundTime", Hibernate.TIMESTAMP);
        List<TransportOrdersListBean> carOwnerInfofeeList = this.getBaseDao().search(sql.toString(), scalarMap, TransportOrdersListBean.class, list.toArray(), 1, pageSize);

        //再查询挂车信息，获取到挂车样式数据
        Map<Long, Integer> carDetailTailCarIdIsPureFlatMap = makeCarDetailTailCarIdIsPureFlatMapData(carOwnerInfofeeList);

        for (TransportOrdersListBean bean : carOwnerInfofeeList) {

            bean.setIsPureFlat(carDetailTailCarIdIsPureFlatMap.get(bean.getCarId()));

            bean.setGoodsPhoneList(new ArrayList<>());
            String newName = StringUtil.hidePhoneInStr(bean.getUserName());
            bean.setUserName(newName);
            // 官方授权账号
//            String authNameTea = tytOwnerAuthService.getAuthNameTea(bean.getAuthName());
//            if(org.apache.commons.lang.StringUtils.isNotBlank(authNameTea)){
//                bean.setAuthNameTea(authNameTea);
//                bean.setAuthName(null);
//            }
            initPhone(bean.getGoodsPhoneList(), bean.getTel(), bean.getUserName());
            initPhone(bean.getGoodsPhoneList(), bean.getTel3(), bean.getUserName());
            initPhone(bean.getGoodsPhoneList(), bean.getTel4(), bean.getUserName());

            if (RefundFlagEnum.退还.getId().equals(bean.getRefundFlag())) {
                //调用获取确认退款时间
                getConfirmRefundRemark(bean);
            } else {
                //调用获取确认付款时间
                getConfirmPaymentRemark(bean);
            }
            //查看最新的一个异常上报订单
            List<TytTransportWaybillEx> transportWaybillExs = transportWayBillExService.getWayBillExsByOrderId(bean.getId(), 0, "0");
            if (transportWaybillExs != null && transportWaybillExs.size() > 0) {
                bean.setExParty(transportWaybillExs.get(0).getExParty());
            } else {
                bean.setExParty("0");
            }

            //增加待评价tab，待评价查询不用再查询是否能评价
            if (queryType!=5) {
                // 是否能评价, 完成订单才有
                try {
                    CheckResult<String> checkResult = feedbackUserService.checkCanPostFeedBack(bean.getId(), userId, Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign)));
                    bean.setCanPostFeedBack(checkResult.isSuccess());
                } catch (Exception e) {
                    // ignore
                }
            } else {
                bean.setCanPostFeedBack(true);
            }

            Car paramCar = new Car();
            paramCar.setAuth(bean.getAuth() == null ? null : String.valueOf(bean.getAuth()));
            paramCar.setCarType(bean.getCarType() == null ? null : Integer.valueOf(bean.getCarType()));
            paramCar.setIsPureFlat(bean.getIsPureFlat() == null ? null : Integer.valueOf(bean.getIsPureFlat()));
            paramCar.setTailNo(bean.getTailNo());
            paramCar.setTailCity(bean.getTailCity());
            bean.setCarIsNeedImprovenData(carService.carIsNeedImprovenData(paramCar));

        }
        if (carOwnerInfofeeList.size() > 0) {
            carOwnerInfofeeList.stream().forEach(
                    infoFeePayinfo -> {
                        String statusDesc = InfofeeDetailServiceImpl.infoFeeStatusDesc(false, infoFeePayinfo.getRefundFlag(), String.valueOf(infoFeePayinfo.getCostStatus()),
                                infoFeePayinfo.getDelayStatus(), infoFeePayinfo.getDelayRefundStatus(), infoFeePayinfo.getDeRefundDueDate());
                        infoFeePayinfo.setStatusDesc(statusDesc);
                    }
            );

            carOwnerInfoFeeBean.setCarOwnerInfofeeList(carOwnerInfofeeList);
        }
        return carOwnerInfoFeeBean;
    }

    /**
     * 构造carId-挂车样式map数据，方面后面直接获取
     * @param carOwnerInfofeeList
     * @return true：属于待完善车辆；false：不属于
     */
    private Map<Long, Integer> makeCarDetailTailCarIdIsPureFlatMapData(List<TransportOrdersListBean> carOwnerInfofeeList) {
        Map<Long, Integer> carDetailTailCarIdIsPureFlatMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(carOwnerInfofeeList)) {
            List<Long> carIdList = carOwnerInfofeeList.stream().map(TransportOrdersListBean::getCarId).filter(Objects::nonNull).collect(Collectors.toList());
            if (carIdList.isEmpty()) {
                return carDetailTailCarIdIsPureFlatMap;
            }
            List<CarDetailTail> carDetailTails = carDetailTailService.selectCarDetailTailByCarIdList(carIdList);
            if (CollectionUtils.isNotEmpty(carDetailTails)) {
                for (CarDetailTail carDetailTail : carDetailTails) {
                    carDetailTailCarIdIsPureFlatMap.put(carDetailTail.getCarId(), carDetailTail.getIsPureFlat());
                }
            }
        }
        return carDetailTailCarIdIsPureFlatMap;
    }

    private static void initPhone(List<MapEntity> phones, String phone, String name) {
        if (StringUtils.isNotBlank(phone)) {
            phones.add(new MapEntity(name, phone));
        }
    }

    /**
     * @return com.tyt.infofee.bean.ShipperInfoFeeBean
     * @Description 获取货主信息费列表接口
     * <AUTHOR>
     * @Date 2018/12/24 15:45
     * @Param [userId, queryActionType, queryID]
     **/
    @Override
    public ShipperInfoFeeBean getShipperInfofeeList(Long userId, int queryActionType, long queryID, int costStatus) throws Exception {

        ShipperInfoFeeBean shipperInfoFeeBean = new ShipperInfoFeeBean();
        //查询运单列表信息(运单->信息费 一对多)
        List<Object> list = new ArrayList<Object>();
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");

        StringBuffer sql = new StringBuffer("select a.ts_order_no tsOrderNo,a.sort_id sortId,a.ts_id tsId," +
                "a.start_point startPoint,a.dest_point destPoint,a.task_content taskContent," +
                "a.create_time createTime,time_limit_identification timeLimitIdentification," +
                "a.guarantee_goods guaranteeGoods, a.machine_remark machineRemark" +
                /*
                "b.weight weight, b.length length, b.wide wide, b.high high, b.begin_loading_time beginLoadingTime, " +
                "b.loading_time loadingTime, b.begin_unload_time beginUnloadTime, b.unload_time unloadTime, b.price price, " +
                "b.publish_type publishType " +
                */
                " from tyt_transport_waybill a where 1=1 ");
        sql.append(" and a.user_id = ?  and a.is_info_fee = 1 and a.info_status in (2,3,4,5,7) "); //信息费运单状态：0待接单  1有人支付成功 （货主的待同意）2装货中（车主是待装货）3车主装货完成  4系统装货完成 5异常上报 6线下成交 7.异常处理完成
        list.add(userId);

        if (queryActionType == 2) {
            sql.append(" and a.sort_id<?");
            list.add(queryID);
        }
        sql.append(" order by a.sort_id desc ");

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("sortId", Hibernate.LONG);
        scalarMap.put("tsId", Hibernate.LONG);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("createTime", Hibernate.TIMESTAMP);
        scalarMap.put("timeLimitIdentification", Hibernate.INTEGER);
        scalarMap.put("guaranteeGoods", Hibernate.INTEGER);
        scalarMap.put("machineRemark", Hibernate.STRING);
        //重量 单位:吨 长宽高 单位：米
        /*
        scalarMap.put("weight", Hibernate.STRING);
        scalarMap.put("length", Hibernate.STRING);
        scalarMap.put("wide", Hibernate.STRING);
        scalarMap.put("high", Hibernate.STRING);
        scalarMap.put("loadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("unloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("beginLoadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("beginUnloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("price", Hibernate.STRING);
        scalarMap.put("publishType", Hibernate.SHORT);
         */
        List<TransportWayBillListBean> shipperInfofeeList = this.getBaseDao().search(sql.toString(), scalarMap, TransportWayBillListBean.class, list.toArray(), 1, pageSize);
        //货主信息费最终筛选完的集合
        List<TransportWayBillListBean> shipperInfofees = new LinkedList<>();

        Set<Long> srcMsgIds = new HashSet<>();
        //运单号集合
        List<Long> tsOrderNoList = new ArrayList<Long>();
        //查询运单关联的信息费列表信息
        if (shipperInfofeeList != null && shipperInfofeeList.size() > 0) {
            Map<String, TransportWayBillListBean> wayBillMap = new HashMap<>();

            //运单记录
            for (TransportWayBillListBean transportWayBillListBean : shipperInfofeeList) {
                //运单号
                String tsOrderNo = transportWayBillListBean.getTsOrderNo();
                //将运单号放入到集合中
                tsOrderNoList.add(Long.parseLong(tsOrderNo));
                wayBillMap.put(tsOrderNo, transportWayBillListBean);
            }

            //运单关联的信息费集合
            List<TransportOrdersListBean> infofeeList = this.getInfofeeList(userId, tsOrderNoList, costStatus);

            if (CollectionUtils.isNotEmpty(infofeeList)) {
                for (TransportOrdersListBean transportOrdersListBean : infofeeList) {
                    //查看最新的一个异常上报订单
                    List<TytTransportWaybillEx> transportWaybillExs = transportWayBillExService.getWayBillExsByOrderId(transportOrdersListBean.getId(), 0, "0");
                    if (transportWaybillExs != null && transportWaybillExs.size() > 0) {
                        transportOrdersListBean.setExParty(transportWaybillExs.get(0).getExParty());
                    } else {
                        transportOrdersListBean.setExParty("0");
                    }

                    String statusDesc = InfofeeDetailServiceImpl.infoFeeStatusDesc(true, transportOrdersListBean.getRefundFlag(), String.valueOf(transportOrdersListBean.getCostStatus()),
                            transportOrdersListBean.getDelayStatus(), transportOrdersListBean.getDelayRefundStatus(), transportOrdersListBean.getDeRefundDueDate());
                    transportOrdersListBean.setStatusDesc(statusDesc);

                    String orderNo = transportOrdersListBean.getTsOrderNo();

                    TransportWayBillListBean wayBillListBean = wayBillMap.get(orderNo);

                    if (wayBillListBean != null) {
                        if (RefundFlagEnum.退还.getId().equals(transportOrdersListBean.getRefundFlag())) {
                            //调用获取确认退款时间
                            getConfirmRefundRemark(transportOrdersListBean);
                        } else {
                            //调用获取确认付款时间
                            getConfirmPaymentRemark(transportOrdersListBean);
                        }
                        wayBillListBean.addInfofee(transportOrdersListBean);

                        srcMsgIds.add(wayBillListBean.getTsId());
                        //有支付信息费的运单对象
                        //shipperInfofees.add(transportWayBillListBean);
                    }

                    //获取平台交易量
                    Integer tradeNums = infofeeDetailService.getTradeNums(transportOrdersListBean.getPayUserId());
                    transportOrdersListBean.setTradeNums(tradeNums == null ? "0" : String.valueOf(tradeNums));

                    try {
                        // 是否能评价
                        CheckResult<String> checkResult = feedbackUserService.checkCanPostFeedBack(transportOrdersListBean.getId(),
                                userId, 2);
                        transportOrdersListBean.setCanPostFeedBack(checkResult.isSuccess());
                    } catch (Exception e) {
                        // ignore
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(srcMsgIds)) {
                //限时货源
                List<Long> longs = backendTransportMapper.selectMsgIdsByMsgId(srcMsgIds);
                Set<Long> backendMsgIdSet = new HashSet<>(longs);
                for (TransportWayBillListBean shipperInfofee : shipperInfofees) {
                    if (backendMsgIdSet.contains(shipperInfofee.getTsId())) {
                        shipperInfofee.setIsBackendTransport(1);
                    }
                }
            }

            //过滤掉infoList为空的信息
            for (TransportWayBillListBean transportWayBillListBean : shipperInfofeeList) {

                List<TransportOrdersListBean> billInfoFeeList = transportWayBillListBean.getInfofeeList();
                if (CollectionUtils.isNotEmpty(billInfoFeeList)) {
                    //设置快照表货物信息
                    TransportOrdersListBean transportOrdersListBean = billInfoFeeList.get(billInfoFeeList.size() - 1);
                    TytTransportBackend tytTransportBackends = backendTransportMapper.selectStatusByMsgId(transportWayBillListBean.getTsId());

                    transportWayBillListBean.setWeight(transportOrdersListBean.getWeight());
                    transportWayBillListBean.setLength(transportOrdersListBean.getLength());
                    transportWayBillListBean.setWide(transportOrdersListBean.getWide());
                    transportWayBillListBean.setHigh(transportOrdersListBean.getHigh());
                    transportWayBillListBean.setLoadingTime(transportOrdersListBean.getLoadingTime());
                    transportWayBillListBean.setUnloadTime(transportOrdersListBean.getUnloadTime());
                    transportWayBillListBean.setBeginLoadingTime(transportOrdersListBean.getBeginLoadingTime());
                    transportWayBillListBean.setBeginUnloadTime(transportOrdersListBean.getBeginUnloadTime());
                    transportWayBillListBean.setPrice(transportOrdersListBean.getPrice());
                    transportWayBillListBean.setPublishType(transportOrdersListBean.getPublishType());
                    transportWayBillListBean.setExcellentGoods(transportOrdersListBean.getExcellentGoods());
                    if (tytTransportBackends != null) {
                        transportWayBillListBean.setStatus(tytTransportBackends.getStatus());
                        transportWayBillListBean.setOrderStatus(tytTransportBackends.getOrderStatus());
                        transportWayBillListBean.setCancelConfirm(tytTransportBackends.getCancelConfirm());

                    }
                    shipperInfofees.add(transportWayBillListBean);
                }
            }
            shipperInfoFeeBean.setShipperInfofeeList(shipperInfofees);
        }
        return shipperInfoFeeBean;
    }

    /**
     * 获取确认付款时间
     *
     * @param bean
     */
    private TransportOrdersListBean getConfirmPaymentRemark(TransportOrdersListBean bean) {
        logger.info("getConfirmPaymentRemark  TransportOrdersListBean tsOrderNo is 【{}】payEndTime is 【{}】", bean.getTsOrderNo(), bean.getPayEndTime());
        //15已支付 21 拒绝退款 30 退款中
        if (bean.getCostStatus() == 15 || bean.getCostStatus() == 21) {
            //当前日期
            String today = TimeUtil.formatDateTime(new Date(System.currentTimeMillis()));
            try {
                //实际应到账时间
                Date actualPayDate = bean.getDePaymentDueDate();
                if (actualPayDate == null) {
                    return bean;
                }
                //剩余自动结算天数=实际应到账时间-当前日期
                int tDay = TimeUtil.getDays(today, TimeUtil.formatDateTime(actualPayDate));
                logger.info("getConfirmPaymentRemark method today:【{}】, actualPayDate: 【{}】, tDay:【{}】", today, actualPayDate, tDay);
                if (tDay > 1) {
                    bean.setConfirmPaymentRemark("还剩" + tDay + "天自动结算");
                } else {
                    bean.setConfirmPaymentRemark("即将自动结算");
                }
            } catch (Exception e) {
                e.printStackTrace();
                logger.info("信息费列表获取确认付款时间异常,异常信息{}", e);
            }

        }
        return bean;
    }

    /**
     * 获取确认退款时间
     *
     * @param bean
     */
    private TransportOrdersListBean getConfirmRefundRemark(TransportOrdersListBean bean) {
        logger.info("confirmRefundRemark  TransportOrdersListBean tsOrderNo is 【{}】deRefundDueDate is 【{}】", bean.getTsOrderNo(), bean.getDeRefundDueDate());
        //15已支付 21 拒绝退款 30 退款中
        if (null != bean.getDeRefundDueDate()) {
            //当前日期
            String today = TimeUtil.formatDateTime(new Date(System.currentTimeMillis()));
            try {
                //实际应到账时间
                Date actualPayDate = bean.getDeRefundDueDate();
                if (actualPayDate == null) {
                    return bean;
                }
                //剩余自动结算天数=实际应到账时间-当前日期
                int tDay = TimeUtil.getDays(today, TimeUtil.formatDateTime(actualPayDate));
                logger.info("getConfirmPaymentRemark method today:【{}】, actualPayDate: 【{}】, tDay:【{}】", today, actualPayDate, tDay);
                if (tDay > 1) {
                    bean.setConfirmRefundRemark("还剩" + tDay + "天自动退还");
                } else {
                    bean.setConfirmRefundRemark("即将自动退还");
                }
            } catch (Exception e) {
                e.printStackTrace();
                logger.info("信息费列表获取确认付款时间异常,异常信息{}", e);
            }

        }
        return bean;
    }

    @Override
    public Integer getCountBySrcMsgId(Long srcMsgId) {
        String sql = "SELECT COUNT(*) FROM `tyt_transport_orders` WHERE `ts_id`=:srcMsgId AND `cost_status` >=15 AND ctime >=:ctime";
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        String ctimeBegin = TimeUtil.formatDateTime(TimeUtil.weeHours(new Date(), 0));
        paramsMap.put("srcMsgId", srcMsgId);
        paramsMap.put("ctime", ctimeBegin);
        BigInteger count = this.getBaseDao().queryByMap(sql, paramsMap, null, null);
        if (count == null)
            return 0;
        return count.intValue();
    }

    @Override
    public Long checkUsefulOrderExist(Long srcMsgId) {
        if (srcMsgId == null) {
            return null;
        }
        String sql = "SELECT id FROM `tyt_transport_orders` WHERE `ts_id`= ? AND `cost_status` >=10 AND ctime >= ? limit 1";

        String ctimeBegin = TimeUtil.formatDateTime(TimeUtil.weeHours(new Date(), 0));
        Object[] params = {srcMsgId, ctimeBegin};

        BigInteger dbInt = this.getBaseDao().query(sql, params);

        Long orderId = null;
        if (dbInt != null) {
            orderId = dbInt.longValue();
        }

        return orderId;
    }

    @Override
    public List<TransportOrderCountVo> getTsOrderCountList(Collection<Long> srcMsgIds) {
        List<TransportOrderCountVo> tsOrderList = null;

        if (CollectionUtils.isEmpty(srcMsgIds)) {
            return tsOrderList;
        }

        String srcMsgIdStr = StringUtils.join(srcMsgIds, ",");

        String sql = "select ts_id srcMsgId, count(*) orderCount from tyt_transport_orders where ts_id in (" + srcMsgIdStr + ") AND cost_status >=10 AND ctime >= ? group by ts_id";

        String ctimeBegin = TimeUtil.formatDateTime(TimeUtil.weeHours(new Date(), 0));
        Object[] params = {ctimeBegin};

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
        scalarMap.put("srcMsgId", Hibernate.LONG);
        scalarMap.put("orderCount", Hibernate.INTEGER);

        tsOrderList = this.getBaseDao().search(sql, scalarMap, TransportOrderCountVo.class, params);

        return tsOrderList;
    }

    @Override
    public int getTsOrderCount(Long srcMsgId) {

        int orderCount = 0;

        List<Long> idList = new ArrayList<>();
        idList.add(srcMsgId);

        List<TransportOrderCountVo> countList = this.getTsOrderCountList(idList);

        if (CollectionUtils.isNotEmpty(countList)) {
            TransportOrderCountVo transportOrderCountVo = countList.get(0);

            orderCount = transportOrderCountVo.getOrderCount();
        }

        return orderCount;
    }

    /**
     * @return java.util.List<com.tyt.infofee.bean.TransportOrdersListBean>
     * @Description 获取运单关联的信息费的list集合
     * <AUTHOR>
     * @Date 2018/12/24 17:47
     * @Param [userId, tsOrderNo]
     **/
    public List<TransportOrdersListBean> getInfofeeList(Long userId, List<Long> ids, int costStatus) throws Exception {

        Map<String, Object> params = new HashMap<String, Object>();
        StringBuffer sql = new StringBuffer(
                "select a.id id, a.pay_user_id payUserId, a.pay_user_name userName, a.pay_amount payAmount, IFNULL(a.total_order_amount,0) totalOrderAmount, a.coupon_amount couponAmount, "
                        + "a.cost_status costStatus, a.pay_end_time payEndTime, a.refund_reason refundReason, "
                        + "a.pay_link_phone payLinkPhone, IFNULL(a.refund_amount,0) refundAmount, a.is_deal_car isDealCar,  a.thirdparty_platform_type thirdpartyPlatformType,"
                        + "a.delay_status as delayStatus, a.de_payment_dueDate as dePaymentDueDate, a.car_id carId,"
                        + "a.head_city headCity, a.head_no headNo, a.tail_city tailCity, a.tail_no tailNo,a.ex_cancel_status exCancelStatus,"
                        + "a.pay_service_charge payServiceCharge, a.ts_order_no tsOrderNo,a.car_replace carReplace,a.source_type sourceType,"
                        + "b.weight weight, b.length length, b.wide wide, b.high high, b.begin_loading_time beginLoadingTime, "
                        + "b.loading_time loadingTime, b.begin_unload_time beginUnloadTime, b.unload_time unloadTime, b.price price,b.excellent_goods excellentGoods, "
                        + "b.publish_type publishType,a.refund_flag refundFlag,a.delay_refund_status delayRefundStatus,de_refund_dueDate deRefundDueDate, "
                        + "CASE a.cost_status WHEN 50 THEN IF(trwe.id is null,0,1) ELSE 1 END AS evaluateStatus "
                        + "from tyt_transport_orders a inner join tyt_transport_order_snapshot b on a.id = b.order_id "
                        + "LEFT JOIN tyt_transport_waybill_ex tre ON tre.order_id = a.id and tre.ex_status=2 "
                        + "LEFT JOIN tyt_transport_waybill_ex_evaluate trwe ON trwe.ex_id = tre.id and trwe.user_id=:exUserId "
                        + "where 1=1 and a.goods_show=0");

        params.put("exUserId", userId);
        if (costStatus != 0) {
            //update by sissy 新增 151(支付完成手动发起延迟付款) 211(拒绝退款 变更成延迟付款)
            //新增 300(车方申请退还订金) 301(订金已延迟退款)
            if (costStatus == 15) {
                sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status=15 and a.delay_status in (0,2) ");
            } else if (costStatus == 151) {
                sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status=15 and a.delay_status=1");
            } else if (costStatus == 211) {
                sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status in (15,21) and  a.delay_status=2 ");
            } else if (costStatus == 40 || costStatus == 45) {
                sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status in (40,45) ");
            } else if (costStatus == 20 || costStatus == 21) {
                sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status in (20,21) ");
            } else if (costStatus == 300) {
                sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status =30 and a.refund_flag = 1 and a.delay_refund_status = 0 ");
            } else if (costStatus == 301) {
                sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status in (15,30) and a.refund_flag = 1 and a.delay_refund_status = 1 ");
            } else {
                sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status = :costStatus"); //信息费支付状态>=15
                params.put("costStatus", costStatus);
            }
        } else {
            sql.append(" and a.user_id =:userId  and a.ts_order_no IN(:ids)  and a.cost_status >= 15 "); //信息费支付状态>=15
        }
        params.put("userId", userId);
        params.put("ids", ids);
        sql.append(" order by a.pay_end_time asc ");

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("payUserId", Hibernate.LONG);
        scalarMap.put("userName", Hibernate.STRING);
        scalarMap.put("payAmount", Hibernate.LONG);
        scalarMap.put("totalOrderAmount", Hibernate.LONG);
        scalarMap.put("couponAmount", Hibernate.LONG);
        scalarMap.put("costStatus", Hibernate.INTEGER);
        scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
        scalarMap.put("payLinkPhone", Hibernate.STRING);
        scalarMap.put("refundAmount", Hibernate.LONG);
        scalarMap.put("refundReason", Hibernate.STRING);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        //货站迭代新增参数
        scalarMap.put("carId", Hibernate.LONG);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        scalarMap.put("payServiceCharge", Hibernate.LONG);
        scalarMap.put("isDealCar", Hibernate.INTEGER);
        scalarMap.put("dePaymentDueDate", Hibernate.TIMESTAMP);
        scalarMap.put("delayStatus", Hibernate.INTEGER);
        scalarMap.put("exCancelStatus", Hibernate.INTEGER);
        scalarMap.put("carReplace", Hibernate.INTEGER);
        scalarMap.put("excellentGoods", Hibernate.INTEGER);
        scalarMap.put("sourceType", Hibernate.INTEGER);

        //快照表字段
        scalarMap.put("weight", Hibernate.STRING);
        scalarMap.put("length", Hibernate.STRING);
        scalarMap.put("wide", Hibernate.STRING);
        scalarMap.put("high", Hibernate.STRING);
        scalarMap.put("loadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("unloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("beginLoadingTime", Hibernate.TIMESTAMP);
        scalarMap.put("beginUnloadTime", Hibernate.TIMESTAMP);
        scalarMap.put("price", Hibernate.STRING);
        scalarMap.put("publishType", Hibernate.SHORT);
        scalarMap.put("refundFlag", Hibernate.INTEGER);
        scalarMap.put("delayRefundStatus", Hibernate.INTEGER);
        scalarMap.put("deRefundDueDate", Hibernate.TIMESTAMP);
        scalarMap.put("evaluateStatus", Hibernate.INTEGER);
        scalarMap.put("thirdpartyPlatformType", Hibernate.INTEGER);
        //scalarMap.put("carCreditRankLevel", Hibernate.STRING);
        //运单关联的信息费集合
        List<TransportOrdersListBean> infofeeList = this.getBaseDao().search(sql.toString(), scalarMap, TransportOrdersListBean.class, params);

        if(CollectionUtils.isNotEmpty(infofeeList)){

            Set<Long> carUserIdSet = infofeeList.stream()
                    .map(TransportOrdersListBean::getPayUserId)
                    .collect(Collectors.toSet());

            String serIds = StringUtils.join(carUserIdSet, ",");

            List<UserCarryPointTotalVo> userCarryPointList = null;
            try {
                userCarryPointList = apiUserCarryPointService.getUserCarryPointList(serIds);
            } catch (Exception e) {
                log.error("", e);
            }

            if(CollectionUtils.isNotEmpty(userCarryPointList)){

                Map<Long, UserCarryPointTotalVo> userPointMap = new HashMap<>();

                for(UserCarryPointTotalVo oneUserPoint: userCarryPointList){
                    Long pointUserId = oneUserPoint.getUserId();
                    userPointMap.put(pointUserId, oneUserPoint);
                }

                for(TransportOrdersListBean oneTransportOrders: infofeeList){
                    Long payUserId = oneTransportOrders.getPayUserId();
                    UserCarryPointTotalVo userCarryPointTotalVo = userPointMap.get(payUserId);
                    if(userCarryPointTotalVo != null){
                        oneTransportOrders.setCarCreditRankLevel(userCarryPointTotalVo.getCarLevel() + "");
                    }
                }
            }
        }

        return infofeeList;
    }

    /**
     * @return int
     * @Description 获取车主待支付的信息费数目
     * <AUTHOR>
     * @Date 2019/1/4 18:27
     * @Param [userId, queryActionType, queryID]
     **/
    public int getUnPaidOrdersNum(Long userId) throws Exception {
        List<Object> list = new ArrayList<Object>();
        //限定只查当天的待支付信息费
        String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
        String endTime = TimeUtil.formatDate(TimeUtil.addDay(TimeUtil.formatDate(new Date()), 1)) + " 00:00:00";

        StringBuffer sql = new StringBuffer("select count(*) FROM tyt_transport_orders  where 1=1 ");

        sql.append(" and pay_user_id=? and cost_status = 10 "); //cost_status = 10 待支付
        list.add(userId);
        sql.append(" and rob_status=?");
        list.add("0");
        sql.append(" and (pay_status=?");
        list.add("0");
        sql.append("  or pay_status=?)");
        list.add("1");
        sql.append(" and ctime>?");
        list.add(beginTime);
        sql.append(" and ctime<=?");
        list.add(endTime);
        sql.append(" order by sort_id desc ");

        //未支付的信息费
        int unPaidOrdersNum = 0;
        BigInteger rowCount = this.getBaseDao().query(sql.toString(), list.toArray());
        if (rowCount != null && rowCount.intValue() > 0) {
            unPaidOrdersNum = rowCount.intValue();
        }
        return unPaidOrdersNum;
    }


    @Override
    public List<TytTransportOrders> getOtherPaidOrders(Long userId, String tsOrderNo, Long[] notInPayUserIds) {
        String selectSQL = "select * from tyt_transport_orders where user_id=:userId and ts_order_no=:tsOrderNo and cost_status>=:costStatus and pay_user_id not in(:notInPayUserIds)";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        paramMap.put("tsOrderNo", tsOrderNo);
        paramMap.put("notInPayUserIds", notInPayUserIds);
        paramMap.put("costStatus", InfofeeStatusEnum.已支付.getId());
        return this.getBaseDao().searchByName(selectSQL, paramMap);

    }

    @Override
    public TytTransportOrders getNewTransportOrders(String tsOrderNo, Long payUserId, InfofeeStatusEnum infofeeStatusEnum) {
        String hql = "from TytTransportOrders where payUserId=? and  tsOrderNo=? and costStatus =?";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, payUserId, tsOrderNo, Integer.parseInt(infofeeStatusEnum.getId()));
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    @Override
    public Integer getCountOrdersbyTsOrderNo(String tsOrderNo, Long payUserId) {
        String sql = "SELECT COUNT(*) FROM `tyt_transport_orders` WHERE `ts_order_no`=? AND `pay_user_id`=? AND `cost_status` NOT IN(6,10)";
        BigInteger rowCount = this.getBaseDao().query(sql, new Object[]{tsOrderNo, payUserId});
        if (rowCount != null && rowCount.intValue() > 0) {
            return rowCount.intValue();
        }
        return 0;
    }

    @Override
    public List<RecentCallListBean> getInfofeeCell(Long tsId) {
        String sql = "SELECT `id` orderId,`pay_user_id` carryUserId,`pay_user_name` carryName,`pay_cell_phone` carryCellPhone," +
                "`head_city` headCity,`head_no` headNo,`tail_city` tailCity,`tail_no` tailNo,`car_id` carId,pay_amount payAmount,pay_end_time callTime FROM `tyt_transport_orders` WHERE `ts_id`=:tsId and cost_status in(15,20,21,25,30,40,45,50)";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("tsId", tsId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("orderId", Hibernate.LONG);
        scalarMap.put("carryUserId", Hibernate.LONG);
        scalarMap.put("carryName", Hibernate.STRING);
        scalarMap.put("carryCellPhone", Hibernate.STRING);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        scalarMap.put("carId", Hibernate.LONG);
        scalarMap.put("payAmount", Hibernate.LONG);
        scalarMap.put("callTime", Hibernate.TIMESTAMP);

        //运单关联的信息费集合
        return this.getBaseDao().search(sql, scalarMap, RecentCallListBean.class, params);
    }

    @Override
    public ResultMsgBean getCarPosition(Long carId) {
        Car byId = carService.getById(carId);
        if (byId == null) {
            return new ResultMsgBean(500, "车辆id参数有误");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("headCity", byId.getHeadCity());
        map.put("carId", byId.getId());
        map.put("auth", byId.getAuth());
        map.put("headNo", byId.getHeadNo());
        map.put("tailCity", byId.getTailCity());
        map.put("tailNo", byId.getTailNo());
        map.put("newTime", System.currentTimeMillis());
        map.put("carIsCanGetLocation", false);
        if ("1".equals(byId.getAuth())) {
            try {
                //6400新增两个有关车辆定位的返回字段（离线状态、离线时长）
                Object currentLocation = carCurrentLocationService.getCurrentLocation(byId.getHeadCity() + byId.getHeadNo());
                map.put("location", currentLocation);
                map.put("carIsCanGetLocation", true);
                return new ResultMsgBean(200, "查询成功", map);
            } catch (Exception e) {
                log.error("getCarPosition 异常 carId：{}, 原因：", carId, e);
                return new ResultMsgBean(500, "服务器异常");
            }
        } else {
            return new ResultMsgBean(ReturnCodeConstant.CAR_NOT_CERTIFIED, "车辆未认证", map);
        }
    }

    @Override
    public int unpaidCancel(Long userId, Long orderId) {
        return super.getBaseDao().executeUpdateSql("update tyt_transport_orders set cost_status = 6, pay_status = 3, rob_status = 12,order_new_status = 35 where id = ? ", new Object[]{orderId});
    }

    @Override
    public List<InfoFeeMyPublishBubbleResultBean> getTradingAndFreezeBubble(Long userId) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("userId", userId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("number", Hibernate.INTEGER);
        scalarMap.put("costStatus", Hibernate.INTEGER);//(20,21,25,30)
        List<TradingAndFreezeBubble> tradingAndFreezeBubbles = super.getBaseDao().search("select count(*) number, CASE cost_status WHEN 20 THEN 20  ELSE 15 END as costStatus " +
                "from tyt_transport_orders where pay_user_id =:userId and cost_status in (15,20,21,25,30) group by CASE cost_status WHEN 20 THEN 20 ELSE 15 END", scalarMap, TradingAndFreezeBubble.class, params);
        if (CollectionUtils.isEmpty(tradingAndFreezeBubbles)) {
            ArrayList<InfoFeeMyPublishBubbleResultBean> resultBeans = new ArrayList<>();
            resultBeans.add(new InfoFeeMyPublishBubbleResultBean("1", "9", "1", 0));
            resultBeans.add(new InfoFeeMyPublishBubbleResultBean("1", "10", "1", 0));
            return resultBeans;
        }

        if (tradingAndFreezeBubbles.size() < 2) {
            if (!Objects.equals(tradingAndFreezeBubbles.get(0).getCostStatus(), OrderStatusType.PAY.getStatus())) {
                tradingAndFreezeBubbles.add(new TradingAndFreezeBubble(0, 15));
            }

            if (!Objects.equals(tradingAndFreezeBubbles.get(0).getCostStatus(), OrderStatusType.FREEZE.getStatus())) {
                tradingAndFreezeBubbles.add(new TradingAndFreezeBubble(0, 20));
            }
        }

        return tradingAndFreezeBubbles.stream()
                .filter(b -> Objects.equals(b.getCostStatus(), OrderStatusType.PAY.getStatus())
                        || Objects.equals(b.getCostStatus(), OrderStatusType.FREEZE.getStatus()))
                .map(b -> {
                    InfoFeeMyPublishBubbleResultBean bubbleResultBean = new InfoFeeMyPublishBubbleResultBean();
                    bubbleResultBean.setType1("1");
                    bubbleResultBean.setIfFunction("1");
                    bubbleResultBean.setNumber(b.getNumber());
                    bubbleResultBean.setType2(Objects.equals(b.getCostStatus(), OrderStatusType.PAY.getStatus()) ? "9" : "10");
                    return bubbleResultBean;
                }).collect(Collectors.toList());
    }

    @Override
    public Long getUserFeedbackTodoCount(Long userId, int userType) {
        BigInteger count;
        StringBuilder sql = new StringBuilder("select count(*) from tyt_transport_orders a FORCE INDEX (Index_user_id_status_time)" +
                " left join tyt_transport_order_snapshot b on a.ts_id = b.src_msg_id and a.id = b.order_id" +
                " LEFT JOIN tyt_transport_waybill_ex tre ON tre.order_id = a.id and tre.ex_status=2 AND tre.order_type = 0 LEFT" +
                " JOIN tyt_transport_waybill_ex_evaluate trwe ON trwe.ex_id = tre.id and trwe.user_id= :userId" +
                " where car_show=0 and a.cost_status >= 35 AND a.mtime > :beginDateTime");
        Date beginDateTime = DateUtils.addDays(new Date(), -30);
        if (userType == 1) {//车方
            sql.append(" AND a.pay_user_id = :userId");
            sql.append(" and a.id not in (select order_id from feedback_user where create_time > :beginDateTime and post_user_type = 1 and post_user_id= :userId)");
        } else {
            sql.append(" AND a.user_id = :userId");
            sql.append(" and a.id not in (select order_id from feedback_user where create_time > :beginDateTime and post_user_type = 2 and post_user_id= :userId)");
        }
        sql.append(" AND (CASE a.cost_status WHEN 50 THEN IF(trwe.id is null,0,1) ELSE 1 END) = 1");
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("beginDateTime", beginDateTime);
        paramsMap.put("userId", userId);
        count = this.getBaseDao().queryByMap(sql.toString(), paramsMap, null, null);
        if (count == null || count.longValue() <= 0L) {
            return 0L;
        }
        return count.longValue();
    }

    @Override
    public ResultMsgBean updateDealCar(Long id, Integer status, Long tsId) {
        BackendTransportBean backendTransportBean = backendTransportMapper.selectBackendStatusByMsgId(tsId);
        if (backendTransportBean == null) {
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "业务参数有误");
        }
        //判断货主是否已经取消订单
        if (backendTransportBean.getStatus() != null && !ObjectUtil.equal(backendTransportBean.getStatus() + "", "3")) {
            return new ResultMsgBean(ReturnCodeConstant.EDIT_TIMEOUT, "货主已取消货源，当前订单不可派车");
        }
        //看是否是第一次设置成交车辆
        if (backendTransportBean.getOrderStatus() != null && ObjectUtil.equal(backendTransportBean.getOrderStatus() + "", "30")) {
            this.getBaseDao().executeUpdateSql("update tyt_transport_backend set order_status=?,carriage_time=?,mtime=NOW() where src_msg_id = ? ", new Object[]{31, new Date(), tsId});
        }
        this.getBaseDao().executeUpdateSql("update tyt_transport_orders set is_deal_car = ?,deal_car_time=NOW(),mtime=mtime where id = ? ", new Object[]{status, id});
        //保存派车记录
        TytTransportOrders orders = this.getById(id);
        BackendDealCarBean carBean = new BackendDealCarBean();
        carBean.setCarId(orders.getCarId());
        carBean.setHeadCity(orders.getHeadCity());
        carBean.setHeadNo(orders.getHeadNo());
        carBean.setTailCity(orders.getTailCity());
        carBean.setTailNo(orders.getTailNo());
        carBean.setStatus(status);
        carBean.setTel(orders.getPayCellPhone());
        carBean.setTsId(tsId);
        carBean.setUserName(orders.getPayUserName());
        carBean.setUserId(orders.getPayUserId());
        backendTransportMapper.insertBackendDealCar(carBean);
        //设为成交车 取消成交车 发送mq 推送微信小程序消息，短信
        tytMqMessageService.sendTransportBackendMessageMq(backendTransportBean.getMsgId(), String.valueOf(status));
        return new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
    }

    @Override
    public ResultMsgBean getBackendTransportPhones(Long tsId) {
        HashMap<String, String> phones = backendTransportMapper.selectBackendPhoneByMsgId(tsId);
        if (phones == null) {
            //todo 先处理不返回错误提示，后续优化
            return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", new ArrayList());
            //return  new ResultMsgBean(ReturnCodeConstant.ERROR, "业务参数有误");
        }
        List<String> phoneList = new ArrayList();
        if (StringUtils.isNotBlank(phones.get("tel"))) {
            phoneList.add(phones.get("tel"));
        }
        if (StringUtils.isNotBlank(phones.get("tel3"))) {
            phoneList.add(phones.get("tel3"));
        }
        if (StringUtils.isNotBlank(phones.get("tel4"))) {
            phoneList.add(phones.get("tel4"));
        }
        return new ResultMsgBean(ReturnCodeConstant.OK, "查询成功", phoneList);
    }

    @Override
    public int getDateRangFinishAndPayAmountOrderTotal(Long userId, Date payDate, Date endDate, Long payAmount) {
        String sql = "select count(*) from tyt_transport_orders where cost_status in (40, 45) " +
                "and pay_user_id = ? and pay_amount >= ? and pay_end_time >= ? and mtime <= ?";
        final BigInteger count = this.getBaseDao().query(sql, new Object[]{userId, payAmount, payDate, endDate});
        return count.intValue();
    }

    @Override
    public long getUnfinishedOrders(Long userId) {
        String selectSQL = "SELECT count(*) from tyt_transport_orders where (user_id=? or pay_user_id=?) and cost_status in (15, 20, 21, 25,30)";
        BigInteger c = this.getBaseDao().query(selectSQL, new Object[]{userId, userId});
        if (c == null)
            return 0;
        return c.longValue();
    }

    @Override
    public BigDecimal getTotalFrozenAmountByUserId(String userId) {
        String selectSQL = "SELECT sum(pay_amount)/100+sum(coupon_amount)/100 as totalPayAmount from tyt_transport_orders where user_id=? and cost_status in (15, 20, 21, 25,30)";
        BigDecimal totalPayAmount = this.getBaseDao().query(selectSQL, new Object[]{userId});
        return totalPayAmount == null ? new BigDecimal("0.00") : totalPayAmount;
    }

    @Override
    public TytTransportOrders userIsPay(Long srcMsgId, Long userId) {
        String hql = "from TytTransportOrders where payUserId = ? and  tsId=? and costStatus >= 15";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, userId, srcMsgId);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public TytTransportOrders getByTsId(Long tsId) {
        String hql = "from TytTransportOrders where  tsId=? and costStatus >= 15";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, tsId);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public int updateCarInfo(TytTransportOrders tytTransportOrders) {
        int i = this.getBaseDao().executeUpdateSql(
                "update tyt_transport_orders set head_city = ?,head_no = ?,tail_city = ?, tail_no = ?, car_id = ?, car_replace = ?, mtime= now(), car_replace = ?, is_deal_car = ? where id = ? ",
                new Object[]{tytTransportOrders.getHeadCity(), tytTransportOrders.getHeadNo(), tytTransportOrders.getTailCity(), tytTransportOrders.getTailNo(), tytTransportOrders.getCarId(), tytTransportOrders.getCarReplace(), tytTransportOrders.getCarReplace(), tytTransportOrders.getIsDealCar(), tytTransportOrders.getId()});
        return i;
    }

    @Override
    public TytTransportOrders getByTsIdByTime(Long tsId) {
        String hql = "from TytTransportOrders where  tsId=? and costStatus >= 15 order by pay_end_time";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, tsId);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public Long getCompletedOrderNum(Long userId, Date startTime, Date endTime) {
        String selectSQL = "SELECT count(id) FROM tyt_transport_orders WHERE pay_user_id =? AND (cost_status IN ( 40, 45 ) " +
                " OR refund_reason IN ( '货已送达,退还订金', '已装货,退还订金', '已装货,协商退款', '货已送达,协商退款' ) " +
                " OR ( cost_status = 50 AND loading_status = 1 ) ) AND create_time >=? AND create_time <=? ";
        BigInteger c = this.getBaseDao().query(selectSQL, new Object[]{userId, startTime, endTime});
        if (c == null) {
            return null;
        }
        return c.longValue();
    }

    @Override
    public void updateOrderForInfoFeeGiveBack(TytTransportOrders orders, Integer refundOriginator, String refundReason) throws Exception {
        if (refundOriginator == 1) {
            orders.setCostStatus(OrderStatusType.REFUNDING.getStatus());
        } else {
            orders.setCostStatus(OrderStatusType.REFUNDED.getStatus());
        }
        orders.setPayServiceCharge(BigDecimal.ZERO);
        orders.setRefundAmount(orders.getPayAmount());
        orders.setRefundTime(new Date());
        orders.setRefundStatus(1); //退款状态更新为 1.退款中
        if (StringUtils.isNotBlank(refundReason)) {
            orders.setRefundReason(refundReason);
        }
        //三方平台类型 0:特运通货源订单  1:特运通货源，满帮订单 2:满帮货源，特运通订单
        Integer thirdpartyPlatformType = orders.getThirdpartyPlatformType();
        //三方平台单号
        String thirdpartyPlatformOrderNo = orders.getThirdpartyPlatformOrderNo();
        //新增当传递过来的refundReason为null 并且orders表中的refundReason 为null 需要赋予默认值
        if (StringUtils.isEmpty(refundReason) && StringUtils.isEmpty(orders.getRefundReason()) && thirdpartyPlatformType != 1) {
            orders.setRefundReason("已装货,退还订金");
        }
        //当是满帮货源通知退款 且退款原因不为空时 根据满帮退款原因判断是否需要退还技术服务费
        Integer tecRefundType=null;
        if(Objects.nonNull(thirdpartyPlatformType)&&thirdpartyPlatformType ==2&&refundOriginator==2&&Strings.isNotEmpty(orders.getTechnicalServiceNo())){
            if(checkWhetherFullRefundTec(refundReason)){
                updateTechnicalOrderInfo(orders.getTecServiceFee()/100,orders.getTechnicalServiceNo());
                tecRefundType=TECHNICAL_FEE_REFUND_TYPE;
            }
        }
        //获取退还原因 当退还原因满足以下条件时 设置装货时间 装货状态
        String refundReasonNew = orders.getRefundReason();
        if ("已装货,协商退款".equals(refundReasonNew) || "已装货,退还订金".equals(refundReasonNew) || "货已送达,协商退款".equals(refundReasonNew) || "货已送达,退还订金".equals(refundReasonNew)) {
            orders.setLoadTime(new Date());
            orders.setLoadingStatus(1);
            if(refundOriginator == 1){
                orders.setOrderNewStatus(OrderStatusEnum.WAIT_LOADED.getCode());
            }else{
                orders.setOrderNewStatus(OrderStatusEnum.WAIT_UNLOADED_OR_RECEIVED.getCode());            }
        }else{
            orders.setOrderNewStatus(OrderStatusEnum.CANCEL.getCode());
        }
        orders.setMtime(new Date());
        this.getBaseDao().update(orders);

        if (thirdpartyPlatformType != null && thirdpartyPlatformType == 1) {
            //发送满帮平台退款的MQ消息
            tytMqMessageService.saveAndSendMbInfofeeMq(orders);
        } else {
            //如果平台类型=2(满帮货源,特运通订单) 并且 退款发起方=1(车方主动申请), 则发送特运通司机申请退订金MQ消息
            if (thirdpartyPlatformType != null && thirdpartyPlatformType == 2 && refundOriginator == 1) {
                //发送满帮平台退款的MQ消息
                tytMqMessageService.saveAndSendDriverRefundDepositMq(thirdpartyPlatformOrderNo, 6007, "其它原因");
            }
            //当tecRefundType不等于空 说明是满帮货源 且发起方是货主 且根据退款原因判断需要全额退还技术服务费
            if (Objects.nonNull(tecRefundType)&&TECHNICAL_FEE_REFUND_TYPE==tecRefundType) {
                //此处用131 表明需要退还技术服务费特殊处理
                tytMqMessageService.saveAndSendInfofeeMq(orders, 131, refundOriginator);
            }else{
                //记日志，发短信，发push消息
                tytMqMessageService.saveAndSendInfofeeMq(orders, 13, refundOriginator);
            }

        }
    }

    @Override
    public Long getSrcMsgIdByCargoId(Long cargoId) {
        String selectSQL = "SELECT src_msg_id from tyt_transport_sync_ymm where " +
                " cargo_id=? and sync_status=0 order by id desc limit 1 ";
        BigInteger srcMsgId = this.getBaseDao().query(selectSQL, new Object[]{cargoId});
        return srcMsgId == null ? null : srcMsgId.longValue();
    }

    @Override
    public int getGoodsPaySuccessCount(Long srcMsgId) {
        String selectSQL = "SELECT count(*) from tyt_transport_orders where ts_id=? and pay_status=2 ";
        BigInteger c = this.getBaseDao().query(selectSQL, new Object[]{srcMsgId});
        if (c != null) {
            return c.intValue();
        }
        return 0;
    }

    @Override
    public TytTransportOrders getByThirdPartyPlatformOrderNo(String thirdPartyPlatformOrderNo) {
        String hql = "from TytTransportOrders where thirdparty_platform_order_no=? and costStatus >= 15";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, thirdPartyPlatformOrderNo);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public int updateOrderForConfirmSuccess(String thirdPartyPlatformOrderNo) throws Exception {
        String updateSQL = "update tyt_transport_orders set rob_status=:robStatus,cost_status=:costStatus ,loading_status=1,load_time=now(),order_new_status=:orderNewStatus " +
                "where thirdparty_platform_order_no=:thirdPartyPlatformOrderNo";
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款
        // 9系统撤销货源退款 10车主取销装货
        paramsMap.put("robStatus", "5");
        paramsMap.put("costStatus", OrderStatusType.CONFIRM_PAY.getStatus());
        paramsMap.put("orderNewStatus", OrderStatusEnum.WAIT_UNLOADED_OR_RECEIVED.getCode());
        paramsMap.put("thirdPartyPlatformOrderNo", thirdPartyPlatformOrderNo);
        return this.executeUpdateSql(updateSQL, paramsMap);
    }

    /**
     * 更新技术服务费表退款信息
     * <AUTHOR>
     * @param refundAmount
     * @param technicalServiceNo
     * @return int
     */
    private int updateTechnicalOrderInfo(Long refundAmount, String technicalServiceNo){
        String updateSQL = "update tyt_transport_technical_order set refund_status=1,refund_time=now(),mtime=now(),refund_amount=:refundAmount  where technical_service_no=:technicalServiceNo";
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("refundAmount", refundAmount);
        paramsMap.put("technicalServiceNo", technicalServiceNo);
        return this.executeUpdateSql(updateSQL, paramsMap);
    }

    @Override
    public void addOldOrderLog(TytTransportOrders orders) {
        String sql = "INSERT INTO `tyt`.`tyt_old_order` (`order_id`, `thirdparty_platform_order_no`, `thirdparty_platform_type`, `cell_phone`, `paymethod`, `STATUS`, `ctime`, `mtime`, `total_fee`,  `user_id`, `pay_channel`, `op_type`, `good_pay_id`, `order_num`, `order_type`,   `user_type`, `user_acct_type`, `goods_name`, `pay_order_no`,`thirdparty_order_serial_num`) " +
                " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        Object[] parameters = new Object[]{orders.getPayNo(), orders.getThirdpartyPlatformOrderNo(), orders.getThirdpartyPlatformType(), orders.getPayCellPhone(), "满帮支付", Order.ORDER_STATUS_PAY_SUCCESS,
                TimeUtil.getTimeStamp(), TimeUtil.getTimeStamp(), orders.getPayAmount(),
                orders.getPayUserId(), "5", 1, orders.getId(), orders.getTsOrderNo(), 2,
                "MANBANG", "MANBANG", "订金", orders.getPayOrderNo(), orders.getThirdpartyPlatformOrderNo()};
        this.getBaseDao().executeUpdateSql(sql, parameters);
    }


    @Override
    public void addZeroAssignOrderLog(TytTransportOrders orders) {
        String sql = "INSERT INTO `tyt`.`tyt_old_order` (`order_id`, `thirdparty_platform_order_no`, `thirdparty_platform_type`, `cell_phone`, `paymethod`, `STATUS`, `ctime`, `mtime`, `total_fee`,  `user_id`, `pay_channel`, `op_type`, `good_pay_id`, `order_num`, `order_type`,   `user_type`, `user_acct_type`, `goods_name`, `pay_order_no`,`thirdparty_order_serial_num`) " +
                " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        Object[] parameters = new Object[]{orders.getPayNo(), orders.getThirdpartyPlatformOrderNo(), orders.getThirdpartyPlatformType(), orders.getPayCellPhone(), "满帮支付", Order.ORDER_STATUS_PAY_SUCCESS,
                TimeUtil.getTimeStamp(), TimeUtil.getTimeStamp(), orders.getPayAmount(),
                orders.getPayUserId(), "5", 1, orders.getId(), orders.getTsOrderNo(), 2,
                "USER", "USEROWN", "订金", orders.getPayOrderNo(), orders.getThirdpartyPlatformOrderNo()};
        this.getBaseDao().executeUpdateSql(sql, parameters);
    }


    @Override
    public int updateOrderForRefundSuccess(String thirdPartyPlatformOrderNo, Long refundAmount, String refundReason, Date refundArrivalTime) throws Exception {
        StringBuffer updateSQL = new StringBuffer("update tyt_transport_orders set " +
                "rob_status=:robStatus," +
                "cost_status=:costStatus," +
                "refund_status=:refundStatus," +
                "refund_reason=:refundReason," +
                "refund_arrival_time=:refundArrivalTime," +
                "refund_amount=:refundAmount,");
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        //当退还原因 满足以下条件 设置load_time=now()
        if ("已装货,协商退款".equals(refundReason) || "已装货,退还订金".equals(refundReason) || "货已送达,协商退款".equals(refundReason) || "货已送达,退还订金".equals(refundReason)) {
            updateSQL.append("loading_status=1,load_time=now(),refund_time=:refundTime,order_new_status=:orderNewStatus where thirdparty_platform_order_no=:thirdPartyPlatformOrderNo");
            paramsMap.put("orderNewStatus", OrderStatusEnum.WAIT_LOADED.getCode());
        } else {
            updateSQL.append(" refund_time=:refundTime,order_new_status=:orderNewStatus where thirdparty_platform_order_no=:thirdPartyPlatformOrderNo");
            paramsMap.put("orderNewStatus", OrderStatusEnum.CANCEL.getCode());
        }

        // 接单状态0待接单 1接单成功 2货主拒绝 3系统拒绝 4同意装货 5车主装货完成 6系统装货完成 7异常上报 8货主撤销货源退款
        // 9系统撤销货源退款 10车主取销装货
        paramsMap.put("robStatus", "2");
        paramsMap.put("costStatus", OrderStatusType.REFUNDED.getStatus());
        paramsMap.put("refundStatus", 2);
        paramsMap.put("refundReason", refundReason);
        paramsMap.put("refundArrivalTime", refundArrivalTime);
        paramsMap.put("refundAmount", refundAmount);
        paramsMap.put("refundTime", refundArrivalTime);
        paramsMap.put("thirdPartyPlatformOrderNo", thirdPartyPlatformOrderNo);
        return this.executeUpdateSql(updateSQL.toString(), paramsMap);
    }

    @Override
    public void addRefundForRefundSuccess(TytTransportOrders tranSportOrders, Long refundAmount, Date refundArrivalTime, Integer tradeId) throws Exception {
        //查询交易记录表id
        //插入tyt_refund表一条交易记录
        String refundSql = "INSERT INTO `tyt`.`tyt_refund` (`user_id`, `refund_time`, `refund_amount`, `good_pay_id`, `trade_id`, `refund_complete_time`, `out_trade_num`, `refund_status`, `refund_type`, `order_id`, `failure_reason`,`refund_no` ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
        Object[] refundParams = new Object[]{tranSportOrders.getPayUserId().intValue(), refundArrivalTime, refundAmount, tranSportOrders.getId(), tradeId, refundArrivalTime, "", 2, 4, tranSportOrders.getPayNo(), "", ""};
        this.getBaseDao().executeUpdateSql(refundSql, refundParams);
    }

    @Override
    public TytTransportOrders getTransportByThirdpartyNo(String thirdpartyNo) {
        String hql = "from TytTransportOrders where thirdpartyPlatformOrderNo=? ";
        List<TytTransportOrders> list = this.getBaseDao().find(hql, thirdpartyNo);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public int checkDepositReadOnly(Long srcMsgId) {
        //获取运输信息表基本信息
        TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain == null) {
            return 0;
        }

        BigDecimal infoFee = transportMain.getInfoFee();
        if (infoFee == null) {
            //无价允许修改
            return 0;
        }

        //发货订金必填开关（0全部不必填；1根据用户判断；2全部必填）
        int publishRequired = 0;
        PublicResource publishDeposit = publicResourceService.getByKey(AbtestConstant.deposit_required);

        if (publishDeposit != null) {
            String value = publishDeposit.getValue();
            publishRequired = Integer.parseInt(value);
        }
        if (publishRequired == 0) {
            return 0;
        }

        //发货是否必填
        int required = 0;
        if (publishRequired == 2) {
            required = 1;
        } else if (publishRequired == 1) {
            //需要判断abtest
            Long userId = transportMain.getUserId();

            Integer userType = abtestService.getUserType(AbtestConstant.deposit_required, userId);

            if (userType != null && userType.intValue() == 1) {
                required = 1;
            }
        }

        int readOnly = 0;
        if (required == 1) {
            //判断开关
            readOnly = tytConfigService.getIntValue(Constant.pay_deposit_read_only, 0);
        }

        return readOnly;
    }

    @Override
    public List<TransportOrders> selectOrdersByActivityIdAndUserId(MarketingActivity activity, Long userId) {

        StringBuffer sql = new StringBuffer("SELECT tor.id orderNumber,tor.ts_order_no tsOrderNo,tor.start_point as startPoint,tor.dest_point as destPoint,tor.task_content as taskContent," +
                " tor.pay_user_name as payUserName,tor.head_city as headCity,tor.head_no as headNo,tor.tail_city as tailCity," +
                " tor.tail_no as tailNo FROM tyt_transport_orders tor" +
                " JOIN marketing_activity_user mau ON tor.user_id = mau.user_id AND mau.is_delete=1 WHERE " +
                " tor.user_id = ? and mau.activity_id = ? AND (tor.cost_status IN ( 15,21 ) OR tor.refund_reason IN ( '未装货,协商退款', '货源取消,协商退款' )" +
                " OR ( tor.cost_status = 25 AND tor.loading_status = 0 ) )  AND tor.pay_end_time >? AND tor.pay_end_time <=? ");
        List<Object> params = new ArrayList<Object>();
        params.add(userId);
        params.add(activity.getId());
        params.add(activity.getStartTime());
        params.add(activity.getEndTime());

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("payUserName", Hibernate.STRING);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        List<TransportOrders> activityTransportOrdersDtos = this.getBaseDao().search(sql.toString(), scalarMap, TransportOrders.class, params.toArray());
        return activityTransportOrdersDtos;
    }

    @Override
    public Integer getCountNum(Long activityId, Long userId) {
        String sql = "SELECT count(DISTINCT s.id ) FROM marketing_activity a left join marketing_activity_user u on a.id = u.activity_id" +
                " LEFT JOIN tyt_transport_orders s ON u.user_id = s.user_id" +
                " where a.id = ? and u.user_id = ? and s.pay_end_time >= a.start_time and s.pay_end_time <= a.end_time";

        BigInteger count = this.getBaseDao().query(sql, new Object[]{activityId, userId});
        if (null != count) {
            return count.intValue() > 0 ? count.intValue() : 0;
        }
        return 0;

    }

    /**
     * @param userId
     * @param queryActionType
     * @param queryID
     * @param clientVersion
     * @param queryType
     * @param clientSign
     * @return java.util.List<com.tyt.model.TytTransportOrders>
     * @description 获取投诉订单列表
     * <AUTHOR>
     * @date 2022/12/14 13:58
     */
    @Override
    public List<TransportOrdersListBean> getComplaintOrdersList(Long userId, int queryActionType, long queryID, String clientVersion, Integer queryType, String clientSign) throws Exception {
        List<Object> list = new ArrayList<Object>();
        int pageSize = AppConfig.getIntProperty("info.fee.query.page.size");
        //订单开始时间
        Date startTime = TimeUtil.addDay(new Date(), -15);
        StringBuffer sql = new StringBuffer("select " +
                " a.id id,a.sort_id sortId, a.ts_order_no tsOrderNo, " +
                " a.start_point startPoint,a.dest_point destPoint,a.task_content taskContent, " +
                " a.pay_end_time payEndTime,a.user_id userId,a.pub_user_name pubUserName, " +
                " a.total_order_amount totalOrderAmount,a.coupon_amount couponAmount," +
                " a.pay_amount payAmount,a.cost_status costStatus," +
                " a.ts_id tsId, a.head_city headCity, a.head_no headNo, " +
                " a.tail_city tailCity, a.tail_no tailNo,a.create_time  createTime " +
                "FROM " +
                " tyt_transport_orders a " +
                "WHERE " +
                " 1 = 1  " +
                "and is_assign_order = 0 " +
                "and cost_status in (35,40,45,50) " +
                "and thirdparty_platform_type in (0,2) " +
                "and is_complaint = 0 " +
                "and create_time >= ? ");
        list.add(startTime);
        if (Integer.parseInt(clientSign) == Constant.ClientSignEnum.ANDROID_CAR.code
                || Integer.parseInt(clientSign) == Constant.ClientSignEnum.IOS_CAR.code) {
            sql.append(" and pay_user_id = ? ");
            list.add(userId);
        } else if (Integer.parseInt(clientSign) == Constant.ClientSignEnum.ANDROID_GOODS.code
                || Integer.parseInt(clientSign) == Constant.ClientSignEnum.IOS_GOODS.code
                || Integer.parseInt(clientSign) == Constant.ClientSignEnum.WEB_GOODS.code) {
            sql.append(" and user_id = ? ");
            list.add(userId);
        }

        if (queryActionType == 2) {
            sql.append(" and sort_id<?");
            list.add(queryID);
        }
        sql.append(" order by sort_id desc ");

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("sortId", Hibernate.LONG);
        scalarMap.put("tsOrderNo", Hibernate.STRING);
        scalarMap.put("startPoint", Hibernate.STRING);
        scalarMap.put("destPoint", Hibernate.STRING);
        scalarMap.put("taskContent", Hibernate.STRING);
        scalarMap.put("payEndTime", Hibernate.TIMESTAMP);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("pubUserName", Hibernate.STRING);
        scalarMap.put("totalOrderAmount", Hibernate.LONG);
        scalarMap.put("couponAmount", Hibernate.LONG);
        scalarMap.put("payAmount", Hibernate.LONG);
        scalarMap.put("costStatus", Hibernate.INTEGER);
        scalarMap.put("tsId", Hibernate.LONG);
        scalarMap.put("headCity", Hibernate.STRING);
        scalarMap.put("headNo", Hibernate.STRING);
        scalarMap.put("tailCity", Hibernate.STRING);
        scalarMap.put("tailNo", Hibernate.STRING);
        scalarMap.put("createTime", Hibernate.TIMESTAMP);
        List<TransportOrdersListBean> complaintOrdersList = this.getBaseDao().search(sql.toString(), scalarMap, TransportOrdersListBean.class, list.toArray(), 1, pageSize);
        return complaintOrdersList;
    }

    @Override
    public List<TytTransportOrders> getOrdersByCarAtivityOrder(Long activityId, Long userId) {
        String selectSQL = "select tor.* from tyt_stimulate_activity tsa left join tyt_transport_orders tor on tsa.order_number = tor.id where activity_status = 0 and marketing_activity_id = :activityId and tsa.user_id = :userId ";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        paramMap.put("activityId", activityId);
        return this.getBaseDao().searchByName(selectSQL, paramMap);

    }

    @Override
    public List<TytTransportOrders> getOrdersByCarAtivityOrderRisk(Long activityId, Long userId, MarketingActivity activity) {
        Date startTime = activity.getStartTime();
        Date endTime = activity.getEndTime();
        //活动单查询不延续3天
        String selectSQL = "select tor.* from tyt_transport_orders_risk tr left join tyt_transport_orders tor on tr.order_number = tor.id where tr.activity_status in (0,3)" +
                " and tr.car_user_id = :userId and tr.pay_end_time >= :startTime and tr.create_time <= :endTime order by tor.mtime DESC ";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        paramMap.put("startTime", startTime);
        paramMap.put("endTime", endTime);
        return this.getBaseDao().searchByName(selectSQL, paramMap);

    }

    @Override
    public List<MybatisTytTransportOrders> getUnPostFeedbackOrders(Long userId, Long payUserId) {
        if (userId == null && payUserId == null) {
            return new ArrayList<>();
        }
        Date oneYearAgo = DateUtils.addDays(new Date(), -30);
        return transportOrdersMapper.getUnPostFeedbackOrders(userId, payUserId, oneYearAgo);
    }

    @Override
    public List<MybatisTytTransportOrders> getOrdersByIdList(List<Long> idList) {
        return transportOrdersMapper.getOrdersByIdList(idList);
    }

    @Override
    public void saveOtherUserChangeWaitToDisappearNew(Long userId, String tsOrderNo) {
        log.info("saveOtherUserChangeWaitToDisappearNew userId: {} tsOrderNo: {}", userId, tsOrderNo);
        List<GoodsDetailOrderBean> noPayList = this.getByOtherUser(userId, tsOrderNo, "0", 10);
        if (noPayList == null || noPayList.isEmpty()) {
            return;
        }
        log.info("saveOtherUserChangeWaitToDisappearNew userId: {} size: {}", userId, noPayList.size());
        for (GoodsDetailOrderBean noPayOrder : noPayList) {
            try {
                //接单状态：11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
                this.saveChangeRobStatusByIdNew(noPayOrder.getId(), "11", 6);
                // 车主的待支付气泡减一
                tytBubbleService.updateBubbleNumber(noPayOrder.getCarOwnerUserId(), "1", "1", -1);
            } catch (Exception e) {
                log.error("saveOtherUserChangeWaitToDisappearNew error tsOrderNo: {}", tsOrderNo, e);
            }
        }
    }

    /**
     * 查询非当前userId的运单数据
     * @param userId
     * @param tsOrderNo
     * @param robStatus
     * @param costStatus
     * @return
     */
    private List<GoodsDetailOrderBean> getByOtherUser(Long userId, String tsOrderNo, String robStatus, Integer costStatus) {
        String selectSQL = "select id,ts_order_no goodsOrderNo,pay_user_id carOwnerUserId,pay_cell_phone carOwnerRegisterPhone" + ",pay_link_phone carOwnerTelephone,pay_amount payAgencyMoney,pay_end_time payEndTime,pay_type payChannel" + " from tyt_transport_orders where user_id!=? and ts_order_no=? and rob_status=? and cost_status=? order by sort_id desc";
        Map<String, Type> paramsMap = new HashMap<String, Type>();
        paramsMap.put("id", Hibernate.LONG);
        paramsMap.put("goodsOrderNo", Hibernate.STRING);
        paramsMap.put("carOwnerUserId", Hibernate.LONG);
        paramsMap.put("carOwnerRegisterPhone", Hibernate.STRING);
        paramsMap.put("carOwnerTelephone", Hibernate.STRING);
        paramsMap.put("payAgencyMoney", Hibernate.LONG);
        paramsMap.put("payEndTime", Hibernate.TIMESTAMP);
        paramsMap.put("payChannel", Hibernate.STRING);
        return this.getBaseDao().search(selectSQL, paramsMap, GoodsDetailOrderBean.class, new Object[]{userId, tsOrderNo, robStatus, costStatus});
    }

    @Override
    public void updateExInfoFeeOrderForGiveBack(TytTransportOrders orders,ExInfoFeeGiveBackBean giveBackBean) throws Exception {
        //将传递过来的退款信息复制给order
        orders.setRefundSpecificReason(giveBackBean.getRefundSpecificReason());
        orders.setRefundRemark(giveBackBean.getRefundRemark());
        orders.setCostStatus(OrderStatusType.REFUNDED.getStatus());
        orders.setRobStatus("2");
        //设置服务费为0元
        orders.setPayServiceCharge(BigDecimal.ZERO);
        //判断是否是全额退款
        Integer refundType = null;
        Long refundAmount = giveBackBean.getRefundAmount();
        boolean flag = (null!=refundAmount&&orders.getPayAmount().longValue() == refundAmount)||null==refundAmount;
        if (flag) {
            if (ObjectUtil.equal(orders.getIsDealCar(), 1)) {
                orders.setIsDealCar(0);
            }
            refundType = 1;
        } else {
            refundType = 2;
        }
        //处理退款原因
        String refundReason = giveBackBean.getRefundReason();
        if (Strings.isNotBlank(refundReason)) {
            orders.setRefundReason(refundReason);
            if ("已装货,协商退款".equals(refundReason) || "已装货,退还订金".equals(refundReason) || "货已送达,协商退款".equals(refundReason) || "货已送达,退还订金".equals(refundReason)) {
                orders.setLoadingStatus(1);
                orders.setLoadTime(new Date());
                if(flag){
                    orders.setOrderNewStatus(OrderStatusEnum.WAIT_UNLOADED_OR_RECEIVED.getCode());
                }
            }else{
                //未装货原因
                if(flag){
                    orders.setOrderNewStatus(OrderStatusEnum.CANCEL.getCode());
                }
            }
        }
        orders.setRefundAmount(refundAmount);
        orders.setRefundTime(new Date());
        orders.setRefundStatus(1); //退款状态更新为 1.退款中
        //设置退还车方金额 和 退还货方金额
        orders.setCarAmount(refundAmount==null?0:refundAmount);
        orders.setGoodsAmount(orders.getPayAmount()-orders.getCarAmount());
        orders.setCarServiceAmount(giveBackBean.getCarServiceAmount()==null?0:giveBackBean.getCarServiceAmount());
        orders.setPlatformServiceAmount(orders.getTecServiceFee()-orders.getCarServiceAmount());
        this.getBaseDao().update(orders);

        //三方平台类型 1:满帮
        Integer thirdPartyPlatformType = orders.getThirdpartyPlatformType();
        if (thirdPartyPlatformType != null && thirdPartyPlatformType == 1) {
            //发送满帮平台退款的MQ消息
            tytMqMessageService.saveAndSendMbInfofeeMq(orders);
        } else {
            //记日志，发短信，发push消息
            tytMqMessageService.saveAndSendInfofeeMq(orders, InfoFeeOperateTypeEnum.info_fee_operate_manage_ex_refund.getCode(), refundType);
        }
    }

    /**
     *  根据满帮退款原因判断是否需要退还技术服务费
     * <AUTHOR>
     * @param refundReason 满帮退款原因
     * @return boolean
     */
    private boolean checkWhetherFullRefundTec(String refundReason){
        MbOrderRefundReasonEnum refundReasonEnum = MbOrderRefundReasonEnum.getRefundReasonEnum(refundReason);
        log.info("checkWhetherFullRefundTec refundReason:{},return:{}",refundReason,Objects.nonNull(refundReasonEnum)?refundReasonEnum:"");
        if(Objects.nonNull(refundReasonEnum)&&refundReasonEnum.isRefundFlag()){
            return true;
        }
        return false;
    }

}
