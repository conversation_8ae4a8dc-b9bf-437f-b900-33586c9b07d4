package com.tyt.infofee.service.impl;

import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.WechatShareTransportBean;
import com.tyt.infofee.service.WechatShareService;
import com.tyt.model.TransportMain;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.XXTea;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class WechatShareServiceImpl implements WechatShareService {
    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Override
    public WechatShareTransportBean getShareInfo(Long userId, TransportMain main, Integer target,String clientSign,String clientVersion) {
        Integer shareType = 0;
        //0 = 车 1=货
        Integer sharePort = 0;
        //分享小程序所需字段
        String wechatAppletUserName = "";
        //分享小程序所需字段
        String wechatAppletSharePath = "";
        //分享小程序的环境 0：正式 1：测试 2：体验
        Integer appletEnvironment = 0;
        //车版
        if (StringUtils.endsWithAny(clientSign, "21", "31")) {
            //车版app分享 H5 OR 小程序 开关,0=H5 1=小程序
            shareType = configService.getIntValue("car_wechat_share_type", 0);
            //车版微信小程序分析所需的userName
            wechatAppletUserName = configService.getStringValue("car_wechat_applet_user_name", "");
            //车版微信小程序分享所需的path
            wechatAppletSharePath = configService.getStringValue("car_wechat_applet_share_path", "");
            //车板分享小程序的环境 0：正式 1：测试 2：体验
            appletEnvironment = configService.getIntValue("car_applet_environment",2);
        }
        //货版
        if (StringUtils.endsWithAny(clientSign, "22", "32")) {
            //货版app分享 H5 OR 小程序 开关,0=H5 1=小程序
            shareType = configService.getIntValue("goods_wechat_share_type", 0);
            sharePort = 1;
            //货版微信小程序分析所需的userName
            wechatAppletUserName = configService.getStringValue("goods_wechat_applet_user_name", "");
            //货版微信小程序分享所需的path
            wechatAppletSharePath = configService.getStringValue("goods_wechat_applet_share_path", "");
            //分享小程序的环境 0：正式 1：测试 2：体验
            appletEnvironment = configService.getIntValue("goods_applet_environment",2);
        }
        log.info("获取分享信息,shareType={},wechatAppletUserName={},wechatAppletSharePath={}", shareType, wechatAppletUserName, wechatAppletSharePath);
        //H5
        WechatShareTransportBean baseShareInfo = getBaseShareInfo(userId, main);
        baseShareInfo.setShareType(shareType);
        if (shareType == 0) {
            return getBaseShareInfo(userId, main);
        }
        //小程序
        if (shareType == 1 && target == 0) {
            //TODO 车APP 版本兼容
            if (sharePort == 0 && StringUtils.isNotBlank(clientVersion) && Integer.parseInt(clientVersion) < 6410) {
                baseShareInfo.setShareType(0);
                return baseShareInfo;
            }
            //方便后期扩展，目前[货APP]分享小程序图片由APP端生成,如果有需求服务端生成则给IconUrl赋值，即当IconUrl为null时客户端生成分享图片，当IconUrl有值时表示服务端生成分享图片
            baseShareInfo.setIconUrl(null);
            baseShareInfo.setAppletUserName(wechatAppletUserName);
            String path = StringUtils.replaceEach(wechatAppletSharePath, new String[]{"${goodsId}", "${detailType}", "${sharerUserId}"}, new String[]{String.valueOf(main.getId()), "2", String.valueOf(userId)});
            baseShareInfo.setPath(path);
            baseShareInfo.setAppletEnvironment(appletEnvironment);
            return baseShareInfo;
        }
        //小程序分享朋友圈的情况返回H5信息
        if (shareType == 1 && target == 1) {
            return baseShareInfo;
        }
        return null;
    }

    public WechatShareTransportBean getBaseShareInfo(Long userId, TransportMain main){
        String title = "特运通邀您免费找货，工程机械货源多，有保障";
        String con = "";
        if (StringUtils.isNotBlank(main.getPrice()) && !main.getPrice().equals("0")) {
            con = main.getTaskContent() + "，" + main.getPrice() + "元";
        } else {
            con = main.getTaskContent();
        }
        String content=String.format("%s，%s->%s", con,main.getStartPoint(),main.getDestPoint());
        Integer IsOwnerGoods=2;//是否是货主货源  1是货主货源 2 不是
        if(userId.intValue()==main.getUserId().intValue()) {
            IsOwnerGoods=1;
        }
        String h5Url = configService.getStringValue("wechat_share_h5_url");
        String encryptGoodsId= XXTea.Encrypt(main.getId()+","+IsOwnerGoods+"", AppConfig.getProperty("tyt.xxtea.key"));

        WechatShareTransportBean share=new WechatShareTransportBean();
        share.setShareUrl(h5Url+"?tk="+encryptGoodsId);
        share.setShareTitle(title);
        share.setShareContent(content);
        share.setIconUrl("http://www.teyuntong.com/app/image_logo/logo120.png");
        return share;
    }
}
