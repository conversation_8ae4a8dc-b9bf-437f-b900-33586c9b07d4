package com.tyt.infofee.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.infofee.service.TytFinancialFlowService;
import com.tyt.model.Order;
import com.tyt.model.TytTradeinfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service("tytFinanciaFlowService")
public class TytFinanciaFlowServiceImpl extends BaseServiceImpl<TytTradeinfo, Long> implements TytFinancialFlowService {

    @Resource(name = "tradeInfoDao")
    public void setBaseDao(BaseDao<TytTradeinfo, Long> tradeInfoDao) {
        super.setBaseDao(tradeInfoDao);
    }

    @Override
    public void addFinancialFlow(int flowType, String accountSymbolPositive, long amount, int applyId, long acountId, long beforeRemaining, long afterRemaining, long userId) {
        String sql = "INSERT INTO `tyt`.`tyt_financial_flow` (" +
                "`create_time`, " +
                "`user_id`, " +
                "`flow_type`, " +
                "`apply_id`, " +
                "`money_amount`, " +
                "`before_reaminging`, " +
                "`after_remaining`, " +
                "`account_id`" +
                ") VALUES (?,?,?,?,?,?,?,?)";
        Object[] params = new Object[]{
                new Date(),
                userId,
                flowType,
                applyId,
                accountSymbolPositive + amount,
                beforeRemaining,
                afterRemaining,
                acountId
        };
        this.executeUpdateSql(sql, params);
    }
}
