package com.tyt.infofee.service;

import com.tyt.plat.entity.base.TytTransportEnterpriseLog;

/**
 * 开票货源企业信息服务层
 *
 * <AUTHOR>
 * @since 2024/07/24 09:47
 */
public interface TransportEnterpriseLogService {

    /**
     * 根据srcMsgId获取开票货源企业信息
     *
     * <AUTHOR>
     * @param srcMsgId 货源ID
     * @return TytTransportEnterpriseLog
     */
   TytTransportEnterpriseLog getBySrcMsgId(Long srcMsgId);

}
