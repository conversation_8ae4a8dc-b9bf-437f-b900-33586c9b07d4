package com.tyt.infofee.service;

import com.tyt.infofee.bean.CreditUserInfo;
import com.tyt.infofee.bean.InfoFeeDetail;
import com.tyt.infofee.bean.TransportInfoFeeBean;

import java.util.List;
import java.util.Map;

public interface IInfofeeDetailService {
    /**
     * 货主运单(信息费)详情
     *
     * @param tsOrderNo
     * @param isPc
     * @return
     */
    InfoFeeDetail ownerInfofeeDetail(String tsOrderNo, String userId, boolean isPc, String clientSign);

    /**
     * 车主运单(信息费)详情
     *
     * @param tsOrderNo
     * @param userId
     * @param isPc
     * @return
     */
    InfoFeeDetail driverInfofeeDetail(String tsOrderNo, String userId, boolean isPc, Long id, String clientSign);

    /**
     * 获取平台合作次数（信息费交易次数）
     *
     * @param userId
     * @return
     */
    Integer getCoopNums(Long userId);

    /**
     * 获取用户信用信息， 如：平台交易量、和我交易量
     * 名称取车方名称
     * @param carUserIds 车方ID组，例：(111,222,333,...)
     * @param userId     当前用户
     * @return List<CreditUserInfo>
     */
    List<CreditUserInfo> queryCreditUserInfo(String carUserIds, String userId);

    /**
     * 获取用户信用信息， 如：平台交易量、和我交易量
     *
     * @param goodsUserIds 货源持有人ID组，例：(111,222,333,...)
     * @param userId       当前用户
     * @return Map<Long, CreditUserInfo> key为userId，value为CreditUserInfo
     */
    Map<Long, CreditUserInfo> queryCreditUserInfoMapForCar(String goodsUserIds, Long userId);

    /**
     * 获取用户信用信息， 如：平台交易量、和我交易量
     *
     * @param goodsUserId 货源持有人ID
     * @param carUserId   当前用户ID
     * @return
     */
    CreditUserInfo getCreditUserInfoForCar(String goodsUserId, String carUserId);

    /**
     * 获取平台交易量
     *
     * @param userId
     * @return
     */
    Integer getTradeNums(Long userId);

    TransportInfoFeeBean queryInfofee(String tsOrderNo, String userId, boolean isPc);
}
