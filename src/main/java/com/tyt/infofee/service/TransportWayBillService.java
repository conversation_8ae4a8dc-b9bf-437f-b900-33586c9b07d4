package com.tyt.infofee.service;

import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.GoodsDetailOrderFinishedBean;
import com.tyt.infofee.bean.GoodsDetailOrderLoadingBean;
import com.tyt.infofee.bean.ListDataBean;
import com.tyt.manbang.bean.request.MbOrderCreateSucNoticeBean;
import com.tyt.model.Transport;
import com.tyt.model.TytTransportOrders;
import com.tyt.model.TytTransportWaybill;
import com.tyt.model.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface TransportWayBillService extends
		BaseService<TytTransportWaybill, String> {
	/**
	 * 我的货源-待同意/装货中/装货完成运单列表查询
	 * 
	 * @param userId
	 * @param queryActionType
	 * @param queryMenuType
	 * @param queryID
	 * @return ListDataBean
	 */
	public ListDataBean updateGetMyWayBillList(Long userId,
			int queryActionType, int queryMenuType, long queryID)
			throws Exception;

	/**
	 * 首次保存线上支付的货物数据到tyt_transport_waybill表
	 * @param originalTransport
	 * @throws Exception
	 */
	public TytTransportWaybill saveOnLineWayBill(Integer zeroAssignOrder,MbOrderCreateSucNoticeBean orderCreateSucNoticeBean, Transport originalTransport, User payUser, Integer timeLimitIdentification, BigDecimal couponAmount) throws Exception;

	/**
	 * 线下成交的货物数据添加到tyt_transport_waybill表
	 * 
	 * @param oldTransport
	 * @return
	 * @throws Exception
	 */
	public void saveOffLineDealGoodsToTransportWayBill(Transport oldTransport,String infoStatus)
			throws Exception;
	/**
	 * 改变运单的车主信息
	 * @param carOwnerUserId:车主用户ID
	 * @param transportNo:运单号
	 * @param infoStatus:信息费运单状态：0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
	 * @param carOwnerRegisterPhone:车主注册电话
	 * @param carOwnerTelephone:车主联系电话
	 * @param agencyMoney:运前信息费
	 * @param carOwnerPayEndTime:车主支付成功实践
	 * @param payNumber:支付人数
	 * @return
	 * @throws Exception
	 */
	public int saveChangeCarOwnerInfo(Long carOwnerUserId,String transportNo,String infoStatus,
			String carOwnerRegisterPhone,String carOwnerTelephone,Long agencyMoney,Date carOwnerPayEndTime,Integer payNumber)
			throws Exception;
	/**
	 * 通过运单号获得一个带锁的对像
	 * @param tsOrdero 
	 * @return TytTransportWaybill
	 */
	 
	  public TytTransportWaybill getTytTransportWaybillForLock(String tsOrderNo);
	/**
	 * 改变运单表的支付人数
	 * @param tsOrderNo
	 * @param number,定义成int类型（数据库也是int类型），人数多的话可能会出错
	 * @return
	 */
	  public int saveChangePayNumber(String tsOrderNo,int payNumber)throws Exception;
      /**
       * 清空某个运单的车主支付信息
       * @param userId
       * @param tsOrderNo
       * @return
       * @throws Exception
       */
//	  public int saveChangeClearCarOwnerInfo(Long userId, String tsOrderNo)throws Exception;
	/**
	 * 修改运单为装货完成
	 * @param userId
	 * @param tsOrderNo
	 * @param tytTransportOrders 
	 * @param transportWayBill 
	 * @param srcMsgId 
	 * @return int 500是运单不存在
	 */
	  public Map<String,Object>  updateWayBillFinish(Long userId,String tsOrderNo, TytTransportWaybill transportWayBill, TytTransportOrders tytTransportOrders, Long srcMsgId);
	/**
	 * 查询装货中运单的支付信息
	 * @param transportNo:运单号
	 * @return
	 * @throws Exception
	 */
	  public List<GoodsDetailOrderLoadingBean> getOrderLoadingInfo(String transportNo)throws Exception;
	  /**
		 * 查询装货完成运单的支付信息
		 * @param userId:用户ID
		 * @param transportNo:运单号
		 * @return
		 * @throws Exception
		 */
	  public List<GoodsDetailOrderFinishedBean> getOrderFinishedInfo(String transportNo)throws Exception;
	/**
	 * 修改用户的该笔运单为某种接单状态
	 * @param userId
	 * @param transportNo
	 * @param infoStatus:信息费运单状态：0待接单  1有人支付成功 （货主的待同意   ）2装货中（车主是待装货 ）3车主装货完成  4系统装货完成 5异常上报
	 * @return
	 */
	public int saveChangeInfoStatus(Long userId, String transportNo,
			String infoStatus);
	/**
	 * 获得账户ID
	 * @param userId
	 * @param affiliatedType
	 * @return
	 */
	public int getUserAccount(Long userId ,int affiliatedType);

	public TytTransportWaybill getTransportWaybillBySrcMsgId(Long srcMsgId);

	/**
	 * 剩最后一个支付成功人时，更新waybill信息
	 * @param tsOrderNo 订单号
	 * @param payUserId 支付人ID
	 * @param payCellPhone 支付人账号
	 * @param payLinkPhone 支付人联系电话
	 * @param payAmount 支付金额
	 * @return
	 */
	public Boolean updateWaybillOneInfo(String tsOrderNo, Long payUserId, String payCellPhone,
													String payLinkPhone, Long payAmount);

	/**
	 * 根据满帮发过来的确认给车方成功的消息 更新tyt_transport_waybill表记录
	 * @param tsOrderNo
	 * @return
	 * @throws Exception
	 */
	int updateWayBillForConfirmSuccess(String tsOrderNo) throws Exception;

	/**
	 * 根据参数添加交易记录 并获取新增的主键Id
	 * @param orders
	 */
	int addTradeInfo(Integer tradeType,TytTransportOrders orders,Integer payUserId,Integer payReceiverUserId,Integer refundingAccount);


	/**
	 * 更新tyt_transport_waybill表
	 * @throws Exception
	 */
	void updateOnLineWayBill( TytTransportWaybill tytTransportWaybill,MbOrderCreateSucNoticeBean orderCreateSucNoticeBean,  User payUser, Integer timeLimitIdentification) throws Exception;

}
