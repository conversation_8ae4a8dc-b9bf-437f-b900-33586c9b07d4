package com.tyt.infofee.service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.service.BaseService;
import com.tyt.infofee.bean.GoodsDetailOrderExceptionBean;
import com.tyt.infofee.bean.ListDataBean;
import com.tyt.infofee.bean.MyComplaintListBean;
import com.tyt.manbang.bean.request.CarExceptionReportingBean;
import com.tyt.manbang.bean.request.HandleExceptionInfoBean;
import com.tyt.manbang.bean.request.ShipperExceptionReportingBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;
import com.tyt.model.TytTransportWaybill;
import com.tyt.model.TytTransportWaybillEx;
import java.util.List;

public interface TransportWayBillExService extends BaseService<TytTransportWaybillEx,Long> {
	/**
	 * 违约异常上报列表查询接口（我的货源/已接单共用）
	 * @param userId
	 * @param queryActionType
	 * @param queryMenuType
	 * @param queryID
	 * @return ListDataBean
	 */
	  public ListDataBean updateGetMyWayBillExList(Long userId,int queryActionType, int queryMenuType,long queryID)  throws Exception;


	/**
	 * 违约异常上报列表查询接口（我的货源/已接单共用）
	 * @param userId
	 * @param queryActionType
	 * @param queryMenuType
	 * @param queryID
	 * @return ListDataBean
	 */
	public ListDataBean updateGetMergeExList(Long userId,int queryActionType, int queryMenuType,long queryID, long queryCID) throws Exception;

	  /**
	   * 保存异常上报信息
	   * @param userId
	   * @param tsOrderNo
	   * @param exParty
	   * @param exType
	   * @param exOther
	 * @param srcMsgId
	   * @return int 200 成功
	   *{code:〞210901〞,msg:〞此货源已经被车主方异常上报，不能再进行异常上报。〞}
		{code:〞210902〞,msg:〞此货源发货方已经进行异常上报，不能再进行异常上报〞}。
		{code:〞210903〞,msg:〞此货源已经完成装货，不能再进行异常上报〞}
	   */
	  public int save(Long userId,String tsOrderNo,String exParty,
				 String exType ,String exOther, TytTransportWaybill transportWayBill, Long srcMsgId);
	  /**
	   * 获取详情中需要的异常信息
	   * @param tsOrderNo:运单号
	   * @return
	   */
	  public GoodsDetailOrderExceptionBean getGoodsDetail(String tsOrderNo)throws Exception;

	/**
	 * 查询运单是否服务评价过
	 *
	 * @param tsOrderId:运单ID
	 * @param userId 用户ID
	 * @return
	 */
	public Integer isExFeedBack(Long tsOrderId, Long userId);

	/**
     * 异常上报接口
     * @param orders
     * @param exParty  //异常上报方身份1车主上报，2货主上报
     * @param exType  //异常上报类型
     * @param exOther  //异常上报类型 其他类型信息
     * @throws Exception
     */
    void saveEx(TytTransportOrders orders, String exParty,String exType, String exOther,Integer loadingStatus,Integer loadingChildStatus,
				String proofReason,
				String pictureVouchers,Long userId)throws Exception;


	/**
	 * 订单发起投诉
	 *
	 * @param orders
	 * @param exParty  //异常上报方身份1车主上报，2货主上报
	 * @param exType  //异常上报类型
	 * @param exOther  //异常上报类型 其他类型信息
	 * @throws Exception
	 */
	void saveComplaint(TytTransportOrders orders, String exParty,String exType, String exOther,Integer loadingStatus,Integer loadingChildStatus,
				String proofReason,
				String pictureVouchers,Long userId) throws Exception;

    void addVoucher(TytTransportOrders orders,Long exId, String exParty,
					String proofReason,
					String pictureVouchers,Long userId);

	/**
	 * 当第三方平台传递过来订金退款或确认消息时 需要对处于异常上报中的数据进行归档处理
	 * @param orderId
	 * @param loadingStatus
	 * @param carAmount
	 * @param goodsAmount
	 * @throws Exception
	 */
	void HandlerTransportWaybillExForThirdPlatform(String orderId,Integer loadingStatus,Long carAmount,Long goodsAmount)throws Exception;

	/**
	 * 满满车方发起异常上报(特运通货源 )
	 * @param carExceptionReportingBean
	 * @param orders
	 * @throws Exception
	 */
	void saveCarExceptionReporting(CarExceptionReportingBean carExceptionReportingBean, TytTransportOrders orders) throws Exception;

	/**
	 * 满满货方发起异常上报 (满帮货源 )
	 * @param shipperExceptionReportingBean
	 * @param orders
	 * @throws Exception
	 */
	void saveShipperExceptionReporting(ShipperExceptionReportingBean shipperExceptionReportingBean, TytTransportOrders orders) throws Exception;


	/**
	 * 满满异常上报处理完成通知
	 * @param handleExceptionInfoBean
	 * @param orders
	 */
	void handleExceptionInfo(HandleExceptionInfoBean handleExceptionInfoBean, TytTransportOrders orders) throws Exception;

	TytTransportWaybillEx getWayBillExByOrderId(Long orederId,Integer orderType);



	/**
	 * 根据参数查看异常上报列表
	 * @param orderId
	 * @param orderType
	 * @param cancelExParty  0所有 1车方 2货方
	 * @return
	 */
	List<TytTransportWaybillEx> getWayBillExsByOrderId(Long orderId,Integer orderType,String cancelExParty);


	TytTransportWaybillEx getWayBillExByOrderIdAndUserId(Long orederId, Long userId);

	/**
	 * 异常上报处理评价
	 * @param orderId
	 * @param userId
	 * @param evaluateParty
	 * @param dealResultEvaluate
	 * @param dealTimeEvaluate
	 * @param serviceAttitudeEvaluate
	 * @return
	 */
	ResultMsgBean evaluate(Long orderId, Long userId, Integer evaluateParty, Integer dealResultEvaluate,
						   Integer dealTimeEvaluate, Integer serviceAttitudeEvaluate, Integer decideResponsibilityEvaluate,String evaluateDetail);

	/**
	 * 我的投诉列表
	 */
	List<MyComplaintListBean> getMyComplaintList(BaseParameter userId, Integer page, Integer size);

	/**
	 * 异常上报撤销处理
	 * @param orders
	 * @param userId
	 * @param cancelExParty
	 * @return
	 */
	ResultMsgBean cancelExSave(TytTransportOrders orders, Long userId,String cancelExParty);


	ResultMsgBean cancelExSave(Long exId, Integer exCancelStatus, String cancelExParty,Long orderId,ResultMsgBean resultMsgBean);

	/**
	 * 查询未完成的异常上报订单
	 * @param userId
	 * @return
	 */
    int selectCountForStatus(Long userId);

	TytTransportWaybillEx getExById(Long waybillExId);
}
