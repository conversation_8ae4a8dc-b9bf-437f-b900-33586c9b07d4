package com.tyt.infofee.controller;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.InfofeeStatusEnum;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillService;
import com.tyt.infofee.service.impl.InfofeeDetailServiceImpl;
import com.tyt.model.*;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.client.trade.infofee.ApiTradeInfoFeeClient;
import com.tyt.promo.service.PromoCouponWriteoffService;
import com.tyt.promo.service.PromoUserCouponService;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Controller
@RequestMapping("/plat/infoFee/orders/")
public class TransportOrdersController extends BaseController {
    private static final String AGREE_LOAD_MESSAGE_TEMPLATE_KEY = "agree.load.message";

    @Resource(name = "transportOrdersService")
    private TransportOrdersService transportOrdersService;

    @Resource(name = "infoFeeBusinessService")
    private InfoFeeBusinessService infoFeeBusinessService;

    @Resource(name = "transportWayBillService")
    private TransportWayBillService transportWayBillService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "tytMessageTmplService")
    private TytMessageTmplService messageTmplService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;

    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Resource(name = "tytBubbleService")
    TytBubbleService tytBubbleService;

    @Resource(name="promoUserCouponService")
    PromoUserCouponService promoUserCouponService;

    @Resource(name="promoCouponWriteoffService")
    PromoCouponWriteoffService couponWriteoffService;

    @Autowired
    private ApiTradeInfoFeeClient apiTradeInfoFeeClient;

    @Autowired
    private IFeedbackUserService feedbackUserService;

    private static final PropertiesFileUtil propertiesFileUtil = PropertiesFileUtil.init("message");

    /**
     * 已接列表查询（待支付/待同意/待装货/已成交/决绝退费）
     *
     * @param baseParameter
     * @param queryID         下拉是0，上滑是最小sortId；（首次queryID=0）
     * @param queryMenuType   1待支付2待同意3待装货4已成交5决绝退费
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @return ResultMsgBean
     */
    @RequestMapping(value = "list")
    @ResponseBody
    public ResultMsgBean list(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
        ResultMsgBean rm = new ResultMsgBean();

        try {
            if (null == queryMenuType || queryMenuType.intValue() < 1 || queryMenuType.intValue() > 5) {

                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("状态不在范围");
                return rm;
            }
            // 检查属性
            if (checkQueryParameter(queryActionType, queryID, rm)) {
                ListDataBean listDataBean = transportOrdersService.updateGetMyOrdersList(baseParameter.getUserId(), queryActionType.intValue(), queryMenuType.intValue(), queryID.longValue());
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(listDataBean);
                rm.setTotalSize(listDataBean == null ? 0l : listDataBean.getData() == null ? 0l : listDataBean.getData().size());

            }

        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * 已接列表查询（待支付/待同意/待装货/已成交/决绝退费）
     *
     * @param baseParameter
     * @param queryID         下拉是0，上滑是最小sortId；（首次queryID=0）
     * @param queryMenuType   1待支付2待同意3待装货4已成交5决绝退费
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @return ResultMsgBean
     */
    @RequestMapping(value = "merge/list")
    @ResponseBody
    public ResultMsgBean mergelist(BaseParameter baseParameter,
                                   @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType,
                                   @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType,
                                   @RequestParam(value = "queryID", defaultValue = "0") Long queryID,
                                   @RequestParam(value = "queryCID", defaultValue = "0") Long queryCID
    ) {
        ResultMsgBean rm = new ResultMsgBean();

        try {
            if (null == queryMenuType || queryMenuType.intValue() < 1 || queryMenuType.intValue() > 7) {

                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("状态不在范围");
                return rm;
            }
            // 检查属性
            if (checkQueryParameter(queryActionType, queryID, rm)) {
                ListDataBean listDataBean = transportOrdersService.getMergeOrdersList(baseParameter.getUserId(), queryActionType.intValue(), queryMenuType.intValue(), queryID.longValue(), queryCID.longValue());
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(listDataBean);
                rm.setTotalSize(listDataBean == null ? 0l : listDataBean.getData() == null ? 0l : listDataBean.getData().size());

            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * 车主待支付信息费接口 - 2019-01-04信息费改版新接口
     *
     * @param baseParameter
     * @param queryID         下拉是0，上滑是最小sortId；（首次queryID=0）
     * @param queryMenuType   1待支付
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @return ResultMsgBean
     */
    @RequestMapping(value = {"carOwnerUnpaidList","carOwnerUnpaidList.action"})
    @ResponseBody
    public ResultMsgBean carOwnerUnpaidList(BaseParameter baseParameter,
                                   @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType,
                                   @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType,
                                   @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (null == queryMenuType || queryMenuType.intValue() != 1) {
                rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                rm.setMsg("状态不在范围");
                return rm;
            }
            // 检查属性
            if (checkQueryParameter(queryActionType, queryID, rm)) {
                ListDataBean listDataBean = transportOrdersService.getCarOwnerUnpaidList(baseParameter.getUserId(), queryActionType.intValue(), queryMenuType.intValue(), queryID.longValue());
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(listDataBean);
                rm.setTotalSize(listDataBean == null ? 0l : listDataBean.getData() == null ? 0l : listDataBean.getData().size());

            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
   }

    private boolean checkQueryParameter(Integer queryActionType, Long queryID, ResultMsgBean rm) {
        if (queryActionType.intValue() < 1 || queryActionType.intValue() > 2) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("查询类型不正确！");
            return false;
        } else if (queryID.longValue() < 0) {
            rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
            rm.setMsg("查询标识错误，最大、最小ID为空！");
            return false;
        }
        return true;

    }

    /**
     * 拒绝/同意装货
     *
     * @return
     */
    //该方法可能已经弃用，请使用其他方法代替，如果你明确该方法是否弃用，请在此补充
    @Deprecated
    @RequestMapping(value = "saveOrderStatus")
    @ResponseBody
    public ResultMsgBean saveChangeOrderRefuseOrAgree(Long userId, String tsOrderNo, Long[] payUserId, Integer operateType) {
        logger.warn("Deprecated_check_use ############# ");
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        Transport transport = null;
        try {
            // 参数验证
            if (userId == null || userId.longValue() <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (tsOrderNo == null || tsOrderNo.trim().equals("")) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("tsOrderNo参数错误");
                return resultMsgBean;
            }
            if (payUserId == null || payUserId.length < 1) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("payUserId参数错误");
                return resultMsgBean;
            }
            if (operateType == null || (operateType != 1 && operateType != 2)) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("operateType参数错误");
                return resultMsgBean;
            }
            if (operateType == 2 && payUserId.length != 1) {
                resultMsgBean.setCode(ReturnCodeConstant.INFO_FEE_ONLY_AGREE_ONE_CAROWNER);
                resultMsgBean.setMsg(propertiesFileUtil.getString("tyt.infofee.agree.only.one.carowner.8000"));
                return resultMsgBean;
            }
            long t1 = System.currentTimeMillis();
            Long realPayUserId = payUserId[0];
            MqMoneyRefundMsg mqMoneyRefundMsg = null;
            TytTransportOrders originalOrder = null;
            if(payUserId.length == 1) {
                originalOrder = transportOrdersService.getTytTransportOrders(tsOrderNo, "1", realPayUserId);
            } else { // 处理并发情况下，多个支付用户时，第一条数据异常导致整个全部拒绝失效的问题
                for(Long ids : payUserId){
                    realPayUserId = ids;
                    originalOrder = transportOrdersService.getTytTransportOrders(tsOrderNo, "1", realPayUserId);
                    if (originalOrder != null) {
                        break;
                    }
                }
            }

            //信息费新版本
            if(TytSwitchUtil.isNewInfofeeVersion()){
                originalOrder = transportOrdersService.getNewTransportOrders(tsOrderNo, realPayUserId, InfofeeStatusEnum.已支付);
            }

            if (originalOrder == null) {
                //线下信息费
                logger.info("tsOrderNo:{}, payUserId: {}", tsOrderNo, realPayUserId);
                originalOrder = transportOrdersService.getTytOfflineTransportOrders(tsOrderNo, realPayUserId);
//					originalOrder = transportOrdersService.getTytOfflineTransportOrders("18102100000025", 148368l);

                logger.info("originalOrder {}", originalOrder == null ? "null" : new Gson().toJson(originalOrder));
            }

            if (originalOrder == null || StringUtil.equalsWithAny("12")) { // 排除线下信息费订单和车方已取消的订单
                resultMsgBean.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
                resultMsgBean.setMsg("订单状态已变更，请刷新后重试");
                return resultMsgBean;
            }

            //信息费新版本
            if(TytSwitchUtil.isNewInfofeeVersion()){
                logger.info("取消未支付订单 {}", tsOrderNo);
                transportOrdersService.saveChangeWaitToDisappearNew(userId, tsOrderNo);
            }

            transport = transportBusiness.getByGoodsId(originalOrder.getTsId());

            logger.info("transport order saveChangeOrderRefuseOrAgree get redis lock begin, src msg id is: " + transport.getSrcMsgId());
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            // tyt_plat_transport_optimize20171123 货源优化代码
            if (LockUtil.lockObject("1", transport.getSrcMsgId() + "", redisLockTimeout) && LockUtil.lockObject("1", tsOrderNo, redisLockTimeout)) {
                logger.info("transport order saveChangeOrderRefuseOrAgree get redis lock successed, src msg id is: " + transport.getSrcMsgId());
                /* comment:以下两步分事务处理,1同意拒绝行为、2发送MQ互不影响 */
                if(!TytSwitchUtil.isNewInfofeeVersion()) {
                    //旧信息费退款
                    mqMoneyRefundMsg = infoFeeBusinessService.saveChangeOrderRefuseOrAgree(userId, payUserId, tsOrderNo, operateType, transport.getSrcMsgId());

                    if (mqMoneyRefundMsg != null) {
                        tytMqMessageService.sendRefundMqMessage(mqMoneyRefundMsg);
                    }
                }else {
                    infoFeeBusinessService.saveChangeOrderRefuseOrAgreeWithNewInfofee(userId, payUserId, tsOrderNo, operateType, transport.getSrcMsgId());
                }
                // 如果是同意装货则给车主发送同意装货的短信提醒
                if (operateType != null) {
                    if (operateType.intValue() == 2) {
                        ShortMsgBean shortMsgBean = new ShortMsgBean();
                        // 根据短信key获取短信模板
                        String content = messageTmplService.getSmsTmpl(AGREE_LOAD_MESSAGE_TEMPLATE_KEY, "货方已同意装货，运单号${orderNum}，请登录特运通查看");
                        shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
                        String messageSerailNum = SerialNumUtil.generateSeriaNum();
                        shortMsgBean.setMessageSerailNum(messageSerailNum);
                        String applySucesscontent = StringUtils.replaceEach(content, new String[]{"${orderNum}"}, new String[]{tsOrderNo});
                        shortMsgBean.setContent(applySucesscontent);
                        User user = userService.getById(realPayUserId);
                        shortMsgBean.setCell_phone(user.getCellPhone());
                        shortMsgBean.setRemark("");
                        String messageContent = JSON.toJSONString(shortMsgBean);
                        logger.info("refuse or agree send mq message content is: " + shortMsgBean);
                        /** 特运通账户ID */
                        String companyAccountUserId = configService.getStringValue("tyt_company_account_user_id");
                        logger.info("transport recommend company user id is: " + companyAccountUserId + " , pub goods user id is: " + transport.getUserId());
                        // 因人工派单货源已有同意装货短信息通知，这里不再进行发送短信
                        if (!companyAccountUserId.equals(transport.getUserId() + "")) {
                            tytMqMessageService.addSaveMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
                            tytMqMessageService.sendMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
                        }
                    }
                }
            }
            long t2 = System.currentTimeMillis();
            logger.info("APP信息费拒绝/同意装货时间【{}ms】," + "参数【userId:{}】,运单号【{}】,车主ID【{}】,操作类型【{}:1拒绝装货 2同意装货】", t2 - t1, userId, tsOrderNo, payUserId, operateType);
            // 返回结果
            return resultMsgBean;
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        } finally {
            if (transport != null) {
                logger.info("transport order saveChangeOrderRefuseOrAgree get redis lock successed, src msg id is: " + transport.getSrcMsgId());
                LockUtil.unLockObject("1", transport.getSrcMsgId() + "");
                LockUtil.unLockObject("1", tsOrderNo);
            }
        }
    }

    /**
     * 车方取消装货
     *
     * @return
     */
    @RequestMapping(value = "saveOrderStatusForCar")
    @ResponseBody
    public ResultMsgBean saveChangeOrderCancelForCar(Long userId, String tsOrderNo, Integer operateType) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        Transport transport = null;
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (tsOrderNo == null || tsOrderNo.trim().equals("")) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("tsOrderNo参数错误");
                return resultMsgBean;
            }
            if (operateType == null || (operateType != 1)) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("operateType参数错误");
                return resultMsgBean;
            }
            long t1 = System.currentTimeMillis();
            Long realPayUserId = userId;
            MqMoneyRefundMsg mqMoneyRefundMsg = null;

            //获取当前订单状态
            SimpleOrderBean simpleOrder = transportOrdersService.getLastOrderByPayUser(tsOrderNo, realPayUserId);
            if (StringUtil.equalsWithAny(simpleOrder.getRobStatus(), "2", "3", "12")) {
                //货主拒绝、系统拒绝、车方取消统统都去 拒绝/取消菜单
                resultMsgBean.setCode(ReturnCodeConstant.ORDER_IS_REFUSE_CODE);
                resultMsgBean.setMsg("您的订单已经被取消了");
                return resultMsgBean;
            } else if (StringUtil.equalsWithAny(simpleOrder.getRobStatus(), "4")) {
                //同意装货，进入待装货
                resultMsgBean.setCode(ReturnCodeConstant.ORDER_IS_AGREE_CODE);
                resultMsgBean.setMsg("您的订单已经同意装货了，请联系客服取消");
                return resultMsgBean;
            }

            TytTransportOrders originalOrder = transportOrdersService.getTytTransportOrders(tsOrderNo, "1", realPayUserId);
            transport = transportBusiness.getByGoodsId(originalOrder.getTsId());

            logger.info("transport order saveOrderStatusForCar get redis lock begin, src msg id is: " + transport.getSrcMsgId());
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("1", transport.getSrcMsgId() + "", redisLockTimeout) && LockUtil.lockObject("1", tsOrderNo, redisLockTimeout)) {
                logger.info("transport order saveOrderStatusForCar get redis lock successed, src msg id is: " + transport.getSrcMsgId());
                /* comment:以下两步分事务处理,1车方取消运单行为、2发送MQ互不影响 */
                mqMoneyRefundMsg = infoFeeBusinessService.saveChangeOrderCancelForCar(userId, tsOrderNo, operateType, transport.getSrcMsgId());
                if (mqMoneyRefundMsg != null) {
                    tytMqMessageService.sendRefundMqMessage(mqMoneyRefundMsg);
                }
            }
            long t2 = System.currentTimeMillis();
            logger.info("APP信息费车方取消装货时间【{}ms】," + "参数【userId:{}】,运单号【{}】,车主ID【{}】,操作类型【取消装货】", t2 - t1, userId, tsOrderNo, userId);
            // 返回结果
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        } finally {
            if (transport != null) {
                logger.info("transport order saveOrderStatusForCar get redis lock successed, src msg id is: " + transport.getSrcMsgId());
                LockUtil.unLockObject("1", transport.getSrcMsgId() + "");
                LockUtil.unLockObject("1", tsOrderNo);
            }
        }
    }

    /**
     * PC已接列表查询（待支付/待同意/待装货/已成交/决绝退费）
     *
     * @param baseParameter
     * @param queryID         下拉是0，上滑是最小sortId；（首次queryID=0）
     * @param queryMenuType   1待支付2待同意3待装货4已成交5决绝退费
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @return ResultMsgBean
     */
    @RequestMapping(value = "list.action")
    @ResponseBody
    public ResultMsgBean listForPc(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
        ResultMsgBean bean = this.list(baseParameter, queryActionType, queryMenuType, queryID);
        return bean;
    }

    @RequestMapping(value = "saveOrderStatus.action")
    @ResponseBody
    public ResultMsgBean saveChangeOrderRefuseOrAgreeAction(Long userId, String tsOrderNo, Long[] payUserId, Integer operateType) {
        return this.saveChangeOrderRefuseOrAgree(userId, tsOrderNo, payUserId, operateType);
    }

    /**
     * PC已接列表查询（全部列表）
     *
     * @param baseParameter
     * @param queryID         下拉是0，上滑是最小sortId；（首次queryID=0）
     * @param queryActionType 1下拉，2上滑；（首次queryActionType=1）
     * @return ResultMsgBean
     */
    @RequestMapping(value = "alllist.action")
    @ResponseBody
    public ResultMsgBean allListForPc(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
        ResultMsgBean rm = new ResultMsgBean();

        try {
            // 检查属性
            if (checkQueryParameter(queryActionType, queryID, rm)) {
                ListDataBean listDataBean = transportOrdersService.updateGetMyOrdersAllList(baseParameter.getUserId(), queryActionType, queryID);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(listDataBean);
                rm.setTotalSize(listDataBean == null ? 0l : listDataBean.getData() == null ? 0l : listDataBean.getData().size());
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    
    /**
     * @Description  车主信息费列表接口
     * <AUTHOR>
     * @Date  2018/12/21 11:38
     * @Param [baseParameter, queryActionType, queryMenuType, queryID]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"carOwnerInfofeeList","carOwnerInfofeeList.action"})
    @ResponseBody
    public ResultMsgBean carOwnerInfofeeList(BaseParameter baseParameter,
                                             @RequestParam(value = "queryID", defaultValue = "0") Long queryID,
                                             @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType,
                                             @RequestParam(value = "queryType", defaultValue = "8") Integer queryType
                                             ) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //检查属性
            if (checkQueryParameter(queryActionType, queryID, rm)) {
                //用户Id
                Long userId = baseParameter.getUserId();
                //clientSign
                String clientSign = baseParameter.getClientSign();
                //车主信息费列表
                CarOwnerInfoFeeBean carOwnerInfofeeList = transportOrdersService.getCarOwnerInfofeeList(userId, queryActionType, queryID, baseParameter.getClientVersion(), queryType,clientSign);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(carOwnerInfofeeList);
                //清空气泡
                tytBubbleService.updateBubbleNumber(userId, "1", "7",0);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @Description  车主信息费列表接口 （新 订单上平台）
     * <AUTHOR>
     * @Date  2021/04/02 11:38
     * @Param [baseParameter, queryActionType, queryMenuType, queryID]
     * @param queryType 0 全部 1待支付  2交易中 3冻结中 4已完成
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"carOwnerfeeList","carOwnerfeeList.action"})
    @ResponseBody
    public ResultMsgBean carOwnerfeeList(BaseParameter baseParameter,
                                             @RequestParam(value = "queryID", defaultValue = "0") Long queryID,
                                             @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType,
                                             @RequestParam(value = "queryType", defaultValue = "0") Integer queryType
    ) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //检查属性
            if (checkQueryParameter(queryActionType, queryID, rm)) {
                //用户Id
                Long userId = baseParameter.getUserId();
                //clientSign
                String clientSign = baseParameter.getClientSign();
                //车主信息费列表
                CarOwnerInfoFeeBean carOwnerInfofeeList = transportOrdersService.getCarOwnerInfofeeList(userId, queryActionType, queryID, baseParameter.getClientVersion(), queryType,clientSign);
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(carOwnerInfofeeList);


                List<InfoFeeMyPublishBubbleResultBean> bubbleResultBeans = transportOrdersService.getTradingAndFreezeBubble(userId);
                carOwnerInfofeeList.setBubbleNumbers(bubbleResultBeans);
                InfoFeeMyPublishBubbleResultBean waitPay = new InfoFeeMyPublishBubbleResultBean("1","1","1", carOwnerInfofeeList.getCarOwnerNopayInfofeeNum());
                bubbleResultBeans.add(waitPay);
                //待评价气泡封装
                InfoFeeMyPublishBubbleResultBean waitFeedBack = new InfoFeeMyPublishBubbleResultBean("1", "7", "0",
                        transportOrdersService.getUserFeedbackTodoCount(userId, 1).intValue());
                bubbleResultBeans.add(waitFeedBack);

            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * @Description  货主信息费列表接口
     * <AUTHOR>
     * @Date  2018/12/21 11:38
     * @Param [baseParameter, queryActionType, queryMenuType, queryID]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"shipperInfofeeList","shipperInfofeeList.action"})
    @ResponseBody
    public ResultMsgBean shipperInfofeeList(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID, @RequestParam(value = "costStatus" ,defaultValue = "0") Integer costStatus) {

        ResultMsgBean rm = new ResultMsgBean();
        try {
            //检查属性
            if (checkQueryParameter(queryActionType, queryID, rm)) {
                //用户Id
                Long userId = baseParameter.getUserId();
                //货主信息费列表
                ShipperInfoFeeBean shipperInfofeeList = transportOrdersService.getShipperInfofeeList(userId, queryActionType, queryID,costStatus);

                //获取用户待评价订单数量
                Integer feedBackTodoCount = feedbackUserService.getUserFeedbackTodoCount(userId, 2);
                shipperInfofeeList.setFeedBackTodoCount(feedBackTodoCount);
                //6270以下兼容实际支付金额和总金额
                try {
                    this.paseAmount(shipperInfofeeList, baseParameter.getClientVersion());
                } catch (Exception e) {
                    logger.error("checkFitError_error : ", e);
                }

                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(shipperInfofeeList);
                //清空气泡
                tytBubbleService.updateBubbleNumber(userId, "2", "7",0);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    private void paseAmount(ShipperInfoFeeBean shipperInfofeeList, String clientVersionStr) {
        if(shipperInfofeeList == null|| CollectionUtils.isEmpty(shipperInfofeeList.getShipperInfofeeList())){
            return;
        }
        if (StringUtils.isNotEmpty(clientVersionStr) && StringUtils.isNotEmpty(clientVersionStr)) {
            Integer clientVersion = Integer.parseInt(clientVersionStr);

            //兼容总金额、实际支付金额
            if(clientVersion < 6270){
                for(TransportWayBillListBean transportWayBillListBean :shipperInfofeeList.getShipperInfofeeList()){
                    for(TransportOrdersListBean transportOrdersListBean :transportWayBillListBean.getInfofeeList()){
                        transportOrdersListBean.setPayAmount(transportOrdersListBean.getTotalOrderAmount());
                    }
                }
            }
        }
    }

    /**
     * 获取车辆定位
     * @param carId
     * @return
     */
    @RequestMapping("getCarPosition")
    @ResponseBody
    public ResultMsgBean getCarPosition(Long carId){
        if (carId==null){
            return new ResultMsgBean(500,"未填写车牌号，无法查看车辆定位！");
        }
        return transportOrdersService.getCarPosition(carId);
    }

    @RequestMapping(value = {"unpaidCancel","unpaidCancel.action"})
    @ResponseBody
    public ResultMsgBean unpaidCancel(Long userId, Long orderId){
        logger.info("待支付取消入参：{}, {}", userId, orderId);
        //获取当前订单状态

        TytTransportOrders orders = transportOrdersService.getById(orderId);

        if(!Objects.equals(userId, orders.getPayUserId())){
            return new ResultMsgBean(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE, "您的订单数据错误");
        }

        if (Objects.equals(orders.getCostStatus(), 6)) {
            //货主拒绝、系统拒绝、车方取消统统都去 拒绝/取消菜单
            return new ResultMsgBean(ReturnCodeConstant.ORDER_IS_REFUSE_CODE, "您的订单已经被取消了");
        } else if (!Objects.equals(orders.getCostStatus(), 10)) {
            //同意装货，进入待装货
            return new ResultMsgBean(ReturnCodeConstant.ORDER_IS_NO_CANCEL_CODE, "您的订单无法取消");
        }

        int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
        try {
            if (LockUtil.lockObject("1", orders.getTsId() + "", redisLockTimeout) && LockUtil.lockObject("1", orders.getTsOrderNo(), redisLockTimeout)) {
                //查询优惠券信息
                PromoCouponWriteoff couponWriteoff = couponWriteoffService.getByUserIdAndOrderId(userId, orders.getId().toString());
                if(couponWriteoff != null){
                    //修改核销状态和优惠券状态为可使用
                    couponWriteoffService.updateStatus(couponWriteoff.getOrderId());
                    promoUserCouponService.updateStatus(couponWriteoff.getCouponId());
                }
                int count = transportOrdersService.unpaidCancel(userId, orderId);
                CompletableFuture.runAsync(() -> {
                    try {
                        apiTradeInfoFeeClient.cancelThirdOrder(orders).execute();
                    } catch (Exception e) {
                        logger.error("cancelThirdOrder orderId: {}", orderId, e);
                    }
                }).whenComplete((aVoid, e) -> {
                    logger.error("cancelThirdOrder orderId: {}", orderId, e);
                });
                if(count > 0){
                    return new ResultMsgBean(ReturnCodeConstant.OK, "取消成功");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            LockUtil.unLockObject("1", orders.getTsId() + "");
            LockUtil.unLockObject("1", orders.getTsOrderNo() );
        }
        return  new ResultMsgBean(ReturnCodeConstant.ERROR, "服务器异常");
    }

    @RequestMapping(value = {"setDealCar", "setDealCar.action"})
    @ResponseBody
    public ResultMsgBean setDealCar(Long id,Long tsId){
        logger.info("设置成交车辆入参：{}", id);
        try {
            ResultMsgBean resultMsgBean = transportOrdersService.updateDealCar(id,1,tsId);
            if (resultMsgBean.getCode().equals(200)){
                resultMsgBean.setMsg("已设为成交车，车/货可查看对方信息");
            }
            return resultMsgBean;
        }catch (Exception e){
            e.printStackTrace();
            return  new ResultMsgBean(ReturnCodeConstant.ERROR, "服务器异常");
        }
    }

    @RequestMapping(value = {"cancelDealCar", "cancelDealCar.action"})
    @ResponseBody
    public ResultMsgBean cancelDealCar(Long id,Long tsId){
        logger.info("设置成交车辆入参：{}", id);
        try {
            return transportOrdersService.updateDealCar(id, 0, tsId);
        }catch (Exception e){
            e.printStackTrace();
            return  new ResultMsgBean(ReturnCodeConstant.ERROR, "服务器异常");
        }
    }

    @RequestMapping("getBackendTransportPhones")
    @ResponseBody
    public ResultMsgBean getBackendTransportPhones(Long tsId){
        logger.info("查询限时货源电话：{}", tsId);
        try {
            return transportOrdersService.getBackendTransportPhones(tsId);
        }catch (Exception e){
            e.printStackTrace();
            return  new ResultMsgBean(ReturnCodeConstant.ERROR, "服务器异常");
        }
    }

    /**
     * @description 获取投诉订单列表接口
     * <AUTHOR>
     * @date 2022/12/14 13:20
     * @param baseParameter
     * @param queryID
     * @param queryActionType
     * @param queryType
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping(value = {"complaintOrdersList","complaintOrdersList.action"})
    @ResponseBody
    public ResultMsgBean complaintOrdersList(BaseParameter baseParameter,
                                         @RequestParam(value = "queryID", defaultValue = "0") Long queryID,
                                         @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType,
                                         @RequestParam(value = "queryType", defaultValue = "0") Integer queryType) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            //检查属性
            if (checkQueryParameter(queryActionType, queryID, rm)) {
                //用户Id
                Long userId = baseParameter.getUserId();
                //clientSign
                String clientSign = baseParameter.getClientSign();
                //clientVersion
                String clientVersion = baseParameter.getClientVersion();
                //获取投诉订单列表
                List <TransportOrdersListBean> complaintOrdersList = transportOrdersService.getComplaintOrdersList(userId, queryActionType, queryID, clientVersion, queryType, clientSign);
                complaintOrdersList.stream().forEach(transportOrders -> {
                    boolean isOwner = false;
                    //如果用户ID等于货方账户ID
                    if(userId.longValue() == transportOrders.getUserId().longValue()){
                        isOwner = true;
                    }
                    //订单状态描述
                    String statusDesc = InfofeeDetailServiceImpl.infoFeeStatusDesc(isOwner, transportOrders.getRefundFlag(),String.valueOf(transportOrders.getCostStatus()),
                            transportOrders.getDelayStatus(),transportOrders.getDelayRefundStatus(),transportOrders.getDeRefundDueDate());
                    transportOrders.setStatusDesc(statusDesc);
                });
                rm.setCode(ReturnCodeConstant.OK);
                rm.setMsg("查询成功");
                rm.setData(complaintOrdersList);
            }
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }
}
