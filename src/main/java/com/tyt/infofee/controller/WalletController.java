package com.tyt.infofee.controller;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.service.CardPrefixBankInfoService;
import com.tyt.infofee.service.TytUserBankcardService;
import com.tyt.infofee.service.WalletService;
import com.tyt.model.PublicResource;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserBankcard;
import com.tyt.model.TytUserSub;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.PublicResourceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import com.tyt.util.tpay.TpayUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 用户钱包的控制器
 *
 * <AUTHOR>
 * @date 2016-11-19上午10:46:59
 * @description
 */
@Controller
@RequestMapping("/plat/wallet/")
public class WalletController extends BaseController {
    private final String tpayAccountUrl = AppConfig.getProperty("tpay.account.api.url");

    @Resource(name = "walletService")
    private WalletService walletService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "cardPrefixBankInfoService")
    private CardPrefixBankInfoService cardPrefixBankInfoService;

    @Resource(name = "publicResourceService")
    private PublicResourceService publicResourceService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "tytUserBankcardService")
    private TytUserBankcardService tytUserBankcardService;

    @Resource(name = "tytConfigService")
    private TytConfigService configService;
    
    @Resource(name = "tytUserSubService")
    private TytUserSubService userSubService;
    /*
     * 余额不足
     */
    private static final int INSUFFICIENT_BALANCE = 300;
    //钱包提现每日最大次数
    public static final String WITHDRAWAL_MAX_NUM = "withdrawal_max_num";
    //钱包提现每日最大金额
    public static final String WITHDRAWAL_MAX_MONEY = "withdrawal_max_money";

	private static final String WITHDRAW_LOCK = "withdraw_lock";

    /**
     * 客户端区分新老app code值，小于等于6的为老版本客户端，大于6的为版本拆分后的新版本
     */
    private static final int CLIENT_SIGN_SEPERATOR = 6;

    private final String MERCHANT_ID = AppConfig.getProperty("tpay.merchantId");
    private final String VERSION = AppConfig.getProperty("tpay.version");


    /**
     * 我的货源列表查询接口
     *
     * @param myPubQueryBean
     * @return
     */
    @RequestMapping(value = {"remaining","remaining.action"})
    @ResponseBody
    public PocketResultBean remaining(String userId) {
        logger.info("get user wallet remaning, user id is:　" + userId);
        PocketResultBean resultMsgBean = new PocketResultBean();
        try {
            if (StringUtils.isEmpty(userId)) {
                resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                resultMsgBean.setMsg("userId不能为空");
            } else {
                // 获取用户钱包当前余额
                List<PocketBean> pocketBeanList = walletService.getAccountByUserId(userId);
                PocketBean curPocket;
                for (int i = 0; i < pocketBeanList.size(); i++) {
                	curPocket = pocketBeanList.get(i);
					if (curPocket.getAffiliatedType().intValue() == 1) {
						resultMsgBean.setRemaining(String.valueOf(curPocket.getCurrentBalance()));
					} else if (curPocket.getAffiliatedType().intValue() == 3) {
						resultMsgBean.setFrozenRemaining(String.valueOf(curPocket.getCurrentBalance()));
					}
				}
                // 获取最新的三条钱包出入账信息
                List<UserWalletFlowBean> pocketFlowBeans = walletService.getUserPocketFlowTopThree(userId);
                logger.info("get user wallet remaining result is: " + resultMsgBean + ", list is: " + pocketFlowBeans);
                //resultMsgBean.setData(pocketFlowBeans);
                //旧钱包不展示余额
                resultMsgBean = new PocketResultBean();
                resultMsgBean.setMsg("余额查询成功");
            }
        } catch (Exception e) {
            logger.error("get user wallet remaning failed, the error message is:　" + e);
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
        }
        return resultMsgBean;
    }

    /**
     * 钱包记录列表
     *
     * @param userId
     * @param currentPage
     * @return
     */
    @RequestMapping(value = {"list","list.action"})
    @ResponseBody
	public PocketResultBean list(String userId, String currentPage, String year, String month,
				String day, String type,String pageSize) {
        logger.info("get user wallet flow list, user id is:　" + userId + ", current page is: " + currentPage);
        PocketResultBean resultMsgBean = new PocketResultBean();
        resultMsgBean.setTime(System.currentTimeMillis() + "");
        try {
            if (StringUtils.isEmpty(userId)) {
                resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                resultMsgBean.setMsg("userId不能为空");
            } else if (StringUtils.isEmpty(currentPage)) {
                resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                resultMsgBean.setMsg("currentPage不能为空");
            } else {
				UserWalletFlowListBean pocketFlowListBean = walletService.getUserPocketFlow(userId,
							currentPage, year, month, day, type,pageSize);
                logger.info("get user wallet flow list result is: " + pocketFlowListBean);
                //resultMsgBean.setData(pocketFlowListBean);
                resultMsgBean.setData(null);
                resultMsgBean.setMsg("查询成功");
            }
        } catch (Exception e) {
            logger.error("get user wallet flow list failed, the error message is:　" + e);
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
        }
        return resultMsgBean;
    }

    /**
     * 钱包提现
     *
     * @param userId          提现人用户id
     * @param cardOwnerName   卡所属人姓名
     * @param cardNumber      卡号
     * @param cardDepositBank 开卡支行
     * @param drawAccount     提现金额
     * @param cardBlongedBank 银行卡所属银行
     * @return
     */
    @RequestMapping(value = "withdraw")
    @ResponseBody
    public ResultMsgBean withdraw(String userId, String cardOwnerName, String cardNumber, String cardDepositBank, String drawAccount, String cardBlongedBank,
                                  String clientSign, @RequestParam(required = false) String verificationCode,@RequestParam(required = false) String cellPhone) {
        logger.info("user wallet withdraw, user id is:　" + userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        resultMsgBean.setMsg("提现申请成功");

        try {
            //是否允许提现 如果出现钱包版本回退 讲不允许用户提现 并提示用户
            boolean unableWithdraw = TytSwitchUtil.isUnableWithdrawl();
            if(unableWithdraw){
                resultMsgBean.setCode(ReturnCodeConstant.VERSION_ERROR_CODE);
                String unableWithdrawMessage = TytSwitchUtil.getUnableWithdrawlMessage();
                resultMsgBean.setMsg(unableWithdrawMessage);
                return resultMsgBean;
            }
            //是否启用tpay收银台版本(true:是,false:否)
            boolean isTpayVersion = TytSwitchUtil.isTpayVersion();
            if(isTpayVersion){
                resultMsgBean.setCode(ReturnCodeConstant.VERSION_ERROR_CODE);
                resultMsgBean.setMsg("请使用最新版本进行操作！");
                return resultMsgBean;
            }
            if (StringUtils.isEmpty(userId)) {
                resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                resultMsgBean.setMsg("userId不能为空");
            } else if (StringUtils.isEmpty(cardOwnerName)) {
                resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                resultMsgBean.setMsg("cardOwnerName不能为空");
            } else if (StringUtils.isEmpty(cardNumber)) {
                resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                resultMsgBean.setMsg("cardNumber不能为空");
            } else if (StringUtils.isEmpty(cardDepositBank)) {
                resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                resultMsgBean.setMsg("cardDepositBank不能为空");
//            } else if (drawAccount == null || StringUtils.contains(drawAccount, ".")) {
            } else if (drawAccount == null || !NumberUtils.isNumber(drawAccount)) {
                resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                resultMsgBean.setMsg("目前提现金额只支持数字");
            } else {

                // 新版车、货app需要校验验证码
                if (CLIENT_SIGN_SEPERATOR < Integer.parseInt(clientSign)) {
                    if (StringUtils.isBlank(verificationCode)) {
                        resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                        resultMsgBean.setMsg("短信验证码不能为空");
                        return resultMsgBean;
                    }

                    String realVerifyCode = RedisUtil.get(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
                    if (!verificationCode.equals(realVerifyCode)) {
                        resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                        resultMsgBean.setMsg("短信验证码错误");
                        return resultMsgBean;
                    }

                    RedisUtil.del(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
                }

            	int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            	logger.info("user withdraw get redis lock begin, user id is: " + userId);
                if (LockUtil.lockObject("1", WITHDRAW_LOCK + "_" + userId, redisLockTimeout)) {
	            	/**
	                 * 今日可提现金额控制
                     * 程序不支持小数提现，如500.00 过滤为500，500.36 过滤为500，后续改进
                     * 原因：数据库单位存储为分，controller单位为元，service单位为分，转换位置及数字类型不一致，需重写才能支持
	                 */
                    BigDecimal bjAccount = new BigDecimal(drawAccount);
                    Integer drawAccountNew = bjAccount.intValue();
                    logger.info("userId:{}提现金额{}元，实际提现金额{}元", userId, drawAccount, drawAccountNew);
//	                Integer drawAccountNew = Integer.parseInt(drawAccount);
	                Integer monVal = configService.getIntValue("withdrawal_max_money");
	                Integer mon = RedisUtil.getObject(WITHDRAWAL_MAX_MONEY + userId);
	                if (mon != null && mon >= 0) { // 非首次
	                    Integer drawMoney = mon + drawAccountNew;
	                    if (drawMoney > monVal) {
	                        Integer tmpBalance = monVal - mon; // 今日剩余提现金额
	                        resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
	                        resultMsgBean.setMsg("今日剩余可提现金额" +tmpBalance + "元");
	                        return resultMsgBean;
	                    }
	                } else { // 首次提现
	                    if (drawAccountNew > monVal) {
	                        resultMsgBean.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
	                        resultMsgBean.setMsg("今日剩余可提现金额" +monVal + "元");
	                        return resultMsgBean;
	                    }
	                }
	
	                /*
					 * 查询用于当前的钱包余额是否足够提现
					 */
	                UserWalletBean userWalletBean = walletService.getUserWalletAccount(userId);
	                logger.info("check user wallet remaing result is: " + userWalletBean.getCurrentBalance() / 100.0 + ", withdraw amount is: " + drawAccountNew);
	                if (userWalletBean.getCurrentBalance() / 100.0 < drawAccountNew) {
	                    resultMsgBean.setCode(INSUFFICIENT_BALANCE);
	                    resultMsgBean.setMsg("余额不足");
	                } else {
	                    // 处理提现申请
	                    ShortMsgBean shortMsgBean = walletService.saveWithdraw(userId, cardOwnerName, cardNumber, cardDepositBank, drawAccountNew, cardBlongedBank);
	                    // 发送提现成功短信
	                    tytMqMessageService.sendMqMessage(shortMsgBean.getMessageSerailNum(), JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
	                    //walletService.saveSendMessage(shortMsgBean, cardBlongedBank, cardNumber, drawAccount);
	                    
	                    //TODO提现成功放缓存里  次数和金额 2018/6/12提现优化
	                    int cacheSeconds = (int) TimeUtil.getTomorrowZeroSeconds();
	                    Integer num = RedisUtil.getObject(WITHDRAWAL_MAX_NUM + userId);
	                    if (num != null && num > 0) {
	                        RedisUtil.setObject(WITHDRAWAL_MAX_NUM + userId, num + 1, cacheSeconds);
	                    } else {
	                        RedisUtil.setObject(WITHDRAWAL_MAX_NUM + userId, 1, cacheSeconds);
	                    }
	                    //金额
	                    if (mon != null && mon > 0) {
	                        RedisUtil.setObject(WITHDRAWAL_MAX_MONEY + userId, mon + drawAccountNew, cacheSeconds);
	                    } else {
	                        RedisUtil.setObject(WITHDRAWAL_MAX_MONEY + userId, drawAccountNew, cacheSeconds);
	                    }
	                }
	            }
            }
        } catch (Exception e) {
            logger.error("user wallet withdraw failed, the error message is:　" + e);
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
        } finally {
			logger.info("user withdraw release redis lock, userid is: " + userId);
		    LockUtil.unLockObject("1", WITHDRAW_LOCK + "_" + userId);
		}
        return resultMsgBean;
    }

    /**
     * 根据卡号查询对应银行名称
     *
     * @param cardId 银行卡号
     * @return
     */
    @RequestMapping(value = "getBankNameByCardId")
    @ResponseBody
    public ResultMsgBean getBankNameByCardId(String cardId) {
        logger.info("根据卡号查询对应开户行：{}", cardId);
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        resultMsgBean.setMsg("查询成功");
        resultMsgBean.setCode(ResultMsgBean.OK);
        if (StringUtils.isEmpty(cardId) || cardId.length() < 7) {
            resultMsgBean.setMsg("卡号格式错误");
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            return resultMsgBean;
        }
        String cardIdPrefix = cardId.substring(0, 6);
        //根据前缀获取缓存的银行名称
        String bankKey = Constant.CARDID_PREFIX_BANKNAME + cardIdPrefix;
        String bankName = cardPrefixBankInfoService.getCacheStringByInit(bankKey);
        logger.info("直接从缓存中获取:{}", bankName);
        if (StringUtils.isEmpty(bankName)) {
            //如果不存在则去调用阿里接口，并保存查询结果
            bankName = cardPrefixBankInfoService.addRemoteAliBankName(cardId);
            logger.info("请求阿里接口后返回:{}", bankName);
        }
        resultMsgBean.setData(bankName);
        return resultMsgBean;
    }

    /**
     * 钱包提现验证
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "VerifyWithdrawal")
    @ResponseBody
    public ResultMsgBean VerifyWithdrawal(String userId) {
        ResultMsgBean result = new ResultMsgBean();
        try {
            if (StringUtils.isEmpty(userId)) {
                result.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                result.setMsg("userId不能为空");
                return result;
            }
            //1.判断用户钱包中的余额是否满足最低提现限制
            UserWalletBean userWalletBean = walletService.getUserWalletAccount(userId);
            PublicResource resource = publicResourceService.getByKey("cashWithdrawalMinimumAmount");
            if (userWalletBean.getCurrentBalance() / 100.0 < Long.valueOf(resource.getValue())) {
                result.setCode(INSUFFICIENT_BALANCE);
                result.setMsg("余额不满足最低提现金额");
                return result;
            }
            //2.验证用户是否实名认证     照片认证标志0未认证1通过2认证中3认证失败
            Integer verifyFlag = userService.getPhotoVerifyFlag(Long.valueOf(userId));
            if (verifyFlag != 1) {
                result.setCode(202);
                result.setMsg("未实名认证");
                return result;
            }
            //3.验证用户是否超过每日最大提现次数/
            Integer num = RedisUtil.getObject(WITHDRAWAL_MAX_NUM + userId);
            if (num != null && num > 0) {
                Integer numVal = configService.getIntValue("withdrawal_max_num");
                if (num >= numVal) {
                    String numStr = configService.getStringValue("withdrawal_over_max_num_hint");
                    result.setCode(203);
                    result.setMsg(numStr);
                    return result;
                } else {
                    //4.验证用户是否超过每日最大提现金额
                    Integer mon = RedisUtil.getObject(WITHDRAWAL_MAX_MONEY + userId);
                    if (mon != null && mon >= 0) {
                        Integer monVal = configService.getIntValue("withdrawal_max_money");
                        if (mon >= monVal) {
                            String monStr = configService.getStringValue("withdrawal_over_max_money_hint");
                            result.setCode(204);
                            result.setMsg(monStr);
                            return result;
                        }
                    }
                }
            }
            //5.验证用户是否绑定银行卡
            List<TytUserBankcard> list = tytUserBankcardService.getByUserId(Long.valueOf(userId));
            if (list != null && list.size() > 0) {
                result.setCode(200);
                result.setMsg("用户存在银行卡信息");
            } else {
                result.setCode(201);
                result.setMsg("用户不存在银行卡信息");
            }
        } catch (Exception e) {
            result.setCode(500);
            result.setMsg("服务器错误");
            e.printStackTrace();
        }
        return result;

    }
    
    @RequestMapping(value = "/setupPwd")
    @ResponseBody
    public ResultMsgBean setupPwd(String userId, Integer type, String pwd) {
        logger.info("setup pocket password, user id is:　" + userId);
        ResultMsgBean rm = new ResultMsgBean();
        rm.setMsg("修改成功");
        try {
            //是否启用tpay收银台版本(true:是,false:否)
            boolean isTpayVersion = TytSwitchUtil.isTpayVersion();
            if(isTpayVersion){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("请使用最新版本进行操作！");
                return rm;
            }
            if (StringUtils.isEmpty(userId)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("userId不能为空");
            } else if (type == null || (type.intValue() != 1 && type.intValue() != 2 && type.intValue() != 3)) {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("type值不合法");
            } else {
            	walletService.updatePocketPwd(userId, type, pwd, rm);
            }
        } catch (Exception e) {
            logger.error("user wallet withdraw failed, the error message is:　" + e);
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("失败");
        }
        return rm;
    }
    
    @RequestMapping(value = "/verifyPwd")
    @ResponseBody
    public ResultMsgBean verifyPwd(String userId, String pwd) {
        logger.info("verifyPwd user id is:　" + userId);
        ResultMsgBean rm = new ResultMsgBean();
        rm.setMsg("密码正确");
        try {
        	 if (StringUtils.isEmpty(userId)) {
                 rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                 rm.setMsg("userId不能为空");
			} else {
				// 查询用户信息
				TytUserSub userSub = userSubService.getTytUserSubByUserId(Long.valueOf(userId));
				if (userSub.getPocketPwdStatus() == null || userSub.getPocketPwdStatus().intValue() == 1) {
	                 rm.setCode(300);
	                 rm.setMsg("请先设置密码");
				} else {
					if (userSub.getPocketPwd().equalsIgnoreCase(MD5Util.GetMD5Code(pwd + userId))) {
		                 rm.setCode(200);
					} else {
						rm.setCode(400);
						rm.setMsg("钱包密码错误，请重新输入");
					}
				}
			}
        } catch (Exception e) {
            logger.error("verifyPwd user id is:　" + userId + ", the error message is: " + e);
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("失败");
        }
        return rm;
    }

    //获取账单
    @RequestMapping(value = "/findBalance")
    @ResponseBody
    public ResultMsgBean findBalance(String userId) {
        logger.info("verifyPwd user id is:　" + userId);
        ResultMsgBean rm = new ResultMsgBean();
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("userId", userId);
            paramMap.put("timestamp",System.currentTimeMillis()+"");
            paramMap.put("merchantId",MERCHANT_ID);
            paramMap.put("version",VERSION);

            String result = TpayUtil.sendBodyRequest(tpayAccountUrl + "/account/info/findBalanceByUserId", paramMap);
            ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("verifyPwd user id is:　" + userId + ", the error message is: " + e);
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("失败");
        }
        return rm;
    }

    /**
     * 账单
     * @param userId
     * @param currentPage
     * @return
     */
    @RequestMapping(value = "queryUserBill")
    @ResponseBody
    public ResultMsgBean queryUserBill(String userId, Integer currentPage,Integer pageSize, String year, String month, String type) {
        try {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("userId", userId);
            paramMap.put("currentPage", currentPage);
            paramMap.put("pageSize", pageSize);
            if(StringUtils.isNotBlank(year) && StringUtils.isNotBlank(month)){
                paramMap.put("year", year);
                paramMap.put("month",month);
            }
            paramMap.put("type",type);
            String result = HttpUtil.get(tpayAccountUrl + "/account/platformTransactionFlow/queryUserBill", paramMap);
            ResultMsgBean resultMsgBean = JSON.parseObject(result, ResultMsgBean.class);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("获取账单出错：", e);
            ResultMsgBean resultMsgBean = new ResultMsgBean();
            resultMsgBean.setCode(500);
            resultMsgBean.setMsg("获取账单出错");
            return resultMsgBean;
        }
    }
}
