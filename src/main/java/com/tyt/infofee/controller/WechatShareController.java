package com.tyt.infofee.controller;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import com.tyt.infofee.service.WechatShareService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.infofee.bean.WechatShareTransportBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.querybean.SourceBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytSourceService;
import com.tyt.user.service.UserService;
import com.tyt.util.XXTea;


@Controller
@RequestMapping("/plat/transport/wechatShare")
public class WechatShareController extends BaseController{

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;
	@Resource(name = "userService")
	private UserService userService;
	@Resource(name = "tytConfigService")
	private TytConfigService configService;
	@Resource(name = "tytSourceService")
	private TytSourceService sourceService;
	@Autowired
	private WechatShareService wechatShareService;
	/**
	 * 获取微信分享信息
	 * @param userId
	 * @param goodsId
	 * @return
	 */
	@RequestMapping(value = "/getShareDetail")
	@ResponseBody
	public ResultMsgBean getShareDetail(Long userId,Long goodsId) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			if(goodsId==null || userId==null ) {
				result.setCode(201);
				result.setMsg("货源ID或用户ID参数缺失");
				return result;
			}
			//1.根据货物id查找货源
			TransportMain main = transportMainService.getById(goodsId);
			if(main!=null) {
				String title=String.format("%s:%s→%s" ,"找板车",main.getStartPoint(),main.getDestPoint());
				String con="";
				if(StringUtils.isNotBlank(main.getPrice())) {
					con=main.getTaskContent()+"，"+main.getPrice()+"元";
				}else {
					con=main.getTaskContent();
				}
				String content=String.format("%s。%s", con,"急走，速联！");
				Integer IsOwnerGoods=2;//是否是货主货源  1是货主货源 2 不是
				if(userId.intValue()==main.getUserId().intValue()) {
					IsOwnerGoods=1;
				}
				String h5Url = configService.getStringValue("wechat_share_h5_url");
				String encryptGoodsId=XXTea.Encrypt(goodsId+","+IsOwnerGoods+"", AppConfig.getProperty("tyt.xxtea.key"));

				WechatShareTransportBean share=new WechatShareTransportBean();
				share.setShareUrl(h5Url+"?tk="+encryptGoodsId);
				share.setShareTitle(title);
				share.setShareContent(content);
				share.setIconUrl("http://www.teyuntong.com/app/image_logo/logo120.png");
				result.setData(share);
				result.setCode(200);
				result.setMsg("分享详情获取成功");
			}else {
				result.setCode(201);
				result.setMsg("货源不存在");
			}
		} catch (Exception e) {
			result.setCode(500);
			result.setMsg("分享信息获取失败");
		}
		return result;

	}

	/**
	 * 新版获取分享信息，支持H5、小程序
	 * @param userId
	 * @param goodsId
	 * @param target target 0 =微信好友 1=朋友圈
	 * @param clientSign Android 车21  货22 iOS车 31  货 32
	 * @return
	 */
	@RequestMapping(value = "/getShareInfo")
	@ResponseBody
	public ResultMsgBean getShareInfo(Long userId,Long goodsId,Integer target,String clientSign,String clientVersion) {
		if (goodsId == null || userId == null || org.apache.commons.lang3.StringUtils.isEmpty(clientSign)||Objects.isNull(target)) {
			return ResultMsgBean.failResponse(201, "缺少必要参数");
		}
		logger.info("新版获取分享信息，支持H5、小程序，userId={},goodsId={},target={},clientSign={},clientVersion={}", userId, goodsId, target, clientSign,clientVersion);
		//1.根据货物id查找货源
		TransportMain main = transportMainService.getById(goodsId);
		if (Objects.isNull(main)) {
			return ResultMsgBean.failResponse(201, "货源不存在");
		}
		WechatShareTransportBean shareInfo = wechatShareService.getShareInfo(userId, main, target, clientSign,clientVersion);
		return ResultMsgBean.successResponse(shareInfo);
	}

	/**
	 * h5页面获取货物详情
	 * @param userId
	 * @param goodsId
	 * @return
	 */
	@RequestMapping(value = "/getGoodsDetail.action")
	@ResponseBody
	public ResultMsgBean getGoodsDetail(String tk) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			if(StringUtils.isBlank(tk)) {
				result.setCode(500);
				result.setMsg("加密货源id为空");
				return result;
			}
			//1.解析加密串 加密串格式为  （货源id,是否是货主货源）-- （37888299,1）
			String val = XXTea.Decrypt(tk, AppConfig.getProperty("tyt.xxtea.key"));
			String[] valStr = val.split(",");
			Long goodsId = null;
			Integer isOwnerGoods = null;
			for (int i = 0; i < valStr.length; i++) {
				goodsId=Long.valueOf(valStr[0].trim());
				isOwnerGoods=Integer.valueOf(valStr[1].trim());
			}
			//2.根据货源id获取详情，
			if(goodsId!=null && goodsId>0) {
				WechatShareTransportBean share=transportMainService.getWechatShareDetail(goodsId);
				share.setIsOwnerGoods(isOwnerGoods);
				//3.如果isOwnerGoods=1是货主货源isOwnerGoods=2不是货主货源 （不是货主货源不显示电话）
				Integer showTel = configService.getIntValue("wechat_share_show_owner_callphone", 1);
				if(isOwnerGoods==null || isOwnerGoods==2) {
					share.setTel("");
					share.setTel3("");
					share.setTel4("");
				}else {
					//如果是货主货源 确定开关是否为开， 1：开  2：关（开关为2 不显示电话）
					if(showTel==2) {
						share.setTel("");
						share.setTel3("");
						share.setTel4("");
					}
				}
				//用户注册身份
				if (share.getUserClass() != null && share.getIdentityType() != null) {
					List<SourceBean> list = sourceService.getByGroupCodeWithNoLimit("user_identity_type"+"_"+share.getUserClass());
					for (SourceBean sourceBean : list) {
						if(Integer.valueOf(sourceBean.getValue())==share.getIdentityType()) {
							share.setIdentityTypeName(sourceBean.getName());
						}
					}
				}
				result.setData(share);
				result.setCode(200);
				result.setMsg("货源详情查询成功");
			}
		} catch (Exception e) {
			result.setCode(500);
			result.setMsg("货源详情获取失败");
		}
		return result;

	}
}
