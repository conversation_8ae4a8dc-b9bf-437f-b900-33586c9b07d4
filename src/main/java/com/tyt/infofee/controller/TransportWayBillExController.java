package com.tyt.infofee.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.infofee.bean.ListDataBean;
import com.tyt.infofee.bean.MyComplaintListBean;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.infofee.service.TransportWayBillService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.TytTransportWaybill;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.PropertiesFileUtil;
import com.tyt.util.ReturnCodeConstant;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/plat/infoFee/ex/")
public class TransportWayBillExController extends BaseController {
	@Resource(name = "transportWayBillExService")
	private TransportWayBillExService transportWayBillExService;
	@Resource(name = "transportWayBillService")
	TransportWayBillService transportWayBillService;
	@Resource(name = "transportService")
	private TransportService transportService;
	@Resource(name = "tytConfigService")
	private TytConfigService configService;
	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;
	private PropertiesFileUtil propertiesFileUtil = PropertiesFileUtil.init("message");

	/**
	 * 违约异常上报列表查询接口（我的货源/已接单共用）
	 *
	 * @param baseParameter
	 * @param queryID
	 *            下拉是0，上滑是最小的ID；（首次queryID=0）
	 * @param queryMenuType
	 *            1车主上报，2货主上报
	 * @param queryActionType
	 *            1下拉，2上滑；（首次queryActionType=1）
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = "list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
		ResultMsgBean rm = new ResultMsgBean();

		try {
			if (null == queryMenuType || queryMenuType.intValue() < 1 || queryMenuType.intValue() > 2) {

				rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
				rm.setMsg("状态不在范围");
				return rm;
			}
			// 检查属性
			if (checkQueryParameter(queryActionType, queryID, rm)) {
				ListDataBean listDataBean = transportWayBillExService.updateGetMyWayBillExList(baseParameter.getUserId(), queryActionType.intValue(), queryMenuType.intValue(), queryID.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(listDataBean);
				rm.setTotalSize(listDataBean == null ? 0l : listDataBean.getData() == null ? 0l : listDataBean.getData().size());

			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 违约异常上报列表查询接口（我的货源/已接单共用）
	 *
	 * @param baseParameter
	 * @param queryID
	 *            下拉是0，上滑是最小的ID；（首次queryID=0）
	 * @param queryMenuType
	 *            1车主上报，2货主上报
	 * @param queryActionType
	 *            1下拉，2上滑；（首次queryActionType=1）
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = "merge/list")
	@ResponseBody
	public ResultMsgBean mergelist(BaseParameter baseParameter,
							  @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType,
							  @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType,
							  @RequestParam(value = "queryID", defaultValue = "0") Long queryID,
							  @RequestParam(value = "queryCID", defaultValue = "0") Long queryCID
	) {
		ResultMsgBean rm = new ResultMsgBean();

		try {
			if (null == queryMenuType || queryMenuType.intValue() < 1 || queryMenuType.intValue() > 2) {

				rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
				rm.setMsg("状态不在范围");
				return rm;
			}
			// 检查属性
			if (checkQueryParameter(queryActionType, queryID, rm)) {
				ListDataBean listDataBean = transportWayBillExService.updateGetMergeExList(baseParameter.getUserId(), queryActionType.intValue(), queryMenuType.intValue(), queryID.longValue(), queryCID.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(listDataBean);
				rm.setTotalSize(listDataBean == null ? 0l : listDataBean.getData() == null ? 0l : listDataBean.getData().size());

			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private boolean checkQueryParameter(Integer queryActionType, Long queryID, ResultMsgBean rm) {
		if (queryActionType.intValue() < 1 || queryActionType.intValue() > 2) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询类型不正确！");
			return false;
		} else if (queryID.longValue() < 0) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询标识错误，最大、最小ID为空！");
			return false;
		}
		return true;

	}

	/**
	 * 俣存异常上报信息
	 *
	 * @param baseParameter
	 * @param tsOrderNo
	 * @param exParty
	 * @param exType
	 * @param exOther
	 * @return
	 */
	@RequestMapping(value = "save")
	@ResponseBody
	public ResultMsgBean save(BaseParameter baseParameter, String tsOrderNo, String exParty, String exType, String exOther) {
		ResultMsgBean rm = new ResultMsgBean();
		TransportMain transport = null;
		try {
			if (null == tsOrderNo || "".equals(tsOrderNo.trim())) {
				rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
				rm.setMsg("运单号不能空");
				return rm;
			}
			if (null == exParty || (!"1".equals(exParty) && !"2".equals(exParty))) {

				rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
				rm.setMsg("异常上报方身份错误");
				return rm;
			}
			if ("1".equals(exParty)) {

				if (Integer.parseInt(exType) < 1 || Integer.parseInt(exType) > 8) {
					rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
					rm.setMsg("异常上报类型错误");
					return rm;
				}
				if ("8".equals(exType) && (null == exOther || "".equals(exOther.trim()))) {
					rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
					rm.setMsg("异常上报其他信息不能为空");
					return rm;
				}
			} else {
				if (Integer.parseInt(exType) < 1 || Integer.parseInt(exType) > 2) {
					rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
					rm.setMsg("异常上报类型错误");
					return rm;
				}
				if ("2".equals(exType) && (null == exOther || "".equals(exOther.trim()))) {
					rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
					rm.setMsg("异常上报其他信息不能为空");
					return rm;
				}
			}
			TytTransportWaybill transportWayBill = transportWayBillService.getTytTransportWaybillForLock(tsOrderNo);
			transport = transportMainService.getById(transportWayBill.getTsId());
			logger.info("transport waybill ex get redis lock begin, src msg id is: " + transport.getSrcMsgId());
			int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
			if (LockUtil.lockObject("1", transport.getSrcMsgId() + "", redisLockTimeout) && LockUtil.lockObject("1", tsOrderNo, redisLockTimeout)) {
				logger.info("transport waybill ex get redis lock successed, src msg id is: " + transport.getSrcMsgId());
				int e = transportWayBillExService.save(baseParameter.getUserId(), tsOrderNo, exParty, exType, exOther, transportWayBill, transport.getSrcMsgId());
				switch (e) {
				case 210901:
					rm.setCode(e);
					rm.setMsg(propertiesFileUtil.getString("tyt.infofee.way.bill.ex.error.210901"));
					break;
				case 210902:
					rm.setCode(e);
					rm.setMsg(propertiesFileUtil.getString("tyt.infofee.way.bill.ex.error.210902"));
					break;
				case 210903:
					rm.setCode(e);
					rm.setMsg(propertiesFileUtil.getString("tyt.infofee.way.bill.ex.error.210903"));
					break;
				case 500:
					logger.info(tsOrderNo + "运单不存在");
					rm.setCode(ReturnCodeConstant.ERROR);
					rm.setMsg("服务器错误");
					break;
				case 501:
					rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
					rm.setMsg("运单不是装货中状态，不能进行异常上报");
					break;
				default:
					rm.setCode(ReturnCodeConstant.OK);
					rm.setMsg("操作成功");
					break;
				}
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		} finally {
			if (transport != null) {
				logger.info("transport waybill ex release redis lock successed, src msg id is: " + transport.getSrcMsgId());
				LockUtil.unLockObject("1", transport.getSrcMsgId() + "");
				LockUtil.unLockObject("1", tsOrderNo);
			}
		}
		return rm;

	}
	/**
	 * 违约异常上报列表查询接口(PC)（我的货源/已接单共用）
	 *
	 * @param baseParameter
	 * @param queryID
	 *            下拉是0，上滑是最小的ID；（首次queryID=0）
	 * @param queryMenuType
	 *            1车主上报，2货主上报
	 * @param queryActionType
	 *            1下拉，2上滑；（首次queryActionType=1）
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = "list.action")
	@ResponseBody
	public ResultMsgBean listForPc(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
		ResultMsgBean bean=this.list(baseParameter, queryActionType, queryMenuType, queryID);
		return bean;
	}

	/**
	 * 保存异常上报信息（PC）
	 *
	 * @param baseParameter
	 * @param tsOrderNo
	 * @param exParty
	 * @param exType
	 * @param exOther
	 * @return
	 */
	@RequestMapping(value = "save.action")
	@ResponseBody
	public ResultMsgBean saveForPc(BaseParameter baseParameter, String tsOrderNo, String exParty, String exType, String exOther) {
		ResultMsgBean bean=this.save(baseParameter, tsOrderNo, exParty, exType, exOther);
		return bean;
	}

    /**
     * 投诉记录列表
     */
    @GetMapping(value = "/complaint/list/my")
    @ResponseBody
    public ResultMsgBean getMyComplaintList(BaseParameter baseParameter, Integer page, Integer size) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (page == null || page <= 0) {
                page = 1;
            }

            if (size == null || size > 20) {
                size = 20;
            }
            List<MyComplaintListBean> result = transportWayBillExService.getMyComplaintList(
					baseParameter, page,
                    size);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("查询成功");
            rm.setData(result);
            rm.setTotalSize((long) result.size());
        } catch (Exception ex) {
            logger.error("服务器异常", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }
}
