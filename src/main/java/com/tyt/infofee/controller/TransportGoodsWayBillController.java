package com.tyt.infofee.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.ListDataBean;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqWaybillFinishMessageBean;
import com.tyt.infofee.bean.plat.PriceReq;
import com.tyt.infofee.enums.AssignOrderTypeEnum;
import com.tyt.infofee.enums.SourceTypeEnum;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillService;
import com.tyt.model.*;
import com.tyt.plat.enums.PriceSourceEnum;
import com.tyt.plat.utils.NumberConvertUtil;
import com.tyt.plat.vo.remote.CarryPriceReq;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.transport.service.BsPublishTransportService;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportService;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.service.PublicResourceService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Map;

@Controller
@RequestMapping("/plat/infoFee/wayBill/")
public class TransportGoodsWayBillController extends BaseController {

	private static final PropertiesFileUtil propertiesFileUtil = PropertiesFileUtil.init("message");

	@Resource(name = "transportWayBillService")
	private TransportWayBillService transportWayBillService;

	@Resource(name = "transportService")
	private TransportService transportService;

	@Resource(name = "userService")
	private UserService userService;

	@Resource(name = "infoFeeBusinessService")
	InfoFeeBusinessService infoFeeBusinessService;

	@Resource(name = "tytMqMessageService")
	TytMqMessageService tytMqMessageService;

	@Resource(name = "tytConfigService")
	private TytConfigService configService;

	@Resource(name = "transportBusiness")
	private TransportBusinessInterface transportBusiness;

	@Resource(name = "transportOrdersService")
	private TransportOrdersService transportOrdersService;

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	@Resource(name = "bsPublishTransportService")
	private BsPublishTransportService bsPublishTransportService;

	@Resource
	PublicResourceService publicResourceService;

	/**
	 * 我的货源-待同意/装货中/装货完成运单列表查询
	 *
	 * @param baseParameter
	 * @param queryID
	 *            下拉是最大的sortId，上滑是最小的ID；（首次queryID=0）
	 * @param queryMenuType
	 *            1待同意2装货中3装货完成
	 * @param queryActionType
	 *            1下拉，2上滑；（首次queryActionType=1）
	 * @return ResultMsgBean
	 */
	@RequestMapping(value = "myList")
	@ResponseBody
	public ResultMsgBean myList(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
		ResultMsgBean rm = new ResultMsgBean();

		try {
			if (null == queryMenuType || queryMenuType.intValue() < 1 || queryMenuType.intValue() > 3) {

				rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
				rm.setMsg("状态不在范围");
				return rm;
			}
			// 检查属性
			if (checkQueryParameter(queryActionType, queryID, rm)) {
				ListDataBean listDataBean = transportWayBillService.updateGetMyWayBillList(baseParameter.getUserId(), queryActionType.intValue(), queryMenuType.intValue(), queryID.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(listDataBean);
				rm.setTotalSize(listDataBean == null ? 0l : listDataBean.getData() == null ? 0l : listDataBean.getData().size());

			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private boolean checkQueryParameter(Integer queryActionType, Long queryID, ResultMsgBean rm) {
		if (queryActionType.intValue() < 1 || queryActionType.intValue() > 2) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询类型不正确！");
			return false;
		} else if (queryID.longValue() < 0) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询标识错误，最大、最小ID为空！");
			return false;
		}
		return true;

	}

	/**
	 * 车主支付信息费（详情页面支付），保存运单表
	 * saveWayBillBean
	 * @param userId 用户ID
	 * @param goodsId 必填 Long 货物信息ID
	 * @param telephone 必填 String 联系电话
	 * @param agencyMoney 必填 Integer 运前信息费（单位元）
	 * @param carOwnerPayType 必填 Integer 1详情页面的支付
	 * @param carriageFee 运费（单位元）
	 * @param infoFeeCarRequest 支付信息费时，所选择的车辆
	 * @param zeroAssignOrder 是否是0元指派单0非 1是
	 * @return
	 */
	@RequestMapping(value = "saveWayBill")
	@ResponseBody
	public ResultMsgBean saveWayBill(Long userId, Long goodsId, String telephone, Long agencyMoney, Integer carOwnerPayType,
									 Integer carriageFee, CarSaveBean infoFeeCarRequest, Integer couponId, Integer driverId,
									 @RequestParam(value = "tecServiceFee", defaultValue = "0") Long tecServiceFee,
									 @RequestParam(value = "promotionPrice", defaultValue = "0") Double promotionPrice,
									 @RequestParam(value = "strikeThroughPrice", defaultValue = "0") Double strikeThroughPrice,
									 @RequestParam(value = "zeroAssignOrder", defaultValue = "0") Integer zeroAssignOrder,
									 String clientVersion) {
		logger.info("userId is {},carOwnerPayType is {},goodsId is {},agencyMoney is {},carriageFee is {},infoFeeCarRequest is {} zeroAssignOrder is {}",userId,carOwnerPayType,goodsId,agencyMoney,carriageFee,JSON.toJSONString(infoFeeCarRequest),zeroAssignOrder);
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
		long t1 = System.currentTimeMillis();
		Transport transport = null;
		try {
			// 必填项验证
			User payUser = userService.getByUserId(userId);
			if (payUser == null) {
				resultMsgBean.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
				resultMsgBean.setMsg("userId代表的对象不存在");
				return resultMsgBean;
			}
			if (goodsId == null || goodsId <= 0) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				resultMsgBean.setMsg("goodsId参数错误");
				return resultMsgBean;
			}
			if (telephone == null || telephone.trim().isEmpty()) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				resultMsgBean.setMsg("telephone参数错误");
				return resultMsgBean;
			}
			if (agencyMoney == null || agencyMoney < 0) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				resultMsgBean.setMsg("agencyMoney参数错误");
				return resultMsgBean;
			}
			//非开票0元指派单时 不可以同时为空
			if(Objects.equal(AssignOrderTypeEnum.no_assign_order.getCode(),zeroAssignOrder)){
				if (agencyMoney <= 0 && (tecServiceFee == null || tecServiceFee <= 0)) {
					resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
					resultMsgBean.setMsg("订金金额和技术服务费不能同时为0");
					return resultMsgBean;
				}
			}
			logger.info("transport good waybill goodId is: " + goodsId);
			// tyt_plat_transport_optimize20171123 货源优化代码
			transport = transportBusiness.getByGoodsId(goodsId);
			// 在操作之前先上redis锁
			if (transport != null) {
				if(Objects.equal(SourceTypeEnum.普通货主.getId(),transport.getSourceType())&&Objects.equal(AssignOrderTypeEnum.no_assign_order.getCode(),zeroAssignOrder)){
					Integer infoFeePaymentMinAmount = configService.getIntValue(Constant.INFO_FEE_PAYMENT_MIN_AMOUNT, 50);
					if(agencyMoney<infoFeePaymentMinAmount){
						resultMsgBean.setCode(ReturnCodeConstant.ERROR);
						resultMsgBean.setMsg("订金金额不能小于"+infoFeePaymentMinAmount+"元");
						return resultMsgBean;
					}
				}

				PriceReq priceReq = new PriceReq();
				priceReq.setPromotionPrice(promotionPrice);
				priceReq.setStrikeThroughPrice(strikeThroughPrice);
				ThreadLocalUtil.set(priceReq);
				int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
				logger.info("transport good waybill get redis lock begin, src msg id is: " + transport.getSrcMsgId());
				if (LockUtil.lockObject("1", transport.getSrcMsgId() + "", redisLockTimeout)) {
					logger.info("transport good waybill get redis lock successed, src msg id is: " + transport.getSrcMsgId());
					// 添加或者修改运单表，修改货物信息，同时添加数据到接单表
					resultMsgBean = infoFeeBusinessService.savePayOrderBusiness(zeroAssignOrder,goodsId, telephone , carriageFee, agencyMoney, payUser, resultMsgBean, transport.getSrcMsgId(), infoFeeCarRequest, couponId, tecServiceFee, driverId, clientVersion);

				}
			} else {
				logger.info("transport good waybill get redis lock timeout");
				resultMsgBean.setCode(ReturnCodeConstant.ERROR);
				resultMsgBean.setMsg("失败");
			}
			return resultMsgBean;
		} catch (Exception e) {
		    logger.info("支付订金发生错误：", e);
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("支付订金失败");
			return resultMsgBean;
		} finally {
			long t2 = System.currentTimeMillis();
			logger.info("支付运前信息费时间总耗时【{}】ms,参数userId【{}】,goodsId【{}】,telephone【{}】,agencyMoney【{}】,carOwnerPayType【{}】", t2 - t1, userId, goodsId, telephone, agencyMoney, carOwnerPayType);
			if (transport != null) {
				// 释放redis锁
				logger.info("transport good waybill release redis lock, src msg id is: " + transport.getSrcMsgId());
				LockUtil.unLockObject("1", transport.getSrcMsgId() + "");
			}
			ThreadLocalUtil.remove();
		}
	}

	@RequestMapping(value = "finish")
	@ResponseBody
	public ResultMsgBean finish(BaseParameter baseParameter, String tsOrderNo) {
		ResultMsgBean rm = new ResultMsgBean();
		TransportMain transport = null;
		try {
			if (null == tsOrderNo || tsOrderNo.equals("")) {
				rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
				rm.setMsg("运单号不能为空");
				return rm;
			}
			// tyt_plat_transport_optimize20171123 货源信息优化
			TytTransportWaybill transportWayBill = transportWayBillService.getById(tsOrderNo);
			TytTransportOrders tytTransportOrders = transportOrdersService.getTytTransportOrders(tsOrderNo, "4", transportWayBill.getPayUserId());
			if(tytTransportOrders == null) { // 20180121,电子合同开发时，发现以前异常上报，页面未刷新时操作装货完成出现的bug
				rm.setCode(211001);
				rm.setMsg(propertiesFileUtil.getString("tyt.infofee.way.bill.error.211001"));
				return rm;
			}
			transport = transportMainService.getById(tytTransportOrders.getTsId());
			int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
			logger.info("transport good waybill finish get redis lock begin, src msg id is: " + transport.getSrcMsgId());
			if (LockUtil.lockObject("1", transport.getSrcMsgId() + "", redisLockTimeout) && LockUtil.lockObject("1", tsOrderNo, redisLockTimeout)) {
				logger.info("transport good waybill finish get redis lock successed, src msg id is: " + transport.getSrcMsgId());
				Map<String, Object> map = transportWayBillService.updateWayBillFinish(baseParameter.getUserId(), tsOrderNo, transportWayBill, tytTransportOrders, transport.getSrcMsgId());
				int n = Integer.parseInt(map.get("code").toString());
				switch (n) {
				case 500:
					rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
					rm.setMsg("运单不存在");
					break;
				case 501:
					rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
					rm.setMsg("运单不是装货中状态，不能操作装货完成");
					break;
				case 211001:
					rm.setCode(n);
					rm.setMsg(propertiesFileUtil.getString("tyt.infofee.way.bill.error.211001"));
					break;
				default:
					MqWaybillFinishMessageBean waybillFinishMessageBean = (MqWaybillFinishMessageBean) map.get("mqBean");
					tytMqMessageService.sendMqMessage(waybillFinishMessageBean.getMessageSerailNum(), JSON.toJSONString(waybillFinishMessageBean), MqBaseMessageBean.MESSAGETYPE_AGREE_LOADING);
					rm.setCode(ReturnCodeConstant.OK);
					rm.setMsg("操作成功");
					break;
				}
			} else {
				logger.info("transport good waybill get redis lock timeout");
				rm.setCode(ReturnCodeConstant.ERROR);
				rm.setMsg("失败");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		} finally {
			if (transport != null) {
				logger.info("transport good waybill finish release redis lock, src msg id is: " + transport.getSrcMsgId());
				LockUtil.unLockObject("1", transport.getSrcMsgId() + "");
				LockUtil.unLockObject("1", tsOrderNo);
			}
		}
		return rm;
	}

	/**
	 * 为管理端调用，避免 userId错误成公司账户
	 * 
	 * @param carUserId
	 * @param goodsId
	 * @param telephone
	 * @param agencyMoney
	 * @param carOwnerPayType
	 * @return
	 */
	@RequestMapping(value = "saveWayBillForManage")
	@ResponseBody
	public ResultMsgBean saveWayBillForManage(Long carUserId, Long goodsId, String telephone, Long agencyMoney, Integer carOwnerPayType, CarSaveBean infoFeeCarRequest, Integer couponId) {
		return this.saveWayBill(carUserId, goodsId, telephone, agencyMoney, carOwnerPayType,null, infoFeeCarRequest, couponId, null,null, 0.0,0.0,0,null);
	}
	
	/**
	 * PC装货完成接口
	 * @param baseParameter
	 * @param tsOrderNo  运单号
	 * @return
	 */
	@RequestMapping(value = "finish.action")
	@ResponseBody
	public ResultMsgBean finishForPc(BaseParameter baseParameter, String tsOrderNo) {
		ResultMsgBean bean = this.finish(baseParameter, tsOrderNo);
		return bean;
	}
	
	@RequestMapping(value = "myList.action")
	@ResponseBody
	public ResultMsgBean myListAction(BaseParameter baseParameter, @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType, @RequestParam(value = "queryMenuType", defaultValue = "1") Integer queryMenuType, @RequestParam(value = "queryID", defaultValue = "0") Long queryID) {
		return this.myList(baseParameter, queryActionType, queryMenuType, queryID);
	}

	/**
	 * @description 获取运价信息接口
	 * <AUTHOR>
	 * @date 2022/6/28 17:22
	 * @param baseParameter
	 * @param goodsId
	 * @return com.tyt.model.ResultMsgBean
	 */
	@RequestMapping(value = "getCarryPrice")
	@ResponseBody
	public ResultMsgBean getCarryPrice(BaseParameter baseParameter, @RequestParam(value = "goodsId") Long goodsId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			int minPrice = 100;
			int maxPrice = 999999;
			//查询货源信息
			TransportMain transportMain = transportMainService.getTransportMainForId(goodsId);
			//远程调用运价对象
			CarryPriceVo carryPriceVo = new CarryPriceVo();
			//如果货源信息不为空
			if(transportMain != null){
				CarryPriceReq carryPriceReq = new CarryPriceReq();
				carryPriceReq.setStartProvince(transportMain.getStartProvinc());
				carryPriceReq.setStartCity(transportMain.getStartCity());
				carryPriceReq.setStartArea(transportMain.getStartArea());
				carryPriceReq.setDestProvince(transportMain.getDestProvinc());
				carryPriceReq.setDestCity(transportMain.getDestCity());
				carryPriceReq.setDestArea(transportMain.getDestArea());
				carryPriceReq.setGoodsName(transportMain.getTaskContent());
				carryPriceReq.setGoodsWeight(NumberConvertUtil.strToDouble(transportMain.getWeight()));
				carryPriceReq.setGoodsLength(NumberConvertUtil.strToDouble(transportMain.getLength()));
				carryPriceReq.setGoodsWide(NumberConvertUtil.strToDouble(transportMain.getWide()));
				carryPriceReq.setGoodsHigh(NumberConvertUtil.strToDouble(transportMain.getHigh()));
				carryPriceReq.setPublishType(transportMain.getPublishType());
				carryPriceReq.setSource(PriceSourceEnum.pay.getSource());
				carryPriceReq.setUserId(baseParameter.getUserId());

				carryPriceReq.setSrcMsgId(transportMain.getSrcMsgId());
				carryPriceReq.setDistance(transportMain.getDistance());

				//请求数据部门的运价信息
				carryPriceVo = bsPublishTransportService.reqCarryPrice(carryPriceReq);
				if(carryPriceVo != null){
					Integer thMinPrice = carryPriceVo.getThMinPrice();
					Integer thMaxPrice = carryPriceVo.getThMaxPrice();
					logger.info("minPrice : {},maxPrice : {}", thMinPrice, thMaxPrice);
					if(thMinPrice != null && thMinPrice > minPrice){
						minPrice = thMinPrice;
					}
					if(thMaxPrice != null && thMaxPrice < maxPrice){
						maxPrice = thMaxPrice;
					}
				}else{
					carryPriceVo = new CarryPriceVo();
				}
			}
			carryPriceVo.setThMinPrice(minPrice);
			carryPriceVo.setThMaxPrice(maxPrice);
			//信息费运价校验开关(1:开 2:关)
			Integer infofeeCarryPriceEnable = configService.getIntValue("infofee_carry_price_enable", 2);
			carryPriceVo.setInfofeeCarryPriceEnable(infofeeCarryPriceEnable);

			rm.setCode(ReturnCodeConstant.OK);
			rm.setData(carryPriceVo);
			rm.setMsg("查询成功");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * 校验订金是否可以修改，包含ab测试
	 * 1只读，0可以修改
	 * @param srcMsgId
	 * @return
	 */
	@PostMapping("/checkDepositReadOnly")
	@ResponseBody
	public ResultMsgBean checkDepositReadOnly(Long srcMsgId) {
		if(srcMsgId == null){
			return ResultMsgBean.failResponse(ResponseEnum.request_error.info("货源id不能为空！"));
		}

		int modify = transportOrdersService.checkDepositReadOnly(srcMsgId);

		return ResultMsgBean.successResponse(modify);
	}

}
