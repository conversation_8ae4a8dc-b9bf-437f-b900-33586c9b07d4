package com.tyt.infofee.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqTytCarIntentionMsg;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.infofee.service.PushPracticeTheCarService;
import com.tyt.model.*;
import com.tyt.transport.querybean.BoPublishTransportBean;
import com.tyt.transport.querybean.TransportPublishBean;
import com.tyt.transport.service.BsPublishTransportService;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Controller
    @RequestMapping("/plat/infoFee/TheCar/")
public class PushPracticeTheCarController extends BaseController {

    @Resource(name = "pushPracticeTheCarService")
    private PushPracticeTheCarService pushPracticeTheCarService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "bsPublishTransportService")
    private BsPublishTransportService bsPublishTransportService;
    @Resource(name = "infoFeeBusinessService")
    InfoFeeBusinessService infoFeeBusinessService;
    @Resource(name = "tytMqMessageService")
    TytMqMessageService tytMqMessageService;
    @Resource(name = "tytConfigService")
    private TytConfigService configService;
    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;
    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;
    /**
     * 熟车列表查询接口
     * @param userId	运输公司id
     * @param queryTrade	（首次queryTrade = 1）下拉：queryTrade=1、上滑：当前的页数
     * @return
     */
    @RequestMapping(value = "Practice")
    @ResponseBody
    public ResultMsgBean getList(Long userId, Long queryTrade) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            if (userId ==null || userId < 1 ){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("userId参数错误");
                return rm;
            }
            if (queryTrade == null || queryTrade < 1 || queryTrade > 34){
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("queryTrade参数错误");
                return rm;
            }
            List<PracticeTheCar> practiceList = pushPracticeTheCarService.getPractice(userId, queryTrade);
            rm.setData(practiceList);
        } catch (Exception e) {
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("失败");
        }
        return rm;
    }

}
