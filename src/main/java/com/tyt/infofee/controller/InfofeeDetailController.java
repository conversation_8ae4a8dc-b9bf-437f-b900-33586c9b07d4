package com.tyt.infofee.controller;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.OrderStatusType;
import com.tyt.infofee.enums.PublishGoodsTypeEnum;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TransportWayBillExService;
import com.tyt.model.*;
import com.tyt.plat.constant.PlatBaseConstant;
import com.tyt.plat.entity.base.TytTransportMainExtend;
import com.tyt.promo.service.PromoCouponWriteoffService;
import com.tyt.promo.service.PromoUserCouponService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.transport.service.SeckillGoodsTransportService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.tyt.model.TytTransportWaybillEx.WAYBILL_EX_ORDER_TYPE_COMPLAINT;
import static com.tyt.model.TytTransportWaybillEx.WAYBILL_EX_ORDER_TYPE_EX;

@Controller
@RequestMapping("/plat/infofee/detail")
public class InfofeeDetailController {
    private static final Logger logger = LoggerFactory.getLogger(InfofeeDetailController.class);

    @Autowired
    private IInfofeeDetailService infofeeDetailService;
    @Autowired
    private TransportOrdersService transportOrdersService;
    @Resource(name = "tytConfigService")
    private TytConfigService configService;
    @Resource(name = "transportWayBillExService")
    private TransportWayBillExService transportWayBillExService;
    @Resource(name = "tytUserSubService")
    private TytUserSubService userSubService;

    @Resource(name="promoUserCouponService")
    PromoUserCouponService promoUserCouponService;

    @Resource(name="promoCouponWriteoffService")
    PromoCouponWriteoffService couponWriteoffService;

    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;

    @Autowired
    private TransportMainService transportMainService;

    /**
     * 6210以下安卓（车、货）宽和高显示反了，兼容调换宽高，后续删除@@@@tmp
     * @param infoFeeDetail
     * @param clientVersionStr
     * @param clientSignStr
     */
    private void checkFitError(InfoFeeDetail infoFeeDetail, String clientVersionStr, String clientSignStr){
        if(infoFeeDetail == null){
            return;
        }
        if (StringUtils.isNotEmpty(clientVersionStr) && StringUtils.isNotEmpty(clientSignStr)) {
            Integer clientVersion = Integer.parseInt(clientVersionStr);
            Integer clientType = Integer.parseInt(clientSignStr);

            if(clientVersion < 6210 &&
                    (clientType.intValue() == Constant.ClientSignEnum.ANDROID_GOODS.code //安卓货版
                            || clientType.intValue() == Constant.ClientSignEnum.ANDROID_CAR.code)){//安卓货版
                BaseTransInfo baseTransInfo = infoFeeDetail.getBaseTransInfo();
                if(baseTransInfo != null){
                    String width = baseTransInfo.getWidth();
                    String height = baseTransInfo.getHeight();
                    baseTransInfo.setHeight(width);
                    baseTransInfo.setWidth(height);

                    logger.info("checkFitError_do_switch : " + baseTransInfo.getTsId());
                }
            }
            //兼容总金额、实际支付金额
            if(clientVersion < 6270&& CollectionUtils.isNotEmpty(infoFeeDetail.getInfoFeePayinfoList())){
                for(InfoFeePayinfo infoFeePayinfo :infoFeeDetail.getInfoFeePayinfoList()){
                    infoFeePayinfo.setPayAmount(infoFeePayinfo.getTotalOrderAmount());
                }
            }
        }
    }

    /**
     * @param tsOrderNo
     * @param userId
     * @param isOwner
     * @return
     */
    @RequestMapping(value = {"/getInfo", "/getInfo.action"}, produces = "application/json")
    @ResponseBody
    public ResultMsgBean queryInfofeeDetail(@RequestParam("tsOrderNo") String tsOrderNo,
                                            @RequestParam("userId") String userId,
                                            @RequestParam("isOwner") int isOwner,
                                            @RequestParam(value = "id", required = false)Long id,
                                            HttpServletRequest request) {
        boolean isPc = false;
        String clientSign = request.getParameter("clientSign");
        if (StringUtils.isNotEmpty(clientSign)) {
            Integer clientType = Integer.parseInt(clientSign);
            // 当客户端的标识是1 或者 是12并且是车方的时候isPc为true 为空 或者 是12并且查询货方的时候isPc为false
            isPc = (clientType == ClientTypeEnum.PC.getId() || (clientType == Constant.ClientSignEnum.PC_GOODS.code && isOwner == 0)) ;
        }
        logger.info("query infofee detail, tsOrderNo is {}, userId is {}, isOwner {}, clientSign {}", tsOrderNo, userId, isOwner, clientSign);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        InfoFeeDetail infoFeeDetail = isOwner == 1 ? infofeeDetailService.ownerInfofeeDetail(tsOrderNo, userId, isPc, clientSign) : infofeeDetailService.driverInfofeeDetail(tsOrderNo, userId, isPc, id, clientSign);

        //6210以下安卓（车、货）宽和高显示反了，兼容，后续删除@@@@tmp
        try {
            String clientVersionStr = request.getParameter(PlatBaseConstant.client_version);
            this.checkFitError(infoFeeDetail, clientVersionStr, clientSign);
        } catch (Exception e) {
            logger.error("checkFitError_error : ", e);
        }

        resultMsgBean.setData(infoFeeDetail);
        return resultMsgBean;
    }

    /**
     * 信息费操作
     *
     * @param userId
     * @param orderId
     * @param operateType //1.冻结2.解冻3.同意退款4.拒绝退款 5.确认打款 6.延迟付款 7货方延迟退款
     * @return
     */
    @RequestMapping(value = {"/operate", "/operate.action"})
    @ResponseBody
    public ResultMsgBean operate(@RequestParam(name = "userId", required = true) Long userId,
                                 @RequestParam(name = "orderId", required = true) Long orderId,
                                 @RequestParam(name = "operateType", required = true) Integer operateType) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (orderId == null || orderId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("orderId参数错误");
                return resultMsgBean;
            }
            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3", String.valueOf(orderId), redisLockTimeout)) {
                logger.info("transport order operate get redis lock successed, order id is: " + orderId);
                TytTransportOrders orders = transportOrdersService.getById(orderId);
                if (orders == null) {
                    resultMsgBean.setCode(501);
                    resultMsgBean.setMsg("该运单不存在");
                    return resultMsgBean;
                }
                //当该订单已经被车方延迟付款过一次后 不允许再次操作
                if (orders.getDelayStatus() > 0 && operateType == 6) {
                    resultMsgBean.setCode(503);
                    resultMsgBean.setMsg("该运单已延迟付款,不允许再次进行该操作");
                    return resultMsgBean;
                }
                //当该订单为不退款订单时 不能允许延迟退款操作
                if (orders.getRefundFlag()==0 && operateType == 7) {
                    resultMsgBean.setCode(503);
                    resultMsgBean.setMsg("该运单不允许延迟退款操作,请确认后重试");
                    return resultMsgBean;
                }
                //当该订单为退款订单时 不能允许确认付款操作
                if (orders.getRefundFlag()==1 && (operateType == 5||operateType == 3||operateType == 4)) {
                    resultMsgBean.setCode(503);
                    resultMsgBean.setMsg("订金退还的订单不支持当前操作");
                    return resultMsgBean;
                }
                //当该订单已经被被货方延迟退款过 不允许再次操作
                if (orders.getDelayRefundStatus() > 0 && operateType == 7) {
                    resultMsgBean.setCode(503);
                    resultMsgBean.setMsg("该运单已延迟退款,不允许再次进行该操作");
                    return resultMsgBean;
                }
                //满帮成交的订单，不允许货方操作延迟付款
                //三方平台类型 1:满帮
                Integer thirdpartyPlatformType = orders.getThirdpartyPlatformType();
                if(thirdpartyPlatformType != null
                    && thirdpartyPlatformType == 1
                    && operateType == 7){
                    resultMsgBean.setCode(503);
                    resultMsgBean.setMsg("此订单为运满满司机接单，不支持延迟退款功能，订单如有异常，可以异常上报");
                    return resultMsgBean;
                }

                if (orders.getCostStatus() == OrderStatusType.PAY.getStatus() || orders.getCostStatus() == OrderStatusType.FREEZE.getStatus()
                        || orders.getCostStatus() == OrderStatusType.REFUSE_REFUND.getStatus()
                        || (orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus() && (operateType == 3 || operateType == 4|| operateType == 7))) {
                     resultMsgBean = transportOrdersService.updateOrderCostStatus(orders, operateType);
                } else {
                    String name = "";
                    if (orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus() && operateType == 5) {
                        name = "该运单货方已发起退款无法装货完成";
                    } else {
                        name = OrderStatusType.getName(orders.getCostStatus());
                    }
                    int code = OrderStatusType.getFailCode(orders.getCostStatus());
                    resultMsgBean.setCode(code);
                    resultMsgBean.setMsg(name);
                }
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        } finally {
            logger.info("transport order operate redis lock finished, order id is: " + orderId);
            LockUtil.unLockObject("3", orderId + "");
        }
    }

    /**
     * 信息费详情页删除已完结状态
     *
     * @param payUserId  支付方用户ID
     * @param deParty   删除方  1车方删除 2 货方删除
     * @param tsOrderNo 运单号
     * @return
     */
    @RequestMapping(value = "/deleteInfoDetail")
    @ResponseBody
    public ResultMsgBean deleteInfoDetail(@RequestParam(name = "payUserId", required = false) Long payUserId,
                                          @RequestParam(name = "deParty", required = true) String deParty,
                                          @RequestParam(name = "tsOrderNo", required = true) String tsOrderNo) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            if (StringUtils.isEmpty(tsOrderNo)) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("tsOrderNo参数错误");
                return resultMsgBean;
            }
            if (Integer.parseInt(deParty) < 1 || Integer.parseInt(deParty) > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("deParty参数错误");
                return resultMsgBean;
            }
            List<TytTransportOrders> ordersList = transportOrdersService.getTransportOrdersList(tsOrderNo,deParty, payUserId);
            if (ordersList == null || ordersList.size() == 0) {
                resultMsgBean.setCode(501);
                resultMsgBean.setMsg("该运单不存在");
                return resultMsgBean;
            }
            for (TytTransportOrders orders : ordersList) {
                if (orders.getCostStatus() != OrderStatusType.TO_PAY_CLOSE.getStatus() &&orders.getCostStatus() != OrderStatusType.REFUNDED.getStatus() &&orders.getCostStatus() != OrderStatusType.CONFIRM_PAY.getStatus()
                        && orders.getCostStatus() != OrderStatusType.AUTO_PAY.getStatus()
                        && orders.getCostStatus() != OrderStatusType.EX_HANDLE_COMPLETE.getStatus()) {
                    String name = OrderStatusType.getName(orders.getCostStatus());
                    int code = OrderStatusType.getFailCode(orders.getCostStatus());
                    resultMsgBean.setCode(code);
                    resultMsgBean.setMsg("信息费处于:" + name + "状态,不可删除！请确认后重试");
                    return resultMsgBean;
                }
            }
            transportOrdersService.updateOrderIsDisplay(tsOrderNo,deParty, payUserId);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        }
    }

    /**
     * 异常上报
     *
     * @param userId
     * @param orderId //订单id
     * @param exParty //异常上报方身份1车主上报，2货主上报
     * @param exType  //异常上报类型
     * @param exOther //异常上报类型 其他类型信息
     * @return
     */
    @RequestMapping({"/exSave", "/exSave.action"})
    @ResponseBody
    public ResultMsgBean exSave(@RequestParam(name = "userId", required = true) Long userId,
                                @RequestParam(name = "orderId", required = true) Long orderId,
                                @RequestParam(name = "exParty", required = true) String exParty,
                                @RequestParam(name = "exType", required = true) String exType,
                                @RequestParam(name = "exOther", required = false) String exOther,
                                @RequestParam(name = "loadingStatus", required = false) Integer loadingStatus,
                                @RequestParam(name = "loadingChildStatus", required = false) Integer loadingChildStatus,
                                @RequestParam(name = "proofReason", required = false) String proofReason,
                                @RequestParam(name = "pictureVouchers", required = false) String pictureVouchers
    ) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (orderId == null || orderId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("orderId参数错误");
                return resultMsgBean;
            }
            if (Integer.parseInt(exParty) < 1 || Integer.parseInt(exParty) > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("exParty参数错误");
                return resultMsgBean;
            }
            if (loadingChildStatus != null && (loadingChildStatus!=1&&loadingChildStatus!=2&&loadingChildStatus!=3)) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                resultMsgBean.setMsg("参数错误");
                return resultMsgBean;
            }
            if ("1".equals(exParty)) {

                if (Integer.parseInt(exType) < 1 || Integer.parseInt(exType) > 16) {
                    resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    resultMsgBean.setMsg("异常上报类型错误");
                    return resultMsgBean;
                }
            } else {
                if (Integer.parseInt(exType) < 1 || Integer.parseInt(exType) > 12) {
                    resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    resultMsgBean.setMsg("异常上报类型错误");
                    return resultMsgBean;
                }
            }

            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3", String.valueOf(orderId), redisLockTimeout)) {

                //查看是否已存在处理中得异常上报订单
                List<TytTransportWaybillEx> transportWaybillExs = transportWayBillExService.getWayBillExsByOrderId(orderId, 0,"0");
                if(transportWaybillExs!=null&&transportWaybillExs.size()>0) {
                    for (TytTransportWaybillEx transportWaybillEx:transportWaybillExs){
                        if ("1".equals(transportWaybillEx.getExStatus()) || "0".equals(transportWaybillEx.getExStatus())) {
                            resultMsgBean.setCode(ReturnCodeConstant.EX_NONE_FINISH);
                            resultMsgBean.setMsg("该订单存在处理中的异常上报");
                            return resultMsgBean;
                        }
                    }
                }

                logger.info("transport order ex save get redis lock successed, order id is: " + orderId);
                TytTransportOrders orders = transportOrdersService.getById(orderId);
                if (orders == null) {
                    resultMsgBean.setCode(501);
                    resultMsgBean.setMsg("该运单不存在");
                    return resultMsgBean;
                }
                //update by sissy 新增 当主动点击延迟付款和 部分退款后 点击拒绝 延迟付款 都可以进行异常上报处理
                if (orders.getCostStatus() == OrderStatusType.FREEZE.getStatus() //冻结中
                        || orders.getCostStatus() == OrderStatusType.REFUSE_REFUND.getStatus() //拒绝退款
                        || (orders.getCostStatus() == OrderStatusType.PAY.getStatus()) //支付成功，车、货均可
                        || (orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus())) {

                    transportWayBillExService.saveEx(orders, exParty, exType, exOther,loadingStatus,loadingChildStatus,proofReason,pictureVouchers,userId);
                } else {
                    int code = OrderStatusType.getFailCode(orders.getCostStatus());
                    String name = OrderStatusType.getName(orders.getCostStatus());
                    resultMsgBean.setCode(code);
                    resultMsgBean.setMsg(name);
                }
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        } finally {
            logger.info("transport order ex save redis lock finished, order id is: " + orderId);
            LockUtil.unLockObject("3", orderId + "");
        }
    }

    /**
     * 撤销异常上报
     *
     * @param userId   //当前操作人Id
     * @param orderId //订单id
     * @param cancelExParty //撤销异常上报方身份 1车主撤销，2货主撤销
     * @return
     */
    @RequestMapping({"/cancelExSave", "/cancelExSave.action"})
    @ResponseBody
    public ResultMsgBean cancelExSave(@RequestParam(name = "userId", required = true) Long userId,
                                @RequestParam(name = "orderId", required = true) Long orderId,
                                @RequestParam(name = "cancelExParty", required = true) String cancelExParty) {
        logger.info("撤销异常上报【userId:{}】【orderId:{}】【cancelExParty:{}】 ",userId, orderId,cancelExParty);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (orderId == null || orderId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("orderId参数错误");
                return resultMsgBean;
            }
            if (Integer.parseInt(cancelExParty) < 1 || Integer.parseInt(cancelExParty) > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("cancelExParty参数错误");
                return resultMsgBean;
            }
            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3", String.valueOf(orderId), redisLockTimeout)) {
                logger.info("transport cancelExSave get redis lock successed, order id is: " + orderId);
                TytTransportOrders orders = transportOrdersService.getById(orderId);
                if (orders == null) {
                    resultMsgBean.setCode(501);
                    resultMsgBean.setMsg("该运单不存在");
                    return resultMsgBean;
                }
                if (orders.getThirdpartyPlatformType()!= null&&orders.getThirdpartyPlatformType()==1) {
                    resultMsgBean.setCode(501);
                    resultMsgBean.setMsg("满帮订单不能撤销异常上报");
                    return resultMsgBean;
                }
                resultMsgBean = transportWayBillExService.cancelExSave(orders, userId, cancelExParty);
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        } finally {
            logger.info("transport cancelExSave save redis lock finished, order id is: " + orderId);
            LockUtil.unLockObject("3", orderId + "");
        }
    }


    /**
     * 订单发起投诉
     *
     * @param userId
     * @param orderId //订单id
     * @param exParty //异常上报方身份1车主上报，2货主上报
     * @param exType  //异常上报类型
     * @param exOther //异常上报类型 其他类型信息
     * @return
     */
    @RequestMapping({"/saveComplaint", "/saveComplaint.action"})
    @ResponseBody
    public ResultMsgBean saveComplaint(@RequestParam(name = "userId", required = true) Long userId,
                                @RequestParam(name = "orderId", required = true) Long orderId,
                                @RequestParam(name = "exParty", required = true) String exParty,
                                @RequestParam(name = "exType", required = true) String exType,
                                @RequestParam(name = "exOther", required = false) String exOther,
                                @RequestParam(name = "loadingStatus", required = true) Integer loadingStatus,
                                @RequestParam(name = "loadingChildStatus", required = false) Integer loadingChildStatus,
                                @RequestParam(name = "proofReason", required = true) String proofReason,
                                @RequestParam(name = "pictureVouchers", required = false) String pictureVouchers
    ) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "提交成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (orderId == null || orderId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("orderId参数错误");
                return resultMsgBean;
            }
            if (Integer.parseInt(exParty) < 1 && Integer.parseInt(exParty) > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("exParty参数错误");
                return resultMsgBean;
            }
            if (loadingChildStatus != null && (loadingChildStatus!=1&&loadingChildStatus!=2&&loadingChildStatus!=3)) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                resultMsgBean.setMsg("参数错误");
                return resultMsgBean;
            }
            if ("1".equals(exParty)) {

                if (Integer.parseInt(exType) < 1 || Integer.parseInt(exType) > 16) {
                    resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    resultMsgBean.setMsg("异常上报类型错误");
                    return resultMsgBean;
                }
            } else {
                if (Integer.parseInt(exType) < 1 || Integer.parseInt(exType) > 12) {
                    resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                    resultMsgBean.setMsg("异常上报类型错误");
                    return resultMsgBean;
                }
            }
            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3", String.valueOf(orderId), redisLockTimeout)) {
                logger.info("transport order ex save get redis lock successed, order id is: " + orderId);
                TytTransportOrders orders = transportOrdersService.getById(orderId);
                if (orders == null) {
                    resultMsgBean.setCode(501);
                    resultMsgBean.setMsg("该运单不存在");
                    return resultMsgBean;
                }
                //update by sissy 新增 当主动点击延迟付款和 部分退款后 点击拒绝 延迟付款 都可以进行异常上报处理
                if (orders.getCostStatus() == OrderStatusType.REFUNDED.getStatus() //已退款
                        || orders.getCostStatus() == OrderStatusType.CONFIRM_PAY.getStatus() //已打款
                        || (orders.getCostStatus() == OrderStatusType.AUTO_PAY.getStatus()) //自动收款
                        || (orders.getCostStatus() == OrderStatusType.EX_HANDLE_COMPLETE.getStatus())) { //异常处理完成

                    transportWayBillExService.saveComplaint(orders, exParty, exType, exOther,loadingStatus,loadingChildStatus,proofReason,pictureVouchers,userId);
                } else {
                    int code = OrderStatusType.getFailCode(orders.getCostStatus());
                    String name = OrderStatusType.getName(orders.getCostStatus());
                    resultMsgBean.setCode(code);
                    resultMsgBean.setMsg(name);
                }
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        } finally {
            logger.info("transport order ex save redis lock finished, order id is: " + orderId);
            LockUtil.unLockObject("3", orderId + "");
        }
    }

    /**
     * 异常上报-补充凭证
     *
     * @param userId
     * @param orderId //订单id
     * @return
     */
    @RequestMapping({"/addVoucher", "/addVoucher.action"})
    @ResponseBody
    public ResultMsgBean addVoucher(@RequestParam(name = "userId", required = true) Long userId,
                                @RequestParam(name = "orderId", required = true) Long orderId,
                                @RequestParam(name = "orderType", required = false) Long orderType,
                                @RequestParam(name = "proofReason", required = false) String proofReason,
                                @RequestParam(name = "pictureVouchers", required = false) String pictureVouchers,
                                String clientSign){

        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        TytTransportOrders orders = transportOrdersService.getById(orderId);
        if (orders == null) {
            resultMsgBean.setCode(501);
            resultMsgBean.setMsg("该运单不存在");
            return resultMsgBean;
        }

        TytTransportWaybillEx transportWaybillEx = null;

        if (orderType == null || orderType == WAYBILL_EX_ORDER_TYPE_EX) {
            transportWaybillEx = transportWayBillExService.getWayBillExByOrderId(orderId, WAYBILL_EX_ORDER_TYPE_EX);
        } else if (orderType == WAYBILL_EX_ORDER_TYPE_COMPLAINT) {
            transportWaybillEx = transportWayBillExService.getWayBillExByOrderId(orderId, WAYBILL_EX_ORDER_TYPE_COMPLAINT);
        }

        if (transportWaybillEx == null) {
            resultMsgBean.setCode(501);
            resultMsgBean.setMsg("工单不存在");
            return resultMsgBean;
        }

        if ("2".equals(transportWaybillEx.getExStatus())) {
            resultMsgBean.setCode(502);
            resultMsgBean.setMsg("提交失败，工单已处理");
            return resultMsgBean;
        }

        int port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
        String exParty = null;
        if (port == 0) {
            exParty = "2";
        } else if (port == 4) {
            exParty = "1";
        } else {
            exParty = String.valueOf(port);
        }
        transportWayBillExService.addVoucher(orders, transportWaybillEx.getId(), exParty, proofReason, pictureVouchers, userId);

        return resultMsgBean;
    }


    /**
     * 信息费退还申请接口
     *
     * @param userId
     * @param orderId
     * @param refundAmount //退款金额
     * @return
     */
    @RequestMapping(value = {"/giveBack", "/giveBack.action"})
    @ResponseBody
    public ResultMsgBean giveBack(@RequestParam(name = "userId", required = true) Long userId,
                                  @RequestParam(name = "orderId", required = true) Long orderId,
                                  @RequestParam(name = "refundAmount", required = true) Long refundAmount,
                                  @RequestParam(name = "refundReason", required = false) String refundReason,
                                  @RequestParam(name = "pwd", required = false) String pwd,
                                  @RequestParam(name = "isRefundCoupon", required = false) String isRefundCoupon,
                                  @RequestParam(name = "refundSpecificReason", required = false) String refundSpecificReason,
                                  @RequestParam(name = "refundRemark", required = false)   String refundRemark) {
        logger.info("giveBack userId is 【{}】 orderId is 【{}】 refundReason is 【{}】refundSpecificReason is {}",userId,orderId,refundReason,refundSpecificReason);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (orderId == null || orderId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("orderId参数错误");
                return resultMsgBean;
            }
            boolean pwdIsTrue = true;
//            TytUserSub userSub = userSubService.getTytUserSubByUserId(Long.valueOf(userId));
//            if (userSub.getPocketPwdStatus() == null || userSub.getPocketPwdStatus().intValue() == 1) {
//                if (StringUtils.isEmpty(pwd)) {
//                    pwdIsTrue = true;
//                } else {
//                    resultMsgBean.setCode(300);
//                    resultMsgBean.setMsg("未设置密码");
//                    return resultMsgBean;
//                }
//            } else {
//                if (StringUtils.isNotBlank(pwd) && userSub.getPocketPwd().equalsIgnoreCase(MD5Util.GetMD5Code(pwd + userId))) {
//                    pwdIsTrue = true;
//                } else {
//                    resultMsgBean.setCode(400);
//                    resultMsgBean.setMsg("钱包密码错误，请重新输入");
//                    return resultMsgBean;
//                }
//            }
            //获取当前订单状态
            if (pwdIsTrue) {
                int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
                if (LockUtil.lockObject("3", String.valueOf(orderId), redisLockTimeout)) {
                    logger.info("transport order infoFee give back get redis lock successed, order id is: " + orderId);
                    TytTransportOrders orders = transportOrdersService.getById(orderId);
                    if (orders == null) {
                        resultMsgBean.setCode(501);
                        resultMsgBean.setMsg("该运单不存在");
                        return resultMsgBean;
                    }
                    //三方平台类型 1:满帮
                    Integer thirdpartyPlatformType = orders.getThirdpartyPlatformType();
                    //支付金额单位分
                    Long payAmount = orders.getPayAmount();
                    if(thirdpartyPlatformType != null && thirdpartyPlatformType == 1){
                        if(refundAmount.longValue() < payAmount.longValue()){
                            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                            resultMsgBean.setMsg("当前订单为运满满平台司机接单，不支持部分退款，" +
                                                 "如果疑问请拨打特运通客服电话  400-668-8998");
                            return resultMsgBean;
                        }
                    }
                    if (orders.getCostStatus() == OrderStatusType.PAY.getStatus() || orders.getCostStatus() == OrderStatusType.FREEZE.getStatus() || orders.getCostStatus() == OrderStatusType.REFUSE_REFUND.getStatus() || orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus()) {
                        orders.setRefundSpecificReason(refundSpecificReason);
                        orders.setRefundRemark(refundRemark);
                        transportOrdersService.updateOrderForGiveBack(orders, refundAmount,refundReason);
                        //查询优惠券信息
                        PromoCouponWriteoff couponWriteoff = couponWriteoffService.getByUserIdAndOrderId(userId, orders.getId().toString());
                        if("1".equals(isRefundCoupon) && couponWriteoff != null){
                            //修改核销状态和优惠券状态为可使用
                            couponWriteoffService.updateStatus(couponWriteoff.getOrderId());
                            promoUserCouponService.updateStatus(couponWriteoff.getCouponId());
                        }
                    } else {
                        int code = OrderStatusType.getFailCode(orders.getCostStatus());
                        String name = OrderStatusType.getName(orders.getCostStatus());
                        resultMsgBean.setCode(code);
                        resultMsgBean.setMsg(name);
                    }
                }
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        } finally {
            logger.info("transport order infoFee give back redis lock finished, order id is: " + orderId);
            LockUtil.unLockObject("3", orderId + "");
        }
    }


    /**
     * 车方订金退还申请/货方退回订金 接口
     *
     * @param userId  车方userId
     * @param orderId 信息费订单
     * @param refundOriginator  退款发起方 1 车方主动申请  2 货方退回
     * @return
     */
    @RequestMapping(value = "/infoFeeGiveBack")
    @ResponseBody
    public ResultMsgBean infoFeeGiveBack( @RequestParam(name = "userId", required = true) Long userId,
                                          @RequestParam(name = "orderId", required = true) Long orderId,
                                          @RequestParam(name = "refundReason", required = false) String refundReason,
                                          @RequestParam(name = "refundOriginator", required = true) Integer refundOriginator,
                                          @RequestParam(name = "refundSpecificReason", required = false) String refundSpecificReason,
                                          @RequestParam (name = "refundRemark", required = false)String refundRemark) {
        logger.info("infoFeeGiveBack userId is 【{}】 orderId is 【{}】refundOriginator is 【{}】 refundReason is 【{}】 refundSpecificReason is【{}】 refundRemark is 【{}】 ", userId, orderId,refundOriginator,refundReason,refundSpecificReason,refundRemark);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (orderId == null || orderId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("orderId参数错误");
                return resultMsgBean;
            }
            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3", String.valueOf(orderId), redisLockTimeout)) {
                logger.info("transport order infoFee infoFeeGiveBack get redis lock successed, order id is: " + orderId);
                TytTransportOrders orders = transportOrdersService.getById(orderId);
                if (orders == null) {
                    resultMsgBean.setCode(501);
                    resultMsgBean.setMsg("该运单不存在");
                    return resultMsgBean;
                }
                if (orders.getRefundFlag()==0) {
                    resultMsgBean.setCode(501);
                    resultMsgBean.setMsg("该运单不允许进行该操作");
                    return resultMsgBean;
                }
                //①:当前状态为已支付时  车方可以发起退款申请  ②:当前状态为退款中或 已支付时  货方可以发起退款
                if ((refundOriginator==1&&(orders.getCostStatus() == OrderStatusType.PAY.getStatus()||
                        (orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus()&&orders.getDelayRefundStatus()==1)))||
                           (refundOriginator==2&&(orders.getCostStatus() == OrderStatusType.PAY.getStatus()||
                                          orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus()))) {
                    orders.setRefundSpecificReason(refundSpecificReason);
                    orders.setRefundRemark(refundRemark);
                    transportOrdersService.updateOrderForInfoFeeGiveBack(orders,refundOriginator,refundReason);
                } else {
                    if(refundOriginator==1&&orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus()&&orders.getDelayRefundStatus()==0){
                        resultMsgBean.setCode(501);
                        resultMsgBean.setMsg("该运单不允许进行该操作");
                        return resultMsgBean;
                    }
                    int code = OrderStatusType.getFailCode(orders.getCostStatus());
                    String name = OrderStatusType.getName(orders.getCostStatus());
                    resultMsgBean.setCode(code);
                    resultMsgBean.setMsg(name);
                }
            }
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        } finally {
            logger.info("transport order infoFee infoFeeGiveBack redis lock finished, order id is: " + orderId);
            LockUtil.unLockObject("3", orderId + "");
        }
    }

    /**
     * 专供后台管理系统异常订单退还定金调用
     * @param giveBackBean 退还实体
     * @return ResultMsgBean
     */
    @PostMapping(value = "/exInfoFeeGiveBack")
    @ResponseBody
    public ResultMsgBean exInfoFeeGiveBack(ExInfoFeeGiveBackBean giveBackBean) {
        logger.info("exInfoFeeGiveBack giveBackBean is 【{}】", JSON.toJSONString(giveBackBean));
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        Long orderId = giveBackBean.getOrderId();
        try {
            //获取当前订单状态
            int redisLockTimeout = configService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
            if (LockUtil.lockObject("3", String.valueOf(orderId), redisLockTimeout)) {
                logger.info("exInfoFeeGiveBack get redis lock successed, order id is: " + orderId);
                TytTransportOrders orders = transportOrdersService.getById(orderId);
                if (orders == null) {
                    resultMsgBean.setCode(501);
                    resultMsgBean.setMsg("该运单不存在");
                    return resultMsgBean;
                }
                //三方平台类型 1:满帮
                Integer thirdPartyPlatformType = orders.getThirdpartyPlatformType();
                //支付金额和退款金额进行对比
                if(thirdPartyPlatformType != null && thirdPartyPlatformType == 1){
                    if(giveBackBean.getRefundAmount()< orders.getPayAmount().longValue()){
                        resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                        resultMsgBean.setMsg("当前订单为运满满平台司机接单，不支持部分退款，" +
                                "如果疑问请拨打特运通客服电话  400-668-8998");
                        return resultMsgBean;
                    }
                }
                //当 ①:状态为已支付时  ②:订金类型为退还 订单状态为退款中 ③:订金类型为不退还 订单状态为已拒绝
                if (orders.getCostStatus() == OrderStatusType.PAY.getStatus()||
                        (orders.getCostStatus() == OrderStatusType.REFUNDING.getStatus()&&orders.getRefundFlag()==1)||
                        (orders.getCostStatus() == OrderStatusType.REFUNDED.getStatus()&&orders.getDelayRefundStatus()==0)) {
                    transportOrdersService.updateExInfoFeeOrderForGiveBack(orders,giveBackBean);
                } else {
                    int code = OrderStatusType.getFailCode(orders.getCostStatus());
                    String name = OrderStatusType.getName(orders.getCostStatus());
                    resultMsgBean.setCode(code);
                    resultMsgBean.setMsg(name);
                }
            }
        } catch (Exception e) {
            logger.error("后台管理系统异常订单退还定金异常:",e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        } finally {
            logger.info("exInfoFeeGiveBack redis lock finished, order id is: " + orderId);
            LockUtil.unLockObject("3", orderId + "");
        }
        return resultMsgBean;
    }

    /**
     * 判断货源是否可以编辑
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    @RequestMapping(value = "/isEdit")
    @ResponseBody
    public ResultMsgBean isEdit(@RequestParam(name = "userId", required = true) Long userId,
                                @RequestParam(name = "srcMsgId", required = true) Long srcMsgId, String clientSign) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (srcMsgId == null || srcMsgId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("srcMsgId参数错误");
                return resultMsgBean;
            }
            //好货抢单锁定判断
            if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(srcMsgId)) {
                return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
            }
            Integer ordersCount = transportOrdersService.getCountBySrcMsgId(srcMsgId);
            if (ordersCount <= 0) {
                if (StringUtils.isNotBlank(clientSign) && Integer.parseInt(clientSign) == Constant.ClientSignEnum.PC.code){
                    TransportMain main = transportMainService.getTransportMainForId(srcMsgId);
                    if (main != null){
                        TytTransportMainExtend extend = transportMainService.getMainExtend(main.getSrcMsgId());
                        if (extend != null && extend.getClientFusion() != null && extend.getClientFusion() == 1){
                            if (null != main.getPublishGoodsType()
                                    && (main.getPublishGoodsType() == PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode()
                                    || PublishGoodsTypeEnum.isExcellentGoods(main.getPublishGoodsType()))){
                                resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                                resultMsgBean.setMsg("该货源享平台权益，请至APP编辑发布");
                                return resultMsgBean;
                            }
                        }
                    }
                }

                resultMsgBean.setData(1);
                resultMsgBean.setMsg("货源可编辑");
            } else {
                resultMsgBean.setData(2);
                resultMsgBean.setMsg("已有车主支付信息费，当天不可编辑再发布");
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
        }
        return resultMsgBean;
    }

    /**
     * @param tsOrderNo
     * @param userId
     * @param isOwner
     * @return
     */
    @RequestMapping(value = {"/queryInfofee"}, produces = "application/json")
    @ResponseBody
    public ResultMsgBean queryInfofee(@RequestParam("tsOrderNo") String tsOrderNo,
                                      @RequestParam("userId") String userId,
                                      @RequestParam("isOwner") int isOwner,
                                      HttpServletRequest request) {
        boolean isPc = false;
        logger.info("query infofee detail, tsOrderNo is {}, userId is {}, isOwner {}, clientSign {}", tsOrderNo, userId, isOwner);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, ResultMsgBean.OK_MSG);
        TransportInfoFeeBean infoFeeDetail = infofeeDetailService.queryInfofee(tsOrderNo, userId, isPc);
        resultMsgBean.setData(infoFeeDetail);
        return resultMsgBean;
    }


    /**
     * 异常处理评价
     *
     * @param userId
     * @param orderId //订单id
     * @param evaluateParty 评价方身份1车主上报，2货主上报
     * @param dealResultEvaluate  处理结果评价：1满意 2 一般 3不满意
     * @param dealTimeEvaluate 处理时长评价：1满意 2 一般 3不满意
     * @param serviceAttitudeEvaluate 服务态度评价：1满意 2 一般 3不满意
     * @return
     */
    @RequestMapping({"/evaluate"})
    @ResponseBody
    public ResultMsgBean evaluate(@RequestParam(name = "userId", required = true) Long userId,
                                @RequestParam(name = "orderId", required = true) Long orderId,
                                @RequestParam(name = "evaluateParty", required = true) Integer evaluateParty,
                                  @RequestParam(name = "dealResultEvaluate", required = true) Integer dealResultEvaluate,
                                  @RequestParam(name = "dealTimeEvaluate", required = false) Integer dealTimeEvaluate,
                                  @RequestParam(name = "serviceAttitudeEvaluate", required = true) Integer serviceAttitudeEvaluate,
                                  @RequestParam(name = "decideResponsibilityEvaluate", required = false) Integer decideResponsibilityEvaluate,
                                  @RequestParam(name = "evaluateDetail", required = false) String evaluateDetail
    ) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            if (orderId == null || orderId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("orderId参数错误");
                return resultMsgBean;
            }
            resultMsgBean = transportWayBillExService.evaluate(orderId, userId,
                    evaluateParty, dealResultEvaluate, dealTimeEvaluate, serviceAttitudeEvaluate, decideResponsibilityEvaluate, evaluateDetail);

            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        }
    }


    private enum ClientTypeEnum {
        //clientType客户端类型1：PC;2：android; 3:ios

        PC(1),
        NEW_PC(12),
        IOS(3),
        ANDROID(2);

        private int id;

        ClientTypeEnum(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

}
