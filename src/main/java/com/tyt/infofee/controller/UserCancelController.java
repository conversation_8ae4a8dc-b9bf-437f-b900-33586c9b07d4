package com.tyt.infofee.controller;

import cn.hutool.http.HttpRequest;
import com.gexin.fastjson.JSON;
import com.google.common.collect.Maps;
import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.enums.PermissionGainTypeEnum;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.infofee.bean.UserCancelBean;
import com.tyt.infofee.bean.UserCancelNoticeBean;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.UserCancelService;
import com.tyt.infofee.service.WalletService;
import com.tyt.manbang.bean.response.BaseResponse;
import com.tyt.model.*;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.bean.GoodsType;
import com.tyt.permission.bean.PermissionChangeType;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TransportService;
import com.tyt.user.bean.Resp;
import com.tyt.user.querybean.SourceBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytSourceService;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSwitchUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户注销
 *
 * <AUTHOR>
 * @date 2021-12-21下午17:46:59
 * @description
 */
@Controller
@RequestMapping("/plat/userCancel/")
public class UserCancelController extends BaseController {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "userCancelService")
	private UserCancelService userCancelService;

    @Resource(name = "tytSourceService")
    private TytSourceService tytSourceService;

    @Resource(name = "transportOrdersService")
    private TransportOrdersService transportOrdersService;

    @Resource(name = "transportService")
    private TransportService transportService;

    @Resource(name = "walletService")
    private WalletService walletService;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;

    @Resource(name = "userBuyGoodsService")
    private UserBuyGoodsService userBuyGoodsService;

    @Resource(name = "tytUserIdentityAuthService")
    private TytUserIdentityAuthService identityAuthService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    public static final String userWeb =AppConfig.getProperty("user.service.api.url");

    private static final String userCenterUrl = AppConfig.getProperty("user.center.api.url");
    private final String MERCHANT_ID = AppConfig.getProperty("tpay.merchantId");
    private final String VERSION = AppConfig.getProperty("tpay.version");

    /**
     * 根据groupCode获取sourceList
     *
     * @param groupCode
     * @return
     */
    @RequestMapping(value = "/get/tytSourceListByGroupCode")
    @ResponseBody
    public ResultMsgBean getTytSourceListByGroupCode(@RequestParam(value = "groupCode", required = true) String groupCode) {
        logger.info("user logOut getTytSourceListByGroupCode groupCode is: " + groupCode);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        List<SourceBean> sourceList = tytSourceService.getByGroupCode("groupCode");
        resultMsgBean.setData(sourceList);
        return resultMsgBean;
    }


    /**
     * 判断用户是否可注销
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/get/judgeUserCancelOrNot", "/get/judgeUserCancelOrNot.action"})
    @ResponseBody
    private ResultMsgBean getJudgeUserCancelOrNot(@RequestParam(value = "userId", required = true) Long userId) {
        logger.info("UserCancelController getJudgeUserCancelOrNot userId is 【{}】", userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.ERROR, "失败", false);
        if (StringUtils.isBlank(String.valueOf(userId))) {
            resultMsgBean.setMsg("userId必填");
            return resultMsgBean;
        }
        //将校验失败的结果添加
        List<String> judgeResult = new ArrayList<String>();
        try {
            //①判断余额是否为0 不为0不可注销
            BigDecimal totalRemaining = null;
            if (!TytSwitchUtil.isManBangVersion()){
                totalRemaining = walletService.getUserAccountBalance(Long.toString(userId));
            }else{
                totalRemaining = walletService.getManBangUserAccountBalance(userId.toString(),null);
            }
            logger.info("UserCancelController getJudgeUserCancelOrNot return totalRemaining is 【{}】", totalRemaining);
            if (totalRemaining.compareTo(new BigDecimal("0.00")) > 0) {
                judgeResult.add("钱包金额大于0元");
            }
            //②查看今天是否有发布中得货源 获取货源发布中的发布条数 不为0不可注销
            Long myTodayGoodCount = transportService.getMyPublishaNbr(Long.valueOf(userId), 1, null);
            logger.info("UserCancelController getJudgeUserCancelOrNot return myTodayGoodCount is 【{}】", myTodayGoodCount);
            if (myTodayGoodCount > 0) {
                judgeResult.add("有货源处理发布中");
            }
            //③:查看当前用户是否有处于未完结状态的信息费 不为0不可注销
            Long unfinishedOrders = transportOrdersService.getUnfinishedOrders(Long.valueOf(userId));
            logger.info("UserCancelController getJudgeUserCancelOrNot return unfinishedOrders is 【{}】", unfinishedOrders);
            if (unfinishedOrders > 0) {
                judgeResult.add("有订单未完成");
            }
            //④:查看用户的车/货会员权益是否到期 100101(车会员) 100201(货会员)
            List<UserPermission> userPermissionList = userPermissionService.getPermissionListByUserId(Long.valueOf(userId));
            logger.info("UserCancelController getJudgeUserCancelOrNot return userPermissionList is 【{}】", JSON.toJSONString(userPermissionList));
            for (UserPermission userPermission : userPermissionList) {
                if (("100101".equals(userPermission.getServicePermissionTypeId())) && userPermission.getStatus() == 1) {
                    judgeResult.add("车会员权益未到期");
                } else if ("100201".equals(userPermission.getServicePermissionTypeId()) && userPermission.getStatus() == 1) {
                    judgeResult.add("货会员权益未到期");
                }
            }
            //⑤:当身份审核中的用户去注销账户时，提示toast：”身份审核中，请稍后再试！“（车货双端都有）
            TytUserIdentityAuth userIdentityAuth = identityAuthService.getByUserId(userId.toString());
            if (userIdentityAuth != null){
                logger.info("UserCancelController getByUserId return userIdentityAuth is 【{}】", JSON.toJSONString(userIdentityAuth));
                if (userIdentityAuth.getExamineStatus()!=null&&userIdentityAuth.getExamineStatus()==1) {
                    judgeResult.add("身份审核中，请稍后再试！");
                }
            }

            if (judgeResult.size() > 0) {
                //通过校验 进行返回
                resultMsgBean.setCode(10001);
                resultMsgBean.setMsg("校验失败");
                resultMsgBean.setData(judgeResult);
            } else {
                //通过校验 进行返回
                resultMsgBean.setCode(ReturnCodeConstant.OK);
                resultMsgBean.setMsg("成功");
                resultMsgBean.setData(true);
            }
            logger.info("UserCancelController getJudgeUserCancelOrNot userId is 【{}】 resultMsgBean is 【{}】", userId, JSON.toJSONString(resultMsgBean));
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("判断用户是否可注销异常,异常信息{}", e);
            resultMsgBean.setMsg("服务器异常");
            return resultMsgBean;
        }
    }

    /**
     * @param userCancelBean
     * @return com.tyt.model.ResultMsgBean
     * @description 用户账号注销申请接口
     * <AUTHOR>
     * @date 2021/12/22 17:20
     */
    @RequestMapping(value = {"userCancelApply", "userCancelApply.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean userCancelApply(UserCancelBean userCancelBean) {
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        Long userId = userCancelBean.getUserId();
        try {
            String verificationCode = userCancelBean.getVerificationCode();
            Integer reason = userCancelBean.getReason();
            String reasonDetail = userCancelBean.getReasonDetail();
            //1.校验参数
            if (userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID不能为空！");
                return rm;
            }
            User user = userService.getByUserId(userId);
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户信息不存在！");
                return rm;
            }
            //获取用户手机号
            String cellPhone = user.getCellPhone();
            if (StringUtils.isBlank(verificationCode)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("验证码不能为空！");
                return rm;
            }
            if (reason == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("注销原因不能为空！");
                return rm;
            }
            //如果注销原因选择4.其它原因，则注销原因详情不能为空
            if (reason != null && reason.intValue() == 4) {
                if (StringUtils.isBlank(reasonDetail)) {
                    rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    rm.setMsg("注销原因详情不能为空！");
                    return rm;
                }
            }
            //2.校验验证码是否正确
            String realVerifyCode = RedisUtil.get(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
            if (!verificationCode.equals(realVerifyCode)) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
                rm.setMsg("短信验证码错误！");
                return rm;
            } else {
                RedisUtil.del(Constant.SMS_VERIFYCODE_PREFFIX + cellPhone);
            }
            //3.判断用户是否可注销
            ResultMsgBean msgBean = this.getJudgeUserCancelOrNot(userId);
            if (msgBean == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户是否可注销校验失败！");
                return rm;
            }
            Integer code = msgBean.getCode();
            if (code == null || code.intValue() != 200) {
                //直接返回注销失败信息
                return msgBean;
            }

            if (!TytSwitchUtil.isManBangVersion()){
                //4.判断用户是否在连连开通代扣协议，如果已开通则调用协议关闭接口关闭代扣协议
                msgBean = userService.cancelAgree(userCancelBean);
                logger.info("调用用户中心代扣协议关闭接口，返回数据msgBean【{}】", JSON.toJSONString(msgBean));
                if(msgBean == null){
                    rm.setCode(ReturnCodeConstant.ERROR);
                    rm.setMsg("查询用户是否在连连开通代扣协议失败！");
                    return rm;
                }
                if(msgBean.getCode() != 200 && msgBean.getCode() != 5002){
                    rm.setCode(ReturnCodeConstant.ERROR);
                    rm.setMsg("关闭已开通的代扣协议失败！");
                    return rm;
                }
            }else{
                Map<String, String> paramMap = Maps.newHashMap();
                paramMap.put("userId",user.getId()+"");
                paramMap.put("timestamp",System.currentTimeMillis()+"");
                paramMap.put("merchantId",MERCHANT_ID);
                paramMap.put("version",VERSION);
                paramMap.put("osVersion",userCancelBean.getOsVersion());
                paramMap.put("clientSign",userCancelBean.getClientSign()+"");
                paramMap.put("clientVersion",userCancelBean.getClientVersion());
                paramMap.put("clientId",userCancelBean.getClientId());
                paramMap.put("ticket",userCancelBean.getTicket());

                Integer interfaceMoveSwitch = tytConfigService.getIntValue("interface_move_switch", 0);
                logger.info("【接口迁移】开关:{}",interfaceMoveSwitch);
                String result = null;
                if(interfaceMoveSwitch == 1){
                    result = HttpRequest.post(userCenterUrl + "/mbOpenAcct/cancel/verify")
                            .body(com.alibaba.fastjson.JSON.toJSONString(paramMap))
                            .execute()
                            .body();
                }else{
                    try {
                        result = HttpRequest.post(userWeb + "/mbOpenAcct/cancel/verify")
                                .body(com.alibaba.fastjson.JSON.toJSONString(paramMap))
                                .execute()
                                .body();
                    } catch (Exception e) {
                        logger.error("【接口迁移】mbOpenAcctInactiveApply新服务调用异常：{}",e);
                    }
                    logger.info("【接口迁移】mbOpenAcctInactiveApply新服务调用返回：{}",result);
                }

                logger.info("集团注销实名结果:【{}】",result);
                Resp resp = com.alibaba.fastjson.JSON.parseObject(result, Resp.class);
                logger.info("<集团注销实名>结果:【{}】", com.alibaba.fastjson.JSON.toJSONString(resp));
                if (resp != null && resp.getCode().equals(ResultMsgBean.OK)) {
                    BaseResponse response = com.alibaba.fastjson.JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(resp.getData()), BaseResponse.class);
                    if (!"T".equals(response.getIsSuccess())) {
                        rm.setCode(ReturnCodeConstant.ERROR);
                        rm.setMsg(response.getErrorMessage());
                        return rm;
                    }
                }else{
                    rm.setCode(ReturnCodeConstant.ERROR);
                    rm.setMsg("集团注销实名失败！");
                    return rm;
                }
            }
            //5.修改用户相关信息并退出登录
            userCancelService.updateUserRelInfoAndLogOut(userCancelBean, userId, user, cellPhone);
        } catch (Exception ex) {
            logger.error("用户账号注销失败！ userId : " + userId, ex);
            return ResultMsgBean.failResponse(ex);
        }
        return rm;
    }

    /**
     * 用户注销提示弹窗获取
     * @param userId 用户id
     * @return rm
     */
    @RequestMapping(value = {"userCancelNotice"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean userCancelNotice(Long userId){
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK, "查询成功");
        try{
            if (userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID不能为空！");
                return rm;
            }
            User user = userService.getByUserId(userId);
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户信息不存在！");
                return rm;
            }
            //获取用户手机号
            String cellPhone = user.getCellPhone();
            UserCancelNoticeBean noticeBean = userCancelService.getCountByCellPhoneAndGoodsPort(cellPhone);
            if (noticeBean == null || noticeBean.getCount() <= 0){
                //获取用户三个月内是否赠送过权益
                Date startTime = TimeUtil.dateAddMonth(TimeUtil.today(),-3);
                if (userBuyGoodsService.isGivingRight(userId, GoodsType.注销赠送商品.getId(),startTime,new Date())){
                    return rm;
                }
                rm.setCode(ReturnCodeConstant.NO_PERMISSION);
                rm.setNoticeData(tytNoticePopupTemplService.getByType(PopupTypeEnum.货app首次注销提示.getType1(),PopupTypeEnum.货app首次注销提示.getType2()));
                return rm;
            }
            if (noticeBean.getCount()<2){
                rm.setCode(ReturnCodeConstant.NO_PERMISSION);
                rm.setNoticeData(tytNoticePopupTemplService.getByType(PopupTypeEnum.货app第二次注销提示.getType1(),PopupTypeEnum.货app第二次注销提示.getType2()));
                return rm;
            }
            rm.setCode(ReturnCodeConstant.NO_PERMISSION);
            rm.setNoticeData(tytNoticePopupTemplService.getByType(PopupTypeEnum.货app第三次注销提示.getType1(),PopupTypeEnum.货app第三次注销提示.getType2()));
            return rm;
        }catch (Exception e){
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 首次注销取消赠送权益
     * @param userId 用户id
     * @return rm
     */
    @RequestMapping(value = {"giveCancelGoods"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean giveCancelGoods(Long userId){
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK, "操作成功");
        try{
            if (userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID不能为空！");
                return rm;
            }
            User user = userService.getByUserId(userId);
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户信息不存在！");
                return rm;
            }
            //获取用户手机号
            String cellPhone = user.getCellPhone();
            UserCancelNoticeBean noticeBean = userCancelService.getCountByCellPhoneAndGoodsPort(cellPhone);
            if (noticeBean!=null && noticeBean.getCount()>0){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("该账号之前注销过，不能赠送权益");
                return rm;
            }
            Date startTime = TimeUtil.dateAddMonth(TimeUtil.today(),-3);
            if (userBuyGoodsService.isGivingRight(userId, GoodsType.注销赠送商品.getId(),startTime,new Date())){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("该账号已赠送过权益！");
                return rm;
            }
            userPermissionService.giveGoodsCard(userId,GoodsType.注销赠送商品, PermissionChangeType.赠送,"userCancel", PermissionGainTypeEnum.赠送);
        }catch (Exception e){
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 获取是第几次注销
     * @param userId 用户id
     * @return rm
     */
    @RequestMapping(value = {"getGoodsCancelCount","getGoodsCancelCount.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getGoodsCancelCount(Long userId){
        ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.OK, "查询成功");
        try{
            if (userId == null) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("用户ID不能为空！");
                return rm;
            }
            User user = userService.getByUserId(userId);
            if (user == null) {
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("用户信息不存在！");
                return rm;
            }
            //获取用户手机号
            String cellPhone = user.getCellPhone();
            UserCancelNoticeBean noticeBean = userCancelService.getCountByCellPhoneAndGoodsPort(cellPhone);
            if (noticeBean == null){
                rm.setData(0);
                return rm;
            }
            rm.setData(noticeBean.getCount());
        }catch (Exception e){
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }






}
