package com.tyt.infofee.controller;

import java.util.List;

import javax.annotation.Resource;

import com.tyt.util.TytSwitchUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.infofee.service.TytUserBankcardService;
/**
 * app银行卡
 *
 */
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytUserBankcard;
import com.tyt.util.ReturnCodeConstant;
@Controller
@RequestMapping("/plat/bankcard/")
public class UserBankcardController extends BaseController {

	@Resource(name = "tytUserBankcardService")
	private TytUserBankcardService tytUserBankcardService;
	
	/**
	 * 获取用户所有银行卡
	 * @param userId
	 * @return
	 */
	@RequestMapping("/getBankcard")
	@ResponseBody
	public ResultMsgBean getBankcard(@RequestParam(value="userId", required=true) Long userId) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			List<TytUserBankcard> bean=tytUserBankcardService.getByUserId(userId);
			result.setCode(ReturnCodeConstant.OK);
			result.setData(bean);
			result.setMsg("银行卡获取成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器异常");
		}
		return result;
		
	}
	/**
	 * 银行卡保存接口
	 * @param userId 用户id
	 * @param card_num  银行卡号
	 * @return
	 */
	@RequestMapping("/saveBankcard")
	@ResponseBody
	public ResultMsgBean saveBankcard(@RequestParam(value="userId", required=true) Long userId,
										@RequestParam(value="cardNum", required=true) String cardNum) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			//是否启用tpay收银台版本(true:是,false:否)
			boolean isTpayVersion = TytSwitchUtil.isTpayVersion();
			if(isTpayVersion){
				result.setCode(ReturnCodeConstant.ERROR);
				result.setMsg("请使用最新版本进行操作！");
				return result;
			}

			if (StringUtils.length(cardNum) < 10) {
				result.setCode(ReturnCodeConstant.ERROR);
				result.setMsg("银行卡号错误");
				return result;
			}
			String flg=tytUserBankcardService.saveBankcard(userId,cardNum);
			if(flg != null) {
				result.setCode(ReturnCodeConstant.OK);
				result.setData(flg);
				result.setMsg("银行卡添加成功");
			}else {
				result.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
				result.setMsg("银行卡重复添加");
			}
		} catch (Exception e) {
			logger.error("saveBankcard 服务器异常", e);
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器异常");
		}
		return result;
		
	} 
	/**
	 * 银行卡删除操作
	 * @param bankcardId 银行卡自增id
	 * @return
	 */
	@RequestMapping("/deleteBankcard")
	@ResponseBody
	public ResultMsgBean deleteBankcard(@RequestParam(value="bankcardId", required=true) Long bankcardId) {
		ResultMsgBean result=new ResultMsgBean();
		try {
			tytUserBankcardService.deleteBankcard(bankcardId);
			result.setCode(ReturnCodeConstant.OK);
			result.setMsg("银行卡删除成功");
		} catch (Exception e) {
			logger.error("deleteBankcard 服务器异常", e);
			result.setCode(ReturnCodeConstant.ERROR);
			result.setMsg("服务器异常");
		}
		return result;
		
	} 
}

