package com.tyt.infofee.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.infofee.service.TransportBackendSTService;
import com.tyt.model.*;
import com.tyt.plat.entity.base.OwnerCompanyLog;
import com.tyt.receive.service.OwnerCompanyLogService;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/plat/infoFee/Backend/")
public class TransportBackendSTController extends BaseController {

    @Resource(name = "transportBackendSTService")
    private TransportBackendSTService transportBackendSTService;
    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;
    @Autowired
    private OwnerCompanyLogService ownerCompanyLogService;

    /**
     * 山推取消订单，运输公司确认接口，下架货源
     * @param id
     * @param userId
     * @return
     */
    @RequestMapping(value = "cancel")
    @ResponseBody
    public ResultMsgBean cancel(Long id,Long userId) throws Exception {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 参数验证
            if (id == null || id.longValue() <= 0L) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("goodsId参数错误");
                return rm;
            }
            TytTransportBackend transportBackend = transportBackendSTService.selectById(id);
            if (transportBackend == null){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("id参数错误");
                return rm;
            }
            //确认山推端是否已取消
            if(transportBackend.getStatus() != 2 ){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("上游货主未取消");
                return rm;
            }
            if (transportBackend.getCancelConfirm() == 1){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("重复取消");
                return rm;
            }
            rm = transportBackendSTService.confirm(transportBackend,id,userId);
        }catch (Exception e){
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("信息错误");
            return rm;
        }
      return rm;
    }

    /**
     * 车方装货/卸货完成接口
     * @param tsId tyt_transport_backend表  src_msg_id
     * @param status 状态 1-待接单 2-已取消 3-已接单 4-已完成 5-无效 6-货源已成交 7-已装货 8-已卸货
     * @param orderStatus 10-待接单 20-已取消 30-已接单未发布 31-已接单发布中 32-已接单撤销 33-已接单过期 40-已完成 50-后台设置为无效 60-货源已成交 70-已装货 80-已卸货
     * @return
     * @throws Exception
     */

    @RequestMapping(value = "unload")
    @ResponseBody
    public ResultMsgBean unload(Long tsId , Integer status , Integer orderStatus) throws Exception {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            // 参数验证
            if (tsId == null || tsId <= 0) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("tsId参数错误");
                return rm;
            }
            if (status == null || status <= 0) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("status参数错误");
                return rm;
            }
            if (orderStatus == null || orderStatus <= 0) {
                rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                rm.setMsg("orderStatus参数错误");
                return rm;
            }
            TytTransportBackend backend = transportBackendSTService.selectbackendById(tsId);
            if (backend == null){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("参数错误");
                return rm;
            }
            if (backend.getStatus() == 2 ){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("此货源已取消，无法操作，请返回到企业货源进行“确定取消订单”操作");
                return rm;
            }
            if (backend.getStatus() == 8){
                rm.setCode(ReturnCodeConstant.ERROR);
                rm.setMsg("此货源已卸货，无法操作");
                return rm;
            }
            transportBackendSTService.updateUnloadById(backend.getId(),status,orderStatus);

            //记录企业货源状态流转日志
            OwnerCompanyLog ownerCompanyLog = new OwnerCompanyLog();
            ownerCompanyLog.setOrderNo(backend.getOrderNo());
            ownerCompanyLog.setBackendId(backend.getId());
            ownerCompanyLog.setCompanyId(backend.getReceiverUserId());
            ownerCompanyLog.setEnterpriseId(backend.getAppletsUserId());
            ownerCompanyLog.setStatus(status);
            ownerCompanyLog.setOrderStatus(orderStatus);
            ownerCompanyLog.setSrcMsgId(backend.getSrcMsgId());
            ownerCompanyLogService.addOwnerCompanyLog(ownerCompanyLog);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setMsg("订单状态已更改，操作已同步上游货主");
        }catch (Exception e){
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }




}
