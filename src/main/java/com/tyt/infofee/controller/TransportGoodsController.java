package com.tyt.infofee.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.gexin.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytBubbleService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.config.util.AppConfig;
import com.tyt.deposit.bean.RefreshContentMinCountDto;
import com.tyt.deposit.entity.base.TytDepositAuthorization;
import com.tyt.deposit.enums.AuthStatusEnum;
import com.tyt.deposit.pojo.dto.DepositBlockCheckDTO;
import com.tyt.deposit.service.DepositService;
import com.tyt.deposit.service.TytGoodsRefreshUserService;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetail;
import com.tyt.excellentgoodshomepage.service.ExcellentGoodsService;
import com.tyt.infofee.bean.*;
import com.tyt.infofee.enums.*;
import com.tyt.infofee.service.InfoFeeBusinessService;
import com.tyt.infofee.service.TytTransportDispatchViewService;
import com.tyt.infofee.service.impl.BackoutReasonServiceImpl;
import com.tyt.invoicetransport.service.InvoiceTransportService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.orderlimit.bean.AcceptOrderLimitInfo;
import com.tyt.orderlimit.service.AcceptOrderLimitRecordService;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.client.trade.infofee.ApiTradeInfoFeeClient;
import com.tyt.plat.client.trade.infofee.dto.UserPerformanceNumDTO;
import com.tyt.plat.client.transport.TransportTecserviceFeeClient;
import com.tyt.plat.client.transport.dto.CarpoolMatchDTO;
import com.tyt.plat.client.transport.dto.CheckIsNeedFreeTecServiceFeeVO;
import com.tyt.plat.client.user.ApiUserInvoiceClient;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.constant.RedisKeyConstant;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.enums.SaveDirectOptEnum;
import com.tyt.plat.mapper.base.*;
import com.tyt.plat.service.user.ApiInvoiceEnterpriseService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.invoice.ThirdDominantInfoVo;
import com.tyt.plat.vo.other.GoodsSmallDO;
import com.tyt.plat.vo.ts.SaveDirectReq;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.plat.vo.ts.TransportUpdateDataReq;
import com.tyt.restrictapp.service.AppLimitLogService;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.enums.ExcellentCardTypeEnum;
import com.tyt.transport.enums.ExcellentGoodsEnums;
import com.tyt.transport.enums.PubTypeEnum;
import com.tyt.transport.enums.UseCarTypeEnum;
import com.tyt.transport.enums.YesOrNoEnum;
import com.tyt.transport.querybean.CallLogBean;
import com.tyt.transport.querybean.TransportCollectBean;
import com.tyt.transport.querybean.TransportDoneRequest;
import com.tyt.transport.service.*;
import com.tyt.transportquotedprice.bean.TytTransportQuotedPrice;
import com.tyt.transportquotedprice.bean.TytTransportQuotedPriceCarVO;
import com.tyt.transportquotedprice.service.TransportQuotedPriceService;
import com.tyt.upgrade.service.UpgradeCheckService;
import com.tyt.user.bean.CarSaveBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserCallPhoneRecordService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.tyt.util.Constant.EXCELLENT_GOODS_PUBLISH_TIME;

@Controller
@RequestMapping("/plat/infoFee/transport/")
@Slf4j
public class TransportGoodsController extends BaseController {
    private static final String RECOMMEND_USER_SCAN_KEY = "recommend_user_scan_key_";
    private static final long ONE_DAY = 24 * 60 * 60;
    private static final String RECOMMEND_USER_SCAN_TIMES = "recommend_user_scan_times_";

    private static final String TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY = "transportQuotedPriceCarLeave";
    // requestSource : 撤销并重发
    private static final int CANCEL_AND_REPUBLISH = 1;
    // 操作类型：撤销货源
    private static final int OPERATE_TYPE_CANCEL_GOODS = 1;

    @Resource(name = "transportMainService")
    private TransportMainService transportMainService;

    @Resource(name = "transportBusiness")
    private TransportBusinessInterface transportBusiness;

    @Resource(name = "tytBubbleService")
    TytBubbleService tytBubbleService;

    @Resource(name = "infoFeeBusinessService")
    private InfoFeeBusinessService infoFeeBusinessService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "transportLogService")
    TransportLogService transportLogService;

    @Resource(name = "transportViewLogService")
    TransportViewLogService transportViewLogService;

    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "tytUserSubService")
    private TytUserSubService tytUserSubService;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;

    @Resource(name = "upgradeCheckService")
    private UpgradeCheckService upgradeCheckService;

    @Autowired
    private BackendTransportMapper backendTransportMapper;

    @Resource(name = "bsPublishTransportService")
    private BsPublishTransportService bsPublishTransportService;

    @Resource(name = "tytTransportSyncYmmService")
    private TytTransportSyncYmmService tytTransportSyncYmmService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Autowired
    private TytTransportDispatchViewService tytTransportDispatchViewService;

    @Resource(name = "threadPoolExecutor")
    private ThreadPoolExecutor threadPoolExecutor;

    @Resource(name = "depositService")
    private DepositService depositService;

    @Autowired
    private AcceptOrderLimitRecordService acceptOrderLimitRecordService;

    @Autowired
    private ExcellentGoodsService excellentGoodsService;

    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    @Autowired
    private TytGoodsRefreshUserService tytGoodsRefreshUserService;

    @Autowired
    private TytUserCallPhoneRecordService tytUserCallPhoneRecordService;

    @Autowired
    private InvoiceTransportService invoiceTransportService;

    @Autowired
    private TransportEventTrickingService transportEventTrickingService;

    @Autowired
    private TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    @Autowired
    private TransportTecserviceFeeClient transportTecserviceFeeClient;

    @Autowired
    private ApiInvoiceEnterpriseService apiInvoiceEnterpriseService;

    @Autowired
    private ApiUserInvoiceClient apiUserInvoiceClient;

    @Autowired
    private SpecialCarDispatchFailureService specialCarDispatchFailureService;

    @Autowired
    private TytTransportQuotedPriceMapper transportQuotedPriceMapper;


    @Autowired
    private TytSpecialCarDispatchDetailMapper  specialCarDispatchDetailMapper;


    //技术服务费取值范围 最大值和最小值
    private static final BigDecimal TEC_SERVICE_FEE_MIN = BigDecimal.valueOf(0.00);
    private static final BigDecimal TEC_SERVICE_FEE_MAX = BigDecimal.valueOf(999999.00);

    private static final String TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY = "transportQuotedPriceCount";

    private static final String TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY = "transportQuotedPriceCountDetainment";
    private static final int CANCEL_AND_REPUBLISH_VALUE = 13;
    private static final String CANCEL_AND_REPUBLISH_KEY = "撤销并重发";
    @Autowired
    private TytTransportCancelLogMapper tytTransportCancelLogMapper;
    @Autowired
    private TransportPublishLogService transportPublishLogService;

    @Autowired
    private BackoutReasonServiceImpl backoutReasonService;

    @Autowired
    private TytAppCallLogMapper tytAppCallLogMapper;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

    @Autowired
    private TytTransportEnterpriseLogMapper transportEnterpriseLogMapper;
    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;
    @Autowired
    private GoodsRefreshManualService goodsRefreshManualService;

    @Autowired
    private AppLimitLogService appLimitLogService;

    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;

    @Autowired
    private TytTransportSyncYmmMapper tytTransportSyncYmmMapper;


    /**
     * 我的货源列表查询接口
     *
     * @param userId          用户id
     * @param queryID         下拉【第一条id】、下滑【最后一条的id】
     * @param queryMenuType   状态：1、发布中 2、已撤销 3、已过期 4、已成交(新增)5发布中（新版本，订单上平台使用）
     * @param queryActionType 下拉/上滑
     * @return
     */
    @RequestMapping(value = "getMyPublish")
    @ResponseBody
    public ResultMsgBean getMyPublish(Long userId, Long queryID, Integer queryMenuType, Integer queryActionType, Integer quotedPriceQueryType, Integer callQueryType, BaseParameter parameter) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            if (userId == null) {
                logger.error("user_id_is_empty: " + JSON.toJSONString(parameter));
                return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
            }
            if (queryID == null || queryID.longValue() < 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("queryID参数错误");
                return resultMsgBean;
            }
            if (queryMenuType == null || queryMenuType < 1 || queryMenuType > 5) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("queryMenuType参数错误");
                return resultMsgBean;
            }
            if (queryActionType == null || queryActionType < 1 || queryActionType > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("queryActionType参数错误");
                return resultMsgBean;
            }
            ListDataBean listDataBean = new ListDataBean();
            /* 获取列表数据 */
            long t1 = System.currentTimeMillis();
            //货源聚合信息实体类
            TransportCollectBean transportCollectBean = transportMainService.getInfoFeeMyPublish(queryMenuType, userId, queryActionType, queryID, parameter.getClientSign(), quotedPriceQueryType, callQueryType);
            if (transportCollectBean != null) {
                List<InfoFeeMyPublishGoodsResultBean> goodsList = transportCollectBean.getGoodsList();
                //检查信用曝光按钮开关状态 V6290
                checkCreditRetopOnoff(goodsList);
                listDataBean.setData(goodsList);

                //6420新增发布中货源展示每个货源的被出价次数
                if (queryMenuType == 1 || queryMenuType == 5) {
                    //发布中的货源的被出价次数
                    makeCarTransportQuotedPriceTimes(goodsList);
                    //发布中货源构造加价按钮
                    makeAddMoneyButton(goodsList);
                    //发布中货源构造开票货源的指派车方信息
                    makeInvoiceTransportAssignCarTel(goodsList);
                }

                makeInstantGrabData(goodsList, queryMenuType);

            }
            /* 获取当前时间 */
            listDataBean.setCurrentTime(System.currentTimeMillis());
            /* 获取气泡数量 */
            List<InfoFeeMyPublishBubbleResultBean> myBubbleList = new ArrayList<InfoFeeMyPublishBubbleResultBean>();
            //气泡数量根据新旧接口开关判断
            boolean newInfofeeVersion = TytSwitchUtil.isNewInfofeeVersion();
            //是否开启信息费新版本 true是 false否 (2019-01-04 信息费改版)
            if (newInfofeeVersion == true) {
                myBubbleList = tytBubbleService.getInfoFeeMyPublishBubbleResultBeanForUserIdNew(userId);
            } else {
                myBubbleList = tytBubbleService.getInfoFeeMyPublishBubbleResultBeanForUserId(userId);
            }
            listDataBean.setBubbleNumbers(myBubbleList);
            // 返回结果
            resultMsgBean.setData(listDataBean);
            long t2 = System.currentTimeMillis();
            logger.info("APP信息费我的货源查询时间【{}ms】;用户【{}】;菜单【{}】;下拉上滑【{}】;查询ID【{}】", t2 - t1, userId, queryMenuType, queryActionType, queryID);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    private void makeInvoiceTransportAssignCarTel(List<InfoFeeMyPublishGoodsResultBean> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return;
        }
        for (InfoFeeMyPublishGoodsResultBean infoFeeMyPublishGoodsResultBean : goodsList) {
            if (infoFeeMyPublishGoodsResultBean != null && infoFeeMyPublishGoodsResultBean.getInvoiceTransport() != null && infoFeeMyPublishGoodsResultBean.getInvoiceTransport() == 1) {
                TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(infoFeeMyPublishGoodsResultBean.getSrcMsgId());
                if (transportEnterpriseLog !=null) {
                    infoFeeMyPublishGoodsResultBean.setAssignCarTel(transportEnterpriseLog.getAssignCarTel());
                }
            }
        }
    }

    /**
     * 处理货源字段，如给专票货源打标；给专车货源打标
     *
     * @param transportBaseList
     */
    public void handleTransportFields(List<InfoFeeMyPublishGoodsResultBean> transportBaseList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(transportBaseList)) {
            return;
        }
        // 给专票货源打标
        List<Long> srcMsgIds = transportBaseList.stream().
                filter(t -> Objects.equals(t.getInvoiceTransport(), 1))
                .map(InfoFeeMyPublishGoodsResultBean::getSrcMsgId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(srcMsgIds)) {
            Map<Long, Long> valueMap = transportEnterpriseLogMapper.getInvoiceSubjectIdBySrcMsgIds(srcMsgIds).stream()
                    .collect(Collectors.toMap(TytTransportEnterpriseLog::getSrcMsgId, TytTransportEnterpriseLog::getInvoiceSubjectId));
            transportBaseList.forEach(t -> t.setInvoiceSubjectId(valueMap.get(t.getSrcMsgId())));
        }

        // 给专车货源打标
        srcMsgIds = transportBaseList.stream().
                filter(t -> Objects.equals(t.getExcellentGoods(), 2))
                .map(InfoFeeMyPublishGoodsResultBean::getSrcMsgId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(srcMsgIds)) {
            Map<Long, Integer> valueMap = tytTransportMainExtendMapper.getUseCarTypeBySrcMsgIds(srcMsgIds).stream()
                    .collect(Collectors.toMap(TytTransportMainExtend::getSrcMsgId, TytTransportMainExtend::getUseCarType));
            transportBaseList.forEach(t -> t.setUseCarType(valueMap.get(t.getSrcMsgId())));
        }

        // 只有好货展示读秒货源标签，非好货把捂货过期时间置为空就隐藏了
        for (InfoFeeMyPublishGoodsResultBean transportListVO : transportBaseList) {
            if (transportListVO.getLabelJson() == null || !transportListVO.getLabelJson().contains("\"iGBIResultData\":1")) {
                transportListVO.setPriorityRecommendExpireTime(null);
            }
        }

    }

    private void makeAddMoneyButton(List<InfoFeeMyPublishGoodsResultBean> goodsList) {
        for (InfoFeeMyPublishGoodsResultBean infoFeeMyPublishGoodsResultBean : goodsList) {
            if (org.apache.commons.lang3.StringUtils.isBlank(infoFeeMyPublishGoodsResultBean.getPrice()) || infoFeeMyPublishGoodsResultBean.getPrice().equals("0")) {
                continue;
            }

            Long tsId = infoFeeMyPublishGoodsResultBean.getSrcMsgId();

            TransportMain transportMainForId = transportMainService.getBySrcMsgId(tsId);

            Date lastAddMoneyTime = tytAppCallLogMapper.getLastAddMoneyTime(tsId);

            Date lastTime;
            if (lastAddMoneyTime == null && (transportMainForId == null || transportMainForId.getCtime() == null)) {
                lastTime = null;
            } else if (lastAddMoneyTime == null) {
                lastTime = transportMainForId.getCtime();
            } else if ((transportMainForId == null || transportMainForId.getCtime() == null) || lastAddMoneyTime.compareTo(transportMainForId.getCtime()) > 0) {
                lastTime = lastAddMoneyTime;
            } else {
                lastTime = transportMainForId.getCtime();
            }

            int viewLogCount = tytAppCallLogMapper.getViewLogCountBySrcMsgId(tsId);

            int callLogCount = tytAppCallLogMapper.getCallLogCountBySrcMsgId(tsId);

            long lastTimeDiff = 0;
            if (lastTime != null) {
                lastTimeDiff = Duration.between(lastTime.toInstant(), new Date().toInstant()).toMinutes();
            }


            if (transportMainForId.getPublishType() != null && transportMainForId.getPublishType() == 2) {
                //一口价
                if (lastTimeDiff >= 30 || viewLogCount > 10) {
                    infoFeeMyPublishGoodsResultBean.setAddMoneyButtonStyle(2);
                    continue;
                }
            } else {
                //电议有价
                if (lastTimeDiff >= 30 || viewLogCount > 10 || callLogCount == 0) {
                    infoFeeMyPublishGoodsResultBean.setAddMoneyButtonStyle(2);
                    continue;
                }
            }
            infoFeeMyPublishGoodsResultBean.setAddMoneyButtonStyle(1);
        }
    }

    private void makeInstantGrabData(List<InfoFeeMyPublishGoodsResultBean> goodsList, Integer queryMenuType) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return;
        }
        for (InfoFeeMyPublishGoodsResultBean infoFeeMyPublishGoodsResultBean : goodsList) {
            if (infoFeeMyPublishGoodsResultBean != null) {
                infoFeeMyPublishGoodsResultBean.setInstantGrab(0);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(infoFeeMyPublishGoodsResultBean.getLabelJson())) {
                    TransportLabelJson transportLabelJson = transportBusiness.getTransportLabelJson(infoFeeMyPublishGoodsResultBean.getLabelJson());
                    if (transportLabelJson != null && transportLabelJson.getInstantGrab() != null && transportLabelJson.getInstantGrab() == 1) {
                        infoFeeMyPublishGoodsResultBean.setInstantGrabResendOverWord(tytConfigService.getStringValue("transport_instant_grab_over_word", "好货权益：有价货源将有更多频次的自动刷新"));
                        infoFeeMyPublishGoodsResultBean.setInstantGrab(1);

                        String instantGrabResendOverCacheKey = CommonUtil.joinRedisKey("tyt:cache:task:instantGrabResendOver", infoFeeMyPublishGoodsResultBean.getSrcMsgId().toString(), DateUtil.dateToString(new Date(), DateUtil.day_format_short));

                        if (queryMenuType == 1 || queryMenuType == 5) {
                            if (RedisUtil.get(instantGrabResendOverCacheKey) == null) {
                                //刷新未结束
                                RefreshContentMinCountDto refreshContentMinCountDto = tytGoodsRefreshUserService.selectMinIntervalAndThisFrequencyByUserId(infoFeeMyPublishGoodsResultBean.getUserId());
                                if (refreshContentMinCountDto != null
                                        && org.apache.commons.lang3.StringUtils.isNotBlank(refreshContentMinCountDto.getMinInterval())
                                        && refreshContentMinCountDto.getTotalTime() != null && refreshContentMinCountDto.getTotalTime() != 0) {
                                    //如果是优车好货（秒抢货源）并且刷新未结束则展示对应的最小刷新间隔和刷新次数
                                    infoFeeMyPublishGoodsResultBean.setInstantGrabResendOverWord("好货权益：最快" + refreshContentMinCountDto.getMinInterval() + "分钟/次自动刷新，持续" + refreshContentMinCountDto.getTotalTime() + "分钟");
                                }
                            }
                        }
                    }
                }
            }
        }
        handleTransportFields(goodsList);
    }

    private void makeCarTransportQuotedPriceTimes(List<InfoFeeMyPublishGoodsResultBean> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return;
        }
        for (InfoFeeMyPublishGoodsResultBean infoFeeMyPublishGoodsResultBean : goodsList) {
            if (transportQuotedPriceService.checkTransportValidityV2(infoFeeMyPublishGoodsResultBean.getSrcMsgId()).getCode() != 200) {
                infoFeeMyPublishGoodsResultBean.setTransportQuotedPriceTimes(0);
                continue;
            }
            try {
                infoFeeMyPublishGoodsResultBean.setTransportQuotedPriceTimes(transportQuotedPriceService.getTransportQuotedPriceCountBySrcMsgIs(infoFeeMyPublishGoodsResultBean.getSrcMsgId()));
            } catch (Exception e) {
                logger.info("构造发布中货源每个货源被报价总次数数据失败");
            }
        }
    }

    /**
     * 检查信用曝光按钮开关状态,关闭状态将creditRetop赋值为0
     *
     * @param goodsList
     */
    private void checkCreditRetopOnoff(List<InfoFeeMyPublishGoodsResultBean> goodsList) {
        Integer onOff = tytConfigService.getIntValue(Constant.PUBLISH_LIST_CREDIT_RETOP_ONOFF, 0);
        //开关关闭,信用曝光按钮不显示
        if (onOff.equals(0)) {
            goodsList.stream().forEach(g -> {
                g.setCreditRetop(0);
            });
        }
    }

    /**
     * 1撤销货源/2设置成交
     *
     * @param userId
     * @param goodsId
     * @param operateType
     * @return
     */
    //该方法可能已经弃用，请使用其他方法代替，如果确认弃用，请在此补充
    @Deprecated
    @RequestMapping(value = "saveGoodsStatus")
    @ResponseBody
    public ResultMsgBean saveBtnStatus(Long userId, Long goodsId, Integer operateType) {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(goodsId)) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }
        logger.warn("Deprecated_check_use ############# ");
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            // 参数验证
            if (goodsId == null || goodsId.longValue() <= 0l) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("goodsId参数错误");
                return resultMsgBean;
            }
            if (operateType == null || operateType < 1 || operateType > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("operateBtnType参数错误");
                return resultMsgBean;
            }

            long t1 = System.currentTimeMillis();
            // 撤销、成交操作
            logger.info("APP信息费撤销/成交开始");
            resultMsgBean = transportBusiness.saveInfoFeeUpdateBtnStatus(userId, operateType, goodsId, resultMsgBean);
            long t2 = System.currentTimeMillis();
            logger.info("APP信息费撤销/成交时间【{}ms】,参数userId:【{}】,goodsId:【{}】,operateType:【{}】(1撤销货源 2设置成交)", t2 - t1, userId, goodsId, operateType);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }
    }

    /**
     * @return com.tyt.model.ResultMsgBean
     * @Description 1撤销货源/2设置成交
     * <AUTHOR>
     * @Date 2018/12/24 18:50
     * @Param [userId, goodsId, operateType]
     * @Param requestSource 1-撤销并重发
     **/
    @RequestMapping(value = {"saveGoodsStatusNewForAssignCar", "saveGoodsStatusNewForAssignCar.action"})
    @ResponseBody
    public ResultMsgBean saveGoodsStatusNewForAssignCar(Long userId, Long goodsId, String backoutReasonKey,
                                            Integer backoutReasonValue) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            // 参数验证
            if (goodsId == null || goodsId.longValue() <= 0l) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("goodsId参数错误");
                return resultMsgBean;
            }

            long t1 = System.currentTimeMillis();
            // 撤销、成交操作
            logger.info("APP信息费撤销开始");
            TransportMain originTransport = transportMainService.getTransportMainForId(goodsId);
            TransportDoneRequest doneRequest = new TransportDoneRequest();
            resultMsgBean = transportBusiness.saveInfoFeeUpdateBtnStatusNew(userId, 1, goodsId, doneRequest, backoutReasonKey, backoutReasonValue, null, null, true, null, null);
            logger.info("saveGoodsStatusNewForAssignCar, goodsId:{}, result:{}", goodsId, JSON.toJSONString(resultMsgBean));
            if (ReturnCodeConstant.OK == resultMsgBean.getCode()) {
                //撤销时 清空加价次数
                //获取加价次数
                TransportMain transportMain = transportMainService.getTransportMainForId(goodsId);

                if (transportMain != null && null != transportMain.getId()) {
                        transportEventTrickingService.cancelPublish(transportMain);
                }
                // 记录货源撤销原因记录
                recordCancelLog(userId, 1, backoutReasonKey, backoutReasonValue, originTransport);
            }

            long t2 = System.currentTimeMillis();
            logger.info("APP信息费撤销/成交时间【{}ms】,参数userId:【{}】,goodsId:【{}】,operateType:【{}】(1撤销货源 2设置成交)", t2 - t1, userId, goodsId, 1);
            return resultMsgBean;
        } catch (Exception e) {
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }
    }

    /**
     * @return com.tyt.model.ResultMsgBean
     * @Description 1撤销货源/2设置成交
     * <AUTHOR>
     * @Date 2018/12/24 18:50
     * @Param [userId, goodsId, operateType]
     * @Param requestSource 1-撤销并重发
     **/
    @RequestMapping(value = {"saveGoodsStatusNew", "saveGoodsStatusNew.action"})
    @ResponseBody
    public ResultMsgBean saveGoodsStatusNew(Long userId, Long goodsId, Integer operateType, String backoutReasonKey,
                                            Integer backoutReasonValue, Integer requestSource, TransportDoneRequest doneRequest) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            //好货抢单锁定判断
            if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(goodsId)) {
                return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
            }
            // 参数验证
            if (goodsId == null || goodsId.longValue() <= 0l) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("goodsId参数错误");
                return resultMsgBean;
            }
            if (operateType == null || operateType < 1 || operateType > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("operateBtnType参数错误");
                return resultMsgBean;
            }
            if (Objects.equals(operateType, OPERATE_TYPE_CANCEL_GOODS) && Objects.equals(requestSource, CANCEL_AND_REPUBLISH)) {
                backoutReasonKey = CANCEL_AND_REPUBLISH_KEY;
                backoutReasonValue = CANCEL_AND_REPUBLISH_VALUE;
            }

            long t1 = System.currentTimeMillis();
            // 撤销、成交操作
            logger.info("APP信息费撤销/成交开始");
            TransportMain originTransport = transportMainService.getTransportMainForId(goodsId);
            resultMsgBean = transportBusiness.saveInfoFeeUpdateBtnStatusNew(userId, operateType, goodsId, doneRequest, backoutReasonKey, backoutReasonValue, null, null, true, null, null);
            logger.info("saveGoodsStatusNew, goodsId:{}, result:{}", goodsId, JSON.toJSONString(resultMsgBean));
            if (ReturnCodeConstant.OK == resultMsgBean.getCode()) {
                //撤销时 清空加价次数
                //获取加价次数
                TransportMain transportMain = transportMainService.getTransportMainForId(goodsId);
                if (Objects.nonNull(transportMain)) {
                    String freightAddMoneyNumKey = Constant.FREIGHT_ADD_MONEY_NUM + "_" + userId + "_" + transportMain.getSrcMsgId();
                    String freightAddMoneyNum = RedisUtil.get(freightAddMoneyNumKey);
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(freightAddMoneyNum)) {
                        RedisUtil.del(freightAddMoneyNumKey);
                    }
                }
                if (null != transportMain.getId()) {
                    //发送MQ货源打通满满货源下架
                    TytTransportSyncYmm resultTransport = tytTransportSyncYmmService.findTransportSyncYmm(transportMain.getId());
                    if (null != resultTransport) {
                        MqRevokeTransportSyncMsg syncMsg = new MqRevokeTransportSyncMsg();
                        String messageSerailNum = SerialNumUtil.generateSeriaNum();
                        syncMsg.setMessageType(MqBaseMessageBean.MB_SYNC_TRANSPORT_WAYBILL_MESSAGE);
                        syncMsg.setPartnerSerialNo(IdUtils.getIncreaseIdByLocalTime());
                        syncMsg.setCargoId(resultTransport.getCargoId());
                        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
                        syncMsg.setPartnerRequestTime(format.format(new Date()));
                        syncMsg.setDeleteReason(1);
                        syncMsg.setSrcMsgId(goodsId);
                        syncMsg.setMessageSerailNum(messageSerailNum);
                        tytMqMessageService.addSaveMqMessage(messageSerailNum, com.alibaba.fastjson.JSON.toJSONString(syncMsg), syncMsg.getMessageType());
                        tytMqMessageService.sendMbMqMessage(messageSerailNum, com.alibaba.fastjson.JSON.toJSONString(syncMsg), syncMsg.getMessageType());
                    }
                    if (null != resultTransport) {
                        //更改同步货源下架状态
                        tytTransportSyncYmmService.updateTransportSyncYmmStatus(resultTransport);
                    }
                    // 撤销埋点
                    if (operateType == 1) {
                        transportEventTrickingService.cancelPublish(transportMain);
                    }
                }
                // 处理专车货源工单状态
                specialCarDispatchFailureService.processWorkOrderStatus(transportMain);
                // 记录货源撤销原因记录
                recordCancelLog(userId, operateType, backoutReasonKey, backoutReasonValue, originTransport);
            }

            long t2 = System.currentTimeMillis();
            logger.info("APP信息费撤销/成交时间【{}ms】,参数userId:【{}】,goodsId:【{}】,operateType:【{}】(1撤销货源 2设置成交)", t2 - t1, userId, goodsId, operateType);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }
    }





    /**
     * 记录货源撤销记录
     *
     * @param userId
     * @param operateType
     * @param backoutReasonKey
     * @param backoutReasonValue
     * @param transportMain
     */
    private void recordCancelLog(Long userId, Integer operateType, String backoutReasonKey, Integer backoutReasonValue, TransportMain transportMain) {
        try {
            if (Objects.equals(operateType, OPERATE_TYPE_CANCEL_GOODS)) {
                TytTransportCancelLog cancelLog = new TytTransportCancelLog();
                cancelLog.setUserId(userId);
                cancelLog.setSrcMsgId(transportMain.getSrcMsgId());
                cancelLog.setTransportJson(JSONObject.toJSONString(transportMain));
                cancelLog.setBackoutReasonKey(backoutReasonKey);
                cancelLog.setBackoutReasonValue(backoutReasonValue);
                cancelLog.setCancelTime(new Date());
                tytTransportCancelLogMapper.insertSelective(cancelLog);
            }
        } catch (Exception e) {
            log.error("saveGoodsStatusNew recordCancelLog error, srcMsgId:{}", transportMain.getId(), e);
        }
    }

    /**
     * @return com.tyt.model.ResultMsgBean
     * @Description 1撤销货源/2设置成交
     * <AUTHOR>
     * @Date 2018/12/24 18:50
     * @Param [userId, goodsId, operateType]
     **/
    @RequestMapping(value = {"saveGoodsStatusNewForPc", "saveGoodsStatusNewForPc.action"})
    @ResponseBody
    public ResultMsgBean saveGoodsStatusNewForPc(Long userId, Long goodsId, Integer operateType, String backoutReasonKey, Integer backoutReasonValue, TransportDoneRequest doneRequest) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        logger.info("saveGoodsStatusNewForPc method userId 【{}】,goodsId 【{}】 operateType 【{}】 backoutReasonKey 【{}】 backoutReasonValue 【{}】 doneRequest 【{}】", userId, goodsId, operateType, backoutReasonKey, backoutReasonValue, JSON.toJSONString(doneRequest));
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(goodsId)) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }
        try {
            // 参数验证
            if (goodsId == null || goodsId.longValue() <= 0l) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("goodsId参数错误");
                return resultMsgBean;
            }
            if (operateType == null || operateType < 1 || operateType > 2) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("operateBtnType参数错误");
                return resultMsgBean;
            }

            long t1 = System.currentTimeMillis();
            // 撤销、成交操作
            logger.info("APP信息费撤销/成交开始");
            resultMsgBean = transportBusiness.saveInfoFeeUpdateBtnStatusNew(userId, operateType, goodsId, doneRequest, backoutReasonKey, backoutReasonValue, null, null, true, null, null);
            if (ReturnCodeConstant.OK == resultMsgBean.getCode()) {
                //撤销时 清空加价次数
                //获取加价次数
                TransportMain transportMain = transportMainService.getTransportMainForId(goodsId);
                if (Objects.nonNull(transportMain)) {
                    String freightAddMoneyNumKey = Constant.FREIGHT_ADD_MONEY_NUM + "_" + userId + "_" + transportMain.getSrcMsgId();
                    String freightAddMoneyNum = RedisUtil.get(freightAddMoneyNumKey);
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(freightAddMoneyNum)) {
                        RedisUtil.del(freightAddMoneyNumKey);
                    }
                }
                specialCarDispatchFailureService.processWorkOrderStatus(transportMain);
            }
            long t2 = System.currentTimeMillis();
            logger.info("APP信息费撤销/成交时间【{}ms】,参数userId:【{}】,goodsId:【{}】,operateType:【{}】(1撤销货源 2设置成交)", t2 - t1, userId, goodsId, operateType);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("APP信息费撤销/成交开始异常 异常信息为【{}】", e.getMessage());
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("失败");
            return resultMsgBean;
        }
    }

    /**
     * 获取详情
     *
     * @return
     */
    @RequestMapping(value = "getSingleDetail")
    public void getSingleDetail(Long userId, Long goodsId, Integer detailType, String clientVersion, Integer clientSign,
                                String isEncypt, Integer viewSource,
                                // 新增埋点字段
                                String sortType, Integer sortIndex, Integer specialMark,
                                String startProvinc, String startCity, String startArea,
                                String destProvinc, String destCity, String destArea,
                                HttpServletRequest request, HttpServletResponse response, Long sharerUserId) {
        logger.info("getSingleDetail userId is: " + userId + ", detailType is: " + detailType + ", goodsId is: " + goodsId);
        try {
            appLimitLogService.addRequestLog(goodsId, userId, 1);

            String defaultUserId = tytConfigService.getStringValue("tyt_transport_default_user_id", "4");
            int defaultUserIdAppOnoff = tytConfigService.getIntValue("tyt_transport_default_user_id_app_onoff", 0);

            //版本升级防止用户跳过规则操作
            if (upgradeCheckService.checkClientInfo(clientVersion, clientSign)) {
                ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NO_PERMISSION, "使用最新版本操作");
                msgBean.setNoticeData(tytNoticePopupTemplService.getTemplByType(PopupTypeEnum.老版本车主升级提示, null));
                printJSON(request, response, msgBean);
                return;
            }

            // 参数验证
            if (goodsId == null || goodsId.longValue() <= 0) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "goodsId参数错误", null, 0);
                return;
            }
            if (detailType == null || detailType < 1 || detailType > 5) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "detailType参数错误", null, 0);
                return;
            }
            if (userId == null) {
                backResponse(request, response, ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "userId参数错误", null, 0);
                return;
            }
            long t1 = System.currentTimeMillis();
            // 结果获取
            GoodsSingleDetailResultBean result = infoFeeBusinessService.getGoodsInfoDetail(goodsId, userId, detailType);

            if (Constant.ClientSignEnum.isCar(clientSign)){
                int searchPermissionNum = userPermissionService.getSearchPermissionNum(userId);
                if (searchPermissionNum >= 0){
                    result.setSearchPermissionNum(searchPermissionNum);
                }
            }

            if (userId.intValue() != result.getDetailBean().getUserId().intValue()) {
                // 5930 是否开启
                PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.用户昵称显示, userId);
                if (!permissionResult.getUse()) { // 详情页是否显示用户昵称
                    result.getDetailBean().setNickName(Constant.REPLACEMENT_STARTS);
                    result.getDetailBean().setAuthNameTea(null);
                }
                PermissionResult goodPermissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货物内容显示, userId);
                if (!goodPermissionResult.getUse()) { // 详情页是否显示货源内容
                    result.getDetailBean().setTaskContent(Constant.REPLACEMENT_STARTS_CONTENT);
                    result.getDetailBean().setWeight(null);
                    result.getDetailBean().setCarLength(null);
                    result.getDetailBean().setCarType(null);
                    result.getDetailBean().setSpecialRequired(null);
                }
                //往查看记录表中记录数据，展示查看次数，只记录调度发货的
                threadPoolExecutor.execute(() -> {
//				if (result.getDetailBean().getSourceType() == 2) {
                    try {
                        User user = userService.getByUserId(userId);
                        //type 1：查看 2：联系
                        tytTransportDispatchViewService.addTransportView(user, result.getDetailBean().getSrcMsgId(), 1);
                    } catch (Exception e) {
                        logger.error("保存查看详情次数出错，userId:{},goodsId:{}", userId, goodsId, e);
                    }
//				}
                });
                //查询用户是否拨打过该货源
                // if (Objects.equals(result.getDetailBean().getExcellentGoods(), 2)) {
                //     result.setCallingStatus(1); // 非会员可以接单专车货源，app端通过这个字段控制
                // } else {
                    result.setCallingStatus(tytUserCallPhoneRecordService.getCallStatusByUserIdAndTsId(goodsId, userId));
                // }
                // 如果是专车货源，或者是普通货源指派专车司机，那么给前端提示跳过权益校验
                if(checkSkipPermission(goodsId, userId)> 0){
                    result.setSkipPermission(YesOrNoEnum.Y.getCode());
                }
            }
            if (!StringUtils.equals(isEncypt, "pc-web")) {
                encyptNickname(result.getDetailBean(), clientVersion);
            }
            if (StringUtils.equals(isEncypt, "pc-web")) {
                String goodIdEncrypt = XXTea.Encrypt(String.valueOf(result.getDetailBean().getId()), AppConfig.getProperty("tyt.xxtea.key"));
                result.getDetailBean().setGoodsIdEncrypt(goodIdEncrypt);
            }

            //2018-1-12日，修改货源详情当是找货详情、收藏详情、精准货源、有好货的上传 电话 (除了IOS都屏蔽)
            //赋值电话号为11个空格，优化投诉时回显11个0
            String tel = "           ";
            Long user_id = result.getDetailBean().getUserId();
            if (userId != null && user_id != null && userId.equals(user_id)) {
                //查看本人货源时，手机号不做隐藏
            } else {
                //由于目前隐藏用户id开关是开启状态，因此之前代码逻辑可化简（优化手机号显示漏洞）
                PermissionResult creditPermission = userPermissionService.checkAuthPermission(Permission.查信用, userId);
                if (!creditPermission.getUse()) {
                    //没有查看信用权限
                    result.getDetailBean().setCellPhone(tel);
                }

                result.getDetailBean().setTelephoneOne(tel);
                result.getDetailBean().setTelephoneTwo(tel);
                result.getDetailBean().setTelephoneThree(tel);
                result.getDetailBean().setUploadCellPhone(tel);

                if (defaultUserIdAppOnoff == 1) {
                    result.getDetailBean().setUserId(Long.parseLong(defaultUserId));
                    result.getTransportUserBean().setUserId(Long.parseLong(defaultUserId));
                }

            }
            //查询用户接单限制信息
            AcceptOrderLimitInfo acceptOrderLimitInfo = acceptOrderLimitRecordService.getAcceptOrderLimitInfo(userId, goodsId);
            result.setAcceptOrderLimitInfo(acceptOrderLimitInfo);

            long t2 = System.currentTimeMillis();
            logger.info("APP信息费查看详情时间【{}ms】,参数userId:【{}】,goodsId:【{}】,detailType:【{}】(1找货列表/收藏列表的详情 2我的货源列表的详情 3已接单列表的详情)", t2 - t1, userId, goodsId, detailType);
            logger.info("getSingleDetail isPaySuccess is: " + result.getIsPaySuccess() + ", isInfoFee is: " + result.getDetailBean().getIsInfoFee());

            try {
                List<TytTransportQuotedPrice> quotedPriceList = transportQuotedPriceMapper.getQuotedPriceListByTransportMainIdOrderByCarQuotedPriceTime(result.getDetailBean().getSrcMsgId());
                if (CollectionUtils.isNotEmpty(quotedPriceList)) {
                    //构造报价总人数，最近车方报价时间数据
                    result.getDetailBean().setQuotedPriceCount(quotedPriceList.size());
                    result.getDetailBean().setLastQuotedPriceTime(quotedPriceList.get(0).getCarQuotedPriceTime());
                }
                TytTransportMainExtend mainExtend = tytTransportMainExtendMapper.getBySrcMsgId(result.getDetailBean().getSrcMsgId());
                if (null != mainExtend && mainExtend.getPerkPrice() != null){
                    result.getDetailBean().setPerkPrice(mainExtend.getPerkPrice());
                }
                if (userId != null && user_id != null && userId.equals(user_id)) {

                } else {
                    //车方查看货源
                    if (null != mainExtend){
                        if (result.getDetailBean().getPublishType() != null && result.getDetailBean().getPublishType() == 2 && (result.getDetailBean().getInvoiceTransport() == null || result.getDetailBean().getInvoiceTransport() != 1)
                                && (result.getDetailBean().getExcellentGoods() == null || result.getDetailBean().getExcellentGoods() != 2 || (mainExtend.getPriceType() != null && mainExtend.getPriceType() == 2))) {
                            //符合报价条件的一口价货源
                            String transportViewCount = RedisUtil.get(TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY + ":" + userId + "-" + result.getDetailBean().getSrcMsgId());
                            if (transportViewCount != null) {
                                if (Integer.valueOf(transportViewCount) == 1) {
                                    //弹过窗的不再弹，与等10秒弹窗共用同一个缓存
                                    if (!RedisUtil.mapExists(TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY + ":" + userId, goodsId.toString())) {
                                        //车方第二次浏览一口价优车2.0货源并且从未对该货源报过价的，货源详情增加挽留弹窗字段
                                        if (!transportQuotedPriceService.getCarIsHaveQuotedPriceToTransport(userId, result.getDetailBean().getSrcMsgId())) {
                                            result.getDetailBean().setGoodCarPriceTransportDetainment(1);
                                            transportQuotedPriceService.recordCarLeaveTransportSingleDetail(userId, goodsId);
                                        }
                                    }
                                }
                                RedisUtil.set(TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY + ":" + userId + "-" + result.getDetailBean().getSrcMsgId(), (Integer.valueOf(transportViewCount) + 1) + "", 60 * 60 * 24);
                            } else {
                                long secondsUntilTomorrow = Duration.between(java.time.LocalDateTime.now(), java.time.LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).plusDays(1)).get(ChronoUnit.SECONDS);
                                RedisUtil.set(TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY + ":" + userId + "-" + result.getDetailBean().getSrcMsgId(), "1", Integer.parseInt(String.valueOf(secondsUntilTomorrow)));
                            }
                        }
                    }

                }
            } catch (Exception e) {

            }


            SingleGoodsDetailBean detailBean = result.getDetailBean();

            if (detailBean != null) {
                TransportMain transportMainForId = transportMainService.getTransportMainForId(detailBean.getSrcMsgId());
                TransportLabelJson transportLabelJson = transportBusiness.getTransportLabelJson(transportMainForId.getLabelJson());
                if (transportLabelJson != null && transportLabelJson.getCommissionTransport() != null && transportLabelJson.getCommissionTransport() == 1) {
                    TytTransportTecServiceFee tytTransportTecServiceFee = tytTransportTecServiceFeeMapper.getBySrcMsgId(detailBean.getSrcMsgId());
                    if (tytTransportTecServiceFee != null) {
                        UserPermission userPermission = userPermissionService.getUserPermission(userId, UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode());
                        UserPermission userPermissionCarSmallMember = userPermissionService.getUserPermission(userId, UserPermission.PermissionTypeEnum.CAR_SMALL_MEMBER.getCode());
                        if ((null != userPermission && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermission.getStatus()))
                                || (null != userPermissionCarSmallMember && UserPermission.StatusEnum.EFFICIENT.getCode().equals(userPermissionCarSmallMember.getStatus()))) {
                            //有车会员（包含小会员），展示车会员技术服务费
                            detailBean.setTecServiceFee(tytTransportTecServiceFee.getMemberAfterFee());
                            detailBean.setTecServiceFeeAfterDiscount(tytTransportTecServiceFee.getMemberBeforeFee());
                            detailBean.setTecServiceFeeInterestsWord(tytTransportTecServiceFee.getMemberInterestsWord());
                            detailBean.setTecServiceFeeInterestsUrl(tytTransportTecServiceFee.getMemberInterestsUrl());
                            if (tytTransportTecServiceFee.getMemberAfterFee() != null && tytTransportTecServiceFee.getMemberBeforeFee() != null) {
                                detailBean.setTecServiceFeeAfterDiscountDValue(tytTransportTecServiceFee.getMemberBeforeFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()));
                            }
                        } else {
                            //无车会员，展示车会员技术服务费
                            detailBean.setTecServiceFee(tytTransportTecServiceFee.getNoMemberAfterFee());
                            detailBean.setTecServiceFeeAfterDiscount(tytTransportTecServiceFee.getNoMemberBeforeFee());
                            detailBean.setTecServiceFeeInterestsWord(tytTransportTecServiceFee.getNoMemberInterestsWord());
                            detailBean.setTecServiceFeeInterestsUrl(tytTransportTecServiceFee.getNoMemberInterestsUrl());
                            if (tytTransportTecServiceFee.getNoMemberAfterFee() != null && tytTransportTecServiceFee.getNoMemberBeforeFee() != null) {
                                detailBean.setTecServiceFeeAfterDiscountDValue(tytTransportTecServiceFee.getNoMemberBeforeFee().subtract(tytTransportTecServiceFee.getNoMemberAfterFee()));
                            }
                            if (tytTransportTecServiceFee.getMemberAfterFee() != null
                                    && tytTransportTecServiceFee.getNoMemberAfterFee() != null
                                    && tytTransportTecServiceFee.getNoMemberAfterFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()).compareTo(BigDecimal.ZERO) > 0) {
                                detailBean.setTecServiceFeeWord("成为会员本单为您省"
                                        + tytTransportTecServiceFee.getNoMemberAfterFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()).setScale(0).toString()
                                        + "元 去购买");
                                //有小会员
                                List<GoodsSmallDO> userBuyList = bsPublishTransportService.getUserBuyList(userId, 1);
                                if (CollectionUtils.isNotEmpty(userBuyList) && userBuyList.get(0) != null && userBuyList.get(0).getId() != null) {
                                    detailBean.setTecServiceFeeBuySmallMemberId(userBuyList.get(0).getId());
                                } else {
                                    detailBean.setTecServiceFeeBuySmallMemberId(-1L);
                                }
                            }
                        }

                        //判断是否符合直接免佣条件
                        CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeVO = bsPublishTransportService.checkIsNeedFreeTecSericeFeeByCarUser(userId, detailBean.getSrcMsgId());
                        if (checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec()) {
                            detailBean.setTecServiceFeeWord(checkIsNeedFreeTecServiceFeeVO.getWord());
                            detailBean.setTecServiceFee(BigDecimal.ZERO);
                            //折前折后技术服务费差值直接使用折前技术服务费
                            detailBean.setTecServiceFeeAfterDiscountDValue(detailBean.getTecServiceFeeAfterDiscount());
                        }
                    }
                } else {
                    //非抽佣货源技术服务费正常展示
                    detailBean.setTecServiceFeeAfterDiscount(detailBean.getTecServiceFee());
                    detailBean.setTecServiceFeeAfterDiscountDValue(BigDecimal.ZERO);
                }

                //货主端只有代调专车非平台才展示技术服务费，其他的不展示技术服务费
                if (Constant.ClientSignEnum.isGoods(clientSign)) {
                    if (!(SourceTypeEnum.isDispatch(detailBean.getSourceType())
                            && ExcellentGoodsEnum.SPECIAL_CAR_GOODS.getCode().equals(detailBean.getExcellentGoods())
                            && !Objects.equals(detailBean.getCargoOwnerId(), 1L))) {
                        detailBean.setTecServiceFee(null);
                        detailBean.setTecServiceFeeAfterDiscount(null);
                        detailBean.setTecServiceFeeWord(null);
                        detailBean.setTecServiceFeeAfterDiscountDValue(null);
                        detailBean.setTecServiceFeeInterestsUrl(null);
                    }
                }

                if (transportMainForId != null && transportMainForId.getInvoiceTransport() != null && transportMainForId.getInvoiceTransport() == 1) {
                    //开票货源额外返回开票主体ID和开票主体服务商code、是否是本平台开票主体
                    detailBean.setMainSubjectId(false);
                    TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(detailBean.getSrcMsgId());
                    if (transportEnterpriseLog == null) {
                        transportEnterpriseLog = new TytTransportEnterpriseLog();
                    }
                    detailBean.setInvoiceSubjectId(transportEnterpriseLog.getInvoiceSubjectId());
                    detailBean.setServiceProviderCode(transportEnterpriseLog.getServiceProviderCode());
                    String invoiceSujectData = tytConfigService.getStringValue("invoice_subject_data", "1,JCZY");
                    String[] split = invoiceSujectData.split(",");
                    if (transportEnterpriseLog.getInvoiceSubjectId() != null && transportEnterpriseLog.getInvoiceSubjectId().compareTo(Long.valueOf(split[0])) == 0) {
                        detailBean.setMainSubjectId(true);
                    }
                    detailBean.setInvoiceSubjectTag(getInvoiceSubjectTag(transportEnterpriseLog));
                    //XHL开票回显收货人信息
                    detailBean.setConsigneeName(transportEnterpriseLog.getConsigneeName());
                    detailBean.setConsigneeTel(transportEnterpriseLog.getConsigneeTel());
                    detailBean.setConsigneeEnterpriseName(transportEnterpriseLog.getConsigneeEnterpriseName());
                }

                detailBean.setCarCanQuotedPriceToPriceTransport(0);
                //车方查看一口价货源是判断是否可以看到报价相关组件，非一口价货源由app来判断
                Integer priceType = tytTransportMainExtendMapper.getPriceTypeBySrcMsgId(detailBean.getSrcMsgId());
                if ((detailBean.getInvoiceTransport() == null || detailBean.getInvoiceTransport() != 1)
                        && (detailBean.getExcellentGoods() == null || detailBean.getExcellentGoods() != 2 || (priceType != null && priceType == 2))) {
                    //非开票货源、除了灵活运价的专车货源才可报价
                    detailBean.setCarCanQuotedPriceToPriceTransport(1);
                }

                // 货源备注拼接固定文案，但编辑发布(viewSource=0)时不拼接，只设计车货APP。 @since 6600
                if (clientSign != null && (Constant.ClientSignEnum.isCar(clientSign) || Constant.ClientSignEnum.isGoods(clientSign))
                        && (clientVersion != null && Integer.parseInt(clientVersion) >= 6600)
                        && !Objects.equals(viewSource, 0)) {
                    detailBean.setRemark((detailBean.getRemark() == null ? "" : detailBean.getRemark()) + "（订金请优先通过平台支付）");
                }

                //专车货源增加返回签约合作商是否是平台
                detailBean.setSpecialCarCooperativeIsNormal(0);
                if (transportMainForId.getCargoOwnerId() != null && transportMainForId.getCargoOwnerId() == 1) {
                    detailBean.setSpecialCarCooperativeIsNormal(1);
                }

                //返回是否同步至YMM字段
                TytTransportSyncYmm tytTransportSyncYmm = tytTransportSyncYmmMapper.selectTytTransportSyncYmmBySrcId(detailBean.getSrcMsgId());
                if (tytTransportSyncYmm != null) {
                    detailBean.setSyncYmm(true);
                }

                Integer count = specialCarDispatchDetailMapper.selectCountByGoodsId(detailBean.getSrcMsgId());
                if (count != null && count > 0) {
                    detailBean.setSpecialCarDispatchCount(count);
                }

            }

            backResponse(request, response, ReturnCodeConstant.OK, "成功", result, 0);
            // 记录日志
            if (detailBean != null) {
                if (userId != null && user_id.longValue() != userId.longValue()) {
                    String today = TimeUtil.formatDate(new Date());
                    String ctime = TimeUtil.formatDate(detailBean.getPublishTime());
                    if (ctime.equals(today)) {
                        viewSource = Objects.isNull(viewSource) ? 0 : viewSource;
                        specialMark = Objects.isNull(specialMark) ? 0 : specialMark;
                        int sortTypeInt = StringUtils.isBlank(sortType) ? 0 : Integer.parseInt(sortType);
                        sortIndex = Objects.isNull(sortIndex) ? 0 : sortIndex;
                        transportLogService.detailsLog(userId, detailBean.getId(), clientVersion, clientSign, detailBean.getStatus(), viewSource, sortTypeInt, sortIndex, specialMark, startProvinc, startCity, startArea, destProvinc, destCity, destArea, detailBean.getBenefitLabelCode(), sharerUserId);
                        logger.info("查看详情记录日志userId【{}】,tsId:【{}】,goodsId:【{}】,labelCode:【{}】,detailType:【{}】(1找货列表/收藏列表的详情 2我的货源列表的详情 3已接单列表的详情)", userId, detailBean.getId(), goodsId, detailBean.getBenefitLabelCode(), detailType);
                        // 记录tyt库内应用日志，不作为统计日志使用
                        transportViewLogService.addLog(userId, detailBean.getId(), clientVersion, clientSign);
                        logger.info("记录应用内查看详情记录日志userId【{}】,tsId:【{}】,goodsId:【{}", userId, detailBean.getId(), goodsId);
                    }
                }
            }

            long t3 = System.currentTimeMillis();
            logger.info("APP信息费查看详情后记录日志时间【{}ms】总耗时【{}ms】", t3 - t2, t3 - t1);
            return;
        } catch (Exception e) {
            logger.error("APP信息费查看详情异常：", e);
            backResponse(request, response, ReturnCodeConstant.ERROR, "失败", null, 0);
            return;
        }
    }

    private String getInvoiceSubjectTag(TytTransportEnterpriseLog transportEnterpriseLog) {
        try {
            Response<InternalWebResult<ThirdDominantInfoVo>> thirdDominantInfoResp = apiUserInvoiceClient.getDominantInfoById(transportEnterpriseLog.getInvoiceSubjectId()).execute();
            ThirdDominantInfoVo thirdDominantInfoVo = InternalClientUtil.getDataDetail(thirdDominantInfoResp);
            if (Objects.nonNull(thirdDominantInfoVo)) {
                return thirdDominantInfoVo.getDominantShortName();
            }
        } catch (IOException e) {
            logger.error("getInvoiceSubjectTag error, {}", transportEnterpriseLog, e);
        }
        return null;
    }

    /**
     * 判断是否可以跳过权益校验
     *
     * @param goodsId
     * @param userId
     * @return
     */
    private Integer checkSkipPermission(Long goodsId, Long userId) {
        return specialCarDispatchDetailMapper.selectCountByUserAndGoodsId(goodsId, userId);
    }

    private void encyptNickname(SingleGoodsDetailBean detailBean, String clientVersion) {
        String restriceLoginFailMinVersion = configService
                .getStringValue(Constant.RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY, "5301");
        int encyptPhoneMinVersionInt = Integer.valueOf(restriceLoginFailMinVersion);
        int clientVersionInt = Integer.valueOf(clientVersion).intValue();
        // 是否需要对电话加密 1 需要 2 不需要
        int isNeedEncypt = configService.getIntValue(Constant.IS_NEED_ENCYPT_KEY, 1);
        detailBean.setIsNeedDecrypt(isNeedEncypt);
        logger.info("isNeedEncypt == 1 && (clientVersionInt > restriceLoginFailMinVersionInt): "
                + (isNeedEncypt == 1 && (clientVersionInt > encyptPhoneMinVersionInt)));
        if (isNeedEncypt == 1 && (clientVersionInt > encyptPhoneMinVersionInt)) {
            if (StringUtils.isNotBlank(detailBean.getNickName())) {
                String nickName = StringUtil.hidePhoneInStr(detailBean.getNickName());
                detailBean.setNickName(XXTea.Encrypt(nickName, AppConfig.getProperty("tyt.xxtea.key")));
                detailBean.setNickNameByAes(AESUtil.enCode(nickName, AppConfig.getProperty("tyt.aes.key")));
            } else {
                detailBean.setIsNeedDecrypt(2);
            }
        }
    }

    private void recordScanTimes(Long userId, String srcMsgId) {
        String today = TimeUtil.formatDate_(new Date());
        String goodScanUserIdsKey = RECOMMEND_USER_SCAN_KEY + srcMsgId + "_" + today;
        String goodScanTimesKeys = RECOMMEND_USER_SCAN_TIMES + srcMsgId + "_" + today;
        // 获取当前都有哪些用户当天在精准推荐模块拨打了该货源电话
        String userIds = cacheService.getString(goodScanUserIdsKey);
        logger.info("query good scan userIds by key: " + goodScanUserIdsKey + ", result is: " + userIds);
        /*
         * 如果是获取结果为空，说明当天还没有人打过电话则记录该用户的拨打信息以及货源被拨打次数信息
         */
        String scanTimes = "";
        String scanUserIds = "";
        boolean isNeedChange = false;
        if (StringUtils.isEmpty(userIds)) {
            isNeedChange = true;
            // 设置货源浏览次数
            scanTimes = "1";
            // 设置浏览货源的用户信息
            scanUserIds = "#" + userId + "#";
        } else {
            /*
             * 只有在该用户没有拨打过电话才处理
             */
            if (userIds.indexOf("#" + userId + "#") == -1) {
                isNeedChange = true;
                // 获取货源浏览次数
                String goodCallTimes = cacheService.getString(goodScanTimesKeys);
                logger.info("query good scan times by key: " + goodScanTimesKeys + ", result is: " + goodCallTimes);
                // 设置次数
                scanTimes = String.valueOf(Integer.valueOf(goodCallTimes) + 1);
                // 设置浏览货源的用户信息
                scanUserIds = userIds + userId + "#";
            }
        }
        if (isNeedChange) {
            cacheService.setString(goodScanTimesKeys, scanTimes, ONE_DAY);
            cacheService.setString(goodScanUserIdsKey, scanUserIds, ONE_DAY);
        }
    }

    @RequestMapping(value = "saveDirect")
    @ResponseBody
    public ResultMsgBean saveDirect(SaveDirectReq saveDirectReq) {
        Integer oldControllerAdapterOnOff = configService.getIntValue("plat_publish_controller_adapter", 0);
        if (oldControllerAdapterOnOff == 1) {
            return bsPublishTransportService.saveDirectAdapter(saveDirectReq);
        } else {
            //好货抢单锁定判断
            if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(saveDirectReq.getGoodsId())) {
                return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
            }
            Long goodsId = saveDirectReq.getGoodsId();
            Long userId = saveDirectReq.getUserId();
            String clientVersion = saveDirectReq.getClientVersion();
            Integer clientSign = Integer.parseInt(saveDirectReq.getClientSign());

            ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
            try {
                if (userId == null || userId.longValue() <= 0) {
                    resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    resultMsgBean.setMsg("userId参数错误");
                    return resultMsgBean;
                }
                if (clientSign == null || clientSign.intValue() <= 0 || clientSign.intValue() > 100) {
                    resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    resultMsgBean.setMsg("clientSign参数错误");
                    return resultMsgBean;
                }
                if (clientVersion == null || clientVersion.trim().length() <= 0) {
                    resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    resultMsgBean.setMsg("clientVersion参数错误");
                    return resultMsgBean;
                }
                // 参数验证
                if (goodsId == null || goodsId.longValue() <= 0) {
                    resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    resultMsgBean.setMsg("goodsId参数错误");
                    return resultMsgBean;
                }

                TransportMain byId = transportMainService.getById(goodsId);
                TytTransportMainExtend mainExtend = tytTransportMainExtendMapper.getBySrcMsgId(byId.getSrcMsgId());
                if (clientSign == Constant.ClientSignEnum.PC.code && mainExtend != null){
                    if (null != byId.getPublishGoodsType() && mainExtend.getClientFusion() != null && mainExtend.getClientFusion() == 1
                            && (byId.getPublishGoodsType() == PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode()
                            || PublishGoodsTypeEnum.isExcellentGoods(byId.getPublishGoodsType()))){
                        resultMsgBean.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
                        resultMsgBean.setMsg("该货源享平台权益，请至APP编辑发布");
                        return resultMsgBean;
                    }
                }


                String timeErrorMsg = "装卸货时间已过期";
                if (byId.getLoadingTime() != null && new Date().compareTo(byId.getLoadingTime()) > 0) {
                    resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    resultMsgBean.setMsg(timeErrorMsg);
                    return resultMsgBean;
                }
                if (byId.getUnloadTime() != null && new Date().compareTo(byId.getUnloadTime()) > 0) {
                    resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                    resultMsgBean.setMsg(timeErrorMsg);
                    return resultMsgBean;
                }
                //增加发货限制验证
                ResultMsgBean msgBean = bsPublishTransportService.validationLimitPublish(userId);
                if (ReturnCodeConstant.OK != msgBean.getCode()) {
                    //TODO 6340版本 兼容 低版本PC提示 强升后可删除
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(saveDirectReq.getClientSign()) && saveDirectReq.getClientSign().equals("1") && org.apache.commons.lang3.StringUtils.isNotEmpty(saveDirectReq.getClientVersion()) && Integer.parseInt(saveDirectReq.getClientVersion()) < 566340) {
                        msgBean.setCode(2023);
                        msgBean.setMsg(msgBean.getNoticeData().getMasterContent());
                    }
                    return msgBean;
                }
                // PC客户端发货处理为普货
                if (Objects.nonNull(saveDirectReq.getClientSign()) && "1".equals(saveDirectReq.getClientSign())) {
                    // PC低于6000版本不允许发货
                    if (org.springframework.util.StringUtils.isEmpty(clientVersion) || Integer.parseInt(clientVersion) < 586000) {
                        return ResultMsgBean.failResponse(1001, "服务升级，当前版本已不可用，请到官网下载最新版本");
                    }
                }

                List<TytExcellentGoodsCardUserDetail> canUseCarList = null;
                Long excellCardId = null;

                // 校验优车黑名单
                if(Objects.equals(byId.getExcellentGoods(),ExcellentGoodsEnums.EXCELLENT.getCode())
                        || Objects.equals(byId.getExcellentGoodsTwo(), ExcellentGoodsTwoEnum.YES.getCode())){
                    DepositBlockCheckDTO depositBlockCheckDTO = depositService.checkDepositBlock(userId, true);
                    if (depositBlockCheckDTO.isBlock()) {
                        throw TytException.createException(new ResponseCode(ReturnCodeConstant.SUPERIOR_CAR_SIGN_BLACK_ERROR, "您当前账号因违反平台规则无法发优车货源，如有疑问请联系客服"));
                    }
                }


                //电议无价
                int youchePublishType = GoodCarPriceTransportPublishTypeEnum.noPrice.getCode();
                if (byId.getPublishType() != null && byId.getPublishType() == 2) {
                    //一口价
                    youchePublishType = GoodCarPriceTransportPublishTypeEnum.fixed.getCode();
                } else if (StringUtils.isNotBlank(byId.getPrice()) && new BigDecimal(byId.getPrice()).compareTo(new BigDecimal(0)) > 0) {
                    //电议有价
                    youchePublishType = GoodCarPriceTransportPublishTypeEnum.havePrice.getCode();
                }

                TytDepositAuthorization tytDepositAuthorization = depositService.selectDepositAuthByUserId(byId.getUserId());

                Transport originalTransport = transportBusiness.getByGoodsId(goodsId);
                TransportLabelJson oldLabelJson = transportBusiness.getTransportLabelJson(originalTransport.getLabelJson());
                if (oldLabelJson == null || oldLabelJson.getGoodCarPriceTransport() == null || oldLabelJson.getGoodCarPriceTransport() != 1) {
                    //非优车定价货源才校验优车以及优车发货卡权限
                    // 如果有优车发货卡，不校验优车限制
                    if(byId.getExcellentCardId() == null){
                        // 如果是优车货源，需要进行优车相关的校验
                        boolean b = bsPublishTransportService.checkExcellentGoods(byId.getExcellentGoods(), byId.getUserId(), youchePublishType);
                        if (!b) {
                            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "无法发布优车货源，请发布普通货源");
                        }
                        // 判断用户所属组和当前发的货源所在组是否一致
                        if (byId.getExcellentGoods() != null && byId.getExcellentGoods() == 1) {
                            TytDepositAuthorization depositAuthorization = depositService.selectDepositAuthByUserId(userId);
                            if (depositAuthorization == null || depositAuthorization.getUserGroup() == null) {
                                return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "您的发货权限存在变化，可编辑后再发布~");
                            }
                            if (depositAuthorization.getUserGroup() != 0 && !depositAuthorization.getUserGroup().equals(byId.getPublishType())) {
                                return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "您的发货权限存在变化，可编辑后再发布~");
                            }

                        }
                    }else {
                        if(tytDepositAuthorization == null || !Objects.equals(tytDepositAuthorization.getAuthStatus(), AuthStatusEnum.NO_AUTH.getStatus())){
                            canUseCarList = excellentGoodsService.getAllCanUseCarListByUserId(byId.getUserId());
                            if(CollUtil.isEmpty(canUseCarList)){
                                msgBean = ResultMsgBean.failResponse(ResultMsgBean.ERROR, "当前无可用优车发货卡，无法发布优车货源");
                                return msgBean;
                            }
                            excellCardId = canUseCarList.get(0).getId();
                        }
                    }
                }

                //指派了车方的开票货源不允许直接发布
                if(byId.getInvoiceTransport() != null && byId.getInvoiceTransport() == 1){
                    TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(byId.getSrcMsgId());
                    if (transportEnterpriseLog != null && StringUtils.isNotBlank(transportEnterpriseLog.getAssignCarTel())) {
                        msgBean = ResultMsgBean.failResponse(ResultMsgBean.ERROR, "该货源为指派货源，请使用编辑再发布");
                        return msgBean;
                    }
                }

                // 拼车货源直接发布，需要判断是否满足拼车条件
                ResultMsgBean carpoolCheckResult = getCarpoolCheckResult(goodsId, byId);
                if (Objects.nonNull(carpoolCheckResult)) {
                    return carpoolCheckResult;
                }

//			// 是否可以正常发货验证 已放到下面service层判断
//			ResultMsgBean checkMsgBean = userService.checkUserPhotoVerifyFlag(userId,clientSign.toString());
//			if (checkMsgBean.getCode() != 200) {
//				return checkMsgBean;
//			}

                if (!transportBusiness.lockTransportOpt(goodsId)) {
                    return transportBusiness.returnLockStatus();
                }

                saveDirectReq.setAutomaticGoodCarPriceTransportType(2);


                if (originalTransport.getInvoiceTransport() != null && originalTransport.getInvoiceTransport() == 1) {
                    TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(originalTransport.getSrcMsgId());
                    if (transportEnterpriseLog != null && Objects.equals(transportEnterpriseLog.getPaymentsType(), YesOrNoEnum.Y.getCode())) {
                        resultMsgBean = bsPublishTransportService.checkSegmentedPayments(transportEnterpriseLog.getPrepaidPrice(), transportEnterpriseLog.getCollectedPrice(),
                                transportEnterpriseLog.getReceiptPrice(), originalTransport.getPrice(), userId, true);
                        if (resultMsgBean.getCode() != 200) {
                            return resultMsgBean;
                        }
                    }
                }
                saveDirectReq.setDirectOptEnum(SaveDirectOptEnum.direct);
                resultMsgBean = transportBusiness.saveGoodsDirectV5930(saveDirectReq, false, true, true,excellCardId);

                // 如果发布成功，优车货源添加发布时间，供货源下放使用,并扣减使用次数
                if (ResultMsgBean.OK == resultMsgBean.getCode() && byId.getExcellentGoods() != null && byId.getExcellentGoods() == 1) {

                    if (byId.getExcellentCardId() == null) {
                        cacheService.setObject(EXCELLENT_GOODS_PUBLISH_TIME + byId.getSrcMsgId(), System.currentTimeMillis(), 24 * 60 * 60);
                        depositService.deductionCount(userId, youchePublishType);
                    } else if (excellCardId != null && (tytDepositAuthorization == null || !Objects.equals(tytDepositAuthorization.getAuthStatus(), AuthStatusEnum.NO_AUTH.getStatus()))) {
                        excellentGoodsService.updateType(excellCardId, ExcellentCardTypeEnum.used);
                    }
                }

                if (ResultMsgBean.OK == resultMsgBean.getCode()) {
                    Map<String, Object> dataMap = (Map<String, Object>) resultMsgBean.getData();
                    Long srcMsgId = Long.valueOf(dataMap.get("srcMsgId").toString());
                    //开票货源记录发布时货主的企业信息
                    if (byId.getInvoiceTransport() != null && byId.getInvoiceTransport() == 1) {
                        //直接发布用旧货源ID查询旧货源的开票主体ID和服务商code，然后用新生成的货源ID保存
                        TytTransportEnterpriseLog transportEnterpriseLog = tytInvoiceEnterpriseMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(byId.getSrcMsgId());
                        invoiceTransportService.saveInvoiceTransportEnterpriseData(byId.getUserId(), srcMsgId, transportEnterpriseLog.getInvoiceSubjectId()
                                , transportEnterpriseLog.getServiceProviderCode(), transportEnterpriseLog.getAssignCarTel(), transportEnterpriseLog.getEnterpriseTaxRate()
                                , transportEnterpriseLog.getConsigneeName(), transportEnterpriseLog.getConsigneeTel(), transportEnterpriseLog.getConsigneeEnterpriseName(),
                                transportEnterpriseLog.getPaymentsType(),transportEnterpriseLog.getPrepaidPrice(),transportEnterpriseLog.getCollectedPrice(),transportEnterpriseLog.getReceiptPrice());
                    }
                    // 记录发布日志
                    transportPublishLogService.recordPublishLog(userId, srcMsgId, PubTypeEnum.DIRECT_PUBLISH, null);

                    //记录价格变动
                    transportPublishLogService.changePriceLog(srcMsgId, byId.getPrice(), byId.getPublishType(), 2);

                    // 是否发放曝光卡
                    transportBusiness.checkAndSaveExposureCardGiveaway(srcMsgId);
                }

                return resultMsgBean;
            } catch (Exception e) {
                PlatCommonUtil.printErrorInfo("saveGoodsDirectV5930_error : ", e);
                return ResultMsgBean.failResponse(e);
            }
        }

    }

    /**
     * 拼车货源校验是否满足拼车条件
     *
     * @param goodsId
     * @param byId
     * @return
     * @throws IOException
     */
    private ResultMsgBean getCarpoolCheckResult(Long goodsId, TransportMain byId) throws IOException {
        ResultMsgBean msgBean;
        TytTransportMainExtend mainExtend = transportMainService.getMainExtend(goodsId);
        if (Objects.nonNull(mainExtend) && Objects.equals(UseCarTypeEnum.PART.getCode(), mainExtend.getUseCarType())) {
            CarpoolMatchDTO matchDTO = new CarpoolMatchDTO();
            matchDTO.setStartCity(byId.getStartCity());
            matchDTO.setDestCity(byId.getDestCity());
            matchDTO.setDistance(StringUtils.isNotBlank(byId.getDistance()) ? new BigDecimal(byId.getDistance()) : null);
            matchDTO.setWeight(StringUtils.isNotBlank(byId.getWeight()) ? new BigDecimal(byId.getWeight()) : null);
            matchDTO.setUserId(byId.getUserId());
            matchDTO.setInvoiceTransport(byId.getInvoiceTransport());
            Response<Boolean> response = transportTecserviceFeeClient.checkMatchCarpool(matchDTO).execute();
            if (response.isSuccessful()) {
                Boolean match = response.body();
                if (Objects.nonNull(match) && !match) {
                    msgBean = ResultMsgBean.failResponse(ResultMsgBean.ERROR, "该货源为拼车货源，暂不支持直接发布，请编辑再发布");
                    return msgBean;
                }
            }
        }
        return null;
    }

    /**
     * 货源自动重发接口，仅内部使用
     */
    @PostMapping(value = "/saveDirectForAutoResend.action")
    @ResponseBody
    public ResultMsgBean saveDirectForAutoResend(SaveDirectReq saveDirectReq) {

        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");;
        Long excellCardId = null;
        try {
            TransportMain byId = transportMainService.getById(saveDirectReq.getGoodsId());

            // 如果有优车发货卡，补发一张
            if (byId.getExcellentCardId() != null) {
                Long excellentGoodsCarId = excellentGoodsService.saveExcellentGoodsPriceCard(saveDirectReq.getUserId(), true);
                if (excellentGoodsCarId != null && excellentGoodsCarId != 0) {
                    excellCardId = excellentGoodsCarId;
                } else {
                    resultMsgBean = ResultMsgBean.failResponse(ResultMsgBean.ERROR, "当前无可用优车发货卡，无法发布优车货源");
                    return resultMsgBean;
                }
            }
            saveDirectReq.setAutomaticGoodCarPriceTransportType(2);
            saveDirectReq.setDirectOptEnum(SaveDirectOptEnum.AUTO_RESEND);
            resultMsgBean = transportBusiness.saveGoodsDirectV5930(saveDirectReq, false, true, true, excellCardId);

            if (excellCardId != null && ResultMsgBean.OK == resultMsgBean.getCode()) {
                excellentGoodsService.updateType(excellCardId, ExcellentCardTypeEnum.used);
            }

            return resultMsgBean;
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("saveGoodsDirectV5930_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 发货检测接口
     *
     * @param userId
     * @param clientVersion
     * @return
     */
    @RequestMapping(value = "saveGoodsCheck")
    @ResponseBody
    public ResultMsgBean saveGoodsCheck(Long userId, String clientVersion, Integer clientSign) {
        logger.info("》》》》》plat controller saveGoodsCheck:{}【开始】", userId);
        try {
            return userService.saveGoodsCheck(userId, clientVersion, clientSign);
        } catch (Exception e) {
            logger.error("》》》》》plat controller saveGoodsCheck:{}$$$$$$$$$$【error/exception:{}】", userId, e.toString());
            e.printStackTrace();
            return new ResultMsgBean(ResultMsgBean.ERROR, "请稍后再试");
        } finally {
            logger.info("》》》》》plat controller saveGoodsCheck:{}【结束】", userId);
        }

    }

    /**
     * PC端我的货源详情接口
     *
     * @param userId
     * @param goodsId       货物ID
     * @param detailType
     * @param clientVersion
     * @param clientSign
     * @param request
     * @param response
     */
    @RequestMapping(value = "getSingleDetail.action")
    public void getSingleDetailForPc(Long userId, Long goodsId, Integer detailType, String clientVersion, Integer clientSign, HttpServletRequest request, HttpServletResponse response) {
        String isEncypt = "pc-web";
        // 访问来源 10-PC
        int viewSource = 10;
        this.getSingleDetail(userId, goodsId, detailType, clientVersion, clientSign, isEncypt, viewSource,null,null,null, null,null,null,null,null,null, request, response, null);
    }

    @RequestMapping(value = "saveDirect.action")
    @ResponseBody
    public ResultMsgBean saveDirectAction(SaveDirectReq saveDirectReq) {
        return this.saveDirect(saveDirectReq);
    }

    @RequestMapping(value = "saveGoodsStatus.action")
    @ResponseBody
    public ResultMsgBean saveBtnStatusAction(Long userId, Long goodsId, Integer operateType) {
        return this.saveBtnStatus(userId, goodsId, operateType);
    }

    /**
     * 转电议或转一口价
     *
     * @param parameter   基础参数
     * @param tsId        tyt_transport_main id
     * @param publishType 货源类型（电议1，一口价2）
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "updatePublishType")
    @ResponseBody
    public ResultMsgBean updatePublishType(BaseParameter parameter, Long tsId, Integer publishType, Integer isBackendTransport, String price) throws Exception {
        if (tsId == null || tsId == 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "tsId参数应大于0");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(parameter.getClientVersion())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "clientVersion不应该为空");
        }

        if (!StringUtil.isNumeric(parameter.getClientSign())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "clientSign参数应数字");
        }
        if (Objects.isNull(publishType)) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "货源类型不能为空");
        }
        if (Objects.isNull(parameter.getUserId())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "userId不能为空");
        }

        if (!transportBusiness.lockTransportOpt(tsId)) {
            return transportBusiness.returnLockStatus();
        }

        try {
            ResultMsgBean resultMsgBean = bsPublishTransportService.updatePublishType(parameter.getUserId(), tsId, publishType,
                    parameter.getClientVersion(), parameter.getClientSign(), isBackendTransport, price);

            return resultMsgBean;
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("updatePublishType_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    @RequestMapping(value = "/addMoney1")
    @ResponseBody
    public ResultMsgBean addMoney1(BaseParameter parameter, Long tsId, String price, Integer isBackendTransport) throws Exception {
        return bsPublishTransportService.freightAddMoneyNum1(parameter.getUserId(), tsId, price);
    }

    /**
     * 运费加价
     *
     * @param parameter
     * @param tsId
     * @param requestSource 1-撤销弹窗
     * @return
     */
    @RequestMapping(value = "/addMoney")
    @ResponseBody
    public ResultMsgBean addMoney(BaseParameter parameter, Long tsId, String price, Integer isBackendTransport,
                                  Integer forceUp, Integer requestSource) throws Exception {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(tsId)) {
            return ResultMsgBean.failResponse(8899010, "已有多个司机抢单，正在匹配最优司机，请耐心等待");
        }
        if (tsId == null || tsId == 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "tsId参数应大于0");
        }
        if (StringUtils.isEmpty(price)) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "价格不能为空");
        }
        if (Objects.isNull(parameter.getUserId())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "用户id不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(parameter.getClientVersion())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "clientVersion不应该为空");
        }

        if (!StringUtil.isNumeric(parameter.getClientSign())) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "clientSign参数应数字");
        }

        if (!transportBusiness.lockTransportOpt(tsId)) {
            return transportBusiness.returnLockStatus();
        }

        try {
            Integer oldControllerAdapterOnOff = configService.getIntValue("plat_publish_controller_adapter", 0);
            if (oldControllerAdapterOnOff == 1) {
                return bsPublishTransportService.freightAddMoneyNumAdapter(parameter.getUserId(), tsId, price,
                        parameter.getClientVersion(), parameter.getClientSign(), isBackendTransport, forceUp, requestSource, 1);
            } else {
                //加价逻辑
                ResultMsgBean resultMsgBean = bsPublishTransportService.freightAddMoneyNum(parameter.getUserId(), tsId, price,
                        parameter.getClientVersion(), parameter.getClientSign(), isBackendTransport, forceUp, requestSource, 1);
                return resultMsgBean;
            }
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("freightAddMoneyNum_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 修改技术服务费
     *
     * @param tsId          货源Id
     * @param tecServiceFee 技术服务费
     * @return ResultMsgBean
     */
    @RequestMapping(value = "/changeTecServiceFee")
    @ResponseBody
    public ResultMsgBean changeTecServiceFee(Long tsId, BigDecimal tecServiceFee) {
        if (tsId == null || tsId == 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "tsId参数应大于0");
        }
        if (tecServiceFee == null || tecServiceFee.compareTo(TEC_SERVICE_FEE_MIN) < 0 || tecServiceFee.compareTo(TEC_SERVICE_FEE_MAX) > 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "技术服务费取值范围有误,请确认后重试!");
        }
        try {
            //修改技术服务费逻辑
            ResultMsgBean resultMsgBean = bsPublishTransportService.changeTecServiceFee(tsId, tecServiceFee);
            return resultMsgBean;
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("changeTecServiceFee error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 校验运费是否可以加价
     *
     * @param srcMsgId
     * @return
     */
    @RequestMapping(value = "/checkAddMoney")
    @ResponseBody
    public ResultMsgBean checkAddMoney(Long srcMsgId) {
        if (srcMsgId == null || srcMsgId == 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "货源id不能为空");
        }
        String addPriceTimeKey = RedisKeyConstant.getAddPriceTimeInterval(srcMsgId);
        String addPriceValue = RedisUtil.get(addPriceTimeKey);
        if (StringUtils.isNotBlank(addPriceValue)) {
            return new ResultMsgBean(ReturnCodeConstant.NO_ADD_MONEY, "10分钟内已加价，请稍后再试。");
        }
        return ResultMsgBean.successResponse();
    }


    /**
     * 车辆信息新增(信息费详情)
     *
     * @param carSaveBean
     * @param orderId
     * @param type        1 车方信息费，2 货方信息费
     * @return
     */
    @ResponseBody
    @RequestMapping("/supplementCarInfoForInfo")
    public ResultMsgBean supplementCarInfo(CarSaveBean carSaveBean, Long orderId, Integer type) {
        return bsPublishTransportService.supplementCarInfo(carSaveBean, orderId, type);
    }

    @ResponseBody
    @RequestMapping("/supplementCarInfoForTransport")
    public ResultMsgBean supplementCarInfoForTransport(CarSaveBean carSaveBean, Long tsId) {
        return bsPublishTransportService.supplementCarInfoForTransport(carSaveBean, tsId);
    }

    /**
     * 车辆信息新增(信息费详情)manage服务
     *
     * @param carSaveBean
     * @param orderId
     * @param type        1 车方信息费，2 货方信息费
     * @return
     */
    @ResponseBody
    @RequestMapping("/supplementCarInfoForInfoForManage")
    public ResultMsgBean supplementCarInfoForInfoForManage(CarSaveBean carSaveBean, Long orderId, Integer type) {
        return bsPublishTransportService.supplementCarInfoForManage(carSaveBean, orderId, type);
    }


    /**
     * 货源删除 【已撤销、已过期、已成交】
     *
     * @param goodsId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/delMyGoods")
    public ResultMsgBean delMyGoods(Long goodsId, Long userId) throws Exception {
        if (Objects.isNull(goodsId)) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "goodsId不能为空");
        }
        TransportMain transportMainForId = transportMainService.getTransportMainForId(goodsId);
        if (Objects.isNull(transportMainForId)) {
            return new ResultMsgBean(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "货源不存在");
        }
        String ctimeStr = TimeUtil.formatDate(transportMainForId.getCtime());
        Date ctime = TimeUtil.parseString(ctimeStr);
        String nowStr = TimeUtil.formatDate(new Date());
        Date nowTime = TimeUtil.parseString(nowStr);
        if (transportMainForId.getStatus() == 1 && transportMainForId.getSource() == 0 && transportMainForId.getDisplayType().equals("1") && ctime.getTime() >= nowTime.getTime()) {
            return new ResultMsgBean(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "发布中的不允许删除");
        }
        logger.info("删除货源,userId={},goodsId={}", userId, goodsId);
        transportMainService.delMyGoods(goodsId);
        return ResultMsgBean.successResponse();
    }

    /**
     * 信用曝光按钮点击修改状态
     *
     * @param srcMsgId tyt_transport_main src_msg_id
     * @return
     * @throws Exception
     */
    @PostMapping(value = "updateCreditRetop")
    @ResponseBody
    public ResultMsgBean updateCreditRetop(Long srcMsgId) throws Exception {
        if (srcMsgId == null || srcMsgId < 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "入参错误");
        }
        boolean result = transportMainService.updateCreditRetop(srcMsgId);
        return result ? ResultMsgBean.successResponse() : ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_ERROR_CODE, "信用已曝光");
    }

    @GetMapping(value = "getInvoiceRateByUserId.action")
    @ResponseBody
    public ResultMsgBean getInvoiceRateByUserId(@RequestParam("userId") Long userId, @RequestParam("invoiceSubjectId") Long invoiceSubjectId) {
        return ResultMsgBean.successResponse(apiInvoiceEnterpriseService.invoiceEnterpriseGetTaxRate(userId, invoiceSubjectId));
    }

    /**
     * 修改货源的长宽高等信息
     */
    @PostMapping(value = "/updateGoodsInfo")
    @ResponseBody
    public ResultMsgBean updateGoodsInfo(TransportUpdateDataReq updateDataReq) {
        if (updateDataReq.getTsId() == null || updateDataReq.getTsId() <= 0) {
            return new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "tsId参数应大于0");
        }
        try {
            Integer oldControllerAdapterOnOff = configService.getIntValue("plat_publish_controller_adapter", 0);
            if (oldControllerAdapterOnOff == 1) {
                return bsPublishTransportService.updateGoodsInfoAdapter(updateDataReq);
            } else {
                return bsPublishTransportService.updateGoodsInfo(updateDataReq);
            }
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("updateGoodsInfo_error : ", e);
            return ResultMsgBean.failResponse(e);
        }
    }

    @PostMapping(value = {"/adapterTest", "/adapterTest.action"})
    @ResponseBody
    public ResultMsgBean adapterTest() {
        return bsPublishTransportService.adapterTest();
    }

    /**
     * 查询曝光动态文案接口
     */
    @GetMapping(value = "/getGoodsDynamic")
    @ResponseBody
    public ResultMsgBean getGoodsDynamic(BaseParameter baseParameter, Long srcMsgId) {
        try {
            return ResultMsgBean.successResponse(goodsRefreshManualService.getGoodsDynamic(baseParameter.getUserId(), baseParameter.getClientSign(), srcMsgId));
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("updateGoodsInfo_error : ", e);
            return ResultMsgBean.successResponse();
        }
    }

    @PostMapping(value = {"/makePriceByGoodCarPriceTransportCarryPrice", "/makePriceByGoodCarPriceTransportCarryPrice.action"})
    @ResponseBody
    public ResultMsgBean makePriceByGoodCarPriceTransportCarryPrice(@RequestBody Transport transport) {
        try {
            if (transport != null) {
                return ResultMsgBean.successResponse(bsPublishTransportService.makePriceByGoodCarPriceTransportCarryPrice(transport));
            }
            return ResultMsgBean.successResponse(null);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("makePriceByGoodCarPriceTransportCarryPrice_error : ", e);
            return ResultMsgBean.successResponse();
        }
    }

}
