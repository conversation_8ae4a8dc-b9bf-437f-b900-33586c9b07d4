package com.tyt.infofee.enums;

/**
 * @description 运满满运单状态
 * <AUTHOR>
 * @date 2023/8/21 10:57
 * @version 1.0
 */
public enum YMMOrderStatusEnum {

    TIMEOUT_CANCEL(-2, "超时取消"),
    FAILURE(-1, "交易失败"),
    CANCELED(0, "已取消"),
    INIT(1, "初始"),
    DEALING(5, "待成交"),
    CLINCH_DEAL(10, "已成交"),
    WAIT_CHECK(15, "待确认"),
    WAIT_DRIVER_CHECK(17, "司机待确认接单"),
    TRANSPORTING(20, "运输中"),
    LOADED(25, "已装货"),
    ARRIVING(30, "货已到达"),
    RECEIPTED(60, "已收回单"),
    COMPLETE(90, "已完成");

    private Integer code;
    private String msg;

    YMMOrderStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

}
