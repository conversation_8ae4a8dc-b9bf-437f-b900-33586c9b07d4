package com.tyt.infofee.enums;

public enum ExPartyEnum{
    //异常上报方1车主上报，2货主上报
    车主上报(1, "ex_party_car","ex_complaint_reason_type_car"),
    货主上报(2, "ex_party_goods","ex_complaint_reason_type_goods");

    private int id;
    private String dicKey;
    private String newDicKey;

    ExPartyEnum(int id, String dicKey, String newDicKey) {
        this.id = id;
        this.dicKey = dicKey;
        this.newDicKey = newDicKey;
    }

    public static ExPartyEnum getExPartyEnumById(int id){
        for(ExPartyEnum partyEnum: values()){
            if(partyEnum.getId() == id){
                return partyEnum;
            }
        }
        return null;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDicKey() {
        return dicKey;
    }

    public void setDicKey(String dicKey) {
            this.dicKey = dicKey;
        }

    public String getNewDicKey() {
        return newDicKey;
    }

    public void setNewDicKey(String newDicKey) {
        this.newDicKey = newDicKey;
    }
}
