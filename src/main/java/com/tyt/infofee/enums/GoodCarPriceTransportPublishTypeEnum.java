package com.tyt.infofee.enums;

import lombok.Getter;

/**
 * 优车定价货源业务逻辑专用货源类型
 */
public enum GoodCarPriceTransportPublishTypeEnum {
    noPrice(1, "电议无价"),
    havePrice(2, "电议有价"),
    fixed(3, "一口价"),

    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    GoodCarPriceTransportPublishTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

}
