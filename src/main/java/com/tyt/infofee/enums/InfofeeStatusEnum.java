package com.tyt.infofee.enums;

public enum  InfofeeStatusEnum {
    //信息费状态：10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成

    待支付("10"),
    已支付("15"),
    已冻结("20"),
    拒绝退款("21"),
    异常上报("25"),
    退款中("30"),
    已退款("35"),
    已打款("40"),
    自动收款("45"),
    异常完成("50");

    private String id;

    InfofeeStatusEnum(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }
}
