package com.tyt.infofee.enums;

/**
 * @Describe 货源来源枚举类
 * <AUTHOR>
 * @Date 2023/8/15
 */
public enum SourceTypeEnum {

    普通货主(1),
    代调发货(2),
    个人货主(3),
    运满满货源(4),
    宏信货源(5);

    private Integer id;

    SourceTypeEnum(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    /**
     * 是否代调账户发货
     */
    public static boolean isDispatch(Integer code) {
        return 代调发货.id.equals(code) || 宏信货源.id.equals(code);
    }
}
