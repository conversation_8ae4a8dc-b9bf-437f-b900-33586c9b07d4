package com.tyt.infofee.enums;

public enum FlowTypeEnum {

    //流水类型，0用户转帐1：支付信息费  2：收到信息费 3退回信息费 4转出信息费6提现 7异常上报 8:支付手续费 9:转入会员费10:支付会员费
    用户转账(0),
    支付信息费(1),
    收到信息费(2),
    退回信息费(3),
    转出信息费(4),
    提现(6),
    异常上报(7),
    支付手续费(8),
    转入会员费(9),
    支付会员费(10),
    好友推荐奖励收益(11),
    活动返现(13),
    退回会员费(14),
    转出会员费(15),
    冻结信息费(100),
    平台入账账户流水(110),
    平台手续费流水(120),
    平台出账账户流水(130),
    平台余额账户流水(140),
    转入保证金(150),
    支付保证金(160),
    渠道结算流水(170),
    退回保证金(180),
    转出保证金(190);

    private int id;

    FlowTypeEnum(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}
