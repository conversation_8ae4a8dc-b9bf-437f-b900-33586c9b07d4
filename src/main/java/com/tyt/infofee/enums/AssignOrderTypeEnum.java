package com.tyt.infofee.enums;

import lombok.Getter;

/**
 * 货源类型
 */
public enum AssignOrderTypeEnum {
    //0:不播报,1:播报
    no_assign_order(0, "非开票0元指派单"),
   assign_order(1, "开票0元指派单"),

    ;

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private AssignOrderTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

}
