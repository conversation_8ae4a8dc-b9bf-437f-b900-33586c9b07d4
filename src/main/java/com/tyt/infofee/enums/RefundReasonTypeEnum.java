package com.tyt.infofee.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RefundReasonTypeEnum
 * @description 退订金原因枚举类
 * @date 2022-10-17 18:35
 */
public enum RefundReasonTypeEnum {

    司机到厂装货(5, "已经装货,退还订金"),
    货已送达(6, "货已送达,退还订金"),
    厂家不发货(3, "因厂家原因不发货,协商退款"),
    其他途径发货(1, "货已从其他途经发出,协商退款"),
    司机原因(2, "司机原因无法承运,协商退款"),
    天气原因(4, "因天气原因取消发货,协商退款"),
    已装货(8, "发货订金不退,与司机沟通后可退,已装货 需退还"),
    已送达(9, "发货订金不退,与司机沟通后可退,已送达 需退还"),
    其他(0, "其他");

    private Integer code;
    private String msg;

    private RefundReasonTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }


}
