package com.tyt.infofee.enums;

import lombok.Getter;
import org.elasticsearch.common.Strings;

/**
 * 满帮货源订金退款原因(根据退款原因判断是否要全额退还技术服务费)
 *
 * <AUTHOR>
 * @since 2024/6/25 14:27
 */
public enum MbOrderRefundReasonEnum {
    shipper_owner_refund_reason_1("货已从其他途径发出，协商退款",  true),//无
    shipper_owner_refund_reason_2("因厂家原因不发货，协商退款", true),//无
    shipper_owner_refund_reason_3("因天气原因取消发货，协商退款", true),//无
    shipper_owner_refund_reason_4("临时不需要用车了",  true),
    shipper_owner_refund_reason_5("从其他途径发出", true),
    shipper_owner_refund_reason_6("货物信息填写错误", true),//无
    shipper_owner_refund_reason_7("禁区限行（未和司机沟通）", true),
    shipper_owner_refund_reason_8("天气原因", true),
    shipper_owner_refund_reason_9("疫情原因无法发货", true),//无
    shipper_owner_refund_reason_10("其他原因", false),
    car_owner_refund_reason_1("司机原因无法承运，协商退款",  false),//无
    car_owner_refund_reason_2("司机迟到、爽约",  false),
    car_owner_refund_reason_3("司机车辆与要求不符", false),
    car_owner_refund_reason_4("要求加价或线下交易", false),//无
    car_owner_refund_reason_5("无任何理由不装货物", false),
    car_owner_refund_reason_6("禁区限行（已和司机沟通）",  false),
    car_owner_refund_reason_7("疫情原因无法承运",  false),//无
    car_owner_refund_reason_8("其他原因",  false),
    ;

    @Getter
    private String refundReason;

    @Getter
    private boolean refundFlag;

    MbOrderRefundReasonEnum(String refundReason, boolean refundFlag) {
        this.refundReason = refundReason;
        this.refundFlag = refundFlag;
    }


    public static MbOrderRefundReasonEnum getRefundReasonEnum(String refundReason) {
        if (Strings.isEmpty(refundReason)) {
            return null;
        }
        for (MbOrderRefundReasonEnum typeEnum : MbOrderRefundReasonEnum.values()) {
            if (typeEnum.getRefundReason().equals(refundReason)) {
                return typeEnum;
            }
        }
        return null;
    }
}
