package com.tyt.infofee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/04/03 10:32
 */
@Getter
@AllArgsConstructor
public enum PublishGoodsTypeEnum {

    //0-普通找车，10-用户出价，20-特惠优车，21-快速优车，22-极速优车，30-专车

    NORMAL_GOODS(0, "普通找车", 0),
    USER_PRICE_GOODS(10, "用户出价", 0),
    EXCELLENT_GOODS(20, "特惠优车", 1),
    QUICK_EXCELLENT_GOODS(21, "快速优车", 1),
    SUPER_QUICK_EXCELLENT_GOODS(22, "极速优车", 1),
    SPECIAL_GOODS(30, "专车", 2),
    ;

    private final int code;
    private final String name;
    private final int oldCode; // excellent_goods

    public static PublishGoodsTypeEnum getByCode(int code) {
        for (PublishGoodsTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    public static boolean isExcellentGoods(Integer code) {
        return Objects.equals(EXCELLENT_GOODS.getCode(), code) ||
                Objects.equals(QUICK_EXCELLENT_GOODS.getCode(), code) ||
                Objects.equals(SUPER_QUICK_EXCELLENT_GOODS.getCode(), code);
    }

}
