package com.tyt.promo.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.util.JSONPObject;
import com.tyt.marketingActivity.bean.ActivityEnum;
import com.tyt.marketingActivity.bean.SpecialMarketActivityCheckBean;
import com.tyt.marketingActivity.service.SpecialActivityPopupService;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.permission.bean.UserPermissionAndCouponBean;
import com.tyt.permission.bean.UserPermissionGetBean;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.coupon.GoodsUserCoupon;
import com.tyt.promo.constant.PromoConstant;
import com.tyt.promo.model.*;
import com.tyt.promo.service.ICouponService;
import com.tyt.promo.service.PromoGiftCouponService;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.service.common.exception.TytException;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytUserCallPhoneRecordService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/plat/promo")
@Slf4j
public class PromoController {

    @Autowired
    private ICouponService couponService;

    @Resource(name="promoGiftCouponService")
    private PromoGiftCouponService giftCouponService;
    @Resource(name = "specialActivityPopupService")
    private SpecialActivityPopupService specialActivityPopupService;
    @Resource(name="userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name = "tytUserCallPhoneRecordService")
    private TytUserCallPhoneRecordService tytUserCallPhoneRecordService;

    @Autowired
    private TytConfigService configService;

    @Autowired
    private UserService userService;

    @Autowired
    private AbtestService abtestService;

    private static final String VIP_MIN_AVG_PRICE = "vip_min_avg_price";

    /**
     * 根据用户查询活动优惠券
     * userId
     * status 0全部  1未使用 2已使用 3无效 4已过期
     * scopeType  1 购买会员 2 购买保险 3支付信息费
     */

    @RequestMapping(value = {"/queryCoupons","/queryCoupons.action"})
    @ResponseBody
    public Object queryCouponListByUserId(@RequestParam(required = true) Long userId,String clientSign,
                                                 @RequestParam(required = false) String callback,
                                                 @RequestParam(required = false,defaultValue = "0") int status,
                                                 @RequestParam(required = false,defaultValue = "0") int scopeType,
                                                 @RequestParam(required = false,defaultValue = "1") int pageNo,
                                                 @RequestParam(required = false,defaultValue = "100") int pageSize) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功！");
        try {
            List<UserCoupon> couponList = couponService.queryCouponListByUserId(userId,scopeType, status, pageNo, pageSize,clientSign);
            msgBean.setData(couponList);
            msgBean.setExtraData(new Date());
            if(StringUtils.isEmpty(callback)) {
                return msgBean;
            }
        }catch (Exception e){
            log.error("优惠券列表查询失败：",e);
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
        }
        return new JSONPObject(callback, msgBean);
    }


    /**
     * 根据用户查询用户新发券
     */
    @GetMapping("/queryNewCoupons/{userId}")
    public ResponseEntity<List<UserCoupon>> queryNewCouponsByUserId(@PathVariable String userId){
        List<UserCoupon> couponList = couponService.getUserNewCoupons(userId);
        return new ResponseEntity<>(couponList, HttpStatus.OK);

    }

    /**
     * 根据礼包id 获取礼包优惠券详情
     * @return
     */
    @RequestMapping(value = "/getGiftDetail")
    @ResponseBody
    public ResultMsgBean getGiftDetail(@RequestParam(value="giftId", required=true) Integer giftId,
                                       @RequestParam(value="noticeId", required=true) Integer noticeId,
                                       @RequestParam(value="userId", required=true) Long userId){
        ResultMsgBean result=new ResultMsgBean();
        try {
            Map<String ,Object> map= giftCouponService.getGiftDetailForGiftId(giftId,noticeId,userId);
            result.setData(map);
        }catch (Exception e){
            log.error("根据礼包id 获取礼包优惠券详情失败", e);
            result.setCode(ReturnCodeConstant.ERROR);
            result.setMsg("服务器错误");
        }
        return result;
    }

    /**
     * 发放50元的优惠券，分享注册给H5使用
     * @return
     */
    @RequestMapping(value = "/giveShareCoupon.action")
    @ResponseBody
    public ResultMsgBean giveShareCoupon(@RequestParam(value="shareUserId", required=true) Long shareUserId){
        ResultMsgBean result=new ResultMsgBean();
        try {
            couponService.giveShareCoupon(shareUserId, PromoConstant.ACTIVITE_SHARE_TYPE_50);
        }catch (Exception e){
            log.error("分享优惠券错误：", e);
            result.setCode(ReturnCodeConstant.ERROR);
            result.setMsg("服务器错误");
        }
        return result;
    }

    /**
     * 发放50元的优惠券，分享注册给H5使用
     * @return
     */
    @RequestMapping(value = "/checkShareCoupon.action")
    @ResponseBody
    public ResultMsgBean checkShareCoupon(@RequestParam(value="userId", required=true) Long userId){
        ResultMsgBean result=new ResultMsgBean();
        try {
            boolean b = couponService.isExistCouponByShare50(userId);
            if(!b) {
                result.setCode(ReturnCodeConstant.ERROR);
                result.setMsg("未获得优惠券");
            }
            result.setData(b);
        }catch (Exception e){
            log.error("发放50元的优惠券，分享注册给H5使用错误：", e);
            result.setCode(ReturnCodeConstant.ERROR);
            result.setMsg("服务器错误");
        }
        return result;
    }

    /**
     * 获取可以使用的代金券接口
     * @param userId
     * @param useScopeDetail
     * @param scopeType
     * @return
     */
    @RequestMapping(value = {"/goodsCoupons.action"})
    @ResponseBody
    public ResultMsgBean getCouponsByGoods(@RequestParam(required = true) Long userId,
                                          @RequestParam(required = true) int useScopeDetail,
                                          @RequestParam(required = true) int scopeType) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功！");
        try {
            List<UserCoupon> couponList = couponService.getGoodsCouponByUserId(userId,scopeType, useScopeDetail);
            msgBean.setData(couponList);
            msgBean.setExtraData(new Date());
        }catch (Exception e){
            log.error("获取可以使用的代金券接口失败：", e);
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
        }
        return msgBean;
    }

    /**
     * 获取用户所有有效的代金券.
     * @param userId userId
     * @param useScopeDetail useScopeDetail
     * @param useScopeDetailArray useScopeDetailArray
     * @return ResultMsgBean
     */
    @GetMapping(value = {"/getAllValidCoupon"})
    @ResponseBody
    public ResultMsgBean getAllValidCoupon(Long userId, Integer useScopeDetail, String useScopeDetailArray) {
        try {

            if(CommonUtil.hasNull(userId, useScopeDetail, useScopeDetailArray)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            List<Integer> scopeDetailList = CommonUtil.arraySplitInteger(useScopeDetailArray);

            if(CollectionUtils.isEmpty(scopeDetailList)){
                throw TytException.createException(ResponseEnum.request_error.info());
            }

            List<GoodsUserCoupon> couponList = couponService.getAllValidCoupon(userId, useScopeDetail, scopeDetailList);

            return ResultMsgBean.successResponse(couponList);
        } catch (Exception e) {
            PlatCommonUtil.printErrorInfo("getAllCoupon_error : userId : " + userId, e);
            return ResultMsgBean.failResponse(e);
        }
    }

    /**
     * 根据用户查询活动优惠券（满减券）
     * userId
     * status 0全部  1未使用 2已使用 3无效 4已过期
     * scopeType  1 购买会员 2 购买保险 3支付信息费
      * <AUTHOR> Lion
      * @Description 查询信息费优惠券
      * @Param [userId, callback]
      * @return java.lang.Object
      * @Date 2022/5/30 11:53
      */
    @RequestMapping(value = {"/queryDepositCoupons","/queryDepositCoupons.action"})
    @ResponseBody
    public Object queryDepositCoupons(@RequestParam(required = true) Long userId,
                                      @RequestParam(required = false) String callback, @RequestParam(required = false) Integer goodsType
                                        ,@RequestParam(required = false) String clientSign) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功！");
        try {
            List<UserCoupon> couponList = couponService.queryInfoCouponListByUserId(userId,goodsType,3, 1, 1, 100,clientSign);
            msgBean.setData(couponList);
            msgBean.setExtraData(new Date());
            if(StringUtils.isEmpty(callback)) {
                return msgBean;
            }
        }catch (Exception e){
            log.error("根据用户查询活动优惠券（满减券）失败：", e);
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
        }
        return new JSONPObject(callback, msgBean);
    }


    /**
     * 根据用户Id查询车主所拥有的会员优惠券
     * @param userId
     * @return Object
     */
    @GetMapping(value = {"/queryCarRedPacketCoupons"})
    @ResponseBody
    public Object queryCarRedPacketCoupons(Long userId) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功！");
        try {
            CarRedPacketCouponDetail redPacketCouponDetail = couponService.queryCarRedPacketCouponListByUserId(userId);
            msgBean.setData(redPacketCouponDetail);
            msgBean.setExtraData(new Date());
        }catch (Exception e){
            log.error("PromoController queryRedPacketCoupons 请求异常:",e);
            return  new ResultMsgBean(ResultMsgBean.ERROR, "服务器错误！");
        }
        return msgBean;
    }

    /**
     * 获取可以使用的代金券接口
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/getRenewalCouponStatus.action"})
    @ResponseBody
    public ResultMsgBean getCouponsByGoods(@RequestParam(required = true) Long userId,Integer clientSign) {
        ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK, "查询成功！");
        try {
            if (clientSign!=null){
                int port = Constant.isCarOrGoodsOrOrigin(clientSign);
                SpecialMarketActivityCheckBean bean = specialActivityPopupService.checkUserIsHave3CarTimes(userId,port, ActivityEnum.会员续费活动);
                if (!bean.isHave3Times()){
                    return msgBean;
                }
                for (int i = 0; i < 5; i++) {
                    ActivityBean amount = couponService.isGrantCoupon(userId,port);
                    if (amount!=null){
                        msgBean.setData(amount);
                        return msgBean;
                    }else {
                        Thread.sleep(1000);
                    }
                }
            }
        }catch (Exception e){
            log.error("获取可以使用的代金券接口失败：", e);
            msgBean.setCode(ReturnCodeConstant.ERROR);
            msgBean.setMsg("服务器错误");
        }
        return msgBean;
    }

    /**
     * 获取优惠券弹窗
     * @param userId 用户id
     * @param clientSign 客户端标识
     * @return rm
     */
    @GetMapping(value = {"/getUserVipNotice"})
    @ResponseBody
    public ResultMsgBean getUserVipNotice(@RequestParam Long userId, @RequestParam String clientSign) {
        try{
            int port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
            if (port != Constant.CAR_PORT && port != Constant.GOODS_PORT) {
                return ResultMsgBean.successResponse();
            }
            List<RedPacketCouponBean> couponBeanList = couponService.queryRedPacketCouponListByUserId(userId, port);
            return ResultMsgBean.successResponse(couponBeanList);
        }catch (Exception e){
            log.error("获取优惠券弹窗失败：", e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "获取优惠券弹窗失败");
        }

    }

    /**
     * 购买会员引导
     * @param userId 用户id
     * @param clientSign 客户端标识
     * @return rm
     */
    @GetMapping(value = {"/getUserPermissionAndCoupon"})
    @ResponseBody
    public ResultMsgBean getUserPermissionAndCoupon(@RequestParam Long userId, @RequestParam String clientSign, String clientVersion) {
        int port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
        if (port != Constant.CAR_PORT && port != Constant.GOODS_PORT) {
            return ResultMsgBean.successResponse();
        }
        UserPermissionAndCouponBean bean = new UserPermissionAndCouponBean();
        //获取权益信息
        List<UserPermissionGetBean> userPermissions = userPermissionService.getUserPermissionByServiceId(userId, port);
        bean.setUserPermissions(userPermissions);
        // 获取优惠券信息
        UserCoupon userCoupon = couponService.getMaxAmountCoupon(userId, port);
        if (null != userCoupon) {
            bean.setCoupon(userCoupon);
        } else {
            String price = configService.getStringValue(VIP_MIN_AVG_PRICE);
            if (StringUtils.isNotBlank(price)) {
                VipPriceNoticeJsonBean priceBean = JSON.parseObject(price, VipPriceNoticeJsonBean.class);
                if (port == Constant.CAR_PORT) {
                    bean.setVipPriceContent(priceBean.getCarPrice());
                    bean.setPriceDifferent(priceBean.getCarPriceDifferent());
                } else {
                    bean.setVipPriceContent(priceBean.getGoodsPrice());
                    bean.setPriceDifferent(priceBean.getGoodsPriceDifferent());
                }
            }
        }
        if (port == Constant.GOODS_PORT){
            Integer userType = abtestService.getUserType(Constant.TRANSMISSION_AB_TEST_KEY, userId);
            if (userType == 1){
                VipNoticeForGoodsBean notice = couponService.getNoticeContent(userId);
                bean.setVipNotice(notice);
            }
        }
        return ResultMsgBean.successResponse(bean);
    }

    /**
     * 获取用户过期优惠券
     * @param userId 用户id
     * @param clientSign 客户端标识
     * @return rm
     */
    @GetMapping(value = {"/expireCoupons"})
    @ResponseBody
    public ResultMsgBean getExpireCoupons(@RequestParam Long userId, @RequestParam String clientSign){
        int port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
        if (port != Constant.CAR_PORT && port != Constant.GOODS_PORT) {
            return ResultMsgBean.successResponse();
        }

        List<UserCoupon> userCoupons = couponService.getExpireCoupons(userId,port);

        return ResultMsgBean.successResponse(userCoupons);
    }

    /**
     * 领取优惠券
     * @param userId 用户id
     * @param couponId 优惠券id
     * @return rm
     */
    @PostMapping(value = {"/updateCouponStatus"})
    @ResponseBody
    public ResultMsgBean updateCouponStatus(@RequestParam Long userId, @RequestParam Integer couponId){
        try {
            couponService.updateCouponStatus(userId,couponId);
            return ResultMsgBean.successResponse();
        }catch (Exception e){
            return ResultMsgBean.failResponse(e);
        }

    }



}
