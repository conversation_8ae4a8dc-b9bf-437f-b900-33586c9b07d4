package com.tyt.promo.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.PromoGiftBox;
import com.tyt.model.PromoGiftCoupon;
import com.tyt.promo.dao.PromoGiftCouponDao;
import com.tyt.promo.model.GiftCouponDetail;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository("promoGiftCouponDao")
public class PromoGiftCouponDaoImpl extends BaseDaoImpl<PromoGiftCoupon,Integer> implements PromoGiftCouponDao {

    public PromoGiftCouponDaoImpl(){
        this.setEntityClass(PromoGiftCoupon.class);
    }

    @Override
    public List<GiftCouponDetail> getGiftDetailForGiftId(Integer giftId, Long userId, Date ctime) {
        String sql=" SELECT * FROM (SELECT uc.coupon_type_id  couponTypeId,  uc.coupon_name couponName,uc.coupon_amount couponAmount,uc.expire_time expireTime, gc.coupon_num  couponNum" +
                " FROM promo_gift_coupon gc LEFT JOIN promo_user_coupon uc ON gc.gift_id=uc.gift_id " +
                " WHERE gc.gift_id=? AND gc.coupon_id=uc.coupon_type_id AND uc.user_id=?  AND uc.ctime<=? ORDER BY uc.ctime DESC ) a GROUP BY a.couponTypeId";
        List<Object> list = new ArrayList<Object>();
        list.add(giftId);
        list.add(userId);
        list.add(ctime);

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("couponTypeId", Hibernate.INTEGER);
        scalarMap.put("couponName", Hibernate.STRING);
        scalarMap.put("couponAmount", Hibernate.BIG_DECIMAL);
        scalarMap.put("expireTime", Hibernate.TIMESTAMP);
        scalarMap.put("couponNum", Hibernate.INTEGER);

        List<GiftCouponDetail> detailList = this.search(sql, scalarMap, GiftCouponDetail.class, list.toArray());
        if(detailList!=null && detailList.size()>0){
            return detailList;
        }else{
            return null;
        }
    }

    @Override
    public PromoGiftBox getGiftBoxDetailForGiftId(Integer giftId) {
        String sql="  SELECT g.name NAME,g.total_num totalNum,g.total_amount totalAmount FROM `promo_gift_box` g WHERE g.id=? ";
        List<Object> list = new ArrayList<Object>();
        list.add(giftId);

        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("name", Hibernate.STRING);
        scalarMap.put("totalNum", Hibernate.INTEGER);
        scalarMap.put("totalAmount", Hibernate.BIG_DECIMAL);

        List<PromoGiftBox> detailList = this.search(sql, scalarMap, PromoGiftBox.class, list.toArray());
        if(detailList!=null && detailList.size()>0){
            return detailList.get(0);
        }else{
            return null;
        }
    }
}
