package com.tyt.promo.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.PromoCoupon;
import com.tyt.model.PromoUserCoupon;
import com.tyt.promo.dao.CouponDao;
import com.tyt.promo.model.UserCoupon;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.Session;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository("couponDao")
public class CouponDaoImpl extends BaseDaoImpl<PromoCoupon,Integer> implements CouponDao {
    private static final Logger logger = LoggerFactory.getLogger(CouponDaoImpl.class);

    public CouponDaoImpl(){
        this.setEntityClass(PromoCoupon.class);
    }

    @Override
    public List<UserCoupon> queryCouponListByUserId(Long userId, int scopeType, int status, int couponType,
                                                    int pageNo, int pageSize,String clientSign,Integer goodsType) throws Exception {
        Date endTime = TimeUtil.weeHours(TimeUtil.addDay(new Date(),-3),0);
        StringBuffer sql=new StringBuffer("select " +
                "SYSDATE() extraDate, user_coupon.id id, coupon.coupon_type_id couponTypeId, coupon.coupon_name couponName, " +
                "coupon.coupon_desc couponDesc, coupon.coupon_amount couponAmount, coupon.use_scope_type useScopeType, " +
                "coupon.use_scope_detail useScopeDetail,coupon.valid_type validType, coupon.valid_date_begin validDateBegin," +
                "coupon.valid_date_end validDateEnd,coupon.valid_days validDays,coupon.link_type linkType, coupon.link_target  linkTarget, coupon.coupon_type couponType,coupon.use_condition useCondition," +
                "user_coupon.coupon_status couponStatus,user_coupon.expire_time expireTime, user_coupon.ctime collectTime," +
                "coupon.use_type useType,coupon.type_name typeName,coupon.use_goods_mark useGoodsMark " +
                "from promo_user_coupon as user_coupon left join promo_coupon as coupon on user_coupon.coupon_type_id = coupon.coupon_type_id " +
                "where 1=1 and user_coupon.del_flag=0 and user_coupon.del_flag=0 " +
                " and user_coupon.coupon_status != 3  and coupon.coupon_status!=2  ");
        List<Object> list = new ArrayList<Object>();
        if (StringUtils.isNotBlank(clientSign)){
            if ("21".equals(clientSign) || "31".equals(clientSign)){
                sql.append(" and coupon.use_type in (0,1)");
            }
            if ("22".equals(clientSign) || "32".equals(clientSign)){
                sql.append(" and coupon.use_type in (0,2)");
            }
        }
        if(userId!=null){
            sql.append(" and user_coupon.user_id =?");
            list.add(userId);
        }
        if(status>0){
            sql.append(" and user_coupon.coupon_status=? ");
            list.add(status);
        }
        if(couponType == 2){
            sql.append(" and coupon.coupon_type=? ");
            list.add(couponType);
        }
        if(scopeType>0){
            sql.append(" and coupon.use_scope_type =? ");
            list.add(scopeType);
        }
        if(Objects.nonNull(goodsType)){
            sql.append(" and (coupon.use_goods_mark =? OR coupon.use_goods_mark =2) ");
            list.add(goodsType);
        }
        sql.append(" and user_coupon.expire_time>=?");
        list.add(endTime);
        sql.append(" order by user_coupon.expire_time  ");

        logger.info("查询优惠券列表sql:{},goodsType:{}",sql,goodsType);
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("extraDate", Hibernate.TIMESTAMP);
        scalarMap.put("id", Hibernate.INTEGER);
        scalarMap.put("couponTypeId", Hibernate.INTEGER);
        scalarMap.put("couponName", Hibernate.STRING);
        scalarMap.put("couponDesc", Hibernate.STRING);
        scalarMap.put("couponAmount", Hibernate.BIG_DECIMAL);
        scalarMap.put("useScopeType", Hibernate.INTEGER);
        scalarMap.put("useScopeDetail", Hibernate.STRING);
        scalarMap.put("validType", Hibernate.INTEGER);
        scalarMap.put("validDateBegin", Hibernate.TIMESTAMP);
        scalarMap.put("validDateEnd", Hibernate.TIMESTAMP);
        scalarMap.put("validDays", Hibernate.INTEGER);
        scalarMap.put("linkType", Hibernate.STRING);
        scalarMap.put("linkTarget", Hibernate.STRING);
        scalarMap.put("couponType", Hibernate.INTEGER);
        scalarMap.put("useCondition", Hibernate.INTEGER);
        scalarMap.put("couponStatus", Hibernate.INTEGER);
        scalarMap.put("expireTime", Hibernate.TIMESTAMP);
        scalarMap.put("collectTime", Hibernate.TIMESTAMP);
        scalarMap.put("useType", Hibernate.INTEGER);
        scalarMap.put("typeName", Hibernate.STRING);
        scalarMap.put("useGoodsMark", Hibernate.INTEGER);
        List<UserCoupon> couponList = this.search(sql.toString(), scalarMap, UserCoupon.class, list.toArray(), pageNo, pageSize);

        return couponList;
    }


    @Override
    public List<UserCoupon> queryCarRedPacketCouponListByUserId(Long userId) throws Exception {
        StringBuffer sql=new StringBuffer("select " +
                "SYSDATE() extraDate, user_coupon.id id, coupon.coupon_type_id couponTypeId, coupon.coupon_name couponName, " +
                "coupon.coupon_desc couponDesc, coupon.coupon_amount couponAmount, coupon.use_scope_type useScopeType, " +
                "coupon.use_scope_detail useScopeDetail,coupon.valid_type validType, coupon.valid_date_begin validDateBegin," +
                "coupon.valid_date_end validDateEnd,coupon.valid_days validDays,coupon.link_type linkType, coupon.link_target  linkTarget, coupon.coupon_type couponType,coupon.use_condition useCondition," +
                "user_coupon.coupon_status couponStatus,user_coupon.expire_time expireTime, user_coupon.ctime collectTime," +
                "coupon.use_type useType,coupon.type_name typeName,coupon.use_goods_mark useGoodsMark " +
                "from promo_user_coupon as user_coupon left join promo_coupon as coupon on user_coupon.coupon_type_id = coupon.coupon_type_id " +
                "where  user_coupon.del_flag=0 and user_coupon.coupon_status= 1  and coupon.coupon_status!=2 and coupon.use_scope_type=8 ");
        List<Object> list = new ArrayList<Object>();

        if(userId!=null){
            sql.append(" and user_coupon.user_id =?");
            list.add(userId);
        }
        sql.append(" order by user_coupon.expire_time  ");

        logger.info("queryCarRedPacketCouponListByUserId sql:{},userId:{}",sql,userId);
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("extraDate", Hibernate.TIMESTAMP);
        scalarMap.put("id", Hibernate.INTEGER);
        scalarMap.put("couponTypeId", Hibernate.INTEGER);
        scalarMap.put("couponName", Hibernate.STRING);
        scalarMap.put("couponDesc", Hibernate.STRING);
        scalarMap.put("couponAmount", Hibernate.BIG_DECIMAL);
        scalarMap.put("useScopeType", Hibernate.INTEGER);
        scalarMap.put("useScopeDetail", Hibernate.STRING);
        scalarMap.put("validType", Hibernate.INTEGER);
        scalarMap.put("validDateBegin", Hibernate.TIMESTAMP);
        scalarMap.put("validDateEnd", Hibernate.TIMESTAMP);
        scalarMap.put("validDays", Hibernate.INTEGER);
        scalarMap.put("linkType", Hibernate.STRING);
        scalarMap.put("linkTarget", Hibernate.STRING);
        scalarMap.put("couponType", Hibernate.INTEGER);
        scalarMap.put("useCondition", Hibernate.INTEGER);
        scalarMap.put("couponStatus", Hibernate.INTEGER);
        scalarMap.put("expireTime", Hibernate.TIMESTAMP);
        scalarMap.put("collectTime", Hibernate.TIMESTAMP);
        scalarMap.put("useType", Hibernate.INTEGER);
        scalarMap.put("typeName", Hibernate.STRING);
        scalarMap.put("useGoodsMark", Hibernate.INTEGER);
        List<UserCoupon> couponList = this.search(sql.toString(), scalarMap, UserCoupon.class, list.toArray());

        return couponList;
    }


    @Override
    public int queryValidRemainQtyByUserId(long userId,Integer clientSign) {
        String sql="select count(1) from promo_user_coupon u left join promo_coupon c on u.coupon_type_id = c.coupon_type_id where u.user_id =? and u.coupon_status = 1";
        if (clientSign!=null){
            if(clientSign == 21 || clientSign == 31){
                sql +=" and c.use_type in (0,1)";
            }
            if (clientSign == 22 || clientSign == 32){
                sql +=" and c.use_type in (0,2)";
            }
        }
        Object query = this.query(sql, new Object[]{userId});
        if(query!=null){
            return Integer.valueOf(String.valueOf(query)).intValue();
        }
        return 0;
    }

    @Override
    public List<UserCoupon> getGoodsCouponByUserId(Long userId, int scopeType, int useScopeDetail) {

        StringBuffer sql=new StringBuffer("select " +
                "user_coupon.id id, coupon.coupon_type_id couponTypeId, coupon.coupon_name couponName, " +
                "coupon.coupon_desc couponDesc,coupon.coupon_amount couponAmount, coupon.use_scope_type useScopeType," +
                "coupon.use_scope_detail useScopeDetail,coupon.valid_type validType, coupon.valid_date_begin validDateBegin," +
                "coupon.valid_date_end validDateEnd,coupon.valid_days validDays, coupon.link_target  linkTarget, coupon.coupon_type couponType,coupon.use_condition useCondition," +
                "coupon.topic_type as topicType, " +
                "user_coupon.coupon_status couponStatus,user_coupon.expire_time expireTime, user_coupon.ctime collectTime " +
                "from promo_user_coupon as user_coupon left join promo_coupon as coupon on user_coupon.coupon_type_id = coupon.coupon_type_id " +
                "where 1=1 and user_coupon.del_flag=0  and coupon.coupon_status!=2 ");
        List<Object> list = new ArrayList<Object>();
        sql.append(" and user_coupon.coupon_status=1 ");
        if(userId!=null){
            sql.append(" and user_coupon.user_id =?");
            list.add(userId);
        }
//        sql.append(" and (coupon.use_scope_detail='0' or coupon.use_scope_detail like ?)");
//        list.add("%"+useScopeDetail+"%");
//        if(scopeType>0){
//            sql.append(" and coupon.use_scope_type =? ");
//            list.add(scopeType);
//        }
        sql.append(" order by user_coupon.expire_time  ");
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.INTEGER);
        scalarMap.put("couponTypeId", Hibernate.INTEGER);
        scalarMap.put("couponName", Hibernate.STRING);
        scalarMap.put("couponDesc", Hibernate.STRING);
        scalarMap.put("couponAmount", Hibernate.BIG_DECIMAL);
        scalarMap.put("useScopeType", Hibernate.INTEGER);
        scalarMap.put("useScopeDetail", Hibernate.STRING);
        scalarMap.put("validType", Hibernate.INTEGER);
        scalarMap.put("validDateBegin", Hibernate.TIMESTAMP);
        scalarMap.put("validDateEnd", Hibernate.TIMESTAMP);
        scalarMap.put("validDays", Hibernate.INTEGER);
        scalarMap.put("linkTarget", Hibernate.STRING);
        scalarMap.put("couponType", Hibernate.INTEGER);
        scalarMap.put("topicType", Hibernate.INTEGER);
        scalarMap.put("useCondition", Hibernate.INTEGER);
        scalarMap.put("couponStatus", Hibernate.INTEGER);
        scalarMap.put("expireTime", Hibernate.TIMESTAMP);
        scalarMap.put("collectTime", Hibernate.TIMESTAMP);
        List<UserCoupon> userCoupons = this.search(sql.toString(), scalarMap, UserCoupon.class, list.toArray());
        // 优惠券按活动主题排序,如果用户存在多个主题的优惠券，则以首个主题券排列在首位
        List<UserCoupon> topicList = new ArrayList<>();
        for (UserCoupon userCoupon : userCoupons) {
            int topicType = userCoupon.getTopicType();
            if (topicType > 0) {
                topicList.add(userCoupon);
            }
        }
        userCoupons.removeAll(topicList);
        userCoupons.addAll(0, topicList);
        // 优惠券按活动主题排序结束

        List<UserCoupon> userCouponList = new ArrayList<>();
        for (UserCoupon userCoupon : userCoupons) {
            String[] goodsIds = userCoupon.getUseScopeDetail().split(",");
            List<String> ids = Arrays.asList(goodsIds);
            if (ids.contains(useScopeDetail+"")){
                userCouponList.add(userCoupon);
            }
        }
        return userCouponList;
    }

    @Override
    public List<UserCoupon> getAllValidCoupon(Long userId){
        String querySql = "select " +
                "user_coupon.id id, coupon.coupon_type_id couponTypeId, coupon.coupon_name couponName, " +
                "coupon.coupon_desc couponDesc,coupon.coupon_amount couponAmount, coupon.use_scope_type useScopeType," +
                "coupon.use_scope_detail useScopeDetail,coupon.valid_type validType, coupon.valid_date_begin validDateBegin," +
                "coupon.valid_date_end validDateEnd,coupon.valid_days validDays, coupon.link_target  linkTarget, coupon.coupon_type couponType,coupon.use_condition useCondition," +
                "coupon.topic_type as topicType, " +
                "user_coupon.coupon_status couponStatus,user_coupon.expire_time expireTime, user_coupon.ctime collectTime " +
                "from promo_user_coupon as user_coupon left join promo_coupon as coupon on user_coupon.coupon_type_id = coupon.coupon_type_id " +
                "where user_coupon.del_flag=0  and coupon.coupon_status!=2 and user_coupon.coupon_status=1 and user_coupon.user_id = ? " +
                " order by user_coupon.id desc limit 300 ";

        Object[] paramArray = {userId};

        Map<String, Type> scalarMap = new HashMap<>();
        scalarMap.put("id", Hibernate.INTEGER);
        scalarMap.put("couponTypeId", Hibernate.INTEGER);
        scalarMap.put("couponName", Hibernate.STRING);
        scalarMap.put("couponDesc", Hibernate.STRING);
        scalarMap.put("couponAmount", Hibernate.BIG_DECIMAL);
        scalarMap.put("useScopeType", Hibernate.INTEGER);
        scalarMap.put("useScopeDetail", Hibernate.STRING);
        scalarMap.put("validType", Hibernate.INTEGER);
        scalarMap.put("validDateBegin", Hibernate.TIMESTAMP);
        scalarMap.put("validDateEnd", Hibernate.TIMESTAMP);
        scalarMap.put("validDays", Hibernate.INTEGER);
        scalarMap.put("linkTarget", Hibernate.STRING);
        scalarMap.put("couponType", Hibernate.INTEGER);
        scalarMap.put("topicType", Hibernate.INTEGER);
        scalarMap.put("useCondition", Hibernate.INTEGER);
        scalarMap.put("couponStatus", Hibernate.INTEGER);
        scalarMap.put("expireTime", Hibernate.TIMESTAMP);
        scalarMap.put("collectTime", Hibernate.TIMESTAMP);

        List<UserCoupon> couponList = this.search(querySql, scalarMap, UserCoupon.class, paramArray);

        return couponList;
    }

    @Override
    public boolean isExistCouponByType(Long userId, Integer activityType) {
        String sql="select count(1) from promo_user_coupon where user_id =? and activity_type = ? and coupon_status = 1";
        Object query = this.query(sql, new Object[]{userId, activityType});
        if(query!=null){
            return Integer.valueOf(String.valueOf(query)).intValue() > 0;
        }
        return false;
    }

    @Override
    public boolean isExistCarVipCouponByUserId(Long userId){
        String sql = "SELECT COUNT(*) FROM `promo_user_coupon` puc LEFT JOIN promo_coupon coupon ON puc.coupon_type_id = coupon.coupon_type_id WHERE puc.user_id =? AND puc.coupon_status = 1 AND (coupon.`use_scope_detail` LIKE '%2%' OR coupon.`use_scope_detail` LIKE '%3%' OR coupon.`use_scope_detail` LIKE '%4%')";
        Object query = this.query(sql, new Object[]{userId});
        if(query!=null){
            return Integer.valueOf(String.valueOf(query)).intValue() > 0;
        }
        return false;
    }

    @Override
    public boolean isExistGoodsVipCouponByUserId(Long userId){
        String sql = "SELECT COUNT(*) FROM `promo_user_coupon` puc LEFT JOIN promo_coupon coupon ON puc.coupon_type_id = coupon.coupon_type_id WHERE puc.user_id =? AND puc.coupon_status = 1 AND coupon.`use_scope_detail` LIKE '%1%'";
        Object query = this.query(sql, new Object[]{userId});
        if(query!=null){
            return Integer.valueOf(String.valueOf(query)).intValue() > 0;
        }
        return false;
    }

    @Override
    public PromoUserCoupon queryUserCoupon(Long userId, Integer couponId) {
        Session session = this.getHibernateTemplate().getSessionFactory().openSession();
        try {
            String querySql = String.format("select * from promo_user_coupon where id = %s and user_id = %s", couponId, userId);
            List<PromoUserCoupon> couponList = session.createSQLQuery(querySql).addEntity(PromoUserCoupon.class).list();
            if (couponList != null && couponList.size() > 0) {
                return couponList.get(0);
            }
        }finally {
            session.close();
        }
        return null;
    }

    @Override
    public PromoCoupon queryCouponTypeInfo(Integer couponTypeId) {
        Session session = this.getHibernateTemplate().getSessionFactory().openSession();
        try {
            String querySql = String.format("select * from promo_coupon where coupon_type_id = %s", couponTypeId);
            List<PromoCoupon> promoCouponList = session.createSQLQuery(querySql).addEntity(PromoCoupon.class).list();
            if (promoCouponList != null && promoCouponList.size() > 0) {
                return promoCouponList.get(0);
            }
        }finally {
            session.close();
        }
        return null;
    }

    @Override
    public List<UserCoupon> getValidCouponByPort(Long userId, int port) {
        StringBuffer querySql = new StringBuffer("select " +
                "user_coupon.id id, coupon.coupon_type_id couponTypeId, coupon.coupon_name couponName, " +
                "coupon.coupon_desc couponDesc,coupon.coupon_amount couponAmount, coupon.use_scope_type useScopeType," +
                "coupon.use_scope_detail useScopeDetail,coupon.valid_type validType, coupon.valid_date_begin validDateBegin," +
                "coupon.valid_date_end validDateEnd,coupon.valid_days validDays, coupon.link_target  linkTarget, coupon.coupon_type couponType,coupon.use_condition useCondition," +
                "coupon.topic_type as topicType, " +
                "user_coupon.coupon_status couponStatus,user_coupon.expire_time expireTime, user_coupon.ctime collectTime " +
                "from promo_user_coupon as user_coupon left join promo_coupon as coupon on user_coupon.coupon_type_id = coupon.coupon_type_id " +
                "where user_coupon.del_flag=0  and coupon.coupon_status!=2 and user_coupon.coupon_status=1 and use_scope_type in (1,8) ");
        List<Object> list = new ArrayList<Object>();
        querySql.append(" and user_coupon.user_id =?");
        list.add(userId);
        if (port == Constant.CAR_PORT) {
            querySql.append(" and coupon.use_type in (0,1)");
        } else {
            querySql.append(" and coupon.use_type in (0,2)");
        }
        querySql.append(" order by coupon.coupon_amount desc limit 300 ");
        Map<String, Type> scalarMap = couponVoMap();

        return this.search(querySql.toString(), scalarMap, UserCoupon.class, list.toArray());
    }

    @Override
    public List<UserCoupon> getExpireCouponByPort(Long userId, int port) {
        StringBuilder querySql = new StringBuilder("select " +
                "user_coupon.id id, coupon.coupon_type_id couponTypeId, coupon.coupon_name couponName, " +
                "coupon.coupon_desc couponDesc,coupon.coupon_amount couponAmount, coupon.use_scope_type useScopeType," +
                "coupon.use_scope_detail useScopeDetail,coupon.valid_type validType, coupon.valid_date_begin validDateBegin," +
                "coupon.valid_date_end validDateEnd,coupon.valid_days validDays, coupon.link_target  linkTarget, coupon.coupon_type couponType,coupon.use_condition useCondition," +
                "coupon.topic_type as topicType, " +
                "user_coupon.coupon_status couponStatus,user_coupon.expire_time expireTime, user_coupon.ctime collectTime " +
                "from promo_user_coupon as user_coupon left join promo_coupon as coupon on user_coupon.coupon_type_id = coupon.coupon_type_id " +
                "where user_coupon.del_flag=0  and coupon.coupon_status!=2 and user_coupon.coupon_status=4 and coupon.use_scope_type in (1,8)");
        List<Object> list = new ArrayList<>();
        querySql.append(" and user_coupon.user_id =?");
        list.add(userId);
        querySql.append(" and user_coupon.expire_time>?");
        list.add(TimeUtil.dateAddMonth(new Date(),-3));
        if (port == Constant.CAR_PORT) {
            querySql.append(" and coupon.use_type in (0,1)");
        } else {
            querySql.append(" and coupon.use_type in (0,2)");
        }

        querySql.append(" order by coupon.coupon_amount desc limit 300 ");
        Map<String, Type> scalarMap = couponVoMap();
        return this.search(querySql.toString(), scalarMap, UserCoupon.class, list.toArray());
    }

    private Map<String, Type> couponVoMap(){
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.INTEGER);
        scalarMap.put("couponTypeId", Hibernate.INTEGER);
        scalarMap.put("couponName", Hibernate.STRING);
        scalarMap.put("couponDesc", Hibernate.STRING);
        scalarMap.put("couponAmount", Hibernate.BIG_DECIMAL);
        scalarMap.put("useScopeType", Hibernate.INTEGER);
        scalarMap.put("useScopeDetail", Hibernate.STRING);
        scalarMap.put("validType", Hibernate.INTEGER);
        scalarMap.put("validDateBegin", Hibernate.TIMESTAMP);
        scalarMap.put("validDateEnd", Hibernate.TIMESTAMP);
        scalarMap.put("validDays", Hibernate.INTEGER);
        scalarMap.put("linkTarget", Hibernate.STRING);
        scalarMap.put("couponType", Hibernate.INTEGER);
        scalarMap.put("topicType", Hibernate.INTEGER);
        scalarMap.put("useCondition", Hibernate.INTEGER);
        scalarMap.put("couponStatus", Hibernate.INTEGER);
        scalarMap.put("expireTime", Hibernate.TIMESTAMP);
        scalarMap.put("collectTime", Hibernate.TIMESTAMP);
        return  scalarMap;
    }
}
