package com.tyt.promo.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.PromoActivity;
import com.tyt.promo.dao.PromoActivityDao;
import org.springframework.stereotype.Repository;

/**
 * @Description  优惠券活动数据层实现类
 * <AUTHOR>
 * @Date  2020/3/12 11:49 
 * @Param 
 * @return 
 **/
@Repository("promoActivityDao")
public class PromoActivityDaoImpl extends BaseDaoImpl<PromoActivity,Integer> implements PromoActivityDao {

    public PromoActivityDaoImpl(){
        this.setEntityClass(PromoActivity.class);
    }

}
