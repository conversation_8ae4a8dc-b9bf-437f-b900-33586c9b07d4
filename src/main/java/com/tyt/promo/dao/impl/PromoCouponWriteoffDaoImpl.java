package com.tyt.promo.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.PromoCouponWriteoff;
import com.tyt.promo.dao.PromoCouponWriteoffDao;
import org.springframework.stereotype.Repository;

@Repository("promoCouponWriteoffDao")
public class PromoCouponWriteoffDaoImpl extends BaseDaoImpl<PromoCouponWriteoff, Integer> implements PromoCouponWriteoffDao {
    public PromoCouponWriteoffDaoImpl() {
        this.setEntityClass(PromoCouponWriteoff.class);
    }
}
