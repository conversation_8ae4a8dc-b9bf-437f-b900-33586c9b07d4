package com.tyt.promo.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.PromoCoupon;
import com.tyt.model.PromoUserCoupon;
import com.tyt.promo.model.UserCoupon;

import java.util.List;

public interface CouponDao extends BaseDao<PromoCoupon,Integer> {

    /**
     *
     * @param couponType  ' 1：全部 2：满减券',
     * @return
     */
    List<UserCoupon> queryCouponListByUserId(Long userId, int scopeType, int status, int couponType,
                                             int pageNo,int pageSize,String clientSign,Integer goodsType) throws Exception;

    /**
     *
     * @param userId 用户id
     * @return List<UserCoupon>
     */
    List<UserCoupon> queryCarRedPacketCouponListByUserId(Long userId) throws Exception;

    /**
     * 查询该用户可用优惠券
     * @param userId
     * @return
     */
    int queryValidRemainQtyByUserId(long userId,Integer clientSign);

    /**
     * 按类型查询用户是否存在优惠券
     *
     * @param userId
     * @param activityType
     * @return
     */
    boolean isExistCouponByType(Long userId, Integer activityType);

    List<UserCoupon> getGoodsCouponByUserId(Long userId, int scopeType, int useScopeDetail);

    /**
     * 获取用户当前所有可用优惠券.
     * @param userId userId
     * @return List
     */
    List<UserCoupon> getAllValidCoupon(Long userId);

    boolean isExistCarVipCouponByUserId(Long userId);

    boolean isExistGoodsVipCouponByUserId(Long userId);

    PromoUserCoupon queryUserCoupon(Long userId, Integer couponId);

    PromoCoupon queryCouponTypeInfo(Integer couponTypeId);

    List<UserCoupon> getValidCouponByPort(Long userId, int port);

    List<UserCoupon> getExpireCouponByPort(Long userId, int port);
}
