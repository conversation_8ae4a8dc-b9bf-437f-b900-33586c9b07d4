package com.tyt.promo.dao;

import com.tyt.base.dao.BaseDao;
import com.tyt.model.PromoGiftBox;
import com.tyt.model.PromoGiftCoupon;
import com.tyt.promo.model.GiftCouponDetail;

import java.util.Date;
import java.util.List;

public interface PromoGiftCouponDao extends BaseDao<PromoGiftCoupon,Integer> {
    List<GiftCouponDetail> getGiftDetailForGiftId(Integer giftId, Long userId, Date ctime);

    PromoGiftBox getGiftBoxDetailForGiftId(Integer giftId);
}
