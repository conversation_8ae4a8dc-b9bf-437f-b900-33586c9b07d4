package com.tyt.promo.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.PromoCouponWriteoff;
import com.tyt.promo.service.PromoCouponWriteoffService;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("promoCouponWriteoffService")
public class PromoCouponWriteoffServiceImpl extends BaseServiceImpl<PromoCouponWriteoff, Integer> implements PromoCouponWriteoffService {
    private Logger logger = LoggerFactory.getLogger(PromoCouponWriteoffServiceImpl.class);

    @Resource(name = "promoCouponWriteoffDao")
    public void setBaseDao(BaseDao<PromoCouponWriteoff, Integer> promoCouponWriteoffDao) {
        super.setBaseDao(promoCouponWriteoffDao);
    }


    @Override
    public PromoCouponWriteoff getByUserIdAndOrderId(Long userId, String orderId) {
        String sql="SELECT * FROM promo_coupon_writeoff w WHERE  user_id = ? AND order_id = ? AND coupon_status=2 order by id desc ";
        List<PromoCouponWriteoff> list = this.getBaseDao().queryForList(sql,new Object[]{userId, orderId});
        if(list != null && list.size() > 0){
            PromoCouponWriteoff promoCouponWriteoff = list.get(0);
            return promoCouponWriteoff;
        }
        return null;
    }

    @Override
    public void updateStatus(String orderId) {
        String sql="UPDATE `promo_coupon_writeoff` SET coupon_status=1,mtime=NOW() WHERE order_id=? AND coupon_status=2 ";
        this.getBaseDao().executeUpdateSql(sql,new Object[]{orderId});
    }
}
