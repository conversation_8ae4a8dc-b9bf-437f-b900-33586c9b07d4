package com.tyt.promo.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.DistributeCouponByTypeMsgBean;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.UserPermissionResponseBean;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.PromoActivity;
import com.tyt.model.PromoCoupon;
import com.tyt.model.PromoUserCoupon;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.service.user.UserAlertService;
import com.tyt.plat.vo.coupon.GoodsUserCoupon;
import com.tyt.promo.constant.PromoConstant;
import com.tyt.promo.dao.CouponDao;
import com.tyt.promo.model.*;
import com.tyt.promo.service.ICouponService;
import com.tyt.promo.service.PromoUserCouponService;
import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.enums.UserPermissionTypeEnum;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service("couponService")
public class CouponServiceImpl extends BaseServiceImpl<PromoCoupon,Integer>  implements ICouponService {
    private static final Logger logger = LoggerFactory.getLogger(CouponServiceImpl.class);

    @Resource(name = "couponDao")
    @Override
    public void setBaseDao(BaseDao<PromoCoupon,Integer> couponDao) {
        super.setBaseDao(couponDao);
    }
    @Resource(name="couponDao")
    private CouponDao couponDao;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Autowired
    private UserAlertService userAlertService;

//    @Autowired
//    private PromoCouponMapper couponMapper;

    @Autowired
    private PromoUserCouponService promoUserCouponService;

    private static final int COUPON_PAYING = 1;

    private static final int COUPON_PAID = 2;

    private static final Integer[] CAR_VIP_ID_LIST = {2,3,4};
    private static final Integer[] GOODS_VIP_ID_LIST = {500,501,502};

    @Override
    public List<UserCoupon> queryCouponListByUserId(Long userId,int scopeType, int status, int pageNo, int pageSize,String clientSign) throws Exception{
//        List<UserCoupon> couponList = couponMapper.queryCouponListByUserId(userId,scopeType, status, offset, count);
        List<UserCoupon> couponList =  couponDao.queryCouponListByUserId(userId,scopeType, status,1, pageNo, pageSize,clientSign,null);
//        if(status != 0){
//            //根据状态过滤
//            couponList = couponList.stream().filter(coupon -> coupon.getCouponStatus() == status).collect(Collectors.toList());
//        }
        //couponStatus排序
        couponList = couponList.stream().sorted((coupon1, coupon2 ) -> {
            int coupon1Status = coupon1.getCouponStatus();
            int coupon2Status = coupon2.getCouponStatus();
            if(coupon1Status == coupon2Status){
                return 0;
            }else if(coupon1Status > coupon2Status){
                return 1;
            }else {
                return -1;
            }
        }).collect(Collectors.toList());
        return couponList;
    }

    /**
     * 查询新增优惠券
     * @param userId
     * @return
     */
    @Override
    public List<UserCoupon> getUserNewCoupons(String userId) {
        List<UserCoupon> couponList = new ArrayList<>();
        Map<String, String> couponMap = RedisUtil.getMap(userId);
        if(couponMap != null) {
            Iterator<String> ites = couponMap.keySet().iterator();
            while(ites.hasNext()) {
                String couponId = ites.next();
                String coupoonStr = couponMap.get(couponId);
                if(StringUtils.isNotEmpty(coupoonStr)){
                    try {
                        UserCoupon userCoupon = new ObjectMapper().readValue(coupoonStr, UserCoupon.class);
                        couponList.add(userCoupon);
                    } catch (IOException e) {
                        logger.error("反序列化优惠券error! {}", ExceptionUtils.getStackTrace(e));
                    }
                }
                RedisUtil.mapRemove(userId, couponId);
            }
        }
        return couponList;
    }

    @Override
    public int getValidRemainQtyByUserId(long userId,Integer clientSign){
//        int remainQty = couponMapper.queryValidRemainQtyByUserId(userId);
        int remainQty =couponDao.queryValidRemainQtyByUserId(userId,clientSign);
        return remainQty;
    }

    @Override
    public void giveShareCoupon(Long userId,Integer activityType) {
        DistributeCouponByTypeMsgBean mqMsg = new DistributeCouponByTypeMsgBean();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(MqBaseMessageBean.GIVE_USER_COUPON);
        //用户ID
        mqMsg.setUserId(userId);
        mqMsg.setActivityType(activityType);

        // 保存发送mq
        final String messageSerailNum = mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        final int messageType = mqMsg.getMessageType();
        logger.info("用户发送指定类型的优惠券，发送MQ:{}", mqJson);
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                //发送并mq信息并保存到数据库
                tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, MqBaseMessageBean.GIVE_USER_COUPON);
                tytMqMessageService.sendMqMessage(messageSerailNum, mqJson, MqBaseMessageBean.GIVE_USER_COUPON);
            }
        });
        // 关闭线程
        executorService.shutdown();
    }

    @Override
    public boolean isExistCouponByShare50(Long userId) {
        return couponDao.isExistCouponByType(userId, PromoConstant.ACTIVITE_SHARE_TYPE_50);
    }
    @Override
    public List<UserCoupon> getGoodsCouponByUserId(Long userId, int scopeType, int useScopeDetail) {
        return couponDao.getGoodsCouponByUserId(userId,scopeType, useScopeDetail);
    }

    @Override
    public boolean isExistCarVipCouponByUserId(Long userId) {
        return couponDao.isExistCarVipCouponByUserId(userId);
    }

    @Override
    public boolean isExistGoodsVipCouponByUserId(Long userId) {
        return couponDao.isExistGoodsVipCouponByUserId(userId);
    }

    @Override
    public List<UserCoupon> queryInfoCouponListByUserId(Long userId,Integer goodsType,int scopeType, int status, int pageNo, int pageSize,String clientSign) throws Exception {
        List<UserCoupon> couponList = couponDao.queryCouponListByUserId(userId, scopeType, status, 1, pageNo, pageSize,clientSign,goodsType);
        //按照面额couponAmount倒序排列
        couponList = couponList.stream().sorted(Comparator.comparing(UserCoupon::getCouponAmount).reversed())
                .collect(Collectors.toList());
        return couponList;
    }

    @Override
    public CarRedPacketCouponDetail queryCarRedPacketCouponListByUserId(Long userId) throws Exception {
        //定义返回实体类
        CarRedPacketCouponDetail redPacketCouponDetail = new CarRedPacketCouponDetail();
        redPacketCouponDetail.setShowRedPacket(false);
        redPacketCouponDetail.setUserId(userId);
        //根据userId获取用户名下可用车主优惠券
        List<UserCoupon> allCouponList = couponDao.queryCarRedPacketCouponListByUserId(userId);
        //当couponList等于空时或者等于0时
        if (allCouponList == null || allCouponList.size() == 0) {
            //判断其是否购买过会员 false 未购买过  true 购买过
            boolean userBuyGoods = userPermissionService.judgeUserBuyGoods(userId);
            if (!userBuyGoods) {
                //判断手机号归属地非保定或非邢台  0 不弹  1弹
                Integer popEnum = userAlertService.vipRedPacket(userId);
                if (null != popEnum && popEnum == 1) {
                    redPacketCouponDetail.setShowRedPacket(true);
                }
            }
            redPacketCouponDetail.setUserBuyGoods(userBuyGoods);
            redPacketCouponDetail.setUserCouponList(allCouponList);
            return redPacketCouponDetail;
        }

        //过滤可用优惠券 不包含不包含通用券(端通用和 车主 一年 两年 三年通用)
        List<UserCoupon> couponList= allCouponList.stream().filter(coupon->coupon.getUseType()==1&&!coupon.getUseScopeDetail().contains(",")).collect(Collectors.toList());
        //当只有一张优惠券时 直接返回进行展示
        if (couponList == null || couponList.size() == 0||couponList.size() == 1) {
            redPacketCouponDetail.setUserCouponList(couponList);
            return redPacketCouponDetail;
        }
        //当存在多张优惠券时
        if (couponList.size() > 1) {
            //调用多张优惠券时排序分组处理接口
            List<UserCoupon> userCouponList = handleCouponSort(couponList);
            //将最终结果放置到返回list中
            redPacketCouponDetail.setUserCouponList(userCouponList);
        }
        return redPacketCouponDetail;
    }

    /**
     * 多张优惠券时排序分组处理
     * @param couponList 优惠券集合
     * @return List<UserCoupon> 排序分组后的优惠券集合
     */
    private  List<UserCoupon> handleCouponSort(List<UserCoupon> couponList){
        //定义排序规则:先按照金额倒序排序 再按照过期时间正序排序
        Comparator<UserCoupon> comparator = Comparator.comparing(UserCoupon::getCouponAmount, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(UserCoupon::getExpireTime, Comparator.nullsLast(Comparator.naturalOrder()));
        //先分组
        Map<String, List<UserCoupon>> userCouponMap = couponList.stream().collect(Collectors.groupingBy(UserCoupon::getUseScopeDetail));

        //设置各个分组首位的list
        List<UserCoupon> groupFirstList = new ArrayList<>();

        //遍历map
        for (String userCouponKey : userCouponMap.keySet()) {
            List<UserCoupon> userCoupons = userCouponMap.get(userCouponKey);
            if (null != userCoupons && userCoupons.size() > 0) {
                // 将每个组内的集合 按照 排序规则 排列 并取出第一个
                List<UserCoupon> userCouponSortList = userCoupons.stream().sorted(comparator).collect(Collectors.toList());
                groupFirstList.add(userCouponSortList.get(0));
            }
        }
        //然后将各个分组中的首位 放到一起 按照使用年限 由大到小排序
        return  groupFirstList.stream().sorted(Comparator.comparing(UserCoupon::getUseScopeDetail, Comparator.nullsLast(Comparator.reverseOrder()))).collect(Collectors.toList());
    }

    @Override
    public ActivityBean isGrantCoupon(Long userId, Integer port){
        String sql = "SELECT user_id userId,coupon_amount couponAmount,time_type timeType FROM `vip_renewal_activity_list` WHERE user_id=? AND port=? AND is_grant=2";
        Object[] params=new Object[] { userId, port };
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("couponAmount", Hibernate.INTEGER);
        scalarMap.put("timeType", Hibernate.INTEGER);
        List<ActivityBean> search = this.getBaseDao().search(sql, scalarMap, ActivityBean.class, params);
        if (search!=null && search.size()>0){
            return search.get(0);
        }
        return null;
    }

    @Override
    public PromoUserCoupon queryUserCoupon(Long userId, Integer couponId) {
        PromoUserCoupon userCoupon = ((CouponDao)getBaseDao()).queryUserCoupon(userId, couponId);
        return userCoupon;
    }

    @Override
    public PromoCoupon queryCouponTypeInfo(Integer couponTypeId) {
        PromoCoupon promoCoupon = ((CouponDao)getBaseDao()).queryCouponTypeInfo(couponTypeId);
        return promoCoupon;
    }

    /**
     * @description 新增核销记录并更新用户优惠券记录
     * <AUTHOR>
     * @date 2022/11/16 17:07
     * @param orderId
     * @param agencyMoney
     * @param coupon
     * @param couponId
     * @param userId
     * @return void
     */
    @Override
    public void addWriteOffCoupon(Long orderId, Long agencyMoney, PromoCoupon coupon, Integer couponId, Long userId) {

        //1.插入核销记录表
        String sql = "INSERT INTO `tyt`.`promo_coupon_writeoff` (`user_id`, `order_id`, `order_amount`, `coupon_id`, `coupon_name`, `coupon_type_id`, `coupon_amount`, `coupon_status`, `mtime`, `ctime` )\n" +
                "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        Date now = new Date();
        BigDecimal couponAmount = coupon.getCouponAmount();
        String couponName = coupon.getCouponName();
        int couponTypeId = coupon.getCouponTypeId();
        BigDecimal orderAmount = new BigDecimal(agencyMoney).add(couponAmount);
        Object[] params = new Object[] {userId, orderId, orderAmount, couponId, couponName, couponTypeId, couponAmount, COUPON_PAID, now, now};
        this.getBaseDao().executeUpdateSql(sql, params);

        //2.更新用户优惠券记录表
        String updateSql = "update promo_user_coupon set coupon_status = 2, consume_time = now(),mtime=now() where id = ?";
        Object[] updateParams = new Object[] {couponId};
        this.getBaseDao().executeUpdateSql(updateSql, updateParams);
    }

    @Override
    public List<UserCoupon> getByActivity(PromoActivity promoActivity){
        List<Object> params=new ArrayList<Object>();
        Map<String, org.hibernate.type.Type> scalarMap=new HashMap<String, org.hibernate.type.Type>();
        String sql = "";
        if (promoActivity.getGrantType() ==1){ //单券
            sql = "SELECT c.`coupon_type_id` couponTypeId,c.`coupon_name` couponName,c.`coupon_amount` couponAmount,c.`coupon_desc` couponDesc,c.`type_name` typeName,c.`valid_type` validType,c.`valid_days` validDays,c.`link_type`linkType,c.`link_target` linkTarget FROM `promo_coupon` c  WHERE c.coupon_type_id=?";
        }else{ //礼包
            sql = "SELECT c.`coupon_type_id` couponTypeId,c.`coupon_name` couponName,c.`coupon_amount` couponAmount,c.`coupon_desc` couponDesc,c.`type_name` typeName,c.`valid_type` validType,c.`valid_days` validDays,c.`link_type`linkType,c.`link_target` linkTarget FROM `promo_gift_coupon` g  LEFT JOIN `promo_coupon` c ON g.`coupon_id`=c.coupon_type_id WHERE g.gift_id=?";
        }
        params.add(promoActivity.getGrantTypeId());
        scalarMap.put("couponTypeId", Hibernate.INTEGER);
        scalarMap.put("couponName", Hibernate.STRING);
        scalarMap.put("couponAmount", Hibernate.BIG_DECIMAL);
        scalarMap.put("couponDesc", Hibernate.STRING);
        scalarMap.put("typeName", Hibernate.STRING);
        scalarMap.put("validType", Hibernate.INTEGER);
        scalarMap.put("validDays", Hibernate.INTEGER);
        scalarMap.put("linkType", Hibernate.STRING);
        scalarMap.put("linkTarget", Hibernate.STRING);
        return this.getBaseDao().search(sql.toString(), scalarMap, UserCoupon.class, params.toArray());
    }

    /**
     * 校验当前优惠券是否在该类型下可用.
     * @param userCoupon userCoupon
     * @param useScopeDetail useScopeDetail
     * @return boolean
     */
    private boolean checkScopeDetailValid(UserCoupon userCoupon, Integer useScopeDetail){

        String useScopeDetailText = userCoupon.getUseScopeDetail();

        List<Integer> detailList = CommonUtil.arraySplitInteger(useScopeDetailText);

        boolean result = (CollectionUtils.isNotEmpty(detailList) && detailList.contains(useScopeDetail));

        return result;
    }

    private void sortUserCoupon(List<UserCoupon> userCouponList){

        if(CollectionUtils.isEmpty(userCouponList)){
            return;
        }

        //定义排序规则:先按照金额倒序排序 再按照过期时间正序排序
        Comparator<UserCoupon> comparator = Comparator.comparing(UserCoupon::getCouponAmount, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(UserCoupon::getExpireTime, Comparator.nullsLast(Comparator.naturalOrder()));

        userCouponList.sort(comparator);

    }

    /**
     * 获得匹配的优惠券.
     * @param allCouponList allCouponList
     * @param useScopeDetail useScopeDetail
     * @return List
     */
    private List<UserCoupon> getMatchUserCouponList(List<UserCoupon> allCouponList, Integer useScopeDetail){
        List<UserCoupon> userCouponListTmp = new ArrayList<>();

        Iterator<UserCoupon> couponIterator = allCouponList.iterator();

        while(couponIterator.hasNext()){
            UserCoupon oneUserCoupon = couponIterator.next();

            boolean scopeValid = this.checkScopeDetailValid(oneUserCoupon, useScopeDetail);

            if(scopeValid){
                userCouponListTmp.add(oneUserCoupon);
                couponIterator.remove();
            }
        }

        this.sortUserCoupon(userCouponListTmp);

        return userCouponListTmp;
    }

    @Override
    public List<GoodsUserCoupon> getAllValidCoupon(Long userId, Integer useScopeDetail, List<Integer> scopeDetailList) {

        List<UserCoupon> allCouponList = couponDao.getAllValidCoupon(userId);

        List<GoodsUserCoupon> goodsCouponList = new ArrayList<>();

        //当前选中的可用优惠券
        List<UserCoupon> currentCouponList = this.getMatchUserCouponList(allCouponList, useScopeDetail);

        GoodsUserCoupon currentGoodsCoupon = new GoodsUserCoupon();
        currentGoodsCoupon.setUseScopeDetail(useScopeDetail);
        currentGoodsCoupon.setCouponList(currentCouponList);

        goodsCouponList.add(currentGoodsCoupon);

        //剩余其他年限的会员优惠券
        scopeDetailList.remove(useScopeDetail);

        //剩余不可用优惠券
        if(CollectionUtils.isNotEmpty(scopeDetailList)){
            scopeDetailList.sort((a, b) -> b - a);

            for(Integer oneScopeDetail: scopeDetailList){
                //当前选中的可用优惠券
                List<UserCoupon> userCouponListTmp = this.getMatchUserCouponList(allCouponList, oneScopeDetail);

                GoodsUserCoupon goodsCouponTmp = new GoodsUserCoupon();
                goodsCouponTmp.setUseScopeDetail(oneScopeDetail);
                goodsCouponTmp.setCouponList(userCouponListTmp);

                goodsCouponList.add(goodsCouponTmp);
            }
        }
        return goodsCouponList;

    }

    @Override
    public List<RedPacketCouponBean> queryRedPacketCouponListByUserId(Long userId, int port) {
        List<UserCoupon> allCouponList = couponDao.getValidCouponByPort(userId, port);

        List<RedPacketCouponBean> redPacketCouponBeans = new ArrayList<>();
        List<Integer> scopeDetailList = null;
        String couponName = null;
        if (port == Constant.CAR_PORT){
            scopeDetailList = Arrays.asList(CAR_VIP_ID_LIST);
            couponName = "无限找货优惠券";
        }else {
            scopeDetailList = Arrays.asList(GOODS_VIP_ID_LIST);
            couponName = "无限发货优惠券";
        }

        boolean flag = false;
        for (Integer useScopeDetail : scopeDetailList) {
            List<UserCoupon> currentCouponList = this.getMatchUserCouponList(allCouponList, useScopeDetail);
            if (CollectionUtils.isNotEmpty(currentCouponList)){
                UserCoupon coupon = currentCouponList.get(0);
                RedPacketCouponBean redPacketCouponBean = new RedPacketCouponBean();
                BeanUtils.copyProperties(coupon,redPacketCouponBean);
                String useType = CouponUseTypeEnum.getUseType(useScopeDetail);
                redPacketCouponBean.setGoodsId(Long.parseLong(useScopeDetail.toString()));
                redPacketCouponBean.setCouponUseType(useType);
                if (port == Constant.CAR_PORT){
                    redPacketCouponBean.setCouponName(couponName);
                }else{
                    redPacketCouponBean.setCouponName(useType + "会员优惠券");
                }
                redPacketCouponBean.setValidDateEnd(coupon.getExpireTime());
                redPacketCouponBeans.add(redPacketCouponBean);
            }
        }
        Comparator<RedPacketCouponBean> comparator = Comparator.comparing(RedPacketCouponBean::getGoodsId, Comparator.nullsLast(Comparator.reverseOrder()));
        redPacketCouponBeans.sort(comparator);
        return redPacketCouponBeans;
    }

    @Override
    public UserCoupon getMaxAmountCoupon(Long userId, int port) {

        List<UserCoupon> allCouponList = couponDao.getValidCouponByPort(userId, port);
        if (CollectionUtils.isEmpty(allCouponList)){
            return null;
        }
        Comparator<UserCoupon> comparator = Comparator.comparing(UserCoupon::getCouponAmount, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(UserCoupon::getExpireTime, Comparator.nullsLast(Comparator.naturalOrder()));
        allCouponList.sort(comparator);
        return allCouponList.get(0);
    }

    @Override
    public List<UserCoupon> getExpireCoupons(Long userId, int port) {
        return couponDao.getExpireCouponByPort(userId, port);
    }

    @Override
    public void updateCouponStatus(Long userId, Integer couponId) {
        // 获取优惠券信息
        PromoUserCoupon coupon = promoUserCouponService.getById(couponId);
        if (null == coupon || !coupon.getUserId().equals(userId)){
            throw TytException.createException(new ResponseCode(ReturnCodeConstant.ERROR, "优惠券不存在"));
        }
        promoUserCouponService.updateCouponStatus(couponId);
    }

    @Override
    public VipNoticeForGoodsBean getNoticeContent(Long userId) {
        VipNoticeForGoodsBean noticeBean = new VipNoticeForGoodsBean();

        //用户是否购买过货会员
        List<UserPermissionResponseBean> list = userPermissionService.getUserOwnerPermission(userId, UserPermissionTypeEnum.GOODS_VIP.getTypeId(), UserPermissionTypeEnum.GOODS_NUM_NEW.getTypeId());
        if (CollectionUtils.isNotEmpty(list)){
            noticeBean.setBuyType(2);
            BigDecimal amount = getGoodsVipDiscountAmount(userId, CouponUseTypeEnum.GOODS_NEW_VIP_3);
            noticeBean.setCouponAmount(amount);
        }else {
            noticeBean.setBuyType(1);
            BigDecimal amount = getGoodsVipDiscountAmount(userId, CouponUseTypeEnum.GOODS_NEW_VIP_1);
            noticeBean.setCouponAmount(amount);
        }
        return noticeBean;
    }

    private UserCoupon getMaxAmountCouponByScope(Long userId, Integer goodsId, int port) {
        List<UserCoupon> couponList = couponDao.getValidCouponByPort(userId, port);
        for (UserCoupon userCoupon : couponList) {
            String[] goodsIds = userCoupon.getUseScopeDetail().split(",");
            List<String> ids = Arrays.asList(goodsIds);
            if (ids.contains(goodsId + "")){
                return userCoupon;
            }
        }
        return null;
    }

    @Override
    public BigDecimal getGoodsVipDiscountAmount(Long userId, CouponUseTypeEnum userType) {
        BigDecimal amount = new BigDecimal(userType.getDiscountAmount());
        // 获取用户一年金额最大优惠券
        UserCoupon maxAmountCoupon = this.getMaxAmountCouponByScope(userId, userType.getCode(), 2);
        logger.info("获取用户{}年最大优惠券{}",userType.getCode(),JSON.toJSONString(maxAmountCoupon));
        if (maxAmountCoupon != null){
            amount = amount.add(maxAmountCoupon.getCouponAmount());
        }
        return amount;
    }

}
