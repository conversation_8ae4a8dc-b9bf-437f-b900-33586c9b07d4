package com.tyt.promo.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytNoticeRemindService;
import com.tyt.model.PromoGiftBox;
import com.tyt.model.PromoGiftCoupon;
import com.tyt.model.TytNoticeRemind;
import com.tyt.promo.dao.PromoGiftCouponDao;
import com.tyt.promo.model.GiftCouponDetail;
import com.tyt.promo.service.PromoGiftCouponService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("promoGiftCouponService")
public class PromoGiftCouponServiceImpl extends BaseServiceImpl<PromoGiftCoupon,Integer> implements PromoGiftCouponService {

    @Resource(name = "promoGiftCouponDao")
    public void setBaseDao(BaseDao<PromoGiftCoupon,Integer> promoGiftCouponDao) {
        super.setBaseDao(promoGiftCouponDao);
    }
    @Resource(name="promoGiftCouponDao")
    private PromoGiftCouponDao promoGiftCouponDao;
    @Resource(name="tytNoticeRemindService")
    private TytNoticeRemindService noticeRemindService;
    @Override
    public Map<String ,Object> getGiftDetailForGiftId(Integer giftId, Integer noticeId, Long userId) {
        TytNoticeRemind remind = noticeRemindService.getById(Long.valueOf(noticeId));
        List<GiftCouponDetail> couponList=null;
        if(remind!=null){
           couponList=promoGiftCouponDao.getGiftDetailForGiftId(giftId,userId,remind.getCtime());

        }
        PromoGiftBox giftDetail=promoGiftCouponDao.getGiftBoxDetailForGiftId(giftId);

        Map<String ,Object> map=new HashMap<>();
        map.put("giftDetail",giftDetail);
        map.put("couponList",couponList);
        return map;
    }
}
