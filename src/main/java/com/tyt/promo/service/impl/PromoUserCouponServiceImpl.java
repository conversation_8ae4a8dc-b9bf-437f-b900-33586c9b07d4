package com.tyt.promo.service.impl;

import cn.hutool.db.DaoTemplate;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.PromoUserCoupon;
import com.tyt.promo.service.PromoUserCouponService;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.crypto.Data;
import java.util.Date;

@Service("promoUserCouponService")
public class PromoUserCouponServiceImpl extends BaseServiceImpl<PromoUserCoupon, Integer> implements PromoUserCouponService {

    private Logger logger = LoggerFactory.getLogger(PromoUserCouponServiceImpl.class);

    @Override
    @Resource(name = "promoUserCouponDao")
    public void setBaseDao(BaseDao<PromoUserCoupon, Integer> promoUserCouponDao) {
        super.setBaseDao(promoUserCouponDao);
    }

    @Override
    public void updateStatus(Integer couponId) {
        String sql="UPDATE `promo_user_coupon` SET coupon_status=1,mtime=NOW() WHERE id =? ";
        this.getBaseDao().executeUpdateSql(sql,new Object[]{couponId});
    }

    @Override
    public void updateCoupom(Integer id, Integer coupomStatus) {
        String sql="UPDATE `promo_user_coupon` SET coupon_status=?,mtime=NOW() WHERE id =?";
        this.getBaseDao().executeUpdateSql(sql,new Object[]{coupomStatus,id});
    }

    @Override
    public void updateCouponStatus(Integer couponId) {
        Date expireTime = TimeUtil.weeHours(new Date(),1);
        String sql="UPDATE `promo_user_coupon` SET coupon_status=1,expire_time=?,mtime=NOW() WHERE id =? and coupon_status=4";
        this.getBaseDao().executeUpdateSql(sql,new Object[]{expireTime,couponId});
    }
}
