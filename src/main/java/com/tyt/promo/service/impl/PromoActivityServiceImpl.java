package com.tyt.promo.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.bean.SendAwardByActivityIdMsgBean;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.model.PromoActivity;
import com.tyt.promo.service.PromoActivityService;
import com.tyt.util.SerialNumUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("promoActivityService")
public class PromoActivityServiceImpl extends BaseServiceImpl<PromoActivity,Integer> implements PromoActivityService {

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;
    @Resource(name = "promoActivityDao")
    public void setBaseDao(BaseDao<PromoActivity,Integer> promoActivityDao) {
        super.setBaseDao(promoActivityDao);
    }

    @Override
    public void sendCouponByActivityIdMQ(Long userId, Integer activityId) {
        SendAwardByActivityIdMsgBean mqMsg = new SendAwardByActivityIdMsgBean();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(MqBaseMessageBean.SEND_COUPON_BY_ACTIVITY_ID);
        //用户ID
        mqMsg.setUserId(userId);
        //活动id
        mqMsg.setActivityId(activityId);

        // 保存发送mq
        final String messageSerailNum = mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        final int messageType = mqMsg.getMessageType();

        //发送并mq信息并保存到数据库
        tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, messageType);
        tytMqMessageService.sendMqMessageDirect(mqMsg.getMessageSerailNum(), JSON.toJSONString(mqMsg), 1000L);
    }
}
