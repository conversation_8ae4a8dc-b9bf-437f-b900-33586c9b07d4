package com.tyt.promo.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.PromoActivity;

/**
 * @Description  优惠券活动服务层
 * <AUTHOR>
 * @Date  2020/3/12 11:51
 * @Param
 * @return
 **/
public interface PromoActivityService extends BaseService<PromoActivity,Integer> {

    /**
     * 发放优惠券
     * @param userId
     * @param activityId
     */
    void sendCouponByActivityIdMQ(Long userId, Integer activityId);
}
