package com.tyt.promo.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.PromoGiftCoupon;

import java.util.Map;

public interface PromoGiftCouponService extends BaseService<PromoGiftCoupon,Integer> {
    /**
     * 根据礼包id查询礼包优惠券详情
     * @return
     * @param giftId
     * @param noticeId
     * @param userId
     */
    Map<String ,Object> getGiftDetailForGiftId(Integer giftId, Integer noticeId, Long userId);
}
