package com.tyt.promo.service;


import com.tyt.model.PromoUserCoupon;


import java.math.BigInteger;
import java.util.List;

public interface PromoUserCouponService {

    /**
     * 修改状态为1
     * @param couponId
     */
    void updateStatus(Integer couponId);

    /**
     * 修改状态
     * @param id
     * @param coupomStatus
     */
    void updateCoupom(Integer id, Integer coupomStatus);

    PromoUserCoupon getById(Integer id);

    void updateCouponStatus(Integer couponId);
}
