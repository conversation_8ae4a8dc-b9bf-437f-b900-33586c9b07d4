package com.tyt.promo.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.PromoActivity;
import com.tyt.model.PromoCoupon;
import com.tyt.model.PromoUserCoupon;
import com.tyt.plat.vo.coupon.GoodsUserCoupon;
import com.tyt.promo.model.*;

import java.math.BigDecimal;
import java.util.List;

public interface ICouponService extends BaseService<PromoCoupon,Integer> {

    List<UserCoupon> queryCouponListByUserId(Long userId,int scopeType, int status, int pageNo, int pageSize,String clientSign) throws Exception;

    List<UserCoupon> getUserNewCoupons(String userId);

    int getValidRemainQtyByUserId(long userId,Integer clientSign);

    /**
     * 给分享用户发放优惠券
     * @param userId
     */
    void giveShareCoupon(Long userId,Integer activityType);

    /**
     * 按类型查询用户是否存在分享获得50优惠券
     *
     * @param userId
     * @return
     */
    boolean isExistCouponByShare50(Long userId);

    List<UserCoupon> getGoodsCouponByUserId(Long userId, int scopeType, int useScopeDetail);

    boolean isExistCarVipCouponByUserId(Long userId);

    boolean isExistGoodsVipCouponByUserId(Long userId);

    List<UserCoupon> queryInfoCouponListByUserId(Long userId,Integer goodsType,int scopeType, int status, int pageNo, int pageSize,String clientSign) throws Exception;


    CarRedPacketCouponDetail queryCarRedPacketCouponListByUserId(Long userId) throws Exception;


    ActivityBean isGrantCoupon(Long userId, Integer port);

    PromoUserCoupon queryUserCoupon(Long userId, Integer couponId);

    PromoCoupon queryCouponTypeInfo(Integer couponTypeId);

    /**
     * @description 新增核销记录并更新用户优惠券记录
     * <AUTHOR>
     * @date 2022/11/16 17:07
     * @param orderId
     * @param agencyMoney
     * @param coupon
     * @param couponId
     * @param userId
     * @return void
     */
    void addWriteOffCoupon(Long orderId, Long agencyMoney, PromoCoupon coupon, Integer couponId, Long userId);


    List<UserCoupon> getByActivity(PromoActivity promoActivity);

    /**
     * 获取用户所有有效的代金券.
     * @param userId userId
     * @param useScopeDetail useScopeDetail
     * @param scopeDetailList scopeDetailList
     * @return List
     */
    List<GoodsUserCoupon> getAllValidCoupon(Long userId, Integer useScopeDetail, List<Integer> scopeDetailList);

    /**
     * 用户优惠券弹窗提醒
     * @param userId 用户id
     * @param port 端
     * @return list
     */
    List<RedPacketCouponBean> queryRedPacketCouponListByUserId(Long userId, int port);

    /**
     * 获取最大金额优惠券
     * @param userId 用户id
     * @param port 1车 2货
     * @return userCoupon
     */
    UserCoupon getMaxAmountCoupon(Long userId, int port);

    /**
     * 获取过期3个月的优惠券
     * @param userId 用户id
     * @param port 1车 2货
     * @return list
     */
    List<UserCoupon> getExpireCoupons(Long userId, int port);

    void updateCouponStatus(Long userId, Integer couponId);

    VipNoticeForGoodsBean getNoticeContent(Long userId);

    BigDecimal getGoodsVipDiscountAmount(Long userId, CouponUseTypeEnum userType);
}
