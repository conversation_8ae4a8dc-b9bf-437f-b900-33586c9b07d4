package com.tyt.promo.model;

import com.tyt.util.Constant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/04/12 13:54
 */
@Getter
public enum CouponUseTypeEnum {

    CAR_VIP_1(2,"1年", "210"),
    CAR_VIP_2(3,"2年", "710"),
    CAR_VIP_3(4,"3年", "1410"),
    GOODS_VIP_1(1,"1年" , "320"),
    GOODS_VIP_2(26,"2年", "1220"),
    GOODS_VIP_3(27,"3年", "2240"),
    GOODS_NEW_VIP_1(500,"1年", "320"),
    GOODS_NEW_VIP_2(501,"2年","1220"),
    GOODS_NEW_VIP_3(502,"3年","2240");


    private final Integer code;

    private final String name;

    private final String discountAmount;

    CouponUseTypeEnum(Integer code, String name, String discountAmount) {
        this.code = code;
        this.name = name;
        this.discountAmount = discountAmount;
    }


    public static String getUseType(int code) {
        CouponUseTypeEnum[] useTypes = CouponUseTypeEnum.values();
        for (CouponUseTypeEnum useType : useTypes) {
            if (useType.code == code) {
                return useType.getName();
            }
        }
        return null;
    }

    public static CouponUseTypeEnum getCouponUserType(int code) {
        CouponUseTypeEnum[] useTypes = CouponUseTypeEnum.values();
        for (CouponUseTypeEnum useType : useTypes) {
            if (useType.code == code) {
                return useType;
            }
        }
        return null;
    }





}
