package com.tyt.promo.model;

import java.math.BigDecimal;
import java.util.Date;

public class PromoCoupon {
    private int couponTypeId;

    private String couponName;

    private BigDecimal couponAmount;

    private int couponStatus;

    private int couponQty;

    private String couponDesc;

    private int useScopeType;

    private String useScopeDetail;

    private int validType;

    private Date validDateBegin;

    private Date validDateEnd;

    private int validDays;

    private Date mtime;

    private Date ctime;

    public int getCouponTypeId() {
        return couponTypeId;
    }

    public void setCouponTypeId(int couponTypeId) {
        this.couponTypeId = couponTypeId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public int getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(int couponStatus) {
        this.couponStatus = couponStatus;
    }

    public int getCouponQty() {
        return couponQty;
    }

    public void setCouponQty(int couponQty) {
        this.couponQty = couponQty;
    }

    public String getCouponDesc() {
        return couponDesc;
    }

    public void setCouponDesc(String couponDesc) {
        this.couponDesc = couponDesc;
    }

    public int getUseScopeType() {
        return useScopeType;
    }

    public void setUseScopeType(int useScopeType) {
        this.useScopeType = useScopeType;
    }

    public String getUseScopeDetail() {
        return useScopeDetail;
    }

    public void setUseScopeDetail(String useScopeDetail) {
        this.useScopeDetail = useScopeDetail;
    }

    public int getValidType() {
        return validType;
    }

    public void setValidType(int validType) {
        this.validType = validType;
    }

    public Date getValidDateBegin() {
        return validDateBegin;
    }

    public void setValidDateBegin(Date validDateBegin) {
        this.validDateBegin = validDateBegin;
    }

    public Date getValidDateEnd() {
        return validDateEnd;
    }

    public void setValidDateEnd(Date validDateEnd) {
        this.validDateEnd = validDateEnd;
    }

    public int getValidDays() {
        return validDays;
    }

    public void setValidDays(int validDays) {
        this.validDays = validDays;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
}