package com.tyt.promo.model;

import java.math.BigDecimal;
import java.util.Date;

public class UserCoupon {
    private int id;

    private int couponTypeId;

    private String couponName;

    private BigDecimal couponAmount;

    private int couponStatus;

    private String couponDesc;

    private int useScopeType;

    private String useScopeDetail;

    private int validType;

    private Date validDateBegin;

    private Date validDateEnd;

    private int validDays;

    private String linkType;
    private String linkTarget;

    private Date collectTime;

    private Date expireTime;
    private int couponType;
    private Integer useCondition;
    private Date extraDate;
    private Integer useType;

    private Integer useGoodsMark;

    /**
     * 主题类型，0-默认主题，1-n对应不同主题，1-会员时长
     */
    private Integer topicType;

    private String typeName;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getCouponTypeId() {
        return couponTypeId;
    }

    public void setCouponTypeId(int couponTypeId) {
        this.couponTypeId = couponTypeId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public int getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(int couponStatus) {
        this.couponStatus = couponStatus;
    }

    public String getCouponDesc() {
        return couponDesc;
    }

    public void setCouponDesc(String couponDesc) {
        this.couponDesc = couponDesc;
    }

    public int getUseScopeType() {
        return useScopeType;
    }

    public void setUseScopeType(int useScopeType) {
        this.useScopeType = useScopeType;
    }

    public String getUseScopeDetail() {
        return useScopeDetail;
    }

    public void setUseScopeDetail(String useScopeDetail) {
        this.useScopeDetail = useScopeDetail;
    }

    public int getValidType() {
        return validType;
    }

    public void setValidType(int validType) {
        this.validType = validType;
    }

    public Date getValidDateBegin() {
        return validDateBegin;
    }

    public void setValidDateBegin(Date validDateBegin) {
        this.validDateBegin = validDateBegin;
    }

    public Date getValidDateEnd() {
        return validDateEnd;
    }

    public void setValidDateEnd(Date validDateEnd) {
        this.validDateEnd = validDateEnd;
    }

    public int getValidDays() {
        return validDays;
    }

    public void setValidDays(int validDays) {
        this.validDays = validDays;
    }

    public String getLinkType() {
        return linkType;
    }

    public void setLinkType(String linkType) {
        this.linkType = linkType;
    }

    public String getLinkTarget() {
        return linkTarget;
    }

    public void setLinkTarget(String linkTarget) {
        this.linkTarget = linkTarget;
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public int getCouponType() {
        return couponType;
    }

    public void setCouponType(int couponType) {
        this.couponType = couponType;
    }

    public Integer getUseCondition() {
        return useCondition;
    }

    public void setUseCondition(Integer useCondition) {
        this.useCondition = useCondition;
    }

    public Date getExtraDate() {
        return extraDate;
    }

    public void setExtraDate(Date extraDate) {
        this.extraDate = extraDate;
    }

    public Integer getUseType() {
        return useType;
    }

    public void setUseType(Integer useType) {
        this.useType = useType;
    }

    public Integer getTopicType() {
        return topicType;
    }

    public void setTopicType(Integer topicType) {
        this.topicType = topicType;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getUseGoodsMark() {
        return useGoodsMark;
    }

    public void setUseGoodsMark(Integer useGoodsMark) {
        this.useGoodsMark = useGoodsMark;
    }
}