package com.tyt.marketingActivity.service;

import com.tyt.activate.bean.ActivateCheckBean;
import com.tyt.acvitity.bean.ActivityListResp;
import com.tyt.base.service.BaseService;
import com.tyt.marketingActivity.bean.MarketingActivityPopupBean;
import com.tyt.marketingActivity.bean.OrderEncourageBean;
import com.tyt.model.MarketingActivity;
import com.tyt.transport.querybean.PopupTemplBean;
import com.tyt.user.querybean.CreditConfigInfo;

import java.util.List;

public interface MarketingActivityService extends BaseService<MarketingActivity,Long> {
    /**
     * 获取用户运营弹窗
     * @param userId
     * @param port
     * @return
     */
    List<MarketingActivityPopupBean> getPopupList(Long userId, int port,String clientVersion,String clientSign) throws Exception;

    void shareReporting(Long userId, int port);

    PopupTemplBean getShareReportNotice(Long userId);

    OrderEncourageBean getPcEncouragePopou(Long userId, Integer activityId);

    List<ActivityListResp> getMyActivityList(Long userId, int port);

    /**
     * @description 查询用户是否在有效活动中
     * <AUTHOR>
     * @date 2022/8/1 14:01
     * @param userId
     * @param activityId
     * @return boolean
     */
    boolean getUserIsExistInActivity(Long userId, Integer activityId);

    ActivityListResp getActivityByUserId(Long userId, Integer activityId);


    CreditConfigInfo getUserExistCreditConfig(Long userId);

    void updateUserConfigStatus(Long configId,Long subId,Long userId);

    boolean isValidActivity(Long activityId, Long userId);

    MarketingActivity getValidActivityByType(Integer activityType,Integer activityPart);

    void shareReportNew(Long userId, Long shareGiveGoodsId);

    boolean userIsValidByType(Integer activityType, Long userId);
}
