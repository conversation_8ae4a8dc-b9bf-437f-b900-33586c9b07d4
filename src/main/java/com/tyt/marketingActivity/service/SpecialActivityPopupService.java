package com.tyt.marketingActivity.service;

import com.tyt.base.service.BaseService;
import com.tyt.marketingActivity.bean.ActivityEnum;
import com.tyt.marketingActivity.bean.MarketActivityBean;
import com.tyt.marketingActivity.bean.SpecialMarketActivityCheckBean;
import com.tyt.model.MarketingActivity;

import java.util.List;

public interface SpecialActivityPopupService {
    /**
     * 获取特殊活动弹窗
     * @param userId
     * @param port
     * @return
     * @throws Exception
     */
    List<MarketActivityBean> getSpecialConditionPopup(Long userId, int port) throws Exception;

    List<MarketActivityBean> getCarUnverifiedPopup(Long userId,int port);

    /**
     * 小于 V3等级的飘窗 提示
     * @param userId
     * @param port
     * @return
     */
    List<MarketActivityBean> getCreditLevelPopup(Long userId,int port);

    /**
     * 检查是否赠送3次车次卡权益
     * @param userId
     * @param port
     * @param activityEnum
     * @return
     * @throws Exception
     */
    SpecialMarketActivityCheckBean checkUserIsHave3CarTimes(Long userId, int port, ActivityEnum activityEnum) throws Exception;
}
