package com.tyt.marketingActivity.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.bean.ActivityListResp;
import com.tyt.acvitity.service.TytCarCreditUpgradePopupService;
import com.tyt.adposition.service.TytStartAdvertService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.marketingActivity.bean.*;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.marketingActivity.service.SpecialActivityPopupService;
import com.tyt.model.MarketingActivity;
import com.tyt.model.MarketingActivityUser;
import com.tyt.mybatis.mapper.MarketingActivityUserMapper;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.bean.GoodsType;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.querybean.PopupTemplBean;
import com.tyt.user.querybean.CreditConfigInfo;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("marketingActivityService")
public class MarketingActivityServiceImpl extends BaseServiceImpl<MarketingActivity,Long> implements MarketingActivityService {

    @Resource(name = "marketingActivityDao")
    public void setBaseDao(BaseDao<MarketingActivity, Long> marketingActivityDao) {
        super.setBaseDao(marketingActivityDao);
    }
    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "tytStartAdvertService")
    private TytStartAdvertService tytStartAdvertService;
    @Resource(name = "specialActivityPopupService")
    private SpecialActivityPopupService specialActivityPopupService;
    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private TytCarCreditUpgradePopupService tytCarCreditUpgradePopupService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource
    private MarketingActivityUserMapper marketingActivityUserMapper;

    @Autowired
    private TytNoticePopupTemplService noticePopupTemplService;

    public static final int[] ACTIVITY_POPUP_POSITION = {};

    private static final String SHARE_GOODS_REPORT_REDIS_KEY = "share_goods_report_";

    @Override
    public List<MarketingActivityPopupBean> getPopupList(Long userId, int port,String clientVersion,String clientSign) throws Exception{
        //获取用户参加的活动
        String scopeSql = "SELECT a.id id,a.popup_id_one popupIdOne,a.popup_id_two popupIdTwo FROM marketing_activity_user u LEFT JOIN marketing_activity a ON u.activity_id=a.id WHERE u.user_id=? AND u.is_delete=1 AND u.is_join=1 AND a.activity_scope=2 AND a.is_need_popup=1 AND a.activity_part=? AND a.status=1 AND a.start_time<NOW() AND a.end_time>NOW() limit 10";
        //传入的参数
        Object[] params = new Object[] {userId,port};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("popupIdOne", Hibernate.LONG);
        scalarMap.put("popupIdTwo", Hibernate.LONG);
        List<MarketActivityBean> popupList =  this.getBaseDao().search(scopeSql, scalarMap, MarketActivityBean.class, params);
        //获取全量用户参加的活动
        String allSql = "SELECT a.id id,a.popup_id_one popupIdOne,a.popup_id_two popupIdTwo FROM marketing_activity a WHERE a.activity_scope=1 AND a.is_need_popup=1 AND a.activity_part=? AND a.status=1 AND a.start_time<NOW() AND a.end_time>NOW() limit 10";
        //传入的参数
        Object[] allParams = new Object[] {port};
        Map<String, Type> allScalarMap = new HashMap<String, Type>();
        allScalarMap.put("id", Hibernate.LONG);
        allScalarMap.put("popupIdOne", Hibernate.LONG);
        allScalarMap.put("popupIdTwo", Hibernate.LONG);
        List<MarketActivityBean> popupList2 =  this.getBaseDao().search(allSql, allScalarMap, MarketActivityBean.class, allParams);
        popupList.addAll(popupList2);
        //获取特殊条件查询弹窗
        List<MarketActivityBean> popupList3 = specialActivityPopupService.getSpecialConditionPopup(userId,port);
        popupList.addAll(popupList3);
        if(CollectionUtils.isEmpty(popupList)){
            List<MarketingActivityPopupBean> marketingActivityPopupBeans = new ArrayList<>();
            List<MarketingActivityPopupBean> specialPopup = getSpecialPopup(port, userId, marketingActivityPopupBeans);
            return specialPopup;
        }
        String upgradeVersions = null;
        int minVersion = 0;
        int upgradeId = 0;
        int creditId=0;
        if (port == 1){
            upgradeVersions = tytConfigService.getStringValue("tyt:plat:config:force_car_upgrade_version");
            upgradeId = tytConfigService.getIntValue("tyt:plat:config:car:upgrade:activity:id",0);
            minVersion = tytConfigService.getIntValue("tyt:plat:config:car:activity:min:version",0);
            creditId = tytConfigService.getIntValue("tyt:plat:config:car:credit:activity:id",0);
        }else if (port == 2){
            upgradeVersions = tytConfigService.getStringValue("tyt:plat:config:force_goods_upgrade_version");
            upgradeId = tytConfigService.getIntValue("tyt:plat:config:goods:upgrade:activity:id",0);
            minVersion = tytConfigService.getIntValue("tyt:plat:config:goods:activity:min:version",0);
            creditId = tytConfigService.getIntValue("tyt:plat:config:goods:credit:activity:id",0);
        }
        //获取弹窗id
        String ids ="";
        if (StringUtils.isNotBlank(upgradeVersions)){
            if (upgradeVersions.contains(clientVersion) && ("22".equals(clientSign) || "21".equals(clientSign))){
                List<MarketingActivityPopupBean> marketingActivityPopupBeans = new ArrayList<>();
                for (MarketActivityBean marketActivityBean : popupList) {
                    if (marketActivityBean.getId().intValue()==upgradeId){
                        if (marketActivityBean.getPopupIdOne()!=null){
                            ids += marketActivityBean.getPopupIdOne()+",";
                        }
                        if (marketActivityBean.getPopupIdTwo()!=null){
                            ids += marketActivityBean.getPopupIdTwo()+",";
                        }
                        //获取弹窗
                        marketingActivityPopupBeans = tytStartAdvertService.getAdListByIds(ids.substring(0,ids.length()-1));
                        return getSpecialPopup(port,userId,marketingActivityPopupBeans);
                    }
                }
            }else{
                Iterator<MarketActivityBean> iterator = popupList.iterator();
                while (iterator.hasNext()){
                    MarketActivityBean next = iterator.next();
                    if ( next.getId().intValue() == upgradeId){
                        iterator.remove();
                    }
                }
            }
        }
        if (minVersion!=0 && creditId!=0){
            if (CollectionUtils.isNotEmpty(popupList) && Integer.parseInt(clientVersion) < minVersion){
                Iterator<MarketActivityBean> iterator = popupList.iterator();
                while (iterator.hasNext()){
                    MarketActivityBean next = iterator.next();
                    if (next.getId().intValue() == creditId){
                        iterator.remove();
                    }
                }
            }
        }
        List<MarketingActivityPopupBean> marketingActivityPopupBeans = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(popupList)){
            for (MarketActivityBean marketActivityBean : popupList) {
                if (marketActivityBean.getPopupIdOne()!=null){
                    ids += marketActivityBean.getPopupIdOne()+",";
                }
                if (marketActivityBean.getPopupIdTwo()!=null){
                    ids += marketActivityBean.getPopupIdTwo()+",";
                }
            }
            //获取弹窗
            marketingActivityPopupBeans =  tytStartAdvertService.getAdListByIds(ids.substring(0,ids.length()-1));
        }
        return getSpecialPopup(port,userId,marketingActivityPopupBeans);
    }

    @Override
    public void shareReporting(Long userId, int port) {
        try {
            SpecialMarketActivityCheckBean checkBean = specialActivityPopupService.checkUserIsHave3CarTimes(userId, port, ActivityEnum.促活车次卡活动);
            if(checkBean.isHave3Times()){
                String sql = "INSERT IGNORE tyt_permission_give_log (user_id, give_date, give_type, goods_id, create_time, update_time) \n" +
                    "VALUES (?,?,?,?,?,? );";
                Date nowDate = new Date();
                Object[] params = {
                    userId,
                    nowDate,
                    2,
                    GoodsType.分享赠送找货次卡.getId(),
                    nowDate,
                    nowDate
                };
                int i = this.getBaseDao().executeUpdateSql(sql, params);
                //发放分享次卡
                if(i > 0){
                    userPermissionService.giveShare2CarTimesPermission(userId,GoodsType.分享赠送找货次卡.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void shareReportNew(Long userId,  Long shareGiveGoodsId) {
        try {
            String sql = "INSERT IGNORE tyt_permission_give_log (user_id, give_date, give_type, goods_id, create_time, update_time) \n" +
                    "VALUES (?,?,?,?,?,? );";
            Date nowDate = new Date();
            Object[] params = {
                    userId,
                    nowDate,
                    2,
                    shareGiveGoodsId,
                    nowDate,
                    nowDate
            };
            int i = this.getBaseDao().executeUpdateSql(sql, params);
            //发放分享次卡
            if(i > 0){
                userPermissionService.giveShare2CarTimesPermission(userId,shareGiveGoodsId);
                RedisUtil.set(SHARE_GOODS_REPORT_REDIS_KEY+ userId,"1", Constant.CACHE_EXPIRE_TIME_5MIN);
            }
        } catch (Exception e) {
            logger.error("分享赠送找货次卡异常",e);
        }
    }

    @Override
    public PopupTemplBean getShareReportNotice(Long userId) {
        String result = RedisUtil.get(SHARE_GOODS_REPORT_REDIS_KEY+ userId);
        if (StringUtils.isNotBlank(result)){
            PopupTemplBean templBean = noticePopupTemplService.getTemplByType(PopupTypeEnum.车促活分享弹窗,null);
            RedisUtil.del(SHARE_GOODS_REPORT_REDIS_KEY+ userId);
            return templBean;
        }
        return null;


    }

    @Override
    public OrderEncourageBean getPcEncouragePopou(Long userId, Integer activityId) {

        String scopeSql = "SELECT a.start_time startTime,a.end_time endTime FROM marketing_activity_user u LEFT JOIN marketing_activity a ON u.activity_id=a.id WHERE u.user_id=? AND u.activity_id=?  AND u.is_delete=1 AND u.is_join=1  AND a.status=1 AND a.start_time<NOW() AND a.end_time>NOW() limit 10";
        //传入的参数
        Object[] params = new Object[] {userId,activityId};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("startTime", Hibernate.TIMESTAMP);
        scalarMap.put("endTime", Hibernate.TIMESTAMP);
        List<OrderEncourageBean> beanList =  this.getBaseDao().search(scopeSql, scalarMap, OrderEncourageBean.class, params);
        if (CollectionUtils.isNotEmpty(beanList)){
            return beanList.get(0);
        }
        return null;
    }

    @Override
    public List<ActivityListResp> getMyActivityList(Long userId, int port){
        String sql = "SELECT id activityId,`activity_name` title,`start_time` startTime,`end_time` endTime,`activity_url` linkUrl,`activity_content` content FROM `marketing_activity` WHERE is_need_show=1 AND STATUS=1 AND ctime<NOW() AND activity_part=? AND activity_scope=1";
        //传入的参数
        Object[] params = new Object[] {port};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("activityId", Hibernate.LONG);
        scalarMap.put("title", Hibernate.STRING);
        scalarMap.put("startTime", Hibernate.TIMESTAMP);
        scalarMap.put("endTime", Hibernate.TIMESTAMP);
        scalarMap.put("linkUrl", Hibernate.STRING);
        scalarMap.put("content", Hibernate.STRING);
        List<ActivityListResp> beanList =  this.getBaseDao().search(sql, scalarMap, ActivityListResp.class, params);
        String scopeSql = "select a.id activityId,a.`activity_name` title,a.`start_time` startTime,a.`end_time` endTime,a.`activity_url` linkUrl,a.`activity_content` content from `marketing_activity_user` u left join `marketing_activity` a on u.`activity_id`=a.id where u.`user_id`=? and u.`is_delete`=1 and a.is_need_show=1 and a.status=1 and a.ctime<now() and a.activity_part=? and a.`activity_scope`=2";
        //传入的参数
        Object[] scopeParams = new Object[] {userId,port};
        Map<String, Type> scopeScalarMap = new HashMap<String, Type>();
        scopeScalarMap.put("activityId", Hibernate.LONG);
        scopeScalarMap.put("title", Hibernate.STRING);
        scopeScalarMap.put("startTime", Hibernate.TIMESTAMP);
        scopeScalarMap.put("endTime", Hibernate.TIMESTAMP);
        scopeScalarMap.put("linkUrl", Hibernate.STRING);
        scopeScalarMap.put("content", Hibernate.STRING);
        List<ActivityListResp> listResps =  this.getBaseDao().search(scopeSql, scopeScalarMap, ActivityListResp.class, scopeParams);
        beanList.addAll(listResps);
        //特殊活动 ++
        if(CollectionUtils.isEmpty(beanList)){
            return new ArrayList<>();
        }
        return beanList;
    }

    /**
     * @description 查询用户是否在有效活动中
     * <AUTHOR>
     * @date 2022/8/1 14:01
     * @param userId
     * @param activityId
     * @return boolean
     */
    @Override
    public boolean getUserIsExistInActivity(Long userId, Integer activityId) {
        String sql = "SELECT " +
                " mau.user_id as userId " +
                "FROM " +
                " marketing_activity ma " +
                " JOIN marketing_activity_user mau ON ma.id = mau.activity_id  " +
                "WHERE " +
                " ma.id = :activityId " +
                " AND mau.user_id = :userId " +
                " AND ma.status = 1 " +
                " AND ma.start_time <= NOW() AND ma.end_time >= NOW() " +
                " AND mau.is_delete = 1";

        Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
        map.put("userId", Hibernate.LONG);

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("activityId", activityId);
        paramMap.put("userId", userId);

        List<Long> list = this.getBaseDao().search(sql, map, null, paramMap);
        if(list != null && list.size() > 0){
            return true;
        }
        return false;
    }

    @Override
    public ActivityListResp getActivityByUserId(Long userId, Integer activityId) {
        String scopeSql = "SELECT a.start_time startTime,a.end_time endTime,u.is_join isJoin FROM marketing_activity_user u LEFT JOIN marketing_activity a ON u.activity_id=a.id WHERE u.user_id=? AND u.activity_id=?  AND u.is_delete=1 AND a.status=1";
        //传入的参数
        Object[] params = new Object[] {userId,activityId};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("startTime", Hibernate.TIMESTAMP);
        scalarMap.put("endTime", Hibernate.TIMESTAMP);
        scalarMap.put("isJoin", Hibernate.INTEGER);
        List<ActivityListResp> beanList =  this.getBaseDao().search(scopeSql, scalarMap, ActivityListResp.class, params);
        if (CollectionUtils.isNotEmpty(beanList)){
            return beanList.get(0);
        }
        return null;
    }

    /**
     * 获取特殊的弹窗信息
     * @param port
     * @param userId
     * @param marketingActivityPopupBeans
     * @return
     */
    private List<MarketingActivityPopupBean> getSpecialPopup(Integer port,Long userId,List<MarketingActivityPopupBean> marketingActivityPopupBeans) {
        if (port == 1) {
            try {
                //6270  车辆存在认证中和认证成功的车辆提示信息消失；未进行车辆认证的用户此规则 优先显示活动
                List<MarketActivityBean> carUnverifiedPopup = specialActivityPopupService.getCarUnverifiedPopup(userId, port);
                if (CollectionUtils.isNotEmpty(carUnverifiedPopup)) {
                    Long popupIdOne = carUnverifiedPopup.get(0).getPopupIdOne();
                    List<MarketingActivityPopupBean> adListByIds = tytStartAdvertService.getAdListByIds(String.valueOf(popupIdOne));
                    if (CollectionUtils.isNotEmpty(adListByIds)) {
                        marketingActivityPopupBeans.add(adListByIds.get(0));
                    }
                }
                //6320 车方每月信用升级弹窗
                Integer tytCarCreditUpgradePopupOnOff = tytConfigService.getIntValue("tytCarCreditUpgradePopupOnOff", 0);
                if (tytCarCreditUpgradePopupOnOff == 1){
                    MarketingActivityPopupBean creditUpgradePopupInfo = tytCarCreditUpgradePopupService.getCreditUpgradePopupInfo(userId);
                    logger.info("车方每月信用升级弹窗信息:{}", JSON.toJSON(creditUpgradePopupInfo));
                    if (Objects.nonNull(creditUpgradePopupInfo)) {
                        marketingActivityPopupBeans.add(creditUpgradePopupInfo);
                    }
                }

                //6320 信用等级 <3 且为会员的 进行飘窗提示
                List<MarketActivityBean> creditLevelPopup = specialActivityPopupService.getCreditLevelPopup(userId, port);
                if (CollectionUtils.isNotEmpty(creditLevelPopup)) {
                    Long popupIdOne = creditLevelPopup.get(0).getPopupIdOne();
                    List<MarketingActivityPopupBean> adListByIds = tytStartAdvertService.getAdListByIds(String.valueOf(popupIdOne));
                    if (CollectionUtils.isNotEmpty(adListByIds)) {
                        marketingActivityPopupBeans.add(adListByIds.get(0));
                    }
                }
            } catch (Exception e) {
                logger.error("获取特殊弹窗异常:{}", e.getMessage());
            }
        }
        return marketingActivityPopupBeans;
    }

    /**
     * @description 查询用户是否在有效活动中
     * <AUTHOR>
     * @date 2022/8/1 14:01
     * @param userId
     * @return boolean
     */
    @Override
    public CreditConfigInfo getUserExistCreditConfig(Long userId) {
        String selectSQL = "SELECT " +
                "cmc.start_time AS startTime," +
                "cmc.end_time AS endTime," +
                "cmcu.user_id AS userId," +
                "cmcs.config_id AS configId," +
                "cmcs.id AS subId," +
                "cmcs.target_number AS targetNumber," +
                "cmcs.LEVEL AS LEVEL," +
                "cmcu.is_join AS isJoin " +
                "FROM " +
                "credit_manage_config cmc " +
                "LEFT JOIN credit_manage_config_sub cmcs ON cmc.id = cmcs.config_id " +
                "LEFT JOIN credit_manage_config_user cmcu ON cmcu.sub_id = cmcs.id  " +
                "WHERE " +
                "cmc.STATUS = 1 " +
                "AND cmc.is_delete = 0 " +
                "AND cmcs.is_enable = 1 " +
                "AND cmcu.is_delete =0 " +
                "AND cmc.start_time <= NOW() AND cmc.end_time >= NOW() " +
                "AND cmcu.user_id  = :userId ";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("userId", userId);

        Map<String, org.hibernate.type.Type> map = new HashMap<String, org.hibernate.type.Type>();
        map.put("userId", Hibernate.LONG);
        map.put("configId", Hibernate.LONG);
        map.put("subId", Hibernate.LONG);
        map.put("startTime", Hibernate.TIMESTAMP);
        map.put("endTime", Hibernate.TIMESTAMP);
        map.put("targetNumber", Hibernate.INTEGER);
        map.put("level", Hibernate.STRING);
        map.put("isJoin", Hibernate.INTEGER);

        List<CreditConfigInfo> CreditConfigInfos = this.getBaseDao().search(selectSQL, map, CreditConfigInfo.class, paramMap);
        if(CreditConfigInfos!=null&&CreditConfigInfos.size()>0){
            return CreditConfigInfos.get(0);
        }
        return null;
    }

    @Override
    public void updateUserConfigStatus(Long configId, Long subId, Long userId) {
        marketingActivityUserMapper.updateUserConfigStatus(configId,subId,userId);
    }

    @Override
    public boolean isValidActivity(Long activityId,Long userId){
        MarketingActivity activity = this.getById(activityId);
        if (activity == null || activity.getStatus()!=1){
            return false;
        }
        if (activity.getActivityScope() == 2){
            List<MarketingActivityUser> user = marketingActivityUserMapper.getByActivityId(activityId,userId);
            if (CollectionUtils.isEmpty(user)){
                return false;
            }
        }
        return true;
    }

    @Override
    public MarketingActivity getValidActivityByType(Integer activityType,Integer activityPart){
        String sql = "SELECT * FROM `marketing_activity` WHERE activity_type=? AND activity_part=? AND STATUS = 1 AND start_time<NOW() AND end_time>NOW()";
        List<MarketingActivity> activityList =  this.getBaseDao().queryForList(sql, new Object[] {activityType,activityPart});
        if (CollectionUtils.isNotEmpty(activityList)){
            return activityList.get(0);
        }
        return null;
    }

    @Override
    public boolean userIsValidByType(Integer activityType,Long userId){
        String sql = "SELECT * FROM `marketing_activity` WHERE activity_type=? AND STATUS = 1 AND start_time<NOW() AND end_time>NOW()";
        List<MarketingActivity> activityList =  this.getBaseDao().queryForList(sql, new Object[] {activityType});
        if (CollectionUtils.isNotEmpty(activityList)){
            MarketingActivity activity = activityList.get(0);
            if (activity.getActivityScope() == 2){
                List<MarketingActivityUser> user = marketingActivityUserMapper.getByActivityId(activity.getId(),userId);
                return CollectionUtils.isNotEmpty(user);
            }
            return true;
        }
        return false;
    }


}
