package com.tyt.marketingActivity.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.marketingActivity.bean.FinishNumAndGiveDaysBean;
import com.tyt.marketingActivity.bean.OrderEncourageBean;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.marketingActivity.service.OrderEncourageRuleService;
import com.tyt.model.MarketingActivity;
import com.tyt.model.TytOrderEncourageRule;
import com.tyt.user.service.TytConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("orderEncourageRuleService")
public class OrderEncourageRuleServiceImpl extends BaseServiceImpl<TytOrderEncourageRule,Long> implements OrderEncourageRuleService {
    @Resource(name = "orderEncourageRuleDao")
    public void setBaseDao(BaseDao<TytOrderEncourageRule, Long> orderEncourageRuleDao) {
        super.setBaseDao(orderEncourageRuleDao);
    }

    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Override
    public OrderEncourageBean saveEncourageDetail(Long userId, Long activityId, int flag) {
        OrderEncourageBean orderEncourageBean = new OrderEncourageBean();
        MarketingActivity activity = marketingActivityService.getById(activityId);
        if (activity!=null){
            orderEncourageBean.setStartTime(activity.getStartTime());
            orderEncourageBean.setEndTime(activity.getEndTime());
        }
        List<TytOrderEncourageRule> ruleList = getByActivityId(activityId);
        orderEncourageBean.setRuleList(ruleList);
        if (flag == 1){
            if (tytConfigService.isEqualsOne("encourage_activity_isJoin_onoff")){
                //更改用户为已参加状态
                updateUserStatus(userId,activityId);
            }
        }else{
            //查询支付单数
            int payNum = getPayNum(userId,activityId);
            orderEncourageBean.setPayNum(payNum);
            //查询单数
            FinishNumAndGiveDaysBean bean = getFinishNumAndGiveDays(userId,activityId);
            if (bean!=null){
                orderEncourageBean.setFinishNum(bean.getFinishNum()==null?0:bean.getFinishNum());
                int giveDays = bean.getGiveDays()==null?0:bean.getGiveDays();
                int repealDays = bean.getRepealDays()==null?0:bean.getRepealDays();
                orderEncourageBean.setGiveDays(giveDays-repealDays);
            }else{
                orderEncourageBean.setFinishNum(0);
                orderEncourageBean.setGiveDays(0);
            }
        }
        return orderEncourageBean;
    }

    @Override
    public OrderEncourageBean getPcEncouragePopou(Long userId) {
        Integer activityId = tytConfigService.getIntValue("newpc_encourage_activity_id");
        if (activityId==null){
            return null;
        }
        OrderEncourageBean bean = marketingActivityService.getPcEncouragePopou(userId,activityId);
        if (bean==null){
            return null;
        }
        List<TytOrderEncourageRule> ruleList = getByActivityId(activityId.longValue());
        bean.setRuleList(ruleList);
        return bean;
    }

    /**
     * 获取支付单数
     * @param userId
     * @param activityId
     * @return
     */
    private int getPayNum(Long userId, Long activityId) {
        StringBuffer sql = new StringBuffer("SELECT COUNT(*) FROM `tyt_stimulate_activity` WHERE user_id=? AND marketing_activity_id=?");
        BigInteger count = this.getBaseDao().query(sql.toString(), new Object[] { userId, activityId });
        if (count!=null){
            return count.intValue();
        }
        return 0;
    }

    private FinishNumAndGiveDaysBean getFinishNumAndGiveDays(Long userId, Long activityId) {
        StringBuffer sql = new StringBuffer("SELECT COUNT(*) finishNum,SUM(donate_days) giveDays,sum(repeal_days) repealDays FROM `tyt_stimulate_activity` WHERE user_id=? AND marketing_activity_id=? and order_status=1");
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("finishNum", Hibernate.INTEGER);
        scalarMap.put("giveDays", Hibernate.INTEGER);
        scalarMap.put("repealDays", Hibernate.INTEGER);
        List<FinishNumAndGiveDaysBean> bean = this.getBaseDao().search(sql.toString(), scalarMap, FinishNumAndGiveDaysBean.class, new Object[]{userId,activityId});
        if (CollectionUtils.isNotEmpty(bean)){
            return bean.get(0);
        }
        return null;
    }

    /**
     * 查询活动规则
     * @param activityId
     * @return
     */
    private List<TytOrderEncourageRule> getByActivityId(Long activityId) {
        StringBuffer sql = new StringBuffer("SELECT * FROM tyt_order_encourage_rule where activity_id ="+activityId);
        return this.getBaseDao().searchByName(sql.toString(),null);
    }

    /**
     * 修改用户参加状态
     * @param userId
     * @param activityId
     */
    private void updateUserStatus(Long userId,Long activityId){
        StringBuffer sql = new StringBuffer("UPDATE `marketing_activity_user` SET is_join=2,mtime=now() WHERE user_id=? AND activity_id=?");
        this.getBaseDao().executeUpdateSql(sql.toString(), new Object[]{userId, activityId});
    }


}
