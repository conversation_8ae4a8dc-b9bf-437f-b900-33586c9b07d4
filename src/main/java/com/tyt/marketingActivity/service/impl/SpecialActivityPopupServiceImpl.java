package com.tyt.marketingActivity.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.tyt.acvitity.service.CsComplaintRecordResultServcie;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.marketingActivity.bean.ActivityEnum;
import com.tyt.marketingActivity.bean.MarketActivityBean;
import com.tyt.marketingActivity.bean.SpecialMarketActivityCheckBean;
import com.tyt.marketingActivity.service.SpecialActivityPopupService;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.MarketingActivity;
import com.tyt.model.User;
import com.tyt.model.UserPermission;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("specialActivityPopupService")
public class SpecialActivityPopupServiceImpl extends BaseServiceImpl<MarketingActivity,Long> implements SpecialActivityPopupService {

    @Resource(name = "marketingActivityDao")
    public void setBaseDao(BaseDao<MarketingActivity, Long> marketingActivityDao) {
        super.setBaseDao(marketingActivityDao);
    }

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;
    @Resource(name = "carService")
    private CarService carService;

    @Autowired
    private CsComplaintRecordResultServcie csComplaintRecordResultServcie;

    @Autowired
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;


    @Override
    public List<MarketActivityBean> getSpecialConditionPopup(Long userId, int port) throws Exception {
        List<MarketActivityBean> activityBeans = new ArrayList<>();
        SpecialMarketActivityCheckBean checkBean = checkUserIsHave3CarTimes(userId, port, ActivityEnum.促活车次卡活动);
        if (checkBean.isHave3Times()) {
            activityBeans.addAll(checkBean.getBeans());
        }
        checkBean = checkUserIsHave3CarTimes(userId, port, ActivityEnum.会员续费活动);
        if (checkBean.isHave3Times()) {
            activityBeans.addAll(checkBean.getBeans());
        }
        checkBean = checkUserIsHave3CarTimes(userId, port, ActivityEnum.非会员缴费活动);
        if (checkBean.isHave3Times()) {
            activityBeans.addAll(checkBean.getBeans());
        }
        checkBean = checkUserIsHave3CarTimes(userId, port, ActivityEnum.非货会员首购活动);
        if (checkBean.isHave3Times()) {
            activityBeans.addAll(checkBean.getBeans());
        }
        return activityBeans;
    }

    public List<MarketActivityBean> getCarUnverifiedPopup(Long userId,int port){
        int carNum = carService.getCarNum(userId);
        if (carNum ==0) {
            Integer activityId = tytConfigService.getIntValue("car_unverified_popup_activity_id");
            if (Objects.nonNull(activityId)) {
                return getIsNeedPopupMarketActivity(activityId.longValue(), port);
            }
        }
        return null;
    }

    @Override
    public List<MarketActivityBean> getCreditLevelPopup(Long userId, int port) {
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(userId);
        String carCreditRankLevel = Objects.isNull(userCreditInfo) || StringUtils.isEmpty(userCreditInfo.getCarCreditRankLevel()) ? "1" : userCreditInfo.getCarCreditRankLevel();
        // 判断30天内是否有严重客诉
        User user = userService.getById(userId);
        int wrongCount = csComplaintRecordResultServcie.getCountByWrongReason(user);
        if (wrongCount > 0) {
            carCreditRankLevel = "0";
        }
        boolean isVip = false;
        // 判断是否拥有会员权益
        List<UserPermission> userPermissionList = userPermissionService.getPermissionListByUserId(userId);
        if (CollUtil.isNotEmpty(userPermissionList)) {
            for (UserPermission userPermission : userPermissionList) {
                if ((UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode().equals(userPermission.getServicePermissionTypeId())) && userPermission.getStatus() == 1) {
                    isVip = true;
                }
            }
        }

        //不是会员不弹飘窗
        if (isVip && Integer.parseInt(carCreditRankLevel) < 3 && Integer.parseInt(carCreditRankLevel) > 0) {
            Integer activityId = tytConfigService.getIntValue("credit_level_popup_activity_id");
            if (Objects.nonNull(activityId)) {
                return getIsNeedPopupMarketActivity(activityId.longValue(), port);
            }
        }
        return null;
    }

    @Override
    public SpecialMarketActivityCheckBean checkUserIsHave3CarTimes(Long userId, int port, ActivityEnum activityEnum) throws Exception{
        SpecialMarketActivityCheckBean checkBean = new SpecialMarketActivityCheckBean();
        checkBean.setHave3Times(false);
        //促活付费转化，赠送3次找货权益弹窗
        if (activityEnum == ActivityEnum.促活车次卡活动){
            Integer activityId = tytConfigService.getIntValue("car_login_activity_id");
            if (activityId != null && activityId>0){
                List<MarketActivityBean> activityBeans = getIsNeedPopupMarketActivity(activityId.longValue(),port);
                if (activityBeans == null){
                    return checkBean;
                }
                User user = userService.getByUserId(userId);
                if (TimeUtil.daysBetween(user.getCtime(),new Date())>3  && user.getVerifyPhotoSign()==1){
                    UserPermission permission =userPermissionService.getUserPermission(userId,"100101");
                    if (permission != null && !(permission.getStartTime().after(TimeUtil.parseDateString("2023-03-25 00:00:00")) && permission.getEndTime().before(TimeUtil.parseDateString("2023-04-02 00:00:00")))) {
                        return checkBean;
                    }
                    checkBean.setIsCar(user.getIsCar());
                    if ("1".equals(user.getIsCar())){
                        checkBean.setHave3Times(true);
                        checkBean.setBeans(activityBeans);
                        return checkBean;
                    }
                }
            }
        }
        //会员续费活动
        if (activityEnum == ActivityEnum.会员续费活动){
            String activityIds = null;
            String typeId = "";
            if (port == 1){
                activityIds = tytConfigService.getStringValue("car_vip_renewal_activity_id");
                typeId = UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode();
            }
            if (port ==2){
                activityIds = tytConfigService.getStringValue("goods_vip_renewal_activity_id");
                typeId = UserPermission.PermissionTypeEnum.GOODS_MEMBER.getCode();
            }
            if (StringUtils.isNotBlank(activityIds)){
                List<MarketActivityBean> activityBeans = getIsNeedPopupMarketActivity(activityIds,port,userId);
                if (activityBeans == null){
                    return checkBean;
                }
                if (StringUtils.isNotBlank(typeId)){
                    UserPermission userPermission = userPermissionService.getUserPermission(userId, typeId);
                    if (userPermission!=null && userPermission.getStatus()==1){
                        return checkBean;
                    }
                }
                checkBean.setHave3Times(true);
                checkBean.setBeans(activityBeans);
                return checkBean;
            }
        }
        //非会员续费活动
        if (activityEnum == ActivityEnum.非会员缴费活动){
            Integer activityId = null;
            String typeId = "";
            if (port == 1){
                activityId = tytConfigService.getIntValue("car_no_vip_activity_id");
                typeId = UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode();
            }
            if (port ==2){
                activityId = tytConfigService.getIntValue("goods_no_vip_activity_id");
                typeId = UserPermission.PermissionTypeEnum.GOODS_MEMBER.getCode();
            }
            if (activityId!=null && activityId>0){
                List<MarketActivityBean> activityBeans = getIsNeedPopupMarketActivity(activityId.toString(),port,userId);
                if (activityBeans == null){
                    return checkBean;
                }
                if (StringUtils.isNotBlank(typeId)){
                    UserPermission userPermission = userPermissionService.getUserPermission(userId, typeId);
                    if (userPermission!=null && userPermission.getStatus()==1){
                        return checkBean;
                    }
                }
                checkBean.setHave3Times(true);
                checkBean.setBeans(activityBeans);
                return checkBean;
            }
        }
        if (activityEnum == ActivityEnum.非货会员首购活动 && port == 2){
            Integer activityId = tytConfigService.getIntValue("goods_shougou_activityId");
            String typeId = UserPermission.PermissionTypeEnum.GOODS_MEMBER.getCode();
            if (activityId!=null && activityId>0){
                List<MarketActivityBean> activityBeans = getIsNeedPopupMarketActivity(activityId.longValue(),port);
                if (activityBeans == null){
                    return checkBean;
                }
                User user = userService.getByUserId(userId);
                if (TimeUtil.daysBetween(user.getCtime(),new Date())>=1){
                    if (StringUtils.isNotBlank(typeId)){
                        UserPermission userPermission = userPermissionService.getUserPermission(userId, typeId);
                        if (userPermission!=null){
                            return checkBean;
                        }
                    }
                    checkBean.setHave3Times(true);
                    checkBean.setBeans(activityBeans);
                }
            }
        }
        return checkBean;
    }

    private List<MarketActivityBean> getIsNeedPopupMarketActivity(Long activityId,int port){
        String sql = "SELECT a.id id,a.popup_id_one popupIdOne,a.popup_id_two popupIdTwo FROM `marketing_activity` a WHERE a.id =? AND a.activity_scope=3 AND a.is_need_popup=1 AND a.activity_part=? AND a.status=1 AND a.start_time<NOW() AND a.end_time>NOW()";
        Object[] allParams = new Object[] {activityId,port};
        Map<String, Type> allScalarMap = new HashMap<String, Type>();
        allScalarMap.put("id", Hibernate.LONG);
        allScalarMap.put("popupIdOne", Hibernate.LONG);
        allScalarMap.put("popupIdTwo", Hibernate.LONG);
        List<MarketActivityBean> beans = this.getBaseDao().search(sql, allScalarMap, MarketActivityBean.class, allParams);
        if (beans!=null && beans.size()>0){
            return beans;
        }
        return null;
    }

    private List<MarketActivityBean> getIsNeedPopupMarketActivity(String activityId,int port,Long userId){
        String sql = "SELECT a.id id,a.popup_id_one popupIdOne,a.popup_id_two popupIdTwo FROM marketing_activity_user u LEFT JOIN marketing_activity a ON u.activity_id=a.id WHERE a.id in ("+activityId+") AND u.user_id=? AND u.is_delete=1 AND u.is_join=1 AND a.activity_scope=3 AND a.is_need_popup=1 AND a.activity_part=? AND a.status=1 AND a.start_time<NOW() AND a.end_time>NOW()";
        Object[] allParams = new Object[] {userId,port};
        Map<String, Type> allScalarMap = new HashMap<String, Type>();
        allScalarMap.put("id", Hibernate.LONG);
        allScalarMap.put("popupIdOne", Hibernate.LONG);
        allScalarMap.put("popupIdTwo", Hibernate.LONG);
        List<MarketActivityBean> beans = this.getBaseDao().search(sql, allScalarMap, MarketActivityBean.class, allParams);
        if (beans!=null && beans.size()>0){
            return beans;
        }
        return null;
    }

}
