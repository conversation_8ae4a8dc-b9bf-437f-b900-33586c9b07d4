package com.tyt.marketingActivity.bean;

import com.tyt.model.TytOrderEncourageRule;

import java.util.Date;
import java.util.List;

public class OrderEncourageBean {

    private Integer payNum;
    private Integer finishNum;
    private Integer giveDays;
    private Date startTime;
    private Date endTime;
    List<TytOrderEncourageRule> ruleList;

    public Integer getPayNum() {
        return payNum;
    }

    public void setPayNum(Integer payNum) {
        this.payNum = payNum;
    }

    public Integer getFinishNum() {
        return finishNum;
    }

    public void setFinishNum(Integer finishNum) {
        this.finishNum = finishNum;
    }

    public Integer getGiveDays() {
        return giveDays;
    }

    public void setGiveDays(Integer giveDays) {
        this.giveDays = giveDays;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<TytOrderEncourageRule> getRuleList() {
        return ruleList;
    }

    public void setRuleList(List<TytOrderEncourageRule> ruleList) {
        this.ruleList = ruleList;
    }
}
