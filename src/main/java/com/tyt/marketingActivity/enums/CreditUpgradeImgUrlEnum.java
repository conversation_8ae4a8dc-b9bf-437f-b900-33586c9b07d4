package com.tyt.marketingActivity.enums;



import java.util.Objects;

/**
 * 车方每月等级弹窗地址信息
 * 图片存放在 www.官网服务器。具体地址：/data/root_pic/www/teyuntong/carCreditLeve/
 */
public enum CreditUpgradeImgUrlEnum {
    level_0(0,"http://www.teyuntong.net/carCreditLeve/current_month_v0.png","http://www.teyuntong.net/carCreditLeve/current_month_v0.png"),
    level_1(1,"https://www.teyuntong.net/carCreditLeve/upgrade_v1.png","https://www.teyuntong.net/carCreditLeve/current_month_v1.png"),
    level_2(2,"https://www.teyuntong.net/carCreditLeve/upgrade_v2.png","https://www.teyuntong.net/carCreditLeve/current_month_v2.png"),
    level_3(3,"https://www.teyuntong.net/carCreditLeve/upgrade_v3.png","https://www.teyuntong.net/carCreditLeve/current_month_v3.png"),
    level_4(4,"https://www.teyuntong.net/carCreditLeve/upgrade_v4.png","https://www.teyuntong.net/carCreditLeve/current_month_v4.png"),
    level_5(5,"https://www.teyuntong.net/carCreditLeve/upgrade_v5.png","https://www.teyuntong.net/carCreditLeve/current_month_v5.png");
    /**
     * 车方等级
     */
    private Integer carCreditRankLevel;
    /**
     * 较上月对比 升级图片url
     */
    private String creditUpgradeImgUrl;
    /**
     * 本月等级图片url
     */
    private String creditImgUrl;

    CreditUpgradeImgUrlEnum(Integer carCreditRankLevel, String creditUpgradeImgUrl,String creditImgUrl) {
        this.carCreditRankLevel = carCreditRankLevel;
        this.creditUpgradeImgUrl = creditUpgradeImgUrl;
        this.creditImgUrl = creditImgUrl;
    }

    public Integer getCarCreditRankLevel() {
        return carCreditRankLevel;
    }

    public String getCreditImgUrl() {
        return creditImgUrl;
    }

    public String getCreditUpgradeImgUrl() {
        return creditUpgradeImgUrl;
    }

    /**
     * 根据等级获取对应等级图片url
     * @param carCreditRankLevel 等级
     * @param type 1 获取信用升级图片url 2 获取本月信用图片url
     * @return
     */
    public static String getCreditUpgradeImgUrlByLevel(Integer carCreditRankLevel,Integer type) {
        if (Objects.isNull(carCreditRankLevel) || Objects.isNull(type)) {
            return null;
        }
        for (CreditUpgradeImgUrlEnum creditUpgradeImgUrlEnum : CreditUpgradeImgUrlEnum.values()) {
            if (creditUpgradeImgUrlEnum.getCarCreditRankLevel()== carCreditRankLevel) {
                //获取信用升级图片url
                if (type == 1) {
                    return creditUpgradeImgUrlEnum.getCreditUpgradeImgUrl();
                }
                //获取本月信用图片url
                if (type == 2) {
                    return creditUpgradeImgUrlEnum.getCreditImgUrl();
                }
            }
        }
        return null;
    }
}
