package com.tyt.marketingActivity.controller;

import com.tyt.activate.bean.ActivateCheckBean;
import com.tyt.activate.service.TytActivateConfigService;
import com.tyt.base.controller.BaseController;
import com.tyt.marketingActivity.bean.MarketingActivityPopupBean;
import com.tyt.marketingActivity.bean.OrderEncourageBean;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.marketingActivity.service.OrderEncourageRuleService;
import com.tyt.model.PublicResource;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.ResultMsgBeanV2;
import com.tyt.plat.utils.PlatCommonUtil;
import com.tyt.plat.vo.ad.ActivityAdvertListVo;
import com.tyt.transport.querybean.PopupTemplBean;
import com.tyt.user.service.PublicResourceService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 活动弹窗配置
 */
@Controller
@RequestMapping("/plat/marketing/activity")
public class MarketingActivityController extends BaseController {

    @Resource(name = "marketingActivityService")
    private MarketingActivityService marketingActivityService;
    @Resource(name = "orderEncourageRuleService")
    private OrderEncourageRuleService orderEncourageRuleService;
    @Autowired
    private PublicResourceService publicResourceService;

    @Autowired
    private TytActivateConfigService tytActivateConfigService;

    /**
     * 获取用户活动弹窗信息
     * @param userId 用户id
     * @return rm
     */
    @RequestMapping(value = "/popupList")
    @ResponseBody
    public ResultMsgBean getList(Long userId,String clientSign,String clientVersion) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            if (null == userId || 0 == userId) {
                rm.setData(new ArrayList<MarketingActivityPopupBean>());
                return rm;
            }
            int port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
            List<MarketingActivityPopupBean> popupList = marketingActivityService.getPopupList(userId, port,clientVersion,clientSign);
            logger.info("popupList" + popupList);
            rm.setData(popupList);
        } catch (Exception ex) {
            ex.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("popupList->服务器错误");
        }
        return rm;
    }

    /**
     * 获取用户活动弹窗信息
     * @param userId 用户id
     * @return rm
     */
    @GetMapping(value = "/activityAdvertList")
    @ResponseBody
    public ResultMsgBeanV2<ActivityAdvertListVo> activityAdvertList(Long userId, String clientSign, String clientVersion) {
        try {
            if (null == userId || 0 == userId) {
                return ResultMsgBeanV2.successResponse();
            }
            int port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
            List<MarketingActivityPopupBean> popupList = marketingActivityService.getPopupList(userId, port,clientVersion,clientSign);

            int count = 3;

            List<MarketingActivityPopupBean> newPopupList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(popupList)){
                for(MarketingActivityPopupBean onePupup: popupList){
                    Integer showPosition = onePupup.getShowPosition();
                    if(showPosition != null && onePupup.equals(3)){
                        continue;
                    }

                    newPopupList.add(onePupup);
                    if(newPopupList.size() >= count){
                        break;
                    }
                }
            }
            ActivityAdvertListVo activityAdvertListVo = new ActivityAdvertListVo();
            activityAdvertListVo.setPopupList(newPopupList);
            activityAdvertListVo.setCount(count);

            return ResultMsgBeanV2.successResponse(activityAdvertListVo);
        } catch (Exception ex) {
            PlatCommonUtil.printErrorInfo("activityAdvertList : ", ex);
            return ResultMsgBeanV2.failResponse(ex);
        }
    }

    /**
     * 微信分享货源上报
     * @param userId 用户id
     * @return rm
     */
    @RequestMapping(value = "/shareReporting")
    @ResponseBody
    public ResultMsgBean shareReporting(Long userId,String clientSign) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"操作成功");
        try {
            int port = Constant.isCarOrGoodsOrOrigin(Integer.parseInt(clientSign));
            marketingActivityService.shareReporting(userId,port);
        } catch (Exception ex) {
            logger.error("分享货源上报异常 : ", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping(value = "/shareReportNew")
    @ResponseBody
    public ResultMsgBean shareReportingNew(Long userId) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"操作成功");
        try {
            ActivateCheckBean checkBean = tytActivateConfigService.checkActivate(userId, 2, false);
            if (checkBean.isActivate()){
                marketingActivityService.shareReportNew(userId,checkBean.getConfig().getShareGiveGoodsId());
            }
        } catch (Exception ex) {
            logger.error("分享货源上报异常 : ", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping(value = "/getShareReportNotice")
    @ResponseBody
    public ResultMsgBean getShareReportNotice(Long userId) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"操作成功");
        try {
            PopupTemplBean templBean = marketingActivityService.getShareReportNotice(userId);
            if (Objects.nonNull(templBean)){
                rm.setData(templBean);
                return rm;
            }
        } catch (Exception ex) {
            logger.error("获取分享货源弹窗异常 : ", ex);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 获取激励活动详情
     * @param userId
     * @param activityId
     * @param flag  1.弹窗 2.我的活动
     * @return
     */
    @RequestMapping(value = "/encourage/detail.action")
    @ResponseBody
    public ResultMsgBean encourageDetail(@RequestParam(name="userId") Long userId, @RequestParam(name="activityId")Long activityId, @RequestParam(name="flag")int flag) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"查询成功");
        try {
            OrderEncourageBean bean = orderEncourageRuleService.saveEncourageDetail(userId,activityId,flag);
            rm.setData(bean);
        } catch (Exception ex) {
            ex.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    @RequestMapping(value = "/pc/encourage/detail")
    @ResponseBody
    public ResultMsgBean pdEncourageDetail(@RequestParam(name="userId") Long userId) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK,"查询成功");
        try {
            OrderEncourageBean bean = orderEncourageRuleService.getPcEncouragePopou(userId);
            if (bean != null){
                rm.setData(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }






}
