package com.tyt.carnews.controller;



import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.controller.BaseController;
import com.tyt.carnews.service.BusinessApplyService;
import com.tyt.model.BusinessApply;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/carNews")
public class BusinessApplyController extends BaseController {
	@Resource(name = "businessApplyService")
	BusinessApplyService businessApplyService;
	
	@Resource(name = "userService")
	UserService userService;
	/**
	 * 点击商家入驻接口
	 * 
	 * @param cooperation
	 * @return
	 */
	@RequestMapping("/clickBusiness.action")
	@ResponseBody
	public void clickBusiness(@RequestParam(name="userId",required=true) Long userId, HttpServletRequest request, HttpServletResponse response) {
		logger.info("商家入驻用户 userId: " + userId );
		ResultMsgBean rm = new ResultMsgBean();
		try {
			User user = userService.getById(userId);
			if(user!=null) {
				BusinessApply businessApply= setBusinessApply(user,1,null,null);
				Long id = (Long) businessApplyService.addSave(businessApply);
				Map<String,Object> map = new HashMap<String,Object>();
				map.put("id", id);
				rm.setData(map);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("保存成功!");
				printJSON(request, response, rm);
				return;
				}else {
					rm.setCode(ReturnCodeConstant.ERROR);
					rm.setMsg("该用户不存在");
					printJSON(request, response, rm);
					return;	
				}
			
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("保存失败,请稍后重试!");
			printJSON(request, response, rm);
			return;
		}
	}
	private BusinessApply setBusinessApply(User user,int channelType,String phone1,Integer businessType) {
		BusinessApply businessApply = new BusinessApply();
		businessApply.setChannelType(channelType);
		businessApply.setPhone1(phone1);
		businessApply.setBusinessType(businessType);
		businessApply.setCtime(new Date());
		businessApply.setStatus(0);
		if(user!=null) {
			businessApply.setCellPhone(user.getCellPhone());
			businessApply.setUserId(user.getId());
			businessApply.setLinkman(user.getTrueName());
		}
		return businessApply;
		
		
	}
	/**
	 * 申请入驻接口
	 * 
	 * @param cooperation
	 * @return
	 */
	@RequestMapping("/saveBusiness.action")
	@ResponseBody
	public void saveBusiness( Long userId,@RequestParam(name="phone1",required=true) String phone1, Long id,
			@RequestParam(name="businessType",required=true) Integer businessType,
			HttpServletRequest request, HttpServletResponse response) {
		logger.info("申请入驻用户 userId: " + userId );
		ResultMsgBean rm = new ResultMsgBean();
		try {
			//判断是否已经存在 存在更新 不存在则insert
			if(id==null||id<=0L) {
				BusinessApply businessApply = setBusinessApply(null,2,phone1,businessType);
				id = (Long) businessApplyService.addSave(businessApply);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("保存成功!");
				printJSON(request, response, rm);
				return;
			}else {
				BusinessApply businessApply = businessApplyService.getById(id);
				businessApply.setBusinessType(businessType);
				businessApply.setChannelType(2);
				businessApply.setPhone1(phone1);
				businessApply.setUtime(new Date());
				businessApplyService.update(businessApply);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("保存成功!");
				printJSON(request, response, rm);
				return;
			}
			
			
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("保存失败,请稍后重试!");
			printJSON(request, response, rm);
			return;
		}
	}
}
