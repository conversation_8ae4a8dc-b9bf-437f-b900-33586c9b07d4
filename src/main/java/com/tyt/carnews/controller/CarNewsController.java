package com.tyt.carnews.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.carnews.bean.CarNewsQueryResultBean;
import com.tyt.carnews.bean.CarNewsQueryResultBeanNew;
import com.tyt.carnews.service.CarNewsService;
import com.tyt.common.service.TytBrowseLogService;
import com.tyt.looppicture.service.HpLoopPictureService;
import com.tyt.model.HpLoopPicture;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytBrowseLog;
import com.tyt.model.TytCarNews;
import com.tyt.model.TytSource;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;

@Controller
@RequestMapping("/plat/car/news")
public class CarNewsController extends BaseController {

	@Resource(name = "carNewsService")
	CarNewsService carNewsService;
	
	@Resource(name = "tytBrowseLogService")
	TytBrowseLogService tytBrowseLogService;

	@Resource(name = "hpLoopPictureService")
	HpLoopPictureService hpLoopPictureService;
	
	private static final int hploop_picture_Type=3;
	private static final String head_brand_key="new_car_brand_head";
	private static final String tail_brand_key="new_car_brand_trailer";
	/**
	 * APP新车列表接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter,
			Integer queryType, Long querySign,
			String province,
			String city,
			String county,
			String carTypeCode,
			String brandCode,
			String priceType,	
			String priceValue			
			){
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
	
				CarNewsQueryResultBean carNewsQueryResultBean = carNewsService.query(province,
								 city,county,queryType.intValue(), querySign == null ? 0
										: querySign.longValue(),carTypeCode,
										 brandCode,
										 priceType,	
										 priceValue);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(carNewsQueryResultBean);
				if(1==queryType.intValue()){
					TytBrowseLog tytBrowseLog =getTytBrowseLog( null, baseParameter.getUserId(),
							 1,  1,  null,
							 baseParameter.getClientSign(),  baseParameter.getClientVersion(),
							 province,  city,
							 county, 
							 carTypeCode,
							 brandCode,
							 priceType,	
							 priceValue	
							) ;
					tytBrowseLogService.add(tytBrowseLog);
				}
			}
	
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	private boolean checkQueryParameter(Integer queryType, Long querySign,
			ResultMsgBean rm) {
		if (queryType == null
				|| (queryType.intValue() != 0 && queryType.intValue() != 1 && queryType
						.intValue() != 2)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询类型不正确！");
			return false;
		} else if (queryType.intValue() == 2
				&& (querySign == null || querySign.longValue() < 0)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询标识错误，页数为空！");
			return false;
		}
		return true;
	
	}

	public TytBrowseLog getTytBrowseLog(Long msgId, Long userId,
			Integer moduleType, Integer functionType, String phone,
			String clientSign, String clientVersion,
			String province, String city,
			String county, 
			String carTypeCode,
			String brandCode,
			String priceType,	
			String priceValue	
			) {
		TytBrowseLog tytBrowseLog = new TytBrowseLog();
		//记录日志
		tytBrowseLog.setClientSign(clientSign);
		tytBrowseLog.setClientVersion(clientVersion);
		tytBrowseLog.setCtime(new Date());
		tytBrowseLog.setFunctionType(functionType);
		tytBrowseLog.setModuleType(moduleType);
		tytBrowseLog.setMsgId(msgId);
		tytBrowseLog.setPhone(phone);
		tytBrowseLog.setUserId(userId);
		tytBrowseLog.setT1(province);
		tytBrowseLog.setT2(city);
		tytBrowseLog.setT3(county);
		tytBrowseLog.setT4(carTypeCode);
		tytBrowseLog.setT5(brandCode);
		tytBrowseLog.setT6(priceValue);
		return tytBrowseLog;
	}
	
	/**
	 * 查询点击量和拨打电话数
	 * @param id  car的id
	 * @return
	 */
	@RequestMapping(value="/getClickRate.action")
	@ResponseBody
	public ResultMsgBean clickRate(@RequestParam(name="id",required=true) Long id) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			TytCarNews clickRate = carNewsService.getClickRate(id);
			if (clickRate!=null) {
				rm.setCode(ReturnCodeConstant.OK);
				rm.setData(clickRate);
				rm.setMsg("查询成功");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	
	
	/**
	 * APP新车列表接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getList")
	@ResponseBody
	public ResultMsgBean getList(BaseParameter baseParameter,
			Integer queryType, Long querySign,
			String province,
			String city,
			String county,
			String carTypeCode,
			String brandCode
			){
		ResultMsgBean rm = new ResultMsgBean();

		// 修正android 参数错误问题
        if(Integer.parseInt(baseParameter.getClientVersion())>5940){
		   carTypeCode="1";
        }

		try {
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
	
				CarNewsQueryResultBeanNew carNewsQueryResultBeanNew = carNewsService.queryNew(province,
								 city,county,queryType.intValue(), querySign == null ? 0
										: querySign.longValue(),carTypeCode,
										 brandCode);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(carNewsQueryResultBeanNew);
				if(1==queryType.intValue()){
					TytBrowseLog tytBrowseLog =getTytBrowseLog( null, baseParameter.getUserId(),
							 1,  1,  null,
							 baseParameter.getClientSign(),  baseParameter.getClientVersion(),
							 province,  city,
							 county, 
							 carTypeCode,
							 brandCode,
							 null,	
							 null	
							) ;
					tytBrowseLogService.add(tytBrowseLog);
				}
			}
	
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
	/**
	 * APP资源获取接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getResource")
	@ResponseBody
	public ResultMsgBean getResource(BaseParameter baseParameter){
		ResultMsgBean rm = new ResultMsgBean();
		try {
			Map<String,Object> map = new HashMap<String,Object>();
			//获取车头品牌
			
			List<TytSource> headBrandList = TytSourceUtil.getSourceList(head_brand_key);
			//获取车挂品牌
			List<TytSource> tailBrandList = TytSourceUtil.getSourceList(tail_brand_key);
			//获取轮播图只获取车辆购买的轮播图
			List<HpLoopPicture> hpLoopPictureList = hpLoopPictureService.getListByType(hploop_picture_Type);
			map.put("headBrandResource", headBrandList);
			map.put("tailBrandResource", tailBrandList);
			map.put("hpLoopPicture", hpLoopPictureList);
			rm.setData(map);
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
