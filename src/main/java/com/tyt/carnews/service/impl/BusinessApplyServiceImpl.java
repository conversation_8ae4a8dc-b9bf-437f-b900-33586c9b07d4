package com.tyt.carnews.service.impl;



import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.carnews.service.BusinessApplyService;
import com.tyt.model.BusinessApply;

@Service("businessApplyService")
public class BusinessApplyServiceImpl extends BaseServiceImpl<BusinessApply, Long>
implements BusinessApplyService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Resource(name = "businessApplyDao")
	public void setBaseDao(BaseDao<BusinessApply, Long> businessApplyDao) {
		super.setBaseDao(businessApplyDao);
	}
}
