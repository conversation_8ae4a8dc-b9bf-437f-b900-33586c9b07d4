package com.tyt.carnews.service.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.carnews.bean.CarNewsQueryBean;
import com.tyt.carnews.bean.CarNewsQueryBeanNew;
import com.tyt.carnews.bean.CarNewsQueryResultBean;
import com.tyt.carnews.bean.CarNewsQueryResultBeanNew;
import com.tyt.carnews.service.CarNewsService;
import com.tyt.common.bean.NewCarQueue;
import com.tyt.config.util.AppConfig;
import com.tyt.model.TytCarNews;
import com.tyt.user.service.UserService;

@Service("carNewsService")
public class CarNewsServiceImpl extends BaseServiceImpl<TytCarNews, Long>
		implements CarNewsService {
	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
	@Resource(name = "userService")
	UserService userService;

	@Resource(name = "carNewsDao")
	public void setBaseDao(BaseDao<TytCarNews, Long> carNewsDao) {
		super.setBaseDao(carNewsDao);
	}

	public CarNewsQueryResultBean query(
			String province, String city,
			String county, int queryType, long querySign,
			String carTypeCode,
			String brandCode,
			String priceType,	
			String priceValue
			){
		CarNewsQueryResultBean carNewsQueryResultBean=new CarNewsQueryResultBean();
		
		List<CarNewsQueryBean> returnList = null;
		
		// 上滑动下滑动最大结果集大小
		int searchSize =AppConfig
				.getIntProperty("tyt.car.news.query.page.size");

		List<Object> list = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer("  WHERE m.status=?  ");
		
		StringBuffer count = new StringBuffer("SELECT count(*)  FROM tyt_car_news m ");
		StringBuffer orderBy= new StringBuffer(" ");
		list.add(2);
		if(null!=province&&!"".equals(province.trim())){
			sb.append(" and m.province=? ");
			list.add(province.trim());
		}
		if(null!=city&&!"".equals(city.trim())&&city.indexOf(province)==-1){
			sb.append(" and m.city=? ");
			list.add(city.trim());
		}
		if(null!=county&&!"".equals(county.trim()) && !county.equals(city)){
			sb.append(" and m.county=? ");
			list.add(county.trim());
		}
		
		if(null!=carTypeCode&&!"".equals(carTypeCode.trim())){
			sb.append(" and m.car_type_code=? ");
			list.add(carTypeCode.trim());
		}
		if(null!=brandCode&&!"".equals(brandCode.trim())){
			StringBuffer inSql=new StringBuffer(" SELECT DISTINCT car_news_id FROM car_news_brand b WHERE  ");
			if(null!=carTypeCode&&!"".equals(carTypeCode.trim())){
				inSql.append(" b.car_type_code=? AND ");
				list.add(carTypeCode.trim());
			}			
			sb.append(" and m.id in(  ");			
			inSql.append(" b.brand_code=? " );
			list.add(brandCode);
			sb.append(inSql+" )");
			
		}
		if(priceType!=null&&!"".equals(priceType.trim())&&("1".equals(priceType)||"2".equals(priceType))){
			if(priceValue!=null &&!"".equals(priceValue.trim())){
				sb.append(" and  m.price<>? ");
				list.add(0);				
				String [] priceValues=priceValue.split("-");
				if("2".equals(priceType)){
					sb.append(" and  m.price>=? ");
					list.add(priceValues[0]);
					sb.append(" and m.price<?   ");
					list.add(priceValues[1]);
				}else{
					if(priceValues.length<2){
						sb.append(" and m.price<=?  ");
						list.add(priceValues[0]);
					}else{
						int s=Integer.parseInt(priceValues[0]);
						int e=Integer.parseInt(priceValues[1]);
						if(s>e){
							int t=s;
							s=e;
							e=t;
						}
						if(s==e){
							sb.append(" and m.price=?  ");
							list.add(s);
						}else{
							sb.append(" and m.price>=? ");
							list.add(s);
							sb.append(" and m.price<? ");
							list.add(e);
						}
					}
				}
				orderBy.append(" order by m.price asc, m.push_level asc ");
			}
		}
		else{
			orderBy.append(" order by m.push_level asc ");
		}
		count.append(sb);
		BigInteger total=this.getBaseDao().query(count.toString(), list.toArray());
		if(total==null||total.intValue()<1){
			carNewsQueryResultBean.setMaxPage(0);
			carNewsQueryResultBean.setPageSize(searchSize);
			carNewsQueryResultBean.setTotalRecord(0);
		}else{
			int totalRecord=total.intValue();
			int pageNumber=(totalRecord+searchSize-1)/searchSize;
			carNewsQueryResultBean.setMaxPage(pageNumber);
			carNewsQueryResultBean.setPageSize(searchSize);
			carNewsQueryResultBean.setTotalRecord(totalRecord);
			// 查询数据集
			long t3=0,t4=0;
			t3=System.currentTimeMillis();
			StringBuffer select = new StringBuffer("SELECT factory_phone1 factoryPhone1,factory_phone2 factoryPhone2,id,utime,abstracts,province,city,county,price,is_special isSpecial,special_url specialUrl,picture_url pictureUrl FROM tyt_car_news  m "
					+ "   ");
			 select.append(sb);
			 select.append(orderBy);
			 Map<String, org.hibernate.type.Type> scalarMap =new HashMap<String,org.hibernate.type.Type>();
			 scalarMap.put("id", Hibernate.LONG);
			 scalarMap.put("abstracts", Hibernate.STRING);
			 scalarMap.put("province", Hibernate.STRING);
			 scalarMap.put("city", Hibernate.STRING);
			 scalarMap.put("county", Hibernate.STRING);		 
			 scalarMap.put("utime", Hibernate.TIMESTAMP);
			 
			 scalarMap.put("price", Hibernate.DOUBLE);
			 scalarMap.put("isSpecial", Hibernate.INTEGER);
			 scalarMap.put("specialUrl", Hibernate.STRING);
			 scalarMap.put("pictureUrl", Hibernate.STRING);
			 scalarMap.put("factoryPhone1", Hibernate.STRING);
			 scalarMap.put("factoryPhone2", Hibernate.STRING);
			 if(queryType==1){
				 querySign=1;
			 }
			 
			 returnList= this.getBaseDao().search(select.toString(), scalarMap, CarNewsQueryBean.class, list.toArray(), Long.valueOf(querySign).intValue(), searchSize);
				
	         t4=System.currentTimeMillis();
	         logger.info("数据库查询时间："+(t4-t3)+"ms");
			 carNewsQueryResultBean.setData(returnList);		
		}
		 return carNewsQueryResultBean;
	}

	public CarNewsQueryResultBeanNew queryNew(
			String province, String city,
			String county, int queryType, long querySign,
			String carTypeCode,
			String brandCode
			){
		CarNewsQueryResultBeanNew carNewsQueryResultBeanNew=new CarNewsQueryResultBeanNew();
		
		List<CarNewsQueryBeanNew> returnList = null;
		
		// 上滑动下滑动最大结果集大小
		int searchSize =AppConfig
				.getIntProperty("tyt.car.news.query.page.size");

		List<Object> list = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer("  WHERE m.status=?  ");

		StringBuffer select = new StringBuffer("SELECT factory_name factoryName,brand_code brandCode ,brand,factory_phone1 factoryPhone1,factory_phone2 factoryPhone2,id,utime,abstracts,province,city,county,is_special isSpecial,special_url specialUrl,sell_range sellRange,business_type businessType,click_amount clickAmount,call_amount callAmount,display_style displayStyle,picture_new_url1 pictureNewUrl1,picture_new_url2 pictureNewUrl2,picture_new_url3 pictureNewUrl3 FROM tyt_car_news  m "
				+ "   ");
		StringBuffer count = new StringBuffer("SELECT count(*)  FROM tyt_car_news m ");
		StringBuffer orderBy= new StringBuffer(" ");
		list.add(2);
		if(null!=province&&!"".equals(province.trim())){
			sb.append(" and m.province=? ");
			list.add(province.trim());
		}
		if(null!=city&&!"".equals(city.trim())&&city.indexOf(province)==-1){
			sb.append(" and m.city=? ");
			list.add(city.trim());
		}
		if(null!=county&&!"".equals(county.trim()) && !county.equals(city)){
			sb.append(" and m.county=? ");
			list.add(county.trim());
		}
		
		if(null!=carTypeCode&&!"".equals(carTypeCode.trim())){
			sb.append(" and m.car_type_code=? ");
			list.add(carTypeCode.trim());
		}
		if(null!=brandCode&&!"".equals(brandCode.trim())){
			StringBuffer inSql=new StringBuffer(" SELECT DISTINCT car_news_id FROM car_news_brand b WHERE b.car_type_code=? AND (");
			sb.append(" and m.id in(  ");
			String []brandCodes=brandCode.split(",");
			list.add(carTypeCode.trim());
			for(int i=0;i<brandCodes.length;i++) {
				if(i!=0) {
					inSql.append(" or " );
				}
				inSql.append(" b.brand_code=? " );
				list.add(brandCodes[i]);
			}
			inSql.append(")");
			sb.append(inSql+")");
		}
		orderBy.append(" order by m.push_level asc ");
		
		//count.append(sb);
		BigInteger total=this.getBaseDao().query(count.toString()+sb.toString(), list.toArray());
		//没有数据查推荐
		if(total==null||total.intValue()<1){
			carNewsQueryResultBeanNew.setMaxPage(0);
			carNewsQueryResultBeanNew.setPageSize(searchSize);
			carNewsQueryResultBeanNew.setTotalRecord(0);
			
			List<Object> tjlist = new ArrayList<Object>();
			StringBuffer tjsb = new StringBuffer("  WHERE m.status=?  ");
			tjlist.add(2);
			boolean serachProvince=false;
			//区为空或是和市相同查市，否则查省
			if(null!=county&&!"".equals(county.trim()) && !county.equals(city)){
			
				tjsb.append(" and m.city=? ");
				tjlist.add(city.trim());
				if(null!=carTypeCode&&!"".equals(carTypeCode.trim())){
					tjsb.append(" and m.car_type_code=? ");
					tjlist.add(carTypeCode.trim());
				}
				BigInteger tjTotal=this.getBaseDao().query(count.toString()+tjsb.toString(), tjlist.toArray());
				if(tjTotal==null||tjTotal.intValue()<1){
					serachProvince=true;
				}else {
					select.append(tjsb);
					select.append(orderBy);
					 Map<String, org.hibernate.type.Type> scalarMap =getScalarMap();
					 returnList= this.getBaseDao().search(select.toString(), scalarMap, CarNewsQueryBeanNew.class, tjlist.toArray(), 1, searchSize);
					 carNewsQueryResultBeanNew.setData(returnList);	
					 carNewsQueryResultBeanNew.setResultType(2);
				}
				
			}else {
				serachProvince=true;
			}
			//查省	
			if(serachProvince) {
				tjlist = new ArrayList<Object>();
				tjsb = new StringBuffer("  WHERE m.status=?  ");
				tjlist.add(2);
				
				if(null!=city&&!"".equals(city.trim())&&city.indexOf(province)==-1){
					tjsb.append(" and m.province=? ");
					tjlist.add(province.trim());
					if(null!=carTypeCode&&!"".equals(carTypeCode.trim())){
						tjsb.append(" and m.car_type_code=? ");
						tjlist.add(carTypeCode.trim());
					}
					BigInteger tjTotal=this.getBaseDao().query(count.toString()+tjsb.toString(), tjlist.toArray());
					if(tjTotal==null||tjTotal.intValue()<1){
						carNewsQueryResultBeanNew.setMaxPage(0);
						carNewsQueryResultBeanNew.setPageSize(searchSize);
						carNewsQueryResultBeanNew.setTotalRecord(0);
						carNewsQueryResultBeanNew.setResultType(0);
					}else {
						select.append(tjsb);
						select.append(orderBy);
						 Map<String, org.hibernate.type.Type> scalarMap =getScalarMap();
						 returnList= this.getBaseDao().search(select.toString(), scalarMap, CarNewsQueryBeanNew.class, tjlist.toArray(), 1, searchSize);
						 carNewsQueryResultBeanNew.setData(returnList);	
						 carNewsQueryResultBeanNew.setResultType(2);
					}
					
				}else {
					carNewsQueryResultBeanNew.setMaxPage(0);
					carNewsQueryResultBeanNew.setPageSize(searchSize);
					carNewsQueryResultBeanNew.setTotalRecord(0);
					carNewsQueryResultBeanNew.setResultType(0);
				}
			}
			
		}else{//查询条件有数据按条件查询
			int totalRecord=total.intValue();
			int pageNumber=(totalRecord+searchSize-1)/searchSize;
			carNewsQueryResultBeanNew.setMaxPage(pageNumber);
			carNewsQueryResultBeanNew.setPageSize(searchSize);
			carNewsQueryResultBeanNew.setTotalRecord(totalRecord);
			// 查询数据集
			long t3=0,t4=0;
			t3=System.currentTimeMillis();
			 select.append(sb);
			 select.append(orderBy);
			 Map<String, org.hibernate.type.Type>  scalarMap=getScalarMap();
			 if(queryType==1){
				 querySign=1;
			 }
			 returnList= this.getBaseDao().search(select.toString(), scalarMap, CarNewsQueryBeanNew.class, list.toArray(), Long.valueOf(querySign).intValue(), searchSize);
				
	         t4=System.currentTimeMillis();
	         logger.info("数据库查询时间："+(t4-t3)+"ms");
			 carNewsQueryResultBeanNew.setData(returnList);	
			 carNewsQueryResultBeanNew.setResultType(1);
		}
		 return carNewsQueryResultBeanNew;
	}
	
	public Map<String, org.hibernate.type.Type>  getScalarMap(){
		 Map<String, org.hibernate.type.Type> scalarMap =new HashMap<String,org.hibernate.type.Type>();
		 scalarMap.put("id", Hibernate.LONG);
		 scalarMap.put("abstracts", Hibernate.STRING);
		 scalarMap.put("province", Hibernate.STRING);
		 scalarMap.put("city", Hibernate.STRING);
		 scalarMap.put("county", Hibernate.STRING);		 
		 scalarMap.put("utime", Hibernate.TIMESTAMP);
		 
		 scalarMap.put("isSpecial", Hibernate.INTEGER);
		 scalarMap.put("specialUrl", Hibernate.STRING);
		
		 scalarMap.put("factoryPhone1", Hibernate.STRING);
		 scalarMap.put("factoryPhone2", Hibernate.STRING);
		 
		 scalarMap.put("factoryName", Hibernate.STRING);
		 scalarMap.put("brandCode", Hibernate.STRING);
		 scalarMap.put("brand", Hibernate.STRING);
		 
		 scalarMap.put("sellRange", Hibernate.INTEGER);
		 scalarMap.put("businessType", Hibernate.INTEGER);
		 scalarMap.put("clickAmount", Hibernate.LONG);
		 scalarMap.put("callAmount", Hibernate.LONG);
		 scalarMap.put("displayStyle", Hibernate.INTEGER);
		 scalarMap.put("pictureNewUrl1", Hibernate.STRING);
		 scalarMap.put("pictureNewUrl2", Hibernate.STRING);
		 scalarMap.put("pictureNewUrl3", Hibernate.STRING);
		 return scalarMap;
	}
	
	@Override
	public TytCarNews getClickRate(Long id) {
		 String sql = "SELECT `id` id,`click_amount` clickAmount,`call_amount` callAmount FROM `tyt_car_news` WHERE id=?";
		 Map<String, org.hibernate.type.Type> map=new HashMap<String, org.hibernate.type.Type>();
		 map.put("id", Hibernate.LONG);
		 map.put("clickAmount", Hibernate.LONG);
		 map.put("callAmount", Hibernate.LONG);
		 List<TytCarNews> search = this.getBaseDao().search(sql, map,TytCarNews.class, new Object[]{id});
		 if (search!=null && search.size()>0) {
			return search.get(0);
		 }
		 return null;
	}

	@Override
	public void updateClickAndCallNum(NewCarQueue bean) {
		// type  2:详情点击    3 ：拨打电话
		String sql="";
		if(bean.getId()!=null && bean.getId().intValue()>0) {
			if(bean.getType()==2) {
				sql="UPDATE tyt_car_news SET click_amount=click_amount+1 WHERE id=? ";
			}else if(bean.getType()==3) {
				sql="UPDATE tyt_car_news SET call_amount=call_amount+1 WHERE id=? ";
			}
		}
		this.executeUpdateSql(sql, new Object[]{bean.getId()});
		
	}
}
