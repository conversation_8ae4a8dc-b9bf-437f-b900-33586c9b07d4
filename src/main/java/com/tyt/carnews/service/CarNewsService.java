package com.tyt.carnews.service;

import com.tyt.base.service.BaseService;
import com.tyt.carnews.bean.CarNewsQueryResultBean;
import com.tyt.carnews.bean.CarNewsQueryResultBeanNew;
import com.tyt.common.bean.NewCarQueue;
import com.tyt.model.TytCarNews;

public interface CarNewsService extends BaseService<TytCarNews, Long> {
	public CarNewsQueryResultBean query(
			String province, String city,
			String county, int queryType, long querySign,
			String carTypeCode,
			String brandCode,
			String priceType,	
			String priceValue					
			);

	TytCarNews getClickRate(Long id);
	
	public CarNewsQueryResultBeanNew queryNew(
			String province, String city,
			String county, int queryType, long querySign,
			String carTypeCode,
			String brandCode
			);

	/**
	 * 更新点击记录和拨打记录
	 * @param bean
	 */
	public void updateClickAndCallNum(NewCarQueue bean);
}
