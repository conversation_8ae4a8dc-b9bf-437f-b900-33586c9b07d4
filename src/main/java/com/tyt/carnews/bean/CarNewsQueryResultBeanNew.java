package com.tyt.carnews.bean;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)

public class CarNewsQueryResultBeanNew implements java.io.Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 2955947558371907498L;
	int totalRecord;
	int pageSize;
	int maxPage;
	int resultType=0;
	List<CarNewsQueryBeanNew> data;
	
	public int getResultType() {
		return resultType;
	}
	public void setResultType(int resultType) {
		this.resultType = resultType;
	}

	public int getTotalRecord() {
		return totalRecord;
	}
	public void setTotalRecord(int totalRecord) {
		this.totalRecord = totalRecord;
	}
	public int getPageSize() {
		return pageSize;
	}
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	public int getMaxPage() {
		return maxPage;
	}
	public void setMaxPage(int maxPage) {
		this.maxPage = maxPage;
	}
	public List<CarNewsQueryBeanNew> getData() {
		return data;
	}
	public void setData(List<CarNewsQueryBeanNew> data) {
		this.data = data;
	}
	
}
