package com.tyt.carnews.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarNewsQueryBean implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5599170577684850964L;
	private Long id;
	private String abstracts;

	private String province;
	private String city;
	private String county;
	private Double price;
	private Integer isSpecial;
	private String specialUrl;
	private String pictureUrl;
	private Date utime;
	private String factoryPhone1;
	private String factoryPhone2;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAbstracts() {
		return abstracts;
	}
	public void setAbstracts(String abstracts) {
		this.abstracts = abstracts;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCounty() {
		return county;
	}
	public void setCounty(String county) {
		this.county = county;
	}
	public Double getPrice() {
		return price;
	}
	public void setPrice(Double price) {
		this.price = price;
	}
	public Integer getIsSpecial() {
		return isSpecial;
	}
	public void setIsSpecial(Integer isSpecial) {
		this.isSpecial = isSpecial;
	}
	public String getSpecialUrl() {
		return specialUrl;
	}
	public void setSpecialUrl(String specialUrl) {
		this.specialUrl = specialUrl;
	}
	public String getPictureUrl() {
		return pictureUrl;
	}
	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}
	public Date getUtime() {
		return utime;
	}
	public void setUtime(Date utime) {
		this.utime = utime;
	}
	public String getFactoryPhone1() {
		return factoryPhone1;
	}
	public void setFactoryPhone1(String factoryPhone1) {
		this.factoryPhone1 = factoryPhone1;
	}
	public String getFactoryPhone2() {
		return factoryPhone2;
	}
	public void setFactoryPhone2(String factoryPhone2) {
		this.factoryPhone2 = factoryPhone2;
	}
	

}