package com.tyt.carnews.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarNewsQueryBeanNew implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5599170577684850964L;
	private Long id;
	private String abstracts;

	private String province;
	private String city;
	private String county;
	//private Double price;
	private Integer isSpecial;
	private String specialUrl;
	//private String pictureUrl;
	private Date utime;
	
	private String factoryPhone1;
	private String factoryPhone2;
	
	private String factoryName;
	private String brandCode;
	private String brand;
	private Integer sellRange;
	private Integer businessType;
	private Long clickAmount=0l;
	private Long callAmount=0l;
	private Integer displayStyle;
	private String pictureNewUrl1;
	private String pictureNewUrl2;
	private String pictureNewUrl3;
	
	
	public Integer getSellRange() {
		return sellRange;
	}
	public void setSellRange(Integer sellRange) {
		this.sellRange = sellRange;
	}
	public Integer getBusinessType() {
		return businessType;
	}
	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}
	public Long getClickAmount() {
		return clickAmount;
	}
	public void setClickAmount(Long clickAmount) {
		this.clickAmount = clickAmount;
	}
	public Long getCallAmount() {
		return callAmount;
	}
	public void setCallAmount(Long callAmount) {
		this.callAmount = callAmount;
	}
	public Integer getDisplayStyle() {
		return displayStyle;
	}
	public void setDisplayStyle(Integer displayStyle) {
		this.displayStyle = displayStyle;
	}
	public String getPictureNewUrl1() {
		return pictureNewUrl1;
	}
	public void setPictureNewUrl1(String pictureNewUrl1) {
		this.pictureNewUrl1 = pictureNewUrl1;
	}
	public String getPictureNewUrl2() {
		return pictureNewUrl2;
	}
	public void setPictureNewUrl2(String pictureNewUrl2) {
		this.pictureNewUrl2 = pictureNewUrl2;
	}
	public String getPictureNewUrl3() {
		return pictureNewUrl3;
	}
	public void setPictureNewUrl3(String pictureNewUrl3) {
		this.pictureNewUrl3 = pictureNewUrl3;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAbstracts() {
		return abstracts;
	}
	public void setAbstracts(String abstracts) {
		this.abstracts = abstracts;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCounty() {
		return county;
	}
	public void setCounty(String county) {
		this.county = county;
	}
	
	public Integer getIsSpecial() {
		return isSpecial;
	}
	public void setIsSpecial(Integer isSpecial) {
		this.isSpecial = isSpecial;
	}
	public String getSpecialUrl() {
		return specialUrl;
	}
	public void setSpecialUrl(String specialUrl) {
		this.specialUrl = specialUrl;
	}

	public Date getUtime() {
		return utime;
	}
	public void setUtime(Date utime) {
		this.utime = utime;
	}
	public String getFactoryPhone1() {
		return factoryPhone1;
	}
	public void setFactoryPhone1(String factoryPhone1) {
		this.factoryPhone1 = factoryPhone1;
	}
	public String getFactoryPhone2() {
		return factoryPhone2;
	}
	public void setFactoryPhone2(String factoryPhone2) {
		this.factoryPhone2 = factoryPhone2;
	}
	public String getFactoryName() {
		return factoryName;
	}
	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}
	public String getBrandCode() {
		return brandCode;
	}
	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}
	public String getBrand() {
		return brand;
	}
	public void setBrand(String brand) {
		this.brand = brand;
	}
	

}