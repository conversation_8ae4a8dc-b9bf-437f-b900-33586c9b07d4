package com.tyt.carnews.bean;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)

public class CarNewsQueryResultBean implements java.io.Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -2602069019730372351L;
	int totalRecord;
	int pageSize;
	int maxPage;
	List<CarNewsQueryBean> data;
	public int getTotalRecord() {
		return totalRecord;
	}
	public void setTotalRecord(int totalRecord) {
		this.totalRecord = totalRecord;
	}
	public int getPageSize() {
		return pageSize;
	}
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	public int getMaxPage() {
		return maxPage;
	}
	public void setMaxPage(int maxPage) {
		this.maxPage = maxPage;
	}
	public List<CarNewsQueryBean> getData() {
		return data;
	}
	public void setData(List<CarNewsQueryBean> data) {
		this.data = data;
	}
	
}
