package com.tyt.carnews.util;

import java.util.concurrent.ConcurrentLinkedQueue;

import com.tyt.carnews.service.CarNewsService;
import com.tyt.common.bean.NewCarQueue;
import com.tyt.util.ApplicationContextUtils;

public class CarNewUtil {

	public static ConcurrentLinkedQueue<NewCarQueue> carNewQueue = new ConcurrentLinkedQueue<NewCarQueue>();
	public static boolean carNewQueueStart = false;
	
	/**
	 * 新车咨询点击量队列消费
	 */
	static {
		if (!carNewQueueStart) {
			Thread newCarQueueThread = new Thread() {
				@Override
				public void run() {
					System.out.println(System.currentTimeMillis() + "__" + this.getName() + " start......");
					
					while (true) {
						try {
							if(carNewQueue.isEmpty()) {
								
							}else {
								NewCarQueue bean=carNewQueue.poll();
								if(bean!=null) {
									CarNewUtil.updateClickAndCallNum(bean);
						        }
							}
							 this.sleep(10);
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
			};
			newCarQueueThread.setName("newCarQueueThread");
			newCarQueueThread.start();
			carNewQueueStart = true;
		}
	}
	
	private static void updateClickAndCallNum(NewCarQueue bean) {
		CarNewsService carNewsService = ApplicationContextUtils.getBean("carNewsService");
		carNewsService.updateClickAndCallNum(bean);
	}
}
