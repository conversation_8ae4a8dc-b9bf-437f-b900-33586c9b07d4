package com.tyt.transportnews.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.transportnews.bean.HpTransportNewsBean;
import com.tyt.transportnews.service.HpTransportNewsService;
import com.tyt.util.ReturnCodeConstant;

/**
 * 
 * <AUTHOR>
 * @date 2017年10月11日上午11:27:37
 * @description
 */
@Controller
@RequestMapping("/plat/transportnews")
public class TransportnewsController extends BaseController {
	@Resource(name = "hpTransportNewsService")
	private HpTransportNewsService transportNewsService;

	/**
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			List<HpTransportNewsBean> list=transportNewsService.getHpTransportNewsBeanList();
			rm.setData(list);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
