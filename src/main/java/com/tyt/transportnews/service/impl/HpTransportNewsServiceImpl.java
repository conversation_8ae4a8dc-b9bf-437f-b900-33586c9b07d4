package com.tyt.transportnews.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.HpTransportNews;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transportnews.bean.HpTransportNewsBean;
import com.tyt.transportnews.service.HpTransportNewsService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.TimeUtil;

@Service(value = "hpTransportNewsService")
public class HpTransportNewsServiceImpl extends BaseServiceImpl<HpTransportNews, Long> implements HpTransportNewsService {
	@Resource(name = "hpTransportNewsDao")
	public void setBaseDao(BaseDao<HpTransportNews, Long> hpTransportNewsDao) {
		super.setBaseDao(hpTransportNewsDao);
	}
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;

	private static final int activeTime = 24*60*60;
	
	public List<HpTransportNewsBean> getHpTransportNewsBeanList(){
		List<HpTransportNewsBean> list=null;
		
		String value = tytConfigService.getStringValue("appHomePageTransportNewsListCacheKey", "transport_news_list_cache_key_{date}");
		String key = StringUtils.replaceEach(value, new String[]{"{date}"},
				new String[]{TimeUtil.formatDate_(new Date())});
		RedisUtil.del(key);
		Object obj= RedisUtil.getObject(key) ;
		if(obj==null){
			int pageSize  = tytConfigService.getIntValue("appHomePageTransportNewsListSize", 3);
			
			String hql="from HpTransportNews where status= ? and openClose=? order by publishTime desc ";
			List<HpTransportNews> li=this.getBaseDao().searchByHql(hql, new Object[]{(short)0,(short)1}, 1, pageSize);
			if(li!=null&&li.size()>0){
				list=new ArrayList<HpTransportNewsBean>();
				HpTransportNewsBean hpTransportNewsBean=null;
				for(HpTransportNews hpTransportNews:li){
					hpTransportNewsBean=new HpTransportNewsBean();
					BeanUtils.copyProperties(hpTransportNews, hpTransportNewsBean);
					list.add(hpTransportNewsBean);
				}
				RedisUtil.setObject(key, list, activeTime);
			}
		}else{
			list=(List<HpTransportNewsBean>)obj;
		}
		return list;
	}
}
