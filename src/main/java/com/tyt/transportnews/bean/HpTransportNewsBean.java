package com.tyt.transportnews.bean;

import java.util.Date;

public class HpTransportNewsBean implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2012806291505394186L;
	
	private Long id;
	private String source;
	private String newsTitle;
	private String pictureUrl;
	private String openUrl;
	private Date publishTime;
	
	public Long getId() {
		return id;
	}
	public String getSource() {
		return source;
	}
	public String getNewsTitle() {
		return newsTitle;
	}
	public String getPictureUrl() {
		return pictureUrl;
	}
	public String getOpenUrl() {
		return openUrl;
	}
	public Date getPublishTime() {
		return publishTime;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public void setNewsTitle(String newsTitle) {
		this.newsTitle = newsTitle;
	}
	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}
	public void setOpenUrl(String openUrl) {
		this.openUrl = openUrl;
	}
	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}
	

}
