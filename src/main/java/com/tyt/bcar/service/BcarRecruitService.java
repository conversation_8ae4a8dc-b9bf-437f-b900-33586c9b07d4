package com.tyt.bcar.service;

import java.util.List;
import java.util.Map;

import com.tyt.base.service.BaseService;
import com.tyt.bcar.bean.BcarRecruitBean;
import com.tyt.bcar.bean.BcarRecruitQueryBean;
import com.tyt.model.TytBcarRecruit;

public interface BcarRecruitService extends BaseService<TytBcarRecruit, Long> {

	/**
	 * 获得大板车招聘列表
	 * 
	 * @param province
	 * @param city
	 * @param county
	 * @param dutyCode
	 * @param queryType
	 * @param querySign
	 * @return List<BcarRecruitQueryBean>
	 */

	public List<BcarRecruitQueryBean> query(String province, String city, String county, String dutyCode, int queryType, long querySign);

	/**
	 * 获得大板车招聘列表
	 * 
	 * @param user_id
	 * @param queryType
	 * @param querySign
	 * @return List<BcarRecruitQueryBean>
	 */

	public List<BcarRecruitQueryBean> myBcarRecruitList(long user_id, int queryType, long querySign);

	/**
	 * 置顶
	 * 
	 * @param id
	 * @return
	 */
	public Map<String, Long> updateTop(Long id);

	/**
	 * 设为已读
	 * 
	 * @param id
	 * @return
	 */
	public void updateSetRead(Long userId, Long id);

	/**
	 * 删除
	 * 
	 * @param id
	 */
	public void delTytBcarRecruit(Long id);

	/**
	 * 大板车发布招聘
	 * 
	 * @param bCar
	 * @param clientSign
	 * @param clientVersion
	 * @return
	 * @throws Exception
	 */
	public boolean addBcarRecruit(TytBcarRecruit bCar, String clientSign, String clientVersion) throws Exception;

	/**
	 * 根据MD5获取对象
	 * 
	 * @param md5
	 * @param userId
	 * @return id
	 * @throws Exception
	 */
	Long getByMd5(String md5, Long userId) throws Exception;

	/**
	 * 获得我的发布数量
	 * 
	 * @param id
	 * @return
	 */
	public int getMyPublishaNbr(Long userId);

	/**
	 * 获得新发布数量
	 * 
	 * @param sortId
	 * @return
	 */
	public int getNewPublishaNbr(Long sortId);

	/**
	 * 编辑对象
	 * 
	 * @param bCar
	 *            TytBcarRecruit老数据
	 * @param bCar
	 *            TytBcarRecruit新数据
	 * @param clientSign
	 *            客户端
	 * @param clientVersion
	 *            版本号
	 * @return true添加成功 false重复信息
	 * @throws Exception
	 */
	boolean updateBcarJob(TytBcarRecruit bCarOld, TytBcarRecruit bCar, String clientSign, String clientVersion) throws Exception;

	/**
	 * 获取详情
	 * 
	 * @param id
	 *            招聘ID
	 * @return
	 * @throws Exception
	 */
	public BcarRecruitBean getBcarRecruitInfo(Long id);

	/**
	 * 阅读次数加1
	 * 
	 * @param id
	 *            求职ID
	 * @throws Exception
	 */
	public void updateReadCounts(Long id) throws Exception;

	/**
	 * 记录用户浏览，改变信息浏览量，查询详情
	 * 
	 * @param id
	 *            招聘ID
	 * @param userId
	 *            用户ID
	 * @return 招聘详情
	 */
	public BcarRecruitBean updateRead(Long id, Long userId) throws Exception;

	/**
	 * 查询板车招聘信息是否属于用户
	 * 
	 * @param userId
	 * @param id
	 * @return
	 */
	public boolean isBlongUser(String userId, String id);

	public boolean isRecruitOpen(String id, int recruitStatusOpen);

	public void updateRecruitStatus(String id, int recruitStatusOpen);

	/**
	 * @description 根据用户ID更新显示状态
	 * <AUTHOR>
	 * @date 2021/12/25 18:36
	 * @param userId
	 * @param displayStatus
	 * @return void
	 */
	int updateRecruitStatusByUserId(Long userId,Integer displayStatus) throws Exception;

	/**
	 * 我的收藏
	 * 
	 * @param ids
	 *            id集合
	 * @param currentPage当前页面
	 * @return
	 */
	public List<BcarRecruitQueryBean> getBcarJobListByIds(List<Long> ids, Integer currentPage);

	/**
	 * 更新认证和开放状态
	 * 
	 * @param id
	 */
	public void updateVerifyAndOpenStatus(Long id);

	/**
	 * 指定时间内是否发布过板车求职信息
	 * 
	 * @param userId
	 * @param i
	 * @return
	 */
	public boolean isHasPublishedInDays(Long userId, int i);

}
