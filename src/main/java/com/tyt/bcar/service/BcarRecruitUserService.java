package com.tyt.bcar.service;

import java.math.BigInteger;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytBcarRecruitUser;

public interface BcarRecruitUserService extends BaseService<TytBcarRecruitUser, Long> {
	
	
	public TytBcarRecruitUser getTytBcarRecruitUser(Long userId,Long brId);
	/**
	 * 保存浏览记录
	 * @param brId 招聘ID
	 * @param userId 用户ID
	 * @param status 状态  0未读 1已读
	 * @return true保存 false不保存
	 */
	public boolean saveRecruitUser(Long brId, Long userId, int status);
	/**
	 * 查询浏览记录
	 * @param brId 招聘ID
	 * @param userId 用户ID
	 * @param status 状态  0未读 1已读
	 * @return
	 */ 
	BigInteger getRecruitUser(Long brId, Long userId, Integer status);

}
