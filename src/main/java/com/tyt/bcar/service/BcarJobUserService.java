package com.tyt.bcar.service;

import java.math.BigInteger;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytBcarJobUser;

public interface BcarJobUserService extends BaseService<TytBcarJobUser, Long> {
	/**
	 * 保存浏览记录
	 * @param brId 求职ID
	 * @param userId 用户ID
	 * @param status 状态  0未读 1已读
	 * @return true保存 false不保存
	 */
	public boolean saveJobUser(Long brId,Long userId,Integer status)throws Exception;
	/**
	 * 查询浏览记录
	 * @param brId 求职ID
	 * @param userId 用户ID
	 * @param status 状态  0未读 1已读
	 * @return
	 */ 
	public BigInteger getJobUser(Long brId,Long userId,Integer status)throws Exception;

}
