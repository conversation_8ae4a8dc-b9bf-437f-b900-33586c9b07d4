package com.tyt.bcar.service;

import java.util.List;
import com.tyt.base.service.BaseService;
import com.tyt.bcar.bean.BcarJobBean;
import com.tyt.bcar.bean.BcarJobQueryBean;
import com.tyt.bcar.bean.BcarTopBean;
import com.tyt.model.TytBcarJob;

public interface BcarJobService extends BaseService<TytBcarJob, Long> {
	/**
	 * 添加对象
	 * 
	 * @param bCar
	 *            TtyBcarJob
	 * @param clientSign
	 *            客户端
	 * @param clientVersion
	 *            版本号
	 * @return true添加成功 false重复信息
	 * @throws Exception
	 */
	public boolean addBcarJob(TytBcarJob bCar, String clientSign, String clientVersion) throws Exception;

	/**
	 * 编辑对象
	 * 
	 * @param bCar
	 *            TtyBcarJob老数据
	 * @param bCar
	 *            TtyBcarJob新数据
	 * @param clientSign
	 *            客户端
	 * @param clientVersion
	 *            版本号
	 * @return true添加成功 false重复信息
	 * @throws Exception
	 */
	public boolean updateBcarJob(Tyt<PERSON><PERSON><PERSON><PERSON> bCarOld, TytBcarJob bCar, String clientSign, String clientVersion) throws Exception;

	/**
	 * 根据MD5获取对象
	 * 
	 * @param md5
	 * @param userId
	 * @return id
	 * @throws Exception
	 */
	public Long getByMd5(String md5, Long userId) throws Exception;

	/**
	 * 删除求职
	 * 
	 * @param id
	 *            求职ID
	 * @param status
	 *            2审核通过 0删除
	 * @return
	 */
	public boolean delBcarJob(Long id, Integer status) throws Exception;

	/**
	 * 置顶求职
	 * 
	 * @param id
	 *            求职ID
	 * @return
	 * @throws Exception
	 */
	public BcarTopBean updateBcarJobTop(Long id) throws Exception;

	/**
	 * 阅读次数加1
	 * 
	 * @param id
	 *            求职ID
	 * @throws Exception
	 */
	public void updateReadCounts(Long id) throws Exception;

	/**
	 * 获得大板车求职列表
	 * 
	 * @param province
	 * @param city
	 * @param county
	 * @param dutyCode
	 * @param queryType
	 * @param querySign
	 * @return List<BcarRecruitQueryBean>
	 */

	public List<BcarJobQueryBean> query(String province, String city, String county, String dutyCode, int queryType, long querySign);

	/**
	 * 获得大板车求职列表
	 * 
	 * @param user_id
	 * @param queryType
	 * @param querySign
	 * @return List<BcarRecruitQueryBean>
	 */

	public List<BcarJobQueryBean> myBcarRecruitList(long user_id, int queryType, long querySign);

	/**
	 * 获得我的发布数量
	 * 
	 * @param id
	 * @return
	 */
	public int getMyPublishaNbr(Long userId);

	/**
	 * 获得新发布数量
	 * 
	 * @param sortId
	 * @return
	 */
	public int getNewPublishaNbr(Long sortId);

	/**
	 * 记录用户浏览，改变信息浏览量，查询详情
	 * 
	 * @param id
	 *            求职ID
	 * @param userId
	 *            用户ID
	 * @return 求职详情
	 */
	public BcarJobBean updateRead(Long id, Long userId) throws Exception;

	/**
	 * 查询用户是否收藏了某个求职信息
	 * 
	 * @param userId
	 * @param id
	 * @param type
	 *            收藏类型
	 * @return
	 */
	public boolean isHasColleced(String userId, String id, int type);

	/**
	 * 保存用户收藏的求职信息
	 * 
	 * @param userId
	 * @param id
	 * @param type
	 *            收藏类型 1是新车咨询 2 板车求职3 板车招聘4 设备求职5设备招聘
	 */
	public void saveCollect(String userId, String id, int type);

	/**
	 * 取消收藏求职信息
	 * 
	 * @param userId
	 * @param id
	 * @param type
	 *            收藏类型 1是新车咨询 2 板车求职3 板车招聘4 设备求职5设备招聘
	 */
	public void deleteCollect(String userId, String id, int type);

	/**
	 * 查询板车求职信息是否属于用户
	 * 
	 * @param userId
	 * @param id
	 * @return
	 */
	public boolean isBlongUser(String userId, String id);

	/**
	 * 查询板车求职信息是否处于开放状态
	 * 
	 * @param id
	 * @param jobStatusOpen
	 * @return
	 */
	public boolean isJobOpen(String id, int jobStatusOpen);

	/**
	 * 更新班车求职信息的开放状态
	 * 
	 * @param id
	 * @param jobStatusOpen
	 */
	public void updateJobStatus(String id, int jobStatusOpen);

	/**
	 * @description 根据用户ID更新显示状态
	 * <AUTHOR>
	 * @date 2021/12/25 18:36
	 * @param userId
	 * @param displayStatus
	 * @return void
	 */
	int updateJobStatusByUserId(Long userId,Integer displayStatus) throws Exception;

	/**
	 * 根据ID集合获取列表
	 * 
	 * @param ids要查询的id集合
	 * @param currentPage当前页面
	 * @return
	 */
	public List<BcarJobQueryBean> getBcarJobListByIds(List<Long> ids, int currentPage);

	/**
	 * 更新认证状态和开放状态
	 * 
	 * @param id
	 */
	public void updateVerifyAndOpenStatus(Long id);

	/**
	 * 查询在指定的天数内是否发布过板车招聘信息
	 * 
	 * @param userId
	 * @param days
	 * @return
	 */
	public boolean isHasPublishedInDays(Long userId, int days);
}
