package com.tyt.bcar.service.impl;


import java.math.BigInteger;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.bcar.service.BcarJobUserService;
import com.tyt.model.TytBcarJobUser;

@Service("bCarJobUserService")
public class BcarJobUserServiceImpl extends BaseServiceImpl<TytBcarJobUser, Long> implements
		BcarJobUserService {

	@Resource(name="bCarJobUserDao")
	public void setBaseDao(BaseDao<TytBcarJobUser, Long> bCarJobUserDao) {
		super.setBaseDao(bCarJobUserDao);
	}

	@Override
	public boolean saveJobUser(Long brId, Long userId, Integer status) {
		if(this.getJobUser(brId, userId, status)==null){
			this.add(new TytBcarJobUser(brId, userId, status));
			return true;
		}
		return false;
	}

	@Override
	public BigInteger getJobUser(Long brId, Long userId, Integer status) {
		String selectSQL="SELECT id from tyt_bcar_job_user "
				+ "where br_id=? and user_id=? and status=?";
		Object[] params=new Object[]{brId,userId,status};
		return this.getBaseDao().query(selectSQL, params);
	}
	
	
	
}
