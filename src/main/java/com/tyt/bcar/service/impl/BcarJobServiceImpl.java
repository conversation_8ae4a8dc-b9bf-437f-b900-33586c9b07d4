package com.tyt.bcar.service.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.bcar.bean.BcarJobBean;
import com.tyt.bcar.bean.BcarJobQueryBean;
import com.tyt.bcar.bean.BcarTopBean;
import com.tyt.bcar.service.BcarJobService;
import com.tyt.bcar.service.BcarJobUserService;
import com.tyt.bcar.util.BcarUtil;
import com.tyt.common.service.TytSequenceService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.TytBcarJob;
import com.tyt.model.TytBcarJobUser;
import com.tyt.model.TytSource;
import com.tyt.model.User;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSourceUtil;

@Service("bCarJobService")
public class BcarJobServiceImpl extends BaseServiceImpl<TytBcarJob, Long> implements BcarJobService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource(name = "userService")
	UserService userService;

	@Resource(name = "tytSequenceService")
	TytSequenceService tytSequenceService;

	@Resource(name = "bCarJobUserService")
	BcarJobUserService bcarJobUserService;

	@Resource(name = "bCarJobUserService")
	BcarJobUserService bCarJobUserService;

	@Resource(name = "bCarJobDao")
	public void setBaseDao(BaseDao<TytBcarJob, Long> bCarJobDao) {
		super.setBaseDao(bCarJobDao);
	}

	@Override
	public boolean addBcarJob(TytBcarJob bCar, String clientSign, String clientVersion) throws Exception {
		// 当前用户信息
		User user = userService.getByUserId(bCar.getUserId());
		// 生成md5
		String bCarMd5 = BcarUtil.createMd5(bCar.getAge(), null, bCar.getCity(), bCar.getCounty(), bCar.getDutyCode(), bCar.getProvince(), bCar.getRemark(), bCar.getUserId(), bCar.getSalaryCode(), bCar.getTelephone(), bCar.getTelName(), bCar.getTitle(), bCar.getYears());
		// 滤重
		Long has = this.getByMd5(bCarMd5, bCar.getUserId());
		if (has == null) {
			bCar.setMd5(bCarMd5);
			bCar.setPublishTime(new Date());
			bCar.setCtime(new Date());
			bCar.setUtime(new Date());
			bCar.setCellPhone(user.getCellPhone());
			bCar.setClientSign(clientSign);
			bCar.setClientVersion(clientVersion);
			bCar.setStatus(Constant.INFO_STATUS_PASS);
			bCar.setSortId(tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_BCAR_JOB_NAME));
			TytSource dutySource = TytSourceUtil.getSourceName("duty", bCar.getDutyCode() + "");
			bCar.setDuty(dutySource == null ? null : dutySource.getName());
			TytSource salarySource = TytSourceUtil.getSourceName("duty", bCar.getDutyCode() + "", bCar.getSalaryCode() + "");
			bCar.setSalary(salarySource == null ? null : salarySource.getName());
			bCar.setReadNbr(0);
			bCar.setResendCounts(0);
			this.add(bCar);
			return true;
		} else {
			return false;

		}

	}

	@Override
	public boolean updateBcarJob(TytBcarJob bCarOld, TytBcarJob bCar, String clientSign, String clientVersion) throws Exception {
		// 生成md5
		String bCarMd5 = BcarUtil.createMd5(bCar.getAge(), null, bCar.getCity(), bCar.getCounty(), bCar.getDutyCode(), bCar.getProvince(), bCar.getRemark(), bCar.getUserId(), bCar.getSalaryCode(), bCar.getTelephone(), bCar.getTelName(), bCar.getTitle(), bCar.getYears());
		// 滤重
		Long has = this.getByMd5(bCarMd5, bCar.getUserId());
		if (has == null || has != null && has.longValue() == bCar.getId().longValue()) {
			bCarOld.setTitle(bCar.getTitle());
			bCarOld.setTelName(bCar.getTelName());
			bCarOld.setProvince(bCar.getProvince());
			bCarOld.setCity(bCar.getCity());
			bCarOld.setCounty(bCar.getCounty());
			bCarOld.setAge(bCar.getAge());
			bCarOld.setYears(bCar.getYears());
			bCarOld.setTelephone(bCar.getTelephone());
			bCarOld.setRemark(bCar.getRemark());
			bCarOld.setMd5(bCarMd5);
			bCarOld.setPublishTime(new Date());
			bCarOld.setUtime(new Date());
			bCarOld.setSortId(tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_BCAR_JOB_NAME));
			bCarOld.setDutyCode(bCar.getDutyCode());
			TytSource dutySource = TytSourceUtil.getSourceName("duty", bCar.getDutyCode() + "");
			bCarOld.setDuty(dutySource == null ? null : dutySource.getName());
			bCarOld.setSalaryCode(bCar.getSalaryCode());
			TytSource salarySource = TytSourceUtil.getSourceName("duty", bCar.getDutyCode() + "", bCar.getSalaryCode() + "");
			bCarOld.setSalary(salarySource == null ? null : salarySource.getName());
			this.update(bCarOld);
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 更新开放状态和审核状态
	 * 
	 * @param id
	 * @param openStatus
	 * @param verifyStatus
	 */
	private void updateOpenAndVerifyStatus(Long id, int openStatus, int verifyStatus) {
		String sql = "UPDATE tyt_bcar_job tbj SET tbj.`status`=?, tbj.`display_status`=? WHERE tbj.`id`=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { verifyStatus, openStatus, id });
	}

	@Override
	public Long getByMd5(String md5, Long userId) throws Exception {
		String selectSQL = "SELECT id FROM tyt_bcar_job where md5=? and status=? and user_id=?";
		Object[] params = new Object[] { md5, Constant.INFO_STATUS_PASS, userId };
		BigInteger obj = this.getBaseDao().query(selectSQL, params);
		if (obj == null) {
			return null;
		} else {
			return obj.longValue();
		}
	}

	@Override
	public boolean delBcarJob(Long id, Integer status) throws Exception {
		String delSQL = "UPDATE tyt_bcar_job SET del_status=? where id=?";
		Object[] params = new Object[] { status, id };
		this.executeUpdateSql(delSQL, params);
		return true;
	}

	@Override
	public BcarTopBean updateBcarJobTop(Long id) throws Exception {
		Long sortId = tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_BCAR_JOB_NAME);
		Date now = new Date();
		String topSQL = "UPDATE tyt_bcar_job SET resend_counts=resend_counts+1,sort_id=?,publish_time=?,utime=?" + " where id=? and status=?";
		Object[] params = new Object[] { sortId, now, now, id, Constant.INFO_STATUS_PASS };
		this.executeUpdateSql(topSQL, params);
		BcarTopBean bCarTopBean = new BcarTopBean(sortId, now);
		return bCarTopBean;
	}

	@Override
	public void updateReadCounts(Long id) throws Exception {
		String updateSQL = "update tyt_bcar_job set read_nbr=read_nbr+1 ,utime=?  where id=?";
		this.getBaseDao().executeUpdateSql(updateSQL, new Object[] { new Date(), id });
	}

	@SuppressWarnings("deprecation")
	@Override
	public List<BcarJobQueryBean> query(String province, String city, String county, String dutyCode, int queryType, long querySign) {
		List<BcarJobQueryBean> returnList = null;
		// 第一次查询大小
		int firstPageSize = AppConfig.getIntProperty("tyt.job.query.first.page.size");
		// 上滑动下滑动最大结果集大小
		int pageSize = AppConfig.getIntProperty("tyt.job.query.page.size");
		List<Object> list = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer(" SELECT  id,sort_id sortId,title,salary_code salaryCode,salary,duty_code dutyCode,duty,province,city,county,publish_time publishTime,user_id userId FROM tyt_bcar_job m " + " WHERE (status=? OR status=?) AND del_status=? AND display_status=?");
		list.add(2);
		list.add(3);
		list.add(0);
		list.add(0);
		int searchSize = pageSize;
		if (null != province && !"".equals(province.trim())) {
			sb.append(" and province=? ");
			list.add(province.trim());
		}
		if (null != city && !"".equals(city.trim()) && city.indexOf(province) == -1) {
			sb.append(" and city=? ");
			list.add(city.trim());
		}
		if (null != county && !"".equals(county.trim()) && !county.equals(city)) {
			sb.append(" and county=? ");
			list.add(county.trim());
		}
		if (null != dutyCode && !"".equals(dutyCode.trim())) {
			sb.append(" and duty_code=? ");
			list.add(dutyCode.trim());
		}
		// 第一次请求
		if (queryType == 1) {
			searchSize = firstPageSize;
			// 大小排序
			sb.append(" order by m.sort_id desc ");
		} else
		// 下拉查新数据
		if (queryType == 0) {
			if (querySign == 0) {
				sb.append(" and m.sort_id>?");
				// 小大排序
				sb.append(" order by m.sort_id desc ");
			} else {
				sb.append(" and m.sort_id>?");
				// 小大排序
				sb.append(" order by m.sort_id asc ");
			}
			list.add(querySign);
		}
		// 上推查历史数据
		else {
			sb.append(" and m.sort_id<?");
			// 大小排序
			sb.append(" order by m.sort_id desc ");
			list.add(querySign);
		}
		// 查询数据集
		long t3 = 0, t4 = 0;
		t3 = System.currentTimeMillis();
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("title", Hibernate.STRING);
		scalarMap.put("sortId", Hibernate.LONG);
		scalarMap.put("salaryCode", Hibernate.INTEGER);
		scalarMap.put("salary", Hibernate.STRING);
		scalarMap.put("dutyCode", Hibernate.INTEGER);
		scalarMap.put("duty", Hibernate.STRING);
		scalarMap.put("province", Hibernate.STRING);
		scalarMap.put("city", Hibernate.STRING);
		scalarMap.put("county", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("userId", Hibernate.LONG);
		List<BcarJobQueryBean> queryList = this.getBaseDao().search(sb.toString(), scalarMap, BcarJobQueryBean.class, list.toArray(), 1, searchSize);
		t4 = System.currentTimeMillis();
		logger.info("数据库查询时间：" + (t4 - t3) + "ms");
		if (queryList != null && queryList.size() > 0) {
			returnList = new ArrayList<BcarJobQueryBean>();
			int size = queryList.size();
			if (queryType == 0 && querySign > 0) {
				for (int i = size - 1; i >= 0; i--) {
					BcarJobQueryBean t = queryList.get(i);
					returnList.add(t);
				}
			} else {
				returnList = queryList;
			}
		}
		return returnList;
	}

	@SuppressWarnings("deprecation")
	@Override
	public List<BcarJobQueryBean> myBcarRecruitList(long user_id, int queryType, long querySign) {
		List<BcarJobQueryBean> returnList = null;
		// 第一次查询大小
		int firstPageSize = AppConfig.getIntProperty("tyt.job.query.first.page.size");
		// 上滑动下滑动最大结果集大小
		int pageSize = AppConfig.getIntProperty("tyt.job.query.page.size");
		List<Object> list = new ArrayList<Object>();
		StringBuffer sb = new StringBuffer(" SELECT STATUS AS 'verifyStatus', display_status AS 'openStatus', id,sort_id sortId,title,salary_code salaryCode,salary,duty_code dutyCode,duty,province,city,county,publish_time publishTime,user_id userId FROM tyt_bcar_job m " + " WHERE del_status=? and user_id=? ");
		list.add(0);
		list.add(user_id);
		int searchSize = pageSize;
		// 第一次请求
		if (queryType == 1) {
			searchSize = firstPageSize;
			// 大小排序
			sb.append(" order by m.sort_id desc ");
		} else
		// 下拉查新数据
		if (queryType == 0) {
			if (querySign == 0) {
				sb.append(" and m.sort_id>?");
				// 小大排序
				sb.append(" order by m.sort_id desc ");
			} else {
				sb.append(" and m.sort_id>?");
				// 小大排序
				sb.append(" order by m.sort_id asc ");
			}
			list.add(querySign);
		}
		// 上推查历史数据
		else {
			sb.append(" and m.sort_id<?");
			// 大小排序
			sb.append(" order by m.sort_id desc ");
			list.add(querySign);
		}
		// 查询数据集
		long t3 = 0, t4 = 0;
		t3 = System.currentTimeMillis();
		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("title", Hibernate.STRING);
		scalarMap.put("sortId", Hibernate.LONG);
		scalarMap.put("salaryCode", Hibernate.INTEGER);
		scalarMap.put("salary", Hibernate.STRING);
		scalarMap.put("dutyCode", Hibernate.INTEGER);
		scalarMap.put("duty", Hibernate.STRING);
		scalarMap.put("province", Hibernate.STRING);
		scalarMap.put("city", Hibernate.STRING);
		scalarMap.put("county", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("openStatus", Hibernate.INTEGER);
		scalarMap.put("verifyStatus", Hibernate.INTEGER);
		List<BcarJobQueryBean> queryList = this.getBaseDao().search(sb.toString(), scalarMap, BcarJobQueryBean.class, list.toArray(), 1, searchSize);
		t4 = System.currentTimeMillis();
		logger.info("数据库查询时间：" + (t4 - t3) + "ms");
		if (queryList != null && queryList.size() > 0) {
			returnList = new ArrayList<BcarJobQueryBean>();
			int size = queryList.size();
			if (queryType == 0 && querySign > 0) {
				for (int i = size - 1; i >= 0; i--) {
					BcarJobQueryBean t = queryList.get(i);
					returnList.add(t);
				}
			} else {
				returnList = queryList;
			}
		}
		return returnList;
	}

	@Override
	public int getMyPublishaNbr(Long userId) {
		String sql = "select count(*) from tyt_bcar_job where user_id=? and  status<? and status>? ";
		BigInteger c = this.getBaseDao().query(sql, new Object[] { userId, Constant.INFO_STATUS_NEVER, Constant.INFO_STATUS_DISABLE });
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}

	@Override
	public int getNewPublishaNbr(Long sortId) {
		String sql = "select count(*) from tyt_bcar_job where sort_id>? and  (status=? OR status=?) AND del_status=? AND display_status=? ";
		BigInteger c = this.getBaseDao().query(sql, new Object[] { sortId, 2,3,0,0 });
		if (c != null) {
			return c.intValue();
		}
		return 0;
	}

	@Override
	public BcarJobBean updateRead(Long id, Long userId) throws Exception {
		// 获取详情
		TytBcarJob bCar = this.getById(id);
		if (bCar != null) {
			if (bCar.getUserId().longValue() != userId.longValue()) {
				BigInteger jobUserId = bCarJobUserService.getJobUser(id, userId, 1);// 浏览记录存在否？
				if (jobUserId == null) {
					bCarJobUserService.add(new TytBcarJobUser(id, userId, 1));// 添加浏览记录
					this.updateReadCounts(id);// 修改浏览人数
				}
			}
			BcarJobBean bCarJob = new BcarJobBean();
			BeanUtils.copyProperties(bCar, bCarJob);
			return bCarJob;
		}
		return null;
	}

	@Override
	public boolean isHasColleced(String userId, String id, int type) {
		String sql = "SELECT COUNT(*) FROM tyt_collect_info tci WHERE tci.user_id=? AND tci.ms_id=? AND tci.`type`=? AND tci.`status`=0";
		BigInteger count = this.getBaseDao().query(sql, new Object[] { userId, id, type });
		return count.intValue() == 1;
	}

	@Override
	public void saveCollect(String userId, String id, int type) {
		/*
		 * 判断收藏信息是否已经存在（可能是出于取消收藏状态）
		 */
		String sql = "SELECT COUNT(*) FROM tyt_collect_info tci WHERE tci.user_id=? AND tci.ms_id=? AND tci.`type`=?";
		BigInteger count = this.getBaseDao().query(sql, new Object[] { userId, id, type });
		Object[] params;
		/*
		 * 存在则更新，不存在则添加
		 */
		if (count.intValue() == 1) {
			sql = "UPDATE tyt_collect_info tci SET tci.status=? WHERE tci.user_id=? AND tci.ms_id=? AND tci.`type`=?";
			params = new Object[] { 0, userId, id, type };
		} else {
			sql = "INSERT INTO `tyt`.`tyt_collect_info` (`user_id`, `ms_id`, `type`, `status`, `ctime`, `utime`) VALUES (?, ?, ?, ?, ?, ?)";
			params = new Object[] { userId, id, type, 0, new Date(), new Date() };
		}
		this.getBaseDao().executeUpdateSql(sql, params);
	}

	@Override
	public void deleteCollect(String userId, String id, int type) {
		String sql = "UPDATE tyt_collect_info tci SET tci.status=? WHERE tci.user_id=? AND tci.ms_id=? AND tci.`type`=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { 1, userId, id, type });
	}

	@Override
	public boolean isBlongUser(String userId, String id) {
		String sql = "SELECT COUNT(*) FROM tyt_bcar_job tsju WHERE tsju.`id`=? AND tsju.`user_id`=?";
		BigInteger count = this.getBaseDao().query(sql, new Object[] { id, userId });
		return count.intValue() == 1;
	}

	@Override
	public boolean isJobOpen(String id, int jobStatusOpen) {
		String sql = "select count(*) from tyt_bcar_job tsj where tsj.`id`=? and tsj.`display_status`=?";
		BigInteger count = this.getBaseDao().query(sql, new Object[] { id, jobStatusOpen });
		return count.intValue() == 1;
	}

	@Override
	public void updateJobStatus(String id, int jobStatusOpen) {
		// 获取当前排序字段
		Long sortId = tytSequenceService.updateGetNextSequenceNbr(Constant.TABLE_BCAR_JOB_NAME);
		String sql = "UPDATE tyt_bcar_job tsj SET tsj.`display_status`=?, tsj.`sort_id`=?, tsj.`utime`=NOW(), tsj.`publish_time`=NOW() WHERE tsj.`id`=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { jobStatusOpen, sortId, id });
	}

	/**
	 * @description 根据用户ID更新显示状态
	 * <AUTHOR>
	 * @date 2021/12/25 18:36
	 * @param userId
	 * @param displayStatus
	 * @return void
	 */
	@Override
	public int updateJobStatusByUserId(Long userId, Integer displayStatus) throws Exception{
		String sql = "UPDATE tyt_bcar_job tbj SET tbj.`display_status`=?, tbj.`utime`=NOW() WHERE tbj.`user_id`=?";
		int result = this.getBaseDao().executeUpdateSql(sql, new Object[]{displayStatus, userId});
		return result;
	}

	@SuppressWarnings("deprecation")
	@Override
	public List<BcarJobQueryBean> getBcarJobListByIds(List<Long> ids, int currentPage) {

		Map<String, Object> params = new HashMap<String, Object>();
		StringBuffer sb = new StringBuffer(" SELECT  id,sort_id sortId,title,salary_code salaryCode,salary,duty_code dutyCode,duty,province,city,county,publish_time publishTime,user_id userId,display_status openStatus,status verifyStatus FROM tyt_bcar_job m " + " WHERE id IN(:ids) AND STATUS IN(:status) and del_status IN(:delStatus) order by m.sort_id desc");
		params.put("ids", ids);
		params.put("status", new Object[] { 2, 3 });
		params.put("delStatus", new Object[] { 0 });

		// 查询数据集
		long t3 = 0, t4 = 0;
		t3 = System.currentTimeMillis();

		Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
		scalarMap.put("id", Hibernate.LONG);
		scalarMap.put("title", Hibernate.STRING);
		scalarMap.put("sortId", Hibernate.LONG);
		scalarMap.put("salaryCode", Hibernate.INTEGER);
		scalarMap.put("salary", Hibernate.STRING);
		scalarMap.put("dutyCode", Hibernate.INTEGER);
		scalarMap.put("duty", Hibernate.STRING);
		scalarMap.put("province", Hibernate.STRING);
		scalarMap.put("city", Hibernate.STRING);
		scalarMap.put("county", Hibernate.STRING);
		scalarMap.put("publishTime", Hibernate.TIMESTAMP);
		scalarMap.put("userId", Hibernate.LONG);
		scalarMap.put("openStatus", Hibernate.INTEGER);
		scalarMap.put("verifyStatus", Hibernate.INTEGER);

		List<BcarJobQueryBean> queryList = this.getBaseDao().search(sb.toString(), scalarMap, BcarJobQueryBean.class, params);

		t4 = System.currentTimeMillis();
		logger.info("数据库查询时间：" + (t4 - t3) + "ms");

		return queryList;
	}

	@Override
	public void updateVerifyAndOpenStatus(Long id) {
		/*
		 * 获取信息执行如下操作 1.如果当前信息为审核未通过状态，编辑后变为未审核，开放状态，需要置顶。
		 * 2.当前信息为未审核状态并且开放状态，编辑后需要置顶。 3.当前信息为未审核并且关闭状态，编辑后变为开放状态，并执行置顶操作。
		 * 4.当前信息为审核且开放状态，编辑后需要置顶。 5.当前信息为审核且关闭状态，编辑后变为开放状态，并执行置顶操作。
		 */
		TytBcarJob job = this.getById(id);
		/*
		 * 审核状态 0;无效1;审核未通过2;待审核3;审核通过
		 */
		if (job.getStatus() == 1) {
			updateOpenAndVerifyStatus(id, 0, 2);
		} else if (job.getStatus() == 2) {
			updateOpenAndVerifyStatus(id, 0, 2);
		} else if (job.getStatus() == 3) {
			updateOpenAndVerifyStatus(id, 0, 3);
		}
	}

	@Override
	public boolean isHasPublishedInDays(Long userId, int days) {
		String sql = "SELECT COUNT(*) FROM tyt_bcar_recruit tbj WHERE UNIX_TIMESTAMP(tbj.`publish_time`)>? AND tbj.`user_id`=?";
		// 获取指定天数前的日期
		Date date = TimeUtil.addDay(-days);
		long secondDaysAgo = date.getTime() / 1000;
		BigInteger count = this.getBaseDao().query(sql, new Object[] { secondDaysAgo, userId });
		return count.intValue() > 0;
	}
}
