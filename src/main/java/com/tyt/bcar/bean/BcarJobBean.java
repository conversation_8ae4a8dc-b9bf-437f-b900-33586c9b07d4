package com.tyt.bcar.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcarJobBean {

	private Long id;
	private Long userId;
	private String title;
	private String telName;
	private String telephone;
	private Integer age;
	private Integer years;
	private String salary;
	private Integer salaryCode;
	private String duty;
	private Integer dutyCode;
	private String province;
	private String city;
	private String county;
	private String remark;
	private Date publishTime;
	private Integer readNbr;
	/* 是否收藏该信息，0：已收藏 1：未收藏 */
	private Integer isCollect;
	/* 在30天内是否发布过大板车求职信息，1：发布过 2：没有发布过 */
	private Integer hasPublished;

	public Integer getHasPublished() {
		return hasPublished;
	}

	public void setHasPublished(Integer hasPublished) {
		this.hasPublished = hasPublished;
	}

	public Integer getIsCollect() {
		return isCollect;
	}

	public void setIsCollect(Integer isCollect) {
		this.isCollect = isCollect;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTelName() {
		return telName;
	}

	public void setTelName(String telName) {
		this.telName = telName;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public Integer getYears() {
		return years;
	}

	public void setYears(Integer years) {
		this.years = years;
	}

	public String getSalary() {
		return salary;
	}

	public void setSalary(String salary) {
		this.salary = salary;
	}

	public String getDuty() {
		return duty;
	}

	public void setDuty(String duty) {
		this.duty = duty;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getPublishTime() {
		return publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	public Integer getSalaryCode() {
		return salaryCode;
	}

	public void setSalaryCode(Integer salaryCode) {
		this.salaryCode = salaryCode;
	}

	public Integer getDutyCode() {
		return dutyCode;
	}

	public void setDutyCode(Integer dutyCode) {
		this.dutyCode = dutyCode;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getReadNbr() {
		return readNbr;
	}

	public void setReadNbr(Integer readNbr) {
		this.readNbr = readNbr;
	}

	@Override
	public String toString() {
		return "BcarJobBean [id=" + id + ", userId=" + userId + ", title=" + title + ", telName=" + telName + ", telephone=" + telephone + ", age=" + age + ", years=" + years + ", salary=" + salary + ", salaryCode=" + salaryCode + ", duty=" + duty + ", dutyCode=" + dutyCode + ", province=" + province + ", city=" + city + ", county=" + county + ", remark=" + remark + ", publishTime=" + publishTime + ", readNbr=" + readNbr + ", isCollect=" + isCollect + "]";
	}
}
