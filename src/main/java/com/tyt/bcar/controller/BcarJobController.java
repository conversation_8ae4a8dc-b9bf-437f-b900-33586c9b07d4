package com.tyt.bcar.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.tyt.corp.CorpRestClient;
import com.tyt.model.*;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.bcar.bean.BcarJobBean;
import com.tyt.bcar.bean.BcarJobQueryBean;
import com.tyt.bcar.bean.BcarTopBean;
import com.tyt.bcar.service.BcarJobService;
import com.tyt.bcar.service.BcarRecruitService;
import com.tyt.bcar.util.BcarUtil;
import com.tyt.common.service.TytBubbleService;
import com.tyt.merchant.service.TytMerchantService;
import com.tyt.scar.service.ScarJobService;
import com.tyt.scar.service.ScarRecruitService;
import com.tyt.transport.service.TransportService;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;

/**
 * 大板车
 * 
 * <AUTHOR>
 * @date 2016年4月12日下午6:00:57
 * 
 */
@Controller
@RequestMapping("/plat/bcar/job")
public class BcarJobController extends BaseController {
	private static final int JOB_STATUS_OPEN = 0;
	private static final int JOB_STATUS_CLOSE = 1;
	private static final int BCAR_STATUS = 2;

	@Resource(name = "bCarJobService")
	BcarJobService bCarJobService;

	@Resource(name = "bCarRecruitService")
	BcarRecruitService bCarRecruitService;

	@Resource(name = "sCarJobService")
	ScarJobService sCarJobService;

	@Resource(name = "sCarRecruitService")
	ScarRecruitService sCarRecruitService;

	@Resource(name = "transportService")
	TransportService transportService;

	@Resource(name = "tytMerchantService")
	TytMerchantService tytMerchantService;
	

	@Resource(name = "tytBubbleService")
	TytBubbleService tytBubbleService;
	
	@Resource(name = "userService")
	private UserService userService;
	@Resource(name = "tytUserSubService")
	TytUserSubService tytUserSubService;

	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;
	/**
	 * APP保存求职信息
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/save")
	@ResponseBody
	public ResultMsgBean saveBcarJob(BaseParameter baseParameter, TytBcarJob bCar) {

		ResultMsgBean rm = new ResultMsgBean();
		try {
			String stringValue = tytConfigService.getStringValue("bcar_recruit_open_status");
			if(org.apache.commons.lang3.StringUtils.isNotBlank(stringValue)){
				if(bCar != null && bCar.getUserId() != null){
					if(stringValue.contains(bCar.getUserId().toString())){
						rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
						rm.setMsg("您已被禁止发布此类消息");
						return rm;
					}
				}
			}
			// 必填项验证
			if (!BcarUtil.validateParams(rm, bCar.getTitle(), bCar.getTelName(), bCar.getDutyCode(), bCar.getProvince(), bCar.getCity(), bCar.getCounty(), bCar.getSalaryCode(), bCar.getTelephone()))
				return rm;
			// 发布
			if (!bCarJobService.addBcarJob(bCar, baseParameter.getClientSign(), baseParameter.getClientVersion())) {
				rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
				rm.setMsg("您已经发布过此条信息!");
				return rm;
			} else {
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("发布成功");
				return rm;
			}

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	/**
	 * APP编辑求职
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/update")
	@ResponseBody
	public ResultMsgBean updateBcarJob(BaseParameter baseParameter, TytBcarJob bCar) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			String stringValue = tytConfigService.getStringValue("bcar_recruit_open_status");
			if(org.apache.commons.lang3.StringUtils.isNotBlank(stringValue)){
				if(bCar != null && bCar.getUserId() != null){
					if(stringValue.contains(bCar.getUserId().toString())){
						rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
						rm.setMsg("您已被禁止发布此类消息");
						return rm;
					}
				}
			}
			// 必填项验证
			if (bCar.getId() == null || bCar.getId().longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职id不能为空");
				return rm;
			}
			// 老数据
			TytBcarJob bCarOld = bCarJobService.getById(bCar.getId());
			if (bCarOld == null) {
				rm.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
				rm.setMsg("非法信息编辑");
				return rm;
			}
			// 必填项验证
			if (!BcarUtil.validateParams(rm, bCar.getTitle(), bCar.getTelName(), bCar.getDutyCode(), bCar.getProvince(), bCar.getCity(), bCar.getCounty(), bCar.getSalaryCode(), bCar.getTelephone()))
				return rm;
			if (bCarJobService.updateBcarJob(bCarOld, bCar, baseParameter.getClientSign(), baseParameter.getClientVersion())) {
				// 更新认证状态和开放状态
				bCarJobService.updateVerifyAndOpenStatus(bCar.getId());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("修改成功");
				return rm;
			} else {
				rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
				rm.setMsg("您已经发布过此条信息!");
				return rm;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	/**
	 * APP查询求职详情
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping("/info")
	@ResponseBody
	public ResultMsgBean getBcarJobInfo(Long id, Long userId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (id == null || id.longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职id不能为空");
				return rm;
			}
			// 查询信息
			BcarJobBean bCarJob = bCarJobService.updateRead(id, userId);
			// 查询用户是否收藏了该条信息
			boolean isHasCollected = bCarJobService.isHasColleced(userId + "", id + "", 2);
			bCarJob.setIsCollect(isHasCollected ? 0 : 1);
			// 查询用户在30天内是否发布过板车招聘信息
			boolean isHasPublished = bCarJobService.isHasPublishedInDays(userId, 30);
			bCarJob.setHasPublished(isHasPublished ? 1 : 2);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("获取成功");
			rm.setData(bCarJob);
			return rm;
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * APP删除求职信息
	 * 
	 * @param id
	 */
	@RequestMapping("/del")
	@ResponseBody
	public ResultMsgBean delBcarJob(Long id) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (id == null || id.longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职id不能为空");
				return rm;
			}
			bCarJobService.delBcarJob(id, 1);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("删除成功");
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * App求职置顶
	 * 
	 * @param id
	 */
	@RequestMapping("/top")
	@ResponseBody
	public ResultMsgBean updateBcarJobTop(Long id) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (id == null || id.longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职id不能为空");
				return rm;
			}
			BcarTopBean bCarTopBean = bCarJobService.updateBcarJobTop(id);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("信息排位已经提前");
			rm.setData(bCarTopBean);
			return rm;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * APP查询求职列表接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter, Integer queryType, Long querySign, String province, String city, String county, String dutyCode) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
				List<BcarJobQueryBean> list = bCarJobService.query(province, city, county, dutyCode, queryType.intValue(), querySign == null ? 0 : querySign.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private boolean checkQueryParameter(Integer queryType, Long querySign, ResultMsgBean rm) {
		if (queryType == null || (queryType.intValue() != 0 && queryType.intValue() != 1 && queryType.intValue() != 2)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询类型不正确！");
			return false;
		} else if (queryType.intValue() != 1 && (querySign == null || querySign.longValue() < 0)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询标识错误，最大、最小ID为空！");
			return false;
		}
		return true;

	}

	/**
	 * APP我的发布列表查询
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/myPublish")
	@ResponseBody
	public ResultMsgBean myPublish(BaseParameter baseParameter, Integer queryType, Long querySign) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
				List<BcarJobQueryBean> list = bCarJobService.myBcarRecruitList(baseParameter.getUserId(), queryType.intValue(), querySign == null ? 0 : querySign.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}


	/**
	 * 我的发布数量
	 * 
	 * @param baseParameter
	 * @param carType
	 * @return
	 */
	@RequestMapping("/myPublishaNbr")
	@ResponseBody
	public ResultMsgBean myPublishaNbr(BaseParameter baseParameter, String carType) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (carType == null || "".equals(carType)) {

				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("carType不能为空");
			} else if (!carType.equals("0") && !carType.equals("1") && !carType.equals("2") && !carType.equals("3") && !carType.equals("4") && !carType.equals("7") && !carType.equals("9")) {
				rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
				rm.setMsg("carType参数类型错误");
			} else if (carType.equals("7")) {
				Map<String, String> map = new HashMap<String, String>();
				//此处Android调用
				// CORP 增加企业货物的总数量返回，包括已读未读的
				String totalBubleNumber = "0";
//                totalBubleNumber = CorpRestClient.getInstance().getCorpTransTotalBubleNumber(baseParameter.getUserId());
				if(StringUtils.isNotEmpty(totalBubleNumber)){
					map.put("corpTotalGoodsNbr",totalBubleNumber);
				}else{
					map.put("corpTotalGoodsNbr","0");
				}
				// CORP 增加企业货物的数量返回，未读数量
				String bubleNumber = "0";
//                bubleNumber =  CorpRestClient.getInstance().getCorpTransBubleNumber(baseParameter.getUserId());
				if(StringUtils.isNotEmpty(bubleNumber)){
					map.put("corpGoodsNbr",bubleNumber);
				}else{
					map.put("corpGoodsNbr","0");
				}
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(map);

			} else {
				Map<String, String> map = new HashMap<String, String>();
				if ("1".equals(carType)) {
					int c = bCarRecruitService.getMyPublishaNbr(baseParameter.getUserId());
					int n = bCarJobService.getMyPublishaNbr(baseParameter.getUserId());
					map.put("bNbr", c + ""); // 招聘数量
					map.put("bJobNbr", n + ""); // 求职数量
				} else if ("2".equals(carType)) {
					// 设备
					int c = sCarRecruitService.getMyPublishaNbr(baseParameter.getUserId());
					int n = sCarJobService.getMyPublishaNbr(baseParameter.getUserId());
					map.put("sNbr", c + "");// 招聘数量
					map.put("sJobNbr", n + ""); // 求职数量
				} else if ("0".equals(carType)) {
					int c = bCarRecruitService.getMyPublishaNbr(baseParameter.getUserId());
					int n = bCarJobService.getMyPublishaNbr(baseParameter.getUserId());
					map.put("bNbr", c + ""); // 招聘数量
					map.put("bJobNbr", n + ""); // 求职数量
					// 设备
					c = sCarRecruitService.getMyPublishaNbr(baseParameter.getUserId());
					n = sCarJobService.getMyPublishaNbr(baseParameter.getUserId());
					map.put("sNbr", c + "");// 招聘数量
					map.put("sJobNbr", n + ""); // 求职数量
					// 货物
					long cc = transportService.getMyPushTytNbr(baseParameter.getUserId(), 1,"0");
					map.put("tranNbr", cc + "");
					// 我得商户信息角标数
					long d = tytMerchantService.getMyMerchantNbr(baseParameter.getUserId(), "0,1");
					map.put("merchantNbr", d + "");
					
					// 我的已接单
					int rr = tytBubbleService.getUserReceivedOrderNbr(baseParameter.getUserId());
					map.put("receivedOrderNbr", rr + "");
					//人工派单数
					User u=userService.getById(baseParameter.getUserId());
					int pd=tytBubbleService.getPerpleOrders(u.getCellPhone());
					map.put("peopleOrdersNbr", pd + "");

					//此处IOS调用
					// CORP 增加企业货物的总数量返回，包括已读未读的
					String totalBubleNumber = "0";
//                    totalBubleNumber = CorpRestClient.getInstance().getCorpTransTotalBubleNumber(baseParameter.getUserId());
					if(StringUtils.isNotEmpty(totalBubleNumber)){
						map.put("corpTotalGoodsNbr",totalBubleNumber);
					}else{
						map.put("corpTotalGoodsNbr","0");
					}
					// CORP 增加企业货物的数量返回，未读数量
					String bubleNumber = "0";
//                    bubleNumber = CorpRestClient.getInstance().getCorpTransBubleNumber(baseParameter.getUserId());
					if(StringUtils.isNotEmpty(bubleNumber)){
						map.put("corpGoodsNbr",bubleNumber);
					}else{
						map.put("corpGoodsNbr","0");
					}
					//APP信息费改版气泡
					int infoFeeCarOwnerBubble=tytBubbleService.getInfoFeeBubble(baseParameter.getUserId(),1,7);
					map.put("infoFeeCarOwnerBubble",infoFeeCarOwnerBubble+"");
					int infoFeeShipperBubble=tytBubbleService.getInfoFeeBubble(baseParameter.getUserId(),2,7);
					map.put("infoFeeShipperBubble",infoFeeShipperBubble+"");
					//将购买货运险的人数提示放入到map集合中
					putBuyerNumTips(map);
				} else if ("3".equals(carType)) {
					// 货物
					long c = transportService.getMyPushTytNbr(baseParameter.getUserId(), 1,"0");
					map.put("tranNbr", c + "");
				} else if ("4".equals(carType)) {
					// 我得商户信息角标数
					long d = tytMerchantService.getMyMerchantNbr(baseParameter.getUserId(), "(0,1)");
					map.put("merchantNbr", d + "");
				}else if ("5".equals(carType)) {
					// 我的已接单
					int d = tytBubbleService.getUserReceivedOrderNbr(baseParameter.getUserId());
					map.put("receivedOrderNbr", d + "");
				}if("6".equals(carType)){
					//人工派单数
					User u=userService.getById(baseParameter.getUserId());
					int pd=tytBubbleService.getPerpleOrders(u.getCellPhone());
					map.put("peopleOrdersNbr", pd + "");
				} else if ("8".equals(carType)) {
					//APP信息费改版气泡
					int infoFeeCarOwnerBubble=tytBubbleService.getInfoFeeBubble(baseParameter.getUserId(),1,7);
					map.put("infoFeeCarOwnerBubble",infoFeeCarOwnerBubble+"");
					int infoFeeShipperBubble=tytBubbleService.getInfoFeeBubble(baseParameter.getUserId(),2,7);
					map.put("infoFeeShipperBubble",infoFeeShipperBubble+"");
				} else if ("9".equals(carType)) {
					//将购买货运险的人数提示放入到map集合中
					putBuyerNumTips(map);
				}
				//app我的发货菜单状态
				TytUserSub tytUserSub=tytUserSubService.getTytUserSubByUserId(baseParameter.getUserId());
				if(tytUserSub!=null){
					map.put("myGoodsMenu",String.valueOf(tytUserSub.getMyGoodsMenu()==null?2:tytUserSub.getMyGoodsMenu()));
				}else{
					map.put("myGoodsMenu","2");
				}
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				if (map.size() > 0)
					rm.setData(map);
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			ex.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;

	}
    
	/**
	 * @Description  将购买货运险的人数提示放入到map集合中
	 * <AUTHOR>
	 * @Date  2019/5/20 15:30
	 * @Param [map]
	 * @return void
	 **/
	public void putBuyerNumTips(Map<String, String> map){
		//获取购买过货运险的人数
		Integer buyerNum = (Integer) cacheService.getObject(Constant.CACHE_BUY_TRANSPORT_INSURANCE_NUM);
		//购买货运险人数提示语模板
		String buyerNumTips = tytConfigService.getStringValue("buyer_insurance_num_tips");
		if(buyerNum == null || buyerNum.intValue() < 0){
			buyerNum = 0;
		}
		buyerNumTips = StringUtils.replaceEach(buyerNumTips, new String[]{"${buyerNum}"}, new String[]{buyerNum.toString()});
		if(StringUtils.isNotBlank(buyerNumTips)) {
			map.put("buy_transport_insurance_num",buyerNumTips);
		}
	}

	/**
	 * 获取新招聘求职数量
	 * 
	 * @param baseParameter
	 * @param carType
	 * @param type
	 * @param bRecruitSortId
	 * @param bJobSortId
	 * @param sRecruitSortId
	 * @param sJobSortId
	 * @return
	 */
	@RequestMapping("/newJobNbr")
	@ResponseBody
	public ResultMsgBean newJobNbr(BaseParameter baseParameter, String carType, String type, Long bRecruitSortId, Long bJobSortId, Long sRecruitSortId, Long sJobSortId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (carType == null || "".equals(carType)) {

				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("carType不能为空");
			} else if (type == null || "".equals(type)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("type不能为空");
			} else {
				Map<String, Integer> map = getNewJobNbr(carType, type, bRecruitSortId, bJobSortId, sRecruitSortId, sJobSortId);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				if (map.size() > 0)
					rm.setData(map);
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;

	}

	public Map<String, Integer> getNewJobNbr(String carType, String type, Long bRecruitSortId, Long bJobSortId, Long sRecruitSortId, Long sJobSortId) {
		Map<String, Integer> map = new HashMap<String, Integer>();
		if ("1".equals(carType)) {
			if ("1".equals(type)) {
				int c = bCarRecruitService.getNewPublishaNbr(bRecruitSortId);
				map.put("bRecruitNbr", c);
			} else if ("2".equals(type)) {
				int c = bCarJobService.getNewPublishaNbr(bJobSortId);
				map.put("bJobNbr", c);
			} else {
				int c = bCarRecruitService.getNewPublishaNbr(bRecruitSortId);
				map.put("bRecruitNbr", c);
				int n = bCarJobService.getNewPublishaNbr(bJobSortId);
				map.put("bJobNbr", n);
			}
		} else if ("2".equals(carType)) {
			// 设备
			if ("1".equals(type)) {
				int c = sCarRecruitService.getNewPublishaNbr(sRecruitSortId);
				map.put("sRecruitNbr", c);
			} else if ("2".equals(type)) {
				int c = sCarJobService.getNewPublishaNbr(sJobSortId);
				map.put("sJobNbr", c);
			} else {
				int c = sCarRecruitService.getNewPublishaNbr(sRecruitSortId);
				map.put("sRecruitNbr", c);
				int n = sCarJobService.getNewPublishaNbr(sJobSortId);
				map.put("sJobNbr", n);
			}
		} else {
			if ("1".equals(type)) {
				int c = bCarRecruitService.getNewPublishaNbr(bRecruitSortId);
				map.put("bRecruitNbr", c);
			} else if ("2".equals(type)) {
				int c = bCarJobService.getNewPublishaNbr(bJobSortId);
				map.put("bJobNbr", c);
			} else {
				int c = bCarRecruitService.getNewPublishaNbr(bRecruitSortId);
				map.put("bRecruitNbr", c);
				int n = bCarJobService.getNewPublishaNbr(bJobSortId);
				map.put("bJobNbr", n);
			}
			// 设备
			if ("1".equals(type)) {
				int c = sCarRecruitService.getNewPublishaNbr(sRecruitSortId);
				map.put("sRecruitNbr", c);
			} else if ("2".equals(type)) {
				int c = sCarJobService.getNewPublishaNbr(sJobSortId);
				map.put("sJobNbr", c);
			} else {
				int c = sCarRecruitService.getNewPublishaNbr(sRecruitSortId);
				map.put("sRecruitNbr", c);
				int n = sCarJobService.getNewPublishaNbr(sJobSortId);
				map.put("sJobNbr", n);
			}
		}
		return map;
	}

	@RequestMapping("collect")
	@ResponseBody
	public ResultMsgBean collect(String userId, String id, String type) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(id)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职信息id不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(type) || (!"1".equals(type) && !"2".equals(type))) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("操作类型type不能为空且只能1和2");
				return rm;
			}
			// 检测用户是否已经收藏了该求职信息
			boolean isHasCollected = bCarJobService.isHasColleced(userId, id, 2);
			/*
			 * 1：收藏 2：取消收藏
			 */
			switch (Integer.valueOf(type)) {
			case 1:
				if (isHasCollected) {
					rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
					rm.setMsg("已收藏过该求职信息，不能重复收藏");
				} else {
					// 保存收藏求职信息
					bCarJobService.saveCollect(userId, id, 2);
				}
				break;
			case 2:
				if (!isHasCollected) {
					rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
					rm.setMsg("您还未收藏该求职信息，不能取消收藏");
				} else {
					// 取消收藏求职信息
					bCarJobService.deleteCollect(userId, id, 2);
				}
				break;
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}

	@RequestMapping("changeStatus")
	@ResponseBody
	public ResultMsgBean changeStatus(String userId, String id, String type) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(id)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("信息id不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(type) || (!"1".equals(type) && !"2".equals(type))) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("操作类型type不能为空且只能1和2");
				return rm;
			}
			// 查询板车求职信息是否属于用户
			boolean isBlongUser = bCarJobService.isBlongUser(userId, id);
			if (isBlongUser) {
				// 获取板车求职信息是否处于开放状态
				boolean isOpen = bCarJobService.isJobOpen(id, JOB_STATUS_OPEN);
				/*
				 * 1：开放 2：关闭
				 */
				switch (Integer.valueOf(type)) {
				case 1:
					if (isOpen) {
						rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
						rm.setMsg("该信息已经处于开放状态");
					} else {
						// 开放信息
						bCarJobService.updateJobStatus(id, JOB_STATUS_OPEN);
					}
					break;
				case 2:
					if (!isOpen) {
						rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
						rm.setMsg("该信息已经处于关闭状态");
					} else {
						// 关闭信息
						bCarJobService.updateJobStatus(id, JOB_STATUS_CLOSE);
					}
					break;
				}
			} else {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("只能操作自己发布的信息");
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}

	@RequestMapping("mulUncollect")
	@ResponseBody
	public ResultMsgBean mulUncollect(String userId, String ids) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(ids)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("信息ids不能为空");
				return rm;
			}
			/*
			 * ids如果为-1则取消全部，否则按照指定的id集合进行操作
			 */
			if ("-1".equals(ids)) {
				sCarJobService.updateCollectStatus(userId, BCAR_STATUS);
			} else {
				// 按照id集合取消收藏
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}
}
