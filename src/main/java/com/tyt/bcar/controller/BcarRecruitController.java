package com.tyt.bcar.controller;

import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import cn.hutool.json.JSONUtil;
import com.tyt.user.service.TytConfigService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.bcar.bean.BcarRecruitBean;
import com.tyt.bcar.bean.BcarRecruitQueryBean;
import com.tyt.bcar.service.BcarJobService;
import com.tyt.bcar.service.BcarRecruitService;
import com.tyt.bcar.service.BcarRecruitUserService;
import com.tyt.bcar.util.BcarUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytBcarRecruit;
import com.tyt.scar.service.ScarJobService;
import com.tyt.util.ReturnCodeConstant;

@Controller
@RequestMapping("/plat/bcar/recruit")
public class BcarRecruitController extends BaseController {
	private static final int RECRUIT_STATUS_OPEN = 0;
	private static final int RECRUIT_STATUS_CLOSE = 1;
	private static final int BRECRUIT_STATUS = 3;
	@Resource(name = "sCarJobService")
	ScarJobService sCarJobService;
	@Resource(name = "bCarRecruitService")
	BcarRecruitService bCarRecruitService;
	@Resource(name = "bCarRecruitUserService")
	BcarRecruitUserService bCarRecruitUserService;
	@Resource(name = "bCarJobService")
	BcarJobService bCarJobService;
	@Resource(name = "tytConfigService")
	TytConfigService tytConfigService;

	/**
	 * APP查询招聘列表接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter, Integer queryType, Long querySign, String province, String city, String county, String dutyCode) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
				List<BcarRecruitQueryBean> list = bCarRecruitService.query(province, city, county, dutyCode, queryType.intValue(), querySign == null ? 0 : querySign.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * APP我的招聘列表接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/myPublish")
	@ResponseBody
	public ResultMsgBean myPublish(BaseParameter baseParameter, Integer queryType, Long querySign) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (checkQueryParameter(queryType, querySign, rm)) {
				List<BcarRecruitQueryBean> list = bCarRecruitService.myBcarRecruitList(baseParameter.getUserId(), queryType.intValue(), querySign == null ? 0 : querySign.longValue());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("查询成功");
				rm.setData(list);
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	private boolean checkQueryParameter(Integer queryType, Long querySign, ResultMsgBean rm) {
		if (queryType == null || (queryType.intValue() != 0 && queryType.intValue() != 1 && queryType.intValue() != 2)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询类型不正确！");
			return false;
		} else if (queryType.intValue() != 1 && (querySign == null || querySign.longValue() < 0)) {
			rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
			rm.setMsg("查询标识错误，最大、最小ID为空！");
			return false;
		}
		return true;

	}

	/**
	 * APP保存招聘信息
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/save")
	@ResponseBody
	public ResultMsgBean saveBcarRecruit(BaseParameter baseParameter, TytBcarRecruit bCar) {
		ResultMsgBean rm = new ResultMsgBean();
		logger.info("saveBcarRecruit", JSONUtil.toJsonStr(bCar));
		try {
			String stringValue = tytConfigService.getStringValue("bcar_recruit_open_status");
			if(org.apache.commons.lang3.StringUtils.isNotBlank(stringValue)){
				if(bCar != null && bCar.getUserId() != null){
					if(stringValue.contains(bCar.getUserId().toString())){
						rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
						rm.setMsg("您已被禁止发布此类消息");
						return rm;
					}
				}
			}
			// 必填项验证
			// if (bCar.getCarTypeCode() == null) {
			// rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
			// rm.setMsg("carTypeCode不能为空");
			// return rm;
			// }
			if (!BcarUtil.validateParams(rm, bCar.getTitle(), bCar.getTelName(), bCar.getDutyCode(), bCar.getProvince(), bCar.getCity(), bCar.getCounty(), bCar.getSalaryCode(), bCar.getTelephone()))
				return rm;
			if (bCarRecruitService.addBcarRecruit(bCar, baseParameter.getClientSign(), baseParameter.getClientVersion())) {
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("发布成功");
				return rm;
			} else {
				rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
				rm.setMsg("您已经发布过此条信息!");
				return rm;
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * APP编辑招聘
	 * 
	 * @param baseParameter
	 * @param bCar
	 * @return
	 */
	@RequestMapping("/update")
	@ResponseBody
	public ResultMsgBean updateBcarRecruit(BaseParameter baseParameter, TytBcarRecruit bCar) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			String stringValue = tytConfigService.getStringValue("bcar_recruit_open_status");
			if(org.apache.commons.lang3.StringUtils.isNotBlank(stringValue)){
				if(bCar != null && bCar.getUserId() != null){
					if(stringValue.contains(bCar.getUserId().toString())){
						rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
						rm.setMsg("您已被禁止发布此类消息");
						return rm;
					}
				}
			}
			// 必填项验证
			if (bCar.getId() == null || bCar.getId().longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("招聘id不能为空");
				return rm;
			}
			// 老数据
			TytBcarRecruit bCarOld = bCarRecruitService.getById(bCar.getId());
			if (bCarOld == null) {
				rm.setCode(ReturnCodeConstant.OBJECT_IS_NOT_EXIT_CODE);
				rm.setMsg("非法信息编辑");
				return rm;
			}
			// 必填项验证
			if (!BcarUtil.validateParams(rm, bCar.getTitle(), bCar.getTelName(), bCar.getDutyCode(), bCar.getProvince(), bCar.getCity(), bCar.getCounty(), bCar.getSalaryCode(), bCar.getTelephone()))
				return rm;
			if (bCarRecruitService.updateBcarJob(bCarOld, bCar, baseParameter.getClientSign(), baseParameter.getClientVersion())) {
				// 更新认证状态和开放状态
				bCarRecruitService.updateVerifyAndOpenStatus(bCar.getId());
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("修改成功");
				return rm;
			} else {
				rm.setCode(ReturnCodeConstant.DATA_HAS_EXIT);
				rm.setMsg("您已经发布过此条信息!");
				return rm;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}

	}

	/**
	 * APP查询招聘详情
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/info")
	@ResponseBody
	public ResultMsgBean getBcarRecruitInfo(Long id, Long userId) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (id == null || id.longValue() <= 0l) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("招聘id不能为空");
				return rm;
			}
			// 查询信息
			BcarRecruitBean bCarRecruit = bCarRecruitService.updateRead(id, userId);
			boolean isHasCollected = bCarJobService.isHasColleced(userId + "", id + "", 3);
			bCarRecruit.setIsCollect(isHasCollected ? 0 : 1);
			// 查询用户在30天内是否发布过板车招聘信息
			boolean isHasPublished = bCarRecruitService.isHasPublishedInDays(userId, 30);
			bCarRecruit.setHasPublished(isHasPublished ? 1 : 2);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("获取成功");
			rm.setData(bCarRecruit);
			return rm;
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
	}

	/**
	 * APP删除招聘信息
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/del")
	@ResponseBody
	public ResultMsgBean del(BaseParameter baseParameter, Long id) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (id != null && id.intValue() > 0) {
				bCarRecruitService.delTytBcarRecruit(id);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("删除成功");
			} else {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("id 不能为空");
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * App招聘置顶
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/top")
	@ResponseBody
	public ResultMsgBean top(BaseParameter baseParameter, Long id) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (id != null && id.intValue() > 0) {

				Map<String, Long> map = bCarRecruitService.updateTop(id);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("信息排位已经提前");
				rm.setData(map);
			} else {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("id 不能为空");
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	/**
	 * APP招聘设置已读
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping("/setRead")
	@ResponseBody
	public ResultMsgBean setRead(BaseParameter baseParameter, Long id) {
		ResultMsgBean rm = new ResultMsgBean();
		try {
			// 检查属性
			if (id != null && id.intValue() > 0) {
				bCarRecruitService.updateSetRead(baseParameter.getUserId(), id);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("操作成功");
			} else {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("id 不能为空");
			}

		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}

	@RequestMapping("collect")
	@ResponseBody
	public ResultMsgBean collect(String userId, String id, String type) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(id)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("求职信息id不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(type) || (!"1".equals(type) && !"2".equals(type))) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("操作类型type不能为空且只能1和2");
				return rm;
			}
			// 检测用户是否已经收藏了该求职信息
			boolean isHasCollected = bCarJobService.isHasColleced(userId, id, 3);
			/*
			 * 1：收藏 2：取消收藏
			 */
			switch (Integer.valueOf(type)) {
			case 1:
				if (isHasCollected) {
					rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
					rm.setMsg("已收藏过该求职信息，不能重复收藏");
				} else {
					// 保存收藏求职信息
					bCarJobService.saveCollect(userId, id, 3);
				}
				break;
			case 2:
				if (!isHasCollected) {
					rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
					rm.setMsg("您还未收藏该求职信息，不能取消收藏");
				} else {
					// 取消收藏求职信息
					bCarJobService.deleteCollect(userId, id, 3);
				}
				break;
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}

	@RequestMapping("changeStatus")
	@ResponseBody
	public ResultMsgBean changeStatus(String userId, String id, String type) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(id)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("信息id不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(type) || (!"1".equals(type) && !"2".equals(type))) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("操作类型type不能为空且只能1和2");
				return rm;
			}
			// 查询板车招聘信息是否属于用户
			boolean isBlongUser = bCarRecruitService.isBlongUser(userId, id);
			if (isBlongUser) {
				// 获取板车招聘信息是否处于开放状态
				boolean isOpen = bCarRecruitService.isRecruitOpen(id, RECRUIT_STATUS_OPEN);
				/*
				 * 1：开放 2：关闭
				 */
				switch (Integer.valueOf(type)) {
				case 1:
					if (isOpen) {
						rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
						rm.setMsg("该信息已经处于开放状态");
					} else {
						// 开放信息
						bCarRecruitService.updateRecruitStatus(id, RECRUIT_STATUS_OPEN);
					}
					break;
				case 2:
					if (!isOpen) {
						rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
						rm.setMsg("该信息已经处于关闭状态");
					} else {
						// 关闭信息
						bCarRecruitService.updateRecruitStatus(id, RECRUIT_STATUS_CLOSE);
					}
					break;
				}
			} else {
				rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
				rm.setMsg("只能操作自己发布的信息");
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}

	@RequestMapping("mulUncollect")
	@ResponseBody
	public ResultMsgBean mulUncollect(String userId, String ids) {
		ResultMsgBean rm = new ResultMsgBean();
		rm.setCode(ReturnCodeConstant.OK);
		rm.setMsg("操作成功");
		try {
			if (StringUtils.isEmpty(userId)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("userId不能为空");
				return rm;
			}
			if (StringUtils.isEmpty(ids)) {
				rm.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				rm.setMsg("信息ids不能为空");
				return rm;
			}
			/*
			 * ids如果为-1则取消全部，否则按照指定的id集合进行操作
			 */
			if ("-1".equals(ids)) {
				sCarJobService.updateCollectStatus(userId, BRECRUIT_STATUS);
			} else {
				// 按照id集合取消收藏
			}
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
			return rm;
		}
		return rm;
	}
}
