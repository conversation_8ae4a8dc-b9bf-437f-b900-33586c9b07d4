package com.tyt.invoicetransport.bean;

import com.tyt.plat.entity.base.TytInvoiceTransportConfigLog;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

@Data
public class TytInvoiceTransportConfigLogDTO extends TytInvoiceTransportConfigLog implements Serializable {

    @Transient
    private Integer operationType;

    private List<TytInvoiceTransportPriceConfig> tytInvoiceTransportPriceConfigList;

}