package com.tyt.invoicetransport.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TytInvoiceTransportPriceConfig implements Serializable {

    /**
     * 吨位高值
     */
    private Integer highTonnageValue;

    /**
     * 吨位低值
     */
    private Integer lowTonnageValue;

    /**
     * 距离高值
     */
    private Integer highDistanceValue;

    /**
     * 距离低值
     */
    private Integer lowDistanceValue;

    /**
     * 运费上线（元）
     */
    private Integer maxPrice;

    /**
     * 车公里单价（元）
     */
    private Integer maxDistanceUnitPrice;

}