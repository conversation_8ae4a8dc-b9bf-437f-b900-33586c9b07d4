package com.tyt.invoicetransport.service;

import com.tyt.invoicetransport.bean.IsInvoiceTransportVO;
import com.tyt.invoicetransport.bean.TytInvoiceTransportConfigLogDTO;
import com.tyt.invoicetransport.bean.TytInvoiceTransportEnterpriseConfigLogDTO;
import com.tyt.invoicetransport.bean.TytInvoiceTransportPriceConfig;
import com.tyt.invoicetransportconfiglog.service.InvoiceTransportConfigService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.User;
import com.tyt.plat.entity.base.*;
import com.tyt.plat.mapper.base.TytInvoiceEnterpriseMapper;
import com.tyt.plat.mapper.base.TytOnlineFreightEnterpriseMapper;
import com.tyt.plat.mapper.base.TytTransportOrdersMapper;
import com.tyt.plat.service.base.AbtestService;
import com.tyt.plat.service.user.ApiInvoiceEnterpriseService;
import com.tyt.plat.vo.map.TytAbtestConfigVo;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class InvoiceTransportServiceImpl implements InvoiceTransportService {

    @Autowired
    private InvoiceTransportConfigService invoiceTransportConfigService;

    @Autowired
    private TytTransportOrdersMapper tytTransportOrdersMapper;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

    @Autowired
    private TytOnlineFreightEnterpriseMapper tytOnlineFreightEnterpriseMapper;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private AbtestService abtestService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private ApiInvoiceEnterpriseService apiInvoiceEnterpriseService;

    private static final BigDecimal ONE_HUNDRED_BIGDECIMAL = new BigDecimal(100);

    private static final BigDecimal DEFAULT_ENTERPRISE_TAX_RATE_BIGDECIMAL = new BigDecimal(6.6);

    private static final String PARAM_ERROR_MSG = "请求参数错误";

    private static final String ENTERPRISE_ERROR_MSG = "未通过企业认证不可发布“专票”货源，如有需要，可联系客服获取帮助";

    @Override
    public ResultMsgBean checkUserCanPublishInvoiceTransport(Long userId, Long invoiceSubjectId) {
        if (userId == null) {
            return ResultMsgBean.failResponse(90001, PARAM_ERROR_MSG);
        }

        TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(userId);

        if (tytInvoiceEnterprise == null) {
            return ResultMsgBean.failResponse(90004, "发布专票货源，需先完成企业认证、企业签约");
        }

        if (invoiceSubjectId == null) {
            //用户未选择开票主体，因为6680app会自动帮用户选中一个开票主体并且调用校验接口，所有没传开票主体表示用户根本就没有开票主体
            return getFailResponseBasedOnVerifyStatus(tytInvoiceEnterprise);
        } else {
            BigDecimal rate;
            //选了开票主体
            try {
                rate = apiInvoiceEnterpriseService.invoiceEnterpriseGetTaxRate(userId, invoiceSubjectId);
            } catch (Exception e) {
                return ResultMsgBean.failResponse(90002, e.getMessage());
            }
            if (rate == null) {
                return ResultMsgBean.failResponse(90002, "您的企业账户当前无法发布“专票”货源，您可以选择不开发票继续发货，如需帮助请联系客服");
            }
            return checkAllNoFreezeOrdersPriceCountIsRules(tytInvoiceEnterprise, rate);
        }

    }

    @Override
    public ResultMsgBean checkUserCanPublishInvoiceTransportNextPageBtn(Long userId, Long invoiceSubjectId, Boolean specialCar, BigDecimal distance, String serviceProviderCode, String clientVersion) {
        if (userId == null) {
            return ResultMsgBean.failResponse(90001, PARAM_ERROR_MSG);
        }

        if (new BigDecimal(clientVersion).compareTo(new BigDecimal("6630")) >= 0) {
            if (distance == null || distance.compareTo(BigDecimal.ZERO) < 0) {
                return ResultMsgBean.failResponse(90001, "正在获取运距，请稍后再试");
            }
        }


        TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(userId);

        if (tytInvoiceEnterprise == null) {
            return ResultMsgBean.failResponse(90001, ENTERPRISE_ERROR_MSG);
        }

        if (specialCar != null && specialCar && invoiceSubjectId != null) {
            //专车开票货源不可选择甘肃网货主体
            String invoiceSujectData = tytConfigService.getStringValue("invoice_subject_data", "1,JCZY");
            String invoiceSujectDataXhl = tytConfigService.getStringValue("invoice_subject_data_xhl", "9,XHL");
            String[] split = invoiceSujectData.split(",");
            if (invoiceSubjectId == Integer.parseInt(split[0])) {
                return ResultMsgBean.failResponse(90001, "专车暂不支持甘肃网货开票主体，请在发票类型中更换");
            }
            split = invoiceSujectDataXhl.split(",");
            if (invoiceSubjectId == Integer.parseInt(split[0])) {
                return ResultMsgBean.failResponse(90001, "专车暂不支持翔和翎开票，请更换专票主体");
            }
        }

        if (StringUtils.isNotBlank(serviceProviderCode) && serviceProviderCode.equals("HBWJ")) {
            String invoiceDistanceRule = tytConfigService.getStringValue("invoice_distance_rule", "20");
            if (distance != null &&  distance.compareTo(new BigDecimal(invoiceDistanceRule)) < 0) {
                return ResultMsgBean.failResponse(90001, "装货地到卸货地小于" + invoiceDistanceRule + "公里，不符合当前税局开票要求，请修正。");
            }
        }
//        String invoiceSujectData = tytConfigService.getStringValue("invoice_subject_data", "1,JCZY");
//        String[] split = invoiceSujectData.split(",");
//        BigDecimal rate = apiInvoiceEnterpriseService.invoiceEnterpriseGetTaxRate(userId, Long.valueOf(split[0]));
        return checkAllNoFreezeOrdersPriceCountIsRules(tytInvoiceEnterprise, new BigDecimal("6.6"));
    }

    @Override
    public ResultMsgBean getAdditionalPriceAndEnterpriseTaxRate(Long userId, String price, Long invoiceSubjectId) {
        if (userId == null || invoiceSubjectId == null) {
            return ResultMsgBean.failResponse(90001, PARAM_ERROR_MSG);
        }

        HashMap<String, String> result = new HashMap<>();

        BigDecimal enterpriseTaxRate = DEFAULT_ENTERPRISE_TAX_RATE_BIGDECIMAL;

        //调用接口根据userId和invoiceSubjectId获取开票费率
        BigDecimal rate = apiInvoiceEnterpriseService.invoiceEnterpriseGetTaxRate(userId, invoiceSubjectId);
        if (rate != null) {
            enterpriseTaxRate = rate;
        }
        result.put("enterpriseTaxRate", enterpriseTaxRate.toString());

        if (StringUtils.isBlank(price)) {
            return ResultMsgBean.successResponse(result);
        }

        int priceInt;
        try {
            priceInt = Integer.parseInt(price);
        } catch (NumberFormatException e) {
            //price参数非数字
            return ResultMsgBean.successResponse(result);
        }

        BigDecimal additionalPrice;

        try {
            additionalPrice = calculateAdditionalFreight(enterpriseTaxRate, new BigDecimal(priceInt));
        } catch (IllegalArgumentException e) {
            return ResultMsgBean.failResponse(90001, e.getMessage());
        }

        result.put("additionalPrice", additionalPrice.toString());

        return ResultMsgBean.successResponse(result);
    }

    @Override
    public ResultMsgBean checkUserCanPublishInvoiceTransportInDoPublish(Long userId, Long invoiceSubjectId) {
        if (userId == null || invoiceSubjectId == null) {
            return ResultMsgBean.failResponse(90001, PARAM_ERROR_MSG);
        }

        TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(userId);

        if (tytInvoiceEnterprise == null) {
            return ResultMsgBean.failResponse(90001, ENTERPRISE_ERROR_MSG);
        }

        //校验ab测试
        if (!checkInvoiceTypeAbtestByUserId(userId)) {
            return ResultMsgBean.failResponse(90002, "您的企业账户当前无法发布“专票”货源，您可以选择不开发票继续发货，如需帮助请联系客服");
        }

        if (StringUtils.isNotBlank(tytInvoiceEnterprise.getEnterpriseName())) {
            TytOnlineFreightEnterprise onlineFreightEnterprise = tytOnlineFreightEnterpriseMapper.getOnlineFreightEnterpriseByName(tytInvoiceEnterprise.getEnterpriseName());
            if (onlineFreightEnterprise != null) {
                return ResultMsgBean.failResponse(90001, "认证企业为网货平台，根据国家网络货运管理条例，网货平台不得相互委托业务");
            }
        }

        //调用接口根据userId和invoiceSubjectId判断用户是否可以发开票货源
        BigDecimal rate;
        try {
            rate = apiInvoiceEnterpriseService.invoiceEnterpriseGetTaxRate(userId, invoiceSubjectId);
        } catch (Exception e) {
            return ResultMsgBean.failResponse(90002, e.getMessage());
        }
        if (rate == null) {
            return ResultMsgBean.failResponse(90002, "您的企业账户当前无法发布“专票”货源，您可以选择不开发票继续发货，或返回上一页修改信息，如需帮助请联系客服");
        }

        return ResultMsgBean.successResponse();
    }

    @Override
    public ResultMsgBean checkUserCanPublishInvoiceTransportInDoSaveDirect(Long userId, Long invoiceSubjectId) {
        if (userId == null) {
            return ResultMsgBean.failResponse(90001, PARAM_ERROR_MSG);
        }

        TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(userId);

        if (tytInvoiceEnterprise == null) {
            return ResultMsgBean.failResponse(90001, ENTERPRISE_ERROR_MSG);
        }

        //校验ab测试
//        if (!checkInvoiceTypeAbtestByUserId(userId)) {
//            return ResultMsgBean.failResponse(90002, "您的企业账户当前无法发布“专票”货源，您可以选择不开发票继续发货，如需帮助请联系客服");
//        }

        if (StringUtils.isNotBlank(tytInvoiceEnterprise.getEnterpriseName())) {
            TytOnlineFreightEnterprise onlineFreightEnterprise = tytOnlineFreightEnterpriseMapper.getOnlineFreightEnterpriseByName(tytInvoiceEnterprise.getEnterpriseName());
            if (onlineFreightEnterprise != null) {
                return ResultMsgBean.failResponse(90001, "认证企业为网货平台，根据国家网络货运管理条例，网货平台不得相互委托业务");
            }
        }

        //调用接口根据userId和invoiceSubjectId判断用户是否可以发开票货源
        BigDecimal rate;
        try {
            rate = apiInvoiceEnterpriseService.invoiceEnterpriseGetTaxRate(userId, invoiceSubjectId);
        } catch (Exception e) {
            return ResultMsgBean.failResponse(90002, e.getMessage());
        }
        if (rate == null) {
            return ResultMsgBean.failResponse(90002, "您选择的开票主体暂不可用，请选择其他主体");
        }

        return checkAllNoFreezeOrdersPriceCountIsRules(tytInvoiceEnterprise, null);
    }

    @Override
    public IsInvoiceTransportVO isInvoiceTransport(Long srcMsgId) {
        TransportMain transportMaind = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMaind != null && transportMaind.getInvoiceTransport() != null && transportMaind.getInvoiceTransport() == 1) {
            return new IsInvoiceTransportVO(1);
        }
        return new IsInvoiceTransportVO(0);
    }

    @Override
    public ResultMsgBean checkInvoiceTransportParam(Long transportUserId, String distance, String price, String weight) {
        log.info("checkInvoiceTransportParam, transportUserId={}, distance={}, price={}, weight={}", transportUserId, distance, price, weight);
        try {
            TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(transportUserId);
            Long enterpriseId = -1L;
            if (tytInvoiceEnterprise != null && tytInvoiceEnterprise.getId() != null) {
                enterpriseId = tytInvoiceEnterprise.getId();
            }
            TytInvoiceTransportEnterpriseConfigLogDTO lastTytInvoiceTransportEnterpriseConfig = invoiceTransportConfigService.getLastTytInvoiceTransportEnterpriseConfig(enterpriseId,null);
            if (lastTytInvoiceTransportEnterpriseConfig == null) {
                return ResultMsgBean.successResponse();
            }

            if (StringUtils.isNotBlank(lastTytInvoiceTransportEnterpriseConfig.getMaxPrice()) && StringUtils.isNotBlank(price)) {
                BigDecimal maxPrice = new BigDecimal(lastTytInvoiceTransportEnterpriseConfig.getMaxPrice()).multiply(new BigDecimal(10000));
                if (new BigDecimal(price).compareTo(maxPrice) > 0) {
                    return ResultMsgBean.failResponse(90005, "运费超限，最高支持" + lastTytInvoiceTransportEnterpriseConfig.getMaxPrice() + "万元，请修改运费");
                }
            }

            List<TytInvoiceTransportPriceConfig> tytInvoiceTransportPriceConfigs = lastTytInvoiceTransportEnterpriseConfig.getTytInvoiceTransportPriceConfigList();

            if (!CollectionUtils.isEmpty(tytInvoiceTransportPriceConfigs)) {
                for (TytInvoiceTransportPriceConfig config : tytInvoiceTransportPriceConfigs) {
                    ResultMsgBean result = checkInvoiceTransportPriceConfig(config, weight, distance, price);
                    if (result != null) {
                        return result;
                    }
                }
            }
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            log.error("校验开票货源运价出错", e);
            log.error("checkInvoiceTransportParam error transportUserId:{}, distance:{}, price:{}, weight:{}", transportUserId, distance, price, weight, e);
            return ResultMsgBean.successResponse();
        }

    }

    @Override
    public ResultMsgBean checkInvoiceTransportMeasure(Long userId, String length, String width, String height, String weight) {
        TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(userId);
        Long enterpriseId = -1L;
        if (tytInvoiceEnterprise != null && tytInvoiceEnterprise.getId() != null) {
            enterpriseId = tytInvoiceEnterprise.getId();
        }
        TytInvoiceTransportEnterpriseConfigLogDTO lastTytInvoiceTransportEnterpriseConfig = invoiceTransportConfigService.getLastTytInvoiceTransportEnterpriseConfig(enterpriseId,null);
        if (lastTytInvoiceTransportEnterpriseConfig == null) {
            return ResultMsgBean.successResponse();
        }
        StringBuilder allDes = new StringBuilder();
        if ((null != lastTytInvoiceTransportEnterpriseConfig.getMaxLength() && StringUtils.isNotBlank(length)
                && new BigDecimal(length).compareTo(BigDecimal.valueOf(lastTytInvoiceTransportEnterpriseConfig.getMaxLength())) > 0)) {
            allDes.append("长上限为").append(lastTytInvoiceTransportEnterpriseConfig.getMaxLength()).append("米");
        }
        if ((null != lastTytInvoiceTransportEnterpriseConfig.getMaxWidth() && StringUtils.isNotBlank(width)
                && new BigDecimal(width).compareTo(BigDecimal.valueOf(lastTytInvoiceTransportEnterpriseConfig.getMaxWidth())) > 0)) {
            if (allDes.length() != 0) {
                allDes.append("、");
            }
            allDes.append("宽上限为").append(lastTytInvoiceTransportEnterpriseConfig.getMaxWidth()).append("米");
        }
        if ((null != lastTytInvoiceTransportEnterpriseConfig.getMaxHeight() && StringUtils.isNotBlank(height)
                && new BigDecimal(height).compareTo(BigDecimal.valueOf(lastTytInvoiceTransportEnterpriseConfig.getMaxHeight())) > 0)) {
            if (allDes.length() != 0) {
                allDes.append("、");
            }
            allDes.append("高上限为").append(lastTytInvoiceTransportEnterpriseConfig.getMaxHeight()).append("米");
        }
        if ((null != lastTytInvoiceTransportEnterpriseConfig.getMaxTonnage() && StringUtils.isNotBlank(weight)
                && new BigDecimal(weight).compareTo(BigDecimal.valueOf(lastTytInvoiceTransportEnterpriseConfig.getMaxTonnage())) > 0)) {
            if (allDes.length() != 0) {
                allDes.append("、");
            }
            allDes.append("重量上限为").append(lastTytInvoiceTransportEnterpriseConfig.getMaxTonnage()).append("吨");
        }
        if (allDes.length() != 0) {
            return ResultMsgBean.failResponse(10001, "货物信息超限：" + allDes);
        }
        return ResultMsgBean.successResponse();
    }

    @Override
    public void saveInvoiceTransportEnterpriseData(Long userId, Long srcMsgId, Long invoiceSubjectId, String serviceProviderCode, String assignCarTel
            , BigDecimal enterpriseTaxRate, String consigneeName, String consigneeTel, String consigneeEnterpriseName,
                                                   Integer paymentsType,BigDecimal prepaidPrice,BigDecimal collectedPrice,BigDecimal receiptPrice
                                                   ) {
        try {
            log.info("开票货源保存发货时的企业信息 货主ID:{};货源ID:{};开票主题ID:{};服务商code:{};指派车方联系方式:{};收货人姓名:{};收货人联系方式:{};收货人企业名称:{}", userId, srcMsgId, invoiceSubjectId
                    , serviceProviderCode, assignCarTel,  consigneeName, consigneeTel, consigneeEnterpriseName);
            TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(userId);
            TytInvoiceEnterpriseLog tytInvoiceEnterpriseLog = new TytInvoiceEnterpriseLog();
            BeanUtils.copyProperties(tytInvoiceEnterprise, tytInvoiceEnterpriseLog);

            tytInvoiceEnterpriseLog.setInvoiceSubjectId(invoiceSubjectId);
            tytInvoiceEnterpriseLog.setServiceProviderCode(serviceProviderCode);
            tytInvoiceEnterpriseLog.setAssignCarTel(assignCarTel);
            tytInvoiceEnterpriseLog.setEnterpriseTaxRate(enterpriseTaxRate);

            tytInvoiceEnterpriseLog.setConsigneeName(consigneeName);
            tytInvoiceEnterpriseLog.setConsigneeTel(consigneeTel);
            tytInvoiceEnterpriseLog.setConsigneeEnterpriseName(consigneeEnterpriseName);

            tytInvoiceEnterpriseLog.setPrepaidPrice(prepaidPrice);
            tytInvoiceEnterpriseLog.setCollectedPrice(collectedPrice);
            tytInvoiceEnterpriseLog.setReceiptPrice(receiptPrice);
            tytInvoiceEnterpriseLog.setPaymentsType(paymentsType);



            tytInvoiceEnterpriseMapper.deleteInvoiceTransportEnterpriseDataBySrcMsgId(srcMsgId);
            tytInvoiceEnterpriseMapper.saveInvoiceTransportEnterpriseData(tytInvoiceEnterpriseLog, srcMsgId);
        } catch (Exception e) {
            log.error("开票货源保存发货时的企业信息异常", e);
        }
    }

    @Override
    public ResultMsgBean checkConsigneeData(Long userId, String consigneeName, String consigneeTel, String consigneeEnterpriseName) {
        //翔和翎开票校验收货人信息
        if (StringUtils.isBlank(consigneeName) &&  StringUtils.isBlank(consigneeTel) && StringUtils.isBlank(consigneeEnterpriseName)) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "请填写收货人信息");
        }
        try {
            User tytUser = userService.getByUserId(userId);
            if (tytUser != null) {
                if (StringUtils.isNotBlank(tytUser.getCellPhone())
                        && tytUser.getCellPhone().equals(consigneeTel)) {
                    return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "收货联系电话不能与发货人账号一致");
                }
                if (StringUtils.isNotBlank(tytUser.getTrueName())
                        && tytUser.getTrueName().equals(consigneeName)) {
                    return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "收货人姓名不能与发货人姓名一致");
                }
            }

            TytInvoiceEnterprise userEnterpriseData = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(userId);
            if (userEnterpriseData != null
                    && StringUtils.isNotBlank(userEnterpriseData.getEnterpriseName())
                    && consigneeEnterpriseName.equals(userEnterpriseData.getEnterpriseName())) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "收货单位不能与发货人企业认证名称一致");
            }

            if (consigneeName.length() > 20) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "收货人姓名超长");
            }
            if (consigneeTel.length() > 20) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "收货联系电话超长");
            }
            if (consigneeEnterpriseName.length() > 64) {
                return ResultMsgBean.failResponse(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "收货单位超长");
            }

        } catch (Exception e) {
            log.info("XHL校验收货人信息失败 原因：", e);
        }
        return ResultMsgBean.successResponse();
    }

    private ResultMsgBean checkInvoiceTransportPriceConfig(TytInvoiceTransportPriceConfig config, String weight, String distance, String price) {
        BigDecimal parsedWeight = StringUtils.isNotBlank(weight) ? new BigDecimal(weight) : null;

        BigDecimal priceBigDecimal = StringUtils.isNotBlank(price) ? new BigDecimal(price) : null;

        BigDecimal distanceBigDecimal = StringUtils.isNotBlank(distance) ? new BigDecimal(distance) : null;

        if (parsedWeight != null
                && (config.getLowTonnageValue() == null || parsedWeight.compareTo(new BigDecimal(config.getLowTonnageValue())) > 0)
                && (config.getHighTonnageValue() == null || parsedWeight.compareTo(new BigDecimal(config.getHighTonnageValue())) <= 0)
                && priceBigDecimal != null
                && distanceBigDecimal != null
                && (config.getLowDistanceValue() == null || distanceBigDecimal.compareTo(new BigDecimal(config.getLowDistanceValue())) > 0)
                && (config.getHighDistanceValue() == null || distanceBigDecimal.compareTo(new BigDecimal(config.getHighDistanceValue())) <= 0)) {
            if (config.getMaxPrice() == null) {
                if (priceBigDecimal.divide(distanceBigDecimal, 2, RoundingMode.DOWN).compareTo(new BigDecimal(config.getMaxDistanceUnitPrice())) > 0) {
                    return ResultMsgBean.failResponse(90005, "运费过高，请修改运费");
                }
            } else if (config.getMaxDistanceUnitPrice() == null) {
                if (priceBigDecimal.compareTo(new BigDecimal(config.getMaxPrice())) > 0) {
                    return ResultMsgBean.failResponse(90005, "运费过高，请修改运费");
                }
            } else if (priceBigDecimal.compareTo(new BigDecimal(config.getMaxPrice())) > 0
                    && priceBigDecimal.divide(distanceBigDecimal, 2, RoundingMode.DOWN).compareTo(new BigDecimal(config.getMaxDistanceUnitPrice())) > 0) {
                return ResultMsgBean.failResponse(90005, "运费过高，请修改运费");
            }
        }
        return null;
    }

    private boolean checkInvoiceTypeAbtestByUserId(Long userId) {
        List<String> abTestCodeList = new ArrayList<>();
        abTestCodeList.add("invoice_type_abtest");
        List<TytAbtestConfigVo> userTypeList = abtestService.getUserTypeList(abTestCodeList, userId);
        if (CollectionUtils.isEmpty(userTypeList) || userTypeList.get(0).getType() == null || userTypeList.get(0).getType() != 1) {
            return false;
        }
        return true;
    }

    public BigDecimal calculateAdditionalFreight(BigDecimal enterpriseTaxRate, BigDecimal price) {
        enterpriseTaxRate = enterpriseTaxRate.divide(ONE_HUNDRED_BIGDECIMAL);
        if (enterpriseTaxRate.compareTo(BigDecimal.ZERO) <= 0 || enterpriseTaxRate.compareTo(BigDecimal.ONE) >= 0) {
            throw new IllegalArgumentException("企业税率应在0和1之间");
        }
        BigDecimal oneMinusTaxRate = BigDecimal.ONE.subtract(enterpriseTaxRate);
//        return price.divide(oneMinusTaxRate, 0， BigDecimal.ROUND_HALF_UP).subtract(price);
        //两位小数四舍五入
//        return price.divide(oneMinusTaxRate, 2, BigDecimal.ROUND_HALF_UP).subtract(price);
        //两位小数向上取整
        return price.divide(oneMinusTaxRate, 2, BigDecimal.ROUND_UP).subtract(price);
        //0位小数直接向上取整
//        return price.divide(oneMinusTaxRate, 0, BigDecimal.ROUND_UP).subtract(price);
    }

    /**
     * 调用本方法时，tytInvoiceEnterprise企业信息不允许为空，本方法内没有做参数校验
     * @param tytInvoiceEnterprise 企业信息
     * @return 可能的错误信息，如果返回200时将同步返回该货主对应的企业税率
     */
    private ResultMsgBean checkAllNoFreezeOrdersPriceCountIsRules(TytInvoiceEnterprise tytInvoiceEnterprise, BigDecimal rate) {
        TytInvoiceTransportConfigLog lastTytInvoiceTransportConfig = new TytInvoiceTransportConfigLogDTO();
        TytInvoiceTransportEnterpriseConfigLogDTO lastTytInvoiceTransportEnterpriseConfig = invoiceTransportConfigService.getLastTytInvoiceTransportEnterpriseConfig(tytInvoiceEnterprise.getId(),null);
        BeanUtils.copyProperties(lastTytInvoiceTransportEnterpriseConfig, lastTytInvoiceTransportConfig);
        if (lastTytInvoiceTransportConfig.getMaxTotalTransportPrice() == null) {
            return ResultMsgBean.successResponse(rate);
        }

        Integer priceCount = tytTransportOrdersMapper.getAllNoFreezeOrdersPriceCount(tytInvoiceEnterprise.getCertigierUserId(), new Date(Timestamp.valueOf(LocalDateTime.now().minusMonths(12)).getTime()));
        if (priceCount != null && priceCount >= 0 && lastTytInvoiceTransportConfig.getMaxTotalTransportPrice().compareTo(Double.parseDouble(String.valueOf(priceCount / 10000))) < 0) {
            return ResultMsgBean.failResponse(90001, "待支付运费已达到" + lastTytInvoiceTransportConfig.getMaxTotalTransportPrice() + "万元，请先将已有运单运费结算完成，才可以发布专票货源");
        }

        return ResultMsgBean.successResponse(rate);
    }

    private ResultMsgBean getFailResponseBasedOnVerifyStatus(TytInvoiceEnterprise tytInvoiceEnterprise) {
        if (tytInvoiceEnterprise.getInfoVerifyStatus() != null
                && (tytInvoiceEnterprise.getInfoVerifyStatus() == 1 || tytInvoiceEnterprise.getInfoVerifyStatus() == 2)) {
            return ResultMsgBean.failResponse(90003, "发布专票货源，需先完成企业签约");
        }
        return ResultMsgBean.failResponse(90004, "发布专票货源，需先完成企业认证、企业签约");
    }

}
