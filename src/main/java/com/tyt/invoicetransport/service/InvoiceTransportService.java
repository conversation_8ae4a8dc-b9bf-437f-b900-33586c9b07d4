package com.tyt.invoicetransport.service;

import com.tyt.invoicetransport.bean.IsInvoiceTransportVO;
import com.tyt.model.ResultMsgBean;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

public interface InvoiceTransportService {

    /** 选择开专票点击确定时校验规则
     *
     * @param userId 货主ID
     * @return ResultMsgBean
     */
    ResultMsgBean checkUserCanPublishInvoiceTransport(Long userId, Long invoiceSubjectId);


    /** 填写完货物信息点击下一页时校验规则
     *
     * @param userId 货主ID
     * @return ResultMsgBean
     */
    ResultMsgBean checkUserCanPublishInvoiceTransportNextPageBtn(Long userId, Long invoiceSubjectId, Boolean specialCar, BigDecimal distance, String serviceProviderCode, String clientVersion);

    /** 计算附加运费
     *
     * @param userId 货主ID
     * @param price 运费
     * @param invoiceSubjectId 开票平台主体ID
     * @return ResultMsgBean
     */
    ResultMsgBean getAdditionalPriceAndEnterpriseTaxRate(Long userId, String price, Long invoiceSubjectId);

    /** 确认发货时校验规则
     *
     * @param userId 货主ID
     * @param invoiceSubjectId 开票平台主体ID
     * @return ResultMsgBean
     */
    ResultMsgBean checkUserCanPublishInvoiceTransportInDoPublish(Long userId, Long invoiceSubjectId);

    /** z直接发布时校验规则
     *
     * @param userId 货主ID
     * @return ResultMsgBean
     */
    ResultMsgBean checkUserCanPublishInvoiceTransportInDoSaveDirect(Long userId, Long invoiceSubjectId);

    IsInvoiceTransportVO isInvoiceTransport(Long srcMsgId);

    /**
     * 校验开票货源参数限制
     * @param transportUserId
     * @param distance
     * @param price
     * @param weight
     * @return
     */
    ResultMsgBean checkInvoiceTransportParam(Long transportUserId, String distance, String price, String weight);

    /**
     * 校验开票货源参数长宽高重量
     * @param userId userId
     * @param length length
     * @param width width
     * @param height height
     * @param weight weight
     * @return ResultMsgBean
     */
    ResultMsgBean checkInvoiceTransportMeasure(Long userId, String length, String width, String height, String weight);

    //开票货源记录发布时货主的企业信息
    void saveInvoiceTransportEnterpriseData(Long userId, Long srcMsgId, Long invoiceSubjectId, String serviceProviderCode, String assignCarTel
            , BigDecimal enterpriseTaxRate, String consigneeName, String consigneeTel, String consigneeEnterpriseName,Integer paymentsType,BigDecimal prepaidPrice,BigDecimal collectedPrice,BigDecimal receiptPrice);

    ResultMsgBean checkConsigneeData(Long userId, String consigneeName, String consigneeTel, String consigneeEnterpriseName) throws Exception;

}
