package com.tyt.invoicetransport.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.invoicetransport.bean.IsInvoiceTransportVO;
import com.tyt.invoicetransport.service.InvoiceTransportService;
import com.tyt.invoicetransportconfiglog.service.InvoiceTransportConfigService;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytInvoiceEnterprise;
import com.tyt.plat.mapper.base.TytInvoiceEnterpriseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@RequestMapping("/plat/invoice/transport")
@Slf4j
public class InvoiceTransportController {

    @Autowired
    private InvoiceTransportService invoiceTransportService;

    @Autowired
    private TytInvoiceEnterpriseMapper tytInvoiceEnterpriseMapper;

    @Autowired
    private InvoiceTransportConfigService invoiceTransportConfigService;

    @PostMapping(value = "/checkUserCanPublishInvoiceTransport")
    @ResponseBody
    public ResultMsgBean checkUserCanPublishInvoiceTransport(BaseParameter baseParameter, Long invoiceSubjectId) {
        return invoiceTransportService.checkUserCanPublishInvoiceTransport(baseParameter.getUserId(), invoiceSubjectId);
    }

    @PostMapping(value = "/checkUserCanPublishInvoiceTransportNextPageBtn")
    @ResponseBody
    public ResultMsgBean checkUserCanPublishInvoiceTransportNextPageBtn(BaseParameter baseParameter, @RequestParam(value = "invoiceSubjectId", required = false) Long invoiceSubjectId, @RequestParam(value = "specialCar", required = false) Boolean specialCar, @RequestParam(value = "distance", required = false) BigDecimal distance, @RequestParam(value = "serviceProviderCode", required = false) String serviceProviderCode) {
        return invoiceTransportService.checkUserCanPublishInvoiceTransportNextPageBtn(baseParameter.getUserId(), invoiceSubjectId, specialCar, distance, serviceProviderCode, baseParameter.getClientVersion());
    }

    @PostMapping(value = "/getAdditionalPriceAndEnterpriseTaxRate")
    @ResponseBody
    public ResultMsgBean getAdditionalPriceAndEnterpriseTaxRate(BaseParameter baseParameter, String price, Long invoiceSubjectId) {
        return invoiceTransportService.getAdditionalPriceAndEnterpriseTaxRate(baseParameter.getUserId(), price, invoiceSubjectId);
    }

    @PostMapping(value = "/getInvoiceTransportConfig")
    @ResponseBody
    public ResultMsgBean getInvoiceTransportConfig(BaseParameter baseParameter,@RequestParam(value = "serviceProviderCode", required = false) String serviceProviderCode) {
        TytInvoiceEnterprise tytInvoiceEnterprise = tytInvoiceEnterpriseMapper.selectByUserIdNoStatus(baseParameter.getUserId());
        Long enterpriseId = -1L;
        if (tytInvoiceEnterprise != null && tytInvoiceEnterprise.getId() != null) {
            enterpriseId = tytInvoiceEnterprise.getId();
        }
        return ResultMsgBean.successResponse(invoiceTransportConfigService.getLastTytInvoiceTransportEnterpriseConfig(enterpriseId, serviceProviderCode));
    }

    @GetMapping(value = "/isInvoiceTransport")
    @ResponseBody
    public ResultMsgBean isInvoiceTransport(Long srcMsgId) {
        if (srcMsgId == null) {
            return ResultMsgBean.successResponse(new IsInvoiceTransportVO(0));
        }
        return ResultMsgBean.successResponse(invoiceTransportService.isInvoiceTransport(srcMsgId));
    }

}
