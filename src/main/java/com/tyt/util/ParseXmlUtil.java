package com.tyt.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class ParseXmlUtil {
	private static List<String> PROVICES = new ArrayList<String>();
	private static Map<String, List<String>> PROVICES_CITY = new HashMap<String, List<String>>();
	private static Map<String, List<String>> CITY_COUNTRY = new HashMap<String, List<String>>();
	static {
		// 一共三项，第一项是所有的省，第二项是所有省对应的市
		List<Map<String, Object>> allData = parseXml();
		Map<String, Object> allProvices = allData.get(0);
		Map<String, Object> allCities = allData.get(1);
		Map<String, Object> allCountries = allData.get(2);
		Set<String> allProvincesKeys = allProvices.keySet();
		Set<String> allCitiesKeys = allCities.keySet();
		Set<String> allCountriesKeys = allCountries.keySet();
		Iterator<String> iteProvincesKeys = allProvincesKeys.iterator();
		Iterator<String> iteCitiesKeys = allCitiesKeys.iterator();
		Iterator<String> iteCountriesKeys = allCountriesKeys.iterator();
		/*
		 * 获取所有的省，并将其对应的市存储起来
		 */
		String provinceKey;
		String province;
		String cityKey;
		String countryKey;
		String city;
		while (iteProvincesKeys.hasNext()) {
			List<String> cities = new ArrayList<String>();
			provinceKey = iteProvincesKeys.next();
			province = (String) allProvices.get(provinceKey);
			// 将省信息保存起来
			PROVICES.add(province);
			/*
			 * 根据key获取省所有的市
			 */
			while (iteCitiesKeys.hasNext()) {
				List<String> countries = new ArrayList<String>();
				cityKey = iteCitiesKeys.next();
				city = (String) allCities.get(cityKey);
				if (cityKey.startsWith(provinceKey + "-")) {
					cities.add((String) allCities.get(cityKey));
				}
				/*
				 * 获取市所有的县或者区
				 */
				while (iteCountriesKeys.hasNext()) {
					countryKey = iteCountriesKeys.next();
					if (countryKey.startsWith(cityKey + "*")) {
						countries.add((String) allCountries.get(countryKey));
					}
				}
				// 注意重新初始化迭代器
				iteCountriesKeys = allCountriesKeys.iterator();
				// 保存市对应的所有县或区
				CITY_COUNTRY.put(city, countries);
			}
			// 注意重新初始化迭代器
			iteCitiesKeys = allCitiesKeys.iterator();
			// 保存省对应的所有市
			PROVICES_CITY.put(province, cities);
		}
	}

	public static List<String> getAllProvinces() {
		return PROVICES;
	}

	public static List<String> getCityByProvince(String province) {
		return PROVICES_CITY.get(province);
	}

	public static List<String> getTownByCity(String city) {
		return CITY_COUNTRY.get(city);
	}

	public static List<Map<String, Object>> parseXml() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> promap = new HashMap<String, Object>();
		Map<String, Object> citymap = new HashMap<String, Object>();
		Map<String, Object> countymap = new HashMap<String, Object>();
		try {
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = factory.newDocumentBuilder();
			Document document = builder.parse(ParseXmlUtil.class.getResource("/").getPath() + "pcc.xml");

			NodeList provinces = document.getElementsByTagName("pro");

			for (int i = 0; i < provinces.getLength(); i++) {
				Node proNode = provinces.item(i);
				Element proElement = (Element) proNode;
				promap.put(i + "", proElement.getAttribute("pro"));
				NodeList citys = proElement.getChildNodes();
				for (int j = 0; j < citys.getLength(); j++) {
					Node cityNode = citys.item(j);
					if (cityNode.getNodeType() != Node.ELEMENT_NODE)
						continue;
					Element cityElement = (Element) cityNode;
					citymap.put(i + "-" + j + "", cityElement.getAttribute("city"));
					NodeList counties = cityElement.getChildNodes();
					for (int c = 0; c < counties.getLength(); c++) {
						Node countyNode = counties.item(c);
						if (countyNode.getNodeType() != Node.ELEMENT_NODE)
							continue;
						Element countyElement = (Element) countyNode;
						countymap.put(i + "-" + j + "*" + c + "", countyElement.getAttribute("county"));
					}
				}
			}
			list.add(promap);
			list.add(citymap);
			list.add(countymap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
}
