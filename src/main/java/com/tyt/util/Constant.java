package com.tyt.util;

import lombok.Getter;

import java.util.Date;

public class Constant {
	public static final String SESSION_USER = "session.user";
	public static final String SESSION_INFO_USER = "webuser";
	public static final String COOKIE_USER = "ssoInfo";
	public static final String WEB_ACCESS_FAILURE = "访问出错......";
	public static final String WEB_DUPLICATE_INFO = "您今天已经发过此条信息，请重新填写!";
	public static final String PHONE_NOT_OPEN = "手机未开通";
	public static final String MSG_NOT_LOGIN = "未登录，亲！";
	public static final String MSG_LOGOUT_WELCOME = "亲！下班了，欢迎再来";
	public static final String MSG_PASSWORD_ERROR = "密码错误";
	public static final String MSG_USER_NOT_EXIST = "用户不存在";
	public static final String MSG_USER_SIGN_INVALID = "抱歉，您没有后台登陆权限!";
	public static final String MSG_LOGIN_OK = "上班了，亲！";
	public static final String MSG_USER_EDIT_OK = "修改成功";
	public static final String MSG_USER_EDIT_ERROR = "修改失败";
	public static final String MSG_EDIT_OK = "修改成功";
	public static final String MSG_EDIT_ERROR = "修改失败";
	public static final String MSG_SAVE_OK = "添加成功";
	public static final String MSG_SAVE_ERROR = "添加失败";
	public static final String MSG_CACHE_NOT_EXIST = "缓存键值不存在:";
	public static final Integer DEFAULT_PAGE_SIZE = 50;
	public static final Integer DEFAULT_PAGE_SIZE_INFO = 8;
	public static final Integer DEFAULT_PAGE_SIZE_INFO_BLOCK = 7;
	public static final Integer DEFAULT_PAGE_SIZE_PC = 8;
    public static Date TODAY = null;
	public static String SDF_TODAY = null;
	public static int INFO_QUERY_STOP = 0;
	public static int INFO_UPLOAD_STOP_ALL = 0;
	public static int INFO_UPLOAD_STOP_USER = 0;
	public static final String CACHE_INFO_STATUS_SETTING = "INFO_STATUS_";
	public static final String CACHE_INFO_QUERY_STOP_SETTING = CACHE_INFO_STATUS_SETTING + 3;
	public static final String CACHE_INFO_UPLOAD_STOP_ALL_SETTING = CACHE_INFO_STATUS_SETTING + 2;
	public static final String CACHE_INFO_UPLOAD_STOP_USER_SETTING = CACHE_INFO_STATUS_SETTING + 1;
	public static final String CACHE_USER_KEY = "USER_";
	public static final String TICKET_CHANGE_KEY = "ticket:change:key";
	public static final String CACHE_TICKET_KEY = "TICKET_";
	public static final String CACHE_CAR_TICKET_KEY = "login:clientSign:user:car:";
	public static final String CACHE_GOODS_TICKET_KEY = "login:clientSign:user:goods:";
	public static final String CACHE_MINI_PORGRAM_GOODS_TICKET_KEY = "login:clientSign:user:miniprogram:goods:";
	public static final String CACHE_MINI_PORGRAM_TRUCKER_TICKET_KEY = "login:clientSign:user:miniprogram:trucker:";
	/**
	 * 用户当天是否展示提示气泡缓存key前缀
	 */
	public static final String CACHE_HELP_BTN_POPUP_KEY_PREFIX = "help_btn_popup_user_id_";
	/**
	 * 发货帮助提示气泡展示逻辑配置key，【单数,秒数】
	 */
	public static final String HELP_BTN_POPUP_CONFIG = "help_btn_popup_config";
	/**
	 * 发货帮助提示气泡展示逻辑默认配置，【单数,秒数】
	 */
	public static final String POPUP_CONFIG_DEFAULT_VALUE = "3,5";
	/**
	 * 发货二页，订金默认填充金额
	 */
	public static final String DEPOSIT_DEFAULT_VALUE_KEY = "deposit_default_value";
	public static final String DEPOSIT_DEFAULT_VALUE = "100";
	public static final String CACHE_OLD_TICKET_KEY = "old:";
	public static final String CACHE_INFO_CELLPHONE_KEY = "CELLPHONE_";
	public static final String CACHE_INFO_KEY = "INFO_";
	public static final String CACHE_INFO_ENABLE_LIST_KEY = "INFO_ENABLE_LIST";
	public static final String CACHE_INFO_DISABLE_LIST_KEY = "INFO_DISABLE_LIST";
	public static final String CACHE_USER_INFO_ENABLE_LIST_KEY = "UI_ENABLE_LIST_";
	public static final String CACHE_USER_INFO_DISABLE_LIST_KEY = "UI_DISABLE_LIST_";
	public static final String CACHE_TRANSPORT_LIST_KEY = "transport_time_list_";
	public static final String CACHE_BLOCK_KEY = "BLOCK_";
	public static final String CACHE_INFO_QUERY_KEY = "IQ_";
	public static final String CACHE_INFO_QUERY_XXTEA_KEY = "IQ_XT_";
	public static final String CACHE_SESSION_KEY = "SESSION_";
	public static final String CACHE_CONFIG_KEY = "CONFIG_";
	public static final String CACHE_VERSION_KEY = "PLAT_VERSION_KEY_";
	public static final long CACHE_EXPIRE_TIME_NEVER = 0;
	public static final long CACHE_EXPIRE_TIME_2MIN = 120;// 2min
	public static final long CACHE_EXPIRE_TIME_10MIN = 600;// 10min
	public static final long CACHE_EXPIRE_TIME_20MIN = 1200;// 20min
	public static final long CACHE_EXPIRE_TIME_12H = 43200;// 12h
	public static final long CACHE_EXPIRE_TIME_2H = 7200;// 2h
	public static final long CACHE_EXPIRE_TIME_24H = 86400;// 24h
	public static final long CACHE_EXPIRE_TIME_60S = 60;// 60s
	public static final long CACHE_EXPIRE_TIME_30S = 30;// 30s
	public static final long CACHE_EXPIRE_TIME_15S = 15;// 15s
	public static final long CACHE_EXPIRE_TIME_10S = 10;// 10s
	public static final long CACHE_EXPIRE_TIME_5S = 5;// 10s
	public static final int CACHE_EXPIRE_LIMIT = 30 * 24 * 60 * 60;
	public static final int COOKIE_EXPIRE_TIME = 12 * 60 * 60;
    public static final long CACHE_EXPIRE_TIME_48H = 172800;// 48h
	public static final String SESSION_VALIDATE_CODE = "SESSION_VALIDATE_CODE";
	/* 新PC刷新 */
	public static final String PC_SHUAXIN = "FAHUO_SHUAXIN";

	/* 货物信息去重键 */
	@Deprecated
	public static final String CACHE_HASHCODE_KEY = "HASHCODE_";

	/* 推荐货物信息去重键 */
	public static final String CACHE_RECOMMEND_HASHCODE_KEY = "HASHCODE_RECOMMEND_";

	/**
	 * 客户端标识1 PC 2 ANDROID 3 IOS 默认1
	 */
	public static final int PLAT_PC = 1;
	public static final int PLAT_ANDROID = 2;
	public static final int PLAT_IOS = 3;
	public static final int PLAT_APAD = 4;
	public static final int PLAT_IPAD = 5;
	public static final int PLAT_WEB = 6;
	public static final String PRIVATE_KEY_ = "1345~opo-4%";
	// TODO PRIVATE_KEY 需要分开吗？
	public static final String PRIVATE_KEY_PC = "1345~opo-4%";
	public static final String PRIVATE_KEY_ANDROID = "1345~opo-4%";
	public static final String PRIVATE_KEY_IOS = "1345~opo-4%";
	public static final Integer POSITION0 = 0;// 不限
	public static final Integer POSITION1 = 1;// 13.5米大板车司机
	public static final Integer POSITION2 = 2;// 17.5米大板车司机
	public static final Integer POSITION3 = 3;// 6.8米/9.6米单机板车司机
	public static final Integer POSITION4 = 4;// 特种车司机
	public static final Integer POSITION5 = 5;// 临时司机
	public static final Integer POSITION6 = 6;// 其他车辆司机
	// 招聘发布-职务
	public static final Integer DUTY0 = 0;// 不限
	public static final Integer DUTY1 = 1;// 主司机
	public static final Integer DUTY2 = 2;// 副司机
	// 招聘发布-教育信息选项
	public static final Integer EDUCATION0 = 0;// 无要求
	public static final Integer EDUCATION1 = 1;// 高中
	public static final Integer EDUCATION2 = 2;// 技校
	public static final Integer EDUCATION3 = 3;// 中专
	public static final Integer EDUCATION4 = 4;// 大专
	public static final Integer EDUCATION5 = 5;// 本科
	public static final Integer EDUCATION6 = 6;// 其它
	// 招聘发布-工资信息选项
	public static final Integer SALARY0 = 0;// 不限
	public static final Integer SALARY1 = 1;// 面议
	public static final Integer SALARY2 = 2;// 5000以下
	public static final Integer SALARY3 = 3;// 5000-6000
	public static final Integer SALARY4 = 4;// 6000-7000
	public static final Integer SALARY5 = 5;// 7000-8000
	public static final Integer SALARY6 = 6;// 8000以上
	// 招聘发布-驾龄信息选项
	public static final Integer YEARS0 = 0;// 无要求
	public static final Integer YEARS1 = 1;// 5年以下
	public static final Integer YEARS2 = 2;// 5-10年
	public static final Integer YEARS3 = 3;// 10年以上
	// 招聘发布-年龄信息选项
	public static final Integer AGE0 = 0;// 无要求
	public static final Integer AGE1 = 1;// 18-30岁
	public static final Integer AGE2 = 2;// 30-40岁
	public static final Integer AGE3 = 3;// 40-50岁
	public static final Integer AGE4 = 4;// 50岁以上
	// 二手车发布-车名品牌
	public static final Integer CARNAME0 = 0;// 牵引车头搜索-不限
	public static final Integer CARNAME1 = 1;// 牵引头品牌-德龙
	public static final Integer CARNAME2 = 2;// 牵引头品牌-欧曼
	public static final Integer CARNAME3 = 3;// 牵引头品牌-东风
	public static final Integer CARNAME4 = 4;// 牵引头品牌-北奔
	public static final Integer CARNAME5 = 5;// 牵引头品牌-解放
	public static final Integer CARNAME6 = 6;// 牵引头品牌-一汽重卡
	public static final Integer CARNAME7 = 7;// 牵引头品牌-其它
	// 二手车发布-价格区间
	public static final Integer PRICE0 = 0;// 不限
	public static final Integer PRICE1 = 1;// 面议
	public static final Integer PRICE2 = 2;// 2万以下
	public static final Integer PRICE3 = 3;// 2-5万
	public static final Integer PRICE4 = 4;// 5-15万
	public static final Integer PRICE5 = 5;// 15-25万
	public static final Integer PRICE6 = 6;// 25万以上
	// 二手车-是否分期付款
	public static final Integer SUBSECTION0 = 0;// 是否分期
	public static final Integer SUBSECTION1 = 1;// 是
	public static final Integer SUBSECTION2 = 2;// 否
	// 二手车发布-车龄
	public static final Integer CARAGE0 = 0;// 不限
	public static final Integer CARAGE1 = 1;// 1年以下
	public static final Integer CARAGE2 = 2;// 1-2年
	public static final Integer CARAGE3 = 3;// 2-3年
	public static final Integer CARAGE4 = 4;// 3-4年
	public static final Integer CARAGE5 = 5;// 4年以上
	// 信息的截取长度
	public static Integer INSURE_DESCRIBE_LENGTH = 25;
	// 司机招聘，二手车，新车资讯，保险广告信息发布状态
	public static Integer INFO_STATUS_DISABLE = 0;// 无效
	public static Integer INFO_STATUS_WAIT = 1;// 待审核
	public static Integer INFO_STATUS_PASS = 2;// 审核通过
	public static Integer INFO_STATUS_FAILURE = 3;// 审核未通过
	public static Integer INFO_STATUS_OUT = 4;// 无效过期信息
	public static Integer INFO_STATUS_NEVER = 5;// 回收站中删除
	// 身份
	public static Integer IDENTITY0 = 0;// 招聘身份
	public static Integer IDENTITY1 = 1;// 公司
	public static Integer IDENTITY2 = 2;// 个人
	// 性别
	public static Integer SEX0 = 0;// 性别
	public static Integer SEX1 = 1;// 男
	public static Integer SEX2 = 2;// 女

	//用户身份认证标志0未认证1通过2认证中3认证失败
	public static Integer IDENTITY_STATUS_NO = 0;// 未认证
	public static Integer IDENTITY_STATUS_SUCCESS = 1;// 通过
	public static Integer IDENTITY_STATUS_ING = 2;// 认证中
	public static Integer IDENTITY_STATUS_FAIL = 3;// 认证失败
	// 事故历史
	public static Integer HISTORY0 = 0;// 事故历史
	public static Integer HISTORY1 = 1;// 无
	public static Integer HISTORY2 = 2;// 有
	public static Integer CATEGORY0 = 0;// 不限
	public static Integer CATEGORY1 = 1;// 在售车
	public static Integer CATEGORY2 = 2;// 积压车
	public static Integer DISTINGUISH0 = 0;// 不限
	public static Integer DISTINGUISH1 = 1;// 招聘者;代售
	public static Integer DISTINGUISH2 = 2;// 求职者;求购
	public static final Integer INSURECOMPANY0 = 0;// 不限
	public static final Integer INSURECOMPANY1 = 1;// 中国人寿
	public static final Integer INSURECOMPANY2 = 2;// 中国人保
	public static final String SUBSIDY0 = "不限";
	public static final String SUBSIDY1 = "五险一金";
	public static final String SUBSIDY2 = "包吃";
	public static final String SUBSIDY3 = "包住";
	public static final String SUBSIDY4 = "年底双薪";
	public static final String SUBSIDY5 = "周末双休";
	public static final String SUBSIDY6 = "交通补助";
	public static final String SUBSIDY7 = "加班补助";
	public static final String SUBSIDY8 = "餐补";
	public static final String SUBSIDY9 = "话补";
	public static final String SUBSIDY10 = "房补";
	public static final String KIND0 = "不限";
	public static final String KIND1 = "交强险";
	public static final String KIND2 = "第三者责任险";
	public static final String KIND3 = "车辆损失险";
	public static final String KIND4 = "不计免赔特约险";
	public static final String KIND5 = "盗抢险";
	public static final String KIND6 = "车上座位责任险";
	public static final String KIND7 = "玻璃单独破碎险";
	public static final String KIND8 = "自燃险";
	public static final String KIND9 = "新增设备损失险";
	public static final Integer ADVICETITLE0 = 0;// 不限
	public static final Integer ADVICETITLE1 = 1;// 诚信问题
	public static final Integer ADVICETITLE2 = 2;// 软件错误
	public static final Integer ADVICETITLE3 = 3;// 改进建议
	public static final Integer ADVICETITLE4 = 4;// 其它问题
	// 意见反馈审核状态
	public static final Integer ADVICE_STATUS_NO = 1;
	public static final Integer ADVICE_STATUS_YES = 2;
	//  货源类型（电议1，一口价2）
	public static final Integer PUBLISH_TUPE1 = 1;
	public static final Integer PUBLISH_TUPE2 = 2;
	// 黑名单信息审核状态
	public static final Integer BLOCKIFO_STATUS_NO = 1;
	public static final Integer BLOCKIFO_STATUS_YES = 2;
	// 新车类型
	public static final Integer MODEL0 = 0;// 不限
	public static final Integer MODEL1 = 1;// 牵引头
	public static final Integer MODEL2 = 2;// 挂车
	public static final Integer MODEL3 = 3;// 牵引头加挂车
	public static final String CACHE_TYT_JOB_ADD_WEB_KEY = "CACHE_TYT_JOB_ADD_WEB_KEY_";
	// 登录缓存userID 的Key head
	public static final String WEB_CACHE_LOGIN_USER_ID = "WEB_CACHE_LOGIN_USER_ID_";
	// 登录成功后缓存cellphone 时间30分钟
	public static final long WEB_LOGIN_CACHE_TIME = 30 * 60;
	// 收集状态
	public static final Integer COLLECT_STATUS0 = 0;// 不限
	public static final Integer COLLECT_STATUS1 = 1;// 收藏
	public static final Integer COLLECT_STATUS2 = 2;// 浏览
	// 访问状态
	public static final Integer VISITE_STATUS0 = 0;// 不限
	public static final Integer VISITE_STATUS1 = 1;// 已浏览
	// 发布条数
	public static final String CACHE_INFO_NUMBER_KEY = "INFO_NUMBER_";
	// 有效期
	public static final Integer SAVADAY1 = 1;// 剩余天数大于等于1；
	public static final String CACHE_PUBLIC_RESOURCE_KEY = "public_resource_";
	public static final String CACHE_PUBLIC_RESOURCE_PREFIX = "public_resource:";

	/**
	 * 购买货运险成功的人数
	 **/
	public static final String CACHE_BUY_TRANSPORT_INSURANCE_NUM = "buy_transport_insurance_num_";
	/**
	 * 验证码缓存的key
	 */
	public static final String CACHE_VERIFY_CODE_KEY = "verify_code_key_";

	/**
	 * 用户设备基本信息缓存的key
	 */
	public static final String PLAT_USER_HEADER = "plat:user:header:";

	/**
	 * 统计发布中货源查看记录和沟通数,hash结构，后缀今天的日期,eg: plat:count:transports:2019-01-01
	 */
	public static final String PLAT_COUNT_TRANSPORT_KEY = "plat:count:transports:";

	/**
	 * 货源今日查看记录数,hash内数据，后缀货源ID,eg: view:count:5732821
	 */
	public static final String VIEW_COUNT_HASH_KEY = "view:count:";

	/**
	 * 货源今日沟通数,hash内数据，后缀货源ID,eg: contact:count:5732821
	 */
	public static final String CONTACT_COUNT_HASH_KEY = "contact:count:";

	/**
	 * 客户端标识枚举
	 *
	 * <AUTHOR>
	 *
	 */
	public enum ClientSignEnum {

		PC(1),PC_GOODS(12), ANDROID(2), IOS(3), APAD(4), IPAD(5), WEB(6),MINI_PROGRAM(7),
		MANAGER(9), OPENAPI(1001),ANDROID_CAR(21),IOS_CAR(31)
		,ANDROID_GOODS(22),IOS_GOODS(32),WEB_GOODS(62),SPLIT(6000)
		,MINI_PROGRAM_GOODS(72),MINI_PROGRAM_TRUCKER(73),MINI_PROGRAM_DEALER(82),H5_CAR(61),H5_GOODS(71);

		@Getter
		public int code;

		ClientSignEnum(int code) {
			this.code = code;
		}

		public static ClientSignEnum getClientSignEnum(int code) {
			ClientSignEnum[] clientSignEnums = ClientSignEnum.values();
			for (ClientSignEnum clientSignEnum : clientSignEnums) {
				if (clientSignEnum.code == code) {
					return clientSignEnum;
				}
			}
			return ClientSignEnum.PC;
		}

		/**
		 * 判读code是否正确
		 *
		 * @param code
		 * @return
		 */
		public static boolean isClientSignEnumcode(int code) {
			ClientSignEnum[] clientSignEnums = ClientSignEnum.values();
			for (ClientSignEnum clientSignEnum : clientSignEnums) {
				if (clientSignEnum.code == code) {
					return true;
				}
			}
			return false;
		}

		/**
		 * 判断是否车主版
		 * @param code
		 * @return
		 */
		public static boolean isCar(int code){
			return ANDROID_CAR.code == code || IOS_CAR.code == code;
		}
		/**
		 * 判断是否货主版
		 * @param code
		 * @return
		 */
		public static boolean isGoods(int code){
			return ANDROID_GOODS.code == code || IOS_GOODS.code == code;
		}
		/**
		 * 判断是否是pc
		 * @param clientSign
		 * @return
		 */
		public static boolean isNewPc(int clientSign){
			return clientSign == PC.code;
		}

		/**
		 * 校验枚举是否相同
		 * @param reqCode code
		 * @return boolean
		 */
		public boolean codeEqualse(Integer reqCode){
			if(reqCode == null){
				return false;
			}
			boolean result = (reqCode.equals(this.getCode()));

			return result;
		}

	}

    /**
     * 新版本客户端标识
     */
    public enum ClientSignNewEnum {

        ANDROID_CAR(21),IOS_CAR(31),ANDROID_GOODS(22),IOS_GOODS(32),WEB_GOODS(62);
        public int code;

        ClientSignNewEnum(int code) {
            this.code = code;
        }

        /**
         * 判读code是否是拆分后的
         */
        public static boolean isClientSignEnumcode(int code) {
            ClientSignNewEnum[] clientSignEnums = ClientSignNewEnum.values();
            for (ClientSignNewEnum clientSignEnum : clientSignEnums) {
                if (clientSignEnum.code == code) {
                    return true;
                }
            }
            return false;
        }

    }

    public static int isCarOrGoodsOrOrigin(int clientSign){
    	//82 小程序算车主端
        if (clientSign == 21 || clientSign ==31|| clientSign == 82){
            return 1;//车主端
        }else if (clientSign == 22 || clientSign == 32 || clientSign == 62){
            return 2; //货主端
        }else if (clientSign == 72) {
        	return 3; // 小程序货主
		}else if (clientSign == 73) {
			return 4; // 小程序车主
		}else {
            return 0; //原特运通
        }
    }

	public static final String CACHE_TYT_CONFIG_MAP_KEY = "TYT_CONFIG_MAP_";
	public static final String CONFIG_TRANSPORTCOUNTS = "transportPublishCounts";
	/**
	 * 货物信息重发标识
	 */
	public static final String CACHE_TS_REPEAT_KEY = "TS_REPEAT_KEY_";
	// 发货限制规则列表
	public static final String CACHE_STTLIMIT_KEY = "CACHE_STTLIMIT_KEY_";
	// 用户发货条数缓存key
	public static final String CACHE_USERSUB_KEY = "CACHE_USERSUB_KEY_";
	// 用户每天可调用第三方认证次数累加
	public static final String CACHE_VERIFY_NUMBER = "CACHE_VERIFY_NUMBER_";
	// 表名
	public static final String TABLE_BCAR_JOB_NAME = "tyt_bcar_job";
	public static final String TABLE_BCAR_RECRUIT_NAME = "tyt_bcar_recruit";
	public static final String TABLE_SCAR_JOB_NAME = "tyt_scar_job";
	public static final String TABLE_SCAR_RECRUIT_NAME = "tyt_scar_recruit";
	public static final String TABLE_WAYBILL_NAME = "tyt_waybill_nbr";// 运单号
	public static final String TABLE_TRANSPORT_WAYBILL_NAME = "tyt_transport_waybill";// 运单号
	public static final String TABLE_TRANSPORT_ORDERS_NAME = "tyt_transport_orders";// 接单表sort_id
	// 用户身份标签缓存键值
	public static final String CACHE_USER_IDENTITY_LABLES_BCAR = "CACHE_IDENTITY_LABLES_BCAR_";
	public static final String CACHE_USER_IDENTITY_LABLES_SCAR = "CACHE_IDENTITY_LABLES_SCAR_";
	// tyt_source父级-子级KEY
	public static final String CACHE_TYT_SOURCE_PARENT_SUB_KEY_ = "CACHE_TYT_SOURCE_PARENT_SUB_KEY_";
	// 地区码以坐标为key
	public static final String CACHE_TYT_GEO_DICT_PX_PY = "CACHE_TYT_GEO_DICT_PX_PY_";
	// 地区码
	public static final String CACHE_TYT_GEO_DICT = "CACHE_TYT_GEO_DICT_";
	public static final int CACHE_TYT_GEO_DICT_TIME = 30 * 24 * 60 * 60;
	// 强制升级的用户map(以id为键值，以手机号为键值)
	public static final String CACHE_FORCE_UPGRADE_USER_ID_MAP_KEY = "CACHE_FORCE_UPGRADE_USER_ID_MAP_KEY_";
	public static final String CACHE_FORCE_UPGRADE_USER_CELLPHONE_MAP_KEY = "CACHE_FORCE_UPGRADE_USER_CELLPHONE_MAP_KEY_";
	public static final String TYT_CONFIG_FORCE_USER_UPGRADE_KEY = "forceUserUpgradeKey";
	public static final String CACHE_FORCE_UPGRADE_CLIENTID_MAP_KEY = "CACHE_FORCE_UPGRADE_CLIENTID_MAP_KEY_";
	/*
	 * 用户拨打货源电话相关常量
	 */
	public static final Short USER_CLASS_DEFUALT_VALUE = -1;
	public static final Short IDENTITY_TYPE_DEFAULT_VALUE = -1;
	public static final String CALL_PHONE_LIMIT_KEY = "call_phone_limit";
	public static final long CALL_PHONE_TIME_CACHE_TIME = 24 * 60 * 60;
	// 1：未进行车辆认证 2：未进行身份认证 3：试用期用户 4: 缴费到期 5：超过所有限制，即车辆认证，身份认证，缴费的拨打电话次数都已用完
	public static final Short USER_CAR_NO_AUTH = 1;
	public static final Short USER_IDENTITY_NO_AUTH = 2;
	public static final Short USER_NO_PAY = 3;
	public static final Short USER_PAY_OUT_DATE = 4;
	public static final Short USER_EXCEED_ALL_TIME = 5;
	public static final String USER_IDENTITY_NO_AUTH_PROMPT = "糟糕！您还未通过身份认证，不能拨打电话，请您先进行身份认证哦~";
	public static final String USER_CAR_NO_AUTH_PROMPT = "糟糕！您今天的拨打次数已经用完了，悄悄告诉你车辆认证后可以获取更多机会哦~";
	public static final String USER_NO_PAY_PROMPT = "糟糕！您今天的拨打次数已经用完了,成为会员后拥有无限找货机会哦~";
	public static final String USER_PAY_OUT_DATE_PROMPT = "糟糕!您的会员特权到期了,续费后可继续享受无限找货机会哦~";
	public static final String USER_EXCEED_ALL_TIME_PROMPT = "今天的拨打货源条数权限已经用完，请明天再来哦~";
	public static final String MACHINE_TYPE_CACHE_KEY = "machine_type_cache_key";
	public static final int MATCHES_TYPE__CACHE_SIZE = 10;
	public static final String MATCHES_TYPE_NEW_CACHE_SIZE = "machine_type_cache_size_key";
    public static final long MATCHES_TYPE__CACHE_TIME = 60 * 60 * 2;
	public static final Integer COMMON_USER_MACHINE_TYPE_SIZE = 16;

	/**
	 * 高速规则的key
	 */
	public static final String CACHE_HIGHWAY_RULE_KEY = "plat_highway_rule_";

	// 猜你喜欢Redis缓存前缀
	public static final String RECOMMEND_GUESS_LIKE_PREFIX = "recommendGuessLikeKey";
	/**
	 * 心跳服务缓存的key
	 */
	public static final String CACHE_RECOMMEND_HEARTBEARTS_KEY = "platRecommendHeartbeart";
	public static final String RECOMMEND_USER_CALL_TIMES_KEY = "recommendUserCallTimes";
	public static final String RECOMMEND_USER_CALL_KEY = "recommendUserCall";
	public static final int ONE_DAY = 24 * 60 * 60;
	// mq发送延时时间
	public static final String CACHE_RECOMMEND_MQDELAYEDSENDTIME_KEY = "mqDelayedSendTime";
	public static final String RECOMMEND_GRADIENT_DISTANCE_KEY = "recommendGradientDistance";
	public static final String RECOMMEND_DISTANCE_KEY = "recommendCarDistance";
	public static final String RECOMMEND_TYPE_NOT_MATCHE_KEY = "recommendTypeNotMatche";
	public static final String RECOMMEND_FREIGHT_DIFFICULT_KEY = "recommendFreightDifficult";
	public static final String STOP_RECOMMEND_NUM_KEY = "stopRecommendDealFeedbackNum";
	public static final String RECOMMEND_GOOD_ACCOMPLISH_KEY = "recommendGoodAccomplish";
	public static final String RECOMMEND_FORBIDDEN_USERIDS_KEY = "recommend_forbidden_userIds_";
	public static final String GUESS_LIKE_COUNT = "guessLikeCount";
	public static final String SEARCH_KEYWORD_KEY = "seatchKeyword";
	public static final String PROBABLY_STANDARD_KEY = "probablyStandard";
	/*
	 * 0 标准化数据 1 准标准化数据 2 非标准化数据
	 */
	public static final int STANDARD_STATUS_STANDARD = 0;
	public static final int STANDARD_STATUS_PROBABILITY = 1;
	public static final int STANDARD_STATUS_NOT = 2;
	public static final String MACHINE_TYPE_NEW_CACHE_KEY = "machine_type_new_cache_key";
	public static final long MATCHES_TYPE_CACHE_TIME = 60 * 60 * 2;
	public static final String SEARCH_GOOD_NUMBER_KEY = "searchGoodNumberKey";
	public static final String CARDID_PREFIX_BANKNAME = "cardId_prefix_bank_name_";

	public static final String CACHE_MAP_CORRECT_KEYS_UPDATEFLAG_KEY = "gaode_map_correct_keys_update_flag";
	public static final String RECOMMEND_IS_SAVE_IOS_DISTANCE_KEY = "recommendIsSaveAndroidDistance";

	// 用户定向更新数据是否需要更新key 1:不需要更新 2：需要更新 更新完成后设置为1
	public static final String SPECIFIED_USER_UPGRADE_ISUPDATE_FLAG = "common_specified_user_upgrade_isupdate_flag";
	public static final String SPECIFIED_USER_UPGRADE_PREFIX = "commonSpecifiedUserUpgradePrefix";
	public static final int SEARCH_KEYWORD_TYPE_FIRST_PRIORITY = 1;
	public static final int SEARCH_KEYWORD_TYPE_SECOND_PRIORITY = 2;
	public static final int SEARCH_KEYWORD_TYPE_THIRD_PRIORITY = 3;
	public static final int SEARCH_KEYWORD_TYPE_ALL_PRIORITY = 0;

	// 发货找货数量Rediskey
	public static final String CACHE_PROVINCES_PUB_SEEK_TRANS_COUNT_KEY = "provinces_pubseek_trans_count_";
	public static final int CACHE_EXPIRE_TIME_12H_INT = 43200;// 12h
	public static final String FREIGHT_EXPRESSION_MINPROFITRATE = "freight_expression_minprofitrate";// 最低利润率公式
	public static final String PROVINCES_SEEK_FAIL_DEFAULT_GUIDING_RATE = "provinces_seek_fail_default_guiding_profit_rate";//
	public static final String FREIGHT_EXPRESSION_GUIDINGPROFITRATE = "freight_expression_guidingProfitRate_base";// 指导利润率内部公式
	public static final String FREIGHT_EXPRESSION_GUIDINGPROFITRATE_POW = "freight_expression_guidingProfitRate";// 指导利润率外部公式
	public static final String FREIGHT_DISTANCE_DIFF_RANGE = "freight_distance_diff_range";// 指导利润率外部公式
	public static final String REDIS_LOCK_TIMEOUT_KEY = "redis_lock_timeout";

	// 专车匹配零担运费规则吨位阈值
	public static final String SPECIAL_CAR_PRICE_CONFIG_TONNAGE = "special_car_price_config_tonnage";

	// 专车发货入口控制开关配置key
	public static final String ZHUANCHE_PUBLISH_OPEN_CONTROL_CONFIG_KEY = "zhuanche_publish_open_control";
	// 专车发货运费测算交接费配置key
	public static final String ZHUANCHE_PUBLISH_HANDOVER_FEE_CONFIG_KEY = "zhuanche_publish_handover_fee";
	// 专车发货运费测算交接费默认值
	public static final String ZHUANCHE_PUBLISH_HANDOVER_FEE_DEFAULT_VALUE = "36.7";
	// 专车发货入口-开启
	public static final Integer ZHUANCHE_PUBLISH_OPEN_CONTROL_CONFIG_OPEN = 1;

	// 专车运费基础价
	public static final String ZHUANCHE_BASE_PRICE = "410";
	// 专车运费一级阶梯价
	public static final String ZHUANCHE_PRICE_11_5 = "11.5";
	// 专车运费二级阶梯价
	public static final String ZHUANCHE_PRICE_8_5 = "8.5";
	// 专车公里数20
	public static final String ZHUANCHE_DISTANCE_20 = "20";
	// 专车公里数100
	public static final String ZHUANCHE_DISTANCE_100 = "100";
	// 专车公里数150
	public static final String ZHUANCHE_DISTANCE_150 = "150";

	// 获取资源接口地址key 171130
	public static final String RESOURCE_INTERFACE_URL = "resource_interface_url";
	public static final int RESOURCE_URL_CACHE_TIME = 1800;// 0.5H

	public static final int CACHE_EXPIRE_TIME_10MIN_INT = 600;// 10min
	public static final int CACHE_EXPIRE_TIME_5MIN = 300;// 5min

	// 查询合同站内信模板（已生效）
	public static final String ECA_MESSAGE_TEMPLET_FINISH = "eca_message_templet_finish";
	// 查询合同站内信模板（签约中）
	public static final String ECA_MESSAGE_TEMPLET_SIGNING = "eca_message_templet_signing";
	public static final String RECOMMEND_NEED_DIRECT_REPUB = "recommend.need.direct.repub";
	public static final String RECOMMEND_PLAT_RESEND_TIME = "recommend.plat.resend.time";
	public static final String LOCK_CAR_PRE_BASE = "lock.car.preference_";

	//投保攻略点赞用户
	public static final String INSURE_RAIDERS_LIKE_USER="insure_raiders_like_user";
	//新地区码缓存KEY
	public static final String CACHE_TYT_GEO_DICT_NEW = "CACHE_TYT_GEO_DICT_NEW_";
	//新地区码以坐标为key
	public static final String CACHE_TYT_GEO_DICT_PX_PY_NEW = "CACHE_TYT_GEO_DICT_PX_PY_NEW_";
	public static final String URL_LIMIT_REFRESH_TIME_KEY = "url.limit.refresh.time";
	public static final String REDIS_PLAT_URL_LIMIT_PREFFIX_KEY = "redis.plat.url.limit.preffix";
	public static final String REQUEST_URL_LIMIT_PLAT_CACHE_KEY = "request.url.limit.plat.cache.key";
	public static final String SINGLE_USER_CALL_URL_LIMIT_CACHE_KEY = "single.user.call.url.limit.cache";
	public static final String SINGLE_USER_CALL_TIME_CACHE_KEY = "single.user.call.time.cache";
	//-----------新版本电话限制提示开始----------//
	public static final int USER_EXCEED_ALL_TIME_NEW = 5;
	public static final int USER_PAY_OUT_DATE_YOUHAOHUO_NEW = 4;
	public static final String USER_PAY_OUT_DATE_YOUHAOHUO_BOTTOM_PROMPT = "不限";
	public static final int USER_CAR_NO_AUTH_NEW = 1;
	public static final String USER_CAR_NO_AUTH_BOTTOM_PROMPT = "+{callTime}次/天";
	public static final int USER_IDENTITY_NO_AUTH_NEW = 2;
	public static final String USER_IDENTITY_NO_AUTH_TOP_PROMPT = "{callTime}次/天";
	public static final int USER_PAY_OUT_DATE_DAKU_NEW = 3;
	public static final String USER_PAY_OUT_DATE_DAKU_TOP_PROMPT = "{callTime}次/天";
	public static final String USER_PAY_OUT_DATE_DAKU_BOTTOM_PROMPT = "不限";
	//-----------新版本电话限制提示结束----------//
	public static final String PAY_LAST_TIME_KEY = "pay.last.time";
	public static final String USER_IDENTITY_NO_AUTH_BOTTOM_PROMPT = "+{callTime}次/天";
	public static final String RESTRICE_LOGIN_FAIL_TIME_VERSION_KEY = "car_login_youhua_version";
	public static final String LOGIN_FAIL_TIME_REDIS_KEY = "login.fail.time.redis";
	public static final String LOGIN_SMS_FAIL_TIME_REDIS_KEY = "login.sms.fail.time.redis";
	public static final String LOGIN_VERIFY_CODE_REDIS_KEY = "login.verify.code.redis";
	public static final int PASSWORD_ERROR_SHOW_CODE_TIME = 10;
	public static final int PASSWORD_ERROR_SHOW_MODIFY_TIME = 5;
	public static final String VERY_OK_REDIS_KEY = "very.ok.redis.key";
	public static final String IS_NEED_ENCYPT_KEY = "is.need.encypt";
	public static final String CACHE_PAGE_GRADE = "CACHE_PAGE_GRADE_";// 分页级别
	public static final String APPLETS_LOGIN_PASSWORD = "verification_code_login_Initial_password";//小程序用户登录密码


	//----------官网域名---------------//
	public static final String CDN_DOMAIN = "http://www.teyuntong.com";
	public static final int RESTRICT_TYPE_ANDROID = 2;

	//企业货源转特运通通知对接人
	public static final String CORP_PUB_TRANSPORT_MESSAGE = "corp_pub_transport_message";
	//企业货源下架通知特运通通知对接人
	public static final String CORP_TRANSPORT_CANNEL_MESSAGE = "corp_transport_cannel_message";

	public static final String REGISTER_LIMIT_MAX_TIME = "register.limit.max.time.key";
	public static final String REGISTER_LIMIT_MAX_TIMES = "register.limit.max.times.key";

	public static final String SMS_VERIFYCODE_PREFFIX = "plat_sms_verifycode_";
	//变更手机号步骤校验缓存
	public static final String CELLPHONE_CHANGE_STEP = "cellphone_change_step_";
	public static final String SMS_VERIFIED_PREFFIX = "plat_sms_verified_";
	public static final int NO_BIND_PROMPTTYPE = 14;
	public static final String NO_BIND_PROMPTCONTENT = "开通会员后可继续查看";
	public static final int EXPERIENCE_TIMEOUT_CAR_PROMPTTYPE = 6;
	public static final int EXPERIENCE_TIMEOUT_NOCAR_PROMPTTYPE = 7;
	public static final String EXPERIENCE_TIMEOUT_PROMPTCONTENT = "开通会员后可继续查看";
	public static final int NORMAL_TIMEOUT_PROMPTTYPE = 10;
	public static final String NORMAL_TIMEOUT_PROMPTCONTENT = "开通会员后可继续查看";
	public static final int VIP_TIMEOUT_PROMPTTYPE = 13;
	public static final String VIP_TIMEOUT_PROMPTCONTENT = "开通会员后可继续查看";
	public static final int EXPERIENCE_NO_IDENTITY_PROMPTTYPE = 3;
	public static final String EXPERIENCE_NO_IDENTITY_PROMPTCONTENT = "为保障您和对方的财产安全，请先进行身份认证";
	public static final int EXPERIENCE_HAVE_CALL_PROMPTTYPE = 4;
	public static final String EXPERIENCE_HAVE_CALL_PROMPTCONTENT = "您还可以查看{num}次电话。立即开通VIP会员，可享无限次拨打！";
	public static final int EXPERIENCE_HAVE_NO_CALL_PROMPTTYPE = 4;
	public static final String EXPERIENCE_HAVE_NO_CALL_PROMPTCONTENT = "您刚使用了最后一次查看电话机会。立即开通VIP会员，可享无限次拨打！";
	public static final int VIP_NO_IDENTITY_PROMPTTYPE = 11;
	public static final String VIP_NO_IDENTITY_PROMPTCONTENT = "为保障您和对方的财产安全，请先进行身份认证";
	public static final int VIP_LESS_THREE_PROMPTTYPE = 12;
	public static final String VIP_LESS_THREE_PROMPTCONTENT = "VIP会员即将到期！为避免影响您正常使用，请尽快续费！";
	public static final int NORMAL_NO_IDENTITY_PROMPTTYPE = 15;
	public static final String NORMAL_NO_IDENTITY_PROMPTCONTENT = "为保障您和对方的财产安全，请先进行身份认证";
	public static final int NORMAL_NO_CAR_PROMPTTYPE = 16;
	public static final String NORMAL_NO_CAR_PROMPTCONTENT = "您未认证车辆，立即认证可继续使用试用会员。";
	public static final int NORMAL_LESS_THREE_PROMPTTYPE = 8;
	public static final String NORMAL_LESS_THREE_PROMPTCONTENT = "试用权限即将到期，立即开通VIP会员，可享无限次拨打！";
	public static final int NORMAL_LAST_PROMPTTYPE = 9;
	public static final String NORMAL_LAST_PROMPTCONTENT = "试用权限即将到期，立即开通VIP会员，可享无限次拨打！";
	public static final int VIP_TODAY_OVER_PROMPTTYPE = 17;
	public static final String VIP_TODAY_OVER_PROMPTCONTENT = "今日查看电话次数已用完";

	public static final int RIGHTS_EXPERIENCE = 1;
	public static final int RIGHTS_EXPERIENCE_TIMEOUT = 2;
	public static final int RIGHTS_NORMAL = 3;
	public static final int RIGHTS_NORMAL_TIMEOUT = 4;
	public static final int RIGHTS_VIP = 5;
	public static final int RIGHTS_VIP_TIMEOUT = 6;

	public static final String NON_VIP_CONTENT = "开通会员，享无限次拨打！";

	public static final String INDEX_EXPERIENCE_NO_IDENTITY_TYPE1 = "100";
	public static final String INDEX_EXPERIENCE_NO_IDENTITY_TYPE2 = "1";
	public static final String INDEX_EXPERIENCE_NO_IDENTITY_CONTENT = "恭喜您获得3次免费查看电话机会！认证身份后开始使用！";
	public static final String INDEX_EXPERIENCE_IDENTITY_TYPE1 = "100";
	public static final String INDEX_EXPERIENCE_IDENTITY_TYPE2 = "2";
	public static final String INDEX_EXPERIENCE_IDENTITY_CONTENT = "恭喜您获得3次免费查看电话机会！可立即开始找货！";
	public static final String INDEX_CAR_AUTH_TYPE1 = "100";
	public static final String INDEX_CAR_AUTH_TYPE2 = "4";
	public static final String INDEX_CAR_AUTH_CONTENT = "恭喜您获得20天试用会员！共30次免费查看电话机会！";

	public static final String REPLACEMENT_STARTS = "会员可看发货人";
	public static final String REPLACEMENT_STARTS_CONTENT = "会员可看货物信息";

	public static final String CACHE_FALL_SHORT_TRANSPORT_LIST_KEY = "cache_fall_short_transport_list_";
	/**  扰乱电话开关*/
	public static final String IS_DISTURB = "is_disturb";
	public static final String DISTURB_USER_ID = "disturb_user_id";
	/**  查询第1次查询*/
	public static final int QUERY_TYPE_FIRIST=1;
	/**  查询上拉*/
	public static final int QUERY_TYPE_PULL_UP=2;
	/**  查询下拉*/
	public static final int QUERY_TYPE_PULL_DOWN=0;
	/**  els开关设置参数*/
	public static final String ELS_ONOFF = "tyt_use_es_onoff";
	/**  用户缓存有效时间*/
	public static final String USER_SUB_CACHE_VALID_TIME = "user_sub_cache_valid_time";
	// 5930开关
	public static final String EQUITY_ON_OFF = "equity_on_off";

	//登录版本号控制
    public static final String LOGIN_VERSION_CONTROL = "app_login_version_control";

	/**
	 * 限制用户发货时间的缓存KEY
	 */
	public static final String CACHE_LIMIT_TRANSPORT_TIME = "limit:publish:transport:";

	public static final String SYNC_CAR_BI_PRIVATEKEY_KEY = "sync.car.bi.privatekey";
	public static final String SYNC_CAR_BI_HTTP_BASE_KEY = "sync.car.bi.http.base";

	/**
	 * 缓存车辆当前位置信息
	 */
	public static final String CACHE_CAR_CURRENT_LOCATION = "car:current:location:";

	/**
	 * 缓存车辆当前位置信息
	 */
	public static final String CACHE_EXPIRE_LOCATION_APPLY = "expire:location:apply:";

    /**
     * 目的地货源查询条数
     */
	public static final String DEST_TRANSPORT_SEARCH_SIZE = "dest_transport_search_size";

    /**
     * 目的地货源缓存key
     */
	public static final String CACHE_DEST_TRANSPORT_SEARCH_KEY = "cache_dest_transport_search_";

	public static final String START_AD_LIST_GET_CACHE_TIME = "start_ad_list_get_cache_time";

	/**
	 * 相似货源缓存key
	 */
	public static final String CACHE_SIMILAR_TRANSPORT_SEARCH_KEY = "cache_similar_transport_search_";


	/**
	 * 平安token的redis key
	 */
	public static final String PING_AN_TOKEN_KEY="ping_an_token_key";
	/**
	 * 老的ticket过期时间
	 */
	public static final String TYT_OLD_TICKET_OUT_TIME_MIN="5";

	/**
	 * 升级版本号限制操作
	 */
	public static final String UPGRADE_CLIENT_VERSION_CHECK="upgrade_client_version_check";

	/**
	 * 升级客户端限制操作
	 */
	public static final String UPGRADE_CLIENT_SIGN_CHECK="upgrade_client_sign_check";

	/**
	 * 签单时添加车辆默认失败原因
	 */
	public static final String CAR_FAILURE_REASON = "车辆信息待完善，请完善后重新提交审核";

	public static final String USER_PERMISSION_REDIS_KEY="user_permission_";

	//登录类型 密码登录
	public static final String LOGIN_TYPE_PWD="PASSWORD";
	//登录类型 短信登录
	public static final String LOGIN_TYPE_SMS="SMS";

	/**
	 * 限时货源已接单通知货主短信模板编号
	 */
	public static final String LIMITED_TRANSPORT_RECEIVING="sms_limited_transport_receiving";

	//check version cache key
	public static final String VERSION_NEW_BY_CLIENT = "VERSION_NEW_BY_CLIENT";
	/**
	 * 定向用户抽奖活动--我的活动页面--活动列表title
	 */
	public static final String LOTTERY_ACTIVITY_TITLE = "幸运大抽奖活动";
	/**
	 * 定向用户抽奖活动--我的活动页面--活动列表内容
	 */
	public static final String LOTTERY_ACTIVITY_CONTENT = "您收到一份大礼包，快来查收吧！";
	/**
	 * 定向用户抽奖活动--我的活动页面--活动列表内容
	 */
	public static final String LOTTERY_ACTIVITY_LINK_URL = "/luckDraw/v1/vipLuck.html";
	/**
	 * 定向用户抽奖活动--奖券过期时间 2 天
	 */
	public static final int EXPIRATION = 2;
	/**
	 * 虚拟号开关key
	 */
	public static final String VIRTUAL_NUMBER_SWITCH = "virtual_number_switch";
	/**
	 * 满帮userId前缀
	 */
	public static final String MANBANG_DOMAIN_CONST = "46_";

	/**
	 * 运费加价次数
	 */
	public static final String FREIGHT_ADD_MONEY_NUM = "freight_add_money_num";

	/**
	 * 校验一口价运费最低价开关
	 */
	public static final String check_low_carry_price = "tyt:plat:config:check_low_carry_price";
	/**
	 * 服务端校验一口价相关字段开关(0关闭校验，空或其他时开启校验)
	 */
	public static final String check_one_mark_price = "tyt:plat:config:check_one_mark_price";

	/**
	 * 发货mq延迟时间(毫秒)
	 */
	public static final String publish_mq_delay_time = "tyt:pc:config:publishMqDelayTime";

	/**
	 * 限制个人相似货源（0关闭；1开启）
	 */
	public static final String check_personal_similarity = "tyt:plat:config:checkPersonalSimilarity";

	/**
	 * 车版强制升级版本（逗号分割）
	 */
	public static final String force_car_upgrade_version = "tyt:plat:config:force_car_upgrade_version";

	/**
	 * 货版是否强制升级开关，1开，0关
	 */
	public static final String force_goods_upgrade = "tyt:plat:config:force_goods_upgrade";
	/**
	 * 货版强制升级版本（逗号分割）
	 */
	public static final String force_goods_upgrade_version = "tyt:plat:config:force_goods_upgrade_version";

	/**
	 * 货版强制升级版本（逗号分割）
	 */
	//public static final String detail_phone_show = "tyt:plat:config:detail_phone_show";

	/**
	 * 支付订金是否可以修改（0可以；1不可以）
	 */
	public static final String pay_deposit_read_only = "tyt:plat:config:pay_deposit_read_only";

	/**
	 * 经过后台自动匹配的标准货源id
	 */
	public static final int back_match_item_id = 9999999;

	/**
	 * 发布页面信用曝光按钮显示开关(0-关闭；1-打开)
	 */
	public static final String PUBLISH_CREDIT_RETOP_ONOFF = "tyt:plat:config:publish:credit:retop:onoff";

	/**
	 * 货源列表页面信用曝光按钮显示开关(0-关闭；1-打开)
	 */
	public static final String PUBLISH_LIST_CREDIT_RETOP_ONOFF = "tyt:plat:config:publish:list:credit:retop:onoff";

	/** 默认用户昵称 **/
	public static final String default_nick_name = "用户...";

	/** 货源刷新间隔(秒) **/
	public static final String transport_top_interval = "tyt:plat:config:transport_top_interval";

	/** 货源刷新最大次数 **/
	public static final String transport_top_max_count = "tyt:plat:config:transport_top_max_count";

	/** 活动场次数据KEY **/
	public static final String ACTIVITY_CONVENTION_ROUNDTIME_JSON = "TYT:ACTIVITY:CONVENTION:ROUNDTIME:JSON";

	public static final String PERMISSION_GAIN_PUBLISH = "PERMISSION_GAIN_PUBLISH_";

	/**
	 * 是否开启车辆信息开关(0-关闭；1-开启)
	 */
	public static final String CAR_INFO_ONOFF = "tyt:plat:config:car:info:onoff";

	/**
	 * 曝光卡回收距发布货源间隔（分钟）
	 */
	public static final String RECOVER_EXPOSURE_TIME = "tyt:transport:recover_exposure_time";
	/**
	 * 优推好车主持续时间（分钟）
	 */
	public static final String PRIORITY_RECOMMEND_SUSTAIN_TIME = "tyt:transport:priority_recommend_sustain_time";

	/**
	 * 隐私号缓存
	 */
	public static final String CACHE_PRIVACY_PHONE_NUM_DATA_KEY = "PRIVACY:CACHE_PRIVACY_PHONE_NUM_DATA:";

	/**
	 * 优车货源下发布时间
	 */
	public static final String EXCELLENT_GOODS_PUBLISH_TIME = "tyt:transport:excellent_goods_publish_time:";

	/** 风险单轨迹判定偏差公里数 */
	public static final String LOCATION_DETERMINE_BIAS_DISTANCE = "LOCATION_DETERMINE_BIAS_DISTANCE";

	/**
	 * 异常上报撤销方 1 车主
	 */
	public static final String CAR_EX_CANCEL = "1";

	/**
	 * 异常上报撤销方 2 货主
	 */
	public static final String GOODS_EX_CANCEL = "2";

	public static final String INFO_FEE_PAYMENT_MIN_AMOUNT = "info_fee_payment_min_amount";
	/**
	 * 用户当月发布次数
	 */
	public static final String MONTH_PUBLISH_NUM= "month_user_publish_num_";

	/**
	 * 开票货源接单APP版本号校验
	 */
	public static final String check_invoice_transport_version = "tyt:plat:config:check_invoice_transport_version";
	/**
	 * 优车2.0定价阻断系数
	 */
	public static final String EXCELLENT_PRICE_THPRICE_MILEAGE = "excellent_price_thPrice_mileage";

	/**
	 * 货会员引导ab测
	 */
	public static final String TRANSMISSION_AB_TEST_KEY = "no_member_unvarnished_transmission_show_abtest";

	public static final int CAR_PORT = 1;
	public static final int GOODS_PORT = 2;
}

