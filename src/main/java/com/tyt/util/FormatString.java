package com.tyt.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tyt.config.util.AppConfig;
/**
 * 从字符串中提取数字
 * <AUTHOR>
 *
 */
public class FormatString {
	/**
	 * 格式化消息
	 * 
	 * @param code
	 *            失败错误码
	 * @param keyArray
	 *            变量Key数组
	 * @param valueArray
	 *            需要替换的value数组
	 * @return
	 */
	public static String formatFailMessage(String code, String[] keyArray,
			String[] valueArray) {
		String message =AppConfig.getProperty(code);
		message = org.apache.commons.lang.StringUtils.replaceEach(message, keyArray, valueArray);
		return message;
	}
	public static String getString(String msg){
		int index=msg.indexOf("(");
		if(index<0)index=msg.indexOf("<");
		String str=index>=0?msg.substring(index):"";
		String regEx="[^0-9]";     
		Pattern p = Pattern.compile(regEx);     
		Matcher m = p.matcher(str); 
		return m.replaceAll("").trim();
	}
	
	public static String getNum(String msg){
		String regEx="[^0-9]";     
		Pattern p = Pattern.compile(regEx);     
		Matcher m = p.matcher(msg); 
		return m.replaceAll("").trim();
	}
	
	public static  String specialStringFilter(String   str){      
		  String regEx="[`^\"''‘”“’]";   
		  Pattern   p   =   Pattern.compile(regEx);      
		  Matcher   m   =   p.matcher(str);      
		  return   m.replaceAll(" ").trim();      
		   }
	public static void main(String[] args) {
//		System.out.println(specialStringFilter("/\"[1？].我:;是*/h~!@#$%a,2.eew&*+=|{}1(ha)；<>/?~！@#￥%……&*——+|{}【】：ss。，、"));
	     System.out.println(getNum("2.2.3"));
	}
	
}
