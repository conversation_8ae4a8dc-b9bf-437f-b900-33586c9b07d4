package com.tyt.util;

import com.tyt.user.service.TytConfigService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * 业务开关工具类
 */
@Service
public class TytSwitchUtil {
    private static final Logger logger = LoggerFactory.getLogger(TytSwitchUtil.class);

    private static final String NEW_INFOFEE_VERSION_KEY = "infoFee_newVersion_enable";

    //购买人保货运险被禁用提示语(1:开 2:关 )
    private static final String DISABLE_BUY_PICC_INSURANCE = "disable_buy_picc_insurance";

    //tpay收银台版本是否启用(1:开 2:关)
    private static final String TPAY_VERSION_ENABLE = "tpay_version_enable";


    //满帮收银台版本是否启用(1:开 2:关)
    private static final String MANBANG_WALLET_SWITCH = "manbang_wallet_switch";

    //是否允许钱包版本之前的版本提现(0不允许，1:允许)
    private static final String OLD_VERSION_ENABLE_WITHDRAWL = "old_version_enable_withdrawl";
    //是否允许钱包版本之前的版本 不允许提现提示信息
    private static final String OLD_VERSION_ENABLE_WITHDRAWL_MESSAGE = "old_version_unable_withdrawl_message";

    //限制特定IP登录开关是否开启(1:开 2:关 )
    private static final String LIMIT_IP_LOGIN_ENABLE = "limit_ip_login_enable";

    //货主权益赠送活动开关(1:开 2:关)
    private static final String GOODS_GIVEWAY_PERMISSION_ONOFF = "goods_giveway_permission_onoff";

    //安卓车主版6140-6142升级开关（1:开 2:关）
    private static final String  CAR_21_6140_UPGRADE_ONOFF = "car_21_6140_upgrade_onoff";

    //货主1+9权益赠送活动开关(1:开 2:关)
    private static final String GOODS_NONMEMBER_ACTIVITY_ONOFF = "goods_nonmember_activity_onoff";

    //货app注册活动赠送商品id（轮巡，逗号分割）
    private static final String GOODS_REGISTER_ACTIVITY_GOODSIDS = "goods_register_activity_goodsIds";

    private static final String TYT_ORDER_OPERATE_MQ_ONOFF = "tyt_order_operate_mq_onoff";

    private static final String MB_ORDER_OPERATE_MQ_ONOFF = "mb_order_operate_mq_onoff";

    private static TytConfigService configService;

    @Autowired
    private TytConfigService configServiceInject;

    @PostConstruct
    private void init(){
        configService = this.configServiceInject;
    }


    /**
     * 是否新版本信息费
     * @return
     */
    public static boolean isNewInfofeeVersion(){
        try {
            return configService.getIntValue(NEW_INFOFEE_VERSION_KEY) == 1;
        }catch (Exception e){
            logger.error("query switch of new infofee version error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * 购买人保货运险是否被禁用开关(1.开 2.关)
     * @return
     */
    public static boolean isDisableBuyPiccInsurance(){
        try {
            return configService.getIntValue(DISABLE_BUY_PICC_INSURANCE) == 1;
        }catch (Exception e){
            logger.error("query disable buy picc insurance error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * @Description  是否启用tpay收银台版本(true:是,false:否)
     * <AUTHOR>
     * @Date  2020/1/16 16:37
     * @Param []
     * @return boolean
     **/
    public static boolean isTpayVersion(){
        try {
            return configService.getIntValue(TPAY_VERSION_ENABLE) == 1;
        }catch (Exception e){
            logger.error("query is tapy version error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }


    /**
     * @Description  是否启用满帮收银台版本(true:是,false:否)
     * <AUTHOR>
     * @Date  2020/1/16 16:37
     * @Param []
     * @return boolean
     **/
    public static boolean isManBangVersion(){
        try {
            return configService.getIntValue(MANBANG_WALLET_SWITCH) == 1;
        }catch (Exception e){
            logger.error("query is manBang version error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }


    /**
     * @Description  钱包版本前版本是否不允许提现(true:是,false:否)
     * <AUTHOR>
     * @Date  2021/1/14 16:37
     * @return boolean
     **/
    public static boolean isUnableWithdrawl(){
        try {
            return configService.getIntValue(OLD_VERSION_ENABLE_WITHDRAWL) == 0;
        }catch (Exception e){
            logger.error("query is tapy version error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * @Description  钱包版本前版本不允许提现 提示信息
     * <AUTHOR>
     * @Date  2021/1/14 16:37
     * @return boolean
     **/
    public static String getUnableWithdrawlMessage(){
        try {
            return configService.getStringValue(OLD_VERSION_ENABLE_WITHDRAWL_MESSAGE);
        }catch (Exception e){
            logger.error("query is tapy version error, {}", ExceptionUtils.getStackTrace(e));
        }
        return "版本升级中";
    }


    /**
     * @description 限制特定IP登录开关是否开启(true:开,false:关)
     * <AUTHOR>
     * @date 2021/1/8 14:36
     * @param
     * @return boolean
     */
    public static boolean isLimitIpLogin(){
        try {
            return configService.getIntValue(LIMIT_IP_LOGIN_ENABLE) == 1;
        }catch (Exception e){
            logger.error("query is limit ip login error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * @description 货主权益赠送活动开关(1:开 2:关)
     * <AUTHOR>
     * @date 2022/2/16 16:59
     * @param
     * @return boolean
     */
    public static boolean isGoodsGivewayPermissionOn(){
        try {
            return configService.getIntValue(GOODS_GIVEWAY_PERMISSION_ONOFF) == 1;
        }catch (Exception e){
            logger.error("query is goodsGiveway permission on error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    public static boolean isCar6140UpgradeOn(){
        try {
            return configService.getIntValue(CAR_21_6140_UPGRADE_ONOFF) == 1;
        }catch (Exception e){
            logger.error("query is Car6140Upgrade on error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * @description 货主1+9权益赠送活动开关(1:开 2:关)
     * <AUTHOR>
     * @date 2022/2/16 16:59
     * @param
     * @return boolean
     */
    public static boolean isGoodsNonMemberActivityOn(){
        try {
            return configService.getIntValue(GOODS_NONMEMBER_ACTIVITY_ONOFF) == 1;
        }catch (Exception e){
            logger.error("query is GoodsnonMember permission on error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * 货app注册活动赠送商品id（轮巡，逗号分割）
     * @return
     */
    public static String getGoodsRegisterActivityGoodsIds(){
        try {
            return configService.getStringValue(GOODS_REGISTER_ACTIVITY_GOODSIDS);
        }catch (Exception e){
            logger.error("query is tapy version error, {}", ExceptionUtils.getStackTrace(e));
        }
        return "164,165,166";
    }

    /**
     * 特运通订单MQ消息发送到新消费者开关
     * <AUTHOR>
     * @param
     * @return boolean
     */
    public static boolean isTytOrderOperateMqOn(){
        try {
            return configService.getIntValue(TYT_ORDER_OPERATE_MQ_ONOFF) == 1;
        }catch (Exception e){
            logger.error("query is GoodsnonMember permission on error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * 满帮订单MQ消息发送到新消费者开关
     * <AUTHOR>
     * @param
     * @return boolean
     */
    public static boolean isMbOrderOperateMqOn(){
        try {
            return configService.getIntValue(MB_ORDER_OPERATE_MQ_ONOFF) == 1;
        }catch (Exception e){
            logger.error("query is GoodsnonMember permission on error, {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }
}
