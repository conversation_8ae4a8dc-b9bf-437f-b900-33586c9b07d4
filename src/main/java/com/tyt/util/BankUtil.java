package com.tyt.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 银行工具类，例如转换银行logo
 * Created by duanwc on 2018/6/15.
 */
public class BankUtil {
    public static Map<String, String> bankMap = null;

    /**
     * 未知银行logo
     */
    private static final String UNKNOW_LOGO = "/bank_logo/unknow.png";
    static {
        bankMap = new HashMap<>();
        // 以下为易宝直连银行
        bankMap.put("招商银行", "/bank_logo/cmb.png"); // 易宝编码：CMBCHINA
        bankMap.put("北京银行", "/bank_logo/bccb.png"); // 易宝编码：BCCB
        bankMap.put("平安银行", "/bank_logo/spabank.png"); // 易宝编码：SZCB
        bankMap.put("工商银行", "/bank_logo/icbc.png"); // 易宝编码：ICBC
        bankMap.put("交通银行", "/bank_logo/bcm.png"); // 易宝编码：BOCO
        bankMap.put("中国银行", "/bank_logo/boc.png"); // 易宝编码：BOC
        bankMap.put("建设银行", "/bank_logo/ccb.png"); // 易宝编码：CCB
        bankMap.put("兴业银行", "/bank_logo/cib.png"); // 易宝编码：CIB
        bankMap.put("农业银行", "/bank_logo/abc.png"); // 易宝编码：ABC
        bankMap.put("民生银行", "/bank_logo/cmbc.png"); // 易宝编码：CMBC
        bankMap.put("中信银行", "/bank_logo/citicbank.png"); // 易宝编码：ECITIC
        bankMap.put("华夏银行", "/bank_logo/hxb.png"); // 易宝编码：HXB
        bankMap.put("上海浦东发展银行", "/bank_logo/spdb.png"); // 易宝编码：SPDB
        bankMap.put("广州银行", "/bank_logo/gzyh.png"); // 易宝编码：GZYH
        bankMap.put("广发银行", "/bank_logo/cgb.png"); // 易宝编码：CGB
        bankMap.put("中国邮政储蓄", "/bank_logo/psbc.png"); // 易宝编码：POST
        bankMap.put("深圳发展银行", "/bank_logo/spabank.png"); // 易宝编码：SDB,现已属于平安银行

        // 以下银行需要输入开户行、省、市
        bankMap.put("东莞农村商业银行", "/bank_logo/drcbank.png"); // 易宝编码：DRCB
        bankMap.put("甘肃银行", "/bank_logo/gsbank.png"); // 易宝编码：GSYH
        bankMap.put("光大银行", "/bank_logo/ceb.png"); // 易宝编码：CEB
        bankMap.put("广东南粤银行", "/bank_logo/gdnybank.png"); // 易宝编码：GDNYYJ
        bankMap.put("邯郸银行", "/bank_logo/hdcb.png"); // 易宝编码：HDCB
        bankMap.put("汉口银行", "/bank_logo/hkbchina.png"); // 易宝编码：HKYH
        bankMap.put("华融湘江银行", "/bank_logo/hrxjbank.png"); // 易宝编码：HRXJBK
        bankMap.put("徽商银行", "/bank_logo/hsbank.png"); // 易宝编码：AHCB
        bankMap.put("吉林银行", "/bank_logo/jlbank.png"); // 易宝编码：JLSB
        bankMap.put("江南农村商业银行", "/bank_logo/jnbank.png"); // 易宝编码：JNNCSYYH
        bankMap.put("江苏银行", "/bank_logo/jsbchina.png"); // 易宝编码：JSBCHINA
        bankMap.put("晋中银行", "/bank_logo/jzbank.png"); // 易宝编码：JZYH
        bankMap.put("龙江银行", "/bank_logo/lj-bank.png"); // 易宝编码：LJYH
        bankMap.put("青岛银行", "/bank_logo/qdccb.png"); // 易宝编码：QDTH
        bankMap.put("成都银行", "/bank_logo/cdsybank.png"); //易宝编码：CDYH

        // 以下是易宝不支持银行
        bankMap.put("库尔勒银行", "/bank_logo/xjkccb.png");//易宝不支持
        bankMap.put("上海农商银行", "/bank_logo/srcb.png");//易宝不支持
        bankMap.put("深圳农村商业银行", "/bank_logo/szncbank.png"); //易宝不支持
        bankMap.put("保定银行", "/bank_logo/bd-bank.png"); //易宝不支持
        bankMap.put("常熟农村商业银行", "/bank_logo/csrcbank.png"); //易宝不支持
        bankMap.put("重庆农村商业银行", "/bank_logo/cqrcb.png"); //易宝不支持
        bankMap.put("遵义市商业银行", "/bank_logo/zysybank.png"); //易宝不支持，遵义市商业银行、六盘水市商业银行、安顺市商业银行重组为 贵州银行
    }

    /**
     * 获取银行卡logo
     * @param bankName 银行名称
     * @return String logo地址
     */
    public static String getBankLogo(String bankName) {
        if(bankMap.get(bankName.trim()) != null){
            return bankMap.get(bankName.trim());
        }
        return UNKNOW_LOGO;
    }
}
