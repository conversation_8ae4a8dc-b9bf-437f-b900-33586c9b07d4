package com.tyt.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class MoneyTools {
	
	/**
	 * 根据本金跟利率计算利息
	 * @param capital
	 * @param interestRate
	 * @return
	 */
	public static BigDecimal getBankInterest(String capital,String interestRate){
		    BigDecimal d = new BigDecimal(capital);      //存款  
	        BigDecimal r = new BigDecimal(interestRate);   //利息  
	        BigDecimal i = d.multiply(r).setScale(2,RoundingMode.HALF_EVEN);
	        //转化成分
	        return i;
	}
	
	public static void main(String[] args) {
		getBankInterest("1001", "0.004");
	}

}
