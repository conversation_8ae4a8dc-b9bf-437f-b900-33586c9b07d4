package com.tyt.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

public class PropertiesFileUtil {

	
	//private  String BUNDLE_NAME = "security_intercept_url";

	public static Map<String,Map<String,String>> resourceMap=new HashMap<String,Map<String,String>>();
	public static Map<String,Long> lastFalshTimeMap=new HashMap<String,Long>();
	
	//刷新间隔 5分钟
	//public static final long refreshInterval=300000;
	public static final long refreshInterval=60000;
	

	private String propertiesFileName;
	
	private PropertiesFileUtil(){
	}
	private PropertiesFileUtil(String propertiesFileName){
		this.propertiesFileName=propertiesFileName;
	}
	
	//初始化
	public synchronized static PropertiesFileUtil init(String propertiesFileName)
	{
		
		if(resourceMap.containsKey(propertiesFileName)){
			if(isFalsh(propertiesFileName)){
				resourceMap.put(propertiesFileName, falshMap(propertiesFileName));
			}	
		}else{
			resourceMap.put(propertiesFileName, falshMap(propertiesFileName));
		}
		return new PropertiesFileUtil(propertiesFileName);
	}

	//获得属性值
	public  String getString(String key) {
		if(isFalsh(propertiesFileName)){
			resourceMap.put(propertiesFileName, falshMap(this.propertiesFileName));
		}	
		Map<String, String> propertiesMap=resourceMap.get(this.propertiesFileName);
		String value=propertiesMap.get(key);		
		return value == null ? "" : value.trim();
	}
	
	
	//刷新文件
	public synchronized static  Map<String ,String> falshMap(String propertiesFileName){
		Properties properties =getProperties(propertiesFileName);
		Set<String> set=properties.stringPropertyNames();
		Map<String,String> propertiesMap=new HashMap<String,String>();		
		for(String key:set){
			String value=properties.getProperty(key);
			propertiesMap.put(key, value==null?"":value);
		}	
		return propertiesMap;
	}
	//获得所有的key
	public  Set<String> getKeySet()
	{	
		if(isFalsh(propertiesFileName)){
			resourceMap.put(propertiesFileName, falshMap(this.propertiesFileName));
		}		
		if(resourceMap.containsKey(propertiesFileName)){
			Map<String,String> propertiesMap=resourceMap.get(propertiesFileName);
			return propertiesMap.keySet();
		}
		return null;
	}
	//获得属性文件
	 public synchronized  static Properties getProperties(String propertiesName){
 		Properties properties=new Properties();
        InputStream conf = null;
        try
        {
           // conf = PropertiesFileUtil.class.getResourceAsStream( "/"+propertiesName+".properties" );
            conf=getFileInputStream(propertiesName);
            properties.load(conf);
        }
        catch (Exception ex2)
        {
            ex2.printStackTrace();
        }
        finally
        {
            try
            {
                conf.close();
            }
            catch (Exception ex1)
            {
            	ex1.printStackTrace();
            }
        }
        return properties;
	    }

	 //是否刷新文件
	public static boolean isFalsh(String propertiesName){
		System.out.println("begin"+System.currentTimeMillis());
		Long lastTime=lastFalshTimeMap.get(propertiesName);
		File file = new File(getFilePath(propertiesName));
		if(file.lastModified()>(lastTime==null?0l:lastTime.longValue())){
			System.out.println("end"+System.currentTimeMillis());
			return true;
		}	
		System.out.println("end"+System.currentTimeMillis());
		return false;
	}
	
	
	public static InputStream getFileInputStream(String propertiesName) throws FileNotFoundException {
		
		File file = new File(getFilePath(propertiesName));
		if (file.exists()) {
			FileInputStream fileInputStream=new FileInputStream(file);
			lastFalshTimeMap.put(propertiesName, file.lastModified());
			return fileInputStream;
		} 
		return null;
	}
	


	public static String getFilePath(String propertiesName){
		final String absPath = Thread.currentThread().getContextClassLoader().getResource("").getPath().replaceAll(
				"%20", " ");
		final StringBuffer resourceFullName = new StringBuffer();
		resourceFullName.append(absPath);
		resourceFullName.append(propertiesName);
		resourceFullName.append(".properties");
		return resourceFullName.toString();
	}
}