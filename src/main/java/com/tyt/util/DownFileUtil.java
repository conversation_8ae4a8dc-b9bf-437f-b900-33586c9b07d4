 
package com.tyt.util;

import com.tyt.config.util.AppConfig;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;


public class DownFileUtil {

	public static final int cache = 10 * 1024;
	public static final boolean isWindows;
	public static final String splash;
	public static final String root;
	static {
		if (System.getProperty("os.name") != null && System.getProperty("os.name").toLowerCase().contains("windows")) {
			isWindows = true;
			splash = "\\";
			root="D:";
		} else {
			isWindows = false;
			splash = "/";
			root="/search";
		}
	}
	
	/**
	 * 根据url下载文件，文件名从response header头中获取
	 * @param url
	 * @return
	 */
	public static String download(String url) {
		return download(url, null);
	}

	/**
	 * 根据url下载文件，保存到filepath中
	 * @param url
	 * @param filepath
	 * @return
	 */
	public static String download(String url, String filepath) {
		try {
			HttpClient client = new DefaultHttpClient();
			HttpGet httpget = new HttpGet(url);
			HttpResponse response = client.execute(httpget);

			HttpEntity entity = response.getEntity();
			InputStream is = entity.getContent();
			if (filepath == null)
				filepath = getFilePath(response);
			File file = new File(filepath);
			file.getParentFile().mkdirs();
			FileOutputStream fileout = new FileOutputStream(file);
			/**
			 * 根据实际运行效果 设置缓冲区大小
			 */
			byte[] buffer=new byte[cache];
			int ch = 0;
			while ((ch = is.read(buffer)) != -1) {
				fileout.write(buffer,0,ch);
			}
			is.close();
			fileout.flush();
			fileout.close();

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 获取response要下载的文件的默认路径
	 * @param response
	 * @return
	 */
	public static String getFilePath(HttpResponse response) {
		String filepath = root + splash;
		String filename = getFileName(response);

		if (filename != null) {
			filepath += filename;
		} else {
			filepath += getRandomFileName();
		}
		return filepath;
	}
	/**
	 * 获取response header中Content-Disposition中的filename值
	 * @param response
	 * @return
	 */
	public static String getFileName(HttpResponse response) {
		Header contentHeader = response.getFirstHeader("Content-Disposition");
		String filename = null;
		if (contentHeader != null) {
			HeaderElement[] values = contentHeader.getElements();
			if (values.length == 1) {
				NameValuePair param = values[0].getParameterByName("filename");
				if (param != null) {
					try {
						//filename = new String(param.getValue().toString().getBytes(), "utf-8");
						//filename=URLDecoder.decode(param.getValue(),"utf-8");
						filename = param.getValue();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		return filename;
	}
	/**
	 * 获取随机文件名
	 * @return
	 */
	public static String getRandomFileName() {
		return String.valueOf(System.currentTimeMillis());
	}
	/**
	 * 获取response header
	 * @param response
	 */
	public static void outHeaders(HttpResponse response) {
		Header[] headers = response.getAllHeaders();
		for (int i = 0; i < headers.length; i++) {
			System.out.println(headers[i]);
		}
	}


	/**
	 * url转MultipartFile
	 * @url:图片URL
	 * @fileName:文件名
	 * @return:返回的文件
	 */
	public static String downloadNew(String url, String filePath) throws Exception {
		File file = null;
		MultipartFile multipartFile = null;
		try {
			HttpURLConnection httpUrl = (HttpURLConnection) new URL(url).openConnection();
			httpUrl.connect();
			file = inputStreamToFile(httpUrl.getInputStream(),filePath);

			httpUrl.disconnect();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static File inputStreamToFile(InputStream ins, String name) throws Exception {
		File file = new File(name);
		OutputStream os = new FileOutputStream(file);
		int len = 8192;
		byte[] buffer = new byte[len];
		int bytesRead;
		while ((bytesRead = ins.read(buffer, 0, len)) != -1){
			os.write(buffer, 0, bytesRead);
		}
		os.close();
		ins.close();
		return file;
	}


	public static String getSaveFilePathName( String typeName,String fileName) {
		String domainurl = "/data/file/" + typeName + "/";// 获取文件路径
		CreateFileUtil.createDir(AppConfig.getProperty("picture.path.domain") + domainurl);
		return domainurl + fileName;
	}
	public static void main(String[] args) {
//		String url = "http://bbs.btwuji.com/job.php?action=download&pid=tpc&tid=320678&aid=216617";
		String url="https://dev-image56-conf-oss.ymm56.com/ymmfile/crm-ymm-pri/2df46739-0a7d-4e67-a99b-40a49f6b8c19.jpg?Expires=1667376090&OSSAccessKeyId=LTAIq0WRi8jPwg5y&Signature=c4wsv167CaY%2Fx%2BkGFc6CEK6dpMo%3D\n";
		String filepath = "c:\\test\\a2.jpg";
//		String filepath = AppConfig.getProperty("picture.path.domain")+DownFileUtil.getSaveFilePathName("htbx","B017136012018000012.pdf");
		System.out.println(filepath);
		try {
			DownFileUtil.downloadNew(url, filepath);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

