package com.tyt.util;

import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.DefaultObjectWrapper;
import freemarker.template.Template;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by duanwc on 18/1/12.
 */
public class FreemarkerUtil {
    private static String defaultCharacter = "UTF-8";
    private static Configuration cfg;

    static {
        cfg = new Configuration(Configuration.getVersion());
        cfg.setDefaultEncoding(defaultCharacter);
        cfg.setTagSyntax(Configuration.AUTO_DETECT_TAG_SYNTAX);
    }
    private FreemarkerUtil(){}

    /**
     *
     * @param data
     * @param tplStr
     * @return
     */
    public static String render(Map<String, Object> data, String tplStr) {
        String result = null;
        String name = "myStrTpl";
        try {
            StringTemplateLoader stringTemplateLoader = new StringTemplateLoader();
            stringTemplateLoader.putTemplate(name, tplStr);
            cfg.setTemplateLoader(stringTemplateLoader);
            Template template = cfg.getTemplate(name, defaultCharacter);
//            template.setClassicCompatible(true);
            StringWriter out = new StringWriter();
            template.process(data, out);
            out.flush();
            result = out.toString();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
