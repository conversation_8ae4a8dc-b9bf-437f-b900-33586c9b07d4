package com.tyt.util;

import java.util.ArrayList;
import java.util.List;

/**
 * List处理工具类
 *
 * <AUTHOR>
 * @since 2024-08-06 13:58
 */
public class ListUtil {

    /**
     * 将列表按照指定大小进行切割。
     *
     * @param originalList 原始列表
     * @param chunkSize    切割的大小
     * @return 分割后的列表集合
     */
    public static List<List<String>> splitIntoChunks(List<String> originalList, int chunkSize) {
        List<List<String>> result = new ArrayList<>();
        for (int start = 0; start < originalList.size(); start += chunkSize) {
            int end = Math.min(start + chunkSize, originalList.size());
            List<String> subList = originalList.subList(start, end);
            result.add(subList);
        }
        return result;
    }

}
