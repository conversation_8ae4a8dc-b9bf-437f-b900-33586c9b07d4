package com.tyt.util;

import com.tyt.transport.annotation.PropertyName;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 类操作相关方法
 *
 * <AUTHOR>
 * @since 2023/10/11 15:25
 */
@Slf4j
public class TytClassUtil {

    /**
     * 通过注解获取属性中文含义
     *
     * @param obj
     * @return
     */
    public static List<ClassProper> classConvertList(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        List<ClassProper> res = new ArrayList<>();
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            // 通过filed获取属性值
            try {
                PropertyName propertyName = field.getAnnotation(PropertyName.class);
                if (Objects.nonNull(propertyName)) {
                    ClassProper classProper = new ClassProper();

                    classProper.setFileName(propertyName.value());
                    classProper.setFileValue(field.get(obj));
                    classProper.setFileType(field.getType());
                    res.add(classProper);
                }
            } catch (IllegalAccessException e) {
                log.error("获取属性中文含义失败:", e);
            }
        }
        return res;
    }


    /**
     * 通过注解获取属性中文含义
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> classConvertMap(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        Map<String, Object> res = new HashMap<>();
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            // 通过filed获取属性值
            try {
                PropertyName propertyName = field.getAnnotation(PropertyName.class);
                if (Objects.nonNull(propertyName)) {
                    res.put(propertyName.value(), field.get(obj));
                }
            } catch (IllegalAccessException e) {
                log.error("获取属性中文含义失败:", e);
            }
        }
        return res;
    }

    @Getter
    @Setter
    public static class ClassProper {

        private String fileName;

        private Object fileValue;

        private Class<?> fileType;
    }
}
