package com.tyt.util;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.tyt.config.util.AppConfig;
import com.tyt.user.querybean.IDCardAuthResult;

public class IDCardAuth {
	public static Logger logger = LoggerFactory
			.getLogger(IDCardAuth.class);
	public static IDCardAuthResult authIdentity(String trueName,String identity){
		
	    BufferedReader reader = null;
	    String result = null;
	    IDCardAuthResult iDCardAuthResult=null;
	    StringBuffer sbf = new StringBuffer();
	    String  httpUrl = AppConfig.getProperty("tyt.user.idcad.auth.host") + "?type=idcard&cardno="+identity+"&name="+trueName;
	    try {
	        URL url = new URL(httpUrl);
	        HttpURLConnection connection = (HttpURLConnection) url
	                .openConnection();
	        connection.setRequestMethod("GET");
	        // 填入apix-key到HTTP header
	        connection.setRequestProperty("apix-key", AppConfig.getProperty("tyt.user.idcad.auth.apiKey"));
	        connection.connect();
	        InputStream is = connection.getInputStream();
	        reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
	        String strRead = null;
	        while ((strRead = reader.readLine()) != null) {
	            sbf.append(strRead);
	            sbf.append("\r\n");
	        }
	        reader.close();
	        result = sbf.toString();
	        if(result!=null){
		    	iDCardAuthResult=JSON.parseObject(result, IDCardAuthResult.class);
		    	iDCardAuthResult.setResult(result);
		    }else{
		    	logger.error("调用身份证第三方验证接口失败返回值为空");
		    	iDCardAuthResult=new IDCardAuthResult();
		    	iDCardAuthResult.setCode(105);
		    	iDCardAuthResult.setMsg("返回值为空");
		    }
	    } catch (Exception e) {
	    	logger.error("调用身份证第三方验证接口失败",e);
	        e.printStackTrace();
	        iDCardAuthResult=new IDCardAuthResult();
	        iDCardAuthResult.setCode(105);
	    	iDCardAuthResult.setMsg("系统错误");
	    }
	    	
	    return iDCardAuthResult;
	}

	public static void main(String[] args) {
	    //发送 GET 请求
		IDCardAuthResult iDCardAuthResult=authIdentity("张产 ","****************");
		iDCardAuthResult.getCode();
		System.out.println(iDCardAuthResult.getCode());
		System.out.println(iDCardAuthResult.getResult());
	}
}
