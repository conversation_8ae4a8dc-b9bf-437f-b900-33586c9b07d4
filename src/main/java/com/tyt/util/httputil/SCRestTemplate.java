package com.tyt.util.httputil;

import com.gexin.fastjson.JSON;
import com.tyt.config.util.AppConfig;
import com.tyt.util.SignUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.DefaultUriTemplateHandler;

import java.lang.reflect.Proxy;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/01/05 10:16
 */

@Configuration
public class SCRestTemplate extends RestTemplate {

    private final static Logger logger = LoggerFactory.getLogger(SCRestTemplate.class);

    @Bean(name = "scRestTemplate")
    public RestOperations proxyBean() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(3_000);
        factory.setReadTimeout(3_000);
        final RestTemplate restTemplate = new RestTemplate(factory);
        //设置 host
        DefaultUriTemplateHandler uriTemplateHandler = new DefaultUriTemplateHandler();
        uriTemplateHandler.setBaseUrl(AppConfig.getProperty("control.server.domin"));
        restTemplate.setUriTemplateHandler(uriTemplateHandler);

        Class<? extends RestOperations> clazz = restTemplate.getClass();
        //生成代理
        return (RestOperations) Proxy.newProxyInstance(clazz.getClassLoader(), clazz.getInterfaces(), (proxy, method, args) -> {
            try {
                //代理之前处理
                if (Objects.nonNull(args)) {

                    if(method.getName().startsWith("get")){

                        String url = (String) args[0];
                        int i = url.indexOf("?");

                        if(i > 0 && url.length() > i){
                            List<NameValuePair> valuePairs = URLEncodedUtils.parse(url.substring(i + 1), StandardCharsets.UTF_8);
                            if(CollectionUtils.isNotEmpty(valuePairs)){

                                TreeMap<String, String> treeMap = valuePairs.stream()
                                        .filter(e -> Objects.nonNull(e.getValue()))
                                        .collect(Collectors.toMap(NameValuePair::getName, NameValuePair::getValue, (e1, e2) -> e1, TreeMap::new));

                                String time = System.currentTimeMillis() + "";
                                treeMap.put("timestamp", time);

                                //签名
                                args[0] = url.concat("&timestamp=")
                                        .concat(time)
                                        .concat("&sign=")
                                        .concat(treeMapToSign(treeMap));
                            }
                        }


                    } else if (method.getName().startsWith("post")){

                        if(args[1] instanceof Map){

                            @SuppressWarnings("unchecked")
                            Map<String, Object> uriVariables = (Map<String, Object>) args[1];
                            if(MapUtils.isNotEmpty(uriVariables)){
                                HttpHeaders headers = new HttpHeaders();
                                headers.setContentType(MediaType.APPLICATION_JSON);
                                args[1] = new HttpEntity<>(args[1], headers);
                            }
                        }

                        if(args[1] instanceof HttpEntity){

                            @SuppressWarnings("unchecked")
                            HttpEntity<Map<String, String>> httpEntity = (HttpEntity<Map<String, String>>) args[1];
                            Map<String, String> multiValueMap = httpEntity.getBody();
                            if(MapUtils.isNotEmpty(multiValueMap)){

                                multiValueMap.put("timestamp", System.currentTimeMillis() +"");

                                TreeMap<String, String> treeMap = multiValueMap.entrySet().stream()
                                        .filter(e -> StringUtils.isNotEmpty(e.getValue()))
                                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, TreeMap::new));

                                //签名
                                multiValueMap.put("sign", treeMapToSign(treeMap));
                            }

                        }

                    }
                }

                //代理执行
                Object result = method.invoke(restTemplate, args);

                //代理之后
                if(result instanceof ResponseEntity){

                    ResponseEntity<?> entity = (ResponseEntity<?>) result;
                    logger.info("请求调度返回码:【{}】, 返回数据: 【{}】", entity.getStatusCodeValue(), entity.getBody());

                } else {
                    logger.info("调度结果返回: 【{}】", JSON.toJSONString(result));
                }

                return result;

            } catch (Exception e){
                e.printStackTrace();
                logger.error("请求调度服务异常:{}", e.getMessage());
                return null;
            }
        });
    }

    private String treeMapToSign(TreeMap<String, ?> treeMap) {
        return SignUtil.sign(treeMap, getPrivateKey());
    }


    /**
     * 获取私钥
     *
     * @return
     */
    public String getPrivateKey() {
        return AppConfig.getProperty("tyt.private.key");
    }


    public static class DefaultResponse{
        private int status;
        private String message;

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
