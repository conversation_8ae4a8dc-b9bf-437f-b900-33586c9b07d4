package com.tyt.util;

import com.google.common.collect.Maps;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.timelimiter.TimeLimiter;
import io.github.resilience4j.timelimiter.TimeLimiterConfig;
import io.github.resilience4j.timelimiter.TimeLimiterRegistry;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:15
 */
@Component
@Slf4j
public class CircuitBreakerService {

    /**
     * 熔断实例, 第三方框架提供
     */
    private final CircuitBreakerRegistry circuitBreakerRegistry =
            CircuitBreakerRegistry.of(getDefaultCircuitBreakerConfig());
    /**
     * 超时实例，第三方框架提供
     */
    private final TimeLimiterRegistry timeLimiterRegistry = TimeLimiterRegistry.of(getDefaultTimeLimiterConfig());

    /**
     * 熔断配置提供者，使用者手动注册
     */
    private final Map<String, Supplier<CircuitBreakerConfig>> circuitBreakerConfigSuppliers = Maps.newConcurrentMap();
    /**
     * 超时配置提供者，使用者手动注册
     */
    private final Map<String, Supplier<TimeLimiterConfig>> timeLimiterConfigSuppliers = Maps.newConcurrentMap();

    /**
     * 执行 TimeLimiter 的线程池，按需修改
     */
    private static final ExecutorService executor = Executors.newCachedThreadPool();

    /**
     * 获取默认熔断配置
     */
    public CircuitBreakerConfig getDefaultCircuitBreakerConfig() {
        return CircuitBreakerConfig.custom()
                .failureRateThreshold(50) // 失败比例
                .slidingWindowSize(30)    // 记录断路器关闭状态下（可以访问的情况下）的调用次数
                .slidingWindowType(CircuitBreakerConfig.SlidingWindowType.COUNT_BASED) //滑动窗口类型，有两种，基于调用次数和基于时间，默认次数
                .waitDurationInOpenState(Duration.of(30, ChronoUnit.SECONDS))  //断路器 open 状态持续时间
                .permittedNumberOfCallsInHalfOpenState(5) // 断路器 half open 状态接受的最大请求数
                .minimumNumberOfCalls(10)  // 开启熔断最小请求数量
                .build();
    }

    /**
     * 获取默认超时配置
     */
    public TimeLimiterConfig getDefaultTimeLimiterConfig() {
        return TimeLimiterConfig.custom()
                .timeoutDuration(Duration.of(3, ChronoUnit.SECONDS)) // 超时时间
                .cancelRunningFuture(true)        // 超时后是否调用 Future 的 cancel 方法
                .build();
    }

    /**
     * 注册超时、熔断配置
     *
     * @param name                         断路器名，一个name对应一个断路器，可以根据方法名/url命名
     * @param circuitBreakerConfigSupplier 断路器配置提供者
     * @param timeLimiterConfigSupplier    超时配置提供者
     */
    public void registerCircuitBreakerConfigSupplier(String name,
                                                     Supplier<CircuitBreakerConfig> circuitBreakerConfigSupplier,
                                                     Supplier<TimeLimiterConfig> timeLimiterConfigSupplier) {
        if (name == null) {
            throw new IllegalArgumentException("CircuitBreakerConfigSupplier 的 name 不能为空");
        }

        if (circuitBreakerConfigSupplier != null) {
            circuitBreakerConfigSuppliers.put(name, circuitBreakerConfigSupplier);
        }

        if (timeLimiterConfigSupplier != null) {
            timeLimiterConfigSuppliers.put(name, timeLimiterConfigSupplier);
        }
    }

    /**
     * 注册超时、熔断配置
     *
     * @param name                 断路器名，一个name对应一个断路器，可以根据方法名/url命名
     * @param circuitBreakerConfig 断路器配置
     * @param timeLimiterConfig    超时配置
     */
    public void registerCircuitBreakerConfig(String name,
                                             CircuitBreakerConfig circuitBreakerConfig,
                                             TimeLimiterConfig timeLimiterConfig) {
        if (name == null) {
            throw new IllegalArgumentException("CircuitBreakerConfigSupplier 的 name 不能为空");
        }

        if (circuitBreakerConfig != null) {
            circuitBreakerConfigSuppliers.put(name, () -> circuitBreakerConfig);
        }

        if (timeLimiterConfig != null) {
            timeLimiterConfigSuppliers.put(name, () -> timeLimiterConfig);
        }
    }

    /**
     * 删除所有断路器
     */
    public void removeAllCircuitBreakers() {
        for (CircuitBreaker circuitBreaker : circuitBreakerRegistry.getAllCircuitBreakers()) {
            circuitBreakerRegistry.remove(circuitBreaker.getName());
        }
    }

    /**
     * 删除所有timeLimiter
     */
    public void removeAllTimeLimiters() {
        for (TimeLimiter timeLimiter : timeLimiterRegistry.getAllTimeLimiters()) {
            timeLimiterRegistry.remove(timeLimiter.getName());
        }
    }

    /**
     * 执行方法，通过 CircuitBreaker 包装，提供熔断能力
     *
     * @param name  name
     * @param toRun 要包装的执行逻辑
     * @return 返回的对象，如果发生异常则会直接抛出异常
     */
    public <T> T executeWithCircuitBreaker(String name,
                                           Supplier<T> toRun) {

        return executeWithCircuitBreaker(name, toRun, throwable -> {
            throw new NoFallbackException("No fallback available.", throwable);
        });
    }

    /**
     * 执行方法，通过 CircuitBreaker 包装，提供熔断能力
     *
     * @param name     name
     * @param toRun    要包装的执行逻辑
     * @param fallback 执行失败后自定义的 fallback
     * @return 返回的对象，如果发生异常则会直接抛出异常
     */
    public <T> T executeWithCircuitBreaker(String name,
                                           Supplier<T> toRun,
                                           Function<Throwable, T> fallback) {
        if (name == null) {
            throw new IllegalArgumentException("CircuitBreaker name 不能为空");
        }

        CircuitBreaker circuitBreaker = getCircuitBreaker(name);
        TimeLimiter timeLimiter = getTimeLimiter(name);

        Supplier<Future<T>> futureSupplier = () -> executor.submit(toRun::get);

        Callable<T> decorateCallable =
                circuitBreaker.decorateCallable(timeLimiter.decorateFutureSupplier(futureSupplier));

        if (fallback != null) {
            return Try.ofCallable(decorateCallable).recover(fallback).get();
        }
        return Try.ofCallable(decorateCallable).get();
    }

    public CircuitBreaker getCircuitBreaker(String name) {
        Optional<CircuitBreaker> optionalCircuitBreaker = circuitBreakerRegistry.find(name);

        // 已经创建，返回现存的
        if (optionalCircuitBreaker.isPresent()) {
            return optionalCircuitBreaker.get();
        }

        // 不存在，重新获取一遍配置并创建
        CircuitBreaker circuitBreaker;

        Supplier<CircuitBreakerConfig> circuitBreakerConfigSupplier = circuitBreakerConfigSuppliers.get(name);
        if (circuitBreakerConfigSupplier != null) {
            CircuitBreakerConfig circuitBreakerConfig = circuitBreakerConfigSupplier.get();
            if (circuitBreakerConfig != null) {
                circuitBreaker = circuitBreakerRegistry.circuitBreaker(name, circuitBreakerConfig);
            } else {
                circuitBreaker = circuitBreakerRegistry.circuitBreaker(name);
            }
        } else {
            circuitBreaker = circuitBreakerRegistry.circuitBreaker(name);
        }

        return circuitBreaker;
    }

    public TimeLimiter getTimeLimiter(String name) {
        Optional<TimeLimiter> optionalTimeLimiter = timeLimiterRegistry.find(name);

        // 已经创建，返回现存的
        if (optionalTimeLimiter.isPresent()) {
            return optionalTimeLimiter.get();
        }

        // 不存在，重新获取一遍配置并创建
        TimeLimiter timeLimiter;

        Supplier<TimeLimiterConfig> timeLimiterConfigSupplier = timeLimiterConfigSuppliers.get(name);
        if (timeLimiterConfigSupplier != null) {
            TimeLimiterConfig timeLimiterConfig = timeLimiterConfigSupplier.get();
            if (timeLimiterConfig != null) {
                timeLimiter = timeLimiterRegistry.timeLimiter(name, timeLimiterConfig);
            } else {
                timeLimiter = timeLimiterRegistry.timeLimiter(name);
            }
        } else {
            timeLimiter = timeLimiterRegistry.timeLimiter(name);
        }

        return timeLimiter;
    }

    static class NoFallbackException extends RuntimeException {

        public NoFallbackException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
