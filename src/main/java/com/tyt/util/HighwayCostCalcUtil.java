package com.tyt.util;

import net.sourceforge.jeval.EvaluationException;
import net.sourceforge.jeval.Evaluator;

import java.math.BigDecimal;


public class HighwayCostCalcUtil {

	/**
	 *  0：按次数 （ 固定金额）    5
	 1：按公里数  （固定金额X公里数）    0.3
	 2：按公里数   （根据吨位计算金额X公里数）（需要表达式支持）0.06-(T-15)*0.03/25
	 3：按吨公里数    （固定金额X公里数X吨数）    0.03
	 4：按吨公里数   （根据吨位计算金额X公里数X吨数）（需要表达式支持）0.06-(T-15)*0.03/25
	 5：按公里数   （根据吨位计算金额X公里数）（需要ROUNDUP表达式支持）3.355+ROUNDUP((T-20)/5,0)*0.836
	 * @param type  类型参考上述定义
	 * @param tonne   吨位
	 * @param distance  距离
	 * @param ruleValue   表达式/值
	 * @return  省内高速收费金额
	 */
	public static Double calcHighwayCostByType(int type,String tonne,String distance,String ruleValue){

		switch (type) {
			case 0:
				//按固定金额计算
				return calcCostByPrice(ruleValue);
			case 1:
				//按公里数固定金额计算
				return calcCostByDistance(tonne,distance,ruleValue);
			case 2:
				//按公里数表达式计算
				return calcCostByDistanceCalc(tonne,distance, ruleValue);
			case 3:
				//按吨公里数固定金额计算
				return calcCostByTonneDistance(tonne,distance,ruleValue);
			case 4:
				//按吨公里数表达式计算
				return calcCostByTonneDistanceCalc(tonne,distance, ruleValue);
			case 5:
				//按吨公里数拆分ROUNDUP函数表达式计算
				return calcCostByDistanceTwoCalc(tonne,distance, ruleValue);
			default:
				return null;
		}
	}

	/**
	 * 按公里数固定金额计算
	 * @param tonne
	 * @param distance
	 * @param ruleValue
	 * @return
	 */
	private static Double calcCostByPrice(String ruleValue){
		if(ruleValue == null || ruleValue.length() <= 0){
			return null;
		}
		return Double.valueOf(ruleValue);
	}

	/**
	 * 按公里数固定金额计算
	 * @param tonne
	 * @param distance
	 * @param ruleValue
	 * @return
	 */
	private static Double calcCostByDistance(String tonne, String distance, String ruleValue){
		BigDecimal distanceDouble = new BigDecimal(distance);
		BigDecimal valueDouble = new BigDecimal(ruleValue);
		//单段金额 =  公里数  X 每公里金额
		valueDouble = distanceDouble.multiply(valueDouble);

		return valueDouble.doubleValue();
	}

	/**
	 * 按公里数表达式计算
	 * @param tonne
	 * @param distance
	 * @param ruleValue
	 * @return
	 */
	private static Double calcCostByDistanceCalc(String tonne, String distance, String ruleValue){
		BigDecimal distanceDouble = new BigDecimal(distance);

		//计算运费基准数
		BigDecimal baseNumber = calcExpression(ruleValue, tonne);
		if(baseNumber == null || baseNumber.doubleValue() < 0 ){
			return null;
		}

		//单段金额 =  公里数  X 基准数金额
		distanceDouble = distanceDouble.multiply(baseNumber);

		return distanceDouble.doubleValue();
	}

	/**
	 * 按吨公里数固定金额计算
	 * @param tonne
	 * @param distance
	 * @param ruleValue
	 * @return
	 */
	private static Double calcCostByTonneDistance(String tonne, String distance, String ruleValue){

		BigDecimal tonneDouble = new BigDecimal(tonne);
		BigDecimal distanceDouble = new BigDecimal(distance);
		BigDecimal valueDouble = new BigDecimal(ruleValue);
		//单段金额 = 吨数  X 公里数  X 每公里金额
		distanceDouble = tonneDouble.multiply(distanceDouble);
		valueDouble = distanceDouble.multiply(valueDouble);

		return valueDouble.doubleValue();

	}

	/**
	 * 按吨公里数表达式计算
	 * @param tonne
	 * @param distance
	 * @param ruleValue
	 * @return
	 */
	private static Double calcCostByTonneDistanceCalc(String tonne, String distance, String ruleValue){
		BigDecimal tonneDouble = new BigDecimal(tonne);
		BigDecimal distanceDouble = new BigDecimal(distance);

		//计算运费基准数
		BigDecimal baseNumber = calcExpression(ruleValue, tonne);
		if(baseNumber == null || baseNumber.doubleValue() < 0 ){
			return null;
		}

		//单段金额 =  吨位  X 公里数  X 基准数金额
		tonneDouble = tonneDouble.multiply(baseNumber);
		distanceDouble = distanceDouble.multiply(tonneDouble);

		return distanceDouble.doubleValue();
	}

	/**
	 * 按公里数拆分ROUNDUP函数表达式计算
	 * @param tonne
	 * @param distance
	 * @param ruleValue
	 * @return
	 */
	private static Double calcCostByDistanceTwoCalc(String tonne, String distance, String ruleValue){

		BigDecimal distanceDouble = new BigDecimal(distance);

		int start = ruleValue.indexOf("ROUNDUP(");
		int end = ruleValue.indexOf(",");
		if(start < 0 || end  < 0 ){
			System.out.println("表达式解析失败！");
			return null;
		}
		String exprFirst = ruleValue.substring(start+8,end);
		String temp = ruleValue.substring(start,end+3);
		String exprSecond = ruleValue.replace(temp, "T");


		//计算第一步函数内部基准数
		BigDecimal baseNumber = calcExpressionByRoundUp(exprFirst, tonne);
		if(baseNumber == null || baseNumber.doubleValue() < 0 ){
			return null;
		}

		//计算第二步基准数
		baseNumber = calcExpression(exprSecond, Double.toString(baseNumber.doubleValue()));
		if(baseNumber == null || baseNumber.doubleValue() < 0 ){
			return null;
		}

		//单段金额 =  公里数  X 基准数金额
		distanceDouble = distanceDouble.multiply(baseNumber);

		return distanceDouble.doubleValue();
	}



	/**
	 * 按吨公里数拆分函数表达式计算
	 * @param tonne
	 * @param distance
	 * @param ruleValue
	 * @return
	 */
//	 private static Double calcCostByTonneDistanceTwoCalc(String tonne, String distance, String ruleValue){
//
//		 BigDecimal tonneDouble = new BigDecimal(tonne);
//		 BigDecimal distanceDouble = new BigDecimal(distance);
//
//		 String[] ruleValues = ruleValue.split("\\|\\|");
//		 if(ruleValues == null || ruleValues.length != 2){
//			 return null;
//		 }
//		 //计算第一步函数内部基准数
//		 BigDecimal baseNumber = calcExpressionByRoundUp(ruleValues[0], tonne);
//		 if(baseNumber == null || baseNumber.doubleValue() < 0 ){
//			 return null;
//		 }
//
//		//计算第二步基准数
//		 baseNumber = calcExpression(ruleValues[1], Double.toString(baseNumber.doubleValue()));
//		 if(baseNumber == null || baseNumber.doubleValue() < 0 ){
//			 return null;
//		 }
//
//		//单段金额 =  吨位  X 公里数  X 基准数金额
//		 tonneDouble = tonneDouble.multiply(baseNumber);
//		 distanceDouble = distanceDouble.multiply(tonneDouble);
//
//		 return distanceDouble.doubleValue();
//	 }

	/**
	 * 解析表达式，并返回计算出来的基准数
	 * 结果四舍五入6位精度
	 * @param expr
	 * @return
	 */
	private static BigDecimal calcExpression(String expr,String tonne){
		//0.09-(#{T}-10)*0.027/39
		if(expr == null || expr.length() <= 0 || expr.indexOf("T") < 0){
			System.out.println("表达式中不存在可用变量T");
			return null;
		}
		expr = expr.replace("T", "#{T}");

		Evaluator eva = new Evaluator();
		eva.putVariable("T", tonne);  //为表达式变量赋值
		String result = "";
		try {
			result = eva.evaluate(expr); //计算结果
//			System.out.println(result);
		} catch (NumberFormatException e) {
			e.printStackTrace();
			System.out.println("表达式错误，数值转换异常"+expr);
		} catch (EvaluationException e) {
			e.printStackTrace();
			System.out.println("表达式计算错误");
		}

		//计算失败返回NULL
		if(result == null || result.length() <= 0){
			System.out.println("表达式计算为空");
			return null;
		}

		//截取小数后6位精度，返回基准数
		BigDecimal b = new BigDecimal(result);
		b = b.setScale(6, BigDecimal.ROUND_HALF_UP);
		System.out.println("高速每公里费用："+b.doubleValue());
		return b;
	}


	/**
	 * 解析表达式，并返回计算出来的基准数
	 * 结果向上取整
	 * @param expr
	 * @return
	 */
	private static BigDecimal calcExpressionByRoundUp(String expr,String tonne){

		if(expr == null || expr.length() <= 0 || expr.indexOf("T") < 0){
			System.out.println("表达式中不存在可用变量T");
			return null;
		}
		expr = expr.replace("T", "#{T}");

		Evaluator eva = new Evaluator();
		eva.putVariable("T", tonne);  //为表达式变量赋值
		String result = "";
		try {
			result = eva.evaluate(expr); //计算结果
			System.out.println(result);
		} catch (NumberFormatException e) {
			e.printStackTrace();
			System.out.println("表达式错误，数值转换异常"+expr);
		} catch (EvaluationException e) {
			e.printStackTrace();
			System.out.println("表达式计算错误");
		}

		//计算失败返回NULL
		if(result == null || result.length() <= 0){
			System.out.println("表达式计算为空");
			return null;
		}

		//向上取整，返回基准数
		BigDecimal b = new BigDecimal(result);
		b  = b.setScale(0, BigDecimal.ROUND_UP);
		System.out.println(b.doubleValue());
		return b;
	}

	/**
	 * 结果四舍五入小数2位精度
	 * @param value  需要处理的Double值
	 * @return
	 */
	public static Double getNumberTwoPrecision(Double value){
		if(value == null ){
			return null;
		}
		//截取小数后2位精度
		BigDecimal b = new BigDecimal(Double.toString(value));
		value = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//		System.out.println(value);
		return value;
	}

	/**
	 * 结果四舍五入小数4位精度
	 * @param value  需要处理的Double值
	 * @return
	 */
	public static Double getNumberFourPrecision(Double value){
		if(value == null ){
			return null;
		}
		//截取小数后2位精度
		BigDecimal b = new BigDecimal(Double.toString(value));
		value = b.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
//		System.out.println(value);
		return value;
	}


	/**
	 * 结果四舍五入小数6位精度
	 * @param value  需要处理的Double值
	 * @return
	 */
	public static Double getNumberSixPrecision(Double value){
		if(value == null ){
			return null;
		}
		//截取小数后6位精度
		BigDecimal b = new BigDecimal(Double.toString(value));
		value = b.setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
		return value;
	}

	/**
	 * 结果四舍五入小数1位精度
	 * @param value  需要处理的Double值
	 * @return
	 */
	public static Double getNumberOnePrecision(Double value){
		if(value == null ){
			return null;
		}
		//截取小数后1位精度
		BigDecimal b = new BigDecimal(Double.toString(value));
		value = b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		return value;
	}

	/**
	 * 两个Double类型相加
	 * @param doubleVal1
	 * @param doubleVal2
	 * @return
	 */
	public static Double calcInCrease(String doubleVal1,String doubleVal2){
		BigDecimal distanceDouble = new BigDecimal(doubleVal1);
		BigDecimal valueDouble = new BigDecimal(doubleVal2);
		valueDouble = distanceDouble.add(valueDouble);
		return valueDouble.doubleValue();
	}

	/**
	 * 两个Double类型相减
	 * @param doubleVal1
	 * @param doubleVal2
	 * @return
	 */
	public static Double calcSubtract(String doubleVal1,String doubleVal2){
		BigDecimal distanceDouble = new BigDecimal(doubleVal1);
		BigDecimal valueDouble = new BigDecimal(doubleVal2);
		valueDouble = distanceDouble.subtract(valueDouble);
		return valueDouble.doubleValue();
	}

	/**
	 * 两个Double类型相乘
	 * @param doubleVal1
	 * @param doubleVal2
	 * @return
	 */
	public static Double calcMultiply(String doubleVal1,String doubleVal2){
		BigDecimal distanceDouble = new BigDecimal(doubleVal1);
		BigDecimal valueDouble = new BigDecimal(doubleVal2);
		valueDouble = distanceDouble.multiply(valueDouble);
		return valueDouble.doubleValue();
	}

	/**
	 * 两个Double类型相除
	 * @param doubleVal1
	 * @param doubleVal2
	 * @return
	 */
	public static Double calcDivide(String doubleVal1,String doubleVal2){
		BigDecimal distanceDouble = new BigDecimal(doubleVal1);
		BigDecimal valueDouble = new BigDecimal(doubleVal2);
		valueDouble = distanceDouble.divide(valueDouble,4,BigDecimal.ROUND_HALF_UP);
		return valueDouble.doubleValue();
	}

	/**
	 * 结果向上取整
	 * @param value  需要处理的Double值
	 * @return
	 */
	public static Integer getNumberIntHalfUp(Double value){
		if(value == null ){
			return null;
		}
		//向上取整
		BigDecimal b = new BigDecimal(Double.toString(value));
		value = b.setScale(0, BigDecimal.ROUND_UP).doubleValue();
		return value.intValue();
	}

	/**
	 * 解析log表达式，计算运价
	 * 结果向上取整
	 * @param expr
	 * @return
	 */
	public static Double calcMinProfitRates(String expr,String tonne,String distance,String cargoLength,String baseCost){
		if(expr == null || expr.length() <= 0 ){
			System.out.println("表达式不存在");
			return null;
		}
		expr = expr.replace("#{T}", tonne);     //吨位
		expr = expr.replace("#{D}", distance);  //距离
		expr = expr.replace("#{C}", cargoLength);//长度
		expr = expr.replace("#{B}", baseCost);   //成本
		Evaluator eva = new Evaluator();
		String result = "";
		try {
			result = eva.evaluate(expr); //计算结果
			System.out.println(result);
		} catch (NumberFormatException e) {
			e.printStackTrace();
			System.out.println("表达式错误，数值转换异常"+expr);
		} catch (EvaluationException e) {
			e.printStackTrace();
			System.out.println("表达式计算错误");
		}

		//计算失败返回NULL
		if(result == null || result.length() <= 0){
			System.out.println("表达式计算为空");
			return null;
		}

		//向上取整，返回基准数
		BigDecimal b = new BigDecimal(result);
		System.out.println(b.doubleValue());
		return getNumberSixPrecision(b.doubleValue());
	}

	/**
	 * 解析log表达式，计算运价
	 * 结果向上取整
	 * @param expr
	 * @return
	 */
	public static Double calcGuidingProfitRates(String expr,String startSeekCount,String startPubCount,String destSeekCount,String destPubCount){
		if(expr == null || expr.length() <= 0 ){
			System.out.println("表达式不存在");
			return null;
		}
		expr = expr.replace("#{SF}", startPubCount);     //出发地发货
		expr = expr.replace("#{SZ}", startSeekCount);    //出发地找货
		expr = expr.replace("#{DF}", destPubCount);		 //目的地发货
		expr = expr.replace("#{DZ}", destSeekCount);     //目的地找货
		Evaluator eva = new Evaluator();
		String result = "";
		try {
			result = eva.evaluate(expr); //计算结果
			System.out.println(result);
		} catch (NumberFormatException e) {
			e.printStackTrace();
			System.out.println("表达式错误，数值转换异常"+expr);
		} catch (EvaluationException e) {
			e.printStackTrace();
			System.out.println("表达式计算错误");
		}

		//计算失败返回NULL
		if(result == null || result.length() <= 0){
			System.out.println("表达式计算为空");
			return null;
		}

		//向上取整，返回基准数
		BigDecimal b = new BigDecimal(result);
		System.out.println(b.doubleValue());
		return b.doubleValue();
	}

	/**
	 * 解析log表达式，计算运价
	 * 结果向上取整
	 * @param expr
	 * @return
	 */
	public static Double calcMathPowAndSixPrecision(Double value,Double pow){
		if(value == null || pow == null){
			return null;
		}
		value = Math.pow(value,pow);
		return HighwayCostCalcUtil.getNumberSixPrecision(value);
	}
}
