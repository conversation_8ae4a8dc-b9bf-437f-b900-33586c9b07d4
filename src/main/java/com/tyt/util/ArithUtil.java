package com.tyt.util;

import java.math.BigDecimal;

/**
 * Java 精确计算，加减乘除，BigDecimal
 * Created by duanwc on 17/10/12.
 */
public class ArithUtil {
    private ArithUtil() {
    }


    /**
     * 两个Double类型相加
     * @param doubleVal1
     * @param doubleVal2
     * @return
     */
    public static Double calcInCrease(String doubleVal1,String doubleVal2){
        BigDecimal distanceDouble = new BigDecimal(doubleVal1);
        BigDecimal valueDouble = new BigDecimal(doubleVal2);
        valueDouble = distanceDouble.add(valueDouble);
        return valueDouble.doubleValue();
    }

    /**
     * 两个Double类型相减
     * @param doubleVal1
     * @param doubleVal2
     * @return
     */
    public static Double calcSubtract(String doubleVal1,String doubleVal2){
        BigDecimal distanceDouble = new BigDecimal(doubleVal1);
        BigDecimal valueDouble = new BigDecimal(doubleVal2);
        valueDouble = distanceDouble.subtract(valueDouble);
        return valueDouble.doubleValue();
    }

    /**
     * 两个Double类型相乘
     * @param doubleVal1
     * @param doubleVal2
     * @return
     */
    public static Double calcMultiply(String doubleVal1,String doubleVal2){
        BigDecimal distanceDouble = new BigDecimal(doubleVal1);
        BigDecimal valueDouble = new BigDecimal(doubleVal2);
        valueDouble = distanceDouble.multiply(valueDouble);
        return valueDouble.doubleValue();
    }

    /**
     * 两个Double类型相除
     * @param doubleVal1
     * @param doubleVal2
     * @return
     */
    public static Double calcDivide(String doubleVal1,String doubleVal2){
        BigDecimal distanceDouble = new BigDecimal(doubleVal1);
        BigDecimal valueDouble = new BigDecimal(doubleVal2);
        valueDouble = distanceDouble.divide(valueDouble,4,BigDecimal.ROUND_HALF_UP);
        return valueDouble.doubleValue();
    }
}
