package com.tyt.util;

import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cloopen.rest.sdk.CCPRestSDK;
import com.tyt.config.util.AppConfig;

public class VoiceUtil {
	static Logger logger = LoggerFactory.getLogger(VoiceUtil.class);

	/**
	 * 发送语音验证
	 * 
	 * @param verifyCode
	 *            验证码
	 * @param phone
	 *            电话 被叫为座机时需要添加区号，如：***********；被叫为分机时分机号由'-'隔开，如：
	 *            ***********-3627
	 * @return
	 */
	public static boolean sendVoiceVerify(String verifyCode, String phone) {
		HashMap<String, Object> result = null;

		CCPRestSDK restAPI = new CCPRestSDK();
		restAPI.init(AppConfig.getProperty("tyt.voice.verify.server.ip"),
				AppConfig.getProperty("tyt.voice.verify.server.port"));// 初始化服务器地址和端口，格式如下，服务器地址不需要写https://
		restAPI.setAccount(
				AppConfig.getProperty("tyt.voice.verify.accountSid"),
				AppConfig.getProperty("tyt.voice.verify.accountToken"));// 初始化主帐号和主帐号TOKEN
		restAPI.setAppId(AppConfig.getProperty("tyt.voice.verify.appId"));// 初始化应用ID
		result = restAPI.voiceVerify(verifyCode, phone,
				AppConfig.getProperty("tyt.voice.verify.displayNum"), "3", "",
				"zh", "", "", "");

		// System.out.println("SDKTestVoiceVerify result=" + result);
		if ("000000".equals(result.get("statusCode"))) {
			logger.info("发送给电话【" + phone + "】语音验证码【" + verifyCode + "】成功");
			// 正常返回输出data包体信息（map）
			// HashMap<String,Object> data = (HashMap<String, Object>)
			// result.get("data");
			// Set<String> keySet = data.keySet();
			// for(String key:keySet){
			// Object object = data.get(key);
			// System.out.println(key +" = "+object);
			// }
			return true;
		} else {
			// 异常返回输出错误码和错误信息
			logger.error("发送给电话【" + phone + "】语音验证码【" + verifyCode + "】出错，错误码="
					+ result.get("statusCode") + " 错误信息= "
					+ result.get("statusMsg"));
			return false;

			// System.out.println("错误码=" + result.get("statusCode")
			// +" 错误信息= "+result.get("statusMsg"));
		}
	}

	public static void main(String[] args) {
		System.out.println(VoiceUtil.sendVoiceVerify("543543", "18810992178"));
	}
}
