package com.tyt.util;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class CalYearsUtil {

    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 不足一年 返回月数 不足一月 返回天数
     * @param fromDate
     * @param currentDate
     * @return
     */
    public static String calculatePeriod(LocalDate fromDate, LocalDate currentDate) {
        if ((fromDate != null) && (currentDate != null)) {
            int years = Period.between(fromDate, currentDate).getYears();
            int months = Period.between(fromDate, currentDate).getMonths();
            StringBuilder showRegTime = new StringBuilder();
            if(years == 0) {
                return showRegTime.append(months).append("月").toString();
            }
            return new StringBuilder().append(years).append("年").append(months).append("月").toString();

        } else {
            return "0年0月";
        }
    }

    /**
     *
     * @param fromText yyyy-MM-dd HH:mm:ss
     * @param currentDate
     * @return
     */
    public static String calculatePeriod(String fromText, LocalDate currentDate){
        return calculatePeriod(LocalDate.parse(fromText, DateTimeFormatter.ofPattern(DATE_FORMAT)), currentDate);
    }
}
