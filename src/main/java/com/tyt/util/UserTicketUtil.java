package com.tyt.util;

import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import org.apache.commons.lang.StringUtils;


public class UserTicketUtil {

	private static TytConfigService tytConfigService;

	private static CacheService cacheService;


	/**
	 * 获取老版本app TicketKey
	 * @param userId
	 * <AUTHOR>
	 * @return
	 * @date 2020-12-09 14:00:26
	 */
	public static String getObjTicketKey(String userId){
		String ticketChangeValue = getTicketChangeValue();
		return Constant.CACHE_TICKET_KEY.concat(ticketChangeValue).concat(userId);
	}

	/**
	 * 获取车版 TicketKey
	 * @param userId
	 * <AUTHOR>
	 * @return
	 * @date 2020-12-09 14:00:26
	 */
	public static String getCarTicketKey(String userId){
		String ticketChangeValue = getTicketChangeValue();
		return Constant.CACHE_CAR_TICKET_KEY.concat(ticketChangeValue).concat(userId);
	}

	/**
	 * 获取货版 TicketKey
	 * @param userId
	 * <AUTHOR>
	 * @return
	 * @date 2020-12-09 14:00:26
	 */
	public static String getGoodsTicketKey(String userId){
		String ticketChangeValue = getTicketChangeValue();
		return Constant.CACHE_GOODS_TICKET_KEY.concat(ticketChangeValue).concat(userId);
	}

	public static String getMiniProgramGoodsKey(String userId){
		String ticketChangeValue = getTicketChangeValue();
		return Constant.CACHE_MINI_PORGRAM_GOODS_TICKET_KEY.concat(ticketChangeValue).concat(userId);
	}

	/**
	 * 获取小程序个人车主 TicketKey
	 *
	 * @param userId
	 * @return
	 * <AUTHOR>
	 * @date 2020-12-09 14:00:26
	 */
	public static String getMiniProgramTruckerKey(String userId) {
		String ticketChangeValue = getTicketChangeValue();
		return Constant.CACHE_MINI_PORGRAM_TRUCKER_TICKET_KEY.concat(ticketChangeValue).concat(userId);
	}



	/**
	 * 获取ticketChangeValue（全部踢出是通过改变 tytConfig 配置项来实现，所以需要从cache中获取 ticket_change_key）
	 * @param
	 * <AUTHOR>
	 * @return
	 * @date 2020-12-09 14:04:06
	 */
	private static String getTicketChangeValue() {
		if(tytConfigService == null){
			tytConfigService = ApplicationContextUtils.getBean(TytConfigService.class);
		}
		String ticket_change_key = tytConfigService.getStringValue("ticket_change_key", "");
		if(StringUtils.isNotBlank(ticket_change_key)){
			return ticket_change_key.trim();
		}
		return "";
	}

	/**
	 * 踢出用户（所有客户端）
	 * @param userId
	 * <AUTHOR>
	 * @return
	 * @date 2020-12-09 14:00:26
	 */
	public static void kickOutAllClient(String userId){
		String objTicketKey = getObjTicketKey(userId);
		if(cacheService == null){
			cacheService = ApplicationContextUtils.getBean("cacheServiceMcImpl");
		}
		Integer ex = AppConfig.getIntProperty("tyt.cache.user.time");
		cacheService.setObject(objTicketKey, "00000", ex);
		cacheService.setObject(Constant.CACHE_OLD_TICKET_KEY.concat(objTicketKey), "00000", ex);
		String carTicketKey = getCarTicketKey(userId);
		cacheService.setObject(carTicketKey, "00000", ex);
		cacheService.setObject(Constant.CACHE_OLD_TICKET_KEY.concat(carTicketKey), "00000", ex);
		String goodsTicketKey = getGoodsTicketKey(userId);
		cacheService.setObject(goodsTicketKey, "00000", ex);
		cacheService.setObject(Constant.CACHE_OLD_TICKET_KEY.concat(goodsTicketKey), "00000", ex);
		String miniProgramTruckerKey = getMiniProgramTruckerKey(userId);
		cacheService.setObject(miniProgramTruckerKey, "00000", ex);
		cacheService.setObject(Constant.CACHE_OLD_TICKET_KEY.concat(miniProgramTruckerKey), "00000", ex);
	}
}
