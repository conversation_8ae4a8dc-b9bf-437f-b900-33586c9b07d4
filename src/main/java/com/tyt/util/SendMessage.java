package com.tyt.util;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SendMessage {
	private static Logger logger = LoggerFactory.getLogger(SendMessage.class);

	public static void sendMessage(String cellPhone, String msg) {
		try {
			HttpClient client = new HttpClient();
			PostMethod post = new PostMethod("http://utf8.sms.webchinese.cn");
			post.addRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");// 在头文件中设置转码
			NameValuePair[] data = { new NameValuePair("Uid", "teyuntong"), new NameValuePair("Key", "f631e6e38de4549d1688"), new NameValuePair("smsMob", cellPhone), new NameValuePair("smsText", msg) };
			post.setRequestBody(data);
			client.executeMethod(post);
			// Header[] headers = post.getResponseHeaders();
			// int statusCode = post.getStatusCode();
			// System.out.println("statusCode:"+statusCode);
			// for(Header h : headers)
			// {
			// System.out.println(h.toString());
			// }
			// String result = new
			// String(post.getResponseBodyAsString().getBytes("utf-8"));
			// System.out.println(result);
			post.releaseConnection();
		} catch (HttpException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public static boolean sendSmsMessage(String cellPhone, String msg) {

		try {
			HttpClient client = new HttpClient();
			PostMethod post = new PostMethod("http://utf8.sms.webchinese.cn");
			post.addRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");// 在头文件中设置转码
			NameValuePair[] data = { new NameValuePair("Uid", "teyuntong"), new NameValuePair("Key", "f631e6e38de4549d1688"), new NameValuePair("smsMob", cellPhone), new NameValuePair("smsText", msg) };
			post.setRequestBody(data);
			client.executeMethod(post);
			int statusCode = post.getStatusCode();
			if (200 == statusCode) {
				String result = new String(post.getResponseBodyAsString().getBytes("utf-8"));
				post.releaseConnection();
				if ("1".equals(result)) {
					return true;
				} else
					return false;

			} else {
				post.releaseConnection();
				return false;
			}
		} catch (HttpException e) {
			e.printStackTrace();
			return false;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 0 提交成功 1 含有敏感词汇 2 余额不足 3 没有号码 4 包含sql语句 10 账号不存在 11 账号注销 12 账号停用 13
	 * IP鉴权失败 14 格式错误 -1 系统异常
	 * 
	 * @param cellPhone
	 * @param msg
	 * @return
	 */
	public static Integer sendMessageWithNewPlat(String cellPhone, String msg) {
		String result = null;
		try {
			// 发送内容
			String sign = "特运通";
			// 创建StringBuffer对象用来操作字符串
			StringBuffer sb = new StringBuffer("http://web.cr6868.com/asmx/smsservice.aspx?");
			// 向StringBuffer追加用户名
			sb.append("name=13301017272");
			// 向StringBuffer追加密码（登陆网页版，在管理中心--基本资料--接口密码，是28位的）
			sb.append("&pwd=44AEDD74EC2B84609B11B76DEBBD");
			// 向StringBuffer追加手机号码
			sb.append("&mobile=" + cellPhone);
			// 向StringBuffer追加消息内容转URL标准码
			sb.append("&content=" + URLEncoder.encode(msg, "UTF-8"));
			// 追加发送时间，可为空，为空为及时发送
			sb.append("&stime=");
			// 加签名
			sb.append("&sign=" + URLEncoder.encode(sign, "UTF-8"));
			// type为固定值pt extno为扩展码，必须为数字 可为空
			sb.append("&type=pt&extno=");
			// 创建url对象
			URL url = new URL(sb.toString());
			// 打开url连接
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			// 设置url请求方式 ‘get’ 或者 ‘post’
			connection.setRequestMethod("POST");
			// 发送
			InputStream is = url.openStream();
			// 转换返回值
			result = convertStreamToString(is);
			logger.info("send message result is: " + result);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return Integer.valueOf(result.split(",")[0]);
	}

	/**
	 * 转换返回值类型为UTF-8格式.
	 * 
	 * @param is
	 * @return
	 */
	public static String convertStreamToString(InputStream is) {
		StringBuilder sb1 = new StringBuilder();
		byte[] bytes = new byte[4096];
		int size = 0;
		try {
			while ((size = is.read(bytes)) > 0) {
				String str = new String(bytes, 0, size, "UTF-8");
				sb1.append(str);
			}
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return sb1.toString();
	}

	public static void main(String[] args) {
		System.out.println(sendMessageWithNewPlat("17090097452", "亲爱的用户，欢迎注册特运通，验证码111111，10分钟内有效，请勿将验证码泄露给他人。"));
	}
}
