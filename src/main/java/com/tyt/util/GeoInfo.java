package com.tyt.util;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@ToString
public class GeoInfo {
    private String longitude;
    private String latitude;
    private String location;

    private String province;

    private String city;

    private String district;

    public GeoInfo splitLocation() {
        if (StringUtils.isNotEmpty(this.location)) {
            String[] locations = location.split(",");
            longitude = locations[0];
            latitude = locations[1];
        }
        return this;
    }
}
