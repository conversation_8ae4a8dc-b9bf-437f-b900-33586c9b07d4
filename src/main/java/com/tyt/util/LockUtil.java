package com.tyt.util;

import com.tyt.service.common.redis.RedisUtil;

/**
 * 用于货物状态修改的加锁工具类
 * 
 * <AUTHOR>
 * 
 */
public class LockUtil {
	final static String REDIS_LOCK_CACHE_KEY = "tyt_redis_lock_key_";
	final static int REDIS_LOCK_CACHE_TIME_OUT = 60;

	/**
	 * 获取对像的锁
	 * 
	 * @param type
	 *            1是锁货物 srcMsgId,2是锁意向表ID
	 * @param key
	 *            要锁定的对像
	 * @param timeOut
	 *            超时时间单位秒 ，不要大于60秒，一般在10秒内
	 * @return true 获得锁，false 超时没有获得
	 */
	public static boolean lockObject(String type, String key, int timeOut) {
		long outTime = System.currentTimeMillis() + timeOut * 1000;
		while (System.currentTimeMillis() <= outTime) {
			boolean isSuccess = RedisUtil.setNxAtom(REDIS_LOCK_CACHE_KEY + type + "_" + key, key, REDIS_LOCK_CACHE_TIME_OUT);
			if (isSuccess) {
				return true;
			} else {
				try {
					Thread.sleep(100);
				} catch (InterruptedException e) {
					e.printStackTrace();
					return false;
				}
			}
		}
		return false;
	}

	/**
	 * 解锁对象
	 * 
	 * @param type
	 *            1是锁货物 srcMsgId,2是锁意向表ID
	 * @param key
	 *            被加密的对象
	 */
	public static void unLockObject(String type, String key) {
		RedisUtil.del(REDIS_LOCK_CACHE_KEY + type + "_" + key);
	}
}
