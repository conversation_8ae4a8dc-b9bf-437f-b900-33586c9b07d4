package com.tyt.util.tpay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.tyt.config.util.AppConfig;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.common.security.SecureLinkUtil;
import com.tyt.util.tpay.httpclient.HttpClient;
import com.tyt.util.tpay.httpclient.HttpRequestSimple;
import com.tyt.util.tpay.httpclient.ResponseStatus;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.TreeMap;

/**
 * @ClassName TpayUtil
 * @Description 收银台调用工具类
 * <AUTHOR>
 * @Date 2020-01-14 11:36
 * @Version 1.0
 */
public class TpayUtil {

    private static final Logger logger = LoggerFactory.getLogger(TpayUtil.class);

    public static HttpClient httpClient = new HttpClient();
    //MD5
    private static final String secretKey =  AppConfig.getProperty("tpay.secretKey");
    //RSA
    private static final String privateKey = AppConfig.getProperty("tpay.privateKey");
    private static final String publicKey =AppConfig.getProperty("tpay.publicKey");
    private static final String signType = AppConfig.getProperty("tpay.signType");

    /**
     * @Description  发送post请求的方法 -- 普通表单参数post方式请求
     * <AUTHOR>
     * @Date  2020/1/14 12:21
     * @Param [url, params, headers]
     * @return com.tyt.model.ResultMsgBean
     **/
    public static ResultMsgBean sendPostRequest(String url, Object params, Object headers) throws Exception {
        //请求体Map集合
        Map <String,String> paramMap = BeanUtils.describe(params);
        //请求头Map集合
        Map<String,String> headerMap = BeanUtils.describe(headers);
        String text = SecureLinkUtil.createLinkString(paramMap);
        String signKey = "";
        if ("RSA".equals(signType)) {
            signKey = privateKey;
        } else if ("MD5".equals(signType)) {
            signKey = secretKey;
        }
        // RSA签名测试
        String sign = SecureLinkUtil.sign(text, signKey, signType);
        paramMap.put("sign",sign);
        paramMap.put("signType",signType);
        //请求后的返回对象
        ResponseStatus responseStatus = httpClient.post(url, paramMap, headerMap);
        String content = responseStatus.getContent();
        ResultMsgBean resultMsgBean = JSON.parseObject(content, ResultMsgBean.class);
        return resultMsgBean;
    }
    
    /**
     * @description 发送post请求的方法 -- application/json body数据
     * <AUTHOR>
     * @date 2020/6/9 14:08
     * @param url 请求的url
     * @param bodyParams 发送的body对象
     * @return java.lang.String
     */
    public static String sendBodyRequest(String url, Object bodyParams) throws Exception {
        //将body对象转换成json字符串
        String body = JSON.toJSONString(bodyParams);
        //对body进行滤空、排序
        TreeMap treeMapBody = JSON.parseObject(body, TreeMap.class, Feature.SortFeidFastMatch);
        //将TreeMap重新转化为json字符串
        body = JSON.toJSONString(treeMapBody, SerializerFeature.MapSortField);

        //根据签名方式是RSA还是MD5，取出签名的秘钥
        String signKey = "";
        if ("RSA".equals(signType)) {
            signKey = privateKey;
        } else if ("MD5".equals(signType)) {
            signKey = secretKey;
        }
        //将body进行签名
        String sign =  SecureLinkUtil.sign(body, signKey, signType);
        logger.info("生成的签名sign为【{}】", sign);

        //发送http请求，返回请求后的结果数据
        String result = HttpRequestSimple.getInstance().postSendHttp(url, body, sign);
        return result;
    }


    /**
     * @description 发送post请求的方法 -- application/json body数据
     * <AUTHOR>
     * @date 2020/6/9 14:08
     * @param url 请求的url
     * @param bodyParams 发送的body对象
     * @param token tpay收银台生成token，用于鉴权
     * @return java.lang.String
     */
    public static String sendBodyRequest(String url, Object bodyParams, String token) throws Exception {
        //将body对象转换成json字符串
        String body = JSON.toJSONString(bodyParams);
        //对body进行滤空、排序
        TreeMap treeMapBody = JSON.parseObject(body, TreeMap.class, Feature.SortFeidFastMatch);
        //将TreeMap重新转化为json字符串
        body = JSON.toJSONString(treeMapBody, SerializerFeature.MapSortField);

        //根据签名方式是RSA还是MD5，取出签名的秘钥
        String signKey = "";
        if ("RSA".equals(signType)) {
            signKey = privateKey;
        } else if ("MD5".equals(signType)) {
            signKey = secretKey;
        }
        //将body进行签名
        String sign =  SecureLinkUtil.sign(body, signKey, signType);
        logger.info("生成的签名sign为【{}】", sign);

        //发送http请求，返回请求后的结果数据
        String result = HttpRequestSimple.getInstance().postSendHttp(url, body, sign, token);
        return result;
    }

    /**
     * 获取用户真实IP地址，不使用request.getRemoteAddr();的原因是有可能用户使用了代理软件方式避免真实IP地址,
     *
     * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值，究竟哪个才是真正的用户端的真实IP呢？
     * 答案是取X-Forwarded-For中第一个非unknown的有效IP字符串。
     *
     * 如：X-Forwarded-For：*************, *************, *************,
     * *************
     *
     * 用户真实IP为： *************
     *
     * @param request
     * @return
     */
     public final static String getIpAddress(HttpServletRequest request)  {
        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址

        String ip = request.getHeader("X-Forwarded-For");
        try{
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("Proxy-Client-IP");

                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("WL-Proxy-Client-IP");

                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("HTTP_CLIENT_IP");
                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("HTTP_X_FORWARDED_FOR");
                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {

                    ip = request.getRemoteHost();//getRemoteAddr();
                    String x ="df";
                }
            } else if (ip.length() > 15) {
                String[] ips = ip.split(",");
                for (int index = 0; index < ips.length; index++) {
                    String strIp = ips[index];
                    if (!("unknown".equalsIgnoreCase(strIp))) {
                        ip = strIp;
                        break;
                    }
                }
            }

        } catch (Exception e) {
            // TODO Auto-generated catch block
            return ip;
        }
        return ip;
     }
}
