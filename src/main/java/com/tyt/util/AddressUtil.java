package com.tyt.util;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import com.tyt.service.common.amap.api.AMapServiceAPI;
import com.tyt.service.common.amap.service.IPInfo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 根据IP地址获取详细的地域信息
 */
public class AddressUtil {

	private static Logger ipLogger = LoggerFactory.getLogger("ipLog");

	/**
	 * 
	 * @param content
	 *            请求的参数 格式为：name=xxx&pwd=xxx
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String getAddresses(String content) {
		if(StringUtils.isEmpty(content)){
			return null;
		}
		// 这里调用pconline的接口
//		String urlStr = "http://ip.taobao.com/service/getIpInfo.php";
		try {
			// 从http://whois.pconline.com.cn取得IP所在的省市区信息
			IPInfo ipInfo = AMapServiceAPI.getIPInfo(content);
			 System.out.println(ipInfo.toString());
			if (ipInfo != null) {
				// 处理返回的省市区信息
				String region = (ipInfo.getProvince());
				System.out.println(region);
				String city = (ipInfo.getCity());
				System.out.println(city);
				ipLogger.info("获取ip地址：【content：{}】 正确【{}】",
						content,  region + city);
				return region + city;
			}

		} catch (Exception e) {
			// TODO Auto-generated catch block
			ipLogger.info("获取ip：【content：{}】获取ip异常,【{}】",
					content, e.toString());
		}
		return null;
	}

	public static Map<String,String> getAddressesMap(String content ) {
		if(StringUtils.isEmpty(content)){
			return null;
		}
		// 这里调用pconline的接口
		Map<String,String> areaMap=new HashMap<String,String>();
//		String urlStr = "http://ip.taobao.com/service/getIpInfo.php";
		try {
			
			// 从http://whois.pconline.com.cn取得IP所在的省市区信息
			IPInfo ipInfo = AMapServiceAPI.getIPInfo(content);
			System.out.println(ipInfo.toString());
			if (ipInfo != null) {
				// 处理返回的省市区信息
				String region = (ipInfo.getProvince());
				if(region==null||region.trim().length()<=0){return null;}
				areaMap.put("province", region);
				String city = (ipInfo.getCity());
				areaMap.put("city", city);
				ipLogger.info("获取ip地址：【content：{}】 正确【{}】",
						content,  region + city);
				return areaMap;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			ipLogger.info("获取ip：【content：{}】获取ip异常,【{}】",
					content, e.toString());
			
		}
		return areaMap;
	}
	
//	/**
//	 * @param urlStr
//	 *            请求的地址
//	 * @param content
//	 *            请求的参数 格式为：name=xxx&pwd=xxx
//	 * @param encoding
//	 *            服务器端请求编码。如GBK,UTF-8等
//	 * @return
//	 */
//	private static String getResult(String urlStr, String content,
//			String encoding) {
//		URL url = null;
//		HttpURLConnection connection = null;
//		try {
//			url = new URL(urlStr);
//			connection = (HttpURLConnection) url.openConnection();// 新建连接实例
//			connection.setConnectTimeout(5000);// 设置连接超时时间，单位毫秒
//			connection.setReadTimeout(5000);// 设置读取数据超时时间，单位毫秒
//			connection.setDoOutput(true);// 是否打开输出流 true|false
//			connection.setDoInput(true);// 是否打开输入流true|false
//			connection.setRequestMethod("POST");// 提交方法POST|GET
//			connection.setUseCaches(false);// 是否缓存true|false
//			connection.connect();// 打开连接端口
//			DataOutputStream out = new DataOutputStream(
//					connection.getOutputStream());// 打开输出流往对端服务器写数据
//			out.writeBytes(content);// 写数据,也就是提交你的表单 name=xxx&pwd=xxx
//			out.flush();// 刷新
//			out.close();// 关闭输出流
//			BufferedReader reader = new BufferedReader(new InputStreamReader(
//					connection.getInputStream(), encoding));// 往对端写完数据对端服务器返回数据
//			// ,以BufferedReader流来读取
//			StringBuffer buffer = new StringBuffer();
//			String line = "";
//			while ((line = reader.readLine()) != null) {
//				buffer.append(line);
//			}
//			reader.close();
//			return buffer.toString();
//		} catch (IOException e) {
//			ipLogger.info("【content：{}】【urlStr：{}】【encoding：{}】获取ip异常,【{}】",
//					content, urlStr, encoding, e.toString());
//		} finally {
//			if (connection != null) {
//				connection.disconnect();// 关闭连接
//			}
//		}
//		return null;
//	}

	/**
	 * unicode 转换成 中文
	 * 
	 * <AUTHOR> 2007-3-15
	 * @param theString
	 * @return
	 */
	private static String decodeUnicode(String theString) {
		char aChar;
		int len = theString.length();
		StringBuffer outBuffer = new StringBuffer(len);
		for (int x = 0; x < len;) {
			aChar = theString.charAt(x++);
			if (aChar == '\\') {
				aChar = theString.charAt(x++);
				if (aChar == 'u') {
					int value = 0;
					for (int i = 0; i < 4; i++) {
						aChar = theString.charAt(x++);
						switch (aChar) {
						case '0':
						case '1':
						case '2':
						case '3':
						case '4':
						case '5':
						case '6':
						case '7':
						case '8':
						case '9':
							value = (value << 4) + aChar - '0';
							break;
						case 'a':
						case 'b':
						case 'c':
						case 'd':
						case 'e':
						case 'f':
							value = (value << 4) + 10 + aChar - 'a';
							break;
						case 'A':
						case 'B':
						case 'C':
						case 'D':
						case 'E':
						case 'F':
							value = (value << 4) + 10 + aChar - 'A';
							break;
						default:
							throw new IllegalArgumentException(
									"Malformed      encoding.");
						}
					}
					outBuffer.append((char) value);
				} else {
					if (aChar == 't') {
						aChar = '\t';
					} else if (aChar == 'r') {
						aChar = '\r';
					} else if (aChar == 'n') {
						aChar = '\n';
					} else if (aChar == 'f') {
						aChar = '\f';
					}
					outBuffer.append(aChar);
				}
			} else {
				outBuffer.append(aChar);
			}
		}
		return outBuffer.toString();
	}

	// 测试
	public static void main(String[] args) {
		// 测试ip *************** 中国=华南=广东省=广州市=越秀区=电信
		String ip = "*************";
		String address = "";
		address = getAddresses( ip );
		System.out.println(address);
		// 输出结果为：广东省,广州市,越秀区
	}
}
