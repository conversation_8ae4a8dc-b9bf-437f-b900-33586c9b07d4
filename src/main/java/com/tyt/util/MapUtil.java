package com.tyt.util;

import com.alibaba.fastjson.JSONObject;
import com.tyt.util.httputil.HttpUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;

/**
 * 高德地图工具类
 */
public class MapUtil {
    //地理/逆地理编码  doc ：https://lbs.amap.com/api/webservice/guide/api/georegeo
    private static  final String RESTAPI_URL = "http://restapi.amap.com/v3/geocode/geo?address={0}&key={1}&city={2}";

    private static final String KEY = "b0700c3768c5595574a80da552211266";

    public static GeoInfo getGeoInfo(String address,String city) {
        String url = MessageFormat.format(RESTAPI_URL, address, KEY,city);
        try {
            String resultStr = HttpUtil.get(url, null);
            if (StringUtils.isEmpty(resultStr)) {
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(resultStr);
            if (!(jsonObject.get("status").equals("1") && jsonObject.get("infocode").equals("10000"))) {
                return null;
            }
            JSONObject json = jsonObject.getJSONArray("geocodes").getJSONObject(0);
            return JSONObject.toJavaObject(json, GeoInfo.class).splitLocation();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static void main(String[] args) {
        GeoInfo geoInfo = getGeoInfo("山西省邢台市兖州区山推机械","邢台市");
        System.out.println(geoInfo);
    }
}
