package com.tyt.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;


/**
 * @Title: SignUtil.java 
 * @Description:
 *
 *  加/解签名工具类
 */
public class SignUtil {
	private static final  Logger logger = LoggerFactory.getLogger(SignUtil.class);
	
	/**
	 * 
	 * @param params 要验证的参数map
	 * @param sign 签名字符串
	 * @param secret 秘钥
	 * @return 验证通过返回真，失败返回false
	 */
    public static boolean verifySignature(TreeMap<String,String> params,String sign,String secret){
        String strSign =  sign(params,secret);
        if(sign.equals(strSign)){
            return true;
        }
        return false;
    }

   /**
    * 
    * @param sortedParams 签名的参数
    * @param secret 秘钥
    * @return 签名后的字符串
    */
    public static String sign(TreeMap<String, ?> sortedParams,String secret){
        //组织原始签名数据
        String signOriginal = sortedParams.entrySet().stream()
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"))
                .concat(secret);
        String sign = Encoder.md5(signOriginal);
        logger.info("签名前字符串：【"+ signOriginal+"】签名后字符串：【" + sign + "】");
        return sign;
    }

    /**
     *
     * @param sortedParams 签名的参数
     * @param secret 秘钥
     * @return 签名后的字符串
     */
    public static String sign(Map<String, ?>  sortedParams,String secret){

        //组织原始签名数据
        String signOriginal = sortedParams.entrySet().stream()
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"))
                .concat(secret);
        String sign = Encoder.md5(signOriginal);
        logger.info("签名前字符串：【"+ signOriginal+"】签名后字符串：【" + sign + "】");
        return sign;
    }

    /**
     * 二进制转字符串
    * @param b
    * @return
     */
    public static String byte2hex(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1)
                hs = hs + "0" + stmp;
            else
                hs = hs + stmp;
        }
        return hs.toUpperCase();
    }

    /**
     * 使用指定的字符集编码请求参数值。
     *
     * @param value 参数值
     * @param charset 字符集
     * @return 编码后的参数值
     */
    public static String encode(String value, String charset) {
        String result = null;
        if (null!=value&& !"".equals(value)) {
            try {
                result = URLEncoder.encode(value, charset);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    public static Map<String, String> getParamsFromUrl(String url) {
        Map<String, String> map = null;
        if (url != null && url.indexOf('?') != -1) {
            map = splitUrlQuery(url.substring(url.indexOf('?') + 1));
        }
        if (map == null) {
            map = new HashMap<String, String>();
        }
        return map;
    }

    /**
     * 从URL中提取所有的参数。
     *
     * @param query URL地址
     * @return 参数映射
     */
    public static Map<String, String> splitUrlQuery(String query) {
        Map<String, String> result = new HashMap<String, String>();

        String[] pairs = query.split("&");
        if (pairs != null && pairs.length > 0) {
            for (String pair : pairs) {
                String[] param = pair.split("=", 2);
                if (param != null && param.length == 2) {
                    result.put(param[0], param[1]);
                }
            }
        }

        return result;
    }
    
    
    public static void main(String[] args) {
		String se="1345~opo-4%";
		TreeMap<String,String> tree=new TreeMap<String,String>();
		
		//token=f5c034fcd7459ab9ee143de2ed7fb2f1&a1=v1&a2=v2&ka=t3&a3=jk&sign=34e3c66dc4e77fc36fc3f5a2a9cc048c
		
//		tree.put("token", "f5c034fcd7459ab9ee143de2ed7fb2f1");
//		tree.put("a1", "v1");
//		tree.put("a2", "v2");
//		tree.put("ka", "t3");
//		tree.put("a3", "");
//		tree.put("a4", "中国");
		tree.put("cellPhone", "18210241342");
		tree.put("password", "e10adc3949ba59abbe56e057f20f883e18210241342");
//		tree.put("title", "1");
//		tree.put("content", "号码");
		tree.put("clientSign", "3");
//		tree.put("osVersion", "ios");
		tree.put("clientVersion", "3000");
//		tree.put("ticket", "c94192fe1fe2ac4b3c1cb359fa6b3895");
//		tree.put("userId", "4645");
//		tree.put("oldPassword", "e10adc3949ba59abbe56e057f20f883e");
//		tree.put("newPassword", "e10adc3949ba59abbe56e057f20f883e");
//		tree.put("cellPhone", "22222222222");
//		tree.put("clientId", "1234567890");
//		tree.put("userSign", "7");
		//client_sign=1&os_version=ios&client_version=2202&client_id=1234567890
	//8e51e6e7eb62b342602d950c647c9b00
    	String aa=SignUtil.sign(tree, se);
    	System.out.println(aa);
	}
   // http://localhost:9090/tyt_plat/plat/user/password/update?userId=4645&clientSign=2&clientVersion=2015&oldPassword=e10adc3949ba59abbe56e057f20f883e&sign=c333e3ba3b0ba55994457571b3954341&ticket=51aeb3bfc0e6050a1e4d94acea3eeb3b&newPassword=e10adc3949ba59abbe56e057f20f883e
  //http://localhost:9090/tyt_plat/plat/user/save?cellPhone=22222222222&password=0f1b4f707949803b9a5d596272f54547&userSign=7&clientSign=2&clientVersion=2015&sign=9f7ce5391c3abd93db9fe9f4175ce8d8
  // http://localhost:9090/tyt_plat/plat/user/get?clientSign=2&clientVersion=2015&userId=4645&ticket=51aeb3bfc0e6050a1e4d94acea3eeb3b&sign=093fa97692c6b0e18a5e8a0ba8227e23
  //http://localhost:9090/tyt_plat/plat/user/login?clientSign=2&clientVersion=2015&cellPhone=15201118690&password=c559743808a518160a64f6f9ffd44fbc&sign=468aa253d7ceb5e78b4c07741c3e569a
    //ticket=c94192fe1fe2ac4b3c1cb359fa6b3895
    //http://localhost:9090/tyt_plat/plat/user/advice/save?clientSign=2&clientVersion=2015&userId=4645&ticket=c94192fe1fe2ac4b3c1cb359fa6b3895&title=1&content=号码&sign=c866b4c1a286a4b6728e09f6ab7941d3
}
