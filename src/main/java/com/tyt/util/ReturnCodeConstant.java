package com.tyt.util;

/**
 * 返回给客户端 code码定义类
 *
 * <AUTHOR>
 *
 */
public class ReturnCodeConstant {

	/**** 正确结果 **/
	public static final int OK = 200;

	/**** 错误结果 **/
	public static final int ERROR = 500;

	/**
	 * 基础参数错误
	 */
	public static final int BASIC_PARAMETER_ERROR = 1001;

	/**
	 * 无效签名 code
	 */
	public static final int INVALID_SIGNATURE_CODE = 1002;
	/**
	 * 无效签名 message
	 */
	public static final String INVALID_SIGNATURE_MSG = "无效签名";

	/**
	 * 未登录 code
	 */
	public static final int NOT_LOGGED_IN_CODE = 1003;

	/**
	 * 拉黑踢登code码
	 */
	public static final int BLACKLIST_NOT_LOGGED_IN_CODE = 100301;

	/**
	 * 拉黑踢登message
	 */
	public static final String BLACKLIST_NOT_LOGGED_IN_MSG = "用户拉黑踢出登录";

	/**
	 * 未登录 message
	 */
	public static final String NOT_LOGGED_IN_MSG = "未登录";
	/**
	 * 统一参数错误 code
	 */
	public static final int ARGUMENTS_ERROR_CODE = 2000;
	/**
	 * 注册错误 code
	 */
	public static final int REGISTER_ERROR_CODE = 2001;

	/**
	 * 登陆错误 code
	 */
	public static final Integer LOGIN_ERROR_CODE = 2002;
	/**
	 * 请求参数为空 code
	 */
	public static final int ARGUMENTS_IS_NULL_CODE = 2003;
	/**
	 * 类型错误 code
	 */
	public static final int TYPE_ERROR_CODE = 2004;
	/**
	 * 图片错误 code
	 */
	public static final int IMAGE_ERROR_CODE = 2005;
	/**
	 * 客户端升级 code
	 */
	public static final int VERSION_UPGADE_CODE = 2006;

	/**
	 * 客户端升级 code
	 */
	public static final int ANZHUO_VERSION_UPGADE_CODE = 200601;
	/**
	 * 重复信息code
	 */
	public static final int DATA_HAS_EXIT = 2007;

	/**
	 * 全局验证错误
	 */
	public static final Integer INFO_REFUSE_UPLOAD_CODE = 2008;
	/**
	 * 对象不存在code
	 */
	public static final int OBJECT_IS_NOT_EXIT_CODE = 2009;
	/**
	 * 订单拒绝code
	 */
	public static final int ORDER_IS_REFUSE_CODE = 2010;
	/**
	 * 订单同意code
	 */
	public static final int ORDER_IS_AGREE_CODE = 2011;

	/**
	 * 订单无法取消
	 */
	public static final int ORDER_IS_NO_CANCEL_CODE = 2012;
	/**
	 * 该手机号已绑定微信小程序
	 */
	public static final int PHONE_HAS_BEEN_BOUND = 2013;

	/**
	 * 绑定账号不可更改
	 */
	public static final int BOUND_PHONE_CANT_CHANGE = 2014;
	/**
	 * 手机号不可变更-审核中
	 */
	public static final int BOUND_PHONE_CANT_CHANGE_AUDIT = 2015;
	/**
	 * 手机号不可变更-变更频繁
	 */
	public static final int BOUND_PHONE_CANT_CHANGE_TIME = 2016;
	public static final int BOUND_PHONE_CANT_CHANGE_PHONE_REPEAT = 2017;
	/**
	 * 用户不存在
	 */
	public static final int USER_NON = 2018;
	/**
	 * 手机号码格式错误
	 */
	public static final int PHONE_FORMAT_ERROR = 2019;
	/**
	 * 小程序没密码登录的用户
	 */
	public static final int APPLETS_LOGIN_PASSWORD = 2021;
	/**
	 * 其它错误code
	 */
	public static final int OTHER_ERROR = 3001;
	/**
	 * 超过限制
	 */
	public static final int MORE_THAN_LIMIT = 3002;
	/**
	 * 验证码超时
	 */
	public static final int VERIFICATION_CODE_TIMEOUT = 3003;
	/**
	 * 调用短信平台发送验证码失败
	 */
	public static final int CALL_MESSAGE_PLAT_FAILED = 3004;
	/**
	 * 其他客户端登录code
	 */
	public static final int REMOTE_LOGIN_CODE = 1004;

	/**
	 * 版本错误
	 */
	public static final int VERSION_ERROR_CODE = 1005;
	/**
	 * 不允许删除
	 */
	public static final int NOT_ALLOWED_DELETE = 4000;
	/**
	 * 接单受限
	 */
	public static final int ORDER_LIMITED = 4003;
	/**
	 * 不能撤销
	 */
	public static final int INFO_FEE_NOT_ALLOWED_CANCEL = 5000;//货物信息不允许撤销
	public static final int INFO_FEE_NOT_ALLOWED_DEAL = 5001;//货物信息不允许设置成交
	public static final int INFO_FEE_NOT_ALLOWED_TO_PAY = 5002;//货物信息不允许支付
	public static final int INFO_FEE_FAIL_TO_MAKE_ORDER = 5003;//下单失败
	public static final int INFO_FEE_OPERATE_DATA_DISABLED = 5004;//数据无效，不能进行再此操作
	public static final int INFO_FEE_ONLY_AGREE_ONE_CAROWNER = 8000;//数据无效，不能进行再此操作

	public static final int publish_type_fee_null = 9001;//一口价货源信息费和运费不能为空
	public static final int fee_must_not_null = 9002;//信息费和运费不能为空
	public static final int fee_not_match = 9003;//一口价货源信息费和运费不能改变
	public static final int price_check_error = 9004;//当前运费过低，请填写合理运费！
	public static final int shunting_quantity_error = 9005;//一口价货源调车数量不能为空 或者 一口价货源调车数量不能大于1



	/**
	 * 其他客户端登录message
	 */
	public static final String REMOTE_LOGIN_MSG = "其他客户端登录";
	public static final String ARGUMENTS_CELLPHONE_IS_NULL_MSG = "账号不能为空";
	public static final String ARGUMENTS_TEL_IS_NULL_MSG = "联系人电话不能为空";
	public static final String ARGUMENTS_PASSWORD_IS_NULL_MSG = "密码不能为空";
	public static final String ARGUMENTS_USERSIGN_IS_NULL_MSG = "用户身份不能为空";
	public static final String ARGUMENTS_ADVICE_TITLE_IS_NULL_MSG = "反馈标题不能为空";
	public static final String ARGUMENTS_ADVICE_CONTENT_IS_NULL_MSG = "反馈内容不能为空";
	public static final String ARGUMENTS_USER_IDENTITY_IDENTITY_IS_NULL_MSG = "身份证号码不能为空";
	public static final String ARGUMENTS_USER_IDENTITY_REALNAME_IS_NULL_MSG = "用户真实姓名不能为空";
	public static final String ARGUMENTS_USER_IDENTITY_MAINURL_IS_NULL_MSG = "用户身份证正面照不能为空";
	public static final String ARGUMENTS_USER_IDENTITY_BACKURL_IS_NULL_MSG = "用户身份证反面照不能为空";
	public static final String ARGUMENTS_ID_IS_NULL_MSG = "ID不能为空";
	public static final String ARGUMENTS_DISTANCE_IS_NULL_MSG = "两地间得间的距离不能为空号";
	public static final String ARGUMENTS_STARTCOORDX_IS_NULL_MSG = "出发地X坐标为空";
	public static final String ARGUMENTS_STARTCOORDY_IS_NULL_MSG = "出发地Y坐标为空";
	public static final String ARGUMENTS_DESTCOORDX_IS_NULL_MSG = "目的地X坐标为空";
	public static final String ARGUMENTS_DESTCOORDY_IS_NULL_MSG = "目的地Y坐标为空";
	public static final String ARGUMENTS_TASKCONTENT_IS_NULL_MSG = "货物内容不能为空";
	public static final String ARGUMENTS_PUBTIME_IS_NULL_MSG = "pubTime不能为空";
	public static final String ARGUMENTS_PUBDATE_IS_NULL_MSG = "pubDate不能为空";
	public static final String ARGUMENTS_STARTLONGITUDE_IS_NULL_MSG = "出发地经度不能为空";
	public static final String ARGUMENTS_STARTLATITUDE_IS_NULL_MSG = "出发地纬度不能为空";
	public static final String ARGUMENTS_DESTLATITUDE_IS_NULL_MSG = "目的地纬度不能为空";
	public static final String ARGUMENTS_DESTLONGITUDE_IS_NULL_MSG = "目的地经度不能为空";
	public static final String ARGUMENTS_EDIT_TIMEOUT_MSG = "编辑超时货源已下架";

    public static final String  REGISTER_DATE_NOT_COME = "此账号已注销，可在${date}后操作注册";

	public static final int INVALID_VERIFY_CODE = 3005;
	//车辆定位接口未认证车辆状态码
	public static final int CAR_NOT_CERTIFIED = 3101;

	public static final int VISIT_TOO_FREQUENT = 6001;

	public static final Integer NOT_ALLOW = 7001;

	public static final Integer NULLIFY_NOT_ALLOW_PUBLISH = 7002;
	//无效货源备注过滤返回码
	public static final Integer NULLIFY_REMARK_NOT_ALLOW_PUBLISH = 7003;
	/**
	 * 字符含有屏蔽词返回码
	 */
	public static final Integer CONTAINS_SHIELDING_WORDS = 7004;
	/**
	 * 货源投诉 该货源已经投诉
	 */
	public static final Integer COMPLAINT_ALREADY = 7005;

	/**
	 * 相似货源
	 */
	public static final Integer SIMILARITY_TRANSPORT = 7006;

	public static final Integer YMM_TRANSPORT_CHECK = 7007;

	//车辆开关开启
	public static final Integer NOT_IS_VIP = 8001;

	public static final Integer AUTH_FAIL = 8002;

	public static final Integer AUTH_ISGOING = 8003;

	public static final Integer NO_SET_PREFER = 8004;

	//无权益code
	public static final Integer NO_PERMISSION = 1010;

	//抽奖活动-无抽奖次数
	public static final Integer LUCKDRAW_NO_TIMES = 1011;

	//抽奖活动-没有中奖
	public static final Integer LUCKDRAW_NO_PRIZE = 1012;

	//未认证
	public static final Integer USER_UNVERIFIED = 1013;

	// 拉新裂变活动-用户未认证
	public static final Integer PULL_NEW_USER_UNVERIFIED = 1015;

	//接单已超时
	public static final int RECEIVE_TIMEOUT = 8500;

	//货源已被他人抢先接单
	public static final int OTHER_RECEIVE = 8501;

	//编辑超时.货源已下架
	public static final int EDIT_TIMEOUT = 8502;




	//开票版本 司机手机号错误
	public static final int BILLING_DRIVER_PHONE_ERR = 4501;
	//开票版本 司机手机号存在
	public static final int BILLING_DRIVER_PHONE_EXIST = 4502;
	//开票 司机信息不存在
	public static final int BILLING_DRIVER_EMPTY = 4503;
	//身份证正照为空
	public static final int BILLING_DRIVER_DICF_EMPTY = 4504;
	//身份证背照为空
	public static final int BILLING_DRIVER_DICB_EMPTY = 4505;
	//驾驶证正照为空
	public static final int BILLING_DRIVER_DL_EMPTY = 4506;
	//就业证照为空
	public static final int BILLING_DRIVER_DC_EMPTY = 4507;
	//没有权限
	public static final int BILLING_DRIVER_NO_PERMISSION = 4508;
	//司机正在承运好货订单
	public static final int BILLING_DRIVER_NOT_DELETE = 4509;
	//调度同步司机 时间格式错误
	public static final int BILLING_DRIVER_DATE_PARAM_ERR = 4510;
	//该车辆非调度车辆
	public static final int BILLING_CAR_ERR = 4511;

	//上传错误 图片为空
	public static final int FILE_UPLOAD_EMPTY = 5501;
	//上传格式不持支
	public static final int FILE_UPLOAD_FORMAT = 5502;
	//上传文件 typeName错误
	public static final int FILE_UPLOAD_PARAM_ERR = 5503;
	//企业认证 异常code 请先完成实名认证
	public static final int FILE_ENTERPRISE_AUTH = 5601;
	//员工管理
	public static final int STAFF_PHONE_ERROR = 5602;
	//员工管理
	public static final int SET_CAR_INFO_ERROR = 5603;
	//活动不存在
	public static final int ACTIVITY_IS_NOT_EXIT_CODE = 6501;

	//活动未开始
	public static final int ACTIVITY_INACTIVE_NOT_START = 6502;
	//活动已过期
	public static final int ACTIVITY_INACTIVE_PERIOD = 6503;
	//没有活动参与资格
	public static final int ACTIVITY_NOT_PERM = 6504;

	//没有活动参与资格-因为没有拨打电话
	public static final int ACTIVITY_NOT_PERM_NEED_CALL_PUBLISHER = 6505;

	/**
	 * 定向用户抽奖活动错误码
	 */
	/**
	 * 活动已无效
	 */
	public static final int ACTIVITY_IS_INVALID = 6600;
	/**
	 * 活动已过期
	 */
	public static final int ACTIVITY_HAS_EXPIRED = 6601;
	/**
	 * 今天机会已用完，明天再来
	 */
	public static final int OPPORTUNITY_USED_TODAY = 6602;
	/**
	 * 您的次数已用完
	 */
	public static final int COUNT_USED = 6603;
	/**
	 * 车辆认证成功才能参与抽奖活动
	 */
	public static final int VEHICLE_NOT_CERTIFIED = 6604;
	/**
	 * 谢谢参与
	 */
	public static final int THANK_YOU_FOR_PARTICIPATION = 6605;
	public static final String THANK_YOU_FOR_PARTICIPATION_MSG = "谢谢参与";

	/**
	 * 设置拨打号异常code
	 */
	public static final int DIAL_PHONE_SET_ERROR_CODE = 6700;

	/**
	 * 转一口价异常
	 */
	public static final int change_fixed_price_error = 9006;

	public static final int COMPANY_BAN_EDIT = 9007;

	/**
	 * 暂不允许货源加价
	 */
	public static final int NO_ADD_MONEY = 9008;

	/**
	 * 非会员用户交易提醒
	 */
	public static final int NO_MEMBER_REMIND = 9009;



	/**
	 * 无异常上报信息
	 */
	public static final int EX_NONE = 10001;

	/**
	 * 异常上报未处理完成
	 */
	public static final int EX_NONE_FINISH = 10002;

	public static final int USER_CAR_GRADE_ERR = 6506;

	//企业认证 未认证状态码
	public static final int INVOICE_ENTERPRISE_UNVERIFIED = 200001;

	//企业认证 经营范围
	public static final int BUSINESS_SCOPE_ERROR = 200002;

	//等级不足
	public static final Integer NO_ENOUGH_LEVEL = 1014;

	//优车签约异常code码：用户已签约
	public static final Integer SUPERIOR_CAR_SIGN_SIGNED_ERROR = 30001;
	//优车签约异常code码：用户未实名认证/实名未通过
	public static final Integer SUPERIOR_CAR_SIGN_NO_IDENTITY_ERROR = 30002;
	//优车签约异常code码：用户未车辆认证/车辆认证未通过
	public static final Integer SUPERIOR_CAR_SIGN_NO_CAR_ERROR = 30003;
	//优车签约异常code码：用户非付费会员
	public static final Integer SUPERIOR_CAR_SIGN_NO_VIP_ERROR = 30004;


	//优车签约异常code码：用户未签约优车
	public static final Integer SUPERIOR_CAR_SIGN_NO_SIGN_ERROR = 30005;
	//优车签约异常code码：用户被拉入黑名单
	public static final Integer SUPERIOR_CAR_SIGN_BLACK_ERROR = 30006;
	//优车签约异常code码：限制接单
	public static final Integer SUPERIOR_CAR_SIGN_FORBID_ORDER_ERROR = 30007;
	//优车签约异常code码：已签约未审核
	public static final Integer SUPERIOR_CAR_SIGNED_NO_CHECK_ERROR = 30008;

	//保证金余额不足
	public static final Integer SUPERIOR_CAR_SIGN_NOT_ENOUGH_ERROR = 30008;
	//有申请中的退款申请
	public static final Integer SUPERIOR_CAR_SIGN_IN_REVIEW_ERROR = 30009;

	//无优车授权，跳转报名或者授权页面
	public static final Integer SUPERIOR_CAR_SIGN_SIGNED_ERROR_JUMP_TAB_FIRST_T = 30010;
	public static final Integer SUPERIOR_CAR_SIGN_SIGNED_ERROR_JUMP_TAB_NO_FIRST_T = 30011;

	public static final Integer YOUCHE_PUBLISH_TRANSPORT_NOT_ENOUGH_ERROR = 30012;


	/**
	 * 获取虚拟号流程执行失败
	 */
	public static final Integer GET_PRIVACY_PHONE_NUM_ERROR = 20001;




	/**
	 * 当天提交企业认证次数大于5时返回code
	 */
	public static final Integer  CHECK_TODAY_ENTERPRISE_NUM=40000;

	/**
	 * 企业名称不正确
	 */
	public static final Integer  TIAN_YAN_CHA_REQUEST_ERROR=40001;
	/**
	 * 企业名称不正确
	 */
	public static final Integer  ENTERPRISE_NAME_ERROR=40002;
	/**
	 * 法人姓名不正确
	 */
	public static final Integer  LEGAL_PERSON_NAME_ERROR=40003;
	/**
	 * 统一信用代码不正确
	 */
	public static final Integer  CREDIT_CODE_ERROR=40004;
	/**
	 * 营业执照状态异常
	 */
	public static final Integer  REG_STATUS_ERROR=40005;
	/**
	 * 营业执照有效期异常
	 */
	public static final Integer  REG_TO_TIME_ERROR=40006;


	/**
	 * app/pc阻断code，走阻断
	 */
	public static final Integer  CAR_PERMISSION_ERROR=50001;

	/**
	 * 非开票货源阻断code
	 */
	public static final Integer BLOCK_ERROR = 50007;

	/**
	 * 开票-调用三方服务商创建运单失败
	 */
	public static final Integer INVOICE_CREATE_WAYBILL_THREE_ERROR = 60001;

	//无权益code
	public static final Integer NO_PERMISSION_PERSONAL = 1020;
}
