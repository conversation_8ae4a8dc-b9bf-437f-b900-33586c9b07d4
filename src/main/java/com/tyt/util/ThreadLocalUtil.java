package com.tyt.util;

import com.tyt.infofee.bean.plat.PriceReq;

/**
 * ThreadLocal 工具类
 *
 * <AUTHOR>
 * @since 2024/06/24 17:13
 */
public final class ThreadLocalUtil {

    private ThreadLocalUtil() {

    }


    private static final ThreadLocal<PriceReq> THREAD_LOCAL_INSTANCE = new ThreadLocal<>();


    public static void set(PriceReq t) {
        THREAD_LOCAL_INSTANCE.set(t);
    }

    public static PriceReq get() {
        return THREAD_LOCAL_INSTANCE.get();
    }

    public static void remove() {
        THREAD_LOCAL_INSTANCE.remove();
    }
}
