package com.tyt.util;

import java.io.File;
import java.util.Iterator;

import javax.annotation.Resource;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.xml.XmlBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.core.io.ClassPathResource;

import com.tyt.model.Geo;
import com.tyt.transport.service.GeoService;
/**
 * 地理位置信息导入 xml -> db
 * 
 * <AUTHOR>
 *
 */
public class ImportGeoInfo {
	 
    @Resource(name = "geoService")
	GeoService geoService;

	/**
	 * @param args
	 * @throws DocumentException 
	 */
	public static void main(String[] args) throws DocumentException {
		
	      
          
        ApplicationContext factory=new ClassPathXmlApplicationContext(new String[] {"config/spring/spring-common.xml","config/spring/spring-mvc.xml"}); 

		
		ImportGeoInfo task = new ImportGeoInfo();
		task.geoService = (GeoService)factory.getBean("geoService");
		String fileName="D:\\work\\TYT\\aliyun\\20140913.xml";
		SAXReader reader = new SAXReader();
		Document document = reader.read(new File(fileName));
        Element root = document.getRootElement();
        System.out.println(root.toString());
        //遍历省节点
        for( Iterator proIterator = root.elementIterator(); proIterator.hasNext(); ) {
           Element proElement = (Element) proIterator.next();
           System.out.println(proElement.toString());
           String name = proElement.attributeValue("pro");
           String rf =  proElement.attributeValue("RF");
           String px =  proElement.attributeValue("px");
           String py =  proElement.attributeValue("py");
           
           Geo pro = new Geo(name,rf,Geo.TYPE_PRO);
           if(!px.equals("非数字")) {
        	   pro.setPx(Double.parseDouble(px));
           }
           if(!py.equals("非数字")) {
        	   pro.setPy(Double.parseDouble(py));
           }
           
           task.geoService.add(pro);
           System.out.println(pro.toString());
           //遍历市节点
           for( Iterator cityIterator = proElement.elementIterator(); cityIterator.hasNext(); ) {
        
        	   Element cityElement = (Element) cityIterator.next();
               System.out.println(cityElement.toString());
               name = cityElement.attributeValue("city");
               rf =  cityElement.attributeValue("RF");
               px =  cityElement.attributeValue("px");
               py =  cityElement.attributeValue("py");
               
               
               Geo city = new Geo(name,rf,Geo.TYPE_CITY);
               city.setPid(pro.getId());
               if(!px.equals("非数字")) {
            	   city.setPx(Double.parseDouble(px));
               }
               if(!py.equals("非数字")) {
            	   city.setPy(Double.parseDouble(py));
               }
               
               task.geoService.add(city);
               System.out.println(city.toString());
               //遍历县节点
               for( Iterator countryIterator = cityElement.elementIterator(); countryIterator.hasNext(); ) {
                   
            	   Element countryElement = (Element) countryIterator.next();
                   System.out.println(countryElement.toString());
                   name = countryElement.attributeValue("county");
                   rf =  countryElement.attributeValue("RF");
                   px =  countryElement.attributeValue("px");
                   py =  countryElement.attributeValue("py");
                   
                   
                   Geo country = new Geo(name,rf,Geo.TYPE_COUNTRY);
                   country.setPid(city.getId());
                   if(!px.equals("非数字")) {
                	   country.setPx(Double.parseDouble(px));
                   }
                   if(!py.equals("非数字")) {
                	   country.setPy(Double.parseDouble(py));
                   }
                   
                   task.geoService.add(country);
                   System.out.println(country.toString());
                   
                   //遍历镇节点
                   for( Iterator townIterator = countryElement.elementIterator(); townIterator.hasNext(); ) {
                       
                	   Element townElement = (Element) townIterator.next();
                       System.out.println(townElement.toString());
                       name = townElement.attributeValue("Town");
                       rf =  townElement.attributeValue("RF");
                       px =  townElement.attributeValue("px");
                       py =  townElement.attributeValue("py");
                       
                       
                       Geo town = new Geo(name,rf,Geo.TYPE_TOWN);
                       town.setPid(country.getId());
                       if(null!=px && !px.equals("非数字")) {
                    	   town.setPx(Double.parseDouble(px));
                       }
                       if(null!=py && !py.equals("非数字")) {
                    	   town.setPy(Double.parseDouble(py));
                       }
                       
                       task.geoService.add(town);
                       System.out.println(town.toString());
                       
                       //Town
                    
                   }
                
               }
            
           }
           
        }
        
        System.exit(0);
	}

}
