package com.tyt.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.util.Date;

/**
 * @className TokenUtil
 * @description tokenUtil工具类
 * <AUTHOR>
 * @date 2022-12-05 16:07
 * @Version 1.0
 */
public class TokenUtil {

    /**
     * 签名秘钥
     */
    public static final String secret = "plat@2022";

    /**
     * 发布者
     */
    public static final String issuer = "tyt";

    /**
     * 过期时间（存放在redis中需要跟redis的过期时间一致）
     * 24小时失效
     */
    public static long ttlMillis = 24*60*60*1000L;
    /**
     * 生成token
     *
     * @param id
     * @return
     */
    public static String createJwtToken(Long id) {
        return createJwtToken(id, issuer, ttlMillis);
    }

    /**
     * 生成Token
     *
     * @param id 编号
     * @param issuer 该JWT的签发者，是否使用是可选的
     * @param ttlMillis 签发时间 （有效时间，过期会报错）
     * @return token String
     */
    public static String createJwtToken(Long id, String issuer, long ttlMillis) {

    // 签名算法 ，将对token进行签名
    SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

    // 生成签发时间
    long nowMillis = System.currentTimeMillis();
    Date now = new Date(nowMillis);

    // 通过秘钥签名JWT
    byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(secret);
    Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());

    // Let's set the JWT Claims
    JwtBuilder builder = Jwts.builder().setId(id.toString())
            .setIssuedAt(now)
            .setIssuer(issuer)
            .signWith(signatureAlgorithm, signingKey);

    // if it has been specified, let's add the expiration
    if (ttlMillis >= 0) {
        long expMillis = nowMillis + ttlMillis;
        Date exp = new Date(expMillis);
        builder.setExpiration(exp);
    }

    // Builds the JWT and serializes it to a compact, URL-safe string
        return builder.compact();
    }

    /**
     * @description 解析token
     * <AUTHOR>
     * @date 2020/1/8 16:30
     * @param jwt token字符串
     * @return io.jsonwebtoken.Claims
     **/
    // Sample method to validate and read the JWT
    public static Claims parseJWT(String jwt) throws Exception{
        // This line will throw an exception if it is not a signed JWS (as expected)
        Claims claims = Jwts.parser()
                    .setSigningKey(DatatypeConverter.parseBase64Binary(secret))
                    .parseClaimsJws(jwt).getBody();

        return claims;
    }

    public static void main(String[] args) {
        //1.生成token
        String token = TokenUtil.createJwtToken(20200107L);
        System.out.println(token);

        //String token = "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIyMDIwMDEwNyIsImlhdCI6MTU3ODQ3MTYwMiwiaXNzIjoidHl0IiwiZXhwIjoxNTc4NDcxNjYyfQ.BgkDcznB69YzBYuEDHDjMhOKTbo5mohOSN2A85QNI84";
        //2.解析token
        Claims claims = null;
        try {
            claims = TokenUtil.parseJWT(token);
            String id = claims.getId();
            System.out.println("生成token的Id为："+id);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

