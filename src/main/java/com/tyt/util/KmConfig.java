package com.tyt.util;
public class KmConfig {

	//主机ip
	private String FtpHost = Config.getString("server");
	//端口号
	private int FtpPort=Integer.parseInt(Config.getString("port"));
	//ftp用户名
	private String FtpUser = Config.getString("username");
	//ftp密码
	private String FtpPassword = Config.getString("password");
	//ftp中的目录
	private String FtpPath = "";
	public String getFtpHost() {
		return FtpHost;
	}
	public void setFtpHost(String ftpHost) {
		FtpHost = ftpHost;
	}
	public int getFtpPort() {
		return FtpPort;
	}
	public void setFtpPort(int ftpPort) {
		FtpPort = ftpPort;
	}
	public String getFtpUser() {
		return FtpUser;
	}
	public void setFtpUser(String ftpUser) {
		FtpUser = ftpUser;
	}
	public String getFtpPassword() {
		return FtpPassword;
	}
	public void setFtpPassword(String ftpPassword) {
		FtpPassword = ftpPassword;
	}
	public String getFtpPath() {
		return FtpPath;
	}
	public void setFtpPath(String ftpPath) {
		FtpPath = ftpPath;
	}
}
