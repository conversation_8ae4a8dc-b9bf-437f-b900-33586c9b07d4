package com.tyt.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 挂车型号与最大载重的粗略关系
 */
public class CarTypeMaxPayload {

    /**
     * 后二桥
     */
    private static final String houer = "1";
    /**
     * 后三桥
     */
    private static final String housan = "2";
    /**
     * 后四桥
     */
    private static final String housi = "3";
    /**
     * 后五桥
     */
    private static final String houwu = "4";
    /**
     * 后六桥
     */
    private static final String houliu = "5";
    /**
     * 后七桥
     */
    private static final String houqi = "6";
    /**
     * 两线四轴
     */
    private static final String ersi = "7";
    /**
     * 三线六轴
     */
    private static final String sanliu = "8";
    /**
     * 四线八轴
     */
    private static final String siba = "9";
    /**
     * 五线十轴
     */
    private static final String wushi = "10";
    /**
     * 六线十二轴
     */
    private static final String liushier = "11";
    /**
     * 七线十四轴
     */
    private static final String qishisi = "13";
    /**
     * 其他
     */
    private static final String qita = "0";

    /**
     * carType 获取maxPayLoad值
     *
     * @param carType 车辆型号
     * @return maxPayLoad
     */
    public static String getMaxPayload(String carType, String maxPayload) {
        if(StringUtils.isNotBlank(maxPayload)) {
            return maxPayload;
        }
        if(StringUtils.isBlank(carType)) {
            return null;
        }
        String maxPayLoad = "1000";
        switch (carType) {
            case houer: {
                maxPayLoad = "30";
                break;
            }
            case housan: {
                maxPayLoad = "60";
                break;
            }
            case housi: {
                maxPayLoad = "60";
                break;
            }
            case houwu: {
                maxPayLoad = "50";
                break;
            }
            case houliu: {
                maxPayLoad = "70";
                break;
            }
            case houqi: {
                maxPayLoad = "80";
                break;
            }
            case ersi: {
                maxPayLoad = "50";
                break;
            }
            case sanliu: {
                maxPayLoad = "90";
                break;
            }
            case siba: {
                maxPayLoad = "80";
                break;
            }
            case wushi: {
                maxPayLoad = "120";
                break;
            }
            case liushier: {
                maxPayLoad = "110";
                break;
            }
            case qishisi: {
                maxPayLoad = "130";
                break;
            }
            case qita: {
                maxPayLoad = "1000";
                break;
            }
        }
        return maxPayLoad;
    }

}
