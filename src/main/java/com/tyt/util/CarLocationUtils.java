package com.tyt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tyt.acvitity.bean.CarLocation;
import com.tyt.acvitity.bean.CarLocationParam;
import com.tyt.config.util.AppConfig;
import com.tyt.plat.client.user.ApiTraceLocationClient;
import com.tyt.plat.commons.internal.InternalClientUtil;
import com.tyt.plat.commons.internal.InternalWebResult;
import com.tyt.plat.entity.base.CurrentLocationVO;
import com.tyt.service.common.common.HttpClientFactory;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 获取车辆位置接口
 */
public class CarLocationUtils {

    public static Logger logger = LoggerFactory.getLogger(CarLocationUtils.class);

    private CarLocationUtils() {
    }

    public static final String SYNC_CAR_BI_HTTP_BASE_KEY = "sync.car.bi.http.base";

    public static final String CACHE_CAR_CURRENT_LOCATION = "car:current:location:";

    public static final String SYNC_CAR_BI_PRIVATEKEY_KEY = "sync.car.bi.privatekey";

    public static final int CACHE_EXPIRE_TIME_5MIN = 300;

    private static CloseableHttpClient httpClient = HttpClientFactory.getHttpClientWithRetry();

    public static String key = AppConfig.getProperty("tyt.private.key");

//    /**
//     * 获取车辆定位信息
//     *
//     * @param carHeadNo
//     * @return
//     * @throws Exception
//     */
//    public static Object getCurrentLocation(String carHeadNo) throws Exception {
//        JSONObject locationData = RedisUtil.getObject(CACHE_CAR_CURRENT_LOCATION + carHeadNo);
//        if (locationData != null) {
//            return locationData;
//        }
//        TytConfigService tytConfigService = (TytConfigService) ApplicationContextUtils.getBean("tytConfigService");
//        // 缓存不存在去BI服务器请求数据
//        String biBase = tytConfigService.getStringValue(SYNC_CAR_BI_HTTP_BASE_KEY, "http://*************");
//        String biPath = "/idc/index.php/apibi/ZhiyunRT/getCarRealTimeLocation";
//        String biPrivateKey = tytConfigService.getStringValue(SYNC_CAR_BI_PRIVATEKEY_KEY, "9d13dcadb5dbff5d0a93b6389ac5c89a");
//        String biApiTime = TimeUtil.formatDateTime(new Date());
//        StringBuffer sb = new StringBuffer();
//        String signOriginal = sb.append(biPrivateKey).append(biPath).append("?api_time=").append(biApiTime)
//                .append("&carHeadNo=").append(carHeadNo).toString();
//        logger.info("signOriginal--" + signOriginal);
//        String sign = MD5Util.GetMD5Code(signOriginal);
//        logger.info("sign--" + sign);
//
//        sb.setLength(0);
//        sb.append(biBase).append(biPath).append("?api_time=").append(URLEncoder.encode(biApiTime, "utf-8"));
//        sb.append("&carHeadNo=").append(carHeadNo);
//        sb.append("&api_sign=").append(sign);
//        String url = sb.toString();
//        logger.info("bi service request getCurrentLocation url is: " + url);
//
//        HttpGet httpGet = new HttpGet(url);
//
//        long startTime = System.currentTimeMillis();
//        CloseableHttpResponse response = httpClient.execute(httpGet);
//        logger.info("调用 bi : {}, 请求耗时: {}", biPath, System.currentTimeMillis() - startTime);
//
//        try {
//            if (response.getStatusLine().getStatusCode() != 200) {
//                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
//                return null;
//            }
//            String strContent = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
//            JSONObject jsonObject = JSON.parseObject(strContent);
//            if (jsonObject.getInteger("code") == 200) {
//                JSONArray arrayData = jsonObject.getJSONArray("data");
//                if (CollectionUtils.isNotEmpty(arrayData)) {
//                    locationData = arrayData.getJSONObject(0);
//                    RedisUtil.setObject(CACHE_CAR_CURRENT_LOCATION + carHeadNo, locationData, CACHE_EXPIRE_TIME_5MIN);
//                }
//            }
//        } catch (Exception e) {
//            logger.error("BI服务器-请求车辆位置接口异常：", e);
//            throw new Exception();
//        } finally {
//            HttpClientUtils.closeQuietly(response);
//        }
//        return locationData;
//    }


    /**
     * @description 获取车辆位置信息
     * <AUTHOR>
     * @date 2022/8/29 14:45
     * @param headCity
     * @param headNo
     * @param beginTime
     * @param endTime
     * @return java.lang.Object
     */
    public static List<CarLocation> getCarLocation(String headCity, String headNo, Date beginTime, Date endTime) throws Exception {
        try {
            //车头车牌号
            String carHeadNo = headCity + headNo;
            ApiTraceLocationClient apiTraceLocationClient = ApplicationContextUtils.getBean(ApiTraceLocationClient.class);
            CarLocationParam carLocationParam = new CarLocationParam();
            carLocationParam.setCarHeadNo(carHeadNo);
            carLocationParam.setBeginTime(beginTime);
            carLocationParam.setEndTime(endTime);

            Response<InternalWebResult<List<CarLocation>>> execute = apiTraceLocationClient.getCarLocus(carLocationParam).execute();

            return new ArrayList<>(InternalClientUtil.getDataDetail(execute));
        }catch (Exception e){
            logger.error("getCarLocation error:",e);
            return new ArrayList<>();
        }


//        TytConfigService tytConfigService = (TytConfigService) ApplicationContextUtils.getBean("tytConfigService");
//        // 缓存不存在去BI服务器请求数据
//        String biBase = tytConfigService.getStringValue(SYNC_CAR_BI_HTTP_BASE_KEY, "http://*************");
//        String biPath = "/idc/index.php/apibi/ZhiyunLocus/getCarLocus";
//        String biPrivateKey = tytConfigService.getStringValue(SYNC_CAR_BI_PRIVATEKEY_KEY, "9d13dcadb5dbff5d0a93b6389ac5c89a");
//        String biApiTime = TimeUtil.formatDateTime(new Date());
//        String biBeginTime = TimeUtil.formatDateTime(beginTime);
//        String biEndTime = TimeUtil.formatDateTime(endTime);
//
//        StringBuffer sb = new StringBuffer();
//        String signOriginal = sb.append(biPrivateKey).append(biPath).append("?api_time=").append(biApiTime)
//                .append("&car_head_no=").append(carHeadNo)
//                .append("&begin_time=").append(biBeginTime)
//                .append("&end_time=").append(biEndTime)
//                .toString();
//        logger.info("signOriginal--" + signOriginal);
//        String sign = MD5Util.GetMD5Code(signOriginal);
//        logger.info("sign--" + sign);
//
//        sb.setLength(0);
//        sb.append(biBase).append(biPath).append("?api_time=").append(URLEncoder.encode(biApiTime, "utf-8"));
//        sb.append("&car_head_no=").append(carHeadNo);
//        sb.append("&begin_time=").append(URLEncoder.encode(biBeginTime, "utf-8"));
//        sb.append("&end_time=").append(URLEncoder.encode(biEndTime, "utf-8"));
//        sb.append("&api_sign=").append(sign);
//        String url = sb.toString();
//        logger.info("bi service request getCurrentLocation url is: " + url);
//
//        //车辆位置集合
//        List<CarLocation> carLocations =  new ArrayList <>();
//
//        HttpGet httpGet = new HttpGet(url);
//
//        long startTime = System.currentTimeMillis();
//        CloseableHttpResponse response = httpClient.execute(httpGet);
//        logger.info("调用 bi : {}, 请求耗时: {}", biPath, System.currentTimeMillis() - startTime);
//
//        try {
//            if (response.getStatusLine().getStatusCode() != 200) {
//                logger.info("Status Code is " + response.getStatusLine().getStatusCode());
//                return null;
//            }
//            String strContent = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
//            JSONObject jsonObject = JSON.parseObject(strContent);
//            if (jsonObject.getInteger("code") == 200) {
//                JSONArray arrayData = jsonObject.getJSONArray("data");
//                if (CollectionUtils.isNotEmpty(arrayData)) {
//                    carLocations = JSONObject.parseArray(arrayData.toJSONString(), CarLocation.class);
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.info("获取车辆位置信息发生错误,headCity:{},headNo{},biBeginTime{},biEndTime{}",
//                    headCity, headNo, biBeginTime, biEndTime);
//            logger.error("BI服务器-请求车辆位置接口异常：", e);
//        } finally {
//            HttpClientUtils.closeQuietly(response);
//        }
//        return carLocations;
    }

}
