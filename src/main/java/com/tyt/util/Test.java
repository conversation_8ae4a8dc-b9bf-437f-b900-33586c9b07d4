package com.tyt.util;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.TimeZone;
import java.util.UUID;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.HashMap;

public class Test {

	/**
	 * @param args
	 */
	public static void main(String[] args) {
     
		/*long t1 = System.currentTimeMillis();
		
		try {
			Thread.sleep(5000);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		long t2 = System.currentTimeMillis();
		System.out.println("t1:"+t2);
		System.out.println("t1:"+t2);
		System.out.println(t2-t1);
		System.out.println((t2-t1)/1000);
		
		
		List scoreList1 = new ArrayList();
		List scoreList2 = new ArrayList();
		
		for(int i=10;i>0;i--) {
		  scoreList1.add(i);
		  //scoreList2.add(scoreList2.size(), i);
		}*/
		
		/*for(int i=0;i<10;i++) {
			  TimeZone tz = TimeZone.getDefault();
			  System.out.println(tz);
		}*/
		/*String a = "abc\\";
		Map map = new HashMap();
		map.put("a", a);
		
		System.out.println(JSON.toJSONString(map));*/

		String b = "吕梁交口---山东临沂罗庄 长期需求13米半挂、\\高栏车 拉（面粉）";
		String c = "吕梁交口---山东临沂罗庄 长期需求13米半挂、\\\\高栏车 拉（面粉）";
		System.out.println(b.length()+ " "+ c.length());
		System.out.println(b.equalsIgnoreCase(c));

		String cellphone="13101078705";
		System.out.println(cellphone);

		System.out.println(setPhoneDisturb(cellphone));
		System.out.println("13689890007");
		System.out.println(setPhoneDisturb("13689890007"));
		System.out.println("16689890007");
		System.out.println(setPhoneDisturb("16689890007"));

		
				
				
	}

	private static String setPhoneDisturb(String phone){
		if(StringUtils.isEmpty(phone)){
			return phone;
		}
		char s [] =phone.toCharArray();
		if (s.length<11){
			return phone;
		}

		StringBuffer sb=new StringBuffer();

		   /*s[6]=(char) (Math.abs(phone.charAt(1)- (Math.abs(phone.charAt(8)-phone.charAt(6))+48)) +48);
		  s[7]=(char) (Math.abs(phone.charAt(1)- (Math.abs(phone.charAt(9)-phone.charAt(7))+48)) +48);
		  s[8]=(char) (Math.abs(phone.charAt(1)- (Math.abs(phone.charAt(10)-phone.charAt(8))+48)) +48);


		  13101078705
13101035105
13689890007
13689863407
16689890007
16689836107

		   */

		s[6]=(char) (Math.abs(phone.charAt(1)- (Math.abs(phone.charAt(8)-phone.charAt(6))+48)) +48);
		s[7]=(char) (Math.abs(phone.charAt(1)- (Math.abs(phone.charAt(9)-phone.charAt(7))+48)) +48);
		s[8]=(char) (Math.abs(phone.charAt(1)- (Math.abs(phone.charAt(10)-phone.charAt(8))+48)) +48);

		return  new String(s);

	}

}
