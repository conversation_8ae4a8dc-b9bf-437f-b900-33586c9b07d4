package com.tyt.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import org.springframework.util.StringUtils;
import com.tyt.model.TransportQueryCondition;

public class FileUtil {
	
	/**
	 * 获得文件夹下的文件全路径集合
	 * @param path
	 * @return
	 */
	public static List<String> readFolder(String path) throws Exception{  
		List<String> filePathList=new ArrayList<String>() ;
        File file = new File(path);  
        if (file.exists()) {  
            File[] files = file.listFiles();  
            if (files.length == 0) {  
                throw new Exception("文件夹没文件");  
            } else {  
                for (File file2 : files) {  
                    if (file2.isDirectory()) {  
                        readFolder(file2.getAbsolutePath());  
                    } else { 
                    	filePathList.add(file2.getAbsolutePath());
                    }  
                }
                return filePathList;
            }  
        } else {  
            throw new Exception("文件夹不存在");
        }
    } 
	
	/**
     * 以行为单位读取文件
     */
    public static List<TransportQueryCondition> readFileByLines(String filePath) throws Exception{
    	List<TransportQueryCondition> conditions=new ArrayList<TransportQueryCondition>();
    	File file = new File(filePath);
    	String pathSepareter=System.getProperties().getProperty("file.separator");
        int firstIndex=filePath.lastIndexOf(pathSepareter)+1;
        int secondIndex=filePath.lastIndexOf(".");
        String fileName=filePath.substring(firstIndex, secondIndex);
        InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(file),"utf-8"); 
        BufferedReader reader = new BufferedReader(inputStreamReader);
        String content = null;
        while ((content = reader.readLine()) != null) {
        	conditions.add(createCondition(fileName,content));
            }
        reader.close();
        inputStreamReader.close();
        return conditions;
    }

	private static TransportQueryCondition createCondition(String fileName,String content) throws Exception {
		TransportQueryCondition condition=new TransportQueryCondition();
		String[] fileNameArr=fileName.split("_");
		condition.setPlatId(Integer.parseInt(fileNameArr[0]));
		condition.setCellPhone(fileNameArr[1]);
		condition.setCtime(TimeUtil.parseyyyymmdd(fileNameArr[2]));
		String[] contentArr=content.split(",");
		condition.setStartProvince(contentArr[0]);
		condition.setStartCity(contentArr[1]);
		condition.setStartCounty(contentArr[2]);
		condition.setStartX(floatStrToInt(contentArr[3]));
		condition.setStartY(floatStrToInt(contentArr[4]));
		condition.setStartScope(strToInt(contentArr[5]));
		if(contentArr.length>6){
			condition.setDestProvince(contentArr[6]);
			condition.setDestCity(contentArr[7]);
			condition.setDestCounty(contentArr[8]);
			condition.setDestX(floatStrToInt(contentArr[9]));
			condition.setDestY(floatStrToInt(contentArr[10]));
			condition.setDestScope(strToInt(contentArr[11]));
		}
		
		return condition;
	}
	/**
	 * 实型字符串*100转整型
	 * @param str
	 * @return
	 */
	private static Integer floatStrToInt(String str){
		Float f=Float.parseFloat(str.trim());
		if(f>0)return  (int)((f * 100) + 0.5);
		else return  (int)((f * 100) - 0.5);
	}
	/**
	 * 字符串转整型
	 * @param str
	 * @return
	 */
	private static Integer strToInt(String str){
		String value=str.trim();
		return (StringUtils.hasLength(value))?Integer.parseInt(value):null;
	}
	
	
	/**
	 * 删除文件夹下的所有文件
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public static void delAllFile(String path) throws Exception{
	       File file = new File(path);
	       if (!file.exists()) {
	         throw new Exception("文件夹不存在");
	       }
	       if (!file.isDirectory()) {
	    	   throw new Exception("文件夹不是目录");
	       }
	       String[] tempList = file.list();
	       File temp = null;
	       for (int i = 0; i < tempList.length; i++) {
	          if (path.endsWith(File.separator)) {
	             temp = new File(path + tempList[i]);
	          } else {
	              temp = new File(path + File.separator + tempList[i]);
	          }
	          if (temp.isFile()) {
	             temp.delete();
	          }
	          if (temp.isDirectory()) {
	             delAllFile(path + "/" + tempList[i]);//先删除文件夹里面的文件
	          }
	       }
	     }

//	public static void main(String[] args) {
//		String filePath="22222/eeee/222_2_e.txt";
//		int firstIndex=filePath.lastIndexOf("/")+1;
//        int secondIndex=filePath.lastIndexOf(".");
//        String fileName=filePath.substring(firstIndex, secondIndex);
//        System.out.println(fileName);
//	}
}
