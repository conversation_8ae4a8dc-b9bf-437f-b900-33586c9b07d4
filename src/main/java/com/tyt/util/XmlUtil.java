package com.tyt.util;

import java.util.*;

public class XmlUtil {

    /**
     * map转xml
     *
     * @param map      map对象
     * @return 返回xml文本
     */
    public static String mapToXML(Map<String, Object> map) {
        return mapToXML(map, "");
    }

    /**
     * map转xml
     *
     * @param map      map对象
     * @param rootName root节点名称
     * @return 返回xml文本
     */
    public static String mapToXML(Map<String, Object> map, String rootName) {
        StringBuilder sb = new StringBuilder();
        sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        if (rootName != null && !rootName.equals("")){
            sb.append("<").append(rootName).append(">");
        }
        innerMapToXML(map, sb);
        if (rootName != null && !rootName.equals("")){
            sb.append("</").append(rootName).append(">");
        }
        return sb.toString();
    }

    private static void innerMapToXML(Map<String, Object> map, StringBuilder sb) {
        Set set = map.keySet();
        for (Iterator it = set.iterator(); it.hasNext(); ) {
            String key = (String) it.next();
            Object value = map.get(key);
            if (null == value)
                value = "";
            if (value.getClass().getName().equals("java.util.ArrayList")) {
                ArrayList list = (ArrayList) map.get(key);
                sb.append("<").append(key).append(">");
                for (int i = 0; i < list.size(); i++) {
                    HashMap<String, Object> hm = (HashMap) list.get(i);
                    innerMapToXML(hm, sb);
                }
                sb.append("</").append(key).append(">");
            } else {
                if (value instanceof HashMap) {
                    sb.append("<").append(key).append(">");
                    innerMapToXML((HashMap) value, sb);
                    sb.append("</").append(key).append(">");
                } else {
                    sb.append("<").append(key).append(">").append(value).append("</").append(key).append(">");
                }
            }
        }
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();
        map.put("a", "hahaha");
        map.put("b", "wawawawa");
        String s= mapToXML(map,"data");
        System.out.println(s);
    }
}
