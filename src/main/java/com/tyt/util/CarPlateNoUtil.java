package com.tyt.util;

import java.util.regex.Pattern;

/**
 * @ClassName CarPlateNoUtil
 * @Description
 * 常规车牌号：省份(单个汉字) + 地区代码(大写字母) + 五位数字/大写英文字母(序号位)  如：京B12345。
 */

public class CarPlateNoUtil {
    
    /**
     * @Description  校验车牌号是否符合规则(不包含挂车)
     * <AUTHOR>
     * @Date  2019/3/14 15:55
     * @Param [content]
     * @return boolean
     **/
    public static boolean checkPlateNumberFormat(String content) {
        String pattern = "[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$";
        return Pattern.matches(pattern, content);
    }

}
