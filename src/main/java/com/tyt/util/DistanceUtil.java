package com.tyt.util;

import java.text.DecimalFormat;

/**
 * 根据经纬度进行相关计算的工具类
 * 
 * <AUTHOR>
 * @date 2016-5-31下午5:02:56
 * @description
 */
public class DistanceUtil {
	private static final long ONE_LATITUDE_DISTANCE = 111;
	private static final double PI = 3.14159265358979323846264338327950288419716939937510582097494459230781640628620899;// 定义圆周率

	/**
	 * 计算地球上任意两点(经纬度)距离
	 * 
	 * @param long1
	 *            第一点经度
	 * @param lat1
	 *            第一点纬度
	 * @param long2
	 *            第二点经度
	 * @param lat2
	 *            第二点纬度
	 * @return 返回距离 单位：米
	 */
	public static double distance(double long1, double lat1, double long2, double lat2) {
		double a, b, R;
		R = 6378137; // 地球半径
		lat1 = lat1 * Math.PI / 180.0;
		lat2 = lat2 * Math.PI / 180.0;
		a = lat1 - lat2;
		b = (long1 - long2) * Math.PI / 180.0;
		double d;
		double sa2, sb2;
		sa2 = Math.sin(a / 2.0);
		sb2 = Math.sin(b / 2.0);
		d = 2 * R * Math.asin(Math.sqrt(sa2 * sa2 + Math.cos(lat1) * Math.cos(lat2) * sb2 * sb2));
		return d;
	}

	/**
	 * 获取以某经纬度为中心的指定公里内的最小矩形对应的左上和右下经纬度
	 * 
	 * @param longitude
	 *            中心坐标的经度
	 * @param latitude
	 *            中心坐标的纬度
	 * @param kilometers
	 *            公里数
	 * @return 长度为4的数组，第一项左上角的纬度，第二项左上角的经度，第三项右下角的纬度，第四项右下角的纬度
	 */
	public static double[] getRectanglePointsByPos(double longitude, double latitude, long kilometers) {
		double[] result = new double[4];
		DecimalFormat df = new DecimalFormat("0.000000");
		/*
		 * 计算矩形左上角和右下角的纬度
		 */
		String increment = df.format((float) kilometers / ONE_LATITUDE_DISTANCE);
		double leftLatitude = latitude + Double.valueOf(increment);
		double rightLatitude = latitude - Double.valueOf(increment);
		/*
		 * 计算矩形左上角和右下角的经度
		 */
		increment = df.format((float) kilometers / (ONE_LATITUDE_DISTANCE * Math.cos(latitude * PI / 180)));
		double leftLongitude = longitude - Double.valueOf(increment);
		double rightLongitude = longitude + Double.valueOf(increment);
		result[0] = leftLatitude;
		result[1] = leftLongitude;
		result[2] = rightLatitude;
		result[3] = rightLongitude;
		return result;
	}
}
