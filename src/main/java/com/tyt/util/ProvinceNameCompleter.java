package com.tyt.util;

import java.util.HashMap;
import java.util.Map;

public class ProvinceNameCompleter {

    // 定义省级名称和对应完整名称的映射
    private static final Map<String, String> provinceMap = new HashMap<>();

    static {
        provinceMap.put("北京", "北京市");
        provinceMap.put("天津", "天津市");
        provinceMap.put("上海", "上海市");
        provinceMap.put("重庆", "重庆市");
        provinceMap.put("河北", "河北省");
        provinceMap.put("山西", "山西省");
        provinceMap.put("辽宁", "辽宁省");
        provinceMap.put("吉林", "吉林省");
        provinceMap.put("黑龙江", "黑龙江省");
        provinceMap.put("江苏", "江苏省");
        provinceMap.put("浙江", "浙江省");
        provinceMap.put("安徽", "安徽省");
        provinceMap.put("福建", "福建省");
        provinceMap.put("江西", "江西省");
        provinceMap.put("山东", "山东省");
        provinceMap.put("河南", "河南省");
        provinceMap.put("湖北", "湖北省");
        provinceMap.put("湖南", "湖南省");
        provinceMap.put("广东", "广东省");
        provinceMap.put("海南", "海南省");
        provinceMap.put("四川", "四川省");
        provinceMap.put("贵州", "贵州省");
        provinceMap.put("云南", "云南省");
        provinceMap.put("陕西", "陕西省");
        provinceMap.put("甘肃", "甘肃省");
        provinceMap.put("青海", "青海省");
        provinceMap.put("宁夏", "宁夏回族自治区");
        provinceMap.put("新疆", "新疆维吾尔自治区");
        provinceMap.put("西藏", "西藏自治区");
        provinceMap.put("广西", "广西壮族自治区");
        provinceMap.put("内蒙古", "内蒙古自治区");
        provinceMap.put("香港", "香港特别行政区");
        provinceMap.put("澳门", "澳门特别行政区");
        provinceMap.put("台湾", "台湾省");
    }

    // 根据省级名称获取完整名称
    public static String completeProvinceName(String provinceName) {
        return provinceMap.getOrDefault(provinceName, "未知省份");
    }

    public static void main(String[] args) {
        // 测试示例
        String[] testProvinces = {"北京", "湖南", "新疆", "香港", "台湾", "未知"};
        
        for (String province : testProvinces) {
            String fullName = completeProvinceName(province);
            System.out.println(province + " -> " + fullName);
        }
    }
}
