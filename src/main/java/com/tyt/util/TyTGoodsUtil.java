package com.tyt.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/08/28 10:38
 */
public class TyTGoodsUtil {

    /**
     * 是否是无价货源，价格为null，空字符串，0，0.0为无价货源
     */
    public static boolean isPriceless(String price) {
        return price == null || price.isEmpty() || price.equals("0") || price.equals("0.0");
    }

    /**
     * 加价
     */
    public static String addPrice(String price, String addPrice) {
        BigDecimal priceNumber = isPriceless(price) ? BigDecimal.ZERO : new BigDecimal(price);
        BigDecimal addPriceNumber = isPriceless(addPrice) ? BigDecimal.ZERO : new BigDecimal(addPrice);
        return priceNumber.add(addPriceNumber).toString();
    }
}
