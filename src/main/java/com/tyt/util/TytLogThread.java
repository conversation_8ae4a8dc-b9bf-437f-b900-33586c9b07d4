package com.tyt.util;

import com.tyt.transport.querybean.FallShortSearchLogBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.tyt.transport.service.TransportLogUtilService;


/**
 * 日志线程
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class TytLogThread implements Runnable {
    static final Logger gatherLog = LoggerFactory.getLogger("gatherLog");
    private TransportLogUtilService transportLogUtilService;
    private int type = 0;

    private long userId;
    private long tsId;
    private String clientVersion;
    private int clientSign;
    private int status;
    private int viewSource;
    private int sortIndex;
    private int specialMark;
    private String startProvinc;
    private String startCity;
    private String startArea;
    private String destProvinc;
    private String destCity;
    private String destArea;

    private String startCoord;
    private String startRange;
    private String destCoord;
    private String destRange;
    private long carId;
    private String headNo;
    private String headCity;
    private int sortType;

    private String numberType;
    private String osVersion;
    private String clientId;

    private String carLength;
    private String carType;
    private String specialRequired;
    private String startWeight;
    private String endWeight;

    private String benefitLabelCode;

    private Long sharerUserId;

    private FallShortSearchLogBean bean;

    public static TytLogThread detailsLog(int type, long userId, long tsId, String clientVersion,
                                          int clientSign, int status, int viewSource,
                                          int sortType, int sortIndex, int specialMark,
                                          String startProvinc, String startCity, String startArea,
                                          String destProvinc, String destCity, String destArea, String benefitLabelCode, Long sharerUserId) {
        return new TytLogThread(type, userId, tsId, clientVersion,
                clientSign, status, viewSource, sortType, sortIndex, specialMark,
                startProvinc, startCity, startArea, destProvinc, destCity, destArea, benefitLabelCode, sharerUserId);
    }

    public TytLogThread(int type, long userId, long tsId, String clientVersion,
                        int clientSign, int status, int viewSource,
                        int sortType, int sortIndex, int specialMark,
                        String startProvinc, String startCity, String startArea,
                        String destProvinc, String destCity, String destArea, String benefitLabelCode, Long sharerUserId) {
        this.type = type;
        this.userId = userId;
        this.tsId = tsId;
        this.clientVersion = clientVersion;
        this.clientSign = clientSign;
        this.status = status;
        this.viewSource = viewSource;
        this.sortType = sortType;
        this.sortIndex = sortIndex;
        this.specialMark = specialMark;
        this.startProvinc = startProvinc;
        this.startCity = startCity;
        this.startArea = startArea;
        this.destProvinc = destProvinc;
        this.destCity = destCity;
        this.destArea = destArea;
        this.benefitLabelCode = benefitLabelCode;
        this.sharerUserId = sharerUserId;
    }

    public static TytLogThread searchLog(int type, long userId, String startCoord, String startRange,
                                         String destCoord, String destRange, long carId, String headNo,
                                         String headCity, String clientVersion, int clientSign, int sortType, String numberType, String osVersion, String clientId, String carLength, String carType, String specialRequired,String startWeight,String endWeight) {
        return new TytLogThread(type, userId, startCoord, startRange,
                destCoord, destRange, carId, headNo,
                headCity, clientVersion, clientSign, sortType, numberType, osVersion, clientId, carLength, carType, specialRequired,startWeight,endWeight);
    }

    public TytLogThread(int type, long userId, String startCoord, String startRange,
                        String destCoord, String destRange, long carId, String headNo,
                        String headCity, String clientVersion, int clientSign, int sortType, String numberType, String osVersion, String clientId, String carLength, String carType, String specialRequired,String startWeight,String endWeight) {
        this.type = type;
        this.userId = userId;
        this.clientVersion = clientVersion;
        this.clientSign = clientSign;
        this.startCoord = startCoord;
        this.startRange = startRange;
        this.destCoord = destCoord;
        this.destRange = destRange;
        this.carId = carId;
        this.headNo = headNo;
        this.headCity = headCity;
        this.sortType = sortType;
        this.numberType = numberType;
        this.osVersion = osVersion;
        this.clientId = clientId;
        this.carLength = carLength;
        this.carType = carType;
        this.specialRequired = specialRequired;
        this.startWeight = startWeight;
        this.endWeight = endWeight;
    }

    public static TytLogThread searchFallShortLog(int type, FallShortSearchLogBean bean) {
        return new TytLogThread(type, bean);
    }

    public TytLogThread(int type, FallShortSearchLogBean bean) {
        this.type = type;
        this.bean = bean;
    }

    public static TytLogThread searchDistanceSortLog(int type, long userId, int sortType,
                                                     String clientVersion, int clientSign) {
        return new TytLogThread(type, userId, sortType,
                clientVersion, clientSign);
    }

    public TytLogThread(int type, long userId, int sortType,
                        String clientVersion, int clientSign) {

        this.type = type;
        this.sortType = sortType;
        this.userId = userId;
        this.clientVersion = clientVersion;
        this.clientSign = clientSign;
    }

    public void run() {
        try {
            if (type == 1) {
                transportLogUtilService.detailsLog(userId, tsId, clientVersion, clientSign, status, viewSource, sortType, sortIndex, specialMark, startProvinc, startCity, startArea, destProvinc, destCity, destArea, benefitLabelCode, sharerUserId);
            } else if (type == 2) {
                transportLogUtilService.searchLog(userId, startCoord, startRange, destCoord, destRange, carId, headNo, headCity, clientVersion, clientSign, sortType, numberType, osVersion, clientId, carLength, carType, specialRequired,startWeight,endWeight);
            } else if (type == 3) {
                transportLogUtilService.searchDistanceSortLog(userId, sortType, clientVersion, clientSign);
            } else if (type == 4) {
                transportLogUtilService.fallShortSearchLog(bean);
            }

        } catch (Exception e) {
            gatherLog.error("保存日志异常,错误信息", e);
        }
    }

    public void setTransportLogUtilService(
            TransportLogUtilService transportLogUtilService) {
        this.transportLogUtilService = transportLogUtilService;
    }

}
