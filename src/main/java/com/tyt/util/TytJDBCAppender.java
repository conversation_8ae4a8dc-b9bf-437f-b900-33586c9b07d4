package com.tyt.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

import org.apache.log4j.spi.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



public class TytJDBCAppender extends org.apache.log4j.jdbc.JDBCAppender {

	public Logger logger = LoggerFactory
			.getLogger(this.getClass());
    protected String jndiName;  
   
    protected Connection connection;  
    public TytJDBCAppender() {  
        super();  
    }  
    protected void closeConnection(Connection con) {  
        try {  
            if (connection != null && !connection.isClosed())  
                connection.close();  
        } catch (SQLException e) {  
            errorHandler.error("Error closing connection", e, ErrorCode.GENERIC_FAILURE);  
        }  
    }  
    @Override  
    protected Connection getConnection() throws SQLException {  
        try {  
        	  Class.forName("org.logicalcobwebs.proxool.ProxoolDriver");
              connection = DriverManager.getConnection("proxool.dbpool");
   
        } catch (Exception e) {  
            System.out.println(e.getMessage());  
        }  
        return connection;  
    }  
    protected void execute(String sql) throws SQLException {
    	logger.info("proxool execute sql:"+sql);
    	super.execute(sql);
      }
    
    
    /** 
     * @return the jndiName 
     */  
    public String getJndiName() {  
        return jndiName;  
    }  
    /** 
     * @param jndiName the jndiName to set 
     */  
    public void setJndiName(String jndiName) {  
        this.jndiName = jndiName;  
    }  
}  

