package com.tyt.util;

import com.sina.sae.channel.SaeChannel;

/**
 * User: Administrator
 * Date: 14-3-15
 * Time: 下午7:35
 */
public class ChannelDemo {

    public static void main(String[] args) {
        SaeChannel channel = new SaeChannel();
        String name = "test";//channel名称
        int duration = 1000;//channel过期时间
        String url = channel.createChannel(name, duration);//返回值为WebSocket的url
        //往上面创建的channel中发送一条消息 返回值num 为连接至channel的客户端数量
        int num = channel.sendMessage(name, "this is a test message");
        int errno = channel.getErrno();//获得错误码
        String errmsg = channel.getErrmsg();//获得错误信息


    }
}
