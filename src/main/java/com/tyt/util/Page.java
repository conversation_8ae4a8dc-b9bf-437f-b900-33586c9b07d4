package com.tyt.util;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用分页工具类，该工具类分页指定泛型的List对集合
 * 
 * <AUTHOR>
 * @time 2010-01-12
 * @param <E>
 *            指定的泛型
 */
public class Page<E>
{
	/**
	 * 
	 */
	public Page() {
		super();
	}

	/**
	 * 每页显示的记录数
	 */
	private int pageRecords = 8;

	/**
	 * 总记录数
	 */
	private int totalRecord;

	/**
	 * 分页切割的启始点
	 */
	private int startIndex;

	/**
	 * 分页切割的结束点
	 */
	private int endIndex;

	/**
	 * 总页数
	 */
	private int maxPage;

	/**
	 * 当前页数
	 */
	private int currentPage = 1;

	/**
	 * 总记录集合
	 */
	private List<E> totalList;

	public Page(List<E> totalList)
	{
		super();
		this.totalList = totalList;
		innit();
	}

	public List<E> getTotalList() {
		return totalList;
	}

	public void setTotalList(List<E> totalList) {
		this.totalList = totalList;
		innit();
	}

	/**
	 * 初始化该分页对象
	 */
	private void innit()
	{
		if (null != totalList)
		{
			totalRecord = totalList.size();

			if (totalRecord % this.pageRecords == 0)
			{
				this.maxPage = totalRecord / this.pageRecords;
			}
			else
			{
				this.maxPage = totalRecord / this.pageRecords + 1;
			}
		}
	}

	/**
	 * 得到分页后的数据
	 * 
	 * @return 分页数据
	 */
	public List<E> getPage(int currentPage)
	{
		this.currentPage = currentPage;

		if (currentPage <= 0)
		{
			this.currentPage = 1;
		}
		if (currentPage >= this.maxPage)
		{
			//下一页不得超过最大页
			this.currentPage = this.maxPage;
		}

		List<E> subList = new ArrayList<E>();

		if (null != this.totalList)
		{
			subList.addAll(this.totalList.subList(getStartIndex(), getEndIndex()));
		}

		return subList;
	}

	/**
	 * 设置每页显示的记录条数,如果不设置则默认为每页显示30条记录
	 * 
	 * @param pageRecords
	 *            每页显示的记录条数(值必需介于10~100之间)
	 */
	public void setPageRecords(int pageRecords)
	{
		/*if (pageRecords >= 10 && pageRecords <= 100)
		{
			this.pageRecords = pageRecords;

			innit();
		}*/
		this.pageRecords = pageRecords;
	}

	public int getStartIndex()
	{
		if (null == this.totalList)
		{
			return 0;
		}

		this.startIndex = (getCurrentPage() - 1) * this.pageRecords;

		if (startIndex > totalRecord)
		{
			startIndex = totalRecord;
		}

		if (startIndex < 0)
		{
			startIndex = 0;
		}

		return startIndex;
	}

	public int getEndIndex()
	{
		if (null == this.totalList)
		{
			return 0;
		}

		endIndex = getStartIndex() + this.pageRecords;

		if (endIndex < 0)
		{
			endIndex = 0;
		}

		if (endIndex < getStartIndex())
		{
			endIndex = getStartIndex();
		}

		if (endIndex > this.totalRecord)
		{
			endIndex = this.totalRecord;
		}

		return endIndex;
	}

	public int getmaxPage()
	{
		return maxPage;
	}

	public int getTotalRecord()
	{
		return totalRecord;
	}

	public boolean isEndPage()
	{
		return this.currentPage == this.maxPage;
	}

	/**
	 * 获取下一页的页数
	 * 
	 * @return 下一页的页数
	 */
	public int getNextPage()
	{
		int nextPage = this.currentPage + 1;

		if (nextPage > this.maxPage)
		{
			nextPage = this.maxPage;
		}
		if (nextPage <= 0)
		{
			nextPage = 1;
		}

		return nextPage;
	}

	/**
	 * 获取上一页的页数
	 * 
	 * @return 上一页的页数
	 */
	public int getPrivyPage()
	{
		int privyPage = this.currentPage - 1;

		if (privyPage > this.maxPage)
		{
			privyPage = this.maxPage;
		}

		if (privyPage <= 0)
		{
			privyPage = 1;
		}

		return privyPage;
	}

	public int getCurrentPage()
	{
		return currentPage;
	}
}
