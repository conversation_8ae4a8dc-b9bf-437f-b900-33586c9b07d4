package com.tyt.util;

import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @ClassName BusinessCodeUtil
 * @Description 校验统一社会信用代码
 * <AUTHOR>
 * @Date 2018-12-10 14:56
 * @Version 1.0
 */
public class BusinessCodeUtil {


   public static boolean isValid(String businessCode) {
        if ((businessCode.equals("")) || businessCode.length() != 18) {
            return false;
        }
        String baseCode = "0123456789ABCDEFGHJKLMNPQRTUWXY";
        char[] baseCodeArray = baseCode.toCharArray();
        Map<Character, Integer> codes = new HashMap<Character, Integer>();
        for (int i = 0; i < baseCode.length(); i++) {
            codes.put(baseCodeArray[i], i);
        }
        char[] businessCodeArray = businessCode.toCharArray();
        Character check = businessCodeArray[17];
        if (baseCode.indexOf(check) == -1) {
            return false;
        }
        int[] wi = { 1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28 };
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            Character key = businessCodeArray[i];
            if (baseCode.indexOf(key) == -1) {
                return false;
            }
            sum += (codes.get(key) * wi[i]);
        }
        int value = 31 - sum % 31;
        return value == codes.get(check);
    }

    public static boolean isValidNew(String businessCode) {
        if (StringUtils.isEmpty(businessCode)) {
            return false;
        }
        return Pattern.matches("^(?=.*\\d)(?=.*[A-Z])[a-zA-Z0-9]{18}$", businessCode.toUpperCase()) || Pattern.matches("^[0-9]{18}",businessCode.toUpperCase());
    }


    public static void main(String[] args) {
//        String businessCode = "911101083443477374";
//        boolean valid =  BusinessCodeUtil.isValid(businessCode);
//        System.out.println(valid);
        System.out.println(isValidNew("098765678987898098"));
    }

}
