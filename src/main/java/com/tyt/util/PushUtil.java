package com.tyt.util;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.gexin.rp.sdk.base.IQueryResult;
import com.gexin.rp.sdk.http.IGtPush;

  
public class PushUtil {

	public static Logger logger = LoggerFactory.getLogger(PushUtil.class);

	public static PropertiesFileUtil propertiesFileUtil = PropertiesFileUtil
			.init("push");

	public static String ip = propertiesFileUtil.getString("ip");
	// 您应用的mastersecret
	public static String master = propertiesFileUtil.getString("masterSecret");
	// 您应用的appkey
	public static String appkey = propertiesFileUtil.getString("appKey");
	// 您应用的appId
	public static String appId = propertiesFileUtil.getString("appId");

	public static ThreadPoolExecutor pushThreadPool;

	
	static {
		System.setProperty("gexin_pushList_needDetails", "true");
		pushThreadPool=new ThreadPoolExecutor( 16
				, 36
				,120
				,TimeUnit.SECONDS
				,new LinkedBlockingQueue<Runnable>(1000)
				,new ThreadPoolExecutor.AbortPolicy()
				);
	}

	public static void clearBadge(String cid){
		DelBadgeThread dbt=new DelBadgeThread(cid);
		pushThreadPool.execute(dbt);
	}

	public static void clearBadgeForIos(String cid){
		List<String> cidList = new ArrayList<String>();
        // 用户应用icon上显示的数字
	 	// "+1"即在原有badge上加1；具体详情使用请参考该接口描述
    	// "-1"即在原有badge上减1；具体详情使用请参考该接口描述
		// 直接设置badge数值，会覆盖原有数值；具体详情使用请参考该接口描述
        String badge = "0";
        cidList.add(cid);
        IGtPush push = new IGtPush(appkey, master);
       
        IQueryResult res = push.setBadgeForCID(badge, appId, cidList);
    	logger.info("停止{}CID,成功{}",cid, res.getResponse());
     //   System.out.println(res.getResponse());
	}
	

	public static void main(String[] args) {

	 /*   List<String> cidList = new ArrayList<String>();
        // 用户应用icon上显示的数字
        String badge = "";
        cidList.add("********************************");
        IGtPush push = new IGtPush(appkey, master);
        // "+1"即在原有badge上加1；具体详情使用请参考该接口描述
        badge = "0";
        // "-1"即在原有badge上减1；具体详情使用请参考该接口描述
        // badge = "-1";
        // 直接设置badge数值，会覆盖原有数值；具体详情使用请参考该接口描述
        //badge = "1";
        IQueryResult res = push.setBadgeForCID(badge, appId, cidList);
        
        System.out.println(res.getResponse());
		*/
        PushUtil.clearBadge("********************************");
	}
}
