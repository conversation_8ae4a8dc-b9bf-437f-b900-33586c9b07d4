package com.tyt.util.filter;

import cn.hutool.core.collection.ListUtil;
import com.tyt.util.ApplicationContextUtils;
import com.tyt.util.filter.wrapper.XssFilterWrapper;
import com.tyt.util.filter.wrapper.XssMultipartFilterWrapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * xss过滤器
 */
@Component("xssFilter")
public class XssFilter implements Filter {

    // 用于创建MultipartHttpServletRequest
    private MultipartResolver multipartResolver;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 注入bean
        multipartResolver = ApplicationContextUtils.getBean("multipartResolver");
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        String contentType = servletRequest.getContentType();
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        String url =httpRequest.getServletPath();
        List<String> paths = ListUtil.toLinkedList("/plat/message/list", "/openapi/message/message/send");
        if (!paths.contains(url)){
            if (StringUtils.isNotBlank(contentType) && contentType.contains("multipart/form-data")) {

                MultipartHttpServletRequest multipartHttpServletRequest = multipartResolver.resolveMultipart((HttpServletRequest) servletRequest);

                chain.doFilter(new XssMultipartFilterWrapper(multipartHttpServletRequest), servletResponse);

            } else {
                chain.doFilter(new XssFilterWrapper((HttpServletRequest) servletRequest), servletResponse);
            }
        }else {
            System.out.println("xssurl:----------------------"+httpRequest.getServletPath());
            chain.doFilter(servletRequest,servletResponse);
        }
    }

    @Override
    public void destroy() {

    }
}