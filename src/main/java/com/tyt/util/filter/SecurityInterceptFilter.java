package com.tyt.util.filter;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.cache.CacheService;
import com.tyt.model.BlacklistUser;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.plat.enums.ProgramFilterConfigEnum;
import com.tyt.plat.enums.UrlFilterConfigEnum;
import com.tyt.user.service.BlacklistUserService;
import com.tyt.user.service.UserService;
import com.tyt.util.*;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

/**
 * Servlet Filter implementation class SecurityIntercept
 * 验证重复登录，登录状态的Filter
 */
@Component("securityInterceptFilter")
public class SecurityInterceptFilter implements Filter {
	private static final Logger logger = LoggerFactory
			.getLogger(SecurityInterceptFilter.class);

	private Set<String> urlSet;

	private Set<String> fileUrlSet;

	/**
	 * 跳过ticket的URL集合，适用于个人货主请求
	 */
	private static final Set<String> SKIP_AUTHENTICATION_URLS = new HashSet<>();

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "userService")
	private UserService userService;

	/**
	 * Default constructor.
	 */
	public SecurityInterceptFilter() {
		// TODO Auto-generated constructor stub
	}

	/**
	 * @see Filter#destroy()
	 */
	@Override
	public void destroy() {
		// TODO Auto-generated method stub
	}

	/**
	 * @see Filter#doFilter(ServletRequest, ServletResponse, FilterChain)
	 */
	@Override
	public void doFilter(ServletRequest request, ServletResponse response,
			FilterChain chain) throws IOException, ServletException {
		// TODO Auto-generated method stub
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;

		String servletPath = httpRequest.getServletPath();
		String contentType = request.getContentType();
		String user_id = request.getParameter("userId");
		String ticket = request.getParameter("ticket");
		String clientSign = request.getParameter("clientSign");
		String clientId = request.getParameter("clientId");

		if(StringUtils.isNotBlank(contentType)&&contentType.indexOf("multipart/form-data") != -1&&fileUrlSet.contains(servletPath)){
			logger.info("请求地址【{}】,请求类型为文件类型", servletPath);
			chain.doFilter(request, response);
			return;
		}else {
			if (StringUtils.isNotBlank(contentType) && contentType.indexOf("multipart/form-data") != -1) {
				Map<String, Object> paramMap = getFormDataParam(httpRequest);
				user_id = Objects.toString(paramMap.get("userId"), null);
				ticket = Objects.toString(paramMap.get("ticket"), null);
				clientSign = Objects.toString(paramMap.get("clientSign"), null);
				clientId = Objects.toString(paramMap.get("clientId"), null);
				if( StringUtils.isBlank(user_id) || StringUtils.isBlank(ticket) || StringUtils.isBlank(clientSign) || StringUtils.isBlank(clientId)) {
					logger.info("form-data参数不完整：servletPath={},userId={}, ticket={}, clientSign={}, clientId={}",servletPath, user_id, ticket, clientSign, clientId);
				}
				//重新放回参数
//				RequestWrapper requestWrapper = new RequestWrapper(httpRequest);
//				requestWrapper.setCharacterEncoding("UTF-8");
//				requestWrapper.addAllParameters(paramMap);
//				httpRequest = requestWrapper;
			}
		}

		// 过滤url
		if (SKIP_AUTHENTICATION_URLS.contains(servletPath)) {
			chain.doFilter(request, response);
			return;
		} else { //进行ticket鉴权
			if (null != urlSet && urlSet.contains(servletPath)) {
				// 检测 ticket
				checkTicketNew(user_id, ticket, clientSign,clientId, httpRequest, httpResponse, chain);
			} else {
				if (null != user_id && !"".equals(user_id) && null != ticket && !"".equals(ticket)) {
					checkTicketNew(user_id, ticket, clientSign,clientId, httpRequest, httpResponse, chain);
				} else {
					chain.doFilter(request, response);
					return;
				}
			}
		}
	}

	/**
	 * form-data请求方式参数获取
	 * @param httpRequest
	 */
	private Map<String,Object> getFormDataParam(HttpServletRequest httpRequest) {
		DiskFileItemFactory factory = new DiskFileItemFactory();
		ServletFileUpload sfu = new ServletFileUpload(factory);
		try {
//			Map<String, String[]> parameterMap = httpRequest.getParameterMap();
//			logger.info("getFormDataParam httpRequest class:{} url:{}",parameterMap.getClass(),httpRequest.getServletPath());
			StringBuffer str = new StringBuffer();
			List<FileItem> list = sfu.parseRequest(httpRequest);
			Map paramMap = new HashMap();
			for (FileItem fileItem : list) {
				//判断是否是普通表单
				if (fileItem.isFormField()) {
					paramMap.put(fileItem.getFieldName(), fileItem.getString("UTF-8"));
					str.append(fileItem.getFieldName() + "=" + fileItem.getString("UTF-8") + "&");

					//parameterMap.put(fileItem.getFieldName(), new String[]{fileItem.getString()});

				}
			}
			logger.info("SecurityInterceptFilter：getFormDataParam结果： {}",  str);
			return paramMap;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return new HashMap<>();
	}

	/**
	 * 枚举配置.
	 */
	private void initWithEnum(){
		UrlFilterConfigEnum[] configArray = UrlFilterConfigEnum.values();
		for(UrlFilterConfigEnum oneUrlEnum : configArray){
			if(oneUrlEnum.isSkipSecurity()){
				SKIP_AUTHENTICATION_URLS.add(oneUrlEnum.getUrl());
			}
		}
	}

	/**
	 * @see Filter#init(FilterConfig)
	 */
	@Override
	public void init(FilterConfig fConfig) throws ServletException {
		PropertiesUtil propertiesUtil = PropertiesUtil
				.init("security_intercept_url");
		urlSet = propertiesUtil.getKeySet();

		//文件上传跳过
		fileUrlSet =new HashSet<>();
		fileUrlSet.add("/plat/user/identity/auth/save");
		fileUrlSet.add("/plat/car/identity/updatesave");
		fileUrlSet.add("/plat/car/identity/save");
		fileUrlSet.add("/plat/transport/condition/file/write");
		fileUrlSet.add("/plat/user/identity/auth/save.action");
		fileUrlSet.add("/plat/user/identity/auth/saveEnterprise");
		fileUrlSet.add("/plat/user/identity/auth/saveNew");
		fileUrlSet.add("/plat/user/identity/auth/saveAuthPic");
		fileUrlSet.add("/plat/common/upload/img");
		fileUrlSet.add("/plat/user/head/save");
		fileUrlSet.add("/plat/user/head/save.action");
		fileUrlSet.add("/plat/user/identity/save");
		fileUrlSet.add("/plat/user/identity/photo/save");
		fileUrlSet.add("/plat/maintainer/update");
		fileUrlSet.add("/plat/maintainer/save");
		fileUrlSet.add("/plat/car/driver/save");
		fileUrlSet.add("/openapi/car/driver/save");
		fileUrlSet.add("/openapi/car/identity/save");
		fileUrlSet.add("/plat/resource/getResourceInterfaceUrl.action");
		fileUrlSet.add("/plat/resource/global/getByParamList.action");
		fileUrlSet.add("/stimulate/updateStimulateOrderStatus.action"); // 刷单凭证上传
		fileUrlSet.add("/stimulate/updateStimulate.action"); // 刷单凭证上传

		SKIP_AUTHENTICATION_URLS.add("/cargocloud/transport/getSingleDetail");
		SKIP_AUTHENTICATION_URLS.add("/cargocloud/transport/saveGoodsStatusNew");
		SKIP_AUTHENTICATION_URLS.add("/cargocloud/transport/getCarPosition");
		SKIP_AUTHENTICATION_URLS.add("/plat/infofee/detail/giveBack.action"); // 订金不退还业务-退款接口
		SKIP_AUTHENTICATION_URLS.add("/plat/user/wxMiniLoginLimit.action"); // 检查小程序是否限制登录的接口
		SKIP_AUTHENTICATION_URLS.add("/plat/user/checkLimitPublish.action"); // 检查小程序是否限制发货的接口
		SKIP_AUTHENTICATION_URLS.add("/plat/userCancel/get/judgeUserCancelOrNot.action");
		SKIP_AUTHENTICATION_URLS.add("/plat/infoFee/transport/makePriceByGoodCarPriceTransportCarryPrice.action");
		SKIP_AUTHENTICATION_URLS.add("/plat/transport/V5930/autoAssignOrderForSpecialCar.action");
		SKIP_AUTHENTICATION_URLS.add("/plat/freight/getSameTransportAveragePrice.action");
		SKIP_AUTHENTICATION_URLS.add("/plat/transportQuotedPrice/transportAgree.action");
		SKIP_AUTHENTICATION_URLS.add("/plat/infoFee/transport/saveDirectForAutoResend.action");
		SKIP_AUTHENTICATION_URLS.add("/plat/user/identity/auth/updateUserPermissionResult");
		SKIP_AUTHENTICATION_URLS.add("/plat/transportQuotedPrice/carQuotedPrice.action");

		this.initWithEnum();
	}

	public void checkTicket(String user_id, String ticket, String clientSign, HttpServletRequest request, HttpServletResponse response,
							FilterChain chain) throws IOException, ServletException {
		// 加上区分上传文件判读
		String contentType = request.getContentType();
		if (StringUtils.isNotBlank(contentType) && contentType.indexOf("multipart/form-data") != -1) {

			chain.doFilter(request, response);

		} else if (StringUtils.isNotBlank(user_id) && StringUtils.isNotBlank(ticket)) {

			String objTicketKey = UserTicketUtil.getObjTicketKey(user_id);
			String carTicketKey = UserTicketUtil.getCarTicketKey(user_id);
			String goodsTicketKey = UserTicketUtil.getGoodsTicketKey(user_id);
			String miniProgramTicketKey = UserTicketUtil.getMiniProgramGoodsKey(user_id);


			Object objTicket = cacheService.getObject(objTicketKey);
			String carTicket = cacheService.getString(carTicketKey);
			String goodsTicket = cacheService.getString(goodsTicketKey);
			String miniProgramTicket = cacheService.getString(miniProgramTicketKey);
			//老ticket
			Object oldObjTicket = cacheService.getObject(Constant.CACHE_OLD_TICKET_KEY + objTicketKey);
			String oldCarTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + carTicketKey);
			String oldGoodsTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + goodsTicketKey);
			String oldMiniProgramTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + miniProgramTicketKey);
			//新增clientId和ticket加密对比

            String clientId = request.getParameter("clientId");
            if (StringUtils.isNotEmpty(clientId)){
                String clientTicket = clientIdAndTicketMd5(clientId, ticket);
                if (StringUtils.isNotEmpty(clientTicket)&&!(clientTicket.equals(clientIdAndTicketMd5(clientId,objTicket))
                        || clientTicket.equals(clientIdAndTicketMd5(clientId,carTicket))|| clientTicket.equals(clientIdAndTicketMd5(clientId,goodsTicket))
						|| clientTicket.equals(clientIdAndTicketMd5(clientId,miniProgramTicket))
                        || clientTicket.equals(clientIdAndTicketMd5(clientId,oldObjTicket))|| clientTicket.equals(clientIdAndTicketMd5(clientId,oldCarTicket))
                        || clientTicket.equals(clientIdAndTicketMd5(clientId,oldGoodsTicket)) || clientTicket.equals(clientIdAndTicketMd5(clientId,oldMiniProgramTicket)))){
                    logger.info("userId={}用户未登录或ticket过期,md5client", user_id);
                    ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
                    printJSON(request, response, msgBean);
                    return;
                }
            }else {
                if (isIncludedPath(request.getServletPath())){
                    logger.info("userId={}用户未登录或ticket过期,clientId为空", user_id);
                    ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
                    printJSON(request, response, msgBean);
                    return;
                } else {
					logger.info("未传递必要参数,clientId为空,userId={},path={}", user_id, request.getServletPath());
				}
            }

			if (StringUtils.isBlank(clientSign)) {

				if (objTicket == null && StringUtils.isBlank(carTicket) && StringUtils.isBlank(goodsTicket)
						&&oldObjTicket==null&& StringUtils.isBlank(oldCarTicket) && StringUtils.isBlank(oldGoodsTicket)) {
					logger.info("userId={}用户未登录或ticket过期", user_id);
					ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
					printJSON(request, response, msgBean);
					return;
				}

				if (ticket.equals(objTicket) || ticket.equals(carTicket) || ticket.equals(goodsTicket)
						|| ticket.equals(oldObjTicket)|| ticket.equals(oldCarTicket) || ticket.equals(oldGoodsTicket)) {

					chain.doFilter(request, response);

				} else if ("0000".equals(objTicket) || "0000".equals(carTicket) || "0000".equals(goodsTicket)
						|| "00000".equals(objTicket) || "00000".equals(carTicket) || "00000".equals(goodsTicket)) {
					logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
					ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
					printJSON(request, response, msgBean);
				} else {
					logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(6));
					ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
					printJSON(request, response, msgBean);
				}
			} else {

				int client = Integer.parseInt(clientSign);
				if (client <= 6 || Constant.ClientSignEnum.PC_GOODS.code == client) {

					if (objTicket == null) {
						logger.info("userId={}用户未登录或ticket过期", user_id);
						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
						printJSON(request, response, msgBean);
						return;
					} else {
						if (ticket.equals(objTicket)||ticket.equals(oldObjTicket)) {
							chain.doFilter(request, response);
						} else if ("0000".equals(objTicket) || "00000".equals(objTicket)) {
							logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
							printJSON(request, response, msgBean);
							return;
						} else {
							logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(client));
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
							printJSON(request, response, msgBean);
							return;
						}
					}
				}

				if (Constant.ClientSignEnum.ANDROID_CAR.code == client || Constant.ClientSignEnum.IOS_CAR.code == client) {

					if (StringUtils.isBlank(carTicket)) {
						logger.info("userId={}用户未登录或ticket过期", user_id);
						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
						printJSON(request, response, msgBean);
						return;
					} else {
						if (ticket.equals(carTicket)||ticket.equals(oldCarTicket)) {
							chain.doFilter(request, response);
						} else if ("0000".equals(carTicket) || "00000".equals(carTicket)) {
							logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
							printJSON(request, response, msgBean);
							return;
						} else {
							logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(client));
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
							printJSON(request, response, msgBean);
							return;
						}
					}
				}

				if (Constant.ClientSignEnum.ANDROID_GOODS.code == client || Constant.ClientSignEnum.IOS_GOODS.code == client
						|| Constant.ClientSignEnum.WEB_GOODS.code == client) {

					if (StringUtils.isBlank(goodsTicket)) {
						logger.info("userId={}用户未登录或ticket过期", user_id);
						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
						printJSON(request, response, msgBean);
					} else {
						if (ticket.equals(goodsTicket)||ticket.equals(oldGoodsTicket)) {
							chain.doFilter(request, response);
						} else if ("0000".equals(goodsTicket) || "00000".equals(goodsTicket)) {
							logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
							printJSON(request, response, msgBean);
						} else {
							logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(client));
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
							printJSON(request, response, msgBean);
						}
					}
				}
			}
		} else {
			logger.info("受保护URL:{}缺少userId 和ticket 参数", request.getServletPath());
			ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "基础参数缺失");
			printJSON(request, response, msgBean);
		}
	}

	public void checkTicketNew(String user_id, String ticket, String clientSign,String clientId, HttpServletRequest request, HttpServletResponse response,
							FilterChain chain) throws IOException, ServletException {
		// 加上区分上传文件判读
//		String contentType = request.getContentType();
//		if (StringUtils.isNotBlank(contentType) && contentType.indexOf("multipart/form-data") != -1) {
//
//			chain.doFilter(request, response);
//
//		} else
		if (StringUtils.isNotBlank(user_id) && StringUtils.isNotBlank(ticket)) {
			//V6450 拉黑需求 新增拉黑后踢登相应码
//			try {
//				List<User> userList = userService.getUserListByIds(user_id);
//				logger.info("intercept filter check user black status, user info :{}", JSON.toJSONString(userList));
//				if (userList != null && !userList.isEmpty()) {
//					User user = userList.get(0);
//					if (1 == user.getBlackStatus()) {
//						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.BLACKLIST_NOT_LOGGED_IN_MSG);
//						printJSON(request, response, msgBean);
//						return;
//					}
//				}
//			} catch (Exception e) {
//				logger.error("intercept filter check user black status error:", e);
//			}

			String objTicketKey = UserTicketUtil.getObjTicketKey(user_id);
			String carTicketKey = UserTicketUtil.getCarTicketKey(user_id);
			String goodsTicketKey = UserTicketUtil.getGoodsTicketKey(user_id);
			String miniProgramGoodsKey = UserTicketUtil.getMiniProgramGoodsKey(user_id);
			String miniProgramTruckerKey = UserTicketUtil.getMiniProgramTruckerKey(user_id);

			//String clientId = request.getParameter("clientId");
			String clientTicket = clientIdAndTicketMd5(clientId, ticket);
			if (StringUtils.isBlank(clientId)&&isIncludedPath(request.getServletPath())){
				logger.info("userId={}用户未登录或ticket过期,clientId为空", user_id);
				ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
				printJSON(request, response, msgBean);
				return;
			}

			if (StringUtils.isNotBlank(clientSign)) {

				int client = Integer.parseInt(clientSign);
				if (Constant.ClientSignEnum.PC.code == client
						|| Constant.ClientSignEnum.ANDROID.code == client
						|| Constant.ClientSignEnum.IOS.code == client
						|| Constant.ClientSignEnum.PC_GOODS.code == client
						|| Constant.ClientSignEnum.MANAGER.code == client) {
					Object objTicket = cacheService.getObject(objTicketKey);
					if (objTicket == null) {
						logger.info("userId={}用户未登录或ticket过期", user_id);
						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
						printJSON(request, response, msgBean);
						return;
					} else {
						if (clientTicket.equals(clientIdAndTicketMd5(clientId,objTicket))) {
							chain.doFilter(request, response);
							return;
						} else if ("0000".equals(objTicket) || "00000".equals(objTicket)) {
							logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
							printJSON(request, response, msgBean);
							return;
						} else {
                            Object oldObjTicket = cacheService.getObject(Constant.CACHE_OLD_TICKET_KEY + objTicketKey);
                            if (clientTicket.equals(clientIdAndTicketMd5(clientId,oldObjTicket))){
                                chain.doFilter(request, response);
                            }else {
                                logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(client));
                                ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
                                printJSON(request, response, msgBean);
                                return;
                            }
						}
					}
				}else if (Constant.ClientSignEnum.ANDROID_CAR.code == client || Constant.ClientSignEnum.IOS_CAR.code == client) {
					String carTicket = cacheService.getString(carTicketKey);
					if (StringUtils.isBlank(carTicket)) {
						logger.info("userId={}用户未登录或ticket过期", user_id);
						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
						printJSON(request, response, msgBean);
						return;
					} else {
						if (clientTicket.equals(clientIdAndTicketMd5(clientId,carTicket))) {
							chain.doFilter(request, response);
							return;
						}else if ("0000".equals(carTicket) || "00000".equals(carTicket)) {
							logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
							printJSON(request, response, msgBean);
							return;
						} else {
                            String oldCarTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + carTicketKey);
                            if (clientTicket.equals(clientIdAndTicketMd5(clientId,oldCarTicket))){
                                chain.doFilter(request, response);
                                return;
                            }else {
                                logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(client));
                                ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
                                printJSON(request, response, msgBean);
                                return;
                            }
						}
					}
				}else if (Constant.ClientSignEnum.ANDROID_GOODS.code == client || Constant.ClientSignEnum.IOS_GOODS.code == client
						|| Constant.ClientSignEnum.WEB_GOODS.code == client) {
					String goodsTicket = cacheService.getString(goodsTicketKey);
					if (StringUtils.isBlank(goodsTicket)) {
						logger.info("userId={}用户未登录或ticket过期", user_id);
						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
						printJSON(request, response, msgBean);
						return;
					} else {
						if (clientTicket.equals(clientIdAndTicketMd5(clientId,goodsTicket))) {
							chain.doFilter(request, response);
							return;
						}else if ("0000".equals(goodsTicket) || "00000".equals(goodsTicket)) {
							logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
							printJSON(request, response, msgBean);
							return;
						} else {
                            String oldGoodsTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + goodsTicketKey);

							boolean ticketEquals = clientTicket.equals(clientIdAndTicketMd5(clientId, oldGoodsTicket));
							if(ticketEquals){
                                chain.doFilter(request, response);
                                return;
                            }else {
                                logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(client));
                                ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
                                printJSON(request, response, msgBean);
                                return;
                            }
						}
					}
				}else if(Constant.ClientSignEnum.MINI_PROGRAM_GOODS.code == client){
					String miniProgramTicket = cacheService.getString(miniProgramGoodsKey);
					if (StringUtils.isBlank(miniProgramTicket)) {
						logger.info("userId={}用户未登录或ticket过期", user_id);
						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
						printJSON(request, response, msgBean);
						return;
					} else {
						if (clientTicket.equals(clientIdAndTicketMd5(clientId,miniProgramTicket))) {
							chain.doFilter(request, response);
							return;
						}else if ("0000".equals(miniProgramTicket) || "00000".equals(miniProgramTicket)) {
							logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
							printJSON(request, response, msgBean);
							return;
						} else {
							String oldMiniProgramTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + miniProgramGoodsKey);
							if(clientTicket.equals(clientIdAndTicketMd5(clientId,oldMiniProgramTicket))){
								chain.doFilter(request, response);
								return;
							}else {
								logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(client));
								ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
								printJSON(request, response, msgBean);
								return;
							}
						}
					}
				} else if(Constant.ClientSignEnum.MINI_PROGRAM_TRUCKER.code == client){
					String miniProgramTruckerTicket =cacheService.getString(miniProgramTruckerKey);
					if (StringUtils.isBlank(miniProgramTruckerTicket)) {
						logger.info("userId={}用户未登录或ticket过期", user_id);
						ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
						printJSON(request, response, msgBean);
						return;
					} else {
						if (clientTicket.equals(clientIdAndTicketMd5(clientId,miniProgramTruckerTicket))) {
							chain.doFilter(request, response);
							return;
						}else if ("0000".equals(miniProgramTruckerTicket) || "00000".equals(miniProgramTruckerTicket)) {
							logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
							ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
							printJSON(request, response, msgBean);
							return;
						} else {
							String oldMiniProgramTruckerTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + miniProgramTruckerKey);
							if(clientTicket.equals(clientIdAndTicketMd5(clientId,oldMiniProgramTruckerTicket))){
								chain.doFilter(request, response);
								return;
							}else {
								// 如果是小程序复用的h5页面的接口，小程序过来不验签
								if (ProgramFilterConfigEnum.getUrlSet().contains(request.getServletPath())) {
									chain.doFilter(request, response);
									return;
								} else {
									logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(client));
									ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
									printJSON(request, response, msgBean);
									return;
								}
							}
						}
					}
				} else {
					logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(6));
					ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
					printJSON(request, response, msgBean);
					return;
				}

			} else {

				String carTicket = cacheService.getString(carTicketKey);
				String goodsTicket = cacheService.getString(goodsTicketKey);
				String miniProgramTicket = cacheService.getString(miniProgramGoodsKey);
				Object objTicket = cacheService.getObject(objTicketKey);
				if (StringUtils.isBlank(carTicket) && StringUtils.isBlank(goodsTicket)) {
					logger.info("userId={}用户未登录或ticket过期", user_id);
					ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
					printJSON(request, response, msgBean);
					return;
				}

				if ( clientTicket.equals(clientIdAndTicketMd5(clientId,carTicket)) || clientTicket.equals(clientIdAndTicketMd5(clientId,goodsTicket))
						|| clientTicket.equals(clientIdAndTicketMd5(clientId,objTicket)) || clientTicket.equals(clientIdAndTicketMd5(clientId,miniProgramTicket))) {
					chain.doFilter(request, response);
					return;
				} else if ( "0000".equals(carTicket) || "0000".equals(goodsTicket)
						|| "00000".equals(carTicket) || "00000".equals(goodsTicket)) {
					logger.info("userId={}，由于后台骚操作，改变了ticket值，需返回1003", user_id);
					ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.NOT_LOGGED_IN_CODE, ReturnCodeConstant.NOT_LOGGED_IN_MSG);
					printJSON(request, response, msgBean);
					return;
				} else {
                    String oldCarTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + carTicketKey);
                    String oldGoodsTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + goodsTicketKey);
                    String oldMiniProgramTicket = cacheService.getString(Constant.CACHE_OLD_TICKET_KEY + miniProgramGoodsKey);
                    Object oldObjTicket = cacheService.getObject(Constant.CACHE_OLD_TICKET_KEY + objTicketKey);
                    if (clientTicket.equals(clientIdAndTicketMd5(clientId,oldCarTicket)) || clientTicket.equals(clientIdAndTicketMd5(clientId,oldGoodsTicket))
                            ||clientTicket.equals(clientIdAndTicketMd5(clientId,oldObjTicket))  ||clientTicket.equals(clientIdAndTicketMd5(clientId,oldMiniProgramTicket))){
                        chain.doFilter(request, response);
                        return;
                    }else{
                        logger.info("userId={}用户采用{}终端访问系统，需要重新登录", user_id, Constant.ClientSignEnum.getClientSignEnum(6));
                        ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.REMOTE_LOGIN_CODE, ReturnCodeConstant.REMOTE_LOGIN_MSG);
                        printJSON(request, response, msgBean);
                        return;
                    }
				}

			}
		} else {
			logger.info("受保护URL:{}缺少userId 和ticket 参数", request.getServletPath());
			ResultMsgBean msgBean = new ResultMsgBean(ReturnCodeConstant.BASIC_PARAMETER_ERROR, "基础参数缺失");
			printJSON(request, response, msgBean);
			return;
		}
	}

	protected void printJSON(HttpServletRequest servletRequest,
			HttpServletResponse servletResponse, Object obj) {
		PrintWriter writer = null;
		try {
			// servletResponse.setHeader("Content-Encoding","gzip");
			// servletResponse.setHeader("Content-Type","application/gzip");
			// servletResponse.setContentType("application/gzip");
			servletResponse.setContentType("text/html;charset=UTF-8");
			servletResponse.setCharacterEncoding("UTF-8");

			writer = servletResponse.getWriter();
			String jsoncallback = servletRequest.getParameter("jsoncallback");
			if (null == jsoncallback || jsoncallback.isEmpty()) {
				writer.print(JSON.toJSONString(obj));
			} else {
				writer.print(jsoncallback + "(" + JSON.toJSONString(obj) + ")");
			}
		} catch (Exception e) {
			logger.error("输出信息失败！", e);
		} finally {
			if (null != writer) {
				writer.close();
			}
		}
	}

	/**
	 * clientId和ticket加密
	 * @param clientId
	 * @param ticket
	 * @return
	 */
	private String clientIdAndTicketMd5(String clientId,Object ticket){
		if (StringUtils.isNotBlank(clientId)&&ticket!=null&&StringUtils.isNotEmpty(ticket.toString())){
			return Encoder.md5(clientId+ticket);
		}
		if (StringUtils.isBlank(clientId)){
			return Encoder.md5(ticket+"");
		}
		return null;
	}

    /**
     * 查询当前接口是否是必须校验clientId接口
     * @param path
     * @return
     */
	private boolean isIncludedPath(String path) {
		if (StringUtils.isNotEmpty(path)) {
			// "/plat/transport/V5930/publish", 因PC端有发布货源时，无法获取clientId的情况，移除发布货源连接验证
			List<String> paths = ListUtil.toLinkedList("/plat/transport/search/list",
					"/plat/transport/fall/short/scope/search", "/plat/transport/fall/short/provinc/search", "/plat/transport/vary",
					"/plat/transport/search/destList", "/plat/user/simulatedLogin", "/plat/transport/search/similarity", "/plat/transport/search/similarity");
			return paths.contains(path);
		}
		return false;
	}
}
