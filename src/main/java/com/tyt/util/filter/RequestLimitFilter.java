package com.tyt.util.filter;

import com.alibaba.fastjson.JSON;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 
 * <AUTHOR>
 * @date 2018年3月5日下午2:24:53
 * @description
 */
@Component("requestLimitFilter")
public class RequestLimitFilter implements Filter {
	private static final Logger logger = LoggerFactory.getLogger(RequestLimitFilter.class);

	// 接口地址和限制信息的map，格式: url地址->时间间隔#访问次数
	public static Map<String, String> urlLimitMap = new HashMap<String, String>();
	// 存储url的访问次数的mmap
	public static Map<String, UrlVisitMsg> urlAndVisitTime = new HashMap<String, UrlVisitMsg>();
	// 多例map
	public static Map<String, UrlVisitMsg> urlInstance = new HashMap<String, UrlVisitMsg>();
	private AtomicInteger threadNum = new AtomicInteger(0);

	public RequestLimitFilter() {
	}

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;
		try {
			String servletPath = httpRequest.getServletPath();
			logger.info("doFilter servletPath is {}",servletPath);
			// 如果不是静态文件才拦截
			if (!isStaticFile(servletPath)) {
				if (urlLimitMap != null && urlLimitMap.size() > 0) {
					// 根据请求地址获取限制信息
					String limitMsg = urlLimitMap.get(servletPath);
					if (StringUtils.isNotEmpty(limitMsg)) {
						int threadLocalNum = threadNum.addAndGet(1);
						String limitTime = limitMsg.split("#")[0];
						String maxVisitTimes = limitMsg.split("#")[1];
						logger.info("requestLimitFilter servletPath is: " + servletPath + " , limitTime is: " + limitTime + " , maxVisitTimes is: "
								+ maxVisitTimes);
						logger.info("requestLimitFilter threadNum is: " + threadLocalNum);
						// 获取当前接口的访问信息
						UrlVisitMsg urlTime = urlAndVisitTime.get(servletPath);
						logger.info("requestLimitFilter key is: " + servletPath + " , visitTimes is: " + urlTime);
						ResultMsgBean rm = new ResultMsgBean();
						if (urlTime != null) {
							// 判断是否超过时间区间
							Long firstMilliSec = urlTime.getFirstMilliSec();
							if ((System.currentTimeMillis() - firstMilliSec) >= Integer.valueOf(limitTime)) {
								logger.info("requestLimitFilter reset servletPath:" + servletPath + ", threadNum is: " + threadLocalNum);
								urlTime.reset();
							} else if (urlTime.getVisitTime().intValue() >= Integer.valueOf(maxVisitTimes)) {
								logger.info("requestLimitFilter servletPath: " + servletPath + " visit limited");
								rm.setCode(ReturnCodeConstant.VISIT_TOO_FREQUENT);
								rm.setMsg("访问过于频繁");
								printJSON(httpRequest, httpResponse, rm);
								return;
							} else {
								logger.info("requestLimitFilter servletPath: " + servletPath + " incr visit times");
								// 增加访问次数
								// RedisUtil.incr(key);
								urlTime.getVisitTime().addAndGet(1);
							}
						}
					}
				}
			}

			chain.doFilter(httpRequest, httpResponse);

		} catch (Throwable e) {

			logger.info("requestLimitFilter error: " + e);
			chain.doFilter(httpRequest, httpResponse);

		}
	}

	private boolean isStaticFile(String servletPath) {
		return servletPath.endsWith(".png") || servletPath.endsWith(".js") || servletPath.endsWith(".css") || servletPath.endsWith(".jpeg")
				|| servletPath.endsWith(".gif") || servletPath.endsWith(".jpg") || servletPath.endsWith(".html") || servletPath.endsWith(".htm");
	}

	// 移动到TytSourceUtil中定时更新
	public static String cacheKey = "request.url.limit.plat.cache";
	// 限制的url信息，5分钟从通过TytSourceUtil更新一次，防止可能的并发造成对redis压力
	public static String urlLimitCache = "";

	protected void printJSON(HttpServletRequest servletRequest, HttpServletResponse servletResponse, Object obj) {
		PrintWriter writer = null;
		try {
			servletResponse.setContentType("text/html;charset=UTF-8");
			servletResponse.setCharacterEncoding("UTF-8");
			writer = servletResponse.getWriter();
			String jsoncallback = servletRequest.getParameter("jsoncallback");
			if (null == jsoncallback || jsoncallback.isEmpty()) {
				writer.print(JSON.toJSONString(obj));
			} else {
				writer.print(jsoncallback + "(" + JSON.toJSONString(obj) + ")");
			}
		} catch (Exception e) {
			logger.error("输出信息失败！", e);
		} finally {
			if (null != writer) {
				writer.close();
			}
		}
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
	}

	@PostConstruct
	private void initSourceUtil() {
		// 只为加载TytSourceUtil启动线程
		@SuppressWarnings("unused")
		boolean threadStart = TytSourceUtil.threadStart;
	}

	public void destroy() {
	}
}
