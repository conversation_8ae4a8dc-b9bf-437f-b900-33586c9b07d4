package com.tyt.util.filter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.WeakHashMap;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.JSON;
import com.tyt.config.util.AppConfig;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;

/**
 * 
 * <AUTHOR>
 * @date 2016-8-18上午11:05:09
 * @description
 */
public class FrequentlyVisitFilter implements Filter {
	private static final Logger logger = LoggerFactory.getLogger(FrequentlyVisitFilter.class);

	/*
	 * 存储用户id和用户调用获取电话接口时间的map
	 */
	private WeakHashMap<String, Long> userVisitMap = new WeakHashMap<String, Long>();

	public FrequentlyVisitFilter() {
	}

	public void destroy() {
	}

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		ResultMsgBean rm = new ResultMsgBean();
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;
		String userId = request.getParameter("userId");
		/*
		 * 对用户id进行非空判断
		 */
		if (StringUtils.isEmpty(userId)) {
			rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
			rm.setMsg("userId不能为空");
			printJSON(httpRequest, httpResponse, rm);
			return;
		}
		/*
		 * 获取用户最近一次调用接口的时间，如果为空则说明还没有调用过，则记录时间方放行，否则判断时间间隔是否大于指定时间如果大于则放行并更新时间，
		 * 否则直接返回调用过于频繁的信息
		 */
		Long timeInMili = userVisitMap.get(userId);
		if (timeInMili == null) {
			userVisitMap.put(userId, System.currentTimeMillis());
			chain.doFilter(request, response);
		} else {
			// 获取最小访问时间间隔,单位为妙
			String minTimeInterval = AppConfig.getProperty("min.visit.time.interval");
			if (StringUtils.isEmpty(minTimeInterval)) {
				minTimeInterval = "1";
			}
			long timeInterval = System.currentTimeMillis() - timeInMili;
			if (timeInterval > Long.valueOf(minTimeInterval) * 1000) {
				userVisitMap.put(userId, System.currentTimeMillis());
				chain.doFilter(request, response);
			} else {
				rm.setCode(ReturnCodeConstant.VISIT_TOO_FREQUENT);
				rm.setMsg("访问过于频繁");
				printJSON(httpRequest, httpResponse, rm);
				return;
			}
		}
	}

	public void init(FilterConfig fConfig) throws ServletException {
	}

	protected void printJSON(HttpServletRequest servletRequest, HttpServletResponse servletResponse, Object obj) {
		PrintWriter writer = null;
		try {
			servletResponse.setContentType("text/html;charset=UTF-8");
			servletResponse.setCharacterEncoding("UTF-8");
			writer = servletResponse.getWriter();
			String jsoncallback = servletRequest.getParameter("jsoncallback");
			if (null == jsoncallback || jsoncallback.isEmpty()) {
				writer.print(JSON.toJSONString(obj));
			} else {
				writer.print(jsoncallback + "(" + JSON.toJSONString(obj) + ")");
			}
		} catch (Exception e) {
			logger.error("输出信息失败！", e);
		} finally {
			if (null != writer) {
				writer.close();
			}
		}
	}
}
