package com.tyt.util.filter;

import com.tyt.util.filter.wrapper.RequestWrapper;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Component("repeatedlyReadFilter")
public class RepeatedlyReadFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain filterChain) throws IOException, ServletException {
        ServletRequest requestWrapper = null;
        if(servletRequest instanceof HttpServletRequest) {
            if(null == servletRequest.getContentType() ||
                    servletRequest.getContentType().contains(MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                /**
                 * 类型是application/x- www-form-urlencoded时也可以直接调用request.getInputStream()或request.getReader()或request.getInputStream()方法
                 * 获取到请求内容再解析出具体都参数，但前提是还没调用request.getParameter()方法
                 * (先写了getParameter()方法，再用getReader()方法不能取到数据)。
                 * 此时当request.getInputStream()或request.getReader()获取到请求内容后，
                 * 无法再调request.getParameter()获取请求内容。
                 * 即对该类型的请求，三个方法互斥，只能调其中一个。
                 * 使用此特性，对表单请求进行处理，来保证InputStream读取回填后，request.getParameter()能获取到值
                 */
                servletRequest.getParameter("");
            }
            requestWrapper = new RequestWrapper((HttpServletRequest) servletRequest);
        }
        if(requestWrapper == null) {
            filterChain.doFilter(servletRequest, servletResponse);
        } else {
            filterChain.doFilter(requestWrapper, servletResponse);
        }
    }

    @Override
    public void destroy() {

    }
}