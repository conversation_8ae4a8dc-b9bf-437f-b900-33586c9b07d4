package com.tyt.util.filter.wrapper;

import org.apache.commons.lang.StringUtils;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.HashMap;
import java.util.Map;

/**
 * xss包装器
 */
public class XssFilterWrapper extends HttpServletRequestWrapper {

    public XssFilterWrapper(HttpServletRequest request) {
        super(request);
    }


    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> requestParams = new HashMap<>(super.getParameterMap());
        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String[] newValues = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                newValues[i] = dealParamString(values[i]);
            }
            requestParams.put(name, newValues);
        }
        return requestParams;
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] values = super.getParameterValues(name);

        if (values == null) {
            return null;
        }

        String[] newValues = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            newValues[i] = dealParamString(values[i]);
        }

        return newValues;
    }

    @Override
    public String getParameter(String name) {
        String value = super.getParameter(name);
        return dealParamString(value);
    }

    /**
     * 转义方法
     *
     * @param val
     * @return
     */
    private String dealParamString(String val) {
        if (StringUtils.isNotBlank(val)) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < val.length(); i++) {
                char character = val.charAt(i);
                switch (character) {
                    case '"':
                        sb.append("&quot;");
                        break;
                    case '&':
                        sb.append("&amp;");
                        break;
                    case '\'':
                        sb.append("&#39");
                        break;
                    case '<':
                        sb.append("&lt;");
                        break;
                    case '>':
                        sb.append("&gt;");
                        break;
                    default:
                        sb.append(character);
                        break;
                }
            }
            return sb.toString();
        }
        return val;
    }

}
