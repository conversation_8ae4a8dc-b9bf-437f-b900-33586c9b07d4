package com.tyt.util.filter.wrapper;

import org.apache.commons.lang.StringUtils;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.HashMap;
import java.util.Map;

public class XssMultipartFilterWrapper extends HttpServletRequestWrapper {

    public XssMultipartFilterWrapper(HttpServletRequest request) {
        super(request);
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> requestParams = new HashMap<>(super.getParameterMap());
        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String[] newValues = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                if ("password".equals(name)) {
                    newValues[i] = values[i];
                } else {
                    newValues[i] = dealParamString(values[i]);
                }
            }
            requestParams.put(name, newValues);
        }
        return requestParams;
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] values = super.getParameterValues(name);

        if (values == null) {
            return null;
        }

        String[] newValues = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            if ("password".equals(name)) {
                newValues[i] = values[i];
            } else {
                newValues[i] = dealParamString(values[i]);
            }
        }
        return newValues;
    }

    @Override
    public String getParameter(String name) {
        if ("password".equals(name)) {
            return super.getParameter(name);
        } else {
            String value = super.getParameter(name);
            return dealParamString(value);
        }
    }

    private String dealParamString(String val) {
        if (StringUtils.isNotBlank(val)) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < val.length(); i++) {
                char character = val.charAt(i);
                switch (character) {
                    case '"':
                        sb.append("&quot;");
                        break;
                    case '&':
                        sb.append("&amp;");
                        break;
                    case '\'':
                        sb.append("&#39");
                        break;
                    case '<':
                        sb.append("&lt;");
                        break;
                    case '>':
                        sb.append("&gt;");
                        break;
                    default:
                        sb.append(character);
                        break;
                }
            }
            return sb.toString();
        }
        return val;
    }

}
