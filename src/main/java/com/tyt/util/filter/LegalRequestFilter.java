package com.tyt.util.filter;

import com.alibaba.fastjson.JSON;
import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.filter.service.TytReqUrlRuleService;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.enums.UrlFilterConfigEnum;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.*;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

/**
 * Servlet Filter implementation class LegalRequestFilter
 */
@Component("legalRequestFilter")
public class LegalRequestFilter implements Filter {
    private static final Logger logger = LoggerFactory
            .getLogger(LegalRequestFilter.class);

    private static final Logger filterLog = LoggerFactory
            .getLogger(FilterLog.class);

    private final Logger apiLog = LoggerFactory.getLogger("apiLog");

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "tytReqUrlRuleService")
    private TytReqUrlRuleService tytReqUrlRuleService;

    private Map<String, String> urlMap;

    /**
     * 处理action连接必要验证
     */
    private Set<String> urlSet;

    private Set<String> fileUrlSet;
    /**
     * 签名请求参数常量
     */
    private final static String SIGN = "sign";
    private final static String CLIENTVERSION = "clientVersion";
    private final static String CLIENTSIGN = "clientSign";

    private final static String OSVERSION = "osVersion";
    private final static String CLIENTID = "clientId";
    private final static String USERID = "userId";

    /**
     * Default constructor.
     */
    public LegalRequestFilter() {
        // TODO Auto-generated constructor stub
    }

    /**
     * @see Filter#destroy()
     */
    @Override
    public void destroy() {
        // TODO Auto-generated method stub
    }

    /**
     * @see Filter#doFilter(ServletRequest, ServletResponse, FilterChain)
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String method = httpRequest.getMethod();
        String contentType = request.getContentType();
        String servletPath = httpRequest.getServletPath();

        String clientSign = request.getParameter(CLIENTSIGN);
        String clientVersion = request.getParameter(CLIENTVERSION);
        String sign = request.getParameter(SIGN);
        if(StringUtils.isEmpty(sign)){
            sign = httpRequest.getHeader("sign");
        }
        String osVersion = request.getParameter(OSVERSION);
        String clientId = request.getParameter(CLIENTID);
        String userId = request.getParameter(USERID);
        String ttkn = request.getParameter("ttkn");
        String ticket = request.getParameter("ticket");

        if (StringUtils.isNotBlank(contentType) && contentType.indexOf("multipart/form-data") != -1) {
            TreeMap<String, String> paramMap = getFormDataParam(httpRequest);
            if(StringUtils.isEmpty(sign)){
                sign = Objects.toString(paramMap.get("sign"), null);
            }
            userId = Objects.toString(paramMap.get("userId"), null);
            ticket = Objects.toString(paramMap.get("ticket"), null);
            clientSign = Objects.toString(paramMap.get("clientSign"), null);
            clientVersion = Objects.toString(paramMap.get("clientVersion"), null);
            clientId = Objects.toString(paramMap.get("clientId"), null);
            osVersion = Objects.toString(paramMap.get("osVersion"), null);
            ttkn = Objects.toString(paramMap.get("ttkn"), null);
        }

        String requesParameters = getRequestParameters(request);
        // 记录访问日志
        filterLog(ttkn, servletPath, clientSign, osVersion, clientVersion, clientId,
                sign, userId, requesParameters, StringUtil.getRealIp(httpRequest));

        if (null != contentType && !"".equals(contentType)
                && contentType.indexOf("multipart/form-data") != -1&&fileUrlSet.contains(servletPath)) {
            logger.info("请求地址【{}】,请求类型为文件类型,参数【{}】", servletPath, requesParameters);
            chain.doFilter(request, response);
            return;
            // request.getParameterMap();
        } else {


//            if (StringUtils.isNotBlank(contentType) && contentType.indexOf("multipart/form-data") != -1) {
//                Map<String,Object> paramMap = getFormDataParam(httpRequest);
//                clientSign = paramMap.get(CLIENTSIGN).toString();
//                clientVersion = paramMap.get(CLIENTVERSION).toString();
//                sign = paramMap.get(SIGN).toString();
//
//                userId = paramMap.get(USERID).toString();
//                ticket = paramMap.get("ticket").toString();
//                //重新放回参数
//                RequestWrapper requestWrapper = new RequestWrapper(httpRequest);
//                requestWrapper.setCharacterEncoding("UTF-8");
//                requestWrapper.addAllParameters(paramMap);
//                httpRequest =requestWrapper;
//            }

            String suffix = getResourceUrl(servletPath);
            // 过滤资源文件;
            if (urlMap.containsKey(suffix)) {
                if (StringUtils.equals("action", suffix)) {
                    if (ticket == null || "".equals(ticket)
                            || userId == null || "".equals(userId)) {
                        logger.info("请求地址【{}】,参数【{}】", servletPath, requesParameters);
                        if(urlSet.contains(servletPath)) {
                            chain.doFilter(request, response);
                            return;
                        }
                        ResultMsgBean msgBean = new ResultMsgBean(
                                ReturnCodeConstant.BASIC_PARAMETER_ERROR, "基础参数缺失");
                        logger.info("action请求，基础参数缺失。url:{}", servletPath);
                        printJSON(httpRequest, httpResponse, msgBean);
                        return;
                    }
                }
                chain.doFilter(request, response);
                return;
            } else {
                if (servletPath.indexOf("proxool") != -1 || servletPath.contains("/s-url/")) {
                    chain.doFilter(request, response);
                    return;
                } else {

                    // 判断请求方式，只支持post请求
                    // if (!"post".equalsIgnoreCase(method)) {
                    // logger.info("请求地址【{}】,请求方式【{}】,只允许post请求！", servletPath,
                    // method);
                    // ResultMsgBean msgBean = new ResultMsgBean(
                    // ReturnCodeConstant.ERROR, "只允许post请求");
                    // printJSON(httpRequest, httpResponse, msgBean);
                    // return;
                    // }

                    //============= 2022年9月5日 新增aso积分墙，调整签名参数验证个数 start=============
                    String signPrivateKey = "tyt.private.key"; //tyt app私钥
                    boolean signSelectFlag = false;
                    if (servletPath.contains("/openapi/partner/")) { // 如果是openapi，并且外部合作商，采用合作商专属key进行验证
                        String timestamp = request.getParameter("timestamp"); // 新增时间戳，用于后续扩展
                        if (sign != null && !"".equals(sign) && timestamp != null && !"".equals(timestamp)) { // 仅验证签名
                            signSelectFlag = true;
                        }
                        signPrivateKey = "tyt.open.partner.private.key"; // 外部合作商密钥，即四方平台
                    } else if (servletPath.contains("/openapi/")) { // 如果是openapi，并且属于公司内部系统，采用内部三方专属key进行验证
                        if (clientSign != null && !"".equals(clientSign) && Constant.ClientSignEnum.isClientSignEnumcode(Integer.parseInt(clientSign))
                                && clientVersion != null && !"".equals(clientVersion)
                                && sign != null && !"".equals(sign)) { // 当前与tyt的app参数验证一致
                            signSelectFlag = true;
                        }
                        signPrivateKey = "tyt.open.private.key";  // 内部三方平台密钥
                    } else { // 最原始的tyt app应用参数验证，使用默认值
                        if (clientSign != null && !"".equals(clientSign) && Constant.ClientSignEnum.isClientSignEnumcode(Integer.parseInt(clientSign))
                                && clientVersion != null && !"".equals(clientVersion)
                                && sign != null && !"".equals(sign)) {
                            signSelectFlag = true;
                        }
                    }
//                    logger.info("当前使用的签名key:{}", signPrivateKey);
                    //============= 2022年9月5日 新增aso积分墙，调整签名参数验证个数 end=============

                    // 判读必填项不为空
                    if (signSelectFlag) {
                        String signLocal = Encoder.md5(requesParameters
                                + AppConfig.getProperty(signPrivateKey));

                        logger.debug("签名前字符串：【" + requesParameters + "】签名：【"
                                + signLocal + "】");

                        // 验证签名 防止伪造
                        boolean signEquals = sign.equals(signLocal);
                        if (signEquals) {
                                chain.doFilter(request, response);
                                response.setContentType("text/html;charset=UTF-8");
                                response.setCharacterEncoding("UTF-8");
                                return;
                        } else {
                            logger.info("请求地址【{}】,参数【{}】sign【{}】验证失败,signLocal【{}】", servletPath,
                                    requesParameters, sign,signLocal);
                            ResultMsgBean msgBean = new ResultMsgBean(
                                    ReturnCodeConstant.INVALID_SIGNATURE_CODE,
                                    ReturnCodeConstant.INVALID_SIGNATURE_MSG);
                            printJSON(httpRequest, httpResponse, msgBean);
                        }

                    } else {
                        logger.info("请求地址【{}】,请求参数【{}】,sign【{}】,必填项为空", servletPath,
                                requesParameters, sign);
                        ResultMsgBean msgBean = new ResultMsgBean(
                                ReturnCodeConstant.BASIC_PARAMETER_ERROR, "基础参数缺失");
                        printJSON(httpRequest, httpResponse, msgBean);
                    }
                }
            }
        }

    }

    //从接口中获取maxId
    private String getMaxIdBySearchAndVary(ServletRequest request) {
        String maxId = request.getParameter("querySign");
        if (StringUtils.isEmpty(maxId)) {
            maxId = request.getParameter("maxId");
        }
        return maxId;
    }

    //校验是否需要拦截次数
    private boolean validVisitCount(String url, String clientId, String maxId) {
        //TODO 是否再验证URL中
        if (StringUtils.isEmpty(url) || StringUtils.isEmpty(clientId) || StringUtils.isEmpty(maxId) || Integer.valueOf(maxId) <= 0) {
            logger.info("validVisitCount：url或者clientId为空,{},{}", url, clientId);
            return false;
        }
        if (!visitUrllist.contains(url)) {
//			logger.info("validVisitCount：当前URL不需要拦截次数,{},{}",url,sign);
            return false;
        }
        //TODO 是否大于次数
        String visitTime = RedisUtil.get("plat_url_visit_limit_" + url + clientId + maxId);

        logger.info("validVisitCount：当前URL访问次数,url:{},clientId:{},maxId:{},visitTime:{}", url, clientId, maxId, visitTime);
        if (!StringUtils.isEmpty(visitTime)) { //第一次
            logger.info("validVisitCount：当前URL超过瞬时最大访问次数,url:{},clientId:{},maxId:{},visitTime:{}", url, clientId, maxId, visitTime);
            return true;
        }
        visitMaxRegionTime = Integer.valueOf(getConfigValueByName("plat_app_url_visit_max_region_time", "1")); //获取时间范围 5s
        RedisUtil.set("plat_url_visit_limit_" + url + clientId + maxId, "1", visitMaxRegionTime);

        return false;
    }

    private static List<String> visitUrllist = null; //需要拦截的URL
    private static int visitMaxCount = 3;   //范围时间内最大访问次数 取緩存
    private static int visitMaxRegionTime = 5;  //范围时间 秒

    private String getConfigValueByName(String cacheName, String defaultValue) {
        String result = cacheService.getString(cacheName);
        if (StringUtils.isEmpty(result)) {
            result = tytConfigService.getStringValue(cacheName, defaultValue);
            cacheService.setString(cacheName, result, Constant.CACHE_EXPIRE_TIME_12H);
        }
        return result;
    }

    public void checkSign() {

    }


    /**
     * form-data请求方式参数获取
     * @param httpRequest
     */
    private TreeMap<String, String> getFormDataParamForSign(HttpServletRequest httpRequest) {
        DiskFileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload sfu = new ServletFileUpload(factory);
        try {
//			Map<String, String[]> parameterMap = httpRequest.getParameterMap();
//			logger.info("getFormDataParam httpRequest class:{} url:{}",parameterMap.getClass(),httpRequest.getServletPath());




            StringBuffer str = new StringBuffer();
            List<FileItem> list = sfu.parseRequest(httpRequest);
            TreeMap<String, String> paramMap = new TreeMap<>();
            for (FileItem fileItem : list) {
                if (!SIGN.equalsIgnoreCase(fileItem.getFieldName())) {
                    //判断是否是普通表单
                    if (fileItem.isFormField()) {
                        if (fileItem.getString("UTF-8") != null && !"".equals(fileItem.getString("UTF-8"))) {
                            String keyValue = fileItem.getFieldName() + "=" + fileItem.getString("UTF-8");
                            paramMap.put(keyValue, keyValue);
//                            paramMap.put(fileItem.getFieldName(), fileItem.getString("UTF-8"));
                        }
                        str.append(fileItem.getFieldName() + "=" + fileItem.getString("UTF-8") + "&");
                        //parameterMap.put(fileItem.getFieldName(), new String[]{fileItem.getString()});

                    }
                }
            }
            logger.info("LegalRequestFilter：getFormDataParam结果： {}",  str);
            return paramMap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new TreeMap<>();
    }

    /**
     * form-data请求方式参数获取
     * @param httpRequest
     */
    private TreeMap<String, String> getFormDataParam(HttpServletRequest httpRequest) {
        DiskFileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload sfu = new ServletFileUpload(factory);
        try {
//			Map<String, String[]> parameterMap = httpRequest.getParameterMap();
//			logger.info("getFormDataParam httpRequest class:{} url:{}",parameterMap.getClass(),httpRequest.getServletPath());
            StringBuffer str = new StringBuffer();
            List<FileItem> list = sfu.parseRequest(httpRequest);
            TreeMap<String, String> paramMap = new TreeMap<>();
            for (FileItem fileItem : list) {
                //判断是否是普通表单
                if (fileItem.isFormField()) {
                    paramMap.put(fileItem.getFieldName(), fileItem.getString("UTF-8"));
                    str.append(fileItem.getFieldName() + "=" + fileItem.getString("UTF-8") + "&");

                    //parameterMap.put(fileItem.getFieldName(), new String[]{fileItem.getString()});

                }
            }
            logger.info("LegalRequestFilter：getFormDataParam结果： {}",  str);
            return paramMap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new TreeMap<>();
    }

    /**
     * 枚举配置.
     */
    private void initWithEnum(){
        UrlFilterConfigEnum[] configArray = UrlFilterConfigEnum.values();
        for(UrlFilterConfigEnum oneUrlEnum : configArray){
            if(oneUrlEnum.isSkipLegal()){
                urlSet.add(oneUrlEnum.getUrl());
            }
        }
    }

    /**
     * @see Filter#init(FilterConfig)
     */
    @Override
    public void init(FilterConfig fConfig) throws ServletException {
        urlMap = new HashMap<String, String>();
        urlMap.put("jpg", "jpg");
        urlMap.put("jpeg", "jpeg");
        urlMap.put("png", "png");
        urlMap.put("gif", "gif");
        urlMap.put("js", "js");
        urlMap.put("css", "css");
        urlMap.put("jsp", "jsp");
        urlMap.put("bmp", "bmp");
        urlMap.put("mpg", "mpg");
        urlMap.put("html", "html");
        urlMap.put("htm", "htm");
        urlMap.put("xml", "xml");
        urlMap.put("rar", "rar");
        urlMap.put("zip", "zip");
        urlMap.put("gzip", "gzip");
        //h5专用
        urlMap.put("action", "action");

        //==== 跳过action url专用
        urlSet = new HashSet<>();
        urlSet.add("/wx/common/api/get/jsapiticket.action"); // 微信公众号唤起APP所使用的接口，无ticket
        urlSet.add("/plat/user/login.action"); // 便于前端调试使用，生产环境用户不使用此连接
        urlSet.add("/plat/activity/collate/getDetail.action"); // 拼团分享详情接口
        urlSet.add("/used/car/sale/getCarSource.action"); // 获取车辆类型
        urlSet.add("/used/car/sale/getDetail.action"); // 获取二手车详情
        urlSet.add("/plat/promo/getRenewalCouponStatus.action"); // 获取可以使用的代金券接口
        urlSet.add("/plat/promo/goodsCoupons.action"); // 获取可以使用的代金券接口
        urlSet.add("/plat/resource/global/getByParam.action"); // 获取页面所需要的公共资源
        urlSet.add("/plat/resource/global/getByParamList.action"); // 获取页面所需要的公共资源集合
        urlSet.add("/plat/transport/wechatShare/getGoodsDetail.action"); // h5页面获取货物详情
        urlSet.add("/convention/getActivityPrizeInfo.action"); // 获取履约活动排名信息
        urlSet.add("/insurance/pingan/callback.action"); // 平安回调
        urlSet.add("/plat/verificationCode/v6000/send.action"); // 发短信
        urlSet.add("/plat/resource/global/pageGet.action"); // 官网获取资源
        urlSet.add("/plat/resource/global/get.action"); // 官网获取资源
        urlSet.add("/plat/transport/search/matches/group/v6000.action"); // 获取标准化词
        urlSet.add("/convention/getOrdersListInfo.action"); // 履约活动运单详情
        urlSet.add("/convention/updateOrSaveActivityUserAddress.action"); // 履约活动-编辑保存该用户的收货地址
        urlSet.add("/convention/updateAwardInfo.action"); // 活动中奖后(JD购物卡)填写收货地址页面
        urlSet.add("/convention/updateTargetPrize.action"); // 更新选择的目标奖品信息
        urlSet.add("/convention/getActivityUserAddressByUserId.action"); // 根据userId获取该用户的收货地址
        urlSet.add("/convention/addActivityUserAddress.action"); // 保存该用户的收货地址
        urlSet.add("/convention/getOrderRanking.action"); // 获取履约活动排名信息
        urlSet.add("/convention/getActivityInfo.action"); // 获取活动信息
        urlSet.add("/convention/getRank.action"); // 获取活动信息
        urlSet.add("/internal/user/getbyid.action"); // 保险查看三天订单
        urlSet.add("/plat/infoFee/transport/saveGoodsStatusNewForPc.action"); // newPc撤销，设成交
        urlSet.add("/plat/carNew/identity/get.action"); // 所有车辆
        urlSet.add("/plat/resource/getResourceInterfaceUrl.action"); // 提供资源接口地址
        urlSet.add("/plat/user/getCredit.action"); // 查询信用
        urlSet.add("/plat/wordbook/list.action"); // 查询字典
        urlSet.add("/plat/user/syncgoods/getEmpowerStatus.action"); // 个人货主用户授权状态查询
        urlSet.add("/plat/user/syncgoods/saveEmpowerStatus.action"); // 个人货主用户授权
        urlSet.add("/plat/user/syncgoods/getEmpowerUser.action"); // 查询货源同步用户接口
        urlSet.add("/plat/user/syncgoods/empower.action"); // 弹窗权益授权接口
        urlSet.add("/stimulate/addStimulateCarLocation.action"); // 刷单申诉接口
        urlSet.add("/stimulate/updateStimulateOrderStatus.action"); // 刷单凭证上传
        urlSet.add("/plat/infofee/detail/giveBack.action"); // 订金不退还业务-退款接口
        urlSet.add("/plat/call/phone/getUserCreditInfo.action"); //  获取用户信用信息
        urlSet.add("/internal/user/updateinsurancecoupom.action"); // 修改赠险卡卷状态接口
        urlSet.add("/plat/guarantee/joinGuaranteeActivity.action"); // 参加保障活动
        urlSet.add("/plat/user/updateUserJoinStatus.action"); // 货方信用管理更新是否加入状态
        urlSet.add("/plat/user/getUserLabel.action"); // 获取用户标签
        urlSet.add("/plat/exposurePermission/getExposurePermissionGainRecordListByUserId.action"); // 查询曝光权益获取记录
        urlSet.add("/plat/exposurePermission/getExposurePermissionUsedRecordListByUserId.action"); // 查询曝光权益使用记录
        urlSet.add("/plat/exposurePermission/getExposurePermissionExpiredRecordListByUserId.action"); // 查询曝光权益过期记录
        urlSet.add("/plat/exposurePermission/getUserExposurePermissionCount.action"); // 查询用户拥有的可用货源曝光权益总次数
        urlSet.add("/plat/exposurePermission/getGainType.action"); // 曝光卡获取方式
        urlSet.add("/convention/carAtivityDetail.action"); // 查询车主透传活动页面所需详情信息
        urlSet.add("/convention/carAtivityOrderList.action"); // 查询车主透传活动 活动单列表数据
        urlSet.add("/stimulate/updateStimulate.action"); // 新刷单凭证上传
        urlSet.add("/convention/getAllOrdersListInfo.action"); // 新风险单里列表
        urlSet.add("/stimulate/getStimulateInfo.action"); // 车、货申诉详情
        urlSet.add("/stimulate/deleteInfo.action");//新申诉删除图片
        urlSet.add("/convention/getNewOrdersListInfo.action");//新履约订单列表
        urlSet.add("/plat/userCancel/getGoodsCancelCount.action"); //获取是第几次注销
        urlSet.add("/plat/user/wxMiniLoginLimit.action"); // 检查小程序是否限制登录的接口
        urlSet.add("/plat/user/checkLimitPublish.action"); // 检查小程序是否限制发货的接口
        urlSet.add("/plat/dwAgent/saveInfo.action"); // 板车服务商家入驻
        urlSet.add("/plat/carNew/getCheckCar.action"); // 订金支付页车辆选择阻断
        urlSet.add("/plat/carNew/getBlockCar.action"); // 订金支付页车辆必填提醒
        urlSet.add("/course/user/saveNewCourse.action"); // 私货课堂读取上报
        urlSet.add("/plat/signing/car/getCarDriverCityList.action"); // 查询车辆信息
        urlSet.add("/plat/signing/car/list.action"); // 查询专车列表
        urlSet.add("/plat/signing/car/getInfo.action"); // 查询专车详情
        urlSet.add("/plat/signing/car/save.action"); // 添加专车
        urlSet.add("/plat/infoFee/transport/makePriceByGoodCarPriceTransportCarryPrice.action");
        urlSet.add("/plat/transport/V5930/autoAssignOrderForSpecialCar.action");
        urlSet.add("/plat/freight/getSameTransportAveragePrice.action");
        urlSet.add("/plat/transportQuotedPrice/transportAgree.action");
        urlSet.add("/plat/infoFee/transport/getInvoiceRateByUserId.action");//调用user serice 根据userId和主体ID获取开票费率接口
        urlSet.add("/plat/infoFee/transport/saveDirectForAutoResend.action");// 货源自动重发接口
        urlSet.add("/plat/transportQuotedPrice/carQuotedPrice.action");// 货源自动重发接口

        urlSet.add("/plat/custom/addCustom.action"); // 根据二维码添加客户

        urlSet.add("/plat/call/phone/test.action"); //test
        urlSet.add("/plat/user/identity/auth/updateUserPermissionResult"); //test

        this.initWithEnum();

        visitUrllist = new ArrayList<>(2);
        visitUrllist.add("/plat/transport/vary");
        visitUrllist.add("/plat/transport/search");

        visitMaxCount = tytConfigService.getIntValue("pc_client_search_limit_count", 30);

        //文件上传跳过
        fileUrlSet =new HashSet<>();
        fileUrlSet.add("/plat/user/identity/auth/save");
        fileUrlSet.add("/plat/car/identity/updatesave");
        fileUrlSet.add("/plat/car/identity/save");
        fileUrlSet.add("/plat/transport/condition/file/write");
        fileUrlSet.add("/plat/user/identity/auth/save.action");
        fileUrlSet.add("/plat/user/identity/auth/saveEnterprise");
        fileUrlSet.add("/plat/user/identity/auth/saveNew");
        fileUrlSet.add("/plat/user/identity/auth/saveAuthPic");
        fileUrlSet.add("/plat/common/upload/img");
        fileUrlSet.add("/plat/user/head/save");
        fileUrlSet.add("/plat/user/head/save.action");
        fileUrlSet.add("/plat/user/identity/save");
        fileUrlSet.add("/plat/user/identity/photo/save");
        fileUrlSet.add("/plat/maintainer/update");
        fileUrlSet.add("/plat/maintainer/save");
        fileUrlSet.add("/plat/car/driver/save");
        fileUrlSet.add("/openapi/car/driver/save");
        fileUrlSet.add("/openapi/car/identity/save");
        //新PC客户端plat工程入参param和formdata同时存在,需排除的接口
        fileUrlSet.add("/plat/truckNavigationInfo/insertTruckNavigationInfo");
        fileUrlSet.add("/openapi/transport/search/matches/group/v6000");
        fileUrlSet.add("/plat/transport/search/matches/v6000");
        fileUrlSet.add("/plat/transport/V5930/publish");
        fileUrlSet.add("/plat/infoFee/transport/getMyPublish");
        fileUrlSet.add("/plat/infoFee/transport/getSingleDetail");
        fileUrlSet.add("/plat/call/phone/record/contacted/list");
        fileUrlSet.add("/plat/infoFee/transport/saveGoodsStatusNew");
        fileUrlSet.add("/plat/infoFee/transport/saveDirect");
        fileUrlSet.add("/plat/transport/V5930/rePublish");
        fileUrlSet.add("/plat/call/phone/record/recent/getByCellPhone");
        fileUrlSet.add("/plat/transport/done/list");
        fileUrlSet.add("/plat/transport/done/getCarPosition");
        fileUrlSet.add("/plat/user/getCellPhone");
        fileUrlSet.add("/plat/infofee/detail/isEdit");
        fileUrlSet.add("/plat/infoFee/transport/updatePublishType");
        fileUrlSet.add("/plat/infoFee/transport/addMoney");
        fileUrlSet.add("/plat/infoFee/transport/delMyGoods");
        fileUrlSet.add("/plat/verificationCode/send");
        fileUrlSet.add("/stimulate/updateStimulateOrderStatus.action"); // 刷单凭证上传
        fileUrlSet.add("/plat/user/login.action"); // 便于前端调试使用，生产环境用户不使用此连接
        fileUrlSet.add("/plat/merchant/save");
        fileUrlSet.add("/stimulate/updateStimulate.action"); // 新刷单凭证上传
        fileUrlSet.add("/userFrame/userVerify/faceVerifyV3"); //人脸识别认证

        logger.info("LegalRequestFilter 出始化成功！");
    }

    /**
     * 获取请求参数
     *
     * @param request
     * @return
     */
    private TreeMap<String, String> getParameters(ServletRequest request) {
        Map<String, String[]> srcParamMap = request.getParameterMap();
        TreeMap<String, String> treeMap = new TreeMap<String, String>();
        for (String key : srcParamMap.keySet()) {
            if (!SIGN.equalsIgnoreCase(key)) {
                Object obj = srcParamMap.get(key);
                String[] values = new String[1];
                if (obj instanceof String[]) {
                    values = (String[]) obj;
                } else {
                    values[0] = obj.toString();
                }
                if (obj != null && !"".equals(values[0]) && values.length > 0) {
                    String keyValue = key + "=" + values[0];
                    treeMap.put(keyValue, keyValue);
                } else {
                    // treeMap.put(key, null);
                }
            }
        }
        logger.info("LegalRequestFilter===>getParameters:{}",JSON.toJSONString(treeMap.values()));
        return treeMap;
    }

    /**
     * 获得排序后的请求参数不包含秘钥
     *
     * @param request
     * @return 获得排序后的请求参数不包含秘钥
     */
    private String getRequestParameters(ServletRequest request) {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String contentType = request.getContentType();
        TreeMap<String, String> treeMap = null;
        if(StringUtils.isNotBlank(contentType) && contentType.indexOf("multipart/form-data") != -1){
            treeMap = getFormDataParamForSign(httpRequest);
        }else{
            treeMap = getParameters(request);
        }

        if (null != treeMap && treeMap.size() > 0) {
            Iterator<String> iterator = treeMap.keySet().iterator();
            StringBuffer orgin = new StringBuffer();
            while (iterator.hasNext()) {
                String name = iterator.next();
                orgin.append(name).append("&");
            }
            orgin.deleteCharAt(orgin.length() - 1);
            return orgin.toString();
        }
        return null;
    }

    /***
     * 取得 资源请求的后缀
     *
     * @param url
     * @return
     */
    public String getResourceUrl(String url) {
        if (null != url && !"".equals(url)) {
            if (url.indexOf(".") != -1) {
                return url.substring(url.indexOf(".") + 1).toLowerCase().trim();
            } else
                return url;
        } else
            return "";
    }

    protected void printJSON(HttpServletRequest servletRequest,
                             HttpServletResponse servletResponse, Object obj) {
        PrintWriter writer = null;
        try {
            // servletResponse.setHeader("Content-Encoding","gzip");
            // servletResponse.setHeader("Content-Type","application/gzip");
            // servletResponse.setContentType("application/gzip");
            servletResponse.setContentType("text/html;charset=UTF-8");
            servletResponse.setCharacterEncoding("UTF-8");

            writer = servletResponse.getWriter();
            String jsoncallback = servletRequest.getParameter("jsoncallback");
            if (null == jsoncallback || jsoncallback.isEmpty()) {
                writer.print(JSON.toJSONString(obj));
            } else {
                writer.print(jsoncallback + "(" + JSON.toJSONString(obj) + ")");
            }
        } catch (Exception e) {
            logger.error("输出信息失败！", e);
        } finally {
            if (null != writer) {
                writer.close();
            }
        }
    }

    /**
     * 记录filter日志，用于分析
     *
     * @param servletPath
     * @param clientSign
     * @param osVersion
     * @param clientVersion
     * @param clientId
     * @param sign
     * @param userId
     * @param requesParameters
     */
    private void filterLog(String ttkn, String servletPath, String clientSign,
                           String osVersion, String clientVersion, String clientId,
                           String sign, String userId, String requesParameters, String ip) {
        filterLog.info("|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}", TimeUtil.formatDate_(new Date()), ip, userId, "", clientSign, servletPath, clientId, clientVersion, requesParameters, "&sign=" + sign, ttkn, osVersion);
        //记录访问监控日志
        apiLog.info("|{}|{}|{}|{}|{}|{}|{}", TimeUtil.formatDate_(new Date()), ip, userId, clientSign, servletPath, clientId, clientVersion);
    }
}
