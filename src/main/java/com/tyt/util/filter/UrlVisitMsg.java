package com.tyt.util.filter;

import java.util.concurrent.atomic.AtomicInteger;
import com.alibaba.fastjson.JSON;

/**
 * 封装url地址和首次访问时间的对象
 * 
 * <AUTHOR>
 * @date 2018年3月16日下午3:23:53
 * @description
 */
public class UrlVisitMsg {

	private Long firstMilliSec;
	private AtomicInteger visitTime;

	public UrlVisitMsg() {
		setFirstMilliSec(System.currentTimeMillis());
		setVisitTime(new AtomicInteger(0));
	}

	public Long getFirstMilliSec() {
		return firstMilliSec;
	}

	public void setFirstMilliSec(Long firstMilliSec) {
		this.firstMilliSec = firstMilliSec;
	}

	public AtomicInteger getVisitTime() {
		return visitTime;
	}

	public void setVisitTime(AtomicInteger visitTime) {
		this.visitTime = visitTime;
	}

	public void reset() {
		int curNum = this.getVisitTime().get();
		if (curNum == 1) {
			this.getVisitTime().addAndGet(1);
		} else {
			this.setFirstMilliSec(System.currentTimeMillis());
			this.getVisitTime().set(1);
		}
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
