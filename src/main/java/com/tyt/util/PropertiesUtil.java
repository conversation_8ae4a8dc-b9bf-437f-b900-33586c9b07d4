package com.tyt.util;

import java.util.HashMap;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.ResourceBundle;
import java.util.Set;

public class PropertiesUtil {

	
	//private  String BUNDLE_NAME = "security_intercept_url";

	public static Map<String,Long> propertiesMap=new HashMap<String,Long>();

	//刷新间隔 5分钟
	public static final long refreshInterval=300000;
	
	private  ResourceBundle RESOURCE_BUNDLE;

	private PropertiesUtil() {
	}
	private PropertiesUtil(ResourceBundle resourceBundle){
		this.RESOURCE_BUNDLE=resourceBundle;
	}
	
	public static PropertiesUtil init(String propertiesFileName)
	{
		ResourceBundle resourceBundle= ResourceBundle.getBundle(propertiesFileName);
		return new PropertiesUtil(resourceBundle);
	}

	public  String getString(String key) {
		String value = null;
		Long oldTime=System.currentTimeMillis();
		if(propertiesMap.containsKey(key)){
			oldTime=propertiesMap.get(key);
		}
		if((System.currentTimeMillis()-oldTime.longValue())<refreshInterval){
			value = System.getProperty(key);
		}
		if(value!=null&&!"".equals(value.trim()))
		{
			return value;
		}
		try {
			value = RESOURCE_BUNDLE.getString(key);
			
		} catch (MissingResourceException e) {
			// logger.warn("get property failed:" + key);
		}
		if (value!=null&&!"".equals(value.trim())) {
			System.setProperty(key,value);
			propertiesMap.put(key, System.currentTimeMillis());
		}
		return value == null ? "" : value.trim();
	}
	
	public  Set<String> getKeySet()
	{
		Set<String>  set=RESOURCE_BUNDLE.keySet();
		return set;
	}
	
	public static void main(String[] args) {
		PropertiesUtil p=PropertiesUtil.init("security_intercept_url");
//		String c=p.getString("cc");
//		System.out.println(c);
//		c=p.getString("cc");
//		System.out.println(c);
		Set<String> set=p.getKeySet();
		for(String s:set)
		{
			System.out.println(s);
		}
	}
}