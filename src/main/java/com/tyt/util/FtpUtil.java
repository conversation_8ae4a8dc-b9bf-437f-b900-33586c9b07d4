package com.tyt.util;
import java.io.DataOutputStream;
import java.io.InputStream;
import sun.net.TelnetOutputStream;
import sun.net.ftp.FtpClient;
public class FtpUtil {
	// 上传文件至FTP通用方法
	public static void upLoadFileFtp(KmConfig kmConfig,InputStream is, String fileName){
		try {
			String ftpHost = kmConfig.getFtpHost();
			String userName = kmConfig.getFtpUser();
			String passWord = kmConfig.getFtpPassword();
			FtpClient ftpClient = FtpClient.create(ftpHost);// ftpHost为FTP服务器的IP地址，port为FTP服务器的登陆端口,ftpHost为String型,port为int型。
			ftpClient.login(userName,passWord.toCharArray()); // userName、passWord分别为FTP服务器的登陆用户名和密码
			ftpClient.setBinaryType();
//			ftpClient.changeDirectory(path);
//			ftpClient.cd(path);// path为FTP服务器上保存上传文件的路径。

			TelnetOutputStream telnetOut=(TelnetOutputStream) ftpClient.putFileStream(fileName,true);// fileName为上传的文件名
			DataOutputStream dataOut = new DataOutputStream(telnetOut);
			byte buffer[] = new byte[1024 * 1024];
			int count = 0;
			while ((count = is.read(buffer)) != -1) {
				dataOut.write(buffer, 0, count);
			}
			telnetOut.close();
			dataOut.close();
			ftpClient.close();
		} catch (Exception e) {
			System.out.println("上传文件失败！请检查系统FTP设置,并确认FTP服务启动");
		}
	}

	// 删除文件至FTP通用方法
	public static void deleteFileFtp(KmConfig kmConfig,String fileName){
		try {
			String ftpHost = kmConfig.getFtpHost();
			String userName = kmConfig.getFtpUser();
			String passWord = kmConfig.getFtpPassword();

			FtpClient ftpClient = FtpClient.create(ftpHost);// ftpHost为FTP服务器的IP地址，port为FTP服务器的登陆端口,ftpHost为String型,port为int型。
			ftpClient.login(userName, null, passWord);   // userName、passWord分别为FTP服务器的登陆用户名和密码
			ftpClient.setBinaryType();
//			ftpClient.cd(path);// path为FTP服务器上保存上传文件的路径
			try {
//				ftpClient.sendServer("dele " + fileName + "\r\n");
				ftpClient.deleteFile(fileName);
			} catch (Exception e) {
				System.out.println("删除文件失败！请检查系统FTP设置,并确认FTP服务启动");
			}
			ftpClient.close();
		} catch (Exception e) {
			System.out.println("删除文件失败！");
		}
	}
}