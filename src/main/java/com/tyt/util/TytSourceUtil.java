package com.tyt.util;

import com.alibaba.fastjson.JSON;
import com.tyt.model.TytSingleUserCallLimit;
import com.tyt.model.TytSource;
import com.tyt.model.TytUrlLimit;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.controller.TransportRecommendController;
import com.tyt.transport.service.impl.TransportServiceImpl;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.filter.RequestLimitFilter;
import com.tyt.util.filter.UrlVisitMsg;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class TytSourceUtil implements InitializingBean {
	public static Map<String, HashMap<String, TytSource>> sourceMap = null;// new
																			// HashMap<String,
																			// Map<String,TytSource>>();
	public static Map<String, List<TytSource>> sourceList = null;// new
																	// HashMap<String,List<TytSource>>();
	public static Map<Long, TytSource> allList = null;
	public static boolean threadStart = false;
	public static boolean initStatus = false;

	public static final String CACHE_TYT_SOURCE_MAP_FLAG_KEY = "tyt_redis_cache_source_flag";

	private static volatile String CURRENT_CACHE_TYT_SOURCE_MAP_FLAG_KEY = "NO_FLAG";

	// 刷新间隔 5分钟
	/*
	 * public static final long refreshInterval=300000; public static long
	 * refreshTime=0l;
	 */

	/**
	 * 通过ID 获得一个资源
	 * 
	 * @param id
	 * @return
	 */
	public static TytSource getTytSource(Long id) {
		if (allList == null || allList.size() < 1) {
			init();
		}

		if (allList != null) {

			TytSource tytSource = allList.get(id);
			return tytSource;
		}
		return null;
	}

	/**
	 * 通过ID 获得一个资源子集
	 * 
	 * @param id
	 * @return
	 */
	public static List<TytSource> getTytSourceSubSet(Long id) {
		if (allList == null || allList.size() < 1) {
			init();
		}

		if (allList != null) {
			TytSource tytSource = allList.get(id);
			if (tytSource != null) {
				return tytSource.getSubset();
			}
		}
		return null;
	}

	/**
	 * 获得一个资源子集
	 * 
	 * @param groupCode
	 *            分组code
	 * @param value
	 *            属性值
	 * @return
	 */
	public static List<TytSource> getTytSourceSubSet(String groupCode, String value) {
		if (sourceMap == null || sourceMap.size() < 1) {
			init();
		}
		if (sourceMap != null) {

			Map<String, TytSource> map = sourceMap.get(groupCode);
			if (map != null && map.size() > 0) {
				TytSource tytSource = map.get(value);
				if (tytSource != null) {
					return tytSource.getSubset();
				}
			}
		}

		return null;
	}

	/**
	 * 根据父节点ID 和本节点的value 取本节点对像
	 * 
	 * @param parentId
	 * @param value
	 * @return
	 */
	public static TytSource getSourceNameByParentId(Long parentId, String value) {
		List<TytSource> list = getTytSourceSubSet(parentId);
		if (list != null && list.size() > 0) {
			TytSource tytSource = list.get(0);
			return getSourceName(tytSource.getGroupCode(), value);
		}
		return null;
	}

	/**
	 * 根据 获得父类下某个子类
	 * 
	 * @param groupCode
	 *            分组code
	 * @param value
	 *            父类的值
	 * @param subValue
	 *            需要查找的子类值
	 * @return TytSource
	 */
	public static TytSource getSourceName(String groupCode, String value, String subValue) {
		TytSource tytSource = getSourceName(groupCode, value);
		if (tytSource != null) {
			List<TytSource> tytSourceList = tytSource.getSubset();
			if (tytSourceList != null) {
				TytSource subTytSource = tytSourceList.get(0);
				return getSourceName(subTytSource.getGroupCode(), subValue);
			}
		}
		return null;
	}

	/**
	 * 获得资源属性TytSource
	 * 
	 * @param groupCode
	 *            分组code
	 * @param value
	 *            属性值
	 * @return TytSource
	 */
	public static TytSource getSourceName(String groupCode, String value) {
		if (sourceMap == null || sourceMap.size() < 1) {
			init();
		}
		if (sourceMap != null) {
			Map<String, TytSource> map = sourceMap.get(groupCode);
			if (map != null && map.size() > 0) {
				return map.get(value);
			}
		}

		return null;
	}

	/**
	 * 获得某一资源列表
	 * 
	 * @param groupCode
	 *            分组code
	 * @return
	 */
	public static List<TytSource> getSourceList(String groupCode) {
		if (sourceList == null || sourceList.size() < 1) {
			init();
		}
		return sourceList == null ? null : sourceList.get(groupCode) == null ? null : sourceList.get(groupCode);
	}

	public synchronized static void init() {
		if (!initStatus) {
			initSource();
			initStatus = true;
		}
	}

	private static final Logger logger = LoggerFactory.getLogger(TytSourceUtil.class);

	public static void initSource() {
		System.out.println(System.currentTimeMillis() + "TytSource begin......");
		long startTime = System.currentTimeMillis();
		TytConfigService tytConfigService = ApplicationContextUtils.getBean("tytConfigService");
		Object o = RedisUtil.getObject(tytConfigService.getStringValue("tyt_redis_cache_source_list", "tyt_redis_cache_source_list"));
		if (o != null) {
			sourceList = (Map<String, List<TytSource>>) o;
		}
		o = RedisUtil.getObject(tytConfigService.getStringValue("tyt_redis_cache_source_map", "tyt_redis_cache_source_map"));
		if (o != null) {
			sourceMap = (Map<String, HashMap<String, TytSource>>) o;
		}
		o = RedisUtil.getObject(tytConfigService.getStringValue("tyt_redis_cache_source_all", "tyt_redis_cache_source_all"));
		if (o != null) {
			allList = (Map<Long, TytSource>) o;
		}
		// TODO 定时更新有好货出发地找货范围
		Map<String, String> disctaceInitMap = RedisUtil.getMap("cache.distance.init.redis");
		if (disctaceInitMap != null) {
			TransportRecommendController.distanceInitMap = disctaceInitMap;
		}
		//--初始化url限制信息开始--//
		RequestLimitFilter.cacheKey = tytConfigService.getStringValue(Constant.REQUEST_URL_LIMIT_PLAT_CACHE_KEY, "request.url.limit.plat.cache");
		RequestLimitFilter.urlLimitCache = RedisUtil.get(RequestLimitFilter.cacheKey);
		List<TytUrlLimit> urlLimitList = getUrlLimit();
		RequestLimitFilter.urlLimitMap = handleUrlLimit(urlLimitList);
		RequestLimitFilter.urlAndVisitTime.clear();
		// 根据限制url初始化限制信息多例对象
		initMulDitance(urlLimitList);
		//--初始化url限制信息结束--//
		logger.info("tytsourceutil cacheKey: " + RequestLimitFilter.cacheKey + ", urlLimitCache: " + RequestLimitFilter.urlLimitCache);
		//--初始化用户拨打电话次数信息开始--//
		TransportServiceImpl.userCallLimitCache = RedisUtil.get(Constant.SINGLE_USER_CALL_URL_LIMIT_CACHE_KEY);
		logger.info("tytsourceutil userCallLimitCache: " + TransportServiceImpl.userCallLimitCache);
		List<TytSingleUserCallLimit> singleUserCallLimitList = urlLimitListgetUserCallLimit();
		initUserCallLimit(singleUserCallLimitList);
		//--初始化用户拨打电话次数信息结束--//
		long endTime = System.currentTimeMillis();
		System.out.println(" TytSource init time: " + (endTime - startTime));
	}

	private static void initUserCallLimit(List<TytSingleUserCallLimit> singleUserCallLimitList) {
		TransportServiceImpl.userAndCallTimeTemp.clear();
		if (singleUserCallLimitList != null && singleUserCallLimitList.size() > 0) {
			TytSingleUserCallLimit singleUserCallLimit;
			for (int i = 0; i < singleUserCallLimitList.size(); i++) {
				singleUserCallLimit = singleUserCallLimitList.get(i);
				TransportServiceImpl.userAndCallTimeTemp.put(String.valueOf(singleUserCallLimit.getUserId()), singleUserCallLimit.getCallTime());
			}
		}
		TransportServiceImpl.userAndCallTime = TransportServiceImpl.userAndCallTimeTemp;
		logger.info("tytsourceutil TransportServiceImpl.userAndCallTime: " + TransportServiceImpl.userAndCallTime);
		
	}

	private static List<TytSingleUserCallLimit> urlLimitListgetUserCallLimit() {
		if (StringUtils.isNotEmpty(TransportServiceImpl.userCallLimitCache)) {
			return JSON.parseArray(TransportServiceImpl.userCallLimitCache, TytSingleUserCallLimit.class);
		} else {
			return new ArrayList<TytSingleUserCallLimit>();
		}
	}

	private static void initMulDitance(List<TytUrlLimit> urlLimitList) {
		RequestLimitFilter.urlInstance.clear();
		if (urlLimitList != null && urlLimitList.size() > 0) {
			TytUrlLimit urlLimit;
			for (int i = 0; i < urlLimitList.size(); i++) {
				urlLimit = urlLimitList.get(i);
				RequestLimitFilter.urlInstance.put(urlLimit.getUrl(), new UrlVisitMsg());
			}
		}
		RequestLimitFilter.urlAndVisitTime = RequestLimitFilter.urlInstance;
		logger.info("tytsourceutil RequestLimitFilter.urlAndVisitTime: " + RequestLimitFilter.urlAndVisitTime);
	}

	private static List<TytUrlLimit> getUrlLimit() {
		if (StringUtils.isNotEmpty(RequestLimitFilter.urlLimitCache)) {
			return JSON.parseArray(RequestLimitFilter.urlLimitCache, TytUrlLimit.class);
		} else {
			return new ArrayList<TytUrlLimit>();
		}
	}

	private static Map<String, String> handleUrlLimit(List<TytUrlLimit> urlLimitList) {
		RequestLimitFilter.urlLimitMap.clear();
		if (urlLimitList != null && urlLimitList.size() > 0) {
			TytUrlLimit curURLLimit = null;
			for (int i = 0; i < urlLimitList.size(); i++) {
				curURLLimit = urlLimitList.get(i);
				RequestLimitFilter.urlLimitMap.put(curURLLimit.getUrl(), curURLLimit.getLimtiTime() + "#" + curURLLimit.getLimtiFrequency());
			}
		}
		return RequestLimitFilter.urlLimitMap;
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		if (!threadStart) {
			Thread tytSourceInitThread = new Thread("tytSourceInitThread") {
				@Override
				public void run() {
					System.out.println(System.currentTimeMillis() + "__" + this.getName() + " start......");

					while (true) {
						try {

							String redisFlagKey = Optional.ofNullable(RedisUtil.get(CACHE_TYT_SOURCE_MAP_FLAG_KEY)).orElse("");
							if(CURRENT_CACHE_TYT_SOURCE_MAP_FLAG_KEY.equals(redisFlagKey)){
								Thread.sleep(TimeUnit.SECONDS.toMillis(10));
								continue;
							}
							logger.info("source 缓存更新 当前 flag :【{}】 , 更新后 flag :【{}】", CURRENT_CACHE_TYT_SOURCE_MAP_FLAG_KEY, redisFlagKey);
							TytSourceUtil.initSource();
							CURRENT_CACHE_TYT_SOURCE_MAP_FLAG_KEY = redisFlagKey;
							Thread.sleep(TimeUnit.SECONDS.toMillis(10));
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
			};
			tytSourceInitThread.start();
			threadStart = true;
		}
	}
}
