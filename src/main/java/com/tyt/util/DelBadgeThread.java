package com.tyt.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 删除角标
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class DelBadgeThread implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger("DelBadgeThread");

    String cid;
    
    public DelBadgeThread(String cid) {
    	this.cid=cid;
    }

    public void run() {
        try {
        	if(cid!=null){
        		PushUtil.clearBadgeForIos(cid);
        	}
        } catch (Exception e) {
            logger.error("删除角标异常,错误信息", e);
        }
    }
   
}
