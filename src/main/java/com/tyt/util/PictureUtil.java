package com.tyt.util;


import com.tyt.user.service.TytConfigService;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/03/20 11:38
 */
public class PictureUtil {

    private static final String PREFIX_PICTURE = "prefix_picture";
    private static final String HTTP = "http";

    private static final String DEFAULT_URL = "http://newtest.teyuntong.net/rootdata";

    public static String getPictureUrl(String pictureUrl) {
        TytConfigService tytConfigService = ApplicationContextUtils.getBean("tytConfigService");
        if (StringUtils.isNotBlank(pictureUrl) && !pictureUrl.startsWith(HTTP)) {
            String prefixPicture = tytConfigService.getStringValue(PREFIX_PICTURE,DEFAULT_URL);
            return prefixPicture + pictureUrl;
        }
        return pictureUrl;
    }
}
