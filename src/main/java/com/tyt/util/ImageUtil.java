package com.tyt.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.multipart.MultipartFile;

import com.tyt.config.util.AppConfig;

public class ImageUtil {

	/**
	 * 上传图片
	 * @param fileName
	 * @return
	 */
//	public static String uploadImage(String name,MultipartFile file,HttpServletRequest request) {
//		String fileName = null;//返回结果
//        try {
//            File targetFile = null;
//            if(file != null){
//            String path = request.getSession().getServletContext().getRealPath(name);
//            fileName = renameFile(file.getOriginalFilename());
//            targetFile = new File(path, fileName);
//            if(!targetFile.exists()){
//                      targetFile.mkdirs();
//                      //保存到服务端上
//                      file.transferTo(targetFile);
//                  }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return fileName;
//    }

	public static String uploadImage(String name,MultipartFile file,HttpServletRequest request) {
		String fileName = null;//返回结果
		 KmConfig km = new KmConfig();
			//上传到ftp
        try {
            if(file != null){
            fileName = name+renameFile(file.getOriginalFilename());
            FtpUtil.upLoadFileFtp(km,file.getInputStream() , fileName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return fileName;
    }

	/**
	 * 重命名上传的图片
	 * @param fileName
	 * @return
	 */
	public static String renameFile(String fileName){
        DateFormat format = new SimpleDateFormat("yyMMddHHmmss");
        String formatDate = format.format(new Date());
        int random        = new Random().nextInt(10000);
        int position      = fileName.lastIndexOf(".");
        String extension  = ".jpg";
        if(position>-1){
        	extension  = fileName.substring(position);
        }
        return formatDate + random + extension;
    }


	public static String renameFile(){
		DateFormat format = new SimpleDateFormat("yyMMddHHmmss");
		String formatDate = format.format(new Date());
		int random = new Random().nextInt(10000);
		String extension  = ".jpg";
		return formatDate + random + extension;
	}


//	public static void deleteImage(String pack,String fileName,HttpServletRequest request){
//		String path = request.getSession().getServletContext().getRealPath(pack);
//        File file = new File(path, fileName);
//        if(file.exists())file.delete();
//	}

	public static void deleteImage(String fileName,HttpServletRequest request){
		KmConfig km = new KmConfig();
		FtpUtil.deleteFileFtp(km, fileName);
	}

	/**
	 * 重命名图片，该图片与项目在同一个服务器
	 * @param pic待上传的图片
	 * @param typeName文件保存分目录名称
	 * @return
	 */
	public static String renamePic(MultipartFile pic,String typeName) {
//		String fileSeparator=System.getProperty("file.separator");//获取系统文件分隔符
		String domainurl="/data/pictures/"+typeName+"/";//获取文件路径
		CreateFileUtil.createDir(AppConfig.getProperty("picture.path.domain")+domainurl);
		return domainurl+ImageUtil.renameFile(pic.getOriginalFilename());
	}


}
