package com.tyt.util;

import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tyt.model.TytConfig;
import com.tyt.service.common.baidu.api.MobilePhoneAPI;
import com.tyt.service.common.baidu.phone.PhoneLocale;

public class MobileUtil {

	
	public static String[] getMobileAddressArr(String mobile){
		String[] resultArray=new String[2];//省市 联通/移动/电信等
		try {
			Pattern pattern = Pattern.compile("1\\d{10}");
			Matcher matcher = pattern.matcher(mobile);
			if(matcher.matches()){
				PhoneLocale locale = MobilePhoneAPI.getMobileLocale(mobile);
				if(locale != null){
					String province = locale.getProvince();
					String city = locale.getCity();
					resultArray[0]=province;
					if(city!=null&&!("").equals(city.trim())&&!city.endsWith("市"))city=city+"市";//为了保持一致，没有市加市
					resultArray[1]=city;
				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			//return "无此号记录";
		}
		return resultArray;
	}

	public static String getMobileAddress(String mobile){
		String[] resultArray=new String[2];//省市 联通/移动/电信等
		try {
			Pattern pattern = Pattern.compile("1\\d{10}");
			Matcher matcher = pattern.matcher(mobile);
			if(matcher.matches()){
				PhoneLocale locale = MobilePhoneAPI.getMobileLocale(mobile);
				if(locale != null){
					String province = locale.getProvince();
					String city = locale.getCity();
					resultArray[0]=province;
					if(city!=null&&!("").equals(city.trim())&&!city.endsWith("市"))city=city+"市";//为了保持一致，没有市加市
					resultArray[1]=city;
				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			//return "无此号记录";
		}
		return resultArray[0] + resultArray[1];
	}
	
	/**
	 * 判断是否在区域内
	 * @param phone
	 * @return false 
	 */
	public static  boolean isQuyu(String phone){
			//TytConfig systemIdeatityAreaTytConfig= tytConfigService.getValue("systemIdeatityArea");
			//String systemIdeatityArea=systemIdeatityAreaTytConfig.getValue();
			String systemIdeatityArea="北京,贵州,天津,郑州";
			String place= MobileUtil.getMobileAddress(phone);
			String [] systemIdeatityAreas=systemIdeatityArea==null?null:systemIdeatityArea.split(",");
			//判断是否在排除范围内
			if(systemIdeatityAreas!=null&& null!=place&&systemIdeatityAreas.length>0){
				for(String area:systemIdeatityAreas){
					if(place.indexOf(area)!=-1){
						return true;
					}
				}			
			}
			return false;
	}

	/**
     * 验证手机号码
	 * 校验规则：以1开头的11位数字
     * @return 验证成功返回true，验证失败返回false  
     */    
    public static boolean isMobile(String mobile){
		if(mobile == null){
			return false;
		}

        String regex = "^1[0-9]{10}$";
        return Pattern.matches(regex, mobile);
    }

    /** 
     * 区号+座机号码
     * @param fixedPhone 
     * @return 
     */  
    public static boolean isFixedPhone(String fixedPhone){  
        String reg="(?:(0[0-9]{2,3}?)?([2-9][0-9]{6,7})?)"; 
        return Pattern.matches(reg, fixedPhone.trim());  
    } 
	
	
	public static void main(String[] args) {
//		System.out.println("--"+MobileUtil.getMobileAddress("18856965215"));

		String phone = "12333333333";

		boolean b = isMobile(phone);

		System.out.println(b);
		System.out.println(isFixedPhone("0122457345"));
//		System.out.println("".indexOf("1"));
//		System.out.println(isQuyu("18810992178"));
	}
	
}

