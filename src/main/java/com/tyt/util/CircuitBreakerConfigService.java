package com.tyt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tyt.user.service.TytConfigService;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.timelimiter.TimeLimiterConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:15
 */
@Component
@Slf4j
public class CircuitBreakerConfigService implements InitializingBean {

    private final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);

    @Autowired
    private CircuitBreakerService circuitBreakerService;
    @Autowired
    private TytConfigService configService;

    private final AtomicReference<String> lastPlatCircuitBreakerConfig = new AtomicReference<>();
    private final AtomicReference<String> lastPlatTimeoutConfig = new AtomicReference<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        // 10s刷新一次配置，并且清除一次本地缓存，使新配置生效
        executorService.scheduleAtFixedRate(() -> {
            try {
                handleCircuitBreakerConfig();
            } catch (Exception e) {
                log.error("熔断配置同步失败 ", e);
            }
        }, 0, 10, TimeUnit.SECONDS);

        executorService.scheduleAtFixedRate(() -> {
            try {
                handleTimeoutConfig();
            } catch (Exception e) {
                log.error("超时配置同步失败 ", e);
            }
        }, 0, 10, TimeUnit.SECONDS);
    }

    /**
     * 超时配置
     */
    private void handleTimeoutConfig() {
        String platTimeoutConfig = configService.getStringValue("platTimeoutConfig");
        if (StringUtils.isBlank(platTimeoutConfig)
                || Objects.equals(lastPlatTimeoutConfig.getAndSet(platTimeoutConfig), platTimeoutConfig)) {
            // 不相等才修改
            return;
        }

        Map<String, TimeLimiterProperties> timeLimiterConfigs = JSON.parseObject(platTimeoutConfig,
                new TypeReference<Map<String, TimeLimiterProperties>>() {
                });
        timeLimiterConfigs.forEach((k, v) -> {
            TimeLimiterConfig.Builder custom =
                    TimeLimiterConfig.from(circuitBreakerService.getDefaultTimeLimiterConfig());
            if (v.getCancelRunningFuture() != null) {
                custom.cancelRunningFuture(v.getCancelRunningFuture());
            }
            if (v.getTimeoutDurationInMs() != null) {
                custom.timeoutDuration(Duration.ofMillis(v.getTimeoutDurationInMs()));
            }
            circuitBreakerService.registerCircuitBreakerConfig(k, null, custom.build());
            // 删除已经存在的 timelimiter
            circuitBreakerService.removeAllTimeLimiters();
        });
    }

    /**
     * 熔断配置
     */
    private void handleCircuitBreakerConfig() {
        String platCircuitBreakerConfig = configService.getStringValue("platCircuitBreakerConfig");
        if (StringUtils.isBlank(platCircuitBreakerConfig)
                || Objects.equals(lastPlatCircuitBreakerConfig.getAndSet(platCircuitBreakerConfig),
                platCircuitBreakerConfig)) {
            // 不相等才修改
            return;
        }

        Map<String, CircuitBreakerProperties> circuitBreakerConfigs =
                JSON.parseObject(platCircuitBreakerConfig,
                        new TypeReference<Map<String, CircuitBreakerProperties>>() {
                        });
        circuitBreakerConfigs.forEach((k, v) -> {
            CircuitBreakerConfig.Builder custom =
                    CircuitBreakerConfig.from(circuitBreakerService.getDefaultCircuitBreakerConfig());
            if (v.getFailureRateThreshold() != null) {
                custom.failureRateThreshold(v.getFailureRateThreshold());
            }
            if (v.getSlidingWindowSize() != null) {
                custom.slidingWindowSize(v.getSlidingWindowSize());
            }
            if (v.getSlidingWindowType() != null) {
                custom.slidingWindowType(v.getSlidingWindowType());
            }
            if (v.getWaitDurationInOpenStateInMs() != null) {
                custom.waitDurationInOpenState(Duration.ofMillis(v.getWaitDurationInOpenStateInMs()));
            }
            if (v.getPermittedNumberOfCallsInHalfOpenState() != null) {
                custom.permittedNumberOfCallsInHalfOpenState(v.getPermittedNumberOfCallsInHalfOpenState());
            }
            if (v.getMinimumNumberOfCalls() != null) {
                custom.minimumNumberOfCalls(v.getMinimumNumberOfCalls());
            }
            circuitBreakerService.registerCircuitBreakerConfig(k, custom.build(), null);
            // 删除已经存在的 CircuitBreakers
            circuitBreakerService.removeAllCircuitBreakers();
        });
    }

    /**
     * 远程熔断配置
     */
    @Data
    static class CircuitBreakerProperties {
        /**
         * 断路器 half open 状态接受的最大请求数
         */
        private Integer permittedNumberOfCallsInHalfOpenState;
        /**
         * 记录断路器关闭状态下（可以访问的情况下）的调用的滑动窗口大小
         */
        private Integer slidingWindowSize;
        /**
         * 滑动窗口类型，有两种，基于调用次数和基于时间
         */
        private CircuitBreakerConfig.SlidingWindowType slidingWindowType;
        /**
         * 开启断路器需要的最小请求数量
         */
        private Integer minimumNumberOfCalls;
        /**
         * 当失败比例超过 failureRateThreshold 的时候，断路器会打开，并开始短路呼叫
         */
        private Float failureRateThreshold;
        /**
         * 断路器 open 状态持续时间, 单位毫秒
         */
        private Long waitDurationInOpenStateInMs;
    }

    /**
     * 远程超时配置
     */
    @Data
    static class TimeLimiterProperties {
        /**
         * 超时时间, 单位毫秒
         */
        private Long timeoutDurationInMs;
        /**
         * 超时后是否调用 Future 的 cancel 方法
         */
        private Boolean cancelRunningFuture;
    }
}
