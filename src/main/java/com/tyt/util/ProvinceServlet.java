package com.tyt.util;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;



/**
 * Servlet implementation class ProvinceServlet
 */
public class ProvinceServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
    /**
     * @see HttpServlet#HttpServlet()
     */
    public ProvinceServlet() {
        super();
        // TODO Auto-generated constructor stub
    }

	/**
	 * @see HttpServlet#service(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html;charset=UTF-8");

		
		String p1=request.getParameter("p1");
		String c1=request.getParameter("c1");
		String c2=request.getParameter("c2");
		List<Map<String, Object>> list=ParseXmlUtil.parseXml();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("promap",list.get(0));
		map.put("citymap",list.get(1));
		map.put("countymap",list.get(2));
		map.put("p1", p1);
		map.put("c1", c1);
		map.put("c2", c2);
		JSONObject json=new JSONObject(map);
		PrintWriter out=response.getWriter();
		out.print(json);
	
	}

}
