package com.tyt.util;

import org.redisson.api.RBloomFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2020/12/09 17:19
 */
@Component("bloomFilterService")
public class BloomFilterService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 创建 布隆过滤器
     * @param key key 唯一标识
     * @param capacity 布隆过滤器初始容量 使用容量 * 10 ，rate 为 0.01 容错次数几乎为 0
     * @param rate 官方建议 0.03; 准确率要求过高可以为 0.01; 切记不要为 0
     * @return RBloomFilter
     */
    public RBloomFilter<Object> createBloomFilter(String key,
                                                  long capacity,
                                                  double rate){
        return createBloomFilter(key, capacity, rate, null, null);
    }

    /**
     * 创建 布隆过滤器
     * @param key key 唯一标识
     * @param capacity 布隆过滤器初始容量 使用容量 * 10 ，rate 为 0.01 容错次数几乎为 0
     * @param rate 官方建议 0.03; 准确率要求过高可以为 0.01; 切记不要为 0
     * @param expire 设置有效时间
     * @return RBloomFilter
     */
    public RBloomFilter<Object> createBloomFilter(String key,
                                                  long capacity,
                                                  double rate,
                                                  Long expire){
        return createBloomFilter(key, capacity, rate, expire, TimeUnit.SECONDS);
    }


    /**
     * 创建 布隆过滤器
     * @param key key 唯一标识
     * @param capacity 布隆过滤器初始容量 使用容量 * 10 ，rate 为 0.01 容错次数几乎为 0
     * @param rate 官方建议 0.03; 准确率要求过高可以为 0.01; 切记不要为 0
     * @param expire 设置有效时间
     * @param unit 有效时间单位
     * @return RBloomFilter
     */
    public RBloomFilter<Object> createBloomFilter(String key,
                                                  long capacity,
                                                  double rate,
                                                  Long expire,
                                                  TimeUnit unit){

        RBloomFilter<Object> filter = getBloomFilter(key);
        if (filter.isExists()) {
            return filter;
        }

        if (filter.tryInit(capacity, rate)) {
            //设置有效时间
            if(Objects.nonNull(expire) && Objects.nonNull(unit)){
                filter.expire(expire, unit);
            }
            return filter;
        }
        logger.error("add redisson bloom filter error,  key is {}", key);
        throw new IllegalStateException("redisson bloom filter init error");

    }

    /**
     * 获取 布隆过滤器
     * @param key 唯一标识
     * @return RBloomFilter
     */
    public RBloomFilter<Object> getBloomFilter(String key){
        return RedissonUtils.getClient().getBloomFilter(key);
    }

    /**
     * 异步删除 布隆过滤器
     * @param key 唯一标识
     */
    public void deleteAsync(String key){
        RBloomFilter<Object> filter = getBloomFilter(key);
        filter.isExistsAsync().thenApplyAsync((Function<Boolean, Object>) ab -> filter.delete());
    }


    /**
     * 删除 布隆过滤器
     * @param key 唯一标识
     */
    public void delete(String key){
        RBloomFilter<Object> filter = getBloomFilter(key);
        if(filter.isExists()){
            filter.delete();
        }
    }

    /**
     * 布隆过滤器中 是否包含该值 ，
     *                  包含：   返回false
     *                  不包含： 添加并返回true
     * @param key 唯一标识
     * @param value 需要验证的值
     * @return boolean 不存在布隆过滤器则返回 true
     */
    public boolean contains(String key, Object value){
        RBloomFilter<Object> filter = getBloomFilter(key);
        if(filter.isExists()){
            if(filter.contains(value)){
                logger.info("redisson bloom filter repeat,  key is {}", key);
                return Boolean.FALSE;
            }
            filter.add(value);
        }
        return Boolean.TRUE;
    }

    /**
     *
     *  验证入参是否存在于布隆过滤器 不存在布隆过滤器则创建
     * @param key key 唯一标识
     * @param capacity 布隆过滤器初始容量 使用容量 * 10 ，rate 为 0.01 容错次数几乎为 0
     * @param rate 官方建议 0.03; 准确率要求过高可以为 0.01; 切记不要为 0
     * @param expire 设置有效时间
     * @param unit 有效时间单位
     * <AUTHOR>
     * @date 2020/12/10
     * @return 返回 验证结果
     */
    public boolean containsAndCreate(String key,
                                     long capacity,
                                     double rate,
                                     Long expire,
                                     TimeUnit unit,
                                     Object value){

        RBloomFilter<Object> filter = createBloomFilter(key, capacity, rate, expire, unit);

        if(filter.contains(value)){
            logger.info("redisson bloom filter repeat,  key is {}", key);
            return Boolean.FALSE;
        }

        filter.add(value);
        return Boolean.TRUE;
    }

    /**
     * 验证入参是否存在于布隆过滤器 不存在布隆过滤器则创建
     * @param key key 唯一标识
     * @param value 验证的值
     * @param callback 回调函数去创建
     * @return 结果
     */
    public boolean containsAndCreate(String key,
                                     Object value,
                                     Long expire,
                                     TimeUnit unit,
                                     Function<RBloomFilter<Object>, Optional<BloomInitConfig>> callback){

        RBloomFilter<Object> filter = getBloomFilter(key);
        //如果不存在
        if(Boolean.FALSE.equals(filter.isExists())){
            //如果存在回调函数则调用
            if(Objects.nonNull(callback)){
                Optional<BloomInitConfig> optionalBloomInitConfig = callback.apply(filter);
                if(optionalBloomInitConfig.isPresent()){
                    BloomInitConfig initConfig = optionalBloomInitConfig.get();
                    filter.tryInit(initConfig.getCapacity(), initConfig.getRate());
                }
            }
        }else{
            if(filter.contains(value)){
                logger.info("redisson bloom filter repeat,  key is {}", key);
                return false;
            }
        }
        filter.add(value);
        //设置有效时间
        if(Objects.nonNull(expire) && Objects.nonNull(unit)){
            filter.expire(expire, unit);
        }
        return true;
    }



    public static class BloomInitConfig{
        private final long capacity;
        private final double rate;

        public BloomInitConfig(long capacity, double rate) {
            this.capacity = capacity;
            this.rate = rate;
        }

        public long getCapacity() {
            return capacity;
        }

        public double getRate() {
            return rate;
        }
    }

}
