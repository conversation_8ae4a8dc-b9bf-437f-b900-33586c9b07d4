package com.tyt.util;

import com.tyt.service.common.entity.ResponseCode;
import com.tyt.service.common.exception.TytException;

/**
 * <AUTHOR>
 * @since 2023/5/31 下午4:44
 */
public class PreConditions {

    private PreConditions() {
    }

    /**
     * 如果expression不为true,则抛出异常
     *
     * @param expression 检验理想中的状态
     * @param errorCode  errorCode
     * @param errorMsg   errorMsg
     */
    public static void check(boolean expression, int errorCode, String errorMsg) {
        if (!expression) {
            throw TytException.createException(new ResponseCode(errorCode, errorMsg));
        }
    }
}
