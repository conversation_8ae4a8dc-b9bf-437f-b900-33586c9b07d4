package com.tyt.util;

import com.google.common.collect.Maps;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.timelimiter.TimeLimiter;
import io.github.resilience4j.timelimiter.TimeLimiterConfig;
import io.github.resilience4j.timelimiter.TimeLimiterRegistry;
import io.vavr.control.Try;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.function.Supplier;

import static com.tyt.util.CircuitBreakerUtil.CircuitBreakerConfiguration.BI_SERVICE;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:15
 */
public class CircuitBreakerUtil {

    public enum CircuitBreakerConfiguration {
        /**
         * biService
         */
        BI_SERVICE;

    }

    private CircuitBreakerUtil() {
    }

    private static final CircuitBreakerRegistry circuitBreakerRegistry;
    private static final TimeLimiterRegistry timeLimiterRegistry;
    /**
     * 执行 TimeLimiter 的线程池，按需修改
     */
    private static final ExecutorService executor = Executors.newCachedThreadPool();


    static {
        // biService熔断配置
        Map<String, CircuitBreakerConfig> circuitBreakerConfigMap = Maps.newHashMap();
        CircuitBreakerConfig biServicceCircuitBreakerConfig = CircuitBreakerConfig.custom()
                .failureRateThreshold(50)
                .waitDurationInOpenState(Duration.ofSeconds(30))
                .permittedNumberOfCallsInHalfOpenState(10)
                .slidingWindowSize(20)
                .minimumNumberOfCalls(10)
                .slidingWindowType(CircuitBreakerConfig.SlidingWindowType.COUNT_BASED)
                .build();
        circuitBreakerConfigMap.put(BI_SERVICE.name(), biServicceCircuitBreakerConfig);
        circuitBreakerRegistry = CircuitBreakerRegistry.of(circuitBreakerConfigMap);

        // biService超时配置
        Map<String, TimeLimiterConfig> timeLimiterConfigMap = Maps.newHashMap();
        TimeLimiterConfig biServicceTimeLimiterConfig = TimeLimiterConfig.custom()
                .cancelRunningFuture(true)
                .timeoutDuration(Duration.ofMillis(1000))
                .build();

        timeLimiterConfigMap.put(BI_SERVICE.name(), biServicceTimeLimiterConfig);
        timeLimiterRegistry = TimeLimiterRegistry.of(timeLimiterConfigMap);
    }

    /**
     * 执行方法，通过 CircuitBreaker 包装，提供熔断能力
     *
     * @param circuitBreakerConfiguration 枚举，对应配置
     * @param toRun                       要包装的执行逻辑
     * @return 返回的对象，如果发生异常则会直接抛出异常
     */
    public static <T> T executeWithCircuitBreaker(CircuitBreakerConfiguration circuitBreakerConfiguration,
                                                  Supplier<T> toRun) {

        return executeWithCircuitBreaker(circuitBreakerConfiguration, toRun, null);
    }

    /**
     * 执行方法，通过 CircuitBreaker 包装，提供熔断能力
     *
     * @param circuitBreakerConfiguration 枚举，对应配置
     * @param toRun                       要包装的执行逻辑
     * @param fallback                    执行失败后自定义的 fallback
     * @return 返回的对象，如果发生异常则会直接抛出异常
     */
    public static <T> T executeWithCircuitBreaker(CircuitBreakerConfiguration circuitBreakerConfiguration,
                                                  Supplier<T> toRun,
                                                  Function<Throwable, T> fallback) {

        String circuitBreakerName = circuitBreakerConfiguration.name();
        CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(circuitBreakerName, circuitBreakerName);
        TimeLimiter timeLimiter = timeLimiterRegistry.timeLimiter(circuitBreakerName, circuitBreakerName);

        Supplier<Future<T>> futureSupplier = () -> executor.submit(toRun::get);

        Callable<T> decorateCallable =
                circuitBreaker.decorateCallable(timeLimiter.decorateFutureSupplier(futureSupplier));

        if (fallback != null) {
            return Try.ofCallable(decorateCallable).recover(fallback).get();
        }
        return Try.ofCallable(decorateCallable).get();
    }
}
