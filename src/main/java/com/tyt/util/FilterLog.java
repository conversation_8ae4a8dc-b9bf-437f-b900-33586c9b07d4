package com.tyt.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;

public class FilterLog implements Logger {
	private static final Logger logger = LoggerFactory
			.getLogger(FilterLog.class);
	
	@Override
	public void debug(String arg0) {
		logger.debug(arg0);
		
	}
	@Override
	public void debug(String arg0, Object arg1) {
		logger.debug( arg0,  arg1);
		
	}
	@Override
	public void debug(String arg0, Object... arg1) {
		logger.debug(arg0, arg1);
		
	}
	@Override
	public void debug(String arg0, Throwable arg1) {
		logger.debug(arg0, arg1);
		
	}
	@Override
	public void debug(Marker arg0, String arg1) {
		logger.debug(arg0, arg1);
		
	}
	@Override
	public void debug(String arg0, Object arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.debug(arg0, arg1, arg2);
	}
	@Override
	public void debug(Marker arg0, String arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.debug(arg0, arg1, arg2);
	}
	@Override
	public void debug(Marker arg0, String arg1, Object... arg2) {
		// TODO Auto-generated method stub
		logger.debug(arg0, arg1, arg2);
	}
	@Override
	public void debug(Marker arg0, String arg1, Throwable arg2) {
		// TODO Auto-generated method stub
		logger.debug(arg0, arg1, arg2);
	}
	@Override
	public void debug(Marker arg0, String arg1, Object arg2, Object arg3) {
		// TODO Auto-generated method stub
		logger.debug(arg0, arg1, arg2,arg3);
	}
	@Override
	public void error(String arg0) {
		logger.error(arg0);
		
	}
	@Override
	public void error(String arg0, Object arg1) {
		logger.error( arg0,  arg1);
		
	}
	@Override
	public void error(String arg0, Object... arg1) {
		logger.error(arg0, arg1);
		
	}
	@Override
	public void error(String arg0, Throwable arg1) {
		logger.error(arg0, arg1);
		
	}
	@Override
	public void error(Marker arg0, String arg1) {
		logger.error(arg0, arg1);
		
	}
	@Override
	public void error(String arg0, Object arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.error(arg0, arg1, arg2);
	}
	@Override
	public void error(Marker arg0, String arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.error(arg0, arg1, arg2);
	}
	@Override
	public void error(Marker arg0, String arg1, Object... arg2) {
		// TODO Auto-generated method stub
		logger.error(arg0, arg1, arg2);
	}
	@Override
	public void error(Marker arg0, String arg1, Throwable arg2) {
		// TODO Auto-generated method stub
		logger.error(arg0, arg1, arg2);
	}
	@Override
	public void error(Marker arg0, String arg1, Object arg2, Object arg3) {
		// TODO Auto-generated method stub
		logger.error(arg0, arg1, arg2,arg3);
	}
	@Override
	public String getName() {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public void info(String arg0) {
		logger.info(arg0);
		
	}
	@Override
	public void info(String arg0, Object arg1) {
		logger.info( arg0,  arg1);
		
	}
	@Override
	public void info(String arg0, Object... arg1) {
		logger.info(arg0, arg1);
		
	}
	@Override
	public void info(String arg0, Throwable arg1) {
		logger.info(arg0, arg1);
		
	}
	@Override
	public void info(Marker arg0, String arg1) {
		logger.info(arg0, arg1);
		
	}
	@Override
	public void info(String arg0, Object arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.info(arg0, arg1, arg2);
	}
	@Override
	public void info(Marker arg0, String arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.info(arg0, arg1, arg2);
	}
	@Override
	public void info(Marker arg0, String arg1, Object... arg2) {
		// TODO Auto-generated method stub
		logger.info(arg0, arg1, arg2);
	}
	@Override
	public void info(Marker arg0, String arg1, Throwable arg2) {
		// TODO Auto-generated method stub
		logger.info(arg0, arg1, arg2);
	}
	@Override
	public void info(Marker arg0, String arg1, Object arg2, Object arg3) {
		// TODO Auto-generated method stub
		logger.info(arg0, arg1, arg2,arg3);
	}
	@Override
	public boolean isDebugEnabled() {
		// TODO Auto-generated method stub
		return logger.isDebugEnabled();
	}
	@Override
	public boolean isDebugEnabled(Marker arg0) {
		// TODO Auto-generated method stub
		return logger.isDebugEnabled(arg0);
	}
	@Override
	public boolean isErrorEnabled() {
		// TODO Auto-generated method stub
		return logger.isErrorEnabled();
	}
	@Override
	public boolean isErrorEnabled(Marker arg0) {
		// TODO Auto-generated method stub
		return logger.isErrorEnabled();
	}
	@Override
	public boolean isInfoEnabled() {
		// TODO Auto-generated method stub
		return logger.isInfoEnabled();
	}
	@Override
	public boolean isInfoEnabled(Marker arg0) {
		// TODO Auto-generated method stub
		return logger.isInfoEnabled();
	}
	@Override
	public boolean isTraceEnabled() {
		// TODO Auto-generated method stub
		return logger.isTraceEnabled();
	}
	@Override
	public boolean isTraceEnabled(Marker arg0) {
		// TODO Auto-generated method stub
		return logger.isTraceEnabled(arg0);
	}
	@Override
	public boolean isWarnEnabled() {
		// TODO Auto-generated method stub
		return logger.isWarnEnabled();
	}
	@Override
	public boolean isWarnEnabled(Marker arg0) {
		return logger.isWarnEnabled(arg0);
	}
	@Override
	public void trace(String arg0) {
		logger.trace(arg0);
		
	}
	@Override
	public void trace(String arg0, Object arg1) {
		logger.trace( arg0,  arg1);
		
	}
	@Override
	public void trace(String arg0, Object... arg1) {
		logger.trace(arg0, arg1);
		
	}
	@Override
	public void trace(String arg0, Throwable arg1) {
		logger.trace(arg0, arg1);
		
	}
	@Override
	public void trace(Marker arg0, String arg1) {
		logger.trace(arg0, arg1);
		
	}
	@Override
	public void trace(String arg0, Object arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.trace(arg0, arg1, arg2);
	}
	@Override
	public void trace(Marker arg0, String arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.trace(arg0, arg1, arg2);
	}
	@Override
	public void trace(Marker arg0, String arg1, Object... arg2) {
		// TODO Auto-generated method stub
		logger.trace(arg0, arg1, arg2);
	}
	@Override
	public void trace(Marker arg0, String arg1, Throwable arg2) {
		// TODO Auto-generated method stub
		logger.trace(arg0, arg1, arg2);
	}
	@Override
	public void trace(Marker arg0, String arg1, Object arg2, Object arg3) {
		// TODO Auto-generated method stub
		logger.trace(arg0, arg1, arg2,arg3);
	}
	@Override
	public void warn(String arg0) {
		logger.warn(arg0);
		
	}
	@Override
	public void warn(String arg0, Object arg1) {
		logger.warn( arg0,  arg1);
		
	}
	@Override
	public void warn(String arg0, Object... arg1) {
		logger.warn(arg0, arg1);
		
	}
	@Override
	public void warn(String arg0, Throwable arg1) {
		logger.warn(arg0, arg1);
		
	}
	@Override
	public void warn(Marker arg0, String arg1) {
		logger.warn(arg0, arg1);
		
	}
	@Override
	public void warn(String arg0, Object arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.warn(arg0, arg1, arg2);
	}
	@Override
	public void warn(Marker arg0, String arg1, Object arg2) {
		// TODO Auto-generated method stub
		logger.warn(arg0, arg1, arg2);
	}
	@Override
	public void warn(Marker arg0, String arg1, Object... arg2) {
		// TODO Auto-generated method stub
		logger.warn(arg0, arg1, arg2);
	}
	@Override
	public void warn(Marker arg0, String arg1, Throwable arg2) {
		// TODO Auto-generated method stub
		logger.warn(arg0, arg1, arg2);
	}
	@Override
	public void warn(Marker arg0, String arg1, Object arg2, Object arg3) {
		// TODO Auto-generated method stub
		logger.warn(arg0, arg1, arg2,arg3);
	}
}
