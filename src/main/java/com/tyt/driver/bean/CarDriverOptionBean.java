package com.tyt.driver.bean;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/01/15 14:34
 */
public class CarDriverOptionBean {


    private String driverPhone;
    private Long driverId;
    private Integer examineStatus;
    private String driverShowName;
    private Date commitTime;
    private String dcImageUrl;
    private String dlFrontImageUrl;
    private String dicBackImageUrl;
    private String dicFrontImageUrl;


    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Integer getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(Integer examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getDriverShowName() {
        return driverShowName;
    }

    public void setDriverShowName(String driverShowName) {
        this.driverShowName = driverShowName;
    }

    public Date getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
    }

    public String getDcImageUrl() {
        return dcImageUrl;
    }

    public void setDcImageUrl(String dcImageUrl) {
        this.dcImageUrl = dcImageUrl;
    }

    public String getDlFrontImageUrl() {
        return dlFrontImageUrl;
    }

    public void setDlFrontImageUrl(String dlFrontImageUrl) {
        this.dlFrontImageUrl = dlFrontImageUrl;
    }

    public String getDicBackImageUrl() {
        return dicBackImageUrl;
    }

    public void setDicBackImageUrl(String dicBackImageUrl) {
        this.dicBackImageUrl = dicBackImageUrl;
    }

    public String getDicFrontImageUrl() {
        return dicFrontImageUrl;
    }

    public void setDicFrontImageUrl(String dicFrontImageUrl) {
        this.dicFrontImageUrl = dicFrontImageUrl;
    }
}
