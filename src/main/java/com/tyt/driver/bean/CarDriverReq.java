package com.tyt.driver.bean;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/01/15 14:41
 */
public class CarDriverReq {

    private Long userId;
    private Long driverId;
    private String driverPhone;
    private String driverShowName;
    private String cellPhone;

    private Optional<String> dicFrontImagePath;
    private Optional<String> dicBackImagePath;
    private Optional<String> dlFrontImagePath;
    private Optional<String> dcImageUrlPath;

    public Optional<String> getDicBackImagePath() {
        return dicBackImagePath;
    }

    public void setDicBackImagePath(Optional<String> dicBackImagePath) {
        this.dicBackImagePath = dicBackImagePath;
    }

    public Optional<String> getDlFrontImagePath() {
        return dlFrontImagePath;
    }

    public void setDlFrontImagePath(Optional<String> dlFrontImagePath) {
        this.dlFrontImagePath = dlFrontImagePath;
    }

    public Optional<String> getDcImageUrlPath() {
        return dcImageUrlPath;
    }

    public void setDcImageUrlPath(Optional<String> dcImageUrlPath) {
        this.dcImageUrlPath = dcImageUrlPath;
    }

    public Optional<String> getDicFrontImagePath() {
        return dicFrontImagePath;
    }

    public void setDicFrontImagePath(Optional<String> dicFrontImagePath) {
        this.dicFrontImagePath = dicFrontImagePath;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getDriverShowName() {
        return driverShowName;
    }

    public void setDriverShowName(String driverShowName) {
        this.driverShowName = driverShowName;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }
}
