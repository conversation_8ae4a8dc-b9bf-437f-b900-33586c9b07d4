package com.tyt.driver.bean;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/01/15 14:47
 */
public class CarDriverDescriptionBean {


    private String driverPhone;
    private Long driverId;
    private Integer examineStatus;
    private String driverShowName;
    private Date commitTime;
    private Integer dcExamineStatus;
    private String dcImageUrl;
    private Integer dlExamineStatus;
    private String dlFrontImageUrl;
    private Integer dicbExamineStatus;
    private String dicBackImageUrl;
    private Integer dicfExamineStatus;
    private String dicFrontImageUrl;
    private String dicfExamineLoseNote;
    private String dicbExamineLoseNote;
    private String dlExamineLoseNote;
    private String dcExamineLoseNote;


    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Integer getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(Integer examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getDriverShowName() {
        return driverShowName;
    }

    public void setDriverShowName(String driverShowName) {
        this.driverShowName = driverShowName;
    }

    public Date getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
    }

    public Integer getDcExamineStatus() {
        return dcExamineStatus;
    }

    public void setDcExamineStatus(Integer dcExamineStatus) {
        this.dcExamineStatus = dcExamineStatus;
    }

    public String getDcImageUrl() {
        return dcImageUrl;
    }

    public void setDcImageUrl(String dcImageUrl) {
        this.dcImageUrl = dcImageUrl;
    }

    public Integer getDlExamineStatus() {
        return dlExamineStatus;
    }

    public void setDlExamineStatus(Integer dlExamineStatus) {
        this.dlExamineStatus = dlExamineStatus;
    }

    public String getDlFrontImageUrl() {
        return dlFrontImageUrl;
    }

    public void setDlFrontImageUrl(String dlFrontImageUrl) {
        this.dlFrontImageUrl = dlFrontImageUrl;
    }

    public Integer getDicbExamineStatus() {
        return dicbExamineStatus;
    }

    public void setDicbExamineStatus(Integer dicbExamineStatus) {
        this.dicbExamineStatus = dicbExamineStatus;
    }

    public String getDicBackImageUrl() {
        return dicBackImageUrl;
    }

    public void setDicBackImageUrl(String dicBackImageUrl) {
        this.dicBackImageUrl = dicBackImageUrl;
    }

    public Integer getDicfExamineStatus() {
        return dicfExamineStatus;
    }

    public void setDicfExamineStatus(Integer dicfExamineStatus) {
        this.dicfExamineStatus = dicfExamineStatus;
    }

    public String getDicFrontImageUrl() {
        return dicFrontImageUrl;
    }

    public void setDicFrontImageUrl(String dicFrontImageUrl) {
        this.dicFrontImageUrl = dicFrontImageUrl;
    }

    public String getDicfExamineLoseNote() {
        return dicfExamineLoseNote;
    }

    public void setDicfExamineLoseNote(String dicfExamineLoseNote) {
        this.dicfExamineLoseNote = dicfExamineLoseNote;
    }

    public String getDicbExamineLoseNote() {
        return dicbExamineLoseNote;
    }

    public void setDicbExamineLoseNote(String dicbExamineLoseNote) {
        this.dicbExamineLoseNote = dicbExamineLoseNote;
    }

    public String getDlExamineLoseNote() {
        return dlExamineLoseNote;
    }

    public void setDlExamineLoseNote(String dlExamineLoseNote) {
        this.dlExamineLoseNote = dlExamineLoseNote;
    }

    public String getDcExamineLoseNote() {
        return dcExamineLoseNote;
    }

    public void setDcExamineLoseNote(String dcExamineLoseNote) {
        this.dcExamineLoseNote = dcExamineLoseNote;
    }
}
