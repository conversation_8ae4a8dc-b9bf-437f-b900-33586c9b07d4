package com.tyt.driver.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.driver.service.TytCarDriverExamineLogService;
import com.tyt.model.TytCarDriverArchives;
import com.tyt.model.TytCarDriverExamineLog;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service("tytCarDriverExamineLogService")
public class TytCarDriverExamineLogServiceImpl extends BaseServiceImpl<TytCarDriverExamineLog, Long> implements TytCarDriverExamineLogService {

    @Resource(name = "tytCarDriverExamineLogDao")
    public void setBaseDao(BaseDao<TytCarDriverExamineLog, Long> tytCarDriverExamineLogLongBaseDao) {
        super.setBaseDao(tytCarDriverExamineLogLongBaseDao);
    }

    @Override
    public void addLog(TytCarDriverArchives archives, Long userId, String name) {
        TytCarDriverExamineLog examineLog = new TytCarDriverExamineLog();
        examineLog.setDriverArchivesId(archives.getId());
        examineLog.setExamineStatus(archives.getExamineStatus());
        examineLog.setDicfExamineStatus(archives.getDicfExamineStatus());
        examineLog.setDicfExamineLoseNote(archives.getDicfExamineLoseNote());
        examineLog.setDicbExamineLoseNote(archives.getDicbExamineLoseNote());
        examineLog.setDicbExamineStatus(archives.getDicbExamineStatus());
        examineLog.setDlExamineLoseNote(archives.getDlExamineLoseNote());
        examineLog.setDlExamineStatus(archives.getDlExamineStatus());
        examineLog.setDcExamineLoseNote(archives.getDcExamineLoseNote());
        examineLog.setDcExamineStatus(archives.getDcExamineStatus());
        examineLog.setCtime(new Date());
        examineLog.setCreateUserId(userId);
        examineLog.setCreateShowName(name);
        this.getBaseDao().insert(examineLog);
    }
}
