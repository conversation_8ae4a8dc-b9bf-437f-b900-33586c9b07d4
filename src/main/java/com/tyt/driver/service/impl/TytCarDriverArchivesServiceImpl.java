package com.tyt.driver.service.impl;


import com.alibaba.fastjson.JSON;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.driver.bean.CarDriverGoodOrderBean;
import com.tyt.driver.bean.CarDriverReq;
import com.tyt.driver.common.AuditDriverStatusEnum;
import com.tyt.driver.service.TytCarDriverArchivesService;
import com.tyt.driver.service.TytCarDriverExamineLogService;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCarDriverArchives;
import com.tyt.model.User;
import com.tyt.mybatis.mapper.CarDetailHeadMapper;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestOperations;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

@Service("tytCarDriverArchivesService")
public class TytCarDriverArchivesServiceImpl extends BaseServiceImpl<TytCarDriverArchives, Long> implements TytCarDriverArchivesService {


    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "scRestTemplate")
    private RestOperations restOperations;
    @Autowired
    private CarDetailHeadMapper carDetailHeadMapper;
    @Resource(name = "tytCarDriverExamineLogService")
    private TytCarDriverExamineLogService tytCarDriverExamineLogService;

    @Resource(name = "tytCarDriverArchivesDao")
    public void setBaseDao(BaseDao<TytCarDriverArchives, Long> tytCarDriverArchivesLongBaseDao) {
        super.setBaseDao(tytCarDriverArchivesLongBaseDao);
    }

    @Override
    public List<TytCarDriverArchives> searchList(Long userId, String searchContent) {
        PageBean bean = new PageBean();
        bean.setPageSize(10);
        bean.setCurrentPage(0);
        if(StringUtils.isNotEmpty(searchContent)){
            searchContent = "%" + searchContent + "%";
            return this.getBaseDao().search(" userId = ? and ( driverPhone " +
                    "like ? or driverShowName like ? ) order by commitTime", new Object[]{userId, searchContent, searchContent}, bean);
        }else{
            return this.getBaseDao().search(" userId = ?  order by commitTime", new Object[]{userId}, bean);
        }
    }

    @Override
    public List<TytCarDriverArchives> searchList(String cellphone) {
        return this.getBaseDao().find("from TytCarDriverArchives where userPhone = ?", cellphone);
    }

    @Override
    public int countCarDriverNumber(Long userId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        BigInteger rowCount = this.getBaseDao().queryByMap("select count(*) from tyt_car_driver_archives where user_id = :userId ", paramMap, null, null);
        return rowCount.intValue();
    }

    @Override
    public ResultMsgBean updateArchives(CarDriverReq carDriverReq) {

        //验证手机号不为空 1开头的长度为11位的数字
        if(org.apache.commons.lang3.StringUtils.isEmpty(carDriverReq.getDriverPhone()) || carDriverReq.getDriverPhone().length() != 11
                || !NumberUtils.isNumber(carDriverReq.getDriverPhone()) || !carDriverReq.getDriverPhone().startsWith("1")){
            return new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_PHONE_ERR,"手机号错误");
        }

        //新增
        TytCarDriverArchives archives;
        if (Objects.isNull(carDriverReq.getDriverId())) {

            archives = new TytCarDriverArchives();

            //验证照片
            Optional<ResultMsgBean> resultMsgBean = checkImage(carDriverReq, archives, true, false);
            if(resultMsgBean.isPresent()){
                return resultMsgBean.get();
            }
            try {
                User user = userService.getByUserId(carDriverReq.getUserId());
                if(Objects.isNull(user)){
                    return new ResultMsgBean(ReturnCodeConstant.BILLING_CAR_ERR,"该车辆非调度车辆");
                }
                archives.setUserId(carDriverReq.getUserId());
                archives.setUserPhone(user.getCellPhone());
                archives.setUserShowName(user.getTrueName());
            } catch (Exception e) {
                e.printStackTrace();
                return new ResultMsgBean(ReturnCodeConstant.BILLING_CAR_ERR,"该车辆非调度车辆");
            }

            //修改 base 信息
            setBaseInfo(carDriverReq, archives);

            archives.setCtime(new Date());
            this.add(archives);

            //添加日志
            tytCarDriverExamineLogService.addLog(archives, archives.getUserId(), archives.getUserShowName());
            //绑定车辆和司机
            carDetailHeadMapper.updateCarDriverIdByPhone(archives.getDriverPhone(),archives.getUserId(),archives.getId());
            //编辑
        }else{
            archives = this.getById(carDriverReq.getDriverId());
            if(Objects.isNull(archives)){
                return new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_EMPTY,"司机信息不存在");
            }

            //如果 司机信息为审核失败 走部分修改图片的方法
            if(Objects.equals(archives.getExamineStatus(), AuditDriverStatusEnum.FAIL.getCode())){
                return updateImage(carDriverReq, archives);
            }

            //验证照片
            Optional<ResultMsgBean> msgBean = checkImage(carDriverReq, archives, false, false);
            if(msgBean.isPresent()){
                return msgBean.get();
            }

            //修改 base 信息
            setBaseInfo(carDriverReq, archives);
            archives.setExamineUserId(null);
            archives.setExamineUserName(null);
            this.update(archives);
            //添加日志
            tytCarDriverExamineLogService.addLog(archives, archives.getUserId(), archives.getUserShowName());
        }

        return new ResultMsgBean(200,"保存成功", archives);

    }

    @Override
    public ResultMsgBean updateImage(CarDriverReq carDriverReq, TytCarDriverArchives archives) {
        logger.info("编辑保存上传参数：{}", JSON.toJSONString(carDriverReq));
        //验证照片
        Optional<ResultMsgBean> msgBean = checkImage(carDriverReq, archives, false, true);
        if(msgBean.isPresent()){
            return msgBean.get();
        }
        archives.setExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
        archives.setUtime(new Date());
        archives.setDriverPhone(carDriverReq.getDriverPhone());
        archives.setDriverShowName(carDriverReq.getDriverShowName());

        this.update(archives);

        //添加日志
        tytCarDriverExamineLogService.addLog(archives, archives.getUserId(), archives.getUserShowName());

        return new ResultMsgBean(200, "修改成功", archives);
    }

    @Override
    public ResultMsgBean deleteDriver(Long driverId, Long userId) {
        TytCarDriverArchives archives = this.getById(driverId);
        if (Objects.nonNull(archives)) {
            if (!Objects.equals(archives.getUserId(), userId)) {
                return new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_EMPTY, "司机信息不存在");
            }

            //好货订单判定
            CarDriverGoodOrderBean result = restOperations.getForObject("/app/driver/driverStatus?id=" + driverId, CarDriverGoodOrderBean.class);
            logger.info("调度好货查询：{}", JSON.toJSONString(result));
            if(Objects.isNull(result)){
                return new ResultMsgBean(500, "网络异常");
            }
            if (Objects.nonNull(result.getData()) && result.getData() > 0) {
                return new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_NOT_DELETE, "司机正在承运好货订单");
            }

            this.delete(driverId);
            //修改车辆表相关司机数据
            carDetailHeadMapper.updateCarDriverIdByDriverId(driverId);

        }
        return new ResultMsgBean(200, "删除成功", archives);
    }


    @Override
    public TytCarDriverArchives getByDriverPhone(String phone) {
        List<TytCarDriverArchives> archives = this.getBaseDao().find("from TytCarDriverArchives where driverPhone = ? ", phone);
        if(CollectionUtils.isNotEmpty(archives)){
            return archives.get(0);
        }
        return null;
    }

    @Override
    public TytCarDriverArchives getByDriverPhone(Long userId, String phone) {
        List<TytCarDriverArchives> archives = this.getBaseDao().find("from TytCarDriverArchives where driverPhone = ? and userId = ? ", phone, userId);
        if(CollectionUtils.isNotEmpty(archives)){
            return archives.get(0);
        }
        return null;
    }

    private static void setBaseInfo(CarDriverReq carDriverReq, TytCarDriverArchives archives) {
        archives.setDriverPhone(carDriverReq.getDriverPhone());
        archives.setDriverShowName(carDriverReq.getDriverShowName());
        archives.setExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
        archives.setDicfExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
        archives.setDicbExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
        archives.setDlExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
        archives.setDcExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
        archives.setCommitTime(new Date());
        archives.setUtime(new Date());
    }


    /**
     * 上传照片
     * @param carDriverReq
     * @return
     */
    private Optional<ResultMsgBean> checkImage(CarDriverReq carDriverReq,
                                               TytCarDriverArchives archives,
                                               boolean isValid, boolean auditCommit) {
        //身份证正面照
        if (carDriverReq.getDicFrontImagePath().isPresent()) {
            archives.setDicFrontImageUrl(carDriverReq.getDicFrontImagePath().get());
            archives.setDicfExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
            archives.setDicfExamineLoseNote(null);
        }else{
            //是否开启验证 和 （为审核提交的的审核失败的）
            if(isValid && (!auditCommit || !Objects.equals(AuditDriverStatusEnum.FAIL.getCode(),archives.getDicfExamineStatus())))
                return Optional.of(new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_DICF_EMPTY, "身份证正面不能为空"));
        }

        //身份证背面照
        if (carDriverReq.getDicBackImagePath().isPresent()) {
            archives.setDicBackImageUrl(carDriverReq.getDicBackImagePath().get());
            archives.setDicbExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
            archives.setDicbExamineLoseNote(null);
        }else{
            //是否开启验证 和 （为审核提交的的审核失败的）
            if(isValid && (!auditCommit || !Objects.equals(AuditDriverStatusEnum.FAIL.getCode(),archives.getDicbExamineStatus())))
                return Optional.of(new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_DICB_EMPTY,"身份证反面不能为空"));
        }

        //驾驶证正面照
        if (carDriverReq.getDlFrontImagePath().isPresent()) {
            archives.setDlFrontImageUrl(carDriverReq.getDlFrontImagePath().get());
            archives.setDlExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
            archives.setDlExamineLoseNote(null);
        }else{
            //是否开启验证 和 （为审核提交的的审核失败的）
            if(isValid && (!auditCommit || !Objects.equals(AuditDriverStatusEnum.FAIL.getCode(),archives.getDlExamineStatus())))
                return Optional.of(new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_DL_EMPTY,"驾驶证正面不能为空"));
        }

        //就业证不能为空
        if (carDriverReq.getDcImageUrlPath().isPresent()) {
            archives.setDcImageUrl(carDriverReq.getDcImageUrlPath().get());
            archives.setDcExamineStatus(AuditDriverStatusEnum.WAITING_AUDIT.getCode());
            archives.setDcExamineLoseNote(null);
        }else{
            //是否开启验证 和 （为审核提交的的审核失败的）
            if(isValid && (!auditCommit || !Objects.equals(AuditDriverStatusEnum.FAIL.getCode(),archives.getDcExamineStatus())))
                return Optional.of(new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_DC_EMPTY,"从业资格证不能为空"));
        }
        return Optional.empty();
    }



}
