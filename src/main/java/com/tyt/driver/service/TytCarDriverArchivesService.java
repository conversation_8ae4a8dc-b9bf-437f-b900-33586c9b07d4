package com.tyt.driver.service;


import com.tyt.base.service.BaseService;
import com.tyt.driver.bean.CarDriverReq;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCarDriverArchives;

import java.util.List;

public interface TytCarDriverArchivesService extends BaseService<TytCarDriverArchives, Long> {

    List<TytCarDriverArchives> searchList(Long userId, String searchContent);

    List<TytCarDriverArchives> searchList(String cellphone);

    int countCarDriverNumber(Long userId);

    ResultMsgBean updateArchives(CarDriverReq carDriverReq);

    ResultMsgBean updateImage(CarDriverReq carDriverReq, TytCarDriverArchives archives);

    ResultMsgBean deleteDriver(Long driverId, Long userId);

    TytCarDriverArchives getByDriverPhone(String phone);

    TytCarDriverArchives getByDriverPhone(Long userId, String phone);


}
