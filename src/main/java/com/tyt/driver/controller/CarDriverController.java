package com.tyt.driver.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.driver.bean.CarDriverDescriptionBean;
import com.tyt.driver.bean.CarDriverOptionBean;
import com.tyt.driver.bean.CarDriverReq;
import com.tyt.driver.common.AuditDriverStatusEnum;
import com.tyt.driver.service.TytCarDriverArchivesService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCarDriverArchives;
import com.tyt.model.TytSource;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TytSourceUtil;
import com.tyt.util.httputil.SCRestTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestOperations;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/01/15 14:29
 */
@Controller
@RequestMapping("/plat/car/driver")
public class CarDriverController extends BaseController {

    @Resource(name = "tytCarDriverArchivesService")
    private TytCarDriverArchivesService tytCarDriverArchivesService;

    @Resource(name = "scRestTemplate")
    private RestOperations restOperations;

    @RequestMapping("/option")
    @ResponseBody
    public ResultMsgBean option(@RequestParam("userId") Long userId, @RequestParam(name = "searchDriver", required = false) String searchDriver) {

        List<TytCarDriverArchives> archives = tytCarDriverArchivesService.searchList(userId, searchDriver);
        List<CarDriverOptionBean> optionBeans = archives.stream()
                .map(a -> {
                    CarDriverOptionBean bean = new CarDriverOptionBean();
                    BeanUtils.copyProperties(a, bean);
                    bean.setDriverId(a.getId());
                    return bean;
                })
                .collect(Collectors.toList());
        return new ResultMsgBean(200, "查询成功", optionBeans);
    }


    @RequestMapping("/save")
    @ResponseBody
    public ResultMsgBean save(HttpServletRequest request,
                              @RequestParam(name = "dicFrontImage", required = false) MultipartFile dicFrontImage,
                              @RequestParam(name = "dicBackImage", required = false) MultipartFile dicBackImage,
                              @RequestParam(name = "dlFrontImage", required = false) MultipartFile dlFrontImage,
                              @RequestParam(name = "dcImageUrl", required = false) MultipartFile dcImageUrl) {

        /* 参数解析 */
        try {

            if(!super.isAllImg(dicFrontImage, dicBackImage, dlFrontImage, dcImageUrl)){
                ResultMsgBean rm = new ResultMsgBean(ResultMsgBean.ERROR,"图片格式有误！");
                return rm;
            }

            Map<String, String> params = parseRequestParams(request);
            logger.info("上传请求参数：{}", JSONObject.toJSONString(params));
            CarDriverReq carDriverReq = new JSONObject(Maps.newHashMap(params)).toJavaObject(CarDriverReq.class);
            //上传照片
            uploadImages(dicFrontImage, dicBackImage, dlFrontImage, dcImageUrl, carDriverReq);

            ResultMsgBean bean = tytCarDriverArchivesService.updateArchives(carDriverReq);

            // 3/26 要下线特运通大件运输智能调度系统，先注释掉下面代码，过三个月可以删除
            /*if(Objects.nonNull(bean.getData()) && bean.getData() instanceof TytCarDriverArchives
                    && StringUtils.isNotEmpty(((TytCarDriverArchives) bean.getData()).getUserPhone())){
                //通知变动
                sendDriverDescriptionChangeMessage(((TytCarDriverArchives) bean.getData()).getUserPhone());
            }*/

            return bean;
        } catch (Exception e) {
            logger.info("添加司机重复异常：{}  【{}】", e.getClass().getName(), e.getMessage());
            //唯一索引重复添加异常
            if (e instanceof DataIntegrityViolationException) {
                return new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_PHONE_EXIST,"手机号已存在");
            }
            e.printStackTrace();
            return new ResultMsgBean(ReturnCodeConstant.ERROR,"网络异常");
        }

    }

    @RequestMapping("/saveNew")
    @ResponseBody
    public ResultMsgBean saveNew(HttpServletRequest request,
                              @RequestParam(name = "dicFrontImage") String dicFrontImage,
                              @RequestParam(name = "dicBackImage") String dicBackImage,
                              @RequestParam(name = "dlFrontImage") String dlFrontImage,
                              @RequestParam(name = "dcImageUrl") String dcImageUrl) {

        /* 参数解析 */
        try {
            Map<String, String> params = parseRequestParams(request);
            logger.info("上传请求参数：{}", JSONObject.toJSONString(params));
            CarDriverReq carDriverReq = new JSONObject(Maps.newHashMap(params)).toJavaObject(CarDriverReq.class);
            //上传照片
            uploadImages(dicFrontImage, dicBackImage, dlFrontImage, dcImageUrl, carDriverReq);

            return tytCarDriverArchivesService.updateArchives(carDriverReq);
        } catch (Exception e) {
            logger.info("添加司机重复异常：{}  【{}】", e.getClass().getName(), e.getMessage());
            //唯一索引重复添加异常
            if (e instanceof DataIntegrityViolationException) {
                return new ResultMsgBean(ReturnCodeConstant.BILLING_DRIVER_PHONE_EXIST,"手机号已存在");
            }
            e.printStackTrace();
            return new ResultMsgBean(ReturnCodeConstant.ERROR,"网络异常");
        }

    }


    @RequestMapping("/remove")
    @ResponseBody
    public ResultMsgBean remove(@RequestParam("userId") Long userId, @RequestParam("driverId") Long driverId) {
        ResultMsgBean bean = tytCarDriverArchivesService.deleteDriver(driverId, userId);
        // 3/26 要下线特运通大件运输智能调度系统，先注释掉下面代码，过三个月可以删除
        /*if(Objects.nonNull(bean.getData()) && bean.getData() instanceof TytCarDriverArchives
                && StringUtils.isNotEmpty(((TytCarDriverArchives) bean.getData()).getUserPhone())){
            //通知变动
            sendDriverDescriptionChangeMessage(((TytCarDriverArchives) bean.getData()).getUserPhone());
        }*/
        return bean;
    }


    @RequestMapping("/description")
    @ResponseBody
    public ResultMsgBean description(@RequestParam(required = false, value = "driverId") Long driverId,
                                     @RequestParam(required = false, value = "driverPhone") String driverPhone) {
        TytCarDriverArchives archives;
        if(Objects.nonNull(driverId)){
            archives = tytCarDriverArchivesService.getById(driverId);
        }else if(StringUtils.isNotEmpty(driverPhone)){
            archives = tytCarDriverArchivesService.getByDriverPhone(driverPhone);
        }else{
            return new ResultMsgBean(200,"查询成功");
        }
        CarDriverDescriptionBean bean = new CarDriverDescriptionBean();
        if (archives != null) {
            BeanUtils.copyProperties(archives, bean);
            bean.setDriverId(driverId);

            if(Objects.equals(AuditDriverStatusEnum.FAIL.getCode(), bean.getDicfExamineStatus()) && StringUtils.isNotEmpty(bean.getDicfExamineLoseNote())){
                TytSource source = TytSourceUtil.getSourceName("billing_dicf_examine_note", bean.getDicfExamineLoseNote());
                if(Objects.nonNull(source)){
                    bean.setDicfExamineLoseNote(source.getName());
                }
            }else{
                bean.setDicfExamineLoseNote(null);
            }
            if(Objects.equals(AuditDriverStatusEnum.FAIL.getCode(), bean.getDicbExamineStatus()) && StringUtils.isNotEmpty(bean.getDicbExamineLoseNote())){
                TytSource source = TytSourceUtil.getSourceName("billing_dicb_examine_note", bean.getDicbExamineLoseNote());
                if(Objects.nonNull(source)){
                    bean.setDicbExamineLoseNote(source.getName());
                }
            }else{
                bean.setDicbExamineLoseNote(null);
            }
            if(Objects.equals(AuditDriverStatusEnum.FAIL.getCode(), bean.getDlExamineStatus()) && StringUtils.isNotEmpty(bean.getDlExamineLoseNote())){
                TytSource source = TytSourceUtil.getSourceName("billing_dl_examine_note", bean.getDlExamineLoseNote());
                if(Objects.nonNull(source)){
                    bean.setDlExamineLoseNote(source.getName());
                }
            }else{
                bean.setDlExamineLoseNote(null);
            }
            if(Objects.equals(AuditDriverStatusEnum.FAIL.getCode(), bean.getDcExamineStatus()) && StringUtils.isNotEmpty(bean.getDcExamineLoseNote())){
                TytSource source = TytSourceUtil.getSourceName("billing_dc_examine_note", bean.getDcExamineLoseNote());
                if(Objects.nonNull(source)){
                    bean.setDcExamineLoseNote(source.getName());
                }
            }else{
                bean.setDcExamineLoseNote(null);
            }

        }
        return new ResultMsgBean(200, "查询成功", bean);
    }



    /**
     * 上传图片
     *
     * @param file
     * @return
     */
    private Optional<String> upload(MultipartFile file) {

        try {
            if (Objects.nonNull(file) && !file.isEmpty()) {
                String fileName = super.renamePic(file, "driver");
                file.transferTo(new File(AppConfig.getProperty("picture.path.domain") + fileName));
                return Optional.of(fileName);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    /**
     * 上传照片
     *
     * @return
     */
    private void uploadImages(MultipartFile dicFrontImage,
                              MultipartFile dicBackImage,
                              MultipartFile dlFrontImage,
                              MultipartFile dcImageUrl,
                              CarDriverReq carDriverReq) {
        //身份证正面照
        carDriverReq.setDicFrontImagePath(upload(dicFrontImage));
        //身份证背面照
        carDriverReq.setDicBackImagePath(upload(dicBackImage));
        //驾驶证正面照
        carDriverReq.setDlFrontImagePath(upload(dlFrontImage));
        //就业证不能为空
        carDriverReq.setDcImageUrlPath(upload(dcImageUrl));
    }


    /**
     * 上传图片
     *
     * @param file
     * @return
     */
    private Optional<String> upload(String file) {
        if(StringUtils.isNotEmpty(file)){
            return Optional.of(file);
        }
        return Optional.empty();
    }


    /**
     * 上传照片
     *
     * @return
     */
    private void uploadImages(String dicFrontImage,
                              String dicBackImage,
                              String dlFrontImage,
                              String dcImageUrl,
                              CarDriverReq carDriverReq) {
        //身份证正面照
        carDriverReq.setDicFrontImagePath(upload(dicFrontImage));
        //身份证背面照
        carDriverReq.setDicBackImagePath(upload(dicBackImage));
        //驾驶证正面照
        carDriverReq.setDlFrontImagePath(upload(dlFrontImage));
        //就业证不能为空
        carDriverReq.setDcImageUrlPath(upload(dcImageUrl));
    }

    private void sendDriverDescriptionChangeMessage(String phone){

        Map<String, String> map= new HashMap<>();
        map.put("ownerPhone", phone);
        SCRestTemplate.DefaultResponse response = restOperations.postForObject("/app/driver/inform", map, SCRestTemplate.DefaultResponse.class);
        if(Objects.nonNull(response) && response.getStatus() == 200){
            logger.info("司机变更通知成功");
        }else{
            logger.error("司机变更通知失败 手机号为：{}", phone);
        }
    }
}
