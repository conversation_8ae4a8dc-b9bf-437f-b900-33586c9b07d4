package com.tyt.driver.common;

public enum AuditDriverStatusEnum {
    WAITING_AUDIT(1,"待审核"),
    SUCCESS(2,"审核通过"),
    FAIL(3,"审核失败")
    ;

    private final String text;
    private final int code;

    AuditDriverStatusEnum( int code, String text) {
        this.text = text;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public String getText() {
        return text;
    }
}
