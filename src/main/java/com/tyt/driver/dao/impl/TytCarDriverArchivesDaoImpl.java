package com.tyt.driver.dao.impl;


import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.driver.dao.TytCarDriverArchivesDao;
import com.tyt.model.TytCarDriverArchives;
import org.springframework.stereotype.Repository;

@Repository("tytCarDriverArchivesDao")
public class TytCarDriverArchivesDaoImpl extends BaseDaoImpl<TytCarDriverArchives, Long> implements TytCarDriverArchivesDao {

    public TytCarDriverArchivesDaoImpl() {
        this.setEntityClass(TytCarDriverArchives.class);
    }
}
