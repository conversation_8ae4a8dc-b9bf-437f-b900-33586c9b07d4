package com.tyt.driver.dao.impl;


import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.driver.dao.TytCarDriverExamineLogDao;
import com.tyt.model.TytCarDriverExamineLog;
import org.springframework.stereotype.Repository;

@Repository("tytCarDriverExamineLogDao")
public class TytCarDriverExamineLogDaoImpl extends BaseDaoImpl<TytCarDriverExamineLog, Long> implements TytCarDriverExamineLogDao {

    public TytCarDriverExamineLogDaoImpl() {
        this.setEntityClass(TytCarDriverExamineLog.class);
    }
}
