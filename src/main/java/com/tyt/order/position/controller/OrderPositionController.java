package com.tyt.order.position.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.order.position.bean.OrderPositionBean;
import com.tyt.order.position.service.OrderPositionsService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/03/04 13:37
 */
@RestController
@RequestMapping("/plat/order/position")
public class OrderPositionController extends BaseController {

    @Autowired
    private OrderPositionsService orderPositionsService;

    /**
     * 订单定位上报
     *
     * @param bean 手机定位信息
     * @return
     */
    @PostMapping("/save")
    public ResultMsgBean saveOrderPosition(OrderPositionBean bean) {
        if (null == bean.getUserId() || StringUtils.isBlank(bean.getOrderIds()) || StringUtils.isBlank(bean.getLatitude()) || StringUtils.isBlank(bean.getLongitude())){
            return ResultMsgBean.successResponse();
        }
        List<String> idList = Arrays.asList(bean.getOrderIds().split(","));
        if (CollectionUtils.isEmpty(idList)) {
            return ResultMsgBean.failResponse(ResultMsgBean.ERROR, "订单列表为空");
        }
        //保存订单列表
        orderPositionsService.savePositions(bean, idList);
        return ResultMsgBean.successResponse();
    }


}
