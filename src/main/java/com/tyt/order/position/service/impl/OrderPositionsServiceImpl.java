package com.tyt.order.position.service.impl;

import com.tyt.order.position.bean.OrderPositionBean;
import com.tyt.order.position.service.OrderPositionsService;
import com.tyt.plat.entity.base.TytOrderPositions;
import com.tyt.plat.mapper.base.TytOrderPositionsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/03/04 13:55
 */
@Service
public class OrderPositionsServiceImpl implements OrderPositionsService {

    private static final String POSITION_TYPE = "手机定位";

    @Autowired
    private TytOrderPositionsMapper orderPositionsMapper;

    @Override
    public void savePositions(OrderPositionBean bean,List<String> orderIds) {
        for (String orderId : orderIds) {
            TytOrderPositions position = TytOrderPositions.builder()
                    .orderId(Long.parseLong(orderId))
                    .userId(bean.getUserId())
                    .latitude(bean.getLatitude())
                    .longitude(bean.getLongitude())
                    .type(POSITION_TYPE)
                    .createTime(new Date())
                    .modifyTime(new Date())
                    .build();
            orderPositionsMapper.insert(position);
        }
    }
}
