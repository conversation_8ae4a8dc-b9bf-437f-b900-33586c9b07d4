package com.tyt.adposition.bean;

import lombok.Data;

/**
* 广告位货源配置
* <AUTHOR>
* @since 2024/4/15 17:55
*/
@Data
public class AdPositionGoodsConfigResp {

    /**
     * 广告位位置:36货源详情页顶部   37订金支付页顶部  38电议弹窗  55货源发布弹窗
     */
    private Integer adPosition;

    /**
     * 货源价格类型 0全部  1有价  2无价  3一口价
     */
    private Integer priceType;

    /**
     * 货源类型 0全部   1普通货源   2优车货源
     */
    private Integer goodsType;

    /**
     * 货主身份 0全部   1个人货主    2企业货主   3货站   4物流公司
     */
    private Integer ownerIdentity;

    /**
     * 开票状态 0不限   1开票    2非开票
     */
    private Integer invoiceTransport;

    /**
     * 订金模式 0不限   1可退    2不可退
     */
    private Integer refundFlag;

    /**
     * 发货方式 0不限   1优车发货  2普通发货
     */
    private Integer deliveryFlag;

    /**
     * 抽佣标识：0-不限；1-有标签；2-无标签；
     */
    private Integer tecServiceFeeFlag;

}
