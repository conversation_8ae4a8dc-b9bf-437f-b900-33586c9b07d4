package com.tyt.adposition.controller;

import com.gexin.fastjson.JSON;
import com.tyt.adposition.bean.AdPositionRuleBean;
import com.tyt.adposition.bean.AdvertInfoListResp;
import com.tyt.adposition.service.AdPositionService;
import com.tyt.adposition.service.TytStartAdvertService;
import com.tyt.adposition.service.impl.AdPositionServiceImpl;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.*;
import com.tyt.plat.vo.ad.AdPositionGoodsSearchVo;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 广告位
 */
@Controller
@RequestMapping("/plat/adposition/")
public class AdPositionController extends BaseController {

    @Resource(name = "adPositionService")
    private AdPositionService adPositionService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytStartAdvertService")
    private TytStartAdvertService tytStartAdvertService;

    /**
     * 车方广告位如下
     * 2 信息费支付完成
     * 4 找货大厅bannner+文字链接
     * 11 支付信息费页面
     * 12 找货大厅浮窗
     * 20 车APP首页品宣图
     * 21 车APP首页车辆买卖
     * 22 车APP首页保险服务
     * 23 车APP首页板车司机
     * 24 车APP首页板车司机(更多)
     * 25 车APP首页运输服务
     * 26 车APP首页车辆维修
     * 27 车APP首页轮播图
     * 28 车APP首页保险服务新(2帧)
     * 29 车APP首页运输服务(新-3帧)
     * 30 车APP协议修订公示
     * 31 车APP信卡片弹层
     * 32 车APP信息费banner
     * 33 车APP源详情banner
     * 36 车APP货源详情页顶部
     * 37 车APP订金支付页顶部
     * 38 车APP电议弹窗
     * 39 车APP启动页
     */
    public static final int[] CAR_AD_POSITION = {2, 4, 11, 12, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35,36,37,38,39};
    /**
     * 货方广告位如下
     * 3 发货页文字链
     * 40 货APP首页品宣图
     * 41 货APP首页车辆买卖
     * 42 货APP首页保险服务
     * 43 货APP首页板车司机
     * 44 货APP首页板车司机(更多)
     * 45 货APP首页运输服务
     * 46 货APP首页车辆维修
     * 47 货APP首页轮播图
     * 48 货APP首页保险服务新(2帧)
     * 50 货APP协议修订公示
     * 51 货APP信卡片弹层
     * 52 货APP信息费banner
     * 53 货APP货源详情banner
     * 54 货APP货源保障计划弹窗
     * 55 货APP货源发布成功弹窗
     * 56 货APP启动页
     */
    public static final int[] GOODS_AD_POSITION = {3, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54,55,56, 57, 58,62};

    /**
     * 49 PC货源详情页banner
     */
    public static final int[] PC_AD_POSITION = {49};

    /**
     * 获取广告位信息
     *
     * @param request
     * @param response
     */
    @RequestMapping("/adlist")
    @ResponseBody
    public ResultMsgBean adlist(BaseParameter baseParameter,
                                @RequestParam(value = "showPosition", required = true) Integer showPosition,
                                HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        Long userId = baseParameter.getUserId();
        try {
            List<AdPosition> list = adPositionService.getAdList(showPosition, userId);
            rm.setData(list);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * 车货版本分离获取广告位信息
     *
     * @param showPositionsString 位置集合
     * @return
     */
    @RequestMapping("/adlistForApp")
    @ResponseBody
    public ResultMsgBean adlistForApp(BaseParameter baseParameter, String showPositionsString) {
        //将请求参数showPositions 定义为逗号分割的字符串
        int[] showPositions = new int[0];
        if (!Strings.isNullOrEmpty(showPositionsString)) {
            String[] split = showPositionsString.split(",");
            showPositions = new int[split.length];
            for (int i = 0; i < split.length; i++) {
                showPositions[i] = Integer.parseInt(split[i]);
            }
        }
        ResultMsgBean rm = new ResultMsgBean();
        Long userId = baseParameter.getUserId();
        String ticket = baseParameter.getTicket();
        String clientSign = baseParameter.getClientSign();
        User user = null;
        String province = "无";
        Boolean isLogin = false;
        //判断用户是否登录
        if (userId != null && userId != 0 && !Strings.isNullOrEmpty(ticket)) {
            isLogin = true;
            try {
                user = userService.getByUserId(userId);
                province = user != null && org.apache.logging.log4j.util.Strings.isNotEmpty(user.getProvince()) ?
                        user.getProvince().replace("省", "") : "无";
            } catch (Exception e) {
                logger.error("服务器异常 获取用户信息有误", e);
            }
        }
        //根据请求参数获取请求位置的集合
        if (showPositions == null || showPositions.length == 0) {
            //当showPositions为空时 且 当前登录的为车app时设置获取首页车app图片
            if (String.valueOf(Constant.ClientSignEnum.ANDROID_CAR.code).equals(clientSign) || (String.valueOf(Constant.ClientSignEnum.IOS_CAR.code).equals(clientSign))) {
                showPositions = CAR_AD_POSITION;
            } else if (String.valueOf(Constant.ClientSignEnum.ANDROID_GOODS.code).equals(clientSign) || (String.valueOf(Constant.ClientSignEnum.IOS_GOODS.code).equals(clientSign))) {
                showPositions = GOODS_AD_POSITION;
            }else if (String.valueOf(Constant.ClientSignEnum.PC.code).equals(clientSign) || (String.valueOf(Constant.ClientSignEnum.PC_GOODS.code).equals(clientSign))) {
                showPositions = PC_AD_POSITION;
            } else {
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("基础参数错误");
                return rm;
            }
        }
        try {
            Map<String, Object> adListForApp = adPositionService.getAdListForApp(showPositions, isLogin, clientSign, user, province);
            rm.setData(adListForApp);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    private String getUserProvince(User user){

        String province = "无";

        if(user == null){
            return province;
        }

        String userProvince = user.getProvince();

        if(StringUtils.isNotBlank(userProvince)){
            province = userProvince.replace("省", "");
        }
        return province;
    }

    /**
     * 找货大厅feed流广告位
     *
     * @return ResultMsgBean
     */
    @GetMapping("/adlistForGoodsSearch.action")
    @ResponseBody
    public ResultMsgBeanV2<AdPositionGoodsSearchVo> adlistForGoodsSearch(BaseParameter baseParameter) {

        int adPosId = AdPositionServiceImpl.AD_POSITION_GOODS_SEARCH;
        int[] showPositions = {adPosId};

        Long userId = baseParameter.getUserId();
        String ticket = baseParameter.getTicket();
        String clientSign = baseParameter.getClientSign();
        User user = null;
        String province = "无";
        Boolean isLogin = false;
        //判断用户是否登录
        if (userId != null && userId != 0 && !Strings.isNullOrEmpty(ticket)) {
            isLogin = true;
            try {
                user = userService.getByUserId(userId);
                province = this.getUserProvince(user);
            } catch (Exception e) {
                logger.error("服务器异常 获取用户信息有误", e);
            }
        }

        try {
            Map<String, Object> adListForApp = adPositionService.getAdListForApp(showPositions, isLogin, clientSign, user, province);

            List<AdPosition> newList = new ArrayList<>();
            if(MapUtils.isNotEmpty(adListForApp)){
                newList = (List<AdPosition>)adListForApp.get(adPosId + "");
            }

            AdPositionGoodsSearchVo adPositionGoodsSearchVo = new AdPositionGoodsSearchVo();
            adPositionGoodsSearchVo.setAdPositionList(newList);
            String interval = tytConfigService.getStringValue("feed_ad_position_interval", "3,8");
            String[] intervalConfig = interval.split(",");
            adPositionGoodsSearchVo.setFirstInterval(Integer.parseInt(intervalConfig[0]));
            adPositionGoodsSearchVo.setLastInterval(Integer.parseInt(intervalConfig[1]));

            return ResultMsgBeanV2.successResponse(adPositionGoodsSearchVo);

        } catch (Exception e) {
            return ResultMsgBeanV2.failResponse(e);
        }
    }

    /**
     * 获取广告位简略版本(供H5所用)
     * @param showPosition 位置
     * @return
     */
    @GetMapping("/adListForH5.action")
    @ResponseBody
    public ResultMsgBean adListForH5(BaseParameter baseParameter,
                                     @RequestParam(value = "showPosition") Integer showPosition) throws Exception {
        ResultMsgBean rm = new ResultMsgBean();
        Long userId = baseParameter.getUserId();
        //根据请求参数获取请求位置的集合
        if (null==showPosition||showPosition<=0||userId==null||userId<=0) {
            rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
            rm.setMsg("基础参数错误");
            return rm;
        }
        try {
            Map<String, Object> adListForApp = adPositionService.getAdListForH5(showPosition);
            rm.setData(adListForApp);
        } catch (Exception e) {
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            logger.error("接口adListForH5 服务器异常", e);
        }
        return rm;
    }


    /**
     * 获取广告位信息
     *
     * @param request
     * @param response
     */
    @RequestMapping("/adlist.action")
    @ResponseBody
    public ResultMsgBean adlistForH5(BaseParameter baseParameter,
                                     @RequestParam(value = "showPosition", required = true) Integer showPosition,
                                     HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean bean = new ResultMsgBean();
        Long userId = baseParameter.getUserId();
        if (userId == null) {
            bean.setCode(ReturnCodeConstant.ERROR);
            bean.setMsg("参数不正确");
            return bean;
        }
        bean = this.adlist(baseParameter, showPosition, request, response);
        HashMap<String, Object> map = new HashMap<String, Object>();
        Object list = bean.getData();
        String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.com/");
        map.put("list", list);
        map.put("tytServerPictureUrl", tytServerPictureUrl);
        bean.setData(map);
        return bean;
    }

    /**
     * 获取广告位信息
     *
     * @param request
     * @param response
     */
    @RequestMapping("/startAdList")
    @ResponseBody
    public ResultMsgBean getStartAdList(BaseParameter baseParameter,
                                        @RequestParam(value = "showPosition", required = true) Integer showPosition,
                                        HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            List<TytStartAdvert> list = tytStartAdvertService.getStartAdList(showPosition);
            rm.setData(list);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * 获取广告位信息
     */
    @RequestMapping("/advertInfoList")
    @ResponseBody
    public ResultMsgBean advertInfoList(BaseParameter parameter) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");

        try {
            final String sign = parameter.getClientSign();
            if (!NumberUtils.isNumber(sign)) {
                logger.error("clientSign error :{}", JSON.toJSONString(parameter));
                rm.setCode(ReturnCodeConstant.BASIC_PARAMETER_ERROR);
                rm.setMsg("clientSign 参数错误");
                return rm;
            }

            if (Constant.ClientSignEnum.isCar(Integer.parseInt(sign))) {
                final Date date = new Date();
                final List<TytStartAdvert> adverts = Stream.of(tytStartAdvertService.getStartAdList(2), tytStartAdvertService.getStartAdList(3))
                        .flatMap(List::stream)
                        .filter(a -> date.before(a.getEndTime()))
                        .collect(Collectors.toList());
                rm.setData(new AdvertInfoListResp(date, adverts));
            }


        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 检测该广告位是否符合货源条件限制
     *
     * @param adPositionIdStr 36 车APP货源详情页顶部 37 车APP订金支付页顶部 38 车APP电议弹框 55 货APP货源发布弹窗 53 货app货源详情页banner
     * @param goodsId      货源ID
     * @return ResultMsgBean
     */
    @RequestMapping("/checkShow")
    @ResponseBody
    public ResultMsgBean checkShow(@RequestParam(value = "adPositionIdStr", required = true) String adPositionIdStr,
                                   @RequestParam(value = "goodsId", required = true) Long goodsId ) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            //将请求参数showPositions 定义为逗号分割的字符串
            Long[] adPositionIds = new Long[0];
            if (!Strings.isNullOrEmpty(adPositionIdStr)) {
                String[] split = adPositionIdStr.split(",");
                adPositionIds = new Long[split.length];
                for (int i = 0; i < split.length; i++) {
                    adPositionIds[i] = Long.valueOf(split[i]);
                }
            }
            List<Long> adPositions = adPositionService.checkShow(adPositionIds, goodsId);
            logger.info("adPositionService checkShow adPositionIds:{},goodsId:{} return adPositions:{}",JSON.toJSONString(adPositionIds),goodsId,adPositions);
            rm.setData(adPositions);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }

    /**
     * 获取条件广告位
     *
     * @param positionId 位置
     * @return rm
     */
    @RequestMapping("/getAdForCondition")
    @ResponseBody
    public ResultMsgBean getAdForCondition(BaseParameter baseParameter, Integer positionId) {
        if (Objects.isNull(positionId) || Objects.isNull(baseParameter.getUserId())) {
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "参数为空");
        }
        try {
            int[] showPositions = new int[]{positionId};
            User user = userService.getByUserId(baseParameter.getUserId());
            Map<String, Object> adListForApp = adPositionService.getAdListForApp(showPositions, true, baseParameter.getClientSign(), user, user.getProvince());
            if (Objects.isNull(adListForApp)) {
                return ResultMsgBean.successResponse();
            }
            Object obj = adListForApp.get(positionId.toString());
            List<AdPositionBean> adPositions = null;
            if (obj instanceof List) {
                adPositions = (List<AdPositionBean>) obj;
            } else {
                return ResultMsgBean.successResponse();
            }
            for (AdPositionBean adPosition : adPositions) {
                if (StringUtils.isBlank(adPosition.getShowRuleContent())) {
                    continue;
                }
                AdPositionRuleBean ruleBean = JSON.parseObject(adPosition.getShowRuleContent(), AdPositionRuleBean.class);
                boolean checkShowRule = adPositionService.checkShowRule(ruleBean, user.getId(), adPosition.getId());
                if (checkShowRule) {
                    return ResultMsgBean.successResponse(adPosition);
                }
            }
            return ResultMsgBean.successResponse();
        } catch (Exception e) {
            logger.error("获取条件广告位异常，", e);
            return ResultMsgBean.failResponse(ReturnCodeConstant.ERROR, "获取广告位失败");
        }

    }





    /**
     * 检测该广告位是否符合货源条件限制
     *
     * @param adPositionIdStr 3 发货也文字链
     * @param invoice 开票类型 0：不开  1：开
     * @param drivice  是否选择发优车 0:普通  1：优车
     * @return ResultMsgBean
     */
    @RequestMapping("/checkShowNew")
    @ResponseBody
    public ResultMsgBean checkShowNew(@RequestParam(value = "adPositionIdStr", required = true) String adPositionIdStr,
                                      @RequestParam(value = "userId", required = true) Long userId,
                                      @RequestParam(value = "invoice", required = true)Integer invoice,Integer drivice ) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            //将请求参数showPositions 定义为逗号分割的字符串
            Long[] adPositionIds = new Long[0];
            if (!Strings.isNullOrEmpty(adPositionIdStr)) {
                String[] split = adPositionIdStr.split(",");
                adPositionIds = new Long[split.length];
                for (int i = 0; i < split.length; i++) {
                    adPositionIds[i] = Long.valueOf(split[i]);
                }
            }
            List<Long> adPositions = adPositionService.checkShowNew(adPositionIds, invoice,drivice,userId);
            logger.info("adPositionService checkShow adPositionIds:{},invoice:{},drivice:{}, return adPositions:{}",JSON.toJSONString(adPositionIds),invoice,drivice,adPositions);
            rm.setData(adPositions);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }
}
