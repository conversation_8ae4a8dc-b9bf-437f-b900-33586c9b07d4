package com.tyt.adposition;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.tyt.enums.LabelEnum;
import com.tyt.model.AdPosition;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by duanwc on 2019/3/13.
 */
public class Main {
    public static void main(String[] args) throws Exception {
        ResultMsgBean rm = new ResultMsgBean();
        List<AdPosition> list = new ArrayList<>();
        AdPosition position = new AdPosition();
        position.setId(0L);
        position.setTitle("这是一个后端管理的标题");
        position.setShowPosition(2); // 这里是广告位显示的位置
        position.setPicUrl("http://att.gamefy.cn/files/201506/att143322006298620.jpg");
        position.setSmallPicUrl("http://att.gamefy.cn/files/201506/att143322006298620.jpg");
        position.setPicLinkUrl("http://www.baidu.com");
        position.setWordContent("这里是一个文字链内容，后台设置就有，不设置就没有");
        position.setWordLinkUrl("文字链的连接地址，同WordContent解释");
        position.setRegIdentity(0); // 这是用户的身份
        position.setUserGroup("0"); // 用户分众类型，参见tyt_source表user_group
        position.setStatus(1); //状态，0-停用，1-启用
        position.setSort(1); // 优先级，数字越大越靠前
        position.setOperaUserId(3L);//后台操作人ID
        position.setOperaUserName("操作人名称，前端无需关注");
        position.setOperaUserIp("操作人IP，前端无需关注");
        position.setCtime(new Date()); // 创建时间
        position.setMtime(TimeUtil.parseDateString("2019-03-05 11:00:01")); // 更新时间
        position.setIsValid(1); // 前端无需关注，是否删除，0-无效，1-有效

        AdPosition position1 = new AdPosition();
        AdPosition position2 = new AdPosition();
        AdPosition position3 = new AdPosition();
        AdPosition position4 = new AdPosition();
        AdPosition position5 = new AdPosition();
        BeanUtils.copyProperties(position, position1);
        BeanUtils.copyProperties(position, position2);
        BeanUtils.copyProperties(position, position3);
        BeanUtils.copyProperties(position, position4);
        BeanUtils.copyProperties(position, position5);
        position1.setId(1L);
        position2.setId(2L);
        position3.setId(3L);
        position4.setId(4L);
        position5.setId(5L);

        position1.setRegIdentity(0);
        position2.setRegIdentity(0);
        position3.setRegIdentity(0);
        position4.setRegIdentity(0);
        position5.setRegIdentity(0);

        position1.setUserGroup("4010,2,1020,2030,3020");
        position2.setUserGroup("4010,2,1010,2010,3010");
        position3.setUserGroup("0");
        position4.setUserGroup("0");
        position5.setUserGroup("0");

        position1.setSort(1);
        position2.setSort(2);
        position3.setSort(1);
        position4.setSort(3);
        position5.setSort(1);

        position1.setMtime(TimeUtil.parseDateString("2019-03-06 11:00:01")); // 更新时间
        position2.setMtime(TimeUtil.parseDateString("2019-03-07 11:00:01")); // 更新时间
        position3.setMtime(TimeUtil.parseDateString("2019-03-12 11:00:01")); // 更新时间
        position4.setMtime(TimeUtil.parseDateString("2019-03-09 11:00:01")); // 更新时间
        position5.setMtime(TimeUtil.parseDateString("2019-03-09 11:00:02")); // 更新时间


        list.add(position);
        list.add(position1);
        list.add(position2);
        list.add(position3);
        list.add(position4);
        list.add(position5);

        boolean isLogin = true;

        /**
         * 以下模块单独处理，不受任何分众限制，包括登录状态
         *
         * 2-信息费支付完成banner
         * 3-发货页文字链
         * 7-板车模块
         * 8-保险模块
         */
        int[] intPosition = {2, 3, 7, 8};
        int showPosition = 1;
        Integer regIdentity = 2;
        Integer carInt = 1010;
        Integer goodInt = 2010;
        Integer deliverInt = 3010;
        List<AdPosition> newList = new ArrayList<>();
        if (ArrayUtils.contains(intPosition, showPosition)) { // 不进行分众
            newList = list;
        } else { // 进行分众
            if(!isLogin) { //未登录的单独进行处理
                for (AdPosition ad : list) {
                    int[] intArr = LabelEnum.stringIdsConvertInts(ad.getUserGroup());
                    boolean unLogin = ArrayUtils.contains(intArr, LabelEnum.未登录.getLabelGroupId());
                    if (unLogin) {
                        newList.add(ad);
                    }
                }
            } else {
                for (AdPosition ad : list) {
                    int[] intArr = LabelEnum.stringIdsConvertInts(ad.getUserGroup());
                    boolean br = ArrayUtils.contains(intArr, regIdentity) || ArrayUtils.contains(intArr, LabelEnum.注册身份不限.getLabelGroupId());
                    boolean bc = ArrayUtils.contains(intArr, carInt) || ArrayUtils.contains(intArr, LabelEnum.车方不限.getLabelGroupId());
                    boolean bg = ArrayUtils.contains(intArr, goodInt) || ArrayUtils.contains(intArr, LabelEnum.货方不限.getLabelGroupId());
                    boolean bd = ArrayUtils.contains(intArr, deliverInt) || ArrayUtils.contains(intArr, LabelEnum.货次卡不限.getLabelGroupId());
                    if (br && bc && bg && bd) {
                        newList.add(ad);
                    }
                }
            }
        }

        List<AdPosition> resultList = newList.stream().sorted( // 多条件排序
                Comparator.comparing((AdPosition a) -> (Integer) a.getSort())
                        .thenComparing((AdPosition a) -> (Date) a.getMtime()).reversed()
        ).limit(4).collect(Collectors.toList());

        rm.setData(resultList);

        String jsonString = JSON.toJSONString(rm, SerializerFeature.PrettyFormat);
        System.out.println(jsonString);


    }
}
