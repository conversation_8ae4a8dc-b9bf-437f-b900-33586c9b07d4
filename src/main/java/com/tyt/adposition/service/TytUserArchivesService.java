package com.tyt.adposition.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytUserArchives;
import com.tyt.model.TytUserBuyGoods;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

public interface TytUserArchivesService extends BaseService<TytUserArchives, Long> {

    TytUserArchives getByUserId(Long userId);

    boolean checkArchivesIsExist(Long userId);

    void updateArchivesValidGoodsPermission(TytUserBuyGoods delayedGoods);

    void updateUserArchivesGoodsPermission(Long userId, String goodsVipLabel, Timestamp endTime);
}
