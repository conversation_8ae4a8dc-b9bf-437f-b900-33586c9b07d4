package com.tyt.adposition.service;

import com.tyt.adposition.bean.AdPositionRuleBean;
import com.tyt.model.AdPosition;
import com.tyt.model.User;

import java.util.List;
import java.util.Map;

/**
 * Created by duanwc on 2019/3/14.
 */
public interface AdPositionService {
    /**
     * 获取广告位列表
     * @param position 位置
     * @param userId 用户ID
     * @return 此用户对应的广告
     */
    List<AdPosition> getAdList(Integer position, Long userId);

    /**
     * 根据adId  获取该广告位ID下用户集合
     * @param adId
     * @return
     */
    Long[]  getAdPositionAdIds(Long adId);

    /**
     * 车货版本分离获取广告位信息
     * @param showPositions 位置集合
     * @param clientSign 终端标识
     * @param user 用户信息
     * @return 此用户对应的广告
     */
    Map<String, Object> getAdListForApp(int[] showPositions, Boolean isLogin, String clientSign, User user,String province) throws Exception;

    /**
     * H5页面简略版获取广告位
     * @param showPosition
     * @return
     * @throws Exception
     */
    Map<String, Object> getAdListForH5(Integer showPosition) throws Exception;

    /**
     * 判断该用户是否已参与该广告活动
     * @param adId 广告ID
     * @param userId userId
     * @return true：已参与，false：未参与或该广告活动并未配置此用户
     */
    boolean getAdPositionUesrDataByAdIdAndUserId(Long adId, Long userId);


    /**
     * 获取保障广告活动的广告ID
     * @return 保障广告活动的广告ID
     */
    Long getGuaranteeActivityId();

    /**
     * 参加货源保障活动
     * @param userId userId
     */
    void joinGuaranteeActivity(Long userId);

    /**
     * 检测广告位是否符合货源条件要求
     * @param adPositionIds
     * @param goodsId
     * @return List<Long>
     */
    List<Long> checkShow(Long[] adPositionIds,Long goodsId);

    /**
     * 检测展示规则
     * @param ruleBean 规则信息
     * @param userId 用户id
     * @param positionId 广告位id
     * @return boolean
     * @throws Exception
     */
    boolean checkShowRule(AdPositionRuleBean ruleBean, Long userId, Long positionId) throws Exception;

    List<Long> checkShowNew(Long[] adPositionIds, Integer invoice, Integer drivice,Long userId);
}
