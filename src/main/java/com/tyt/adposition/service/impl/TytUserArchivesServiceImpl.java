package com.tyt.adposition.service.impl;

import com.tyt.adposition.service.TytUserArchivesService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.goods.service.GoodsService;
import com.tyt.model.TytGoods;
import com.tyt.model.TytUserArchives;
import com.tyt.model.TytUserBuyGoods;
import com.tyt.util.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

@Service("tytUserArchivesService")
public class TytUserArchivesServiceImpl extends BaseServiceImpl<TytUserArchives, Long> implements TytUserArchivesService {

    private Logger logger = LoggerFactory.getLogger(TytUserArchivesServiceImpl.class);

    @Resource(name = "tytUserArchivesDao")
    public void setBaseDao(BaseDao<TytUserArchives, Long> tytUserArchivesDao) {
        super.setBaseDao(tytUserArchivesDao);
    }

    @Autowired
    private GoodsService goodsService;

    @Override
    public TytUserArchives getByUserId(Long userId){
        String sql = "SELECT * FROM `tyt_user_archives` WHERE user_id=?";
        List<TytUserArchives> userArchives = this.getBaseDao().search(sql,new Object[]{userId},1,1);
        if (userArchives!=null && userArchives.size()>0){
            return userArchives.get(0);
        }
        return null;
    }

    @Override
    public boolean checkArchivesIsExist(Long userId){
        String sql = "SELECT COUNT(*) FROM `tyt_user_archives` WHERE user_id=?";
        BigInteger count = this.getBaseDao().query(sql, new Object[] {userId});
        if (count!=null && count.intValue()>0) {
            return true;
        }
        return false;

    }

    @Override
    public void updateArchivesValidGoodsPermission(TytUserBuyGoods delayedGoods) {
        TytGoods goods = goodsService.getById(delayedGoods.getGoodsId());
        if (goods == null){
            return;
        }
        Timestamp startTime = TimeUtil.getTimeStamp();
        Timestamp endTime = getEndTime(goods, startTime);
        updateUserArchivesGoodsPermission(delayedGoods.getUserId(), "货会员", endTime);
    }

    @Override
    public void updateUserArchivesGoodsPermission(Long userId, String goodsVipLabel, Timestamp endTime){
        String sql = "UPDATE `tyt_user_archives` SET good_vip_label =?, goodvip_due_date =? WHERE user_id =? ";
        this.getBaseDao().executeUpdateSql(sql, new Object[] { goodsVipLabel, endTime, userId});
    }



    private Timestamp getEndTime(TytGoods goods, Timestamp startTime) {
        //商品有效期
        Integer effectiveTime = goods.getEffectiveTime();
        //有效期单位 （day:天; month:月;year:年;boolean:布尔）
        String unit = goods.getUnit();
        //有效期--结束时间
        Timestamp endTime = null;
        if ("year".equals(unit)) {
            endTime = TimeUtil.stampAddYear(startTime, effectiveTime);
        } else if ("month".equals(unit)) {
            endTime = TimeUtil.stampAddMonth(startTime, effectiveTime);
        } else if ("day".equals(unit)) {
            endTime = TimeUtil.stampAdd(startTime, effectiveTime);
        }
        return endTime;
    }
}
