package com.tyt.adposition.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.gexin.fastjson.JSON;
import com.tyt.acvitity.service.TransportOrdersRiskService;
import com.tyt.adposition.bean.AdPositionGoodsConfigResp;
import com.tyt.adposition.bean.AdPositionRuleBean;
import com.tyt.adposition.enums.ConditionContentEnum;
import com.tyt.adposition.enums.DwsIdentityTypeEnum;
import com.tyt.adposition.enums.UserTypeEnum;
import com.tyt.adposition.enums.VipStatusEnum;
import com.tyt.adposition.service.AdPositionService;
import com.tyt.adposition.service.TytUserArchivesService;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.deposit.entity.base.TytDepositAuthorization;
import com.tyt.deposit.mapper.base.TytDepositAuthorizationMapper;
import com.tyt.enums.LabelEnum;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetail;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.*;
import com.tyt.plat.entity.base.TytAdPositionGoodsConfig;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.TytTransportTecServiceFee;
import com.tyt.plat.entity.base.TytUserRecord;
import com.tyt.plat.enums.TransportBusinessTypeEnum;
import com.tyt.plat.mapper.base.TytTransportTecServiceFeeMapper;
import com.tyt.plat.service.base.TytUserRecordService;
import com.tyt.plat.mapper.base.TytAdPositionGoodsConfigMapper;
import com.tyt.plat.mapper.base.TytExcellentGoodsCardUserDetailMapper;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.dao.TransportMainDao;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.bean.UserIdentityLabelBean;
import com.tyt.user.enums.UserLabelTypeEnum;
import com.tyt.user.service.TytUserSubService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSourceUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service("adPositionService")
public class AdPositionServiceImpl extends BaseServiceImpl<AdPosition, Long>
        implements AdPositionService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytUserSubService")
    private TytUserSubService userSubService;

    @Resource(name = "tytUserArchivesService")
    private TytUserArchivesService userArchivesService;

    @Autowired
    private TransportMainDao transportMainDao;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private TytUserRecordService tytUserRecordService;

    @Autowired
    private TransportOrdersRiskService transportOrdersRiskService;

    @Autowired
    private TytDepositAuthorizationMapper tytDepositAuthorizationMapper;

    @Autowired
    private TytExcellentGoodsCardUserDetailMapper tytExcellentGoodsCardUserDetailMapper;

    @Autowired
    private TytAdPositionGoodsConfigMapper tytAdPositionGoodsConfigMapper;

    @Autowired
    private TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    private static volatile String UPDATE_FLAG = "NO_FLAG";

    private final String CODE = "ad_position_";

    @Resource(name = "adPositionDao")
    public void setBaseDao(BaseDao<AdPosition, Long> adPositionLongBaseDao) {
        super.setBaseDao(adPositionLongBaseDao);
    }

    /**
     * 广告位是否长期展示 1 长期展示  2 定制时间
     */
    public static final Integer LONG_TERM_DISPLAY = 1;

    public static final Integer CUSTOM_TIME_DISPLAY = 2;

    public static final String REDIS_AD_POSITION_KEY = "ad:position:";

    public static final String REDIS_AD_POSITION_FLAG_KEY = "ad:position:flag:key";

    /**
     * 不需要分众的广告位如下
     * 21 车APP首页车辆买卖
     * 22 车APP首页保险服务
     * 23 车APP首页板车司机
     * 24 车APP首页板车司机(更多)
     * 25 车APP首页运输服务
     * 26 车APP首页车辆维修
     * 28 车APP首页保险服务新(2帧)
     * 29 车APP首页运输服务(新-3帧)
     * 30 车APP协议修订公示
     * 41 货APP首页车辆买卖
     * 42 货APP首页保险服务
     * 43 货APP首页板车司机
     * 44 货APP首页板车司机(更多)
     * 45 货APP首页运输服务
     * 46 货APP首页车辆维修
     * 48 货APP首页保险服务新(2帧)
     * 50 货APP协议修订公示
     */
    public static final int[] NO_FZ_POSITION = {21, 22, 23, 24, 25, 26, 28, 29, 30, 41, 42, 43, 44, 45, 46, 48, 50};
    /**
     * 需要分众的广告位如下
     * 2 信息费支付完成
     * 3 发货页文字链
     * 4 找货大厅bannner+文字链接
     * 11 支付信息费页面
     * 12 找货大厅浮窗
     * 20 车APP首页品宣图
     * 27 车APP首页轮播图
     * 31 车APP信卡片弹层
     * 32 车APP信息费banner
     * 33 车APP源详情banner
     * 36 车APP货源详情页顶部
     * 37 车APP订金支付页顶部
     * 38 车APP电议弹窗
     * 39 车APP启动页
     * 40 货APP首页品宣图
     * 47 货APP首页轮播图
     * 49 PC货源详情页banner
     * 51 货APP信卡片弹层
     * 52 货APP信息费banner
     * 53 货APP货源详情banner
     * 54 货源保障计划弹窗
     * 55 货APP货源发布成功弹窗
     * 56 货APP启动页
     */
    public static final int[] FZ_POSITION = {2, 3, 4, 11, 12, 20, 27, 31, 32, 33, 40, 47, 49,
            51, 52, 53, 54, 35, 36, 37, 38, 39, 55, 56, 57, 58, 59, 60, 61, 62};


    private static final int AD_POSITION_DIAN_YI = 38;

    public static final int AD_POSITION_GOODS_SEARCH = 61;

    /**
     * 有货源条件限制的广告位
     * 36 车APP货源详情页顶部
     * 37 车APP订金支付页顶部
     * 38 车APP电议弹窗
     * 55 货APP货源发布成功弹窗
     */
    public static final int[] GOODS_CONFIG_FZ_POSITION = {36, 37, 38, 55,3,53};
    /**
     * 销售一级审核身份 车方类型
     */
    public static String[] carSide = {"6", "7", "8"};

    /**
     * 销售一级审核身份 货方类型
     */
    public static String[] cargoSide = {"1", "2", "3"};

    public static Map<String, List<AdPosition>> cacheMap = new HashMap<>();

    {
        this.adPositionCommend();
    }

    /**
     * 更新本地缓存数据，广告推广位
     */
    private void adPositionCommend() {

        ScheduledThreadPoolExecutor exec = new ScheduledThreadPoolExecutor(1);
        /**
         * 创建并执行一个在给定初始延迟后首次启用的定期操作，后续操作具有给定的周期；<br/>
         * 也就是将在 initialDelay 后开始执行，然后在initialDelay+period 后执行，<br/>
         * 接着在 initialDelay + 2 * period 后执行，依此类推。
         * 暂定10秒执行一次
         */
        //暂定10秒 判断 flag 是否修改
        exec.scheduleAtFixedRate(() -> {
            try {

                String flagKey = Optional.ofNullable(RedisUtil.get(REDIS_AD_POSITION_FLAG_KEY)).orElse("");
                if (UPDATE_FLAG.equals(flagKey)) {
                    return;
                }

                logger.info("ad position update 当前flag:【{}】 修改后flag:【{}】", UPDATE_FLAG, flagKey);

                List<TytSource> adPositions = TytSourceUtil.getSourceList("ad_position");

                if (CollectionUtils.isNotEmpty(adPositions)) {
                    cacheMap = adPositions.stream()
                            .map(source -> {
                                String cacheKey = REDIS_AD_POSITION_KEY + source.getValue();
                                List<AdPosition> list = RedisUtil.getObjectList(cacheKey);
                                return Pair.of(cacheKey, list);
                            })
                            .filter(p -> CollectionUtils.isNotEmpty(p.getValue()))
                            .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
                }
                logger.info("ad position update adlist:【{}】", JSON.toJSONString(cacheMap));
                UPDATE_FLAG = flagKey;
            } catch (Exception e) {
                logger.error("初始化广告位异常：", e);
            }

        }, 10, 10 * 1000, TimeUnit.MILLISECONDS);
    }


    @Override
    public List<AdPosition> getAdList(Integer position, Long userId) {
        /**
         * 用户身份
         * 1-货方
         * 2-车方  默认值
         */
        Integer regIdentity = LabelEnum.车方.getLabelGroupId();
        Integer carInt = LabelEnum.车方不限.getLabelGroupId();
        Integer goodInt = LabelEnum.货方不限.getLabelGroupId();
        Integer deliverInt = LabelEnum.货次卡不限.getLabelGroupId();
        boolean isLogin = false; // 是否登录
        String province = "无";
        //获取当前天 的毫秒数  例如 2022-08-03的毫秒数
        long nowTime = System.currentTimeMillis();
        if (userId != null && userId != 0) {  // 获取用户身份和层次
            // 获取用户身份
            try {
                String carVipLabel;
                String goodVipLabel;
                String deliverGoodsLabel;
                TytUserArchives userArchives = userArchivesService.getByUserId(userId);
                if (userArchives != null) {
                    carVipLabel = userArchives.getCarVipLabel(); // 车标签
                    goodVipLabel = userArchives.getGoodVipLabel(); // 货标签
                    deliverGoodsLabel = userArchives.getDeliverGoodsLabel(); // 次卡标签

                    if (StringUtils.isBlank(carVipLabel)) {
                        carVipLabel = "车方无标签";
                    }
                    if (StringUtils.isBlank(goodVipLabel)) {
                        goodVipLabel = "货方无标签";
                    }
                    if (StringUtils.isBlank(deliverGoodsLabel)) {
                        deliverGoodsLabel = "货次卡无标签";
                    }
                } else {
                    carVipLabel = "车方无标签";
                    goodVipLabel = "货方无标签";
                    deliverGoodsLabel = "货次卡无标签";
                }

                carInt = LabelEnum.getLabelGroupIdByLabel(carVipLabel);
                goodInt = LabelEnum.getLabelGroupIdByLabel(goodVipLabel);
                deliverInt = LabelEnum.getLabelGroupIdByLabel(deliverGoodsLabel);

                User user = userService.getByUserId(userId);
                if (user.getUserClass() != null) { //为null 则为默认值
                    if (ArrayUtils.contains(carSide, user.getDeliver_type_one())) {
                        regIdentity = 2; // 车方
                    } else if (ArrayUtils.contains(cargoSide, user.getDeliver_type_one())) {
                        regIdentity = 1; // 货方
                    } else {
                        regIdentity = 2; // 车方
                    }
                }
                province = user != null && Strings.isNotEmpty(user.getProvince()) ?
                        user.getProvince().replace("省", "") : "无";
                isLogin = true;
            } catch (Exception e) {
                isLogin = false;
                // 未登录时，会进入catch模块，对应当前全部身份配置数据
                logger.error("用户ID:{" + userId + "}查询异常，userId为空");
            }
        }

        // 本地缓存加载数据
        List<AdPosition> list = cacheMap.get(REDIS_AD_POSITION_KEY + position);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        /**
         * 以下模块单独处理，不受任何分众限制，包括登录状态
         *
         * 2-信息费支付完成banner
         * 3-发货页文字链
         * 4-找货大厅bannner+文字链接
         * 7-板车模块
         * 8-保险模块
         * 9-板车板块新(3帧)
         * 10-保险板块新(3帧)
         * 11-支付信息费页面
         * 12-找货大厅浮窗(货站之星)
         * 13-发货大厅浮窗（仅公共资源配置的用户显示--我的主页）
         * 14-我的页面会员活动链接
         * 30-车APP协议修订公示
         * 49-PC货源详情页banner
         * 50-货APP协议修订公示
         */
        int[] intPosition = {7, 8, 9, 10, 13, 14, 30, 50};
        int[] tsPosition = {2, 3, 4, 11, 12, 49};
        List<AdPosition> newList = new ArrayList<>();
        if (ArrayUtils.contains(intPosition, position)) { // 不进行分众
            newList = list;
        } else { // 进行分众
            if (!isLogin) { //未登录的单独进行处理
                for (AdPosition ad : list) {
                    int[] intArr = LabelEnum.stringIdsConvertInts(ad.getUserGroup());
                    boolean unLogin = ArrayUtils.contains(intArr, LabelEnum.未登录.getLabelGroupId());
                    if (unLogin) {
                        newList.add(ad);
                    }
                }
            } else {
                //针对2022-08-30 广告位版本 为兼容新老版本 对历史接口作处理
                if (ArrayUtils.contains(tsPosition, position)) {
                    for (AdPosition ad : list) {
                        int[] intArr = LabelEnum.stringIdsConvertInts(ad.getUserGroup());
                        boolean login = ArrayUtils.contains(intArr, LabelEnum.已登录.getLabelGroupId());
                        if (ArrayUtils.contains(tsPosition, position) && login) {
                            Integer showType = ad.getShowType();
                            Date startTime = ad.getStartTime();
                            Date endTime = ad.getEndTime();
                            if (showType == LONG_TERM_DISPLAY || (showType == CUSTOM_TIME_DISPLAY &&
                                    (nowTime >= startTime.getTime() && nowTime <= endTime.getTime()))) {
                                String[] StringShowProvince = new String[0];
                                if (Strings.isNotEmpty(ad.getShowProvince())) {
                                    StringShowProvince = ad.getShowProvince().split(",");
                                }
                                boolean showProvince = ArrayUtils.contains(StringShowProvince, province);
                                //定义用户是否被导入广告位
                                boolean userInAd = false;
                                //查看该活动中是否存在用户
//                                boolean userCount= getAdPositionAdIds(ad.getId(), 0L);
                                if (ad.getIsImportUser() == 1) {
                                    //查看该活动中是否导入用户
                                    userInAd = getAdPositionAdIds(ad.getId(), userId);
                                }
                                logger.info("getAdList 【province:{}】 【StringShowProvince:{}】【showProvince:{}】【userInAd:{}】", province, StringShowProvince, showProvince, userInAd);
                                if ((ad.getIsImportUser() == 1 && userInAd) || (StringShowProvince.length > 0 && showProvince) || (StringShowProvince.length == 0 && ad.getIsImportUser() == 0)) {
                                    newList.add(ad);
                                }

                            }
                        }
                    }
                } else {

                    for (AdPosition ad : list) {
                        int[] intArr = LabelEnum.stringIdsConvertInts(ad.getUserGroup());
                        boolean br = ArrayUtils.contains(intArr, regIdentity) || ArrayUtils.contains(intArr, LabelEnum.注册身份不限.getLabelGroupId());
                        boolean bc = ArrayUtils.contains(intArr, carInt) || ArrayUtils.contains(intArr, LabelEnum.车方不限.getLabelGroupId());
                        boolean bg = ArrayUtils.contains(intArr, goodInt) || ArrayUtils.contains(intArr, LabelEnum.货方不限.getLabelGroupId());
                        boolean bd = ArrayUtils.contains(intArr, deliverInt) || ArrayUtils.contains(intArr, LabelEnum.货次卡不限.getLabelGroupId());
                        if (br && bc && bg && bd) {
                            newList.add(ad);
                        }
                    }
                }
            }
        }

        List<AdPosition> resultList = newList.stream().sorted( // 多条件排序
                Comparator.comparing((AdPosition a) -> a.getSort())
                        .thenComparing((AdPosition a) -> a.getMtime()).reversed()
        ).limit(4).collect(Collectors.toList());


        return resultList;
    }


    @Override
    public Map<String, Object> getAdListForApp(int[] showPositions, Boolean isLogin, String clientSign, User user, String province) {
        //获取当前天 的毫秒数  例如 2022-08-03的毫秒数
        long nowTime = System.currentTimeMillis();
        Map<String, Object> adPositionShowMap = new HashMap<String, Object>();
        for (int i = 0; i < showPositions.length; i++) {
            int position = showPositions[i];
            List<AdPosition> list = cacheMap.get(REDIS_AD_POSITION_KEY + position);
            logger.info("getAdListForApp 【position:{}】 返回【list:{}】", position, list != null && list.size() > 0 ? JSON.toJSONString(list) : "");
            List<AdPosition> newList = new ArrayList<>();
            // 不进行分众
            if (ArrayUtils.contains(NO_FZ_POSITION, position)) {
                newList = list;
            } else {
                // 进行分众 未登录的单独进行处理
                if (!isLogin) {
                    if (list != null) {
                        for (AdPosition ad : list) {
                            int[] intArr = LabelEnum.stringIdsConvertInts(ad.getUserGroup());
                            boolean unLogin = ArrayUtils.contains(intArr, LabelEnum.未登录.getLabelGroupId());
                            if (unLogin) {
                                newList.add(ad);
                            }
                        }
                    }

                } else {
                    if (list != null) {
                        for (AdPosition ad : list) {
                            int[] intArr = LabelEnum.stringIdsConvertInts(ad.getUserGroup());
                            boolean login = ArrayUtils.contains(intArr, LabelEnum.已登录.getLabelGroupId());
                            if (ArrayUtils.contains(FZ_POSITION, position) && login) {
                                Integer showType = ad.getShowType();
                                Date startTime = ad.getStartTime();
                                Date endTime = ad.getEndTime();
                                //判断活动当前是否满足展示条件
                                if (showType == LONG_TERM_DISPLAY || (showType == CUSTOM_TIME_DISPLAY &&
                                        (nowTime >= startTime.getTime() && nowTime <= endTime.getTime()))) {
                                    boolean userInAd = false;
                                    //查看该活动中是否存在用户
                                    if (ad.getIsImportUser() == 1) {
                                        //查看该活动中是否导入用户
                                        userInAd = getAdPositionAdIds(ad.getId(), user.getId());
                                    }
                                    String[] stringShowProvince = new String[0];
                                    if (Strings.isNotEmpty(ad.getShowProvince())) {
                                        stringShowProvince = ad.getShowProvince().split(",");
                                    }
                                    boolean condition = Objects.equals(ad.getUserType(), UserTypeEnum.ALL_USER.getCode()) && ad.getGoodsIdentity().equals(DwsIdentityTypeEnum.ALL_USER.getCode().toString()) && ad.getVipStatus().equals(VipStatusEnum.ALL_USER.getCode()) && stringShowProvince.length==0;
                                    //如果用户非导入用户，则走规则
                                    boolean userIdentity = false;
                                    if (!userInAd) {
                                        userIdentity = checkUserIdentity(ad, user.getId(), clientSign, province);
                                    }
                                    logger.info("getAdListForApp 【province:{}】 【userIdentity:{}】【userInAd:{}】", province, userIdentity, userInAd);
                                    if (userInAd || (ad.getIsImportUser() == 1 && !condition && userIdentity) || (ad.getIsImportUser() == 0 && userIdentity)){
                                        Integer showPosition = ad.getShowPosition();
                                        //判断当前广告的显示位置是否为新增加的 货源保障计划广告弹窗
                                        if (showPosition != null && showPosition.equals(54)) {
                                            //判断该广告配置了该用户
                                            if (userInAd) {
                                                //已确定该用户在活动名单中，判断该用户是否已参加了活动
                                                guaranteeActivityCheck(ad, user, newList);
                                            }
                                        } else {
                                            newList.add(ad);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (newList != null) {
                logger.info("getAdListForApp newList is 【{}】", JSON.toJSONString(newList));
                //将newList的部分值 赋值给新的 需要返回的List<AdPositionBean>
                List<AdPositionBean> adPositionBeanList = new ArrayList<>();
                for (AdPosition adPosition : newList) {
                    AdPositionBean adPositionBean = new AdPositionBean();
                    BeanUtils.copyProperties(adPosition, adPositionBean);
                    adPositionBeanList.add(adPositionBean);
                }

                //新增当是带有货源条件的广告位时 可能配置多个 故把limit4 改成limit 20
                if(ArrayUtils.contains(GOODS_CONFIG_FZ_POSITION, position)){
                    // 多条件排序
                    List<AdPositionBean> resultList = adPositionBeanList.stream().sorted(
                            Comparator.comparing((AdPositionBean a) -> a.getSort())
                                    .thenComparing((AdPositionBean a) -> a.getMtime()).reversed()
                    ).limit(20).collect(Collectors.toList());

                    if(3 == position){
                        for(AdPositionBean ad : resultList){
                            TytAdPositionGoodsConfig config = tytAdPositionGoodsConfigMapper.getByAdId(ad.getId());
                            logger.info("getAdListForApp config is 【{}】",JSON.toJSONString(config));
                            if(null != config){
                                ad.setInvoiceTransport(config.getInvoiceTransport());
                                ad.setDeliverylag(config.getDeliveryFlag());
                            }
                        }
                    }
                    //将排序后的结果放入map中 统一返回
                    adPositionShowMap.put(String.valueOf(position), resultList);
                } else if(AD_POSITION_GOODS_SEARCH == position){
                    //找货大厅
                    List<AdPositionBean> resultList = adPositionBeanList.stream().sorted(
                            Comparator.comparing((AdPositionBean a) -> a.getSort())
                                    .thenComparing((AdPositionBean a) -> a.getMtime()).reversed()
                    ).limit(5).collect(Collectors.toList());
                    //将排序后的结果放入map中 统一返回
                    adPositionShowMap.put(String.valueOf(position), resultList);
                } else {
                    // 多条件排序
                    List<AdPositionBean> resultList = adPositionBeanList.stream().sorted(
                            Comparator.comparing((AdPositionBean a) -> a.getSort())
                                    .thenComparing((AdPositionBean a) -> a.getMtime()).reversed()
                    ).limit(4).collect(Collectors.toList());
                    //将排序后的结果放入map中 统一返回
                    adPositionShowMap.put(String.valueOf(position), resultList);
                }
            } else {
                adPositionShowMap.put(String.valueOf(position), newList);
            }

        }
        return adPositionShowMap;
    }


    @Override
    public Map<String, Object> getAdListForH5(Integer showPosition) {
        Map<String, Object> adPositionShowMap = new HashMap<String, Object>();
        List<AdPosition> list = cacheMap.get(REDIS_AD_POSITION_KEY + showPosition);
        logger.info("getAdListForH5 【position:{}】 返回【list:{}】", showPosition, list != null && list.size() > 0 ? JSON.toJSONString(list) : "");
        adPositionShowMap.put(String.valueOf(showPosition), list);
        return adPositionShowMap;
    }

    /**
     * 数据库查询广告位列表
     *
     * @param position 广告位的位置
     * @return
     */
    private List<AdPosition> getList(Integer position) {
        String sql = "SELECT * FROM `tyt_ad_position` where show_position = ? and status = 1 and is_valid = 1 LIMIT 100";
        final Object[] params = {
                position
        };
        List<AdPosition> adPositions = this.getBaseDao().queryForList(sql, params);
        return adPositions;
    }

    /**
     * 是否已参加保障活动
     *
     * @param ad      广告
     * @param user    用户
     * @param newList 该用户应该显示的广告list
     */
    private void guaranteeActivityCheck(AdPosition ad, User user, List<AdPosition> newList) {
        if (ad.getId() == null || user.getId() == null) {
            return;
        }
        //判断该用户是否已参与该广告活动
        boolean adPositionUesrDataByAdIdAndUserId = getAdPositionUesrDataByAdIdAndUserId(ad.getId(), user.getId());
        if (!adPositionUesrDataByAdIdAndUserId) {
            newList.add(ad);
        }
    }


    @Override
    public Long[] getAdPositionAdIds(Long adId) {
        Long[] longArr = new Long[0];
        StringBuffer sb = new StringBuffer("SELECT id, user_id as userId,ad_id as adId FROM `tyt_ad_position_user` where ad_id=:adId and is_delete = 1");
        Map<String, Object> params = new HashMap<>();
        params.put("adId", adId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("adId", Hibernate.LONG);
        List<AdPositionUser> adPositionsUserList = this.getBaseDao().search(sb.toString(), scalarMap,
                AdPositionUser.class, params);
        if (adPositionsUserList != null && adPositionsUserList.size() > 0) {
            longArr = new Long[adPositionsUserList.size()];
            for (int i = 0; i < adPositionsUserList.size(); i++) {
                longArr[i] = Long.valueOf(adPositionsUserList.get(i).getUserId());
            }
        }
        return longArr;
    }

    /**
     * @param adId
     * @param userId
     * @return boolean
     * @description 根据请求参数判断用户是否在活动中 或者活动中是否存在用户
     * <AUTHOR>
     * @date 2023/9/1 13:36
     * @version 1.0
     */
    private boolean getAdPositionAdIds(Long adId, Long userId) {
        Object[] paramArray = {adId};
        String sql = "SELECT id FROM `tyt_ad_position_user` where ad_id=? and is_delete = 1  ";
        if (userId != null && userId.intValue() > 0) {
            sql += " and user_id=? ";
            paramArray = new Object[]{adId, userId};
        }
        sql = sql + " limit 1 ";
        BigInteger c = this.getBaseDao().query(sql, paramArray);
        if (c != null && c.intValue() > 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean getAdPositionUesrDataByAdIdAndUserId(Long adId, Long userId) {
        if (adId == null || userId == null) {
            return false;
        }
        String sb = "SELECT join_type as joinType FROM `tyt_ad_position_user` where ad_id=:adId and user_id=:userId and is_delete = 1";
        Map<String, Object> params = new HashMap<>();
        params.put("adId", adId);
        params.put("userId", userId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
        scalarMap.put("joinType", Hibernate.INTEGER);
        List<AdPositionUser> adPositionsUserList = this.getBaseDao().search(sb, scalarMap, AdPositionUser.class, params);
        if (adPositionsUserList != null && adPositionsUserList.size() > 0) {
            if (adPositionsUserList.get(0).getJoinType() == 2) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Long getGuaranteeActivityId() {
        String sql = "SELECT id FROM `tyt_ad_position` where show_position = 54 and status = 1";
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
        scalarMap.put("id", Hibernate.LONG);
        List<AdPosition> adPositions = this.getBaseDao().search(sql, scalarMap, AdPosition.class, new HashMap<>());
        if (adPositions != null && adPositions.size() > 0) {
            return adPositions.get(0).getId();
        } else {
            return null;
        }
    }

    @Override
    public void joinGuaranteeActivity(Long userId) {
        Long adId = getGuaranteeActivityId();
        if (adId != null && userId != null) {
            String sql = "update tyt_ad_position_user set join_type = 2, mtime = NOW() where ad_id=:adId and user_id=:userId and is_delete = 1";
            Map<String, Object> params = new HashMap<>();
            params.put("adId", adId);
            params.put("userId", userId);
            this.getBaseDao().executeUpdateSql(sql, params);
        }
    }

    /**
     * 判断当前货物是否有符合条件的广告位配置所对应
     * (由于此接口后台配置较多 经讨论 不再使用枚举
     * 直接使用数字加备注 便于日后更好理解与修改)
     *
     * @param adPositionIds 某个广告位位置配置组合
     * @param goodsId       货源Id
     * @return List<Long>
     */
    @Override
    public List<Long> checkShow(Long[] adPositionIds, Long goodsId) {
        List<Long> adPositionShowIds = new ArrayList();
        try {
            //查看货源信息
            TransportMain transportMain = transportMainService.getTransportMainForId(goodsId);
            if (Objects.isNull(transportMain)){
                return adPositionShowIds;
            }
            Integer mainRefundFlag = transportMain.getRefundFlag();
            Integer mainExcellentGoods = transportMain.getExcellentGoods();
            int type = transportMainService.getTransportBusinessType(goodsId);
            if (type == TransportBusinessTypeEnum.automaticGoodCarPriceTransport.getCode()) {
                mainExcellentGoods = 3; //普货转优车定价货源
            }
            //发货方式
            Integer mainDeliveryFlag = transportMain.getExcellentGoods();
            //数据库:1电议 2 一口价   代码标识:(1无价 2一口价  3有价)
            Integer mainPublishType = transportMain.getPublishType();
            //当数据库类型返回为电议  且价格大于0时 定义为有价3
            if (1 == mainPublishType && Strings.isNotEmpty(transportMain.getPrice())) {
                mainPublishType = 3;
            }
            // invoice_transport(是否专票货源 0：否；1:是)
            Integer mainInvoiceTransport = transportMain.getInvoiceTransport();
            //用户自选身份  1个人货主 2企业货主 3货站 4物流公司
            Integer mainIdentity = getIdentityByUserId(transportMain.getUserId());
            logger.info("checkShow getTransportMainById 【goodsId:{}】【mainRefundFlag:{}】【mainExcellentGoods:{}】【mainPublishType:{}】【mainInvoiceTransport:{}】【mainIdentity:{}】", goodsId, mainRefundFlag, mainExcellentGoods, mainPublishType, mainInvoiceTransport, mainIdentity);
            //抽佣信息
            TytTransportTecServiceFee transportTecServiceFee = tytTransportTecServiceFeeMapper.getBySrcMsgId(transportMain.getId());
            //优车2.0标识
            boolean isGoodCarPriceTransport = false;
            TransportLabelJson transportLabelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
            if (ObjectUtil.isNotNull(transportLabelJson)
                    && ObjectUtil.isNotNull(transportLabelJson.getGoodCarPriceTransport())
                    && transportLabelJson.getGoodCarPriceTransport() == 1) {
                isGoodCarPriceTransport = true;
            }
            //获取货源信息
            for (Long adPositionId : adPositionIds) {
                AdPositionGoodsConfigResp goodsConfig = getGoodsConfigByAdPositionId(adPositionId);
                if (null != goodsConfig) {
                    Integer refundFlag = goodsConfig.getRefundFlag();
                    Integer goodsType = goodsConfig.getGoodsType();
                    Integer priceType = goodsConfig.getPriceType();
                    Integer ownerIdentity = goodsConfig.getOwnerIdentity();
                    Integer invoiceTransport = goodsConfig.getInvoiceTransport();
                    Integer deliveryFlag = goodsConfig.getDeliveryFlag();
                    Integer tecServiceFeeFlag = goodsConfig.getTecServiceFeeFlag();
                    logger.info("checkShow getGoodsConfigByAdPositionId 【adPositionId:{}】【refundFlag:{}】【goodsType:{}】【priceType:{}】【ownerIdentity:{}】【invoiceTransport:{}】", adPositionId, refundFlag, goodsType, priceType, ownerIdentity, invoiceTransport);
                    //逻辑处理是否符合要求
                    // refundFlag(0不限   1可退    2不可退)   mainRefundFlag(订金是否退还（0不退还；1退还）
                    if (null != refundFlag && (refundFlag == 0
                            || (refundFlag == 1 && mainRefundFlag == 1)
                            || (refundFlag == 2 && mainRefundFlag == 0))) {
                        // goodsType(0全部   1普通货源   2优车货源)   mainExcellentGoods(0:普通货源 1:优车货源)
                        if (null != goodsType && (goodsType == 0
                                || (goodsType == 1 && mainExcellentGoods == 0)
                                || ((goodsType == 2 || goodsType == 5) && mainExcellentGoods == 1)
                                || (goodsType == 3 && mainExcellentGoods == 3)
                                || (goodsType == 4 && mainExcellentGoods == 2)
                                || (goodsType == 6 && isGoodCarPriceTransport))) {
                            // 开票状态( 0不限   1开票    2非开票)   mainExcellentGoods(0:普通货源 1:优车货源)
                            if (null != invoiceTransport && (invoiceTransport == 0
                                    || (invoiceTransport == 1 && mainInvoiceTransport == 1)
                                    || (invoiceTransport == 2 && mainInvoiceTransport == 0))) {
                                //ownerIdentity(货主身份 0全部  1个人货主 2企业货主 3货站 4物流公司)  mainIdentity（用户自选身份  1个人货主 2企业货主 3货站 4物流公司）
                                if (null != ownerIdentity && (ownerIdentity == 0
                                        || (mainIdentity != null && ownerIdentity == mainIdentity))) {
                                    //priceType(货源价格类型 0全部  1有价(电议有价和一口价)  2无价  3一口价) mainPublishType(1无价 2一口价  3有价)
                                    if (null != priceType && (priceType == 0
                                            || ((priceType == 1 && (mainPublishType == 2||mainPublishType == 3))
                                            || (priceType == 2 && mainPublishType == 1)|| (priceType == 3 && mainPublishType == 2)))) {
                                        //发货方式判断 deliveryFlag 发货方式 0不限   1优车发货  2普通发货
                                        if(null != deliveryFlag && (deliveryFlag == 0
                                                || (deliveryFlag == 1 && mainDeliveryFlag == 1)
                                                || (deliveryFlag == 2 && mainDeliveryFlag == 0))){
                                            //抽佣标签判断 tecServiceFeeFlag 抽佣标识：0-不限；1-有标签；2-无标签；
                                            if (null != tecServiceFeeFlag && (tecServiceFeeFlag == 0
                                                    || tecServiceFeeFlag == 2
                                                    || (tecServiceFeeFlag == 1 && ObjectUtil.isNotNull(transportTecServiceFee)))) {
                                                adPositionShowIds.add(adPositionId);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.info("adPositionService checkShow error:", e);
        }
        return adPositionShowIds;
    }

    @Override
    public List<Long> checkShowNew(Long[] adPositionIds, Integer invoice, Integer drivice,Long userId){
        List<Long> adPositionShowIds = new ArrayList();
        //优车权益
        TytDepositAuthorization tytDepositAuthorization = tytDepositAuthorizationMapper.getByUserId(userId);
        //优车发货卡
        TytExcellentGoodsCardUserDetail detail = tytExcellentGoodsCardUserDetailMapper.getByUserDate(userId);
        try{
            for (Long adPositionId : adPositionIds) {
                AdPositionGoodsConfigResp goodsConfig = getGoodsConfigByAdPositionId(adPositionId);
                if (null != goodsConfig) {
                    Integer adPosition = goodsConfig.getAdPosition();

                    Integer invoiceTransport = goodsConfig.getInvoiceTransport();
                    Integer deliveryFlag = goodsConfig.getDeliveryFlag();
                    logger.info("checkShowNew getGoodsConfigByAdPositionId 【adPositionId:{}】【invoiceTransport:{}】【deliveryFlag{}】", adPositionId, invoiceTransport,deliveryFlag);
                    //逻辑处理是否符合要求
                    if(null != invoiceTransport && (invoiceTransport == 0 || (invoiceTransport == 1 && invoice == 1) || (invoiceTransport == 2 && invoice == 0))){

                        //如果是会员
                        if(null != tytDepositAuthorization || null != detail){
                            adPositionShowIds = packageData(drivice, deliveryFlag, adPositionShowIds, adPositionId);
                            if(CollectionUtils.isNotEmpty(adPositionShowIds)){
                                break ;
                            }
                        }else if(null == tytDepositAuthorization && null == detail){
                            //如果选择了优车发货
                            adPositionShowIds = DepositAuthPackageData(drivice, deliveryFlag, adPositionShowIds, adPositionId);
                            if(CollectionUtils.isNotEmpty(adPositionShowIds)){
                                break ;
                            }
                        }
                    }
                }
            }
        }catch (Exception e) {
            logger.info("adPositionService checkShowNew error:", e);
        }
        return adPositionShowIds;
    }

    /**
     * 优车用户
     * @param drivice 开票状态
     * @param deliveryFlag 发货类型

     * @param adPositionShowIds 封装广告位id
     * @param adPositionId 广告位id
     * @return List<Long>
     */
    public List<Long> packageData(Integer drivice,Integer deliveryFlag,List<Long> adPositionShowIds,Long adPositionId){
        //如果是优车用户，选择了优车发货或者没选都返回优车的广告位
        if(null == drivice || ( null != drivice && 1 == drivice)){
            if(null != deliveryFlag && (deliveryFlag == 0 || deliveryFlag == 1)){
                adPositionShowIds.add(adPositionId);
                return adPositionShowIds;
            }
        }else{
            if(null != deliveryFlag && (deliveryFlag == 0 || deliveryFlag == 2)){
                adPositionShowIds.add(adPositionId);
                return adPositionShowIds;
            }
        }
        return adPositionShowIds;
    }


    public List<Long> DepositAuthPackageData(Integer drivice,Integer deliveryFlag,List<Long> adPositionShowIds,Long adPositionId){
        //如果不是优车用户只有选择了优车发货才会展示优车的广告位
        if(null != drivice && 1 == drivice){
            if(null != deliveryFlag && (deliveryFlag == 0 || deliveryFlag == 1)){
                adPositionShowIds.add(adPositionId);
                return adPositionShowIds;
            }
        }else{
            if(null != deliveryFlag && (deliveryFlag == 0 || deliveryFlag == 2)){
                adPositionShowIds.add(adPositionId);
                return adPositionShowIds;
            }
        }
        return adPositionShowIds;
    }

    @Override
    public boolean checkShowRule(AdPositionRuleBean ruleBean, Long userId, Long positionId) throws Exception {
        Date today = new Date();
        TytUserRecord userRecord = tytUserRecordService.getByUserIdAndCode(userId, CODE + positionId);
        if (!Objects.isNull(userRecord)) {
            if (Integer.parseInt(userRecord.getValue()) >= ruleBean.getRuleTimes()) {
                return false;
            }
            int ruleDays = ruleBean.getRuleDays() - 1;
            Date ruleTime = TimeUtil.weeHours(TimeUtil.addDay(today, -ruleDays), 0);
            Date lastRecord = userRecord.getModifyTime() == null ? userRecord.getCreateTime() : userRecord.getModifyTime();
            if (lastRecord.after(ruleTime)) {
                return false;
            }
        }
        if (Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.LOGIN.getCode())) {
            return true;
        }
        int conditionDays = ruleBean.getConditionDays() - 1;
        Date conditionTime = TimeUtil.weeHours(TimeUtil.addDay(today, -conditionDays), 0);
        if (Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.NO_PUBLISH.getCode()) || Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.PUBLISH_GOODS.getCode())) {
            Integer count = transportMainService.getPublishCountByUserId(userId, conditionTime, today);
            if (Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.NO_PUBLISH.getCode()) && count > 0) {
                return false;
            }
            if (Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.PUBLISH_GOODS.getCode()) && count < ruleBean.getConditionTimes()) {
                return false;
            }
        }
        if (Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.FINISH_ORDERS.getCode()) || Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.NO_ORDERS.getCode())) {
            Integer count = transportOrdersRiskService.getGoodsCountByTime(conditionTime, today, userId);
            if (Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.NO_ORDERS.getCode()) && count > 0) {
                return false;
            }
            if (Objects.equals(ruleBean.getConditionContent(), ConditionContentEnum.FINISH_ORDERS.getCode()) && count < ruleBean.getConditionTimes()) {
                return false;
            }
        }
        return true;
    }


    private AdPositionGoodsConfigResp getGoodsConfigByAdPositionId(Long adPositionId) {
        String sb = "SELECT ad_position adPosition,price_type as priceType,goods_type as goodsType,owner_identity as ownerIdentity,invoice_transport as invoiceTransport,refund_flag as refundFlag,delivery_flag as deliveryFlag, tec_service_fee_flag tecServiceFeeFlag  FROM `tyt_ad_position_goods_config` where ad_position_id=:adPositionId";
        Map<String, Object> params = new HashMap<>();
        params.put("adPositionId", adPositionId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
        scalarMap.put("adPosition", Hibernate.INTEGER);
        scalarMap.put("priceType", Hibernate.INTEGER);
        scalarMap.put("goodsType", Hibernate.INTEGER);
        scalarMap.put("ownerIdentity", Hibernate.INTEGER);
        scalarMap.put("invoiceTransport", Hibernate.INTEGER);
        scalarMap.put("refundFlag", Hibernate.INTEGER);
        scalarMap.put("deliveryFlag", Hibernate.INTEGER);
        scalarMap.put("tecServiceFeeFlag", Hibernate.INTEGER);
        List<AdPositionGoodsConfigResp> adPositionsUserList = this.getBaseDao().search(sb, scalarMap, AdPositionGoodsConfigResp.class, params);
        if (adPositionsUserList != null && adPositionsUserList.size() > 0) {
            return adPositionsUserList.get(0);
        }
        return null;
    }

    /**
     * 获取货主身份
     *
     * @return
     */
    private Integer getIdentityByUserId(Long userId) {
        String sb = "SELECT goods_type_first as goodsTypeFirst, goods_type_second as goodsTypeSecond FROM `tyt_user_identity_label` where user_id=:userId";
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<>();
        scalarMap.put("goodsTypeFirst", Hibernate.INTEGER);
        scalarMap.put("goodsTypeSecond", Hibernate.INTEGER);
        List<UserIdentityLabelBean> userIdentityLabelList = this.getBaseDao().search(sb, scalarMap, UserIdentityLabelBean.class, params);
        if (null != userIdentityLabelList && userIdentityLabelList.size() > 0) {
            UserIdentityLabelBean labelBean = userIdentityLabelList.get(0);
            Integer goodsTypeFirst = labelBean.getGoodsTypeFirst();
            Integer goodsTypeSecond = labelBean.getGoodsTypeSecond();
            if (null != goodsTypeFirst && null != goodsTypeSecond) {
                UserLabelTypeEnum anEnum = UserLabelTypeEnum.getEnum(goodsTypeFirst, goodsTypeSecond);
                if (!CommonUtil.hasNull(anEnum)) {
                    return anEnum.getAlias();
                }
            }
        }
        return null;
    }


    /**
     * 身份是否满足广告位
     *
     * @param ad     广告位信息
     * @param userId 用户id
     * @return boolean
     */
    private boolean checkUserIdentity(AdPosition ad, Long userId, String clientSign, String province) {
        // 省条件
        if (Strings.isNotEmpty(ad.getShowProvince())) {
            String[] stringShowProvince = ad.getShowProvince().split(",");
            if (!ArrayUtils.contains(stringShowProvince, province)) {
                return false;
            }
        }
        if (Objects.equals(ad.getUserType(), UserTypeEnum.ALL_USER.getCode()) && ad.getGoodsIdentity().equals(DwsIdentityTypeEnum.ALL_USER.getCode().toString()) && ad.getVipStatus().equals(VipStatusEnum.ALL_USER.getCode())) {
            return true;
        }
        // 会员
        if (!Objects.equals(ad.getVipStatus(), VipStatusEnum.ALL_USER.getCode())) {
            int userVipType = VipStatusEnum.NO_VIP.getCode();
            String typeId = Constant.ClientSignEnum.isCar(Integer.parseInt(clientSign)) ? UserPermission.PermissionTypeEnum.CAR_MEMBER.getCode() : UserPermission.PermissionTypeEnum.GOODS_MEMBER.getCode();
            UserPermission userPermission = userPermissionService.getUserPermission(userId, typeId);
            if (!Objects.isNull(userPermission) && userPermission.getStatus() == 1) {
                userVipType = VipStatusEnum.VIP.getCode();
            }
            if (ad.getVipStatus() != userVipType) {
                return false;
            }
        }
        // 货主身份
        DwsNewIdentityTwoData data = apiDataUserCreditInfoService.getIdentityByUserId(userId);
        if (!Objects.equals(ad.getGoodsIdentity(), DwsIdentityTypeEnum.ALL_USER.getCode().toString())) {
            if (Objects.isNull(data)) {
                return false;
            }
            String[] adGoodsIdentity = ad.getGoodsIdentity().split(",");
            boolean userIdentity = ArrayUtils.contains(adGoodsIdentity, String.valueOf(data.getType()));
            if (!userIdentity) {
                return false;
            }
        }

        //是否是体验差一手货主
        if (Objects.equals(ad.getUserType(), UserTypeEnum.POOR_EXPERIENCE.getCode())) {
            return transportMainService.checkPoorExperience(data);
        }
        return true;
    }
}
