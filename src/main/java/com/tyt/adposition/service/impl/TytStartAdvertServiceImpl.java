package com.tyt.adposition.service.impl;

import com.tyt.adposition.service.TytStartAdvertService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.marketingActivity.bean.MarketActivityBean;
import com.tyt.marketingActivity.bean.MarketingActivityPopupBean;
import com.tyt.model.TytStartAdvert;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("tytStartAdvertService")
public class TytStartAdvertServiceImpl extends BaseServiceImpl<TytStartAdvert, Long> implements TytStartAdvertService {

    @Resource(name = "tytStartAdvertDao")
    public void setBaseDao(BaseDao<TytStartAdvert, Long> tytStartAdvertDao) {
        super.setBaseDao(tytStartAdvertDao);
    }

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    public static final String CACHE_GET_START_AD_LIST = "cache_get_start_ad_list_";

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public List<TytStartAdvert> getStartAdList(Integer position){
        Object advertList = RedisUtil.getObject(CACHE_GET_START_AD_LIST + position);
        if (advertList == null){
            String sql = "SELECT * FROM `tyt_start_advert` WHERE status=1 AND show_position=? AND end_time>NOW() ORDER BY start_time ASC LIMIT 2 ";
            final Object[] params = {
                    position
            };
            List<TytStartAdvert> adPositions = this.getBaseDao().queryForList(sql,params);
            int cacheTime = tytConfigService.getIntValue(Constant.START_AD_LIST_GET_CACHE_TIME, 300);
            RedisUtil.setObject( CACHE_GET_START_AD_LIST+ position, adPositions, cacheTime);
            return adPositions;
        }else{
            logger.info("get start ad by redis");
            return (List<TytStartAdvert>)advertList;
        }



    }

    @Override
    public List<MarketingActivityPopupBean> getAdListByIds(String ids) {
        String sql = "SELECT `id`,`title`,`show_position` showPosition,`pic_url` picUrl,`link_type` linkType,`pic_link_url` picLinkUrl,"
                + "`time_interval` timeInterval,`time_unit` timeUnit FROM `tyt_start_advert` WHERE `status`=1 and id in ("+ids+") order by sort desc ";

        //传入的参数
        Object[] params = new Object[] {};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("showPosition", Hibernate.INTEGER);
        scalarMap.put("picUrl", Hibernate.STRING);
        scalarMap.put("linkType", Hibernate.STRING);
        scalarMap.put("picLinkUrl", Hibernate.STRING);
        scalarMap.put("timeInterval", Hibernate.INTEGER);
        scalarMap.put("timeUnit", Hibernate.STRING);
        List<MarketingActivityPopupBean> popupList =  this.getBaseDao().search(sql, scalarMap, MarketingActivityPopupBean.class, params);
        if (CollectionUtils.isNotEmpty(popupList)){
            return popupList;
        }
        return new ArrayList<>();
    }

}
