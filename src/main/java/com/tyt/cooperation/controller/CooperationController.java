package com.tyt.cooperation.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.tyt.base.controller.BaseController;
import com.tyt.cooperation.service.CooperationInfoService;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;
@Controller
@RequestMapping("/web/cooperation")
public class CooperationController extends BaseController {
	
	@Resource(name = "cooperationInfoService")
	CooperationInfoService cooperationInfoService;
	
	/**
	 * 保存
	 * @param cooperation
	 * @return
	 */
	@RequestMapping("/save")
	@ResponseBody
	public void saveCooperation(String companyName,String teleName,
			String telePhone,String sourceType,
			HttpServletRequest request,HttpServletResponse response){
		ResultMsgBean rm = new ResultMsgBean();
		
		try {
			companyName=new String(companyName.getBytes("ISO8859-1"),"UTF-8");
			teleName=new String(teleName.getBytes("ISO8859-1"),"UTF-8");
			cooperationInfoService.addCooperationInfo(
					companyName, teleName,
					telePhone, sourceType);
			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("保存成功!");
			printJSON(request, response, rm);
			return;
		} catch (Exception e) {
			e.printStackTrace();
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("保存失败,请稍后重试!");
			printJSON(request, response, rm);
			return;
		}
		
	}

}
