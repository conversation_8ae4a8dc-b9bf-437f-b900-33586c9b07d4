package com.tyt.cooperation.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cooperation.service.CooperationInfoService;
import com.tyt.model.TytCooperationInfo;

@Service("cooperationInfoService")
public class CooperationInfoServiceImpl extends BaseServiceImpl<TytCooperationInfo, Long>
		implements CooperationInfoService {
	
	@Resource(name = "cooperationInfoDao")
	public void setBaseDao(BaseDao<TytCooperationInfo, Long> cooperationInfoDao) {
		super.setBaseDao(cooperationInfoDao);
	}

	@Override
	public TytCooperationInfo addCooperationInfo(String companyName, String teleName,
			String telePhone,String sourceType) throws Exception {
		TytCooperationInfo cooperation=new TytCooperationInfo(
				companyName, teleName, telePhone, sourceType, new Date());
		this.add(cooperation);
		return cooperation;
	}

}
