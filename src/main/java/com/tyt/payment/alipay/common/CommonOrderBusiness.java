package com.tyt.payment.alipay.common;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.cache.CacheService;
import com.tyt.config.util.AppConfig;
import com.tyt.model.Order;
import com.tyt.model.User;
import com.tyt.payment.alipay.service.OrderService;
import com.tyt.payment.alipay.service.PayBackService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;

@Service("commonOrderBusiness")
public class CommonOrderBusiness{

	
	@Resource(name = "commonUserBusiness")
	private CommonUserBusiness commonUserBusiness;
	
	@Resource(name = "userService")
	private UserService userService;
	
	@Resource(name = "orderService")
	private OrderService orderService;
	
	@Resource(name = "payBackService")
	private PayBackService payBackService;
	
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
	
	/**
	 * 更新订单,用户信息
	 * @param order
	 * @return
	 * @throws Exception
	 */
	public Boolean updateOrderAndUser(Order order) throws Exception{
		        cacheService.del(Constant.CACHE_USER_KEY + order.getCellPhone());
				order.setStatus(Order.ORDER_STATUS_PAY_SUCCESS);
				
				User user=commonUserBusiness.findUserFromMCorDBByCellPhone(order.getCellPhone());
				user.setRenewalYears(order.getRenewalYears());

				if (user.getUserType() == User.USER_TYPE_TRIAL) {
					user.setUserType(User.USER_TYPE_VIP);
					user.setPayDate(TimeUtil.getTimeStamp());
					user.setPayStatus(User.PAY_STATUS_FIRST);
					user.setEndTime(TimeUtil.addYearDays(
							user.getRenewalYears(), user.getServeDays()));
				} else if (user.getUserType() == User.USER_TYPE_VIP) {
					user.setPayStatus(User.PAY_STATUS_RENEWAL);
					user.setRenewalDate(TimeUtil.getTimeStamp());
					user.setEndTime(TimeUtil.addYearDays(
							user.getRenewalYears(), user.getServeDays()));
				}
				user.setServeDays(TimeUtil.getDays(TimeUtil.getTimeStamp(),user.getEndTime()));
				user.setMtime(TimeUtil.getTimeStamp());
				
				userService.update(user);
				boolean b = cacheService
						.setObject(
								Constant.CACHE_USER_KEY + user.getCellPhone(),
								user,AppConfig.getIntProperty("tyt.cache.user.time"));
				order.setMtime(TimeUtil.getTimeStamp());
				orderService.update(order);
                return b;
		
	}
	
	/**
	 * 用户付款成功取消操作
	 * @param id
	 * @param cancel
	 * @return
	 * @throws Exception 
	 */
	public String alipayIsSuccess(String orderId,Integer cancel) throws Exception{
		if (cancel == null) {
			if (orderId != null) {
				Order order = orderService.getByOrderId(orderId);
				if (order != null) {
					if (order.getStatus() == Order.ORDER_STATUS_PAY_SUCCESS) {
						return "付款成功，请重新登录！";
					} else {
						return "如果支付成功，最多10分钟后重新登陆查看，否则请支付！";
					}
				} else {
					return  "请去支付页面选择《去充值》按钮";
				}
			} else {
				return "orderId为空，请重新支付！";
			}
		}else{
			return "您做了取消操作，请重新支付！";
		}
	}

	
}
