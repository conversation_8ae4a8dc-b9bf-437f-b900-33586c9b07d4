package com.tyt.payment.alipay.common;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.cache.CacheService;
import com.tyt.model.User;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;

@Service("commonUserBusiness")
public class CommonUserBusiness {

	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
	@Resource(name = "userService")
	private UserService userService;
	
	public User findUserFromMCorDBByCellPhone(String cellPhone) throws Exception{
		
			Object object = cacheService.getObject(Constant.CACHE_USER_KEY+ cellPhone);
			User user = null;
			if (object == null) {
				user = userService.getUserByCellphone(cellPhone);
			} else {
				user = (User) object;
			}
			return user;
	}
}
