package com.tyt.payment.alipay.controller;

import java.io.PrintWriter;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.model.Order;
import com.tyt.model.PayBack;
import com.tyt.model.PayMethod;
import com.tyt.model.Price;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.payment.alipay.common.CommonOrderBusiness;
import com.tyt.payment.alipay.common.CommonUserBusiness;
import com.tyt.payment.alipay.config.AlipayConfig;
import com.tyt.payment.alipay.service.OrderService;
import com.tyt.payment.alipay.service.PayBackService;
import com.tyt.payment.alipay.util.AlipayCore;
import com.tyt.payment.alipay.util.AlipayNotify;
import com.tyt.payment.alipay.util.AlipaySubmit;
import com.tyt.payment.service.PayMethodService;
import com.tyt.payment.service.PriceService;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import com.tyt.util.Encoder;
import com.tyt.util.TimeUtil;


@Controller
@RequestMapping("/plat/alipay")
public class AlipayController extends BaseController {
	
	@Resource(name = "commonUserBusiness")
	private CommonUserBusiness commonUserBusiness;
	
	@Resource(name = "commonOrderBusiness")
	private CommonOrderBusiness commonOrderBusiness;
	
	@Resource(name = "payBackService")
	private PayBackService payBackService;
	
	@Resource(name = "orderService")
	private OrderService orderService;
	
	@Resource(name = "priceService")
	private PriceService priceService;
	
	@Resource(name = "payMethodService")
	private PayMethodService payMethodService;
	
	@Resource(name = "userService")
	private UserService userService;
	
	@RequestMapping(value = "/webIsSuccess",method=RequestMethod.GET)
	public String webIsSuccess(String orderId,String cellPhone,RedirectAttributes attributes,Integer cancel,
			HttpServletRequest request,HttpServletResponse response){
		try{
			attributes.addAttribute("msg",commonOrderBusiness.alipayIsSuccess(orderId, cancel));
		}catch(Exception e){
			logger.info("alipay:webIsSuccess "+cellPhone+" Exception"+e.toString());
		}finally{
			String token=Encoder.md5(cellPhone+AppConfig.getProperty("tyt.private.key"));
			attributes.addAttribute("cellPhone", cellPhone);
			attributes.addAttribute("token", token);
		}
		return "redirect:/pc/user/list";
	}
	
	@RequestMapping(value = "/access")
	public void access(Long priceId, String cellPhone,String token, Long payMethodId,
			@RequestParam(value = "platId", defaultValue = Constant.PLAT_PC
					+ "") Integer platId, HttpServletRequest request,
			@RequestParam(value = "payChannel", defaultValue = "alipay") String payChannel,
			HttpServletResponse response) {
		/*参数验证*/
		if (!StringUtils.hasLength(token)) {
			logger.info("alipay:access token is null.");
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
					"token is null");
			printJSON(request, response, msgBean);
			return;
		}
		if (!StringUtils.hasLength(cellPhone)) {
			logger.info("alipay:access cellPhone is null.");
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
					"cellPhone is null");
			printJSON(request, response, msgBean);
			return;
		}
		if (priceId == null) {
			logger.info("alipay:access priceId is null.");
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
					"priceId is null");
			printJSON(request, response, msgBean);
			return;
		}
		if (payMethodId == null) {
			logger.info("alipay:access payMethodId is null.");
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
					"payMethodId is null");
			printJSON(request, response, msgBean);
			return;
		}
//		if (userType == null) {
//			logger.info("alipay:access userType is null.");
//			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
//					"userType is null");
//			printJSON(request, response, msgBean);
//			return;
//		}
		if (!validateToken(cellPhone, token, request, response)) {
			logger.info("transport:save.token error");
			return;
		}
		try {
			// 支付类型
			// 必填，不能修改
			// 服务器异步通知页面路径
			// 需http://格式的完整路径，不能加?id=123这类自定义参数

			// 页面跳转同步通知页面路径
			// String return_url =
			// "http://*************:9090/test_ssh/alipay/sync";
			// 需http://格式的完整路径，不能加?id=123这类自定义参数，不能写成http://localhost/
			// 商户订单号
			// String out_trade_no = new
			// String(request.getParameter("WIDout_trade_no").getBytes("ISO-8859-1"),"UTF-8");
			// 商户网站订单系统中唯一订单号，必填
//			String out_trade_no = MakeOrderNum.getOrderNo();
			// 订单名称
			// String subject = new
			// String(request.getParameter("WIDsubject").getBytes("ISO-8859-1"),"UTF-8");
			// 必填
//			String subject = cellPhone + "特运通" + (userType == 1 ? "续费" : "缴费");
			// 付款金额
			// String total_fee = new
			// String(request.getParameter("WIDtotal_fee").getBytes("ISO-8859-1"),"UTF-8");
			// 必填
			// String total_fee=renewalYears*0.01+"";
			// 订单描述

			// String body = new
			// String(request.getParameter("WIDbody").getBytes("ISO-8859-1"),"UTF-8");
			// 商品展示地址
			// String show_url = new
			// String(request.getParameter("WIDshow_url").getBytes("ISO-8859-1"),"UTF-8");
			// 需以http://开头的完整路径，例如：http://www.商户网址.com/myorder.html

			// 防钓鱼时间戳
//			String anti_phishing_key = "";
			// 若要使用请调用类文件submit中的query_timestamp函数

			// 客户端的IP地址
//			String exter_invoke_ip = StringUtil.getRealIp(request);
			// 非局域网的外网IP地址，如：*********
			ResultMsgBean msgBean = null;
//			Order order = new Order();
//			order.setCellPhone(cellPhone);
//			order.setCtime(TimeUtil.getTimeStamp());
//			order.setMtime(TimeUtil.getTimeStamp());
//			order.setStatus(AlipayOrder.ALIPAY_ORDER_STATUS_NO_PAY);
//			order.setPayStatus(userType == 1 ? 2 : 1);

			PayMethod payMethod = payMethodService.getById(payMethodId);
			if (payMethod == null) {
				msgBean = new ResultMsgBean(ResultMsgBean.OK, "数据库没有此支付方式的记录");
				printJSON(request, response, msgBean);
				return;
			}

			
//			order.setOrderId(out_trade_no);
//			Price price = priceService.getById(priceId);
//			if (price == null) {
//				msgBean = new ResultMsgBean(ResultMsgBean.OK, "数据库没有此价格的记录");
//				printJSON(request, response, msgBean);
//				return;
//			}
//			order.setRenewalYears(price.getYears());
//			order.setPlatId(platId);
//			order.setTotalFee(price.getPrice());
//			orderService.add(order);
			/*根据priceId查询数据库*/
			Price price=priceService.getById(priceId);
			/*根据cellPhone查询数据库*/
			User user=userService.getUserByCellphone(cellPhone);
			/*生成order对象*/
			Order order=orderService.getOrder(price,user,payChannel,platId);
			order.setDefaultBank(payMethod.getRf());
			/*保存order*/
			orderService.add(order);
			logger.info("alipay:access订单号" + order.getOrderId() + "保存订单到数据库成."
					+ order.toString());
			// 打包请求参数
			Map<String, String> sParaTemp = createAlipayRequestMap(order);
			logger.info("alipay:access订单号" + order.getOrderId() + "请求参数为"+ sParaTemp);
			// 建立请求
			if (platId != 1) {//生成pc嵌入网页的请求url
				String url = AlipayCore.createLinkString(sParaTemp);
				msgBean = new ResultMsgBean(ResultMsgBean.OK, url);
			} else {//生成客户端请求url,暂时未使用
				String sHtmlText = AlipaySubmit.buildRequest(sParaTemp, "get","确认");
				msgBean = new ResultMsgBean(ResultMsgBean.OK, sHtmlText);
			}
			msgBean.setData(order);
			printJSON(request, response, msgBean);
		} catch (Exception e) {
			logger.info("alipay access:" + cellPhone + " Exception.");
			e.printStackTrace();
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,e.toString());
			printJSON(request, response, msgBean);
			return;
		}

	}
	/**
	 * 生成支付宝请求参数
	 * @param order
	 * @return
	 */
	private Map<String, String> createAlipayRequestMap(Order order){
		Map<String, String> sParaTemp = new HashMap<String, String>();
		sParaTemp.put("service", "create_direct_pay_by_user");
		sParaTemp.put("partner", AlipayConfig.partner);
		sParaTemp.put("seller_email", AlipayConfig.seller_email);
		sParaTemp.put("_input_charset", AlipayConfig.input_charset);
		sParaTemp.put("payment_type", "1");
		sParaTemp.put("notify_url", AppConfig.getProperty("alipay.backUrl"));
		sParaTemp.put("return_url", "");
		sParaTemp.put("out_trade_no", order.getOrderId());
		sParaTemp.put("subject", order.getCellPhone()+(order.getPayStatus()==1?"缴费":"续费"));
		sParaTemp.put("total_fee", order.getTotalFee().floatValue()/100 + "");
		// sParaTemp.put("body", WIDbody);
		// sParaTemp.put("show_url", WIDshow_url);
		sParaTemp.put("anti_phishing_key", "");
		sParaTemp.put("exter_invoke_ip", "");
		sParaTemp.put("extra_common_param", order.getId() + "");
		sParaTemp.put("paymethod", order.getPayMethod());
		sParaTemp.put("defaultbank", order.getDefaultBank());
		return sParaTemp;
	}
	
	/*@RequestMapping(value = "/sync",method=RequestMethod.GET)
	public String sync(HttpServletRequest request,HttpServletResponse response,RedirectAttributes attributes){
		Map<String,String> params = new HashMap<String,String>();
		Map requestParams = request.getParameterMap();
		String cellPhone=null;
		try{
		for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i]
						: valueStr + values[i] + ",";
			}
			//乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
			if(name.equals("subject"))valueStr = URLDecoder.decode(valueStr, "utf-8");
			else valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
//			valueStr = URLDecoder.decode(valueStr, "utf-8");
			params.put(name, valueStr);
		}
		//获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以下仅供参考)//
		//交易状态
		String trade_status=null;
		if(StringUtils.hasLength(request.getParameter("trade_status"))){
			trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"),"UTF-8");
		}
//		
		Long id=null;
		if(StringUtils.hasLength(request.getParameter("extra_common_param"))){
			id = Long.parseLong(new String(request.getParameter("extra_common_param")));
		}
		
		String is_success=null;
		if(StringUtils.hasLength(request.getParameter("is_success"))){
			is_success = new String(request.getParameter("is_success").getBytes("ISO-8859-1"),"UTF-8");
		}
		//获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以上仅供参考)//
		
		//计算得出通知验证结果
		boolean verify_result = AlipayNotify.verify(params);
//		PrintWriter out=response.getWriter();
		Order order=orderService.getById(id);
//		order.setTradeStatus(trade_status);
		cellPhone=order.getCellPhone();
		logger.info("alipay sync:"+cellPhone+" params verify "+verify_result+"["+params+"]");
		if(verify_result){//验证成功
			//////////////////////////////////////////////////////////////////////////////////////////
			//请在这里加上商户的业务逻辑程序代码
           
			//——请根据您的业务逻辑来编写程序（以下代码仅作参考）——
			if(trade_status.equals("TRADE_FINISHED") || trade_status.equals("TRADE_SUCCESS")){
				//判断该笔订单是否在商户网站中已经做过处理
//				getInfos(request,order);
//				if(order.getStatus()==AlipayOrder.ALIPAY_ORDER_STATUS_NO_PAY){
//					order.setStatus(AlipayOrder.ALIPAY_ORDER_STATUS_PAY_SUCCESS);
//					order.setMtime(TimeUtil.getTimeStamp());
//					alipayOrderService.update(order);
//					//修改用户的相关信息
//					
//					Object object=cacheService.getObject(Constant.CACHE_USER_KEY+ cellPhone);
//				        User user=null;
//				        if(object==null){
//				        	user=userService.getUserByCellphone(cellPhone);
//				        }else{
//				        	user=(User) object;
//				        }
//				    user.setRenewalYears(order.getQuantity());
//				    user.setServeDays(user.getServeDays()+order.getQuantity()*365);
//					if(user.getUserType()==User.USER_TYPE_TRIAL){
//						user.setUserType(User.USER_TYPE_VIP);
//						user.setPayDate(TimeUtil.getTimeStamp());
//						user.setPayStatus(User.PAY_STATUS_FIRST);
//						user.setEndTime(TimeUtil.stampAdd(user.getPayDate(), user.getServeDays()+TimeUtil.getDays(user.getPayDate())));
//					}else if(user.getUserType()==User.USER_TYPE_VIP){
//						user.setPayStatus(User.PAY_STATUS_RENEWAL);
//						user.setRenewalDate(TimeUtil.getTimeStamp());
//						user.setEndTime(TimeUtil.stampAdd(user.getRenewalDate(), user.getServeDays()+TimeUtil.getDays(user.getRenewalDate())));
//					}
//					user.setMtime(TimeUtil.getTimeStamp());
//					userService.update(user);
//				}
					//如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
					//如果有做过处理，不执行商户的业务程序
				attributes.addAttribute("msg", "付款成功，请退出后重新登录。");
			}
			
			//该页面可做页面美工编辑
//			out.println("验证成功<br />");
			//——请根据您的业务逻辑来编写程序（以上代码仅作参考）——

			//////////////////////////////////////////////////////////////////////////////////////////
		}else{
			//该页面可做页面美工编辑
//			out.println("验证失败");
//			order.setStatus(AlipayOrder.ALIPAY_ORDER_STATUS_SYNC_VERIFY_FAILURE);
//			order.setMtime(TimeUtil.getTimeStamp());
//			alipayOrderService.update(order);
			attributes.addAttribute("msg", "付款失败，请重新支付。");
		}
		String token=Encoder.md5(cellPhone+AppConfig.getProperty("tyt.private.key"));
		attributes.addAttribute("cellPhone", cellPhone);
		attributes.addAttribute("token", token);
		return "redirect:/pc/user/list";
		
		}catch(Exception e){
			logger.info("alipay sync:"+cellPhone+" Exception."+e.getMessage());
			e.printStackTrace();
		}finally{
			
		}
		return null;
	}*/
	
	

	@RequestMapping(value = "/async",method=RequestMethod.POST)
	public void async(HttpServletRequest request,HttpServletResponse response){
		logger.info("alipay/async支付宝后台异步通知开始");
		Map<String,String> params = new HashMap<String,String>();
		Map requestParams = request.getParameterMap();
		logger.info("alipay/async支付宝异步通知参数"+requestParams.toString());
		Order order=null;
		try {
			PrintWriter out=response.getWriter();
			for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
				String name = (String) iter.next();
				String[] values = (String[]) requestParams.get(name);
				String valueStr = "";
				for (int i = 0; i < values.length; i++) {
					valueStr = (i == values.length - 1) ? valueStr + values[i]: valueStr + values[i] + ",";
				}
				//乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
				if(name.equals("subject"))valueStr = URLDecoder.decode(valueStr, "utf-8");
				else valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
				params.put(name, valueStr);
			}
			/*获得交易结果状态码*/
			String trade_status=null;
			if(params.containsKey("trade_status")&&StringUtils.hasLength(params.get("trade_status"))){
				trade_status = new String(params.get("trade_status").getBytes("ISO-8859-1"),"UTF-8");
			}
			/*获得商户订单号*/
			String orderId=new String(params.get("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");
			/*获得金额*/
			Integer price=(int) (Float.parseFloat(new String(params.get("price")))*100);
			/*记录异步通知参数到数据库*/
			payBackService.add(getPayBack(params));
			logger.info("unionpay/BackRcvResponse订单号"+orderId+"异步通知参数保存到数据库.");
			//获取支付宝参数验证结果
			boolean verify_result=AlipayNotify.verify(params);
			logger.info("alipay/async订单号"+orderId+" 参数验证 "+(verify_result?"成功":"失败"));
			if(verify_result){
				if(trade_status.equals("TRADE_FINISHED")){
				} else if (trade_status.equals("TRADE_SUCCESS")){
					    /*验证金额有没被修改*/
					    order=orderService.getByIdAndAmount(orderId,price);
					    if(order==null){
							logger.info("alipay/async订单号"+orderId+"不存在或金额"+price+"错误.");
							throw new Exception("没有此订单或订单金额被改");
						}
						/*改变用户信息并增加天数,改变订单状态*/
						if(order.getStatus()==Order.ORDER_STATUS_NO_PAY){
							boolean b=commonOrderBusiness.updateOrderAndUser(order);
							logger.info("alipay/async订单号订单号"+orderId+"付款"+b);
						}
				}
				out.println("success");	//请不要修改或删除
			}else{//验证失败
//				order.setStatus(Order.ORDER_STATUS_ASYNC_VERIFY_FAILURE);
//				order.setMtime(TimeUtil.getTimeStamp());
//				orderService.update(order);
				out.println("fail");
			}
			logger.info("alipay:async订单号"+orderId+"接收支付宝后台通知结束.");
		} catch (Exception e) {
			logger.info("alipay:async"+order.getCellPhone()+ " Exception."+e.getMessage());
			e.printStackTrace();
		}
	}
//	@RequestMapping(value = "/getToken")
//	public String getToken(RedirectAttributes attributes,
//			Long priceId,String cellPhone,
//			Integer userType ,
//			Long payMethodId,
//			HttpServletRequest request,HttpServletResponse response){
//		String token=Encoder.md5(cellPhone+AppConfig.getProperty("tyt.private.key"));
//		attributes.addAttribute("cellPhone", cellPhone);
//		attributes.addAttribute("token", token);
//		attributes.addAttribute("userType", userType);
//		attributes.addAttribute("priceId", priceId);
//		attributes.addAttribute("payMethodId", payMethodId);
//		return "redirect:/alipay/access";
//	}
	
//	@RequestMapping(value = "/toGetResource")
//	public String toGetResource(RedirectAttributes attributes,
//			String cellPhone,Integer type ,
//			HttpServletRequest request,HttpServletResponse response){
//		String token=Encoder.md5(cellPhone+AppConfig.getProperty("tyt.private.key"));
//		attributes.addAttribute("cellPhone", cellPhone);
//		attributes.addAttribute("token", token);
//		attributes.addAttribute("type", type);
//		return "redirect:/resource/get";
//	}
	/**
	 * 是否支付成功
	 * @param orderId
	 * @param cellPhone
	 * @param token
	 * @param request
	 * @param response
	 *//*
	@RequestMapping(value = "/isPay")
	public void isPay(String orderId,String cellPhone,String token,
			HttpServletRequest request,HttpServletResponse response){
		ResultMsgBean msgBean=null;
		User user=null;
		try{
			参数判空
			if(!StringUtils.hasLength(cellPhone)){
	        	logger.info("alipay:isPay cellPhone is null.");
				msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
						"cellPhone is null");
				printJSON(request, response, msgBean);
				return;
	        }
			if(!StringUtils.hasLength(orderId)){
	        	logger.info("alipay:isPay orderId is null.");
				msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
						"orderId is null");
				printJSON(request, response, msgBean);
				return;
	        }
			if (!validateToken(cellPhone, token, request, response)) {
				logger.info("alipay:isPay.token error");
				return;
			}
			
			String msg=commonOrderBusiness.alipayIsSuccess(orderId, null);
			Integer code=null;
			if(msg.contains("成功")){
				code=ResultMsgBean.OK;
				msg="付款成功";
			}else{
				code=ResultMsgBean.ERROR;
			}
			msgBean = new ResultMsgBean(code,msg);
			if(code==ResultMsgBean.OK){
				user=commonUserBusiness.findUserFromMCorDBByCellPhone(cellPhone);
				msgBean.setData(user);
			}
			printJSON(request, response, msgBean);
			logger.info("alipay:isPay "+cellPhone+msg+user);
			return;
		}catch(Exception e){
			logger.info("alipay:isPay "+cellPhone+" Exception."+e.getMessage());
			msgBean = new ResultMsgBean(ResultMsgBean.ERROR,"Exception");
			printJSON(request, response, msgBean);
			return;
		}finally{
			msgBean=null;
			user=null;
		}
		
		
		
	}*/
	/**
	 * 解析支付宝异步通知参数，返回PayBack对象
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public PayBack getPayBack(Map<String,String> params) throws Exception {
		PayBack payBack=new PayBack();
		//支付宝流水号
		String trade_no=null;
		if(params.containsKey("trade_no")&&StringUtils.hasLength(params.get("trade_no"))){
			trade_no = new String(params.get("trade_no"));
			payBack.setTradeNo(trade_no);
		}
		
		String out_trade_no=null;
		if(params.containsKey("out_trade_no")&&StringUtils.hasLength(params.get("out_trade_no"))){
			out_trade_no = new String(params.get("out_trade_no"));
			payBack.setPayNo(out_trade_no);
		}
		
		String trade_status=null;
		if(params.containsKey("trade_status")&&StringUtils.hasLength(params.get("trade_status"))){
			trade_status = new String(params.get("trade_status"));
	        payBack.setStatus(trade_status);
		}
		
		Float price=null;
		if(params.containsKey("price")&&StringUtils.hasLength(params.get("price"))){
			price = Float.parseFloat(new String(params.get("price")))*100;
	        payBack.setAmount(price.intValue());
		}
		
		payBack.setOther(params.toString());
		payBack.setCreateTime(TimeUtil.getTimeStamp());
		return payBack;
	}
}
