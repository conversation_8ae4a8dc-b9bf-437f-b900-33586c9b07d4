package com.tyt.payment.alipay.service;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Order;
import com.tyt.model.Price;
import com.tyt.model.User;
import com.tyt.payment.alipay.util.MakeOrderNum;
import com.tyt.util.TimeUtil;

@Service("orderService")
public class OrderServiceImpl extends BaseServiceImpl<Order,Long> implements OrderService{
	
	@Resource(name="orderDao")
	public void setBaseDao(BaseDao<Order, Long> orderDao) {
	        super.setBaseDao(orderDao);
	}


	@Override
	public Order getOrder(Price price, User user,String payChannel, Integer platId)throws Exception {
			
		/*判断数据库是否有该条记录*/
		if(price==null){
			 throw new Exception("mysql priceId无有效对应记录.");
		}
		if(user==null){
			throw new Exception("mysql cellPhone无效.");
		}
		/*生成Order对象*/
		Order order=new Order();
		order.setOrderId(MakeOrderNum.getOrderNo());
		order.setCellPhone(user.getCellPhone());
		order.setPayStatus(
				user.getUserType()==User.USER_TYPE_VIP?User.PAY_STATUS_RENEWAL:User.PAY_STATUS_FIRST
						);
		order.setPayMethod(payChannel);
		order.setPlatId(platId);
		order.setStatus(Order.ORDER_STATUS_NO_PAY);
		order.setCtime(TimeUtil.getTimeStamp());
		order.setMtime(TimeUtil.getTimeStamp());
		order.setRenewalYears(price.getYears());
		order.setTotalFee(price.getPrice());
		return order;
	}


	@Override
	public Order getByIdAndAmount(String orderId, Integer amount) {
		StringBuffer sql=new StringBuffer();
		sql.append(" entity.orderId='").append(orderId).append("'");
		sql.append(" and entity.totalFee=").append(amount);
		List<Order> orders=this.getList(sql.toString(), null);
		if(orders.size()>=0)return orders.get(0);
		return null;
	}


	@Override
	public Order getByOrderId(String orderId) throws Exception {
		  StringBuffer sql = new StringBuffer();
	      sql.append(" entity.orderId ='").append(orderId.trim()).append("'");
	      List<Order> orderList = getList(sql.toString(), null);
	     
	      Order order = null;
	      if(orderList != null && orderList.size()>0) {
	    	  order = (Order) orderList.get(0);
	      }
	      return order;
	}


}
