package com.tyt.payment.alipay.service;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.Order;
import com.tyt.model.PayBack;

@Service("payBackService")
public class PayBackServiceImpl extends BaseServiceImpl<PayBack,Long> implements PayBackService{
	
	@Resource(name="payBackDao")
	public void setBaseDao(BaseDao<PayBack, Long> payBackDao) {
	        super.setBaseDao(payBackDao);
	}

	@Override
	public PayBack getByPayNo(String payNo) throws Exception {
		  StringBuffer sql = new StringBuffer();
	      sql.append(" entity.payNo ='").append(payNo.trim()).append("'");
	      List<PayBack> payBackList = getList(sql.toString(), null);
	     
	      PayBack payBack = null;
	      if(payBackList != null && payBackList.size()>0) {
	    	  payBack = (PayBack) payBackList.get(0);
	      }
	      return payBack;
	}


	
}
