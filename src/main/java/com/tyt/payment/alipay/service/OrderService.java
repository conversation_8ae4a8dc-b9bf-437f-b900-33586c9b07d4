package com.tyt.payment.alipay.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.Order;
import com.tyt.model.Price;
import com.tyt.model.User;

public interface OrderService extends BaseService<Order, Long> {
	/**
	 * 保存订单
	 * @param price
	 * @param cellPhone
	 * @param userType
	 * @param payChannel
	 * @param platId
	 */
	public Order getOrder(Price price,User user,String payChannel, Integer platId)throws Exception;
	/**
	 * 根据订单id和金额查询订单，以验证金额是否被改		
	 * @param orderId
	 * @param amount
	 * @return
	 */
	public Order getByIdAndAmount(String orderId,Integer amount)throws Exception;
	/**
	 * 根据商户订单号查询订单
	 * @param orderId
	 * @return
	 * @throws Exception
	 */
	public Order getByOrderId(String orderId)throws Exception;
}
