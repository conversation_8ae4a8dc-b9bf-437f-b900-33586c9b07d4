package com.tyt.payment.alipay.util;

import com.tyt.common.service.TytSequenceService;
import com.tyt.util.ApplicationContextUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class MakeOrderNum extends Thread{

    private static TytSequenceService tytSequenceService;

    /**
     * 生成订单编号
     * @return
     */
    public static synchronized String getOrderNo() {
        if(tytSequenceService == null ){
            tytSequenceService = ApplicationContextUtils.getBean("tytSequenceService");
        }
        String  orderNum = "";
        try {
               orderNum = tytSequenceService.updateGetOrderNumberForDateTime("tyt_transport_order_num");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return orderNum;
    }

}
