package com.tyt.payment.unionpay.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.model.Order;
import com.tyt.model.PayBack;
import com.tyt.model.Price;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.payment.alipay.common.CommonOrderBusiness;
import com.tyt.payment.alipay.service.OrderService;
import com.tyt.payment.alipay.service.PayBackService;
import com.tyt.payment.service.PriceService;
import com.tyt.payment.unionpay.util.Config;
import com.tyt.payment.unionpay.util.DemoBase;
import com.tyt.user.service.UserService;
import com.unionpay.acp.sdk.SDKConfig;
import com.unionpay.acp.sdk.SDKConstants;
import com.unionpay.acp.sdk.SDKUtil;

@Controller
@RequestMapping("/plat/unionpay")
public class UnionpayController extends BaseController{
	
	@Resource(name = "priceService")
	private PriceService priceService;
	
	@Resource(name = "orderService")
	private OrderService orderService;
	
	@Resource(name = "userService")
	private UserService userService;
	
	@Resource(name = "payBackService")
	private PayBackService payBackService;
	
	@Resource(name = "commonOrderBusiness")
	private CommonOrderBusiness commonOrderBusiness;
	/**
	 * 订单保存接口
	 * @param priceId开通年限id
	 * @param cellPhone电话
	 * @param userType用户类型
	 * @param token
	 * @param payChannel支付渠道
	 * @param platId终端
	 * @param request
	 * @param response
	 */
	@RequestMapping("/order/save")
	public void save(Long priceId, String cellPhone,String token,
			@RequestParam(value = "payChannel", defaultValue = "unionpay") String payChannel, 
			@RequestParam(value = "platId", defaultValue ="1") Integer platId, 
					HttpServletRequest request,HttpServletResponse response){
		/*读取客户端传递过来的参数，方便日志调试*/
		StringBuffer condition=new StringBuffer();
		condition.append(" condition[").append("cellPhone:").append(cellPhone)
		         .append(" payChannel:").append(payChannel)
		         .append(" priceId:").append(priceId)
		         .append(" platId:").append(platId).append("]");
		/*参数验证*/
		if (!StringUtils.hasLength(cellPhone)) {
			logger.info("/unionpay/order/save cellPhone is null."+condition);
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
					"cellPhone is null");
			printJSON(request, response, msgBean);
			return;
		}
		if (priceId == null) {
			logger.info("/unionpay/order/save priceId is null."+condition);
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,
					"priceId is null");
			printJSON(request, response, msgBean);
			return;
		}
		/*token验证*/
		if (!validateToken(cellPhone, token, request, response)) {
			logger.info("/unionpay/order/save token error."+condition);
			return;
		}
		
		try{
			/*根据priceId查询数据库*/
			Price price=priceService.getById(priceId);
			/*根据cellPhone查询数据库*/
			User user=userService.getUserByCellphone(cellPhone);
			/*生成order对象*/
			Order order=orderService.getOrder(price,user,payChannel,platId);
			/*保存order*/
			orderService.add(order);
			/*从classpath加载acp_sdk.properties文件*/
			SDKConfig.getConfig().loadPropertiesFromSrc();// 从classpath加载acp_sdk.properties文件
			/*生成请求银联的Map对象*/
			Map<String, String> data=createUnionpayRequestMap(order);
			/*数据签名*/
			data = DemoBase.signData(data);
			// 交易请求url 从配置文件读取
			SDKConfig config=Config.getConfig();
			String requestAppUrl = config.getAppRequestUrl();
			/*数据提交到银联后台并获得返回信息*/
			Map<String, String> resmap = DemoBase.submitUrl(data, requestAppUrl);
			String tn=resmap.get("tn");//取得交易流水号
			logger.info("/unionpay/order/save"+"用户"+cellPhone+"获得银联流水号"+tn);
			if(!StringUtils.hasLength(tn))throw new Exception("服务器请求银联流水号失败!");//交易流水号为空
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.OK,tn);
			printJSON(request, response, msgBean);
			return;
		}catch(Exception e){
			e.printStackTrace();
			ResultMsgBean msgBean = new ResultMsgBean(ResultMsgBean.ERROR,e.toString());
			printJSON(request, response, msgBean);
			return;
		}
	}
	@RequestMapping("/backRcvResponse")
	public void backRcvResponse(HttpServletRequest request, HttpServletResponse response){
		try {
			logger.info("unionpay/backRcvResponse接受银联后台异步通知开始");
			request.setCharacterEncoding("ISO-8859-1");
			String encoding = request.getParameter(SDKConstants.param_encoding);
			// 获取请求参数中所有的信息
			Map<String, String> reqParam = getAllRequestParam(request);
			// 打印请求报文
			logger.info("unionpay/BackRcvResponse返回参数"+reqParam);
			Map<String, String> valideData = null;
			if (null != reqParam && !reqParam.isEmpty()) {
				Iterator<Entry<String, String>> it = reqParam.entrySet().iterator();
				valideData = new HashMap<String, String>(reqParam.size());
				while (it.hasNext()) {
					Entry<String, String> e = it.next();
					String key = (String) e.getKey();
					String value = (String) e.getValue();
					value = new String(value.getBytes("ISO-8859-1"), encoding);
					valideData.put(key, value);
				}
			}
			String orderId=valideData.get("orderId");
			/*记录异步通知参数到数据库*/
			payBackService.add(getPayBack(valideData));
			logger.info("unionpay/BackRcvResponse订单号"+orderId+"异步通知参数保存到数据库.");
			
			// 验证签名
			if (!SDKUtil.validate(valideData, encoding)) {
				logger.info("unionpay/BackRcvResponse订单号"+orderId+"验证签名结果[失败].");
			} else {
				System.out.println(valideData.get("orderId")); //其他字段也可用类似方式获取
				logger.info("unionpay/BackRcvResponse订单号"+orderId+"验证签名结果[成功].");
				/*获得订单号和金额*/
				String amountStr=valideData.get("txnAmt");
				Integer amount=Integer.parseInt(amountStr);
				/*验证金额有没被修改*/
				Order order=orderService.getByIdAndAmount(orderId,amount);
				if(order==null){
					logger.info("unionpay/BackRcvResponse订单号"+orderId+"不存在或金额"+amount+"错误.");
					throw new Exception("没有此订单或订单金额被改");
				}else{
					/*改变用户信息并增加天数,改变订单状态*/
					if(order.getStatus()==Order.ORDER_STATUS_NO_PAY){
						boolean b=commonOrderBusiness.updateOrderAndUser(order);
						logger.info("union/BackRcvResponse订单号"+orderId+"付款"+b);
					}
					
				}
				
			}
			logger.info("unionpay/BackRcvResponse订单号"+orderId+"接收后台通知结束.");
		} catch (Exception e) {
			logger.info("unionpay/BackRcvResponse异常"+e.toString());
			e.printStackTrace();
			return;
		}
}
	
	
	/**
	 * 获取请求参数中所有的信息
	 * 
	 * @param request
	 * @return
	 */
	public  Map<String, String> getAllRequestParam(final HttpServletRequest request) {
		Map<String, String> res = new HashMap<String, String>();
		Enumeration<?> temp = request.getParameterNames();
		if (null != temp) {
			while (temp.hasMoreElements()) {
				String en = (String) temp.nextElement();
				String value = request.getParameter(en);
				res.put(en, value);
				//在报文上送时，如果字段的值为空，则不上送<下面的处理为在获取所有参数数据时，判断若值为空，则删除这个字段>
				//System.out.println("ServletUtil类247行  temp数据的键=="+en+"     值==="+value);
				if (null == res.get(en) || "".equals(res.get(en))) {
					res.remove(en);
				}
			}
		}
		return res;
	}
	/**
	 * 生成Map
	 * @param price
	 * @param user
	 * @param payChannel
	 * @param platId
	 * @return
	 */
	private Map<String, String> createUnionpayRequestMap(Order order) {
		Map<String, String> data = new HashMap<String, String>();
		// 版本号
		data.put("version", "5.0.0");
		// 字符集编码 默认"UTF-8"
		data.put("encoding", "UTF-8");
		// 签名方法 01 RSA
		data.put("signMethod", "01");
		// 交易类型 01-消费
		data.put("txnType", "01");
		// 交易子类型 01:自助消费 02:订购 03:分期付款
		data.put("txnSubType", "01");
		// 业务类型
		data.put("bizType", "000201");
		// 渠道类型，07-PC，08-手机
		data.put("channelType", "08");
		// 前台通知地址 ，控件接入方式无作用
		data.put("frontUrl", "");
		// 后台通知地址
		data.put("backUrl",  AppConfig.getProperty("unionpay.backUrl"));
		// 接入类型，商户接入填0 0- 商户 ， 1： 收单， 2：平台商户
		data.put("accessType", "0");
		// 商户号码，请改成自己的商户号
		data.put("merId", "898110254111491");
		// 商户订单号，8-40位数字字母
		data.put("orderId", order.getOrderId());
		// 订单发送时间，取系统时间
		data.put("txnTime", new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));
		// 交易金额，单位分
		data.put("txnAmt",order.getTotalFee().intValue()+"");
		// 交易币种
		data.put("currencyCode", "156");
		// 请求方保留域，透传字段，查询、通知、对账文件中均会原样出现
		// data.put("reqReserved", "透传信息");
		// 订单描述，可不上送，上送时控件中会显示该信息
		// data.put("orderDesc", "订单描述");
        return data;
	}
	
	/**
	 * 解析银联通知参数，返回PayBack对象
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public PayBack getPayBack(Map<String,String> params) throws Exception {
		PayBack payBack=new PayBack();
		//银联流水号
		if(StringUtils.hasLength(params.get("queryId"))){
			payBack.setTradeNo(params.get("queryId"));
		}
		
		if(StringUtils.hasLength(params.get("orderId"))){
			payBack.setPayNo(params.get("orderId"));
		}
		
		if(StringUtils.hasLength(params.get("respMsg"))){
	        payBack.setStatus(params.get("respMsg"));
		}
		
		if(StringUtils.hasLength(params.get("txnAmt"))){
	        payBack.setAmount(Integer.parseInt(params.get("txnAmt")));
		}
		payBack.setOther(params.toString());
		payBack.setCreateTime(new Date());
		return payBack;
	}
}
