package com.tyt.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.RefundMsg;
import com.tyt.payment.service.OrderRefundMQService;
import com.tyt.util.SerialNumUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Description 订单调用三方退款MQ消息
 * <AUTHOR>
 * @Date 2019/10/16 15:17
 * @Param
 * @return
 **/
@Service("orderRefundMQService")
public class OrderRefundMQServiceImpl implements OrderRefundMQService {

    public static Logger logger = LoggerFactory.getLogger(OrderRefundMQServiceImpl.class);

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    /**
     * @return void
     * @Description 调用第三方支付进行商品订单退款MQ消息
     * <AUTHOR>
     * @Date 2019/12/19 18:14
     * @Param [totalAmount, amount, outTradeNo, tradeInfoId, userId, transforeBank, messageType, thirdpartyOrderSerialNum, payMethod]
     **/
    @Override
    public void sendThirdPartyRefund2MQ(String totalAmount, String amount, String outTradeNo, Integer tradeInfoId, String userId, Integer transforeBank, Integer messageType, String thirdpartyOrderSerialNum, String payMethod) {
        RefundMsg mqMsg = new RefundMsg();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(messageType);
        //订单总金额,单位:分
        mqMsg.setTotalAmount(totalAmount);
        //退款金额,单位:分
        mqMsg.setAmount(amount);
        //退款码
        mqMsg.setOutTradeNo(outTradeNo);
        //交易记录ID
        mqMsg.setTradeInfoId(tradeInfoId);
        //退款用户ID
        mqMsg.setUserId(userId);
        //退款方式: 1.支付宝 2.微信 3.易宝
        mqMsg.setTransforeBank(transforeBank);
        //第三方支付平台生成的订单号
        mqMsg.setThirdpartyOrderSerialNum(thirdpartyOrderSerialNum);
        //付款方式: 微信,支付宝,易宝,连连支付
        mqMsg.setPayMethod(payMethod);
        // 保存发送mq
        final String messageSerailNum = mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                //发送并mq信息并保存到数据库
                tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, messageType);
                tytMqMessageService.sendMqMessage(messageSerailNum, mqJson, messageType);
            }
        });
        // 关闭线程
        executorService.shutdown();
    }
}
