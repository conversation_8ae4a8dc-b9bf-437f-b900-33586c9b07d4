package com.tyt.payment.service.impl;

import java.util.List;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cache.CacheService;
import com.tyt.model.Price;
import com.tyt.payment.dao.PriceDao;
import com.tyt.payment.service.PriceService;
import com.tyt.util.Constant;
@Service("priceService")
public class PriceServiceImpl extends BaseServiceImpl<Price, Long> implements PriceService {

    private final static  String PRICE_KEY="price_key";
	
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;
	
	@Resource(name="priceDao")
	public void setBaseDao(BaseDao<Price, Long> priceDao) {
	        super.setBaseDao(priceDao);
	}
	
	@Override
	public List<Price> getEnabledList() {
		@SuppressWarnings("unchecked")
		List<Price> prices=(List<Price>) cacheService.getObject(PRICE_KEY);
		if(prices==null){
			prices=((PriceDao)(this.getBaseDao())).search(" entity.status=1", null);
			cacheService.setObject(PRICE_KEY, prices, Constant.CACHE_EXPIRE_TIME_24H);
		}
		return prices;
	}
		
}