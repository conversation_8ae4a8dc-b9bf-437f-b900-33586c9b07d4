package com.tyt.payment.service;

/**
 * @Description 商品订单调用三方退款MQ消息
 * <AUTHOR>
 * @Date 2019/10/16 15:12
 * @Param
 * @return
 **/
public interface OrderRefundMQService {

    /**
     * @return void
     * @Description 调用第三方支付进行商品订单退款MQ消息
     * <AUTHOR>
     * @Date 2019/12/19 18:14
     * @Param [totalAmount, amount, outTradeNo, tradeInfoId, userId, transforeBank, messageType, thirdpartyOrderSerialNum, payMethod]
     **/
    public void sendThirdPartyRefund2MQ(String totalAmount, String amount, String outTradeNo, Integer tradeInfoId, String userId, Integer transforeBank, Integer messageType, String thirdpartyOrderSerialNum, String payMethod);
}
