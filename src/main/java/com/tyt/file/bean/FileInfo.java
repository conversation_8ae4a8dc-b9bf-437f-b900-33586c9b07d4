package com.tyt.file.bean;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName FileInfo
 * @Description 文件信息实体类
 * <AUTHOR>
 * @Date 2019-01-16 20:17
 * @Version 1.0
 */
@Data
public class FileInfo {

    //主键
    private Long id;
    //业务ID
    private Long businessId;
    //文件类型:1.异常上报凭证图片,
    private Integer fileType;
    //文件原名称
    private String originalFileName;
    //文件名称
    private String  fileName;
    //文件路径
    private String  filePath;
    //文件大小
    private Long  fileSize;
    //文件后缀名称
    private String fileSuffix;
    //缩略文件名称
    private String  smallFileName;
    //缩略文件路径
    private String  smallFilePath;
    //创建人ID
    private Long createUserId;
    //创建人姓名
    private String  createUserName;
    //创建时间
    private Date ctime;
    //修改人ID
    private Long updateUserId;
    //修改人姓名
    private String updateUserName;
    //修改时间
    private Date utime;
    //文件状态: 1-正常,2-删除
    private Integer status;
    //排序ID
    private Integer sortId;

    /**
     * 凭证说明
     */
    private String  reason;

    private Integer type;

    private Long secondBusinessId;
}
