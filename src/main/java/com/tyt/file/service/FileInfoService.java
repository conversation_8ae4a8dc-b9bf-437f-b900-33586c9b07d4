package com.tyt.file.service;

import com.tyt.file.bean.FileInfo;
import com.tyt.model.User;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @ClassName FileInfoService
 * @Description 文件信息服务层接口
 * <AUTHOR>
 * @Date 2019-01-17 14:58
 * @Version 1.0
 */
public interface FileInfoService {

    /**
     * @Description   上传文件接口方法
     * <AUTHOR>
     * @Date  2020/3/24 16:57
     * @Param [user, businessId, fileType, uploadFile, typeName]
     * @return java.util.Map
     **/
    Map uploadFile(User user, Long businessId, Integer fileType,
                   MultipartFile uploadFile, String typeName) throws Exception;

    /**
     * @Description  获取文件列表方法
     * <AUTHOR>
     * @Date  2019/1/18 11:36
     * @Param [businessId, fileType]
     * @return java.util.List<com.tyt.file.bean.FileInfo>
     **/
    List<FileInfo> getFileInfoList(Long businessId, Integer fileType);

    /**
     * 获取文件列表方法
     * @param businessId
     * @param secondBusinessId
     * @param fileType
     * @return
     */
    List<FileInfo> getFileInfoList(Long businessId, Long secondBusinessId, Integer fileType);

    /**
     * @Description  获取单个文件信息的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:23
     * @Param [id]
     * @return com.tyt.file.bean.FileInfo
     **/
    FileInfo getSingleFileInfo(Long id);

    /**
     * @Description  更新文件信息状态的方法
     * <AUTHOR>
     * @Date  2019/1/18 12:28
     * @Param [curUser, id, status]
     * @return int
     **/
    int updateFileInfoStatus(User user, Long id, Integer status);

    /**
     * @Description  根据业务ID更新文件信息状态的方法
     * <AUTHOR>
     * @Date  2020/1/9 10:49
     * @Param [user, businessId, status]
     * @return int
     **/
    int updateStatusByBusinessId(User user, Long businessId, Integer status);

    Map uploadFiles(User curUser, Long id,  MultipartFile file, int i, String typeName,String reason,Long secondBusinessId)throws Exception;

    void deleteByBusinessId(Long id);

    void deleteByBusinessIds(List<Long> fileIds);


    int insertFileInfo(FileInfo fileInfo);
}
