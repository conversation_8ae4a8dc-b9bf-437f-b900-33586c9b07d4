package com.tyt.file.controller;

import com.google.common.collect.Sets;
import com.tyt.base.controller.BaseController;
import com.tyt.config.util.AppConfig;
import com.tyt.file.bean.UploadResult;
import com.tyt.model.ResultMsgBean;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/02/03 10:44
 */
@Controller
@RequestMapping("/plat/common/upload")
public class UploadController extends BaseController {

    public static final Set<String> TYPE_NAME_SET = Sets.newHashSet(
            "driver",
            "car",
            "user",
            "head",
            "merchant");


    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @RequestMapping("/img")
    @ResponseBody
    public ResultMsgBean img(@RequestParam("file") MultipartFile file, @RequestParam("typeName") String typeName){
        if(Objects.isNull(file) || file.isEmpty()){
            return new ResultMsgBean(ReturnCodeConstant.FILE_UPLOAD_EMPTY, "文件为空");
        }
        if(!TYPE_NAME_SET.contains(typeName)){
            return new ResultMsgBean(ReturnCodeConstant.FILE_UPLOAD_PARAM_ERR, "typeName错误");
        }
        if(!super.isAllImg(file)){
            return new ResultMsgBean(ReturnCodeConstant.FILE_UPLOAD_EMPTY, "图片格式错误！");
        }

        try {
            return new ResultMsgBean(200,"上传成功", upload(file, typeName));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new ResultMsgBean(500,"网络异常");
    }



    private UploadResult upload(MultipartFile file, String typeName)throws IOException{
        String fileName = super.renamePic(file, typeName);
        file.transferTo(new File(AppConfig.getProperty("picture.path.domain") + fileName));

        UploadResult result = new UploadResult();
        String prefix = tytConfigService.getStringValue("tyt_out_net_file_path_name_url","http://dev.teyuntong.net/manage_new/");

        result.setPath(prefix.concat(fileName));
        result.setRelativePath(fileName);
        return result;
    }

}
