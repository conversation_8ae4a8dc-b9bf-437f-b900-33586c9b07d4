package com.tyt.cargoStation.bean;

import com.tyt.infofee.bean.MqBaseMessageBean;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description  货站信息消息对象
 * <AUTHOR>
 * @Date  2019/12/7 11:56
 * @Param
 * @return
 **/
@Data
public class CargoStationInfoMsg extends MqBaseMessageBean implements Serializable {

    //货站ID
    private Long stationId;
    //货站昵称
    private String stationName;
    //通知的用户ID,用逗号分隔的字符串
    private String userIds;
    //货源数量
    private Integer goodsCount;

}
