package com.tyt.cargoStation.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.cargoStation.service.CargoStationInfoService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytCargoStationInfo;
import com.tyt.model.TytUserFollowRelation;
import com.tyt.transport.querybean.TransportCollectBean;
import com.tyt.transport.service.TransportMainService;
import com.tyt.userFollow.service.UserFollowRelationService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description  货站基本信息接口
 * <AUTHOR>
 * @Date  2019/12/3 17:41
 * @Param 
 * @return 
 **/
@Controller
@RequestMapping("/plat/cargoStation/info/")
public class CargoStationInfoController extends BaseController {

       @Autowired
       private CargoStationInfoService cargoStationInfoService;

       @Autowired
       private UserFollowRelationService userFollowRelationService;

       @Autowired
       private TransportMainService transportMainService;

       /**
        * @Description  货站基本信息接口
        * <AUTHOR>
        * @Date  2019/12/4 12:05
        * @Param [baseParameter, stationId, request, response]
        * @return com.tyt.model.ResultMsgBean
        **/
       @RequestMapping(value = {"cargoStationBasicInfo","cargoStationBasicInfo.action"}, method = RequestMethod.POST)
       @ResponseBody
       public ResultMsgBean cargoStationBasicInfo(BaseParameter baseParameter, Long stationId,
                                                  HttpServletRequest request, HttpServletResponse response) {
       ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
       try {
              //根据传入的参数，查询货站基本信息
              Map<String,Object> map = new HashMap<String,Object>();
              //获取登录用户Id
              Long userId = baseParameter.getUserId();
              if(userId == null) {
                     resultMsgBean.setMsg("登录用户Id不能为空！");
                     resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                     return resultMsgBean;
              }
              //获取货站Id
              if(stationId == null){
                     resultMsgBean.setMsg("货站Id不能为空！");
                     resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                     return resultMsgBean;
              }
              //获取货站基本信息
              TytCargoStationInfo cargoStationInfo = cargoStationInfoService.getCargoStationBasicInfo(userId, stationId);
              if(cargoStationInfo == null){
                  resultMsgBean.setMsg("货站基本信息不存在！");
                  resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                  return resultMsgBean;
              }
              //获取关注我的人数量
              Integer followedUserCount = userFollowRelationService.getFollowedUserCount(stationId);
              //关注状态：1.已关注 2.未关注/取消关注
              Integer followStatus = userFollowRelationService.getFollowStatus(userId, stationId);
              //今天是否发送过货源广播: 1.已发送 2.未发送
              Integer isSendBroadcast = this.isSendBroadcast(cargoStationInfo);
              map.put("cargoStationInfo", cargoStationInfo);
              map.put("followedUserCount",followedUserCount);
              //关注状态：1.已关注 2.未关注/取消关注
              map.put("followStatus", followStatus);
              //今天是否发送过货源广播: 1.已发送 2.未发送
              map.put("isSendBroadcast",isSendBroadcast);
              resultMsgBean.setCode(ReturnCodeConstant.OK);
              resultMsgBean.setMsg("查询货站基本信息成功！");
              resultMsgBean.setData(map);
       } catch (Exception e) {
              resultMsgBean.setCode(ResultMsgBean.ERROR);
              resultMsgBean.setMsg("服务器错误");
              logger.info("查询货站基本信息发生错误！"+e.getMessage());
              e.printStackTrace();
       }
       return resultMsgBean;
    }

    /**
     * @Description  今天是否发送过货源广播
     * <AUTHOR>
     * @Date  2019/12/6 10:49
     * @Param [cargoStationInfo]
     * @return java.lang.Integer
     **/
    private Integer isSendBroadcast(TytCargoStationInfo cargoStationInfo) {
       Integer isSendBroadcast = 2;
       Date sendBroadcastTime = cargoStationInfo.getSendBroadcastTime();
       if(sendBroadcastTime != null){
           //当天日期，格式 yyyy-MM-dd
           String todayDate = TimeUtil.formatDate(new Date());
           String sendBroadcastDate = TimeUtil.formatDate(sendBroadcastTime);
           //发送广播日期为当天日期，说明今天已经发送过广播
           if(todayDate.equals(sendBroadcastDate)){
               isSendBroadcast = 1;
           }
       }
       return isSendBroadcast;
    }

       /**
     * @Description  货站今日货源列表信息接口
     * <AUTHOR>
     * @Date  2019/12/5 15:51
     * @Param [baseParameter, stationId, queryMenuType, queryID, queryActionType, request, response]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"cargoStationTransport","cargoStationTransport.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean cargoStationTransport(BaseParameter baseParameter, Long stationId,
                                               @RequestParam(value = "queryMenuType", defaultValue = "5") Integer queryMenuType,
                                               @RequestParam(value = "queryID", defaultValue = "0") Long queryID,
                                               @RequestParam(value = "queryActionType", defaultValue = "1") Integer queryActionType,
                                               HttpServletRequest request, HttpServletResponse response) {
           ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
           try {
                  //货源聚合信息实体类(货源列表、货源数量)
                  TransportCollectBean transportCollectBean = new TransportCollectBean();
                  //获取登录用户Id
                  Long userId = baseParameter.getUserId();
                  if(userId == null) {
                         resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                         resultMsgBean.setMsg("登录用户Id不能为空！");
                         return resultMsgBean;
                  }
                  //获取货站Id
                  if(stationId == null){
                         resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                         resultMsgBean.setMsg("货站Id不能为空！");
                         return resultMsgBean;
                  }
                  if (queryID == null || queryID.longValue() < 0) {
                         resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                         resultMsgBean.setMsg("queryID参数错误");
                         return resultMsgBean;
                  }
                  if (queryMenuType == null || queryMenuType < 1 || queryMenuType > 5) {
                         resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                         resultMsgBean.setMsg("queryMenuType参数错误");
                         return resultMsgBean;
                  }
                  if (queryActionType == null || queryActionType < 1 || queryActionType > 2) {
                         resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                         resultMsgBean.setMsg("queryActionType参数错误");
                         return resultMsgBean;
                  }
                  //根据传入的参数，查询货站发布的今日货源信息
                  transportCollectBean = transportMainService.getInfoFeeMyPublish(queryMenuType, stationId, queryActionType, queryID, baseParameter.getClientSign(), null, null);
                  resultMsgBean.setCode(ReturnCodeConstant.OK);
                  resultMsgBean.setMsg("查询货站今日货源列表信息成功！");
                  resultMsgBean.setData(transportCollectBean);
           } catch (Exception e) {
                  resultMsgBean.setCode(ResultMsgBean.ERROR);
                  resultMsgBean.setMsg("服务器错误");
                  logger.info("查询货站今日货源列表信息发生错误！"+e.getMessage());
                  e.printStackTrace();
           }
           return resultMsgBean;
    }

       /**
        * @Description  货站之星列表数据接口
        * <AUTHOR>
        * @Date  2019/12/5 18:26
        * @Param [baseParameter, request, response]
        * @return com.tyt.model.ResultMsgBean
        **/
       @RequestMapping(value = {"getCargoStationList","getCargoStationList.action"}, method = RequestMethod.POST)
       @ResponseBody
       public ResultMsgBean getCargoStationList(BaseParameter baseParameter,
                                                HttpServletRequest request, HttpServletResponse response) {
              ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
              try {
                     //根据传入的参数，查询货站基本信息
                     Map<String,Object> map = new HashMap<String,Object>();
                     //获取登录用户Id
                     Long userId = baseParameter.getUserId();
                     if(userId == null) {
                            resultMsgBean.setMsg("登录用户Id不能为空！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            return resultMsgBean;
                     }
                     //获取货站之星列表数据
                     List<TytCargoStationInfo> cargoStationList = cargoStationInfoService.getCargoStationList();
                     map.put("cargoStationList", cargoStationList);

                     resultMsgBean.setCode(ReturnCodeConstant.OK);
                     resultMsgBean.setMsg("查询货站之星列表数据成功！");
                     resultMsgBean.setData(map);
              } catch (Exception e) {
                     resultMsgBean.setCode(ResultMsgBean.ERROR);
                     resultMsgBean.setMsg("服务器错误");
                     logger.info("查询货站之星列表数据发生错误！"+e.getMessage());
                     e.printStackTrace();
              }
              return resultMsgBean;
       }

       /**
        * @Description  发送货源广播接口(每天一次)
        * <AUTHOR>
        * @Date  2019/12/6 10:26
        * @Param [baseParameter, request, response]
        * @return com.tyt.model.ResultMsgBean
        **/
       @RequestMapping(value = {"sendGoodsBroadcast","sendGoodsBroadcast.action"}, method = RequestMethod.POST)
       @ResponseBody
       public ResultMsgBean sendGoodsBroadcast(BaseParameter baseParameter,Long stationId,
                                          HttpServletRequest request, HttpServletResponse response) {
              ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
              try {
                     Map<String,Object> map = new HashMap<String,Object>();
                     //获取登录用户Id
                     Long userId = baseParameter.getUserId();
                     if(userId == null) {
                            resultMsgBean.setMsg("登录用户Id不能为空！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            return resultMsgBean;
                     }
                     //获取货站Id
                     if(stationId == null){
                            resultMsgBean.setMsg("货站Id不能为空！");
                            resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                            return resultMsgBean;
                     }
                     //获取货站基本信息
                     TytCargoStationInfo cargoStationInfo = cargoStationInfoService.getCargoStationBasicInfo(userId, stationId);
                     if(cargoStationInfo == null){
                            resultMsgBean.setMsg("货站基本信息不存在！");
                            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                            return resultMsgBean;
                     }
                     //今天是否发送过货源广播: 1.已发送 2.未发送
                     Integer isSendBroadcast = this.isSendBroadcast(cargoStationInfo);
                     if(isSendBroadcast != null && isSendBroadcast.intValue() == 1){
                            resultMsgBean.setMsg("您今天已发送过货源广播,每天只可发送1次！");
                            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                            return resultMsgBean;
                     }
                     //查询关注我的人列表
                     TytUserFollowRelation userFollowRelation = new TytUserFollowRelation();
                     userFollowRelation.setFollowUserId(stationId);
                     userFollowRelation.setFollowStatus(1);
                     List<TytUserFollowRelation> followedUserList = userFollowRelationService.findList(userFollowRelation);
                     if(followedUserList == null || followedUserList.size() <= 0){
                            resultMsgBean.setMsg("还没有人关注你哦！");
                            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                            return resultMsgBean;
                     }
                     //查询货站发布的今日货源信息
                     TransportCollectBean transportCollectBean = transportMainService.getInfoFeeMyPublish(5, stationId, 1, 0L,baseParameter.getClientSign(), null, null);
                     if(transportCollectBean == null || transportCollectBean.getGoodsCount() <= 0){
                            resultMsgBean.setMsg("今天还没有发布货源呢！");
                            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
                            return resultMsgBean;
                     }
                     //发送货源广播信息
                     cargoStationInfoService.saveGoodsBroadcast(cargoStationInfo, transportCollectBean,followedUserList);
                     //今天是否发送过货源广播: 1.已发送 2.未发送
                     map.put("isSendBroadcast",1);
                     resultMsgBean.setCode(ReturnCodeConstant.OK);
                     resultMsgBean.setMsg("发送货源广播成功！");
                     resultMsgBean.setData(map);
              } catch (Exception e) {
                     resultMsgBean.setCode(ResultMsgBean.ERROR);
                     resultMsgBean.setMsg("服务器错误");
                     logger.info("发送货源广播发生错误！"+e.getMessage());
                     e.printStackTrace();
              }
              return resultMsgBean;
       }
}
