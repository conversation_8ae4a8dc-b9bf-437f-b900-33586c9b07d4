package com.tyt.cargoStation.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.cargoStation.dao.CargoStationInfoDao;
import com.tyt.model.TytCargoStationInfo;
import org.springframework.stereotype.Repository;

/***
 * @Description  货站基本信息数据层实现类
 * <AUTHOR>
 * @Date  2019/12/3 16:09
 * @Param
 * @return
 **/
@Repository("cargoStationInfoDao")
public class CargoStationInfoDaoImpl extends BaseDaoImpl<TytCargoStationInfo, Long>  implements CargoStationInfoDao {

    public CargoStationInfoDaoImpl() {
        this.setEntityClass(TytCargoStationInfo.class);
    }

}
