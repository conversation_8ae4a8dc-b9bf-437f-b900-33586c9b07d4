package com.tyt.cargoStation.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytCargoStationInfo;
import com.tyt.model.TytUserFollowRelation;
import com.tyt.transport.querybean.TransportCollectBean;

import java.util.List;

/**
 * @Description  货站基本信息服务层
 * <AUTHOR>
 * @Date  2019/12/3 16:13
 * @Param
 * @return
 **/
public interface CargoStationInfoService extends BaseService<TytCargoStationInfo, Long> {

       /**
        * @Description  获取车站基本信息方法
        * <AUTHOR>
        * @Date  2019/12/4 10:53
        * @Param []
        * @return com.tyt.model.TytCargoStationInfo
        **/
       public TytCargoStationInfo getCargoStationBasicInfo(Long userId,Long stationId);

       /**
        * @Description  获取货站之星列表数据
        * <AUTHOR>
        * @Date  2019/12/5 18:06
        * @Param []
        * @return java.util.List<com.tyt.model.TytCargoStationInfo>
        **/
       public List<TytCargoStationInfo> getCargoStationList();

       /**
        * @Description  发送货源广播方法
        * <AUTHOR>
        * @Date  2019/12/6 11:11
        * @Param [cargoStationInfo, transportCollectBean]
        * @return void
        **/
       public void saveGoodsBroadcast(TytCargoStationInfo cargoStationInfo, TransportCollectBean transportCollectBean,List<TytUserFollowRelation> followedUserList);
}
