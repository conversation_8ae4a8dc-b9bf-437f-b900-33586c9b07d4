package com.tyt.cargoStation.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.cargoStation.bean.CargoStationInfoMsg;
import com.tyt.cargoStation.service.CargoStationInfoService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.model.CarInsuranceInquiry;
import com.tyt.model.TytCargoStationInfo;
import com.tyt.model.TytUserFollowRelation;
import com.tyt.permission.bean.MqUserPermissionMsg;
import com.tyt.transport.querybean.TransportCollectBean;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.TytSourceService;
import com.tyt.user.service.UserService;
import com.tyt.userFollow.service.UserFollowRelationService;
import com.tyt.util.SerialNumUtil;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Description  货站基本信息服务层实现类
 * <AUTHOR>
 * @Date  2019/12/3 16:24
 * @Param 
 * @return 
 **/
@Service("cargoStationInfoService")
public class CargoStationInfoServiceImpl extends BaseServiceImpl<TytCargoStationInfo, Long> implements CargoStationInfoService {

    public Logger logger = LoggerFactory.getLogger(this.getClass());

    //服务器图片路径(新规则)
    public static final String TYT_SERVER_PICTURE_URL_NEW = "tyt_server_picture_url_new";

    @Resource(name = "tytSourceService")
    private TytSourceService tytSourceService;

    @Resource(name = "userService")
    private UserService userService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Autowired
    private UserFollowRelationService userFollowRelationService;

    @Resource(name = "cargoStationInfoDao")
    public void setBaseDao(BaseDao<TytCargoStationInfo, Long> cargoStationInfoDao) {
        super.setBaseDao(cargoStationInfoDao);
    }

    /**
     * @Description  获取货站基本信息实现方法
     * <AUTHOR>
     * @Date  2019/12/4 10:54
     * @Param [userId, stationId]
     * @return com.tyt.model.TytCargoStationInfo
     **/
    @Override
    public TytCargoStationInfo getCargoStationBasicInfo(Long userId, Long stationId) {

        String sql = "select i.id id,i.station_id stationId," +
                " u.user_name stationName," +
                " i.bg_url bgUrl,i.head_url headUrl," +
                " i.is_star isStar,i.influence_score influenceScore," +
                " i.station_labels stationLabels," +
                " i.individual_sign individualSign," +
                " i.station_location stationLocation," +
                " i.is_enterprise isEnterprise,i.is_member isMember," +
                " t.transport_score transportScore,t.ts_rank transportRank," +
                " i.transport_score_degree transportScoreDegree," +
                " i.goods_scale goodsScale,i.service_degree serviceDegree," +
                " i.special_operate specialOperate,i.operate_history operateHistory," +
                " i.industry_service industryService,i.send_broadcast_time sendBroadcastTime," +
                " i.remark remark " +
                " from tyt_cargo_station_info i " +
                " left join tyt_user u on i.station_id = u.id " +
                " left join tyt_recommend.api_data_user_credit_info_two t on i.station_id = t.user_id " +
                " where i.station_id = :stationId and i.is_star = 1";

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("stationId", stationId);

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("stationId", Hibernate.LONG);
        scalarMap.put("stationName", Hibernate.STRING);
        scalarMap.put("bgUrl", Hibernate.STRING);
        scalarMap.put("headUrl", Hibernate.STRING);
        scalarMap.put("isStar", Hibernate.INTEGER);
        scalarMap.put("influenceScore", Hibernate.DOUBLE);
        scalarMap.put("stationLabels", Hibernate.STRING);
        scalarMap.put("individualSign", Hibernate.STRING);
        scalarMap.put("stationLocation", Hibernate.STRING);
        scalarMap.put("isEnterprise", Hibernate.INTEGER);
        scalarMap.put("isMember", Hibernate.INTEGER);
        scalarMap.put("transportScore", Hibernate.STRING);
        scalarMap.put("transportRank", Hibernate.STRING);
        scalarMap.put("transportScoreDegree", Hibernate.STRING);
        scalarMap.put("goodsScale", Hibernate.STRING);
        scalarMap.put("serviceDegree", Hibernate.STRING);
        scalarMap.put("specialOperate", Hibernate.STRING);
        scalarMap.put("operateHistory", Hibernate.STRING);
        scalarMap.put("industryService", Hibernate.STRING);
        scalarMap.put("sendBroadcastTime", Hibernate.DATE);
        scalarMap.put("remark", Hibernate.STRING);

        //服务器图片路径(新规则)
        String tytServerPictureUrlNew = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_NEW);
        //查询货站基本信息
        List<TytCargoStationInfo> cargoStationInfos = this.getBaseDao().search(sql, scalarMap, TytCargoStationInfo.class, params);
        if(cargoStationInfos != null && cargoStationInfos.size() > 0){
            TytCargoStationInfo cargoStationInfo = cargoStationInfos.get(0);
            //货站背景Url
            String bgUrl = cargoStationInfo.getBgUrl();
            //货站头像Url
            String headUrl = cargoStationInfo.getHeadUrl();
            if(StringUtils.isNotBlank(bgUrl)){
                cargoStationInfo.setBgUrl(tytServerPictureUrlNew + bgUrl);
            }
            if(StringUtils.isNotBlank(headUrl)){
                cargoStationInfo.setHeadUrl(tytServerPictureUrlNew + headUrl);
            }
            //返回货站基本信息
            return cargoStationInfo;
        }
        return null;
    }

    /**
     * @Description  获取货站之星列表数据
     * <AUTHOR>
     * @Date  2019/12/5 18:06
     * @Param []
     * @return java.util.List<com.tyt.model.TytCargoStationInfo>
     **/
    @Override
    public List<TytCargoStationInfo> getCargoStationList() {

        String sql = "SELECT " +
                " i.id id, " +
                " i.station_id stationId, " +
                " u.user_name stationName, " +
                " i.head_url headUrl, " +
                " i.is_star isStar, " +
                " (select count(*) from tyt_user_follow_relation r where r.follow_status = 1 and r.follow_user_id = i.station_id) followedUserCount " +
                "FROM " +
                " tyt_cargo_station_info i " +
                " LEFT JOIN tyt_user u ON i.station_id = u.id " +
                "where i.is_star = 1";
        Map<String, Object> params = new HashMap<String, Object>();

        Map<String, org.hibernate.type.Type> scalarMap = new HashMap<String, org.hibernate.type.Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("stationId", Hibernate.LONG);
        scalarMap.put("stationName", Hibernate.STRING);
        scalarMap.put("headUrl", Hibernate.STRING);
        scalarMap.put("isStar", Hibernate.INTEGER);
        scalarMap.put("followedUserCount", Hibernate.INTEGER);

        //服务器图片路径(新规则)
        String tytServerPictureUrlNew = tytConfigService.getStringValue(TYT_SERVER_PICTURE_URL_NEW);

        //查询货站之星列表数据
        List<TytCargoStationInfo> cargoStationList = this.getBaseDao().search(sql, scalarMap, TytCargoStationInfo.class, params);
        if(cargoStationList != null && cargoStationList.size() > 0){
            for (TytCargoStationInfo cargoStationInfo : cargoStationList) {
                //货站头像Url
                String headUrl = cargoStationInfo.getHeadUrl();
                cargoStationInfo.setHeadUrl(tytServerPictureUrlNew + headUrl);
            }
        }
        return cargoStationList;
    }

    /**
     * @Description  发送货源广播方法
     * <AUTHOR>
     * @Date  2019/12/6 11:11
     * @Param [cargoStationInfo, transportCollectBean]
     * @return void
     **/
    @Override
    public void saveGoodsBroadcast(TytCargoStationInfo cargoStationInfo, TransportCollectBean transportCollectBean,List<TytUserFollowRelation> followedUserList) {
        //货站ID
        Long stationId = cargoStationInfo.getStationId();
        //货站名称
        String stationName = cargoStationInfo.getStationName();
        //货源数量
        Integer goodsCount = transportCollectBean.getGoodsCount();

        //1.更新货站发送货源广播时间
        String sql = "update tyt_cargo_station_info set send_broadcast_time =:sendBroadcastTime where station_id =:stationId";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("sendBroadcastTime", new Date());
        params.put("stationId", stationId);
        //更新执行的结果
        int result = this.getBaseDao().executeUpdateSql(sql, params);

        //2.发送货源广播的MQ消息
        //通知的用户ID,用逗号分隔的字符串
        List<String> userIds = new ArrayList<String>();
        if(followedUserList != null && followedUserList.size() > 0){
            for (TytUserFollowRelation followedUser : followedUserList) {
                String userId = followedUser.getUserId().toString();
                userIds.add(userId);
            }
            //50个userId为一组，发送货源广播的mq消息
            List<List<String>> userIdList = splitList(userIds, 50);
            if(userIdList != null && userIdList.size() > 0){
                for (List<String> userIdSubs : userIdList) {
                    //调用发送货源广播的MQ消息的方法
                    sendGoodsBroadcastMq(stationId, stationName, goodsCount,userIdSubs);
                }
            }
        }
    }

    /**
     * @Description  List按指定长度分割--使用guava实现
     * <AUTHOR>
     * @Date  2020/1/2 11:46
     * @Param [list, groupSize]
     * @return java.util.List<java.util.List<java.lang.String>>
     **/
    private  List<List<String>> splitList(List<String> list , int groupSize){
        return  Lists.partition(list, groupSize); // 使用guava
    }

    /**
     * @Description  发送货源广播的MQ消息
     * <AUTHOR>
     * @Date  2019/12/7 13:34
     * @Param [stationId, stationName, goodsCount]
     * @return void
     **/
    private void sendGoodsBroadcastMq(Long stationId, String stationName, Integer goodsCount,List<String> userIds) {
        CargoStationInfoMsg mqMsg = new CargoStationInfoMsg();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(MqBaseMessageBean.SEND_GOODS_BROADCAST_MESSAGE);

        mqMsg.setStationId(stationId);
        mqMsg.setStationName(stationName);
        mqMsg.setGoodsCount(goodsCount);
        //通知的用户ID,用逗号分隔的字符串
        mqMsg.setUserIds(String.join(",", userIds));

        // 保存发送mq
        final String messageSerailNum =mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        final int messageType = mqMsg.getMessageType();

        logger.info("====发送货源广播的MQ消息内容为:===="+mqJson);
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                //发送并mq信息并保存到数据库
                tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, messageType);
                tytMqMessageService.sendMqMessage(messageSerailNum, mqJson, messageType);
            }
        });
        // 关闭线程
        executorService.shutdown();
    }
}
