package com.tyt.customservice.service;

import com.tyt.customservice.dto.CustomServiceRuleListReq;
import com.tyt.plat.entity.base.TytCustomServiceRule;
import com.tyt.plat.mapper.base.TytCustomServiceRuleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CustomServiceRuleServiceImpl implements CustomServiceRuleService{
    @Autowired
    private TytCustomServiceRuleMapper tytCustomServiceRuleMapper;

    @Override
    public List<TytCustomServiceRule> getCustomServiceRuleList(CustomServiceRuleListReq customServiceRuleListReq) {
        return tytCustomServiceRuleMapper.selectList(customServiceRuleListReq);
    }

    @Override
    public TytCustomServiceRule getCustomServiceRuleDetails(Long id) {
        return tytCustomServiceRuleMapper.selectByPrimaryKey(id);
    }
}
