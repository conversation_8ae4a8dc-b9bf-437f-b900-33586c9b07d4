package com.tyt.customservice.service;

import com.tyt.customservice.dto.CustomServiceRuleListReq;
import com.tyt.plat.entity.base.TytCustomServiceRule;

import java.util.List;

public interface CustomServiceRuleService {
    /**
     * 获取客户服务列表信息
     * @param customServiceRuleListReq
     * @return
     */
    List<TytCustomServiceRule> getCustomServiceRuleList(CustomServiceRuleListReq customServiceRuleListReq);

    /**
     * 根据id获取客户服务详情
     * @param id
     * @return
     */
    TytCustomServiceRule getCustomServiceRuleDetails(Long id);
}
