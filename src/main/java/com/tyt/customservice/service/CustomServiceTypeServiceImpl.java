package com.tyt.customservice.service;

import com.tyt.plat.entity.base.TytCustomServiceType;
import com.tyt.plat.mapper.base.TytCustomServiceTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CustomServiceTypeServiceImpl implements CustomServiceTypeService{
    @Autowired
    private TytCustomServiceTypeMapper tytCustomServiceTypeMapper;

    @Override
    public List<TytCustomServiceType> getAll() {
        return tytCustomServiceTypeMapper.selectAll();
    }
}
