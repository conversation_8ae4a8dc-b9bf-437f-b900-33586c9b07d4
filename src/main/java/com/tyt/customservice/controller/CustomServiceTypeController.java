package com.tyt.customservice.controller;

import com.tyt.customservice.service.CustomServiceTypeService;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytCustomServiceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客户服务分类
 */
@RestController
@RequestMapping(value = "custom/service/type")
public class CustomServiceTypeController {
    @Autowired
    private CustomServiceTypeService customServiceTypeService;

    /**
     * 获取所有客户服务分类信息
     * @return
     */
    @GetMapping(value = "get/all")
    public ResultMsgBean getCustomServiceTypeAll() {
        List<TytCustomServiceType> all = customServiceTypeService.getAll();
        return ResultMsgBean.successResponse(all);
    }
}
