package com.tyt.customservice.controller;

import com.tyt.customservice.dto.CustomServiceRuleListReq;
import com.tyt.customservice.service.CustomServiceRuleService;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytCustomServiceRule;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 客户服务
 */
@RestController
@RequestMapping(value = "custom/service/rule")
public class CustomServiceRuleController {
    @Autowired
    private CustomServiceRuleService customServiceRuleService;

    /**
     * 获取客户服务列表信息
     * @param customServiceRuleListReq
     * @return
     */
    @GetMapping(value = "get/list")
    public ResultMsgBean getCustomServiceRuleList(CustomServiceRuleListReq customServiceRuleListReq) {
        List<TytCustomServiceRule> customServiceRuleList = customServiceRuleService.getCustomServiceRuleList(customServiceRuleListReq);
        return ResultMsgBean.successResponse(customServiceRuleList);
    }

    /**
     * 获取客户服务详情信息
     * @param id
     * @return
     */
    @GetMapping(value = "get/details")
    public ResultMsgBean getCustomServiceRuleDetails(Long id){
        if (Objects.isNull(id)){
            return ResultMsgBean.failResponse(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE,"id不能为空");
        }
        TytCustomServiceRule customServiceRuleDetails = customServiceRuleService.getCustomServiceRuleDetails(id);
        return ResultMsgBean.successResponse(customServiceRuleDetails);
    }
}
