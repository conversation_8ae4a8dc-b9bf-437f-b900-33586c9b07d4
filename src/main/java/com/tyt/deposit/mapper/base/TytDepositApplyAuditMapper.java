package com.tyt.deposit.mapper.base;

import com.tyt.deposit.bean.RefundAuditStatusBean;
import com.tyt.deposit.entity.base.TytDepositApplyAudit;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytDepositApplyAuditMapper extends CustomBaseMapper<TytDepositApplyAudit> {

    List<RefundAuditStatusBean> selectRefundAuditStatusByParam(@Param("userId") Long userId,@Param("finalAuditStatus") Integer finalAuditStatus);

    List<TytDepositApplyAudit> selectRefundAuditByUserId(@Param("userId") Long userId, @Param("auditStatus") Integer auditStatus,@Param("ensureType")  Integer ensureType);

    Integer selectCountUserIdAndType(@Param("userId") Long userId,@Param("ensureAmountType")  Integer ensureAmountType);
}