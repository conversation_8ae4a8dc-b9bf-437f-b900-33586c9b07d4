package com.tyt.deposit.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.deposit.entity.base.TytDepositOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytDepositOrderMapper extends CustomBaseMapper<TytDepositOrder> {

    List<TytDepositOrder> selectDepositOrderListByUserId(@Param("userId") Long userId);
}