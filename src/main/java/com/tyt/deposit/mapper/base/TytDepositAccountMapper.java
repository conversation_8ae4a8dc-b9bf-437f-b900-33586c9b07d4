package com.tyt.deposit.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.deposit.entity.base.TytDepositAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytDepositAccountMapper extends CustomBaseMapper<TytDepositAccount> {

    List<TytDepositAccount> selectDepositAccountByUserId(@Param("userId") Long userId);

    TytDepositAccount selectDepositAccountByUserIdAndType(@Param("userId") Long userId, @Param("type") Integer type);
}