package com.tyt.deposit.mapper.base;

import com.tyt.deposit.entity.base.TytGoodsRefreshConfig;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytGoodsRefreshConfigMapper extends CustomBaseMapper<TytGoodsRefreshConfig> {

    /**
     * 返回没有配置用户的所有有效配置，按修改时间倒序
     */
    List<TytGoodsRefreshConfig> selectConfigsWithoutUser();

    /**
     * 根据userId返回所有有效的刷新配置和曝光限制配置
     */
    List<TytGoodsRefreshConfig> selectConfigByUserId(@Param("userId") Long userId);

    /**
     * 返回货源匹配的好货模型数据，只有普货才有好货模型，查询结果需要进一步过滤
     */
    List<TytGoodsRefreshConfig> getInstantGrabList(@Param("userId") Long userId);

}