package com.tyt.deposit.mapper.base;


import com.tyt.deposit.bean.GoodsRefreshConfigDto;
import com.tyt.deposit.entity.base.TytGoodsRefreshUser;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytGoodsRefreshUserMapper extends CustomBaseMapper<TytGoodsRefreshUser> {
    /**
     * @return List<RefreshContentDto>
     * @Param userId:
     */
    List<GoodsRefreshConfigDto> selectExcellentGoodsContentByUserId(@Param("userId") Long userId);
}