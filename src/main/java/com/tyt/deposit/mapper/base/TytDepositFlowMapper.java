package com.tyt.deposit.mapper.base;

import com.tyt.deposit.bean.DepositFlowBean;
import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.deposit.entity.base.TytDepositFlow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytDepositFlowMapper extends CustomBaseMapper<TytDepositFlow> {

    List<DepositFlowBean> selectDepositFlowListByUserId(@Param("userId") Long userId);



}