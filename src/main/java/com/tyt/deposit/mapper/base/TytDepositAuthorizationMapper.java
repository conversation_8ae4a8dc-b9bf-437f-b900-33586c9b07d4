package com.tyt.deposit.mapper.base;

import com.tyt.plat.commons.tools.CustomBaseMapper;
import com.tyt.deposit.entity.base.TytDepositAuthorization;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytDepositAuthorizationMapper extends CustomBaseMapper<TytDepositAuthorization> {

    TytDepositAuthorization selectDepositAuthByUserId(@Param("userId") Long userId);

    TytDepositAuthorization getByUserId(@Param("userId") Long userId);
}