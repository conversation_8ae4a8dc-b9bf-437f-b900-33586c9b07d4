package com.tyt.deposit.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/09/13 16:52
 */

@Data
public class DepositBlockCheckDTO implements Serializable {

    /**
     * 是否封禁 true 封禁 false 未封禁
     */
    private boolean block;

    /**
     * 封禁类型(1 运营类 2 违约类)
     */
    private Integer blockType;

    /**
     * 是否永久封禁(0 不是 1 是)
     */
    private boolean permanentBlock;

    /**
     * 封禁结束时间
     */
    private Date blockEndTime;

    /**
     * 封禁天数
     */
    private Integer blockDuration;

    /**
     * 封禁提示信息
     */
    private String errMessage;

}
