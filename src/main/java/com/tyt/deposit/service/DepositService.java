package com.tyt.deposit.service;

import com.tyt.deposit.bean.DepositDetailBean;
import com.tyt.deposit.bean.DepositFlowBean;
import com.tyt.deposit.bean.ExcellentGoodsRemainCountBean;
import com.tyt.deposit.entity.base.TytDepositAccount;
import com.tyt.deposit.entity.base.TytDepositApplyAudit;
import com.tyt.deposit.entity.base.TytDepositAuthorization;
import com.tyt.deposit.entity.base.TytDepositBlock;
import com.tyt.deposit.pojo.dto.DepositBlockCheckDTO;
import com.tyt.deposit.pojo.vo.AppletEntranceInfo;
import com.tyt.deposit.pojo.vo.TransactionRecordsVo;
import com.tyt.model.ResultMsgBean;

import java.util.List;


/**
 * @ClassName：DepositService
 * @Author: TYT
 * @Date: 2023/6/7 13:49
 * @Description:
 */
public interface DepositService {

    TytDepositAuthorization selectDepositAuthByUserId(Long userId);

    TytDepositAuthorization selectDepositAuthByUserIdAndConfig(Long userId);

    List<DepositFlowBean> selectDepositFlowListByUserId(Long userId);

    DepositDetailBean getUserDepositDetail(Long userId);

    boolean isFirstTransportOwner(Long userId);

    ResultMsgBean refundApply(Long userId, ResultMsgBean rm);

    TytDepositAccount getDepositAccount(Long userId, Integer balanceType);

    List<TytDepositApplyAudit> selectRefundAuditByUserId(Long userId, Integer auditStatus, Integer ensureType);

    /**
     * 优车发货扣减发货次数
     *
     * @param userId
     * @param type 1：电议无价、2：电议有价、3：一口价
     */
    void deductionCount(Long userId, Integer type);

    /**
     * 查询剩余发货次数
     *
     * @param userId
     * @return
     */
    ExcellentGoodsRemainCountBean getRemainingCount(Long userId);

    TytDepositBlock getDepositBlock(Long userId);

    DepositBlockCheckDTO checkDepositBlock(Long userId, boolean isToast);

    /**
     * 查询优车成交记录
     *
     * @param userId
     * @return
     */
    TransactionRecordsVo getTransactionRecords(Long userId) throws Exception;

    /**
     * 获取最小刷新间隔
     *
     * @param userId
     * @return
     */
    Integer minRefreshInterval(Long userId);

    /**
     * 获取货APP 跳转小程序极简发货参数信息
     * @param userId
     * @return 返回null 或 isShowGoodsApplet = 0 则不显示极简发货入口
     */
    AppletEntranceInfo getAppletEntranceInfo(Long userId);
}
