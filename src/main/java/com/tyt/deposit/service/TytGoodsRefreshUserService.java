package com.tyt.deposit.service;


import com.tyt.deposit.bean.RefreshContentDto;
import com.tyt.deposit.bean.RefreshContentMinCountDto;
import com.tyt.model.Transport;

/**
 * @author: helian
 * @since: 2023/10/13 17:09
 */
public interface TytGoodsRefreshUserService {

    /**
     * 获取频率最小的一条配置
     */
    RefreshContentDto getMinIntervalContent(Long userId);

    RefreshContentMinCountDto selectMinIntervalAndThisFrequencyByUserId(Long userId);

    /**
     * 6470 修改：
     * 1. 优货也走好货模型
     * 2. 除了优货、普货，其他类型不走好货模型
     */
    boolean isCheckInstantGrab(Transport transport);

}
