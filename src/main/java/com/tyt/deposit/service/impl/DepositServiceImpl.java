package com.tyt.deposit.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.acvitity.service.TransportOrdersRiskService;
import com.tyt.deposit.bean.*;
import com.tyt.deposit.entity.base.TytDepositAccount;
import com.tyt.deposit.entity.base.TytDepositApplyAudit;
import com.tyt.deposit.entity.base.TytDepositAuthorization;
import com.tyt.deposit.entity.base.TytDepositBlock;
import com.tyt.deposit.enums.AuthStatusEnum;
import com.tyt.deposit.mapper.base.*;
import com.tyt.deposit.pojo.dto.DepositBlockCheckDTO;
import com.tyt.deposit.pojo.vo.DepositAuthorizeConfig;
import com.tyt.deposit.pojo.vo.TransactionRecordsVo;
import com.tyt.deposit.pojo.vo.AppletEntranceInfo;
import com.tyt.deposit.service.DepositService;
import com.tyt.deposit.service.TytGoodsRefreshUserService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.plat.entity.base.ExcellentGoodsUsingRecord;
import com.tyt.plat.entity.base.TytOwnerUserWhitelist;
import com.tyt.plat.entity.base.TytUserIdentityLabel;
import com.tyt.plat.mapper.base.ExcellentGoodsGroupMapper;
import com.tyt.plat.mapper.base.ExcellentGoodsUsingRecordMapper;
import com.tyt.plat.mapper.base.TytOwnerUserWhitelistMapper;
import com.tyt.plat.mapper.base.TytUserIdentityLabelMapper;
import com.tyt.plat.utils.DateUtil;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TytTransportDispatchService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.unionpay.acp.sdk.SDKConstants.COMMA;

/**
 * @ClassName：DepositServiceImpl
 * @Author: TYT
 * @Date: 2023/6/7 13:49
 * @Description:
 */
@Service("depositService")
@Slf4j
@RequiredArgsConstructor
public class DepositServiceImpl implements DepositService {

    /**
     * 优货置顶配置，逗号拼接的3个整数（开关状态，刷新间隔，刷新次数）
     */
    private static final String RESEND_EXCELLENT_KEY = "tyt:config:task:tsTopExcellentConfig";

    public static final String EXCELLENT_GOODS_GROUP_DEFAULT_CONFIG = "excellent_goods_group_default_config";

    /**
     * 优活刷新开关  1：开 2 关
     */
    private static final String CONFIG_SWITCH = "1";

    @Resource(name = "tytGoodsRefreshConfigService")
    private TytGoodsRefreshUserService tytGoodsRefreshUserService;

    @Autowired
    private TytDepositAuthorizationMapper authorizationMapper;

    @Autowired
    private TytDepositFlowMapper flowMapper;

    @Autowired
    private TytDepositAccountMapper accountMapper;

    @Autowired
    private TytDepositApplyAuditMapper applyAuditMapper;

    @Autowired
    private TransportMainService transportMainService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;

    @Autowired
    ExcellentGoodsGroupMapper excellentGoodsGroupMapper;

    @Autowired
    ExcellentGoodsUsingRecordMapper excellentGoodsUsingRecordMapper;

    private final TytDepositBlockMapper depositBlockMapper;
    @Autowired
    private TytOwnerUserWhitelistMapper tytOwnerUserWhitelistMapper;

    @Autowired
    private TransportOrdersRiskService transportOrdersRiskService;

    @Autowired
    private TytTransportDispatchService tytTransportDispatchService;

    @Autowired
    private UserService userService;

    @Resource(name = "tytConfigService")
    private TytConfigService configService;

    @Autowired
    private TytUserIdentityLabelMapper tytUserIdentityLabelMapper;


    /**
     * 类型 1余额账户、2冻结金额
     **/
    public static final Integer BALANCE_TYPE_NORMAL = 1;
    public static final Integer BALANCE_TYPE_FROZEN = 2;

    /**
     * 保证金类型：1.退还保证金(用户提交的申请类型) 2.扣除保证金(客服提交的申请类型)
     */
    private static final int ENSURE_AMOUNT_TYPE_REFUND = 1;
    private static final int ENSURE_AMOUNT_TYPE_DEDUCT = 2;

    public static final String DEPOSIT_AUTHORIZE_CONFIG_KEY = "deposit_authorize_config";


    @Override
    public TytDepositAuthorization selectDepositAuthByUserId(Long userId) {
        return authorizationMapper.selectDepositAuthByUserId(userId);
    }

    @Override
    public TytDepositAuthorization selectDepositAuthByUserIdAndConfig(Long userId) {
        TytDepositAuthorization tytDepositAuthorization = authorizationMapper.selectDepositAuthByUserId(userId);
        if (tytDepositAuthorization == null) {
            try {
                String depositAuthorizeConfigJsonString = tytConfigService.getStringValue(DEPOSIT_AUTHORIZE_CONFIG_KEY);
                if (StringUtils.isNotBlank(depositAuthorizeConfigJsonString)) {
                    DepositAuthorizeConfig depositAuthorizeConfigNow = JSONObject.parseObject(depositAuthorizeConfigJsonString, DepositAuthorizeConfig.class);
                    if (depositAuthorizeConfigNow != null && depositAuthorizeConfigNow.getOpenType() != null && depositAuthorizeConfigNow.getOpenType() == 1) {
                        //优货用户授权默认配置为全部用户
                        tytDepositAuthorization = new TytDepositAuthorization();
                        tytDepositAuthorization.setAuthStatus(0);
                        tytDepositAuthorization.setBlackStatus(0);
                        tytDepositAuthorization.setRefundLimitStatus(1);
                        tytDepositAuthorization.setUserGroup(depositAuthorizeConfigNow.getUserGroup());
                        tytDepositAuthorization.setRequireAmount(depositAuthorizeConfigNow.getRequireAmount());
                        tytDepositAuthorization.setConfigIsAll(true);
                    }
                }
            } catch (Exception e) {
                log.error("获取优货用户授权默认设置失败 原因：", e);
            }
        } else {
            tytDepositAuthorization.setConfigIsAll(false);
        }
        return tytDepositAuthorization;
    }

    @Override
    public List<DepositFlowBean> selectDepositFlowListByUserId(Long userId) {
        return flowMapper.selectDepositFlowListByUserId(userId);
    }

    @Override
    public DepositDetailBean getUserDepositDetail(Long userId) {
        DepositDetailBean depositDetail = new DepositDetailBean();
        // 查询签署状态
        TytDepositAuthorization authorization = this.selectDepositAuthByUserIdAndConfig(userId);
        // 查询保证金余额
        TytDepositAccount availableAccount = accountMapper.selectDepositAccountByUserIdAndType(userId, BALANCE_TYPE_NORMAL);
        TytDepositAccount frozenAccount = accountMapper.selectDepositAccountByUserIdAndType(userId, BALANCE_TYPE_FROZEN);
        if (authorization != null) {
            if (availableAccount != null && frozenAccount != null) {
                BigDecimal availableBalance = availableAccount.getBalance();
                BigDecimal frozenBalance = frozenAccount.getBalance();
                BigDecimal totalBalance = frozenBalance.add(availableBalance);
                depositDetail.setBalance(totalBalance);
                // 获取保证金最低限制金额
                // Integer depositMinLimit = tytConfigService.getIntValue("deposit_min_limit", 0);
                depositDetail.setIsEnough(false);
                if (totalBalance.compareTo(authorization.getRequireAmount()) >= 0) {
                    depositDetail.setIsEnough(true);
                }
            }
            // 设置返回授权状态
            depositDetail.setAuthStatus(authorization.getAuthStatus());
            // 查询用户退款状态
            List<RefundAuditStatusBean> refundBeanList = applyAuditMapper.selectRefundAuditStatusByParam(userId, 0);
            if (refundBeanList != null) {
                for (RefundAuditStatusBean refundBean : refundBeanList) {
                    Integer ensureAmountType = refundBean.getEnsureAmountType();
                    depositDetail.setRefundStatus(0);
                    if (ensureAmountType != null && ensureAmountType == 1) {
                        depositDetail.setRefundMsg("您的退款已受理，将于15个工作日之内退还");
                    }
                    if (ensureAmountType != null && ensureAmountType == 2) {
                        depositDetail.setRefundMsg("运单号" + refundBean.getTsOrderNo() + "存在审核中的退款申请");
                    }

                }
            }
            // 退款限制状态(0关闭，1开启)
            Integer refundLimitStatus = authorization.getRefundLimitStatus();
            if (refundLimitStatus == 1) {
                // 查询用户近15天内是否发过优车货源
                String useDate = DateUtil.dateToString(DateUtils.addDays(new Date(), -15), DateUtil.day_format);
                List<ExcellentGoodsUsingRecord> excellentGoodsUsingRecordList = excellentGoodsUsingRecordMapper.selectListByUserIdAndDate(userId, useDate);
                if (CollUtil.isNotEmpty(excellentGoodsUsingRecordList)) {
                    depositDetail.setIsPublish(1);
                } else {
                    depositDetail.setIsPublish(0);
                }
            } else if (refundLimitStatus == 0) {
                depositDetail.setIsPublish(0);
            }


            // 查询优货剩余使用次数
            if (authorization.getAuthStatus() != null && authorization.getAuthStatus() == 1) {
                ExcellentGoodsRemainCountBean remainingCountBean = getRemainingCount(userId);
                depositDetail.setType(remainingCountBean.getType());
                depositDetail.setRemainingCount(remainingCountBean.getRemainingCount());
                depositDetail.setRemainingCallNoPriceCount(remainingCountBean.getRemainingCallNoPriceCount());
                depositDetail.setRemainingCallPriceCount(remainingCountBean.getRemainingCallPriceCount());
                depositDetail.setRemainingFixedPriceCount(remainingCountBean.getRemainingFixedPriceCount());
            }

            //如果是因为优货用户授权默认配置而得到的优货权限，但是不是一手货主则没有我的保证金入口
            depositDetail.setEnsureAmountTabIsOpen(true);
            if (authorization.getConfigIsAll() && !isFirstTransportOwner(userId)) {
                depositDetail.setEnsureAmountTabIsOpen(false);
            }

        }
        return depositDetail;
    }

    @Override
    public boolean isFirstTransportOwner(Long userId) {
        //因为优车用户授权配置才得到了优车去授权资格

        Example exa = new Example(TytUserIdentityLabel.class);
        exa.and().andEqualTo("userId", userId);
        TytUserIdentityLabel tytUserIdentityLabel = tytUserIdentityLabelMapper.selectOneByExample(exa);

        if (tytUserIdentityLabel != null
                && tytUserIdentityLabel.getGoodsTypeFirst() != null
                && (tytUserIdentityLabel.getGoodsTypeFirst() == 1 || tytUserIdentityLabel.getGoodsTypeFirst() == 2)
                && tytUserIdentityLabel.getGoodsTypeSecond() != null
                && tytUserIdentityLabel.getGoodsTypeSecond() == 1) {
            //一手货主
            return true;
        }
        return false;
    }

    @Override
    public ResultMsgBean refundApply(Long userId, ResultMsgBean rm) {

        // 查询是否存在审核中的退款申请
        List<RefundAuditStatusBean> refundBeanList = applyAuditMapper.selectRefundAuditStatusByParam(userId, 0);
        if (refundBeanList != null) {
            for (RefundAuditStatusBean refundBean : refundBeanList) {
                Integer ensureAmountType = refundBean.getEnsureAmountType();

                if (ensureAmountType != null && ensureAmountType == 1) {
                    rm.setCode(1001);
                    rm.setMsg("您的退款已受理，将于15个工作日之内退还");
                    return rm;
                }
                if (ensureAmountType != null && ensureAmountType == 2) {
                    rm.setCode(1002);
                    rm.setMsg("运单号" + refundBean.getTsOrderNo() + "存在审核中的退款申请");
                    return rm;
                }
            }

        }
        // 查询签署状态
        TytDepositAuthorization authorization = authorizationMapper.selectDepositAuthByUserId(userId);
        // 退款限制状态(0关闭，1开启)
        Integer refundLimitStatus = authorization.getRefundLimitStatus();
        if (refundLimitStatus == 1) {
            // 查询用户是否满足连续15天未发布优货
            String useDate = DateUtil.dateToString(DateUtils.addDays(new Date(), -15), DateUtil.day_format);
            List<ExcellentGoodsUsingRecord> excellentGoodsUsingRecordList = excellentGoodsUsingRecordMapper.selectListByUserIdAndDate(userId, useDate);
            if (CollUtil.isNotEmpty(excellentGoodsUsingRecordList)) {
                rm.setCode(1003);
                rm.setMsg("您暂不满足退保证金条件（连续15天未使用“优货”功能），详询客服 ************");
                return rm;
            }
        }

        // 查询保证金账单看是否满足余额
        TytDepositAccount availableAccount = accountMapper.selectDepositAccountByUserIdAndType(userId, BALANCE_TYPE_NORMAL);
        // 获取目前可用余额
        BigDecimal availableBalance = availableAccount.getBalance();
        if (availableAccount == null || availableBalance.compareTo(new BigDecimal("0.00")) < 0) {
            rm.setCode(500);
            rm.setMsg("该用户保证金账户不存在或退款金额不足");
            return rm;
        }
        TytDepositAccount frozenAccount = accountMapper.selectDepositAccountByUserIdAndType(userId, BALANCE_TYPE_FROZEN);

        // 更新用户保证金账户余额和冻结金额
        frozenAccount.setBalance(frozenAccount.getBalance().add(availableBalance));
        accountMapper.updateByPrimaryKey(frozenAccount);
        availableAccount.setBalance(new BigDecimal("0.00"));
        accountMapper.updateByPrimaryKey(availableAccount);

        // 在运营后台生成一条退还申请记录
        TytDepositApplyAudit applyAudit = new TytDepositApplyAudit();
        applyAudit.setUserId(userId);
        applyAudit.setUserApplyTime(new Date());
        applyAudit.setEnsureAmountType(1);
        applyAudit.setHandleStatus(0);
        applyAudit.setAuditStage(0);
        applyAudit.setAuditStatus(0);
        applyAudit.setFinalAuditStatus(0);
        applyAudit.setRefundReason("用户申请");
        applyAudit.setRefundAmount(availableBalance);
        applyAudit.setCtime(new Date());
        applyAudit.setMtime(new Date());
        //新增记录申请次数
        Integer count = applyAuditMapper.selectCountUserIdAndType(userId, ENSURE_AMOUNT_TYPE_REFUND);
        applyAudit.setAlreadyDeductNum(0);
        applyAudit.setRefundApplyNum(count + 1);
        applyAuditMapper.insert(applyAudit);
        return rm;

    }

    @Override
    public TytDepositAccount getDepositAccount(Long userId, Integer balanceType) {
        return accountMapper.selectDepositAccountByUserIdAndType(userId, balanceType);
    }

    @Override
    public List<TytDepositApplyAudit> selectRefundAuditByUserId(Long userId, Integer auditStatus, Integer ensureType) {
        return applyAuditMapper.selectRefundAuditByUserId(userId, auditStatus, ensureType);
    }

    @Override
    public void deductionCount(Long userId, Integer type) {
        // 查询优货剩余使用次数
        String useDate = DateUtil.dateToString(new Date(), DateUtil.day_format);
        ExcellentGoodsUsingRecord excellentGoodsUsingRecord = excellentGoodsUsingRecordMapper.selectByUserIdAndDate(userId, useDate);

        if (excellentGoodsUsingRecord == null) {
            int callNoPriceUseCount = 0;
            int callPriceUseCount = 0;
            int fixedPriceUseCount = 0;
            if (type == 1) {
                callNoPriceUseCount = 1;
            } else if (type == 2) {
                callPriceUseCount = 1;
            } else if (type == 3) {
                fixedPriceUseCount = 1;
            }

            excellentGoodsUsingRecord = ExcellentGoodsUsingRecord.builder()
                    .useDate(DateUtil.stringToDate(useDate, DateUtil.day_format))
                    .useCount(1)
                    .userId(userId)
                    .ctime(new Date())
                    .mtime(new Date())
                    .callNoPriceUseCount(callNoPriceUseCount)
                    .callPriceUseCount(callPriceUseCount)
                    .fixedPriceUseCount(fixedPriceUseCount)
                    .build();
            excellentGoodsUsingRecordMapper.insert(excellentGoodsUsingRecord);
        } else {
            Integer useCount = excellentGoodsUsingRecord.getUseCount();
            useCount = useCount == null ? 0 : useCount;

            Integer callNoPriceUseCount = excellentGoodsUsingRecord.getCallNoPriceUseCount();
            callNoPriceUseCount = callNoPriceUseCount == null ? 0 : callNoPriceUseCount;
            Integer callPriceUseCount = excellentGoodsUsingRecord.getCallPriceUseCount();
            callPriceUseCount = callPriceUseCount == null ? 0 : callPriceUseCount;
            Integer fixedPriceUseCount = excellentGoodsUsingRecord.getFixedPriceUseCount();
            fixedPriceUseCount = fixedPriceUseCount == null ? 0 : fixedPriceUseCount;

            excellentGoodsUsingRecord.setUseCount(useCount + 1);
            if (type == 1) {
                excellentGoodsUsingRecord.setCallNoPriceUseCount(callNoPriceUseCount + 1);
            } else if (type == 2) {
                excellentGoodsUsingRecord.setCallPriceUseCount(callPriceUseCount + 1);
            } else if (type == 3) {
                excellentGoodsUsingRecord.setFixedPriceUseCount(fixedPriceUseCount + 1);
            }

            excellentGoodsUsingRecord.setMtime(new Date());
            excellentGoodsUsingRecordMapper.updateByPrimaryKey(excellentGoodsUsingRecord);
        }
    }

    @Override
    public ExcellentGoodsRemainCountBean getRemainingCount(Long userId) {
        ExcellentGoodsRemainCountBean remainCountBean = new ExcellentGoodsRemainCountBean();
        // 查询优货剩余使用次数
        List<ExcellentGoodsGroupDetailBean> detailBeanList = excellentGoodsGroupMapper.selectUserGroupInfo(userId);
        ExcellentGoodsGroupDetailBean detailBean;
        if (CollUtil.isNotEmpty(detailBeanList)) {
            detailBean = detailBeanList.get(0);
        } else {
            //分组配置不存在，获取默认配置
            String excellentGoodsGroupDefaultConfigJsonString = tytConfigService.getStringValue(EXCELLENT_GOODS_GROUP_DEFAULT_CONFIG, "{}");
            detailBean = JSON.parseObject(excellentGoodsGroupDefaultConfigJsonString, ExcellentGoodsGroupDetailBean.class);
            if (detailBean == null) {
                detailBean = new ExcellentGoodsGroupDetailBean();
                detailBean.setType(1);
                detailBean.setUserId(userId);
                detailBean.setGoodsCount(0);
                detailBean.setCallNoPriceCount(0);
                detailBean.setCallPriceCount(0);
                detailBean.setFixedPriceCount(0);
            }
            if (detailBean.getType() == null) {
                detailBean.setType(1);
            }
        }
        // 查询使用次数
        // 日次数
        String beginDay = null;
        String endDay = null;
        if (detailBean.getType() == 1) {
            beginDay = DateUtil.dateToString(new Date(), DateUtil.day_format);
        } else {
            beginDay = DateUtil.dateToString(DateUtil.getFirstDayOfMonth(), DateUtil.day_format);
            endDay = DateUtil.dateToString(DateUtil.getEndDayOfMonth(), DateUtil.day_format);
        }
        ExcellentGoodsUsingRecord excellentGoodsUsingRecord = excellentGoodsUsingRecordMapper.selectUsedCount(beginDay, endDay, userId);
        if (excellentGoodsUsingRecord == null) {
            excellentGoodsUsingRecord = new ExcellentGoodsUsingRecord();
        }

        int callNoPriceUseCount = excellentGoodsUsingRecord.getCallNoPriceUseCount() == null ? 0 : excellentGoodsUsingRecord.getCallNoPriceUseCount();
        int remainingCallNoPriceUseCount = (detailBean.getCallNoPriceCount() == null ? 0 : detailBean.getCallNoPriceCount()) - callNoPriceUseCount;
        remainCountBean.setRemainingCallNoPriceCount(Math.max(remainingCallNoPriceUseCount, 0));

        int callPriceUseCount = excellentGoodsUsingRecord.getCallPriceUseCount() == null ? 0 : excellentGoodsUsingRecord.getCallPriceUseCount();
        int remainingCallPriceUseCount = (detailBean.getCallPriceCount() == null ? 0 : detailBean.getCallPriceCount()) - callPriceUseCount;
        remainCountBean.setRemainingCallPriceCount(Math.max(remainingCallPriceUseCount, 0));

        int usedCount = remainCountBean.getRemainingCallNoPriceCount() + remainCountBean.getRemainingCallPriceCount();

        int fixedPriceUseCount = excellentGoodsUsingRecord.getFixedPriceUseCount() == null ? 0 : excellentGoodsUsingRecord.getFixedPriceUseCount();
        if (detailBean.getFixedPriceCount() == null) {
            remainCountBean.setRemainingFixedPriceCount(null);
        } else {
            int remainingFixedPriceUseCount = (detailBean.getFixedPriceCount() == null ? 0 : detailBean.getFixedPriceCount()) - fixedPriceUseCount;
            remainCountBean.setRemainingFixedPriceCount(Math.max(remainingFixedPriceUseCount, 0));
            usedCount += remainCountBean.getRemainingFixedPriceCount();
        }

        //手动计算总次数，以消除一口价次数无限的影响
        remainCountBean.setRemainingCount(Math.max(usedCount, 0));

        remainCountBean.setType(detailBean.getType());
        return remainCountBean;
    }

    @Override
    public TytDepositBlock getDepositBlock(Long userId) {
        return depositBlockMapper.selectByUserId(userId);
    }

    @Override
    public DepositBlockCheckDTO checkDepositBlock(Long userId, boolean isToast) {
        DepositBlockCheckDTO result = new DepositBlockCheckDTO();

        TytDepositBlock depositBlock = getDepositBlock(userId);
        result.setBlock(false);

        if (depositBlock == null) {
            return result;
        }

        Date blockBeginTime = depositBlock.getBlockBeginTime();
        Date blockEndTime = depositBlock.getBlockEndTime();

        result.setBlockEndTime(blockEndTime);
        result.setBlockType(depositBlock.getBlockType());
        result.setPermanentBlock(Boolean.TRUE.equals(depositBlock.getPermanentBlock()));
        result.setBlockDuration((int) TimeUnit.MILLISECONDS.toDays(blockEndTime.getTime() - blockBeginTime.getTime()));

        Date now = new Date();
        // 封禁   这里额外判断了一下`延迟封禁`的状态，因为定时任务可能有延迟
        if (Objects.equals(depositBlock.getBlockStatus(), 1) || Objects.equals(depositBlock.getBlockStatus(), 2)) {
            if (Boolean.TRUE.equals(depositBlock.getPermanentBlock())
                    || now.before(blockEndTime) && now.after(blockBeginTime)) {

                // 永封 or 在封禁范围内才算被封禁
                result.setBlock(true);
            }
        }

        if (result.isBlock()) {
            if (result.isPermanentBlock()) {
                if (Objects.equals(1, result.getBlockType())) {
                    // 运营类永封
                    if (isToast) {
                        result.setErrMessage("您因优车履约量不达标，被限制发布优货，如有问题请联系客服。");
                    } else {
                        result.setErrMessage("您因优车履约量不达标，被限制发布优货，如您有问题请联系客服。");
                    }
                } else {
                    // 违约类永封
                    if (isToast) {
                        result.setErrMessage(" 您因客诉量较高，被限制发布优货权，如有问题请联系客服。");
                    } else {
                        result.setErrMessage(" 您因客诉量较高，被限制发布优货权，如您有问题请联系客服。");
                    }
                }
            } else {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日HH点");
                if (Objects.equals(1, result.getBlockType())) {
                    // 运营类阶段封
                    if (isToast) {
                        result.setErrMessage(String.format("您因优车履约量不达标，被限制发布优货权益%d天，权益恢复时间%s，如有问题请联系客服。",
                                result.getBlockDuration(), dateFormat.format(result.getBlockEndTime())));
                    } else {
                        result.setErrMessage(String.format("您因优车履约量不达标，被限制发布优货权益%d天，权益恢复时间%s，请提升履约量以确保优车服务正常使用," +
                                        "如您有问题请联系客服。",
                                result.getBlockDuration(), dateFormat.format(result.getBlockEndTime())));
                    }
                } else {
                    // 违约类阶段封
                    if (isToast) {
                        result.setErrMessage(String.format("您因客诉量较高，被限制发布优货权益%d天，权益恢复时间%s，如有问题请联系客服。",
                                result.getBlockDuration(), dateFormat.format(result.getBlockEndTime())));
                    } else {
                        result.setErrMessage(String.format("您因客诉量较高，被限制发布优货权益%d天，权益恢复时间%s" +
                                        "，请您遵守平台规则避免客诉纠纷，确保优车服务正常使用，如您有问题请联系客服。",
                                result.getBlockDuration(), dateFormat.format(result.getBlockEndTime())));
                    }
                }
            }
        } else {
            result.setErrMessage("");
        }
        return result;
    }

    @Override
    public AppletEntranceInfo getAppletEntranceInfo(Long userId) {
        Integer goodsAppletEntranceOnOff = tytConfigService.getIntValue("goods_applet_entrance_on_off", 0);
        if (goodsAppletEntranceOnOff == 1) {
            //小程序userName
            String goodsWechatAppletUserName = tytConfigService.getStringValue("goods_applet_entrance_username", "gh_dd4c78be8450");
            //小程序环境  0：正式 1：测试 2：体验
            Integer goodsAppletEnvironment = tytConfigService.getIntValue("goods_applet_environment", 2);
            //跳转路径
            String goodsAppletEntrancePath = tytConfigService.getStringValue("goods_applet_entrance_path", "pages/releaseGoods/index");
            TytOwnerUserWhitelist tytOwnerUserWhitelist = tytOwnerUserWhitelistMapper.selectByUserId(userId);
            AppletEntranceInfo appletEntranceInfo = new AppletEntranceInfo();
            appletEntranceInfo.setAppletUserName(goodsWechatAppletUserName);
            appletEntranceInfo.setAppletEnvironment(goodsAppletEnvironment);
            appletEntranceInfo.setAppletPath(goodsAppletEntrancePath);
            appletEntranceInfo.setIsShowGoodsApplet(Objects.isNull(tytOwnerUserWhitelist) ? 0 : 1);
            return appletEntranceInfo;
        }
        return null;
    }

    @Override
    public TransactionRecordsVo getTransactionRecords(Long userId) throws Exception {

        TransactionRecordsVo transactionRecordsVo = new TransactionRecordsVo();
        // 判断用户是否授权
        TytDepositAuthorization depositAuthorization = authorizationMapper.selectDepositAuthByUserId(userId);
        if (depositAuthorization != null && depositAuthorization.getAuthStatus().equals(AuthStatusEnum.NO_AUTH.getStatus())) {
            transactionRecordsVo.setAuthStatus(AuthStatusEnum.NO_AUTH.getStatus());
        } else {
            transactionRecordsVo.setAuthStatus(AuthStatusEnum.AUTHED.getStatus());
        }
        // 查询最快刷新间隔，如果没有默认值，默认给10分钟
        if (AuthStatusEnum.NO_AUTH.getStatus().equals(transactionRecordsVo.getAuthStatus())) {
            Integer minRefreshInterval = minRefreshInterval(userId);
            transactionRecordsVo.setMinRefreshInterval(minRefreshInterval == null ? 10 : minRefreshInterval);
        }
        // 查询本月已发货源
        Date startTime = cn.hutool.core.date.DateUtil.beginOfMonth(new Date()).toJdkDate();
        Date endTime = cn.hutool.core.date.DateUtil.endOfMonth(new Date()).toJdkDate();
        int appCount = transportMainService.selectCountOfExcellentGoods(userId, startTime, endTime);
        User user = userService.getByUserId(userId);
        int dispatchCount = tytTransportDispatchService.countExcellentByPhone(user.getCellPhone(), startTime, endTime);
        transactionRecordsVo.setThisMonthPublished(appCount + dispatchCount);

        //查询上月已发货源
        Date lastStartTime = cn.hutool.core.date.DateUtil.beginOfMonth(cn.hutool.core.date.DateUtil.lastMonth()).toJdkDate();
        Date lastEndTime = cn.hutool.core.date.DateUtil.endOfMonth(cn.hutool.core.date.DateUtil.lastMonth()).toJdkDate();
        int lastAppCount = transportMainService.selectCountOfExcellentGoods(userId, lastStartTime, lastEndTime);
        int lastDispatchCount = tytTransportDispatchService.countExcellentByPhone(user.getCellPhone(), lastStartTime, lastEndTime);
        transactionRecordsVo.setLastMonthPublished(lastAppCount + lastDispatchCount);

        // 查询本月已成交单
        String transactionMonth = cn.hutool.core.date.DateUtil.date().toString(DateUtil.MONTH_FORMAT);
        Integer thisMonthTransaction = transportOrdersRiskService.getOrderForTransaction(userId, transactionMonth);
        transactionRecordsVo.setThisMonthTransaction(thisMonthTransaction);

        // 查询上月已成交单
        String lastTransactionMonth = cn.hutool.core.date.DateUtil.lastMonth().toString(DateUtil.MONTH_FORMAT);
        Integer lastMonthTransaction = transportOrdersRiskService.getOrderForTransaction(userId, lastTransactionMonth);
        transactionRecordsVo.setLastMonthTransaction(lastMonthTransaction);

        // 可发优车数量
        List<ExcellentGoodsGroupDetailBean> detailBeanList = excellentGoodsGroupMapper.selectUserGroupInfo(userId);
        if (CollUtil.isNotEmpty(detailBeanList)) {
            ExcellentGoodsGroupDetailBean detailBean = detailBeanList.get(0);
            transactionRecordsVo.setExcellentGoodsCount(detailBean.getGoodsCount());
            transactionRecordsVo.setType(detailBean.getType());
        }

        return transactionRecordsVo;
    }

    @Override
    public Integer minRefreshInterval(Long userId) {
        Integer minRefreshInterval = null;
        // 获取设置的刷新次数及刷新间隔,如果没有配置，就走默认设置
        RefreshContentDto minIntervalContent = tytGoodsRefreshUserService.getMinIntervalContent(userId);
        if (minIntervalContent != null) {
            minRefreshInterval = Integer.valueOf(minIntervalContent.getInterval());
        } else {
            minRefreshInterval = getExcellentDefaultInterval();
        }
        return minRefreshInterval;
    }

    private Integer getExcellentDefaultInterval() {

        String excellentTopConfig = tytConfigService.getStringValue(RESEND_EXCELLENT_KEY, "");

        String[] split = excellentTopConfig.split(COMMA);
        if (split.length == 3 && CONFIG_SWITCH.equals(split[0])) {
            return Integer.parseInt(split[1]);
        }
        return null;
    }

}
