package com.tyt.deposit.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.deposit.bean.GoodsRefreshConfigDto;
import com.tyt.deposit.bean.RefreshContentDto;
import com.tyt.deposit.bean.RefreshContentMinCountDto;
import com.tyt.deposit.mapper.base.TytGoodsRefreshConfigMapper;
import com.tyt.deposit.mapper.base.TytGoodsRefreshUserMapper;
import com.tyt.deposit.service.TytGoodsRefreshUserService;
import com.tyt.model.Transport;
import com.tyt.plat.constant.AbtestConstant;
import com.tyt.plat.service.base.AbtestService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * @author: helian
 * @since: 2023/10/13 17:09
 */
@Slf4j
@AllArgsConstructor
@Service("tytGoodsRefreshConfigService")
public class TytGoodsRefreshUserServiceImpl implements TytGoodsRefreshUserService {

    private final TytGoodsRefreshUserMapper tytGoodsRefreshUserMapper;
    private final TytGoodsRefreshConfigMapper tytGoodsRefreshConfigMapper;
    private final AbtestService abtestService;

    // 兼容无效数据，为空的数据忽略掉
    private final static Predicate<RefreshContentDto> REFRESH_NOT_BLANK =
            (RefreshContentDto t) -> StringUtils.isNotBlank(t.getInterval()) && StringUtils.isNotBlank(t.getFrequency());
    // 比较最小的间隔
    private final static Comparator<RefreshContentDto> MIN_INTERVAL =
            Comparator.comparingInt(t -> Integer.parseInt(t.getInterval()));

    @Override
    public RefreshContentDto getMinIntervalContent(Long userId) {
        if (userId == null) {
            return null;
        }
        List<GoodsRefreshConfigDto> configs = tytGoodsRefreshUserMapper.selectExcellentGoodsContentByUserId(userId);
        if (configs.isEmpty()) {
            return null;
        }
        return configs.stream().map(t -> JSON.parseArray(t.getContent(), RefreshContentDto.class))
                .flatMap(Collection::stream)
                .filter(REFRESH_NOT_BLANK)
                .min(MIN_INTERVAL)
                .orElse(null);
    }

    @Override
    public RefreshContentMinCountDto selectMinIntervalAndThisFrequencyByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        RefreshContentMinCountDto refreshContentMinCountDto = new RefreshContentMinCountDto();

        // 6440 需求，一个用户会返回多个刷新配置，取频率最小的那个
        List<GoodsRefreshConfigDto> configs = tytGoodsRefreshUserMapper.selectExcellentGoodsContentByUserId(userId);
        if (CollectionUtil.isNotEmpty(configs)) {
            List<RefreshContentDto> minIntervalContents = configs.stream()
                    .map(t -> JSON.parseArray(t.getContent(), RefreshContentDto.class))
                    .min((t1, t2) -> {
                        RefreshContentDto r1 = t1.stream().filter(REFRESH_NOT_BLANK).min(MIN_INTERVAL).get();
                        RefreshContentDto r2 = t2.stream().filter(REFRESH_NOT_BLANK).min(MIN_INTERVAL).get();
                        return Integer.parseInt(r1.getInterval()) - Integer.parseInt(r2.getInterval());
                    }).get();

            Integer minInterval = minIntervalContents.stream()
                    .filter(REFRESH_NOT_BLANK)
                    .map(t -> Integer.parseInt(t.getInterval()))
                    .min(Comparator.comparingInt(t -> t)).get();

            refreshContentMinCountDto.setMinInterval(minInterval.toString());
            refreshContentMinCountDto.setTotalTime(minIntervalContents.stream()
                    .filter(REFRESH_NOT_BLANK)
                    .map(it -> Integer.parseInt(it.getFrequency()) * Integer.parseInt(it.getInterval()))
                    .reduce(0, Integer::sum));

        }
        return refreshContentMinCountDto;
    }


    @Override
    public boolean isCheckInstantGrab(Transport transport) {
        String code;
        if (Objects.equals(transport.getExcellentGoods(), 1)) {
            code = AbtestConstant.EXCELLENT_GOODS_PASS_MODEL;
        } else if (Objects.equals(transport.getExcellentGoods(), 0)) {
            code = AbtestConstant.GENERAL_GOODS_PASS_MODEL;
        } else if (Objects.equals(transport.getExcellentGoods(), 2)) {
            code = AbtestConstant.SPECIAL_GOODS_PASS_MODEL;
        } else {
            return false;
        }
        return abtestService.getUserType(code, transport.getUserId()) == 1;
    }

}
