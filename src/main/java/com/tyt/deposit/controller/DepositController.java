package com.tyt.deposit.controller;

import com.tyt.deposit.bean.DepositDetailBean;
import com.tyt.deposit.bean.DepositFlowBean;
import com.tyt.deposit.bean.ExcellentGoodsRemainCountBean;
import com.tyt.deposit.entity.base.TytDepositAuthorization;
import com.tyt.deposit.enums.AuthStatusEnum;
import com.tyt.deposit.pojo.dto.DepositBlockCheckDTO;
import com.tyt.deposit.pojo.vo.AppletEntranceInfo;
import com.tyt.deposit.pojo.vo.DepositBlockInfoVO;
import com.tyt.deposit.pojo.vo.TransactionRecordsVo;
import com.tyt.deposit.service.DepositService;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：DepositController
 * @Author: TYT
 * @Date: 2023/6/6 15:14
 * @Description:
 */
@Controller
@RequestMapping("/plat/deposit")
public class DepositController {

    private static final Logger logger = LoggerFactory.getLogger(DepositController.class);

    @Resource(name = "depositService")
    private DepositService depositService;


    /**
     * 保证金退还申请接口
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/refundApply", "/refundApply.action"})
    @ResponseBody
    public ResultMsgBean refundApply(@RequestParam(name = "userId", required = true) Long userId) {
        logger.info("deposit refundApply userId is 【{}】", userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            return depositService.refundApply(userId, resultMsgBean);
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        }
    }

    /**
     * 用户保证金相关状态
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/detail", "/detail.action"})
    @ResponseBody
    public ResultMsgBean detail(@RequestParam(name = "userId", required = true) Long userId) {
        logger.info("deposit detail userId is 【{}】", userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            DepositDetailBean userDepositDetail = depositService.getUserDepositDetail(userId);
            resultMsgBean.setData(userDepositDetail);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        }
    }


    /**
     * 用户保证金流水
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/flowList", "/flowList.action"})
    @ResponseBody
    public ResultMsgBean flowList(@RequestParam(name = "userId", required = true) Long userId) {
        logger.info("deposit flowList userId is 【{}】", userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            Map<String, Object> resultMap = new HashMap<>();
            List<DepositFlowBean> depositFlows = depositService.selectDepositFlowListByUserId(userId);
            resultMap.put("depositFlowList", depositFlows);
            resultMsgBean.setData(resultMap);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        }
    }

    /**
     * 优货权益名单查询
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = {"/queryEquity", "/queryEquity.action"})
    @ResponseBody
    public ResultMsgBean queryEquity(@RequestParam(name = "userId", required = true) Long userId) {
        logger.info("deposit queryEquity userId is 【{}】", userId);
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "操作成功");
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("userId参数错误");
                return resultMsgBean;
            }
            Map<String, Object> resultMap = new HashMap<>();
            TytDepositAuthorization depositAuthorization = depositService.selectDepositAuthByUserIdAndConfig(userId);
            if (depositAuthorization == null) {
                resultMap.put("isJoin", 0);
            } else {
                resultMap.put("isJoin", 1);// 是否加入(是否被导入列表 0未导入 1已导入)
                resultMap.put("authStatus", depositAuthorization.getAuthStatus());// 授权状态(0未授权，1已授权)
                resultMap.put("blackStatus", depositAuthorization.getBlackStatus());// 黑名单状态(0未加入黑名单，1已加入黑名单)
                resultMap.put("userGroup", depositAuthorization.getUserGroup());// 用户分组 0电议+一口价 1电议  2 一口价
                // 如果已经授权，获取刷新次数
                if (AuthStatusEnum.NO_AUTH.getStatus().equals(depositAuthorization.getAuthStatus())) {
                    Integer minRefreshInterval = depositService.minRefreshInterval(userId);
                    resultMap.put("minRefreshInterval", minRefreshInterval);
                }
            }

            DepositBlockCheckDTO depositBlockCheckDTO = depositService.checkDepositBlock(userId, false);

            DepositBlockInfoVO depositBlockInfoVO = DepositBlockInfoVO.builder()
                    .block(depositBlockCheckDTO.isBlock())
                    .errMessage(depositBlockCheckDTO.getErrMessage()).build();

            resultMap.put("depositBlock", depositBlockInfoVO);

            resultMap.put("userId", userId);
            //极简发货跳转小程序信息
            AppletEntranceInfo appletEntranceInfo = depositService.getAppletEntranceInfo(userId);
            resultMap.put("appletEntranceInfo",appletEntranceInfo);

            resultMap.put("ensureAmountTabIsOpen", true);
            //如果是因为优货用户授权默认配置而得到的优货权限，但是不是一手货主则没有我的保证金入口
            if (depositAuthorization != null && depositAuthorization.getConfigIsAll() && !depositService.isFirstTransportOwner(userId)) {
                resultMap.put("ensureAmountTabIsOpen", false);
            }

            if (depositAuthorization != null && depositAuthorization.getAuthStatus() != null && depositAuthorization.getAuthStatus() == 1) {
                //已授权才返回优车发货次数
                ExcellentGoodsRemainCountBean remainingCount = depositService.getRemainingCount(userId);
                if (remainingCount != null) {
                    resultMap.put("remainingCount", remainingCount.getRemainingCount());
                    resultMap.put("remainingCallNoPriceCount", remainingCount.getRemainingCallNoPriceCount());
                    resultMap.put("remainingCallPriceCount", remainingCount.getRemainingCallPriceCount());
                    resultMap.put("remainingFixedPriceCount", remainingCount.getRemainingFixedPriceCount());
                }
            }

            resultMsgBean.setData(resultMap);
            return resultMsgBean;
        } catch (Exception e) {
            logger.error("优货权益名单查询异常userId{}:",userId, e);
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器错误");
            return resultMsgBean;
        }
    }


    /**
     * 查询用户签约状态-提供给优车报名，需跳过登录验证
     *
     * @param ownerId 用户id
     * @return ResultMsgBean
     */
    @RequestMapping(value = {"/detailForEnroll", "/detailForEnroll.action"})
    @ResponseBody
    public ResultMsgBean detailForEnroll(@RequestParam(name = "ownerId", required = true) Long ownerId) {
        logger.info("deposit detail userId is 【{}】", ownerId);
        return detail(ownerId);
    }

    /**
     * 优车用户数据统计
     *
     * @param userId 用户id
     * @return ResultMsgBean
     */
    @RequestMapping(value = {"/transactionRecords"})
    @ResponseBody
    public ResultMsgBean getTransactionRecords(@RequestParam(name = "userId") Long userId) throws Exception {
        logger.info("transactionRecords userId is 【{}】", userId);
        TransactionRecordsVo transactionRecordsVo = depositService.getTransactionRecords(userId);
        return ResultMsgBean.successResponse(transactionRecordsVo);
    }


}
