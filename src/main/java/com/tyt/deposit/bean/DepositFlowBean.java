package com.tyt.deposit.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName：DepositDetailBean
 * @Author: TYT
 * @Date: 2023/6/7 14:35
 * @Description:
 */
@Data
public class DepositFlowBean {

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资金流向 1：转入 2：转出
     */
    private Integer direction;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 运单号
     */
    private String tsOrderNo;

    /**
     * 交易类型 1：保证金退款，2：扣除保证金，3：缴纳保证金
     */
    private Integer operateType;




}
