package com.tyt.deposit.bean;

import lombok.Data;

/**
 * @ClassName：ExcellentGoodsRemainCountBean
 * @Author: TYT
 * @Date: 2023/6/7 14:35
 * @Description:
 */
@Data
public class ExcellentGoodsRemainCountBean {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 剩余总发货次数
     */
    private Integer remainingCount;

    /**
     * 电议无价剩余发货次数
     */
    private Integer remainingCallNoPriceCount;

    /**
     * 电议有价剩余发货次数
     */
    private Integer remainingCallPriceCount;

    /**
     * 一口价剩余发货次数
     */
    private Integer remainingFixedPriceCount;

    /**
     * 限额类型
     */
    private Integer type;


}
