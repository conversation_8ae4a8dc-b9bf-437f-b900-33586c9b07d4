package com.tyt.deposit.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName：DepositDetailBean
 * @Author: TYT
 * @Date: 2023/6/7 14:35
 * @Description:
 */
@Data
public class DepositDetailBean {

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 授权状态(0未授权，1已授权)
     */
    private Integer authStatus;

    /**
     * 退款状态(0未申请退款，1退款中 2 退款完成 3拒绝退款)
     */
    private Integer refundStatus;


    /**
     * 退款提示信息
     */
    private String refundMsg;

    /**
     * 某个时间段内是否发过优车货源
     * 0 没有发货
     * 1 存在发货
     */
    private Integer isPublish;


    /**
     * 保证金金额是否充足
     * true 充足
     * false 不充足
     */
    private Boolean isEnough;

    /**
     * 优车发货剩余总次数
     */
    private Integer remainingCount;
    /**
     * 1:日次数 2：月次数
     */
    private Integer type;

    /**
     * 我的保证金入口是否打开
     */
    private Boolean ensureAmountTabIsOpen;

    /**
     * 电议无价剩余发货次数
     */
    private Integer remainingCallNoPriceCount;

    /**
     * 电议有价剩余发货次数
     */
    private Integer remainingCallPriceCount;

    /**
     * 一口价剩余发货次数
     */
    private Integer remainingFixedPriceCount;


}
