package com.tyt.deposit.bean;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName：DepositDetailBean
 * @Author: TYT
 * @Date: 2023/6/7 14:35
 * @Description:
 */
@Data
public class ExcellentGoodsGroupDetailBean {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 发货上限数量
     */
    private Integer goodsCount;

    /**
     * 限额类型
     */
    private Integer type;

    /**
     * 电议无价发货次数
     */
    private Integer callNoPriceCount;

    /**
     * 电议有价发货次数
     */
    private Integer callPriceCount;

    /**
     * 一口价发货次数
     */
    private Integer fixedPriceCount;


}
