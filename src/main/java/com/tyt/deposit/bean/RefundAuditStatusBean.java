package com.tyt.deposit.bean;

import lombok.Data;


/**
 * @ClassName：DepositDetailBean
 * @Author: TYT
 * @Date: 2023/6/7 14:35
 * @Description:
 */
@Data
public class RefundAuditStatusBean {

    /**
     * 处理状态 0.待处理 1.同意 2.拒绝
     */
    private Integer handleStatus;

    /**
     * 客服审核状态 0.审核中 1.审核通过 2.审核不通过
     */
    private Integer finalAuditStatus;


    /**
     * 保证金类型：1.退还保证金(用户提交的申请类型) 2.扣除保证金(客服提交的申请类型)
     */
    private Integer ensureAmountType;


    /**
     * 运单号
     */
    private String tsOrderNo;


}
