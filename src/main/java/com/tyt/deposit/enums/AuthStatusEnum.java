package com.tyt.deposit.enums;

/**
 * @Describe 优车授权
 * <AUTHOR>
 * @Date 2023/6/9
 */
public enum AuthStatusEnum {

    /**
     * 未授权
     */
    AUTHED(0),
    /**
     * 已授权
     */
    NO_AUTH(1);


    AuthStatusEnum(Integer status) {
        this.status = status;
    }

    /**
     * 授权状态
     */
    private final Integer status;


    public Integer getStatus() {
        return status;
    }

}
