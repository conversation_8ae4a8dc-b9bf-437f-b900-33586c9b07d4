package com.tyt.deposit.enums;

import com.tyt.enums.AppLabelEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/6/9
 */
public enum AuditStatusEnum {

    /**
     * 保证金审核状态
     */
    审核中(0),
    审核通过(1),
    审核不通过(2);


    AuditStatusEnum(Integer status) {
        this.status = status;
    }

    /**
     * 审核状态
     */
    private final Integer status;


    public Integer getStatus() {
        return status;
    }

}
