package com.tyt.deposit.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_deposit_order")
public class TytDepositOrder {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 支付订单号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 保证金单号
     */
    @Column(name = "deposit_no")
    private String depositNo;

    /**
     * 交易类型 1：缴纳保证金 ,2：扣除保证金，3：保证金退款
     */
    @Column(name = "operate_type")
    private Integer operateType;

    /**
     * 交易金额
     */
    @Column(name = "trade_amount")
    private BigDecimal tradeAmount;

    /**
     * 交易状态(0.交易中 1.交易失败 2.交易成功)
     */
    private Integer status;

    /**
     * 业务单号
     */
    @Column(name = "business_no")
    private String businessNo;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}