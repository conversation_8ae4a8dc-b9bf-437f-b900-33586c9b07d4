package com.tyt.deposit.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_goods_refresh_config")
public class TytGoodsRefreshConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 分组类型code
     */
    private String code;

    /**
     * 分组类型名称
     */
    private String name;

    /**
     * 分组内容(json格式区分 {间隔:次数})
     */
    private String content;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 状态（0未删除，1已删除）
     */
    private Integer del;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 修改人
     */
    @Column(name = "modify_name")
    private String modifyName;

    /**
     * 配置类型，1:刷新配置  2:货源曝光限制
     */
    private Integer configType;

    /**
     * 货源类型，0:普通货源  1：优车货源
     * 6710 支持多选，多个用逗号隔开
     */
    private String excellentGoods;

    /**
     * 货源价格，0:全部货源 1:一口价 2:有价电议 3:无价电议
     * 6710 支持多选，多个用逗号隔开
     */
    private String goodsPriceType;

    /**
     * 发货时是否过好货模型  0：否  1：是
     */
    private Integer instantGrab;

    /**
     * 货主身份，格式：1-1，多个用逗号隔开
     */
    private String userGoodsType;
}