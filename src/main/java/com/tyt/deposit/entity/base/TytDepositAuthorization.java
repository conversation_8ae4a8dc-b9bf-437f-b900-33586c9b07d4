package com.tyt.deposit.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_deposit_authorization")
public class TytDepositAuthorization {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 授权状态(0未授权，1已授权)
     */
    @Column(name = "auth_status")
    private Integer authStatus;

    /**
     * 授权时间
     */
    @Column(name = "auth_time")
    private Date authTime;

    /**
     * 黑名单状态(0未加入黑名单，1已加入黑名单)
     */
    @Column(name = "black_status")
    private Integer blackStatus;

    /**
     * 是否删除(0未删除，1已删除)
     */
    @Column(name = "delete_status")
    private Integer deleteStatus;

    /**
     * 用户分组 0电议+一口价 1电议  2 一口价
     */
    @Column(name = "user_group")
    private Integer userGroup;

    /**
     * 操作人ID
     */
    @Column(name = "operate_id")
    private Long operateId;

    /**
     * 操作人
     */
    @Column(name = "operate_user_name")
    private String operateUserName;

    /**
     *  需缴保证金金额
     */
    @Column(name = "require_amount")
    private BigDecimal requireAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 退款限制状态(0关闭，1开启)
     */
    @Column(name = "refund_limit_status")
    private Integer refundLimitStatus;

    /**
     * 是否是因为默认配置为全部用户才获取到了去授权的资格
     */
    private Boolean configIsAll;
}