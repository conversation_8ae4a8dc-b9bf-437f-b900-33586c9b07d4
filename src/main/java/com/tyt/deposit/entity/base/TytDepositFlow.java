package com.tyt.deposit.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_deposit_flow")
public class TytDepositFlow {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 保证金单号
     */
    @Column(name = "deposit_no")
    private String depositNo;

    /**
     * 流水单号
     */
    @Column(name = "flow_no")
    private String flowNo;

    /**
     * 交易方用户信息（现用与存放公司收款账户）
     */
    @Column(name = "transaction_account_info")
    private String transactionAccountInfo;

    /**
     * 资金流向 1：转入 2：转出
     */
    private Integer direction;

    /**
     * 交易金额
     */
    @Column(name = "trade_amount")
    private BigDecimal tradeAmount;

    /**
     * 交易前余额
     */
    @Column(name = "pre_amount")
    private BigDecimal preAmount;

    /**
     * 交易后余额
     */
    @Column(name = "end_amount")
    private BigDecimal endAmount;

    /**
     * 创建时间
     */
    private Date ctime;
}