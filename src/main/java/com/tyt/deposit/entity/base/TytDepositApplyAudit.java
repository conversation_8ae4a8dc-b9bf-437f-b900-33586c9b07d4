package com.tyt.deposit.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_deposit_apply_audit")
public class TytDepositApplyAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 异常上报表ID (扣除保证金时不能为空)
     */
    @Column(name = "ex_id")
    private Long exId;

    /**
     * 用户上报时间
     */
    @Column(name = "user_apply_time")
    private Date userApplyTime;

    /**
     * 客服上报时间
     */
    @Column(name = "customer_service_apply_time")
    private Date customerServiceApplyTime;

    /**
     * 运单号
     */
    @Column(name = "ts_order_no")
    private String tsOrderNo;

    /**
     * 发货人账号
     */
    @Column(name = "pub_cell_phone")
    private String pubCellPhone;

    /**
     * 车主账号
     */
    @Column(name = "pay_cell_phone")
    private String payCellPhone;

    /**
     * 扣除金额/退还金额 单位:元
     */
    @Column(name = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 扣除原因
     */
    @Column(name = "refund_reason")
    private String refundReason;

    /**
     * 保证金类型：1.退还保证金(用户提交的申请类型) 2.扣除保证金(客服提交的申请类型)
     */
    @Column(name = "ensure_amount_type")
    private Integer ensureAmountType;

    /**
     * 处理状态 0.待处理 1.同意 2.拒绝
     */
    @Column(name = "handle_status")
    private Integer handleStatus;

    /**
     * 审核状态 0.审核中 1.审核通过 2.审核不通过
     */
    @Column(name = "audit_status")
    private Integer auditStatus;

    /**
     * 审核阶段 1 一级审核 2 二级审核 3 三级审核
     */
    @Column(name = "audit_stage")
    private Integer auditStage;

    /**
     * 审核状态 0.审核中 1.审核通过 2.审核不通过
     */
    @Column(name = "final_audit_status")
    private Integer finalAuditStatus;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 填写remark操作人Id
     */
    @Column(name = "remark_operate_id")
    private Long remarkOperateId;

    /**
     * 填写remark操作人姓名
     */
    @Column(name = "remark_operate_user_name")
    private String remarkOperateUserName;

    /**
     * 最新填写remark时间
     */
    @Column(name = "remark_operate_time")
    private Date remarkOperateTime;

    /**
     * 用户申请保证金退还累计次数
     */
    @Column(name = "refund_apply_num")
    private Integer refundApplyNum;

    /**
     * 已实际发生扣除保证金累计次数
     */
    @Column(name = "already_deduct_num")
    private Integer alreadyDeductNum;


    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}