package com.tyt.noticePopup.bean;

import com.tyt.noticePopup.enums.PopupTypeEnum;

public class PopupSaveBean {
    private PopupTypeEnum popupTypeEnum;
    private Long productionId = 0L;  //通知人id
    private Long receiveId;  //被通知人id
    private String params;   //主内容参数替换内容  英文逗号分隔
    private Integer originPopup =0;
    private Integer carPopup = 0;
    private Integer goodsPopup =0;

    public PopupTypeEnum getPopupTypeEnum() {
        return popupTypeEnum;
    }

    public void setPopupTypeEnum(PopupTypeEnum popupTypeEnum) {
        this.popupTypeEnum = popupTypeEnum;
    }

    public Long getProductionId() {
        return productionId;
    }

    public void setProductionId(Long productionId) {
        this.productionId = productionId;
    }

    public Long getReceiveId() {
        return receiveId;
    }

    public void setReceiveId(Long receiveId) {
        this.receiveId = receiveId;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public Integer getOriginPopup() {
        return originPopup;
    }

    public void setOriginPopup(Integer originPopup) {
        this.originPopup = originPopup;
    }

    public Integer getCarPopup() {
        return carPopup;
    }

    public void setCarPopup(Integer carPopup) {
        this.carPopup = carPopup;
    }

    public Integer getGoodsPopup() {
        return goodsPopup;
    }

    public void setGoodsPopup(Integer goodsPopup) {
        this.goodsPopup = goodsPopup;
    }
}
