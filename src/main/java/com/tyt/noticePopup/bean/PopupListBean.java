package com.tyt.noticePopup.bean;

import java.io.Serializable;

public class PopupListBean implements Serializable {
    private static final long serialVersionUID = -8752466181933454422L;
    private String msgId;
    private Long productionId;
    private Long receiveId;
    private String style;
    private String title;
    private String titleColor;
    private String masterContent;
    private String masterContentColor;
    private String leftButtonContent;
    private String leftButtonLink;
    private String leftButtonType;
    private String leftButtonBackcolor;
    private String leftButtonContentColor;
    private String rightButtonContent;
    private String rightButtonLink;
    private String rightButtonType;
    private String rightButtonBackcolor;
    private String rightButtonContentColor;
    private String guideContent;
    private String guideContentColor;
    private String guideButtonType;
    private String guideButtonContent;
    private String guideButtonBackcolor;
    private String guideButtonContentColor;
    private String guideButtonLink;
    private String pictureAddr;

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public Long getProductionId() {
        return productionId;
    }

    public void setProductionId(Long productionId) {
        this.productionId = productionId;
    }

    public Long getReceiveId() {
        return receiveId;
    }

    public void setReceiveId(Long receiveId) {
        this.receiveId = receiveId;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleColor() {
        return titleColor;
    }

    public void setTitleColor(String titleColor) {
        this.titleColor = titleColor;
    }

    public String getMasterContent() {
        return masterContent;
    }

    public void setMasterContent(String masterContent) {
        this.masterContent = masterContent;
    }

    public String getMasterContentColor() {
        return masterContentColor;
    }

    public void setMasterContentColor(String masterContentColor) {
        this.masterContentColor = masterContentColor;
    }

    public String getLeftButtonContent() {
        return leftButtonContent;
    }

    public void setLeftButtonContent(String leftButtonContent) {
        this.leftButtonContent = leftButtonContent;
    }

    public String getLeftButtonLink() {
        return leftButtonLink;
    }

    public void setLeftButtonLink(String leftButtonLink) {
        this.leftButtonLink = leftButtonLink;
    }

    public String getLeftButtonType() {
        return leftButtonType;
    }

    public void setLeftButtonType(String leftButtonType) {
        this.leftButtonType = leftButtonType;
    }

    public String getLeftButtonBackcolor() {
        return leftButtonBackcolor;
    }

    public void setLeftButtonBackcolor(String leftButtonBackcolor) {
        this.leftButtonBackcolor = leftButtonBackcolor;
    }

    public String getLeftButtonContentColor() {
        return leftButtonContentColor;
    }

    public void setLeftButtonContentColor(String leftButtonContentColor) {
        this.leftButtonContentColor = leftButtonContentColor;
    }

    public String getRightButtonContent() {
        return rightButtonContent;
    }

    public void setRightButtonContent(String rightButtonContent) {
        this.rightButtonContent = rightButtonContent;
    }

    public String getRightButtonLink() {
        return rightButtonLink;
    }

    public void setRightButtonLink(String rightButtonLink) {
        this.rightButtonLink = rightButtonLink;
    }

    public String getRightButtonType() {
        return rightButtonType;
    }

    public void setRightButtonType(String rightButtonType) {
        this.rightButtonType = rightButtonType;
    }

    public String getRightButtonBackcolor() {
        return rightButtonBackcolor;
    }

    public void setRightButtonBackcolor(String rightButtonBackcolor) {
        this.rightButtonBackcolor = rightButtonBackcolor;
    }

    public String getRightButtonContentColor() {
        return rightButtonContentColor;
    }

    public void setRightButtonContentColor(String rightButtonContentColor) {
        this.rightButtonContentColor = rightButtonContentColor;
    }

    public String getGuideContent() {
        return guideContent;
    }

    public void setGuideContent(String guideContent) {
        this.guideContent = guideContent;
    }

    public String getGuideContentColor() {
        return guideContentColor;
    }

    public void setGuideContentColor(String guideContentColor) {
        this.guideContentColor = guideContentColor;
    }

    public String getGuideButtonType() {
        return guideButtonType;
    }

    public void setGuideButtonType(String guideButtonType) {
        this.guideButtonType = guideButtonType;
    }

    public String getGuideButtonContent() {
        return guideButtonContent;
    }

    public void setGuideButtonContent(String guideButtonContent) {
        this.guideButtonContent = guideButtonContent;
    }

    public String getGuideButtonBackcolor() {
        return guideButtonBackcolor;
    }

    public void setGuideButtonBackcolor(String guideButtonBackcolor) {
        this.guideButtonBackcolor = guideButtonBackcolor;
    }

    public String getGuideButtonContentColor() {
        return guideButtonContentColor;
    }

    public void setGuideButtonContentColor(String guideButtonContentColor) {
        this.guideButtonContentColor = guideButtonContentColor;
    }

    public String getGuideButtonLink() {
        return guideButtonLink;
    }

    public void setGuideButtonLink(String guideButtonLink) {
        this.guideButtonLink = guideButtonLink;
    }

    public String getPictureAddr() {
        return pictureAddr;
    }

    public void setPictureAddr(String pictureAddr) {
        this.pictureAddr = pictureAddr;
    }
}
