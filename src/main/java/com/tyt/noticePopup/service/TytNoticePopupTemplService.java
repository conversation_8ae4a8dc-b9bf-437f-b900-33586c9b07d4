package com.tyt.noticePopup.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.TytNoticePopup;
import com.tyt.model.TytNoticePopupTempl;
import com.tyt.noticePopup.bean.PopupListBean;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.transport.querybean.PopupTemplBean;

import java.math.BigInteger;
import java.util.List;

public interface TytNoticePopupTemplService extends BaseService<TytNoticePopupTempl,Long> {
    /**
     * 翻译弹框内容
     * @param popup
     * @return
     */
    PopupListBean getPopupList(TytNoticePopup popup);

    /**
     * 获取引导框
     * @return
     */
    PopupListBean getPicList(Long userId);

    /**
     * 根据弹框类型获取弹框模板
     * @param type1
     * @param type2
     * @return
     */
    TytNoticePopupTempl getByType(int type1, int type2);

    /**
     * 获取弹窗内容
     * @param popup  弹窗类型
     * @param masterParams  替换参数  逗号分隔
     * @return  templ
     */
    PopupTemplBean getTemplByType(PopupTypeEnum popup, String masterParams);
}
