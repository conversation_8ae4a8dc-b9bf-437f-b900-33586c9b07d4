package com.tyt.noticePopup.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytNoticePopup;
import com.tyt.model.TytNoticePopupTempl;
import com.tyt.noticePopup.bean.PopupListBean;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.transport.querybean.PopupTemplBean;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("tytNoticePopupTemplService")
public class TytNoticePopupTemplServiceImpl extends BaseServiceImpl<TytNoticePopupTempl,Long>implements TytNoticePopupTemplService {

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "tytNoticePopuTemplDao")
    public void setBaseDao(BaseDao<TytNoticePopupTempl, Long> tytNoticePopuTemplDao) {
        super.setBaseDao(tytNoticePopuTemplDao);
    }

    private static Long IS_CAR_VIP_TEMPL = 18L;
    private static Long IS_NOT_CAR_VIP_TEMPL = 19L;

    @Override
    public PopupListBean getPopupList(TytNoticePopup popup){
        PopupListBean listBean = new PopupListBean();
        BeanUtils.copyProperties(popup, listBean);
        TytNoticePopupTempl templ = this.getBaseDao().getByIdForLock(popup.getTemplId());
        BeanUtils.copyProperties(templ, listBean);
        if (StringUtils.isNotBlank(popup.getContent())){
            String[] paramResult = popup.getContent().split(",");
            String masterContent = listBean.getMasterContent();
            for (int i=0;i<paramResult.length;i++){
                masterContent = StringUtils.replace(masterContent,"{param"+(i+1)+"}",paramResult[i]);
            }
            listBean.setMasterContent(masterContent);
        }
        return listBean;
    }

    @Override
    public PopupListBean getPicList(Long userId) {
        boolean isCarVip = userPermissionService.isVipCallPhonePermission(userId);
        Long templId = IS_NOT_CAR_VIP_TEMPL;
        if (isCarVip){
            templId = IS_CAR_VIP_TEMPL;
        }
        TytNoticePopupTempl templ = this.getBaseDao().getByIdForLock(templId);
        if (templ!=null && templ.getStatus()==1){
            PopupListBean bean = new PopupListBean();
            BeanUtils.copyProperties(templ, bean);
            return bean;
        }
        return null;
    }

    @Override
    public TytNoticePopupTempl getByType(int type1, int type2){
        String sql = "select * from tyt_notice_popup_templ WHERE type1=? AND type2=?";
        List<TytNoticePopupTempl> list = this.getBaseDao().queryForList(sql, new Object[]{type1,type2});
        if (list!=null && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    @Override
    public PopupTemplBean getTemplByType(PopupTypeEnum popup,String masterParams){
        TytNoticePopupTempl templ = this.getByType(popup.getType1(),popup.getType2());
        if (templ!=null){
            PopupTemplBean templBean = new PopupTemplBean();
            BeanUtils.copyProperties(templ,templBean);
            if (StringUtils.isNotBlank(masterParams)){
                String[] paramResult = masterParams.split(",");
                List<String> param = new ArrayList<>();
                String masterContent = templ.getMasterContent();
                for (int i=0;i<paramResult.length;i++){
                    masterContent = StringUtils.replace(masterContent,"{param"+(i+1)+"}",paramResult[i]);
                }
                templBean.setMasterContent(masterContent);
            }
            return templBean;
        }
        return null;
    }
}
