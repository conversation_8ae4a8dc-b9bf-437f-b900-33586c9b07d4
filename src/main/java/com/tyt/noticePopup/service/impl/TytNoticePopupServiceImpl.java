package com.tyt.noticePopup.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytNoticePopup;
import com.tyt.model.TytNoticePopupTempl;
import com.tyt.noticePopup.bean.PopupListBean;
import com.tyt.noticePopup.bean.PopupSaveBean;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.noticePopup.service.TytNoticePopupService;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.util.TimeUtil;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("tytNoticePopupService")
public class TytNoticePopupServiceImpl extends BaseServiceImpl<TytNoticePopup,Long> implements TytNoticePopupService {

    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;

    @Resource(name = "tytNoticePopupDao")
    public void setBaseDao(BaseDao<TytNoticePopup, Long> tytNoticePopupDao) {
        super.setBaseDao(tytNoticePopupDao);
    }

    @Override
    public PopupListBean updateGetPopupList(Long userId,int pushPort) throws Exception{
        StringBuffer selectSql = new StringBuffer("SELECT `id` id, `content` content, `templ_id` templId FROM `tyt_notice_popup` WHERE 1=1");
        List<Object> list = new ArrayList<Object>();
        selectSql.append(" and receive_id=?");
        list.add(userId);
        selectSql.append(" and receive_status=?");
        list.add(1);
        selectSql.append(" and status=?");
        list.add(1);
        selectSql.append(" and ctime>?");
        list.add(TimeUtil.weeHours(new Date(),0));
        if (pushPort == 0){
            selectSql.append(" and origin_popup=1");
        }else if (pushPort ==1){
            selectSql.append(" and car_popup=1");
        } else if (pushPort == 5){ //新pc
            TytNoticePopupTempl templ = tytNoticePopupTemplService.getByType(PopupTypeEnum.车次卡权益获取提醒.getType1(),PopupTypeEnum.车次卡权益获取提醒.getType2());
            selectSql.append(" and car_popup=1 and templ_id =?");
            list.add(templ.getId());
        }else{
            selectSql.append(" and goods_popup=1");
        }
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        scalarMap.put("content", Hibernate.STRING);
        scalarMap.put("templId", Hibernate.LONG);
        List<TytNoticePopup> popups = this.getBaseDao().search(selectSql.toString(), scalarMap, TytNoticePopup.class, list.toArray(),1,1);
        PopupListBean popupBean = null;
        if (popups != null && popups.size() > 0){
            TytNoticePopup popup = popups.get(0);
            popupBean = tytNoticePopupTemplService.getPopupList(popup);
            String updateSql = "UPDATE `tyt_notice_popup` SET `receive_time`=now(),`receive_status`=2 WHERE `receive_id`=:receiveId AND `status`=1 AND `receive_status`=1 AND id=:id";
            Map<String ,Object> map =new HashMap<>();
            map.put("receiveId", userId);
            map.put("id",popup.getId());
            this.getBaseDao().executeUpdateSql(updateSql, map);
        }
        return popupBean;
    }

    @Override
    public void savePopup(PopupSaveBean saveBean){
        TytNoticePopupTempl templ = tytNoticePopupTemplService.getByType(saveBean.getPopupTypeEnum().getType1(),saveBean.getPopupTypeEnum().getType2());
        TytNoticePopup noticePopup = new TytNoticePopup();
        noticePopup.setContent(saveBean.getParams());
        noticePopup.setProductionId(saveBean.getProductionId());
        noticePopup.setReceiveId(saveBean.getReceiveId());
        if (templ!=null){
            noticePopup.setTemplId(templ.getId());
            noticePopup.setTitle(templ.getTitle());
            noticePopup.setType(templ.getType1());
        }
        noticePopup.setReceiveStatus(1);
        noticePopup.setCtime(new Date());
        noticePopup.setStatus(1);
        noticePopup.setOriginPopup(saveBean.getOriginPopup());
        noticePopup.setCarPopup(saveBean.getCarPopup());
        noticePopup.setGoodsPopup(saveBean.getGoodsPopup());
        this.getBaseDao().insertSave(noticePopup);
    }

    @Override
    public void savePopup(PopupTypeEnum popupTypeEnum,Long userId,Integer clientPort){
        PopupSaveBean saveBean = new PopupSaveBean();
        saveBean.setPopupTypeEnum(popupTypeEnum);
        saveBean.setReceiveId(userId);
        if (clientPort == 1){
            saveBean.setCarPopup(1);
        }else if (clientPort == 2){
            saveBean.setGoodsPopup(1);
        }else{
            saveBean.setOriginPopup(1);
        }
        savePopup(saveBean);
    }

}
