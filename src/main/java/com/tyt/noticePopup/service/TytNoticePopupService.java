package com.tyt.noticePopup.service;

import com.tyt.model.TytNoticePopup;
import com.tyt.model.TytNoticePopupTempl;
import com.tyt.noticePopup.bean.PopupListBean;
import com.tyt.noticePopup.bean.PopupSaveBean;
import com.tyt.noticePopup.enums.PopupTypeEnum;

import java.util.List;

public interface TytNoticePopupService {
    /**
     * 弹框提醒
     * @param userId
     * @return
     */
    PopupListBean updateGetPopupList(Long userId, int pushPort)throws Exception;

    void savePopup(PopupSaveBean saveBean);

    void savePopup(PopupTypeEnum popupTypeEnum, Long userId, Integer clientPort);
}
