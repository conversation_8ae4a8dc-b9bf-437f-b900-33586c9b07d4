package com.tyt.noticePopup.controller;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.noticePopup.bean.PopupListBean;
import com.tyt.noticePopup.service.TytNoticePopupService;
import com.tyt.noticePopup.service.TytNoticePopupTemplService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping("plat/notice/popup/new")
public class NoticePopupController extends BaseController {

    @Resource(name = "tytNoticePopupService")
    private TytNoticePopupService tytNoticePopupService;
    @Resource(name = "tytNoticePopupTemplService")
    private TytNoticePopupTemplService tytNoticePopupTemplService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    /**
     * 弹框提醒（5930）
     * @param baseParameter param
     * @return rm
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public ResultMsgBean getPopupList(BaseParameter baseParameter){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            int clientSign = Integer.parseInt(baseParameter.getClientSign());
            int pushPort = 0;
            if (Constant.ClientSignEnum.isNewPc(clientSign)){
                pushPort = 5;//pc
            }else{
                pushPort = Constant.isCarOrGoodsOrOrigin(clientSign);
            }
            PopupListBean list = tytNoticePopupService.updateGetPopupList(baseParameter.getUserId(),pushPort);
            resultMsgBean.setData(list);

        }catch (Exception e){
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器异常");
        }
        return resultMsgBean;
    }

    /**
     *获取引导框
     * @param userId  用户id
     * @return rm
     */
    @RequestMapping(value = "/picList")
    @ResponseBody
    public ResultMsgBean getPicList(Long userId){
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            boolean isOpen= tytConfigService.isEqualsOne(Constant.EQUITY_ON_OFF);
            if (isOpen){
                PopupListBean bean = tytNoticePopupTemplService.getPicList(userId);
                resultMsgBean.setData(bean);
            }
        }catch (Exception e){
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器异常");
        }
        return resultMsgBean;
    }

}
