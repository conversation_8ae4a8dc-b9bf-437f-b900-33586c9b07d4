package com.tyt.permission.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.adposition.service.TytUserArchivesService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.enums.PermissionGainTypeEnum;
import com.tyt.enums.PermissionGainTypeNewEnum;
import com.tyt.goods.service.GoodsService;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.infofee.bean.UserPermissionResponseBean;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.service.UserCancelService;
import com.tyt.marketingActivity.enums.ActivityTypeEnum;
import com.tyt.marketingActivity.service.MarketingActivityService;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.messagecenter.core.vo.mq.ShortMessageBean;
import com.tyt.model.*;
import com.tyt.noticePopup.enums.PopupTypeEnum;
import com.tyt.permission.bean.*;
import com.tyt.permission.service.PermissionSortService;
import com.tyt.permission.service.ServicePermissionService;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.TytUserPermissionGain;
import com.tyt.plat.entity.base.TytUserPermissionUsed;
import com.tyt.plat.mapper.base.TytUserPermissionGainMapper;
import com.tyt.plat.mapper.base.TytUserPermissionUsedMapper;
import com.tyt.plat.service.mq.MessageCenterPushService;
import com.tyt.promo.service.ICouponService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.enums.UserPermissionTypeEnum;
import com.tyt.util.Constant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import com.tyt.util.TytSwitchUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.tyt.user.enums.UserPermissionTypeEnum.*;


@Service("userPermissionService")
public class UserPermissionServiceImpl extends BaseServiceImpl<UserPermission, Long> implements UserPermissionService {
    public static Logger logger = LoggerFactory.getLogger(UserPermissionServiceImpl.class);

    @Resource(name = "permissionSortService")
    private PermissionSortService permissionSortService;

    @Resource(name = "servicePermissionService")
    private ServicePermissionService servicePermissionService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    @Resource(name = "tytMessageTmplService")
    private TytMessageTmplService messageTmplService;

    @Autowired
    private UserBuyGoodsService userBuyGoodsService;

    @Autowired
    private ICouponService couponService;

    @Autowired
    private UserCancelService userCancelService;

    @Autowired
    private MarketingActivityService marketingActivityService;

    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private TytUserPermissionUsedMapper tytUserPermissionUsedMapper;

    @Autowired
    private TytUserPermissionGainMapper tytUserPermissionGainMapper;

    @Autowired
    private TytUserArchivesService tytUserArchivesService;

    @Resource(name = "userPermissionDao")
    @Override
    public void setBaseDao(BaseDao<UserPermission, Long> userPermissionDao) {
        super.setBaseDao(userPermissionDao);
    }

    /**
     * 类型 - 年
     */
    public final static String UNIT_YEAR = "year";

    /**
     * 类型 - 日
     */
    public final static String UNIT_DAY = "day";

    /**
     * 权益使用次数，MAP结构，目前包含：电话拨打、信用查询
     */
    public final static String REDIS_PERMISSION = "permission:record:";

    //货app注册权益发放总数
    public static final String GOODS_GRANT_TOTAL_NUM_KEY = "goods_grant_total_num";

    public static final String GOODS_REGISTER_ACTIVITY_SMS_TMPL = "goods_register_activity_sms";

    /**
     * 记录权益变更日志
     *
     * @param userPermission 用户权益
     * @param changeType     权益变更类型 1-购买 2-赠送 3-次卡次数使用完 4-时间到期
     */
    @Override
    public void addUserPermissionLog(UserPermission userPermission, Integer changeType) {
        String sql = "INSERT INTO tyt_user_permission_log(user_id, service_permission_id, permission_sort_type, service_permission_type_id," +
                " service_permission_type_name, priority, start_time, end_time, status, total_num, used_num, change_type)" +
                " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        final Object[] params = {
                userPermission.getUserId(),
                userPermission.getServicePermissionId(),
                userPermission.getPermissionSortType(),
                userPermission.getServicePermissionTypeId(),
                userPermission.getServicePermissionTypeName(),
                userPermission.getPriority(),
                userPermission.getStartTime(),
                userPermission.getEndTime(),
                userPermission.getStatus(),
                userPermission.getTotalNum(),
                userPermission.getUsedNum(),
                changeType
        };
        this.getBaseDao().executeUpdateSql(sql, params);
        logger.info("用户{}权益变更日志记录完成, 类型{}", userPermission, changeType);
    }

    @Override
    public List<UserPermission> getPermissionListByUserId(Long userId) {
        String sql = "SELECT * FROM `tyt_user_permission` WHERE user_id = ?  ORDER BY service_permission_id";
        final Object[] params = {
                userId
        };
        List<UserPermission> permissions = this.getBaseDao().queryForList(sql, params);
        return permissions;
    }

    @Override
    public PermissionResult updateAuthPermissionReturn(Permission permission, Long userId) {
        PermissionResult result = new PermissionResult();
        // 1. 获取权益类型的对象
        ServicePermission servicePermission = servicePermissionService.getServicePermission(permission.getId());
        // 1.1 如果bindDep属性为null值，则表示用户拥有此项权益
        if(StringUtils.isBlank(servicePermission.getBindDep())) {
            result.setUse(true);
            return result;
        }
        // 2. 查询当前用户指定类型的权益列表
        List<UserPermission> userPermissions = this.getListByUserIdAndType(userId, servicePermission.getBindDep());
        // 3. 开始鉴权
        /**
         * 无权益 处理
         */
        if (userPermissions.isEmpty()) { // 无权益
            result.setUse(false);
            if (StringUtils.isNotBlank(servicePermission.getNoPermissionMsg())) {
                result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getNoPermissionMsg()));
            }
            return result;
        }
        /**
         * 日受限权益 处理
         */
        if (servicePermission.getDayLimit() != null && servicePermission.getDayLimit() != 0) {
            // redis map结构原子+1，记录拨打电话次数
            Long counter = RedisUtil.mapHincr(REDIS_PERMISSION + TimeUtil.getTodayStr() + ":" + userId, String.valueOf(permission.getId()), (int) Constant.CACHE_EXPIRE_TIME_24H);
            if (counter > servicePermission.getDayLimit()) {
                result.setUse(false);
                if (StringUtils.isNotBlank(servicePermission.getDayLimitMsg())) {
                    result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getDayLimitMsg()));
                }
                return result;
            }
        }
        /**
         * 如果是寄生依赖关系，存在依赖则表示有权益
         */
        if (servicePermission.getParasitic()) {
            if (CollectionUtils.isNotEmpty(userPermissions)) {
                result.setUse(true);
                return result;
            }
        }
        /**
         * 主动权益，即拨打电话、发货等
         */
        Date currDate = new Date();
        for (UserPermission userPermission : userPermissions) {
            int differDays = TimeUtil.daysBetween(currDate, userPermission.getEndTime());
            if (userPermission.getSubType().equals(1)) { // 年卡会员
                result.reset(); // 重置结果属性
                result.setType(userPermission.getPermissionSortType()); // 设置会员类型
                if (differDays >= 0 && differDays < servicePermission.getLessThanDay()) {
                    result.setUse(true);
                    if (StringUtils.isNotBlank(servicePermission.getExpireMsg())) {
                        result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getExpireMsg()));
                    }
                }
                if (differDays < 0) {
                    result.setUse(false);
                    if (StringUtils.isNotBlank(servicePermission.getNoPermissionMsg())) {
                        result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getNoPermissionMsg()));
                    }
                    continue;
                }
                break;
            } else if (userPermission.getSubType().equals(2) || userPermission.getSubType().equals(3)) { // 通用次卡
                result.reset(); // 重置结果属性
                result.setType(userPermission.getPermissionSortType()); // 设置会员类型
                int leftTimes = userPermission.getTotalNum() - userPermission.getUsedNum(); // 剩余次数
                if ((differDays >= 0 && differDays < servicePermission.getLessThanDay())
                        || (leftTimes > 0 && leftTimes <= servicePermission.getLessThanTimes())) {
                    result.setUse(true);
                    if (StringUtils.isNotBlank(servicePermission.getExpireMsg())) {
                        result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getExpireMsg()));
                    }
                }
                if (differDays < 0 || leftTimes <= 0) {
                    result.setUse(false);
                    if (StringUtils.isNotBlank(servicePermission.getNoPermissionMsg())) {
                        result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getNoPermissionMsg()));
                    }
                    continue;
                }
                // 次卡 -1
                this.updateCountingCardDecr(userPermission, leftTimes,result);
                break;
            }
        }
        logger.info("用户{}权益鉴定结果:{}", userId, result);
        return result;
    }

    @Override
    public PermissionResult checkAuthPermission(Permission permission, Long userId) {
        PermissionResult result = new PermissionResult();
        // 1. 获取权益类型的对象
        ServicePermission servicePermission = servicePermissionService.getServicePermission(permission.getId());
        // 1.1 如果bindDep属性为null值，则表示用户拥有此项权益
        if(StringUtils.isBlank(servicePermission.getBindDep())) {
            result.setUse(true);
            return result;
        }
        // 2. 查询当前用户指定类型的权益列表
        List<UserPermission> userPermissions = this.getListByUserIdAndType(userId, servicePermission.getBindDep());
        // 3. 开始鉴权
        /**
         * 无权益 处理
         */
        if (userPermissions.isEmpty()) { // 无权益
            result.setUse(false);
            if (StringUtils.isNotBlank(servicePermission.getNoPermissionMsg())) {
                result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getNoPermissionMsg()));
            }
            return result;
        }
        /**
         * 日受限权益 处理
         */
        if (servicePermission.getDayLimit() != null && servicePermission.getDayLimit() != 0) {
            // redis map结构原子+1，记录拨打电话次数
            String dayLimitKey = REDIS_PERMISSION + TimeUtil.getTodayStr() + ":" + userId;
            String limitHashKey = String.valueOf(permission.getId());

            String limitValueStr = RedisUtil.getMapValue(dayLimitKey, limitHashKey);

            Integer limitValue = 0;
            if(StringUtils.isNotBlank(limitValueStr)){
                limitValue = Integer.parseInt(limitValueStr);
            }

            if (limitValue >= servicePermission.getDayLimit()) {
                result.setUse(false);
                if (StringUtils.isNotBlank(servicePermission.getDayLimitMsg())) {
                    result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getDayLimitMsg()));
                }
                return result;
            }
        }
        /**
         * 如果是寄生依赖关系，存在依赖则表示有权益
         */
        if (servicePermission.getParasitic()) {
            if (CollectionUtils.isNotEmpty(userPermissions)) {
                result.setUse(true);
                return result;
            }
        }
        /**
         * 主动权益，即拨打电话、发货等
         */
        Date currDate = new Date();
        for (UserPermission userPermission : userPermissions) {
            int differDays = TimeUtil.daysBetween(currDate, userPermission.getEndTime());
            if (userPermission.getPermissionSortType().equals(1)) { // 年卡会员
                result.reset(); // 重置结果属性
                result.setType(userPermission.getPermissionSortType()); // 设置会员类型
                if (differDays >= 0 && differDays < servicePermission.getLessThanDay()) {
                    result.setUse(true);
                    if (StringUtils.isNotBlank(servicePermission.getExpireMsg())) {
                        result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getExpireMsg()));
                    }
                }
                if (differDays < 0) {
                    result.setUse(false);
                    if (StringUtils.isNotBlank(servicePermission.getNoPermissionMsg())) {
                        result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getNoPermissionMsg()));
                    }
                    continue;
                }
                break;
            } else if (userPermission.getPermissionSortType().equals(2)) { // 次卡会员
                result.reset(); // 重置结果属性
                result.setType(userPermission.getPermissionSortType()); // 设置会员类型
                Integer leftTimes = userPermission.getTotalNum() - userPermission.getUsedNum(); // 剩余次数
                if ((differDays >= 0 && differDays < servicePermission.getLessThanDay())
                        || (leftTimes > 0 && leftTimes <= servicePermission.getLessThanTimes())) {
                    result.setUse(true);
                    if (StringUtils.isNotBlank(servicePermission.getExpireMsg())) {
                        result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getExpireMsg()));
                    }
                }
                if (differDays < 0 || leftTimes <= 0) {
                    result.setUse(false);
                    if (StringUtils.isNotBlank(servicePermission.getNoPermissionMsg())) {
                        result.setPopupTypeEnum(PopupTypeEnum.getByName(servicePermission.getNoPermissionMsg()));
                    }
                    continue;
                }
                // 只做校验，不做次卡 -1
                //this.updateCountingCardDecr(userPermission, leftTimes);
                break;
            }
        }
        logger.info("用户{}权益鉴定结果:{}", userId, result);
        return result;
    }

    @Override
    public List<UserPermission> getListByUserIdAndType(Long userId, String bindDep) {
        // 将 100101,100201 这样的字符串替换为：'100101','100201'，避免mysql查询类型不匹配
        bindDep = bindDep.replace(",", "','");
        StringBuilder bindDepBuilder = new StringBuilder(bindDep);
        bindDepBuilder.insert(0, "'").insert(bindDepBuilder.length(), "'");

        String sql = "SELECT * FROM `tyt_user_permission` WHERE user_id = ? " +
                " and service_permission_type_id in (" + bindDepBuilder.toString() + ") and status = 1 ORDER BY priority";
        final Object[] params = {
                userId
        };
        List<UserPermission> permissions = this.getBaseDao().queryForList(sql, params);
        return permissions;
    }


    @Override
    public int getSearchPermissionNum(Long userId) {
        List<UserPermission> userPermissions = this.getListByUserIdAndType(userId,"100101,100102,100103");
        int count = 0;
        if (CollectionUtils.isNotEmpty(userPermissions)){
            for (UserPermission userPermission : userPermissions) {
                if (userPermission.getServicePermissionTypeId().equals(UserPermissionTypeEnum.CAR_VIP.getTypeId())) {
                    return -1;
                }
                if (userPermission.getServicePermissionTypeId().equals(UserPermissionTypeEnum.CAR_NUM_MEAL.getTypeId())
                        || userPermission.getServicePermissionTypeId().equals(UserPermissionTypeEnum.CAR_NUM.getTypeId())) {
                    int num = userPermission.getTotalNum() - userPermission.getUsedNum();
                    count += num;
                }
            }
        }
        return count;
    }

    /**
     * 处理新增发货次卡使用逻辑
     */
    private Long doPermissionGainUse(UserPermission userPermission){

        String servicePermissionTypeId = userPermission.getServicePermissionTypeId();

        if(!UserPermissionTypeEnum.GOODS_NUM.equalsCode(servicePermissionTypeId)
                && !GOODS_NUM_NEW.equalsCode(servicePermissionTypeId)
                && !CAR_NUM.equalsCode(servicePermissionTypeId)
                && !CAR_NUM_MEAL.equalsCode(servicePermissionTypeId)){
            return null;
        }
        Date nowTime = new Date();

        Date dayStartTime = DateUtil.startOfDay(nowTime);

        String servicePermissionTypeName = userPermission.getServicePermissionTypeName();
        Long userId = userPermission.getUserId();

        TytUserPermissionGain firstPermissionGain = tytUserPermissionGainMapper.getFirstPermissionGain(dayStartTime, userId, servicePermissionTypeId);

        if(firstPermissionGain == null){
            logger.error("TytUserPermissionGain_is_null ########## userId : {}", userId);
            //可能由于某些异常情况，导致 gain 和 user_permission 不一致
            return 0L;
        }

        Long gainId = firstPermissionGain.getId();

        tytUserPermissionGainMapper.updatePermissionUseCount(gainId);

        //保存使用记录
        TytUserPermissionUsed userPermissionUsed = new TytUserPermissionUsed();
        userPermissionUsed.setUserId(userId);
        userPermissionUsed.setServicePermissionTypeId(servicePermissionTypeId);
        userPermissionUsed.setServicePermissionTypeName(servicePermissionTypeName);
        userPermissionUsed.setCreateTime(nowTime);
        userPermissionUsed.setSrcMsgId(0L);
        userPermissionUsed.setGainId(gainId);

        tytUserPermissionUsedMapper.insertSelective(userPermissionUsed);
        return userPermissionUsed.getId();
    }

    @Override
    public int updateCountingCardDecr(UserPermission userPermission, Integer leftTimes,PermissionResult result) {
        int usedNum = userPermission.getUsedNum() + 1;
        userPermission.setUsedNum(usedNum);
        if (usedNum == userPermission.getTotalNum()) {
            userPermission.setStatus(2);
            logger.info("用户{} 次卡权益使用完成，记录权益变更记录", userPermission.getUserId());
            this.addUserPermissionLog(userPermission, PermissionChangeType.次卡到期.getId());

            //如果是货会员次卡权益消耗完后，发放待生效的权益，更新购买商品生效权益状态为已失效
            if (GOODS_NUM_NEW.equalsCode(userPermission.getServicePermissionTypeId())) {
                logger.info("货会员次卡权益消耗完 更新购买商品生效权益状态为已失效");
                TytUserBuyGoods delayedGoods = userBuyGoodsService.getDelayedGoods(userPermission.getUserId(), 1);
                if (ObjectUtil.isNotNull(delayedGoods)) {
                    //发放权益
                    logger.info("货会员次卡权益消耗完 发放权益");
                    this.sendUserPermission2MQ(userPermission.getUserId(), delayedGoods.getGoodsId(), PermissionChangeType.购买.getId(), delayedGoods, null);
                    //更新用户档案
                    tytUserArchivesService.updateArchivesValidGoodsPermission(delayedGoods);
                }else{
                    tytUserArchivesService.updateUserArchivesGoodsPermission(userPermission.getUserId(), "货会员过期", TimeUtil.getTimeStamp());
                }
                //更新生效状态
                userBuyGoodsService.updateDelayedGoodsStatus(ObjectUtil.isNotNull(delayedGoods) ? delayedGoods.getId() : null, userPermission.getUserId());
            }
        }
        logger.info("用户{} 更新权益使用次数+1，usedNum={},status={}", userPermission.getUserId(), userPermission.getUsedNum(), userPermission.getStatus());
        this.update(userPermission);

        Long usedRecordId = this.doPermissionGainUse(userPermission);
        result.setUsedRecordId(usedRecordId);
        return 1;
    }

    @Override
    public void giveExperienceMember(User user,Integer clientPort) {  //1车app   2货app   0原app
        //判断是否给用户赠送权益
        if(isGivePermission(user)){
            Long userId = user.getId();
            //发放体验权益，包含车体验、货体验
            if (clientPort == 1){
                try {
                    TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, GoodsType.车体验.getId(), "login", 2);
                    this.sendUserPermission2MQ(userId, GoodsType.车体验.getId(), PermissionChangeType.赠送.getId(), userBuyGoods, null);
                    logger.info("为用户{}发放车体验用户权益", userId);
                } catch (Exception e) {
                    logger.error("增加购买赠送车体验权益记录", e);
                }
            }else if (clientPort == 2){
                try {
                    Integer platId = user.getPlatId();
                    Long goodsId = GoodsType.货体验.getId();
                    if(TytSwitchUtil.isGoodsGivewayPermissionOn()){
                        goodsId = GoodsType.货主权益赠送活动.getId();
                    }
                    //是否是货app注册用户
                    if (Constant.isCarOrGoodsOrOrigin(platId)==2){
                        //判断注册活动是否有效
                        MarketingActivity activity = marketingActivityService.getValidActivityByType(ActivityTypeEnum.goods_register_activity.getId(),2);
                        if (activity!=null){
                            //获取商品id
                            String goodsIds = TytSwitchUtil.getGoodsRegisterActivityGoodsIds();
                            if (StringUtils.isNotBlank(goodsIds)){
                                String[] ids = goodsIds.split(",");
                                String count = RedisUtil.get(GOODS_GRANT_TOTAL_NUM_KEY);
                                if (count == null){
                                    goodsId = Long.parseLong(ids[0]);
                                    RedisUtil.set(GOODS_GRANT_TOTAL_NUM_KEY,"1",(int)Constant.CACHE_EXPIRE_TIME_48H);
                                }else{
                                    goodsId = Long.parseLong(ids[Integer.parseInt(count)%ids.length]);
                                    RedisUtil.set(GOODS_GRANT_TOTAL_NUM_KEY,Integer.parseInt(count)+1+"",(int)Constant.CACHE_EXPIRE_TIME_48H);
                                }
                                //发短信
                                sendRemindMessage(goodsId,user.getCellPhone());
                            }
                        }
                    }
                    TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, goodsId, "login", 2);
                    this.sendUserPermission2MQ(userId, goodsId, PermissionChangeType.赠送.getId(), userBuyGoods, null);
                    logger.info("为用户{}发放货体验用户权益", userId);
                } catch (Exception e) {
                    logger.error("增加购买赠送货体验权益记录", e);
                }
            }else{
                try {
                    TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, GoodsType.体验会员.getId(), "login", 2);
                    this.sendUserPermission2MQ(userId, GoodsType.体验会员.getId(), PermissionChangeType.赠送.getId(), userBuyGoods, null);
                    logger.info("为用户{}发放体验用户权益", userId);
                } catch (Exception e) {
                    logger.error("增加购买赠送体验权益记录", e);
                }
            }
        }
    }


    @Override
    public void giveTrialMember(User user) {
        try {
            //判断是否给用户赠送权益
            if(isGivePermission(user)){
                //用户ID
                Long userId = user.getId();
                TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, GoodsType.车试用.getId(), "login", 2);
                //发放试用权益
                this.sendUserPermission2MQ(userId, GoodsType.车试用.getId(), PermissionChangeType.赠送.getId(), userBuyGoods, null);
                logger.info("为用户{}发放试用用户权益", userId);
            }
        } catch (Exception e) {
            logger.error("增加购买赠送试用权益记录", e);
        }
    }

    /**
     * @description 发放1+9发货次卡权益
     * <AUTHOR>
     * @date 2022/8/1 12:30
     * @param user
     * @return void
     */
    @Override
    public void giveGoodsCard(User user) {
        //用户ID
        Long userId = user.getId();
        try {
            TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, GoodsType.发货次卡赠送活动.getId(), "login", 2);
            this.sendUserPermission2MQ(userId, GoodsType.发货次卡赠送活动.getId(), PermissionChangeType.赠送.getId(), userBuyGoods, null);
            logger.info("为用户{}发放1+9发货次卡", userId);
        } catch (Exception e) {
            logger.error("为用户发放1+9发货次卡发生错误", e);
        }
    }

    @Override
    public void giveGoodsCard(Long userId, GoodsType goodsType, PermissionChangeType permissionChangeType,
            String buyPath,PermissionGainTypeEnum permissionGainTypeEnum) {
        try {
            logger.info("发放权益, userId:{}, 权益类型:{}, 发送类型:{}, buyPath：{}", userId, goodsType,
                    permissionChangeType, buyPath);
            TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, goodsType.getId(),
                    buyPath, 2);
            this.sendUserPermission2MQ(userId, goodsType.getId(), permissionChangeType.getId(), userBuyGoods,null);
        } catch (Exception e) {
            logger.error("发放权益失败, userId:{}, 权益类型:{}, 发送类型:{}, buyPath：{}", userId, goodsType,
                    permissionChangeType, buyPath);
        }
    }

    @Override
    public void giveGoodsCard(Long userId, GoodsType goodsType, PermissionChangeType permissionChangeType,
                              String buyPath,PermissionGainTypeNewEnum permissionGainTypeEnum) {
        try {
            logger.info("发放权益, userId:{}, 权益类型:{}, 发送类型:{}, buyPath：{}", userId, goodsType,
                    permissionChangeType, buyPath);
            TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, goodsType.getId(),
                    buyPath, 2);
            ExposureRecordInfo exposureRecordInfo = ExposureRecordInfo.builder()
                    .sendType(0)
                    .gainType(permissionGainTypeEnum.getId())
                    .build();
            this.sendUserPermission2MQ(userId, goodsType.getId(), permissionChangeType.getId(), userBuyGoods,exposureRecordInfo);
        } catch (Exception e) {
            logger.error("发放权益失败, userId:{}, 权益类型:{}, 发送类型:{}, buyPath：{}", userId, goodsType,
                    permissionChangeType, buyPath);
        }
    }

    /**
     * 发放7天10次曝光次卡
     * @param user
     */
    @Override
    public void giveExposureCard(User user) {
        //用户ID
        Long userId = user.getId();
        try {
            TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, GoodsType.货源曝光权益7天10次.getId(), "publish", 2);
            ExposureRecordInfo exposureRecordInfo = ExposureRecordInfo.builder()
                    .sendType(0)
                    .gainType(PermissionGainTypeNewEnum.发布保障货源.getId())
                    .build();
            this.sendUserPermission2MQ(userId, GoodsType.货源曝光权益7天10次.getId(), PermissionChangeType.赠送.getId(), userBuyGoods, exposureRecordInfo);
            logger.info("为用户{}发放7天10次曝光次卡", userId);
        } catch (Exception e) {
            logger.error("为用户发放7天10次曝光次卡发生错误", e);
        }
    }

    /**
     * 购买商品成功,发送分配用户权益的MQ消息
     *
     * @param userId     用户ID
     * @param goodsId    商品ID
     * @param changeType 变更类型(1.购买 2.赠送 3.次卡用完 4.时间到期 )
     * @param userBuyGoods 商品订单对象
     * <AUTHOR>
     * @Date 2019/7/2 15:39
     */
    @Override
    public void sendUserPermission2MQ(Long userId, Long goodsId, Integer changeType, TytUserBuyGoods userBuyGoods, ExposureRecordInfo exposureRecordInfo) {

        MqUserPermissionMsg mqMsg = new MqUserPermissionMsg();
        mqMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        mqMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_USER_PERMISSION_MESSAGE);
        //用户ID
        mqMsg.setUserId(userId);
        //商品ID
        mqMsg.setGoodsId(goodsId);
        //变更类型(1.购买 2.赠送 ...... )
        mqMsg.setChangeType(changeType);
        //商品订单信息
        mqMsg.setUserBuyGoods(userBuyGoods);
        //获取方式：主要针对曝光次卡，其它权益默认传 100-赠送
        mqMsg.setExposureRecordInfo(exposureRecordInfo);

        // 保存发送mq
        final String messageSerailNum = mqMsg.getMessageSerailNum();
        final String mqJson = JSON.toJSONString(mqMsg);
        final int messageType = mqMsg.getMessageType();
        logger.info("用户权益发放，发送MQ:{}", mqJson);
        // 建立线程池
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                //发送并mq信息并保存到数据库
                tytMqMessageService.addSaveMqMessage(messageSerailNum, mqJson, MqBaseMessageBean.MESSAGETYPE_USER_PERMISSION_MESSAGE);
                tytMqMessageService.sendMqMessage(messageSerailNum, mqJson, MqBaseMessageBean.MESSAGETYPE_USER_PERMISSION_MESSAGE);
            }
        });
        // 关闭线程
        executorService.shutdown();
    }


    @Override
    public boolean isVipCallPhonePermission(Long userId) {

        List list = this.getListByUserIdAndType(userId, "100101");
        return list != null && list.size() > 0;
    }

    @Override
    public boolean isVipPublishPermission(Long userId) {
        List list = this.getListByUserIdAndType(userId, "100201");
        return list != null && list.size() > 0;

    }

    @Override
    public boolean isPublishCardPermission(Long userId) {
        String sql = "SELECT * FROM `tyt_user_permission` WHERE user_id = ? and service_permission_type_id ='100202' and total_num - used_num > 0 ORDER BY service_permission_id";
        final Object[] params = {
                userId
        };
        List<UserPermission> list = this.getBaseDao().queryForList(sql, params);
        return list != null && list.size() > 0;
    }


    private List<UserPermission> getUserVipPermission(Long userId) {
        String sql = "SELECT * FROM `tyt_user_permission` WHERE user_id = ? and service_permission_type_id in ('100101','100201') ORDER BY service_permission_id";
        final Object[] params = {
                userId
        };
        List<UserPermission> permissions = this.getBaseDao().queryForList(sql, params);
        return permissions;
    }

    @Override
    public UserPermission getUserPermission(Long userId, String typeId) {
        String sql = "SELECT * FROM `tyt_user_permission` WHERE user_id = ? and service_permission_type_id=? ORDER BY service_permission_id";
        final Object[] params = {
                userId, typeId
        };
        List<UserPermission> permissions = this.getBaseDao().queryForList(sql, params);
        if (permissions != null && permissions.size() > 0) {
            return permissions.get(0);
        }
        return null;
    }

    @Override
    public UserPermissionBean getUserPermission(Long userId) throws Exception {
        UserPermissionBean permissionBean = new UserPermissionBean();
        boolean isExistCarVipCoupon = couponService.isExistCarVipCouponByUserId(userId);
        boolean isExistGoodsVipCoupon = couponService.isExistGoodsVipCouponByUserId(userId);
        if (isExistCarVipCoupon) {
            permissionBean.setCarCouponStatus("有可用优惠券");
        }
        if (isExistGoodsVipCoupon) {
            permissionBean.setGoodsCouponStatus("有可用优惠券");
        }
        String carVipStatus = "未购买";
        String carVipRemark = "购买后无限找货";
        String goodsVipStatus = "未购买";
        String goodsVipRemark = "购买后无限发货";
        String goodsNumRemark = "无发货次数";
        String carNumRemark = "无找货次数";
        boolean carVip = false;
        boolean goodsVip = false;
        List<UserPermission> permissions = getUserVipPermission(userId);
        if (permissions != null && permissions.size() > 0) {
            for (UserPermission permission : permissions) {
                String remark = "";
                String status = "";
                boolean isVip = false;
                if (permission.getStatus() == 1) {
                    remark = TimeUtil.parseDateStringA(permission.getEndTime()) + "到期";
                    int days = TimeUtil.daysBetween(new Date(), permission.getEndTime());
                    if (days > 30) {
                        status = "";
                    } else {
                        status = "即将到期";
                    }
                    isVip = true;
                } else {
                    status = "已过期";
                    int days = TimeUtil.daysBetween(permission.getEndTime(), new Date());
                    if (days < 365) {
                        remark = "已过期" + days + "天";
                    } else {
                        int years = days / 365;
                        remark = "已过期" + years + "年";
                    }
                }
                if ("100101".equals(permission.getServicePermissionTypeId())) {
                    carVipStatus = status;
                    carVipRemark = remark;
                    carVip = isVip;
                } else {
                    goodsVipStatus = status;
                    goodsVipRemark = remark;
                    goodsVip = isVip;
                }
            }
        }

        if (carVip) {
            carNumRemark = "无限找货";
        } else {
            UserPermission carNumPermission = getUserPermission(userId, "100102");
            if (carNumPermission != null) {
                if (carNumPermission.getStatus() == 1) {
                    int surplusNum = carNumPermission.getTotalNum() - carNumPermission.getUsedNum();
                    carNumRemark = surplusNum + "次找货," + TimeUtil.parseDateStringA(carNumPermission.getEndTime()) + "到期";
                }
            }
        }
        if (goodsVip) {
            goodsNumRemark = "无限发货";
        } else {
            UserPermission goodsNumPermission = getUserPermission(userId, "100202");
            if (goodsNumPermission != null) {
                if (goodsNumPermission.getStatus() == 1) {
                    int surplusNum = goodsNumPermission.getTotalNum() - goodsNumPermission.getUsedNum();
                    goodsNumRemark = surplusNum + "次发货," + TimeUtil.parseDateStringA(goodsNumPermission.getEndTime()) + "到期";
                }
            }
        }
        permissionBean.setCarVipStatus(carVipStatus);
        permissionBean.setCarVipRemark(carVipRemark);
        permissionBean.setGoodsVipStatus(goodsVipStatus);
        permissionBean.setGoodsVipRemark(goodsVipRemark);
        permissionBean.setCarNumRemark(carNumRemark);
        permissionBean.setGoodsNumRemark(goodsNumRemark);
        return permissionBean;
    }

    @Override
    public UserPermissionBean getUserPermissionByClientSign(Long userId) throws Exception {
        UserPermissionBean permissionBean = new UserPermissionBean();
        boolean isExistCarVipCoupon = couponService.isExistCarVipCouponByUserId(userId);
        boolean isExistGoodsVipCoupon = couponService.isExistGoodsVipCouponByUserId(userId);
        if (isExistCarVipCoupon) {
            permissionBean.setCarCouponStatus("有可用优惠券");
        }
        if (isExistGoodsVipCoupon) {
            permissionBean.setGoodsCouponStatus("有可用优惠券");
        }
        String carVipStatus = "未购买";
        String carVipRemark = "未购买";
        String goodsVipStatus = "未购买";
        String goodsVipRemark = "未购买";
        List<UserPermission> permissions = getUserVipPermission(userId);
        if (permissions != null && permissions.size() > 0) {
            for (UserPermission permission : permissions) {
                String remark = "";
                String status = "";
                if (permission.getStatus() == 1) {
                    remark = TimeUtil.parseDateStringA(permission.getEndTime()) + "到期";
                    int days = TimeUtil.daysBetween(new Date(), permission.getEndTime());
                    if (days > 30) {
                        status = "";
                    } else {
                        status = "即将到期";
                        //remark += " (剩余" + (days+1) + "天)";
                    }
                } else {
                    status = "已过期";
                    remark = "已过期";
                }
                if ("100101".equals(permission.getServicePermissionTypeId())) {
                    carVipStatus = status;
                    carVipRemark = remark;
                } else {
                    goodsVipStatus = status;
                    goodsVipRemark = remark;
                }
            }
        }
        permissionBean.setCarVipStatus(carVipStatus);
        permissionBean.setCarVipRemark(carVipRemark);
        permissionBean.setGoodsVipStatus(goodsVipStatus);
        permissionBean.setGoodsVipRemark(goodsVipRemark);
        return permissionBean;
    }

    /**
     * 发放微信分享赠送两次找货权益
     *
     * @param userId
     */
    @Override
    public void giveShare2CarTimesPermission(Long userId, Long goodsId) {
        try {
            TytUserBuyGoods userBuyGoods = userBuyGoodsService.saveUserBuyGoodsInfo(userId, goodsId, "share", 2);
            //发放试用权益
            this.sendUserPermission2MQ(userId, goodsId, PermissionChangeType.赠送.getId(), userBuyGoods, null);
            logger.info("为用户{}发放分享赠送两次找货权益权益", userId);
        } catch (Exception e) {
            logger.error("分享赠送两次找货权益异常", e);
        }
    }

    /**
     * @description 是否给用户赠送权益
     * <AUTHOR>
     * @date 2022/2/17 10:51
     * @param user
     * @return boolean
     */
    @Override
    public boolean isGivePermission(User user) {
        boolean flag = false;
        //用户手机号
        String cellPhone = user.getCellPhone();
        //1.如果用户不存在注销记录，则赠送权益
        if(!userCancelService.isUserCancelInfoExist(cellPhone)){
            flag = true;
        }
        //2. ...
        return flag;
    }

    @Override
    public UserPermission getPermissionListByUserIdAndServicePermissionTypeId(Long userId) {
        String sql = "SELECT * FROM `tyt_user_permission` WHERE user_id = ?  and service_permission_type_id = 100302 and status = 1 limit 1";
        final Object[] params = {
                userId
        };
        List<UserPermission> permissions = this.getBaseDao().queryForList(sql, params);
        if (permissions != null && permissions.size() > 0) {
            return permissions.get(0);
        }
        return null;
    }

    @Override
    public List<UserPermissionResponseBean> getUserOwnerPermission(Long userId,String numId,String vipId) {
        String sql = "SELECT service_permission_type_id as servicePermissionTypeId,status, total_num-used_num as remainNum,end_time as endTime,now() as systemTime,user_id as userId FROM `tyt_user_permission` WHERE user_id = ? and service_permission_type_id in (?,?) ORDER BY service_permission_id";
        final Object[] params = {
                userId,numId, vipId
        };
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("status", Hibernate.INTEGER);
        scalarMap.put("endTime", Hibernate.TIMESTAMP);
        scalarMap.put("systemTime", Hibernate.TIMESTAMP);
        scalarMap.put("servicePermissionTypeId", Hibernate.STRING);
        scalarMap.put("remainNum", Hibernate.INTEGER);
        List<UserPermissionResponseBean> permissionsList = this.getBaseDao().search(sql, scalarMap, UserPermissionResponseBean.class, params);
        return permissionsList;
    }

    @Override
    public List<UserPermissionResponseBean> getGoodsPermission(Long userId) {
        String sql = "SELECT service_permission_type_id as servicePermissionTypeId,status, total_num-used_num as remainNum,end_time as endTime,now() as systemTime,user_id as userId " +
                "FROM `tyt_user_permission` WHERE user_id = ? and service_permission_type_id in (?,?,?) ORDER BY priority";
        final Object[] params = {
                userId, "100201", "100202", "100203"
        };
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("userId", Hibernate.LONG);
        scalarMap.put("status", Hibernate.INTEGER);
        scalarMap.put("endTime", Hibernate.TIMESTAMP);
        scalarMap.put("systemTime", Hibernate.TIMESTAMP);
        scalarMap.put("servicePermissionTypeId", Hibernate.STRING);
        scalarMap.put("remainNum", Hibernate.INTEGER);
        List<UserPermissionResponseBean> permissionsList = this.getBaseDao().search(sql, scalarMap, UserPermissionResponseBean.class, params);
        return permissionsList;
    }

    private void sendRemindMessage(Long goodsId, String cellPhone) throws Exception{
        TytGoods goods =goodsService.getById(goodsId);
        if (goods == null){
            return ;
        }
        //获取短信模板
        String smsTmpl = messageTmplService.getSmsTmpl(GOODS_REGISTER_ACTIVITY_SMS_TMPL);
        String expireTime = TimeUtil.formatDateMonthDay(TimeUtil.addDay(new Date(),goods.getEffectiveTime()));
        String messageContent = StringUtils.replaceEach(smsTmpl, new String[]{"${goodsName}","${expireTime}"}, new String[]{goods.getName(),expireTime});
        //发送短信
        ShortMessageBean shortMessageBean =messageCenterPushService.createShortMessage(cellPhone,messageContent,"货app注册活动赠送权益短信提醒");
        messageCenterPushService.sendMultiMessage(shortMessageBean,null,null);
    }
    @Override
    public boolean judgeUserBuyGoods(Long userId) {
        // service_permission_type_id 100201(发货会员)  100202(发货次数)  100101(车会员)  100102(拨打次数)
        StringBuilder sqlBuilder = new StringBuilder().append("SELECT id FROM `tyt_user_permission` WHERE user_id=? AND service_permission_type_id = 100101 limit 1");
        Object[] params = {userId};
        Map<String, Type> scalarMap = new HashMap<String, Type>();
        scalarMap.put("id", Hibernate.LONG);
        List<TytUserBuyGoods> userBuyGoods = this.getBaseDao().search(sqlBuilder.toString(), scalarMap, TytUserBuyGoods.class, params);
        if(CollectionUtils.isEmpty(userBuyGoods)||userBuyGoods.size()==0){
            return false;
        }
        return true;
    }

    @Override
    public int getPublishGoodsPermissionCount(Long userId) {
        Date time = TimeUtil.addDay(-1);
        String sql = "SELECT COUNT(*) FROM tyt_user_permission WHERE `service_permission_id`='10020' AND end_time>? AND STATUS=1 AND user_id=?";
        BigInteger count = this.getBaseDao().query(sql, new Object[] { time,userId });
        return count.intValue();
    }

    @Override
    public List<UserPermissionGetBean> getUserPermissionByServiceId(Long userId, Integer port) {
        String numId = port == Constant.CAR_PORT? CAR_NUM.getTypeId():UserPermissionTypeEnum.GOODS_NUM.getTypeId();
        String vipId = port == Constant.CAR_PORT?UserPermissionTypeEnum.CAR_VIP.getTypeId():UserPermissionTypeEnum.GOODS_VIP.getTypeId();
        String sql = "SELECT * FROM `tyt_user_permission` WHERE user_id = ? and service_permission_type_id in(?,?)";
        final Object[] params = {
                userId,numId,vipId
        };
        List<UserPermission> userPermissions = this.getBaseDao().queryForList(sql, params);
        List<UserPermissionGetBean> permissions = new ArrayList<>();

        userPermissions = BeanUtil.copyToList(userPermissions, UserPermission.class);
        //兼容新会员权益
        Map<String, UserPermission> userPermissionMap = userPermissions.stream().collect(Collectors.toMap(UserPermission::getServicePermissionTypeId, a -> a, (k1, k2) -> k1));
        if (port == Constant.CAR_PORT) {
            this.compatibilityNewPermission(userId, userPermissionMap, CAR_NUM.getTypeId(), CAR_NUM_MEAL.getTypeId());
        } else {
            this.compatibilityNewPermission(userId, userPermissionMap, GOODS_NUM.getTypeId(), GOODS_NUM_NEW.getTypeId());
        }
        userPermissions = new ArrayList<>(userPermissionMap.values());
        if (CollectionUtils.isEmpty(userPermissions)){
            return permissions;
        }
        for (UserPermission userPermission : userPermissions) {
            UserPermissionGetBean userPermissionGetBean = new UserPermissionGetBean();
            BeanUtils.copyProperties(userPermission,userPermissionGetBean);
            if(userPermission.getStatus() == 1 && userPermission.getEndTime().before(TimeUtil.weeHours(new Date(), 0))){
                userPermissionGetBean.setStatus(3);
            }
            userPermissionGetBean.setSurplusNum(userPermission.getTotalNum() - userPermission.getUsedNum());
            permissions.add(userPermissionGetBean);
        }
        return permissions;
    }

    @Override
    public List<UserPermission> getUserPermissionByServiceIdNew(Long userId, int serviceId) {
        String sql = "SELECT * FROM `tyt_user_permission` WHERE user_id = ? and service_permission_id = ?";
        final Object[] params = {
                userId,serviceId
        };
        return this.getBaseDao().queryForList(sql, params);
    }

    @Override
    public void updatePublishPermissionTsId(Object usedRecordId, Long tsId) {
        try {
            Long recordId = (Long) usedRecordId;
            String sql = "update tyt_user_permission_used set src_msg_id = ? where id = ?";
            final Object[] params = { tsId, recordId };
            this.getBaseDao().executeUpdateSql(sql, params);
            logger.info("更新发货权益使用记录货源ID：{}，{}", recordId, tsId);
        } catch (Exception e) {
            logger.error("更新发货权益使用记录货源ID异常：{}，{}", JSON.toJSONString(usedRecordId), tsId);
        }
    }

    /**
     * 兼容新权益
     */
    public void compatibilityNewPermission(Long userId,
                                    Map<String, UserPermission> userPermissionMap,
                                    String oldPermissionType,
                                    String newPermissionType) {
        try {
            List<UserPermission> goodsNumNewPermissions = getListByUserIdAndType(userId, newPermissionType);
            if (!goodsNumNewPermissions.isEmpty()) {
                UserPermission carNumMealPermission = goodsNumNewPermissions.get(0);
                carNumMealPermission = BeanUtil.copyProperties(carNumMealPermission, UserPermission.class);
                if (carNumMealPermission.getStatus() == 1) {
                    if (userPermissionMap.containsKey(oldPermissionType)) {
                        UserPermission carNumPermission = userPermissionMap.get(oldPermissionType);
                        if (carNumPermission.getStatus() == 1) {
                            carNumPermission.setTotalNum(carNumPermission.getTotalNum() + carNumMealPermission.getTotalNum());
                            carNumPermission.setUsedNum(carNumPermission.getUsedNum() + carNumMealPermission.getUsedNum());
                        } else {
                            carNumPermission.setTotalNum(carNumMealPermission.getTotalNum());
                            carNumPermission.setUsedNum(carNumMealPermission.getUsedNum());
                            carNumPermission.setStatus(carNumMealPermission.getStatus());
                        }
                        carNumPermission.setEndTime(carNumMealPermission.getEndTime().after(carNumPermission.getEndTime())
                                ? carNumMealPermission.getEndTime() : carNumPermission.getEndTime());
                        userPermissionMap.put(oldPermissionType, carNumPermission);
                    } else {
                        carNumMealPermission.setServicePermissionTypeId(oldPermissionType);
                        userPermissionMap.put(oldPermissionType, carNumMealPermission);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("compatibility new permission error:", e);
        }
    }
}
