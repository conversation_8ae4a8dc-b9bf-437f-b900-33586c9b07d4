package com.tyt.permission.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.ServicePermission;
import com.tyt.permission.service.ServicePermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service("servicePermissionService")
public class ServicePermissionServiceImpl extends BaseServiceImpl<ServicePermission, Long> implements ServicePermissionService {
	public static Logger logger = LoggerFactory.getLogger(ServicePermissionServiceImpl.class);

	@Resource(name = "servicePermissionDao")
	public void setBaseDao(BaseDao<ServicePermission, Long> servicePermissionDao) {
		super.setBaseDao(servicePermissionDao);
	}

	@Override
	public ServicePermission getServicePermission(Long id) {
		return this.getById(id);
	}
}
