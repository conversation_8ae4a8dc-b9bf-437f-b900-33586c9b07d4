package com.tyt.permission.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.GoodsPermission;
import com.tyt.permission.service.GoodsPermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service("goodsPermissionService")
public class GoodsPermissionServiceImpl extends BaseServiceImpl<GoodsPermission, Long> implements GoodsPermissionService {
	public static Logger logger = LoggerFactory.getLogger(GoodsPermissionServiceImpl.class);

	@Resource(name = "goodsPermissionDao")
	public void setBaseDao(BaseDao<GoodsPermission, Long> goodsPermissionDao) {
		super.setBaseDao(goodsPermissionDao);
	}


}
