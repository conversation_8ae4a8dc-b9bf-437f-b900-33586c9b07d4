package com.tyt.permission.service.impl;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.PermissionSort;
import com.tyt.permission.service.PermissionSortService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service("permissionSortService")
public class PermissionSortServiceImpl extends BaseServiceImpl<PermissionSort, Long> implements PermissionSortService {
	public static Logger logger = LoggerFactory.getLogger(PermissionSortServiceImpl.class);

	@Resource(name = "permissionSortDao")
	public void setBaseDao(BaseDao<PermissionSort, Long> permissionSortDao) {
		super.setBaseDao(permissionSortDao);
	}

	@Override
	public List<PermissionSort> getPermissionByGoods(Long goodsId) {
		String sql = "SELECT p.* FROM `tyt_goods_permission` g LEFT JOIN permission_sort p ON g.permission_sort_id = p.id WHERE p.`status` = 1 and g.goods_id = ? ORDER BY p.service_permission_id, p.type";
		final Object[] params = {
				goodsId
		};
		List<PermissionSort> permissions = this.getBaseDao().queryForList(sql, params);
		return permissions;
	}
}
