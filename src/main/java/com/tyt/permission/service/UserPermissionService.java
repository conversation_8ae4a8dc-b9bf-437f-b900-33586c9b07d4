package com.tyt.permission.service;

import com.tyt.base.service.BaseService;
import com.tyt.enums.PermissionGainTypeEnum;
import com.tyt.enums.PermissionGainTypeNewEnum;
import com.tyt.infofee.bean.UserPermissionResponseBean;
import com.tyt.model.TytUserBuyGoods;
import com.tyt.model.User;
import com.tyt.model.UserPermission;
import com.tyt.permission.bean.*;

import java.util.List;
import java.util.Map;

public interface UserPermissionService extends BaseService<UserPermission, Long> {

    /**
     * 根据userId获取用户已存在的权益列表
     *
     * @param userId 用户ID
     * @return List<UserPermission>
     */
    List<UserPermission> getPermissionListByUserId(Long userId);

    /**
     * 记录权益变更日志
     *
     * @param userPermission 用户权益
     * @param changeType     权益变更类型 1-购买 2-赠送 3-次卡次数使用完 4-时间到期
     */
    void addUserPermissionLog(UserPermission userPermission, Integer changeType);

    /**
     * 权益鉴定结果
     *
     * @param permission 权益枚举值
     * @param userId     用户ID
     * @return 权益鉴定结果
     */
    PermissionResult updateAuthPermissionReturn(Permission permission, Long userId);

    /**
     * 检查用户是否有权益，不减权益，只做校验，目前已支持（查信用，）
     * 同方法 updateAuthPermissionReturn
     * 注意：使用该方法校验其他权益时，请先检查代码是否可以支持，然后添加备注
     * @param permission
     * @param userId
     * @return
     */
    PermissionResult checkAuthPermission(Permission permission, Long userId);

    /**
     * 通过用户ID和权益绑定的类型ID，获取用户权益列表
     *
     * @param userId  用户ID
     * @param bindDep 权益绑定关系的多个ID，例如：100101,100102,100201
     * @return 用户当前权益列表
     */
    List<UserPermission> getListByUserIdAndType(Long userId, String bindDep);

    int getSearchPermissionNum(Long userId);

    /**
     * 计次卡权益，次数原子 -1
     *
     * @param userPermission 用户权益内容
     * @param leftTimes      剩余次数
     * @return 更新条数
     */
    int updateCountingCardDecr(UserPermission userPermission, Integer leftTimes,PermissionResult result);

    /**
     * @Description  购买商品成功,发送分配用户权益的MQ消息
     * <AUTHOR>
     * @Date  2019/7/2 15:39
     * @Param [userId, goodsId, changeType, userBuyGoods, gainType]
     * @return void
     **/
    void sendUserPermission2MQ(Long userId, Long goodsId, Integer changeType, TytUserBuyGoods userBuyGoods, ExposureRecordInfo exposureRecordInfo);

    /**
     * 发放体验会员
     *
     * @param user
     */
    void giveExperienceMember(User user, Integer clientPort);

    /**
     * 发放车试用会员
     *
     * @param user
     */
    void giveTrialMember(User user);

    /**
     * @description 发放1+9发货次卡权益
     * <AUTHOR>
     * @date 2022/8/1 12:30
     * @param user
     * @return void
     */
    void giveGoodsCard(User user);

    /**
     * 赠送权益
     *
     * @param userId               用户id
     * @param goodsType            权益类型
     * @param permissionChangeType 权益赠送类型
     * @param buyPath              购买商品路径，无值传入 ""
     */
    void giveGoodsCard(Long userId,
                       GoodsType goodsType,
                       PermissionChangeType permissionChangeType,
                       String buyPath,PermissionGainTypeEnum permissionGainTypeEnum);

    void giveGoodsCard(Long userId, GoodsType goodsType, PermissionChangeType permissionChangeType,
                       String buyPath, PermissionGainTypeNewEnum permissionGainTypeEnum);

    /**
     * 发放7天10次曝光次卡
     * @param user
     */
    void giveExposureCard(User user);

    /**
     * 判断用户数是否是拨打电话会员权益
     *
     * @param userId
     * @return
     */
     boolean isVipCallPhonePermission(Long userId);

    /**
     * 判断用户数是否是发货会员权益
     * @param userId
     * @return
     */

    boolean isVipPublishPermission(Long userId);

    /**
     * 判断用户是否有发货次卡权益
     * @param userId
     * @return
     */
    boolean isPublishCardPermission(Long userId);


    UserPermission getUserPermission(Long userId, String typeId);

    UserPermissionBean getUserPermission(Long userId) throws Exception;

    UserPermissionBean getUserPermissionByClientSign(Long userId) throws Exception;

    /**
     * 发放微信分享赠送两次找货权益
     * @param userId
     */
    void giveShare2CarTimesPermission(Long userId, Long goodsId);

    /**
     * @description 是否给用户赠送权益
     * <AUTHOR>
     * @date 2022/2/17 10:51
     * @param user
     * @return boolean
     */
    boolean isGivePermission(User user);

    /**
     * @description 查询用户拥有的可用货源曝光权益总次数
     * @param userId userId
     * @return UserPermission
     */
    UserPermission getPermissionListByUserIdAndServicePermissionTypeId(Long userId);

    /**
     *  查看当前用户所拥有的会员及次卡相关信息
     * @param userId userId
     * @param vipId  车会员权益Id(100101) 货会员权益Id(100201)
     * @param numId  车拨打次数权益Id(100102) 货发货次数权益Id(100202)
     * @return
     */
    List<UserPermissionResponseBean> getUserOwnerPermission(Long userId,String numId,String vipId);

    /**
     * 查询货用户会员及次卡权益列表
     */
    List<UserPermissionResponseBean> getGoodsPermission(Long userId);

    /**
     * 判断用户是否购买过车会员
     * @param userId 用户Id
     * @return boolean
     */
    boolean judgeUserBuyGoods(Long userId);

    int getPublishGoodsPermissionCount(Long userId);

    List<UserPermissionGetBean> getUserPermissionByServiceId(Long userId, Integer port);

    List<UserPermission> getUserPermissionByServiceIdNew(Long userId, int i);

    void updatePublishPermissionTsId(Object usedRecordId, Long tsId);

    void compatibilityNewPermission(Long userId,
                                    Map<String, UserPermission> userPermissionMap,
                                    String oldPermissionType,
                                    String newPermissionType);
}
