package com.tyt.permission.dao.impl;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.UserPermission;
import com.tyt.permission.dao.UserPermissionDao;
import org.springframework.stereotype.Repository;


@Repository("userPermissionDao")
public class UserPermissionDaoImpl extends BaseDaoImpl<UserPermission, Long> implements UserPermissionDao {

	public UserPermissionDaoImpl() {
		this.setEntityClass(UserPermission.class);
	}
}
