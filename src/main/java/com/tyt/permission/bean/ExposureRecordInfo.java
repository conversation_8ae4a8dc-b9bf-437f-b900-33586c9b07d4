package com.tyt.permission.bean;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ExposureRecordInfo {

    /**
     * 获取类型(0.购买 1.新用户注册 2.沉默用户登录 3.流失用户登录 4.信用登录获取 5.信用完成履约任务发放 6.购买会员赠送 7 目前未知 8 活动获取 9 发布有价货源)
     */
    private Integer gainType;

    /**
     * 发放方式 0 系统自动方法 1 后台手动发放
     */
    private Integer sendType;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 备注
     */
    private String remark;
}
