package com.tyt.permission.bean;

import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.model.TytUserBuyGoods;

/**
 * @Description  分配用户权益mq消息
 * <AUTHOR>
 * @Date  2019/7/2 15:34
 * @Param
 * @return
 **/
public class MqUserPermissionMsg extends MqBaseMessageBean {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private Long goodsId;
    /**
     * 变更类型(1.购买 2.赠送 3.次卡用完 4.时间到期 )
     */
    private Integer changeType;
    /**
     * 商品订单信息
     */
    private TytUserBuyGoods userBuyGoods;
    /**
     * 获取方式：主要针对曝光次卡，其它权益默认传 100-赠送
     */
    private Integer gainType;

    private String ordNum;
    /**
     * 曝光卡相关信息
     */
    private ExposureRecordInfo exposureRecordInfo;
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public TytUserBuyGoods getUserBuyGoods() {
        return userBuyGoods;
    }

    public void setUserBuyGoods(TytUserBuyGoods userBuyGoods) {
        this.userBuyGoods = userBuyGoods;
    }

    public Integer getGainType() {
        return gainType;
    }

    public void setGainType(Integer gainType) {
        this.gainType = gainType;
    }

    public ExposureRecordInfo getExposureRecordInfo() {
        return exposureRecordInfo;
    }

    public String getOrdNum() {
        return ordNum;
    }

    public void setOrdNum(String ordNum) {
        this.ordNum = ordNum;
    }

    public void setExposureRecordInfo(ExposureRecordInfo exposureRecordInfo) {
        this.exposureRecordInfo = exposureRecordInfo;
    }
}
