package com.tyt.permission.bean;

import java.util.Arrays;
import java.util.Objects;

/**
 * Created by duanwc on 2019/7/2.
 */
public enum GoodsType {
    体验会员(9),
    车试用(10),
    车体验(37),
    货体验(12),
    货主权益赠送活动(100),
    分享赠送找货次卡(54),
    发货次卡赠送活动(103),
    一天找货权益(79),
    货源曝光权益7天10次(120),
    货源曝光权益7天5次(121),
    货源曝光权益7天1次(122),
    货源曝光权益7天30次(133),
    货源曝光权益7天3次(134),
    _1天100次拨打电话(153),
    注销赠送商品(167)
    ;

    ;




    GoodsType(long id) {
        this.id = id;
    }

    private long id;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static GoodsType findById(Long id){
        return Arrays.stream(values()).filter(it -> Objects.equals(id,it.id)).findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
