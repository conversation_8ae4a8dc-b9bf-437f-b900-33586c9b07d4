package com.tyt.permission.bean;

import com.tyt.noticePopup.enums.PopupTypeEnum;

/**
 * Created by duanwc on 2019/7/2.
 */
public class PermissionResult {
    /**
     * 是否可以使用该权益，可以使用为true，不可以使用为false，默认为true
     */
    private boolean use = true;

    /**
     * 提示枚举
     */
    private PopupTypeEnum popupTypeEnum;

    /**
     * 1 = 时间（会员），2 = 次卡
     */
    private Integer type;

    private Long usedRecordId;

    public boolean getUse() {
        return use;
    }

    public void setUse(boolean use) {
        this.use = use;
    }

    public PopupTypeEnum getPopupTypeEnum() {
        return popupTypeEnum;
    }

    public void setPopupTypeEnum(PopupTypeEnum popupTypeEnum) {
        this.popupTypeEnum = popupTypeEnum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getUsedRecordId() {
        return usedRecordId;
    }

    public void setUsedRecordId(Long usedRecordId) {
        this.usedRecordId = usedRecordId;
    }

    /**
     * 重置对象
     */
    public void reset(){
        this.use = true;
        this.popupTypeEnum = null;
        this.type = null;
    }

    @Override
    public String toString() {
        return "PermissionResult{" +
                "use=" + use +
                ", popupTypeEnum=" + popupTypeEnum +
                ", type=" + type +
                '}';
    }
}
