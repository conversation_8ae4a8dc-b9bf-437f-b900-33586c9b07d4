package com.tyt.permission.bean;

/**
 * Created by duanwc on 2019/7/2.
 */
public enum Permission {
    拨打货源电话(10010,0),
    货源发布(10020,0),
    查信用(10030,0),
    用户昵称显示(10040,0),
    货物内容显示(10050,0),
    货源拨打量显示(10060,0),
    货源回拨联系人(10070,0),
    新货提醒(10080,0),

    货源曝光权益(10090,0),

    有价货源发布(10020,1);


    //type 类型 默认为0，特殊情况特殊设置
    Permission(long id,int type) {
        this.id = id;
        this.type = type;
    }

    private long id;
    private int type;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
