package com.tyt.permission.controller;

import com.alibaba.fastjson.JSON;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.excellentgoodshomepage.service.ExcellentGoodsService;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.invoicetransport.service.InvoiceTransportService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.model.TytUserBuyGoods;
import com.tyt.model.UserPermission;
import com.tyt.permission.bean.GoodsPublishTypeEnum;
import com.tyt.permission.bean.UserPermissionBean;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.transport.querybean.CheckGoodCarPriceTransportBean;
import com.tyt.transport.service.BsPublishTransportService;
import com.tyt.transport.service.TransportMainService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.*;
import java.util.stream.Collectors;

import static com.tyt.user.enums.UserPermissionTypeEnum.*;

/**
 * @Description  用户权益接口
 * <AUTHOR>
 * @Date  2019/7/3 10:21
 * @Param
 * @return
 **/
@Controller
@RequestMapping("/plat/permission/userPermission/")
public class UserPermissionController extends BaseController {

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private UserBuyGoodsService userBuyGoodsService;

    @Autowired
    private BsPublishTransportService bsPublishTransportService;

    @Autowired
    private ExcellentGoodsService excellentGoodsService;

    @Autowired
    private InvoiceTransportService invoiceTransportService;

    @Autowired
    private TransportMainService transportMainService;

    /**
     * @Description  我的权益列表接口
     * <AUTHOR>
     * @Date  2019/7/3 10:31
     * @Param [baseParameter, request, response]
     * @return com.tyt.model.ResultMsgBean
     **/
    @RequestMapping(value = {"userPermissionList","userPermissionList.action"}, method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean userPermissionList(BaseParameter baseParameter,HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ResultMsgBean.OK, "操作成功!");
        try {
            //根据传入的参数，查询收银台商品列表
            Map<String,Object> map = new HashMap<String,Object>();
            //获取用户Id
            Long userId = baseParameter.getUserId();
            if(userId == null) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("用户Id不能为空！");
                return resultMsgBean;
            }
            //用户获取权益信息列表
            List<UserPermission> userPermissions = userPermissionService.getPermissionListByUserId(userId);
            userPermissions = BeanUtil.copyToList(userPermissions, UserPermission.class);
            //兼容新会员权益
            Map<String, UserPermission> userPermissionMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(userPermissions)) {
                userPermissionMap = userPermissions.stream().collect(Collectors.toMap(UserPermission::getServicePermissionTypeId, a -> a, (k1, k2) -> k1));
            }
            userPermissionService.compatibilityNewPermission(userId, userPermissionMap, CAR_NUM.getTypeId(), CAR_NUM_MEAL.getTypeId());
            userPermissionService.compatibilityNewPermission(userId, userPermissionMap, GOODS_NUM.getTypeId(), GOODS_NUM_NEW.getTypeId());
            userPermissions = new ArrayList<>(userPermissionMap.values());
            //查询用户购买商品记录(实际上是权益获取记录)
            //订单状态(0.待支付 1.支付失败 2.支付成功)
            List <TytUserBuyGoods> userBuyGoods = userBuyGoodsService.getUserBuyGoods(userId,2);
            //当前服务器时间
            map.put("currentTime", TimeUtil.getTimeStamp());
            map.put("userPermissions", userPermissions);
            map.put("userBuyGoods",userBuyGoods);
            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("查询我的权益列表成功！");
            resultMsgBean.setData(map);
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("查询我的权益列表发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return resultMsgBean;
    }

    /**
     * 我的页面 -- 权益查询
     * @param baseParameter
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "getUserPermission", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsgBean getUserPermission(BaseParameter baseParameter,HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean resultMsgBean = new ResultMsgBean();
        try {
            //获取用户Id和clientSign
            Long userId = baseParameter.getUserId();
            String clientSign = baseParameter.getClientSign();
            if(userId == null) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("用户Id不能为空！");
                return resultMsgBean;
            }
            if(StringUtils.isEmpty(clientSign)) {
                resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
                resultMsgBean.setMsg("ClientSign不能为空！");
                return resultMsgBean;
            }
            UserPermissionBean userPermission =new UserPermissionBean();
            //当前登录的为新的app和新的webPc返回信息发生变更
            if(String.valueOf(Constant.ClientSignEnum.ANDROID_GOODS.code).equals(clientSign)||
                    String.valueOf(Constant.ClientSignEnum.IOS_GOODS.code).equals(clientSign)||
                    String.valueOf(Constant.ClientSignEnum.IOS_CAR.code).equals(clientSign)||
                    String.valueOf(Constant.ClientSignEnum.ANDROID_CAR.code).equals(clientSign)||
                    String.valueOf(Constant.ClientSignEnum.WEB_GOODS.code).equals(clientSign)){
                //用户获取权益信息列表
                 userPermission = userPermissionService.getUserPermissionByClientSign(userId);
            }else{
                //用户获取权益信息列表
                 userPermission = userPermissionService.getUserPermission(userId);
            }
            resultMsgBean.setCode(ReturnCodeConstant.OK);
            resultMsgBean.setMsg("我的权益查询成功！");
            resultMsgBean.setData(userPermission);
        } catch (Exception e) {
            resultMsgBean.setCode(ResultMsgBean.ERROR);
            resultMsgBean.setMsg("服务器错误");
            logger.info("查询我的权益发生错误！"+e.getMessage());
            e.printStackTrace();
        }
        return resultMsgBean;
    }

    /**
     * 发货权益锚点
     * @param userId 用户id
     * @return rm
     */
    @RequestMapping(value = "/getGoodsPoint")
    @ResponseBody
    public ResultMsgBean getGoodsPoint(@RequestParam(name = "userId") Long userId,
                                       Long srcMsgId, // 为空发布货源；不为空编辑发布/再发一单
                                       String clientSign, String clientVersion) {
        try {
            //参数解析
            if (userId == null || userId.intValue() <= 0) {
                return ResultMsgBean.failResponse(ResponseEnum.request_error.info());
            }

            // 6530需求改动，编辑发布/再发一单按原来货源的类型，如果没有，默认普货
            if (srcMsgId != null) {
                TransportMain transportMain = transportMainService.getById(srcMsgId);
                if (transportMain != null) {
                    Integer excellentGoods = transportMain.getExcellentGoods();
                    if (excellentGoods == 1) { // 优车，包含1.0，2.0
                        // 先判断是否优车2.0
                        TransportLabelJson transportLabelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
                        if (transportLabelJson != null && Objects.equals(transportLabelJson.getGoodCarPriceTransport(), 1)) {
                            //非优车一口价货源判断是否符合优车定价货源条件，如果符合则自动转优车定价
                            CheckGoodCarPriceTransportBean priceTransportBean = new CheckGoodCarPriceTransportBean(
                                    transportMain.getStartProvinc(), transportMain.getStartCity(), transportMain.getStartArea(),
                                    transportMain.getDestProvinc(), transportMain.getDestCity(), transportMain.getDestArea(),
                                    transportMain.getTaskContent(), transportMain.getWeight(),
                                    transportMain.getLength(), transportMain.getWide(), transportMain.getHigh(),
                                    transportMain.getDistance() == null ? null : transportMain.getDistance(),
                                    transportMain.getExcellentGoods(), transportMain.getUserId(),
                                    transportMain.getPublishType(), transportMain.getPrice(), transportMain.getGoodTypeName());
                            if (bsPublishTransportService.checkTransportIsGoodCarPriceTransport(priceTransportBean)) {
                                return ResultMsgBean.successResponse(GoodsPublishTypeEnum.EXCELLENT_GOODS_2.getCode());
                            }
                        } else { // 优车1.0
                            // 是否有签约有优车发货权益
                            boolean excellent = bsPublishTransportService.checkExcellentGoodsForPermission(userId);
                            if (excellent && userPermissionService.isVipPublishPermission(userId)) {
                                return ResultMsgBean.successResponse(GoodsPublishTypeEnum.EXCELLENT_GOODS.getCode());
                            }
                            // 判断是否有优车发货卡
                            int count = excellentGoodsService.getAllCanUseCardCountNumByUserId(userId);
                            if (count > 0) {
                                return ResultMsgBean.successResponse(GoodsPublishTypeEnum.EXCELLENT_GOODS.getCode());
                            }
                        }
                    } else if (excellentGoods == 2) { // 专车货源，直接返回
                        return ResultMsgBean.successResponse(GoodsPublishTypeEnum.SPECIAL_GOODS.getCode());
                    } else { // 普货直接返回
                        return ResultMsgBean.successResponse(GoodsPublishTypeEnum.NORMAL_GOODS.getCode());
                    }
                }
                return ResultMsgBean.successResponse(GoodsPublishTypeEnum.NORMAL_GOODS.getCode());
            }

            //是否有签约有优车发货权益
            boolean excellent = bsPublishTransportService.checkExcellentGoodsForPermission(userId);
            if (excellent && userPermissionService.isVipPublishPermission(userId)) {
                return ResultMsgBean.successResponse(GoodsPublishTypeEnum.EXCELLENT_GOODS.getCode());
            }
            //判断是否有优车发货卡
            int count = excellentGoodsService.getAllCanUseCardCountNumByUserId(userId);
            if (count > 0) {
                return ResultMsgBean.successResponse(GoodsPublishTypeEnum.EXCELLENT_GOODS.getCode());
            }
            //判断用户是否有普通发货权益
            int permissionCount = userPermissionService.getPublishGoodsPermissionCount(userId);
            if (permissionCount > 0) {
                return ResultMsgBean.successResponse(GoodsPublishTypeEnum.NORMAL_GOODS.getCode());
            }

            if (String.valueOf(Constant.ClientSignEnum.IOS_GOODS.code).equals(clientSign) && "6440".equals(clientVersion)){
                return ResultMsgBean.successResponse(GoodsPublishTypeEnum.NORMAL_GOODS.getCode());
            }

            return ResultMsgBean.successResponse(GoodsPublishTypeEnum.NONE.getCode());

        } catch (Exception e) {
            logger.error("获取发货锚点失败:", e);
            return ResultMsgBean.failResponse(ResponseEnum.sys_error.info());
        }
    }


    /**
     * 编辑发布判断用户是否具备开票权限
     * @param userId userId
     * @param invoiceSubjectId 开票平台主体ID
     * @return 200
     */
    @RequestMapping(value = "/checkUserCanPublishInvoiceTransportInDoPublish")
    @ResponseBody
    public ResultMsgBean checkUserCanPublishInvoiceTransportInDoPublish(Long userId, Long invoiceSubjectId) {
        return invoiceTransportService.checkUserCanPublishInvoiceTransportInDoPublish(userId, invoiceSubjectId);
    }

    /**
     * 编辑发布判断用户是否具备开票权限以及校验发货参数
     * @param userId userId
     * @param distance distance
     * @param price price
     * @param weight weight
     * @param invoiceSubjectId 开票平台主体ID
     * @return 200
     */
    @RequestMapping(value = "/checkUserCanPublishInvoiceTransportInDoPublishAndCheckParam")
    @ResponseBody
    public ResultMsgBean checkUserCanPublishInvoiceTransportInDoPublishAndCheckParam(Long userId, String distance, String price, String weight, String length, String width, String height, Long invoiceSubjectId) {
        //开票货源长宽高重量限制校验
        ResultMsgBean resultMsgBean = invoiceTransportService.checkInvoiceTransportMeasure(userId, length, width, height, weight);
        if (resultMsgBean.getCode() != 200) {
            return resultMsgBean;
        }
        //开票货源货主资格校验
        resultMsgBean = invoiceTransportService.checkUserCanPublishInvoiceTransportInDoPublish(userId, invoiceSubjectId);
        if (resultMsgBean.getCode() != 200) {
            return resultMsgBean;
        }
        //开票货源限制校验
        resultMsgBean = invoiceTransportService.checkInvoiceTransportParam(userId, distance, price, weight);
        if (resultMsgBean.getCode() != 200) {
            return resultMsgBean;
        }
        return ResultMsgBean.successResponse();
    }

    /**
     * 直接发布判断用户是否具备开票权限
     * @param userId userId
     * @return 200
     */
    @RequestMapping(value = "/checkUserCanPublishInvoiceTransportInDoSaveDirectAndCheckParam")
    @ResponseBody
    public ResultMsgBean checkUserCanPublishInvoiceTransportInDoSaveDirect(Long userId, String distance, String price, String weight, String length, String width, String height, Long invoiceSubjectId) {
        //开票货源长宽高重量限制校验
        ResultMsgBean resultMsgBean = invoiceTransportService.checkInvoiceTransportMeasure(userId, length, width, height, weight);
        if (resultMsgBean.getCode() != 200) {
            return resultMsgBean;
        }
        resultMsgBean = invoiceTransportService.checkUserCanPublishInvoiceTransportInDoSaveDirect(userId, invoiceSubjectId);
        if (resultMsgBean.getCode() != 200) {
            return resultMsgBean;
        }
        //开票货源限制校验
        resultMsgBean = invoiceTransportService.checkInvoiceTransportParam(userId, distance, price, weight);
        if (resultMsgBean.getCode() != 200) {
            return resultMsgBean;
        }
        return ResultMsgBean.successResponse();
    }

    /**
     * 根据货主ID和司机运费获取费率并计算附加运费
     * @param userId transportUserId
     * @param price price
     * @param invoiceSubjectId 开票平台主体ID
     * @return 200
     */
    @RequestMapping(value = "/getAdditionalPriceAndEnterpriseTaxRate")
    @ResponseBody
    public ResultMsgBean getAdditionalPriceAndEnterpriseTaxRate(Long userId, String price, Long invoiceSubjectId) {
        return invoiceTransportService.getAdditionalPriceAndEnterpriseTaxRate(userId, price, invoiceSubjectId);
    }

}
