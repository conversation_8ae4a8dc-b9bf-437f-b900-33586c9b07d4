package com.tyt.successaccount.service;


import com.tyt.base.service.BaseService;
import com.tyt.model.MembersPayRecord;
import com.tyt.model.User;

public interface MembersPayRecordService extends BaseService<MembersPayRecord, Long>{
	/**
	 * 缴费完成后插入会员缴费记录表 (操作人是当前登录用户)
	 * @param user  缴费用户
	 * @param accountId  缴费表id
	 * @param changeType 变更类型1.平台缴费变更 2.后台缴费变更  3.异常扣减变更  4.vip延期变更  5.活动加赠变更  6.其他加赠变更 7.后台确认付款变更（线上订单查询--确认付款）
	 * @param extralDays 变更天数，赠送时使用(非赠送操作赋值null)
	 * @param note 变更备注，(非赠送操作赋值null)
	 */
	public void saveMembersPayRecord(User user,Long accountId,Integer changeType,Integer extralDays,String note);
	/**
	 * 根据用户id获取用户流水表记录
	 * @param userId
	 * @return
	 */
	public MembersPayRecord getListByUserId(Long userId);
}
