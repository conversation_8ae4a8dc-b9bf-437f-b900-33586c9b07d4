package com.tyt.successaccount.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.MembersPayRecord;
import com.tyt.model.User;
import com.tyt.successaccount.service.MembersPayRecordService;
import com.tyt.util.TimeUtil;


@Service("membersPayRecordService")
public class MembersPayRecordServiceImpl extends BaseServiceImpl<MembersPayRecord,Long> implements MembersPayRecordService{

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name="membersPayRecordDao")
	public void setBaseDao(BaseDao<MembersPayRecord, Long> membersPayRecordDao) {
	        super.setBaseDao(membersPayRecordDao);
	}
	@Override
	public void saveMembersPayRecord(User user, Long accountId,Integer changeType,Integer extralDays,String note) {
		try {
			MembersPayRecord record =new MembersPayRecord();
			//用户iD
			record.setUserId(user.getId());
			//变更类型
			record.setChangeType(changeType);
			//变更备注
			record.setChangeRemark(note);
			//变更业务id,对应缴费表id
			if(accountId!=null && accountId.intValue()>0) {
				record.setChangeAccountId(accountId);
			}
			//缴费次数
			record.setPayNumber(user.getPayNumber());
			//操作人 （当前登录用户）
			record.setOperatorId(user.getId());
			record.setOperatorName(user.getUserName());
			//
			MembersPayRecord lastRecord = this.getListByUserId(user.getId());
			if(lastRecord!=null) {
				//如果记录表有数据，取最新一条的变更后开始和结束时间
				record.setBeforeMembersStartDate(lastRecord.getAfterMembersStartDate());
				record.setBeforeMembersEndDate(lastRecord.getAfterMembersEndDate());
				//此处为还在有效期内操作
				if(lastRecord.getAfterMembersEndDate().getTime()>=new Date().getTime()) {
					//此处为赠送或减天数操作
					if(extralDays!=null) {
						if(extralDays<0) {
							//如果是减天数操作（变更后开始：变更前开始   。变更后结束：变更前结束-天数）
							record.setAfterMembersEndDate(TimeUtil.addDay(record.getBeforeMembersEndDate(), extralDays));
							record.setAfterMembersStartDate(TimeUtil.addDay(record.getAfterMembersEndDate(),extralDays));
						}else if(extralDays>0){
							//如果是赠送天数操作（变更后开始：变更前结束  。变更后结束：变更后开始+天数）
							record.setAfterMembersStartDate(lastRecord.getAfterMembersEndDate());
							record.setAfterMembersEndDate(TimeUtil.addDay(record.getAfterMembersStartDate(), extralDays));
						}
					}else {
						//此为有效期内正常缴费操作
						//如果变更前结束日期>=今天  （变更后开始：变更前结束   。变更后结束：变更后开始+变更天数）
						record.setAfterMembersStartDate(lastRecord.getAfterMembersEndDate());
						record.setAfterMembersEndDate(TimeUtil.dateAddYear(record.getAfterMembersStartDate(), user.getRenewalYears()));
					}
					//用户变更状态：1VIP有效、2VIP到期、3试用
					record.setBeforeChangeStatus(1);
				}else {
					//此为已不再有效期内操作
					if(extralDays!=null) {//赠送
						record.setAfterMembersStartDate(new Date());
						record.setAfterMembersEndDate(TimeUtil.addDay(new Date(), extralDays));
					}else {
						//此为不在有效期内正常缴费操作
						//如果变更前结束日期<今天  （变更后开始：今天   。变更后结束：今天+变更天数）
						record.setAfterMembersStartDate(new Date());
						record.setAfterMembersEndDate(TimeUtil.dateAddYear(new Date(), user.getRenewalYears()));
					}
					record.setBeforeChangeStatus(2);
				}
			}else {
				//以下为没有记录用户操作
				if(extralDays!=null) {
					record.setAfterMembersStartDate(new Date());
					record.setAfterMembersEndDate(TimeUtil.addDay(new Date(), extralDays));
				}else {
					//如果记录表没有记录，新增一条
					record.setAfterMembersStartDate(new Date());
					record.setAfterMembersEndDate(TimeUtil.dateAddYear(new Date(), user.getRenewalYears()));
				}
				record.setBeforeChangeStatus(3);
			}
			//如果变更后的结束日期>今天  变更后状态为 1 有效  否则为2 到期
			if(record.getAfterMembersEndDate().getTime()>new Date().getTime()) {
				record.setAfterChangeStatus(1);
			}else {
				record.setAfterChangeStatus(2);
			}
			//变更天数
			if(extralDays!=null) {
				record.setChangeDays(extralDays);
			}else {
				record.setChangeDays(TimeUtil.daysBetween(record.getAfterMembersStartDate(), record.getAfterMembersEndDate()));
			}

			this.add(record);
		} catch (Exception e) {
			logger.error("异常信息:", e);
		}
	}

	@Override
	public MembersPayRecord getListByUserId(Long userId) {
		String sql="select * from members_pay_record r where r.user_id=? ORDER BY r.id desc, r.ctime DESC ";
		List<MembersPayRecord> list = this.getBaseDao().queryForList(sql, new Object[] {userId});
		if(list!=null && list.size()>0) {
			return list.get(0);
		}else {
			return null;
		}
	}
}
