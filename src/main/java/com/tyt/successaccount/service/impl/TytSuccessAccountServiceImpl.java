package com.tyt.successaccount.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.model.TytSuccessAccount;
import com.tyt.successaccount.service.TytSuccessAccountService;

@Service("successAccountService")
public class TytSuccessAccountServiceImpl extends BaseServiceImpl<TytSuccessAccount, Long> implements TytSuccessAccountService {

	@Resource(name = "successAccountDao")
	public void setBaseDao(BaseDao<TytSuccessAccount, Long> successAccountDao) {
		super.setBaseDao(successAccountDao);
	}

	@Override
	public void updateEndTime(Integer payNumber, Long userId, String endTime) {
		String sql=new String("update tyt_success_account set end_date=?"
				+ " where user_id=? and number=?");
		List<Object> params=new ArrayList<Object>();
		params.add(endTime);
		params.add(userId);
		params.add(payNumber);
		//执行数据库更新
		this.executeUpdateSql(sql, params.toArray());
	}
}
