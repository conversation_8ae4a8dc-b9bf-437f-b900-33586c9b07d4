package com.tyt.successaccount.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.MembersPayRecord;
import com.tyt.successaccount.dao.MembersPayRecordDao;
@Repository("membersPayRecordDao")
public class MembersPayRecordDaoImpl extends BaseDaoImpl<MembersPayRecord,Long> implements MembersPayRecordDao  {
	public MembersPayRecordDaoImpl(){
		   this.setEntityClass(MembersPayRecord.class);
	   }
}
