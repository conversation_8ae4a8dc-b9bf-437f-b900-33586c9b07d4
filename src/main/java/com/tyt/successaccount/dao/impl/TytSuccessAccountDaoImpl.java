package com.tyt.successaccount.dao.impl;

import org.springframework.stereotype.Repository;

import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.TytSuccessAccount;
import com.tyt.successaccount.dao.TytSuccessAccountDao;

@Repository("successAccountDao")
public class TytSuccessAccountDaoImpl extends BaseDaoImpl<TytSuccessAccount, Long> implements TytSuccessAccountDao {

	public TytSuccessAccountDaoImpl() {
		this.setEntityClass(TytSuccessAccount.class);
	}
}
