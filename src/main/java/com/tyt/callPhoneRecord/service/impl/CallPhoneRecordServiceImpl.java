package com.tyt.callPhoneRecord.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.callPhoneRecord.bean.CallPhoneRecordVo;
import com.tyt.callPhoneRecord.bean.RecentCallListBean;
import com.tyt.callPhoneRecord.enums.RatingTypeEnum;
import com.tyt.callPhoneRecord.service.CallPhoneRecordService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.ASRTaskMsg;
import com.tyt.infofee.bean.CreditUserInfo;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.infofee.service.TytTransportDispatchViewService;
import com.tyt.model.*;
import com.tyt.mybatis.mapper.InfofeeDetailMapper;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.biz.feedback.pojo.UserFeedbackRatingAndLabelDTO;
import com.tyt.plat.biz.feedback.service.IFeedbackUserService;
import com.tyt.plat.client.trade.infofee.ApiTradeInfoFeeClient;
import com.tyt.plat.entity.base.CallPhoneRecordDO;
import com.tyt.plat.mapper.base.CallPhoneRecordMapper;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.IdCardUtil;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import com.tytrecommend.recommend.service.GoodTypeCntService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.Hibernate;
import org.hibernate.type.Type;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service("callPhoneRecordService")
@Slf4j
public class CallPhoneRecordServiceImpl extends BaseServiceImpl<CallPhoneRecord, Long> implements CallPhoneRecordService {

    @Resource(name = "userPermissionService")
    private UserPermissionService userPermissionService;

    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

//    @Resource(name = "usercreditinfoservice")
//    private UserCreditInfoService userCreditInfoService;

    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "transportOrdersService")
    private TransportOrdersService transportOrdersService;
    @Autowired
    private InfofeeDetailMapper infofeeDetailMapper;

    @Resource(name = "callPhoneRecordDao")
    public void setBaseDao(BaseDao<CallPhoneRecord, Long> callPhoneRecordDao) {
        super.setBaseDao(callPhoneRecordDao);
    }

    @Autowired
    private TytTransportDispatchViewService tytTransportDispatchViewService;
    @Autowired
    private TransportMainService transportMainService;
    @Autowired
    private ApiTradeInfoFeeClient apiTradeInfoFeeClient;
    @Resource(name = "threadPoolExecutor")
    private ThreadPoolExecutor threadPoolExecutor;
    @Autowired
    private CallPhoneRecordMapper callPhoneRecordMapper;
    @Resource(name = "goodTypeCntService")
    private GoodTypeCntService goodTypeCntService;
    @Autowired
    private IFeedbackUserService feedbackUserService;

    @Autowired
    private TytConfigService tytConfigService;

    @Resource(name = "tytMqMessageService")
    private TytMqMessageService tytMqMessageService;

    private static final String TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY = "transportCallPhoneRecoredCount";

    @Override
    public void saveRecord(CallPhoneRecord record) throws Exception {
        boolean isCarVip = userPermissionService.isVipCallPhonePermission(record.getCarUserId());
        User user = userService.getByUserId(record.getCarUserId());
        if (isCarVip) {
            record.setCarIsVip(1);
        } else {
            record.setCarIsVip(2);
        }
        record.setCarUserName(user.getTrueName() == null ? user.getUserName() : user.getTrueName());
        record.setCreateTime(new Date());
        this.add(record);

        //添加未查看的通话记录缓存
        if (record.getSrcMsgId() != null) {
            TransportMain bySrcMsgId = transportMainService.getBySrcMsgId(record.getSrcMsgId());
            if (bySrcMsgId != null) {
                recordCarCallPhone(bySrcMsgId, record.getCarUserId());
            }
        }

        try{
            log.info("联系货主抽大奖-拨打电话userId:{}",record.getCarUserId());
            List<RewardTaskVO> rewardTaskVOByType = getRewardTaskVOByType(record.getCarUserId(), 3);
            if(CollUtil.isNotEmpty(rewardTaskVOByType)){
                RewardTaskVO rewardTaskVO = rewardTaskVOByType.get(0);
                if(rewardTaskVO.getTaskStatus() != 2){
                    rewardTaskVO.setButtonName(6);
                    updateRewardTaskStatus(rewardTaskVO);
                }
            }
        }catch (Exception e){
            log.info("联系货主抽大奖-拨打电话异常",e);

        }
    }

    /**
     * Records a car call phone for a given transport.
     *
     * @param transportMain the transport object containing the necessary information
     */
    private void recordCarCallPhone(TransportMain transportMain, Long carUserId) {
        long secondsUntilTomorrow = Duration.between(java.time.LocalDateTime.now(), java.time.LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).plusDays(1)).get(ChronoUnit.SECONDS);
        if (RedisUtil.exists(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId())) {
            if (!RedisUtil.mapExists(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString())) {
                RedisUtil.mapPut(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), carUserId.toString());
                RedisUtil.expire(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), Integer.parseInt(String.valueOf(secondsUntilTomorrow)));
            } else {
                String mapValue = RedisUtil.getMapValue(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString());
                mapValue += ("," + carUserId.toString());
                RedisUtil.mapPut(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), mapValue);
                RedisUtil.expire(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), Integer.parseInt(String.valueOf(secondsUntilTomorrow)));
            }
        } else {
            RedisUtil.setMap(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), carUserId.toString(), Integer.parseInt(String.valueOf(secondsUntilTomorrow)));
        }
    }

    @Override
    public List<RewardTaskVO> getRewardTaskVOByType(Long userId, Integer taskType){
        String sql = "select id,task_status as taskStatus,button_name as buttonName from tyt_reward_task where user_id = ? and create_time > ? and create_time < ? and task_type = ?";
        Object[] allParams = new Object[] {userId, TimeUtil.today(),TimeUtil.getEndOfToday(),taskType};
        Map<String, Type> allScalarMap = new HashMap<>();
        allScalarMap.put("id", Hibernate.LONG);
        allScalarMap.put("taskStatus", Hibernate.INTEGER);
        allScalarMap.put("buttonName", Hibernate.INTEGER);
        return this.getBaseDao().search(sql, allScalarMap, RewardTaskVO.class, allParams);
    }

    @Override
    public CallPhoneRecordDO getLatestCallRecord(List<Long> srcMsgIds) {
        return callPhoneRecordMapper.getLatestCallRecord(srcMsgIds);
    }

    @Override
    public void updateRewardTaskStatus(RewardTaskVO rewardTaskVO){

        String sql="UPDATE tyt_reward_task SET task_status=?,button_name=?,modify_time=now() where id=?";
        this.getBaseDao().executeUpdateSql(sql, new Object[]{rewardTaskVO.getTaskStatus(),rewardTaskVO.getButtonName(),rewardTaskVO.getId()});
    }

    @Override
    public void addContactRecord(Long srcMsgId, Long userId) {
        //往查看记录表中记录数据，展示查看次数，只记录调度发货的
        threadPoolExecutor.execute(() -> {
            try {
//            TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
//            if (transportMain != null && transportMain.getSourceType() == 2) {
                User user = userService.getByUserId(userId);
                //type 1：查看 2：联系
                tytTransportDispatchViewService.addTransportView(user, srcMsgId, 2);
//            }
            } catch (Exception e) {
                log.error("保存查看详情次数出错，carUserId:{},srcMsgId:{}", userId, srcMsgId, e);
            }
        });
    }

    @Override
    public long countByCarUserIdAndTimeBetween(Long carUserId, Date startTime, Date endTime) {
        String sql = "SELECT count(*) FROM `call_phone_record` WHERE car_user_id=? and create_time>? and create_time<?";
        BigInteger count = getBaseDao().query(sql, new Object[]{carUserId, startTime, endTime});
        return count == null ? 0 : count.longValue();
    }

    /**
     * 查询车主最近一年成交货物类型数量
     * @param carUserId
     * @param goodTypeName
     * @return
     */
    public Integer getGoodsTypeNum(Long carUserId, String goodTypeName) {
        Integer num = 0;
        try {
            if (Objects.isNull(carUserId) || StringUtils.isBlank(goodTypeName)) {
                log.info("getGoodsTypeNum 参数为空，carUserId:{}, goodTypeName:{}", carUserId, goodTypeName);
                return num;
            }
            Integer goodsTypeCnt = goodTypeCntService.getGoodsTypeCnt(carUserId, goodTypeName);
            log.info("getGoodsTypeNum查询bi同步表，结果；{}, carUserId:{}, goodTypeName:{}", goodsTypeCnt, carUserId, goodTypeName);
            if (Objects.nonNull(goodsTypeCnt)) {
                num = goodsTypeCnt;
            }
        } catch (Exception e) {
            log.error("getGoodsTypeNum异常，carUserId：{}, goodTypeName:{}", carUserId, goodTypeName, e);
        }
        return num;
    }

    @Override
    public List<CallPhoneRecordVo> contactedList(Long userId, Long tsId) throws Exception {
        List<CallPhoneRecord> callPhoneRecords = getCallPhoneRecords(tsId);
        TransportMain transportMain = transportMainService.getTransportMainForId(tsId);
        //CommonUtil.loopSleep(1000, 4);
        List<CallPhoneRecordVo> CallPhoneRecordVos = this.increaseVo(callPhoneRecords);
        String tytServerPictureUrl = tytConfigService.getStringValue("tyt_server_picture_url_old", "http://www.teyuntong.com/rootdata");
        for (CallPhoneRecordVo callPhoneRecord : CallPhoneRecordVos) {
            callPhoneRecord.setGoodsTypeName(transportMain.getGoodTypeName());
            callPhoneRecord.setGoodsTypeNum(getGoodsTypeNum(callPhoneRecord.getCarUserId(), transportMain.getGoodTypeName()));
            User user = userService.getByUserId(callPhoneRecord.getCarUserId());
            List<CreditUserInfo> creditUserInfos = infofeeDetailMapper.queryCreditUserInfo("(" + callPhoneRecord.getCarUserId().toString() + ")", userId.toString());
            if (CollectionUtils.isNotEmpty(creditUserInfos)) {
                CreditUserInfo creditUserInfo = creditUserInfos.get(0);
                callPhoneRecord.setCoopNums(creditUserInfo.getCoopNums());
                callPhoneRecord.setTradeNums(creditUserInfo.getTradeNums());
            }
            try {
                if (StringUtils.isNotBlank(user.getIdCard())) {
                    String showName = callPhoneRecord.getCarUserName().substring(0, 1) + IdCardUtil.getCallGender(user.getIdCard());
                    callPhoneRecord.setCarUserName(showName);
                } else {
                    callPhoneRecord.setCarUserName(callPhoneRecord.getCarUserName());
                }
            } catch (Exception e) {
                log.error("contactedList setCarUserName error", e);
            }


            //返回拼好的头像
            if (StringUtils.isNotBlank(user.getHeadUrl())) {
                String pattern = "^(http|https)";
                Pattern compiledPattern = Pattern.compile(pattern);
                Matcher matcher = compiledPattern.matcher(user.getHeadUrl());
                if (!matcher.find()) {
                    callPhoneRecord.setHeadUrl(tytServerPictureUrl + user.getHeadUrl());
                } else {
                    callPhoneRecord.setHeadUrl(user.getHeadUrl());
                }
            }

            //返回是否实名认证
            if (user.getVerifyPhotoSign() != null && user.getVerifyPhotoSign() == 1) {
                callPhoneRecord.setRealNameAuthentication(true);
            } else {
                callPhoneRecord.setRealNameAuthentication(false);
            }

            // ApiDataUserCreditInfoTwo apiDataUserCreditInfoTwo = apiDataUserCreditInfoService.getById(callPhoneRecord.getCarUserId());
            // if (apiDataUserCreditInfoTwo != null) {
            //     callPhoneRecord.setCarCreditRankLevel(apiDataUserCreditInfoTwo.getCarCreditRankLevel());
            // }
            //查询车方的好评率和标签
            UserFeedbackRatingAndLabelDTO userFeedbackRatingAndLabel = feedbackUserService.getUserFeedbackRatingAndLabel(callPhoneRecord.getCarUserId(), 1);
            if(userFeedbackRatingAndLabel != null){
                callPhoneRecord.setRating(userFeedbackRatingAndLabel.getRating());
                callPhoneRecord.setPositiveCount(userFeedbackRatingAndLabel.getPositiveCount());
                callPhoneRecord.setPositiveLabels(userFeedbackRatingAndLabel.getPositiveLabels());
                callPhoneRecord.setNegativeLabels(userFeedbackRatingAndLabel.getNegativeLabels());
                // 30天保护期
                if (user != null && DateUtils.addDays(user.getCtime() == null ? new Date() : user.getCtime(), 30).before(new Date())) {
                    //总评价量
                    Long total = userFeedbackRatingAndLabel.getTotal();
                    //好评数
                    Long positiveCount = userFeedbackRatingAndLabel.getPositiveCount();
                    if (total == 0) {
                        callPhoneRecord.setRatingType(RatingTypeEnum.RECENT_NO_RATE.getCode());
                    } else if (total > 0 && total < 3) {
                        //有好评
                        if(positiveCount > 0){
                            callPhoneRecord.setRatingType(RatingTypeEnum.RECENT_RECEIVE_POSITIVE.getCode());
                        } else { //无好评
                            callPhoneRecord.setRatingType(RatingTypeEnum.RECENT_NOT_RECEIVE_POSITIVE.getCode());
                        }
                    } else if (total >= 3) {
                        callPhoneRecord.setRatingType(RatingTypeEnum.SHOW_RATING.getCode());
                    }
                } else {
                    callPhoneRecord.setRating(null);
                    callPhoneRecord.setRatingType(RatingTypeEnum.NOT_SHOW_RATING.getCode());
                    callPhoneRecord.setNegativeLabels(Collections.emptyList());
                }

            }

            //返回备注字段
            if (callPhoneRecord.getSrcMsgId() != null && callPhoneRecord.getCarUserId() != null) {
                String callPhoneRecordsRemark = getCallPhoneRecordsRemark(callPhoneRecord.getSrcMsgId(), callPhoneRecord.getCarUserId());
                if (StringUtils.isNotBlank(callPhoneRecordsRemark)) {
                    callPhoneRecord.setRemark(callPhoneRecordsRemark);
                }
            }

        }

        //获取意向车源数据后把未查看的数量去掉
        //货主查看意向车源，就需要把未查看的数量去掉
        if (RedisUtil.exists(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + userId)
                && RedisUtil.mapExists(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + userId, tsId.toString())) {
            //将该货源的货主未浏览通话记录次数清空
            RedisUtil.mapRemove(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + userId, tsId.toString());
        }

        return CallPhoneRecordVos;
    }

    private List<CallPhoneRecordVo> increaseVo(List<CallPhoneRecord> callPhoneRecords) {
        List<CallPhoneRecordVo> callPhoneRecordVos = new ArrayList<>();

        for (CallPhoneRecord callPhoneRecord : callPhoneRecords) {
            CallPhoneRecordVo callPhoneRecordVo = new CallPhoneRecordVo();
            BeanUtils.copyProperties(callPhoneRecord, callPhoneRecordVo);
            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(callPhoneRecord.getCarUserId());
            if (userCreditInfo != null) {
                if (null != userCreditInfo.getCarTotalServerScore()) {
                    callPhoneRecordVo.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
                }
                if (null != userCreditInfo.getCarServerRankScore()) {
                    callPhoneRecordVo.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
                }
                callPhoneRecordVo.setCarCreditRankLevel(userCreditInfo.getCarCreditRankLevel());
            }
            callPhoneRecordVos.add(callPhoneRecordVo);
        }
        return callPhoneRecordVos;
    }

    private List<CallPhoneRecord> getCallPhoneRecords(Long tsId) {
        String sql = "SELECT * FROM (SELECT * FROM `call_phone_record` WHERE src_msg_id = ? ORDER BY id DESC) AS a GROUP BY car_user_id";
        final Object[] params = {
                tsId
        };
        return this.getBaseDao().queryForList(sql, params);
    }

    /**
     * Retrieves the remark from the call_phone_record_remark table based on the given source message ID and car user ID.
     *
     * @param srcMsgId  the source message ID
     * @param carUserId the car user ID
     * @return the remark associated with the provided source message ID and car user ID, or null if no remark is found
     */
    private String getCallPhoneRecordsRemark(Long srcMsgId, Long carUserId) {
        String sql = "select remark from call_phone_record_remark where src_msg_id = ? and car_user_id = ? limit 1";
        final Object[] params = {
                srcMsgId, carUserId
        };
        return this.getBaseDao().query(sql, params);
    }

    @Override
    public List<RecentCallListBean> getRecentCallList(Long tsId) throws Exception {
        List<RecentCallListBean> callListBeans = new ArrayList<>();
        List<RecentCallListBean> listBeans = transportOrdersService.getInfofeeCell(tsId);
        List<CallPhoneRecord> records = getCallPhoneRecords(tsId);
        Set<Long> userIds = new HashSet<>();
        if (listBeans != null && listBeans.size() > 0) {
            callListBeans.addAll(listBeans);
            for (RecentCallListBean listBean : listBeans) {
                userIds.add(listBean.getCarryUserId());
            }
        }
        if (records != null && records.size() > 0) {
            for (CallPhoneRecord record : records) {
                if (!userIds.isEmpty() && userIds.contains(record.getCarUserId())) {
                    break;
                }
                User user = userService.getByUserId(record.getCarUserId());
                RecentCallListBean bean = new RecentCallListBean();
                bean.setCarryUserId(record.getCarUserId());
                bean.setCarryName(user.getTrueName());
                bean.setCarryCellPhone(user.getCellPhone());
                bean.setCallTime(record.getCreateTime());
                callListBeans.add(bean);
            }
        }

        //V V6220版本新增
        for (RecentCallListBean callListBean : callListBeans) {
            ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(callListBean.getCarryUserId());
            if (userCreditInfo != null) {
                if (null != userCreditInfo.getCarTotalServerScore()) {
                    callListBean.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
                }
                if (null != userCreditInfo.getCarServerRankScore()) {
                    callListBean.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
                }
            }
        }

        return callListBeans;
    }

    @Override
    public void asrCreate(Long srcMsgId) {
        ASRTaskMsg asrTaskMsg = tytMqMessageService.addASRTaskdMqMessageDistribute(null, srcMsgId, null);
        tytMqMessageService.sendASRTaskdMqMessageDistribute(asrTaskMsg);
//        ASRTaskMsg asrTaskMsg = new ASRTaskMsg();
//        asrTaskMsg.setSrcMsgId(srcMsgId);
//        String jsonString = JSONObject.toJSONString(asrTaskMsg);
//        tytMqMessageService.sendMsgCustom(jsonString, "INFRA_ASR_TOPIC", SerialNumUtil.generateSeriaNum(), "ASR_DISTRIBUTE", 0L);
    }

}
