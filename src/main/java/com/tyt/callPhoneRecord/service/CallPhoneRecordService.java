package com.tyt.callPhoneRecord.service;

import com.tyt.base.service.BaseService;
import com.tyt.callPhoneRecord.bean.CallPhoneRecordVo;
import com.tyt.callPhoneRecord.bean.RecentCallListBean;
import com.tyt.model.CallPhoneRecord;
import com.tyt.model.RewardTaskVO;
import com.tyt.plat.entity.base.CallPhoneRecordDO;

import java.util.Date;
import java.util.List;

public interface CallPhoneRecordService extends BaseService<CallPhoneRecord, Long> {
    /**
     * 保存拨打电话记录
     *
     * @param record
     */
    void saveRecord(CallPhoneRecord record) throws Exception;

    /**
     * 获取回拨人列表
     * @param userId
     * @param tsId
     * @return
     */
    List<CallPhoneRecordVo> contactedList(Long userId, Long tsId) throws Exception;

    List<RecentCallListBean> getRecentCallList(Long tsId) throws Exception;

    void addContactRecord(Long srcMsgId, Long userId);

    Integer getGoodsTypeNum(Long carId, String goodTypeName);

    long countByCarUserIdAndTimeBetween(Long userId, Date startTime, Date endTime);

    void asrCreate(Long srcMsgId);

    void updateRewardTaskStatus(RewardTaskVO rewardTaskVO);

    List<RewardTaskVO> getRewardTaskVOByType(Long userId, Integer taskType);

    CallPhoneRecordDO getLatestCallRecord(List<Long> srcMsgIds);
}
