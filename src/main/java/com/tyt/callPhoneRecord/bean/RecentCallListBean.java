package com.tyt.callPhoneRecord.bean;

import java.io.Serializable;
import java.util.Date;

public class RecentCallListBean implements Serializable {

    private static final long serialVersionUID = -4010377873411494984L;
    private Long orderId;
    private Long carId;
    private String headCity;
    private String headNo;
    private String tailCity;
    private String tailNo;
    private Long carryUserId;
    private String carryCellPhone;
    private String carryName;
    private Long payAmount;
    private Date callTime;
    private Integer carTotalServerScore;//'车方服务分_new'

    private Integer carServerRankScore;//'车方等级_new'

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public String getHeadCity() {
        return headCity;
    }

    public void setHeadCity(String headCity) {
        this.headCity = headCity;
    }

    public String getHeadNo() {
        return headNo;
    }

    public void setHeadNo(String headNo) {
        this.headNo = headNo;
    }

    public String getTailCity() {
        return tailCity;
    }

    public void setTailCity(String tailCity) {
        this.tailCity = tailCity;
    }

    public String getTailNo() {
        return tailNo;
    }

    public void setTailNo(String tailNo) {
        this.tailNo = tailNo;
    }

    public Long getCarryUserId() {
        return carryUserId;
    }

    public void setCarryUserId(Long carryUserId) {
        this.carryUserId = carryUserId;
    }

    public String getCarryCellPhone() {
        return carryCellPhone;
    }

    public void setCarryCellPhone(String carryCellPhone) {
        this.carryCellPhone = carryCellPhone;
    }

    public String getCarryName() {
        return carryName;
    }

    public void setCarryName(String carryName) {
        this.carryName = carryName;
    }

    public Long getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(Long payAmount) {
        this.payAmount = payAmount;
    }

    public Date getCallTime() {
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Integer getCarTotalServerScore() {
        return carTotalServerScore;
    }

    public void setCarTotalServerScore(Integer carTotalServerScore) {
        this.carTotalServerScore = carTotalServerScore;
    }

    public Integer getCarServerRankScore() {
        return carServerRankScore;
    }

    public void setCarServerRankScore(Integer carServerRankScore) {
        this.carServerRankScore = carServerRankScore;
    }

    @Override
    public String toString() {
        return "RecentCallListBean{" +
                "orderId=" + orderId +
                ", carId=" + carId +
                ", headCity='" + headCity + '\'' +
                ", headNo='" + headNo + '\'' +
                ", tailCity='" + tailCity + '\'' +
                ", tailNo='" + tailNo + '\'' +
                ", carryUserId=" + carryUserId +
                ", carryCellPhone='" + carryCellPhone + '\'' +
                ", carryName='" + carryName + '\'' +
                ", payAmount=" + payAmount +
                ", callTime=" + callTime +
                ", carTotalServerScore=" + carTotalServerScore +
                ", carServerRankScore=" + carServerRankScore +
                '}';
    }
}
