package com.tyt.callPhoneRecord.bean;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @ClassName UserVo
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/6/28 17:15
 * @Verdion 1.0
 **/
@Data
public class UserVo {
    private Long id;
    private String cellPhone;
    private String recommenderTel;//推荐人手机号
    private Long qq;
    private String userName;
    private String password;
    private String trueName;
    private String idCard;
    private Integer userType;
    private String homePhone;
    private String pcSign;
    private Integer serveDays;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer userSign;
    private Integer qqModTimes;
    private Timestamp qqModTime;
    private String verifyCode;
    private String province;
    private String city;
    private String county;
    private Timestamp payDate;
    private Timestamp renewalDate;
    private Integer renewalYears;
    private String ticket;//登陆ticket
    private Integer contactNum;//QQ联系人数量
    private Integer infoUploadFlag;
    private Integer infoPublishFlag = 2;
    private Integer killBill =0;//0 1速易通
    private String sales;//跟踪销售
    private Integer  verifyFlag = 0 ;//身份验证标记 0 未通过 1通过
    private Integer  platId;
    private String note;
    private Integer phoneOpenFlag=0;//手机开通字段
    private Integer qqBoxFlag;//qq消息盒子标记位 打开 1 可不打开0 默认1
    private Integer payStatus;//付费状态
    private Integer phoneServeDays;//手机剩余天数
    private String source;//用户来源
    private Timestamp endTime;//用户到期时间
    private Timestamp appointTime;//预约时间
    private int isMock; //是否马甲用户
    private String mbUserId;//传递给满帮的userId
    private String mbMemberID;//满帮钱包会员号

    /**
     * 2017-08-30
     * 黑名单状态，0-否，1-是
     */
    private Integer blackStatus = 0;

    /*********新增以下字段*/
    String isCar;// '是否完善了车的信息',
    String isBank;// '是否绑定银行卡信息',
    String headUrl;//'头像url',
    String payPassword;//'支付密码',
    Integer clientSign;//'客户端标识 1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB',
    Date lastTime;// '最后登陆时间',
    String clientVersion;//'客户端版本号',
    String osVersion;//系统版本号

    /*2015-8-31*/
    String sex;
    /*2015-09-07*/
    String deliverType;//发货方身份0货主1货站
    /*2015-11-10*/
    String isEnabled;//'用户有效标识 0无效 1有效',
    String sourceRemark;//'来源备注',
    String paymentReason;//'未付款原因',
    String bank;//'银行',
    Integer money;//'金额',

    /*2015-12-14*/
    Integer extraDays=0;//赠送天数
    Integer payNumber=0;//缴费次第
    String  payChannel;//'缴费渠道（1线上支付 2线下支付）',
    //实名认证验证照片标志0未验证1通过3认证失败
    Integer verifyPhotoSign;

    //用户分数
    Integer userPart;
    Integer remainNumber;

    //渠道
    Integer channel=0;
    String c1;
    String c2;
    String c3;

    //2017-03-06
    String deliver_type_one;//销售审核一级身份
    Integer userClass;//用户分类1、发货方2、车辆方 见 source  user_class
    Integer identityType;//用户身份见source表 user_identity_type

    private Integer carUserSign;
    private Integer goodsUserSign;
    private String carUserName;
    private Date carLastLoginTime;
    private Date goodsLastLoginTime;

    // 2021-01-23 开票、成为调度车
    /**
     * 是否调度车 1是 0不是
     */
    private Integer isDispatch;

    // 2022-06-28  根据手机号查询最近联系人是否为注册用户新增用户服务分和等级
    private Integer carTotalServerScore;//'车方服务分_new'

    private Integer carServerRankScore;//'车方等级_new'

}
