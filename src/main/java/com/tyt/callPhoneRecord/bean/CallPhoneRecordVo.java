package com.tyt.callPhoneRecord.bean;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName CallPhoneRecordVo
 * @Description
 * <AUTHOR> Lion
 * @Date 2022/6/28 17:06
 * @Verdion 1.0
 **/
@Data
public class CallPhoneRecordVo {

    private Long id;
    private Long srcMsgId;
    private Long carUserId;
    private String carUserName;
    private Integer carIsVip;
    private String path;
    private String module;
    private Date createTime;
    private String platId;

    //头像，已拼好
    private String headUrl;

    //是否实名认证
    private Boolean realNameAuthentication;

    /**
     * 平台交易数
     */
    private String tradeNums="0";

    /**
     * 与我交易数
     */
    private String coopNums="0";


    private Integer carTotalServerScore;//'车方服务分_new'

    private Integer carServerRankScore;//'车方等级_new'
    /**
     * 车方信用等级
     */
    private String carCreditRankLevel;

    /**
     * 好评率
     */
    private String rating;

    /**
     * 好评率展示类型：
     * 0：不展示好评率
     * 1：评价量≥3条时,展示好评率
     * 2：0＜评价量＜3，且有好评，展示近期有好评
     * 3：0＜评价量＜3，且无好评，展示近期无好评
     * 4：评价量=0，展示暂无评价
     */
    private Integer ratingType;

    /**
     * 好评数
     */
    private Long positiveCount;

    /**
     * 货物类型
     */
    private String goodsTypeName;

    /**
     * 承运货物次数
     */
    private Integer goodsTypeNum = 0;


    /**
     * 好评标签名，最多2条
     */
    private List<String> positiveLabels;

    /**
     * 差评标签名，最多1条
     */
    private List<String> negativeLabels;

    /**
     * 备注，仅货方能查看修改
     */
    private String remark;


}
