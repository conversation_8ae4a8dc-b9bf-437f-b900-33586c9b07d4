package com.tyt.callPhoneRecord.controller;

import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.callPhoneRecord.bean.CallPhoneRecordVo;
import com.tyt.callPhoneRecord.bean.RecentCallListBean;
import com.tyt.callPhoneRecord.bean.UserVo;
import com.tyt.callPhoneRecord.service.CallPhoneRecordService;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.CallPhoneRecord;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.user.service.UserService;
import com.tyt.util.ReturnCodeConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("plat/call/phone/record")
public class CallPhoneRecordController {

    @Resource(name = "callPhoneRecordService")
    private CallPhoneRecordService callPhoneRecordService;
    @Resource(name = "userService")
    private UserService userService;
    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    /**
     * 保存拨打电话记录
     *
     * @param record record
     * @return rm
     */
    @RequestMapping("/save")
    @ResponseBody
    public ResultMsgBean saveRecord(CallPhoneRecord record, Long userId, String clientSign) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            if (checkParameter(record, resultMsgBean)) {
                record.setCarUserId(userId);
                record.setPlatId(clientSign);
                callPhoneRecordService.saveRecord(record);
                callPhoneRecordService.addContactRecord(record.getSrcMsgId(), record.getCarUserId());
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器异常");
        }
        return resultMsgBean;
    }

    private boolean checkParameter(CallPhoneRecord record, ResultMsgBean resultMsgBean) {
        //2019-12-10 去掉拨打人昵称的校验，传入的carUserName没有用到,直接从User取出的
        //不知道当初这个接口参数的校验作用是什么
//        if (StringUtils.isBlank(record.getCarUserName())) {
//            resultMsgBean.setCode(201);
//            resultMsgBean.setMsg("车方昵称不能为空");
//            return false;
//        }
        return true;
    }

    /**
     * 查询拨打电话记录
     *
     * @param tsId
     * @return
     */
    @RequestMapping(value = {"/contacted/list", "/contacted/list.action"})
    @ResponseBody
    public ResultMsgBean contactedList(
            @RequestParam(value = "tsId", required = true) Long tsId,
            @RequestParam(value = "userId", required = true) Long userId
    ) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        try {
            List<CallPhoneRecordVo> list = callPhoneRecordService.contactedList(userId, tsId);
            resultMsgBean.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器异常");
        }
        return resultMsgBean;
    }


    /**
     * 最近联系人列表
     *
     * @param tsId
     * @return
     */
    @RequestMapping("/recent/callList")
    @ResponseBody
    public ResultMsgBean recentCallList(@RequestParam(value = "tsId", required = true) Long tsId) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            List<RecentCallListBean> list = callPhoneRecordService.getRecentCallList(tsId);
            resultMsgBean.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器异常");
        }
        return resultMsgBean;
    }

    /**
     * 根据手机号查询最近联系人是否为注册用户
     *
     * @param cellPhone
     * @return
     */
    @RequestMapping("/recent/getByCellPhone")
    @ResponseBody
    public ResultMsgBean getByCellPhone(@RequestParam(value = "cellPhone", required = true) String cellPhone) {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            User user = userService.getUserByCell(cellPhone);
            if (user != null) {
                UserVo userVo = new UserVo();
                BeanUtils.copyProperties(user, userVo);
                ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(user.getId());
                if (userCreditInfo != null) {
                    if (null != userCreditInfo.getCarTotalServerScore()) {
                        userVo.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
                    }
                    if (null != userCreditInfo.getCarServerRankScore()) {
                        userVo.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
                    }
                }
                resultMsgBean.setData(userVo);
            } else {
                resultMsgBean.setCode(201);
                resultMsgBean.setMsg("未注册用户");
                resultMsgBean.setExtraData("承运人未注册特运通，不能查看车辆位置。请尽快邀请对方注册。");
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMsgBean.setCode(ReturnCodeConstant.ERROR);
            resultMsgBean.setMsg("服务器异常");
        }
        return resultMsgBean;
    }

}
