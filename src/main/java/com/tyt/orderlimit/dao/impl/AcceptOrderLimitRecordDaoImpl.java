package com.tyt.orderlimit.dao.impl;


import com.tyt.base.dao.BaseDaoImpl;
import com.tyt.model.AcceptOrderLimitRecord;
import com.tyt.orderlimit.dao.AcceptOrderLimitRecordDao;
import org.springframework.stereotype.Repository;

@Repository("acceptOrderLimitRecordDao")
public class AcceptOrderLimitRecordDaoImpl extends BaseDaoImpl<AcceptOrderLimitRecord, Long> implements AcceptOrderLimitRecordDao {
	public AcceptOrderLimitRecordDaoImpl() {
		this.setEntityClass(AcceptOrderLimitRecord.class);
	}

}
