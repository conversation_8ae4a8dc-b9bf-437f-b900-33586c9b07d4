package com.tyt.orderlimit.service;

import com.tyt.base.service.BaseService;
import com.tyt.model.AcceptOrderLimitRecord;
import com.tyt.orderlimit.bean.AcceptOrderLimitInfo;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 限制接单记录服务层
 * @date 2023/11/28 17:54
 */
public interface AcceptOrderLimitRecordService extends BaseService<AcceptOrderLimitRecord, Long> {

    /**
     * 根据用户ID和货物ID获取接单限制信息
     * @param userId
     * @param goodsId
     * @return
     * @throws Exception
     */
    AcceptOrderLimitInfo getAcceptOrderLimitInfo(Long userId, Long goodsId) throws Exception;

    /**
     * 查询接单限制处理记录
     * @param userId
     * @param status
     * @param date
     * @return
     */
    AcceptOrderLimitRecord getAcceptOrderLimitRecord(Long userId, Integer status, Date date);
}
