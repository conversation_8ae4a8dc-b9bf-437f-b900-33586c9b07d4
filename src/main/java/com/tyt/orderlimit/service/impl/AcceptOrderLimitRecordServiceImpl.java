package com.tyt.orderlimit.service.impl;


import com.tyt.base.dao.BaseDao;
import com.tyt.base.service.BaseServiceImpl;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.model.AcceptOrderLimitRecord;
import com.tyt.model.TransportMain;
import com.tyt.orderlimit.bean.AcceptOrderLimitInfo;
import com.tyt.orderlimit.service.AcceptOrderLimitRecordService;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TransportMainService;
import com.tyt.user.service.TytConfigService;
import com.tyt.user.service.UserService;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


@Service("acceptOrderLimitRecordService")
@Slf4j
public class AcceptOrderLimitRecordServiceImpl extends BaseServiceImpl<AcceptOrderLimitRecord, Long> implements AcceptOrderLimitRecordService {

    @Autowired
    private UserService userService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private TytMessageTmplService messageTmplService;

    /**
     * 每日接单限制缓存key
     */
    private static final String DAILY_LIMIT_KEY_PREFIX = "daily:limit:";

    /**
     * 接单倒计时缓存key
     */
    private static final String COUNT_DOWN_KEY_PREFIX = "count:down:";


    @Resource(name = "acceptOrderLimitRecordDao")
    public void setBaseDao(BaseDao<AcceptOrderLimitRecord, Long> acceptOrderLimitRecordDao) {
        super.setBaseDao(acceptOrderLimitRecordDao);
    }


    /**
     * 根据用户ID和货物ID获取接单限制信息的方法
     *
     * @param userId
     * @param goodsId
     * @throws Exception
     * @returnx
     */
    @Override
    public AcceptOrderLimitInfo getAcceptOrderLimitInfo(Long userId, Long goodsId) throws Exception {
        // 接单限制信息
        AcceptOrderLimitInfo acceptOrderLimitInfo = new AcceptOrderLimitInfo();
        // 当前时间
        Date nowDate = new Date();
        // 接单限制货源发布时间限制 0:不限发布时间 1:发布1小时之内
        Integer limitGoodsPublishTime = tytConfigService.getIntValue("accept_order_limit_goods_publish_time", 1);
        // 接单限制货源类型 0:全部货源 1:优货 2:普货
        Integer limitGoodsType = tytConfigService.getIntValue("accept_order_limit_goods_type", 1);
        // 当天是否被限制缓存信息
        String dailyLimitKey = DAILY_LIMIT_KEY_PREFIX + userId + ":" + goodsId;
        // 当前用户+货源被限制倒计时缓存信息
        String countDownKey = COUNT_DOWN_KEY_PREFIX + userId + ":" + goodsId;

        AcceptOrderLimitRecord acceptOrderLimitRecord = this.getAcceptOrderLimitRecord(userId, 1, nowDate);

        // 1.判断货源信息是否需要接单限制
        TransportMain transportMain = transportMainService.getTransportMainForId(goodsId);
        if (transportMain != null && transportMain.getStatus() == 1 && TimeUtil.isToday(transportMain.getCtime().getTime())
        && isWithinLimitTime(transportMain.getCtime(), limitGoodsPublishTime) && limitGoodsTypeCheck(transportMain.getExcellentGoods(), limitGoodsType)) {

            // 2.查询用户ID+货源ID当天是否被限制过一次
            String dailyLimitInfo = RedisUtil.get(dailyLimitKey);
            // 如果被限制过一次则查询倒计时信息
            if (StringUtils.isNotBlank(dailyLimitInfo)) {
                // 2.1 查询用户ID+货源ID是否存在倒计时信息
                String countDownKeyInfo = RedisUtil.get(countDownKey);
                if (StringUtils.isNotBlank(countDownKeyInfo) && acceptOrderLimitRecord != null) {
                    Long countDownTime = RedisUtil.getJedis().ttl(countDownKey);
                    if (countDownTime != null && countDownTime > 0) {
                        // 倒计时时间，单位:秒
                        acceptOrderLimitInfo.setCountdownTime(countDownTime.intValue());
                        // 接单限制提醒信息
                        acceptOrderLimitInfo.setLimitRemindInfo(getLimitRemindInfo(acceptOrderLimitRecord));

                        return acceptOrderLimitInfo;
                    }
                }
            } else {
                // 3.判断用户限制接单记录是否存在且命中
                if (acceptOrderLimitRecord != null) {
                    // 4.设置限制接单倒计时缓存信息
                    LocalDateTime now = LocalDateTime.now();
                    LocalDateTime lastSecondOfDay = now.withHour(23).withMinute(59).withSecond(59);
                    long seconds = ChronoUnit.SECONDS.between(now, lastSecondOfDay);
                    RedisUtil.setNxAtom(dailyLimitKey, "1", (int) seconds);

                    // 接单限制处罚项(单位:秒)
                    Integer acceptOrderLimitItem = acceptOrderLimitRecord.getAcceptOrderLimitItem();
                    RedisUtil.setNxAtom(countDownKey, String.valueOf(acceptOrderLimitItem), acceptOrderLimitItem);

                    // 倒计时时间，单位:秒
                    acceptOrderLimitInfo.setCountdownTime(acceptOrderLimitItem);
                    // 接单限制提醒信息
                    acceptOrderLimitInfo.setLimitRemindInfo(getLimitRemindInfo(acceptOrderLimitRecord));
                    return acceptOrderLimitInfo;
                }
            }
        }
        return acceptOrderLimitInfo;
    }

    private String getLimitRemindInfo(AcceptOrderLimitRecord acceptOrderLimitRecord) {
        Date acceptOrderLimitEndTime = acceptOrderLimitRecord.getAcceptOrderLimitEndTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(acceptOrderLimitEndTime);
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        Date newDate = calendar.getTime();
        // 接单限制到期时间
        String limitEndTime = new SimpleDateFormat("yyyy-MM-dd").format(newDate);
        // 限制接单提醒模板
        String acceptOrderLimitTmpl = messageTmplService.getSmsTmpl("accept_order_limit_tmpl");
        // 接单限制提醒信息
        String limitRemindInfo = StringUtils.replaceEach(acceptOrderLimitTmpl, new String[]{"{param1}"}, new String[]{limitEndTime});
        return limitRemindInfo;
    }

    /**
     * 判断当前时间和首次发布时间之间的时间差是否小于1小时
     *
     * @param firstPublishTime
     * @return
     */
    private static boolean isWithinLimitTime(Date firstPublishTime, Integer limitTime) {
        if(limitTime > 0){
            // 当前时间
            Instant current =  Instant.now();
            // 货源首发时间
            Instant firstPublish = firstPublishTime.toInstant();
            Duration duration = Duration.between(firstPublish, current);
            return duration.toHours() < limitTime;
        }else{
            return true;
        }
    }

    /**
     * 接单限制货源类型校验
     *
     * @param excellentGoods
     * @param limitGoodsType
     * @return
     */
    private static boolean limitGoodsTypeCheck(Integer excellentGoods, Integer limitGoodsType) {
        if (limitGoodsType == 0) { // 限制货源类型：0:全部货源
            return true;
        } else if (limitGoodsType == 1) { // 限制货源类型 1:优货
            return excellentGoods == 1;
        } else if (limitGoodsType == 2) { // 限制货源类型 2:普货
            return excellentGoods == 0;
        }
        return false;
    }

    /**
     * 查询接单限制处理记录
     *
     * @param userId
     * @param status
     * @param date
     * @return
     */
    @Override
    public AcceptOrderLimitRecord getAcceptOrderLimitRecord(Long userId, Integer status, Date date) {
        String sql = "SELECT * FROM  accept_order_limit_record  WHERE 1=1 " +
                " AND user_id = ? " +
                " AND accept_order_limit_status = ? " +
                " AND accept_order_limit_start_time <= ? " +
                " AND accept_order_limit_end_time >= ? " +
                " ORDER BY id DESC LIMIT 1";
        List<AcceptOrderLimitRecord> acceptOrderLimitRecords = this.getBaseDao().queryForList(sql,
                new Object[]{userId, status, date, date});

        if (CollectionUtils.isNotEmpty(acceptOrderLimitRecords)) {
            return acceptOrderLimitRecords.get(0);
        }
        return null;
    }
}
