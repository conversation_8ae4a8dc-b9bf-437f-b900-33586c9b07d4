package com.tyt.auth;

import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.TokenUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * @description plat服务授权API
 * <AUTHOR>
 * @date 2022/12/5 16:25
 */
@RestController
@RequestMapping("/plat/auth")
public class AuthController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    /**
     * @description 获取token接口
     * <AUTHOR>
     * @date 2022/12/5 17:47
     * @param userId
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping("/getToken")
    @ResponseBody
    public ResultMsgBean getToken(@RequestParam(name="userId", required=true) Long userId) {
        ResultMsgBean rm = new ResultMsgBean();
        try {
            //生成token
            String token = TokenUtil.createJwtToken(userId);

            HashMap <String, Object> map = new HashMap <>();
            map.put("token", token);
            rm.setCode(ReturnCodeConstant.OK);
            rm.setData(map);
            rm.setMsg("查询成功");
        } catch (Exception e) {
            e.printStackTrace();
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
            logger.info("获取token失败，失败原因:"+e);
        }
        return rm;

    }
}
