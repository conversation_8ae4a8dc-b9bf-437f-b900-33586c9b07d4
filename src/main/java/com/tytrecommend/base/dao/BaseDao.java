package com.tytrecommend.base.dao;

import com.tytrecommend.model.PageBean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>Basic Data Access Interface </p>
 * <p>Provide basic insert, delete, update, find
 * @param <T> entity
 * @param <PK> primary key
 */
public interface BaseDao<T,PK extends Serializable> {

    /**
     * Provide for insert entity
     * @param entity
     */
    public void insert(T entity);
    
    public Serializable insertSave(T entity);

    /**
     * Provide for update entity
     * @param entity
     */
    public void update(T entity);

    /**
     * Provide for delete entity
     * @param entity
     */
    public void delete(T entity);

    /**
     * Provide for delete entity by id
     * @param id
     */
    public void deleteById(PK id);

    /**
     * Provide for find an entity by id
     * @param id
     * @return
     */
    public T findById(PK id);

    /**
     * search List<entity> by condition
     * <p>Default: search All</p>
     * <p>If the data is too large please Override the method in Child class by condition or use the PageBean</p>
     * @param condition
     * @return List<T>
     */
    public List<T> search(T condition);


    /**
     * search List<entity> by condition
     * <p>Default: search All</p>
     * <p>Notice: In the {@link BaseDaoHibImpl} the condition entity is 'entity'</p>
     * <p><h1>Example:</h1>
     * <li>entity.userName='ZouLei' and entity.age =22</li>
     * </p>
     * <b>Notice:</b><p><em>In the JDBC implements, the condition is SQL</em></p>
     * @param condition Search condition by HQL
     * @param pageBean Search by page
     * @return List<T>
     */
    public List<T> search(String condition, PageBean pageBean);


    /**
     * search List<entity> by condition
     * <p>Default: search All</p>
     * @param map Field and Value
     * @param orderBy
     * @param pageBean
     * @return
     */
    public List<T> search(Map<String, Object> map, Map<String, Object> orderBy, PageBean pageBean);



    /**
     *
     * @param hql 附带有命名参数的HQL
     * @param params 命名参数的名数组
     * @param pageBean
     * @return
     */

     public List<T> search(final String hql, final Object[] params, final PageBean pageBean) ;
  /**
   *
   * @param queryString hql
   * @param values 参数值
   * @return
   */
     public List<T> find(String queryString, Object values);

     /**
      *
      * @param queryString hsl
      * @param values 参数值
      * @return
      */
     public List<T> find(String queryString, Object... values);


     /**
      * sql语句分页查询结果集合
      * @param sql sql语句需要包含表名
      * @param params 参数值
      * @param pageNumber 页数
      * @param pageSize 页大小
      * @return List<T>
      */
     public List<T> search(final String sql, final Object[] params, final int pageNumber, final int pageSize);

     /**
      * 通过sql查询一个对象
      * @param sql sql语句包含表名
      * @param params 参数数组
      * @return <O> O
      */
     public <O> O query(final String sql, final Object[] params) ;
	 /**
      * sql语句分页查询结果集合
      * @param sql sql语句需要包含表名
      * @param params 参数值
      * @param pageNumber 页数
      * @param pageSize 页大小
      * @param scalarMap 类属性类型key字段名，value hibernate类型
      * @param c 返回实体类
      * @return <O> List<O >
      */
     @SuppressWarnings("unchecked")
     public <O> List<O > search(final String sql,
                                final Map<String, org.hibernate.type.Type> scalarMap,
                                final Class<O> c, final Object[] params, final int pageNumber,
                                final int pageSize) ;

 	/**
 	 * 占位符？传参
 	 * 
 	 * @param sql
 	 * @param parameters
 	 * @return
 	 */
 	public int executeUpdateSql(final String sql, final Object[] parameters);

 	/**
 	 * 名称传参 :ids
 	 * 
 	 * @param sql
 	 * @param map
 	 * @return
 	 */
 	public int executeUpdateSql(final String sql, final Map<String, Object> map);

 	/**
 	 * 通过ID获得一个带锁的查询
 	 * 
 	 * @param id
 	 * @return
 	 */
 	public T getByIdForLock(PK id);

}

