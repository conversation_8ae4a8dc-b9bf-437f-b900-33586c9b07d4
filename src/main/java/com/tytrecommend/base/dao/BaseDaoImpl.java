package com.tytrecommend.base.dao;

import com.tytrecommend.model.PageBean;

import org.hibernate.HibernateException;
import org.hibernate.LockMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.HibernateTemplate;

import javax.annotation.Resource;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>Basic Data Access implements by Hibernate</p>
 * <p>Provide basic insert, delete, update, find
 * <AUTHOR>
 *<li>mail: <EMAIL></li>
 * @param <T> entity
 * @param <PK> primary key
 */
public class BaseDaoImpl<T,PK extends Serializable> implements BaseDao<T, PK> {

    /**
     * support for hibernateTemplateTytLog Provide for SessionFactory
     */
    private HibernateTemplate hibernateTemplateTytRecommend;

   // private hibernateTemplateTytLog readhibernateTemplateTytLog;

    @SuppressWarnings("unchecked")
    private Class entityClass;

    public HibernateTemplate gethibernateTemplateTytRecommend() {
        return hibernateTemplateTytRecommend;
    }

    @Resource(name="hibernateTemplateTytRecommend")
    public void sethibernateTemplateTytRecommend(HibernateTemplate hibernateTemplateTytRecommend) {
        this.hibernateTemplateTytRecommend = hibernateTemplateTytRecommend;
    }

   /* public hibernateTemplateTytLog getReadhibernateTemplateTytLog() {
        return readhibernateTemplateTytLog;
    }

    @PublicResource(name="readhibernateTemplateTytLog")
    public void setReadhibernateTemplateTytLog(hibernateTemplateTytLog readhibernateTemplateTytLog) {
        this.readhibernateTemplateTytLog = readhibernateTemplateTytLog;
    }
*/
    @SuppressWarnings("unchecked")
    public void setEntityClass(Class entityClass) {
        this.entityClass = entityClass;
    }

    @SuppressWarnings("unchecked")
    public Class getEntityClass() {
        return entityClass;
    }

    public void delete(T entity) {
        hibernateTemplateTytRecommend.delete(entity);
    }

    @SuppressWarnings("unchecked")
    public void deleteById(PK id) {
        hibernateTemplateTytRecommend.delete(hibernateTemplateTytRecommend.get(entityClass, id));
    }

    @SuppressWarnings("unchecked")
    public T findById(PK id) {
        return (T)hibernateTemplateTytRecommend.get(entityClass, id);
    }

    public void insert(T entity) {
        hibernateTemplateTytRecommend.save(entity);
    }
    
    public Serializable insertSave(T entity) {
       return hibernateTemplateTytRecommend.save(entity);
    }

    public void update(T entity) {
        hibernateTemplateTytRecommend.update(entity);
    }

    @SuppressWarnings("unchecked")
    public List<T> search(T condition) {

        //If condition is empty, then search all
        if(null==condition){
            //entity name
            String entityName=entityClass.toString();
            //table name
            String tableName=entityName.substring(entityName.lastIndexOf(".")+1);
            return (List<T>) hibernateTemplateTytRecommend.find("from "+tableName);

        }else{
            return hibernateTemplateTytRecommend.findByExample(condition);
        }
    }

    @SuppressWarnings("unchecked")
    public List<T> search(final String condition,final PageBean pageBean) {
        // If the condition is empty then search all
        if (null==condition && null== pageBean) {
            return search(null);
        }

        return (List<T>) hibernateTemplateTytRecommend.executeFind(new HibernateCallback<T>() {
            //entity name
            String entityName=entityClass.toString();
            //table name
            String tableName=entityName.substring(entityName.lastIndexOf(".")+1);
            // HQL
            StringBuffer hql=new StringBuffer("from ");
            StringBuffer Counthql=new StringBuffer("select count(*) from ");

            public T doInHibernate(Session session) throws HibernateException,
                    SQLException {
                //Assembly HQL
                hql.append(tableName);
                Counthql.append(tableName);

                hql.append(" as entity where 1=1 ");
                Counthql.append(" as entity where 1=1 ");

                if(!"".equals(condition) && null!=condition){
                    hql.append(" and "+condition);
                    Counthql.append(" and "+condition);
                }
                Query query=session.createQuery(hql.toString());
                //If Page is not empty then setting page
                if(null!=pageBean){
                    //set all count
                    Query countQuery=session.createQuery(Counthql.toString());
                    pageBean.setRowCount(Long.parseLong(countQuery.uniqueResult().toString()));
                    //pageBean.setRowCount(query.list().size());
                    // Page setting
                    int firstResultIndex=pageBean.getPageSize()*(pageBean.getCurrentPage()-1);
                    query.setFirstResult(firstResultIndex);
                    query.setMaxResults(pageBean.getPageSize());
                }
                return (T) query.list();

            }
        });
    }

    @Override
    public List<T> search(Map<String, Object> map,Map<String, Object> orderBy, PageBean pageBean) {
        return search(null,pageBean);
    }

    
/**
 * 
 * @param hql 附带有命名参数的HQL
 * @param params 命名参数的名数组
 * @param pageBean
 * @return
 */
    @SuppressWarnings("unchecked")
    public List<T> search(final String hql, final Object[] params,final PageBean pageBean) {
        // If the condition is empty then search all
        if (null==hql && null== pageBean) {
            return search(null);
        }

        return (List<T>) hibernateTemplateTytRecommend.executeFind(new HibernateCallback<T>() {
            //entity name
            String entityName=entityClass.toString();
            //table name
            String tableName=entityName.substring(entityName.lastIndexOf(".")+1);
            // HQL
            StringBuffer hql=new StringBuffer("from ");
            StringBuffer Counthql=new StringBuffer("select count(*) from ");

            public T doInHibernate(Session session) throws HibernateException,
                    SQLException {
                //Assembly HQL
                hql.append(tableName);
                Counthql.append(tableName);

                hql.append(" as entity where 1=1 ");
                Counthql.append(" as entity where 1=1 ");

                if(!"".equals(hql) && null!=hql){
                    hql.append(" and "+hql);
                    Counthql.append(" and "+hql);
                }
                
                Query query=session.createQuery(hql.toString());
                
                if (params != null) {
                    int count = params.length;
                    for (int i = 0; i < count; i++) {
                        query.setParameter(i, params[i]);
                    }
                }
                //If Page is not empty then setting page
                if(null!=pageBean){
                    //set all count
                    Query countQuery=session.createQuery(Counthql.toString());
                    if (params != null) {
                        int count = params.length;
                        for (int i = 0; i < count; i++) {
                        	countQuery.setParameter(i, params[i]);
                        }
                    }            
                    pageBean.setRowCount(Long.parseLong(countQuery.uniqueResult().toString()));
                    //pageBean.setRowCount(query.list().size());
                    // Page setting
                    int firstResultIndex=pageBean.getPageSize()*(pageBean.getCurrentPage()-1);
                    query.setFirstResult(firstResultIndex);
                    query.setMaxResults(pageBean.getPageSize());
                }
                return (T) query.list();

            }
        });
    }

	@Override
	public List<T> find(String queryString, Object value) {
		return (List<T>) hibernateTemplateTytRecommend.find(queryString, value);
	}
	
	@Override
	public List<T> find(String queryString, Object... values) {
		return (List<T>) hibernateTemplateTytRecommend.find(queryString, values);
	}
	
	
	
	 /**
     * sql语句分页查询结果集合
     * @param sql sql语句需要包含表名
     * @param params 参数值
     * @param pageNumber 页数
     * @param pageSize 页大小
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<T> search(final String sql, final Object[] params,final int pageNumber, final int pageSize) {
        // If the condition is empty then search all
        if (null==sql &&pageNumber!=0 &&pageSize!=0 ) {
            return search(null);
        }

        return (List<T>) hibernateTemplateTytRecommend.executeFind(new HibernateCallback<T>() {

            public T doInHibernate(Session session) throws HibernateException,
                    SQLException {
            	SQLQuery query=session.createSQLQuery(sql.toString());
                if (params != null) {
                    int count = params.length;
                    for (int i = 0; i < count; i++) {
                        query.setParameter(i, params[i]);
                    }
                }
                int offRow = ((pageNumber == 0 ? 1 : pageNumber) - 1)
                        * pageSize;
                    query.setFirstResult(offRow);
                    query.setMaxResults(pageSize);
                    query.addEntity(entityClass);                
                return (T) query.list();
            }
        });
    }

	/**
	 * 返回查询sql的一个对象
	 * @param sql
	 *            sql语句包含表名
	 * @param params
	 *            参数数组
	 * @return <O> O
	 */
	@SuppressWarnings("unchecked")
	public <O> O query(final String sql, final Object[] params) {
		HibernateTemplate tmpl = gethibernateTemplateTytRecommend();
		return tmpl.execute(new HibernateCallback<O>() {

			public O doInHibernate(Session session) throws HibernateException,
					SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				List<O> list = query.list();
				if (list != null && list.size() > 0) {
					return list.get(0);
				}
				return null;
			}
		});
	}
	/**
	 * sql语句分页查询结果集合
	 * 
	 * @param <O>
	 * 
	 * @param sql
	 *            sql语句需要包含表名
	 * @param params
	 *            参数值
	 * @param pageNumber
	 *            页数
	 * @param pageSize
	 *            页大小
	 * @param scalarMap
	 *            类属性类型key字段名，value hibernate类型
	 * @param c
	 *            实体类Class
	 * @return <O> List<O >
	 */
	@SuppressWarnings("unchecked")
	public <O> List<O> search(final String sql,
			final Map<String, org.hibernate.type.Type> scalarMap,
			final Class<O> c, final Object[] params, final int pageNumber,
			final int pageSize) {
		// If the condition is empty then search all
		if (null == sql && pageNumber != 0 && pageSize != 0) {
			return null;
		}

		return (List<O>) hibernateTemplateTytRecommend.executeFind(new HibernateCallback<O>() {

			public O doInHibernate(Session session) throws HibernateException,
					SQLException {
				SQLQuery query = session.createSQLQuery(sql.toString());
				if (params != null) {
					int count = params.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, params[i]);
					}
				}
				if (null != scalarMap) {
					for (Map.Entry<String, org.hibernate.type.Type> entry : scalarMap
							.entrySet()) {
						query.addScalar(entry.getKey(), entry.getValue());
					}
				}

				query.setResultTransformer(Transformers.aliasToBean(c));

				int offRow = ((pageNumber == 0 ? 1 : pageNumber) - 1)
						* pageSize;
				query.setFirstResult(offRow);
				query.setMaxResults(pageSize);
				return (O) query.list();
			}
		});
	}

	public int executeUpdateSql(final String sql, final Object[] parameters) {

		return hibernateTemplateTytRecommend.execute(new HibernateCallback<Integer>() {

			public Integer doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (parameters != null) {
					int count = parameters.length;
					for (int i = 0; i < count; i++) {
						query.setParameter(i, parameters[i]);
					}
				}
				return query.executeUpdate();
			}
		}).intValue();
	}

	public int executeUpdateSql(final String sql, final Map<String, Object> map) {

		return hibernateTemplateTytRecommend.execute(new HibernateCallback<Integer>() {

			public Integer doInHibernate(Session session) throws HibernateException, SQLException {
				SQLQuery query = session.createSQLQuery(sql);
				if (map != null) {
					Set<String> keySet = map.keySet();
					for (String string : keySet) {
						Object obj = map.get(string);
						// 这里考虑传入的参数是什么类型，不同类型使用的方法不同
						if (obj instanceof Collection<?>) {
							query.setParameterList(string, (Collection<?>) obj);
						} else if (obj instanceof Object[]) {
							query.setParameterList(string, (Object[]) obj);
						} else {
							query.setParameter(string, obj);
						}
					}
				}
				return query.executeUpdate();
			}
		}).intValue();
	}
	public T getByIdForLock(PK id) {
		return (T) hibernateTemplateTytRecommend.get(entityClass, id, LockMode.UPGRADE);
	}
}

