package com.tytrecommend.base.service;

import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.model.PageBean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * User: Administrator
 * Date: 13-11-10
 * Time: 下午5:06
 */
public class BaseServiceImpl<T,PK extends Serializable> implements BaseService<T,PK> {

    private BaseDao<T, PK> baseDao;

    public void setBaseDao(BaseDao<T, PK> baseDao) {
        this.baseDao = baseDao;
    }
    public BaseDao<T, PK> getBaseDao() {
        return baseDao;
    }


    /**
     * 添加
     * @param t
     */
    public void add(T t){
        baseDao.insert(t);
    }

    /**
     * 修改
     * @param t
     */
    public  void update(T t){
        baseDao.update(t);
    }

    /**
     * 删除
     * @param pk
     */
    public void delete(PK pk){
        baseDao.deleteById(pk);
    }

    /**
     * 查询单条纪录
     * @param pk
     * @return
     */
    public T getById(PK pk) {
        return baseDao.findById(pk);
    }


    /**
     * 根据条件分页查询
     * @param condition
     * @param page
     * @return
     */
    @Override
    public List<T> getList(String condition,PageBean page){
        return baseDao.search(condition, page);
    }

    @Override
    public T find(T t) {
      List<T> list =  baseDao.search(t);
      if(list != null && list.size() >0) return list.get(0);

      return null;
    }
	
    @Override
	public List<T> findList(T t) {
		return baseDao.search(t);
	}
    
    @Override
 	public int executeUpdateSql(String sql, Object[] parameters) {
 		return baseDao.executeUpdateSql(sql, parameters);
 	}
     @Override
 	public int executeUpdateSql(String sql, Map<String, Object> map) {
 		return baseDao.executeUpdateSql(sql, map);
 	}
 	@Override
 	public T getByIdForLock(PK id) {
 		return baseDao.getByIdForLock(id);
 	}
}