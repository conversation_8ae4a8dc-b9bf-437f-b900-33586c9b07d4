package com.tytrecommend.base.bean;

import java.io.Serializable;

public class BaseParameter  implements Serializable {

	private static final long serialVersionUID = -5462891432636466499L;
	private String clientSign;
	private String osVersion;
	private String clientVersion;
	private String clientId;
	private String sign; 
	private Long userId;
	private String ticket;
	public String getClientSign() {
		return clientSign;
	}
	public void setClientSign(String clientSign) {
		this.clientSign = clientSign;
	}
	public String getOsVersion() {
		return osVersion;
	}
	public void setOsVersion(String osVersion) {
		this.osVersion = osVersion;
	}
	public String getClientVersion() {
		return clientVersion;
	}
	public void setClientVersion(String clientVersion) {
		this.clientVersion = clientVersion;
	}
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	public String getSign() {
		return sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getTicket() {
		return ticket;
	}
	public void setTicket(String ticket) {
		this.ticket = ticket;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	
}
