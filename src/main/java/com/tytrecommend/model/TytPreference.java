package com.tytrecommend.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytPreference entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_preference", catalog = "tyt_recommend")
public class TytPreference implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3350421805181511090L;
	// Fields

	private Long id;
	private Long userId;
	private Long carId;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private Integer startCoordX;
	private Integer startCoordY;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private Integer destCoordX;
	private Integer destCoordY;
	private String price;
	private Integer beginWeight;
	private Integer endWeight;
	private Integer length;
	private Integer wide;
	private Integer high;
	private Date ctime;
	private String md5;
	private Long isUpdate;
	private Long status;
	private Date utime;
	
	private int findGoodOnoff;
	private String preferenceCar;
	private Date onLineTime;
	

	// Constructors

	/** default constructor */
	public TytPreference() {
	}

	/** minimal constructor */
	public TytPreference(Long userId, Long carId, Long status, Date utime) {
		this.userId = userId;
		this.carId = carId;
		this.status = status;
		this.utime = utime;
	}

	/** full constructor */
	public TytPreference(Long userId, Long carId, String startProvinc,
			String startCity, String startArea, Integer startCoordX,
			Integer startCoordY, String destProvinc, String destCity,
			String destArea, Integer destCoordX, Integer destCoordY,
			String price, Integer beginWeight, Integer endWeight,
			Integer length, Integer wide, Integer high, Date ctime, String md5,
			Long isUpdate, Long status, 
			Date utime,int findGoodOnoff,String preferenceCar,Date onLineTime) {
		this.userId = userId;
		this.carId = carId;
		this.startProvinc = startProvinc;
		this.startCity = startCity;
		this.startArea = startArea;
		this.startCoordX = startCoordX;
		this.startCoordY = startCoordY;
		this.destProvinc = destProvinc;
		this.destCity = destCity;
		this.destArea = destArea;
		this.destCoordX = destCoordX;
		this.destCoordY = destCoordY;
		this.price = price;
		this.beginWeight = beginWeight;
		this.endWeight = endWeight;
		this.length = length;
		this.wide = wide;
		this.high = high;
		this.ctime = ctime;
		this.md5 = md5;
		this.isUpdate = isUpdate;
		this.status = status;
	
		this.utime = utime;
		this.findGoodOnoff = findGoodOnoff;
		this.preferenceCar = preferenceCar;
		this.onLineTime = onLineTime;
	}

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id", nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "car_id", nullable = false)
	public Long getCarId() {
		return this.carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "start_provinc", length = 50)
	public String getStartProvinc() {
		return this.startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	@Column(name = "start_city", length = 50)
	public String getStartCity() {
		return this.startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	@Column(name = "start_area", length = 50)
	public String getStartArea() {
		return this.startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	@Column(name = "start_coord_x")
	public Integer getStartCoordX() {
		return this.startCoordX;
	}

	public void setStartCoordX(Integer startCoordX) {
		this.startCoordX = startCoordX;
	}

	@Column(name = "start_coord_y")
	public Integer getStartCoordY() {
		return this.startCoordY;
	}

	public void setStartCoordY(Integer startCoordY) {
		this.startCoordY = startCoordY;
	}

	@Column(name = "dest_provinc", length = 50)
	public String getDestProvinc() {
		return this.destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	@Column(name = "dest_city", length = 50)
	public String getDestCity() {
		return this.destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	@Column(name = "dest_area", length = 50)
	public String getDestArea() {
		return this.destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "dest_coord_x")
	public Integer getDestCoordX() {
		return this.destCoordX;
	}

	public void setDestCoordX(Integer destCoordX) {
		this.destCoordX = destCoordX;
	}

	@Column(name = "dest_coord_y")
	public Integer getDestCoordY() {
		return this.destCoordY;
	}

	public void setDestCoordY(Integer destCoordY) {
		this.destCoordY = destCoordY;
	}

	@Column(name = "price", length = 30)
	public String getPrice() {
		return this.price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	@Column(name = "begin_weight")
	public Integer getBeginWeight() {
		return this.beginWeight;
	}

	public void setBeginWeight(Integer beginWeight) {
		this.beginWeight = beginWeight;
	}

	@Column(name = "end_weight")
	public Integer getEndWeight() {
		return this.endWeight;
	}

	public void setEndWeight(Integer endWeight) {
		this.endWeight = endWeight;
	}

	@Column(name = "length", length = 30)
	public Integer getLength() {
		return this.length;
	}

	public void setLength(Integer length) {
		this.length = length;
	}

	@Column(name = "wide", length = 30)
	public Integer getWide() {
		return this.wide;
	}

	public void setWide(Integer wide) {
		this.wide = wide;
	}

	@Column(name = "high", length = 30)
	public Integer getHigh() {
		return this.high;
	}

	public void setHigh(Integer high) {
		this.high = high;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "md5", length = 32)
	public String getMd5() {
		return this.md5;
	}

	public void setMd5(String md5) {
		this.md5 = md5;
	}

	@Column(name = "is_update")
	public Long getIsUpdate() {
		return this.isUpdate;
	}

	public void setIsUpdate(Long isUpdate) {
		this.isUpdate = isUpdate;
	}

	@Column(name = "status", nullable = false)
	public Long getStatus() {
		return this.status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}


	@Column(name = "utime", nullable = false, length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name = "find_good_onoff")
	public int getFindGoodOnoff() {
		return findGoodOnoff;
	}

	public void setFindGoodOnoff(int findGoodOnoff) {
		this.findGoodOnoff = findGoodOnoff;
	}
	@Column(name = "preference_car")
	public String getPreferenceCar() {
		return preferenceCar;
	}

	public void setPreferenceCar(String preferenceCar) {
		this.preferenceCar = preferenceCar;
	}
	@Column(name = "online_time")
	public Date getOnLineTime() {
		return onLineTime;
	}

	public void setOnLineTime(Date onLineTime) {
		this.onLineTime = onLineTime;
	}
	
	

}