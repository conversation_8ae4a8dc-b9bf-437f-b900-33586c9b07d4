package com.tytrecommend.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytPreferenceCar entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_preference_car", catalog = "tyt_recommend")
public class TytPreferenceCar implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5058687866502012025L;
	// Fields

	private Long id;
	private Long userId;
	private Long carId;
	private Long tpId;
	private String carCode;
	private String carName;
	private Date ctime;

	// Constructors

	/** default constructor */
	public TytPreferenceCar() {
	}

	/** minimal constructor */
	public TytPreferenceCar(Long userId, Long carId, Long tpId) {
		this.userId = userId;
		this.carId = carId;
		this.tpId = tpId;
	}

	/** full constructor */
	public TytPreferenceCar(Long userId, Long carId, Long tpId, String carCode,
			String carName, Date ctime) {
		this.userId = userId;
		this.carId = carId;
		this.tpId = tpId;
		this.carCode = carCode;
		this.carName = carName;
		this.ctime = ctime;
	}

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id", nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "car_id", nullable = false)
	public Long getCarId() {
		return this.carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "tp_id", nullable = false)
	public Long getTpId() {
		return this.tpId;
	}

	public void setTpId(Long tpId) {
		this.tpId = tpId;
	}

	@Column(name = "car_code", length = 10)
	public String getCarCode() {
		return this.carCode;
	}

	public void setCarCode(String carCode) {
		this.carCode = carCode;
	}

	@Column(name = "car_name", length = 30)
	public String getCarName() {
		return this.carName;
	}

	public void setCarName(String carName) {
		this.carName = carName;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

}