package com.tytrecommend.model;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 表名：tyt_credit_deduct_log
 */
@Data
@Entity
@Table(name = "tyt_credit_deduct_log", catalog = "tyt_recommend")
public class CreditDeductLog implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -5514356951408660297L;

    /**
     * id
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户类型：1-车方；2-货方
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 扣分项
     */
    @Column(name = "deduct_type")
    private String deductType;

    /**
     * 扣除分数
     */
    @Column(name = "deduct_score")
    private Integer deductScore;

    /**
     * 是否发送站内信：0-否；1-是；
     */
    @Column(name = "is_push")
    private Integer isPush;

    /**
     * 创建时间
     */
    @Column(name = "ctime")
    private Date cTime;

    /**
     * 更新时间
     */
    @Column(name = "mtime")
    private Date mTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private Long remark;

}
