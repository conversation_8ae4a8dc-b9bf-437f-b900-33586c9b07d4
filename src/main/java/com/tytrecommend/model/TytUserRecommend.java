package com.tytrecommend.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;


import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * TytUserRecommend entity. <AUTHOR> Persistence Tools
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "tyt_user_recommend", catalog = "tyt_recommend")
public class TytUserRecommend implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -7802646192681646726L;
	private Long id;
	private Long rdUserId;
	private Long userId;
	private Long carId;
	private Long tsId;
	private String taskContent;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private String startCoord;
	private String destCoord;
	private String startDetailAdd;
	private String destDetailAdd;
	private Integer androidDistance;
	private Integer iosDistance;
	private Integer verifyPhotoSign;
	private Date pubDate;
	private String isInfoFee;
	private Date releaseTime;
	private Date regTime;
	private String nickName;
	private String price;
	private Integer recommendStatus;
	private Date ctime;
	private Integer carIndex;
	private Integer matching;
	private Integer invalidTime;
	private Date endTime;
	private Date utime;


	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "rd_user_id", nullable = false)
	public Long getRdUserId() {
		return this.rdUserId;
	}

	public void setRdUserId(Long rdUserId) {
		this.rdUserId = rdUserId;
	}
	@Column(name = "user_id", nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "car_id", nullable = false)
	public Long getCarId() {
		return this.carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "ts_id", nullable = false)
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "task_content", length = 500)
	public String getTaskContent() {
		return this.taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	@Column(name = "start_provinc", length = 50)
	public String getStartProvinc() {
		return this.startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	@Column(name = "start_city", length = 50)
	public String getStartCity() {
		return this.startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	@Column(name = "start_area", length = 50)
	public String getStartArea() {
		return this.startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	@Column(name = "dest_provinc", length = 50)
	public String getDestProvinc() {
		return this.destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	@Column(name = "dest_city", length = 50)
	public String getDestCity() {
		return this.destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	@Column(name = "dest_area", length = 50)
	public String getDestArea() {
		return this.destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "start_coord", length = 50)
	public String getStartCoord() {
		return this.startCoord;
	}

	public void setStartCoord(String startCoord) {
		this.startCoord = startCoord;
	}

	@Column(name = "dest_coord", length = 50)
	public String getDestCoord() {
		return this.destCoord;
	}

	public void setDestCoord(String destCoord) {
		this.destCoord = destCoord;
	}

	@Column(name = "start_detail_add", length = 200)
	public String getStartDetailAdd() {
		return this.startDetailAdd;
	}

	public void setStartDetailAdd(String startDetailAdd) {
		this.startDetailAdd = startDetailAdd;
	}

	@Column(name = "dest_detail_add", length = 200)
	public String getDestDetailAdd() {
		return this.destDetailAdd;
	}

	public void setDestDetailAdd(String destDetailAdd) {
		this.destDetailAdd = destDetailAdd;
	}

	@Column(name = "android_distance")
	public Integer getAndroidDistance() {
		return this.androidDistance;
	}

	public void setAndroidDistance(Integer androidDistance) {
		this.androidDistance = androidDistance;
	}

	@Column(name = "ios_distance")
	public Integer getIosDistance() {
		return this.iosDistance;
	}

	public void setIosDistance(Integer iosDistance) {
		this.iosDistance = iosDistance;
	}

	@Column(name = "verify_photo_sign")
	public Integer getVerifyPhotoSign() {
		return this.verifyPhotoSign;
	}

	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
		this.verifyPhotoSign = verifyPhotoSign;
	}

	@Column(name = "pub_date", length = 0)
	public Date getPubDate() {
		return this.pubDate;
	}

	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}

	@Column(name = "is_info_fee", length = 4)
	public String getIsInfoFee() {
		return this.isInfoFee;
	}

	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}

	@Column(name = "release_time", length = 0)
	public Date getReleaseTime() {
		return this.releaseTime;
	}

	public void setReleaseTime(Date releaseTime) {
		this.releaseTime = releaseTime;
	}

	@Column(name = "reg_time", length = 0)
	public Date getRegTime() {
		return this.regTime;
	}

	public void setRegTime(Date regTime) {
		this.regTime = regTime;
	}

	@Column(name = "nick_name", length = 100)
	public String getNickName() {
		return this.nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	@Column(name = "price", length = 30)
	public String getPrice() {
		return this.price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	@Column(name = "recommend_status")
	public Integer getRecommendStatus() {
		return this.recommendStatus;
	}

	public void setRecommendStatus(Integer recommendStatus) {
		this.recommendStatus = recommendStatus;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "car_index")
	public Integer getCarIndex() {
		return this.carIndex;
	}

	public void setCarIndex(Integer carIndex) {
		this.carIndex = carIndex;
	}

	@Column(name = "matching")
	public Integer getMatching() {
		return this.matching;
	}

	public void setMatching(Integer matching) {
		this.matching = matching;
	}

	@Column(name = "invalid_time")
	public Integer getInvalidTime() {
		return this.invalidTime;
	}

	public void setInvalidTime(Integer invalidTime) {
		this.invalidTime = invalidTime;
	}

	@Column(name = "end_time", length = 0)
	public Date getEndTime() {
		return this.endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	@Column(name = "utime", nullable = false, length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

}