package com.tytrecommend.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * TytFeedback entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_feedback", catalog = "tyt_recommend")
public class TytFeedback implements java.io.Serializable {
	private static final long serialVersionUID = -7895969385430502613L;

	private Long id;
	private Long tsId;
	private Long carId;
	private Long userId;
	private Long fbUserId;
	private Date ctime;
	private Integer status;
	private Set<TytFeedbackSub> feedbackSubs = new HashSet<TytFeedbackSub>();

	/** default constructor */
	public TytFeedback() {
	}

	/** full constructor */
	public TytFeedback(Long tsId, Long carId, Long userId, Long fbUserId, Date ctime, Integer status) {
		this.tsId = tsId;
		this.carId = carId;
		this.userId = userId;
		this.fbUserId = fbUserId;
		this.ctime = ctime;
		this.status = status;
	}

	/*
	 * 一对多关联TytMsgTmplTime
	 */
	@OneToMany(mappedBy = "feedback", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	public Set<TytFeedbackSub> getFeedbackSubs() {
		return feedbackSubs;
	}

	public void setFeedbackSubs(Set<TytFeedbackSub> feedbackSubs) {
		this.feedbackSubs = feedbackSubs;
	}

	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "ts_id", nullable = false)
	public Long getTsId() {
		return this.tsId;
	}

	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}

	@Column(name = "car_id", nullable = false)
	public Long getCarId() {
		return this.carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "user_id", nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "fb_user_id", nullable = false)
	public Long getFbUserId() {
		return this.fbUserId;
	}

	public void setFbUserId(Long fbUserId) {
		this.fbUserId = fbUserId;
	}

	@Column(name = "ctime", nullable = false, length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "status", nullable = false)
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

}