package com.tytrecommend.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytTsPercent entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_ts_percent", catalog = "tyt_recommend")
public class TytTsPercent implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 1301230706510504347L;
	private Long id;
	private Long userId;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private Integer total;
	private Integer amount;
	private Integer percent;
	private Date ctime;
	private Date utime;
	private Integer status;


	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id", nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
	@Column(name = "start_provinc", length = 50)
	public String getStartProvinc() {
		return this.startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	@Column(name = "start_city", length = 50)
	public String getStartCity() {
		return this.startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	@Column(name = "start_area", length = 50)
	public String getStartArea() {
		return this.startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	@Column(name = "dest_provinc", length = 50)
	public String getDestProvinc() {
		return this.destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	@Column(name = "dest_city", length = 50)
	public String getDestCity() {
		return this.destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	@Column(name = "dest_area", length = 50)
	public String getDestArea() {
		return this.destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "total")
	public Integer getTotal() {
		return this.total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	@Column(name = "amount")
	public Integer getAmount() {
		return this.amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	@Column(name = "percent")
	public Integer getPercent() {
		return this.percent;
	}

	public void setPercent(Integer percent) {
		this.percent = percent;
	}

	@Column(name = "ctime", nullable = false, length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	@Column(name = "utime", length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	@Column(name = "status")
	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

}