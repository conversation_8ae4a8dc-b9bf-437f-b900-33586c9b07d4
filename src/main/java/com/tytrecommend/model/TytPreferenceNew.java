package com.tytrecommend.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * TytPreference entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_preference_new", catalog = "tyt_recommend")
public class TytPreferenceNew implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3350421805181511090L;
	// Fields

	private Long id;
	private Long userId;
	private Long carId;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private Integer startCoordX;
	private Integer startCoordY;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private Integer destCoordX;
	private Integer destCoordY;
	private Integer beginWeight;
	private Integer endWeight;
	private Integer length;
	private Integer wide;
	private Integer high;
	private Date ctime;
	private Integer isAuthUpdate;
	private Long status;
	private Date utime;
	
	private int findGoodOnoff;
	private String preferenceCar;
	private String startDistance;
	
//	private String currentSpeed;
//	private String currentDetailAddr;
//	private String currentPosition;// 车辆方位角度
//	private Integer currentStatus;
//	private Double startPointLongitude;//车辆当前位置经度
//	private Double startPointLatitude;//车辆当前位置纬度
	
	

	// Constructors

	
	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "user_id", nullable = false)
	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Column(name = "car_id", nullable = false)
	public Long getCarId() {
		return this.carId;
	}

	public void setCarId(Long carId) {
		this.carId = carId;
	}

	@Column(name = "start_provinc", length = 50)
	public String getStartProvinc() {
		return this.startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	@Column(name = "start_city", length = 50)
	public String getStartCity() {
		return this.startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	@Column(name = "start_area", length = 50)
	public String getStartArea() {
		return this.startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	@Column(name = "start_coord_x")
	public Integer getStartCoordX() {
		return this.startCoordX;
	}

	public void setStartCoordX(Integer startCoordX) {
		this.startCoordX = startCoordX;
	}

	@Column(name = "start_coord_y")
	public Integer getStartCoordY() {
		return this.startCoordY;
	}

	public void setStartCoordY(Integer startCoordY) {
		this.startCoordY = startCoordY;
	}

	@Column(name = "dest_provinc", length = 50)
	public String getDestProvinc() {
		return this.destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	@Column(name = "dest_city", length = 50)
	public String getDestCity() {
		return this.destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	@Column(name = "dest_area", length = 50)
	public String getDestArea() {
		return this.destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	@Column(name = "dest_coord_x")
	public Integer getDestCoordX() {
		return this.destCoordX;
	}

	public void setDestCoordX(Integer destCoordX) {
		this.destCoordX = destCoordX;
	}

	@Column(name = "dest_coord_y")
	public Integer getDestCoordY() {
		return this.destCoordY;
	}

	public void setDestCoordY(Integer destCoordY) {
		this.destCoordY = destCoordY;
	}



	@Column(name = "begin_weight")
	public Integer getBeginWeight() {
		return this.beginWeight;
	}

	public void setBeginWeight(Integer beginWeight) {
		this.beginWeight = beginWeight;
	}

	@Column(name = "end_weight")
	public Integer getEndWeight() {
		return this.endWeight;
	}

	public void setEndWeight(Integer endWeight) {
		this.endWeight = endWeight;
	}

	@Column(name = "length", length = 30)
	public Integer getLength() {
		return this.length;
	}

	public void setLength(Integer length) {
		this.length = length;
	}

	@Column(name = "wide", length = 30)
	public Integer getWide() {
		return this.wide;
	}

	public void setWide(Integer wide) {
		this.wide = wide;
	}

	@Column(name = "high", length = 30)
	public Integer getHigh() {
		return this.high;
	}

	public void setHigh(Integer high) {
		this.high = high;
	}

	@Column(name = "ctime", length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	

	@Column(name = "is_auth_update")
	public Integer getIsAuthUpdate() {
		return this.isAuthUpdate;
	}

	public void setIsAuthUpdate(Integer isAuthUpdate) {
		this.isAuthUpdate = isAuthUpdate;
	}

	@Column(name = "status", nullable = false)
	public Long getStatus() {
		return this.status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}


	@Column(name = "utime", nullable = false, length = 0)
	public Date getUtime() {
		return this.utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}
	@Column(name = "find_good_onoff")
	public int getFindGoodOnoff() {
		return findGoodOnoff;
	}

	public void setFindGoodOnoff(int findGoodOnoff) {
		this.findGoodOnoff = findGoodOnoff;
	}
	@Column(name = "preference_car")
	public String getPreferenceCar() {
		return preferenceCar;
	}

	public void setPreferenceCar(String preferenceCar) {
		this.preferenceCar = preferenceCar;
	}

	@Column(name = "start_distance")
	public String getStartDistance() {
		return startDistance;
	}

	public void setStartDistance(String startDistance) {
		this.startDistance = startDistance;
	}

//	@Column(name = "current_speed")
//	public String getCurrentSpeed() {
//		return currentSpeed;
//	}
//
//	public void setCurrentSpeed(String currentSpeed) {
//		this.currentSpeed = currentSpeed;
//	}
//	@Column(name = "current_detail_addr")
//	public String getCurrentDetailAddr() {
//		return currentDetailAddr;
//	}
//
//	public void setCurrentDetailAddr(String currentDetailAddr) {
//		this.currentDetailAddr = currentDetailAddr;
//	}
//	@Column(name = "current_position")
//	public String getCurrentPosition() {
//		return currentPosition;
//	}
//
//	public void setCurrentPosition(String currentPosition) {
//		this.currentPosition = currentPosition;
//	}
//	@Column(name = "current_status")
//	public Integer getCurrentStatus() {
//		return currentStatus;
//	}
//
//	public void setCurrentStatus(Integer currentStatus) {
//		this.currentStatus = currentStatus;
//	}
//	@Column(name = "start_point_longitude")
//	public Double getStartPointLongitude() {
//		return startPointLongitude;
//	}
//
//	public void setStartPointLongitude(Double startPointLongitude) {
//		this.startPointLongitude = startPointLongitude;
//	}
//	@Column(name = "start_point_latitude")
//	public Double getStartPointLatitude() {
//		return startPointLatitude;
//	}
//
//	public void setStartPointLatitude(Double startPointLatitude) {
//		this.startPointLatitude = startPointLatitude;
//	}
	

}