package com.tytrecommend.model;

import java.util.Date;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * TytFeedbackSub entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "tyt_feedback_sub", catalog = "tyt_recommend")
public class TytFeedbackSub implements java.io.Serializable {
	private static final long serialVersionUID = 6303467642089995182L;
	
	private Long id;
	private Integer fbCode;
	private String fbName;
	private Date ctime;
	private TytFeedback feedback;

	// Constructors

	/** default constructor */
	public TytFeedbackSub() {
	}

	/** minimal constructor */
	public TytFeedbackSub(Date ctime) {
		this.ctime = ctime;
	}

	/** full constructor */
	public TytFeedbackSub(Integer fbCode, String fbName, Date ctime) {
		this.fbCode = fbCode;
		this.fbName = fbName;
		this.ctime = ctime;
	}

	// Property accessors
	@Id
	@GeneratedValue
	@Column(name = "id", unique = true, nullable = false)
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	/*
	 * 多对一关联TytMsgTmpl
	 */
	@ManyToOne(cascade = CascadeType.ALL, optional = false)
	@JoinColumn(name = "fb_id", referencedColumnName = "id")
	public TytFeedback getFeedback() {
		return feedback;
	}

	public void setFeedback(TytFeedback feedback) {
		this.feedback = feedback;
	}

	@Column(name = "fb_code")
	public Integer getFbCode() {
		return this.fbCode;
	}

	public void setFbCode(Integer fbCode) {
		this.fbCode = fbCode;
	}

	@Column(name = "fb_name", length = 30)
	public String getFbName() {
		return this.fbName;
	}

	public void setFbName(String fbName) {
		this.fbName = fbName;
	}

	@Column(name = "ctime", nullable = false, length = 0)
	public Date getCtime() {
		return this.ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

}