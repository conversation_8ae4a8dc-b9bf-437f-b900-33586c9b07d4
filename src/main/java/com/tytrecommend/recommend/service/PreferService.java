package com.tytrecommend.recommend.service;


import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import com.tytrecommend.base.service.BaseService;
import com.tytrecommend.model.TytPreference;
import com.tytrecommend.model.TytPreferenceCar;

public interface PreferService extends BaseService<TytPreference, Long>{

	public boolean saveTytPreference(TytPreference tytPre,List<TytPreferenceCar> tytPreCars);

	public boolean updateTytPreference(Map<String, String> params,
			TytPreference count) throws IllegalAccessException, InvocationTargetException;

	public TytPreference newFindPreferenceByCarId(Long id);

	public String createMD5String(Map<String, String> params);
	
	public boolean updateIsUpdate(Long id);

	public boolean updateUserPreferOnlineTime(Long id);

}
