package com.tytrecommend.recommend.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import com.tytrecommend.base.service.BaseService;
import com.tytrecommend.recommend.bean.GuessLikeResultBean;
import com.tytrecommend.model.TytUserRecommend;

public interface UserRecommendService extends BaseService<TytUserRecommend, Long> {

	/**
	 * 获得精准推荐列表
	 * 
	 * @param carId
	 * @param id
	 * @return
	 */
	public List<TytUserRecommend> updateGetAccurateListForCarId(Long userId, Long carId, Long id,Integer accurateTransportListSize);

	/**
	 * 修改用户失效过期精准货源状态
	 * 
	 * @param userId
	 * @param carId
	 */
	public void updateInvalidOrOverdueAccurateData(Long userId, Long carId);

	/**
	 * 根据车辆id查询车辆的猜你喜欢列表
	 * 
	 * @param carId
	 * @param guessYouLikeCount 
	 * @return
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 */
	public void queryTransportListByCarId(String carId, List<GuessLikeResultBean> guessLikeResultBeans, String clientSign, Integer guessYouLikeCount) throws IllegalAccessException, InvocationTargetException;

	/**
	 * 根据车辆信息和货物信息删除精准推荐的货物
	 * 
	 * @param carId
	 * @param srcMsgId
	 */
	public void deleteRecommByCarIdAndGoodId(String carId, Long srcMsgId);

}
