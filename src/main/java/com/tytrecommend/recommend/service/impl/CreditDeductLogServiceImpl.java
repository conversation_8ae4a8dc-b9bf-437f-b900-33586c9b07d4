package com.tytrecommend.recommend.service.impl;

import com.tyt.util.TimeUtil;
import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;
import com.tytrecommend.model.CreditDeductLog;
import com.tytrecommend.recommend.service.CreditDeductLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("creditDeductLogService")
public class CreditDeductLogServiceImpl extends BaseServiceImpl<CreditDeductLog, Long> implements CreditDeductLogService {

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "creditDeductLogDao")
	public void setBaseDao(BaseDao<CreditDeductLog, Long> creditDeductLogDao) {
		super.setBaseDao(creditDeductLogDao);
	}

	@Override
	public List<CreditDeductLog> getListByUserId(Long userId, Integer userType,Long queryTime) {
		String sql = "SELECT t.id,t.user_id,t.user_type,t.deduct_type,t.deduct_score,t.is_push,t.ctime,t.mtime,t.remark FROM tyt_credit_deduct_log t WHERE t.user_id = ? AND t.user_type = ? AND t.ctime > ? AND t.ctime < ?";
		String beginTime;
		String endTime;
		if (null==queryTime||queryTime<=0) {
			beginTime = TimeUtil.getMonthFirstDayFom(new Date());
			endTime = TimeUtil.getMonthLastDayFom(new Date());
		} else {
			beginTime = TimeUtil.getMonthFirstDayFom(new Date(queryTime));
			endTime = TimeUtil.getMonthLastDayFom(new Date(queryTime));
		}
		List<CreditDeductLog> list = this.getBaseDao().search(sql, new Object[]{userId,userType,beginTime,endTime},1,100);
		return list;
	}
}
