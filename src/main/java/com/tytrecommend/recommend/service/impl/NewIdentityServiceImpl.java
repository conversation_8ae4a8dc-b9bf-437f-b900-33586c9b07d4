package com.tytrecommend.recommend.service.impl;

import com.tyt.plat.mapper.recommend.NewIdentityMapper;
import com.tytrecommend.model.NewIdentity;
import com.tytrecommend.recommend.service.NewIdentityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import static org.springframework.transaction.annotation.Propagation.NOT_SUPPORTED;

/**
 * bi身份信息
 * <AUTHOR>
 * @since 2024-05-30 20:01
 */
@Service("newIdentityService")
public class NewIdentityServiceImpl implements NewIdentityService {
    @Autowired
    private NewIdentityMapper newIdentityMapper;

    @Override
    @Transactional(propagation = NOT_SUPPORTED)
    public NewIdentity getByUserId(Long userId) {
        return newIdentityMapper.getByUserId(userId);
    }
}
