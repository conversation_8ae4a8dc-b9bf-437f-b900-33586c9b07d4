package com.tytrecommend.recommend.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.TimeUtil;
import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;
import com.tytrecommend.model.TytUserRecommend;
import com.tytrecommend.recommend.bean.GuessLikeResultBean;
import com.tytrecommend.recommend.service.UserRecommendService;

@Service("userRecommendService")
public class UserRecommendServiceImpl extends BaseServiceImpl<TytUserRecommend, Long> implements UserRecommendService {
	private static final String RECOMMEND_CAR = "recommend_car_";
	private static final int RECOMMEND_STATUS_DELETE = 3;
	public Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource(name = "tytConfigService")
	private TytConfigService configService;

	@Resource(name = "tytUserRecommendDao")
	public void setBaseDao(BaseDao<TytUserRecommend, Long> tytUserRecommendDao) {
		super.setBaseDao(tytUserRecommendDao);
	}

	public List<TytUserRecommend> updateGetAccurateListForCarId(Long userId, Long carId, Long id, Integer accurateTransportListSize) {
		// 精准货物列表条数
		// Integer accurateTransportListSize =
		// configService.getIntValue(ACCURATETRANSPORTLISTSIZE);
		if (accurateTransportListSize == null) {
			accurateTransportListSize = 10;
		}
		// 先删除无效的和过期数据在查询
		updateInvalidOrOverdueAccurateData(userId, carId);

		StringBuffer sql = new StringBuffer();
		sql.append("SELECT ");
		sql.append(" r.`id`,r.`rd_user_id`,r.`user_id`,r.`car_id`,r.`ts_id`,r.`task_content`,r.`start_provinc`,r.`start_city`, ");
		sql.append(" r.`start_area`,r.`dest_provinc`,r.`dest_city`,r.`dest_area`,r.`start_coord`,r.`dest_coord`,r.`start_detail_add` ");
		sql.append(" ,r.`dest_detail_add`,r.`android_distance`,r.`reg_time`,r.`ios_distance`,r.`price`,r.`verify_photo_sign`,r.`pub_date`,r.`is_info_fee`,r.`release_time` ");
		sql.append(" ,r.`nick_name`,r.`recommend_status`,r.`ctime`,r.`car_index`,r.`matching`/100 matching,r.`end_time`,r.`invalid_time`,r.`utime` ");
		sql.append(" FROM tyt_user_recommend r WHERE r.rd_user_id=? and car_id=? AND recommend_status=? AND ctime>? AND ctime<=? AND end_time>? ");
		List<Object> params = new ArrayList<Object>();
		String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
		String endTime = TimeUtil.formatDateTime(new Date());
		params.add(userId);
		params.add(carId);
		params.add(0);
		params.add(beginTime);
		params.add(endTime);
		params.add(endTime);

		if (id != null && id.intValue() != 0) {
			sql.append(" and id>?");
			params.add(id);
		}
		sql.append(" order by ctime  desc ");
		List<TytUserRecommend> list = this.getBaseDao().search(sql.toString(), params.toArray(), 1, accurateTransportListSize);
		return list;
	}

	public void updateInvalidOrOverdueAccurateData(Long userId, Long carId) {
		String beginTime = TimeUtil.formatDate(new Date()) + " 00:00:00";
		String endTime = TimeUtil.formatDateTime(new Date());
		// 修改为过期
		String sql = "UPDATE tyt_recommend.tyt_user_recommend  SET  recommend_status=?  WHERE rd_user_id=? " + "AND car_id=? AND recommend_status=? AND ctime>? AND ctime<=? AND end_time<=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { 2, userId, carId, 0, beginTime, endTime, endTime });
		// 修改为无效
		sql = "UPDATE tyt_recommend.tyt_user_recommend   " + " SET  recommend_status=?  " + " WHERE  rd_user_id=? and car_id=? " + " AND recommend_status=? AND ctime>? " + " AND ctime<=? and ts_id  NOT IN (" + " SELECT distinct t.src_msg_id  FROM tyt.tyt_transport t WHERE " + " t.src_msg_id IN (SELECT ts_id  FROM ( SELECT d.ts_id FROM tyt_recommend.tyt_user_recommend d" + " WHERE d.rd_user_id=?  AND  d.car_id=? AND  " + " d.recommend_status=? AND d.ctime>? AND d.ctime<=? ) p )" + " AND t.ctime>? AND t.ctime<=? AND t.STATUS=?" + " AND (t.info_status=? or t.info_status=?))";
	}

	@Override
	public void queryTransportListByCarId(String carId, List<GuessLikeResultBean> guessLikeResultBeans, String clientSign, Integer guessYouLikeCount) throws IllegalAccessException, InvocationTargetException {
		String recommendCarKey = RECOMMEND_CAR + carId + "_" + TimeUtil.formatDate_(new Date());
		// 根据车辆id查询车辆的猜你喜欢的货物id集合
		List<Object> guessYouLikeList = RedisUtil.getObjectZSet(recommendCarKey, 0, guessYouLikeCount);
//		logger.info("query guess you like list by key: " + recommendCarKey + ", result is: " + guessYouLikeList);
		if (guessYouLikeList != null && guessYouLikeList.size() > 0) {
			guessLikeResultBeans = configGuessYouLikeList(guessYouLikeList, clientSign, guessLikeResultBeans);
		}
	}

	@SuppressWarnings("rawtypes")
	private List<GuessLikeResultBean> configGuessYouLikeList(List guessYouLikeList, String clientSign, List<GuessLikeResultBean> guessLikeResultBeans) throws IllegalAccessException, InvocationTargetException {
		GuessLikeResultBean guessLikeResultBean;
		for (int i = 0; i < guessYouLikeList.size(); i++) {
			guessLikeResultBean = (GuessLikeResultBean) guessYouLikeList.get(i);
			/*
			 * 区分android和ios设置距离,终端标识(1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB)
			 */
			if ("2".equals(clientSign)) {
				guessLikeResultBean.setDistance(guessLikeResultBean.getAndroidDistance() + "");
			} else if ("3".equals(clientSign)) {
				guessLikeResultBean.setDistance(guessLikeResultBean.getIosDistance() + "");
			}
			guessLikeResultBeans.add(guessLikeResultBean);
		}
		return guessLikeResultBeans;
	}

	@Override
	public void deleteRecommByCarIdAndGoodId(String carId, Long srcMsgId) {
		String sql = "UPDATE tyt_user_recommend SET recommend_status=? WHERE car_id=? AND ts_id=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { RECOMMEND_STATUS_DELETE, carId, srcMsgId });
	}
}
