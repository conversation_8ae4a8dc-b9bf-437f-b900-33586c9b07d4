package com.tytrecommend.recommend.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;




import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;
import com.tytrecommend.model.CarLicenseDict;
import com.tytrecommend.recommend.service.CarLicenseDictService;


@Service("carLicenseDictService")
public class CarLicenseDictServiceImpl extends BaseServiceImpl<CarLicenseDict, Long> implements CarLicenseDictService {

	

	@Resource(name = "carLicenseDictDao")
	public void setBaseDao(BaseDao<CarLicenseDict, Long> carLicenseDictDao) {
		super.setBaseDao(carLicenseDictDao);
	}
	
	public CarLicenseDict getCarLicenseDict(String carNo){
		String hql="from CarLicenseDict where carKey=?";
		List<CarLicenseDict> list=this.getBaseDao().find(hql, carNo);
		if(list!=null && list.size()>0){
			return list.get(0);
		}else {
			list = this.getBaseDao().find(hql, carNo.substring(0, 1));
			if(list!=null && list.size()>0){
				return list.get(0);
			}
			return null;
		}
		
	}
	
	public CarLicenseDict getCarLicenseDict(String headCity,String headNo) {
		CarLicenseDict	carLicenseDict=getCarLicenseDict(headCity.trim()+headNo.trim().toUpperCase());
			if(carLicenseDict!=null){
				return carLicenseDict;
			}else return null;
		}
	
}
