package com.tytrecommend.recommend.service.impl;

import com.tyt.model.TytGeoDict;
import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;
import com.tytrecommend.model.TytPreferenceNew;
import com.tytrecommend.recommend.business.TytGeoDictService;
import com.tytrecommend.recommend.service.PreferNewService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service("preferNewService")
public class PreferNewServiceImpl extends BaseServiceImpl<TytPreferenceNew, Long> implements PreferNewService {

	@Resource(name = "preferDao")
	public void setBaseDao(BaseDao<TytPreferenceNew, Long> preferDao) {
		super.setBaseDao(preferDao);
	}

	@Resource(name = "tytGeoDictBusiness")
	private TytGeoDictService tytGeoDictBusiness;

	@Override
	public TytPreferenceNew newFindPreferenceByCarId(Long id) {
		String sql = " from TytPreferenceNew where carId=?";
		List<TytPreferenceNew> tytPreference = this.getBaseDao().find(sql, new Object[] { id });
		return (tytPreference == null || tytPreference.size() < 1) ? null : tytPreference.get(0);
	}

	@Override
	public void updatePoint(String id, String provinc, String city, String area, String isUpdateType) {
		String sql = "";
		// 获取坐标信息
		Integer coordX = null;
		Integer coordY = null;
		if ((provinc != null && !"".equals(provinc)) && (city != null && !"".equals(city))) {
			TytGeoDict tytGeoDictStart = tytGeoDictBusiness.getTytGeoDict(provinc, city, area);
			coordX = Integer.valueOf(tytGeoDictStart.getPx().toString());
			coordY = Integer.valueOf(tytGeoDictStart.getPy().toString());
		}
		switch (isUpdateType) {
		case "1":
			sql = "UPDATE tyt_recommend.`tyt_preference_new` tpn SET tpn.`start_provinc`=?, tpn.`start_city`=?, tpn.`start_area`=?,tpn.`start_coord_x`=?,tpn.`start_coord_y`=? WHERE car_id=?";
			break;
		case "2":
			sql = "UPDATE tyt_recommend.`tyt_preference_new` tpn SET tpn.`dest_provinc`=?, tpn.`dest_city`=?, tpn.`dest_area`=?,tpn.`dest_coord_x`=?,tpn.`dest_coord_y`=? WHERE car_id=?";
			break;
		}
		this.getBaseDao().executeUpdateSql(sql, new Object[] { provinc, city, area, coordX, coordY, id });
	}

	@Override
	public void updateSize(String id, String beginWeight, String endWeight, String length, String wide, String high, String goodType) {
		if (StringUtils.isNotEmpty(length)) {
			length = String.valueOf((int) ((Double.valueOf(length) * 100)));
		} else {
			length = null;
		}
		if (StringUtils.isNotEmpty(wide)) {
			wide = String.valueOf((int) ((Double.valueOf(wide) * 100)));
		} else {
			wide = null;
		}
		if (StringUtils.isNotEmpty(high)) {
			high = String.valueOf((int) ((Double.valueOf(high) * 100)));
		} else {
			high = null;
		}
		if (StringUtils.isNotEmpty(beginWeight)) {
			beginWeight = String.valueOf((int) ((Double.valueOf(beginWeight) * 100)));
		}
		if (StringUtils.isNotEmpty(endWeight)) {
			endWeight = String.valueOf((int) ((Double.valueOf(endWeight) * 100)));
		}
		String sql = "UPDATE tyt_recommend.`tyt_preference_new` tpn SET tpn.`begin_weight`=?, tpn.`end_weight`=?, tpn.`length`=?, tpn.`wide`=?, tpn.`high`=?, tpn.`preference_car`=?, tpn.`utime`=NOW() WHERE tpn.`car_id`=?";
		this.getBaseDao().executeUpdateSql(sql, new Object[] { beginWeight, endWeight, length, wide, high, goodType, id });
	}

	@Override
	public void saveIfNOtExits(String carId, Long userId) {
		List<TytPreferenceNew> preferenceNewList = this.getBaseDao().find("from TytPreferenceNew where carId=? ", Long.valueOf(carId));
		// 不存在才保存
		if (preferenceNewList == null || preferenceNewList.size() == 0) {
			TytPreferenceNew preferenceNew = new TytPreferenceNew();
			// 设置偏好车辆id
			preferenceNew.setCarId(Long.valueOf(carId));
			// 设置车辆所属用户id
			preferenceNew.setUserId(userId);
			// 设置创建时间
			preferenceNew.setCtime(new Date());
			// 设置车辆自动位置更新为手动更新 是否自动更新1：自动更新 2：手动更新 3：强制手动更新
			preferenceNew.setIsAuthUpdate(2);
			// 设置状态为正常 状态0正常1无效
			preferenceNew.setStatus(0L);
			// 设置更新时间
			preferenceNew.setUtime(new Date());
			// 设置找货开关为关 找货开关0是关,1是开
			preferenceNew.setFindGoodOnoff(0);
			this.getBaseDao().insert(preferenceNew);
		}
	}

}
