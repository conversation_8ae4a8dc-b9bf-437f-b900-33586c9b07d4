package com.tytrecommend.recommend.service.impl;




import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.tyt.infofee.bean.MqGuessLikeMsg;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqRecommMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.model.Car;
import com.tyt.model.TytGeoDict;
import com.tyt.user.service.CarService;
import com.tyt.util.MD5Util;
import com.tyt.util.SerialNumUtil;
import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;
import com.tytrecommend.model.TytPreference;
import com.tytrecommend.model.TytPreferenceCar;
import com.tytrecommend.model.TytPreferenceHistory;
import com.tytrecommend.recommend.bean.MqMsgCacheInitBean;
import com.tytrecommend.recommend.business.TytGeoDictService;
import com.tytrecommend.recommend.service.PreferCarService;
import com.tytrecommend.recommend.service.PreferHistoryService;
import com.tytrecommend.recommend.service.PreferService;

@Service("preferService")
public class PreferServiceImpl extends BaseServiceImpl<TytPreference, Long> implements PreferService {
	
	@Resource(name="preferDao")
	public void setBaseDao(BaseDao<TytPreference, Long> preferDao){
		super.setBaseDao(preferDao);
	}
	@Resource(name="preferCarService")
	private PreferCarService preferCarService;
	
	@Resource(name="tytMqRecommMessService")
	private TytMqRecommMessageService tytMqRecommMessService;
	
	@Resource(name="carService")
	private CarService carService;
	
	@Resource(name="tytGeoDictBusiness")
	private TytGeoDictService tytGeoDictBusiness;
	
	@Resource(name="preferHistoryService")
	private PreferHistoryService preferHistoryService;
	
	@Override
	public boolean saveTytPreference(TytPreference tytPre,List<TytPreferenceCar> tytPreCars) {
		Long preId=(Long) this.getBaseDao().insertSave(tytPre);
		if(tytPreCars!=null){
			for(TytPreferenceCar tytPreferenceCar :tytPreCars){
				tytPreferenceCar.setTpId(preId);
				preferCarService.add(tytPreferenceCar);
			}
		}
		if(preId==null){
			return false;
		}else{
			return true;
		}	
	}

	
	/**
	 * 更新偏好设置
	 * @throws InvocationTargetException 
	 * @throws IllegalAccessException 
	 */
	@Override
	public boolean updateTytPreference(Map<String, String> params,
			TytPreference tytPreference) throws IllegalAccessException, InvocationTargetException {
		String originalMd5 =tytPreference.getMd5();
		String newMd5 = createMD5String(params);
		if(newMd5.equals(originalMd5)){
			//未更新
			return false;
		}
		//记录偏好更新
		TytPreferenceHistory history = new TytPreferenceHistory();
		TytPreference tytPreferenceUse= new TytPreference();
		BeanUtils.copyProperties(tytPreferenceUse, tytPreference);
		tytPreferenceUse.setId(null);
		BeanUtils.copyProperties(history, tytPreferenceUse);
		
		history.setTpId(tytPreference.getId());
		history.setHistoryTime(new Date());
		preferHistoryService.add(history);
	
		tytPreference.setDestArea(StringUtils.isBlank(params.get("destArea"))?null:params.get("destArea"));
		tytPreference.setDestCity(StringUtils.isBlank(params.get("destCity"))?null:params.get("destCity"));
		tytPreference.setDestProvinc(StringUtils.isBlank(params.get("destProvinc"))?null:params.get("destProvinc"));
		tytPreference.setStartArea(StringUtils.isBlank(params.get("startArea"))?null:params.get("startArea"));
		tytPreference.setStartCity(StringUtils.isBlank(params.get("startCity"))?null:params.get("startCity"));
		tytPreference.setStartProvinc(StringUtils.isBlank(params.get("startProvinc"))?null:params.get("startProvinc"));
		if(StringUtils.isNotEmpty(params.get("length")))
			tytPreference.setLength((int) (Double.valueOf(params.get("length"))*100));
		else 
			tytPreference.setLength(null);
		if(StringUtils.isNotEmpty(params.get("wide")))
			tytPreference.setWide((int) (Double.valueOf(params.get("wide"))*100));
		else tytPreference.setWide(null);
		if(StringUtils.isNotEmpty(params.get("high")))
			tytPreference.setHigh((int) (Double.valueOf(params.get("high"))*100));
		else tytPreference.setHigh(null);
		if(StringUtils.isNotEmpty(params.get("endWeight")))
			tytPreference.setEndWeight((int) (Double.valueOf(params.get("endWeight"))*100));
		else tytPreference.setEndWeight(null);
		if(StringUtils.isNotEmpty(params.get("beginWeight")))
			tytPreference.setBeginWeight((int) (Double.valueOf(params.get("beginWeight"))*100));
		else tytPreference.setBeginWeight(null);
		
		//设置出发地和目的地的坐标
		if((params.get("startProvinc")!=null&&!"".equals(params.get("startProvinc")))&&
				(params.get("startCity")!=null&&!"".equals(params.get("startCity")))){
			TytGeoDict tytGeoDictStart = tytGeoDictBusiness.getTytGeoDict(params.get("startProvinc"),params.get("startCity"),params.get("startArea"));
			tytPreference.setStartCoordX(Integer.valueOf(tytGeoDictStart.getPx().toString()));
			tytPreference.setStartCoordY(Integer.valueOf(tytGeoDictStart.getPy().toString()));
		}else{
			tytPreference.setStartCoordX(null);
			tytPreference.setStartCoordY(null);
		}
		if((params.get("destProvinc")!=null&&!"".equals(params.get("destProvinc")))&&
				(params.get("destCity")!=null&&!"".equals(params.get("destCity")))){
			TytGeoDict tytGeoDictDest = tytGeoDictBusiness.getTytGeoDict(params.get("destProvinc"),params.get("destCity"),params.get("destArea"));
			tytPreference.setDestCoordX(Integer.valueOf(tytGeoDictDest.getPx().toString()));
			tytPreference.setDestCoordY(Integer.valueOf(tytGeoDictDest.getPy().toString()));
		}else{
			tytPreference.setDestCoordX(null);
			tytPreference.setDestCoordY(null);
		}
		//设置md5 唯一值验证
		tytPreference.setMd5(newMd5);
		tytPreference.setIsUpdate(Long.valueOf("1"));
		tytPreference.setUtime(new Date());
		this.getBaseDao().update(tytPreference);
		TytPreferenceCar preCar  = new TytPreferenceCar();
		preCar.setCarId(Long.valueOf(params.get("id")));
		//优化
		tytPreference.setOnLineTime(new Date());
		//设置偏好货物类型
		tytPreference.setPreferenceCar(params.get("goodType"));
		if(StringUtils.isNotEmpty(params.get("goodType"))){
			String[] goodTypes = params.get("goodType").split("、");
			//把新增的车型 源数据存在 而新增车型没有的删除
			boolean isFlag = false;
			List<TytPreferenceCar> preferCarList = preferCarService.newgetPreferCarByCarId(Long.valueOf(params.get("id")));
			for(TytPreferenceCar preferCar :preferCarList){
				for(String preCarName : goodTypes){
					if(preCarName.equals(preferCar.getCarName())){
						isFlag=true;
					}
				}
				if(!isFlag){
					preferCarService.delete(preferCar.getId());
				}
				isFlag=false;
			}
			//循环列表有的跳过没有的新增
			for(String preCarName : goodTypes){
				TytPreferenceCar tytPreferenceCarOld = preferCarService.newgetPreferCarByName(preCarName,Long.valueOf(params.get("id")));
				if(tytPreferenceCarOld==null){
					TytPreferenceCar tytPreferenceCar = new TytPreferenceCar();
					tytPreferenceCar.setCarId(Long.valueOf(params.get("id")));
					tytPreferenceCar.setCarName(preCarName);
					tytPreferenceCar.setTpId(tytPreference.getId());
					tytPreferenceCar.setUserId(Long.valueOf(params.get("userId")));
					tytPreferenceCar.setCtime(new Date());
					preferCarService.add(tytPreferenceCar);;
				}else{
					continue;
				}
			}
		}else{
			List<TytPreferenceCar> preferCarList = preferCarService.newgetPreferCarByCarId(Long.valueOf(params.get("id")));
			if(preferCarList!=null && preferCarList.size()>0){
				for(TytPreferenceCar preferCar :preferCarList){
						preferCarService.delete(preferCar.getId());
					}				
				}
		}
		return true;
		
		
	}
	
	/**
	 * 生成MD5按下面的格式排序 其中类型偏好排序sort
	 * 载重偏好最小最大尺寸偏好长宽高类型偏好 出发地(省市区) 目的地
	 */
	public String createMD5String(Map<String, String> params) {
		StringBuffer sb = new StringBuffer();
		sb.append(params.get("beginWeight")).append(params.get("endWeight"));//载重偏好
		sb.append(params.get("length")).append(params.get("wide")).append(params.get("high"));//尺寸偏好长宽高
		if(params.get("goodType")!=null && !"".equals(params.get("goodType")))
			sb.append(sort(params.get("goodType")));//货物类型偏好
		sb.append(params.get("startProvinc")).append(params.get("startCity")).append(params.get("startArea"));//出发地偏好
		sb.append(params.get("destProvinc")).append(params.get("destCity")).append(params.get("destArea"));//目的地偏好
		return MD5Util.GetMD5Code(sb.toString());
	}
	//排序货物类型以保证一致
	private  String sort(String testSort){
		String [] arr = testSort.split("、");
		List<String> list =Arrays.asList(arr);
		Collections.sort(list);
		return list.toString();
		}

	@Override
	public TytPreference newFindPreferenceByCarId(Long Carid) {
		String sql=" from TytPreference where carId=?";
		List<TytPreference> tytPreference= this.getBaseDao().find(sql, new Object[]{Carid});
		return (tytPreference==null||tytPreference.size()<1)? null:tytPreference.get(0);
	}


	@Override
	public boolean updateIsUpdate(Long id) {
		int count = this.getBaseDao().executeUpdateSql("update tyt_recommend.tyt_preference set is_update = 0 where car_id =? ", new Object[]{id});
		return count>0?true:false;
	}

	@Override
	public boolean updateUserPreferOnlineTime(Long id) {
		int count = this.getBaseDao().executeUpdateSql("update tyt_recommend.tyt_preference set online_time = now() where user_id =? ", new Object[]{id});
		return count>0?true:false;
	}

}
