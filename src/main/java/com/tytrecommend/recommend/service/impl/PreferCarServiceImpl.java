package com.tytrecommend.recommend.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;

import com.tytrecommend.recommend.service.PreferCarService;
import com.tytrecommend.model.TytPreferenceCar;


@Service("preferCarService")
public class PreferCarServiceImpl extends BaseServiceImpl<TytPreferenceCar, Long> implements PreferCarService{
	@Resource(name="preferCarDao")
	public void setBaseDao(BaseDao<TytPreferenceCar, Long> preferCarDao){
		super.setBaseDao(preferCarDao);
	}
	@Override
	public List<TytPreferenceCar> newgetPreferCar(Long preId) {
		List<TytPreferenceCar> cars = this.getBaseDao().find(" from TytPreferenceCar where tpId=?", new Object[]{preId});
		return cars;
		
	}
	@Override
	public List<TytPreferenceCar> newgetPreferCarByCarId(Long carId) {
		List<TytPreferenceCar> cars = this.getBaseDao().find("from TytPreferenceCar where carId=?", new Object[]{carId});
		return cars;
		
	}
	@Override
	public TytPreferenceCar newgetPreferCarByName(String preCarName,Long carId) {
		String sql =  "from TytPreferenceCar where carId=?and carName=?";
		List<TytPreferenceCar> cars=this.getBaseDao().find(sql, new Object[]{carId,preCarName});
		return (cars==null||cars.size()<1)? null:cars.get(0);
		
	}
	
	
}
