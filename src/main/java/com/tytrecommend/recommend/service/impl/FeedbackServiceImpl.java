package com.tytrecommend.recommend.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.tyt.cache.CacheService;
import com.tyt.model.Transport;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.TimeUtil;
import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;
import com.tytrecommend.model.TytFeedback;
import com.tytrecommend.model.TytFeedbackSub;
import com.tytrecommend.recommend.service.FeedbackService;
import com.tytrecommend.recommend.service.UserRecommendService;

@Service("feedbackService")
public class FeedbackServiceImpl extends BaseServiceImpl<TytFeedback, Long> implements FeedbackService {
	public Logger logger = LoggerFactory.getLogger(this.getClass());
	/*
	 * 保存距离与索引位置的关系的map
	 */
	private static Map<String, Integer> locationIndexMap;
	@Resource(name = "userRecommendService")
	private UserRecommendService userRecommendService;
	@Resource(name = "tytConfigService")
	private TytConfigService configService;
	@Resource(name = "cacheServiceMcImpl")
	private CacheService cacheService;

	@Resource(name = "feedbackDao")
	public void setBaseDao(BaseDao<TytFeedback, Long> feedbackDao) {
		super.setBaseDao(feedbackDao);
	}

	@Override
	public void saveFeedback(String carId, String userId, Transport transport, String code) {
		TytFeedback feedback = new TytFeedback();
		feedback.setCarId(Long.valueOf(carId));
		feedback.setCtime(new Date());
		feedback.setFbUserId(Long.valueOf(userId));
		feedback.setStatus(0);
		if (transport != null) {
			feedback.setTsId(transport.getSrcMsgId());
			feedback.setUserId(transport.getUserId());
			/*
			 * 处理用户针对该精准货源的反馈信息
			 */
			String[] codesArr = code.split("-");
			if (codesArr.length > 0) {
				Set<TytFeedbackSub> feedbackSubs = new HashSet<TytFeedbackSub>();
				feedback.setFeedbackSubs(feedbackSubs);
				int curCode;
				String feedbackName;
				TytFeedbackSub feedbackSub;
				for (int i = 0; i < codesArr.length; i++) {
					feedbackSub = new TytFeedbackSub();
					curCode = Integer.valueOf(codesArr[i]);
					feedbackSub.setFbCode(curCode);
					feedbackName = queryFeedbackNameByCode(curCode);
					feedbackSub.setFbName(feedbackName);
					feedbackSub.setFeedback(feedback);
					feedbackSub.setCtime(new Date());
					feedbackSubs.add(feedbackSub);
					// 根据不同的反馈状态码处理反馈
					dealFeedback(curCode, carId, transport.getSrcMsgId(), transport);
				}
				// 保存精准货源反馈信息
				this.getBaseDao().insert(feedback);
			}
		}
	}

	private void dealFeedback(int curCode, String carId, Long srcMsgId, Transport transport) {
		switch (curCode) {
		case 2:
			/*
			 * 空驶距离远: 用户上报后，直接从用户推荐列表中删除该条货源，且当前页面从货物信息详情页返回精准货源列表；同时减少推荐距离
			 */
			dealEmptyDriveFar(carId, srcMsgId);
			break;
		case 3:
			/*
			 * 货物类型不符: 选择货物类型不符提交后，推荐机制中加入类型判断机制，当前后续的推荐中排除给该用户推荐此类货物，仅在当天有效
			 */
			dealGoodTypeNotMatches(carId, transport);
			break;
		case 4:
			/*
			 * 目的地拉货难:
			 * 选择该理由的用户推荐机制中加入目的地判断机制，即D目的地（dest_area字段）的货物不给此类用户推荐；仅在当天有效
			 */
			dealDestFreightDifficult(carId, transport);
			break;
		case 7:
			/*
			 * 货已成交:
			 * 同一单货源单日累计被3个或以上客户反馈此项时，该发货方当日发的所有货源均不再走精准货源推荐，全部直接发到找货列表（其货源可以参与猜你喜欢
			 * ）
			 */
			dealGoodAccomplish(transport);
			break;
		}
	}

	private void dealEmptyDriveFar(String carId, Long srcMsgId) {
		// 删除用户推荐列表中的该条货源
		userRecommendService.deleteRecommByCarIdAndGoodId(carId, srcMsgId);
		// 减少用户车辆当天的找货距离，并存储该车辆当天的找货距离到缓存中，在下次给该车辆推送新精准货源的时候使用
		String gradientDistance = configService.fetchRecommStrValue(Constant.RECOMMEND_GRADIENT_DISTANCE_KEY, "25#20#15#10#8#7#5");
		// 获取用户当天该车的找货距离
		String today = TimeUtil.formatDate_(new Date());
		// recommend_distance_${carId}_${today}
		String recommDistanceKey = queryKeyFromConfig(Constant.RECOMMEND_DISTANCE_KEY, "recommend_distance_${carId}_${today}", new String[] { "${carId}", "${today}" }, new String[] { carId, today });
		// 获取用户当前车辆当天的精准货源推荐距离
		String curRecommDistance = cacheService.getString(recommDistanceKey);
		logger.info("dealEmptyDriveLong query from memcache by key: " + recommDistanceKey + ",  result is: " + curRecommDistance);
		// 获取距离递减梯度的数组
		String[] gradientDistanceArr = gradientDistance.split("#");
		if (locationIndexMap == null) {
			locationIndexMap = new HashMap<String, Integer>();
			for (int i = 0; i < gradientDistanceArr.length; i++) {
				locationIndexMap.put(gradientDistanceArr[i], i);
			}
		}
		/*
		 * 如果是没有值则说明用户当天还没有对精准货源反馈过找货距离远
		 */
		if (!StringUtils.isEmpty(curRecommDistance)) {
			int curDistance = Integer.valueOf(curRecommDistance).intValue();
			int minDistance = Integer.valueOf(gradientDistanceArr[gradientDistanceArr.length - 1]);
			// 如果当前找货距离为最小距离则不再改变当天该车的精准货源推荐距离
			if (curDistance != minDistance) {
				// 获取当前距离在距离递减梯度数组中的位置
				int curLocationIndex = locationIndexMap.get(curRecommDistance);
				logger.info("dealEmptyDriveLong current location index is: " + curLocationIndex);
				// 设置车辆递减后的精准货源找货距离
				cacheService.setString(recommDistanceKey, gradientDistanceArr[curLocationIndex + 1], Constant.ONE_DAY);
			}
		} else {
			// 没有车辆精准推荐货源距离则从最高递减一级
			cacheService.setString(recommDistanceKey, gradientDistanceArr[1], Constant.ONE_DAY);
		}
	}

	private String queryKeyFromConfig(String configKey, String defaultValue, String[] searchList, String[] replacementList) {
		String recommDistanceKey = configService.fetchRecommStrValue(configKey, defaultValue);
		recommDistanceKey = StringUtils.replaceEach(recommDistanceKey, searchList, replacementList);
		return recommDistanceKey;
	}

	private void dealGoodAccomplish(Transport transport) {
		// 获取有多少人反馈货源已成交则该货主当日所有货源不推荐
		int stopRecommendNum = configService.fetchRecommendIntValue(Constant.STOP_RECOMMEND_NUM_KEY, 3);
		logger.info("dealGoodAccomplish stop recommend num is: " + stopRecommendNum);
		/*
		 * 记录该货物当天被反馈货已成交的次数，如果达到了3次，则记录该货主到当日禁止精准推荐的列表中
		 * recommend_good_accomplish_${srcMsgId}_${today}
		 */
		String recommGoodAccomplishKey = queryKeyFromConfig(Constant.RECOMMEND_GOOD_ACCOMPLISH_KEY, "recommend_good_accomplish_${srcMsgId}_${today}", new String[] { "${srcMsgId}", "${today}" }, new String[] { transport.getSrcMsgId() + "", TimeUtil.formatDate_(new Date()) });
		String recommendGoodAccomplishCount = cacheService.getString(recommGoodAccomplishKey);
		logger.info("dealGoodAccomplish query from memcache key is: " + recommGoodAccomplishKey + " , result is: " + recommendGoodAccomplishCount);
		if (StringUtils.isEmpty(recommendGoodAccomplishCount)) {
			recommendGoodAccomplishCount = "0";
		}
		int recommGoodAccomplishNewCount = Integer.valueOf(recommendGoodAccomplishCount) + 1;
		cacheService.setString(recommGoodAccomplishKey, recommGoodAccomplishNewCount + "", Constant.ONE_DAY);
		if (recommGoodAccomplishNewCount >= stopRecommendNum) {
			String recommForbidUserIdsKey = Constant.RECOMMEND_FORBIDDEN_USERIDS_KEY + TimeUtil.formatDate_(new Date());
			// 获取当天被禁止进行精准推荐的用户列表
			List<String> recommForbidUserIds = RedisUtil.getList(recommForbidUserIdsKey);
			logger.info("dealGoodAccomplish query from redis key is: " + recommForbidUserIdsKey + " , result is: " + recommForbidUserIds);
			String goodPublisherUserId = transport.getUserId() + "";
			if (recommForbidUserIds == null || recommForbidUserIds.size() == 0) {
				recommForbidUserIds = new ArrayList<String>();
				recommForbidUserIds.add(goodPublisherUserId);
			} else {
				if (!recommForbidUserIds.contains(goodPublisherUserId)) {
					recommForbidUserIds.add(goodPublisherUserId);
				}
			}
			RedisUtil.setList(recommForbidUserIdsKey, recommForbidUserIds, Constant.ONE_DAY);
		}
	}

	private void dealDestFreightDifficult(String carId, Transport transport) {
		// recommend_freight_difficult_${carId}_${date}
		String freightDifficultKey = queryKeyFromConfig(Constant.RECOMMEND_FREIGHT_DIFFICULT_KEY, "recommend_freight_difficult_${carId}_${date}", new String[] { "${carId}", "${date}" }, new String[] { carId, TimeUtil.formatDate_(new Date()) });
		// 获取该车上报的类型不符的信息
		List<String> freightDifficultTypes = RedisUtil.getList(freightDifficultKey);
		String destination = fetchDestination(transport);
		logger.info("dealDestinationFreightDifficult query from redis by key: " + freightDifficultKey + ", result is:" + freightDifficultTypes + ", feedback transport destination is: " + destination);
		/*
		 * 如果已有上报信息则增加并保存否则新建并保存
		 */
		if (freightDifficultTypes == null || freightDifficultTypes.size() == 0) {
			freightDifficultTypes = new ArrayList<String>();
			freightDifficultTypes.add(destination);
		} else {
			// 如果已经上报过则不需要保存
			if (!freightDifficultTypes.contains(destination)) {
				freightDifficultTypes.add(destination);
			}
		}
		RedisUtil.setList(freightDifficultKey, freightDifficultTypes, Constant.ONE_DAY);

	}

	private String fetchDestination(Transport transport) {
		String destination = null;
		if (StringUtils.isNotEmpty(transport.getDestArea())) {
			destination = transport.getDestArea();
		} else if (StringUtils.isNotEmpty(transport.getDestCity())) {
			destination = transport.getDestCity();
		} else if (StringUtils.isNotEmpty(transport.getDestProvinc())) {
			destination = transport.getDestProvinc();
		}
		return destination;
	}

	private void dealGoodTypeNotMatches(String carId, Transport transport) {
		// recommend_type_not_matche_${carId}_${date}
		String carNotMatcheTypesKey = queryKeyFromConfig(Constant.RECOMMEND_TYPE_NOT_MATCHE_KEY, "recommend_type_not_matche_${carId}_${date}", new String[] { "${carId}", "${date}" }, new String[] { carId, TimeUtil.formatDate_(new Date()) });
		// 获取该车上报的类型不符的信息
		List<String> carNotMatcheTypes = RedisUtil.getList(carNotMatcheTypesKey);
		logger.info("dealGoodTypeNotMatches query from redis by key: " + carNotMatcheTypesKey + ", result is:" + carNotMatcheTypes);
		String machineType = transport.getGoodTypeName();
		/*
		 * 如果已有上报信息则增加并保存否则新建并保存
		 */
		if (carNotMatcheTypes == null || carNotMatcheTypes.size() == 0) {
			carNotMatcheTypes = new ArrayList<String>();
			carNotMatcheTypes.add(machineType);
		} else {
			// 如果已经上报过则不需要保存
			if (!carNotMatcheTypes.contains(machineType)) {
				carNotMatcheTypes.add(machineType);
			}
		}
		RedisUtil.setList(carNotMatcheTypesKey, carNotMatcheTypes, Constant.ONE_DAY);
	}

	private String queryFeedbackNameByCode(int curCode) {
		String feedbackName = null;
		/*
		 * 1=价格低#2=空驶距离远#3=货物类型不符#4=目的地拉货难#5=货源不真实#6=路况复杂#7=货已成交
		 */
		switch (curCode) {
		case 1:
			feedbackName = "价格低";
			break;
		case 2:
			feedbackName = "空驶距离远";
			break;
		case 3:
			feedbackName = "货物类型不符";
			break;
		case 4:
			feedbackName = "目的地拉货难";
			break;
		case 5:
			feedbackName = "货源不真实";
			break;
		case 6:
			feedbackName = "路况复杂";
			break;
		case 7:
			feedbackName = "货已成交";
			break;
		}
		return feedbackName;
	}
}
