package com.tytrecommend.recommend.service.impl;

import com.tyt.apiDataUserCreditInfo.service.ApiDataUserCreditInfoService;
import com.tyt.callPhoneRecord.bean.CallPhoneRecordVo;
import com.tyt.model.ApiDataUserCreditInfoTwo;
import com.tyt.model.CallPhoneRecord;
import com.tytrecommend.recommend.service.UserCreditInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName UserCreditInfoServiceImpl
 * @Description TODO
 * <AUTHOR> Lion
 * @Date 2022/6/29 15:06
 * @Verdion 1.0
 **/
@Service("usercreditinfoservice")
public class UserCreditInfoServiceImpl implements UserCreditInfoService {

    @Resource(name = "apiDataUserCreditInfoService")
    private ApiDataUserCreditInfoService apiDataUserCreditInfoService;

    @Override
    public void getIncreaseVo(CallPhoneRecord callPhoneRecord, List<CallPhoneRecordVo> callPhoneRecordVos) {
        CallPhoneRecordVo callPhoneRecordVo = new CallPhoneRecordVo();
        BeanUtils.copyProperties(callPhoneRecord,callPhoneRecordVo);
        ApiDataUserCreditInfoTwo userCreditInfo = apiDataUserCreditInfoService.getById(callPhoneRecord.getCarUserId());
        if (userCreditInfo!=null) {
            if (null != userCreditInfo.getCarTotalServerScore()) {
                callPhoneRecordVo.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
            }
            if (null != userCreditInfo.getCarServerRankScore()) {
                callPhoneRecordVo.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
            }
        }
        callPhoneRecordVos.add(callPhoneRecordVo);
    }
}
