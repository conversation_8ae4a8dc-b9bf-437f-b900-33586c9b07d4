package com.tytrecommend.recommend.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tytrecommend.base.dao.BaseDao;
import com.tytrecommend.base.service.BaseServiceImpl;
import com.tytrecommend.model.TytPreferenceHistory;
import com.tytrecommend.recommend.service.PreferHistoryService;

@Service("preferHistoryService")
public class PreferHistoryServiceImpl extends BaseServiceImpl<TytPreferenceHistory, Long> implements
		PreferHistoryService {
	@Resource(name="preferHistoryDao")
	public void setBaseDao(BaseDao<TytPreferenceHistory, Long> preferHistoryDao){
		super.setBaseDao(preferHistoryDao);
	}
	
}
