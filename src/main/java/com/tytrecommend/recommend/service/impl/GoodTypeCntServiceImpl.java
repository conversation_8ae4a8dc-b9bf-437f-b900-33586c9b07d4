package com.tytrecommend.recommend.service.impl;

import com.tyt.plat.mapper.recommend.GoodTypeCntMapper;
import com.tytrecommend.recommend.service.GoodTypeCntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 车主货物类型履约量
 *
 * <AUTHOR>
 * @since 2024-12-02 13:26
 */
@Service("goodTypeCntService")
public class GoodTypeCntServiceImpl implements GoodTypeCntService {
    @Autowired
    private GoodTypeCntMapper goodTypeCntMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public Integer getGoodsTypeCnt(Long userId, String goodTypeName) {
        return goodTypeCntMapper.getGoodsTypeCnt(userId, goodTypeName);
    }
}
