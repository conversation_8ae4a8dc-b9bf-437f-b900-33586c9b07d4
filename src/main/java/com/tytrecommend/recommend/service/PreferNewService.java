package com.tytrecommend.recommend.service;

import com.tytrecommend.base.service.BaseService;
import com.tytrecommend.model.TytPreferenceNew;

public interface PreferNewService extends BaseService<TytPreferenceNew, Long> {

	TytPreferenceNew newFindPreferenceByCarId(Long id);

	/**
	 * 更新出发地或者是目的地
	 * 
	 * @param id
	 * @param provinc
	 * @param city
	 * @param area
	 * @param isUpdateType
	 *            1 出发地信息 2目的地信息
	 */
	void updatePoint(String id, String provinc, String city, String area, String isUpdateType);

	/**
	 * 
	 * @param id
	 * @param beginWeight
	 * @param endWeight
	 * @param length
	 * @param wide
	 * @param high
	 * @param goodType
	 */
	void updateSize(String id, String beginWeight, String endWeight, String length, String wide, String high, String goodType);

	/**
	 * 
	 * @param carId
	 *            车辆id
	 * @param userId
	 *            车辆所属用户id
	 */
	void saveIfNOtExits(String carId, Long userId);

}
