package com.tytrecommend.recommend.dao.impl;

import java.math.BigInteger;
import java.util.List;


import org.springframework.stereotype.Repository;

import com.tytrecommend.base.dao.BaseDaoImpl;
import com.tytrecommend.recommend.dao.PreferCarDao;
import com.tytrecommend.model.TytPreferenceCar;
@Repository("preferCarDao")
public class PreferCarDaoImpl extends BaseDaoImpl<TytPreferenceCar, Long>  implements  PreferCarDao  {
	public PreferCarDaoImpl(){
		this.setEntityClass(TytPreferenceCar.class);
	}
	public List<TytPreferenceCar> getPreferCar(Long preId) {
		List<TytPreferenceCar> preferCars  = this.find("from TytPreferenceCar where preId=?", new Object[]{preId});
		return preferCars;
	}
	public List<TytPreferenceCar> getPreferCarByCarId(BigInteger carId) {
		TytPreferenceCar tytCar= new TytPreferenceCar();
		tytCar.setCarId(Long.parseLong(carId.toString()));
		List<TytPreferenceCar> preferCars =  this.search(tytCar);
		return preferCars;
	}
}
