package com.tytrecommend.recommend.dao.impl;

import com.tytrecommend.base.dao.BaseDaoImpl;
import com.tytrecommend.model.NewIdentity;
import com.tytrecommend.recommend.dao.NewIdentityDao;
import org.springframework.stereotype.Repository;

/**
 * bi身份信息
 * <AUTHOR>
 * @since 2024-05-30 20:09
 */
@Repository("newIdentityDao")
public class NewIdentityDaoImpl extends BaseDaoImpl<NewIdentity, Long> implements NewIdentityDao {
    public NewIdentityDaoImpl() {
        this.setEntityClass(NewIdentity.class);
    }
}
