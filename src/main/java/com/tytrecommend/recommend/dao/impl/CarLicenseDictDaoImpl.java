package com.tytrecommend.recommend.dao.impl;

import org.springframework.stereotype.Repository;

import com.tytrecommend.base.dao.BaseDaoImpl;
import com.tytrecommend.model.CarLicenseDict;
import com.tytrecommend.recommend.dao.CarLicenseDictDao;


@Repository("carLicenseDictDao")
public class CarLicenseDictDaoImpl extends BaseDaoImpl<CarLicenseDict, Long> implements CarLicenseDictDao {
	public CarLicenseDictDaoImpl(){
		this.setEntityClass(CarLicenseDict.class);
	}

	
}
