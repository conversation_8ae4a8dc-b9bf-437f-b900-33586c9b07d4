package com.tytrecommend.recommend.bean;

import java.math.BigInteger;

public class PreferenceQueryBean {
	BigInteger   id; //偏好ID 
	String startProvinc; //出发地省份
	String startCity; // 出发地城市
	String startArea; //  出发地地区
	String destProvinc; // 目的地省份
	String destCity; //  目的地城市
	String destArea; //目的地地区
	Integer beginWeight; // 最少货重
	Integer endWeight; //最大货重
	String length; // 货物长度
	String wide; // 货物宽度
	String high; // 货物高度
	String goodType; // 货物类型
	BigInteger carId; //车辆ID
	String md5;
	String isUpdate;
	public BigInteger getId() {
		return id;
	}
	public void setId(BigInteger id) {
		this.id = id;
	}
	public String getStartProvinc() {
		return startProvinc;
	}
	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}
	public String getStartCity() {
		return startCity;
	}
	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}
	public String getStartArea() {
		return startArea;
	}
	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}
	public String getDestProvinc() {
		return destProvinc;
	}
	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}
	public String getDestCity() {
		return destCity;
	}
	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}
	public String getDestArea() {
		return destArea;
	}
	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}
	public Integer getBeginWeight() {
		return beginWeight;
	}
	public void setBeginWeight(Integer beginWeight) {
		this.beginWeight = beginWeight;
	}
	public Integer getEndWeight() {
		return endWeight;
	}
	public void setEndWeight(Integer endWeight) {
		this.endWeight = endWeight;
	}
	public String getLength() {
		return length;
	}
	public void setLength(String length) {
		this.length = length;
	}
	public String getWide() {
		return wide;
	}
	public void setWide(String wide) {
		this.wide = wide;
	}
	public String getHigh() {
		return high;
	}
	public void setHigh(String high) {
		this.high = high;
	}
	public String getGoodType() {
		return goodType;
	}
	public void setGoodType(String goodType) {
		this.goodType = goodType;
	}
	public BigInteger getCarId() {
		return carId;
	}
	public void setCarId(BigInteger carId) {
		this.carId = carId;
	}
	public String getMd5() {
		return md5;
	}
	public void setMd5(String md5) {
		this.md5 = md5;
	}
	public String getIsUpdate() {
		return isUpdate;
	}
	public void setIsUpdate(String isUpdate) {
		this.isUpdate = isUpdate;
	}
	
	

}
