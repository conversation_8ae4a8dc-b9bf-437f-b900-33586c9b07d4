package com.tytrecommend.recommend.bean;

import java.util.Date;


import com.fasterxml.jackson.annotation.JsonInclude;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserRecommendBean implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long rdUserId;
	private Long userId;
	private Long carId;
	private Long tsId;
	private String taskContent;
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private String startCoord;
	private String destCoord;
	private String startDetailAdd;
	private String destDetailAdd;
	private Integer androidDistance;
	private Integer iosDistance;
	private Integer verifyPhotoSign;
	private Date pubDate;
	private String isInfoFee;
	private Date releaseTime;
	private Date regTime;
	private String nickName;
	private String price;
	private Date ctime;
	private Integer carIndex;
	private Integer matching;
	private Integer invalidTime;
	private Date endTime;
	
	
	public Long getId() {
		return id;
	}
	public Long getRdUserId() {
		return rdUserId;
	}
	public Long getUserId() {
		return userId;
	}
	public Long getCarId() {
		return carId;
	}
	public Long getTsId() {
		return tsId;
	}
	public String getTaskContent() {
		return taskContent;
	}
	public String getStartProvinc() {
		return startProvinc;
	}
	public String getStartCity() {
		return startCity;
	}
	public String getStartArea() {
		return startArea;
	}
	public String getDestProvinc() {
		return destProvinc;
	}
	public String getDestCity() {
		return destCity;
	}
	public String getDestArea() {
		return destArea;
	}
	public String getStartCoord() {
		return startCoord;
	}
	public String getDestCoord() {
		return destCoord;
	}
	public String getStartDetailAdd() {
		return startDetailAdd;
	}
	public String getDestDetailAdd() {
		return destDetailAdd;
	}
	public Integer getAndroidDistance() {
		return androidDistance;
	}
	public Integer getIosDistance() {
		return iosDistance;
	}
	public Integer getVerifyPhotoSign() {
		return verifyPhotoSign;
	}
	public Date getPubDate() {
		return pubDate;
	}
	public String getIsInfoFee() {
		return isInfoFee;
	}
	public Date getReleaseTime() {
		return releaseTime;
	}
	public Date getRegTime() {
		return regTime;
	}
	public String getNickName() {
		return nickName;
	}
	public String getPrice() {
		return price;
	}
	public Date getCtime() {
		return ctime;
	}
	public Integer getCarIndex() {
		return carIndex;
	}
	public Integer getMatching() {
		return matching;
	}
	public Integer getInvalidTime() {
		return invalidTime;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public void setRdUserId(Long rdUserId) {
		this.rdUserId = rdUserId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public void setCarId(Long carId) {
		this.carId = carId;
	}
	public void setTsId(Long tsId) {
		this.tsId = tsId;
	}
	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}
	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}
	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}
	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}
	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}
	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}
	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}
	public void setStartCoord(String startCoord) {
		this.startCoord = startCoord;
	}
	public void setDestCoord(String destCoord) {
		this.destCoord = destCoord;
	}
	public void setStartDetailAdd(String startDetailAdd) {
		this.startDetailAdd = startDetailAdd;
	}
	public void setDestDetailAdd(String destDetailAdd) {
		this.destDetailAdd = destDetailAdd;
	}
	public void setAndroidDistance(Integer androidDistance) {
		this.androidDistance = androidDistance;
	}
	public void setIosDistance(Integer iosDistance) {
		this.iosDistance = iosDistance;
	}
	public void setVerifyPhotoSign(Integer verifyPhotoSign) {
		this.verifyPhotoSign = verifyPhotoSign;
	}
	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}
	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}
	public void setReleaseTime(Date releaseTime) {
		this.releaseTime = releaseTime;
	}
	public void setRegTime(Date regTime) {
		this.regTime = regTime;
	}
	public void setNickName(String nickName) {
		this.nickName = nickName;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	public void setCarIndex(Integer carIndex) {
		this.carIndex = carIndex;
	}
	public void setMatching(Integer matching) {
		this.matching = matching;
	}
	public void setInvalidTime(Integer invalidTime) {
		this.invalidTime = invalidTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}



}