package com.tytrecommend.recommend.bean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;


/**
 *
 * <AUTHOR>
 * @date 2017-7-20下午5:19:28
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GuessLikeResultBean {
	private Long id;
	private Long userId;
	// 出发地 目的地 省市区
	private String startProvinc;
	private String startCity;
	private String startArea;
	private String destProvinc;
	private String destCity;
	private String destArea;
	private String taskContent;
	private Integer status;
	private Integer verifyFlag;
	private String price;
	private String startDetailAdd;
	private String destDetailAdd;
	private Date pubDate;
	private Date ctime;
	private String remark;
	private String isInfoFee;
	private String nickName;
	private Date regTime;
	private String type;
	private String brand;
	private String goodTypeName;
	private String distance;
	private Integer androidDistance;
	private Integer iosDistance;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getAndroidDistance() {
		return androidDistance;
	}

	public void setAndroidDistance(Integer androidDistance) {
		this.androidDistance = androidDistance;
	}

	public Integer getIosDistance() {
		return iosDistance;
	}

	public void setIosDistance(Integer iosDistance) {
		this.iosDistance = iosDistance;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getStartProvinc() {
		return startProvinc;
	}

	public void setStartProvinc(String startProvinc) {
		this.startProvinc = startProvinc;
	}

	public String getStartCity() {
		return startCity;
	}

	public void setStartCity(String startCity) {
		this.startCity = startCity;
	}

	public String getStartArea() {
		return startArea;
	}

	public void setStartArea(String startArea) {
		this.startArea = startArea;
	}

	public String getDestProvinc() {
		return destProvinc;
	}

	public void setDestProvinc(String destProvinc) {
		this.destProvinc = destProvinc;
	}

	public String getDestCity() {
		return destCity;
	}

	public void setDestCity(String destCity) {
		this.destCity = destCity;
	}

	public String getDestArea() {
		return destArea;
	}

	public void setDestArea(String destArea) {
		this.destArea = destArea;
	}

	public String getTaskContent() {
		return taskContent;
	}

	public void setTaskContent(String taskContent) {
		this.taskContent = taskContent;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getVerifyFlag() {
		return verifyFlag;
	}

	public void setVerifyFlag(Integer verifyFlag) {
		this.verifyFlag = verifyFlag;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getStartDetailAdd() {
		return startDetailAdd;
	}

	public void setStartDetailAdd(String startDetailAdd) {
		this.startDetailAdd = startDetailAdd;
	}

	public String getDestDetailAdd() {
		return destDetailAdd;
	}

	public void setDestDetailAdd(String destDetailAdd) {
		this.destDetailAdd = destDetailAdd;
	}

	public Date getPubDate() {
		return pubDate;
	}

	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getIsInfoFee() {
		return isInfoFee;
	}

	public void setIsInfoFee(String isInfoFee) {
		this.isInfoFee = isInfoFee;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public Date getRegTime() {
		return regTime;
	}

	public void setRegTime(Date regTime) {
		this.regTime = regTime;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getGoodTypeName() {
		return goodTypeName;
	}

	public void setGoodTypeName(String goodTypeName) {
		this.goodTypeName = goodTypeName;
	}

	public String getDistance() {
		return distance;
	}

	public void setDistance(String distance) {
		this.distance = distance;
	}

	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}
	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
