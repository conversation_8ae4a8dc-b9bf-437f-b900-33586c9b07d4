package com.tytrecommend.recommend.controller;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;


















import com.alibaba.fastjson.JSON;
import com.tyt.common.service.TytMqRecommMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqGuessLikeMsg;
import com.tyt.model.Car;
import com.tyt.model.TytGeoDict;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.CarService;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import com.tytrecommend.base.controller.BaseController;
import com.tytrecommend.model.TytPreference;
import com.tytrecommend.model.TytPreferenceCar;
import com.tytrecommend.recommend.bean.MqMsgCacheInitBean;
import com.tytrecommend.recommend.business.TytGeoDictService;
import com.tytrecommend.recommend.service.PreferService;


/**
 * 
 * <AUTHOR>
 * @date 2017年7月21日
 */

@Controller
@RequestMapping("/plat/car/prefer")
public class PreferController extends BaseController {
	
	@Resource(name = "tytGeoDictBusiness")
	private TytGeoDictService tytGeoDictBusiness;
	
	@Resource(name = "preferService")
	private PreferService preferService;
	
	@Resource(name = "carService")
	private CarService carService;
	
	@Resource(name = "tytConfigService")
	private TytConfigService tytConfigService;
	
	@Resource(name="tytMqRecommMessService")
	private TytMqRecommMessageService tytMqRecommMessService;
	/**
	 * 设置偏好
	 * 
	 */
	@RequestMapping(value="configPrefer")
	public void configPrefer(HttpServletRequest request, HttpServletResponse response){
		try {
			/* 参数解析 */
			Map<String, String> params = parseRequestParams(request);
			Long userId = Long.parseLong(params.get("userId"));
			Long carId = Long.parseLong(params.get("id"));
			String condition = "userId_" + userId + "车辆偏好设置 ";
			/* 业务参数验证 */
			@SuppressWarnings("serial")
			List<String> names = new ArrayList<String>() {
				{
					add("id");
					add("userId");
				}
			};
			boolean isFlag = false;
			if (!validateArguments(condition, request, response, params, names))
				return;
			Car car = carService.newGetbyId(carId);
			//验证是否已经设置了偏好
			TytPreference count =preferService.newFindPreferenceByCarId(carId);
			if(count!=null){
				//设置了偏好 更新
				isFlag =preferService.updateTytPreference(params,count);
			}else{
				//创建偏好设置对象
				TytPreference tytPre = createTytPreference(params,car);
				List<TytPreferenceCar> tytPreCars = createTytPreferenceCar(params);
				isFlag = preferService.saveTytPreference(tytPre,tytPreCars);
			}
			if("1".equals(car.getFindGoodOnOff())&&isFlag){
				// 清除缓存偏好设置  mq   发送清空猜你喜欢列表Mq	
				MqMsgCacheInitBean mqMsgCacheInitBean = new MqMsgCacheInitBean();
				mqMsgCacheInitBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());	
				mqMsgCacheInitBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_CAR_CACHE_MESSAGE);
				mqMsgCacheInitBean.setCarId(carId);
				mqMsgCacheInitBean.setUserId(userId);
				tytMqRecommMessService.addSaveMqMessage(mqMsgCacheInitBean.getMessageSerailNum(), JSON.toJSONString(mqMsgCacheInitBean), MqBaseMessageBean.MESSAGETYPE_CAR_CACHE_MESSAGE);
				tytMqRecommMessService.sendMqMessageWithResult(mqMsgCacheInitBean.getMessageSerailNum(), JSON.toJSONString(mqMsgCacheInitBean), MqBaseMessageBean.MESSAGETYPE_CAR_CACHE_MESSAGE);
				//清猜你喜欢列表  清除猜你喜欢缓存
				String guessLikeRedisKey = tytConfigService.getStringValue(Constant.RECOMMEND_GUESS_LIKE_PREFIX,"recommend_car_");
		        guessLikeRedisKey = guessLikeRedisKey.concat(carId+"").concat("_").concat(TimeUtil.formatDate_(new Date()));
		        RedisUtil.del(guessLikeRedisKey);
		        RedisUtil.del("recommend_guess_like_maxid_"+carId);
		        
				MqGuessLikeMsg mqFindGoodOnOffMessageBean=new MqGuessLikeMsg();
				mqFindGoodOnOffMessageBean.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
				mqFindGoodOnOffMessageBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_RECOMMEND_TRANSPORT_FINDGOOD_MESSAGE);
				mqFindGoodOnOffMessageBean.setMethodType(0);
				String md5 = preferService.createMD5String(params);
				mqFindGoodOnOffMessageBean.setMd5(md5);
				mqFindGoodOnOffMessageBean.setCarId(carId);
				Integer delayedTime = tytConfigService.getIntValue(Constant.CACHE_RECOMMEND_MQDELAYEDSENDTIME_KEY);
				long delayedTimes = System.currentTimeMillis()+delayedTime;
				tytMqRecommMessService.addSaveMqMessage(mqFindGoodOnOffMessageBean.getMessageSerailNum(), JSON.toJSONString(mqFindGoodOnOffMessageBean), MqBaseMessageBean.MESSAGETYPE_RECOMMEND_TRANSPORT_FINDGOOD_MESSAGE,1,delayedTimes);
				boolean isSuccess = tytMqRecommMessService.sendTimerMqMessageWithResult(mqFindGoodOnOffMessageBean.getMessageSerailNum(), JSON.toJSONString(mqFindGoodOnOffMessageBean),
						MqBaseMessageBean.MESSAGETYPE_RECOMMEND_TRANSPORT_FINDGOOD_MESSAGE,delayedTimes);
				if(isSuccess){
					preferService.updateIsUpdate(carId);
				}
			}
			backResponse(request, response, ReturnCodeConstant.OK, "车辆偏好设置成功", null, 0);
			logger.info(condition + "偏好设置成功");
		} catch (Exception e) {
			e.printStackTrace();
			backResponse(request, response, ReturnCodeConstant.ERROR, "服务器错误", null, 0);
		}
	}
	private List<TytPreferenceCar> createTytPreferenceCar(Map<String, String> params) {
		if(StringUtils.isEmpty(params.get("goodType"))){
			return null;
		}
		List<TytPreferenceCar> tytPreferenceCars = new ArrayList<TytPreferenceCar>();
		String[] goodTypes = params.get("goodType").split("、");
		for(String preCarName : goodTypes){
			TytPreferenceCar tytPreferenceCar = new TytPreferenceCar();
			tytPreferenceCar.setCarId(Long.valueOf(params.get("id")));
			tytPreferenceCar.setCarName(preCarName);
			tytPreferenceCar.setUserId(Long.valueOf(params.get("userId")));
			tytPreferenceCar.setCtime(new Date());
			tytPreferenceCars.add(tytPreferenceCar);
		}
		
		return tytPreferenceCars;
	}
	/**
	 * 创建偏好对象
	 * @param params
	 * @param car 
	 * @return
	 */
	private TytPreference createTytPreference(Map<String, String> params, Car car) {
		TytPreference tytPreference = new TytPreference();
		tytPreference.setCarId(Long.valueOf(params.get("id")));
		tytPreference.setUserId(Long.valueOf(params.get("userId")));
		tytPreference.setDestArea(StringUtils.isBlank(params.get("destArea"))?null:params.get("destArea"));
		tytPreference.setDestCity(StringUtils.isBlank(params.get("destCity"))?null:params.get("destCity"));
		tytPreference.setDestProvinc(StringUtils.isBlank(params.get("destProvinc"))?null:params.get("destProvinc"));
		tytPreference.setStartArea(StringUtils.isBlank(params.get("startArea"))?null:params.get("startArea"));
		tytPreference.setStartCity(StringUtils.isBlank(params.get("startCity"))?null:params.get("startCity"));
		tytPreference.setStartProvinc(StringUtils.isBlank(params.get("startProvinc"))?null:params.get("startProvinc"));
		if(StringUtils.isNotEmpty(params.get("length")))
			tytPreference.setLength((int) (Double.valueOf(params.get("length"))*100));
		else 
			tytPreference.setLength(null);
		if(StringUtils.isNotEmpty(params.get("wide")))
			tytPreference.setWide((int) (Double.valueOf(params.get("wide"))*100));
		else tytPreference.setWide(null);
		if(StringUtils.isNotEmpty(params.get("high")))
			tytPreference.setHigh((int) (Double.valueOf(params.get("high"))*100));
		else tytPreference.setHigh(null);
		if(StringUtils.isNotEmpty(params.get("endWeight")))
			tytPreference.setEndWeight((int) (Double.valueOf(params.get("endWeight"))*100));
		else tytPreference.setEndWeight(null);
		if(StringUtils.isNotEmpty(params.get("beginWeight")))
			tytPreference.setBeginWeight((int) (Double.valueOf(params.get("beginWeight"))*100));
		else tytPreference.setBeginWeight(null);
		//优化 新增
		tytPreference.setPreferenceCar(params.get("goodType"));
		if(StringUtils.isNotEmpty(car.getFindGoodOnOff()))
			tytPreference.setFindGoodOnoff(Integer.valueOf(car.getFindGoodOnOff()));
		tytPreference.setOnLineTime(new Date());
		//设置出发地和目的地的坐标
		if((params.get("startProvinc")!=null&&!"".equals(params.get("startProvinc")))&&
				(params.get("startCity")!=null&&!"".equals(params.get("startCity")))){
			TytGeoDict tytGeoDictStart = tytGeoDictBusiness.getTytGeoDict(params.get("startProvinc"),params.get("startCity"),params.get("startArea"));
			tytPreference.setStartCoordX(Integer.valueOf(tytGeoDictStart.getPx().toString()));
			tytPreference.setStartCoordY(Integer.valueOf(tytGeoDictStart.getPy().toString()));
		}else{
			tytPreference.setStartCoordX(null);
			tytPreference.setStartCoordY(null);
		}
		if((params.get("destProvinc")!=null&&!"".equals(params.get("destProvinc")))&&
				(params.get("destCity")!=null&&!"".equals(params.get("destCity")))){
			TytGeoDict tytGeoDictDest = tytGeoDictBusiness.getTytGeoDict(params.get("destProvinc"),params.get("destCity"),params.get("destArea"));
			tytPreference.setDestCoordX(Integer.valueOf(tytGeoDictDest.getPx().toString()));
			tytPreference.setDestCoordY(Integer.valueOf(tytGeoDictDest.getPy().toString()));
		}else{
			tytPreference.setDestCoordX(null);
			tytPreference.setDestCoordX(null);
		}
		//设置md5 唯一值验证
		String md5String = preferService.createMD5String(params);
		tytPreference.setMd5(md5String);
		tytPreference.setIsUpdate(Long.valueOf("1"));
		tytPreference.setStatus(Long.valueOf("0"));
		tytPreference.setCtime(new Date());
		tytPreference.setUtime(new Date());
		
		
		return tytPreference;
	}
	
	/**
	 * 修改偏好设置
	 * 
	 */
	@RequestMapping(value="updatePrefer")
	public void updatePrefer(HttpServletRequest request, HttpServletResponse response){
		
	}
	

}
