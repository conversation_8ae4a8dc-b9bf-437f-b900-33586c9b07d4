package com.tytrecommend.recommend.controller;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.ReturnCodeConstant;
import com.tytrecommend.base.controller.BaseController;
import com.tytrecommend.recommend.service.PreferNewService;

/**
 * 
 * <AUTHOR>
 * @date 2017年7月21日
 */

@Controller
@RequestMapping("/plat/car/preferNew")
public class PreferNewController extends BaseController {

	@Resource(name = "preferNewService")
	private PreferNewService preferNewService;

	/**
	 * 更新偏好的出发地或目的地信息
	 * 
	 * @param baseParameter
	 * @param id
	 *            偏好的车辆id
	 * @param provinc
	 *            省
	 * @param city
	 *            市
	 * @param area
	 *            县/区
	 * @param isUpdateType
	 *            更新的出发地还是目的地 1 出发地信息 2目的地信息
	 * @return
	 */
	@RequestMapping(value = "/setPoint")
	@ResponseBody
	public ResultMsgBean setPoint(BaseParameter baseParameter, String id, String provinc, String city, String area, String isUpdateType) {
		logger.info("car preferNew setPoint id is: " + id + " , province is: " + provinc + " , city is: " + city + " , area is: " + area + " , isUpdateType is: " + isUpdateType);
		ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "");
		try {
			if (StringUtils.isEmpty(id)) {
				rm.setMsg("id不能为空");
			} else if (StringUtils.isEmpty(provinc) && "1".equals(isUpdateType)) {
				rm.setMsg("provinc不能为空");
			} else if (StringUtils.isEmpty(city) && "1".equals(isUpdateType)) {
				rm.setMsg("city不能为空");
			} else if (StringUtils.isEmpty(area) && "1".equals(isUpdateType)) {
				rm.setMsg("area不能为空");
			} else if (StringUtils.isEmpty(isUpdateType) || !("1".equals(isUpdateType) || "2".equals(isUpdateType))) {
				rm.setMsg("isUpdateType不能为空并且只能为1或2");
			} else {
				logger.info("car preferNew get redis lock begin carId is: " + id);
				if (LockUtil.lockObject("1", Constant.LOCK_CAR_PRE_BASE + id, 10)) {
					// 如果当前carId数据还不存在则保存一条
					preferNewService.saveIfNOtExits(id, baseParameter.getUserId());
				}
				preferNewService.updatePoint(id, provinc, city, area, isUpdateType);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("车辆偏好设置成功");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		} finally {
			logger.info("car preferNew release redis lock carId is: " + id);
			LockUtil.unLockObject("1", Constant.LOCK_CAR_PRE_BASE + id);
		}
		logger.info("car preferNew setPoint result is: " + rm);
		return rm;
	}

	/**
	 * 
	 * @param baseParameter
	 * @param id
	 * @param beginWeight
	 * @param endWeight
	 * @param length
	 * @param wide
	 * @param high
	 * @param goodType
	 * @return
	 */
	@RequestMapping(value = "/setSize")
	@ResponseBody
	public ResultMsgBean setSize(BaseParameter baseParameter, String id, String beginWeight, String endWeight, String length, String wide, String high, String goodType) {
		logger.info("car preferNew setSize id is: " + id + " , beginWeight is: " + beginWeight + " , endWeight is: " + endWeight + " , length is: " + length + " , wide is: " + wide + " , high is: " + high + " , goodType is: " + goodType);
		ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "");
		try {
			if (StringUtils.isEmpty(id)) {
				rm.setMsg("id不能为空");
			} else {
				logger.info("car preferNew get redis lock begin carId is: " + id);
				if (LockUtil.lockObject("1", Constant.LOCK_CAR_PRE_BASE + id, 10)) {
					logger.info("car preferNew get redis lock success carId is: " + id);
					// 如果当前carId数据还不存在则保存一条
					preferNewService.saveIfNOtExits(id, baseParameter.getUserId());
				}
				preferNewService.updateSize(id, beginWeight, endWeight, length, wide, high, goodType);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("车辆偏好设置成功");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		} finally {
			logger.info("car preferNew release redis lock carId is: " + id);
			LockUtil.unLockObject("1", Constant.LOCK_CAR_PRE_BASE + id);
		}
		logger.info("car preferNew setSize result is: " + rm);
		return rm;
	}
}
