package com.tytrecommend.recommend.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.ReturnCodeConstant;
import com.tytrecommend.model.CreditDeductLog;
import com.tytrecommend.recommend.service.CreditDeductLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping("/plat/user/credit")
public class CreditDeductController extends BaseController {

	public Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource(name = "creditDeductLogService")
	private CreditDeductLogService creditDeductLogService;

	/**
	 * 查询用户信用分扣除记录
	 *
	 * 接口wiki：http://192.168.2.20:3300/project/37/interface/api/10599
	 *
	 * @param userType
	 * @param queryUserId
	 * @return
	 */
	@RequestMapping(value = "/getCreditDeduct", method = RequestMethod.GET)
	@ResponseBody
	public ResultMsgBean getCreditDeduct(Integer userType,Long queryUserId,Long queryTime) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
		try {
			if (userType==null||queryUserId==null) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				resultMsgBean.setMsg(userType==null?"userType不能为空":"queryUserId不能为空");
			} else if (userType!=1&&userType!=2) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_ERROR_CODE);
				resultMsgBean.setMsg("用户类型不合法");
			} else {
				List<CreditDeductLog> list = creditDeductLogService.getListByUserId(queryUserId,userType,queryTime);
				resultMsgBean.setData(list);
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("失败");
		}
		return resultMsgBean;
	}

}
