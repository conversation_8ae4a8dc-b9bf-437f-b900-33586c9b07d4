package com.tytrecommend.recommend.controller;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.alibaba.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqGuessLikeMsg;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.ReturnCodeConstant;
import com.tyt.util.SerialNumUtil;
import com.tytrecommend.model.TytUserRecommend;
import com.tytrecommend.recommend.bean.GuessLikeResultBean;
import com.tytrecommend.recommend.service.UserRecommendService;

/**
 * 
 * <AUTHOR>
 * @date 2017-7-20上午10:08:05
 * @description
 */
@Controller
@RequestMapping("/plat/recommend")
public class RecommendController extends BaseController {
	@Resource(name = "userRecommendService")
	private UserRecommendService userRecommendService;
	@Resource(name = "tytMqMessageService")
	private TytMqMessageService mqMessageService;

	@Resource(name = "tytConfigService")
	private TytConfigService configService;
	private static final String ACCURATETRANSPORTLISTSIZE = "accurateTransportListSize";
	private static final String GUESS_YOU_LIKE_COUNT_KEY = "guessYouLikeCount";
	private static final String QUERY_GUESS_LIKE_CAR_KEY = "queryGuessLikeCar";
	private static final String CAR_GUESS_LIKE_CACHE_TIME_KEY = "carGuessLikeCacheTime";

	@RequestMapping(value = "/guessLikeList")
	@ResponseBody
	public ResultMsgBean guessLikeList(String carId, String clientSign) {
		ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "请求成功");
		logger.info("query guess you like list, carId is: " + carId);
		try {
			if (StringUtils.isEmpty(carId)) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				resultMsgBean.setMsg("carId不能为空");
			} else if (StringUtils.isEmpty(clientSign)) {
				resultMsgBean.setCode(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE);
				resultMsgBean.setMsg("clientSign不能为空");
			} else {
				List<GuessLikeResultBean> guessLikeResultBeans = new ArrayList<GuessLikeResultBean>();
				// 获取需要返回猜你喜欢列表条数
				Integer guessYouLikeCount = configService.getIntValue(GUESS_YOU_LIKE_COUNT_KEY);
				if (guessYouLikeCount == null) {
					guessYouLikeCount = 9;
				}
				userRecommendService.queryTransportListByCarId(carId, guessLikeResultBeans, clientSign, guessYouLikeCount);
				resultMsgBean.setData(guessLikeResultBeans);
				if (isNeenSendMq(carId)) {
					// 发送mq消息更新车辆的猜你喜欢列表
					MqGuessLikeMsg guessLikeMsg = new MqGuessLikeMsg();
					guessLikeMsg.setMessageType(MqBaseMessageBean.MESSAGETYPE_GUESS_YOU_LIKE_INCREMENT_MESSAGE);
					String messageSerailNum = SerialNumUtil.generateSeriaNum();
					guessLikeMsg.setMessageSerailNum(messageSerailNum);
					guessLikeMsg.setCarId(Long.valueOf(carId));
					guessLikeMsg.setMethodType(2);
					String messageContent = JSON.toJSONString(guessLikeMsg);
					mqMessageService.addSaveRecommendMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_GUESS_YOU_LIKE_INCREMENT_MESSAGE);
					// 发送mq消息
					mqMessageService.sendRecommendMqMessage(messageSerailNum, messageContent, MqBaseMessageBean.MESSAGETYPE_GUESS_YOU_LIKE_INCREMENT_MESSAGE);
					String carKey = configService.getStringValue(QUERY_GUESS_LIKE_CAR_KEY, "query_guess_like_car_") + carId;
					int cacheTime = configService.getIntValue(CAR_GUESS_LIKE_CACHE_TIME_KEY, 60);
					RedisUtil.set(carKey, System.currentTimeMillis() + "", cacheTime);
				}
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			resultMsgBean.setCode(ReturnCodeConstant.ERROR);
			resultMsgBean.setMsg("服务器错误");
		}
		// 设置当前时间的毫秒值
		resultMsgBean.setTime(System.currentTimeMillis() + "");
//		logger.info("query guess you like list result is: " + resultMsgBean);
		logger.info("query guess you like list is done, carId is: " + carId);
		return resultMsgBean;
	}

	private boolean isNeenSendMq(String carId) {
		/*
		 * 从缓存中查询是否有该车的查询信息，如果有则不发mq消息
		 */
		String carKey = configService.getStringValue(QUERY_GUESS_LIKE_CAR_KEY, "query_guess_like_car_") + carId;
		if (RedisUtil.exists(carKey)) {
			return false;
		} else {
			return true;
		}
	}

	@RequestMapping(value = "/list")
	@ResponseBody
	public ResultMsgBean list(BaseParameter baseParameter, Long carId, Long id) {

		ResultMsgBean rm = new ResultMsgBean();
		try {
			if (null == carId || carId.intValue() == 0) {
				rm.setCode(ReturnCodeConstant.TYPE_ERROR_CODE);
				rm.setMsg("carId不能为空");
				return rm;
			}
			Integer accurateTransportListSize = configService.getIntValue(ACCURATETRANSPORTLISTSIZE);

			List<TytUserRecommend> list = userRecommendService.updateGetAccurateListForCarId(baseParameter.getUserId(), carId, id, accurateTransportListSize);

			rm.setCode(ReturnCodeConstant.OK);
			rm.setMsg("查询成功");
			rm.setData(list);
			rm.setTime(String.valueOf(System.currentTimeMillis()));
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
