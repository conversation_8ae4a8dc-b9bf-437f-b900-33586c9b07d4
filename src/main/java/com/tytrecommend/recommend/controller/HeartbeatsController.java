package com.tytrecommend.recommend.controller;

import com.tyt.base.controller.BaseController;
import com.tyt.cache.CacheService;
import com.tyt.model.ResultMsgBean;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.TytConfigService;
import com.tyt.util.Constant;
import com.tyt.util.ReturnCodeConstant;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 
 * <AUTHOR>
 * @date 2017-07-20
 * @description  心跳服务
 */
@Controller
@RequestMapping("/plat/heartbeats")
public class HeartbeatsController extends BaseController {

    @Resource(name = "cacheServiceMcImpl")
    private CacheService cacheService;

    @Resource(name = "tytConfigService")
    TytConfigService tytConfigService;

    /**
     *  心跳服务维持检测
     *  create by tianjw on 20170720
     */
    @RequestMapping(value="check")
    @ResponseBody
    public ResultMsgBean checkHeartbeats(String userId, HttpServletRequest request, HttpServletResponse response){
        ResultMsgBean rm = new ResultMsgBean();
        if(!StringUtils.hasLength(userId)){
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("刷新心跳失败");
            return rm;
        }
//        //直接根据用户ID重新设置缓存
//        String cacheKey = tytConfigService.getStringValue(Constant.CACHE_RECOMMEND_HEARTBEARTS_KEY);
//        if(!StringUtils.hasLength(cacheKey)){
//            cacheKey = "plat_recommend_heartbeart_";//默认KEY
//        }
//        cacheKey += userId;
//        String cacheTime = tytConfigService.getStringValue("recommendHeartbeatCacheTime");
//        if(!StringUtils.hasLength(cacheTime)){
//            cacheTime = "180";//默认1分钟
//        }
        Long currentSystemMillis = System.currentTimeMillis();
//        RedisUtil.set(cacheKey,""+currentSystemMillis,Integer.valueOf(cacheTime));
        rm.setData(currentSystemMillis);//返回系统毫秒数
        rm.setCode(ReturnCodeConstant.OK);
        rm.setMsg("刷新心跳成功");
        return rm;
    }
}
