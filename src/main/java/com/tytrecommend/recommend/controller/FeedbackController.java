package com.tytrecommend.recommend.controller;

import javax.annotation.Resource;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tyt.base.bean.BaseParameter;
import com.tyt.base.controller.BaseController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TransportService;
import com.tyt.util.ReturnCodeConstant;
import com.tytrecommend.recommend.service.FeedbackService;

/**
 * 
 * <AUTHOR>
 * @date 2017-7-20上午10:08:05
 * @description
 */
@Controller
@RequestMapping("/plat/feedback")
public class FeedbackController extends BaseController {
	@Resource(name = "feedbackService")
	private FeedbackService feedbackService;

	@Resource(name = "transportService")
	private TransportService transportService;

	@Resource(name = "transportMainService")
	private TransportMainService transportMainService;

	/**
	 * 精准货源问题反馈
	 * 
	 * @param baseParameter
	 * @param carId
	 * @param userId
	 * @param tsId
	 * @param code
	 * @return
	 */
	@RequestMapping(value = "/feedback")
	@ResponseBody
	public ResultMsgBean feedback(BaseParameter baseParameter, String carId, String userId, String tsId, String code) {
		logger.info("recommend feedback carId is: " + carId + ", userId is: " + userId + ", tsId is: " + tsId + ", code is: " + code);
		ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.ARGUMENTS_IS_NULL_CODE, "");
		try {
			if (StringUtils.isEmpty(carId)) {
				rm.setMsg("carId不能为空");
			} else if (StringUtils.isEmpty(userId)) {
				rm.setMsg("userId不能为空");
			} else if (StringUtils.isEmpty(tsId)) {
				rm.setMsg("tsId不能为空");
			} else if (StringUtils.isEmpty(code)) {
				rm.setMsg("code不能为空");
			} else {
				// 根据货物id查询货物信息
				TransportMain transportMain = transportMainService.getById(Long.valueOf(tsId));
				Transport transport = new Transport();
				BeanUtils.copyProperties(transport, transportMain);
				feedbackService.saveFeedback(carId, userId, transport, code);
				rm.setCode(ReturnCodeConstant.OK);
				rm.setMsg("操作成功");
			}
		} catch (Exception ex) {
			logger.error("服务器异常", ex);
			rm.setCode(ReturnCodeConstant.ERROR);
			rm.setMsg("服务器错误");
		}
		return rm;
	}
}
