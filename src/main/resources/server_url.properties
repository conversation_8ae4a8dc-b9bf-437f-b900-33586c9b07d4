##### config properties #####

# current profile
active_profile=dev

#tyt database config
jdbc_driverClassName=com.mysql.jdbc.Driver
#note:database connection infomation for test invironment
#tyt_jdbc_url=*********************************************************************************************************
#tyt_jdbc_username=tyt_write
#tyt_jdbc_password=Tyt_write
#tyt_jdbc_maxPoolSize=50
#tyt_jdbc_minPoolSize=5
#tyt_jdbc_maxStatements=10
#tyt_jdbc_initialPoolSize=5
#tyt_jdbc_maxIdleTime=600
#tyt_jdbc_idleConnectionTestPeriod=10

#note:database connection infomation for dev invironment

#tyt_jdbc_url=***********************************************************************************************************
#tyt_jdbc_username=tyt_dev
#tyt_jdbc_password=tyt_dev#20200724

tyt_jdbc_url=***********************************************************************************************************
tyt_jdbc_username=tyt_dev
tyt_jdbc_password=tyt_dev#20200724

#tyt_jdbc_url=***********************************************************************************************************
#tyt_jdbc_username=test_readonly
#tyt_jdbc_password=test_readonly@TE

#tyt_jdbc_url=*********************************************************************************************************
#tyt_jdbc_username=tyt_write
#tyt_jdbc_password=Tyt_write

tyt_jdbc_maxPoolSize=50
tyt_jdbc_minPoolSize=5
tyt_jdbc_maxStatements=10
tyt_jdbc_initialPoolSize=5
tyt_jdbc_maxIdleTime=600
tyt_jdbc_idleConnectionTestPeriod=10

###############################################################
#tytrecommend  database config
#note:database connection infomation for local invironment

#tytrecommend_jdbc_url=*****************************************************************************************************
#tytrecommend_jdbc_username=tyt_dev
#tytrecommend_jdbc_password=tyt_dev#20200724

tytrecommend_jdbc_url=*********************************************************************************************************************
tytrecommend_jdbc_username=tyt_dev
tytrecommend_jdbc_password=tyt_dev#20200724


tytrecommend_jdbc_maxPoolSize=50
tytrecommend_jdbc_minPoolSize=5
tytrecommend_jdbc_maxStatements=10
tytrecommend_jdbc_initialPoolSize=5
tytrecommend_jdbc_maxIdleTime=600
tytrecommend_jdbc_idleConnectionTestPeriod=10

#note:database connection infomation for test invironment
#tytrecommend_jdbc_url=**************************************************************
#tytrecommend_jdbc_username=tyt_plat
#tytrecommend_jdbc_password=tyt_plat
#tytrecommend_jdbc_maxPoolSize=50
#tytrecommend_jdbc_minPoolSize=5
#tytrecommend_jdbc_maxStatements=10
#tytrecommend_jdbc_initialPoolSize=5
#tytrecommend_jdbc_maxIdleTime=600
#tytrecommend_jdbc_idleConnectionTestPeriod=10

#note:database connection infomation for release invironment
#tytrecommend_jdbc_url=**************************************************************
#tytrecommend_jdbc_username=tyt_plat
#tytrecommend_jdbc_password=tyt_plat
#tytrecommend_jdbc_maxPoolSize=50
#tytrecommend_jdbc_minPoolSize=5
#tytrecommend_jdbc_maxStatements=10
#tytrecommend_jdbc_initialPoolSize=5
#tytrecommend_jdbc_maxIdleTime=600
#tytrecommend_jdbc_idleConnectionTestPeriod=10

#note:database connection infomation for online invironment
#tytlog_jdbc_url=************************************************************************************************
#tytlog_jdbc_username=tyt_write
#tytlog_jdbc_password=tyt_write
#tytlog_jdbc_maxPoolSize=100
#tytlog_jdbc_minPoolSize=20
#tytlog_jdbc_maxStatements=20
#tytlog_jdbc_initialPoolSize=20
#tytlog_jdbc_maxIdleTime=600
#tytlog_jdbc_idleConnectionTestPeriod=10

###############################################################
#note:mq infomation
ACCESS_KEY=LTAI5t9324vzFd6VLMybLzoE
SECRET_KEY=******************************
TAG=tag
SendMsgTimeoutMillis=500
TOPIC=TYT_TEST_MQ
GROUP_ID=GID_TYT_TEST_MQ
NAMESRV_ADDR=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
#the domain is product
#TOPIC=TOPIC_TYT_HB2
#PRODUCER_ID=PID_TOPIC_TYT_HB2
#CONSUMER_ID=CID_TOPIC_TYT_HB2
#ONSAddr=http\://onsaddr-internal.aliyun.com\:8080/rocketmq/nsaddr4client-internal

##### message center config #####
MESSAGE_CENTER_TOPIC=MESSAGE_CENTER_TOPIC_RELEASE
MESSAGE_CENTER_GROUP=GID_MESSAGE_CENTER_TOPIC_RELEASE

##### message center route config #####
OFTEN_ROUTE_TOPIC=OFTEN_ROUTE_TOPIC_RELEASE
OFTEN_ROUTE_GROUP=GID_OFTEN_ROUTE_TOPIC_RELEASE

LOG=2
LOG_TOPIC=tyt_log
LOG_SERVERS=*************:9092
#LOG_SERVERS=59.110.63.6:9092
LOG_CLIENT=tyt_Producer
###############################################################################

#############################RECOMMEND MQ CONFIG############################################
#test recommend mq
R_TAG=tyt_recommend
R_TOPIC=tyt_test_recommend
R_PRODUCER_ID=PID_test_recommend
R_CONSUMER_ID=CID_test_recommend

#release recommend mq
#R_TAG=tyt_recommend
#R_TOPIC=tyt_release_recommend
#R_PRODUCER_ID=PID_release_recommend
#R_CONSUMER_ID=CID_release_recommend

#product recommend mq
#R_TAG=tyt_recommend
#R_TOPIC=tyt_recommend
#R_PRODUCER_ID=PID_tyt_recommend
#R_CONSUMER_ID=CID_yt_recommend

############################RECOMMEND MQ CONFIG END#########################################
#cache config
#aliyun local
#tyt.cache.server=*************
#tyt.cache.server.user=5683f0571e7c4a1a
#tyt.cache.server.port=1121
#tyt.cache.server.password=Tyttest1
## DE_ :de ;QA_ :test; RE_ :release;LINE_ :production
#tyt.cache.server.env=QA_


#aliyun test
#tyt.cache.server=5683f0571e7c4a1a.m.cnbjalicm12pub001.ocs.aliyuncs.com
#tyt.cache.server.user=5683f0571e7c4a1a
#tyt.cache.server.port=11211
#tyt.cache.server.password=Tyttest1
## DE_ :de ;QA_ :test; RE_ :release;LINE_ :production
#tyt.cache.server.env=QA_

#aliyun release
#tyt.cache.server=5683f0571e7c4a1a.m.cnbjalicm12pub001.ocs.aliyuncs.com
#tyt.cache.server.user=5683f0571e7c4a1a
#tyt.cache.server.port=11211
#tyt.cache.server.password=Tyttest1
## DE_ :de ;QA_ :test; RE_ :release;LINE_ :production
#tyt.cache.server.env=RE_


#aliyun production
#tyt.cache.server=91ef94dd18b611e4.m.cnbjalicm12pub001.ocs.aliyuncs.com
#tyt.cache.server.port=11211
#tyt.cache.server.user=91ef94dd18b611e4
#tyt.cache.server.password=9e2a_3988
#tyt.cache.server.env=LINE_

############################REDIS CONFIG########################
# aliyun redis local
#tyt.redis.host=public-network-dev-0.redis.rds.aliyuncs.com
#tyt.redis.port=6379
#tyt.redis.password=TyT@dev#20220323
#tyt.redis.database=0

# aliyun redis test
#tyt.redis.host=r-2zeb3bdea8fac6d4.redis.rds.aliyuncs.com
#tyt.redis.port=6379
#tyt.redis.password=Tytwrite01
#tyt.redis.database=2

tyt.redis.host=public-network-dev-0.redis.rds.aliyuncs.com
tyt.redis.port=16379
tyt.redis.password=TyT@dev#20220323
tyt.redis.database=0

#tyt.redis.host=r-2zehe8qbmlx8071kcppd.redis.rds.aliyuncs.com
#tyt.redis.port=6379
#tyt.redis.password=tyt_Test_redis0608
#tyt.redis.database=2




#\u534E\u6CF0\u4FDD\u9669webservice\u914D\u7F6E
htbx.webservice.url=http://***************:8080/HT_interfacePlatform/webservice/ImportService?wsdl
htbx.webservice.user.name=TYT
htbx.webservice.user.pwd=HuoYun@321
htbx.webservice.key=2wsx1qaz
#\u534E\u6CF0\u4FDD\u9669\u652F\u4ED8\u914D\u7F6E
#test
htbx.pay.channelCode=100058
htbx.pay.key=123456@HT
#online
#htbx.pay.channelCode=100051
#htbx.pay.key=tyt2018@HT

# aliyun es test
tyt.es.host=************
tyt.es.port=9200
#tyt.es.user=elastic
#tyt.es.password=Elastic_test

# aliyun es local
#tyt.es.host=***********
#tyt.es.port=9200
#tyt.es.user=
#tyt.es.password=

picc.insurance.url=http://test.router.aibaoxian.com/routerServices
picc.insurance.key=TYT#test
#picc.payment.callback.url=http://www.teyuntong.com/htbx/pay_sync_callback.html
picc.payment.callback.url=http://*************/htbx/pay_sync_callback.html
picc.epolicy.host=http://**************:8940


mq.access.key=LTAI5t9324vzFd6VLMybLzoE
mq.secret.key=******************************
mq.tag=tag
mq.topic=DEV_SYNC_MB_GOODS_SOURCE
mq.groupid=GID_DEV_SYNC_MB_GOODS_SOURCE
mq.nameser.addr=http://MQ_INST_1955986389231769_BX2m3KBq.cn-beijing.mq-internal.aliyuncs.com:8080

##### ========== ali oss config ========== #####
oss.endpoint=oss-cn-beijing.aliyuncs.com
oss.accessKeyId=LTAI5tMXi79MuDcLySos1Fve
oss.accessKeySecret=******************************
oss.bucketName=tyt-audio
oss.domain=https://tytaudio.teyuntong.net/

##### ========== esign config ========== #####
esign.appId: 7439005644
esign.secret: 5a38881f2442b4489e9f75980673eb72
