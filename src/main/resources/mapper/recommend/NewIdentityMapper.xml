<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.recommend.NewIdentityMapper">
    <resultMap id="BaseResultMap" type="com.tytrecommend.model.NewIdentity">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="cal_dt" jdbcType="TIMESTAMP" property="calDt" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="type" jdbcType="SMALLINT" property="type" />
        <result column="main_name" jdbcType="VARCHAR" property="mainName" />
        <result column="user_follow" jdbcType="VARCHAR" property="userFollow" />
        <result column="auth_status" jdbcType="SMALLINT" property="authStatus" />
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    </resultMap>

    <sql id="baseColumns">
        id, cal_dt, user_id, type, main_name, user_follow, auth_status, ctime
    </sql>

    <select id="getByUserId" resultMap="BaseResultMap">
        select <include refid="baseColumns"/>
        from dws_new_identity_two_data
        where user_id=#{userId}
    </select>

    <select id="batchGetByUserId" resultMap="BaseResultMap">
        select <include refid="baseColumns"/>
        from dws_new_identity_two_data
        where user_id in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </select>
</mapper>