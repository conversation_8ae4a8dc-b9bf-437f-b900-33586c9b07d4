<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.recommend.GoodTypeCntMapper">
	<resultMap id="BaseResultMap" type="com.tyt.plat.entity.recommend.GoodTypeCnt">
		<!--
		  WARNING - @mbg.generated
		-->
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="good_type_name" jdbcType="VARCHAR" property="goodTypeName" />
		<result column="cnt" jdbcType="INTEGER" property="cnt" />
	</resultMap>

	<sql id="baseColumns">
		user_id, good_type_name, cnt
	</sql>

	<select id="getGoodsTypeCnt" resultType="java.lang.Integer">
		select cnt
		from dws_good_type_cnt
		where user_id = #{userId} and good_type_name = #{goodTypeName}
	</select>

</mapper>