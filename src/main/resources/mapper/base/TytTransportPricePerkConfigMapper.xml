<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportPricePerkConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportPricePerkConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_city" jdbcType="VARCHAR" property="startCity" />
    <result column="dest_city" jdbcType="VARCHAR" property="destCity" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="perk_ratio" jdbcType="INTEGER" property="perkRatio" />
    <result column="distance_min" jdbcType="INTEGER" property="distanceMin" />
    <result column="distance_max" jdbcType="INTEGER" property="distanceMax" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>

  <select id="getConfigByCity" resultMap="BaseResultMap">
    select *
    from tyt_transport_price_perk_config
    where start_city = #{startCity}
      and dest_city = #{destCity}
      and status = 1
      and delete_status = 0
      and start_time &lt;= NOW()
      and end_time >= NOW()
    order by id limit 1
  </select>

</mapper>