<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.deposit.mapper.base.TytGoodsRefreshConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.deposit.entity.base.TytGoodsRefreshConfig">
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="code" jdbcType="VARCHAR" property="code" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="content" jdbcType="VARCHAR" property="content" />
      <result column="status" jdbcType="INTEGER" property="status" />
      <result column="del" jdbcType="INTEGER" property="del" />
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
      <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
      <result column="create_name" jdbcType="VARCHAR" property="createName" />
      <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
      <result column="config_type" jdbcType="TINYINT" property="configType"/>
      <result column="excellent_goods" jdbcType="VARCHAR" property="excellentGoods"/>
      <result column="goods_price_type" jdbcType="VARCHAR" property="goodsPriceType"/>
      <result column="instant_grab" jdbcType="TINYINT" property="instantGrab"/>
      <result column="user_goods_type" jdbcType="VARCHAR" property="userGoodsType"/>
    </resultMap>

    <select id="selectConfigsWithoutUser" resultMap="BaseResultMap">
      select t.code, t.content, t.config_type, t.excellent_goods, t.goods_price_type, t.instant_grab, t.user_goods_type
      from tyt_goods_refresh_config t
      left join tyt_goods_refresh_user u on u.refresh_code = t.code
      where t.status = 1
      and t.del = 0
      and u.id is null
      order by t.modify_time desc
    </select>

    <select id="selectConfigByUserId" resultMap="BaseResultMap">
      select t.code, t.content, t.config_type, t.excellent_goods, t.goods_price_type, t.user_goods_type
      from tyt_goods_refresh_user u
      join tyt_goods_refresh_config t on u.refresh_code = t.code
      where u.user_id = #{userId}
      and t.status = 1
      and t.del = 0
      order by u.modify_time desc
    </select>

    <select id="getInstantGrabList" resultMap="BaseResultMap">
      SELECT
        c.goods_price_type,
        c.instant_grab
      FROM tyt_goods_refresh_config c
      JOIN tyt_goods_refresh_user u ON c.code = u.refresh_code
      WHERE c.`status` = 1
        AND c.del = 0
        AND config_type = 1
        AND excellent_goods = 0
        AND u.user_id = #{userId}
      ORDER BY c.goods_price_type desc
    </select>
</mapper>