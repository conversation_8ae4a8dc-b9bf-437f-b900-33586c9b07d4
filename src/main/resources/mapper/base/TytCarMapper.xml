<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCarMapper">




  <select id="queryMasterDriver" resultType="com.tyt.plat.entity.base.TytCarVO">
      select    tid.id driverUserId,
                driver_name driverName,
                driver_phone driverPhone from tyt_car tc left join tyt_invoice_driver tid on tc.driver_phone = tid.phone where tc.id=#{carId} and tid.user_id= tc.user_id and tid.verify_status = 1 order by tid.id desc limit 1
  </select>

    <select id="getByCarId" resultType="java.lang.String">
        select owner from  tyt_car_detail_head where car_id = #{carId} limit 1
    </select>

    <select id="selectCarIdByUserIdAndHeadCityNo" resultType="long">
        SELECT c.id FROM tyt_car c
            WHERE c.user_id=#{userId} AND auth=1
              AND c.head_city=#{headCity} AND c.head_no=#{headNo}
              AND c.tail_city=#{tailCity} AND c.tail_no=#{tailNo}
              AND c.is_delete=1 limit 1;
    </select>

</mapper>