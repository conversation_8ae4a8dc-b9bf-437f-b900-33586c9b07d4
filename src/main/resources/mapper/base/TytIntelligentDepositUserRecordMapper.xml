<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytIntelligentDepositUserRecordMapper">
	<resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytIntelligentDepositUserRecord">
		<!--
		  WARNING - @mbg.generated
		-->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="default_deposit" jdbcType="DECIMAL" property="defaultDeposit" />
		<result column="default_refund_flag" jdbcType="INTEGER" property="defaultRefundFlag" />
		<result column="user_deposit" jdbcType="DECIMAL" property="userDeposit" />
		<result column="user_refund_flag" jdbcType="INTEGER" property="userRefundFlag" />
		<result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
		<result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
	</resultMap>

	<select id="selectByUserId" resultType="java.lang.Long">
		select id
		from tyt_intelligent_deposit_user_record
		where user_id = #{userId} limit 1
	</select>
</mapper>