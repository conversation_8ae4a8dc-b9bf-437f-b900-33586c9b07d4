<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytUserempowerSyncgoodsMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytUserempowerSyncgoods">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="goods_user_name" jdbcType="VARCHAR" property="goodsUserName" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    </resultMap>

    <select id="selectUserEmpower" resultMap="BaseResultMap">
        select * from tyt_userempower_syncgoods where is_delete = 0 and user_id = #{userId}
    </select>







</mapper>