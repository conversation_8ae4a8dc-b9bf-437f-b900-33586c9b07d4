<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.CsNewCustomMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.CsNewCustom">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="custom_name" jdbcType="VARCHAR" property="customName" />
    <result column="custom_phone" jdbcType="VARCHAR" property="customPhone" />
    <result column="custom_source" jdbcType="VARCHAR" property="customSource" />
    <result column="register_status" jdbcType="SMALLINT" property="registerStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="modify_id" jdbcType="BIGINT" property="modifyId" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="belong_to" jdbcType="INTEGER" property="belongTo" />
    <result column="is_recommend_user" jdbcType="SMALLINT" property="isRecommendUser" />
    <result column="recommend_cell_phone" jdbcType="VARCHAR" property="recommendCellPhone" />
    <result column="recommend_user_id" jdbcType="BIGINT" property="recommendUserId" />
  </resultMap>

  <select id="checkMaintainedUser" resultType="com.tyt.plat.vo.invoice.MaintainedUserInfoDto">
    SELECT
      u.id,
      u.serve_days,
      u.cell_phone,
      u.user_name,
      u.true_name,
      u.ctime,
      u.end_time,
      u.verify_photo_sign,
      u.identity_type,
      u.user_class,
      u.user_type,
      u.last_time,
      u.is_car,
      s.maintain_man
    FROM tyt_user u
           LEFT JOIN tyt_user_sub s
                     ON u.id=s.user_id
    WHERE u.cell_phone= #{cellPhone,jdbcType=VARCHAR}
  </select>

  <select id="getNewCustomByCellPhone" resultMap="BaseResultMap">
    select * from cs_new_custom where custom_phone = #{phone}

  </select>

  <select id="getByBindId" resultType="com.tyt.plat.vo.invoice.MaintainedUserInfoDto">
    select id,name as trueName,login_phone_no as cellPhone from cs_business_user_bind where id = #{userId}

  </select>
</mapper>