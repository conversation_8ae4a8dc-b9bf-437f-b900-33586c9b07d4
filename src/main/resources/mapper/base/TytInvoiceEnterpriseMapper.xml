<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytInvoiceEnterpriseMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytInvoiceEnterprise">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
    <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName" />
    <result column="legal_person_phone" jdbcType="VARCHAR" property="legalPersonPhone" />
    <result column="legal_person_card" jdbcType="VARCHAR" property="legalPersonCard" />
    <result column="legal_person_card_url_g" jdbcType="VARCHAR" property="legalPersonCardUrlG" />
    <result column="legal_person_card_url_t" jdbcType="VARCHAR" property="legalPersonCardUrlT" />
    <result column="enterprise_credit_code" jdbcType="VARCHAR" property="enterpriseCreditCode" />
    <result column="enterprise_type" jdbcType="VARCHAR" property="enterpriseType" />
    <result column="enterprise_business_scope" jdbcType="VARCHAR" property="enterpriseBusinessScope" />
    <result column="enterprise_home_address" jdbcType="VARCHAR" property="enterpriseHomeAddress" />
    <result column="enterprise_detail_address" jdbcType="VARCHAR" property="enterpriseDetailAddress" />
    <result column="license_url" jdbcType="VARCHAR" property="licenseUrl" />
    <result column="license_start_time" jdbcType="TIMESTAMP" property="licenseStartTime" />
    <result column="license_end_time" jdbcType="TIMESTAMP" property="licenseEndTime" />
    <result column="transport_license_url" jdbcType="VARCHAR" property="transportLicenseUrl" />
    <result column="sign_type" jdbcType="INTEGER" property="signType" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="contract_start_time" jdbcType="TIMESTAMP" property="contractStartTime" />
    <result column="contract_end_time" jdbcType="TIMESTAMP" property="contractEndTime" />
    <result column="contract_continue_time" jdbcType="TIMESTAMP" property="contractContinueTime" />
    <result column="contract_url" jdbcType="VARCHAR" property="contractUrl" />
    <result column="certigier_user_id" jdbcType="BIGINT" property="certigierUserId" />
    <result column="certigier_user_name" jdbcType="VARCHAR" property="certigierUserName" />
    <result column="certigier_user_phone" jdbcType="VARCHAR" property="certigierUserPhone" />
    <result column="authorization_url" jdbcType="VARCHAR" property="authorizationUrl" />
    <result column="enterprise_tax_rate" jdbcType="DECIMAL" property="enterpriseTaxRate" />
    <result column="tax_rate_float" jdbcType="INTEGER" property="taxRateFloat" />
    <result column="enterprise_verify_status" jdbcType="INTEGER" property="enterpriseVerifyStatus" />
    <result column="info_verify_status" jdbcType="INTEGER" property="infoVerifyStatus" />
    <result column="info_verify_reason" jdbcType="VARCHAR" property="infoVerifyReason" />
    <result column="info_verify_commit_time" jdbcType="TIMESTAMP" property="infoVerifyCommitTime" />
    <result column="contract_verify_status" jdbcType="INTEGER" property="contractVerifyStatus" />
    <result column="contract_verify_reason" jdbcType="VARCHAR" property="contractVerifyReason" />
    <result column="contract_verify_commit_time" jdbcType="TIMESTAMP" property="contractVerifyCommitTime" />
    <result column="contract_audit_time" jdbcType="TIMESTAMP" property="contractAuditTime" />
    <result column="contract_audit_user_id" jdbcType="BIGINT" property="contractAuditUserId" />
    <result column="contract_audit_user_name" jdbcType="VARCHAR" property="contractAuditUserName" />
    <result column="certigier_verify_status" jdbcType="INTEGER" property="certigierVerifyStatus" />
    <result column="certigier_verify_reason" jdbcType="VARCHAR" property="certigierVerifyReason" />
    <result column="certigier_verify_commit_time" jdbcType="TIMESTAMP" property="certigierVerifyCommitTime" />
    <result column="certigier_audit_time" jdbcType="TIMESTAMP" property="certigierAuditTime" />
    <result column="certigier_audit_user_id" jdbcType="BIGINT" property="certigierAuditUserId" />
    <result column="certigier_audit_user_name" jdbcType="VARCHAR" property="certigierAuditUserName" />
    <result column="account_status" jdbcType="INTEGER" property="accountStatus" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="info_audit_time" jdbcType="TIMESTAMP" property="infoAuditTime" />
    <result column="info_audit_user_id" jdbcType="BIGINT" property="infoAuditUserId" />
    <result column="info_audit_user_name" jdbcType="VARCHAR" property="infoAuditUserName" />
    <result column="transport_reject_reason" jdbcType="VARCHAR" property="transportRejectReason" />
    <result column="transport_license_status" jdbcType="INTEGER" property="transportLicenseStatus" />
    <result column="transport_commit_time" jdbcType="TIMESTAMP" property="transportCommitTime" />
    <result column="transport_audit_time" jdbcType="TIMESTAMP" property="transportAuditTime" />
    <result column="transport_audit_user_id" jdbcType="BIGINT" property="transportAuditUserId" />
    <result column="transport_audit_user_name" jdbcType="VARCHAR" property="transportAuditUserName" />
    <result column="real_verify" jdbcType="INTEGER" property="realVerify" />
    <result column="license_permanent" jdbcType="INTEGER" property="licensePermanent" />
    <result column="contract_final_status" jdbcType="INTEGER" property="contractFinalStatus" />
    <result column="contract_final_reason" jdbcType="VARCHAR" property="contractFinalReason" />
    <result column="manager_flag" jdbcType="INTEGER" property="managerFlag" />
    <result column="invoice_close_flag" jdbcType="INTEGER" property="invoiceCloseFlag" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
    <result column="customer_manager_name" jdbcType="VARCHAR" property="customerManagerName" />
    <result column="customer_manager_phone" jdbcType="VARCHAR" property="customerManagerPhone" />
    <result column="customer_manager_email" jdbcType="VARCHAR" property="customerManagerEmail" />
  </resultMap>

  <resultMap id="TytTransportEnterpriseLogMap" type="com.tyt.plat.entity.base.TytTransportEnterpriseLog">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="src_msg_id" property="srcMsgId" jdbcType="BIGINT"/>
    <result column="enterprise_id" property="enterpriseId" jdbcType="BIGINT"/>
    <result column="enterprise_name" property="enterpriseName" jdbcType="VARCHAR"/>
    <result column="legal_person_name" property="legalPersonName" jdbcType="VARCHAR"/>
    <result column="legal_person_phone" property="legalPersonPhone" jdbcType="VARCHAR"/>
    <result column="legal_person_card" property="legalPersonCard" jdbcType="VARCHAR"/>
    <result column="legal_person_card_url_g" property="legalPersonCardUrlG" jdbcType="VARCHAR"/>
    <result column="legal_person_card_url_t" property="legalPersonCardUrlT" jdbcType="VARCHAR"/>
    <result column="enterprise_credit_code" property="enterpriseCreditCode" jdbcType="VARCHAR"/>
    <result column="enterprise_type" property="enterpriseType" jdbcType="VARCHAR"/>
    <result column="enterprise_business_scope" property="enterpriseBusinessScope" jdbcType="VARCHAR"/>
    <result column="enterprise_home_address" property="enterpriseHomeAddress" jdbcType="VARCHAR"/>
    <result column="enterprise_detail_address" property="enterpriseDetailAddress" jdbcType="VARCHAR"/>
    <result column="license_url" property="licenseUrl" jdbcType="VARCHAR"/>
    <result column="license_start_time" property="licenseStartTime" jdbcType="TIMESTAMP"/>
    <result column="license_end_time" property="licenseEndTime" jdbcType="TIMESTAMP"/>
    <result column="transport_license_url" property="transportLicenseUrl" jdbcType="VARCHAR"/>
    <result column="sign_type" property="signType" jdbcType="INTEGER"/>
    <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
    <result column="contract_start_time" property="contractStartTime" jdbcType="TIMESTAMP"/>
    <result column="contract_end_time" property="contractEndTime" jdbcType="TIMESTAMP"/>
    <result column="certigier_user_id" property="certigierUserId" jdbcType="BIGINT"/>
    <result column="certigier_user_name" property="certigierUserName" jdbcType="VARCHAR"/>
    <result column="certigier_user_phone" property="certigierUserPhone" jdbcType="VARCHAR"/>
    <result column="enterprise_tax_rate" property="enterpriseTaxRate" jdbcType="DECIMAL"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    <result column="invoice_subject_id" property="invoiceSubjectId" jdbcType="BIGINT"/>
    <result column="service_provider_code" property="serviceProviderCode" jdbcType="VARCHAR"/>
    <result column="assign_car_tel" property="assignCarTel" jdbcType="VARCHAR"/>
    <result column="consignee_name" property="consigneeName" jdbcType="VARCHAR"/>
    <result column="consignee_tel" property="consigneeTel" jdbcType="VARCHAR"/>
    <result column="consignee_enterprise_name" property="consigneeEnterpriseName" jdbcType="VARCHAR"/>
    <result column="prepaid_price" property="prepaidPrice" jdbcType="DECIMAL"/>
    <result column="collected_price" property="collectedPrice" jdbcType="DECIMAL"/>
    <result column="receipt_price" property="receiptPrice" jdbcType="DECIMAL"/>
    <result column="payments_type" property="paymentsType" jdbcType="INTEGER"/>
  </resultMap>

    <select id="getManagerEnterprise" resultMap="BaseResultMap">
      select * from tyt_invoice_enterprise
      where manager_flag = 1
        and enterprise_credit_code = #{enterpriseCreditCode}
        and id != #{excludeId}
    </select>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select * from tyt_invoice_enterprise where certigier_user_id =#{useId,jdbcType=BIGINT} AND info_verify_status = #{infoVerifyStatus,jdbcType=INTEGER} order by id limit 1
  </select>


  <select id="selectByUserIdNoStatus" resultMap="BaseResultMap">
    select * from tyt_invoice_enterprise where certigier_user_id =#{useId,jdbcType=BIGINT} order by id limit 1
  </select>

  <select id="getInvoiceTransportEnterpriseLogBySrcMsgId" resultMap="TytTransportEnterpriseLogMap">
    select * from tyt_transport_enterprise_log where src_msg_id = #{srcMsgId}
  </select>

    <select id="getEnterpriseByCode" resultMap="BaseResultMap">
      select * from tyt_invoice_enterprise
      where enterprise_credit_code = #{enterpriseCreditCode} limit 1
    </select>

    <delete id="deleteInvoiceTransportEnterpriseDataBySrcMsgId">
    delete from tyt_transport_enterprise_log where src_msg_id = #{srcMsgId}
  </delete>

  <insert id="saveInvoiceTransportEnterpriseData">
    insert into tyt_transport_enterprise_log (src_msg_id, enterprise_id, enterprise_name, legal_person_name
                                                          , legal_person_phone, legal_person_card, legal_person_card_url_g
                                                          , legal_person_card_url_t, enterprise_credit_code, enterprise_type
                                                          , enterprise_business_scope, enterprise_home_address, enterprise_detail_address
                                                          , license_url, license_start_time, license_end_time, transport_license_url
                                                          , sign_type, contract_no, contract_start_time, contract_end_time
                                                          , certigier_user_id, certigier_user_name, certigier_user_phone
                                                          , enterprise_tax_rate, remark, invoice_subject_id, service_provider_code, assign_car_tel, create_time, modify_time
                                             , consignee_name, consignee_tel, consignee_enterprise_name,prepaid_price,collected_price,receipt_price,payments_type)
      VALUE (
      #{srcMsgId}, #{userEnterpriseData.id}, #{userEnterpriseData.enterpriseName}, #{userEnterpriseData.legalPersonName}
      , #{userEnterpriseData.legalPersonPhone}, #{userEnterpriseData.legalPersonCard}, #{userEnterpriseData.legalPersonCardUrlG}
      , #{userEnterpriseData.legalPersonCardUrlT}, #{userEnterpriseData.enterpriseCreditCode}, #{userEnterpriseData.enterpriseType}
      , #{userEnterpriseData.enterpriseBusinessScope}, #{userEnterpriseData.enterpriseHomeAddress}, #{userEnterpriseData.enterpriseDetailAddress}
      , #{userEnterpriseData.licenseUrl}, #{userEnterpriseData.licenseStartTime}, #{userEnterpriseData.licenseEndTime}, #{userEnterpriseData.transportLicenseUrl}
      , #{userEnterpriseData.signType}, #{userEnterpriseData.contractNo}, #{userEnterpriseData.contractStartTime}, #{userEnterpriseData.contractEndTime}
      , #{userEnterpriseData.certigierUserId}, #{userEnterpriseData.certigierUserName}, #{userEnterpriseData.certigierUserPhone}
      , #{userEnterpriseData.enterpriseTaxRate}, #{userEnterpriseData.remark}, #{userEnterpriseData.invoiceSubjectId}, #{userEnterpriseData.serviceProviderCode}
      , #{userEnterpriseData.assignCarTel}, now(), now(), #{userEnterpriseData.consigneeName}, #{userEnterpriseData.consigneeTel}, #{userEnterpriseData.consigneeEnterpriseName}
      , #{userEnterpriseData.prepaidPrice},#{userEnterpriseData.collectedPrice},#{userEnterpriseData.receiptPrice},#{userEnterpriseData.paymentsType}
      )
  </insert>


  <select id="getByTransportUserId" resultType="java.lang.Long">
    select id
    from tyt_invoice_enterprise where certigier_user_id = #{goodsUserId}
  </select>
</mapper>