<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.PricingRouteConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.pricingRoute.bean.PricingRouteConfig">
        <id property="id" column="id" />
        <result property="transportType" column="transport_type" />
        <result property="startProvince" column="start_province" />
        <result property="startCity" column="start_city" />
        <result property="startArea" column="start_area" />
        <result property="startAddress" column="start_address" />
        <result property="destProvince" column="dest_province" />
        <result property="destCity" column="dest_city" />
        <result property="destArea" column="dest_area" />
        <result property="destAddress" column="dest_address" />
        <result property="enabledStatus" column="enabled_status" />
        <result property="isDelete" column="is_delete" />
        <result property="createName" column="create_name" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="modifyName" column="modify_name" />
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="list" resultMap="BaseResultMap">
        SELECT * FROM tyt_pricing_route_config
        WHERE is_delete = 0
        <if test="transportType != null">
            AND transport_type = #{transportType}
        </if>
        <if test="enabledStatus != null">
            AND enabled_status = #{enabledStatus}
        </if>
        <if test="startAddress != null and startAddress != ''">
            AND start_address = #{startAddress}
        </if>
        <if test="destAddress != null and destAddress != ''">
            AND dest_address = #{destAddress}
        </if>
        ORDER BY id DESC
    </select>

    <!-- 校验是否是优车定价路线 -->
    <select id="checkExcellentGoodsPricingRoute" resultType="integer">
        SELECT 1 FROM tyt_pricing_route_config
        WHERE is_delete = 0
        AND transport_type = 1
        AND enabled_status = 1
        AND (
            #{startAddress} LIKE CONCAT(start_address, '%') AND #{destAddress} LIKE CONCAT(dest_address, '%')
            or start_address = '全国' AND #{destAddress} LIKE CONCAT(dest_address, '%')
            or #{startAddress} LIKE CONCAT(start_address, '%') AND dest_address = '全国'
            or start_address = '全国' AND dest_address = '全国'
        )
        LIMIT 1
    </select>

</mapper>