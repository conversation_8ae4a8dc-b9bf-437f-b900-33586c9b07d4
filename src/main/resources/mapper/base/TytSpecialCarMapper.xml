<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSpecialCarMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSpecialCar">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="tyt_cell_phone" jdbcType="VARCHAR" property="tytCellPhone" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="driver_user_id" jdbcType="BIGINT" property="driverUserId" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="driving_ability" jdbcType="VARCHAR" property="drivingAbility" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="head_city_no" jdbcType="VARCHAR" property="headCityNo" />
    <result column="tail_city_no" jdbcType="VARCHAR" property="tailCityNo" />
    <result column="length" jdbcType="VARCHAR" property="length" />
    <result column="table_height" jdbcType="VARCHAR" property="tableHeight" />
    <result column="other_pure_flat" jdbcType="VARCHAR" property="otherPureFlat" />
    <result column="ladder_type" jdbcType="VARCHAR" property="ladderType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="fraction" jdbcType="VARCHAR" property="fraction" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="bi_distance" jdbcType="VARCHAR" property="biDistance"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="car_id" jdbcType="BIGINT" property="carId" />
  </resultMap>

  <select id="getList" resultMap="BaseResultMap">

    SELECT
      id,
      user_id,
      name,
      head_city_no,
      city,
      driving_ability,
      status,
      reason,
      modify_time
    FROM
      tyt_special_car
    WHERE
      user_id = #{userId}
      order by id desc
      LIMIT #{page},#{pageSize}
  </select>

  <select id="getInfo" resultType="java.lang.Integer">
        select count(*) from tyt_special_car
        where
            user_id = #{userId}
            and driver_id = #{driverId}
            and driver_user_id = #{driverUserId}
            and (status = 1 or status = 0)

  </select>

  <select id="getTotal" resultType="java.lang.Integer">
    select count(*) from tyt_special_car where user_id = #{userId}
  </select>
</mapper>