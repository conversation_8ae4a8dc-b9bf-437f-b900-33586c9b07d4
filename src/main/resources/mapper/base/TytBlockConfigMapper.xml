<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytBlockConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytBlockConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_type" jdbcType="INTEGER" property="configType" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="license_plate" jdbcType="INTEGER" property="licensePlate" />
    <result column="new_user_permission" jdbcType="INTEGER" property="newUserPermission" />
    <result column="old_user_permission" jdbcType="INTEGER" property="oldUserPermission" />
    <result column="old_user_phone" jdbcType="INTEGER" property="oldUserPhone" />
    <result column="remind" jdbcType="INTEGER" property="remind" />
    <result column="block" jdbcType="INTEGER" property="block" />
    <result column="receiving_orders" jdbcType="INTEGER" property="receivingOrders" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="open_time" jdbcType="TIMESTAMP" property="openTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
  </resultMap>


  <select id="getConfig" resultMap="BaseResultMap">
    select id,config_type,province,city,license_plate,new_user_permission,old_user_permission,old_user_phone,remind
    ,block,receiving_orders,open_time
    from tyt_block_config
    where status = 1
    order by config_type desc,modify_time desc
    limit 3
  </select>
</mapper>