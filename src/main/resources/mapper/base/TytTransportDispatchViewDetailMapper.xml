<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportDispatchViewDetailMapper">
	<resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportDispatchViewDetail">
	<!--
	  WARNING - @mbg.generated
	-->
	<id column="id" jdbcType="BIGINT" property="id" />
	<result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
	<result column="car_user_id" jdbcType="BIGINT" property="carUserId" />
	<result column="car_user_name" jdbcType="VARCHAR" property="carUserName" />
	<result column="car_nick_name" jdbcType="VARCHAR" property="carNickName" />
	<result column="car_phone" jdbcType="VARCHAR" property="carPhone" />
	<result column="type" jdbcType="INTEGER" property="type" />
	<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
	<result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
	</resultMap>

	<select id="queryViewCarUser" resultType="java.lang.Long">
		select distinct car_user_id
		from tyt_transport_dispatch_view_detail
		where src_msg_id = #{srcMsgId} and type = 1
	</select>
</mapper>