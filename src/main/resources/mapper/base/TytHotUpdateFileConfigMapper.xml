<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytHotUpdateFileConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.transport.bean.TytHotUpdateFileConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="release_version" jdbcType="VARCHAR" property="releaseVersion" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="publish_type" jdbcType="INTEGER" property="publishType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
  </resultMap>

  <select id="getAppHotUpdateValidAndPublishByReleaseVersion" resultMap="BaseResultMap">
    select id, release_version, client_type, file_url, version, publish_type, create_time, create_user_name, create_user_id
    from tyt_hot_update_file_config where release_version = #{releaseVersion} and client_type = #{clentType} and publish_type = 1
  </select>

</mapper>