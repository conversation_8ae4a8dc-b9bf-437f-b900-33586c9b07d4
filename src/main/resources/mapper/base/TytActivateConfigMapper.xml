<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytActivateConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytActivateConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="login_give_goods" jdbcType="BIGINT" property="loginGiveGoods" />
    <result column="login_give_goods_id" jdbcType="BIGINT" property="loginGiveGoodsId" />
    <result column="login_give_goods_times" jdbcType="INTEGER" property="loginGiveGoodsTimes" />
    <result column="share_give_goods" jdbcType="BIGINT" property="shareGiveGoods" />
    <result column="share_give_goods_id" jdbcType="BIGINT" property="shareGiveGoodsId" />
    <result column="share_give_goods_times" jdbcType="INTEGER" property="shareGiveGoodsTimes" />
    <result column="limit_login_days" jdbcType="INTEGER" property="limitLoginDays" />
    <result column="limit_call_days" jdbcType="INTEGER" property="limitCallDays" />
    <result column="limit_call_num" jdbcType="INTEGER" property="limitCallNum" />
    <result column="no_give_days" jdbcType="INTEGER" property="noGiveDays" />
    <result column="vip_status" jdbcType="INTEGER" property="vipStatus" />
    <result column="register_status" jdbcType="INTEGER" property="registerStatus" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="getActivateConfig" resultMap="BaseResultMap">
    select * from tyt_activate_config where status=1 limit 1
  </select>

  <select id="getBlackStatus" resultType="java.lang.Integer">
    select count(1) from tyt_activate_config_black where user_id=#{userId}
  </select>

</mapper>