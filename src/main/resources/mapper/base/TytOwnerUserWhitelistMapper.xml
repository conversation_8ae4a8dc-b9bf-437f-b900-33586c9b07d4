<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytOwnerUserWhitelistMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytOwnerUserWhitelist">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <sql id="BaseSql">
    id,user_id,cell_phone,operater,is_delete,ctime,mtime
  </sql>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select
    <include refid="BaseSql"/>
    from tyt_owner_user_whitelist
    where user_id = #{userId,jdbcType=BIGINT} and is_delete = 1 limit 1
  </select>
</mapper>