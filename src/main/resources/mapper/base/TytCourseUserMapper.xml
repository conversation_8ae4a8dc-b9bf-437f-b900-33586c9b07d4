<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCourseUserMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCourseUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="course_id" jdbcType="BIGINT" property="courseId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="study_time" jdbcType="TIMESTAMP" property="studyTime" />
    <result column="study_status" jdbcType="INTEGER" property="studyStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_man_id" jdbcType="BIGINT" property="createManId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_man_id" jdbcType="BIGINT" property="modifyManId" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
  </resultMap>

    <select id="getLearnInfoCount" resultType="java.lang.Integer">
      select count(*) from tyt_course_user cu
          inner join tyt_course_info ci on cu.course_id = ci.id
      where
        ci.type = #{type}
        and ci.enable = 1
        and ci.status = 1
        and cu.user_id = #{userId}
        <if test="learnFlag == 1">
          and cu.study_status = 2
        </if>
        <if test="learnFlag != 1">
          and cu.study_status != 2
        </if>
    </select>

</mapper>