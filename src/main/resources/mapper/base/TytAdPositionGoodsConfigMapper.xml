<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytAdPositionGoodsConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytAdPositionGoodsConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ad_position_id" jdbcType="BIGINT" property="adPositionId" />
    <result column="ad_position" jdbcType="INTEGER" property="adPosition" />
    <result column="price_type" jdbcType="INTEGER" property="priceType" />
    <result column="goods_type" jdbcType="INTEGER" property="goodsType" />
    <result column="owner_identity" jdbcType="INTEGER" property="ownerIdentity" />
    <result column="invoice_transport" jdbcType="INTEGER" property="invoiceTransport" />
    <result column="refund_flag" jdbcType="INTEGER" property="refundFlag" />
    <result column="delivery_flag" jdbcType="INTEGER" property="deliveryFlag" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <select id="getByAdId" resultMap="BaseResultMap">
    select invoice_transport,delivery_flag from tyt_ad_position_goods_config where ad_position_id = #{id} limit 1
  </select>
</mapper>