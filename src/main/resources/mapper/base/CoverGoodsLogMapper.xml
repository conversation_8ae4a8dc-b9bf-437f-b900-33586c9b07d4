<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.CoverGoodsLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.CoverGoodsLogDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="ts_id" property="tsId" />
        <result column="goods_level" property="goodsLevel" />
        <result column="view_index" property="viewIndex" />
        <result column="hit_rule" property="hitRule" />
        <result column="priority_recommend_expire_time" property="priorityRecommendExpireTime" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, ts_id, goods_level, view_index, hit_rule, priority_recommend_expire_time, create_time
    </sql>

    <select id="getByUserAndTsId" resultMap="BaseResultMap">
        select * from tyt_cover_goods_log
        where user_id = #{userId}
        and ts_id = #{tsId}
        limit 1
    </select>

    <select id="countTodayCoverTimesWithoutGoodGoods" resultType="integer">
        select count(*) from tyt_cover_goods_log
        where user_id = #{userId}
        and create_time > curdate()
        and goods_level != 1
    </select>

    <select id="getCoverGoodsIds" resultType="long">
        select ts_id from tyt_cover_goods_log
        where user_id = #{userId}
        and ts_id in ( <foreach collection="srcMsgIds" item="i" separator=","> #{i} </foreach> )
    </select>

</mapper>
