<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportExposureMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportExposure">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="has_call" jdbcType="INTEGER" property="hasCall" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="change_id" jdbcType="BIGINT" property="changeId" />
  </resultMap>

  <select id="selectMaxRankLevelByDestTransportSearchBean" parameterType="com.tyt.transport.querybean.DestTransportSearchBean" resultType="java.lang.Long">
    select tt.id from tyt_transport_exposure tte INNER JOIN tyt_transport tt on tte.src_msg_id = tt.src_msg_id
    <where>
      tt.status=1
      and tt.display_type=1
      and tt.is_display=1
      and tt.start_provinc=#{startProvinc}
      and tt.start_city=#{startCity}
      and tte.status =1
      and tt.ctime BETWEEN CONCAT( CURDATE(), ' 00:00:00' ) AND CONCAT( CURDATE(), ' 23:59:59' )
      <if test="startArea !=null and startArea !=''">
        and tt.start_area= #{startArea}
      </if>
      <if test="destProvinc !=null and destProvinc !=''">
        and tt.dest_provinc=#{destProvinc}
      </if>
      <if test="destCity !=null and destCity !=''">
        and tt.dest_city=#{destCity}
      </if>
      <if test="destArea !=null and destArea !=''">
        and tt.dest_area= #{destArea}
      </if>
    </where>
    order by tt.total_score desc limit 1
  </select>

  <select id="selectMaxRankLeveBySimilarityCode" resultType="java.lang.Long">
    select
      tt.id
    from tyt_transport tt FORCE INDEX (idx_ctime_similarity_src)
           INNER JOIN tyt_transport_exposure tte on tte.src_msg_id = tt.src_msg_id
    where
      tt.ctime >= CONCAT(CURDATE(), ' 00:00:00' )
      and tt.ctime &lt;= now()
      and tt.status=1
      and tt.similarity_code = #{similarityCode}
      and tt.display_type=1
      and tt.is_display=1
      and tt.src_msg_id != #{goodsId}
      and tte.status =1
      and tte.create_time >= CONCAT(CURDATE(), ' 00:00:00' )
    order by tt.total_score desc, tt.ctime desc
    limit 1
  </select>


  <select id="getMaxChangeId" resultType="java.lang.Long">
    select max(change_id) from tyt_transport_exposure
  </select>

</mapper>
