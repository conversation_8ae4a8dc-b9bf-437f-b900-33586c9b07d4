<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytUserConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytUserConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectByConfigTypesAndUserId" resultMap="BaseResultMap">
        select *
        from tyt_user_config where user_id = #{userId}
        <if test="configTypes != null and configTypes.size() != 0">
            <foreach item="configType" collection="configTypes" open="and type in  (" close=")" separator=",">
                #{configType}
            </foreach>
        </if>
    </select>

    <select id="selectByUserIdAndType" resultMap="BaseResultMap">
        select *
        from tyt_user_config
        where user_id = #{userId}
          and type = #{type}
    </select>
</mapper>