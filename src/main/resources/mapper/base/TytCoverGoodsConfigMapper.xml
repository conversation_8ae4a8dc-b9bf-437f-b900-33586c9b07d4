<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCoverGoodsConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCoverGoodsConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="config_item" jdbcType="INTEGER" property="configItem"/>
        <result column="config_code" jdbcType="INTEGER" property="configCode"/>
        <result column="enable" jdbcType="INTEGER" property="enable"/>
        <result column="config_ext" jdbcType="VARCHAR" property="configExt"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        select *
        from tyt_cover_goods_config
    </select>
</mapper>