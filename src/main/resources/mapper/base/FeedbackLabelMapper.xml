<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.FeedbackLabelMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.FeedbackLabel">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="label_name" jdbcType="VARCHAR" property="labelName"/>
        <result column="label_type" jdbcType="SMALLINT" property="labelType"/>
        <result column="feedback_type" jdbcType="SMALLINT" property="feedbackType"/>
        <result column="enable_status" jdbcType="TINYINT" property="enableStatus"/>
        <result column="modify_user_id" jdbcType="BIGINT" property="modifyUserId"/>
        <result column="modify_user_name" jdbcType="VARCHAR" property="modifyUserName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="show_priority" jdbcType="TIMESTAMP" property="showPriority"/>
    </resultMap>

    <select id="count" resultType="long">
        select count(*)
        from feedback_label
        where label_type = #{labelType}
          and feedback_type = #{feedbackType}
          and enable_status = 1
        <if test="labelIds != null and labelIds.size() != 0">
            <foreach collection="labelIds" item="item" open="and id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select id,
               label_name,
               label_type,
               feedback_type,
               enable_status,
               modify_user_id,
               modify_user_name,
               create_time,
               update_time,
               show_priority
        from feedback_label
        where label_type = #{labelType}
          and enable_status = #{enableStatus}
          and feedback_type = #{feedbackType}
        order by update_time desc
    </select>
</mapper>