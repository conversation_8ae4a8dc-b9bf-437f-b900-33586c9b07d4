<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSpecialCarDispatchFailureMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSpecialCarDispatchFailure">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="work_order_status" jdbcType="INTEGER" property="workOrderStatus" />
    <result column="response_time" jdbcType="TIMESTAMP" property="responseTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="publish_user_type" jdbcType="TINYINT" property="publishUserType" />
    <result column="dispatch_type" jdbcType="TINYINT" property="dispatchType" />
    <result column="publish_user_id" jdbcType="BIGINT" property="publishUserId" />
    <result column="publish_user_name" jdbcType="VARCHAR" property="publishUserName" />
    <result column="dispatcher_id" jdbcType="BIGINT" property="dispatcherId" />
    <result column="dispatcher_name" jdbcType="VARCHAR" property="dispatcherName" />
    <result column="owner_freight" jdbcType="DECIMAL" property="ownerFreight" />
    <result column="give_goods_phone" jdbcType="VARCHAR" property="giveGoodsPhone" />
    <result column="give_goods_name" jdbcType="VARCHAR" property="giveGoodsName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="del_status" jdbcType="TINYINT" property="delStatus" />
    <result column="declare_in_public" jdbcType="INTEGER" property="declareInPublic" />
  </resultMap>

  <update id="updateWorkOrderStatus">
    update tyt_special_car_dispatch_failure
    set work_order_status = #{workOrderStatus}, end_time = #{endTime}
    where src_msg_id = #{srcMsgId} and del_status = 0
  </update>

  <update id="updateDeclareInPublicToNo">
    update tyt_special_car_dispatch_failure
    set declare_in_public = 0
    where src_msg_id = #{srcMsgId}
  </update>

  <select id="selectBySrcMsgId" resultMap="BaseResultMap">
        select *
        from tyt_special_car_dispatch_failure
        where src_msg_id = #{srcMsgId} and del_status = 0 limit 1
    </select>
</mapper>