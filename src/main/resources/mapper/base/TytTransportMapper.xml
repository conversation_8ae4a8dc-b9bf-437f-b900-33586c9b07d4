<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportMapper">

    <resultMap id="TytTransportResultMap" type="com.tyt.model.Transport">
        <id property="id" column="id"/>
        <result property="sort" column="sort"/>
        <result property="startPoint" column="start_point"/>
        <result property="destPoint" column="dest_point"/>
        <result property="taskContent" column="task_content"/>
        <result property="tel" column="tel"/>
        <result property="pubTime" column="pub_time"/>
        <result property="pubQQ" column="pub_qq"/>
        <result property="nickName" column="nick_name"/>
        <result property="userShowName" column="user_show_name"/>
        <result property="status" column="status"/>
        <result property="source" column="source"/>
        <result property="ctime" column="ctime"/>
        <result property="mtime" column="mtime"/>
        <result property="uploadCellPhone" column="upload_cellphone"/>
        <result property="resend" column="resend"/>
        <result property="startCoord" column="start_coord"/>
        <result property="destCoord" column="dest_coord"/>
        <result property="platId" column="plat_id"/>
        <result property="verifyFlag" column="verify_flag"/>
        <result property="price" column="price"/>
        <result property="userId" column="user_id"/>
        <result property="priceCode" column="price_code"/>
        <result property="startCoordX" column="start_coord_x"/>
        <result property="startCoordXValue" column="start_coord_x"/>
        <result property="startCoordY" column="start_coord_y"/>
        <result property="startCoordYValue" column="start_coord_y"/>
        <result property="destCoordX" column="dest_coord_x"/>
        <result property="destCoordXValue" column="dest_coord_x"/>
        <result property="destCoordY" column="dest_coord_y"/>
        <result property="destCoordYValue" column="dest_coord_y"/>
        <result property="startDetailAdd" column="start_detail_add"/>
        <result property="startLongitude" column="start_longitude"/>
        <result property="startLongitudeValue" column="start_longitude"/>
        <result property="startLatitude" column="start_latitude"/>
        <result property="startLatitudeValue" column="start_latitude"/>
        <result property="destDetailAdd" column="dest_detail_add"/>
        <result property="destLongitude" column="dest_longitude"/>
        <result property="destLongitudeValue" column="dest_longitude"/>
        <result property="destLatitude" column="dest_latitude"/>
        <result property="destLatitudeValue" column="dest_latitude"/>
        <result property="pubDate" column="pub_date"/>
        <result property="goodsCode" column="goods_code"/>
        <result property="weightCode" column="weight_code"/>
        <result property="weight" column="weight"/>
        <result property="length" column="LENGTH"/>
        <result property="wide" column="wide"/>
        <result property="high" column="high"/>
        <result property="isSuperelevation" column="is_superelevation"/>
        <result property="linkman" column="linkman"/>
        <result property="remark" column="remark"/>
        <result property="distance" column="distance"/>
        <result property="pubGoodsTime" column="pub_goods_time"/>
        <result property="tel3" column="tel3"/>
        <result property="tel4" column="tel4"/>
        <result property="displayType" column="display_type"/>
        <result property="hashCode" column="hash_code"/>
        <result property="isCar" column="is_car"/>
        <result property="userType" column="user_type"/>
        <result property="pcOldContent" column="pc_old_content"/>
        <result property="resendCounts" column="resend_counts"/>
        <result property="verifyPhotoSign" column="verify_photo_sign"/>
        <result property="userPart" column="user_part"/>
        <result property="startCity" column="start_city"/>
        <result property="srcMsgId" column="src_msg_id"/>
        <result property="startProvinc" column="start_provinc"/>
        <result property="startArea" column="start_area"/>
        <result property="destProvinc" column="dest_provinc"/>
        <result property="destCity" column="dest_city"/>
        <result property="destArea" column="dest_area"/>
        <result property="clientVersion" column="client_version"/>
        <result property="isInfoFee" column="is_info_fee"/>
        <result property="infoStatus" column="info_status"/>
        <result property="tsOrderNo" column="ts_order_no"/>
        <result property="releaseTime" column="release_time"/>
        <result property="regTime" column="reg_time"/>
        <result property="type" column="type"/>
        <result property="brand" column="brand"/>
        <result property="goodTypeName" column="good_type_name"/>
        <result property="goodNumber" column="good_number"/>
        <result property="isStandard" column="is_standard"/>
        <result property="matchItemId" column="match_item_id"/>
        <result property="androidDistance" column="android_distance"/>
        <result property="iosDistance" column="ios_distance"/>
        <result property="isDisplay" column="is_display"/>
        <result property="referLength" column="refer_length"/>
        <result property="referWidth" column="refer_width"/>
        <result property="referHeight" column="refer_height"/>
        <result property="referWeight" column="refer_weight"/>
        <result property="carLength" column="car_length"/>
        <result property="loadingTime" column="loading_time"/>
        <result property="beginUnloadTime" column="begin_unload_time"/>
        <result property="unloadTime" column="unload_time"/>
        <result property="carMinLength" column="car_min_length"/>
        <result property="carMaxLength" column="car_max_length"/>
        <result property="carType" column="car_type"/>
        <result property="beginLoadingTime" column="begin_loading_time"/>
        <result property="carStyle" column="car_style"/>
        <result property="workPlaneMinHigh" column="work_plane_min_high"/>
        <result property="workPlaneMaxHigh" column="work_plane_max_high"/>
        <result property="workPlaneMinLength" column="work_plane_min_length"/>
        <result property="workPlaneMaxLength" column="work_plane_max_length"/>
        <result property="climb" column="climb"/>
        <result property="orderNumber" column="order_number"/>
        <result property="evaluate" column="evaluate"/>
        <result property="specialRequired" column="special_required"/>
        <result property="similarityCode" column="similarity_code"/>
        <result property="similarityFirstId" column="similarity_first_id"/>
        <result property="similarityFirstInfo" column="similarity_first_info"/>
        <result property="changeTime" column="change_time"/>
        <result property="tyreExposedFlag" column="tyre_exposed_flag"/>
        <result property="carLengthLabels" column="car_length_labels"/>
        <result property="shuntingQuantity" column="shunting_quantity"/>
        <result property="firstPublishType" column="first_publish_type"/>
        <result property="publishType" column="publish_type"/>
        <result property="infoFee" column="info_fee"/>
        <result property="isDelete" column="is_delete"/>
        <result property="exclusiveType" column="exclusive_type"/>
        <result property="totalScore" column="total_score"/>
        <result property="rankLevel" column="rank_level"/>
        <result property="isShow" column="is_show"/>
        <result property="refundFlag" column="refund_flag"/>
        <result property="sourceType" column="source_type"/>
        <result property="tradeNum" column="trade_num"/>
        <result property="authName" column="auth_name"/>
        <result property="labelJson" column="label_json"/>
        <result property="guaranteeGoods" column="guarantee_goods"/>
        <result property="creditRetop" column="credit_retop"/>
        <result property="sortType" column="sort_type"/>
        <result property="priorityRecommendExpireTime" column="priority_recommend_expire_time"/>
        <result property="excellentGoods" column="excellent_goods"/>
        <result property="driverDriving" column="driver_driving"/>
        <result property="tecServiceFee" column="tec_service_fee"/>
        <result property="machineRemark" column="machine_remark"/>
        <result property="excellentCardId" column="excellent_card_id"/>
        <result property="invoiceTransport" column="invoice_transport"/>
        <result property="additionalPrice" column="additional_price"/>
        <result property="enterpriseTaxRate" column="enterprise_tax_rate"/>
    </resultMap>

    <select id="selectTransportById" resultType="com.tyt.plat.entity.base.TytTransportVO">
        select id id,user_id userId,src_msg_id srcMsgId  from tyt_transport where src_msg_id = #{0} and status = 1
    </select>
    
    <select id="getSimilarityList" resultMap="TytTransportResultMap">
        SELECT * FROM tyt_transport
        WHERE `status` = 1
        AND display_type = 1
        AND is_display = 1
        AND src_msg_id != #{similarityFirstId}
        <if test="filterUserIds!=null and filterUserIds.size()>0">
            AND user_id NOT IN (<foreach collection="filterUserIds" item="item" separator=",">#{item}</foreach>)
        </if>
        AND similarity_code = #{similarityCode}
        ORDER BY total_score DESC, ctime
        LIMIT 30
    </select>

    <select id="getTsIdBySrcMsgId" resultType="java.lang.Long">
        select id
        from tyt_transport
        where src_msg_id = #{srcMsgId}
          and status = 1
        order by id desc
            limit 1
    </select>
</mapper>
