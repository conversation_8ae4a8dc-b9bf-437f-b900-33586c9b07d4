<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportQuotedPriceMapper">
  <resultMap id="BaseResultMap" type="com.tyt.transportquotedprice.bean.TytTransportQuotedPrice">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="car_id" jdbcType="BIGINT" property="carId" />
    <result column="car_user_name" jdbcType="VARCHAR" property="carUserName" />
    <result column="transport_user_id" jdbcType="BIGINT" property="transportUserId" />
    <result column="transport_user_name" jdbcType="VARCHAR" property="transportUserName" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="car_quoted_price" jdbcType="INTEGER" property="carQuotedPrice" />
    <result column="transport_quoted_price" jdbcType="INTEGER" property="transportQuotedPrice" />
    <result column="car_is_done" jdbcType="INTEGER" property="carIsDone" />
    <result column="transport_is_done" jdbcType="INTEGER" property="transportIsDone" />
    <result column="final_quoted_price_is_done" jdbcType="INTEGER" property="finalQuotedPriceIsDone" />
    <result column="final_quoted_price" jdbcType="INTEGER" property="finalQuotedPrice" />
    <result column="final_quoted_price_type" jdbcType="INTEGER" property="finalQuotedPriceType" />
    <result column="car_quoted_price_time" jdbcType="TIMESTAMP" property="carQuotedPriceTime" />
    <result column="transport_quoted_price_time" jdbcType="TIMESTAMP" property="transportQuotedPriceTime" />
    <result column="car_quoted_price_times" jdbcType="INTEGER" property="carQuotedPriceTimes" />
    <result column="transport_quoted_price_times" jdbcType="INTEGER" property="transportQuotedPriceTimes" />
    <result column="quoted_type" jdbcType="INTEGER" property="quotedType" />
    <result column="transport_num" jdbcType="INTEGER" property="transportNum" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
  </resultMap>

  <select id="getQuotedPriceByCarUserIdAndTransportMainId" resultMap="BaseResultMap">
    select *
    from tyt_transport_quoted_price where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
  </select>

  <select id="getQuotedPriceById" resultMap="BaseResultMap">
    select *
    from tyt_transport_quoted_price where id = #{transportQuotedPriceId}
  </select>

  <select id="getQuotedPriceListByTransportMainId" resultMap="BaseResultMap">
    select *
    from tyt_transport_quoted_price where src_msg_id = #{srcMsgId} order by car_quoted_price_time desc;
  </select>

  <select id="getQuotedPriceListByTransportMainIdOrderByCarQuotedPriceTime" resultMap="BaseResultMap">
    select *
    from tyt_transport_quoted_price where src_msg_id = #{srcMsgId} order by car_quoted_price_time desc
  </select>

  <select id="getTransportHaveAnyQuotedPrice" resultType="java.lang.Long">
    select src_msg_id
    from tyt_transport_quoted_price
    where transport_user_id = #{transportUserId} and final_quoted_price_is_done = 0
      and car_quoted_price_time between #{todayBeginTime} and #{todayEndTime}
  </select>

  <select id="getCountTransportMainIsValidForIdList" resultType="java.lang.Integer">
    select count(1) from tyt_transport_main where id in (
    <foreach collection="srcMsgIdList" index="index" item="id" separator=",">
      #{id}
    </foreach>
      )
    and status = 1 and pub_date between #{todayBeginTime} and #{todayEndTime} limit 1
  </select>

  <select id="getTransportMainIsValidSrcMsgIdsForIdList" resultType="long">
    select src_msg_id from tyt_transport_main where id in (
    <foreach collection="srcMsgIdList" index="index" item="id" separator=",">
      #{id}
    </foreach>
    )
    and status = 1 and pub_date between #{todayBeginTime} and #{todayEndTime}
  </select>

  <select id="getCarHaveNewTransportQuotedPrice" resultType="java.lang.Long">
    select src_msg_id
    from tyt_transport_quoted_price
    where car_id = #{carUserId} and final_quoted_price_is_done = 0 and car_quoted_price_times = transport_quoted_price_times
      and car_quoted_price_time between #{todayBeginTime} and #{todayEndTime}
  </select>

  <select id="getCarHaveNewTransportQuotedPriceLastTime" resultType="date">
    select max(transport_quoted_price_time)
    from tyt_transport_quoted_price
    where car_id = #{carUserId} and final_quoted_price_is_done = 0 and car_quoted_price_times = transport_quoted_price_times
      and car_quoted_price_time between #{todayBeginTime} and #{todayEndTime}
      and src_msg_id in
    <foreach close=")" collection="srcMsgIdList" index="index" item="srcMsgId" open="(" separator=",">
      #{srcMsgId}
    </foreach>
  </select>

  <select id="getCarHaveAgreeTransportQuotedPrice" resultType="java.lang.Long">
    select src_msg_id
    from tyt_transport_quoted_price
    where car_id = #{carUserId} and final_quoted_price_is_done = 1 and final_quoted_price_type = 1
      and car_quoted_price_time between #{todayBeginTime} and #{todayEndTime}
  </select>

  <select id="getCarHaveAgreeTransportQuotedPriceLastTime" resultType="date">
    select max(transport_quoted_price_time)
    from tyt_transport_quoted_price
    where car_id = #{carUserId} and final_quoted_price_is_done = 1 and final_quoted_price_type = 1
      and car_quoted_price_time between #{todayBeginTime} and #{todayEndTime}
        and src_msg_id in
    <foreach close=")" collection="srcMsgIdList" index="index" item="srcMsgId" open="(" separator=",">
      #{srcMsgId}
    </foreach>
  </select>

  <select id="getCarAndThisTransportIsHaveNewTransportQuotedPrice" resultType="java.lang.Integer">
    select count(1)
    from tyt_transport_quoted_price
    where car_id = #{carUserId} and src_msg_id = #{srcMsgId} and final_quoted_price_is_done = 0 and car_quoted_price_times = transport_quoted_price_times
      and car_quoted_price_time between #{todayBeginTime} and #{todayEndTime}
  </select>

  <select id="getCarAndThisTransportIsHaveAgreeTransportQuotedPrice" resultType="java.lang.Integer">
    select count(1)
    from tyt_transport_quoted_price
    where car_id = #{carUserId} and src_msg_id = #{srcMsgId} and final_quoted_price_is_done = 1 and final_quoted_price_type = 1
      and car_quoted_price_time between #{todayBeginTime} and #{todayEndTime}
  </select>

  <select id="getTransportQuotedPriceCountBySrcMsgIs" resultType="java.lang.Integer">
    select count(1)
    from tyt_transport_quoted_price
    where src_msg_id = #{srcMsgId}
      and car_quoted_price_time between #{todayBeginTime} and #{todayEndTime}
  </select>

  <select id="getQuotedPriceListByCarId" resultType="com.tyt.transport.querybean.CallLogBean">
    SELECT
    tsm.`tel`,
    tsm.`tel3`,
    tsm.`tel4`,
    tsm.`nick_name` nickName,
    tsm.`start_point` startPosition,
    tsm.`dest_point` destPosition,
    tsm.`id` goodId,
    tsm.`src_msg_id` srcMsgId,
    UNIX_TIMESTAMP( ttqp.`car_quoted_price_time` )* 1000 callTime,
    tsm.`task_content` remark,
    UNIX_TIMESTAMP( tsm.`ctime` )* 1000 pubDate,
    tsm.`is_info_fee` isInfoFee,
    tsm.`status` goodStatus,
    tsm.loading_time loadingTime,
    tsm.unload_time unloadTime,
    tsm.begin_loading_time beginLoadingTime,
    tsm.begin_unload_time beginUnloadTime,
    tsm.weight weight,
    tsm.length length,
    tsm.wide width,
    tsm.high height,
    tsm.price price,
    tsm.reg_time regTime,
    tsm.`user_type` userType,
    tsm.`user_id` goodsUserId,
    tsm.`start_provinc` startProvinc,
    tsm.`start_city` startCity,
    tsm.`start_area` startArea,
    tsm.`dest_provinc` destProvinc,
    tsm.`dest_city` destCity,
    tsm.`refund_flag` refundFlag,
    tsm.`dest_area` destArea,
    tsm.`ts_order_no` tsOrderNo,
    tsm.publish_type publishType,
    tsm.info_fee infoFee,
    tso.pay_status payStatus,
    tso.rob_status robStatus,
    tso.pay_amount payAgencyMoney,
    ttqp.car_id callerId,
    tsm.exclusive_type exclusiveType,
    tsm.source_type sourceType,
    tsm.auth_name authName,
    tsm.guarantee_goods guaranteeGoods,
    tsm.rank_level rankLevel,
    tsm.start_longitude startLongitude,
    tsm.start_latitude startLatitude,
    tsm.label_json labelJson,
    tsm.priority_recommend_expire_time priorityRecommendExpireTime,
    tsm.excellent_goods excellentGoods,
    tsm.tec_service_fee tecServiceFee,
    tsm.machine_remark machineRemark,
    tsm.invoice_transport invoiceTransport,
    tsm.additional_price additionalPrice,
    tsm.enterprise_tax_rate enterpriseTaxRate,
    ttqp.reason
    FROM
    tyt_transport_quoted_price ttqp
    LEFT JOIN tyt_transport_main tsm ON ttqp.`src_msg_id` = tsm.id
    LEFT JOIN tyt_transport_orders tso ON tso.ts_order_no = tsm.ts_order_no AND tso.pay_user_id = ttqp.car_id
    where ttqp.`car_id`=#{userId} AND ttqp.`car_quoted_price_time` > #{startDate}
    ORDER BY ttqp.`car_quoted_price_time` DESC, tso.id DESC
  </select>

  <select id="getCarIsHaveQuotedPriceToTransport" resultType="java.lang.Integer">
    select count(1) from tyt_transport_quoted_price where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
  </select>

  <select id="getSrcMsgIdListByTransportUserIdAndDate" resultType="java.lang.Long">
    select src_msg_id from tyt_transport_quoted_price where transport_user_id = #{transportUserId} and car_quoted_price_time >= #{startDate}
  </select>

    <select id="getAllQuotedPriceListBySrcMsgIdList" resultMap="BaseResultMap">
      select *
      from tyt_transport_quoted_price where src_msg_id in
      <foreach close=")" collection="srcMsgIds" index="index" item="srcMsgId" open="(" separator=",">
        #{srcMsgId}
      </foreach>
      order by car_quoted_price_time desc
    </select>

  <select id="getTransportHaveOptionQuotedPriceCount" resultType="java.lang.Integer">
    select count(1)
    from tyt_transport_quoted_price
    where src_msg_id = #{srcMsgId} and (transport_quoted_price_times != 0 or (final_quoted_price_is_done = 1 and final_quoted_price_type = 1))
  </select>

  <select id="getQuotedPriceLastOneBySrcMsgId" resultMap="BaseResultMap">
    select *
    from tyt_transport_quoted_price
    where src_msg_id = #{srcMsgId} limit 1
  </select>

  <select id="countSystemQuotedPrice" resultType="java.lang.Integer">
    select count(1)
    from tyt_transport_quoted_price
    where src_msg_id = #{srcMsgId} and quoted_type = 1
  </select>

  <select id="getLatestQuotedRecord" resultMap="BaseResultMap">
    select *
    from tyt_transport_quoted_price
    where src_msg_id in
    <foreach collection="srcMsgIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by id desc limit 1
  </select>

  <insert id="firstCarQuotedPrice" useGeneratedKeys="true" keyProperty="id">
    insert into tyt_transport_quoted_price (car_id, car_user_name, transport_user_id, transport_user_name, src_msg_id, car_quoted_price, transport_quoted_price
                                           , car_is_done, transport_is_done, final_quoted_price_is_done, final_quoted_price, final_quoted_price_type
                                           , car_quoted_price_time, transport_quoted_price_time, car_quoted_price_times, transport_quoted_price_times, reason) VALUE (
    #{carUserId}, #{carUserName}, #{transportUserId}, #{transportUserName}, #{srcMsgId}, #{price}, null, 1, 0, 0, null, null, now(), null, 1, 0, #{reason})
  </insert>

  <insert id="firstCarQuotedPriceV2" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO tyt_transport_quoted_price (
      car_id, car_user_name, transport_user_id, transport_user_name, src_msg_id,
      car_quoted_price, transport_quoted_price,
      car_is_done, transport_is_done, final_quoted_price_is_done,
      final_quoted_price, final_quoted_price_type,
      car_quoted_price_time, transport_quoted_price_time,
      car_quoted_price_times, transport_quoted_price_times, reason
    ) VALUES (
               #{carId}, #{carUserName}, #{transportUserId}, #{transportUserName}, #{srcMsgId},
               #{carQuotedPrice}, null,
               1, 0, 0,
               null, null,
               now(), null,
               1, 0, #{reason}
             )
  </insert>

  <update id="subsequentCarQuotedPrice">
    update tyt_transport_quoted_price set car_user_name = #{carUserName}, car_quoted_price = #{price}, car_is_done = 1, transport_is_done = 0
                                        , car_quoted_price_time = now(), car_quoted_price_times = car_quoted_price_times + 1
    <if test="reason != null">
      , reason = #{reason}
    </if>
                                      where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
  </update>

  <update id="transportQuotedPrice">
    update tyt_transport_quoted_price set transport_quoted_price = #{price}, car_is_done = 0, transport_is_done = 1
                                        , transport_quoted_price_time = now(), transport_quoted_price_times = transport_quoted_price_times + 1
                                      where id = #{transportQuotedPriceId}
  </update>

  <update id="carAgree">
    update tyt_transport_quoted_price set final_quoted_price_is_done = 1, car_is_done = 1, transport_is_done = 1
                                        , final_quoted_price = transport_quoted_price, final_quoted_price_type = 2
                                        , car_quoted_price_time = now() where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
  </update>

  <update id="transportAgree">
    update tyt_transport_quoted_price set final_quoted_price_is_done = 1, car_is_done = 1, transport_is_done = 1
                                        , final_quoted_price = car_quoted_price, final_quoted_price_type = 1
                                        , transport_quoted_price_time = now() , price_assistant_auto_agree = #{priceAssistantAutoAgree}
                                      where id = #{transportQuotedPriceId}
  </update>

  <insert id="addTransportQuotedPriceBIDataLog" parameterType="com.tyt.plat.vo.remote.TytQuotedPriceBiData">
    insert into tyt_quoted_price_bi_data (src_msg_id, car_user_id, quoted_type, quoted_price, carry_price, create_time) VALUE
        (#{srcMsgId}, #{carUserId}, #{quotedType}, #{quotedPrice}, #{carryPrice}, #{createTime})
  </insert>

  <insert id="transportQuotedPriceModifyPriceLog">
    insert into quoted_modify_price_log (transport_quoted_price_id, src_msg_id, before_price, after_price, create_time, modify_time) VALUE
      (#{transportQuotedPriceId}, #{srcMsgId}, #{befortPrice}, #{afterPrice}, now(), now())
  </insert>
</mapper>