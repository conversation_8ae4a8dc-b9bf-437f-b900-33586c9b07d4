<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.AccountUpdateLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.AccountUpdateLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="new_phone" jdbcType="VARCHAR" property="newPhone" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="opt_id" jdbcType="BIGINT" property="optId" />
    <result column="opt_name" jdbcType="VARCHAR" property="optName" />
    <result column="update_type" jdbcType="INTEGER" property="updateType" />
    <result column="certificate_url" jdbcType="VARCHAR" property="certificateUrl" />
    <result column="cell_phone_change_edit_id" jdbcType="BIGINT" property="cellPhoneChangeEditId" />
  </resultMap>
  <sql id="BaseSql">
    id,user_id,cell_phone,new_phone,reason,ctime,opt_id,opt_name,update_type,certificate_url,cell_phone_change_edit_id
  </sql>

  <select id="countByTimePeriod" resultType="java.lang.Integer">
    select
    count(id)
    from account_update_log
    where  user_id = #{userId,jdbcType=BIGINT} and ctime >=#{startTime,jdbcType=VARCHAR}
  </select>

  <select id="selectRecentAccountUpdateLog" resultMap="BaseResultMap">
    select
    <include refid="BaseSql"/>
    from account_update_log
    where user_id = #{userId,jdbcType=BIGINT} order by id desc limit 1
  </select>

  <delete id="deleteFromTytCellphone">
    delete from tyt_cellphone where cell_phone= #{cellPhone,jdbcType=VARCHAR}
  </delete>

  <update id="updateIdentityAuthMobile">
    UPDATE tyt_user_identity_auth set mobile=#{mobile,jdbcType=VARCHAR} where user_id=#{userId,jdbcType=BIGINT}
  </update>

  <update id="updateBlacklistUser">
    UPDATE blacklist_user SET cell_phone=#{newPhone,jdbcType=VARCHAR} WHERE user_id=#{userId,jdbcType=BIGINT} and cell_phone=#{oldPhone}
  </update>

  <update id="updateUserCellphoneInfo">
    update tyt_user SET cell_phone = #{cellPhone,jdbcType=VARCHAR},ticket = #{ticket,jdbcType=VARCHAR},mtime = now() where id = #{userId,jdbcType=BIGINT}
  </update>
</mapper>