<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.TransportGoodModelFactorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TransportGoodModelFactorDO">
        <id column="id" property="id" />
        <result column="model_level" property="modelLevel" />
        <result column="weight_start" property="weightStart" />
        <result column="weight_end" property="weightEnd" />
        <result column="distance_start" property="distanceStart" />
        <result column="distance_end" property="distanceEnd" />
        <result column="price_lower" property="priceLower" />
        <result column="price_upper" property="priceUpper" />
        <result column="unit_price_lower" property="unitPriceLower" />
        <result column="unit_price_upper" property="unitPriceUpper" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="matchRules" resultMap="BaseResultMap">
        SELECT *
        FROM tyt_transport_good_model_factor
        WHERE #{weight} >= weight_start
          AND weight_end > #{weight}
          AND #{distance} >= distance_start
          AND distance_end > #{distance}
    </select>
</mapper>
