<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportTechnicalOrderMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportTechnicalOrder">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="pay_user_id" jdbcType="BIGINT" property="payUserId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="technical_service_no" jdbcType="VARCHAR" property="technicalServiceNo" />
    <result column="technical_service_fee" jdbcType="BIGINT" property="technicalServiceFee" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
</mapper>