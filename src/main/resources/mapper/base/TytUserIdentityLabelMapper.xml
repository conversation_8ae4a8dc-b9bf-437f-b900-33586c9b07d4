<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytUserIdentityLabelMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytUserIdentityLabel">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="goods_type_first" jdbcType="INTEGER" property="goodsTypeFirst" />
    <result column="goods_type_second" jdbcType="INTEGER" property="goodsTypeSecond" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="audit_goods_type_first" jdbcType="INTEGER" property="auditGoodsTypeFirst" />
    <result column="audit_goods_type_second" jdbcType="INTEGER" property="auditGoodsTypeSecond" />
    <result column="audit_goods_type_remark" jdbcType="VARCHAR" property="auditGoodsTypeRemark" />
  </resultMap>

  <select id="getUserGoodsType" resultMap="BaseResultMap">
    select id,
           user_id,
           goods_type_first,
           goods_type_second,
           create_time,
           modify_time,
           audit_goods_type_first,
           audit_goods_type_second,
           audit_goods_type_remark
    from tyt_user_identity_label
    where user_id=#{userId}
  </select>
</mapper>