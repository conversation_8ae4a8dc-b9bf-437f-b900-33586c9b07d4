<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytUserPermissionUsedMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytUserPermissionUsed">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="service_permission_type_id" jdbcType="VARCHAR" property="servicePermissionTypeId" />
    <result column="service_permission_type_name" jdbcType="VARCHAR" property="servicePermissionTypeName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="gain_id" jdbcType="BIGINT" property="gainId" />
  </resultMap>
</mapper>