<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytBlockConfigUserMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytBlockConfigUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />

    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
  </resultMap>


  <select id="getByIdAndUserId" resultMap="BaseResultMap">
    select id from tyt_block_config_user where config_id = #{id} and user_id = #{userId} and status = 1 limit 1
  </select>
</mapper>