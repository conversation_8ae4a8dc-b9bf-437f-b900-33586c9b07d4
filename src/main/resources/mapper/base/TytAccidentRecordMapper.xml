<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytAccidentRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytAccidentRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="primitive_type" jdbcType="VARCHAR" property="primitiveType" />
    <result column="subtype" jdbcType="VARCHAR" property="subtype" />
    <result column="remake" jdbcType="VARCHAR" property="remake" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="data_sample_json" jdbcType="VARCHAR" property="dataSampleJson" />
  </resultMap>
</mapper>