<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytMbCargoSyncInfoMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytMbCargoSyncInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cargo_id" jdbcType="BIGINT" property="cargoId"/>
        <result column="cargo_name" jdbcType="VARCHAR" property="cargoName"/>
        <result column="load_province_name" jdbcType="VARCHAR" property="loadProvinceName"/>
        <result column="load_city_name" jdbcType="VARCHAR" property="loadCityName"/>
        <result column="load_district_name" jdbcType="VARCHAR" property="loadDistrictName"/>
        <result column="load_detail_address" jdbcType="VARCHAR" property="loadDetailAddress"/>
        <result column="load_lat" jdbcType="DOUBLE" property="loadLat"/>
        <result column="load_lon" jdbcType="DOUBLE" property="loadLon"/>
        <result column="unload_province_name" jdbcType="VARCHAR" property="unloadProvinceName"/>
        <result column="unload_city_name" jdbcType="VARCHAR" property="unloadCityName"/>
        <result column="unload_district_name" jdbcType="VARCHAR" property="unloadDistrictName"/>
        <result column="unload_detail_address" jdbcType="VARCHAR" property="unloadDetailAddress"/>
        <result column="unload_lat" jdbcType="DOUBLE" property="unloadLat"/>
        <result column="unload_lon" jdbcType="DOUBLE" property="unloadLon"/>
        <result column="packing_types" jdbcType="VARCHAR" property="packingTypes"/>
        <result column="packing_type_desc" jdbcType="VARCHAR" property="packingTypeDesc"/>
        <result column="min_weight" jdbcType="DOUBLE" property="minWeight"/>
        <result column="max_weight" jdbcType="DOUBLE" property="maxWeight"/>
        <result column="truck_count" jdbcType="INTEGER" property="truckCount"/>
        <result column="truck_lengths" jdbcType="VARCHAR" property="truckLengths"/>
        <result column="truck_types" jdbcType="VARCHAR" property="truckTypes"/>
        <result column="truck_user_types" jdbcType="INTEGER" property="truckUserTypes"/>
        <result column="load_time" jdbcType="TIMESTAMP" property="loadTime"/>
        <result column="unload_time" jdbcType="TIMESTAMP" property="unloadTime"/>
        <result column="deposit_amt" jdbcType="BIGINT" property="depositAmt"/>
        <result column="deposit_return" jdbcType="INTEGER" property="depositReturn"/>
        <result column="expect_freight" jdbcType="BIGINT" property="expectFreight"/>
        <result column="deal_mode" jdbcType="INTEGER" property="dealMode"/>
        <result column="min_length" jdbcType="DOUBLE" property="minLength"/>
        <result column="max_length" jdbcType="DOUBLE" property="maxLength"/>
        <result column="min_width" jdbcType="DOUBLE" property="minWidth"/>
        <result column="max_width" jdbcType="DOUBLE" property="maxWidth"/>
        <result column="min_height" jdbcType="DOUBLE" property="minHeight"/>
        <result column="max_height" jdbcType="DOUBLE" property="maxHeight"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_telephone" jdbcType="VARCHAR" property="contactTelephone"/>
        <result column="shipper_id" jdbcType="BIGINT" property="shipperId"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="last_operate_action" jdbcType="INTEGER" property="lastOperateAction"/>
        <result column="start_point" jdbcType="VARCHAR" property="startPoint"/>
        <result column="dest_point" jdbcType="VARCHAR" property="destPoint"/>
        <result column="cargo_version" jdbcType="INTEGER" property="cargoVersion"/>
        <result column="del_reason" jdbcType="VARCHAR" property="delReason"/>
    </resultMap>

    <sql id="refId">
        tmcsi.`id`
        id,
        tmcsi.`cargo_id`
        cargoId,
        tmcsi.`cargo_name` cargoName,
        tmcsi.`load_province_name` loadProvinceName,
        tmcsi.`load_city_name` loadCityName,
        tmcsi.`load_district_name` loadDistrictName,
        tmcsi.`load_detail_address` loadDetailAddress,
        tmcsi.`load_lat` loadLat,
        tmcsi.`load_lon` loadLon,
        tmcsi.`unload_province_name` unloadProvinceName,
        tmcsi.`unload_city_name` unloadCityName,
        tmcsi.`unload_district_name` unloadDistrictName,
        tmcsi.`unload_detail_address` unloadDetailAddress,
        tmcsi.`unload_lat` unloadLat,
        tmcsi.`min_weight` minWeight,
        tmcsi.`truck_count` truckCount,
        tmcsi.`load_time` loadTime,
        tmcsi.`unload_time` unloadTime,
        tmcsi.`deposit_amt`/100 depositAmt,
        tmcsi.`deposit_return` depositReturn,
        tmcsi.`expect_freight`/100 expectFreight,
        tmcsi.`deal_mode` dealMode,
        tmcsi.`min_length` minLength,
        tmcsi.`min_width` minWidth,
        tmcsi.`min_height` minHeight,
        tmcsi.`contact_name` contactName,
        tmcsi.`contact_telephone` contactTelephone,
        tmcsi.`shipper_id` shipperId,
        tmcsi.`del_flag` delFlag,
        tmcsi.`start_point` startPoint,
        tmcsi.unload_lat unloadLat,
        tmcsi.unload_lon unloadLon,
        tmcsi.unload_time unloadTime,
        tmcsi.cargo_version cargoVersion,
        tmcsi.del_reason delReason,
        tmcsi.create_time createTime,
        tmcsi.truck_lengths truckLengths,
        tmcsi.truck_types truckTypes,
        tmcsi.max_weight maxWeight,
        tmcsi.`dest_point` destPoint
    </sql>


    <select id="selectYMMCargoById" resultType="com.tyt.plat.entity.base.TytMbCargoSyncInfo">
        select
        <include refid="refId"></include>
        from tyt_mb_cargo_sync_info tmcsi
        where tmcsi.cargo_id = #{cargoId}
    </select>

    <select id="getTransportYmmList" resultType="com.tyt.transport.querybean.TransportYmmListBean">
        select
        <include refid="refId"></include>
        from tyt_mb_cargo_sync_info tmcsi
            left join tyt_transport_mb_merge ttmm on ttmm.cargo_id = tmcsi.cargo_id
        <where>
            ttmm.id is null and tmcsi.del_flag = 0
            <if test="req.queryActionType !=null and req.queryActionType==2 ">
                AND tmcsi.id &lt; #{req.queryId}
            </if>

            <if test="req.queryActionType !=null and req.queryActionType==1 ">
                AND tmcsi.id &gt; #{req.queryId}
            </if>
            order by tmcsi.create_time desc limit 30
        </where>
    </select>
</mapper>