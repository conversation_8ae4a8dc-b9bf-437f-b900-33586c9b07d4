<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportExtendMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportExtend">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId"/>
        <result column="ts_id" jdbcType="BIGINT" property="tsId"/>
        <result column="use_car_type" jdbcType="INTEGER" property="useCarType"/>
        <result column="price_type" jdbcType="INTEGER" property="priceType"/>
        <result column="suggest_min_price" jdbcType="INTEGER" property="suggestMinPrice"/>
        <result column="suggest_max_price" jdbcType="INTEGER" property="suggestMaxPrice"/>
        <result column="fix_price_fast" jdbcType="INTEGER" property="fixPriceFast" />
        <result column="cost_price" jdbcType="INTEGER" property="costPrice" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="good_model_score" jdbcType="DECIMAL" property="goodModelScore" />
        <result column="lim_good_model_score" jdbcType="DECIMAL" property="limGoodModelScore" />
        <result column="commission_score" jdbcType="DECIMAL" property="commissionScore" />
        <result column="top_flag" jdbcType="INTEGER" property="topFlag" />
        <result column="seckill_goods" jdbcType="INTEGER" property="seckillGoods" />
        <result column="perk_price" jdbcType="INTEGER" property="perkPrice" />
        <result column="good_transport_label" jdbcType="INTEGER" property="goodTransportLabel" />
        <result column="good_transport_label_part" jdbcType="INTEGER" property="goodTransportLabelPart" />
        <result column="client_fusion" jdbcType="INTEGER" property="clientFusion" />
    </resultMap>
    <select id="selectByTsId" resultMap="BaseResultMap">
        select * from tyt_transport_extend where ts_id = #{tsId}
    </select>

</mapper>