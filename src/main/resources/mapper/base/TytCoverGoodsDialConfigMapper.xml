<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCoverGoodsDialConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCoverGoodsDialConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="del_flag" jdbcType="SMALLINT" property="delFlag"/>
        <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId"/>
        <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="x_time_in_seconds" jdbcType="INTEGER" property="xTimeInSeconds"/>
        <result column="y_time_in_seconds" jdbcType="INTEGER" property="yTimeInSeconds"/>
        <result column="n_times" jdbcType="INTEGER" property="nTimes"/>
        <result column="enable" jdbcType="SMALLINT" property="enable"/>
        <result column="grant_n_times" jdbcType="INTEGER" property="grantNTimes"/>
    </resultMap>

    <select id="selectMaxXTime" resultType="java.lang.Integer">
        select max(x_time_in_seconds)
        from tyt_cover_goods_dial_config
        where enable = 1
    </select>
</mapper>