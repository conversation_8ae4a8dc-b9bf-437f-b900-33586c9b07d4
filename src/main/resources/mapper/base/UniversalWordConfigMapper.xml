<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.mybatis.mapper.UniversalWordConfigMapper">

    <resultMap id="BaseResultMap" type="com.tyt.universalWordConfig.bean.UniversalWordConfigInfo">
        <id column="id" property="id" jdbcType="BIGINT"></id>
        <result column="site" property="site" jdbcType="VARCHAR"></result>
        <result column="code" property="code" jdbcType="VARCHAR"></result>
        <result column="content" property="content" jdbcType="VARCHAR"></result>
        <result column="remark" property="remark" jdbcType="VARCHAR"></result>
        <result column="type" property="type" jdbcType="INTEGER"></result>
        <result column="delete_status" property="deleteStatus" jdbcType="INTEGER"></result>
        <result column="status" property="status" jdbcType="INTEGER"></result>
        <result column="modify_user_id" property="modifyUserId" jdbcType="BIGINT"></result>
        <result column="modify_user_name" property="modifyUserName" jdbcType="VARCHAR"></result>
        <result column="ctime" property="ctime" jdbcType="DATE"></result>
        <result column="mtime" property="mtime" jdbcType="DATE"></result>
    </resultMap>

    <select id="getAllUniversalWordConfigInfoListByType" resultMap="BaseResultMap" parameterType="integer">
        select id, site, code, content, remark, type, delete_status, status, modify_user_id, modify_user_name, ctime, mtime
        from tyt_universal_word_config tuwc
        where delete_status = 0 and status = 1 and type like CONCAT('%', #{type}, '%')
        order by mtime desc
    </select>

    <select id="getAllUniversalWordConfigInfoListByCodeList" resultMap="BaseResultMap" parameterType="list">
        SELECT id, site, code, content, remark, type, delete_status, status, modify_user_id, modify_user_name, ctime, mtime
        from tyt_universal_word_config tuwc
        where delete_status = 0 and status = 1 and code in (
        <foreach collection="codes" item="code" separator=",">
            #{code}
        </foreach>
        )
    </select>

</mapper>