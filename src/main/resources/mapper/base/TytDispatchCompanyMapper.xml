<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytDispatchCompanyMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytDispatchCompany">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
        <result column="ding_talk_phone" jdbcType="VARCHAR" property="dingTalkPhone" />
        <result column="is_valid" jdbcType="TINYINT" property="isValid" />
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
        <result column="mtime" jdbcType="INTEGER" property="mtime" />
    </resultMap>

    <sql id="base_columns">
        id, user_id, user_name, cell_phone, ding_talk_phone, is_valid, ctime, mtime
    </sql>

    <select id="countByUserId" resultType="java.lang.Integer">
        select count(*)
        from tyt_dispatch_company
        where user_id = #{userId} and is_valid = 1
    </select>

    <select id="getByUserId" resultMap="BaseResultMap">
        select <include refid="base_columns"/>
        from tyt_dispatch_company
        where user_id = #{userId} and is_valid = 1
    </select>

</mapper>