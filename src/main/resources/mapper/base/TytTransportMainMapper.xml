<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportMainMapper">


    <select id="selectTransportById" resultType="com.tyt.plat.entity.base.TytTransportVO">
        select user_id userId from tyt_transport_main where src_msg_id = #{0}
    </select>

    <select id="hasUserTransportLast30Day" resultType="date">
        SELECT ctime
        FROM tyt_transport_main
        WHERE user_id = #{userId}
          AND ctime between DATE_SUB(CURDATE(), INTERVAL 30 DAY) and CURDATE()
          order by id desc LIMIT 1
    </select>

    <select id="getUserSomeDayTransportData" resultType="com.tyt.plat.entity.base.TytTransportVO">
        SELECT src_msg_id AS srcMsgId, `status`, publish_type AS publishType
        FROM tyt_transport_main
        WHERE user_id = #{userId}
          AND ctime between CONCAT(#{someDay}, ' 00:00:00') and CONCAT(#{someDay}, ' 23:59:59')
    </select>

    <select id="hasContactInSrcMsgIds" resultType="integer">
        SELECT 1
        FROM tyt_transport_dispatch_view
        WHERE src_msg_id IN ( <foreach collection="srcMsgIds" item="i" separator=","> #{i} </foreach> )
        AND contact_count > 0
        LIMIT 1
    </select>

    <select id="getFirstTransport" resultType="com.tyt.model.TransportMain">
        select *
        from tyt_transport_main FORCE INDEX (IDX_USERID_CTIME)
        where user_id = #{userId} order by ctime limit 1
    </select>

    <select id="getInReleaseTransportIdList" resultType="java.lang.Long">
        select src_msg_id
        from tyt_transport where status = 1 and user_id = #{transportUserId} and ctime >= #{todayStartDate}
    </select>

    <select id="getSameTransportInPublishCount" resultType="java.lang.Integer">
        select count(1) from (select id from tyt_transport_main where similarity_code = #{similarityCode} and id != #{srcMsgId}
                                                  and status = 1 AND ctime between CONCAT(#{today}, ' 00:00:00') and CONCAT(#{today}, ' 23:59:59') limit 1) tab
    </select>

    <select id="countByGoodTypeName" resultType="java.lang.Integer">
        select count(1)
        from tyt_transport_main
        where good_type_name = #{goodTypeName} and id in
        <foreach collection="srcMsgIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectDistanceBySrcMsgId" resultType="java.lang.Integer">
        select distance
        from tyt_transport_main
        where id = #{srcMsgId}
    </select>


    <select id="getSameStartCityAndDestCityTransportInPublish" resultType="java.lang.Integer">
        select count(1) from (select id from tyt_transport_main where start_city = #{startCity} and dest_city = #{destCity} and id != #{srcMsgId}
                                                  and status = 1 AND ctime between CONCAT(#{today}, ' 00:00:00') and CONCAT(#{today}, ' 23:59:59') limit 4) tab
    </select>

    <select id="getUserPublishingSrcMsgIds" resultType="java.lang.Long">
        select id
        from tyt_transport_main
        where user_id = #{userId} and status = 1 and ctime > current_date()
    </select>

</mapper>
