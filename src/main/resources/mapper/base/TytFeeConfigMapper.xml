<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytFeeConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytFeeConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="min_val" jdbcType="DECIMAL" property="minVal" javaType = "java.math.BigDecimal"/>
    <result column="unit" jdbcType="TINYINT" property="unit" />
    <result column="max_val" jdbcType="DECIMAL" property="maxVal" javaType = "java.math.BigDecimal" />
    <result column="ratio" jdbcType="DECIMAL" property="ratio" javaType = "INTEGER" />
    <result column="sort_id" jdbcType="INTEGER" property="sortId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
    <result column="del_status" jdbcType="TINYINT" property="delStatus" />
  </resultMap>

  <select id="listFeeConfig" resultType="com.tyt.plat.entity.base.TytFeeConfig">
     select id,type, min_val as minVal, max_val as maxVal, ratio, sort_id as sortId from tyt_fee_config  where del_status = 0 order by sort_id asc
  </select>

</mapper>