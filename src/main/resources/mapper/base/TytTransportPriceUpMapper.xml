<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportPriceUpMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportPriceUp">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="up_number" jdbcType="INTEGER" property="upNumber" />
    <result column="up_price" jdbcType="DECIMAL" property="upPrice" />
    <result column="before_price" jdbcType="DECIMAL" property="beforePrice" />
    <result column="after_price" jdbcType="DECIMAL" property="afterPrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>