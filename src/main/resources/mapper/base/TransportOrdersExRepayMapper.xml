<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TransportOrdersExRepayMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TransportOrdersExRepay">
        <id column="id" property="id" />
        <result column="ts_order_no" property="tsOrderNo" />
        <result column="order_id" property="orderId" />
        <result column="ex_party" property="exParty" />
        <result column="goods_user_id" property="goodsUserId" />
        <result column="car_user_id" property="carUserId" />
        <result column="compensate_amount" property="compensateAmount" />
        <result column="compensate_type" property="compensateType" />
        <result column="compensate_reason" property="compensateReason" />
        <result column="compensate_time" property="compensateTime" />
        <result column="repay_target" property="repayTarget" />
        <result column="repay_user_id" property="repayUserId" />
        <result column="repay_amount" property="repayAmount" />
        <result column="repay_status" property="repayStatus" />
        <result column="delete" property="delete" />
        <result column="pay_time" property="payTime" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <select id="myRepayList" resultType="com.tyt.plat.biz.repay.pojo.RepayListResp" parameterType="com.tyt.plat.biz.repay.pojo.RepayListReq">
        select er.id repayId, er.repay_amount repayAmount, er.order_id orderId, er.ts_order_no tsOrderNo, er.pay_time repayTime, o.start_point startPoint, o.dest_point destPoint, o.pub_user_name pubUserName
        from tyt_transport_waybill_ex_repay er left join tyt_transport_orders o on
        <if test="req.userType != null and req.userType == 1">
            er.car_user_id = o.pay_user_id
        </if>
        <if test="req.userType != null and req.userType == 2">
            er.goods_user_id = o.user_id
        </if>
        and er.order_id = o.id
        where er.repay_status = #{req.repayStatus}
        and er.repay_user_id = #{req.userId}
        order by er.modify_time desc
    </select>
</mapper>