<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytMqMessageCenterMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytMqMessageCenter">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="message_serial_num" jdbcType="VARCHAR" property="messageSerialNum" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="message_type" jdbcType="VARCHAR" property="messageType" />
    <result column="fail_count" jdbcType="INTEGER" property="failCount" />
    <result column="message_content" jdbcType="LONGVARCHAR" property="messageContent" />
  </resultMap>
</mapper>