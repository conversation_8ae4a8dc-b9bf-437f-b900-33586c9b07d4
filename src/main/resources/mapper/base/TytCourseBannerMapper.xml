<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCourseBannerMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCourseBanner">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="link_url" jdbcType="VARCHAR" property="linkUrl" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sort_number" jdbcType="INTEGER" property="sortNumber" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_man_id" jdbcType="BIGINT" property="createManId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_man_id" jdbcType="BIGINT" property="modifyManId" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
  </resultMap>

    <select id="getBannerList" resultMap="BaseResultMap">
      select
        id,
        type,
        title,
        img_url,
        link_url,
        enable,
        status,
        sort_number,
        remark
      from tyt_course_banner
      where status = 1
        and enable = 1
        and type = #{type}
      order by sort_number desc
    </select>

</mapper>