<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytAbtestConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytAbtestConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="enable" jdbcType="INTEGER" property="enable"/>
        <result column="default_type" jdbcType="INTEGER" property="defaultType"/>
    </resultMap>
    <select id="selectAllConfig" resultMap="BaseResultMap">
        select *
        from tyt_abtest_config
        where enable = 1
    </select>
</mapper>