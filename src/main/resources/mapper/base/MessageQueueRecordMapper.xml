<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.MessageQueueRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.MessageQueueRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="topic" jdbcType="VARCHAR" property="topic" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="ali_mq_id" jdbcType="VARCHAR" property="aliMqId" />
    <result column="msg_key" jdbcType="VARCHAR" property="msgKey" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="consume_count" jdbcType="INTEGER" property="consumeCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="msg_content" jdbcType="LONGVARCHAR" property="msgContent" />
  </resultMap>
</mapper>