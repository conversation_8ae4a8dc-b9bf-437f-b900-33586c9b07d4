<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.ExcellentGoodsUsingRecordMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.ExcellentGoodsUsingRecord">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="use_count" jdbcType="INTEGER" property="useCount"/>
        <result column="use_date" jdbcType="DATE" property="useDate"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
        <result column="call_no_price_use_count" jdbcType="INTEGER" property="callNoPriceUseCount"/>
        <result column="call_price_use_count" jdbcType="INTEGER" property="callPriceUseCount"/>
        <result column="fixed_price_use_count" jdbcType="INTEGER" property="fixedPriceUseCount"/>
    </resultMap>

    <select id="selectUsedCount" resultType="com.tyt.plat.entity.base.ExcellentGoodsUsingRecord">
        select sum(use_count) useCount
             , sum(call_no_price_use_count) callNoPriceUseCount, sum(call_price_use_count) callPriceUseCount, sum(fixed_price_use_count) fixedPriceUseCount
        from excellent_goods_using_record
        where user_id = #{userId}
        <if test="beginDay != null">
            and use_date &gt;= #{beginDay}
        </if>
        <if test="endDay != null">
            and use_date &lt;= #{endDay}
        </if>
    </select>

    <select id="selectByUserIdAndDate" resultType="com.tyt.plat.entity.base.ExcellentGoodsUsingRecord">
        select id, user_id userId,use_count useCount,use_date useDate,ctime,mtime
             , call_no_price_use_count callNoPriceUseCount, call_price_use_count callPriceUseCount, fixed_price_use_count fixedPriceUseCount
        from excellent_goods_using_record where user_id = #{userId} and use_date = #{useDate}
    </select>

    <select id="selectListByUserIdAndDate" resultType="com.tyt.plat.entity.base.ExcellentGoodsUsingRecord">
        select user_id userId,use_count useCount,use_date useDate,ctime,mtime from excellent_goods_using_record where user_id = #{userId} and use_date>=#{useDate}
    </select>
</mapper>