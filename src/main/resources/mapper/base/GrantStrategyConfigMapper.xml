<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.GrantStrategyConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.model.GrantStrategyConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="grant_type" jdbcType="VARCHAR" property="grantType"/>
        <result column="times_to_grant" jdbcType="INTEGER" property="timesToGrant"/>
        <result column="gain_type" jdbcType="VARCHAR" property="gainType"/>
        <result column="gain_num" jdbcType="INTEGER" property="gainNum"/>
        <result column="goods_use_num" jdbcType="INTEGER" property="goodsUseNum"/>
        <result column="goods_effective_time" jdbcType="INTEGER" property="goodsEffectiveTime"/>
        <result column="gain_type_id" jdbcType="BIGINT" property="gainTypeId"/>
        <result column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="stop_type" jdbcType="VARCHAR" property="stopType"/>
        <result column="enable_flag" jdbcType="INTEGER" property="enableFlag"/>
        <result column="times_to_stop" jdbcType="INTEGER" property="timesToStop"/>
        <result column="repeat_frequency" jdbcType="BIGINT" property="repeatFrequency"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
    </resultMap>

    <select id="selectByGrantType" resultMap="BaseResultMap">
        select * from tyt_grant_strategy_config where enable_flag=1 and grant_type = #{grantType} and now()>=begin_time and now() &lt;= end_time order by create_time desc
    </select>

</mapper>