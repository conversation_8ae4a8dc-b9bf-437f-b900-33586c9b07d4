<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.CsMaintainedCustomMapper">
  <resultMap id="BaseResultMap" type="com.tyt.model.CsMaintainedCustom">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="belong_to" jdbcType="SMALLINT" property="belongTo" />
    <result column="custom_id" jdbcType="BIGINT" property="customId" />
    <result column="custom_phone" jdbcType="VARCHAR" property="customPhone" />
    <result column="maintainer_id" jdbcType="BIGINT" property="maintainerId" />
    <result column="maintainer_name" jdbcType="VARCHAR" property="maintainerName" />
    <result column="goods_maintainer_id" jdbcType="BIGINT" property="goodsMaintainerId" />
    <result column="goods_maintainer_name" jdbcType="VARCHAR" property="goodsMaintainerName" />
    <result column="last_comm_time" jdbcType="TIMESTAMP" property="lastCommTime" />
    <result column="no_pay_reason_one" jdbcType="BIGINT" property="noPayReasonOne" />
    <result column="not_pay_reason_two" jdbcType="BIGINT" property="notPayReasonTwo" />
    <result column="no_pay_reason_three" jdbcType="BIGINT" property="noPayReasonThree" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="modify_id" jdbcType="BIGINT" property="modifyId" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="rvisit_time" jdbcType="TIMESTAMP" property="rvisitTime" />
    <result column="user_label" jdbcType="VARCHAR" property="userLabel" />
    <result column="is_need_defender" jdbcType="SMALLINT" property="isNeedDefender" />
    <result column="is_need_move" jdbcType="SMALLINT" property="isNeedMove" />
    <result column="is_need_show" jdbcType="SMALLINT" property="isNeedShow" />
    <result column="is_need_force_show" jdbcType="SMALLINT" property="isNeedForceShow" />
    <result column="show_time" jdbcType="TIMESTAMP" property="showTime" />
    <result column="intention_rank" jdbcType="INTEGER" property="intentionRank" />
    <result column="previous_maintainer_id" jdbcType="BIGINT" property="previousMaintainerId" />
    <result column="previous_maintainer_name" jdbcType="VARCHAR" property="previousMaintainerName" />
    <result column="area_type" jdbcType="SMALLINT" property="areaType" />
    <result column="empower_status" jdbcType="SMALLINT" property="empowerStatus" />
    <result column="goods_intention" jdbcType="SMALLINT" property="goodsIntention" />
    <result column="dispatcher_id" jdbcType="BIGINT" property="dispatcherId" />
    <result column="dispatcher_name" jdbcType="VARCHAR" property="dispatcherName" />
    <result column="binding_dispatch_time" jdbcType="TIMESTAMP" property="bindingDispatchTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="goods_owner" jdbcType="INTEGER" property="goodsOwner" />
    <result column="goods_sync" property="goodsSync" jdbcType="INTEGER" />
  </resultMap>


  <select id="getCsMaintainedCustomByUserid" resultMap="BaseResultMap">
    select * from cs_maintained_custom where status = 1 and custom_id = #{userId}
  </select>

</mapper>