<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.LotteryRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.LotteryRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_call_phone" jdbcType="VARCHAR" property="userCallPhone" />
    <result column="promo_activity_id" jdbcType="BIGINT" property="promoActivityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="draw_activity_info_id" jdbcType="BIGINT" property="drawActivityInfoId" />
    <result column="prob_coupon_id" jdbcType="BIGINT" property="probCouponId" />
    <result column="prob_coupon_name" jdbcType="VARCHAR" property="probCouponName" />
    <result column="award_type" jdbcType="TINYINT" property="awardType" />
    <result column="coupon_amount" jdbcType="DECIMAL" property="couponAmount" />
    <result column="expiration_time" jdbcType="BIGINT" property="expirationTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
</mapper>