<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytOwnerAuthMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytOwnerAuth">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="auth_name" jdbcType="VARCHAR" property="authName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="op_name" jdbcType="VARCHAR" property="opName" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
</mapper>