<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSpecialCarDispatchDetailMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSpecialCarDispatchDetail">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dispatch_id" jdbcType="BIGINT" property="dispatchId" />
    <result column="ts_id" jdbcType="BIGINT" property="tsId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="pay_link_phone" jdbcType="VARCHAR" property="payLinkPhone" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="distance" jdbcType="DECIMAL" property="distance" />
    <result column="car_info_id" jdbcType="BIGINT" property="carInfoId" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="dispatch_status" jdbcType="INTEGER" property="dispatchStatus" />
    <result column="dispatch_error_msg" jdbcType="VARCHAR" property="dispatchErrorMsg" />
    <result column="dispatch_time" jdbcType="TIMESTAMP" property="dispatchTime" />
    <result column="accept_status" jdbcType="INTEGER" property="acceptStatus" />
    <result column="accept_time" jdbcType="TIMESTAMP" property="acceptTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="manual_assign" jdbcType="TINYINT" property="manualAssign" />
    <result column="car_id" jdbcType="BIGINT" property="carId" />
    <result column="car_type_match" jdbcType="INTEGER" property="carTypeMatch" />
  </resultMap>
  <select id="selectCountByUserAndGoodsId" resultType="java.lang.Integer">
    select count(*) from tyt_special_car_dispatch_detail where user_id = #{userId} and ts_id = #{goodsId} and dispatch_status = 1
  </select>

  <select id="selectCountByGoodsId" resultType="java.lang.Integer">
    select count(*) from tyt_special_car_dispatch_detail where ts_id = #{goodsId} and dispatch_status = 1
  </select>

</mapper>