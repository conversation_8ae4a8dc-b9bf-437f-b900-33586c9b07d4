<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSpecialCarAutoDispatchConfigMapper">
	<resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSpecialCarAutoDispatchConfig">
		<!--
		  WARNING - @mbg.generated
		-->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="start_city" jdbcType="VARCHAR" property="startCity" />
		<result column="dest_city" jdbcType="VARCHAR" property="destCity" />
		<result column="distance_limit" jdbcType="INTEGER" property="distanceLimit" />
		<result column="favor_rate" jdbcType="DECIMAL" property="favorRate" />
		<result column="receive_rate" jdbcType="DECIMAL" property="receiveRate" />
		<result column="dispatch_type" jdbcType="INTEGER" property="dispatchType" />
		<result column="max_dispatch_num" jdbcType="INTEGER" property="maxDispatchNum" />
		<result column="after_minutes" jdbcType="INTEGER" property="afterMinutes" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
		<result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
		<result column="op_user_name" jdbcType="VARCHAR" property="opUserName" />
		<result column="del_status" jdbcType="INTEGER" property="delStatus" />
	</resultMap>

	<select id="selectDispatchConfigByRoute" resultMap="BaseResultMap">
		select *
		from tyt_special_car_auto_dispatch_config
		where del_status = 0 and status = 1 and start_city = #{startCity} and dest_city = #{destCity}
		limit 1
	</select>
</mapper>