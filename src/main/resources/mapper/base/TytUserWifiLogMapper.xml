<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytUserWifiLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytUserWifiLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="wifi_name" jdbcType="VARCHAR" property="wifiName" />
    <result column="escalation_time" jdbcType="TIMESTAMP" property="escalationTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>