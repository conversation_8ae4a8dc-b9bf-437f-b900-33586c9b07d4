<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCarTypeLincenseMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCarTypeLincense">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="car_type_two" jdbcType="VARCHAR" property="carTypeTwo" />
    <result column="license_type" jdbcType="VARCHAR" property="licenseType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <select id="getByName" resultType="java.lang.String">
        select license_type from tyt_car_type_lincense where car_type = #{carType} limit 1

  </select>
</mapper>