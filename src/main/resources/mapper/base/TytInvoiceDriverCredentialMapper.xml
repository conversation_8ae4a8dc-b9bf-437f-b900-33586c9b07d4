<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytInvoiceDriverCredentialMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytInvoiceDriverCredential">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="invoice_driver_id" jdbcType="INTEGER" property="invoiceDriverId"/>
        <result column="pic_type" jdbcType="TINYINT" property="picType"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="verify_status" jdbcType="TINYINT" property="verifyStatus"/>
        <result column="verify_desc" jdbcType="VARCHAR" property="verifyDesc"/>
        <result column="orc_json" jdbcType="VARCHAR" property="orcJson"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectByDriverId" resultMap="BaseResultMap">
        select *
        from tyt_invoice_driver_credential
        where invoice_driver_id = #{driverId}
        order by pic_type asc
    </select>

</mapper>