<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportOrderSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportOrderSnapshot">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="snapshot_time" jdbcType="TIMESTAMP" property="snapshotTime" />
    <result column="start_point" jdbcType="VARCHAR" property="startPoint" />
    <result column="dest_point" jdbcType="VARCHAR" property="destPoint" />
    <result column="task_content" jdbcType="VARCHAR" property="taskContent" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="pub_time" jdbcType="VARCHAR" property="pubTime" />
    <result column="pub_qq" jdbcType="BIGINT" property="pubQq" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="user_show_name" jdbcType="VARCHAR" property="userShowName" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="source" jdbcType="SMALLINT" property="source" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="upload_cellphone" jdbcType="VARCHAR" property="uploadCellphone" />
    <result column="resend" jdbcType="SMALLINT" property="resend" />
    <result column="start_coord" jdbcType="VARCHAR" property="startCoord" />
    <result column="dest_coord" jdbcType="VARCHAR" property="destCoord" />
    <result column="plat_id" jdbcType="SMALLINT" property="platId" />
    <result column="verify_flag" jdbcType="SMALLINT" property="verifyFlag" />
    <result column="price" jdbcType="VARCHAR" property="price" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="price_code" jdbcType="VARCHAR" property="priceCode" />
    <result column="start_coord_x" jdbcType="INTEGER" property="startCoordX" />
    <result column="start_coord_y" jdbcType="INTEGER" property="startCoordY" />
    <result column="dest_coord_x" jdbcType="INTEGER" property="destCoordX" />
    <result column="dest_coord_y" jdbcType="INTEGER" property="destCoordY" />
    <result column="start_detail_add" jdbcType="VARCHAR" property="startDetailAdd" />
    <result column="start_longitude" jdbcType="INTEGER" property="startLongitude" />
    <result column="start_latitude" jdbcType="INTEGER" property="startLatitude" />
    <result column="dest_detail_add" jdbcType="VARCHAR" property="destDetailAdd" />
    <result column="dest_longitude" jdbcType="INTEGER" property="destLongitude" />
    <result column="dest_latitude" jdbcType="INTEGER" property="destLatitude" />
    <result column="pub_date" jdbcType="TIMESTAMP" property="pubDate" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="weight_code" jdbcType="VARCHAR" property="weightCode" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="length" jdbcType="VARCHAR" property="length" />
    <result column="wide" jdbcType="VARCHAR" property="wide" />
    <result column="high" jdbcType="VARCHAR" property="high" />
    <result column="is_superelevation" jdbcType="CHAR" property="isSuperelevation" />
    <result column="linkman" jdbcType="VARCHAR" property="linkman" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="distance" jdbcType="INTEGER" property="distance" />
    <result column="pub_goods_time" jdbcType="TIMESTAMP" property="pubGoodsTime" />
    <result column="tel3" jdbcType="VARCHAR" property="tel3" />
    <result column="tel4" jdbcType="VARCHAR" property="tel4" />
    <result column="display_type" jdbcType="CHAR" property="displayType" />
    <result column="hash_code" jdbcType="VARCHAR" property="hashCode" />
    <result column="is_car" jdbcType="CHAR" property="isCar" />
    <result column="user_type" jdbcType="SMALLINT" property="userType" />
    <result column="pc_old_content" jdbcType="VARCHAR" property="pcOldContent" />
    <result column="resend_counts" jdbcType="INTEGER" property="resendCounts" />
    <result column="verify_photo_sign" jdbcType="SMALLINT" property="verifyPhotoSign" />
    <result column="user_part" jdbcType="INTEGER" property="userPart" />
    <result column="start_city" jdbcType="VARCHAR" property="startCity" />
    <result column="start_provinc" jdbcType="VARCHAR" property="startProvinc" />
    <result column="start_area" jdbcType="VARCHAR" property="startArea" />
    <result column="dest_provinc" jdbcType="VARCHAR" property="destProvinc" />
    <result column="dest_city" jdbcType="VARCHAR" property="destCity" />
    <result column="dest_area" jdbcType="VARCHAR" property="destArea" />
    <result column="client_version" jdbcType="VARCHAR" property="clientVersion" />
    <result column="is_info_fee" jdbcType="CHAR" property="isInfoFee" />
    <result column="info_status" jdbcType="CHAR" property="infoStatus" />
    <result column="ts_order_no" jdbcType="VARCHAR" property="tsOrderNo" />
    <result column="release_time" jdbcType="TIMESTAMP" property="releaseTime" />
    <result column="reg_time" jdbcType="TIMESTAMP" property="regTime" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="good_type_name" jdbcType="VARCHAR" property="goodTypeName" />
    <result column="good_number" jdbcType="INTEGER" property="goodNumber" />
    <result column="is_standard" jdbcType="TINYINT" property="isStandard" />
    <result column="match_item_id" jdbcType="INTEGER" property="matchItemId" />
    <result column="android_distance" jdbcType="INTEGER" property="androidDistance" />
    <result column="ios_distance" jdbcType="INTEGER" property="iosDistance" />
    <result column="is_display" jdbcType="SMALLINT" property="isDisplay" />
    <result column="refer_length" jdbcType="INTEGER" property="referLength" />
    <result column="refer_width" jdbcType="INTEGER" property="referWidth" />
    <result column="refer_height" jdbcType="INTEGER" property="referHeight" />
    <result column="refer_weight" jdbcType="INTEGER" property="referWeight" />
    <result column="car_length" jdbcType="VARCHAR" property="carLength" />
    <result column="loading_time" jdbcType="TIMESTAMP" property="loadingTime" />
    <result column="begin_unload_time" jdbcType="TIMESTAMP" property="beginUnloadTime" />
    <result column="unload_time" jdbcType="TIMESTAMP" property="unloadTime" />
    <result column="car_min_length" jdbcType="DECIMAL" property="carMinLength" />
    <result column="car_max_length" jdbcType="DECIMAL" property="carMaxLength" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="begin_loading_time" jdbcType="TIMESTAMP" property="beginLoadingTime" />
    <result column="car_style" jdbcType="VARCHAR" property="carStyle" />
    <result column="work_plane_min_high" jdbcType="DECIMAL" property="workPlaneMinHigh" />
    <result column="work_plane_max_high" jdbcType="DECIMAL" property="workPlaneMaxHigh" />
    <result column="work_plane_min_length" jdbcType="DECIMAL" property="workPlaneMinLength" />
    <result column="work_plane_max_length" jdbcType="DECIMAL" property="workPlaneMaxLength" />
    <result column="climb" jdbcType="VARCHAR" property="climb" />
    <result column="order_number" jdbcType="INTEGER" property="orderNumber" />
    <result column="evaluate" jdbcType="INTEGER" property="evaluate" />
    <result column="special_required" jdbcType="VARCHAR" property="specialRequired" />
    <result column="similarity_code" jdbcType="VARCHAR" property="similarityCode" />
    <result column="similarity_first_id" jdbcType="BIGINT" property="similarityFirstId" />
    <result column="similarity_first_info" jdbcType="VARCHAR" property="similarityFirstInfo" />
    <result column="tyre_exposed_flag" jdbcType="VARCHAR" property="tyreExposedFlag" />
    <result column="car_length_labels" jdbcType="VARCHAR" property="carLengthLabels" />
    <result column="shunting_quantity" jdbcType="INTEGER" property="shuntingQuantity" />
    <result column="first_publish_type" jdbcType="SMALLINT" property="firstPublishType" />
    <result column="publish_type" jdbcType="SMALLINT" property="publishType" />
    <result column="info_fee" jdbcType="DECIMAL" property="infoFee" />
    <result column="is_delete" jdbcType="SMALLINT" property="isDelete" />
    <result column="exclusive_type" jdbcType="INTEGER" property="exclusiveType" />
    <result column="total_score" jdbcType="DECIMAL" property="totalScore" />
    <result column="rank_level" jdbcType="INTEGER" property="rankLevel" />
    <result column="refund_flag" jdbcType="INTEGER" property="refundFlag" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
    <result column="auth_name" jdbcType="VARCHAR" property="authName" />
    <result column="label_json" jdbcType="VARCHAR" property="labelJson" />
    <result column="guarantee_goods" jdbcType="INTEGER" property="guaranteeGoods" />
  </resultMap>

  <select id="countToBeAllocateBySrcMsgId" resultType="integer">
    select count(1) from tyt_transport_order_snapshot
    where src_msg_id=#{srcMsgId}
    and order_allocate_state = 3
  </select>
</mapper>