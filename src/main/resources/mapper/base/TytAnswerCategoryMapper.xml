<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytAnswerCategoryMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytAnswerCategory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="belong_type" jdbcType="INTEGER" property="belongType" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sort_number" jdbcType="INTEGER" property="sortNumber" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

    <select id="getCategoryList" resultMap="BaseResultMap">
      select
        id,
        belong_type,
        category_name,
        icon_url,
        create_time,
        modify_time,
        status,
        sort_number,
        remark
      from
        tyt_answer_category
      where status = 1
        and show_scope = 2
        and belong_type = #{belongType}
      order by sort_number desc
        limit 1000
    </select>

  <select id="getCategoryListByBelongType" resultMap="BaseResultMap">
    select
      id,
      belong_type,
      category_name,
      icon_url,
      create_time,
      modify_time,
      status,
      sort_number,
      remark
    from
      tyt_answer_category
    where status = 1
      and show_scope = 2
      and belong_type = #{belongType}
  </select>

</mapper>