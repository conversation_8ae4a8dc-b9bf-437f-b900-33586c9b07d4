<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytMbCargoSyncLogMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytMbCargoSyncLog">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="cargo_info_id" property="cargoInfoId"/>
        <result column="cargo_id" property="cargoId"/>
        <result column="sync_type" property="syncType"/>
        <result column="info_json" property="infoJson"/>
        <result column="cargo_version" property="cargoVersion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        cargo_info_id,
        cargo_id,
        sync_type,
        info_json,
        cargo_version
    </sql>

    <select id="selectLastByCargoId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tyt_mb_cargo_sync_log where cargo_id = #{cargoId}
        order by cargo_version desc limit 1
    </select>
    <select id="selectByCargoIdAndVersion" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tyt_mb_cargo_sync_log where cargo_id = #{cargoId} and cargo_version = #{cargoVersion}
        order by create_time desc limit 1
    </select>
</mapper>
