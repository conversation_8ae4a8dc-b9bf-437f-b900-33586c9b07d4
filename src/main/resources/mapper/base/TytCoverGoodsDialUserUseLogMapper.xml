<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCoverGoodsDialUserUseLogMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCoverGoodsDialUserUseLog">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="ts_id" jdbcType="BIGINT" property="tsId"/>
        <result column="use_reason" jdbcType="VARCHAR" property="useReason"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="change_num" jdbcType="TIMESTAMP" property="changeNum"/>
        <result column="change_type" jdbcType="INTEGER" property="changeType"/>
        <result column="expire_time" jdbcType="INTEGER" property="expireTime"/>
    </resultMap>

    <select id="selectUserUseLogByUserId" resultMap="BaseResultMap">
        select *
        from tyt_cover_goods_dial_user_use_log
        where user_id = #{userId}
        order by id desc
    </select>


    <insert id="insertUserUseLogByParams">
        INSERT INTO `tyt`.`tyt_cover_goods_dial_user_use_log` (`user_id`, `ts_id`, `use_reason`, `create_time`, `update_time`)
        VALUES (#{userId}, #{tsId}, #{useReason}, now(), now())
    </insert>

    <select id="countByUserIdAndChangeType" resultType="java.lang.Integer">
        select count(*)
        from tyt_cover_goods_dial_user_use_log
        where user_id = #{userId}
          and change_type = #{changeType}
    </select>
</mapper>