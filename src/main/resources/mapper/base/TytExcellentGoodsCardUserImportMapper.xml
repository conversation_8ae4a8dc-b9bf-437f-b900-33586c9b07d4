<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytExcellentGoodsCardUserImportMapper">
  <resultMap id="BaseResultMap" type="com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserImport">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="first_refresh_times" jdbcType="INTEGER" property="firstRefreshTimes" />
    <result column="first_refresh_interval" jdbcType="INTEGER" property="firstRefreshInterval" />
    <result column="second_refresh_times" jdbcType="INTEGER" property="secondRefreshTimes" />
    <result column="second_refresh_interval" jdbcType="INTEGER" property="secondRefreshInterval" />
    <result column="plan_grant_num" jdbcType="INTEGER" property="planGrantNum" />
    <result column="reality_grant_num" jdbcType="INTEGER" property="realityGrantNum" />
    <result column="valid_date_begin" jdbcType="TIMESTAMP" property="validDateBegin" />
    <result column="valid_date_end" jdbcType="TIMESTAMP" property="validDateEnd" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
  </resultMap>
</mapper>