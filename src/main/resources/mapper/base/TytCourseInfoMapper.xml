<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCourseInfoMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCourseInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="cover_img_url" jdbcType="VARCHAR" property="coverImgUrl" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="summary" jdbcType="VARCHAR" property="summary" />
    <result column="template_type" jdbcType="INTEGER" property="templateType" />
    <result column="link_url" jdbcType="VARCHAR" property="linkUrl" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sort_number" jdbcType="INTEGER" property="sortNumber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_man_id" jdbcType="BIGINT" property="createManId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_man_id" jdbcType="BIGINT" property="modifyManId" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
  </resultMap>

    <select id="getCourseList" resultMap="BaseResultMap">
        select
        id,
        category_id,
        type,
        cover_img_url,
        title,
        summary,
        template_type,
        link_url,
        enable,
        status,
        sort_number
        from tyt_course_info
        where type = #{type}
        and enable = 1
        and status = 1
        <if test="categoryId != null">
            and category_id = #{categoryId}
        </if>
        <if test="sortNumber != null">
            and sort_number &lt; #{sortNumber}
        </if>
        order by sort_number desc
        limit 20
    </select>

    <select id="getMyCourseList" resultMap="BaseResultMap">
      select
        ci.id,
        ci.category_id,
        ci.type,
        ci.cover_img_url,
        ci.title,
        ci.summary,
        ci.template_type,
        ci.link_url,
        ci.enable,
        ci.status,
        ci.sort_number
      from tyt_course_user cu
      inner join tyt_course_info ci on cu.course_id = ci.id
      where ci.type = #{type}
        and ci.enable = 1
        and ci.status = 1
        and cu.user_id = #{userId}
        <if test="learnFlag == 1">
            and cu.study_status = 2
        </if>
        <if test="learnFlag != 1">
            and cu.study_status != 2
        </if>
        <if test="sortNumber != null">
            and ci.sort_number &lt; #{sortNumber}
        </if>
        order by ci.sort_number desc
        limit 20
    </select>

</mapper>