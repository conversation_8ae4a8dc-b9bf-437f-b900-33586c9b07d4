<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCourseContentMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCourseContent">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="course_id" jdbcType="BIGINT" property="courseId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="content_text" jdbcType="LONGVARCHAR" property="contentText" />
  </resultMap>
</mapper>