<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytContractGmvRateConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytContractGmvRateConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmv_range" jdbcType="VARCHAR" property="gmvRange" />
    <result column="freight_rate" jdbcType="VARCHAR" property="freightRate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

    <select id="getRateConfigList" resultMap="BaseResultMap">
      select * from tyt_contract_gmv_rate_config
      order by id
        limit 10
    </select>

</mapper>