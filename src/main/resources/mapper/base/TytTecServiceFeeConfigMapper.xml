<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTecServiceFeeConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTecServiceFeeConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_transport_type" jdbcType="INTEGER" property="applyTransportType" />
    <result column="special_carcooperative_type" jdbcType="INTEGER" property="specialCarCooperativeType" />
    <result column="refund_flag_type" jdbcType="INTEGER" property="refundFlagType" />
    <result column="car_member_type" jdbcType="INTEGER" property="carMemberType" />
    <result column="price_publish_type" jdbcType="INTEGER" property="pricePublishType" />
    <result column="free_tec_service_fee_type" jdbcType="INTEGER" property="freeTecServiceFeeType" />
    <result column="free_tec_service_fee_view_count" jdbcType="INTEGER" property="freeTecServiceFeeViewCount" />
    <result column="free_tec_service_fee_call_count" jdbcType="INTEGER" property="freeTecServiceFeeCallCount" />
    <result column="free_tec_service_fee_time" jdbcType="INTEGER" property="freeTecServiceFeeTime" />
    <result column="privacy_phone_type" jdbcType="INTEGER" property="privacyPhoneType" />
    <result column="interests_word" jdbcType="VARCHAR" property="interestsWord" />
    <result column="interests_url" jdbcType="VARCHAR" property="interestsUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_user_id" jdbcType="BIGINT" property="modifyUserId" />
    <result column="modify_user_name" jdbcType="VARCHAR" property="modifyUserName" />
  </resultMap>

  <select id="getByConditionsUseToCompute" parameterType="com.tyt.plat.entity.base.TytTecServiceFeeConfig" resultMap="BaseResultMap">
    SELECT * FROM tyt_tec_service_fee_config
    WHERE apply_transport_type = #{applyTransportType}
      <if test="applyTransportType == 1">
          AND special_carcooperative_type = #{specialCarCooperativeType}
      </if>
      AND (refund_flag_type = #{refundFlagType} or refund_flag_type = 0)
      AND (price_publish_type = #{pricePublishType} or price_publish_type = 0) limit 1
  </select>

  <select id="getMatchConditionLastViewTime" resultType="java.util.Date">
      SELECT ctime
      FROM tyt_transport_view_log
      WHERE ts_id = #{srcMsgId}
        and ctime is not null and ctime >= #{todayStartDate} and ctime &lt; #{tomorrowStartDate}
      ORDER BY ctime
          LIMIT 1 OFFSET #{freeTecServiceFeeViewCount}
  </select>

  <select id="getMatchConditionLastCallTime" resultType="java.util.Date">
      SELECT create_time
      FROM tyt_app_call_log
      WHERE src_msg_id = #{srcMsgId}
        and create_time is not null and create_time &gt;= #{todayStartDate} and create_time &lt; #{tomorrowStartDate}
      ORDER BY create_time
          LIMIT 1 OFFSET #{freeTecServiceFeeCallCount}
  </select>

</mapper>