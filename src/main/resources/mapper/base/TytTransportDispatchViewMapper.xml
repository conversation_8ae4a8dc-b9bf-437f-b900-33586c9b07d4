<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportDispatchViewMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportDispatchView">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId"/>
        <result column="car_user_id" jdbcType="BIGINT" property="carUserId"/>
        <result column="car_user_name" jdbcType="VARCHAR" property="carUserName"/>
        <result column="car_nick_name" jdbcType="VARCHAR" property="carNickName"/>
        <result column="car_phone" jdbcType="VARCHAR" property="carPhone"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="remark_user_id" jdbcType="BIGINT" property="remarkUserId"/>
        <result column="remark_user_name" jdbcType="VARCHAR" property="remarkUserName"/>
        <result column="remark_time" jdbcType="TIMESTAMP" property="remarkTime"/>
        <result column="view_count" jdbcType="INTEGER" property="viewCount"/>
        <result column="contact_count" jdbcType="INTEGER" property="contactCount"/>
        <result column="view_time" jdbcType="TIMESTAMP" property="viewTime"/>
        <result column="contact_time" jdbcType="TIMESTAMP" property="contactTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
</mapper>