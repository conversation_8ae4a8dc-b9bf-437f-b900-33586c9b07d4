<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.OwnerCompanyLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.OwnerCompanyLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="backend_id" jdbcType="BIGINT" property="backendId" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>