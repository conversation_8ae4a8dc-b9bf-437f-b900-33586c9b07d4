<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.deposit.mapper.base.TytDepositBlockMapper">
  <resultMap id="BaseResultMap" type="com.tyt.deposit.entity.base.TytDepositBlock">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="block_status" jdbcType="SMALLINT" property="blockStatus" />
    <result column="block_type" jdbcType="SMALLINT" property="blockType" />
    <result column="permanent_block" jdbcType="SMALLINT" property="permanentBlock" />
    <result column="del_flag" jdbcType="SMALLINT" property="delFlag" />
    <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId" />
    <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="block_begin_time" jdbcType="TIMESTAMP" property="blockBeginTime" />
    <result column="block_end_time" jdbcType="TIMESTAMP" property="blockEndTime" />
    <result column="sms_notify" jdbcType="SMALLINT" property="smsNotify" />
  </resultMap>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select * from tyt_deposit_block where user_id= #{userId}
    </select>
</mapper>