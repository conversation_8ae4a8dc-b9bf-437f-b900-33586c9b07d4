<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.BlackListIdentityCardMapper">

    <select id="getByIdCard" resultType="com.tyt.plat.entity.base.BlacklistIdentityCard">
        select id, identity_card identityCard, status, reason, create_time createTime, modify_time modifyTime
        from blacklist_identity_card
        where identity_card = #{idCard} limit 1
    </select>
</mapper>