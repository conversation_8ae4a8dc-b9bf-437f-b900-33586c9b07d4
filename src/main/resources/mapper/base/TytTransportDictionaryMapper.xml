<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportDictionaryMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportDictionary">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="keyword" jdbcType="VARCHAR" property="keyword"/>
        <result column="rate" jdbcType="INTEGER" property="rate"/>
        <result column="correct_word" jdbcType="VARCHAR" property="correctWord"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <select id="checkDictionaryChange" resultType="java.lang.Long">
        select id from tyt_transport_dictionary
        <where>
            <if test="modifyTime != null and modifyTime != ''">
                and modify_time &gt; #{modifyTime}
            </if>
        </where>
        limit 1
    </select>

    <select id="getPageDictionaryList" resultMap="BaseResultMap">
        select *
        from tyt_transport_dictionary
        where id &gt; #{maxId}
          and status = 1
        order by id asc
            limit #{pageSize}
    </select>


    <select id="selectAll" resultMap="BaseResultMap">
        select *
        from tyt_transport_dictionary
        where status = 1
    </select>


</mapper>