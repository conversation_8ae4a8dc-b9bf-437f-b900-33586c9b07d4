<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytInvoiceTransportConfigLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytInvoiceTransportConfigLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="max_tonnage" jdbcType="DOUBLE" property="maxTonnage" />
    <result column="max_tonnage_is_change" jdbcType="INTEGER" property="maxTonnageIsChange" />
    <result column="max_length" jdbcType="DOUBLE" property="maxLength" />
    <result column="max_length_is_change" jdbcType="INTEGER" property="maxLengthIsChange" />
    <result column="max_width" jdbcType="DOUBLE" property="maxWidth" />
    <result column="max_width_is_change" jdbcType="INTEGER" property="maxWidthIsChange" />
    <result column="max_height" jdbcType="DOUBLE" property="maxHeight" />
    <result column="max_height_is_change" jdbcType="INTEGER" property="maxHeightIsChange" />
    <result column="max_total_transport_price" jdbcType="DOUBLE" property="maxTotalTransportPrice" />
    <result column="max_total_transport_price_is_change" jdbcType="INTEGER" property="maxTotalTransportPriceIsChange" />
    <result column="max_price" jdbcType="VARCHAR" property="maxPrice" />
    <result column="load_need_load_stage" jdbcType="INTEGER" property="loadNeedLoadStage" />
    <result column="unload_need_load_stage" jdbcType="INTEGER" property="unloadNeedLoadStage" />
    <result column="get_need_load_stage" jdbcType="INTEGER" property="getNeedLoadStage" />
    <result column="car_weight" jdbcType="DECIMAL" property="carWeight" />
    <result column="car_width" jdbcType="DECIMAL" property="carWidth" />
    <result column="car_height" jdbcType="DECIMAL" property="carHeight" />
    <result column="car_length" jdbcType="DECIMAL" property="carLength" />
    <result column="load_need_load_photo" jdbcType="INTEGER" property="loadNeedLoadPhoto" />
    <result column="get_need_load_photo" jdbcType="INTEGER" property="getNeedLoadPhoto" />
    <result column="unload_need_unload_photo" jdbcType="INTEGER" property="unloadNeedUnloadPhoto" />
    <result column="get_need_unload_photo" jdbcType="INTEGER" property="getNeedUnloadPhoto" />
    <result column="unlod_need_receipt_photo" jdbcType="INTEGER" property="unlodNeedReceiptPhoto" />
    <result column="get_need_receipt_photo" jdbcType="INTEGER" property="getNeedReceiptPhoto" />
    <result column="update_price_config" jdbcType="INTEGER" property="updatePriceConfig" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="take_order_car_require_type" jdbcType="INTEGER" property="takeOrderCarRequireType" />
    <result column="car_require_auth" jdbcType="INTEGER" property="carRequireAuth" />
    <result column="car_require_wait_auth" jdbcType="INTEGER" property="carRequireWaitAuth" />
    <result column="car_require_conform" jdbcType="INTEGER" property="carRequireConform" />
    <result column="car_require_no_conform" jdbcType="INTEGER" property="carRequireNoConform" />
    <result column="can_assign_car" jdbcType="INTEGER" property="canAssignCar" />
    <result column="invoice_service_type" jdbcType="INTEGER" property="invoiceServiceType" />
    <result column="load_driver_car_photo" jdbcType="INTEGER" property="loadDriverCarPhoto" />
    <result column="unload_driver_car_photo" jdbcType="INTEGER" property="unloadDriverCarPhoto" />
    <result column="segmented_payments" jdbcType="INTEGER" property="segmentedPayments" />

  </resultMap>

  <resultMap id="EnterpriseResultMap" type="com.tyt.plat.entity.base.TytInvoiceTransportEnterpriseConfigLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="max_tonnage" jdbcType="DOUBLE" property="maxTonnage" />
    <result column="max_tonnage_is_change" jdbcType="INTEGER" property="maxTonnageIsChange" />
    <result column="max_length" jdbcType="DOUBLE" property="maxLength" />
    <result column="max_length_is_change" jdbcType="INTEGER" property="maxLengthIsChange" />
    <result column="max_width" jdbcType="DOUBLE" property="maxWidth" />
    <result column="max_width_is_change" jdbcType="INTEGER" property="maxWidthIsChange" />
    <result column="max_height" jdbcType="DOUBLE" property="maxHeight" />
    <result column="max_height_is_change" jdbcType="INTEGER" property="maxHeightIsChange" />
    <result column="max_total_transport_price" jdbcType="DOUBLE" property="maxTotalTransportPrice" />
    <result column="max_total_transport_price_is_change" jdbcType="INTEGER" property="maxTotalTransportPriceIsChange" />
    <result column="max_price" jdbcType="VARCHAR" property="maxPrice" />
    <result column="load_need_load_stage" jdbcType="INTEGER" property="loadNeedLoadStage" />
    <result column="unload_need_load_stage" jdbcType="INTEGER" property="unloadNeedLoadStage" />
    <result column="get_need_load_stage" jdbcType="INTEGER" property="getNeedLoadStage" />
    <result column="car_weight" jdbcType="DECIMAL" property="carWeight" />
    <result column="car_width" jdbcType="DECIMAL" property="carWidth" />
    <result column="car_height" jdbcType="DECIMAL" property="carHeight" />
    <result column="car_length" jdbcType="DECIMAL" property="carLength" />
    <result column="load_need_load_photo" jdbcType="INTEGER" property="loadNeedLoadPhoto" />
    <result column="get_need_load_photo" jdbcType="INTEGER" property="getNeedLoadPhoto" />
    <result column="unload_need_unload_photo" jdbcType="INTEGER" property="unloadNeedUnloadPhoto" />
    <result column="get_need_unload_photo" jdbcType="INTEGER" property="getNeedUnloadPhoto" />
    <result column="unlod_need_receipt_photo" jdbcType="INTEGER" property="unlodNeedReceiptPhoto" />
    <result column="get_need_receipt_photo" jdbcType="INTEGER" property="getNeedReceiptPhoto" />
    <result column="update_price_config" jdbcType="INTEGER" property="updatePriceConfig" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="take_order_car_require_type" jdbcType="INTEGER" property="takeOrderCarRequireType" />
    <result column="car_require_auth" jdbcType="INTEGER" property="carRequireAuth" />
    <result column="car_require_wait_auth" jdbcType="INTEGER" property="carRequireWaitAuth" />
    <result column="car_require_conform" jdbcType="INTEGER" property="carRequireConform" />
    <result column="car_require_no_conform" jdbcType="INTEGER" property="carRequireNoConform" />
    <result column="can_assign_car" jdbcType="INTEGER" property="canAssignCar" />
    <result column="segmented_payments" jdbcType="INTEGER" property="segmentedPayments" />
  </resultMap>

  <select id="getLastTytInvoiceTransportConfig" resultMap="BaseResultMap">
    select *
    from tyt_invoice_transport_config_log order by id desc limit 1
  </select>

  <select id="isHaveLastTytInvoiceTransportEnterpriseConfig" resultType="java.lang.Integer">
    select count(1)
    from tyt_invoice_transport_enterprise_config_log where enterprise_id = #{enterpriseId} limit 1
  </select>

  <select id="getTytInvoiceTransportPriceConfig" resultType="com.tyt.invoicetransport.bean.TytInvoiceTransportPriceConfig">
    select high_tonnage_value as highTonnageValue, low_tonnage_value as lowTonnageValue
         , high_distance_value as highDistanceValue, low_distance_value as lowDistanceValue
         , max_price as maxPrice, max_distance_unit_price as maxDistanceUnitPrice from tyt_invoice_transport_price_config
  </select>

  <select id="getLastTytInvoiceTransportEnterpriseConfig" resultMap="EnterpriseResultMap">
    select *
    from tyt_invoice_transport_enterprise_config_log where enterprise_id = #{enterpriseId} order by id desc limit 1
  </select>

  <select id="getTytInvoiceTransportPriceEnterpriseConfig"
          resultType="com.tyt.invoicetransport.bean.TytInvoiceTransportPriceConfig">
    select high_tonnage_value as highTonnageValue, low_tonnage_value as lowTonnageValue
         , high_distance_value as highDistanceValue, low_distance_value as lowDistanceValue
         , max_price as maxPrice, max_distance_unit_price as maxDistanceUnitPrice from tyt_invoice_transport_price_enterprise_config
    where enterprise_id = #{enterpriseId}
  </select>

  <select id="getLastInvoiceConfigByServiceType" resultMap="BaseResultMap">
    select id, max_tonnage, max_tonnage_is_change, max_length, max_length_is_change, max_width, max_width_is_change
         , max_height, max_height_is_change, max_total_transport_price, max_total_transport_price_is_change, update_price_config, create_time
         , create_user_id, create_user_name, max_price, load_need_load_stage, unload_need_load_stage, get_need_load_stage
         , car_weight, car_width, car_height, car_length, load_need_load_photo, get_need_load_photo
         , unload_need_unload_photo, get_need_unload_photo, unlod_need_receipt_photo, get_need_receipt_photo
         , take_order_car_require_type, car_require_auth, car_require_wait_auth, car_require_conform, car_require_no_conform, can_assign_car,invoice_service_type,load_driver_car_photo,unload_driver_car_photo,segmented_payments
    from tyt_invoice_transport_config_log where invoice_service_type = #{invoiceServiceType} order by id desc limit 1
  </select>

</mapper>