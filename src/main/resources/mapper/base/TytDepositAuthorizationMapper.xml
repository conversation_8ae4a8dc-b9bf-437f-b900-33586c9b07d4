<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.deposit.mapper.base.TytDepositAuthorizationMapper">
    <resultMap id="BaseResultMap" type="com.tyt.deposit.entity.base.TytDepositAuthorization">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="auth_status" jdbcType="INTEGER" property="authStatus"/>
        <result column="auth_time" jdbcType="TIMESTAMP" property="authTime"/>
        <result column="black_status" jdbcType="INTEGER" property="blackStatus"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
        <result column="user_group" jdbcType="INTEGER" property="userGroup"/>
        <result column="operate_id" jdbcType="BIGINT" property="operateId"/>
        <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
        <!--优车V2 新增 -->
        <result column="require_amount" jdbcType="DECIMAL" property="requireAmount"/>
        <result column="refund_limit_status" jdbcType="INTEGER" property="refundLimitStatus"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, user_id, auth_status, auth_time, black_status, delete_status, user_group,operate_id, operate_user_name,
     remark, ctime, mtime, require_amount, refund_limit_status
  </sql>

    <select id="selectDepositAuthByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_deposit_authorization
        where user_id=#{userId,jdbcType=BIGINT} and delete_status = 0 limit 1
    </select>

    <select id="getByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_deposit_authorization
        where user_id=#{userId,jdbcType=BIGINT} and auth_status = 1 and delete_status = 0 and black_status = 0   limit 1
    </select>


</mapper>