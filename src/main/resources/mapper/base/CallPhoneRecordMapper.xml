<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.CallPhoneRecordMapper">
	<resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.CallPhoneRecordDO">
		<!--
		  WARNING - @mbg.generated
		-->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
		<result column="car_user_id" jdbcType="BIGINT" property="carUserId" />
		<result column="car_user_name" jdbcType="VARCHAR" property="carUserName" />
		<result column="car_is_vip" jdbcType="SMALLINT" property="carIsVip" />
		<result column="path" jdbcType="VARCHAR" property="path" />
		<result column="module" jdbcType="VARCHAR" property="module" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="plat_id" jdbcType="VARCHAR" property="platId" />
	</resultMap>

	<select id="getLatestCallRecord" resultMap="BaseResultMap">
		select *
		from call_phone_record
		where src_msg_id in
		<foreach collection="srcMsgIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		order by id desc limit 1
	</select>
</mapper>