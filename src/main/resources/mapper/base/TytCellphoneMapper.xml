<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCellphoneMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCellphone">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <select id="selectByCellPhone" resultMap="BaseResultMap">
    select * from tyt_cellphone where cell_phone = #{cellPhone,jdbcType=VARCHAR} limit 1
  </select>

  <insert id="insertCellPhoneToTemp">
    INSERT INTO `tyt_cellphone` (`cell_phone`) VALUES(#{cellPhone,jdbcType=VARCHAR})
  </insert>

  <delete id="deleteFromTytCellphone">
    delete from tyt_cellphone where cell_phone= #{cellPhone,jdbcType=VARCHAR}
  </delete>
</mapper>