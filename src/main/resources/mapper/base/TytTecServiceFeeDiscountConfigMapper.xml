<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tyt.plat.mapper.base.TytTecServiceFeeDiscountConfigMapper">

    <!-- Result Map 定义 -->
    <resultMap id="TytTecServiceFeeDiscountConfigMap" type="com.tyt.plat.entity.base.TytTecServiceFeeDiscountConfig">
        <id column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="stage_id" property="stageId"/>
        <result column="proportion_id" property="proportionId"/>
        <result column="discount_time" property="discountTime"/>
        <result column="discount" property="discount"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <select id="getTytTecServiceFeeDiscountConfigByProportionId" resultMap="TytTecServiceFeeDiscountConfigMap">
        select * from tyt_tec_service_fee_discount_config where proportion_id = #{proportionId} order by discount_time
    </select>

    <select id="getTytTecServiceFeeDiscountConfigByProportionIdAndMin" resultMap="TytTecServiceFeeDiscountConfigMap">
        select * from tyt_tec_service_fee_discount_config where proportion_id = #{proportionId} and discount_time is not null and discount_time >= #{matchConditionEarliestTimeBetweenNowMin} order by discount_time limit 1
    </select>

</mapper>