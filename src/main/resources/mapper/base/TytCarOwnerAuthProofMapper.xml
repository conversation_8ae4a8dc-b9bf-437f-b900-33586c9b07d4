<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCarOwnerAuthProofMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCarOwnerAuthProof">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="car_owner_auth_id" jdbcType="BIGINT" property="carOwnerAuthId" />
    <result column="certificate_material_url" jdbcType="VARCHAR" property="certificateMaterialUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
</mapper>