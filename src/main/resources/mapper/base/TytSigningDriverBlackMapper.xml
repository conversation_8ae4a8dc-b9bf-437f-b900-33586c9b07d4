<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSigningDriverBlackMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSigningDriverBlack">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="sign_status" jdbcType="INTEGER" property="signStatus" />
    <result column="black_type" jdbcType="INTEGER" property="blackType" />
    <result column="restrict_num" jdbcType="INTEGER" property="restrictNum" />
    <result column="restrict_start_time" jdbcType="TIMESTAMP" property="restrictStartTime" />
    <result column="restrict_end_time" jdbcType="TIMESTAMP" property="restrictEndTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="getByDriverUserId" resultType="java.lang.Integer">
    SELECT count(*) FROM  tyt_signing_driver_black where user_id = #{driverUserId} and sign_status = 2
  </select>
</mapper>