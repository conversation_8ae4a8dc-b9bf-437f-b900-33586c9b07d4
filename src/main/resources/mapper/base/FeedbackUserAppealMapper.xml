<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.FeedbackUserAppealMapper">

    <sql id="queryBase">
        id,feedback_id feedbackId,appeal_status appealStatus,reject_reason rejectReason,appeal_time appealTime,action_user_id actionUserId
            ,action_user_name actionUserName,action_time actionTime,create_time createTime,modify_time modifyTime
    </sql>

    <select id="getAppealByFeedbackId" resultType="com.tyt.plat.entity.base.FeedbackUserAppeal">
        select
        <include refid="queryBase"/>
        from tyt.feedback_user_appeal where feedback_id = #{feedBackId}
    </select>
    <select id="getAppealByFeedBackIdList" resultType="com.tyt.plat.entity.base.FeedbackUserAppeal">
        select
            <include refid="queryBase"/>
        from tyt.feedback_user_appeal
        where feedback_id in
        <foreach item='id' index='index' collection='feedBackIdList' open='(' separator=',' close=')'>
            #{id}
        </foreach>
    </select>
</mapper>