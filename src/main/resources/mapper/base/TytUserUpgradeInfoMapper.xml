<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytUserUpgradeInfoMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytUserUpgradeInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="upgrade_table_id" jdbcType="BIGINT" property="upgradeTableId" />
    <result column="upgrade_source" jdbcType="TINYINT" property="upgradeSource" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="last_login_version" jdbcType="VARCHAR" property="lastLoginVersion" />
    <result column="upgrade_type" jdbcType="TINYINT" property="upgradeType" />
    <result column="upgrade_status" jdbcType="TINYINT" property="upgradeStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="getByUserIdAndUpgradeId" resultMap="BaseResultMap">
    select * from tyt_user_upgrade_info where user_id=#{userId} and upgrade_table_id=#{upgradeId} and upgrade_source=1 limit 1
  </select>

  <select id="getByUser" resultMap="BaseResultMap">
    select id, upgrade_table_id, user_id, last_login_version from tyt_user_upgrade_info where user_id=#{userId} and upgrade_source=1 and upgrade_status = 0 limit 10;
  </select>

  <update id="updateStatusById">
    update tyt_user_upgrade_info set upgrade_status = 1, last_login_version = #{userVersion} where id = #{id}
  </update>

</mapper>