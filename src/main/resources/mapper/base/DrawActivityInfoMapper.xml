<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.DrawActivityInfoMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.DrawActivityInfoEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="limit_times_type" jdbcType="INTEGER" property="limitTimesType" />
    <result column="limit_times" jdbcType="INTEGER" property="limitTimes" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="app_type" jdbcType="INTEGER" property="appType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="biz_check_sign" jdbcType="VARCHAR" property="bizCheckSign" />
  </resultMap>
</mapper>