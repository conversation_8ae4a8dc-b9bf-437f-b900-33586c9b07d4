<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCarTypeMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCarType">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="car_classify" jdbcType="TINYINT" property="carClassify" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>