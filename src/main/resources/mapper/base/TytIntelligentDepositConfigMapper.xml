<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytIntelligentDepositConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytIntelligentDepositConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_tonnage" jdbcType="INTEGER" property="startTonnage" />
    <result column="end_tonnage" jdbcType="INTEGER" property="endTonnage" />
    <result column="start_distance" jdbcType="INTEGER" property="startDistance" />
    <result column="end_distance" jdbcType="INTEGER" property="endDistance" />
    <result column="deposit" jdbcType="INTEGER" property="deposit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

    <select id="getConfigByWeightAndDistance" resultMap="BaseResultMap">
      select *
      from tyt_intelligent_deposit_config
      where start_tonnage &lt; #{weight} and end_tonnage &gt;= #{weight}
        and start_distance &lt; #{distance} and end_distance &gt;= #{distance}
    </select>
</mapper>