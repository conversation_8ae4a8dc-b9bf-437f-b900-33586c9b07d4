<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.DwMarketV4BrandInfoMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.DwMarketV4BrandInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="changer_id" jdbcType="BIGINT" property="changerId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="etime" jdbcType="TIMESTAMP" property="etime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="show_status" jdbcType="TINYINT" property="showStatus" />
    <result column="handle_status" jdbcType="TINYINT" property="handleStatus" />
    <result column="cooperate_status" jdbcType="TINYINT" property="cooperateStatus" />
  </resultMap>
</mapper>