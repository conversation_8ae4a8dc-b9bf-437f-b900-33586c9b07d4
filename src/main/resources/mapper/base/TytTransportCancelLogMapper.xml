<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportCancelLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportCancelLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="backout_reason_key" jdbcType="VARCHAR" property="backoutReasonKey" />
    <result column="backout_reason_value" jdbcType="INTEGER" property="backoutReasonValue" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="transport_json" jdbcType="LONGVARCHAR" property="transportJson" />
  </resultMap>
</mapper>