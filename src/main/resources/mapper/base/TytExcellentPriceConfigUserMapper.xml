<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytExcellentPriceConfigUserMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytExcellentPriceConfigUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_coefficient" jdbcType="DECIMAL" property="userCoefficient" />
    <result column="create_id" jdbcType="BIGINT" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="BIGINT" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getByConfigIdAndUserId" resultMap="BaseResultMap">
      select *
      from tyt_excellent_price_config_user
      where config_id = #{configId}
        and user_id = #{userId}

    </select>
</mapper>