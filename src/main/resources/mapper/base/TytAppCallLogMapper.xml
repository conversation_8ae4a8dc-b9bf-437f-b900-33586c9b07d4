<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytAppCallLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytAppCallLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="call_module" jdbcType="TINYINT" property="callModule" />
    <result column="from_car" jdbcType="VARCHAR" property="fromCar" />
    <result column="car_id" jdbcType="VARCHAR" property="carId" />
    <result column="call_time" jdbcType="TIMESTAMP" property="callTime" />
    <result column="caller_id" jdbcType="BIGINT" property="callerId" />
    <result column="called_info_id" jdbcType="BIGINT" property="calledInfoId" />
    <result column="call_result_code" jdbcType="TINYINT" property="callResultCode" />
    <result column="call_result_name" jdbcType="VARCHAR" property="callResultName" />
    <result column="pub_user_id" jdbcType="BIGINT" property="pubUserId" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="reference" jdbcType="VARCHAR" property="reference" />
    <result column="plat_id" jdbcType="VARCHAR" property="platId" />
    <result column="marker_owner_names" jdbcType="VARCHAR" property="markerOwnerNames" />
    <result column="marker_owner_codes" jdbcType="VARCHAR" property="markerOwnerCodes" />
  </resultMap>


  <select id="callDetail" resultMap="BaseResultMap" resultType="com.tyt.plat.entity.base.TytAppCallLog">
    SELECT * FROM tyt_app_call_log WHERE caller_id= #{userId} AND called_info_id= #{goodId}  and plat_id =#{clientSign}
  </select>

  <sql id="callLogQuery">
    and tacl.`caller_id`=#{userId}
    AND tacl.call_time > #{startDate}
    <if test="callResultCode != null and callResultCode > 0">
      AND tacl.`call_result_code`=#{callResultCode}
    </if>
    <if test="fromCar != null and fromCar != ''">
      AND tacl.`from_car`=#{fromCar}
    </if>
    <if test="cellPhone != null and cellPhone != ''">
      and (
        tsm.`tel` = #{cellPhone}
        or tsm.`tel3` = #{cellPhone}
        or tsm.`tel4` = #{cellPhone}
        or tsm.`upload_cellphone` = #{cellPhone}
      )
    </if>
  </sql>

  <select id="getCallLogCount" resultType="java.lang.Integer">
    SELECT count(*) FROM tyt_app_call_log tacl
    LEFT JOIN tyt_transport_main tsm ON tacl.`src_msg_id`=tsm.id
    LEFT JOIN tyt_transport_orders tso ON tso.ts_order_no = tsm.ts_order_no AND tso.pay_user_id = tacl.caller_id
    <where>
        <include refid="callLogQuery" />
    </where>
  </select>

  <select id="getCallLogList" resultType="com.tyt.transport.querybean.CallLogBean">
    SELECT
      tsm.`tel`,
      tsm.`tel3`,
      tsm.`tel4`,
      tsm.`nick_name` nickName,
      tsm.`start_point` startPosition,
      tsm.`dest_point` destPosition,
      tsm.`id` goodId,
      tsm.`src_msg_id` srcMsgId,
      UNIX_TIMESTAMP( tacl.`call_time` )* 1000 callTime,
      tacl.`call_result_code` callResultCode,
      tacl.`call_result_name` callResultName,
      tsm.`task_content` remark,
      UNIX_TIMESTAMP( tsm.`ctime` )* 1000 pubDate,
      tsm.`is_info_fee` isInfoFee,
      tsm.`status` goodStatus,
      tacl.reference `reference`,
      tsm.loading_time loadingTime,
      tsm.unload_time unloadTime,
      tsm.begin_loading_time beginLoadingTime,
      tsm.begin_unload_time beginUnloadTime,
      tsm.weight weight,
      tsm.length length,
      tsm.wide width,
      tsm.high height,
      tsm.price price,
      tsm.reg_time regTime,
      tsm.`user_type` userType,
      tsm.`user_id` goodsUserId,
      tsm.`start_provinc` startProvinc,
      tsm.`start_city` startCity,
      tsm.`start_area` startArea,
      tsm.`dest_provinc` destProvinc,
      tsm.`dest_city` destCity,
      tsm.`refund_flag` refundFlag,
      tsm.`dest_area` destArea,
      tsm.`ts_order_no` tsOrderNo,
      tsm.publish_type publishType,
      tsm.info_fee infoFee,
      tso.pay_status payStatus,
      tso.rob_status robStatus,
      tso.pay_amount payAgencyMoney,
      tacl.caller_id callerId,
      tsm.exclusive_type exclusiveType,
      tsm.source_type sourceType,
      tsm.auth_name authName,
      tsm.guarantee_goods guaranteeGoods,
      tsm.rank_level rankLevel,
      tsm.start_longitude startLongitude,
      tsm.start_latitude startLatitude,
      tsm.label_json labelJson,
      tsm.priority_recommend_expire_time priorityRecommendExpireTime,
      tsm.excellent_goods excellentGoods,
      tsm.tec_service_fee tecServiceFee,
      tsm.machine_remark machineRemark,
      tsm.invoice_transport invoiceTransport,
      tsm.additional_price additionalPrice,
      tsm.enterprise_tax_rate enterpriseTaxRate
    FROM
      tyt_app_call_log tacl
        LEFT JOIN tyt_transport_main tsm ON tacl.`src_msg_id` = tsm.id
        LEFT JOIN tyt_transport_orders tso ON tso.ts_order_no = tsm.ts_order_no
            AND tso.pay_user_id = tacl.caller_id
    <where>
      <include refid="callLogQuery" />
    </where>
    ORDER BY tacl.`call_time` DESC, tso.id DESC
  </select>

  <select id="getViewLogList" resultType="com.tyt.transport.querybean.CallLogBean">
    SELECT
      tsm.`tel`,
      tsm.`tel3`,
      tsm.`tel4`,
      tsm.`nick_name` nickName,
      tsm.`start_point` startPosition,
      tsm.`dest_point` destPosition,
      tsm.`id` goodId,
      tsm.`src_msg_id` srcMsgId,
      UNIX_TIMESTAMP( ttvl.`ctime` )* 1000 callTime,
      tsm.`task_content` remark,
      UNIX_TIMESTAMP( tsm.`ctime` )* 1000 pubDate,
      tsm.`is_info_fee` isInfoFee,
      tsm.`status` goodStatus,
      tsm.loading_time loadingTime,
      tsm.unload_time unloadTime,
      tsm.begin_loading_time beginLoadingTime,
      tsm.begin_unload_time beginUnloadTime,
      tsm.weight weight,
      tsm.length length,
      tsm.wide width,
      tsm.high height,
      tsm.price price,
      tsm.reg_time regTime,
      tsm.`user_type` userType,
      tsm.`user_id` goodsUserId,
      tsm.`start_provinc` startProvinc,
      tsm.`start_city` startCity,
      tsm.`start_area` startArea,
      tsm.`dest_provinc` destProvinc,
      tsm.`dest_city` destCity,
      tsm.`refund_flag` refundFlag,
      tsm.`dest_area` destArea,
      tsm.`ts_order_no` tsOrderNo,
      tsm.publish_type publishType,
      tsm.info_fee infoFee,
      tso.pay_status payStatus,
      tso.rob_status robStatus,
      tso.pay_amount payAgencyMoney,
      ttvl.user_id callerId,
      tsm.exclusive_type exclusiveType,
      tsm.source_type sourceType,
      tsm.auth_name authName,
      tsm.guarantee_goods guaranteeGoods,
      tsm.rank_level rankLevel,
      tsm.start_longitude startLongitude,
      tsm.start_latitude startLatitude,
      tsm.label_json labelJson,
      tsm.priority_recommend_expire_time priorityRecommendExpireTime,
      tsm.excellent_goods excellentGoods,
      tsm.tec_service_fee tecServiceFee,
      tsm.machine_remark machineRemark,
      tsm.invoice_transport invoiceTransport,
      tsm.additional_price additionalPrice,
      tsm.enterprise_tax_rate enterpriseTaxRate
    FROM
      tyt_transport_view_log ttvl
        LEFT JOIN tyt_transport_main tsm ON ttvl.`ts_id` = tsm.id
        LEFT JOIN tyt_transport_orders tso ON tso.ts_order_no = tsm.ts_order_no AND tso.pay_user_id = ttvl.user_id
    where ttvl.`user_id` = #{userId} AND ttvl.`ctime` > #{startDate}
    ORDER BY ttvl.`ctime` DESC
  </select>

  <select id="getLastAddMoneyTime" resultType="java.util.Date">
    select ctime
    from tyt_backout_reason where backout_reason = '7' and src_msg_id = #{tsId} order by ctime desc limit 1
  </select>

  <select id="getViewLogCountBySrcMsgId" resultType="java.lang.Integer">
    select count(1) from tyt_transport_view_log where ts_id = #{tsId}
  </select>

  <select id="getViewLogUserIdListBySrcMsgId" resultType="long">
    select user_id from tyt_transport_view_log where ts_id = #{tsId}
  </select>

  <select id="getCallLogCountBySrcMsgId" resultType="java.lang.Integer">
    select count(1) from tyt_app_call_log where src_msg_id = #{tsId}
  </select>

  <select id="getSrcMsgIdListByTransportUserIdAndDate" resultType="java.lang.Long">
    SELECT src_msg_id FROM tyt_app_call_log WHERE pub_user_id = #{transportUserId} and call_time >= #{startDate}
  </select>

</mapper>