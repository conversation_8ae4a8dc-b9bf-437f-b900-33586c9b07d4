<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportTecServiceFeeMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportTecServiceFee">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="member_before_fee" jdbcType="DECIMAL" property="memberBeforeFee" />
    <result column="member_after_fee" jdbcType="DECIMAL" property="memberAfterFee" />
    <result column="no_member_after_fee" jdbcType="DECIMAL" property="noMemberAfterFee" />
    <result column="no_member_before_fee" jdbcType="DECIMAL" property="noMemberBeforeFee" />
    <result column="member_interests_word" jdbcType="VARCHAR" property="memberInterestsWord" />
    <result column="member_interests_url" jdbcType="VARCHAR" property="memberInterestsUrl" />
    <result column="no_member_interests_word" jdbcType="VARCHAR" property="noMemberInterestsWord" />
    <result column="no_member_interests_url" jdbcType="VARCHAR" property="noMemberInterestsUrl" />
    <result column="member_show_privacy_phone_tab" jdbcType="INTEGER" property="memberShowPrivacyPhoneTab" />
    <result column="no_member_show_privacy_phone_tab" jdbcType="INTEGER" property="noMemberShowPrivacyPhoneTab" />
    <result column="member_free_type" jdbcType="INTEGER" property="memberFreeTecServiceFeeType" />
    <result column="member_free_view_count" jdbcType="INTEGER" property="memberFreeViewCount" />
    <result column="member_free_call_count" jdbcType="INTEGER" property="memberFreeCallCount" />
    <result column="member_free_ready_type" jdbcType="INTEGER" property="memberFreeReadyType" />
    <result column="member_free_ready_time" jdbcType="INTEGER" property="memberFreeReadyTime" />
    <result column="member_free_time" jdbcType="INTEGER" property="memberFreeTecServiceFeeTime" />
    <result column="no_member_free_type" jdbcType="INTEGER" property="noMemberFreeTecServiceFeeType" />
    <result column="no_member_free_view_count" jdbcType="INTEGER" property="noMemberFreeViewCount" />
    <result column="no_member_free_call_count" jdbcType="INTEGER" property="noMemberFreeCallCount" />
    <result column="no_member_free_ready_type" jdbcType="INTEGER" property="noMemberFreeReadyType" />
    <result column="no_member_free_ready_time" jdbcType="INTEGER" property="noMemberFreeReadyTime" />
    <result column="no_member_free_time" jdbcType="INTEGER" property="noMemberFreeTecServiceFeeTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="member_use_commission_stage_type" jdbcType="INTEGER" property="memberUseCommissionStageType" />
    <result column="no_member_use_commission_stage_type" jdbcType="INTEGER" property="noMemberUseCommissionStageType" />
    <result column="commission_score" jdbcType="DECIMAL" property="commissionScore" />
    <result column="apply_transport_type" property="applyTransportType" jdbcType="INTEGER" />
    <result column="refund_flag_type" property="refundFlagType" jdbcType="INTEGER" />
    <result column="price_publish_type" property="pricePublishType" jdbcType="INTEGER" />
    <result column="transport_proportion_num" property="transportProportionNum" jdbcType="INTEGER" />
    <result column="price_min" property="priceMin" jdbcType="DECIMAL" />
    <result column="price_max" property="priceMax" jdbcType="DECIMAL" />
    <result column="tec_service_fee_rate" property="tecServiceFeeRate" jdbcType="DECIMAL" />
    <result column="tec_service_fee_min" property="tecServiceFeeMin" jdbcType="DECIMAL" />
    <result column="tec_service_fee_max" property="tecServiceFeeMax" jdbcType="DECIMAL" />
    <result column="discount_time" property="discountTime" jdbcType="INTEGER" />
    <result column="discount" property="discount" jdbcType="DECIMAL" />
    <result column="discount_config" property="discountConfig" jdbcType="VARCHAR" />
  </resultMap>

  <!-- INSERT (CREATE) -->
  <insert id="insert" parameterType="com.tyt.plat.entity.base.TytTransportTecServiceFee">
    INSERT INTO tyt_transport_tec_service_fee (src_msg_id, member_before_fee, member_after_fee, no_member_after_fee, no_member_before_fee, member_interests_word
        , member_interests_url, no_member_interests_word, no_member_interests_url, member_show_privacy_phone_tab, no_member_show_privacy_phone_tab
        , member_free_type, member_free_view_count, member_free_call_count, member_free_ready_type, member_free_ready_time, member_free_time
        , no_member_free_type, no_member_free_view_count, no_member_free_call_count, no_member_free_ready_type, no_member_free_ready_time, no_member_free_time
        , create_time, modify_time, member_use_commission_stage_type, no_member_use_commission_stage_type, commission_score, good_car_price_transport_carry_price
        , transport_proportion_num, price_min, price_max, tec_service_fee_rate, tec_service_fee_min, tec_service_fee_max, discount_time, discount, discount_config, all_discount
        , apply_transport_type, refund_flag_type, price_publish_type, good_transport_label_type, good_transport_label, route_type, start_city, dest_city, distance_min, distance_max)
    VALUES (#{srcMsgId}, #{memberBeforeFee}, #{memberAfterFee}, #{noMemberAfterFee}, #{noMemberBeforeFee}, #{memberInterestsWord}, #{memberInterestsUrl}, #{noMemberInterestsWord}, #{noMemberInterestsUrl}, #{memberShowPrivacyPhoneTab}, #{noMemberShowPrivacyPhoneTab}
           , #{memberFreeTecServiceFeeType}, #{memberFreeViewCount}, #{memberFreeCallCount}, #{memberFreeReadyType}, #{memberFreeReadyTime}, #{memberFreeTecServiceFeeTime}
           , #{noMemberFreeTecServiceFeeType}, #{noMemberFreeViewCount}, #{noMemberFreeCallCount}, #{noMemberFreeReadyType}, #{noMemberFreeReadyTime}, #{noMemberFreeTecServiceFeeTime}
           , #{createTime}, #{modifyTime}, #{memberUseCommissionStageType}, #{noMemberUseCommissionStageType}, #{commissionScore}, #{goodCarPriceTransportCarryPrice}
           , #{transportProportionNum}, #{priceMin}, #{priceMax}, #{tecServiceFeeRate}, #{tecServiceFeeMin}, #{tecServiceFeeMax}, #{discountTime}, #{discount}, #{discountConfig}, #{allDiscount}
           , #{applyTransportType}, #{refundFlagType}, #{pricePublishType},#{goodTransportLabelType}, #{goodTransportLabel}, #{routeType}, #{startCity}, #{destCity}, #{distanceMin}, #{distanceMax})
  </insert>

    <insert id="saveTransportTecServiceFeeLog">
        insert into tyt_transport_tec_service_fee_log (src_msg_id, option_type, from_type, create_time) VALUE (#{srcMsgId}, #{optionType}, #{fromType}, now())
    </insert>

    <insert id="addFreeTecServiceFeeLog" parameterType="com.tyt.plat.entity.base.TytFreeTecServiceFeeLog">
        INSERT INTO `tyt_free_tec_service_fee_log` (
            `src_msg_id`,
            `new_transport_user_free`,
            `city_free`,
            `good_car_price_transport_free`,
            `member_time_out_free`,
            `no_member_time_out_free`,
            `create_time`,
            `modify_time`
        ) VALUES (
                     #{srcMsgId},
                     #{newTransportUserFree},
                     #{cityFree},
                     #{goodCarPriceTransportFree},
                     #{memberTimeOutFree},
                     #{noMemberTimeOutFree},
                     #{createTime},
                     #{modifyTime}
                 )
    </insert>

    <!-- SELECT (READ) -->
  <select id="getBySrcMsgId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT * FROM tyt_transport_tec_service_fee WHERE src_msg_id = #{srcMsgId}
  </select>

  <!-- UPDATE -->
  <update id="updateBySrcMsgId" parameterType="com.tyt.plat.entity.base.TytTransportTecServiceFee">
    UPDATE tyt_transport_tec_service_fee SET
                                       member_before_fee=#{memberBeforeFee},
                                       member_after_fee=#{memberAfterFee},
                                       no_member_before_fee=#{noMemberBeforeFee},
                                       no_member_after_fee=#{noMemberAfterFee},
                                       member_interests_word=#{memberInterestsWord},
                                       member_interests_url=#{memberInterestsUrl},
                                       no_member_interests_word=#{noMemberInterestsWord},
                                       no_member_interests_url=#{noMemberInterestsUrl},
                                       member_show_privacy_phone_tab=#{memberShowPrivacyPhoneTab},
                                       no_member_show_privacy_phone_tab=#{noMemberShowPrivacyPhoneTab},
                                       member_free_type = #{memberFreeTecServiceFeeType},
                                       member_free_view_count = #{memberFreeViewCount},
                                       member_free_call_count = #{memberFreeCallCount},
                                       member_free_ready_type = #{memberFreeReadyType},
                                       member_free_ready_time = #{memberFreeReadyTime},
                                       member_free_time=#{memberFreeTecServiceFeeTime},
                                       no_member_free_type=#{noMemberFreeTecServiceFeeType},
                                       no_member_free_view_count = #{noMemberFreeViewCount},
                                       no_member_free_call_count = #{noMemberFreeCallCount},
                                       no_member_free_ready_type = #{noMemberFreeReadyType},
                                       no_member_free_ready_time = #{noMemberFreeReadyTime},
                                       no_member_free_time=#{noMemberFreeTecServiceFeeTime},
                                       member_use_commission_stage_type=#{memberUseCommissionStageType},
                                       no_member_use_commission_stage_type=#{noMemberUseCommissionStageType},
                                       commission_score=#{commissionScore},
                                       good_car_price_transport_carry_price = #{goodCarPriceTransportCarryPrice},
                                       transport_proportion_num=#{transportProportionNum},
                                       price_min=#{priceMin},
                                       price_max=#{priceMax},
                                       tec_service_fee_rate=#{tecServiceFeeRate},
                                       tec_service_fee_min=#{tecServiceFeeMin},
                                       tec_service_fee_max=#{tecServiceFeeMax},
                                       discount_time=#{discountTime},
                                       discount=#{discount},
                                       discount_config=#{discountConfig},
                                       all_discount=#{allDiscount},
                                       apply_transport_type=#{applyTransportType},
                                       refund_flag_type=#{refundFlagType},
                                       price_publish_type=#{pricePublishType},
                                       good_transport_label_type = #{goodTransportLabelType},
                                       good_transport_label = #{goodTransportLabel},
                                       route_type = #{routeType},
                                       start_city = #{startCity},
                                       dest_city = #{destCity},
                                       distance_min = #{distanceMin},
                                       distance_max = #{distanceMax},
                                       modify_time=#{modifyTime}
    WHERE src_msg_id = #{srcMsgId}
  </update>

  <!-- DELETE -->
  <delete id="delete" parameterType="java.lang.Long">
    DELETE FROM tyt_transport_tec_service_fee WHERE src_msg_id = #{srcMsgId}
  </delete>

    <delete id="deleteFreeTecServiceFeeLogBySrcMsgId" parameterType="java.lang.Long">
        DELETE FROM  tyt_free_tec_service_fee_log WHERE src_msg_id = #{srcMsgId}
    </delete>


    <select id="getActivity" resultType="com.tyt.acvitity.bean.ActivityInfo">
        select id as marketingActivityId,activity_type as type,start_time as time  from marketing_activity where activity_type = #{type} and status = 1 and  end_time >= now()
                                           AND start_time &lt;= now()
    </select>

    <select id="getActivityRecord" resultType="java.lang.Integer">
        select count(*) from activity_invitee_record where activity_id = #{activityId} and invited_user_id = #{userId}
    </select>

    <select id="getPrize" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            activity_inviter_reward_log
        WHERE
            activity_id = #{activityId} and role_type = 2 and type = 1

          AND invitee_id = #{userId}
    </select>

    <select id="getUserAuth" resultType="java.lang.Integer">
        select identity_status from tyt_user_identity_auth where user_id = #{userId} limit 1
    </select>

    <select id="getCarUserPhone" resultType="java.lang.Integer">
        select count(*) from activity_invitee_record record left join


        tyt_user_call_phone_record r on record.invited_user_id = r.user_id


        where r.user_id = #{userId} and r.ctime &lt; record.create_time limit 1
    </select>

    <select id="getGoodsUserTransport" resultType="java.lang.Integer">
        select count(*) from activity_invitee_record record left join
        tyt_transport_main r on record.invited_user_id = r.user_id
        where r.user_id = #{userId} and r.ctime &lt; record.create_time  limit 1
    </select>

</mapper>