<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytMessageMapper">

    <select id="queryMyMessageCount" resultType="java.lang.Integer">
        select count(*)
        from tyt_user_msg u
        left join tyt_message m on u.msg_id = m.id
        where u.user_id=#{userId} AND u.del_status='0'
          AND m.status=0 AND m.send_status='2' and m.end_time>=now()  AND m.send_time &lt;= NOW() and u.read_status = '0'
          and u.push_port=#{pushPort}
    </select>
</mapper>