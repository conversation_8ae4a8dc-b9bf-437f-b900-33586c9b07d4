<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCustomServiceRuleMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCustomServiceRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="port" jdbcType="SMALLINT" property="port" />
    <result column="rule_title" jdbcType="VARCHAR" property="ruleTitle" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="rule_type_name" jdbcType="VARCHAR" property="ruleTypeName" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="rule_content" jdbcType="LONGVARCHAR" property="ruleContent" />
  </resultMap>
  <sql id="Base_Column_List">
    id, port, rule_title, type_id, rule_type_name, `status`, sort, remark, operater,
    ctime, mtime
  </sql>

  <select id="selectList" resultMap="BaseResultMap" parameterType="com.tyt.customservice.dto.CustomServiceRuleListReq">
    select
    <include refid="Base_Column_List"/>
    from tyt_custom_service_rule
    <where>
      <if test="typeId !=null">
        type_id = #{typeId,jdbcType=INTEGER}
      </if>
      <if test="ruleTitle !=null and ruleTitle !=''">
        and rule_title LIKE CONCAT(#{ruleTitle,jdbcType=VARCHAR},'%')
      </if>
      and status = 1
    </where>
    order by sort desc
  </select>
</mapper>
