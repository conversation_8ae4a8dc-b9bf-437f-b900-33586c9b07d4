<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCarCreditUpgradePopupMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCarCreditUpgradePopup">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="is_popup" jdbcType="INTEGER" property="isPopup" />
    <result column="popup_type" jdbcType="INTEGER" property="popupType" />
    <result column="popup_time" jdbcType="TIMESTAMP" property="popupTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>

  <sql id="BaseSql">
    id,user_id,is_popup,popup_type,popup_time,ctime
  </sql>

  <select id="selectByUserIdPopupType" resultMap="BaseResultMap">
    select
    <include refid="BaseSql"/>
    from tyt_car_credit_upgrade_popup
    where user_id=#{userId,jdbcType=BIGINT} and popup_type = #{popupType,jdbcType=INTEGER} limit 1
  </select>

</mapper>