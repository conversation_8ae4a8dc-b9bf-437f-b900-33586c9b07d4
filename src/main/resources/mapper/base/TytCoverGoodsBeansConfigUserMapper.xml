<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCoverGoodsBeansConfigUserMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCoverGoodsBeansConfigUser">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="total_num" jdbcType="INTEGER" property="totalNum"/>
        <result column="used_num" jdbcType="INTEGER" property="usedNum"/>
        <result column="left_num" jdbcType="INTEGER" property="leftNum"/>
        <result column="beans_name" jdbcType="VARCHAR" property="beansName"/>
        <result column="validate_time" jdbcType="TIMESTAMP" property="validateTime"/>
        <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId"/>
        <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="beans_status" jdbcType="INTEGER" property="beansStatus"/>
        <result column="dial_config_id" jdbcType="BIGINT" property="dialConfigId"/>
    </resultMap>

    <select id="selectCoverGoodsDialGainLogByUserId"
            resultMap="BaseResultMap">
        select beansUser.*
        from tyt_cover_goods_beans_config_user beansUser
        where beansUser.user_id = #{userId}
        order by beansUser.update_time DESC
    </select>

    <select id="selectLeftNumByUserId"
            resultMap="BaseResultMap">
        select beansUser.*
        from tyt_cover_goods_beans_config_user beansUser
        where beansUser.user_id = #{userId}
          and beansUser.beans_status=1
          and left_num > 0
        order by beansUser.validate_time asc
    </select>

    <select id="selectTotalBeansLeftNumByUserId"  resultType="com.tyt.plat.biz.covergoodsdial.pojo.CoverGoodsUserBeansVO">
        select user_id as userId, sum(left_num) as totalLeftNum
        from tyt_cover_goods_beans_config_user
        where user_id = #{userId}
          and beans_status = 1
    </select>

    <update id="useBeansById">
        update tyt_cover_goods_beans_config_user
        set left_num=left_num - 1,
            used_num=used_num + 1,
            beans_status=#{beansStatus},
            update_time=now()
        where id = #{id}
    </update>
</mapper>