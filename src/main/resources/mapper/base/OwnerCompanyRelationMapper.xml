<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.OwnerCompanyRelationMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.OwnerCompanyRelation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id,enterprise_id,company_id,status,create_time
  </sql>
  <select id="selectByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from owner_company_relation
    where company_id = #{companyId,jdbcType=BIGINT} and status = 1
  </select>
</mapper>
