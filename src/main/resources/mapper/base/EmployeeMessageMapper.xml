<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.EmployeeMessageMapper">


  <insert id="insertEmployeeMessage" parameterType="com.tyt.model.EmployeeMessage">
    insert into employee_message(title, content, push_user_id, push_user_name, ctime, operation_time, type, read_status, user_id, user_name,  push_status, utime, status, tmpl_id)
            values(#{title},#{content},#{pushUserId},#{pushUserName},#{ctime},#{operationTime},#{type},#{readStatus},#{userId},#{userName},#{pushStatus},#{utime},#{status},#{tmplId});
  </insert>

</mapper>