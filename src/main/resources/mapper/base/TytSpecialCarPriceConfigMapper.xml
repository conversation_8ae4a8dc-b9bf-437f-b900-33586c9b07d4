<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSpecialCarPriceConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSpecialCarPriceConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_city" jdbcType="VARCHAR" property="startCity" />
    <result column="dest_city" jdbcType="VARCHAR" property="destCity" />
    <result column="start_tonnage" jdbcType="INTEGER" property="startTonnage" />
    <result column="end_tonnage" jdbcType="INTEGER" property="endTonnage" />
    <result column="cargo_owner_id" jdbcType="BIGINT" property="cargoOwnerId" />
    <result column="cargo_owner_name" jdbcType="VARCHAR" property="cargoOwnerName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="price_rule" jdbcType="VARCHAR" property="priceRule" />
    <result column="less_price_rule" jdbcType="VARCHAR" property="lessPriceRule" />
    <result column="driving_fee" jdbcType="DECIMAL" property="drivingFee" />
    <result column="price_type" jdbcType="INTEGER" property="priceType" />
    <result column="price_lower_limit" jdbcType="DECIMAL" property="priceLowerLimit" />
    <result column="less_price_lower_limit" jdbcType="DECIMAL" property="lessPriceLowerLimit" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="modify_user_id" jdbcType="BIGINT" property="modifyUserId" />
    <result column="modify_user_name" jdbcType="VARCHAR" property="modifyUserName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="del_status" jdbcType="TINYINT" property="delStatus" />
  </resultMap>

    <sql id="base_Columns">
        id,
        start_city as startCity,
        dest_city as destCity,
        start_tonnage as startTonnage,
        end_tonnage as endTonnage,
        cargo_owner_id as cargoOwnerId,
        cargo_owner_name as cargoOwnerName,
        status,
        price_rule as priceRule,
        less_price_rule as lessPriceRule,
        driving_fee as drivingFee,
        price_type as priceType,
        price_lower_limit as priceLowerLimit,
        less_price_lower_limit as lessPriceLowerLimit,
        create_user_id as createUserId,
        create_user_name as createUserName,
        modify_user_id as modifyUserId,
        modify_user_name as modifyUserName,
        remark,
        create_time as createTime,
        modify_time as modifyTime,
        del_status as delStatus
    </sql>

    <select id="selectMatchPriceConfig"
            resultType="com.tyt.plat.entity.base.TytSpecialCarPriceConfig">
        select <include refid="base_Columns"/>
        from tyt_special_car_price_config
        where del_status = 0 and start_city = #{startCity} and dest_city = #{destCity} and cargo_owner_id = #{cargoOwnerId}
        and start_tonnage &lt; #{weight} and end_tonnage >= #{weight} and status = 1 limit 1
    </select>

    <select id="getConfigStatus"  resultType="com.tyt.plat.entity.base.TytSpecialCarPriceConfig">
        select start_city as startCity,dest_city as destCity
        from tyt_special_car_price_config
        where del_status = 0 and status = 1
    </select>

    <select id="selectByCities" resultType="com.tyt.plat.entity.base.TytSpecialCarPriceConfig">
        SELECT start_city as startCity,dest_city as destCity FROM tyt_special_car_price_config
        <where>
            del_status = 0 and status = 1
            <if test="startCity != null and startCity != ''">
                AND start_city = #{startCity}
            </if>
            <if test="destCity != null and destCity != ''">
                AND dest_city = #{destCity}
            </if>
        </where>
    </select>

    <select id="countByOwnerAndRoute" resultType="java.lang.Integer">
        select count(*)
        from tyt_special_car_price_config
        where del_status = 0 and status = 1 and start_city = #{startCity} and dest_city = #{destCity} and cargo_owner_id = #{cargoOwnerId}
    </select>

    <select id="countMatchPriceConfigCityAndRule" resultType="integer">
        select count(1)
        from tyt_special_car_price_config
        where del_status = 0
          and start_city = #{startCity}
          and dest_city = #{destCity}
          and cargo_owner_id != (select id
      from tyt_dispatch_cooperative
      where cooperative_name = '平台' and status = 1 and delete_flag = 0 limit 1)
          and status = 1
    </select>

</mapper>