<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportAfterOrderDataMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportAfterOrderData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="task_content" jdbcType="VARCHAR" property="taskContent" />
    <result column="good_type_name" jdbcType="VARCHAR" property="goodTypeName" />
    <result column="start_city" jdbcType="VARCHAR" property="startCity" />
    <result column="dest_city" jdbcType="VARCHAR" property="destCity" />
    <result column="distance" jdbcType="VARCHAR" property="distance" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="price" jdbcType="VARCHAR" property="price" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="getSameTransportAveragePrice" resultType="com.tyt.transport.querybean.SameTransportAveragePriceData">
    <foreach item='date' index='index' collection='req.dates' separator=' union all '>
      select DATEDIFF(now(), #{date}) as dayNum, avg(price) as averagePrice, count(*) as sameCount from tyt_transport_after_order_data
      where start_city = #{req.startCity} and dest_city = #{req.destCity}
      and create_time &gt;= #{date}
      and good_type_name = #{req.goodTypeName}
      and distance &gt;= (#{req.distance} - 10) and distance &lt;= (#{req.distance} + 10)
      and weight &gt;= #{req.weightMin} and weight &lt; #{req.weightMax}
      and price is not null and price != '0' and price != ''
    </foreach>
    union all
    <foreach item='date' index='index' collection='req.dates' separator=' union all '>
      select DATEDIFF(now(), #{date}) as dayNum, avg(price) as averagePrice, count(*) as sameCount from tyt_transport_after_order_data
      where start_city = #{req.startCity} and dest_city = #{req.destCity}
      and create_time &gt;= #{date}
      and distance &gt;= (#{req.distance} - 10) and distance &lt;= (#{req.distance} + 10)
      and weight &gt;= #{req.weightMin} and weight &lt; #{req.weightMax}
      and price is not null and price != '0' and price != ''
    </foreach>
  </select>

</mapper>