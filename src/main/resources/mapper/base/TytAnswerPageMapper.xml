<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytAnswerPageMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytAnswerPage">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="show_scope" jdbcType="INTEGER" property="showScope" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="link_url" jdbcType="VARCHAR" property="linkUrl" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sort_number" jdbcType="INTEGER" property="sortNumber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_man_id" jdbcType="BIGINT" property="createManId" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_man_id" jdbcType="BIGINT" property="modifyManId" />
    <result column="publish_status" jdbcType="INTEGER" property="publishStatus" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="publish_man_id" jdbcType="BIGINT" property="publishManId" />
  </resultMap>

    <select id="getAnswerList" resultMap="BaseResultMap">
      select
      id,
      category_id,
      show_scope,
      title,
      link_url,
      status,
      sort_number,
      create_time,
      modify_time,
      publish_status,
      publish_time
      from
        tyt_answer_page
      where status = 1
        and show_scope = 2
        and category_id = #{categoryId}
        <if test="sortNumber != null">
          and sort_number &lt; #{sortNumber}
        </if>
        order by sort_number desc
      limit 1000
    </select>

  <select id="queryByTitleFragment" resultMap="BaseResultMap">
    select id,
           category_id,
           show_scope,
           title,
           link_url,
           status,
           create_time
    from tyt_answer_page
    where status = 1
      and show_scope = 2
      and title like concat('%', #{titleFragment}, '%')
      and category_id in
      <foreach collection="categoryIdList" open="(" close=")" item="categoryId" separator=",">
        #{categoryId}
      </foreach>
    order by create_time
      limit 20
  </select>
</mapper>