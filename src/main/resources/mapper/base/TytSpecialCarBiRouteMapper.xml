<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSpecialCarBiRouteMapper">
	<resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSpecialCarBiRoute">
		<!--
		  WARNING - @mbg.generated
		-->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="start_city" jdbcType="VARCHAR" property="startCity" />
		<result column="dest_city" jdbcType="VARCHAR" property="destCity" />
		<result column="del_status" jdbcType="VARCHAR" property="delStatus" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
	</resultMap>

	<select id="countByRoute" resultType="java.lang.Integer">
		select count(*)
		from tyt_special_car_bi_route
		where del_status = 0
		  and start_city = #{startCity} and dest_city = #{destCity}
	</select>
</mapper>