<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCustomInformationMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCustomInformation">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="goods_phone" jdbcType="VARCHAR" property="goodsPhone"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="source" jdbcType="TINYINT" property="source"/>
        <result column="indentity_type" jdbcType="VARCHAR" property="indentityType"/>
        <result column="publish_good_type" jdbcType="VARCHAR" property="publishGoodType"/>
        <result column="custom_levels" jdbcType="VARCHAR" property="customLevels"/>
        <result column="market_maintenance_personnel" jdbcType="VARCHAR" property="marketMaintenancePersonnel"/>
        <result column="market_maintenance_personnel_user_id" jdbcType="BIGINT"
                property="marketMaintenancePersonnelUserId"/>
        <result column="dispatch_maintenance_personnel" jdbcType="VARCHAR" property="dispatchMaintenancePersonnel"/>
        <result column="dispatch_maintenance_personnel_user_id" jdbcType="BIGINT"
                property="dispatchMaintenancePersonnelUserId"/>
        <result column="dispatch_intention_levels" jdbcType="VARCHAR" property="dispatchIntentionLevels"/>
        <result column="detail_address" jdbcType="VARCHAR" property="detailAddress"/>
        <result column="goods_city" jdbcType="VARCHAR" property="goodsCity"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="company_voucher" jdbcType="VARCHAR" property="companyVoucher"/>
        <result column="position" jdbcType="VARCHAR" property="position"/>
        <result column="mobile_belonging_place" jdbcType="VARCHAR" property="mobileBelongingPlace"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="performance_time" jdbcType="TIMESTAMP" property="performanceTime"/>
    </resultMap>
    <select id="selectByGoodsPhone" resultMap="BaseResultMap">
        select id,
               user_id,
               name,
               source,
               indentity_type,
               publish_good_type,
               custom_levels,
               market_maintenance_personnel,
               market_maintenance_personnel_user_id,
               dispatch_maintenance_personnel,
               dispatch_maintenance_personnel_user_id,
               dispatch_intention_levels,
               detail_address,
               goods_city,
               company_name,
               company_voucher,
               position,
               mobile_belonging_place,
               status,
               create_time,
               create_user_id,
               create_user_name,
               update_user_id,
               update_user_name,
               update_time,
               performance_time
        from tyt_custom_information
        where goods_phone = #{goodsPhone}
          and status = 1
    </select>
</mapper>