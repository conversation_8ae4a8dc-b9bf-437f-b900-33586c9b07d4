<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.FeedbackUserLabelMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.FeedbackUserLabel">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="label_id" jdbcType="BIGINT" property="labelId"/>
        <result column="label_name" jdbcType="VARCHAR" property="labelName"/>
        <result column="feedback_id" jdbcType="BIGINT" property="feedbackId"/>
        <result column="post_user_id" jdbcType="BIGINT" property="postUserId"/>
        <result column="post_user_type" jdbcType="SMALLINT" property="postUserType"/>
        <result column="receive_user_id" jdbcType="BIGINT" property="receiveUserId"/>
        <result column="receive_user_type" jdbcType="SMALLINT" property="receiveUserType"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="label_feedback_type" jdbcType="SMALLINT" property="labelFeedbackType"/>
        <result column="ts_order_id" jdbcType="BIGINT" property="tsOrderId"/>
        <result column="ts_id" jdbcType="BIGINT" property="tsId"/>
        <result column="receiver_hide_flag" jdbcType="BIGINT" property="receiverHideFlag"/>
    </resultMap>

    <update id="deleteByFeedbackId">
        update feedback_user_label
        set del_flag= 1
        where feedback_id = #{feedbackId}
    </update>

    <select id="selectByByFeedbackIdAndDelFlag" resultMap="BaseResultMap">
        select *
        from feedback_user_label
        where feedback_id = #{feedbackId}
          and del_flag = #{delFlag}
    </select>

    <select id="count" resultType="java.lang.Long">
        select count(*)
        from feedback_user_label
        where create_time > #{beginTime}
          and label_feedback_type = #{feedbackType}
          and del_flag = #{delFlag}
        <if test="postUserId != null">
            and post_user_id = #{postUserId}
        </if>
        <if test="postUserType != null">
            and post_user_type = #{postUserType}
        </if>
        <if test="receiveUserId != null">
            and receive_user_id = #{receiveUserId}
            and receiver_hide_flag = 0
        </if>
        <if test="receiveUserType != null">
            and receive_user_type = #{receiveUserType}
        </if>
    </select>

    <select id="sum" resultType="com.tyt.plat.biz.feedback.manager.pojo.FeedbackLabelSumDTO">
        SELECT label_id            as labelId,
               label_name          as labelName,
               label_feedback_type as labelType,
               count(*)            as total
        FROM feedback_user_label
        where create_time > #{beginTime}
          and del_flag = #{delFlag}
        <if test="postUserId != null">
            and post_user_id = #{postUserId}
        </if>
        <if test="postUserType != null">
            and post_user_type = #{postUserType}
        </if>
        <if test="receiveUserId != null">
            and receive_user_id = #{receiveUserId}
        </if>
        <if test="receiveUserType != null">
            and receive_user_type = #{receiveUserType}
        </if>
        <if test="feedbackType != null">
            and label_feedback_type = #{feedbackType}
        </if>

        GROUP BY label_id
        ORDER BY total DESC
    </select>

    <select id="selectLabelNames" resultType="java.lang.String">
        select label_name
        from feedback_user_label
        where feedback_id = #{feedbackId}
          and del_flag = #{delFlag}
    </select>
</mapper>