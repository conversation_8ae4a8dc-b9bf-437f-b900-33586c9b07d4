<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSpecialCarContactRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSpecialCarContactRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="contact_status" jdbcType="SMALLINT" property="contactStatus" />
    <result column="work_order_status" jdbcType="SMALLINT" property="workOrderStatus" />
    <result column="goods_status" jdbcType="SMALLINT" property="goodsStatus" />
    <result column="is_change" jdbcType="SMALLINT" property="isChange" />
    <result column="contact_role" jdbcType="VARCHAR" property="contactRole" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_content" jdbcType="VARCHAR" property="contactContent" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="change_reason" jdbcType="VARCHAR" property="changeReason" />
    <result column="deal_reason" jdbcType="VARCHAR" property="dealReason" />
    <result column="not_deal_reason" jdbcType="VARCHAR" property="notDealReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <select id="countBySrcMsgId" resultType="java.lang.Integer">
    select count(*)
    from tyt_special_car_contact_record
    where src_msg_id = #{srcMsgId}
  </select>
</mapper>