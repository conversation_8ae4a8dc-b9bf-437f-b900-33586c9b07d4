<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportMainExtendMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportMainExtend">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId"/>
        <result column="use_car_type" jdbcType="INTEGER" property="useCarType"/>
        <result column="price_type" jdbcType="INTEGER" property="priceType"/>
        <result column="suggest_min_price" jdbcType="INTEGER" property="suggestMinPrice" />
        <result column="suggest_max_price" jdbcType="INTEGER" property="suggestMaxPrice" />
        <result column="fix_price_fast" jdbcType="INTEGER" property="fixPriceFast" />
        <result column="cost_price" jdbcType="INTEGER" property="costPrice" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="good_model_score" jdbcType="DECIMAL" property="goodModelScore" />
        <result column="good_model_level" jdbcType="INTEGER" property="goodModelLevel" />
        <result column="price_cap" jdbcType="INTEGER" property="priceCap" />
        <result column="lim_good_model_score" jdbcType="DECIMAL" property="limGoodModelScore" />
        <result column="lim_good_model_level" jdbcType="INTEGER" property="limGoodModelLevel" />
        <result column="commission_score" jdbcType="DECIMAL" property="commissionScore" />
        <result column="seckill_goods" jdbcType="INTEGER" property="seckillGoods" />
        <result column="perk_price" jdbcType="INTEGER" property="perkPrice" />
        <result column="good_transport_label" jdbcType="INTEGER" property="goodTransportLabel" />
        <result column="good_transport_label_part" jdbcType="INTEGER" property="goodTransportLabelPart" />
        <result column="client_fusion" jdbcType="INTEGER" property="clientFusion" />
    </resultMap>

    <select id="getBySrcMsgId" resultMap="BaseResultMap">
        select * from tyt_transport_main_extend where src_msg_id = #{srcMsgId}
    </select>


    <select id="getUseCarTypeBySrcMsgIds" resultMap="BaseResultMap">
        select src_msg_id, use_car_type
        from tyt_transport_main_extend
        where src_msg_id in ( <foreach collection="list" item="i" separator=","> #{i} </foreach> )
        and use_car_type is not null
    </select>

    <select id="getPriceTypeBySrcMsgId" resultType="java.lang.Integer">
        select price_type
        from tyt_transport_main_extend where src_msg_id = #{srcMsgId}
    </select>

    <update id="recordScoreEveryTransportUpdate">
        update tyt_transport_main_extend set good_model_score = #{instantGrabScore}, commission_score = #{commissionScore}, modify_time = now() where src_msg_id = #{srcMsgId}
    </update>

    <select id="getTransportIsSeckillGoods" resultType="integer">
        select count(1)
        from tyt_transport_main_extend where src_msg_id = #{srcMsgId} and seckill_goods = 1
    </select>

    <select id="getSeckillGoodsHaveSuccess" resultType="integer">
        select count(1)
        from tyt_transport_order_snapshot where src_msg_id = #{srcMsgId} and order_allocate_state = 1
    </select>

    <select id="getSeckillGoodsHaveError" resultType="integer">
        select count(1)
        from tyt_transport_order_snapshot where src_msg_id = #{srcMsgId} and order_allocate_state = 3
    </select>

</mapper>