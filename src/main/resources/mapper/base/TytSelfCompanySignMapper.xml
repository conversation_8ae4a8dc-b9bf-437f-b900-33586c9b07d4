<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSelfCompanySignMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSelfCompanySign">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="link_user_name" jdbcType="VARCHAR" property="linkUserName" />
    <result column="link_phone" jdbcType="VARCHAR" property="linkPhone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="sign_account_id" jdbcType="VARCHAR" property="signAccountId" />
    <result column="sign_seal_url" jdbcType="VARCHAR" property="signSealUrl" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

    <select id="getSelfCompanySign" resultMap="BaseResultMap">
      select * from
        tyt_self_company_sign
      where enable = 1
          limit 1
    </select>
</mapper>