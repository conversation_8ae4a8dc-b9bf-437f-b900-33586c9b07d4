<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.deposit.mapper.base.TytDepositApplyAuditMapper">
    <resultMap id="BaseResultMap" type="com.tyt.deposit.entity.base.TytDepositApplyAudit">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="ex_id" jdbcType="BIGINT" property="exId"/>
        <result column="user_apply_time" jdbcType="TIMESTAMP" property="userApplyTime"/>
        <result column="customer_service_apply_time" jdbcType="TIMESTAMP" property="customerServiceApplyTime"/>
        <result column="ts_order_no" jdbcType="VARCHAR" property="tsOrderNo"/>
        <result column="pub_cell_phone" jdbcType="VARCHAR" property="pubCellPhone"/>
        <result column="pay_cell_phone" jdbcType="VARCHAR" property="payCellPhone"/>
        <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount"/>
        <result column="refund_reason" jdbcType="VARCHAR" property="refundReason"/>
        <result column="ensure_amount_type" jdbcType="INTEGER" property="ensureAmountType"/>
        <result column="handle_status" jdbcType="INTEGER" property="handleStatus"/>
        <result column="audit_stage" jdbcType="INTEGER" property="auditStage"/>
        <result column="final_audit_status" jdbcType="INTEGER" property="finalAuditStatus"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
    </resultMap>

    <select id="selectRefundAuditStatusByParam" resultType="com.tyt.deposit.bean.RefundAuditStatusBean">
       SELECT  tdaa.handle_status      AS handleStatus,
               tdaa.final_audit_status AS finalAuditStatus,
               tdaa.ts_order_no       AS tsOrderNo,
               tdaa.ensure_amount_type  AS ensureAmountType
               FROM tyt_deposit_apply_audit  tdaa
               WHERE tdaa.user_id = #{userId,jdbcType=BIGINT}
                <if test="finalAuditStatus != null">
                    and tdaa.final_audit_status = #{finalAuditStatus}
                </if>
                ORDER BY tdaa.id DESC
    </select>
    <select id="selectRefundAuditByUserId" resultMap="BaseResultMap">
        select *
        from tyt_deposit_apply_audit
        where user_id = #{userId}
          <if test="auditStatus != null">
              and final_audit_status = #{auditStatus}
          </if>

        <if test="ensureType != null">
            and ensure_amount_type = #{ensureType}
        </if>

    </select>

    <select id="selectCountUserIdAndType" resultType="java.lang.Integer">
        select count(1)
        from tyt_deposit_apply_audit
        where user_id = #{userId} and ensure_amount_type = #{ensureAmountType}
    </select>

</mapper>