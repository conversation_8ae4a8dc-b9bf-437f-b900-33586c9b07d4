<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytInvoiceDriverMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytInvoiceDriver">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="verify_status" jdbcType="TINYINT" property="verifyStatus" />
    <result column="verify_user_id" jdbcType="INTEGER" property="verifyUserId" />
    <result column="verify_user_name" jdbcType="VARCHAR" property="verifyUserName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="id_card_gender" jdbcType="TINYINT" property="idCardGender" />
    <result column="id_card_effective_begin_time" jdbcType="TIMESTAMP" property="idCardEffectiveBeginTime" />
    <result column="id_card_expiration_time" jdbcType="TIMESTAMP" property="idCardExpirationTime" />
    <result column="id_card_nation" jdbcType="VARCHAR" property="idCardNation" />
    <result column="id_card_birthday" jdbcType="TIMESTAMP" property="idCardBirthday" />
    <result column="id_card_address" jdbcType="VARCHAR" property="idCardAddress" />
    <result column="self" jdbcType="INTEGER" property="self"/>
    <result column="id_card_truth" jdbcType="INTEGER" property="idCardTruth"/>
    <result column="qc_card" jdbcType="VARCHAR" property="qcCard" />
    <result column="qc_card_expiration_time" jdbcType="TIMESTAMP" property="qcCardExpirationTime" />
    <result column="qc_card_truth" jdbcType="INTEGER" property="qcCardTruth"/>
    <result column="qc_name" jdbcType="VARCHAR" property="qcName"/>
    <result column="qc_card_vehicle_type" jdbcType="VARCHAR" property="qcCardVehicleType"/>
    <result column="qc_card_type" jdbcType="VARCHAR" property="qcCardType"/>
    <result column="qc_card_send_time" jdbcType="TIMESTAMP" property="qcCardSendTime"/>
    <result column="dl_card" jdbcType="VARCHAR" property="dlCard" />
    <result column="dl_card_vehicle_type" jdbcType="VARCHAR" property="dlCardVehicleType" />
    <result column="dl_card_auth_gov" jdbcType="VARCHAR" property="dlCardAuthGov" />
    <result column="dl_card_issue_time" jdbcType="TIMESTAMP" property="dlCardIssueTime" />
    <result column="dl_card_effective_begin_time" jdbcType="TIMESTAMP" property="dlCardEffectiveBeginTime" />
    <result column="dl_card_expiration_time" jdbcType="TIMESTAMP" property="dlCardExpirationTime" />
    <result column="dl_card_truth" jdbcType="INTEGER" property="dlCardTruth"/>
    <result column="empower" jdbcType="INTEGER" property="empower"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="id_card_permanent" jdbcType="TINYINT" property="idCardPermanent" />
    <result column="empower" jdbcType="TINYINT" property="empower" />
    <result column="driver_user_id" jdbcType="BIGINT" property="driverUserId" />
    <result column="dl_name" jdbcType="VARCHAR" property="dlName" />
    <result column="id_card_sign_gov" jdbcType="VARCHAR" property="idCardSignGov" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
      <result column="dl_card_permanent" jdbcType="TINYINT" property="dlCardPermanent" />
  </resultMap>

  <select id="selectByUserIdAndVerifyStatus" resultMap="BaseResultMap">
      select *
      from tyt_invoice_driver
      where user_id = #{userId}
        and delete_flag = 0
        <if test="verifyStatus != null">
          and verify_status= #{verifyStatus}
        </if>
      order by self desc,create_time desc
  </select>

  <select id="countByUserIdAndVerifyStatus" resultType="java.lang.Long">
    select count(*)
    from tyt_invoice_driver
    where user_id=#{userId}
      and delete_flag = 0
  <if test="verifyStatus != null">
    and verify_status= #{verifyStatus}
  </if>
  </select>

  <select id="countByUserIdAndIdCard" resultType="long">
      select count(*)
      from tyt_invoice_driver
      where user_id = #{userId}
        and id_card = #{idCard}
        and delete_flag = 0
  </select>

  <select id="getByPhone" resultMap="BaseResultMap">
    select *
    from tyt_invoice_driver
    where phone = #{cellPhone}
      and empower = 1
      and delete_flag = 0
    limit 10
  </select>

  <select id="getDriverPhone" resultMap="BaseResultMap">
    select id,phone,name,verify_status,empower,dl_card_vehicle_type
    from tyt_invoice_driver
    where user_id = #{userId} and phone = #{cellPhone} and verify_status = 1 and self != 2 and empower = 2 and delete_flag = 0 order by id desc
      limit 1
  </select>

  <select id="getByUserIdInvoice" resultMap="BaseResultMap">
    select id,phone,name,verify_status,empower,dl_card_vehicle_type
    from tyt_invoice_driver
    where user_id = #{userId} and verify_status = 1 and self = 2 and empower = 2 and delete_flag = 0 order by id desc
      limit 1
  </select>

  <select id="getByCarUserId" resultMap="BaseResultMap">
    select id,phone,name,verify_status ,empower,dl_card_vehicle_type
    from tyt_invoice_driver
    where user_id = #{userId} and delete_flag = 0  ORDER BY (case when verify_status = 1 then 1 else 2 end),id desc
      limit 100
  </select>

  <select id="countByUserIdAndPhone" resultType="long">
    select count(*)
    from tyt_invoice_driver
    where user_id = #{userId}
      and phone = #{cellPhone}
      and delete_flag = 0
  </select>

  <select id="countByUserIdIdCard" resultType="long">
    select count(*)
    from tyt_invoice_driver
    where user_id = #{userId}
      and id_card = #{idCard}
      and id != #{id}
      and delete_flag = 0
  </select>

  <select id="countByUserIdPhone" resultType="long">
    select count(*)
    from tyt_invoice_driver
    where user_id = #{userId}
      and phone = #{cellPhone}
      and id != #{id}
      and delete_flag = 0
  </select>

  <select id="getInvoiceDriverListByDriverUserId" resultMap="BaseResultMap">
    SELECT id,
           user_id                      as userId,
           driver_user_id               as driverUserId,
           verify_status                as verifyStatus,
           verify_user_id               as verifyUserId,
           verify_user_name             as verifyUserName,
           phone,
           name,
           id_card                      as idCard,
           id_card_gender               as idCardGender,
           id_card_effective_begin_time as idCardEffectiveBeginTime,
           id_card_expiration_time      as idCardExpirationTime,
           id_card_nation               as idCardNation,
           id_card_birthday             as idCardBirthday,
           id_card_address              as idCardAddress,
           self,
           id_card_truth                as idCardTruth,
           qc_card                      as qcCard,
           qc_card_expiration_time      as qcCardExpirationTime,
           qc_card_truth                as qcCardTruth,
           qc_name                      as qcName,
           qc_card_vehicle_type         as qcCardVehicleType,
           qc_card_type                 as qcCardType,
           qc_card_send_time            as qcCardSendTime,
           dl_card                      as dlCard,
           dl_card_vehicle_type         as dlCardVehicleType,
           dl_card_auth_gov             as dlCardAuthGov,
           dl_card_issue_time           as dlCardIssueTime,
           dl_card_effective_begin_time as dlCardEffectiveBeginTime,
           dl_card_expiration_time      as dlCardExpirationTime,
           dl_card_truth                as dlCardTruth,
           empower,
           create_time                  as createTime,
           update_time                  as updateTime,
           id_card_permanent            as idCardPermanent,
           dl_card_permanent            as dlCardPermanent
    FROM tyt_invoice_driver
    WHERE driver_user_id = #{driverUserId}
      and verify_status = 1
      and delete_flag = 0
  </select>

    <select id="getByUserId" resultMap="BaseResultMap">
        select id,phone,name,verify_status ,empower,dl_card_vehicle_type,driver_user_id
        from tyt_invoice_driver
        where user_id = #{userId}
          and delete_flag = 0
        ORDER BY id desc
        limit 100
    </select>

    <update id="updatePhoneByPhone">
        update tyt_invoice_driver
        set phone = #{newPhone},
            update_time = now()
        where phone = #{oldPhone}
    </update>
    <update id="updateDelStatusByCellPhone">
        update tyt_invoice_driver
        set delete_flag = 1,
            update_time = now()
        where phone = #{cellPhone};
    </update>
</mapper>