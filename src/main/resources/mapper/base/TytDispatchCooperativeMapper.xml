<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytDispatchCooperativeMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytDispatchCooperative">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cooperative_name" jdbcType="VARCHAR" property="cooperativeName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="action_user_id" jdbcType="BIGINT" property="actionUserId" />
    <result column="action_user_name" jdbcType="VARCHAR" property="actionUserName" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

    <select id="selectByName" resultMap="BaseResultMap">
      select *
      from tyt_dispatch_cooperative
      where cooperative_name = #{name} and status = 1 and delete_flag = 0 limit 1
    </select>

  <select id="selectListByName" resultType="com.tyt.transport.vo.CargoOwnerInfoVo">
    select id as cargoOwnerId,
           cooperative_name as cargoOwnerName
    from tyt_dispatch_cooperative
    where status = 1 and delete_flag = 0
    <if test="name != null and name != ''">
      and cooperative_name like concat("%",#{name},"%")
    </if>
  </select>

</mapper>