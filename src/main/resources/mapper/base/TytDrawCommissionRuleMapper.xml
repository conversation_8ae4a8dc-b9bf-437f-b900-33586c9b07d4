<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytDrawCommissionRuleMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytDrawCommissionRule">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="commission_source" jdbcType="INTEGER" property="commissionSource"/>
        <result column="publish_type" jdbcType="INTEGER" property="publishType"/>
        <result column="have_price" jdbcType="INTEGER" property="havePrice"/>
        <result column="refund_flag" jdbcType="INTEGER" property="refundFlag"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="daily_start" jdbcType="TIME" property="dailyStart"/>
        <result column="daily_end" jdbcType="TIME" property="dailyEnd"/>
        <result column="commission_max_count" jdbcType="INTEGER" property="commissionMaxCount"/>
        <result column="rule" jdbcType="INTEGER" property="rule"/>
        <result column="enabled" jdbcType="INTEGER" property="enabled"/>
        <result column="good_transport" jdbcType="INTEGER" property="goodTransport"/>
        <result column="min_fractional_line" jdbcType="DECIMAL" property="minFractionalLine"/>
        <result column="max_fractional_line" jdbcType="DECIMAL" property="maxFractionalLine"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_id" jdbcType="BIGINT" property="modifyId"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <select id="selectRule" resultMap="BaseResultMap">
        select *
        from tyt_draw_commission_rule
        where enabled = 1
          and (commission_source = #{req.commissionSource} or commission_source= -1)
          and (publish_type = #{req.publishType} or publish_type = -1)
          and (have_price = #{req.havePrice} or have_price = -1)
          and (refund_flag = #{req.refundFlag} or refund_flag = -1)
          and start_date &lt;= #{req.commissionTime}
          and end_date &gt;= #{req.commissionTime}
          and (
              (good_transport = 1 and #{req.goodsModelScore} BETWEEN min_fractional_line AND max_fractional_line)
              or good_transport != 1
            )
       order by modify_time desc limit 1
    </select>
</mapper>