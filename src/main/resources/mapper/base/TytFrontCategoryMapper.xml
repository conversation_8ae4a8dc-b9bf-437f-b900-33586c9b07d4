<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytFrontCategoryMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytFrontCategory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="belong_type" jdbcType="INTEGER" property="belongType" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
</mapper>