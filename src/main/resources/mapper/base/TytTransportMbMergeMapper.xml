<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportMbMergeMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportMbMerge">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="cargo_id" jdbcType="BIGINT" property="cargoId" />
        <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="cargo_version" jdbcType="INTEGER" property="cargoVersion" />
    </resultMap>
    <select id="selectByCargoId" resultMap="BaseResultMap">
        select src_msg_id
        from tyt_transport_mb_merge
        where cargo_id = #{cargoId}
          and status = 0
            limit 1
    </select>
    <select id="selectBySrcMsgId" resultMap="BaseResultMap">
        select *
        from tyt_transport_mb_merge
        where src_msg_id = #{srcMsgId}
        order by create_time desc
            limit 1
    </select>
    <update id="updateCargoMerge">
        update tyt_transport_mb_merge set status = 1 where src_msg_id = #{srcMsgId}
    </update>

    <select id="selectBeforeVersionJson" resultType="com.tyt.transport.querybean.TransportYmmListBean">
        select
            ttmm.cargo_id  cargoId,
            ttmm.src_msg_id  srcMsgId,
            ttmm.cargo_version cargoVersion,
            tmcs.info_json infoJson
        from tyt_transport_mb_merge ttmm
                 left join tyt_mb_cargo_sync_log tmcs on tmcs.cargo_id = ttmm.cargo_id
        where ttmm.src_msg_id=#{srcMsgId}
          and ttmm.cargo_version = tmcs.cargo_version order by ttmm.create_time desc limit 1
    </select>


    <select id="selectLastVersionJson" resultType="com.tyt.transport.querybean.TransportYmmSyncLogBean">
        select
            cargo_id cargoId,
            cargo_version cargoVersion,
            info_json infoJson
        from tyt_mb_cargo_sync_log
        where cargo_id=#{cargoId} and sync_type != 2 order by cargo_version desc limit 1
    </select>

</mapper>