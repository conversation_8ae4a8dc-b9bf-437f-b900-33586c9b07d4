<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportOrdersMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.MybatisTytTransportOrders">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_point" jdbcType="VARCHAR" property="startPoint" />
    <result column="dest_point" jdbcType="VARCHAR" property="destPoint" />
    <result column="task_content" jdbcType="VARCHAR" property="taskContent" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="pub_time" jdbcType="VARCHAR" property="pubTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="upload_cellphone" jdbcType="VARCHAR" property="uploadCellphone" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="pub_user_name" jdbcType="VARCHAR" property="pubUserName" />
    <result column="linkman" jdbcType="VARCHAR" property="linkman" />
    <result column="tel3" jdbcType="VARCHAR" property="tel3" />
    <result column="tel4" jdbcType="VARCHAR" property="tel4" />
    <result column="sort_id" jdbcType="BIGINT" property="sortId" />
    <result column="ts_order_no" jdbcType="VARCHAR" property="tsOrderNo" />
    <result column="thirdparty_platform_type" jdbcType="INTEGER" property="thirdpartyPlatformType" />
    <result column="thirdparty_platform_order_no" jdbcType="VARCHAR" property="thirdpartyPlatformOrderNo" />
    <result column="ts_id" jdbcType="BIGINT" property="tsId" />
    <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo" />
    <result column="refund_flag" jdbcType="INTEGER" property="refundFlag" />
    <result column="de_refund_dueDate" jdbcType="TIMESTAMP" property="deRefundDuedate" />
    <result column="delay_refund_status" jdbcType="INTEGER" property="delayRefundStatus" />
    <result column="info_fee_type" jdbcType="TINYINT" property="infoFeeType" />
    <result column="pay_user_id" jdbcType="BIGINT" property="payUserId" />
    <result column="pay_user_name" jdbcType="VARCHAR" property="payUserName" />
    <result column="pay_cell_phone" jdbcType="VARCHAR" property="payCellPhone" />
    <result column="pay_link_phone" jdbcType="VARCHAR" property="payLinkPhone" />
    <result column="rob_status" jdbcType="CHAR" property="robStatus" />
    <result column="pay_status" jdbcType="CHAR" property="payStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="pay_type" jdbcType="CHAR" property="payType" />
    <result column="pay_sub_channel" jdbcType="VARCHAR" property="paySubChannel" />
    <result column="pay_amount" jdbcType="BIGINT" property="payAmount" />
    <result column="pay_fee_amount" jdbcType="BIGINT" property="payFeeAmount" />
    <result column="total_order_amount" jdbcType="BIGINT" property="totalOrderAmount" />
    <result column="car_amount" jdbcType="BIGINT" property="carAmount" />
    <result column="goods_amount" jdbcType="BIGINT" property="goodsAmount" />
    <result column="handle_ex_time" jdbcType="TIMESTAMP" property="handleExTime" />
    <result column="coupon_amount" jdbcType="BIGINT" property="couponAmount" />
    <result column="pay_service_charge" jdbcType="INTEGER" property="payServiceCharge" />
    <result column="carriage_fee" jdbcType="INTEGER" property="carriageFee" />
    <result column="time_limit_identification" jdbcType="INTEGER" property="timeLimitIdentification" />
    <result column="pay_end_time" jdbcType="TIMESTAMP" property="payEndTime" />
    <result column="refund_amount" jdbcType="BIGINT" property="refundAmount" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="refund_arrival_time" jdbcType="TIMESTAMP" property="refundArrivalTime" />
    <result column="refund_status" jdbcType="CHAR" property="refundStatus" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
    <result column="refund_err_msg" jdbcType="VARCHAR" property="refundErrMsg" />
    <result column="agree_time" jdbcType="TIMESTAMP" property="agreeTime" />
    <result column="load_time" jdbcType="TIMESTAMP" property="loadTime" />
    <result column="refuse_time" jdbcType="TIMESTAMP" property="refuseTime" />
    <result column="head_city" jdbcType="VARCHAR" property="headCity" />
    <result column="head_no" jdbcType="VARCHAR" property="headNo" />
    <result column="tail_city" jdbcType="VARCHAR" property="tailCity" />
    <result column="tail_no" jdbcType="VARCHAR" property="tailNo" />
    <result column="car_id" jdbcType="BIGINT" property="carId" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ex_cancel_status" jdbcType="INTEGER" property="exCancelStatus" />
    <result column="cost_status" jdbcType="SMALLINT" property="costStatus" />
    <result column="op_id" jdbcType="BIGINT" property="opId" />
    <result column="op_name" jdbcType="VARCHAR" property="opName" />
    <result column="is_deal_car" jdbcType="INTEGER" property="isDealCar" />
    <result column="deal_car_time" jdbcType="TIMESTAMP" property="dealCarTime" />
    <result column="delay_status" jdbcType="INTEGER" property="delayStatus" />
    <result column="goods_show" jdbcType="INTEGER" property="goodsShow" />
    <result column="car_show" jdbcType="INTEGER" property="carShow" />
    <result column="de_payment_dueDate" jdbcType="TIMESTAMP" property="dePaymentDuedate" />
    <result column="loading_status" jdbcType="INTEGER" property="loadingStatus" />
    <result column="loading_child_status" jdbcType="INTEGER" property="loadingChildStatus" />
    <result column="car_replace" jdbcType="INTEGER" property="carReplace" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
    <result column="is_complaint" jdbcType="INTEGER" property="isComplaint" />
    <result column="order_new_status" jdbcType="INTEGER" property="orderNewStatus" />
  </resultMap>

  <select id="getUnPostFeedbackOrders" resultMap="BaseResultMap">
    SELECT a.*
    FROM
    tyt_transport_orders a
    WHERE a.order_new_status IN (20, 25, 30,35) and a.cost_status &gt;= 15
    AND a.mtime > #{beginTime}
    AND a.is_assign_order= 0
    <if test="payUserId != null">
      and a.pay_user_id = #{payUserId}
    </if>
    <if test="userId != null">
      AND a.user_id = #{userId}
    </if>
    and a.id not in (
    select order_id
    from   feedback_user
    where 1 = 1
    <if test="payUserId != null">
      and post_user_type = 1
      and post_user_id=#{payUserId}
    </if>
    <if test="userId != null">
      and post_user_type = 2
      and post_user_id=#{userId}
    </if>
    )
    order by mtime desc
  </select>

  <select id="getUnPostFeedbackOrdersNew" resultMap="BaseResultMap">
    SELECT a.*
    FROM
    tyt_transport_orders a
    WHERE a.order_new_status IN (20, 25, 30,35)
    AND a.mtime > #{beginTime}
    AND a.is_assign_order= 0
    <if test="payUserId != null">
      and a.pay_user_id = #{payUserId}
      <if test="driverIdList != null and driverIdList.size() > 0">
        and !(a.driver_id IN
        <foreach item='driverId' index='index' collection='driverIdList' open='(' separator=',' close=')'>
          #{driverId}
        </foreach>
        and a.cost_status &gt;= 15 and a.pay_user_id != #{payUserId})
      </if>
    </if>
    <if test="userId != null">
      AND a.user_id = #{userId}
      <if test="driverIdList != null and driverIdList.size() > 0">
          and !(a.driver_id IN
          <foreach item='driverId' index='index' collection='driverIdList' open='(' separator=',' close=')'>
              #{driverId}
          </foreach>
          and a.cost_status &gt;= 15 and a.pay_user_id != #{userId})
       </if>
    </if>
    and a.id not in (
    select order_id
    from   feedback_user
    where create_time > #{beginTime}
    <if test="payUserId != null">
      and post_user_type = 1
      and post_user_id=#{payUserId}
    </if>
    <if test="userId != null">
      and post_user_type = 2
      and post_user_id=#{userId}
    </if>
    )
    order by mtime desc
  </select>
  
  <select id="firstHonourAnAgreement" resultType="integer">
    select count(1)
    from (
           SELECT
             id
           FROM tyt_transport_orders
           WHERE (cost_status IN ( 35,40, 45 )
             OR refund_reason IN ( '货已送达,退还订金', '已装货,退还订金', '已装货,协商退款', '货已送达,协商退款' )
             OR ( cost_status = 50 AND loading_status = 1 ))
             and user_id = #{userId}
             and pay_end_time >= NOW() - INTERVAL 6 MONTH
             limit 1) tab
  </select>

  <select id="getOrdersByIdList" resultType="com.tyt.plat.entity.base.MybatisTytTransportOrders">
    SELECT id,task_content taskContent, head_city headCity, head_no headNo, tail_city tailCity, tail_no tailNo
    FROM
    tyt_transport_orders
    WHERE id in
    <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="getAllNoFreezeOrdersPriceCount" resultType="java.lang.Integer">
    select sum(carriage_fee) from tyt_transport_orders where order_new_status in (15,20,25) and user_id = #{userId} and  mtime >= #{startDate};
  </select>


  <select id="getPayUserIdCarIdCTime" resultType="java.lang.Integer">
    select count(*) from tyt_transport_orders where pay_user_id = #{userId} and car_id = #{carId}
            AND (cost_status IN ( 15,20,21,25,40, 45 ) OR refund_reason IN ( '货已送达,退还订金', '已装货,退还订金', '已装货,协商退款', '货已送达,协商退款' )OR ( cost_status = 50 AND loading_status = 1 ) )
                                                and pay_end_time >= #{openTime} and pay_end_time &lt;= #{endTime}
  </select>

</mapper>