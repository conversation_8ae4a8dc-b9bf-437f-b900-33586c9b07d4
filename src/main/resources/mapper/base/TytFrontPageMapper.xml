<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytFrontPageMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytFrontPage">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="client_type" jdbcType="INTEGER" property="clientType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="link_url" jdbcType="VARCHAR" property="linkUrl" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_man_id" jdbcType="BIGINT" property="createManId" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_man_id" jdbcType="BIGINT" property="modifyManId" />
    <result column="publish_status" jdbcType="INTEGER" property="publishStatus" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="publish_man_id" jdbcType="BIGINT" property="publishManId" />
  </resultMap>
</mapper>