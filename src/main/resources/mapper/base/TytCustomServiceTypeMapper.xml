<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCustomServiceTypeMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCustomServiceType">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="is_delete" jdbcType="SMALLINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, type_name, is_delete, create_time, update_time
  </sql>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tyt_custom_service_type
    where is_delete = 0
    </select>
</mapper>
