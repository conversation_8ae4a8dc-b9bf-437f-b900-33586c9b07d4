<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytUserRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytUserRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

    <resultMap id="GoodsSmallResultMap" type="com.tyt.plat.vo.other.GoodsSmallDO">
        <id column="id" property="id" />
        <result column="meal_name" property="mealName" />
        <result column="user_type" property="userType" />
        <result column="effective_days" property="effectiveDays" />
        <result column="transport_type" property="transportType" />
        <result column="use_num" property="useNum" />
        <result column="original_price" property="originalPrice" />
        <result column="current_price" property="currentPrice" />
        <result column="goods_id" property="goodsId" />
        <result column="status" property="status" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="action_user_id" property="actionUserId" />
        <result column="action_user_name" property="actionUserName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

  <sql id="BaseResultSql">
    id,user_id,code,status, value, create_time,modify_time,remark
  </sql>
  <select id="selectByUserIdAndCode" resultMap="BaseResultMap">
    select
    <include refid="BaseResultSql" />
    from tyt_user_record where user_id =#{userId,jdbcType=BIGINT} and code =#{code,jdbcType=VARCHAR}
  </select>

  <select id="selectIndividuationSettings" resultType="com.tyt.plat.vo.other.IndividuationSettings">
    select
    user_id AS userId,code,status
    from tyt_user_record
    where user_id =#{userId,jdbcType=BIGINT}
    and code in
    <foreach close=")" collection="codes" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <insert id="saveFindTransportLogBIData">
    insert into tyt_find_transport_bi_data_log (src_msg_id, car_user_id, type, second_num, interrupt_type, interrupt_time, release_time, client_type, create_time)
      value (#{srcMsgId}, #{carUserId}, #{type}, #{secondNum}, #{interruptType}, #{interruptTime}, #{releaseTime}, #{clientType}, now())
  </insert>

  <insert id="insertIntoGoodsAddressLevelRecordGoodCarPriceTransportIsTrue">
    INSERT INTO goods_address_level_record  (
              src_msg_id,
              good_car_price_transport
              <if test="distanceKilometer != null">
                ,distance_kilometer
              </if>
              <if test="otherFee != null">
                ,other_fee
              </if>
           )
    VALUES (
            #{srcMsgId},
            1
            <if test="distanceKilometer != null">
              , #{distanceKilometer}
            </if>
            <if test="otherFee != null">
              , #{otherFee}
            </if>
            )
  </insert>

  <update id="updateGoodsAddressLevelRecordGoodCarPriceTransportIsTrueBySrcMsgId" >
    update goods_address_level_record
    set good_car_price_transport = 1
    <if test="distanceKilometer != null">
      ,distance_kilometer = #{distanceKilometer}
    </if>
    <if test="otherFee != null">
      ,other_fee = #{otherFee}
    </if>
    where src_msg_id = #{srcMsgId}
  </update>

  <select id="selectGoodsAddressLevelRecordIdBySrcMsgId" parameterType="long" resultType="long">
    select id
    from goods_address_level_record
    where src_msg_id = #{srcMsgId}
  </select>

  <select id="getGoodsAddressLevelRecordByGoodsId" resultType="com.tyt.plat.vo.other.GoodsAddressLevelRecordVo">
    select src_msg_id as srcMsgId,
           distance_kilometer as distanceKilometer,
           other_fee as otherFee
    from goods_address_level_record
    where src_msg_id = #{srcMsgId}
  </select>

    <select id="getGoodsSmallIdList" resultType="java.lang.Long">
        select goods_small_id
        from tyt_goods_small_user_config
        where user_id = #{userId}
    </select>

    <sql id="GoodsSmallList">
        id, meal_name, user_type, effective_days, transport_type, use_num, FLOOR(original_price) original_price, FLOOR(current_price) current_price, goods_id, status, delete_flag, action_user_id, action_user_name, create_time, modify_time
    </sql>

    <select id="getByIdList"
            resultMap="GoodsSmallResultMap">
        select <include refid="GoodsSmallList"/>
        from tyt_goods_small
        WHERE status = 1 and delete_flag = 0 AND
        id IN
        <foreach item="id" index="index" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by current_price
    </select>

</mapper>