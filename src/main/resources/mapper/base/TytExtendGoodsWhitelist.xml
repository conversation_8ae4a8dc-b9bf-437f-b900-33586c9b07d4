<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytExtendGoodsWhitelistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytExtendGoodsWhitelist">
        <id column="id" property="id" />
        <result column="goods_station_id" property="goodsStationId" />
        <result column="goods_station_name" property="goodsStationName" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_user_id" property="updateUserId" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, goods_station_id, goods_station_name, ctime, mtime, update_user_name, update_user_id, type
    </sql>

    <select id="getWhitelist" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_extend_goods_whitelist WHERE goods_station_id = #{goodsStationId,jdbcType=BIGINT}
    </select>

</mapper>
