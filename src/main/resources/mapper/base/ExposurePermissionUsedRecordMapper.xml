<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.ExposurePermissionUsedRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.ExposurePermissionUsedRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="start_point" jdbcType="VARCHAR" property="startPoint" />
    <result column="dest_point" jdbcType="VARCHAR" property="destPoint" />
    <result column="task_content" jdbcType="VARCHAR" property="taskContent" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>

  <select id="getExposurePermissionUsedRecordListByUserId" resultMap="BaseResultMap">
    SELECT id, user_id, src_msg_id, start_point, task_content, dest_point, ctime FROM exposure_permission_used_record
    where user_id = #{userId}
      and ctime >= DATE_SUB(#{today}, INTERVAL 6 DAY)
    order by ctime desc
  </select>
</mapper>